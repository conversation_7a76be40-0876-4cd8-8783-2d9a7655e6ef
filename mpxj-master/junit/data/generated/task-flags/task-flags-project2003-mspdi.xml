<?xml version="1.0"?>
<Project xmlns="http://schemas.microsoft.com/project"><Name>task-flags-project2003-mspdi.xml</Name><Author>Project User</Author><CreationDate>2014-10-17T19:40:00</CreationDate><LastSaved>2014-10-17T19:40:00</LastSaved><ScheduleFromStart>1</ScheduleFromStart><StartDate>2014-10-17T08:00:00</StartDate><FinishDate>2014-10-17T17:00:00</FinishDate><FYStartDate>1</FYStartDate><CriticalSlackLimit>0</CriticalSlackLimit><CurrencyDigits>2</CurrencyDigits><CurrencySymbol>£</CurrencySymbol><CurrencySymbolPosition>0</CurrencySymbolPosition><CalendarUID>1</CalendarUID><DefaultStartTime>08:00:00</DefaultStartTime><DefaultFinishTime>17:00:00</DefaultFinishTime><MinutesPerDay>480</MinutesPerDay><MinutesPerWeek>2400</MinutesPerWeek><DaysPerMonth>20</DaysPerMonth><DefaultTaskType>0</DefaultTaskType><DefaultFixedCostAccrual>3</DefaultFixedCostAccrual><DefaultStandardRate>0</DefaultStandardRate><DefaultOvertimeRate>0</DefaultOvertimeRate><DurationFormat>7</DurationFormat><WorkFormat>2</WorkFormat><EditableActualCosts>0</EditableActualCosts><HonorConstraints>0</HonorConstraints><InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary><MultipleCriticalPaths>0</MultipleCriticalPaths><NewTasksEffortDriven>1</NewTasksEffortDriven><NewTasksEstimated>1</NewTasksEstimated><SplitsInProgressTasks>1</SplitsInProgressTasks><SpreadActualCost>0</SpreadActualCost><SpreadPercentComplete>0</SpreadPercentComplete><TaskUpdatesResource>1</TaskUpdatesResource><FiscalYearStart>0</FiscalYearStart><WeekStartDay>1</WeekStartDay><MoveCompletedEndsBack>0</MoveCompletedEndsBack><MoveRemainingStartsBack>0</MoveRemainingStartsBack><MoveRemainingStartsForward>0</MoveRemainingStartsForward><MoveCompletedEndsForward>0</MoveCompletedEndsForward><BaselineForEarnedValue>0</BaselineForEarnedValue><AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks><CurrentDate>2014-10-17T08:00:00</CurrentDate><MicrosoftProjectServerURL>1</MicrosoftProjectServerURL><Autolink>1</Autolink><NewTaskStartDate>0</NewTaskStartDate><DefaultTaskEVMethod>0</DefaultTaskEVMethod><ProjectExternallyEdited>0</ProjectExternallyEdited><ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate><ActualsInSync>1</ActualsInSync><RemoveFileProperties>0</RemoveFileProperties><AdminProject>0</AdminProject><OutlineCodes/><WBSMasks/><ExtendedAttributes><ExtendedAttribute><FieldID>188743752</FieldID><FieldName>Flag1</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743753</FieldID><FieldName>Flag2</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743754</FieldID><FieldName>Flag3</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743755</FieldID><FieldName>Flag4</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743756</FieldID><FieldName>Flag5</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743757</FieldID><FieldName>Flag6</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743758</FieldID><FieldName>Flag7</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743759</FieldID><FieldName>Flag8</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743760</FieldID><FieldName>Flag9</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743761</FieldID><FieldName>Flag10</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743972</FieldID><FieldName>Flag11</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743973</FieldID><FieldName>Flag12</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743974</FieldID><FieldName>Flag13</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743975</FieldID><FieldName>Flag14</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743976</FieldID><FieldName>Flag15</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743977</FieldID><FieldName>Flag16</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743978</FieldID><FieldName>Flag17</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743979</FieldID><FieldName>Flag18</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743980</FieldID><FieldName>Flag19</FieldName></ExtendedAttribute><ExtendedAttribute><FieldID>188743981</FieldID><FieldName>Flag20</FieldName></ExtendedAttribute></ExtendedAttributes><Calendars><Calendar><UID>1</UID><Name>Standard</Name><IsBaseCalendar>1</IsBaseCalendar><BaseCalendarUID>-1</BaseCalendarUID><WeekDays><WeekDay><DayType>1</DayType><DayWorking>0</DayWorking></WeekDay><WeekDay><DayType>2</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>3</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>4</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>5</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>6</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>7</DayType><DayWorking>0</DayWorking></WeekDay></WeekDays></Calendar></Calendars><Tasks><Task><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>0</WBS><OutlineNumber>0</OutlineNumber><OutlineLevel>0</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>53</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>0</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>1</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected></Task><Task><UID>1</UID><ID>1</ID><Name>Flag1</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>1</WBS><OutlineNumber>1</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>1</UID><FieldID>188743752</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>2</UID><ID>2</ID><Name>Flag2</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>2</WBS><OutlineNumber>2</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>2</UID><FieldID>188743753</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>3</UID><ID>3</ID><Name>Flag3</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>3</WBS><OutlineNumber>3</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>3</UID><FieldID>188743754</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>4</UID><ID>4</ID><Name>Flag4</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>4</WBS><OutlineNumber>4</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>4</UID><FieldID>188743755</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>5</UID><ID>5</ID><Name>Flag5</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>5</WBS><OutlineNumber>5</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>5</UID><FieldID>188743756</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>6</UID><ID>6</ID><Name>Flag6</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>6</WBS><OutlineNumber>6</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>6</UID><FieldID>188743757</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>7</UID><ID>7</ID><Name>Flag7</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>7</WBS><OutlineNumber>7</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>7</UID><FieldID>188743758</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>8</UID><ID>8</ID><Name>Flag8</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>8</WBS><OutlineNumber>8</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>8</UID><FieldID>188743759</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>9</UID><ID>9</ID><Name>Flag9</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>9</WBS><OutlineNumber>9</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>9</UID><FieldID>188743760</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>10</UID><ID>10</ID><Name>Flag10</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>10</WBS><OutlineNumber>10</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>10</UID><FieldID>188743761</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>11</UID><ID>11</ID><Name>Flag11</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>11</WBS><OutlineNumber>11</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>11</UID><FieldID>188743972</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>12</UID><ID>12</ID><Name>Flag12</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>12</WBS><OutlineNumber>12</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>12</UID><FieldID>188743973</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>13</UID><ID>13</ID><Name>Flag13</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>13</WBS><OutlineNumber>13</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>13</UID><FieldID>188743974</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>14</UID><ID>14</ID><Name>Flag14</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>14</WBS><OutlineNumber>14</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>14</UID><FieldID>188743975</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>15</UID><ID>15</ID><Name>Flag15</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>15</WBS><OutlineNumber>15</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>15</UID><FieldID>188743976</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>16</UID><ID>16</ID><Name>Flag16</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>16</WBS><OutlineNumber>16</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>16</UID><FieldID>188743977</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>17</UID><ID>17</ID><Name>Flag17</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>17</WBS><OutlineNumber>17</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>17</UID><FieldID>188743978</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>18</UID><ID>18</ID><Name>Flag18</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>18</WBS><OutlineNumber>18</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>18</UID><FieldID>188743979</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>19</UID><ID>19</ID><Name>Flag19</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>19</WBS><OutlineNumber>19</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>19</UID><FieldID>188743980</FieldID><Value>1</Value></ExtendedAttribute></Task><Task><UID>20</UID><ID>20</ID><Name>Flag20</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2014-10-17T19:40:00</CreateDate><WBS>20</WBS><OutlineNumber>20</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Duration>PT8H0M0S</Duration><DurationFormat>39</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2014-10-17T08:00:00</EarlyStart><EarlyFinish>2014-10-17T17:00:00</EarlyFinish><LateStart>2014-10-17T08:00:00</LateStart><LateFinish>2014-10-17T17:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT8H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><ExtendedAttribute><UID>20</UID><FieldID>188743981</FieldID><Value>1</Value></ExtendedAttribute></Task></Tasks><Resources><Resource><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>2</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive><IsEnterprise>0</IsEnterprise><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate></Resource></Resources><Assignments><Assignment><UID>1</UID><TaskUID>1</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>1</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>2</UID><TaskUID>2</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>2</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>3</UID><TaskUID>3</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>3</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>4</UID><TaskUID>4</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>4</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>5</UID><TaskUID>5</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>5</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>6</UID><TaskUID>6</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>6</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>7</UID><TaskUID>7</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>7</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>8</UID><TaskUID>8</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>8</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>9</UID><TaskUID>9</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>9</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>10</UID><TaskUID>10</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>10</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>11</UID><TaskUID>11</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>11</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>12</UID><TaskUID>12</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>12</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>13</UID><TaskUID>13</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>13</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>14</UID><TaskUID>14</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>14</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>15</UID><TaskUID>15</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>15</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>16</UID><TaskUID>16</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>16</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>17</UID><TaskUID>17</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>17</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>18</UID><TaskUID>18</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>18</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>19</UID><TaskUID>19</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>19</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment><Assignment><UID>20</UID><TaskUID>20</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2014-10-17T17:00:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2014-10-17T08:00:00</Start><Stop>2014-10-17T08:00:00</Stop><Resume>2014-10-17T08:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2014-10-17T19:40:00</CreationDate><TimephasedData><Type>1</Type><UID>20</UID><Start>2014-10-17T08:00:00</Start><Finish>2014-10-17T17:00:00</Finish><Unit>2</Unit><Value>PT8H0M0S</Value></TimephasedData></Assignment></Assignments></Project>
