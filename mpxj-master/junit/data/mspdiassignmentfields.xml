<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<Name>mspdiassignmentfields.xml</Name>
	<Title>assignment test</Title>
	<Company></Company>
	<CreationDate>2011-07-06T08:00:00</CreationDate>
	<LastSaved>2011-07-19T21:56:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2011-07-06T08:00:00</StartDate>
	<FinishDate>2011-07-18T16:01:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2011-07-19T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>1</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>1</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes/>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<Name>Resource One</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<Name>Budget Work Resource</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>-65533</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<Name>Budget Cost Resource</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<Name>Resource Two</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Name>assignment test</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2011-07-06T12:06:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2011-07-06T08:00:00</Start>
			<Finish>2011-07-18T16:01:00</Finish>
			<Duration>PT71H1M0S</Duration>
			<ManualStart>2011-07-06T08:00:00</ManualStart>
			<ManualFinish>2011-07-18T16:01:00</ManualFinish>
			<ManualDuration>PT71H1M0S</ManualDuration>
			<DurationFormat>21</DurationFormat>
			<Work>PT103H0M0S</Work>
			<Stop>2011-07-06T08:55:00</Stop>
			<Resume>2011-07-06T08:55:00</Resume>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2011-07-06T08:00:00</EarlyStart>
			<EarlyFinish>2011-07-18T16:01:00</EarlyFinish>
			<LateStart>2011-07-06T08:00:00</LateStart>
			<LateFinish>2011-07-18T16:01:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>6180000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>6</PercentComplete>
			<PercentWorkComplete>7</PercentWorkComplete>
			<Cost>505200</Cost>
			<OvertimeCost>140000</OvertimeCost>
			<OvertimeWork>PT29H0M0S</OvertimeWork>
			<ActualStart>2011-07-06T08:00:00</ActualStart>
			<ActualDuration>PT4H27M0S</ActualDuration>
			<ActualCost>29908.45</ActualCost>
			<ActualOvertimeCost>15208.45</ActualOvertimeCost>
			<ActualWork>PT7H0M0S</ActualWork>
			<ActualOvertimeWork>PT3H6M0S</ActualOvertimeWork>
			<RegularWork>PT74H0M0S</RegularWork>
			<RemainingDuration>PT66H34M0S</RemainingDuration>
			<RemainingCost>475291.55</RemainingCost>
			<RemainingWork>PT96H0M0S</RemainingWork>
			<RemainingOvertimeCost>124791.55</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT25H54M0S</RemainingOvertimeWork>
			<ACWP>29908.45</ACWP>
			<CV>-29908.45</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Task One</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2011-07-06T12:06:00</CreateDate>
			<WBS>1</WBS>
			<OutlineNumber>1</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2011-07-06T08:00:00</Start>
			<Finish>2011-07-18T16:01:00</Finish>
			<Duration>PT71H1M0S</Duration>
			<ManualStart>2011-07-06T08:00:00</ManualStart>
			<ManualFinish>2011-07-18T16:01:00</ManualFinish>
			<ManualDuration>PT71H1M0S</ManualDuration>
			<DurationFormat>7</DurationFormat>
			<Work>PT103H0M0S</Work>
			<Stop>2011-07-06T08:55:00</Stop>
			<Resume>2011-07-06T08:55:00</Resume>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2011-07-06T08:00:00</EarlyStart>
			<EarlyFinish>2011-07-18T16:01:00</EarlyFinish>
			<LateStart>2011-07-06T08:00:00</LateStart>
			<LateFinish>2011-07-18T16:01:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>23410</FinishVariance>
			<WorkVariance>6180000.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>6</PercentComplete>
			<PercentWorkComplete>7</PercentWorkComplete>
			<Cost>505200</Cost>
			<OvertimeCost>140000</OvertimeCost>
			<OvertimeWork>PT29H0M0S</OvertimeWork>
			<ActualStart>2011-07-06T08:00:00</ActualStart>
			<ActualDuration>PT4H27M0S</ActualDuration>
			<ActualCost>29908.45</ActualCost>
			<ActualOvertimeCost>15208.45</ActualOvertimeCost>
			<ActualWork>PT7H0M0S</ActualWork>
			<ActualOvertimeWork>PT3H6M0S</ActualOvertimeWork>
			<RegularWork>PT74H0M0S</RegularWork>
			<RemainingDuration>PT66H34M0S</RemainingDuration>
			<RemainingCost>475291.55</RemainingCost>
			<RemainingWork>PT96H0M0S</RemainingWork>
			<RemainingOvertimeCost>124791.55</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT25H54M0S</RemainingOvertimeWork>
			<ACWP>29908.45</ACWP>
			<CV>-29908.45</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<Baseline>
				<Number>0</Number>
				<Finish>2011-07-11T17:00:00</Finish>
			</Baseline>
			<TimephasedData>
				<Type>11</Type>
				<UID>1</UID>
				<Start>2011-07-06T08:00:00</Start>
				<Finish>2011-07-06T13:27:00</Finish>
				<Unit>2</Unit>
				<Value>6.27</Value>
			</TimephasedData>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:06:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Resource One</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>1.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT71H0M0S</RegularWork>
			<OvertimeWork>PT9H0M0S</OvertimeWork>
			<ActualWork>PT2H0M0S</ActualWork>
			<RemainingWork>PT78H0M0S</RemainingWork>
			<ActualOvertimeWork>PT1H6M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT7H54M0S</RemainingOvertimeWork>
			<PercentWorkComplete>3</PercentWorkComplete>
			<StandardRate>50</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>409000</Cost>
			<OvertimeRate>60</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>54000</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>11108.45</ActualCost>
			<ActualOvertimeCost>6608.45</ActualOvertimeCost>
			<RemainingCost>397891.56</RemainingCost>
			<RemainingOvertimeCost>47391.55</RemainingOvertimeCost>
			<WorkVariance>4800000.00</WorkVariance>
			<CostVariance>409000</CostVariance>
			<SV>0.00</SV>
			<CV>-11108.45</CV>
			<ACWP>11108.45</ACWP>
			<CalendarUID>3</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:09:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<Rates>
				<Rate>
					<RatesFrom>1984-01-01T00:00:00</RatesFrom>
					<RatesTo>2049-12-31T23:59:06</RatesTo>
					<RateTable>0</RateTable>
					<StandardRate>50</StandardRate>
					<StandardRateFormat>2</StandardRateFormat>
					<OvertimeRate>60</OvertimeRate>
					<OvertimeRateFormat>2</OvertimeRateFormat>
					<CostPerUse>0</CostPerUse>
				</Rate>
			</Rates>
		</Resource>
		<Resource>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Budget Work Resource</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>B</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>4</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:22:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>1</IsBudget>
			<Rates/>
		</Resource>
		<Resource>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Budget Cost Resource</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<Initials>B</Initials>
			<WorkGroup>1</WorkGroup>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>0</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<ActualCost>0</ActualCost>
			<RemainingCost>0</RemainingCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>5</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:33:00</CreationDate>
			<IsCostResource>1</IsCostResource>
			<IsBudget>1</IsBudget>
			<Rates/>
		</Resource>
		<Resource>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Resource Two</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT23H0M0S</Work>
			<RegularWork>PT3H0M0S</RegularWork>
			<OvertimeWork>PT20H0M0S</OvertimeWork>
			<ActualWork>PT5H0M0S</ActualWork>
			<RemainingWork>PT18H0M0S</RemainingWork>
			<ActualOvertimeWork>PT2H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT18H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>22</PercentWorkComplete>
			<StandardRate>33</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>96200</Cost>
			<OvertimeRate>39</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>86000</OvertimeCost>
			<CostPerUse>100</CostPerUse>
			<ActualCost>18800</ActualCost>
			<ActualOvertimeCost>8600</ActualOvertimeCost>
			<RemainingCost>77400</RemainingCost>
			<RemainingOvertimeCost>77400</RemainingOvertimeCost>
			<WorkVariance>1380000.00</WorkVariance>
			<CostVariance>96200</CostVariance>
			<SV>0.00</SV>
			<CV>-18800.00</CV>
			<ACWP>18800.00</ACWP>
			<CalendarUID>6</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T15:31:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<Rates>
				<Rate>
					<RatesFrom>1984-01-01T00:00:00</RatesFrom>
					<RatesTo>2049-12-31T23:59:06</RatesTo>
					<RateTable>0</RateTable>
					<StandardRate>33</StandardRate>
					<StandardRateFormat>2</StandardRateFormat>
					<OvertimeRate>39</OvertimeRate>
					<OvertimeRateFormat>2</OvertimeRateFormat>
					<CostPerUse>100</CostPerUse>
				</Rate>
				<Rate>
					<RatesFrom>1984-01-01T00:00:00</RatesFrom>
					<RatesTo>2049-12-31T23:59:06</RatesTo>
					<RateTable>1</RateTable>
					<StandardRate>34</StandardRate>
					<StandardRateFormat>2</StandardRateFormat>
					<OvertimeRate>43</OvertimeRate>
					<OvertimeRateFormat>2</OvertimeRateFormat>
					<CostPerUse>0</CostPerUse>
				</Rate>
			</Rates>
		</Resource>
	</Resources>
	<Assignments>
		<Assignment>
			<UID>2</UID>
			<TaskUID>1</TaskUID>
			<ResourceUID>1</ResourceUID>
			<PercentWorkComplete>3</PercentWorkComplete>
			<ActualCost>11108.45</ActualCost>
			<ActualOvertimeCost>6608.45</ActualOvertimeCost>
			<ActualOvertimeWork>PT1H6M5.07S</ActualOvertimeWork>
			<ActualStart>2011-07-06T08:00:00</ActualStart>
			<ActualWork>PT2H0M5.07S</ActualWork>
			<ACWP>11108.45</ACWP>
			<Confirmed>1</Confirmed>
			<Cost>409000</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>409000</CostVariance>
			<CV>-11108.45</CV>
			<Delay>0</Delay>
			<Finish>2011-07-18T16:01:00</Finish>
			<FinishVariance>-10190</FinishVariance>
			<WorkVariance>4800000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Notes>Assignment Notes</Notes>
			<Overallocated>0</Overallocated>
			<OvertimeCost>54000</OvertimeCost>
			<OvertimeWork>PT9H0M0S</OvertimeWork>
			<RegularWork>PT71H0M0S</RegularWork>
			<RemainingCost>397891.55</RemainingCost>
			<RemainingOvertimeCost>47391.55</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT7H54M0S</RemainingOvertimeWork>
			<RemainingWork>PT77H59M54.94S</RemainingWork>
			<ResponsePending>1</ResponsePending>
			<Start>2011-07-06T08:00:00</Start>
			<Stop>2011-07-06T08:55:00</Stop>
			<Resume>2011-07-06T08:55:00</Resume>
			<StartVariance>9600</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:09:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<Baseline>
				<Number>0</Number>
				<Start>2011-07-04T08:00:00</Start>
				<Finish>2011-07-20T17:00:00</Finish>
			</Baseline>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:55:00</Start>
				<Finish>2011-07-07T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H54M5.07S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-07T08:55:00</Start>
				<Finish>2011-07-08T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H54M5.07S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-08T08:55:00</Start>
				<Finish>2011-07-09T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT7H52M53.24S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-09T08:55:00</Start>
				<Finish>2011-07-10T08:55:00</Finish>
				<Unit>2</Unit>
				<Value></Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-10T08:55:00</Start>
				<Finish>2011-07-11T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT1H1M11.83S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-11T08:55:00</Start>
				<Finish>2011-07-12T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H54M5.07S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-12T08:55:00</Start>
				<Finish>2011-07-13T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H54M5.07S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-13T08:55:00</Start>
				<Finish>2011-07-14T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H54M5.07S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-14T08:55:00</Start>
				<Finish>2011-07-15T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H54M5.07S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-15T08:55:00</Start>
				<Finish>2011-07-16T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT7H52M53.24S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-16T08:55:00</Start>
				<Finish>2011-07-17T08:55:00</Finish>
				<Unit>2</Unit>
				<Value></Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-17T08:55:00</Start>
				<Finish>2011-07-18T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT1H1M11.83S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2011-07-18T08:55:00</Start>
				<Finish>2011-07-18T16:01:00</Finish>
				<Unit>2</Unit>
				<Value>PT6H47M14.37S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:00:00</Start>
				<Finish>2011-07-06T08:01:00</Finish>
				<Unit>0</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:01:00</Start>
				<Finish>2011-07-06T08:02:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:02:00</Start>
				<Finish>2011-07-06T08:03:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:03:00</Start>
				<Finish>2011-07-06T08:04:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:04:00</Start>
				<Finish>2011-07-06T08:05:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:05:00</Start>
				<Finish>2011-07-06T08:06:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:06:00</Start>
				<Finish>2011-07-06T08:07:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:07:00</Start>
				<Finish>2011-07-06T08:08:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:08:00</Start>
				<Finish>2011-07-06T08:09:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:09:00</Start>
				<Finish>2011-07-06T08:10:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:10:00</Start>
				<Finish>2011-07-06T08:11:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:11:00</Start>
				<Finish>2011-07-06T08:12:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:12:00</Start>
				<Finish>2011-07-06T08:13:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:13:00</Start>
				<Finish>2011-07-06T08:14:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:14:00</Start>
				<Finish>2011-07-06T08:15:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:15:00</Start>
				<Finish>2011-07-06T08:16:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:16:00</Start>
				<Finish>2011-07-06T08:17:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:17:00</Start>
				<Finish>2011-07-06T08:18:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:18:00</Start>
				<Finish>2011-07-06T08:19:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:19:00</Start>
				<Finish>2011-07-06T08:20:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:20:00</Start>
				<Finish>2011-07-06T08:21:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:21:00</Start>
				<Finish>2011-07-06T08:22:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:22:00</Start>
				<Finish>2011-07-06T08:23:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:23:00</Start>
				<Finish>2011-07-06T08:24:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:24:00</Start>
				<Finish>2011-07-06T08:25:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:25:00</Start>
				<Finish>2011-07-06T08:26:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:26:00</Start>
				<Finish>2011-07-06T08:27:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:27:00</Start>
				<Finish>2011-07-06T08:28:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:28:00</Start>
				<Finish>2011-07-06T08:29:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:29:00</Start>
				<Finish>2011-07-06T08:30:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:30:00</Start>
				<Finish>2011-07-06T08:31:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:31:00</Start>
				<Finish>2011-07-06T08:32:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:32:00</Start>
				<Finish>2011-07-06T08:33:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:33:00</Start>
				<Finish>2011-07-06T08:34:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:34:00</Start>
				<Finish>2011-07-06T08:35:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:35:00</Start>
				<Finish>2011-07-06T08:36:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:36:00</Start>
				<Finish>2011-07-06T08:37:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:37:00</Start>
				<Finish>2011-07-06T08:38:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:38:00</Start>
				<Finish>2011-07-06T08:39:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:39:00</Start>
				<Finish>2011-07-06T08:40:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:40:00</Start>
				<Finish>2011-07-06T08:41:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:41:00</Start>
				<Finish>2011-07-06T08:42:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:42:00</Start>
				<Finish>2011-07-06T08:43:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:43:00</Start>
				<Finish>2011-07-06T08:44:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:44:00</Start>
				<Finish>2011-07-06T08:45:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:45:00</Start>
				<Finish>2011-07-06T08:46:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:46:00</Start>
				<Finish>2011-07-06T08:47:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:47:00</Start>
				<Finish>2011-07-06T08:48:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:48:00</Start>
				<Finish>2011-07-06T08:49:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:49:00</Start>
				<Finish>2011-07-06T08:50:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:50:00</Start>
				<Finish>2011-07-06T08:51:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:51:00</Start>
				<Finish>2011-07-06T08:52:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:52:00</Start>
				<Finish>2011-07-06T08:53:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:53:00</Start>
				<Finish>2011-07-06T08:54:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:54:00</Start>
				<Finish>2011-07-06T08:55:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H1M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:00:00</Start>
				<Finish>2011-07-06T08:01:00</Finish>
				<Unit>0</Unit>
				<Value>PT1H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:01:00</Start>
				<Finish>2011-07-06T08:02:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:02:00</Start>
				<Finish>2011-07-06T08:03:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:03:00</Start>
				<Finish>2011-07-06T08:04:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:04:00</Start>
				<Finish>2011-07-06T08:05:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:05:00</Start>
				<Finish>2011-07-06T08:06:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:06:00</Start>
				<Finish>2011-07-06T08:07:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:07:00</Start>
				<Finish>2011-07-06T08:08:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:08:00</Start>
				<Finish>2011-07-06T08:09:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:09:00</Start>
				<Finish>2011-07-06T08:10:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:10:00</Start>
				<Finish>2011-07-06T08:11:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:11:00</Start>
				<Finish>2011-07-06T08:12:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:12:00</Start>
				<Finish>2011-07-06T08:13:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:13:00</Start>
				<Finish>2011-07-06T08:14:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:14:00</Start>
				<Finish>2011-07-06T08:15:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:15:00</Start>
				<Finish>2011-07-06T08:16:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:16:00</Start>
				<Finish>2011-07-06T08:17:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:17:00</Start>
				<Finish>2011-07-06T08:18:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:18:00</Start>
				<Finish>2011-07-06T08:19:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:19:00</Start>
				<Finish>2011-07-06T08:20:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:20:00</Start>
				<Finish>2011-07-06T08:21:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:21:00</Start>
				<Finish>2011-07-06T08:22:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:22:00</Start>
				<Finish>2011-07-06T08:23:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:23:00</Start>
				<Finish>2011-07-06T08:24:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:24:00</Start>
				<Finish>2011-07-06T08:25:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:25:00</Start>
				<Finish>2011-07-06T08:26:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:26:00</Start>
				<Finish>2011-07-06T08:27:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:27:00</Start>
				<Finish>2011-07-06T08:28:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:28:00</Start>
				<Finish>2011-07-06T08:29:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:29:00</Start>
				<Finish>2011-07-06T08:30:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:30:00</Start>
				<Finish>2011-07-06T08:31:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:31:00</Start>
				<Finish>2011-07-06T08:32:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:32:00</Start>
				<Finish>2011-07-06T08:33:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:33:00</Start>
				<Finish>2011-07-06T08:34:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:34:00</Start>
				<Finish>2011-07-06T08:35:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:35:00</Start>
				<Finish>2011-07-06T08:36:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:36:00</Start>
				<Finish>2011-07-06T08:37:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:37:00</Start>
				<Finish>2011-07-06T08:38:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:38:00</Start>
				<Finish>2011-07-06T08:39:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:39:00</Start>
				<Finish>2011-07-06T08:40:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:40:00</Start>
				<Finish>2011-07-06T08:41:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:41:00</Start>
				<Finish>2011-07-06T08:42:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:42:00</Start>
				<Finish>2011-07-06T08:43:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:43:00</Start>
				<Finish>2011-07-06T08:44:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:44:00</Start>
				<Finish>2011-07-06T08:45:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:45:00</Start>
				<Finish>2011-07-06T08:46:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:46:00</Start>
				<Finish>2011-07-06T08:47:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:47:00</Start>
				<Finish>2011-07-06T08:48:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:48:00</Start>
				<Finish>2011-07-06T08:49:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:49:00</Start>
				<Finish>2011-07-06T08:50:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:50:00</Start>
				<Finish>2011-07-06T08:51:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:51:00</Start>
				<Finish>2011-07-06T08:52:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:52:00</Start>
				<Finish>2011-07-06T08:53:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:53:00</Start>
				<Finish>2011-07-06T08:54:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>2</UID>
				<Start>2011-07-06T08:54:00</Start>
				<Finish>2011-07-06T08:55:00</Finish>
				<Unit>0</Unit>
				<Value>PT0H0M6.76S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>3</UID>
			<TaskUID>0</TaskUID>
			<ResourceUID>2</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2011-07-06T08:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2011-07-06T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT0H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:30:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT97H0M0S</BudgetWork>
		</Assignment>
		<Assignment>
			<UID>4</UID>
			<TaskUID>0</TaskUID>
			<ResourceUID>3</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Finish>2011-07-06T08:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>1</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2011-07-06T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT0H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T12:33:00</CreationDate>
			<BudgetCost>9600</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
		</Assignment>
		<Assignment>
			<UID>5</UID>
			<TaskUID>1</TaskUID>
			<ResourceUID>4</ResourceUID>
			<PercentWorkComplete>22</PercentWorkComplete>
			<ActualCost>18800</ActualCost>
			<ActualOvertimeCost>8600</ActualOvertimeCost>
			<ActualOvertimeWork>PT2H0M0S</ActualOvertimeWork>
			<ActualStart>2011-07-06T08:55:00</ActualStart>
			<ActualWork>PT5H0M0S</ActualWork>
			<ACWP>18800.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>96200</Cost>
			<CostRateTable>1</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>96200</CostVariance>
			<CV>-18800.00</CV>
			<Delay>550</Delay>
			<Finish>2011-07-07T13:55:00</Finish>
			<FinishVariance>-49850</FinishVariance>
			<WorkVariance>1380000.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<Hyperlink>Test Hyperlink Display Text</Hyperlink>
			<HyperlinkAddress>http://news.bbc.co.uk</HyperlinkAddress>
			<HyperlinkSubAddress>x</HyperlinkSubAddress>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>86000</OvertimeCost>
			<OvertimeWork>PT20H0M0S</OvertimeWork>
			<RegularWork>PT3H0M0S</RegularWork>
			<RemainingCost>77400</RemainingCost>
			<RemainingOvertimeCost>77400</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT18H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT18H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2011-07-06T08:55:00</Start>
			<Stop>2011-07-07T13:55:00</Stop>
			<Resume>2011-07-07T13:55:00</Resume>
			<StartVariance>5350</StartVariance>
			<Units>0.25</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT23H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2011-07-06T15:31:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<Baseline>
				<Number>0</Number>
				<Start>2011-07-05T08:00:00</Start>
				<Finish>2011-07-21T17:00:00</Finish>
			</Baseline>
			<TimephasedData>
				<Type>2</Type>
				<UID>5</UID>
				<Start>2011-07-06T08:55:00</Start>
				<Finish>2011-07-07T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT3H20M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>2</Type>
				<UID>5</UID>
				<Start>2011-07-07T08:55:00</Start>
				<Finish>2011-07-07T13:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT1H40M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>5</UID>
				<Start>2011-07-06T08:55:00</Start>
				<Finish>2011-07-07T08:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT1H20M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>3</Type>
				<UID>5</UID>
				<Start>2011-07-07T08:55:00</Start>
				<Finish>2011-07-07T13:55:00</Finish>
				<Unit>2</Unit>
				<Value>PT0H40M0S</Value>
			</TimephasedData>
		</Assignment>
	</Assignments>
</Project>