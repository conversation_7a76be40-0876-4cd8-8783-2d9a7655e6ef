<?xml version="1.0"?>
<Project xmlns="http://schemas.microsoft.com/project"><Name>baselines.xml</Name><Title>project1</Title><Company></Company><Author></Author><CreationDate>2008-01-30T08:12:00</CreationDate><LastSaved>2008-02-01T23:25:00</LastSaved><ScheduleFromStart>1</ScheduleFromStart><StartDate>2008-01-30T09:00:00</StartDate><FinishDate>2008-01-31T08:30:00</FinishDate><FYStartDate>1</FYStartDate><CriticalSlackLimit>0</CriticalSlackLimit><CurrencyDigits>2</CurrencyDigits><CurrencySymbol>£</CurrencySymbol><CurrencySymbolPosition>0</CurrencySymbolPosition><CalendarUID>1</CalendarUID><DefaultStartTime>09:00:00</DefaultStartTime><DefaultFinishTime>17:30:00</DefaultFinishTime><MinutesPerDay>450</MinutesPerDay><MinutesPerWeek>2250</MinutesPerWeek><DaysPerMonth>20</DaysPerMonth><DefaultTaskType>0</DefaultTaskType><DefaultFixedCostAccrual>3</DefaultFixedCostAccrual><DefaultStandardRate>0</DefaultStandardRate><DefaultOvertimeRate>0</DefaultOvertimeRate><DurationFormat>7</DurationFormat><WorkFormat>2</WorkFormat><EditableActualCosts>0</EditableActualCosts><HonorConstraints>0</HonorConstraints><InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary><MultipleCriticalPaths>0</MultipleCriticalPaths><NewTasksEffortDriven>1</NewTasksEffortDriven><NewTasksEstimated>1</NewTasksEstimated><SplitsInProgressTasks>1</SplitsInProgressTasks><SpreadActualCost>0</SpreadActualCost><SpreadPercentComplete>0</SpreadPercentComplete><TaskUpdatesResource>1</TaskUpdatesResource><FiscalYearStart>0</FiscalYearStart><WeekStartDay>1</WeekStartDay><MoveCompletedEndsBack>0</MoveCompletedEndsBack><MoveRemainingStartsBack>0</MoveRemainingStartsBack><MoveRemainingStartsForward>0</MoveRemainingStartsForward><MoveCompletedEndsForward>0</MoveCompletedEndsForward><BaselineForEarnedValue>0</BaselineForEarnedValue><AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks><CurrentDate>2008-02-01T08:00:00</CurrentDate><MicrosoftProjectServerURL>1</MicrosoftProjectServerURL><Autolink>1</Autolink><NewTaskStartDate>0</NewTaskStartDate><DefaultTaskEVMethod>0</DefaultTaskEVMethod><ProjectExternallyEdited>0</ProjectExternallyEdited><ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate><ActualsInSync>1</ActualsInSync><RemoveFileProperties>0</RemoveFileProperties><AdminProject>0</AdminProject><OutlineCodes/><WBSMasks/><ExtendedAttributes/><Calendars><Calendar><UID>1</UID><Name>Standard</Name><IsBaseCalendar>1</IsBaseCalendar><BaseCalendarUID>-1</BaseCalendarUID><WeekDays><WeekDay><DayType>1</DayType><DayWorking>0</DayWorking></WeekDay><WeekDay><DayType>2</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>3</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>4</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>5</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>6</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>7</DayType><DayWorking>0</DayWorking></WeekDay></WeekDays></Calendar><Calendar><UID>3</UID><Name>Resource One</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>4</UID><Name>Used for Microsoft Project 98 Baseline Calendar</Name><IsBaseCalendar>1</IsBaseCalendar><BaseCalendarUID>-1</BaseCalendarUID><WeekDays><WeekDay><DayType>1</DayType><DayWorking>0</DayWorking></WeekDay><WeekDay><DayType>2</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>3</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>4</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>5</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>6</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>7</DayType><DayWorking>0</DayWorking></WeekDay></WeekDays></Calendar></Calendars><Tasks><Task><UID>0</UID><ID>0</ID><Name>project1</Name><Type>1</Type><IsNull>0</IsNull><CreateDate>2008-01-30T08:12:00</CreateDate><WBS>0</WBS><OutlineNumber>0</OutlineNumber><OutlineLevel>0</OutlineLevel><Priority>500</Priority><Start>2008-01-30T09:00:00</Start><Finish>2008-01-31T08:30:00</Finish><Duration>PT7H30M0S</Duration><DurationFormat>21</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>0</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>0</Estimated><Milestone>0</Milestone><Summary>1</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2008-01-30T09:00:00</EarlyStart><EarlyFinish>2008-01-31T08:30:00</EarlyFinish><LateStart>2008-01-30T09:00:00</LateStart><LateFinish>2008-01-31T08:30:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT7H30M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected></Task><Task><UID>1</UID><ID>1</ID><Name>Task One</Name><Type>0</Type><IsNull>0</IsNull><CreateDate>2008-01-30T08:19:00</CreateDate><WBS>1</WBS><OutlineNumber>1</OutlineNumber><OutlineLevel>1</OutlineLevel><Priority>500</Priority><Start>2008-01-30T09:00:00</Start><Finish>2008-01-31T08:30:00</Finish><Duration>PT7H30M0S</Duration><DurationFormat>7</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>1</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>0</Estimated><Milestone>0</Milestone><Summary>0</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2008-01-30T09:00:00</EarlyStart><EarlyFinish>2008-01-31T08:30:00</EarlyFinish><LateStart>2008-01-30T09:00:00</LateStart><LateFinish>2008-01-31T08:30:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT7H30M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><Baseline><Number>1</Number><Start>2001-01-01T09:00:00</Start><Finish>2000-01-01T17:30:00</Finish><Duration>PT7H30M0S</Duration><DurationFormat>7</DurationFormat><Work>PT1H0M0S</Work><Cost>100</Cost></Baseline><Baseline><Number>2</Number><Start>2001-01-02T09:00:00</Start><Finish>2000-01-02T17:30:00</Finish><Duration>PT15H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT2H0M0S</Work><Cost>200</Cost></Baseline><Baseline><Number>3</Number><Start>2001-01-03T09:00:00</Start><Finish>2000-01-03T17:30:00</Finish><Duration>PT22H30M0S</Duration><DurationFormat>7</DurationFormat><Work>PT3H0M0S</Work><Cost>300</Cost></Baseline><Baseline><Number>4</Number><Start>2001-01-04T09:00:00</Start><Finish>2000-01-04T17:30:00</Finish><Duration>PT30H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT4H0M0S</Work><Cost>400</Cost></Baseline><Baseline><Number>5</Number><Start>2001-01-05T09:00:00</Start><Finish>2000-01-05T17:30:00</Finish><Duration>PT37H30M0S</Duration><DurationFormat>7</DurationFormat><Work>PT5H0M0S</Work><Cost>500</Cost></Baseline><Baseline><Number>6</Number><Start>2001-01-06T09:00:00</Start><Finish>2000-01-06T17:30:00</Finish><Duration>PT45H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT6H0M0S</Work><Cost>600</Cost></Baseline><Baseline><Number>7</Number><Start>2001-01-07T09:00:00</Start><Finish>2000-01-07T17:30:00</Finish><Duration>PT52H30M0S</Duration><DurationFormat>7</DurationFormat><Work>PT7H0M0S</Work><Cost>700</Cost></Baseline><Baseline><Number>8</Number><Start>2001-01-08T09:00:00</Start><Finish>2000-01-08T17:30:00</Finish><Duration>PT60H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT8H0M0S</Work><Cost>800</Cost></Baseline><Baseline><Number>9</Number><Start>2001-01-09T09:00:00</Start><Finish>2000-01-09T17:30:00</Finish><Duration>PT67H30M0S</Duration><DurationFormat>7</DurationFormat><Work>PT9H0M0S</Work><Cost>900</Cost></Baseline><Baseline><Number>10</Number><Start>2001-01-10T09:00:00</Start><Finish>2000-01-10T17:30:00</Finish><Duration>PT75H0M0S</Duration><DurationFormat>7</DurationFormat><Work>PT10H0M0S</Work><Cost>1000</Cost></Baseline></Task></Tasks><Resources><Resource><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>2</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive><IsEnterprise>0</IsEnterprise><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2008-01-30T08:12:00</CreationDate></Resource><Resource><UID>1</UID><ID>1</ID><Name>Resource One</Name><Type>1</Type><IsNull>0</IsNull><Initials>R</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>3</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive><IsEnterprise>0</IsEnterprise><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2008-01-31T21:49:00</CreationDate><Baseline><Number>1</Number><Work>PT1H0M0S</Work><Cost>100</Cost></Baseline><Baseline><Number>2</Number><Work>PT2H0M0S</Work><Cost>200</Cost></Baseline><Baseline><Number>3</Number><Work>PT3H0M0S</Work><Cost>300</Cost></Baseline><Baseline><Number>4</Number><Work>PT4H0M0S</Work><Cost>400</Cost></Baseline><Baseline><Number>5</Number><Work>PT5H0M0S</Work><Cost>500</Cost></Baseline><Baseline><Number>6</Number><Work>PT6H0M0S</Work><Cost>600</Cost></Baseline><Baseline><Number>7</Number><Work>PT7H0M0S</Work><Cost>700</Cost></Baseline><Baseline><Number>8</Number><Work>PT8H0M0S</Work><Cost>800</Cost></Baseline><Baseline><Number>9</Number><Work>PT9H0M0S</Work><Cost>900</Cost></Baseline><Baseline><Number>10</Number><Work>PT10H0M0S</Work><Cost>1000</Cost></Baseline></Resource></Resources><Assignments><Assignment><UID>1</UID><TaskUID>1</TaskUID><ResourceUID>-65535</ResourceUID><PercentWorkComplete>0</PercentWorkComplete><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><ActualWork>PT0H0M0S</ActualWork><ACWP>0</ACWP><Confirmed>0</Confirmed><Cost>0</Cost><CostRateTable>0</CostRateTable><CostVariance>0</CostVariance><CV>0</CV><Delay>0</Delay><Finish>2008-01-31T08:30:00</Finish><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><HasFixedRateUnits>1</HasFixedRateUnits><FixedMaterial>0</FixedMaterial><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>7</LevelingDelayFormat><LinkedFields>0</LinkedFields><Milestone>0</Milestone><Overallocated>0</Overallocated><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><RemainingWork>PT0H0M0S</RemainingWork><ResponsePending>0</ResponsePending><Start>2008-01-30T09:00:00</Start><Stop>2008-01-30T09:00:00</Stop><Resume>2008-01-30T09:00:00</Resume><StartVariance>0</StartVariance><Units>1</Units><UpdateNeeded>0</UpdateNeeded><VAC>0</VAC><Work>PT0H0M0S</Work><WorkContour>0</WorkContour><BCWS>0</BCWS><BCWP>0</BCWP><BookingType>0</BookingType><ActualWorkProtected>PT0H0M0S</ActualWorkProtected><ActualOvertimeWorkProtected>PT0H0M0S</ActualOvertimeWorkProtected><CreationDate>2008-01-30T08:19:00</CreationDate><Baseline><Number>1</Number><Start>2008-01-30T09:00:00</Start><Finish>2008-01-31T08:30:00</Finish><Work>PT7H30M0S</Work></Baseline><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T09:00:00</Start><Finish>2008-01-30T10:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T10:00:00</Start><Finish>2008-01-30T11:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T11:00:00</Start><Finish>2008-01-30T12:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T12:00:00</Start><Finish>2008-01-30T13:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T13:00:00</Start><Finish>2008-01-30T14:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T14:00:00</Start><Finish>2008-01-30T15:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T15:00:00</Start><Finish>2008-01-30T16:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T16:00:00</Start><Finish>2008-01-30T17:00:00</Finish><Unit>1</Unit><Value>PT1H0M0S</Value></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T17:00:00</Start><Finish>2008-01-30T18:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T18:00:00</Start><Finish>2008-01-30T19:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T19:00:00</Start><Finish>2008-01-30T20:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T20:00:00</Start><Finish>2008-01-30T21:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T21:00:00</Start><Finish>2008-01-30T22:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T22:00:00</Start><Finish>2008-01-30T23:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-30T23:00:00</Start><Finish>2008-01-31T00:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T00:00:00</Start><Finish>2008-01-31T01:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T01:00:00</Start><Finish>2008-01-31T02:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T02:00:00</Start><Finish>2008-01-31T03:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T03:00:00</Start><Finish>2008-01-31T04:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T04:00:00</Start><Finish>2008-01-31T05:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T05:00:00</Start><Finish>2008-01-31T06:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T06:00:00</Start><Finish>2008-01-31T07:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T07:00:00</Start><Finish>2008-01-31T08:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>1</Type><UID>1</UID><Start>2008-01-31T08:00:00</Start><Finish>2008-01-31T08:30:00</Finish><Unit>1</Unit><Value>PT0H30M0S</Value></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T09:00:00</Start><Finish>2008-01-30T10:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T10:00:00</Start><Finish>2008-01-30T11:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T11:00:00</Start><Finish>2008-01-30T12:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T12:00:00</Start><Finish>2008-01-30T13:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T13:00:00</Start><Finish>2008-01-30T14:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T14:00:00</Start><Finish>2008-01-30T15:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T15:00:00</Start><Finish>2008-01-30T16:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T16:00:00</Start><Finish>2008-01-30T17:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T17:00:00</Start><Finish>2008-01-30T18:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T18:00:00</Start><Finish>2008-01-30T19:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T19:00:00</Start><Finish>2008-01-30T20:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T20:00:00</Start><Finish>2008-01-30T21:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T21:00:00</Start><Finish>2008-01-30T22:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T22:00:00</Start><Finish>2008-01-30T23:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-30T23:00:00</Start><Finish>2008-01-31T00:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T00:00:00</Start><Finish>2008-01-31T01:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T01:00:00</Start><Finish>2008-01-31T02:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T02:00:00</Start><Finish>2008-01-31T03:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T03:00:00</Start><Finish>2008-01-31T04:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T04:00:00</Start><Finish>2008-01-31T05:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T05:00:00</Start><Finish>2008-01-31T06:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T06:00:00</Start><Finish>2008-01-31T07:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T07:00:00</Start><Finish>2008-01-31T08:00:00</Finish><Unit>1</Unit></TimephasedData><TimephasedData><Type>17</Type><UID>1</UID><Start>2008-01-31T08:00:00</Start><Finish>2008-01-31T08:30:00</Finish><Unit>1</Unit></TimephasedData></Assignment></Assignments></Project>
