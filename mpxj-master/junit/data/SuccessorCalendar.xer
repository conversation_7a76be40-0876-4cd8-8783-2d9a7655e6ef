ERMHDR	8.2.0	2020-03-17	Project	admin	Primavera Admin	dbxDatabaseNoName	Project Management	USD
%T	CURRTYPE
%F	curr_id	decimal_digit_cnt	curr_symbol	decimal_symbol	digit_group_symbol	pos_curr_fmt_type	neg_curr_fmt_type	curr_type	curr_short_name	group_digit_cnt	base_exch_rate
%R	1	2	$	.	,	#1.1	(#1.1)	Dollar	USD	3	1
%R	10	2	$	.	,	#1.1	(#1.1)	Argentine Peso	ARS	3	3.077
%R	11	2	A$	.	,	#1.1	(#1.1)	Australian Dollar	AUST	3	1.208
%R	13	2	R$	.	,	#1.1	(#1.1)	Brazilian Real	BRL	3	2.014
%R	14	2	�	.	,	#1.1	(#1.1)	British Pound	U.K.	3	0.501762
%R	15	2	CA$	.	,	#1.1	(#1.1)	Canadian Dollar	CAD	3	1.10573
%R	16	2	Y	.	,	#1.1	(#1.1)	Chinese Yuan	CNY	3	7.694
%R	17	2	�	,	.	#1.1	(#1.1)	EURO	EUR	3	0.739088
%R	20	2	HK$	.	,	#1.1	(#1.1)	Hong Kong Dollar	HKD	3	7.81967
%R	21	2	Rs	.	,	#1.1	(#1.1)	Indian Rupee	INR	3	40.67
%R	23	2	�	.	,	#1.1	(#1.1)	Japanese Yen	JPY	3	120.167
%R	24	2	K	.	,	#1.1	(#1.1)	Korean Won	KRW	3	924.743
%R	25	2	N$	.	,	#1.1	(#1.1)	Mexican Peso	MXN	3	10.7938
%R	26	2	R	.	,	#1.1	(#1.1)	Russian Rouble	RUB	3	25.8085
%R	28	2	Sk	.	,	#1.1	(#1.1)	Swedish Krona	SEK	3	6.80579
%R	29	2	kr	.	,	#1.1	(#1.1)	Swiss Franc	CHF	3	1.21864
%R	30	2	NIS	.	,	#1.1	(#1.1)	Israel Shekel	ILS	3	3.96384
%R	31	2	$	.	,	#1.1	(#1.1)	(New Currency)	CUR	3	1
%T	OBS
%F	obs_id	parent_obs_id	guid	seq_num	obs_name	obs_descr
%R	565			0	Enterprise	<HTML><BODY></BODY></HTML>
%T	PROJECT
%F	proj_id	fy_start_month_num	rsrc_self_add_flag	allow_complete_flag	rsrc_multi_assign_flag	checkout_flag	project_flag	step_complete_flag	cost_qty_recalc_flag	batch_sum_flag	name_sep_char	def_complete_pct_type	proj_short_name	acct_id	orig_proj_id	source_proj_id	base_type_id	clndr_id	sum_base_proj_id	task_code_base	task_code_step	priority_num	wbs_max_sum_level	strgy_priority_num	last_checksum	critical_drtn_hr_cnt	def_cost_per_qty	last_recalc_date	plan_start_date	plan_end_date	scd_end_date	add_date	last_tasksum_date	fcst_start_date	def_duration_type	task_code_prefix	guid	def_qty_type	add_by_name	web_local_root_path	proj_url	def_rate_type	add_act_remain_flag	act_this_per_link_flag	def_task_type	act_pct_link_flag	critical_path_type	task_code_prefix_flag	def_rollup_dates_flag	use_project_baseline_flag	rem_target_link_flag	reset_planned_flag	allow_neg_act_flag	sum_assign_level	last_fin_dates_id	last_baseline_update_date	cr_external_key	apply_actuals_date	location_id	loaded_scope_level	export_flag	new_fin_dates_id	next_data_date	close_period_flag	sum_refresh_date	trsrcsum_loaded
%R	34738	1	Y	Y	Y	N	Y	N	N	Y	.	CP_Drtn	TEST PROJECT					44739		1000	10	10				0	0.00	2019-10-02 00:00	2019-05-06 08:00		2020-12-30 08:00	2020-03-16 13:10			DT_FixedDUR2	A	M31SEvDY9ki6TH6LB6ihnQ	QT_Hour	admin			COST_PER_QTY	N	Y	TT_Task	Y	CT_TotFloat	Y	Y	Y	Y	N	N	SL_Taskrsrc						7	Y
%T	CALENDAR
%F	clndr_id	default_flag	clndr_name	proj_id	base_clndr_id	last_chng_date	clndr_type	day_hr_cnt	week_hr_cnt	month_hr_cnt	year_hr_cnt	rsrc_private	clndr_data
%R	44739	N	5 Day	34738			CA_Project	8	40	172	2000	N	(0||CalendarData()((0||DaysOfWeek()((0||1()())(0||2()((0||0(s|08:00|f|12:00)())(0||1(s|13:00|f|17:00)())))(0||3()((0||0(s|08:00|f|12:00)())(0||1(s|13:00|f|17:00)())))(0||4()((0||0(s|08:00|f|12:00)())(0||1(s|13:00|f|17:00)())))(0||5()((0||0(s|08:00|f|12:00)())(0||1(s|13:00|f|17:00)())))(0||6()((0||0(s|08:00|f|12:00)())(0||1(s|13:00|f|17:00)())))(0||7()())))(0||Exceptions()((0||0(d|43650)())(0||1(d|43651)())(0||2(d|43710)())(0||3(d|43797)())(0||4(d|43798)())(0||5(d|43823)())(0||6(d|43824)())(0||7(d|43831)())(0||8(d|43976)())(0||9(d|44015)())(0||10(d|44081)())(0||11(d|44161)())(0||12(d|44189)())(0||13(d|44190)())))))
%T	SCHEDOPTIONS
%F	schedoptions_id	proj_id	sched_outer_depend_type	sched_open_critical_flag	sched_lag_early_start_flag	sched_retained_logic	sched_setplantoforecast	sched_float_type	sched_calendar_on_relationship_lag	sched_use_expect_end_flag	sched_progress_override	level_float_thrs_cnt	level_outer_assign_flag	level_outer_assign_priority	level_over_alloc_pct	level_within_float_flag	level_keep_sched_date_flag	level_all_rsrc_flag	sched_use_project_end_date_for_float	enable_multiple_longest_path_calc	limit_multiple_longest_path_calc	max_multiple_longest_path	use_total_float_multiple_longest_paths	key_activity_for_multiple_longest_paths	LevelPriorityList
%R	1	34738	SD_Both	N	Y	Y	N	FT_FF	rcal_Successor	Y	N	0	N	5	25	N	Y	Y	Y	N	Y	10	Y		priority_type,ASC
%T	PROJWBS
%F	wbs_id	proj_id	obs_id	seq_num	proj_node_flag	sum_data_flag	status_code	wbs_short_name	wbs_name	phase_id	parent_wbs_id	ev_user_pct	ev_etc_user_value	orig_cost	indep_remain_total_cost	ann_dscnt_rate_pct	dscnt_period_type	indep_remain_work_qty	anticip_start_date	anticip_end_date	ev_compute_type	ev_etc_compute_type	guid	tmpl_guid	plan_open_state
%R	355248	34738	565	2019	Y	N	WS_Open	TEST	TEST PROJECT		355247			0.00	0.00						EC_Cmp_pct	EE_Rem_hr	0/Zpmr8vWES1lvDJ2Yk1nQ
%T	ACTVTYPE
%F	actv_code_type_id	actv_short_len	seq_num	actv_code_type	proj_id	wbs_id	actv_code_type_scope
%T	TASK
%F	task_id	proj_id	wbs_id	clndr_id	phys_complete_pct	rev_fdbk_flag	lock_plan_flag	auto_compute_act_flag	complete_pct_type	task_type	duration_type	status_code	task_code	task_name	rsrc_id	total_float_hr_cnt	free_float_hr_cnt	remain_drtn_hr_cnt	act_work_qty	remain_work_qty	target_work_qty	target_drtn_hr_cnt	target_equip_qty	act_equip_qty	remain_equip_qty	cstr_date	act_start_date	act_end_date	late_start_date	late_end_date	expect_end_date	early_start_date	early_end_date	restart_date	reend_date	target_start_date	target_end_date	rem_late_start_date	rem_late_end_date	cstr_type	priority_type	suspend_date	resume_date	float_path	float_path_order	guid	tmpl_guid	cstr_date2	cstr_type2	driving_path_flag	act_this_per_work_qty	act_this_per_equip_qty	external_early_start_date	external_late_end_date	create_date	update_date	create_user	update_user	location_id
%R	1338401	34738	355248	44739	0	N	N	N	CP_Drtn	TT_Task	DT_FixedDrtn	TK_Complete	A1000	Test				0	0	0	0	24	0	0	0	2019-06-10 00:00	2019-06-10 08:00	2019-06-12 17:00	2020-12-30 08:00	2020-12-30 08:00		2019-10-02 00:00	2019-10-02 00:00			2019-06-10 08:00	2019-06-12 17:00			CS_MSOA	PT_Normal					AJArd2UNNkKUSaTYrFjK+A				N	0	0			2020-03-16 13:11	2020-03-17 13:25	admin	admin
%T	ACTVCODE
%F	actv_code_id	parent_actv_code_id	actv_code_type_id	actv_code_name	short_name	seq_num	color
%T	TASKPRED
%F	task_pred_id	task_id	pred_task_id	proj_id	pred_proj_id	pred_type	lag_hr_cnt	float_path	aref	arls
%T	TASKACTV
%F	task_id	actv_code_type_id	actv_code_id	proj_id
%E
