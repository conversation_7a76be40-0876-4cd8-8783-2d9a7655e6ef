
<project project-start="20060825T080000Z" manager="" company="Steelray Software" name="" mrproject-version="2">
    <calendars>
        <day-types>
            <day-type description="A default working day" name="Working" id="0"/>
            <day-type description="A default non working day" name="Nonworking" id="1"/>
            <day-type description="Use day from base calendar" name="Use base" id="2"/>
        </day-types>
        <calendar id="1" name="Standard">
            <default-week sun="1" sat="1" fri="0" thu="0" wed="0" tue="0" mon="0"/>
            <overridden-day-types>
                <overridden-day-type id="0">
                    <interval end="1200" start="0800"/>
                    <interval end="1700" start="1300"/>
                </overridden-day-type>
            </overridden-day-types>
            <days/>
            <calendar id="2" name="Unnamed Resource">
                <default-week sun="2" sat="2" fri="2" thu="2" wed="2" tue="2" mon="2"/>
                <overridden-day-types/>
                <days/>
            </calendar>
            <calendar id="3" name="<PERSON> Golden">
                <default-week sun="2" sat="2" fri="2" thu="2" wed="2" tue="2" mon="2"/>
                <overridden-day-types/>
                <days/>
            </calendar>
            <calendar id="4" name="Jon Iles">
                <default-week sun="2" sat="2" fri="2" thu="2" wed="2" tue="2" mon="2"/>
                <overridden-day-types/>
                <days/>
            </calendar>
            <calendar id="7" name="Concrete">
                <default-week sun="2" sat="2" fri="2" thu="2" wed="2" tue="2" mon="2"/>
                <overridden-day-types/>
                <days/>
            </calendar>
        </calendar>
        <calendar id="6" name="Night Shift">
            <default-week sun="1" sat="0" fri="0" thu="0" wed="0" tue="0" mon="0"/>
            <overridden-day-types>
                <overridden-day-type id="0">
                    <interval end="0000" start="2300"/>
                </overridden-day-type>
            </overridden-day-types>
            <days/>
            <calendar id="5" name="Brian Leach">
                <default-week sun="2" sat="2" fri="2" thu="2" wed="2" tue="2" mon="2"/>
                <overridden-day-types/>
                <days/>
            </calendar>
        </calendar>
    </calendars>
    <tasks>
        <task scheduling="fixed-duration" type="normal" priority="5000" percent-complete="0" work="3225600" work-start="20060825T080000Z" end="20060908T090824Z" start="20060825T000000Z" note="" name="mpp9resource" id="0">
            <predecessors/>
            <task scheduling="fixed-work" type="normal" priority="5000" percent-complete="0" work="2073600" work-start="20060825T080000Z" end="20060830T080000Z" start="20060825T000000Z" note="" name="Task A" id="2">
                <predecessors/>
            </task>
            <task scheduling="fixed-work" type="normal" priority="5000" percent-complete="0" work="1152000" work-start="20060830T080000Z" end="20060908T090824Z" start="20060830T000000Z" note="" name="Contoured Task" id="3">
                <predecessors>
                    <predecessor lag="0" type="FS" predecessor-id="2" id="1"/>
                </predecessors>
            </task>
        </task>
    </tasks>
    <resources>
        <resource calendar="2" note="" units="0" type="1" id="0"/>
        <resource calendar="3" note="" units="0" type="1" email="<EMAIL>" short-name="WG" name="Wade Golden" id="1"/>
        <resource calendar="4" note="" units="0" type="1" short-name="JI" name="Jon Iles" id="2"/>
        <resource calendar="5" note="" units="0" type="1" short-name="BL" name="Brian Leach" id="3"/>
        <resource calendar="7" note="" units="0" type="2" short-name="Con" name="Concrete" id="4"/>
    </resources>
    <allocations>
        <allocation units="100" resource-id="1" task-id="2"/>
        <allocation units="100" resource-id="2" task-id="2"/>
        <allocation units="100" resource-id="3" task-id="2"/>
        <allocation units="100" resource-id="1" task-id="3"/>
    </allocations>
</project>
