{% extends 'base.html' %}

{% block title %}甘特图 - AI-DEEP-CPMS{% endblock %}

{% block extra_css %}
<style>
.gantt-toolbar {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.project-selector {
    max-width: 350px;
}

#gantt-container {
    min-height: 600px;
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    background: white;
    overflow: hidden;
}

.task-controls {
    margin-top: 2rem;
}

.task-form {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem;
    border-radius: 1rem;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.task-item {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.task-item:hover {
    background-color: var(--light-color);
    transform: translateX(5px);
}

.stats-badge {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
    font-size: 0.875rem;
}

.toolbar-section {
    background: white;
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

.gantt-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 1rem 1rem 0 0;
    margin: -1px -1px 0 -1px;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1">
                    <i class="fas fa-chart-gantt me-2 text-primary"></i>
                    项目甘特图
                </h2>
                <p class="text-muted mb-0">可视化项目进度和任务依赖关系</p>
            </div>
            <div>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>返回首页
                </a>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="gantt-toolbar">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="toolbar-section">
                        <label for="project-select" class="form-label fw-semibold mb-2">
                            <i class="fas fa-folder-open me-1 text-primary"></i>
                            选择项目
                        </label>
                        <select id="project-select" class="form-select project-selector">
                            <option value="">请选择项目...</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fas fa-cogs me-1 text-primary"></i>
                            操作
                        </label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary flex-fill" onclick="loadGanttChart()">
                                <i class="fas fa-sync-alt me-1"></i>加载甘特图
                            </button>
                            <button class="btn btn-success flex-fill" onclick="showAddTaskForm()">
                                <i class="fas fa-plus me-1"></i>添加任务
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fas fa-eye me-1 text-primary"></i>
                            视图选项
                        </label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-secondary flex-fill" onclick="zoomToFit()">
                                <i class="fas fa-expand-arrows-alt me-1"></i>适应窗口
                            </button>
                            <button class="btn btn-outline-primary flex-fill" onclick="exportGantt()">
                                <i class="fas fa-download me-1"></i>导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 甘特图容器 -->
        <div class="card border-0 shadow-sm">
            <div class="gantt-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 fw-bold">
                            <i class="fas fa-chart-gantt me-2"></i>
                            项目进度甘特图
                        </h5>
                        <small class="opacity-75">实时可视化项目任务和进度</small>
                    </div>
                    <div class="d-flex gap-2">
                        <span class="stats-badge" id="task-count">
                            <i class="fas fa-tasks me-1"></i>任务数: 0
                        </span>
                        <span class="stats-badge" id="critical-path">
                            <i class="fas fa-route me-1"></i>关键路径: 0
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="gantt-container">
                    <div class="empty-state">
                        <i class="fas fa-chart-gantt"></i>
                        <h5 class="mt-3 mb-2">甘特图视图</h5>
                        <p class="mb-4">请选择项目并点击"加载甘特图"按钮开始可视化您的项目进度</p>
                        <div class="d-flex justify-content-center gap-3">
                            <div class="text-center">
                                <div class="feature-icon mx-auto mb-2" style="width: 2rem; height: 2rem; font-size: 0.875rem;">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <small class="text-muted">任务管理</small>
                            </div>
                            <div class="text-center">
                                <div class="feature-icon mx-auto mb-2" style="width: 2rem; height: 2rem; font-size: 0.875rem;">
                                    <i class="fas fa-link"></i>
                                </div>
                                <small class="text-muted">依赖关系</small>
                            </div>
                            <div class="text-center">
                                <div class="feature-icon mx-auto mb-2" style="width: 2rem; height: 2rem; font-size: 0.875rem;">
                                    <i class="fas fa-route"></i>
                                </div>
                                <small class="text-muted">关键路径</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 任务控制面板 -->
        <div class="task-controls" id="task-controls" style="display: none;">
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    任务列表
                                </h6>
                                <button class="btn btn-sm btn-outline-primary" onclick="loadTaskList(currentProjectId)">
                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="task-list" style="max-height: 400px; overflow-y: auto;">
                                <!-- 任务列表将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white border-bottom">
                            <h6 class="mb-0 fw-bold">
                                <i class="fas fa-info-circle me-2 text-primary"></i>
                                任务详情
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="task-details">
                                <div class="text-center py-4">
                                    <i class="fas fa-mouse-pointer fa-2x text-muted mb-3"></i>
                                    <p class="text-muted">请从左侧列表选择一个任务查看详情</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 添加任务表单 -->
        <div class="task-form" id="add-task-form" style="display: none;">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold">
                            <i class="fas fa-plus me-2 text-success"></i>
                            添加新任务
                        </h5>
                        <button type="button" class="btn-close" onclick="hideAddTaskForm()"></button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="task-form">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="task-wbs" class="form-label fw-semibold">
                                        <i class="fas fa-code me-1 text-primary"></i>
                                        WBS编码 *
                                    </label>
                                    <input type="text" class="form-control" id="task-wbs" required placeholder="例如: 1.1.1">
                                </div>
                                <div class="mb-3">
                                    <label for="task-name" class="form-label fw-semibold">
                                        <i class="fas fa-tag me-1 text-primary"></i>
                                        任务名称 *
                                    </label>
                                    <input type="text" class="form-control" id="task-name" required placeholder="输入任务名称">
                                </div>
                                <div class="mb-3">
                                    <label for="task-unit" class="form-label fw-semibold">
                                        <i class="fas fa-ruler me-1 text-primary"></i>
                                        单位
                                    </label>
                                    <input type="text" class="form-control" id="task-unit" placeholder="例如: 立方米、平方米">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="task-quantity" class="form-label fw-semibold">
                                        <i class="fas fa-calculator me-1 text-primary"></i>
                                        工程数量
                                    </label>
                                    <input type="number" class="form-control" id="task-quantity" step="0.01" placeholder="0.00">
                                </div>
                                <div class="mb-3">
                                    <label for="task-efficiency" class="form-label fw-semibold">
                                        <i class="fas fa-tachometer-alt me-1 text-primary"></i>
                                        每日工效
                                    </label>
                                    <input type="number" class="form-control" id="task-efficiency" step="0.01" placeholder="0.00">
                                </div>
                                <div class="mb-3">
                                    <label for="task-start" class="form-label fw-semibold">
                                        <i class="fas fa-calendar-alt me-1 text-primary"></i>
                                        开始时间 *
                                    </label>
                                    <input type="datetime-local" class="form-control" id="task-start" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-4">
                                    <label for="task-description" class="form-label fw-semibold">
                                        <i class="fas fa-align-left me-1 text-primary"></i>
                                        任务描述
                                    </label>
                                    <textarea class="form-control" id="task-description" rows="3" placeholder="详细描述任务内容和要求..."></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2 justify-content-end">
                            <button type="button" class="btn btn-outline-secondary" onclick="hideAddTaskForm()">
                                <i class="fas fa-times me-1"></i>取消
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>创建任务
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentProjectId = null;
let ganttChart = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadProjects();
    
    // 绑定表单提交事件
    document.getElementById('task-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createTask();
    });
});

// 加载项目列表
function loadProjects() {
    fetch('/api/projects/')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('project-select');
            select.innerHTML = '<option value="">请选择项目...</option>';
            
            if (data.results) {
                data.results.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.id;
                    option.textContent = project.name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading projects:', error);
            alert('加载项目列表失败');
        });
}

// 加载甘特图
function loadGanttChart() {
    const projectId = document.getElementById('project-select').value;
    if (!projectId) {
        alert('请先选择一个项目');
        return;
    }
    
    currentProjectId = projectId;
    
    fetch(`/api/projects/${projectId}/gantt_data/`)
        .then(response => response.json())
        .then(data => {
            renderGanttChart(data);
            loadTaskList(projectId);
            document.getElementById('task-controls').style.display = 'block';
            
            // 更新统计信息
            document.getElementById('task-count').textContent = `任务数: ${data.tasks.length}`;
            const criticalTasks = data.tasks.filter(task => task.critical).length;
            document.getElementById('critical-path').textContent = `关键路径: ${criticalTasks}`;
        })
        .catch(error => {
            console.error('Error loading gantt data:', error);
            alert('加载甘特图数据失败');
        });
}

// 渲染甘特图
function renderGanttChart(data) {
    ganttChart = Highcharts.ganttChart('gantt-container', {
        title: {
            text: '项目甘特图'
        },
        xAxis: {
            type: 'datetime',
            min: data.min_date,
            max: data.max_date
        },
        yAxis: {
            type: 'category',
            grid: {
                columns: [{
                    title: {
                        text: 'WBS'
                    },
                    categories: data.tasks.map(task => task.name.split(' ')[0])
                }, {
                    title: {
                        text: '任务名称'
                    },
                    categories: data.tasks.map(task => task.name.split(' ').slice(1).join(' '))
                }]
            }
        },
        series: [{
            name: '任务',
            data: data.tasks.map((task, index) => ({
                id: task.id,
                name: task.name,
                start: new Date(task.start).getTime(),
                end: new Date(task.end).getTime(),
                y: index,
                completed: task.progress || 0,
                color: task.critical ? '#ff6b6b' : (task.status === 'completed' ? '#51cf66' : '#339af0')
            }))
        }],
        plotOptions: {
            gantt: {
                dataLabels: {
                    enabled: true,
                    format: '{point.name}'
                }
            }
        },
        tooltip: {
            pointFormat: '<b>{point.name}</b><br/>开始: {point.start:%Y-%m-%d}<br/>结束: {point.end:%Y-%m-%d}<br/>进度: {point.completed:.1f}%'
        }
    });
}

// 加载任务列表
function loadTaskList(projectId) {
    fetch(`/api/tasks/?project_id=${projectId}`)
        .then(response => response.json())
        .then(data => {
            const taskList = document.getElementById('task-list');
            let html = '';
            
            if (data.results && data.results.length > 0) {
                data.results.forEach(task => {
                    html += `
                        <div class="task-item p-2 border-bottom" onclick="showTaskDetails(${task.id})" style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${task.wbs_code}</strong> ${task.name}
                                    <br><small class="text-muted">${task.status}</small>
                                </div>
                                <div>
                                    <span class="badge bg-${getStatusColor(task.status)}">${task.progress_percentage}%</span>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html = '<p class="text-muted">暂无任务</p>';
            }
            
            taskList.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading tasks:', error);
        });
}

// 显示任务详情
function showTaskDetails(taskId) {
    fetch(`/api/tasks/${taskId}/`)
        .then(response => response.json())
        .then(task => {
            const details = document.getElementById('task-details');
            details.innerHTML = `
                <h6>${task.wbs_code} - ${task.name}</h6>
                <p><strong>描述:</strong> ${task.description || '无'}</p>
                <p><strong>状态:</strong> <span class="badge bg-${getStatusColor(task.status)}">${task.status}</span></p>
                <p><strong>进度:</strong> ${task.progress_percentage}%</p>
                <p><strong>开始时间:</strong> ${new Date(task.start_date).toLocaleDateString()}</p>
                <p><strong>结束时间:</strong> ${new Date(task.end_date).toLocaleDateString()}</p>
                <p><strong>工程数量:</strong> ${task.quantity} ${task.unit}</p>
                <p><strong>每日工效:</strong> ${task.daily_efficiency}</p>
                <p><strong>计划工期:</strong> ${task.planned_duration} 天</p>
                <div class="mt-3">
                    <button class="btn btn-sm btn-primary" onclick="editTask(${task.id})">编辑</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteTask(${task.id})">删除</button>
                </div>
            `;
        })
        .catch(error => {
            console.error('Error loading task details:', error);
        });
}

// 显示添加任务表单
function showAddTaskForm() {
    if (!currentProjectId) {
        alert('请先选择一个项目');
        return;
    }
    document.getElementById('add-task-form').style.display = 'block';
}

// 隐藏添加任务表单
function hideAddTaskForm() {
    document.getElementById('add-task-form').style.display = 'none';
    document.getElementById('task-form').reset();
}

// 创建任务
function createTask() {
    const formData = {
        project: currentProjectId,
        wbs_code: document.getElementById('task-wbs').value,
        name: document.getElementById('task-name').value,
        description: document.getElementById('task-description').value,
        unit: document.getElementById('task-unit').value,
        quantity: parseFloat(document.getElementById('task-quantity').value) || 1,
        daily_efficiency: parseFloat(document.getElementById('task-efficiency').value) || 1,
        start_date: document.getElementById('task-start').value,
        end_date: document.getElementById('task-start').value, // 临时设置，后端会计算
        status: 'not_started',
        priority: 'normal',
        order: 0
    };
    
    fetch('/api/tasks/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            alert('任务创建成功');
            hideAddTaskForm();
            loadGanttChart(); // 重新加载甘特图
        } else {
            alert('任务创建失败: ' + JSON.stringify(data));
        }
    })
    .catch(error => {
        console.error('Error creating task:', error);
        alert('任务创建失败');
    });
}

// 辅助函数
function getStatusColor(status) {
    const colors = {
        'not_started': 'secondary',
        'in_progress': 'primary',
        'completed': 'success',
        'paused': 'warning',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function zoomToFit() {
    if (ganttChart) {
        ganttChart.xAxis[0].setExtremes();
    }
}

function exportGantt() {
    if (ganttChart) {
        ganttChart.exportChart({
            type: 'image/png',
            filename: 'gantt-chart'
        });
    }
}

function editTask(taskId) {
    alert(`编辑任务 ${taskId} 功能开发中...`);
}

function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？')) {
        fetch(`/api/tasks/${taskId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => {
            if (response.ok) {
                alert('任务删除成功');
                loadGanttChart(); // 重新加载甘特图
            } else {
                alert('任务删除失败');
            }
        })
        .catch(error => {
            console.error('Error deleting task:', error);
            alert('任务删除失败');
        });
    }
}
</script>
{% endblock %}
