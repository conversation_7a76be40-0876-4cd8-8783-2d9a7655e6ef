<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Highcharts -->
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/gantt/gantt.js"></script>
    <script src="https://code.highcharts.com/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/modules/export-data.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-chart-gantt me-2 text-primary"></i>
                    甘特图测试页面
                </h1>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    这是一个测试页面，用于验证Highcharts甘特图是否正常工作。
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-gantt me-2"></i>
                            示例甘特图
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="gantt-container" style="height: 400px;"></div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="createTestGantt()">
                        <i class="fas fa-play me-2"></i>创建测试甘特图
                    </button>
                    <button class="btn btn-success" onclick="exportGantt()">
                        <i class="fas fa-download me-2"></i>导出甘特图
                    </button>
                    <a href="/gantt/" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>返回甘特图页面
                    </a>
                </div>
                
                <div class="mt-4">
                    <h6>调试信息:</h6>
                    <div id="debug-info" class="alert alert-secondary">
                        <div>Highcharts 状态: <span id="highcharts-status">检查中...</span></div>
                        <div>甘特图模块状态: <span id="gantt-status">检查中...</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testGanttChart = null;
        
        // 页面加载时检查Highcharts状态
        document.addEventListener('DOMContentLoaded', function() {
            checkHighchartsStatus();
            createTestGantt();
        });
        
        // 检查Highcharts状态
        function checkHighchartsStatus() {
            const highchartsStatus = document.getElementById('highcharts-status');
            const ganttStatus = document.getElementById('gantt-status');
            
            if (typeof Highcharts !== 'undefined') {
                highchartsStatus.innerHTML = '<span class="text-success">✅ 已加载</span>';
                
                if (typeof Highcharts.ganttChart === 'function') {
                    ganttStatus.innerHTML = '<span class="text-success">✅ 已加载</span>';
                } else {
                    ganttStatus.innerHTML = '<span class="text-danger">❌ 未加载</span>';
                }
            } else {
                highchartsStatus.innerHTML = '<span class="text-danger">❌ 未加载</span>';
                ganttStatus.innerHTML = '<span class="text-danger">❌ 未加载</span>';
            }
        }
        
        // 创建测试甘特图
        function createTestGantt() {
            if (typeof Highcharts === 'undefined' || typeof Highcharts.ganttChart !== 'function') {
                document.getElementById('gantt-container').innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h5>Highcharts甘特图模块未加载</h5>
                        <p>请检查网络连接或刷新页面重试</p>
                    </div>
                `;
                return;
            }
            
            try {
                // 示例数据
                const today = new Date();
                const day = 24 * 60 * 60 * 1000;
                
                testGanttChart = Highcharts.ganttChart('gantt-container', {
                    title: {
                        text: '示例项目甘特图'
                    },
                    subtitle: {
                        text: '这是一个测试甘特图，验证Highcharts功能是否正常'
                    },
                    xAxis: {
                        type: 'datetime'
                    },
                    yAxis: {
                        type: 'category',
                        grid: {
                            columns: [{
                                title: {
                                    text: 'WBS'
                                },
                                categories: ['1.1', '1.2', '1.3', '1.4']
                            }, {
                                title: {
                                    text: '任务名称'
                                },
                                categories: ['项目启动', '需求分析', '系统设计', '开发实现']
                            }]
                        }
                    },
                    series: [{
                        name: '项目任务',
                        data: [{
                            id: 'task1',
                            name: '项目启动',
                            start: today.getTime(),
                            end: today.getTime() + 3 * day,
                            y: 0,
                            completed: 1,
                            color: '#10b981'
                        }, {
                            id: 'task2',
                            name: '需求分析',
                            start: today.getTime() + 2 * day,
                            end: today.getTime() + 7 * day,
                            y: 1,
                            completed: 0.6,
                            color: '#3b82f6'
                        }, {
                            id: 'task3',
                            name: '系统设计',
                            start: today.getTime() + 6 * day,
                            end: today.getTime() + 12 * day,
                            y: 2,
                            completed: 0.2,
                            color: '#f59e0b'
                        }, {
                            id: 'task4',
                            name: '开发实现',
                            start: today.getTime() + 10 * day,
                            end: today.getTime() + 20 * day,
                            y: 3,
                            completed: 0,
                            color: '#ef4444'
                        }]
                    }],
                    plotOptions: {
                        gantt: {
                            dataLabels: {
                                enabled: true,
                                format: '{point.name}',
                                style: {
                                    fontSize: '12px'
                                }
                            }
                        }
                    },
                    tooltip: {
                        pointFormat: '<b>{point.name}</b><br/>开始: {point.start:%Y-%m-%d}<br/>结束: {point.end:%Y-%m-%d}<br/>进度: {point.completed:.1%}'
                    },
                    credits: {
                        enabled: false
                    }
                });
                
                console.log('测试甘特图创建成功');
                
            } catch (error) {
                console.error('创建甘特图失败:', error);
                document.getElementById('gantt-container').innerHTML = `
                    <div class="alert alert-danger text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h5>甘特图创建失败</h5>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // 导出甘特图
        function exportGantt() {
            if (testGanttChart) {
                testGanttChart.exportChart({
                    type: 'image/png',
                    filename: 'test-gantt-chart'
                });
            } else {
                alert('请先创建甘特图');
            }
        }
    </script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
