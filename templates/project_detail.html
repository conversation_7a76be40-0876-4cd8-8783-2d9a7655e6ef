{% extends 'base.html' %}

{% block title %}项目详情 - AI-DEEP-CPMS{% endblock %}

{% block extra_css %}
<style>
.project-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem 1rem 0 0;
    margin: -1px -1px 0 -1px;
}

.progress-ring {
    width: 120px;
    height: 120px;
}

.progress-ring circle {
    fill: transparent;
    stroke-width: 8;
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}

.progress-ring .background {
    stroke: rgba(255, 255, 255, 0.2);
}

.progress-ring .progress {
    stroke: #10b981;
    stroke-dasharray: 314;
    stroke-dashoffset: 314;
    transition: stroke-dashoffset 0.5s ease;
}

.metric-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.metric-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.metric-label {
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 0.875rem;
}

.tab-content {
    padding: 2rem 0;
}

.task-item {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.task-item:hover {
    box-shadow: var(--shadow-sm);
    transform: translateX(5px);
}

.list-item {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 项目头部 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="project-header">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-3">
                            <a href="/" class="btn btn-outline-light me-3">
                                <i class="fas fa-arrow-left"></i>
                            </a>
                            <div>
                                <h2 class="fw-bold mb-1" id="project-name">项目名称</h2>
                                <p class="mb-0 opacity-75" id="project-description">项目描述</p>
                            </div>
                        </div>
                        <div class="d-flex flex-wrap gap-3">
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="fas fa-calendar me-1"></i>
                                <span id="project-start-date">开始日期</span>
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                <span id="project-location">项目地点</span>
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2" id="project-status">
                                <i class="fas fa-info-circle me-1"></i>
                                项目状态
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="progress-ring mx-auto">
                            <svg width="120" height="120">
                                <circle class="background" cx="60" cy="60" r="50"></circle>
                                <circle class="progress" cx="60" cy="60" r="50" id="progress-circle"></circle>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle text-center">
                                <div class="h3 fw-bold mb-0" id="progress-percentage">0%</div>
                                <small class="opacity-75">完成进度</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目指标 -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="total-tasks">0</div>
                    <div class="metric-label">
                        <i class="fas fa-tasks me-1"></i>
                        总任务数
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="completed-tasks">0</div>
                    <div class="metric-label">
                        <i class="fas fa-check-circle me-1"></i>
                        已完成任务
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="total-cost">¥0</div>
                    <div class="metric-label">
                        <i class="fas fa-coins me-1"></i>
                        项目总成本
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="list-items-count">0</div>
                    <div class="metric-label">
                        <i class="fas fa-list me-1"></i>
                        清单项数量
                    </div>
                </div>
            </div>
        </div>

        <!-- 选项卡导航 -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <ul class="nav nav-tabs card-header-tabs" id="projectTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab">
                            <i class="fas fa-tasks me-2"></i>任务管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="gantt-tab" data-bs-toggle="tab" data-bs-target="#gantt" type="button" role="tab">
                            <i class="fas fa-chart-gantt me-2"></i>甘特图
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="list-tab" data-bs-toggle="tab" data-bs-target="#list" type="button" role="tab">
                            <i class="fas fa-list-check me-2"></i>项目清单
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="resources-tab" data-bs-toggle="tab" data-bs-target="#resources" type="button" role="tab">
                            <i class="fas fa-coins me-2"></i>资源管理
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="projectTabContent">
                    <!-- 任务管理 -->
                    <div class="tab-pane fade show active" id="tasks" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="fw-bold mb-0">项目任务</h5>
                            <button class="btn btn-success" onclick="addTask()">
                                <i class="fas fa-plus me-1"></i>添加任务
                            </button>
                        </div>
                        <div id="tasks-list">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="text-muted mt-3">正在加载任务数据...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 甘特图 -->
                    <div class="tab-pane fade" id="gantt" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="fw-bold mb-0">项目甘特图</h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-secondary" onclick="refreshGantt()">
                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                </button>
                                <button class="btn btn-outline-primary" onclick="exportGantt()">
                                    <i class="fas fa-download me-1"></i>导出
                                </button>
                            </div>
                        </div>
                        <div id="gantt-container" style="height: 500px; border: 1px solid var(--border-color); border-radius: 0.75rem;">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <i class="fas fa-chart-gantt fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">甘特图将在这里显示</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 项目清单 -->
                    <div class="tab-pane fade" id="list" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="fw-bold mb-0">项目清单</h5>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary" onclick="importList()">
                                    <i class="fas fa-file-import me-1"></i>导入清单
                                </button>
                                <button class="btn btn-success" onclick="addListItem()">
                                    <i class="fas fa-plus me-1"></i>添加清单项
                                </button>
                            </div>
                        </div>
                        <div id="list-items">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="text-muted mt-3">正在加载清单数据...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 资源管理 -->
                    <div class="tab-pane fade" id="resources" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h5 class="fw-bold mb-0">项目资源</h5>
                            <button class="btn btn-success" onclick="addResource()">
                                <i class="fas fa-plus me-1"></i>添加资源
                            </button>
                        </div>
                        <div id="resources-list">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="text-muted mt-3">正在加载资源数据...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentProjectId = null;

// 页面加载时获取项目ID并加载数据
document.addEventListener('DOMContentLoaded', function() {
    // 从URL获取项目ID
    const urlParams = new URLSearchParams(window.location.search);
    currentProjectId = urlParams.get('id');
    
    if (currentProjectId) {
        loadProjectDetails();
        loadProjectMetrics();
        loadTasks();
    } else {
        alert('项目ID不存在');
        window.location.href = '/';
    }
});

// 加载项目详情
function loadProjectDetails() {
    fetch(`/api/projects/${currentProjectId}/`)
        .then(response => response.json())
        .then(project => {
            document.getElementById('project-name').textContent = project.name;
            document.getElementById('project-description').textContent = project.description || '暂无描述';
            document.getElementById('project-start-date').textContent = new Date(project.start_date).toLocaleDateString('zh-CN');
            document.getElementById('project-location').textContent = project.location || '未设置';
            
            const statusBadge = document.getElementById('project-status');
            statusBadge.innerHTML = `<i class="fas fa-info-circle me-1"></i>${getStatusText(project.status)}`;
            statusBadge.className = `badge bg-${getStatusColor(project.status)} text-white px-3 py-2`;
        })
        .catch(error => {
            console.error('Error loading project details:', error);
        });
}

// 加载项目指标
function loadProjectMetrics() {
    // 加载任务统计
    fetch(`/api/tasks/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            const tasks = data.results || [];
            const totalTasks = tasks.length;
            const completedTasks = tasks.filter(t => t.status === 'completed').length;
            const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
            
            document.getElementById('total-tasks').textContent = totalTasks;
            document.getElementById('completed-tasks').textContent = completedTasks;
            document.getElementById('progress-percentage').textContent = progress + '%';
            
            // 更新进度环
            const circle = document.getElementById('progress-circle');
            const circumference = 2 * Math.PI * 50;
            const offset = circumference - (progress / 100) * circumference;
            circle.style.strokeDashoffset = offset;
        });
    
    // 加载清单统计
    fetch(`/api/list-items/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            const listItems = data.results || [];
            const totalCost = listItems.reduce((sum, item) => sum + (parseFloat(item.comprehensive_total_price) || 0), 0);
            
            document.getElementById('list-items-count').textContent = listItems.length;
            document.getElementById('total-cost').textContent = '¥' + totalCost.toLocaleString('zh-CN', {minimumFractionDigits: 2});
        });
}

// 加载任务列表
function loadTasks() {
    fetch(`/api/tasks/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            const tasks = data.results || [];
            let html = '';
            
            if (tasks.length > 0) {
                tasks.forEach(task => {
                    html += `
                        <div class="task-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="fw-bold mb-1">${task.wbs_code} - ${task.name}</h6>
                                    <p class="text-muted mb-2">${task.description || '暂无描述'}</p>
                                    <div class="d-flex gap-3 text-sm">
                                        <span><i class="fas fa-calendar me-1"></i>${new Date(task.start_date).toLocaleDateString('zh-CN')}</span>
                                        <span><i class="fas fa-clock me-1"></i>${task.planned_duration || 0}天</span>
                                        <span><i class="fas fa-percentage me-1"></i>${task.progress_percentage}%</span>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-${getStatusColor(task.status)} mb-2">${getStatusText(task.status)}</span>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-sm btn-outline-primary" onclick="editTask(${task.id})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteTask(${task.id})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html = `
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无任务</h5>
                        <p class="text-muted">点击"添加任务"按钮创建第一个任务</p>
                    </div>
                `;
            }
            
            document.getElementById('tasks-list').innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading tasks:', error);
            document.getElementById('tasks-list').innerHTML = '<p class="text-danger">加载失败</p>';
        });
}

// 辅助函数
function getStatusColor(status) {
    const colors = {
        'planning': 'secondary',
        'active': 'primary',
        'paused': 'warning',
        'completed': 'success',
        'cancelled': 'danger',
        'not_started': 'secondary',
        'in_progress': 'primary'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'planning': '规划中',
        'active': '进行中',
        'paused': '暂停',
        'completed': '已完成',
        'cancelled': '已取消',
        'not_started': '未开始',
        'in_progress': '进行中'
    };
    return texts[status] || status;
}

// 功能函数（待实现）
function addTask() {
    alert('添加任务功能开发中...');
}

function editTask(taskId) {
    alert(`编辑任务 ${taskId} 功能开发中...`);
}

function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？')) {
        // 实现删除逻辑
        alert('删除功能开发中...');
    }
}

function refreshGantt() {
    alert('刷新甘特图功能开发中...');
}

function exportGantt() {
    alert('导出甘特图功能开发中...');
}

function importList() {
    alert('导入清单功能开发中...');
}

function addListItem() {
    alert('添加清单项功能开发中...');
}

function addResource() {
    alert('添加资源功能开发中...');
}
</script>
{% endblock %}
