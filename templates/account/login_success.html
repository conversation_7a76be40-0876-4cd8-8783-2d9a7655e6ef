{% extends 'base.html' %}

{% block title %}登录成功 - AI-DEEP-CPMS{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card border-0 shadow-lg">
            <div class="card-body p-5">
                <!-- 登录成功标题 -->
                <div class="text-center mb-4">
                    <div class="feature-icon mx-auto mb-3" style="width: 4rem; height: 4rem; font-size: 2rem; background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="fw-bold">登录成功！</h3>
                    <p class="text-muted">欢迎回到AI-DEEP-CPMS</p>
                </div>

                <!-- 用户信息 -->
                <div class="alert alert-success border-0 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-user-circle fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-1">欢迎，{{ user.username }}！</h6>
                            <small>{{ user.email }}</small>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="mb-4">
                    <h6 class="fw-bold mb-3">您可以：</h6>
                    <div class="d-grid gap-2">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>
                            进入项目管理
                        </a>
                        <a href="/gantt/" class="btn btn-outline-primary">
                            <i class="fas fa-chart-gantt me-2"></i>
                            查看甘特图
                        </a>
                        <a href="/admin/" class="btn btn-outline-secondary" target="_blank">
                            <i class="fas fa-cog me-2"></i>
                            管理后台
                        </a>
                    </div>
                </div>

                <!-- 自动跳转提示 -->
                <div class="text-center">
                    <p class="text-muted mb-0">
                        <i class="fas fa-clock me-1"></i>
                        3秒后自动跳转到首页...
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 3秒后自动跳转到首页
setTimeout(function() {
    window.location.href = '/';
}, 3000);
</script>
{% endblock %}
