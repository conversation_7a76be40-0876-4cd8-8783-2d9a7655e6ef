{% extends 'base.html' %}

{% block title %}首页 - AI-DEEP-CPMS{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<div class="row mb-5">
    <div class="col-12">
        <div class="jumbotron text-white position-relative overflow-hidden">
            <div class="position-absolute top-0 start-0 w-100 h-100" style="background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>') no-repeat center center; background-size: cover;"></div>
            <div class="position-relative">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <h1 class="display-4 fw-bold mb-4">
                            <i class="fas fa-rocket me-3"></i>
                            欢迎使用 AI-DEEP-CPMS
                        </h1>
                        <p class="lead mb-4">智能深度精细化项目管理系统 - 类似Microsoft Project的专业项目管理工具</p>
                        <div class="d-flex flex-wrap gap-3 mb-4">
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="fas fa-check me-1"></i>甘特图可视化
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="fas fa-check me-1"></i>成本精细化管理
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="fas fa-check me-1"></i>多格式文件支持
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="fas fa-check me-1"></i>智能分析报表
                            </span>
                        </div>
                        {% if not user.is_authenticated %}
                        <div class="d-flex gap-3">
                            <a class="btn btn-light btn-lg px-4" href="/accounts/signup/">
                                <i class="fas fa-user-plus me-2"></i>
                                免费试用30天
                            </a>
                            <a class="btn btn-outline-light btn-lg px-4" href="/accounts/login/">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                立即登录
                            </a>
                        </div>
                        {% else %}
                        <div class="d-flex gap-3">
                            <a class="btn btn-light btn-lg px-4" href="/gantt/">
                                <i class="fas fa-chart-gantt me-2"></i>
                                查看甘特图
                            </a>
                            <a class="btn btn-outline-light btn-lg px-4" href="#projects-section">
                                <i class="fas fa-folder-open me-2"></i>
                                我的项目
                            </a>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-lg-4 text-center">
                        <div class="feature-icon mx-auto mb-3" style="width: 6rem; height: 6rem; font-size: 3rem;">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <h5 class="text-white">专业项目管理</h5>
                        <p class="text-white-50">让项目管理变得简单高效</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if user.is_authenticated %}
<!-- 统计数据 -->
<div class="row mb-5">
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number" id="total-projects">0</div>
            <div class="stats-label">
                <i class="fas fa-folder-open me-1"></i>
                总项目数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number" id="total-tasks">0</div>
            <div class="stats-label">
                <i class="fas fa-tasks me-1"></i>
                总任务数
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number" id="active-projects">0</div>
            <div class="stats-label">
                <i class="fas fa-play-circle me-1"></i>
                进行中项目
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card">
            <div class="stats-number" id="completion-rate">0%</div>
            <div class="stats-label">
                <i class="fas fa-chart-line me-1"></i>
                完成率
            </div>
        </div>
    </div>
</div>

<!-- 功能卡片 -->
<div class="row mb-5">
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-folder-open"></i>
                </div>
                <h5 class="card-title">项目管理</h5>
                <p class="card-text text-muted">创建和管理您的项目，设置工作日历和基本信息</p>
                <a href="#projects-section" class="btn btn-primary" onclick="loadProjects()">
                    <i class="fas fa-arrow-right me-1"></i>进入
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-chart-gantt"></i>
                </div>
                <h5 class="card-title">甘特图</h5>
                <p class="card-text text-muted">可视化项目进度，支持拖拽调整和依赖关系</p>
                <a href="/gantt/" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-1"></i>查看
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-list-check"></i>
                </div>
                <h5 class="card-title">清单管理</h5>
                <p class="card-text text-muted">管理项目清单和定额，支持Excel导入导出</p>
                <a href="#" class="btn btn-primary" onclick="loadListItems()">
                    <i class="fas fa-arrow-right me-1"></i>管理
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="feature-icon mx-auto">
                    <i class="fas fa-coins"></i>
                </div>
                <h5 class="card-title">资源管理</h5>
                <p class="card-text text-muted">管理项目资源和成本，实现精细化成本控制</p>
                <a href="#" class="btn btn-primary" onclick="loadResources()">
                    <i class="fas fa-arrow-right me-1"></i>管理
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 项目列表区域 -->
<div class="row" id="projects-section">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-folder-open me-2 text-primary"></i>
                        我的项目
                    </h5>
                    <small class="text-muted">管理您的所有项目</small>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshProjects()">
                        <i class="fas fa-sync-alt me-1"></i>刷新
                    </button>
                    <button class="btn btn-success btn-sm" onclick="createProject()">
                        <i class="fas fa-plus me-1"></i>新建项目
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="projects-list">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="text-muted mt-3">正在加载项目数据...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 甘特图区域 -->
<div class="row mt-4" id="gantt-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-chart-gantt me-2 text-primary"></i>
                        项目甘特图
                    </h5>
                    <small class="text-muted">可视化项目进度和任务依赖关系</small>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="zoomToFit()">
                        <i class="fas fa-expand-arrows-alt me-1"></i>适应窗口
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportGantt()">
                        <i class="fas fa-download me-1"></i>导出
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="gantt-container" style="height: 500px; border-radius: 0.5rem; border: 1px solid var(--border-color);"></div>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- 功能特性展示 -->
<div class="row mb-5">
    <div class="col-12 text-center mb-5">
        <h2 class="fw-bold">为什么选择 AI-DEEP-CPMS？</h2>
        <p class="text-muted lead">专业的项目管理解决方案，让您的项目管理更加高效</p>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="feature-icon mx-auto mb-3">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <h5 class="card-title fw-bold">智能项目计划</h5>
                <p class="card-text text-muted">创建详细的项目计划，包含任务、时间线和依赖关系。支持WBS分解和多级任务管理。</p>
                <ul class="list-unstyled text-start">
                    <li><i class="fas fa-check text-success me-2"></i>WBS任务分解</li>
                    <li><i class="fas fa-check text-success me-2"></i>依赖关系管理</li>
                    <li><i class="fas fa-check text-success me-2"></i>自动工期计算</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="feature-icon mx-auto mb-3">
                    <i class="fas fa-chart-gantt"></i>
                </div>
                <h5 class="card-title fw-bold">可视化甘特图</h5>
                <p class="card-text text-muted">可视化项目进度，支持拖拽调整和实时更新。关键路径自动标识。</p>
                <ul class="list-unstyled text-start">
                    <li><i class="fas fa-check text-success me-2"></i>交互式甘特图</li>
                    <li><i class="fas fa-check text-success me-2"></i>关键路径分析</li>
                    <li><i class="fas fa-check text-success me-2"></i>进度实时跟踪</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center p-4">
                <div class="feature-icon mx-auto mb-3">
                    <i class="fas fa-coins"></i>
                </div>
                <h5 class="card-title fw-bold">精细化成本管理</h5>
                <p class="card-text text-muted">精细化成本控制，支持清单、定额和资源管理。实现项目成本的全面掌控。</p>
                <ul class="list-unstyled text-start">
                    <li><i class="fas fa-check text-success me-2"></i>清单定额管理</li>
                    <li><i class="fas fa-check text-success me-2"></i>资源价格控制</li>
                    <li><i class="fas fa-check text-success me-2"></i>成本分析报表</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- 更多特性 -->
<div class="row mb-5">
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                        <i class="fas fa-file-import"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">多格式文件支持</h5>
                </div>
                <p class="text-muted mb-0">支持Excel、PDF、MPP等多种格式的文件导入导出，兼容Microsoft Project。</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">团队协作</h5>
                </div>
                <p class="text-muted mb-0">支持多用户协作，权限管理，实时同步项目进度和变更。</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">智能分析</h5>
                </div>
                <p class="text-muted mb-0">AI驱动的项目分析，提供进度预测、风险识别和优化建议。</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="feature-icon me-3" style="width: 2.5rem; height: 2.5rem; font-size: 1rem;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h5 class="mb-0 fw-bold">移动端支持</h5>
                </div>
                <p class="text-muted mb-0">响应式设计，支持手机和平板访问，随时随地管理项目。</p>
            </div>
        </div>
    </div>
</div>

<!-- 行动号召 -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);">
            <div class="card-body text-center text-white p-5">
                <h3 class="fw-bold mb-3">准备开始您的项目管理之旅？</h3>
                <p class="lead mb-4">立即注册，享受30天免费试用，体验专业的项目管理功能</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="/accounts/signup/" class="btn btn-light btn-lg px-4">
                        <i class="fas fa-rocket me-2"></i>
                        免费开始试用
                    </a>
                    <a href="/accounts/login/" class="btn btn-outline-light btn-lg px-4">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        已有账户登录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if user.is_authenticated %}
<script>
// 加载统计数据
function loadStats() {
    fetch('/api/projects/')
        .then(response => response.json())
        .then(data => {
            const projects = data.results || [];
            const totalProjects = projects.length;
            const activeProjects = projects.filter(p => p.status === 'active').length;

            document.getElementById('total-projects').textContent = totalProjects;
            document.getElementById('active-projects').textContent = activeProjects;

            // 加载任务统计
            return fetch('/api/tasks/');
        })
        .then(response => response.json())
        .then(data => {
            const tasks = data.results || [];
            const totalTasks = tasks.length;
            const completedTasks = tasks.filter(t => t.status === 'completed').length;
            const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

            document.getElementById('total-tasks').textContent = totalTasks;
            document.getElementById('completion-rate').textContent = completionRate + '%';
        })
        .catch(error => {
            console.error('Error loading stats:', error);
        });
}

// 加载项目列表
function loadProjects() {
    fetch('/api/projects/')
        .then(response => response.json())
        .then(data => {
            let html = '';
            if (data.results && data.results.length > 0) {
                html = '<div class="row">';
                data.results.forEach(project => {
                    const statusColor = getStatusColor(project.status);
                    const statusText = getStatusText(project.status);
                    const startDate = new Date(project.start_date).toLocaleDateString('zh-CN');

                    html += `
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 project-card" style="cursor: pointer;" onclick="viewProject(${project.id})">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title fw-bold mb-0">${project.name}</h6>
                                        <span class="badge bg-${statusColor}">${statusText}</span>
                                    </div>
                                    <p class="card-text text-muted small mb-3">${project.description || '暂无描述'}</p>
                                    <div class="d-flex justify-content-between align-items-center text-small">
                                        <span class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            ${startDate}
                                        </span>
                                        <span class="text-muted">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            ${project.location || '未设置'}
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent border-top-0">
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-primary flex-fill" onclick="event.stopPropagation(); viewProject(${project.id})">
                                            <i class="fas fa-eye me-1"></i>查看
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); viewProjectTasks(${project.id})">
                                            <i class="fas fa-tasks me-1"></i>任务
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); loadGanttChart(${project.id})">
                                            <i class="fas fa-chart-gantt me-1"></i>甘特图
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
            } else {
                html = `
                    <div class="text-center py-5">
                        <div class="feature-icon mx-auto mb-3" style="background: var(--light-color); color: var(--secondary-color);">
                            <i class="fas fa-folder-plus"></i>
                        </div>
                        <h5 class="text-muted">暂无项目</h5>
                        <p class="text-muted">创建您的第一个项目开始管理</p>
                        <button class="btn btn-primary" onclick="createProject()">
                            <i class="fas fa-plus me-1"></i>创建项目
                        </button>
                    </div>
                `;
            }
            document.getElementById('projects-list').innerHTML = html;
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('projects-list').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                    <h5 class="text-danger">加载失败</h5>
                    <p class="text-muted">请检查网络连接或稍后重试</p>
                    <button class="btn btn-outline-primary" onclick="loadProjects()">
                        <i class="fas fa-sync-alt me-1"></i>重新加载
                    </button>
                </div>
            `;
        });
}

// 加载甘特图
function loadGanttChart(projectId) {
    if (!projectId) {
        alert('请先选择一个项目');
        return;
    }
    
    fetch(`/api/projects/${projectId}/gantt_data/`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('gantt-section').style.display = 'block';
            
            Highcharts.ganttChart('gantt-container', {
                title: {
                    text: '项目甘特图'
                },
                xAxis: {
                    type: 'datetime',
                    min: data.min_date,
                    max: data.max_date
                },
                series: [{
                    name: '任务',
                    data: data.tasks
                }]
            });
        })
        .catch(error => {
            console.error('Error:', error);
            alert('加载甘特图失败');
        });
}

// 辅助函数
function getStatusColor(status) {
    const colors = {
        'planning': 'secondary',
        'active': 'primary',
        'paused': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'planning': '规划中',
        'active': '进行中',
        'paused': '暂停',
        'completed': '已完成',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

function createProject() {
    // TODO: 实现创建项目功能
    alert('创建项目功能开发中...');
}

function viewProject(projectId) {
    window.location.href = `/project/?id=${projectId}`;
}

function viewProjectTasks(projectId) {
    window.location.href = `/project-tasks/?id=${projectId}`;
}

function loadListItems() {
    alert('清单管理功能开发中...');
}

function loadResources() {
    alert('资源管理功能开发中...');
}

// 刷新项目列表
function refreshProjects() {
    document.getElementById('projects-list').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-muted mt-3">正在刷新项目数据...</p>
        </div>
    `;
    loadProjects();
    loadStats();
}

// 创建项目功能 - 跳转到专门的创建页面
function createProject() {
    window.location.href = '/create-project/';
}



// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

// 页面加载时自动加载项目列表和统计数据
document.addEventListener('DOMContentLoaded', function() {
    loadProjects();
    loadStats();
});
</script>
{% endif %}
{% endblock %}
