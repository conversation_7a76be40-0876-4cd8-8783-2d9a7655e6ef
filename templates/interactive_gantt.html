{% extends 'base.html' %}

{% block title %}交互式甘特图 - AI-DEEP-CPMS{% endblock %}

{% block extra_css %}
<style>
/* 主要布局样式 */
.main-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
}

/* 甘特图容器 */
#gantt-container {
    min-height: 600px;
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    background: white;
    overflow: hidden;
    margin-bottom: 2rem;
}

/* 控制面板 */
.control-panel {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.button-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.button-group .btn {
    min-width: 120px;
}

/* 任务对话框 */
.task-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.task-dialog.hidden {
    display: none;
}

.dialog-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--dark-color);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
}

/* 统计面板 */
.stats-panel {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .button-group {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dialog-content {
        width: 95%;
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="fw-bold mb-2">
                        <i class="fas fa-chart-gantt me-3"></i>
                        交互式甘特图
                    </h1>
                    <p class="mb-0 opacity-75">拖拽任务条调整时间，添加和删除任务，实时交互管理</p>
                </div>
                <div>
                    <a href="/gantt/" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-1"></i>返回甘特图
                    </a>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <h5 class="fw-bold mb-3">
                <i class="fas fa-cogs me-2 text-primary"></i>
                操作控制
            </h5>
            <div class="button-group">
                <button id="btnAddTask" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>添加任务
                </button>
                <button id="btnRemoveSelected" class="btn btn-danger" disabled>
                    <i class="fas fa-trash me-2"></i>删除选中
                </button>
                <button id="btnExportChart" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>导出图表
                </button>
                <button id="btnResetChart" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-2"></i>重置数据
                </button>
            </div>
        </div>

        <!-- 甘特图容器 -->
        <div id="gantt-container"></div>

        <!-- 统计面板 -->
        <div class="stats-panel">
            <h5 class="fw-bold mb-3">
                <i class="fas fa-chart-bar me-2 text-primary"></i>
                项目统计
            </h5>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalTasks">0</div>
                    <div class="stat-label">总任务数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="completedTasks">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="inProgressTasks">0</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="milestones">0</div>
                    <div class="stat-label">里程碑</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加任务对话框 -->
<div id="addTaskDialog" class="task-dialog hidden">
    <div class="dialog-content">
        <h4 class="fw-bold mb-3">
            <i class="fas fa-plus me-2 text-success"></i>
            添加新任务
        </h4>
        
        <form id="taskForm">
            <div class="form-group">
                <label for="taskName">任务名称 *</label>
                <input type="text" id="taskName" required placeholder="输入任务名称">
            </div>
            
            <div class="form-group">
                <label for="taskDepartment">部门</label>
                <select id="taskDepartment">
                    <option value="0">技术部</option>
                    <option value="1">市场部</option>
                    <option value="2">销售部</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="taskDependency">依赖任务</label>
                <select id="taskDependency">
                    <option value="">无依赖</option>
                    <!-- 动态填充 -->
                </select>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="taskMilestone">
                    <label for="taskMilestone">里程碑任务</label>
                </div>
            </div>
            
            <div class="d-flex gap-2 justify-content-end">
                <button type="button" id="btnCancelTask" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check me-1"></i>添加
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/advanced-gantt.js' %}"></script>
<script>
let interactiveGantt = null;
let isAddingTask = false;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeInteractiveGantt();
    setupEventListeners();
});

// 初始化交互式甘特图
function initializeInteractiveGantt() {
    if (typeof AdvancedGantt === 'undefined') {
        setTimeout(initializeInteractiveGantt, 500);
        return;
    }
    
    const today = new Date();
    const day = 24 * 60 * 60 * 1000;
    
    // 示例数据
    const sampleData = [{
        start: today.getTime() + 2 * day,
        end: today.getTime() + 5 * day,
        name: '原型开发',
        id: 'prototype',
        y: 0,
        owner: '技术团队'
    }, {
        start: today.getTime() + 6 * day,
        name: '原型完成',
        milestone: true,
        dependency: 'prototype',
        id: 'proto_done',
        y: 0,
        owner: '项目经理'
    }, {
        start: today.getTime() + 7 * day,
        end: today.getTime() + 11 * day,
        name: '测试阶段',
        dependency: 'proto_done',
        y: 0,
        owner: '测试团队'
    }, {
        start: today.getTime() + 5 * day,
        end: today.getTime() + 8 * day,
        name: '产品页面',
        y: 1,
        owner: '市场团队'
    }, {
        start: today.getTime() + 9 * day,
        end: today.getTime() + 10 * day,
        name: '新闻通讯',
        y: 1,
        owner: '市场团队'
    }, {
        start: today.getTime() + 9 * day,
        end: today.getTime() + 11 * day,
        name: '许可证办理',
        id: 'licensing',
        y: 2,
        owner: '销售团队'
    }, {
        start: today.getTime() + 11.5 * day,
        end: today.getTime() + 12.5 * day,
        name: '产品发布',
        dependency: 'licensing',
        y: 2,
        owner: '销售团队'
    }];
    
    interactiveGantt = new AdvancedGantt('gantt-container', {
        title: '交互式项目甘特图',
        subtitle: '拖拽任务条调整时间，点击选择任务',
        data: [{
            name: '示例项目',
            data: sampleData
        }],
        enableDragDrop: true,
        enableInteraction: true,
        showCurrentDate: true,
        showWeekends: true,
        height: 600,
        onTaskSelect: updateRemoveButtonStatus,
        onTaskUnselect: updateRemoveButtonStatus,
        onChartReady: function(chart) {
            console.log('交互式甘特图初始化完成');
            updateStatistics();
        }
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 添加任务按钮
    document.getElementById('btnAddTask').addEventListener('click', showAddTaskDialog);

    // 删除选中任务按钮
    document.getElementById('btnRemoveSelected').addEventListener('click', removeSelectedTasks);

    // 导出图表按钮
    document.getElementById('btnExportChart').addEventListener('click', exportChart);

    // 重置图表按钮
    document.getElementById('btnResetChart').addEventListener('click', resetChart);

    // 对话框按钮
    document.getElementById('btnCancelTask').addEventListener('click', hideAddTaskDialog);
    document.getElementById('taskForm').addEventListener('submit', addNewTask);
}

// 更新删除按钮状态
function updateRemoveButtonStatus() {
    const chart = interactiveGantt?.chart;
    if (chart) {
        setTimeout(() => {
            const selectedPoints = chart.getSelectedPoints();
            document.getElementById('btnRemoveSelected').disabled =
                selectedPoints.length === 0 || isAddingTask;
        }, 10);
    }
}

// 显示添加任务对话框
function showAddTaskDialog() {
    // 更新依赖任务列表
    updateDependencyList();

    // 显示对话框
    document.getElementById('addTaskDialog').classList.remove('hidden');
    isAddingTask = true;

    // 聚焦到任务名称输入框
    document.getElementById('taskName').focus();

    // 更新按钮状态
    updateRemoveButtonStatus();
}

// 隐藏添加任务对话框
function hideAddTaskDialog() {
    document.getElementById('addTaskDialog').classList.add('hidden');
    document.getElementById('taskForm').reset();
    isAddingTask = false;
    updateRemoveButtonStatus();
}

// 更新依赖任务列表
function updateDependencyList() {
    const select = document.getElementById('taskDependency');
    let html = '<option value="">无依赖</option>';

    if (interactiveGantt?.chart?.series[0]?.points) {
        interactiveGantt.chart.series[0].points.forEach(point => {
            html += `<option value="${point.id}">${point.name}</option>`;
        });
    }

    select.innerHTML = html;
}

// 添加新任务
function addNewTask(e) {
    e.preventDefault();

    const chart = interactiveGantt?.chart;
    if (!chart) return;

    const series = chart.series[0];
    const name = document.getElementById('taskName').value;
    const department = parseInt(document.getElementById('taskDepartment').value);
    const dependencyId = document.getElementById('taskDependency').value;
    const isMilestone = document.getElementById('taskMilestone').checked;

    const dependency = dependencyId ? chart.get(dependencyId) : null;
    const day = 24 * 60 * 60 * 1000;
    const today = new Date().getTime();

    // 计算该部门最晚的结束时间
    let maxEnd = series.points.reduce((acc, point) => {
        return point.y === department && point.end ? Math.max(acc, point.end) : acc;
    }, today);

    if (maxEnd === today) {
        maxEnd = today;
    }

    // 添加任务
    const newTask = {
        start: maxEnd + (isMilestone ? day : 0),
        end: isMilestone ? undefined : maxEnd + day,
        y: department,
        name: name,
        dependency: dependency ? dependency.id : undefined,
        milestone: isMilestone || undefined,
        id: 'task_' + Date.now(),
        owner: ['技术团队', '市场团队', '销售团队'][department]
    };

    series.addPoint(newTask);

    // 隐藏对话框
    hideAddTaskDialog();

    // 更新统计
    updateStatistics();

    showNotification('任务添加成功', 'success');
}

// 删除选中任务
function removeSelectedTasks() {
    const chart = interactiveGantt?.chart;
    if (chart) {
        const selectedPoints = chart.getSelectedPoints();
        selectedPoints.forEach(point => point.remove());
        updateStatistics();
        showNotification(`已删除 ${selectedPoints.length} 个任务`, 'success');
    }
}

// 导出图表
function exportChart() {
    if (interactiveGantt) {
        interactiveGantt.exportChart({
            type: 'image/png',
            filename: 'interactive-gantt-chart'
        });
        showNotification('图表导出成功', 'success');
    }
}

// 重置图表
function resetChart() {
    if (confirm('确定要重置图表数据吗？此操作不可撤销。')) {
        if (interactiveGantt) {
            interactiveGantt.destroy();
            initializeInteractiveGantt();
            showNotification('图表已重置', 'info');
        }
    }
}

// 更新统计信息
function updateStatistics() {
    const chart = interactiveGantt?.chart;
    if (!chart || !chart.series[0]) return;

    const points = chart.series[0].points;
    const totalTasks = points.length;
    const milestones = points.filter(p => p.milestone).length;
    const completedTasks = points.filter(p => p.completed && p.completed.amount >= 1).length;
    const inProgressTasks = points.filter(p => p.completed && p.completed.amount > 0 && p.completed.amount < 1).length;

    document.getElementById('totalTasks').textContent = totalTasks;
    document.getElementById('completedTasks').textContent = completedTasks;
    document.getElementById('inProgressTasks').textContent = inProgressTasks;
    document.getElementById('milestones').textContent = milestones;
}

// 通知系统
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
