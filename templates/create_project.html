{% extends 'base.html' %}

{% block title %}新建项目 - AI-DEEP-CPMS{% endblock %}

{% block extra_css %}
<style>
.create-project-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem 1rem 0 0;
    margin: -1px -1px 0 -1px;
}

.method-card {
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
}

.method-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.method-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
}

.method-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1rem;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.step {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: var(--border-color);
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 0.5rem;
    position: relative;
}

.step.active {
    background: var(--primary-color);
    color: white;
}

.step.completed {
    background: var(--success-color);
    color: white;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 100%;
    width: 2rem;
    height: 2px;
    background: var(--border-color);
    transform: translateY(-50%);
}

.step.completed:not(:last-child)::after {
    background: var(--success-color);
}

.form-section {
    display: none;
}

.form-section.active {
    display: block;
}

.file-drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: 1rem;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.supported-formats {
    background: var(--light-color);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
}

.format-badge {
    display: inline-block;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    margin: 0.25rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.progress-bar {
    height: 0.5rem;
    background: var(--light-color);
    border-radius: 0.25rem;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    width: 0%;
    transition: width 0.3s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="create-project-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="fw-bold mb-1">
                            <i class="fas fa-plus-circle me-2"></i>
                            新建项目
                        </h2>
                        <p class="mb-0 opacity-75">创建新项目或从现有文件导入</p>
                    </div>
                    <div>
                        <a href="/" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i>返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step active" id="step-1">1</div>
            <div class="step" id="step-2">2</div>
            <div class="step" id="step-3">3</div>
        </div>

        <!-- 创建方式选择 -->
        <div class="form-section active" id="section-1">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-route me-2 text-primary"></i>
                        选择创建方式
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-4">
                            <div class="method-card" data-method="blank" onclick="selectMethod('blank')">
                                <div class="method-icon">
                                    <i class="fas fa-file-plus"></i>
                                </div>
                                <h5 class="fw-bold mb-2">空白项目</h5>
                                <p class="text-muted mb-3">从零开始创建新项目</p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>自定义项目设置</li>
                                    <li><i class="fas fa-check text-success me-2"></i>灵活的任务结构</li>
                                    <li><i class="fas fa-check text-success me-2"></i>完全控制项目内容</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="method-card" data-method="template" onclick="selectMethod('template')">
                                <div class="method-icon">
                                    <i class="fas fa-copy"></i>
                                </div>
                                <h5 class="fw-bold mb-2">项目模板</h5>
                                <p class="text-muted mb-3">使用预定义的项目模板</p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>快速项目启动</li>
                                    <li><i class="fas fa-check text-success me-2"></i>行业最佳实践</li>
                                    <li><i class="fas fa-check text-success me-2"></i>标准化流程</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="method-card" data-method="import" onclick="selectMethod('import')">
                                <div class="method-icon">
                                    <i class="fas fa-file-import"></i>
                                </div>
                                <h5 class="fw-bold mb-2">导入文件</h5>
                                <p class="text-muted mb-3">从现有项目文件导入</p>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>支持多种格式</li>
                                    <li><i class="fas fa-check text-success me-2"></i>保留原有数据</li>
                                    <li><i class="fas fa-check text-success me-2"></i>快速迁移项目</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button class="btn btn-primary btn-lg px-4" onclick="nextStep()" disabled id="next-btn-1">
                            <i class="fas fa-arrow-right me-2"></i>
                            下一步
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目配置 -->
        <div class="form-section" id="section-2">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-cog me-2 text-primary"></i>
                        项目配置
                    </h5>
                </div>
                <div class="card-body p-4">
                    <!-- 空白项目表单 -->
                    <div id="blank-form" style="display: none;">
                        <form id="project-form">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="project-name" class="form-label fw-semibold">
                                            <i class="fas fa-tag me-1 text-primary"></i>
                                            项目名称 *
                                        </label>
                                        <input type="text" class="form-control" id="project-name" required placeholder="输入项目名称">
                                    </div>
                                    <div class="mb-3">
                                        <label for="project-location" class="form-label fw-semibold">
                                            <i class="fas fa-map-marker-alt me-1 text-primary"></i>
                                            项目地点
                                        </label>
                                        <input type="text" class="form-control" id="project-location" placeholder="输入项目地点">
                                    </div>
                                    <div class="mb-3">
                                        <label for="project-start-date" class="form-label fw-semibold">
                                            <i class="fas fa-calendar-alt me-1 text-primary"></i>
                                            开始日期 *
                                        </label>
                                        <input type="date" class="form-control" id="project-start-date" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="project-description" class="form-label fw-semibold">
                                            <i class="fas fa-align-left me-1 text-primary"></i>
                                            项目描述
                                        </label>
                                        <textarea class="form-control" id="project-description" rows="3" placeholder="描述项目的目标和范围"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="work-hours" class="form-label fw-semibold">
                                            <i class="fas fa-clock me-1 text-primary"></i>
                                            每日工作小时数
                                        </label>
                                        <input type="number" class="form-control" id="work-hours" value="8" min="1" max="24">
                                    </div>
                                </div>
                            </div>

                            <!-- 工作日历设置 -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <h6 class="fw-bold mb-3">
                                        <i class="fas fa-calendar-week me-2 text-primary"></i>
                                        工作日历设置
                                    </h6>
                                    <div class="row g-2">
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="monday" checked>
                                                <label class="form-check-label" for="monday">周一</label>
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="tuesday" checked>
                                                <label class="form-check-label" for="tuesday">周二</label>
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="wednesday" checked>
                                                <label class="form-check-label" for="wednesday">周三</label>
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="thursday" checked>
                                                <label class="form-check-label" for="thursday">周四</label>
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="friday" checked>
                                                <label class="form-check-label" for="friday">周五</label>
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="saturday">
                                                <label class="form-check-label" for="saturday">周六</label>
                                            </div>
                                        </div>
                                        <div class="col-md-1 col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="sunday">
                                                <label class="form-check-label" for="sunday">周日</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 模板选择表单 -->
                    <div id="template-form" style="display: none;">
                        <div class="row g-4">
                            <div class="col-md-4">
                                <div class="card template-card" data-template="construction" onclick="selectTemplate('construction')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-hard-hat fa-3x text-warning mb-3"></i>
                                        <h6 class="fw-bold">建筑工程</h6>
                                        <p class="text-muted small">适用于建筑施工项目</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card template-card" data-template="software" onclick="selectTemplate('software')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-code fa-3x text-info mb-3"></i>
                                        <h6 class="fw-bold">软件开发</h6>
                                        <p class="text-muted small">适用于软件开发项目</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card template-card" data-template="marketing" onclick="selectTemplate('marketing')">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bullhorn fa-3x text-success mb-3"></i>
                                        <h6 class="fw-bold">市场营销</h6>
                                        <p class="text-muted small">适用于营销推广项目</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件导入表单 -->
                    <div id="import-form" style="display: none;">
                        <div class="file-drop-zone" id="file-drop-zone" onclick="document.getElementById('file-input').click()">
                            <div class="mb-3">
                                <i class="fas fa-cloud-upload-alt fa-4x text-primary mb-3"></i>
                                <h5 class="fw-bold">拖拽文件到此处或点击选择</h5>
                                <p class="text-muted">支持多种项目管理文件格式</p>
                            </div>
                            <input type="file" id="file-input" style="display: none;" accept=".mpp,.xml,.xlsx,.xls,.csv,.json,.gan,.planner">
                        </div>

                        <div class="supported-formats">
                            <h6 class="fw-bold mb-2">
                                <i class="fas fa-info-circle me-1 text-primary"></i>
                                支持的文件格式
                            </h6>
                            <div class="mb-2">
                                <strong>读取支持：</strong>
                                <span class="format-badge">MPP</span>
                                <span class="format-badge">XML</span>
                                <span class="format-badge">XLSX</span>
                                <span class="format-badge">XLS</span>
                                <span class="format-badge">CSV</span>
                                <span class="format-badge">JSON</span>
                                <span class="format-badge">GAN</span>
                                <span class="format-badge">PLANNER</span>
                            </div>
                            <div>
                                <strong>兼容软件：</strong>
                                Microsoft Project, GanttProject, Planner, Excel
                            </div>
                        </div>

                        <div id="file-info" style="display: none;" class="mt-3">
                            <div class="alert alert-info">
                                <h6 class="fw-bold mb-2">
                                    <i class="fas fa-file me-1"></i>
                                    已选择文件
                                </h6>
                                <p class="mb-1"><strong>文件名：</strong><span id="file-name"></span></p>
                                <p class="mb-1"><strong>文件大小：</strong><span id="file-size"></span></p>
                                <p class="mb-0"><strong>文件格式：</strong><span id="file-format"></span></p>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <button class="btn btn-outline-secondary btn-lg px-4" onclick="prevStep()">
                            <i class="fas fa-arrow-left me-2"></i>
                            上一步
                        </button>
                        <button class="btn btn-primary btn-lg px-4" onclick="nextStep()" id="next-btn-2">
                            <i class="fas fa-arrow-right me-2"></i>
                            下一步
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 确认创建 -->
        <div class="form-section" id="section-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-check-circle me-2 text-primary"></i>
                        确认创建
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div id="project-summary">
                        <!-- 项目摘要将在这里显示 -->
                    </div>

                    <div class="progress-bar" id="progress-container" style="display: none;">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>

                    <div id="creation-status" style="display: none;">
                        <!-- 创建状态将在这里显示 -->
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <button class="btn btn-outline-secondary btn-lg px-4" onclick="prevStep()" id="prev-btn-3">
                            <i class="fas fa-arrow-left me-2"></i>
                            上一步
                        </button>
                        <button class="btn btn-success btn-lg px-4" onclick="createProject()" id="create-btn">
                            <i class="fas fa-rocket me-2"></i>
                            创建项目
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentStep = 1;
let selectedMethod = null;
let selectedTemplate = null;
let selectedFile = null;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认开始日期为今天
    document.getElementById('project-start-date').value = new Date().toISOString().split('T')[0];

    // 文件拖拽功能
    setupFileDrop();
});

// 选择创建方式
function selectMethod(method) {
    selectedMethod = method;

    // 更新UI
    document.querySelectorAll('.method-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-method="${method}"]`).classList.add('selected');

    // 启用下一步按钮
    document.getElementById('next-btn-1').disabled = false;
}

// 选择模板
function selectTemplate(template) {
    selectedTemplate = template;

    // 更新UI
    document.querySelectorAll('.template-card').forEach(card => {
        card.classList.remove('border-primary');
    });
    document.querySelector(`[data-template="${template}"]`).classList.add('border-primary');
}

// 下一步
function nextStep() {
    if (currentStep < 3) {
        // 隐藏当前步骤
        document.getElementById(`section-${currentStep}`).classList.remove('active');
        document.getElementById(`step-${currentStep}`).classList.remove('active');
        document.getElementById(`step-${currentStep}`).classList.add('completed');

        currentStep++;

        // 显示下一步骤
        document.getElementById(`section-${currentStep}`).classList.add('active');
        document.getElementById(`step-${currentStep}`).classList.add('active');

        // 根据选择的方法显示相应表单
        if (currentStep === 2) {
            showMethodForm();
        } else if (currentStep === 3) {
            showProjectSummary();
        }
    }
}

// 上一步
function prevStep() {
    if (currentStep > 1) {
        // 隐藏当前步骤
        document.getElementById(`section-${currentStep}`).classList.remove('active');
        document.getElementById(`step-${currentStep}`).classList.remove('active');

        currentStep--;

        // 显示上一步骤
        document.getElementById(`section-${currentStep}`).classList.add('active');
        document.getElementById(`step-${currentStep}`).classList.add('active');
        document.getElementById(`step-${currentStep}`).classList.remove('completed');
    }
}

// 显示方法表单
function showMethodForm() {
    // 隐藏所有表单
    document.getElementById('blank-form').style.display = 'none';
    document.getElementById('template-form').style.display = 'none';
    document.getElementById('import-form').style.display = 'none';

    // 显示选择的表单
    if (selectedMethod === 'blank') {
        document.getElementById('blank-form').style.display = 'block';
    } else if (selectedMethod === 'template') {
        document.getElementById('template-form').style.display = 'block';
    } else if (selectedMethod === 'import') {
        document.getElementById('import-form').style.display = 'block';
    }
}

// 显示项目摘要
function showProjectSummary() {
    let summaryHtml = '';

    if (selectedMethod === 'blank') {
        const name = document.getElementById('project-name').value;
        const description = document.getElementById('project-description').value;
        const location = document.getElementById('project-location').value;
        const startDate = document.getElementById('project-start-date').value;
        const workHours = document.getElementById('work-hours').value;

        summaryHtml = `
            <div class="alert alert-info border-0">
                <h6 class="fw-bold mb-3">
                    <i class="fas fa-file-plus me-2"></i>
                    空白项目摘要
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>项目名称：</strong>${name}</p>
                        <p><strong>项目地点：</strong>${location || '未设置'}</p>
                        <p><strong>开始日期：</strong>${startDate}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>工作时间：</strong>每日${workHours}小时</p>
                        <p><strong>项目描述：</strong>${description || '无'}</p>
                    </div>
                </div>
            </div>
        `;
    } else if (selectedMethod === 'template') {
        summaryHtml = `
            <div class="alert alert-info border-0">
                <h6 class="fw-bold mb-3">
                    <i class="fas fa-copy me-2"></i>
                    模板项目摘要
                </h6>
                <p><strong>选择的模板：</strong>${selectedTemplate || '未选择'}</p>
                <p>将基于选择的模板创建项目，包含预定义的任务结构和最佳实践。</p>
            </div>
        `;
    } else if (selectedMethod === 'import') {
        summaryHtml = `
            <div class="alert alert-info border-0">
                <h6 class="fw-bold mb-3">
                    <i class="fas fa-file-import me-2"></i>
                    导入项目摘要
                </h6>
                ${selectedFile ? `
                    <p><strong>文件名：</strong>${selectedFile.name}</p>
                    <p><strong>文件大小：</strong>${formatFileSize(selectedFile.size)}</p>
                    <p><strong>文件格式：</strong>${getFileExtension(selectedFile.name).toUpperCase()}</p>
                ` : '<p class="text-warning">请选择要导入的文件</p>'}
            </div>
        `;
    }

    document.getElementById('project-summary').innerHTML = summaryHtml;
}

// 创建项目
function createProject() {
    // 显示进度条
    document.getElementById('progress-container').style.display = 'block';
    document.getElementById('create-btn').disabled = true;
    document.getElementById('prev-btn-3').disabled = true;

    let progress = 0;
    const progressFill = document.getElementById('progress-fill');
    const statusDiv = document.getElementById('creation-status');

    // 模拟进度更新
    const progressInterval = setInterval(() => {
        progress += 10;
        progressFill.style.width = progress + '%';

        if (progress >= 100) {
            clearInterval(progressInterval);

            // 根据选择的方法创建项目
            if (selectedMethod === 'blank') {
                createBlankProject();
            } else if (selectedMethod === 'template') {
                createTemplateProject();
            } else if (selectedMethod === 'import') {
                importProject();
            }
        }
    }, 200);
}

// 创建空白项目
function createBlankProject() {
    const formData = {
        name: document.getElementById('project-name').value,
        description: document.getElementById('project-description').value,
        location: document.getElementById('project-location').value,
        start_date: document.getElementById('project-start-date').value,
        work_hours_per_day: parseInt(document.getElementById('work-hours').value),
        monday: document.getElementById('monday').checked,
        tuesday: document.getElementById('tuesday').checked,
        wednesday: document.getElementById('wednesday').checked,
        thursday: document.getElementById('thursday').checked,
        friday: document.getElementById('friday').checked,
        saturday: document.getElementById('saturday').checked,
        sunday: document.getElementById('sunday').checked,
        status: 'planning'
    };

    fetch('/api/projects/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            showSuccess(`项目 "${data.name}" 创建成功！`, data.id);
        } else {
            showError('项目创建失败: ' + JSON.stringify(data));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('项目创建失败，请稍后重试');
    });
}

// 创建模板项目
function createTemplateProject() {
    // TODO: 实现模板项目创建
    showError('模板项目功能开发中...');
}

// 导入项目
function importProject() {
    if (!selectedFile) {
        showError('请选择要导入的文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);

    fetch('/api/projects/import_project/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.project_id) {
            showSuccess(`项目 "${data.project_name}" 导入成功！导入了 ${data.tasks_count} 个任务`, data.project_id);
        } else {
            showError('项目导入失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('项目导入失败，请稍后重试');
    });
}

// 显示成功信息
function showSuccess(message, projectId) {
    const statusDiv = document.getElementById('creation-status');
    statusDiv.style.display = 'block';
    statusDiv.innerHTML = `
        <div class="alert alert-success border-0">
            <h6 class="fw-bold mb-2">
                <i class="fas fa-check-circle me-2"></i>
                创建成功！
            </h6>
            <p class="mb-3">${message}</p>
            <div class="d-flex gap-2">
                <a href="/project/?id=${projectId}" class="btn btn-primary">
                    <i class="fas fa-eye me-1"></i>查看项目
                </a>
                <a href="/gantt/" class="btn btn-outline-primary">
                    <i class="fas fa-chart-gantt me-1"></i>甘特图
                </a>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
            </div>
        </div>
    `;
}

// 显示错误信息
function showError(message) {
    const statusDiv = document.getElementById('creation-status');
    statusDiv.style.display = 'block';
    statusDiv.innerHTML = `
        <div class="alert alert-danger border-0">
            <h6 class="fw-bold mb-2">
                <i class="fas fa-exclamation-triangle me-2"></i>
                创建失败
            </h6>
            <p class="mb-3">${message}</p>
            <button class="btn btn-outline-danger" onclick="resetCreation()">
                <i class="fas fa-redo me-1"></i>重新尝试
            </button>
        </div>
    `;
}

// 重置创建状态
function resetCreation() {
    document.getElementById('progress-container').style.display = 'none';
    document.getElementById('creation-status').style.display = 'none';
    document.getElementById('create-btn').disabled = false;
    document.getElementById('prev-btn-3').disabled = false;
    document.getElementById('progress-fill').style.width = '0%';
}

// 设置文件拖拽功能
function setupFileDrop() {
    const dropZone = document.getElementById('file-drop-zone');
    const fileInput = document.getElementById('file-input');

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFile(e.target.files[0]);
        }
    });

    // 拖拽事件
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('dragover');

        if (e.dataTransfer.files.length > 0) {
            handleFile(e.dataTransfer.files[0]);
        }
    });
}

// 处理文件
function handleFile(file) {
    selectedFile = file;

    // 显示文件信息
    document.getElementById('file-name').textContent = file.name;
    document.getElementById('file-size').textContent = formatFileSize(file.size);
    document.getElementById('file-format').textContent = getFileExtension(file.name).toUpperCase();
    document.getElementById('file-info').style.display = 'block';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取文件扩展名
function getFileExtension(filename) {
    return filename.split('.').pop().toLowerCase();
}

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}