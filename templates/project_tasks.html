{% extends 'base.html' %}

{% block title %}项目任务管理 - AI-DEEP-CPMS{% endblock %}

{% block extra_css %}
<style>
/* 主要布局样式 */
.main-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 1rem 0;
}

.project-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
}

.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.breadcrumb-nav a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-nav a:hover {
    color: white;
}

/* 任务卡片样式 */
.task-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #10b981, #f59e0b, #ef4444);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.task-card:hover::before {
    transform: scaleX(1);
}

.task-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.task-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.task-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.task-progress {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 1rem;
}

.task-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #10b981);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 甘特图样式 */
.gantt-container {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.gantt-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 1rem 1rem 0 0;
    margin: -2rem -2rem 2rem -2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 0.875rem;
}

/* 工具栏样式 */
.toolbar {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.toolbar-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .task-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .task-header {
        flex-direction: column;
        gap: 1rem;
    }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    text-align: center;
}

.feature-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin: 0 auto 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <div class="container">
        <!-- 项目头部 -->
        <div class="project-header">
            <div class="breadcrumb-nav">
                <a href="/" class="me-2">
                    <i class="fas fa-home me-1"></i>首页
                </a>
                <span class="me-2">/</span>
                <a href="/projects/" class="me-2">项目管理</a>
                <span class="me-2">/</span>
                <span id="project-name-breadcrumb">项目详情</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="fw-bold mb-2" id="project-title">
                        <i class="fas fa-project-diagram me-3"></i>
                        项目名称
                    </h1>
                    <p class="mb-0 opacity-75" id="project-description">项目描述信息</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-light" onclick="showProjectSettings()">
                        <i class="fas fa-cog me-1"></i>项目设置
                    </button>
                    <button class="btn btn-light" onclick="exportProject()">
                        <i class="fas fa-download me-1"></i>导出项目
                    </button>
                </div>
            </div>
        </div>

        <!-- 项目统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="total-tasks">0</div>
                <div class="stat-label">
                    <i class="fas fa-tasks me-1"></i>
                    总任务数
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completed-tasks">0</div>
                <div class="stat-label">
                    <i class="fas fa-check-circle me-1"></i>
                    已完成
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="progress-percent">0%</div>
                <div class="stat-label">
                    <i class="fas fa-chart-line me-1"></i>
                    完成进度
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="days-remaining">0</div>
                <div class="stat-label">
                    <i class="fas fa-calendar-alt me-1"></i>
                    剩余天数
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fas fa-plus me-1 text-primary"></i>
                            创建任务
                        </label>
                        <button class="btn btn-success w-100" onclick="showAddTaskForm()">
                            <i class="fas fa-plus me-1"></i>新建任务
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fas fa-chart-gantt me-1 text-primary"></i>
                            甘特图
                        </label>
                        <button class="btn btn-primary w-100" onclick="showGanttView()">
                            <i class="fas fa-chart-gantt me-1"></i>甘特图视图
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fas fa-filter me-1 text-primary"></i>
                            筛选
                        </label>
                        <select class="form-select" id="task-filter" onchange="filterTasks()">
                            <option value="all">全部任务</option>
                            <option value="not_started">未开始</option>
                            <option value="in_progress">进行中</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-2">
                            <i class="fas fa-search me-1 text-primary"></i>
                            搜索
                        </label>
                        <input type="text" class="form-control" placeholder="搜索任务..." id="task-search" oninput="searchTasks()">
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div id="tasks-container">
            <!-- 任务将在这里动态加载 -->
            <div class="empty-state">
                <i class="fas fa-tasks"></i>
                <h4 class="mt-3 mb-2">项目任务管理</h4>
                <p class="mb-4">开始创建和管理您的项目任务</p>
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <h6 class="fw-bold">创建任务</h6>
                        <small class="text-muted">添加新的项目任务</small>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-gantt"></i>
                        </div>
                        <h6 class="fw-bold">甘特图</h6>
                        <small class="text-muted">可视化项目进度</small>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <h6 class="fw-bold">依赖关系</h6>
                        <small class="text-muted">管理任务依赖</small>
                    </div>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary btn-lg" onclick="showAddTaskForm()">
                        <i class="fas fa-plus me-2"></i>创建第一个任务
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentProjectId = null;
let allTasks = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从URL获取项目ID
    const urlParams = new URLSearchParams(window.location.search);
    currentProjectId = urlParams.get('id') || urlParams.get('project_id');
    
    if (currentProjectId) {
        loadProjectData();
    } else {
        showNotification('项目ID不存在', 'error');
        window.location.href = '/';
    }
});

// 加载项目数据
function loadProjectData() {
    Promise.all([
        loadProjectInfo(),
        loadProjectTasks(),
        loadProjectStats()
    ]).then(() => {
        console.log('项目数据加载完成');
    }).catch(error => {
        console.error('加载项目数据失败:', error);
        showNotification('加载项目数据失败', 'error');
    });
}

// 加载项目信息
function loadProjectInfo() {
    return fetch(`/api/projects/${currentProjectId}/`)
        .then(response => response.json())
        .then(project => {
            document.getElementById('project-title').innerHTML = `
                <i class="fas fa-project-diagram me-3"></i>${project.name}
            `;
            document.getElementById('project-description').textContent = project.description || '暂无描述';
            document.getElementById('project-name-breadcrumb').textContent = project.name;
        });
}

// 加载项目任务
function loadProjectTasks() {
    return fetch(`/api/tasks/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            allTasks = data.results || [];
            renderTasks(allTasks);
        });
}

// 加载项目统计
function loadProjectStats() {
    return fetch(`/api/tasks/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            const tasks = data.results || [];
            updateStats(tasks);
        });
}

// 更新统计信息
function updateStats(tasks) {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const progressPercent = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // 计算剩余天数
    const now = new Date();
    const futureTasks = tasks.filter(task => new Date(task.end_date) > now);
    const daysRemaining = futureTasks.length > 0 ?
        Math.max(...futureTasks.map(task => Math.ceil((new Date(task.end_date) - now) / (1000 * 60 * 60 * 24)))) : 0;

    document.getElementById('total-tasks').textContent = totalTasks;
    document.getElementById('completed-tasks').textContent = completedTasks;
    document.getElementById('progress-percent').textContent = progressPercent + '%';
    document.getElementById('days-remaining').textContent = daysRemaining;
}

// 渲染任务列表
function renderTasks(tasks) {
    const container = document.getElementById('tasks-container');

    if (tasks.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-tasks"></i>
                <h4 class="mt-3 mb-2">暂无任务</h4>
                <p class="mb-4">开始创建您的第一个项目任务</p>
                <button class="btn btn-primary btn-lg" onclick="showAddTaskForm()">
                    <i class="fas fa-plus me-2"></i>创建第一个任务
                </button>
            </div>
        `;
        return;
    }

    let html = '';
    tasks.forEach(task => {
        const progressPercent = Math.round(task.progress_percentage || 0);
        const statusColor = getStatusColor(task.status);
        const priorityIcon = getPriorityIcon(task.priority);

        html += `
            <div class="task-card" onclick="showTaskDetails(${task.id})">
                <div class="task-header">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-light text-dark me-2">${task.wbs_code}</span>
                            ${priorityIcon}
                            <h5 class="mb-0 fw-bold">${task.name}</h5>
                        </div>
                        <p class="text-muted mb-0">${task.description || '暂无描述'}</p>
                    </div>
                    <div class="text-end ms-3">
                        <span class="badge bg-${statusColor} mb-2">${getStatusText(task.status)}</span>
                        <div class="h4 mb-0 fw-bold text-${statusColor}">${progressPercent}%</div>
                    </div>
                </div>
                <div class="task-meta">
                    <span><i class="fas fa-calendar me-1"></i>${formatDate(task.start_date)}</span>
                    <span><i class="fas fa-clock me-1"></i>${task.planned_duration || 0}天</span>
                    <span><i class="fas fa-calculator me-1"></i>${task.quantity || 0} ${task.unit || ''}</span>
                </div>
                <div class="task-progress">
                    <div class="task-progress-bar" style="width: ${progressPercent}%"></div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 功能函数
function showAddTaskForm() {
    window.location.href = `/gantt/?project_id=${currentProjectId}`;
}

function showGanttView() {
    window.location.href = `/gantt/?project_id=${currentProjectId}`;
}

function showTaskDetails(taskId) {
    window.location.href = `/gantt/?project_id=${currentProjectId}&task_id=${taskId}`;
}

function showProjectSettings() {
    showNotification('项目设置功能开发中...', 'info');
}

function exportProject() {
    showNotification('项目导出功能开发中...', 'info');
}

function filterTasks() {
    const filterValue = document.getElementById('task-filter').value;
    let filteredTasks = allTasks;

    if (filterValue !== 'all') {
        filteredTasks = allTasks.filter(task => task.status === filterValue);
    }

    renderTasks(filteredTasks);
}

function searchTasks() {
    const searchTerm = document.getElementById('task-search').value.toLowerCase();
    const filteredTasks = allTasks.filter(task =>
        task.name.toLowerCase().includes(searchTerm) ||
        task.wbs_code.toLowerCase().includes(searchTerm) ||
        (task.description && task.description.toLowerCase().includes(searchTerm))
    );

    renderTasks(filteredTasks);
}

// 辅助函数
function getStatusColor(status) {
    const colors = {
        'not_started': 'secondary',
        'in_progress': 'primary',
        'completed': 'success',
        'paused': 'warning',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'not_started': '未开始',
        'in_progress': '进行中',
        'completed': '已完成',
        'paused': '暂停',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

function getPriorityIcon(priority) {
    const icons = {
        'low': '<i class="fas fa-flag text-secondary me-2" title="低优先级"></i>',
        'normal': '<i class="fas fa-flag text-primary me-2" title="普通优先级"></i>',
        'high': '<i class="fas fa-flag text-warning me-2" title="高优先级"></i>',
        'urgent': '<i class="fas fa-flag text-danger me-2" title="紧急"></i>'
    };
    return icons[priority] || icons['normal'];
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('zh-CN');
}

// 通知系统
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}
</script>
{% endblock %}
