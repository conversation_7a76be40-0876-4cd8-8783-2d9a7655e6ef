from django.db import models
from projects.models import Project


class ProjectQuota(models.Model):
    """项目定额模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='quotas', verbose_name='项目')
    quota_number = models.CharField(max_length=50, verbose_name='定额编号')
    name = models.CharField(max_length=200, verbose_name='定额项名称')
    work_content = models.TextField(verbose_name='工作内容')
    unit = models.CharField(max_length=50, verbose_name='单位')
    unit_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='单价',
        default=0
    )

    # 定额分类
    CATEGORY_CHOICES = [
        ('labor', '人工'),
        ('material', '材料'),
        ('machinery', '机械'),
        ('comprehensive', '综合'),
    ]
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='comprehensive', verbose_name='定额分类')

    # 备注
    remarks = models.TextField(verbose_name='备注', blank=True)

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '项目定额'
        verbose_name_plural = '项目定额'
        unique_together = ['project', 'quota_number']
        ordering = ['quota_number']

    def __str__(self):
        return f"{self.quota_number} - {self.name}"

    @property
    def total_resource_cost(self):
        """计算资源总成本"""
        return sum([res.total_price for res in self.resources.all()])

    def update_unit_price_from_resources(self):
        """根据资源成本更新单价"""
        self.unit_price = self.total_resource_cost
        self.save()


class QuotaResourceConsumption(models.Model):
    """定额资源消耗明细模型"""
    quota = models.ForeignKey(
        ProjectQuota,
        on_delete=models.CASCADE,
        related_name='resource_consumptions',
        verbose_name='定额项'
    )

    # 资源信息
    resource_number = models.CharField(max_length=50, verbose_name='资源编号')
    resource_name = models.CharField(max_length=200, verbose_name='资源名称')

    # 资源分类
    RESOURCE_CATEGORY_CHOICES = [
        ('labor', '人工'),
        ('material', '材料'),
        ('machinery', '机械'),
        ('indirect', '间接费用'),
    ]
    category = models.CharField(max_length=20, choices=RESOURCE_CATEGORY_CHOICES, verbose_name='资源类别')

    unit = models.CharField(max_length=50, verbose_name='单位')
    consumption = models.DecimalField(
        max_digits=10, decimal_places=4,
        verbose_name='消耗数量'
    )
    unit_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='单价',
        default=0
    )
    total_price = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='合价',
        blank=True, null=True
    )

    # 备注
    remarks = models.TextField(verbose_name='备注', blank=True)

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '定额资源消耗明细'
        verbose_name_plural = '定额资源消耗明细'
        unique_together = ['quota', 'resource_number']
        ordering = ['resource_number']

    def __str__(self):
        return f"{self.quota.quota_number} - {self.resource_name}"

    def save(self, *args, **kwargs):
        # 自动计算合价
        self.total_price = self.consumption * self.unit_price
        super().save(*args, **kwargs)

        # 更新定额单价
        self.quota.update_unit_price_from_resources()
