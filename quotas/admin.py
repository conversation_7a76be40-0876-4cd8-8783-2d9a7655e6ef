from django.contrib import admin
from .models import ProjectQuota, QuotaResourceConsumption


class QuotaResourceConsumptionInline(admin.TabularInline):
    model = QuotaResourceConsumption
    extra = 1
    readonly_fields = ['total_price']


@admin.register(ProjectQuota)
class ProjectQuotaAdmin(admin.ModelAdmin):
    list_display = ['quota_number', 'name', 'project', 'category', 'unit', 'unit_price']
    list_filter = ['project', 'category']
    search_fields = ['quota_number', 'name', 'work_content']
    inlines = [QuotaResourceConsumptionInline]


@admin.register(QuotaResourceConsumption)
class QuotaResourceConsumptionAdmin(admin.ModelAdmin):
    list_display = ['resource_number', 'resource_name', 'quota', 'category', 'consumption', 'unit_price', 'total_price']
    list_filter = ['quota__project', 'category']
    search_fields = ['resource_number', 'resource_name']
    readonly_fields = ['total_price']
