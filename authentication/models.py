from django.db import models
from django.contrib.auth.models import User
from datetime import datetime, timedelta


class UserProfile(models.Model):
    """用户配置文件模型"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name='用户')

    # 试用期相关
    trial_start_date = models.DateTimeField(auto_now_add=True, verbose_name='试用开始时间')
    trial_end_date = models.DateTimeField(verbose_name='试用结束时间', null=True, blank=True)
    is_trial_active = models.BooleanField(default=True, verbose_name='试用是否激活')

    # 订阅相关
    SUBSCRIPTION_STATUS_CHOICES = [
        ('trial', '试用期'),
        ('active', '已订阅'),
        ('expired', '已过期'),
        ('cancelled', '已取消'),
    ]
    subscription_status = models.CharField(
        max_length=20,
        choices=SUBSCRIPTION_STATUS_CHOICES,
        default='trial',
        verbose_name='订阅状态'
    )
    subscription_start_date = models.DateTimeField(null=True, blank=True, verbose_name='订阅开始时间')
    subscription_end_date = models.DateTimeField(null=True, blank=True, verbose_name='订阅结束时间')

    # 用户信息
    company = models.CharField(max_length=200, verbose_name='公司名称', blank=True)
    phone = models.CharField(max_length=20, verbose_name='电话号码', blank=True)
    avatar = models.ImageField(upload_to='avatars/', verbose_name='头像', null=True, blank=True)

    # 偏好设置
    language = models.CharField(max_length=10, default='zh-hans', verbose_name='语言偏好')
    timezone = models.CharField(max_length=50, default='Asia/Shanghai', verbose_name='时区')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户配置'
        verbose_name_plural = '用户配置'

    def __str__(self):
        return f"{self.user.username} - {self.subscription_status}"

    def save(self, *args, **kwargs):
        # 设置试用结束时间（30天）
        if not self.trial_end_date and self.trial_start_date:
            self.trial_end_date = self.trial_start_date + timedelta(days=30)
        super().save(*args, **kwargs)

    @property
    def is_trial_expired(self):
        """检查试用期是否过期"""
        if not self.trial_end_date:
            return False
        from django.utils import timezone
        return timezone.now() > self.trial_end_date

    @property
    def is_subscription_active(self):
        """检查订阅是否激活"""
        if self.subscription_status == 'trial':
            return not self.is_trial_expired
        elif self.subscription_status == 'active':
            if self.subscription_end_date:
                from django.utils import timezone
                return timezone.now() <= self.subscription_end_date
            return True
        return False

    @property
    def days_remaining(self):
        """获取剩余天数"""
        from django.utils import timezone
        if self.subscription_status == 'trial':
            if self.trial_end_date:
                remaining = self.trial_end_date - timezone.now()
                return max(0, remaining.days)
        elif self.subscription_status == 'active':
            if self.subscription_end_date:
                remaining = self.subscription_end_date - timezone.now()
                return max(0, remaining.days)
        return 0


class SubscriptionPlan(models.Model):
    """订阅计划模型"""
    name = models.CharField(max_length=100, verbose_name='计划名称')
    description = models.TextField(verbose_name='计划描述', blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='价格（美元）')
    duration_days = models.IntegerField(verbose_name='有效期（天）', default=30)

    # 功能限制
    max_projects = models.IntegerField(verbose_name='最大项目数', default=10)
    max_tasks_per_project = models.IntegerField(verbose_name='每个项目最大任务数', default=1000)
    max_storage_mb = models.IntegerField(verbose_name='最大存储空间（MB）', default=1000)

    # 功能权限
    can_export = models.BooleanField(default=True, verbose_name='可以导出')
    can_import = models.BooleanField(default=True, verbose_name='可以导入')
    can_use_api = models.BooleanField(default=True, verbose_name='可以使用API')
    can_collaborate = models.BooleanField(default=True, verbose_name='可以协作')

    is_active = models.BooleanField(default=True, verbose_name='是否激活')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '订阅计划'
        verbose_name_plural = '订阅计划'
        ordering = ['price']

    def __str__(self):
        return f"{self.name} - ${self.price}"


class UserSubscription(models.Model):
    """用户订阅记录模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='subscriptions', verbose_name='用户')
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.CASCADE, verbose_name='订阅计划')

    start_date = models.DateTimeField(verbose_name='开始时间')
    end_date = models.DateTimeField(verbose_name='结束时间')

    # 支付信息
    payment_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='支付金额')
    payment_method = models.CharField(max_length=50, verbose_name='支付方式', blank=True)
    payment_transaction_id = models.CharField(max_length=100, verbose_name='交易ID', blank=True)

    # 状态
    STATUS_CHOICES = [
        ('pending', '待支付'),
        ('active', '激活'),
        ('expired', '已过期'),
        ('cancelled', '已取消'),
        ('refunded', '已退款'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '用户订阅记录'
        verbose_name_plural = '用户订阅记录'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.plan.name} ({self.status})"
