from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import UserProfile, SubscriptionPlan, UserSubscription


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户配置'


class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)


# 重新注册User模型
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin):
    list_display = ['name', 'price', 'duration_days', 'max_projects', 'max_tasks_per_project', 'is_active']
    list_filter = ['is_active', 'can_export', 'can_import', 'can_use_api', 'can_collaborate']
    search_fields = ['name', 'description']
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'price', 'duration_days', 'is_active')
        }),
        ('功能限制', {
            'fields': ('max_projects', 'max_tasks_per_project', 'max_storage_mb')
        }),
        ('功能权限', {
            'fields': ('can_export', 'can_import', 'can_use_api', 'can_collaborate')
        }),
    )


@admin.register(UserSubscription)
class UserSubscriptionAdmin(admin.ModelAdmin):
    list_display = ['user', 'plan', 'status', 'start_date', 'end_date', 'payment_amount']
    list_filter = ['status', 'plan', 'start_date']
    search_fields = ['user__username', 'user__email', 'payment_transaction_id']
    readonly_fields = ['created_at', 'updated_at']
