{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/histogram-bellcurve\n * @requires highcharts\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/histogram-bellcurve\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Series\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/histogram-bellcurve\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ histogram_bellcurve_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es-modules/Series/DerivedComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent, defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\n/**\n * Provides methods for auto setting/updating series data based on the based\n * series data.\n * @private\n */\nvar DerivedComposition;\n(function (DerivedComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    DerivedComposition.hasDerivedData = true;\n    /**\n     * Method to be implemented - inside the method the series has already\n     * access to the base series via m `this.baseSeries` and the bases data is\n     * initialised. It should return data in the format accepted by\n     * `Series.setData()` method\n     * @private\n     */\n    DerivedComposition.setDerivedData = noop;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        const seriesProto = SeriesClass.prototype;\n        seriesProto.addBaseSeriesEvents = addBaseSeriesEvents;\n        seriesProto.addEvents = addEvents;\n        seriesProto.destroy = destroy;\n        seriesProto.init = init;\n        seriesProto.setBaseSeries = setBaseSeries;\n        return SeriesClass;\n    }\n    DerivedComposition.compose = compose;\n    /**\n     * Initialise series\n     * @private\n     */\n    function init() {\n        highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default().prototype.init.apply(this, arguments);\n        this.initialised = false;\n        this.baseSeries = null;\n        this.eventRemovers = [];\n        this.addEvents();\n    }\n    DerivedComposition.init = init;\n    /**\n     * Sets base series for the series\n     * @private\n     */\n    function setBaseSeries() {\n        const chart = this.chart, baseSeriesOptions = this.options.baseSeries, baseSeries = (defined(baseSeriesOptions) &&\n            (chart.series[baseSeriesOptions] ||\n                chart.get(baseSeriesOptions)));\n        this.baseSeries = baseSeries || null;\n    }\n    DerivedComposition.setBaseSeries = setBaseSeries;\n    /**\n     * Adds events for the series\n     * @private\n     */\n    function addEvents() {\n        this.eventRemovers.push(addEvent(this.chart, 'afterLinkSeries', () => {\n            this.setBaseSeries();\n            if (this.baseSeries && !this.initialised) {\n                this.setDerivedData();\n                this.addBaseSeriesEvents();\n                this.initialised = true;\n            }\n        }));\n    }\n    DerivedComposition.addEvents = addEvents;\n    /**\n     * Adds events to the base series - it required for recalculating the data\n     * in the series if the base series is updated / removed / etc.\n     * @private\n     */\n    function addBaseSeriesEvents() {\n        this.eventRemovers.push(addEvent(this.baseSeries, 'updatedData', () => {\n            this.setDerivedData();\n        }), addEvent(this.baseSeries, 'destroy', () => {\n            this.baseSeries = null;\n            this.initialised = false;\n        }));\n    }\n    DerivedComposition.addBaseSeriesEvents = addBaseSeriesEvents;\n    /**\n     * Destroys the series\n     * @private\n     */\n    function destroy() {\n        this.eventRemovers.forEach((remover) => {\n            remover();\n        });\n        highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default().prototype.destroy.apply(this, arguments);\n    }\n    DerivedComposition.destroy = destroy;\n})(DerivedComposition || (DerivedComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_DerivedComposition = (DerivedComposition);\n\n;// ./code/es-modules/Series/Histogram/HistogramSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Sebastian Domas\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A histogram is a column series which represents the distribution of the\n * data set in the base series. Histogram splits data into bins and shows\n * their frequencies.\n *\n * @sample {highcharts} highcharts/demo/histogram/\n *         Histogram\n *\n * @extends      plotOptions.column\n * @excluding    boostThreshold, dragDrop, pointInterval, pointIntervalUnit,\n *               stacking, boostBlending\n * @product      highcharts\n * @since        6.0.0\n * @requires     modules/histogram-bellcurve\n * @optionparent plotOptions.histogram\n */\nconst HistogramSeriesDefaults = {\n    /**\n     * A preferable number of bins. It is a suggestion, so a histogram may\n     * have a different number of bins. By default it is set to the square\n     * root of the base series' data length. Available options are:\n     * `square-root`, `sturges`, `rice`. You can also define a function\n     * which takes a `baseSeries` as a parameter and should return a\n     * positive integer.\n     *\n     * @type {\"square-root\"|\"sturges\"|\"rice\"|number|Function}\n     */\n    binsNumber: 'square-root',\n    /**\n     * Width of each bin. By default the bin's width is calculated as\n     * `(max - min) / number of bins`. This option takes precedence over\n     * [binsNumber](#plotOptions.histogram.binsNumber).\n     *\n     * @type {number}\n     */\n    binWidth: void 0,\n    pointPadding: 0,\n    groupPadding: 0,\n    grouping: false,\n    pointPlacement: 'between',\n    tooltip: {\n        headerFormat: '',\n        pointFormat: ('<span style=\"font-size: 0.8em\">{point.x} - {point.x2}' +\n            '</span><br/>' +\n            '<span style=\"color:{point.color}\">\\u25CF</span>' +\n            ' {series.name} <b>{point.y}</b><br/>')\n    }\n};\n/**\n * A `histogram` series. If the [type](#series.histogram.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.histogram\n * @excluding data, dataParser, dataURL, boostThreshold, boostBlending\n * @product   highcharts\n * @since     6.0.0\n * @requires  modules/histogram-bellcurve\n * @apioption series.histogram\n */\n/**\n * An integer identifying the index to use for the base series, or a string\n * representing the id of the series.\n *\n * @type      {number|string}\n * @apioption series.histogram.baseSeries\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Histogram_HistogramSeriesDefaults = (HistogramSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Histogram/HistogramSeries.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Sebastian Domas\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { arrayMax, arrayMin, correctFloat, extend, isNumber, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* ************************************************************************** *\n *  HISTOGRAM\n * ************************************************************************** */\n/**\n * A dictionary with formulas for calculating number of bins based on the\n * base series\n **/\nconst binsNumberFormulas = {\n    'square-root': function (baseSeries) {\n        return Math.ceil(Math.sqrt(baseSeries.options.data.length));\n    },\n    'sturges': function (baseSeries) {\n        return Math.ceil(Math.log(baseSeries.options.data.length) * Math.LOG2E);\n    },\n    'rice': function (baseSeries) {\n        return Math.ceil(2 * Math.pow(baseSeries.options.data.length, 1 / 3));\n    }\n};\n/**\n * Returns a function for mapping number to the closed (right opened) bins\n * @private\n * @param {Array<number>} bins\n * Width of the bins\n */\nfunction fitToBinLeftClosed(bins) {\n    return function (y) {\n        let i = 1;\n        while (bins[i] <= y) {\n            i++;\n        }\n        return bins[--i];\n    };\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Histogram class\n * @private\n * @class\n * @name Highcharts.seriesTypes.histogram\n * @augments Highcharts.Series\n */\nclass HistogramSeries extends ColumnSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    binsNumber() {\n        const binsNumberOption = this.options.binsNumber;\n        const binsNumber = binsNumberFormulas[binsNumberOption] ||\n            // #7457\n            (typeof binsNumberOption === 'function' && binsNumberOption);\n        return Math.ceil((binsNumber && binsNumber(this.baseSeries)) ||\n            (isNumber(binsNumberOption) ?\n                binsNumberOption :\n                binsNumberFormulas['square-root'](this.baseSeries)));\n    }\n    derivedData(baseData, binsNumber, binWidth) {\n        const series = this, max = correctFloat(arrayMax(baseData)), \n        // Float correction needed, because first frequency value is not\n        // corrected when generating frequencies (within for loop).\n        min = correctFloat(arrayMin(baseData)), frequencies = [], bins = {}, data = [];\n        let x;\n        binWidth = series.binWidth = (correctFloat(isNumber(binWidth) ?\n            (binWidth || 1) :\n            (max - min) / binsNumber));\n        // #12077 negative pointRange causes wrong calculations,\n        // browser hanging.\n        series.options.pointRange = Math.max(binWidth, 0);\n        // If binWidth is 0 then max and min are equaled,\n        // increment the x with some positive value to quit the loop\n        for (x = min; \n        // This condition is needed because of the margin of error while\n        // operating on decimal numbers. Without that, additional bin\n        // was sometimes noticeable on the graph, because of too small\n        // precision of float correction.\n        x < max &&\n            (series.userOptions.binWidth ||\n                correctFloat(max - x) >= binWidth ||\n                // #13069 - Every add and subtract operation should\n                // be corrected, due to general problems with\n                // operations on float numbers in JS.\n                correctFloat(correctFloat(min + (frequencies.length * binWidth)) -\n                    x) <= 0); x = correctFloat(x + binWidth)) {\n            frequencies.push(x);\n            bins[x] = 0;\n        }\n        if (bins[min] !== 0) {\n            frequencies.push(min);\n            bins[min] = 0;\n        }\n        const fitToBin = fitToBinLeftClosed(frequencies.map((elem) => parseFloat(elem)));\n        for (const y of baseData) {\n            bins[correctFloat(fitToBin(y))]++;\n        }\n        for (const key of Object.keys(bins)) {\n            data.push({\n                x: Number(key),\n                y: bins[key],\n                x2: correctFloat(Number(key) + binWidth)\n            });\n        }\n        data.sort((a, b) => (a.x - b.x));\n        data[data.length - 1].x2 = max;\n        return data;\n    }\n    setDerivedData() {\n        const yData = this.baseSeries?.getColumn('y');\n        if (!yData?.length) {\n            this.setData([]);\n            return;\n        }\n        const data = this.derivedData(yData, this.binsNumber(), this.options.binWidth);\n        this.setData(data, false);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nHistogramSeries.defaultOptions = merge(ColumnSeries.defaultOptions, Histogram_HistogramSeriesDefaults);\nextend(HistogramSeries.prototype, {\n    hasDerivedData: Series_DerivedComposition.hasDerivedData\n});\nSeries_DerivedComposition.compose(HistogramSeries);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('histogram', HistogramSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Histogram_HistogramSeries = ((/* unused pure expression or super */ null && (HistogramSeries)));\n\n;// ./code/es-modules/Series/Bellcurve/BellcurveSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Sebastian Domas\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Optiions\n *\n * */\n/**\n * A bell curve is an areaspline series which represents the probability\n * density function of the normal distribution. It calculates mean and\n * standard deviation of the base series data and plots the curve according\n * to the calculated parameters.\n *\n * @sample {highcharts} highcharts/demo/bellcurve/\n *         Bell curve\n *\n * @extends      plotOptions.areaspline\n * @since        6.0.0\n * @product      highcharts\n * @excluding    boostThreshold, connectNulls, dragDrop, stacking,\n *               pointInterval, pointIntervalUnit\n * @requires     modules/histogram-bellcurve\n * @optionparent plotOptions.bellcurve\n */\nconst BellcurveSeriesDefaults = {\n    /**\n     * @see [fillColor](#plotOptions.bellcurve.fillColor)\n     * @see [fillOpacity](#plotOptions.bellcurve.fillOpacity)\n     *\n     * @apioption plotOptions.bellcurve.color\n     */\n    /**\n     * @see [color](#plotOptions.bellcurve.color)\n     * @see [fillOpacity](#plotOptions.bellcurve.fillOpacity)\n     *\n     * @apioption plotOptions.bellcurve.fillColor\n     */\n    /**\n     * @see [color](#plotOptions.bellcurve.color)\n     * @see [fillColor](#plotOptions.bellcurve.fillColor)\n     *\n     * @default   {highcharts} 0.75\n     * @default   {highstock} 0.75\n     * @apioption plotOptions.bellcurve.fillOpacity\n     */\n    /**\n     * This option allows to define the length of the bell curve. A unit of\n     * the length of the bell curve is standard deviation.\n     *\n     * @sample highcharts/plotoptions/bellcurve-intervals-pointsininterval\n     *         Intervals and points in interval\n     */\n    intervals: 3,\n    /**\n     * Defines how many points should be plotted within 1 interval. See\n     * `plotOptions.bellcurve.intervals`.\n     *\n     * @sample highcharts/plotoptions/bellcurve-intervals-pointsininterval\n     *         Intervals and points in interval\n     */\n    pointsInInterval: 3,\n    marker: {\n        enabled: false\n    }\n};\n/**\n * A `bellcurve` series. If the [type](#series.bellcurve.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * For options that apply to multiple series, it is recommended to add\n * them to the [plotOptions.series](#plotOptions.series) options structure.\n * To apply to all series of this specific type, apply it to\n * [plotOptions.bellcurve](#plotOptions.bellcurve).\n *\n * @extends   series,plotOptions.bellcurve\n * @since     6.0.0\n * @product   highcharts\n * @excluding dataParser, dataURL, data, boostThreshold, boostBlending\n * @requires  modules/histogram-bellcurve\n * @apioption series.bellcurve\n */\n/**\n * An integer identifying the index to use for the base series, or a string\n * representing the id of the series.\n *\n * @type      {number|string}\n * @apioption series.bellcurve.baseSeries\n */\n/**\n * @see [fillColor](#series.bellcurve.fillColor)\n * @see [fillOpacity](#series.bellcurve.fillOpacity)\n *\n * @apioption series.bellcurve.color\n */\n/**\n * @see [color](#series.bellcurve.color)\n * @see [fillOpacity](#series.bellcurve.fillOpacity)\n *\n * @apioption series.bellcurve.fillColor\n */\n/**\n * @see [color](#series.bellcurve.color)\n * @see [fillColor](#series.bellcurve.fillColor)\n *\n * @default   {highcharts} 0.75\n * @default   {highstock} 0.75\n * @apioption series.bellcurve.fillOpacity\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bellcurve_BellcurveSeriesDefaults = (BellcurveSeriesDefaults);\n\n;// ./code/es-modules/Series/Bellcurve/BellcurveSeries.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Sebastian Domas\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { areaspline: AreaSplineSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: BellcurveSeries_correctFloat, isNumber: BellcurveSeries_isNumber, merge: BellcurveSeries_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Bell curve class\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.bellcurve\n *\n * @augments Highcharts.Series\n */\nclass BellcurveSeries extends AreaSplineSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /** @private */\n    static mean(data) {\n        const length = data.length, sum = data.reduce(function (sum, value) {\n            return (sum += value);\n        }, 0);\n        return length > 0 && sum / length;\n    }\n    /** @private */\n    static standardDeviation(data, average) {\n        const len = data.length;\n        average = BellcurveSeries_isNumber(average) ?\n            average : BellcurveSeries.mean(data);\n        const sum = data.reduce((sum, value) => {\n            const diff = value - average;\n            return (sum += diff * diff);\n        }, 0);\n        return len > 1 && Math.sqrt(sum / (len - 1));\n    }\n    /** @private */\n    static normalDensity(x, mean, standardDeviation) {\n        const translation = x - mean;\n        return Math.exp(-(translation * translation) /\n            (2 * standardDeviation * standardDeviation)) / (standardDeviation * Math.sqrt(2 * Math.PI));\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    derivedData(mean, standardDeviation) {\n        const options = this.options, intervals = options.intervals, pointsInInterval = options.pointsInInterval, stop = intervals * pointsInInterval * 2 + 1, increment = standardDeviation / pointsInInterval, data = [];\n        let x = mean - intervals * standardDeviation;\n        for (let i = 0; i < stop; i++) {\n            data.push([x, BellcurveSeries.normalDensity(x, mean, standardDeviation)]);\n            x += increment;\n        }\n        return data;\n    }\n    setDerivedData() {\n        const series = this;\n        if (series.baseSeries?.getColumn('y').length || 0 > 1) {\n            series.setMean();\n            series.setStandardDeviation();\n            series.setData(series.derivedData(series.mean || 0, series.standardDeviation || 0), false, void 0, false);\n        }\n        return (void 0);\n    }\n    setMean() {\n        const series = this;\n        series.mean = BellcurveSeries_correctFloat(BellcurveSeries.mean(series.baseSeries?.getColumn('y') || []));\n    }\n    setStandardDeviation() {\n        const series = this;\n        series.standardDeviation = BellcurveSeries_correctFloat(BellcurveSeries.standardDeviation(series.baseSeries?.getColumn('y') || [], series.mean));\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBellcurveSeries.defaultOptions = BellcurveSeries_merge(AreaSplineSeries.defaultOptions, Bellcurve_BellcurveSeriesDefaults);\nSeries_DerivedComposition.compose(BellcurveSeries);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('bellcurve', BellcurveSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bellcurve_BellcurveSeries = ((/* unused pure expression or super */ null && (BellcurveSeries)));\n\n;// ./code/es-modules/masters/modules/histogram-bellcurve.js\n\n\n\n\n\n/* harmony default export */ const histogram_bellcurve_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__512__", "DerivedComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "histogram_bellcurve_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "noop", "addEvent", "defined", "init", "apply", "arguments", "initialised", "baseSeries", "eventRemovers", "addEvents", "setBaseSeries", "chart", "baseSeriesOptions", "options", "series", "push", "setDerivedData", "addBaseSeriesEvents", "destroy", "for<PERSON>ach", "remover", "hasDerivedData", "compose", "SeriesClass", "seriesProto", "Series_DerivedComposition", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "column", "ColumnSeries", "seriesTypes", "arrayMax", "arrayMin", "correctFloat", "extend", "isNumber", "merge", "binsNumberFormulas", "Math", "ceil", "sqrt", "data", "length", "log", "LOG2E", "pow", "HistogramSeries", "binsNumber", "binsNumberOption", "derivedData", "baseData", "<PERSON><PERSON><PERSON><PERSON>", "bins", "x", "max", "min", "frequencies", "pointRange", "userOptions", "fitToBin", "map", "elem", "parseFloat", "y", "i", "keys", "Number", "x2", "sort", "b", "yData", "getColumn", "setData", "defaultOptions", "pointPadding", "groupPadding", "grouping", "pointPlacement", "tooltip", "headerFormat", "pointFormat", "registerSeriesType", "areaspline", "AreaSplineSeries", "BellcurveSeries_correctFloat", "BellcurveSeries_isNumber", "BellcurveSeries_merge", "BellcurveSeries", "mean", "sum", "reduce", "value", "standardDeviation", "average", "len", "diff", "normalDensity", "translation", "exp", "PI", "intervals", "pointsInInterval", "stop", "increment", "set<PERSON><PERSON>", "setStandardDeviation", "marker", "enabled"], "mappings": "CAUA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC3G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,yCAA0C,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,MAAS,CAACA,EAAK,cAAiB,CAAE,GACjJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,yCAAyC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAErJA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACrH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAmHNC,EAnHUC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmGzB,EAAoB,KACvH0B,EAAuH1B,EAAoBI,CAAC,CAACqB,GASjJ,GAAM,CAAEE,KAAAA,CAAI,CAAE,CAAIH,IAGZ,CAAEI,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAE,CAAIL,KAY/B,AAAC,SAAU3B,CAAkB,EA2CzB,SAASiC,IACLJ,IAA0GR,SAAS,CAACY,IAAI,CAACC,KAAK,CAAC,IAAI,CAAEC,WACrI,IAAI,CAACC,WAAW,CAAG,CAAA,EACnB,IAAI,CAACC,UAAU,CAAG,KAClB,IAAI,CAACC,aAAa,CAAG,EAAE,CACvB,IAAI,CAACC,SAAS,EAClB,CAMA,SAASC,IACL,IAAMC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAoB,IAAI,CAACC,OAAO,CAACN,UAAU,CAAEA,EAAcL,EAAQU,IACxFD,CAAAA,EAAMG,MAAM,CAACF,EAAkB,EAC5BD,EAAMvB,GAAG,CAACwB,EAAiB,CACnC,CAAA,IAAI,CAACL,UAAU,CAAGA,GAAc,IACpC,CAMA,SAASE,IACL,IAAI,CAACD,aAAa,CAACO,IAAI,CAACd,EAAS,IAAI,CAACU,KAAK,CAAE,kBAAmB,KAC5D,IAAI,CAACD,aAAa,GACd,IAAI,CAACH,UAAU,EAAI,CAAC,IAAI,CAACD,WAAW,GACpC,IAAI,CAACU,cAAc,GACnB,IAAI,CAACC,mBAAmB,GACxB,IAAI,CAACX,WAAW,CAAG,CAAA,EAE3B,GACJ,CAOA,SAASW,IACL,IAAI,CAACT,aAAa,CAACO,IAAI,CAACd,EAAS,IAAI,CAACM,UAAU,CAAE,cAAe,KAC7D,IAAI,CAACS,cAAc,EACvB,GAAIf,EAAS,IAAI,CAACM,UAAU,CAAE,UAAW,KACrC,IAAI,CAACA,UAAU,CAAG,KAClB,IAAI,CAACD,WAAW,CAAG,CAAA,CACvB,GACJ,CAMA,SAASY,IACL,IAAI,CAACV,aAAa,CAACW,OAAO,CAAC,AAACC,IACxBA,GACJ,GACArB,IAA0GR,SAAS,CAAC2B,OAAO,CAACd,KAAK,CAAC,IAAI,CAAEC,UAC5I,CAzFAnC,EAAmBmD,cAAc,CAAG,CAAA,EAQpCnD,EAAmB8C,cAAc,CAAGhB,EAmBpC9B,EAAmBoD,OAAO,CAT1B,SAAiBC,CAAW,EACxB,IAAMC,EAAcD,EAAYhC,SAAS,CAMzC,OALAiC,EAAYP,mBAAmB,CAAGA,EAClCO,EAAYf,SAAS,CAAGA,EACxBe,EAAYN,OAAO,CAAGA,EACtBM,EAAYrB,IAAI,CAAGA,EACnBqB,EAAYd,aAAa,CAAGA,EACrBa,CACX,EAaArD,EAAmBiC,IAAI,CAAGA,EAW1BjC,EAAmBwC,aAAa,CAAGA,EAenCxC,EAAmBuC,SAAS,CAAGA,EAc/BvC,EAAmB+C,mBAAmB,CAAGA,EAWzC/C,EAAmBgD,OAAO,CAAGA,CACjC,EAAGhD,GAAuBA,CAAAA,EAAqB,CAAC,CAAA,GAMnB,IAAMuD,EAA6BvD,EA8FhE,IAAIwD,EAAmIrD,EAAoB,KACvJsD,EAAuJtD,EAAoBI,CAAC,CAACiD,GAgBjL,GAAM,CAAEE,OAAQC,CAAY,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAElL,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,aAAAA,CAAY,CAAEC,OAAAA,CAAM,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE,CAAIvC,IAQjEwC,EAAqB,CACvB,cAAe,SAAU9B,CAAU,EAC/B,OAAO+B,KAAKC,IAAI,CAACD,KAAKE,IAAI,CAACjC,EAAWM,OAAO,CAAC4B,IAAI,CAACC,MAAM,EAC7D,EACA,QAAW,SAAUnC,CAAU,EAC3B,OAAO+B,KAAKC,IAAI,CAACD,KAAKK,GAAG,CAACpC,EAAWM,OAAO,CAAC4B,IAAI,CAACC,MAAM,EAAIJ,KAAKM,KAAK,CAC1E,EACA,KAAQ,SAAUrC,CAAU,EACxB,OAAO+B,KAAKC,IAAI,CAAC,EAAID,KAAKO,GAAG,CAACtC,EAAWM,OAAO,CAAC4B,IAAI,CAACC,MAAM,CAAE,EAAI,GACtE,CACJ,CA4BA,OAAMI,UAAwBjB,EAM1BkB,YAAa,CACT,IAAMC,EAAmB,IAAI,CAACnC,OAAO,CAACkC,UAAU,CAC1CA,EAAaV,CAAkB,CAACW,EAAiB,EAElD,AAA4B,YAA5B,OAAOA,GAAmCA,EAC/C,OAAOV,KAAKC,IAAI,CAAC,AAACQ,GAAcA,EAAW,IAAI,CAACxC,UAAU,GACrD4B,CAAAA,EAASa,GACNA,EACAX,CAAkB,CAAC,cAAc,CAAC,IAAI,CAAC9B,UAAU,CAAA,EAC7D,CACA0C,YAAYC,CAAQ,CAAEH,CAAU,CAAEI,CAAQ,CAAE,KArCpBC,MA0ChBC,EAJiBC,EAAMrB,EAAaF,EAASmB,IAGjDK,EAAMtB,EAAaD,EAASkB,IAAYM,EAAc,EAAE,CAAEJ,EAAO,CAAC,EAAGX,EAAO,EAAE,CAU9E,IARAU,EAAWrC,AALI,IAAI,CAKDqC,QAAQ,CAAIlB,EAAaE,EAASgB,GAC/CA,GAAY,EACb,AAACG,CAAAA,EAAMC,CAAE,EAAKR,GAGlBjC,AAVe,IAAI,CAUZD,OAAO,CAAC4C,UAAU,CAAGnB,KAAKgB,GAAG,CAACH,EAAU,GAG1CE,EAAIE,EAKTF,EAAIC,GACCxC,CAAAA,AAnBU,IAAI,CAmBP4C,WAAW,CAACP,QAAQ,EACxBlB,EAAaqB,EAAMD,IAAMF,GAIzBlB,AACU,GADVA,EAAaA,EAAasB,EAAOC,EAAYd,MAAM,CAAGS,GAClDE,EAAM,EAAIA,EAAIpB,EAAaoB,EAAIF,GACvCK,EAAYzC,IAAI,CAACsC,GACjBD,CAAI,CAACC,EAAE,CAAG,CAEI,CAAA,IAAdD,CAAI,CAACG,EAAI,GACTC,EAAYzC,IAAI,CAACwC,GACjBH,CAAI,CAACG,EAAI,CAAG,GAEhB,IAAMI,GAvEcP,EAuEgBI,EAAYI,GAAG,CAAC,AAACC,GAASC,WAAWD,IAtEtE,SAAUE,CAAC,EACd,IAAIC,EAAI,EACR,KAAOZ,CAAI,CAACY,EAAE,EAAID,GACdC,IAEJ,OAAOZ,CAAI,CAAC,EAAEY,EAAE,AACpB,GAiEI,IAAK,IAAMD,KAAKb,EACZE,CAAI,CAACnB,EAAa0B,EAASI,IAAI,GAEnC,IAAK,IAAMhF,KAAOE,OAAOgF,IAAI,CAACb,GAC1BX,EAAK1B,IAAI,CAAC,CACNsC,EAAGa,OAAOnF,GACVgF,EAAGX,CAAI,CAACrE,EAAI,CACZoF,GAAIlC,EAAaiC,OAAOnF,GAAOoE,EACnC,GAIJ,OAFAV,EAAK2B,IAAI,CAAC,CAACvF,EAAGwF,IAAOxF,EAAEwE,CAAC,CAAGgB,EAAEhB,CAAC,EAC9BZ,CAAI,CAACA,EAAKC,MAAM,CAAG,EAAE,CAACyB,EAAE,CAAGb,EACpBb,CACX,CACAzB,gBAAiB,CACb,IAAMsD,EAAQ,IAAI,CAAC/D,UAAU,EAAEgE,UAAU,KACzC,GAAI,CAACD,GAAO5B,OAAQ,CAChB,IAAI,CAAC8B,OAAO,CAAC,EAAE,EACf,MACJ,CACA,IAAM/B,EAAO,IAAI,CAACQ,WAAW,CAACqB,EAAO,IAAI,CAACvB,UAAU,GAAI,IAAI,CAAClC,OAAO,CAACsC,QAAQ,EAC7E,IAAI,CAACqB,OAAO,CAAC/B,EAAM,CAAA,EACvB,CACJ,CAMAK,EAAgB2B,cAAc,CAAGrC,EAAMP,EAAa4C,cAAc,CA5MlC,CAW5B1B,WAAY,cAQZI,SAAU,KAAK,EACfuB,aAAc,EACdC,aAAc,EACdC,SAAU,CAAA,EACVC,eAAgB,UAChBC,QAAS,CACLC,aAAc,GACdC,YAAc,iJAIlB,CACJ,GA8KA9C,EAAOY,EAAgBvD,SAAS,CAAE,CAC9B8B,eAAgBI,EAA0BJ,cAAc,AAC5D,GACAI,EAA0BH,OAAO,CAACwB,GAClCnB,IAA0IsD,kBAAkB,CAAC,YAAanC,GAuJ1K,GAAM,CAAEoC,WAAYC,CAAgB,CAAE,CAAG,AAACxD,IAA2IG,WAAW,CAE1L,CAAEG,aAAcmD,CAA4B,CAAEjD,SAAUkD,CAAwB,CAAEjD,MAAOkD,CAAqB,CAAE,CAAIzF,GAe1H,OAAM0F,UAAwBJ,EAO1B,OAAOK,KAAK/C,CAAI,CAAE,CACd,IAAMC,EAASD,EAAKC,MAAM,CAAE+C,EAAMhD,EAAKiD,MAAM,CAAC,SAAUD,CAAG,CAAEE,CAAK,EAC9D,OAAQF,EAAOE,CACnB,EAAG,GACH,OAAOjD,EAAS,GAAK+C,EAAM/C,CAC/B,CAEA,OAAOkD,kBAAkBnD,CAAI,CAAEoD,CAAO,CAAE,CACpC,IAAMC,EAAMrD,EAAKC,MAAM,CACvBmD,EAAUR,EAAyBQ,GAC/BA,EAAUN,EAAgBC,IAAI,CAAC/C,GACnC,IAAMgD,EAAMhD,EAAKiD,MAAM,CAAC,CAACD,EAAKE,KAC1B,IAAMI,EAAOJ,EAAQE,EACrB,OAAQJ,EAAOM,EAAOA,CAC1B,EAAG,GACH,OAAOD,EAAM,GAAKxD,KAAKE,IAAI,CAACiD,EAAOK,CAAAA,EAAM,CAAA,EAC7C,CAEA,OAAOE,cAAc3C,CAAC,CAAEmC,CAAI,CAAEI,CAAiB,CAAE,CAC7C,IAAMK,EAAc5C,EAAImC,EACxB,OAAOlD,KAAK4D,GAAG,CAAC,CAAED,CAAAA,EAAcA,CAAU,EACrC,CAAA,EAAIL,EAAoBA,CAAgB,GAAOA,CAAAA,EAAoBtD,KAAKE,IAAI,CAAC,EAAIF,KAAK6D,EAAE,CAAA,CACjG,CAMAlD,YAAYuC,CAAI,CAAEI,CAAiB,CAAE,CACjC,IAAM/E,EAAU,IAAI,CAACA,OAAO,CAAEuF,EAAYvF,EAAQuF,SAAS,CAAEC,EAAmBxF,EAAQwF,gBAAgB,CAAEC,EAAOF,EAAYC,EAAmB,EAAI,EAAGE,EAAYX,EAAoBS,EAAkB5D,EAAO,EAAE,CAC9MY,EAAImC,EAAOY,EAAYR,EAC3B,IAAK,IAAI5B,EAAI,EAAGA,EAAIsC,EAAMtC,IACtBvB,EAAK1B,IAAI,CAAC,CAACsC,EAAGkC,EAAgBS,aAAa,CAAC3C,EAAGmC,EAAMI,GAAmB,EACxEvC,GAAKkD,EAET,OAAO9D,CACX,CACAzB,gBAAiB,CAETF,AADW,IAAI,CACRP,UAAU,EAAEgE,UAAU,KAAK7B,SAClC5B,AAFW,IAAI,CAER0F,OAAO,GACd1F,AAHW,IAAI,CAGR2F,oBAAoB,GAC3B3F,AAJW,IAAI,CAIR0D,OAAO,CAAC1D,AAJJ,IAAI,CAIOmC,WAAW,CAACnC,AAJvB,IAAI,CAI0B0E,IAAI,EAAI,EAAG1E,AAJzC,IAAI,CAI4C8E,iBAAiB,EAAI,GAAI,CAAA,EAAO,KAAK,EAAG,CAAA,GAG3G,CACAY,SAAU,CAEN1F,AADe,IAAI,CACZ0E,IAAI,CAAGJ,EAA6BG,EAAgBC,IAAI,CAAC1E,AADjD,IAAI,CACoDP,UAAU,EAAEgE,UAAU,MAAQ,EAAE,EAC3G,CACAkC,sBAAuB,CAEnB3F,AADe,IAAI,CACZ8E,iBAAiB,CAAGR,EAA6BG,EAAgBK,iBAAiB,CAAC9E,AAD3E,IAAI,CAC8EP,UAAU,EAAEgE,UAAU,MAAQ,EAAE,CAAEzD,AADpH,IAAI,CACuH0E,IAAI,EAClJ,CACJ,CAMAD,EAAgBd,cAAc,CAAGa,EAAsBH,EAAiBV,cAAc,CAhMtD,CA4B5B2B,UAAW,EAQXC,iBAAkB,EAClBK,OAAQ,CACJC,QAAS,CAAA,CACb,CACJ,GAyJAlF,EAA0BH,OAAO,CAACiE,GAClC5D,IAA0IsD,kBAAkB,CAAC,YAAaM,GAc7I,IAAM5F,EAA4BE,IAGrD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}