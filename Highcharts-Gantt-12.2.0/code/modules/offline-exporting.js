!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.AST,e._Highcharts.Chart,e._Highcharts.HttpUtilities):"function"==typeof define&&define.amd?define("highcharts/modules/offline-exporting",["highcharts/highcharts"],function(e){return t(e,e.AST,e.Chart,e.HttpUtilities)}):"object"==typeof exports?exports["highcharts/modules/offline-exporting"]=t(e._Highcharts,e._Highcharts.AST,e._Highcharts.Chart,e._Highcharts.HttpUtilities):e.Highcharts=t(e.Highcharts,e.Highcharts.AST,e.Highcharts.Chart,e.Highcharts.HttpUtilities)}("undefined"==typeof window?this:window,(e,t,n,o)=>(()=>{"use strict";var i,r,l,s,a={156:e=>{e.exports=o},660:e=>{e.exports=t},944:t=>{t.exports=e},960:e=>{e.exports=n}},c={};function h(e){var t=c[e];if(void 0!==t)return t.exports;var n=c[e]={exports:{}};return a[e](n,n.exports,h),n.exports}h.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return h.d(t,{a:t}),t},h.d=(e,t)=>{for(var n in t)h.o(t,n)&&!h.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},h.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var d={};h.d(d,{default:()=>ep});var p=h(944),u=h.n(p);let{isSafari:g,win:f,win:{document:m}}=u(),x=f.URL||f.webkitURL||f;function y(e){let t=e.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(t&&t.length>3&&f.atob&&f.ArrayBuffer&&f.Uint8Array&&f.Blob&&x.createObjectURL){let e=f.atob(t[3]),n=new f.ArrayBuffer(e.length),o=new f.Uint8Array(n);for(let t=0;t<o.length;++t)o[t]=e.charCodeAt(t);return x.createObjectURL(new f.Blob([o],{type:t[1]}))}}let b={dataURLtoBlob:y,downloadURL:function(e,t){let n=f.navigator,o=m.createElement("a");if("string"!=typeof e&&!(e instanceof String)&&n.msSaveOrOpenBlob){n.msSaveOrOpenBlob(e,t);return}if(e=""+e,n.userAgent.length>1e3)throw Error("Input too long");let i=/Edge\/\d+/.test(n.userAgent);if((g&&"string"==typeof e&&0===e.indexOf("data:application/pdf")||i||e.length>2e6)&&!(e=y(e)||""))throw Error("Failed to convert to blob");if(void 0!==o.download)o.href=e,o.download=t,m.body.appendChild(o),o.click(),m.body.removeChild(o);else try{if(!f.open(e,"chart"))throw Error("Failed to open window")}catch{f.location.href=e}}};var v=h(660),w=h.n(v);h(960),function(e){e.compose=function(e){return e.navigation||(e.navigation=new t(e)),e};class t{constructor(e){this.updates=[],this.chart=e}addUpdate(e){this.chart.navigation.updates.push(e)}update(e,t){this.updates.forEach(n=>{n.call(this.chart,e,t)})}}e.Additions=t}(i||(i={}));let S=i,{isTouchDevice:E}=u(),C={exporting:{allowTableSorting:!0,type:"image/png",url:`https://export-svg.highcharts.com?v=${u().version}`,pdfFont:{normal:void 0,bold:void 0,bolditalic:void 0,italic:void 0},printMaxWidth:780,scale:2,buttons:{contextButton:{className:"highcharts-contextbutton",menuClassName:"highcharts-contextmenu",symbol:"menu",titleKey:"contextButtonTitle",menuItems:["viewFullscreen","printChart","separator","downloadPNG","downloadJPEG","downloadSVG"]}},menuItemDefinitions:{viewFullscreen:{textKey:"viewFullscreen",onclick:function(){this.fullscreen&&this.fullscreen.toggle()}},printChart:{textKey:"printChart",onclick:function(){this.print()}},separator:{separator:!0},downloadPNG:{textKey:"downloadPNG",onclick:function(){this.exportChart()}},downloadJPEG:{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},downloadPDF:{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},downloadSVG:{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}}},lang:{viewFullscreen:"View in full screen",exitFullscreen:"Exit from full screen",printChart:"Print chart",downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"},navigation:{buttonOptions:{symbolSize:14,symbolX:14.5,symbolY:13.5,align:"right",buttonSpacing:5,height:28,y:-5,verticalAlign:"top",width:28,symbolFill:"#666666",symbolStroke:"#666666",symbolStrokeWidth:3,theme:{fill:"#ffffff",padding:5,stroke:"none","stroke-linecap":"round"}},menuStyle:{border:"none",borderRadius:"3px",background:"#ffffff",padding:"0.5em"},menuItemStyle:{background:"none",borderRadius:"3px",color:"#333333",padding:"0.5em",fontSize:E?"0.9em":"0.8em",transition:"background 250ms, color 250ms"},menuItemHoverStyle:{background:"#f2f2f2"}}};!function(e){let t=[];function n(e,t,n,o){return[["M",e,t+2.5],["L",e+n,t+2.5],["M",e,t+o/2+.5],["L",e+n,t+o/2+.5],["M",e,t+o-1.5],["L",e+n,t+o-1.5]]}function o(e,t,n,o){let i=o/3-2,r=[];return r.concat(this.circle(n-i,t,i,i),this.circle(n-i,t+i+4,i,i),this.circle(n-i,t+2*(i+4),i,i))}e.compose=function(e){if(-1===t.indexOf(e)){t.push(e);let i=e.prototype.symbols;i.menu=n,i.menuball=o.bind(i)}}}(r||(r={}));let F=r,{composed:O}=u(),{addEvent:T,fireEvent:k,pushUnique:P}=u();function L(){this.fullscreen=new M(this)}class M{static compose(e){P(O,"Fullscreen")&&T(e,"beforeRender",L)}constructor(e){this.chart=e,this.isOpen=!1;let t=e.renderTo;!this.browserProps&&("function"==typeof t.requestFullscreen?this.browserProps={fullscreenChange:"fullscreenchange",requestFullscreen:"requestFullscreen",exitFullscreen:"exitFullscreen"}:t.mozRequestFullScreen?this.browserProps={fullscreenChange:"mozfullscreenchange",requestFullscreen:"mozRequestFullScreen",exitFullscreen:"mozCancelFullScreen"}:t.webkitRequestFullScreen?this.browserProps={fullscreenChange:"webkitfullscreenchange",requestFullscreen:"webkitRequestFullScreen",exitFullscreen:"webkitExitFullscreen"}:t.msRequestFullscreen&&(this.browserProps={fullscreenChange:"MSFullscreenChange",requestFullscreen:"msRequestFullscreen",exitFullscreen:"msExitFullscreen"}))}close(){let e=this,t=e.chart,n=t.options.chart;k(t,"fullscreenClose",null,function(){e.isOpen&&e.browserProps&&t.container.ownerDocument instanceof Document&&t.container.ownerDocument[e.browserProps.exitFullscreen](),e.unbindFullscreenEvent&&(e.unbindFullscreenEvent=e.unbindFullscreenEvent()),t.setSize(e.origWidth,e.origHeight,!1),e.origWidth=void 0,e.origHeight=void 0,n.width=e.origWidthOption,n.height=e.origHeightOption,e.origWidthOption=void 0,e.origHeightOption=void 0,e.isOpen=!1,e.setButtonText()})}open(){let e=this,t=e.chart,n=t.options.chart;k(t,"fullscreenOpen",null,function(){if(n&&(e.origWidthOption=n.width,e.origHeightOption=n.height),e.origWidth=t.chartWidth,e.origHeight=t.chartHeight,e.browserProps){let n=T(t.container.ownerDocument,e.browserProps.fullscreenChange,function(){e.isOpen?(e.isOpen=!1,e.close()):(t.setSize(null,null,!1),e.isOpen=!0,e.setButtonText())}),o=T(t,"destroy",n);e.unbindFullscreenEvent=()=>{n(),o()};let i=t.renderTo[e.browserProps.requestFullscreen]();i&&i.catch(function(){alert("Full screen is not supported inside a frame.")})}})}setButtonText(){let e=this.chart,t=e.exportDivElements,n=e.options.exporting,o=n&&n.buttons&&n.buttons.contextButton.menuItems,i=e.options.lang;if(n&&n.menuItemDefinitions&&i&&i.exitFullscreen&&i.viewFullscreen&&o&&t){let e=t[o.indexOf("viewFullscreen")];e&&w().setElementHTML(e,this.isOpen?i.exitFullscreen:n.menuItemDefinitions.viewFullscreen.text||i.viewFullscreen)}}toggle(){this.isOpen?this.close():this.open()}}var N=h(156),G=h.n(N);let{defaultOptions:H}=u(),{doc:A,SVG_NS:D,win:R}=u(),{addEvent:B,css:V,createElement:I,discardElement:U,extend:j,find:W,fireEvent:z,isObject:q,merge:$,objectEach:K,pick:_,removeEvent:J,splat:X,uniqueKey:Y}=u();!function(e){let t,n=[/-/,/^(clipPath|cssText|d|height|width)$/,/^font$/,/[lL]ogical(Width|Height)$/,/^parentRule$/,/^(cssRules|ownerRules)$/,/perspective/,/TapHighlightColor/,/^transition/,/^length$/,/^\d+$/],o=["fill","stroke","strokeLinecap","strokeLinejoin","strokeWidth","textAnchor","x","y"];e.inlineAllowlist=[];let i=["clipPath","defs","desc"];function r(e){let t,n,o=this,i=o.renderer,r=$(o.options.navigation.buttonOptions,e),l=r.onclick,s=r.menuItems,a=r.symbolSize||12;if(o.btnCount||(o.btnCount=0),o.exportDivElements||(o.exportDivElements=[],o.exportSVGElements=[]),!1===r.enabled||!r.theme)return;let c=o.styledMode?{}:r.theme;l?n=function(e){e&&e.stopPropagation(),l.call(o,e)}:s&&(n=function(e){e&&e.stopPropagation(),o.contextMenu(h.menuClassName,s,h.translateX||0,h.translateY||0,h.width||0,h.height||0,h),h.setState(2)}),r.text&&r.symbol?c.paddingLeft=_(c.paddingLeft,30):r.text||j(c,{width:r.width,height:r.height,padding:0});let h=i.button(r.text,0,0,n,c,void 0,void 0,void 0,void 0,r.useHTML).addClass(e.className).attr({title:_(o.options.lang[r._titleKey||r.titleKey],"")});h.menuClassName=e.menuClassName||"highcharts-menu-"+o.btnCount++,r.symbol&&(t=i.symbol(r.symbol,Math.round((r.symbolX||0)-a/2),Math.round((r.symbolY||0)-a/2),a,a,{width:a,height:a}).addClass("highcharts-button-symbol").attr({zIndex:1}).add(h),o.styledMode||t.attr({stroke:r.symbolStroke,fill:r.symbolFill,"stroke-width":r.symbolStrokeWidth||1})),h.add(o.exportingGroup).align(j(r,{width:h.width,x:_(r.x,o.buttonOffset)}),!0,"spacingBox"),o.buttonOffset+=((h.width||0)+r.buttonSpacing)*("right"===r.align?-1:1),o.exportSVGElements.push(h,t)}function l(){if(!this.printReverseInfo)return;let{childNodes:e,origDisplay:n,resetParams:o}=this.printReverseInfo;this.moveContainers(this.renderTo),[].forEach.call(e,function(e,t){1===e.nodeType&&(e.style.display=n[t]||"")}),this.isPrinting=!1,o&&this.setSize.apply(this,o),delete this.printReverseInfo,t=void 0,z(this,"afterPrint")}function s(){let e=A.body,t=this.options.exporting.printMaxWidth,n={childNodes:e.childNodes,origDisplay:[],resetParams:void 0};this.isPrinting=!0,this.pointer?.reset(void 0,0),z(this,"beforePrint"),t&&this.chartWidth>t&&(n.resetParams=[this.options.chart.width,void 0,!1],this.setSize(t,void 0,!1)),[].forEach.call(n.childNodes,function(e,t){1===e.nodeType&&(n.origDisplay[t]=e.style.display,e.style.display="none")}),this.moveContainers(e),this.printReverseInfo=n}function a(e){e.renderExporting(),B(e,"redraw",e.renderExporting),B(e,"destroy",e.destroyExport)}function c(e,t,n,o,i,r,l){let s=this,a=s.options.navigation,c=s.chartWidth,h=s.chartHeight,d="cache-"+e,p=Math.max(i,r),g,f=s[d];f||(s.exportContextMenu=s[d]=f=I("div",{className:e},{position:"absolute",zIndex:1e3,padding:p+"px",pointerEvents:"auto",...s.renderer.style},s.scrollablePlotArea?.fixedDiv||s.container),g=I("ul",{className:"highcharts-menu"},s.styledMode?{}:{listStyle:"none",margin:0,padding:0},f),s.styledMode||V(g,j({MozBoxShadow:"3px 3px 10px #888",WebkitBoxShadow:"3px 3px 10px #888",boxShadow:"3px 3px 10px #888"},a.menuStyle)),f.hideMenu=function(){V(f,{display:"none"}),l&&l.setState(0),s.openMenu=!1,V(s.renderTo,{overflow:"hidden"}),V(s.container,{overflow:"hidden"}),u().clearTimeout(f.hideTimer),z(s,"exportMenuHidden")},s.exportEvents.push(B(f,"mouseleave",function(){f.hideTimer=R.setTimeout(f.hideMenu,500)}),B(f,"mouseenter",function(){u().clearTimeout(f.hideTimer)}),B(A,"mouseup",function(t){s.pointer?.inClass(t.target,e)||f.hideMenu()}),B(f,"click",function(){s.openMenu&&f.hideMenu()})),t.forEach(function(e){if("string"==typeof e&&(e=s.options.exporting.menuItemDefinitions[e]),q(e,!0)){let t;e.separator?t=I("hr",void 0,void 0,g):("viewData"===e.textKey&&s.isDataTableVisible&&(e.textKey="hideData"),t=I("li",{className:"highcharts-menu-item",onclick:function(t){t&&t.stopPropagation(),f.hideMenu(),"string"!=typeof e&&e.onclick&&e.onclick.apply(s,arguments)}},void 0,g),w().setElementHTML(t,e.text||s.options.lang[e.textKey]),s.styledMode||(t.onmouseover=function(){V(this,a.menuItemHoverStyle)},t.onmouseout=function(){V(this,a.menuItemStyle)},V(t,j({cursor:"pointer"},a.menuItemStyle||{})))),s.exportDivElements.push(t)}}),s.exportDivElements.push(g,f),s.exportMenuWidth=f.offsetWidth,s.exportMenuHeight=f.offsetHeight);let m={display:"block"};n+(s.exportMenuWidth||0)>c?m.right=c-n-i-p+"px":m.left=n-p+"px",o+r+(s.exportMenuHeight||0)>h&&l.alignOptions?.verticalAlign!=="top"?m.bottom=h-o-p+"px":m.top=o+r-p+"px",V(f,m),V(s.renderTo,{overflow:""}),V(s.container,{overflow:""}),s.openMenu=!0,z(s,"exportMenuShown")}function h(e){let t,n=e?e.target:this,o=n.exportSVGElements,i=n.exportDivElements,r=n.exportEvents;o&&(o.forEach((e,i)=>{e&&(e.onclick=e.ontouchstart=null,n[t="cache-"+e.menuClassName]&&delete n[t],o[i]=e.destroy())}),o.length=0),n.exportingGroup&&(n.exportingGroup.destroy(),delete n.exportingGroup),i&&(i.forEach(function(e,t){e&&(u().clearTimeout(e.hideTimer),J(e,"mouseleave"),i[t]=e.onmouseout=e.onmouseover=e.ontouchstart=e.onclick=null,U(e))}),i.length=0),r&&(r.forEach(function(e){e()}),r.length=0)}function d(e,t){let n=this.getSVGForExport(e,t);e=$(this.options.exporting,e),G().post(e.url,{filename:e.filename?e.filename.replace(/\//g,"-"):this.getFilename(),type:e.type,width:e.width,scale:e.scale,svg:n},e.fetchOptions)}function p(e){return e&&this.inlineStyles(),this.resolveCSSVariables(),this.container.innerHTML}function g(){let e=this.userOptions.title&&this.userOptions.title.text,t=this.options.exporting.filename;return t?t.replace(/\//g,"-"):("string"==typeof e&&(t=e.toLowerCase().replace(/<\/?[^>]+(>|$)/g,"").replace(/[\s_]+/g,"-").replace(/[^a-z\d\-]/g,"").replace(/^[\-]+/g,"").replace(/[\-]+/g,"-").substr(0,24).replace(/[\-]+$/g,"")),(!t||t.length<5)&&(t="chart"),t)}function f(e){let t,n,o=$(this.options,e);o.plotOptions=$(this.userOptions.plotOptions,e&&e.plotOptions),o.time=$(this.userOptions.time,e&&e.time);let i=I("div",null,{position:"absolute",top:"-9999em",width:this.chartWidth+"px",height:this.chartHeight+"px"},A.body),r=this.renderTo.style.width,l=this.renderTo.style.height,s=o.exporting.sourceWidth||o.chart.width||/px$/.test(r)&&parseInt(r,10)||(o.isGantt?800:600),a=o.exporting.sourceHeight||o.chart.height||/px$/.test(l)&&parseInt(l,10)||400;j(o.chart,{animation:!1,renderTo:i,forExport:!0,renderer:"SVGRenderer",width:s,height:a}),o.exporting.enabled=!1,delete o.data,o.series=[],this.series.forEach(function(e){(n=$(e.userOptions,{animation:!1,enableMouseTracking:!1,showCheckbox:!1,visible:e.visible})).isInternal||o.series.push(n)});let c={};this.axes.forEach(function(e){e.userOptions.internalKey||(e.userOptions.internalKey=Y()),e.options.isInternal||(c[e.coll]||(c[e.coll]=!0,o[e.coll]=[]),o[e.coll].push($(e.userOptions,{visible:e.visible,type:e.type,uniqueNames:e.uniqueNames})))}),o.colorAxis=this.userOptions.colorAxis;let h=new this.constructor(o,this.callback);return e&&["xAxis","yAxis","series"].forEach(function(t){let n={};e[t]&&(n[t]=e[t],h.update(n))}),this.axes.forEach(function(t){let n=W(h.axes,e=>e.options.internalKey===t.userOptions.internalKey);if(n){let o=t.getExtremes(),i=X(e?.[t.coll]||{})[0],r="min"in i?i.min:o.userMin,l="max"in i?i.max:o.userMax;(void 0!==r&&r!==n.min||void 0!==l&&l!==n.max)&&n.setExtremes(r??void 0,l??void 0,!0,!1)}}),t=h.getChartHTML(this.styledMode||o.exporting?.applyStyleSheets),z(this,"getSVG",{chartCopy:h}),t=this.sanitizeSVG(t,o),o=null,h.destroy(),U(i),t}function m(e,t){let n=this.options.exporting;return this.getSVG($({chart:{borderRadius:0}},n.chartOptions,t,{exporting:{sourceWidth:e&&e.sourceWidth||n.sourceWidth,sourceHeight:e&&e.sourceHeight||n.sourceHeight}}))}function x(){let t,r=e.inlineAllowlist,l={},s=A.createElement("iframe");V(s,{width:"1px",height:"1px",visibility:"hidden"}),A.body.appendChild(s);let a=s.contentWindow&&s.contentWindow.document;a&&a.body.appendChild(a.createElementNS(D,"svg")),!function e(s){let c,h,d,p,g,f,m={};if(a&&1===s.nodeType&&-1===i.indexOf(s.nodeName)){if(c=R.getComputedStyle(s,null),h="svg"===s.nodeName?{}:R.getComputedStyle(s.parentNode,null),!l[s.nodeName]){t=a.getElementsByTagName("svg")[0],d=a.createElementNS(s.namespaceURI,s.nodeName),t.appendChild(d);let e=R.getComputedStyle(d,null),n={};for(let t in e)t.length<1e3&&"string"==typeof e[t]&&!/^\d+$/.test(t)&&(n[t]=e[t]);l[s.nodeName]=n,"text"===s.nodeName&&delete l.text.fill,t.removeChild(d)}for(let e in c)(u().isFirefox||u().isMS||u().isSafari||Object.hasOwnProperty.call(c,e))&&function(e,t){if(p=g=!1,r.length){for(f=r.length;f--&&!g;)g=r[f].test(t);p=!g}for("transform"===t&&"none"===e&&(p=!0),f=n.length;f--&&!p;){if(t.length>1e3)throw Error("Input too long");p=n[f].test(t)||"function"==typeof e}!p&&(h[t]!==e||"svg"===s.nodeName)&&l[s.nodeName][t]!==e&&(o&&-1===o.indexOf(t)?m[t]=e:e&&s.setAttribute(t.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}),e))}(c[e],e);if(V(s,m),"svg"===s.nodeName&&s.setAttribute("stroke-width","1px"),"text"===s.nodeName)return;[].forEach.call(s.children||s.childNodes,e)}}(this.container.querySelector("svg")),t.parentNode.removeChild(t),s.parentNode.removeChild(s)}function y(){let e=this.container.querySelectorAll("*"),t=["color","fill","stop-color","stroke"];Array.from(e).forEach(e=>{t.forEach(t=>{let n=e.getAttribute(t);n?.includes("var(")&&e.setAttribute(t,getComputedStyle(e).getPropertyValue(t))})})}function b(e){let{scrollablePlotArea:t}=this;(t?[t.fixedDiv,t.scrollingContainer]:[this.container]).forEach(function(t){e.appendChild(t)})}function v(){let e=this,t=(t,n,o)=>{e.isDirtyExporting=!0,$(!0,e.options[t],n),_(o,!0)&&e.redraw()};e.exporting={update:function(e,n){t("exporting",e,n)}},S.compose(e).navigation.addUpdate((e,n)=>{t("navigation",e,n)})}function E({alignTo:e,key:t,textPxLength:n}){let o=this.options.exporting,{align:i,buttonSpacing:r=0,verticalAlign:l,width:s=0}=$(this.options.navigation?.buttonOptions,o?.buttons?.contextButton),a=e.width-n,c=s+r;(o?.enabled??!0)&&"title"===t&&"right"===i&&"top"===l&&a<2*c&&(a<c?e.width-=c:this.title?.alignValue!=="left"&&(e.x-=c-a/2))}function O(){let e=this;!e.isPrinting&&(t=e,u().isSafari||e.beforePrint(),setTimeout(()=>{R.focus(),R.print(),u().isSafari||setTimeout(()=>{e.afterPrint()},1e3)},1))}function T(){let e=this,t=e.options.exporting,n=t.buttons,o=e.isDirtyExporting||!e.exportSVGElements;e.buttonOffset=0,e.isDirtyExporting&&e.destroyExport(),o&&!1!==t.enabled&&(e.exportEvents=[],e.exportingGroup=e.exportingGroup||e.renderer.g("exporting-group").attr({zIndex:3}).add(),K(n,function(t){e.addButton(t)}),e.isDirtyExporting=!1)}function k(e,t){let n=e.indexOf("</svg>")+6,o=e.indexOf("<foreignObject")>-1,i=e.substr(n);return e=e.substr(0,n),o?e=e.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />"):i&&t?.exporting?.allowHTML&&(i='<foreignObject x="0" y="0" width="'+t.chart.width+'" height="'+t.chart.height+'"><body xmlns="http://www.w3.org/1999/xhtml">'+i.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />")+"</body></foreignObject>",e=e.replace("</svg>",i+"</svg>")),e=e.replace(/zIndex="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery\d+="[^"]+"/g,"").replace(/url\(("|&quot;)(.*?)("|&quot;)\;?\)/g,"url($2)").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ (NS\d+\:)?href=/g," xlink:href=").replace(/\n+/g," ").replace(/&nbsp;/g,"\xa0").replace(/&shy;/g,"\xad")}e.compose=function(e,n){F.compose(n),M.compose(e);let o=e.prototype;o.exportChart||(o.afterPrint=l,o.exportChart=d,o.inlineStyles=x,o.print=O,o.sanitizeSVG=k,o.getChartHTML=p,o.getSVG=f,o.getSVGForExport=m,o.getFilename=g,o.moveContainers=b,o.beforePrint=s,o.contextMenu=c,o.addButton=r,o.destroyExport=h,o.renderExporting=T,o.resolveCSSVariables=y,o.callbacks.push(a),B(e,"init",v),B(e,"layOutTitle",E),u().isSafari&&R.matchMedia("print").addListener(function(e){t&&(e.matches?t.beforePrint():t.afterPrint())}),H.exporting=$(C.exporting,H.exporting),H.lang=$(C.lang,H.lang),H.navigation=$(C.navigation,H.navigation))}}(l||(l={}));let Z=l,Q={libURL:"https://code.highcharts.com/12.2.0/lib/",menuItemDefinitions:{downloadPNG:{textKey:"downloadPNG",onclick:function(){this.exportChartLocal()}},downloadJPEG:{textKey:"downloadJPEG",onclick:function(){this.exportChartLocal({type:"image/jpeg"})}},downloadSVG:{textKey:"downloadSVG",onclick:function(){this.exportChartLocal({type:"image/svg+xml"})}},downloadPDF:{textKey:"downloadPDF",onclick:function(){this.exportChartLocal({type:"application/pdf"})}}}},{defaultOptions:ee}=u(),{downloadURL:et}=b,{doc:en,win:eo}=u(),{ajax:ei}=G(),{addEvent:er,error:el,extend:es,fireEvent:ea,merge:ec}=u();w().allowedAttributes.push("data-z-index","fill-opacity","filter","rx","ry","stroke-dasharray","stroke-linejoin","stroke-opacity","text-anchor","transform","version","viewBox","visibility","xmlns","xmlns:xlink"),w().allowedTags.push("desc","clippath","g"),function(e){function t(t,n){let o=this,i=ec(o.options.exporting,t),r=function(e){!1===i.fallbackToExportServer?i.error?i.error(i,e):el(28,!0):o.exportChart(i)};if(u().isMS&&o.styledMode&&!Z.inlineAllowlist.length&&Z.inlineAllowlist.push(/^blockSize/,/^border/,/^caretColor/,/^color/,/^columnRule/,/^columnRuleColor/,/^cssFloat/,/^cursor/,/^fill$/,/^fillOpacity/,/^font/,/^inlineSize/,/^length/,/^lineHeight/,/^opacity/,/^outline/,/^parentRule/,/^rx$/,/^ry$/,/^stroke/,/^textAlign/,/^textAnchor/,/^textDecoration/,/^transform/,/^vectorEffect/,/^visibility/,/^x$/,/^y$/),u().isMS&&("application/pdf"===i.type||o.container.getElementsByTagName("image").length&&"image/svg+xml"!==i.type)||"application/pdf"===i.type&&[].some.call(o.container.getElementsByTagName("image"),function(e){let t=e.getAttribute("href");return""!==t&&"string"==typeof t&&0!==t.indexOf("data:")})){r(Error("Image type not supported for this chart/browser."));return}o.getSVGForLocalExport(i,n||{},r,function(t){t.indexOf("<foreignObject")>-1&&"image/svg+xml"!==i.type&&(u().isMS||"application/pdf"===i.type)?r(Error("Image type not supported for charts with embedded HTML")):e.downloadSVGLocal(t,es({filename:o.getFilename()},i),r,()=>ea(o,"exportChartLocalSuccess"))})}function n(e,t){let n=en.getElementsByTagName("head")[0],o=en.createElement("script");o.type="text/javascript",o.src=e,o.onload=t,o.onerror=function(){el("Error loading script "+e)},n.appendChild(o)}function o(t,n,o,i){let r=this,l=e=>r.sanitizeSVG(e,d),s=()=>{u&&f===g&&i(l(h.innerHTML))},a=(e,t,n)=>{++f,n.imageElement.setAttributeNS("http://www.w3.org/1999/xlink","href",e),s()},c,h,d,p=null,u,g=0,f=0;r.unbindGetSVG=er(r,"getSVG",e=>{d=e.chartCopy.options,g=(u=(h=e.chartCopy.container.cloneNode(!0))&&h.getElementsByTagName("image")||[]).length}),r.getSVGForExport(t,n);try{if(!u||!u.length){i(l(h.innerHTML));return}for(let n=0;n<u.length;n++)(p=(c=u[n]).getAttributeNS("http://www.w3.org/1999/xlink","href"))?e.imageToDataUrl(p,"image/png",{imageElement:c},t.scale,a,o,o,o):(f++,c.parentNode.removeChild(c),n--,s())}catch(e){o(e)}r.unbindGetSVG()}function i(t,n,o,i,r,l,s,a,c){let h=new eo.Image,d,p=()=>{setTimeout(function(){let e,l=en.createElement("canvas"),a=l.getContext&&l.getContext("2d");try{if(a){l.height=h.height*i,l.width=h.width*i,a.drawImage(h,0,0,l.width,l.height);try{e=l.toDataURL(n),r(e,n,o,i)}catch(e){d(t,n,o,i)}}else s(t,n,o,i)}finally{c&&c(t,n,o,i)}},e.loadEventDeferDelay)},u=()=>{a(t,n,o,i),c&&c(t,n,o,i)};d=()=>{h=new eo.Image,d=l,h.crossOrigin="Anonymous",h.onload=p,h.onerror=u,h.src=t},h.onload=p,h.onerror=u,h.src=t}function r(t){let n=eo.navigator.userAgent,o=n.indexOf("WebKit")>-1&&0>n.indexOf("Chrome");try{if(!o&&-1===t.indexOf("<foreignObject"))return e.domurl.createObjectURL(new eo.Blob([t],{type:"image/svg+xml;charset-utf-16"}))}catch(e){}return"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent(t)}function l(e,t,n,o){let i=(Number(e.getAttribute("width"))+2*t)*n,r=(Number(e.getAttribute("height"))+2*t)*n,l=new eo.jspdf.jsPDF(r>i?"p":"l","pt",[i,r]);[].forEach.call(e.querySelectorAll('*[visibility="hidden"]'),function(e){e.parentNode.removeChild(e)});let s=e.querySelectorAll("linearGradient");for(let e=0;e<s.length;e++){let t=s[e].querySelectorAll("stop"),n=0;for(;n<t.length&&"0"===t[n].getAttribute("offset")&&"0"===t[n+1].getAttribute("offset");)t[n].remove(),n++}[].forEach.call(e.querySelectorAll("tspan"),e=>{"​"===e.textContent&&(e.textContent=" ",e.setAttribute("dx",-5))}),l.svg(e,{x:0,y:0,width:i,height:r,removeInvalid:!0}).then(()=>o(l.output("datauristring")))}e.CanVGRenderer={},e.domurl=eo.URL||eo.webkitURL||eo,e.loadEventDeferDelay=150*!!u().isMS,e.compose=function(e){let n=e.prototype;return n.exportChartLocal||(n.getSVGForLocalExport=o,n.exportChartLocal=t,ec(!0,ee.exporting,Q)),e},e.downloadSVGLocal=function(t,o,s,a){let c=en.createElement("div"),h=o.type||"image/png",d=(o.filename||"chart")+"."+("image/svg+xml"===h?"svg":h.split("/")[1]),p=o.scale||1,u,g,f,m=o.libURL||ee.exporting.libURL,x=!0,y=o.pdfFont;m="/"!==m.slice(-1)?m+"/":m;let b=(e,t)=>{let n,o,i=(e,t)=>{eo.jspdf.jsPDF.API.events.push(["initialized",function(){this.addFileToVFS(e,t),this.addFont(e,"HighchartsFont",e),this.getFontList().HighchartsFont||this.setFont("HighchartsFont")}])};y&&(o=e.textContent||"",!/[^\u0000-\u007F\u200B]+/.test(o))&&(y=void 0);let r=["normal","italic","bold","bolditalic"],l=()=>{let e=r.shift();if(!e)return t();let o=y&&y[e];o?ei({url:o,responseType:"blob",success:(t,o)=>{let r=new FileReader;r.onloadend=function(){if("string"==typeof this.result){let t=this.result.split(",")[1];i(e,t),"normal"===e&&(n=t)}l()},r.readAsDataURL(o.response)},error:l}):(n&&i(e,n),l())};l()},v=()=>{let e,n;w().setElementHTML(c,t);let o=c.getElementsByTagName("text"),i=function(e,t){let n=e;for(;n&&n!==c;){if(n.style[t]){let o=n.style[t];"fontSize"===t&&/em$/.test(o)&&(o=Math.round(16*parseFloat(o))+"px"),e.style[t]=o;break}n=n.parentNode}};[].forEach.call(o,function(t){for(["fontFamily","fontSize"].forEach(e=>{i(t,e)}),t.style.fontFamily=y&&y.normal?"HighchartsFont":String(t.style.fontFamily&&t.style.fontFamily.split(" ").splice(-1)),e=t.getElementsByTagName("title"),[].forEach.call(e,function(e){t.removeChild(e)}),n=t.getElementsByClassName("highcharts-text-outline");n.length>0;){let e=n[0];e.parentNode&&e.parentNode.removeChild(e)}});let r=c.querySelector("svg");r&&b(r,()=>{l(r,0,p,e=>{try{et(e,d),a&&a()}catch(e){s(e)}})})};if("image/svg+xml"===h)try{void 0!==eo.MSBlobBuilder?((g=new eo.MSBlobBuilder).append(t),u=g.getBlob("image/svg+xml")):u=r(t),et(u,d),a&&a()}catch(e){s(e)}else"application/pdf"===h?eo.jspdf&&eo.jspdf.jsPDF?v():(x=!0,n(m+"jspdf.js",function(){n(m+"svg2pdf.js",v)})):(u=r(t),f=function(){try{e.domurl.revokeObjectURL(u)}catch(e){}},i(u,h,{},p,function(e){try{et(e,d),a&&a()}catch(e){s(e)}},function(){if(t.length>1e8)throw Error("Input too long");let e=en.createElement("canvas"),o=e.getContext("2d"),i=t.match(/^<svg[^>]*\s{,1000}width\s{,1000}=\s{,1000}\"?(\d+)\"?[^>]*>/),r=t.match(/^<svg[^>]*\s{0,1000}height\s{,1000}=\s{,1000}\"?(\d+)\"?[^>]*>/);if(o&&i&&r){let l=+i[1]*p,c=+r[1]*p,u=()=>{eo.canvg.Canvg.fromString(o,t).start();try{et(eo.navigator.msSaveOrOpenBlob?e.msToBlob():e.toDataURL(h),d),a&&a()}catch(e){s(e)}finally{f()}};e.width=l,e.height=c,eo.canvg?u():(x=!0,n(m+"canvg.js",u))}},s,s,function(){x&&f()}))},e.getScript=n,e.imageToDataUrl=i,e.svgToDataUrl=r,e.svgToPdf=l}(s||(s={}));let eh=s,ed=u();ed.dataURLtoBlob=ed.dataURLtoBlob||b.dataURLtoBlob,ed.downloadSVGLocal=eh.downloadSVGLocal,ed.downloadURL=ed.downloadURL||b.downloadURL,eh.compose(ed.Chart);let ep=u();return d.default})());