{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module modules/arc-diagram\n * @requires highcharts/modules/sankey\n *\n * Arc diagram module\n *\n * (c) 2021 Piotr Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SVGRenderer\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/arc-diagram\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Series\"],amd1[\"SVGRenderer\"],amd1[\"SVGElement\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/arc-diagram\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"SVGRenderer\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"SVGRenderer\"], root[\"Highcharts\"][\"SVGElement\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__540__, __WEBPACK_EXTERNAL_MODULE__28__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ arc_diagram_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/NodesComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto, prototype: { pointClass: { prototype: pointProto } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined, extend, find, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar NodesComposition;\n(function (NodesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(PointClass, SeriesClass) {\n        const pointProto = PointClass.prototype, seriesProto = SeriesClass.prototype;\n        pointProto.setNodeState = setNodeState;\n        pointProto.setState = setNodeState;\n        pointProto.update = updateNode;\n        seriesProto.destroy = destroy;\n        seriesProto.setData = setData;\n        return SeriesClass;\n    }\n    NodesComposition.compose = compose;\n    /**\n     * Create a single node that holds information on incoming and outgoing\n     * links.\n     * @private\n     */\n    function createNode(id) {\n        const PointClass = this.pointClass, findById = (nodes, id) => find(nodes, (node) => node.id === id);\n        let node = findById(this.nodes, id), options;\n        if (!node) {\n            options = this.options.nodes && findById(this.options.nodes, id);\n            const newNode = new PointClass(this, extend({\n                className: 'highcharts-node',\n                isNode: true,\n                id: id,\n                y: 1 // Pass isNull test\n            }, options));\n            newNode.linksTo = [];\n            newNode.linksFrom = [];\n            /**\n             * Return the largest sum of either the incoming or outgoing links.\n             * @private\n             */\n            newNode.getSum = function () {\n                let sumTo = 0, sumFrom = 0;\n                newNode.linksTo.forEach((link) => {\n                    sumTo += link.weight || 0;\n                });\n                newNode.linksFrom.forEach((link) => {\n                    sumFrom += link.weight || 0;\n                });\n                return Math.max(sumTo, sumFrom);\n            };\n            /**\n             * Get the offset in weight values of a point/link.\n             * @private\n             */\n            newNode.offset = function (point, coll) {\n                let offset = 0;\n                for (let i = 0; i < newNode[coll].length; i++) {\n                    if (newNode[coll][i] === point) {\n                        return offset;\n                    }\n                    offset += newNode[coll][i].weight;\n                }\n            };\n            // Return true if the node has a shape, otherwise all links are\n            // outgoing.\n            newNode.hasShape = function () {\n                let outgoing = 0;\n                newNode.linksTo.forEach((link) => {\n                    if (link.outgoing) {\n                        outgoing++;\n                    }\n                });\n                return (!newNode.linksTo.length ||\n                    outgoing !== newNode.linksTo.length);\n            };\n            newNode.index = this.nodes.push(newNode) - 1;\n            node = newNode;\n        }\n        node.formatPrefix = 'node';\n        // For use in formats\n        node.name = node.name || node.options.id || '';\n        // Mass is used in networkgraph:\n        node.mass = pick(\n        // Node:\n        node.options.mass, node.options.marker && node.options.marker.radius, \n        // Series:\n        this.options.marker && this.options.marker.radius, \n        // Default:\n        4);\n        return node;\n    }\n    NodesComposition.createNode = createNode;\n    /**\n     * Destroy all nodes and links.\n     * @private\n     */\n    function destroy() {\n        // Nodes must also be destroyed (#8682, #9300)\n        this.data = []\n            .concat(this.points || [], this.nodes);\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    NodesComposition.destroy = destroy;\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects but\n     * pushed to the this.nodes array.\n     * @private\n     */\n    function generatePoints() {\n        const chart = this.chart, nodeLookup = {};\n        seriesProto.generatePoints.call(this);\n        if (!this.nodes) {\n            this.nodes = []; // List of Point-like node items\n        }\n        this.colorCounter = 0;\n        // Reset links from previous run\n        this.nodes.forEach((node) => {\n            node.linksFrom.length = 0;\n            node.linksTo.length = 0;\n            node.level = node.options.level;\n        });\n        // Create the node list and set up links\n        this.points.forEach((point) => {\n            if (defined(point.from)) {\n                if (!nodeLookup[point.from]) {\n                    nodeLookup[point.from] = this.createNode(point.from);\n                }\n                nodeLookup[point.from].linksFrom.push(point);\n                point.fromNode = nodeLookup[point.from];\n                // Point color defaults to the fromNode's color\n                if (chart.styledMode) {\n                    point.colorIndex = pick(point.options.colorIndex, nodeLookup[point.from].colorIndex);\n                }\n                else {\n                    point.color =\n                        point.options.color || nodeLookup[point.from].color;\n                }\n            }\n            if (defined(point.to)) {\n                if (!nodeLookup[point.to]) {\n                    nodeLookup[point.to] = this.createNode(point.to);\n                }\n                nodeLookup[point.to].linksTo.push(point);\n                point.toNode = nodeLookup[point.to];\n            }\n            point.name = point.name || point.id; // For use in formats\n        }, this);\n        // Store lookup table for later use\n        this.nodeLookup = nodeLookup;\n    }\n    NodesComposition.generatePoints = generatePoints;\n    /**\n     * Destroy all nodes on setting new data\n     * @private\n     */\n    function setData() {\n        if (this.nodes) {\n            this.nodes.forEach((node) => {\n                node.destroy();\n            });\n            this.nodes.length = 0;\n        }\n        seriesProto.setData.apply(this, arguments);\n    }\n    /**\n     * When hovering node, highlight all connected links. When hovering a link,\n     * highlight all connected nodes.\n     * @private\n     */\n    function setNodeState(state) {\n        const args = arguments, others = this.isNode ? this.linksTo.concat(this.linksFrom) :\n            [this.fromNode, this.toNode];\n        if (state !== 'select') {\n            others.forEach((linkOrNode) => {\n                if (linkOrNode && linkOrNode.series) {\n                    pointProto.setState.apply(linkOrNode, args);\n                    if (!linkOrNode.isNode) {\n                        if (linkOrNode.fromNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.fromNode, args);\n                        }\n                        if (linkOrNode.toNode && linkOrNode.toNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.toNode, args);\n                        }\n                    }\n                }\n            });\n        }\n        pointProto.setState.apply(this, args);\n    }\n    NodesComposition.setNodeState = setNodeState;\n    /**\n     * When updating a node, don't update `series.options.data`, but\n     * `series.options.nodes`\n     * @private\n     */\n    function updateNode(options, redraw, animation, runEvent) {\n        const nodes = this.series.options.nodes, data = this.series.options.data, dataLength = data?.length || 0, linkConfig = data?.[this.index];\n        pointProto.update.call(this, options, this.isNode ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        if (this.isNode) {\n            // `this.index` refers to `series.nodes`, not `options.nodes` array\n            const nodeIndex = (nodes || [])\n                .reduce(// Array.findIndex needs a polyfill\n            (prevIndex, n, index) => (this.id === n.id ? index : prevIndex), -1), \n            // Merge old config with new config. New config is stored in\n            // options.data, because of default logic in point.update()\n            nodeConfig = merge(nodes && nodes[nodeIndex] || {}, data?.[this.index] || {});\n            // Restore link config\n            if (data) {\n                if (linkConfig) {\n                    data[this.index] = linkConfig;\n                }\n                else {\n                    // Remove node from config if there's more nodes than links\n                    data.length = dataLength;\n                }\n            }\n            // Set node config\n            if (nodes) {\n                if (nodeIndex >= 0) {\n                    nodes[nodeIndex] = nodeConfig;\n                }\n                else {\n                    nodes.push(nodeConfig);\n                }\n            }\n            else {\n                this.series.options.nodes = [nodeConfig];\n            }\n            if (pick(redraw, true)) {\n                this.series.chart.redraw(animation);\n            }\n        }\n    }\n    NodesComposition.updateNode = updateNode;\n})(NodesComposition || (NodesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_NodesComposition = (NodesComposition);\n\n;// ./code/es-modules/Series/ArcDiagram/ArcDiagramPoint.js\n/* *\n *\n *  Arc diagram module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { seriesTypes: { sankey: { prototype: { pointClass: SankeyPoint } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { extend: ArcDiagramPoint_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass ArcDiagramPoint extends SankeyPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    isValid() {\n        // No null points here\n        return true;\n    }\n}\nArcDiagramPoint_extend(ArcDiagramPoint.prototype, {\n    setState: Series_NodesComposition.setNodeState\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ArcDiagram_ArcDiagramPoint = (ArcDiagramPoint);\n\n;// ./code/es-modules/Series/ArcDiagram/ArcDiagramSeriesDefaults.js\n/* *\n *\n *  Arc diagram module\n *\n *  (c) 2021 Piotr Madej, Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n *  Arc diagram series is a chart drawing style in which\n *  the vertices of the chart are positioned along a line\n *  on the Euclidean plane and the edges are drawn as a semicircle\n *  in one of the two half-planes delimited by the line,\n *  or as smooth curves formed by sequences of semicircles.\n *\n * @sample highcharts/demo/arc-diagram/\n *         Arc Diagram\n *\n * @extends      plotOptions.sankey\n * @since 10.0.0\n * @product      highcharts\n * @requires     modules/arc-diagram\n * @exclude      curveFactor, connectEnds, connectNulls, colorAxis, colorKey,\n *               dataSorting, dragDrop, getExtremesFromAll, legendSymbolColor,\n *               nodeAlignment, nodePadding, centerInCategory, pointInterval,\n *               pointIntervalUnit, pointPlacement, pointStart, relativeXValue,\n *               softThreshold, stack, stacking, step, xAxis, yAxis\n * @optionparent plotOptions.arcdiagram\n */\nconst ArcDiagramSeriesDefaults = {\n    /**\n     * The option to center links rather than position them one after\n     * another\n     *\n     * @type    {boolean}\n     * @since 10.0.0\n     * @default false\n     * @product highcharts\n     */\n    centeredLinks: false,\n    /**\n     * Whether nodes with different values should have the same size. If set\n     * to true, all nodes are calculated based on the `nodePadding` and\n     * current `plotArea`. It is possible to override it using the\n     * `marker.radius` option.\n     *\n     * @type    {boolean}\n     * @since 10.0.0\n     * @default false\n     * @product highcharts\n     */\n    equalNodes: false,\n    /**\n     * Options for the data labels appearing on top of the nodes and links.\n     * For arc diagram charts, data labels are visible for the nodes by\n     * default, but hidden for links. This is controlled by modifying the\n     * `nodeFormat`, and the `format` that applies to links and is an empty\n     * string by default.\n     *\n     * @declare Highcharts.SeriesArcDiagramDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label that\n         * follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option. Setting\n         * `useHTML` to true will disable this option.\n         *\n         * @extends plotOptions.networkgraph.dataLabels.linkTextPath\n         * @since 10.0.0\n         */\n        linkTextPath: {\n            /**\n             * @type    {Highcharts.SVGAttributes}\n             * @default {\"startOffset\":\"25%\"}\n             */\n            attributes: {\n                /**\n                 * @ignore-option\n                 */\n                startOffset: '25%'\n            }\n        }\n    },\n    /**\n     * The radius of the link arc. If not set, series renders a semi-circle\n     * between the nodes, except when overflowing the edge of the plot area,\n     * in which case an arc touching the edge is rendered. If `linkRadius`\n     * is set, an arc extending to the given value is rendered.\n     *\n     * @type    {number}\n     * @since 10.0.0\n     * @default undefined\n     * @product highcharts\n     * @apioption series.arcdiagram.linkRadius\n     */\n    /**\n     * The global link weight, in pixels. If not set, width is calculated\n     * per link, depending on the weight value.\n     *\n     * @sample highcharts/series-arcdiagram/link-weight\n     *         Link weight\n     *\n     * @type    {number}\n     * @since 10.0.0\n     * @default undefined\n     * @product highcharts\n     * @apioption series.arcdiagram.linkWeight\n     */\n    /**\n     * @extends   plotOptions.series.marker\n     * @excluding enabled, enabledThreshold, height, width\n     */\n    marker: {\n        fillOpacity: 1,\n        lineWidth: 0,\n        states: {},\n        symbol: 'circle'\n    },\n    /**\n     * The offset of an arc diagram nodes column in relation to the\n     * `plotArea`. The offset equal to 50% places nodes in the center of a\n     * chart. By default the series is placed so that the biggest node is\n     * touching the bottom border of the `plotArea`.\n     *\n     * @type    {string}\n     * @since 10.0.0\n     * @default '100%'\n     * @product highcharts\n     * @apioption series.arcdiagram.offset\n     */\n    offset: '100%',\n    /**\n     * Whether the series should be placed on the other side of the\n     * `plotArea`.\n     *\n     * @type    {boolean}\n     * @since 10.0.0\n     * @default false\n     * @product highcharts\n     */\n    reversed: false\n};\n/**\n * An `arcdiagram` series. If the [type](#series.arcdiagram.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.arcdiagram\n * @exclude   dataSorting, boostThreshold, boostBlending, curveFactor,\n *            connectEnds, connectNulls, colorAxis, colorKey, dataSorting,\n *            dragDrop, getExtremesFromAll, nodePadding, centerInCategory,\n *            pointInterval, pointIntervalUnit, pointPlacement,\n *            pointStart, relativeXValue, softThreshold, stack,\n *            stacking, step, xAxis, yAxis\n * @product   highcharts\n * @requires  modules/sankey\n * @requires  modules/arc-diagram\n * @apioption series.arcdiagram\n */\n/**\n * @extends   plotOptions.series.marker\n * @excluding enabled, enabledThreshold, height, radius, width\n * @apioption series.arcdiagram.marker\n */\n/**\n * @type      {Highcharts.SeriesArcDiagramDataLabelsOptionsObject|Array<Highcharts.SeriesArcDiagramDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.arcdiagram.data.dataLabels\n */\n/**\n * A collection of options for the individual nodes. The nodes in an arc diagram\n * are auto-generated instances of `Highcharts.Point`, but options can be\n * applied here and linked by the `id`.\n *\n * @extends   series.sankey.nodes\n * @type      {Array<*>}\n * @product   highcharts\n * @excluding column, level\n * @apioption series.arcdiagram.nodes\n */\n/**\n * Individual data label for each node. The options are the same as the ones for\n * [series.arcdiagram.dataLabels](#series.arcdiagram.dataLabels).\n *\n * @type\n * {Highcharts.SeriesArcDiagramDataLabelsOptionsObject|Array<Highcharts.SeriesArcDiagramDataLabelsOptionsObject>}\n *\n * @apioption series.arcdiagram.nodes.dataLabels\n */\n/**\n * Individual data label for each node. The options are the same as the ones for\n * [series.arcdiagram.dataLabels](#series.arcdiagram.dataLabels).\n *\n * @type\n * {Highcharts.SeriesArcDiagramDataLabelsOptionsObject|Array<Highcharts.SeriesArcDiagramDataLabelsOptionsObject>}\n *\n */\n/**\n * An array of data points for the series. For the `arcdiagram` series type,\n * points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a few\n * settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.area.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2',\n *         weight: 2\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3',\n *         weight: 5\n *     }]\n *  ```\n *\n * @type      {Array<*>}\n * @extends   series.sankey.data\n * @product   highcharts\n * @excluding outgoing, dataLabels\n * @apioption series.arcdiagram.data\n */\n''; // Adds doclets above to the transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ArcDiagram_ArcDiagramSeriesDefaults = (ArcDiagramSeriesDefaults);\n\n;// ./code/es-modules/Series/Sankey/SankeyColumnComposition.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: SankeyColumnComposition_defined, getAlignFactor, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar SankeyColumnComposition;\n(function (SankeyColumnComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * SankeyColumn Composition\n     * @private\n     * @function Highcharts.SankeyColumn#compose\n     *\n     * @param {Array<SankeyPoint>} points\n     * The array of nodes\n     * @param {SankeySeries} series\n     * Series connected to column\n     * @return {ArrayComposition} SankeyColumnArray\n     */\n    function compose(points, series) {\n        const sankeyColumnArray = points;\n        sankeyColumnArray.sankeyColumn =\n            new SankeyColumnAdditions(sankeyColumnArray, series);\n        return sankeyColumnArray;\n    }\n    SankeyColumnComposition.compose = compose;\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    class SankeyColumnAdditions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(points, series) {\n            this.points = points;\n            this.series = series;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Calculate translation factor used in column and nodes distribution\n         * @private\n         * @function Highcharts.SankeyColumn#getTranslationFactor\n         *\n         * @param {SankeySeries} series\n         * The Series\n         * @return {number} TranslationFactor\n         * Translation Factor\n         */\n        getTranslationFactor(series) {\n            const column = this.points, nodes = column.slice(), chart = series.chart, minLinkWidth = series.options.minLinkWidth || 0;\n            let skipPoint, factor = 0, i, remainingHeight = ((chart.plotSizeY || 0) -\n                (series.options.borderWidth || 0) -\n                (column.length - 1) * series.nodePadding);\n            // Because the minLinkWidth option doesn't obey the direct\n            // translation, we need to run translation iteratively, check\n            // node heights, remove those nodes affected by minLinkWidth,\n            // check again, etc.\n            while (column.length) {\n                factor = remainingHeight / column.sankeyColumn.sum();\n                skipPoint = false;\n                i = column.length;\n                while (i--) {\n                    if (column[i].getSum() * factor < minLinkWidth) {\n                        column.splice(i, 1);\n                        remainingHeight =\n                            Math.max(0, remainingHeight - minLinkWidth);\n                        skipPoint = true;\n                    }\n                }\n                if (!skipPoint) {\n                    break;\n                }\n            }\n            // Re-insert original nodes\n            column.length = 0;\n            for (const node of nodes) {\n                column.push(node);\n            }\n            return factor;\n        }\n        /**\n         * Get the top position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} top\n         * The top position of the column\n         */\n        top(factor) {\n            const series = this.series, nodePadding = series.nodePadding, height = this.points.reduce((height, node) => {\n                if (height > 0) {\n                    height += nodePadding;\n                }\n                const nodeHeight = Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                height += nodeHeight;\n                return height;\n            }, 0);\n            // Node alignment option handling #19096\n            return getAlignFactor(series.options.nodeAlignment || 'center') * ((series.chart.plotSizeY || 0) - height);\n        }\n        /**\n         * Get the left position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} left\n         * The left position of the column\n         */\n        left(factor) {\n            const series = this.series, chart = series.chart, equalNodes = series.options.equalNodes, maxNodesLength = (chart.inverted ? chart.plotHeight : chart.plotWidth), nodePadding = series.nodePadding, width = this.points.reduce((width, node) => {\n                if (width > 0) {\n                    width += nodePadding;\n                }\n                const nodeWidth = equalNodes ?\n                    maxNodesLength / node.series.nodes.length -\n                        nodePadding :\n                    Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                width += nodeWidth;\n                return width;\n            }, 0);\n            return ((chart.plotSizeX || 0) - Math.round(width)) / 2;\n        }\n        /**\n         * Calculate sum of all nodes inside specific column\n         * @private\n         * @function Highcharts.SankeyColumn#sum\n         *\n         * @param {ArrayComposition} this\n         * Sankey Column Array\n         *\n         * @return {number} sum\n         * Sum of all nodes inside column\n         */\n        sum() {\n            return this.points.reduce((sum, node) => (sum + node.getSum()), 0);\n        }\n        /**\n         * Get the offset in pixels of a node inside the column\n         * @private\n         * @function Highcharts.SankeyColumn#offset\n         *\n         * @param {SankeyPoint} node\n         * Sankey node\n         * @param {number} factor\n         * Translation Factor\n         * @return {number} offset\n         * Offset of a node inside column\n         */\n        offset(node, factor) {\n            const column = this.points, series = this.series, nodePadding = series.nodePadding;\n            let offset = 0, totalNodeOffset;\n            if (series.is('organization') && node.hangsFrom) {\n                return {\n                    absoluteTop: node.hangsFrom.nodeY\n                };\n            }\n            for (let i = 0; i < column.length; i++) {\n                const sum = column[i].getSum();\n                const height = Math.max(sum * factor, series.options.minLinkWidth || 0);\n                const directionOffset = node.options[series.chart.inverted ?\n                    'offsetHorizontal' :\n                    'offsetVertical'], optionOffset = node.options.offset || 0;\n                if (sum) {\n                    totalNodeOffset = height + nodePadding;\n                }\n                else {\n                    // If node sum equals 0 nodePadding is missed #12453\n                    totalNodeOffset = 0;\n                }\n                if (column[i] === node) {\n                    return {\n                        relativeTop: offset + (SankeyColumnComposition_defined(directionOffset) ?\n                            // `directionOffset` is a percent of the node\n                            // height\n                            relativeLength(directionOffset, height) :\n                            relativeLength(optionOffset, totalNodeOffset))\n                    };\n                }\n                offset += totalNodeOffset;\n            }\n        }\n    }\n    SankeyColumnComposition.SankeyColumnAdditions = SankeyColumnAdditions;\n})(SankeyColumnComposition || (SankeyColumnComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeyColumnComposition = (SankeyColumnComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/ArcDiagram/ArcDiagramSeries.js\n/* *\n *\n *  Arc diagram module\n *\n *  (c) 2021 Piotr Madej, Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\n\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\nconst { prototype: { symbols } } = (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default());\nconst { seriesTypes: { column: ColumnSeries, sankey: SankeySeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\nconst { crisp, extend: ArcDiagramSeries_extend, merge: ArcDiagramSeries_merge, pick: ArcDiagramSeries_pick, relativeLength: ArcDiagramSeries_relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.arcdiagram\n *\n * @augments Highcharts.seriesTypes.sankey\n */\nclass ArcDiagramSeries extends SankeySeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create node columns by analyzing the nodes and the relations between\n     * incoming and outgoing links.\n     * @private\n     */\n    createNodeColumns() {\n        const series = this, chart = series.chart, \n        // Column needs casting, to much methods required at the same time\n        column = Sankey_SankeyColumnComposition.compose([], series);\n        column.sankeyColumn.maxLength = chart.inverted ?\n            chart.plotHeight : chart.plotWidth;\n        // Get the translation factor needed for each column to fill up the plot\n        // height\n        column.sankeyColumn.getTranslationFactor = (series) => {\n            const nodes = column.slice(), minLinkWidth = this.options.minLinkWidth || 0;\n            let skipPoint, factor = 0, i, radius, maxRadius = 0, scale = 1, additionalSpace = 0, remainingWidth = (chart.plotSizeX || 0) -\n                (series.options.marker &&\n                    series.options.marker.lineWidth || 0) -\n                (column.length - 1) *\n                    series.nodePadding;\n            // Because the minLinkWidth option doesn't obey the direct\n            // translation, we need to run translation iteratively, check node\n            // heights, remove those nodes affected by minLinkWidth, check\n            // again, etc.\n            while (column.length) {\n                factor = remainingWidth / column.sankeyColumn.sum();\n                skipPoint = false;\n                i = column.length;\n                while (i--) {\n                    radius = (column[i].getSum()) * factor * scale;\n                    const plotArea = Math.min(chart.plotHeight, chart.plotWidth);\n                    if (radius > plotArea) {\n                        scale = Math.min(plotArea / radius, scale);\n                    }\n                    else if (radius < minLinkWidth) {\n                        column.splice(i, 1);\n                        remainingWidth -= minLinkWidth;\n                        radius = minLinkWidth;\n                        skipPoint = true;\n                    }\n                    additionalSpace += radius * (1 - scale) / 2;\n                    maxRadius = Math.max(maxRadius, radius);\n                }\n                if (!skipPoint) {\n                    break;\n                }\n            }\n            // Re-insert original nodes\n            column.length = 0;\n            nodes.forEach((node) => {\n                node.scale = scale;\n                column.push(node);\n            });\n            column.sankeyColumn.maxRadius = maxRadius;\n            column.sankeyColumn.scale = scale;\n            column.sankeyColumn.additionalSpace = additionalSpace;\n            return factor;\n        };\n        column.sankeyColumn.offset = function (node, factor) {\n            const equalNodes = node.series.options.equalNodes, nodePadding = series.nodePadding, maxRadius = Math.min(chart.plotWidth, chart.plotHeight, (column.sankeyColumn.maxLength || 0) /\n                series.nodes.length - nodePadding);\n            let offset = column.sankeyColumn.additionalSpace || 0, totalNodeOffset;\n            for (let i = 0; i < column.length; i++) {\n                const sum = column[i].getSum() *\n                    (column.sankeyColumn.scale || 0);\n                const width = equalNodes ?\n                    maxRadius :\n                    Math.max(sum * factor, series.options.minLinkWidth || 0);\n                if (sum) {\n                    totalNodeOffset = width + nodePadding;\n                }\n                else {\n                    // If node sum equals 0 nodePadding is missed #12453\n                    totalNodeOffset = 0;\n                }\n                if (column[i] === node) {\n                    return {\n                        relativeLeft: offset + ArcDiagramSeries_relativeLength(node.options.offset || 0, totalNodeOffset)\n                    };\n                }\n                offset += totalNodeOffset;\n            }\n        };\n        // Add nodes directly to the column right after it's creation\n        series.nodes.forEach(function (node) {\n            node.column = 0;\n            column.push(node);\n        });\n        return [column];\n    }\n    /**\n     * Run translation operations for one link.\n     * @private\n     */\n    translateLink(point) {\n        const series = this, fromNode = point.fromNode, toNode = point.toNode, chart = this.chart, translationFactor = series.translationFactor, pointOptions = point.options, seriesOptions = series.options, linkWeight = ArcDiagramSeries_pick(pointOptions.linkWeight, seriesOptions.linkWeight, Math.max((point.weight || 0) *\n            translationFactor *\n            fromNode.scale, (series.options.minLinkWidth || 0))), centeredLinks = point.series.options.centeredLinks, nodeTop = fromNode.nodeY;\n        const getX = (node, fromOrTo) => {\n            const linkLeft = ((node.offset(point, fromOrTo) || 0) *\n                translationFactor);\n            const x = Math.min(node.nodeX + linkLeft, \n            // Prevent links from spilling below the node (#12014)\n            node.nodeX + (node.shapeArgs && node.shapeArgs.height || 0) - linkWeight);\n            return x;\n        };\n        let fromX = centeredLinks ?\n            fromNode.nodeX +\n                ((fromNode.shapeArgs.height || 0) - linkWeight) / 2 :\n            getX(fromNode, 'linksFrom'), toX = centeredLinks ? toNode.nodeX +\n            ((toNode.shapeArgs.height || 0) - linkWeight) / 2 :\n            getX(toNode, 'linksTo'), bottom = nodeTop;\n        if (fromX > toX) {\n            [fromX, toX] = [toX, fromX];\n        }\n        if (seriesOptions.reversed) {\n            [fromX, toX] = [toX, fromX];\n            bottom = (chart.plotSizeY || 0) - bottom;\n        }\n        point.shapeType = 'path';\n        point.linkBase = [\n            fromX,\n            fromX + linkWeight,\n            toX,\n            toX + linkWeight\n        ];\n        const linkRadius = ((toX + linkWeight - fromX) / Math.abs(toX + linkWeight - fromX)) * ArcDiagramSeries_pick(seriesOptions.linkRadius, Math.min(Math.abs(toX + linkWeight - fromX) / 2, fromNode.nodeY - Math.abs(linkWeight)));\n        point.shapeArgs = {\n            d: [\n                ['M', fromX, bottom],\n                [\n                    'A',\n                    (toX + linkWeight - fromX) / 2,\n                    linkRadius,\n                    0,\n                    0,\n                    1,\n                    toX + linkWeight,\n                    bottom\n                ],\n                ['L', toX, bottom],\n                [\n                    'A',\n                    (toX - fromX - linkWeight) / 2,\n                    linkRadius - linkWeight,\n                    0,\n                    0,\n                    0,\n                    fromX + linkWeight,\n                    bottom\n                ],\n                ['Z']\n            ]\n        };\n        point.dlBox = {\n            x: fromX + (toX - fromX) / 2,\n            y: bottom - linkRadius,\n            height: linkWeight,\n            width: 0\n        };\n        // And set the tooltip anchor in the middle\n        point.tooltipPos = chart.inverted ? [\n            (chart.plotSizeY || 0) - point.dlBox.y - linkWeight / 2,\n            (chart.plotSizeX || 0) - point.dlBox.x\n        ] : [\n            point.dlBox.x,\n            point.dlBox.y + linkWeight / 2\n        ];\n        // Pass test in drawPoints\n        point.y = point.plotY = 1;\n        point.x = point.plotX = 1;\n        if (!point.color) {\n            point.color = fromNode.color;\n        }\n    }\n    /**\n     * Run translation operations for one node.\n     * @private\n     */\n    translateNode(node, column) {\n        const series = this, translationFactor = series.translationFactor, chart = series.chart, maxNodesLength = chart.inverted ?\n            chart.plotWidth : chart.plotHeight, options = series.options, maxRadius = Math.min(chart.plotWidth, chart.plotHeight, maxNodesLength / node.series.nodes.length - this.nodePadding), sum = node.getSum() * (column.sankeyColumn.scale || 0), equalNodes = options.equalNodes, nodeHeight = equalNodes ?\n            maxRadius :\n            Math.max(sum * translationFactor, this.options.minLinkWidth || 0), lineWidth = options.marker?.lineWidth || 0, nodeOffset = column.sankeyColumn.offset(node, translationFactor), fromNodeLeft = crisp(ArcDiagramSeries_pick(nodeOffset && nodeOffset.absoluteLeft, ((column.sankeyColumn.left(translationFactor) || 0) +\n            (nodeOffset && nodeOffset.relativeLeft || 0))), lineWidth), markerOptions = ArcDiagramSeries_merge(options.marker, node.options.marker), symbol = markerOptions.symbol, markerRadius = markerOptions.radius, top = parseInt(options.offset, 10) *\n            ((chart.inverted ?\n                chart.plotWidth : chart.plotHeight) - (crisp(this.colDistance * (node.column || 0) +\n                (markerOptions.lineWidth || 0) / 2, lineWidth) +\n                (column.sankeyColumn.scale || 0) *\n                    (column.sankeyColumn.maxRadius || 0) / 2)) / 100;\n        node.sum = sum;\n        // If node sum is 0, don’t render the rect #12453\n        if (sum) {\n            // Draw the node\n            node.nodeX = fromNodeLeft;\n            node.nodeY = top;\n            const x = fromNodeLeft, width = node.options.width || options.width || nodeHeight, height = node.options.height || options.height || nodeHeight;\n            let y = top;\n            if (options.reversed) {\n                y = (chart.plotSizeY || 0) - top;\n                if (chart.inverted) {\n                    y = (chart.plotSizeY || 0) - top;\n                }\n            }\n            if (this.mapOptionsToLevel) {\n                // Calculate data label options for the point\n                node.dlOptions = SankeySeries.getDLOptions({\n                    level: this.mapOptionsToLevel[node.level],\n                    optionsPoint: node.options\n                });\n            }\n            // Pass test in drawPoints\n            node.plotX = 1;\n            node.plotY = 1;\n            // Set the anchor position for tooltips\n            node.tooltipPos = chart.inverted ? [\n                (chart.plotSizeY || 0) - y - height / 2,\n                (chart.plotSizeX || 0) - x - width / 2\n            ] : [\n                x + width / 2,\n                y + height / 2\n            ];\n            node.shapeType = 'path';\n            node.shapeArgs = {\n                d: symbols[symbol || 'circle'](x, y - (markerRadius || height) / 2, markerRadius || width, markerRadius || height),\n                width: markerRadius || width,\n                height: markerRadius || height\n            };\n            node.dlBox = {\n                x: x + width / 2,\n                y: y,\n                height: 0,\n                width: 0\n            };\n        }\n        else {\n            node.dlOptions = {\n                enabled: false\n            };\n        }\n    }\n    // Networkgraph has two separate collecions of nodes and lines, render\n    // dataLabels for both sets:\n    drawDataLabels() {\n        if (this.options.dataLabels) {\n            const textPath = this.options.dataLabels.textPath;\n            // Render node labels:\n            ColumnSeries.prototype.drawDataLabels.call(this, this.nodes);\n            // Render link labels:\n            this.options.dataLabels.textPath =\n                this.options.dataLabels.linkTextPath;\n            ColumnSeries.prototype.drawDataLabels.call(this, this.data);\n            // Restore nodes\n            this.options.dataLabels.textPath = textPath;\n        }\n    }\n    pointAttribs(point, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    state) {\n        if (point && point.isNode) {\n            const { ...attrs } = highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default().prototype.pointAttribs\n                .apply(this, arguments);\n            return attrs;\n        }\n        return super.pointAttribs.apply(this, arguments);\n    }\n    markerAttribs(point) {\n        if (point.isNode) {\n            return super.markerAttribs.apply(this, arguments);\n        }\n        return {};\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nArcDiagramSeries.defaultOptions = ArcDiagramSeries_merge(SankeySeries.defaultOptions, ArcDiagram_ArcDiagramSeriesDefaults);\nArcDiagramSeries_extend(ArcDiagramSeries.prototype, {\n    orderNodes: false\n});\nArcDiagramSeries.prototype.pointClass = ArcDiagram_ArcDiagramPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('arcdiagram', ArcDiagramSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ArcDiagram_ArcDiagramSeries = ((/* unused pure expression or super */ null && (ArcDiagramSeries)));\n\n;// ./code/es-modules/masters/modules/arc-diagram.js\n\n\n\n\n/* harmony default export */ const arc_diagram_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__540__", "__WEBPACK_EXTERNAL_MODULE__28__", "NodesComposition", "SankeyColumnComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "arc_diagram_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "series", "seriesProto", "pointClass", "pointProto", "defined", "extend", "find", "merge", "pick", "destroy", "data", "concat", "points", "nodes", "apply", "arguments", "setData", "for<PERSON>ach", "node", "length", "setNodeState", "state", "args", "others", "isNode", "linksTo", "linksFrom", "fromNode", "toNode", "linkOrNode", "setState", "graphic", "updateNode", "options", "redraw", "animation", "runEvent", "dataLength", "linkConfig", "index", "update", "nodeIndex", "reduce", "prevIndex", "id", "nodeConfig", "push", "chart", "compose", "PointClass", "SeriesClass", "createNode", "findById", "newNode", "className", "y", "getSum", "sumTo", "sumFrom", "link", "weight", "Math", "max", "offset", "point", "coll", "i", "<PERSON><PERSON><PERSON><PERSON>", "outgoing", "formatPrefix", "name", "mass", "marker", "radius", "generatePoints", "nodeLookup", "colorCounter", "level", "from", "styledMode", "colorIndex", "color", "to", "Series_NodesComposition", "seriesTypes", "sankey", "SankeyPoint", "ArcDiagramPoint_extend", "ArcDiagramPoint", "<PERSON><PERSON><PERSON><PERSON>", "SankeyColumnComposition_defined", "getAlignFactor", "<PERSON><PERSON><PERSON><PERSON>", "sankeyColumnArray", "sankeyColumn", "SankeyColumnAdditions", "constructor", "getTranslationFactor", "column", "slice", "minLinkWidth", "skipPoint", "factor", "remainingHeight", "plotSizeY", "borderWidth", "nodePadding", "sum", "splice", "top", "height", "nodeAlignment", "left", "equalNodes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inverted", "plotHeight", "plot<PERSON>id<PERSON>", "width", "plotSizeX", "round", "totalNodeOffset", "is", "hangsFrom", "absoluteTop", "nodeY", "directionOffset", "optionOffset", "relativeTop", "Sankey_SankeyColumnComposition", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "deg2rad", "addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "path", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "renderer", "textWrapper", "text", "textPath", "undo", "e", "textPathId", "attr", "textAttribs", "x", "dx", "transform", "box", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "element", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "symbols", "ColumnSeries", "SankeySeries", "crisp", "ArcDiagramSeries_extend", "ArcDiagramSeries_merge", "ArcDiagramSeries_pick", "ArcDiagramSeries_relativeLength", "ArcDiagramSeries", "createNodeColumns", "max<PERSON><PERSON><PERSON>", "maxRadius", "scale", "additionalSpace", "remainingWidth", "lineWidth", "<PERSON><PERSON><PERSON>", "min", "relativeLeft", "translateLink", "translationFactor", "pointOptions", "seriesOptions", "linkWeight", "centeredLinks", "nodeTop", "getX", "fromOrTo", "linkLeft", "nodeX", "shapeArgs", "fromX", "toX", "bottom", "reversed", "shapeType", "linkBase", "linkRadius", "abs", "dlBox", "tooltipPos", "plotY", "plotX", "translateNode", "nodeHeight", "nodeOffset", "fromNodeLeft", "absoluteLeft", "markerOptions", "symbol", "markerRadius", "parseInt", "colDistance", "mapOptionsToLevel", "dlOptions", "getDLOptions", "optionsPoint", "drawDataLabels", "dataLabels", "linkTextPath", "pointAttribs", "attrs", "markerAttribs", "defaultOptions", "fillOpacity", "states", "orderNodes", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,EAClL,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,MAAS,CAACA,EAAK,WAAc,CAACA,EAAK,UAAa,CAAE,GAChL,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,EAEpNA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,WAAc,CAAEA,EAAK,UAAa,CAAC,UAAa,CAC1L,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC3K,AAAC,CAAA,KACP,aACA,IA2HNC,EAijBAC,EA5qBUC,EAAuB,CAE/B,GACC,AAACZ,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,AAAClB,IACxB,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,IAAOpB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACtB,EAASwB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GASjL,GAAM,CAAEE,OAAQ,CAAET,UAAWU,CAAW,CAAEV,UAAW,CAAEW,WAAY,CAAEX,UAAWY,CAAU,CAAE,CAAE,CAAE,CAAE,CAAIJ,IAEhG,CAAEK,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIX,KAOhD,AAAC,SAAU5B,CAAgB,EAsGvB,SAASwC,IAIL,OAFA,IAAI,CAACC,IAAI,CAAG,EAAE,CACTC,MAAM,CAAC,IAAI,CAACC,MAAM,EAAI,EAAE,CAAE,IAAI,CAACC,KAAK,EAClCZ,EAAYQ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC3C,CAsDA,SAASC,IACD,IAAI,CAACH,KAAK,GACV,IAAI,CAACA,KAAK,CAACI,OAAO,CAAC,AAACC,IAChBA,EAAKT,OAAO,EAChB,GACA,IAAI,CAACI,KAAK,CAACM,MAAM,CAAG,GAExBlB,EAAYe,OAAO,CAACF,KAAK,CAAC,IAAI,CAAEC,UACpC,CAMA,SAASK,EAAaC,CAAK,EACvB,IAAMC,EAAOP,UAAWQ,EAAS,IAAI,CAACC,MAAM,CAAG,IAAI,CAACC,OAAO,CAACd,MAAM,CAAC,IAAI,CAACe,SAAS,EAC7E,CAAC,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,MAAM,CAAC,AAClB,CAAA,WAAVP,GACAE,EAAON,OAAO,CAAC,AAACY,IACRA,GAAcA,EAAW7B,MAAM,GAC/BG,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAYP,GAClC,CAACO,EAAWL,MAAM,GACdK,EAAWF,QAAQ,CAACI,OAAO,EAC3B5B,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAWF,QAAQ,CAAEL,GAE/CO,EAAWD,MAAM,EAAIC,EAAWD,MAAM,CAACG,OAAO,EAC9C5B,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAWD,MAAM,CAAEN,IAI7D,GAEJnB,EAAW2B,QAAQ,CAAChB,KAAK,CAAC,IAAI,CAAEQ,EACpC,CAOA,SAASU,EAAWC,CAAO,CAAEC,CAAM,CAAEC,CAAS,CAAEC,CAAQ,EACpD,IAAMvB,EAAQ,IAAI,CAACb,MAAM,CAACiC,OAAO,CAACpB,KAAK,CAAEH,EAAO,IAAI,CAACV,MAAM,CAACiC,OAAO,CAACvB,IAAI,CAAE2B,EAAa3B,GAAMS,QAAU,EAAGmB,EAAa5B,GAAM,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAGzI,GAFApC,EAAWqC,MAAM,CAAC/C,IAAI,CAAC,IAAI,CAAEwC,EAAS,CAAA,IAAI,CAACT,MAAM,EAAWU,EAC5DC,EAAWC,GACP,IAAI,CAACZ,MAAM,CAAE,CAEb,IAAMiB,EAAY,AAAC5B,CAAAA,GAAS,EAAE,AAAD,EACxB6B,MAAM,CACX,CAACC,EAAWlE,EAAG8D,IAAW,IAAI,CAACK,EAAE,GAAKnE,EAAEmE,EAAE,CAAGL,EAAQI,EAAY,IAGjEE,EAAatC,EAAMM,GAASA,CAAK,CAAC4B,EAAU,EAAI,CAAC,EAAG/B,GAAM,CAAC,IAAI,CAAC6B,KAAK,CAAC,EAAI,CAAC,GAEvE7B,IACI4B,EACA5B,CAAI,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAAGD,EAInB5B,EAAKS,MAAM,CAAGkB,GAIlBxB,EACI4B,GAAa,EACb5B,CAAK,CAAC4B,EAAU,CAAGI,EAGnBhC,EAAMiC,IAAI,CAACD,GAIf,IAAI,CAAC7C,MAAM,CAACiC,OAAO,CAACpB,KAAK,CAAG,CAACgC,EAAW,CAExCrC,EAAK0B,EAAQ,CAAA,IACb,IAAI,CAAClC,MAAM,CAAC+C,KAAK,CAACb,MAAM,CAACC,EAEjC,CACJ,CAxNAlE,EAAiB+E,OAAO,CATxB,SAAiBC,CAAU,CAAEC,CAAW,EACpC,IAAM/C,EAAa8C,EAAW1D,SAAS,CAAEU,EAAciD,EAAY3D,SAAS,CAM5E,OALAY,EAAWiB,YAAY,CAAGA,EAC1BjB,EAAW2B,QAAQ,CAAGV,EACtBjB,EAAWqC,MAAM,CAAGR,EACpB/B,EAAYQ,OAAO,CAAGA,EACtBR,EAAYe,OAAO,CAAGA,EACfkC,CACX,EA2EAjF,EAAiBkF,UAAU,CApE3B,SAAoBP,CAAE,EAClB,IAAMK,EAAa,IAAI,CAAC/C,UAAU,CAAEkD,EAAW,CAACvC,EAAO+B,IAAOtC,EAAKO,EAAO,AAACK,GAASA,EAAK0B,EAAE,GAAKA,GAC5F1B,EAAOkC,EAAS,IAAI,CAACvC,KAAK,CAAE+B,GAAKX,EACrC,GAAI,CAACf,EAAM,CACPe,EAAU,IAAI,CAACA,OAAO,CAACpB,KAAK,EAAIuC,EAAS,IAAI,CAACnB,OAAO,CAACpB,KAAK,CAAE+B,GAC7D,IAAMS,EAAU,IAAIJ,EAAW,IAAI,CAAE5C,EAAO,CACxCiD,UAAW,kBACX9B,OAAQ,CAAA,EACRoB,GAAIA,EACJW,EAAG,CACP,EAAGtB,GACHoB,CAAAA,EAAQ5B,OAAO,CAAG,EAAE,CACpB4B,EAAQ3B,SAAS,CAAG,EAAE,CAKtB2B,EAAQG,MAAM,CAAG,WACb,IAAIC,EAAQ,EAAGC,EAAU,EAOzB,OANAL,EAAQ5B,OAAO,CAACR,OAAO,CAAC,AAAC0C,IACrBF,GAASE,EAAKC,MAAM,EAAI,CAC5B,GACAP,EAAQ3B,SAAS,CAACT,OAAO,CAAC,AAAC0C,IACvBD,GAAWC,EAAKC,MAAM,EAAI,CAC9B,GACOC,KAAKC,GAAG,CAACL,EAAOC,EAC3B,EAKAL,EAAQU,MAAM,CAAG,SAAUC,CAAK,CAAEC,CAAI,EAClC,IAAIF,EAAS,EACb,IAAK,IAAIG,EAAI,EAAGA,EAAIb,CAAO,CAACY,EAAK,CAAC9C,MAAM,CAAE+C,IAAK,CAC3C,GAAIb,CAAO,CAACY,EAAK,CAACC,EAAE,GAAKF,EACrB,OAAOD,EAEXA,GAAUV,CAAO,CAACY,EAAK,CAACC,EAAE,CAACN,MAAM,AACrC,CACJ,EAGAP,EAAQc,QAAQ,CAAG,WACf,IAAIC,EAAW,EAMf,OALAf,EAAQ5B,OAAO,CAACR,OAAO,CAAC,AAAC0C,IACjBA,EAAKS,QAAQ,EACbA,GAER,GACQ,CAACf,EAAQ5B,OAAO,CAACN,MAAM,EAC3BiD,IAAaf,EAAQ5B,OAAO,CAACN,MAAM,AAC3C,EACAkC,EAAQd,KAAK,CAAG,IAAI,CAAC1B,KAAK,CAACiC,IAAI,CAACO,GAAW,EAC3CnC,EAAOmC,CACX,CAYA,OAXAnC,EAAKmD,YAAY,CAAG,OAEpBnD,EAAKoD,IAAI,CAAGpD,EAAKoD,IAAI,EAAIpD,EAAKe,OAAO,CAACW,EAAE,EAAI,GAE5C1B,EAAKqD,IAAI,CAAG/D,EAEZU,EAAKe,OAAO,CAACsC,IAAI,CAAErD,EAAKe,OAAO,CAACuC,MAAM,EAAItD,EAAKe,OAAO,CAACuC,MAAM,CAACC,MAAM,CAEpE,IAAI,CAACxC,OAAO,CAACuC,MAAM,EAAI,IAAI,CAACvC,OAAO,CAACuC,MAAM,CAACC,MAAM,CAEjD,GACOvD,CACX,EAYAjD,EAAiBwC,OAAO,CAAGA,EAgD3BxC,EAAiByG,cAAc,CA1C/B,WACI,IAAM3B,EAAQ,IAAI,CAACA,KAAK,CAAE4B,EAAa,CAAC,EACxC1E,EAAYyE,cAAc,CAACjF,IAAI,CAAC,IAAI,EAC/B,IAAI,CAACoB,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAElB,IAAI,CAAC+D,YAAY,CAAG,EAEpB,IAAI,CAAC/D,KAAK,CAACI,OAAO,CAAC,AAACC,IAChBA,EAAKQ,SAAS,CAACP,MAAM,CAAG,EACxBD,EAAKO,OAAO,CAACN,MAAM,CAAG,EACtBD,EAAK2D,KAAK,CAAG3D,EAAKe,OAAO,CAAC4C,KAAK,AACnC,GAEA,IAAI,CAACjE,MAAM,CAACK,OAAO,CAAC,AAAC+C,IACb5D,EAAQ4D,EAAMc,IAAI,IACbH,CAAU,CAACX,EAAMc,IAAI,CAAC,EACvBH,CAAAA,CAAU,CAACX,EAAMc,IAAI,CAAC,CAAG,IAAI,CAAC3B,UAAU,CAACa,EAAMc,IAAI,CAAA,EAEvDH,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACpD,SAAS,CAACoB,IAAI,CAACkB,GACtCA,EAAMrC,QAAQ,CAAGgD,CAAU,CAACX,EAAMc,IAAI,CAAC,CAEnC/B,EAAMgC,UAAU,CAChBf,EAAMgB,UAAU,CAAGxE,EAAKwD,EAAM/B,OAAO,CAAC+C,UAAU,CAAEL,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACE,UAAU,EAGnFhB,EAAMiB,KAAK,CACPjB,EAAM/B,OAAO,CAACgD,KAAK,EAAIN,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACG,KAAK,EAG3D7E,EAAQ4D,EAAMkB,EAAE,IACXP,CAAU,CAACX,EAAMkB,EAAE,CAAC,EACrBP,CAAAA,CAAU,CAACX,EAAMkB,EAAE,CAAC,CAAG,IAAI,CAAC/B,UAAU,CAACa,EAAMkB,EAAE,CAAA,EAEnDP,CAAU,CAACX,EAAMkB,EAAE,CAAC,CAACzD,OAAO,CAACqB,IAAI,CAACkB,GAClCA,EAAMpC,MAAM,CAAG+C,CAAU,CAACX,EAAMkB,EAAE,CAAC,EAEvClB,EAAMM,IAAI,CAAGN,EAAMM,IAAI,EAAIN,EAAMpB,EAAE,AACvC,EAAG,IAAI,EAEP,IAAI,CAAC+B,UAAU,CAAGA,CACtB,EAwCA1G,EAAiBmD,YAAY,CAAGA,EA6ChCnD,EAAiB+D,UAAU,CAAGA,CAClC,EAAG/D,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMkH,EAA2BlH,EAiBxD,CAAEmH,YAAa,CAAEC,OAAQ,CAAE9F,UAAW,CAAEW,WAAYoF,CAAW,CAAE,CAAE,CAAE,CAAE,CAAIvF,IAE3E,CAAEM,OAAQkF,CAAsB,CAAE,CAAI1F,GAM5C,OAAM2F,UAAwBF,EAO1BG,SAAU,CAEN,MAAO,CAAA,CACX,CACJ,CACAF,EAAuBC,EAAgBjG,SAAS,CAAE,CAC9CuC,SAAUqD,EAAwB/D,YAAY,AAClD,GA4QA,GAAM,CAAEhB,QAASsF,CAA+B,CAAEC,eAAAA,CAAc,CAAEC,eAAAA,CAAc,CAAE,CAAI/F,KAOtF,AAAC,SAAU3B,CAAuB,EA4B9BA,EAAwB8E,OAAO,CAN/B,SAAiBpC,CAAM,CAAEZ,CAAM,EAI3B,OAFA6F,AAD0BjF,EACRkF,YAAY,CAC1B,IAAIC,EAFkBnF,EAEuBZ,GAFvBY,CAI9B,CAOA,OAAMmF,EAMFC,YAAYpF,CAAM,CAAEZ,CAAM,CAAE,CACxB,IAAI,CAACY,MAAM,CAAGA,EACd,IAAI,CAACZ,MAAM,CAAGA,CAClB,CAgBAiG,qBAAqBjG,CAAM,CAAE,CACzB,IAAMkG,EAAS,IAAI,CAACtF,MAAM,CAAEC,EAAQqF,EAAOC,KAAK,GAAIpD,EAAQ/C,EAAO+C,KAAK,CAAEqD,EAAepG,EAAOiC,OAAO,CAACmE,YAAY,EAAI,EACpHC,EAAWC,EAAS,EAAGpC,EAAGqC,EAAmB,AAACxD,CAAAA,EAAMyD,SAAS,EAAI,CAAA,EAChExG,CAAAA,EAAOiC,OAAO,CAACwE,WAAW,EAAI,CAAA,EAC/B,AAACP,CAAAA,EAAO/E,MAAM,CAAG,CAAA,EAAKnB,EAAO0G,WAAW,CAK5C,KAAOR,EAAO/E,MAAM,EAAE,CAIlB,IAHAmF,EAASC,EAAkBL,EAAOJ,YAAY,CAACa,GAAG,GAClDN,EAAY,CAAA,EACZnC,EAAIgC,EAAO/E,MAAM,CACV+C,KACCgC,CAAM,CAAChC,EAAE,CAACV,MAAM,GAAK8C,EAASF,IAC9BF,EAAOU,MAAM,CAAC1C,EAAG,GACjBqC,EACI1C,KAAKC,GAAG,CAAC,EAAGyC,EAAkBH,GAClCC,EAAY,CAAA,GAGpB,GAAI,CAACA,EACD,KAER,CAGA,IAAK,IAAMnF,KADXgF,EAAO/E,MAAM,CAAG,EACGN,GACfqF,EAAOpD,IAAI,CAAC5B,GAEhB,OAAOoF,CACX,CAWAO,IAAIP,CAAM,CAAE,CACR,IAAMtG,EAAS,IAAI,CAACA,MAAM,CAAE0G,EAAc1G,EAAO0G,WAAW,CAAEI,EAAS,IAAI,CAAClG,MAAM,CAAC8B,MAAM,CAAC,CAACoE,EAAQ5F,KAC3F4F,EAAS,GACTA,CAAAA,GAAUJ,CAAU,EAGxBI,GADmBjD,KAAKC,GAAG,CAAC5C,EAAKsC,MAAM,GAAK8C,EAAQtG,EAAOiC,OAAO,CAACmE,YAAY,EAAI,IAGpF,GAEH,OAAOT,EAAe3F,EAAOiC,OAAO,CAAC8E,aAAa,EAAI,UAAa,CAAA,AAAC/G,CAAAA,EAAO+C,KAAK,CAACyD,SAAS,EAAI,CAAA,EAAKM,CAAK,CAC5G,CAWAE,KAAKV,CAAM,CAAE,CACT,IAAMtG,EAAS,IAAI,CAACA,MAAM,CAAE+C,EAAQ/C,EAAO+C,KAAK,CAAEkE,EAAajH,EAAOiC,OAAO,CAACgF,UAAU,CAAEC,EAAkBnE,EAAMoE,QAAQ,CAAGpE,EAAMqE,UAAU,CAAGrE,EAAMsE,SAAS,CAAGX,EAAc1G,EAAO0G,WAAW,CAAEY,EAAQ,IAAI,CAAC1G,MAAM,CAAC8B,MAAM,CAAC,CAAC4E,EAAOpG,KAC/NoG,EAAQ,GACRA,CAAAA,GAASZ,CAAU,EAMvBY,GAJkBL,EACdC,EAAiBhG,EAAKlB,MAAM,CAACa,KAAK,CAACM,MAAM,CACrCuF,EACJ7C,KAAKC,GAAG,CAAC5C,EAAKsC,MAAM,GAAK8C,EAAQtG,EAAOiC,OAAO,CAACmE,YAAY,EAAI,IAGrE,GACH,MAAO,AAAC,CAAA,AAACrD,CAAAA,EAAMwE,SAAS,EAAI,CAAA,EAAK1D,KAAK2D,KAAK,CAACF,EAAK,EAAK,CAC1D,CAYAX,KAAM,CACF,OAAO,IAAI,CAAC/F,MAAM,CAAC8B,MAAM,CAAC,CAACiE,EAAKzF,IAAUyF,EAAMzF,EAAKsC,MAAM,GAAK,EACpE,CAaAO,OAAO7C,CAAI,CAAEoF,CAAM,CAAE,CACjB,IAAMJ,EAAS,IAAI,CAACtF,MAAM,CAAEZ,EAAS,IAAI,CAACA,MAAM,CAAE0G,EAAc1G,EAAO0G,WAAW,CAC9E3C,EAAS,EAAG0D,EAChB,GAAIzH,EAAO0H,EAAE,CAAC,iBAAmBxG,EAAKyG,SAAS,CAC3C,MAAO,CACHC,YAAa1G,EAAKyG,SAAS,CAACE,KAAK,AACrC,EAEJ,IAAK,IAAI3D,EAAI,EAAGA,EAAIgC,EAAO/E,MAAM,CAAE+C,IAAK,CACpC,IAAMyC,EAAMT,CAAM,CAAChC,EAAE,CAACV,MAAM,GACtBsD,EAASjD,KAAKC,GAAG,CAAC6C,EAAML,EAAQtG,EAAOiC,OAAO,CAACmE,YAAY,EAAI,GAC/D0B,EAAkB5G,EAAKe,OAAO,CAACjC,EAAO+C,KAAK,CAACoE,QAAQ,CACtD,mBACA,iBAAiB,CAAEY,EAAe7G,EAAKe,OAAO,CAAC8B,MAAM,EAAI,EAQ7D,GANI0D,EADAd,EACkBG,EAASJ,EAIT,EAElBR,CAAM,CAAChC,EAAE,GAAKhD,EACd,MAAO,CACH8G,YAAajE,EAAU2B,CAAAA,EAAgCoC,GAGnDlC,EAAekC,EAAiBhB,GAChClB,EAAemC,EAAcN,EAAe,CACpD,EAEJ1D,GAAU0D,CACd,CACJ,CACJ,CACAvJ,EAAwB6H,qBAAqB,CAAGA,CACpD,EAAG7H,GAA4BA,CAAAA,EAA0B,CAAC,CAAA,GAM7B,IAAM+J,EAAkC/J,EAGrE,IAAIgK,EAAmG7J,EAAoB,KACvH8J,EAAuH9J,EAAoBI,CAAC,CAACyJ,GAE7IE,EAAuH/J,EAAoB,KAC3IgK,EAA2IhK,EAAoBI,CAAC,CAAC2J,GAEjKE,EAAmHjK,EAAoB,IACvIkK,EAAuIlK,EAAoBI,CAAC,CAAC6J,GAgBjK,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAI3I,IACf,CAAE4I,SAAAA,CAAQ,CAAElI,MAAOmI,CAAc,CAAEC,UAAAA,CAAS,CAAEvI,QAASwI,CAAgB,CAAEvI,OAAQwI,CAAe,CAAE,CAAIhJ,IAyB5G,SAASiJ,EAAYC,CAAI,CAAEC,CAAe,EAEtCA,EAAkBN,EAAe,CAAA,EAAM,CACnCO,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAACC,QAAQ,CAACD,GAAG,CAAEE,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAER,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAD,EAAOA,GAASW,GAAYA,EAASX,IAAI,CAErCW,GACAA,EAASC,IAAI,GAEbZ,GAAQE,EAAS,CACjB,IAAMU,EAAOlB,EAASe,EAAa,kBAAmB,AAACI,IACnD,GAAIb,GAAQE,EAAS,CAEjB,IAAIY,EAAad,EAAKe,IAAI,CAAC,MACtBD,GACDd,EAAKe,IAAI,CAAC,KAAMD,EAAalB,KAGjC,IAAMoB,EAAc,CAGhBC,EAAG,EACHzG,EAAG,CACP,EACIqF,EAAiBM,EAAWe,EAAE,IAC9BF,EAAYE,EAAE,CAAGf,EAAWe,EAAE,CAC9B,OAAOf,EAAWe,EAAE,EAEpBrB,EAAiBM,EAAWC,EAAE,IAC9BY,EAAYZ,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBK,EAAYM,IAAI,CAACC,GAEjB,IAAI,CAACD,IAAI,CAAC,CAAEI,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAC1J,OAAO,EAAC,EAGhC,IAAM2J,EAAWR,EAAE/I,KAAK,CAACsF,KAAK,CAAC,EAC/ByD,CAAAA,EAAE/I,KAAK,CAACM,MAAM,CAAG,EACjByI,EAAE/I,KAAK,CAAC,EAAE,CAAG,CACTwJ,QAAS,WACTnB,WAAYL,EAAgBK,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCiB,KAAM,CAAC,EAAEhB,EAAI,CAAC,EAAEO,EAAW,CAAC,AAChC,GACAO,SAAAA,CACJ,CACJ,CACJ,EAEAZ,CAAAA,EAAYE,QAAQ,CAAG,CAAEX,KAAAA,EAAMY,KAAAA,CAAK,CACxC,MAEIH,EAAYM,IAAI,CAAC,CAAEG,GAAI,EAAGd,GAAI,CAAE,GAChC,OAAOK,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACa,KAAK,GAEVf,EAAYgB,SAAS,CAAG,GACxB,IAAI,CAACjB,QAAQ,CAACkB,SAAS,CAACjB,IAErB,IAAI,AACf,CAWA,SAASkB,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAACC,OAAO,EAAEC,cAAc,YAC1D,GAAIF,EAAI,CACJ,IAAMG,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC3B,QAAQ,CAAC4B,WAAW,CAAC,IAAI,CAACL,OAAO,EAAGM,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQV,EAC5BW,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMpK,MAAM,CAIrEyK,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAE9B,EAAAA,CAAC,CAAEzG,EAAAA,CAAC,CAAE,CAAGuI,EAAgBC,EAAW,AAAClB,CAAAA,EAAGmB,iBAAiB,CAACH,GAAa,EAAC,EAAKrD,EAASyD,EAASpI,KAAKqI,GAAG,CAACH,GAAWI,EAAStI,KAAKuI,GAAG,CAACL,GAC7I,MAAO,CACH,CACI/B,EAAIoB,EAAYa,EAChB1I,EAAI6H,EAAYe,EACnB,CACD,CACInC,EAAIiB,EAAIgB,EACR1I,EAAI0H,EAAIkB,EACX,CACJ,AACL,EACA,IAAK,IAAIjI,EAAI,EAAGmI,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiBlL,MAAM,CACpD,IAAK,IAAIqL,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBvI,EAClBsI,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAc5B,EAAG+B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAxB,EAAQlI,IAAI,CAAC6J,GACb3B,EAAQlI,IAAI,CAAC4J,KAGK,IAAdL,GACArB,EAAQ6B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BX,EAAQlI,IAAI,CAAC4J,GAGzB,CACA,MAAO9C,EAAG,CAGN,KACJ,CAEJ1F,GAAKoI,EAAU,EACf,GAAI,CACA,IAAMG,EAAevI,EAAImI,EAAWS,EAAUjC,EAAGkC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI9B,EAAQ6B,OAAO,CAACF,GAChB3B,EAAQ6B,OAAO,CAACH,EACpB,CACA,MAAO9C,EAAG,CAGN,KACJ,CACJ,CAEIoB,EAAQ7J,MAAM,EACd6J,EAAQlI,IAAI,CAACkI,CAAO,CAAC,EAAE,CAAC7E,KAAK,IAEjCyE,EAAKI,OAAO,CAAGA,CACnB,CACA,OAAOJ,CACX,CAWA,SAASoC,EAAarC,CAAK,EACvB,IAAMsC,EAAetC,EAAMsC,YAAY,CAAEjJ,EAAQ2G,EAAM3G,KAAK,CAAEgF,EAAmBiE,CAAY,CAACjJ,EAAMK,YAAY,CAAG,WAAW,EAC1H4I,EAAavD,QAAQ,CACrBV,GAAmB,CAACiE,EAAaC,OAAO,GACxC,IAAI,CAACpE,WAAW,CAAC9E,EAAMmJ,gBAAgB,GAAG,IAAI,GAAKnJ,EAAMjC,OAAO,CAAEiH,GAC9DhF,EAAMoJ,aAAa,EACnB,CAACpE,EAAgBC,OAAO,EAExBjF,CAAAA,EAAMoJ,aAAa,CAAIpJ,EAAMoJ,aAAa,CAAC3M,OAAO,EAAE,EAGhE,CAoCA4M,AA3BiB,CAAA,CACbrK,QATJ,SAAiBsK,CAAe,EAC5B7E,EAAS6E,EAAiB,eAAgB5C,GAC1CjC,EAAS6E,EAAiB,wBAAyBN,GACnD,IAAMO,EAAkBD,EAAgB/N,SAAS,AAC5CgO,CAAAA,EAAgBzE,WAAW,EAC5ByE,CAAAA,EAAgBzE,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EAyBoB9F,OAAO,CAAEuF,KAC7B,GAAM,CAAEhJ,UAAW,CAAEiO,QAAAA,CAAO,CAAE,CAAE,CAAInF,IAC9B,CAAEjD,YAAa,CAAEc,OAAQuH,CAAY,CAAEpI,OAAQqI,CAAY,CAAE,CAAE,CAAI3N,IACnE,CAAE4N,MAAAA,CAAK,CAAEtN,OAAQuN,CAAuB,CAAErN,MAAOsN,CAAsB,CAAErN,KAAMsN,CAAqB,CAAElI,eAAgBmI,CAA+B,CAAE,CAAIlO,GAajK,OAAMmO,UAAyBN,EAW3BO,mBAAoB,CAChB,IAAMjO,EAAS,IAAI,CAAE+C,EAAQ/C,EAAO+C,KAAK,CAEzCmD,EAAS+B,EAA+BjF,OAAO,CAAC,EAAE,CAAEhD,GAgFpD,OA/EAkG,EAAOJ,YAAY,CAACoI,SAAS,CAAGnL,EAAMoE,QAAQ,CAC1CpE,EAAMqE,UAAU,CAAGrE,EAAMsE,SAAS,CAGtCnB,EAAOJ,YAAY,CAACG,oBAAoB,CAAG,AAACjG,IACxC,IAAMa,EAAQqF,EAAOC,KAAK,GAAIC,EAAe,IAAI,CAACnE,OAAO,CAACmE,YAAY,EAAI,EACtEC,EAAWC,EAAS,EAAGpC,EAAGO,EAAQ0J,EAAY,EAAGC,EAAQ,EAAGC,EAAkB,EAAGC,EAAiB,AAACvL,CAAAA,EAAMwE,SAAS,EAAI,CAAA,EACrHvH,CAAAA,EAAOiC,OAAO,CAACuC,MAAM,EAClBxE,EAAOiC,OAAO,CAACuC,MAAM,CAAC+J,SAAS,EAAI,CAAA,EACvC,AAACrI,CAAAA,EAAO/E,MAAM,CAAG,CAAA,EACbnB,EAAO0G,WAAW,CAK1B,KAAOR,EAAO/E,MAAM,EAAE,CAIlB,IAHAmF,EAASgI,EAAiBpI,EAAOJ,YAAY,CAACa,GAAG,GACjDN,EAAY,CAAA,EACZnC,EAAIgC,EAAO/E,MAAM,CACV+C,KAAK,CACRO,EAAS,AAACyB,CAAM,CAAChC,EAAE,CAACV,MAAM,GAAM8C,EAAS8H,EACzC,IAAMI,EAAW3K,KAAK4K,GAAG,CAAC1L,EAAMqE,UAAU,CAAErE,EAAMsE,SAAS,CACvD5C,CAAAA,EAAS+J,EACTJ,EAAQvK,KAAK4K,GAAG,CAACD,EAAW/J,EAAQ2J,GAE/B3J,EAAS2B,IACdF,EAAOU,MAAM,CAAC1C,EAAG,GACjBoK,GAAkBlI,EAClB3B,EAAS2B,EACTC,EAAY,CAAA,GAEhBgI,GAAmB5J,EAAU,CAAA,EAAI2J,CAAI,EAAK,EAC1CD,EAAYtK,KAAKC,GAAG,CAACqK,EAAW1J,EACpC,CACA,GAAI,CAAC4B,EACD,KAER,CAUA,OARAH,EAAO/E,MAAM,CAAG,EAChBN,EAAMI,OAAO,CAAC,AAACC,IACXA,EAAKkN,KAAK,CAAGA,EACblI,EAAOpD,IAAI,CAAC5B,EAChB,GACAgF,EAAOJ,YAAY,CAACqI,SAAS,CAAGA,EAChCjI,EAAOJ,YAAY,CAACsI,KAAK,CAAGA,EAC5BlI,EAAOJ,YAAY,CAACuI,eAAe,CAAGA,EAC/B/H,CACX,EACAJ,EAAOJ,YAAY,CAAC/B,MAAM,CAAG,SAAU7C,CAAI,CAAEoF,CAAM,EAC/C,IAAMW,EAAa/F,EAAKlB,MAAM,CAACiC,OAAO,CAACgF,UAAU,CAAEP,EAAc1G,EAAO0G,WAAW,CAAEyH,EAAYtK,KAAK4K,GAAG,CAAC1L,EAAMsE,SAAS,CAAEtE,EAAMqE,UAAU,CAAE,AAAClB,CAAAA,EAAOJ,YAAY,CAACoI,SAAS,EAAI,CAAA,EAC3KlO,EAAOa,KAAK,CAACM,MAAM,CAAGuF,GACtB3C,EAASmC,EAAOJ,YAAY,CAACuI,eAAe,EAAI,EAAG5G,EACvD,IAAK,IAAIvD,EAAI,EAAGA,EAAIgC,EAAO/E,MAAM,CAAE+C,IAAK,CACpC,IAAMyC,EAAMT,CAAM,CAAChC,EAAE,CAACV,MAAM,GACvB0C,CAAAA,EAAOJ,YAAY,CAACsI,KAAK,EAAI,CAAA,EAC5B9G,EAAQL,EACVkH,EACAtK,KAAKC,GAAG,CAAC6C,EAAML,EAAQtG,EAAOiC,OAAO,CAACmE,YAAY,EAAI,GAQ1D,GANIqB,EADAd,EACkBW,EAAQZ,EAIR,EAElBR,CAAM,CAAChC,EAAE,GAAKhD,EACd,MAAO,CACHwN,aAAc3K,EAASgK,EAAgC7M,EAAKe,OAAO,CAAC8B,MAAM,EAAI,EAAG0D,EACrF,EAEJ1D,GAAU0D,CACd,CACJ,EAEAzH,EAAOa,KAAK,CAACI,OAAO,CAAC,SAAUC,CAAI,EAC/BA,EAAKgF,MAAM,CAAG,EACdA,EAAOpD,IAAI,CAAC5B,EAChB,GACO,CAACgF,EAAO,AACnB,CAKAyI,cAAc3K,CAAK,CAAE,CACjB,IAAqBrC,EAAWqC,EAAMrC,QAAQ,CAAEC,EAASoC,EAAMpC,MAAM,CAAEmB,EAAQ,IAAI,CAACA,KAAK,CAAE6L,EAAoB5O,AAAhG,IAAI,CAAmG4O,iBAAiB,CAAEC,EAAe7K,EAAM/B,OAAO,CAAE6M,EAAgB9O,AAAxK,IAAI,CAA2KiC,OAAO,CAAE8M,EAAajB,EAAsBe,EAAaE,UAAU,CAAED,EAAcC,UAAU,CAAElL,KAAKC,GAAG,CAAC,AAACE,CAAAA,EAAMJ,MAAM,EAAI,CAAA,EACnTgL,EACAjN,EAASyM,KAAK,CAAGpO,AAFN,IAAI,CAESiC,OAAO,CAACmE,YAAY,EAAI,IAAM4I,EAAgBhL,EAAMhE,MAAM,CAACiC,OAAO,CAAC+M,aAAa,CAAEC,EAAUtN,EAASkG,KAAK,CAChIqH,EAAO,CAAChO,EAAMiO,KAChB,IAAMC,EAAY,AAAClO,CAAAA,EAAK6C,MAAM,CAACC,EAAOmL,IAAa,CAAA,EAC/CP,EAIJ,OAHU/K,KAAK4K,GAAG,CAACvN,EAAKmO,KAAK,CAAGD,EAEhClO,EAAKmO,KAAK,CAAInO,CAAAA,EAAKoO,SAAS,EAAIpO,EAAKoO,SAAS,CAACxI,MAAM,EAAI,CAAA,EAAKiI,EAElE,EACIQ,EAAQP,EACRrN,EAAS0N,KAAK,CACV,AAAC,CAAA,AAAC1N,CAAAA,EAAS2N,SAAS,CAACxI,MAAM,EAAI,CAAA,EAAKiI,CAAS,EAAK,EACtDG,EAAKvN,EAAU,aAAc6N,EAAMR,EAAgBpN,EAAOyN,KAAK,CAC/D,AAAC,CAAA,AAACzN,CAAAA,EAAO0N,SAAS,CAACxI,MAAM,EAAI,CAAA,EAAKiI,CAAS,EAAK,EAChDG,EAAKtN,EAAQ,WAAY6N,EAASR,EAClCM,EAAQC,GACR,CAAA,CAACD,EAAOC,EAAI,CAAG,CAACA,EAAKD,EAAM,AAAD,EAE1BT,EAAcY,QAAQ,GACtB,CAACH,EAAOC,EAAI,CAAG,CAACA,EAAKD,EAAM,CAC3BE,EAAS,AAAC1M,CAAAA,EAAMyD,SAAS,EAAI,CAAA,EAAKiJ,GAEtCzL,EAAM2L,SAAS,CAAG,OAClB3L,EAAM4L,QAAQ,CAAG,CACbL,EACAA,EAAQR,EACRS,EACAA,EAAMT,EACT,CACD,IAAMc,EAAa,AAAEL,CAAAA,EAAMT,EAAaQ,CAAI,EAAK1L,KAAKiM,GAAG,CAACN,EAAMT,EAAaQ,GAAUzB,EAAsBgB,EAAce,UAAU,CAAEhM,KAAK4K,GAAG,CAAC5K,KAAKiM,GAAG,CAACN,EAAMT,EAAaQ,GAAS,EAAG5N,EAASkG,KAAK,CAAGhE,KAAKiM,GAAG,CAACf,IAClN/K,CAAAA,EAAMsL,SAAS,CAAG,CACd1Q,EAAG,CACC,CAAC,IAAK2Q,EAAOE,EAAO,CACpB,CACI,IACA,AAACD,CAAAA,EAAMT,EAAaQ,CAAI,EAAK,EAC7BM,EACA,EACA,EACA,EACAL,EAAMT,EACNU,EACH,CACD,CAAC,IAAKD,EAAKC,EAAO,CAClB,CACI,IACA,AAACD,CAAAA,EAAMD,EAAQR,CAAS,EAAK,EAC7Bc,EAAad,EACb,EACA,EACA,EACAQ,EAAQR,EACRU,EACH,CACD,CAAC,IAAI,CACR,AACL,EACAzL,EAAM+L,KAAK,CAAG,CACV/F,EAAGuF,EAAQ,AAACC,CAAAA,EAAMD,CAAI,EAAK,EAC3BhM,EAAGkM,EAASI,EACZ/I,OAAQiI,EACRzH,MAAO,CACX,EAEAtD,EAAMgM,UAAU,CAAGjN,EAAMoE,QAAQ,CAAG,CAChC,AAACpE,CAAAA,EAAMyD,SAAS,EAAI,CAAA,EAAKxC,EAAM+L,KAAK,CAACxM,CAAC,CAAGwL,EAAa,EACtD,AAAChM,CAAAA,EAAMwE,SAAS,EAAI,CAAA,EAAKvD,EAAM+L,KAAK,CAAC/F,CAAC,CACzC,CAAG,CACAhG,EAAM+L,KAAK,CAAC/F,CAAC,CACbhG,EAAM+L,KAAK,CAACxM,CAAC,CAAGwL,EAAa,EAChC,CAED/K,EAAMT,CAAC,CAAGS,EAAMiM,KAAK,CAAG,EACxBjM,EAAMgG,CAAC,CAAGhG,EAAMkM,KAAK,CAAG,EACnBlM,EAAMiB,KAAK,EACZjB,CAAAA,EAAMiB,KAAK,CAAGtD,EAASsD,KAAK,AAAD,CAEnC,CAKAkL,cAAcjP,CAAI,CAAEgF,CAAM,CAAE,CACxB,IAAqB0I,EAAoB5O,AAA1B,IAAI,CAA6B4O,iBAAiB,CAAE7L,EAAQ/C,AAA5D,IAAI,CAA+D+C,KAAK,CAAEmE,EAAiBnE,EAAMoE,QAAQ,CACpHpE,EAAMsE,SAAS,CAAGtE,EAAMqE,UAAU,CAAEnF,EAAUjC,AADnC,IAAI,CACsCiC,OAAO,CAAEkM,EAAYtK,KAAK4K,GAAG,CAAC1L,EAAMsE,SAAS,CAAEtE,EAAMqE,UAAU,CAAEF,EAAiBhG,EAAKlB,MAAM,CAACa,KAAK,CAACM,MAAM,CAAG,IAAI,CAACuF,WAAW,EAAGC,EAAMzF,EAAKsC,MAAM,GAAM0C,CAAAA,EAAOJ,YAAY,CAACsI,KAAK,EAAI,CAAA,EAAqCgC,EAAanJ,AAAjChF,EAAQgF,UAAU,CAC5QkH,EACAtK,KAAKC,GAAG,CAAC6C,EAAMiI,EAAmB,IAAI,CAAC3M,OAAO,CAACmE,YAAY,EAAI,GAAImI,EAAYtM,EAAQuC,MAAM,EAAE+J,WAAa,EAAG8B,EAAanK,EAAOJ,YAAY,CAAC/B,MAAM,CAAC7C,EAAM0N,GAAoB0B,EAAe3C,EAAMG,EAAsBuC,GAAcA,EAAWE,YAAY,CAAG,AAACrK,CAAAA,EAAOJ,YAAY,CAACkB,IAAI,CAAC4H,IAAsB,CAAA,EACnTyB,CAAAA,GAAcA,EAAW3B,YAAY,EAAI,CAAA,GAAMH,GAAYiC,EAAgB3C,EAAuB5L,EAAQuC,MAAM,CAAEtD,EAAKe,OAAO,CAACuC,MAAM,EAAGiM,EAASD,EAAcC,MAAM,CAAEC,EAAeF,EAAc/L,MAAM,CAAEoC,EAAM8J,SAAS1O,EAAQ8B,MAAM,CAAE,IAC3O,CAAA,AAAChB,CAAAA,EAAMoE,QAAQ,CACZpE,EAAMsE,SAAS,CAAGtE,EAAMqE,UAAU,AAAD,EAAMuG,CAAAA,EAAM,IAAI,CAACiD,WAAW,CAAI1P,CAAAA,EAAKgF,MAAM,EAAI,CAAA,EAChF,AAACsK,CAAAA,EAAcjC,SAAS,EAAI,CAAA,EAAK,EAAGA,GACpC,AAACrI,CAAAA,EAAOJ,YAAY,CAACsI,KAAK,EAAI,CAAA,EACzBlI,CAAAA,EAAOJ,YAAY,CAACqI,SAAS,EAAI,CAAA,EAAK,CAAA,CAAC,EAAK,IAGzD,GAFAjN,EAAKyF,GAAG,CAAGA,EAEPA,EAAK,CAELzF,EAAKmO,KAAK,CAAGiB,EACbpP,EAAK2G,KAAK,CAAGhB,EACb,IAAwBS,EAAQpG,EAAKe,OAAO,CAACqF,KAAK,EAAIrF,EAAQqF,KAAK,EAAI8I,EAAYtJ,EAAS5F,EAAKe,OAAO,CAAC6E,MAAM,EAAI7E,EAAQ6E,MAAM,EAAIsJ,EACjI7M,EAAIsD,CACJ5E,CAAAA,EAAQyN,QAAQ,GAChBnM,EAAI,AAACR,CAAAA,EAAMyD,SAAS,EAAI,CAAA,EAAKK,EACzB9D,EAAMoE,QAAQ,EACd5D,CAAAA,EAAI,AAACR,CAAAA,EAAMyD,SAAS,EAAI,CAAA,EAAKK,CAAE,GAGnC,IAAI,CAACgK,iBAAiB,EAEtB3P,CAAAA,EAAK4P,SAAS,CAAGpD,EAAaqD,YAAY,CAAC,CACvClM,MAAO,IAAI,CAACgM,iBAAiB,CAAC3P,EAAK2D,KAAK,CAAC,CACzCmM,aAAc9P,EAAKe,OAAO,AAC9B,EAAC,EAGLf,EAAKgP,KAAK,CAAG,EACbhP,EAAK+O,KAAK,CAAG,EAEb/O,EAAK8O,UAAU,CAAGjN,EAAMoE,QAAQ,CAAG,CAC/B,AAACpE,CAAAA,EAAMyD,SAAS,EAAI,CAAA,EAAKjD,EAAIuD,EAAS,EACtC,AAAC/D,CAAAA,EAAMwE,SAAS,EAAI,CAAA,EArBd+I,EAqBuBhJ,EAAQ,EACxC,CAAG,CACA0C,AAvBMsG,EAuBFhJ,EAAQ,EACZ/D,EAAIuD,EAAS,EAChB,CACD5F,EAAKyO,SAAS,CAAG,OACjBzO,EAAKoO,SAAS,CAAG,CACb1Q,EAAG4O,CAAO,CAACiD,GAAU,SAAS,CA5BxBH,EA4B4B/M,EAAI,AAACmN,CAAAA,GAAgB5J,CAAK,EAAK,EAAG4J,GAAgBpJ,EAAOoJ,GAAgB5J,GAC3GQ,MAAOoJ,GAAgBpJ,EACvBR,OAAQ4J,GAAgB5J,CAC5B,EACA5F,EAAK6O,KAAK,CAAG,CACT/F,EAAGA,AAjCGsG,EAiCChJ,EAAQ,EACf/D,EAAGA,EACHuD,OAAQ,EACRQ,MAAO,CACX,CACJ,MAEIpG,EAAK4P,SAAS,CAAG,CACb7H,QAAS,CAAA,CACb,CAER,CAGAgI,gBAAiB,CACb,GAAI,IAAI,CAAChP,OAAO,CAACiP,UAAU,CAAE,CACzB,IAAMxH,EAAW,IAAI,CAACzH,OAAO,CAACiP,UAAU,CAACxH,QAAQ,CAEjD+D,EAAalO,SAAS,CAAC0R,cAAc,CAACxR,IAAI,CAAC,IAAI,CAAE,IAAI,CAACoB,KAAK,EAE3D,IAAI,CAACoB,OAAO,CAACiP,UAAU,CAACxH,QAAQ,CAC5B,IAAI,CAACzH,OAAO,CAACiP,UAAU,CAACC,YAAY,CACxC1D,EAAalO,SAAS,CAAC0R,cAAc,CAACxR,IAAI,CAAC,IAAI,CAAE,IAAI,CAACiB,IAAI,EAE1D,IAAI,CAACuB,OAAO,CAACiP,UAAU,CAACxH,QAAQ,CAAGA,CACvC,CACJ,CACA0H,aAAapN,CAAK,CAElB3C,CAAK,CAAE,CACH,GAAI2C,GAASA,EAAMxC,MAAM,CAAE,CACvB,GAAM,CAAE,GAAG6P,EAAO,CAAGlJ,IAA0G5I,SAAS,CAAC6R,YAAY,CAChJtQ,KAAK,CAAC,IAAI,CAAEC,WACjB,OAAOsQ,CACX,CACA,OAAO,KAAK,CAACD,aAAatQ,KAAK,CAAC,IAAI,CAAEC,UAC1C,CACAuQ,cAActN,CAAK,CAAE,QACjB,AAAIA,EAAMxC,MAAM,CACL,KAAK,CAAC8P,cAAcxQ,KAAK,CAAC,IAAI,CAAEC,WAEpC,CAAC,CACZ,CACJ,CAMAiN,EAAiBuD,cAAc,CAAG1D,EAAuBH,EAAa6D,cAAc,CAl+BnD,CAU7BvC,cAAe,CAAA,EAYf/H,WAAY,CAAA,EAYZiK,WAAY,CAYRC,aAAc,CAKVjI,WAAY,CAIRE,YAAa,KACjB,CACJ,CACJ,EA8BA5E,OAAQ,CACJgN,YAAa,EACbjD,UAAW,EACXkD,OAAQ,CAAC,EACThB,OAAQ,QACZ,EAaA1M,OAAQ,OAUR2L,SAAU,CAAA,CACd,GA82BA9B,EAAwBI,EAAiBzO,SAAS,CAAE,CAChDmS,WAAY,CAAA,CAChB,GACA1D,EAAiBzO,SAAS,CAACW,UAAU,CA/gC4BsF,EAghCjEzF,IAA0I4R,kBAAkB,CAAC,aAAc3D,GAa9I,IAAMrO,GAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}