{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/funnel\n * @requires highcharts\n *\n * Highcharts funnel module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/funnel\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/funnel\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ funnel_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Series/Funnel/FunnelSeriesDefaults.js\n/* *\n *\n *  Highcharts funnel module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Funnel charts are a type of chart often used to visualize stages in a\n * sales project, where the top are the initial stages with the most\n * clients. It requires that the modules/funnel.js file is loaded.\n *\n * @sample highcharts/demo/funnel/\n *         Funnel demo\n *\n * @extends      plotOptions.pie\n * @excluding    innerSize,size,dataSorting\n * @product      highcharts\n * @requires     modules/funnel\n * @optionparent plotOptions.funnel\n */\nconst FunnelSeriesDefaults = {\n    /**\n     * Initial animation is by default disabled for the funnel chart.\n     */\n    animation: false,\n    /**\n     * The corner radius of the border surrounding all points or series. A\n     * number signifies pixels. A percentage string, like for example `50%`,\n     * signifies a size relative to the series width.\n     *\n     * @sample highcharts/plotoptions/funnel-border-radius\n     *         Funnel and pyramid with rounded border\n     */\n    borderRadius: 0,\n    /**\n     * The center of the series. By default, it is centered in the middle\n     * of the plot area, so it fills the plot area height.\n     *\n     * @type    {Array<number|string>}\n     * @default [\"50%\", \"50%\"]\n     * @since   3.0\n     */\n    center: ['50%', '50%'],\n    /**\n     * The width of the funnel compared to the width of the plot area,\n     * or the pixel width if it is a number.\n     *\n     * @type  {number|string}\n     * @since 3.0\n     */\n    width: '90%',\n    /**\n     * The width of the neck, the lower part of the funnel. A number defines\n     * pixel width, a percentage string defines a percentage of the plot\n     * area width.\n     *\n     * @sample {highcharts} highcharts/demo/funnel/\n     *         Funnel demo\n     *\n     * @type  {number|string}\n     * @since 3.0\n     */\n    neckWidth: '30%',\n    /**\n     * The height of the funnel or pyramid. If it is a number it defines\n     * the pixel height, if it is a percentage string it is the percentage\n     * of the plot area height.\n     *\n     * @sample {highcharts} highcharts/demo/funnel/\n     *         Funnel demo\n     *\n     * @type  {number|string}\n     * @since 3.0\n     */\n    height: '100%',\n    /**\n     * The height of the neck, the lower part of the funnel. A number\n     * defines pixel width, a percentage string defines a percentage of the\n     * plot area height.\n     *\n     * @type {number|string}\n     */\n    neckHeight: '25%',\n    /**\n     * A reversed funnel has the widest area down. A reversed funnel with\n     * no neck width and neck height is a pyramid.\n     *\n     * @since 3.0.10\n     */\n    reversed: false,\n    /**\n     * To avoid adapting the data label size in Pie.drawDataLabels.\n     * @ignore-option\n     */\n    size: true,\n    dataLabels: {\n        connectorWidth: 1,\n        verticalAlign: 'middle'\n    },\n    /**\n     * Options for the series states.\n     */\n    states: {\n        /**\n         * @excluding halo, marker, lineWidth, lineWidthPlus\n         * @apioption plotOptions.funnel.states.hover\n         */\n        /**\n         * Options for a selected funnel item.\n         *\n         * @excluding halo, marker, lineWidth, lineWidthPlus\n         */\n        select: {\n            /**\n             * A specific color for the selected point.\n             *\n             * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            color: \"#cccccc\" /* Palette.neutralColor20 */,\n            /**\n             * A specific border color for the selected point.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            borderColor: \"#000000\" /* Palette.neutralColor100 */\n        }\n    }\n};\n/**\n * A `funnel` series. If the [type](#series.funnel.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.funnel\n * @excluding dataParser, dataURL, stack, xAxis, yAxis, dataSorting,\n *            boostBlending, boostThreshold\n * @product   highcharts\n * @requires  modules/funnel\n * @apioption series.funnel\n */\n/**\n * An array of data points for the series. For the `funnel` series type,\n * points can be given in the following ways:\n *\n * 1.  An array of numerical values. In this case, the numerical values\n * will be interpreted as `y` options. Example:\n *\n *  ```js\n *  data: [0, 5, 3, 5]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.funnel.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         y: 3,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         y: 1,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|null|*>}\n * @extends   series.pie.data\n * @excluding sliced\n * @product   highcharts\n * @apioption series.funnel.data\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Funnel_FunnelSeriesDefaults = (FunnelSeriesDefaults);\n\n;// ./code/es-modules/Extensions/BorderRadius.js\n/* *\n *\n *  Highcharts Border Radius module\n *\n *  Author: Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, extend, isObject, merge, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst defaultBorderRadiusOptions = {\n    radius: 0,\n    scope: 'stack',\n    where: void 0\n};\n/* *\n *\n *  Variables\n *\n * */\nlet oldArc = noop;\nlet oldRoundedRect = noop;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction applyBorderRadius(path, i, r) {\n    const a = path[i];\n    let b = path[i + 1];\n    if (b[0] === 'Z') {\n        b = path[0];\n    }\n    let line, arc, fromLineToArc;\n    // From straight line to arc\n    if ((a[0] === 'M' || a[0] === 'L') && b[0] === 'A') {\n        line = a;\n        arc = b;\n        fromLineToArc = true;\n        // From arc to straight line\n    }\n    else if (a[0] === 'A' && (b[0] === 'M' || b[0] === 'L')) {\n        line = b;\n        arc = a;\n    }\n    if (line && arc && arc.params) {\n        const bigR = arc[1], \n        // In our use cases, outer pie slice arcs are clockwise and inner\n        // arcs (donut/sunburst etc) are anti-clockwise\n        clockwise = arc[5], params = arc.params, { start, end, cx, cy } = params;\n        // Some geometric constants\n        const relativeR = clockwise ? (bigR - r) : (bigR + r), \n        // The angle, on the big arc, that the border radius arc takes up\n        angleOfBorderRadius = relativeR ? Math.asin(r / relativeR) : 0, angleOffset = clockwise ?\n            angleOfBorderRadius :\n            -angleOfBorderRadius, \n        // The distance along the radius of the big arc to the starting\n        // point of the small border radius arc\n        distanceBigCenterToStartArc = (Math.cos(angleOfBorderRadius) *\n            relativeR);\n        // From line to arc\n        if (fromLineToArc) {\n            // Update the cache\n            params.start = start + angleOffset;\n            // First move to the start position at the radial line. We want to\n            // start one borderRadius closer to the center.\n            line[1] = cx + distanceBigCenterToStartArc * Math.cos(start);\n            line[2] = cy + distanceBigCenterToStartArc * Math.sin(start);\n            // Now draw an arc towards the point where the small circle touches\n            // the great circle.\n            path.splice(i + 1, 0, [\n                'A',\n                r,\n                r,\n                0, // Slanting,\n                0, // Long arc\n                1, // Clockwise\n                cx + bigR * Math.cos(params.start),\n                cy + bigR * Math.sin(params.start)\n            ]);\n            // From arc to line\n        }\n        else {\n            // Update the cache\n            params.end = end - angleOffset;\n            // End the big arc a bit earlier\n            arc[6] = cx + bigR * Math.cos(params.end);\n            arc[7] = cy + bigR * Math.sin(params.end);\n            // Draw a small arc towards a point on the end angle, but one\n            // borderRadius closer to the center relative to the perimeter.\n            path.splice(i + 1, 0, [\n                'A',\n                r,\n                r,\n                0,\n                0,\n                1,\n                cx + distanceBigCenterToStartArc * Math.cos(end),\n                cy + distanceBigCenterToStartArc * Math.sin(end)\n            ]);\n        }\n        // Long or short arc must be reconsidered because we have modified the\n        // start and end points\n        arc[4] = Math.abs(params.end - params.start) < Math.PI ? 0 : 1;\n    }\n}\n/**\n * Extend arc with borderRadius.\n * @private\n */\nfunction arc(x, y, w, h, options = {}) {\n    const path = oldArc(x, y, w, h, options), { innerR = 0, r = w, start = 0, end = 0 } = options;\n    if (options.open || !options.borderRadius) {\n        return path;\n    }\n    const alpha = end - start, sinHalfAlpha = Math.sin(alpha / 2), borderRadius = Math.max(Math.min(relativeLength(options.borderRadius || 0, r - innerR), \n    // Cap to half the sector radius\n    (r - innerR) / 2, \n    // For smaller pie slices, cap to the largest small circle that\n    // can be fitted within the sector\n    (r * sinHalfAlpha) / (1 + sinHalfAlpha)), 0), \n    // For the inner radius, we need an extra cap because the inner arc\n    // is shorter than the outer arc\n    innerBorderRadius = Math.min(borderRadius, 2 * (alpha / Math.PI) * innerR);\n    // Apply turn-by-turn border radius. Start at the end since we're\n    // splicing in arc segments.\n    let i = path.length - 1;\n    while (i--) {\n        applyBorderRadius(path, i, i > 1 ? innerBorderRadius : borderRadius);\n    }\n    return path;\n}\n/** @private */\nfunction seriesOnAfterColumnTranslate() {\n    if (this.options.borderRadius &&\n        !(this.chart.is3d && this.chart.is3d())) {\n        const { options, yAxis } = this, percent = options.stacking === 'percent', seriesDefault = defaultOptions.plotOptions?.[this.type]\n            ?.borderRadius, borderRadius = optionsToObject(options.borderRadius, isObject(seriesDefault) ? seriesDefault : {}), reversed = yAxis.options.reversed;\n        for (const point of this.points) {\n            const { shapeArgs } = point;\n            if (point.shapeType === 'roundedRect' && shapeArgs) {\n                const { width = 0, height = 0, y = 0 } = shapeArgs;\n                let brBoxY = y, brBoxHeight = height;\n                // It would be nice to refactor StackItem.getStackBox/\n                // setOffset so that we could get a reliable box out of\n                // it. Currently it is close if we remove the label\n                // offset, but we still need to run crispCol and also\n                // flip it if inverted, so atm it is simpler to do it\n                // like the below.\n                if (borderRadius.scope === 'stack' &&\n                    point.stackTotal) {\n                    const stackEnd = yAxis.translate(percent ? 100 : point.stackTotal, false, true, false, true), stackThreshold = yAxis.translate(options.threshold || 0, false, true, false, true), box = this.crispCol(0, Math.min(stackEnd, stackThreshold), 0, Math.abs(stackEnd - stackThreshold));\n                    brBoxY = box.y;\n                    brBoxHeight = box.height;\n                }\n                const flip = (point.negative ? -1 : 1) *\n                    (reversed ? -1 : 1) === -1;\n                // Handle the where option\n                let where = borderRadius.where;\n                // Waterfall, hanging columns should have rounding on\n                // all sides\n                if (!where &&\n                    this.is('waterfall') &&\n                    Math.abs((point.yBottom || 0) -\n                        (this.translatedThreshold || 0)) > this.borderWidth) {\n                    where = 'all';\n                }\n                if (!where) {\n                    where = 'end';\n                }\n                // Get the radius\n                const r = Math.min(relativeLength(borderRadius.radius, width), width / 2, \n                // Cap to the height, but not if where is `end`\n                where === 'all' ? height / 2 : Infinity) || 0;\n                // If the `where` option is 'end', cut off the\n                // rectangles by making the border-radius box one r\n                // greater, so that the imaginary radius falls outside\n                // the rectangle.\n                if (where === 'end') {\n                    if (flip) {\n                        brBoxY -= r;\n                        brBoxHeight += r;\n                    }\n                    else {\n                        brBoxHeight += r;\n                    }\n                }\n                extend(shapeArgs, { brBoxHeight, brBoxY, r });\n            }\n        }\n    }\n}\n/** @private */\nfunction compose(SeriesClass, SVGElementClass, SVGRendererClass) {\n    const PieSeriesClass = SeriesClass.types.pie;\n    if (!SVGElementClass.symbolCustomAttribs.includes('borderRadius')) {\n        const symbols = SVGRendererClass.prototype.symbols;\n        addEvent(SeriesClass, 'afterColumnTranslate', seriesOnAfterColumnTranslate, {\n            // After columnrange and polar column modifications\n            order: 9\n        });\n        addEvent(PieSeriesClass, 'afterTranslate', pieSeriesOnAfterTranslate);\n        SVGElementClass.symbolCustomAttribs.push('borderRadius', 'brBoxHeight', 'brBoxY');\n        oldArc = symbols.arc;\n        oldRoundedRect = symbols.roundedRect;\n        symbols.arc = arc;\n        symbols.roundedRect = roundedRect;\n    }\n}\n/** @private */\nfunction optionsToObject(options, seriesBROptions) {\n    if (!isObject(options)) {\n        options = { radius: options || 0 };\n    }\n    return merge(defaultBorderRadiusOptions, seriesBROptions, options);\n}\n/** @private */\nfunction pieSeriesOnAfterTranslate() {\n    const borderRadius = optionsToObject(this.options.borderRadius);\n    for (const point of this.points) {\n        const shapeArgs = point.shapeArgs;\n        if (shapeArgs) {\n            shapeArgs.borderRadius = relativeLength(borderRadius.radius, (shapeArgs.r || 0) - ((shapeArgs.innerR) || 0));\n        }\n    }\n}\n/**\n * Extend roundedRect with individual cutting through rOffset.\n * @private\n */\nfunction roundedRect(x, y, width, height, options = {}) {\n    const path = oldRoundedRect(x, y, width, height, options), { r = 0, brBoxHeight = height, brBoxY = y } = options, brOffsetTop = y - brBoxY, brOffsetBtm = (brBoxY + brBoxHeight) - (y + height), \n    // When the distance to the border-radius box is greater than the r\n    // itself, it means no border radius. The -0.1 accounts for float\n    // rounding errors.\n    rTop = (brOffsetTop - r) > -0.1 ? 0 : r, rBtm = (brOffsetBtm - r) > -0.1 ? 0 : r, cutTop = Math.max(rTop && brOffsetTop, 0), cutBtm = Math.max(rBtm && brOffsetBtm, 0);\n    /*\n\n    The naming of control points:\n\n      / a -------- b \\\n     /                \\\n    h                  c\n    |                  |\n    |                  |\n    |                  |\n    g                  d\n     \\                /\n      \\ f -------- e /\n\n    */\n    const a = [x + rTop, y], b = [x + width - rTop, y], c = [x + width, y + rTop], d = [\n        x + width, y + height - rBtm\n    ], e = [\n        x + width - rBtm,\n        y + height\n    ], f = [x + rBtm, y + height], g = [x, y + height - rBtm], h = [x, y + rTop];\n    const applyPythagoras = (r, altitude) => Math.sqrt(Math.pow(r, 2) - Math.pow(altitude, 2));\n    // Inside stacks, cut off part of the top\n    if (cutTop) {\n        const base = applyPythagoras(rTop, rTop - cutTop);\n        a[0] -= base;\n        b[0] += base;\n        c[1] = h[1] = y + rTop - cutTop;\n    }\n    // Column is lower than the radius. Cut off bottom inside the top\n    // radius.\n    if (height < rTop - cutTop) {\n        const base = applyPythagoras(rTop, rTop - cutTop - height);\n        c[0] = d[0] = x + width - rTop + base;\n        e[0] = Math.min(c[0], e[0]);\n        f[0] = Math.max(d[0], f[0]);\n        g[0] = h[0] = x + rTop - base;\n        c[1] = h[1] = y + height;\n    }\n    // Inside stacks, cut off part of the bottom\n    if (cutBtm) {\n        const base = applyPythagoras(rBtm, rBtm - cutBtm);\n        e[0] += base;\n        f[0] -= base;\n        d[1] = g[1] = y + height - rBtm + cutBtm;\n    }\n    // Cut off top inside the bottom radius\n    if (height < rBtm - cutBtm) {\n        const base = applyPythagoras(rBtm, rBtm - cutBtm - height);\n        c[0] = d[0] = x + width - rBtm + base;\n        b[0] = Math.min(c[0], b[0]);\n        a[0] = Math.max(d[0], a[0]);\n        g[0] = h[0] = x + rBtm - base;\n        d[1] = g[1] = y;\n    }\n    // Preserve the box for data labels\n    path.length = 0;\n    path.push(['M', ...a], \n    // Top side\n    ['L', ...b], \n    // Top right corner\n    ['A', rTop, rTop, 0, 0, 1, ...c], \n    // Right side\n    ['L', ...d], \n    // Bottom right corner\n    ['A', rBtm, rBtm, 0, 0, 1, ...e], \n    // Bottom side\n    ['L', ...f], \n    // Bottom left corner\n    ['A', rBtm, rBtm, 0, 0, 1, ...g], \n    // Left side\n    ['L', ...h], \n    // Top left corner\n    ['A', rTop, rTop, 0, 0, 1, ...a], ['Z']);\n    return path;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BorderRadius = {\n    compose,\n    optionsToObject\n};\n/* harmony default export */ const Extensions_BorderRadius = (BorderRadius);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Detailed options for border radius.\n *\n * @sample  {highcharts} highcharts/plotoptions/column-borderradius/\n *          Rounded columns\n * @sample  highcharts/plotoptions/series-border-radius\n *          Column and pie with rounded border\n *\n * @interface Highcharts.BorderRadiusOptionsObject\n */ /**\n* The border radius. A number signifies pixels. A percentage string, like for\n* example `50%`, signifies a relative size. For columns this is relative to the\n* column width, for pies it is relative to the radius and the inner radius.\n*\n* @name Highcharts.BorderRadiusOptionsObject#radius\n* @type {string|number}\n*/ /**\n* The scope of the rounding for column charts. In a stacked column chart, the\n* value `point` means each single point will get rounded corners. The value\n* `stack` means the rounding will apply to the full stack, so that only points\n* close to the top or bottom will receive rounding.\n*\n* @name Highcharts.BorderRadiusOptionsObject#scope\n* @validvalue [\"point\", \"stack\"]\n* @type {string}\n*/ /**\n* For column charts, where in the point or stack to apply rounding. The `end`\n* value means only those corners at the point value will be rounded, leaving\n* the corners at the base or threshold unrounded. This is the most intuitive\n* behaviour. The `all` value means also the base will be rounded.\n*\n* @name Highcharts.BorderRadiusOptionsObject#where\n* @validvalue [\"all\", \"end\"]\n* @type {string}\n* @default end\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Funnel/FunnelSeries.js\n/* *\n *\n *  Highcharts funnel module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { composed, noop: FunnelSeries_noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { column: ColumnSeries, pie: PieSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent: FunnelSeries_addEvent, correctFloat, extend: FunnelSeries_extend, fireEvent, isArray, merge: FunnelSeries_merge, pick, pushUnique, relativeLength: FunnelSeries_relativeLength, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst baseAlignDataLabel = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series.prototype.alignDataLabel;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get positions - either an integer or a percentage string must be\n * given.\n * @private\n * @param {number|string|undefined} length\n *        Length\n * @param {number} relativeTo\n *        Relative factor\n * @return {number}\n *         Relative position\n */\nfunction getLength(length, relativeTo) {\n    return (/%$/).test(length) ?\n        relativeTo * parseInt(length, 10) / 100 :\n        parseInt(length, 10);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.funnel\n *\n * @augments Highcharts.Series\n */\nclass FunnelSeries extends PieSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    alignDataLabel(point, dataLabel, options, alignTo, isNew) {\n        const series = point.series, reversed = series.options.reversed, dlBox = point.dlBox || point.shapeArgs, { align, padding = 0, verticalAlign } = options, inside = ((series.options || {}).dataLabels || {}).inside, centerY = series.center[1], plotY = point.plotY || 0, pointPlotY = (reversed ?\n            2 * centerY - plotY :\n            plotY), \n        // #16176: Only SVGLabel has height set\n        dataLabelHeight = dataLabel.height ?? dataLabel.getBBox().height, widthAtLabel = series.getWidthAt(pointPlotY - dlBox.height / 2 +\n            dataLabelHeight), offset = verticalAlign === 'middle' ?\n            (dlBox.topWidth - dlBox.bottomWidth) / 4 :\n            (widthAtLabel - dlBox.bottomWidth) / 2;\n        let y = dlBox.y, x = dlBox.x;\n        if (verticalAlign === 'middle') {\n            y = dlBox.y - dlBox.height / 2 + dataLabelHeight / 2;\n        }\n        else if (verticalAlign === 'top') {\n            y = dlBox.y - dlBox.height + dataLabelHeight + padding;\n        }\n        if (verticalAlign === 'top' && !reversed ||\n            verticalAlign === 'bottom' && reversed ||\n            verticalAlign === 'middle') {\n            if (align === 'right') {\n                x = dlBox.x - padding + offset;\n            }\n            else if (align === 'left') {\n                x = dlBox.x + padding - offset;\n            }\n        }\n        alignTo = {\n            x: x,\n            y: reversed ? y - dlBox.height : y,\n            width: dlBox.bottomWidth,\n            height: dlBox.height\n        };\n        options.verticalAlign = 'bottom';\n        if (inside) {\n            // If the distance were positive (as default), the overlapping\n            // labels logic would skip these labels and they would be allowed\n            // to overlap.\n            options.distance = void 0;\n        }\n        // Call the parent method\n        if (inside && point.visible) {\n            baseAlignDataLabel.call(series, point, dataLabel, options, alignTo, isNew);\n        }\n        if (inside) {\n            if (!point.visible && point.dataLabel) {\n                // Avoid animation from top\n                point.dataLabel.placed = false;\n            }\n            // If label is inside and we have contrast, set it:\n            if (point.contrastColor) {\n                dataLabel.css({\n                    color: point.contrastColor\n                });\n            }\n        }\n    }\n    /**\n     * Extend the data label method.\n     * @private\n     */\n    drawDataLabels() {\n        (splat(this.options.dataLabels || {})[0].inside ?\n            ColumnSeries :\n            PieSeries).prototype.drawDataLabels.call(this);\n    }\n    /** @private */\n    getDataLabelPosition(point, distance) {\n        const y = point.plotY || 0, sign = point.half ? 1 : -1, x = this.getX(y, !!point.half, point);\n        return {\n            distance,\n            // Initial position of the data label - it's utilized for finding\n            // the final position for the label\n            natural: {\n                x: 0,\n                y\n            },\n            computed: {\n            // Used for generating connector path - initialized later in\n            // drawDataLabels function x: undefined, y: undefined\n            },\n            // Left - funnel on the left side of the data label\n            // Right - funnel on the right side of the data label\n            alignment: point.half ? 'right' : 'left',\n            connectorPosition: {\n                breakAt: {\n                    x: x + (distance - 5) * sign,\n                    y\n                },\n                touchingSliceAt: {\n                    x: x + distance * sign,\n                    y\n                }\n            }\n        };\n    }\n    /**\n     * Overrides the pie translate method.\n     * @private\n     */\n    translate() {\n        const series = this, chart = series.chart, options = series.options, reversed = options.reversed, ignoreHiddenPoint = options.ignoreHiddenPoint, borderRadiusObject = Extensions_BorderRadius.optionsToObject(options.borderRadius), plotWidth = chart.plotWidth, plotHeight = chart.plotHeight, center = options.center, centerX = getLength(center[0], plotWidth), centerY = getLength(center[1], plotHeight), width = getLength(options.width, plotWidth), height = getLength(options.height, plotHeight), neckWidth = getLength(options.neckWidth, plotWidth), neckHeight = getLength(options.neckHeight, plotHeight), neckY = (centerY - height / 2) + height - neckHeight, points = series.points, borderRadius = FunnelSeries_relativeLength(borderRadiusObject.radius, width), radiusScope = borderRadiusObject.scope, half = (options.dataLabels.position === 'left' ?\n            1 :\n            0), roundingFactors = (angle) => {\n            const tan = Math.tan(angle / 2), cosA = Math.cos(alpha), sinA = Math.sin(alpha);\n            let r = borderRadius, t = r / tan, k = Math.tan((Math.PI - angle) / 3.2104);\n            if (t > maxT) {\n                t = maxT;\n                r = t * tan;\n            }\n            k *= r;\n            return {\n                dx: [t * cosA, (t - k) * cosA, t - k, t],\n                dy: [t * sinA, (t - k) * sinA, t - k, t]\n                    .map((i) => (reversed ? -i : i))\n            };\n        };\n        let sum = 0, cumulative = 0, // Start at top\n        tempWidth, path, fraction, alpha, // The angle between top and left point's edges\n        maxT, x1, y1, x2, x3, y3, x4, y5;\n        series.getWidthAt = function (y) {\n            const top = (centerY - height / 2);\n            return (y > neckY || height === neckHeight) ?\n                neckWidth :\n                neckWidth + (width - neckWidth) *\n                    (1 - (y - top) / (height - neckHeight));\n        };\n        series.getX = function (y, half, point) {\n            return centerX + (half ? -1 : 1) *\n                ((series.getWidthAt(reversed ? 2 * centerY - y : y) / 2) +\n                    (point.dataLabel?.dataLabelPosition?.distance ??\n                        FunnelSeries_relativeLength(this.options.dataLabels?.distance || 0, width)));\n        };\n        // Expose\n        series.center = [centerX, centerY, height];\n        series.centerX = centerX;\n        /*\n        Individual point coordinate naming:\n\n        x1,y1 _________________ x2,y1\n        \\                         /\n         \\                       /\n          \\                     /\n           \\                   /\n            \\                 /\n           x3,y3 _________ x4,y3\n\n        Additional for the base of the neck:\n\n             |               |\n             |               |\n             |               |\n           x3,y5 _________ x4,y5\n\n        */\n        // get the total sum\n        for (const point of points) {\n            if (point.y && point.isValid() &&\n                (!ignoreHiddenPoint || point.visible !== false)) {\n                sum += point.y;\n            }\n        }\n        for (const point of points) {\n            // Set start and end positions\n            y5 = null;\n            fraction = sum ? point.y / sum : 0;\n            y1 = centerY - height / 2 + cumulative * height;\n            y3 = y1 + fraction * height;\n            tempWidth = series.getWidthAt(y1);\n            x1 = centerX - tempWidth / 2;\n            x2 = x1 + tempWidth;\n            tempWidth = series.getWidthAt(y3);\n            x3 = centerX - tempWidth / 2;\n            x4 = x3 + tempWidth;\n            // The entire point is within the neck\n            if (correctFloat(y1) >= neckY) {\n                x1 = x3 = centerX - neckWidth / 2;\n                x2 = x4 = centerX + neckWidth / 2;\n                // The base of the neck\n            }\n            else if (y3 > neckY) {\n                y5 = y3;\n                tempWidth = series.getWidthAt(neckY);\n                x3 = centerX - tempWidth / 2;\n                x4 = x3 + tempWidth;\n                y3 = neckY;\n            }\n            if (reversed) {\n                y1 = 2 * centerY - y1;\n                y3 = 2 * centerY - y3;\n                if (y5 !== null) {\n                    y5 = 2 * centerY - y5;\n                }\n            }\n            if (borderRadius && (radiusScope === 'point' ||\n                point.index === 0 ||\n                point.index === points.length - 1 ||\n                y5 !== null)) {\n                // Creating the path of funnel points with rounded corners\n                // (#18839)\n                const h = Math.abs(y3 - y1), xSide = x2 - x4, lBase = x4 - x3, lSide = Math.sqrt(xSide * xSide + h * h);\n                // If xSide equals zero, return Infinity to avoid dividing\n                // by zero (#20319)\n                alpha = Math.atan(xSide !== 0 ? h / xSide : Infinity);\n                maxT = lSide / 2;\n                if (y5 !== null) {\n                    maxT = Math.min(maxT, Math.abs(y5 - y3) / 2);\n                }\n                if (lBase >= 1) {\n                    maxT = Math.min(maxT, lBase / 2);\n                }\n                // Creating a point base\n                let f = roundingFactors(alpha);\n                if (radiusScope === 'stack' && point.index !== 0) {\n                    path = [\n                        ['M', x1, y1],\n                        ['L', x2, y1]\n                    ];\n                }\n                else {\n                    path = [\n                        ['M', x1 + f.dx[0], y1 + f.dy[0]],\n                        [\n                            'C',\n                            x1 + f.dx[1], y1 + f.dy[1],\n                            x1 + f.dx[2], y1,\n                            x1 + f.dx[3], y1\n                        ],\n                        ['L', x2 - f.dx[3], y1],\n                        [\n                            'C',\n                            x2 - f.dx[2], y1,\n                            x2 - f.dx[1], y1 + f.dy[1],\n                            x2 - f.dx[0], y1 + f.dy[0]\n                        ]\n                    ];\n                }\n                if (y5 !== null) {\n                    // Closure of point with extension\n                    const fr = roundingFactors(Math.PI / 2);\n                    f = roundingFactors(Math.PI / 2 + alpha);\n                    path.push(['L', x4 + f.dx[0], y3 - f.dy[0]], [\n                        'C',\n                        x4 + f.dx[1], y3 - f.dy[1],\n                        x4, y3 + f.dy[2],\n                        x4, y3 + f.dy[3]\n                    ]);\n                    if (radiusScope === 'stack' &&\n                        point.index !== points.length - 1) {\n                        path.push(['L', x4, y5], ['L', x3, y5]);\n                    }\n                    else {\n                        path.push(['L', x4, y5 - fr.dy[3]], [\n                            'C',\n                            x4, y5 - fr.dy[2],\n                            x4 - fr.dx[2], y5,\n                            x4 - fr.dx[3], y5\n                        ], ['L', x3 + fr.dx[3], y5], [\n                            'C',\n                            x3 + fr.dx[2], y5,\n                            x3, y5 - fr.dy[2],\n                            x3, y5 - fr.dy[3]\n                        ]);\n                    }\n                    path.push(['L', x3, y3 + f.dy[3]], [\n                        'C',\n                        x3, y3 + f.dy[2],\n                        x3 - f.dx[1], y3 - f.dy[1],\n                        x3 - f.dx[0], y3 - f.dy[0]\n                    ]);\n                }\n                else if (lBase >= 1) {\n                    // Closure of point without extension\n                    f = roundingFactors(Math.PI - alpha);\n                    if (radiusScope === 'stack' && point.index === 0) {\n                        path.push(['L', x4, y3], ['L', x3, y3]);\n                    }\n                    else {\n                        path.push(['L', x4 + f.dx[0], y3 - f.dy[0]], [\n                            'C',\n                            x4 + f.dx[1], y3 - f.dy[1],\n                            x4 - f.dx[2], y3,\n                            x4 - f.dx[3], y3\n                        ], ['L', x3 + f.dx[3], y3], [\n                            'C',\n                            x3 + f.dx[2], y3,\n                            x3 - f.dx[1], y3 - f.dy[1],\n                            x3 - f.dx[0], y3 - f.dy[0]\n                        ]);\n                    }\n                }\n                else {\n                    // Creating a rounded tip of the \"pyramid\"\n                    f = roundingFactors(Math.PI - alpha * 2);\n                    path.push(['L', x3 + f.dx[0], y3 - f.dy[0]], [\n                        'C',\n                        x3 + f.dx[1], y3 - f.dy[1],\n                        x3 - f.dx[1], y3 - f.dy[1],\n                        x3 - f.dx[0], y3 - f.dy[0]\n                    ]);\n                }\n            }\n            else {\n                // Creating the path of funnel points without rounded corners\n                path = [\n                    ['M', x1, y1],\n                    ['L', x2, y1],\n                    ['L', x4, y3]\n                ];\n                if (y5 !== null) {\n                    path.push(['L', x4, y5], ['L', x3, y5]);\n                }\n                path.push(['L', x3, y3]);\n            }\n            path.push(['Z']);\n            // Prepare for using shared dr\n            point.shapeType = 'path';\n            point.shapeArgs = { d: path };\n            // For tooltips and data labels\n            point.percentage = fraction * 100;\n            point.plotX = centerX;\n            point.plotY = (y1 + (y5 || y3)) / 2;\n            // Placement of tooltips and data labels\n            point.tooltipPos = [\n                centerX,\n                point.plotY\n            ];\n            point.dlBox = {\n                x: x3,\n                y: y1,\n                topWidth: x2 - x1,\n                bottomWidth: x4 - x3,\n                height: Math.abs(pick(y5, y3) - y1),\n                width: NaN\n            };\n            // Slice is a noop on funnel points\n            point.slice = FunnelSeries_noop;\n            // Mimicking pie data label placement logic\n            point.half = half;\n            if (point.isValid() &&\n                (!ignoreHiddenPoint || point.visible !== false)) {\n                cumulative += fraction;\n            }\n        }\n        fireEvent(series, 'afterTranslate');\n    }\n    /**\n     * Funnel items don't have angles (#2289).\n     * @private\n     */\n    sortByAngle(points) {\n        points.sort((a, b) => (a.plotY - b.plotY));\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nFunnelSeries.defaultOptions = FunnelSeries_merge(PieSeries.defaultOptions, Funnel_FunnelSeriesDefaults);\nFunnelSeries_extend(FunnelSeries.prototype, {\n    animate: FunnelSeries_noop\n});\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (FunnelSeries) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(ChartClass) {\n        if (pushUnique(composed, 'FunnelSeries')) {\n            FunnelSeries_addEvent(ChartClass, 'afterHideAllOverlappingLabels', onChartAfterHideAllOverlappingLabels);\n        }\n    }\n    FunnelSeries.compose = compose;\n    /** @private */\n    function onChartAfterHideAllOverlappingLabels() {\n        for (const series of this.series) {\n            let dataLabelsOptions = series.options && series.options.dataLabels;\n            if (isArray(dataLabelsOptions)) {\n                dataLabelsOptions = dataLabelsOptions[0];\n            }\n            if (series.is('pie') &&\n                series.placeDataLabels &&\n                dataLabelsOptions &&\n                !dataLabelsOptions.inside) {\n                series.placeDataLabels();\n            }\n        }\n    }\n})(FunnelSeries || (FunnelSeries = {}));\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('funnel', FunnelSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Funnel_FunnelSeries = (FunnelSeries);\n\n;// ./code/es-modules/Series/Pyramid/PyramidSeriesDefaults.js\n/* *\n *\n *  Highcharts funnel module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\nconst PyramidSeriesDefaults = {\n    /**\n     * The pyramid neck height is zero by default, as opposed to the funnel,\n     * which shares the same layout logic.\n     *\n     * @since 3.0.10\n     */\n    neckHeight: '0%',\n    /**\n     * The pyramid neck width is zero by default, as opposed to the funnel,\n     * which shares the same layout logic.\n     *\n     * @since 3.0.10\n     */\n    neckWidth: '0%',\n    /**\n     * The pyramid is reversed by default, as opposed to the funnel, which\n     * shares the layout engine, and is not reversed.\n     *\n     * @since 3.0.10\n     */\n    reversed: true\n};\n/**\n * A `pyramid` series. If the [type](#series.pyramid.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.pyramid\n * @excluding dataParser, dataURL, stack, xAxis, yAxis, dataSorting,\n *            boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/funnel\n * @apioption series.pyramid\n */\n/**\n * An array of data points for the series. For the `pyramid` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.pyramid.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        y: 9,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        y: 6,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|null|*>}\n * @extends   series.pie.data\n * @excluding sliced\n * @product   highcharts\n * @apioption series.pyramid.data\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pyramid_PyramidSeriesDefaults = (PyramidSeriesDefaults);\n\n;// ./code/es-modules/Series/Pyramid/PyramidSeries.js\n/* *\n *\n *  Highcharts funnel module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: PyramidSeries_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Pyramid series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pyramid\n *\n * @augments Highcharts.Series\n */\nclass PyramidSeries extends Funnel_FunnelSeries {\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A pyramid series is a special type of funnel, without neck and reversed\n * by default.\n *\n * @sample highcharts/demo/pyramid/\n *         Pyramid chart\n *\n * @extends      plotOptions.funnel\n * @product      highcharts\n * @requires     modules/funnel\n * @optionparent plotOptions.pyramid\n */\nPyramidSeries.defaultOptions = PyramidSeries_merge(Funnel_FunnelSeries.defaultOptions, Pyramid_PyramidSeriesDefaults);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('pyramid', PyramidSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pyramid_PyramidSeries = ((/* unused pure expression or super */ null && (PyramidSeries)));\n\n;// ./code/es-modules/masters/modules/funnel.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nFunnel_FunnelSeries.compose(G.Chart);\n/* harmony default export */ const funnel_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "funnel_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "defaultOptions", "noop", "addEvent", "extend", "isObject", "merge", "<PERSON><PERSON><PERSON><PERSON>", "defaultBorderRadiusOptions", "radius", "scope", "where", "optionsToObject", "options", "seriesBROptions", "Extensions_BorderRadius", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "composed", "FunnelSeries_noop", "column", "ColumnSeries", "pie", "PieSeries", "seriesTypes", "FunnelSeries_addEvent", "correctFloat", "FunnelSeries_extend", "fireEvent", "isArray", "FunnelSeries_merge", "pick", "pushUnique", "FunnelSeries_relativeLength", "splat", "baseAlignDataLabel", "series", "alignDataLabel", "<PERSON><PERSON><PERSON><PERSON>", "length", "relativeTo", "test", "parseInt", "FunnelSeries", "point", "dataLabel", "alignTo", "isNew", "reversed", "dlBox", "shapeArgs", "align", "padding", "verticalAlign", "inside", "dataLabels", "centerY", "center", "plotY", "dataLabelHeight", "height", "getBBox", "widthAtLabel", "getWidthAt", "pointPlotY", "offset", "topWidth", "bottomWidth", "y", "x", "width", "distance", "visible", "placed", "contrastColor", "css", "color", "drawDataLabels", "getDataLabelPosition", "sign", "half", "getX", "natural", "computed", "alignment", "connectorPosition", "breakAt", "touchingSliceAt", "translate", "chart", "ignoreHiddenPoint", "borderRadiusObject", "borderRadius", "plot<PERSON>id<PERSON>", "plotHeight", "centerX", "neckWidth", "neckHeight", "neckY", "points", "radiusScope", "position", "roundingFactors", "angle", "tan", "Math", "cosA", "cos", "alpha", "sinA", "sin", "r", "t", "k", "PI", "maxT", "dx", "dy", "map", "i", "sum", "cumulative", "temp<PERSON>idth", "path", "fraction", "x1", "y1", "x2", "x3", "y3", "x4", "y5", "top", "dataLabelPosition", "<PERSON><PERSON><PERSON><PERSON>", "index", "h", "abs", "xSide", "lBase", "lSide", "sqrt", "atan", "Infinity", "min", "f", "fr", "push", "shapeType", "percentage", "plotX", "tooltipPos", "NaN", "slice", "sortByAngle", "sort", "b", "animation", "size", "connectorWidth", "states", "select", "borderColor", "animate", "onChartAfterHideAllOverlappingLabels", "dataLabelsOptions", "is", "placeDataLabels", "compose", "ChartClass", "registerSeriesType", "Funnel_FunnelSeries", "PyramidSeries_merge", "PyramidSeries", "G", "Chart"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA2NrH,GAAM,CAAEE,eAAAA,CAAc,CAAE,CAAID,IAEtB,CAAEE,KAAAA,CAAI,CAAE,CAAIF,IAEZ,CAAEG,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,eAAAA,CAAc,CAAE,CAAIP,IAMzDQ,EAA6B,CAC/BC,OAAQ,EACRC,MAAO,QACPC,MAAO,KAAK,CAChB,EAuMA,SAASC,EAAgBC,CAAO,CAAEC,CAAe,EAI7C,OAHKT,EAASQ,IACVA,CAAAA,EAAU,CAAEJ,OAAQI,GAAW,CAAE,CAAA,EAE9BP,EAAME,EAA4BM,EAAiBD,EAC9D,CA0G6B,IAAME,EAJd,CAEjBH,gBAAAA,CACJ,EA8CA,IAAII,EAAmIxC,EAAoB,KACvJyC,EAAuJzC,EAAoBI,CAAC,CAACoC,GAgBjL,GAAM,CAAEE,SAAAA,CAAQ,CAAEhB,KAAMiB,CAAiB,CAAE,CAAInB,IAGzC,CAAEoB,OAAQC,CAAY,CAAEC,IAAKC,CAAS,CAAE,CAAG,AAACN,IAA2IO,WAAW,CAElM,CAAErB,SAAUsB,CAAqB,CAAEC,aAAAA,CAAY,CAAEtB,OAAQuB,CAAmB,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEvB,MAAOwB,CAAkB,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAEzB,eAAgB0B,CAA2B,CAAEC,MAAAA,CAAK,CAAE,CAAIlC,IAMvMmC,EAAqB,AAAClB,IAA2ImB,MAAM,CAAC1C,SAAS,CAAC2C,cAAc,CAiBtM,SAASC,EAAUC,CAAM,CAAEC,CAAU,EACjC,MAAO,AAAC,KAAMC,IAAI,CAACF,GACfC,EAAaE,SAASH,EAAQ,IAAM,IACpCG,SAASH,EAAQ,GACzB,CAaA,MAAMI,UAAqBpB,EAUvBc,eAAeO,CAAK,CAAEC,CAAS,CAAEhC,CAAO,CAAEiC,CAAO,CAAEC,CAAK,CAAE,CACtD,IAAMX,EAASQ,EAAMR,MAAM,CAAEY,EAAWZ,EAAOvB,OAAO,CAACmC,QAAQ,CAAEC,EAAQL,EAAMK,KAAK,EAAIL,EAAMM,SAAS,CAAE,CAAEC,MAAAA,CAAK,CAAEC,QAAAA,EAAU,CAAC,CAAEC,cAAAA,CAAa,CAAE,CAAGxC,EAASyC,EAAS,AAAC,CAAA,AAAClB,CAAAA,EAAOvB,OAAO,EAAI,CAAC,CAAA,EAAG0C,UAAU,EAAI,CAAC,CAAA,EAAGD,MAAM,CAAEE,EAAUpB,EAAOqB,MAAM,CAAC,EAAE,CAAEC,EAAQd,EAAMc,KAAK,EAAI,EAIxQC,EAAkBd,EAAUe,MAAM,EAAIf,EAAUgB,OAAO,GAAGD,MAAM,CAAEE,EAAe1B,EAAO2B,UAAU,CAACC,AAJsLhB,CAAAA,EACrR,EAAIQ,EAAUE,EACdA,CAAI,EAEwGT,EAAMW,MAAM,CAAG,EAC3HD,GAAkBM,EAASZ,AAAkB,WAAlBA,EAC3B,AAACJ,CAAAA,EAAMiB,QAAQ,CAAGjB,EAAMkB,WAAW,AAAD,EAAK,EACvC,AAACL,CAAAA,EAAeb,EAAMkB,WAAW,AAAD,EAAK,EACrCC,EAAInB,EAAMmB,CAAC,CAAEC,EAAIpB,EAAMoB,CAAC,AACxBhB,AAAkB,CAAA,WAAlBA,EACAe,EAAInB,EAAMmB,CAAC,CAAGnB,EAAMW,MAAM,CAAG,EAAID,EAAkB,EAE5B,QAAlBN,GACLe,CAAAA,EAAInB,EAAMmB,CAAC,CAAGnB,EAAMW,MAAM,CAAGD,EAAkBP,CAAM,EAErDC,CAAAA,AAAkB,QAAlBA,GAA2B,CAACL,GAC5BK,AAAkB,WAAlBA,GAA8BL,GAC9BK,AAAkB,WAAlBA,CAAyB,IACrBF,AAAU,UAAVA,EACAkB,EAAIpB,EAAMoB,CAAC,CAAGjB,EAAUa,EAET,SAAVd,GACLkB,CAAAA,EAAIpB,EAAMoB,CAAC,CAAGjB,EAAUa,CAAK,GAGrCnB,EAAU,CACNuB,EAAGA,EACHD,EAAGpB,EAAWoB,EAAInB,EAAMW,MAAM,CAAGQ,EACjCE,MAAOrB,EAAMkB,WAAW,CACxBP,OAAQX,EAAMW,MAAM,AACxB,EACA/C,EAAQwC,aAAa,CAAG,SACpBC,GAIAzC,CAAAA,EAAQ0D,QAAQ,CAAG,KAAK,CAAA,EAGxBjB,GAAUV,EAAM4B,OAAO,EACvBrC,EAAmBvC,IAAI,CAACwC,EAAQQ,EAAOC,EAAWhC,EAASiC,EAASC,GAEpEO,IACI,CAACV,EAAM4B,OAAO,EAAI5B,EAAMC,SAAS,EAEjCD,CAAAA,EAAMC,SAAS,CAAC4B,MAAM,CAAG,CAAA,CAAI,EAG7B7B,EAAM8B,aAAa,EACnB7B,EAAU8B,GAAG,CAAC,CACVC,MAAOhC,EAAM8B,aAAa,AAC9B,GAGZ,CAKAG,gBAAiB,CACb,AAAC3C,CAAAA,EAAM,IAAI,CAACrB,OAAO,CAAC0C,UAAU,EAAI,CAAC,EAAE,CAAC,EAAE,CAACD,MAAM,CAC3CjC,EACAE,CAAQ,EAAG7B,SAAS,CAACmF,cAAc,CAACjF,IAAI,CAAC,IAAI,CACrD,CAEAkF,qBAAqBlC,CAAK,CAAE2B,CAAQ,CAAE,CAClC,IAAMH,EAAIxB,EAAMc,KAAK,EAAI,EAAGqB,EAAOnC,EAAMoC,IAAI,CAAG,EAAI,GAAIX,EAAI,IAAI,CAACY,IAAI,CAACb,EAAG,CAAC,CAACxB,EAAMoC,IAAI,CAAEpC,GACvF,MAAO,CACH2B,SAAAA,EAGAW,QAAS,CACLb,EAAG,EACHD,EAAAA,CACJ,EACAe,SAAU,CAGV,EAGAC,UAAWxC,EAAMoC,IAAI,CAAG,QAAU,OAClCK,kBAAmB,CACfC,QAAS,CACLjB,EAAGA,EAAI,AAACE,CAAAA,EAAW,CAAA,EAAKQ,EACxBX,EAAAA,CACJ,EACAmB,gBAAiB,CACblB,EAAGA,EAAIE,EAAWQ,EAClBX,EAAAA,CACJ,CACJ,CACJ,CACJ,CAKAoB,WAAY,CACR,IAAMpD,EAAS,IAAI,CAAEqD,EAAQrD,EAAOqD,KAAK,CAAE5E,EAAUuB,EAAOvB,OAAO,CAAEmC,EAAWnC,EAAQmC,QAAQ,CAAE0C,EAAoB7E,EAAQ6E,iBAAiB,CAAEC,EAAqB5E,EAAwBH,eAAe,CAACC,EAAQ+E,YAAY,EAAGC,EAAYJ,EAAMI,SAAS,CAAEC,EAAaL,EAAMK,UAAU,CAAErC,EAAS5C,EAAQ4C,MAAM,CAAEsC,EAAUzD,EAAUmB,CAAM,CAAC,EAAE,CAAEoC,GAAYrC,EAAUlB,EAAUmB,CAAM,CAAC,EAAE,CAAEqC,GAAaxB,EAAQhC,EAAUzB,EAAQyD,KAAK,CAAEuB,GAAYjC,EAAStB,EAAUzB,EAAQ+C,MAAM,CAAEkC,GAAaE,EAAY1D,EAAUzB,EAAQmF,SAAS,CAAEH,GAAYI,EAAa3D,EAAUzB,EAAQoF,UAAU,CAAEH,GAAaI,EAAQ,AAAC1C,EAAUI,EAAS,EAAKA,EAASqC,EAAYE,EAAS/D,EAAO+D,MAAM,CAAEP,EAAe3D,EAA4B0D,EAAmBlF,MAAM,CAAE6D,GAAQ8B,EAAcT,EAAmBjF,KAAK,CAAEsE,EAAQnE,CAAAA,CAAAA,AAAgC,SAAhCA,EAAQ0C,UAAU,CAAC8C,QAAQ,AAAU,EAEp0BC,EAAkB,AAACC,IACvB,IAAMC,EAAMC,KAAKD,GAAG,CAACD,EAAQ,GAAIG,EAAOD,KAAKE,GAAG,CAACC,GAAQC,EAAOJ,KAAKK,GAAG,CAACF,GACrEG,EAAInB,EAAcoB,EAAID,EAAIP,EAAKS,EAAIR,KAAKD,GAAG,CAAC,AAACC,CAAAA,KAAKS,EAAE,CAAGX,CAAI,EAAK,QAMpE,OALIS,EAAIG,GAEJJ,CAAAA,EAAIC,AADJA,CAAAA,EAAIG,CAAG,EACCX,CAAE,EAGP,CACHY,GAAI,CAACJ,EAAIN,EAAM,AAACM,CAAAA,EAFpBC,CAAAA,GAAKF,CAAAA,CAEmBE,EAAKP,EAAMM,EAAIC,EAAGD,EAAE,CACxCK,GAAI,CAACL,EAAIH,EAAM,AAACG,CAAAA,EAAIC,CAAAA,EAAKJ,EAAMG,EAAIC,EAAGD,EAAE,CACnCM,GAAG,CAAC,AAACC,GAAOvE,EAAW,CAACuE,EAAIA,EACrC,CACJ,EACIC,EAAM,EAAGC,EAAa,EAC1BC,EAAWC,EAAMC,EAAUhB,EAC3BO,EAAMU,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAqC9B,IAAK,IAAMvF,KApCXR,EAAO2B,UAAU,CAAG,SAAUK,CAAC,EAC3B,IAAMgE,EAAO5E,EAAUI,EAAS,EAChC,OAAO,AAACQ,EAAI8B,GAAStC,IAAWqC,EAC5BD,EACAA,EAAY,AAAC1B,CAAAA,EAAQ0B,CAAQ,EACxB,CAAA,EAAI,AAAC5B,CAAAA,EAAIgE,CAAE,EAAMxE,CAAAA,EAASqC,CAAS,CAAC,CACjD,EACA7D,EAAO6C,IAAI,CAAG,SAAUb,CAAC,CAAEY,CAAI,CAAEpC,CAAK,EAClC,OAAOmD,EAAU,AAACf,CAAAA,EAAO,GAAK,CAAA,EACzB,CAAA,AAAC5C,EAAO2B,UAAU,CAACf,EAAW,EAAIQ,EAAUY,EAAIA,GAAK,EACjDxB,CAAAA,EAAMC,SAAS,EAAEwF,mBAAmB9D,UACjCtC,EAA4B,IAAI,CAACpB,OAAO,CAAC0C,UAAU,EAAEgB,UAAY,EAAGD,EAAK,CAAC,CAC1F,EAEAlC,EAAOqB,MAAM,CAAG,CAACsC,EAASvC,EAASI,EAAO,CAC1CxB,EAAO2D,OAAO,CAAGA,EAqBGI,GACZvD,EAAMwB,CAAC,EAAIxB,EAAM0F,OAAO,IACvB,CAAA,CAAC5C,GAAqB9C,AAAkB,CAAA,IAAlBA,EAAM4B,OAAO,AAAS,GAC7CgD,CAAAA,GAAO5E,EAAMwB,CAAC,AAADA,EAGrB,IAAK,IAAMxB,KAASuD,EAAQ,CAgCxB,GA9BAgC,EAAK,KACLP,EAAWJ,EAAM5E,EAAMwB,CAAC,CAAGoD,EAAM,EAEjCS,EAAKH,AADLA,CAAAA,EAAKtE,EAAUI,EAAS,EAAI6D,EAAa7D,CAAK,EACpCgE,EAAWhE,EAGrBmE,EAAKF,AADLA,CAAAA,EAAK9B,EAAU2B,AADfA,CAAAA,EAAYtF,EAAO2B,UAAU,CAAC+D,EAAE,EACL,CAAA,EACjBJ,EAGVQ,EAAKF,AADLA,CAAAA,EAAKjC,EAAU2B,AADfA,CAAAA,EAAYtF,EAAO2B,UAAU,CAACkE,EAAE,EACL,CAAA,EACjBP,EAENhG,EAAaoG,IAAO5B,GACpB2B,EAAKG,EAAKjC,EAAUC,EAAY,EAChC+B,EAAKG,EAAKnC,EAAUC,EAAY,GAG3BiC,EAAK/B,IACViC,EAAKF,EAGLC,EAAKF,AADLA,CAAAA,EAAKjC,EAAU2B,AADfA,CAAAA,EAAYtF,EAAO2B,UAAU,CAACmC,EAAK,EACR,CAAA,EACjBwB,EACVO,EAAK/B,GAELlD,IACA8E,EAAK,EAAItE,EAAUsE,EACnBG,EAAK,EAAIzE,EAAUyE,EACR,OAAPE,GACAA,CAAAA,EAAK,EAAI3E,EAAU2E,CAAC,GAGxBvC,GAAiBQ,CAAAA,AAAgB,UAAhBA,GACjBxD,AAAgB,IAAhBA,EAAM2F,KAAK,EACX3F,EAAM2F,KAAK,GAAKpC,EAAO5D,MAAM,CAAG,GAChC4F,AAAO,OAAPA,CAAU,EAAI,CAGd,IAAMK,EAAI/B,KAAKgC,GAAG,CAACR,EAAKH,GAAKY,EAAQX,EAAKG,EAAIS,EAAQT,EAAKF,EAAIY,EAAQnC,KAAKoC,IAAI,CAACH,EAAQA,EAAQF,EAAIA,GAGrG5B,EAAQH,KAAKqC,IAAI,CAACJ,AAAU,IAAVA,EAAcF,EAAIE,EAAQK,KAC5C5B,EAAOyB,EAAQ,EACJ,OAAPT,GACAhB,CAAAA,EAAOV,KAAKuC,GAAG,CAAC7B,EAAMV,KAAKgC,GAAG,CAACN,EAAKF,GAAM,EAAC,EAE3CU,GAAS,GACTxB,CAAAA,EAAOV,KAAKuC,GAAG,CAAC7B,EAAMwB,EAAQ,EAAC,EAGnC,IAAIM,EAAI3C,EAAgBM,GAyBxB,GAvBIe,EADAvB,AAAgB,UAAhBA,GAA2BxD,AAAgB,IAAhBA,EAAM2F,KAAK,CAC/B,CACH,CAAC,IAAKV,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAID,EAAG,CAChB,CAGM,CACH,CAAC,IAAKD,EAAKoB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EAAKmB,EAAE5B,EAAE,CAAC,EAAE,CAAC,CACjC,CACI,IACAQ,EAAKoB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EAAKmB,EAAE5B,EAAE,CAAC,EAAE,CAC1BQ,EAAKoB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EACdD,EAAKoB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EACjB,CACD,CAAC,IAAKC,EAAKkB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EAAG,CACvB,CACI,IACAC,EAAKkB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EACdC,EAAKkB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EAAKmB,EAAE5B,EAAE,CAAC,EAAE,CAC1BU,EAAKkB,EAAE7B,EAAE,CAAC,EAAE,CAAEU,EAAKmB,EAAE5B,EAAE,CAAC,EAAE,CAC7B,CACJ,CAEDc,AAAO,OAAPA,EAAa,CAEb,IAAMe,EAAK5C,EAAgBG,KAAKS,EAAE,CAAG,GACrC+B,EAAI3C,EAAgBG,KAAKS,EAAE,CAAG,EAAIN,GAClCe,EAAKwB,IAAI,CAAC,CAAC,IAAKjB,EAAKe,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAAC,CAAE,CACzC,IACAa,EAAKe,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC1Ba,EAAID,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAChBa,EAAID,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CACnB,EACGjB,AAAgB,UAAhBA,GACAxD,EAAM2F,KAAK,GAAKpC,EAAO5D,MAAM,CAAG,EAChCoF,EAAKwB,IAAI,CAAC,CAAC,IAAKjB,EAAIC,EAAG,CAAE,CAAC,IAAKH,EAAIG,EAAG,EAGtCR,EAAKwB,IAAI,CAAC,CAAC,IAAKjB,EAAIC,EAAKe,EAAG7B,EAAE,CAAC,EAAE,CAAC,CAAE,CAChC,IACAa,EAAIC,EAAKe,EAAG7B,EAAE,CAAC,EAAE,CACjBa,EAAKgB,EAAG9B,EAAE,CAAC,EAAE,CAAEe,EACfD,EAAKgB,EAAG9B,EAAE,CAAC,EAAE,CAAEe,EAClB,CAAE,CAAC,IAAKH,EAAKkB,EAAG9B,EAAE,CAAC,EAAE,CAAEe,EAAG,CAAE,CACzB,IACAH,EAAKkB,EAAG9B,EAAE,CAAC,EAAE,CAAEe,EACfH,EAAIG,EAAKe,EAAG7B,EAAE,CAAC,EAAE,CACjBW,EAAIG,EAAKe,EAAG7B,EAAE,CAAC,EAAE,CACpB,EAELM,EAAKwB,IAAI,CAAC,CAAC,IAAKnB,EAAIC,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAAC,CAAE,CAC/B,IACAW,EAAIC,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAChBW,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC1BW,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC7B,CACL,MACSsB,GAAS,GAEdM,EAAI3C,EAAgBG,KAAKS,EAAE,CAAGN,GAC1BR,AAAgB,UAAhBA,GAA2BxD,AAAgB,IAAhBA,EAAM2F,KAAK,CACtCZ,EAAKwB,IAAI,CAAC,CAAC,IAAKjB,EAAID,EAAG,CAAE,CAAC,IAAKD,EAAIC,EAAG,EAGtCN,EAAKwB,IAAI,CAAC,CAAC,IAAKjB,EAAKe,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAAC,CAAE,CACzC,IACAa,EAAKe,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC1Ba,EAAKe,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EACdC,EAAKe,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EACjB,CAAE,CAAC,IAAKD,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAG,CAAE,CACxB,IACAD,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EACdD,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC1BW,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC7B,IAKL4B,EAAI3C,EAAgBG,KAAKS,EAAE,CAAGN,AAAQ,EAARA,GAC9Be,EAAKwB,IAAI,CAAC,CAAC,IAAKnB,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAAC,CAAE,CACzC,IACAW,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC1BW,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC1BW,EAAKiB,EAAE7B,EAAE,CAAC,EAAE,CAAEa,EAAKgB,EAAE5B,EAAE,CAAC,EAAE,CAC7B,EAET,MAGIM,EAAO,CACH,CAAC,IAAKE,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAID,EAAG,CACb,CAAC,IAAKI,EAAID,EAAG,CAChB,CACU,OAAPE,GACAR,EAAKwB,IAAI,CAAC,CAAC,IAAKjB,EAAIC,EAAG,CAAE,CAAC,IAAKH,EAAIG,EAAG,EAE1CR,EAAKwB,IAAI,CAAC,CAAC,IAAKnB,EAAIC,EAAG,EAE3BN,EAAKwB,IAAI,CAAC,CAAC,IAAI,EAEfvG,EAAMwG,SAAS,CAAG,OAClBxG,EAAMM,SAAS,CAAG,CAAEnE,EAAG4I,CAAK,EAE5B/E,EAAMyG,UAAU,CAAGzB,AAAW,IAAXA,EACnBhF,EAAM0G,KAAK,CAAGvD,EACdnD,EAAMc,KAAK,CAAG,AAACoE,CAAAA,EAAMK,CAAAA,GAAMF,CAAC,CAAC,EAAK,EAElCrF,EAAM2G,UAAU,CAAG,CACfxD,EACAnD,EAAMc,KAAK,CACd,CACDd,EAAMK,KAAK,CAAG,CACVoB,EAAG2D,EACH5D,EAAG0D,EACH5D,SAAU6D,EAAKF,EACf1D,YAAa+D,EAAKF,EAClBpE,OAAQ6C,KAAKgC,GAAG,CAAC1G,EAAKoG,EAAIF,GAAMH,GAChCxD,MAAOkF,GACX,EAEA5G,EAAM6G,KAAK,CAAGtI,EAEdyB,EAAMoC,IAAI,CAAGA,EACTpC,EAAM0F,OAAO,IACZ,CAAA,CAAC5C,GAAqB9C,AAAkB,CAAA,IAAlBA,EAAM4B,OAAO,AAAS,GAC7CiD,CAAAA,GAAcG,CAAO,CAE7B,CACAhG,EAAUQ,EAAQ,iBACtB,CAKAsH,YAAYvD,CAAM,CAAE,CAChBA,EAAOwD,IAAI,CAAC,CAAC3K,EAAG4K,IAAO5K,EAAE0E,KAAK,CAAGkG,EAAElG,KAAK,CAC5C,CACJ,CAMAf,EAAa1C,cAAc,CAAG6B,EAAmBP,EAAUtB,cAAc,CAz9B5C,CAIzB4J,UAAW,CAAA,EASXjE,aAAc,EASdnC,OAAQ,CAAC,MAAO,MAAM,CAQtBa,MAAO,MAYP0B,UAAW,MAYXpC,OAAQ,OAQRqC,WAAY,MAOZjD,SAAU,CAAA,EAKV8G,KAAM,CAAA,EACNvG,WAAY,CACRwG,eAAgB,EAChB1G,cAAe,QACnB,EAIA2G,OAAQ,CAUJC,OAAQ,CAMJrF,MAAO,UAMPsF,YAAa,SACjB,CACJ,CACJ,GA+2BAvI,EAAoBgB,EAAajD,SAAS,CAAE,CACxCyK,QAAShJ,CACb,GAMA,AAAC,SAAUwB,CAAY,EAcnB,SAASyH,IACL,IAAK,IAAMhI,KAAU,IAAI,CAACA,MAAM,CAAE,CAC9B,IAAIiI,EAAoBjI,EAAOvB,OAAO,EAAIuB,EAAOvB,OAAO,CAAC0C,UAAU,CAC/D1B,EAAQwI,IACRA,CAAAA,EAAoBA,CAAiB,CAAC,EAAE,AAAD,EAEvCjI,EAAOkI,EAAE,CAAC,QACVlI,EAAOmI,eAAe,EACtBF,GACA,CAACA,EAAkB/G,MAAM,EACzBlB,EAAOmI,eAAe,EAE9B,CACJ,CAfA5H,EAAa6H,OAAO,CALpB,SAAiBC,CAAU,EACnBzI,EAAWd,EAAU,iBACrBO,EAAsBgJ,EAAY,gCAAiCL,EAE3E,CAiBJ,EAAGzH,GAAiBA,CAAAA,EAAe,CAAC,CAAA,GACpC1B,IAA0IyJ,kBAAkB,CAAC,SAAU/H,GAM1I,IAAMgI,EAAuBhI,EAqHpD,CAAErC,MAAOsK,CAAmB,CAAE,CAAI5K,GAexC,OAAM6K,UAAsBF,EAC5B,CAkBAE,EAAc5K,cAAc,CAAG2K,EAAoBD,EAAoB1K,cAAc,CAnIvD,CAO1BgG,WAAY,KAOZD,UAAW,KAOXhD,SAAU,CAAA,CACd,GA8GA/B,IAA0IyJ,kBAAkB,CAAC,UAAWG,GAcxK,IAAMC,EAAK9K,IACX2K,EAAoBH,OAAO,CAACM,EAAEC,KAAK,EACN,IAAMjL,EAAeE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}