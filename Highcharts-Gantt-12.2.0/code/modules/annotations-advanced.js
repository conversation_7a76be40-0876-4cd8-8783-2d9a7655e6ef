!function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Templating,t._Highcharts.AST):"function"==typeof define&&define.amd?define("highcharts/modules/annotations-advanced",["highcharts/highcharts"],function(t){return i(t,t.SeriesRegistry,t.Templating,t.AST)}):"object"==typeof exports?exports["highcharts/modules/annotations-advanced"]=i(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Templating,t._Highcharts.AST):t.Highcharts=i(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Templating,t.Highcharts.AST)}("undefined"==typeof window?this:window,(t,i,s,e)=>(()=>{"use strict";var o,n,a,r,h={512:t=>{t.exports=i},660:t=>{t.exports=e},944:i=>{i.exports=t},984:t=>{t.exports=s}},l={};function p(t){var i=l[t];if(void 0!==i)return i.exports;var s=l[t]={exports:{}};return h[t](s,s.exports,p),s.exports}p.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return p.d(i,{a:i}),i},p.d=(t,i)=>{for(var s in i)p.o(i,s)&&!p.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:i[s]})},p.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i);var c={};p.d(c,{default:()=>s$});var d=p(944),u=p.n(d);let{addEvent:x,erase:g,find:y,fireEvent:f,pick:m,wrap:v}=u();function b(t,i){let s=this.initAnnotation(t);return this.options.annotations.push(s.options),m(i,!0)&&(s.redraw(),s.graphic.attr({opacity:1})),s}function A(){let t=this;t.plotBoxClip=this.renderer.clipRect(this.plotBox),t.controlPointsGroup=t.renderer.g("control-points").attr({zIndex:99}).clip(t.plotBoxClip).add(),t.options.annotations.forEach((i,s)=>{if(!t.annotations.some(t=>t.options===i)){let e=t.initAnnotation(i);t.options.annotations[s]=e.options}}),t.drawAnnotations(),x(t,"redraw",t.drawAnnotations),x(t,"destroy",function(){t.plotBoxClip.destroy(),t.controlPointsGroup.destroy()}),x(t,"exportData",function(i){let s=t.annotations,e=(this.options.exporting&&this.options.exporting.csv||{}).columnHeaderFormatter,o=!i.dataRows[1].xValues,n=t.options.lang&&t.options.lang.exportData&&t.options.lang.exportData.annotationHeader,a=i.dataRows[0].length,r=t.options.exporting&&t.options.exporting.csv&&t.options.exporting.csv.annotations&&t.options.exporting.csv.annotations.itemDelimiter,h=t.options.exporting&&t.options.exporting.csv&&t.options.exporting.csv.annotations&&t.options.exporting.csv.annotations.join;s.forEach(t=>{t.options.labelOptions&&t.options.labelOptions.includeInDataExport&&t.labels.forEach(t=>{if(t.options.text){let s=t.options.text;t.points.forEach(t=>{let e=t.x,o=t.series.xAxis?t.series.xAxis.index:-1,n=!1;if(-1===o){let t=i.dataRows[0].length,a=Array(t);for(let i=0;i<t;++i)a[i]="";a.push(s),a.xValues=[],a.xValues[o]=e,i.dataRows.push(a),n=!0}if(n||i.dataRows.forEach(t=>{!n&&t.xValues&&void 0!==o&&e===t.xValues[o]&&(h&&t.length>a?t[t.length-1]+=r+s:t.push(s),n=!0)}),!n){let t=i.dataRows[0].length,n=Array(t);for(let i=0;i<t;++i)n[i]="";n[0]=e,n.push(s),n.xValues=[],void 0!==o&&(n.xValues[o]=e),i.dataRows.push(n)}})}})});let l=0;i.dataRows.forEach(t=>{l=Math.max(l,t.length)});let p=l-i.dataRows[0].length;for(let t=0;t<p;t++){let s=function(t){let i;return e&&!1!==(i=e(t))?i:(i=n+" "+t,o)?{columnTitle:i,topLevelColumnTitle:i}:i}(t+1);o?(i.dataRows[0].push(s.topLevelColumnTitle),i.dataRows[1].push(s.columnTitle)):i.dataRows[0].push(s)}})}function P(){this.plotBoxClip.attr(this.plotBox),this.annotations.forEach(t=>{t.redraw(),t.graphic.animate({opacity:1},t.animationConfig)})}function O(t){let i=this.annotations,s="annotations"===t.coll?t:y(i,function(i){return i.options.id===t});s&&(f(s,"remove"),g(this.options.annotations,s.options),g(i,s),s.destroy())}function M(){this.annotations=[],this.options.annotations||(this.options.annotations=[])}function k(t){this.chart.hasDraggedAnnotation||t.apply(this,Array.prototype.slice.call(arguments,1))}(o||(o={})).compose=function(t,i,s){let e=i.prototype;if(!e.addAnnotation){let o=s.prototype;x(i,"afterInit",M),e.addAnnotation=b,e.callbacks.push(A),e.collectionsWithInit.annotations=[b],e.collectionsWithUpdate.push("annotations"),e.drawAnnotations=P,e.removeAnnotation=O,e.initAnnotation=function(i){let s=new(t.types[i.type]||t)(this,i);return this.annotations.push(s),s},v(o,"onContainerMouseDown",k)}};let w=o,{defined:E}=u(),{doc:C,isTouchDevice:T}=u(),{addEvent:B,fireEvent:L,objectEach:N,pick:Y,removeEvent:X}=u(),S=class{addEvents(){let t=this,i=function(i){B(i,T?"touchstart":"mousedown",i=>{t.onMouseDown(i)},{passive:!1})};if(i(this.graphic.element),(t.labels||[]).forEach(t=>{t.options.useHTML&&t.graphic.text&&!t.graphic.text.foreignObject&&i(t.graphic.text.element)}),N(t.options.events,(i,s)=>{let e=function(e){"click"===s&&t.cancelClick||i.call(t,t.chart.pointer?.normalize(e),t.target)};-1===(t.nonDOMEvents||[]).indexOf(s)?(B(t.graphic.element,s,e,{passive:!1}),t.graphic.div&&B(t.graphic.div,s,e,{passive:!1})):B(t,s,e,{passive:!1})}),t.options.draggable&&(B(t,"drag",t.onDrag),!t.graphic.renderer.styledMode)){let i={cursor:{x:"ew-resize",y:"ns-resize",xy:"move"}[t.options.draggable]};t.graphic.css(i),(t.labels||[]).forEach(t=>{t.options.useHTML&&t.graphic.text&&!t.graphic.text.foreignObject&&t.graphic.text.css(i)})}t.isUpdating||L(t,"add")}destroy(){this.removeDocEvents(),X(this),this.hcEvents=null}mouseMoveToRadians(t,i,s){let e=t.prevChartY-s,o=t.prevChartX-i,n=t.chartY-s,a=t.chartX-i,r;return this.chart.inverted&&(r=o,o=e,e=r,r=a,a=n,n=r),Math.atan2(n,a)-Math.atan2(e,o)}mouseMoveToScale(t,i,s){let e=t.prevChartX-i,o=t.prevChartY-s,n=t.chartX-i,a=t.chartY-s,r=(n||1)/(e||1),h=(a||1)/(o||1);if(this.chart.inverted){let t=h;h=r,r=t}return{x:r,y:h}}mouseMoveToTranslation(t){let i=t.chartX-t.prevChartX,s=t.chartY-t.prevChartY,e;return this.chart.inverted&&(e=s,s=i,i=e),{x:i,y:s}}onDrag(t){if(this.chart.isInsidePlot(t.chartX-this.chart.plotLeft,t.chartY-this.chart.plotTop,{visiblePlotOnly:!0})){let i=this.mouseMoveToTranslation(t);"x"===this.options.draggable&&(i.y=0),"y"===this.options.draggable&&(i.x=0),this.points.length?this.translate(i.x,i.y):(this.shapes.forEach(t=>t.translate(i.x,i.y)),this.labels.forEach(t=>t.translate(i.x,i.y))),this.redraw(!1)}}onMouseDown(t){if(t.preventDefault&&t.preventDefault(),2===t.button)return;let i=this,s=i.chart.pointer,e=t?.sourceCapabilities?.firesTouchEvents||!1,o=(t=s?.normalize(t)||t).chartX,n=t.chartY;i.cancelClick=!1,i.chart.hasDraggedAnnotation=!0,i.removeDrag=B(C,T||e?"touchmove":"mousemove",function(t){i.hasDragged=!0,(t=s?.normalize(t)||t).prevChartX=o,t.prevChartY=n,L(i,"drag",t),o=t.chartX,n=t.chartY},T||e?{passive:!1}:void 0),i.removeMouseUp=B(C,T||e?"touchend":"mouseup",function(){let t=Y(i.target&&i.target.annotation,i.target);t&&(t.cancelClick=i.hasDragged),i.cancelClick=i.hasDragged,i.chart.hasDraggedAnnotation=!1,i.hasDragged&&L(Y(t,i),"afterUpdate"),i.hasDragged=!1,i.onMouseUp()},T||e?{passive:!1}:void 0)}onMouseUp(){this.removeDocEvents()}removeDocEvents(){this.removeDrag&&(this.removeDrag=this.removeDrag()),this.removeMouseUp&&(this.removeMouseUp=this.removeMouseUp())}},{merge:I,pick:D}=u(),R=class extends S{constructor(t,i,s,e){super(),this.nonDOMEvents=["drag"],this.chart=t,this.target=i,this.options=s,this.index=D(s.index,e)}destroy(){super.destroy(),this.graphic&&(this.graphic=this.graphic.destroy()),this.chart=null,this.target=null,this.options=null}redraw(t){this.graphic[t?"animate":"attr"](this.options.positioner.call(this,this.target))}render(){let t=this.chart,i=this.options;this.graphic=t.renderer.symbol(i.symbol,0,0,i.width,i.height).add(t.controlPointsGroup).css(i.style),this.setVisibility(i.visible),this.addEvents()}setVisibility(t){this.graphic[t?"show":"hide"](),this.options.visible=t}update(t){let i=this.chart,s=this.target,e=this.index,o=I(!0,this.options,t);this.destroy(),this.constructor(i,s,o,e),this.render(i.controlPointsGroup),this.redraw()}};var F=p(512),W=p.n(F);let{series:{prototype:z}}=W(),{defined:U,fireEvent:V}=u();class H{static fromPoint(t){return new H(t.series.chart,null,{x:t.x,y:t.y,xAxis:t.series.xAxis,yAxis:t.series.yAxis})}static pointToPixels(t,i){let s=t.series,e=s.chart,o=t.plotX||0,n=t.plotY||0,a;return e.inverted&&(t.mock?(o=t.plotY,n=t.plotX):(o=e.plotWidth-(t.plotY||0),n=e.plotHeight-(t.plotX||0))),s&&!i&&(o+=(a=s.getPlotBox()).translateX,n+=a.translateY),{x:o,y:n}}static pointToOptions(t){return{x:t.x,y:t.y,xAxis:t.series.xAxis,yAxis:t.series.yAxis}}constructor(t,i,s){this.mock=!0,this.point=this,this.series={visible:!0,chart:t,getPlotBox:z.getPlotBox},this.target=i||null,this.options=s,this.applyOptions(this.getOptions())}applyOptions(t){this.command=t.command,this.setAxis(t,"x"),this.setAxis(t,"y"),this.refresh()}getOptions(){return this.hasDynamicOptions()?this.options(this.target):this.options}hasDynamicOptions(){return"function"==typeof this.options}isInsidePlot(){let t=this.plotX,i=this.plotY,s=this.series.xAxis,e=this.series.yAxis,o={x:t,y:i,isInsidePlot:!0,options:{}};return s&&(o.isInsidePlot=U(t)&&t>=0&&t<=s.len),e&&(o.isInsidePlot=o.isInsidePlot&&U(i)&&i>=0&&i<=e.len),V(this.series.chart,"afterIsInsidePlot",o),o.isInsidePlot}refresh(){let t=this.series,i=t.xAxis,s=t.yAxis,e=this.getOptions();i?(this.x=e.x,this.plotX=i.toPixels(e.x,!0)):(this.x=void 0,this.plotX=e.x),s?(this.y=e.y,this.plotY=s.toPixels(e.y,!0)):(this.y=null,this.plotY=e.y),this.isInside=this.isInsidePlot()}refreshOptions(){let t=this.series,i=t.xAxis,s=t.yAxis;this.x=this.options.x=i?this.options.x=i.toValue(this.plotX,!0):this.plotX,this.y=this.options.y=s?s.toValue(this.plotY,!0):this.plotY}rotate(t,i,s){if(!this.hasDynamicOptions()){let e=Math.cos(s),o=Math.sin(s),n=this.plotX-t,a=this.plotY-i;this.plotX=n*e-a*o+t,this.plotY=n*o+a*e+i,this.refreshOptions()}}scale(t,i,s,e){if(!this.hasDynamicOptions()){let o=this.plotX*s,n=this.plotY*e;this.plotX=(1-s)*t+o,this.plotY=(1-e)*i+n,this.refreshOptions()}}setAxis(t,i){let s=i+"Axis",e=t[s],o=this.series.chart;this.series[s]="object"==typeof e?e:U(e)?o[s][e]||o.get(e):null}toAnchor(){let t=[this.plotX,this.plotY,0,0];return this.series.chart.inverted&&(t[0]=this.plotY,t[1]=this.plotX),t}translate(t,i,s,e){this.hasDynamicOptions()||(this.plotX+=s,this.plotY+=e,this.refreshOptions())}}!function(t){function i(){let t=this.controlPoints,i=this.options.controlPoints||[];i.forEach((s,e)=>{let o=u().merge(this.options.controlPointOptions,s);o.index||(o.index=e),i[e]=o,t.push(new R(this.chart,this,o))})}function s(t){let i=t.series.getPlotBox(),s=t.series.chart,e=t.mock?t.toAnchor():s.tooltip&&s.tooltip.getAnchor.call({chart:t.series.chart},t)||[0,0,0,0],o={x:e[0]+(this.options.x||0),y:e[1]+(this.options.y||0),height:e[2]||0,width:e[3]||0};return{relativePosition:o,absolutePosition:u().merge(o,{x:o.x+(t.mock?i.translateX:s.plotLeft),y:o.y+(t.mock?i.translateY:s.plotTop)})}}function e(){this.controlPoints.forEach(t=>t.destroy()),this.chart=null,this.controlPoints=null,this.points=null,this.options=null,this.annotation&&(this.annotation=null)}function o(){let t=this.options;return t.points||t.point&&u().splat(t.point)}function n(){let t,i,s=this.getPointsOptions(),e=this.points,o=s&&s.length||0;for(t=0;t<o;t++){if(!(i=this.point(s[t],e[t]))){e.length=0;return}i.mock&&i.refresh(),e[t]=i}return e}function a(t,i){if(t&&t.series)return t;if(!i||null===i.series){if(u().isObject(t))i=new H(this.chart,this,t);else if(u().isString(t))i=this.chart.get(t)||null;else if("function"==typeof t){let s=t.call(i,this);i=s.series?s:new H(this.chart,this,t)}}return i}function r(t){this.controlPoints.forEach(i=>i.redraw(t))}function h(){this.controlPoints.forEach(t=>t.render())}function l(t,i,s,e,o){if(this.chart.inverted){let t=i;i=s,s=t}this.points.forEach((n,a)=>this.transformPoint(t,i,s,e,o,a),this)}function p(t,i,s,e,o,n){let a=this.points[n];a.mock||(a=this.points[n]=H.fromPoint(a)),a[t](i,s,e,o)}function c(t,i){this.transform("translate",null,null,t,i)}function d(t,i,s){this.transformPoint("translate",null,null,t,i,s)}t.compose=function(t){let x=t.prototype;x.addControlPoints||u().merge(!0,x,{addControlPoints:i,anchor:s,destroyControlTarget:e,getPointsOptions:o,linkPoints:n,point:a,redrawControlPoints:r,renderControlPoints:h,transform:l,transformPoint:p,translate:c,translatePoint:d})}}(n||(n={}));let j=n,{merge:q}=u();class _{constructor(t,i,s,e){this.annotation=t,this.chart=t.chart,this.collection="label"===e?"labels":"shapes",this.controlPoints=[],this.options=i,this.points=[],this.index=s,this.itemType=e,this.init(t,i,s)}attr(...t){this.graphic.attr.apply(this.graphic,arguments)}attrsFromOptions(t){let i,s,e=this.constructor.attrsMap,o={},n=this.chart.styledMode;for(i in t)s=e[i],void 0===e[i]||n&&-1!==["fill","stroke","stroke-width"].indexOf(s)||(o[s]=t[i]);return o}destroy(){this.graphic&&(this.graphic=this.graphic.destroy()),this.tracker&&(this.tracker=this.tracker.destroy()),this.destroyControlTarget()}init(t,i,s){this.annotation=t,this.chart=t.chart,this.options=i,this.points=[],this.controlPoints=[],this.index=s,this.linkPoints(),this.addControlPoints()}redraw(t){this.redrawControlPoints(t)}render(t){this.options.className&&this.graphic&&this.graphic.addClass(this.options.className),this.renderControlPoints()}rotate(t,i,s){this.transform("rotate",t,i,s)}scale(t,i,s,e){this.transform("scale",t,i,s,e)}setControlPointsVisibility(t){this.controlPoints.forEach(i=>{i.setVisibility(t)})}shouldBeDrawn(){return!!this.points.length}translateShape(t,i,s){let e=this.annotation.chart,o=this.annotation.userOptions,n=e.annotations.indexOf(this.annotation),a=e.options.annotations[n];this.translatePoint(t,i,0),s&&this.translatePoint(t,i,1),a[this.collection][this.index].point=this.options.point,o[this.collection][this.index].point=this.options.point}update(t){let i=this.annotation,s=q(!0,this.options,t),e=this.graphic.parentGroup,o=this.constructor;this.destroy(),q(!0,this,new o(i,s,this.index,this.itemType)),this.render(e),this.redraw()}}j.compose(_);let G=_,{defaultMarkers:K}={defaultMarkers:{arrow:{tagName:"marker",attributes:{id:"arrow",refY:5,refX:9,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 0 L 10 5 L 0 10 Z","stroke-width":0}}]},"reverse-arrow":{tagName:"marker",attributes:{id:"reverse-arrow",refY:5,refX:1,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 5 L 10 0 L 10 10 Z","stroke-width":0}}]}}},{addEvent:Z,defined:$,extend:J,merge:Q,uniqueKey:tt}=u(),ti=to("marker-end"),ts=to("marker-start"),te="rgba(192,192,192,"+(u().svg?1e-4:.002)+")";function to(t){return function(i){this.attr(t,"url(#"+i+")")}}function tn(){this.options.defs=Q(K,this.options.defs||{})}function ta(t,i){let s={attributes:{id:t}},e={stroke:i.color||"none",fill:i.color||"rgba(0, 0, 0, 0.75)"};s.children=i.children&&i.children.map(function(t){return Q(e,t)});let o=Q(!0,{attributes:{markerWidth:20,markerHeight:20,refX:0,refY:0,orient:"auto"}},i,s),n=this.definition(o);return n.id=t,n}class tr extends G{static compose(t,i){let s=i.prototype;s.addMarker||(Z(t,"afterGetContainer",tn),s.addMarker=ta)}constructor(t,i,s){super(t,i,s,"shape"),this.type="path"}toD(){let t=this.options.d;if(t)return"function"==typeof t?t.call(this):t;let i=this.points,s=i.length,e=[],o=s,n=i[0],a=o&&this.anchor(n).absolutePosition,r=0,h;if(a)for(e.push(["M",a.x,a.y]);++r<s&&o;)h=(n=i[r]).command||"L",a=this.anchor(n).absolutePosition,"M"===h?e.push([h,a.x,a.y]):"L"===h?e.push([h,a.x,a.y]):"Z"===h&&e.push([h]),o=n.series.visible;return o&&this.graphic?this.chart.renderer.crispLine(e,this.graphic.strokeWidth()):null}shouldBeDrawn(){return super.shouldBeDrawn()||!!this.options.d}render(t){let i=this.options,s=this.attrsFromOptions(i);this.graphic=this.annotation.chart.renderer.path([["M",0,0]]).attr(s).add(t),this.tracker=this.annotation.chart.renderer.path([["M",0,0]]).addClass("highcharts-tracker-line").attr({zIndex:2}).add(t),this.annotation.chart.styledMode||this.tracker.attr({"stroke-linejoin":"round",stroke:te,fill:te,"stroke-width":this.graphic.strokeWidth()+2*i.snap}),super.render(),J(this.graphic,{markerStartSetter:ts,markerEndSetter:ti}),this.setMarkers(this)}redraw(t){if(this.graphic){let i=this.toD(),s=t?"animate":"attr";i?(this.graphic[s]({d:i}),this.tracker[s]({d:i})):(this.graphic.attr({d:"M 0 -9000000000"}),this.tracker.attr({d:"M 0 -9000000000"})),this.graphic.placed=this.tracker.placed=!!i}super.redraw(t)}setMarkers(t){let i=t.options,s=t.chart,e=s.options.defs,o=i.fill,n=$(o)&&"none"!==o?o:i.stroke;["markerStart","markerEnd"].forEach(function(o){let a,r,h,l,p=i[o];if(p){for(h in e)if((p===((a=e[h]).attributes&&a.attributes.id)||p===a.id)&&"marker"===a.tagName){r=a;break}r&&(l=t[o]=s.renderer.addMarker((i.id||tt())+"-"+p,Q(r,{color:n})),t.attr(o,l.getAttribute("id")))}})}}tr.attrsMap={dashStyle:"dashstyle",strokeWidth:"stroke-width",stroke:"stroke",fill:"fill",zIndex:"zIndex"};let{merge:th}=u();class tl extends G{constructor(t,i,s){super(t,i,s,"shape"),this.type="rect",this.translate=super.translateShape}render(t){let i=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.rect(0,-9e9,0,0).attr(i).add(t),super.render()}redraw(t){if(this.graphic){let i=this.anchor(this.points[0]).absolutePosition;i?this.graphic[t?"animate":"attr"]({x:i.x,y:i.y,width:this.options.width,height:this.options.height}):this.attr({x:0,y:-9e9}),this.graphic.placed=!!i}super.redraw(t)}}tl.attrsMap=th(tr.attrsMap,{width:"width",height:"height"});let{merge:tp}=u();class tc extends G{constructor(t,i,s){super(t,i,s,"shape"),this.type="circle",this.translate=super.translateShape}redraw(t){if(this.graphic){let i=this.anchor(this.points[0]).absolutePosition;i?this.graphic[t?"animate":"attr"]({x:i.x,y:i.y,r:this.options.r}):this.graphic.attr({x:0,y:-9e9}),this.graphic.placed=!!i}super.redraw.call(this,t)}render(t){let i=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.circle(0,-9e9,0).attr(i).add(t),super.render()}setRadius(t){this.options.r=t}}tc.attrsMap=tp(tr.attrsMap,{r:"r"});let{merge:td,defined:tu}=u();class tx extends G{constructor(t,i,s){super(t,i,s,"shape"),this.type="ellipse"}init(t,i,s){tu(i.yAxis)&&i.points.forEach(t=>{t.yAxis=i.yAxis}),tu(i.xAxis)&&i.points.forEach(t=>{t.xAxis=i.xAxis}),super.init(t,i,s)}render(t){this.graphic=this.annotation.chart.renderer.createElement("ellipse").attr(this.attrsFromOptions(this.options)).add(t),super.render()}translate(t,i){super.translateShape(t,i,!0)}getDistanceFromLine(t,i,s,e){return Math.abs((i.y-t.y)*s-(i.x-t.x)*e+i.x*t.y-i.y*t.x)/Math.sqrt((i.y-t.y)*(i.y-t.y)+(i.x-t.x)*(i.x-t.x))}getAttrs(t,i){let s=t.x,e=t.y,o=i.x,n=i.y,a=(s+o)/2,r=Math.sqrt((s-o)*(s-o)/4+(e-n)*(e-n)/4),h=180*Math.atan((n-e)/(o-s))/Math.PI;return a<s&&(h+=180),{cx:a,cy:(e+n)/2,rx:r,ry:this.getRY(),angle:h}}getRY(){let t=this.getYAxis();return tu(t)?Math.abs(t.toPixels(this.options.ry)-t.toPixels(0)):this.options.ry}getYAxis(){let t=this.options.yAxis;return this.chart.yAxis[t]}getAbsolutePosition(t){return this.anchor(t).absolutePosition}redraw(t){if(this.graphic){let i=this.getAbsolutePosition(this.points[0]),s=this.getAbsolutePosition(this.points[1]),e=this.getAttrs(i,s);i?this.graphic[t?"animate":"attr"]({cx:e.cx,cy:e.cy,rx:e.rx,ry:e.ry,rotation:e.angle,rotationOriginX:e.cx,rotationOriginY:e.cy}):this.graphic.attr({x:0,y:-9e9}),this.graphic.placed=!!i}super.redraw(t)}setYRadius(t){let i=this.annotation.userOptions.shapes;this.options.ry=t,i&&i[0]&&(i[0].ry=t,i[0].ry=t)}}tx.attrsMap=td(tr.attrsMap,{ry:"ry"});var tg=p(984),ty=p.n(tg);let{format:tf}=ty(),{extend:tm,getAlignFactor:tv,isNumber:tb,pick:tA}=u();function tP(t,i,s,e,o){let n=o&&o.anchorX,a=o&&o.anchorY,r,h,l=s/2;return tb(n)&&tb(a)&&(r=[["M",n,a]],(h=i-a)<0&&(h=-e-h),h<s&&(l=n<t+s/2?h:s-h),a>i+e?r.push(["L",t+l,i+e]):a<i?r.push(["L",t+l,i]):n<t?r.push(["L",t,i+e/2]):n>t+s&&r.push(["L",t+s,i+e/2])),r||[]}class tO extends G{static alignedPosition(t,i){return{x:Math.round((i.x||0)+(t.x||0)+(i.width-(t.width||0))*tv(t.align)),y:Math.round((i.y||0)+(t.y||0)+(i.height-(t.height||0))*tv(t.verticalAlign))}}static compose(t){t.prototype.symbols.connector=tP}static justifiedOptions(t,i,s,e){let o,n=s.align,a=s.verticalAlign,r=i.box?0:i.padding||0,h=i.getBBox(),l={align:n,verticalAlign:a,x:s.x,y:s.y,width:i.width,height:i.height},p=(e.x||0)-t.plotLeft,c=(e.y||0)-t.plotTop;return(o=p+r)<0&&("right"===n?l.align="left":l.x=(l.x||0)-o),(o=p+h.width-r)>t.plotWidth&&("left"===n?l.align="right":l.x=(l.x||0)+t.plotWidth-o),(o=c+r)<0&&("bottom"===a?l.verticalAlign="top":l.y=(l.y||0)-o),(o=c+h.height-r)>t.plotHeight&&("top"===a?l.verticalAlign="bottom":l.y=(l.y||0)+t.plotHeight-o),l}constructor(t,i,s){super(t,i,s,"label")}translatePoint(t,i){super.translatePoint(t,i,0)}translate(t,i){let s=this.annotation.chart,e=this.annotation.userOptions,o=s.annotations.indexOf(this.annotation),n=s.options.annotations[o];if(s.inverted){let s=t;t=i,i=s}this.options.x+=t,this.options.y+=i,n[this.collection][this.index].x=this.options.x,n[this.collection][this.index].y=this.options.y,e[this.collection][this.index].x=this.options.x,e[this.collection][this.index].y=this.options.y}render(t){let i=this.options,s=this.attrsFromOptions(i),e=i.style;this.graphic=this.annotation.chart.renderer.label("",0,-9999,i.shape,null,null,i.useHTML,null,"annotation-label").attr(s).add(t),this.annotation.chart.styledMode||("contrast"===e.color&&(e.color=this.annotation.chart.renderer.getContrast(tO.shapesWithoutBackground.indexOf(i.shape)>-1?"#FFFFFF":i.backgroundColor)),this.graphic.css(i.style).shadow(i.shadow)),this.graphic.labelrank=i.labelrank,super.render()}redraw(t){let i=this.options,s=this.text||i.format||i.text,e=this.graphic,o=this.points[0];if(!e){this.redraw(t);return}e.attr({text:s?tf(String(s),o,this.annotation.chart):i.formatter.call(o,this)});let n=this.anchor(o),a=this.position(n);a?(e.alignAttr=a,a.anchorX=n.absolutePosition.x,a.anchorY=n.absolutePosition.y,e[t?"animate":"attr"](a)):e.attr({x:0,y:-9999}),e.placed=!!a,super.redraw(t)}anchor(t){let i=super.anchor.apply(this,arguments),s=this.options.x||0,e=this.options.y||0;return i.absolutePosition.x-=s,i.absolutePosition.y-=e,i.relativePosition.x-=s,i.relativePosition.y-=e,i}position(t){let i=this.graphic,s=this.annotation.chart,e=s.tooltip,o=this.points[0],n=this.options,a=t.absolutePosition,r=t.relativePosition,h,l,p,c,d=o.series.visible&&H.prototype.isInsidePlot.call(o);if(i&&d){let{width:t=0,height:u=0}=i;n.distance&&e?h=e.getPosition.call({chart:s,distance:tA(n.distance,16),getPlayingField:e.getPlayingField,pointer:e.pointer},t,u,{plotX:r.x,plotY:r.y,negative:o.negative,ttBelow:o.ttBelow,h:r.height||r.width}):n.positioner?h=n.positioner.call(this):(l={x:a.x,y:a.y,width:0,height:0},h=tO.alignedPosition(tm(n,{width:t,height:u}),l),"justify"===this.options.overflow&&(h=tO.alignedPosition(tO.justifiedOptions(s,i,n,h),l))),n.crop&&(p=h.x-s.plotLeft,c=h.y-s.plotTop,d=s.isInsidePlot(p,c)&&s.isInsidePlot(p+t,c+u))}return d?h:null}}tO.attrsMap={backgroundColor:"fill",borderColor:"stroke",borderWidth:"stroke-width",zIndex:"zIndex",borderRadius:"r",padding:"padding"},tO.shapesWithoutBackground=["connector"];class tM extends G{constructor(t,i,s){super(t,i,s,"shape"),this.type="image",this.translate=super.translateShape}render(t){let i=this.attrsFromOptions(this.options),s=this.options;this.graphic=this.annotation.chart.renderer.image(s.src,0,-9e9,s.width,s.height).attr(i).add(t),this.graphic.width=s.width,this.graphic.height=s.height,super.render()}redraw(t){if(this.graphic){let i=this.anchor(this.points[0]),s=tO.prototype.position.call(this,i);s?this.graphic[t?"animate":"attr"]({x:s.x,y:s.y}):this.graphic.attr({x:0,y:-9e9}),this.graphic.placed=!!s}super.redraw(t)}}tM.attrsMap={width:"width",height:"height",zIndex:"zIndex"};var tk=p(660),tw=p.n(tk);let{addEvent:tE,createElement:tC}=u(),tT=class{constructor(t,i){this.iconsURL=i,this.container=this.createPopupContainer(t),this.closeButton=this.addCloseButton()}createPopupContainer(t,i="highcharts-popup highcharts-no-tooltip"){return tC("div",{className:i},void 0,t)}addCloseButton(t="highcharts-popup-close"){let i=this,s=this.iconsURL,e=tC("button",{className:t},void 0,this.container);return e.style["background-image"]="url("+(s.match(/png|svg|jpeg|jpg|gif/ig)?s:s+"close.svg")+")",["click","touchstart"].forEach(t=>{tE(e,t,i.closeButtonEvents.bind(i))}),tE(document,"keydown",function(t){"Escape"===t.code&&i.closeButtonEvents()}),e}closeButtonEvents(){this.closePopup()}showPopup(t="highcharts-annotation-toolbar"){let i=this.container,s=this.closeButton;this.type=void 0,i.innerHTML=tw().emptyHTML,i.className.indexOf(t)>=0&&(i.classList.remove(t),i.removeAttribute("style")),i.appendChild(s),i.style.display="block",i.style.height=""}closePopup(){this.container.style.display="none"}},{doc:tB,isFirefox:tL}=u(),{createElement:tN,isArray:tY,isObject:tX,objectEach:tS,pick:tI,stableSort:tD}=u();function tR(t,i,s,e,o,n){let a,r;if(!i)return;let h=this.addInput,l=this.lang;tS(e,(e,n)=>{a=""!==s?s+"."+n:n,tX(e)&&(!tY(e)||tY(e)&&tX(e[0])?((r=l[n]||n).match(/\d/g)||o.push([!0,r,t]),tR.call(this,t,i,a,e,o,!1)):o.push([this,a,"annotation",t,e]))}),n&&(tD(o,t=>t[1].match(/format/g)?-1:1),tL&&o.reverse(),o.forEach(t=>{!0===t[0]?tN("span",{className:"highcharts-annotation-title"},void 0,t[2]).appendChild(tB.createTextNode(t[1])):(t[4]={value:t[4][0],type:t[4][1]},h.apply(t[0],t.splice(1)))}))}let{doc:tF}=u(),{seriesTypes:tW}=W(),{addEvent:tz,createElement:tU,defined:tV,isArray:tH,isObject:tj,objectEach:tq,stableSort:t_}=u();!function(t){t[t["params.algorithm"]=0]="params.algorithm",t[t["params.average"]=1]="params.average"}(a||(a={}));let tG={"algorithm-pivotpoints":["standard","fibonacci","camarilla"],"average-disparityindex":["sma","ema","dema","tema","wma"]};function tK(t){let i=tU("div",{className:"highcharts-popup-lhs-col"},void 0,t),s=tU("div",{className:"highcharts-popup-rhs-col"},void 0,t);return tU("div",{className:"highcharts-popup-rhs-col-wrapper"},void 0,s),{lhsCol:i,rhsCol:s}}function tZ(t,i,s,e){let o=i.params||i.options.params;e.innerHTML=tw().emptyHTML,tU("h3",{className:"highcharts-indicator-title"},void 0,e).appendChild(tF.createTextNode(t4(i,s).indicatorFullName)),tU("input",{type:"hidden",name:"highcharts-type-"+s,value:s},void 0,e),t5.call(this,s,"series",t,e,i,i.linkedParent&&i.linkedParent.options.id),o.volumeSeriesID&&t5.call(this,s,"volume",t,e,i,i.linkedParent&&o.volumeSeriesID),tJ.call(this,t,"params",o,s,e)}function t$(t,i,s,e){function o(i,s){let e=x.parentNode.children[1];tZ.call(n,t,i,s,x),e&&(e.style.display="block"),l&&i.options&&tU("input",{type:"hidden",name:"highcharts-id-"+s,value:i.options.id},void 0,x).setAttribute("highcharts-data-series-id",i.options.id)}let n=this,a=n.lang,r=i.querySelectorAll(".highcharts-popup-lhs-col")[0],h=i.querySelectorAll(".highcharts-popup-rhs-col")[0],l="edit"===s,p=l?t.series:t.options.plotOptions||{};if(!t&&p)return;let c,d=[];l||tH(p)?tH(p)&&(d=t9.call(this,p)):d=t2.call(this,p,e),t_(d,(t,i)=>{let s=t.indicatorFullName.toLowerCase(),e=i.indicatorFullName.toLowerCase();return s<e?-1:+(s>e)}),r.children[1]&&r.children[1].remove();let u=tU("ul",{className:"highcharts-indicator-list"},void 0,r),x=h.querySelectorAll(".highcharts-popup-rhs-col-wrapper")[0];if(d.forEach(t=>{let{indicatorFullName:i,indicatorType:s,series:e}=t;c=tU("li",{className:"highcharts-indicator-list"},void 0,u);let n=tU("button",{className:"highcharts-indicator-list-item",textContent:i},void 0,c);["click","touchstart"].forEach(t=>{tz(n,t,function(){o(e,s)})})}),d.length>0){let{series:t,indicatorType:i}=d[0];o(t,i)}else l||(tw().setElementHTML(x.parentNode.children[0],a.noFilterMatch||""),x.parentNode.children[1].style.display="none")}function tJ(t,i,s,e,o){if(!t)return;let n=this.addInput;tq(s,(s,r)=>{let h=i+"."+r;if(tV(s)&&h){if(tj(s)&&(n.call(this,h,e,o,{}),tJ.call(this,t,h,s,e,o)),h in a){let n=t0.call(this,e,h,o);t1.call(this,t,i,n,e,r,s)}else"params.volumeSeriesID"===h||tH(s)||n.call(this,h,e,o,{value:s,type:"number"})}})}function tQ(t,i){let s=this,e=i.querySelectorAll(".highcharts-popup-lhs-col")[0],o=this.lang.clearFilter,n=tU("div",{className:"highcharts-input-wrapper"},void 0,e),a=function(i){t$.call(s,t,s.container,"add",i)},r=this.addInput("searchIndicators","input",n,{value:"",type:"text",htmlFor:"search-indicators",labelClassName:"highcharts-input-search-indicators-label"}),h=tU("a",{textContent:o},void 0,n);r.classList.add("highcharts-input-search-indicators"),h.classList.add("clear-filter-button"),tz(r,"input",function(){a(this.value),this.value.length?h.style.display="inline-block":h.style.display="none"}),["click","touchstart"].forEach(t=>{tz(h,t,function(){r.value="",a(""),h.style.display="none"})})}function t0(t,i,s){let e=i.split("."),o=e[e.length-1],n="highcharts-"+i+"-type-"+t,a=this.lang;tU("label",{htmlFor:n},null,s).appendChild(tF.createTextNode(a[o]||i));let r=tU("select",{name:n,className:"highcharts-popup-field",id:"highcharts-select-"+i},null,s);return r.setAttribute("id","highcharts-select-"+i),r}function t1(t,i,s,e,o,n,a){"series"===i||"volume"===i?t.series.forEach(t=>{let e=t.options,o=e.name||e.params?t.name:e.id||"";"highcharts-navigator-series"!==e.id&&e.id!==(a&&a.options&&a.options.id)&&(tV(n)||"volume"!==i||"column"!==t.type||(n=e.id),tU("option",{value:e.id},void 0,s).appendChild(tF.createTextNode(o)))}):e&&o&&tG[o+"-"+e].forEach(t=>{tU("option",{value:t},void 0,s).appendChild(tF.createTextNode(t))}),tV(n)&&(s.value=n)}function t2(t,i){let s,e=this.chart&&this.chart.options.lang,o=e&&e.navigation&&e.navigation.popup&&e.navigation.popup.indicatorAliases,n=[];return tq(t,(t,e)=>{let a=t&&t.options;if(t.params||a&&a.params){let{indicatorFullName:a,indicatorType:r}=t4(t,e);if(i){let e=RegExp(i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"i"),h=o&&o[r]&&o[r].join(" ")||"";(a.match(e)||h.match(e))&&(s={indicatorFullName:a,indicatorType:r,series:t},n.push(s))}else s={indicatorFullName:a,indicatorType:r,series:t},n.push(s)}}),n}function t9(t){let i=[];return t.forEach(t=>{t.is("sma")&&i.push({indicatorFullName:t.name,indicatorType:t.type,series:t})}),i}function t4(t,i){let s=t.options,e=tW[i]&&tW[i].prototype.nameBase||i.toUpperCase(),o=i;return s&&s.type&&(o=t.options.type,e=t.name),{indicatorFullName:e,indicatorType:o}}function t5(t,i,s,e,o,n){if(!s)return;let a=t0.call(this,t,i,e);t1.call(this,s,i,a,void 0,void 0,void 0,o),tV(n)&&(a.value=n)}let{doc:t3}=u(),{addEvent:t6,createElement:t7}=u();function t8(){return t7("div",{className:"highcharts-tab-item-content highcharts-no-mousewheel"},void 0,this.container)}function it(t,i){let s=this.container,e=this.lang,o="highcharts-tab-item";0===i&&(o+=" highcharts-tab-disabled");let n=t7("button",{className:o},void 0,s);return n.appendChild(t3.createTextNode(e[t+"Button"]||t)),n.setAttribute("highcharts-data-tab-type",t),n}function ii(){let t=this.container,i=t.querySelectorAll(".highcharts-tab-item"),s=t.querySelectorAll(".highcharts-tab-item-content");for(let t=0;t<i.length;t++)i[t].classList.remove("highcharts-tab-item-active"),s[t].classList.remove("highcharts-tab-item-show")}function is(t,i){let s=this.container.querySelectorAll(".highcharts-tab-item-content");t.className+=" highcharts-tab-item-active",s[i].className+=" highcharts-tab-item-show"}function ie(t){let i=this;this.container.querySelectorAll(".highcharts-tab-item").forEach((s,e)=>{(0!==t||"edit"!==s.getAttribute("highcharts-data-tab-type"))&&["click","touchstart"].forEach(t=>{t6(s,t,function(){ii.call(i),is.call(i,this,e)})})})}let{doc:io}=u(),{getOptions:ia}=u(),{addEvent:ir,createElement:ih,extend:il,fireEvent:ip,pick:ic}=u();class id extends tT{constructor(t,i,s){super(t,i),this.chart=s,this.lang=(ia().lang.navigation||{}).popup||{},ir(this.container,"mousedown",()=>{let t=s&&s.navigationBindings&&s.navigationBindings.activeAnnotation;if(t){t.cancelClick=!0;let i=ir(io,"click",()=>{setTimeout(()=>{t.cancelClick=!1},0),i()})}})}addInput(t,i,s,e){let o=t.split("."),n=o[o.length-1],a=this.lang,r="highcharts-"+i+"-"+ic(e.htmlFor,n);n.match(/^\d+$/)||ih("label",{htmlFor:r,className:e.labelClassName},void 0,s).appendChild(io.createTextNode(a[n]||n));let h=ih("input",{name:r,value:e.value,type:e.type,className:"highcharts-popup-field"},void 0,s);return h.setAttribute("highcharts-data-name",t),h}closeButtonEvents(){if(this.chart){let t=this.chart.navigationBindings;ip(t,"closePopup"),t&&t.selectedButtonElement&&ip(t,"deselectButton",{button:t.selectedButtonElement})}else super.closeButtonEvents()}addButton(t,i,s,e,o){let n=ih("button",void 0,void 0,t);return n.appendChild(io.createTextNode(i)),o&&["click","touchstart"].forEach(t=>{ir(n,t,()=>(this.closePopup(),o(function(t,i){let s=Array.prototype.slice.call(t.querySelectorAll("input")),e=Array.prototype.slice.call(t.querySelectorAll("select")),o=t.querySelectorAll("#highcharts-select-series > option:checked")[0],n=t.querySelectorAll("#highcharts-select-volume > option:checked")[0],a={actionType:i,linkedTo:o&&o.getAttribute("value")||"",fields:{}};return s.forEach(t=>{let i=t.getAttribute("highcharts-data-name");t.getAttribute("highcharts-data-series-id")?a.seriesId=t.value:i?a.fields[i]=t.value:a.type=t.value}),e.forEach(t=>{let i=t.id;if("highcharts-select-series"!==i&&"highcharts-select-volume"!==i){let s=i.split("highcharts-select-")[1];a.fields[s]=t.value}}),n&&(a.fields["params.volumeSeriesID"]=n.getAttribute("value")||""),a}(e,s))))}),n}showForm(t,i,s,e){i&&(this.showPopup(),"indicators"===t&&this.indicators.addForm.call(this,i,s,e),"annotation-toolbar"===t&&this.annotations.addToolbar.call(this,i,s,e),"annotation-edit"===t&&this.annotations.addForm.call(this,i,s,e),"flag"===t&&this.annotations.addForm.call(this,i,s,e,!0),this.type=t,this.container.style.height=this.container.offsetHeight+"px")}}il(id.prototype,{annotations:{addForm:function(t,i,s,e){if(!t)return;let o=this.container,n=this.lang,a=tN("h2",{className:"highcharts-popup-main-title"},void 0,o);a.appendChild(tB.createTextNode(n[i.langKey]||i.langKey||"")),a=tN("div",{className:"highcharts-popup-lhs-col highcharts-popup-lhs-full"},void 0,o);let r=tN("div",{className:"highcharts-popup-bottom-row"},void 0,o);tR.call(this,a,t,"",i,[],!0),this.addButton(r,e?n.addButton||"Add":n.saveButton||"Save",e?"add":"save",o,s)},addToolbar:function(t,i,s){let e=this.lang,o=this.container,n=this.showForm,a="highcharts-annotation-toolbar";-1===o.className.indexOf(a)&&(o.className+=" "+a+" highcharts-no-mousewheel"),t&&(o.style.top=t.plotTop+10+"px");let r=tN("p",{className:"highcharts-annotation-label"},void 0,o);r.setAttribute("aria-label","Annotation type"),r.appendChild(tB.createTextNode(tI(e[i.langKey]||i.langKey,i.shapes&&i.shapes[0].type,"")));let h=this.addButton(o,e.editButton||"Edit","edit",o,()=>{n.call(this,"annotation-edit",t,i,s)});h.className+=" highcharts-annotation-edit-button",h.style["background-image"]="url("+this.iconsURL+"edit.svg)",h=this.addButton(o,e.removeButton||"Remove","remove",o,s),h.className+=" highcharts-annotation-remove-button",h.style["background-image"]="url("+this.iconsURL+"destroy.svg)"}},indicators:{addForm:function(t,i,s){let e,o=this.lang;if(!t)return;this.tabs.init.call(this,t);let n=this.container.querySelectorAll(".highcharts-tab-item-content");tK(n[0]),tQ.call(this,t,n[0]),t$.call(this,t,n[0],"add"),e=n[0].querySelectorAll(".highcharts-popup-rhs-col")[0],this.addButton(e,o.addButton||"add","add",e,s),tK(n[1]),t$.call(this,t,n[1],"edit"),e=n[1].querySelectorAll(".highcharts-popup-rhs-col")[0],this.addButton(e,o.saveButton||"save","edit",e,s),this.addButton(e,o.removeButton||"remove","remove",e,s)},getAmount:function(){let t=0;return this.series.forEach(i=>{(i.params||i.options.params)&&t++}),t}},tabs:{init:function(t){if(!t)return;let i=this.indicators.getAmount.call(t),s=it.call(this,"add");it.call(this,"edit",i),t8.call(this),t8.call(this),ie.call(this,i),is.call(this,s,0)}}});let{composed:iu}=u(),{addEvent:ix,pushUnique:ig,wrap:iy}=u();function im(){this.popup&&this.popup.closePopup()}function iv(t){this.popup||(this.popup=new id(this.chart.container,this.chart.options.navigation.iconsURL||this.chart.options.stockTools&&this.chart.options.stockTools.gui.iconsURL||"https://code.highcharts.com/12.2.0/gfx/stock-icons/",this.chart)),this.popup.showForm(t.formType,this.chart,t.options,t.onSubmit)}function ib(t,i){this.inClass(i.target,"highcharts-popup")||t.apply(this,Array.prototype.slice.call(arguments,1))}let iA={compose:function(t,i){ig(iu,"Popup")&&(ix(t,"closePopup",im),ix(t,"showPopup",iv),iy(i.prototype,"onContainerMouseDown",ib))}},{getDeferredAnimation:iP}=u(),{destroyObjectProperties:iO,erase:iM,fireEvent:ik,merge:iw,pick:iE,splat:iC}=u();function iT(t,i){let s={};return["labels","shapes"].forEach(e=>{let o=t[e],n=i[e];o&&(n?s[e]=iC(n).map((t,i)=>iw(o[i],t)):s[e]=t[e])}),s}class iB extends S{static compose(t,i,s,e){w.compose(iB,t,s),tO.compose(e),tr.compose(t,e),i.compose(iB,t),iA.compose(i,s)}constructor(t,i){super(),this.coll="annotations",this.chart=t,this.points=[],this.controlPoints=[],this.coll="annotations",this.index=-1,this.labels=[],this.shapes=[],this.options=iw(this.defaultOptions,i),this.userOptions=i;let s=iT(this.options,i);this.options.labels=s.labels,this.options.shapes=s.shapes,this.init(t,this.options)}addClipPaths(){this.setClipAxes(),this.clipXAxis&&this.clipYAxis&&this.options.crop&&(this.clipRect=this.chart.renderer.clipRect(this.getClipBox()))}addLabels(){let t=this.options.labels||[];t.forEach((i,s)=>{let e=this.initLabel(i,s);iw(!0,t[s],e.options)})}addShapes(){let t=this.options.shapes||[];t.forEach((i,s)=>{let e=this.initShape(i,s);iw(!0,t[s],e.options)})}destroy(){let t=this.chart,i=function(t){t.destroy()};this.labels.forEach(i),this.shapes.forEach(i),this.clipXAxis=null,this.clipYAxis=null,iM(t.labelCollectors,this.labelCollector),super.destroy(),this.destroyControlTarget(),iO(this,t)}destroyItem(t){iM(this[t.itemType+"s"],t),t.destroy()}getClipBox(){if(this.clipXAxis&&this.clipYAxis)return{x:this.clipXAxis.left,y:this.clipYAxis.top,width:this.clipXAxis.width,height:this.clipYAxis.height}}initProperties(t,i){this.setOptions(i);let s=iT(this.options,i);this.options.labels=s.labels,this.options.shapes=s.shapes,this.chart=t,this.points=[],this.controlPoints=[],this.coll="annotations",this.userOptions=i,this.labels=[],this.shapes=[]}init(t,i,s=this.index){let e=this.chart,o=this.options.animation;this.index=s,this.linkPoints(),this.addControlPoints(),this.addShapes(),this.addLabels(),this.setLabelCollector(),this.animationConfig=iP(e,o)}initLabel(t,i){let s=new tO(this,iw(this.options.labelOptions,{controlPointOptions:this.options.controlPointOptions},t),i);return s.itemType="label",this.labels.push(s),s}initShape(t,i){let s=iw(this.options.shapeOptions,{controlPointOptions:this.options.controlPointOptions},t),e=new iB.shapesMap[s.type](this,s,i);return e.itemType="shape",this.shapes.push(e),e}redraw(t){this.linkPoints(),this.graphic||this.render(),this.clipRect&&this.clipRect.animate(this.getClipBox()),this.redrawItems(this.shapes,t),this.redrawItems(this.labels,t),this.redrawControlPoints(t)}redrawItem(t,i){t.linkPoints(),t.shouldBeDrawn()?(t.graphic||this.renderItem(t),t.redraw(iE(i,!0)&&t.graphic.placed),t.points.length&&function(t){let i=t.graphic,s=t.points.some(t=>!1!==t.series.visible&&!1!==t.visible);i&&(s?"hidden"===i.visibility&&i.show():i.hide())}(t)):this.destroyItem(t)}redrawItems(t,i){let s=t.length;for(;s--;)this.redrawItem(t[s],i)}remove(){return this.chart.removeAnnotation(this)}render(){let t=this.chart.renderer;this.graphic=t.g("annotation").attr({opacity:0,zIndex:this.options.zIndex,visibility:this.options.visible?"inherit":"hidden"}).add(),this.shapesGroup=t.g("annotation-shapes").add(this.graphic),this.options.crop&&this.shapesGroup.clip(this.chart.plotBoxClip),this.labelsGroup=t.g("annotation-labels").attr({translateX:0,translateY:0}).add(this.graphic),this.addClipPaths(),this.clipRect&&this.graphic.clip(this.clipRect),this.renderItems(this.shapes),this.renderItems(this.labels),this.addEvents(),this.renderControlPoints()}renderItem(t){t.render("label"===t.itemType?this.labelsGroup:this.shapesGroup)}renderItems(t){let i=t.length;for(;i--;)this.renderItem(t[i])}setClipAxes(){let t=this.chart.xAxis,i=this.chart.yAxis,s=(this.options.labels||[]).concat(this.options.shapes||[]).reduce((s,e)=>{let o=e&&(e.point||e.points&&e.points[0]);return[t[o&&o.xAxis]||s[0],i[o&&o.yAxis]||s[1]]},[]);this.clipXAxis=s[0],this.clipYAxis=s[1]}setControlPointsVisibility(t){let i=function(i){i.setControlPointsVisibility(t)};this.controlPoints.forEach(i=>{i.setVisibility(t)}),this.shapes.forEach(i),this.labels.forEach(i)}setLabelCollector(){let t=this;t.labelCollector=function(){return t.labels.reduce(function(t,i){return i.options.allowOverlap||t.push(i.graphic),t},[])},t.chart.labelCollectors.push(t.labelCollector)}setOptions(t){this.options=iw(this.defaultOptions,t)}setVisibility(t){let i=this.options,s=this.chart.navigationBindings,e=iE(t,!i.visible);if(this.graphic.attr("visibility",e?"inherit":"hidden"),!e){let t=function(t){t.setControlPointsVisibility(e)};this.shapes.forEach(t),this.labels.forEach(t),s.activeAnnotation===this&&s.popup&&"annotation-toolbar"===s.popup.type&&ik(s,"closePopup")}i.visible=e}update(t,i){let s=this.chart,e=iT(this.userOptions,t),o=s.annotations.indexOf(this),n=iw(!0,this.userOptions,t);n.labels=e.labels,n.shapes=e.shapes,this.destroy(),this.initProperties(s,n),this.init(s,n),s.options.annotations[o]=this.options,this.isUpdating=!0,iE(i,!0)&&s.drawAnnotations(),ik(this,"afterUpdate"),this.isUpdating=!1}}iB.ControlPoint=R,iB.MockPoint=H,iB.shapesMap={rect:tl,circle:tc,ellipse:tx,path:tr,image:tM},iB.types={},iB.prototype.defaultOptions={visible:!0,animation:{},crop:!0,draggable:"xy",labelOptions:{align:"center",allowOverlap:!1,backgroundColor:"rgba(0, 0, 0, 0.75)",borderColor:"#000000",borderRadius:3,borderWidth:1,className:"highcharts-no-tooltip",crop:!1,formatter:function(){return E(this.y)?""+this.y:"Annotation label"},includeInDataExport:!0,overflow:"justify",padding:5,shadow:!1,shape:"callout",style:{fontSize:"0.7em",fontWeight:"normal",color:"contrast"},useHTML:!1,verticalAlign:"bottom",x:0,y:-16},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1,fill:"rgba(0, 0, 0, 0.75)",r:0,snap:2},controlPointOptions:{events:{},style:{cursor:"pointer",fill:"#ffffff",stroke:"#000000","stroke-width":2},height:10,symbol:"circle",visible:!1,width:10},events:{},zIndex:6},iB.prototype.nonDOMEvents=["add","afterUpdate","drag","remove"],j.compose(iB);let iL=iB;!function(t){t.compose=function(t){return t.navigation||(t.navigation=new i(t)),t};class i{constructor(t){this.updates=[],this.chart=t}addUpdate(t){this.chart.navigation.updates.push(t)}update(t,i){this.updates.forEach(s=>{s.call(this.chart,t,i)})}}t.Additions=i}(r||(r={}));let iN=r,{defined:iY,isNumber:iX,pick:iS}=u(),iI={backgroundColor:"string",borderColor:"string",borderRadius:"string",color:"string",fill:"string",fontSize:"string",labels:"string",name:"string",stroke:"string",title:"string"},iD={annotationsFieldsTypes:iI,getAssignedAxis:function(t){return t.filter(t=>{let i=t.axis.getExtremes(),s=i.min,e=i.max,o=iS(t.axis.minPointOffset,0);return iX(s)&&iX(e)&&t.value>=s-o&&t.value<=e+o&&!t.axis.options.isInternal})[0]},getFieldType:function(t,i){let s=iI[t],e=typeof i;return iY(s)&&(e=s),({string:"text",number:"number",boolean:"checkbox"})[e]}},{getAssignedAxis:iR}=iD,{isNumber:iF,merge:iW}=u(),iz={lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),s=i&&iR(i.xAxis),e=i&&iR(i.yAxis),o=this.chart.options.navigation;if(s&&e)return this.chart.addAnnotation(iW({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{x:s.value,y:e.value,xAxis:s.axis.index,yAxis:e.axis.index},r:5}]},o.annotationsOptions,o.bindings.circleAnnotation.annotationsOptions))},steps:[function(t,i){let s,e=i.options.shapes,o=e&&e[0]&&e[0].point||{};if(iF(o.xAxis)&&iF(o.yAxis)){let i=this.chart.inverted,e=this.chart.xAxis[o.xAxis].toPixels(o.x),n=this.chart.yAxis[o.yAxis].toPixels(o.y);s=Math.max(Math.sqrt(Math.pow(i?n-t.chartX:e-t.chartX,2)+Math.pow(i?e-t.chartY:n-t.chartY,2)),5)}i.update({shapes:[{r:s}]})}]},ellipseAnnotation:{className:"highcharts-ellipse-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),s=i&&iR(i.xAxis),e=i&&iR(i.yAxis),o=this.chart.options.navigation;if(s&&e)return this.chart.addAnnotation(iW({langKey:"ellipse",type:"basicAnnotation",shapes:[{type:"ellipse",xAxis:s.axis.index,yAxis:e.axis.index,points:[{x:s.value,y:e.value},{x:s.value,y:e.value}],ry:1}]},o.annotationsOptions,o.bindings.ellipseAnnotation.annotationsOptions))},steps:[function(t,i){let s=i.shapes[0],e=s.getAbsolutePosition(s.points[1]);s.translatePoint(t.chartX-e.x,t.chartY-e.y,1),s.redraw(!1)},function(t,i){let s=i.shapes[0],e=s.getAbsolutePosition(s.points[0]),o=s.getAbsolutePosition(s.points[1]),n=s.getDistanceFromLine(e,o,t.chartX,t.chartY),a=s.getYAxis(),r=Math.abs(a.toValue(0)-a.toValue(n));s.setYRadius(r),s.redraw(!1)}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),s=i&&iR(i.xAxis),e=i&&iR(i.yAxis);if(!s||!e)return;let o=s.value,n=e.value,a=s.axis.index,r=e.axis.index,h=this.chart.options.navigation;return this.chart.addAnnotation(iW({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:a,yAxis:r,x:o,y:n},{xAxis:a,yAxis:r,x:o,y:n},{xAxis:a,yAxis:r,x:o,y:n},{xAxis:a,yAxis:r,x:o,y:n},{command:"Z"}]}]},h.annotationsOptions,h.bindings.rectangleAnnotation.annotationsOptions))},steps:[function(t,i){let s=i.options.shapes,e=s&&s[0]&&s[0].points||[],o=this.chart.pointer?.getCoordinates(t),n=o&&iR(o.xAxis),a=o&&iR(o.yAxis);if(n&&a){let t=n.value,s=a.value;e[1].x=t,e[2].x=t,e[2].y=s,e[3].y=s,i.update({shapes:[{points:e}]})}}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),s=i&&iR(i.xAxis),e=i&&iR(i.yAxis),o=this.chart.options.navigation;if(s&&e)return this.chart.addAnnotation(iW({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}",overflow:"none",crop:!0},labels:[{point:{xAxis:s.axis.index,yAxis:e.axis.index,x:s.value,y:e.value}}]},o.annotationsOptions,o.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}},{setOptions:iU}=u(),{format:iV}=ty(),{composed:iH,doc:ij,win:iq}=u(),{getAssignedAxis:i_,getFieldType:iG}=iD,{addEvent:iK,attr:iZ,defined:i$,fireEvent:iJ,isArray:iQ,isFunction:i0,isNumber:i1,isObject:i2,merge:i9,objectEach:i4,pick:i5,pushUnique:i3}=u();function i6(){this.chart.navigationBindings&&this.chart.navigationBindings.deselectAnnotation()}function i7(){this.navigationBindings&&this.navigationBindings.destroy()}function i8(){let t=this.options;t&&t.navigation&&t.navigation.bindings&&(this.navigationBindings=new so(this,t.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())}function st(){let t=this.navigationBindings,i="highcharts-disabled-btn";if(this&&t){let s=!1;if(this.series.forEach(t=>{!t.options.isInternal&&t.visible&&(s=!0)}),this.navigationBindings&&this.navigationBindings.container&&this.navigationBindings.container[0]){let e=this.navigationBindings.container[0];i4(t.boundClassNames,(t,o)=>{let n=e.querySelectorAll("."+o);if(n)for(let e=0;e<n.length;e++){let o=n[e],a=o.className;"normal"===t.noDataState?-1!==a.indexOf(i)&&o.classList.remove(i):s?-1!==a.indexOf(i)&&o.classList.remove(i):-1===a.indexOf(i)&&(o.className+=" "+i)}})}}}function si(){this.deselectAnnotation()}function ss(){this.selectedButtonElement=null}function se(t){let i,s,e=t.prototype.defaultOptions.events&&t.prototype.defaultOptions.events.click;function o(t){let i=this,s=i.chart.navigationBindings,o=s.activeAnnotation;e&&e.call(i,t),o!==i?(s.deselectAnnotation(),s.activeAnnotation=i,i.setControlPointsVisibility(!0),iJ(s,"showPopup",{annotation:i,formType:"annotation-toolbar",options:s.annotationToFields(i),onSubmit:function(t){if("remove"===t.actionType)s.activeAnnotation=!1,s.chart.removeAnnotation(i);else{let e={};s.fieldsToOptions(t.fields,e),s.deselectAnnotation();let o=e.typeOptions;"measure"===i.options.type&&(o.crosshairY.enabled=0!==o.crosshairY.strokeWidth,o.crosshairX.enabled=0!==o.crosshairX.strokeWidth),i.update(e)}}})):iJ(s,"closePopup"),t.activeAnnotation=!0}i9(!0,t.prototype.defaultOptions.events,{click:o,touchstart:function(t){i=t.touches[0].clientX,s=t.touches[0].clientY},touchend:function(t){i&&Math.sqrt(Math.pow(i-t.changedTouches[0].clientX,2)+Math.pow(s-t.changedTouches[0].clientY,2))>=4||o.call(this,t)}})}class so{static compose(t,i){i3(iH,"NavigationBindings")&&(iK(t,"remove",i6),se(t),i4(t.types,t=>{se(t)}),iK(i,"destroy",i7),iK(i,"load",i8),iK(i,"render",st),iK(so,"closePopup",si),iK(so,"deselectButton",ss),iU(iz))}constructor(t,i){this.boundClassNames=void 0,this.chart=t,this.options=i,this.eventsToUnbind=[],this.container=this.chart.container.getElementsByClassName(this.options.bindingsClassName||""),this.container.length||(this.container=ij.getElementsByClassName(this.options.bindingsClassName||""))}getCoords(t){let i=this.chart.pointer?.getCoordinates(t);return[i&&i_(i.xAxis),i&&i_(i.yAxis)]}initEvents(){let t=this,i=t.chart,s=t.container,e=t.options;t.boundClassNames={},i4(e.bindings||{},i=>{t.boundClassNames[i.className]=i}),[].forEach.call(s,i=>{t.eventsToUnbind.push(iK(i,"click",s=>{let e=t.getButtonEvents(i,s);e&&!e.button.classList.contains("highcharts-disabled-btn")&&t.bindingsButtonClick(e.button,e.events,s)}))}),i4(e.events||{},(i,s)=>{i0(i)&&t.eventsToUnbind.push(iK(t,s,i,{passive:!1}))}),t.eventsToUnbind.push(iK(i.container,"click",function(s){!i.cancelClick&&i.isInsidePlot(s.chartX-i.plotLeft,s.chartY-i.plotTop,{visiblePlotOnly:!0})&&t.bindingsChartClick(this,s)})),t.eventsToUnbind.push(iK(i.container,u().isTouchDevice?"touchmove":"mousemove",function(i){t.bindingsContainerMouseMove(this,i)},u().isTouchDevice?{passive:!1}:void 0))}initUpdate(){let t=this;iN.compose(this.chart).navigation.addUpdate(i=>{t.update(i)})}bindingsButtonClick(t,i,s){let e=this.chart,o=e.renderer.boxWrapper,n=!0;this.selectedButtonElement&&(this.selectedButtonElement.classList===t.classList&&(n=!1),iJ(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&e.removeAnnotation(this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1)),n?(this.selectedButton=i,this.selectedButtonElement=t,iJ(this,"selectButton",{button:t}),i.init&&i.init.call(this,t,s),(i.start||i.steps)&&e.renderer.boxWrapper.addClass("highcharts-draw-mode")):(e.stockTools&&t.classList.remove("highcharts-active"),o.removeClass("highcharts-draw-mode"),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null)}bindingsChartClick(t,i){t=this.chart;let s=this.activeAnnotation,e=this.selectedButton,o=t.renderer.boxWrapper;s&&(s.cancelClick||i.activeAnnotation||!i.target.parentNode||function(t,i){let s=iq.Element.prototype,e=s.matches||s.msMatchesSelector||s.webkitMatchesSelector,o=null;if(s.closest)o=s.closest.call(t,i);else do{if(e.call(t,i))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return o}(i.target,".highcharts-popup")?s.cancelClick&&setTimeout(()=>{s.cancelClick=!1},0):iJ(this,"closePopup")),e&&e.start&&(this.nextEvent?(this.nextEvent(i,this.currentUserDetails),this.steps&&(this.stepIndex++,e.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=e.steps[this.stepIndex]:(iJ(this,"deselectButton",{button:this.selectedButtonElement}),o.removeClass("highcharts-draw-mode"),e.end&&e.end.call(this,i,this.currentUserDetails),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null))):(this.currentUserDetails=e.start.call(this,i),this.currentUserDetails&&e.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=e.steps[this.stepIndex]):(iJ(this,"deselectButton",{button:this.selectedButtonElement}),o.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,e.end&&e.end.call(this,i,this.currentUserDetails))))}bindingsContainerMouseMove(t,i){this.mouseMoveEvent&&this.mouseMoveEvent(i,this.currentUserDetails)}fieldsToOptions(t,i){return i4(t,(t,s)=>{let e=parseFloat(t),o=s.split("."),n=o.length-1;if(!i1(e)||t.match(/px|em/g)||s.match(/format/g)||(t=e),"undefined"!==t){let s=i;o.forEach((i,e)=>{if("__proto__"!==i&&"constructor"!==i){let a=i5(o[e+1],"");n===e?s[i]=t:(s[i]||(s[i]=a.match(/\d/g)?[]:{}),s=s[i])}})}}),i}deselectAnnotation(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)}annotationToFields(t){let i=t.options,s=so.annotationsEditable,e=s.nestedOptions,o=i5(i.type,i.shapes&&i.shapes[0]&&i.shapes[0].type,i.labels&&i.labels[0]&&i.labels[0].type,"label"),n=so.annotationsNonEditable[i.langKey]||[],a={langKey:i.langKey,type:o};function r(i,s,o,a,h){let l;o&&i$(i)&&-1===n.indexOf(s)&&((o.indexOf&&o.indexOf(s))>=0||o[s]||!0===o)&&(iQ(i)?(a[s]=[],i.forEach((t,i)=>{i2(t)?(a[s][i]={},i4(t,(t,o)=>{r(t,o,e[s],a[s][i],s)})):r(t,0,e[s],a[s],s)})):i2(i)?(l={},iQ(a)?(a.push(l),l[s]={},l=l[s]):a[s]=l,i4(i,(t,i)=>{r(t,i,0===s?o:e[s],l,s)})):"format"===s?a[s]=[iV(i,t.labels[0].points[0]).toString(),"text"]:iQ(a)?a.push([i,iG(h,i)]):a[s]=[i,iG(s,i)])}return i4(i,(t,n)=>{"typeOptions"===n?(a[n]={},i4(i[n],(t,i)=>{r(t,i,e,a[n],i)})):r(t,n,s[o],a,n)}),a}getClickedClassNames(t,i){let s=i.target,e=[],o;for(;s&&s.tagName&&((o=iZ(s,"class"))&&(e=e.concat(o.split(" ").map(t=>[t,s]))),(s=s.parentNode)!==t););return e}getButtonEvents(t,i){let s,e=this;return this.getClickedClassNames(t,i).forEach(t=>{e.boundClassNames[t[0]]&&!s&&(s={events:e.boundClassNames[t[0]],button:t[1]})}),s}update(t){this.options=i9(!0,this.options,t),this.removeEvents(),this.initEvents()}removeEvents(){this.eventsToUnbind.forEach(t=>t())}destroy(){this.removeEvents()}}so.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],label:["style"],style:["fontSize","color"],background:["fill","strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],ellipse:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background","crosshairY","crosshairX"],fibonacci:[],tunnel:["background","line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]},so.annotationsNonEditable={rectangle:["crosshairX","crosshairY","labelOptions"],ellipse:["labelOptions"],circle:["labelOptions"]};let sn=u();sn.Annotation=sn.Annotation||iL,sn.NavigationBindings=sn.NavigationBindings||so,sn.Annotation.compose(sn.Chart,sn.NavigationBindings,sn.Pointer,sn.SVGRenderer);let{merge:sa}=u();class sr extends iL{addControlPoints(){let t=this.options,i=sr.basicControlPoints,s=this.basicType;(t.labels||t.shapes||[]).forEach(t=>{t.controlPoints=i[s]})}init(){let t=this.options;if(t.shapes){delete t.labelOptions;let i=t.shapes[0].type;t.shapes[0].className=(t.shapes[0].className||"")+" highcharts-basic-shape",i&&"path"!==i?this.basicType=i:this.basicType="rectangle"}else delete t.shapes,this.basicType="label";super.init.apply(this,arguments)}}sr.basicControlPoints={label:[{symbol:"triangle-down",positioner:function(t){if(!t.graphic.placed)return{x:0,y:-9e7};let i=H.pointToPixels(t.points[0]);return{x:i.x-(this.graphic.width||0)/2,y:i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=this.mouseMoveToTranslation(t);i.translatePoint(s.x,s.y),i.annotation.userOptions.labels[0].point=i.options.point,i.redraw(!1)}}},{symbol:"square",positioner:function(t){return t.graphic.placed?{x:t.graphic.alignAttr.x-(this.graphic.width||0)/2,y:t.graphic.alignAttr.y-(this.graphic.height||0)/2}:{x:0,y:-9e7}},events:{drag:function(t,i){let s=this.mouseMoveToTranslation(t);i.translate(s.x,s.y),i.annotation.userOptions.labels[0].point=i.options.point,i.redraw(!1)}}}],rectangle:[{positioner:function(t){let i=H.pointToPixels(t.points[2]);return{x:i.x-4,y:i.y-4}},events:{drag:function(t,i){let s=i.annotation,e=this.chart.pointer?.getCoordinates(t),o=i.options.points,n=s.userOptions.shapes,a=s.clipXAxis?.index||0,r=s.clipYAxis?.index||0;if(e){let t=e.xAxis[a].value,s=e.yAxis[r].value;o[1].x=t,o[2].x=t,o[2].y=s,o[3].y=s,n&&n[0]&&(n[0].points=i.options.points)}s.redraw(!1)}}}],circle:[{positioner:function(t){let i=H.pointToPixels(t.points[0]),s=t.options.r;return{x:i.x+s*Math.cos(Math.PI/4)-(this.graphic.width||0)/2,y:i.y+s*Math.sin(Math.PI/4)-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=i.annotation,e=this.mouseMoveToTranslation(t),o=s.userOptions.shapes;i.setRadius(Math.max(i.options.r+e.y/Math.sin(Math.PI/4),5)),o&&o[0]&&(o[0].r=i.options.r,o[0].point=i.options.point),i.redraw(!1)}}}],ellipse:[{positioner:function(t){let i=t.getAbsolutePosition(t.points[0]);return{x:i.x-(this.graphic.width||0)/2,y:i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=i.getAbsolutePosition(i.points[0]);i.translatePoint(t.chartX-s.x,t.chartY-s.y,0),i.redraw(!1)}}},{positioner:function(t){let i=t.getAbsolutePosition(t.points[1]);return{x:i.x-(this.graphic.width||0)/2,y:i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=i.getAbsolutePosition(i.points[1]);i.translatePoint(t.chartX-s.x,t.chartY-s.y,1),i.redraw(!1)}}},{positioner:function(t){let i=t.getAbsolutePosition(t.points[0]),s=t.getAbsolutePosition(t.points[1]),e=t.getAttrs(i,s);return{x:e.cx-(this.graphic.width||0)/2+e.ry*Math.sin(e.angle*Math.PI/180),y:e.cy-(this.graphic.height||0)/2-e.ry*Math.cos(e.angle*Math.PI/180)}},events:{drag:function(t,i){let s=i.getAbsolutePosition(i.points[0]),e=i.getAbsolutePosition(i.points[1]),o=i.getDistanceFromLine(s,e,t.chartX,t.chartY),n=i.getYAxis(),a=Math.abs(n.toValue(0)-n.toValue(o));i.setYRadius(a),i.redraw(!1)}}}]},sr.prototype.defaultOptions=sa(iL.prototype.defaultOptions,{}),iL.types.basicAnnotation=sr;let{merge:sh}=u();class sl extends iL{setClipAxes(){this.clipXAxis=this.chart.xAxis[this.options.typeOptions.xAxis],this.clipYAxis=this.chart.yAxis[this.options.typeOptions.yAxis]}getPointsOptions(){let t=this.options.typeOptions;return(t.points||[]).map(i=>(i.xAxis=t.xAxis,i.yAxis=t.yAxis,i))}getControlPointsOptions(){return this.getPointsOptions()}addControlPoints(){this.getControlPointsOptions().forEach(function(t,i){let s=new R(this.chart,this,sh(this.options.controlPointOptions,t.controlPoint),i);this.controlPoints.push(s),t.controlPoint=s.options},this)}addShapes(){let t=this.options.typeOptions,i=this.initShape(sh(t.line,{type:"path",className:"highcharts-crooked-lines",points:this.points.map((t,i)=>function(t){return t.annotation.points[i]})}),0);t.line=i.options}}sl.prototype.defaultOptions=sh(iL.prototype.defaultOptions,{typeOptions:{xAxis:0,yAxis:0,line:{fill:"none"}},controlPointOptions:{positioner:function(t){let i=this.graphic,s=H.pointToPixels(t.points[this.index]);return{x:s.x-(i.width||0)/2,y:s.y-(i.height||0)/2}},events:{drag:function(t,i){if(i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})){let s=this.mouseMoveToTranslation(t),e=i.options.typeOptions;i.translatePoint(s.x,s.y,this.index),e.points[this.index].x=i.points[this.index].x,e.points[this.index].y=i.points[this.index].y,i.redraw(!1)}}}}}),iL.types.crookedLine=sl;let sp=sl,{merge:sc}=u();class sd extends sp{addLabels(){this.getPointsOptions().forEach((t,i)=>{let s=this.options.typeOptions,e=this.initLabel(sc(t.label,{text:s.labels[i],point:function(t){return t.annotation.points[i]}}),!1);t.label=e.options})}}sd.prototype.defaultOptions=sc(sp.prototype.defaultOptions,{typeOptions:{labels:["(0)","(A)","(B)","(C)","(D)","(E)"],line:{strokeWidth:1}},labelOptions:{align:"center",allowOverlap:!0,crop:!0,overflow:"none",type:"rect",backgroundColor:"none",borderWidth:0,y:-5}}),iL.types.elliottWave=sd;let{merge:su}=u();class sx extends sp{getPointsOptions(){let t=sp.prototype.getPointsOptions.call(this),i=this.options.typeOptions.yAxis||0,s=this.chart.yAxis[i];if(t[2]=this.heightPointOptions(t[1]),t[3]=this.heightPointOptions(t[0]),s&&s.logarithmic){let i=s.toPixels(t[2].y)-s.toPixels(t[1].y),e=s.toPixels(t[0].y)+i;t[3].y=s.toValue(e)}return t}getControlPointsOptions(){return this.getPointsOptions().slice(0,2)}heightPointOptions(t){let i=su(t),s=this.options.typeOptions;return i.y+=s.height,i}addControlPoints(){sp.prototype.addControlPoints.call(this);let t=this.options,i=t.typeOptions,s=new R(this.chart,this,su(t.controlPointOptions,i.heightControlPoint),2);this.controlPoints.push(s),i.heightControlPoint=s.options}addShapes(){this.addLine(),this.addBackground()}addLine(){let t=this.initShape(su(this.options.typeOptions.line,{type:"path",points:[this.points[0],this.points[1],function(t){let i=H.pointToOptions(t.annotation.points[2]);return i.command="M",i},this.points[3]],className:"highcharts-tunnel-lines"}),0);this.options.typeOptions.line=t.options}addBackground(){let t=this.initShape(su(this.options.typeOptions.background,{type:"path",points:this.points.slice(),className:"highcharts-tunnel-background"}),1);this.options.typeOptions.background=t.options}translateSide(t,i,s){let e=Number(s);this.translatePoint(t,i,e),this.translatePoint(t,i,0===e?3:2)}translateHeight(t){this.translatePoint(0,t,2),this.translatePoint(0,t,3),this.options.typeOptions.height=this.points[3].y-this.points[0].y,this.userOptions.typeOptions.height=this.options.typeOptions.height}}sx.prototype.defaultOptions=su(sp.prototype.defaultOptions,{typeOptions:{background:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0},line:{strokeWidth:1},height:-2,heightControlPoint:{positioner:function(t){let i=H.pointToPixels(t.points[2]),s=H.pointToPixels(t.points[3]),e=(i.x+s.x)/2;return{x:e-(this.graphic.width||0)/2,y:(s.y-i.y)/(s.x-i.x)*(e-i.x)+i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})&&(i.translateHeight(this.mouseMoveToTranslation(t).y),i.redraw(!1))}}}},controlPointOptions:{events:{drag:function(t,i){if(i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})){let s=this.mouseMoveToTranslation(t);i.translateSide(s.x,s.y,!!this.index),i.redraw(!1)}}}}}),iL.types.tunnel=sx;let sg=sx,{merge:sy}=u();class sf extends sp{static edgePoint(t,i){return function(s){let e=s.annotation,o=e.options.typeOptions.type,n=e.points;return("horizontalLine"===o||"verticalLine"===o)&&(n=[n[0],new H(e.chart,n[0].target,{x:n[0].x+ +("horizontalLine"===o),y:n[0].y+ +("verticalLine"===o),xAxis:n[0].options.xAxis,yAxis:n[0].options.yAxis})]),sf.findEdgePoint(n[t],n[i])}}static findEdgeCoordinate(t,i,s,e){let o="x"===s?"y":"x";return(i[s]-t[s])*(e-t[o])/(i[o]-t[o])+t[s]}static findEdgePoint(t,i){let s,e,o,n=t.series.chart,a=t.series.xAxis,r=i.series.yAxis,h=H.pointToPixels(t),l=H.pointToPixels(i),p=l.x-h.x,c=l.y-h.y,d=a.left,u=d+a.width,x=r.top,g=x+r.height,y=p<0?d:u,f=c<0?x:g,m={x:0===p?h.x:y,y:0===c?h.y:f};return 0!==p&&0!==c&&(e=sf.findEdgeCoordinate(h,l,"y",y),s=sf.findEdgeCoordinate(h,l,"x",f),e>=x&&e<=g?(m.x=y,m.y=e):(m.x=s,m.y=f)),m.x-=n.plotLeft,m.y-=n.plotTop,t.series.chart.inverted&&(o=m.x,m.x=m.y,m.y=o),m}addShapes(){let t=this.options.typeOptions,i=[this.points[0],sf.endEdgePoint];t.type.match(/line/gi)&&(i[0]=sf.startEdgePoint);let s=this.initShape(sy(t.line,{type:"path",points:i,className:"highcharts-infinity-lines"}),0);t.line=s.options}}sf.endEdgePoint=sf.edgePoint(0,1),sf.startEdgePoint=sf.edgePoint(1,0),sf.prototype.defaultOptions=sy(sp.prototype.defaultOptions,{}),iL.types.infinityLine=sf;let sm=sf,{merge:sv,isNumber:sb,defined:sA}=u();class sP extends sp{init(t,i,s){sA(i.yAxis)&&i.points.forEach(t=>{t.yAxis=i.yAxis}),sA(i.xAxis)&&i.points.forEach(t=>{t.xAxis=i.xAxis}),super.init(t,i,s)}setPath(){this.shapes[0].options.d=this.getPath()}getPath(){return[["M",this.startX,this.y]].concat(function(t,i,s,e){let o=[];for(let n=1;n<=i;n++)o.push(["A",t/2,t/2,0,1,1,s+n*t,e]);return o}(this.pixelInterval,this.numberOfCircles,this.startX,this.y))}addShapes(){let t=this.options.typeOptions;this.setPathProperties();let i=this.initShape(sv(t.line,{type:"path",d:this.getPath(),points:this.options.points,className:"highcharts-timecycles-lines"}),0);t.line=i.options}addControlPoints(){let t=this.options,i=t.typeOptions;t.controlPointOptions.style.cursor=this.chart.inverted?"ns-resize":"ew-resize",i.controlPointOptions.forEach(i=>{let s=sv(t.controlPointOptions,i),e=new R(this.chart,this,s,0);this.controlPoints.push(e)})}setPathProperties(){let t=this.options.typeOptions,i=t.points;if(!i)return;let s=i[0],e=i[1],o=t.xAxis||0,n=t.yAxis||0,a=this.chart.xAxis[o],r=this.chart.yAxis[n],h=s.x,l=s.y,p=e.x;if(!h||!p)return;let c=sb(l)?r.toPixels(l):r.top+r.height,d=sb(h)?a.toPixels(h):a.left,u=sb(p)?a.toPixels(p):a.left+30,x=a.len,g=Math.round(Math.max(Math.abs(u-d),2)),y=Math.floor(x/g)+2,f=(Math.floor((d-a.left)/g)+1)*g;this.startX=d-f,this.y=c,this.pixelInterval=g,this.numberOfCircles=y}redraw(t){this.setPathProperties(),this.setPath(),super.redraw(t)}}sP.prototype.defaultOptions=sv(sp.prototype.defaultOptions,{typeOptions:{controlPointOptions:[{positioner:function(t){let i=t.points[0];return{x:t.anchor(i).absolutePosition.x-(this.graphic.width||0)/2,y:t.y-(this.graphic.height||0)}},events:{drag:function(t,i){let s=i.anchor(i.points[0]).absolutePosition;i.translatePoint(t.chartX-s.x,0,0),i.redraw(!1)}}},{positioner:function(t){let i=t.points[1];return{x:t.anchor(i).absolutePosition.x-(this.graphic.width||0)/2,y:t.y-(this.graphic.height||0)}},events:{drag:function(t,i){let s=i.anchor(i.points[1]).absolutePosition;i.translatePoint(t.chartX-s.x,0,1),i.redraw(!1)}}}]}}),iL.types.timeCycles=sP;let{merge:sO}=u();function sM(t,i){return function(){let s=this.annotation;if(!s.startRetracements||!s.endRetracements)return[];let e=this.anchor(s.startRetracements[t]).absolutePosition,o=this.anchor(s.endRetracements[t]).absolutePosition,n=[["M",Math.round(e.x),Math.round(e.y)],["L",Math.round(o.x),Math.round(o.y)]];if(i){let i=this.anchor(s.endRetracements[t-1]).absolutePosition,e=this.anchor(s.startRetracements[t-1]).absolutePosition;n.push(["L",Math.round(i.x),Math.round(i.y)],["L",Math.round(e.x),Math.round(e.y)])}return n}}class sk extends sg{linkPoints(){super.linkPoints(),this.linkRetracementsPoints()}linkRetracementsPoints(){let t=this.points,i=t[0].y-t[3].y,s=t[1].y-t[2].y,e=t[0].x,o=t[1].x;sk.levels.forEach((n,a)=>{let r=t[0].y-i*n,h=t[1].y-s*n,l=this.options.typeOptions.reversed?sk.levels.length-a-1:a;this.startRetracements=this.startRetracements||[],this.endRetracements=this.endRetracements||[],this.linkRetracementPoint(l,e,r,this.startRetracements),this.linkRetracementPoint(l,o,h,this.endRetracements)})}linkRetracementPoint(t,i,s,e){let o=e[t],n=this.options.typeOptions;o?(o.options.x=i,o.options.y=s,o.refresh()):e[t]=new H(this.chart,this,{x:i,y:s,xAxis:n.xAxis,yAxis:n.yAxis})}addShapes(){sk.levels.forEach(function(t,i){let{backgroundColors:s,lineColor:e,lineColors:o}=this.options.typeOptions;this.initShape({type:"path",d:sM(i),stroke:o[i]||e,className:"highcharts-fibonacci-line"},i),i>0&&this.initShape({type:"path",fill:s[i-1],strokeWidth:0,d:sM(i,!0),className:"highcharts-fibonacci-background-"+(i-1)})},this)}addLabels(){sk.levels.forEach(function(t,i){let s=this.options.typeOptions,e=this.initLabel(sO(s.labels[i],{point:function(t){return H.pointToOptions(t.annotation.startRetracements[i])},text:t.toString()}));s.labels[i]=e.options},this)}}sk.levels=[0,.236,.382,.5,.618,.786,1],sk.prototype.defaultOptions=sO(sg.prototype.defaultOptions,{typeOptions:{reversed:!1,height:2,backgroundColors:["rgba(130, 170, 255, 0.4)","rgba(139, 191, 216, 0.4)","rgba(150, 216, 192, 0.4)","rgba(156, 229, 161, 0.4)","rgba(162, 241, 130, 0.4)","rgba(169, 255, 101, 0.4)"],lineColor:"#999999",lineColors:[],labels:[]},labelOptions:{allowOverlap:!0,align:"right",backgroundColor:"none",borderWidth:0,crop:!1,overflow:"none",shape:"rect",style:{color:"grey"},verticalAlign:"middle",y:0}}),iL.types.fibonacci=sk;let{merge:sw}=u();function sE(t,i,s){return function(e){let o=e.annotation.chart,n=o.inverted?o.plotTop:o.plotLeft,a=e.annotation.points,r=a[0].series.xAxis,h=a.length>1?a[1].plotX-a[0].plotX:0,l=r.toValue(a[0].plotX+n+s*h);return a=[new H(o,a[0].target,{x:l,y:0,xAxis:a[0].options.xAxis,yAxis:a[0].options.yAxis}),new H(o,a[0].target,{x:l,y:1,xAxis:a[0].options.xAxis,yAxis:a[0].options.yAxis})],sm.findEdgePoint(a[t],a[i])}}class sC extends sp{addShapes(){let t=1,i=1;for(let s=0;s<11;s++){let e=s?t:0,o=[sE(1,0,e),sE(0,1,e)];t=(i=t+i)-t,1===s&&(this.secondLineEdgePoints=[o[0],o[1]]),this.initShape(sw(this.options.typeOptions.line,{type:"path",points:o,className:"highcharts-fibonacci-timezones-lines"}),s)}}addControlPoints(){let t=this.options,i=t.typeOptions,s=new R(this.chart,this,sw(t.controlPointOptions,i.controlPointOptions),0);this.controlPoints.push(s),i.controlPointOptions=s.options}}sC.prototype.defaultOptions=sw(sp.prototype.defaultOptions,{typeOptions:{line:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1,fill:void 0},controlPointOptions:{positioner:function(){let t=this.target,i=this.graphic,s=t.secondLineEdgePoints,e={annotation:t},o=s[0](e).y,n=s[1](e).y,a=this.chart.plotLeft,r=this.chart.plotTop,h=s[0](e).x,l=(o+n)/2;return this.chart.inverted&&([h,l]=[l,h]),{x:a+h-(i.width||0)/2,y:r+l-(i.height||0)/2}},events:{drag:function(t,i){if(i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})){let s=this.mouseMoveToTranslation(t);i.translatePoint(s.x,0,1),i.redraw(!1)}}}}}}),iL.types.fibonacciTimeZones=sC;let{merge:sT}=u();class sB extends sm{static outerLineEdgePoint(t){return function(i){let s=i.annotation,e=s.points;return sB.findEdgePoint(e[t],e[0],new H(s.chart,i,s.midPointOptions()))}}static findEdgePoint(t,i,s){let e=Math.atan2(s.plotY-i.plotY,s.plotX-i.plotX);return{x:t.plotX+1e7*Math.cos(e),y:t.plotY+1e7*Math.sin(e)}}static middleLineEdgePoint(t){let i=t.annotation,s=i.points;return sm.findEdgePoint(s[0],new H(i.chart,t,i.midPointOptions()))}midPointOptions(){let t=this.points;return{x:(t[1].x+t[2].x)/2,y:(t[1].y+t[2].y)/2,xAxis:t[0].series.xAxis,yAxis:t[0].series.yAxis}}addShapes(){this.addLines(),this.addBackgrounds()}addLines(){let t="highcharts-pitchfork-lines";this.initShape({type:"path",points:[this.points[0],sB.middleLineEdgePoint],className:t},0),this.initShape({type:"path",points:[this.points[1],sB.topLineEdgePoint],className:t},1),this.initShape({type:"path",points:[this.points[2],sB.bottomLineEdgePoint],className:t},2)}addBackgrounds(){let t=this.shapes,i=this.options.typeOptions,s=this.initShape(sT(i.innerBackground,{type:"path",points:[function(t){let i=t.annotation,s=i.points,e=i.midPointOptions();return{x:(s[1].x+e.x)/2,y:(s[1].y+e.y)/2,xAxis:e.xAxis,yAxis:e.yAxis}},t[1].points[1],t[2].points[1],function(t){let i=t.annotation,s=i.points,e=i.midPointOptions();return{x:(e.x+s[2].x)/2,y:(e.y+s[2].y)/2,xAxis:e.xAxis,yAxis:e.yAxis}}],className:"highcharts-pitchfork-inner-background"}),3),e=this.initShape(sT(i.outerBackground,{type:"path",points:[this.points[1],t[1].points[1],t[2].points[1],this.points[2]],className:"highcharts-pitchfork-outer-background"}),4);i.innerBackground=s.options,i.outerBackground=e.options}}sB.topLineEdgePoint=sB.outerLineEdgePoint(1),sB.bottomLineEdgePoint=sB.outerLineEdgePoint(0),sB.prototype.defaultOptions=sT(sm.prototype.defaultOptions,{typeOptions:{innerBackground:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0},outerBackground:{fill:"rgba(156, 229, 161, 0.4)",strokeWidth:0}}}),iL.types.pitchfork=sB;let{merge:sL,pick:sN}=u();class sY extends iL{static connectorFirstPoint(t){let i=t.annotation,s=i.chart,e=s.inverted,o=i.points[0],n=sN(o.series.yAxis&&o.series.yAxis.left,0),a=sN(o.series.yAxis&&o.series.yAxis.top,0),r=i.options.typeOptions.label.offset,h=H.pointToPixels(o,!0)[e?"x":"y"];return{x:o.x,xAxis:o.series.xAxis,y:h+r+(e?n-s.plotLeft:a-s.plotTop)}}static connectorSecondPoint(t){let i=t.annotation,s=i.chart,e=s.inverted,o=i.options.typeOptions,n=i.points[0],a=sN(n.series.yAxis&&n.series.yAxis.left,0),r=sN(n.series.yAxis&&n.series.yAxis.top,0),h=H.pointToPixels(n,!0)[e?"x":"y"],l=o.yOffset;return o.label.offset<0&&(l*=-1),{x:n.x,xAxis:n.series.xAxis,y:h+l+(e?a-s.plotLeft:r-s.plotTop)}}getPointsOptions(){return[this.options.typeOptions.point]}addShapes(){let t=this.options.typeOptions,i=this.initShape(sL(t.connector,{type:"path",points:[sY.connectorFirstPoint,sY.connectorSecondPoint],className:"highcharts-vertical-line"}),0);t.connector=i.options,this.userOptions.typeOptions.point=t.point}addLabels(){let t=this.options.typeOptions,i=t.label,s=0,e=i.offset,o=i.offset<0?"bottom":"top",n="center";this.chart.inverted&&(s=i.offset,e=0,o="middle",n=i.offset<0?"right":"left"),t.label=this.initLabel(sL(i,{verticalAlign:o,align:n,x:s,y:e})).options}}sY.prototype.defaultOptions=sL(iL.prototype.defaultOptions,{typeOptions:{yOffset:10,label:{offset:-40,point:function(t){return t.annotation.points[0]},allowOverlap:!0,backgroundColor:"none",borderWidth:0,crop:!0,overflow:"none",shape:"rect",text:"{y:.2f}"},connector:{strokeWidth:1,markerEnd:"arrow"}}}),iL.types.verticalLine=sY;let{defined:sX,extend:sS,isNumber:sI,merge:sD,pick:sR}=u();function sF(){let t=0,i=0,s=0,e=this.chart.series,o=sV(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax);return e.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{sW(t,o)&&sI(t.y)&&(i+=t.y,s++)})}),s>0&&(t=i/s),t}function sW(t,i){return!t.isNull&&sI(t.y)&&t.x>i.xAxisMin&&t.x<=i.xAxisMax&&t.y>i.yAxisMin&&t.y<=i.yAxisMax}function sz(){let t=this.chart.series,i=sV(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),s=0;return t.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{sW(t,i)&&s++})}),s}function sU(){return"Min: "+this.min+"<br>Max: "+this.max+"<br>Average: "+this.average.toFixed(2)+"<br>Bins: "+this.bins}function sV(t,i,s,e){return{xAxisMin:Math.min(i,t),xAxisMax:Math.max(i,t),yAxisMin:Math.min(e,s),yAxisMax:Math.max(e,s)}}function sH(t,i,s){return t.toValue(t.toPixels(i)+s)}function sj(){let t=this.options.typeOptions,i=this.chart,s=i.inverted,e=i.xAxis[t.xAxis],o=i.yAxis[t.yAxis],n=t.background,a=s?n.height:n.width,r=s?n.width:n.height,h=t.selectType,l=s?e.left:o.top,p=s?o.top:e.left;this.startXMin=t.point.x,this.startYMin=t.point.y,sI(a)?this.startXMax=this.startXMin+a:this.startXMax=sH(e,this.startXMin,parseFloat(a)),sI(r)?this.startYMax=this.startYMin-r:this.startYMax=sH(o,this.startYMin,parseFloat(r)),"x"===h?(this.startYMin=o.toValue(l),this.startYMax=o.toValue(l+o.len)):"y"===h&&(this.startXMin=e.toValue(p),this.startXMax=e.toValue(p+e.len))}function sq(){let t=this.chart.series,i=sV(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),s=-1/0,e=!1;return t.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{sI(t.y)&&t.y>s&&sW(t,i)&&(s=t.y,e=!0)})}),e||(s=0),s}function s_(){let t=this.chart.series,i=sV(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),s=1/0,e=!1;return t.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{sI(t.y)&&t.y<s&&sW(t,i)&&(s=t.y,e=!0)})}),e||(s=0),s}function sG(t){let i=this.options.typeOptions,s=this.chart.xAxis[i.xAxis],e=this.chart.yAxis[i.yAxis],o=this.offsetX,n=this.offsetY;this.xAxisMin=sH(s,this.startXMin,o),this.xAxisMax=sH(s,this.startXMax,o),this.yAxisMin=sH(e,this.startYMin,n),this.yAxisMax=sH(e,this.startYMax,n),this.min=s_.call(this),this.max=sq.call(this),this.average=sF.call(this),this.bins=sz.call(this),t&&this.resize(0,0)}function sK(t,i,s,e,o){let n=this.options.typeOptions,a=n.selectType,r=this.chart.xAxis[n.xAxis],h=this.chart.yAxis[n.yAxis],l=this.startXMin,p=this.startXMax,c=this.startYMin,d=this.startYMax,u=this.offsetX,x=this.offsetY;i&&("x"===a?0===s?this.startXMin=sH(r,l,e):this.startXMax=sH(r,p,e):"y"===a?0===s?this.startYMin=sH(h,c,o):this.startYMax=sH(h,d,o):(this.startXMax=sH(r,p,e),this.startYMax=sH(h,d,o))),t&&(this.startXMin=sH(r,l,u),this.startXMax=sH(r,p,u),this.startYMin=sH(h,c,x),this.startYMax=sH(h,d,x),this.offsetX=0,this.offsetY=0),this.options.typeOptions.point={x:this.startXMin,y:this.startYMin},this.userOptions.typeOptions.point={x:this.startXMin,y:this.startYMin}}class sZ extends iL{init(t,i,s){super.init(t,i,s),this.offsetX=0,this.offsetY=0,this.resizeX=0,this.resizeY=0,sj.call(this),this.addValues(),this.addShapes()}setClipAxes(){this.clipXAxis=this.chart.xAxis[this.options.typeOptions.xAxis],this.clipYAxis=this.chart.yAxis[this.options.typeOptions.yAxis]}shapePointsOptions(){let t=this.options.typeOptions,i=t.xAxis,s=t.yAxis;return[{x:this.xAxisMin,y:this.yAxisMin,xAxis:i,yAxis:s},{x:this.xAxisMax,y:this.yAxisMin,xAxis:i,yAxis:s},{x:this.xAxisMax,y:this.yAxisMax,xAxis:i,yAxis:s},{x:this.xAxisMin,y:this.yAxisMax,xAxis:i,yAxis:s},{command:"Z"}]}addControlPoints(){let t=this.chart.inverted,i=this.options.controlPointOptions,s=this.options.typeOptions.selectType;sX(this.userOptions.controlPointOptions?.style?.cursor)||("x"===s?i.style.cursor=t?"ns-resize":"ew-resize":"y"!==s||(i.style.cursor=t?"ew-resize":"ns-resize"));let e=new R(this.chart,this,this.options.controlPointOptions,0);this.controlPoints.push(e),"xy"!==s&&(e=new R(this.chart,this,this.options.controlPointOptions,1),this.controlPoints.push(e))}addValues(t){let i=this.options.typeOptions,s=i.label.formatter;sG.call(this,t),i.label.enabled&&(this.labels.length>0?this.labels[0].text=s&&s.call(this)||sU.call(this):this.initLabel(sS({shape:"rect",backgroundColor:"none",color:"black",borderWidth:0,dashStyle:"Dash",overflow:"allow",align:"left",y:0,x:0,verticalAlign:"top",crop:!0,xAxis:0,yAxis:0,point:function(t){let s=t.annotation,e=t.options;return{x:s.xAxisMin,y:s.yAxisMin,xAxis:sR(i.xAxis,e.xAxis),yAxis:sR(i.yAxis,e.yAxis)}},text:s&&s.call(this)||sU.call(this)},i.label),void 0))}addShapes(){this.addCrosshairs(),this.addBackground()}addBackground(){let t=this.shapePointsOptions();void 0!==t[0].x&&this.initShape(sS({type:"path",points:t,className:"highcharts-measure-background"},this.options.typeOptions.background),2)}addCrosshairs(){let t=this.chart,i=this.options.typeOptions,s=this.options.typeOptions.point,e=t.xAxis[i.xAxis],o=t.yAxis[i.yAxis],n=t.inverted,a={point:s,type:"path"},r=e.toPixels(this.xAxisMin),h=e.toPixels(this.xAxisMax),l=o.toPixels(this.yAxisMin),p=o.toPixels(this.yAxisMax),c=[],d=[],u,x,g;n&&(g=r,r=l,l=g,g=h,h=p,p=g),i.crosshairX.enabled&&(c=[["M",r,l+(p-l)/2],["L",h,l+(p-l)/2]]),i.crosshairY.enabled&&(d=[["M",r+(h-r)/2,l],["L",r+(h-r)/2,p]]),this.shapes.length>0?(this.shapes[0].options.d=c,this.shapes[1].options.d=d):(u=sD(a,{className:"highcharts-measure-crosshair-x"},i.crosshairX),x=sD(a,{className:"highcharts-measure-crosshair-y"},i.crosshairY),this.initShape(sS({d:c},u),0),this.initShape(sS({d:d},x),1))}onDrag(t){let i=this.mouseMoveToTranslation(t),s=this.options.typeOptions.selectType,e="y"===s?0:i.x,o="x"===s?0:i.y;this.translate(e,o),this.offsetX+=e,this.offsetY+=o,this.redraw(!1,!1,!0)}resize(t,i,s,e){let o=this.shapes[2];"x"===e?0===s?(o.translatePoint(t,0,0),o.translatePoint(t,i,3)):(o.translatePoint(t,0,1),o.translatePoint(t,i,2)):"y"===e?0===s?(o.translatePoint(0,i,0),o.translatePoint(0,i,1)):(o.translatePoint(0,i,2),o.translatePoint(0,i,3)):(o.translatePoint(t,0,1),o.translatePoint(t,i,2),o.translatePoint(0,i,3)),sK.call(this,!1,!0,s,t,i),this.options.typeOptions.background.height=Math.abs(this.startYMax-this.startYMin),this.options.typeOptions.background.width=Math.abs(this.startXMax-this.startXMin)}redraw(t,i,s){this.linkPoints(),this.graphic||this.render(),s&&sK.call(this,!0,!1),this.clipRect&&this.clipRect.animate(this.getClipBox()),this.addValues(i),this.addCrosshairs(),this.redrawItems(this.shapes,t),this.redrawItems(this.labels,t);let e=this.options.typeOptions.background;if(e?.strokeWidth&&this.shapes[2]?.graphic){let t=e.strokeWidth/2,i=this.shapes[2],s=i.graphic.pathArray,o=s[0],n=s[1],a=s[2],r=s[3];o[1]=(o[1]||0)+t,n[1]=(n[1]||0)-t,a[1]=(a[1]||0)-t,r[1]=(r[1]||0)+t,o[2]=(o[2]||0)+t,n[2]=(n[2]||0)+t,a[2]=(a[2]||0)-t,r[2]=(r[2]||0)-t,i.graphic.attr({d:s})}this.controlPoints.forEach(t=>t.redraw())}translate(t,i){this.shapes.forEach(s=>s.translate(t,i))}}sZ.prototype.defaultOptions=sD(iL.prototype.defaultOptions,{typeOptions:{selectType:"xy",xAxis:0,yAxis:0,background:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0,stroke:void 0},crosshairX:{enabled:!0,zIndex:6,dashStyle:"Dash",markerEnd:"arrow"},crosshairY:{enabled:!0,zIndex:6,dashStyle:"Dash",markerEnd:"arrow"},label:{enabled:!0,style:{fontSize:"0.7em",color:"#666666"},formatter:void 0}},controlPointOptions:{positioner:function(t){let i=this.index,s=t.chart,e=t.options,o=e.typeOptions,n=o.selectType,a=e.controlPointOptions,r=s.inverted,h=s.xAxis[o.xAxis],l=s.yAxis[o.yAxis],p=sV(t.xAxisMin,t.xAxisMax,t.yAxisMin,t.yAxisMax),c=t.xAxisMax,d=t.yAxisMax,u,x;return"x"===n&&(d=(p.yAxisMax+p.yAxisMin)/2,0===i&&(c=t.xAxisMin)),"y"===n&&(c=p.xAxisMin+(p.xAxisMax-p.xAxisMin)/2,0===i&&(d=t.yAxisMin)),r?(u=l.toPixels(d),x=h.toPixels(c)):(u=h.toPixels(c),x=l.toPixels(d)),{x:u-a.width/2,y:x-a.height/2}},events:{drag:function(t,i){let s=this.mouseMoveToTranslation(t),e=i.options.typeOptions.selectType,o=this.index,n="y"===e?0:s.x,a="x"===e?0:s.y;i.resize(n,a,o,e),i.resizeX+=n,i.resizeY+=a,i.redraw(!1,!0)}}}}),iL.types.measure=sZ;let s$=u();return c.default})());