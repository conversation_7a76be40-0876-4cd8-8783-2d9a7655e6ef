{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/stock-tools\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Advanced Highcharts Stock tools\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"AST\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/stock-tools\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Templating\"],amd1[\"Series\"],amd1[\"AST\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/stock-tools\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"Series\"], root[\"_Highcharts\"][\"AST\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"AST\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__660__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 820:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ stock_tools_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(chart) {\n            this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        addUpdate(updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        }\n        /**\n         * @private\n         */\n        update(options, redraw) {\n            this.updates.forEach((updateFn) => {\n                updateFn.call(this.chart, options, redraw);\n            });\n        }\n    }\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined, isNumber, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define types for editable fields per annotation. There is no need to define\n * numbers, because they won't change their type to string.\n * @private\n */\nconst annotationsFieldsTypes = {\n    backgroundColor: 'string',\n    borderColor: 'string',\n    borderRadius: 'string',\n    color: 'string',\n    fill: 'string',\n    fontSize: 'string',\n    labels: 'string',\n    name: 'string',\n    stroke: 'string',\n    title: 'string'\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns the first xAxis or yAxis that was clicked with its value.\n *\n * @private\n *\n * @param {Array<Highcharts.PointerAxisCoordinateObject>} coords\n *        All the chart's x or y axes with a current pointer's axis value.\n *\n * @return {Highcharts.PointerAxisCoordinateObject}\n *         Object with a first found axis and its value that pointer\n *         is currently pointing.\n */\nfunction getAssignedAxis(coords) {\n    return coords.filter((coord) => {\n        const extremes = coord.axis.getExtremes(), axisMin = extremes.min, axisMax = extremes.max, \n        // Correct axis edges when axis has series\n        // with pointRange (like column)\n        minPointOffset = pick(coord.axis.minPointOffset, 0);\n        return isNumber(axisMin) && isNumber(axisMax) &&\n            coord.value >= (axisMin - minPointOffset) &&\n            coord.value <= (axisMax + minPointOffset) &&\n            // Don't count navigator axis\n            !coord.axis.options.isInternal;\n    })[0]; // If the axes overlap, return the first axis that was found.\n}\n/**\n * Get field type according to value\n *\n * @private\n *\n * @param {'boolean'|'number'|'string'} value\n * Atomic type (one of: string, number, boolean)\n *\n * @return {'checkbox'|'number'|'text'}\n * Field type (one of: text, number, checkbox)\n */\nfunction getFieldType(key, value) {\n    const predefinedType = annotationsFieldsTypes[key];\n    let fieldType = typeof value;\n    if (defined(predefinedType)) {\n        fieldType = predefinedType;\n    }\n    return {\n        'string': 'text',\n        'number': 'number',\n        'boolean': 'checkbox'\n    }[fieldType];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingUtilities = {\n    annotationsFieldsTypes,\n    getAssignedAxis,\n    getFieldType\n};\n/* harmony default export */ const NavigationBindingsUtilities = (NavigationBindingUtilities);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getAssignedAxis: NavigationBindingsDefaults_getAssignedAxis } = NavigationBindingsUtilities;\n\nconst { isNumber: NavigationBindingsDefaults_isNumber, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * Configure the Popup strings in the chart. Requires the\n     * `annotations.js` or `annotations-advanced.js` module to be\n     * loaded.\n     * @since   7.0.0\n     * @product highcharts highstock\n     */\n    navigation: {\n        /**\n         * Translations for all field names used in popup.\n         *\n         * @product highcharts highstock\n         */\n        popup: {\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            shapeOptions: 'Shape options',\n            typeOptions: 'Details',\n            fill: 'Fill',\n            format: 'Text',\n            strokeWidth: 'Line width',\n            stroke: 'Line color',\n            title: 'Title',\n            name: 'Name',\n            labelOptions: 'Label options',\n            labels: 'Labels',\n            backgroundColor: 'Background color',\n            backgroundColors: 'Background colors',\n            borderColor: 'Border color',\n            borderRadius: 'Border radius',\n            borderWidth: 'Border width',\n            style: 'Style',\n            padding: 'Padding',\n            fontSize: 'Font size',\n            color: 'Color',\n            height: 'Height',\n            shapes: 'Shape options'\n        }\n    }\n};\n/**\n * @optionparent navigation\n * @product      highcharts highstock\n */\nconst navigation = {\n    /**\n     * A CSS class name where all bindings will be attached to. Multiple\n     * charts on the same page should have separate class names to prevent\n     * duplicating events.\n     *\n     * Default value of versions < 7.0.4 `highcharts-bindings-wrapper`\n     *\n     * @since     7.0.0\n     * @type      {string}\n     */\n    bindingsClassName: 'highcharts-bindings-container',\n    /**\n     * Bindings definitions for custom HTML buttons. Each binding implements\n     * simple event-driven interface:\n     *\n     * - `className`: classname used to bind event to\n     *\n     * - `init`: initial event, fired on button click\n     *\n     * - `start`: fired on first click on a chart\n     *\n     * - `steps`: array of sequential events fired one after another on each\n     *   of users clicks\n     *\n     * - `end`: last event to be called after last step event\n     *\n     * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>|*}\n     *\n     * @sample {highstock} stock/stocktools/stocktools-thresholds\n     *               Custom bindings\n     * @sample {highcharts} highcharts/annotations/bindings/\n     *               Simple binding\n     * @sample {highcharts} highcharts/annotations/bindings-custom-annotation/\n     *               Custom annotation binding\n     *\n     * @since        7.0.0\n     * @requires     modules/annotations\n     * @product      highcharts highstock\n     */\n    bindings: {\n        /**\n         * A circle annotation bindings. Includes `start` and one event in\n         * `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-circle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        circleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-circle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(merge({\n                    langKey: 'circle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'circle',\n                            point: {\n                                x: coordsX.value,\n                                y: coordsY.value,\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index\n                            },\n                            r: 5\n                        }]\n                }, navigation.annotationsOptions, navigation.bindings.circleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    const shapes = annotation.options.shapes, mockPointOpts = ((shapes && shapes[0] && shapes[0].point) ||\n                        {});\n                    let distance;\n                    if (NavigationBindingsDefaults_isNumber(mockPointOpts.xAxis) &&\n                        NavigationBindingsDefaults_isNumber(mockPointOpts.yAxis)) {\n                        const inverted = this.chart.inverted, x = this.chart.xAxis[mockPointOpts.xAxis]\n                            .toPixels(mockPointOpts.x), y = this.chart.yAxis[mockPointOpts.yAxis]\n                            .toPixels(mockPointOpts.y);\n                        distance = Math.max(Math.sqrt(Math.pow(inverted ? y - e.chartX : x - e.chartX, 2) +\n                            Math.pow(inverted ? x - e.chartY : y - e.chartY, 2)), 5);\n                    }\n                    annotation.update({\n                        shapes: [{\n                                r: distance\n                            }]\n                    });\n                }\n            ]\n        },\n        /**\n         * A ellipse annotation bindings. Includes `start` and two events in\n         * `steps` array. First updates the second point, responsible for a\n         * rx width, and second updates the ry width.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-ellipse-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        ellipseAnnotation: {\n            className: 'highcharts-ellipse-annotation',\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(merge({\n                    langKey: 'ellipse',\n                    type: 'basicAnnotation',\n                    shapes: [\n                        {\n                            type: 'ellipse',\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }, {\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }],\n                            ry: 1\n                        }\n                    ]\n                }, navigation.annotationsOptions, navigation.bindings.ellipseAnnotation\n                    .annotationsOptions));\n            },\n            steps: [\n                function (e, annotation) {\n                    const target = annotation.shapes[0], position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                },\n                function (e, annotation) {\n                    const target = annotation.shapes[0], position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), newR = target.getDistanceFromLine(position, position2, e.chartX, e.chartY), yAxis = target.getYAxis(), newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            ]\n        },\n        /**\n         * A rectangle annotation bindings. Includes `start` and one event\n         * in `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-rectangle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        rectangleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-rectangle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                const x = coordsX.value, y = coordsY.value, xAxis = coordsX.axis.index, yAxis = coordsY.axis.index, navigation = this.chart.options.navigation;\n                return this.chart.addAnnotation(merge({\n                    langKey: 'rectangle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'path',\n                            points: [\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { command: 'Z' }\n                            ]\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .rectangleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    const shapes = annotation.options.shapes, points = ((shapes && shapes[0] && shapes[0].points) ||\n                        []), coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                    if (coordsX && coordsY) {\n                        const x = coordsX.value, y = coordsY.value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        annotation.update({\n                            shapes: [{\n                                    points: points\n                                }]\n                        });\n                    }\n                }\n            ]\n        },\n        /**\n         * A label annotation bindings. Includes `start` event only.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-label-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        labelAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-label-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(merge({\n                    langKey: 'label',\n                    type: 'basicAnnotation',\n                    labelOptions: {\n                        format: '{y:.2f}',\n                        overflow: 'none',\n                        crop: true\n                    },\n                    labels: [{\n                            point: {\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index,\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .labelAnnotation\n                    .annotationsOptions));\n            }\n        }\n    },\n    /**\n     * Path where Highcharts will look for icons. Change this to use icons\n     * from a different server.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/12.2.0/gfx/stock-icons/\n     * @since     7.1.3\n     * @apioption navigation.iconsURL\n     */\n    /**\n     * A `showPopup` event. Fired when selecting for example an annotation.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.showPopup\n     */\n    /**\n     * A `closePopup` event. Fired when Popup should be hidden, for example\n     * when clicking on an annotation again.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.closePopup\n     */\n    /**\n     * Event fired on a button click.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.selectButton\n     */\n    /**\n     * Event fired when button state should change, for example after\n     * adding an annotation.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.deselectButton\n     */\n    /**\n     * Events to communicate between Stock Tools and custom GUI.\n     *\n     * @since        7.0.0\n     * @product      highcharts highstock\n     * @optionparent navigation.events\n     */\n    events: {},\n    /**\n     * Additional options to be merged into all annotations.\n     *\n     * @sample stock/stocktools/navigation-annotation-options\n     *         Set red color of all line annotations\n     *\n     * @type      {Highcharts.AnnotationsOptions}\n     * @extends   annotations\n     * @exclude   crookedLine, elliottWave, fibonacci, infinityLine,\n     *            measure, pitchfork, tunnel, verticalLine, basicAnnotation\n     * @requires     modules/annotations\n     * @apioption navigation.annotationsOptions\n     */\n    annotationsOptions: {\n        animation: {\n            defer: 0\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingDefaults = {\n    lang,\n    navigation\n};\n/* harmony default export */ const NavigationBindingsDefaults = (NavigationBindingDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindings.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { composed, doc, win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { getAssignedAxis: NavigationBindings_getAssignedAxis, getFieldType: NavigationBindings_getFieldType } = NavigationBindingsUtilities;\n\nconst { addEvent, attr, defined: NavigationBindings_defined, fireEvent, isArray, isFunction, isNumber: NavigationBindings_isNumber, isObject, merge: NavigationBindings_merge, objectEach, pick: NavigationBindings_pick, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * IE 9-11 polyfill for Element.closest():\n * @private\n */\nfunction closestPolyfill(el, s) {\n    const ElementProto = win.Element.prototype, elementMatches = ElementProto.matches ||\n        ElementProto.msMatchesSelector ||\n        ElementProto.webkitMatchesSelector;\n    let ret = null;\n    if (ElementProto.closest) {\n        ret = ElementProto.closest.call(el, s);\n    }\n    else {\n        do {\n            if (elementMatches.call(el, s)) {\n                return el;\n            }\n            el = el.parentElement || el.parentNode;\n        } while (el !== null && el.nodeType === 1);\n    }\n    return ret;\n}\n/**\n * @private\n */\nfunction onAnnotationRemove() {\n    if (this.chart.navigationBindings) {\n        this.chart.navigationBindings.deselectAnnotation();\n    }\n}\n/**\n * @private\n */\nfunction onChartDestroy() {\n    if (this.navigationBindings) {\n        this.navigationBindings.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartLoad() {\n    const options = this.options;\n    if (options && options.navigation && options.navigation.bindings) {\n        this.navigationBindings = new NavigationBindings(this, options.navigation);\n        this.navigationBindings.initEvents();\n        this.navigationBindings.initUpdate();\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    const navigationBindings = this.navigationBindings, disabledClassName = 'highcharts-disabled-btn';\n    if (this && navigationBindings) {\n        // Check if the buttons should be enabled/disabled based on\n        // visible series.\n        let buttonsEnabled = false;\n        this.series.forEach((series) => {\n            if (!series.options.isInternal && series.visible) {\n                buttonsEnabled = true;\n            }\n        });\n        if (this.navigationBindings &&\n            this.navigationBindings.container &&\n            this.navigationBindings.container[0]) {\n            const container = this.navigationBindings.container[0];\n            objectEach(navigationBindings.boundClassNames, (value, key) => {\n                // Get the HTML element corresponding to the className taken\n                // from StockToolsBindings.\n                const buttonNode = container.querySelectorAll('.' + key);\n                if (buttonNode) {\n                    for (let i = 0; i < buttonNode.length; i++) {\n                        const button = buttonNode[i], cls = button.className;\n                        if (value.noDataState === 'normal') {\n                            // If button has noDataState: 'normal', and has\n                            // disabledClassName, remove this className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                        else if (!buttonsEnabled) {\n                            if (cls.indexOf(disabledClassName) === -1) {\n                                button.className += ' ' + disabledClassName;\n                            }\n                        }\n                        else {\n                            // Enable all buttons by deleting the className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                    }\n                }\n            });\n        }\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsClosePopup() {\n    this.deselectAnnotation();\n}\n/**\n * @private\n */\nfunction onNavigationBindingsDeselectButton() {\n    this.selectedButtonElement = null;\n}\n/**\n * Show edit-annotation form:\n * @private\n */\nfunction selectableAnnotation(annotationType) {\n    const originalClick = annotationType.prototype.defaultOptions.events &&\n        annotationType.prototype.defaultOptions.events.click;\n    /**\n     * Select and show popup\n     * @private\n     */\n    function selectAndShowPopup(eventArguments) {\n        const annotation = this, navigation = annotation.chart.navigationBindings, prevAnnotation = navigation.activeAnnotation;\n        if (originalClick) {\n            originalClick.call(annotation, eventArguments);\n        }\n        if (prevAnnotation !== annotation) {\n            // Select current:\n            navigation.deselectAnnotation();\n            navigation.activeAnnotation = annotation;\n            annotation.setControlPointsVisibility(true);\n            fireEvent(navigation, 'showPopup', {\n                annotation: annotation,\n                formType: 'annotation-toolbar',\n                options: navigation.annotationToFields(annotation),\n                onSubmit: function (data) {\n                    if (data.actionType === 'remove') {\n                        navigation.activeAnnotation = false;\n                        navigation.chart.removeAnnotation(annotation);\n                    }\n                    else {\n                        const config = {};\n                        navigation.fieldsToOptions(data.fields, config);\n                        navigation.deselectAnnotation();\n                        const typeOptions = config.typeOptions;\n                        if (annotation.options.type === 'measure') {\n                            // Manually disable crooshars according to\n                            // stroke width of the shape:\n                            typeOptions.crosshairY.enabled = (typeOptions.crosshairY\n                                .strokeWidth !== 0);\n                            typeOptions.crosshairX.enabled = (typeOptions.crosshairX\n                                .strokeWidth !== 0);\n                        }\n                        annotation.update(config);\n                    }\n                }\n            });\n        }\n        else {\n            // Deselect current:\n            fireEvent(navigation, 'closePopup');\n        }\n        // Let bubble event to chart.click:\n        eventArguments.activeAnnotation = true;\n    }\n    // #18276, show popup on touchend, but not on touchmove\n    let touchStartX, touchStartY;\n    /**\n     *\n     */\n    function saveCoords(e) {\n        touchStartX = e.touches[0].clientX;\n        touchStartY = e.touches[0].clientY;\n    }\n    /**\n     *\n     */\n    function checkForTouchmove(e) {\n        const hasMoved = touchStartX ? Math.sqrt(Math.pow(touchStartX - e.changedTouches[0].clientX, 2) +\n            Math.pow(touchStartY - e.changedTouches[0].clientY, 2)) >= 4 : false;\n        if (!hasMoved) {\n            selectAndShowPopup.call(this, e);\n        }\n    }\n    NavigationBindings_merge(true, annotationType.prototype.defaultOptions.events, {\n        click: selectAndShowPopup,\n        touchstart: saveCoords,\n        touchend: checkForTouchmove\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nclass NavigationBindings {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AnnotationClass, ChartClass) {\n        if (pushUnique(composed, 'NavigationBindings')) {\n            addEvent(AnnotationClass, 'remove', onAnnotationRemove);\n            // Basic shapes:\n            selectableAnnotation(AnnotationClass);\n            // Advanced annotations:\n            objectEach(AnnotationClass.types, (annotationType) => {\n                selectableAnnotation(annotationType);\n            });\n            addEvent(ChartClass, 'destroy', onChartDestroy);\n            addEvent(ChartClass, 'load', onChartLoad);\n            addEvent(ChartClass, 'render', onChartRender);\n            addEvent(NavigationBindings, 'closePopup', onNavigationBindingsClosePopup);\n            addEvent(NavigationBindings, 'deselectButton', onNavigationBindingsDeselectButton);\n            setOptions(NavigationBindingsDefaults);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, options) {\n        this.boundClassNames = void 0;\n        this.chart = chart;\n        this.options = options;\n        this.eventsToUnbind = [];\n        this.container =\n            this.chart.container.getElementsByClassName(this.options.bindingsClassName || '');\n        if (!this.container.length) {\n            this.container = doc.getElementsByClassName(this.options.bindingsClassName || '');\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getCoords(e) {\n        const coords = this.chart.pointer?.getCoordinates(e);\n        return [\n            coords && NavigationBindings_getAssignedAxis(coords.xAxis),\n            coords && NavigationBindings_getAssignedAxis(coords.yAxis)\n        ];\n    }\n    /**\n     * Init all events connected to NavigationBindings.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initEvents\n     */\n    initEvents() {\n        const navigation = this, chart = navigation.chart, bindingsContainer = navigation.container, options = navigation.options;\n        // Shorthand object for getting events for buttons:\n        navigation.boundClassNames = {};\n        objectEach((options.bindings || {}), (value) => {\n            navigation.boundClassNames[value.className] = value;\n        });\n        // Handle multiple containers with the same class names:\n        [].forEach.call(bindingsContainer, (subContainer) => {\n            navigation.eventsToUnbind.push(addEvent(subContainer, 'click', (event) => {\n                const bindings = navigation.getButtonEvents(subContainer, event);\n                if (bindings &&\n                    (!bindings.button.classList\n                        .contains('highcharts-disabled-btn'))) {\n                    navigation.bindingsButtonClick(bindings.button, bindings.events, event);\n                }\n            }));\n        });\n        objectEach((options.events || {}), (callback, eventName) => {\n            if (isFunction(callback)) {\n                navigation.eventsToUnbind.push(addEvent(navigation, eventName, callback, { passive: false }));\n            }\n        });\n        navigation.eventsToUnbind.push(addEvent(chart.container, 'click', function (e) {\n            if (!chart.cancelClick &&\n                chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                navigation.bindingsChartClick(this, e);\n            }\n        }));\n        navigation.eventsToUnbind.push(addEvent(chart.container, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? 'touchmove' : 'mousemove', function (e) {\n            navigation.bindingsContainerMouseMove(this, e);\n        }, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? { passive: false } : void 0));\n    }\n    /**\n     * Common chart.update() delegation, shared between bindings and exporting.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initUpdate\n     */\n    initUpdate() {\n        const navigation = this;\n        Chart_ChartNavigationComposition\n            .compose(this.chart).navigation\n            .addUpdate((options) => {\n            navigation.update(options);\n        });\n    }\n    /**\n     * Hook for click on a button, method selects/unselects buttons,\n     * then calls `bindings.init` callback.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsButtonClick\n     *\n     * @param {Highcharts.HTMLDOMElement} [button]\n     *        Clicked button\n     *\n     * @param {Object} events\n     *        Events passed down from bindings (`init`, `start`, `step`, `end`)\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event\n     */\n    bindingsButtonClick(button, events, clickEvent) {\n        const navigation = this, chart = navigation.chart, svgContainer = chart.renderer.boxWrapper;\n        let shouldEventBeFired = true;\n        if (navigation.selectedButtonElement) {\n            if (navigation.selectedButtonElement.classList === button.classList) {\n                shouldEventBeFired = false;\n            }\n            fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n            if (navigation.nextEvent) {\n                // Remove in-progress annotations adders:\n                if (navigation.currentUserDetails &&\n                    navigation.currentUserDetails.coll === 'annotations') {\n                    chart.removeAnnotation(navigation.currentUserDetails);\n                }\n                navigation.mouseMoveEvent = navigation.nextEvent = false;\n            }\n        }\n        if (shouldEventBeFired) {\n            navigation.selectedButton = events;\n            navigation.selectedButtonElement = button;\n            fireEvent(navigation, 'selectButton', { button: button });\n            // Call \"init\" event, for example to open modal window\n            if (events.init) {\n                events.init.call(navigation, button, clickEvent);\n            }\n            if (events.start || events.steps) {\n                chart.renderer.boxWrapper.addClass('highcharts-draw-mode');\n            }\n        }\n        else {\n            chart.stockTools && button.classList.remove('highcharts-active');\n            svgContainer.removeClass('highcharts-draw-mode');\n            navigation.nextEvent = false;\n            navigation.mouseMoveEvent = false;\n            navigation.selectedButton = null;\n        }\n    }\n    /**\n     * Hook for click on a chart, first click on a chart calls `start` event,\n     * then on all subsequent clicks iterate over `steps` array.\n     * When finished, calls `end` event.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsChartClick\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that click was performed on.\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event.\n     */\n    bindingsChartClick(chart, clickEvent) {\n        chart = this.chart;\n        const navigation = this, activeAnnotation = navigation.activeAnnotation, selectedButton = navigation.selectedButton, svgContainer = chart.renderer.boxWrapper;\n        if (activeAnnotation) {\n            // Click outside popups, should close them and deselect the\n            // annotation\n            if (!activeAnnotation.cancelClick && // #15729\n                !clickEvent.activeAnnotation &&\n                // Element could be removed in the child action, e.g. button\n                clickEvent.target.parentNode &&\n                // TO DO: Polyfill for IE11?\n                !closestPolyfill(clickEvent.target, '.highcharts-popup')) {\n                fireEvent(navigation, 'closePopup');\n            }\n            else if (activeAnnotation.cancelClick) {\n                // Reset cancelClick after the other event handlers have run\n                setTimeout(() => {\n                    activeAnnotation.cancelClick = false;\n                }, 0);\n            }\n        }\n        if (!selectedButton || !selectedButton.start) {\n            return;\n        }\n        if (!navigation.nextEvent) {\n            // Call init method:\n            navigation.currentUserDetails = selectedButton.start.call(navigation, clickEvent);\n            // If steps exists (e.g. Annotations), bind them:\n            if (navigation.currentUserDetails && selectedButton.steps) {\n                navigation.stepIndex = 0;\n                navigation.steps = true;\n                navigation.mouseMoveEvent = navigation.nextEvent =\n                    selectedButton.steps[navigation.stepIndex];\n            }\n            else {\n                fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                svgContainer.removeClass('highcharts-draw-mode');\n                navigation.steps = false;\n                navigation.selectedButton = null;\n                // First click is also the last one:\n                if (selectedButton.end) {\n                    selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                }\n            }\n        }\n        else {\n            navigation.nextEvent(clickEvent, navigation.currentUserDetails);\n            if (navigation.steps) {\n                navigation.stepIndex++;\n                if (selectedButton.steps[navigation.stepIndex]) {\n                    // If we have more steps, bind them one by one:\n                    navigation.mouseMoveEvent = navigation.nextEvent = selectedButton.steps[navigation.stepIndex];\n                }\n                else {\n                    fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                    svgContainer.removeClass('highcharts-draw-mode');\n                    // That was the last step, call end():\n                    if (selectedButton.end) {\n                        selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                    }\n                    navigation.nextEvent = false;\n                    navigation.mouseMoveEvent = false;\n                    navigation.selectedButton = null;\n                }\n            }\n        }\n    }\n    /**\n     * Hook for mouse move on a chart's container. It calls current step.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsContainerMouseMove\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Chart's container.\n     *\n     * @param {global.Event} moveEvent\n     *        Browser's move event.\n     */\n    bindingsContainerMouseMove(_container, moveEvent) {\n        if (this.mouseMoveEvent) {\n            this.mouseMoveEvent(moveEvent, this.currentUserDetails);\n        }\n    }\n    /**\n     * Translate fields (e.g. `params.period` or `marker.styles.color`) to\n     * Highcharts options object (e.g. `{ params: { period } }`).\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#fieldsToOptions<T>\n     *\n     * @param {Highcharts.Dictionary<string>} fields\n     *        Fields from popup form.\n     *\n     * @param {T} config\n     *        Default config to be modified.\n     *\n     * @return {T}\n     *         Modified config\n     */\n    fieldsToOptions(fields, config) {\n        objectEach(fields, (value, field) => {\n            const parsedValue = parseFloat(value), path = field.split('.'), pathLength = path.length - 1;\n            // If it's a number (not \"format\" options), parse it:\n            if (NavigationBindings_isNumber(parsedValue) &&\n                !value.match(/px|em/g) &&\n                !field.match(/format/g)) {\n                value = parsedValue;\n            }\n            // Remove values like 0\n            if (value !== 'undefined') {\n                let parent = config;\n                path.forEach((name, index) => {\n                    if (name !== '__proto__' && name !== 'constructor') {\n                        const nextName = NavigationBindings_pick(path[index + 1], '');\n                        if (pathLength === index) {\n                            // Last index, put value:\n                            parent[name] = value;\n                        }\n                        else if (!parent[name]) {\n                            // Create middle property:\n                            parent[name] = nextName.match(/\\d/g) ?\n                                [] :\n                                {};\n                            parent = parent[name];\n                        }\n                        else {\n                            // Jump into next property\n                            parent = parent[name];\n                        }\n                    }\n                });\n            }\n        });\n        return config;\n    }\n    /**\n     * Shorthand method to deselect an annotation.\n     *\n     * @function Highcharts.NavigationBindings#deselectAnnotation\n     */\n    deselectAnnotation() {\n        if (this.activeAnnotation) {\n            this.activeAnnotation.setControlPointsVisibility(false);\n            this.activeAnnotation = false;\n        }\n    }\n    /**\n     * Generates API config for popup in the same format as options for\n     * Annotation object.\n     *\n     * @function Highcharts.NavigationBindings#annotationToFields\n     *\n     * @param {Highcharts.Annotation} annotation\n     *        Annotations object\n     *\n     * @return {Highcharts.Dictionary<string>}\n     *         Annotation options to be displayed in popup box\n     */\n    annotationToFields(annotation) {\n        const options = annotation.options, editables = NavigationBindings.annotationsEditable, nestedEditables = editables.nestedOptions, type = NavigationBindings_pick(options.type, options.shapes && options.shapes[0] &&\n            options.shapes[0].type, options.labels && options.labels[0] &&\n            options.labels[0].type, 'label'), nonEditables = NavigationBindings.annotationsNonEditable[options.langKey] || [], visualOptions = {\n            langKey: options.langKey,\n            type: type\n        };\n        /**\n         * Nested options traversing. Method goes down to the options and copies\n         * allowed options (with values) to new object, which is last parameter:\n         * \"parent\".\n         *\n         * @private\n         *\n         * @param {*} option\n         *        Atomic type or object/array\n         *\n         * @param {string} key\n         *        Option name, for example \"visible\" or \"x\", \"y\"\n         *\n         * @param {Object} parentEditables\n         *        Editables from NavigationBindings.annotationsEditable\n         *\n         * @param {Object} parent\n         *        Where new options will be assigned\n         */\n        function traverse(option, key, parentEditables, parent, parentKey) {\n            let nextParent;\n            if (parentEditables &&\n                NavigationBindings_defined(option) &&\n                nonEditables.indexOf(key) === -1 &&\n                ((parentEditables.indexOf &&\n                    parentEditables.indexOf(key)) >= 0 ||\n                    parentEditables[key] || // Nested array\n                    parentEditables === true // Simple array\n                )) {\n                // Roots:\n                if (isArray(option)) {\n                    parent[key] = [];\n                    option.forEach((arrayOption, i) => {\n                        if (!isObject(arrayOption)) {\n                            // Simple arrays, e.g. [String, Number, Boolean]\n                            traverse(arrayOption, 0, nestedEditables[key], parent[key], key);\n                        }\n                        else {\n                            // Advanced arrays, e.g. [Object, Object]\n                            parent[key][i] = {};\n                            objectEach(arrayOption, (nestedOption, nestedKey) => {\n                                traverse(nestedOption, nestedKey, nestedEditables[key], parent[key][i], key);\n                            });\n                        }\n                    });\n                }\n                else if (isObject(option)) {\n                    nextParent = {};\n                    if (isArray(parent)) {\n                        parent.push(nextParent);\n                        nextParent[key] = {};\n                        nextParent = nextParent[key];\n                    }\n                    else {\n                        parent[key] = nextParent;\n                    }\n                    objectEach(option, (nestedOption, nestedKey) => {\n                        traverse(nestedOption, nestedKey, key === 0 ?\n                            parentEditables :\n                            nestedEditables[key], nextParent, key);\n                    });\n                }\n                else {\n                    // Leaf:\n                    if (key === 'format') {\n                        parent[key] = [\n                            format(option, annotation.labels[0].points[0]).toString(),\n                            'text'\n                        ];\n                    }\n                    else if (isArray(parent)) {\n                        parent.push([option, NavigationBindings_getFieldType(parentKey, option)]);\n                    }\n                    else {\n                        parent[key] = [option, NavigationBindings_getFieldType(key, option)];\n                    }\n                }\n            }\n        }\n        objectEach(options, (option, key) => {\n            if (key === 'typeOptions') {\n                visualOptions[key] = {};\n                objectEach(options[key], (typeOption, typeKey) => {\n                    traverse(typeOption, typeKey, nestedEditables, visualOptions[key], typeKey);\n                });\n            }\n            else {\n                traverse(option, key, editables[type], visualOptions, key);\n            }\n        });\n        return visualOptions;\n    }\n    /**\n     * Get all class names for all parents in the element. Iterates until finds\n     * main container.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getClickedClassNames\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     * Container that event is bound to.\n     *\n     * @param {global.Event} event\n     * Browser's event.\n     *\n     * @return {Array<Array<string, Highcharts.HTMLDOMElement>>}\n     * Array of class names with corresponding elements\n     */\n    getClickedClassNames(container, event) {\n        let element = event.target, classNames = [], elemClassName;\n        while (element && element.tagName) {\n            elemClassName = attr(element, 'class');\n            if (elemClassName) {\n                classNames = classNames.concat(elemClassName\n                    .split(' ')\n                    // eslint-disable-next-line no-loop-func\n                    .map((name) => ([name, element])));\n            }\n            element = element.parentNode;\n            if (element === container) {\n                return classNames;\n            }\n        }\n        return classNames;\n    }\n    /**\n     * Get events bound to a button. It's a custom event delegation to find all\n     * events connected to the element.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getButtonEvents\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Container that event is bound to.\n     *\n     * @param {global.Event} event\n     *        Browser's event.\n     *\n     * @return {Object}\n     *         Object with events (init, start, steps, and end)\n     */\n    getButtonEvents(container, event) {\n        const navigation = this, classNames = this.getClickedClassNames(container, event);\n        let bindings;\n        classNames.forEach((className) => {\n            if (navigation.boundClassNames[className[0]] && !bindings) {\n                bindings = {\n                    events: navigation.boundClassNames[className[0]],\n                    button: className[1]\n                };\n            }\n        });\n        return bindings;\n    }\n    /**\n     * Bindings are just events, so the whole update process is simply\n     * removing old events and adding new ones.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#update\n     */\n    update(options) {\n        this.options = NavigationBindings_merge(true, this.options, options);\n        this.removeEvents();\n        this.initEvents();\n    }\n    /**\n     * Remove all events created in the navigation.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#removeEvents\n     */\n    removeEvents() {\n        this.eventsToUnbind.forEach((unbinder) => unbinder());\n    }\n    /**\n     * @private\n     * @function Highcharts.NavigationBindings#destroy\n     */\n    destroy() {\n        this.removeEvents();\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n// Define which options from annotations should show up in edit box:\nNavigationBindings.annotationsEditable = {\n    // `typeOptions` are always available\n    // Nested and shared options:\n    nestedOptions: {\n        labelOptions: ['style', 'format', 'backgroundColor'],\n        labels: ['style'],\n        label: ['style'],\n        style: ['fontSize', 'color'],\n        background: ['fill', 'strokeWidth', 'stroke'],\n        innerBackground: ['fill', 'strokeWidth', 'stroke'],\n        outerBackground: ['fill', 'strokeWidth', 'stroke'],\n        shapeOptions: ['fill', 'strokeWidth', 'stroke'],\n        shapes: ['fill', 'strokeWidth', 'stroke'],\n        line: ['strokeWidth', 'stroke'],\n        backgroundColors: [true],\n        connector: ['fill', 'strokeWidth', 'stroke'],\n        crosshairX: ['strokeWidth', 'stroke'],\n        crosshairY: ['strokeWidth', 'stroke']\n    },\n    // Simple shapes:\n    circle: ['shapes'],\n    ellipse: ['shapes'],\n    verticalLine: [],\n    label: ['labelOptions'],\n    // Measure\n    measure: ['background', 'crosshairY', 'crosshairX'],\n    // Others:\n    fibonacci: [],\n    tunnel: ['background', 'line', 'height'],\n    pitchfork: ['innerBackground', 'outerBackground'],\n    rect: ['shapes'],\n    // Crooked lines, elliots, arrows etc:\n    crookedLine: [],\n    basicAnnotation: ['shapes', 'labelOptions']\n};\n// Define non editable fields per annotation, for example Rectangle inherits\n// options from Measure, but crosshairs are not available\nNavigationBindings.annotationsNonEditable = {\n    rectangle: ['crosshairX', 'crosshairY', 'labelOptions'],\n    ellipse: ['labelOptions'],\n    circle: ['labelOptions']\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_NavigationBindings = (NavigationBindings);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A config object for navigation bindings in annotations.\n *\n * @interface Highcharts.NavigationBindingsOptionsObject\n */ /**\n* ClassName of the element for a binding.\n* @name Highcharts.NavigationBindingsOptionsObject#className\n* @type {string|undefined}\n*/ /**\n* Last event to be fired after last step event.\n* @name Highcharts.NavigationBindingsOptionsObject#end\n* @type {Function|undefined}\n*/ /**\n* Initial event, fired on a button click.\n* @name Highcharts.NavigationBindingsOptionsObject#init\n* @type {Function|undefined}\n*/ /**\n* Event fired on first click on a chart.\n* @name Highcharts.NavigationBindingsOptionsObject#start\n* @type {Function|undefined}\n*/ /**\n* Last event to be fired after last step event. Array of step events to be\n* called sequentially after each user click.\n* @name Highcharts.NavigationBindingsOptionsObject#steps\n* @type {Array<Function>|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es-modules/Stock/StockTools/StockToolsUtilities.js\n/**\n *\n *  Events generator for Stock tools\n *\n *  (c) 2009-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { getAssignedAxis: StockToolsUtilities_getAssignedAxis, getFieldType: StockToolsUtilities_getFieldType } = NavigationBindingsUtilities;\n\n\nconst { defined: StockToolsUtilities_defined, fireEvent: StockToolsUtilities_fireEvent, isNumber: StockToolsUtilities_isNumber, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @private\n */\nconst indicatorsWithAxes = [\n    'apo',\n    'ad',\n    'aroon',\n    'aroonoscillator',\n    'atr',\n    'ao',\n    'cci',\n    'chaikin',\n    'cmf',\n    'cmo',\n    'disparityindex',\n    'dmi',\n    'dpo',\n    'linearRegressionAngle',\n    'linearRegressionIntercept',\n    'linearRegressionSlope',\n    'klinger',\n    'macd',\n    'mfi',\n    'momentum',\n    'natr',\n    'obv',\n    'ppo',\n    'roc',\n    'rsi',\n    'slowstochastic',\n    'stochastic',\n    'trix',\n    'williamsr'\n];\n/**\n * @private\n */\nconst indicatorsWithVolume = [\n    'ad',\n    'cmf',\n    'klinger',\n    'mfi',\n    'obv',\n    'vbp',\n    'vwap'\n];\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Generates function which will add a flag series using modal in GUI.\n * Method fires an event \"showPopup\" with config:\n * `{type, options, callback}`.\n *\n * Example: NavigationBindings.utils.addFlagFromForm('url(...)') - will\n * generate function that shows modal in GUI.\n *\n * @private\n * @function bindingsUtils.addFlagFromForm\n *\n * @param {Highcharts.FlagsShapeValue} type\n *        Type of flag series, e.g. \"squarepin\"\n *\n * @return {Function}\n *         Callback to be used in `start` callback\n */\nfunction addFlagFromForm(type) {\n    return function (e) {\n        const navigation = this, chart = navigation.chart, toolbar = chart.stockTools, point = attractToPoint(e, chart);\n        if (!point) {\n            return;\n        }\n        const pointConfig = {\n            x: point.x,\n            y: point.y\n        };\n        const seriesOptions = {\n            type: 'flags',\n            onSeries: point.series.id,\n            shape: type,\n            data: [pointConfig],\n            xAxis: point.xAxis,\n            yAxis: point.yAxis,\n            point: {\n                events: {\n                    click: function () {\n                        const point = this, options = point.options;\n                        StockToolsUtilities_fireEvent(navigation, 'showPopup', {\n                            point: point,\n                            formType: 'annotation-toolbar',\n                            options: {\n                                langKey: 'flags',\n                                type: 'flags',\n                                title: [\n                                    options.title,\n                                    StockToolsUtilities_getFieldType('title', options.title)\n                                ],\n                                name: [\n                                    options.name,\n                                    StockToolsUtilities_getFieldType('name', options.name)\n                                ]\n                            },\n                            onSubmit: function (updated) {\n                                if (updated.actionType === 'remove') {\n                                    point.remove();\n                                }\n                                else {\n                                    point.update(navigation.fieldsToOptions(updated.fields, {}));\n                                }\n                            }\n                        });\n                    }\n                }\n            }\n        };\n        if (!toolbar || !toolbar.guiEnabled) {\n            chart.addSeries(seriesOptions);\n        }\n        StockToolsUtilities_fireEvent(navigation, 'showPopup', {\n            formType: 'flag',\n            // Enabled options:\n            options: {\n                langKey: 'flags',\n                type: 'flags',\n                title: ['A', StockToolsUtilities_getFieldType('label', 'A')],\n                name: ['Flag A', StockToolsUtilities_getFieldType('label', 'Flag A')]\n            },\n            // Callback on submit:\n            onSubmit: function (data) {\n                navigation.fieldsToOptions(data.fields, seriesOptions.data[0]);\n                chart.addSeries(seriesOptions);\n            }\n        });\n    };\n}\n/**\n * @private\n * @todo\n * Consider using getHoverData(), but always kdTree (columns?)\n */\nfunction attractToPoint(e, chart) {\n    const coords = chart.pointer?.getCoordinates(e);\n    let coordsX, coordsY, distX = Number.MAX_VALUE, closestPoint;\n    if (chart.navigationBindings && coords) {\n        coordsX = StockToolsUtilities_getAssignedAxis(coords.xAxis);\n        coordsY = StockToolsUtilities_getAssignedAxis(coords.yAxis);\n    }\n    // Exit if clicked out of axes area.\n    if (!coordsX || !coordsY) {\n        return;\n    }\n    const x = coordsX.value;\n    const y = coordsY.value;\n    // Search by 'x' but only in yAxis' series.\n    coordsY.axis.series.forEach((series) => {\n        if (series.points) {\n            const point = series.searchPoint(e, true);\n            if (point && distX > Math.abs(point.x - x)) {\n                distX = Math.abs(point.x - x);\n                closestPoint = point;\n            }\n        }\n    });\n    if (closestPoint && closestPoint.x && closestPoint.y) {\n        return {\n            x: closestPoint.x,\n            y: closestPoint.y,\n            below: y < closestPoint.y,\n            series: closestPoint.series,\n            xAxis: closestPoint.series.xAxis.index || 0,\n            yAxis: closestPoint.series.yAxis.index || 0\n        };\n    }\n}\n/**\n * Shorthand to check if given yAxis comes from navigator.\n *\n * @private\n * @function bindingsUtils.isNotNavigatorYAxis\n *\n * @param {Highcharts.Axis} axis\n * Axis to check.\n *\n * @return {boolean}\n * True, if axis comes from navigator.\n */\nfunction isNotNavigatorYAxis(axis) {\n    return axis.userOptions.className !== 'highcharts-navigator-yaxis';\n}\n/**\n * Check if any of the price indicators are enabled.\n * @private\n * @function bindingsUtils.isLastPriceEnabled\n *\n * @param {Array} series\n *        Array of series.\n *\n * @return {boolean}\n *         Tells which indicator is enabled.\n */\nfunction isPriceIndicatorEnabled(series) {\n    return series.some((s) => s.lastVisiblePrice || s.lastPrice);\n}\n/**\n * @private\n */\nfunction manageIndicators(data) {\n    const chart = this.chart, seriesConfig = {\n        linkedTo: data.linkedTo,\n        type: data.type\n    };\n    let yAxis, parentSeries, defaultOptions, series;\n    if (data.actionType === 'edit') {\n        this.fieldsToOptions(data.fields, seriesConfig);\n        series = chart.get(data.seriesId);\n        if (series) {\n            series.update(seriesConfig, false);\n        }\n    }\n    else if (data.actionType === 'remove') {\n        series = chart.get(data.seriesId);\n        if (series) {\n            yAxis = series.yAxis;\n            if (series.linkedSeries) {\n                series.linkedSeries.forEach((linkedSeries) => {\n                    linkedSeries.remove(false);\n                });\n            }\n            series.remove(false);\n            if (indicatorsWithAxes.indexOf(series.type) >= 0) {\n                const removedYAxisProps = {\n                    height: yAxis.options.height,\n                    top: yAxis.options.top\n                };\n                yAxis.remove(false);\n                this.resizeYAxes(removedYAxisProps);\n            }\n        }\n    }\n    else {\n        seriesConfig.id = uniqueKey();\n        this.fieldsToOptions(data.fields, seriesConfig);\n        parentSeries = chart.get(seriesConfig.linkedTo);\n        defaultOptions = getOptions().plotOptions;\n        // Make sure that indicator uses the SUM approx if SUM approx is used\n        // by parent series (#13950).\n        if (typeof parentSeries !== 'undefined' &&\n            parentSeries instanceof (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()) &&\n            parentSeries.getDGApproximation() === 'sum' &&\n            // If indicator has defined approx type, use it (e.g. \"ranges\")\n            !StockToolsUtilities_defined(defaultOptions && defaultOptions[seriesConfig.type] &&\n                defaultOptions.dataGrouping &&\n                defaultOptions.dataGrouping.approximation)) {\n            seriesConfig.dataGrouping = {\n                approximation: 'sum'\n            };\n        }\n        if (indicatorsWithAxes.indexOf(data.type) >= 0) {\n            yAxis = chart.addAxis({\n                id: uniqueKey(),\n                offset: 0,\n                opposite: true,\n                title: {\n                    text: ''\n                },\n                tickPixelInterval: 40,\n                showLastLabel: false,\n                labels: {\n                    align: 'left',\n                    y: -2\n                }\n            }, false, false);\n            seriesConfig.yAxis = yAxis.options.id;\n            this.resizeYAxes();\n        }\n        else {\n            seriesConfig.yAxis = chart.get(data.linkedTo).options.yAxis;\n        }\n        if (indicatorsWithVolume.indexOf(data.type) >= 0) {\n            seriesConfig.params.volumeSeriesID = chart.series.filter(function (series) {\n                return series.options.type === 'column';\n            })[0].options.id;\n        }\n        chart.addSeries(seriesConfig, false);\n    }\n    StockToolsUtilities_fireEvent(this, 'deselectButton', {\n        button: this.selectedButtonElement\n    });\n    chart.redraw();\n}\n/**\n * Update height for an annotation. Height is calculated as a difference\n * between last point in `typeOptions` and current position. It's a value,\n * not pixels height.\n *\n * @private\n * @function bindingsUtils.updateHeight\n *\n * @param {Highcharts.PointerEventObject} e\n *        normalized browser event\n *\n * @param {Highcharts.Annotation} annotation\n *        Annotation to be updated\n */\nfunction updateHeight(e, annotation) {\n    const options = annotation.options.typeOptions, yAxis = StockToolsUtilities_isNumber(options.yAxis) && this.chart.yAxis[options.yAxis];\n    if (yAxis && options.points) {\n        annotation.update({\n            typeOptions: {\n                height: yAxis.toValue(e[yAxis.horiz ? 'chartX' : 'chartY']) -\n                    (options.points[1].y || 0)\n            }\n        });\n    }\n}\n/**\n * Update each point after specified index, most of the annotations use\n * this. For example crooked line: logic behind updating each point is the\n * same, only index changes when adding an annotation.\n *\n * Example: NavigationBindings.utils.updateNthPoint(1) - will generate\n * function that updates all consecutive points except point with index=0.\n *\n * @private\n * @function bindingsUtils.updateNthPoint\n *\n * @param {number} startIndex\n *        Index from which point should update\n *\n * @return {Function}\n *         Callback to be used in steps array\n */\nfunction updateNthPoint(startIndex) {\n    return function (e, annotation) {\n        const options = annotation.options.typeOptions, xAxis = StockToolsUtilities_isNumber(options.xAxis) && this.chart.xAxis[options.xAxis], yAxis = StockToolsUtilities_isNumber(options.yAxis) && this.chart.yAxis[options.yAxis];\n        if (xAxis && yAxis) {\n            options.points.forEach((point, index) => {\n                if (index >= startIndex) {\n                    point.x = xAxis.toValue(e[xAxis.horiz ? 'chartX' : 'chartY']);\n                    point.y = yAxis.toValue(e[yAxis.horiz ? 'chartX' : 'chartY']);\n                }\n            });\n            annotation.update({\n                typeOptions: {\n                    points: options.points\n                }\n            });\n        }\n    };\n}\n/**\n * Update size of background (rect) in some annotations: Measure, Simple\n * Rect.\n *\n * @private\n * @function Highcharts.NavigationBindingsUtilsObject.updateRectSize\n *\n * @param {Highcharts.PointerEventObject} event\n * Normalized browser event\n *\n * @param {Highcharts.Annotation} annotation\n * Annotation to be updated\n */\nfunction updateRectSize(event, annotation) {\n    const chart = annotation.chart, options = annotation.options.typeOptions, xAxis = StockToolsUtilities_isNumber(options.xAxis) && chart.xAxis[options.xAxis], yAxis = StockToolsUtilities_isNumber(options.yAxis) && chart.yAxis[options.yAxis];\n    if (xAxis && yAxis) {\n        const x = xAxis.toValue(event[xAxis.horiz ? 'chartX' : 'chartY']), y = yAxis.toValue(event[yAxis.horiz ? 'chartX' : 'chartY']), width = x - options.point.x, height = options.point.y - y;\n        annotation.update({\n            typeOptions: {\n                background: {\n                    width: chart.inverted ? height : width,\n                    height: chart.inverted ? width : height\n                }\n            }\n        });\n    }\n}\n/**\n * Compares two arrays of strings, checking their length and if corresponding\n * elements are equal.\n *\n * @param {string[]} a\n *        The first array to compare.\n * @param {string[]} b\n *        The second array to compare.\n * @return {boolean}\n *          Return `true` if the arrays are equal, otherwise `false`.\n */\nfunction shallowArraysEqual(a, b) {\n    if (!StockToolsUtilities_defined(a) || !StockToolsUtilities_defined(b)) {\n        return false;\n    }\n    if (a.length !== b.length) {\n        return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst StockToolsUtilities = {\n    indicatorsWithAxes,\n    indicatorsWithVolume,\n    addFlagFromForm,\n    attractToPoint,\n    getAssignedAxis: StockToolsUtilities_getAssignedAxis,\n    isNotNavigatorYAxis,\n    isPriceIndicatorEnabled,\n    manageIndicators,\n    shallowArraysEqual,\n    updateHeight,\n    updateNthPoint,\n    updateRectSize\n};\n/* harmony default export */ const StockTools_StockToolsUtilities = (StockToolsUtilities);\n\n;// ./code/es-modules/Stock/StockTools/StockToolsBindings.js\n/**\n *\n *  Events generator for Stock tools\n *\n *  (c) 2009-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { addFlagFromForm: StockToolsBindings_addFlagFromForm, attractToPoint: StockToolsBindings_attractToPoint, isNotNavigatorYAxis: StockToolsBindings_isNotNavigatorYAxis, isPriceIndicatorEnabled: StockToolsBindings_isPriceIndicatorEnabled, manageIndicators: StockToolsBindings_manageIndicators, updateHeight: StockToolsBindings_updateHeight, updateNthPoint: StockToolsBindings_updateNthPoint, updateRectSize: StockToolsBindings_updateRectSize } = StockTools_StockToolsUtilities;\n\nconst { fireEvent: StockToolsBindings_fireEvent, merge: StockToolsBindings_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @sample {highstock} stock/stocktools/custom-stock-tools-bindings\n *         Custom stock tools bindings\n *\n * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>}\n * @since        7.0.0\n * @optionparent navigation.bindings\n */\nconst StockToolsBindings = {\n    // Line type annotations:\n    /**\n     * A segment annotation bindings. Includes `start` and one event in `steps`\n     * array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-segment\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    segment: {\n        /** @ignore-option */\n        className: 'highcharts-segment',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'segment',\n                type: 'crookedLine',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }, {\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.segment.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A segment with an arrow annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-arrow-segment\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    arrowSegment: {\n        /** @ignore-option */\n        className: 'highcharts-arrow-segment',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'arrowSegment',\n                type: 'crookedLine',\n                typeOptions: {\n                    line: {\n                        markerEnd: 'arrow'\n                    },\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }, {\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.arrowSegment.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A ray annotation bindings. Includes `start` and one event in `steps`\n     * array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-ray\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    ray: {\n        /** @ignore-option */\n        className: 'highcharts-ray',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'ray',\n                type: 'infinityLine',\n                typeOptions: {\n                    type: 'ray',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }, {\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.ray.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A ray with an arrow annotation bindings. Includes `start` and one event\n     * in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-arrow-ray\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    arrowRay: {\n        /** @ignore-option */\n        className: 'highcharts-arrow-ray',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'arrowRay',\n                type: 'infinityLine',\n                typeOptions: {\n                    type: 'ray',\n                    line: {\n                        markerEnd: 'arrow'\n                    },\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }, {\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.arrowRay.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A line annotation. Includes `start` and one event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-infinity-line\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    infinityLine: {\n        /** @ignore-option */\n        className: 'highcharts-infinity-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'infinityLine',\n                type: 'infinityLine',\n                typeOptions: {\n                    type: 'line',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }, {\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.infinityLine.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A line with arrow annotation. Includes `start` and one event in `steps`\n     * array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-arrow-infinity-line\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    arrowInfinityLine: {\n        /** @ignore-option */\n        className: 'highcharts-arrow-infinity-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'arrowInfinityLine',\n                type: 'infinityLine',\n                typeOptions: {\n                    type: 'line',\n                    line: {\n                        markerEnd: 'arrow'\n                    },\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }, {\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.arrowInfinityLine\n                .annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A horizontal line annotation. Includes `start` event.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-horizontal-line\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    horizontalLine: {\n        /** @ignore-option */\n        className: 'highcharts-horizontal-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'horizontalLine',\n                type: 'infinityLine',\n                draggable: 'y',\n                typeOptions: {\n                    type: 'horizontalLine',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings\n                .horizontalLine.annotationsOptions);\n            this.chart.addAnnotation(options);\n        }\n    },\n    /**\n     * A vertical line annotation. Includes `start` event.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-vertical-line\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    verticalLine: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'verticalLine',\n                type: 'infinityLine',\n                draggable: 'x',\n                typeOptions: {\n                    type: 'verticalLine',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.verticalLine.annotationsOptions);\n            this.chart.addAnnotation(options);\n        }\n    },\n    /**\n     * Crooked line (three points) annotation bindings. Includes `start` and two\n     * events in `steps` (for second and third points in crooked line) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-crooked3\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    // Crooked Line type annotations:\n    crooked3: {\n        /** @ignore-option */\n        className: 'highcharts-crooked3',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'crooked3',\n                type: 'crookedLine',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [\n                        { x, y },\n                        { x, y },\n                        { x, y }\n                    ]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.crooked3.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2)\n        ]\n    },\n    /**\n     * Crooked line (five points) annotation bindings. Includes `start` and four\n     * events in `steps` (for all consequent points in crooked line) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-crooked5\", \"start\": function() {}, \"steps\": [function() {}, function() {}, function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    crooked5: {\n        /** @ignore-option */\n        className: 'highcharts-crooked5',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'crooked5',\n                type: 'crookedLine',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [\n                        { x, y },\n                        { x, y },\n                        { x, y },\n                        { x, y },\n                        { x, y }\n                    ]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.crooked5.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2),\n            StockToolsBindings_updateNthPoint(3),\n            StockToolsBindings_updateNthPoint(4)\n        ]\n    },\n    /**\n     * Elliott wave (three points) annotation bindings. Includes `start` and two\n     * events in `steps` (for second and third points) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-elliott3\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    elliott3: {\n        /** @ignore-option */\n        className: 'highcharts-elliott3',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'elliott3',\n                type: 'elliottWave',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [\n                        { x, y },\n                        { x, y },\n                        { x, y },\n                        { x, y }\n                    ]\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.elliott3.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2),\n            StockToolsBindings_updateNthPoint(3)\n        ]\n    },\n    /**\n     * Elliott wave (five points) annotation bindings. Includes `start` and four\n     * event in `steps` (for all consequent points in Elliott wave) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-elliott3\", \"start\": function() {}, \"steps\": [function() {}, function() {}, function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    elliott5: {\n        /** @ignore-option */\n        className: 'highcharts-elliott5',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'elliott5',\n                type: 'elliottWave',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [\n                        { x, y },\n                        { x, y },\n                        { x, y },\n                        { x, y },\n                        { x, y },\n                        { x, y }\n                    ]\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.elliott5.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2),\n            StockToolsBindings_updateNthPoint(3),\n            StockToolsBindings_updateNthPoint(4),\n            StockToolsBindings_updateNthPoint(5)\n        ]\n    },\n    /**\n     * A measure (x-dimension) annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-measure-x\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    measureX: {\n        /** @ignore-option */\n        className: 'highcharts-measure-x',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'measure',\n                type: 'measure',\n                typeOptions: {\n                    selectType: 'x',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    point: { x, y },\n                    crosshairX: {\n                        strokeWidth: 1,\n                        stroke: \"#000000\" /* Palette.neutralColor100 */\n                    },\n                    crosshairY: {\n                        enabled: false,\n                        strokeWidth: 0,\n                        stroke: \"#000000\" /* Palette.neutralColor100 */\n                    },\n                    background: {\n                        width: 0,\n                        height: 0,\n                        strokeWidth: 0,\n                        stroke: \"#ffffff\" /* Palette.backgroundColor */\n                    }\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.measureX.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateRectSize\n        ]\n    },\n    /**\n     * A measure (y-dimension) annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-measure-y\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    measureY: {\n        /** @ignore-option */\n        className: 'highcharts-measure-y',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'measure',\n                type: 'measure',\n                typeOptions: {\n                    selectType: 'y',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    point: { x, y },\n                    crosshairX: {\n                        enabled: false,\n                        strokeWidth: 0,\n                        stroke: \"#000000\" /* Palette.neutralColor100 */\n                    },\n                    crosshairY: {\n                        strokeWidth: 1,\n                        stroke: \"#000000\" /* Palette.neutralColor100 */\n                    },\n                    background: {\n                        width: 0,\n                        height: 0,\n                        strokeWidth: 0,\n                        stroke: \"#ffffff\" /* Palette.backgroundColor */\n                    }\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.measureY.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateRectSize\n        ]\n    },\n    /**\n     * A measure (xy-dimension) annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-measure-xy\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    measureXY: {\n        /** @ignore-option */\n        className: 'highcharts-measure-xy',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'measure',\n                type: 'measure',\n                typeOptions: {\n                    selectType: 'xy',\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    point: { x, y },\n                    background: {\n                        width: 0,\n                        height: 0,\n                        strokeWidth: 0,\n                        stroke: \"#ffffff\" /* Palette.backgroundColor */\n                    },\n                    crosshairX: {\n                        strokeWidth: 1,\n                        stroke: \"#000000\" /* Palette.neutralColor100 */\n                    },\n                    crosshairY: {\n                        strokeWidth: 1,\n                        stroke: \"#000000\" /* Palette.neutralColor100 */\n                    }\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.measureXY.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateRectSize\n        ]\n    },\n    // Advanced type annotations:\n    /**\n     * A fibonacci annotation bindings. Includes `start` and two events in\n     * `steps` array (updates second point, then height).\n     *\n     *   @sample {highstock} stock/stocktools/custom-stock-tools-bindings\n     *     Custom stock tools bindings\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-fibonacci\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": { \"typeOptions\": { \"reversed\": false }}}\n     */\n    fibonacci: {\n        className: 'highcharts-fibonacci',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'fibonacci',\n                type: 'fibonacci',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [\n                        { x, y },\n                        { x, y }\n                    ]\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.fibonacci.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateHeight\n        ]\n    },\n    /**\n     * A parallel channel (tunnel) annotation bindings. Includes `start` and\n     * two events in `steps` array (updates second point, then height).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-parallel-channel\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    parallelChannel: {\n        /** @ignore-option */\n        className: 'highcharts-parallel-channel',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'parallelChannel',\n                type: 'tunnel',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [\n                        { x, y },\n                        { x, y }\n                    ]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.parallelChannel\n                .annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateHeight\n        ]\n    },\n    /**\n     * An Andrew's pitchfork annotation bindings. Includes `start` and two\n     * events in `steps` array (sets second and third control points).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-pitchfork\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    pitchfork: {\n        /** @ignore-option */\n        className: 'highcharts-pitchfork',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'pitchfork',\n                type: 'pitchfork',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value,\n                            y: coordsY.value,\n                            controlPoint: {\n                                style: {\n                                    fill: \"#f21313\" /* Palette.negativeColor */\n                                }\n                            }\n                        },\n                        { x, y },\n                        { x, y }\n                    ],\n                    innerBackground: {\n                        fill: 'rgba(100, 170, 255, 0.8)'\n                    }\n                },\n                shapeOptions: {\n                    strokeWidth: 2\n                }\n            }, navigation.annotationsOptions, navigation.bindings.pitchfork.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2)\n        ]\n    },\n    // Labels with arrow and auto increments\n    /**\n     * A vertical counter annotation bindings. Includes `start` event. On click,\n     * finds the closest point and marks it with a numeric annotation -\n     * incrementing counter on each add.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-vertical-counter\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    verticalCounter: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-counter',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const closestPoint = StockToolsBindings_attractToPoint(e, this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            this.verticalCounter = this.verticalCounter || 0;\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'verticalCounter',\n                type: 'verticalLine',\n                typeOptions: {\n                    point: {\n                        x: closestPoint.x,\n                        y: closestPoint.y,\n                        xAxis: closestPoint.xAxis,\n                        yAxis: closestPoint.yAxis\n                    },\n                    label: {\n                        offset: closestPoint.below ? 40 : -40,\n                        text: this.verticalCounter.toString()\n                    }\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */,\n                        fontSize: '0.7em'\n                    }\n                },\n                shapeOptions: {\n                    stroke: 'rgba(0, 0, 0, 0.75)',\n                    strokeWidth: 1\n                }\n            }, navigation.annotationsOptions, navigation.bindings\n                .verticalCounter.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            this.verticalCounter++;\n            annotation.options.events.click.call(annotation, {});\n        }\n    },\n    /**\n     * A time cycles annotation bindings. Includes `start` event and 1 `step`\n     * event. first click marks the beginning of the circle, and the second one\n     * sets its diameter.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-time-cycles\", \"start\": function() {}, \"steps\": [function (){}] \"annotationsOptions\": {}}\n     */\n    timeCycles: {\n        className: 'highcharts-time-cycles',\n        start: function (e) {\n            const closestPoint = StockToolsBindings_attractToPoint(e, this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'timeCycles',\n                type: 'timeCycles',\n                typeOptions: {\n                    xAxis: closestPoint.xAxis,\n                    yAxis: closestPoint.yAxis,\n                    points: [{\n                            x: closestPoint.x\n                        }, {\n                            x: closestPoint.x\n                        }],\n                    line: {\n                        stroke: 'rgba(0, 0, 0, 0.75)',\n                        fill: 'transparent',\n                        strokeWidth: 2\n                    }\n                }\n            }, navigation.annotationsOptions, navigation.bindings.timeCycles.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            annotation.options.events.click.call(annotation, {});\n            return annotation;\n        },\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    verticalLabel: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-label',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const closestPoint = StockToolsBindings_attractToPoint(e, this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'verticalLabel',\n                type: 'verticalLine',\n                typeOptions: {\n                    point: {\n                        x: closestPoint.x,\n                        y: closestPoint.y,\n                        xAxis: closestPoint.xAxis,\n                        yAxis: closestPoint.yAxis\n                    },\n                    label: {\n                        offset: closestPoint.below ? 40 : -40\n                    }\n                },\n                labelOptions: {\n                    style: {\n                        color: \"#666666\" /* Palette.neutralColor60 */,\n                        fontSize: '0.7em'\n                    }\n                },\n                shapeOptions: {\n                    stroke: 'rgba(0, 0, 0, 0.75)',\n                    strokeWidth: 1\n                }\n            }, navigation.annotationsOptions, navigation.bindings\n                .verticalLabel.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            annotation.options.events.click.call(annotation, {});\n        }\n    },\n    /**\n     * A vertical arrow annotation bindings. Includes `start` event. On click,\n     * finds the closest point and marks it with an arrow.\n     * `${palette.positiveColor}` is the color of the arrow when\n     * pointing from above and `${palette.negativeColor}`\n     * when pointing from below the point.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-vertical-arrow\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    verticalArrow: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-arrow',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const closestPoint = StockToolsBindings_attractToPoint(e, this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                langKey: 'verticalArrow',\n                type: 'verticalLine',\n                typeOptions: {\n                    point: {\n                        x: closestPoint.x,\n                        y: closestPoint.y,\n                        xAxis: closestPoint.xAxis,\n                        yAxis: closestPoint.yAxis\n                    },\n                    label: {\n                        offset: closestPoint.below ? 40 : -40,\n                        format: ' '\n                    },\n                    connector: {\n                        fill: 'none',\n                        stroke: closestPoint.below ?\n                            \"#f21313\" /* Palette.negativeColor */ :\n                            \"#06b535\" /* Palette.positiveColor */\n                    }\n                },\n                shapeOptions: {\n                    stroke: 'rgba(0, 0, 0, 0.75)',\n                    strokeWidth: 1\n                }\n            }, navigation.annotationsOptions, navigation.bindings\n                .verticalArrow.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            annotation.options.events.click.call(annotation, {});\n        }\n    },\n    /**\n     * The Fibonacci Time Zones annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-fibonacci-time-zones\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    fibonacciTimeZones: {\n        /** @ignore-option */\n        className: 'highcharts-fibonacci-time-zones',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            const [coordsX, coordsY] = this.getCoords(e);\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            const navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                type: 'fibonacciTimeZones',\n                langKey: 'fibonacciTimeZones',\n                typeOptions: {\n                    xAxis: coordsX.axis.index,\n                    yAxis: coordsY.axis.index,\n                    points: [{\n                            x: coordsX.value\n                        }]\n                }\n            }, navigation.annotationsOptions, navigation.bindings.fibonacciTimeZones\n                .annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        // eslint-disable-next-line valid-jsdoc\n        steps: [\n            function (e, annotation) {\n                const mockPointOpts = annotation.options.typeOptions.points, x = mockPointOpts && mockPointOpts[0].x, [coordsX, coordsY] = this.getCoords(e);\n                if (coordsX && coordsY) {\n                    annotation.update({\n                        typeOptions: {\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: x\n                                }, {\n                                    x: coordsX.value\n                                }]\n                        }\n                    });\n                }\n            }\n        ]\n    },\n    // Flag types:\n    /**\n     * A flag series bindings. Includes `start` event. On click, finds the\n     * closest point and marks it with a flag with `'circlepin'` shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-circlepin\", \"start\": function() {}}\n     */\n    flagCirclepin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-circlepin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('circlepin')\n    },\n    /**\n     * A flag series bindings. Includes `start` event. On click, finds the\n     * closest point and marks it with a flag with `'diamondpin'` shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-diamondpin\", \"start\": function() {}}\n     */\n    flagDiamondpin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-diamondpin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('flag')\n    },\n    /**\n     * A flag series bindings. Includes `start` event.\n     * On click, finds the closest point and marks it with a flag with\n     * `'squarepin'` shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-squarepin\", \"start\": function() {}}\n     */\n    flagSquarepin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-squarepin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('squarepin')\n    },\n    /**\n     * A flag series bindings. Includes `start` event.\n     * On click, finds the closest point and marks it with a flag without pin\n     * shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-simplepin\", \"start\": function() {}}\n     */\n    flagSimplepin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-simplepin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('nopin')\n    },\n    // Other tools:\n    /**\n     * Enables zooming in xAxis on a chart. Includes `start` event which\n     * changes [chart.zoomType](#chart.zoomType).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-zoom-x\", \"init\": function() {}}\n     */\n    zoomX: {\n        /** @ignore-option */\n        className: 'highcharts-zoom-x',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.update({\n                chart: {\n                    zooming: {\n                        type: 'x'\n                    }\n                }\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Enables zooming in yAxis on a chart. Includes `start` event which\n     * changes [chart.zoomType](#chart.zoomType).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-zoom-y\", \"init\": function() {}}\n     */\n    zoomY: {\n        /** @ignore-option */\n        className: 'highcharts-zoom-y',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.update({\n                chart: {\n                    zooming: {\n                        type: 'y'\n                    }\n                }\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Enables zooming in xAxis and yAxis on a chart. Includes `start` event\n     * which changes [chart.zoomType](#chart.zoomType).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-zoom-xy\", \"init\": function() {}}\n     */\n    zoomXY: {\n        /** @ignore-option */\n        className: 'highcharts-zoom-xy',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.update({\n                chart: {\n                    zooming: {\n                        type: 'xy'\n                    }\n                }\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'line'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-line\", \"init\": function() {}}\n     */\n    seriesTypeLine: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'line',\n                useOhlcData: true\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'ohlc'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-ohlc\", \"init\": function() {}}\n     */\n    seriesTypeOhlc: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-ohlc',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'ohlc'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'candlestick'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-candlestick\", \"init\": function() {}}\n     */\n    seriesTypeCandlestick: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-candlestick',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'candlestick'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'heikinashi'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-heikinashi\", \"init\": function() {}}\n     */\n    seriesTypeHeikinAshi: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-heikinashi',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'heikinashi'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Change main series to `'hlc'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-hlc\", \"init\": function () {}}\n     */\n    seriesTypeHLC: {\n        className: 'highcharts-series-type-hlc',\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'hlc',\n                useOhlcData: true\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button });\n        }\n    },\n    /**\n     * Changes main series to `'hollowcandlestick'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-hollowcandlestick\", \"init\": function() {}}\n     */\n    seriesTypeHollowCandlestick: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-hollowcandlestick',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'hollowcandlestick'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Displays chart in fullscreen.\n     *\n     * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"noDataState\": \"normal\", \"highcharts-full-screen\", \"init\": function() {}}\n     */\n    fullScreen: {\n        /** @ignore-option */\n        className: 'highcharts-full-screen',\n        noDataState: 'normal',\n        /** @ignore-option */\n        init: function (button) {\n            if (this.chart.fullscreen) {\n                this.chart.fullscreen.toggle();\n            }\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Hides/shows two price indicators:\n     * - last price in the dataset\n     * - last price in the selected range\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-current-price-indicator\", \"init\": function() {}}\n     */\n    currentPriceIndicator: {\n        /** @ignore-option */\n        className: 'highcharts-current-price-indicator',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            const chart = this.chart, series = chart.series, gui = chart.stockTools, priceIndicatorEnabled = StockToolsBindings_isPriceIndicatorEnabled(chart.series);\n            if (gui && gui.guiEnabled) {\n                series.forEach(function (series) {\n                    series.update({\n                        lastPrice: { enabled: !priceIndicatorEnabled },\n                        lastVisiblePrice: {\n                            enabled: !priceIndicatorEnabled,\n                            label: { enabled: true }\n                        }\n                    }, false);\n                });\n                chart.redraw();\n            }\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Indicators bindings. Includes `init` event to show a popup.\n     *\n     * Note: In order to show base series from the chart in the popup's\n     * dropdown each series requires\n     * [series.id](https://api.highcharts.com/highstock/series.line.id) to be\n     * defined.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-indicators\", \"init\": function() {}}\n     */\n    indicators: {\n        /** @ignore-option */\n        className: 'highcharts-indicators',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function () {\n            const navigation = this;\n            StockToolsBindings_fireEvent(navigation, 'showPopup', {\n                formType: 'indicators',\n                options: {},\n                // Callback on submit:\n                onSubmit: function (data) {\n                    StockToolsBindings_manageIndicators.call(navigation, data);\n                }\n            });\n        }\n    },\n    /**\n     * Hides/shows all annotations on a chart.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-toggle-annotations\", \"init\": function() {}}\n     */\n    toggleAnnotations: {\n        /** @ignore-option */\n        className: 'highcharts-toggle-annotations',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            const chart = this.chart, gui = chart.stockTools, iconsURL = gui.getIconsURL();\n            this.toggledAnnotations = !this.toggledAnnotations;\n            (chart.annotations || []).forEach(function (annotation) {\n                annotation.setVisibility(!this.toggledAnnotations);\n            }, this);\n            if (gui && gui.guiEnabled) {\n                if (this.toggledAnnotations) {\n                    button.firstChild.style['background-image'] =\n                        'url(\"' + iconsURL +\n                            'annotations-hidden.svg\")';\n                }\n                else {\n                    button.firstChild.style['background-image'] =\n                        'url(\"' + iconsURL +\n                            'annotations-visible.svg\")';\n                }\n            }\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Save a chart in localStorage under `highcharts-chart` key.\n     * Stored items:\n     * - annotations\n     * - indicators (with yAxes)\n     * - flags\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-save-chart\", \"noDataState\": \"normal\", \"init\": function() {}}\n     */\n    saveChart: {\n        /** @ignore-option */\n        className: 'highcharts-save-chart',\n        noDataState: 'normal',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            const navigation = this, chart = navigation.chart, annotations = [], indicators = [], flags = [], yAxes = [];\n            chart.annotations.forEach(function (annotation, index) {\n                annotations[index] = annotation.userOptions;\n            });\n            chart.series.forEach(function (series) {\n                if (series.is('sma')) {\n                    indicators.push(series.userOptions);\n                }\n                else if (series.type === 'flags') {\n                    flags.push(series.userOptions);\n                }\n            });\n            chart.yAxis.forEach(function (yAxis) {\n                if (StockToolsBindings_isNotNavigatorYAxis(yAxis)) {\n                    yAxes.push(yAxis.options);\n                }\n            });\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().win.localStorage.setItem('highcharts-chart', JSON.stringify({\n                annotations: annotations,\n                indicators: indicators,\n                flags: flags,\n                yAxes: yAxes\n            }));\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const StockTools_StockToolsBindings = (StockToolsBindings);\n\n;// ./code/es-modules/Stock/StockTools/StockToolsDefaults.js\n/* *\n *\n *  GUI generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst StockToolsDefaults_lang = {\n    /**\n     * Configure the stockTools GUI titles(hints) in the chart. Requires\n     * the `stock-tools.js` module to be loaded.\n     *\n     * @product highstock\n     * @since   7.0.0\n     */\n    stockTools: {\n        gui: {\n            // Main buttons:\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            crookedLines: 'Crooked lines',\n            measure: 'Measure',\n            advanced: 'Advanced',\n            toggleAnnotations: 'Toggle annotations',\n            verticalLabels: 'Vertical labels',\n            flags: 'Flags',\n            zoomChange: 'Zoom change',\n            typeChange: 'Type change',\n            saveChart: 'Save chart',\n            indicators: 'Indicators',\n            currentPriceIndicator: 'Current Price Indicators',\n            // Other features:\n            zoomX: 'Zoom X',\n            zoomY: 'Zoom Y',\n            zoomXY: 'Zooom XY',\n            fullScreen: 'Fullscreen',\n            typeOHLC: 'OHLC',\n            typeLine: 'Line',\n            typeCandlestick: 'Candlestick',\n            typeHLC: 'HLC',\n            typeHollowCandlestick: 'Hollow Candlestick',\n            typeHeikinAshi: 'Heikin Ashi',\n            // Basic shapes:\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            label: 'Label',\n            rectangle: 'Rectangle',\n            // Flags:\n            flagCirclepin: 'Flag circle',\n            flagDiamondpin: 'Flag diamond',\n            flagSquarepin: 'Flag square',\n            flagSimplepin: 'Flag simple',\n            // Measures:\n            measureXY: 'Measure XY',\n            measureX: 'Measure X',\n            measureY: 'Measure Y',\n            // Segment, ray and line:\n            segment: 'Segment',\n            arrowSegment: 'Arrow segment',\n            ray: 'Ray',\n            arrowRay: 'Arrow ray',\n            line: 'Line',\n            arrowInfinityLine: 'Arrow line',\n            horizontalLine: 'Horizontal line',\n            verticalLine: 'Vertical line',\n            infinityLine: 'Infinity line',\n            // Crooked lines:\n            crooked3: 'Crooked 3 line',\n            crooked5: 'Crooked 5 line',\n            elliott3: 'Elliott 3 line',\n            elliott5: 'Elliott 5 line',\n            // Counters:\n            verticalCounter: 'Vertical counter',\n            verticalLabel: 'Vertical label',\n            verticalArrow: 'Vertical arrow',\n            // Advanced:\n            fibonacci: 'Fibonacci',\n            fibonacciTimeZones: 'Fibonacci Time Zones',\n            pitchfork: 'Pitchfork',\n            parallelChannel: 'Parallel channel',\n            timeCycles: 'Time Cycles'\n        }\n    },\n    navigation: {\n        popup: {\n            // Annotations:\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            segment: 'Segment',\n            arrowSegment: 'Arrow segment',\n            ray: 'Ray',\n            arrowRay: 'Arrow ray',\n            line: 'Line',\n            arrowInfinityLine: 'Arrow line',\n            horizontalLine: 'Horizontal line',\n            verticalLine: 'Vertical line',\n            crooked3: 'Crooked 3 line',\n            crooked5: 'Crooked 5 line',\n            elliott3: 'Elliott 3 line',\n            elliott5: 'Elliott 5 line',\n            verticalCounter: 'Vertical counter',\n            verticalLabel: 'Vertical label',\n            verticalArrow: 'Vertical arrow',\n            fibonacci: 'Fibonacci',\n            fibonacciTimeZones: 'Fibonacci Time Zones',\n            pitchfork: 'Pitchfork',\n            parallelChannel: 'Parallel channel',\n            infinityLine: 'Infinity line',\n            measure: 'Measure',\n            measureXY: 'Measure XY',\n            measureX: 'Measure X',\n            measureY: 'Measure Y',\n            timeCycles: 'Time Cycles',\n            // Flags:\n            flags: 'Flags',\n            // GUI elements:\n            addButton: 'Add',\n            saveButton: 'Save',\n            editButton: 'Edit',\n            removeButton: 'Remove',\n            series: 'Series',\n            volume: 'Volume',\n            connector: 'Connector',\n            // Field names:\n            innerBackground: 'Inner background',\n            outerBackground: 'Outer background',\n            crosshairX: 'Crosshair X',\n            crosshairY: 'Crosshair Y',\n            tunnel: 'Tunnel',\n            background: 'Background',\n            // Indicators' searchbox (#16019):\n            noFilterMatch: 'No match',\n            // Indicators' params (#15170):\n            searchIndicators: 'Search Indicators',\n            clearFilter: '\\u2715 clear filter',\n            index: 'Index',\n            period: 'Period',\n            periods: 'Periods',\n            standardDeviation: 'Standard deviation',\n            periodTenkan: 'Tenkan period',\n            periodSenkouSpanB: 'Senkou Span B period',\n            periodATR: 'ATR period',\n            multiplierATR: 'ATR multiplier',\n            shortPeriod: 'Short period',\n            longPeriod: 'Long period',\n            signalPeriod: 'Signal period',\n            decimals: 'Decimals',\n            algorithm: 'Algorithm',\n            topBand: 'Top band',\n            bottomBand: 'Bottom band',\n            initialAccelerationFactor: 'Initial acceleration factor',\n            maxAccelerationFactor: 'Max acceleration factor',\n            increment: 'Increment',\n            multiplier: 'Multiplier',\n            ranges: 'Ranges',\n            highIndex: 'High index',\n            lowIndex: 'Low index',\n            deviation: 'Deviation',\n            xAxisUnit: 'x-axis unit',\n            factor: 'Factor',\n            fastAvgPeriod: 'Fast average period',\n            slowAvgPeriod: 'Slow average period',\n            average: 'Average',\n            /**\n             * Configure the aliases for indicator names.\n             *\n             * @product highstock\n             * @since 9.3.0\n             */\n            indicatorAliases: {\n                // Overlays\n                /**\n                 * Acceleration Bands alias.\n                 *\n                 * @default ['Acceleration Bands']\n                 * @type    {Array<string>}\n                 */\n                abands: ['Acceleration Bands'],\n                /**\n                 * Bollinger Bands alias.\n                 *\n                 * @default ['Bollinger Bands']\n                 * @type    {Array<string>}\n                 */\n                bb: ['Bollinger Bands'],\n                /**\n                 * Double Exponential Moving Average alias.\n                 *\n                 * @default ['Double Exponential Moving Average']\n                 * @type    {Array<string>}\n                 */\n                dema: ['Double Exponential Moving Average'],\n                /**\n                 *  Exponential Moving Average alias.\n                 *\n                 * @default ['Exponential Moving Average']\n                 * @type    {Array<string>}\n                 */\n                ema: ['Exponential Moving Average'],\n                /**\n                 *  Ichimoku Kinko Hyo alias.\n                 *\n                 * @default ['Ichimoku Kinko Hyo']\n                 * @type    {Array<string>}\n                 */\n                ikh: ['Ichimoku Kinko Hyo'],\n                /**\n                 *  Keltner Channels alias.\n                 *\n                 * @default ['Keltner Channels']\n                 * @type    {Array<string>}\n                 */\n                keltnerchannels: ['Keltner Channels'],\n                /**\n                 *  Linear Regression alias.\n                 *\n                 * @default ['Linear Regression']\n                 * @type    {Array<string>}\n                 */\n                linearRegression: ['Linear Regression'],\n                /**\n                 *  Pivot Points alias.\n                 *\n                 * @default ['Pivot Points']\n                 * @type    {Array<string>}\n                 */\n                pivotpoints: ['Pivot Points'],\n                /**\n                 *  Price Channel alias.\n                 *\n                 * @default ['Price Channel']\n                 * @type    {Array<string>}\n                 */\n                pc: ['Price Channel'],\n                /**\n                 *  Price Envelopes alias.\n                 *\n                 * @default ['Price Envelopes']\n                 * @type    {Array<string>}\n                 */\n                priceenvelopes: ['Price Envelopes'],\n                /**\n                 *  Parabolic SAR alias.\n                 *\n                 * @default ['Parabolic SAR']\n                 * @type    {Array<string>}\n                 */\n                psar: ['Parabolic SAR'],\n                /**\n                 *  Simple Moving Average alias.\n                 *\n                 * @default ['Simple Moving Average']\n                 * @type    {Array<string>}\n                 */\n                sma: ['Simple Moving Average'],\n                /**\n                 *  Super Trend alias.\n                 *\n                 * @default ['Super Trend']\n                 * @type    {Array<string>}\n                 */\n                supertrend: ['Super Trend'],\n                /**\n                 *  Triple Exponential Moving Average alias.\n                 *\n                 * @default ['Triple Exponential Moving Average']\n                 * @type    {Array<string>}\n                 */\n                tema: ['Triple Exponential Moving Average'],\n                /**\n                 *  Volume by Price alias.\n                 *\n                 * @default ['Volume by Price']\n                 * @type    {Array<string>}\n                 */\n                vbp: ['Volume by Price'],\n                /**\n                 *  Volume Weighted Moving Average alias.\n                 *\n                 * @default ['Volume Weighted Moving Average']\n                 * @type    {Array<string>}\n                 */\n                vwap: ['Volume Weighted Moving Average'],\n                /**\n                 *  Weighted Moving Average alias.\n                 *\n                 * @default ['Weighted Moving Average']\n                 * @type    {Array<string>}\n                 */\n                wma: ['Weighted Moving Average'],\n                /**\n                 *  Zig Zagalias.\n                 *\n                 * @default ['Zig Zag']\n                 * @type    {Array<string>}\n                 */\n                zigzag: ['Zig Zag'],\n                // Oscilators\n                /**\n                 *  Absolute price indicator alias.\n                 *\n                 * @default ['Absolute price indicator']\n                 * @type    {Array<string>}\n                 */\n                apo: ['Absolute price indicator'],\n                /**\n                 * Accumulation/Distribution alias.\n                 *\n                 * @default ['Accumulation/Distribution’]\n                 * @type    {Array<string>}\n                 */\n                ad: ['Accumulation/Distribution'],\n                /**\n                 *  Aroon alias.\n                 *\n                 * @default ['Aroon']\n                 * @type    {Array<string>}\n                 */\n                aroon: ['Aroon'],\n                /**\n                 *  Aroon oscillator alias.\n                 *\n                 * @default ['Aroon oscillator']\n                 * @type    {Array<string>}\n                 */\n                aroonoscillator: ['Aroon oscillator'],\n                /**\n                 *  Average True Range alias.\n                 *\n                 * @default ['Average True Range’]\n                 * @type    {Array<string>}\n                 */\n                atr: ['Average True Range'],\n                /**\n                 *  Awesome oscillator alias.\n                 *\n                 * @default ['Awesome oscillator’]\n                 * @type    {Array<string>}\n                 */\n                ao: ['Awesome oscillator'],\n                /**\n                 *  Commodity Channel Index alias.\n                 *\n                 * @default ['Commodity Channel Index’]\n                 * @type    {Array<string>}\n                 */\n                cci: ['Commodity Channel Index'],\n                /**\n                 *  Chaikin alias.\n                 *\n                 * @default ['Chaikin’]\n                 * @type    {Array<string>}\n                 */\n                chaikin: ['Chaikin'],\n                /**\n                 *  Chaikin Money Flow alias.\n                 *\n                 * @default ['Chaikin Money Flow’]\n                 * @type    {Array<string>}\n                 */\n                cmf: ['Chaikin Money Flow'],\n                /**\n                 *  Chande Momentum Oscillator alias.\n                 *\n                 * @default ['Chande Momentum Oscillator’]\n                 * @type    {Array<string>}\n                 */\n                cmo: ['Chande Momentum Oscillator'],\n                /**\n                 *  Disparity Index alias.\n                 *\n                 * @default ['Disparity Index’]\n                 * @type    {Array<string>}\n                 */\n                disparityindex: ['Disparity Index'],\n                /**\n                 *  Directional Movement Index alias.\n                 *\n                 * @default ['Directional Movement Index’]\n                 * @type    {Array<string>}\n                 */\n                dmi: ['Directional Movement Index'],\n                /**\n                 *  Detrended price oscillator alias.\n                 *\n                 * @default ['Detrended price oscillator’]\n                 * @type    {Array<string>}\n                 */\n                dpo: ['Detrended price oscillator'],\n                /**\n                 *  Klinger Oscillator alias.\n                 *\n                 * @default [‘Klinger Oscillator’]\n                 * @type    {Array<string>}\n                 */\n                klinger: ['Klinger Oscillator'],\n                /**\n                 *  Linear Regression Angle alias.\n                 *\n                 * @default [‘Linear Regression Angle’]\n                 * @type    {Array<string>}\n                 */\n                linearRegressionAngle: ['Linear Regression Angle'],\n                /**\n                 *  Linear Regression Intercept alias.\n                 *\n                 * @default [‘Linear Regression Intercept’]\n                 * @type    {Array<string>}\n                 */\n                linearRegressionIntercept: ['Linear Regression Intercept'],\n                /**\n                 *  Linear Regression Slope alias.\n                 *\n                 * @default [‘Linear Regression Slope’]\n                 * @type    {Array<string>}\n                 */\n                linearRegressionSlope: ['Linear Regression Slope'],\n                /**\n                 *  Moving Average Convergence Divergence alias.\n                 *\n                 * @default ['Moving Average Convergence Divergence’]\n                 * @type    {Array<string>}\n                 */\n                macd: ['Moving Average Convergence Divergence'],\n                /**\n                 *  Money Flow Index alias.\n                 *\n                 * @default ['Money Flow Index’]\n                 * @type    {Array<string>}\n                 */\n                mfi: ['Money Flow Index'],\n                /**\n                 *  Momentum alias.\n                 *\n                 * @default [‘Momentum’]\n                 * @type    {Array<string>}\n                 */\n                momentum: ['Momentum'],\n                /**\n                 *  Normalized Average True Range alias.\n                 *\n                 * @default ['Normalized Average True Range’]\n                 * @type    {Array<string>}\n                 */\n                natr: ['Normalized Average True Range'],\n                /**\n                 *  On-Balance Volume alias.\n                 *\n                 * @default ['On-Balance Volume’]\n                 * @type    {Array<string>}\n                 */\n                obv: ['On-Balance Volume'],\n                /**\n                 * Percentage Price oscillator alias.\n                 *\n                 * @default ['Percentage Price oscillator’]\n                 * @type    {Array<string>}\n                 */\n                ppo: ['Percentage Price oscillator'],\n                /**\n                 *  Rate of Change alias.\n                 *\n                 * @default ['Rate of Change’]\n                 * @type    {Array<string>}\n                 */\n                roc: ['Rate of Change'],\n                /**\n                 *  Relative Strength Index alias.\n                 *\n                 * @default ['Relative Strength Index’]\n                 * @type    {Array<string>}\n                 */\n                rsi: ['Relative Strength Index'],\n                /**\n                 *  Slow Stochastic alias.\n                 *\n                 * @default [‘Slow Stochastic’]\n                 * @type    {Array<string>}\n                 */\n                slowstochastic: ['Slow Stochastic'],\n                /**\n                 *  Stochastic alias.\n                 *\n                 * @default [‘Stochastic’]\n                 * @type    {Array<string>}\n                 */\n                stochastic: ['Stochastic'],\n                /**\n                 *  TRIX alias.\n                 *\n                 * @default [‘TRIX’]\n                 * @type    {Array<string>}\n                 */\n                trix: ['TRIX'],\n                /**\n                 *  Williams %R alias.\n                 *\n                 * @default [‘Williams %R’]\n                 * @type    {Array<string>}\n                 */\n                williamsr: ['Williams %R']\n            }\n        }\n    }\n};\n/**\n * Configure the stockTools gui strings in the chart. Requires the\n * [stockTools module]() to be loaded. For a description of the module\n * and information on its features, see [Highcharts StockTools]().\n *\n * @product highstock\n *\n * @sample stock/demo/stock-tools-gui Stock Tools GUI\n *\n * @sample stock/demo/stock-tools-custom-gui Stock Tools customized GUI\n *\n * @since        7.0.0\n * @optionparent stockTools\n */\nconst stockTools = {\n    /**\n     * Definitions of buttons in Stock Tools GUI.\n     */\n    gui: {\n        /**\n         * Path where Highcharts will look for icons. Change this to use\n         * icons from a different server.\n         *\n         * Since 7.1.3 use [iconsURL](#navigation.iconsURL) for popup and\n         * stock tools.\n         *\n         * @deprecated\n         * @apioption stockTools.gui.iconsURL\n         *\n         */\n        /**\n         * Enable or disable the stockTools gui.\n         */\n        enabled: true,\n        /**\n         * A CSS class name to apply to the stocktools' div,\n         * allowing unique CSS styling for each chart.\n         */\n        className: 'highcharts-bindings-wrapper',\n        /**\n         * A CSS class name to apply to the container of buttons,\n         * allowing unique CSS styling for each chart.\n         */\n        toolbarClassName: 'stocktools-toolbar',\n        /**\n         * A collection of strings pointing to config options for the\n         * toolbar items. Each name refers to a unique key from the\n         * definitions object.\n         *\n         * @type    {Array<string>}\n         * @default [\n         *   'indicators',\n         *   'separator',\n         *   'simpleShapes',\n         *   'lines',\n         *   'crookedLines',\n         *   'measure',\n         *   'advanced',\n         *   'toggleAnnotations',\n         *   'separator',\n         *   'verticalLabels',\n         *   'flags',\n         *   'separator',\n         *   'zoomChange',\n         *   'fullScreen',\n         *   'typeChange',\n         *   'separator',\n         *   'currentPriceIndicator',\n         *   'saveChart'\n         * ]\n         */\n        buttons: [\n            'indicators',\n            'separator',\n            'simpleShapes',\n            'lines',\n            'crookedLines',\n            'measure',\n            'advanced',\n            'toggleAnnotations',\n            'separator',\n            'verticalLabels',\n            'flags',\n            'separator',\n            'zoomChange',\n            'fullScreen',\n            'typeChange',\n            'separator',\n            'currentPriceIndicator',\n            'saveChart'\n        ],\n        /**\n         * An options object of the buttons definitions. Each name refers to\n         * unique key from buttons array.\n         */\n        definitions: {\n            separator: {\n                elementType: 'span',\n                /**\n                 * A predefined background symbol for the button.\n                 */\n                symbol: 'separator.svg'\n            },\n            simpleShapes: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'label',\n                 *   'circle',\n                 *   'ellipse',\n                 *   'rectangle'\n                 * ]\n                 *\n                 */\n                items: [\n                    'label',\n                    'circle',\n                    'ellipse',\n                    'rectangle'\n                ],\n                circle: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'circle.svg'\n                },\n                ellipse: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'ellipse.svg'\n                },\n                rectangle: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'rectangle.svg'\n                },\n                label: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'label.svg'\n                }\n            },\n            flags: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'flagCirclepin',\n                 *   'flagDiamondpin',\n                 *   'flagSquarepin',\n                 *   'flagSimplepin'\n                 * ]\n                 *\n                 */\n                items: [\n                    'flagCirclepin',\n                    'flagDiamondpin',\n                    'flagSquarepin',\n                    'flagSimplepin'\n                ],\n                flagSimplepin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'flag-basic.svg'\n                },\n                flagDiamondpin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'flag-diamond.svg'\n                },\n                flagSquarepin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'flag-trapeze.svg'\n                },\n                flagCirclepin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'flag-elipse.svg'\n                }\n            },\n            lines: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'segment',\n                 *   'arrowSegment',\n                 *   'ray',\n                 *   'arrowRay',\n                 *   'line',\n                 *   'arrowInfinityLine',\n                 *   'horizontalLine',\n                 *   'verticalLine'\n                 * ]\n                 */\n                items: [\n                    'segment',\n                    'arrowSegment',\n                    'ray',\n                    'arrowRay',\n                    'line',\n                    'arrowInfinityLine',\n                    'horizontalLine',\n                    'verticalLine'\n                ],\n                segment: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'segment.svg'\n                },\n                arrowSegment: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'arrow-segment.svg'\n                },\n                ray: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'ray.svg'\n                },\n                arrowRay: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'arrow-ray.svg'\n                },\n                line: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'line.svg'\n                },\n                arrowInfinityLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'arrow-line.svg'\n                },\n                verticalLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-line.svg'\n                },\n                horizontalLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'horizontal-line.svg'\n                }\n            },\n            crookedLines: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'elliott3',\n                 *   'elliott5',\n                 *   'crooked3',\n                 *   'crooked5'\n                 * ]\n                 *\n                 */\n                items: [\n                    'elliott3',\n                    'elliott5',\n                    'crooked3',\n                    'crooked5'\n                ],\n                crooked3: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'crooked-3.svg'\n                },\n                crooked5: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'crooked-5.svg'\n                },\n                elliott3: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'elliott-3.svg'\n                },\n                elliott5: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'elliott-5.svg'\n                }\n            },\n            verticalLabels: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'verticalCounter',\n                 *   'verticalLabel',\n                 *   'verticalArrow'\n                 * ]\n                 */\n                items: [\n                    'verticalCounter',\n                    'verticalLabel',\n                    'verticalArrow'\n                ],\n                verticalCounter: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-counter.svg'\n                },\n                verticalLabel: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-label.svg'\n                },\n                verticalArrow: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-arrow.svg'\n                }\n            },\n            advanced: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'fibonacci',\n                 *   'fibonacciTimeZones',\n                 *   'pitchfork',\n                 *   'parallelChannel',\n                 *   'timeCycles'\n                 * ]\n                 */\n                items: [\n                    'fibonacci',\n                    'fibonacciTimeZones',\n                    'pitchfork',\n                    'parallelChannel',\n                    'timeCycles'\n                ],\n                pitchfork: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'pitchfork.svg'\n                },\n                fibonacci: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'fibonacci.svg'\n                },\n                fibonacciTimeZones: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'fibonacci-timezone.svg'\n                },\n                parallelChannel: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'parallel-channel.svg'\n                },\n                timeCycles: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type {string}\n                     */\n                    symbol: 'time-cycles.svg'\n                }\n            },\n            measure: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'measureXY',\n                 *   'measureX',\n                 *   'measureY'\n                 * ]\n                 */\n                items: [\n                    'measureXY',\n                    'measureX',\n                    'measureY'\n                ],\n                measureX: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'measure-x.svg'\n                },\n                measureY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'measure-y.svg'\n                },\n                measureXY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'measure-xy.svg'\n                }\n            },\n            toggleAnnotations: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'annotations-visible.svg'\n            },\n            currentPriceIndicator: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'current-price-show.svg'\n            },\n            indicators: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'indicators.svg'\n            },\n            zoomChange: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'zoomX',\n                 *   'zoomY',\n                 *   'zoomXY'\n                 * ]\n                 */\n                items: [\n                    'zoomX',\n                    'zoomY',\n                    'zoomXY'\n                ],\n                zoomX: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'zoom-x.svg'\n                },\n                zoomY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'zoom-y.svg'\n                },\n                zoomXY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'zoom-xy.svg'\n                }\n            },\n            typeChange: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'typeOHLC',\n                 *   'typeLine',\n                 *   'typeCandlestick'\n                 *   'typeHollowCandlestick'\n                 * ]\n                 */\n                items: [\n                    'typeOHLC',\n                    'typeLine',\n                    'typeCandlestick',\n                    'typeHollowCandlestick',\n                    'typeHLC',\n                    'typeHeikinAshi'\n                ],\n                typeOHLC: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-ohlc.svg'\n                },\n                typeLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-line.svg'\n                },\n                typeCandlestick: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-candlestick.svg'\n                },\n                typeHLC: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-hlc.svg'\n                },\n                typeHeikinAshi: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-heikin-ashi.svg'\n                },\n                typeHollowCandlestick: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-hollow-candlestick.svg'\n                }\n            },\n            fullScreen: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'fullscreen.svg'\n            },\n            saveChart: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'save-chart.svg'\n            }\n        },\n        /**\n         * Whether the stock tools toolbar is visible.\n         *\n         * @since 11.4.4\n         */\n        visible: true\n    }\n};\n/* *\n *\n *  Default Exports\n *\n * */\nconst StockToolsDefaults = {\n    lang: StockToolsDefaults_lang,\n    stockTools\n};\n/* harmony default export */ const StockTools_StockToolsDefaults = (StockToolsDefaults);\n\n;// ./code/es-modules/Stock/StockTools/StockTools.js\n/**\n *\n *  Events generator for Stock tools\n *\n *  (c) 2009-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions: StockTools_setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { getAssignedAxis: StockTools_getAssignedAxis } = NavigationBindingsUtilities;\n\n\n\nconst { isNotNavigatorYAxis: StockTools_isNotNavigatorYAxis, isPriceIndicatorEnabled: StockTools_isPriceIndicatorEnabled } = StockTools_StockToolsUtilities;\n\nconst { correctFloat, defined: StockTools_defined, isNumber: StockTools_isNumber, pick: StockTools_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(NavigationBindingsClass) {\n    const navigationProto = NavigationBindingsClass.prototype;\n    if (!navigationProto.utils?.manageIndicators) {\n        // Extends NavigationBindings to support indicators and resizers:\n        navigationProto.getYAxisPositions = navigationGetYAxisPositions;\n        navigationProto.getYAxisResizers = navigationGetYAxisResizers;\n        navigationProto.recalculateYAxisPositions =\n            navigationRecalculateYAxisPositions;\n        navigationProto.resizeYAxes = navigationResizeYAxes;\n        navigationProto.utils = navigationProto.utils || {};\n        navigationProto.utils.indicatorsWithAxes = StockTools_StockToolsUtilities.indicatorsWithAxes;\n        navigationProto.utils.indicatorsWithVolume = StockTools_StockToolsUtilities.indicatorsWithVolume;\n        navigationProto.utils.getAssignedAxis = StockTools_getAssignedAxis;\n        navigationProto.utils.isPriceIndicatorEnabled = StockTools_isPriceIndicatorEnabled;\n        navigationProto.utils.manageIndicators = StockTools_StockToolsUtilities.manageIndicators;\n        StockTools_setOptions(StockTools_StockToolsDefaults);\n        StockTools_setOptions({\n            navigation: {\n                bindings: StockTools_StockToolsBindings\n            }\n        });\n    }\n}\n/**\n * Get current positions for all yAxes. If new axis does not have position,\n * returned is default height and last available top place.\n *\n * @private\n * @function Highcharts.NavigationBindings#getYAxisPositions\n *\n * @param {Array<Highcharts.Axis>} yAxes\n *        Array of yAxes available in the chart.\n *\n * @param {number} plotHeight\n *        Available height in the chart.\n *\n * @param {number} defaultHeight\n *        Default height in percents.\n *\n * @param {Highcharts.AxisPositions} removedYAxisProps\n *        Height and top value of the removed yAxis in percents.\n *\n * @return {Highcharts.YAxisPositions}\n *         An object containing an array of calculated positions\n *         in percentages. Format: `{top: Number, height: Number}`\n *         and maximum value of top + height of axes.\n */\nfunction navigationGetYAxisPositions(yAxes, plotHeight, defaultHeight, removedYAxisProps) {\n    let allAxesHeight = 0, previousAxisHeight, removedHeight, removedTop;\n    /** @private */\n    function isPercentage(prop) {\n        return StockTools_defined(prop) && !StockTools_isNumber(prop) && prop.match('%');\n    }\n    if (removedYAxisProps) {\n        removedTop = correctFloat((parseFloat(removedYAxisProps.top) / 100));\n        removedHeight = correctFloat((parseFloat(removedYAxisProps.height) / 100));\n    }\n    const positions = yAxes.map((yAxis, index) => {\n        let height = correctFloat(isPercentage(yAxis.options.height) ?\n            parseFloat(yAxis.options.height) / 100 :\n            yAxis.height / plotHeight), top = correctFloat(isPercentage(yAxis.options.top) ?\n            parseFloat(yAxis.options.top) / 100 :\n            (yAxis.top - yAxis.chart.plotTop) / plotHeight);\n        if (!removedHeight) {\n            // New axis' height is NaN so we can check if\n            // the axis is newly created this way\n            if (!StockTools_isNumber(height)) {\n                // Check if the previous axis is the\n                // indicator axis (every indicator inherits from sma)\n                height = yAxes[index - 1].series\n                    .every((s) => s.is('sma')) ?\n                    previousAxisHeight : defaultHeight / 100;\n            }\n            if (!StockTools_isNumber(top)) {\n                top = allAxesHeight;\n            }\n            previousAxisHeight = height;\n            allAxesHeight = correctFloat(Math.max(allAxesHeight, (top || 0) + (height || 0)));\n        }\n        else {\n            // Move all axes which were below the removed axis up.\n            if (top > removedTop) {\n                top -= removedHeight;\n            }\n            allAxesHeight = Math.max(allAxesHeight, (top || 0) + (height || 0));\n        }\n        return {\n            height: height * 100,\n            top: top * 100\n        };\n    });\n    return { positions, allAxesHeight };\n}\n/**\n * Get current resize options for each yAxis. Note that each resize is\n * linked to the next axis, except the last one which shouldn't affect\n * axes in the navigator. Because indicator can be removed with it's yAxis\n * in the middle of yAxis array, we need to bind closest yAxes back.\n *\n * @private\n * @function Highcharts.NavigationBindings#getYAxisResizers\n *\n * @param {Array<Highcharts.Axis>} yAxes\n *        Array of yAxes available in the chart\n *\n * @return {Array<object>}\n *         An array of resizer options.\n *         Format: `{enabled: Boolean, controlledAxis: { next: [String]}}`\n */\nfunction navigationGetYAxisResizers(yAxes) {\n    const resizers = [];\n    yAxes.forEach(function (_yAxis, index) {\n        const nextYAxis = yAxes[index + 1];\n        // We have next axis, bind them:\n        if (nextYAxis) {\n            resizers[index] = {\n                enabled: true,\n                controlledAxis: {\n                    next: [\n                        StockTools_pick(nextYAxis.options.id, nextYAxis.index)\n                    ]\n                }\n            };\n        }\n        else {\n            // Remove binding:\n            resizers[index] = {\n                enabled: false\n            };\n        }\n    });\n    return resizers;\n}\n/**\n * Utility to modify calculated positions according to the remaining/needed\n * space. Later, these positions are used in `yAxis.update({ top, height })`\n *\n * @private\n * @function Highcharts.NavigationBindings#recalculateYAxisPositions\n * @param {Array<Highcharts.Dictionary<number>>} positions\n * Default positions of all yAxes.\n * @param {number} changedSpace\n * How much space should be added or removed.\n * @param {boolean} modifyHeight\n * Update only `top` or both `top` and `height`.\n * @param {number} adder\n * `-1` or `1`, to determine whether we should add or remove space.\n *\n * @return {Array<object>}\n *         Modified positions,\n */\nfunction navigationRecalculateYAxisPositions(positions, changedSpace, modifyHeight, adder) {\n    positions.forEach(function (position, index) {\n        const prevPosition = positions[index - 1];\n        position.top = !prevPosition ? 0 :\n            correctFloat(prevPosition.height + prevPosition.top);\n        if (modifyHeight) {\n            position.height = correctFloat(position.height + adder * changedSpace);\n        }\n    });\n    return positions;\n}\n/**\n * Resize all yAxes (except navigator) to fit the plotting height. Method\n * checks if new axis is added, if the new axis will fit under previous\n * axes it is placed there. If not, current plot area is scaled\n * to make room for new axis.\n *\n * If axis is removed, the current plot area stretches to fit into 100%\n * of the plot area.\n *\n * @private\n */\nfunction navigationResizeYAxes(removedYAxisProps) {\n    // The height of the new axis before rescalling. In %, but as a number.\n    const defaultHeight = 20;\n    const chart = this.chart, \n    // Only non-navigator axes\n    yAxes = chart.yAxis.filter(StockTools_isNotNavigatorYAxis), plotHeight = chart.plotHeight, \n    // Gather current heights (in %)\n    { positions, allAxesHeight } = this.getYAxisPositions(yAxes, plotHeight, defaultHeight, removedYAxisProps), resizers = this.getYAxisResizers(yAxes);\n    // Check if the axis is being either added or removed and\n    // if the new indicator axis will fit under existing axes.\n    // if so, there is no need to scale them.\n    if (!removedYAxisProps &&\n        allAxesHeight <= correctFloat(0.8 + defaultHeight / 100)) {\n        positions[positions.length - 1] = {\n            height: defaultHeight,\n            top: correctFloat(allAxesHeight * 100 - defaultHeight)\n        };\n    }\n    else {\n        positions.forEach(function (position) {\n            position.height = (position.height / (allAxesHeight * 100)) * 100;\n            position.top = (position.top / (allAxesHeight * 100)) * 100;\n        });\n    }\n    positions.forEach(function (position, index) {\n        yAxes[index].update({\n            height: position.height + '%',\n            top: position.top + '%',\n            resize: resizers[index],\n            offset: 0\n        }, false);\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst StockTools = {\n    compose\n};\n/* harmony default export */ const StockTools_StockTools = (StockTools);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n;// ./code/es-modules/Stock/StockTools/StockToolbar.js\n/* *\n *\n *  GUI generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { addEvent: StockToolbar_addEvent, createElement, css, defined: StockToolbar_defined, fireEvent: StockToolbar_fireEvent, getStyle, isArray: StockToolbar_isArray, merge: StockToolbar_merge, pick: StockToolbar_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { shallowArraysEqual: StockToolbar_shallowArraysEqual } = StockTools_StockToolsUtilities;\n/* *\n *\n *  Classes\n *\n * */\n/**\n * Toolbar Class\n *\n * @private\n * @class\n *\n * @param {object} options\n *        Options of toolbar\n *\n * @param {Highcharts.Dictionary<string>|undefined} langOptions\n *        Language options\n *\n * @param {Highcharts.Chart} chart\n *        Reference to chart\n */\nclass Toolbar {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options, langOptions, chart) {\n        this.width = 0;\n        this.isDirty = false;\n        this.chart = chart;\n        this.options = options;\n        this.lang = langOptions;\n        // Set url for icons.\n        this.iconsURL = this.getIconsURL();\n        this.guiEnabled = options.enabled;\n        this.visible = StockToolbar_pick(options.visible, true);\n        this.guiClassName = options.className;\n        this.toolbarClassName = options.toolbarClassName;\n        // General events collection which should be removed upon\n        // destroy/update:\n        this.eventsToUnbind = [];\n        if (this.guiEnabled) {\n            this.createContainer();\n            this.createButtons();\n            this.showHideNavigation();\n        }\n        StockToolbar_fireEvent(this, 'afterInit');\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create and set up stockTools buttons with their events and submenus.\n     * @private\n     */\n    createButtons() {\n        const lang = this.lang, guiOptions = this.options, toolbar = this.toolbar, buttons = guiOptions.buttons, defs = guiOptions.definitions, allButtons = toolbar.childNodes;\n        this.buttonList = buttons;\n        // Create buttons\n        buttons.forEach((btnName) => {\n            const button = this.addButton(toolbar, defs, btnName, lang);\n            this.eventsToUnbind.push(StockToolbar_addEvent(button.buttonWrapper, 'click', () => this.eraseActiveButtons(allButtons, button.buttonWrapper)));\n            if (StockToolbar_isArray(defs[btnName].items)) {\n                // Create submenu buttons\n                this.addSubmenu(button, defs[btnName]);\n            }\n        });\n    }\n    /**\n     * Create submenu (list of buttons) for the option. In example main button\n     * is Line, in submenu will be buttons with types of lines.\n     *\n     * @private\n     *\n     * @param {Highcharts.Dictionary<Highcharts.HTMLDOMElement>} parentBtn\n     *        Button which has submenu\n     *\n     * @param {Highcharts.StockToolsGuiDefinitionsButtonsOptions} button\n     *        List of all buttons\n     */\n    addSubmenu(parentBtn, button) {\n        const submenuArrow = parentBtn.submenuArrow, buttonWrapper = parentBtn.buttonWrapper, buttonWidth = getStyle(buttonWrapper, 'width'), wrapper = this.wrapper, menuWrapper = this.listWrapper, allButtons = this.toolbar.childNodes, \n        // Create submenu container\n        submenuWrapper = this.submenu = createElement('ul', {\n            className: 'highcharts-submenu-wrapper'\n        }, void 0, buttonWrapper);\n        // Create submenu buttons and select the first one\n        this.addSubmenuItems(buttonWrapper, button);\n        // Show / hide submenu\n        this.eventsToUnbind.push(StockToolbar_addEvent(submenuArrow, 'click', (e) => {\n            e.stopPropagation();\n            // Erase active class on all other buttons\n            this.eraseActiveButtons(allButtons, buttonWrapper);\n            // Hide menu\n            if (buttonWrapper.className\n                .indexOf('highcharts-current') >= 0) {\n                menuWrapper.style.width =\n                    menuWrapper.startWidth + 'px';\n                buttonWrapper.classList.remove('highcharts-current');\n                submenuWrapper.style.display = 'none';\n            }\n            else {\n                // Show menu\n                // to calculate height of element\n                submenuWrapper.style.display = 'block';\n                let topMargin = submenuWrapper.offsetHeight -\n                    buttonWrapper.offsetHeight - 3;\n                // Calculate position of submenu in the box\n                // if submenu is inside, reset top margin\n                if (\n                // Cut on the bottom\n                !(submenuWrapper.offsetHeight +\n                    buttonWrapper.offsetTop >\n                    wrapper.offsetHeight &&\n                    // Cut on the top\n                    buttonWrapper.offsetTop > topMargin)) {\n                    topMargin = 0;\n                }\n                // Apply calculated styles\n                css(submenuWrapper, {\n                    top: -topMargin + 'px',\n                    left: buttonWidth + 3 + 'px'\n                });\n                buttonWrapper.className += ' highcharts-current';\n                menuWrapper.startWidth = wrapper.offsetWidth;\n                menuWrapper.style.width = menuWrapper.startWidth +\n                    getStyle(menuWrapper, 'padding-left') +\n                    submenuWrapper.offsetWidth + 3 + 'px';\n            }\n        }));\n    }\n    /**\n     * Create buttons in submenu\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} buttonWrapper\n     *        Button where submenu is placed\n     *\n     * @param {Highcharts.StockToolsGuiDefinitionsButtonsOptions} button\n     *        List of all buttons options\n     */\n    addSubmenuItems(buttonWrapper, button) {\n        const _self = this, submenuWrapper = this.submenu, lang = this.lang, menuWrapper = this.listWrapper, items = button.items;\n        let submenuBtn;\n        // Add items to submenu\n        items.forEach((btnName) => {\n            // Add buttons to submenu\n            submenuBtn = this.addButton(submenuWrapper, button, btnName, lang);\n            this.eventsToUnbind.push(StockToolbar_addEvent(submenuBtn.mainButton, 'click', function () {\n                _self.switchSymbol(this, buttonWrapper, true);\n                menuWrapper.style.width =\n                    menuWrapper.startWidth + 'px';\n                submenuWrapper.style.display = 'none';\n            }));\n        });\n        // Select first submenu item\n        const firstSubmenuItem = submenuWrapper.querySelectorAll('li > .highcharts-menu-item-btn')[0];\n        // Replace current symbol, in main button, with submenu's button style\n        this.switchSymbol(firstSubmenuItem, false);\n    }\n    /**\n     * Erase active class on all other buttons.\n     * @private\n     */\n    eraseActiveButtons(buttons, currentButton, submenuItems) {\n        [].forEach.call(buttons, (btn) => {\n            if (btn !== currentButton) {\n                btn.classList.remove('highcharts-current');\n                btn.classList.remove('highcharts-active');\n                submenuItems =\n                    btn.querySelectorAll('.highcharts-submenu-wrapper');\n                // Hide submenu\n                if (submenuItems.length > 0) {\n                    submenuItems[0].style.display = 'none';\n                }\n            }\n        });\n    }\n    /**\n     * Create single button. Consist of HTML elements `li`, `button`, and (if\n     * exists) submenu container.\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} target\n     *        HTML reference, where button should be added\n     *\n     * @param {object} options\n     *        All options, by btnName refer to particular button\n     *\n     * @param {string} btnName\n     *        Button name of functionality mapped for specific class\n     *\n     * @param {Highcharts.Dictionary<string>} lang\n     *        All titles, by btnName refer to particular button\n     *\n     * @return {object}\n     *         References to all created HTML elements\n     */\n    addButton(target, options, btnName, lang = {}) {\n        const btnOptions = options[btnName], items = btnOptions.items, classMapping = Toolbar.prototype.classMapping, userClassName = btnOptions.className || '';\n        // Main button wrapper\n        const buttonWrapper = createElement('li', {\n            className: StockToolbar_pick(classMapping[btnName], '') + ' ' + userClassName,\n            title: lang[btnName] || btnName\n        }, void 0, target);\n        // Single button\n        const elementType = (btnOptions.elementType || 'button');\n        const mainButton = createElement(elementType, {\n            className: 'highcharts-menu-item-btn'\n        }, void 0, buttonWrapper);\n        // Submenu\n        if (items && items.length) {\n            // Arrow is a hook to show / hide submenu\n            const submenuArrow = createElement('button', {\n                className: 'highcharts-submenu-item-arrow ' +\n                    'highcharts-arrow-right'\n            }, void 0, buttonWrapper);\n            submenuArrow.style.backgroundImage = 'url(' +\n                this.iconsURL + 'arrow-bottom.svg)';\n            return {\n                buttonWrapper,\n                mainButton,\n                submenuArrow\n            };\n        }\n        mainButton.style.backgroundImage = 'url(' +\n            this.iconsURL + btnOptions.symbol + ')';\n        return {\n            buttonWrapper,\n            mainButton\n        };\n    }\n    /**\n     * Create navigation's HTML elements: container and arrows.\n     * @private\n     */\n    addNavigation() {\n        const wrapper = this.wrapper;\n        // Arrow wrapper\n        this.arrowWrapper = createElement('div', {\n            className: 'highcharts-arrow-wrapper'\n        });\n        this.arrowUp = createElement('div', {\n            className: 'highcharts-arrow-up'\n        }, void 0, this.arrowWrapper);\n        this.arrowUp.style.backgroundImage =\n            'url(' + this.iconsURL + 'arrow-right.svg)';\n        this.arrowDown = createElement('div', {\n            className: 'highcharts-arrow-down'\n        }, void 0, this.arrowWrapper);\n        this.arrowDown.style.backgroundImage =\n            'url(' + this.iconsURL + 'arrow-right.svg)';\n        wrapper.insertBefore(this.arrowWrapper, wrapper.childNodes[0]);\n        // Attach scroll events\n        this.scrollButtons();\n    }\n    /**\n     * Add events to navigation (two arrows) which allows user to scroll\n     * top/down GUI buttons, if container's height is not enough.\n     * @private\n     */\n    scrollButtons() {\n        const wrapper = this.wrapper, toolbar = this.toolbar, step = 0.1 * wrapper.offsetHeight; // 0.1 = 10%\n        let targetY = 0;\n        this.eventsToUnbind.push(StockToolbar_addEvent(this.arrowUp, 'click', () => {\n            if (targetY > 0) {\n                targetY -= step;\n                toolbar.style.marginTop = -targetY + 'px';\n            }\n        }));\n        this.eventsToUnbind.push(StockToolbar_addEvent(this.arrowDown, 'click', () => {\n            if (wrapper.offsetHeight + targetY <=\n                toolbar.offsetHeight + step) {\n                targetY += step;\n                toolbar.style.marginTop = -targetY + 'px';\n            }\n        }));\n    }\n    /*\n     * Create the stockTools container and sets up event bindings.\n     *\n     */\n    createContainer() {\n        const chart = this.chart, guiOptions = this.options, container = chart.container, navigation = chart.options.navigation, bindingsClassName = navigation?.bindingsClassName, self = this;\n        let listWrapper, toolbar;\n        // Create main container\n        const wrapper = this.wrapper = createElement('div', {\n            className: 'highcharts-stocktools-wrapper ' +\n                guiOptions.className + ' ' + bindingsClassName\n        });\n        container.appendChild(wrapper);\n        this.showHideBtn = createElement('div', {\n            className: 'highcharts-toggle-toolbar highcharts-arrow-left'\n        }, void 0, wrapper);\n        // Toggle menu\n        this.eventsToUnbind.push(StockToolbar_addEvent(this.showHideBtn, 'click', () => {\n            this.update({\n                gui: {\n                    visible: !self.visible\n                }\n            });\n        }));\n        // Mimic event behaviour of being outside chart.container\n        [\n            'mousedown',\n            'mousemove',\n            'click',\n            'touchstart'\n        ].forEach((eventType) => {\n            StockToolbar_addEvent(wrapper, eventType, (e) => e.stopPropagation());\n        });\n        StockToolbar_addEvent(wrapper, 'mouseover', (e) => chart.pointer?.onContainerMouseLeave(e));\n        // Toolbar\n        this.toolbar = toolbar = createElement('ul', {\n            className: 'highcharts-stocktools-toolbar ' +\n                guiOptions.toolbarClassName\n        });\n        // Add container for list of buttons\n        this.listWrapper = listWrapper = createElement('div', {\n            className: 'highcharts-menu-wrapper'\n        });\n        wrapper.insertBefore(listWrapper, wrapper.childNodes[0]);\n        listWrapper.insertBefore(toolbar, listWrapper.childNodes[0]);\n        this.showHideToolbar();\n        // Add navigation which allows user to scroll down / top GUI buttons\n        this.addNavigation();\n    }\n    /**\n     * Function called in redraw verifies if the navigation should be visible.\n     * @private\n     */\n    showHideNavigation() {\n        // Arrows\n        // 50px space for arrows\n        if (this.visible &&\n            this.toolbar.offsetHeight > (this.wrapper.offsetHeight - 50)) {\n            this.arrowWrapper.style.display = 'block';\n        }\n        else {\n            // Reset margin if whole toolbar is visible\n            this.toolbar.style.marginTop = '0px';\n            // Hide arrows\n            this.arrowWrapper.style.display = 'none';\n        }\n    }\n    /**\n     * Create button which shows or hides GUI toolbar.\n     * @private\n     */\n    showHideToolbar() {\n        const wrapper = this.wrapper, toolbar = this.listWrapper, submenu = this.submenu, \n        // Show hide toolbar\n        showHideBtn = this.showHideBtn;\n        let visible = this.visible;\n        showHideBtn.style.backgroundImage =\n            'url(' + this.iconsURL + 'arrow-right.svg)';\n        if (!visible) {\n            // Hide\n            if (submenu) {\n                submenu.style.display = 'none';\n            }\n            showHideBtn.style.left = '0px';\n            visible = this.visible = false;\n            toolbar.classList.add('highcharts-hide');\n            showHideBtn.classList.add('highcharts-arrow-right');\n            wrapper.style.height = showHideBtn.offsetHeight + 'px';\n        }\n        else {\n            wrapper.style.height = '100%';\n            toolbar.classList.remove('highcharts-hide');\n            showHideBtn.classList.remove('highcharts-arrow-right');\n            showHideBtn.style.top = getStyle(toolbar, 'padding-top') + 'px';\n            showHideBtn.style.left = (wrapper.offsetWidth +\n                getStyle(toolbar, 'padding-left')) + 'px';\n        }\n    }\n    /*\n     * In main GUI button, replace icon and class with submenu button's\n     * class / symbol.\n     *\n     * @param {HTMLDOMElement} - submenu button\n     * @param {Boolean} - true or false\n     *\n     */\n    switchSymbol(button, redraw) {\n        const buttonWrapper = button.parentNode, buttonWrapperClass = buttonWrapper.className, \n        // Main button in first level og GUI\n        mainNavButton = buttonWrapper.parentNode.parentNode;\n        // If the button is disabled, don't do anything\n        if (buttonWrapperClass.indexOf('highcharts-disabled-btn') > -1) {\n            return;\n        }\n        // Set class\n        mainNavButton.className = '';\n        if (buttonWrapperClass) {\n            mainNavButton.classList.add(buttonWrapperClass.trim());\n        }\n        // Set icon\n        mainNavButton\n            .querySelectorAll('.highcharts-menu-item-btn')[0]\n            .style.backgroundImage =\n            button.style.backgroundImage;\n        // Set active class\n        if (redraw) {\n            this.toggleButtonActiveClass(mainNavButton);\n        }\n    }\n    /**\n     * Set select state (active class) on button.\n     * @private\n     */\n    toggleButtonActiveClass(button) {\n        const classList = button.classList;\n        if (classList.contains('highcharts-active')) {\n            classList.remove('highcharts-active');\n        }\n        else {\n            classList.add('highcharts-active');\n        }\n    }\n    /**\n     * Remove active class from all buttons except defined.\n     * @private\n     */\n    unselectAllButtons(button) {\n        const activeBtns = button.parentNode\n            .querySelectorAll('.highcharts-active');\n        [].forEach.call(activeBtns, (activeBtn) => {\n            if (activeBtn !== button) {\n                activeBtn.classList.remove('highcharts-active');\n            }\n        });\n    }\n    /**\n     * Update GUI with given options.\n     * @private\n     */\n    update(options, redraw) {\n        this.isDirty = !!options.gui.definitions;\n        StockToolbar_merge(true, this.chart.options.stockTools, options);\n        StockToolbar_merge(true, this.options, options.gui);\n        this.visible = StockToolbar_pick(this.options.visible && this.options.enabled, true);\n        // If Stock Tools are updated, then bindings should be updated too:\n        if (this.chart.navigationBindings) {\n            this.chart.navigationBindings.update();\n        }\n        this.chart.isDirtyBox = true;\n        if (StockToolbar_pick(redraw, true)) {\n            this.chart.redraw();\n        }\n    }\n    /**\n     * Destroy all HTML GUI elements.\n     * @private\n     */\n    destroy() {\n        const stockToolsDiv = this.wrapper, parent = stockToolsDiv && stockToolsDiv.parentNode;\n        this.eventsToUnbind.forEach((unbinder) => unbinder());\n        // Remove the empty element\n        if (parent) {\n            parent.removeChild(stockToolsDiv);\n        }\n    }\n    /**\n     * Redraws the toolbar based on the current state of the options.\n     * @private\n     */\n    redraw() {\n        if (this.options.enabled !== this.guiEnabled) {\n            this.handleGuiEnabledChange();\n        }\n        else {\n            if (!this.guiEnabled) {\n                return;\n            }\n            this.updateClassNames();\n            this.updateButtons();\n            this.updateVisibility();\n            this.showHideNavigation();\n            this.showHideToolbar();\n        }\n    }\n    /**\n     * Hadles the change of the `enabled` option.\n     * @private\n     */\n    handleGuiEnabledChange() {\n        if (this.options.enabled === false) {\n            this.destroy();\n            this.visible = false;\n        }\n        if (this.options.enabled === true) {\n            this.createContainer();\n            this.createButtons();\n        }\n        this.guiEnabled = this.options.enabled;\n    }\n    /**\n     * Updates the class names of the GUI and toolbar elements.\n     * @private\n     */\n    updateClassNames() {\n        if (this.options.className !== this.guiClassName) {\n            if (this.guiClassName) {\n                this.wrapper.classList.remove(this.guiClassName);\n            }\n            if (this.options.className) {\n                this.wrapper.classList.add(this.options.className);\n            }\n            this.guiClassName = this.options.className;\n        }\n        if (this.options.toolbarClassName !== this.toolbarClassName) {\n            if (this.toolbarClassName) {\n                this.toolbar.classList.remove(this.toolbarClassName);\n            }\n            if (this.options.toolbarClassName) {\n                this.toolbar.classList.add(this.options.toolbarClassName);\n            }\n            this.toolbarClassName = this.options.toolbarClassName;\n        }\n    }\n    /**\n     * Updates the buttons in the toolbar if the button options have changed.\n     * @private\n     */\n    updateButtons() {\n        if (!StockToolbar_shallowArraysEqual(this.options.buttons, this.buttonList) ||\n            this.isDirty) {\n            this.toolbar.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n            this.createButtons();\n        }\n    }\n    /**\n     * Updates visibility based on current options.\n     * @private\n     */\n    updateVisibility() {\n        if (StockToolbar_defined(this.options.visible)) {\n            this.visible = this.options.visible;\n        }\n    }\n    /**\n     * @private\n     */\n    getIconsURL() {\n        return this.chart.options.navigation.iconsURL ||\n            this.options.iconsURL ||\n            'https://code.highcharts.com/12.2.0/gfx/stock-icons/';\n    }\n}\nToolbar.prototype.classMapping = {\n    circle: 'highcharts-circle-annotation',\n    ellipse: 'highcharts-ellipse-annotation',\n    rectangle: 'highcharts-rectangle-annotation',\n    label: 'highcharts-label-annotation',\n    segment: 'highcharts-segment',\n    arrowSegment: 'highcharts-arrow-segment',\n    ray: 'highcharts-ray',\n    arrowRay: 'highcharts-arrow-ray',\n    line: 'highcharts-infinity-line',\n    arrowInfinityLine: 'highcharts-arrow-infinity-line',\n    verticalLine: 'highcharts-vertical-line',\n    horizontalLine: 'highcharts-horizontal-line',\n    crooked3: 'highcharts-crooked3',\n    crooked5: 'highcharts-crooked5',\n    elliott3: 'highcharts-elliott3',\n    elliott5: 'highcharts-elliott5',\n    pitchfork: 'highcharts-pitchfork',\n    fibonacci: 'highcharts-fibonacci',\n    fibonacciTimeZones: 'highcharts-fibonacci-time-zones',\n    parallelChannel: 'highcharts-parallel-channel',\n    measureX: 'highcharts-measure-x',\n    measureY: 'highcharts-measure-y',\n    measureXY: 'highcharts-measure-xy',\n    timeCycles: 'highcharts-time-cycles',\n    verticalCounter: 'highcharts-vertical-counter',\n    verticalLabel: 'highcharts-vertical-label',\n    verticalArrow: 'highcharts-vertical-arrow',\n    currentPriceIndicator: 'highcharts-current-price-indicator',\n    indicators: 'highcharts-indicators',\n    flagCirclepin: 'highcharts-flag-circlepin',\n    flagDiamondpin: 'highcharts-flag-diamondpin',\n    flagSquarepin: 'highcharts-flag-squarepin',\n    flagSimplepin: 'highcharts-flag-simplepin',\n    zoomX: 'highcharts-zoom-x',\n    zoomY: 'highcharts-zoom-y',\n    zoomXY: 'highcharts-zoom-xy',\n    typeLine: 'highcharts-series-type-line',\n    typeOHLC: 'highcharts-series-type-ohlc',\n    typeHLC: 'highcharts-series-type-hlc',\n    typeCandlestick: 'highcharts-series-type-candlestick',\n    typeHollowCandlestick: 'highcharts-series-type-hollowcandlestick',\n    typeHeikinAshi: 'highcharts-series-type-heikinashi',\n    fullScreen: 'highcharts-full-screen',\n    toggleAnnotations: 'highcharts-toggle-annotations',\n    saveChart: 'highcharts-save-chart',\n    separator: 'highcharts-separator'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const StockToolbar = (Toolbar);\n\n;// ./code/es-modules/Stock/StockTools/StockToolsGui.js\n/* *\n *\n *  GUI generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions: StockToolsGui_setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { addEvent: StockToolsGui_addEvent, getStyle: StockToolsGui_getStyle, merge: StockToolsGui_merge, pick: StockToolsGui_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Verify if Toolbar should be added.\n * @private\n */\nfunction chartSetStockTools(options) {\n    const chartOptions = this.options, lang = chartOptions.lang, guiOptions = StockToolsGui_merge(chartOptions.stockTools && chartOptions.stockTools.gui, options && options.gui), langOptions = lang && lang.stockTools && lang.stockTools.gui;\n    this.stockTools = new StockToolbar(guiOptions, langOptions, this);\n    if (this.stockTools.guiEnabled) {\n        this.isDirtyBox = true;\n    }\n}\n/**\n * @private\n */\nfunction StockToolsGui_compose(ChartClass, NavigationBindingsClass) {\n    const chartProto = ChartClass.prototype;\n    if (!chartProto.setStockTools) {\n        StockToolsGui_addEvent(ChartClass, 'afterGetContainer', onChartAfterGetContainer);\n        StockToolsGui_addEvent(ChartClass, 'beforeRedraw', onChartBeforeRedraw);\n        StockToolsGui_addEvent(ChartClass, 'beforeRender', onChartBeforeRedraw);\n        StockToolsGui_addEvent(ChartClass, 'destroy', StockToolsGui_onChartDestroy);\n        StockToolsGui_addEvent(ChartClass, 'getMargins', onChartGetMargins, { order: 0 });\n        StockToolsGui_addEvent(ChartClass, 'render', StockToolsGui_onChartRender);\n        chartProto.setStockTools = chartSetStockTools;\n        StockToolsGui_addEvent(NavigationBindingsClass, 'deselectButton', StockToolsGui_onNavigationBindingsDeselectButton);\n        StockToolsGui_addEvent(NavigationBindingsClass, 'selectButton', onNavigationBindingsSelectButton);\n        StockToolsGui_setOptions(StockTools_StockToolsDefaults);\n    }\n}\n/**\n * Run HTML generator\n * @private\n */\nfunction onChartAfterGetContainer() {\n    this.setStockTools();\n}\n/**\n * Handle beforeRedraw and beforeRender\n * @private\n */\nfunction onChartBeforeRedraw() {\n    if (this.stockTools) {\n        this.stockTools.redraw();\n        setOffset(this);\n    }\n}\n/**\n * Function to calculate and set the offset width for stock tools.\n * @private\n */\nfunction setOffset(chart) {\n    if (chart.stockTools?.guiEnabled) {\n        const optionsChart = chart.options.chart;\n        const listWrapper = chart.stockTools.listWrapper;\n        const offsetWidth = listWrapper && ((listWrapper.startWidth +\n            StockToolsGui_getStyle(listWrapper, 'padding-left') +\n            StockToolsGui_getStyle(listWrapper, 'padding-right')) || listWrapper.offsetWidth);\n        chart.stockTools.width = offsetWidth;\n        let dirty = false;\n        if (offsetWidth < chart.plotWidth) {\n            const nextX = StockToolsGui_pick(optionsChart.spacingLeft, optionsChart.spacing && optionsChart.spacing[3], 0) + offsetWidth;\n            const diff = nextX - chart.spacingBox.x;\n            chart.spacingBox.x = nextX;\n            chart.spacingBox.width -= diff;\n            dirty = true;\n        }\n        else if (offsetWidth === 0) {\n            dirty = true;\n        }\n        if (offsetWidth !== chart.stockTools.prevOffsetWidth) {\n            chart.stockTools.prevOffsetWidth = offsetWidth;\n            if (dirty) {\n                chart.isDirtyLegend = true;\n            }\n        }\n    }\n}\n/**\n * @private\n */\nfunction StockToolsGui_onChartDestroy() {\n    if (this.stockTools) {\n        this.stockTools.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartGetMargins() {\n    const offsetWidth = this.stockTools?.visible && this.stockTools.guiEnabled ?\n        this.stockTools.width : 0;\n    if (offsetWidth && offsetWidth < this.plotWidth) {\n        this.plotLeft += offsetWidth;\n        this.spacing[3] += offsetWidth;\n    }\n}\n/**\n * Check if the correct price indicator button is displayed, #15029.\n * @private\n */\nfunction StockToolsGui_onChartRender() {\n    const stockTools = this.stockTools, button = stockTools &&\n        stockTools.toolbar &&\n        stockTools.toolbar.querySelector('.highcharts-current-price-indicator');\n    // Change the initial button background.\n    if (stockTools &&\n        this.navigationBindings &&\n        this.options.series &&\n        button) {\n        if (this.navigationBindings.utils\n            ?.isPriceIndicatorEnabled?.(this.series)) {\n            button.firstChild.style['background-image'] =\n                'url(\"' + stockTools.getIconsURL() + 'current-price-hide.svg\")';\n        }\n        else {\n            button.firstChild.style['background-image'] =\n                'url(\"' + stockTools.getIconsURL() + 'current-price-show.svg\")';\n        }\n    }\n}\n/**\n * @private\n */\nfunction StockToolsGui_onNavigationBindingsDeselectButton(event) {\n    const className = 'highcharts-submenu-wrapper', gui = this.chart.stockTools;\n    if (gui && gui.guiEnabled) {\n        let button = event.button;\n        // If deselecting a button from a submenu, select state for it's parent\n        if (button.parentNode.className.indexOf(className) >= 0) {\n            button = button.parentNode.parentNode;\n        }\n        button.classList.remove('highcharts-active');\n    }\n}\n/**\n * Communication with bindings\n * @private\n */\nfunction onNavigationBindingsSelectButton(event) {\n    const className = 'highcharts-submenu-wrapper', gui = this.chart.stockTools;\n    if (gui && gui.guiEnabled) {\n        let button = event.button;\n        // Unselect other active buttons\n        gui.unselectAllButtons(event.button);\n        // If clicked on a submenu, select state for it's parent\n        if (button.parentNode.className.indexOf(className) >= 0) {\n            button = button.parentNode.parentNode;\n        }\n        // Set active class on the current button\n        gui.toggleButtonActiveClass(button);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst StockToolsGui = {\n    compose: StockToolsGui_compose\n};\n/* harmony default export */ const StockTools_StockToolsGui = (StockToolsGui);\n\n;// ./code/es-modules/masters/modules/stock-tools.js\n\n\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.NavigationBindings = G.NavigationBindings || Annotations_NavigationBindings;\nG.Toolbar = StockToolbar;\nStockTools_StockTools.compose(G.NavigationBindings);\nStockTools_StockToolsGui.compose(G.Chart, G.NavigationBindings);\n/* harmony default export */ const stock_tools_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__660__", "ChartNavigationComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "stock_tools_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "compose", "chart", "navigation", "Additions", "constructor", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "for<PERSON>ach", "Chart_ChartNavigationComposition", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "defined", "isNumber", "pick", "annotationsFieldsTypes", "backgroundColor", "borderColor", "borderRadius", "color", "fill", "fontSize", "labels", "name", "stroke", "title", "NavigationBindingsUtilities", "getAssignedAxis", "coords", "filter", "coord", "extremes", "axis", "getExtremes", "axisMin", "min", "axisMax", "max", "minPointOffset", "value", "isInternal", "getFieldType", "predefinedType", "fieldType", "NavigationBindingsDefaults_getAssignedAxis", "NavigationBindingsDefaults_isNumber", "merge", "NavigationBindingsDefaults", "lang", "popup", "simpleShapes", "lines", "circle", "ellipse", "rectangle", "label", "shapeOptions", "typeOptions", "format", "strokeWidth", "labelOptions", "backgroundColors", "borderWidth", "style", "padding", "height", "shapes", "bindingsClassName", "bindings", "circleAnnotation", "className", "start", "e", "pointer", "getCoordinates", "coordsX", "xAxis", "coordsY", "yAxis", "addAnnotation", "lang<PERSON><PERSON>", "type", "point", "x", "y", "index", "r", "annotationsOptions", "steps", "annotation", "distance", "mockPointOpts", "inverted", "toPixels", "Math", "sqrt", "pow", "chartX", "chartY", "ellipseAnnotation", "points", "ry", "target", "position", "getAbsolutePosition", "translatePoint", "position2", "newR", "getDistanceFromLine", "getYAxis", "newRY", "abs", "toValue", "setYRadius", "rectangleAnnotation", "command", "labelAnnotation", "overflow", "crop", "events", "animation", "defer", "setOptions", "composed", "doc", "win", "NavigationBindings_getAssignedAxis", "NavigationBindings_getFieldType", "addEvent", "attr", "NavigationBindings_defined", "fireEvent", "isArray", "isFunction", "NavigationBindings_isNumber", "isObject", "NavigationBindings_merge", "objectEach", "NavigationBindings_pick", "pushUnique", "onAnnotationRemove", "navigationBindings", "deselectAnnotation", "onChartDestroy", "destroy", "onChartLoad", "NavigationBindings", "initEvents", "initUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledClassName", "buttonsEnabled", "series", "visible", "container", "boundClassNames", "buttonNode", "querySelectorAll", "i", "length", "button", "cls", "noDataState", "indexOf", "classList", "remove", "onNavigationBindingsClosePopup", "onNavigationBindingsDeselectButton", "selectedButtonElement", "selectableAnnotation", "annotationType", "touchStartX", "touchStartY", "originalClick", "defaultOptions", "click", "selectAndShowPopup", "eventArguments", "prevAnnotation", "activeAnnotation", "setControlPointsVisibility", "formType", "annotationToFields", "onSubmit", "data", "actionType", "removeAnnotation", "config", "fieldsToOptions", "fields", "crosshairY", "enabled", "crosshairX", "touchstart", "touches", "clientX", "clientY", "touchend", "changedTouches", "AnnotationClass", "ChartClass", "types", "eventsToUnbind", "getElementsByClassName", "getCoords", "bindingsContainer", "subContainer", "event", "getButtonEvents", "contains", "bindingsButtonClick", "callback", "eventName", "passive", "cancelClick", "isInsidePlot", "plotLeft", "plotTop", "visiblePlotOnly", "bindingsChartClick", "isTouchDevice", "bindingsContainerMouseMove", "clickEvent", "svgContainer", "renderer", "boxWrapper", "shouldEventBeFired", "nextEvent", "currentUserDetails", "coll", "mouseMoveEvent", "<PERSON><PERSON><PERSON><PERSON>", "init", "addClass", "stockTools", "removeClass", "parentNode", "closestPolyfill", "el", "s", "ElementProto", "Element", "elementMatches", "matches", "msMatchesSelector", "webkitMatchesSelector", "ret", "closest", "parentElement", "nodeType", "setTimeout", "stepIndex", "end", "_container", "moveEvent", "field", "parsedValue", "parseFloat", "path", "split", "<PERSON><PERSON><PERSON><PERSON>", "match", "parent", "nextName", "editables", "annotationsEditable", "nestedEditables", "nestedOptions", "nonEditables", "annotationsNonEditable", "visualOptions", "traverse", "option", "parentEditables", "parent<PERSON><PERSON>", "nextParent", "arrayOption", "nestedOption", "nested<PERSON><PERSON>", "toString", "typeOption", "typeKey", "getClickedClassNames", "element", "classNames", "elemClassName", "tagName", "concat", "map", "removeEvents", "unbinder", "background", "innerBackground", "outerBackground", "line", "connector", "verticalLine", "measure", "<PERSON><PERSON><PERSON><PERSON>", "tunnel", "pitchfork", "rect", "crookedLine", "basicAnnotation", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "getOptions", "StockToolsUtilities_getAssignedAxis", "StockToolsUtilities_getFieldType", "StockToolsUtilities_defined", "StockToolsUtilities_fireEvent", "StockToolsUtilities_isNumber", "<PERSON><PERSON><PERSON>", "indicatorsWithAxes", "indicatorsWithVolume", "attractToPoint", "distX", "Number", "MAX_VALUE", "closestPoint", "searchPoint", "below", "StockTools_StockToolsUtilities", "addFlagFromForm", "toolbar", "pointConfig", "seriesOptions", "onSeries", "id", "shape", "updated", "gui<PERSON><PERSON><PERSON>", "addSeries", "isNotNavigatorYAxis", "userOptions", "isPriceIndicatorEnabled", "some", "lastVisiblePrice", "lastPrice", "manageIndicators", "parentSeries", "seriesConfig", "linkedTo", "seriesId", "linkedSeries", "removedYAxisProps", "top", "resizeYAxes", "plotOptions", "getDGApproximation", "dataGrouping", "approximation", "addAxis", "offset", "opposite", "text", "tickPixelInterval", "showLastLabel", "align", "params", "volumeSeriesID", "shallowArraysEqual", "b", "updateHeight", "horiz", "updateNthPoint", "startIndex", "updateRectSize", "width", "StockToolsBindings_addFlagFromForm", "StockToolsBindings_attractToPoint", "StockToolsBindings_isNotNavigatorYAxis", "StockToolsBindings_isPriceIndicatorEnabled", "StockToolsBindings_manageIndicators", "StockToolsBindings_updateHeight", "StockToolsBindings_updateNthPoint", "StockToolsBindings_updateRectSize", "StockToolsBindings_fireEvent", "StockToolsBindings_merge", "StockToolsBindings", "segment", "arrowSegment", "markerEnd", "ray", "arrowRay", "infinityLine", "arrowInfinityLine", "horizontalLine", "draggable", "crooked3", "crooked5", "elliott3", "elliott5", "measureX", "selectType", "measureY", "measureXY", "parallelChannel", "controlPoint", "verticalCounter", "timeCycles", "verticalLabel", "verticalArrow", "fibonacciTimeZones", "flagCir<PERSON><PERSON>", "flag<PERSON><PERSON><PERSON><PERSON>", "flagSquarepin", "flagSimplepin", "zoomX", "zooming", "zoomY", "zoomXY", "seriesTypeLine", "useOhlcData", "seriesTypeOhlc", "seriesTypeCandlestick", "seriesTypeHeikinAshi", "seriesTypeHLC", "seriesTypeHollowCandlestick", "fullScreen", "fullscreen", "toggle", "currentPriceIndicator", "gui", "priceIndicatorEnabled", "indicators", "toggleAnnotations", "iconsURL", "getIconsURL", "toggledAnnotations", "annotations", "setVisibility", "<PERSON><PERSON><PERSON><PERSON>", "saveChart", "flags", "yAxes", "is", "localStorage", "setItem", "JSON", "stringify", "StockToolsDefaults", "crookedLines", "advanced", "verticalLabels", "zoomChange", "typeChange", "typeOHLC", "typeLine", "typeCandlestick", "typeHLC", "typeHollowCandlestick", "typeHeikinAshi", "addButton", "saveButton", "edit<PERSON><PERSON><PERSON>", "removeButton", "volume", "noFilterMatch", "searchIndicators", "clearFilter", "period", "periods", "standardDeviation", "periodTenkan", "periodSenkouSpanB", "periodATR", "multiplierATR", "shortPeriod", "<PERSON>P<PERSON><PERSON>", "signalPeriod", "decimals", "algorithm", "topBand", "bottomBand", "initialAccelerationFactor", "maxAccelerationFactor", "increment", "multiplier", "ranges", "highIndex", "lowIndex", "deviation", "xAxisUnit", "factor", "fastAvgPeriod", "slowAvgPeriod", "average", "indicatorAliases", "abands", "bb", "dema", "ema", "ikh", "keltnerchannels", "linearRegression", "pivotpoints", "pc", "priceenvelopes", "psar", "sma", "supertrend", "tema", "vbp", "vwap", "wma", "zigzag", "apo", "ad", "aroon", "aroonoscillator", "atr", "ao", "cci", "chaikin", "cmf", "cmo", "disparityindex", "dmi", "dpo", "klinger", "linearRegressionAngle", "linearRegressionIntercept", "linearRegressionSlope", "macd", "mfi", "momentum", "natr", "obv", "ppo", "roc", "rsi", "slowstochastic", "stochastic", "trix", "<PERSON><PERSON><PERSON><PERSON>", "toolbarClassName", "buttons", "definitions", "separator", "elementType", "symbol", "items", "StockTools_setOptions", "StockTools_getAssignedAxis", "StockTools_isNotNavigatorYAxis", "StockTools_isPriceIndicatorEnabled", "correctFloat", "StockTools_defined", "StockTools_isNumber", "StockTools_pick", "navigationGetYAxisPositions", "plotHeight", "defaultHeight", "allAxesHeight", "previousAxisHeight", "removedHeight", "removedTop", "isPercentage", "positions", "every", "navigationGetYAxisResizers", "resizers", "_yAxis", "nextYAxis", "controlledAxis", "next", "navigationRecalculateYAxisPositions", "changedSpace", "modifyHeight", "adder", "prevPosition", "navigationResizeYAxes", "getYAxisPositions", "getYAxisResizers", "resize", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "StockToolbar_addEvent", "createElement", "css", "StockToolbar_defined", "StockToolbar_fireEvent", "getStyle", "StockToolbar_isArray", "StockToolbar_merge", "StockToolbar_pick", "StockToolbar_shallowArraysEqual", "<PERSON><PERSON><PERSON>", "langOptions", "isDirty", "guiClassName", "createContainer", "createButtons", "showHideNavigation", "guiOptions", "defs", "allButtons", "childNodes", "buttonList", "btnName", "buttonWrapper", "eraseActiveButtons", "addSubmenu", "parentBtn", "submenuArrow", "buttonWidth", "wrapper", "menuWrapper", "listWrapper", "submenuWrapper", "submenu", "addSubmenuItems", "stopPropagation", "startWidth", "display", "<PERSON><PERSON><PERSON><PERSON>", "offsetHeight", "offsetTop", "left", "offsetWidth", "submenuBtn", "_self", "mainButton", "switchSymbol", "firstSubmenuItem", "currentButton", "submenuItems", "btn", "btnOptions", "classMapping", "userClassName", "backgroundImage", "addNavigation", "arrowWrapper", "arrowUp", "arrowDown", "insertBefore", "scrollButtons", "step", "targetY", "marginTop", "self", "append<PERSON><PERSON><PERSON>", "showHideBtn", "eventType", "onContainerMouseLeave", "showHideToolbar", "add", "buttonWrapperClass", "mainNavButton", "trim", "toggleButtonActiveClass", "unselectAllButtons", "activeBtns", "activeBtn", "isDirtyBox", "stockToolsDiv", "<PERSON><PERSON><PERSON><PERSON>", "handleGuiEnabledChange", "updateClassNames", "updateButtons", "updateVisibility", "innerHTML", "emptyHTML", "StockToolsGui_setOptions", "StockToolsGui_addEvent", "StockToolsGui_getStyle", "StockToolsGui_merge", "StockToolsGui_pick", "chartSetStockTools", "chartOptions", "onChartAfterGetContainer", "setStockTools", "onChartBeforeRedraw", "setOffset", "optionsChart", "dirty", "plot<PERSON>id<PERSON>", "nextX", "spacingLeft", "spacing", "diff", "spacingBox", "prevOffsetWidth", "isDirtyLegend", "StockToolsGui_onChartDestroy", "onChart<PERSON><PERSON><PERSON><PERSON><PERSON>", "StockToolsGui_onChartRender", "querySelector", "utils", "StockToolsGui_onNavigationBindingsDeselectButton", "onNavigationBindingsSelectButton", "G", "StockTools_StockTools", "NavigationBindingsClass", "navigationProto", "recalculateYAxisPositions", "StockTools_StockToolsGui", "chartProto", "order", "Chart"], "mappings": "CAaA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,GAAM,EACnI,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,UAAa,CAACA,EAAK,MAAS,CAACA,EAAK,GAAM,CAAE,GACjJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,GAAM,EAErKA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,GAAM,CAC5I,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,IACzI,AAAC,CAAA,KACP,aACA,IAiHNC,EAjHUC,EAAuB,CAE/B,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,AAAChB,IACxB,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,IAAOlB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACpB,EAASsB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,IAkBrH,AAAC,SAAU1B,CAA0B,EAqBjCA,EAA2B4B,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHKA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAIC,EAAUF,EAAK,EAEnCA,CACX,CAYA,OAAME,EAMFC,YAAYH,CAAK,CAAE,CACf,IAAI,CAACI,OAAO,CAAG,EAAE,CACjB,IAAI,CAACJ,KAAK,CAAGA,CACjB,CAaAK,UAAUC,CAAQ,CAAE,CAChB,IAAI,CAACN,KAAK,CAACC,UAAU,CAACG,OAAO,CAACG,IAAI,CAACD,EACvC,CAIAE,OAAOC,CAAO,CAAEC,CAAM,CAAE,CACpB,IAAI,CAACN,OAAO,CAACO,OAAO,CAAC,AAACL,IAClBA,EAASZ,IAAI,CAAC,IAAI,CAACM,KAAK,CAAES,EAASC,EACvC,EACJ,CACJ,CACAvC,EAA2B+B,SAAS,CAAGA,CAC3C,EAAG/B,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAMnC,IAAMyC,EAAoCzC,EAGvE,IAAI0C,EAAmHvC,EAAoB,KACvIwC,EAAuIxC,EAAoBI,CAAC,CAACmC,GAajK,GAAM,CAAEE,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAInB,IAW/BoB,EAAyB,CAC3BC,gBAAiB,SACjBC,YAAa,SACbC,aAAc,SACdC,MAAO,SACPC,KAAM,SACNC,SAAU,SACVC,OAAQ,SACRC,KAAM,SACNC,OAAQ,SACRC,MAAO,QACX,EAgEmCC,EALA,CAC/BX,uBAAAA,EACAY,gBA3CJ,SAAyBC,CAAM,EAC3B,OAAOA,EAAOC,MAAM,CAAC,AAACC,IAClB,IAAMC,EAAWD,EAAME,IAAI,CAACC,WAAW,GAAIC,EAAUH,EAASI,GAAG,CAAEC,EAAUL,EAASM,GAAG,CAGzFC,EAAiBxB,EAAKgB,EAAME,IAAI,CAACM,cAAc,CAAE,GACjD,OAAOzB,EAASqB,IAAYrB,EAASuB,IACjCN,EAAMS,KAAK,EAAKL,EAAUI,GAC1BR,EAAMS,KAAK,EAAKH,EAAUE,GAE1B,CAACR,EAAME,IAAI,CAAC1B,OAAO,CAACkC,UAAU,AACtC,EAAE,CAAC,EAAE,AACT,EAgCIC,aApBJ,SAAsB5D,CAAG,CAAE0D,CAAK,EAC5B,IAAMG,EAAiB3B,CAAsB,CAAClC,EAAI,CAC9C8D,EAAY,OAAOJ,EAIvB,OAHI3B,EAAQ8B,IACRC,CAAAA,EAAYD,CAAa,EAEtB,CAAA,CACH,OAAU,OACV,OAAU,SACV,QAAW,UACf,CAAA,CAAC,CAACC,EAAU,AAChB,CAUA,EAeM,CAAEhB,gBAAiBiB,CAA0C,CAAE,CAAGlB,EAElE,CAAEb,SAAUgC,CAAmC,CAAEC,MAAAA,CAAK,CAAE,CAAInD,IA8X/BoD,EAJD,CAC9BC,KAlXS,CAQTlD,WAAY,CAMRmD,MAAO,CACHC,aAAc,gBACdC,MAAO,QACPC,OAAQ,SACRC,QAAS,UACTC,UAAW,YACXC,MAAO,QACPC,aAAc,gBACdC,YAAa,UACbrC,KAAM,OACNsC,OAAQ,OACRC,YAAa,aACbnC,OAAQ,aACRC,MAAO,QACPF,KAAM,OACNqC,aAAc,gBACdtC,OAAQ,SACRN,gBAAiB,mBACjB6C,iBAAkB,oBAClB5C,YAAa,eACbC,aAAc,gBACd4C,YAAa,eACbC,MAAO,QACPC,QAAS,UACT3C,SAAU,YACVF,MAAO,QACP8C,OAAQ,SACRC,OAAQ,eACZ,CACJ,CACJ,EAuUIpE,WAlUe,CAWfqE,kBAAmB,gCA6BnBC,SAAU,CAQNC,iBAAkB,CAEdC,UAAW,+BAEXC,MAAO,SAAUC,CAAC,EACd,IAAM5C,EAAS,IAAI,CAAC/B,KAAK,CAAC4E,OAAO,EAAEC,eAAeF,GAAIG,EAAU/C,GAAUgB,EAA2ChB,EAAOgD,KAAK,EAAGC,EAAUjD,GAAUgB,EAA2ChB,EAAOkD,KAAK,EAAGhF,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAE5P,GAAI,AAAC6E,GAAYE,EAGjB,OAAO,IAAI,CAAChF,KAAK,CAACkF,aAAa,CAACjC,EAAM,CAClCkC,QAAS,SACTC,KAAM,kBACNf,OAAQ,CAAC,CACDe,KAAM,SACNC,MAAO,CACHC,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,CAChBqC,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,AAC7B,EACAC,EAAG,CACP,EAAE,AACV,EAAGxF,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACC,gBAAgB,CACjEkB,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUhB,CAAC,CAAEiB,CAAU,EACnB,IAEIC,EAFExB,EAASuB,EAAWnF,OAAO,CAAC4D,MAAM,CAAEyB,EAAiB,AAACzB,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACgB,KAAK,EAC9F,CAAC,EAEL,GAAIrC,EAAoC8C,EAAcf,KAAK,GACvD/B,EAAoC8C,EAAcb,KAAK,EAAG,CAC1D,IAAMc,EAAW,IAAI,CAAC/F,KAAK,CAAC+F,QAAQ,CAAET,EAAI,IAAI,CAACtF,KAAK,CAAC+E,KAAK,CAACe,EAAcf,KAAK,CAAC,CAC1EiB,QAAQ,CAACF,EAAcR,CAAC,EAAGC,EAAI,IAAI,CAACvF,KAAK,CAACiF,KAAK,CAACa,EAAcb,KAAK,CAAC,CACpEe,QAAQ,CAACF,EAAcP,CAAC,EAC7BM,EAAWI,KAAKzD,GAAG,CAACyD,KAAKC,IAAI,CAACD,KAAKE,GAAG,CAACJ,EAAWR,EAAIZ,EAAEyB,MAAM,CAAGd,EAAIX,EAAEyB,MAAM,CAAE,GAC3EH,KAAKE,GAAG,CAACJ,EAAWT,EAAIX,EAAE0B,MAAM,CAAGd,EAAIZ,EAAE0B,MAAM,CAAE,IAAK,EAC9D,CACAT,EAAWpF,MAAM,CAAC,CACd6D,OAAQ,CAAC,CACDoB,EAAGI,CACP,EAAE,AACV,EACJ,EACH,AACL,EASAS,kBAAmB,CACf7B,UAAW,gCACXC,MAAO,SAAUC,CAAC,EACd,IAAM5C,EAAS,IAAI,CAAC/B,KAAK,CAAC4E,OAAO,EAAEC,eAAeF,GAAIG,EAAU/C,GAAUgB,EAA2ChB,EAAOgD,KAAK,EAAGC,EAAUjD,GAAUgB,EAA2ChB,EAAOkD,KAAK,EAAGhF,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAC5P,GAAI,AAAC6E,GAAYE,EAGjB,OAAO,IAAI,CAAChF,KAAK,CAACkF,aAAa,CAACjC,EAAM,CAClCkC,QAAS,UACTC,KAAM,kBACNf,OAAQ,CACJ,CACIe,KAAM,UACNL,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,CACN8D,GAAI,CACR,EACH,AACL,EAAGvG,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC+B,iBAAiB,CAClEZ,kBAAkB,EAC3B,EACAC,MAAO,CACH,SAAUhB,CAAC,CAAEiB,CAAU,EACnB,IAAMa,EAASb,EAAWvB,MAAM,CAAC,EAAE,CAAEqC,EAAWD,EAAOE,mBAAmB,CAACF,EAAOF,MAAM,CAAC,EAAE,EAC3FE,EAAOG,cAAc,CAACjC,EAAEyB,MAAM,CAAGM,EAASpB,CAAC,CAAEX,EAAE0B,MAAM,CAAGK,EAASnB,CAAC,CAAE,GACpEkB,EAAO/F,MAAM,CAAC,CAAA,EAClB,EACA,SAAUiE,CAAC,CAAEiB,CAAU,EACnB,IAAMa,EAASb,EAAWvB,MAAM,CAAC,EAAE,CAAEqC,EAAWD,EAAOE,mBAAmB,CAACF,EAAOF,MAAM,CAAC,EAAE,EAAGM,EAAYJ,EAAOE,mBAAmB,CAACF,EAAOF,MAAM,CAAC,EAAE,EAAGO,EAAOL,EAAOM,mBAAmB,CAACL,EAAUG,EAAWlC,EAAEyB,MAAM,CAAEzB,EAAE0B,MAAM,EAAGpB,EAAQwB,EAAOO,QAAQ,GAAIC,EAAQhB,KAAKiB,GAAG,CAACjC,EAAMkC,OAAO,CAAC,GAAKlC,EAAMkC,OAAO,CAACL,IACjTL,EAAOW,UAAU,CAACH,GAClBR,EAAO/F,MAAM,CAAC,CAAA,EAClB,EACH,AACL,EAQA2G,oBAAqB,CAEjB5C,UAAW,kCAEXC,MAAO,SAAUC,CAAC,EACd,IAAM5C,EAAS,IAAI,CAAC/B,KAAK,CAAC4E,OAAO,EAAEC,eAAeF,GAAIG,EAAU/C,GAAUgB,EAA2ChB,EAAOgD,KAAK,EAAGC,EAAUjD,GAAUgB,EAA2ChB,EAAOkD,KAAK,EAE/M,GAAI,CAACH,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEqC,EAAQD,EAAQ3C,IAAI,CAACqD,KAAK,CAAEP,EAAQD,EAAQ7C,IAAI,CAACqD,KAAK,CAAEvF,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAC9I,OAAO,IAAI,CAACD,KAAK,CAACkF,aAAa,CAACjC,EAAM,CAClCkC,QAAS,YACTC,KAAM,kBACNf,OAAQ,CAAC,CACDe,KAAM,OACNmB,OAAQ,CACJ,CAAExB,MAAAA,EAAOE,MAAAA,EAAOK,EAAAA,EAAGC,EAAAA,CAAE,EACrB,CAAER,MAAAA,EAAOE,MAAAA,EAAOK,EAAAA,EAAGC,EAAAA,CAAE,EACrB,CAAER,MAAAA,EAAOE,MAAAA,EAAOK,EAAAA,EAAGC,EAAAA,CAAE,EACrB,CAAER,MAAAA,EAAOE,MAAAA,EAAOK,EAAAA,EAAGC,EAAAA,CAAE,EACrB,CAAE+B,QAAS,GAAI,EAClB,AACL,EAAE,AACV,EAAGrH,EACEyF,kBAAkB,CAAEzF,EACpBsE,QAAQ,CACR8C,mBAAmB,CACnB3B,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUhB,CAAC,CAAEiB,CAAU,EACnB,IAAMvB,EAASuB,EAAWnF,OAAO,CAAC4D,MAAM,CAAEkC,EAAU,AAAClC,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACkC,MAAM,EACxF,EAAE,CAAGxE,EAAS,IAAI,CAAC/B,KAAK,CAAC4E,OAAO,EAAEC,eAAeF,GAAIG,EAAU/C,GAAUgB,EAA2ChB,EAAOgD,KAAK,EAAGC,EAAUjD,GAAUgB,EAA2ChB,EAAOkD,KAAK,EAClN,GAAIH,GAAWE,EAAS,CACpB,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,AAE1C6D,CAAAA,CAAM,CAAC,EAAE,CAACjB,CAAC,CAAGA,EAEdiB,CAAM,CAAC,EAAE,CAACjB,CAAC,CAAGA,EACdiB,CAAM,CAAC,EAAE,CAAChB,CAAC,CAAGA,EAEdgB,CAAM,CAAC,EAAE,CAAChB,CAAC,CAAGA,EACdK,EAAWpF,MAAM,CAAC,CACd6D,OAAQ,CAAC,CACDkC,OAAQA,CACZ,EAAE,AACV,EACJ,CACJ,EACH,AACL,EAOAgB,gBAAiB,CAEb9C,UAAW,8BAEXC,MAAO,SAAUC,CAAC,EACd,IAAM5C,EAAS,IAAI,CAAC/B,KAAK,CAAC4E,OAAO,EAAEC,eAAeF,GAAIG,EAAU/C,GAAUgB,EAA2ChB,EAAOgD,KAAK,EAAGC,EAAUjD,GAAUgB,EAA2ChB,EAAOkD,KAAK,EAAGhF,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAE5P,GAAI,AAAC6E,GAAYE,EAGjB,OAAO,IAAI,CAAChF,KAAK,CAACkF,aAAa,CAACjC,EAAM,CAClCkC,QAAS,QACTC,KAAM,kBACNrB,aAAc,CACVF,OAAQ,UACR2D,SAAU,OACVC,KAAM,CAAA,CACV,EACAhG,OAAQ,CAAC,CACD4D,MAAO,CACHN,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBF,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,CACJ,EAAE,AACV,EAAGzC,EACEyF,kBAAkB,CAAEzF,EACpBsE,QAAQ,CACRgD,eAAe,CACf7B,kBAAkB,EAC3B,CACJ,CACJ,EAmDAgC,OAAQ,CAAC,EAcThC,mBAAoB,CAChBiC,UAAW,CACPC,MAAO,CACX,CACJ,CACJ,CASA,EAgBM,CAAEC,WAAAA,CAAU,CAAE,CAAI/H,IAElB,CAAE+D,OAAAA,CAAM,CAAE,CAAI/C,IAEd,CAAEgH,SAAAA,CAAQ,CAAEC,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAIlI,IAG1B,CAAEgC,gBAAiBmG,CAAkC,CAAErF,aAAcsF,CAA+B,CAAE,CAAGrG,EAEzG,CAAEsG,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAErH,QAASsH,CAA0B,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEC,WAAAA,CAAU,CAAExH,SAAUyH,CAA2B,CAAEC,SAAAA,CAAQ,CAAEzF,MAAO0F,CAAwB,CAAEC,WAAAA,CAAU,CAAE3H,KAAM4H,CAAuB,CAAEC,WAAAA,CAAU,CAAE,CAAIhJ,IA+B1O,SAASiJ,IACD,IAAI,CAAC/I,KAAK,CAACgJ,kBAAkB,EAC7B,IAAI,CAAChJ,KAAK,CAACgJ,kBAAkB,CAACC,kBAAkB,EAExD,CAIA,SAASC,IACD,IAAI,CAACF,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACG,OAAO,EAEvC,CAIA,SAASC,IACL,IAAM3I,EAAU,IAAI,CAACA,OAAO,CACxBA,GAAWA,EAAQR,UAAU,EAAIQ,EAAQR,UAAU,CAACsE,QAAQ,GAC5D,IAAI,CAACyE,kBAAkB,CAAG,IAAIK,EAAmB,IAAI,CAAE5I,EAAQR,UAAU,EACzE,IAAI,CAAC+I,kBAAkB,CAACM,UAAU,GAClC,IAAI,CAACN,kBAAkB,CAACO,UAAU,GAE1C,CAIA,SAASC,IACL,IAAMR,EAAqB,IAAI,CAACA,kBAAkB,CAAES,EAAoB,0BACxE,GAAI,IAAI,EAAIT,EAAoB,CAG5B,IAAIU,EAAiB,CAAA,EAMrB,GALA,IAAI,CAACC,MAAM,CAAChJ,OAAO,CAAC,AAACgJ,IACb,CAACA,EAAOlJ,OAAO,CAACkC,UAAU,EAAIgH,EAAOC,OAAO,EAC5CF,CAAAA,EAAiB,CAAA,CAAG,CAE5B,GACI,IAAI,CAACV,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACa,SAAS,EACjC,IAAI,CAACb,kBAAkB,CAACa,SAAS,CAAC,EAAE,CAAE,CACtC,IAAMA,EAAY,IAAI,CAACb,kBAAkB,CAACa,SAAS,CAAC,EAAE,CACtDjB,EAAWI,EAAmBc,eAAe,CAAE,CAACpH,EAAO1D,KAGnD,IAAM+K,EAAaF,EAAUG,gBAAgB,CAAC,IAAMhL,GACpD,GAAI+K,EACA,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAWG,MAAM,CAAED,IAAK,CACxC,IAAME,EAASJ,CAAU,CAACE,EAAE,CAAEG,EAAMD,EAAO1F,SAAS,AAChD/B,AAAsB,CAAA,WAAtBA,EAAM2H,WAAW,CAGsB,KAAnCD,EAAIE,OAAO,CAACb,IACZU,EAAOI,SAAS,CAACC,MAAM,CAACf,GAGtBC,EAOiC,KAAnCU,EAAIE,OAAO,CAACb,IACZU,EAAOI,SAAS,CAACC,MAAM,CAACf,GAPW,KAAnCW,EAAIE,OAAO,CAACb,IACZU,CAAAA,EAAO1F,SAAS,EAAI,IAAMgF,CAAgB,CAStD,CAER,EACJ,CACJ,CACJ,CAIA,SAASgB,IACL,IAAI,CAACxB,kBAAkB,EAC3B,CAIA,SAASyB,IACL,IAAI,CAACC,qBAAqB,CAAG,IACjC,CAKA,SAASC,EAAqBC,CAAc,EACxC,IAmDIC,EAAaC,EAnDXC,EAAgBH,EAAerL,SAAS,CAACyL,cAAc,CAACvD,MAAM,EAChEmD,EAAerL,SAAS,CAACyL,cAAc,CAACvD,MAAM,CAACwD,KAAK,CAKxD,SAASC,EAAmBC,CAAc,EACtC,IAAMxF,EAAa,IAAI,CAAE3F,EAAa2F,EAAW5F,KAAK,CAACgJ,kBAAkB,CAAEqC,EAAiBpL,EAAWqL,gBAAgB,CACnHN,GACAA,EAActL,IAAI,CAACkG,EAAYwF,GAE/BC,IAAmBzF,GAEnB3F,EAAWgJ,kBAAkB,GAC7BhJ,EAAWqL,gBAAgB,CAAG1F,EAC9BA,EAAW2F,0BAA0B,CAAC,CAAA,GACtCjD,EAAUrI,EAAY,YAAa,CAC/B2F,WAAYA,EACZ4F,SAAU,qBACV/K,QAASR,EAAWwL,kBAAkB,CAAC7F,GACvC8F,SAAU,SAAUC,CAAI,EACpB,GAAIA,AAAoB,WAApBA,EAAKC,UAAU,CACf3L,EAAWqL,gBAAgB,CAAG,CAAA,EAC9BrL,EAAWD,KAAK,CAAC6L,gBAAgB,CAACjG,OAEjC,CACD,IAAMkG,EAAS,CAAC,EAChB7L,EAAW8L,eAAe,CAACJ,EAAKK,MAAM,CAAEF,GACxC7L,EAAWgJ,kBAAkB,GAC7B,IAAMrF,EAAckI,EAAOlI,WAAW,AACN,CAAA,YAA5BgC,EAAWnF,OAAO,CAAC2E,IAAI,GAGvBxB,EAAYqI,UAAU,CAACC,OAAO,CAAItI,AACb,IADaA,EAAYqI,UAAU,CACnDnI,WAAW,CAChBF,EAAYuI,UAAU,CAACD,OAAO,CAAItI,AACb,IADaA,EAAYuI,UAAU,CACnDrI,WAAW,EAEpB8B,EAAWpF,MAAM,CAACsL,EACtB,CACJ,CACJ,IAIAxD,EAAUrI,EAAY,cAG1BmL,EAAeE,gBAAgB,CAAG,CAAA,CACtC,CAoBA3C,EAAyB,CAAA,EAAMkC,EAAerL,SAAS,CAACyL,cAAc,CAACvD,MAAM,CAAE,CAC3EwD,MAAOC,EACPiB,WAhBJ,SAAoBzH,CAAC,EACjBmG,EAAcnG,EAAE0H,OAAO,CAAC,EAAE,CAACC,OAAO,CAClCvB,EAAcpG,EAAE0H,OAAO,CAAC,EAAE,CAACE,OAAO,AACtC,EAcIC,SAVJ,SAA2B7H,CAAC,EACPmG,GAAc7E,KAAKC,IAAI,CAACD,KAAKE,GAAG,CAAC2E,EAAcnG,EAAE8H,cAAc,CAAC,EAAE,CAACH,OAAO,CAAE,GACzFrG,KAAKE,GAAG,CAAC4E,EAAcpG,EAAE8H,cAAc,CAAC,EAAE,CAACF,OAAO,CAAE,KAAO,GAE3DpB,EAAmBzL,IAAI,CAAC,IAAI,CAAEiF,EAEtC,CAKA,EACJ,CASA,MAAM0E,EAMF,OAAOtJ,QAAQ2M,CAAe,CAAEC,CAAU,CAAE,CACpC7D,EAAWhB,EAAU,wBACrBK,EAASuE,EAAiB,SAAU3D,GAEpC6B,EAAqB8B,GAErB9D,EAAW8D,EAAgBE,KAAK,CAAE,AAAC/B,IAC/BD,EAAqBC,EACzB,GACA1C,EAASwE,EAAY,UAAWzD,GAChCf,EAASwE,EAAY,OAAQvD,GAC7BjB,EAASwE,EAAY,SAAUnD,GAC/BrB,EAASkB,EAAoB,aAAcoB,GAC3CtC,EAASkB,EAAoB,iBAAkBqB,GAC/C7C,EAAW3E,GAEnB,CAMA/C,YAAYH,CAAK,CAAES,CAAO,CAAE,CACxB,IAAI,CAACqJ,eAAe,CAAG,KAAK,EAC5B,IAAI,CAAC9J,KAAK,CAAGA,EACb,IAAI,CAACS,OAAO,CAAGA,EACf,IAAI,CAACoM,cAAc,CAAG,EAAE,CACxB,IAAI,CAAChD,SAAS,CACV,IAAI,CAAC7J,KAAK,CAAC6J,SAAS,CAACiD,sBAAsB,CAAC,IAAI,CAACrM,OAAO,CAAC6D,iBAAiB,EAAI,IAC7E,IAAI,CAACuF,SAAS,CAACK,MAAM,EACtB,CAAA,IAAI,CAACL,SAAS,CAAG9B,EAAI+E,sBAAsB,CAAC,IAAI,CAACrM,OAAO,CAAC6D,iBAAiB,EAAI,GAAE,CAExF,CAMAyI,UAAUpI,CAAC,CAAE,CACT,IAAM5C,EAAS,IAAI,CAAC/B,KAAK,CAAC4E,OAAO,EAAEC,eAAeF,GAClD,MAAO,CACH5C,GAAUkG,EAAmClG,EAAOgD,KAAK,EACzDhD,GAAUkG,EAAmClG,EAAOkD,KAAK,EAC5D,AACL,CAOAqE,YAAa,CACT,IAAMrJ,EAAa,IAAI,CAAED,EAAQC,EAAWD,KAAK,CAAEgN,EAAoB/M,EAAW4J,SAAS,CAAEpJ,EAAUR,EAAWQ,OAAO,AAEzHR,CAAAA,EAAW6J,eAAe,CAAG,CAAC,EAC9BlB,EAAYnI,EAAQ8D,QAAQ,EAAI,CAAC,EAAI,AAAC7B,IAClCzC,EAAW6J,eAAe,CAACpH,EAAM+B,SAAS,CAAC,CAAG/B,CAClD,GAEA,EAAE,CAAC/B,OAAO,CAACjB,IAAI,CAACsN,EAAmB,AAACC,IAChChN,EAAW4M,cAAc,CAACtM,IAAI,CAAC4H,EAAS8E,EAAc,QAAS,AAACC,IAC5D,IAAM3I,EAAWtE,EAAWkN,eAAe,CAACF,EAAcC,GACtD3I,GACC,CAACA,EAAS4F,MAAM,CAACI,SAAS,CACtB6C,QAAQ,CAAC,4BACdnN,EAAWoN,mBAAmB,CAAC9I,EAAS4F,MAAM,CAAE5F,EAASmD,MAAM,CAAEwF,EAEzE,GACJ,GACAtE,EAAYnI,EAAQiH,MAAM,EAAI,CAAC,EAAI,CAAC4F,EAAUC,KACtC/E,EAAW8E,IACXrN,EAAW4M,cAAc,CAACtM,IAAI,CAAC4H,EAASlI,EAAYsN,EAAWD,EAAU,CAAEE,QAAS,CAAA,CAAM,GAElG,GACAvN,EAAW4M,cAAc,CAACtM,IAAI,CAAC4H,EAASnI,EAAM6J,SAAS,CAAE,QAAS,SAAUlF,CAAC,EACrE,CAAC3E,EAAMyN,WAAW,EAClBzN,EAAM0N,YAAY,CAAC/I,EAAEyB,MAAM,CAAGpG,EAAM2N,QAAQ,CAAEhJ,EAAE0B,MAAM,CAAGrG,EAAM4N,OAAO,CAAE,CACpEC,gBAAiB,CAAA,CACrB,IACA5N,EAAW6N,kBAAkB,CAAC,IAAI,CAAEnJ,EAE5C,IACA1E,EAAW4M,cAAc,CAACtM,IAAI,CAAC4H,EAASnI,EAAM6J,SAAS,CAAE,AAAC/J,IAA+EiO,aAAa,CAAG,YAAc,YAAa,SAAUpJ,CAAC,EAC3L1E,EAAW+N,0BAA0B,CAAC,IAAI,CAAErJ,EAChD,EAAG,AAAC7E,IAA+EiO,aAAa,CAAG,CAAEP,QAAS,CAAA,CAAM,EAAI,KAAK,GACjI,CAOAjE,YAAa,CACT,IAAMtJ,EAAa,IAAI,CACvBW,EACKb,OAAO,CAAC,IAAI,CAACC,KAAK,EAAEC,UAAU,CAC9BI,SAAS,CAAC,AAACI,IACZR,EAAWO,MAAM,CAACC,EACtB,EACJ,CAiBA4M,oBAAoBlD,CAAM,CAAEzC,CAAM,CAAEuG,CAAU,CAAE,CAC5C,IAAyBjO,EAAQC,AAAd,IAAI,CAAqBD,KAAK,CAAEkO,EAAelO,EAAMmO,QAAQ,CAACC,UAAU,CACvFC,EAAqB,CAAA,CACrBpO,CAFe,IAAI,CAER0K,qBAAqB,GAC5B1K,AAHW,IAAI,CAGJ0K,qBAAqB,CAACJ,SAAS,GAAKJ,EAAOI,SAAS,EAC/D8D,CAAAA,EAAqB,CAAA,CAAI,EAE7B/F,EANe,IAAI,CAMG,iBAAkB,CAAE6B,OAAQlK,AANnC,IAAI,CAM0C0K,qBAAqB,AAAC,GAC/E1K,AAPW,IAAI,CAOJqO,SAAS,GAEhBrO,AATO,IAAI,CASAsO,kBAAkB,EAC7BtO,AAAuC,gBAAvCA,AAVO,IAAI,CAUAsO,kBAAkB,CAACC,IAAI,EAClCxO,EAAM6L,gBAAgB,CAAC5L,AAXhB,IAAI,CAWuBsO,kBAAkB,EAExDtO,AAbW,IAAI,CAaJwO,cAAc,CAAGxO,AAbjB,IAAI,CAawBqO,SAAS,CAAG,CAAA,IAGvDD,GACApO,AAjBe,IAAI,CAiBRyO,cAAc,CAAGhH,EAC5BzH,AAlBe,IAAI,CAkBR0K,qBAAqB,CAAGR,EACnC7B,EAnBe,IAAI,CAmBG,eAAgB,CAAE6B,OAAQA,CAAO,GAEnDzC,EAAOiH,IAAI,EACXjH,EAAOiH,IAAI,CAACjP,IAAI,CAtBL,IAAI,CAsBcyK,EAAQ8D,GAErCvG,CAAAA,EAAOhD,KAAK,EAAIgD,EAAO/B,KAAK,AAAD,GAC3B3F,EAAMmO,QAAQ,CAACC,UAAU,CAACQ,QAAQ,CAAC,0BAIvC5O,EAAM6O,UAAU,EAAI1E,EAAOI,SAAS,CAACC,MAAM,CAAC,qBAC5C0D,EAAaY,WAAW,CAAC,wBACzB7O,AA/Be,IAAI,CA+BRqO,SAAS,CAAG,CAAA,EACvBrO,AAhCe,IAAI,CAgCRwO,cAAc,CAAG,CAAA,EAC5BxO,AAjCe,IAAI,CAiCRyO,cAAc,CAAG,KAEpC,CAeAZ,mBAAmB9N,CAAK,CAAEiO,CAAU,CAAE,CAClCjO,EAAQ,IAAI,CAACA,KAAK,CAClB,IAAyBsL,EAAmBrL,AAAzB,IAAI,CAAgCqL,gBAAgB,CAAEoD,EAAiBzO,AAAvE,IAAI,CAA8EyO,cAAc,CAAER,EAAelO,EAAMmO,QAAQ,CAACC,UAAU,CACzJ9C,IAGI,AAACA,EAAiBmC,WAAW,EAC5BQ,EAAW3C,gBAAgB,GAE5B2C,EAAWxH,MAAM,CAACsI,UAAU,EAE3BC,AA1XjB,SAAyBC,CAAE,CAAEC,CAAC,EAC1B,IAAMC,EAAenH,EAAIoH,OAAO,CAAC5P,SAAS,CAAE6P,EAAiBF,EAAaG,OAAO,EAC7EH,EAAaI,iBAAiB,EAC9BJ,EAAaK,qBAAqB,CAClCC,EAAM,KACV,GAAIN,EAAaO,OAAO,CACpBD,EAAMN,EAAaO,OAAO,CAAChQ,IAAI,CAACuP,EAAIC,QAGpC,EAAG,CACC,GAAIG,EAAe3P,IAAI,CAACuP,EAAIC,GACxB,OAAOD,EAEXA,EAAKA,EAAGU,aAAa,EAAIV,EAAGF,UAAU,AAC1C,OAASE,AAAO,OAAPA,GAAeA,AAAgB,IAAhBA,EAAGW,QAAQ,CAAQ,CAE/C,OAAOH,CACX,EAyWiCxB,EAAWxH,MAAM,CAAE,qBAG/B6E,EAAiBmC,WAAW,EAEjCoC,WAAW,KACPvE,EAAiBmC,WAAW,CAAG,CAAA,CACnC,EAAG,GANHnF,EAVW,IAAI,CAUO,eASzBoG,GAAmBA,EAAehK,KAAK,GAGvCzE,AAtBc,IAAI,CAsBPqO,SAAS,EAsBrBrO,AA5Ce,IAAI,CA4CRqO,SAAS,CAACL,EAAYhO,AA5ClB,IAAI,CA4CyBsO,kBAAkB,EAC1DtO,AA7CW,IAAI,CA6CJ0F,KAAK,GAChB1F,AA9CW,IAAI,CA8CJ6P,SAAS,GAChBpB,EAAe/I,KAAK,CAAC1F,AA/Cd,IAAI,CA+CqB6P,SAAS,CAAC,CAE1C7P,AAjDO,IAAI,CAiDAwO,cAAc,CAAGxO,AAjDrB,IAAI,CAiD4BqO,SAAS,CAAGI,EAAe/I,KAAK,CAAC1F,AAjDjE,IAAI,CAiDwE6P,SAAS,CAAC,EAG7FxH,EApDO,IAAI,CAoDW,iBAAkB,CAAE6B,OAAQlK,AApD3C,IAAI,CAoDkD0K,qBAAqB,AAAC,GACnFuD,EAAaY,WAAW,CAAC,wBAErBJ,EAAeqB,GAAG,EAClBrB,EAAeqB,GAAG,CAACrQ,IAAI,CAxDpB,IAAI,CAwD6BuO,EAAYhO,AAxD7C,IAAI,CAwDoDsO,kBAAkB,EAEjFtO,AA1DO,IAAI,CA0DAqO,SAAS,CAAG,CAAA,EACvBrO,AA3DO,IAAI,CA2DAwO,cAAc,CAAG,CAAA,EAC5BxO,AA5DO,IAAI,CA4DAyO,cAAc,CAAG,SApCpCzO,AAxBe,IAAI,CAwBRsO,kBAAkB,CAAGG,EAAehK,KAAK,CAAChF,IAAI,CAxB1C,IAAI,CAwBmDuO,GAElEhO,AA1BW,IAAI,CA0BJsO,kBAAkB,EAAIG,EAAe/I,KAAK,EACrD1F,AA3BW,IAAI,CA2BJ6P,SAAS,CAAG,EACvB7P,AA5BW,IAAI,CA4BJ0F,KAAK,CAAG,CAAA,EACnB1F,AA7BW,IAAI,CA6BJwO,cAAc,CAAGxO,AA7BjB,IAAI,CA6BwBqO,SAAS,CAC5CI,EAAe/I,KAAK,CAAC1F,AA9Bd,IAAI,CA8BqB6P,SAAS,CAAC,GAG9CxH,EAjCW,IAAI,CAiCO,iBAAkB,CAAE6B,OAAQlK,AAjCvC,IAAI,CAiC8C0K,qBAAqB,AAAC,GACnFuD,EAAaY,WAAW,CAAC,wBACzB7O,AAnCW,IAAI,CAmCJ0F,KAAK,CAAG,CAAA,EACnB1F,AApCW,IAAI,CAoCJyO,cAAc,CAAG,KAExBA,EAAeqB,GAAG,EAClBrB,EAAeqB,GAAG,CAACrQ,IAAI,CAvChB,IAAI,CAuCyBuO,EAAYhO,AAvCzC,IAAI,CAuCgDsO,kBAAkB,IAyB7F,CAaAP,2BAA2BgC,CAAU,CAAEC,CAAS,CAAE,CAC1C,IAAI,CAACxB,cAAc,EACnB,IAAI,CAACA,cAAc,CAACwB,EAAW,IAAI,CAAC1B,kBAAkB,CAE9D,CAiBAxC,gBAAgBC,CAAM,CAAEF,CAAM,CAAE,CAkC5B,OAjCAlD,EAAWoD,EAAQ,CAACtJ,EAAOwN,KACvB,IAAMC,EAAcC,WAAW1N,GAAQ2N,EAAOH,EAAMI,KAAK,CAAC,KAAMC,EAAaF,EAAKnG,MAAM,CAAG,EAQ3F,IANIzB,EAA4B0H,IAC3BzN,EAAM8N,KAAK,CAAC,WACZN,EAAMM,KAAK,CAAC,YACb9N,CAAAA,EAAQyN,CAAU,EAGlBzN,AAAU,cAAVA,EAAuB,CACvB,IAAI+N,EAAS3E,EACbuE,EAAK1P,OAAO,CAAC,CAACe,EAAM8D,KAChB,GAAI9D,AAAS,cAATA,GAAwBA,AAAS,gBAATA,EAAwB,CAChD,IAAMgP,EAAW7H,EAAwBwH,CAAI,CAAC7K,EAAQ,EAAE,CAAE,GACtD+K,CAAAA,IAAe/K,EAEfiL,CAAM,CAAC/O,EAAK,CAAGgB,GAET+N,CAAM,CAAC/O,EAAK,EAElB+O,CAAAA,CAAM,CAAC/O,EAAK,CAAGgP,EAASF,KAAK,CAAC,OAC1B,EAAE,CACF,CAAC,CAAA,EAKLC,EAASA,CAAM,CAAC/O,EAAK,CAE7B,CACJ,EACJ,CACJ,GACOoK,CACX,CAMA7C,oBAAqB,CACb,IAAI,CAACqC,gBAAgB,GACrB,IAAI,CAACA,gBAAgB,CAACC,0BAA0B,CAAC,CAAA,GACjD,IAAI,CAACD,gBAAgB,CAAG,CAAA,EAEhC,CAaAG,mBAAmB7F,CAAU,CAAE,CAC3B,IAAMnF,EAAUmF,EAAWnF,OAAO,CAAEkQ,EAAYtH,EAAmBuH,mBAAmB,CAAEC,EAAkBF,EAAUG,aAAa,CAAE1L,EAAOyD,EAAwBpI,EAAQ2E,IAAI,CAAE3E,EAAQ4D,MAAM,EAAI5D,EAAQ4D,MAAM,CAAC,EAAE,EAC/M5D,EAAQ4D,MAAM,CAAC,EAAE,CAACe,IAAI,CAAE3E,EAAQgB,MAAM,EAAIhB,EAAQgB,MAAM,CAAC,EAAE,EAC3DhB,EAAQgB,MAAM,CAAC,EAAE,CAAC2D,IAAI,CAAE,SAAU2L,EAAe1H,EAAmB2H,sBAAsB,CAACvQ,EAAQ0E,OAAO,CAAC,EAAI,EAAE,CAAE8L,EAAgB,CACnI9L,QAAS1E,EAAQ0E,OAAO,CACxBC,KAAMA,CACV,EAoBA,SAAS8L,EAASC,CAAM,CAAEnS,CAAG,CAAEoS,CAAe,CAAEX,CAAM,CAAEY,CAAS,EAC7D,IAAIC,EACAF,GACA/I,EAA2B8I,IAC3BJ,AAA8B,KAA9BA,EAAazG,OAAO,CAACtL,IACpB,CAAA,AAACoS,CAAAA,EAAgB9G,OAAO,EACrB8G,EAAgB9G,OAAO,CAACtL,EAAG,GAAM,GACjCoS,CAAe,CAACpS,EAAI,EACpBoS,AAAoB,CAAA,IAApBA,CAAuB,IAGvB7I,EAAQ4I,IACRV,CAAM,CAACzR,EAAI,CAAG,EAAE,CAChBmS,EAAOxQ,OAAO,CAAC,CAAC4Q,EAAatH,KACpBvB,EAAS6I,IAMVd,CAAM,CAACzR,EAAI,CAACiL,EAAE,CAAG,CAAC,EAClBrB,EAAW2I,EAAa,CAACC,EAAcC,KACnCP,EAASM,EAAcC,EAAWZ,CAAe,CAAC7R,EAAI,CAAEyR,CAAM,CAACzR,EAAI,CAACiL,EAAE,CAAEjL,EAC5E,IAPAkS,EAASK,EAAa,EAAGV,CAAe,CAAC7R,EAAI,CAAEyR,CAAM,CAACzR,EAAI,CAAEA,EASpE,IAEK0J,EAASyI,IACdG,EAAa,CAAC,EACV/I,EAAQkI,IACRA,EAAOlQ,IAAI,CAAC+Q,GACZA,CAAU,CAACtS,EAAI,CAAG,CAAC,EACnBsS,EAAaA,CAAU,CAACtS,EAAI,EAG5ByR,CAAM,CAACzR,EAAI,CAAGsS,EAElB1I,EAAWuI,EAAQ,CAACK,EAAcC,KAC9BP,EAASM,EAAcC,EAAWzS,AAAQ,IAARA,EAC9BoS,EACAP,CAAe,CAAC7R,EAAI,CAAEsS,EAAYtS,EAC1C,IAIIA,AAAQ,WAARA,EACAyR,CAAM,CAACzR,EAAI,CAAG,CACV6E,EAAOsN,EAAQvL,EAAWnE,MAAM,CAAC,EAAE,CAAC8E,MAAM,CAAC,EAAE,EAAEmL,QAAQ,GACvD,OACH,CAEInJ,EAAQkI,GACbA,EAAOlQ,IAAI,CAAC,CAAC4Q,EAAQjJ,EAAgCmJ,EAAWF,GAAQ,EAGxEV,CAAM,CAACzR,EAAI,CAAG,CAACmS,EAAQjJ,EAAgClJ,EAAKmS,GAAQ,CAIpF,CAYA,OAXAvI,EAAWnI,EAAS,CAAC0Q,EAAQnS,KACrBA,AAAQ,gBAARA,GACAiS,CAAa,CAACjS,EAAI,CAAG,CAAC,EACtB4J,EAAWnI,CAAO,CAACzB,EAAI,CAAE,CAAC2S,EAAYC,KAClCV,EAASS,EAAYC,EAASf,EAAiBI,CAAa,CAACjS,EAAI,CAAE4S,EACvE,IAGAV,EAASC,EAAQnS,EAAK2R,CAAS,CAACvL,EAAK,CAAE6L,EAAejS,EAE9D,GACOiS,CACX,CAiBAY,qBAAqBhI,CAAS,CAAEqD,CAAK,CAAE,CACnC,IAAI4E,EAAU5E,EAAMzG,MAAM,CAAEsL,EAAa,EAAE,CAAEC,EAC7C,KAAOF,GAAWA,EAAQG,OAAO,GAC7BD,CAAAA,EAAgB5J,EAAK0J,EAAS,QAAO,GAEjCC,CAAAA,EAAaA,EAAWG,MAAM,CAACF,EAC1B1B,KAAK,CAAC,KAEN6B,GAAG,CAAC,AAACzQ,GAAU,CAACA,EAAMoQ,EAAQ,EAAE,EAGrCA,AADJA,CAAAA,EAAUA,EAAQ/C,UAAU,AAAD,IACXlF,KAIpB,OAAOkI,CACX,CAiBA5E,gBAAgBtD,CAAS,CAAEqD,CAAK,CAAE,CAC9B,IACI3I,EADEtE,EAAa,IAAI,CAUvB,OARA8R,AAFsC,IAAI,CAACF,oBAAoB,CAAChI,EAAWqD,GAEhEvM,OAAO,CAAC,AAAC8D,IACZxE,EAAW6J,eAAe,CAACrF,CAAS,CAAC,EAAE,CAAC,EAAI,CAACF,GAC7CA,CAAAA,EAAW,CACPmD,OAAQzH,EAAW6J,eAAe,CAACrF,CAAS,CAAC,EAAE,CAAC,CAChD0F,OAAQ1F,CAAS,CAAC,EAAE,AACxB,CAAA,CAER,GACOF,CACX,CAQA/D,OAAOC,CAAO,CAAE,CACZ,IAAI,CAACA,OAAO,CAAGkI,EAAyB,CAAA,EAAM,IAAI,CAAClI,OAAO,CAAEA,GAC5D,IAAI,CAAC2R,YAAY,GACjB,IAAI,CAAC9I,UAAU,EACnB,CAOA8I,cAAe,CACX,IAAI,CAACvF,cAAc,CAAClM,OAAO,CAAC,AAAC0R,GAAaA,IAC9C,CAKAlJ,SAAU,CACN,IAAI,CAACiJ,YAAY,EACrB,CACJ,CAOA/I,EAAmBuH,mBAAmB,CAAG,CAGrCE,cAAe,CACX/M,aAAc,CAAC,QAAS,SAAU,kBAAkB,CACpDtC,OAAQ,CAAC,QAAQ,CACjBiC,MAAO,CAAC,QAAQ,CAChBQ,MAAO,CAAC,WAAY,QAAQ,CAC5BoO,WAAY,CAAC,OAAQ,cAAe,SAAS,CAC7CC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClD7O,aAAc,CAAC,OAAQ,cAAe,SAAS,CAC/CU,OAAQ,CAAC,OAAQ,cAAe,SAAS,CACzCoO,KAAM,CAAC,cAAe,SAAS,CAC/BzO,iBAAkB,CAAC,CAAA,EAAK,CACxB0O,UAAW,CAAC,OAAQ,cAAe,SAAS,CAC5CvG,WAAY,CAAC,cAAe,SAAS,CACrCF,WAAY,CAAC,cAAe,SAAS,AACzC,EAEA1I,OAAQ,CAAC,SAAS,CAClBC,QAAS,CAAC,SAAS,CACnBmP,aAAc,EAAE,CAChBjP,MAAO,CAAC,eAAe,CAEvBkP,QAAS,CAAC,aAAc,aAAc,aAAa,CAEnDC,UAAW,EAAE,CACbC,OAAQ,CAAC,aAAc,OAAQ,SAAS,CACxCC,UAAW,CAAC,kBAAmB,kBAAkB,CACjDC,KAAM,CAAC,SAAS,CAEhBC,YAAa,EAAE,CACfC,gBAAiB,CAAC,SAAU,eAAe,AAC/C,EAGA7J,EAAmB2H,sBAAsB,CAAG,CACxCvN,UAAW,CAAC,aAAc,aAAc,eAAe,CACvDD,QAAS,CAAC,eAAe,CACzBD,OAAQ,CAAC,eAAe,AAC5B,EAyCA,IAAI4P,EAAmG7U,EAAoB,KACvH8U,EAAuH9U,EAAoBI,CAAC,CAACyU,GAejJ,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAIvT,IAElB,CAAEgC,gBAAiBwR,CAAmC,CAAE1Q,aAAc2Q,EAAgC,CAAE,CAAG1R,EAG3G,CAAEd,QAASyS,EAA2B,CAAElL,UAAWmL,EAA6B,CAAEzS,SAAU0S,EAA4B,CAAEC,UAAAA,EAAS,CAAE,CAAI7T,IASzI8T,GAAqB,CACvB,MACA,KACA,QACA,kBACA,MACA,KACA,MACA,UACA,MACA,MACA,iBACA,MACA,MACA,wBACA,4BACA,wBACA,UACA,OACA,MACA,WACA,OACA,MACA,MACA,MACA,MACA,iBACA,aACA,OACA,YACH,CAIKC,GAAuB,CACzB,KACA,MACA,UACA,MACA,MACA,MACA,OACH,CAiGD,SAASC,GAAenP,CAAC,CAAE3E,CAAK,EAC5B,IAAM+B,EAAS/B,EAAM4E,OAAO,EAAEC,eAAeF,GACzCG,EAASE,EAAS+O,EAAQC,OAAOC,SAAS,CAAEC,EAMhD,GALIlU,EAAMgJ,kBAAkB,EAAIjH,IAC5B+C,EAAUwO,EAAoCvR,EAAOgD,KAAK,EAC1DC,EAAUsO,EAAoCvR,EAAOkD,KAAK,GAG1D,CAACH,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,CAWvB,GATAsC,EAAQ7C,IAAI,CAACwH,MAAM,CAAChJ,OAAO,CAAC,AAACgJ,IACzB,GAAIA,EAAOpD,MAAM,CAAE,CACf,IAAMlB,EAAQsE,EAAOwK,WAAW,CAACxP,EAAG,CAAA,GAChCU,GAAS0O,EAAQ9N,KAAKiB,GAAG,CAAC7B,EAAMC,CAAC,CAAGA,KACpCyO,EAAQ9N,KAAKiB,GAAG,CAAC7B,EAAMC,CAAC,CAAGA,GAC3B4O,EAAe7O,EAEvB,CACJ,GACI6O,GAAgBA,EAAa5O,CAAC,EAAI4O,EAAa3O,CAAC,CAChD,MAAO,CACHD,EAAG4O,EAAa5O,CAAC,CACjBC,EAAG2O,EAAa3O,CAAC,CACjB6O,MAAO7O,EAAI2O,EAAa3O,CAAC,CACzBoE,OAAQuK,EAAavK,MAAM,CAC3B5E,MAAOmP,EAAavK,MAAM,CAAC5E,KAAK,CAACS,KAAK,EAAI,EAC1CP,MAAOiP,EAAavK,MAAM,CAAC1E,KAAK,CAACO,KAAK,EAAI,CAC9C,CAER,CAwP6B,IAAM6O,GAdP,CACxBT,mBAAAA,GACAC,qBAAAA,GACAS,gBAxVJ,SAAyBlP,CAAI,EACzB,OAAO,SAAUT,CAAC,EACd,IAAM1E,EAAa,IAAI,CAAED,EAAQC,EAAWD,KAAK,CAAEuU,EAAUvU,EAAM6O,UAAU,CAAExJ,EAAQyO,GAAenP,EAAG3E,GACzG,GAAI,CAACqF,EACD,OAEJ,IAAMmP,EAAc,CAChBlP,EAAGD,EAAMC,CAAC,CACVC,EAAGF,EAAME,CAAC,AACd,EACMkP,EAAgB,CAClBrP,KAAM,QACNsP,SAAUrP,EAAMsE,MAAM,CAACgL,EAAE,CACzBC,MAAOxP,EACPuG,KAAM,CAAC6I,EAAY,CACnBzP,MAAOM,EAAMN,KAAK,CAClBE,MAAOI,EAAMJ,KAAK,CAClBI,MAAO,CACHqC,OAAQ,CACJwD,MAAO,WACH,IAAM7F,EAAQ,IAAI,CAAE5E,EAAU4E,EAAM5E,OAAO,CAC3CgT,GAA8BxT,EAAY,YAAa,CACnDoF,MAAOA,EACPmG,SAAU,qBACV/K,QAAS,CACL0E,QAAS,QACTC,KAAM,QACNxD,MAAO,CACHnB,EAAQmB,KAAK,CACb2R,GAAiC,QAAS9S,EAAQmB,KAAK,EAC1D,CACDF,KAAM,CACFjB,EAAQiB,IAAI,CACZ6R,GAAiC,OAAQ9S,EAAQiB,IAAI,EACxD,AACL,EACAgK,SAAU,SAAUmJ,CAAO,EACnBA,AAAuB,WAAvBA,EAAQjJ,UAAU,CAClBvG,EAAMmF,MAAM,GAGZnF,EAAM7E,MAAM,CAACP,EAAW8L,eAAe,CAAC8I,EAAQ7I,MAAM,CAAE,CAAC,GAEjE,CACJ,EACJ,CACJ,CACJ,CACJ,EACKuI,GAAYA,EAAQO,UAAU,EAC/B9U,EAAM+U,SAAS,CAACN,GAEpBhB,GAA8BxT,EAAY,YAAa,CACnDuL,SAAU,OAEV/K,QAAS,CACL0E,QAAS,QACTC,KAAM,QACNxD,MAAO,CAAC,IAAK2R,GAAiC,QAAS,KAAK,CAC5D7R,KAAM,CAAC,SAAU6R,GAAiC,QAAS,UAAU,AACzE,EAEA7H,SAAU,SAAUC,CAAI,EACpB1L,EAAW8L,eAAe,CAACJ,EAAKK,MAAM,CAAEyI,EAAc9I,IAAI,CAAC,EAAE,EAC7D3L,EAAM+U,SAAS,CAACN,EACpB,CACJ,EACJ,CACJ,EAqRIX,eAAAA,GACAhS,gBAAiBwR,EACjB0B,oBAnOJ,SAA6B7S,CAAI,EAC7B,MAAOA,AAA+B,+BAA/BA,EAAK8S,WAAW,CAACxQ,SAAS,AACrC,EAkOIyQ,wBAtNJ,SAAiCvL,CAAM,EACnC,OAAOA,EAAOwL,IAAI,CAAC,AAACjG,GAAMA,EAAEkG,gBAAgB,EAAIlG,EAAEmG,SAAS,CAC/D,EAqNIC,iBAjNJ,SAA0B3J,CAAI,EAC1B,IAII1G,EAAOsQ,EAActK,EAAgBtB,EAJnC3J,EAAQ,IAAI,CAACA,KAAK,CAAEwV,EAAe,CACrCC,SAAU9J,EAAK8J,QAAQ,CACvBrQ,KAAMuG,EAAKvG,IAAI,AACnB,EAEA,GAAIuG,AAAoB,SAApBA,EAAKC,UAAU,CACf,IAAI,CAACG,eAAe,CAACJ,EAAKK,MAAM,CAAEwJ,GAClC7L,CAAAA,EAAS3J,EAAMX,GAAG,CAACsM,EAAK+J,QAAQ,CAAA,GAE5B/L,EAAOnJ,MAAM,CAACgV,EAAc,CAAA,QAG/B,GAAI7J,AAAoB,WAApBA,EAAKC,UAAU,CAEpB,CAAA,GAAIjC,AADJA,CAAAA,EAAS3J,EAAMX,GAAG,CAACsM,EAAK+J,QAAQ,CAAA,IAE5BzQ,EAAQ0E,EAAO1E,KAAK,CAChB0E,EAAOgM,YAAY,EACnBhM,EAAOgM,YAAY,CAAChV,OAAO,CAAC,AAACgV,IACzBA,EAAanL,MAAM,CAAC,CAAA,EACxB,GAEJb,EAAOa,MAAM,CAAC,CAAA,GACVoJ,GAAmBtJ,OAAO,CAACX,EAAOvE,IAAI,GAAK,GAAG,CAC9C,IAAMwQ,EAAoB,CACtBxR,OAAQa,EAAMxE,OAAO,CAAC2D,MAAM,CAC5ByR,IAAK5Q,EAAMxE,OAAO,CAACoV,GAAG,AAC1B,EACA5Q,EAAMuF,MAAM,CAAC,CAAA,GACb,IAAI,CAACsL,WAAW,CAACF,EACrB,CACJ,MAGAJ,EAAab,EAAE,CAAGhB,KAClB,IAAI,CAAC5H,eAAe,CAACJ,EAAKK,MAAM,CAAEwJ,GAClCD,EAAevV,EAAMX,GAAG,CAACmW,EAAaC,QAAQ,EAC9CxK,EAAiBoI,IAAa0C,WAAW,CAGb,KAAA,IAAjBR,GACPA,aAAyBnC,KACzBmC,AAAsC,QAAtCA,EAAaS,kBAAkB,IAE/B,CAACxC,GAA4BvI,GAAkBA,CAAc,CAACuK,EAAapQ,IAAI,CAAC,EAC5E6F,EAAegL,YAAY,EAC3BhL,EAAegL,YAAY,CAACC,aAAa,GAC7CV,CAAAA,EAAaS,YAAY,CAAG,CACxBC,cAAe,KACnB,CAAA,EAEAtC,GAAmBtJ,OAAO,CAACqB,EAAKvG,IAAI,GAAK,GAezCoQ,EAAavQ,KAAK,CAAGA,AAdrBA,CAAAA,EAAQjF,EAAMmW,OAAO,CAAC,CAClBxB,GAAIhB,KACJyC,OAAQ,EACRC,SAAU,CAAA,EACVzU,MAAO,CACH0U,KAAM,EACV,EACAC,kBAAmB,GACnBC,cAAe,CAAA,EACf/U,OAAQ,CACJgV,MAAO,OACPlR,EAAG,EACP,CACJ,EAAG,CAAA,EAAO,CAAA,EAAK,EACY9E,OAAO,CAACkU,EAAE,CACrC,IAAI,CAACmB,WAAW,IAGhBN,EAAavQ,KAAK,CAAGjF,EAAMX,GAAG,CAACsM,EAAK8J,QAAQ,EAAEhV,OAAO,CAACwE,KAAK,CAE3D4O,GAAqBvJ,OAAO,CAACqB,EAAKvG,IAAI,GAAK,GAC3CoQ,CAAAA,EAAakB,MAAM,CAACC,cAAc,CAAG3W,EAAM2J,MAAM,CAAC3H,MAAM,CAAC,SAAU2H,CAAM,EACrE,MAAOA,AAAwB,WAAxBA,EAAOlJ,OAAO,CAAC2E,IAAI,AAC9B,EAAE,CAAC,EAAE,CAAC3E,OAAO,CAACkU,EAAE,AAAD,EAEnB3U,EAAM+U,SAAS,CAACS,EAAc,CAAA,GAElC/B,GAA8B,IAAI,CAAE,iBAAkB,CAClDtJ,OAAQ,IAAI,CAACQ,qBAAqB,AACtC,GACA3K,EAAMU,MAAM,EAChB,EA+HIkW,mBA5BJ,SAA4B9X,CAAC,CAAE+X,CAAC,EAC5B,GAAI,CAACrD,GAA4B1U,IAAM,CAAC0U,GAA4BqD,IAGhE/X,EAAEoL,MAAM,GAAK2M,EAAE3M,MAAM,CAFrB,MAAO,CAAA,EAKX,IAAK,IAAID,EAAI,EAAGA,EAAInL,EAAEoL,MAAM,CAAED,IAC1B,GAAInL,CAAC,CAACmL,EAAE,GAAK4M,CAAC,CAAC5M,EAAE,CACb,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,EAgBI6M,aAjHJ,SAAsBnS,CAAC,CAAEiB,CAAU,EAC/B,IAAMnF,EAAUmF,EAAWnF,OAAO,CAACmD,WAAW,CAAEqB,EAAQyO,GAA6BjT,EAAQwE,KAAK,GAAK,IAAI,CAACjF,KAAK,CAACiF,KAAK,CAACxE,EAAQwE,KAAK,CAAC,CAClIA,GAASxE,EAAQ8F,MAAM,EACvBX,EAAWpF,MAAM,CAAC,CACdoD,YAAa,CACTQ,OAAQa,EAAMkC,OAAO,CAACxC,CAAC,CAACM,EAAM8R,KAAK,CAAG,SAAW,SAAS,EACrDtW,CAAAA,EAAQ8F,MAAM,CAAC,EAAE,CAAChB,CAAC,EAAI,CAAA,CAChC,CACJ,EAER,EAwGIyR,eAtFJ,SAAwBC,CAAU,EAC9B,OAAO,SAAUtS,CAAC,CAAEiB,CAAU,EAC1B,IAAMnF,EAAUmF,EAAWnF,OAAO,CAACmD,WAAW,CAAEmB,EAAQ2O,GAA6BjT,EAAQsE,KAAK,GAAK,IAAI,CAAC/E,KAAK,CAAC+E,KAAK,CAACtE,EAAQsE,KAAK,CAAC,CAAEE,EAAQyO,GAA6BjT,EAAQwE,KAAK,GAAK,IAAI,CAACjF,KAAK,CAACiF,KAAK,CAACxE,EAAQwE,KAAK,CAAC,CAC1NF,GAASE,IACTxE,EAAQ8F,MAAM,CAAC5F,OAAO,CAAC,CAAC0E,EAAOG,KACvBA,GAASyR,IACT5R,EAAMC,CAAC,CAAGP,EAAMoC,OAAO,CAACxC,CAAC,CAACI,EAAMgS,KAAK,CAAG,SAAW,SAAS,EAC5D1R,EAAME,CAAC,CAAGN,EAAMkC,OAAO,CAACxC,CAAC,CAACM,EAAM8R,KAAK,CAAG,SAAW,SAAS,EAEpE,GACAnR,EAAWpF,MAAM,CAAC,CACdoD,YAAa,CACT2C,OAAQ9F,EAAQ8F,MAAM,AAC1B,CACJ,GAER,CACJ,EAsEI2Q,eAxDJ,SAAwBhK,CAAK,CAAEtH,CAAU,EACrC,IAAM5F,EAAQ4F,EAAW5F,KAAK,CAAES,EAAUmF,EAAWnF,OAAO,CAACmD,WAAW,CAAEmB,EAAQ2O,GAA6BjT,EAAQsE,KAAK,GAAK/E,EAAM+E,KAAK,CAACtE,EAAQsE,KAAK,CAAC,CAAEE,EAAQyO,GAA6BjT,EAAQwE,KAAK,GAAKjF,EAAMiF,KAAK,CAACxE,EAAQwE,KAAK,CAAC,CAC9O,GAAIF,GAASE,EAAO,CAChB,IAAMK,EAAIP,EAAMoC,OAAO,CAAC+F,CAAK,CAACnI,EAAMgS,KAAK,CAAG,SAAW,SAAS,EAAGxR,EAAIN,EAAMkC,OAAO,CAAC+F,CAAK,CAACjI,EAAM8R,KAAK,CAAG,SAAW,SAAS,EAAGI,EAAQ7R,EAAI7E,EAAQ4E,KAAK,CAACC,CAAC,CAAElB,EAAS3D,EAAQ4E,KAAK,CAACE,CAAC,CAAGA,EACxLK,EAAWpF,MAAM,CAAC,CACdoD,YAAa,CACT0O,WAAY,CACR6E,MAAOnX,EAAM+F,QAAQ,CAAG3B,EAAS+S,EACjC/S,OAAQpE,EAAM+F,QAAQ,CAAGoR,EAAQ/S,CACrC,CACJ,CACJ,EACJ,CACJ,CA4CA,EAkBM,CAAEkQ,gBAAiB8C,EAAkC,CAAEtD,eAAgBuD,EAAiC,CAAErC,oBAAqBsC,EAAsC,CAAEpC,wBAAyBqC,EAA0C,CAAEjC,iBAAkBkC,EAAmC,CAAEV,aAAcW,EAA+B,CAAET,eAAgBU,EAAiC,CAAER,eAAgBS,EAAiC,CAAE,CAAGtD,GAE3b,CAAE/L,UAAWsP,EAA4B,CAAE3U,MAAO4U,EAAwB,CAAE,CAAI/X,IAchFgY,GAAqB,CAUvBC,QAAS,CAELtT,UAAW,qBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,UACTC,KAAM,cACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACwT,OAAO,CAACrS,kBAAkB,EAChF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GACrC,AACL,EASAM,aAAc,CAEVvT,UAAW,2BAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,eACTC,KAAM,cACNxB,YAAa,CACT6O,KAAM,CACFwF,UAAW,OACf,EACAlT,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACyT,YAAY,CAACtS,kBAAkB,EACrF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GACrC,AACL,EASAQ,IAAK,CAEDzT,UAAW,iBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,MACTC,KAAM,eACNxB,YAAa,CACTwB,KAAM,MACNL,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC2T,GAAG,CAACxS,kBAAkB,EAC5E,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GACrC,AACL,EASAS,SAAU,CAEN1T,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,WACTC,KAAM,eACNxB,YAAa,CACTwB,KAAM,MACNqN,KAAM,CACFwF,UAAW,OACf,EACAlT,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC4T,QAAQ,CAACzS,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GACrC,AACL,EAQAU,aAAc,CAEV3T,UAAW,2BAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,eACTC,KAAM,eACNxB,YAAa,CACTwB,KAAM,OACNL,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC6T,YAAY,CAAC1S,kBAAkB,EACrF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GACrC,AACL,EASAW,kBAAmB,CAEf5T,UAAW,iCAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,oBACTC,KAAM,eACNxB,YAAa,CACTwB,KAAM,OACNqN,KAAM,CACFwF,UAAW,OACf,EACAlT,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC8T,iBAAiB,CAClE3S,kBAAkB,EACvB,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GACrC,AACL,EAQAY,eAAgB,CAEZ7T,UAAW,6BAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,iBACTC,KAAM,eACNmT,UAAW,IACX3U,YAAa,CACTwB,KAAM,iBACNL,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAChD+T,cAAc,CAAC5S,kBAAkB,EACtC,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EAC7B,CACJ,EAQAkS,aAAc,CAEVlO,UAAW,2BAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,eACTC,KAAM,eACNmT,UAAW,IACX3U,YAAa,CACTwB,KAAM,eACNL,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACoO,YAAY,CAACjN,kBAAkB,EACrF,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EAC7B,CACJ,EAUA+X,SAAU,CAEN/T,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,WACTC,KAAM,cACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,AACL,CACJ,EAAGtF,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACiU,QAAQ,CAAC9S,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAe,SAAU,CAENhU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,WACTC,KAAM,cACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,AACL,CACJ,EAAGtF,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACkU,QAAQ,CAAC/S,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAgB,SAAU,CAENjU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,WACTC,KAAM,cACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,AACL,EACAxB,aAAc,CACVG,MAAO,CACH5C,MAAO,SACX,CACJ,CACJ,EAAGrB,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACmU,QAAQ,CAAChT,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAiB,SAAU,CAENlU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,WACTC,KAAM,cACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,AACL,EACAxB,aAAc,CACVG,MAAO,CACH5C,MAAO,SACX,CACJ,CACJ,EAAGrB,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACoU,QAAQ,CAACjT,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAkB,SAAU,CAENnU,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,UACTC,KAAM,UACNxB,YAAa,CACTiV,WAAY,IACZ9T,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBH,MAAO,CAAEC,EAAAA,EAAGC,EAAAA,CAAE,EACd4G,WAAY,CACRrI,YAAa,EACbnC,OAAQ,SACZ,EACAsK,WAAY,CACRC,QAAS,CAAA,EACTpI,YAAa,EACbnC,OAAQ,SACZ,EACA2Q,WAAY,CACR6E,MAAO,EACP/S,OAAQ,EACRN,YAAa,EACbnC,OAAQ,SACZ,CACJ,EACAoC,aAAc,CACVG,MAAO,CACH5C,MAAO,SACX,CACJ,CACJ,EAAGrB,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACqU,QAAQ,CAAClT,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACHgS,GACH,AACL,EASAmB,SAAU,CAENrU,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,UACTC,KAAM,UACNxB,YAAa,CACTiV,WAAY,IACZ9T,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBH,MAAO,CAAEC,EAAAA,EAAGC,EAAAA,CAAE,EACd4G,WAAY,CACRD,QAAS,CAAA,EACTpI,YAAa,EACbnC,OAAQ,SACZ,EACAsK,WAAY,CACRnI,YAAa,EACbnC,OAAQ,SACZ,EACA2Q,WAAY,CACR6E,MAAO,EACP/S,OAAQ,EACRN,YAAa,EACbnC,OAAQ,SACZ,CACJ,EACAoC,aAAc,CACVG,MAAO,CACH5C,MAAO,SACX,CACJ,CACJ,EAAGrB,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACuU,QAAQ,CAACpT,kBAAkB,EACjF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACHgS,GACH,AACL,EASAoB,UAAW,CAEPtU,UAAW,wBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,UACTC,KAAM,UACNxB,YAAa,CACTiV,WAAY,KACZ9T,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBH,MAAO,CAAEC,EAAAA,EAAGC,EAAAA,CAAE,EACd+M,WAAY,CACR6E,MAAO,EACP/S,OAAQ,EACRN,YAAa,EACbnC,OAAQ,SACZ,EACAwK,WAAY,CACRrI,YAAa,EACbnC,OAAQ,SACZ,EACAsK,WAAY,CACRnI,YAAa,EACbnC,OAAQ,SACZ,CACJ,EACAoC,aAAc,CACVG,MAAO,CACH5C,MAAO,SACX,CACJ,CACJ,EAAGrB,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACwU,SAAS,CAACrT,kBAAkB,EAClF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACHgS,GACH,AACL,EAaA9E,UAAW,CACPpO,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,YACTC,KAAM,YACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,AACL,EACAxB,aAAc,CACVG,MAAO,CACH5C,MAAO,SACX,CACJ,CACJ,EAAGrB,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACsO,SAAS,CAACnN,kBAAkB,EAClF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCD,GACH,AACL,EASAuB,gBAAiB,CAEbvU,UAAW,8BAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,kBACTC,KAAM,SACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,AACL,CACJ,EAAGtF,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACyU,eAAe,CAChEtT,kBAAkB,EACvB,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCD,GACH,AACL,EASA1E,UAAW,CAEPtO,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAMM,EAAIR,EAAQpC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEzC,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACvH1S,QAAS,YACTC,KAAM,YACNxB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,CAChBuW,aAAc,CACV/U,MAAO,CACH3C,KAAM,SACV,CACJ,CACJ,EACA,CAAE+D,EAAAA,EAAGC,EAAAA,CAAE,EACP,CAAED,EAAAA,EAAGC,EAAAA,CAAE,EACV,CACDgN,gBAAiB,CACbhR,KAAM,0BACV,CACJ,EACAoC,aAAc,CACVG,YAAa,CACjB,CACJ,EAAG7D,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAACwO,SAAS,CAACrN,kBAAkB,EAClF,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAEAkF,MAAO,CACH+R,GAAkC,GAClCA,GAAkC,GACrC,AACL,EAWAwB,gBAAiB,CAEbzU,UAAW,8BAGXC,MAAO,SAAUC,CAAC,EACd,IAAMuP,EAAemD,GAAkC1S,EAAG,IAAI,CAAC3E,KAAK,EAEpE,GAAI,CAACkU,EACD,MAEJ,CAAA,IAAI,CAACgF,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,EAC/C,IAAMjZ,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,kBACTC,KAAM,eACNxB,YAAa,CACTyB,MAAO,CACHC,EAAG4O,EAAa5O,CAAC,CACjBC,EAAG2O,EAAa3O,CAAC,CACjBR,MAAOmP,EAAanP,KAAK,CACzBE,MAAOiP,EAAajP,KAAK,AAC7B,EACAvB,MAAO,CACH0S,OAAQlC,EAAaE,KAAK,CAAG,GAAK,IAClCkC,KAAM,IAAI,CAAC4C,eAAe,CAACxH,QAAQ,EACvC,CACJ,EACA3N,aAAc,CACVG,MAAO,CACH5C,MAAO,UACPE,SAAU,OACd,CACJ,EACAmC,aAAc,CACVhC,OAAQ,sBACRmC,YAAa,CACjB,CACJ,EAAG7D,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAChD2U,eAAe,CAACxT,kBAAkB,EAAGE,EAAa,IAAI,CAAC5F,KAAK,CAACkF,aAAa,CAACzE,EAChF,CAAA,IAAI,CAACyY,eAAe,GACpBtT,EAAWnF,OAAO,CAACiH,MAAM,CAACwD,KAAK,CAACxL,IAAI,CAACkG,EAAY,CAAC,EACtD,CACJ,EAUAuT,WAAY,CACR1U,UAAW,yBACXC,MAAO,SAAUC,CAAC,EACd,IAAMuP,EAAemD,GAAkC1S,EAAG,IAAI,CAAC3E,KAAK,EAEpE,GAAI,CAACkU,EACD,OAEJ,IAAMjU,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,aACTC,KAAM,aACNxB,YAAa,CACTmB,MAAOmP,EAAanP,KAAK,CACzBE,MAAOiP,EAAajP,KAAK,CACzBsB,OAAQ,CAAC,CACDjB,EAAG4O,EAAa5O,CAAC,AACrB,EAAG,CACCA,EAAG4O,EAAa5O,CAAC,AACrB,EAAE,CACNmN,KAAM,CACF9Q,OAAQ,sBACRJ,KAAM,cACNuC,YAAa,CACjB,CACJ,CACJ,EAAG7D,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC4U,UAAU,CAACzT,kBAAkB,EAAGE,EAAa,IAAI,CAAC5F,KAAK,CAACkF,aAAa,CAACzE,GAE5H,OADAmF,EAAWnF,OAAO,CAACiH,MAAM,CAACwD,KAAK,CAACxL,IAAI,CAACkG,EAAY,CAAC,GAC3CA,CACX,EACAD,MAAO,CACH+R,GAAkC,GACrC,AACL,EACA0B,cAAe,CAEX3U,UAAW,4BAGXC,MAAO,SAAUC,CAAC,EACd,IAAMuP,EAAemD,GAAkC1S,EAAG,IAAI,CAAC3E,KAAK,EAEpE,GAAI,CAACkU,EACD,OAEJ,IAAMjU,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,gBACTC,KAAM,eACNxB,YAAa,CACTyB,MAAO,CACHC,EAAG4O,EAAa5O,CAAC,CACjBC,EAAG2O,EAAa3O,CAAC,CACjBR,MAAOmP,EAAanP,KAAK,CACzBE,MAAOiP,EAAajP,KAAK,AAC7B,EACAvB,MAAO,CACH0S,OAAQlC,EAAaE,KAAK,CAAG,GAAK,GACtC,CACJ,EACArQ,aAAc,CACVG,MAAO,CACH5C,MAAO,UACPE,SAAU,OACd,CACJ,EACAmC,aAAc,CACVhC,OAAQ,sBACRmC,YAAa,CACjB,CACJ,EAAG7D,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAChD6U,aAAa,CAAC1T,kBAAkB,EAAGE,EAAa,IAAI,CAAC5F,KAAK,CAACkF,aAAa,CAACzE,GAC9EmF,EAAWnF,OAAO,CAACiH,MAAM,CAACwD,KAAK,CAACxL,IAAI,CAACkG,EAAY,CAAC,EACtD,CACJ,EAYAyT,cAAe,CAEX5U,UAAW,4BAGXC,MAAO,SAAUC,CAAC,EACd,IAAMuP,EAAemD,GAAkC1S,EAAG,IAAI,CAAC3E,KAAK,EAEpE,GAAI,CAACkU,EACD,OAEJ,IAAMjU,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjF1S,QAAS,gBACTC,KAAM,eACNxB,YAAa,CACTyB,MAAO,CACHC,EAAG4O,EAAa5O,CAAC,CACjBC,EAAG2O,EAAa3O,CAAC,CACjBR,MAAOmP,EAAanP,KAAK,CACzBE,MAAOiP,EAAajP,KAAK,AAC7B,EACAvB,MAAO,CACH0S,OAAQlC,EAAaE,KAAK,CAAG,GAAK,IAClCvQ,OAAQ,GACZ,EACA6O,UAAW,CACPnR,KAAM,OACNI,OAAQuS,EAAaE,KAAK,CACtB,UACA,SACR,CACJ,EACAzQ,aAAc,CACVhC,OAAQ,sBACRmC,YAAa,CACjB,CACJ,EAAG7D,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAChD8U,aAAa,CAAC3T,kBAAkB,EAAGE,EAAa,IAAI,CAAC5F,KAAK,CAACkF,aAAa,CAACzE,GAC9EmF,EAAWnF,OAAO,CAACiH,MAAM,CAACwD,KAAK,CAACxL,IAAI,CAACkG,EAAY,CAAC,EACtD,CACJ,EASA0T,mBAAoB,CAEhB7U,UAAW,kCAGXC,MAAO,SAAUC,CAAC,EACd,GAAM,CAACG,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GAE1C,GAAI,CAACG,GAAW,CAACE,EACb,OAEJ,IAAM/E,EAAa,IAAI,CAACD,KAAK,CAACS,OAAO,CAACR,UAAU,CAAEQ,EAAUoX,GAAyB,CACjFzS,KAAM,qBACND,QAAS,qBACTvB,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGR,EAAQpC,KAAK,AACpB,EAAE,AACV,CACJ,EAAGzC,EAAWyF,kBAAkB,CAAEzF,EAAWsE,QAAQ,CAAC+U,kBAAkB,CACnE5T,kBAAkB,EACvB,OAAO,IAAI,CAAC1F,KAAK,CAACkF,aAAa,CAACzE,EACpC,EAGAkF,MAAO,CACH,SAAUhB,CAAC,CAAEiB,CAAU,EACnB,IAAME,EAAgBF,EAAWnF,OAAO,CAACmD,WAAW,CAAC2C,MAAM,CAAEjB,EAAIQ,GAAiBA,CAAa,CAAC,EAAE,CAACR,CAAC,CAAE,CAACR,EAASE,EAAQ,CAAG,IAAI,CAAC+H,SAAS,CAACpI,GACtIG,GAAWE,GACXY,EAAWpF,MAAM,CAAC,CACdoD,YAAa,CACTmB,MAAOD,EAAQ3C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGA,CACP,EAAG,CACCA,EAAGR,EAAQpC,KAAK,AACpB,EAAE,AACV,CACJ,EAER,EACH,AACL,EAUA6W,cAAe,CAEX9U,UAAW,4BAEXC,MAAO0S,GAAmC,YAC9C,EASAoC,eAAgB,CAEZ/U,UAAW,6BAEXC,MAAO0S,GAAmC,OAC9C,EAUAqC,cAAe,CAEXhV,UAAW,4BAEXC,MAAO0S,GAAmC,YAC9C,EAUAsC,cAAe,CAEXjV,UAAW,4BAEXC,MAAO0S,GAAmC,QAC9C,EAUAuC,MAAO,CAEHlV,UAAW,oBAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAACQ,MAAM,CAAC,CACdR,MAAO,CACH4Z,QAAS,CACLxU,KAAM,GACV,CACJ,CACJ,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EASA0P,MAAO,CAEHpV,UAAW,oBAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAACQ,MAAM,CAAC,CACdR,MAAO,CACH4Z,QAAS,CACLxU,KAAM,GACV,CACJ,CACJ,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EASA2P,OAAQ,CAEJrV,UAAW,qBAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAACQ,MAAM,CAAC,CACdR,MAAO,CACH4Z,QAAS,CACLxU,KAAM,IACV,CACJ,CACJ,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAQA4P,eAAgB,CAEZtV,UAAW,8BAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAAC2J,MAAM,CAAC,EAAE,CAACnJ,MAAM,CAAC,CACxB4E,KAAM,OACN4U,YAAa,CAAA,CACjB,GACApC,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAQA8P,eAAgB,CAEZxV,UAAW,8BAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAAC2J,MAAM,CAAC,EAAE,CAACnJ,MAAM,CAAC,CACxB4E,KAAM,MACV,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAQA+P,sBAAuB,CAEnBzV,UAAW,qCAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAAC2J,MAAM,CAAC,EAAE,CAACnJ,MAAM,CAAC,CACxB4E,KAAM,aACV,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAQAgQ,qBAAsB,CAElB1V,UAAW,oCAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAAC2J,MAAM,CAAC,EAAE,CAACnJ,MAAM,CAAC,CACxB4E,KAAM,YACV,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAQAiQ,cAAe,CACX3V,UAAW,6BACXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAAC2J,MAAM,CAAC,EAAE,CAACnJ,MAAM,CAAC,CACxB4E,KAAM,MACN4U,YAAa,CAAA,CACjB,GACApC,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAAA,CAAO,EAClE,CACJ,EAQAkQ,4BAA6B,CAEzB5V,UAAW,2CAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAI,CAACnK,KAAK,CAAC2J,MAAM,CAAC,EAAE,CAACnJ,MAAM,CAAC,CACxB4E,KAAM,mBACV,GACAwS,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAUAmQ,WAAY,CAER7V,UAAW,yBACX4F,YAAa,SAEbsE,KAAM,SAAUxE,CAAM,EACd,IAAI,CAACnK,KAAK,CAACua,UAAU,EACrB,IAAI,CAACva,KAAK,CAACua,UAAU,CAACC,MAAM,GAEhC5C,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAUAsQ,sBAAuB,CAEnBhW,UAAW,qCAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAMnK,EAAQ,IAAI,CAACA,KAAK,CAAE2J,EAAS3J,EAAM2J,MAAM,CAAE+Q,EAAM1a,EAAM6O,UAAU,CAAE8L,EAAwBpD,GAA2CvX,EAAM2J,MAAM,EACpJ+Q,GAAOA,EAAI5F,UAAU,GACrBnL,EAAOhJ,OAAO,CAAC,SAAUgJ,CAAM,EAC3BA,EAAOnJ,MAAM,CAAC,CACV6U,UAAW,CAAEnJ,QAAS,CAACyO,CAAsB,EAC7CvF,iBAAkB,CACdlJ,QAAS,CAACyO,EACVjX,MAAO,CAAEwI,QAAS,CAAA,CAAK,CAC3B,CACJ,EAAG,CAAA,EACP,GACAlM,EAAMU,MAAM,IAEhBkX,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAaAyQ,WAAY,CAERnW,UAAW,wBAGXkK,KAAM,WACF,IAAM1O,EAAa,IAAI,CACvB2X,GAA6B3X,EAAY,YAAa,CAClDuL,SAAU,aACV/K,QAAS,CAAC,EAEViL,SAAU,SAAUC,CAAI,EACpB6L,GAAoC9X,IAAI,CAACO,EAAY0L,EACzD,CACJ,EACJ,CACJ,EAQAkP,kBAAmB,CAEfpW,UAAW,gCAGXkK,KAAM,SAAUxE,CAAM,EAClB,IAAMnK,EAAQ,IAAI,CAACA,KAAK,CAAE0a,EAAM1a,EAAM6O,UAAU,CAAEiM,EAAWJ,EAAIK,WAAW,EAC5E,CAAA,IAAI,CAACC,kBAAkB,CAAG,CAAC,IAAI,CAACA,kBAAkB,CAClD,AAAChb,CAAAA,EAAMib,WAAW,EAAI,EAAE,AAAD,EAAGta,OAAO,CAAC,SAAUiF,CAAU,EAClDA,EAAWsV,aAAa,CAAC,CAAC,IAAI,CAACF,kBAAkB,CACrD,EAAG,IAAI,EACHN,GAAOA,EAAI5F,UAAU,GACjB,IAAI,CAACkG,kBAAkB,CACvB7Q,EAAOgR,UAAU,CAACjX,KAAK,CAAC,mBAAmB,CACvC,QAAU4W,EACN,2BAGR3Q,EAAOgR,UAAU,CAACjX,KAAK,CAAC,mBAAmB,CACvC,QAAU4W,EACN,6BAGhBlD,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,EAYAiR,UAAW,CAEP3W,UAAW,wBACX4F,YAAa,SAGbsE,KAAM,SAAUxE,CAAM,EAClB,IAAyBnK,EAAQC,AAAd,IAAI,CAAqBD,KAAK,CAAEib,EAAc,EAAE,CAAEL,EAAa,EAAE,CAAES,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAC5Gtb,EAAMib,WAAW,CAACta,OAAO,CAAC,SAAUiF,CAAU,CAAEJ,CAAK,EACjDyV,CAAW,CAACzV,EAAM,CAAGI,EAAWqP,WAAW,AAC/C,GACAjV,EAAM2J,MAAM,CAAChJ,OAAO,CAAC,SAAUgJ,CAAM,EAC7BA,EAAO4R,EAAE,CAAC,OACVX,EAAWra,IAAI,CAACoJ,EAAOsL,WAAW,EAEb,UAAhBtL,EAAOvE,IAAI,EAChBiW,EAAM9a,IAAI,CAACoJ,EAAOsL,WAAW,CAErC,GACAjV,EAAMiF,KAAK,CAACtE,OAAO,CAAC,SAAUsE,CAAK,EAC3BqS,GAAuCrS,IACvCqW,EAAM/a,IAAI,CAAC0E,EAAMxE,OAAO,CAEhC,GACAX,IAA8EkI,GAAG,CAACwT,YAAY,CAACC,OAAO,CAAC,mBAAoBC,KAAKC,SAAS,CAAC,CACtIV,YAAaA,EACbL,WAAYA,EACZS,MAAOA,EACPC,MAAOA,CACX,IACA1D,GAA6B,IAAI,CAAE,iBAAkB,CAAEzN,OAAQA,CAAO,EAC1E,CACJ,CACJ,EAirCMyR,GAAqB,CACvBzY,KArpC4B,CAQ5B0L,WAAY,CACR6L,IAAK,CAEDrX,aAAc,gBACdC,MAAO,QACPuY,aAAc,gBACdjJ,QAAS,UACTkJ,SAAU,WACVjB,kBAAmB,qBACnBkB,eAAgB,kBAChBV,MAAO,QACPW,WAAY,cACZC,WAAY,cACZb,UAAW,aACXR,WAAY,aACZH,sBAAuB,2BAEvBd,MAAO,SACPE,MAAO,SACPC,OAAQ,WACRQ,WAAY,aACZ4B,SAAU,OACVC,SAAU,OACVC,gBAAiB,cACjBC,QAAS,MACTC,sBAAuB,qBACvBC,eAAgB,cAEhBhZ,OAAQ,SACRC,QAAS,UACTE,MAAO,QACPD,UAAW,YAEX8V,cAAe,cACfC,eAAgB,eAChBC,cAAe,cACfC,cAAe,cAEfX,UAAW,aACXH,SAAU,YACVE,SAAU,YAEVf,QAAS,UACTC,aAAc,gBACdE,IAAK,MACLC,SAAU,YACV1F,KAAM,OACN4F,kBAAmB,aACnBC,eAAgB,kBAChB3F,aAAc,gBACdyF,aAAc,gBAEdI,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBAEVO,gBAAiB,mBACjBE,cAAe,iBACfC,cAAe,iBAEfxG,UAAW,YACXyG,mBAAoB,uBACpBvG,UAAW,YACXiG,gBAAiB,mBACjBG,WAAY,aAChB,CACJ,EACAlZ,WAAY,CACRmD,MAAO,CAEHG,OAAQ,SACRC,QAAS,UACTC,UAAW,YACXC,MAAO,QACPqU,QAAS,UACTC,aAAc,gBACdE,IAAK,MACLC,SAAU,YACV1F,KAAM,OACN4F,kBAAmB,aACnBC,eAAgB,kBAChB3F,aAAc,gBACd6F,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBACVO,gBAAiB,mBACjBE,cAAe,iBACfC,cAAe,iBACfxG,UAAW,YACXyG,mBAAoB,uBACpBvG,UAAW,YACXiG,gBAAiB,mBACjBZ,aAAc,gBACdxF,QAAS,UACTmG,UAAW,aACXH,SAAU,YACVE,SAAU,YACVK,WAAY,cAEZkC,MAAO,QAEPmB,UAAW,MACXC,WAAY,OACZC,WAAY,OACZC,aAAc,SACdhT,OAAQ,SACRiT,OAAQ,SACRlK,UAAW,YAEXH,gBAAiB,mBACjBC,gBAAiB,mBACjBrG,WAAY,cACZF,WAAY,cACZ6G,OAAQ,SACRR,WAAY,aAEZuK,cAAe,WAEfC,iBAAkB,oBAClBC,YAAa,iBACbvX,MAAO,QACPwX,OAAQ,SACRC,QAAS,UACTC,kBAAmB,qBACnBC,aAAc,gBACdC,kBAAmB,uBACnBC,UAAW,aACXC,cAAe,iBACfC,YAAa,eACbC,WAAY,cACZC,aAAc,gBACdC,SAAU,WACVC,UAAW,YACXC,QAAS,WACTC,WAAY,cACZC,0BAA2B,8BAC3BC,sBAAuB,0BACvBC,UAAW,YACXC,WAAY,aACZC,OAAQ,SACRC,UAAW,aACXC,SAAU,YACVC,UAAW,YACXC,UAAW,cACXC,OAAQ,SACRC,cAAe,sBACfC,cAAe,sBACfC,QAAS,UAOTC,iBAAkB,CAQdC,OAAQ,CAAC,qBAAqB,CAO9BC,GAAI,CAAC,kBAAkB,CAOvBC,KAAM,CAAC,oCAAoC,CAO3CC,IAAK,CAAC,6BAA6B,CAOnCC,IAAK,CAAC,qBAAqB,CAO3BC,gBAAiB,CAAC,mBAAmB,CAOrCC,iBAAkB,CAAC,oBAAoB,CAOvCC,YAAa,CAAC,eAAe,CAO7BC,GAAI,CAAC,gBAAgB,CAOrBC,eAAgB,CAAC,kBAAkB,CAOnCC,KAAM,CAAC,gBAAgB,CAOvBC,IAAK,CAAC,wBAAwB,CAO9BC,WAAY,CAAC,cAAc,CAO3BC,KAAM,CAAC,oCAAoC,CAO3CC,IAAK,CAAC,kBAAkB,CAOxBC,KAAM,CAAC,iCAAiC,CAOxCC,IAAK,CAAC,0BAA0B,CAOhCC,OAAQ,CAAC,UAAU,CAQnBC,IAAK,CAAC,2BAA2B,CAOjCC,GAAI,CAAC,4BAA4B,CAOjCC,MAAO,CAAC,QAAQ,CAOhBC,gBAAiB,CAAC,mBAAmB,CAOrCC,IAAK,CAAC,qBAAqB,CAO3BC,GAAI,CAAC,qBAAqB,CAO1BC,IAAK,CAAC,0BAA0B,CAOhCC,QAAS,CAAC,UAAU,CAOpBC,IAAK,CAAC,qBAAqB,CAO3BC,IAAK,CAAC,6BAA6B,CAOnCC,eAAgB,CAAC,kBAAkB,CAOnCC,IAAK,CAAC,6BAA6B,CAOnCC,IAAK,CAAC,6BAA6B,CAOnCC,QAAS,CAAC,qBAAqB,CAO/BC,sBAAuB,CAAC,0BAA0B,CAOlDC,0BAA2B,CAAC,8BAA8B,CAO1DC,sBAAuB,CAAC,0BAA0B,CAOlDC,KAAM,CAAC,wCAAwC,CAO/CC,IAAK,CAAC,mBAAmB,CAOzBC,SAAU,CAAC,WAAW,CAOtBC,KAAM,CAAC,gCAAgC,CAOvCC,IAAK,CAAC,oBAAoB,CAO1BC,IAAK,CAAC,8BAA8B,CAOpCC,IAAK,CAAC,iBAAiB,CAOvBC,IAAK,CAAC,0BAA0B,CAOhCC,eAAgB,CAAC,kBAAkB,CAOnCC,WAAY,CAAC,aAAa,CAO1BC,KAAM,CAAC,OAAO,CAOdC,UAAW,CAAC,cAAc,AAC9B,CACJ,CACJ,CACJ,EAmqBI7S,WAppBe,CAIf6L,IAAK,CAeDxO,QAAS,CAAA,EAKTzH,UAAW,8BAKXkd,iBAAkB,qBA4BlBC,QAAS,CACL,aACA,YACA,eACA,QACA,eACA,UACA,WACA,oBACA,YACA,iBACA,QACA,YACA,aACA,aACA,aACA,YACA,wBACA,YACH,CAKDC,YAAa,CACTC,UAAW,CACPC,YAAa,OAIbC,OAAQ,eACZ,EACA3e,aAAc,CAcV4e,MAAO,CACH,QACA,SACA,UACA,YACH,CACD1e,OAAQ,CAOJye,OAAQ,YACZ,EACAxe,QAAS,CAOLwe,OAAQ,aACZ,EACAve,UAAW,CAOPue,OAAQ,eACZ,EACAte,MAAO,CAOHse,OAAQ,WACZ,CACJ,EACA3G,MAAO,CAcH4G,MAAO,CACH,gBACA,iBACA,gBACA,gBACH,CACDvI,cAAe,CAOXsI,OAAQ,gBACZ,EACAxI,eAAgB,CAOZwI,OAAQ,kBACZ,EACAvI,cAAe,CAMXuI,OAAQ,kBACZ,EACAzI,cAAe,CAMXyI,OAAQ,iBACZ,CACJ,EACA1e,MAAO,CAiBH2e,MAAO,CACH,UACA,eACA,MACA,WACA,OACA,oBACA,iBACA,eACH,CACDlK,QAAS,CAMLiK,OAAQ,aACZ,EACAhK,aAAc,CAMVgK,OAAQ,mBACZ,EACA9J,IAAK,CAMD8J,OAAQ,SACZ,EACA7J,SAAU,CAMN6J,OAAQ,eACZ,EACAvP,KAAM,CAMFuP,OAAQ,UACZ,EACA3J,kBAAmB,CAMf2J,OAAQ,gBACZ,EACArP,aAAc,CAMVqP,OAAQ,mBACZ,EACA1J,eAAgB,CAMZ0J,OAAQ,qBACZ,CACJ,EACAnG,aAAc,CAcVoG,MAAO,CACH,WACA,WACA,WACA,WACH,CACDzJ,SAAU,CAMNwJ,OAAQ,eACZ,EACAvJ,SAAU,CAMNuJ,OAAQ,eACZ,EACAtJ,SAAU,CAMNsJ,OAAQ,eACZ,EACArJ,SAAU,CAMNqJ,OAAQ,eACZ,CACJ,EACAjG,eAAgB,CAYZkG,MAAO,CACH,kBACA,gBACA,gBACH,CACD/I,gBAAiB,CAMb8I,OAAQ,sBACZ,EACA5I,cAAe,CAMX4I,OAAQ,oBACZ,EACA3I,cAAe,CAMX2I,OAAQ,oBACZ,CACJ,EACAlG,SAAU,CAcNmG,MAAO,CACH,YACA,qBACA,YACA,kBACA,aACH,CACDlP,UAAW,CAMPiP,OAAQ,eACZ,EACAnP,UAAW,CAMPmP,OAAQ,eACZ,EACA1I,mBAAoB,CAMhB0I,OAAQ,wBACZ,EACAhJ,gBAAiB,CAMbgJ,OAAQ,sBACZ,EACA7I,WAAY,CAMR6I,OAAQ,iBACZ,CACJ,EACApP,QAAS,CAYLqP,MAAO,CACH,YACA,WACA,WACH,CACDrJ,SAAU,CAMNoJ,OAAQ,eACZ,EACAlJ,SAAU,CAMNkJ,OAAQ,eACZ,EACAjJ,UAAW,CAMPiJ,OAAQ,gBACZ,CACJ,EACAnH,kBAAmB,CAMfmH,OAAQ,yBACZ,EACAvH,sBAAuB,CAMnBuH,OAAQ,wBACZ,EACApH,WAAY,CAMRoH,OAAQ,gBACZ,EACAhG,WAAY,CAYRiG,MAAO,CACH,QACA,QACA,SACH,CACDtI,MAAO,CAMHqI,OAAQ,YACZ,EACAnI,MAAO,CAMHmI,OAAQ,YACZ,EACAlI,OAAQ,CAMJkI,OAAQ,aACZ,CACJ,EACA/F,WAAY,CAaRgG,MAAO,CACH,WACA,WACA,kBACA,wBACA,UACA,iBACH,CACD/F,SAAU,CAMN8F,OAAQ,iBACZ,EACA7F,SAAU,CAMN6F,OAAQ,iBACZ,EACA5F,gBAAiB,CAMb4F,OAAQ,wBACZ,EACA3F,QAAS,CAML2F,OAAQ,gBACZ,EACAzF,eAAgB,CAMZyF,OAAQ,wBACZ,EACA1F,sBAAuB,CAMnB0F,OAAQ,+BACZ,CACJ,EACA1H,WAAY,CAMR0H,OAAQ,gBACZ,EACA5G,UAAW,CAMP4G,OAAQ,gBACZ,CACJ,EAMApY,QAAS,CAAA,CACb,CACJ,CASA,EAiBM,CAAE/B,WAAYqa,EAAqB,CAAE,CAAIpiB,IAEzC,CAAEgC,gBAAiBqgB,EAA0B,CAAE,CAAGtgB,EAIlD,CAAEmT,oBAAqBoN,EAA8B,CAAElN,wBAAyBmN,EAAkC,CAAE,CAAGhO,GAEvH,CAAEiO,aAAAA,EAAY,CAAEvhB,QAASwhB,EAAkB,CAAEvhB,SAAUwhB,EAAmB,CAAEvhB,KAAMwhB,EAAe,CAAE,CAAI3iB,IAwD7G,SAAS4iB,GAA4BpH,CAAK,CAAEqH,CAAU,CAAEC,CAAa,CAAEhN,CAAiB,EACpF,IAAIiN,EAAgB,EAAGC,EAAoBC,EAAeC,EAE1D,SAASC,EAAa1jB,CAAI,EACtB,OAAOgjB,GAAmBhjB,IAAS,CAACijB,GAAoBjjB,IAASA,EAAKiR,KAAK,CAAC,IAChF,CAuCA,OAtCIoF,IACAoN,EAAaV,GAAclS,WAAWwF,EAAkBC,GAAG,EAAI,KAC/DkN,EAAgBT,GAAclS,WAAWwF,EAAkBxR,MAAM,EAAI,MAoClE,CAAE8e,UAlCS5H,EAAMnJ,GAAG,CAAC,CAAClN,EAAOO,KAChC,IAAIpB,EAASke,GAAaW,EAAahe,EAAMxE,OAAO,CAAC2D,MAAM,EACvDgM,WAAWnL,EAAMxE,OAAO,CAAC2D,MAAM,EAAI,IACnCa,EAAMb,MAAM,CAAGue,GAAa9M,EAAMyM,GAAaW,EAAahe,EAAMxE,OAAO,CAACoV,GAAG,EAC7EzF,WAAWnL,EAAMxE,OAAO,CAACoV,GAAG,EAAI,IAChC,AAAC5Q,CAAAA,EAAM4Q,GAAG,CAAG5Q,EAAMjF,KAAK,CAAC4N,OAAO,AAAD,EAAK+U,GAwBxC,OAvBKI,GAkBGlN,EAAMmN,GACNnN,CAAAA,GAAOkN,CAAY,EAEvBF,EAAgB5c,KAAKzD,GAAG,CAACqgB,EAAe,AAAChN,CAAAA,GAAO,CAAA,EAAMzR,CAAAA,GAAU,CAAA,KAlB3Doe,GAAoBpe,IAGrBA,CAAAA,EAASkX,CAAK,CAAC9V,EAAQ,EAAE,CAACmE,MAAM,CAC3BwZ,KAAK,CAAC,AAACjU,GAAMA,EAAEqM,EAAE,CAAC,QACnBuH,EAAqBF,EAAgB,GAAE,EAE1CJ,GAAoB3M,IACrBA,CAAAA,EAAMgN,CAAY,EAEtBC,EAAqB1e,EACrBye,EAAgBP,GAAarc,KAAKzD,GAAG,CAACqgB,EAAe,AAAChN,CAAAA,GAAO,CAAA,EAAMzR,CAAAA,GAAU,CAAA,KAS1E,CACHA,OAAQA,AAAS,IAATA,EACRyR,IAAKA,AAAM,IAANA,CACT,CACJ,GACoBgN,cAAAA,CAAc,CACtC,CAiBA,SAASO,GAA2B9H,CAAK,EACrC,IAAM+H,EAAW,EAAE,CAqBnB,OApBA/H,EAAM3a,OAAO,CAAC,SAAU2iB,CAAM,CAAE9d,CAAK,EACjC,IAAM+d,EAAYjI,CAAK,CAAC9V,EAAQ,EAAE,CAE9B+d,EACAF,CAAQ,CAAC7d,EAAM,CAAG,CACd0G,QAAS,CAAA,EACTsX,eAAgB,CACZC,KAAM,CACFhB,GAAgBc,EAAU9iB,OAAO,CAACkU,EAAE,CAAE4O,EAAU/d,KAAK,EACxD,AACL,CACJ,EAIA6d,CAAQ,CAAC7d,EAAM,CAAG,CACd0G,QAAS,CAAA,CACb,CAER,GACOmX,CACX,CAmBA,SAASK,GAAoCR,CAAS,CAAES,CAAY,CAAEC,CAAY,CAAEC,CAAK,EASrF,OARAX,EAAUviB,OAAO,CAAC,SAAU+F,CAAQ,CAAElB,CAAK,EACvC,IAAMse,EAAeZ,CAAS,CAAC1d,EAAQ,EAAE,AACzCkB,CAAAA,EAASmP,GAAG,CAAG,AAACiO,EACZxB,GAAawB,EAAa1f,MAAM,CAAG0f,EAAajO,GAAG,EADxB,EAE3B+N,GACAld,CAAAA,EAAStC,MAAM,CAAGke,GAAa5b,EAAStC,MAAM,CAAGyf,EAAQF,EAAY,CAE7E,GACOT,CACX,CAYA,SAASa,GAAsBnO,CAAiB,EAG5C,IAAM5V,EAAQ,IAAI,CAACA,KAAK,CAExBsb,EAAQtb,EAAMiF,KAAK,CAACjD,MAAM,CAACogB,IAAiCO,EAAa3iB,EAAM2iB,UAAU,CAEzF,CAAEO,UAAAA,CAAS,CAAEL,cAAAA,CAAa,CAAE,CAAG,IAAI,CAACmB,iBAAiB,CAAC1I,EAAOqH,EALvC,GAKkE/M,GAAoByN,EAAW,IAAI,CAACY,gBAAgB,CAAC3I,EAIzI,EAAC1F,GACDiN,GAAiBP,GAAa,GAC9BY,CAAS,CAACA,EAAUhZ,MAAM,CAAG,EAAE,CAAG,CAC9B9F,OAZc,GAadyR,IAAKyM,GAAaO,AAAgB,IAAhBA,EAbJ,GAclB,EAGAK,EAAUviB,OAAO,CAAC,SAAU+F,CAAQ,EAChCA,EAAStC,MAAM,CAAG,AAACsC,EAAStC,MAAM,CAAIye,CAAAA,AAAgB,IAAhBA,CAAkB,EAAM,IAC9Dnc,EAASmP,GAAG,CAAG,AAACnP,EAASmP,GAAG,CAAIgN,CAAAA,AAAgB,IAAhBA,CAAkB,EAAM,GAC5D,GAEJK,EAAUviB,OAAO,CAAC,SAAU+F,CAAQ,CAAElB,CAAK,EACvC8V,CAAK,CAAC9V,EAAM,CAAChF,MAAM,CAAC,CAChB4D,OAAQsC,EAAStC,MAAM,CAAG,IAC1ByR,IAAKnP,EAASmP,GAAG,CAAG,IACpBqO,OAAQb,CAAQ,CAAC7d,EAAM,CACvB4Q,OAAQ,CACZ,EAAG,CAAA,EACP,EACJ,CAYA,IAAI+N,GAAuF7lB,EAAoB,KAC3G8lB,GAA2G9lB,EAAoBI,CAAC,CAACylB,IAiBrI,GAAM,CAAEhc,SAAUkc,EAAqB,CAAEC,cAAAA,EAAa,CAAEC,IAAAA,EAAG,CAAExjB,QAASyjB,EAAoB,CAAElc,UAAWmc,EAAsB,CAAEC,SAAAA,EAAQ,CAAEnc,QAASoc,EAAoB,CAAE1hB,MAAO2hB,EAAkB,CAAE3jB,KAAM4jB,EAAiB,CAAE,CAAI/kB,IAC1N,CAAE8W,mBAAoBkO,EAA+B,CAAE,CAAGzQ,EAqBhE,OAAM0Q,GAMF5kB,YAAYM,CAAO,CAAEukB,CAAW,CAAEhlB,CAAK,CAAE,CACrC,IAAI,CAACmX,KAAK,CAAG,EACb,IAAI,CAAC8N,OAAO,CAAG,CAAA,EACf,IAAI,CAACjlB,KAAK,CAAGA,EACb,IAAI,CAACS,OAAO,CAAGA,EACf,IAAI,CAAC0C,IAAI,CAAG6hB,EAEZ,IAAI,CAAClK,QAAQ,CAAG,IAAI,CAACC,WAAW,GAChC,IAAI,CAACjG,UAAU,CAAGrU,EAAQyL,OAAO,CACjC,IAAI,CAACtC,OAAO,CAAGib,GAAkBpkB,EAAQmJ,OAAO,CAAE,CAAA,GAClD,IAAI,CAACsb,YAAY,CAAGzkB,EAAQgE,SAAS,CACrC,IAAI,CAACkd,gBAAgB,CAAGlhB,EAAQkhB,gBAAgB,CAGhD,IAAI,CAAC9U,cAAc,CAAG,EAAE,CACpB,IAAI,CAACiI,UAAU,GACf,IAAI,CAACqQ,eAAe,GACpB,IAAI,CAACC,aAAa,GAClB,IAAI,CAACC,kBAAkB,IAE3BZ,GAAuB,IAAI,CAAE,YACjC,CAUAW,eAAgB,CACZ,IAAMjiB,EAAO,IAAI,CAACA,IAAI,CAAEmiB,EAAa,IAAI,CAAC7kB,OAAO,CAAE8T,EAAU,IAAI,CAACA,OAAO,CAAEqN,EAAU0D,EAAW1D,OAAO,CAAE2D,EAAOD,EAAWzD,WAAW,CAAE2D,EAAajR,EAAQkR,UAAU,AACvK,CAAA,IAAI,CAACC,UAAU,CAAG9D,EAElBA,EAAQjhB,OAAO,CAAC,AAACglB,IACb,IAAMxb,EAAS,IAAI,CAACqS,SAAS,CAACjI,EAASgR,EAAMI,EAASxiB,GACtD,IAAI,CAAC0J,cAAc,CAACtM,IAAI,CAAC8jB,GAAsBla,EAAOyb,aAAa,CAAE,QAAS,IAAM,IAAI,CAACC,kBAAkB,CAACL,EAAYrb,EAAOyb,aAAa,IACxIjB,GAAqBY,CAAI,CAACI,EAAQ,CAAC1D,KAAK,GAExC,IAAI,CAAC6D,UAAU,CAAC3b,EAAQob,CAAI,CAACI,EAAQ,CAE7C,EACJ,CAaAG,WAAWC,CAAS,CAAE5b,CAAM,CAAE,CAC1B,IAAM6b,EAAeD,EAAUC,YAAY,CAAEJ,EAAgBG,EAAUH,aAAa,CAAEK,EAAcvB,GAASkB,EAAe,SAAUM,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAc,IAAI,CAACC,WAAW,CAAEZ,EAAa,IAAI,CAACjR,OAAO,CAACkR,UAAU,CAElOY,EAAiB,IAAI,CAACC,OAAO,CAAGhC,GAAc,KAAM,CAChD7f,UAAW,4BACf,EAAG,KAAK,EAAGmhB,GAEX,IAAI,CAACW,eAAe,CAACX,EAAezb,GAEpC,IAAI,CAAC0C,cAAc,CAACtM,IAAI,CAAC8jB,GAAsB2B,EAAc,QAAS,AAACrhB,IAKnE,GAJAA,EAAE6hB,eAAe,GAEjB,IAAI,CAACX,kBAAkB,CAACL,EAAYI,GAEhCA,EAAcnhB,SAAS,CACtB6F,OAAO,CAAC,uBAAyB,EAClC6b,EAAYjiB,KAAK,CAACiT,KAAK,CACnBgP,EAAYM,UAAU,CAAG,KAC7Bb,EAAcrb,SAAS,CAACC,MAAM,CAAC,sBAC/B6b,EAAeniB,KAAK,CAACwiB,OAAO,CAAG,WAE9B,CAGDL,EAAeniB,KAAK,CAACwiB,OAAO,CAAG,QAC/B,IAAIC,EAAYN,EAAeO,YAAY,CACvChB,EAAcgB,YAAY,CAAG,CAK/BP,CAAAA,EAAeO,YAAY,CACzBhB,EAAciB,SAAS,CACvBX,EAAQU,YAAY,EAEpBhB,EAAciB,SAAS,CAAGF,GAC1BA,CAAAA,EAAY,CAAA,EAGhBpC,GAAI8B,EAAgB,CAChBxQ,IAAK,CAAC8Q,EAAY,KAClBG,KAAMb,EAAc,EAAI,IAC5B,GACAL,EAAcnhB,SAAS,EAAI,sBAC3B0hB,EAAYM,UAAU,CAAGP,EAAQa,WAAW,CAC5CZ,EAAYjiB,KAAK,CAACiT,KAAK,CAAGgP,EAAYM,UAAU,CAC5C/B,GAASyB,EAAa,gBACtBE,EAAeU,WAAW,CAAG,EAAI,IACzC,CACJ,GACJ,CAYAR,gBAAgBX,CAAa,CAAEzb,CAAM,CAAE,CACnC,IACI6c,EADEC,EAAQ,IAAI,CAAEZ,EAAiB,IAAI,CAACC,OAAO,CAAEnjB,EAAO,IAAI,CAACA,IAAI,CAAEgjB,EAAc,IAAI,CAACC,WAAW,CAGnGnE,AAH6G9X,EAAO8X,KAAK,CAGnHthB,OAAO,CAAC,AAACglB,IAEXqB,EAAa,IAAI,CAACxK,SAAS,CAAC6J,EAAgBlc,EAAQwb,EAASxiB,GAC7D,IAAI,CAAC0J,cAAc,CAACtM,IAAI,CAAC8jB,GAAsB2C,EAAWE,UAAU,CAAE,QAAS,WAC3ED,EAAME,YAAY,CAAC,IAAI,CAAEvB,EAAe,CAAA,GACxCO,EAAYjiB,KAAK,CAACiT,KAAK,CACnBgP,EAAYM,UAAU,CAAG,KAC7BJ,EAAeniB,KAAK,CAACwiB,OAAO,CAAG,MACnC,GACJ,GAEA,IAAMU,EAAmBf,EAAerc,gBAAgB,CAAC,iCAAiC,CAAC,EAAE,CAE7F,IAAI,CAACmd,YAAY,CAACC,EAAkB,CAAA,EACxC,CAKAvB,mBAAmBjE,CAAO,CAAEyF,CAAa,CAAEC,CAAY,CAAE,CACrD,EAAE,CAAC3mB,OAAO,CAACjB,IAAI,CAACkiB,EAAS,AAAC2F,IAClBA,IAAQF,IACRE,EAAIhd,SAAS,CAACC,MAAM,CAAC,sBACrB+c,EAAIhd,SAAS,CAACC,MAAM,CAAC,qBAIjB8c,AAHJA,CAAAA,EACIC,EAAIvd,gBAAgB,CAAC,8BAA6B,EAErCE,MAAM,CAAG,GACtBod,CAAAA,CAAY,CAAC,EAAE,CAACpjB,KAAK,CAACwiB,OAAO,CAAG,MAAK,EAGjD,EACJ,CAsBAlK,UAAU/V,CAAM,CAAEhG,CAAO,CAAEklB,CAAO,CAAExiB,EAAO,CAAC,CAAC,CAAE,CAC3C,IAAMqkB,EAAa/mB,CAAO,CAACklB,EAAQ,CAAE1D,EAAQuF,EAAWvF,KAAK,CAAEwF,EAAe1C,GAAQvlB,SAAS,CAACioB,YAAY,CAAEC,EAAgBF,EAAW/iB,SAAS,EAAI,GAEhJmhB,EAAgBtB,GAAc,KAAM,CACtC7f,UAAWogB,GAAkB4C,CAAY,CAAC9B,EAAQ,CAAE,IAAM,IAAM+B,EAChE9lB,MAAOuB,CAAI,CAACwiB,EAAQ,EAAIA,CAC5B,EAAG,KAAK,EAAGlf,GAGLygB,EAAa5C,GADEkD,EAAWzF,WAAW,EAAI,SACD,CAC1Ctd,UAAW,0BACf,EAAG,KAAK,EAAGmhB,GAEX,GAAI3D,GAASA,EAAM/X,MAAM,CAAE,CAEvB,IAAM8b,EAAe1B,GAAc,SAAU,CACzC7f,UAAW,sDAEf,EAAG,KAAK,EAAGmhB,GAGX,OAFAI,EAAa9hB,KAAK,CAACyjB,eAAe,CAAG,OACjC,IAAI,CAAC7M,QAAQ,CAAG,oBACb,CACH8K,cAAAA,EACAsB,WAAAA,EACAlB,aAAAA,CACJ,CACJ,CAGA,OAFAkB,EAAWhjB,KAAK,CAACyjB,eAAe,CAAG,OAC/B,IAAI,CAAC7M,QAAQ,CAAG0M,EAAWxF,MAAM,CAAG,IACjC,CACH4D,cAAAA,EACAsB,WAAAA,CACJ,CACJ,CAKAU,eAAgB,CACZ,IAAM1B,EAAU,IAAI,CAACA,OAAO,AAE5B,CAAA,IAAI,CAAC2B,YAAY,CAAGvD,GAAc,MAAO,CACrC7f,UAAW,0BACf,GACA,IAAI,CAACqjB,OAAO,CAAGxD,GAAc,MAAO,CAChC7f,UAAW,qBACf,EAAG,KAAK,EAAG,IAAI,CAACojB,YAAY,EAC5B,IAAI,CAACC,OAAO,CAAC5jB,KAAK,CAACyjB,eAAe,CAC9B,OAAS,IAAI,CAAC7M,QAAQ,CAAG,mBAC7B,IAAI,CAACiN,SAAS,CAAGzD,GAAc,MAAO,CAClC7f,UAAW,uBACf,EAAG,KAAK,EAAG,IAAI,CAACojB,YAAY,EAC5B,IAAI,CAACE,SAAS,CAAC7jB,KAAK,CAACyjB,eAAe,CAChC,OAAS,IAAI,CAAC7M,QAAQ,CAAG,mBAC7BoL,EAAQ8B,YAAY,CAAC,IAAI,CAACH,YAAY,CAAE3B,EAAQT,UAAU,CAAC,EAAE,EAE7D,IAAI,CAACwC,aAAa,EACtB,CAMAA,eAAgB,CACZ,IAAM/B,EAAU,IAAI,CAACA,OAAO,CAAE3R,EAAU,IAAI,CAACA,OAAO,CAAE2T,EAAO,GAAMhC,EAAQU,YAAY,CACnFuB,EAAU,EACd,IAAI,CAACtb,cAAc,CAACtM,IAAI,CAAC8jB,GAAsB,IAAI,CAACyD,OAAO,CAAE,QAAS,KAC9DK,EAAU,IACVA,GAAWD,EACX3T,EAAQrQ,KAAK,CAACkkB,SAAS,CAAG,CAACD,EAAU,KAE7C,IACA,IAAI,CAACtb,cAAc,CAACtM,IAAI,CAAC8jB,GAAsB,IAAI,CAAC0D,SAAS,CAAE,QAAS,KAChE7B,EAAQU,YAAY,CAAGuB,GACvB5T,EAAQqS,YAAY,CAAGsB,IACvBC,GAAWD,EACX3T,EAAQrQ,KAAK,CAACkkB,SAAS,CAAG,CAACD,EAAU,KAE7C,GACJ,CAKAhD,iBAAkB,CACd,IACIiB,EAAa7R,EADXvU,EAAQ,IAAI,CAACA,KAAK,CAAEslB,EAAa,IAAI,CAAC7kB,OAAO,CAAEoJ,EAAY7J,EAAM6J,SAAS,CAAE5J,EAAaD,EAAMS,OAAO,CAACR,UAAU,CAAEqE,EAAoBrE,GAAYqE,kBAAmB+jB,EAAO,IAAI,CAGjLnC,EAAU,IAAI,CAACA,OAAO,CAAG5B,GAAc,MAAO,CAChD7f,UAAW,iCACP6gB,EAAW7gB,SAAS,CAAG,IAAMH,CACrC,GACAuF,EAAUye,WAAW,CAACpC,GACtB,IAAI,CAACqC,WAAW,CAAGjE,GAAc,MAAO,CACpC7f,UAAW,iDACf,EAAG,KAAK,EAAGyhB,GAEX,IAAI,CAACrZ,cAAc,CAACtM,IAAI,CAAC8jB,GAAsB,IAAI,CAACkE,WAAW,CAAE,QAAS,KACtE,IAAI,CAAC/nB,MAAM,CAAC,CACRka,IAAK,CACD9Q,QAAS,CAACye,EAAKze,OAAO,AAC1B,CACJ,EACJ,IAEA,CACI,YACA,YACA,QACA,aACH,CAACjJ,OAAO,CAAC,AAAC6nB,IACPnE,GAAsB6B,EAASsC,EAAW,AAAC7jB,GAAMA,EAAE6hB,eAAe,GACtE,GACAnC,GAAsB6B,EAAS,YAAa,AAACvhB,GAAM3E,EAAM4E,OAAO,EAAE6jB,sBAAsB9jB,IAExF,IAAI,CAAC4P,OAAO,CAAGA,EAAU+P,GAAc,KAAM,CACzC7f,UAAW,iCACP6gB,EAAW3D,gBAAgB,AACnC,GAEA,IAAI,CAACyE,WAAW,CAAGA,EAAc9B,GAAc,MAAO,CAClD7f,UAAW,yBACf,GACAyhB,EAAQ8B,YAAY,CAAC5B,EAAaF,EAAQT,UAAU,CAAC,EAAE,EACvDW,EAAY4B,YAAY,CAACzT,EAAS6R,EAAYX,UAAU,CAAC,EAAE,EAC3D,IAAI,CAACiD,eAAe,GAEpB,IAAI,CAACd,aAAa,EACtB,CAKAvC,oBAAqB,CAGb,IAAI,CAACzb,OAAO,EACZ,IAAI,CAAC2K,OAAO,CAACqS,YAAY,CAAI,IAAI,CAACV,OAAO,CAACU,YAAY,CAAG,GACzD,IAAI,CAACiB,YAAY,CAAC3jB,KAAK,CAACwiB,OAAO,CAAG,SAIlC,IAAI,CAACnS,OAAO,CAACrQ,KAAK,CAACkkB,SAAS,CAAG,MAE/B,IAAI,CAACP,YAAY,CAAC3jB,KAAK,CAACwiB,OAAO,CAAG,OAE1C,CAKAgC,iBAAkB,CACd,IAAMxC,EAAU,IAAI,CAACA,OAAO,CAAE3R,EAAU,IAAI,CAAC6R,WAAW,CAAEE,EAAU,IAAI,CAACA,OAAO,CAEhFiC,EAAc,IAAI,CAACA,WAAW,CAC1B3e,EAAU,IAAI,CAACA,OAAO,AAC1B2e,CAAAA,EAAYrkB,KAAK,CAACyjB,eAAe,CAC7B,OAAS,IAAI,CAAC7M,QAAQ,CAAG,mBACxBlR,GAYDsc,EAAQhiB,KAAK,CAACE,MAAM,CAAG,OACvBmQ,EAAQhK,SAAS,CAACC,MAAM,CAAC,mBACzB+d,EAAYhe,SAAS,CAACC,MAAM,CAAC,0BAC7B+d,EAAYrkB,KAAK,CAAC2R,GAAG,CAAG6O,GAASnQ,EAAS,eAAiB,KAC3DgU,EAAYrkB,KAAK,CAAC4iB,IAAI,CAAG,AAACZ,EAAQa,WAAW,CACzCrC,GAASnQ,EAAS,gBAAmB,OAfrC+R,GACAA,CAAAA,EAAQpiB,KAAK,CAACwiB,OAAO,CAAG,MAAK,EAEjC6B,EAAYrkB,KAAK,CAAC4iB,IAAI,CAAG,MACzBld,EAAU,IAAI,CAACA,OAAO,CAAG,CAAA,EACzB2K,EAAQhK,SAAS,CAACoe,GAAG,CAAC,mBACtBJ,EAAYhe,SAAS,CAACoe,GAAG,CAAC,0BAC1BzC,EAAQhiB,KAAK,CAACE,MAAM,CAAGmkB,EAAY3B,YAAY,CAAG,KAU1D,CASAO,aAAahd,CAAM,CAAEzJ,CAAM,CAAE,CACzB,IAAMklB,EAAgBzb,EAAO4E,UAAU,CAAE6Z,EAAqBhD,EAAcnhB,SAAS,CAErFokB,EAAgBjD,EAAc7W,UAAU,CAACA,UAAU,EAE/C6Z,CAAAA,EAAmBte,OAAO,CAAC,2BAA6B,EAAC,IAI7Due,EAAcpkB,SAAS,CAAG,GACtBmkB,GACAC,EAActe,SAAS,CAACoe,GAAG,CAACC,EAAmBE,IAAI,IAGvDD,EACK7e,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAChD9F,KAAK,CAACyjB,eAAe,CACtBxd,EAAOjG,KAAK,CAACyjB,eAAe,CAE5BjnB,GACA,IAAI,CAACqoB,uBAAuB,CAACF,GAErC,CAKAE,wBAAwB5e,CAAM,CAAE,CAC5B,IAAMI,EAAYJ,EAAOI,SAAS,CAC9BA,EAAU6C,QAAQ,CAAC,qBACnB7C,EAAUC,MAAM,CAAC,qBAGjBD,EAAUoe,GAAG,CAAC,oBAEtB,CAKAK,mBAAmB7e,CAAM,CAAE,CACvB,IAAM8e,EAAa9e,EAAO4E,UAAU,CAC/B/E,gBAAgB,CAAC,sBACtB,EAAE,CAACrJ,OAAO,CAACjB,IAAI,CAACupB,EAAY,AAACC,IACrBA,IAAc/e,GACd+e,EAAU3e,SAAS,CAACC,MAAM,CAAC,oBAEnC,EACJ,CAKAhK,OAAOC,CAAO,CAAEC,CAAM,CAAE,CACpB,IAAI,CAACukB,OAAO,CAAG,CAAC,CAACxkB,EAAQia,GAAG,CAACmH,WAAW,CACxC+C,GAAmB,CAAA,EAAM,IAAI,CAAC5kB,KAAK,CAACS,OAAO,CAACoO,UAAU,CAAEpO,GACxDmkB,GAAmB,CAAA,EAAM,IAAI,CAACnkB,OAAO,CAAEA,EAAQia,GAAG,EAClD,IAAI,CAAC9Q,OAAO,CAAGib,GAAkB,IAAI,CAACpkB,OAAO,CAACmJ,OAAO,EAAI,IAAI,CAACnJ,OAAO,CAACyL,OAAO,CAAE,CAAA,GAE3E,IAAI,CAAClM,KAAK,CAACgJ,kBAAkB,EAC7B,IAAI,CAAChJ,KAAK,CAACgJ,kBAAkB,CAACxI,MAAM,GAExC,IAAI,CAACR,KAAK,CAACmpB,UAAU,CAAG,CAAA,EACpBtE,GAAkBnkB,EAAQ,CAAA,IAC1B,IAAI,CAACV,KAAK,CAACU,MAAM,EAEzB,CAKAyI,SAAU,CACN,IAAMigB,EAAgB,IAAI,CAAClD,OAAO,CAAEzV,EAAS2Y,GAAiBA,EAAcra,UAAU,CACtF,IAAI,CAAClC,cAAc,CAAClM,OAAO,CAAC,AAAC0R,GAAaA,KAEtC5B,GACAA,EAAO4Y,WAAW,CAACD,EAE3B,CAKA1oB,QAAS,CACL,GAAI,IAAI,CAACD,OAAO,CAACyL,OAAO,GAAK,IAAI,CAAC4I,UAAU,CACxC,IAAI,CAACwU,sBAAsB,OAE1B,CACD,GAAI,CAAC,IAAI,CAACxU,UAAU,CAChB,OAEJ,IAAI,CAACyU,gBAAgB,GACrB,IAAI,CAACC,aAAa,GAClB,IAAI,CAACC,gBAAgB,GACrB,IAAI,CAACpE,kBAAkB,GACvB,IAAI,CAACqD,eAAe,EACxB,CACJ,CAKAY,wBAAyB,CACQ,CAAA,IAAzB,IAAI,CAAC7oB,OAAO,CAACyL,OAAO,GACpB,IAAI,CAAC/C,OAAO,GACZ,IAAI,CAACS,OAAO,CAAG,CAAA,GAEU,CAAA,IAAzB,IAAI,CAACnJ,OAAO,CAACyL,OAAO,GACpB,IAAI,CAACiZ,eAAe,GACpB,IAAI,CAACC,aAAa,IAEtB,IAAI,CAACtQ,UAAU,CAAG,IAAI,CAACrU,OAAO,CAACyL,OAAO,AAC1C,CAKAqd,kBAAmB,CACX,IAAI,CAAC9oB,OAAO,CAACgE,SAAS,GAAK,IAAI,CAACygB,YAAY,GACxC,IAAI,CAACA,YAAY,EACjB,IAAI,CAACgB,OAAO,CAAC3b,SAAS,CAACC,MAAM,CAAC,IAAI,CAAC0a,YAAY,EAE/C,IAAI,CAACzkB,OAAO,CAACgE,SAAS,EACtB,IAAI,CAACyhB,OAAO,CAAC3b,SAAS,CAACoe,GAAG,CAAC,IAAI,CAACloB,OAAO,CAACgE,SAAS,EAErD,IAAI,CAACygB,YAAY,CAAG,IAAI,CAACzkB,OAAO,CAACgE,SAAS,EAE1C,IAAI,CAAChE,OAAO,CAACkhB,gBAAgB,GAAK,IAAI,CAACA,gBAAgB,GACnD,IAAI,CAACA,gBAAgB,EACrB,IAAI,CAACpN,OAAO,CAAChK,SAAS,CAACC,MAAM,CAAC,IAAI,CAACmX,gBAAgB,EAEnD,IAAI,CAAClhB,OAAO,CAACkhB,gBAAgB,EAC7B,IAAI,CAACpN,OAAO,CAAChK,SAAS,CAACoe,GAAG,CAAC,IAAI,CAACloB,OAAO,CAACkhB,gBAAgB,EAE5D,IAAI,CAACA,gBAAgB,CAAG,IAAI,CAAClhB,OAAO,CAACkhB,gBAAgB,CAE7D,CAKA6H,eAAgB,CACR,CAAA,CAAC1E,GAAgC,IAAI,CAACrkB,OAAO,CAACmhB,OAAO,CAAE,IAAI,CAAC8D,UAAU,GACtE,IAAI,CAACT,OAAO,AAAD,IACX,IAAI,CAAC1Q,OAAO,CAACmV,SAAS,CAAG,AAACtF,KAA+FuF,SAAS,CAClI,IAAI,CAACvE,aAAa,GAE1B,CAKAqE,kBAAmB,CACXjF,GAAqB,IAAI,CAAC/jB,OAAO,CAACmJ,OAAO,GACzC,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACnJ,OAAO,CAACmJ,OAAO,AAAD,CAE1C,CAIAmR,aAAc,CACV,OAAO,IAAI,CAAC/a,KAAK,CAACS,OAAO,CAACR,UAAU,CAAC6a,QAAQ,EACzC,IAAI,CAACra,OAAO,CAACqa,QAAQ,EACrB,qDACR,CACJ,CACAiK,GAAQvlB,SAAS,CAACioB,YAAY,CAAG,CAC7BlkB,OAAQ,+BACRC,QAAS,gCACTC,UAAW,kCACXC,MAAO,8BACPqU,QAAS,qBACTC,aAAc,2BACdE,IAAK,iBACLC,SAAU,uBACV1F,KAAM,2BACN4F,kBAAmB,iCACnB1F,aAAc,2BACd2F,eAAgB,6BAChBE,SAAU,sBACVC,SAAU,sBACVC,SAAU,sBACVC,SAAU,sBACV5F,UAAW,uBACXF,UAAW,uBACXyG,mBAAoB,kCACpBN,gBAAiB,8BACjBJ,SAAU,uBACVE,SAAU,uBACVC,UAAW,wBACXI,WAAY,yBACZD,gBAAiB,8BACjBE,cAAe,4BACfC,cAAe,4BACfoB,sBAAuB,qCACvBG,WAAY,wBACZrB,cAAe,4BACfC,eAAgB,6BAChBC,cAAe,4BACfC,cAAe,4BACfC,MAAO,oBACPE,MAAO,oBACPC,OAAQ,qBACRqC,SAAU,8BACVD,SAAU,8BACVG,QAAS,6BACTD,gBAAiB,qCACjBE,sBAAuB,2CACvBC,eAAgB,oCAChBjC,WAAY,yBACZO,kBAAmB,gCACnBO,UAAW,wBACX0G,UAAW,sBACf,EAsBA,GAAM,CAAEja,WAAY+hB,EAAwB,CAAE,CAAI9pB,IAI5C,CAAEqI,SAAU0hB,EAAsB,CAAEnF,SAAUoF,EAAsB,CAAE7mB,MAAO8mB,EAAmB,CAAE9oB,KAAM+oB,EAAkB,CAAE,CAAIlqB,IAUtI,SAASmqB,GAAmBxpB,CAAO,EAC/B,IAAMypB,EAAe,IAAI,CAACzpB,OAAO,CAAE0C,EAAO+mB,EAAa/mB,IAAI,CAAEmiB,EAAayE,GAAoBG,EAAarb,UAAU,EAAIqb,EAAarb,UAAU,CAAC6L,GAAG,CAAEja,GAAWA,EAAQia,GAAG,EAAGsK,EAAc7hB,GAAQA,EAAK0L,UAAU,EAAI1L,EAAK0L,UAAU,CAAC6L,GAAG,AAC3O,CAAA,IAAI,CAAC7L,UAAU,CAAG,IAhC6BkW,GAgCZO,EAAYN,EAAa,IAAI,EAC5D,IAAI,CAACnW,UAAU,CAACiG,UAAU,EAC1B,CAAA,IAAI,CAACqU,UAAU,CAAG,CAAA,CAAG,CAE7B,CAuBA,SAASgB,KACL,IAAI,CAACC,aAAa,EACtB,CAKA,SAASC,KACD,IAAI,CAACxb,UAAU,GACf,IAAI,CAACA,UAAU,CAACnO,MAAM,GACtB4pB,AAOR,SAAmBtqB,CAAK,EACpB,GAAIA,EAAM6O,UAAU,EAAEiG,WAAY,CAC9B,IAAMyV,EAAevqB,EAAMS,OAAO,CAACT,KAAK,CAClComB,EAAcpmB,EAAM6O,UAAU,CAACuX,WAAW,CAC1CW,EAAcX,GAAgB,CAAA,AAACA,EAAYK,UAAU,CACvDqD,GAAuB1D,EAAa,gBACpC0D,GAAuB1D,EAAa,kBAAqBA,EAAYW,WAAW,AAAD,CACnF/mB,CAAAA,EAAM6O,UAAU,CAACsI,KAAK,CAAG4P,EACzB,IAAIyD,EAAQ,CAAA,EACZ,GAAIzD,EAAc/mB,EAAMyqB,SAAS,CAAE,CAC/B,IAAMC,EAAQV,GAAmBO,EAAaI,WAAW,CAAEJ,EAAaK,OAAO,EAAIL,EAAaK,OAAO,CAAC,EAAE,CAAE,GAAK7D,EAC3G8D,EAAOH,EAAQ1qB,EAAM8qB,UAAU,CAACxlB,CAAC,AACvCtF,CAAAA,EAAM8qB,UAAU,CAACxlB,CAAC,CAAGolB,EACrB1qB,EAAM8qB,UAAU,CAAC3T,KAAK,EAAI0T,EAC1BL,EAAQ,CAAA,CACZ,MACyB,IAAhBzD,GACLyD,CAAAA,EAAQ,CAAA,CAAG,EAEXzD,IAAgB/mB,EAAM6O,UAAU,CAACkc,eAAe,GAChD/qB,EAAM6O,UAAU,CAACkc,eAAe,CAAGhE,EAC/ByD,GACAxqB,CAAAA,EAAMgrB,aAAa,CAAG,CAAA,CAAG,EAGrC,CACJ,EAjCkB,IAAI,EAEtB,CAmCA,SAASC,KACD,IAAI,CAACpc,UAAU,EACf,IAAI,CAACA,UAAU,CAAC1F,OAAO,EAE/B,CAIA,SAAS+hB,KACL,IAAMnE,EAAc,IAAI,CAAClY,UAAU,EAAEjF,SAAW,IAAI,CAACiF,UAAU,CAACiG,UAAU,CACtE,IAAI,CAACjG,UAAU,CAACsI,KAAK,CAAG,EACxB4P,GAAeA,EAAc,IAAI,CAAC0D,SAAS,GAC3C,IAAI,CAAC9c,QAAQ,EAAIoZ,EACjB,IAAI,CAAC6D,OAAO,CAAC,EAAE,EAAI7D,EAE3B,CAKA,SAASoE,KACL,IAAMtc,EAAa,IAAI,CAACA,UAAU,CAAE1E,EAAS0E,GACzCA,EAAW0F,OAAO,EAClB1F,EAAW0F,OAAO,CAAC6W,aAAa,CAAC,uCAEjCvc,GACA,IAAI,CAAC7F,kBAAkB,EACvB,IAAI,CAACvI,OAAO,CAACkJ,MAAM,EACnBQ,IACI,IAAI,CAACnB,kBAAkB,CAACqiB,KAAK,EAC3BnW,0BAA0B,IAAI,CAACvL,MAAM,EACvCQ,EAAOgR,UAAU,CAACjX,KAAK,CAAC,mBAAmB,CACvC,QAAU2K,EAAWkM,WAAW,GAAK,2BAGzC5Q,EAAOgR,UAAU,CAACjX,KAAK,CAAC,mBAAmB,CACvC,QAAU2K,EAAWkM,WAAW,GAAK,2BAGrD,CAIA,SAASuQ,GAAiDpe,CAAK,EAC3D,IAAgDwN,EAAM,IAAI,CAAC1a,KAAK,CAAC6O,UAAU,CAC3E,GAAI6L,GAAOA,EAAI5F,UAAU,CAAE,CACvB,IAAI3K,EAAS+C,EAAM/C,MAAM,CAErBA,EAAO4E,UAAU,CAACtK,SAAS,CAAC6F,OAAO,CAJzB,+BAIwC,GAClDH,CAAAA,EAASA,EAAO4E,UAAU,CAACA,UAAU,AAAD,EAExC5E,EAAOI,SAAS,CAACC,MAAM,CAAC,oBAC5B,CACJ,CAKA,SAAS+gB,GAAiCre,CAAK,EAC3C,IAAgDwN,EAAM,IAAI,CAAC1a,KAAK,CAAC6O,UAAU,CAC3E,GAAI6L,GAAOA,EAAI5F,UAAU,CAAE,CACvB,IAAI3K,EAAS+C,EAAM/C,MAAM,CAEzBuQ,EAAIsO,kBAAkB,CAAC9b,EAAM/C,MAAM,EAE/BA,EAAO4E,UAAU,CAACtK,SAAS,CAAC6F,OAAO,CANzB,+BAMwC,GAClDH,CAAAA,EAASA,EAAO4E,UAAU,CAACA,UAAU,AAAD,EAGxC2L,EAAIqO,uBAAuB,CAAC5e,EAChC,CACJ,CAmBA,IAAMqhB,GAAK1rB,GACX0rB,CAAAA,GAAEniB,kBAAkB,CAAGmiB,GAAEniB,kBAAkB,EAvqI0BA,EAwqIrEmiB,GAAEzG,OAAO,CAtM0CA,GAuMnD0G,AAh0BmB,CAAA,CACf1rB,QApNJ,SAAiB2rB,CAAuB,EACpC,IAAMC,EAAkBD,EAAwBlsB,SAAS,AACpDmsB,CAAAA,EAAgBN,KAAK,EAAE/V,mBAExBqW,EAAgB3H,iBAAiB,CAAGtB,GACpCiJ,EAAgB1H,gBAAgB,CAAGb,GACnCuI,EAAgBC,yBAAyB,CACrClI,GACJiI,EAAgB7V,WAAW,CAAGiO,GAC9B4H,EAAgBN,KAAK,CAAGM,EAAgBN,KAAK,EAAI,CAAC,EAClDM,EAAgBN,KAAK,CAACzX,kBAAkB,CAAGS,GAA+BT,kBAAkB,CAC5F+X,EAAgBN,KAAK,CAACxX,oBAAoB,CAAGQ,GAA+BR,oBAAoB,CAChG8X,EAAgBN,KAAK,CAACvpB,eAAe,CAAGqgB,GACxCwJ,EAAgBN,KAAK,CAACnW,uBAAuB,CAAGmN,GAChDsJ,EAAgBN,KAAK,CAAC/V,gBAAgB,CAAGjB,GAA+BiB,gBAAgB,CACxF4M,GAhD4DtG,IAiD5DsG,GAAsB,CAClBjiB,WAAY,CACRsE,SAluCoDuT,EAmuCxD,CACJ,GAER,CA+LA,CAAA,EA8zBsB/X,OAAO,CAACyrB,GAAEniB,kBAAkB,EAClDwiB,AAjBsB,CAAA,CAClB9rB,QAhJJ,SAA+B4M,CAAU,CAAE+e,CAAuB,EAC9D,IAAMI,EAAanf,EAAWnN,SAAS,AAClCssB,CAAAA,EAAW1B,aAAa,GACzBP,GAAuBld,EAAY,oBAAqBwd,IACxDN,GAAuBld,EAAY,eAAgB0d,IACnDR,GAAuBld,EAAY,eAAgB0d,IACnDR,GAAuBld,EAAY,UAAWse,IAC9CpB,GAAuBld,EAAY,aAAcue,GAAmB,CAAEa,MAAO,CAAE,GAC/ElC,GAAuBld,EAAY,SAAUwe,IAC7CW,EAAW1B,aAAa,CAAGH,GAC3BJ,GAAuB6B,EAAyB,iBAAkBJ,IAClEzB,GAAuB6B,EAAyB,eAAgBH,IAChE3B,GAj6B4DhO,IAm6BpE,CAmIA,CAAA,EAeyB7b,OAAO,CAACyrB,GAAEQ,KAAK,CAAER,GAAEniB,kBAAkB,EACjC,IAAMzJ,GAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}