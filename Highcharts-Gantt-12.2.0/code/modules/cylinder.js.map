{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/cylinder\n * @requires highcharts\n * @requires highcharts/highcharts-3d\n *\n * Highcharts cylinder module\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/cylinder\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Color\"],amd1[\"RendererRegistry\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/cylinder\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"RendererRegistry\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__608__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 608:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ cylinder_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Core/Math3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable max-len */\n/**\n * Apply 3-D rotation\n * Euler Angles (XYZ):\n *     cosA = cos(Alfa|Roll)\n *     cosB = cos(Beta|Pitch)\n *     cosG = cos(Gamma|Yaw)\n *\n * Composite rotation:\n * |          cosB * cosG             |           cosB * sinG            |    -sinB    |\n * | sinA * sinB * cosG - cosA * sinG | sinA * sinB * sinG + cosA * cosG | sinA * cosB |\n * | cosA * sinB * cosG + sinA * sinG | cosA * sinB * sinG - sinA * cosG | cosA * cosB |\n *\n * Now, Gamma/Yaw is not used (angle=0), so we assume cosG = 1 and sinG = 0, so\n * we get:\n * |     cosB    |   0    |   - sinB    |\n * | sinA * sinB |  cosA  | sinA * cosB |\n * | cosA * sinB | - sinA | cosA * cosB |\n *\n * But in browsers, y is reversed, so we get sinA => -sinA. The general result\n * is:\n * |      cosB     |   0    |    - sinB     |     | x |     | px |\n * | - sinA * sinB |  cosA  | - sinA * cosB |  x  | y |  =  | py |\n * |  cosA * sinB  |  sinA  |  cosA * cosB  |     | z |     | pz |\n *\n * @private\n * @function rotate3D\n */\n/* eslint-enable max-len */\n/**\n * Rotates the position as defined in angles.\n * @private\n * @param {number} x\n *        X coordinate\n * @param {number} y\n *        Y coordinate\n * @param {number} z\n *        Z coordinate\n * @param {Highcharts.Rotation3DObject} angles\n *        Rotation angles\n * @return {Highcharts.Position3DObject}\n *         Rotated position\n */\nfunction rotate3D(x, y, z, angles) {\n    return {\n        x: angles.cosB * x - angles.sinB * z,\n        y: -angles.sinA * angles.sinB * x + angles.cosA * y -\n            angles.cosB * angles.sinA * z,\n        z: angles.cosA * angles.sinB * x + angles.sinA * y +\n            angles.cosA * angles.cosB * z\n    };\n}\n/**\n * Transforms a given array of points according to the angles in chart.options.\n *\n * @private\n * @function Highcharts.perspective\n *\n * @param {Array<Highcharts.Position3DObject>} points\n * The array of points\n *\n * @param {Highcharts.Chart} chart\n * The chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @param {boolean} [useInvertedPersp]\n * Whether to use inverted perspective in calculations\n *\n * @return {Array<Highcharts.Position3DObject>}\n * An array of transformed points\n *\n * @requires highcharts-3d\n */\nfunction perspective(points, chart, insidePlotArea, useInvertedPersp) {\n    const options3d = chart.options.chart.options3d, \n    /* The useInvertedPersp argument is used for inverted charts with\n     * already inverted elements, such as dataLabels or tooltip positions.\n     */\n    inverted = pick(useInvertedPersp, insidePlotArea ? chart.inverted : false), origin = {\n        x: chart.plotWidth / 2,\n        y: chart.plotHeight / 2,\n        z: options3d.depth / 2,\n        vd: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0)\n    }, scale = chart.scale3d || 1, beta = deg2rad * options3d.beta * (inverted ? -1 : 1), alpha = deg2rad * options3d.alpha * (inverted ? -1 : 1), angles = {\n        cosA: Math.cos(alpha),\n        cosB: Math.cos(-beta),\n        sinA: Math.sin(alpha),\n        sinB: Math.sin(-beta)\n    };\n    if (!insidePlotArea) {\n        origin.x += chart.plotLeft;\n        origin.y += chart.plotTop;\n    }\n    // Transform each point\n    return points.map(function (point) {\n        const rotated = rotate3D((inverted ? point.y : point.x) - origin.x, (inverted ? point.x : point.y) - origin.y, (point.z || 0) - origin.z, angles), \n        // Apply perspective\n        coordinate = perspective3D(rotated, origin, origin.vd);\n        // Apply translation\n        coordinate.x = coordinate.x * scale + origin.x;\n        coordinate.y = coordinate.y * scale + origin.y;\n        coordinate.z = rotated.z * scale + origin.z;\n        return {\n            x: (inverted ? coordinate.y : coordinate.x),\n            y: (inverted ? coordinate.x : coordinate.y),\n            z: coordinate.z\n        };\n    });\n}\n/**\n * Perspective3D function is available in global Highcharts scope because is\n * needed also outside of perspective() function (#8042).\n * @private\n * @function Highcharts.perspective3D\n *\n * @param {Highcharts.Position3DObject} coordinate\n * 3D position\n *\n * @param {Highcharts.Position3DObject} origin\n * 3D root position\n *\n * @param {number} distance\n * Perspective distance\n *\n * @return {Highcharts.PositionObject}\n * Perspective 3D Position\n *\n * @requires highcharts-3d\n */\nfunction perspective3D(coordinate, origin, distance) {\n    const projection = ((distance > 0) &&\n        (distance < Number.POSITIVE_INFINITY)) ?\n        distance / (coordinate.z + origin.z + distance) :\n        1;\n    return {\n        x: coordinate.x * projection,\n        y: coordinate.y * projection\n    };\n}\n/**\n * Calculate a distance from camera to points - made for calculating zIndex of\n * scatter points.\n *\n * @private\n * @function Highcharts.pointCameraDistance\n *\n * @param {Highcharts.Dictionary<number>} coordinates\n * Coordinates of the specific point\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @return {number}\n * Distance from camera to point\n *\n * @requires highcharts-3d\n */\nfunction pointCameraDistance(coordinates, chart) {\n    const options3d = chart.options.chart.options3d, cameraPosition = {\n        x: chart.plotWidth / 2,\n        y: chart.plotHeight / 2,\n        z: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0) +\n            options3d.depth\n    }, \n    // Added support for objects with plotX or x coordinates.\n    distance = Math.sqrt(Math.pow(cameraPosition.x - pick(coordinates.plotX, coordinates.x), 2) +\n        Math.pow(cameraPosition.y - pick(coordinates.plotY, coordinates.y), 2) +\n        Math.pow(cameraPosition.z - pick(coordinates.plotZ, coordinates.z), 2));\n    return distance;\n}\n/**\n * Calculate area of a 2D polygon using Shoelace algorithm\n * https://en.wikipedia.org/wiki/Shoelace_formula\n *\n * @private\n * @function Highcharts.shapeArea\n *\n * @param {Array<Highcharts.PositionObject>} vertexes\n * 2D Polygon\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea(vertexes) {\n    let area = 0, i, j;\n    for (i = 0; i < vertexes.length; i++) {\n        j = (i + 1) % vertexes.length;\n        area += vertexes[i].x * vertexes[j].y - vertexes[j].x * vertexes[i].y;\n    }\n    return area / 2;\n}\n/**\n * Calculate area of a 3D polygon after perspective projection\n *\n * @private\n * @function Highcharts.shapeArea3d\n *\n * @param {Array<Highcharts.Position3DObject>} vertexes\n * 3D Polygon\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea3D(vertexes, chart, insidePlotArea) {\n    return shapeArea(perspective(vertexes, chart, insidePlotArea));\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Math3D = {\n    perspective,\n    perspective3D,\n    pointCameraDistance,\n    shapeArea,\n    shapeArea3D\n};\n/* harmony default export */ const Core_Math3D = (Math3D);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n;// ./code/es-modules/Series/Cylinder/SVGElement3DCylinder.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { Element3D: SVGElement3D } = highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType().prototype;\n/* *\n *\n *  Class\n *\n * */\nclass SVGElement3DCylinder extends SVGElement3D {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.parts = ['top', 'bottom', 'front', 'back'];\n        this.pathType = 'cylinder';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    fillSetter(fill) {\n        this.singleSetterForParts('fill', null, {\n            front: fill,\n            back: fill,\n            top: color(fill).brighten(0.1).get(),\n            bottom: color(fill).brighten(-0.1).get()\n        });\n        // Fill for animation getter (#6776)\n        this.color = this.fill = fill;\n        return this;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Cylinder_SVGElement3DCylinder = (SVGElement3DCylinder);\n\n;// ./code/es-modules/Series/Cylinder/CylinderComposition.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { charts, deg2rad: CylinderComposition_deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: CylinderComposition_perspective } = Core_Math3D;\n\n\nconst { extend, pick: CylinderComposition_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n *\n */\nfunction compose(SVGRendererClass) {\n    const rendererProto = SVGRendererClass.prototype;\n    if (!rendererProto.cylinder) {\n        rendererProto.Element3D.types.cylinder = Cylinder_SVGElement3DCylinder;\n        extend(rendererProto, {\n            cylinder: rendererCylinder,\n            cylinderPath: rendererCylinderPath,\n            getCurvedPath: rendererGetCurvedPath,\n            getCylinderBack: rendererGetCylinderBack,\n            getCylinderEnd: rendererGetCylinderEnd,\n            getCylinderFront: rendererGetCylinderFront\n        });\n    }\n}\n/**\n * Check if a path is simplified. The simplified path contains only lineTo\n * segments, whereas non-simplified contain curves.\n * @private\n */\nfunction isSimplified(path) {\n    return !path.some((seg) => seg[0] === 'C');\n}\n/** @private */\nfunction rendererCylinder(shapeArgs) {\n    return this.element3d('cylinder', shapeArgs);\n}\n/**\n * Generates paths and zIndexes.\n * @private\n */\nfunction rendererCylinderPath(shapeArgs) {\n    const renderer = this, chart = charts[renderer.chartIndex], \n    // Decide zIndexes of parts based on cuboid logic, for consistency.\n    cuboidData = this.cuboidPath(shapeArgs), isTopFirst = !cuboidData.isTop, isFronFirst = !cuboidData.isFront, top = renderer.getCylinderEnd(chart, shapeArgs), bottom = renderer.getCylinderEnd(chart, shapeArgs, true);\n    return {\n        front: renderer.getCylinderFront(top, bottom),\n        back: renderer.getCylinderBack(top, bottom),\n        top: top,\n        bottom: bottom,\n        zIndexes: {\n            top: isTopFirst ? 3 : 0,\n            bottom: isTopFirst ? 0 : 3,\n            front: isFronFirst ? 2 : 1,\n            back: isFronFirst ? 1 : 2,\n            group: cuboidData.zIndexes.group\n        }\n    };\n}\n/**\n * Returns curved path in format of:\n * [ M, x, y, ...[C, cp1x, cp2y, cp2x, cp2y, epx, epy]*n_times ]\n * (cp - control point, ep - end point)\n * @private\n */\nfunction rendererGetCurvedPath(points) {\n    const path = [['M', points[0].x, points[0].y]], limit = points.length - 2;\n    for (let i = 1; i < limit; i += 3) {\n        path.push([\n            'C',\n            points[i].x, points[i].y,\n            points[i + 1].x, points[i + 1].y,\n            points[i + 2].x, points[i + 2].y\n        ]);\n    }\n    return path;\n}\n/**\n * Returns cylinder Back path.\n * @private\n */\nfunction rendererGetCylinderBack(topPath, bottomPath) {\n    const path = [];\n    if (isSimplified(topPath)) {\n        const move = topPath[0], line2 = topPath[2];\n        if (move[0] === 'M' && line2[0] === 'L') {\n            path.push(['M', line2[1], line2[2]]);\n            path.push(topPath[3]);\n            // End at start\n            path.push(['L', move[1], move[2]]);\n        }\n    }\n    else {\n        if (topPath[2][0] === 'C') {\n            path.push(['M', topPath[2][5], topPath[2][6]]);\n        }\n        path.push(topPath[3], topPath[4]);\n    }\n    if (isSimplified(bottomPath)) {\n        const move = bottomPath[0];\n        if (move[0] === 'M') {\n            path.push(['L', move[1], move[2]]);\n            path.push(bottomPath[3]);\n            path.push(bottomPath[2]);\n        }\n    }\n    else {\n        const curve2 = bottomPath[2], curve3 = bottomPath[3], curve4 = bottomPath[4];\n        if (curve2[0] === 'C' && curve3[0] === 'C' && curve4[0] === 'C') {\n            path.push(['L', curve4[5], curve4[6]]);\n            path.push([\n                'C',\n                curve4[3],\n                curve4[4],\n                curve4[1],\n                curve4[2],\n                curve3[5],\n                curve3[6]\n            ]);\n            path.push([\n                'C',\n                curve3[3],\n                curve3[4],\n                curve3[1],\n                curve3[2],\n                curve2[5],\n                curve2[6]\n            ]);\n        }\n    }\n    path.push(['Z']);\n    return path;\n}\n/**\n * Returns cylinder path for top or bottom.\n * @private\n */\nfunction rendererGetCylinderEnd(chart, shapeArgs, isBottom) {\n    const { width = 0, height = 0, alphaCorrection = 0 } = shapeArgs, \n    // A half of the smaller one out of width or depth (optional, because\n    // there's no depth for a funnel that reuses the code)\n    depth = CylinderComposition_pick(shapeArgs.depth, width, 0), radius = Math.min(width, depth) / 2, \n    // Approximated longest diameter\n    angleOffset = CylinderComposition_deg2rad * (chart.options.chart.options3d.beta - 90 +\n        alphaCorrection), \n    // Could be top or bottom of the cylinder\n    y = (shapeArgs.y || 0) + (isBottom ? height : 0), \n    // Use cubic Bezier curve to draw a circle in x,z (y is constant).\n    // More math. at spencermortensen.com/articles/bezier-circle/\n    c = 0.5519 * radius, centerX = width / 2 + (shapeArgs.x || 0), centerZ = depth / 2 + (shapeArgs.z || 0), \n    // Points could be generated in a loop, but readability will plummet\n    points = [{\n            x: 0,\n            y: y,\n            z: radius\n        }, {\n            x: c,\n            y: y,\n            z: radius\n        }, {\n            x: radius,\n            y: y,\n            z: c\n        }, {\n            x: radius,\n            y: y,\n            z: 0\n        }, {\n            x: radius,\n            y: y,\n            z: -c\n        }, {\n            x: c,\n            y: y,\n            z: -radius\n        }, {\n            x: 0,\n            y: y,\n            z: -radius\n        }, {\n            x: -c,\n            y: y,\n            z: -radius\n        }, {\n            x: -radius,\n            y: y,\n            z: -c\n        }, {\n            x: -radius,\n            y: y,\n            z: 0\n        }, {\n            x: -radius,\n            y: y,\n            z: c\n        }, {\n            x: -c,\n            y: y,\n            z: radius\n        }, {\n            x: 0,\n            y: y,\n            z: radius\n        }], cosTheta = Math.cos(angleOffset), sinTheta = Math.sin(angleOffset);\n    let path, x, z;\n    // Rotate to match chart's beta and translate to the shape center\n    for (const point of points) {\n        x = point.x;\n        z = point.z;\n        point.x = (x * cosTheta - z * sinTheta) + centerX;\n        point.z = (z * cosTheta + x * sinTheta) + centerZ;\n    }\n    const perspectivePoints = CylinderComposition_perspective(points, chart, true);\n    // Check for sub-pixel curve issue, compare front and back edges\n    if (Math.abs(perspectivePoints[3].y - perspectivePoints[9].y) < 2.5 &&\n        Math.abs(perspectivePoints[0].y - perspectivePoints[6].y) < 2.5) {\n        // Use simplified shape\n        path = this.toLinePath([\n            perspectivePoints[0],\n            perspectivePoints[3],\n            perspectivePoints[6],\n            perspectivePoints[9]\n        ], true);\n    }\n    else {\n        // Or default curved path to imitate ellipse (2D circle)\n        path = this.getCurvedPath(perspectivePoints);\n    }\n    return path;\n}\n/**\n * Returns cylinder Front path.\n * @private\n */\nfunction rendererGetCylinderFront(topPath, bottomPath) {\n    const path = topPath.slice(0, 3);\n    if (isSimplified(bottomPath)) {\n        const move = bottomPath[0];\n        if (move[0] === 'M') {\n            path.push(bottomPath[2]);\n            path.push(bottomPath[1]);\n            path.push(['L', move[1], move[2]]);\n        }\n    }\n    else {\n        const move = bottomPath[0], curve1 = bottomPath[1], curve2 = bottomPath[2];\n        if (move[0] === 'M' && curve1[0] === 'C' && curve2[0] === 'C') {\n            path.push(['L', curve2[5], curve2[6]]);\n            path.push([\n                'C',\n                curve2[3],\n                curve2[4],\n                curve2[1],\n                curve2[2],\n                curve1[5],\n                curve1[6]\n            ]);\n            path.push([\n                'C',\n                curve1[3],\n                curve1[4],\n                curve1[1],\n                curve1[2],\n                move[1],\n                move[2]\n            ]);\n        }\n    }\n    path.push(['Z']);\n    return path;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst CylinderComposition = {\n    compose\n};\n/* harmony default export */ const Cylinder_CylinderComposition = (CylinderComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Cylinder/CylinderPoint.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: { pointClass: ColumnPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: CylinderPoint_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass CylinderPoint extends ColumnPoint {\n}\nCylinderPoint_extend(CylinderPoint.prototype, {\n    shapeType: 'cylinder'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Cylinder_CylinderPoint = (CylinderPoint);\n\n;// ./code/es-modules/Series/Cylinder/CylinderSeriesDefaults.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A cylinder graph is a variation of a 3d column graph. The cylinder graph\n * features cylindrical points.\n *\n * @sample {highcharts} highcharts/demo/cylinder/\n *         Cylinder graph\n *\n * @extends      plotOptions.column\n * @since        7.0.0\n * @product      highcharts\n * @excluding    allAreas, boostThreshold, colorAxis, compare, compareBase,\n *               dragDrop, boostBlending\n * @requires     modules/cylinder\n * @optionparent plotOptions.cylinder\n */\nconst CylinderSeriesDefaults = {};\n/**\n * A `cylinder` series. If the [type](#series.cylinder.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cylinder\n * @since     7.0.0\n * @product   highcharts\n * @excluding allAreas, boostThreshold, colorAxis, compare, compareBase,\n *            boostBlending\n * @requires  modules/cylinder\n * @apioption series.cylinder\n */\n/**\n * An array of data points for the series. For the `cylinder` series type,\n * points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. The `x` values will be automatically\n *    calculated, either starting at 0 and incremented by 1, or from\n *    `pointStart` and `pointInterval` given in the series options. If the axis\n *    has categories, these will be used. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of arrays with 2 values. In this case, the values correspond to\n *    `x,y`. If the first value is a string, it is applied as the name of the\n *    point, and the `x` value is inferred.\n *    ```js\n *    data: [\n *        [0, 0],\n *        [1, 8],\n *        [2, 9]\n *    ]\n *    ```\n *\n * 3. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.cylinder.turboThreshold), this option is not\n *    available.\n *\n *    ```js\n *    data: [{\n *        x: 1,\n *        y: 2,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 4,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|Array<(number|string),(number|null)>|null|*>}\n * @extends   series.column.data\n * @product   highcharts highstock\n * @apioption series.cylinder.data\n */\n''; // Detaches doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Cylinder_CylinderSeriesDefaults = (CylinderSeriesDefaults);\n\n;// ./code/es-modules/Series/Cylinder/CylinderSeries.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { column: ColumnSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: CylinderSeries_extend, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The cylinder series type.\n *\n * @requires highcharts-3d\n * @requires modules/cylinder\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cylinder\n *\n * @augments Highcharts.Series\n */\nclass CylinderSeries extends ColumnSeries {\n}\n/* *\n *\n *  Static Properties\n *\n * */\nCylinderSeries.compose = Cylinder_CylinderComposition.compose;\nCylinderSeries.defaultOptions = merge(ColumnSeries.defaultOptions, Cylinder_CylinderSeriesDefaults);\nCylinderSeries_extend(CylinderSeries.prototype, {\n    pointClass: Cylinder_CylinderPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cylinder', CylinderSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Cylinder_CylinderSeries = (CylinderSeries);\n\n;// ./code/es-modules/masters/modules/cylinder.js\n\n\n\n\n\nCylinder_CylinderSeries.compose(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType());\n/* harmony default export */ const cylinder_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__608__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cylinder_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "deg2rad", "pick", "perspective", "points", "chart", "insidePlotArea", "useInvertedPersp", "options3d", "options", "inverted", "origin", "x", "plot<PERSON>id<PERSON>", "y", "plotHeight", "z", "depth", "vd", "viewDistance", "scale", "scale3d", "beta", "alpha", "angles", "cosA", "Math", "cos", "cosB", "sinA", "sin", "sinB", "plotLeft", "plotTop", "map", "point", "rotated", "coordinate", "perspective3D", "distance", "projection", "Number", "POSITIVE_INFINITY", "shapeArea", "vertexes", "area", "i", "j", "length", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "parse", "color", "Element3D", "SVGElement3D", "getRendererType", "Cylinder_SVGElement3DC<PERSON>inder", "constructor", "arguments", "parts", "pathType", "fillSetter", "fill", "singleSetterForParts", "front", "back", "top", "brighten", "bottom", "charts", "CylinderComposition_deg2rad", "CylinderComposition_perspective", "pointCameraDistance", "coordinates", "cameraPosition", "sqrt", "pow", "plotX", "plotY", "plotZ", "shapeArea3D", "extend", "CylinderComposition_pick", "isSimplified", "path", "some", "seg", "rendererCylinder", "shapeArgs", "element3d", "rendererCylinderPath", "renderer", "chartIndex", "cuboidData", "cuboidPath", "isTopFirst", "isTop", "isFronFirst", "isFront", "getCylinderEnd", "getCylinderFront", "getCylinderBack", "zIndexes", "group", "rendererGetCurvedPath", "limit", "push", "rendererGetCylinderBack", "topPath", "bottomPath", "move", "line2", "curve2", "curve3", "curve4", "rendererGetCylinderEnd", "isBottom", "width", "height", "alphaCorrection", "radius", "min", "angleOffset", "c", "centerX", "centerZ", "cosTheta", "sinTheta", "perspectivePoints", "abs", "to<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON>ved<PERSON><PERSON>", "rendererGetCylinderFront", "slice", "curve1", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "column", "pointClass", "ColumnPoint", "seriesTypes", "CylinderPoint_extend", "CylinderPoint", "shapeType", "ColumnSeries", "CylinderSeries_extend", "merge", "CylinderSeries", "compose", "SVGRendererClass", "rendererProto", "cylinder", "types", "cylinderPath", "defaultOptions", "registerSeriesType", "Cylinder_CylinderSeries"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,cAAiB,EACnJ,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,gBAAmB,CAACA,EAAK,cAAiB,CAAE,GAC9J,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAElLA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAC5J,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,IACzI,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAarH,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAID,IAEf,CAAEE,KAAAA,CAAI,CAAE,CAAIF,IAiFlB,SAASG,EAAYC,CAAM,CAAEC,CAAK,CAAEC,CAAc,CAAEC,CAAgB,EAChE,IAAMC,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAI/CE,EAAWR,EAAKK,EAAkBD,EAAAA,GAAiBD,EAAMK,QAAQ,EAAWC,EAAS,CACjFC,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGR,EAAUS,KAAK,CAAG,EACrBC,GAAIhB,EAAKM,EAAUS,KAAK,CAAE,GAAKf,EAAKM,EAAUW,YAAY,CAAE,EAChE,EAAGC,EAAQf,EAAMgB,OAAO,EAAI,EAAGC,EAAOrB,EAAUO,EAAUc,IAAI,CAAIZ,CAAAA,EAAW,GAAK,CAAA,EAAIa,EAAQtB,EAAUO,EAAUe,KAAK,CAAIb,CAAAA,EAAW,GAAK,CAAA,EAAIc,EAAS,CACpJC,KAAMC,KAAKC,GAAG,CAACJ,GACfK,KAAMF,KAAKC,GAAG,CAAC,CAACL,GAChBO,KAAMH,KAAKI,GAAG,CAACP,GACfQ,KAAML,KAAKI,GAAG,CAAC,CAACR,EACpB,EAMA,OALKhB,IACDK,EAAOC,CAAC,EAAIP,EAAM2B,QAAQ,CAC1BrB,EAAOG,CAAC,EAAIT,EAAM4B,OAAO,EAGtB7B,EAAO8B,GAAG,CAAC,SAAUC,CAAK,MArDnBvB,EAAGE,EAAGE,EAsDhB,IAAMoB,GAtDIxB,EAsDe,AAACF,CAAAA,EAAWyB,EAAMrB,CAAC,CAAGqB,EAAMvB,CAAC,AAADA,EAAKD,EAAOC,CAAC,CAtDrDE,EAsDuD,AAACJ,CAAAA,EAAWyB,EAAMvB,CAAC,CAAGuB,EAAMrB,CAAC,AAADA,EAAKH,EAAOG,CAAC,CAtD7FE,EAsD+F,AAACmB,CAAAA,EAAMnB,CAAC,EAAI,CAAA,EAAKL,EAAOK,CAAC,CArDrI,CACHJ,EAAGY,AAoDuIA,EApDhII,IAAI,CAAGhB,EAAIY,AAoDqHA,EApD9GO,IAAI,CAAGf,EACnCF,EAAG,CAACU,AAmDsIA,EAnD/HK,IAAI,CAAGL,AAmDwHA,EAnDjHO,IAAI,CAAGnB,EAAIY,AAmDsGA,EAnD/FC,IAAI,CAAGX,EAC9CU,AAkDsIA,EAlD/HI,IAAI,CAAGJ,AAkDwHA,EAlDjHK,IAAI,CAAGb,EAChCA,EAAGQ,AAiDuIA,EAjDhIC,IAAI,CAAGD,AAiDyHA,EAjDlHO,IAAI,CAAGnB,EAAIY,AAiDuGA,EAjDhGK,IAAI,CAAGf,EAC7CU,AAgDsIA,EAhD/HC,IAAI,CAAGD,AAgDwHA,EAhDjHI,IAAI,CAAGZ,CACpC,GAiDIqB,EAAaC,EAAcF,EAASzB,EAAQA,EAAOO,EAAE,EAKrD,OAHAmB,EAAWzB,CAAC,CAAGyB,EAAWzB,CAAC,CAAGQ,EAAQT,EAAOC,CAAC,CAC9CyB,EAAWvB,CAAC,CAAGuB,EAAWvB,CAAC,CAAGM,EAAQT,EAAOG,CAAC,CAC9CuB,EAAWrB,CAAC,CAAGoB,EAAQpB,CAAC,CAAGI,EAAQT,EAAOK,CAAC,CACpC,CACHJ,EAAIF,EAAW2B,EAAWvB,CAAC,CAAGuB,EAAWzB,CAAC,CAC1CE,EAAIJ,EAAW2B,EAAWzB,CAAC,CAAGyB,EAAWvB,CAAC,CAC1CE,EAAGqB,EAAWrB,CAAC,AACnB,CACJ,EACJ,CAqBA,SAASsB,EAAcD,CAAU,CAAE1B,CAAM,CAAE4B,CAAQ,EAC/C,IAAMC,EAAa,AAAC,AAACD,EAAW,GAC3BA,EAAWE,OAAOC,iBAAiB,CACpCH,EAAYF,CAAAA,EAAWrB,CAAC,CAAGL,EAAOK,CAAC,CAAGuB,CAAO,EAC7C,EACJ,MAAO,CACH3B,EAAGyB,EAAWzB,CAAC,CAAG4B,EAClB1B,EAAGuB,EAAWvB,CAAC,CAAG0B,CACtB,CACJ,CA+CA,SAASG,EAAUC,CAAQ,EACvB,IAAIC,EAAO,EAAGC,EAAGC,EACjB,IAAKD,EAAI,EAAGA,EAAIF,EAASI,MAAM,CAAEF,IAC7BC,EAAI,AAACD,CAAAA,EAAI,CAAA,EAAKF,EAASI,MAAM,CAC7BH,GAAQD,CAAQ,CAACE,EAAE,CAAClC,CAAC,CAAGgC,CAAQ,CAACG,EAAE,CAACjC,CAAC,CAAG8B,CAAQ,CAACG,EAAE,CAACnC,CAAC,CAAGgC,CAAQ,CAACE,EAAE,CAAChC,CAAC,CAEzE,OAAO+B,EAAO,CAClB,CAuCA,IAAII,EAA+FzE,EAAoB,KACnH0E,EAAmH1E,EAAoBI,CAAC,CAACqE,GAEzIE,EAA2I3E,EAAoB,KAC/J4E,EAA+J5E,EAAoBI,CAAC,CAACuE,GAiBzL,GAAM,CAAEE,MAAOC,CAAK,CAAE,CAAIJ,IAEpB,CAAEK,UAAWC,CAAY,CAAE,CAAGJ,IAAkJK,eAAe,GAAG/D,SAAS,CAuC9KgE,EAjCnC,cAAmCF,EAC/BG,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,KAAK,CAAG,CAAC,MAAO,SAAU,QAAS,OAAO,CAC/C,IAAI,CAACC,QAAQ,CAAG,UACpB,CAMAC,WAAWC,CAAI,CAAE,CASb,OARA,IAAI,CAACC,oBAAoB,CAAC,OAAQ,KAAM,CACpCC,MAAOF,EACPG,KAAMH,EACNI,IAAKd,EAAMU,GAAMK,QAAQ,CAAC,IAAK9E,GAAG,GAClC+E,OAAQhB,EAAMU,GAAMK,QAAQ,CAAC,KAAM9E,GAAG,EAC1C,GAEA,IAAI,CAAC+D,KAAK,CAAG,IAAI,CAACU,IAAI,CAAGA,EAClB,IAAI,AACf,CACJ,EAwBM,CAAEO,OAAAA,CAAM,CAAEtE,QAASuE,CAA2B,CAAE,CAAIxE,IAEpD,CAAEG,YAAasE,CAA+B,CAAE,CA5FvC,CACXtE,YAAAA,EACAmC,cAAAA,EACAoC,oBAnEJ,SAA6BC,CAAW,CAAEtE,CAAK,EAC3C,IAAMG,EAAYH,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAAEoE,EAAiB,CAC9DhE,EAAGP,EAAMQ,SAAS,CAAG,EACrBC,EAAGT,EAAMU,UAAU,CAAG,EACtBC,EAAGd,EAAKM,EAAUS,KAAK,CAAE,GAAKf,EAAKM,EAAUW,YAAY,CAAE,GACvDX,EAAUS,KAAK,AACvB,EAKA,OAHWS,KAAKmD,IAAI,CAACnD,KAAKoD,GAAG,CAACF,EAAehE,CAAC,CAAGV,EAAKyE,EAAYI,KAAK,CAAEJ,EAAY/D,CAAC,EAAG,GACrFc,KAAKoD,GAAG,CAACF,EAAe9D,CAAC,CAAGZ,EAAKyE,EAAYK,KAAK,CAAEL,EAAY7D,CAAC,EAAG,GACpEY,KAAKoD,GAAG,CAACF,EAAe5D,CAAC,CAAGd,EAAKyE,EAAYM,KAAK,CAAEN,EAAY3D,CAAC,EAAG,GAE5E,EAwDI2B,UAAAA,EACAuC,YAbJ,SAAqBtC,CAAQ,CAAEvC,CAAK,CAAEC,CAAc,EAChD,OAAOqC,EAAUxC,EAAYyC,EAAUvC,EAAOC,GAClD,CAYA,EAyFM,CAAE6E,OAAAA,CAAM,CAAEjF,KAAMkF,CAAwB,CAAE,CAAIpF,IA4BpD,SAASqF,EAAaC,CAAI,EACtB,MAAO,CAACA,EAAKC,IAAI,CAAC,AAACC,GAAQA,AAAW,MAAXA,CAAG,CAAC,EAAE,CACrC,CAEA,SAASC,EAAiBC,CAAS,EAC/B,OAAO,IAAI,CAACC,SAAS,CAAC,WAAYD,EACtC,CAKA,SAASE,EAAqBF,CAAS,EACnC,IAAuBrF,EAAQkE,CAAM,CAACsB,AAArB,IAAI,CAA0BC,UAAU,CAAC,CAE1DC,EAAa,IAAI,CAACC,UAAU,CAACN,GAAYO,EAAa,CAACF,EAAWG,KAAK,CAAEC,EAAc,CAACJ,EAAWK,OAAO,CAAEhC,EAAMyB,AAFjG,IAAI,CAEsGQ,cAAc,CAAChG,EAAOqF,GAAYpB,EAASuB,AAFrJ,IAAI,CAE0JQ,cAAc,CAAChG,EAAOqF,EAAW,CAAA,GAChN,MAAO,CACHxB,MAAO2B,AAJM,IAAI,CAIDS,gBAAgB,CAAClC,EAAKE,GACtCH,KAAM0B,AALO,IAAI,CAKFU,eAAe,CAACnC,EAAKE,GACpCF,IAAKA,EACLE,OAAQA,EACRkC,SAAU,CACNpC,IAAK6B,AAAa,IAAbA,EACL3B,OAAQ2B,AAAiB,GAAjBA,EACR/B,MAAOiC,EAAc,EAAI,EACzBhC,KAAMgC,EAAc,EAAI,EACxBM,MAAOV,EAAWS,QAAQ,CAACC,KAAK,AACpC,CACJ,CACJ,CAOA,SAASC,EAAsBtG,CAAM,EACjC,IAAMkF,EAAO,CAAC,CAAC,IAAKlF,CAAM,CAAC,EAAE,CAACQ,CAAC,CAAER,CAAM,CAAC,EAAE,CAACU,CAAC,CAAC,CAAC,CAAE6F,EAAQvG,EAAO4C,MAAM,CAAG,EACxE,IAAK,IAAIF,EAAI,EAAGA,EAAI6D,EAAO7D,GAAK,EAC5BwC,EAAKsB,IAAI,CAAC,CACN,IACAxG,CAAM,CAAC0C,EAAE,CAAClC,CAAC,CAAER,CAAM,CAAC0C,EAAE,CAAChC,CAAC,CACxBV,CAAM,CAAC0C,EAAI,EAAE,CAAClC,CAAC,CAAER,CAAM,CAAC0C,EAAI,EAAE,CAAChC,CAAC,CAChCV,CAAM,CAAC0C,EAAI,EAAE,CAAClC,CAAC,CAAER,CAAM,CAAC0C,EAAI,EAAE,CAAChC,CAAC,CACnC,EAEL,OAAOwE,CACX,CAKA,SAASuB,EAAwBC,CAAO,CAAEC,CAAU,EAChD,IAAMzB,EAAO,EAAE,CACf,GAAID,EAAayB,GAAU,CACvB,IAAME,EAAOF,CAAO,CAAC,EAAE,CAAEG,EAAQH,CAAO,CAAC,EAAE,AAC3B,CAAA,MAAZE,CAAI,CAAC,EAAE,EAAYC,AAAa,MAAbA,CAAK,CAAC,EAAE,GAC3B3B,EAAKsB,IAAI,CAAC,CAAC,IAAKK,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,EACnC3B,EAAKsB,IAAI,CAACE,CAAO,CAAC,EAAE,EAEpBxB,EAAKsB,IAAI,CAAC,CAAC,IAAKI,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,CAAC,EAEzC,KAE0B,MAAlBF,CAAO,CAAC,EAAE,CAAC,EAAE,EACbxB,EAAKsB,IAAI,CAAC,CAAC,IAAKE,CAAO,CAAC,EAAE,CAAC,EAAE,CAAEA,CAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAEjDxB,EAAKsB,IAAI,CAACE,CAAO,CAAC,EAAE,CAAEA,CAAO,CAAC,EAAE,EAEpC,GAAIzB,EAAa0B,GAAa,CAC1B,IAAMC,EAAOD,CAAU,CAAC,EAAE,AACV,CAAA,MAAZC,CAAI,CAAC,EAAE,GACP1B,EAAKsB,IAAI,CAAC,CAAC,IAAKI,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,CAAC,EACjC1B,EAAKsB,IAAI,CAACG,CAAU,CAAC,EAAE,EACvBzB,EAAKsB,IAAI,CAACG,CAAU,CAAC,EAAE,EAE/B,KACK,CACD,IAAMG,EAASH,CAAU,CAAC,EAAE,CAAEI,EAASJ,CAAU,CAAC,EAAE,CAAEK,EAASL,CAAU,CAAC,EAAE,AAC1D,CAAA,MAAdG,CAAM,CAAC,EAAE,EAAYC,AAAc,MAAdA,CAAM,CAAC,EAAE,EAAYC,AAAc,MAAdA,CAAM,CAAC,EAAE,GACnD9B,EAAKsB,IAAI,CAAC,CAAC,IAAKQ,CAAM,CAAC,EAAE,CAAEA,CAAM,CAAC,EAAE,CAAC,EACrC9B,EAAKsB,IAAI,CAAC,CACN,IACAQ,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTD,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACZ,EACD7B,EAAKsB,IAAI,CAAC,CACN,IACAO,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTD,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACZ,EAET,CAEA,OADA5B,EAAKsB,IAAI,CAAC,CAAC,IAAI,EACRtB,CACX,CAKA,SAAS+B,EAAuBhH,CAAK,CAAEqF,CAAS,CAAE4B,CAAQ,EACtD,IAkEIhC,EAAM1E,EAAGI,EAlEP,CAAEuG,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAEC,gBAAAA,EAAkB,CAAC,CAAE,CAAG/B,EAGvDzE,EAAQmE,EAAyBM,EAAUzE,KAAK,CAAEsG,EAAO,GAAIG,EAAShG,KAAKiG,GAAG,CAACJ,EAAOtG,GAAS,EAE/F2G,EAAcpD,EAA+BnE,CAAAA,EAAMI,OAAO,CAACJ,KAAK,CAACG,SAAS,CAACc,IAAI,CAAG,GAC9EmG,CAAc,EAElB3G,EAAI,AAAC4E,CAAAA,EAAU5E,CAAC,EAAI,CAAA,EAAMwG,CAAAA,EAAWE,EAAS,CAAA,EAG9CK,EAAI,MAASH,EAAQI,EAAUP,EAAQ,EAAK7B,CAAAA,EAAU9E,CAAC,EAAI,CAAA,EAAImH,EAAU9G,EAAQ,EAAKyE,CAAAA,EAAU1E,CAAC,EAAI,CAAA,EAErGZ,EAAS,CAAC,CACFQ,EAAG,EACHE,EAAGA,EACHE,EAAG0G,CACP,EAAG,CACC9G,EAAGiH,EACH/G,EAAGA,EACHE,EAAG0G,CACP,EAAG,CACC9G,EAAG8G,EACH5G,EAAGA,EACHE,EAAG6G,CACP,EAAG,CACCjH,EAAG8G,EACH5G,EAAGA,EACHE,EAAG,CACP,EAAG,CACCJ,EAAG8G,EACH5G,EAAGA,EACHE,EAAG,CAAC6G,CACR,EAAG,CACCjH,EAAGiH,EACH/G,EAAGA,EACHE,EAAG,CAAC0G,CACR,EAAG,CACC9G,EAAG,EACHE,EAAGA,EACHE,EAAG,CAAC0G,CACR,EAAG,CACC9G,EAAG,CAACiH,EACJ/G,EAAGA,EACHE,EAAG,CAAC0G,CACR,EAAG,CACC9G,EAAG,CAAC8G,EACJ5G,EAAGA,EACHE,EAAG,CAAC6G,CACR,EAAG,CACCjH,EAAG,CAAC8G,EACJ5G,EAAGA,EACHE,EAAG,CACP,EAAG,CACCJ,EAAG,CAAC8G,EACJ5G,EAAGA,EACHE,EAAG6G,CACP,EAAG,CACCjH,EAAG,CAACiH,EACJ/G,EAAGA,EACHE,EAAG0G,CACP,EAAG,CACC9G,EAAG,EACHE,EAAGA,EACHE,EAAG0G,CACP,EAAE,CAAEM,EAAWtG,KAAKC,GAAG,CAACiG,GAAcK,EAAWvG,KAAKI,GAAG,CAAC8F,GAG9D,IAAK,IAAMzF,KAAS/B,EAChBQ,EAAIuB,EAAMvB,CAAC,CACXI,EAAImB,EAAMnB,CAAC,CACXmB,EAAMvB,CAAC,CAAG,AAACA,EAAIoH,EAAWhH,EAAIiH,EAAYH,EAC1C3F,EAAMnB,CAAC,CAAG,AAACA,EAAIgH,EAAWpH,EAAIqH,EAAYF,EAE9C,IAAMG,EAAoBzD,EAAgCrE,EAAQC,EAAO,CAAA,GAgBzE,OAdIqB,AAA4D,IAA5DA,KAAKyG,GAAG,CAACD,CAAiB,CAAC,EAAE,CAACpH,CAAC,CAAGoH,CAAiB,CAAC,EAAE,CAACpH,CAAC,GACxDY,AAA4D,IAA5DA,KAAKyG,GAAG,CAACD,CAAiB,CAAC,EAAE,CAACpH,CAAC,CAAGoH,CAAiB,CAAC,EAAE,CAACpH,CAAC,EAEjD,IAAI,CAACsH,UAAU,CAAC,CACnBF,CAAiB,CAAC,EAAE,CACpBA,CAAiB,CAAC,EAAE,CACpBA,CAAiB,CAAC,EAAE,CACpBA,CAAiB,CAAC,EAAE,CACvB,CAAE,CAAA,GAII,IAAI,CAACG,aAAa,CAACH,EAGlC,CAKA,SAASI,EAAyBxB,CAAO,CAAEC,CAAU,EACjD,IAAMzB,EAAOwB,EAAQyB,KAAK,CAAC,EAAG,GAC9B,GAAIlD,EAAa0B,GAAa,CAC1B,IAAMC,EAAOD,CAAU,CAAC,EAAE,AACV,CAAA,MAAZC,CAAI,CAAC,EAAE,GACP1B,EAAKsB,IAAI,CAACG,CAAU,CAAC,EAAE,EACvBzB,EAAKsB,IAAI,CAACG,CAAU,CAAC,EAAE,EACvBzB,EAAKsB,IAAI,CAAC,CAAC,IAAKI,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,CAAC,EAEzC,KACK,CACD,IAAMA,EAAOD,CAAU,CAAC,EAAE,CAAEyB,EAASzB,CAAU,CAAC,EAAE,CAAEG,EAASH,CAAU,CAAC,EAAE,AAC1D,CAAA,MAAZC,CAAI,CAAC,EAAE,EAAYwB,AAAc,MAAdA,CAAM,CAAC,EAAE,EAAYtB,AAAc,MAAdA,CAAM,CAAC,EAAE,GACjD5B,EAAKsB,IAAI,CAAC,CAAC,IAAKM,CAAM,CAAC,EAAE,CAAEA,CAAM,CAAC,EAAE,CAAC,EACrC5B,EAAKsB,IAAI,CAAC,CACN,IACAM,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTsB,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACZ,EACDlD,EAAKsB,IAAI,CAAC,CACN,IACA4B,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTxB,CAAI,CAAC,EAAE,CACPA,CAAI,CAAC,EAAE,CACV,EAET,CAEA,OADA1B,EAAKsB,IAAI,CAAC,CAAC,IAAI,EACRtB,CACX,CAYA,IAAImD,EAAmIjK,EAAoB,KACvJkK,EAAuJlK,EAAoBI,CAAC,CAAC6J,GAiBjL,GAAM,CAAEE,OAAQ,CAAEjJ,UAAW,CAAEkJ,WAAYC,CAAW,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAEhN,CAAE3D,OAAQ4D,CAAoB,CAAE,CAAI/I,GAM1C,OAAMgJ,UAAsBH,EAC5B,CACAE,EAAqBC,EAActJ,SAAS,CAAE,CAC1CuJ,UAAW,UACf,GA+IA,GAAM,CAAEN,OAAQO,CAAY,CAAE,CAAG,AAACR,IAA2II,WAAW,CAElL,CAAE3D,OAAQgE,CAAqB,CAAEC,MAAAA,CAAK,CAAE,CAAIpJ,GAkBlD,OAAMqJ,UAAuBH,EAC7B,CAMAG,EAAeC,OAAO,CAvdtB,SAAiBC,CAAgB,EAC7B,IAAMC,EAAgBD,EAAiB7J,SAAS,AAC3C8J,CAAAA,EAAcC,QAAQ,GACvBD,EAAcjG,SAAS,CAACmG,KAAK,CAACD,QAAQ,CAAG/F,EACzCyB,EAAOqE,EAAe,CAClBC,SAAUhE,EACVkE,aAAc/D,EACdyC,cAAe3B,EACfH,gBAAiBM,EACjBR,eAAgBgB,EAChBf,iBAAkBgC,CACtB,GAER,EA2cAe,EAAeO,cAAc,CAAGR,EAAMF,EAAaU,cAAc,CAhIlC,CAAC,GAiIhCT,EAAsBE,EAAe3J,SAAS,CAAE,CAC5CkJ,WAvKyDI,CAwK7D,GACAN,IAA0ImB,kBAAkB,CAAC,WAAYR,GAczKS,AAR8DT,EAQtCC,OAAO,CAAClG,IAAkJK,eAAe,IACpK,IAAM3D,EAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}