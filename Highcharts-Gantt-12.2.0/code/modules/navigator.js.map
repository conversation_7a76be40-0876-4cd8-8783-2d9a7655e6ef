{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/navigator\n * @requires highcharts\n *\n * Standalone navigator module\n *\n * (c) 2009-2025 Mateusz Bernacik\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/navigator\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Chart\"],amd1[\"Axis\"],amd1[\"Color\"],amd1[\"SeriesRegistry\"],amd1[\"RendererRegistry\"],amd1[\"SVGRenderer\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/navigator\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Chart\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"RendererRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__960__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__608__, __WEBPACK_EXTERNAL_MODULE__540__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 608:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ navigator_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n;// ./code/es-modules/Stock/Navigator/ChartNavigatorComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst composedMembers = [];\n/* *\n *\n *  Variables\n *\n * */\nlet NavigatorConstructor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass, NavigatorClass) {\n    if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().pushUnique(composedMembers, ChartClass)) {\n        const chartProto = ChartClass.prototype;\n        NavigatorConstructor = NavigatorClass;\n        chartProto.callbacks.push(onChartCallback);\n        addEvent(ChartClass, 'afterAddSeries', onChartAfterAddSeries);\n        addEvent(ChartClass, 'afterSetChartSize', onChartAfterSetChartSize);\n        addEvent(ChartClass, 'afterUpdate', onChartAfterUpdate);\n        addEvent(ChartClass, 'beforeRender', onChartBeforeRender);\n        addEvent(ChartClass, 'beforeShowResetZoom', onChartBeforeShowResetZoom);\n        addEvent(ChartClass, 'update', onChartUpdate);\n    }\n}\n/**\n * Handle adding new series.\n * @private\n */\nfunction onChartAfterAddSeries() {\n    if (this.navigator) {\n        // Recompute which series should be shown in navigator, and add them\n        this.navigator.setBaseSeries(null, false);\n    }\n}\n/**\n * For stock charts, extend the Chart.setChartSize method so that we can set the\n * final top position of the navigator once the height of the chart, including\n * the legend, is determined. #367. We can't use Chart.getMargins, because\n * labels offsets are not calculated yet.\n * @private\n */\nfunction onChartAfterSetChartSize() {\n    const legend = this.legend, navigator = this.navigator;\n    let legendOptions, xAxis, yAxis;\n    if (navigator) {\n        legendOptions = legend && legend.options;\n        xAxis = navigator.xAxis;\n        yAxis = navigator.yAxis;\n        const { scrollbarHeight, scrollButtonSize } = navigator;\n        // Compute the top position\n        if (this.inverted) {\n            navigator.left = navigator.opposite ?\n                this.chartWidth - scrollbarHeight -\n                    navigator.height :\n                this.spacing[3] + scrollbarHeight;\n            navigator.top = this.plotTop + scrollButtonSize;\n        }\n        else {\n            navigator.left = pick(xAxis.left, this.plotLeft + scrollButtonSize);\n            navigator.top = navigator.navigatorOptions.top ||\n                this.chartHeight -\n                    navigator.height -\n                    scrollbarHeight -\n                    (this.scrollbar?.options.margin || 0) -\n                    this.spacing[2] -\n                    (this.rangeSelector && this.extraBottomMargin ?\n                        this.rangeSelector.getHeight() :\n                        0) -\n                    ((legendOptions &&\n                        legendOptions.verticalAlign === 'bottom' &&\n                        legendOptions.layout !== 'proximate' && // #13392\n                        legendOptions.enabled &&\n                        !legendOptions.floating) ?\n                        legend.legendHeight +\n                            pick(legendOptions.margin, 10) :\n                        0) -\n                    (this.titleOffset ? this.titleOffset[2] : 0);\n        }\n        if (xAxis && yAxis) { // False if navigator is disabled (#904)\n            if (this.inverted) {\n                xAxis.options.left = yAxis.options.left = navigator.left;\n            }\n            else {\n                xAxis.options.top = yAxis.options.top = navigator.top;\n            }\n            xAxis.setAxisSize();\n            yAxis.setAxisSize();\n        }\n    }\n}\n/**\n * Initialize navigator, if no scrolling exists yet.\n * @private\n */\nfunction onChartAfterUpdate(event) {\n    if (!this.navigator && !this.scroller &&\n        (this.options.navigator.enabled ||\n            this.options.scrollbar.enabled)) {\n        this.scroller = this.navigator = new NavigatorConstructor(this);\n        if (pick(event.redraw, true)) {\n            this.redraw(event.animation); // #7067\n        }\n    }\n}\n/**\n * Initialize navigator for stock charts\n * @private\n */\nfunction onChartBeforeRender() {\n    const options = this.options;\n    if (options.navigator.enabled ||\n        options.scrollbar.enabled) {\n        this.scroller = this.navigator = new NavigatorConstructor(this);\n    }\n}\n/**\n * For Stock charts. For x only zooming, do not to create the zoom button\n * because X axis zooming is already allowed by the Navigator and Range\n * selector. (#9285)\n * @private\n */\nfunction onChartBeforeShowResetZoom() {\n    const chartOptions = this.options, navigator = chartOptions.navigator, rangeSelector = chartOptions.rangeSelector;\n    if (((navigator && navigator.enabled) ||\n        (rangeSelector && rangeSelector.enabled)) &&\n        ((!isTouchDevice &&\n            this.zooming.type === 'x') ||\n            (isTouchDevice && this.zooming.pinchType === 'x'))) {\n        return false;\n    }\n}\n/**\n * @private\n */\nfunction onChartCallback(chart) {\n    const navigator = chart.navigator;\n    // Initialize the navigator\n    if (navigator && chart.xAxis[0]) {\n        const extremes = chart.xAxis[0].getExtremes();\n        navigator.render(extremes.min, extremes.max);\n    }\n}\n/**\n * Merge options, if no scrolling exists yet\n * @private\n */\nfunction onChartUpdate(e) {\n    const navigatorOptions = (e.options.navigator || {}), scrollbarOptions = (e.options.scrollbar || {});\n    if (!this.navigator && !this.scroller &&\n        (navigatorOptions.enabled || scrollbarOptions.enabled)) {\n        merge(true, this.options.navigator, navigatorOptions);\n        merge(true, this.options.scrollbar, scrollbarOptions);\n        delete e.options.navigator;\n        delete e.options.scrollbar;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst ChartNavigatorComposition = {\n    compose\n};\n/* harmony default export */ const Navigator_ChartNavigatorComposition = (ChartNavigatorComposition);\n\n;// ./code/es-modules/Core/Axis/NavigatorAxisComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isTouchDevice: NavigatorAxisComposition_isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: NavigatorAxisComposition_addEvent, correctFloat, defined, isNumber, pick: NavigatorAxisComposition_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onAxisInit() {\n    const axis = this;\n    if (!axis.navigatorAxis) {\n        axis.navigatorAxis = new NavigatorAxisAdditions(axis);\n    }\n}\n/**\n * For Stock charts, override selection zooming with some special features\n * because X axis zooming is already allowed by the Navigator and Range\n * selector.\n * @private\n */\nfunction onAxisSetExtremes(e) {\n    const axis = this, chart = axis.chart, chartOptions = chart.options, navigator = chartOptions.navigator, navigatorAxis = axis.navigatorAxis, pinchType = chart.zooming.pinchType, rangeSelector = chartOptions.rangeSelector, zoomType = chart.zooming.type;\n    let zoomed;\n    if (axis.isXAxis &&\n        (navigator?.enabled || rangeSelector?.enabled)) {\n        // For y only zooming, ignore the X axis completely\n        if (zoomType === 'y' && e.trigger === 'zoom') {\n            zoomed = false;\n            // For xy zooming, record the state of the zoom before zoom selection,\n            // then when the reset button is pressed, revert to this state. This\n            // should apply only if the chart is initialized with a range (#6612),\n            // otherwise zoom all the way out.\n        }\n        else if (((e.trigger === 'zoom' && zoomType === 'xy') ||\n            (NavigatorAxisComposition_isTouchDevice && pinchType === 'xy')) &&\n            axis.options.range) {\n            const previousZoom = navigatorAxis.previousZoom;\n            // Minimum defined, zooming in\n            if (defined(e.min)) {\n                navigatorAxis.previousZoom = [axis.min, axis.max];\n                // Minimum undefined, resetting zoom\n            }\n            else if (previousZoom) {\n                e.min = previousZoom[0];\n                e.max = previousZoom[1];\n                navigatorAxis.previousZoom = void 0;\n            }\n        }\n    }\n    if (typeof zoomed !== 'undefined') {\n        e.preventDefault();\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass NavigatorAxisAdditions {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    static compose(AxisClass) {\n        if (!AxisClass.keepProps.includes('navigatorAxis')) {\n            AxisClass.keepProps.push('navigatorAxis');\n            NavigatorAxisComposition_addEvent(AxisClass, 'init', onAxisInit);\n            NavigatorAxisComposition_addEvent(AxisClass, 'setExtremes', onAxisSetExtremes);\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(axis) {\n        this.axis = axis;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    destroy() {\n        this.axis = void 0;\n    }\n    /**\n     * Add logic to normalize the zoomed range in order to preserve the pressed\n     * state of range selector buttons\n     *\n     * @private\n     * @function Highcharts.Axis#toFixedRange\n     */\n    toFixedRange(pxMin, pxMax, fixedMin, fixedMax) {\n        const axis = this.axis, halfPointRange = (axis.pointRange || 0) / 2;\n        let newMin = NavigatorAxisComposition_pick(fixedMin, axis.translate(pxMin, true, !axis.horiz)), newMax = NavigatorAxisComposition_pick(fixedMax, axis.translate(pxMax, true, !axis.horiz));\n        // Add/remove half point range to/from the extremes (#1172)\n        if (!defined(fixedMin)) {\n            newMin = correctFloat(newMin + halfPointRange);\n        }\n        if (!defined(fixedMax)) {\n            newMax = correctFloat(newMax - halfPointRange);\n        }\n        if (!isNumber(newMin) || !isNumber(newMax)) { // #1195, #7411\n            newMin = newMax = void 0;\n        }\n        return {\n            min: newMin,\n            max: newMax\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const NavigatorAxisComposition = (NavigatorAxisAdditions);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Stock/Navigator/NavigatorDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { seriesTypes } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * The navigator is a small series below the main series, displaying\n * a view of the entire data set. It provides tools to zoom in and\n * out on parts of the data as well as panning across the dataset.\n *\n * @product      highstock gantt\n * @optionparent navigator\n */\nconst NavigatorDefaults = {\n    /**\n     * Whether the navigator and scrollbar should adapt to updated data\n     * in the base X axis. When loading data async, as in the demo below,\n     * this should be `false`. Otherwise new data will trigger navigator\n     * redraw, which will cause unwanted looping. In the demo below, the\n     * data in the navigator is set only once. On navigating, only the main\n     * chart content is updated.\n     *\n     * @sample {highstock} stock/demo/lazy-loading/\n     *         Set to false with async data loading\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption navigator.adaptToUpdatedData\n     */\n    /**\n     * An integer identifying the index to use for the base series, or a\n     * string representing the id of the series.\n     *\n     * **Note**: As of Highcharts 5.0, this is now a deprecated option.\n     * Prefer [series.showInNavigator](#plotOptions.series.showInNavigator).\n     *\n     * @see [series.showInNavigator](#plotOptions.series.showInNavigator)\n     *\n     * @deprecated\n     * @type      {number|string}\n     * @default   0\n     * @apioption navigator.baseSeries\n     */\n    /**\n     * Enable or disable the navigator.\n     *\n     * @sample {highstock} stock/navigator/enabled/\n     *         Disable the navigator\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption navigator.enabled\n     */\n    /**\n     * When the chart is inverted, whether to draw the navigator on the\n     * opposite side.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     5.0.8\n     * @apioption navigator.opposite\n     */\n    /**\n     * The height of the navigator.\n     *\n     * @sample {highstock} stock/navigator/height/\n     *         A higher navigator\n     */\n    height: 40,\n    /**\n     * The distance from the nearest element, the X axis or X axis labels.\n     *\n     * @sample {highstock} stock/navigator/margin/\n     *         A margin of 2 draws the navigator closer to the X axis labels\n     */\n    margin: 22,\n    /**\n     * Whether the mask should be inside the range marking the zoomed\n     * range, or outside. In Highcharts Stock 1.x it was always `false`.\n     *\n     * @sample {highstock} stock/demo/maskinside-false/\n     *         False, mask outside\n     *\n     * @since   2.0\n     */\n    maskInside: true,\n    /**\n     * Options for the handles for dragging the zoomed area.\n     *\n     * @sample {highstock} stock/navigator/handles/\n     *         Colored handles\n     */\n    handles: {\n        /**\n         * Width for handles.\n         *\n         * @sample {highstock} stock/navigator/styled-handles/\n         *         Styled handles\n         *\n         * @since   6.0.0\n         */\n        width: 7,\n        /**\n         * Border radius of the handles.\n         *\n         * @sample {highstock} stock/navigator/handles-border-radius/\n         *      Border radius on the navigator handles.\n         *\n         * @since 11.4.2\n         */\n        borderRadius: 0,\n        /**\n         * Height for handles.\n         *\n         * @sample {highstock} stock/navigator/styled-handles/\n         *         Styled handles\n         *\n         * @since   6.0.0\n         */\n        height: 15,\n        /**\n         * Array to define shapes of handles. 0-index for left, 1-index for\n         * right.\n         *\n         * Additionally, the URL to a graphic can be given on this form:\n         * `url(graphic.png)`. Note that for the image to be applied to\n         * exported charts, its URL needs to be accessible by the export\n         * server.\n         *\n         * Custom callbacks for symbol path generation can also be added to\n         * `Highcharts.SVGRenderer.prototype.symbols`. The callback is then\n         * used by its method name, as shown in the demo.\n         *\n         * @sample {highstock} stock/navigator/styled-handles/\n         *         Styled handles\n         *\n         * @type    {Array<string>}\n         * @default [\"navigator-handle\", \"navigator-handle\"]\n         * @since   6.0.0\n         */\n        symbols: ['navigator-handle', 'navigator-handle'],\n        /**\n         * Allows to enable/disable handles.\n         *\n         * @since   6.0.0\n         */\n        enabled: true,\n        /**\n         * The width for the handle border and the stripes inside.\n         *\n         * @sample {highstock} stock/navigator/styled-handles/\n         *         Styled handles\n         *\n         * @since     6.0.0\n         * @apioption navigator.handles.lineWidth\n         */\n        lineWidth: 1,\n        /**\n         * The fill for the handle.\n         *\n         * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        backgroundColor: \"#f2f2f2\" /* Palette.neutralColor5 */,\n        /**\n         * The stroke for the handle border and the stripes inside.\n         *\n         * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        borderColor: \"#999999\" /* Palette.neutralColor40 */\n    },\n    /**\n     * The color of the mask covering the areas of the navigator series\n     * that are currently not visible in the main series. The default\n     * color is bluish with an opacity of 0.3 to see the series below.\n     *\n     * @see In styled mode, the mask is styled with the\n     *      `.highcharts-navigator-mask` and\n     *      `.highcharts-navigator-mask-inside` classes.\n     *\n     * @sample {highstock} stock/navigator/maskfill/\n     *         Blue, semi transparent mask\n     *\n     * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @default rgba(102,133,194,0.3)\n     */\n    maskFill: color(\"#667aff\" /* Palette.highlightColor60 */).setOpacity(0.3).get(),\n    /**\n     * The color of the line marking the currently zoomed area in the\n     * navigator.\n     *\n     * @sample {highstock} stock/navigator/outline/\n     *         2px blue outline\n     *\n     * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @default #cccccc\n     */\n    outlineColor: \"#999999\" /* Palette.neutralColor40 */,\n    /**\n     * The width of the line marking the currently zoomed area in the\n     * navigator.\n     *\n     * @see In styled mode, the outline stroke width is set with the\n     *      `.highcharts-navigator-outline` class.\n     *\n     * @sample {highstock} stock/navigator/outline/\n     *         2px blue outline\n     *\n     * @type    {number}\n     */\n    outlineWidth: 1,\n    /**\n     * Options for the navigator series. Available options are the same\n     * as any series, documented at [plotOptions](#plotOptions.series)\n     * and [series](#series).\n     *\n     * Unless data is explicitly defined on navigator.series, the data\n     * is borrowed from the first series in the chart.\n     *\n     * Default series options for the navigator series are:\n     * ```js\n     * series: {\n     *     type: 'areaspline',\n     *     fillOpacity: 0.05,\n     *     dataGrouping: {\n     *         smoothed: true\n     *     },\n     *     lineWidth: 1,\n     *     marker: {\n     *         enabled: false\n     *     }\n     * }\n     * ```\n     *\n     * @see In styled mode, the navigator series is styled with the\n     *      `.highcharts-navigator-series` class.\n     *\n     * @sample {highstock} stock/navigator/series-data/\n     *         Using a separate data set for the navigator\n     * @sample {highstock} stock/navigator/series/\n     *         A green navigator series\n     *\n     * @type {*|Array<*>|Highcharts.SeriesOptionsType|Array<Highcharts.SeriesOptionsType>}\n     */\n    series: {\n        /**\n         * The type of the navigator series.\n         *\n         * Heads up:\n         * In column-type navigator, zooming is limited to at least one\n         * point with its `pointRange`.\n         *\n         * @sample {highstock} stock/navigator/column/\n         *         Column type navigator\n         *\n         * @type    {string}\n         * @default {highstock} `areaspline` if defined, otherwise `line`\n         * @default {gantt} gantt\n         */\n        type: (typeof seriesTypes.areaspline === 'undefined' ?\n            'line' :\n            'areaspline'),\n        /**\n         * The fill opacity of the navigator series.\n         */\n        fillOpacity: 0.05,\n        /**\n         * The pixel line width of the navigator series.\n         */\n        lineWidth: 1,\n        /**\n         * @ignore-option\n         */\n        compare: null,\n        /**\n         * @ignore-option\n         */\n        sonification: {\n            enabled: false\n        },\n        /**\n         * Unless data is explicitly defined, the data is borrowed from the\n         * first series in the chart.\n         *\n         * @type      {Array<number|Array<number|string|null>|object|null>}\n         * @product   highstock\n         * @apioption navigator.series.data\n         */\n        /**\n         * Data grouping options for the navigator series.\n         *\n         * @extends plotOptions.series.dataGrouping\n         */\n        dataGrouping: {\n            approximation: 'average',\n            enabled: true,\n            groupPixelWidth: 2,\n            // Replace smoothed property by anchors, #12455.\n            firstAnchor: 'firstPoint',\n            anchor: 'middle',\n            lastAnchor: 'lastPoint',\n            // Day and week differs from plotOptions.series.dataGrouping\n            units: [\n                ['millisecond', [1, 2, 5, 10, 20, 25, 50, 100, 200, 500]],\n                ['second', [1, 2, 5, 10, 15, 30]],\n                ['minute', [1, 2, 5, 10, 15, 30]],\n                ['hour', [1, 2, 3, 4, 6, 8, 12]],\n                ['day', [1, 2, 3, 4]],\n                ['week', [1, 2, 3]],\n                ['month', [1, 3, 6]],\n                ['year', null]\n            ]\n        },\n        /**\n         * Data label options for the navigator series. Data labels are\n         * disabled by default on the navigator series.\n         *\n         * @extends plotOptions.series.dataLabels\n         */\n        dataLabels: {\n            enabled: false,\n            zIndex: 2 // #1839\n        },\n        id: 'highcharts-navigator-series',\n        className: 'highcharts-navigator-series',\n        /**\n         * Sets the fill color of the navigator series.\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @apioption navigator.series.color\n         */\n        /**\n         * Line color for the navigator series. Allows setting the color\n         * while disallowing the default candlestick setting.\n         *\n         * @type {Highcharts.ColorString|null}\n         */\n        lineColor: null, // #4602\n        marker: {\n            enabled: false\n        },\n        /**\n         * Since Highcharts Stock v8, default value is the same as default\n         * `pointRange` defined for a specific type (e.g. `null` for\n         * column type).\n         *\n         * In Highcharts Stock version < 8, defaults to 0.\n         *\n         * @extends plotOptions.series.pointRange\n         * @type {number|null}\n         * @apioption navigator.series.pointRange\n         */\n        /**\n         * The threshold option. Setting it to 0 will make the default\n         * navigator area series draw its area from the 0 value and up.\n         *\n         * @type {number|null}\n         */\n        threshold: null\n    },\n    /**\n     * Enable or disable navigator sticking to right, while adding new\n     * points. If `undefined`, the navigator sticks to the axis maximum only\n     * if it was already at the maximum prior to adding points.\n     *\n     * @type      {boolean}\n     * @default   undefined\n     * @since 10.2.1\n     * @sample {highstock} stock/navigator/sticktomax-false/\n     * stickToMax set to false\n     * @apioption navigator.stickToMax\n     */\n    /**\n     * Options for the navigator X axis. Default series options for the\n     * navigator xAxis are:\n     * ```js\n     * xAxis: {\n     *     tickWidth: 0,\n     *     lineWidth: 0,\n     *     gridLineWidth: 1,\n     *     tickPixelInterval: 200,\n     *     labels: {\n     *            align: 'left',\n     *         style: {\n     *             color: '#888'\n     *         },\n     *         x: 3,\n     *         y: -4\n     *     }\n     * }\n     * ```\n     *\n     * @extends   xAxis\n     * @excluding linkedTo, maxZoom, minRange, opposite, range, scrollbar,\n     *            showEmpty, maxRange\n     */\n    xAxis: {\n        /**\n         * Additional range on the right side of the xAxis. Works similar to\n         * `xAxis.maxPadding`, but the value is set in terms of axis values,\n         * percentage or pixels.\n         *\n         * If it's a number, it is interpreted as axis values, which in a\n         * datetime axis equals milliseconds.\n         *\n         * If it's a percentage string, is interpreted as percentages of the\n         * axis length. An overscroll of 50% will make a 100px axis 50px longer.\n         *\n         * If it's a pixel string, it is interpreted as a fixed pixel value, but\n         * limited to 90% of the axis length.\n         *\n         * If it's undefined, the value is inherited from `xAxis.overscroll`.\n         *\n         * Can be set for both, main xAxis and navigator's xAxis.\n         *\n         * @type    {number | string | undefined}\n         * @since   6.0.0\n         * @apioption navigator.xAxis.overscroll\n         */\n        className: 'highcharts-navigator-xaxis',\n        tickLength: 0,\n        lineWidth: 0,\n        gridLineColor: \"#e6e6e6\" /* Palette.neutralColor10 */,\n        id: 'navigator-x-axis',\n        gridLineWidth: 1,\n        tickPixelInterval: 200,\n        labels: {\n            align: 'left',\n            /**\n             * @type {Highcharts.CSSObject}\n             */\n            style: {\n                /** @ignore */\n                color: \"#000000\" /* Palette.neutralColor100 */,\n                /** @ignore */\n                fontSize: '0.7em',\n                /** @ignore */\n                opacity: 0.6,\n                /** @ignore */\n                textOutline: '2px contrast'\n            },\n            x: 3,\n            y: -4\n        },\n        crosshair: false\n    },\n    /**\n     * Options for the navigator Y axis. Default series options for the\n     * navigator yAxis are:\n     * ```js\n     * yAxis: {\n     *     gridLineWidth: 0,\n     *     startOnTick: false,\n     *     endOnTick: false,\n     *     minPadding: 0.1,\n     *     maxPadding: 0.1,\n     *     labels: {\n     *         enabled: false\n     *     },\n     *     title: {\n     *         text: null\n     *     },\n     *     tickWidth: 0\n     * }\n     * ```\n     *\n     * @extends   yAxis\n     * @excluding height, linkedTo, maxZoom, minRange, ordinal, range,\n     *            showEmpty, scrollbar, top, units, maxRange, minLength,\n     *            maxLength, resize\n     */\n    yAxis: {\n        className: 'highcharts-navigator-yaxis',\n        gridLineWidth: 0,\n        startOnTick: false,\n        endOnTick: false,\n        minPadding: 0.1,\n        id: 'navigator-y-axis',\n        maxPadding: 0.1,\n        labels: {\n            enabled: false\n        },\n        crosshair: false,\n        title: {\n            text: void 0\n        },\n        tickLength: 0,\n        tickWidth: 0\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Navigator_NavigatorDefaults = (NavigatorDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Maximum range which can be set using the navigator's handles.\n * Opposite of [xAxis.minRange](#xAxis.minRange).\n *\n * @sample {highstock} stock/navigator/maxrange/\n *         Defined max and min range\n *\n * @type      {number}\n * @since     6.0.0\n * @product   highstock gantt\n * @apioption xAxis.maxRange\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Core/Renderer/SVG/Symbols.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: Symbols_defined, isNumber: Symbols_isNumber, pick: Symbols_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable require-jsdoc, valid-jsdoc */\n/**\n *\n */\nfunction arc(cx, cy, w, h, options) {\n    const arc = [];\n    if (options) {\n        let start = options.start || 0, end = options.end || 0;\n        const rx = Symbols_pick(options.r, w), ry = Symbols_pick(options.r, h || w), \n        // Subtract a small number to prevent cos and sin of start and end\n        // from becoming equal on 360 arcs (#1561). The size of the circle\n        // affects the constant, therefore the division by `rx`. If the\n        // proximity is too small, the arc disappears. If it is too great, a\n        // gap appears. This can be seen in the animation of the official\n        // bubble demo (#20585).\n        proximity = 0.0002 / (options.borderRadius ? 1 : Math.max(rx, 1)), fullCircle = (Math.abs(end - start - 2 * Math.PI) <\n            proximity);\n        if (fullCircle) {\n            start = Math.PI / 2;\n            end = Math.PI * 2.5 - proximity;\n        }\n        const innerRadius = options.innerR, open = Symbols_pick(options.open, fullCircle), cosStart = Math.cos(start), sinStart = Math.sin(start), cosEnd = Math.cos(end), sinEnd = Math.sin(end), \n        // Proximity takes care of rounding errors around PI (#6971)\n        longArc = Symbols_pick(options.longArc, end - start - Math.PI < proximity ? 0 : 1);\n        let arcSegment = [\n            'A', // ArcTo\n            rx, // X radius\n            ry, // Y radius\n            0, // Slanting\n            longArc, // Long or short arc\n            Symbols_pick(options.clockwise, 1), // Clockwise\n            cx + rx * cosEnd,\n            cy + ry * sinEnd\n        ];\n        arcSegment.params = { start, end, cx, cy }; // Memo for border radius\n        arc.push([\n            'M',\n            cx + rx * cosStart,\n            cy + ry * sinStart\n        ], arcSegment);\n        if (Symbols_defined(innerRadius)) {\n            arcSegment = [\n                'A', // ArcTo\n                innerRadius, // X radius\n                innerRadius, // Y radius\n                0, // Slanting\n                longArc, // Long or short arc\n                // Clockwise - opposite to the outer arc clockwise\n                Symbols_defined(options.clockwise) ? 1 - options.clockwise : 0,\n                cx + innerRadius * cosStart,\n                cy + innerRadius * sinStart\n            ];\n            // Memo for border radius\n            arcSegment.params = {\n                start: end,\n                end: start,\n                cx,\n                cy\n            };\n            arc.push(open ?\n                [\n                    'M',\n                    cx + innerRadius * cosEnd,\n                    cy + innerRadius * sinEnd\n                ] : [\n                'L',\n                cx + innerRadius * cosEnd,\n                cy + innerRadius * sinEnd\n            ], arcSegment);\n        }\n        if (!open) {\n            arc.push(['Z']);\n        }\n    }\n    return arc;\n}\n/**\n * Callout shape used for default tooltips.\n */\nfunction callout(x, y, w, h, options) {\n    const arrowLength = 6, halfDistance = 6, r = Math.min((options?.r) || 0, w, h), safeDistance = r + halfDistance, anchorX = options?.anchorX, anchorY = options?.anchorY || 0;\n    const path = roundedRect(x, y, w, h, { r });\n    if (!Symbols_isNumber(anchorX)) {\n        return path;\n    }\n    // Do not render a connector, if anchor starts inside the label\n    if (anchorX < w && anchorX > 0 && anchorY < h && anchorY > 0) {\n        return path;\n    }\n    // Anchor on right side\n    if (x + anchorX > w - safeDistance) {\n        // Chevron\n        if (anchorY > y + safeDistance &&\n            anchorY < y + h - safeDistance) {\n            path.splice(3, 1, ['L', x + w, anchorY - halfDistance], ['L', x + w + arrowLength, anchorY], ['L', x + w, anchorY + halfDistance], ['L', x + w, y + h - r]);\n            // Simple connector\n        }\n        else {\n            if (anchorX < w) { // Corner connector\n                const isTopCorner = anchorY < y + safeDistance, cornerY = isTopCorner ? y : y + h, sliceStart = isTopCorner ? 2 : 5;\n                path.splice(sliceStart, 0, ['L', anchorX, anchorY], ['L', x + w - r, cornerY]);\n            }\n            else { // Side connector\n                path.splice(3, 1, ['L', x + w, h / 2], ['L', anchorX, anchorY], ['L', x + w, h / 2], ['L', x + w, y + h - r]);\n            }\n        }\n        // Anchor on left side\n    }\n    else if (x + anchorX < safeDistance) {\n        // Chevron\n        if (anchorY > y + safeDistance &&\n            anchorY < y + h - safeDistance) {\n            path.splice(7, 1, ['L', x, anchorY + halfDistance], ['L', x - arrowLength, anchorY], ['L', x, anchorY - halfDistance], ['L', x, y + r]);\n            // Simple connector\n        }\n        else {\n            if (anchorX > 0) { // Corner connector\n                const isTopCorner = anchorY < y + safeDistance, cornerY = isTopCorner ? y : y + h, sliceStart = isTopCorner ? 1 : 6;\n                path.splice(sliceStart, 0, ['L', anchorX, anchorY], ['L', x + r, cornerY]);\n            }\n            else { // Side connector\n                path.splice(7, 1, ['L', x, h / 2], ['L', anchorX, anchorY], ['L', x, h / 2], ['L', x, y + r]);\n            }\n        }\n    }\n    else if ( // Replace bottom\n    anchorY > h &&\n        anchorX < w - safeDistance) {\n        path.splice(5, 1, ['L', anchorX + halfDistance, y + h], ['L', anchorX, y + h + arrowLength], ['L', anchorX - halfDistance, y + h], ['L', x + r, y + h]);\n    }\n    else if ( // Replace top\n    anchorY < 0 &&\n        anchorX > safeDistance) {\n        path.splice(1, 1, ['L', anchorX - halfDistance, y], ['L', anchorX, y - arrowLength], ['L', anchorX + halfDistance, y], ['L', w - r, y]);\n    }\n    return path;\n}\n/**\n *\n */\nfunction circle(x, y, w, h) {\n    // Return a full arc\n    return arc(x + w / 2, y + h / 2, w / 2, h / 2, {\n        start: Math.PI * 0.5,\n        end: Math.PI * 2.5,\n        open: false\n    });\n}\n/**\n *\n */\nfunction diamond(x, y, w, h) {\n    return [\n        ['M', x + w / 2, y],\n        ['L', x + w, y + h / 2],\n        ['L', x + w / 2, y + h],\n        ['L', x, y + h / 2],\n        ['Z']\n    ];\n}\n// #15291\n/**\n *\n */\nfunction rect(x, y, w, h, options) {\n    if (options?.r) {\n        return roundedRect(x, y, w, h, options);\n    }\n    return [\n        ['M', x, y],\n        ['L', x + w, y],\n        ['L', x + w, y + h],\n        ['L', x, y + h],\n        ['Z']\n    ];\n}\n/**\n *\n */\nfunction roundedRect(x, y, w, h, options) {\n    const r = options?.r || 0;\n    return [\n        ['M', x + r, y],\n        ['L', x + w - r, y], // Top side\n        ['A', r, r, 0, 0, 1, x + w, y + r], // Top-right corner\n        ['L', x + w, y + h - r], // Right side\n        ['A', r, r, 0, 0, 1, x + w - r, y + h], // Bottom-right corner\n        ['L', x + r, y + h], // Bottom side\n        ['A', r, r, 0, 0, 1, x, y + h - r], // Bottom-left corner\n        ['L', x, y + r], // Left side\n        ['A', r, r, 0, 0, 1, x + r, y],\n        ['Z'] // Top-left corner\n    ];\n}\n/**\n *\n */\nfunction triangle(x, y, w, h) {\n    return [\n        ['M', x + w / 2, y],\n        ['L', x + w, y + h],\n        ['L', x, y + h],\n        ['Z']\n    ];\n}\n/**\n *\n */\nfunction triangleDown(x, y, w, h) {\n    return [\n        ['M', x, y],\n        ['L', x + w, y],\n        ['L', x + w / 2, y + h],\n        ['Z']\n    ];\n}\nconst Symbols = {\n    arc,\n    callout,\n    circle,\n    diamond,\n    rect,\n    roundedRect,\n    square: rect,\n    triangle,\n    'triangle-down': triangleDown\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SVG_Symbols = (Symbols);\n\n;// ./code/es-modules/Stock/Navigator/NavigatorSymbols.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Draw one of the handles on the side of the zoomed range in the navigator.\n * @private\n */\nfunction navigatorHandle(_x, _y, width, height, options = {}) {\n    const halfWidth = options.width ? options.width / 2 : width, markerPosition = 1.5, r = relativeLength(options.borderRadius || 0, Math.min(halfWidth * 2, height));\n    height = options.height || height;\n    return [\n        ['M', -markerPosition, height / 2 - 3.5],\n        ['L', -markerPosition, height / 2 + 4.5],\n        ['M', markerPosition - 1, height / 2 - 3.5],\n        ['L', markerPosition - 1, height / 2 + 4.5],\n        ...SVG_Symbols.rect(-halfWidth - 1, 0.5, halfWidth * 2 + 1, height, { r })\n    ];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigatorSymbols = {\n    'navigator-handle': navigatorHandle\n};\n/* harmony default export */ const Navigator_NavigatorSymbols = (NavigatorSymbols);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n;// ./code/es-modules/Stock/Utilities/StockUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: StockUtilities_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Sets the chart.fixedRange to the specified value. If the value is larger\n * than actual range, sets it to the maximum possible range. (#20327)\n *\n * @private\n * @function Highcharts.StockChart#setFixedRange\n * @param {number|undefined} range\n *        Range to set in axis units.\n */\nfunction setFixedRange(range) {\n    const xAxis = this.xAxis[0];\n    if (StockUtilities_defined(xAxis.dataMax) &&\n        StockUtilities_defined(xAxis.dataMin) &&\n        range) {\n        this.fixedRange = Math.min(range, xAxis.dataMax - xAxis.dataMin);\n    }\n    else {\n        this.fixedRange = range;\n    }\n}\nconst StockUtilities = {\n    setFixedRange\n};\n/* harmony default export */ const Utilities_StockUtilities = (StockUtilities);\n\n;// ./code/es-modules/Stock/Navigator/NavigatorComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { getRendererType } = (highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default());\n\nconst { setFixedRange: NavigatorComposition_setFixedRange } = Utilities_StockUtilities;\n\nconst { addEvent: NavigatorComposition_addEvent, extend, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Variables\n *\n * */\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction NavigatorComposition_compose(ChartClass, AxisClass, SeriesClass) {\n    NavigatorAxisComposition.compose(AxisClass);\n    if (pushUnique(composed, 'Navigator')) {\n        ChartClass.prototype.setFixedRange = NavigatorComposition_setFixedRange;\n        extend(getRendererType().prototype.symbols, Navigator_NavigatorSymbols);\n        NavigatorComposition_addEvent(SeriesClass, 'afterUpdate', onSeriesAfterUpdate);\n        setOptions({ navigator: Navigator_NavigatorDefaults });\n    }\n}\n/**\n * Handle updating series\n * @private\n */\nfunction onSeriesAfterUpdate() {\n    if (this.chart.navigator && !this.options.isInternal) {\n        this.chart.navigator.setBaseSeries(null, false);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigatorComposition = {\n    compose: NavigatorComposition_compose\n};\n/* harmony default export */ const Navigator_NavigatorComposition = (NavigatorComposition);\n\n;// ./code/es-modules/Core/Axis/ScrollbarAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed: ScrollbarAxis_composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: ScrollbarAxis_addEvent, defined: ScrollbarAxis_defined, pick: ScrollbarAxis_pick, pushUnique: ScrollbarAxis_pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ScrollbarAxis;\n(function (ScrollbarAxis) {\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let Scrollbar;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Attaches to axis events to create scrollbars if enabled.\n     *\n     * @private\n     *\n     * @param {Highcharts.Axis} AxisClass\n     * Axis class to extend.\n     *\n     * @param {Highcharts.Scrollbar} ScrollbarClass\n     * Scrollbar class to use.\n     */\n    function compose(AxisClass, ScrollbarClass) {\n        if (ScrollbarAxis_pushUnique(ScrollbarAxis_composed, 'Axis.Scrollbar')) {\n            Scrollbar = ScrollbarClass;\n            ScrollbarAxis_addEvent(AxisClass, 'afterGetOffset', onAxisAfterGetOffset);\n            ScrollbarAxis_addEvent(AxisClass, 'afterInit', onAxisAfterInit);\n            ScrollbarAxis_addEvent(AxisClass, 'afterRender', onAxisAfterRender);\n        }\n    }\n    ScrollbarAxis.compose = compose;\n    /** @private */\n    function getExtremes(axis) {\n        const axisMin = ScrollbarAxis_pick(axis.options?.min, axis.min);\n        const axisMax = ScrollbarAxis_pick(axis.options?.max, axis.max);\n        return {\n            axisMin,\n            axisMax,\n            scrollMin: ScrollbarAxis_defined(axis.dataMin) ?\n                Math.min(axisMin, axis.min, axis.dataMin, ScrollbarAxis_pick(axis.threshold, Infinity)) : axisMin,\n            scrollMax: ScrollbarAxis_defined(axis.dataMax) ?\n                Math.max(axisMax, axis.max, axis.dataMax, ScrollbarAxis_pick(axis.threshold, -Infinity)) : axisMax\n        };\n    }\n    /**\n     * Make space for a scrollbar.\n     * @private\n     */\n    function onAxisAfterGetOffset() {\n        const axis = this, scrollbar = axis.scrollbar, opposite = scrollbar && !scrollbar.options.opposite, index = axis.horiz ? 2 : opposite ? 3 : 1;\n        if (scrollbar) {\n            // Reset scrollbars offsets\n            axis.chart.scrollbarsOffsets = [0, 0];\n            axis.chart.axisOffset[index] +=\n                scrollbar.size + (scrollbar.options.margin || 0);\n        }\n    }\n    /**\n     * Wrap axis initialization and create scrollbar if enabled.\n     * @private\n     */\n    function onAxisAfterInit() {\n        const axis = this;\n        if (axis.options?.scrollbar?.enabled) {\n            // Predefined options:\n            axis.options.scrollbar.vertical = !axis.horiz;\n            axis.options.startOnTick = axis.options.endOnTick = false;\n            axis.scrollbar = new Scrollbar(axis.chart.renderer, axis.options.scrollbar, axis.chart);\n            ScrollbarAxis_addEvent(axis.scrollbar, 'changed', function (e) {\n                const { axisMin, axisMax, scrollMin: unitedMin, scrollMax: unitedMax } = getExtremes(axis), range = unitedMax - unitedMin;\n                let to, from;\n                // #12834, scroll when show/hide series, wrong extremes\n                if (!ScrollbarAxis_defined(axisMin) || !ScrollbarAxis_defined(axisMax)) {\n                    return;\n                }\n                if ((axis.horiz && !axis.reversed) ||\n                    (!axis.horiz && axis.reversed)) {\n                    to = unitedMin + range * this.to;\n                    from = unitedMin + range * this.from;\n                }\n                else {\n                    // Y-values in browser are reversed, but this also\n                    // applies for reversed horizontal axis:\n                    to = unitedMin + range * (1 - this.from);\n                    from = unitedMin + range * (1 - this.to);\n                }\n                if (this.shouldUpdateExtremes(e.DOMType)) {\n                    // #17977, set animation to undefined instead of true\n                    const animate = e.DOMType === 'mousemove' ||\n                        e.DOMType === 'touchmove' ? false : void 0;\n                    axis.setExtremes(from, to, true, animate, e);\n                }\n                else {\n                    // When live redraw is disabled, don't change extremes\n                    // Only change the position of the scrollbar thumb\n                    this.setRange(this.from, this.to);\n                }\n            });\n        }\n    }\n    /**\n     * Wrap rendering axis, and update scrollbar if one is created.\n     * @private\n     */\n    function onAxisAfterRender() {\n        const axis = this, { scrollMin, scrollMax } = getExtremes(axis), scrollbar = axis.scrollbar, offset = (axis.axisTitleMargin + (axis.titleOffset || 0)), scrollbarsOffsets = axis.chart.scrollbarsOffsets, axisMargin = axis.options.margin || 0;\n        let offsetsIndex, from, to;\n        if (scrollbar && scrollbarsOffsets) {\n            if (axis.horiz) {\n                // Reserve space for labels/title\n                if (!axis.opposite) {\n                    scrollbarsOffsets[1] += offset;\n                }\n                scrollbar.position(axis.left, (axis.top +\n                    axis.height +\n                    2 +\n                    scrollbarsOffsets[1] -\n                    (axis.opposite ? axisMargin : 0)), axis.width, axis.height);\n                // Next scrollbar should reserve space for margin (if set)\n                if (!axis.opposite) {\n                    scrollbarsOffsets[1] += axisMargin;\n                }\n                offsetsIndex = 1;\n            }\n            else {\n                // Reserve space for labels/title\n                if (axis.opposite) {\n                    scrollbarsOffsets[0] += offset;\n                }\n                let xPosition;\n                if (!scrollbar.options.opposite) {\n                    xPosition = axis.opposite ? 0 : axisMargin;\n                }\n                else {\n                    xPosition = axis.left +\n                        axis.width +\n                        2 +\n                        scrollbarsOffsets[0] -\n                        (axis.opposite ? 0 : axisMargin);\n                }\n                scrollbar.position(xPosition, axis.top, axis.width, axis.height);\n                // Next scrollbar should reserve space for margin (if set)\n                if (axis.opposite) {\n                    scrollbarsOffsets[0] += axisMargin;\n                }\n                offsetsIndex = 0;\n            }\n            scrollbarsOffsets[offsetsIndex] += scrollbar.size +\n                (scrollbar.options.margin || 0);\n            if (isNaN(scrollMin) ||\n                isNaN(scrollMax) ||\n                !ScrollbarAxis_defined(axis.min) ||\n                !ScrollbarAxis_defined(axis.max) ||\n                axis.dataMin === axis.dataMax // #10733\n            ) {\n                // Default action: when data extremes are the same or there is\n                // not extremes on the axis, but scrollbar exists, make it\n                // full size\n                scrollbar.setRange(0, 1);\n            }\n            else if (axis.min === axis.max) { // #20359\n                // When the extremes are the same, set the scrollbar to a point\n                // within the extremes range. Utilize pointRange to perform the\n                // calculations. (#20359)\n                const interval = axis.pointRange / (axis.dataMax +\n                    1);\n                from = interval * axis.min;\n                to = interval * (axis.max + 1);\n                scrollbar.setRange(from, to);\n            }\n            else {\n                from = ((axis.min - scrollMin) /\n                    (scrollMax - scrollMin));\n                to = ((axis.max - scrollMin) /\n                    (scrollMax - scrollMin));\n                if ((axis.horiz && !axis.reversed) ||\n                    (!axis.horiz && axis.reversed)) {\n                    scrollbar.setRange(from, to);\n                }\n                else {\n                    // Inverse vertical axis\n                    scrollbar.setRange(1 - to, 1 - from);\n                }\n            }\n        }\n    }\n})(ScrollbarAxis || (ScrollbarAxis = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Axis_ScrollbarAxis = (ScrollbarAxis);\n\n;// ./code/es-modules/Stock/Scrollbar/ScrollbarDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constant\n *\n * */\n/**\n *\n * The scrollbar is a means of panning over the X axis of a stock chart.\n * Scrollbars can also be applied to other types of axes.\n *\n * Another approach to scrollable charts is the [chart.scrollablePlotArea](\n * https://api.highcharts.com/highcharts/chart.scrollablePlotArea) option that\n * is especially suitable for simpler cartesian charts on mobile.\n *\n * In styled mode, all the presentational options for the\n * scrollbar are replaced by the classes `.highcharts-scrollbar-thumb`,\n * `.highcharts-scrollbar-arrow`, `.highcharts-scrollbar-button`,\n * `.highcharts-scrollbar-rifles` and `.highcharts-scrollbar-track`.\n *\n * @sample stock/yaxis/inverted-bar-scrollbar/\n *         A scrollbar on a simple bar chart\n *\n * @product highstock gantt\n * @optionparent scrollbar\n *\n * @private\n */\nconst ScrollbarDefaults = {\n    /**\n     * The height of the scrollbar. If `buttonsEnabled` is true , the height\n     * also applies to the width of the scroll arrows so that they are always\n     * squares.\n     *\n     * @sample stock/scrollbar/style/\n     *         Non-default height\n     *\n     * @type    {number}\n     */\n    height: 10,\n    /**\n     * The border rounding radius of the bar.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     */\n    barBorderRadius: 5,\n    /**\n     * The corner radius of the scrollbar buttons.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     */\n    buttonBorderRadius: 0,\n    /**\n     * Enable or disable the buttons at the end of the scrollbar.\n     *\n     * @since 11.0.0\n     */\n    buttonsEnabled: false,\n    /**\n     * Enable or disable the scrollbar.\n     *\n     * @sample stock/scrollbar/enabled/\n     *         Disable the scrollbar, only use navigator\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption scrollbar.enabled\n     */\n    /**\n     * Whether to redraw the main chart as the scrollbar or the navigator\n     * zoomed window is moved. Defaults to `true` for modern browsers and\n     * `false` for legacy IE browsers as well as mobile devices.\n     *\n     * @sample stock/scrollbar/liveredraw\n     *         Setting live redraw to false\n     *\n     * @type  {boolean}\n     * @since 1.3\n     */\n    liveRedraw: void 0,\n    /**\n     * The margin between the scrollbar and its axis when the scrollbar is\n     * applied directly to an axis, or the navigator in case that is enabled.\n     * Defaults to 10 for axis, 3 for navigator.\n     *\n     * @type {number|undefined}\n     */\n    margin: void 0,\n    /**\n     * The minimum width of the scrollbar.\n     *\n     * @since 1.2.5\n     */\n    minWidth: 6,\n    /** @ignore-option */\n    opposite: true,\n    /**\n     * Whether to show or hide the scrollbar when the scrolled content is\n     * zoomed out to it full extent.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption scrollbar.showFull\n     */\n    step: 0.2,\n    /**\n     * The z index of the scrollbar group.\n     */\n    zIndex: 3,\n    /**\n     * The background color of the scrollbar itself.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    barBackgroundColor: \"#cccccc\" /* Palette.neutralColor20 */,\n    /**\n     * The width of the bar's border.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     */\n    barBorderWidth: 0,\n    /**\n     * The color of the scrollbar's border.\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    barBorderColor: \"#cccccc\" /* Palette.neutralColor20 */,\n    /**\n     * The color of the small arrow inside the scrollbar buttons.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    buttonArrowColor: \"#333333\" /* Palette.neutralColor80 */,\n    /**\n     * The color of scrollbar buttons.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    buttonBackgroundColor: \"#e6e6e6\" /* Palette.neutralColor10 */,\n    /**\n     * The color of the border of the scrollbar buttons.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    buttonBorderColor: \"#cccccc\" /* Palette.neutralColor20 */,\n    /**\n     * The border width of the scrollbar buttons.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     */\n    buttonBorderWidth: 1,\n    /**\n     * The color of the small rifles in the middle of the scrollbar.\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    rifleColor: 'none',\n    /**\n     * The color of the track background.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    trackBackgroundColor: 'rgba(255, 255, 255, 0.001)', // #18922\n    /**\n     * The color of the border of the scrollbar track.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    trackBorderColor: \"#cccccc\" /* Palette.neutralColor20 */,\n    /**\n     * The corner radius of the border of the scrollbar track.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     */\n    trackBorderRadius: 5,\n    /**\n     * The width of the border of the scrollbar track.\n     *\n     * @sample stock/scrollbar/style/\n     *         Scrollbar styling\n     */\n    trackBorderWidth: 1\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Scrollbar_ScrollbarDefaults = (ScrollbarDefaults);\n\n;// ./code/es-modules/Stock/Scrollbar/Scrollbar.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { addEvent: Scrollbar_addEvent, correctFloat: Scrollbar_correctFloat, crisp, defined: Scrollbar_defined, destroyObjectProperties, fireEvent, merge: Scrollbar_merge, pick: Scrollbar_pick, removeEvent } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/* eslint-disable no-invalid-this, valid-jsdoc */\n/**\n * A reusable scrollbar, internally used in Highcharts Stock's\n * navigator and optionally on individual axes.\n *\n * @private\n * @class\n * @name Highcharts.Scrollbar\n * @param {Highcharts.SVGRenderer} renderer\n * @param {Highcharts.ScrollbarOptions} options\n * @param {Highcharts.Chart} chart\n */\nclass Scrollbar {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AxisClass) {\n        Axis_ScrollbarAxis.compose(AxisClass, Scrollbar);\n    }\n    /**\n     * When we have vertical scrollbar, rifles and arrow in buttons should be\n     * rotated. The same method is used in Navigator's handles, to rotate them.\n     *\n     * @function Highcharts.swapXY\n     *\n     * @param {Highcharts.SVGPathArray} path\n     * Path to be rotated.\n     *\n     * @param {boolean} [vertical]\n     * If vertical scrollbar, swap x-y values.\n     *\n     * @return {Highcharts.SVGPathArray}\n     * Rotated path.\n     *\n     * @requires modules/stock\n     */\n    static swapXY(path, vertical) {\n        if (vertical) {\n            path.forEach((seg) => {\n                const len = seg.length;\n                let temp;\n                for (let i = 0; i < len; i += 2) {\n                    temp = seg[i + 1];\n                    if (typeof temp === 'number') {\n                        seg[i + 1] = seg[i + 2];\n                        seg[i + 2] = temp;\n                    }\n                }\n            });\n        }\n        return path;\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(renderer, options, chart) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this._events = [];\n        this.chartX = 0;\n        this.chartY = 0;\n        this.from = 0;\n        this.scrollbarButtons = [];\n        this.scrollbarLeft = 0;\n        this.scrollbarStrokeWidth = 1;\n        this.scrollbarTop = 0;\n        this.size = 0;\n        this.to = 0;\n        this.trackBorderWidth = 1;\n        this.x = 0;\n        this.y = 0;\n        this.init(renderer, options, chart);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Set up the mouse and touch events for the Scrollbar\n     *\n     * @private\n     * @function Highcharts.Scrollbar#addEvents\n     */\n    addEvents() {\n        const buttonsOrder = this.options.inverted ? [1, 0] : [0, 1], buttons = this.scrollbarButtons, bar = this.scrollbarGroup.element, track = this.track.element, mouseDownHandler = this.mouseDownHandler.bind(this), mouseMoveHandler = this.mouseMoveHandler.bind(this), mouseUpHandler = this.mouseUpHandler.bind(this);\n        const _events = [\n            // Mouse events\n            [\n                buttons[buttonsOrder[0]].element,\n                'click',\n                this.buttonToMinClick.bind(this)\n            ],\n            [\n                buttons[buttonsOrder[1]].element,\n                'click',\n                this.buttonToMaxClick.bind(this)\n            ],\n            [track, 'click', this.trackClick.bind(this)],\n            [bar, 'mousedown', mouseDownHandler],\n            [bar.ownerDocument, 'mousemove', mouseMoveHandler],\n            [bar.ownerDocument, 'mouseup', mouseUpHandler],\n            // Touch events\n            [bar, 'touchstart', mouseDownHandler],\n            [bar.ownerDocument, 'touchmove', mouseMoveHandler],\n            [bar.ownerDocument, 'touchend', mouseUpHandler]\n        ];\n        // Add them all\n        _events.forEach(function (args) {\n            Scrollbar_addEvent.apply(null, args);\n        });\n        this._events = _events;\n    }\n    buttonToMaxClick(e) {\n        const scroller = this;\n        const range = ((scroller.to - scroller.from) *\n            Scrollbar_pick(scroller.options.step, 0.2));\n        scroller.updatePosition(scroller.from + range, scroller.to + range);\n        fireEvent(scroller, 'changed', {\n            from: scroller.from,\n            to: scroller.to,\n            trigger: 'scrollbar',\n            DOMEvent: e\n        });\n    }\n    buttonToMinClick(e) {\n        const scroller = this;\n        const range = Scrollbar_correctFloat(scroller.to - scroller.from) *\n            Scrollbar_pick(scroller.options.step, 0.2);\n        scroller.updatePosition(Scrollbar_correctFloat(scroller.from - range), Scrollbar_correctFloat(scroller.to - range));\n        fireEvent(scroller, 'changed', {\n            from: scroller.from,\n            to: scroller.to,\n            trigger: 'scrollbar',\n            DOMEvent: e\n        });\n    }\n    /**\n     * Get normalized (0-1) cursor position over the scrollbar\n     *\n     * @private\n     * @function Highcharts.Scrollbar#cursorToScrollbarPosition\n     *\n     * @param  {*} normalizedEvent\n     *         normalized event, with chartX and chartY values\n     *\n     * @return {Highcharts.Dictionary<number>}\n     *         Local position {chartX, chartY}\n     */\n    cursorToScrollbarPosition(normalizedEvent) {\n        const scroller = this, options = scroller.options, minWidthDifference = options.minWidth > scroller.calculatedWidth ?\n            options.minWidth :\n            0; // `minWidth` distorts translation\n        return {\n            chartX: (normalizedEvent.chartX - scroller.x -\n                scroller.xOffset) /\n                (scroller.barWidth - minWidthDifference),\n            chartY: (normalizedEvent.chartY - scroller.y -\n                scroller.yOffset) /\n                (scroller.barWidth - minWidthDifference)\n        };\n    }\n    /**\n     * Destroys allocated elements.\n     *\n     * @private\n     * @function Highcharts.Scrollbar#destroy\n     */\n    destroy() {\n        const scroller = this, navigator = scroller.chart.scroller;\n        // Disconnect events added in addEvents\n        scroller.removeEvents();\n        // Destroy properties\n        [\n            'track',\n            'scrollbarRifles',\n            'scrollbar',\n            'scrollbarGroup',\n            'group'\n        ].forEach(function (prop) {\n            if (scroller[prop] && scroller[prop].destroy) {\n                scroller[prop] = scroller[prop].destroy();\n            }\n        });\n        // #6421, chart may have more scrollbars\n        if (navigator && scroller === navigator.scrollbar) {\n            navigator.scrollbar = null;\n            // Destroy elements in collection\n            destroyObjectProperties(navigator.scrollbarButtons);\n        }\n    }\n    /**\n     * Draw the scrollbar buttons with arrows\n     *\n     * @private\n     * @function Highcharts.Scrollbar#drawScrollbarButton\n     * @param {number} index\n     *        0 is left, 1 is right\n     */\n    drawScrollbarButton(index) {\n        const scroller = this, renderer = scroller.renderer, scrollbarButtons = scroller.scrollbarButtons, options = scroller.options, size = scroller.size, group = renderer.g().add(scroller.group);\n        scrollbarButtons.push(group);\n        if (options.buttonsEnabled) {\n            // Create a rectangle for the scrollbar button\n            const rect = renderer.rect()\n                .addClass('highcharts-scrollbar-button')\n                .add(group);\n            // Presentational attributes\n            if (!scroller.chart.styledMode) {\n                rect.attr({\n                    stroke: options.buttonBorderColor,\n                    'stroke-width': options.buttonBorderWidth,\n                    fill: options.buttonBackgroundColor\n                });\n            }\n            // Place the rectangle based on the rendered stroke width\n            rect.attr(rect.crisp({\n                x: -0.5,\n                y: -0.5,\n                width: size,\n                height: size,\n                r: options.buttonBorderRadius\n            }, rect.strokeWidth()));\n            // Button arrow\n            const arrow = renderer\n                .path(Scrollbar.swapXY([[\n                    'M',\n                    size / 2 + (index ? -1 : 1),\n                    size / 2 - 3\n                ], [\n                    'L',\n                    size / 2 + (index ? -1 : 1),\n                    size / 2 + 3\n                ], [\n                    'L',\n                    size / 2 + (index ? 2 : -2),\n                    size / 2\n                ]], options.vertical))\n                .addClass('highcharts-scrollbar-arrow')\n                .add(scrollbarButtons[index]);\n            if (!scroller.chart.styledMode) {\n                arrow.attr({\n                    fill: options.buttonArrowColor\n                });\n            }\n        }\n    }\n    /**\n     * @private\n     * @function Highcharts.Scrollbar#init\n     * @param {Highcharts.SVGRenderer} renderer\n     * @param {Highcharts.ScrollbarOptions} options\n     * @param {Highcharts.Chart} chart\n     */\n    init(renderer, options, chart) {\n        const scroller = this;\n        scroller.scrollbarButtons = [];\n        scroller.renderer = renderer;\n        scroller.userOptions = options;\n        scroller.options = Scrollbar_merge(Scrollbar_ScrollbarDefaults, defaultOptions.scrollbar, options);\n        scroller.options.margin = Scrollbar_pick(scroller.options.margin, 10);\n        scroller.chart = chart;\n        // Backward compatibility\n        scroller.size = Scrollbar_pick(scroller.options.size, scroller.options.height);\n        // Init\n        if (options.enabled) {\n            scroller.render();\n            scroller.addEvents();\n        }\n    }\n    mouseDownHandler(e) {\n        const scroller = this, normalizedEvent = scroller.chart.pointer?.normalize(e) || e, mousePosition = scroller.cursorToScrollbarPosition(normalizedEvent);\n        scroller.chartX = mousePosition.chartX;\n        scroller.chartY = mousePosition.chartY;\n        scroller.initPositions = [scroller.from, scroller.to];\n        scroller.grabbedCenter = true;\n    }\n    /**\n     * Event handler for the mouse move event.\n     * @private\n     */\n    mouseMoveHandler(e) {\n        const scroller = this, normalizedEvent = scroller.chart.pointer?.normalize(e) || e, options = scroller.options, direction = options.vertical ?\n            'chartY' : 'chartX', initPositions = scroller.initPositions || [];\n        let scrollPosition, chartPosition, change;\n        // In iOS, a mousemove event with e.pageX === 0 is fired when\n        // holding the finger down in the center of the scrollbar. This\n        // should be ignored.\n        if (scroller.grabbedCenter &&\n            // #4696, scrollbar failed on Android\n            (!e.touches || e.touches[0][direction] !== 0)) {\n            chartPosition = scroller.cursorToScrollbarPosition(normalizedEvent)[direction];\n            scrollPosition = scroller[direction];\n            change = chartPosition - scrollPosition;\n            scroller.hasDragged = true;\n            scroller.updatePosition(initPositions[0] + change, initPositions[1] + change);\n            if (scroller.hasDragged) {\n                fireEvent(scroller, 'changed', {\n                    from: scroller.from,\n                    to: scroller.to,\n                    trigger: 'scrollbar',\n                    DOMType: e.type,\n                    DOMEvent: e\n                });\n            }\n        }\n    }\n    /**\n     * Event handler for the mouse up event.\n     * @private\n     */\n    mouseUpHandler(e) {\n        const scroller = this;\n        if (scroller.hasDragged) {\n            fireEvent(scroller, 'changed', {\n                from: scroller.from,\n                to: scroller.to,\n                trigger: 'scrollbar',\n                DOMType: e.type,\n                DOMEvent: e\n            });\n        }\n        scroller.grabbedCenter =\n            scroller.hasDragged =\n                scroller.chartX =\n                    scroller.chartY = null;\n    }\n    /**\n     * Position the scrollbar, method called from a parent with defined\n     * dimensions.\n     *\n     * @private\n     * @function Highcharts.Scrollbar#position\n     * @param {number} x\n     *        x-position on the chart\n     * @param {number} y\n     *        y-position on the chart\n     * @param {number} width\n     *        width of the scrollbar\n     * @param {number} height\n     *        height of the scrollbar\n     */\n    position(x, y, width, height) {\n        const scroller = this, options = scroller.options, { buttonsEnabled, margin = 0, vertical } = options, method = scroller.rendered ? 'animate' : 'attr';\n        let xOffset = height, yOffset = 0;\n        // Make the scrollbar visible when it is repositioned, #15763.\n        scroller.group.show();\n        scroller.x = x;\n        scroller.y = y + this.trackBorderWidth;\n        scroller.width = width; // Width with buttons\n        scroller.height = height;\n        scroller.xOffset = xOffset;\n        scroller.yOffset = yOffset;\n        // If Scrollbar is a vertical type, swap options:\n        if (vertical) {\n            scroller.width = scroller.yOffset = width = yOffset = scroller.size;\n            scroller.xOffset = xOffset = 0;\n            scroller.yOffset = yOffset = buttonsEnabled ? scroller.size : 0;\n            // Width without buttons\n            scroller.barWidth = height - (buttonsEnabled ? width * 2 : 0);\n            scroller.x = x = x + margin;\n        }\n        else {\n            scroller.height = height = scroller.size;\n            scroller.xOffset = xOffset = buttonsEnabled ? scroller.size : 0;\n            // Width without buttons\n            scroller.barWidth = width - (buttonsEnabled ? height * 2 : 0);\n            scroller.y = scroller.y + margin;\n        }\n        // Set general position for a group:\n        scroller.group[method]({\n            translateX: x,\n            translateY: scroller.y\n        });\n        // Resize background/track:\n        scroller.track[method]({\n            width: width,\n            height: height\n        });\n        // Move right/bottom button to its place:\n        scroller.scrollbarButtons[1][method]({\n            translateX: vertical ? 0 : width - xOffset,\n            translateY: vertical ? height - yOffset : 0\n        });\n    }\n    /**\n     * Removes the event handlers attached previously with addEvents.\n     *\n     * @private\n     * @function Highcharts.Scrollbar#removeEvents\n     */\n    removeEvents() {\n        this._events.forEach(function (args) {\n            removeEvent.apply(null, args);\n        });\n        this._events.length = 0;\n    }\n    /**\n     * Render scrollbar with all required items.\n     *\n     * @private\n     * @function Highcharts.Scrollbar#render\n     */\n    render() {\n        const scroller = this, renderer = scroller.renderer, options = scroller.options, size = scroller.size, styledMode = scroller.chart.styledMode, group = renderer.g('scrollbar')\n            .attr({\n            zIndex: options.zIndex\n        })\n            .hide() // Initially hide the scrollbar #15863\n            .add();\n        // Draw the scrollbar group\n        scroller.group = group;\n        // Draw the scrollbar track:\n        scroller.track = renderer.rect()\n            .addClass('highcharts-scrollbar-track')\n            .attr({\n            r: options.trackBorderRadius || 0,\n            height: size,\n            width: size\n        }).add(group);\n        if (!styledMode) {\n            scroller.track.attr({\n                fill: options.trackBackgroundColor,\n                stroke: options.trackBorderColor,\n                'stroke-width': options.trackBorderWidth\n            });\n        }\n        const trackBorderWidth = scroller.trackBorderWidth =\n            scroller.track.strokeWidth();\n        scroller.track.attr({\n            x: -crisp(0, trackBorderWidth),\n            y: -crisp(0, trackBorderWidth)\n        });\n        // Draw the scrollbar itself\n        scroller.scrollbarGroup = renderer.g().add(group);\n        scroller.scrollbar = renderer.rect()\n            .addClass('highcharts-scrollbar-thumb')\n            .attr({\n            height: size - trackBorderWidth,\n            width: size - trackBorderWidth,\n            r: options.barBorderRadius || 0\n        }).add(scroller.scrollbarGroup);\n        scroller.scrollbarRifles = renderer\n            .path(Scrollbar.swapXY([\n            ['M', -3, size / 4],\n            ['L', -3, 2 * size / 3],\n            ['M', 0, size / 4],\n            ['L', 0, 2 * size / 3],\n            ['M', 3, size / 4],\n            ['L', 3, 2 * size / 3]\n        ], options.vertical))\n            .addClass('highcharts-scrollbar-rifles')\n            .add(scroller.scrollbarGroup);\n        if (!styledMode) {\n            scroller.scrollbar.attr({\n                fill: options.barBackgroundColor,\n                stroke: options.barBorderColor,\n                'stroke-width': options.barBorderWidth\n            });\n            scroller.scrollbarRifles.attr({\n                stroke: options.rifleColor,\n                'stroke-width': 1\n            });\n        }\n        scroller.scrollbarStrokeWidth = scroller.scrollbar.strokeWidth();\n        scroller.scrollbarGroup.translate(-crisp(0, scroller.scrollbarStrokeWidth), -crisp(0, scroller.scrollbarStrokeWidth));\n        // Draw the buttons:\n        scroller.drawScrollbarButton(0);\n        scroller.drawScrollbarButton(1);\n    }\n    /**\n     * Set scrollbar size, with a given scale.\n     *\n     * @private\n     * @function Highcharts.Scrollbar#setRange\n     * @param {number} from\n     *        scale (0-1) where bar should start\n     * @param {number} to\n     *        scale (0-1) where bar should end\n     */\n    setRange(from, to) {\n        const scroller = this, options = scroller.options, vertical = options.vertical, minWidth = options.minWidth, fullWidth = scroller.barWidth, method = (this.rendered &&\n            !this.hasDragged &&\n            !(this.chart.navigator && this.chart.navigator.hasDragged)) ? 'animate' : 'attr';\n        if (!Scrollbar_defined(fullWidth)) {\n            return;\n        }\n        const toPX = fullWidth * Math.min(to, 1);\n        let fromPX, newSize;\n        from = Math.max(from, 0);\n        fromPX = Math.ceil(fullWidth * from);\n        scroller.calculatedWidth = newSize = Scrollbar_correctFloat(toPX - fromPX);\n        // We need to recalculate position, if minWidth is used\n        if (newSize < minWidth) {\n            fromPX = (fullWidth - minWidth + newSize) * from;\n            newSize = minWidth;\n        }\n        const newPos = Math.floor(fromPX + scroller.xOffset + scroller.yOffset);\n        const newRiflesPos = newSize / 2 - 0.5; // -0.5 -> rifle line width / 2\n        // Store current position:\n        scroller.from = from;\n        scroller.to = to;\n        if (!vertical) {\n            scroller.scrollbarGroup[method]({\n                translateX: newPos\n            });\n            scroller.scrollbar[method]({\n                width: newSize\n            });\n            scroller.scrollbarRifles[method]({\n                translateX: newRiflesPos\n            });\n            scroller.scrollbarLeft = newPos;\n            scroller.scrollbarTop = 0;\n        }\n        else {\n            scroller.scrollbarGroup[method]({\n                translateY: newPos\n            });\n            scroller.scrollbar[method]({\n                height: newSize\n            });\n            scroller.scrollbarRifles[method]({\n                translateY: newRiflesPos\n            });\n            scroller.scrollbarTop = newPos;\n            scroller.scrollbarLeft = 0;\n        }\n        if (newSize <= 12) {\n            scroller.scrollbarRifles.hide();\n        }\n        else {\n            scroller.scrollbarRifles.show();\n        }\n        // Show or hide the scrollbar based on the showFull setting\n        if (options.showFull === false) {\n            if (from <= 0 && to >= 1) {\n                scroller.group.hide();\n            }\n            else {\n                scroller.group.show();\n            }\n        }\n        scroller.rendered = true;\n    }\n    /**\n     * Checks if the extremes should be updated in response to a scrollbar\n     * change event.\n     *\n     * @private\n     * @function Highcharts.Scrollbar#shouldUpdateExtremes\n     */\n    shouldUpdateExtremes(eventType) {\n        return (Scrollbar_pick(this.options.liveRedraw, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).svg &&\n            !(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice &&\n            !this.chart.boosted) ||\n            // Mouseup always should change extremes\n            eventType === 'mouseup' ||\n            eventType === 'touchend' ||\n            // Internal events\n            !Scrollbar_defined(eventType));\n    }\n    trackClick(e) {\n        const scroller = this;\n        const normalizedEvent = scroller.chart.pointer?.normalize(e) || e, range = scroller.to - scroller.from, top = scroller.y + scroller.scrollbarTop, left = scroller.x + scroller.scrollbarLeft;\n        if ((scroller.options.vertical && normalizedEvent.chartY > top) ||\n            (!scroller.options.vertical && normalizedEvent.chartX > left)) {\n            // On the top or on the left side of the track:\n            scroller.updatePosition(scroller.from + range, scroller.to + range);\n        }\n        else {\n            // On the bottom or the right side of the track:\n            scroller.updatePosition(scroller.from - range, scroller.to - range);\n        }\n        fireEvent(scroller, 'changed', {\n            from: scroller.from,\n            to: scroller.to,\n            trigger: 'scrollbar',\n            DOMEvent: e\n        });\n    }\n    /**\n     * Update the scrollbar with new options\n     *\n     * @private\n     * @function Highcharts.Scrollbar#update\n     * @param  {Highcharts.ScrollbarOptions} options\n     */\n    update(options) {\n        this.destroy();\n        this.init(this.chart.renderer, Scrollbar_merge(true, this.options, options), this.chart);\n    }\n    /**\n     * Update position option in the Scrollbar, with normalized 0-1 scale\n     *\n     * @private\n     * @function Highcharts.Scrollbar#updatePosition\n     * @param  {number} from\n     * @param  {number} to\n     */\n    updatePosition(from, to) {\n        if (to > 1) {\n            from = Scrollbar_correctFloat(1 - Scrollbar_correctFloat(to - from));\n            to = 1;\n        }\n        if (from < 0) {\n            to = Scrollbar_correctFloat(to - from);\n            from = 0;\n        }\n        this.from = from;\n        this.to = to;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nScrollbar.defaultOptions = Scrollbar_ScrollbarDefaults;\n/* *\n *\n *  Registry\n *\n * */\ndefaultOptions.scrollbar = Scrollbar_merge(true, Scrollbar.defaultOptions, defaultOptions.scrollbar);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Scrollbar_Scrollbar = (Scrollbar);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es-modules/Stock/Navigator/Navigator.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { defaultOptions: Navigator_defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { isTouchDevice: Navigator_isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { prototype: { symbols } } = (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default());\n\nconst { addEvent: Navigator_addEvent, clamp, correctFloat: Navigator_correctFloat, defined: Navigator_defined, destroyObjectProperties: Navigator_destroyObjectProperties, erase, extend: Navigator_extend, find, fireEvent: Navigator_fireEvent, isArray, isNumber: Navigator_isNumber, merge: Navigator_merge, pick: Navigator_pick, removeEvent: Navigator_removeEvent, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Finding the min or max of a set of variables where we don't know if they are\n * defined, is a pattern that is repeated several places in Highcharts. Consider\n * making this a global utility method.\n * @private\n */\nfunction numExt(extreme, ...args) {\n    const numbers = [].filter.call(args, Navigator_isNumber);\n    if (numbers.length) {\n        return Math[extreme].apply(0, numbers);\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Navigator class\n *\n * @private\n * @class\n * @name Highcharts.Navigator\n *\n * @param {Highcharts.Chart} chart\n *        Chart object\n */\nclass Navigator {\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    static compose(ChartClass, AxisClass, SeriesClass) {\n        Navigator_ChartNavigatorComposition.compose(ChartClass, Navigator);\n        Navigator_NavigatorComposition.compose(ChartClass, AxisClass, SeriesClass);\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart) {\n        this.isDirty = false;\n        this.scrollbarHeight = 0;\n        this.init(chart);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Draw one of the handles on the side of the zoomed range in the navigator.\n     *\n     * @private\n     * @function Highcharts.Navigator#drawHandle\n     *\n     * @param {number} x\n     *        The x center for the handle\n     *\n     * @param {number} index\n     *        0 for left and 1 for right\n     *\n     * @param {boolean|undefined} inverted\n     *        Flag for chart.inverted\n     *\n     * @param {string} verb\n     *        Use 'animate' or 'attr'\n     */\n    drawHandle(x, index, inverted, verb) {\n        const navigator = this, height = navigator.navigatorOptions.handles.height;\n        // Place it\n        navigator.handles[index][verb](inverted ? {\n            translateX: Math.round(navigator.left + navigator.height / 2),\n            translateY: Math.round(navigator.top + parseInt(x, 10) + 0.5 - height)\n        } : {\n            translateX: Math.round(navigator.left + parseInt(x, 10)),\n            translateY: Math.round(navigator.top + navigator.height / 2 - height / 2 - 1)\n        });\n    }\n    /**\n     * Render outline around the zoomed range\n     *\n     * @private\n     * @function Highcharts.Navigator#drawOutline\n     *\n     * @param {number} zoomedMin\n     *        in pixels position where zoomed range starts\n     *\n     * @param {number} zoomedMax\n     *        in pixels position where zoomed range ends\n     *\n     * @param {boolean|undefined} inverted\n     *        flag if chart is inverted\n     *\n     * @param {string} verb\n     *        use 'animate' or 'attr'\n     */\n    drawOutline(zoomedMin, zoomedMax, inverted, verb) {\n        const navigator = this, maskInside = navigator.navigatorOptions.maskInside, outlineWidth = navigator.outline.strokeWidth(), halfOutline = outlineWidth / 2, outlineCorrection = (outlineWidth % 2) / 2, // #5800\n        scrollButtonSize = navigator.scrollButtonSize, navigatorSize = navigator.size, navigatorTop = navigator.top, height = navigator.height, lineTop = navigatorTop - halfOutline, lineBtm = navigatorTop + height;\n        let left = navigator.left, verticalMin, path;\n        if (inverted) {\n            verticalMin = navigatorTop + zoomedMax + outlineCorrection;\n            zoomedMax = navigatorTop + zoomedMin + outlineCorrection;\n            path = [\n                [\n                    'M',\n                    left + height,\n                    navigatorTop - scrollButtonSize - outlineCorrection\n                ],\n                // Top right of zoomed range\n                ['L', left + height, verticalMin],\n                ['L', left, verticalMin], // Top left of z.r.\n                ['M', left, zoomedMax], // Bottom left of z.r.\n                ['L', left + height, zoomedMax], // Bottom right of z.r.\n                [\n                    'L',\n                    left + height,\n                    navigatorTop + navigatorSize + scrollButtonSize\n                ]\n            ];\n            if (maskInside) {\n                path.push(\n                // Upper left of zoomed range\n                ['M', left + height, verticalMin - halfOutline], \n                // Upper right of z.r.\n                [\n                    'L',\n                    left + height,\n                    zoomedMax + halfOutline\n                ]);\n            }\n        }\n        else {\n            left -= scrollButtonSize;\n            zoomedMin += left + scrollButtonSize - outlineCorrection;\n            zoomedMax += left + scrollButtonSize - outlineCorrection;\n            path = [\n                // Left\n                ['M', left, lineTop],\n                // Upper left of zoomed range\n                ['L', zoomedMin, lineTop],\n                // Lower left of z.r.\n                ['L', zoomedMin, lineBtm],\n                // Lower right of z.r.\n                ['M', zoomedMax, lineBtm],\n                // Upper right of z.r.\n                ['L', zoomedMax, lineTop],\n                // Right\n                [\n                    'L',\n                    left + navigatorSize + scrollButtonSize * 2,\n                    lineTop\n                ]\n            ];\n            if (maskInside) {\n                path.push(\n                // Upper left of zoomed range\n                ['M', zoomedMin - halfOutline, lineTop], \n                // Upper right of z.r.\n                ['L', zoomedMax + halfOutline, lineTop]);\n            }\n        }\n        navigator.outline[verb]({\n            d: path\n        });\n    }\n    /**\n     * Render outline around the zoomed range\n     *\n     * @private\n     * @function Highcharts.Navigator#drawMasks\n     *\n     * @param {number} zoomedMin\n     *        in pixels position where zoomed range starts\n     *\n     * @param {number} zoomedMax\n     *        in pixels position where zoomed range ends\n     *\n     * @param {boolean|undefined} inverted\n     *        flag if chart is inverted\n     *\n     * @param {string} verb\n     *        use 'animate' or 'attr'\n     */\n    drawMasks(zoomedMin, zoomedMax, inverted, verb) {\n        const navigator = this, left = navigator.left, top = navigator.top, navigatorHeight = navigator.height;\n        let height, width, x, y;\n        // Determine rectangle position & size\n        // According to (non)inverted position:\n        if (inverted) {\n            x = [left, left, left];\n            y = [top, top + zoomedMin, top + zoomedMax];\n            width = [navigatorHeight, navigatorHeight, navigatorHeight];\n            height = [\n                zoomedMin,\n                zoomedMax - zoomedMin,\n                navigator.size - zoomedMax\n            ];\n        }\n        else {\n            x = [left, left + zoomedMin, left + zoomedMax];\n            y = [top, top, top];\n            width = [\n                zoomedMin,\n                zoomedMax - zoomedMin,\n                navigator.size - zoomedMax\n            ];\n            height = [navigatorHeight, navigatorHeight, navigatorHeight];\n        }\n        navigator.shades.forEach((shade, i) => {\n            shade[verb]({\n                x: x[i],\n                y: y[i],\n                width: width[i],\n                height: height[i]\n            });\n        });\n    }\n    /**\n     * Generate and update DOM elements for a navigator:\n     *\n     * - main navigator group\n     *\n     * - all shades\n     *\n     * - outline\n     *\n     * - handles\n     *\n     * @private\n     * @function Highcharts.Navigator#renderElements\n     */\n    renderElements() {\n        const navigator = this, navigatorOptions = navigator.navigatorOptions, maskInside = navigatorOptions.maskInside, chart = navigator.chart, inverted = chart.inverted, renderer = chart.renderer, mouseCursor = {\n            cursor: inverted ? 'ns-resize' : 'ew-resize'\n        }, \n        // Create the main navigator group\n        navigatorGroup = navigator.navigatorGroup ??\n            (navigator.navigatorGroup = renderer\n                .g('navigator')\n                .attr({\n                zIndex: 8,\n                visibility: 'hidden'\n            })\n                .add());\n        // Create masks, each mask will get events and fill:\n        [\n            !maskInside,\n            maskInside,\n            !maskInside\n        ].forEach((hasMask, index) => {\n            const shade = navigator.shades[index] ??\n                (navigator.shades[index] = renderer.rect()\n                    .addClass('highcharts-navigator-mask' +\n                    (index === 1 ? '-inside' : '-outside'))\n                    .add(navigatorGroup));\n            if (!chart.styledMode) {\n                shade.attr({\n                    fill: hasMask ? navigatorOptions.maskFill : 'rgba(0,0,0,0)'\n                });\n                if (index === 1) {\n                    shade.css(mouseCursor);\n                }\n            }\n        });\n        // Create the outline:\n        if (!navigator.outline) {\n            navigator.outline = renderer.path()\n                .addClass('highcharts-navigator-outline')\n                .add(navigatorGroup);\n        }\n        if (!chart.styledMode) {\n            navigator.outline.attr({\n                'stroke-width': navigatorOptions.outlineWidth,\n                stroke: navigatorOptions.outlineColor\n            });\n        }\n        // Create the handlers:\n        if (navigatorOptions.handles?.enabled) {\n            const handlesOptions = navigatorOptions.handles, { height, width } = handlesOptions;\n            [0, 1].forEach((index) => {\n                const symbolName = handlesOptions.symbols[index];\n                if (!navigator.handles[index] ||\n                    navigator.handles[index].symbolUrl !== symbolName) {\n                    // Generate symbol from scratch if we're dealing with an URL\n                    navigator.handles[index]?.destroy();\n                    navigator.handles[index] = renderer.symbol(symbolName, -width / 2 - 1, 0, width, height, handlesOptions);\n                    // Z index is 6 for right handle, 7 for left. Can't be 10,\n                    // because of the tooltip in inverted chart (#2908).\n                    navigator.handles[index].attr({ zIndex: 7 - index })\n                        .addClass('highcharts-navigator-handle ' +\n                        'highcharts-navigator-handle-' +\n                        ['left', 'right'][index]).add(navigatorGroup);\n                    navigator.addMouseEvents();\n                    // If the navigator symbol changed, update its path and name\n                }\n                else if (!navigator.handles[index].isImg &&\n                    navigator.handles[index].symbolName !== symbolName) {\n                    const symbolFn = symbols[symbolName], path = symbolFn.call(symbols, -width / 2 - 1, 0, width, height);\n                    navigator.handles[index].attr({\n                        d: path\n                    });\n                    navigator.handles[index].symbolName = symbolName;\n                }\n                if (chart.inverted) {\n                    navigator.handles[index].attr({\n                        rotation: 90,\n                        rotationOriginX: Math.floor(-width / 2),\n                        rotationOriginY: (height + width) / 2\n                    });\n                }\n                if (!chart.styledMode) {\n                    navigator.handles[index]\n                        .attr({\n                        fill: handlesOptions.backgroundColor,\n                        stroke: handlesOptions.borderColor,\n                        'stroke-width': handlesOptions.lineWidth,\n                        width: handlesOptions.width,\n                        height: handlesOptions.height,\n                        x: -width / 2 - 1,\n                        y: 0\n                    })\n                        .css(mouseCursor);\n                }\n            });\n        }\n    }\n    /**\n     * Update navigator\n     *\n     * @private\n     * @function Highcharts.Navigator#update\n     *\n     * @param {Highcharts.NavigatorOptions} options\n     *        Options to merge in when updating navigator\n     */\n    update(options, redraw = false) {\n        const chart = this.chart, invertedUpdate = chart.options.chart.inverted !==\n            chart.scrollbar?.options.vertical;\n        Navigator_merge(true, chart.options.navigator, options);\n        this.navigatorOptions = chart.options.navigator || {};\n        this.setOpposite();\n        // Revert to destroy/init for navigator/scrollbar enabled toggle\n        if (Navigator_defined(options.enabled) || invertedUpdate) {\n            this.destroy();\n            this.navigatorEnabled = options.enabled || this.navigatorEnabled;\n            return this.init(chart);\n        }\n        if (this.navigatorEnabled) {\n            this.isDirty = true;\n            if (options.adaptToUpdatedData === false) {\n                this.baseSeries.forEach((series) => {\n                    Navigator_removeEvent(series, 'updatedData', this.updatedDataHandler);\n                }, this);\n            }\n            if (options.adaptToUpdatedData) {\n                this.baseSeries.forEach((series) => {\n                    series.eventsToUnbind.push(Navigator_addEvent(series, 'updatedData', this.updatedDataHandler));\n                }, this);\n            }\n            // Update navigator series\n            if (options.series || options.baseSeries) {\n                this.setBaseSeries(void 0, false);\n            }\n            // Update navigator axis\n            if (options.height || options.xAxis || options.yAxis) {\n                this.height = options.height ?? this.height;\n                const offsets = this.getXAxisOffsets();\n                this.xAxis.update({\n                    ...options.xAxis,\n                    offsets,\n                    [chart.inverted ? 'width' : 'height']: this.height,\n                    [chart.inverted ? 'height' : 'width']: void 0\n                }, false);\n                this.yAxis.update({\n                    ...options.yAxis,\n                    [chart.inverted ? 'width' : 'height']: this.height\n                }, false);\n            }\n        }\n        if (redraw) {\n            chart.redraw();\n        }\n    }\n    /**\n     * Render the navigator\n     *\n     * @private\n     * @function Highcharts.Navigator#render\n     * @param {number} min\n     *        X axis value minimum\n     * @param {number} max\n     *        X axis value maximum\n     * @param {number} [pxMin]\n     *        Pixel value minimum\n     * @param {number} [pxMax]\n     *        Pixel value maximum\n     */\n    render(min, max, pxMin, pxMax) {\n        const navigator = this, chart = navigator.chart, xAxis = navigator.xAxis, pointRange = xAxis.pointRange || 0, scrollbarXAxis = xAxis.navigatorAxis.fake ? chart.xAxis[0] : xAxis, navigatorEnabled = navigator.navigatorEnabled, rendered = navigator.rendered, inverted = chart.inverted, minRange = chart.xAxis[0].minRange, maxRange = chart.xAxis[0].options.maxRange, scrollButtonSize = navigator.scrollButtonSize;\n        let navigatorWidth, scrollbarLeft, scrollbarTop, scrollbarHeight = navigator.scrollbarHeight, navigatorSize, verb;\n        // Don't redraw while moving the handles (#4703).\n        if (this.hasDragged && !Navigator_defined(pxMin)) {\n            return;\n        }\n        if (this.isDirty) {\n            // Update DOM navigator elements\n            this.renderElements();\n        }\n        min = Navigator_correctFloat(min - pointRange / 2);\n        max = Navigator_correctFloat(max + pointRange / 2);\n        // Don't render the navigator until we have data (#486, #4202, #5172).\n        if (!Navigator_isNumber(min) || !Navigator_isNumber(max)) {\n            // However, if navigator was already rendered, we may need to resize\n            // it. For example hidden series, but visible navigator (#6022).\n            if (rendered) {\n                pxMin = 0;\n                pxMax = Navigator_pick(xAxis.width, scrollbarXAxis.width);\n            }\n            else {\n                return;\n            }\n        }\n        navigator.left = Navigator_pick(xAxis.left, \n        // In case of scrollbar only, without navigator\n        chart.plotLeft + scrollButtonSize +\n            (inverted ? chart.plotWidth : 0));\n        let zoomedMax = navigator.size = navigatorSize = Navigator_pick(xAxis.len, (inverted ? chart.plotHeight : chart.plotWidth) -\n            2 * scrollButtonSize);\n        if (inverted) {\n            navigatorWidth = scrollbarHeight;\n        }\n        else {\n            navigatorWidth = navigatorSize + 2 * scrollButtonSize;\n        }\n        // Get the pixel position of the handles\n        pxMin = Navigator_pick(pxMin, xAxis.toPixels(min, true));\n        pxMax = Navigator_pick(pxMax, xAxis.toPixels(max, true));\n        // Verify (#1851, #2238)\n        if (!Navigator_isNumber(pxMin) || Math.abs(pxMin) === Infinity) {\n            pxMin = 0;\n            pxMax = navigatorWidth;\n        }\n        // Are we below the minRange? (#2618, #6191)\n        const newMin = xAxis.toValue(pxMin, true), newMax = xAxis.toValue(pxMax, true), currentRange = Math.abs(Navigator_correctFloat(newMax - newMin));\n        if (currentRange < minRange) {\n            if (this.grabbedLeft) {\n                pxMin = xAxis.toPixels(newMax - minRange - pointRange, true);\n            }\n            else if (this.grabbedRight) {\n                pxMax = xAxis.toPixels(newMin + minRange + pointRange, true);\n            }\n        }\n        else if (Navigator_defined(maxRange) &&\n            Navigator_correctFloat(currentRange - pointRange) > maxRange) {\n            if (this.grabbedLeft) {\n                pxMin = xAxis.toPixels(newMax - maxRange - pointRange, true);\n            }\n            else if (this.grabbedRight) {\n                pxMax = xAxis.toPixels(newMin + maxRange + pointRange, true);\n            }\n        }\n        // Handles are allowed to cross, but never exceed the plot area\n        navigator.zoomedMax = clamp(Math.max(pxMin, pxMax), 0, zoomedMax);\n        navigator.zoomedMin = clamp(navigator.fixedWidth ?\n            navigator.zoomedMax - navigator.fixedWidth :\n            Math.min(pxMin, pxMax), 0, zoomedMax);\n        navigator.range = navigator.zoomedMax - navigator.zoomedMin;\n        zoomedMax = Math.round(navigator.zoomedMax);\n        const zoomedMin = Math.round(navigator.zoomedMin);\n        if (navigatorEnabled) {\n            navigator.navigatorGroup.attr({\n                visibility: 'inherit'\n            });\n            // Place elements\n            verb = rendered && !navigator.hasDragged ? 'animate' : 'attr';\n            navigator.drawMasks(zoomedMin, zoomedMax, inverted, verb);\n            navigator.drawOutline(zoomedMin, zoomedMax, inverted, verb);\n            if (navigator.navigatorOptions.handles.enabled) {\n                navigator.drawHandle(zoomedMin, 0, inverted, verb);\n                navigator.drawHandle(zoomedMax, 1, inverted, verb);\n            }\n        }\n        if (navigator.scrollbar) {\n            if (inverted) {\n                scrollbarTop = navigator.top - scrollButtonSize;\n                scrollbarLeft = navigator.left - scrollbarHeight +\n                    (navigatorEnabled || !scrollbarXAxis.opposite ? 0 :\n                        // Multiple axes has offsets:\n                        (scrollbarXAxis.titleOffset || 0) +\n                            // Self margin from the axis.title\n                            scrollbarXAxis.axisTitleMargin);\n                scrollbarHeight = navigatorSize + 2 * scrollButtonSize;\n            }\n            else {\n                scrollbarTop = navigator.top + (navigatorEnabled ?\n                    navigator.height :\n                    -scrollbarHeight);\n                scrollbarLeft = navigator.left - scrollButtonSize;\n            }\n            // Reposition scrollbar\n            navigator.scrollbar.position(scrollbarLeft, scrollbarTop, navigatorWidth, scrollbarHeight);\n            // Keep scale 0-1\n            navigator.scrollbar.setRange(\n            // Use real value, not rounded because range can be very small\n            // (#1716)\n            navigator.zoomedMin / (navigatorSize || 1), navigator.zoomedMax / (navigatorSize || 1));\n        }\n        navigator.rendered = true;\n        this.isDirty = false;\n        Navigator_fireEvent(this, 'afterRender');\n    }\n    /**\n     * Set up the mouse and touch events for the navigator\n     *\n     * @private\n     * @function Highcharts.Navigator#addMouseEvents\n     */\n    addMouseEvents() {\n        const navigator = this, chart = navigator.chart, container = chart.container;\n        let eventsToUnbind = [], mouseMoveHandler, mouseUpHandler;\n        /**\n         * Create mouse events' handlers.\n         * Make them as separate functions to enable wrapping them:\n         */\n        navigator.mouseMoveHandler = mouseMoveHandler = function (e) {\n            navigator.onMouseMove(e);\n        };\n        navigator.mouseUpHandler = mouseUpHandler = function (e) {\n            navigator.onMouseUp(e);\n        };\n        // Add shades and handles mousedown events\n        eventsToUnbind = navigator.getPartsEvents('mousedown');\n        eventsToUnbind.push(\n        // Add mouse move and mouseup events. These are bind to doc/div,\n        // because Navigator.grabbedSomething flags are stored in mousedown\n        // events\n        Navigator_addEvent(chart.renderTo, 'mousemove', mouseMoveHandler), Navigator_addEvent(container.ownerDocument, 'mouseup', mouseUpHandler), \n        // Touch events\n        Navigator_addEvent(chart.renderTo, 'touchmove', mouseMoveHandler), Navigator_addEvent(container.ownerDocument, 'touchend', mouseUpHandler));\n        eventsToUnbind.concat(navigator.getPartsEvents('touchstart'));\n        navigator.eventsToUnbind = eventsToUnbind;\n        // Data events\n        if (navigator.series && navigator.series[0]) {\n            eventsToUnbind.push(Navigator_addEvent(navigator.series[0].xAxis, 'foundExtremes', function () {\n                chart.navigator.modifyNavigatorAxisExtremes();\n            }));\n        }\n    }\n    /**\n     * Generate events for handles and masks\n     *\n     * @private\n     * @function Highcharts.Navigator#getPartsEvents\n     *\n     * @param {string} eventName\n     *        Event name handler, 'mousedown' or 'touchstart'\n     *\n     * @return {Array<Function>}\n     *         An array of functions to remove navigator functions from the\n     *         events again.\n     */\n    getPartsEvents(eventName) {\n        const navigator = this, events = [];\n        ['shades', 'handles'].forEach(function (name) {\n            navigator[name].forEach(function (navigatorItem, index) {\n                events.push(Navigator_addEvent(navigatorItem.element, eventName, function (e) {\n                    navigator[name + 'Mousedown'](e, index);\n                }));\n            });\n        });\n        return events;\n    }\n    /**\n     * Mousedown on a shaded mask, either:\n     *\n     * - will be stored for future drag&drop\n     *\n     * - will directly shift to a new range\n     *\n     * @private\n     * @function Highcharts.Navigator#shadesMousedown\n     *\n     * @param {Highcharts.PointerEventObject} e\n     *        Mouse event\n     *\n     * @param {number} index\n     *        Index of a mask in Navigator.shades array\n     */\n    shadesMousedown(e, index) {\n        e = this.chart.pointer?.normalize(e) || e;\n        const navigator = this, chart = navigator.chart, xAxis = navigator.xAxis, zoomedMin = navigator.zoomedMin, navigatorSize = navigator.size, range = navigator.range;\n        let navigatorPosition = navigator.left, chartX = e.chartX, fixedMax, fixedMin, ext, left;\n        // For inverted chart, swap some options:\n        if (chart.inverted) {\n            chartX = e.chartY;\n            navigatorPosition = navigator.top;\n        }\n        if (index === 1) {\n            // Store information for drag&drop\n            navigator.grabbedCenter = chartX;\n            navigator.fixedWidth = range;\n            navigator.dragOffset = chartX - zoomedMin;\n        }\n        else {\n            // Shift the range by clicking on shaded areas\n            left = chartX - navigatorPosition - range / 2;\n            if (index === 0) {\n                left = Math.max(0, left);\n            }\n            else if (index === 2 && left + range >= navigatorSize) {\n                left = navigatorSize - range;\n                if (navigator.reversedExtremes) {\n                    // #7713\n                    left -= range;\n                    fixedMin = navigator.getUnionExtremes().dataMin;\n                }\n                else {\n                    // #2293, #3543\n                    fixedMax = navigator.getUnionExtremes().dataMax;\n                }\n            }\n            if (left !== zoomedMin) { // It has actually moved\n                navigator.fixedWidth = range; // #1370\n                ext = xAxis.navigatorAxis.toFixedRange(left, left + range, fixedMin, fixedMax);\n                if (Navigator_defined(ext.min)) { // #7411\n                    Navigator_fireEvent(this, 'setRange', {\n                        min: Math.min(ext.min, ext.max),\n                        max: Math.max(ext.min, ext.max),\n                        redraw: true,\n                        eventArguments: {\n                            trigger: 'navigator'\n                        }\n                    });\n                }\n            }\n        }\n    }\n    /**\n     * Mousedown on a handle mask.\n     * Will store necessary information for drag&drop.\n     *\n     * @private\n     * @function Highcharts.Navigator#handlesMousedown\n     * @param {Highcharts.PointerEventObject} e\n     *        Mouse event\n     * @param {number} index\n     *        Index of a handle in Navigator.handles array\n     */\n    handlesMousedown(e, index) {\n        e = this.chart.pointer?.normalize(e) || e;\n        const navigator = this, chart = navigator.chart, baseXAxis = chart.xAxis[0], \n        // For reversed axes, min and max are changed,\n        // so the other extreme should be stored\n        reverse = navigator.reversedExtremes;\n        if (index === 0) {\n            // Grab the left handle\n            navigator.grabbedLeft = true;\n            navigator.otherHandlePos = navigator.zoomedMax;\n            navigator.fixedExtreme = reverse ? baseXAxis.min : baseXAxis.max;\n        }\n        else {\n            // Grab the right handle\n            navigator.grabbedRight = true;\n            navigator.otherHandlePos = navigator.zoomedMin;\n            navigator.fixedExtreme = reverse ? baseXAxis.max : baseXAxis.min;\n        }\n        chart.setFixedRange(void 0);\n    }\n    /**\n     * Mouse move event based on x/y mouse position.\n     *\n     * @private\n     * @function Highcharts.Navigator#onMouseMove\n     *\n     * @param {Highcharts.PointerEventObject} e\n     *        Mouse event\n     */\n    onMouseMove(e) {\n        const navigator = this, chart = navigator.chart, navigatorSize = navigator.navigatorSize, range = navigator.range, dragOffset = navigator.dragOffset, inverted = chart.inverted;\n        let left = navigator.left, chartX;\n        // In iOS, a mousemove event with e.pageX === 0 is fired when holding\n        // the finger down in the center of the scrollbar. This should be\n        // ignored.\n        if (!e.touches || e.touches[0].pageX !== 0) { // #4696\n            e = chart.pointer?.normalize(e) || e;\n            chartX = e.chartX;\n            // Swap some options for inverted chart\n            if (inverted) {\n                left = navigator.top;\n                chartX = e.chartY;\n            }\n            // Drag left handle or top handle\n            if (navigator.grabbedLeft) {\n                navigator.hasDragged = true;\n                navigator.render(0, 0, chartX - left, navigator.otherHandlePos);\n                // Drag right handle or bottom handle\n            }\n            else if (navigator.grabbedRight) {\n                navigator.hasDragged = true;\n                navigator.render(0, 0, navigator.otherHandlePos, chartX - left);\n                // Drag scrollbar or open area in navigator\n            }\n            else if (navigator.grabbedCenter) {\n                navigator.hasDragged = true;\n                if (chartX < dragOffset) { // Outside left\n                    chartX = dragOffset;\n                    // Outside right\n                }\n                else if (chartX >\n                    navigatorSize + dragOffset - range) {\n                    chartX = navigatorSize + dragOffset - range;\n                }\n                navigator.render(0, 0, chartX - dragOffset, chartX - dragOffset + range);\n            }\n            if (navigator.hasDragged &&\n                navigator.scrollbar &&\n                Navigator_pick(navigator.scrollbar.options.liveRedraw, \n                // By default, don't run live redraw on touch\n                // devices or if the chart is in boost.\n                !Navigator_isTouchDevice &&\n                    !this.chart.boosted)) {\n                e.DOMType = e.type;\n                setTimeout(function () {\n                    navigator.onMouseUp(e);\n                }, 0);\n            }\n        }\n    }\n    /**\n     * Mouse up event based on x/y mouse position.\n     *\n     * @private\n     * @function Highcharts.Navigator#onMouseUp\n     * @param {Highcharts.PointerEventObject} e\n     *        Mouse event\n     */\n    onMouseUp(e) {\n        const navigator = this, chart = navigator.chart, xAxis = navigator.xAxis, scrollbar = navigator.scrollbar, DOMEvent = e.DOMEvent || e, inverted = chart.inverted, verb = navigator.rendered && !navigator.hasDragged ?\n            'animate' : 'attr';\n        let zoomedMax, zoomedMin, unionExtremes, fixedMin, fixedMax, ext;\n        if (\n        // MouseUp is called for both, navigator and scrollbar (that order),\n        // which causes calling afterSetExtremes twice. Prevent first call\n        // by checking if scrollbar is going to set new extremes (#6334)\n        (navigator.hasDragged && (!scrollbar || !scrollbar.hasDragged)) ||\n            e.trigger === 'scrollbar') {\n            unionExtremes = navigator.getUnionExtremes();\n            // When dragging one handle, make sure the other one doesn't change\n            if (navigator.zoomedMin === navigator.otherHandlePos) {\n                fixedMin = navigator.fixedExtreme;\n            }\n            else if (navigator.zoomedMax === navigator.otherHandlePos) {\n                fixedMax = navigator.fixedExtreme;\n            }\n            // Snap to right edge (#4076)\n            if (navigator.zoomedMax === navigator.size) {\n                fixedMax = navigator.reversedExtremes ?\n                    unionExtremes.dataMin :\n                    unionExtremes.dataMax;\n            }\n            // Snap to left edge (#7576)\n            if (navigator.zoomedMin === 0) {\n                fixedMin = navigator.reversedExtremes ?\n                    unionExtremes.dataMax :\n                    unionExtremes.dataMin;\n            }\n            ext = xAxis.navigatorAxis.toFixedRange(navigator.zoomedMin, navigator.zoomedMax, fixedMin, fixedMax);\n            if (Navigator_defined(ext.min)) {\n                Navigator_fireEvent(this, 'setRange', {\n                    min: Math.min(ext.min, ext.max),\n                    max: Math.max(ext.min, ext.max),\n                    redraw: true,\n                    animation: navigator.hasDragged ? false : null,\n                    eventArguments: {\n                        trigger: 'navigator',\n                        triggerOp: 'navigator-drag',\n                        DOMEvent: DOMEvent // #1838\n                    }\n                });\n            }\n        }\n        if (e.DOMType !== 'mousemove' &&\n            e.DOMType !== 'touchmove') {\n            navigator.grabbedLeft = navigator.grabbedRight =\n                navigator.grabbedCenter = navigator.fixedWidth =\n                    navigator.fixedExtreme = navigator.otherHandlePos =\n                        navigator.hasDragged = navigator.dragOffset = null;\n        }\n        // Update position of navigator shades, outline and handles (#12573)\n        if (navigator.navigatorEnabled &&\n            Navigator_isNumber(navigator.zoomedMin) &&\n            Navigator_isNumber(navigator.zoomedMax)) {\n            zoomedMin = Math.round(navigator.zoomedMin);\n            zoomedMax = Math.round(navigator.zoomedMax);\n            if (navigator.shades) {\n                navigator.drawMasks(zoomedMin, zoomedMax, inverted, verb);\n            }\n            if (navigator.outline) {\n                navigator.drawOutline(zoomedMin, zoomedMax, inverted, verb);\n            }\n            if (navigator.navigatorOptions.handles.enabled &&\n                Object.keys(navigator.handles).length ===\n                    navigator.handles.length) {\n                navigator.drawHandle(zoomedMin, 0, inverted, verb);\n                navigator.drawHandle(zoomedMax, 1, inverted, verb);\n            }\n        }\n    }\n    /**\n     * Removes the event handlers attached previously with addEvents.\n     *\n     * @private\n     * @function Highcharts.Navigator#removeEvents\n     */\n    removeEvents() {\n        if (this.eventsToUnbind) {\n            this.eventsToUnbind.forEach(function (unbind) {\n                unbind();\n            });\n            this.eventsToUnbind = void 0;\n        }\n        this.removeBaseSeriesEvents();\n    }\n    /**\n     * Remove data events.\n     *\n     * @private\n     * @function Highcharts.Navigator#removeBaseSeriesEvents\n     */\n    removeBaseSeriesEvents() {\n        const baseSeries = this.baseSeries || [];\n        if (this.navigatorEnabled && baseSeries[0]) {\n            if (this.navigatorOptions.adaptToUpdatedData !== false) {\n                baseSeries.forEach(function (series) {\n                    Navigator_removeEvent(series, 'updatedData', this.updatedDataHandler);\n                }, this);\n            }\n            // We only listen for extremes-events on the first baseSeries\n            if (baseSeries[0].xAxis) {\n                Navigator_removeEvent(baseSeries[0].xAxis, 'foundExtremes', this.modifyBaseAxisExtremes);\n            }\n        }\n    }\n    /**\n     * Calculate the navigator xAxis offsets\n     *\n     * @private\n     */\n    getXAxisOffsets() {\n        return (this.chart.inverted ?\n            [this.scrollButtonSize, 0, -this.scrollButtonSize, 0] :\n            [0, -this.scrollButtonSize, 0, this.scrollButtonSize]);\n    }\n    /**\n     * Initialize the Navigator object\n     *\n     * @private\n     * @function Highcharts.Navigator#init\n     */\n    init(chart) {\n        const chartOptions = chart.options, navigatorOptions = chartOptions.navigator || {}, navigatorEnabled = navigatorOptions.enabled, scrollbarOptions = chartOptions.scrollbar || {}, scrollbarEnabled = scrollbarOptions.enabled, height = navigatorEnabled && navigatorOptions.height || 0, scrollbarHeight = scrollbarEnabled && scrollbarOptions.height || 0, scrollButtonSize = scrollbarOptions.buttonsEnabled && scrollbarHeight || 0;\n        this.handles = [];\n        this.shades = [];\n        this.chart = chart;\n        this.setBaseSeries();\n        this.height = height;\n        this.scrollbarHeight = scrollbarHeight;\n        this.scrollButtonSize = scrollButtonSize;\n        this.scrollbarEnabled = scrollbarEnabled;\n        this.navigatorEnabled = navigatorEnabled;\n        this.navigatorOptions = navigatorOptions;\n        this.scrollbarOptions = scrollbarOptions;\n        this.setOpposite();\n        const navigator = this, baseSeries = navigator.baseSeries, xAxisIndex = chart.xAxis.length, yAxisIndex = chart.yAxis.length, baseXaxis = baseSeries && baseSeries[0] && baseSeries[0].xAxis ||\n            chart.xAxis[0] || { options: {} };\n        chart.isDirtyBox = true;\n        if (navigator.navigatorEnabled) {\n            const offsets = this.getXAxisOffsets();\n            // An x axis is required for scrollbar also\n            navigator.xAxis = new (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default())(chart, Navigator_merge({\n                // Inherit base xAxis' break, ordinal options and overscroll\n                breaks: baseXaxis.options.breaks,\n                ordinal: baseXaxis.options.ordinal,\n                overscroll: baseXaxis.options.overscroll\n            }, navigatorOptions.xAxis, {\n                type: 'datetime',\n                yAxis: navigatorOptions.yAxis?.id,\n                index: xAxisIndex,\n                isInternal: true,\n                offset: 0,\n                keepOrdinalPadding: true, // #2436\n                startOnTick: false,\n                endOnTick: false,\n                // Inherit base xAxis' padding when ordinal is false (#16915).\n                minPadding: baseXaxis.options.ordinal ? 0 :\n                    baseXaxis.options.minPadding,\n                maxPadding: baseXaxis.options.ordinal ? 0 :\n                    baseXaxis.options.maxPadding,\n                zoomEnabled: false\n            }, chart.inverted ? {\n                offsets,\n                width: height\n            } : {\n                offsets,\n                height\n            }), 'xAxis');\n            navigator.yAxis = new (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default())(chart, Navigator_merge(navigatorOptions.yAxis, {\n                alignTicks: false,\n                offset: 0,\n                index: yAxisIndex,\n                isInternal: true,\n                reversed: Navigator_pick((navigatorOptions.yAxis &&\n                    navigatorOptions.yAxis.reversed), (chart.yAxis[0] && chart.yAxis[0].reversed), false), // #14060\n                zoomEnabled: false\n            }, chart.inverted ? {\n                width: height\n            } : {\n                height: height\n            }), 'yAxis');\n            // If we have a base series, initialize the navigator series\n            if (baseSeries || navigatorOptions.series.data) {\n                navigator.updateNavigatorSeries(false);\n                // If not, set up an event to listen for added series\n            }\n            else if (chart.series.length === 0) {\n                navigator.unbindRedraw = Navigator_addEvent(chart, 'beforeRedraw', function () {\n                    // We've got one, now add it as base\n                    if (chart.series.length > 0 && !navigator.series) {\n                        navigator.setBaseSeries();\n                        navigator.unbindRedraw(); // Reset\n                    }\n                });\n            }\n            navigator.reversedExtremes = (chart.inverted && !navigator.xAxis.reversed) || (!chart.inverted && navigator.xAxis.reversed);\n            // Render items, so we can bind events to them:\n            navigator.renderElements();\n            // Add mouse events\n            navigator.addMouseEvents();\n            // In case of scrollbar only, fake an x axis to get translation\n        }\n        else {\n            navigator.xAxis = {\n                chart,\n                navigatorAxis: {\n                    fake: true\n                },\n                translate: function (value, reverse) {\n                    const axis = chart.xAxis[0], ext = axis.getExtremes(), scrollTrackWidth = axis.len - 2 * scrollButtonSize, min = numExt('min', axis.options.min, ext.dataMin), valueRange = numExt('max', axis.options.max, ext.dataMax) - min;\n                    return reverse ?\n                        // From pixel to value\n                        (value * valueRange / scrollTrackWidth) + min :\n                        // From value to pixel\n                        scrollTrackWidth * (value - min) / valueRange;\n                },\n                toPixels: function (value) {\n                    return this.translate(value);\n                },\n                toValue: function (value) {\n                    return this.translate(value, true);\n                }\n            };\n            navigator.xAxis.navigatorAxis.axis = navigator.xAxis;\n            navigator.xAxis.navigatorAxis.toFixedRange = (NavigatorAxisComposition.prototype.toFixedRange.bind(navigator.xAxis.navigatorAxis));\n        }\n        // Initialize the scrollbar\n        if (chart.options.scrollbar?.enabled) {\n            const options = Navigator_merge(chart.options.scrollbar, { vertical: chart.inverted });\n            if (!Navigator_isNumber(options.margin)) {\n                options.margin = chart.inverted ? -3 : 3;\n            }\n            chart.scrollbar = navigator.scrollbar = new Scrollbar_Scrollbar(chart.renderer, options, chart);\n            Navigator_addEvent(navigator.scrollbar, 'changed', function (e) {\n                const range = navigator.size, to = range * this.to, from = range * this.from;\n                navigator.hasDragged = navigator.scrollbar.hasDragged;\n                navigator.render(0, 0, from, to);\n                if (this.shouldUpdateExtremes(e.DOMType)) {\n                    setTimeout(function () {\n                        navigator.onMouseUp(e);\n                    });\n                }\n            });\n        }\n        // Add data events\n        navigator.addBaseSeriesEvents();\n        // Add redraw events\n        navigator.addChartEvents();\n    }\n    /**\n     * Set the opposite property on navigator\n     *\n     * @private\n     */\n    setOpposite() {\n        const navigatorOptions = this.navigatorOptions, navigatorEnabled = this.navigatorEnabled, chart = this.chart;\n        this.opposite = Navigator_pick(navigatorOptions.opposite, Boolean(!navigatorEnabled && chart.inverted)); // #6262\n    }\n    /**\n     * Get the union data extremes of the chart - the outer data extremes of the\n     * base X axis and the navigator axis.\n     *\n     * @private\n     * @function Highcharts.Navigator#getUnionExtremes\n     */\n    getUnionExtremes(returnFalseOnNoBaseSeries) {\n        const baseAxis = this.chart.xAxis[0], time = this.chart.time, navAxis = this.xAxis, navAxisOptions = navAxis.options, baseAxisOptions = baseAxis.options;\n        let ret;\n        if (!returnFalseOnNoBaseSeries || baseAxis.dataMin !== null) {\n            ret = {\n                dataMin: Navigator_pick(// #4053\n                time.parse(navAxisOptions?.min), numExt('min', time.parse(baseAxisOptions.min), baseAxis.dataMin, navAxis.dataMin, navAxis.min)),\n                dataMax: Navigator_pick(time.parse(navAxisOptions?.max), numExt('max', time.parse(baseAxisOptions.max), baseAxis.dataMax, navAxis.dataMax, navAxis.max))\n            };\n        }\n        return ret;\n    }\n    /**\n     * Set the base series and update the navigator series from this. With a bit\n     * of modification we should be able to make this an API method to be called\n     * from the outside\n     *\n     * @private\n     * @function Highcharts.Navigator#setBaseSeries\n     * @param {Highcharts.SeriesOptionsType} [baseSeriesOptions]\n     *        Additional series options for a navigator\n     * @param {boolean} [redraw]\n     *        Whether to redraw after update.\n     */\n    setBaseSeries(baseSeriesOptions, redraw) {\n        const chart = this.chart, baseSeries = this.baseSeries = [];\n        baseSeriesOptions = (baseSeriesOptions ||\n            chart.options && chart.options.navigator.baseSeries ||\n            (chart.series.length ?\n                // Find the first non-navigator series (#8430)\n                find(chart.series, (s) => (!s.options.isInternal)).index :\n                0));\n        // Iterate through series and add the ones that should be shown in\n        // navigator.\n        (chart.series || []).forEach((series, i) => {\n            if (\n            // Don't include existing nav series\n            !series.options.isInternal &&\n                (series.options.showInNavigator ||\n                    (i === baseSeriesOptions ||\n                        series.options.id === baseSeriesOptions) &&\n                        series.options.showInNavigator !== false)) {\n                baseSeries.push(series);\n            }\n        });\n        // When run after render, this.xAxis already exists\n        if (this.xAxis && !this.xAxis.navigatorAxis.fake) {\n            this.updateNavigatorSeries(true, redraw);\n        }\n    }\n    /**\n     * Update series in the navigator from baseSeries, adding new if does not\n     * exist.\n     *\n     * @private\n     * @function Highcharts.Navigator.updateNavigatorSeries\n     */\n    updateNavigatorSeries(addEvents, redraw) {\n        const navigator = this, chart = navigator.chart, baseSeries = navigator.baseSeries, navSeriesMixin = {\n            enableMouseTracking: false,\n            index: null, // #6162\n            linkedTo: null, // #6734\n            group: 'nav', // For columns\n            padXAxis: false,\n            xAxis: this.navigatorOptions.xAxis?.id,\n            yAxis: this.navigatorOptions.yAxis?.id,\n            showInLegend: false,\n            stacking: void 0, // #4823\n            isInternal: true,\n            states: {\n                inactive: {\n                    opacity: 1\n                }\n            }\n        }, \n        // Remove navigator series that are no longer in the baseSeries\n        navigatorSeries = navigator.series =\n            (navigator.series || []).filter((navSeries) => {\n                const base = navSeries.baseSeries;\n                if (baseSeries.indexOf(base) < 0) { // Not in array\n                    // If there is still a base series connected to this\n                    // series, remove event handler and reference.\n                    if (base) {\n                        Navigator_removeEvent(base, 'updatedData', navigator.updatedDataHandler);\n                        delete base.navigatorSeries;\n                    }\n                    // Kill the nav series. It may already have been\n                    // destroyed (#8715).\n                    if (navSeries.chart) {\n                        navSeries.destroy();\n                    }\n                    return false;\n                }\n                return true;\n            });\n        let baseOptions, mergedNavSeriesOptions, chartNavigatorSeriesOptions = navigator.navigatorOptions.series, baseNavigatorOptions;\n        // Go through each base series and merge the options to create new\n        // series\n        if (baseSeries && baseSeries.length) {\n            baseSeries.forEach((base) => {\n                const linkedNavSeries = base.navigatorSeries, userNavOptions = Navigator_extend(\n                // Grab color and visibility from base as default\n                {\n                    color: base.color,\n                    visible: base.visible\n                }, !isArray(chartNavigatorSeriesOptions) ?\n                    chartNavigatorSeriesOptions :\n                    Navigator_defaultOptions.navigator.series);\n                // Don't update if the series exists in nav and we have disabled\n                // adaptToUpdatedData.\n                if (linkedNavSeries &&\n                    navigator.navigatorOptions.adaptToUpdatedData === false) {\n                    return;\n                }\n                navSeriesMixin.name = 'Navigator ' + baseSeries.length;\n                baseOptions = base.options || {};\n                baseNavigatorOptions = baseOptions.navigatorOptions || {};\n                // The dataLabels options are not merged correctly\n                // if the settings are an array, #13847.\n                userNavOptions.dataLabels = splat(userNavOptions.dataLabels);\n                mergedNavSeriesOptions = Navigator_merge(baseOptions, navSeriesMixin, userNavOptions, baseNavigatorOptions);\n                // Once nav series type is resolved, pick correct pointRange\n                mergedNavSeriesOptions.pointRange = Navigator_pick(\n                // Stricte set pointRange in options\n                userNavOptions.pointRange, baseNavigatorOptions.pointRange, \n                // Fallback to default values, e.g. `null` for column\n                Navigator_defaultOptions.plotOptions[mergedNavSeriesOptions.type || 'line'].pointRange);\n                // Merge data separately. Do a slice to avoid mutating the\n                // navigator options from base series (#4923).\n                const navigatorSeriesData = baseNavigatorOptions.data || userNavOptions.data;\n                navigator.hasNavigatorData =\n                    navigator.hasNavigatorData || !!navigatorSeriesData;\n                mergedNavSeriesOptions.data = (navigatorSeriesData ||\n                    baseOptions.data?.slice(0));\n                // Update or add the series\n                if (linkedNavSeries && linkedNavSeries.options) {\n                    linkedNavSeries.update(mergedNavSeriesOptions, redraw);\n                }\n                else {\n                    base.navigatorSeries = chart.initSeries(mergedNavSeriesOptions);\n                    // Set data on initial run with dataSorting enabled (#20318)\n                    chart.setSortedData();\n                    base.navigatorSeries.baseSeries = base; // Store ref\n                    navigatorSeries.push(base.navigatorSeries);\n                }\n            });\n        }\n        // If user has defined data (and no base series) or explicitly defined\n        // navigator.series as an array, we create these series on top of any\n        // base series.\n        if (chartNavigatorSeriesOptions.data &&\n            !(baseSeries && baseSeries.length) ||\n            isArray(chartNavigatorSeriesOptions)) {\n            navigator.hasNavigatorData = false;\n            // Allow navigator.series to be an array\n            chartNavigatorSeriesOptions =\n                splat(chartNavigatorSeriesOptions);\n            chartNavigatorSeriesOptions.forEach((userSeriesOptions, i) => {\n                navSeriesMixin.name =\n                    'Navigator ' + (navigatorSeries.length + 1);\n                mergedNavSeriesOptions = Navigator_merge(Navigator_defaultOptions.navigator.series, {\n                    // Since we don't have a base series to pull color from,\n                    // try to fake it by using color from series with same\n                    // index. Otherwise pull from the colors array. We need\n                    // an explicit color as otherwise updates will increment\n                    // color counter and we'll get a new color for each\n                    // update of the nav series.\n                    color: chart.series[i] &&\n                        !chart.series[i].options.isInternal &&\n                        chart.series[i].color ||\n                        chart.options.colors[i] ||\n                        chart.options.colors[0]\n                }, navSeriesMixin, userSeriesOptions);\n                mergedNavSeriesOptions.data = userSeriesOptions.data;\n                if (mergedNavSeriesOptions.data) {\n                    navigator.hasNavigatorData = true;\n                    navigatorSeries.push(chart.initSeries(mergedNavSeriesOptions));\n                }\n            });\n        }\n        if (addEvents) {\n            this.addBaseSeriesEvents();\n        }\n    }\n    /**\n     * Add data events.\n     * For example when main series is updated we need to recalculate extremes\n     *\n     * @private\n     * @function Highcharts.Navigator#addBaseSeriesEvent\n     */\n    addBaseSeriesEvents() {\n        const navigator = this, baseSeries = navigator.baseSeries || [];\n        // Bind modified extremes event to first base's xAxis only.\n        // In event of > 1 base-xAxes, the navigator will ignore those.\n        // Adding this multiple times to the same axis is no problem, as\n        // duplicates should be discarded by the browser.\n        if (baseSeries[0] && baseSeries[0].xAxis) {\n            baseSeries[0].eventsToUnbind.push(Navigator_addEvent(baseSeries[0].xAxis, 'foundExtremes', this.modifyBaseAxisExtremes));\n        }\n        baseSeries.forEach((base) => {\n            // Link base series show/hide to navigator series visibility\n            base.eventsToUnbind.push(Navigator_addEvent(base, 'show', function () {\n                if (this.navigatorSeries) {\n                    this.navigatorSeries.setVisible(true, false);\n                }\n            }));\n            base.eventsToUnbind.push(Navigator_addEvent(base, 'hide', function () {\n                if (this.navigatorSeries) {\n                    this.navigatorSeries.setVisible(false, false);\n                }\n            }));\n            // Respond to updated data in the base series, unless explicitly\n            // not adapting to data changes.\n            if (this.navigatorOptions.adaptToUpdatedData !== false) {\n                if (base.xAxis) {\n                    base.eventsToUnbind.push(Navigator_addEvent(base, 'updatedData', this.updatedDataHandler));\n                }\n            }\n            // Handle series removal\n            base.eventsToUnbind.push(Navigator_addEvent(base, 'remove', function () {\n                if (baseSeries) {\n                    erase(baseSeries, base); // #21043\n                }\n                if (this.navigatorSeries && navigator.series) {\n                    erase(navigator.series, this.navigatorSeries);\n                    if (Navigator_defined(this.navigatorSeries.options)) {\n                        this.navigatorSeries.remove(false);\n                    }\n                    delete this.navigatorSeries;\n                }\n            }));\n        });\n    }\n    /**\n     * Get minimum from all base series connected to the navigator\n     * @private\n     * @param {number} currentSeriesMin\n     *        Minium from the current series\n     * @return {number}\n     *         Minimum from all series\n     */\n    getBaseSeriesMin(currentSeriesMin) {\n        return this.baseSeries.reduce(function (min, series) {\n            // #10193\n            return Math.min(min, series.getColumn('x')[0] ?? min);\n        }, currentSeriesMin);\n    }\n    /**\n     * Set the navigator x axis extremes to reflect the total. The navigator\n     * extremes should always be the extremes of the union of all series in the\n     * chart as well as the navigator series.\n     *\n     * @private\n     * @function Highcharts.Navigator#modifyNavigatorAxisExtremes\n     */\n    modifyNavigatorAxisExtremes() {\n        const xAxis = this.xAxis;\n        if (typeof xAxis.getExtremes !== 'undefined') {\n            const unionExtremes = this.getUnionExtremes(true);\n            if (unionExtremes &&\n                (unionExtremes.dataMin !== xAxis.min ||\n                    unionExtremes.dataMax !== xAxis.max)) {\n                xAxis.min = unionExtremes.dataMin;\n                xAxis.max = unionExtremes.dataMax;\n            }\n        }\n    }\n    /**\n     * Hook to modify the base axis extremes with information from the Navigator\n     *\n     * @private\n     * @function Highcharts.Navigator#modifyBaseAxisExtremes\n     */\n    modifyBaseAxisExtremes() {\n        const baseXAxis = this, navigator = baseXAxis.chart.navigator, baseExtremes = baseXAxis.getExtremes(), baseMin = baseExtremes.min, baseMax = baseExtremes.max, baseDataMin = baseExtremes.dataMin, baseDataMax = baseExtremes.dataMax, range = baseMax - baseMin, stickToMin = navigator.stickToMin, stickToMax = navigator.stickToMax, overscroll = Navigator_pick(baseXAxis.ordinal?.convertOverscroll(baseXAxis.options.overscroll), 0), navigatorSeries = navigator.series && navigator.series[0], hasSetExtremes = !!baseXAxis.setExtremes, \n        // When the extremes have been set by range selector button, don't\n        // stick to min or max. The range selector buttons will handle the\n        // extremes. (#5489)\n        unmutable = baseXAxis.eventArgs &&\n            baseXAxis.eventArgs.trigger === 'rangeSelectorButton';\n        let newMax, newMin;\n        if (!unmutable) {\n            // If the zoomed range is already at the min, move it to the right\n            // as new data comes in\n            if (stickToMin) {\n                newMin = baseDataMin;\n                newMax = newMin + range;\n            }\n            // If the zoomed range is already at the max, move it to the right\n            // as new data comes in\n            if (stickToMax) {\n                newMax = baseDataMax + overscroll;\n                // If stickToMin is true, the new min value is set above\n                if (!stickToMin) {\n                    newMin = Math.max(baseDataMin, // Don't go below data extremes (#13184)\n                    newMax - range, navigator.getBaseSeriesMin(navigatorSeries && navigatorSeries.xData ?\n                        navigatorSeries.xData[0] :\n                        -Number.MAX_VALUE));\n                }\n            }\n            // Update the extremes\n            if (hasSetExtremes && (stickToMin || stickToMax)) {\n                if (Navigator_isNumber(newMin)) {\n                    baseXAxis.min = baseXAxis.userMin = newMin;\n                    baseXAxis.max = baseXAxis.userMax = newMax;\n                }\n            }\n        }\n        // Reset\n        navigator.stickToMin =\n            navigator.stickToMax = null;\n    }\n    /**\n     * Handler for updated data on the base series. When data is modified, the\n     * navigator series must reflect it. This is called from the Chart.redraw\n     * function before axis and series extremes are computed.\n     *\n     * @private\n     * @function Highcharts.Navigator#updateDataHandler\n     */\n    updatedDataHandler() {\n        const navigator = this.chart.navigator, baseSeries = this, navigatorSeries = this.navigatorSeries, shouldStickToMax = navigator.reversedExtremes ?\n            Math.round(navigator.zoomedMin) === 0 :\n            Math.round(navigator.zoomedMax) >= Math.round(navigator.size);\n        // If the scrollbar is scrolled all the way to the right, keep right as\n        // new data comes in, unless user set navigator.stickToMax to false.\n        navigator.stickToMax = Navigator_pick(this.chart.options.navigator &&\n            this.chart.options.navigator.stickToMax, shouldStickToMax);\n        navigator.stickToMin = navigator.shouldStickToMin(baseSeries, navigator);\n        // Set the navigator series data to the new data of the base series\n        if (navigatorSeries && !navigator.hasNavigatorData) {\n            navigatorSeries.options.pointStart = baseSeries.getColumn('x')[0];\n            navigatorSeries.setData(baseSeries.options.data, false, null, false); // #5414\n        }\n    }\n    /**\n     * Detect if the zoomed area should stick to the minimum, #14742.\n     *\n     * @private\n     * @function Highcharts.Navigator#shouldStickToMin\n     */\n    shouldStickToMin(baseSeries, navigator) {\n        const xDataMin = navigator.getBaseSeriesMin(baseSeries.getColumn('x')[0]), xAxis = baseSeries.xAxis, max = xAxis.max, min = xAxis.min, range = xAxis.options.range;\n        let stickToMin = true;\n        if (Navigator_isNumber(max) && Navigator_isNumber(min)) {\n            // If range declared, stick to the minimum only if the range\n            // is smaller than the data set range.\n            if (range && max - xDataMin > 0) {\n                stickToMin = max - xDataMin < range;\n            }\n            else {\n                // If the current axis minimum falls outside the new\n                // updated dataset, we must adjust.\n                stickToMin = min <= xDataMin;\n            }\n        }\n        else {\n            stickToMin = false; // #15864\n        }\n        return stickToMin;\n    }\n    /**\n     * Add chart events, like redrawing navigator, when chart requires that.\n     *\n     * @private\n     * @function Highcharts.Navigator#addChartEvents\n     */\n    addChartEvents() {\n        if (!this.eventsToUnbind) {\n            this.eventsToUnbind = [];\n        }\n        this.eventsToUnbind.push(\n        // Move the scrollbar after redraw, like after data updata even if\n        // axes don't redraw\n        Navigator_addEvent(this.chart, 'redraw', function () {\n            const navigator = this.navigator, xAxis = navigator && (navigator.baseSeries &&\n                navigator.baseSeries[0] &&\n                navigator.baseSeries[0].xAxis ||\n                this.xAxis[0]); // #5709, #13114\n            if (xAxis) {\n                navigator.render(xAxis.min, xAxis.max);\n            }\n        }), \n        // Make room for the navigator, can be placed around the chart:\n        Navigator_addEvent(this.chart, 'getMargins', function () {\n            const chart = this, navigator = chart.navigator;\n            let marginName = navigator.opposite ?\n                'plotTop' : 'marginBottom';\n            if (chart.inverted) {\n                marginName = navigator.opposite ?\n                    'marginRight' : 'plotLeft';\n            }\n            chart[marginName] = (chart[marginName] || 0) + (navigator.navigatorEnabled || !chart.inverted ?\n                navigator.height +\n                    (this.scrollbar?.options.margin || 0) +\n                    navigator.scrollbarHeight : 0) + (navigator.navigatorOptions.margin || 0);\n        }), Navigator_addEvent(Navigator, 'setRange', function (e) {\n            this.chart.xAxis[0].setExtremes(e.min, e.max, e.redraw, e.animation, e.eventArguments);\n        }));\n    }\n    /**\n     * Destroys allocated elements.\n     *\n     * @private\n     * @function Highcharts.Navigator#destroy\n     */\n    destroy() {\n        // Disconnect events added in addEvents\n        this.removeEvents();\n        if (this.xAxis) {\n            erase(this.chart.xAxis, this.xAxis);\n            erase(this.chart.axes, this.xAxis);\n        }\n        if (this.yAxis) {\n            erase(this.chart.yAxis, this.yAxis);\n            erase(this.chart.axes, this.yAxis);\n        }\n        // Destroy series\n        (this.series || []).forEach((s) => {\n            if (s.destroy) {\n                s.destroy();\n            }\n        });\n        // Destroy properties\n        [\n            'series', 'xAxis', 'yAxis', 'shades', 'outline', 'scrollbarTrack',\n            'scrollbarRifles', 'scrollbarGroup', 'scrollbar', 'navigatorGroup',\n            'rendered'\n        ].forEach((prop) => {\n            if (this[prop] && this[prop].destroy) {\n                this[prop].destroy();\n            }\n            this[prop] = null;\n        });\n        // Destroy elements in collection\n        [this.handles].forEach((coll) => {\n            Navigator_destroyObjectProperties(coll);\n        });\n        // Clean up linked series\n        this.baseSeries.forEach((s) => {\n            s.navigatorSeries = void 0;\n        });\n        this.navigatorEnabled = false;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Navigator_Navigator = (Navigator);\n\n;// ./code/es-modules/Stock/Navigator/StandaloneNavigatorDefaults.js\n/* *\n *\n *  (c) 2010-2025 Mateusz Bernacik\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst standaloneNavigatorDefaults = {\n    chart: {\n        height: 70,\n        margin: [0, 5, 0, 5]\n    },\n    exporting: {\n        enabled: false\n    },\n    legend: {\n        enabled: false\n    },\n    navigator: {\n        enabled: false\n    },\n    plotOptions: {\n        series: {\n            states: {\n                hover: {\n                    enabled: false\n                }\n            },\n            marker: {\n                enabled: false\n            }\n        }\n    },\n    scrollbar: {\n        enabled: false\n    },\n    title: {\n        text: ''\n    },\n    tooltip: {\n        enabled: false\n    },\n    xAxis: {\n        visible: false\n    },\n    yAxis: {\n        height: 0,\n        visible: false\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const StandaloneNavigatorDefaults = (standaloneNavigatorDefaults);\n\n;// ./code/es-modules/Stock/Navigator/StandaloneNavigator.js\n/* *\n *\n *  (c) 2010-2025 Mateusz Bernacik\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\nconst { merge: StandaloneNavigator_merge, addEvent: StandaloneNavigator_addEvent, fireEvent: StandaloneNavigator_fireEvent, pick: StandaloneNavigator_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The StandaloneNavigator class. The StandaloneNavigator class allows for\n * creating a standalone navigator component that synchronizes the extremes\n * across multiple bound charts.\n *\n * @class\n * @name Highcharts.StandaloneNavigator\n *\n * @param {string|Highcharts.HTMLDOMElement} [renderTo]\n * The DOM element to render to, or its id.\n *\n * @param {StandaloneNavigatorOptions} userOptions\n * The standalone navigator options.\n */\nclass StandaloneNavigator {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Factory function for standalone navigator.\n     *\n     * @function Highcharts.navigator\n     *\n     * @param {string|Highcharts.HTMLDOMElement} [renderTo]\n     * The DOM element to render to, or its id.\n     *\n     * @param {StandaloneNavigatorOptions} options\n     * The standalone navigator options with chart-like structure.\n     *\n     * Returns the navigator object.\n     */\n    static navigator(renderTo, options) {\n        const nav = new StandaloneNavigator(renderTo, options);\n        if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).navigators) {\n            (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).navigators = [nav];\n        }\n        else {\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().navigators.push(nav);\n        }\n        return nav;\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(element, userOptions) {\n        this.boundAxes = [];\n        this.userOptions = userOptions;\n        this.chartOptions = StandaloneNavigator_merge(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().getOptions(), StandaloneNavigatorDefaults, userOptions.chart, { navigator: userOptions });\n        if (this.chartOptions.chart && userOptions.height) {\n            this.chartOptions.chart.height = userOptions.height;\n        }\n        const chart = new (highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default())(element, this.chartOptions);\n        chart.options = StandaloneNavigator_merge(chart.options, { navigator: { enabled: true }, scrollbar: { enabled: true } });\n        if (this.chartOptions.navigator && this.chartOptions.scrollbar) {\n            this.chartOptions.navigator.enabled = true;\n            this.chartOptions.scrollbar.enabled = true;\n        }\n        this.navigator = new Navigator_Navigator(chart);\n        chart.navigator = this.navigator;\n        this.initNavigator();\n    }\n    /**\n     * Binds an axis to the standalone navigator,\n     * allowing the navigator to control the axis' range.\n     *\n     * @sample stock/standalone-navigator/bind/\n     *         Bind chart with a button\n     *\n     * @function Highcharts.StandaloneNavigator#bind\n     *\n     * @param {Axis | Chart} axisOrChart\n     *        The Axis or Chart to bind to the navigator.\n     *\n     * @param {boolean} [twoWay=true]\n     *        Enables two-way binding between the navigator and the axis/chart.\n     *        When true, changes in the navigator's range will update the axis\n     *        and vice versa. When false, changes in the navigator's range will\n     *        be reflected in the axis, but changes in the axis ranges won't be\n     *        reflected on the navigator.\n     */\n    bind(axisOrChart, twoWay = true) {\n        const nav = this;\n        // If the chart is passed, bind the first xAxis\n        const axis = (axisOrChart instanceof (highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default())) ?\n            axisOrChart.xAxis[0] :\n            axisOrChart;\n        if (!(axis instanceof (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()))) {\n            return;\n        }\n        const { min, max } = this.navigator.xAxis, removeEventCallbacks = [];\n        if (twoWay) {\n            const removeSetExtremesEvent = StandaloneNavigator_addEvent(axis, 'setExtremes', (e) => {\n                if (e.trigger === 'pan' ||\n                    e.trigger === 'zoom' ||\n                    e.trigger === 'mousewheel') {\n                    nav.setRange(e.min, e.max, true, e.trigger !== 'pan' && e.trigger !== 'mousewheel', { trigger: axis });\n                }\n            });\n            removeEventCallbacks.push(removeSetExtremesEvent);\n        }\n        const removeSetRangeEvent = StandaloneNavigator_addEvent(this.navigator, 'setRange', (e) => {\n            axis.setExtremes(e.min, e.max, e.redraw, e.animation);\n        });\n        removeEventCallbacks.push(removeSetRangeEvent);\n        let boundAxis = this.boundAxes.filter(function (boundAxis) {\n            return boundAxis.axis === axis;\n        })[0];\n        if (!boundAxis) {\n            boundAxis = { axis, callbacks: [] };\n            this.boundAxes.push(boundAxis);\n        }\n        boundAxis.callbacks = removeEventCallbacks;\n        // Show axis' series in navigator based on showInNavigator property\n        axis.series.forEach((series) => {\n            if (series.options.showInNavigator) {\n                nav.addSeries(series.options);\n            }\n        });\n        // Set extremes to match the navigator's extremes\n        axis.setExtremes(min, max);\n        // Unbind the axis before it's destroyed\n        StandaloneNavigator_addEvent(axis, 'destroy', (e) => {\n            if (!e.keepEvents) {\n                this.unbind(axis);\n            }\n        });\n    }\n    /**\n     * Unbinds a single axis or all bound axes from the standalone navigator.\n     *\n     * @sample stock/standalone-navigator/unbind/\n     *         Unbind chart with a button\n     *\n     * @function Highcharts.StandaloneNavigator#unbind\n     *\n     * @param {Chart | Axis | undefined} axisOrChart\n     *        Passing a Chart object unbinds the first X axis of the chart,\n     *        an Axis object unbinds that specific axis,\n     *        and undefined unbinds all axes bound to the navigator.\n     */\n    unbind(axisOrChart) {\n        // If no axis or chart is provided, unbind all bound axes\n        if (!axisOrChart) {\n            this.boundAxes.forEach(({ callbacks }) => {\n                callbacks.forEach((removeCallback) => removeCallback());\n            });\n            this.boundAxes.length = 0;\n            return;\n        }\n        const axis = (axisOrChart instanceof (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default())) ?\n            axisOrChart :\n            axisOrChart.xAxis[0];\n        for (let i = this.boundAxes.length - 1; i >= 0; i--) {\n            if (this.boundAxes[i].axis === axis) {\n                this.boundAxes[i].callbacks.forEach((callback) => callback());\n                this.boundAxes.splice(i, 1);\n            }\n        }\n    }\n    /**\n     * Destroys allocated standalone navigator elements.\n     *\n     * @function Highcharts.StandaloneNavigator#destroy\n     */\n    destroy() {\n        // Disconnect events\n        this.boundAxes.forEach(({ callbacks }) => {\n            callbacks.forEach((removeCallback) => removeCallback());\n        });\n        this.boundAxes.length = 0;\n        this.navigator.destroy();\n        this.navigator.chart.destroy();\n    }\n    /**\n     * Updates the standalone navigator's options with a new set of user\n     * options.\n     *\n     * @sample stock/standalone-navigator/update/\n     *         Bind chart with a button\n     *\n     * @function Highcharts.StandaloneNavigator#update\n     *\n     * @param  {StandaloneNavigatorOptions} newOptions\n     *         Updates the standalone navigator's options with new user options.\n     *\n     * @param  {boolean | undefined} redraw\n     *         Whether to redraw the standalone navigator. By default, if not\n     *         specified, the standalone navigator will be redrawn.\n     */\n    update(newOptions, redraw) {\n        this.chartOptions = StandaloneNavigator_merge(this.chartOptions, newOptions.height && { chart: { height: newOptions.height } }, newOptions.chart, { navigator: newOptions });\n        this.navigator.chart.update(this.chartOptions, redraw);\n    }\n    /**\n     * Redraws the standalone navigator.\n     *\n     * @function Highcharts.StandaloneNavigator#redraw\n     */\n    redraw() {\n        this.navigator.chart.redraw();\n    }\n    /**\n     * Adds a series to the standalone navigator.\n     *\n     * @private\n     *\n     * @param {SeriesOptions} seriesOptions\n     *        Options for the series to be added to the navigator.\n     */\n    addSeries(seriesOptions) {\n        this.navigator.chart.addSeries(StandaloneNavigator_merge(seriesOptions, { showInNavigator: StandaloneNavigator_pick(seriesOptions.showInNavigator, true) }));\n        this.navigator.setBaseSeries();\n    }\n    /**\n     * Initialize the standalone navigator.\n     *\n     * @private\n     */\n    initNavigator() {\n        const nav = this.navigator;\n        nav.top = 1;\n        nav.xAxis.setScale();\n        nav.yAxis.setScale();\n        nav.xAxis.render();\n        nav.yAxis.render();\n        nav.series?.forEach((s) => {\n            s.translate();\n            s.render();\n            s.redraw();\n        });\n        const { min, max } = this.getInitialExtremes();\n        nav.chart.xAxis[0].userMin = min;\n        nav.chart.xAxis[0].userMax = max;\n        nav.render(min, max);\n    }\n    /**\n     * Get the current range of the standalone navigator.\n     *\n     * @sample stock/standalone-navigator/getrange/\n     *         Report the standalone navigator's range by clicking on a button\n     *\n     * @function Highcharts.StandaloneNavigator#getRange\n     *\n     * @return {Highcharts.ExtremesObject}\n     *         The current range of the standalone navigator.\n     */\n    getRange() {\n        const { min, max } = this.navigator.chart.xAxis[0].getExtremes(), { userMin, userMax, min: dataMin, max: dataMax } = this.navigator.xAxis.getExtremes();\n        return {\n            min: StandaloneNavigator_pick(min, dataMin),\n            max: StandaloneNavigator_pick(max, dataMax),\n            dataMin,\n            dataMax,\n            userMin,\n            userMax\n        };\n    }\n    /**\n     * Set the range of the standalone navigator.\n     *\n     * @sample stock/standalone-navigator/setrange/\n     *         Set range from a button\n     *\n     * @function Highcharts.StandaloneNavigator#setRange\n     *\n     * @param {number | undefined} min\n     *        The new minimum value.\n     *\n     * @param {number | undefined} max\n     *        The new maximum value.\n     *\n     * @emits Highcharts.StandaloneNavigator#event:setRange\n     */\n    setRange(min, max, redraw, animation, eventArguments) {\n        StandaloneNavigator_fireEvent(this.navigator, 'setRange', {\n            min,\n            max,\n            redraw,\n            animation,\n            eventArguments: StandaloneNavigator_merge(eventArguments, { trigger: 'navigator' })\n        });\n    }\n    /**\n     * Get the initial, options based extremes for the standalone navigator.\n     *\n     * @private\n     *\n     * @return {{ min: number, max: number }}\n     *         The initial minimum and maximum extremes values.\n     */\n    getInitialExtremes() {\n        const { min, max } = this.navigator.xAxis.getExtremes();\n        return {\n            min: min,\n            max: max\n        };\n    }\n}\n/* harmony default export */ const Navigator_StandaloneNavigator = (StandaloneNavigator);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Standalone Navigator options.\n *\n * @interface Highcharts.StandaloneNavigatorOptions\n */ /**\n*/\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/navigator.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.StandaloneNavigator = G.StandaloneNavigator || Navigator_StandaloneNavigator;\nG.navigator = G.StandaloneNavigator.navigator;\nNavigator_NavigatorComposition.compose(G.Chart, G.Axis, G.Series);\n/* harmony default export */ const navigator_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__960__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__608__", "__WEBPACK_EXTERNAL_MODULE__540__", "NavigatorConstructor", "ScrollbarAxis", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "navigator_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "isTouchDevice", "addEvent", "merge", "pick", "composedMembers", "onChartAfterAddSeries", "navigator", "setBaseSeries", "onChartAfterSetChartSize", "legendOptions", "xAxis", "yAxis", "legend", "options", "scrollbarHeight", "scrollButtonSize", "inverted", "left", "opposite", "chartWidth", "height", "spacing", "top", "plotTop", "plotLeft", "navigatorOptions", "chartHeight", "scrollbar", "margin", "rangeSelector", "extraBottom<PERSON>argin", "getHeight", "verticalAlign", "layout", "enabled", "floating", "legend<PERSON><PERSON>ght", "titleOffset", "setAxisSize", "onChartAfterUpdate", "event", "scroller", "redraw", "animation", "onChartBeforeRender", "onChartBeforeShowResetZoom", "chartOptions", "zooming", "type", "pinchType", "onChartCallback", "chart", "extremes", "getExtremes", "render", "min", "max", "onChartUpdate", "e", "scrollbarOptions", "Navigator_ChartNavigatorComposition", "compose", "ChartClass", "NavigatorClass", "pushUnique", "chartProto", "callbacks", "push", "NavigatorAxisComposition_isTouchDevice", "NavigatorAxisComposition_addEvent", "correctFloat", "defined", "isNumber", "NavigatorAxisComposition_pick", "onAxisInit", "axis", "navigatorA<PERSON>s", "NavigatorAxisAdditions", "onAxisSetExtremes", "zoomed", "zoomType", "isXAxis", "trigger", "range", "previousZoom", "preventDefault", "AxisClass", "keepProps", "includes", "constructor", "destroy", "toFixedRange", "pxMin", "pxMax", "fixedMin", "fixedMax", "halfPointRange", "pointRange", "newMin", "translate", "horiz", "newMax", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "parse", "color", "seriesTypes", "Navigator<PERSON>efaults", "maskInside", "handles", "width", "borderRadius", "symbols", "lineWidth", "backgroundColor", "borderColor", "maskFill", "setOpacity", "outlineColor", "outlineWidth", "series", "areaspline", "fillOpacity", "compare", "sonification", "dataGrouping", "approximation", "groupPixelWidth", "firstAnchor", "anchor", "lastAnchor", "units", "dataLabels", "zIndex", "id", "className", "lineColor", "marker", "threshold", "tick<PERSON><PERSON>th", "gridLineColor", "gridLineWidth", "tickPixelInterval", "labels", "align", "style", "fontSize", "opacity", "textOutline", "x", "y", "crosshair", "startOnTick", "endOnTick", "minPadding", "maxPadding", "title", "text", "tickWidth", "Symbols_defined", "Symbols_isNumber", "Symbols_pick", "SVG_Symbols", "rect", "w", "h", "r", "roundedRect", "<PERSON><PERSON><PERSON><PERSON>", "Navigator_NavigatorSymbols", "_x", "_y", "halfWidth", "Math", "markerPosition", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "StockUtilities_defined", "setOptions", "composed", "getRendererType", "setFixedRange", "NavigatorComposition_setFixedRange", "dataMax", "dataMin", "fixedRange", "NavigatorComposition_addEvent", "extend", "onSeriesAfterUpdate", "isInternal", "Navigator_NavigatorComposition", "SeriesClass", "NavigatorAxisComposition", "ScrollbarAxis_composed", "ScrollbarAxis_addEvent", "ScrollbarAxis_defined", "ScrollbarAxis_pick", "ScrollbarAxis_pushUnique", "Sc<PERSON><PERSON>", "axisMin", "axisMax", "scrollMin", "Infinity", "scrollMax", "onAxisAfterGetOffset", "index", "scrollbarsOffsets", "axisOffset", "size", "onAxisAfterInit", "vertical", "renderer", "to", "from", "unitedMin", "unitedMax", "reversed", "shouldUpdateExtremes", "DOMType", "animate", "setExtremes", "setRang<PERSON>", "onAxisAfterRender", "offsetsIndex", "offset", "axisTitleMargin", "axisMargin", "position", "xPosition", "isNaN", "interval", "ScrollbarClass", "Axis_ScrollbarAxis", "Scrollbar_ScrollbarDefaults", "barBorderRadius", "buttonBorderRadius", "buttonsEnabled", "liveRedraw", "min<PERSON><PERSON><PERSON>", "step", "barBackgroundColor", "barBorder<PERSON>idth", "barBorderColor", "buttonArrowColor", "buttonBackgroundColor", "buttonBorderColor", "buttonBorderWidth", "rifleColor", "trackBackgroundColor", "trackBorderColor", "trackBorderRadius", "trackBorderWidth", "defaultOptions", "Scrollbar_addEvent", "Scrollbar_correctFloat", "crisp", "Scrollbar_defined", "destroyObjectProperties", "fireEvent", "Scrollbar_merge", "Scrollbar_pick", "removeEvent", "swapXY", "path", "for<PERSON>ach", "seg", "temp", "len", "length", "i", "_events", "chartX", "chartY", "scrollbarButtons", "scrollbarLeft", "scrollbarStrokeWidth", "scrollbarTop", "init", "addEvents", "buttonsOrder", "buttons", "bar", "scrollbarGroup", "element", "track", "mouseDownHandler", "bind", "mouseMoveHandler", "mouseUpHandler", "buttonToMinClick", "buttonToMaxClick", "trackClick", "ownerDocument", "args", "apply", "updatePosition", "DOMEvent", "cursorToScrollbarPosition", "normalizedEvent", "minWidthDifference", "calculatedWidth", "xOffset", "<PERSON><PERSON><PERSON><PERSON>", "yOffset", "removeEvents", "drawScrollbarButton", "group", "g", "add", "addClass", "styledMode", "attr", "stroke", "fill", "strokeWidth", "arrow", "userOptions", "pointer", "normalize", "mousePosition", "initPositions", "grabbedCenter", "change", "direction", "touches", "chartPosition", "hasDragged", "method", "rendered", "show", "translateX", "translateY", "hide", "scrollbarRifles", "fromPX", "newSize", "fullWidth", "toPX", "ceil", "newPos", "floor", "newRiflesPos", "showFull", "eventType", "svg", "boosted", "update", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "Navigator_defaultOptions", "Navigator_isTouchDevice", "Navigator_addEvent", "clamp", "Navigator_correctFloat", "Navigator_defined", "Navigator_destroyObjectProperties", "erase", "Navigator_extend", "find", "Navigator_fireEvent", "isArray", "Navigator_isNumber", "Navigator_merge", "Navigator_pick", "Navigator_removeEvent", "splat", "numExt", "extreme", "numbers", "filter", "Navigator", "isDirty", "<PERSON><PERSON><PERSON><PERSON>", "verb", "round", "parseInt", "drawOutline", "zoomedMin", "zoomedMax", "outline", "halfOutline", "outlineCorrection", "navigatorSize", "navigatorTop", "lineTop", "lineBtm", "verticalMin", "drawMasks", "navigator<PERSON><PERSON>ght", "shades", "shade", "renderElements", "mouseCursor", "cursor", "navigatorGroup", "visibility", "hasMask", "css", "handlesOptions", "symbolName", "symbolUrl", "isImg", "symbolFn", "symbol", "addMouseEvents", "rotation", "rotationOriginX", "rotationOriginY", "invertedUpdate", "setOpposite", "navigator<PERSON><PERSON><PERSON>", "adaptToUpdatedData", "baseSeries", "updatedDataHandler", "eventsToUnbind", "offsets", "getXAxisOffsets", "scrollbarXAxis", "fake", "minRange", "max<PERSON><PERSON><PERSON>", "navigator<PERSON><PERSON><PERSON>", "plot<PERSON>id<PERSON>", "plotHeight", "toPixels", "abs", "toValue", "currentRange", "grabbedLeft", "grabbedRight", "fixedWidth", "container", "onMouseMove", "onMouseUp", "getPartsEvents", "renderTo", "concat", "modifyNavigatorAxisExtremes", "eventName", "events", "name", "navigatorItem", "shadesMousedown", "navigatorPosition", "ext", "dragOffset", "reversedExtremes", "getUnionExtremes", "eventArguments", "handlesMousedown", "baseXAxis", "reverse", "otherHandlePos", "fixedExtreme", "pageX", "setTimeout", "unionExtremes", "triggerOp", "keys", "unbind", "removeBaseSeriesEvents", "modifyBaseAxisExtremes", "scrollbarEnabled", "xAxisIndex", "yAxisIndex", "baseXaxis", "isDirtyBox", "breaks", "ordinal", "overscroll", "keepOrdinalPadding", "zoomEnabled", "alignTicks", "data", "updateNavigatorSeries", "unbindRedraw", "value", "scrollTrackWidth", "valueRange", "addBaseSeriesEvents", "addChartEvents", "Boolean", "returnFalseOnNoBaseSeries", "ret", "baseAxis", "time", "navAxis", "navAxisOptions", "baseAxisOptions", "baseSeriesOptions", "s", "showInNavigator", "navSeriesMixin", "enableMouseTracking", "linkedTo", "padXAxis", "showInLegend", "stacking", "states", "inactive", "navigatorSeries", "navSeries", "base", "indexOf", "baseOptions", "mergedNavSeriesOptions", "chartNavigatorSeriesOptions", "baseNavigatorOptions", "linkedNavSeries", "userNavOptions", "visible", "plotOptions", "navigatorSeriesData", "hasNavigatorData", "slice", "initSeries", "setSortedData", "userSeriesOptions", "colors", "setVisible", "remove", "getBaseSeriesMin", "currentSeriesMin", "reduce", "getColumn", "baseExtremes", "baseMin", "baseMax", "baseDataMin", "baseDataMax", "stickToMin", "stickToMax", "convertOverscroll", "hasSetExtremes", "eventArgs", "xData", "Number", "MAX_VALUE", "userMin", "userMax", "shouldStickToMax", "shouldStickToMin", "pointStart", "setData", "xDataMin", "marginName", "axes", "coll", "standaloneNavigatorDefaults", "exporting", "hover", "tooltip", "StandaloneNavigator_merge", "StandaloneNavigator_addEvent", "StandaloneNavigator_fireEvent", "StandaloneNavigator_pick", "StandaloneNavigator", "nav", "navigators", "boundAxes", "getOptions", "initNavigator", "axisOrChart", "twoWay", "removeEventCallbacks", "removeSetExtremesEvent", "removeSetRangeEvent", "boundAxis", "addSeries", "keepEvents", "removeCallback", "callback", "splice", "newOptions", "seriesOptions", "setScale", "getInitialExtremes", "getRange", "G", "Chart", "Axis", "Series"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,WAAc,EAClP,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,IAAO,CAACA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAACA,EAAK,gBAAmB,CAACA,EAAK,WAAc,CAAE,GAC9M,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,WAAc,EAElRA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAAEA,EAAK,UAAa,CAAC,WAAc,CACxP,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC/O,AAAC,CAAA,KACP,iBAuJNC,EAtJM,IA64CNC,EA74CUC,EAAuB,CAE/B,IACC,AAACd,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGY,CAEX,EAEA,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIS,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAanB,OAAO,CAG5B,IAAIC,EAASe,CAAwB,CAACE,EAAS,CAAG,CAGjDlB,QAAS,CAAC,CACX,EAMA,OAHAe,CAAmB,CAACG,EAAS,CAACjB,EAAQA,EAAOD,OAAO,CAAEiB,GAG/ChB,EAAOD,OAAO,AACtB,CAMCiB,EAAoBI,CAAC,CAAG,AAACpB,IACxB,IAAIqB,EAASrB,GAAUA,EAAOsB,UAAU,CACvC,IAAOtB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAgB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACxB,EAAS0B,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC5B,EAAS2B,IAC5EE,OAAOC,cAAc,CAAC9B,EAAS2B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAEzIE,EAA2F3B,EAAoB,KAC/G4B,EAA+G5B,EAAoBI,CAAC,CAACuB,GAazI,GAAM,CAAEE,cAAAA,CAAa,CAAE,CAAIL,IAErB,CAAEM,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIR,IAM7BS,EAAkB,EAAE,CAgC1B,SAASC,IACD,IAAI,CAACC,SAAS,EAEd,IAAI,CAACA,SAAS,CAACC,aAAa,CAAC,KAAM,CAAA,EAE3C,CAQA,SAASC,IACL,IACIC,EAAeC,EAAOC,EADpBC,EAAS,IAAI,CAACA,MAAM,CAAEN,EAAY,IAAI,CAACA,SAAS,CAEtD,GAAIA,EAAW,CACXG,EAAgBG,GAAUA,EAAOC,OAAO,CACxCH,EAAQJ,EAAUI,KAAK,CACvBC,EAAQL,EAAUK,KAAK,CACvB,GAAM,CAAEG,gBAAAA,CAAe,CAAEC,iBAAAA,CAAgB,CAAE,CAAGT,CAE1C,CAAA,IAAI,CAACU,QAAQ,EACbV,EAAUW,IAAI,CAAGX,EAAUY,QAAQ,CAC/B,IAAI,CAACC,UAAU,CAAGL,EACdR,EAAUc,MAAM,CACpB,IAAI,CAACC,OAAO,CAAC,EAAE,CAAGP,EACtBR,EAAUgB,GAAG,CAAG,IAAI,CAACC,OAAO,CAAGR,IAG/BT,EAAUW,IAAI,CAAGd,EAAKO,EAAMO,IAAI,CAAE,IAAI,CAACO,QAAQ,CAAGT,GAClDT,EAAUgB,GAAG,CAAGhB,EAAUmB,gBAAgB,CAACH,GAAG,EAC1C,IAAI,CAACI,WAAW,CACZpB,EAAUc,MAAM,CAChBN,EACC,CAAA,IAAI,CAACa,SAAS,EAAEd,QAAQe,QAAU,CAAA,EACnC,IAAI,CAACP,OAAO,CAAC,EAAE,CACd,CAAA,IAAI,CAACQ,aAAa,EAAI,IAAI,CAACC,iBAAiB,CACzC,IAAI,CAACD,aAAa,CAACE,SAAS,GAC5B,CAAA,EACH,CAAA,AAACtB,GACEA,AAAgC,WAAhCA,EAAcuB,aAAa,EAC3BvB,AAAyB,cAAzBA,EAAcwB,MAAM,EACpBxB,EAAcyB,OAAO,EACrB,CAACzB,EAAc0B,QAAQ,CACvBvB,EAAOwB,YAAY,CACfjC,EAAKM,EAAcmB,MAAM,CAAE,IAC/B,CAAA,EACH,CAAA,IAAI,CAACS,WAAW,CAAG,IAAI,CAACA,WAAW,CAAC,EAAE,CAAG,CAAA,GAElD3B,GAASC,IACL,IAAI,CAACK,QAAQ,CACbN,EAAMG,OAAO,CAACI,IAAI,CAAGN,EAAME,OAAO,CAACI,IAAI,CAAGX,EAAUW,IAAI,CAGxDP,EAAMG,OAAO,CAACS,GAAG,CAAGX,EAAME,OAAO,CAACS,GAAG,CAAGhB,EAAUgB,GAAG,CAEzDZ,EAAM4B,WAAW,GACjB3B,EAAM2B,WAAW,GAEzB,CACJ,CAKA,SAASC,EAAmBC,CAAK,EACzB,CAAC,IAAI,CAAClC,SAAS,EAAI,CAAC,IAAI,CAACmC,QAAQ,EAChC,CAAA,IAAI,CAAC5B,OAAO,CAACP,SAAS,CAAC4B,OAAO,EAC3B,IAAI,CAACrB,OAAO,CAACc,SAAS,CAACO,OAAO,AAAD,IACjC,IAAI,CAACO,QAAQ,CAAG,IAAI,CAACnC,SAAS,CAAG,IAAIvC,EAAqB,IAAI,EAC1DoC,EAAKqC,EAAME,MAAM,CAAE,CAAA,IACnB,IAAI,CAACA,MAAM,CAACF,EAAMG,SAAS,EAGvC,CAKA,SAASC,IACL,IAAM/B,EAAU,IAAI,CAACA,OAAO,CACxBA,CAAAA,EAAQP,SAAS,CAAC4B,OAAO,EACzBrB,EAAQc,SAAS,CAACO,OAAO,AAAD,GACxB,CAAA,IAAI,CAACO,QAAQ,CAAG,IAAI,CAACnC,SAAS,CAAG,IAAIvC,EAAqB,IAAI,CAAA,CAEtE,CAOA,SAAS8E,IACL,IAAMC,EAAe,IAAI,CAACjC,OAAO,CAAEP,EAAYwC,EAAaxC,SAAS,CAAEuB,EAAgBiB,EAAajB,aAAa,CACjH,GAAI,AAAC,CAAA,AAACvB,GAAaA,EAAU4B,OAAO,EAC/BL,GAAiBA,EAAcK,OAAO,GACtC,CAAA,AAAC,CAAClC,GACC,AAAsB,MAAtB,IAAI,CAAC+C,OAAO,CAACC,IAAI,EAChBhD,GAAiB,AAA2B,MAA3B,IAAI,CAAC+C,OAAO,CAACE,SAAS,AAAQ,EACpD,MAAO,CAAA,CAEf,CAIA,SAASC,EAAgBC,CAAK,EAC1B,IAAM7C,EAAY6C,EAAM7C,SAAS,CAEjC,GAAIA,GAAa6C,EAAMzC,KAAK,CAAC,EAAE,CAAE,CAC7B,IAAM0C,EAAWD,EAAMzC,KAAK,CAAC,EAAE,CAAC2C,WAAW,GAC3C/C,EAAUgD,MAAM,CAACF,EAASG,GAAG,CAAEH,EAASI,GAAG,CAC/C,CACJ,CAKA,SAASC,EAAcC,CAAC,EACpB,IAAMjC,EAAoBiC,EAAE7C,OAAO,CAACP,SAAS,EAAI,CAAC,EAAIqD,EAAoBD,EAAE7C,OAAO,CAACc,SAAS,EAAI,CAAC,CAC9F,EAAC,IAAI,CAACrB,SAAS,EAAI,CAAC,IAAI,CAACmC,QAAQ,EAChChB,CAAAA,EAAiBS,OAAO,EAAIyB,EAAiBzB,OAAO,AAAD,IACpDhC,EAAM,CAAA,EAAM,IAAI,CAACW,OAAO,CAACP,SAAS,CAAEmB,GACpCvB,EAAM,CAAA,EAAM,IAAI,CAACW,OAAO,CAACc,SAAS,CAAEgC,GACpC,OAAOD,EAAE7C,OAAO,CAACP,SAAS,CAC1B,OAAOoD,EAAE7C,OAAO,CAACc,SAAS,CAElC,CAS6B,IAAMiC,EAHD,CAC9BC,QAvJJ,SAAiBC,CAAU,CAAEC,CAAc,EACvC,GAAIpE,IAA8EqE,UAAU,CAAC5D,EAAiB0D,GAAa,CACvH,IAAMG,EAAaH,EAAWzE,SAAS,CACvCtB,EAAuBgG,EACvBE,EAAWC,SAAS,CAACC,IAAI,CAACjB,GAC1BjD,EAAS6D,EAAY,iBAAkBzD,GACvCJ,EAAS6D,EAAY,oBAAqBtD,GAC1CP,EAAS6D,EAAY,cAAevB,GACpCtC,EAAS6D,EAAY,eAAgBlB,GACrC3C,EAAS6D,EAAY,sBAAuBjB,GAC5C5C,EAAS6D,EAAY,SAAUL,EACnC,CACJ,CA4IA,EAeM,CAAEzD,cAAeoE,CAAsC,CAAE,CAAIzE,IAE7D,CAAEM,SAAUoE,CAAiC,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAErE,KAAMsE,CAA6B,CAAE,CAAI9E,IAS/H,SAAS+E,IAEAC,AADQ,IAAI,CACPC,aAAa,EACnBD,CAAAA,AAFS,IAAI,CAERC,aAAa,CAAG,IAAIC,EAFhB,IAAI,CAEuC,CAE5D,CAOA,SAASC,EAAkBpB,CAAC,EACxB,IACIqB,EADe5B,EAAQwB,AAAd,IAAI,CAAexB,KAAK,CAAEL,EAAeK,EAAMtC,OAAO,CAAEP,EAAYwC,EAAaxC,SAAS,CAAEsE,EAAgBD,AAA5G,IAAI,CAA6GC,aAAa,CAAE3B,EAAYE,EAAMJ,OAAO,CAACE,SAAS,CAAEpB,EAAgBiB,EAAajB,aAAa,CAAEmD,EAAW7B,EAAMJ,OAAO,CAACC,IAAI,CAE3P,GAAI2B,AAFS,IAAI,CAERM,OAAO,EACX3E,CAAAA,GAAW4B,SAAWL,GAAeK,OAAM,GAE5C,GAAI8C,AAAa,MAAbA,GAAoBtB,AAAc,SAAdA,EAAEwB,OAAO,CAC7BH,EAAS,CAAA,OAMR,GAAI,AAAC,CAAA,AAAe,SAAdrB,EAAEwB,OAAO,EAAeF,AAAa,OAAbA,GAC9BZ,GAA0CnB,AAAc,OAAdA,CAAkB,GAC7D0B,AAdK,IAAI,CAcJ9D,OAAO,CAACsE,KAAK,CAAE,CACpB,IAAMC,EAAeR,EAAcQ,YAAY,CAE3Cb,EAAQb,EAAEH,GAAG,EACbqB,EAAcQ,YAAY,CAAG,CAACT,AAlB7B,IAAI,CAkB8BpB,GAAG,CAAEoB,AAlBvC,IAAI,CAkBwCnB,GAAG,CAAC,CAG5C4B,IACL1B,EAAEH,GAAG,CAAG6B,CAAY,CAAC,EAAE,CACvB1B,EAAEF,GAAG,CAAG4B,CAAY,CAAC,EAAE,CACvBR,EAAcQ,YAAY,CAAG,KAAK,EAE1C,EAEkB,KAAA,IAAXL,GACPrB,EAAE2B,cAAc,EAExB,CAUA,MAAMR,EASF,OAAOhB,QAAQyB,CAAS,CAAE,CACjBA,EAAUC,SAAS,CAACC,QAAQ,CAAC,mBAC9BF,EAAUC,SAAS,CAACpB,IAAI,CAAC,iBACzBE,EAAkCiB,EAAW,OAAQZ,GACrDL,EAAkCiB,EAAW,cAAeR,GAEpE,CAMAW,YAAYd,CAAI,CAAE,CACd,IAAI,CAACA,IAAI,CAAGA,CAChB,CASAe,SAAU,CACN,IAAI,CAACf,IAAI,CAAG,KAAK,CACrB,CAQAgB,aAAaC,CAAK,CAAEC,CAAK,CAAEC,CAAQ,CAAEC,CAAQ,CAAE,CAC3C,IAAMpB,EAAO,IAAI,CAACA,IAAI,CAAEqB,EAAiB,AAACrB,CAAAA,EAAKsB,UAAU,EAAI,CAAA,EAAK,EAC9DC,EAASzB,EAA8BqB,EAAUnB,EAAKwB,SAAS,CAACP,EAAO,CAAA,EAAM,CAACjB,EAAKyB,KAAK,GAAIC,EAAS5B,EAA8BsB,EAAUpB,EAAKwB,SAAS,CAACN,EAAO,CAAA,EAAM,CAAClB,EAAKyB,KAAK,GAWxL,OATK7B,EAAQuB,IACTI,CAAAA,EAAS5B,EAAa4B,EAASF,EAAc,EAE5CzB,EAAQwB,IACTM,CAAAA,EAAS/B,EAAa+B,EAASL,EAAc,EAE5CxB,EAAS0B,IAAY1B,EAAS6B,IAC/BH,CAAAA,EAASG,EAAS,KAAK,CAAA,EAEpB,CACH9C,IAAK2C,EACL1C,IAAK6C,CACT,CACJ,CACJ,CASA,IAAIC,EAA+FnI,EAAoB,KACnHoI,EAAmHpI,EAAoBI,CAAC,CAAC+H,GAEzIE,EAAmIrI,EAAoB,KACvJsI,EAAuJtI,EAAoBI,CAAC,CAACiI,GAajL,GAAM,CAAEE,MAAOC,CAAK,CAAE,CAAIJ,IAEpB,CAAEK,YAAAA,CAAW,CAAE,CAAIH,IAcnBI,EAAoB,CAuDtBzF,OAAQ,GAORQ,OAAQ,GAURkF,WAAY,CAAA,EAOZC,QAAS,CASLC,MAAO,EASPC,aAAc,EASd7F,OAAQ,GAqBR8F,QAAS,CAAC,mBAAoB,mBAAmB,CAMjDhF,QAAS,CAAA,EAUTiF,UAAW,EAMXC,gBAAiB,UAMjBC,YAAa,SACjB,EAgBAC,SAAUX,EAAM,WAA0CY,UAAU,CAAC,IAAKrI,GAAG,GAW7EsI,aAAc,UAadC,aAAc,EAkCdC,OAAQ,CAeJ1E,KAAO,AAAkC,KAAA,IAA3B4D,EAAYe,UAAU,CAChC,OACA,aAIJC,YAAa,IAIbT,UAAW,EAIXU,QAAS,KAITC,aAAc,CACV5F,QAAS,CAAA,CACb,EAcA6F,aAAc,CACVC,cAAe,UACf9F,QAAS,CAAA,EACT+F,gBAAiB,EAEjBC,YAAa,aACbC,OAAQ,SACRC,WAAY,YAEZC,MAAO,CACH,CAAC,cAAe,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAAC,CACzD,CAAC,SAAU,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAG,CAAC,CACjC,CAAC,SAAU,CAAC,EAAG,EAAG,EAAG,GAAI,GAAI,GAAG,CAAC,CACjC,CAAC,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAAC,CAChC,CAAC,MAAO,CAAC,EAAG,EAAG,EAAG,EAAE,CAAC,CACrB,CAAC,OAAQ,CAAC,EAAG,EAAG,EAAE,CAAC,CACnB,CAAC,QAAS,CAAC,EAAG,EAAG,EAAE,CAAC,CACpB,CAAC,OAAQ,KAAK,CACjB,AACL,EAOAC,WAAY,CACRpG,QAAS,CAAA,EACTqG,OAAQ,CACZ,EACAC,GAAI,8BACJC,UAAW,8BAaXC,UAAW,KACXC,OAAQ,CACJzG,QAAS,CAAA,CACb,EAkBA0G,UAAW,IACf,EAqCAlI,MAAO,CAuBH+H,UAAW,6BACXI,WAAY,EACZ1B,UAAW,EACX2B,cAAe,UACfN,GAAI,mBACJO,cAAe,EACfC,kBAAmB,IACnBC,OAAQ,CACJC,MAAO,OAIPC,MAAO,CAEHxC,MAAO,UAEPyC,SAAU,QAEVC,QAAS,GAETC,YAAa,cACjB,EACAC,EAAG,EACHC,EAAG,EACP,EACAC,UAAW,CAAA,CACf,EA0BA9I,MAAO,CACH8H,UAAW,6BACXM,cAAe,EACfW,YAAa,CAAA,EACbC,UAAW,CAAA,EACXC,WAAY,GACZpB,GAAI,mBACJqB,WAAY,GACZZ,OAAQ,CACJ/G,QAAS,CAAA,CACb,EACAuH,UAAW,CAAA,EACXK,MAAO,CACHC,KAAM,KAAK,CACf,EACAlB,WAAY,EACZmB,UAAW,CACf,CACJ,EAsCM,CAAEzF,QAAS0F,CAAe,CAAEzF,SAAU0F,CAAgB,CAAE/J,KAAMgK,CAAY,CAAE,CAAIxK,IA8OnDyK,EAhBnB,CAKZC,KAzDJ,SAAcd,CAAC,CAAEC,CAAC,CAAEc,CAAC,CAAEC,CAAC,CAAE1J,CAAO,SAC7B,AAAIA,GAAS2J,EACFC,AAaf,SAAqBlB,CAAC,CAAEC,CAAC,CAAEc,CAAC,CAAEC,CAAC,CAAE1J,CAAO,EACpC,IAAM2J,EAAI3J,GAAS2J,GAAK,EACxB,MAAO,CACH,CAAC,IAAKjB,EAAIiB,EAAGhB,EAAE,CACf,CAAC,IAAKD,EAAIe,EAAIE,EAAGhB,EAAE,CACnB,CAAC,IAAKgB,EAAGA,EAAG,EAAG,EAAG,EAAGjB,EAAIe,EAAGd,EAAIgB,EAAE,CAClC,CAAC,IAAKjB,EAAIe,EAAGd,EAAIe,EAAIC,EAAE,CACvB,CAAC,IAAKA,EAAGA,EAAG,EAAG,EAAG,EAAGjB,EAAIe,EAAIE,EAAGhB,EAAIe,EAAE,CACtC,CAAC,IAAKhB,EAAIiB,EAAGhB,EAAIe,EAAE,CACnB,CAAC,IAAKC,EAAGA,EAAG,EAAG,EAAG,EAAGjB,EAAGC,EAAIe,EAAIC,EAAE,CAClC,CAAC,IAAKjB,EAAGC,EAAIgB,EAAE,CACf,CAAC,IAAKA,EAAGA,EAAG,EAAG,EAAG,EAAGjB,EAAIiB,EAAGhB,EAAE,CAC9B,CAAC,IAAI,CACR,AACL,EA3B2BD,EAAGC,EAAGc,EAAGC,EAAG1J,GAE5B,CACH,CAAC,IAAK0I,EAAGC,EAAE,CACX,CAAC,IAAKD,EAAIe,EAAGd,EAAE,CACf,CAAC,IAAKD,EAAIe,EAAGd,EAAIe,EAAE,CACnB,CAAC,IAAKhB,EAAGC,EAAIe,EAAE,CACf,CAAC,IAAI,CACR,AACL,CAmDA,EAqBM,CAAEG,eAAAA,CAAc,CAAE,CAAI/K,IA6BOgL,EAHV,CACrB,mBAjBJ,SAAyBC,CAAE,CAAEC,CAAE,CAAE7D,CAAK,CAAE5F,CAAM,CAAEP,EAAU,CAAC,CAAC,EACxD,IAAMiK,EAAYjK,EAAQmG,KAAK,CAAGnG,EAAQmG,KAAK,CAAG,EAAIA,EAA6BwD,EAAIE,EAAe7J,EAAQoG,YAAY,EAAI,EAAG8D,KAAKxH,GAAG,CAACuH,AAAY,EAAZA,EAAe1J,IAEzJ,MAAO,CACH,CAAC,IAAK,KAAiBA,AAF3BA,CAAAA,EAASP,EAAQO,MAAM,EAAIA,CAAK,EAEI,EAAI,IAAI,CACxC,CAAC,IAAK,KAAiBA,EAAS,EAAI,IAAI,CACxC,CAAC,IAAK4J,GAAoB5J,EAAS,EAAI,IAAI,CAC3C,CAAC,IAAK4J,GAAoB5J,EAAS,EAAI,IAAI,IACxCgJ,EAAYC,IAAI,CAAC,CAACS,EAAY,EAAG,GAAKA,AAAY,EAAZA,EAAgB,EAAG1J,EAAQ,CAAEoJ,EAAAA,CAAE,GAC3E,AACL,CAQA,EAIA,IAAIS,GAA2I9M,EAAoB,KAC/J+M,GAA+J/M,EAAoBI,CAAC,CAAC0M,IAazL,GAAM,CAAE1G,QAAS4G,EAAsB,CAAE,CAAIxL,IA2CvC,CAAEyL,WAAAA,EAAU,CAAE,CAAIzL,IAElB,CAAE0L,SAAAA,EAAQ,CAAE,CAAI1L,IAKhB,CAAE2L,gBAAAA,EAAe,CAAE,CAAIJ,KAEvB,CAAEK,cAAeC,EAAkC,CAAE,CA1BpC,CACnBD,cAZJ,SAAuBpG,CAAK,EACxB,IAAMzE,EAAQ,IAAI,CAACA,KAAK,CAAC,EAAE,AACvByK,CAAAA,GAAuBzK,EAAM+K,OAAO,GACpCN,GAAuBzK,EAAMgL,OAAO,GACpCvG,EACA,IAAI,CAACwG,UAAU,CAAGZ,KAAKxH,GAAG,CAAC4B,EAAOzE,EAAM+K,OAAO,CAAG/K,EAAMgL,OAAO,EAG/D,IAAI,CAACC,UAAU,CAAGxG,CAE1B,CAGA,EA0BM,CAAElF,SAAU2L,EAA6B,CAAEC,OAAAA,EAAM,CAAE7H,WAAAA,EAAU,CAAE,CAAIrE,IA2BzE,SAASmM,KACD,IAAI,CAAC3I,KAAK,CAAC7C,SAAS,EAAI,CAAC,IAAI,CAACO,OAAO,CAACkL,UAAU,EAChD,IAAI,CAAC5I,KAAK,CAAC7C,SAAS,CAACC,aAAa,CAAC,KAAM,CAAA,EAEjD,CAS6B,IAAMyL,GAHN,CACzBnI,QAxBJ,SAAsCC,CAAU,CAAEwB,CAAS,CAAE2G,CAAW,EACpEC,AAr5B2DrH,EAq5BlChB,OAAO,CAACyB,GAC7BtB,GAAWqH,GAAU,eACrBvH,EAAWzE,SAAS,CAACkM,aAAa,CAAGC,GACrCK,GAAOP,KAAkBjM,SAAS,CAAC6H,OAAO,CAAEyD,GAC5CiB,GAA8BK,EAAa,cAAeH,IAC1DV,GAAW,CAAE9K,UArZ6CuG,CAqZN,GAE5D,CAiBA,EAeM,CAAEwE,SAAUc,EAAsB,CAAE,CAAIxM,IAExC,CAAEM,SAAUmM,EAAsB,CAAE7H,QAAS8H,EAAqB,CAAElM,KAAMmM,EAAkB,CAAEtI,WAAYuI,EAAwB,CAAE,CAAI5M,KAO9I,AAAC,SAAU3B,CAAa,EAMpB,IAAIwO,EA2BJ,SAASnJ,EAAYsB,CAAI,EACrB,IAAM8H,EAAUH,GAAmB3H,EAAK9D,OAAO,EAAE0C,IAAKoB,EAAKpB,GAAG,EACxDmJ,EAAUJ,GAAmB3H,EAAK9D,OAAO,EAAE2C,IAAKmB,EAAKnB,GAAG,EAC9D,MAAO,CACHiJ,QAAAA,EACAC,QAAAA,EACAC,UAAWN,GAAsB1H,EAAK+G,OAAO,EACzCX,KAAKxH,GAAG,CAACkJ,EAAS9H,EAAKpB,GAAG,CAAEoB,EAAK+G,OAAO,CAAEY,GAAmB3H,EAAKiE,SAAS,CAAEgE,MAAaH,EAC9FI,UAAWR,GAAsB1H,EAAK8G,OAAO,EACzCV,KAAKvH,GAAG,CAACkJ,EAAS/H,EAAKnB,GAAG,CAAEmB,EAAK8G,OAAO,CAAEa,GAAmB3H,EAAKiE,SAAS,CAAE,CAACgE,MAAaF,CACnG,CACJ,CAKA,SAASI,IACL,IAAmBnL,EAAYgD,AAAlB,IAAI,CAAmBhD,SAAS,CAAET,EAAWS,GAAa,CAACA,EAAUd,OAAO,CAACK,QAAQ,CAAE6L,EAAQpI,AAA/F,IAAI,CAAgGyB,KAAK,CAAG,EAAIlF,EAAW,EAAI,EACxIS,IAEAgD,AAHS,IAAI,CAGRxB,KAAK,CAAC6J,iBAAiB,CAAG,CAAC,EAAG,EAAE,CACrCrI,AAJS,IAAI,CAIRxB,KAAK,CAAC8J,UAAU,CAACF,EAAM,EACxBpL,EAAUuL,IAAI,CAAIvL,CAAAA,EAAUd,OAAO,CAACe,MAAM,EAAI,CAAA,EAE1D,CAKA,SAASuL,IACL,IAAMxI,EAAO,IAAI,AACbA,CAAAA,EAAK9D,OAAO,EAAEc,WAAWO,UAEzByC,EAAK9D,OAAO,CAACc,SAAS,CAACyL,QAAQ,CAAG,CAACzI,EAAKyB,KAAK,CAC7CzB,EAAK9D,OAAO,CAAC6I,WAAW,CAAG/E,EAAK9D,OAAO,CAAC8I,SAAS,CAAG,CAAA,EACpDhF,EAAKhD,SAAS,CAAG,IAAI6K,EAAU7H,EAAKxB,KAAK,CAACkK,QAAQ,CAAE1I,EAAK9D,OAAO,CAACc,SAAS,CAAEgD,EAAKxB,KAAK,EACtFiJ,GAAuBzH,EAAKhD,SAAS,CAAE,UAAW,SAAU+B,CAAC,EACzD,IACI4J,EAAIC,EADF,CAAEd,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAEC,UAAWa,CAAS,CAAEX,UAAWY,CAAS,CAAE,CAAGpK,EAAYsB,GAAOQ,EAAQsI,EAAYD,EAGhH,GAAI,AAACnB,GAAsBI,IAAaJ,GAAsBK,IAc9D,GAXI,AAAC/H,EAAKyB,KAAK,EAAI,CAACzB,EAAK+I,QAAQ,EAC5B,CAAC/I,EAAKyB,KAAK,EAAIzB,EAAK+I,QAAQ,EAC7BJ,EAAKE,EAAYrI,EAAQ,IAAI,CAACmI,EAAE,CAChCC,EAAOC,EAAYrI,EAAQ,IAAI,CAACoI,IAAI,GAKpCD,EAAKE,EAAYrI,EAAS,CAAA,EAAI,IAAI,CAACoI,IAAI,AAAD,EACtCA,EAAOC,EAAYrI,EAAS,CAAA,EAAI,IAAI,CAACmI,EAAE,AAAD,GAEtC,IAAI,CAACK,oBAAoB,CAACjK,EAAEkK,OAAO,EAAG,CAEtC,IAAMC,EAAUnK,AAAc,cAAdA,EAAEkK,OAAO,EACrBlK,AAAc,cAAdA,EAAEkK,OAAO,EAA2B,KAAK,EAC7CjJ,EAAKmJ,WAAW,CAACP,EAAMD,EAAI,CAAA,EAAMO,EAASnK,EAC9C,MAII,IAAI,CAACqK,QAAQ,CAAC,IAAI,CAACR,IAAI,CAAE,IAAI,CAACD,EAAE,EAExC,GAER,CAKA,SAASU,IACL,IACIC,EAAcV,EAAMD,EADL,CAAEX,UAAAA,CAAS,CAAEE,UAAAA,CAAS,CAAE,CAAGxJ,EAAjC,IAAI,EAAgD1B,EAAYgD,AAAhE,IAAI,CAAiEhD,SAAS,CAAEuM,EAAUvJ,AAA1F,IAAI,CAA2FwJ,eAAe,CAAIxJ,CAAAA,AAAlH,IAAI,CAAmHtC,WAAW,EAAI,CAAA,EAAK2K,EAAoBrI,AAA/J,IAAI,CAAgKxB,KAAK,CAAC6J,iBAAiB,CAAEoB,EAAazJ,AAA1M,IAAI,CAA2M9D,OAAO,CAACe,MAAM,EAAI,EAE9O,GAAID,GAAaqL,EAAmB,CAChC,GAAIrI,AAHK,IAAI,CAGJyB,KAAK,CAELzB,AALA,IAAI,CAKCzD,QAAQ,EACd8L,CAAAA,CAAiB,CAAC,EAAE,EAAIkB,CAAK,EAEjCvM,EAAU0M,QAAQ,CAAC1J,AARd,IAAI,CAQe1D,IAAI,CAAG0D,AAR1B,IAAI,CAQ2BrD,GAAG,CACnCqD,AATC,IAAI,CASAvD,MAAM,CACX,EACA4L,CAAiB,CAAC,EAAE,CACnBrI,CAAAA,AAZA,IAAI,CAYCzD,QAAQ,CAAGkN,EAAa,CAAA,EAAKzJ,AAZlC,IAAI,CAYmCqC,KAAK,CAAErC,AAZ9C,IAAI,CAY+CvD,MAAM,EAEzDuD,AAdA,IAAI,CAcCzD,QAAQ,EACd8L,CAAAA,CAAiB,CAAC,EAAE,EAAIoB,CAAS,EAErCH,EAAe,MAEd,KAKGK,CAHA3J,CArBC,IAAI,CAqBAzD,QAAQ,EACb8L,CAAAA,CAAiB,CAAC,EAAE,EAAIkB,CAAK,EAO7BI,EAJC3M,EAAUd,OAAO,CAACK,QAAQ,CAIfyD,AA7BX,IAAI,CA6BY1D,IAAI,CACjB0D,AA9BH,IAAI,CA8BIqC,KAAK,CACV,EACAgG,CAAiB,CAAC,EAAE,CACnBrI,CAAAA,AAjCJ,IAAI,CAiCKzD,QAAQ,CAAG,EAAIkN,CAAS,EAPtBzJ,AA1BX,IAAI,CA0BYzD,QAAQ,CAAG,EAAIkN,EASpCzM,EAAU0M,QAAQ,CAACC,EAAW3J,AAnCzB,IAAI,CAmC0BrD,GAAG,CAAEqD,AAnCnC,IAAI,CAmCoCqC,KAAK,CAAErC,AAnC/C,IAAI,CAmCgDvD,MAAM,EAE3DuD,AArCC,IAAI,CAqCAzD,QAAQ,EACb8L,CAAAA,CAAiB,CAAC,EAAE,EAAIoB,CAAS,EAErCH,EAAe,CACnB,CAGA,GAFAjB,CAAiB,CAACiB,EAAa,EAAItM,EAAUuL,IAAI,CAC5CvL,CAAAA,EAAUd,OAAO,CAACe,MAAM,EAAI,CAAA,EAC7B2M,MAAM5B,IACN4B,MAAM1B,IACN,CAACR,GAAsB1H,AA9ClB,IAAI,CA8CmBpB,GAAG,GAC/B,CAAC8I,GAAsB1H,AA/ClB,IAAI,CA+CmBnB,GAAG,GAC/BmB,AAhDK,IAAI,CAgDJ+G,OAAO,GAAK/G,AAhDZ,IAAI,CAgDa8G,OAAO,CAK7B9J,EAAUoM,QAAQ,CAAC,EAAG,QAErB,GAAIpJ,AAvDA,IAAI,CAuDCpB,GAAG,GAAKoB,AAvDb,IAAI,CAuDcnB,GAAG,CAAE,CAI5B,IAAMgL,EAAW7J,AA3DZ,IAAI,CA2DasB,UAAU,CAAItB,CAAAA,AA3D/B,IAAI,CA2DgC8G,OAAO,CAC5C,CAAA,EACJ8B,EAAOiB,EAAW7J,AA7Db,IAAI,CA6DcpB,GAAG,CAC1B+J,EAAKkB,EAAY7J,CAAAA,AA9DZ,IAAI,CA8DanB,GAAG,CAAG,CAAA,EAC5B7B,EAAUoM,QAAQ,CAACR,EAAMD,EAC7B,MAEIC,EAAQ,AAAC5I,CAAAA,AAlEJ,IAAI,CAkEKpB,GAAG,CAAGoJ,CAAQ,EACvBE,CAAAA,EAAYF,CAAQ,EACzBW,EAAM,AAAC3I,CAAAA,AApEF,IAAI,CAoEGnB,GAAG,CAAGmJ,CAAQ,EACrBE,CAAAA,EAAYF,CAAQ,EACrB,AAAChI,AAtEA,IAAI,CAsECyB,KAAK,EAAI,CAACzB,AAtEf,IAAI,CAsEgB+I,QAAQ,EAC5B,CAAC/I,AAvED,IAAI,CAuEEyB,KAAK,EAAIzB,AAvEf,IAAI,CAuEgB+I,QAAQ,CAC7B/L,EAAUoM,QAAQ,CAACR,EAAMD,GAIzB3L,EAAUoM,QAAQ,CAAC,EAAIT,EAAI,EAAIC,EAG3C,CACJ,CA3JAvP,EAAc6F,OAAO,CARrB,SAAiByB,CAAS,CAAEmJ,CAAc,EAClClC,GAAyBJ,GAAwB,oBACjDK,EAAYiC,EACZrC,GAAuB9G,EAAW,iBAAkBwH,GACpDV,GAAuB9G,EAAW,YAAa6H,GAC/Cf,GAAuB9G,EAAW,cAAe0I,GAEzD,CA6JJ,EAAGhQ,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAM0Q,GAAsB1Q,EA+NtB2Q,GAvLT,CAWtBvN,OAAQ,GAORwN,gBAAiB,EAOjBC,mBAAoB,EAMpBC,eAAgB,CAAA,EAsBhBC,WAAY,KAAK,EAQjBnN,OAAQ,KAAK,EAMboN,SAAU,EAEV9N,SAAU,CAAA,EASV+N,KAAM,GAIN1G,OAAQ,EASR2G,mBAAoB,UAOpBC,eAAgB,EAMhBC,eAAgB,UAShBC,iBAAkB,UASlBC,sBAAuB,UASvBC,kBAAmB,UAOnBC,kBAAmB,EAMnBC,WAAY,OASZC,qBAAsB,6BAStBC,iBAAkB,UAOlBC,kBAAmB,EAOnBC,iBAAkB,CACtB,EAoBM,CAAEC,eAAAA,EAAc,CAAE,CAAInQ,IAKtB,CAAEM,SAAU8P,EAAkB,CAAEzL,aAAc0L,EAAsB,CAAEC,MAAAA,EAAK,CAAE1L,QAAS2L,EAAiB,CAAEC,wBAAAA,EAAuB,CAAEC,UAAAA,EAAS,CAAElQ,MAAOmQ,EAAe,CAAElQ,KAAMmQ,EAAc,CAAEC,YAAAA,EAAW,CAAE,CAAI5Q,GAkBlN,OAAM6M,GAMF,OAAO3I,QAAQyB,CAAS,CAAE,CACtBoJ,GAAmB7K,OAAO,CAACyB,EAAWkH,GAC1C,CAkBA,OAAOgE,OAAOC,CAAI,CAAErD,CAAQ,CAAE,CAc1B,OAbIA,GACAqD,EAAKC,OAAO,CAAC,AAACC,IACV,IACIC,EADEC,EAAMF,EAAIG,MAAM,CAEtB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAKE,GAAK,EAEN,UAAhB,MADJH,CAAAA,EAAOD,CAAG,CAACI,EAAI,EAAE,AAAD,IAEZJ,CAAG,CAACI,EAAI,EAAE,CAAGJ,CAAG,CAACI,EAAI,EAAE,CACvBJ,CAAG,CAACI,EAAI,EAAE,CAAGH,EAGzB,GAEGH,CACX,CAMAhL,YAAY4H,CAAQ,CAAExM,CAAO,CAAEsC,CAAK,CAAE,CAMlC,IAAI,CAAC6N,OAAO,CAAG,EAAE,CACjB,IAAI,CAACC,MAAM,CAAG,EACd,IAAI,CAACC,MAAM,CAAG,EACd,IAAI,CAAC3D,IAAI,CAAG,EACZ,IAAI,CAAC4D,gBAAgB,CAAG,EAAE,CAC1B,IAAI,CAACC,aAAa,CAAG,EACrB,IAAI,CAACC,oBAAoB,CAAG,EAC5B,IAAI,CAACC,YAAY,CAAG,EACpB,IAAI,CAACpE,IAAI,CAAG,EACZ,IAAI,CAACI,EAAE,CAAG,EACV,IAAI,CAACuC,gBAAgB,CAAG,EACxB,IAAI,CAACtG,CAAC,CAAG,EACT,IAAI,CAACC,CAAC,CAAG,EACT,IAAI,CAAC+H,IAAI,CAAClE,EAAUxM,EAASsC,EACjC,CAYAqO,WAAY,CACR,IAAMC,EAAe,IAAI,CAAC5Q,OAAO,CAACG,QAAQ,CAAG,CAAC,EAAG,EAAE,CAAG,CAAC,EAAG,EAAE,CAAE0Q,EAAU,IAAI,CAACP,gBAAgB,CAAEQ,EAAM,IAAI,CAACC,cAAc,CAACC,OAAO,CAAEC,EAAQ,IAAI,CAACA,KAAK,CAACD,OAAO,CAAEE,EAAmB,IAAI,CAACA,gBAAgB,CAACC,IAAI,CAAC,IAAI,EAAGC,EAAmB,IAAI,CAACA,gBAAgB,CAACD,IAAI,CAAC,IAAI,EAAGE,EAAiB,IAAI,CAACA,cAAc,CAACF,IAAI,CAAC,IAAI,EAChThB,EAAU,CAEZ,CACIU,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAACI,OAAO,CAChC,QACA,IAAI,CAACM,gBAAgB,CAACH,IAAI,CAAC,IAAI,EAClC,CACD,CACIN,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAACI,OAAO,CAChC,QACA,IAAI,CAACO,gBAAgB,CAACJ,IAAI,CAAC,IAAI,EAClC,CACD,CAACF,EAAO,QAAS,IAAI,CAACO,UAAU,CAACL,IAAI,CAAC,IAAI,EAAE,CAC5C,CAACL,EAAK,YAAaI,EAAiB,CACpC,CAACJ,EAAIW,aAAa,CAAE,YAAaL,EAAiB,CAClD,CAACN,EAAIW,aAAa,CAAE,UAAWJ,EAAe,CAE9C,CAACP,EAAK,aAAcI,EAAiB,CACrC,CAACJ,EAAIW,aAAa,CAAE,YAAaL,EAAiB,CAClD,CAACN,EAAIW,aAAa,CAAE,WAAYJ,EAAe,CAClD,CAEDlB,EAAQN,OAAO,CAAC,SAAU6B,CAAI,EAC1BxC,GAAmByC,KAAK,CAAC,KAAMD,EACnC,GACA,IAAI,CAACvB,OAAO,CAAGA,CACnB,CACAoB,iBAAiB1O,CAAC,CAAE,CAEhB,IAAMyB,EAAS,AAAC1C,CAAAA,AADC,IAAI,CACI6K,EAAE,CAAG7K,AADb,IAAI,CACkB8K,IAAI,AAAD,EACtC+C,GAAe7N,AAFF,IAAI,CAEO5B,OAAO,CAACoO,IAAI,CAAE,IAC1CxM,AAHiB,IAAI,CAGZgQ,cAAc,CAAChQ,AAHP,IAAI,CAGY8K,IAAI,CAAGpI,EAAO1C,AAH9B,IAAI,CAGmC6K,EAAE,CAAGnI,GAC7DiL,GAJiB,IAAI,CAID,UAAW,CAC3B7C,KAAM9K,AALO,IAAI,CAKF8K,IAAI,CACnBD,GAAI7K,AANS,IAAI,CAMJ6K,EAAE,CACfpI,QAAS,YACTwN,SAAUhP,CACd,EACJ,CACAyO,iBAAiBzO,CAAC,CAAE,CAEhB,IAAMyB,EAAQ6K,GAAuBvN,AADpB,IAAI,CACyB6K,EAAE,CAAG7K,AADlC,IAAI,CACuC8K,IAAI,EAC5D+C,GAAe7N,AAFF,IAAI,CAEO5B,OAAO,CAACoO,IAAI,CAAE,IAC1CxM,AAHiB,IAAI,CAGZgQ,cAAc,CAACzC,GAAuBvN,AAH9B,IAAI,CAGmC8K,IAAI,CAAGpI,GAAQ6K,GAAuBvN,AAH7E,IAAI,CAGkF6K,EAAE,CAAGnI,IAC5GiL,GAJiB,IAAI,CAID,UAAW,CAC3B7C,KAAM9K,AALO,IAAI,CAKF8K,IAAI,CACnBD,GAAI7K,AANS,IAAI,CAMJ6K,EAAE,CACfpI,QAAS,YACTwN,SAAUhP,CACd,EACJ,CAaAiP,0BAA0BC,CAAe,CAAE,CACvC,IAAuB/R,EAAU4B,AAAhB,IAAI,CAAqB5B,OAAO,CAAEgS,EAAqBhS,EAAQmO,QAAQ,CAAGvM,AAA1E,IAAI,CAA+EqQ,eAAe,CAC/GjS,EAAQmO,QAAQ,CAChB,EACJ,MAAO,CACHiC,OAAQ,AAAC2B,CAAAA,EAAgB3B,MAAM,CAAGxO,AAJrB,IAAI,CAI0B8G,CAAC,CACxC9G,AALS,IAAI,CAKJsQ,OAAO,AAAD,EACdtQ,CAAAA,AANQ,IAAI,CAMHuQ,QAAQ,CAAGH,CAAiB,EAC1C3B,OAAQ,AAAC0B,CAAAA,EAAgB1B,MAAM,CAAGzO,AAPrB,IAAI,CAO0B+G,CAAC,CACxC/G,AARS,IAAI,CAQJwQ,OAAO,AAAD,EACdxQ,CAAAA,AATQ,IAAI,CASHuQ,QAAQ,CAAGH,CAAiB,CAC9C,CACJ,CAOAnN,SAAU,CACN,IAAMjD,EAAW,IAAI,CAAEnC,EAAYmC,EAASU,KAAK,CAACV,QAAQ,CAE1DA,EAASyQ,YAAY,GAErB,CACI,QACA,kBACA,YACA,iBACA,QACH,CAACxC,OAAO,CAAC,SAAUtR,CAAI,EAChBqD,CAAQ,CAACrD,EAAK,EAAIqD,CAAQ,CAACrD,EAAK,CAACsG,OAAO,EACxCjD,CAAAA,CAAQ,CAACrD,EAAK,CAAGqD,CAAQ,CAACrD,EAAK,CAACsG,OAAO,EAAC,CAEhD,GAEIpF,GAAamC,IAAanC,EAAUqB,SAAS,GAC7CrB,EAAUqB,SAAS,CAAG,KAEtBwO,GAAwB7P,EAAU6Q,gBAAgB,EAE1D,CASAgC,oBAAoBpG,CAAK,CAAE,CACvB,IAAuBM,EAAW5K,AAAjB,IAAI,CAAsB4K,QAAQ,CAAE8D,EAAmB1O,AAAvD,IAAI,CAA4D0O,gBAAgB,CAAEtQ,EAAU4B,AAA5F,IAAI,CAAiG5B,OAAO,CAAEqM,EAAOzK,AAArH,IAAI,CAA0HyK,IAAI,CAAEkG,EAAQ/F,EAASgG,CAAC,GAAGC,GAAG,CAAC7Q,AAA7J,IAAI,CAAkK2Q,KAAK,EAE5L,GADAjC,EAAiBhN,IAAI,CAACiP,GAClBvS,EAAQiO,cAAc,CAAE,CAExB,IAAMzE,EAAOgD,EAAShD,IAAI,GACrBkJ,QAAQ,CAAC,+BACTD,GAAG,CAACF,EAEJ3Q,CARQ,IAAI,CAQHU,KAAK,CAACqQ,UAAU,EAC1BnJ,EAAKoJ,IAAI,CAAC,CACNC,OAAQ7S,EAAQ0O,iBAAiB,CACjC,eAAgB1O,EAAQ2O,iBAAiB,CACzCmE,KAAM9S,EAAQyO,qBAAqB,AACvC,GAGJjF,EAAKoJ,IAAI,CAACpJ,EAAK4F,KAAK,CAAC,CACjB1G,EAAG,IACHC,EAAG,IACHxC,MAAOkG,EACP9L,OAAQ8L,EACR1C,EAAG3J,EAAQgO,kBAAkB,AACjC,EAAGxE,EAAKuJ,WAAW,KAEnB,IAAMC,EAAQxG,EACToD,IAAI,CAACjE,GAAUgE,MAAM,CAAC,CAAC,CACpB,IACAtD,EAAO,EAAKH,CAAAA,EAAQ,GAAK,CAAA,EACzBG,EAAO,EAAI,EACd,CAAE,CACC,IACAA,EAAO,EAAKH,CAAAA,EAAQ,GAAK,CAAA,EACzBG,EAAO,EAAI,EACd,CAAE,CACC,IACAA,EAAO,EAAKH,CAAAA,EAAQ,EAAI,EAAC,EACzBG,EAAO,EACV,CAAC,CAAErM,EAAQuM,QAAQ,GACnBmG,QAAQ,CAAC,8BACTD,GAAG,CAACnC,CAAgB,CAACpE,EAAM,CAC3BtK,CAxCQ,IAAI,CAwCHU,KAAK,CAACqQ,UAAU,EAC1BK,EAAMJ,IAAI,CAAC,CACPE,KAAM9S,EAAQwO,gBAAgB,AAClC,EAER,CACJ,CAQAkC,KAAKlE,CAAQ,CAAExM,CAAO,CAAEsC,CAAK,CAAE,CAE3BV,AADiB,IAAI,CACZ0O,gBAAgB,CAAG,EAAE,CAC9B1O,AAFiB,IAAI,CAEZ4K,QAAQ,CAAGA,EACpB5K,AAHiB,IAAI,CAGZqR,WAAW,CAAGjT,EACvB4B,AAJiB,IAAI,CAIZ5B,OAAO,CAAGwP,GAAgB1B,GAA6BmB,GAAenO,SAAS,CAAEd,GAC1F4B,AALiB,IAAI,CAKZ5B,OAAO,CAACe,MAAM,CAAG0O,GAAe7N,AALxB,IAAI,CAK6B5B,OAAO,CAACe,MAAM,CAAE,IAClEa,AANiB,IAAI,CAMZU,KAAK,CAAGA,EAEjBV,AARiB,IAAI,CAQZyK,IAAI,CAAGoD,GAAe7N,AARd,IAAI,CAQmB5B,OAAO,CAACqM,IAAI,CAAEzK,AARrC,IAAI,CAQ0C5B,OAAO,CAACO,MAAM,EAEzEP,EAAQqB,OAAO,GACfO,AAXa,IAAI,CAWRa,MAAM,GACfb,AAZa,IAAI,CAYR+O,SAAS,GAE1B,CACAO,iBAAiBrO,CAAC,CAAE,CAChB,IAAuBkP,EAAkBnQ,AAAxB,IAAI,CAA6BU,KAAK,CAAC4Q,OAAO,EAAEC,UAAUtQ,IAAMA,EAAGuQ,EAAgBxR,AAAnF,IAAI,CAAwFkQ,yBAAyB,CAACC,EACvInQ,CADiB,IAAI,CACZwO,MAAM,CAAGgD,EAAchD,MAAM,CACtCxO,AAFiB,IAAI,CAEZyO,MAAM,CAAG+C,EAAc/C,MAAM,CACtCzO,AAHiB,IAAI,CAGZyR,aAAa,CAAG,CAACzR,AAHT,IAAI,CAGc8K,IAAI,CAAE9K,AAHxB,IAAI,CAG6B6K,EAAE,CAAC,CACrD7K,AAJiB,IAAI,CAIZ0R,aAAa,CAAG,CAAA,CAC7B,CAKAlC,iBAAiBvO,CAAC,CAAE,CAChB,IAEmC0Q,EAFZxB,EAAkBnQ,AAAxB,IAAI,CAA6BU,KAAK,CAAC4Q,OAAO,EAAEC,UAAUtQ,IAAMA,EAA+B2Q,EAAYxT,AAA9B4B,AAA7E,IAAI,CAAkF5B,OAAO,CAAsBuM,QAAQ,CACxI,SAAW,SAAU8G,EAAgBzR,AADxB,IAAI,CAC6ByR,aAAa,EAAI,EAAE,AAKjEzR,CANa,IAAI,CAMR0R,aAAa,EAErB,CAAA,CAACzQ,EAAE4Q,OAAO,EAAI5Q,AAA4B,IAA5BA,EAAE4Q,OAAO,CAAC,EAAE,CAACD,EAAU,AAAK,IAG3CD,EAASG,AAFO9R,AATH,IAAI,CASQkQ,yBAAyB,CAACC,EAAgB,CAACyB,EAAU,CAC7D5R,AAVJ,IAAI,AAUQ,CAAC4R,EAAU,CAEpC5R,AAZa,IAAI,CAYR+R,UAAU,CAAG,CAAA,EACtB/R,AAba,IAAI,CAaRgQ,cAAc,CAACyB,CAAa,CAAC,EAAE,CAAGE,EAAQF,CAAa,CAAC,EAAE,CAAGE,GAClE3R,AAdS,IAAI,CAcJ+R,UAAU,EACnBpE,GAfS,IAAI,CAeO,UAAW,CAC3B7C,KAAM9K,AAhBD,IAAI,CAgBM8K,IAAI,CACnBD,GAAI7K,AAjBC,IAAI,CAiBI6K,EAAE,CACfpI,QAAS,YACT0I,QAASlK,EAAEV,IAAI,CACf0P,SAAUhP,CACd,GAGZ,CAKAwO,eAAexO,CAAC,CAAE,CAEVjB,AADa,IAAI,CACR+R,UAAU,EACnBpE,GAFa,IAAI,CAEG,UAAW,CAC3B7C,KAAM9K,AAHG,IAAI,CAGE8K,IAAI,CACnBD,GAAI7K,AAJK,IAAI,CAIA6K,EAAE,CACfpI,QAAS,YACT0I,QAASlK,EAAEV,IAAI,CACf0P,SAAUhP,CACd,GAEJjB,AAViB,IAAI,CAUZ0R,aAAa,CAClB1R,AAXa,IAAI,CAWR+R,UAAU,CACf/R,AAZS,IAAI,CAYJwO,MAAM,CACXxO,AAbK,IAAI,CAaAyO,MAAM,CAAG,IAClC,CAgBA7C,SAAS9E,CAAC,CAAEC,CAAC,CAAExC,CAAK,CAAE5F,CAAM,CAAE,CAC1B,GAAmD,CAAE0N,eAAAA,CAAc,CAAElN,OAAAA,EAAS,CAAC,CAAEwL,SAAAA,CAAQ,CAAE,CAA1D3K,AAAhB,IAAI,CAAqB5B,OAAO,CAAsD4T,EAAShS,AAA/F,IAAI,CAAoGiS,QAAQ,CAAG,UAAY,OAC5I3B,EAAU3R,EAAQ6R,EAAU,EAEhCxQ,AAHiB,IAAI,CAGZ2Q,KAAK,CAACuB,IAAI,GACnBlS,AAJiB,IAAI,CAIZ8G,CAAC,CAAGA,EACb9G,AALiB,IAAI,CAKZ+G,CAAC,CAAGA,EAAI,IAAI,CAACqG,gBAAgB,CACtCpN,AANiB,IAAI,CAMZuE,KAAK,CAAGA,EACjBvE,AAPiB,IAAI,CAOZrB,MAAM,CAAGA,EAClBqB,AARiB,IAAI,CAQZsQ,OAAO,CAAGA,EACnBtQ,AATiB,IAAI,CASZwQ,OAAO,CAAGA,EAEf7F,GACA3K,AAZa,IAAI,CAYRuE,KAAK,CAAGvE,AAZJ,IAAI,CAYSwQ,OAAO,CAAGjM,EAAQiM,EAAUxQ,AAZzC,IAAI,CAY8CyK,IAAI,CACnEzK,AAba,IAAI,CAaRsQ,OAAO,CAAGA,EAAU,EAC7BtQ,AAda,IAAI,CAcRwQ,OAAO,CAAGA,EAAUnE,EAAiBrM,AAdjC,IAAI,CAcsCyK,IAAI,CAAG,EAE9DzK,AAhBa,IAAI,CAgBRuQ,QAAQ,CAAG5R,EAAU0N,CAAAA,EAAiB9H,AAAQ,EAARA,EAAY,CAAA,EAC3DvE,AAjBa,IAAI,CAiBR8G,CAAC,CAAGA,GAAQ3H,IAGrBa,AApBa,IAAI,CAoBRrB,MAAM,CAAGA,EAASqB,AApBd,IAAI,CAoBmByK,IAAI,CACxCzK,AArBa,IAAI,CAqBRsQ,OAAO,CAAGA,EAAUjE,EAAiBrM,AArBjC,IAAI,CAqBsCyK,IAAI,CAAG,EAE9DzK,AAvBa,IAAI,CAuBRuQ,QAAQ,CAAGhM,EAAS8H,CAAAA,EAAiB1N,AAAS,EAATA,EAAa,CAAA,EAC3DqB,AAxBa,IAAI,CAwBR+G,CAAC,CAAG/G,AAxBA,IAAI,CAwBK+G,CAAC,CAAG5H,GAG9Ba,AA3BiB,IAAI,CA2BZ2Q,KAAK,CAACqB,EAAO,CAAC,CACnBG,WAAYrL,EACZsL,WAAYpS,AA7BC,IAAI,CA6BI+G,CAAC,AAC1B,GAEA/G,AAhCiB,IAAI,CAgCZqP,KAAK,CAAC2C,EAAO,CAAC,CACnBzN,MAAOA,EACP5F,OAAQA,CACZ,GAEAqB,AArCiB,IAAI,CAqCZ0O,gBAAgB,CAAC,EAAE,CAACsD,EAAO,CAAC,CACjCG,WAAYxH,EAAW,EAAIpG,EAAQ+L,EACnC8B,WAAYzH,EAAWhM,EAAS6R,EAAU,CAC9C,EACJ,CAOAC,cAAe,CACX,IAAI,CAAClC,OAAO,CAACN,OAAO,CAAC,SAAU6B,CAAI,EAC/BhC,GAAYiC,KAAK,CAAC,KAAMD,EAC5B,GACA,IAAI,CAACvB,OAAO,CAACF,MAAM,CAAG,CAC1B,CAOAxN,QAAS,CACL,IAAuB+J,EAAW5K,AAAjB,IAAI,CAAsB4K,QAAQ,CAAExM,EAAU4B,AAA9C,IAAI,CAAmD5B,OAAO,CAAEqM,EAAOzK,AAAvE,IAAI,CAA4EyK,IAAI,CAAEsG,EAAa/Q,AAAnG,IAAI,CAAwGU,KAAK,CAACqQ,UAAU,CAAEJ,EAAQ/F,EAASgG,CAAC,CAAC,aAC7JI,IAAI,CAAC,CACNlL,OAAQ1H,EAAQ0H,MAAM,AAC1B,GACKuM,IAAI,GACJxB,GAAG,EAER7Q,CAPiB,IAAI,CAOZ2Q,KAAK,CAAGA,EAEjB3Q,AATiB,IAAI,CASZqP,KAAK,CAAGzE,EAAShD,IAAI,GACzBkJ,QAAQ,CAAC,8BACTE,IAAI,CAAC,CACNjJ,EAAG3J,EAAQ+O,iBAAiB,EAAI,EAChCxO,OAAQ8L,EACRlG,MAAOkG,CACX,GAAGoG,GAAG,CAACF,GACFI,GACD/Q,AAjBa,IAAI,CAiBRqP,KAAK,CAAC2B,IAAI,CAAC,CAChBE,KAAM9S,EAAQ6O,oBAAoB,CAClCgE,OAAQ7S,EAAQ8O,gBAAgB,CAChC,eAAgB9O,EAAQgP,gBAAgB,AAC5C,GAEJ,IAAMA,EAAmBpN,AAvBR,IAAI,CAuBaoN,gBAAgB,CAC9CpN,AAxBa,IAAI,CAwBRqP,KAAK,CAAC8B,WAAW,GAC9BnR,AAzBiB,IAAI,CAyBZqP,KAAK,CAAC2B,IAAI,CAAC,CAChBlK,EAAG,CAAC0G,GAAM,EAAGJ,GACbrG,EAAG,CAACyG,GAAM,EAAGJ,EACjB,GAEApN,AA9BiB,IAAI,CA8BZmP,cAAc,CAAGvE,EAASgG,CAAC,GAAGC,GAAG,CAACF,GAC3C3Q,AA/BiB,IAAI,CA+BZd,SAAS,CAAG0L,EAAShD,IAAI,GAC7BkJ,QAAQ,CAAC,8BACTE,IAAI,CAAC,CACNrS,OAAQ8L,EAAO2C,EACf7I,MAAOkG,EAAO2C,EACdrF,EAAG3J,EAAQ+N,eAAe,EAAI,CAClC,GAAG0E,GAAG,CAAC7Q,AArCU,IAAI,CAqCLmP,cAAc,EAC9BnP,AAtCiB,IAAI,CAsCZsS,eAAe,CAAG1H,EACtBoD,IAAI,CAACjE,GAAUgE,MAAM,CAAC,CACvB,CAAC,IAAK,GAAItD,EAAO,EAAE,CACnB,CAAC,IAAK,GAAI,EAAIA,EAAO,EAAE,CACvB,CAAC,IAAK,EAAGA,EAAO,EAAE,CAClB,CAAC,IAAK,EAAG,EAAIA,EAAO,EAAE,CACtB,CAAC,IAAK,EAAGA,EAAO,EAAE,CAClB,CAAC,IAAK,EAAG,EAAIA,EAAO,EAAE,CACzB,CAAErM,EAAQuM,QAAQ,GACdmG,QAAQ,CAAC,+BACTD,GAAG,CAAC7Q,AAhDQ,IAAI,CAgDHmP,cAAc,EAC3B4B,IACD/Q,AAlDa,IAAI,CAkDRd,SAAS,CAAC8R,IAAI,CAAC,CACpBE,KAAM9S,EAAQqO,kBAAkB,CAChCwE,OAAQ7S,EAAQuO,cAAc,CAC9B,eAAgBvO,EAAQsO,cAAc,AAC1C,GACA1M,AAvDa,IAAI,CAuDRsS,eAAe,CAACtB,IAAI,CAAC,CAC1BC,OAAQ7S,EAAQ4O,UAAU,CAC1B,eAAgB,CACpB,IAEJhN,AA5DiB,IAAI,CA4DZ4O,oBAAoB,CAAG5O,AA5Df,IAAI,CA4DoBd,SAAS,CAACiS,WAAW,GAC9DnR,AA7DiB,IAAI,CA6DZmP,cAAc,CAACzL,SAAS,CAAC,CAAC8J,GAAM,EAAGxN,AA7D3B,IAAI,CA6DgC4O,oBAAoB,EAAG,CAACpB,GAAM,EAAGxN,AA7DrE,IAAI,CA6D0E4O,oBAAoB,GAEnH5O,AA/DiB,IAAI,CA+DZ0Q,mBAAmB,CAAC,GAC7B1Q,AAhEiB,IAAI,CAgEZ0Q,mBAAmB,CAAC,EACjC,CAWApF,SAASR,CAAI,CAAED,CAAE,CAAE,CACf,IAOI0H,EAAQC,EAPWpU,EAAU4B,AAAhB,IAAI,CAAqB5B,OAAO,CAAEuM,EAAWvM,EAAQuM,QAAQ,CAAE4B,EAAWnO,EAAQmO,QAAQ,CAAEkG,EAAYzS,AAAxG,IAAI,CAA6GuQ,QAAQ,CAAEyB,EAAS,AAAC,CAAA,IAAI,CAACC,QAAQ,EAC9J,IAAI,CAACF,UAAU,EACd,IAAI,CAACrR,KAAK,CAAC7C,SAAS,EAAI,IAAI,CAAC6C,KAAK,CAAC7C,SAAS,CAACkU,UAAU,CAAiB,OAAZ,UAClE,GAAI,CAACtE,GAAkBgF,GACnB,OAEJ,IAAMC,EAAOD,EAAYnK,KAAKxH,GAAG,CAAC+J,EAAI,GAGtC0H,EAASjK,KAAKqK,IAAI,CAACF,EADnB3H,CAAAA,EAAOxC,KAAKvH,GAAG,CAAC+J,EAAM,EAAC,GAEvB9K,AAViB,IAAI,CAUZqQ,eAAe,CAAGmC,EAAUjF,GAAuBmF,EAAOH,GAE/DC,EAAUjG,IACVgG,EAAS,AAACE,CAAAA,EAAYlG,EAAWiG,CAAM,EAAK1H,EAC5C0H,EAAUjG,GAEd,IAAMqG,EAAStK,KAAKuK,KAAK,CAACN,EAASvS,AAhBlB,IAAI,CAgBuBsQ,OAAO,CAAGtQ,AAhBrC,IAAI,CAgB0CwQ,OAAO,EAChEsC,EAAeN,EAAU,EAAI,EAEnCxS,CAnBiB,IAAI,CAmBZ8K,IAAI,CAAGA,EAChB9K,AApBiB,IAAI,CAoBZ6K,EAAE,CAAGA,EACTF,GAcD3K,AAnCa,IAAI,CAmCRmP,cAAc,CAAC6C,EAAO,CAAC,CAC5BI,WAAYQ,CAChB,GACA5S,AAtCa,IAAI,CAsCRd,SAAS,CAAC8S,EAAO,CAAC,CACvBrT,OAAQ6T,CACZ,GACAxS,AAzCa,IAAI,CAyCRsS,eAAe,CAACN,EAAO,CAAC,CAC7BI,WAAYU,CAChB,GACA9S,AA5Ca,IAAI,CA4CR6O,YAAY,CAAG+D,EACxB5S,AA7Ca,IAAI,CA6CR2O,aAAa,CAAG,IAvBzB3O,AAtBa,IAAI,CAsBRmP,cAAc,CAAC6C,EAAO,CAAC,CAC5BG,WAAYS,CAChB,GACA5S,AAzBa,IAAI,CAyBRd,SAAS,CAAC8S,EAAO,CAAC,CACvBzN,MAAOiO,CACX,GACAxS,AA5Ba,IAAI,CA4BRsS,eAAe,CAACN,EAAO,CAAC,CAC7BG,WAAYW,CAChB,GACA9S,AA/Ba,IAAI,CA+BR2O,aAAa,CAAGiE,EACzB5S,AAhCa,IAAI,CAgCR6O,YAAY,CAAG,GAexB2D,GAAW,GACXxS,AAhDa,IAAI,CAgDRsS,eAAe,CAACD,IAAI,GAG7BrS,AAnDa,IAAI,CAmDRsS,eAAe,CAACJ,IAAI,GAGR,CAAA,IAArB9T,EAAQ2U,QAAQ,GACZjI,GAAQ,GAAKD,GAAM,EACnB7K,AAxDS,IAAI,CAwDJ2Q,KAAK,CAAC0B,IAAI,GAGnBrS,AA3DS,IAAI,CA2DJ2Q,KAAK,CAACuB,IAAI,IAG3BlS,AA9DiB,IAAI,CA8DZiS,QAAQ,CAAG,CAAA,CACxB,CAQA/G,qBAAqB8H,CAAS,CAAE,CAC5B,OAAQnF,GAAe,IAAI,CAACzP,OAAO,CAACkO,UAAU,CAAE,AAACpP,IAA+E+V,GAAG,EAC/H,CAAC,AAAC/V,IAA+EK,aAAa,EAC9F,CAAC,IAAI,CAACmD,KAAK,CAACwS,OAAO,GAEnBF,AAAc,YAAdA,GACAA,AAAc,aAAdA,GAEA,CAACvF,GAAkBuF,EAC3B,CACApD,WAAW3O,CAAC,CAAE,CAEV,IAAMkP,EAAkBnQ,AADP,IAAI,CACYU,KAAK,CAAC4Q,OAAO,EAAEC,UAAUtQ,IAAMA,EAAGyB,EAAQ1C,AAD1D,IAAI,CAC+D6K,EAAE,CAAG7K,AADxE,IAAI,CAC6E8K,IAAI,CAAEjM,EAAMmB,AAD7F,IAAI,CACkG+G,CAAC,CAAG/G,AAD1G,IAAI,CAC+G6O,YAAY,CAAErQ,EAAOwB,AADxI,IAAI,CAC6I8G,CAAC,CAAG9G,AADrJ,IAAI,CAC0J2O,aAAa,AACxL,AAAC3O,CAFY,IAAI,CAEP5B,OAAO,CAACuM,QAAQ,EAAIwF,EAAgB1B,MAAM,CAAG5P,GACtD,CAACmB,AAHW,IAAI,CAGN5B,OAAO,CAACuM,QAAQ,EAAIwF,EAAgB3B,MAAM,CAAGhQ,EAExDwB,AALa,IAAI,CAKRgQ,cAAc,CAAChQ,AALX,IAAI,CAKgB8K,IAAI,CAAGpI,EAAO1C,AALlC,IAAI,CAKuC6K,EAAE,CAAGnI,GAI7D1C,AATa,IAAI,CASRgQ,cAAc,CAAChQ,AATX,IAAI,CASgB8K,IAAI,CAAGpI,EAAO1C,AATlC,IAAI,CASuC6K,EAAE,CAAGnI,GAEjEiL,GAXiB,IAAI,CAWD,UAAW,CAC3B7C,KAAM9K,AAZO,IAAI,CAYF8K,IAAI,CACnBD,GAAI7K,AAbS,IAAI,CAaJ6K,EAAE,CACfpI,QAAS,YACTwN,SAAUhP,CACd,EACJ,CAQAkS,OAAO/U,CAAO,CAAE,CACZ,IAAI,CAAC6E,OAAO,GACZ,IAAI,CAAC6L,IAAI,CAAC,IAAI,CAACpO,KAAK,CAACkK,QAAQ,CAAEgD,GAAgB,CAAA,EAAM,IAAI,CAACxP,OAAO,CAAEA,GAAU,IAAI,CAACsC,KAAK,CAC3F,CASAsP,eAAelF,CAAI,CAAED,CAAE,CAAE,CACjBA,EAAK,IACLC,EAAOyC,GAAuB,EAAIA,GAAuB1C,EAAKC,IAC9DD,EAAK,GAELC,EAAO,IACPD,EAAK0C,GAAuB1C,EAAKC,GACjCA,EAAO,GAEX,IAAI,CAACA,IAAI,CAAGA,EACZ,IAAI,CAACD,EAAE,CAAGA,CACd,CACJ,CAMAd,GAAUsD,cAAc,CAAGnB,GAM3BmB,GAAenO,SAAS,CAAG0O,GAAgB,CAAA,EAAM7D,GAAUsD,cAAc,CAAEA,GAAenO,SAAS,EASnG,IAAIkU,GAAuH1X,EAAoB,KAC3I2X,GAA2I3X,EAAoBI,CAAC,CAACsX,IAerK,GAAM,CAAE/F,eAAgBiG,EAAwB,CAAE,CAAIpW,IAEhD,CAAEK,cAAegW,EAAuB,CAAE,CAAIrW,IAK9C,CAAEN,UAAW,CAAE6H,QAAAA,EAAO,CAAE,CAAE,CAAI4O,KAE9B,CAAE7V,SAAUgW,EAAkB,CAAEC,MAAAA,EAAK,CAAE5R,aAAc6R,EAAsB,CAAE5R,QAAS6R,EAAiB,CAAEjG,wBAAyBkG,EAAiC,CAAEC,MAAAA,EAAK,CAAEzK,OAAQ0K,EAAgB,CAAEC,KAAAA,EAAI,CAAEpG,UAAWqG,EAAmB,CAAEC,QAAAA,EAAO,CAAElS,SAAUmS,EAAkB,CAAEzW,MAAO0W,EAAe,CAAEzW,KAAM0W,EAAc,CAAEtG,YAAauG,EAAqB,CAAEC,MAAAA,EAAK,CAAE,CAAIpX,IAYtX,SAASqX,GAAOC,CAAO,CAAE,GAAG1E,CAAI,EAC5B,IAAM2E,EAAU,EAAE,CAACC,MAAM,CAAC5X,IAAI,CAACgT,EAAMoE,IACrC,GAAIO,EAAQpG,MAAM,CACd,OAAO/F,IAAI,CAACkM,EAAQ,CAACzE,KAAK,CAAC,EAAG0E,EAEtC,CAgBA,MAAME,GAMF,OAAOvT,QAAQC,CAAU,CAAEwB,CAAS,CAAE2G,CAAW,CAAE,CAC/CrI,EAAoCC,OAAO,CAACC,EAAYsT,IACxDpL,GAA+BnI,OAAO,CAACC,EAAYwB,EAAW2G,EAClE,CAMAxG,YAAYtC,CAAK,CAAE,CACf,IAAI,CAACkU,OAAO,CAAG,CAAA,EACf,IAAI,CAACvW,eAAe,CAAG,EACvB,IAAI,CAACyQ,IAAI,CAACpO,EACd,CAwBAmU,WAAW/N,CAAC,CAAEwD,CAAK,CAAE/L,CAAQ,CAAEuW,CAAI,CAAE,CACjC,IAAwBnW,EAASd,AAAf,IAAI,CAAqBmB,gBAAgB,CAACsF,OAAO,CAAC3F,MAAM,CAE1Ed,AAFkB,IAAI,CAEZyG,OAAO,CAACgG,EAAM,CAACwK,EAAK,CAACvW,EAAW,CACtC4T,WAAY7J,KAAKyM,KAAK,CAAClX,AAHT,IAAI,CAGeW,IAAI,CAAGX,AAH1B,IAAI,CAGgCc,MAAM,CAAG,GAC3DyT,WAAY9J,KAAKyM,KAAK,CAAClX,AAJT,IAAI,CAIegB,GAAG,CAAGmW,SAASlO,EAAG,IAAM,GAAMnI,EACnE,EAAI,CACAwT,WAAY7J,KAAKyM,KAAK,CAAClX,AANT,IAAI,CAMeW,IAAI,CAAGwW,SAASlO,EAAG,KACpDsL,WAAY9J,KAAKyM,KAAK,CAAClX,AAPT,IAAI,CAOegB,GAAG,CAAGhB,AAPzB,IAAI,CAO+Bc,MAAM,CAAG,EAAIA,EAAS,EAAI,EAC/E,EACJ,CAmBAsW,YAAYC,CAAS,CAAEC,CAAS,CAAE5W,CAAQ,CAAEuW,CAAI,CAAE,CAC9C,IAAwBzQ,EAAaxG,AAAnB,IAAI,CAAyBmB,gBAAgB,CAACqF,UAAU,CAAEW,EAAenH,AAAzE,IAAI,CAA+EuX,OAAO,CAACjE,WAAW,GAAIkE,EAAcrQ,EAAe,EAAGsQ,EAAoB,AAACtQ,EAAe,EAAK,EACrM1G,EAAmBT,AADD,IAAI,CACOS,gBAAgB,CAAEiX,EAAgB1X,AAD7C,IAAI,CACmD4M,IAAI,CAAE+K,EAAe3X,AAD5E,IAAI,CACkFgB,GAAG,CAAEF,EAASd,AADpG,IAAI,CAC0Gc,MAAM,CAAE8W,EAAUD,EAAeH,EAAaK,EAAUF,EAAe7W,EACnMH,EAAOX,AAFO,IAAI,CAEDW,IAAI,CAAEmX,EAAa3H,EACpCzP,GACAoX,EAAcH,EAAeL,EAAYG,EACzCH,EAAYK,EAAeN,EAAYI,EACvCtH,EAAO,CACH,CACI,IACAxP,EAAOG,EACP6W,EAAelX,EAAmBgX,EACrC,CAED,CAAC,IAAK9W,EAAOG,EAAQgX,EAAY,CACjC,CAAC,IAAKnX,EAAMmX,EAAY,CACxB,CAAC,IAAKnX,EAAM2W,EAAU,CACtB,CAAC,IAAK3W,EAAOG,EAAQwW,EAAU,CAC/B,CACI,IACA3W,EAAOG,EACP6W,EAAeD,EAAgBjX,EAClC,CACJ,CACG+F,GACA2J,EAAKtM,IAAI,CAET,CAAC,IAAKlD,EAAOG,EAAQgX,EAAcN,EAAY,CAE/C,CACI,IACA7W,EAAOG,EACPwW,EAAYE,EACf,IAIL7W,GAAQF,EACR4W,GAAa1W,EAAOF,EAAmBgX,EACvCH,GAAa3W,EAAOF,EAAmBgX,EACvCtH,EAAO,CAEH,CAAC,IAAKxP,EAAMiX,EAAQ,CAEpB,CAAC,IAAKP,EAAWO,EAAQ,CAEzB,CAAC,IAAKP,EAAWQ,EAAQ,CAEzB,CAAC,IAAKP,EAAWO,EAAQ,CAEzB,CAAC,IAAKP,EAAWM,EAAQ,CAEzB,CACI,IACAjX,EAAO+W,EAAgBjX,AAAmB,EAAnBA,EACvBmX,EACH,CACJ,CACGpR,GACA2J,EAAKtM,IAAI,CAET,CAAC,IAAKwT,EAAYG,EAAaI,EAAQ,CAEvC,CAAC,IAAKN,EAAYE,EAAaI,EAAQ,GAG/C5X,AAjEkB,IAAI,CAiEZuX,OAAO,CAACN,EAAK,CAAC,CACpB7Y,EAAG+R,CACP,EACJ,CAmBA4H,UAAUV,CAAS,CAAEC,CAAS,CAAE5W,CAAQ,CAAEuW,CAAI,CAAE,CAC5C,IACInW,EAAQ4F,EAAOuC,EAAGC,EADEvI,EAAOX,AAAb,IAAI,CAAmBW,IAAI,CAAEK,EAAMhB,AAAnC,IAAI,CAAyCgB,GAAG,CAAEgX,EAAkBhY,AAApE,IAAI,CAA0Ec,MAAM,CAIlGJ,GACAuI,EAAI,CAACtI,EAAMA,EAAMA,EAAK,CACtBuI,EAAI,CAAClI,EAAKA,EAAMqW,EAAWrW,EAAMsW,EAAU,CAC3C5Q,EAAQ,CAACsR,EAAiBA,EAAiBA,EAAgB,CAC3DlX,EAAS,CACLuW,EACAC,EAAYD,EACZrX,AAXU,IAAI,CAWJ4M,IAAI,CAAG0K,EACpB,GAGDrO,EAAI,CAACtI,EAAMA,EAAO0W,EAAW1W,EAAO2W,EAAU,CAC9CpO,EAAI,CAAClI,EAAKA,EAAKA,EAAI,CACnB0F,EAAQ,CACJ2Q,EACAC,EAAYD,EACZrX,AApBU,IAAI,CAoBJ4M,IAAI,CAAG0K,EACpB,CACDxW,EAAS,CAACkX,EAAiBA,EAAiBA,EAAgB,EAEhEhY,AAxBkB,IAAI,CAwBZiY,MAAM,CAAC7H,OAAO,CAAC,CAAC8H,EAAOzH,KAC7ByH,CAAK,CAACjB,EAAK,CAAC,CACRhO,EAAGA,CAAC,CAACwH,EAAE,CACPvH,EAAGA,CAAC,CAACuH,EAAE,CACP/J,MAAOA,CAAK,CAAC+J,EAAE,CACf3P,OAAQA,CAAM,CAAC2P,EAAE,AACrB,EACJ,EACJ,CAeA0H,gBAAiB,CACb,IAAMnY,EAAY,IAAI,CAAEmB,EAAmBnB,EAAUmB,gBAAgB,CAAEqF,EAAarF,EAAiBqF,UAAU,CAAE3D,EAAQ7C,EAAU6C,KAAK,CAAEnC,EAAWmC,EAAMnC,QAAQ,CAAEqM,EAAWlK,EAAMkK,QAAQ,CAAEqL,EAAc,CAC1MC,OAAQ3X,EAAW,YAAc,WACrC,EAEA4X,EAAiBtY,EAAUsY,cAAc,EACpCtY,CAAAA,EAAUsY,cAAc,CAAGvL,EACvBgG,CAAC,CAAC,aACFI,IAAI,CAAC,CACNlL,OAAQ,EACRsQ,WAAY,QAChB,GACKvF,GAAG,EAAC,EAkCb,GAhCA,CACI,CAACxM,EACDA,EACA,CAACA,EACJ,CAAC4J,OAAO,CAAC,CAACoI,EAAS/L,KAChB,IAAMyL,EAAQlY,EAAUiY,MAAM,CAACxL,EAAM,EAChCzM,CAAAA,EAAUiY,MAAM,CAACxL,EAAM,CAAGM,EAAShD,IAAI,GACnCkJ,QAAQ,CAAC,4BACTxG,CAAAA,AAAU,IAAVA,EAAc,UAAY,UAAS,GACnCuG,GAAG,CAACsF,EAAc,CACtBzV,CAAAA,EAAMqQ,UAAU,GACjBgF,EAAM/E,IAAI,CAAC,CACPE,KAAMmF,EAAUrX,EAAiB6F,QAAQ,CAAG,eAChD,GACc,IAAVyF,GACAyL,EAAMO,GAAG,CAACL,GAGtB,GAEKpY,EAAUuX,OAAO,EAClBvX,CAAAA,EAAUuX,OAAO,CAAGxK,EAASoD,IAAI,GAC5B8C,QAAQ,CAAC,gCACTD,GAAG,CAACsF,EAAc,EAEtBzV,EAAMqQ,UAAU,EACjBlT,EAAUuX,OAAO,CAACpE,IAAI,CAAC,CACnB,eAAgBhS,EAAiBgG,YAAY,CAC7CiM,OAAQjS,EAAiB+F,YAAY,AACzC,GAGA/F,EAAiBsF,OAAO,EAAE7E,QAAS,CACnC,IAAM8W,EAAiBvX,EAAiBsF,OAAO,CAAE,CAAE3F,OAAAA,CAAM,CAAE4F,MAAAA,CAAK,CAAE,CAAGgS,EACrE,CAAC,EAAG,EAAE,CAACtI,OAAO,CAAC,AAAC3D,IACZ,IAAMkM,EAAaD,EAAe9R,OAAO,CAAC6F,EAAM,CAChD,GAAI,AAACzM,EAAUyG,OAAO,CAACgG,EAAM,EACzBzM,EAAUyG,OAAO,CAACgG,EAAM,CAACmM,SAAS,GAAKD,EAatC,CAAA,GAAI,CAAC3Y,EAAUyG,OAAO,CAACgG,EAAM,CAACoM,KAAK,EACpC7Y,EAAUyG,OAAO,CAACgG,EAAM,CAACkM,UAAU,GAAKA,EAAY,CACpD,IAAsCxI,EAAO2I,AAA5BlS,EAAO,CAAC+R,EAAW,CAAkB1Z,IAAI,CAAC2H,GAAS,CAACF,EAAQ,EAAI,EAAG,EAAGA,EAAO5F,GAC9Fd,EAAUyG,OAAO,CAACgG,EAAM,CAAC0G,IAAI,CAAC,CAC1B/U,EAAG+R,CACP,GACAnQ,EAAUyG,OAAO,CAACgG,EAAM,CAACkM,UAAU,CAAGA,CAC1C,CAAA,MAlBI3Y,EAAUyG,OAAO,CAACgG,EAAM,EAAErH,UAC1BpF,EAAUyG,OAAO,CAACgG,EAAM,CAAGM,EAASgM,MAAM,CAACJ,EAAY,CAACjS,EAAQ,EAAI,EAAG,EAAGA,EAAO5F,EAAQ4X,GAGzF1Y,EAAUyG,OAAO,CAACgG,EAAM,CAAC0G,IAAI,CAAC,CAAElL,OAAQ,EAAIwE,CAAM,GAC7CwG,QAAQ,CAAC,2DAEV,CAAC,OAAQ,QAAQ,CAACxG,EAAM,EAAEuG,GAAG,CAACsF,GAClCtY,EAAUgZ,cAAc,EAWxBnW,CAAAA,EAAMnC,QAAQ,EACdV,EAAUyG,OAAO,CAACgG,EAAM,CAAC0G,IAAI,CAAC,CAC1B8F,SAAU,GACVC,gBAAiBzO,KAAKuK,KAAK,CAAC,CAACtO,EAAQ,GACrCyS,gBAAiB,AAACrY,CAAAA,EAAS4F,CAAI,EAAK,CACxC,GAEC7D,EAAMqQ,UAAU,EACjBlT,EAAUyG,OAAO,CAACgG,EAAM,CACnB0G,IAAI,CAAC,CACNE,KAAMqF,EAAe5R,eAAe,CACpCsM,OAAQsF,EAAe3R,WAAW,CAClC,eAAgB2R,EAAe7R,SAAS,CACxCH,MAAOgS,EAAehS,KAAK,CAC3B5F,OAAQ4X,EAAe5X,MAAM,CAC7BmI,EAAG,CAACvC,EAAQ,EAAI,EAChBwC,EAAG,CACP,GACKuP,GAAG,CAACL,EAEjB,EACJ,CACJ,CAUA9C,OAAO/U,CAAO,CAAE6B,EAAS,CAAA,CAAK,CAAE,CAC5B,IAAMS,EAAQ,IAAI,CAACA,KAAK,CAAEuW,EAAiBvW,EAAMtC,OAAO,CAACsC,KAAK,CAACnC,QAAQ,GACnEmC,EAAMxB,SAAS,EAAEd,QAAQuM,SAK7B,GAJAwJ,GAAgB,CAAA,EAAMzT,EAAMtC,OAAO,CAACP,SAAS,CAAEO,GAC/C,IAAI,CAACY,gBAAgB,CAAG0B,EAAMtC,OAAO,CAACP,SAAS,EAAI,CAAC,EACpD,IAAI,CAACqZ,WAAW,GAEZvD,GAAkBvV,EAAQqB,OAAO,GAAKwX,EAGtC,OAFA,IAAI,CAAChU,OAAO,GACZ,IAAI,CAACkU,gBAAgB,CAAG/Y,EAAQqB,OAAO,EAAI,IAAI,CAAC0X,gBAAgB,CACzD,IAAI,CAACrI,IAAI,CAACpO,GAErB,GAAI,IAAI,CAACyW,gBAAgB,GACrB,IAAI,CAACvC,OAAO,CAAG,CAAA,EACoB,CAAA,IAA/BxW,EAAQgZ,kBAAkB,EAC1B,IAAI,CAACC,UAAU,CAACpJ,OAAO,CAAC,AAAChJ,IACrBoP,GAAsBpP,EAAQ,cAAe,IAAI,CAACqS,kBAAkB,CACxE,EAAG,IAAI,EAEPlZ,EAAQgZ,kBAAkB,EAC1B,IAAI,CAACC,UAAU,CAACpJ,OAAO,CAAC,AAAChJ,IACrBA,EAAOsS,cAAc,CAAC7V,IAAI,CAAC8R,GAAmBvO,EAAQ,cAAe,IAAI,CAACqS,kBAAkB,EAChG,EAAG,IAAI,EAGPlZ,CAAAA,EAAQ6G,MAAM,EAAI7G,EAAQiZ,UAAU,AAAD,GACnC,IAAI,CAACvZ,aAAa,CAAC,KAAK,EAAG,CAAA,GAG3BM,EAAQO,MAAM,EAAIP,EAAQH,KAAK,EAAIG,EAAQF,KAAK,EAAE,CAClD,IAAI,CAACS,MAAM,CAAGP,EAAQO,MAAM,EAAI,IAAI,CAACA,MAAM,CAC3C,IAAM6Y,EAAU,IAAI,CAACC,eAAe,GACpC,IAAI,CAACxZ,KAAK,CAACkV,MAAM,CAAC,CACd,GAAG/U,EAAQH,KAAK,CAChBuZ,QAAAA,EACA,CAAC9W,EAAMnC,QAAQ,CAAG,QAAU,SAAS,CAAE,IAAI,CAACI,MAAM,CAClD,CAAC+B,EAAMnC,QAAQ,CAAG,SAAW,QAAQ,CAAE,KAAK,CAChD,EAAG,CAAA,GACH,IAAI,CAACL,KAAK,CAACiV,MAAM,CAAC,CACd,GAAG/U,EAAQF,KAAK,CAChB,CAACwC,EAAMnC,QAAQ,CAAG,QAAU,SAAS,CAAE,IAAI,CAACI,MAAM,AACtD,EAAG,CAAA,EACP,CAEAsB,GACAS,EAAMT,MAAM,EAEpB,CAeAY,OAAOC,CAAG,CAAEC,CAAG,CAAEoC,CAAK,CAAEC,CAAK,CAAE,CAC3B,IAAwB1C,EAAQ7C,AAAd,IAAI,CAAoB6C,KAAK,CAAEzC,EAAQJ,AAAvC,IAAI,CAA6CI,KAAK,CAAEuF,EAAavF,EAAMuF,UAAU,EAAI,EAAGkU,EAAiBzZ,EAAMkE,aAAa,CAACwV,IAAI,CAAGjX,EAAMzC,KAAK,CAAC,EAAE,CAAGA,EAAOkZ,EAAmBtZ,AAAnL,IAAI,CAAyLsZ,gBAAgB,CAAElF,EAAWpU,AAA1N,IAAI,CAAgOoU,QAAQ,CAAE1T,EAAWmC,EAAMnC,QAAQ,CAAEqZ,EAAWlX,EAAMzC,KAAK,CAAC,EAAE,CAAC2Z,QAAQ,CAAEC,EAAWnX,EAAMzC,KAAK,CAAC,EAAE,CAACG,OAAO,CAACyZ,QAAQ,CAAEvZ,EAAmBT,AAA5W,IAAI,CAAkXS,gBAAgB,CACpZwZ,EAAgBnJ,EAAeE,EAAcxQ,EAAkBR,AADjD,IAAI,CACuDQ,eAAe,CAAEkX,EAAeT,EAE7G,GAAI,IAAI,CAAC/C,UAAU,EAAI,CAAC4B,GAAkBxQ,GACtC,OASJ,GAPI,IAAI,CAACyR,OAAO,EAEZ,IAAI,CAACoB,cAAc,GAEvBlV,EAAM4S,GAAuB5S,EAAM0C,EAAa,GAChDzC,EAAM2S,GAAuB3S,EAAMyC,EAAa,GAE5C,CAAC0Q,GAAmBpT,IAAQ,CAACoT,GAAmBnT,GAAM,CAGtD,IAAIkR,EAKA,OAJA9O,EAAQ,EACRC,EAAQgR,GAAenW,EAAMsG,KAAK,CAAEmT,EAAenT,KAAK,CAKhE,CACA1G,AAxBkB,IAAI,CAwBZW,IAAI,CAAG4V,GAAenW,EAAMO,IAAI,CAE1CkC,EAAM3B,QAAQ,CAAGT,EACZC,CAAAA,EAAWmC,EAAMqX,SAAS,CAAG,CAAA,GAClC,IAAI5C,EAAYtX,AA5BE,IAAI,CA4BI4M,IAAI,CAAG8K,EAAgBnB,GAAenW,EAAMmQ,GAAG,CAAE,AAAC7P,CAAAA,EAAWmC,EAAMsX,UAAU,CAAGtX,EAAMqX,SAAS,AAAD,EACpH,EAAIzZ,GAEJwZ,EADAvZ,EACiBF,EAGAkX,EAAgB,EAAIjX,EAGzC6E,EAAQiR,GAAejR,EAAOlF,EAAMga,QAAQ,CAACnX,EAAK,CAAA,IAClDsC,EAAQgR,GAAehR,EAAOnF,EAAMga,QAAQ,CAAClX,EAAK,CAAA,IAE7CmT,GAAmB/Q,IAAUmF,KAAK4P,GAAG,CAAC/U,KAAWgH,MAClDhH,EAAQ,EACRC,EAAQ0U,GAGZ,IAAMrU,EAASxF,EAAMka,OAAO,CAAChV,EAAO,CAAA,GAAOS,EAAS3F,EAAMka,OAAO,CAAC/U,EAAO,CAAA,GAAOgV,EAAe9P,KAAK4P,GAAG,CAACxE,GAAuB9P,EAASH,GACpI2U,CAAAA,EAAeR,EACX,IAAI,CAACS,WAAW,CAChBlV,EAAQlF,EAAMga,QAAQ,CAACrU,EAASgU,EAAWpU,EAAY,CAAA,GAElD,IAAI,CAAC8U,YAAY,EACtBlV,CAAAA,EAAQnF,EAAMga,QAAQ,CAACxU,EAASmU,EAAWpU,EAAY,CAAA,EAAI,EAG1DmQ,GAAkBkE,IACvBnE,GAAuB0E,EAAe5U,GAAcqU,IAChD,IAAI,CAACQ,WAAW,CAChBlV,EAAQlF,EAAMga,QAAQ,CAACrU,EAASiU,EAAWrU,EAAY,CAAA,GAElD,IAAI,CAAC8U,YAAY,EACtBlV,CAAAA,EAAQnF,EAAMga,QAAQ,CAACxU,EAASoU,EAAWrU,EAAY,CAAA,EAAI,GAInE3F,AAhEkB,IAAI,CAgEZsX,SAAS,CAAG1B,GAAMnL,KAAKvH,GAAG,CAACoC,EAAOC,GAAQ,EAAG+R,GACvDtX,AAjEkB,IAAI,CAiEZqX,SAAS,CAAGzB,GAAM5V,AAjEV,IAAI,CAiEgB0a,UAAU,CAC5C1a,AAlEc,IAAI,CAkERsX,SAAS,CAAGtX,AAlER,IAAI,CAkEc0a,UAAU,CAC1CjQ,KAAKxH,GAAG,CAACqC,EAAOC,GAAQ,EAAG+R,GAC/BtX,AApEkB,IAAI,CAoEZ6E,KAAK,CAAG7E,AApEA,IAAI,CAoEMsX,SAAS,CAAGtX,AApEtB,IAAI,CAoE4BqX,SAAS,CAC3DC,EAAY7M,KAAKyM,KAAK,CAAClX,AArEL,IAAI,CAqEWsX,SAAS,EAC1C,IAAMD,EAAY5M,KAAKyM,KAAK,CAAClX,AAtEX,IAAI,CAsEiBqX,SAAS,EAC5CiC,IACAtZ,AAxEc,IAAI,CAwERsY,cAAc,CAACnF,IAAI,CAAC,CAC1BoF,WAAY,SAChB,GAEAtB,EAAO7C,GAAY,CAACpU,AA5EN,IAAI,CA4EYkU,UAAU,CAAG,UAAY,OACvDlU,AA7Ec,IAAI,CA6ER+X,SAAS,CAACV,EAAWC,EAAW5W,EAAUuW,GACpDjX,AA9Ec,IAAI,CA8ERoX,WAAW,CAACC,EAAWC,EAAW5W,EAAUuW,GAClDjX,AA/EU,IAAI,CA+EJmB,gBAAgB,CAACsF,OAAO,CAAC7E,OAAO,GAC1C5B,AAhFU,IAAI,CAgFJgX,UAAU,CAACK,EAAW,EAAG3W,EAAUuW,GAC7CjX,AAjFU,IAAI,CAiFJgX,UAAU,CAACM,EAAW,EAAG5W,EAAUuW,KAGjDjX,AApFc,IAAI,CAoFRqB,SAAS,GACfX,GACAsQ,EAAehR,AAtFL,IAAI,CAsFWgB,GAAG,CAAGP,EAC/BqQ,EAAgB9Q,AAvFN,IAAI,CAuFYW,IAAI,CAAGH,EAC5B8Y,CAAAA,GAAoB,CAACO,EAAejZ,QAAQ,CAAG,EAE5C,AAACiZ,CAAAA,EAAe9X,WAAW,EAAI,CAAA,EAE3B8X,EAAehM,eAAe,AAAD,EACzCrN,EAAkBkX,EAAgB,EAAIjX,IAGtCuQ,EAAehR,AAhGL,IAAI,CAgGWgB,GAAG,CAAIsY,CAAAA,EAC5BtZ,AAjGM,IAAI,CAiGAc,MAAM,CAChB,CAACN,CAAc,EACnBsQ,EAAgB9Q,AAnGN,IAAI,CAmGYW,IAAI,CAAGF,GAGrCT,AAtGc,IAAI,CAsGRqB,SAAS,CAAC0M,QAAQ,CAAC+C,EAAeE,EAAciJ,EAAgBzZ,GAE1ER,AAxGc,IAAI,CAwGRqB,SAAS,CAACoM,QAAQ,CAG5BzN,AA3Gc,IAAI,CA2GRqX,SAAS,CAAIK,CAAAA,GAAiB,CAAA,EAAI1X,AA3G9B,IAAI,CA2GoCsX,SAAS,CAAII,CAAAA,GAAiB,CAAA,IAExF1X,AA7GkB,IAAI,CA6GZoU,QAAQ,CAAG,CAAA,EACrB,IAAI,CAAC2C,OAAO,CAAG,CAAA,EACfZ,GAAoB,IAAI,CAAE,cAC9B,CAOA6C,gBAAiB,CACb,IAAMhZ,EAAY,IAAI,CAAE6C,EAAQ7C,EAAU6C,KAAK,CAAE8X,EAAY9X,EAAM8X,SAAS,CACxEjB,EAAiB,EAAE,CAAE/H,EAAkBC,CAK3C5R,CAAAA,EAAU2R,gBAAgB,CAAGA,EAAmB,SAAUvO,CAAC,EACvDpD,EAAU4a,WAAW,CAACxX,EAC1B,EACApD,EAAU4R,cAAc,CAAGA,EAAiB,SAAUxO,CAAC,EACnDpD,EAAU6a,SAAS,CAACzX,EACxB,EAGAsW,AADAA,CAAAA,EAAiB1Z,EAAU8a,cAAc,CAAC,YAAW,EACtCjX,IAAI,CAInB8R,GAAmB9S,EAAMkY,QAAQ,CAAE,YAAapJ,GAAmBgE,GAAmBgF,EAAU3I,aAAa,CAAE,UAAWJ,GAE1H+D,GAAmB9S,EAAMkY,QAAQ,CAAE,YAAapJ,GAAmBgE,GAAmBgF,EAAU3I,aAAa,CAAE,WAAYJ,IAC3H8H,EAAesB,MAAM,CAAChb,EAAU8a,cAAc,CAAC,eAC/C9a,EAAU0Z,cAAc,CAAGA,EAEvB1Z,EAAUoH,MAAM,EAAIpH,EAAUoH,MAAM,CAAC,EAAE,EACvCsS,EAAe7V,IAAI,CAAC8R,GAAmB3V,EAAUoH,MAAM,CAAC,EAAE,CAAChH,KAAK,CAAE,gBAAiB,WAC/EyC,EAAM7C,SAAS,CAACib,2BAA2B,EAC/C,GAER,CAcAH,eAAeI,CAAS,CAAE,CACtB,IAAMlb,EAAY,IAAI,CAAEmb,EAAS,EAAE,CAQnC,MAPA,CAAC,SAAU,UAAU,CAAC/K,OAAO,CAAC,SAAUgL,CAAI,EACxCpb,CAAS,CAACob,EAAK,CAAChL,OAAO,CAAC,SAAUiL,CAAa,CAAE5O,CAAK,EAClD0O,EAAOtX,IAAI,CAAC8R,GAAmB0F,EAAc9J,OAAO,CAAE2J,EAAW,SAAU9X,CAAC,EACxEpD,CAAS,CAACob,EAAO,YAAY,CAAChY,EAAGqJ,EACrC,GACJ,EACJ,GACO0O,CACX,CAiBAG,gBAAgBlY,CAAC,CAAEqJ,CAAK,CAAE,CACtBrJ,EAAI,IAAI,CAACP,KAAK,CAAC4Q,OAAO,EAAEC,UAAUtQ,IAAMA,EACxC,IAAwBP,EAAQ7C,AAAd,IAAI,CAAoB6C,KAAK,CAAEzC,EAAQJ,AAAvC,IAAI,CAA6CI,KAAK,CAAEiX,EAAYrX,AAApE,IAAI,CAA0EqX,SAAS,CAAEK,EAAgB1X,AAAzG,IAAI,CAA+G4M,IAAI,CAAE/H,EAAQ7E,AAAjI,IAAI,CAAuI6E,KAAK,CAC9J0W,EAAoBvb,AADN,IAAI,CACYW,IAAI,CAAEgQ,EAASvN,EAAEuN,MAAM,CAAElL,EAAUD,EAAUgW,EAAK7a,CAEhFkC,CAAAA,EAAMnC,QAAQ,GACdiQ,EAASvN,EAAEwN,MAAM,CACjB2K,EAAoBvb,AALN,IAAI,CAKYgB,GAAG,EAEjCyL,AAAU,IAAVA,GAEAzM,AATc,IAAI,CASR6T,aAAa,CAAGlD,EAC1B3Q,AAVc,IAAI,CAUR0a,UAAU,CAAG7V,EACvB7E,AAXc,IAAI,CAWRyb,UAAU,CAAG9K,EAAS0G,IAIhC1W,EAAOgQ,EAAS4K,EAAoB1W,EAAQ,EACxC4H,AAAU,IAAVA,EACA9L,EAAO8J,KAAKvH,GAAG,CAAC,EAAGvC,GAEJ,IAAV8L,GAAe9L,EAAOkE,GAAS6S,IACpC/W,EAAO+W,EAAgB7S,EACnB7E,AArBM,IAAI,CAqBA0b,gBAAgB,EAE1B/a,GAAQkE,EACRW,EAAWxF,AAxBL,IAAI,CAwBW2b,gBAAgB,GAAGvQ,OAAO,EAI/C3F,EAAWzF,AA5BL,IAAI,CA4BW2b,gBAAgB,GAAGxQ,OAAO,EAGnDxK,IAAS0W,IACTrX,AAhCU,IAAI,CAgCJ0a,UAAU,CAAG7V,EAEnBiR,GAAkB0F,AADtBA,CAAAA,EAAMpb,EAAMkE,aAAa,CAACe,YAAY,CAAC1E,EAAMA,EAAOkE,EAAOW,EAAUC,EAAQ,EACnDxC,GAAG,GACzBkT,GAAoB,IAAI,CAAE,WAAY,CAClClT,IAAKwH,KAAKxH,GAAG,CAACuY,EAAIvY,GAAG,CAAEuY,EAAItY,GAAG,EAC9BA,IAAKuH,KAAKvH,GAAG,CAACsY,EAAIvY,GAAG,CAAEuY,EAAItY,GAAG,EAC9Bd,OAAQ,CAAA,EACRwZ,eAAgB,CACZhX,QAAS,WACb,CACJ,IAIhB,CAYAiX,iBAAiBzY,CAAC,CAAEqJ,CAAK,CAAE,CACvBrJ,EAAI,IAAI,CAACP,KAAK,CAAC4Q,OAAO,EAAEC,UAAUtQ,IAAMA,EACxC,IAAwBP,EAAQ7C,AAAd,IAAI,CAAoB6C,KAAK,CAAEiZ,EAAYjZ,EAAMzC,KAAK,CAAC,EAAE,CAG3E2b,EAAU/b,AAHQ,IAAI,CAGF0b,gBAAgB,AAChCjP,AAAU,CAAA,IAAVA,GAEAzM,AANc,IAAI,CAMRwa,WAAW,CAAG,CAAA,EACxBxa,AAPc,IAAI,CAORgc,cAAc,CAAGhc,AAPb,IAAI,CAOmBsX,SAAS,CAC9CtX,AARc,IAAI,CAQRic,YAAY,CAAGF,EAAUD,EAAU7Y,GAAG,CAAG6Y,EAAU5Y,GAAG,GAIhElD,AAZc,IAAI,CAYRya,YAAY,CAAG,CAAA,EACzBza,AAbc,IAAI,CAaRgc,cAAc,CAAGhc,AAbb,IAAI,CAamBqX,SAAS,CAC9CrX,AAdc,IAAI,CAcRic,YAAY,CAAGF,EAAUD,EAAU5Y,GAAG,CAAG4Y,EAAU7Y,GAAG,EAEpEJ,EAAMoI,aAAa,CAAC,KAAK,EAC7B,CAUA2P,YAAYxX,CAAC,CAAE,CACX,IAAMpD,EAAY,IAAI,CAAE6C,EAAQ7C,EAAU6C,KAAK,CAAE6U,EAAgB1X,EAAU0X,aAAa,CAAE7S,EAAQ7E,EAAU6E,KAAK,CAAE4W,EAAazb,EAAUyb,UAAU,CAAE/a,EAAWmC,EAAMnC,QAAQ,CAC3KC,EAAOX,EAAUW,IAAI,CAAEgQ,EAIvB,CAAA,CAACvN,EAAE4Q,OAAO,EAAI5Q,AAAuB,IAAvBA,EAAE4Q,OAAO,CAAC,EAAE,CAACkI,KAAK,AAAK,IAErCvL,EAASvN,AADTA,CAAAA,EAAIP,EAAM4Q,OAAO,EAAEC,UAAUtQ,IAAMA,CAAAA,EACxBuN,MAAM,CAEbjQ,IACAC,EAAOX,EAAUgB,GAAG,CACpB2P,EAASvN,EAAEwN,MAAM,EAGjB5Q,EAAUwa,WAAW,EACrBxa,EAAUkU,UAAU,CAAG,CAAA,EACvBlU,EAAUgD,MAAM,CAAC,EAAG,EAAG2N,EAAShQ,EAAMX,EAAUgc,cAAc,GAGzDhc,EAAUya,YAAY,EAC3Bza,EAAUkU,UAAU,CAAG,CAAA,EACvBlU,EAAUgD,MAAM,CAAC,EAAG,EAAGhD,EAAUgc,cAAc,CAAErL,EAAShQ,IAGrDX,EAAU6T,aAAa,GAC5B7T,EAAUkU,UAAU,CAAG,CAAA,EACnBvD,EAAS8K,EACT9K,EAAS8K,EAGJ9K,EACL+G,EAAgB+D,EAAa5W,GAC7B8L,CAAAA,EAAS+G,EAAgB+D,EAAa5W,CAAI,EAE9C7E,EAAUgD,MAAM,CAAC,EAAG,EAAG2N,EAAS8K,EAAY9K,EAAS8K,EAAa5W,IAElE7E,EAAUkU,UAAU,EACpBlU,EAAUqB,SAAS,EACnBkV,GAAevW,EAAUqB,SAAS,CAACd,OAAO,CAACkO,UAAU,CAGrD,CAACiH,IACG,CAAC,IAAI,CAAC7S,KAAK,CAACwS,OAAO,IACvBjS,EAAEkK,OAAO,CAAGlK,EAAEV,IAAI,CAClByZ,WAAW,WACPnc,EAAU6a,SAAS,CAACzX,EACxB,EAAG,IAGf,CASAyX,UAAUzX,CAAC,CAAE,CACT,IAEIkU,EAAWD,EAAW+E,EAAe5W,EAAUC,EAAU+V,EAFrC3Y,EAAQ7C,AAAd,IAAI,CAAoB6C,KAAK,CAAEzC,EAAQJ,AAAvC,IAAI,CAA6CI,KAAK,CAAEiB,EAAYrB,AAApE,IAAI,CAA0EqB,SAAS,CAAE+Q,EAAWhP,EAAEgP,QAAQ,EAAIhP,EAAG1C,EAAWmC,EAAMnC,QAAQ,CAAEuW,EAAOjX,AAAvJ,IAAI,CAA6JoU,QAAQ,EAAI,CAACpU,AAA9K,IAAI,CAAoLkU,UAAU,CAChN,UAAY,OAMhB,CAAA,AAAClU,AAPiB,IAAI,CAOXkU,UAAU,EAAK,CAAA,CAAC7S,GAAa,CAACA,EAAU6S,UAAU,AAAD,GACxD9Q,AAAc,cAAdA,EAAEwB,OAAO,AAAe,IACxBwX,EAAgBpc,AATF,IAAI,CASQ2b,gBAAgB,GAEtC3b,AAXU,IAAI,CAWJqX,SAAS,GAAKrX,AAXd,IAAI,CAWoBgc,cAAc,CAChDxW,EAAWxF,AAZD,IAAI,CAYOic,YAAY,CAE5Bjc,AAdK,IAAI,CAcCsX,SAAS,GAAKtX,AAdnB,IAAI,CAcyBgc,cAAc,EACrDvW,CAAAA,EAAWzF,AAfD,IAAI,CAeOic,YAAY,AAAD,EAGhCjc,AAlBU,IAAI,CAkBJsX,SAAS,GAAKtX,AAlBd,IAAI,CAkBoB4M,IAAI,EACtCnH,CAAAA,EAAWzF,AAnBD,IAAI,CAmBO0b,gBAAgB,CACjCU,EAAchR,OAAO,CACrBgR,EAAcjR,OAAO,AAAD,EAGA,IAAxBnL,AAxBU,IAAI,CAwBJqX,SAAS,EACnB7R,CAAAA,EAAWxF,AAzBD,IAAI,CAyBO0b,gBAAgB,CACjCU,EAAcjR,OAAO,CACrBiR,EAAchR,OAAO,AAAD,EAGxB0K,GAAkB0F,AADtBA,CAAAA,EAAMpb,EAAMkE,aAAa,CAACe,YAAY,CAACrF,AA7BzB,IAAI,CA6B+BqX,SAAS,CAAErX,AA7B9C,IAAI,CA6BoDsX,SAAS,CAAE9R,EAAUC,EAAQ,EACzExC,GAAG,GACzBkT,GAAoB,IAAI,CAAE,WAAY,CAClClT,IAAKwH,KAAKxH,GAAG,CAACuY,EAAIvY,GAAG,CAAEuY,EAAItY,GAAG,EAC9BA,IAAKuH,KAAKvH,GAAG,CAACsY,EAAIvY,GAAG,CAAEuY,EAAItY,GAAG,EAC9Bd,OAAQ,CAAA,EACRC,UAAWrC,CAAAA,AAnCL,IAAI,CAmCWkU,UAAU,EAAW,KAC1C0H,eAAgB,CACZhX,QAAS,YACTyX,UAAW,iBACXjK,SAAUA,CACd,CACJ,IAGU,cAAdhP,EAAEkK,OAAO,EACTlK,AAAc,cAAdA,EAAEkK,OAAO,EACTtN,CAAAA,AA9Cc,IAAI,CA8CRwa,WAAW,CAAGxa,AA9CV,IAAI,CA8CgBya,YAAY,CAC1Cza,AA/CU,IAAI,CA+CJ6T,aAAa,CAAG7T,AA/ChB,IAAI,CA+CsB0a,UAAU,CAC1C1a,AAhDM,IAAI,CAgDAic,YAAY,CAAGjc,AAhDnB,IAAI,CAgDyBgc,cAAc,CAC7Chc,AAjDE,IAAI,CAiDIkU,UAAU,CAAGlU,AAjDrB,IAAI,CAiD2Byb,UAAU,CAAG,IAAG,EAG7Dzb,AApDc,IAAI,CAoDRsZ,gBAAgB,EAC1BjD,GAAmBrW,AArDL,IAAI,CAqDWqX,SAAS,GACtChB,GAAmBrW,AAtDL,IAAI,CAsDWsX,SAAS,IACtCD,EAAY5M,KAAKyM,KAAK,CAAClX,AAvDT,IAAI,CAuDeqX,SAAS,EAC1CC,EAAY7M,KAAKyM,KAAK,CAAClX,AAxDT,IAAI,CAwDesX,SAAS,EACtCtX,AAzDU,IAAI,CAyDJiY,MAAM,EAChBjY,AA1DU,IAAI,CA0DJ+X,SAAS,CAACV,EAAWC,EAAW5W,EAAUuW,GAEpDjX,AA5DU,IAAI,CA4DJuX,OAAO,EACjBvX,AA7DU,IAAI,CA6DJoX,WAAW,CAACC,EAAWC,EAAW5W,EAAUuW,GAEtDjX,AA/DU,IAAI,CA+DJmB,gBAAgB,CAACsF,OAAO,CAAC7E,OAAO,EAC1CnD,OAAO6d,IAAI,CAACtc,AAhEF,IAAI,CAgEQyG,OAAO,EAAE+J,MAAM,GACjCxQ,AAjEM,IAAI,CAiEAyG,OAAO,CAAC+J,MAAM,GAC5BxQ,AAlEU,IAAI,CAkEJgX,UAAU,CAACK,EAAW,EAAG3W,EAAUuW,GAC7CjX,AAnEU,IAAI,CAmEJgX,UAAU,CAACM,EAAW,EAAG5W,EAAUuW,IAGzD,CAOArE,cAAe,CACP,IAAI,CAAC8G,cAAc,GACnB,IAAI,CAACA,cAAc,CAACtJ,OAAO,CAAC,SAAUmM,CAAM,EACxCA,GACJ,GACA,IAAI,CAAC7C,cAAc,CAAG,KAAK,GAE/B,IAAI,CAAC8C,sBAAsB,EAC/B,CAOAA,wBAAyB,CACrB,IAAMhD,EAAa,IAAI,CAACA,UAAU,EAAI,EAAE,AACpC,CAAA,IAAI,CAACF,gBAAgB,EAAIE,CAAU,CAAC,EAAE,GACW,CAAA,IAA7C,IAAI,CAACrY,gBAAgB,CAACoY,kBAAkB,EACxCC,EAAWpJ,OAAO,CAAC,SAAUhJ,CAAM,EAC/BoP,GAAsBpP,EAAQ,cAAe,IAAI,CAACqS,kBAAkB,CACxE,EAAG,IAAI,EAGPD,CAAU,CAAC,EAAE,CAACpZ,KAAK,EACnBoW,GAAsBgD,CAAU,CAAC,EAAE,CAACpZ,KAAK,CAAE,gBAAiB,IAAI,CAACqc,sBAAsB,EAGnG,CAMA7C,iBAAkB,CACd,OAAQ,IAAI,CAAC/W,KAAK,CAACnC,QAAQ,CACvB,CAAC,IAAI,CAACD,gBAAgB,CAAE,EAAG,CAAC,IAAI,CAACA,gBAAgB,CAAE,EAAE,CACrD,CAAC,EAAG,CAAC,IAAI,CAACA,gBAAgB,CAAE,EAAG,IAAI,CAACA,gBAAgB,CAAC,AAC7D,CAOAwQ,KAAKpO,CAAK,CAAE,CACR,IAAML,EAAeK,EAAMtC,OAAO,CAAEY,EAAmBqB,EAAaxC,SAAS,EAAI,CAAC,EAAGsZ,EAAmBnY,EAAiBS,OAAO,CAAEyB,EAAmBb,EAAanB,SAAS,EAAI,CAAC,EAAGqb,EAAmBrZ,EAAiBzB,OAAO,CAAEd,EAASwY,GAAoBnY,EAAiBL,MAAM,EAAI,EAAGN,EAAkBkc,GAAoBrZ,EAAiBvC,MAAM,EAAI,EAAGL,EAAmB4C,EAAiBmL,cAAc,EAAIhO,GAAmB,CACxa,CAAA,IAAI,CAACiG,OAAO,CAAG,EAAE,CACjB,IAAI,CAACwR,MAAM,CAAG,EAAE,CAChB,IAAI,CAACpV,KAAK,CAAGA,EACb,IAAI,CAAC5C,aAAa,GAClB,IAAI,CAACa,MAAM,CAAGA,EACd,IAAI,CAACN,eAAe,CAAGA,EACvB,IAAI,CAACC,gBAAgB,CAAGA,EACxB,IAAI,CAACic,gBAAgB,CAAGA,EACxB,IAAI,CAACpD,gBAAgB,CAAGA,EACxB,IAAI,CAACnY,gBAAgB,CAAGA,EACxB,IAAI,CAACkC,gBAAgB,CAAGA,EACxB,IAAI,CAACgW,WAAW,GAChB,IAAMrZ,EAAY,IAAI,CAAEwZ,EAAaxZ,EAAUwZ,UAAU,CAAEmD,EAAa9Z,EAAMzC,KAAK,CAACoQ,MAAM,CAAEoM,EAAa/Z,EAAMxC,KAAK,CAACmQ,MAAM,CAAEqM,EAAYrD,GAAcA,CAAU,CAAC,EAAE,EAAIA,CAAU,CAAC,EAAE,CAACpZ,KAAK,EACvLyC,EAAMzC,KAAK,CAAC,EAAE,EAAI,CAAEG,QAAS,CAAC,CAAE,EAEpC,GADAsC,EAAMia,UAAU,CAAG,CAAA,EACf9c,EAAUsZ,gBAAgB,CAAE,CAC5B,IAAMK,EAAU,IAAI,CAACC,eAAe,EAEpC5Z,CAAAA,EAAUI,KAAK,CAAG,GAAKX,CAAAA,GAAgG,EAAGoD,EAAOyT,GAAgB,CAE7IyG,OAAQF,EAAUtc,OAAO,CAACwc,MAAM,CAChCC,QAASH,EAAUtc,OAAO,CAACyc,OAAO,CAClCC,WAAYJ,EAAUtc,OAAO,CAAC0c,UAAU,AAC5C,EAAG9b,EAAiBf,KAAK,CAAE,CACvBsC,KAAM,WACNrC,MAAOc,EAAiBd,KAAK,EAAE6H,GAC/BuE,MAAOkQ,EACPlR,WAAY,CAAA,EACZmC,OAAQ,EACRsP,mBAAoB,CAAA,EACpB9T,YAAa,CAAA,EACbC,UAAW,CAAA,EAEXC,WAAYuT,EAAUtc,OAAO,CAACyc,OAAO,CAAG,EACpCH,EAAUtc,OAAO,CAAC+I,UAAU,CAChCC,WAAYsT,EAAUtc,OAAO,CAACyc,OAAO,CAAG,EACpCH,EAAUtc,OAAO,CAACgJ,UAAU,CAChC4T,YAAa,CAAA,CACjB,EAAGta,EAAMnC,QAAQ,CAAG,CAChBiZ,QAAAA,EACAjT,MAAO5F,CACX,EAAI,CACA6Y,QAAAA,EACA7Y,OAAAA,CACJ,GAAI,SACJd,EAAUK,KAAK,CAAG,GAAKZ,CAAAA,GAAgG,EAAGoD,EAAOyT,GAAgBnV,EAAiBd,KAAK,CAAE,CACrK+c,WAAY,CAAA,EACZxP,OAAQ,EACRnB,MAAOmQ,EACPnR,WAAY,CAAA,EACZ2B,SAAUmJ,GAAgBpV,EAAiBd,KAAK,EAC5Cc,EAAiBd,KAAK,CAAC+M,QAAQ,CAAIvK,EAAMxC,KAAK,CAAC,EAAE,EAAIwC,EAAMxC,KAAK,CAAC,EAAE,CAAC+M,QAAQ,CAAG,CAAA,GACnF+P,YAAa,CAAA,CACjB,EAAGta,EAAMnC,QAAQ,CAAG,CAChBgG,MAAO5F,CACX,EAAI,CACAA,OAAQA,CACZ,GAAI,SAEA0Y,GAAcrY,EAAiBiG,MAAM,CAACiW,IAAI,CAC1Crd,EAAUsd,qBAAqB,CAAC,CAAA,GAGH,IAAxBza,EAAMuE,MAAM,CAACoJ,MAAM,EACxBxQ,CAAAA,EAAUud,YAAY,CAAG5H,GAAmB9S,EAAO,eAAgB,WAE3DA,EAAMuE,MAAM,CAACoJ,MAAM,CAAG,GAAK,CAACxQ,EAAUoH,MAAM,GAC5CpH,EAAUC,aAAa,GACvBD,EAAUud,YAAY,GAE9B,EAAC,EAELvd,EAAU0b,gBAAgB,CAAG,AAAC7Y,EAAMnC,QAAQ,EAAI,CAACV,EAAUI,KAAK,CAACgN,QAAQ,EAAM,CAACvK,EAAMnC,QAAQ,EAAIV,EAAUI,KAAK,CAACgN,QAAQ,CAE1HpN,EAAUmY,cAAc,GAExBnY,EAAUgZ,cAAc,EAE5B,MAEIhZ,EAAUI,KAAK,CAAG,CACdyC,MAAAA,EACAyB,cAAe,CACXwV,KAAM,CAAA,CACV,EACAjU,UAAW,SAAU2X,CAAK,CAAEzB,CAAO,EAC/B,IAAM1X,EAAOxB,EAAMzC,KAAK,CAAC,EAAE,CAAEob,EAAMnX,EAAKtB,WAAW,GAAI0a,EAAmBpZ,EAAKkM,GAAG,CAAG,EAAI9P,EAAkBwC,EAAMyT,GAAO,MAAOrS,EAAK9D,OAAO,CAAC0C,GAAG,CAAEuY,EAAIpQ,OAAO,EAAGsS,EAAahH,GAAO,MAAOrS,EAAK9D,OAAO,CAAC2C,GAAG,CAAEsY,EAAIrQ,OAAO,EAAIlI,EAC3N,OAAO8Y,EAEH,AAACyB,EAAQE,EAAaD,EAAoBxa,EAE1Cwa,EAAoBD,CAAAA,EAAQva,CAAE,EAAKya,CAC3C,EACAtD,SAAU,SAAUoD,CAAK,EACrB,OAAO,IAAI,CAAC3X,SAAS,CAAC2X,EAC1B,EACAlD,QAAS,SAAUkD,CAAK,EACpB,OAAO,IAAI,CAAC3X,SAAS,CAAC2X,EAAO,CAAA,EACjC,CACJ,EACAxd,EAAUI,KAAK,CAACkE,aAAa,CAACD,IAAI,CAAGrE,EAAUI,KAAK,CACpDJ,EAAUI,KAAK,CAACkE,aAAa,CAACe,YAAY,CAAIuG,AAn+FKrH,EAm+FoBxF,SAAS,CAACsG,YAAY,CAACqM,IAAI,CAAC1R,EAAUI,KAAK,CAACkE,aAAa,EAGpI,GAAIzB,EAAMtC,OAAO,CAACc,SAAS,EAAEO,QAAS,CAClC,IAAMrB,EAAU+V,GAAgBzT,EAAMtC,OAAO,CAACc,SAAS,CAAE,CAAEyL,SAAUjK,EAAMnC,QAAQ,AAAC,GAC/E2V,GAAmB9V,EAAQe,MAAM,GAClCf,CAAAA,EAAQe,MAAM,CAAGuB,EAAMnC,QAAQ,CAAG,GAAK,CAAA,EAE3CmC,EAAMxB,SAAS,CAAGrB,EAAUqB,SAAS,CAAG,IAh/BM6K,GAg/BkBrJ,EAAMkK,QAAQ,CAAExM,EAASsC,GACzF8S,GAAmB3V,EAAUqB,SAAS,CAAE,UAAW,SAAU+B,CAAC,EAC1D,IAAMyB,EAAQ7E,EAAU4M,IAAI,CAAEI,EAAKnI,EAAQ,IAAI,CAACmI,EAAE,CAAEC,EAAOpI,EAAQ,IAAI,CAACoI,IAAI,AAC5EjN,CAAAA,EAAUkU,UAAU,CAAGlU,EAAUqB,SAAS,CAAC6S,UAAU,CACrDlU,EAAUgD,MAAM,CAAC,EAAG,EAAGiK,EAAMD,GACzB,IAAI,CAACK,oBAAoB,CAACjK,EAAEkK,OAAO,GACnC6O,WAAW,WACPnc,EAAU6a,SAAS,CAACzX,EACxB,EAER,EACJ,CAEApD,EAAU2d,mBAAmB,GAE7B3d,EAAU4d,cAAc,EAC5B,CAMAvE,aAAc,CACV,IAAMlY,EAAmB,IAAI,CAACA,gBAAgB,CAAEmY,EAAmB,IAAI,CAACA,gBAAgB,CAAEzW,EAAQ,IAAI,CAACA,KAAK,AAC5G,CAAA,IAAI,CAACjC,QAAQ,CAAG2V,GAAepV,EAAiBP,QAAQ,CAAEid,CAAAA,CAAQ,CAAA,CAACvE,GAAoBzW,EAAMnC,QAAQ,AAAD,EACxG,CAQAib,iBAAiBmC,CAAyB,CAAE,CACxC,IACIC,EADEC,EAAW,IAAI,CAACnb,KAAK,CAACzC,KAAK,CAAC,EAAE,CAAE6d,EAAO,IAAI,CAACpb,KAAK,CAACob,IAAI,CAAEC,EAAU,IAAI,CAAC9d,KAAK,CAAE+d,EAAiBD,EAAQ3d,OAAO,CAAE6d,EAAkBJ,EAASzd,OAAO,CASxJ,OAPKud,GAA6BE,AAAqB,OAArBA,EAAS5S,OAAO,EAC9C2S,CAAAA,EAAM,CACF3S,QAASmL,GACT0H,EAAK7X,KAAK,CAAC+X,GAAgBlb,KAAMyT,GAAO,MAAOuH,EAAK7X,KAAK,CAACgY,EAAgBnb,GAAG,EAAG+a,EAAS5S,OAAO,CAAE8S,EAAQ9S,OAAO,CAAE8S,EAAQjb,GAAG,GAC9HkI,QAASoL,GAAe0H,EAAK7X,KAAK,CAAC+X,GAAgBjb,KAAMwT,GAAO,MAAOuH,EAAK7X,KAAK,CAACgY,EAAgBlb,GAAG,EAAG8a,EAAS7S,OAAO,CAAE+S,EAAQ/S,OAAO,CAAE+S,EAAQhb,GAAG,EAC1J,CAAA,EAEG6a,CACX,CAaA9d,cAAcoe,CAAiB,CAAEjc,CAAM,CAAE,CACrC,IAAMS,EAAQ,IAAI,CAACA,KAAK,CAAE2W,EAAa,IAAI,CAACA,UAAU,CAAG,EAAE,CAC3D6E,EAAqBA,GACjBxb,EAAMtC,OAAO,EAAIsC,EAAMtC,OAAO,CAACP,SAAS,CAACwZ,UAAU,EAClD3W,CAAAA,EAAMuE,MAAM,CAACoJ,MAAM,CAEhB0F,GAAKrT,EAAMuE,MAAM,CAAE,AAACkX,GAAO,CAACA,EAAE/d,OAAO,CAACkL,UAAU,EAAGgB,KAAK,CACxD,CAAA,EAGR,AAAC5J,CAAAA,EAAMuE,MAAM,EAAI,EAAE,AAAD,EAAGgJ,OAAO,CAAC,CAAChJ,EAAQqJ,KAGlC,CAACrJ,EAAO7G,OAAO,CAACkL,UAAU,EACrBrE,CAAAA,EAAO7G,OAAO,CAACge,eAAe,EAC3B,AAAC9N,CAAAA,IAAM4N,GACHjX,EAAO7G,OAAO,CAAC2H,EAAE,GAAKmW,CAAgB,GACtCjX,AAAmC,CAAA,IAAnCA,EAAO7G,OAAO,CAACge,eAAe,AAAS,GAC/C/E,EAAW3V,IAAI,CAACuD,EAExB,GAEI,IAAI,CAAChH,KAAK,EAAI,CAAC,IAAI,CAACA,KAAK,CAACkE,aAAa,CAACwV,IAAI,EAC5C,IAAI,CAACwD,qBAAqB,CAAC,CAAA,EAAMlb,EAEzC,CAQAkb,sBAAsBpM,CAAS,CAAE9O,CAAM,CAAE,CACrC,IAAMpC,EAAY,IAAI,CAAE6C,EAAQ7C,EAAU6C,KAAK,CAAE2W,EAAaxZ,EAAUwZ,UAAU,CAAEgF,EAAiB,CACjGC,oBAAqB,CAAA,EACrBhS,MAAO,KACPiS,SAAU,KACV5L,MAAO,MACP6L,SAAU,CAAA,EACVve,MAAO,IAAI,CAACe,gBAAgB,CAACf,KAAK,EAAE8H,GACpC7H,MAAO,IAAI,CAACc,gBAAgB,CAACd,KAAK,EAAE6H,GACpC0W,aAAc,CAAA,EACdC,SAAU,KAAK,EACfpT,WAAY,CAAA,EACZqT,OAAQ,CACJC,SAAU,CACNhW,QAAS,CACb,CACJ,CACJ,EAEAiW,EAAkBhf,EAAUoH,MAAM,CAC9B,AAACpH,CAAAA,EAAUoH,MAAM,EAAI,EAAE,AAAD,EAAGyP,MAAM,CAAC,AAACoI,IAC7B,IAAMC,EAAOD,EAAUzF,UAAU,OACjC,CAAIA,CAAAA,AAA2B,EAA3BA,EAAW2F,OAAO,CAACD,EAAQ,IAGvBA,IACA1I,GAAsB0I,EAAM,cAAelf,EAAUyZ,kBAAkB,EACvE,OAAOyF,EAAKF,eAAe,EAI3BC,EAAUpc,KAAK,EACfoc,EAAU7Z,OAAO,GAEd,CAAA,EAGf,GACAga,EAAaC,EAAwBC,EAA8Btf,EAAUmB,gBAAgB,CAACiG,MAAM,CAAEmY,EAGtG/F,GAAcA,EAAWhJ,MAAM,EAC/BgJ,EAAWpJ,OAAO,CAAC,AAAC8O,IAChB,IAAMM,EAAkBN,EAAKF,eAAe,CAAES,EAAiBxJ,GAE/D,CACI5P,MAAO6Y,EAAK7Y,KAAK,CACjBqZ,QAASR,EAAKQ,OAAO,AACzB,EAAG,AAACtJ,GAAQkJ,GAER7J,GAAyBzV,SAAS,CAACoH,MAAM,CADzCkY,GAIJ,GAAIE,GACAxf,AAAkD,CAAA,IAAlDA,EAAUmB,gBAAgB,CAACoY,kBAAkB,CAC7C,MAEJiF,CAAAA,EAAepD,IAAI,CAAG,aAAe5B,EAAWhJ,MAAM,CAEtD+O,EAAuBH,AADvBA,CAAAA,EAAcF,EAAK3e,OAAO,EAAI,CAAC,CAAA,EACIY,gBAAgB,EAAI,CAAC,EAGxDse,EAAezX,UAAU,CAAGyO,GAAMgJ,EAAezX,UAAU,EAG3DqX,AAFAA,CAAAA,EAAyB/I,GAAgB8I,EAAaZ,EAAgBiB,EAAgBF,EAAoB,EAEnF5Z,UAAU,CAAG4Q,GAEpCkJ,EAAe9Z,UAAU,CAAE4Z,EAAqB5Z,UAAU,CAE1D8P,GAAyBkK,WAAW,CAACN,EAAuB3c,IAAI,EAAI,OAAO,CAACiD,UAAU,EAGtF,IAAMia,EAAsBL,EAAqBlC,IAAI,EAAIoC,EAAepC,IAAI,AAC5Erd,CAAAA,EAAU6f,gBAAgB,CACtB7f,EAAU6f,gBAAgB,EAAI,CAAC,CAACD,EACpCP,EAAuBhC,IAAI,CAAIuC,GAC3BR,EAAY/B,IAAI,EAAEyC,MAAM,GAExBN,GAAmBA,EAAgBjf,OAAO,CAC1Cif,EAAgBlK,MAAM,CAAC+J,EAAwBjd,IAG/C8c,EAAKF,eAAe,CAAGnc,EAAMkd,UAAU,CAACV,GAExCxc,EAAMmd,aAAa,GACnBd,EAAKF,eAAe,CAACxF,UAAU,CAAG0F,EAClCF,EAAgBnb,IAAI,CAACqb,EAAKF,eAAe,EAEjD,GAKAM,CAAAA,EAA4BjC,IAAI,EAChC,CAAE7D,CAAAA,GAAcA,EAAWhJ,MAAM,AAAD,GAChC4F,GAAQkJ,EAA2B,IACnCtf,EAAU6f,gBAAgB,CAAG,CAAA,EAI7BP,AAFAA,CAAAA,EACI7I,GAAM6I,EAA2B,EACTlP,OAAO,CAAC,CAAC6P,EAAmBxP,KACpD+N,EAAepD,IAAI,CACf,aAAgB4D,CAAAA,EAAgBxO,MAAM,CAAG,CAAA,EAc7C6O,AAbAA,CAAAA,EAAyB/I,GAAgBb,GAAyBzV,SAAS,CAACoH,MAAM,CAAE,CAOhFf,MAAOxD,EAAMuE,MAAM,CAACqJ,EAAE,EAClB,CAAC5N,EAAMuE,MAAM,CAACqJ,EAAE,CAAClQ,OAAO,CAACkL,UAAU,EACnC5I,EAAMuE,MAAM,CAACqJ,EAAE,CAACpK,KAAK,EACrBxD,EAAMtC,OAAO,CAAC2f,MAAM,CAACzP,EAAE,EACvB5N,EAAMtC,OAAO,CAAC2f,MAAM,CAAC,EAAE,AAC/B,EAAG1B,EAAgByB,EAAiB,EACb5C,IAAI,CAAG4C,EAAkB5C,IAAI,CAChDgC,EAAuBhC,IAAI,GAC3Brd,EAAU6f,gBAAgB,CAAG,CAAA,EAC7Bb,EAAgBnb,IAAI,CAAChB,EAAMkd,UAAU,CAACV,IAE9C,IAEAnO,GACA,IAAI,CAACyM,mBAAmB,EAEhC,CAQAA,qBAAsB,CAClB,IAAM3d,EAAY,IAAI,CAAEwZ,EAAaxZ,EAAUwZ,UAAU,EAAI,EAAE,AAK3DA,CAAAA,CAAU,CAAC,EAAE,EAAIA,CAAU,CAAC,EAAE,CAACpZ,KAAK,EACpCoZ,CAAU,CAAC,EAAE,CAACE,cAAc,CAAC7V,IAAI,CAAC8R,GAAmB6D,CAAU,CAAC,EAAE,CAACpZ,KAAK,CAAE,gBAAiB,IAAI,CAACqc,sBAAsB,GAE1HjD,EAAWpJ,OAAO,CAAC,AAAC8O,IAEhBA,EAAKxF,cAAc,CAAC7V,IAAI,CAAC8R,GAAmBuJ,EAAM,OAAQ,WAClD,IAAI,CAACF,eAAe,EACpB,IAAI,CAACA,eAAe,CAACmB,UAAU,CAAC,CAAA,EAAM,CAAA,EAE9C,IACAjB,EAAKxF,cAAc,CAAC7V,IAAI,CAAC8R,GAAmBuJ,EAAM,OAAQ,WAClD,IAAI,CAACF,eAAe,EACpB,IAAI,CAACA,eAAe,CAACmB,UAAU,CAAC,CAAA,EAAO,CAAA,EAE/C,IAGiD,CAAA,IAA7C,IAAI,CAAChf,gBAAgB,CAACoY,kBAAkB,EACpC2F,EAAK9e,KAAK,EACV8e,EAAKxF,cAAc,CAAC7V,IAAI,CAAC8R,GAAmBuJ,EAAM,cAAe,IAAI,CAACzF,kBAAkB,GAIhGyF,EAAKxF,cAAc,CAAC7V,IAAI,CAAC8R,GAAmBuJ,EAAM,SAAU,WACpD1F,GACAxD,GAAMwD,EAAY0F,GAElB,IAAI,CAACF,eAAe,EAAIhf,EAAUoH,MAAM,GACxC4O,GAAMhW,EAAUoH,MAAM,CAAE,IAAI,CAAC4X,eAAe,EACxClJ,GAAkB,IAAI,CAACkJ,eAAe,CAACze,OAAO,GAC9C,IAAI,CAACye,eAAe,CAACoB,MAAM,CAAC,CAAA,GAEhC,OAAO,IAAI,CAACpB,eAAe,CAEnC,GACJ,EACJ,CASAqB,iBAAiBC,CAAgB,CAAE,CAC/B,OAAO,IAAI,CAAC9G,UAAU,CAAC+G,MAAM,CAAC,SAAUtd,CAAG,CAAEmE,CAAM,EAE/C,OAAOqD,KAAKxH,GAAG,CAACA,EAAKmE,EAAOoZ,SAAS,CAAC,IAAI,CAAC,EAAE,EAAIvd,EACrD,EAAGqd,EACP,CASArF,6BAA8B,CAC1B,IAAM7a,EAAQ,IAAI,CAACA,KAAK,CACxB,GAAI,AAA6B,KAAA,IAAtBA,EAAM2C,WAAW,CAAkB,CAC1C,IAAMqZ,EAAgB,IAAI,CAACT,gBAAgB,CAAC,CAAA,GACxCS,GACCA,CAAAA,EAAchR,OAAO,GAAKhL,EAAM6C,GAAG,EAChCmZ,EAAcjR,OAAO,GAAK/K,EAAM8C,GAAG,AAAD,IACtC9C,EAAM6C,GAAG,CAAGmZ,EAAchR,OAAO,CACjChL,EAAM8C,GAAG,CAAGkZ,EAAcjR,OAAO,CAEzC,CACJ,CAOAsR,wBAAyB,CACrB,IAMI1W,EAAQH,EANY5F,EAAY8b,AAAlB,IAAI,CAAwBjZ,KAAK,CAAC7C,SAAS,CAAEygB,EAAe3E,AAA5D,IAAI,CAAkE/Y,WAAW,GAAI2d,EAAUD,EAAaxd,GAAG,CAAE0d,EAAUF,EAAavd,GAAG,CAAE0d,EAAcH,EAAarV,OAAO,CAAEyV,EAAcJ,EAAatV,OAAO,CAAEtG,EAAQ8b,EAAUD,EAASI,EAAa9gB,EAAU8gB,UAAU,CAAEC,EAAa/gB,EAAU+gB,UAAU,CAAE9D,EAAa1G,GAAeuF,AAAlV,IAAI,CAAwVkB,OAAO,EAAEgE,kBAAkBlF,AAAvX,IAAI,CAA6Xvb,OAAO,CAAC0c,UAAU,EAAG,GAAI+B,EAAkBhf,EAAUoH,MAAM,EAAIpH,EAAUoH,MAAM,CAAC,EAAE,CAAE6Z,EAAiB,CAAC,CAACnF,AAAxe,IAAI,CAA8etO,WAAW,AAO3gB,EAHQsO,CAAAA,AAJM,IAAI,CAIAoF,SAAS,EAC3BpF,AAAgC,wBAAhCA,AALc,IAAI,CAKRoF,SAAS,CAACtc,OAAO,AAAyB,IAKhDkc,GAEA/a,CAAAA,EAASH,AADTA,CAAAA,EAASgb,CAAU,EACD/b,CAAI,EAItBkc,IACAhb,EAAS8a,EAAc5D,EAElB6D,GACDlb,CAAAA,EAAS6E,KAAKvH,GAAG,CAAC0d,EAClB7a,EAASlB,EAAO7E,EAAUqgB,gBAAgB,CAACrB,GAAmBA,EAAgBmC,KAAK,CAC/EnC,EAAgBmC,KAAK,CAAC,EAAE,CACxB,CAACC,OAAOC,SAAS,EAAC,GAI1BJ,GAAmBH,CAAAA,GAAcC,CAAS,GACtC1K,GAAmBzQ,KACnBkW,AA7BM,IAAI,CA6BA7Y,GAAG,CAAG6Y,AA7BV,IAAI,CA6BgBwF,OAAO,CAAG1b,EACpCkW,AA9BM,IAAI,CA8BA5Y,GAAG,CAAG4Y,AA9BV,IAAI,CA8BgByF,OAAO,CAAGxb,IAKhD/F,EAAU8gB,UAAU,CAChB9gB,EAAU+gB,UAAU,CAAG,IAC/B,CASAtH,oBAAqB,CACjB,IAAMzZ,EAAY,IAAI,CAAC6C,KAAK,CAAC7C,SAAS,CAAqBgf,EAAkB,IAAI,CAACA,eAAe,CAAEwC,EAAmBxhB,EAAU0b,gBAAgB,CAC5IjR,AAAoC,IAApCA,KAAKyM,KAAK,CAAClX,EAAUqX,SAAS,EAC9B5M,KAAKyM,KAAK,CAAClX,EAAUsX,SAAS,GAAK7M,KAAKyM,KAAK,CAAClX,EAAU4M,IAAI,CAGhE5M,CAAAA,EAAU+gB,UAAU,CAAGxK,GAAe,IAAI,CAAC1T,KAAK,CAACtC,OAAO,CAACP,SAAS,EAC9D,IAAI,CAAC6C,KAAK,CAACtC,OAAO,CAACP,SAAS,CAAC+gB,UAAU,CAAES,GAC7CxhB,EAAU8gB,UAAU,CAAG9gB,EAAUyhB,gBAAgB,CAPI,IAAI,CAOKzhB,GAE1Dgf,GAAmB,CAAChf,EAAU6f,gBAAgB,GAC9Cb,EAAgBze,OAAO,CAACmhB,UAAU,CAAGlI,AAVY,IAAI,CAULgH,SAAS,CAAC,IAAI,CAAC,EAAE,CACjExB,EAAgB2C,OAAO,CAACnI,AAXyB,IAAI,CAWlBjZ,OAAO,CAAC8c,IAAI,CAAE,CAAA,EAAO,KAAM,CAAA,GAEtE,CAOAoE,iBAAiBjI,CAAU,CAAExZ,CAAS,CAAE,CACpC,IAAM4hB,EAAW5hB,EAAUqgB,gBAAgB,CAAC7G,EAAWgH,SAAS,CAAC,IAAI,CAAC,EAAE,EAAGpgB,EAAQoZ,EAAWpZ,KAAK,CAAE8C,EAAM9C,EAAM8C,GAAG,CAAED,EAAM7C,EAAM6C,GAAG,CAAE4B,EAAQzE,EAAMG,OAAO,CAACsE,KAAK,CAC9Jic,EAAa,CAAA,EAgBjB,QAfIzK,CAAAA,GAAmBnT,IAAQmT,GAAmBpT,EAAG,IAG7C4B,GAAS3B,EAAM0e,EAAW,EACb1e,EAAM0e,EAAW/c,EAKjB5B,GAAO2e,EAOhC,CAOAhE,gBAAiB,CACR,IAAI,CAAClE,cAAc,EACpB,CAAA,IAAI,CAACA,cAAc,CAAG,EAAE,AAAD,EAE3B,IAAI,CAACA,cAAc,CAAC7V,IAAI,CAGxB8R,GAAmB,IAAI,CAAC9S,KAAK,CAAE,SAAU,WACrC,IAAM7C,EAAY,IAAI,CAACA,SAAS,CAAEI,EAAQJ,GAAcA,CAAAA,EAAUwZ,UAAU,EACxExZ,EAAUwZ,UAAU,CAAC,EAAE,EACvBxZ,EAAUwZ,UAAU,CAAC,EAAE,CAACpZ,KAAK,EAC7B,IAAI,CAACA,KAAK,CAAC,EAAE,AAAD,EACZA,GACAJ,EAAUgD,MAAM,CAAC5C,EAAM6C,GAAG,CAAE7C,EAAM8C,GAAG,CAE7C,GAEAyS,GAAmB,IAAI,CAAC9S,KAAK,CAAE,aAAc,WACzC,IAAoB7C,EAAY6C,AAAlB,IAAI,CAAoB7C,SAAS,CAC3C6hB,EAAa7hB,EAAUY,QAAQ,CAC/B,UAAY,cACZiC,CAHU,IAAI,CAGRnC,QAAQ,EACdmhB,CAAAA,EAAa7hB,EAAUY,QAAQ,CAC3B,cAAgB,UAAS,EAEjCiC,AAPc,IAAI,AAOb,CAACgf,EAAW,CAAG,AAAChf,CAAAA,AAPP,IAAI,AAOQ,CAACgf,EAAW,EAAI,CAAA,EAAM7hB,CAAAA,EAAUsZ,gBAAgB,EAAI,CAACzW,AAPjE,IAAI,CAOmEnC,QAAQ,CACzFV,EAAUc,MAAM,CACX,CAAA,IAAI,CAACO,SAAS,EAAEd,QAAQe,QAAU,CAAA,EACnCtB,EAAUQ,eAAe,CAAG,CAAA,EAAMR,CAAAA,EAAUmB,gBAAgB,CAACG,MAAM,EAAI,CAAA,CACnF,GAAIqU,GAAmBmB,GAAW,WAAY,SAAU1T,CAAC,EACrD,IAAI,CAACP,KAAK,CAACzC,KAAK,CAAC,EAAE,CAACoN,WAAW,CAACpK,EAAEH,GAAG,CAAEG,EAAEF,GAAG,CAAEE,EAAEhB,MAAM,CAAEgB,EAAEf,SAAS,CAAEe,EAAEwY,cAAc,CACzF,GACJ,CAOAxW,SAAU,CAEN,IAAI,CAACwN,YAAY,GACb,IAAI,CAACxS,KAAK,GACV4V,GAAM,IAAI,CAACnT,KAAK,CAACzC,KAAK,CAAE,IAAI,CAACA,KAAK,EAClC4V,GAAM,IAAI,CAACnT,KAAK,CAACif,IAAI,CAAE,IAAI,CAAC1hB,KAAK,GAEjC,IAAI,CAACC,KAAK,GACV2V,GAAM,IAAI,CAACnT,KAAK,CAACxC,KAAK,CAAE,IAAI,CAACA,KAAK,EAClC2V,GAAM,IAAI,CAACnT,KAAK,CAACif,IAAI,CAAE,IAAI,CAACzhB,KAAK,GAGrC,AAAC,CAAA,IAAI,CAAC+G,MAAM,EAAI,EAAE,AAAD,EAAGgJ,OAAO,CAAC,AAACkO,IACrBA,EAAElZ,OAAO,EACTkZ,EAAElZ,OAAO,EAEjB,GAEA,CACI,SAAU,QAAS,QAAS,SAAU,UAAW,iBACjD,kBAAmB,iBAAkB,YAAa,iBAClD,WACH,CAACgL,OAAO,CAAC,AAACtR,IACH,IAAI,CAACA,EAAK,EAAI,IAAI,CAACA,EAAK,CAACsG,OAAO,EAChC,IAAI,CAACtG,EAAK,CAACsG,OAAO,GAEtB,IAAI,CAACtG,EAAK,CAAG,IACjB,GAEA,CAAC,IAAI,CAAC2H,OAAO,CAAC,CAAC2J,OAAO,CAAC,AAAC2R,IACpBhM,GAAkCgM,EACtC,GAEA,IAAI,CAACvI,UAAU,CAACpJ,OAAO,CAAC,AAACkO,IACrBA,EAAEU,eAAe,CAAG,KAAK,CAC7B,GACA,IAAI,CAAC1F,gBAAgB,CAAG,CAAA,CAC5B,CACJ,CAwBA,IAAM0I,GAA8B,CAChCnf,MAAO,CACH/B,OAAQ,GACRQ,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAE,AACxB,EACA2gB,UAAW,CACPrgB,QAAS,CAAA,CACb,EACAtB,OAAQ,CACJsB,QAAS,CAAA,CACb,EACA5B,UAAW,CACP4B,QAAS,CAAA,CACb,EACA+d,YAAa,CACTvY,OAAQ,CACJ0X,OAAQ,CACJoD,MAAO,CACHtgB,QAAS,CAAA,CACb,CACJ,EACAyG,OAAQ,CACJzG,QAAS,CAAA,CACb,CACJ,CACJ,EACAP,UAAW,CACPO,QAAS,CAAA,CACb,EACA4H,MAAO,CACHC,KAAM,EACV,EACA0Y,QAAS,CACLvgB,QAAS,CAAA,CACb,EACAxB,MAAO,CACHsf,QAAS,CAAA,CACb,EACArf,MAAO,CACHS,OAAQ,EACR4e,QAAS,CAAA,CACb,CACJ,EAyBM,CAAE9f,MAAOwiB,EAAyB,CAAEziB,SAAU0iB,EAA4B,CAAEvS,UAAWwS,EAA6B,CAAEziB,KAAM0iB,EAAwB,CAAE,CAAIljB,GAoBhK,OAAMmjB,GAmBF,OAAOxiB,UAAU+a,CAAQ,CAAExa,CAAO,CAAE,CAChC,IAAMkiB,EAAM,IAAID,GAAoBzH,EAAUxa,GAO9C,OANK,AAAClB,IAA+EqjB,UAAU,CAI3FrjB,IAA8EqjB,UAAU,CAAC7e,IAAI,CAAC4e,GAH9F,AAACpjB,IAA+EqjB,UAAU,CAAG,CAACD,EAAI,CAK/FA,CACX,CAMAtd,YAAYoM,CAAO,CAAEiC,CAAW,CAAE,CAC9B,IAAI,CAACmP,SAAS,CAAG,EAAE,CACnB,IAAI,CAACnP,WAAW,CAAGA,EACnB,IAAI,CAAChR,YAAY,CAAG4f,GAA0B/iB,IAA8EujB,UAAU,GA5E5EZ,GA4E6GxO,EAAY3Q,KAAK,CAAE,CAAE7C,UAAWwT,CAAY,GAC/M,IAAI,CAAChR,YAAY,CAACK,KAAK,EAAI2Q,EAAY1S,MAAM,EAC7C,CAAA,IAAI,CAAC0B,YAAY,CAACK,KAAK,CAAC/B,MAAM,CAAG0S,EAAY1S,MAAM,AAAD,EAEtD,IAAM+B,EAAQ,GAAKtD,CAAAA,GAAoG,EAAGgS,EAAS,IAAI,CAAC/O,YAAY,CACpJK,CAAAA,EAAMtC,OAAO,CAAG6hB,GAA0Bvf,EAAMtC,OAAO,CAAE,CAAEP,UAAW,CAAE4B,QAAS,CAAA,CAAK,EAAGP,UAAW,CAAEO,QAAS,CAAA,CAAK,CAAE,GAClH,IAAI,CAACY,YAAY,CAACxC,SAAS,EAAI,IAAI,CAACwC,YAAY,CAACnB,SAAS,GAC1D,IAAI,CAACmB,YAAY,CAACxC,SAAS,CAAC4B,OAAO,CAAG,CAAA,EACtC,IAAI,CAACY,YAAY,CAACnB,SAAS,CAACO,OAAO,CAAG,CAAA,GAE1C,IAAI,CAAC5B,SAAS,CAAG,IAxJiC8W,GAwJTjU,GACzCA,EAAM7C,SAAS,CAAG,IAAI,CAACA,SAAS,CAChC,IAAI,CAAC6iB,aAAa,EACtB,CAoBAnR,KAAKoR,CAAW,CAAEC,EAAS,CAAA,CAAI,CAAE,CAC7B,IAAMN,EAAM,IAAI,CAEVpe,EAAO,AAACye,aAAwBvjB,IAClCujB,EAAY1iB,KAAK,CAAC,EAAE,CACpB0iB,EACJ,GAAI,CAAEze,CAAAA,aAAiB5E,GAAiG,EACpH,OAEJ,GAAM,CAAEwD,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAG,IAAI,CAAClD,SAAS,CAACI,KAAK,CAAE4iB,EAAuB,EAAE,CACpE,GAAID,EAAQ,CACR,IAAME,EAAyBZ,GAA6Bhe,EAAM,cAAe,AAACjB,IAC1EA,CAAAA,AAAc,QAAdA,EAAEwB,OAAO,EACTxB,AAAc,SAAdA,EAAEwB,OAAO,EACTxB,AAAc,eAAdA,EAAEwB,OAAO,AAAgB,GACzB6d,EAAIhV,QAAQ,CAACrK,EAAEH,GAAG,CAAEG,EAAEF,GAAG,CAAE,CAAA,EAAME,AAAc,QAAdA,EAAEwB,OAAO,EAAcxB,AAAc,eAAdA,EAAEwB,OAAO,CAAmB,CAAEA,QAASP,CAAK,EAE5G,GACA2e,EAAqBnf,IAAI,CAACof,EAC9B,CACA,IAAMC,EAAsBb,GAA6B,IAAI,CAACriB,SAAS,CAAE,WAAY,AAACoD,IAClFiB,EAAKmJ,WAAW,CAACpK,EAAEH,GAAG,CAAEG,EAAEF,GAAG,CAAEE,EAAEhB,MAAM,CAAEgB,EAAEf,SAAS,CACxD,GACA2gB,EAAqBnf,IAAI,CAACqf,GAC1B,IAAIC,EAAY,IAAI,CAACR,SAAS,CAAC9L,MAAM,CAAC,SAAUsM,CAAS,EACrD,OAAOA,EAAU9e,IAAI,GAAKA,CAC9B,EAAE,CAAC,EAAE,CACA8e,IACDA,EAAY,CAAE9e,KAAAA,EAAMT,UAAW,EAAE,AAAC,EAClC,IAAI,CAAC+e,SAAS,CAAC9e,IAAI,CAACsf,IAExBA,EAAUvf,SAAS,CAAGof,EAEtB3e,EAAK+C,MAAM,CAACgJ,OAAO,CAAC,AAAChJ,IACbA,EAAO7G,OAAO,CAACge,eAAe,EAC9BkE,EAAIW,SAAS,CAAChc,EAAO7G,OAAO,CAEpC,GAEA8D,EAAKmJ,WAAW,CAACvK,EAAKC,GAEtBmf,GAA6Bhe,EAAM,UAAW,AAACjB,IACtCA,EAAEigB,UAAU,EACb,IAAI,CAAC9G,MAAM,CAAClY,EAEpB,EACJ,CAcAkY,OAAOuG,CAAW,CAAE,CAEhB,GAAI,CAACA,EAAa,CACd,IAAI,CAACH,SAAS,CAACvS,OAAO,CAAC,CAAC,CAAExM,UAAAA,CAAS,CAAE,IACjCA,EAAUwM,OAAO,CAAC,AAACkT,GAAmBA,IAC1C,GACA,IAAI,CAACX,SAAS,CAACnS,MAAM,CAAG,EACxB,MACJ,CACA,IAAMnM,EAAO,AAACye,aAAwBrjB,IAClCqjB,EACAA,EAAY1iB,KAAK,CAAC,EAAE,CACxB,IAAK,IAAIqQ,EAAI,IAAI,CAACkS,SAAS,CAACnS,MAAM,CAAG,EAAGC,GAAK,EAAGA,IACxC,IAAI,CAACkS,SAAS,CAAClS,EAAE,CAACpM,IAAI,GAAKA,IAC3B,IAAI,CAACse,SAAS,CAAClS,EAAE,CAAC7M,SAAS,CAACwM,OAAO,CAAC,AAACmT,GAAaA,KAClD,IAAI,CAACZ,SAAS,CAACa,MAAM,CAAC/S,EAAG,GAGrC,CAMArL,SAAU,CAEN,IAAI,CAACud,SAAS,CAACvS,OAAO,CAAC,CAAC,CAAExM,UAAAA,CAAS,CAAE,IACjCA,EAAUwM,OAAO,CAAC,AAACkT,GAAmBA,IAC1C,GACA,IAAI,CAACX,SAAS,CAACnS,MAAM,CAAG,EACxB,IAAI,CAACxQ,SAAS,CAACoF,OAAO,GACtB,IAAI,CAACpF,SAAS,CAAC6C,KAAK,CAACuC,OAAO,EAChC,CAiBAkQ,OAAOmO,CAAU,CAAErhB,CAAM,CAAE,CACvB,IAAI,CAACI,YAAY,CAAG4f,GAA0B,IAAI,CAAC5f,YAAY,CAAEihB,EAAW3iB,MAAM,EAAI,CAAE+B,MAAO,CAAE/B,OAAQ2iB,EAAW3iB,MAAM,AAAC,CAAE,EAAG2iB,EAAW5gB,KAAK,CAAE,CAAE7C,UAAWyjB,CAAW,GAC1K,IAAI,CAACzjB,SAAS,CAAC6C,KAAK,CAACyS,MAAM,CAAC,IAAI,CAAC9S,YAAY,CAAEJ,EACnD,CAMAA,QAAS,CACL,IAAI,CAACpC,SAAS,CAAC6C,KAAK,CAACT,MAAM,EAC/B,CASAghB,UAAUM,CAAa,CAAE,CACrB,IAAI,CAAC1jB,SAAS,CAAC6C,KAAK,CAACugB,SAAS,CAAChB,GAA0BsB,EAAe,CAAEnF,gBAAiBgE,GAAyBmB,EAAcnF,eAAe,CAAE,CAAA,EAAM,IACzJ,IAAI,CAACve,SAAS,CAACC,aAAa,EAChC,CAMA4iB,eAAgB,CACZ,IAAMJ,EAAM,IAAI,CAACziB,SAAS,AAC1ByiB,CAAAA,EAAIzhB,GAAG,CAAG,EACVyhB,EAAIriB,KAAK,CAACujB,QAAQ,GAClBlB,EAAIpiB,KAAK,CAACsjB,QAAQ,GAClBlB,EAAIriB,KAAK,CAAC4C,MAAM,GAChByf,EAAIpiB,KAAK,CAAC2C,MAAM,GAChByf,EAAIrb,MAAM,EAAEgJ,QAAQ,AAACkO,IACjBA,EAAEzY,SAAS,GACXyY,EAAEtb,MAAM,GACRsb,EAAElc,MAAM,EACZ,GACA,GAAM,CAAEa,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAG,IAAI,CAAC0gB,kBAAkB,EAC5CnB,CAAAA,EAAI5f,KAAK,CAACzC,KAAK,CAAC,EAAE,CAACkhB,OAAO,CAAGre,EAC7Bwf,EAAI5f,KAAK,CAACzC,KAAK,CAAC,EAAE,CAACmhB,OAAO,CAAGre,EAC7Buf,EAAIzf,MAAM,CAACC,EAAKC,EACpB,CAYA2gB,UAAW,CACP,GAAM,CAAE5gB,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAG,IAAI,CAAClD,SAAS,CAAC6C,KAAK,CAACzC,KAAK,CAAC,EAAE,CAAC2C,WAAW,GAAI,CAAEue,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAEte,IAAKmI,CAAO,CAAElI,IAAKiI,CAAO,CAAE,CAAG,IAAI,CAACnL,SAAS,CAACI,KAAK,CAAC2C,WAAW,GACrJ,MAAO,CACHE,IAAKsf,GAAyBtf,EAAKmI,GACnClI,IAAKqf,GAAyBrf,EAAKiI,GACnCC,QAAAA,EACAD,QAAAA,EACAmW,QAAAA,EACAC,QAAAA,CACJ,CACJ,CAiBA9T,SAASxK,CAAG,CAAEC,CAAG,CAAEd,CAAM,CAAEC,CAAS,CAAEuZ,CAAc,CAAE,CAClD0G,GAA8B,IAAI,CAACtiB,SAAS,CAAE,WAAY,CACtDiD,IAAAA,EACAC,IAAAA,EACAd,OAAAA,EACAC,UAAAA,EACAuZ,eAAgBwG,GAA0BxG,EAAgB,CAAEhX,QAAS,WAAY,EACrF,EACJ,CASAgf,oBAAqB,CACjB,GAAM,CAAE3gB,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAG,IAAI,CAAClD,SAAS,CAACI,KAAK,CAAC2C,WAAW,GACrD,MAAO,CACHE,IAAKA,EACLC,IAAKA,CACT,CACJ,CACJ,CAqBA,IAAM4gB,GAAKzkB,GACXykB,CAAAA,GAAEtB,mBAAmB,CAAGsB,GAAEtB,mBAAmB,EArBuBA,GAsBpEsB,GAAE9jB,SAAS,CAAG8jB,GAAEtB,mBAAmB,CAACxiB,SAAS,CAC7C0L,GAA+BnI,OAAO,CAACugB,GAAEC,KAAK,CAAED,GAAEE,IAAI,CAAEF,GAAEG,MAAM,EACnC,IAAM9kB,GAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}