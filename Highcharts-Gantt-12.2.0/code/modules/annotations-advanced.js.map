{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/annotations\n * @requires highcharts\n *\n * Annotations module\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"AST\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/annotations-advanced\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"Templating\"],amd1[\"AST\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/annotations-advanced\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"Templating\"], root[\"_Highcharts\"][\"AST\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"AST\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__660__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ annotations_advanced_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Annotations/AnnotationChart.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent, erase, find, fireEvent, pick, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add an annotation to the chart after render time.\n *\n * @sample highcharts/annotations/add-annotation/\n *         Add annotation\n *\n * @function Highcharts.Chart#addAnnotation\n *\n * @param  {Highcharts.AnnotationsOptions} options\n *         The annotation options for the new, detailed annotation.\n *\n * @param {boolean} [redraw]\n *\n * @return {Highcharts.Annotation}\n *         The newly generated annotation.\n */\nfunction chartAddAnnotation(userOptions, redraw) {\n    const annotation = this.initAnnotation(userOptions);\n    this.options.annotations.push(annotation.options);\n    if (pick(redraw, true)) {\n        annotation.redraw();\n        annotation.graphic.attr({\n            opacity: 1\n        });\n    }\n    return annotation;\n}\n/**\n * @private\n */\nfunction chartCallback() {\n    const chart = this;\n    chart.plotBoxClip = this.renderer.clipRect(this.plotBox);\n    chart.controlPointsGroup = chart.renderer\n        .g('control-points')\n        .attr({ zIndex: 99 })\n        .clip(chart.plotBoxClip)\n        .add();\n    chart.options.annotations.forEach((annotationOptions, i) => {\n        if (\n        // Verify that it has not been previously added in a responsive rule\n        !chart.annotations.some((annotation) => annotation.options === annotationOptions)) {\n            const annotation = chart.initAnnotation(annotationOptions);\n            chart.options.annotations[i] = annotation.options;\n        }\n    });\n    chart.drawAnnotations();\n    addEvent(chart, 'redraw', chart.drawAnnotations);\n    addEvent(chart, 'destroy', function () {\n        chart.plotBoxClip.destroy();\n        chart.controlPointsGroup.destroy();\n    });\n    addEvent(chart, 'exportData', function (event) {\n        const annotations = chart.annotations, csvColumnHeaderFormatter = ((this.options.exporting &&\n            this.options.exporting.csv) ||\n            {}).columnHeaderFormatter, \n        // If second row doesn't have xValues\n        // then it is a title row thus multiple level header is in use.\n        multiLevelHeaders = !event.dataRows[1].xValues, annotationHeader = (chart.options.lang &&\n            chart.options.lang.exportData &&\n            chart.options.lang.exportData.annotationHeader), columnHeaderFormatter = function (index) {\n            let s;\n            if (csvColumnHeaderFormatter) {\n                s = csvColumnHeaderFormatter(index);\n                if (s !== false) {\n                    return s;\n                }\n            }\n            s = annotationHeader + ' ' + index;\n            if (multiLevelHeaders) {\n                return {\n                    columnTitle: s,\n                    topLevelColumnTitle: s\n                };\n            }\n            return s;\n        }, startRowLength = event.dataRows[0].length, annotationSeparator = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.itemDelimiter), joinAnnotations = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.join);\n        annotations.forEach((annotation) => {\n            if (annotation.options.labelOptions &&\n                annotation.options.labelOptions.includeInDataExport) {\n                annotation.labels.forEach((label) => {\n                    if (label.options.text) {\n                        const annotationText = label.options.text;\n                        label.points.forEach((points) => {\n                            const annotationX = points.x, xAxisIndex = points.series.xAxis ?\n                                points.series.xAxis.index :\n                                -1;\n                            let wasAdded = false;\n                            // Annotation not connected to any xAxis -\n                            // add new row.\n                            if (xAxisIndex === -1) {\n                                const n = event.dataRows[0].length, newRow = new Array(n);\n                                for (let i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow.push(annotationText);\n                                newRow.xValues = [];\n                                newRow.xValues[xAxisIndex] = annotationX;\n                                event.dataRows.push(newRow);\n                                wasAdded = true;\n                            }\n                            // Annotation placed on a exported data point\n                            // - add new column\n                            if (!wasAdded) {\n                                event.dataRows.forEach((row) => {\n                                    if (!wasAdded &&\n                                        row.xValues &&\n                                        xAxisIndex !== void 0 &&\n                                        annotationX === row.xValues[xAxisIndex]) {\n                                        if (joinAnnotations &&\n                                            row.length > startRowLength) {\n                                            row[row.length - 1] += (annotationSeparator +\n                                                annotationText);\n                                        }\n                                        else {\n                                            row.push(annotationText);\n                                        }\n                                        wasAdded = true;\n                                    }\n                                });\n                            }\n                            // Annotation not placed on any exported data point,\n                            // but connected to the xAxis - add new row\n                            if (!wasAdded) {\n                                const n = event.dataRows[0].length, newRow = new Array(n);\n                                for (let i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow[0] = annotationX;\n                                newRow.push(annotationText);\n                                newRow.xValues = [];\n                                if (xAxisIndex !== void 0) {\n                                    newRow.xValues[xAxisIndex] = annotationX;\n                                }\n                                event.dataRows.push(newRow);\n                            }\n                        });\n                    }\n                });\n            }\n        });\n        let maxRowLen = 0;\n        event.dataRows.forEach((row) => {\n            maxRowLen = Math.max(maxRowLen, row.length);\n        });\n        const newRows = maxRowLen - event.dataRows[0].length;\n        for (let i = 0; i < newRows; i++) {\n            const header = columnHeaderFormatter(i + 1);\n            if (multiLevelHeaders) {\n                event.dataRows[0].push(header.topLevelColumnTitle);\n                event.dataRows[1].push(header.columnTitle);\n            }\n            else {\n                event.dataRows[0].push(header);\n            }\n        }\n    });\n}\n/**\n * @private\n */\nfunction chartDrawAnnotations() {\n    this.plotBoxClip.attr(this.plotBox);\n    this.annotations.forEach((annotation) => {\n        annotation.redraw();\n        annotation.graphic.animate({\n            opacity: 1\n        }, annotation.animationConfig);\n    });\n}\n/**\n * Remove an annotation from the chart.\n *\n * @function Highcharts.Chart#removeAnnotation\n *\n * @param {number|string|Highcharts.Annotation} idOrAnnotation\n *        The annotation's id or direct annotation object.\n */\nfunction chartRemoveAnnotation(idOrAnnotation) {\n    const annotations = this.annotations, annotation = (idOrAnnotation.coll === 'annotations') ?\n        idOrAnnotation :\n        find(annotations, function (annotation) {\n            return annotation.options.id === idOrAnnotation;\n        });\n    if (annotation) {\n        fireEvent(annotation, 'remove');\n        erase(this.options.annotations, annotation.options);\n        erase(annotations, annotation);\n        annotation.destroy();\n    }\n}\n/**\n * Create lookups initially\n * @private\n */\nfunction onChartAfterInit() {\n    const chart = this;\n    chart.annotations = [];\n    if (!this.options.annotations) {\n        this.options.annotations = [];\n    }\n}\n/**\n * @private\n */\nfunction wrapPointerOnContainerMouseDown(proceed) {\n    if (!this.chart.hasDraggedAnnotation) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\n/**\n * @private\n */\nvar AnnotationChart;\n(function (AnnotationChart) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(AnnotationClass, ChartClass, PointerClass) {\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.addAnnotation) {\n            const pointerProto = PointerClass.prototype;\n            addEvent(ChartClass, 'afterInit', onChartAfterInit);\n            chartProto.addAnnotation = chartAddAnnotation;\n            chartProto.callbacks.push(chartCallback);\n            chartProto.collectionsWithInit.annotations = [chartAddAnnotation];\n            chartProto.collectionsWithUpdate.push('annotations');\n            chartProto.drawAnnotations = chartDrawAnnotations;\n            chartProto.removeAnnotation = chartRemoveAnnotation;\n            chartProto.initAnnotation = function chartInitAnnotation(userOptions) {\n                const Constructor = (AnnotationClass.types[userOptions.type] ||\n                    AnnotationClass), annotation = new Constructor(this, userOptions);\n                this.annotations.push(annotation);\n                return annotation;\n            };\n            wrap(pointerProto, 'onContainerMouseDown', wrapPointerOnContainerMouseDown);\n        }\n    }\n    AnnotationChart.compose = compose;\n})(AnnotationChart || (AnnotationChart = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_AnnotationChart = (AnnotationChart);\n\n;// ./code/es-modules/Extensions/Annotations/AnnotationDefaults.js\n/* *\n *\n *  Imports\n *\n * */\n\nconst { defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A basic type of an annotation. It allows to add custom labels\n * or shapes. The items can be tied to points, axis coordinates\n * or chart pixel coordinates.\n *\n * @sample highcharts/annotations/basic/\n *         Basic annotations\n * @sample highcharts/demo/annotations/\n *         Advanced annotations\n * @sample highcharts/css/annotations\n *         Styled mode\n * @sample highcharts/annotations-advanced/controllable\n *         Controllable items\n * @sample {highstock} stock/annotations/fibonacci-retracements\n *         Custom annotation, Fibonacci retracement\n *\n * @type         {Array<*>}\n * @since        6.0.0\n * @requires     modules/annotations\n * @optionparent annotations\n */\nconst AnnotationDefaults = {\n    /**\n     * Sets an ID for an annotation. Can be user later when\n     * removing an annotation in [Chart#removeAnnotation(id)](\n     * /class-reference/Highcharts.Chart#removeAnnotation) method.\n     *\n     * @type      {number|string}\n     * @apioption annotations.id\n     */\n    /**\n     * Whether the annotation is visible.\n     *\n     * @sample highcharts/annotations/visible/\n     *         Set annotation visibility\n     */\n    visible: true,\n    /**\n     * Enable or disable the initial animation when a series is\n     * displayed for the `annotation`. The animation can also be set\n     * as a configuration object. Please note that this option only\n     * applies to the initial animation.\n     * For other animations, see [chart.animation](#chart.animation)\n     * and the animation parameter under the API methods.\n     * The following properties are supported:\n     *\n     * - `defer`: The animation delay time in milliseconds.\n     *\n     * @sample {highcharts} highcharts/annotations/defer/\n     *          Animation defer settings\n     * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n     * @since 8.2.0\n     */\n    animation: {},\n    /**\n     * Whether to hide the part of the annotation\n     * that is outside the plot area.\n     *\n     * @sample highcharts/annotations/label-crop-overflow/\n     *         Crop line annotation\n     * @type  {boolean}\n     * @since 9.3.0\n     */\n    crop: true,\n    /**\n     * The animation delay time in milliseconds.\n     * Set to `0` renders annotation immediately.\n     * As `undefined` inherits defer time from the [series.animation.defer](#plotOptions.series.animation.defer).\n     *\n     * @type      {number}\n     * @since 8.2.0\n     * @apioption annotations.animation.defer\n     */\n    /**\n     * Allow an annotation to be draggable by a user. Possible\n     * values are `'x'`, `'xy'`, `'y'` and `''` (disabled).\n     *\n     * @sample highcharts/annotations/draggable/\n     *         Annotations draggable: 'xy'\n     *\n     * @type {Highcharts.AnnotationDraggableValue}\n     */\n    draggable: 'xy',\n    /**\n     * Options for annotation's labels. Each label inherits options\n     * from the labelOptions object. An option from the labelOptions\n     * can be overwritten by config for a specific label.\n     *\n     * @requires modules/annotations\n     */\n    labelOptions: {\n        /**\n         * The alignment of the annotation's label. If right,\n         * the right side of the label should be touching the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'center',\n        /**\n         * Whether to allow the annotation's labels to overlap.\n         * To make the labels less sensitive for overlapping,\n         * the can be set to 0.\n         *\n         * @sample highcharts/annotations/tooltip-like/\n         *         Hide overlapping labels\n         */\n        allowOverlap: false,\n        /**\n         * The background color or gradient for the annotation's\n         * label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        backgroundColor: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The border color for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString}\n         */\n        borderColor: \"#000000\" /* Palette.neutralColor100 */,\n        /**\n         * The border radius in pixels for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderRadius: 3,\n        /**\n         * The border width in pixels for the annotation's label\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderWidth: 1,\n        /**\n         * A class name for styling by CSS.\n         *\n         * @sample highcharts/css/annotations\n         *         Styled mode annotations\n         *\n         * @since 6.0.5\n         */\n        className: 'highcharts-no-tooltip',\n        /**\n         * Whether to hide the annotation's label\n         * that is outside the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         */\n        crop: false,\n        /**\n         * The label's pixel distance from the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type      {number}\n         * @apioption annotations.labelOptions.distance\n         */\n        /**\n         * A\n         * [format](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * string for the data label.\n         *\n         * @see [plotOptions.series.dataLabels.format](plotOptions.series.dataLabels.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.format\n         */\n        /**\n         * Alias for the format option.\n         *\n         * @see [format](annotations.labelOptions.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.text\n         */\n        /**\n         * Callback JavaScript function to format the annotation's\n         * label. Note that if a `format` or `text` are defined,\n         * the format or text take precedence and the formatter is\n         * ignored. `This` refers to a point object.\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type    {Highcharts.FormatterCallbackFunction<Highcharts.Point>}\n         * @default function () { return defined(this.y) ? this.y : 'Annotation label'; }\n         */\n        formatter: function () {\n            return defined(this.y) ? '' + this.y : 'Annotation label';\n        },\n        /**\n         * Whether the annotation is visible in the exported data\n         * table.\n         *\n         * @sample highcharts/annotations/include-in-data-export/\n         *         Do not include in the data export\n         *\n         * @since 8.2.0\n         * @requires modules/export-data\n         */\n        includeInDataExport: true,\n        /**\n         * How to handle the annotation's label that flow outside\n         * the plot area. The justify option aligns the label inside\n         * the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         *\n         * @validvalue [\"allow\", \"justify\"]\n         */\n        overflow: 'justify',\n        /**\n         * When either the borderWidth or the backgroundColor is\n         * set, this is the padding within the box.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        padding: 5,\n        /**\n         * The shadow of the box. The shadow can be an object\n         * configuration containing `color`, `offsetX`, `offsetY`,\n         * `opacity` and `width`.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {boolean|Highcharts.ShadowOptionsObject}\n         */\n        shadow: false,\n        /**\n         * The name of a symbol to use for the border around the\n         * label. Symbols are predefined functions on the Renderer\n         * object.\n         *\n         * @sample highcharts/annotations/shapes/\n         *         Available shapes for labels\n         */\n        shape: 'callout',\n        /**\n         * Styles for the annotation's label.\n         *\n         * @see [plotOptions.series.dataLabels.style](plotOptions.series.dataLabels.style.html)\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @ignore */\n            fontSize: '0.7em',\n            /** @ignore */\n            fontWeight: 'normal',\n            /** @ignore */\n            color: 'contrast'\n        },\n        /**\n         * Whether to [use HTML](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting#html)\n         * to render the annotation's label.\n         */\n        useHTML: false,\n        /**\n         * The vertical alignment of the annotation's label.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'bottom',\n        /**\n         * The x position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        x: 0,\n        /**\n         * The y position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        y: -16\n    },\n    /**\n     * An array of labels for the annotation. For options that apply\n     * to multiple labels, they can be added to the\n     * [labelOptions](annotations.labelOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.labelOptions\n     * @apioption annotations.labels\n     */\n    /**\n     * This option defines the point to which the label will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-point/\n     *         Attach annotation to a mock point\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @requires  modules/annotations\n     * @apioption annotations.labels.point\n     */\n    /**\n     * An array of shapes for the annotation. For options that apply\n     * to multiple shapes, then can be added to the\n     * [shapeOptions](annotations.shapeOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.shapeOptions\n     * @apioption annotations.shapes\n     */\n    /**\n     * This option defines the point to which the shape will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @extends   annotations.labels.point\n     * @requires  modules/annotations\n     * @apioption annotations.shapes.point\n     */\n    /**\n     * An array of points for the shape\n     * or a callback function that returns that shape point.\n     *\n     * This option is available\n     * for shapes which can use multiple points such as path. A\n     * point can be either a point object or a point's id.\n     *\n     * @see [annotations.shapes.point](annotations.shapes.point.html)\n     *\n     * @type      {Array<Highcharts.AnnotationShapePointOptions>}\n     * @extends   annotations.labels.point\n     * @apioption annotations.shapes.points\n     */\n    /**\n     * The URL for an image to use as the annotation shape. Note,\n     * type has to be set to `'image'`.\n     *\n     * @see [annotations.shapes.type](annotations.shapes.type)\n     * @sample highcharts/annotations/shape-src/\n     *         Define a marker image url for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.src\n     */\n    /**\n     * Id of the marker which will be drawn at the final vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerEnd\n     */\n    /**\n     * Id of the marker which will be drawn at the first vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample {highcharts} highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerStart\n     */\n    /**\n     * Options for annotation's shapes. Each shape inherits options\n     * from the shapeOptions object. An option from the shapeOptions\n     * can be overwritten by config for a specific shape.\n     *\n     * @requires  modules/annotations\n     */\n    shapeOptions: {\n        /**\n         *\n         * The radius of the shape in y direction.\n         * Used for the ellipse.\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.ry\n         **/\n        /**\n         *\n         * The xAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.xAxis\n         **/\n        /**\n         * The yAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.yAxis\n         **/\n        /**\n         * The width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.width\n         **/\n        /**\n         * The height of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.height\n         */\n        /**\n         * The type of the shape.\n         * Available options are circle, rect and ellipse.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {string}\n         * @default   rect\n         * @apioption annotations.shapeOptions.type\n         */\n        /**\n         * The URL for an image to use as the annotation shape.\n         * Note, type has to be set to `'image'`.\n         *\n         * @see [annotations.shapeOptions.type](annotations.shapeOptions.type)\n         * @sample highcharts/annotations/shape-src/\n         *         Define a marker image url for annotations\n         *\n         * @type      {string}\n         * @apioption annotations.shapeOptions.src\n         */\n        /**\n         * Name of the dash style to use for the shape's stroke.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-dashstyle-all/\n         *         Possible values demonstrated\n         *\n         * @type      {Highcharts.DashStyleValue}\n         * @apioption annotations.shapeOptions.dashStyle\n         */\n        /**\n         * The color of the shape's stroke.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString}\n         */\n        stroke: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The pixel stroke width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        strokeWidth: 1,\n        /**\n         * The color of the shape's fill.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        fill: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The radius of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        r: 0,\n        /**\n         * Defines additional snapping area around an annotation\n         * making this annotation to focus. Defined in pixels.\n         */\n        snap: 2\n    },\n    /**\n     * Options for annotation's control points. Each control point\n     * inherits options from controlPointOptions object.\n     * Options from the controlPointOptions can be overwritten\n     * by options in a specific control point.\n     *\n     * @declare  Highcharts.AnnotationControlPointOptionsObject\n     * @requires modules/annotations\n     */\n    controlPointOptions: {\n        /**\n         * @type      {Highcharts.AnnotationControlPointPositionerFunction}\n         * @apioption annotations.controlPointOptions.positioner\n         */\n        /**\n         * @type {Highcharts.Dictionary<Function>}\n         */\n        events: {},\n        /**\n         * @type {Highcharts.SVGAttributes}\n         */\n        style: {\n            cursor: 'pointer',\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            stroke: \"#000000\" /* Palette.neutralColor100 */,\n            'stroke-width': 2\n        },\n        height: 10,\n        symbol: 'circle',\n        visible: false,\n        width: 10\n    },\n    /**\n     * Event callback when annotation is added to the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.add\n     */\n    /**\n     * Event callback when annotation is updated (e.g. drag and\n     * dropped or resized by control points).\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.afterUpdate\n     */\n    /**\n     * Fires when the annotation is clicked.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.click\n     */\n    /**\n     * Fires when the annotation is dragged.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @apioption annotations.events.drag\n     */\n    /**\n     * Event callback when annotation is removed from the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.remove\n     */\n    /**\n     * Events available in annotations.\n     *\n     * @requires modules/annotations\n     */\n    events: {},\n    /**\n     * The Z index of the annotation.\n     */\n    zIndex: 6\n}; // Type options are expected but not set\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_AnnotationDefaults = (AnnotationDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/EventEmitter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc, isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: EventEmitter_addEvent, fireEvent: EventEmitter_fireEvent, objectEach, pick: EventEmitter_pick, removeEvent } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nclass EventEmitter {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add emitter events.\n     * @private\n     */\n    addEvents() {\n        const emitter = this, addMouseDownEvent = function (element) {\n            EventEmitter_addEvent(element, isTouchDevice ? 'touchstart' : 'mousedown', (e) => {\n                emitter.onMouseDown(e);\n            }, { passive: false });\n        };\n        addMouseDownEvent(this.graphic.element);\n        (emitter.labels || []).forEach((label) => {\n            if (label.options.useHTML &&\n                label.graphic.text &&\n                !label.graphic.text.foreignObject) {\n                // Mousedown event bound to HTML element (#13070).\n                addMouseDownEvent(label.graphic.text.element);\n            }\n        });\n        objectEach(emitter.options.events, (event, type) => {\n            const eventHandler = function (e) {\n                if (type !== 'click' || !emitter.cancelClick) {\n                    event.call(emitter, emitter.chart.pointer?.normalize(e), emitter.target);\n                }\n            };\n            if ((emitter.nonDOMEvents || []).indexOf(type) === -1) {\n                EventEmitter_addEvent(emitter.graphic.element, type, eventHandler, { passive: false });\n                if (emitter.graphic.div) {\n                    EventEmitter_addEvent(emitter.graphic.div, type, eventHandler, { passive: false });\n                }\n            }\n            else {\n                EventEmitter_addEvent(emitter, type, eventHandler, { passive: false });\n            }\n        });\n        if (emitter.options.draggable) {\n            EventEmitter_addEvent(emitter, 'drag', emitter.onDrag);\n            if (!emitter.graphic.renderer.styledMode) {\n                const cssPointer = {\n                    cursor: {\n                        x: 'ew-resize',\n                        y: 'ns-resize',\n                        xy: 'move'\n                    }[emitter.options.draggable]\n                };\n                emitter.graphic.css(cssPointer);\n                (emitter.labels || []).forEach((label) => {\n                    if (label.options.useHTML &&\n                        label.graphic.text &&\n                        !label.graphic.text.foreignObject) {\n                        label.graphic.text.css(cssPointer);\n                    }\n                });\n            }\n        }\n        if (!emitter.isUpdating) {\n            EventEmitter_fireEvent(emitter, 'add');\n        }\n    }\n    /**\n     * Destroy the event emitter.\n     */\n    destroy() {\n        this.removeDocEvents();\n        removeEvent(this);\n        this.hcEvents = null;\n    }\n    /**\n     * Map mouse move event to the radians.\n     * @private\n     */\n    mouseMoveToRadians(e, cx, cy) {\n        let prevDy = e.prevChartY - cy, prevDx = e.prevChartX - cx, dy = e.chartY - cy, dx = e.chartX - cx, temp;\n        if (this.chart.inverted) {\n            temp = prevDx;\n            prevDx = prevDy;\n            prevDy = temp;\n            temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        return Math.atan2(dy, dx) - Math.atan2(prevDy, prevDx);\n    }\n    /**\n     * Map mouse move to the scale factors.\n     * @private\n     */\n    mouseMoveToScale(e, cx, cy) {\n        const prevDx = e.prevChartX - cx, prevDy = e.prevChartY - cy, dx = e.chartX - cx, dy = e.chartY - cy;\n        let sx = (dx || 1) / (prevDx || 1), sy = (dy || 1) / (prevDy || 1);\n        if (this.chart.inverted) {\n            const temp = sy;\n            sy = sx;\n            sx = temp;\n        }\n        return {\n            x: sx,\n            y: sy\n        };\n    }\n    /**\n     * Map mouse move event to the distance between two following events.\n     * @private\n     */\n    mouseMoveToTranslation(e) {\n        let dx = e.chartX - e.prevChartX, dy = e.chartY - e.prevChartY, temp;\n        if (this.chart.inverted) {\n            temp = dy;\n            dy = dx;\n            dx = temp;\n        }\n        return {\n            x: dx,\n            y: dy\n        };\n    }\n    /**\n     * Drag and drop event. All basic annotations should share this\n     * capability as well as the extended ones.\n     * @private\n     */\n    onDrag(e) {\n        if (this.chart.isInsidePlot(e.chartX - this.chart.plotLeft, e.chartY - this.chart.plotTop, {\n            visiblePlotOnly: true\n        })) {\n            const translation = this.mouseMoveToTranslation(e);\n            if (this.options.draggable === 'x') {\n                translation.y = 0;\n            }\n            if (this.options.draggable === 'y') {\n                translation.x = 0;\n            }\n            const emitter = this;\n            if (emitter.points.length) {\n                emitter.translate(translation.x, translation.y);\n            }\n            else {\n                emitter.shapes.forEach((shape) => shape.translate(translation.x, translation.y));\n                emitter.labels.forEach((label) => label.translate(translation.x, translation.y));\n            }\n            this.redraw(false);\n        }\n    }\n    /**\n     * Mouse down handler.\n     * @private\n     */\n    onMouseDown(e) {\n        if (e.preventDefault) {\n            e.preventDefault();\n        }\n        // On right click, do nothing:\n        if (e.button === 2) {\n            return;\n        }\n        const emitter = this, pointer = emitter.chart.pointer, \n        // Using experimental property on event object to check if event was\n        // created by touch on screen on hybrid device (#18122)\n        firesTouchEvents = (e?.sourceCapabilities?.firesTouchEvents) || false;\n        e = pointer?.normalize(e) || e;\n        let prevChartX = e.chartX, prevChartY = e.chartY;\n        emitter.cancelClick = false;\n        emitter.chart.hasDraggedAnnotation = true;\n        emitter.removeDrag = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchmove' : 'mousemove', function (e) {\n            emitter.hasDragged = true;\n            e = pointer?.normalize(e) || e;\n            e.prevChartX = prevChartX;\n            e.prevChartY = prevChartY;\n            EventEmitter_fireEvent(emitter, 'drag', e);\n            prevChartX = e.chartX;\n            prevChartY = e.chartY;\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n        emitter.removeMouseUp = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchend' : 'mouseup', function () {\n            // Sometimes the target is the annotation and sometimes its the\n            // controllable\n            const annotation = EventEmitter_pick(emitter.target && emitter.target.annotation, emitter.target);\n            if (annotation) {\n                // Keep annotation selected after dragging control point\n                annotation.cancelClick = emitter.hasDragged;\n            }\n            emitter.cancelClick = emitter.hasDragged;\n            emitter.chart.hasDraggedAnnotation = false;\n            if (emitter.hasDragged) {\n                // ControlPoints vs Annotation:\n                EventEmitter_fireEvent(EventEmitter_pick(annotation, // #15952\n                emitter), 'afterUpdate');\n            }\n            emitter.hasDragged = false;\n            emitter.onMouseUp();\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n    }\n    /**\n     * Mouse up handler.\n     */\n    onMouseUp() {\n        this.removeDocEvents();\n    }\n    /**\n     * Remove emitter document events.\n     * @private\n     */\n    removeDocEvents() {\n        if (this.removeDrag) {\n            this.removeDrag = this.removeDrag();\n        }\n        if (this.removeMouseUp) {\n            this.removeMouseUp = this.removeMouseUp();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_EventEmitter = (EventEmitter);\n\n;// ./code/es-modules/Extensions/Annotations/ControlPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge, pick: ControlPoint_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A control point class which is a connection between controllable\n * transform methods and a user actions.\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.AnnotationControlPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * A chart instance.\n *\n * @param {Highcharts.AnnotationControllable} target\n * A controllable instance which is a target for a control point.\n *\n * @param {Highcharts.AnnotationControlPointOptionsObject} options\n * An options object.\n *\n * @param {number} [index]\n * Point index.\n */\nclass ControlPoint extends Annotations_EventEmitter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, target, options, index) {\n        super();\n        /**\n         * List of events for `annotation.options.events` that should not be\n         * added to `annotation.graphic` but to the `annotation`.\n         * @private\n         * @name Highcharts.AnnotationControlPoint#nonDOMEvents\n         * @type {Array<string>}\n         */\n        this.nonDOMEvents = ['drag'];\n        this.chart = chart;\n        this.target = target;\n        this.options = options;\n        this.index = ControlPoint_pick(options.index, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy the control point.\n     * @private\n     */\n    destroy() {\n        super.destroy();\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        this.chart = null;\n        this.target = null;\n        this.options = null;\n    }\n    /**\n     * Redraw the control point.\n     * @private\n     * @param {boolean} [animation]\n     */\n    redraw(animation) {\n        this.graphic[animation ? 'animate' : 'attr'](this.options.positioner.call(this, this.target));\n    }\n    /**\n     * Render the control point.\n     * @private\n     */\n    render() {\n        const chart = this.chart, options = this.options;\n        this.graphic = chart.renderer\n            .symbol(options.symbol, 0, 0, options.width, options.height)\n            .add(chart.controlPointsGroup)\n            .css(options.style);\n        this.setVisibility(options.visible);\n        // `npm test -- --tests \"@highcharts/highcharts/annotations-advanced/*\"`\n        this.addEvents();\n    }\n    /**\n     * Set the visibility of the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#setVisibility\n     *\n     * @param {boolean} visible\n     * Visibility of the control point.\n     *\n     */\n    setVisibility(visible) {\n        this.graphic[visible ? 'show' : 'hide']();\n        this.options.visible = visible;\n    }\n    /**\n     * Update the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#update\n     *\n     * @param {Partial<Highcharts.AnnotationControlPointOptionsObject>} userOptions\n     * New options for the control point.\n     */\n    update(userOptions) {\n        const chart = this.chart, target = this.target, index = this.index, options = merge(true, this.options, userOptions);\n        this.destroy();\n        this.constructor(chart, target, options, index);\n        this.render(chart.controlPointsGroup);\n        this.redraw();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_ControlPoint = (ControlPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback to modify annotation's positioner controls.\n *\n * @callback Highcharts.AnnotationControlPointPositionerFunction\n * @param {Highcharts.AnnotationControlPoint} this\n * @param {Highcharts.AnnotationControllable} target\n * @return {Highcharts.PositionObject}\n */\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Extensions/Annotations/MockPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined: MockPoint_defined, fireEvent: MockPoint_fireEvent } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A trimmed point object which imitates {@link Highchart.Point} class. It is\n * created when there is a need of pointing to some chart's position using axis\n * values or pixel values\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationMockPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * The chart instance.\n *\n * @param {Highcharts.AnnotationControllable|null} target\n * The related controllable.\n *\n * @param {Highcharts.AnnotationMockPointOptionsObject|Function} options\n * The options object.\n */\nclass MockPoint {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Create a mock point from a real Highcharts point.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.Point} point\n     *\n     * @return {Highcharts.AnnotationMockPoint}\n     * A mock point instance.\n     */\n    static fromPoint(point) {\n        return new MockPoint(point.series.chart, null, {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        });\n    }\n    /**\n     * Get the pixel position from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @param {boolean} [paneCoordinates]\n     *        Whether the pixel position should be relative\n     *\n     * @return {Highcharts.PositionObject} pixel position\n     */\n    static pointToPixels(point, paneCoordinates) {\n        const series = point.series, chart = series.chart;\n        let x = point.plotX || 0, y = point.plotY || 0, plotBox;\n        if (chart.inverted) {\n            if (point.mock) {\n                x = point.plotY;\n                y = point.plotX;\n            }\n            else {\n                x = chart.plotWidth - (point.plotY || 0);\n                y = chart.plotHeight - (point.plotX || 0);\n            }\n        }\n        if (series && !paneCoordinates) {\n            plotBox = series.getPlotBox();\n            x += plotBox.translateX;\n            y += plotBox.translateY;\n        }\n        return {\n            x: x,\n            y: y\n        };\n    }\n    /**\n     * Get fresh mock point options from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * A mock point's options.\n     */\n    static pointToOptions(point) {\n        return {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        };\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, target, options) {\n        /* *\n         *\n         * Functions\n         *\n         * */\n        /**\n         * A flag indicating that a point is not the real one.\n         *\n         * @type {boolean}\n         * @default true\n         */\n        this.mock = true;\n        // Circular reference for formats and formatters\n        this.point = this;\n        /**\n         * A mock series instance imitating a real series from a real point.\n         *\n         * @name Annotation.AnnotationMockPoint#series\n         * @type {Highcharts.AnnotationMockSeries}\n         */\n        this.series = {\n            visible: true,\n            chart: chart,\n            getPlotBox: seriesProto.getPlotBox\n        };\n        /**\n         * @name Annotation.AnnotationMockPoint#target\n         * @type {Highcharts.AnnotationControllable|null}\n         */\n        this.target = target || null;\n        /**\n         * Options for the mock point.\n         *\n         * @name Annotation.AnnotationMockPoint#options\n         * @type {Highcharts.AnnotationsMockPointOptionsObject}\n         */\n        this.options = options;\n        /**\n         * If an xAxis is set it represents the point's value in terms of the\n         * xAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#x\n         * @type {number|undefined}\n         */\n        /**\n         * If an yAxis is set it represents the point's value in terms of the\n         * yAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#y\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel x coordinate relative to its plot\n         * box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotX\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel y position relative to its plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotY\n         * @type {number|undefined}\n         */\n        /**\n         * Whether the point is inside the plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#isInside\n         * @type {boolean|undefined}\n         */\n        this.applyOptions(this.getOptions());\n    }\n    /**\n     * Apply options for the point.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     */\n    applyOptions(options) {\n        this.command = options.command;\n        this.setAxis(options, 'x');\n        this.setAxis(options, 'y');\n        this.refresh();\n    }\n    /**\n     * Get the point's options.\n     * @private\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * The mock point's options.\n     */\n    getOptions() {\n        return this.hasDynamicOptions() ?\n            this.options(this.target) :\n            this.options;\n    }\n    /**\n     * Check if the point has dynamic options.\n     * @private\n     * @return {boolean}\n     * A positive flag if the point has dynamic options.\n     */\n    hasDynamicOptions() {\n        return typeof this.options === 'function';\n    }\n    /**\n     * Check if the point is inside its pane.\n     * @private\n     * @return {boolean} A flag indicating whether the point is inside the pane.\n     */\n    isInsidePlot() {\n        const plotX = this.plotX, plotY = this.plotY, xAxis = this.series.xAxis, yAxis = this.series.yAxis, e = {\n            x: plotX,\n            y: plotY,\n            isInsidePlot: true,\n            options: {}\n        };\n        if (xAxis) {\n            e.isInsidePlot = MockPoint_defined(plotX) && plotX >= 0 && plotX <= xAxis.len;\n        }\n        if (yAxis) {\n            e.isInsidePlot =\n                e.isInsidePlot &&\n                    MockPoint_defined(plotY) &&\n                    plotY >= 0 && plotY <= yAxis.len;\n        }\n        MockPoint_fireEvent(this.series.chart, 'afterIsInsidePlot', e);\n        return e.isInsidePlot;\n    }\n    /**\n     * Refresh point values and coordinates based on its options.\n     * @private\n     */\n    refresh() {\n        const series = this.series, xAxis = series.xAxis, yAxis = series.yAxis, options = this.getOptions();\n        if (xAxis) {\n            this.x = options.x;\n            this.plotX = xAxis.toPixels(options.x, true);\n        }\n        else {\n            this.x = void 0;\n            this.plotX = options.x;\n        }\n        if (yAxis) {\n            this.y = options.y;\n            this.plotY = yAxis.toPixels(options.y, true);\n        }\n        else {\n            this.y = null;\n            this.plotY = options.y;\n        }\n        this.isInside = this.isInsidePlot();\n    }\n    /**\n     * Refresh point options based on its plot coordinates.\n     * @private\n     */\n    refreshOptions() {\n        const series = this.series, xAxis = series.xAxis, yAxis = series.yAxis;\n        this.x = this.options.x = xAxis ?\n            this.options.x = xAxis.toValue(this.plotX, true) :\n            this.plotX;\n        this.y = this.options.y = yAxis ?\n            yAxis.toValue(this.plotY, true) :\n            this.plotY;\n    }\n    /**\n     * Rotate the point.\n     * @private\n     * @param {number} cx origin x rotation\n     * @param {number} cy origin y rotation\n     * @param {number} radians\n     */\n    rotate(cx, cy, radians) {\n        if (!this.hasDynamicOptions()) {\n            const cos = Math.cos(radians), sin = Math.sin(radians), x = this.plotX - cx, y = this.plotY - cy, tx = x * cos - y * sin, ty = x * sin + y * cos;\n            this.plotX = tx + cx;\n            this.plotY = ty + cy;\n            this.refreshOptions();\n        }\n    }\n    /**\n     * Scale the point.\n     *\n     * @private\n     *\n     * @param {number} cx\n     * Origin x transformation.\n     *\n     * @param {number} cy\n     * Origin y transformation.\n     *\n     * @param {number} sx\n     * Scale factor x.\n     *\n     * @param {number} sy\n     * Scale factor y.\n     */\n    scale(cx, cy, sx, sy) {\n        if (!this.hasDynamicOptions()) {\n            const x = this.plotX * sx, y = this.plotY * sy, tx = (1 - sx) * cx, ty = (1 - sy) * cy;\n            this.plotX = tx + x;\n            this.plotY = ty + y;\n            this.refreshOptions();\n        }\n    }\n    /**\n     * Set x or y axis.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     * @param {string} xOrY\n     * 'x' or 'y' string literal\n     */\n    setAxis(options, xOrY) {\n        const axisName = (xOrY + 'Axis'), axisOptions = options[axisName], chart = this.series.chart;\n        this.series[axisName] =\n            typeof axisOptions === 'object' ?\n                axisOptions :\n                MockPoint_defined(axisOptions) ?\n                    (chart[axisName][axisOptions] ||\n                        // @todo v--- (axisName)[axisOptions] ?\n                        chart.get(axisOptions)) :\n                    null;\n    }\n    /**\n     * Transform the mock point to an anchor (relative position on the chart).\n     * @private\n     * @return {Array<number>}\n     * A quadruple of numbers which denotes x, y, width and height of the box\n     **/\n    toAnchor() {\n        const anchor = [this.plotX, this.plotY, 0, 0];\n        if (this.series.chart.inverted) {\n            anchor[0] = this.plotY;\n            anchor[1] = this.plotX;\n        }\n        return anchor;\n    }\n    /**\n     * Translate the point.\n     *\n     * @private\n     *\n     * @param {number|undefined} cx\n     * Origin x transformation.\n     *\n     * @param {number|undefined} cy\n     * Origin y transformation.\n     *\n     * @param {number} dx\n     * Translation for x coordinate.\n     *\n     * @param {number} dy\n     * Translation for y coordinate.\n     **/\n    translate(_cx, _cy, dx, dy) {\n        if (!this.hasDynamicOptions()) {\n            this.plotX += dx;\n            this.plotY += dy;\n            this.refreshOptions();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_MockPoint = (MockPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.AnnotationMockLabelOptionsObject\n */ /**\n* Point instance of the point.\n* @name Highcharts.AnnotationMockLabelOptionsObject#point\n* @type {Highcharts.AnnotationMockPoint}\n*/ /**\n* X value translated to x axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#x\n* @type {number|null}\n*/ /**\n* Y value translated to y axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#y\n* @type {number|null}\n*/\n/**\n * Object of shape point.\n *\n * @interface Highcharts.AnnotationMockPointOptionsObject\n */ /**\n* The x position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.x\n*/ /**\n* The y position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.y\n*/ /**\n* This number defines which xAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the xAxis array. If the option is not configured or the axis\n* is not found the point's x coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.xAxis\n*/ /**\n* This number defines which yAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the yAxis array. If the option is not configured or the axis\n* is not found the point's y coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.yAxis\n*/\n/**\n * Callback function that returns the annotation shape point.\n *\n * @callback Highcharts.AnnotationMockPointFunction\n *\n * @param  {Highcharts.Annotation} annotation\n *         An annotation instance.\n *\n * @return {Highcharts.AnnotationMockPointOptionsObject}\n *         Annotations shape point.\n */\n/**\n * A mock series instance imitating a real series from a real point.\n * @private\n * @interface Highcharts.AnnotationMockSeries\n */ /**\n* Whether a series is visible.\n* @name Highcharts.AnnotationMockSeries#visible\n* @type {boolean}\n*/ /**\n* A chart instance.\n* @name Highcharts.AnnotationMockSeries#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationMockSeries#getPlotBox\n* @type {Function}\n*/\n/**\n * Indicates if this is a mock point for an annotation.\n * @name Highcharts.Point#mock\n * @type {boolean|undefined}\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/ControlTarget.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n/* *\n *\n *  Composition Namespace\n *\n * */\nvar ControlTarget;\n(function (ControlTarget) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add control points.\n     * @private\n     */\n    function addControlPoints() {\n        const controlPoints = this.controlPoints, controlPointsOptions = this.options.controlPoints || [];\n        controlPointsOptions.forEach((controlPointOptions, i) => {\n            const options = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(this.options.controlPointOptions, controlPointOptions);\n            if (!options.index) {\n                options.index = i;\n            }\n            controlPointsOptions[i] = options;\n            controlPoints.push(new Annotations_ControlPoint(this.chart, this, options));\n        });\n    }\n    /**\n     * Returns object which denotes anchor position - relative and absolute.\n     * @private\n     * @param {Highcharts.AnnotationPointType} point\n     * An annotation point.\n     *\n     * @return {Highcharts.AnnotationAnchorObject}\n     * An annotation anchor.\n     */\n    function anchor(point) {\n        const plotBox = point.series.getPlotBox(), chart = point.series.chart, box = point.mock ?\n            point.toAnchor() :\n            chart.tooltip &&\n                chart.tooltip.getAnchor.call({\n                    chart: point.series.chart\n                }, point) ||\n                [0, 0, 0, 0], anchor = {\n            x: box[0] + (this.options.x || 0),\n            y: box[1] + (this.options.y || 0),\n            height: box[2] || 0,\n            width: box[3] || 0\n        };\n        return {\n            relativePosition: anchor,\n            absolutePosition: highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(anchor, {\n                x: anchor.x + (point.mock ? plotBox.translateX : chart.plotLeft),\n                y: anchor.y + (point.mock ? plotBox.translateY : chart.plotTop)\n            })\n        };\n    }\n    /**\n     * Adds shared functions to be used with targets of ControlPoint.\n     * @private\n     */\n    function compose(ControlTargetClass) {\n        const controlProto = ControlTargetClass.prototype;\n        if (!controlProto.addControlPoints) {\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(true, controlProto, {\n                addControlPoints,\n                anchor,\n                destroyControlTarget,\n                getPointsOptions,\n                linkPoints,\n                point,\n                redrawControlPoints,\n                renderControlPoints,\n                transform,\n                transformPoint,\n                translate,\n                translatePoint\n            });\n        }\n    }\n    ControlTarget.compose = compose;\n    /**\n     * Destroy control points.\n     * @private\n     */\n    function destroyControlTarget() {\n        this.controlPoints.forEach((controlPoint) => controlPoint.destroy());\n        this.chart = null;\n        this.controlPoints = null;\n        this.points = null;\n        this.options = null;\n        if (this.annotation) {\n            this.annotation = null;\n        }\n    }\n    /**\n     * Get the points options.\n     * @private\n     * @return {Array<Highcharts.PointOptionsObject>}\n     * An array of points' options.\n     */\n    function getPointsOptions() {\n        const options = this.options;\n        return (options.points ||\n            (options.point && highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().splat(options.point)));\n    }\n    /**\n     * Find point-like objects based on points options.\n     * @private\n     * @return {Array<Annotation.PointLike>}\n     *         An array of point-like objects.\n     */\n    function linkPoints() {\n        const pointsOptions = this.getPointsOptions(), points = this.points, len = (pointsOptions && pointsOptions.length) || 0;\n        let i, point;\n        for (i = 0; i < len; i++) {\n            point = this.point(pointsOptions[i], points[i]);\n            if (!point) {\n                points.length = 0;\n                return;\n            }\n            if (point.mock) {\n                point.refresh();\n            }\n            points[i] = point;\n        }\n        return points;\n    }\n    /**\n     * Map point's options to a point-like object.\n     * @private\n     * @param {string|Function|Highcharts.AnnotationMockPointOptionsObject|Highcharts.AnnotationPointType} pointOptions\n     *        Point's options.\n     * @param {Highcharts.AnnotationPointType} point\n     *        A point-like instance.\n     * @return {Highcharts.AnnotationPointType|null}\n     *         If the point is found/set returns this point, otherwise null\n     */\n    function point(pointOptions, point) {\n        if (pointOptions && pointOptions.series) {\n            return pointOptions;\n        }\n        if (!point || point.series === null) {\n            if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(pointOptions)) {\n                point = new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n            else if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isString(pointOptions)) {\n                point = this.chart.get(pointOptions) || null;\n            }\n            else if (typeof pointOptions === 'function') {\n                const pointConfig = pointOptions.call(point, this);\n                point = pointConfig.series ?\n                    pointConfig :\n                    new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n        }\n        return point;\n    }\n    /**\n     * Redraw control points.\n     * @private\n     */\n    function redrawControlPoints(animation) {\n        this.controlPoints.forEach((controlPoint) => controlPoint.redraw(animation));\n    }\n    /**\n     * Render control points.\n     * @private\n     */\n    function renderControlPoints() {\n        this.controlPoints.forEach((controlPoint) => controlPoint.render());\n    }\n    /**\n     * Transform control points with a specific transformation.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number} [p2]\n     *        Param for the transformation\n     */\n    function transform(transformation, cx, cy, p1, p2) {\n        if (this.chart.inverted) {\n            const temp = cx;\n            cx = cy;\n            cy = temp;\n        }\n        this.points.forEach((_point, i) => (this.transformPoint(transformation, cx, cy, p1, p2, i)), this);\n    }\n    /**\n     * Transform a point with a specific transformation\n     * If a transformed point is a real point it is replaced with\n     * the mock point.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number|undefined} p2\n     *        Param for the transformation\n     * @param {number} i\n     *        Index of the point\n     */\n    function transformPoint(transformation, cx, cy, p1, p2, i) {\n        let point = this.points[i];\n        if (!point.mock) {\n            point = this.points[i] = Annotations_MockPoint.fromPoint(point);\n        }\n        point[transformation](cx, cy, p1, p2);\n    }\n    /**\n     * Translate control points.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     **/\n    function translate(dx, dy) {\n        this.transform('translate', null, null, dx, dy);\n    }\n    /**\n     * Translate a specific control point.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {number} i\n     *        Index of the point\n     **/\n    function translatePoint(dx, dy, i) {\n        this.transformPoint('translate', null, null, dx, dy, i);\n    }\n})(ControlTarget || (ControlTarget = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_ControlTarget = (ControlTarget);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/Controllable.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge: Controllable_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * It provides methods for handling points, control points\n * and points transformations.\n * @private\n */\nclass Controllable {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(annotation, options, index, itemType) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.collection = (itemType === 'label' ? 'labels' : 'shapes');\n        this.controlPoints = [];\n        this.options = options;\n        this.points = [];\n        this.index = index;\n        this.itemType = itemType;\n        this.init(annotation, options, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Redirect attr usage on the controllable graphic element.\n     * @private\n     */\n    attr(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    ..._args) {\n        this.graphic.attr.apply(this.graphic, arguments);\n    }\n    /**\n     * Utility function for mapping item's options\n     * to element's attribute\n     * @private\n     * @param {Highcharts.AnnotationsLabelsOptions|Highcharts.AnnotationsShapesOptions} options\n     * @return {Highcharts.SVGAttributes}\n     *         Mapped options.\n     */\n    attrsFromOptions(options) {\n        const map = this.constructor.attrsMap, attrs = {}, styledMode = this.chart.styledMode;\n        let key, mappedKey;\n        for (key in options) { // eslint-disable-line guard-for-in\n            mappedKey = map[key];\n            if (typeof map[key] !== 'undefined' &&\n                (!styledMode ||\n                    ['fill', 'stroke', 'stroke-width']\n                        .indexOf(mappedKey) === -1)) {\n                attrs[mappedKey] = options[key];\n            }\n        }\n        return attrs;\n    }\n    /**\n     * Destroy a controllable.\n     * @private\n     */\n    destroy() {\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        if (this.tracker) {\n            this.tracker = this.tracker.destroy();\n        }\n        this.destroyControlTarget();\n    }\n    /**\n     * Init the controllable\n     * @private\n     */\n    init(annotation, options, index) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.options = options;\n        this.points = [];\n        this.controlPoints = [];\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n    }\n    /**\n     * Redraw a controllable.\n     * @private\n     */\n    redraw(animation) {\n        this.redrawControlPoints(animation);\n    }\n    /**\n     * Render a controllable.\n     * @private\n     */\n    render(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _parentGroup) {\n        if (this.options.className && this.graphic) {\n            this.graphic.addClass(this.options.className);\n        }\n        this.renderControlPoints();\n    }\n    /**\n     * Rotate a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} radians\n     **/\n    rotate(cx, cy, radians) {\n        this.transform('rotate', cx, cy, radians);\n    }\n    /**\n     * Scale a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} sx\n     *        Scale factor x\n     * @param {number} sy\n     *        Scale factor y\n     */\n    scale(cx, cy, sx, sy) {\n        this.transform('scale', cx, cy, sx, sy);\n    }\n    /**\n     * Set control points' visibility.\n     * @private\n     */\n    setControlPointsVisibility(visible) {\n        this.controlPoints.forEach((controlPoint) => {\n            controlPoint.setVisibility(visible);\n        });\n    }\n    /**\n     * Check if a controllable should be rendered/redrawn.\n     * @private\n     * @return {boolean}\n     *         Whether a controllable should be drawn.\n     */\n    shouldBeDrawn() {\n        return !!this.points.length;\n    }\n    /**\n     * Translate shape within controllable item.\n     * Replaces `controllable.translate` method.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {boolean|undefined} translateSecondPoint\n     *        If the shape has two points attached to it, this option allows you\n     *        to translate also the second point.\n     */\n    translateShape(dx, dy, translateSecondPoint) {\n        const chart = this.annotation.chart, \n        // Annotation.options\n        shapeOptions = this.annotation.userOptions, \n        // Chart.options.annotations\n        annotationIndex = chart.annotations.indexOf(this.annotation), chartOptions = chart.options.annotations[annotationIndex];\n        this.translatePoint(dx, dy, 0);\n        if (translateSecondPoint) {\n            this.translatePoint(dx, dy, 1);\n        }\n        // Options stored in:\n        // - chart (for exporting)\n        // - current config (for redraws)\n        chartOptions[this.collection][this.index]\n            .point = this.options.point;\n        shapeOptions[this.collection][this.index]\n            .point = this.options.point;\n    }\n    /**\n     * Update a controllable.\n     * @private\n     */\n    update(newOptions) {\n        const annotation = this.annotation, options = Controllable_merge(true, this.options, newOptions), parentGroup = this.graphic.parentGroup, Constructor = this.constructor;\n        this.destroy();\n        const newControllable = new Constructor(annotation, options, this.index, this.itemType);\n        Controllable_merge(true, this, newControllable);\n        this.render(parentGroup);\n        this.redraw();\n    }\n}\nAnnotations_ControlTarget.compose(Controllable);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_Controllable = (Controllable);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * An object which denotes a controllable's anchor positions - relative and\n * absolute.\n *\n * @private\n * @interface Highcharts.AnnotationAnchorObject\n */ /**\n* Relative to the plot area position\n* @name Highcharts.AnnotationAnchorObject#relativePosition\n* @type {Highcharts.BBoxObject}\n*/ /**\n* Absolute position\n* @name Highcharts.AnnotationAnchorObject#absolutePosition\n* @type {Highcharts.BBoxObject}\n*/\n/**\n * @interface Highcharts.AnnotationControllable\n */ /**\n* @name Highcharts.AnnotationControllable#annotation\n* @type {Highcharts.Annotation}\n*/ /**\n* @name Highcharts.AnnotationControllable#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationControllable#collection\n* @type {string}\n*/ /**\n* @private\n* @name Highcharts.AnnotationControllable#controlPoints\n* @type {Array<Highcharts.AnnotationControlPoint>}\n*/ /**\n* @name Highcharts.AnnotationControllable#points\n* @type {Array<Highcharts.Point>}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableDefaults.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/**\n * Options for configuring markers for annotations.\n *\n * An example of the arrow marker:\n * <pre>\n * {\n *   arrow: {\n *     id: 'arrow',\n *     tagName: 'marker',\n *     refY: 5,\n *     refX: 5,\n *     markerWidth: 10,\n *     markerHeight: 10,\n *     children: [{\n *       tagName: 'path',\n *       attrs: {\n *         d: 'M 0 0 L 10 5 L 0 10 Z',\n *         'stroke-width': 0\n *       }\n *     }]\n *   }\n * }\n * </pre>\n *\n * @sample highcharts/annotations/custom-markers/\n *         Define a custom marker for annotations\n *\n * @sample highcharts/css/annotations-markers/\n *         Define markers in a styled mode\n *\n * @type         {Highcharts.Dictionary<Highcharts.ASTNode>}\n * @since        6.0.0\n * @optionparent defs\n */\nconst defaultMarkers = {\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    arrow: {\n        tagName: 'marker',\n        attributes: {\n            id: 'arrow',\n            refY: 5,\n            refX: 9,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        /**\n         * @type {Array<Highcharts.DefsOptions>}\n         */\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    d: 'M 0 0 L 10 5 L 0 10 Z', // Triangle (used as an arrow)\n                    'stroke-width': 0\n                }\n            }]\n    },\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    'reverse-arrow': {\n        tagName: 'marker',\n        attributes: {\n            id: 'reverse-arrow',\n            refY: 5,\n            refX: 1,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    // Reverse triangle (used as an arrow)\n                    d: 'M 0 5 L 10 0 L 10 10 Z',\n                    'stroke-width': 0\n                }\n            }]\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ControllableDefaults = {\n    defaultMarkers\n};\n/* harmony default export */ const Controllables_ControllableDefaults = (ControllableDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllablePath.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defaultMarkers: ControllablePath_defaultMarkers } = Controllables_ControllableDefaults;\n\n\nconst { addEvent: ControllablePath_addEvent, defined: ControllablePath_defined, extend, merge: ControllablePath_merge, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\nconst markerEndSetter = createMarkerSetter('marker-end');\nconst markerStartSetter = createMarkerSetter('marker-start');\n// See TRACKER_FILL in highcharts.js\nconst TRACKER_FILL = 'rgba(192,192,192,' + ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).svg ? 0.0001 : 0.002) + ')';\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction createMarkerSetter(markerType) {\n    return function (value) {\n        this.attr(markerType, 'url(#' + value + ')');\n    };\n}\n/**\n * @private\n */\nfunction onChartAfterGetContainer() {\n    this.options.defs = ControllablePath_merge(ControllablePath_defaultMarkers, this.options.defs || {});\n    ///  objectEach(this.options.defs, function (def): void {\n    //     const attributes = def.attributes;\n    //     if (\n    //         def.tagName === 'marker' &&\n    //         attributes &&\n    //         attributes.id &&\n    //         attributes.display !== 'none'\n    //     ) {\n    //         this.renderer.addMarker(attributes.id, def);\n    //     }\n    // }, this);\n}\n/**\n * @private\n */\nfunction svgRendererAddMarker(id, markerOptions) {\n    const options = { attributes: { id } };\n    const attrs = {\n        stroke: markerOptions.color || 'none',\n        fill: markerOptions.color || 'rgba(0, 0, 0, 0.75)'\n    };\n    options.children = (markerOptions.children &&\n        markerOptions.children.map(function (child) {\n            return ControllablePath_merge(attrs, child);\n        }));\n    const ast = ControllablePath_merge(true, {\n        attributes: {\n            markerWidth: 20,\n            markerHeight: 20,\n            refX: 0,\n            refY: 0,\n            orient: 'auto'\n        }\n    }, markerOptions, options);\n    const marker = this.definition(ast);\n    marker.id = id;\n    return marker;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable path class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllablePath\n *\n * @param {Highcharts.Annotation}\n * Related annotation.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A path's options object.\n *\n * @param {number} index\n * Index of the path.\n */\nclass ControllablePath extends Controllables_Controllable {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass, SVGRendererClass) {\n        const svgRendererProto = SVGRendererClass.prototype;\n        if (!svgRendererProto.addMarker) {\n            ControllablePath_addEvent(ChartClass, 'afterGetContainer', onChartAfterGetContainer);\n            svgRendererProto.addMarker = svgRendererAddMarker;\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'path';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Map the controllable path to 'd' path attribute.\n     *\n     * @return {Highcharts.SVGPathArray|null}\n     * A path's d attribute.\n     */\n    toD() {\n        const dOption = this.options.d;\n        if (dOption) {\n            return typeof dOption === 'function' ?\n                dOption.call(this) :\n                dOption;\n        }\n        const points = this.points, len = points.length, d = [];\n        let showPath = len, point = points[0], position = showPath && this.anchor(point).absolutePosition, pointIndex = 0, command;\n        if (position) {\n            d.push(['M', position.x, position.y]);\n            while (++pointIndex < len && showPath) {\n                point = points[pointIndex];\n                command = point.command || 'L';\n                position = this.anchor(point).absolutePosition;\n                if (command === 'M') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'L') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'Z') {\n                    d.push([command]);\n                }\n                showPath = point.series.visible;\n            }\n        }\n        return (showPath && this.graphic ?\n            this.chart.renderer.crispLine(d, this.graphic.strokeWidth()) :\n            null);\n    }\n    shouldBeDrawn() {\n        return super.shouldBeDrawn() || !!this.options.d;\n    }\n    render(parent) {\n        const options = this.options, attrs = this.attrsFromOptions(options);\n        this.graphic = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .attr(attrs)\n            .add(parent);\n        this.tracker = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .addClass('highcharts-tracker-line')\n            .attr({\n            zIndex: 2\n        })\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            this.tracker.attr({\n                'stroke-linejoin': 'round', // #1225\n                stroke: TRACKER_FILL,\n                fill: TRACKER_FILL,\n                'stroke-width': this.graphic.strokeWidth() +\n                    options.snap * 2\n            });\n        }\n        super.render();\n        extend(this.graphic, { markerStartSetter, markerEndSetter });\n        this.setMarkers(this);\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const d = this.toD(), action = animation ? 'animate' : 'attr';\n            if (d) {\n                this.graphic[action]({ d: d });\n                this.tracker[action]({ d: d });\n            }\n            else {\n                this.graphic.attr({ d: 'M 0 ' + -9e9 });\n                this.tracker.attr({ d: 'M 0 ' + -9e9 });\n            }\n            this.graphic.placed = this.tracker.placed = !!d;\n        }\n        super.redraw(animation);\n    }\n    /**\n     * Set markers.\n     * @private\n     * @param {Highcharts.AnnotationControllablePath} item\n     */\n    setMarkers(item) {\n        const itemOptions = item.options, chart = item.chart, defs = chart.options.defs, fill = itemOptions.fill, color = ControllablePath_defined(fill) && fill !== 'none' ?\n            fill :\n            itemOptions.stroke;\n        const setMarker = function (markerType) {\n            const markerId = itemOptions[markerType];\n            let def, predefinedMarker, key, marker;\n            if (markerId) {\n                for (key in defs) { // eslint-disable-line guard-for-in\n                    def = defs[key];\n                    if ((markerId === (def.attributes && def.attributes.id) ||\n                        // Legacy, for\n                        // unit-tests/annotations/annotations-shapes\n                        markerId === def.id) &&\n                        def.tagName === 'marker') {\n                        predefinedMarker = def;\n                        break;\n                    }\n                }\n                if (predefinedMarker) {\n                    marker = item[markerType] = chart.renderer\n                        .addMarker((itemOptions.id || uniqueKey()) + '-' + markerId, ControllablePath_merge(predefinedMarker, { color: color }));\n                    item.attr(markerType, marker.getAttribute('id'));\n                }\n            }\n        };\n        ['markerStart', 'markerEnd']\n            .forEach(setMarker);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @name Highcharts.AnnotationControllablePath.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllablePath.attrsMap = {\n    dashStyle: 'dashstyle',\n    strokeWidth: 'stroke-width',\n    stroke: 'stroke',\n    fill: 'fill',\n    zIndex: 'zIndex'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllablePath = (ControllablePath);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableRect.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableRect_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable rect class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableRect\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A rect's options.\n *\n * @param {number} index\n * Index of the rectangle\n */\nclass ControllableRect extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'rect';\n        this.translate = super.translateShape;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .rect(0, -9e9, 0, 0)\n            .attr(attrs)\n            .add(parent);\n        super.render();\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    width: this.options.width,\n                    height: this.options.height\n                });\n            }\n            else {\n                this.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @type {Annotation.ControllableRect.AttrsMap}\n */\nControllableRect.attrsMap = ControllableRect_merge(Controllables_ControllablePath.attrsMap, {\n    width: 'width',\n    height: 'height'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableRect = (ControllableRect);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableCircle.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableCircle_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable circle class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableCircle\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the circle\n */\nclass ControllableCircle extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'circle';\n        this.translate = super.translateShape;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    r: this.options.r\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = !!position;\n        }\n        super.redraw.call(this, animation);\n    }\n    /**\n     * @private\n     */\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .circle(0, -9e9, 0)\n            .attr(attrs)\n            .add(parent);\n        super.render();\n    }\n    /**\n     * Set the radius.\n     * @private\n     * @param {number} r\n     *        A radius to be set\n     */\n    setRadius(r) {\n        this.options.r = r;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element\n * attributes.\n *\n * @name Highcharts.AnnotationControllableCircle.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableCircle.attrsMap = ControllableCircle_merge(Controllables_ControllablePath.attrsMap, { r: 'r' });\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableCircle = (ControllableCircle);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableEllipse.js\n/* *\n *\n * Author: Pawel Lysy\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableEllipse_merge, defined: ControllableEllipse_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable ellipse class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableEllipse\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the Ellipse\n */\nclass ControllableEllipse extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'ellipse';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    init(annotation, options, index) {\n        if (ControllableEllipse_defined(options.yAxis)) {\n            options.points.forEach((point) => {\n                point.yAxis = options.yAxis;\n            });\n        }\n        if (ControllableEllipse_defined(options.xAxis)) {\n            options.points.forEach((point) => {\n                point.xAxis = options.xAxis;\n            });\n        }\n        super.init(annotation, options, index);\n    }\n    /**\n     * Render the element\n     * @private\n     * @param parent\n     *        Parent SVG element.\n     */\n    render(parent) {\n        this.graphic = this.annotation.chart.renderer.createElement('ellipse')\n            .attr(this.attrsFromOptions(this.options))\n            .add(parent);\n        super.render();\n    }\n    /**\n     * Translate the points. Mostly used to handle dragging of the ellipse.\n     * @private\n     */\n    translate(dx, dy) {\n        super.translateShape(dx, dy, true);\n    }\n    /**\n     * Get the distance from the line to the point.\n     * @private\n     * @param point1\n     *        First point which is on the line\n     * @param point2\n     *        Second point\n     * @param x0\n     *        Point's x value from which you want to calculate the distance from\n     * @param y0\n     *        Point's y value from which you want to calculate the distance from\n     */\n    getDistanceFromLine(point1, point2, x0, y0) {\n        return Math.abs((point2.y - point1.y) * x0 - (point2.x - point1.x) * y0 +\n            point2.x * point1.y - point2.y * point1.x) / Math.sqrt((point2.y - point1.y) * (point2.y - point1.y) +\n            (point2.x - point1.x) * (point2.x - point1.x));\n    }\n    /**\n     * The function calculates the svg attributes of the ellipse, and returns\n     * all parameters necessary to draw the ellipse.\n     * @private\n     * @param position\n     *        Absolute position of the first point in points array\n     * @param position2\n     *        Absolute position of the second point in points array\n     */\n    getAttrs(position, position2) {\n        const x1 = position.x, y1 = position.y, x2 = position2.x, y2 = position2.y, cx = (x1 + x2) / 2, cy = (y1 + y2) / 2, rx = Math.sqrt((x1 - x2) * (x1 - x2) / 4 + (y1 - y2) * (y1 - y2) / 4), tan = (y2 - y1) / (x2 - x1);\n        let angle = Math.atan(tan) * 180 / Math.PI;\n        if (cx < x1) {\n            angle += 180;\n        }\n        const ry = this.getRY();\n        return { cx, cy, rx, ry, angle };\n    }\n    /**\n     * Get the value of minor radius of the ellipse.\n     * @private\n     */\n    getRY() {\n        const yAxis = this.getYAxis();\n        return ControllableEllipse_defined(yAxis) ?\n            Math.abs(yAxis.toPixels(this.options.ry) - yAxis.toPixels(0)) :\n            this.options.ry;\n    }\n    /**\n     * Get the yAxis object to which the ellipse is pinned.\n     * @private\n     */\n    getYAxis() {\n        const yAxisIndex = this.options.yAxis;\n        return this.chart.yAxis[yAxisIndex];\n    }\n    /**\n     * Get the absolute coordinates of the MockPoint\n     * @private\n     * @param point\n     *        MockPoint that is added through options\n     */\n    getAbsolutePosition(point) {\n        return this.anchor(point).absolutePosition;\n    }\n    /**\n     * Redraw the element\n     * @private\n     * @param animation\n     *        Display an animation\n     */\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.getAbsolutePosition(this.points[0]), position2 = this.getAbsolutePosition(this.points[1]), attrs = this.getAttrs(position, position2);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    cx: attrs.cx,\n                    cy: attrs.cy,\n                    rx: attrs.rx,\n                    ry: attrs.ry,\n                    rotation: attrs.angle,\n                    rotationOriginX: attrs.cx,\n                    rotationOriginY: attrs.cy\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n    /**\n     * Set the radius Y.\n     * @private\n     * @param {number} ry\n     *        A radius in y direction to be set\n     */\n    setYRadius(ry) {\n        const shapes = this.annotation.userOptions.shapes;\n        this.options.ry = ry;\n        if (shapes && shapes[0]) {\n            shapes[0].ry = ry;\n            shapes[0].ry = ry;\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element\n * attributes.\n *\n * @name Highcharts.AnnotationControllableEllipse.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableEllipse.attrsMap = ControllableEllipse_merge(Controllables_ControllablePath.attrsMap, {\n    ry: 'ry'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableEllipse = (ControllableEllipse);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableLabel.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\n\nconst { extend: ControllableLabel_extend, getAlignFactor, isNumber, pick: ControllableLabel_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * General symbol definition for labels with connector\n * @private\n */\nfunction symbolConnector(x, y, w, h, options) {\n    const anchorX = options && options.anchorX, anchorY = options && options.anchorY;\n    let path, yOffset, lateral = w / 2;\n    if (isNumber(anchorX) && isNumber(anchorY)) {\n        path = [['M', anchorX, anchorY]];\n        // Prefer 45 deg connectors\n        yOffset = y - anchorY;\n        if (yOffset < 0) {\n            yOffset = -h - yOffset;\n        }\n        if (yOffset < w) {\n            lateral = anchorX < x + (w / 2) ? yOffset : w - yOffset;\n        }\n        // Anchor below label\n        if (anchorY > y + h) {\n            path.push(['L', x + lateral, y + h]);\n            // Anchor above label\n        }\n        else if (anchorY < y) {\n            path.push(['L', x + lateral, y]);\n            // Anchor left of label\n        }\n        else if (anchorX < x) {\n            path.push(['L', x, y + h / 2]);\n            // Anchor right of label\n        }\n        else if (anchorX > x + w) {\n            path.push(['L', x + w, y + h / 2]);\n        }\n    }\n    return path || [];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable label class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableLabel\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n * @param {Highcharts.AnnotationsLabelOptions} options\n * A label's options.\n * @param {number} index\n * Index of the label.\n */\nclass ControllableLabel extends Controllables_Controllable {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns new aligned position based alignment options and box to align to.\n     * It is almost a one-to-one copy from SVGElement.prototype.align\n     * except it does not use and mutate an element\n     *\n     * @param {Highcharts.AnnotationAlignObject} alignOptions\n     *\n     * @param {Highcharts.BBoxObject} box\n     *\n     * @return {Highcharts.PositionObject}\n     * Aligned position.\n     */\n    static alignedPosition(alignOptions, box) {\n        return {\n            x: Math.round((box.x || 0) + (alignOptions.x || 0) +\n                (box.width - (alignOptions.width || 0)) *\n                    getAlignFactor(alignOptions.align)),\n            y: Math.round((box.y || 0) + (alignOptions.y || 0) +\n                (box.height - (alignOptions.height || 0)) *\n                    getAlignFactor(alignOptions.verticalAlign))\n        };\n    }\n    static compose(SVGRendererClass) {\n        const symbols = SVGRendererClass.prototype.symbols;\n        symbols.connector = symbolConnector;\n    }\n    /**\n     * Returns new alignment options for a label if the label is outside the\n     * plot area. It is almost a one-to-one copy from\n     * Series.prototype.justifyDataLabel except it does not mutate the label and\n     * it works with absolute instead of relative position.\n     */\n    static justifiedOptions(chart, label, alignOptions, alignAttr) {\n        const align = alignOptions.align, verticalAlign = alignOptions.verticalAlign, padding = label.box ? 0 : (label.padding || 0), bBox = label.getBBox(), \n        //\n        options = {\n            align: align,\n            verticalAlign: verticalAlign,\n            x: alignOptions.x,\n            y: alignOptions.y,\n            width: label.width,\n            height: label.height\n        }, \n        //\n        x = (alignAttr.x || 0) - chart.plotLeft, y = (alignAttr.y || 0) - chart.plotTop;\n        let off;\n        // Off left\n        off = x + padding;\n        if (off < 0) {\n            if (align === 'right') {\n                options.align = 'left';\n            }\n            else {\n                options.x = (options.x || 0) - off;\n            }\n        }\n        // Off right\n        off = x + bBox.width - padding;\n        if (off > chart.plotWidth) {\n            if (align === 'left') {\n                options.align = 'right';\n            }\n            else {\n                options.x = (options.x || 0) + chart.plotWidth - off;\n            }\n        }\n        // Off top\n        off = y + padding;\n        if (off < 0) {\n            if (verticalAlign === 'bottom') {\n                options.verticalAlign = 'top';\n            }\n            else {\n                options.y = (options.y || 0) - off;\n            }\n        }\n        // Off bottom\n        off = y + bBox.height - padding;\n        if (off > chart.plotHeight) {\n            if (verticalAlign === 'top') {\n                options.verticalAlign = 'bottom';\n            }\n            else {\n                options.y = (options.y || 0) + chart.plotHeight - off;\n            }\n        }\n        return options;\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'label');\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Translate the point of the label by deltaX and deltaY translations.\n     * The point is the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    translatePoint(dx, dy) {\n        super.translatePoint(dx, dy, 0);\n    }\n    /**\n     * Translate x and y position relative to the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    translate(dx, dy) {\n        const chart = this.annotation.chart, \n        // Annotation.options\n        labelOptions = this.annotation.userOptions, \n        // Chart.options.annotations\n        annotationIndex = chart.annotations.indexOf(this.annotation), chartAnnotations = chart.options.annotations, chartOptions = chartAnnotations[annotationIndex];\n        if (chart.inverted) {\n            const temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        // Local options:\n        this.options.x += dx;\n        this.options.y += dy;\n        // Options stored in chart:\n        chartOptions[this.collection][this.index].x = this.options.x;\n        chartOptions[this.collection][this.index].y = this.options.y;\n        labelOptions[this.collection][this.index].x = this.options.x;\n        labelOptions[this.collection][this.index].y = this.options.y;\n    }\n    render(parent) {\n        const options = this.options, attrs = this.attrsFromOptions(options), style = options.style;\n        this.graphic = this.annotation.chart.renderer\n            .label('', 0, -9999, // #10055\n        options.shape, null, null, options.useHTML, null, 'annotation-label')\n            .attr(attrs)\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            if (style.color === 'contrast') {\n                style.color = this.annotation.chart.renderer.getContrast(ControllableLabel.shapesWithoutBackground.indexOf(options.shape) > -1 ? '#FFFFFF' : options.backgroundColor);\n            }\n            this.graphic\n                .css(options.style)\n                .shadow(options.shadow);\n        }\n        this.graphic.labelrank = options.labelrank;\n        super.render();\n    }\n    redraw(animation) {\n        const options = this.options, text = this.text || options.format || options.text, label = this.graphic, point = this.points[0];\n        if (!label) {\n            this.redraw(animation);\n            return;\n        }\n        label.attr({\n            text: text ?\n                format(String(text), point, this.annotation.chart) :\n                options.formatter.call(point, this)\n        });\n        const anchor = this.anchor(point);\n        const attrs = this.position(anchor);\n        if (attrs) {\n            label.alignAttr = attrs;\n            attrs.anchorX = anchor.absolutePosition.x;\n            attrs.anchorY = anchor.absolutePosition.y;\n            label[animation ? 'animate' : 'attr'](attrs);\n        }\n        else {\n            label.attr({\n                x: 0,\n                y: -9999 // #10055\n            });\n        }\n        label.placed = !!attrs;\n        super.redraw(animation);\n    }\n    /**\n     * All basic shapes don't support alignTo() method except label.\n     * For a controllable label, we need to subtract translation from\n     * options.\n     */\n    anchor(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _point) {\n        const anchor = super.anchor.apply(this, arguments), x = this.options.x || 0, y = this.options.y || 0;\n        anchor.absolutePosition.x -= x;\n        anchor.absolutePosition.y -= y;\n        anchor.relativePosition.x -= x;\n        anchor.relativePosition.y -= y;\n        return anchor;\n    }\n    /**\n     * Returns the label position relative to its anchor.\n     */\n    position(anchor) {\n        const item = this.graphic, chart = this.annotation.chart, tooltip = chart.tooltip, point = this.points[0], itemOptions = this.options, anchorAbsolutePosition = anchor.absolutePosition, anchorRelativePosition = anchor.relativePosition;\n        let itemPosition, alignTo, itemPosRelativeX, itemPosRelativeY, showItem = point.series.visible &&\n            Annotations_MockPoint.prototype.isInsidePlot.call(point);\n        if (item && showItem) {\n            const { width = 0, height = 0 } = item;\n            if (itemOptions.distance && tooltip) {\n                itemPosition = tooltip.getPosition.call({\n                    chart,\n                    distance: ControllableLabel_pick(itemOptions.distance, 16),\n                    getPlayingField: tooltip.getPlayingField,\n                    pointer: tooltip.pointer\n                }, width, height, {\n                    plotX: anchorRelativePosition.x,\n                    plotY: anchorRelativePosition.y,\n                    negative: point.negative,\n                    ttBelow: point.ttBelow,\n                    h: (anchorRelativePosition.height ||\n                        anchorRelativePosition.width)\n                });\n            }\n            else if (itemOptions.positioner) {\n                itemPosition = itemOptions.positioner.call(this);\n            }\n            else {\n                alignTo = {\n                    x: anchorAbsolutePosition.x,\n                    y: anchorAbsolutePosition.y,\n                    width: 0,\n                    height: 0\n                };\n                itemPosition = ControllableLabel.alignedPosition(ControllableLabel_extend(itemOptions, {\n                    width,\n                    height\n                }), alignTo);\n                if (this.options.overflow === 'justify') {\n                    itemPosition = ControllableLabel.alignedPosition(ControllableLabel.justifiedOptions(chart, item, itemOptions, itemPosition), alignTo);\n                }\n            }\n            if (itemOptions.crop) {\n                itemPosRelativeX = itemPosition.x - chart.plotLeft;\n                itemPosRelativeY = itemPosition.y - chart.plotTop;\n                showItem =\n                    chart.isInsidePlot(itemPosRelativeX, itemPosRelativeY) &&\n                        chart.isInsidePlot(itemPosRelativeX + width, itemPosRelativeY + height);\n            }\n        }\n        return showItem ? itemPosition : null;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @type {Highcharts.Dictionary<string>}\n */\nControllableLabel.attrsMap = {\n    backgroundColor: 'fill',\n    borderColor: 'stroke',\n    borderWidth: 'stroke-width',\n    zIndex: 'zIndex',\n    borderRadius: 'r',\n    padding: 'padding'\n};\n/**\n * Shapes which do not have background - the object is used for proper\n * setting of the contrast color.\n *\n * @type {Array<string>}\n */\nControllableLabel.shapesWithoutBackground = ['connector'];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableLabel = (ControllableLabel);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableImage.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable image class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableImage\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A controllable's options.\n *\n * @param {number} index\n * Index of the image.\n */\nclass ControllableImage extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'image';\n        this.translate = super.translateShape;\n    }\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options), options = this.options;\n        this.graphic = this.annotation.chart.renderer\n            .image(options.src, 0, -9e9, options.width, options.height)\n            .attr(attrs)\n            .add(parent);\n        this.graphic.width = options.width;\n        this.graphic.height = options.height;\n        super.render();\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const anchor = this.anchor(this.points[0]), position = Controllables_ControllableLabel.prototype.position.call(this, anchor);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @name Highcharts.AnnotationControllableImage.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableImage.attrsMap = {\n    width: 'width',\n    height: 'height',\n    zIndex: 'zIndex'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableImage = (ControllableImage);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n;// ./code/es-modules/Shared/BaseForm.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\nconst { addEvent: BaseForm_addEvent, createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass BaseForm {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(parentDiv, iconsURL) {\n        this.iconsURL = iconsURL;\n        this.container = this.createPopupContainer(parentDiv);\n        this.closeButton = this.addCloseButton();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create popup div container.\n     *\n     * @param {HTMLElement} parentDiv\n     * Parent div to attach popup.\n     *\n     * @param  {string} className\n     * Class name of the popup.\n     *\n     * @return {HTMLElement}\n     * Popup div.\n     */\n    createPopupContainer(parentDiv, className = 'highcharts-popup highcharts-no-tooltip') {\n        return createElement('div', { className }, void 0, parentDiv);\n    }\n    /**\n     * Create HTML element and attach click event to close popup.\n     *\n     * @param {string} className\n     * Class name of the close button.\n     *\n     * @return {HTMLElement}\n     * Close button.\n     */\n    addCloseButton(className = 'highcharts-popup-close') {\n        const popup = this, iconsURL = this.iconsURL;\n        // Create close popup button.\n        const closeButton = createElement('button', { className }, void 0, this.container);\n        closeButton.style['background-image'] = 'url(' +\n            (iconsURL.match(/png|svg|jpeg|jpg|gif/ig) ?\n                iconsURL : iconsURL + 'close.svg') + ')';\n        ['click', 'touchstart'].forEach((eventName) => {\n            BaseForm_addEvent(closeButton, eventName, popup.closeButtonEvents.bind(popup));\n        });\n        // Close popup when press ESC\n        BaseForm_addEvent(document, 'keydown', function (event) {\n            if (event.code === 'Escape') {\n                popup.closeButtonEvents();\n            }\n        });\n        return closeButton;\n    }\n    /**\n     * Close button events.\n     * @return {void}\n     */\n    closeButtonEvents() {\n        this.closePopup();\n    }\n    /**\n     * Reset content of the current popup and show.\n     *\n     * @param {string} toolbarClass\n     * Class name of the toolbar which styles should be reset.\n     */\n    showPopup(toolbarClass = 'highcharts-annotation-toolbar') {\n        const popupDiv = this.container, popupCloseButton = this.closeButton;\n        this.type = void 0;\n        // Reset content.\n        popupDiv.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n        // Reset toolbar styles if exists.\n        if (popupDiv.className.indexOf(toolbarClass) >= 0) {\n            popupDiv.classList.remove(toolbarClass);\n            // Reset toolbar inline styles\n            popupDiv.removeAttribute('style');\n        }\n        // Add close button.\n        popupDiv.appendChild(popupCloseButton);\n        popupDiv.style.display = 'block';\n        popupDiv.style.height = '';\n    }\n    /**\n     * Hide popup.\n     */\n    closePopup() {\n        this.container.style.display = 'none';\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Shared_BaseForm = (BaseForm);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupAnnotations.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc: PopupAnnotations_doc, isFirefox } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { createElement: PopupAnnotations_createElement, isArray, isObject, objectEach: PopupAnnotations_objectEach, pick: PopupAnnotations_pick, stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create annotation simple form.\n * It contains fields with param names.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Object} options\n * Options\n * @param {Function} callback\n * On click callback\n * @param {boolean} [isInit]\n * If it is a form declared for init annotation\n */\nfunction addForm(chart, options, callback, isInit) {\n    if (!chart) {\n        return;\n    }\n    const popupDiv = this.container, lang = this.lang;\n    // Create title of annotations\n    let lhsCol = PopupAnnotations_createElement('h2', {\n        className: 'highcharts-popup-main-title'\n    }, void 0, popupDiv);\n    lhsCol.appendChild(PopupAnnotations_doc.createTextNode(lang[options.langKey] || options.langKey || ''));\n    // Left column\n    lhsCol = PopupAnnotations_createElement('div', {\n        className: ('highcharts-popup-lhs-col highcharts-popup-lhs-full')\n    }, void 0, popupDiv);\n    const bottomRow = PopupAnnotations_createElement('div', {\n        className: 'highcharts-popup-bottom-row'\n    }, void 0, popupDiv);\n    addFormFields.call(this, lhsCol, chart, '', options, [], true);\n    this.addButton(bottomRow, isInit ?\n        (lang.addButton || 'Add') :\n        (lang.saveButton || 'Save'), isInit ? 'add' : 'save', popupDiv, callback);\n}\n/**\n * Create annotation simple form. It contains two buttons\n * (edit / remove) and text label.\n * @private\n * @param {Highcharts.Chart} - chart\n * @param {Highcharts.AnnotationsOptions} - options\n * @param {Function} - on click callback\n */\nfunction addToolbar(chart, options, callback) {\n    const lang = this.lang, popupDiv = this.container, showForm = this.showForm, toolbarClass = 'highcharts-annotation-toolbar';\n    // Set small size\n    if (popupDiv.className.indexOf(toolbarClass) === -1) {\n        popupDiv.className += ' ' + toolbarClass + ' highcharts-no-mousewheel';\n    }\n    // Set position\n    if (chart) {\n        popupDiv.style.top = chart.plotTop + 10 + 'px';\n    }\n    // Create label\n    const label = PopupAnnotations_createElement('p', {\n        className: 'highcharts-annotation-label'\n    }, void 0, popupDiv);\n    label.setAttribute('aria-label', 'Annotation type');\n    label.appendChild(PopupAnnotations_doc.createTextNode(PopupAnnotations_pick(\n    // Advanced annotations:\n    lang[options.langKey] || options.langKey, \n    // Basic shapes:\n    options.shapes && options.shapes[0].type, '')));\n    // Add buttons\n    let button = this.addButton(popupDiv, lang.editButton || 'Edit', 'edit', popupDiv, () => {\n        showForm.call(this, 'annotation-edit', chart, options, callback);\n    });\n    button.className += ' highcharts-annotation-edit-button';\n    button.style['background-image'] = 'url(' +\n        this.iconsURL + 'edit.svg)';\n    button = this.addButton(popupDiv, lang.removeButton || 'Remove', 'remove', popupDiv, callback);\n    button.className += ' highcharts-annotation-remove-button';\n    button.style['background-image'] = 'url(' +\n        this.iconsURL + 'destroy.svg)';\n}\n/**\n * Create annotation's form fields.\n * @private\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Div where inputs are placed\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.AnnotationsOptions} options\n * Options\n * @param {Array<unknown>} storage\n * Array where all items are stored\n * @param {boolean} [isRoot]\n * Recursive flag for root\n */\nfunction addFormFields(parentDiv, chart, parentNode, options, storage, isRoot) {\n    if (!chart) {\n        return;\n    }\n    const addInput = this.addInput, lang = this.lang;\n    let parentFullName, titleName;\n    PopupAnnotations_objectEach(options, (value, option) => {\n        // Create name like params.styles.fontSize\n        parentFullName = parentNode !== '' ? parentNode + '.' + option : option;\n        if (isObject(value)) {\n            if (\n            // Value is object of options\n            !isArray(value) ||\n                // Array of objects with params. i.e labels in Fibonacci\n                (isArray(value) && isObject(value[0]))) {\n                titleName = lang[option] || option;\n                if (!titleName.match(/\\d/g)) {\n                    storage.push([\n                        true,\n                        titleName,\n                        parentDiv\n                    ]);\n                }\n                addFormFields.call(this, parentDiv, chart, parentFullName, value, storage, false);\n            }\n            else {\n                storage.push([\n                    this,\n                    parentFullName,\n                    'annotation',\n                    parentDiv,\n                    value\n                ]);\n            }\n        }\n    });\n    if (isRoot) {\n        stableSort(storage, (a) => (a[1].match(/format/g) ? -1 : 1));\n        if (isFirefox) {\n            storage.reverse(); // (#14691)\n        }\n        storage.forEach((genInput) => {\n            if (genInput[0] === true) {\n                PopupAnnotations_createElement('span', {\n                    className: 'highcharts-annotation-title'\n                }, void 0, genInput[2]).appendChild(PopupAnnotations_doc.createTextNode(genInput[1]));\n            }\n            else {\n                genInput[4] = {\n                    value: genInput[4][0],\n                    type: genInput[4][1]\n                };\n                addInput.apply(genInput[0], genInput.splice(1));\n            }\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupAnnotations = {\n    addForm,\n    addToolbar\n};\n/* harmony default export */ const Popup_PopupAnnotations = (PopupAnnotations);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupIndicators.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { doc: PopupIndicators_doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { seriesTypes } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent: PopupIndicators_addEvent, createElement: PopupIndicators_createElement, defined: PopupIndicators_defined, isArray: PopupIndicators_isArray, isObject: PopupIndicators_isObject, objectEach: PopupIndicators_objectEach, stableSort: PopupIndicators_stableSort } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Enums\n *\n * */\n/**\n * Enum for properties which should have dropdown list.\n * @private\n */\nvar DropdownProperties;\n(function (DropdownProperties) {\n    DropdownProperties[DropdownProperties[\"params.algorithm\"] = 0] = \"params.algorithm\";\n    DropdownProperties[DropdownProperties[\"params.average\"] = 1] = \"params.average\";\n})(DropdownProperties || (DropdownProperties = {}));\n/**\n * List of available algorithms for the specific indicator.\n * @private\n */\nconst dropdownParameters = {\n    'algorithm-pivotpoints': ['standard', 'fibonacci', 'camarilla'],\n    'average-disparityindex': ['sma', 'ema', 'dema', 'tema', 'wma']\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create two columns (divs) in HTML.\n * @private\n * @param {Highcharts.HTMLDOMElement} container\n * Container of columns\n * @return {Highcharts.Dictionary<Highcharts.HTMLDOMElement>}\n * Reference to two HTML columns (lhsCol, rhsCol)\n */\nfunction addColsContainer(container) {\n    // Left column\n    const lhsCol = PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-lhs-col'\n    }, void 0, container);\n    // Right column\n    const rhsCol = PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col'\n    }, void 0, container);\n    // Wrapper content\n    PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col-wrapper'\n    }, void 0, rhsCol);\n    return {\n        lhsCol: lhsCol,\n        rhsCol: rhsCol\n    };\n}\n/**\n * Create indicator's form. It contains two tabs (ADD and EDIT) with\n * content.\n * @private\n */\nfunction PopupIndicators_addForm(chart, _options, callback) {\n    const lang = this.lang;\n    let buttonParentDiv;\n    if (!chart) {\n        return;\n    }\n    // Add tabs\n    this.tabs.init.call(this, chart);\n    // Get all tabs content divs\n    const tabsContainers = this.container\n        .querySelectorAll('.highcharts-tab-item-content');\n    // ADD tab\n    addColsContainer(tabsContainers[0]);\n    addSearchBox.call(this, chart, tabsContainers[0]);\n    addIndicatorList.call(this, chart, tabsContainers[0], 'add');\n    buttonParentDiv = tabsContainers[0]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.addButton || 'add', 'add', buttonParentDiv, callback);\n    // EDIT tab\n    addColsContainer(tabsContainers[1]);\n    addIndicatorList.call(this, chart, tabsContainers[1], 'edit');\n    buttonParentDiv = tabsContainers[1]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.saveButton || 'save', 'edit', buttonParentDiv, callback);\n    this.addButton(buttonParentDiv, lang.removeButton || 'remove', 'remove', buttonParentDiv, callback);\n}\n/**\n * Create typical inputs for chosen indicator. Fields are extracted from\n * defaultOptions (ADD mode) or current indicator (ADD mode). Two extra\n * fields are added:\n * - hidden input - contains indicator type (required for callback)\n * - select - list of series which can be linked with indicator\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Highcharts.Series} series\n * Indicator\n * @param {string} seriesType\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} rhsColWrapper\n * Element where created HTML list is added\n */\nfunction PopupIndicators_addFormFields(chart, series, seriesType, rhsColWrapper) {\n    const fields = series.params || series.options.params;\n    // Reset current content\n    rhsColWrapper.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n    // Create title (indicator name in the right column)\n    PopupIndicators_createElement('h3', {\n        className: 'highcharts-indicator-title'\n    }, void 0, rhsColWrapper).appendChild(PopupIndicators_doc.createTextNode(getNameType(series, seriesType).indicatorFullName));\n    // Input type\n    PopupIndicators_createElement('input', {\n        type: 'hidden',\n        name: 'highcharts-type-' + seriesType,\n        value: seriesType\n    }, void 0, rhsColWrapper);\n    // List all series with id\n    listAllSeries.call(this, seriesType, 'series', chart, rhsColWrapper, series, series.linkedParent && series.linkedParent.options.id);\n    if (fields.volumeSeriesID) {\n        listAllSeries.call(this, seriesType, 'volume', chart, rhsColWrapper, series, series.linkedParent && fields.volumeSeriesID);\n    }\n    // Add param fields\n    addParamInputs.call(this, chart, 'params', fields, seriesType, rhsColWrapper);\n}\n/**\n * Create HTML list of all indicators (ADD mode) or added indicators\n * (EDIT mode).\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string} listType\n *        Type of list depending on the selected bookmark.\n *        Might be 'add' or 'edit'.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n */\nfunction addIndicatorList(chart, parentDiv, listType, filter) {\n    /**\n     *\n     */\n    function selectIndicator(series, indicatorType) {\n        const button = rhsColWrapper.parentNode\n            .children[1];\n        PopupIndicators_addFormFields.call(popup, chart, series, indicatorType, rhsColWrapper);\n        if (button) {\n            button.style.display = 'block';\n        }\n        // Add hidden input with series.id\n        if (isEdit && series.options) {\n            PopupIndicators_createElement('input', {\n                type: 'hidden',\n                name: 'highcharts-id-' + indicatorType,\n                value: series.options.id\n            }, void 0, rhsColWrapper).setAttribute('highcharts-data-series-id', series.options.id);\n        }\n    }\n    const popup = this, lang = popup.lang, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], rhsCol = parentDiv.querySelectorAll('.highcharts-popup-rhs-col')[0], isEdit = listType === 'edit', series = (isEdit ?\n        chart.series : // EDIT mode\n        chart.options.plotOptions || {} // ADD mode\n    );\n    if (!chart && series) {\n        return;\n    }\n    let item, filteredSeriesArray = [];\n    // Filter and sort the series.\n    if (!isEdit && !PopupIndicators_isArray(series)) {\n        // Apply filters only for the 'add' indicator list.\n        filteredSeriesArray = filterSeries.call(this, series, filter);\n    }\n    else if (PopupIndicators_isArray(series)) {\n        filteredSeriesArray = filterSeriesArray.call(this, series);\n    }\n    // Sort indicators alphabetically.\n    PopupIndicators_stableSort(filteredSeriesArray, (a, b) => {\n        const seriesAName = a.indicatorFullName.toLowerCase(), seriesBName = b.indicatorFullName.toLowerCase();\n        return (seriesAName < seriesBName) ?\n            -1 : (seriesAName > seriesBName) ? 1 : 0;\n    });\n    // If the list exists remove it from the DOM\n    // in order to create a new one with different filters.\n    if (lhsCol.children[1]) {\n        lhsCol.children[1].remove();\n    }\n    // Create wrapper for list.\n    const indicatorList = PopupIndicators_createElement('ul', {\n        className: 'highcharts-indicator-list'\n    }, void 0, lhsCol);\n    const rhsColWrapper = rhsCol.querySelectorAll('.highcharts-popup-rhs-col-wrapper')[0];\n    filteredSeriesArray.forEach((seriesSet) => {\n        const { indicatorFullName, indicatorType, series } = seriesSet;\n        item = PopupIndicators_createElement('li', {\n            className: 'highcharts-indicator-list'\n        }, void 0, indicatorList);\n        const btn = PopupIndicators_createElement('button', {\n            className: 'highcharts-indicator-list-item',\n            textContent: indicatorFullName\n        }, void 0, item);\n        ['click', 'touchstart'].forEach((eventName) => {\n            PopupIndicators_addEvent(btn, eventName, function () {\n                selectIndicator(series, indicatorType);\n            });\n        });\n    });\n    // Select first item from the list\n    if (filteredSeriesArray.length > 0) {\n        const { series, indicatorType } = filteredSeriesArray[0];\n        selectIndicator(series, indicatorType);\n    }\n    else if (!isEdit) {\n        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(rhsColWrapper.parentNode.children[0], lang.noFilterMatch || '');\n        rhsColWrapper.parentNode.children[1]\n            .style.display = 'none';\n    }\n}\n/**\n * Recurrent function which lists all fields, from params object and\n * create them as inputs. Each input has unique `data-name` attribute,\n * which keeps chain of fields i.e params.styles.fontSize.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.PopupFieldsDictionary<string>} fields\n * Params which are based for input create\n * @param {string} type\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Element where created HTML list is added\n */\nfunction addParamInputs(chart, parentNode, fields, type, parentDiv) {\n    if (!chart) {\n        return;\n    }\n    const addInput = this.addInput;\n    PopupIndicators_objectEach(fields, (value, fieldName) => {\n        // Create name like params.styles.fontSize\n        const parentFullName = parentNode + '.' + fieldName;\n        if (PopupIndicators_defined(value) && // Skip if field is unnecessary, #15362\n            parentFullName) {\n            if (PopupIndicators_isObject(value)) {\n                // (15733) 'Periods' has an arrayed value. Label must be\n                // created here.\n                addInput.call(this, parentFullName, type, parentDiv, {});\n                addParamInputs.call(this, chart, parentFullName, value, type, parentDiv);\n            }\n            // If the option is listed in dropdown enum,\n            // add the selection box for it.\n            if (parentFullName in DropdownProperties) {\n                // Add selection boxes.\n                const selectBox = addSelection.call(this, type, parentFullName, parentDiv);\n                // Add possible dropdown options.\n                addSelectionOptions.call(this, chart, parentNode, selectBox, type, fieldName, value);\n            }\n            else if (\n            // Skip volume field which is created by addFormFields.\n            parentFullName !== 'params.volumeSeriesID' &&\n                !PopupIndicators_isArray(value) // Skip params declared in array.\n            ) {\n                addInput.call(this, parentFullName, type, parentDiv, {\n                    value: value,\n                    type: 'number'\n                } // All inputs are text type\n                );\n            }\n        }\n    });\n}\n/**\n * Add searchbox HTML element and its' label.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} parentDiv\n *        HTML parent element.\n */\nfunction addSearchBox(chart, parentDiv) {\n    const popup = this, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], options = 'searchIndicators', inputAttributes = {\n        value: '',\n        type: 'text',\n        htmlFor: 'search-indicators',\n        labelClassName: 'highcharts-input-search-indicators-label'\n    }, clearFilterText = this.lang.clearFilter, inputWrapper = PopupIndicators_createElement('div', {\n        className: 'highcharts-input-wrapper'\n    }, void 0, lhsCol);\n    const handleInputChange = function (inputText) {\n        // Apply some filters.\n        addIndicatorList.call(popup, chart, popup.container, 'add', inputText);\n    };\n    // Add input field with the label and button.\n    const input = this.addInput(options, 'input', inputWrapper, inputAttributes), button = PopupIndicators_createElement('a', {\n        textContent: clearFilterText\n    }, void 0, inputWrapper);\n    input.classList.add('highcharts-input-search-indicators');\n    button.classList.add('clear-filter-button');\n    // Add input change events.\n    PopupIndicators_addEvent(input, 'input', function () {\n        handleInputChange(this.value);\n        // Show clear filter button.\n        if (this.value.length) {\n            button.style.display = 'inline-block';\n        }\n        else {\n            button.style.display = 'none';\n        }\n    });\n    // Add clear filter click event.\n    ['click', 'touchstart'].forEach((eventName) => {\n        PopupIndicators_addEvent(button, eventName, function () {\n            // Clear the input.\n            input.value = '';\n            handleInputChange('');\n            // Hide clear filter button- no longer necessary.\n            button.style.display = 'none';\n        });\n    });\n}\n/**\n * Add selection HTML element and its' label.\n *\n * @private\n *\n * @param {string} indicatorType\n * Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n * Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n * HTML parent element.\n */\nfunction addSelection(indicatorType, optionName, parentDiv) {\n    const optionParamList = optionName.split('.'), labelText = optionParamList[optionParamList.length - 1], selectName = 'highcharts-' + optionName + '-type-' + indicatorType, lang = this.lang;\n    // Add a label for the selection box.\n    PopupIndicators_createElement('label', {\n        htmlFor: selectName\n    }, null, parentDiv).appendChild(PopupIndicators_doc.createTextNode(lang[labelText] || optionName));\n    // Create a selection box.\n    const selectBox = PopupIndicators_createElement('select', {\n        name: selectName,\n        className: 'highcharts-popup-field',\n        id: 'highcharts-select-' + optionName\n    }, null, parentDiv);\n    selectBox.setAttribute('id', 'highcharts-select-' + optionName);\n    return selectBox;\n}\n/**\n * Get and add selection options.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLSelectElement} [selectBox]\n *        HTML select box element to which the options are being added.\n *\n * @param {string|undefined} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string|undefined} parameterName\n *        Name of the parameter which should be applied.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction addSelectionOptions(chart, optionName, selectBox, indicatorType, parameterName, selectedOption, currentSeries) {\n    // Get and apply selection options for the possible series.\n    if (optionName === 'series' || optionName === 'volume') {\n        // List all series which have id - mandatory for indicator.\n        chart.series.forEach((series) => {\n            const seriesOptions = series.options, seriesName = seriesOptions.name ||\n                seriesOptions.params ?\n                series.name :\n                seriesOptions.id || '';\n            if (seriesOptions.id !== 'highcharts-navigator-series' &&\n                seriesOptions.id !== (currentSeries &&\n                    currentSeries.options &&\n                    currentSeries.options.id)) {\n                if (!PopupIndicators_defined(selectedOption) &&\n                    optionName === 'volume' &&\n                    series.type === 'column') {\n                    selectedOption = seriesOptions.id;\n                }\n                PopupIndicators_createElement('option', {\n                    value: seriesOptions.id\n                }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(seriesName));\n            }\n        });\n    }\n    else if (indicatorType && parameterName) {\n        // Get and apply options for the possible parameters.\n        const dropdownKey = parameterName + '-' + indicatorType, parameterOption = dropdownParameters[dropdownKey];\n        parameterOption.forEach((element) => {\n            PopupIndicators_createElement('option', {\n                value: element\n            }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(element));\n        });\n    }\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/**\n * Filter object of series which are not indicators.\n * If the filter string exists, check against it.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series are available in the plotOptions.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeries(series, filter) {\n    const popup = this, lang = popup.chart && popup.chart.options.lang, indicatorAliases = lang &&\n        lang.navigation &&\n        lang.navigation.popup &&\n        lang.navigation.popup.indicatorAliases, filteredSeriesArray = [];\n    let filteredSeries;\n    PopupIndicators_objectEach(series, (series, value) => {\n        const seriesOptions = series && series.options;\n        // Allow only indicators.\n        if (series.params || seriesOptions &&\n            seriesOptions.params) {\n            const { indicatorFullName, indicatorType } = getNameType(series, value);\n            if (filter) {\n                // Replace invalid characters.\n                const validFilter = filter.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n                const regex = new RegExp(validFilter, 'i'), alias = indicatorAliases &&\n                    indicatorAliases[indicatorType] &&\n                    indicatorAliases[indicatorType].join(' ') || '';\n                if (indicatorFullName.match(regex) ||\n                    alias.match(regex)) {\n                    filteredSeries = {\n                        indicatorFullName,\n                        indicatorType,\n                        series: series\n                    };\n                    filteredSeriesArray.push(filteredSeries);\n                }\n            }\n            else {\n                filteredSeries = {\n                    indicatorFullName,\n                    indicatorType,\n                    series: series\n                };\n                filteredSeriesArray.push(filteredSeries);\n            }\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Filter an array of series and map its names and types.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series that are available in the plotOptions.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeriesArray(series) {\n    const filteredSeriesArray = [];\n    // Allow only indicators.\n    series.forEach((series) => {\n        if (series.is('sma')) {\n            filteredSeriesArray.push({\n                indicatorFullName: series.name,\n                indicatorType: series.type,\n                series: series\n            });\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Get amount of indicators added to chart.\n * @private\n * @return {number} - Amount of indicators\n */\nfunction getAmount() {\n    let counter = 0;\n    this.series.forEach((serie) => {\n        if (serie.params ||\n            serie.options.params) {\n            counter++;\n        }\n    });\n    return counter;\n}\n/**\n * Extract full name and type of requested indicator.\n *\n * @private\n *\n * @param {Highcharts.Series} series\n * Series which name is needed(EDITmode - defaultOptions.series,\n * ADDmode - indicator series).\n *\n * @param {string} [indicatorType]\n * Type of the indicator i.e. sma, ema...\n *\n * @return {Highcharts.Dictionary<string>}\n * Full name and series type.\n */\nfunction getNameType(series, indicatorType) {\n    const options = series.options;\n    // Add mode\n    let seriesName = (seriesTypes[indicatorType] &&\n        seriesTypes[indicatorType].prototype.nameBase) ||\n        indicatorType.toUpperCase(), seriesType = indicatorType;\n    // Edit\n    if (options && options.type) {\n        seriesType = series.options.type;\n        seriesName = series.name;\n    }\n    return {\n        indicatorFullName: seriesName,\n        indicatorType: seriesType\n    };\n}\n/**\n * Create the selection box for the series,\n * add options and apply the default one.\n *\n * @private\n *\n * @param {string} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction listAllSeries(indicatorType, optionName, chart, parentDiv, currentSeries, selectedOption) {\n    const popup = this;\n    // Won't work without the chart.\n    if (!chart) {\n        return;\n    }\n    // Add selection boxes.\n    const selectBox = addSelection.call(popup, indicatorType, optionName, parentDiv);\n    // Add possible dropdown options.\n    addSelectionOptions.call(popup, chart, optionName, selectBox, void 0, void 0, void 0, currentSeries);\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupIndicators = {\n    addForm: PopupIndicators_addForm,\n    getAmount\n};\n/* harmony default export */ const Popup_PopupIndicators = (PopupIndicators);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupTabs.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc: PopupTabs_doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: PopupTabs_addEvent, createElement: PopupTabs_createElement } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create tab content\n * @private\n * @return {HTMLDOMElement} - created HTML tab-content element\n */\nfunction addContentItem() {\n    const popupDiv = this.container;\n    return PopupTabs_createElement('div', {\n        // #12100\n        className: 'highcharts-tab-item-content highcharts-no-mousewheel'\n    }, void 0, popupDiv);\n}\n/**\n * Create tab menu item\n * @private\n * @param {string} tabName\n * `add` or `edit`\n * @param {number} [disableTab]\n * Disable tab when 0\n * @return {Highcharts.HTMLDOMElement}\n * Created HTML tab-menu element\n */\nfunction addMenuItem(tabName, disableTab) {\n    const popupDiv = this.container, lang = this.lang;\n    let className = 'highcharts-tab-item';\n    if (disableTab === 0) {\n        className += ' highcharts-tab-disabled';\n    }\n    // Tab 1\n    const menuItem = PopupTabs_createElement('button', {\n        className\n    }, void 0, popupDiv);\n    menuItem.appendChild(PopupTabs_doc.createTextNode(lang[tabName + 'Button'] || tabName));\n    menuItem.setAttribute('highcharts-data-tab-type', tabName);\n    return menuItem;\n}\n/**\n * Set all tabs as invisible.\n * @private\n */\nfunction deselectAll() {\n    const popupDiv = this.container, tabs = popupDiv\n        .querySelectorAll('.highcharts-tab-item'), tabsContent = popupDiv\n        .querySelectorAll('.highcharts-tab-item-content');\n    for (let i = 0; i < tabs.length; i++) {\n        tabs[i].classList.remove('highcharts-tab-item-active');\n        tabsContent[i].classList.remove('highcharts-tab-item-show');\n    }\n}\n/**\n * Init tabs. Create tab menu items, tabs containers\n * @private\n * @param {Highcharts.Chart} chart\n * Reference to current chart\n */\nfunction init(chart) {\n    if (!chart) {\n        return;\n    }\n    const indicatorsCount = this.indicators.getAmount.call(chart);\n    // Create menu items\n    const firstTab = addMenuItem.call(this, 'add'); // Run by default\n    addMenuItem.call(this, 'edit', indicatorsCount);\n    // Create tabs containers\n    addContentItem.call(this);\n    addContentItem.call(this);\n    switchTabs.call(this, indicatorsCount);\n    // Activate first tab\n    selectTab.call(this, firstTab, 0);\n}\n/**\n * Set tab as visible\n * @private\n * @param {globals.Element} - current tab\n * @param {number} - Index of tab in menu\n */\nfunction selectTab(tab, index) {\n    const allTabs = this.container\n        .querySelectorAll('.highcharts-tab-item-content');\n    tab.className += ' highcharts-tab-item-active';\n    allTabs[index].className += ' highcharts-tab-item-show';\n}\n/**\n * Add click event to each tab\n * @private\n * @param {number} disableTab\n * Disable tab when 0\n */\nfunction switchTabs(disableTab) {\n    const popup = this, popupDiv = this.container, tabs = popupDiv.querySelectorAll('.highcharts-tab-item');\n    tabs.forEach((tab, i) => {\n        if (disableTab === 0 &&\n            tab.getAttribute('highcharts-data-tab-type') === 'edit') {\n            return;\n        }\n        ['click', 'touchstart'].forEach((eventName) => {\n            PopupTabs_addEvent(tab, eventName, function () {\n                // Reset class on other elements\n                deselectAll.call(popup);\n                selectTab.call(popup, this, i);\n            });\n        });\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupTabs = {\n    init\n};\n/* harmony default export */ const Popup_PopupTabs = (PopupTabs);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/Popup.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { doc: Popup_doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { getOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { addEvent: Popup_addEvent, createElement: Popup_createElement, extend: Popup_extend, fireEvent: Popup_fireEvent, pick: Popup_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get values from all inputs and selections then create JSON.\n *\n * @private\n *\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * The container where inputs and selections are created.\n *\n * @param {string} type\n * Type of the popup bookmark (add|edit|remove).\n */\nfunction getFields(parentDiv, type) {\n    const inputList = Array.prototype.slice.call(parentDiv.querySelectorAll('input')), selectList = Array.prototype.slice.call(parentDiv.querySelectorAll('select')), optionSeries = '#highcharts-select-series > option:checked', optionVolume = '#highcharts-select-volume > option:checked', linkedTo = parentDiv.querySelectorAll(optionSeries)[0], volumeTo = parentDiv.querySelectorAll(optionVolume)[0];\n    const fieldsOutput = {\n        actionType: type,\n        linkedTo: linkedTo && linkedTo.getAttribute('value') || '',\n        fields: {}\n    };\n    inputList.forEach((input) => {\n        const param = input.getAttribute('highcharts-data-name'), seriesId = input.getAttribute('highcharts-data-series-id');\n        // Params\n        if (seriesId) {\n            fieldsOutput.seriesId = input.value;\n        }\n        else if (param) {\n            fieldsOutput.fields[param] = input.value;\n        }\n        else {\n            // Type like sma / ema\n            fieldsOutput.type = input.value;\n        }\n    });\n    selectList.forEach((select) => {\n        const id = select.id;\n        // Get inputs only for the parameters, not for series and volume.\n        if (id !== 'highcharts-select-series' &&\n            id !== 'highcharts-select-volume') {\n            const parameter = id.split('highcharts-select-')[1];\n            fieldsOutput.fields[parameter] = select.value;\n        }\n    });\n    if (volumeTo) {\n        fieldsOutput.fields['params.volumeSeriesID'] = volumeTo\n            .getAttribute('value') || '';\n    }\n    return fieldsOutput;\n}\n/* *\n *\n *  Class\n *\n * */\nclass Popup extends Shared_BaseForm {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(parentDiv, iconsURL, chart) {\n        super(parentDiv, iconsURL);\n        this.chart = chart;\n        this.lang = (getOptions().lang.navigation || {}).popup || {};\n        Popup_addEvent(this.container, 'mousedown', () => {\n            const activeAnnotation = chart &&\n                chart.navigationBindings &&\n                chart.navigationBindings.activeAnnotation;\n            if (activeAnnotation) {\n                activeAnnotation.cancelClick = true;\n                const unbind = Popup_addEvent(Popup_doc, 'click', () => {\n                    setTimeout(() => {\n                        activeAnnotation.cancelClick = false;\n                    }, 0);\n                    unbind();\n                });\n            }\n        });\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create input with label.\n     *\n     * @private\n     *\n     * @param {string} option\n     *        Chain of fields i.e params.styles.fontSize separated by the dot.\n     *\n     * @param {string} indicatorType\n     *        Type of the indicator i.e. sma, ema...\n     *\n     * @param {HTMLDOMElement} parentDiv\n     *        HTML parent element.\n     *\n     * @param {Highcharts.InputAttributes} inputAttributes\n     *        Attributes of the input.\n     *\n     * @return {HTMLInputElement}\n     *         Return created input element.\n     */\n    addInput(option, indicatorType, parentDiv, inputAttributes) {\n        const optionParamList = option.split('.'), optionName = optionParamList[optionParamList.length - 1], lang = this.lang, inputName = 'highcharts-' + indicatorType + '-' + Popup_pick(inputAttributes.htmlFor, optionName);\n        if (!optionName.match(/^\\d+$/)) {\n            // Add label\n            Popup_createElement('label', {\n                htmlFor: inputName,\n                className: inputAttributes.labelClassName\n            }, void 0, parentDiv).appendChild(Popup_doc.createTextNode(lang[optionName] || optionName));\n        }\n        // Add input\n        const input = Popup_createElement('input', {\n            name: inputName,\n            value: inputAttributes.value,\n            type: inputAttributes.type,\n            className: 'highcharts-popup-field'\n        }, void 0, parentDiv);\n        input.setAttribute('highcharts-data-name', option);\n        return input;\n    }\n    closeButtonEvents() {\n        if (this.chart) {\n            const navigationBindings = this.chart.navigationBindings;\n            Popup_fireEvent(navigationBindings, 'closePopup');\n            if (navigationBindings &&\n                navigationBindings.selectedButtonElement) {\n                Popup_fireEvent(navigationBindings, 'deselectButton', { button: navigationBindings.selectedButtonElement });\n            }\n        }\n        else {\n            super.closeButtonEvents();\n        }\n    }\n    /**\n     * Create button.\n     * @private\n     * @param {Highcharts.HTMLDOMElement} parentDiv\n     * Container where elements should be added\n     * @param {string} label\n     * Text placed as button label\n     * @param {string} type\n     * add | edit | remove\n     * @param {Function} callback\n     * On click callback\n     * @param {Highcharts.HTMLDOMElement} fieldsDiv\n     * Container where inputs are generated\n     * @return {Highcharts.HTMLDOMElement}\n     * HTML button\n     */\n    addButton(parentDiv, label, type, fieldsDiv, callback) {\n        const button = Popup_createElement('button', void 0, void 0, parentDiv);\n        button.appendChild(Popup_doc.createTextNode(label));\n        if (callback) {\n            ['click', 'touchstart'].forEach((eventName) => {\n                Popup_addEvent(button, eventName, () => {\n                    this.closePopup();\n                    return callback(getFields(fieldsDiv, type));\n                });\n            });\n        }\n        return button;\n    }\n    /**\n     * Create content and show popup.\n     * @private\n     * @param {string} - type of popup i.e indicators\n     * @param {Highcharts.Chart} - chart\n     * @param {Highcharts.AnnotationsOptions} - options\n     * @param {Function} - on click callback\n     */\n    showForm(type, chart, options, callback) {\n        if (!chart) {\n            return;\n        }\n        // Show blank popup\n        this.showPopup();\n        // Indicator form\n        if (type === 'indicators') {\n            this.indicators.addForm.call(this, chart, options, callback);\n        }\n        // Annotation small toolbar\n        if (type === 'annotation-toolbar') {\n            this.annotations.addToolbar.call(this, chart, options, callback);\n        }\n        // Annotation edit form\n        if (type === 'annotation-edit') {\n            this.annotations.addForm.call(this, chart, options, callback);\n        }\n        // Flags form - add / edit\n        if (type === 'flag') {\n            this.annotations.addForm.call(this, chart, options, callback, true);\n        }\n        this.type = type;\n        // Explicit height is needed to make inner elements scrollable\n        this.container.style.height = this.container.offsetHeight + 'px';\n    }\n}\nPopup_extend(Popup.prototype, {\n    annotations: Popup_PopupAnnotations,\n    indicators: Popup_PopupIndicators,\n    tabs: Popup_PopupTabs\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Popup_Popup = (Popup);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupComposition.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent: PopupComposition_addEvent, pushUnique, wrap: PopupComposition_wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(NagivationBindingsClass, PointerClass) {\n    if (pushUnique(composed, 'Popup')) {\n        PopupComposition_addEvent(NagivationBindingsClass, 'closePopup', onNavigationBindingsClosePopup);\n        PopupComposition_addEvent(NagivationBindingsClass, 'showPopup', onNavigationBindingsShowPopup);\n        PopupComposition_wrap(PointerClass.prototype, 'onContainerMouseDown', wrapPointerOnContainerMouserDown);\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsClosePopup() {\n    if (this.popup) {\n        this.popup.closePopup();\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsShowPopup(config) {\n    if (!this.popup) {\n        // Add popup to main container\n        this.popup = new Popup_Popup(this.chart.container, (this.chart.options.navigation.iconsURL ||\n            (this.chart.options.stockTools &&\n                this.chart.options.stockTools.gui.iconsURL) ||\n            'https://code.highcharts.com/12.2.0/gfx/stock-icons/'), this.chart);\n    }\n    this.popup.showForm(config.formType, this.chart, config.options, config.onSubmit);\n}\n/**\n * `onContainerMouseDown` blocks internal popup events, due to e.preventDefault.\n * Related issue #4606\n * @private\n */\nfunction wrapPointerOnContainerMouserDown(proceed, e) {\n    // Elements is not in popup\n    if (!this.inClass(e.target, 'highcharts-popup')) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupComposition = {\n    compose\n};\n/* harmony default export */ const Popup_PopupComposition = (PopupComposition);\n\n;// ./code/es-modules/Extensions/Annotations/Annotation.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getDeferredAnimation } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { destroyObjectProperties, erase: Annotation_erase, fireEvent: Annotation_fireEvent, merge: Annotation_merge, pick: Annotation_pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Hide or show annotation attached to points.\n * @private\n */\nfunction adjustVisibility(item) {\n    const label = item.graphic, hasVisiblePoints = item.points.some((point) => (point.series.visible !== false &&\n        point.visible !== false));\n    if (label) {\n        if (!hasVisiblePoints) {\n            label.hide();\n        }\n        else if (label.visibility === 'hidden') {\n            label.show();\n        }\n    }\n}\n/**\n * @private\n */\nfunction getLabelsAndShapesOptions(baseOptions, newOptions) {\n    const mergedOptions = {};\n    ['labels', 'shapes'].forEach((name) => {\n        const someBaseOptions = baseOptions[name], newOptionsValue = newOptions[name];\n        if (someBaseOptions) {\n            if (newOptionsValue) {\n                mergedOptions[name] = splat(newOptionsValue).map((basicOptions, i) => Annotation_merge(someBaseOptions[i], basicOptions));\n            }\n            else {\n                mergedOptions[name] = baseOptions[name];\n            }\n        }\n    });\n    return mergedOptions;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * An annotation class which serves as a container for items like labels or\n * shapes. Created items are positioned on the chart either by linking them to\n * existing points or created mock points\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.Annotation\n *\n * @param {Highcharts.Chart} chart\n *        A chart instance\n * @param {Highcharts.AnnotationsOptions} userOptions\n *        The annotation options\n */\nclass Annotation extends Annotations_EventEmitter {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    static compose(ChartClass, NavigationBindingsClass, PointerClass, SVGRendererClass) {\n        Annotations_AnnotationChart.compose(Annotation, ChartClass, PointerClass);\n        Controllables_ControllableLabel.compose(SVGRendererClass);\n        Controllables_ControllablePath.compose(ChartClass, SVGRendererClass);\n        NavigationBindingsClass.compose(Annotation, ChartClass);\n        Popup_PopupComposition.compose(NavigationBindingsClass, PointerClass);\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(chart, userOptions) {\n        super();\n        this.coll = 'annotations';\n        /**\n         * The chart that the annotation belongs to.\n         *\n         * @name Highcharts.Annotation#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The array of points which defines the annotation.\n         * @private\n         * @name Highcharts.Annotation#points\n         * @type {Array<Highcharts.Point>}\n         */\n        this.points = [];\n        /**\n         * The array of control points.\n         * @private\n         * @name Highcharts.Annotation#controlPoints\n         * @type {Array<Annotation.ControlPoint>}\n         */\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.index = -1;\n        /**\n         * The array of labels which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#labels\n         * @type {Array<Highcharts.AnnotationLabelType>}\n         */\n        this.labels = [];\n        /**\n         * The array of shapes which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#shapes\n         * @type {Array<Highcharts.AnnotationShapeType>}\n         */\n        this.shapes = [];\n        /**\n         * The options for the annotations.\n         *\n         * @name Highcharts.Annotation#options\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        this.options = Annotation_merge(this.defaultOptions, userOptions);\n        /**\n         * The user options for the annotations.\n         *\n         * @name Highcharts.Annotation#userOptions\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        this.userOptions = userOptions;\n        // Handle labels and shapes - those are arrays\n        // Merging does not work with arrays (stores reference)\n        const labelsAndShapes = getLabelsAndShapesOptions(this.options, userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        /**\n         * The callback that reports to the overlapping labels logic which\n         * labels it should account for.\n         * @private\n         * @name Highcharts.Annotation#labelCollector\n         * @type {Function}\n         */\n        /**\n         * The group svg element.\n         *\n         * @name Highcharts.Annotation#group\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's shapes.\n         *\n         * @name Highcharts.Annotation#shapesGroup\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's labels.\n         *\n         * @name Highcharts.Annotation#labelsGroup\n         * @type {Highcharts.SVGElement}\n         */\n        this.init(chart, this.options);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    addClipPaths() {\n        this.setClipAxes();\n        if (this.clipXAxis &&\n            this.clipYAxis &&\n            this.options.crop // #15399\n        ) {\n            this.clipRect = this.chart.renderer.clipRect(this.getClipBox());\n        }\n    }\n    /**\n     * @private\n     */\n    addLabels() {\n        const labelsOptions = (this.options.labels || []);\n        labelsOptions.forEach((labelOptions, i) => {\n            const label = this.initLabel(labelOptions, i);\n            Annotation_merge(true, labelsOptions[i], label.options);\n        });\n    }\n    /**\n     * @private\n     */\n    addShapes() {\n        const shapes = this.options.shapes || [];\n        shapes.forEach((shapeOptions, i) => {\n            const shape = this.initShape(shapeOptions, i);\n            Annotation_merge(true, shapes[i], shape.options);\n        });\n    }\n    /**\n     * Destroy the annotation. This function does not touch the chart\n     * that the annotation belongs to (all annotations are kept in\n     * the chart.annotations array) - it is recommended to use\n     * {@link Highcharts.Chart#removeAnnotation} instead.\n     * @private\n     */\n    destroy() {\n        const chart = this.chart, destroyItem = function (item) {\n            item.destroy();\n        };\n        this.labels.forEach(destroyItem);\n        this.shapes.forEach(destroyItem);\n        this.clipXAxis = null;\n        this.clipYAxis = null;\n        Annotation_erase(chart.labelCollectors, this.labelCollector);\n        super.destroy();\n        this.destroyControlTarget();\n        destroyObjectProperties(this, chart);\n    }\n    /**\n     * Destroy a single item.\n     * @private\n     */\n    destroyItem(item) {\n        // Erase from shapes or labels array\n        Annotation_erase(this[item.itemType + 's'], item);\n        item.destroy();\n    }\n    /**\n     * @private\n     */\n    getClipBox() {\n        if (this.clipXAxis && this.clipYAxis) {\n            return {\n                x: this.clipXAxis.left,\n                y: this.clipYAxis.top,\n                width: this.clipXAxis.width,\n                height: this.clipYAxis.height\n            };\n        }\n    }\n    /**\n     * Initialize the annotation properties.\n     * @private\n     */\n    initProperties(chart, userOptions) {\n        this.setOptions(userOptions);\n        const labelsAndShapes = getLabelsAndShapesOptions(this.options, userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        this.chart = chart;\n        this.points = [];\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.userOptions = userOptions;\n        this.labels = [];\n        this.shapes = [];\n    }\n    /**\n     * Initialize the annotation.\n     * @private\n     */\n    init(_annotationOrChart, _userOptions, index = this.index) {\n        const chart = this.chart, animOptions = this.options.animation;\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n        this.addShapes();\n        this.addLabels();\n        this.setLabelCollector();\n        this.animationConfig = getDeferredAnimation(chart, animOptions);\n    }\n    /**\n     * Initialisation of a single label\n     * @private\n     */\n    initLabel(labelOptions, index) {\n        const options = Annotation_merge(this.options.labelOptions, {\n            controlPointOptions: this.options.controlPointOptions\n        }, labelOptions), label = new Controllables_ControllableLabel(this, options, index);\n        label.itemType = 'label';\n        this.labels.push(label);\n        return label;\n    }\n    /**\n     * Initialisation of a single shape\n     * @private\n     * @param {Object} shapeOptions\n     * a config object for a single shape\n     * @param {number} index\n     * annotation may have many shapes, this is the shape's index saved in\n     * shapes.index.\n     */\n    initShape(shapeOptions, index) {\n        const options = Annotation_merge(this.options.shapeOptions, {\n            controlPointOptions: this.options.controlPointOptions\n        }, shapeOptions), shape = new (Annotation.shapesMap[options.type])(this, options, index);\n        shape.itemType = 'shape';\n        this.shapes.push(shape);\n        return shape;\n    }\n    /**\n     * @private\n     */\n    redraw(animation) {\n        this.linkPoints();\n        if (!this.graphic) {\n            this.render();\n        }\n        if (this.clipRect) {\n            this.clipRect.animate(this.getClipBox());\n        }\n        this.redrawItems(this.shapes, animation);\n        this.redrawItems(this.labels, animation);\n        this.redrawControlPoints(animation);\n    }\n    /**\n     * Redraw a single item.\n     * @private\n     */\n    redrawItem(item, animation) {\n        item.linkPoints();\n        if (!item.shouldBeDrawn()) {\n            this.destroyItem(item);\n        }\n        else {\n            if (!item.graphic) {\n                this.renderItem(item);\n            }\n            item.redraw(Annotation_pick(animation, true) && item.graphic.placed);\n            if (item.points.length) {\n                adjustVisibility(item);\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    redrawItems(items, animation) {\n        let i = items.length;\n        // Needs a backward loop. Labels/shapes array might be modified due to\n        // destruction of the item\n        while (i--) {\n            this.redrawItem(items[i], animation);\n        }\n    }\n    /**\n     * See {@link Highcharts.Chart#removeAnnotation}.\n     * @private\n     */\n    remove() {\n        // Let chart.update() remove annotations on demand\n        return this.chart.removeAnnotation(this);\n    }\n    /**\n     * @private\n     */\n    render() {\n        const renderer = this.chart.renderer;\n        this.graphic = renderer\n            .g('annotation')\n            .attr({\n            opacity: 0,\n            zIndex: this.options.zIndex,\n            visibility: this.options.visible ?\n                'inherit' :\n                'hidden'\n        })\n            .add();\n        this.shapesGroup = renderer\n            .g('annotation-shapes')\n            .add(this.graphic);\n        if (this.options.crop) { // #15399\n            this.shapesGroup.clip(this.chart.plotBoxClip);\n        }\n        this.labelsGroup = renderer\n            .g('annotation-labels')\n            .attr({\n            // `hideOverlappingLabels` requires translation\n            translateX: 0,\n            translateY: 0\n        })\n            .add(this.graphic);\n        this.addClipPaths();\n        if (this.clipRect) {\n            this.graphic.clip(this.clipRect);\n        }\n        // Render shapes and labels before adding events (#13070).\n        this.renderItems(this.shapes);\n        this.renderItems(this.labels);\n        this.addEvents();\n        this.renderControlPoints();\n    }\n    /**\n     * @private\n     */\n    renderItem(item) {\n        item.render(item.itemType === 'label' ?\n            this.labelsGroup :\n            this.shapesGroup);\n    }\n    /**\n     * @private\n     */\n    renderItems(items) {\n        let i = items.length;\n        while (i--) {\n            this.renderItem(items[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    setClipAxes() {\n        const xAxes = this.chart.xAxis, yAxes = this.chart.yAxis, linkedAxes = (this.options.labels || [])\n            .concat(this.options.shapes || [])\n            .reduce((axes, labelOrShape) => {\n            const point = labelOrShape &&\n                (labelOrShape.point ||\n                    (labelOrShape.points && labelOrShape.points[0]));\n            return [\n                xAxes[point && point.xAxis] || axes[0],\n                yAxes[point && point.yAxis] || axes[1]\n            ];\n        }, []);\n        this.clipXAxis = linkedAxes[0];\n        this.clipYAxis = linkedAxes[1];\n    }\n    /**\n     * @private\n     */\n    setControlPointsVisibility(visible) {\n        const setItemControlPointsVisibility = function (item) {\n            item.setControlPointsVisibility(visible);\n        };\n        this.controlPoints.forEach((controlPoint) => {\n            controlPoint.setVisibility(visible);\n        });\n        this.shapes.forEach(setItemControlPointsVisibility);\n        this.labels.forEach(setItemControlPointsVisibility);\n    }\n    /**\n     * @private\n     */\n    setLabelCollector() {\n        const annotation = this;\n        annotation.labelCollector = function () {\n            return annotation.labels.reduce(function (labels, label) {\n                if (!label.options.allowOverlap) {\n                    labels.push(label.graphic);\n                }\n                return labels;\n            }, []);\n        };\n        annotation.chart.labelCollectors.push(annotation.labelCollector);\n    }\n    /**\n     * Set an annotation options.\n     * @private\n     * @param {Highcharts.AnnotationsOptions} userOptions\n     *        User options for an annotation\n     */\n    setOptions(userOptions) {\n        this.options = Annotation_merge(this.defaultOptions, userOptions);\n    }\n    /**\n     * Set the annotation's visibility.\n     * @private\n     * @param {boolean} [visible]\n     * Whether to show or hide an annotation. If the param is omitted, the\n     * annotation's visibility is toggled.\n     */\n    setVisibility(visible) {\n        const options = this.options, navigation = this.chart.navigationBindings, visibility = Annotation_pick(visible, !options.visible);\n        this.graphic.attr('visibility', visibility ? 'inherit' : 'hidden');\n        if (!visibility) {\n            const setItemControlPointsVisibility = function (item) {\n                item.setControlPointsVisibility(visibility);\n            };\n            this.shapes.forEach(setItemControlPointsVisibility);\n            this.labels.forEach(setItemControlPointsVisibility);\n            if (navigation.activeAnnotation === this &&\n                navigation.popup &&\n                navigation.popup.type === 'annotation-toolbar') {\n                Annotation_fireEvent(navigation, 'closePopup');\n            }\n        }\n        options.visible = visibility;\n    }\n    /**\n     * Updates an annotation.\n     *\n     * @function Highcharts.Annotation#update\n     *\n     * @param {Partial<Highcharts.AnnotationsOptions>} userOptions\n     *        New user options for the annotation.\n     *\n     */\n    update(userOptions, redraw) {\n        const chart = this.chart, labelsAndShapes = getLabelsAndShapesOptions(this.userOptions, userOptions), userOptionsIndex = chart.annotations.indexOf(this), options = Annotation_merge(true, this.userOptions, userOptions);\n        options.labels = labelsAndShapes.labels;\n        options.shapes = labelsAndShapes.shapes;\n        this.destroy();\n        this.initProperties(chart, options);\n        this.init(chart, options);\n        // Update options in chart options, used in exporting (#9767, #21507):\n        chart.options.annotations[userOptionsIndex] = this.options;\n        this.isUpdating = true;\n        if (Annotation_pick(redraw, true)) {\n            chart.drawAnnotations();\n        }\n        Annotation_fireEvent(this, 'afterUpdate');\n        this.isUpdating = false;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * @private\n */\nAnnotation.ControlPoint = Annotations_ControlPoint;\n/**\n * @private\n */\nAnnotation.MockPoint = Annotations_MockPoint;\n/**\n * An object uses for mapping between a shape type and a constructor.\n * To add a new shape type extend this object with type name as a key\n * and a constructor as its value.\n *\n * @private\n */\nAnnotation.shapesMap = {\n    'rect': Controllables_ControllableRect,\n    'circle': Controllables_ControllableCircle,\n    'ellipse': Controllables_ControllableEllipse,\n    'path': Controllables_ControllablePath,\n    'image': Controllables_ControllableImage\n};\n/**\n * @private\n */\nAnnotation.types = {};\nAnnotation.prototype.defaultOptions = Annotations_AnnotationDefaults;\n/**\n * List of events for `annotation.options.events` that should not be\n * added to `annotation.graphic` but to the `annotation`.\n *\n * @private\n * @type {Array<string>}\n */\nAnnotation.prototype.nonDOMEvents = ['add', 'afterUpdate', 'drag', 'remove'];\nAnnotations_ControlTarget.compose(Annotation);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_Annotation = (Annotation);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Possible directions for draggable annotations. An empty string (`''`)\n * makes the annotation undraggable.\n *\n * @typedef {''|'x'|'xy'|'y'} Highcharts.AnnotationDraggableValue\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableCircle|\n *          Highcharts.AnnotationControllableImage|\n *          Highcharts.AnnotationControllablePath|\n *          Highcharts.AnnotationControllableRect\n *     } Highcharts.AnnotationShapeType\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableLabel\n *     } Highcharts.AnnotationLabelType\n * @requires modules/annotations\n */\n/**\n * A point-like object, a mock point or a point used in series.\n * @private\n * @typedef {\n *          Highcharts.AnnotationMockPoint|\n *          Highcharts.Point\n *     } Highcharts.AnnotationPointType\n * @requires modules/annotations\n */\n/**\n * Shape point as string, object or function.\n *\n * @typedef {\n *          string|\n *          Highcharts.AnnotationMockPointOptionsObject|\n *          Highcharts.AnnotationMockPointFunction\n *     } Highcharts.AnnotationShapePointOptions\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(chart) {\n            this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        addUpdate(updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        }\n        /**\n         * @private\n         */\n        update(options, redraw) {\n            this.updates.forEach((updateFn) => {\n                updateFn.call(this.chart, options, redraw);\n            });\n        }\n    }\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: NavigationBindingsUtilities_defined, isNumber: NavigationBindingsUtilities_isNumber, pick: NavigationBindingsUtilities_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define types for editable fields per annotation. There is no need to define\n * numbers, because they won't change their type to string.\n * @private\n */\nconst annotationsFieldsTypes = {\n    backgroundColor: 'string',\n    borderColor: 'string',\n    borderRadius: 'string',\n    color: 'string',\n    fill: 'string',\n    fontSize: 'string',\n    labels: 'string',\n    name: 'string',\n    stroke: 'string',\n    title: 'string'\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns the first xAxis or yAxis that was clicked with its value.\n *\n * @private\n *\n * @param {Array<Highcharts.PointerAxisCoordinateObject>} coords\n *        All the chart's x or y axes with a current pointer's axis value.\n *\n * @return {Highcharts.PointerAxisCoordinateObject}\n *         Object with a first found axis and its value that pointer\n *         is currently pointing.\n */\nfunction getAssignedAxis(coords) {\n    return coords.filter((coord) => {\n        const extremes = coord.axis.getExtremes(), axisMin = extremes.min, axisMax = extremes.max, \n        // Correct axis edges when axis has series\n        // with pointRange (like column)\n        minPointOffset = NavigationBindingsUtilities_pick(coord.axis.minPointOffset, 0);\n        return NavigationBindingsUtilities_isNumber(axisMin) && NavigationBindingsUtilities_isNumber(axisMax) &&\n            coord.value >= (axisMin - minPointOffset) &&\n            coord.value <= (axisMax + minPointOffset) &&\n            // Don't count navigator axis\n            !coord.axis.options.isInternal;\n    })[0]; // If the axes overlap, return the first axis that was found.\n}\n/**\n * Get field type according to value\n *\n * @private\n *\n * @param {'boolean'|'number'|'string'} value\n * Atomic type (one of: string, number, boolean)\n *\n * @return {'checkbox'|'number'|'text'}\n * Field type (one of: text, number, checkbox)\n */\nfunction getFieldType(key, value) {\n    const predefinedType = annotationsFieldsTypes[key];\n    let fieldType = typeof value;\n    if (NavigationBindingsUtilities_defined(predefinedType)) {\n        fieldType = predefinedType;\n    }\n    return {\n        'string': 'text',\n        'number': 'number',\n        'boolean': 'checkbox'\n    }[fieldType];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingUtilities = {\n    annotationsFieldsTypes,\n    getAssignedAxis,\n    getFieldType\n};\n/* harmony default export */ const NavigationBindingsUtilities = (NavigationBindingUtilities);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getAssignedAxis: NavigationBindingsDefaults_getAssignedAxis } = NavigationBindingsUtilities;\n\nconst { isNumber: NavigationBindingsDefaults_isNumber, merge: NavigationBindingsDefaults_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * Configure the Popup strings in the chart. Requires the\n     * `annotations.js` or `annotations-advanced.js` module to be\n     * loaded.\n     * @since   7.0.0\n     * @product highcharts highstock\n     */\n    navigation: {\n        /**\n         * Translations for all field names used in popup.\n         *\n         * @product highcharts highstock\n         */\n        popup: {\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            shapeOptions: 'Shape options',\n            typeOptions: 'Details',\n            fill: 'Fill',\n            format: 'Text',\n            strokeWidth: 'Line width',\n            stroke: 'Line color',\n            title: 'Title',\n            name: 'Name',\n            labelOptions: 'Label options',\n            labels: 'Labels',\n            backgroundColor: 'Background color',\n            backgroundColors: 'Background colors',\n            borderColor: 'Border color',\n            borderRadius: 'Border radius',\n            borderWidth: 'Border width',\n            style: 'Style',\n            padding: 'Padding',\n            fontSize: 'Font size',\n            color: 'Color',\n            height: 'Height',\n            shapes: 'Shape options'\n        }\n    }\n};\n/**\n * @optionparent navigation\n * @product      highcharts highstock\n */\nconst navigation = {\n    /**\n     * A CSS class name where all bindings will be attached to. Multiple\n     * charts on the same page should have separate class names to prevent\n     * duplicating events.\n     *\n     * Default value of versions < 7.0.4 `highcharts-bindings-wrapper`\n     *\n     * @since     7.0.0\n     * @type      {string}\n     */\n    bindingsClassName: 'highcharts-bindings-container',\n    /**\n     * Bindings definitions for custom HTML buttons. Each binding implements\n     * simple event-driven interface:\n     *\n     * - `className`: classname used to bind event to\n     *\n     * - `init`: initial event, fired on button click\n     *\n     * - `start`: fired on first click on a chart\n     *\n     * - `steps`: array of sequential events fired one after another on each\n     *   of users clicks\n     *\n     * - `end`: last event to be called after last step event\n     *\n     * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>|*}\n     *\n     * @sample {highstock} stock/stocktools/stocktools-thresholds\n     *               Custom bindings\n     * @sample {highcharts} highcharts/annotations/bindings/\n     *               Simple binding\n     * @sample {highcharts} highcharts/annotations/bindings-custom-annotation/\n     *               Custom annotation binding\n     *\n     * @since        7.0.0\n     * @requires     modules/annotations\n     * @product      highcharts highstock\n     */\n    bindings: {\n        /**\n         * A circle annotation bindings. Includes `start` and one event in\n         * `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-circle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        circleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-circle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'circle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'circle',\n                            point: {\n                                x: coordsX.value,\n                                y: coordsY.value,\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index\n                            },\n                            r: 5\n                        }]\n                }, navigation.annotationsOptions, navigation.bindings.circleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    const shapes = annotation.options.shapes, mockPointOpts = ((shapes && shapes[0] && shapes[0].point) ||\n                        {});\n                    let distance;\n                    if (NavigationBindingsDefaults_isNumber(mockPointOpts.xAxis) &&\n                        NavigationBindingsDefaults_isNumber(mockPointOpts.yAxis)) {\n                        const inverted = this.chart.inverted, x = this.chart.xAxis[mockPointOpts.xAxis]\n                            .toPixels(mockPointOpts.x), y = this.chart.yAxis[mockPointOpts.yAxis]\n                            .toPixels(mockPointOpts.y);\n                        distance = Math.max(Math.sqrt(Math.pow(inverted ? y - e.chartX : x - e.chartX, 2) +\n                            Math.pow(inverted ? x - e.chartY : y - e.chartY, 2)), 5);\n                    }\n                    annotation.update({\n                        shapes: [{\n                                r: distance\n                            }]\n                    });\n                }\n            ]\n        },\n        /**\n         * A ellipse annotation bindings. Includes `start` and two events in\n         * `steps` array. First updates the second point, responsible for a\n         * rx width, and second updates the ry width.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-ellipse-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        ellipseAnnotation: {\n            className: 'highcharts-ellipse-annotation',\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'ellipse',\n                    type: 'basicAnnotation',\n                    shapes: [\n                        {\n                            type: 'ellipse',\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }, {\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }],\n                            ry: 1\n                        }\n                    ]\n                }, navigation.annotationsOptions, navigation.bindings.ellipseAnnotation\n                    .annotationsOptions));\n            },\n            steps: [\n                function (e, annotation) {\n                    const target = annotation.shapes[0], position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                },\n                function (e, annotation) {\n                    const target = annotation.shapes[0], position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), newR = target.getDistanceFromLine(position, position2, e.chartX, e.chartY), yAxis = target.getYAxis(), newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            ]\n        },\n        /**\n         * A rectangle annotation bindings. Includes `start` and one event\n         * in `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-rectangle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        rectangleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-rectangle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                const x = coordsX.value, y = coordsY.value, xAxis = coordsX.axis.index, yAxis = coordsY.axis.index, navigation = this.chart.options.navigation;\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'rectangle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'path',\n                            points: [\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { xAxis, yAxis, x, y },\n                                { command: 'Z' }\n                            ]\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .rectangleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    const shapes = annotation.options.shapes, points = ((shapes && shapes[0] && shapes[0].points) ||\n                        []), coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                    if (coordsX && coordsY) {\n                        const x = coordsX.value, y = coordsY.value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        annotation.update({\n                            shapes: [{\n                                    points: points\n                                }]\n                        });\n                    }\n                }\n            ]\n        },\n        /**\n         * A label annotation bindings. Includes `start` event only.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-label-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        labelAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-label-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                const coords = this.chart.pointer?.getCoordinates(e), coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis), coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis), navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'label',\n                    type: 'basicAnnotation',\n                    labelOptions: {\n                        format: '{y:.2f}',\n                        overflow: 'none',\n                        crop: true\n                    },\n                    labels: [{\n                            point: {\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index,\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .labelAnnotation\n                    .annotationsOptions));\n            }\n        }\n    },\n    /**\n     * Path where Highcharts will look for icons. Change this to use icons\n     * from a different server.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/12.2.0/gfx/stock-icons/\n     * @since     7.1.3\n     * @apioption navigation.iconsURL\n     */\n    /**\n     * A `showPopup` event. Fired when selecting for example an annotation.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.showPopup\n     */\n    /**\n     * A `closePopup` event. Fired when Popup should be hidden, for example\n     * when clicking on an annotation again.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.closePopup\n     */\n    /**\n     * Event fired on a button click.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.selectButton\n     */\n    /**\n     * Event fired when button state should change, for example after\n     * adding an annotation.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.deselectButton\n     */\n    /**\n     * Events to communicate between Stock Tools and custom GUI.\n     *\n     * @since        7.0.0\n     * @product      highcharts highstock\n     * @optionparent navigation.events\n     */\n    events: {},\n    /**\n     * Additional options to be merged into all annotations.\n     *\n     * @sample stock/stocktools/navigation-annotation-options\n     *         Set red color of all line annotations\n     *\n     * @type      {Highcharts.AnnotationsOptions}\n     * @extends   annotations\n     * @exclude   crookedLine, elliottWave, fibonacci, infinityLine,\n     *            measure, pitchfork, tunnel, verticalLine, basicAnnotation\n     * @requires     modules/annotations\n     * @apioption navigation.annotationsOptions\n     */\n    annotationsOptions: {\n        animation: {\n            defer: 0\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingDefaults = {\n    lang,\n    navigation\n};\n/* harmony default export */ const NavigationBindingsDefaults = (NavigationBindingDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/NavigationBindings.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { setOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { format: NavigationBindings_format } = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default());\n\nconst { composed: NavigationBindings_composed, doc: NavigationBindings_doc, win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { getAssignedAxis: NavigationBindings_getAssignedAxis, getFieldType: NavigationBindings_getFieldType } = NavigationBindingsUtilities;\n\nconst { addEvent: NavigationBindings_addEvent, attr, defined: NavigationBindings_defined, fireEvent: NavigationBindings_fireEvent, isArray: NavigationBindings_isArray, isFunction, isNumber: NavigationBindings_isNumber, isObject: NavigationBindings_isObject, merge: NavigationBindings_merge, objectEach: NavigationBindings_objectEach, pick: NavigationBindings_pick, pushUnique: NavigationBindings_pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * IE 9-11 polyfill for Element.closest():\n * @private\n */\nfunction closestPolyfill(el, s) {\n    const ElementProto = win.Element.prototype, elementMatches = ElementProto.matches ||\n        ElementProto.msMatchesSelector ||\n        ElementProto.webkitMatchesSelector;\n    let ret = null;\n    if (ElementProto.closest) {\n        ret = ElementProto.closest.call(el, s);\n    }\n    else {\n        do {\n            if (elementMatches.call(el, s)) {\n                return el;\n            }\n            el = el.parentElement || el.parentNode;\n        } while (el !== null && el.nodeType === 1);\n    }\n    return ret;\n}\n/**\n * @private\n */\nfunction onAnnotationRemove() {\n    if (this.chart.navigationBindings) {\n        this.chart.navigationBindings.deselectAnnotation();\n    }\n}\n/**\n * @private\n */\nfunction onChartDestroy() {\n    if (this.navigationBindings) {\n        this.navigationBindings.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartLoad() {\n    const options = this.options;\n    if (options && options.navigation && options.navigation.bindings) {\n        this.navigationBindings = new NavigationBindings(this, options.navigation);\n        this.navigationBindings.initEvents();\n        this.navigationBindings.initUpdate();\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    const navigationBindings = this.navigationBindings, disabledClassName = 'highcharts-disabled-btn';\n    if (this && navigationBindings) {\n        // Check if the buttons should be enabled/disabled based on\n        // visible series.\n        let buttonsEnabled = false;\n        this.series.forEach((series) => {\n            if (!series.options.isInternal && series.visible) {\n                buttonsEnabled = true;\n            }\n        });\n        if (this.navigationBindings &&\n            this.navigationBindings.container &&\n            this.navigationBindings.container[0]) {\n            const container = this.navigationBindings.container[0];\n            NavigationBindings_objectEach(navigationBindings.boundClassNames, (value, key) => {\n                // Get the HTML element corresponding to the className taken\n                // from StockToolsBindings.\n                const buttonNode = container.querySelectorAll('.' + key);\n                if (buttonNode) {\n                    for (let i = 0; i < buttonNode.length; i++) {\n                        const button = buttonNode[i], cls = button.className;\n                        if (value.noDataState === 'normal') {\n                            // If button has noDataState: 'normal', and has\n                            // disabledClassName, remove this className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                        else if (!buttonsEnabled) {\n                            if (cls.indexOf(disabledClassName) === -1) {\n                                button.className += ' ' + disabledClassName;\n                            }\n                        }\n                        else {\n                            // Enable all buttons by deleting the className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                    }\n                }\n            });\n        }\n    }\n}\n/**\n * @private\n */\nfunction NavigationBindings_onNavigationBindingsClosePopup() {\n    this.deselectAnnotation();\n}\n/**\n * @private\n */\nfunction onNavigationBindingsDeselectButton() {\n    this.selectedButtonElement = null;\n}\n/**\n * Show edit-annotation form:\n * @private\n */\nfunction selectableAnnotation(annotationType) {\n    const originalClick = annotationType.prototype.defaultOptions.events &&\n        annotationType.prototype.defaultOptions.events.click;\n    /**\n     * Select and show popup\n     * @private\n     */\n    function selectAndShowPopup(eventArguments) {\n        const annotation = this, navigation = annotation.chart.navigationBindings, prevAnnotation = navigation.activeAnnotation;\n        if (originalClick) {\n            originalClick.call(annotation, eventArguments);\n        }\n        if (prevAnnotation !== annotation) {\n            // Select current:\n            navigation.deselectAnnotation();\n            navigation.activeAnnotation = annotation;\n            annotation.setControlPointsVisibility(true);\n            NavigationBindings_fireEvent(navigation, 'showPopup', {\n                annotation: annotation,\n                formType: 'annotation-toolbar',\n                options: navigation.annotationToFields(annotation),\n                onSubmit: function (data) {\n                    if (data.actionType === 'remove') {\n                        navigation.activeAnnotation = false;\n                        navigation.chart.removeAnnotation(annotation);\n                    }\n                    else {\n                        const config = {};\n                        navigation.fieldsToOptions(data.fields, config);\n                        navigation.deselectAnnotation();\n                        const typeOptions = config.typeOptions;\n                        if (annotation.options.type === 'measure') {\n                            // Manually disable crooshars according to\n                            // stroke width of the shape:\n                            typeOptions.crosshairY.enabled = (typeOptions.crosshairY\n                                .strokeWidth !== 0);\n                            typeOptions.crosshairX.enabled = (typeOptions.crosshairX\n                                .strokeWidth !== 0);\n                        }\n                        annotation.update(config);\n                    }\n                }\n            });\n        }\n        else {\n            // Deselect current:\n            NavigationBindings_fireEvent(navigation, 'closePopup');\n        }\n        // Let bubble event to chart.click:\n        eventArguments.activeAnnotation = true;\n    }\n    // #18276, show popup on touchend, but not on touchmove\n    let touchStartX, touchStartY;\n    /**\n     *\n     */\n    function saveCoords(e) {\n        touchStartX = e.touches[0].clientX;\n        touchStartY = e.touches[0].clientY;\n    }\n    /**\n     *\n     */\n    function checkForTouchmove(e) {\n        const hasMoved = touchStartX ? Math.sqrt(Math.pow(touchStartX - e.changedTouches[0].clientX, 2) +\n            Math.pow(touchStartY - e.changedTouches[0].clientY, 2)) >= 4 : false;\n        if (!hasMoved) {\n            selectAndShowPopup.call(this, e);\n        }\n    }\n    NavigationBindings_merge(true, annotationType.prototype.defaultOptions.events, {\n        click: selectAndShowPopup,\n        touchstart: saveCoords,\n        touchend: checkForTouchmove\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nclass NavigationBindings {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AnnotationClass, ChartClass) {\n        if (NavigationBindings_pushUnique(NavigationBindings_composed, 'NavigationBindings')) {\n            NavigationBindings_addEvent(AnnotationClass, 'remove', onAnnotationRemove);\n            // Basic shapes:\n            selectableAnnotation(AnnotationClass);\n            // Advanced annotations:\n            NavigationBindings_objectEach(AnnotationClass.types, (annotationType) => {\n                selectableAnnotation(annotationType);\n            });\n            NavigationBindings_addEvent(ChartClass, 'destroy', onChartDestroy);\n            NavigationBindings_addEvent(ChartClass, 'load', onChartLoad);\n            NavigationBindings_addEvent(ChartClass, 'render', onChartRender);\n            NavigationBindings_addEvent(NavigationBindings, 'closePopup', NavigationBindings_onNavigationBindingsClosePopup);\n            NavigationBindings_addEvent(NavigationBindings, 'deselectButton', onNavigationBindingsDeselectButton);\n            setOptions(NavigationBindingsDefaults);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, options) {\n        this.boundClassNames = void 0;\n        this.chart = chart;\n        this.options = options;\n        this.eventsToUnbind = [];\n        this.container =\n            this.chart.container.getElementsByClassName(this.options.bindingsClassName || '');\n        if (!this.container.length) {\n            this.container = NavigationBindings_doc.getElementsByClassName(this.options.bindingsClassName || '');\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getCoords(e) {\n        const coords = this.chart.pointer?.getCoordinates(e);\n        return [\n            coords && NavigationBindings_getAssignedAxis(coords.xAxis),\n            coords && NavigationBindings_getAssignedAxis(coords.yAxis)\n        ];\n    }\n    /**\n     * Init all events connected to NavigationBindings.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initEvents\n     */\n    initEvents() {\n        const navigation = this, chart = navigation.chart, bindingsContainer = navigation.container, options = navigation.options;\n        // Shorthand object for getting events for buttons:\n        navigation.boundClassNames = {};\n        NavigationBindings_objectEach((options.bindings || {}), (value) => {\n            navigation.boundClassNames[value.className] = value;\n        });\n        // Handle multiple containers with the same class names:\n        [].forEach.call(bindingsContainer, (subContainer) => {\n            navigation.eventsToUnbind.push(NavigationBindings_addEvent(subContainer, 'click', (event) => {\n                const bindings = navigation.getButtonEvents(subContainer, event);\n                if (bindings &&\n                    (!bindings.button.classList\n                        .contains('highcharts-disabled-btn'))) {\n                    navigation.bindingsButtonClick(bindings.button, bindings.events, event);\n                }\n            }));\n        });\n        NavigationBindings_objectEach((options.events || {}), (callback, eventName) => {\n            if (isFunction(callback)) {\n                navigation.eventsToUnbind.push(NavigationBindings_addEvent(navigation, eventName, callback, { passive: false }));\n            }\n        });\n        navigation.eventsToUnbind.push(NavigationBindings_addEvent(chart.container, 'click', function (e) {\n            if (!chart.cancelClick &&\n                chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                navigation.bindingsChartClick(this, e);\n            }\n        }));\n        navigation.eventsToUnbind.push(NavigationBindings_addEvent(chart.container, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? 'touchmove' : 'mousemove', function (e) {\n            navigation.bindingsContainerMouseMove(this, e);\n        }, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? { passive: false } : void 0));\n    }\n    /**\n     * Common chart.update() delegation, shared between bindings and exporting.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initUpdate\n     */\n    initUpdate() {\n        const navigation = this;\n        Chart_ChartNavigationComposition\n            .compose(this.chart).navigation\n            .addUpdate((options) => {\n            navigation.update(options);\n        });\n    }\n    /**\n     * Hook for click on a button, method selects/unselects buttons,\n     * then calls `bindings.init` callback.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsButtonClick\n     *\n     * @param {Highcharts.HTMLDOMElement} [button]\n     *        Clicked button\n     *\n     * @param {Object} events\n     *        Events passed down from bindings (`init`, `start`, `step`, `end`)\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event\n     */\n    bindingsButtonClick(button, events, clickEvent) {\n        const navigation = this, chart = navigation.chart, svgContainer = chart.renderer.boxWrapper;\n        let shouldEventBeFired = true;\n        if (navigation.selectedButtonElement) {\n            if (navigation.selectedButtonElement.classList === button.classList) {\n                shouldEventBeFired = false;\n            }\n            NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n            if (navigation.nextEvent) {\n                // Remove in-progress annotations adders:\n                if (navigation.currentUserDetails &&\n                    navigation.currentUserDetails.coll === 'annotations') {\n                    chart.removeAnnotation(navigation.currentUserDetails);\n                }\n                navigation.mouseMoveEvent = navigation.nextEvent = false;\n            }\n        }\n        if (shouldEventBeFired) {\n            navigation.selectedButton = events;\n            navigation.selectedButtonElement = button;\n            NavigationBindings_fireEvent(navigation, 'selectButton', { button: button });\n            // Call \"init\" event, for example to open modal window\n            if (events.init) {\n                events.init.call(navigation, button, clickEvent);\n            }\n            if (events.start || events.steps) {\n                chart.renderer.boxWrapper.addClass('highcharts-draw-mode');\n            }\n        }\n        else {\n            chart.stockTools && button.classList.remove('highcharts-active');\n            svgContainer.removeClass('highcharts-draw-mode');\n            navigation.nextEvent = false;\n            navigation.mouseMoveEvent = false;\n            navigation.selectedButton = null;\n        }\n    }\n    /**\n     * Hook for click on a chart, first click on a chart calls `start` event,\n     * then on all subsequent clicks iterate over `steps` array.\n     * When finished, calls `end` event.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsChartClick\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that click was performed on.\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event.\n     */\n    bindingsChartClick(chart, clickEvent) {\n        chart = this.chart;\n        const navigation = this, activeAnnotation = navigation.activeAnnotation, selectedButton = navigation.selectedButton, svgContainer = chart.renderer.boxWrapper;\n        if (activeAnnotation) {\n            // Click outside popups, should close them and deselect the\n            // annotation\n            if (!activeAnnotation.cancelClick && // #15729\n                !clickEvent.activeAnnotation &&\n                // Element could be removed in the child action, e.g. button\n                clickEvent.target.parentNode &&\n                // TO DO: Polyfill for IE11?\n                !closestPolyfill(clickEvent.target, '.highcharts-popup')) {\n                NavigationBindings_fireEvent(navigation, 'closePopup');\n            }\n            else if (activeAnnotation.cancelClick) {\n                // Reset cancelClick after the other event handlers have run\n                setTimeout(() => {\n                    activeAnnotation.cancelClick = false;\n                }, 0);\n            }\n        }\n        if (!selectedButton || !selectedButton.start) {\n            return;\n        }\n        if (!navigation.nextEvent) {\n            // Call init method:\n            navigation.currentUserDetails = selectedButton.start.call(navigation, clickEvent);\n            // If steps exists (e.g. Annotations), bind them:\n            if (navigation.currentUserDetails && selectedButton.steps) {\n                navigation.stepIndex = 0;\n                navigation.steps = true;\n                navigation.mouseMoveEvent = navigation.nextEvent =\n                    selectedButton.steps[navigation.stepIndex];\n            }\n            else {\n                NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                svgContainer.removeClass('highcharts-draw-mode');\n                navigation.steps = false;\n                navigation.selectedButton = null;\n                // First click is also the last one:\n                if (selectedButton.end) {\n                    selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                }\n            }\n        }\n        else {\n            navigation.nextEvent(clickEvent, navigation.currentUserDetails);\n            if (navigation.steps) {\n                navigation.stepIndex++;\n                if (selectedButton.steps[navigation.stepIndex]) {\n                    // If we have more steps, bind them one by one:\n                    navigation.mouseMoveEvent = navigation.nextEvent = selectedButton.steps[navigation.stepIndex];\n                }\n                else {\n                    NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                    svgContainer.removeClass('highcharts-draw-mode');\n                    // That was the last step, call end():\n                    if (selectedButton.end) {\n                        selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                    }\n                    navigation.nextEvent = false;\n                    navigation.mouseMoveEvent = false;\n                    navigation.selectedButton = null;\n                }\n            }\n        }\n    }\n    /**\n     * Hook for mouse move on a chart's container. It calls current step.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsContainerMouseMove\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Chart's container.\n     *\n     * @param {global.Event} moveEvent\n     *        Browser's move event.\n     */\n    bindingsContainerMouseMove(_container, moveEvent) {\n        if (this.mouseMoveEvent) {\n            this.mouseMoveEvent(moveEvent, this.currentUserDetails);\n        }\n    }\n    /**\n     * Translate fields (e.g. `params.period` or `marker.styles.color`) to\n     * Highcharts options object (e.g. `{ params: { period } }`).\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#fieldsToOptions<T>\n     *\n     * @param {Highcharts.Dictionary<string>} fields\n     *        Fields from popup form.\n     *\n     * @param {T} config\n     *        Default config to be modified.\n     *\n     * @return {T}\n     *         Modified config\n     */\n    fieldsToOptions(fields, config) {\n        NavigationBindings_objectEach(fields, (value, field) => {\n            const parsedValue = parseFloat(value), path = field.split('.'), pathLength = path.length - 1;\n            // If it's a number (not \"format\" options), parse it:\n            if (NavigationBindings_isNumber(parsedValue) &&\n                !value.match(/px|em/g) &&\n                !field.match(/format/g)) {\n                value = parsedValue;\n            }\n            // Remove values like 0\n            if (value !== 'undefined') {\n                let parent = config;\n                path.forEach((name, index) => {\n                    if (name !== '__proto__' && name !== 'constructor') {\n                        const nextName = NavigationBindings_pick(path[index + 1], '');\n                        if (pathLength === index) {\n                            // Last index, put value:\n                            parent[name] = value;\n                        }\n                        else if (!parent[name]) {\n                            // Create middle property:\n                            parent[name] = nextName.match(/\\d/g) ?\n                                [] :\n                                {};\n                            parent = parent[name];\n                        }\n                        else {\n                            // Jump into next property\n                            parent = parent[name];\n                        }\n                    }\n                });\n            }\n        });\n        return config;\n    }\n    /**\n     * Shorthand method to deselect an annotation.\n     *\n     * @function Highcharts.NavigationBindings#deselectAnnotation\n     */\n    deselectAnnotation() {\n        if (this.activeAnnotation) {\n            this.activeAnnotation.setControlPointsVisibility(false);\n            this.activeAnnotation = false;\n        }\n    }\n    /**\n     * Generates API config for popup in the same format as options for\n     * Annotation object.\n     *\n     * @function Highcharts.NavigationBindings#annotationToFields\n     *\n     * @param {Highcharts.Annotation} annotation\n     *        Annotations object\n     *\n     * @return {Highcharts.Dictionary<string>}\n     *         Annotation options to be displayed in popup box\n     */\n    annotationToFields(annotation) {\n        const options = annotation.options, editables = NavigationBindings.annotationsEditable, nestedEditables = editables.nestedOptions, type = NavigationBindings_pick(options.type, options.shapes && options.shapes[0] &&\n            options.shapes[0].type, options.labels && options.labels[0] &&\n            options.labels[0].type, 'label'), nonEditables = NavigationBindings.annotationsNonEditable[options.langKey] || [], visualOptions = {\n            langKey: options.langKey,\n            type: type\n        };\n        /**\n         * Nested options traversing. Method goes down to the options and copies\n         * allowed options (with values) to new object, which is last parameter:\n         * \"parent\".\n         *\n         * @private\n         *\n         * @param {*} option\n         *        Atomic type or object/array\n         *\n         * @param {string} key\n         *        Option name, for example \"visible\" or \"x\", \"y\"\n         *\n         * @param {Object} parentEditables\n         *        Editables from NavigationBindings.annotationsEditable\n         *\n         * @param {Object} parent\n         *        Where new options will be assigned\n         */\n        function traverse(option, key, parentEditables, parent, parentKey) {\n            let nextParent;\n            if (parentEditables &&\n                NavigationBindings_defined(option) &&\n                nonEditables.indexOf(key) === -1 &&\n                ((parentEditables.indexOf &&\n                    parentEditables.indexOf(key)) >= 0 ||\n                    parentEditables[key] || // Nested array\n                    parentEditables === true // Simple array\n                )) {\n                // Roots:\n                if (NavigationBindings_isArray(option)) {\n                    parent[key] = [];\n                    option.forEach((arrayOption, i) => {\n                        if (!NavigationBindings_isObject(arrayOption)) {\n                            // Simple arrays, e.g. [String, Number, Boolean]\n                            traverse(arrayOption, 0, nestedEditables[key], parent[key], key);\n                        }\n                        else {\n                            // Advanced arrays, e.g. [Object, Object]\n                            parent[key][i] = {};\n                            NavigationBindings_objectEach(arrayOption, (nestedOption, nestedKey) => {\n                                traverse(nestedOption, nestedKey, nestedEditables[key], parent[key][i], key);\n                            });\n                        }\n                    });\n                }\n                else if (NavigationBindings_isObject(option)) {\n                    nextParent = {};\n                    if (NavigationBindings_isArray(parent)) {\n                        parent.push(nextParent);\n                        nextParent[key] = {};\n                        nextParent = nextParent[key];\n                    }\n                    else {\n                        parent[key] = nextParent;\n                    }\n                    NavigationBindings_objectEach(option, (nestedOption, nestedKey) => {\n                        traverse(nestedOption, nestedKey, key === 0 ?\n                            parentEditables :\n                            nestedEditables[key], nextParent, key);\n                    });\n                }\n                else {\n                    // Leaf:\n                    if (key === 'format') {\n                        parent[key] = [\n                            NavigationBindings_format(option, annotation.labels[0].points[0]).toString(),\n                            'text'\n                        ];\n                    }\n                    else if (NavigationBindings_isArray(parent)) {\n                        parent.push([option, NavigationBindings_getFieldType(parentKey, option)]);\n                    }\n                    else {\n                        parent[key] = [option, NavigationBindings_getFieldType(key, option)];\n                    }\n                }\n            }\n        }\n        NavigationBindings_objectEach(options, (option, key) => {\n            if (key === 'typeOptions') {\n                visualOptions[key] = {};\n                NavigationBindings_objectEach(options[key], (typeOption, typeKey) => {\n                    traverse(typeOption, typeKey, nestedEditables, visualOptions[key], typeKey);\n                });\n            }\n            else {\n                traverse(option, key, editables[type], visualOptions, key);\n            }\n        });\n        return visualOptions;\n    }\n    /**\n     * Get all class names for all parents in the element. Iterates until finds\n     * main container.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getClickedClassNames\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     * Container that event is bound to.\n     *\n     * @param {global.Event} event\n     * Browser's event.\n     *\n     * @return {Array<Array<string, Highcharts.HTMLDOMElement>>}\n     * Array of class names with corresponding elements\n     */\n    getClickedClassNames(container, event) {\n        let element = event.target, classNames = [], elemClassName;\n        while (element && element.tagName) {\n            elemClassName = attr(element, 'class');\n            if (elemClassName) {\n                classNames = classNames.concat(elemClassName\n                    .split(' ')\n                    // eslint-disable-next-line no-loop-func\n                    .map((name) => ([name, element])));\n            }\n            element = element.parentNode;\n            if (element === container) {\n                return classNames;\n            }\n        }\n        return classNames;\n    }\n    /**\n     * Get events bound to a button. It's a custom event delegation to find all\n     * events connected to the element.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getButtonEvents\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Container that event is bound to.\n     *\n     * @param {global.Event} event\n     *        Browser's event.\n     *\n     * @return {Object}\n     *         Object with events (init, start, steps, and end)\n     */\n    getButtonEvents(container, event) {\n        const navigation = this, classNames = this.getClickedClassNames(container, event);\n        let bindings;\n        classNames.forEach((className) => {\n            if (navigation.boundClassNames[className[0]] && !bindings) {\n                bindings = {\n                    events: navigation.boundClassNames[className[0]],\n                    button: className[1]\n                };\n            }\n        });\n        return bindings;\n    }\n    /**\n     * Bindings are just events, so the whole update process is simply\n     * removing old events and adding new ones.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#update\n     */\n    update(options) {\n        this.options = NavigationBindings_merge(true, this.options, options);\n        this.removeEvents();\n        this.initEvents();\n    }\n    /**\n     * Remove all events created in the navigation.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#removeEvents\n     */\n    removeEvents() {\n        this.eventsToUnbind.forEach((unbinder) => unbinder());\n    }\n    /**\n     * @private\n     * @function Highcharts.NavigationBindings#destroy\n     */\n    destroy() {\n        this.removeEvents();\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n// Define which options from annotations should show up in edit box:\nNavigationBindings.annotationsEditable = {\n    // `typeOptions` are always available\n    // Nested and shared options:\n    nestedOptions: {\n        labelOptions: ['style', 'format', 'backgroundColor'],\n        labels: ['style'],\n        label: ['style'],\n        style: ['fontSize', 'color'],\n        background: ['fill', 'strokeWidth', 'stroke'],\n        innerBackground: ['fill', 'strokeWidth', 'stroke'],\n        outerBackground: ['fill', 'strokeWidth', 'stroke'],\n        shapeOptions: ['fill', 'strokeWidth', 'stroke'],\n        shapes: ['fill', 'strokeWidth', 'stroke'],\n        line: ['strokeWidth', 'stroke'],\n        backgroundColors: [true],\n        connector: ['fill', 'strokeWidth', 'stroke'],\n        crosshairX: ['strokeWidth', 'stroke'],\n        crosshairY: ['strokeWidth', 'stroke']\n    },\n    // Simple shapes:\n    circle: ['shapes'],\n    ellipse: ['shapes'],\n    verticalLine: [],\n    label: ['labelOptions'],\n    // Measure\n    measure: ['background', 'crosshairY', 'crosshairX'],\n    // Others:\n    fibonacci: [],\n    tunnel: ['background', 'line', 'height'],\n    pitchfork: ['innerBackground', 'outerBackground'],\n    rect: ['shapes'],\n    // Crooked lines, elliots, arrows etc:\n    crookedLine: [],\n    basicAnnotation: ['shapes', 'labelOptions']\n};\n// Define non editable fields per annotation, for example Rectangle inherits\n// options from Measure, but crosshairs are not available\nNavigationBindings.annotationsNonEditable = {\n    rectangle: ['crosshairX', 'crosshairY', 'labelOptions'],\n    ellipse: ['labelOptions'],\n    circle: ['labelOptions']\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_NavigationBindings = (NavigationBindings);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A config object for navigation bindings in annotations.\n *\n * @interface Highcharts.NavigationBindingsOptionsObject\n */ /**\n* ClassName of the element for a binding.\n* @name Highcharts.NavigationBindingsOptionsObject#className\n* @type {string|undefined}\n*/ /**\n* Last event to be fired after last step event.\n* @name Highcharts.NavigationBindingsOptionsObject#end\n* @type {Function|undefined}\n*/ /**\n* Initial event, fired on a button click.\n* @name Highcharts.NavigationBindingsOptionsObject#init\n* @type {Function|undefined}\n*/ /**\n* Event fired on first click on a chart.\n* @name Highcharts.NavigationBindingsOptionsObject#start\n* @type {Function|undefined}\n*/ /**\n* Last event to be fired after last step event. Array of step events to be\n* called sequentially after each user click.\n* @name Highcharts.NavigationBindingsOptionsObject#steps\n* @type {Array<Function>|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/annotations.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Annotation = G.Annotation || Annotations_Annotation;\nG.NavigationBindings = G.NavigationBindings || Annotations_NavigationBindings;\nG.Annotation.compose(G.Chart, G.NavigationBindings, G.Pointer, G.SVGRenderer);\n/* harmony default export */ const annotations_src = ((/* unused pure expression or super */ null && (Highcharts)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/BasicAnnotation.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: BasicAnnotation_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass BasicAnnotation extends Annotations_Annotation {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addControlPoints() {\n        const options = this.options, controlPoints = BasicAnnotation.basicControlPoints, annotationType = this.basicType, optionsGroup = (options.labels ||\n            options.shapes ||\n            []);\n        optionsGroup.forEach((group) => {\n            group.controlPoints = controlPoints[annotationType];\n        });\n    }\n    init() {\n        const options = this.options;\n        if (options.shapes) {\n            delete options.labelOptions;\n            const type = options.shapes[0].type;\n            options.shapes[0].className =\n                (options.shapes[0].className || '') + ' highcharts-basic-shape';\n            // The rectangle is rendered as a path, whereas other basic shapes\n            // are rendered as their respective SVG shapes.\n            if (type && type !== 'path') {\n                this.basicType = type;\n            }\n            else {\n                this.basicType = 'rectangle';\n            }\n        }\n        else {\n            delete options.shapes;\n            this.basicType = 'label';\n        }\n        super.init.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBasicAnnotation.basicControlPoints = {\n    label: [{\n            symbol: 'triangle-down',\n            positioner: function (target) {\n                if (!target.graphic.placed) {\n                    return {\n                        x: 0,\n                        y: -9e7\n                    };\n                }\n                const xy = Annotations_MockPoint\n                    .pointToPixels(target.points[0]);\n                return {\n                    x: xy.x - (this.graphic.width || 0) / 2,\n                    y: xy.y - (this.graphic.height || 0) / 2\n                };\n            },\n            // TRANSLATE POINT/ANCHOR\n            events: {\n                drag: function (e, target) {\n                    const xy = this.mouseMoveToTranslation(e);\n                    target.translatePoint(xy.x, xy.y);\n                    target.annotation.userOptions.labels[0].point =\n                        target.options.point;\n                    target.redraw(false);\n                }\n            }\n        }, {\n            symbol: 'square',\n            positioner: function (target) {\n                if (!target.graphic.placed) {\n                    return {\n                        x: 0,\n                        y: -9e7\n                    };\n                }\n                return {\n                    x: target.graphic.alignAttr.x -\n                        (this.graphic.width || 0) / 2,\n                    y: target.graphic.alignAttr.y -\n                        (this.graphic.height || 0) / 2\n                };\n            },\n            // TRANSLATE POSITION WITHOUT CHANGING THE\n            // ANCHOR\n            events: {\n                drag: function (e, target) {\n                    const xy = this.mouseMoveToTranslation(e);\n                    target.translate(xy.x, xy.y);\n                    target.annotation.userOptions.labels[0].point =\n                        target.options.point;\n                    target.redraw(false);\n                }\n            }\n        }],\n    rectangle: [{\n            positioner: function (annotation) {\n                const xy = Annotations_MockPoint\n                    .pointToPixels(annotation.points[2]);\n                return {\n                    x: xy.x - 4,\n                    y: xy.y - 4\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const annotation = target.annotation, coords = this.chart.pointer?.getCoordinates(e), points = target.options.points, shapes = annotation.userOptions.shapes, xAxisIndex = annotation.clipXAxis?.index || 0, yAxisIndex = annotation.clipYAxis?.index || 0;\n                    if (coords) {\n                        const x = coords.xAxis[xAxisIndex].value, y = coords.yAxis[yAxisIndex].value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        if (shapes && shapes[0]) {\n                            shapes[0].points = target.options.points;\n                        }\n                    }\n                    annotation.redraw(false);\n                }\n            }\n        }],\n    circle: [{\n            positioner: function (target) {\n                const xy = Annotations_MockPoint.pointToPixels(target.points[0]), r = target.options.r;\n                return {\n                    x: xy.x + r * Math.cos(Math.PI / 4) -\n                        (this.graphic.width || 0) / 2,\n                    y: xy.y + r * Math.sin(Math.PI / 4) -\n                        (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                // TRANSFORM RADIUS ACCORDING TO Y\n                // TRANSLATION\n                drag: function (e, target) {\n                    const annotation = target.annotation, position = this.mouseMoveToTranslation(e), shapes = annotation.userOptions.shapes;\n                    target.setRadius(Math.max(target.options.r +\n                        position.y /\n                            Math.sin(Math.PI / 4), 5));\n                    if (shapes && shapes[0]) {\n                        shapes[0].r = target.options.r;\n                        shapes[0].point = target.options.point;\n                    }\n                    target.redraw(false);\n                }\n            }\n        }],\n    ellipse: [{\n            positioner: function (target) {\n                const position = target.getAbsolutePosition(target.points[0]);\n                return {\n                    x: position.x - (this.graphic.width || 0) / 2,\n                    y: position.y - (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const position = target.getAbsolutePosition(target.points[0]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 0);\n                    target.redraw(false);\n                }\n            }\n        }, {\n            positioner: function (target) {\n                const position = target.getAbsolutePosition(target.points[1]);\n                return {\n                    x: position.x - (this.graphic.width || 0) / 2,\n                    y: position.y - (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                }\n            }\n        }, {\n            positioner: function (target) {\n                const position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), attrs = target.getAttrs(position, position2);\n                return {\n                    x: attrs.cx - (this.graphic.width || 0) / 2 +\n                        attrs.ry * Math.sin((attrs.angle * Math.PI) / 180),\n                    y: attrs.cy - (this.graphic.height || 0) / 2 -\n                        attrs.ry * Math.cos((attrs.angle * Math.PI) / 180)\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), newR = target.getDistanceFromLine(position, position2, e.chartX, e.chartY), yAxis = target.getYAxis(), newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            }\n        }]\n};\nBasicAnnotation.prototype.defaultOptions = BasicAnnotation_merge(Annotations_Annotation.prototype.defaultOptions, {});\nAnnotations_Annotation.types.basicAnnotation = BasicAnnotation;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_BasicAnnotation = ((/* unused pure expression or super */ null && (BasicAnnotation)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/CrookedLine.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: CrookedLine_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass CrookedLine extends Annotations_Annotation {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Overrides default setter to get axes from typeOptions.\n     * @private\n     */\n    setClipAxes() {\n        this.clipXAxis = this.chart.xAxis[this.options.typeOptions.xAxis];\n        this.clipYAxis = this.chart.yAxis[this.options.typeOptions.yAxis];\n    }\n    getPointsOptions() {\n        const typeOptions = this.options.typeOptions;\n        return (typeOptions.points || []).map((pointOptions) => {\n            pointOptions.xAxis = typeOptions.xAxis;\n            pointOptions.yAxis = typeOptions.yAxis;\n            return pointOptions;\n        });\n    }\n    getControlPointsOptions() {\n        return this.getPointsOptions();\n    }\n    addControlPoints() {\n        this.getControlPointsOptions().forEach(function (pointOptions, i) {\n            const controlPoint = new Annotations_ControlPoint(this.chart, this, CrookedLine_merge(this.options.controlPointOptions, pointOptions.controlPoint), i);\n            this.controlPoints.push(controlPoint);\n            pointOptions.controlPoint = controlPoint.options;\n        }, this);\n    }\n    addShapes() {\n        const typeOptions = this.options.typeOptions, shape = this.initShape(CrookedLine_merge(typeOptions.line, {\n            type: 'path',\n            className: 'highcharts-crooked-lines',\n            points: this.points.map((_point, i) => (function (target) {\n                return target.annotation.points[i];\n            }))\n        }), 0);\n        typeOptions.line = shape.options;\n    }\n}\nCrookedLine.prototype.defaultOptions = CrookedLine_merge(Annotations_Annotation.prototype.defaultOptions, \n/**\n * A crooked line annotation.\n *\n * @sample highcharts/annotations-advanced/crooked-line/\n *         Crooked line\n *\n * @product      highstock\n * @optionparent annotations.crookedLine\n */\n{\n    /**\n     * @extends   annotations.labelOptions\n     * @apioption annotations.crookedLine.labelOptions\n     */\n    /**\n     * @extends   annotations.shapeOptions\n     * @apioption annotations.crookedLine.shapeOptions\n     */\n    /**\n     * Additional options for an annotation with the type.\n     */\n    typeOptions: {\n        /**\n         * This number defines which xAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the xAxis array.\n         */\n        xAxis: 0,\n        /**\n         * This number defines which yAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the xAxis array.\n         */\n        yAxis: 0,\n        /**\n         * @type      {Array<*>}\n         * @apioption annotations.crookedLine.typeOptions.points\n         */\n        /**\n         * The x position of the point.\n         *\n         * @type      {number}\n         * @apioption annotations.crookedLine.typeOptions.points.x\n         */\n        /**\n         * The y position of the point.\n         *\n         * @type      {number}\n         * @apioption annotations.crookedLine.typeOptions.points.y\n         */\n        /**\n         * @type      {number}\n         * @excluding positioner, events\n         * @apioption annotations.crookedLine.typeOptions.points.controlPoint\n         */\n        /**\n         * Line options.\n         *\n         * @excluding height, point, points, r, type, width\n         */\n        line: {\n            fill: 'none'\n        }\n    },\n    /**\n     * @excluding positioner, events\n     */\n    controlPointOptions: {\n        positioner: function (target) {\n            const graphic = this.graphic, xy = Annotations_MockPoint.pointToPixels(target.points[this.index]);\n            return {\n                x: xy.x - (graphic.width || 0) / 2,\n                y: xy.y - (graphic.height || 0) / 2\n            };\n        },\n        events: {\n            drag: function (e, target) {\n                if (target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                    const translation = this.mouseMoveToTranslation(e), typeOptions = target.options.typeOptions;\n                    target.translatePoint(translation.x, translation.y, this.index);\n                    // Update options:\n                    typeOptions.points[this.index].x =\n                        target.points[this.index].x;\n                    typeOptions.points[this.index].y =\n                        target.points[this.index].y;\n                    target.redraw(false);\n                }\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.crookedLine = CrookedLine;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_CrookedLine = (CrookedLine);\n\n;// ./code/es-modules/Extensions/Annotations/Types/ElliottWave.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ElliottWave_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass ElliottWave extends Types_CrookedLine {\n    /* *\n     *\n     * Functions\n     *\n     * */\n    addLabels() {\n        this.getPointsOptions().forEach((point, i) => {\n            const typeOptions = this.options.typeOptions, label = this.initLabel(ElliottWave_merge(point.label, {\n                text: typeOptions.labels[i],\n                point: function (target) {\n                    return target.annotation.points[i];\n                }\n            }), false);\n            point.label = label.options;\n        });\n    }\n}\nElliottWave.prototype.defaultOptions = ElliottWave_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * An elliott wave annotation.\n *\n * @sample highcharts/annotations-advanced/elliott-wave/\n *         Elliott wave\n *\n * @extends      annotations.crookedLine\n * @product      highstock\n * @optionparent annotations.elliottWave\n */\n{\n    typeOptions: {\n        /**\n         * @extends   annotations.crookedLine.labelOptions\n         * @apioption annotations.elliottWave.typeOptions.points.label\n         */\n        /**\n         * @ignore-option\n         */\n        labels: ['(0)', '(A)', '(B)', '(C)', '(D)', '(E)'],\n        line: {\n            strokeWidth: 1\n        }\n    },\n    labelOptions: {\n        align: 'center',\n        allowOverlap: true,\n        crop: true,\n        overflow: 'none',\n        type: 'rect',\n        backgroundColor: 'none',\n        borderWidth: 0,\n        y: -5\n    }\n});\nAnnotations_Annotation.types.elliottWave = ElliottWave;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_ElliottWave = ((/* unused pure expression or super */ null && (ElliottWave)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Tunnel.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { merge: Tunnel_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction getSecondCoordinate(p1, p2, x) {\n    return (p2.y - p1.y) / (p2.x - p1.x) * (x - p1.x) + p1.y;\n}\n/* *\n *\n *  Class\n *\n * */\nclass Tunnel extends Types_CrookedLine {\n    /* *\n     *\n     * Functions\n     *\n     * */\n    getPointsOptions() {\n        const pointsOptions = Types_CrookedLine.prototype.getPointsOptions.call(this), yAxisIndex = this.options.typeOptions.yAxis || 0, yAxis = this.chart.yAxis[yAxisIndex];\n        pointsOptions[2] = this.heightPointOptions(pointsOptions[1]);\n        pointsOptions[3] = this.heightPointOptions(pointsOptions[0]);\n        // In case of log axis, translate the bottom left point again, #16769\n        if (yAxis && yAxis.logarithmic) {\n            // Get the height in pixels\n            const h = yAxis.toPixels(pointsOptions[2].y) -\n                yAxis.toPixels(pointsOptions[1].y), \n            // Get the pixel position of the last point\n            y3 = yAxis.toPixels(pointsOptions[0].y) + h;\n            // Set the new value\n            pointsOptions[3].y = yAxis.toValue(y3);\n        }\n        return pointsOptions;\n    }\n    getControlPointsOptions() {\n        return this.getPointsOptions().slice(0, 2);\n    }\n    heightPointOptions(pointOptions) {\n        const heightPointOptions = Tunnel_merge(pointOptions), typeOptions = this.options.typeOptions;\n        heightPointOptions.y += typeOptions.height;\n        return heightPointOptions;\n    }\n    addControlPoints() {\n        Types_CrookedLine.prototype.addControlPoints.call(this);\n        const options = this.options, typeOptions = options.typeOptions, controlPoint = new Annotations_ControlPoint(this.chart, this, Tunnel_merge(options.controlPointOptions, typeOptions.heightControlPoint), 2);\n        this.controlPoints.push(controlPoint);\n        typeOptions.heightControlPoint = controlPoint.options;\n    }\n    addShapes() {\n        this.addLine();\n        this.addBackground();\n    }\n    addLine() {\n        const line = this.initShape(Tunnel_merge(this.options.typeOptions.line, {\n            type: 'path',\n            points: [\n                this.points[0],\n                this.points[1],\n                function (target) {\n                    const pointOptions = Annotations_MockPoint.pointToOptions(target.annotation.points[2]);\n                    pointOptions.command = 'M';\n                    return pointOptions;\n                },\n                this.points[3]\n            ],\n            className: 'highcharts-tunnel-lines'\n        }), 0);\n        this.options.typeOptions.line = line.options;\n    }\n    addBackground() {\n        const background = this.initShape(Tunnel_merge(this.options.typeOptions.background, {\n            type: 'path',\n            points: this.points.slice(),\n            className: 'highcharts-tunnel-background'\n        }), 1);\n        this.options.typeOptions.background = background.options;\n    }\n    /**\n     * Translate start or end (\"left\" or \"right\") side of the tunnel.\n     * @private\n     * @param {number} dx\n     * the amount of x translation\n     * @param {number} dy\n     * the amount of y translation\n     * @param {boolean} [end]\n     * whether to translate start or end side\n     */\n    translateSide(dx, dy, end) {\n        const topIndex = Number(end), bottomIndex = topIndex === 0 ? 3 : 2;\n        this.translatePoint(dx, dy, topIndex);\n        this.translatePoint(dx, dy, bottomIndex);\n    }\n    /**\n     * Translate height of the tunnel.\n     * @private\n     * @param {number} dh\n     * the amount of height translation\n     */\n    translateHeight(dh) {\n        this.translatePoint(0, dh, 2);\n        this.translatePoint(0, dh, 3);\n        this.options.typeOptions.height = this.points[3].y -\n            this.points[0].y;\n        this.userOptions.typeOptions.height = this.options.typeOptions.height;\n    }\n}\nTunnel.prototype.defaultOptions = Tunnel_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * A tunnel annotation.\n *\n * @extends annotations.crookedLine\n * @sample highcharts/annotations-advanced/tunnel/\n *         Tunnel\n * @product highstock\n * @optionparent annotations.tunnel\n */\n{\n    typeOptions: {\n        /**\n         * Background options.\n         *\n         * @type {Object}\n         * @excluding height, point, points, r, type, width, markerEnd,\n         *            markerStart\n         */\n        background: {\n            fill: 'rgba(130, 170, 255, 0.4)',\n            strokeWidth: 0\n        },\n        line: {\n            strokeWidth: 1\n        },\n        /**\n         * The height of the annotation in terms of yAxis.\n         */\n        height: -2,\n        /**\n         * Options for the control point which controls\n         * the annotation's height.\n         *\n         * @extends annotations.crookedLine.controlPointOptions\n         * @excluding positioner, events\n         */\n        heightControlPoint: {\n            positioner: function (target) {\n                const startXY = Annotations_MockPoint.pointToPixels(target.points[2]), endXY = Annotations_MockPoint.pointToPixels(target.points[3]), x = (startXY.x + endXY.x) / 2;\n                return {\n                    x: x - (this.graphic.width || 0) / 2,\n                    y: getSecondCoordinate(startXY, endXY, x) -\n                        (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    if (target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                        visiblePlotOnly: true\n                    })) {\n                        target.translateHeight(this.mouseMoveToTranslation(e).y);\n                        target.redraw(false);\n                    }\n                }\n            }\n        }\n    },\n    /**\n     * @extends annotations.crookedLine.controlPointOptions\n     * @excluding positioner, events\n     */\n    controlPointOptions: {\n        events: {\n            drag: function (e, target) {\n                if (target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                    const translation = this.mouseMoveToTranslation(e);\n                    target.translateSide(translation.x, translation.y, !!this.index);\n                    target.redraw(false);\n                }\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.tunnel = Tunnel;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Tunnel = (Tunnel);\n\n;// ./code/es-modules/Extensions/Annotations/Types/InfinityLine.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: InfinityLine_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass InfinityLine extends Types_CrookedLine {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static edgePoint(startIndex, endIndex) {\n        return function (target) {\n            const annotation = target.annotation, type = annotation.options.typeOptions.type;\n            let points = annotation.points;\n            if (type === 'horizontalLine' || type === 'verticalLine') {\n                // Horizontal and vertical lines have only one point,\n                // make a copy of it:\n                points = [\n                    points[0],\n                    new Annotations_MockPoint(annotation.chart, points[0].target, {\n                        // Add 0 or 1 to x or y depending on type\n                        x: points[0].x + +(type === 'horizontalLine'),\n                        y: points[0].y + +(type === 'verticalLine'),\n                        xAxis: points[0].options.xAxis,\n                        yAxis: points[0].options.yAxis\n                    })\n                ];\n            }\n            return InfinityLine.findEdgePoint(points[startIndex], points[endIndex]);\n        };\n    }\n    static findEdgeCoordinate(firstPoint, secondPoint, xOrY, edgePointFirstCoordinate) {\n        const xOrYOpposite = xOrY === 'x' ? 'y' : 'x';\n        // Solves equation for x or y\n        // y - y1 = (y2 - y1) / (x2 - x1) * (x - x1)\n        return ((secondPoint[xOrY] - firstPoint[xOrY]) *\n            (edgePointFirstCoordinate - firstPoint[xOrYOpposite]) /\n            (secondPoint[xOrYOpposite] - firstPoint[xOrYOpposite]) +\n            firstPoint[xOrY]);\n    }\n    static findEdgePoint(firstPoint, secondPoint) {\n        const chart = firstPoint.series.chart, xAxis = firstPoint.series.xAxis, yAxis = secondPoint.series.yAxis, firstPointPixels = Annotations_MockPoint.pointToPixels(firstPoint), secondPointPixels = Annotations_MockPoint.pointToPixels(secondPoint), deltaX = secondPointPixels.x - firstPointPixels.x, deltaY = secondPointPixels.y - firstPointPixels.y, xAxisMin = xAxis.left, xAxisMax = xAxisMin + xAxis.width, yAxisMin = yAxis.top, yAxisMax = yAxisMin + yAxis.height, xLimit = deltaX < 0 ? xAxisMin : xAxisMax, yLimit = deltaY < 0 ? yAxisMin : yAxisMax, edgePoint = {\n            x: deltaX === 0 ? firstPointPixels.x : xLimit,\n            y: deltaY === 0 ? firstPointPixels.y : yLimit\n        };\n        let edgePointX, edgePointY, swap;\n        if (deltaX !== 0 && deltaY !== 0) {\n            edgePointY = InfinityLine.findEdgeCoordinate(firstPointPixels, secondPointPixels, 'y', xLimit);\n            edgePointX = InfinityLine.findEdgeCoordinate(firstPointPixels, secondPointPixels, 'x', yLimit);\n            if (edgePointY >= yAxisMin && edgePointY <= yAxisMax) {\n                edgePoint.x = xLimit;\n                edgePoint.y = edgePointY;\n            }\n            else {\n                edgePoint.x = edgePointX;\n                edgePoint.y = yLimit;\n            }\n        }\n        edgePoint.x -= chart.plotLeft;\n        edgePoint.y -= chart.plotTop;\n        if (firstPoint.series.chart.inverted) {\n            swap = edgePoint.x;\n            edgePoint.x = edgePoint.y;\n            edgePoint.y = swap;\n        }\n        return edgePoint;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addShapes() {\n        const typeOptions = this.options.typeOptions, points = [\n            this.points[0],\n            InfinityLine.endEdgePoint\n        ];\n        // Be case-insensitive (#15155) e.g.:\n        // - line\n        // - horizontalLine\n        // - verticalLine\n        if (typeOptions.type.match(/line/gi)) {\n            points[0] = InfinityLine.startEdgePoint;\n        }\n        const line = this.initShape(InfinityLine_merge(typeOptions.line, {\n            type: 'path',\n            points: points,\n            className: 'highcharts-infinity-lines'\n        }), 0);\n        typeOptions.line = line.options;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nInfinityLine.endEdgePoint = InfinityLine.edgePoint(0, 1);\nInfinityLine.startEdgePoint = InfinityLine.edgePoint(1, 0);\nInfinityLine.prototype.defaultOptions = InfinityLine_merge(Types_CrookedLine.prototype.defaultOptions, {});\nAnnotations_Annotation.types.infinityLine = InfinityLine;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_InfinityLine = (InfinityLine);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * An infinity line annotation.\n *\n * @sample highcharts/annotations-advanced/infinity-line/\n *         Infinity Line\n *\n * @extends   annotations.crookedLine\n * @product   highstock\n * @apioption annotations.infinityLine\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/Extensions/Annotations/Types/TimeCycles.js\n/* *\n *\n *  Authors: <AUTHORS>\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: TimeCycles_merge, isNumber: TimeCycles_isNumber, defined: TimeCycles_defined } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Function to create start of the path.\n * @param {number} x x position of the TimeCycles\n * @param {number} y y position of the TimeCycles\n * @return {string} path\n */\nfunction getStartingPath(x, y) {\n    return ['M', x, y];\n}\n/**\n * Function which generates the path of the halfcircle.\n *\n * @param {number} pixelInterval diameter of the circle in pixels\n * @param {number} numberOfCircles number of cricles\n * @param {number} startX x position of the first circle\n * @param {number} y y position of the bottom of the timeCycles\n * @return {string} path\n *\n */\nfunction getCirclePath(pixelInterval, numberOfCircles, startX, y) {\n    const path = [];\n    for (let i = 1; i <= numberOfCircles; i++) {\n        path.push([\n            'A',\n            pixelInterval / 2,\n            pixelInterval / 2,\n            0,\n            1,\n            1,\n            startX + i * pixelInterval,\n            y\n        ]);\n    }\n    return path;\n}\n/* *\n *\n *  Class\n *\n * */\nclass TimeCycles extends Types_CrookedLine {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(annotation, userOptions, index) {\n        if (TimeCycles_defined(userOptions.yAxis)) {\n            userOptions.points.forEach((point) => {\n                point.yAxis = userOptions.yAxis;\n            });\n        }\n        if (TimeCycles_defined(userOptions.xAxis)) {\n            userOptions.points.forEach((point) => {\n                point.xAxis = userOptions.xAxis;\n            });\n        }\n        super.init(annotation, userOptions, index);\n    }\n    setPath() {\n        this.shapes[0].options.d = this.getPath();\n    }\n    getPath() {\n        return [getStartingPath(this.startX, this.y)].concat(getCirclePath(this.pixelInterval, this.numberOfCircles, this.startX, this.y));\n    }\n    addShapes() {\n        const typeOptions = this.options.typeOptions;\n        this.setPathProperties();\n        const shape = this.initShape(TimeCycles_merge(typeOptions.line, {\n            type: 'path',\n            d: this.getPath(),\n            points: this.options.points,\n            className: 'highcharts-timecycles-lines'\n        }), 0);\n        typeOptions.line = shape.options;\n    }\n    addControlPoints() {\n        const options = this.options, typeOptions = options.typeOptions;\n        options.controlPointOptions.style.cursor = this.chart.inverted ?\n            'ns-resize' :\n            'ew-resize';\n        typeOptions.controlPointOptions.forEach((option) => {\n            const controlPointsOptions = TimeCycles_merge(options.controlPointOptions, option);\n            const controlPoint = new Annotations_ControlPoint(this.chart, this, controlPointsOptions, 0);\n            this.controlPoints.push(controlPoint);\n        });\n    }\n    setPathProperties() {\n        const options = this.options.typeOptions, points = options.points;\n        if (!points) {\n            return;\n        }\n        const point1 = points[0], point2 = points[1], xAxisNumber = options.xAxis || 0, yAxisNumber = options.yAxis || 0, xAxis = this.chart.xAxis[xAxisNumber], yAxis = this.chart.yAxis[yAxisNumber], xValue1 = point1.x, yValue = point1.y, xValue2 = point2.x;\n        if (!xValue1 || !xValue2) {\n            return;\n        }\n        const y = TimeCycles_isNumber(yValue) ?\n            yAxis.toPixels(yValue) :\n            yAxis.top + yAxis.height, x = TimeCycles_isNumber(xValue1) ? xAxis.toPixels(xValue1) : xAxis.left, x2 = TimeCycles_isNumber(xValue2) ? xAxis.toPixels(xValue2) : xAxis.left + 30, xAxisLength = xAxis.len, pixelInterval = Math.round(Math.max(Math.abs(x2 - x), 2)), \n        // There can be 2 not full circles on the chart, so add 2.\n        numberOfCircles = Math.floor(xAxisLength / pixelInterval) + 2, \n        // Calculate where the annotation should start drawing relative to\n        // first point.\n        pixelShift = (Math.floor((x - xAxis.left) / pixelInterval) + 1) * pixelInterval;\n        this.startX = x - pixelShift;\n        this.y = y;\n        this.pixelInterval = pixelInterval;\n        this.numberOfCircles = numberOfCircles;\n    }\n    redraw(animation) {\n        this.setPathProperties();\n        this.setPath();\n        super.redraw(animation);\n    }\n}\nTimeCycles.prototype.defaultOptions = TimeCycles_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * The TimeCycles Annotation\n *\n * @sample highcharts/annotations-advanced/time-cycles/\n *         Time Cycles annotation\n *\n * @extends      annotations.crookedLine\n * @product      highstock\n * @exclude      labelOptions\n * @optionparent annotations.timeCycles\n */\n{\n    typeOptions: {\n        /**\n         * @exclude   y\n         * @product   highstock\n         * @apioption annotations.timeCycles.typeOptions.points\n         */\n        controlPointOptions: [{\n                positioner: function (target) {\n                    const point = target.points[0], position = target.anchor(point).absolutePosition;\n                    return {\n                        x: position.x - (this.graphic.width || 0) / 2,\n                        y: target.y - (this.graphic.height || 0)\n                    };\n                },\n                events: {\n                    drag: function (e, target) {\n                        const position = target.anchor(target.points[0]).absolutePosition;\n                        target.translatePoint(e.chartX - position.x, 0, 0);\n                        target.redraw(false);\n                    }\n                }\n            }, {\n                positioner: function (target) {\n                    const point = target.points[1], position = target.anchor(point).absolutePosition;\n                    return {\n                        x: position.x - (this.graphic.width || 0) / 2,\n                        y: target.y - (this.graphic.height || 0)\n                    };\n                },\n                events: {\n                    drag: function (e, target) {\n                        const position = target.anchor(target.points[1]).absolutePosition;\n                        target.translatePoint(e.chartX - position.x, 0, 1);\n                        target.redraw(false);\n                    }\n                }\n            }]\n    }\n});\nAnnotations_Annotation.types.timeCycles = TimeCycles;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_TimeCycles = ((/* unused pure expression or super */ null && (TimeCycles)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Fibonacci.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: Fibonacci_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction createPathDGenerator(retracementIndex, isBackground) {\n    return function () {\n        const annotation = this.annotation;\n        if (!annotation.startRetracements || !annotation.endRetracements) {\n            return [];\n        }\n        const leftTop = this.anchor(annotation.startRetracements[retracementIndex]).absolutePosition, rightTop = this.anchor(annotation.endRetracements[retracementIndex]).absolutePosition, d = [\n            ['M', Math.round(leftTop.x), Math.round(leftTop.y)],\n            ['L', Math.round(rightTop.x), Math.round(rightTop.y)]\n        ];\n        if (isBackground) {\n            const rightBottom = this.anchor(annotation.endRetracements[retracementIndex - 1]).absolutePosition;\n            const leftBottom = this.anchor(annotation.startRetracements[retracementIndex - 1]).absolutePosition;\n            d.push(['L', Math.round(rightBottom.x), Math.round(rightBottom.y)], ['L', Math.round(leftBottom.x), Math.round(leftBottom.y)]);\n        }\n        return d;\n    };\n}\n/* *\n *\n *  Class\n *\n * */\nclass Fibonacci extends Types_Tunnel {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    linkPoints() {\n        super.linkPoints();\n        this.linkRetracementsPoints();\n        return;\n    }\n    linkRetracementsPoints() {\n        const points = this.points, startDiff = points[0].y - points[3].y, endDiff = points[1].y - points[2].y, startX = points[0].x, endX = points[1].x;\n        Fibonacci.levels.forEach((level, i) => {\n            const startRetracement = points[0].y - startDiff * level, endRetracement = points[1].y - endDiff * level, index = this.options.typeOptions.reversed ?\n                (Fibonacci.levels.length - i - 1) : i;\n            this.startRetracements = this.startRetracements || [];\n            this.endRetracements = this.endRetracements || [];\n            this.linkRetracementPoint(index, startX, startRetracement, this.startRetracements);\n            this.linkRetracementPoint(index, endX, endRetracement, this.endRetracements);\n        });\n    }\n    linkRetracementPoint(pointIndex, x, y, retracements) {\n        const point = retracements[pointIndex], typeOptions = this.options.typeOptions;\n        if (!point) {\n            retracements[pointIndex] = new Annotations_MockPoint(this.chart, this, {\n                x: x,\n                y: y,\n                xAxis: typeOptions.xAxis,\n                yAxis: typeOptions.yAxis\n            });\n        }\n        else {\n            point.options.x = x;\n            point.options.y = y;\n            point.refresh();\n        }\n    }\n    addShapes() {\n        Fibonacci.levels.forEach(function (_level, i) {\n            const { backgroundColors, lineColor, lineColors } = this.options.typeOptions;\n            this.initShape({\n                type: 'path',\n                d: createPathDGenerator(i),\n                stroke: lineColors[i] || lineColor,\n                className: 'highcharts-fibonacci-line'\n            }, i);\n            if (i > 0) {\n                this.initShape({\n                    type: 'path',\n                    fill: backgroundColors[i - 1],\n                    strokeWidth: 0,\n                    d: createPathDGenerator(i, true),\n                    className: 'highcharts-fibonacci-background-' + (i - 1)\n                });\n            }\n        }, this);\n    }\n    addLabels() {\n        Fibonacci.levels.forEach(function (level, i) {\n            const options = this.options.typeOptions, label = this.initLabel(Fibonacci_merge(options.labels[i], {\n                point: function (target) {\n                    const point = Annotations_MockPoint.pointToOptions(target.annotation.startRetracements[i]);\n                    return point;\n                },\n                text: level.toString()\n            }));\n            options.labels[i] = label.options;\n        }, this);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nFibonacci.levels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1];\nFibonacci.prototype.defaultOptions = Fibonacci_merge(Types_Tunnel.prototype.defaultOptions, \n/**\n * A fibonacci annotation.\n *\n * @sample highcharts/annotations-advanced/fibonacci/\n *         Fibonacci\n *\n * @extends      annotations.crookedLine\n * @product      highstock\n * @optionparent annotations.fibonacci\n */\n{\n    typeOptions: {\n        /**\n         * Whether the annotation levels should be reversed. By default they\n         * start from 0 and go to 1.\n         *\n         * @sample highcharts/annotations-advanced/fibonacci-reversed/\n         *         Fibonacci annotation reversed\n         *\n         * @type {boolean}\n         * @apioption annotations.fibonacci.typeOptions.reversed\n         */\n        reversed: false,\n        /**\n         * The height of the fibonacci in terms of yAxis.\n         */\n        height: 2,\n        /**\n         * An array of background colors:\n         * Default to:\n         * ```\n         * [\n         * 'rgba(130, 170, 255, 0.4)',\n         * 'rgba(139, 191, 216, 0.4)',\n         * 'rgba(150, 216, 192, 0.4)',\n         * 'rgba(156, 229, 161, 0.4)',\n         * 'rgba(162, 241, 130, 0.4)',\n         * 'rgba(169, 255, 101, 0.4)'\n         * ]\n         * ```\n         */\n        backgroundColors: [\n            'rgba(130, 170, 255, 0.4)',\n            'rgba(139, 191, 216, 0.4)',\n            'rgba(150, 216, 192, 0.4)',\n            'rgba(156, 229, 161, 0.4)',\n            'rgba(162, 241, 130, 0.4)',\n            'rgba(169, 255, 101, 0.4)'\n        ],\n        /**\n         * The color of line.\n         */\n        lineColor: \"#999999\" /* Palette.neutralColor40 */,\n        /**\n         * An array of colors for the lines.\n         */\n        lineColors: [],\n        /**\n         * An array with options for the labels.\n         *\n         * @type      {Array<*>}\n         * @extends   annotations.crookedLine.labelOptions\n         * @apioption annotations.fibonacci.typeOptions.labels\n         */\n        labels: []\n    },\n    labelOptions: {\n        allowOverlap: true,\n        align: 'right',\n        backgroundColor: 'none',\n        borderWidth: 0,\n        crop: false,\n        overflow: 'none',\n        shape: 'rect',\n        style: {\n            color: 'grey'\n        },\n        verticalAlign: 'middle',\n        y: 0\n    }\n});\nAnnotations_Annotation.types.fibonacci = Fibonacci;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Fibonacci = ((/* unused pure expression or super */ null && (Fibonacci)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/FibonacciTimeZones.js\n/* *\n *\n *  Author: Rafal Sebestjanski\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\nconst { merge: FibonacciTimeZones_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\nMethod taken (and slightly changed) from the InfinityLine annotation.\n\nIt uses x coordinate to create two mock points on the same x. Then,\nit uses some logic from InfinityLine to find equation of the line passing\nthrough our two points and, using that equation, it finds and returns\nthe coordinates of where the line intersects the plot area edges.\n\nThis is being done for each fibonacci time zone line.\n\n\n        this point here is found\n            |\n            v\n    |---------*--------------------------------------------------------|\n    |                                                                  |\n    |                                                                  |\n    |                                                                  |\n    |                                                                  |\n    |         *   copy of the primary point                            |\n    |                                                                  |\n    |         *   primary point (e.g. the one given in options)        |\n    |                                                                  |\n    |---------*--------------------------------------------------------|\n        and this point here is found (intersection with the plot area edge)\n\n* @private\n*/\nfunction edgePoint(startIndex, endIndex, fibonacciIndex) {\n    return function (target) {\n        const chart = target.annotation.chart, plotLeftOrTop = chart.inverted ? chart.plotTop : chart.plotLeft;\n        let points = target.annotation.points;\n        const xAxis = points[0].series.xAxis, \n        // Distance between the two first lines in pixels\n        deltaX = points.length > 1 ?\n            points[1].plotX - points[0].plotX : 0, \n        // `firstLine.x + fibb * offset`\n        x = xAxis.toValue(points[0].plotX + plotLeftOrTop + fibonacciIndex * deltaX);\n        // We need 2 mock points with the same x coordinate, different y\n        points = [\n            new Annotations_MockPoint(chart, points[0].target, {\n                x: x,\n                y: 0,\n                xAxis: points[0].options.xAxis,\n                yAxis: points[0].options.yAxis\n            }),\n            new Annotations_MockPoint(chart, points[0].target, {\n                x: x,\n                y: 1,\n                xAxis: points[0].options.xAxis,\n                yAxis: points[0].options.yAxis\n            })\n        ];\n        return Types_InfinityLine.findEdgePoint(points[startIndex], points[endIndex]);\n    };\n}\n/* *\n *\n *  Class\n *\n * */\nclass FibonacciTimeZones extends Types_CrookedLine {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addShapes() {\n        const numberOfLines = 11;\n        let fibb = 1, nextFibb = 1;\n        for (let i = 0; i < numberOfLines; i++) {\n            // The fibb variable equals to 1 twice - correct it in the first\n            // iteration so the lines don't overlap\n            const correctedFibb = !i ? 0 : fibb, points = [\n                edgePoint(1, 0, correctedFibb),\n                edgePoint(0, 1, correctedFibb)\n            ];\n            // Calculate fibonacci\n            nextFibb = fibb + nextFibb;\n            fibb = nextFibb - fibb;\n            // Save the second line for the control point\n            if (i === 1) {\n                this.secondLineEdgePoints = [points[0], points[1]];\n            }\n            this.initShape(FibonacciTimeZones_merge(this.options.typeOptions.line, {\n                type: 'path',\n                points: points,\n                className: 'highcharts-fibonacci-timezones-lines'\n            }), i // Shape's index. Can be found in annotation.shapes[i].index\n            );\n        }\n    }\n    addControlPoints() {\n        const options = this.options, typeOptions = options.typeOptions, controlPoint = new Annotations_ControlPoint(this.chart, this, FibonacciTimeZones_merge(options.controlPointOptions, typeOptions.controlPointOptions), 0);\n        this.controlPoints.push(controlPoint);\n        typeOptions.controlPointOptions = controlPoint.options;\n    }\n}\nFibonacciTimeZones.prototype.defaultOptions = FibonacciTimeZones_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * The Fibonacci Time Zones annotation.\n *\n * @sample highcharts/annotations-advanced/fibonacci-time-zones/\n *         Fibonacci Time Zones\n *\n * @extends      annotations.crookedLine\n * @since        9.3.0\n * @product      highstock\n * @optionparent annotations.fibonacciTimeZones\n */\n{\n    typeOptions: {\n        /**\n         * @exclude   y\n         * @since     9.3.0\n         * @product   highstock\n         * @apioption annotations.fibonacciTimeZones.typeOptions.points\n         */\n        // Options for showing in popup edit\n        line: {\n            /**\n             * The color of the lines.\n             *\n             * @type      {string}\n             * @since     9.3.0\n             * @default   'rgba(0, 0, 0, 0.75)'\n             * @apioption annotations.fibonacciTimeZones.typeOptions.line.stroke\n             */\n            stroke: 'rgba(0, 0, 0, 0.75)',\n            /**\n             * The width of the lines.\n             *\n             * @type      {number}\n             * @since     9.3.0\n             * @default   1\n             * @apioption annotations.fibonacciTimeZones.typeOptions.line.strokeWidth\n             */\n            strokeWidth: 1,\n            // Don't inherit fill (don't display in popup edit)\n            fill: void 0\n        },\n        controlPointOptions: {\n            positioner: function () {\n                // The control point is in the middle of the second line\n                const target = this.target, graphic = this.graphic, edgePoints = target.secondLineEdgePoints, args = { annotation: target }, firstEdgePointY = edgePoints[0](args).y, secondEdgePointY = edgePoints[1](args).y, plotLeft = this.chart.plotLeft, plotTop = this.chart.plotTop;\n                let x = edgePoints[0](args).x, y = (firstEdgePointY + secondEdgePointY) / 2;\n                if (this.chart.inverted) {\n                    [x, y] = [y, x];\n                }\n                return {\n                    x: plotLeft + x - (graphic.width || 0) / 2,\n                    y: plotTop + y - (graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const isInsidePlot = target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                        visiblePlotOnly: true\n                    });\n                    if (isInsidePlot) {\n                        const translation = this.mouseMoveToTranslation(e);\n                        target.translatePoint(translation.x, 0, 1);\n                        target.redraw(false);\n                    }\n                }\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.fibonacciTimeZones = FibonacciTimeZones;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_FibonacciTimeZones = ((/* unused pure expression or super */ null && (FibonacciTimeZones)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Pitchfork.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: Pitchfork_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass Pitchfork extends Types_InfinityLine {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static outerLineEdgePoint(firstPointIndex) {\n        return function (target) {\n            const annotation = target.annotation, points = annotation.points;\n            return Pitchfork.findEdgePoint(points[firstPointIndex], points[0], new Annotations_MockPoint(annotation.chart, target, annotation.midPointOptions()));\n        };\n    }\n    static findEdgePoint(point, firstAnglePoint, secondAnglePoint) {\n        const angle = Math.atan2((secondAnglePoint.plotY -\n            firstAnglePoint.plotY), secondAnglePoint.plotX - firstAnglePoint.plotX), distance = 1e7;\n        return {\n            x: point.plotX + distance * Math.cos(angle),\n            y: point.plotY + distance * Math.sin(angle)\n        };\n    }\n    static middleLineEdgePoint(target) {\n        const annotation = target.annotation, points = annotation.points;\n        return Types_InfinityLine.findEdgePoint(points[0], new Annotations_MockPoint(annotation.chart, target, annotation.midPointOptions()));\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    midPointOptions() {\n        const points = this.points;\n        return {\n            x: (points[1].x + points[2].x) / 2,\n            y: (points[1].y + points[2].y) / 2,\n            xAxis: points[0].series.xAxis,\n            yAxis: points[0].series.yAxis\n        };\n    }\n    addShapes() {\n        this.addLines();\n        this.addBackgrounds();\n    }\n    addLines() {\n        const className = 'highcharts-pitchfork-lines';\n        this.initShape({\n            type: 'path',\n            points: [\n                this.points[0],\n                Pitchfork.middleLineEdgePoint\n            ],\n            className\n        }, 0);\n        this.initShape({\n            type: 'path',\n            points: [\n                this.points[1],\n                Pitchfork.topLineEdgePoint\n            ],\n            className\n        }, 1);\n        this.initShape({\n            type: 'path',\n            points: [\n                this.points[2],\n                Pitchfork.bottomLineEdgePoint\n            ],\n            className\n        }, 2);\n    }\n    addBackgrounds() {\n        const shapes = this.shapes, typeOptions = this.options.typeOptions;\n        const innerBackground = this.initShape(Pitchfork_merge(typeOptions.innerBackground, {\n            type: 'path',\n            points: [\n                function (target) {\n                    const annotation = target.annotation, points = annotation.points, midPointOptions = annotation.midPointOptions();\n                    return {\n                        x: (points[1].x + midPointOptions.x) / 2,\n                        y: (points[1].y + midPointOptions.y) / 2,\n                        xAxis: midPointOptions.xAxis,\n                        yAxis: midPointOptions.yAxis\n                    };\n                },\n                shapes[1].points[1],\n                shapes[2].points[1],\n                function (target) {\n                    const annotation = target.annotation, points = annotation.points, midPointOptions = annotation.midPointOptions();\n                    return {\n                        x: (midPointOptions.x + points[2].x) / 2,\n                        y: (midPointOptions.y + points[2].y) / 2,\n                        xAxis: midPointOptions.xAxis,\n                        yAxis: midPointOptions.yAxis\n                    };\n                }\n            ],\n            className: 'highcharts-pitchfork-inner-background'\n        }), 3);\n        const outerBackground = this.initShape(Pitchfork_merge(typeOptions.outerBackground, {\n            type: 'path',\n            points: [\n                this.points[1],\n                shapes[1].points[1],\n                shapes[2].points[1],\n                this.points[2]\n            ],\n            className: 'highcharts-pitchfork-outer-background'\n        }), 4);\n        typeOptions.innerBackground = innerBackground.options;\n        typeOptions.outerBackground = outerBackground.options;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nPitchfork.topLineEdgePoint = Pitchfork.outerLineEdgePoint(1);\nPitchfork.bottomLineEdgePoint = Pitchfork.outerLineEdgePoint(0);\nPitchfork.prototype.defaultOptions = Pitchfork_merge(Types_InfinityLine.prototype.defaultOptions, \n/**\n * A pitchfork annotation.\n *\n * @sample highcharts/annotations-advanced/pitchfork/\n *         Pitchfork\n *\n * @extends      annotations.infinityLine\n * @product      highstock\n * @optionparent annotations.pitchfork\n */\n{\n    typeOptions: {\n        /**\n         * Inner background options.\n         *\n         * @extends   annotations.crookedLine.shapeOptions\n         * @excluding height, r, type, width\n         */\n        innerBackground: {\n            fill: 'rgba(130, 170, 255, 0.4)',\n            strokeWidth: 0\n        },\n        /**\n         * Outer background options.\n         *\n         * @extends   annotations.crookedLine.shapeOptions\n         * @excluding height, r, type, width\n         */\n        outerBackground: {\n            fill: 'rgba(156, 229, 161, 0.4)',\n            strokeWidth: 0\n        }\n    }\n});\nAnnotations_Annotation.types.pitchfork = Pitchfork;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Pitchfork = ((/* unused pure expression or super */ null && (Pitchfork)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/VerticalLine.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: VerticalLine_merge, pick: VerticalLine_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass VerticalLine extends Annotations_Annotation {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static connectorFirstPoint(target) {\n        const annotation = target.annotation, chart = annotation.chart, inverted = chart.inverted, point = annotation.points[0], left = VerticalLine_pick(point.series.yAxis && point.series.yAxis.left, 0), top = VerticalLine_pick(point.series.yAxis && point.series.yAxis.top, 0), offset = annotation.options.typeOptions.label.offset, y = Annotations_MockPoint.pointToPixels(point, true)[inverted ? 'x' : 'y'];\n        return {\n            x: point.x,\n            xAxis: point.series.xAxis,\n            y: y + offset +\n                (inverted ? (left - chart.plotLeft) : (top - chart.plotTop))\n        };\n    }\n    static connectorSecondPoint(target) {\n        const annotation = target.annotation, chart = annotation.chart, inverted = chart.inverted, typeOptions = annotation.options.typeOptions, point = annotation.points[0], left = VerticalLine_pick(point.series.yAxis && point.series.yAxis.left, 0), top = VerticalLine_pick(point.series.yAxis && point.series.yAxis.top, 0), y = Annotations_MockPoint.pointToPixels(point, true)[inverted ? 'x' : 'y'];\n        let yOffset = typeOptions.yOffset;\n        if (typeOptions.label.offset < 0) {\n            yOffset *= -1;\n        }\n        return {\n            x: point.x,\n            xAxis: point.series.xAxis,\n            y: y + yOffset +\n                (inverted ? (left - chart.plotLeft) : (top - chart.plotTop))\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getPointsOptions() {\n        return [this.options.typeOptions.point];\n    }\n    addShapes() {\n        const typeOptions = this.options.typeOptions, connector = this.initShape(VerticalLine_merge(typeOptions.connector, {\n            type: 'path',\n            points: [\n                VerticalLine.connectorFirstPoint,\n                VerticalLine.connectorSecondPoint\n            ],\n            className: 'highcharts-vertical-line'\n        }), 0);\n        typeOptions.connector = connector.options;\n        this.userOptions.typeOptions.point = typeOptions.point;\n    }\n    addLabels() {\n        const typeOptions = this.options.typeOptions, labelOptions = typeOptions.label;\n        let x = 0, y = labelOptions.offset, verticalAlign = labelOptions.offset < 0 ? 'bottom' : 'top', align = 'center';\n        if (this.chart.inverted) {\n            x = labelOptions.offset;\n            y = 0;\n            verticalAlign = 'middle';\n            align = labelOptions.offset < 0 ? 'right' : 'left';\n        }\n        const label = this.initLabel(VerticalLine_merge(labelOptions, {\n            verticalAlign: verticalAlign,\n            align: align,\n            x: x,\n            y: y\n        }));\n        typeOptions.label = label.options;\n    }\n}\nVerticalLine.prototype.defaultOptions = VerticalLine_merge(Annotations_Annotation.prototype.defaultOptions, \n/**\n * A vertical line annotation.\n *\n * @sample highcharts/annotations-advanced/vertical-line/\n *         Vertical line\n *\n * @extends      annotations.crookedLine\n * @excluding    labels, shapes, controlPointOptions\n * @product      highstock\n * @optionparent annotations.verticalLine\n */\n{\n    typeOptions: {\n        /**\n         * @ignore\n         */\n        yOffset: 10,\n        /**\n         * Label options.\n         *\n         * @extends annotations.crookedLine.labelOptions\n         */\n        label: {\n            offset: -40,\n            point: function (target) {\n                return target.annotation.points[0];\n            },\n            allowOverlap: true,\n            backgroundColor: 'none',\n            borderWidth: 0,\n            crop: true,\n            overflow: 'none',\n            shape: 'rect',\n            text: '{y:.2f}'\n        },\n        /**\n         * Connector options.\n         *\n         * @extends   annotations.crookedLine.shapeOptions\n         * @excluding height, r, type, width\n         */\n        connector: {\n            strokeWidth: 1,\n            markerEnd: 'arrow'\n        }\n    }\n});\nAnnotations_Annotation.types.verticalLine = VerticalLine;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_VerticalLine = ((/* unused pure expression or super */ null && (VerticalLine)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Measure.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { defined: Measure_defined, extend: Measure_extend, isNumber: Measure_isNumber, merge: Measure_merge, pick: Measure_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction average() {\n    let average = 0, pointsTotal = 0, pointsAmount = 0;\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (isPointWithinExtremes(point, ext) &&\n                    Measure_isNumber(point.y)) {\n                    pointsTotal += point.y;\n                    pointsAmount++;\n                }\n            });\n        }\n    });\n    if (pointsAmount > 0) {\n        average = pointsTotal / pointsAmount;\n    }\n    return average;\n}\n/**\n * @private\n */\nfunction isPointWithinExtremes(point, ext) {\n    return (!point.isNull &&\n        Measure_isNumber(point.y) &&\n        point.x > ext.xAxisMin &&\n        point.x <= ext.xAxisMax &&\n        point.y > ext.yAxisMin &&\n        point.y <= ext.yAxisMax);\n}\n/**\n * @private\n */\nfunction bins() {\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    let bins = 0;\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (isPointWithinExtremes(point, ext)) {\n                    bins++;\n                }\n            });\n        }\n    });\n    return bins;\n}\n/**\n * Default formatter of label's content\n * @private\n */\nfunction defaultFormatter() {\n    return 'Min: ' + this.min +\n        '<br>Max: ' + this.max +\n        '<br>Average: ' + this.average.toFixed(2) +\n        '<br>Bins: ' + this.bins;\n}\n/**\n * Set values for xAxisMin, xAxisMax, yAxisMin, yAxisMax, also\n * when chart is inverted\n * @private\n */\nfunction getExtremes(xAxisMin, xAxisMax, yAxisMin, yAxisMax) {\n    return {\n        xAxisMin: Math.min(xAxisMax, xAxisMin),\n        xAxisMax: Math.max(xAxisMax, xAxisMin),\n        yAxisMin: Math.min(yAxisMax, yAxisMin),\n        yAxisMax: Math.max(yAxisMax, yAxisMin)\n    };\n}\n/**\n * Set current xAxisMin, xAxisMax, yAxisMin, yAxisMax.\n * Calculations of measure values (min, max, average, bins).\n * @private\n * @param {Highcharts.Axis} axis\n *        X or y axis reference\n * @param {number} value\n *        Point's value (x or y)\n * @param {number} offset\n *        Amount of pixels\n */\nfunction getPointPos(axis, value, offset) {\n    return axis.toValue(axis.toPixels(value) + offset);\n}\n/**\n * Set starting points\n * @private\n */\nfunction Measure_init() {\n    const options = this.options.typeOptions, chart = this.chart, inverted = chart.inverted, xAxis = chart.xAxis[options.xAxis], yAxis = chart.yAxis[options.yAxis], bg = options.background, width = inverted ? bg.height : bg.width, height = inverted ? bg.width : bg.height, selectType = options.selectType, top = inverted ? xAxis.left : yAxis.top, // #13664\n    left = inverted ? yAxis.top : xAxis.left; // #13664\n    this.startXMin = options.point.x;\n    this.startYMin = options.point.y;\n    if (Measure_isNumber(width)) {\n        this.startXMax = this.startXMin + width;\n    }\n    else {\n        this.startXMax = getPointPos(xAxis, this.startXMin, parseFloat(width));\n    }\n    if (Measure_isNumber(height)) {\n        this.startYMax = this.startYMin - height;\n    }\n    else {\n        this.startYMax = getPointPos(yAxis, this.startYMin, parseFloat(height));\n    }\n    // X / y selection type\n    if (selectType === 'x') {\n        this.startYMin = yAxis.toValue(top);\n        this.startYMax = yAxis.toValue(top + yAxis.len);\n    }\n    else if (selectType === 'y') {\n        this.startXMin = xAxis.toValue(left);\n        this.startXMax = xAxis.toValue(left + xAxis.len);\n    }\n}\n/**\n * @private\n */\nfunction max() {\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    let max = -Infinity, isCalculated = false; // To avoid Infinity in formatter\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (Measure_isNumber(point.y) &&\n                    point.y > max &&\n                    isPointWithinExtremes(point, ext)) {\n                    max = point.y;\n                    isCalculated = true;\n                }\n            });\n        }\n    });\n    if (!isCalculated) {\n        max = 0;\n    }\n    return max;\n}\n/**\n * Definitions of calculations (min, max, average, bins)\n * @private\n */\nfunction min() {\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    let min = Infinity, isCalculated = false; // To avoid Infinity in formatter\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (Measure_isNumber(point.y) &&\n                    point.y < min &&\n                    isPointWithinExtremes(point, ext)) {\n                    min = point.y;\n                    isCalculated = true;\n                }\n            });\n        }\n    });\n    if (!isCalculated) {\n        min = 0;\n    }\n    return min;\n}\n/**\n * Set current xAxisMin, xAxisMax, yAxisMin, yAxisMax.\n * Calculations of measure values (min, max, average, bins).\n * @private\n * @param {boolean} [resize]\n *        Flag if shape is resized.\n */\nfunction recalculate(resize) {\n    const options = this.options.typeOptions, xAxis = this.chart.xAxis[options.xAxis], yAxis = this.chart.yAxis[options.yAxis], offsetX = this.offsetX, offsetY = this.offsetY;\n    this.xAxisMin = getPointPos(xAxis, this.startXMin, offsetX);\n    this.xAxisMax = getPointPos(xAxis, this.startXMax, offsetX);\n    this.yAxisMin = getPointPos(yAxis, this.startYMin, offsetY);\n    this.yAxisMax = getPointPos(yAxis, this.startYMax, offsetY);\n    this.min = min.call(this);\n    this.max = max.call(this);\n    this.average = average.call(this);\n    this.bins = bins.call(this);\n    if (resize) {\n        this.resize(0, 0);\n    }\n}\n/**\n * Update position of start points\n * (startXMin, startXMax, startYMin, startYMax)\n * @private\n * @param {boolean} redraw\n *        Flag if shape is redraw\n * @param {boolean} resize\n *        Flag if shape is resized\n * @param {number} cpIndex\n *        Index of controlPoint\n */\nfunction updateStartPoints(redraw, resize, cpIndex, dx, dy) {\n    const options = this.options.typeOptions, selectType = options.selectType, xAxis = this.chart.xAxis[options.xAxis], yAxis = this.chart.yAxis[options.yAxis], startXMin = this.startXMin, startXMax = this.startXMax, startYMin = this.startYMin, startYMax = this.startYMax, offsetX = this.offsetX, offsetY = this.offsetY;\n    if (resize) {\n        if (selectType === 'x') {\n            if (cpIndex === 0) {\n                this.startXMin = getPointPos(xAxis, startXMin, dx);\n            }\n            else {\n                this.startXMax = getPointPos(xAxis, startXMax, dx);\n            }\n        }\n        else if (selectType === 'y') {\n            if (cpIndex === 0) {\n                this.startYMin = getPointPos(yAxis, startYMin, dy);\n            }\n            else {\n                this.startYMax = getPointPos(yAxis, startYMax, dy);\n            }\n        }\n        else {\n            this.startXMax = getPointPos(xAxis, startXMax, dx);\n            this.startYMax = getPointPos(yAxis, startYMax, dy);\n        }\n    }\n    if (redraw) {\n        this.startXMin = getPointPos(xAxis, startXMin, offsetX);\n        this.startXMax = getPointPos(xAxis, startXMax, offsetX);\n        this.startYMin = getPointPos(yAxis, startYMin, offsetY);\n        this.startYMax = getPointPos(yAxis, startYMax, offsetY);\n        this.offsetX = 0;\n        this.offsetY = 0;\n    }\n    this.options.typeOptions.point = {\n        x: this.startXMin,\n        y: this.startYMin\n    };\n    // We need to update userOptions as well as they are used in\n    // the Annotation.update() method to initialize the annotation, #19121.\n    this.userOptions.typeOptions.point = {\n        x: this.startXMin,\n        y: this.startYMin\n    };\n}\n/* *\n *\n *  Class\n *\n * */\nclass Measure extends Annotations_Annotation {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Init annotation object.\n     * @private\n     */\n    init(annotationOrChart, userOptions, index) {\n        super.init(annotationOrChart, userOptions, index);\n        this.offsetX = 0;\n        this.offsetY = 0;\n        this.resizeX = 0;\n        this.resizeY = 0;\n        Measure_init.call(this);\n        this.addValues();\n        this.addShapes();\n    }\n    /**\n     * Overrides default setter to get axes from typeOptions.\n     * @private\n     */\n    setClipAxes() {\n        this.clipXAxis = this.chart.xAxis[this.options.typeOptions.xAxis];\n        this.clipYAxis = this.chart.yAxis[this.options.typeOptions.yAxis];\n    }\n    /**\n     * Get points configuration objects for shapes.\n     * @private\n     */\n    shapePointsOptions() {\n        const options = this.options.typeOptions, xAxis = options.xAxis, yAxis = options.yAxis;\n        return [\n            {\n                x: this.xAxisMin,\n                y: this.yAxisMin,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                x: this.xAxisMax,\n                y: this.yAxisMin,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                x: this.xAxisMax,\n                y: this.yAxisMax,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                x: this.xAxisMin,\n                y: this.yAxisMax,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                command: 'Z'\n            }\n        ];\n    }\n    addControlPoints() {\n        const inverted = this.chart.inverted, options = this.options.controlPointOptions, selectType = this.options.typeOptions.selectType;\n        if (!Measure_defined(this.userOptions.controlPointOptions?.style?.cursor)) {\n            if (selectType === 'x') {\n                options.style.cursor = inverted ? 'ns-resize' : 'ew-resize';\n            }\n            else if (selectType === 'y') {\n                options.style.cursor = inverted ? 'ew-resize' : 'ns-resize';\n            }\n        }\n        let controlPoint = new Annotations_ControlPoint(this.chart, this, this.options.controlPointOptions, 0);\n        this.controlPoints.push(controlPoint);\n        // Add extra controlPoint for horizontal and vertical range\n        if (selectType !== 'xy') {\n            controlPoint = new Annotations_ControlPoint(this.chart, this, this.options.controlPointOptions, 1);\n            this.controlPoints.push(controlPoint);\n        }\n    }\n    /**\n     * Add label with calculated values (min, max, average, bins).\n     * @private\n     * @param {boolean} [resize]\n     * The flag for resize shape\n     */\n    addValues(resize) {\n        const typeOptions = this.options.typeOptions, formatter = typeOptions.label.formatter;\n        // Set xAxisMin, xAxisMax, yAxisMin, yAxisMax\n        recalculate.call(this, resize);\n        if (!typeOptions.label.enabled) {\n            return;\n        }\n        if (this.labels.length > 0) {\n            (this.labels[0]).text = ((formatter && formatter.call(this)) ||\n                defaultFormatter.call(this));\n        }\n        else {\n            this.initLabel(Measure_extend({\n                shape: 'rect',\n                backgroundColor: 'none',\n                color: 'black',\n                borderWidth: 0,\n                dashStyle: 'Dash',\n                overflow: 'allow',\n                align: 'left',\n                y: 0,\n                x: 0,\n                verticalAlign: 'top',\n                crop: true,\n                xAxis: 0,\n                yAxis: 0,\n                point: function (target) {\n                    const annotation = target.annotation, options = target.options;\n                    return {\n                        x: annotation.xAxisMin,\n                        y: annotation.yAxisMin,\n                        xAxis: Measure_pick(typeOptions.xAxis, options.xAxis),\n                        yAxis: Measure_pick(typeOptions.yAxis, options.yAxis)\n                    };\n                },\n                text: ((formatter && formatter.call(this)) ||\n                    defaultFormatter.call(this))\n            }, typeOptions.label), void 0);\n        }\n    }\n    /**\n     * Crosshair, background (rect).\n     * @private\n     */\n    addShapes() {\n        this.addCrosshairs();\n        this.addBackground();\n    }\n    /**\n     * Add background shape.\n     * @private\n     */\n    addBackground() {\n        const shapePoints = this.shapePointsOptions();\n        if (typeof shapePoints[0].x === 'undefined') {\n            return;\n        }\n        this.initShape(Measure_extend({\n            type: 'path',\n            points: shapePoints,\n            className: 'highcharts-measure-background'\n        }, this.options.typeOptions.background), 2);\n    }\n    /**\n     * Add internal crosshair shapes (on top and bottom).\n     * @private\n     */\n    addCrosshairs() {\n        const chart = this.chart, options = this.options.typeOptions, point = this.options.typeOptions.point, xAxis = chart.xAxis[options.xAxis], yAxis = chart.yAxis[options.yAxis], inverted = chart.inverted, defaultOptions = {\n            point: point,\n            type: 'path'\n        };\n        let xAxisMin = xAxis.toPixels(this.xAxisMin), xAxisMax = xAxis.toPixels(this.xAxisMax), yAxisMin = yAxis.toPixels(this.yAxisMin), yAxisMax = yAxis.toPixels(this.yAxisMax), pathH = [], pathV = [], crosshairOptionsX, crosshairOptionsY, temp;\n        if (inverted) {\n            temp = xAxisMin;\n            xAxisMin = yAxisMin;\n            yAxisMin = temp;\n            temp = xAxisMax;\n            xAxisMax = yAxisMax;\n            yAxisMax = temp;\n        }\n        // Horizontal line\n        if (options.crosshairX.enabled) {\n            pathH = [[\n                    'M',\n                    xAxisMin,\n                    yAxisMin + ((yAxisMax - yAxisMin) / 2)\n                ], [\n                    'L',\n                    xAxisMax,\n                    yAxisMin + ((yAxisMax - yAxisMin) / 2)\n                ]];\n        }\n        // Vertical line\n        if (options.crosshairY.enabled) {\n            pathV = [[\n                    'M',\n                    xAxisMin + ((xAxisMax - xAxisMin) / 2),\n                    yAxisMin\n                ], [\n                    'L',\n                    xAxisMin + ((xAxisMax - xAxisMin) / 2),\n                    yAxisMax\n                ]];\n        }\n        // Update existed crosshair\n        if (this.shapes.length > 0) {\n            this.shapes[0].options.d = pathH;\n            this.shapes[1].options.d = pathV;\n        }\n        else {\n            // Add new crosshairs\n            crosshairOptionsX = Measure_merge(defaultOptions, { className: 'highcharts-measure-crosshair-x' }, options.crosshairX);\n            crosshairOptionsY = Measure_merge(defaultOptions, { className: 'highcharts-measure-crosshair-y' }, options.crosshairY);\n            this.initShape(Measure_extend({ d: pathH }, crosshairOptionsX), 0);\n            this.initShape(Measure_extend({ d: pathV }, crosshairOptionsY), 1);\n        }\n    }\n    onDrag(e) {\n        const translation = this.mouseMoveToTranslation(e), selectType = this.options.typeOptions.selectType, x = selectType === 'y' ? 0 : translation.x, y = selectType === 'x' ? 0 : translation.y;\n        this.translate(x, y);\n        this.offsetX += x;\n        this.offsetY += y;\n        // Animation, resize, setStartPoints\n        this.redraw(false, false, true);\n    }\n    /**\n     * Translate start or end (\"left\" or \"right\") side of the measure.\n     * Update start points (startXMin, startXMax, startYMin, startYMax)\n     * @private\n     * @param {number} dx\n     * the amount of x translation\n     * @param {number} dy\n     * the amount of y translation\n     * @param {number} cpIndex\n     * index of control point\n     * @param {Highcharts.AnnotationDraggableValue} selectType\n     * x / y / xy\n     */\n    resize(dx, dy, cpIndex, selectType) {\n        // Background shape\n        const bckShape = this.shapes[2];\n        if (selectType === 'x') {\n            if (cpIndex === 0) {\n                bckShape.translatePoint(dx, 0, 0);\n                bckShape.translatePoint(dx, dy, 3);\n            }\n            else {\n                bckShape.translatePoint(dx, 0, 1);\n                bckShape.translatePoint(dx, dy, 2);\n            }\n        }\n        else if (selectType === 'y') {\n            if (cpIndex === 0) {\n                bckShape.translatePoint(0, dy, 0);\n                bckShape.translatePoint(0, dy, 1);\n            }\n            else {\n                bckShape.translatePoint(0, dy, 2);\n                bckShape.translatePoint(0, dy, 3);\n            }\n        }\n        else {\n            bckShape.translatePoint(dx, 0, 1);\n            bckShape.translatePoint(dx, dy, 2);\n            bckShape.translatePoint(0, dy, 3);\n        }\n        updateStartPoints.call(this, false, true, cpIndex, dx, dy);\n        this.options.typeOptions.background.height = Math.abs(this.startYMax - this.startYMin);\n        this.options.typeOptions.background.width = Math.abs(this.startXMax - this.startXMin);\n    }\n    /**\n     * Redraw event which render elements and update start points if needed.\n     * @private\n     * @param {boolean} animation\n     * @param {boolean} [resize]\n     * flag if resized\n     * @param {boolean} [setStartPoints]\n     * update position of start points\n     */\n    redraw(animation, resize, setStartPoints) {\n        this.linkPoints();\n        if (!this.graphic) {\n            this.render();\n        }\n        if (setStartPoints) {\n            updateStartPoints.call(this, true, false);\n        }\n        // #11174 - clipBox was not recalculate during resize / redraw\n        if (this.clipRect) {\n            this.clipRect.animate(this.getClipBox());\n        }\n        this.addValues(resize);\n        this.addCrosshairs();\n        this.redrawItems(this.shapes, animation);\n        this.redrawItems(this.labels, animation);\n        const backgroundOptions = this.options.typeOptions.background;\n        if (backgroundOptions?.strokeWidth &&\n            this.shapes[2]?.graphic) {\n            const offset = (backgroundOptions.strokeWidth) / 2;\n            const background = this.shapes[2];\n            const path = background.graphic.pathArray;\n            const p1 = path[0];\n            const p2 = path[1];\n            const p3 = path[2];\n            const p4 = path[3];\n            p1[1] = (p1[1] || 0) + offset;\n            p2[1] = (p2[1] || 0) - offset;\n            p3[1] = (p3[1] || 0) - offset;\n            p4[1] = (p4[1] || 0) + offset;\n            p1[2] = (p1[2] || 0) + offset;\n            p2[2] = (p2[2] || 0) + offset;\n            p3[2] = (p3[2] || 0) - offset;\n            p4[2] = (p4[2] || 0) - offset;\n            background.graphic.attr({\n                d: path\n            });\n        }\n        // Redraw control point to run positioner\n        this.controlPoints.forEach((controlPoint) => controlPoint.redraw());\n    }\n    translate(dx, dy) {\n        this.shapes.forEach((item) => item.translate(dx, dy));\n    }\n}\nMeasure.prototype.defaultOptions = Measure_merge(Annotations_Annotation.prototype.defaultOptions, \n/**\n * A measure annotation.\n *\n * @extends annotations.crookedLine\n * @excluding labels, labelOptions, shapes, shapeOptions\n * @sample highcharts/annotations-advanced/measure/\n *         Measure\n * @product highstock\n * @optionparent annotations.measure\n */\n{\n    typeOptions: {\n        /**\n         * Decides in what dimensions the user can resize by dragging the\n         * mouse. Can be one of x, y or xy.\n         */\n        selectType: 'xy',\n        /**\n         * This number defines which xAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the xAxis array.\n         */\n        xAxis: 0,\n        /**\n         * This number defines which yAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the yAxis array.\n         */\n        yAxis: 0,\n        background: {\n            /**\n             * The color of the rectangle.\n             */\n            fill: 'rgba(130, 170, 255, 0.4)',\n            /**\n             * The width of border.\n             */\n            strokeWidth: 0,\n            /**\n             * The color of border.\n             */\n            stroke: void 0\n        },\n        /**\n         * Configure a crosshair that is horizontally placed in middle of\n         * rectangle.\n         *\n         */\n        crosshairX: {\n            /**\n             * Enable or disable the horizontal crosshair.\n             *\n             */\n            enabled: true,\n            /**\n             * The Z index of the crosshair in annotation.\n             */\n            zIndex: 6,\n            /**\n             * The dash or dot style of the crosshair's line. For possible\n             * values, see\n             * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n             *\n             * @type    {Highcharts.DashStyleValue}\n             * @default Dash\n             */\n            dashStyle: 'Dash',\n            /**\n             * The marker-end defines the arrowhead that will be drawn\n             * at the final vertex of the given crosshair's path.\n             *\n             * @type       {string}\n             * @default    arrow\n             */\n            markerEnd: 'arrow'\n        },\n        /**\n         * Configure a crosshair that is vertically placed in middle of\n         * rectangle.\n         */\n        crosshairY: {\n            /**\n             * Enable or disable the vertical crosshair.\n             *\n             */\n            enabled: true,\n            /**\n             * The Z index of the crosshair in annotation.\n             */\n            zIndex: 6,\n            /**\n             * The dash or dot style of the crosshair's line. For possible\n             * values, see\n             * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n             *\n             * @type      {Highcharts.DashStyleValue}\n             * @default   Dash\n             * @apioption annotations.measure.typeOptions.crosshairY.dashStyle\n             *\n             */\n            dashStyle: 'Dash',\n            /**\n             * The marker-end defines the arrowhead that will be drawn\n             * at the final vertex of the given crosshair's path.\n             *\n             * @type       {string}\n             * @default    arrow\n             * @validvalue [\"none\", \"arrow\"]\n             *\n             */\n            markerEnd: 'arrow'\n        },\n        label: {\n            /**\n             * Enable or disable the label text (min, max, average,\n             * bins values).\n             *\n             * Defaults to true.\n             */\n            enabled: true,\n            /**\n             * CSS styles for the measure label.\n             *\n             * @type    {Highcharts.CSSObject}\n             * @default {\"color\": \"#666666\", \"fontSize\": \"11px\"}\n             */\n            style: {\n                fontSize: '0.7em',\n                color: \"#666666\" /* Palette.neutralColor60 */\n            },\n            /**\n             * Formatter function for the label text.\n             *\n             * Available data are:\n             *\n             * <table>\n             *\n             * <tbody>\n             *\n             * <tr>\n             *\n             * <td>`this.min`</td>\n             *\n             * <td>The minimum value of the points in the selected\n             * range.</td>\n             *\n             * </tr>\n             *\n             * <tr>\n             *\n             * <td>`this.max`</td>\n             *\n             * <td>The maximum value of the points in the selected\n             * range.</td>\n             *\n             * </tr>\n             *\n             * <tr>\n             *\n             * <td>`this.average`</td>\n             *\n             * <td>The average value of the points in the selected\n             * range.</td>\n             *\n             * </tr>\n             *\n             * <tr>\n             *\n             * <td>`this.bins`</td>\n             *\n             * <td>The amount of the points in the selected range.</td>\n             *\n             * </tr>\n             *\n             * </table>\n             *\n             * @type {Function}\n             *\n             */\n            formatter: void 0\n        }\n    },\n    controlPointOptions: {\n        positioner: function (target) {\n            const cpIndex = this.index, chart = target.chart, options = target.options, typeOptions = options.typeOptions, selectType = typeOptions.selectType, controlPointOptions = options.controlPointOptions, inverted = chart.inverted, xAxis = chart.xAxis[typeOptions.xAxis], yAxis = chart.yAxis[typeOptions.yAxis], ext = getExtremes(target.xAxisMin, target.xAxisMax, target.yAxisMin, target.yAxisMax);\n            let targetX = target.xAxisMax, targetY = target.yAxisMax, x, y;\n            if (selectType === 'x') {\n                targetY = (ext.yAxisMax + ext.yAxisMin) / 2;\n                // First control point\n                if (cpIndex === 0) {\n                    targetX = target.xAxisMin;\n                }\n            }\n            if (selectType === 'y') {\n                targetX = ext.xAxisMin +\n                    ((ext.xAxisMax - ext.xAxisMin) / 2);\n                // First control point\n                if (cpIndex === 0) {\n                    targetY = target.yAxisMin;\n                }\n            }\n            if (inverted) {\n                x = yAxis.toPixels(targetY);\n                y = xAxis.toPixels(targetX);\n            }\n            else {\n                x = xAxis.toPixels(targetX);\n                y = yAxis.toPixels(targetY);\n            }\n            return {\n                x: x - (controlPointOptions.width / 2),\n                y: y - (controlPointOptions.height / 2)\n            };\n        },\n        events: {\n            drag: function (e, target) {\n                const translation = this.mouseMoveToTranslation(e), selectType = target.options.typeOptions.selectType, index = this.index, x = selectType === 'y' ? 0 : translation.x, y = selectType === 'x' ? 0 : translation.y;\n                target.resize(x, y, index, selectType);\n                target.resizeX += x;\n                target.resizeY += y;\n                target.redraw(false, true);\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.measure = Measure;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Measure = ((/* unused pure expression or super */ null && (Measure)));\n\n;// ./code/es-modules/masters/modules/annotations-advanced.js\n/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/annotations-advanced\n * @requires highcharts\n *\n * Annotations module\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const annotations_advanced_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__660__", "AnnotationChart", "ControlTarget", "DropdownProperties", "ChartNavigationComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "annotations_advanced_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "erase", "find", "fireEvent", "pick", "wrap", "chartAddAnnotation", "userOptions", "redraw", "annotation", "initAnnotation", "options", "annotations", "push", "graphic", "attr", "opacity", "chartCallback", "chart", "plotBoxClip", "renderer", "clipRect", "plotBox", "controlPointsGroup", "g", "zIndex", "clip", "add", "for<PERSON>ach", "annotationOptions", "i", "some", "drawAnnotations", "destroy", "event", "csvColumnHeaderFormatter", "exporting", "csv", "columnHeaderFormatter", "multiLevelHeaders", "dataRows", "xValues", "annotationHeader", "lang", "exportData", "startRowLength", "length", "annotationSeparator", "itemDelimiter", "joinAnnotations", "join", "labelOptions", "includeInDataExport", "labels", "label", "text", "annotationText", "points", "annotationX", "x", "xAxisIndex", "series", "xAxis", "index", "wasAdded", "newRow", "Array", "row", "maxRowLen", "Math", "max", "newRows", "header", "s", "columnTitle", "topLevelColumnTitle", "chartDrawAnnotations", "animate", "animationConfig", "chartRemoveAnnotation", "idOrAnnotation", "coll", "id", "onChartAfterInit", "wrapPointerOnContainerMouseDown", "proceed", "hasDraggedAnnotation", "apply", "slice", "arguments", "compose", "AnnotationClass", "ChartClass", "PointerClass", "chartProto", "addAnnotation", "pointer<PERSON><PERSON><PERSON>", "callbacks", "collectionsWithInit", "collectionsWithUpdate", "removeAnnotation", "types", "type", "Annotations_AnnotationChart", "defined", "doc", "isTouchDevice", "EventEmitter_addEvent", "EventEmitter_fireEvent", "objectEach", "EventEmitter_pick", "removeEvent", "Annotations_EventEmitter", "addEvents", "emitter", "addMouseDownEvent", "element", "e", "onMouseDown", "passive", "useHTML", "foreignObject", "events", "<PERSON><PERSON><PERSON><PERSON>", "cancelClick", "pointer", "normalize", "target", "nonDOMEvents", "indexOf", "div", "draggable", "onDrag", "styledMode", "cssPointer", "cursor", "y", "xy", "css", "isUpdating", "removeDocEvents", "hcEvents", "mouseMoveToRadians", "cx", "cy", "prevDy", "prevChartY", "prevDx", "prevChartX", "dy", "chartY", "dx", "chartX", "temp", "inverted", "atan2", "mouseMoveToScale", "sx", "sy", "mouseMoveToTranslation", "isInsidePlot", "plotLeft", "plotTop", "visiblePlotOnly", "translation", "translate", "shapes", "shape", "preventDefault", "button", "firesTouchEvents", "sourceCapabilities", "removeDrag", "hasDragged", "removeMouseUp", "onMouseUp", "merge", "ControlPoint_pick", "Annotations_ControlPoint", "constructor", "animation", "positioner", "render", "symbol", "width", "height", "style", "setVisibility", "visible", "update", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesProto", "MockPoint_defined", "MockPoint_fireEvent", "MockPoint", "fromPoint", "point", "yAxis", "pointToPixels", "paneCoordinates", "plotX", "plotY", "mock", "plot<PERSON>id<PERSON>", "plotHeight", "getPlotBox", "translateX", "translateY", "pointToOptions", "applyOptions", "getOptions", "command", "setAxis", "refresh", "hasDynamicOptions", "len", "toPixels", "isInside", "refreshOptions", "toValue", "rotate", "radians", "cos", "sin", "tx", "ty", "scale", "xOrY", "axisName", "axisOptions", "toAnchor", "anchor", "_cx", "_cy", "addControlPoints", "controlPoints", "controlPointsOptions", "controlPointOptions", "box", "tooltip", "getAnchor", "relativePosition", "absolutePosition", "destroyControlTarget", "controlPoint", "getPointsOptions", "splat", "linkPoints", "pointsOptions", "pointOptions", "isObject", "isString", "pointConfig", "redrawControlPoints", "renderControlPoints", "transform", "transformation", "p1", "p2", "_point", "transformPoint", "Annotations_MockPoint", "translatePoint", "ControlTargetClass", "controlProto", "Annotations_ControlTarget", "Controllable_merge", "Controllable", "itemType", "collection", "init", "_args", "attrsFromOptions", "<PERSON><PERSON><PERSON>", "map", "attrsMap", "attrs", "tracker", "_parentGroup", "className", "addClass", "setControlPointsVisibility", "shouldBeDrawn", "translateShape", "translateSecondPoint", "shapeOptions", "annotationIndex", "chartOptions", "newOptions", "parentGroup", "<PERSON><PERSON><PERSON><PERSON>", "Controllables_Controllable", "defaultMarkers", "ControllablePath_defaultMarkers", "arrow", "tagName", "attributes", "refY", "refX", "marker<PERSON>id<PERSON>", "markerHeight", "children", "ControllablePath_addEvent", "ControllablePath_defined", "extend", "ControllablePath_merge", "<PERSON><PERSON><PERSON>", "markerEndSetter", "createMarkerSetter", "markerStartSetter", "TRACKER_FILL", "svg", "markerType", "value", "onChartAfterGetContainer", "defs", "svgRendererAddMarker", "markerOptions", "stroke", "color", "fill", "child", "ast", "orient", "marker", "ControllablePath", "SVGRendererClass", "svgRendererProto", "add<PERSON><PERSON><PERSON>", "toD", "dOption", "showPath", "position", "pointIndex", "crispLine", "strokeWidth", "parent", "path", "snap", "setMarkers", "action", "placed", "item", "itemOptions", "def", "predefined<PERSON>ark<PERSON>", "markerId", "getAttribute", "dashStyle", "ControllableRect_merge", "ControllableRect", "rect", "Boolean", "Controllables_ControllablePath", "ControllableCircle_merge", "ControllableCircle", "r", "circle", "setRadius", "ControllableEllipse_merge", "ControllableEllipse_defined", "ControllableEllipse", "createElement", "getDistanceFromLine", "point1", "point2", "x0", "y0", "abs", "sqrt", "getAttrs", "position2", "x1", "y1", "x2", "y2", "rx", "angle", "atan", "PI", "ry", "getRY", "getYAxis", "yAxisIndex", "getAbsolutePosition", "rotation", "rotationOriginX", "rotationOriginY", "setYRadius", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "format", "ControllableLabel_extend", "getAlignFactor", "isNumber", "ControllableLabel_pick", "symbolConnector", "w", "h", "anchorX", "anchorY", "yOffset", "lateral", "ControllableLabel", "alignedPosition", "alignOptions", "round", "align", "verticalAlign", "symbols", "connector", "justifiedOptions", "alignAttr", "off", "padding", "bBox", "getBBox", "chartAnnotations", "getContrast", "shapesWithoutBackground", "backgroundColor", "shadow", "labelrank", "String", "formatter", "anchorAbsolutePosition", "anchorRelativePosition", "itemPosition", "alignTo", "itemPosRelativeX", "itemPosRelativeY", "showItem", "distance", "getPosition", "getPlayingField", "negative", "ttBelow", "overflow", "crop", "borderColor", "borderWidth", "borderRadius", "ControllableImage", "image", "src", "Controllables_ControllableLabel", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "BaseForm_addEvent", "Shared_BaseForm", "parentDiv", "iconsURL", "container", "createPopupContainer", "closeButton", "addCloseButton", "popup", "match", "eventName", "closeButtonEvents", "bind", "document", "code", "closePopup", "showPopup", "toolbarClass", "popupDiv", "popupClose<PERSON><PERSON>on", "innerHTML", "emptyHTML", "classList", "remove", "removeAttribute", "append<PERSON><PERSON><PERSON>", "display", "PopupAnnotations_doc", "isFirefox", "PopupAnnotations_createElement", "isArray", "PopupAnnotations_objectEach", "PopupAnnotations_pick", "stableSort", "addFormFields", "parentNode", "storage", "isRoot", "parentFullName", "<PERSON><PERSON><PERSON>", "addInput", "option", "reverse", "genInput", "createTextNode", "splice", "PopupIndicators_doc", "seriesTypes", "PopupIndicators_addEvent", "PopupIndicators_createElement", "PopupIndicators_defined", "PopupIndicators_isArray", "PopupIndicators_isObject", "PopupIndicators_objectEach", "PopupIndicators_stableSort", "dropdownParameters", "addColsContainer", "lhsCol", "rhsCol", "PopupIndicators_addFormFields", "seriesType", "rhsColWrapper", "fields", "params", "getNameType", "indicatorFullName", "name", "listAllSeries", "linkedParent", "volumeSeriesID", "addParamInputs", "addIndicatorList", "listType", "filter", "selectIndicator", "indicatorType", "isEdit", "setAttribute", "querySelectorAll", "plotOptions", "filteredSeriesArray", "filterSeriesArray", "filterSeries", "b", "seriesAName", "toLowerCase", "seriesBName", "indicatorList", "seriesSet", "btn", "textContent", "setElementHTML", "noFilterMatch", "fieldName", "selectBox", "addSelection", "addSelectionOptions", "addSearchBox", "clearFilterText", "clearFilter", "inputWrapper", "handleInputChange", "inputText", "input", "htmlFor", "labelClassName", "optionName", "optionParamList", "split", "labelText", "selectName", "parameterName", "selectedOption", "currentSeries", "seriesOptions", "seriesName", "parameterOption", "filteredSeries", "indicatorAliases", "navigation", "regex", "RegExp", "replace", "alias", "is", "nameBase", "toUpperCase", "PopupTabs_doc", "PopupTabs_addEvent", "PopupTabs_createElement", "addContentItem", "addMenuItem", "tabName", "disableTab", "menuItem", "deselectAll", "tabs", "tabsContent", "selectTab", "tab", "allTabs", "switchTabs", "Popup_doc", "Popup_addEvent", "Popup_createElement", "Popup_extend", "Popup_fireEvent", "Popup_pick", "Popup", "activeAnnotation", "navigationBindings", "unbind", "setTimeout", "inputAttributes", "inputName", "selectedButtonElement", "addButton", "fieldsDiv", "callback", "getFields", "inputList", "selectList", "linkedTo", "volumeTo", "fieldsOutput", "actionType", "param", "seriesId", "select", "parameter", "showForm", "indicators", "addForm", "addToolbar", "offsetHeight", "isInit", "lang<PERSON><PERSON>", "bottomRow", "saveButton", "top", "edit<PERSON><PERSON><PERSON>", "removeButton", "_options", "buttonParentDiv", "tabsContainers", "getAmount", "counter", "serie", "indicatorsCount", "firstTab", "composed", "PopupComposition_addEvent", "pushUnique", "PopupComposition_wrap", "onNavigationBindingsClosePopup", "onNavigationBindingsShowPopup", "config", "stockTools", "gui", "formType", "onSubmit", "wrapPointerOnContainerMouserDown", "inClass", "Popup_PopupComposition", "NagivationBindingsClass", "getDeferredAnimation", "destroyObjectProperties", "Annotation_erase", "Annotation_fireEvent", "Annotation_merge", "Annotation_pick", "getLabelsAndShapesOptions", "baseOptions", "mergedOptions", "someBaseOptions", "newOptionsValue", "basicOptions", "Annotation", "NavigationBindingsClass", "defaultOptions", "labelsAndShapes", "addClipPaths", "setClipAxes", "clipXAxis", "clipYAxis", "getClipBox", "addLabels", "labelsOptions", "initLabel", "addShapes", "initShape", "destroyItem", "labelCollectors", "labelCollector", "left", "initProperties", "setOptions", "_annotation<PERSON>r<PERSON>hart", "_userOptions", "animOptions", "set<PERSON>abelCollector", "shapesMap", "redrawItems", "redrawItem", "renderItem", "adjustVisibility", "hasVisiblePoints", "visibility", "show", "hide", "items", "shapesGroup", "labelsGroup", "renderItems", "xAxes", "yAxes", "linkedAxes", "concat", "reduce", "axes", "labelOrShape", "setItemControlPointsVisibility", "allowOverlap", "userOptionsIndex", "ControlPoint", "fontSize", "fontWeight", "Annotations_Annotation", "Additions", "updates", "addUpdate", "updateFn", "Chart_ChartNavigationComposition", "NavigationBindingsUtilities_defined", "NavigationBindingsUtilities_isNumber", "NavigationBindingsUtilities_pick", "annotationsFieldsTypes", "title", "NavigationBindingsUtilities", "getAssignedAxis", "coords", "coord", "extremes", "axis", "getExtremes", "axisMin", "min", "axisMax", "minPointOffset", "isInternal", "getFieldType", "predefinedType", "fieldType", "NavigationBindingsDefaults_getAssignedAxis", "NavigationBindingsDefaults_isNumber", "NavigationBindingsDefaults_merge", "NavigationBindingsDefaults", "simpleShapes", "lines", "ellipse", "rectangle", "typeOptions", "backgroundColors", "bindingsClassName", "bindings", "circleAnnotation", "start", "getCoordinates", "coordsX", "coordsY", "annotationsOptions", "steps", "mockPointOpts", "pow", "ellipseAnnotation", "newR", "newRY", "rectangleAnnotation", "labelAnnotation", "defer", "NavigationBindings_format", "NavigationBindings_composed", "NavigationBindings_doc", "win", "NavigationBindings_getAssignedAxis", "NavigationBindings_getFieldType", "NavigationBindings_addEvent", "NavigationBindings_defined", "NavigationBindings_fireEvent", "NavigationBindings_isArray", "isFunction", "NavigationBindings_isNumber", "NavigationBindings_isObject", "NavigationBindings_merge", "NavigationBindings_objectEach", "NavigationBindings_pick", "NavigationBindings_pushUnique", "onAnnotationRemove", "deselectAnnotation", "onChartDestroy", "onChartLoad", "NavigationBindings", "initEvents", "initUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledClassName", "buttonsEnabled", "boundClassNames", "buttonNode", "cls", "noDataState", "NavigationBindings_onNavigationBindingsClosePopup", "onNavigationBindingsDeselectButton", "selectableAnnotation", "annotationType", "touchStartX", "touchStartY", "originalClick", "click", "selectAndShowPopup", "eventArguments", "prevAnnotation", "annotationToFields", "data", "fieldsToOptions", "crosshairY", "enabled", "crosshairX", "touchstart", "touches", "clientX", "clientY", "touchend", "changedTouches", "eventsToUnbind", "getElementsByClassName", "getCoords", "bindingsContainer", "subContainer", "getButtonEvents", "contains", "bindingsButtonClick", "bindingsChartClick", "bindingsContainerMouseMove", "clickEvent", "svgContainer", "boxWrapper", "shouldEventBeFired", "nextEvent", "currentUserDetails", "mouseMoveEvent", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "closestPolyfill", "el", "ElementProto", "Element", "elementMatches", "matches", "msMatchesSelector", "webkitMatchesSelector", "ret", "closest", "parentElement", "nodeType", "stepIndex", "end", "_container", "moveEvent", "field", "parsedValue", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "nextName", "editables", "annotationsEditable", "nestedEditables", "nestedOptions", "nonEditables", "annotationsNonEditable", "visualOptions", "traverse", "parentEditables", "parent<PERSON><PERSON>", "nextParent", "arrayOption", "nestedOption", "nested<PERSON><PERSON>", "toString", "typeOption", "typeKey", "getClickedClassNames", "classNames", "elemClassName", "removeEvents", "unbinder", "background", "innerBackground", "outerBackground", "line", "verticalLine", "measure", "<PERSON><PERSON><PERSON><PERSON>", "tunnel", "pitchfork", "crookedLine", "basicAnnotation", "G", "Chart", "Pointer", "<PERSON><PERSON><PERSON><PERSON>", "BasicAnnotation_merge", "BasicAnnotation", "basicControlPoints", "basicType", "optionsGroup", "group", "drag", "CrookedLine_merge", "Crooked<PERSON>ine", "getControlPointsOptions", "Types_CrookedLine", "ElliottWave_merge", "Elliott<PERSON><PERSON>", "<PERSON><PERSON>tt<PERSON>ave", "Tunnel_merge", "Tunnel", "heightPointOptions", "logarithmic", "y3", "heightControlPoint", "addLine", "addBackground", "translateSide", "topIndex", "Number", "translateHeight", "dh", "startXY", "endXY", "getSecondCoordinate", "Types_Tunnel", "InfinityLine_merge", "InfinityLine", "edgePoint", "startIndex", "endIndex", "findEdgePoint", "findEdgeCoordinate", "firstPoint", "secondPoint", "edgePointFirstCoordinate", "xOrYOpposite", "edgePointX", "edgePointY", "swap", "firstPointPixels", "secondPointPixels", "deltaX", "deltaY", "xAxisMin", "xAxisMax", "yAxisMin", "yAxisMax", "xLimit", "yLimit", "endEdgePoint", "startEdgePoint", "infinityLine", "Types_InfinityLine", "TimeCycles_merge", "TimeCycles_isNumber", "TimeCycles_defined", "TimeCycles", "set<PERSON>ath", "<PERSON><PERSON><PERSON>", "startX", "getCircle<PERSON>ath", "pixelInterval", "numberOfCircles", "setPathProperties", "xAxisNumber", "yAxisNumber", "xValue1", "yValue", "xValue2", "xAxisLength", "floor", "pixelShift", "timeCycles", "Fibonacci_merge", "createPathDGenerator", "retracementIndex", "isBackground", "startRetracements", "endRetracements", "leftTop", "rightTop", "rightBottom", "leftBottom", "<PERSON><PERSON><PERSON><PERSON>", "linkRetracementsPoints", "startDiff", "endDiff", "endX", "levels", "level", "startRetracement", "endRetracement", "reversed", "linkRetracementPoint", "retracements", "_level", "lineColor", "lineColors", "FibonacciTimeZones_merge", "fibonacciIndex", "plotLeftOrTop", "FibonacciTimeZones", "fibb", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "secondLineEdgePoints", "edgePoints", "args", "firstEdgePointY", "secondEdgePointY", "fibonacciTimeZones", "Pitchfork_merge", "Pitchfork", "outerLineEdgePoint", "firstPointIndex", "midPointOptions", "firstAnglePoint", "secondAnglePoint", "middleLineEdgePoint", "addLines", "addBackgrounds", "topLineEdgePoint", "bottomLineEdgePoint", "VerticalLine_merge", "VerticalLine_pick", "VerticalLine", "connectorFirstPoint", "offset", "connectorSecondPoint", "markerEnd", "Measure_defined", "Measure_extend", "Measure_isNumber", "Measure_merge", "Measure_pick", "average", "pointsTotal", "pointsAmount", "ext", "isPointWithinExtremes", "isNull", "bins", "defaultFormatter", "toFixed", "getPointPos", "Measure_init", "bg", "selectType", "startXMin", "startYMin", "startXMax", "startYMax", "Infinity", "isCalculated", "recalculate", "resize", "offsetX", "offsetY", "updateStartPoints", "cpIndex", "Measure", "annotation<PERSON>r<PERSON>hart", "resizeX", "resizeY", "addValues", "shapePointsOptions", "addCrosshairs", "shapePoints", "pathH", "pathV", "crosshairOptionsX", "crosshairOptionsY", "bckShape", "setStartPoints", "backgroundOptions", "pathArray", "p3", "p4", "targetX", "targetY"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,GAAM,EAC3I,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,0CAA2C,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAACA,EAAK,GAAM,CAAE,GAClK,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,0CAA0C,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,CAAEA,EAAK,WAAc,CAAC,GAAM,EAEtLA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CACpJ,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,IACzI,AAAC,CAAA,KACP,aACA,IAmVNC,EAkiDAC,EAskEAC,EA4pDAC,EAvlLUC,EAAuB,CAE/B,IACC,AAACb,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAalB,OAAO,CAG5B,IAAIC,EAASc,CAAwB,CAACE,EAAS,CAAG,CAGjDjB,QAAS,CAAC,CACX,EAMA,OAHAc,CAAmB,CAACG,EAAS,CAAChB,EAAQA,EAAOD,OAAO,CAAEgB,GAG/Cf,EAAOD,OAAO,AACtB,CAMCgB,EAAoBI,CAAC,CAAG,AAACnB,IACxB,IAAIoB,EAASpB,GAAUA,EAAOqB,UAAU,CACvC,IAAOrB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAe,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACvB,EAASyB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC3B,EAAS0B,IAC5EE,OAAOC,cAAc,CAAC7B,EAAS0B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAarH,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAIN,IAsB1D,SAASO,EAAmBC,CAAW,CAAEC,CAAM,EAC3C,IAAMC,EAAa,IAAI,CAACC,cAAc,CAACH,GAQvC,OAPA,IAAI,CAACI,OAAO,CAACC,WAAW,CAACC,IAAI,CAACJ,EAAWE,OAAO,EAC5CP,EAAKI,EAAQ,CAAA,KACbC,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAACC,IAAI,CAAC,CACpBC,QAAS,CACb,IAEGP,CACX,CAIA,SAASQ,IACL,IAAMC,EAAQ,IAAI,AAClBA,CAAAA,EAAMC,WAAW,CAAG,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACC,OAAO,EACvDJ,EAAMK,kBAAkB,CAAGL,EAAME,QAAQ,CACpCI,CAAC,CAAC,kBACFT,IAAI,CAAC,CAAEU,OAAQ,EAAG,GAClBC,IAAI,CAACR,EAAMC,WAAW,EACtBQ,GAAG,GACRT,EAAMP,OAAO,CAACC,WAAW,CAACgB,OAAO,CAAC,CAACC,EAAmBC,KAClD,GAEA,CAACZ,EAAMN,WAAW,CAACmB,IAAI,CAAC,AAACtB,GAAeA,EAAWE,OAAO,GAAKkB,GAAoB,CAC/E,IAAMpB,EAAaS,EAAMR,cAAc,CAACmB,EACxCX,CAAAA,EAAMP,OAAO,CAACC,WAAW,CAACkB,EAAE,CAAGrB,EAAWE,OAAO,AACrD,CACJ,GACAO,EAAMc,eAAe,GACrBhC,EAASkB,EAAO,SAAUA,EAAMc,eAAe,EAC/ChC,EAASkB,EAAO,UAAW,WACvBA,EAAMC,WAAW,CAACc,OAAO,GACzBf,EAAMK,kBAAkB,CAACU,OAAO,EACpC,GACAjC,EAASkB,EAAO,aAAc,SAAUgB,CAAK,EACzC,IAAMtB,EAAcM,EAAMN,WAAW,CAAEuB,EAA2B,AAAC,CAAA,AAAC,IAAI,CAACxB,OAAO,CAACyB,SAAS,EACtF,IAAI,CAACzB,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC1B,CAAC,CAAA,EAAGC,qBAAqB,CAG7BC,EAAoB,CAACL,EAAMM,QAAQ,CAAC,EAAE,CAACC,OAAO,CAAEC,EAAoBxB,EAAMP,OAAO,CAACgC,IAAI,EAClFzB,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,EAC7B1B,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,CAACF,gBAAgB,CAgB/CG,EAAiBX,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEC,EAAuB7B,EAAMP,OAAO,CAACyB,SAAS,EACxFlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACoC,aAAa,CAAGC,EAAmB/B,EAAMP,OAAO,CAACyB,SAAS,EAClGlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACsC,IAAI,CAChDtC,EAAYgB,OAAO,CAAC,AAACnB,IACbA,EAAWE,OAAO,CAACwC,YAAY,EAC/B1C,EAAWE,OAAO,CAACwC,YAAY,CAACC,mBAAmB,EACnD3C,EAAW4C,MAAM,CAACzB,OAAO,CAAC,AAAC0B,IACvB,GAAIA,EAAM3C,OAAO,CAAC4C,IAAI,CAAE,CACpB,IAAMC,EAAiBF,EAAM3C,OAAO,CAAC4C,IAAI,CACzCD,EAAMG,MAAM,CAAC7B,OAAO,CAAC,AAAC6B,IAClB,IAAMC,EAAcD,EAAOE,CAAC,CAAEC,EAAaH,EAAOI,MAAM,CAACC,KAAK,CAC1DL,EAAOI,MAAM,CAACC,KAAK,CAACC,KAAK,CACzB,GACAC,EAAW,CAAA,EAGf,GAAIJ,AAAe,KAAfA,EAAmB,CACnB,IAAMjF,EAAIuD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEmB,EAAS,AAAIC,MAAMvF,GACvD,IAAK,IAAImD,EAAI,EAAGA,EAAInD,EAAG,EAAEmD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,GAEhBmC,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACnBwB,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,EAC7BxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,GACpBD,EAAW,CAAA,CACf,CAuBA,GApBKA,GACD9B,EAAMM,QAAQ,CAACZ,OAAO,CAAC,AAACuC,IAChB,CAACH,GACDG,EAAI1B,OAAO,EACXmB,AAAe,KAAK,IAApBA,GACAF,IAAgBS,EAAI1B,OAAO,CAACmB,EAAW,GACnCX,GACAkB,EAAIrB,MAAM,CAAGD,EACbsB,CAAG,CAACA,EAAIrB,MAAM,CAAG,EAAE,EAAKC,EACpBS,EAGJW,EAAItD,IAAI,CAAC2C,GAEbQ,EAAW,CAAA,EAEnB,GAIA,CAACA,EAAU,CACX,IAAMrF,EAAIuD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEmB,EAAS,AAAIC,MAAMvF,GACvD,IAAK,IAAImD,EAAI,EAAGA,EAAInD,EAAG,EAAEmD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,EAEhBmC,CAAAA,CAAM,CAAC,EAAE,CAAGP,EACZO,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACA,KAAK,IAApBmB,GACAK,CAAAA,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,CAAU,EAE3CxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,EACxB,CACJ,EACJ,CACJ,EAER,GACA,IAAIG,EAAY,EAChBlC,EAAMM,QAAQ,CAACZ,OAAO,CAAC,AAACuC,IACpBC,EAAYC,KAAKC,GAAG,CAACF,EAAWD,EAAIrB,MAAM,CAC9C,GACA,IAAMyB,EAAUH,EAAYlC,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CACpD,IAAK,IAAIhB,EAAI,EAAGA,EAAIyC,EAASzC,IAAK,CAC9B,IAAM0C,EAASlC,AA7F0D,SAAUyB,CAAK,EACxF,IAAIU,SACJ,AAAItC,GAEIsC,AAAM,CAAA,IADVA,CAAAA,EAAItC,EAAyB4B,EAAK,EAEvBU,GAGfA,EAAI/B,EAAmB,IAAMqB,EACzBxB,GACO,CACHmC,YAAaD,EACbE,oBAAqBF,CACzB,EAEGA,CACX,EA6EyC3C,EAAI,GACrCS,GACAL,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOG,mBAAmB,EACjDzC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOE,WAAW,GAGzCxC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAE/B,CACJ,EACJ,CAIA,SAASI,IACL,IAAI,CAACzD,WAAW,CAACJ,IAAI,CAAC,IAAI,CAACO,OAAO,EAClC,IAAI,CAACV,WAAW,CAACgB,OAAO,CAAC,AAACnB,IACtBA,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAAC+D,OAAO,CAAC,CACvB7D,QAAS,CACb,EAAGP,EAAWqE,eAAe,CACjC,EACJ,CASA,SAASC,EAAsBC,CAAc,EACzC,IAAMpE,EAAc,IAAI,CAACA,WAAW,CAAEH,EAAa,AAACuE,AAAwB,gBAAxBA,EAAeC,IAAI,CACnED,EACA9E,EAAKU,EAAa,SAAUH,CAAU,EAClC,OAAOA,EAAWE,OAAO,CAACuE,EAAE,GAAKF,CACrC,GACAvE,IACAN,EAAUM,EAAY,UACtBR,EAAM,IAAI,CAACU,OAAO,CAACC,WAAW,CAAEH,EAAWE,OAAO,EAClDV,EAAMW,EAAaH,GACnBA,EAAWwB,OAAO,GAE1B,CAKA,SAASkD,IAELjE,AADc,IAAI,CACZN,WAAW,CAAG,EAAE,CACjB,IAAI,CAACD,OAAO,CAACC,WAAW,EACzB,CAAA,IAAI,CAACD,OAAO,CAACC,WAAW,CAAG,EAAE,AAAD,CAEpC,CAIA,SAASwE,EAAgCC,CAAO,EACvC,IAAI,CAACnE,KAAK,CAACoE,oBAAoB,EAChCD,EAAQE,KAAK,CAAC,IAAI,CAAErB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAAC8F,UAAW,GAElE,CAuCIxH,AACDA,CAAAA,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,CAAC,EADvByH,OAAO,CApBvB,SAAiBC,CAAe,CAAEC,CAAU,CAAEC,CAAY,EACtD,IAAMC,EAAaF,EAAWnG,SAAS,CACvC,GAAI,CAACqG,EAAWC,aAAa,CAAE,CAC3B,IAAMC,EAAeH,EAAapG,SAAS,CAC3CO,EAAS4F,EAAY,YAAaT,GAClCW,EAAWC,aAAa,CAAGzF,EAC3BwF,EAAWG,SAAS,CAACpF,IAAI,CAACI,GAC1B6E,EAAWI,mBAAmB,CAACtF,WAAW,CAAG,CAACN,EAAmB,CACjEwF,EAAWK,qBAAqB,CAACtF,IAAI,CAAC,eACtCiF,EAAW9D,eAAe,CAAG4C,EAC7BkB,EAAWM,gBAAgB,CAAGrB,EAC9Be,EAAWpF,cAAc,CAAG,SAA6BH,CAAW,EAChE,IACsBE,EAAa,GADdkF,CAAAA,EAAgBU,KAAK,CAAC9F,EAAY+F,IAAI,CAAC,EACxDX,CAAc,EAAiC,IAAI,CAAEpF,GAEzD,OADA,IAAI,CAACK,WAAW,CAACC,IAAI,CAACJ,GACfA,CACX,EACAJ,EAAK2F,EAAc,uBAAwBZ,EAC/C,CACJ,EAQyB,IAAMmB,EAA+BtI,EAS5D,CAAEuI,QAAAA,CAAO,CAAE,CAAIzG,IAkoBf,CAAE0G,IAAAA,CAAG,CAAEC,cAAAA,CAAa,CAAE,CAAI3G,IAE1B,CAAEC,SAAU2G,CAAqB,CAAExG,UAAWyG,CAAsB,CAAEC,WAAAA,CAAU,CAAEzG,KAAM0G,CAAiB,CAAEC,YAAAA,CAAW,CAAE,CAAIhH,IAsO/FiH,EA7NnC,MAUIC,WAAY,CACR,IAAMC,EAAU,IAAI,CAAEC,EAAoB,SAAUC,CAAO,EACvDT,EAAsBS,EAASV,EAAgB,aAAe,YAAa,AAACW,IACxEH,EAAQI,WAAW,CAACD,EACxB,EAAG,CAAEE,QAAS,CAAA,CAAM,EACxB,EA0BA,GAzBAJ,EAAkB,IAAI,CAACrG,OAAO,CAACsG,OAAO,EACtC,AAACF,CAAAA,EAAQ7D,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,AAAC0B,IACxBA,EAAM3C,OAAO,CAAC6G,OAAO,EACrBlE,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACkE,aAAa,EAEjCN,EAAkB7D,EAAMxC,OAAO,CAACyC,IAAI,CAAC6D,OAAO,CAEpD,GACAP,EAAWK,EAAQvG,OAAO,CAAC+G,MAAM,CAAE,CAACxF,EAAOoE,KACvC,IAAMqB,EAAe,SAAUN,CAAC,EACf,UAATf,GAAqBY,EAAQU,WAAW,EACxC1F,EAAMvC,IAAI,CAACuH,EAASA,EAAQhG,KAAK,CAAC2G,OAAO,EAAEC,UAAUT,GAAIH,EAAQa,MAAM,CAE/E,CACI,AAA+C,CAAA,KAA/C,AAACb,CAAAA,EAAQc,YAAY,EAAI,EAAE,AAAD,EAAGC,OAAO,CAAC3B,IACrCK,EAAsBO,EAAQpG,OAAO,CAACsG,OAAO,CAAEd,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,GAChFL,EAAQpG,OAAO,CAACoH,GAAG,EACnBvB,EAAsBO,EAAQpG,OAAO,CAACoH,GAAG,CAAE5B,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,IAIpFZ,EAAsBO,EAASZ,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,EAE5E,GACIL,EAAQvG,OAAO,CAACwH,SAAS,GACzBxB,EAAsBO,EAAS,OAAQA,EAAQkB,MAAM,EACjD,CAAClB,EAAQpG,OAAO,CAACM,QAAQ,CAACiH,UAAU,EAAE,CACtC,IAAMC,EAAa,CACfC,OAAQ,CACJ5E,EAAG,YACH6E,EAAG,YACHC,GAAI,MACR,CAAC,CAACvB,EAAQvG,OAAO,CAACwH,SAAS,CAAC,AAChC,EACAjB,EAAQpG,OAAO,CAAC4H,GAAG,CAACJ,GACpB,AAACpB,CAAAA,EAAQ7D,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,AAAC0B,IACxBA,EAAM3C,OAAO,CAAC6G,OAAO,EACrBlE,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACkE,aAAa,EACjCnE,EAAMxC,OAAO,CAACyC,IAAI,CAACmF,GAAG,CAACJ,EAE/B,EACJ,CAECpB,EAAQyB,UAAU,EACnB/B,EAAuBM,EAAS,MAExC,CAIAjF,SAAU,CACN,IAAI,CAAC2G,eAAe,GACpB7B,EAAY,IAAI,EAChB,IAAI,CAAC8B,QAAQ,CAAG,IACpB,CAKAC,mBAAmBzB,CAAC,CAAE0B,CAAE,CAAEC,CAAE,CAAE,CAC1B,IAAIC,EAAS5B,EAAE6B,UAAU,CAAGF,EAAIG,EAAS9B,EAAE+B,UAAU,CAAGL,EAAIM,EAAKhC,EAAEiC,MAAM,CAAGN,EAAIO,EAAKlC,EAAEmC,MAAM,CAAGT,EAAIU,EASpG,OARI,IAAI,CAACvI,KAAK,CAACwI,QAAQ,GACnBD,EAAON,EACPA,EAASF,EACTA,EAASQ,EACTA,EAAOF,EACPA,EAAKF,EACLA,EAAKI,GAEFpF,KAAKsF,KAAK,CAACN,EAAIE,GAAMlF,KAAKsF,KAAK,CAACV,EAAQE,EACnD,CAKAS,iBAAiBvC,CAAC,CAAE0B,CAAE,CAAEC,CAAE,CAAE,CACxB,IAAMG,EAAS9B,EAAE+B,UAAU,CAAGL,EAAIE,EAAS5B,EAAE6B,UAAU,CAAGF,EAAIO,EAAKlC,EAAEmC,MAAM,CAAGT,EAAIM,EAAKhC,EAAEiC,MAAM,CAAGN,EAC9Fa,EAAK,AAACN,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAAIW,EAAK,AAACT,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAChE,GAAI,IAAI,CAAC/H,KAAK,CAACwI,QAAQ,CAAE,CACrB,IAAMD,EAAOK,EACbA,EAAKD,EACLA,EAAKJ,CACT,CACA,MAAO,CACH9F,EAAGkG,EACHrB,EAAGsB,CACP,CACJ,CAKAC,uBAAuB1C,CAAC,CAAE,CACtB,IAAIkC,EAAKlC,EAAEmC,MAAM,CAAGnC,EAAE+B,UAAU,CAAEC,EAAKhC,EAAEiC,MAAM,CAAGjC,EAAE6B,UAAU,CAAEO,EAMhE,OALI,IAAI,CAACvI,KAAK,CAACwI,QAAQ,GACnBD,EAAOJ,EACPA,EAAKE,EACLA,EAAKE,GAEF,CACH9F,EAAG4F,EACHf,EAAGa,CACP,CACJ,CAMAjB,OAAOf,CAAC,CAAE,CACN,GAAI,IAAI,CAACnG,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAG,IAAI,CAACtI,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAG,IAAI,CAACpI,KAAK,CAACgJ,OAAO,CAAE,CACvFC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,EACjB,CAAA,MAA3B,IAAI,CAAC1G,OAAO,CAACwH,SAAS,EACtBiC,CAAAA,EAAY5B,CAAC,CAAG,CAAA,EAEW,MAA3B,IAAI,CAAC7H,OAAO,CAACwH,SAAS,EACtBiC,CAAAA,EAAYzG,CAAC,CAAG,CAAA,EAGhBuD,AADY,IAAI,CACRzD,MAAM,CAACX,MAAM,CACrBoE,AAFY,IAAI,CAERmD,SAAS,CAACD,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,GAG9CtB,AALY,IAAI,CAKRoD,MAAM,CAAC1I,OAAO,CAAC,AAAC2I,GAAUA,EAAMF,SAAS,CAACD,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,GAC9EtB,AANY,IAAI,CAMR7D,MAAM,CAACzB,OAAO,CAAC,AAAC0B,GAAUA,EAAM+G,SAAS,CAACD,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,IAElF,IAAI,CAAChI,MAAM,CAAC,CAAA,EAChB,CACJ,CAKA8G,YAAYD,CAAC,CAAE,CAKX,GAJIA,EAAEmD,cAAc,EAChBnD,EAAEmD,cAAc,GAGhBnD,AAAa,IAAbA,EAAEoD,MAAM,CACR,OAEJ,IAAMvD,EAAU,IAAI,CAAEW,EAAUX,EAAQhG,KAAK,CAAC2G,OAAO,CAGrD6C,EAAmB,AAACrD,GAAGsD,oBAAoBD,kBAAqB,CAAA,EAE5DtB,EAAa/B,AADjBA,CAAAA,EAAIQ,GAASC,UAAUT,IAAMA,CAAAA,EACVmC,MAAM,CAAEN,EAAa7B,EAAEiC,MAAM,AAChDpC,CAAAA,EAAQU,WAAW,CAAG,CAAA,EACtBV,EAAQhG,KAAK,CAACoE,oBAAoB,CAAG,CAAA,EACrC4B,EAAQ0D,UAAU,CAAGjE,EAAsBF,EAAKC,GAAiBgE,EAAmB,YAAc,YAAa,SAAUrD,CAAC,EACtHH,EAAQ2D,UAAU,CAAG,CAAA,EAErBxD,AADAA,CAAAA,EAAIQ,GAASC,UAAUT,IAAMA,CAAAA,EAC3B+B,UAAU,CAAGA,EACf/B,EAAE6B,UAAU,CAAGA,EACftC,EAAuBM,EAAS,OAAQG,GACxC+B,EAAa/B,EAAEmC,MAAM,CACrBN,EAAa7B,EAAEiC,MAAM,AACzB,EAAG5C,GAAiBgE,EAAmB,CAAEnD,QAAS,CAAA,CAAM,EAAI,KAAK,GACjEL,EAAQ4D,aAAa,CAAGnE,EAAsBF,EAAKC,GAAiBgE,EAAmB,WAAa,UAAW,WAG3G,IAAMjK,EAAaqG,EAAkBI,EAAQa,MAAM,EAAIb,EAAQa,MAAM,CAACtH,UAAU,CAAEyG,EAAQa,MAAM,EAC5FtH,GAEAA,CAAAA,EAAWmH,WAAW,CAAGV,EAAQ2D,UAAU,AAAD,EAE9C3D,EAAQU,WAAW,CAAGV,EAAQ2D,UAAU,CACxC3D,EAAQhG,KAAK,CAACoE,oBAAoB,CAAG,CAAA,EACjC4B,EAAQ2D,UAAU,EAElBjE,EAAuBE,EAAkBrG,EACzCyG,GAAU,eAEdA,EAAQ2D,UAAU,CAAG,CAAA,EACrB3D,EAAQ6D,SAAS,EACrB,EAAGrE,GAAiBgE,EAAmB,CAAEnD,QAAS,CAAA,CAAM,EAAI,KAAK,EACrE,CAIAwD,WAAY,CACR,IAAI,CAACnC,eAAe,EACxB,CAKAA,iBAAkB,CACV,IAAI,CAACgC,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAI,CAACA,UAAU,EAAC,EAElC,IAAI,CAACE,aAAa,EAClB,CAAA,IAAI,CAACA,aAAa,CAAG,IAAI,CAACA,aAAa,EAAC,CAEhD,CACJ,EAiBM,CAAEE,MAAAA,CAAK,CAAE5K,KAAM6K,CAAiB,CAAE,CAAIlL,IA4HTmL,EA/FnC,cAA2BlE,EAMvBmE,YAAYjK,CAAK,CAAE6G,CAAM,CAAEpH,CAAO,CAAEoD,CAAK,CAAE,CACvC,KAAK,GAQL,IAAI,CAACiE,YAAY,CAAG,CAAC,OAAO,CAC5B,IAAI,CAAC9G,KAAK,CAAGA,EACb,IAAI,CAAC6G,MAAM,CAAGA,EACd,IAAI,CAACpH,OAAO,CAAGA,EACf,IAAI,CAACoD,KAAK,CAAGkH,EAAkBtK,EAAQoD,KAAK,CAAEA,EAClD,CAUA9B,SAAU,CACN,KAAK,CAACA,UACF,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAExC,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAAC6G,MAAM,CAAG,KACd,IAAI,CAACpH,OAAO,CAAG,IACnB,CAMAH,OAAO4K,CAAS,CAAE,CACd,IAAI,CAACtK,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,IAAI,CAACzK,OAAO,CAAC0K,UAAU,CAAC1L,IAAI,CAAC,IAAI,CAAE,IAAI,CAACoI,MAAM,EAC/F,CAKAuD,QAAS,CACL,IAAMpK,EAAQ,IAAI,CAACA,KAAK,CAAEP,EAAU,IAAI,CAACA,OAAO,AAChD,CAAA,IAAI,CAACG,OAAO,CAAGI,EAAME,QAAQ,CACxBmK,MAAM,CAAC5K,EAAQ4K,MAAM,CAAE,EAAG,EAAG5K,EAAQ6K,KAAK,CAAE7K,EAAQ8K,MAAM,EAC1D9J,GAAG,CAACT,EAAMK,kBAAkB,EAC5BmH,GAAG,CAAC/H,EAAQ+K,KAAK,EACtB,IAAI,CAACC,aAAa,CAAChL,EAAQiL,OAAO,EAElC,IAAI,CAAC3E,SAAS,EAClB,CAUA0E,cAAcC,CAAO,CAAE,CACnB,IAAI,CAAC9K,OAAO,CAAC8K,EAAU,OAAS,OAAO,GACvC,IAAI,CAACjL,OAAO,CAACiL,OAAO,CAAGA,CAC3B,CASAC,OAAOtL,CAAW,CAAE,CAChB,IAAMW,EAAQ,IAAI,CAACA,KAAK,CAAE6G,EAAS,IAAI,CAACA,MAAM,CAAEhE,EAAQ,IAAI,CAACA,KAAK,CAAEpD,EAAUqK,EAAM,CAAA,EAAM,IAAI,CAACrK,OAAO,CAAEJ,GACxG,IAAI,CAAC0B,OAAO,GACZ,IAAI,CAACkJ,WAAW,CAACjK,EAAO6G,EAAQpH,EAASoD,GACzC,IAAI,CAACuH,MAAM,CAACpK,EAAMK,kBAAkB,EACpC,IAAI,CAACf,MAAM,EACf,CACJ,EAuBA,IAAIsL,EAAmIvN,EAAoB,KACvJwN,EAAuJxN,EAAoBI,CAAC,CAACmN,GASjL,GAAM,CAAEjI,OAAQ,CAAEpE,UAAWuM,CAAW,CAAE,CAAE,CAAID,IAE1C,CAAEvF,QAASyF,CAAiB,CAAE9L,UAAW+L,CAAmB,CAAE,CAAInM,GA4BxE,OAAMoM,EAiBF,OAAOC,UAAUC,CAAK,CAAE,CACpB,OAAO,IAAIF,EAAUE,EAAMxI,MAAM,CAAC3C,KAAK,CAAE,KAAM,CAC3CyC,EAAG0I,EAAM1I,CAAC,CACV6E,EAAG6D,EAAM7D,CAAC,CACV1E,MAAOuI,EAAMxI,MAAM,CAACC,KAAK,CACzBwI,MAAOD,EAAMxI,MAAM,CAACyI,KAAK,AAC7B,EACJ,CAcA,OAAOC,cAAcF,CAAK,CAAEG,CAAe,CAAE,CACzC,IAAM3I,EAASwI,EAAMxI,MAAM,CAAE3C,EAAQ2C,EAAO3C,KAAK,CAC7CyC,EAAI0I,EAAMI,KAAK,EAAI,EAAGjE,EAAI6D,EAAMK,KAAK,EAAI,EAAGpL,EAgBhD,OAfIJ,EAAMwI,QAAQ,GACV2C,EAAMM,IAAI,EACVhJ,EAAI0I,EAAMK,KAAK,CACflE,EAAI6D,EAAMI,KAAK,GAGf9I,EAAIzC,EAAM0L,SAAS,CAAIP,CAAAA,EAAMK,KAAK,EAAI,CAAA,EACtClE,EAAItH,EAAM2L,UAAU,CAAIR,CAAAA,EAAMI,KAAK,EAAI,CAAA,IAG3C5I,GAAU,CAAC2I,IAEX7I,GAAKrC,AADLA,CAAAA,EAAUuC,EAAOiJ,UAAU,EAAC,EACfC,UAAU,CACvBvE,GAAKlH,EAAQ0L,UAAU,EAEpB,CACHrJ,EAAGA,EACH6E,EAAGA,CACP,CACJ,CAYA,OAAOyE,eAAeZ,CAAK,CAAE,CACzB,MAAO,CACH1I,EAAG0I,EAAM1I,CAAC,CACV6E,EAAG6D,EAAM7D,CAAC,CACV1E,MAAOuI,EAAMxI,MAAM,CAACC,KAAK,CACzBwI,MAAOD,EAAMxI,MAAM,CAACyI,KAAK,AAC7B,CACJ,CAMAnB,YAAYjK,CAAK,CAAE6G,CAAM,CAAEpH,CAAO,CAAE,CAYhC,IAAI,CAACgM,IAAI,CAAG,CAAA,EAEZ,IAAI,CAACN,KAAK,CAAG,IAAI,CAOjB,IAAI,CAACxI,MAAM,CAAG,CACV+H,QAAS,CAAA,EACT1K,MAAOA,EACP4L,WAAYd,EAAYc,UAAU,AACtC,EAKA,IAAI,CAAC/E,MAAM,CAAGA,GAAU,KAOxB,IAAI,CAACpH,OAAO,CAAGA,EAkCf,IAAI,CAACuM,YAAY,CAAC,IAAI,CAACC,UAAU,GACrC,CAMAD,aAAavM,CAAO,CAAE,CAClB,IAAI,CAACyM,OAAO,CAAGzM,EAAQyM,OAAO,CAC9B,IAAI,CAACC,OAAO,CAAC1M,EAAS,KACtB,IAAI,CAAC0M,OAAO,CAAC1M,EAAS,KACtB,IAAI,CAAC2M,OAAO,EAChB,CAOAH,YAAa,CACT,OAAO,IAAI,CAACI,iBAAiB,GACzB,IAAI,CAAC5M,OAAO,CAAC,IAAI,CAACoH,MAAM,EACxB,IAAI,CAACpH,OAAO,AACpB,CAOA4M,mBAAoB,CAChB,MAAO,AAAwB,YAAxB,OAAO,IAAI,CAAC5M,OAAO,AAC9B,CAMAqJ,cAAe,CACX,IAAMyC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAQ,IAAI,CAACA,KAAK,CAAE5I,EAAQ,IAAI,CAACD,MAAM,CAACC,KAAK,CAAEwI,EAAQ,IAAI,CAACzI,MAAM,CAACyI,KAAK,CAAEjF,EAAI,CACpG1D,EAAG8I,EACHjE,EAAGkE,EACH1C,aAAc,CAAA,EACdrJ,QAAS,CAAC,CACd,EAWA,OAVImD,GACAuD,CAAAA,EAAE2C,YAAY,CAAGiC,EAAkBQ,IAAUA,GAAS,GAAKA,GAAS3I,EAAM0J,GAAG,AAAD,EAE5ElB,GACAjF,CAAAA,EAAE2C,YAAY,CACV3C,EAAE2C,YAAY,EACViC,EAAkBS,IAClBA,GAAS,GAAKA,GAASJ,EAAMkB,GAAG,AAAD,EAE3CtB,EAAoB,IAAI,CAACrI,MAAM,CAAC3C,KAAK,CAAE,oBAAqBmG,GACrDA,EAAE2C,YAAY,AACzB,CAKAsD,SAAU,CACN,IAAMzJ,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAEwI,EAAQzI,EAAOyI,KAAK,CAAE3L,EAAU,IAAI,CAACwM,UAAU,GAC7FrJ,GACA,IAAI,CAACH,CAAC,CAAGhD,EAAQgD,CAAC,CAClB,IAAI,CAAC8I,KAAK,CAAG3I,EAAM2J,QAAQ,CAAC9M,EAAQgD,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KAAK,EACd,IAAI,CAAC8I,KAAK,CAAG9L,EAAQgD,CAAC,EAEtB2I,GACA,IAAI,CAAC9D,CAAC,CAAG7H,EAAQ6H,CAAC,CAClB,IAAI,CAACkE,KAAK,CAAGJ,EAAMmB,QAAQ,CAAC9M,EAAQ6H,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KACT,IAAI,CAACkE,KAAK,CAAG/L,EAAQ6H,CAAC,EAE1B,IAAI,CAACkF,QAAQ,CAAG,IAAI,CAAC1D,YAAY,EACrC,CAKA2D,gBAAiB,CACb,IAAM9J,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAEwI,EAAQzI,EAAOyI,KAAK,AACtE,CAAA,IAAI,CAAC3I,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAAGG,EACtB,IAAI,CAACnD,OAAO,CAACgD,CAAC,CAAGG,EAAM8J,OAAO,CAAC,IAAI,CAACnB,KAAK,CAAE,CAAA,GAC3C,IAAI,CAACA,KAAK,CACd,IAAI,CAACjE,CAAC,CAAG,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,CAAG8D,EACtBA,EAAMsB,OAAO,CAAC,IAAI,CAAClB,KAAK,CAAE,CAAA,GAC1B,IAAI,CAACA,KAAK,AAClB,CAQAmB,OAAO9E,CAAE,CAAEC,CAAE,CAAE8E,CAAO,CAAE,CACpB,GAAI,CAAC,IAAI,CAACP,iBAAiB,GAAI,CAC3B,IAAMQ,EAAM1J,KAAK0J,GAAG,CAACD,GAAUE,EAAM3J,KAAK2J,GAAG,CAACF,GAAUnK,EAAI,IAAI,CAAC8I,KAAK,CAAG1D,EAAIP,EAAI,IAAI,CAACkE,KAAK,CAAG1D,CAC9F,CAAA,IAAI,CAACyD,KAAK,CAAGwB,AAD0FtK,EAAIoK,EAAMvF,EAAIwF,EACnGjF,EAClB,IAAI,CAAC2D,KAAK,CAAGwB,AAFkHvK,EAAIqK,EAAMxF,EAAIuF,EAE3H/E,EAClB,IAAI,CAAC2E,cAAc,EACvB,CACJ,CAkBAQ,MAAMpF,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,CAAE,CAClB,GAAI,CAAC,IAAI,CAACyD,iBAAiB,GAAI,CAC3B,IAAM5J,EAAI,IAAI,CAAC8I,KAAK,CAAG5C,EAAIrB,EAAI,IAAI,CAACkE,KAAK,CAAG5C,CAC5C,CAAA,IAAI,CAAC2C,KAAK,CAAGwB,AADyC,CAAA,EAAIpE,CAAC,EAAKd,EAC9CpF,EAClB,IAAI,CAAC+I,KAAK,CAAGwB,AAF6D,CAAA,EAAIpE,CAAC,EAAKd,EAElER,EAClB,IAAI,CAACmF,cAAc,EACvB,CACJ,CAQAN,QAAQ1M,CAAO,CAAEyN,CAAI,CAAE,CACnB,IAAMC,EAAYD,EAAO,OAASE,EAAc3N,CAAO,CAAC0N,EAAS,CAAEnN,EAAQ,IAAI,CAAC2C,MAAM,CAAC3C,KAAK,AAC5F,CAAA,IAAI,CAAC2C,MAAM,CAACwK,EAAS,CACjB,AAAuB,UAAvB,OAAOC,EACHA,EACArC,EAAkBqC,GACbpN,CAAK,CAACmN,EAAS,CAACC,EAAY,EAEzBpN,EAAM5B,GAAG,CAACgP,GACd,IAChB,CAOAC,UAAW,CACP,IAAMC,EAAS,CAAC,IAAI,CAAC/B,KAAK,CAAE,IAAI,CAACC,KAAK,CAAE,EAAG,EAAE,CAK7C,OAJI,IAAI,CAAC7I,MAAM,CAAC3C,KAAK,CAACwI,QAAQ,GAC1B8E,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC9B,KAAK,CACtB8B,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC/B,KAAK,EAEnB+B,CACX,CAkBAnE,UAAUoE,CAAG,CAAEC,CAAG,CAAEnF,CAAE,CAAEF,CAAE,CAAE,CACnB,IAAI,CAACkE,iBAAiB,KACvB,IAAI,CAACd,KAAK,EAAIlD,EACd,IAAI,CAACmD,KAAK,EAAIrD,EACd,IAAI,CAACsE,cAAc,GAE3B,CACJ,EAiHA,AAAC,SAAUzP,CAAa,EAepB,SAASyQ,IACL,IAAMC,EAAgB,IAAI,CAACA,aAAa,CAAEC,EAAuB,IAAI,CAAClO,OAAO,CAACiO,aAAa,EAAI,EAAE,CACjGC,EAAqBjN,OAAO,CAAC,CAACkN,EAAqBhN,KAC/C,IAAMnB,EAAUZ,IAA8EiL,KAAK,CAAC,IAAI,CAACrK,OAAO,CAACmO,mBAAmB,CAAEA,EACjInO,CAAAA,EAAQoD,KAAK,EACdpD,CAAAA,EAAQoD,KAAK,CAAGjC,CAAAA,EAEpB+M,CAAoB,CAAC/M,EAAE,CAAGnB,EAC1BiO,EAAc/N,IAAI,CAAC,IAAIqK,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAEP,GACtE,EACJ,CAUA,SAAS6N,EAAOnC,CAAK,EACjB,IAAM/K,EAAU+K,EAAMxI,MAAM,CAACiJ,UAAU,GAAI5L,EAAQmL,EAAMxI,MAAM,CAAC3C,KAAK,CAAE6N,EAAM1C,EAAMM,IAAI,CACnFN,EAAMkC,QAAQ,GACdrN,EAAM8N,OAAO,EACT9N,EAAM8N,OAAO,CAACC,SAAS,CAACtP,IAAI,CAAC,CACzBuB,MAAOmL,EAAMxI,MAAM,CAAC3C,KAAK,AAC7B,EAAGmL,IACH,CAAC,EAAG,EAAG,EAAG,EAAE,CAAEmC,EAAS,CAC3B7K,EAAGoL,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAACpO,OAAO,CAACgD,CAAC,EAAI,CAAA,EAC/B6E,EAAGuG,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAACpO,OAAO,CAAC6H,CAAC,EAAI,CAAA,EAC/BiD,OAAQsD,CAAG,CAAC,EAAE,EAAI,EAClBvD,MAAOuD,CAAG,CAAC,EAAE,EAAI,CACrB,EACA,MAAO,CACHG,iBAAkBV,EAClBW,iBAAkBpP,IAA8EiL,KAAK,CAACwD,EAAQ,CAC1G7K,EAAG6K,EAAO7K,CAAC,CAAI0I,CAAAA,EAAMM,IAAI,CAAGrL,EAAQyL,UAAU,CAAG7L,EAAM+I,QAAQ,AAAD,EAC9DzB,EAAGgG,EAAOhG,CAAC,CAAI6D,CAAAA,EAAMM,IAAI,CAAGrL,EAAQ0L,UAAU,CAAG9L,EAAMgJ,OAAO,AAAD,CACjE,EACJ,CACJ,CA6BA,SAASkF,IACL,IAAI,CAACR,aAAa,CAAChN,OAAO,CAAC,AAACyN,GAAiBA,EAAapN,OAAO,IACjE,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAAC0N,aAAa,CAAG,KACrB,IAAI,CAACnL,MAAM,CAAG,KACd,IAAI,CAAC9C,OAAO,CAAG,KACX,IAAI,CAACF,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAG,CAE7B,CAOA,SAAS6O,IACL,IAAM3O,EAAU,IAAI,CAACA,OAAO,CAC5B,OAAQA,EAAQ8C,MAAM,EACjB9C,EAAQ0L,KAAK,EAAItM,IAA8EwP,KAAK,CAAC5O,EAAQ0L,KAAK,CAC3H,CAOA,SAASmD,IACL,IACI1N,EAAGuK,EADDoD,EAAgB,IAAI,CAACH,gBAAgB,GAAI7L,EAAS,IAAI,CAACA,MAAM,CAAE+J,EAAM,AAACiC,GAAiBA,EAAc3M,MAAM,EAAK,EAEtH,IAAKhB,EAAI,EAAGA,EAAI0L,EAAK1L,IAAK,CAEtB,GAAI,CADJuK,CAAAA,EAAQ,IAAI,CAACA,KAAK,CAACoD,CAAa,CAAC3N,EAAE,CAAE2B,CAAM,CAAC3B,EAAE,CAAA,EAClC,CACR2B,EAAOX,MAAM,CAAG,EAChB,MACJ,CACIuJ,EAAMM,IAAI,EACVN,EAAMiB,OAAO,GAEjB7J,CAAM,CAAC3B,EAAE,CAAGuK,CAChB,CACA,OAAO5I,CACX,CAWA,SAAS4I,EAAMqD,CAAY,CAAErD,CAAK,EAC9B,GAAIqD,GAAgBA,EAAa7L,MAAM,CACnC,OAAO6L,EAEX,GAAI,CAACrD,GAASA,AAAiB,OAAjBA,EAAMxI,MAAM,EACtB,GAAI9D,IAA8E4P,QAAQ,CAACD,GACvFrD,EAAQ,IA1PoCF,EA0PV,IAAI,CAACjL,KAAK,CAAE,IAAI,CAAEwO,QAEnD,GAAI3P,IAA8E6P,QAAQ,CAACF,GAC5FrD,EAAQ,IAAI,CAACnL,KAAK,CAAC5B,GAAG,CAACoQ,IAAiB,UAEvC,GAAI,AAAwB,YAAxB,OAAOA,EAA6B,CACzC,IAAMG,EAAcH,EAAa/P,IAAI,CAAC0M,EAAO,IAAI,EACjDA,EAAQwD,EAAYhM,MAAM,CACtBgM,EACA,IAnQwC1D,EAmQd,IAAI,CAACjL,KAAK,CAAE,IAAI,CAAEwO,EACpD,EAEJ,OAAOrD,CACX,CAKA,SAASyD,EAAoB1E,CAAS,EAClC,IAAI,CAACwD,aAAa,CAAChN,OAAO,CAAC,AAACyN,GAAiBA,EAAa7O,MAAM,CAAC4K,GACrE,CAKA,SAAS2E,IACL,IAAI,CAACnB,aAAa,CAAChN,OAAO,CAAC,AAACyN,GAAiBA,EAAa/D,MAAM,GACpE,CAeA,SAAS0E,EAAUC,CAAc,CAAElH,CAAE,CAAEC,CAAE,CAAEkH,CAAE,CAAEC,CAAE,EAC7C,GAAI,IAAI,CAACjP,KAAK,CAACwI,QAAQ,CAAE,CACrB,IAAMD,EAAOV,EACbA,EAAKC,EACLA,EAAKS,CACT,CACA,IAAI,CAAChG,MAAM,CAAC7B,OAAO,CAAC,CAACwO,EAAQtO,IAAO,IAAI,CAACuO,cAAc,CAACJ,EAAgBlH,EAAIC,EAAIkH,EAAIC,EAAIrO,GAAK,IAAI,CACrG,CAmBA,SAASuO,EAAeJ,CAAc,CAAElH,CAAE,CAAEC,CAAE,CAAEkH,CAAE,CAAEC,CAAE,CAAErO,CAAC,EACrD,IAAIuK,EAAQ,IAAI,CAAC5I,MAAM,CAAC3B,EAAE,AACrBuK,CAAAA,EAAMM,IAAI,EACXN,CAAAA,EAAQ,IAAI,CAAC5I,MAAM,CAAC3B,EAAE,CAAGwO,AAjUuBnE,EAiUDC,SAAS,CAACC,EAAK,EAElEA,CAAK,CAAC4D,EAAe,CAAClH,EAAIC,EAAIkH,EAAIC,EACtC,CASA,SAAS9F,EAAUd,CAAE,CAAEF,CAAE,EACrB,IAAI,CAAC2G,SAAS,CAAC,YAAa,KAAM,KAAMzG,EAAIF,EAChD,CAWA,SAASkH,EAAehH,CAAE,CAAEF,CAAE,CAAEvH,CAAC,EAC7B,IAAI,CAACuO,cAAc,CAAC,YAAa,KAAM,KAAM9G,EAAIF,EAAIvH,EACzD,CAlKA5D,EAAcwH,OAAO,CAnBrB,SAAiB8K,CAAkB,EAC/B,IAAMC,EAAeD,EAAmB/Q,SAAS,AAC5CgR,CAAAA,EAAa9B,gBAAgB,EAC9B5O,IAA8EiL,KAAK,CAAC,CAAA,EAAMyF,EAAc,CACpG9B,iBAAAA,EACAH,OAAAA,EACAY,qBAAAA,EACAE,iBAAAA,EACAE,WAAAA,EACAnD,MAAAA,EACAyD,oBAAAA,EACAC,oBAAAA,EACAC,UAAAA,EACAK,eAAAA,EACAhG,UAAAA,EACAkG,eAAAA,CACJ,EAER,CAoKJ,EAAGrS,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAMwS,EAA6BxS,EAW1D,CAAE8M,MAAO2F,CAAkB,CAAE,CAAI5Q,GAWvC,OAAM6Q,EAMFzF,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE8M,CAAQ,CAAE,CAC9C,IAAI,CAACpQ,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAAC4P,UAAU,CAAID,AAAa,UAAbA,EAAuB,SAAW,SACrD,IAAI,CAACjC,aAAa,CAAG,EAAE,CACvB,IAAI,CAACjO,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACM,KAAK,CAAGA,EACb,IAAI,CAAC8M,QAAQ,CAAGA,EAChB,IAAI,CAACE,IAAI,CAACtQ,EAAYE,EAASoD,EACnC,CAUAhD,KAEA,GAAGiQ,CAAK,CAAE,CACN,IAAI,CAAClQ,OAAO,CAACC,IAAI,CAACwE,KAAK,CAAC,IAAI,CAACzE,OAAO,CAAE2E,UAC1C,CASAwL,iBAAiBtQ,CAAO,CAAE,CACtB,IACI1B,EAAKiS,EADHC,EAAM,IAAI,CAAChG,WAAW,CAACiG,QAAQ,CAAEC,EAAQ,CAAC,EAAGhJ,EAAa,IAAI,CAACnH,KAAK,CAACmH,UAAU,CAErF,IAAKpJ,KAAO0B,EACRuQ,EAAYC,CAAG,CAAClS,EAAI,CACI,KAAA,IAAbkS,CAAG,CAAClS,EAAI,EACd,AAACoJ,GACE,AAC4B,KAD5B,CAAC,OAAQ,SAAU,eAAe,CAC7BJ,OAAO,CAACiJ,IACjBG,CAAAA,CAAK,CAACH,EAAU,CAAGvQ,CAAO,CAAC1B,EAAI,AAAD,EAGtC,OAAOoS,CACX,CAKApP,SAAU,CACF,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAEpC,IAAI,CAACqP,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACrP,OAAO,EAAC,EAExC,IAAI,CAACmN,oBAAoB,EAC7B,CAKA2B,KAAKtQ,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CAC7B,IAAI,CAACtD,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACmL,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC7K,KAAK,CAAGA,EACb,IAAI,CAACyL,UAAU,GACf,IAAI,CAACb,gBAAgB,EACzB,CAKAnO,OAAO4K,CAAS,CAAE,CACd,IAAI,CAAC0E,mBAAmB,CAAC1E,EAC7B,CAKAE,OAEAiG,CAAY,CAAE,CACN,IAAI,CAAC5Q,OAAO,CAAC6Q,SAAS,EAAI,IAAI,CAAC1Q,OAAO,EACtC,IAAI,CAACA,OAAO,CAAC2Q,QAAQ,CAAC,IAAI,CAAC9Q,OAAO,CAAC6Q,SAAS,EAEhD,IAAI,CAACzB,mBAAmB,EAC5B,CAUAlC,OAAO9E,CAAE,CAAEC,CAAE,CAAE8E,CAAO,CAAE,CACpB,IAAI,CAACkC,SAAS,CAAC,SAAUjH,EAAIC,EAAI8E,EACrC,CAaAK,MAAMpF,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,CAAE,CAClB,IAAI,CAACkG,SAAS,CAAC,QAASjH,EAAIC,EAAIa,EAAIC,EACxC,CAKA4H,2BAA2B9F,CAAO,CAAE,CAChC,IAAI,CAACgD,aAAa,CAAChN,OAAO,CAAC,AAACyN,IACxBA,EAAa1D,aAAa,CAACC,EAC/B,EACJ,CAOA+F,eAAgB,CACZ,MAAO,CAAC,CAAC,IAAI,CAAClO,MAAM,CAACX,MAAM,AAC/B,CAaA8O,eAAerI,CAAE,CAAEF,CAAE,CAAEwI,CAAoB,CAAE,CACzC,IAAM3Q,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAEnC4Q,EAAe,IAAI,CAACrR,UAAU,CAACF,WAAW,CAE1CwR,EAAkB7Q,EAAMN,WAAW,CAACqH,OAAO,CAAC,IAAI,CAACxH,UAAU,EAAGuR,EAAe9Q,EAAMP,OAAO,CAACC,WAAW,CAACmR,EAAgB,CACvH,IAAI,CAACxB,cAAc,CAAChH,EAAIF,EAAI,GACxBwI,GACA,IAAI,CAACtB,cAAc,CAAChH,EAAIF,EAAI,GAKhC2I,CAAY,CAAC,IAAI,CAAClB,UAAU,CAAC,CAAC,IAAI,CAAC/M,KAAK,CAAC,CACpCsI,KAAK,CAAG,IAAI,CAAC1L,OAAO,CAAC0L,KAAK,CAC/ByF,CAAY,CAAC,IAAI,CAAChB,UAAU,CAAC,CAAC,IAAI,CAAC/M,KAAK,CAAC,CACpCsI,KAAK,CAAG,IAAI,CAAC1L,OAAO,CAAC0L,KAAK,AACnC,CAKAR,OAAOoG,CAAU,CAAE,CACf,IAAMxR,EAAa,IAAI,CAACA,UAAU,CAAEE,EAAUgQ,EAAmB,CAAA,EAAM,IAAI,CAAChQ,OAAO,CAAEsR,GAAaC,EAAc,IAAI,CAACpR,OAAO,CAACoR,WAAW,CAAEC,EAAc,IAAI,CAAChH,WAAW,CACxK,IAAI,CAAClJ,OAAO,GAEZ0O,EAAmB,CAAA,EAAM,IAAI,CADL,IAAIwB,EAAY1R,EAAYE,EAAS,IAAI,CAACoD,KAAK,CAAE,IAAI,CAAC8M,QAAQ,GAEtF,IAAI,CAACvF,MAAM,CAAC4G,GACZ,IAAI,CAAC1R,MAAM,EACf,CACJ,CACAkQ,EAA0BhL,OAAO,CAACkL,GAML,IAAMwB,EAA8BxB,EAoJ3D,CAAEyB,eAAgBC,CAA+B,CAAE,CAd5B,CACzBD,eApDmB,CAInBE,MAAO,CACHC,QAAS,SACTC,WAAY,CACRvN,GAAI,QACJwN,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EAIAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CACR3T,EAAG,wBACH,eAAgB,CACpB,CACJ,EAAE,AACV,EAIA,gBAAiB,CACb0T,QAAS,SACTC,WAAY,CACRvN,GAAI,gBACJwN,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EACAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CAER3T,EAAG,yBACH,eAAgB,CACpB,CACJ,EAAE,AACV,CACJ,CAQA,EAeM,CAAEkB,SAAU+S,CAAyB,CAAEvM,QAASwM,CAAwB,CAAEC,OAAAA,CAAM,CAAEjI,MAAOkI,CAAsB,CAAEC,UAAAA,EAAS,CAAE,CAAIpT,IAMhIqT,GAAkBC,GAAmB,cACrCC,GAAoBD,GAAmB,gBAEvCE,GAAe,oBAAuB,CAAA,AAACxT,IAA+EyT,GAAG,CAAG,KAAS,IAAI,EAAK,IASpJ,SAASH,GAAmBI,CAAU,EAClC,OAAO,SAAUC,CAAK,EAClB,IAAI,CAAC3S,IAAI,CAAC0S,EAAY,QAAUC,EAAQ,IAC5C,CACJ,CAIA,SAASC,KACL,IAAI,CAAChT,OAAO,CAACiT,IAAI,CAAGV,EAAuBZ,EAAiC,IAAI,CAAC3R,OAAO,CAACiT,IAAI,EAAI,CAAC,EAYtG,CAIA,SAASC,GAAqB3O,CAAE,CAAE4O,CAAa,EAC3C,IAAMnT,EAAU,CAAE8R,WAAY,CAAEvN,GAAAA,CAAG,CAAE,EAC/BmM,EAAQ,CACV0C,OAAQD,EAAcE,KAAK,EAAI,OAC/BC,KAAMH,EAAcE,KAAK,EAAI,qBACjC,CACArT,CAAAA,EAAQmS,QAAQ,CAAIgB,EAAchB,QAAQ,EACtCgB,EAAchB,QAAQ,CAAC3B,GAAG,CAAC,SAAU+C,CAAK,EACtC,OAAOhB,EAAuB7B,EAAO6C,EACzC,GACJ,IAAMC,EAAMjB,EAAuB,CAAA,EAAM,CACrCT,WAAY,CACRG,YAAa,GACbC,aAAc,GACdF,KAAM,EACND,KAAM,EACN0B,OAAQ,MACZ,CACJ,EAAGN,EAAenT,GACZ0T,EAAS,IAAI,CAACrV,UAAU,CAACmV,GAE/B,OADAE,EAAOnP,EAAE,CAAGA,EACLmP,CACX,CAwBA,MAAMC,WAAyBlC,EAM3B,OAAO1M,QAAQE,CAAU,CAAE2O,CAAgB,CAAE,CACzC,IAAMC,EAAmBD,EAAiB9U,SAAS,AAC9C+U,CAAAA,EAAiBC,SAAS,GAC3B1B,EAA0BnN,EAAY,oBAAqB+N,IAC3Da,EAAiBC,SAAS,CAAGZ,GAErC,CAMA1I,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,MAChB,CAYAoO,KAAM,CACF,IAAMC,EAAU,IAAI,CAAChU,OAAO,CAAC7B,CAAC,CAC9B,GAAI6V,EACA,MAAO,AAAmB,YAAnB,OAAOA,EACVA,EAAQhV,IAAI,CAAC,IAAI,EACjBgV,EAER,IAAMlR,EAAS,IAAI,CAACA,MAAM,CAAE+J,EAAM/J,EAAOX,MAAM,CAAEhE,EAAI,EAAE,CACnD8V,EAAWpH,EAAKnB,EAAQ5I,CAAM,CAAC,EAAE,CAAEoR,EAAWD,GAAY,IAAI,CAACpG,MAAM,CAACnC,GAAO8C,gBAAgB,CAAE2F,EAAa,EAAG1H,EACnH,GAAIyH,EAEA,IADA/V,EAAE+B,IAAI,CAAC,CAAC,IAAKgU,EAASlR,CAAC,CAAEkR,EAASrM,CAAC,CAAC,EAC7B,EAAEsM,EAAatH,GAAOoH,GAEzBxH,EAAUf,AADVA,CAAAA,EAAQ5I,CAAM,CAACqR,EAAW,AAAD,EACT1H,OAAO,EAAI,IAC3ByH,EAAW,IAAI,CAACrG,MAAM,CAACnC,GAAO8C,gBAAgB,CAC1C/B,AAAY,MAAZA,EACAtO,EAAE+B,IAAI,CAAC,CAACuM,EAASyH,EAASlR,CAAC,CAAEkR,EAASrM,CAAC,CAAC,EAEnC4E,AAAY,MAAZA,EACLtO,EAAE+B,IAAI,CAAC,CAACuM,EAASyH,EAASlR,CAAC,CAAEkR,EAASrM,CAAC,CAAC,EAEvB,MAAZ4E,GACLtO,EAAE+B,IAAI,CAAC,CAACuM,EAAQ,EAEpBwH,EAAWvI,EAAMxI,MAAM,CAAC+H,OAAO,CAGvC,OAAQgJ,GAAY,IAAI,CAAC9T,OAAO,CAC5B,IAAI,CAACI,KAAK,CAACE,QAAQ,CAAC2T,SAAS,CAACjW,EAAG,IAAI,CAACgC,OAAO,CAACkU,WAAW,IACzD,IACR,CACArD,eAAgB,CACZ,OAAO,KAAK,CAACA,iBAAmB,CAAC,CAAC,IAAI,CAAChR,OAAO,CAAC7B,CAAC,AACpD,CACAwM,OAAO2J,CAAM,CAAE,CACX,IAAMtU,EAAU,IAAI,CAACA,OAAO,CAAE0Q,EAAQ,IAAI,CAACJ,gBAAgB,CAACtQ,EAC5D,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC8T,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBnU,IAAI,CAACsQ,GACL1P,GAAG,CAACsT,GACT,IAAI,CAAC3D,OAAO,CAAG,IAAI,CAAC7Q,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC8T,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBzD,QAAQ,CAAC,2BACT1Q,IAAI,CAAC,CACNU,OAAQ,CACZ,GACKE,GAAG,CAACsT,GACJ,IAAI,CAACxU,UAAU,CAACS,KAAK,CAACmH,UAAU,EACjC,IAAI,CAACiJ,OAAO,CAACvQ,IAAI,CAAC,CACd,kBAAmB,QACnBgT,OAAQR,GACRU,KAAMV,GACN,eAAgB,IAAI,CAACzS,OAAO,CAACkU,WAAW,GACpCrU,AAAe,EAAfA,EAAQwU,IAAI,AACpB,GAEJ,KAAK,CAAC7J,SACN2H,EAAO,IAAI,CAACnS,OAAO,CAAE,CAAEwS,kBAAAA,GAAmBF,gBAAAA,EAAgB,GAC1D,IAAI,CAACgC,UAAU,CAAC,IAAI,CACxB,CACA5U,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAMhC,EAAI,IAAI,CAAC4V,GAAG,GAAIW,EAASjK,EAAY,UAAY,OACnDtM,GACA,IAAI,CAACgC,OAAO,CAACuU,EAAO,CAAC,CAAEvW,EAAGA,CAAE,GAC5B,IAAI,CAACwS,OAAO,CAAC+D,EAAO,CAAC,CAAEvW,EAAGA,CAAE,KAG5B,IAAI,CAACgC,OAAO,CAACC,IAAI,CAAC,CAAEjC,EAAG,iBAAc,GACrC,IAAI,CAACwS,OAAO,CAACvQ,IAAI,CAAC,CAAEjC,EAAG,iBAAc,IAEzC,IAAI,CAACgC,OAAO,CAACwU,MAAM,CAAG,IAAI,CAAChE,OAAO,CAACgE,MAAM,CAAG,CAAC,CAACxW,CAClD,CACA,KAAK,CAAC0B,OAAO4K,EACjB,CAMAgK,WAAWG,CAAI,CAAE,CACb,IAAMC,EAAcD,EAAK5U,OAAO,CAAEO,EAAQqU,EAAKrU,KAAK,CAAE0S,EAAO1S,EAAMP,OAAO,CAACiT,IAAI,CAAEK,EAAOuB,EAAYvB,IAAI,CAAED,EAAQhB,EAAyBiB,IAASA,AAAS,SAATA,EAChJA,EACAuB,EAAYzB,MAAM,CAuBtB,CAAC,cAAe,YAAY,CACvBnS,OAAO,CAvBM,SAAU6R,CAAU,EAClC,IACIgC,EAAKC,EAAkBzW,EAAKoV,EAD1BsB,EAAWH,CAAW,CAAC/B,EAAW,CAExC,GAAIkC,EAAU,CACV,IAAK1W,KAAO2U,EAER,GAAI,AAAC+B,CAAAA,IAAcF,CAAAA,AADnBA,CAAAA,EAAM7B,CAAI,CAAC3U,EAAI,AAAD,EACSwT,UAAU,EAAIgD,EAAIhD,UAAU,CAACvN,EAAE,AAAD,GAGjDyQ,IAAaF,EAAIvQ,EAAE,AAAD,GAClBuQ,AAAgB,WAAhBA,EAAIjD,OAAO,CAAe,CAC1BkD,EAAmBD,EACnB,KACJ,CAEAC,IACArB,EAASkB,CAAI,CAAC9B,EAAW,CAAGvS,EAAME,QAAQ,CACrCqT,SAAS,CAAC,AAACe,CAAAA,EAAYtQ,EAAE,EAAIiO,IAAU,EAAK,IAAMwC,EAAUzC,EAAuBwC,EAAkB,CAAE1B,MAAOA,CAAM,IACzHuB,EAAKxU,IAAI,CAAC0S,EAAYY,EAAOuB,YAAY,CAAC,OAElD,CACJ,EAGJ,CACJ,CAYAtB,GAAiBlD,QAAQ,CAAG,CACxByE,UAAW,YACXb,YAAa,eACbjB,OAAQ,SACRE,KAAM,OACNxS,OAAQ,QACZ,EAkBA,GAAM,CAAEuJ,MAAO8K,EAAsB,CAAE,CAAI/V,GAwB3C,OAAMgW,WAAyB3D,EAM3BjH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,OACZ,IAAI,CAAC+D,SAAS,CAAG,KAAK,CAACuH,cAC3B,CAMAtG,OAAO2J,CAAM,CAAE,CACX,IAAM5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACtQ,OAAO,CAChD,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC4U,IAAI,CAAC,EAAG,KAAM,EAAG,GACjBjV,IAAI,CAACsQ,GACL1P,GAAG,CAACsT,GACT,KAAK,CAAC3J,QACV,CACA9K,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAM+T,EAAW,IAAI,CAACrG,MAAM,CAAC,IAAI,CAAC/K,MAAM,CAAC,EAAE,EAAE0L,gBAAgB,CACzD0F,EACA,IAAI,CAAC/T,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCzH,EAAGkR,EAASlR,CAAC,CACb6E,EAAGqM,EAASrM,CAAC,CACbgD,MAAO,IAAI,CAAC7K,OAAO,CAAC6K,KAAK,CACzBC,OAAQ,IAAI,CAAC9K,OAAO,CAAC8K,MAAM,AAC/B,GAGA,IAAI,CAAC1K,IAAI,CAAC,CACN4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACwU,MAAM,CAAGW,CAAAA,CAAQpB,CAClC,CACA,KAAK,CAACrU,OAAO4K,EACjB,CACJ,CAWA2K,GAAiB3E,QAAQ,CAAG0E,GAAuBI,AAjGkB5B,GAiGalD,QAAQ,CAAE,CACxF5F,MAAO,QACPC,OAAQ,QACZ,GAkBA,GAAM,CAAET,MAAOmL,EAAwB,CAAE,CAAIpW,GAmB7C,OAAMqW,WAA2BhE,EAM7BjH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,SACZ,IAAI,CAAC+D,SAAS,CAAG,KAAK,CAACuH,cAC3B,CASApR,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAM+T,EAAW,IAAI,CAACrG,MAAM,CAAC,IAAI,CAAC/K,MAAM,CAAC,EAAE,EAAE0L,gBAAgB,CACzD0F,EACA,IAAI,CAAC/T,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCzH,EAAGkR,EAASlR,CAAC,CACb6E,EAAGqM,EAASrM,CAAC,CACb6N,EAAG,IAAI,CAAC1V,OAAO,CAAC0V,CAAC,AACrB,GAGA,IAAI,CAACvV,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACwU,MAAM,CAAG,CAAC,CAACT,CAC5B,CACA,KAAK,CAACrU,OAAOb,IAAI,CAAC,IAAI,CAAEyL,EAC5B,CAIAE,OAAO2J,CAAM,CAAE,CACX,IAAM5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACtQ,OAAO,CAChD,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCkV,MAAM,CAAC,EAAG,KAAM,GAChBvV,IAAI,CAACsQ,GACL1P,GAAG,CAACsT,GACT,KAAK,CAAC3J,QACV,CAOAiL,UAAUF,CAAC,CAAE,CACT,IAAI,CAAC1V,OAAO,CAAC0V,CAAC,CAAGA,CACrB,CACJ,CAaAD,GAAmBhF,QAAQ,CAAG+E,GAAyBD,AAtNc5B,GAsNiBlD,QAAQ,CAAE,CAAEiF,EAAG,GAAI,GAoBzG,GAAM,CAAErL,MAAOwL,EAAyB,CAAEhQ,QAASiQ,EAA2B,CAAE,CAAI1W,GAmBpF,OAAM2W,WAA4BtE,EAM9BjH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,SAChB,CASAyK,KAAKtQ,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACzB0S,GAA4B9V,EAAQ2L,KAAK,GACzC3L,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACpBA,EAAMC,KAAK,CAAG3L,EAAQ2L,KAAK,AAC/B,GAEAmK,GAA4B9V,EAAQmD,KAAK,GACzCnD,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACpBA,EAAMvI,KAAK,CAAGnD,EAAQmD,KAAK,AAC/B,GAEJ,KAAK,CAACiN,KAAKtQ,EAAYE,EAASoD,EACpC,CAOAuH,OAAO2J,CAAM,CAAE,CACX,IAAI,CAACnU,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CAACuV,aAAa,CAAC,WACvD5V,IAAI,CAAC,IAAI,CAACkQ,gBAAgB,CAAC,IAAI,CAACtQ,OAAO,GACvCgB,GAAG,CAACsT,GACT,KAAK,CAAC3J,QACV,CAKAjB,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,KAAK,CAACuI,eAAerI,EAAIF,EAAI,CAAA,EACjC,CAaAuN,oBAAoBC,CAAM,CAAEC,CAAM,CAAEC,CAAE,CAAEC,CAAE,CAAE,CACxC,OAAO3S,KAAK4S,GAAG,CAAC,AAACH,CAAAA,EAAOtO,CAAC,CAAGqO,EAAOrO,CAAC,AAADA,EAAKuO,EAAK,AAACD,CAAAA,EAAOnT,CAAC,CAAGkT,EAAOlT,CAAC,AAADA,EAAKqT,EACjEF,EAAOnT,CAAC,CAAGkT,EAAOrO,CAAC,CAAGsO,EAAOtO,CAAC,CAAGqO,EAAOlT,CAAC,EAAIU,KAAK6S,IAAI,CAAC,AAACJ,CAAAA,EAAOtO,CAAC,CAAGqO,EAAOrO,CAAC,AAADA,EAAMsO,CAAAA,EAAOtO,CAAC,CAAGqO,EAAOrO,CAAC,AAADA,EAClG,AAACsO,CAAAA,EAAOnT,CAAC,CAAGkT,EAAOlT,CAAC,AAADA,EAAMmT,CAAAA,EAAOnT,CAAC,CAAGkT,EAAOlT,CAAC,AAADA,EACnD,CAUAwT,SAAStC,CAAQ,CAAEuC,CAAS,CAAE,CAC1B,IAAMC,EAAKxC,EAASlR,CAAC,CAAE2T,EAAKzC,EAASrM,CAAC,CAAE+O,EAAKH,EAAUzT,CAAC,CAAE6T,EAAKJ,EAAU5O,CAAC,CAAEO,EAAK,AAACsO,CAAAA,EAAKE,CAAC,EAAK,EAAuBE,EAAKpT,KAAK6S,IAAI,CAAC,AAACG,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,EAAI,AAACD,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,GACnLE,EAAQrT,AAAiB,IAAjBA,KAAKsT,IAAI,CAD4K,AAACH,CAAAA,EAAKF,CAAC,EAAMC,CAAAA,EAAKF,CAAC,GACjLhT,KAAKuT,EAAE,CAK1C,OAJI7O,EAAKsO,GACLK,CAAAA,GAAS,GAAE,EAGR,CAAE3O,GAAAA,EAAIC,GANwF,AAACsO,CAAAA,EAAKE,CAAC,EAAK,EAMhGC,GAAAA,EAAII,GADV,IAAI,CAACC,KAAK,GACIJ,MAAAA,CAAM,CACnC,CAKAI,OAAQ,CACJ,IAAMxL,EAAQ,IAAI,CAACyL,QAAQ,GAC3B,OAAOtB,GAA4BnK,GAC/BjI,KAAK4S,GAAG,CAAC3K,EAAMmB,QAAQ,CAAC,IAAI,CAAC9M,OAAO,CAACkX,EAAE,EAAIvL,EAAMmB,QAAQ,CAAC,IAC1D,IAAI,CAAC9M,OAAO,CAACkX,EAAE,AACvB,CAKAE,UAAW,CACP,IAAMC,EAAa,IAAI,CAACrX,OAAO,CAAC2L,KAAK,CACrC,OAAO,IAAI,CAACpL,KAAK,CAACoL,KAAK,CAAC0L,EAAW,AACvC,CAOAC,oBAAoB5L,CAAK,CAAE,CACvB,OAAO,IAAI,CAACmC,MAAM,CAACnC,GAAO8C,gBAAgB,AAC9C,CAOA3O,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAM+T,EAAW,IAAI,CAACoD,mBAAmB,CAAC,IAAI,CAACxU,MAAM,CAAC,EAAE,EAAG2T,EAAY,IAAI,CAACa,mBAAmB,CAAC,IAAI,CAACxU,MAAM,CAAC,EAAE,EAAG4N,EAAQ,IAAI,CAAC8F,QAAQ,CAACtC,EAAUuC,GAC7IvC,EACA,IAAI,CAAC/T,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCrC,GAAIsI,EAAMtI,EAAE,CACZC,GAAIqI,EAAMrI,EAAE,CACZyO,GAAIpG,EAAMoG,EAAE,CACZI,GAAIxG,EAAMwG,EAAE,CACZK,SAAU7G,EAAMqG,KAAK,CACrBS,gBAAiB9G,EAAMtI,EAAE,CACzBqP,gBAAiB/G,EAAMrI,EAAE,AAC7B,GAGA,IAAI,CAAClI,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACwU,MAAM,CAAGW,CAAAA,CAAQpB,CAClC,CACA,KAAK,CAACrU,OAAO4K,EACjB,CAOAiN,WAAWR,CAAE,CAAE,CACX,IAAMvN,EAAS,IAAI,CAAC7J,UAAU,CAACF,WAAW,CAAC+J,MAAM,AACjD,CAAA,IAAI,CAAC3J,OAAO,CAACkX,EAAE,CAAGA,EACdvN,GAAUA,CAAM,CAAC,EAAE,GACnBA,CAAM,CAAC,EAAE,CAACuN,EAAE,CAAGA,EACfvN,CAAM,CAAC,EAAE,CAACuN,EAAE,CAAGA,EAEvB,CACJ,CAaAnB,GAAoBtF,QAAQ,CAAGoF,GAA0BN,AA3aY5B,GA2amBlD,QAAQ,CAAE,CAC9FyG,GAAI,IACR,GASA,IAAIS,GAAmH/Z,EAAoB,KACvIga,GAAuIha,EAAoBI,CAAC,CAAC2Z,IAUjK,GAAM,CAAEE,OAAAA,EAAM,CAAE,CAAID,KAGd,CAAEtF,OAAQwF,EAAwB,CAAEC,eAAAA,EAAc,CAAEC,SAAAA,EAAQ,CAAEvY,KAAMwY,EAAsB,CAAE,CAAI7Y,IAUtG,SAAS8Y,GAAgBlV,CAAC,CAAE6E,CAAC,CAAEsQ,CAAC,CAAEC,CAAC,CAAEpY,CAAO,EACxC,IAAMqY,EAAUrY,GAAWA,EAAQqY,OAAO,CAAEC,EAAUtY,GAAWA,EAAQsY,OAAO,CAC5E/D,EAAMgE,EAASC,EAAUL,EAAI,EA4BjC,OA3BIH,GAASK,IAAYL,GAASM,KAC9B/D,EAAO,CAAC,CAAC,IAAK8D,EAASC,EAAQ,CAAC,CAEhCC,CAAAA,EAAU1Q,EAAIyQ,CAAM,EACN,GACVC,CAAAA,EAAU,CAACH,EAAIG,CAAM,EAErBA,EAAUJ,GACVK,CAAAA,EAAUH,EAAUrV,EAAKmV,EAAI,EAAKI,EAAUJ,EAAII,CAAM,EAGtDD,EAAUzQ,EAAIuQ,EACd7D,EAAKrU,IAAI,CAAC,CAAC,IAAK8C,EAAIwV,EAAS3Q,EAAIuQ,EAAE,EAG9BE,EAAUzQ,EACf0M,EAAKrU,IAAI,CAAC,CAAC,IAAK8C,EAAIwV,EAAS3Q,EAAE,EAG1BwQ,EAAUrV,EACfuR,EAAKrU,IAAI,CAAC,CAAC,IAAK8C,EAAG6E,EAAIuQ,EAAI,EAAE,EAGxBC,EAAUrV,EAAImV,GACnB5D,EAAKrU,IAAI,CAAC,CAAC,IAAK8C,EAAImV,EAAGtQ,EAAIuQ,EAAI,EAAE,GAGlC7D,GAAQ,EAAE,AACrB,CAsBA,MAAMkE,WAA0BhH,EAkB5B,OAAOiH,gBAAgBC,CAAY,CAAEvK,CAAG,CAAE,CACtC,MAAO,CACHpL,EAAGU,KAAKkV,KAAK,CAAC,AAACxK,CAAAA,EAAIpL,CAAC,EAAI,CAAA,EAAM2V,CAAAA,EAAa3V,CAAC,EAAI,CAAA,EAC5C,AAACoL,CAAAA,EAAIvD,KAAK,CAAI8N,CAAAA,EAAa9N,KAAK,EAAI,CAAA,CAAC,EACjCkN,GAAeY,EAAaE,KAAK,GACzChR,EAAGnE,KAAKkV,KAAK,CAAC,AAACxK,CAAAA,EAAIvG,CAAC,EAAI,CAAA,EAAM8Q,CAAAA,EAAa9Q,CAAC,EAAI,CAAA,EAC5C,AAACuG,CAAAA,EAAItD,MAAM,CAAI6N,CAAAA,EAAa7N,MAAM,EAAI,CAAA,CAAC,EACnCiN,GAAeY,EAAaG,aAAa,EACrD,CACJ,CACA,OAAO/T,QAAQ6O,CAAgB,CAAE,CAE7BmF,AADgBnF,EAAiB9U,SAAS,CAACia,OAAO,CAC1CC,SAAS,CAAGd,EACxB,CAOA,OAAOe,iBAAiB1Y,CAAK,CAAEoC,CAAK,CAAEgW,CAAY,CAAEO,CAAS,CAAE,CAC3D,IAYIC,EAZEN,EAAQF,EAAaE,KAAK,CAAEC,EAAgBH,EAAaG,aAAa,CAAEM,EAAUzW,EAAMyL,GAAG,CAAG,EAAKzL,EAAMyW,OAAO,EAAI,EAAIC,EAAO1W,EAAM2W,OAAO,GAElJtZ,EAAU,CACN6Y,MAAOA,EACPC,cAAeA,EACf9V,EAAG2V,EAAa3V,CAAC,CACjB6E,EAAG8Q,EAAa9Q,CAAC,CACjBgD,MAAOlI,EAAMkI,KAAK,CAClBC,OAAQnI,EAAMmI,MAAM,AACxB,EAEA9H,EAAI,AAACkW,CAAAA,EAAUlW,CAAC,EAAI,CAAA,EAAKzC,EAAM+I,QAAQ,CAAEzB,EAAI,AAACqR,CAAAA,EAAUrR,CAAC,EAAI,CAAA,EAAKtH,EAAMgJ,OAAO,CA0C/E,MAvCA4P,CAAAA,EAAMnW,EAAIoW,CAAM,EACN,IACFP,AAAU,UAAVA,EACA7Y,EAAQ6Y,KAAK,CAAG,OAGhB7Y,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKmW,GAIvCA,CAAAA,EAAMnW,EAAIqW,EAAKxO,KAAK,CAAGuO,CAAM,EACnB7Y,EAAM0L,SAAS,GACjB4M,AAAU,SAAVA,EACA7Y,EAAQ6Y,KAAK,CAAG,QAGhB7Y,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKzC,EAAM0L,SAAS,CAAGkN,GAIzDA,CAAAA,EAAMtR,EAAIuR,CAAM,EACN,IACFN,AAAkB,WAAlBA,EACA9Y,EAAQ8Y,aAAa,CAAG,MAGxB9Y,EAAQ6H,CAAC,CAAG,AAAC7H,CAAAA,EAAQ6H,CAAC,EAAI,CAAA,EAAKsR,GAIvCA,CAAAA,EAAMtR,EAAIwR,EAAKvO,MAAM,CAAGsO,CAAM,EACpB7Y,EAAM2L,UAAU,GAClB4M,AAAkB,QAAlBA,EACA9Y,EAAQ8Y,aAAa,CAAG,SAGxB9Y,EAAQ6H,CAAC,CAAG,AAAC7H,CAAAA,EAAQ6H,CAAC,EAAI,CAAA,EAAKtH,EAAM2L,UAAU,CAAGiN,GAGnDnZ,CACX,CAMAwK,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,QACtC,CAaAwM,eAAehH,CAAE,CAAEF,CAAE,CAAE,CACnB,KAAK,CAACkH,eAAehH,EAAIF,EAAI,EACjC,CAOAgB,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,IAAMnI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAEnCiC,EAAe,IAAI,CAAC1C,UAAU,CAACF,WAAW,CAE1CwR,EAAkB7Q,EAAMN,WAAW,CAACqH,OAAO,CAAC,IAAI,CAACxH,UAAU,EAAiDuR,EAAekI,AAA1ChZ,EAAMP,OAAO,CAACC,WAAW,AAAiC,CAACmR,EAAgB,CAC5J,GAAI7Q,EAAMwI,QAAQ,CAAE,CAChB,IAAMD,EAAOF,EACbA,EAAKF,EACLA,EAAKI,CACT,CAEA,IAAI,CAAC9I,OAAO,CAACgD,CAAC,EAAI4F,EAClB,IAAI,CAAC5I,OAAO,CAAC6H,CAAC,EAAIa,EAElB2I,CAAY,CAAC,IAAI,CAAClB,UAAU,CAAC,CAAC,IAAI,CAAC/M,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DqO,CAAY,CAAC,IAAI,CAAClB,UAAU,CAAC,CAAC,IAAI,CAAC/M,KAAK,CAAC,CAACyE,CAAC,CAAG,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,CAC5DrF,CAAY,CAAC,IAAI,CAAC2N,UAAU,CAAC,CAAC,IAAI,CAAC/M,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DR,CAAY,CAAC,IAAI,CAAC2N,UAAU,CAAC,CAAC,IAAI,CAAC/M,KAAK,CAAC,CAACyE,CAAC,CAAG,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,AAChE,CACA8C,OAAO2J,CAAM,CAAE,CACX,IAAMtU,EAAU,IAAI,CAACA,OAAO,CAAE0Q,EAAQ,IAAI,CAACJ,gBAAgB,CAACtQ,GAAU+K,EAAQ/K,EAAQ+K,KAAK,AAC3F,CAAA,IAAI,CAAC5K,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCkC,KAAK,CAAC,GAAI,EAAG,MAClB3C,EAAQ4J,KAAK,CAAE,KAAM,KAAM5J,EAAQ6G,OAAO,CAAE,KAAM,oBAC7CzG,IAAI,CAACsQ,GACL1P,GAAG,CAACsT,GACJ,IAAI,CAACxU,UAAU,CAACS,KAAK,CAACmH,UAAU,GACb,aAAhBqD,EAAMsI,KAAK,EACXtI,CAAAA,EAAMsI,KAAK,CAAG,IAAI,CAACvT,UAAU,CAACS,KAAK,CAACE,QAAQ,CAAC+Y,WAAW,CAACf,GAAkBgB,uBAAuB,CAACnS,OAAO,CAACtH,EAAQ4J,KAAK,EAAI,GAAK,UAAY5J,EAAQ0Z,eAAe,CAAA,EAExK,IAAI,CAACvZ,OAAO,CACP4H,GAAG,CAAC/H,EAAQ+K,KAAK,EACjB4O,MAAM,CAAC3Z,EAAQ2Z,MAAM,GAE9B,IAAI,CAACxZ,OAAO,CAACyZ,SAAS,CAAG5Z,EAAQ4Z,SAAS,CAC1C,KAAK,CAACjP,QACV,CACA9K,OAAO4K,CAAS,CAAE,CACd,IAAMzK,EAAU,IAAI,CAACA,OAAO,CAAE4C,EAAO,IAAI,CAACA,IAAI,EAAI5C,EAAQ6X,MAAM,EAAI7X,EAAQ4C,IAAI,CAAED,EAAQ,IAAI,CAACxC,OAAO,CAAEuL,EAAQ,IAAI,CAAC5I,MAAM,CAAC,EAAE,CAC9H,GAAI,CAACH,EAAO,CACR,IAAI,CAAC9C,MAAM,CAAC4K,GACZ,MACJ,CACA9H,EAAMvC,IAAI,CAAC,CACPwC,KAAMA,EACFiV,GAAOgC,OAAOjX,GAAO8I,EAAO,IAAI,CAAC5L,UAAU,CAACS,KAAK,EACjDP,EAAQ8Z,SAAS,CAAC9a,IAAI,CAAC0M,EAAO,IAAI,CAC1C,GACA,IAAMmC,EAAS,IAAI,CAACA,MAAM,CAACnC,GACrBgF,EAAQ,IAAI,CAACwD,QAAQ,CAACrG,GACxB6C,GACA/N,EAAMuW,SAAS,CAAGxI,EAClBA,EAAM2H,OAAO,CAAGxK,EAAOW,gBAAgB,CAACxL,CAAC,CACzC0N,EAAM4H,OAAO,CAAGzK,EAAOW,gBAAgB,CAAC3G,CAAC,CACzClF,CAAK,CAAC8H,EAAY,UAAY,OAAO,CAACiG,IAGtC/N,EAAMvC,IAAI,CAAC,CACP4C,EAAG,EACH6E,EAAG,KACP,GAEJlF,EAAMgS,MAAM,CAAG,CAAC,CAACjE,EACjB,KAAK,CAAC7Q,OAAO4K,EACjB,CAMAoD,OAEA4B,CAAM,CAAE,CACJ,IAAM5B,EAAS,KAAK,CAACA,OAAOjJ,KAAK,CAAC,IAAI,CAAEE,WAAY9B,EAAI,IAAI,CAAChD,OAAO,CAACgD,CAAC,EAAI,EAAG6E,EAAI,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,EAAI,EAKnG,OAJAgG,EAAOW,gBAAgB,CAACxL,CAAC,EAAIA,EAC7B6K,EAAOW,gBAAgB,CAAC3G,CAAC,EAAIA,EAC7BgG,EAAOU,gBAAgB,CAACvL,CAAC,EAAIA,EAC7B6K,EAAOU,gBAAgB,CAAC1G,CAAC,EAAIA,EACtBgG,CACX,CAIAqG,SAASrG,CAAM,CAAE,CACb,IAAM+G,EAAO,IAAI,CAACzU,OAAO,CAAEI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAAE8N,EAAU9N,EAAM8N,OAAO,CAAE3C,EAAQ,IAAI,CAAC5I,MAAM,CAAC,EAAE,CAAE+R,EAAc,IAAI,CAAC7U,OAAO,CAAE+Z,EAAyBlM,EAAOW,gBAAgB,CAAEwL,EAAyBnM,EAAOU,gBAAgB,CACrO0L,EAAcC,EAASC,EAAkBC,EAAkBC,EAAW3O,EAAMxI,MAAM,CAAC+H,OAAO,EAC1F0E,AA1qDgDnE,EA0qD1B1M,SAAS,CAACuK,YAAY,CAACrK,IAAI,CAAC0M,GACtD,GAAIkJ,GAAQyF,EAAU,CAClB,GAAM,CAAExP,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAG8J,CAC9BC,CAAAA,EAAYyF,QAAQ,EAAIjM,EACxB4L,EAAe5L,EAAQkM,WAAW,CAACvb,IAAI,CAAC,CACpCuB,MAAAA,EACA+Z,SAAUrC,GAAuBpD,EAAYyF,QAAQ,CAAE,IACvDE,gBAAiBnM,EAAQmM,eAAe,CACxCtT,QAASmH,EAAQnH,OAAO,AAC5B,EAAG2D,EAAOC,EAAQ,CACdgB,MAAOkO,EAAuBhX,CAAC,CAC/B+I,MAAOiO,EAAuBnS,CAAC,CAC/B4S,SAAU/O,EAAM+O,QAAQ,CACxBC,QAAShP,EAAMgP,OAAO,CACtBtC,EAAI4B,EAAuBlP,MAAM,EAC7BkP,EAAuBnP,KAAK,AACpC,GAEKgK,EAAYnK,UAAU,CAC3BuP,EAAepF,EAAYnK,UAAU,CAAC1L,IAAI,CAAC,IAAI,GAG/Ckb,EAAU,CACNlX,EAAG+W,EAAuB/W,CAAC,CAC3B6E,EAAGkS,EAAuBlS,CAAC,CAC3BgD,MAAO,EACPC,OAAQ,CACZ,EACAmP,EAAexB,GAAkBC,eAAe,CAACZ,GAAyBjD,EAAa,CACnFhK,MAAAA,EACAC,OAAAA,CACJ,GAAIoP,GAC0B,YAA1B,IAAI,CAACla,OAAO,CAAC2a,QAAQ,EACrBV,CAAAA,EAAexB,GAAkBC,eAAe,CAACD,GAAkBQ,gBAAgB,CAAC1Y,EAAOqU,EAAMC,EAAaoF,GAAeC,EAAO,GAGxIrF,EAAY+F,IAAI,GAChBT,EAAmBF,EAAajX,CAAC,CAAGzC,EAAM+I,QAAQ,CAClD8Q,EAAmBH,EAAapS,CAAC,CAAGtH,EAAMgJ,OAAO,CACjD8Q,EACI9Z,EAAM8I,YAAY,CAAC8Q,EAAkBC,IACjC7Z,EAAM8I,YAAY,CAAC8Q,EAAmBtP,EAAOuP,EAAmBtP,GAEhF,CACA,OAAOuP,EAAWJ,EAAe,IACrC,CACJ,CAWAxB,GAAkBhI,QAAQ,CAAG,CACzBiJ,gBAAiB,OACjBmB,YAAa,SACbC,YAAa,eACbha,OAAQ,SACRia,aAAc,IACd3B,QAAS,SACb,EAOAX,GAAkBgB,uBAAuB,CAAG,CAAC,YAAY,AAwCzD,OAAMuB,WAA0BvJ,EAM5BjH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,QACZ,IAAI,CAAC+D,SAAS,CAAG,KAAK,CAACuH,cAC3B,CACAtG,OAAO2J,CAAM,CAAE,CACX,IAAM5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACtQ,OAAO,EAAGA,EAAU,IAAI,CAACA,OAAO,AACzE,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCwa,KAAK,CAACjb,EAAQkb,GAAG,CAAE,EAAG,KAAMlb,EAAQ6K,KAAK,CAAE7K,EAAQ8K,MAAM,EACzD1K,IAAI,CAACsQ,GACL1P,GAAG,CAACsT,GACT,IAAI,CAACnU,OAAO,CAAC0K,KAAK,CAAG7K,EAAQ6K,KAAK,CAClC,IAAI,CAAC1K,OAAO,CAAC2K,MAAM,CAAG9K,EAAQ8K,MAAM,CACpC,KAAK,CAACH,QACV,CACA9K,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAM0N,EAAS,IAAI,CAACA,MAAM,CAAC,IAAI,CAAC/K,MAAM,CAAC,EAAE,EAAGoR,EAAWiH,AA9DG1C,GA8D6B3Z,SAAS,CAACoV,QAAQ,CAAClV,IAAI,CAAC,IAAI,CAAE6O,GACjHqG,EACA,IAAI,CAAC/T,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCzH,EAAGkR,EAASlR,CAAC,CACb6E,EAAGqM,EAASrM,CAAC,AACjB,GAGA,IAAI,CAAC1H,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACwU,MAAM,CAAGW,CAAAA,CAAQpB,CAClC,CACA,KAAK,CAACrU,OAAO4K,EACjB,CACJ,CAYAuQ,GAAkBvK,QAAQ,CAAG,CACzB5F,MAAO,QACPC,OAAQ,SACRhK,OAAQ,QACZ,EASA,IAAIsa,GAAuFxd,EAAoB,KAC3Gyd,GAA2Gzd,EAAoBI,CAAC,CAACod,IAmBrI,GAAM,CAAE/b,SAAUic,EAAiB,CAAEtF,cAAAA,EAAa,CAAE,CAAI5W,IAyGrBmc,GAnGnC,MAMI/Q,YAAYgR,CAAS,CAAEC,CAAQ,CAAE,CAC7B,IAAI,CAACA,QAAQ,CAAGA,EAChB,IAAI,CAACC,SAAS,CAAG,IAAI,CAACC,oBAAoB,CAACH,GAC3C,IAAI,CAACI,WAAW,CAAG,IAAI,CAACC,cAAc,EAC1C,CAkBAF,qBAAqBH,CAAS,CAAE3K,EAAY,wCAAwC,CAAE,CAClF,OAAOmF,GAAc,MAAO,CAAEnF,UAAAA,CAAU,EAAG,KAAK,EAAG2K,EACvD,CAUAK,eAAehL,EAAY,wBAAwB,CAAE,CACjD,IAAMiL,EAAQ,IAAI,CAAEL,EAAW,IAAI,CAACA,QAAQ,CAEtCG,EAAc5F,GAAc,SAAU,CAAEnF,UAAAA,CAAU,EAAG,KAAK,EAAG,IAAI,CAAC6K,SAAS,EAajF,OAZAE,EAAY7Q,KAAK,CAAC,mBAAmB,CAAG,OACnC0Q,CAAAA,EAASM,KAAK,CAAC,0BACZN,EAAWA,EAAW,WAAU,EAAK,IAC7C,CAAC,QAAS,aAAa,CAACxa,OAAO,CAAC,AAAC+a,IAC7BV,GAAkBM,EAAaI,EAAWF,EAAMG,iBAAiB,CAACC,IAAI,CAACJ,GAC3E,GAEAR,GAAkBa,SAAU,UAAW,SAAU5a,CAAK,EAC/B,WAAfA,EAAM6a,IAAI,EACVN,EAAMG,iBAAiB,EAE/B,GACOL,CACX,CAKAK,mBAAoB,CAChB,IAAI,CAACI,UAAU,EACnB,CAOAC,UAAUC,EAAe,+BAA+B,CAAE,CACtD,IAAMC,EAAW,IAAI,CAACd,SAAS,CAAEe,EAAmB,IAAI,CAACb,WAAW,AACpE,CAAA,IAAI,CAACjW,IAAI,CAAG,KAAK,EAEjB6W,EAASE,SAAS,CAAG,AAACrB,KAA+FsB,SAAS,CAE1HH,EAAS3L,SAAS,CAACvJ,OAAO,CAACiV,IAAiB,IAC5CC,EAASI,SAAS,CAACC,MAAM,CAACN,GAE1BC,EAASM,eAAe,CAAC,UAG7BN,EAASO,WAAW,CAACN,GACrBD,EAASzR,KAAK,CAACiS,OAAO,CAAG,QACzBR,EAASzR,KAAK,CAACD,MAAM,CAAG,EAC5B,CAIAuR,YAAa,CACT,IAAI,CAACX,SAAS,CAAC3Q,KAAK,CAACiS,OAAO,CAAG,MACnC,CACJ,EAsBM,CAAElX,IAAKmX,EAAoB,CAAEC,UAAAA,EAAS,CAAE,CAAI9d,IAE5C,CAAE4W,cAAemH,EAA8B,CAAEC,QAAAA,EAAO,CAAEpO,SAAAA,EAAQ,CAAE9I,WAAYmX,EAA2B,CAAE5d,KAAM6d,EAAqB,CAAEC,WAAAA,EAAU,CAAE,CAAIne,IAiGhK,SAASoe,GAAchC,CAAS,CAAEjb,CAAK,CAAEkd,CAAU,CAAEzd,CAAO,CAAE0d,CAAO,CAAEC,CAAM,MAKrEC,EAAgBC,EAJpB,GAAI,CAACtd,EACD,OAEJ,IAAMud,EAAW,IAAI,CAACA,QAAQ,CAAE9b,EAAO,IAAI,CAACA,IAAI,CAEhDqb,GAA4Brd,EAAS,CAAC+S,EAAOgL,KAEzCH,EAAiBH,AAAe,KAAfA,EAAoBA,EAAa,IAAMM,EAASA,EAC7D/O,GAAS+D,KAGT,CAACqK,GAAQrK,IAEJqK,GAAQrK,IAAU/D,GAAS+D,CAAK,CAAC,EAAE,GAE/B8K,AADLA,CAAAA,EAAY7b,CAAI,CAAC+b,EAAO,EAAIA,CAAK,EAClBhC,KAAK,CAAC,QACjB2B,EAAQxd,IAAI,CAAC,CACT,CAAA,EACA2d,EACArC,EACH,EAELgC,GAAcxe,IAAI,CAAC,IAAI,CAAEwc,EAAWjb,EAAOqd,EAAgB7K,EAAO2K,EAAS,CAAA,IAG3EA,EAAQxd,IAAI,CAAC,CACT,IAAI,CACJ0d,EACA,aACApC,EACAzI,EACH,EAGb,GACI4K,IACAJ,GAAWG,EAAS,AAACtf,GAAOA,CAAC,CAAC,EAAE,CAAC2d,KAAK,CAAC,WAAa,GAAK,GACrDmB,IACAQ,EAAQM,OAAO,GAEnBN,EAAQzc,OAAO,CAAC,AAACgd,IACTA,AAAgB,CAAA,IAAhBA,CAAQ,CAAC,EAAE,CACXd,GAA+B,OAAQ,CACnCtM,UAAW,6BACf,EAAG,KAAK,EAAGoN,CAAQ,CAAC,EAAE,EAAElB,WAAW,CAACE,GAAqBiB,cAAc,CAACD,CAAQ,CAAC,EAAE,IAGnFA,CAAQ,CAAC,EAAE,CAAG,CACVlL,MAAOkL,CAAQ,CAAC,EAAE,CAAC,EAAE,CACrBtY,KAAMsY,CAAQ,CAAC,EAAE,CAAC,EAAE,AACxB,EACAH,EAASlZ,KAAK,CAACqZ,CAAQ,CAAC,EAAE,CAAEA,EAASE,MAAM,CAAC,IAEpD,GAER,CA2BA,GAAM,CAAErY,IAAKsY,EAAmB,CAAE,CAAIhf,IAEhC,CAAEif,YAAAA,EAAW,CAAE,CAAIjT,IAEnB,CAAE/L,SAAUif,EAAwB,CAAEtI,cAAeuI,EAA6B,CAAE1Y,QAAS2Y,EAAuB,CAAEpB,QAASqB,EAAuB,CAAEzP,SAAU0P,EAAwB,CAAExY,WAAYyY,EAA0B,CAAEpB,WAAYqB,EAA0B,CAAE,CAAIxf,KAWtR,AAAC,SAAU5B,CAAkB,EACzBA,CAAkB,CAACA,CAAkB,CAAC,mBAAmB,CAAG,EAAE,CAAG,mBACjEA,CAAkB,CAACA,CAAkB,CAAC,iBAAiB,CAAG,EAAE,CAAG,gBACnE,EAAGA,GAAuBA,CAAAA,EAAqB,CAAC,CAAA,GAKhD,IAAMqhB,GAAqB,CACvB,wBAAyB,CAAC,WAAY,YAAa,YAAY,CAC/D,yBAA0B,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAM,AACnE,EAcA,SAASC,GAAiBpD,CAAS,EAE/B,IAAMqD,EAASR,GAA8B,MAAO,CAChD1N,UAAW,0BACf,EAAG,KAAK,EAAG6K,GAELsD,EAAST,GAA8B,MAAO,CAChD1N,UAAW,0BACf,EAAG,KAAK,EAAG6K,GAKX,OAHA6C,GAA8B,MAAO,CACjC1N,UAAW,kCACf,EAAG,KAAK,EAAGmO,GACJ,CACHD,OAAQA,EACRC,OAAQA,CACZ,CACJ,CAgDA,SAASC,GAA8B1e,CAAK,CAAE2C,CAAM,CAAEgc,CAAU,CAAEC,CAAa,EAC3E,IAAMC,EAASlc,EAAOmc,MAAM,EAAInc,EAAOlD,OAAO,CAACqf,MAAM,AAErDF,CAAAA,EAAczC,SAAS,CAAG,AAACrB,KAA+FsB,SAAS,CAEnI4B,GAA8B,KAAM,CAChC1N,UAAW,4BACf,EAAG,KAAK,EAAGsO,GAAepC,WAAW,CAACqB,GAAoBF,cAAc,CAACoB,GAAYpc,EAAQgc,GAAYK,iBAAiB,GAE1HhB,GAA8B,QAAS,CACnC5Y,KAAM,SACN6Z,KAAM,mBAAqBN,EAC3BnM,MAAOmM,CACX,EAAG,KAAK,EAAGC,GAEXM,GAAczgB,IAAI,CAAC,IAAI,CAAEkgB,EAAY,SAAU3e,EAAO4e,EAAejc,EAAQA,EAAOwc,YAAY,EAAIxc,EAAOwc,YAAY,CAAC1f,OAAO,CAACuE,EAAE,EAC9H6a,EAAOO,cAAc,EACrBF,GAAczgB,IAAI,CAAC,IAAI,CAAEkgB,EAAY,SAAU3e,EAAO4e,EAAejc,EAAQA,EAAOwc,YAAY,EAAIN,EAAOO,cAAc,EAG7HC,GAAe5gB,IAAI,CAAC,IAAI,CAAEuB,EAAO,SAAU6e,EAAQF,EAAYC,EACnE,CAwBA,SAASU,GAAiBtf,CAAK,CAAEib,CAAS,CAAEsE,CAAQ,CAAEC,CAAM,EAIxD,SAASC,EAAgB9c,CAAM,CAAE+c,CAAa,EAC1C,IAAMnW,EAASqV,EAAc1B,UAAU,CAClCtL,QAAQ,CAAC,EAAE,CAChB8M,GAA8BjgB,IAAI,CAAC8c,EAAOvb,EAAO2C,EAAQ+c,EAAed,GACpErV,GACAA,CAAAA,EAAOiB,KAAK,CAACiS,OAAO,CAAG,OAAM,EAG7BkD,GAAUhd,EAAOlD,OAAO,EACxBue,GAA8B,QAAS,CACnC5Y,KAAM,SACN6Z,KAAM,iBAAmBS,EACzBlN,MAAO7P,EAAOlD,OAAO,CAACuE,EAAE,AAC5B,EAAG,KAAK,EAAG4a,GAAegB,YAAY,CAAC,4BAA6Bjd,EAAOlD,OAAO,CAACuE,EAAE,CAE7F,CACA,IAAMuX,EAAQ,IAAI,CAAE9Z,EAAO8Z,EAAM9Z,IAAI,CAAE+c,EAASvD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEpB,EAASxD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEF,EAASJ,AAAa,SAAbA,EAAqB5c,EAAUgd,EACrN3f,EAAM2C,MAAM,CACZ3C,EAAMP,OAAO,CAACqgB,WAAW,EAAI,CAAC,EAElC,GAAI,CAAC9f,GAAS2C,EACV,OAEJ,IAAI0R,EAAM0L,EAAsB,EAAE,AAE9B,CAACJ,GAAWzB,GAAwBvb,GAI/Bub,GAAwBvb,IAC7Bod,CAAAA,EAAsBC,GAAkBvhB,IAAI,CAAC,IAAI,CAAEkE,EAAM,EAHzDod,EAAsBE,GAAaxhB,IAAI,CAAC,IAAI,CAAEkE,EAAQ6c,GAM1DnB,GAA2B0B,EAAqB,CAACliB,EAAGqiB,KAChD,IAAMC,EAActiB,EAAEmhB,iBAAiB,CAACoB,WAAW,GAAIC,EAAcH,EAAElB,iBAAiB,CAACoB,WAAW,GACpG,OAAO,AAACD,EAAcE,EAClB,GAAK,CAACF,CAAAA,EAAcE,CAAU,CACtC,GAGI7B,EAAO5M,QAAQ,CAAC,EAAE,EAClB4M,EAAO5M,QAAQ,CAAC,EAAE,CAAC0K,MAAM,GAG7B,IAAMgE,EAAgBtC,GAA8B,KAAM,CACtD1N,UAAW,2BACf,EAAG,KAAK,EAAGkO,GACLI,EAAgBH,EAAOoB,gBAAgB,CAAC,oCAAoC,CAAC,EAAE,CAiBrF,GAhBAE,EAAoBrf,OAAO,CAAC,AAAC6f,IACzB,GAAM,CAAEvB,kBAAAA,CAAiB,CAAEU,cAAAA,CAAa,CAAE/c,OAAAA,CAAM,CAAE,CAAG4d,EACrDlM,EAAO2J,GAA8B,KAAM,CACvC1N,UAAW,2BACf,EAAG,KAAK,EAAGgQ,GACX,IAAME,EAAMxC,GAA8B,SAAU,CAChD1N,UAAW,iCACXmQ,YAAazB,CACjB,EAAG,KAAK,EAAG3K,GACX,CAAC,QAAS,aAAa,CAAC3T,OAAO,CAAC,AAAC+a,IAC7BsC,GAAyByC,EAAK/E,EAAW,WACrCgE,EAAgB9c,EAAQ+c,EAC5B,EACJ,EACJ,GAEIK,EAAoBne,MAAM,CAAG,EAAG,CAChC,GAAM,CAAEe,OAAAA,CAAM,CAAE+c,cAAAA,CAAa,CAAE,CAAGK,CAAmB,CAAC,EAAE,CACxDN,EAAgB9c,EAAQ+c,EAC5B,MACUC,IACN7E,KAA8F4F,cAAc,CAAC9B,EAAc1B,UAAU,CAACtL,QAAQ,CAAC,EAAE,CAAEnQ,EAAKkf,aAAa,EAAI,IACzK/B,EAAc1B,UAAU,CAACtL,QAAQ,CAAC,EAAE,CAC/BpH,KAAK,CAACiS,OAAO,CAAG,OAE7B,CAiBA,SAAS4C,GAAerf,CAAK,CAAEkd,CAAU,CAAE2B,CAAM,CAAEzZ,CAAI,CAAE6V,CAAS,EAC9D,GAAI,CAACjb,EACD,OAEJ,IAAMud,EAAW,IAAI,CAACA,QAAQ,CAC9Ba,GAA2BS,EAAQ,CAACrM,EAAOoO,KAEvC,IAAMvD,EAAiBH,EAAa,IAAM0D,EAC1C,GAAI3C,GAAwBzL,IACxB6K,GASA,GARIc,GAAyB3L,KAGzB+K,EAAS9e,IAAI,CAAC,IAAI,CAAE4e,EAAgBjY,EAAM6V,EAAW,CAAC,GACtDoE,GAAe5gB,IAAI,CAAC,IAAI,CAAEuB,EAAOqd,EAAgB7K,EAAOpN,EAAM6V,IAI9DoC,KAAkBpgB,EAAoB,CAEtC,IAAM4jB,EAAYC,GAAariB,IAAI,CAAC,IAAI,CAAE2G,EAAMiY,EAAgBpC,GAEhE8F,GAAoBtiB,IAAI,CAAC,IAAI,CAAEuB,EAAOkd,EAAY2D,EAAWzb,EAAMwb,EAAWpO,EAClF,KAGmB,0BAAnB6K,GACKa,GAAwB1L,IAEzB+K,EAAS9e,IAAI,CAAC,IAAI,CAAE4e,EAAgBjY,EAAM6V,EAAW,CACjDzI,MAAOA,EACPpN,KAAM,QACV,GAIZ,EACJ,CAYA,SAAS4b,GAAahhB,CAAK,CAAEib,CAAS,EAClC,IAAMM,EAAQ,IAAI,CAAEiD,EAASvD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAKpFoB,EAAkB,IAAI,CAACxf,IAAI,CAACyf,WAAW,CAAEC,EAAenD,GAA8B,MAAO,CAC5F1N,UAAW,0BACf,EAAG,KAAK,EAAGkO,GACL4C,EAAoB,SAAUC,CAAS,EAEzC/B,GAAiB7gB,IAAI,CAAC8c,EAAOvb,EAAOub,EAAMJ,SAAS,CAAE,MAAOkG,EAChE,EAEMC,EAAQ,IAAI,CAAC/D,QAAQ,CAbwE,mBAa9D,QAAS4D,EAb2F,CACrI3O,MAAO,GACPpN,KAAM,OACNmc,QAAS,oBACTC,eAAgB,0CACpB,GAQ8EjY,EAASyU,GAA8B,IAAK,CACtHyC,YAAaQ,CACjB,EAAG,KAAK,EAAGE,GACXG,EAAMjF,SAAS,CAAC5b,GAAG,CAAC,sCACpB8I,EAAO8S,SAAS,CAAC5b,GAAG,CAAC,uBAErBsd,GAAyBuD,EAAO,QAAS,WACrCF,EAAkB,IAAI,CAAC5O,KAAK,EAExB,IAAI,CAACA,KAAK,CAAC5Q,MAAM,CACjB2H,EAAOiB,KAAK,CAACiS,OAAO,CAAG,eAGvBlT,EAAOiB,KAAK,CAACiS,OAAO,CAAG,MAE/B,GAEA,CAAC,QAAS,aAAa,CAAC/b,OAAO,CAAC,AAAC+a,IAC7BsC,GAAyBxU,EAAQkS,EAAW,WAExC6F,EAAM9O,KAAK,CAAG,GACd4O,EAAkB,IAElB7X,EAAOiB,KAAK,CAACiS,OAAO,CAAG,MAC3B,EACJ,EACJ,CAeA,SAASqE,GAAapB,CAAa,CAAE+B,CAAU,CAAExG,CAAS,EACtD,IAAMyG,EAAkBD,EAAWE,KAAK,CAAC,KAAMC,EAAYF,CAAe,CAACA,EAAgB9f,MAAM,CAAG,EAAE,CAAEigB,EAAa,cAAgBJ,EAAa,SAAW/B,EAAeje,EAAO,IAAI,CAACA,IAAI,CAE5Luc,GAA8B,QAAS,CACnCuD,QAASM,CACb,EAAG,KAAM5G,GAAWuB,WAAW,CAACqB,GAAoBF,cAAc,CAAClc,CAAI,CAACmgB,EAAU,EAAIH,IAEtF,IAAMZ,EAAY7C,GAA8B,SAAU,CACtDiB,KAAM4C,EACNvR,UAAW,yBACXtM,GAAI,qBAAuByd,CAC/B,EAAG,KAAMxG,GAET,OADA4F,EAAUjB,YAAY,CAAC,KAAM,qBAAuB6B,GAC7CZ,CACX,CAwBA,SAASE,GAAoB/gB,CAAK,CAAEyhB,CAAU,CAAEZ,CAAS,CAAEnB,CAAa,CAAEoC,CAAa,CAAEC,CAAc,CAAEC,CAAa,EAE9GP,AAAe,WAAfA,GAA2BA,AAAe,WAAfA,EAE3BzhB,EAAM2C,MAAM,CAACjC,OAAO,CAAC,AAACiC,IAClB,IAAMsf,EAAgBtf,EAAOlD,OAAO,CAAEyiB,EAAaD,EAAchD,IAAI,EACjEgD,EAAcnD,MAAM,CACpBnc,EAAOsc,IAAI,CACXgD,EAAcje,EAAE,EAAI,EACC,CAAA,gCAArBie,EAAcje,EAAE,EAChBie,EAAcje,EAAE,GAAMge,CAAAA,GAClBA,EAAcviB,OAAO,EACrBuiB,EAAcviB,OAAO,CAACuE,EAAE,AAAD,IACtBia,GAAwB8D,IACzBN,AAAe,WAAfA,GACA9e,AAAgB,WAAhBA,EAAOyC,IAAI,EACX2c,CAAAA,EAAiBE,EAAcje,EAAE,AAAD,EAEpCga,GAA8B,SAAU,CACpCxL,MAAOyP,EAAcje,EAAE,AAC3B,EAAG,KAAK,EAAG6c,GAAWrE,WAAW,CAACqB,GAAoBF,cAAc,CAACuE,IAE7E,GAEKxC,GAAiBoC,GAGtBK,AAD2E7D,EAAkB,CAAzEwD,EAAgB,IAAMpC,EAAgE,CAC1Fhf,OAAO,CAAC,AAACwF,IACrB8X,GAA8B,SAAU,CACpCxL,MAAOtM,CACX,EAAG,KAAK,EAAG2a,GAAWrE,WAAW,CAACqB,GAAoBF,cAAc,CAACzX,GACzE,GAGA+X,GAAwB8D,IACxBlB,CAAAA,EAAUrO,KAAK,CAAGuP,CAAa,CAEvC,CAiBA,SAAS9B,GAAatd,CAAM,CAAE6c,CAAM,EAChC,IAII4C,EAJgB3gB,EAAO8Z,AAAb,IAAI,CAAevb,KAAK,EAAIub,AAA5B,IAAI,CAA8Bvb,KAAK,CAACP,OAAO,CAACgC,IAAI,CAAE4gB,EAAmB5gB,GACnFA,EAAK6gB,UAAU,EACf7gB,EAAK6gB,UAAU,CAAC/G,KAAK,EACrB9Z,EAAK6gB,UAAU,CAAC/G,KAAK,CAAC8G,gBAAgB,CAAEtC,EAAsB,EAAE,CAkCpE,OAhCA3B,GAA2Bzb,EAAQ,CAACA,EAAQ6P,KACxC,IAAMyP,EAAgBtf,GAAUA,EAAOlD,OAAO,CAE9C,GAAIkD,EAAOmc,MAAM,EAAImD,GACjBA,EAAcnD,MAAM,CAAE,CACtB,GAAM,CAAEE,kBAAAA,CAAiB,CAAEU,cAAAA,CAAa,CAAE,CAAGX,GAAYpc,EAAQ6P,GACjE,GAAIgN,EAAQ,CAGR,IAAM+C,EAAQ,AAAIC,OADEhD,EAAOiD,OAAO,CAAC,sBAAuB,QACpB,KAAMC,EAAQL,GAChDA,CAAgB,CAAC3C,EAAc,EAC/B2C,CAAgB,CAAC3C,EAAc,CAAC1d,IAAI,CAAC,MAAQ,GAC7Cgd,CAAAA,EAAkBxD,KAAK,CAAC+G,IACxBG,EAAMlH,KAAK,CAAC+G,EAAK,IACjBH,EAAiB,CACbpD,kBAAAA,EACAU,cAAAA,EACA/c,OAAQA,CACZ,EACAod,EAAoBpgB,IAAI,CAACyiB,GAEjC,MAEIA,EAAiB,CACbpD,kBAAAA,EACAU,cAAAA,EACA/c,OAAQA,CACZ,EACAod,EAAoBpgB,IAAI,CAACyiB,EAEjC,CACJ,GACOrC,CACX,CAYA,SAASC,GAAkBrd,CAAM,EAC7B,IAAMod,EAAsB,EAAE,CAW9B,OATApd,EAAOjC,OAAO,CAAC,AAACiC,IACRA,EAAOggB,EAAE,CAAC,QACV5C,EAAoBpgB,IAAI,CAAC,CACrBqf,kBAAmBrc,EAAOsc,IAAI,CAC9BS,cAAe/c,EAAOyC,IAAI,CAC1BzC,OAAQA,CACZ,EAER,GACOod,CACX,CA+BA,SAAShB,GAAYpc,CAAM,CAAE+c,CAAa,EACtC,IAAMjgB,EAAUkD,EAAOlD,OAAO,CAE1ByiB,EAAa,AAACpE,EAAW,CAAC4B,EAAc,EACxC5B,EAAW,CAAC4B,EAAc,CAACnhB,SAAS,CAACqkB,QAAQ,EAC7ClD,EAAcmD,WAAW,GAAIlE,EAAae,EAM9C,OAJIjgB,GAAWA,EAAQ2F,IAAI,GACvBuZ,EAAahc,EAAOlD,OAAO,CAAC2F,IAAI,CAChC8c,EAAavf,EAAOsc,IAAI,EAErB,CACHD,kBAAmBkD,EACnBxC,cAAef,CACnB,CACJ,CAsBA,SAASO,GAAcQ,CAAa,CAAE+B,CAAU,CAAEzhB,CAAK,CAAEib,CAAS,CAAE+G,CAAa,CAAED,CAAc,EAG7F,GAAI,CAAC/hB,EACD,OAGJ,IAAM6gB,EAAYC,GAAariB,IAAI,CANrB,IAAI,CAMyBihB,EAAe+B,EAAYxG,GAEtE8F,GAAoBtiB,IAAI,CARV,IAAI,CAQcuB,EAAOyhB,EAAYZ,EAAW,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGmB,GAElF/D,GAAwB8D,IACxBlB,CAAAA,EAAUrO,KAAK,CAAGuP,CAAa,CAEvC,CA0BA,GAAM,CAAExc,IAAKud,EAAa,CAAE,CAAIjkB,IAE1B,CAAEC,SAAUikB,EAAkB,CAAEtN,cAAeuN,EAAuB,CAAE,CAAInkB,IAWlF,SAASokB,KAEL,OAAOD,GAAwB,MAAO,CAElC1S,UAAW,sDACf,EAAG,KAAK,EAJS,IAAI,CAAC6K,SAAS,CAKnC,CAWA,SAAS+H,GAAYC,CAAO,CAAEC,CAAU,EACpC,IAAMnH,EAAW,IAAI,CAACd,SAAS,CAAE1Z,EAAO,IAAI,CAACA,IAAI,CAC7C6O,EAAY,qBACG,CAAA,IAAf8S,GACA9S,CAAAA,GAAa,0BAAyB,EAG1C,IAAM+S,EAAWL,GAAwB,SAAU,CAC/C1S,UAAAA,CACJ,EAAG,KAAK,EAAG2L,GAGX,OAFAoH,EAAS7G,WAAW,CAACsG,GAAcnF,cAAc,CAAClc,CAAI,CAAC0hB,EAAU,SAAS,EAAIA,IAC9EE,EAASzD,YAAY,CAAC,2BAA4BuD,GAC3CE,CACX,CAKA,SAASC,KACL,IAAMrH,EAAW,IAAI,CAACd,SAAS,CAAEoI,EAAOtH,EACnC4D,gBAAgB,CAAC,wBAAyB2D,EAAcvH,EACxD4D,gBAAgB,CAAC,gCACtB,IAAK,IAAIjf,EAAI,EAAGA,EAAI2iB,EAAK3hB,MAAM,CAAEhB,IAC7B2iB,CAAI,CAAC3iB,EAAE,CAACyb,SAAS,CAACC,MAAM,CAAC,8BACzBkH,CAAW,CAAC5iB,EAAE,CAACyb,SAAS,CAACC,MAAM,CAAC,2BAExC,CA4BA,SAASmH,GAAUC,CAAG,CAAE7gB,CAAK,EACzB,IAAM8gB,EAAU,IAAI,CAACxI,SAAS,CACzB0E,gBAAgB,CAAC,+BACtB6D,CAAAA,EAAIpT,SAAS,EAAI,8BACjBqT,CAAO,CAAC9gB,EAAM,CAACyN,SAAS,EAAI,2BAChC,CAOA,SAASsT,GAAWR,CAAU,EAC1B,IAAM7H,EAAQ,IAAI,CAClBgI,AADsDtH,AAAvB,IAAI,CAACd,SAAS,CAAkB0E,gBAAgB,CAAC,wBAC3Enf,OAAO,CAAC,CAACgjB,EAAK9iB,KACXwiB,CAAAA,AAAe,IAAfA,GACAM,AAAiD,SAAjDA,EAAIhP,YAAY,CAAC,2BAAqC,GAG1D,CAAC,QAAS,aAAa,CAAChU,OAAO,CAAC,AAAC+a,IAC7BsH,GAAmBW,EAAKjI,EAAW,WAE/B6H,GAAY7kB,IAAI,CAAC8c,GACjBkI,GAAUhlB,IAAI,CAAC8c,EAAO,IAAI,CAAE3a,EAChC,EACJ,EACJ,EACJ,CA0BA,GAAM,CAAE2E,IAAKse,EAAS,CAAE,CAAIhlB,IAEtB,CAAEoN,WAAAA,EAAU,CAAE,CAAIpN,IAKlB,CAAEC,SAAUglB,EAAc,CAAErO,cAAesO,EAAmB,CAAEhS,OAAQiS,EAAY,CAAE/kB,UAAWglB,EAAe,CAAE/kB,KAAMglB,EAAU,CAAE,CAAIrlB,GA0D9I,OAAMslB,WAAcnJ,GAMhB/Q,YAAYgR,CAAS,CAAEC,CAAQ,CAAElb,CAAK,CAAE,CACpC,KAAK,CAACib,EAAWC,GACjB,IAAI,CAAClb,KAAK,CAAGA,EACb,IAAI,CAACyB,IAAI,CAAG,AAACwK,CAAAA,KAAaxK,IAAI,CAAC6gB,UAAU,EAAI,CAAC,CAAA,EAAG/G,KAAK,EAAI,CAAC,EAC3DuI,GAAe,IAAI,CAAC3I,SAAS,CAAE,YAAa,KACxC,IAAMiJ,EAAmBpkB,GACrBA,EAAMqkB,kBAAkB,EACxBrkB,EAAMqkB,kBAAkB,CAACD,gBAAgB,CAC7C,GAAIA,EAAkB,CAClBA,EAAiB1d,WAAW,CAAG,CAAA,EAC/B,IAAM4d,EAASR,GAAeD,GAAW,QAAS,KAC9CU,WAAW,KACPH,EAAiB1d,WAAW,CAAG,CAAA,CACnC,EAAG,GACH4d,GACJ,EACJ,CACJ,EACJ,CA0BA/G,SAASC,CAAM,CAAEkC,CAAa,CAAEzE,CAAS,CAAEuJ,CAAe,CAAE,CACxD,IAAM9C,EAAkBlE,EAAOmE,KAAK,CAAC,KAAMF,EAAaC,CAAe,CAACA,EAAgB9f,MAAM,CAAG,EAAE,CAAEH,EAAO,IAAI,CAACA,IAAI,CAAEgjB,EAAY,cAAgB/E,EAAgB,IAAMwE,GAAWM,EAAgBjD,OAAO,CAAEE,GACxMA,EAAWjG,KAAK,CAAC,UAElBuI,GAAoB,QAAS,CACzBxC,QAASkD,EACTnU,UAAWkU,EAAgBhD,cAAc,AAC7C,EAAG,KAAK,EAAGvG,GAAWuB,WAAW,CAACqH,GAAUlG,cAAc,CAAClc,CAAI,CAACggB,EAAW,EAAIA,IAGnF,IAAMH,EAAQyC,GAAoB,QAAS,CACvC9E,KAAMwF,EACNjS,MAAOgS,EAAgBhS,KAAK,CAC5BpN,KAAMof,EAAgBpf,IAAI,CAC1BkL,UAAW,wBACf,EAAG,KAAK,EAAG2K,GAEX,OADAqG,EAAM1B,YAAY,CAAC,uBAAwBpC,GACpC8D,CACX,CACA5F,mBAAoB,CAChB,GAAI,IAAI,CAAC1b,KAAK,CAAE,CACZ,IAAMqkB,EAAqB,IAAI,CAACrkB,KAAK,CAACqkB,kBAAkB,CACxDJ,GAAgBI,EAAoB,cAChCA,GACAA,EAAmBK,qBAAqB,EACxCT,GAAgBI,EAAoB,iBAAkB,CAAE9a,OAAQ8a,EAAmBK,qBAAqB,AAAC,EAEjH,MAEI,KAAK,CAAChJ,mBAEd,CAiBAiJ,UAAU1J,CAAS,CAAE7Y,CAAK,CAAEgD,CAAI,CAAEwf,CAAS,CAAEC,CAAQ,CAAE,CACnD,IAAMtb,EAASwa,GAAoB,SAAU,KAAK,EAAG,KAAK,EAAG9I,GAU7D,OATA1R,EAAOiT,WAAW,CAACqH,GAAUlG,cAAc,CAACvb,IACxCyiB,GACA,CAAC,QAAS,aAAa,CAACnkB,OAAO,CAAC,AAAC+a,IAC7BqI,GAAeva,EAAQkS,EAAW,KAC9B,IAAI,CAACK,UAAU,GACR+I,EAASC,AAlJpC,SAAmB7J,CAAS,CAAE7V,CAAI,EAC9B,IAAM2f,EAAY/hB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAACwc,EAAU4E,gBAAgB,CAAC,UAAWmF,EAAahiB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAACwc,EAAU4E,gBAAgB,CAAC,WAAsIoF,EAAWhK,EAAU4E,gBAAgB,CAAhJ,6CAA8J,CAAC,EAAE,CAAEqF,EAAWjK,EAAU4E,gBAAgB,CAA3I,6CAAyJ,CAAC,EAAE,CACpYsF,EAAe,CACjBC,WAAYhgB,EACZ6f,SAAUA,GAAYA,EAASvQ,YAAY,CAAC,UAAY,GACxDmK,OAAQ,CAAC,CACb,EA4BA,OA3BAkG,EAAUrkB,OAAO,CAAC,AAAC4gB,IACf,IAAM+D,EAAQ/D,EAAM5M,YAAY,CAAC,wBAAoC4M,EAAM5M,YAAY,CAAC,6BAGpFyQ,EAAaG,QAAQ,CAAGhE,EAAM9O,KAAK,CAE9B6S,EACLF,EAAatG,MAAM,CAACwG,EAAM,CAAG/D,EAAM9O,KAAK,CAIxC2S,EAAa/f,IAAI,CAAGkc,EAAM9O,KAAK,AAEvC,GACAwS,EAAWtkB,OAAO,CAAC,AAAC6kB,IAChB,IAAMvhB,EAAKuhB,EAAOvhB,EAAE,CAEpB,GAAIA,AAAO,6BAAPA,GACAA,AAAO,6BAAPA,EAAmC,CACnC,IAAMwhB,EAAYxhB,EAAG2d,KAAK,CAAC,qBAAqB,CAAC,EAAE,AACnDwD,CAAAA,EAAatG,MAAM,CAAC2G,EAAU,CAAGD,EAAO/S,KAAK,AACjD,CACJ,GACI0S,GACAC,CAAAA,EAAatG,MAAM,CAAC,wBAAwB,CAAGqG,EAC1CxQ,YAAY,CAAC,UAAY,EAAC,EAE5ByQ,CACX,EA+G8CP,EAAWxf,KAE7C,GAEGmE,CACX,CASAkc,SAASrgB,CAAI,CAAEpF,CAAK,CAAEP,CAAO,CAAEolB,CAAQ,CAAE,CAChC7kB,IAIL,IAAI,CAAC+b,SAAS,GAED,eAAT3W,GACA,IAAI,CAACsgB,UAAU,CAACC,OAAO,CAAClnB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASolB,GAG1C,uBAATzf,GACA,IAAI,CAAC1F,WAAW,CAACkmB,UAAU,CAACnnB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASolB,GAG9C,oBAATzf,GACA,IAAI,CAAC1F,WAAW,CAACimB,OAAO,CAAClnB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASolB,GAG3C,SAATzf,GACA,IAAI,CAAC1F,WAAW,CAACimB,OAAO,CAAClnB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASolB,EAAU,CAAA,GAElE,IAAI,CAACzf,IAAI,CAAGA,EAEZ,IAAI,CAAC+V,SAAS,CAAC3Q,KAAK,CAACD,MAAM,CAAG,IAAI,CAAC4Q,SAAS,CAAC0K,YAAY,CAAG,KAChE,CACJ,CACA7B,GAAaG,GAAM5lB,SAAS,CAAE,CAC1BmB,YAt9BqB,CACrBimB,QA7IJ,SAAiB3lB,CAAK,CAAEP,CAAO,CAAEolB,CAAQ,CAAEiB,CAAM,EAC7C,GAAI,CAAC9lB,EACD,OAEJ,IAAMic,EAAW,IAAI,CAACd,SAAS,CAAE1Z,EAAO,IAAI,CAACA,IAAI,CAE7C+c,EAAS5B,GAA+B,KAAM,CAC9CtM,UAAW,6BACf,EAAG,KAAK,EAAG2L,GACXuC,EAAOhC,WAAW,CAACE,GAAqBiB,cAAc,CAAClc,CAAI,CAAChC,EAAQsmB,OAAO,CAAC,EAAItmB,EAAQsmB,OAAO,EAAI,KAEnGvH,EAAS5B,GAA+B,MAAO,CAC3CtM,UAAY,oDAChB,EAAG,KAAK,EAAG2L,GACX,IAAM+J,EAAYpJ,GAA+B,MAAO,CACpDtM,UAAW,6BACf,EAAG,KAAK,EAAG2L,GACXgB,GAAcxe,IAAI,CAAC,IAAI,CAAE+f,EAAQxe,EAAO,GAAIP,EAAS,EAAE,CAAE,CAAA,GACzD,IAAI,CAACklB,SAAS,CAACqB,EAAWF,EACrBrkB,EAAKkjB,SAAS,EAAI,MAClBljB,EAAKwkB,UAAU,EAAI,OAASH,EAAS,MAAQ,OAAQ7J,EAAU4I,EACxE,EAyHIe,WAhHJ,SAAoB5lB,CAAK,CAAEP,CAAO,CAAEolB,CAAQ,EACxC,IAAMpjB,EAAO,IAAI,CAACA,IAAI,CAAEwa,EAAW,IAAI,CAACd,SAAS,CAAEsK,EAAW,IAAI,CAACA,QAAQ,CAAEzJ,EAAe,+BAE3C,CAAA,KAA7CC,EAAS3L,SAAS,CAACvJ,OAAO,CAACiV,IAC3BC,CAAAA,EAAS3L,SAAS,EAAI,IAAM0L,EAAe,2BAA0B,EAGrEhc,GACAic,CAAAA,EAASzR,KAAK,CAAC0b,GAAG,CAAGlmB,EAAMgJ,OAAO,CAAG,GAAK,IAAG,EAGjD,IAAM5G,EAAQwa,GAA+B,IAAK,CAC9CtM,UAAW,6BACf,EAAG,KAAK,EAAG2L,GACX7Z,EAAMwd,YAAY,CAAC,aAAc,mBACjCxd,EAAMoa,WAAW,CAACE,GAAqBiB,cAAc,CAACZ,GAEtDtb,CAAI,CAAChC,EAAQsmB,OAAO,CAAC,EAAItmB,EAAQsmB,OAAO,CAExCtmB,EAAQ2J,MAAM,EAAI3J,EAAQ2J,MAAM,CAAC,EAAE,CAAChE,IAAI,CAAE,MAE1C,IAAImE,EAAS,IAAI,CAACob,SAAS,CAAC1I,EAAUxa,EAAK0kB,UAAU,EAAI,OAAQ,OAAQlK,EAAU,KAC/EwJ,EAAShnB,IAAI,CAAC,IAAI,CAAE,kBAAmBuB,EAAOP,EAASolB,EAC3D,EACAtb,CAAAA,EAAO+G,SAAS,EAAI,qCACpB/G,EAAOiB,KAAK,CAAC,mBAAmB,CAAG,OAC/B,IAAI,CAAC0Q,QAAQ,CAAG,YACpB3R,EAAS,IAAI,CAACob,SAAS,CAAC1I,EAAUxa,EAAK2kB,YAAY,EAAI,SAAU,SAAUnK,EAAU4I,GACrFtb,EAAO+G,SAAS,EAAI,uCACpB/G,EAAOiB,KAAK,CAAC,mBAAmB,CAAG,OAC/B,IAAI,CAAC0Q,QAAQ,CAAG,cACxB,CAkFA,EAo9BIwK,WAlXoB,CACpBC,QAlhBJ,SAAiC3lB,CAAK,CAAEqmB,CAAQ,CAAExB,CAAQ,EACtD,IACIyB,EADE7kB,EAAO,IAAI,CAACA,IAAI,CAEtB,GAAI,CAACzB,EACD,OAGJ,IAAI,CAACujB,IAAI,CAAC1T,IAAI,CAACpR,IAAI,CAAC,IAAI,CAAEuB,GAE1B,IAAMumB,EAAiB,IAAI,CAACpL,SAAS,CAChC0E,gBAAgB,CAAC,gCAEtBtB,GAAiBgI,CAAc,CAAC,EAAE,EAClCvF,GAAaviB,IAAI,CAAC,IAAI,CAAEuB,EAAOumB,CAAc,CAAC,EAAE,EAChDjH,GAAiB7gB,IAAI,CAAC,IAAI,CAAEuB,EAAOumB,CAAc,CAAC,EAAE,CAAE,OACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9B1G,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC2B,EAAiB7kB,EAAKkjB,SAAS,EAAI,MAAO,MAAO2B,EAAiBzB,GAEjFtG,GAAiBgI,CAAc,CAAC,EAAE,EAClCjH,GAAiB7gB,IAAI,CAAC,IAAI,CAAEuB,EAAOumB,CAAc,CAAC,EAAE,CAAE,QACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9B1G,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC2B,EAAiB7kB,EAAKwkB,UAAU,EAAI,OAAQ,OAAQK,EAAiBzB,GACpF,IAAI,CAACF,SAAS,CAAC2B,EAAiB7kB,EAAK2kB,YAAY,EAAI,SAAU,SAAUE,EAAiBzB,EAC9F,EA0fI2B,UApFJ,WACI,IAAIC,EAAU,EAOd,OANA,IAAI,CAAC9jB,MAAM,CAACjC,OAAO,CAAC,AAACgmB,IACbA,CAAAA,EAAM5H,MAAM,EACZ4H,EAAMjnB,OAAO,CAACqf,MAAM,AAAD,GACnB2H,GAER,GACOA,CACX,CA4EA,EAgXIlD,KA1Oc,CACd1T,KAvDJ,SAAc7P,CAAK,EACf,GAAI,CAACA,EACD,OAEJ,IAAM2mB,EAAkB,IAAI,CAACjB,UAAU,CAACc,SAAS,CAAC/nB,IAAI,CAACuB,GAEjD4mB,EAAW1D,GAAYzkB,IAAI,CAAC,IAAI,CAAE,OACxCykB,GAAYzkB,IAAI,CAAC,IAAI,CAAE,OAAQkoB,GAE/B1D,GAAexkB,IAAI,CAAC,IAAI,EACxBwkB,GAAexkB,IAAI,CAAC,IAAI,EACxBmlB,GAAWnlB,IAAI,CAAC,IAAI,CAAEkoB,GAEtBlD,GAAUhlB,IAAI,CAAC,IAAI,CAAEmoB,EAAU,EACnC,CA0CA,CAyOA,GAsBA,GAAM,CAAEC,SAAAA,EAAQ,CAAE,CAAIhoB,IAGhB,CAAEC,SAAUgoB,EAAyB,CAAEC,WAAAA,EAAU,CAAE5nB,KAAM6nB,EAAqB,CAAE,CAAInoB,IAmB1F,SAASooB,KACD,IAAI,CAAC1L,KAAK,EACV,IAAI,CAACA,KAAK,CAACO,UAAU,EAE7B,CAIA,SAASoL,GAA8BC,CAAM,EACpC,IAAI,CAAC5L,KAAK,EAEX,CAAA,IAAI,CAACA,KAAK,CAAG,IAjD6B4I,GAiDb,IAAI,CAACnkB,KAAK,CAACmb,SAAS,CAAG,IAAI,CAACnb,KAAK,CAACP,OAAO,CAAC6iB,UAAU,CAACpH,QAAQ,EACrF,IAAI,CAAClb,KAAK,CAACP,OAAO,CAAC2nB,UAAU,EAC1B,IAAI,CAACpnB,KAAK,CAACP,OAAO,CAAC2nB,UAAU,CAACC,GAAG,CAACnM,QAAQ,EAC9C,sDAAwD,IAAI,CAAClb,KAAK,CAAA,EAE1E,IAAI,CAACub,KAAK,CAACkK,QAAQ,CAAC0B,EAAOG,QAAQ,CAAE,IAAI,CAACtnB,KAAK,CAAEmnB,EAAO1nB,OAAO,CAAE0nB,EAAOI,QAAQ,CACpF,CAMA,SAASC,GAAiCrjB,CAAO,CAAEgC,CAAC,EAE3C,IAAI,CAACshB,OAAO,CAACthB,EAAEU,MAAM,CAAE,qBACxB1C,EAAQE,KAAK,CAAC,IAAI,CAAErB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAAC8F,UAAW,GAElE,CAS6B,IAAMmjB,GAHV,CACrBljB,QA7CJ,SAAiBmjB,CAAuB,CAAEhjB,CAAY,EAC9CoiB,GAAWF,GAAU,WACrBC,GAA0Ba,EAAyB,aAAcV,IACjEH,GAA0Ba,EAAyB,YAAaT,IAChEF,GAAsBriB,EAAapG,SAAS,CAAE,uBAAwBipB,IAE9E,CAwCA,EAeM,CAAEI,qBAAAA,EAAoB,CAAE,CAAI/oB,IAe5B,CAAEgpB,wBAAAA,EAAuB,CAAE9oB,MAAO+oB,EAAgB,CAAE7oB,UAAW8oB,EAAoB,CAAEje,MAAOke,EAAgB,CAAE9oB,KAAM+oB,EAAe,CAAE5Z,MAAAA,EAAK,CAAE,CAAIxP,IAyBtJ,SAASqpB,GAA0BC,CAAW,CAAEpX,CAAU,EACtD,IAAMqX,EAAgB,CAAC,EAYvB,MAXA,CAAC,SAAU,SAAS,CAAC1nB,OAAO,CAAC,AAACue,IAC1B,IAAMoJ,EAAkBF,CAAW,CAAClJ,EAAK,CAAEqJ,EAAkBvX,CAAU,CAACkO,EAAK,CACzEoJ,IACIC,EACAF,CAAa,CAACnJ,EAAK,CAAG5Q,GAAMia,GAAiBrY,GAAG,CAAC,CAACsY,EAAc3nB,IAAMonB,GAAiBK,CAAe,CAACznB,EAAE,CAAE2nB,IAG3GH,CAAa,CAACnJ,EAAK,CAAGkJ,CAAW,CAAClJ,EAAK,CAGnD,GACOmJ,CACX,CAqBA,MAAMI,WAAmB1iB,EASrB,OAAOtB,QAAQE,CAAU,CAAE+jB,CAAuB,CAAE9jB,CAAY,CAAE0O,CAAgB,CAAE,CAChFhO,EAA4Bb,OAAO,CAACgkB,GAAY9jB,EAAYC,GAC5DiW,AApiD8D1C,GAoiD9B1T,OAAO,CAAC6O,GACxC2B,AAv0E6D5B,GAu0E9B5O,OAAO,CAACE,EAAY2O,GACnDoV,EAAwBjkB,OAAO,CAACgkB,GAAY9jB,GAC5CgjB,GAAuBljB,OAAO,CAACikB,EAAyB9jB,EAC5D,CAMAsF,YAAYjK,CAAK,CAAEX,CAAW,CAAE,CAC5B,KAAK,GACL,IAAI,CAAC0E,IAAI,CAAG,cAOZ,IAAI,CAAC/D,KAAK,CAAGA,EAOb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAOhB,IAAI,CAACmL,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC3J,IAAI,CAAG,cACZ,IAAI,CAAClB,KAAK,CAAG,GAOb,IAAI,CAACV,MAAM,CAAG,EAAE,CAOhB,IAAI,CAACiH,MAAM,CAAG,EAAE,CAOhB,IAAI,CAAC3J,OAAO,CAAGuoB,GAAiB,IAAI,CAACU,cAAc,CAAErpB,GAOrD,IAAI,CAACA,WAAW,CAAGA,EAGnB,IAAMspB,EAAkBT,GAA0B,IAAI,CAACzoB,OAAO,CAAEJ,EAChE,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAGwmB,EAAgBxmB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC2J,MAAM,CAAGuf,EAAgBvf,MAAM,CA0B5C,IAAI,CAACyG,IAAI,CAAC7P,EAAO,IAAI,CAACP,OAAO,CACjC,CASAmpB,cAAe,CACX,IAAI,CAACC,WAAW,GACZ,IAAI,CAACC,SAAS,EACd,IAAI,CAACC,SAAS,EACd,IAAI,CAACtpB,OAAO,CAAC4a,IAAI,EAEjB,CAAA,IAAI,CAACla,QAAQ,CAAG,IAAI,CAACH,KAAK,CAACE,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC6oB,UAAU,GAAE,CAEtE,CAIAC,WAAY,CACR,IAAMC,EAAiB,IAAI,CAACzpB,OAAO,CAAC0C,MAAM,EAAI,EAAE,CAChD+mB,EAAcxoB,OAAO,CAAC,CAACuB,EAAcrB,KACjC,IAAMwB,EAAQ,IAAI,CAAC+mB,SAAS,CAAClnB,EAAcrB,GAC3ConB,GAAiB,CAAA,EAAMkB,CAAa,CAACtoB,EAAE,CAAEwB,EAAM3C,OAAO,CAC1D,EACJ,CAIA2pB,WAAY,CACR,IAAMhgB,EAAS,IAAI,CAAC3J,OAAO,CAAC2J,MAAM,EAAI,EAAE,CACxCA,EAAO1I,OAAO,CAAC,CAACkQ,EAAchQ,KAC1B,IAAMyI,EAAQ,IAAI,CAACggB,SAAS,CAACzY,EAAchQ,GAC3ConB,GAAiB,CAAA,EAAM5e,CAAM,CAACxI,EAAE,CAAEyI,EAAM5J,OAAO,CACnD,EACJ,CAQAsB,SAAU,CACN,IAAMf,EAAQ,IAAI,CAACA,KAAK,CAAEspB,EAAc,SAAUjV,CAAI,EAClDA,EAAKtT,OAAO,EAChB,EACA,IAAI,CAACoB,MAAM,CAACzB,OAAO,CAAC4oB,GACpB,IAAI,CAAClgB,MAAM,CAAC1I,OAAO,CAAC4oB,GACpB,IAAI,CAACR,SAAS,CAAG,KACjB,IAAI,CAACC,SAAS,CAAG,KACjBjB,GAAiB9nB,EAAMupB,eAAe,CAAE,IAAI,CAACC,cAAc,EAC3D,KAAK,CAACzoB,UACN,IAAI,CAACmN,oBAAoB,GACzB2Z,GAAwB,IAAI,CAAE7nB,EAClC,CAKAspB,YAAYjV,CAAI,CAAE,CAEdyT,GAAiB,IAAI,CAACzT,EAAK1E,QAAQ,CAAG,IAAI,CAAE0E,GAC5CA,EAAKtT,OAAO,EAChB,CAIAioB,YAAa,CACT,GAAI,IAAI,CAACF,SAAS,EAAI,IAAI,CAACC,SAAS,CAChC,MAAO,CACHtmB,EAAG,IAAI,CAACqmB,SAAS,CAACW,IAAI,CACtBniB,EAAG,IAAI,CAACyhB,SAAS,CAAC7C,GAAG,CACrB5b,MAAO,IAAI,CAACwe,SAAS,CAACxe,KAAK,CAC3BC,OAAQ,IAAI,CAACwe,SAAS,CAACxe,MAAM,AACjC,CAER,CAKAmf,eAAe1pB,CAAK,CAAEX,CAAW,CAAE,CAC/B,IAAI,CAACsqB,UAAU,CAACtqB,GAChB,IAAMspB,EAAkBT,GAA0B,IAAI,CAACzoB,OAAO,CAAEJ,EAChE,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAGwmB,EAAgBxmB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC2J,MAAM,CAAGuf,EAAgBvf,MAAM,CAC5C,IAAI,CAACpJ,KAAK,CAAGA,EACb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAChB,IAAI,CAACmL,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC3J,IAAI,CAAG,cACZ,IAAI,CAAC1E,WAAW,CAAGA,EACnB,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACiH,MAAM,CAAG,EAAE,AACpB,CAKAyG,KAAK+Z,CAAkB,CAAEC,CAAY,CAAEhnB,EAAQ,IAAI,CAACA,KAAK,CAAE,CACvD,IAAM7C,EAAQ,IAAI,CAACA,KAAK,CAAE8pB,EAAc,IAAI,CAACrqB,OAAO,CAACyK,SAAS,AAC9D,CAAA,IAAI,CAACrH,KAAK,CAAGA,EACb,IAAI,CAACyL,UAAU,GACf,IAAI,CAACb,gBAAgB,GACrB,IAAI,CAAC2b,SAAS,GACd,IAAI,CAACH,SAAS,GACd,IAAI,CAACc,iBAAiB,GACtB,IAAI,CAACnmB,eAAe,CAAGgkB,GAAqB5nB,EAAO8pB,EACvD,CAKAX,UAAUlnB,CAAY,CAAEY,CAAK,CAAE,CAC3B,IAEkBT,EAAQ,IAzvDoC8V,GAyvDA,IAAI,CAFlD8P,GAAiB,IAAI,CAACvoB,OAAO,CAACwC,YAAY,CAAE,CACxD2L,oBAAqB,IAAI,CAACnO,OAAO,CAACmO,mBAAmB,AACzD,EAAG3L,GAA0EY,GAG7E,OAFAT,EAAMuN,QAAQ,CAAG,QACjB,IAAI,CAACxN,MAAM,CAACxC,IAAI,CAACyC,GACVA,CACX,CAUAinB,UAAUzY,CAAY,CAAE/N,CAAK,CAAE,CAC3B,IAAMpD,EAAUuoB,GAAiB,IAAI,CAACvoB,OAAO,CAACmR,YAAY,CAAE,CACxDhD,oBAAqB,IAAI,CAACnO,OAAO,CAACmO,mBAAmB,AACzD,EAAGgD,GAAevH,EAAQ,IAAKmf,GAAWwB,SAAS,CAACvqB,EAAQ2F,IAAI,CAAC,CAAE,IAAI,CAAE3F,EAASoD,GAGlF,OAFAwG,EAAMsG,QAAQ,CAAG,QACjB,IAAI,CAACvG,MAAM,CAACzJ,IAAI,CAAC0J,GACVA,CACX,CAIA/J,OAAO4K,CAAS,CAAE,CACd,IAAI,CAACoE,UAAU,GACV,IAAI,CAAC1O,OAAO,EACb,IAAI,CAACwK,MAAM,GAEX,IAAI,CAACjK,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,OAAO,CAAC,IAAI,CAACqlB,UAAU,IAEzC,IAAI,CAACiB,WAAW,CAAC,IAAI,CAAC7gB,MAAM,CAAEc,GAC9B,IAAI,CAAC+f,WAAW,CAAC,IAAI,CAAC9nB,MAAM,CAAE+H,GAC9B,IAAI,CAAC0E,mBAAmB,CAAC1E,EAC7B,CAKAggB,WAAW7V,CAAI,CAAEnK,CAAS,CAAE,CACxBmK,EAAK/F,UAAU,GACV+F,EAAK5D,aAAa,IAId4D,EAAKzU,OAAO,EACb,IAAI,CAACuqB,UAAU,CAAC9V,GAEpBA,EAAK/U,MAAM,CAAC2oB,GAAgB/d,EAAW,CAAA,IAASmK,EAAKzU,OAAO,CAACwU,MAAM,EAC/DC,EAAK9R,MAAM,CAACX,MAAM,EAClBwoB,AAtUhB,SAA0B/V,CAAI,EAC1B,IAAMjS,EAAQiS,EAAKzU,OAAO,CAAEyqB,EAAmBhW,EAAK9R,MAAM,CAAC1B,IAAI,CAAC,AAACsK,GAAWA,AAAyB,CAAA,IAAzBA,EAAMxI,MAAM,CAAC+H,OAAO,EAC5FS,AAAkB,CAAA,IAAlBA,EAAMT,OAAO,EACbtI,IACKioB,EAGyB,WAArBjoB,EAAMkoB,UAAU,EACrBloB,EAAMmoB,IAAI,GAHVnoB,EAAMooB,IAAI,GAMtB,EA2TiCnW,IARrB,IAAI,CAACiV,WAAW,CAACjV,EAWzB,CAIA4V,YAAYQ,CAAK,CAAEvgB,CAAS,CAAE,CAC1B,IAAItJ,EAAI6pB,EAAM7oB,MAAM,CAGpB,KAAOhB,KACH,IAAI,CAACspB,UAAU,CAACO,CAAK,CAAC7pB,EAAE,CAAEsJ,EAElC,CAKAoS,QAAS,CAEL,OAAO,IAAI,CAACtc,KAAK,CAACkF,gBAAgB,CAAC,IAAI,CAC3C,CAIAkF,QAAS,CACL,IAAMlK,EAAW,IAAI,CAACF,KAAK,CAACE,QAAQ,AACpC,CAAA,IAAI,CAACN,OAAO,CAAGM,EACVI,CAAC,CAAC,cACFT,IAAI,CAAC,CACNC,QAAS,EACTS,OAAQ,IAAI,CAACd,OAAO,CAACc,MAAM,CAC3B+pB,WAAY,IAAI,CAAC7qB,OAAO,CAACiL,OAAO,CAC5B,UACA,QACR,GACKjK,GAAG,GACR,IAAI,CAACiqB,WAAW,CAAGxqB,EACdI,CAAC,CAAC,qBACFG,GAAG,CAAC,IAAI,CAACb,OAAO,EACjB,IAAI,CAACH,OAAO,CAAC4a,IAAI,EACjB,IAAI,CAACqQ,WAAW,CAAClqB,IAAI,CAAC,IAAI,CAACR,KAAK,CAACC,WAAW,EAEhD,IAAI,CAAC0qB,WAAW,CAAGzqB,EACdI,CAAC,CAAC,qBACFT,IAAI,CAAC,CAENgM,WAAY,EACZC,WAAY,CAChB,GACKrL,GAAG,CAAC,IAAI,CAACb,OAAO,EACrB,IAAI,CAACgpB,YAAY,GACb,IAAI,CAACzoB,QAAQ,EACb,IAAI,CAACP,OAAO,CAACY,IAAI,CAAC,IAAI,CAACL,QAAQ,EAGnC,IAAI,CAACyqB,WAAW,CAAC,IAAI,CAACxhB,MAAM,EAC5B,IAAI,CAACwhB,WAAW,CAAC,IAAI,CAACzoB,MAAM,EAC5B,IAAI,CAAC4D,SAAS,GACd,IAAI,CAAC8I,mBAAmB,EAC5B,CAIAsb,WAAW9V,CAAI,CAAE,CACbA,EAAKjK,MAAM,CAACiK,AAAkB,UAAlBA,EAAK1E,QAAQ,CACrB,IAAI,CAACgb,WAAW,CAChB,IAAI,CAACD,WAAW,CACxB,CAIAE,YAAYH,CAAK,CAAE,CACf,IAAI7pB,EAAI6pB,EAAM7oB,MAAM,CACpB,KAAOhB,KACH,IAAI,CAACupB,UAAU,CAACM,CAAK,CAAC7pB,EAAE,CAEhC,CAIAioB,aAAc,CACV,IAAMgC,EAAQ,IAAI,CAAC7qB,KAAK,CAAC4C,KAAK,CAAEkoB,EAAQ,IAAI,CAAC9qB,KAAK,CAACoL,KAAK,CAAE2f,EAAa,AAAC,CAAA,IAAI,CAACtrB,OAAO,CAAC0C,MAAM,EAAI,EAAE,AAAD,EAC3F6oB,MAAM,CAAC,IAAI,CAACvrB,OAAO,CAAC2J,MAAM,EAAI,EAAE,EAChC6hB,MAAM,CAAC,CAACC,EAAMC,KACf,IAAMhgB,EAAQggB,GACTA,CAAAA,EAAahgB,KAAK,EACdggB,EAAa5oB,MAAM,EAAI4oB,EAAa5oB,MAAM,CAAC,EAAE,EACtD,MAAO,CACHsoB,CAAK,CAAC1f,GAASA,EAAMvI,KAAK,CAAC,EAAIsoB,CAAI,CAAC,EAAE,CACtCJ,CAAK,CAAC3f,GAASA,EAAMC,KAAK,CAAC,EAAI8f,CAAI,CAAC,EAAE,CACzC,AACL,EAAG,EAAE,CACL,CAAA,IAAI,CAACpC,SAAS,CAAGiC,CAAU,CAAC,EAAE,CAC9B,IAAI,CAAChC,SAAS,CAAGgC,CAAU,CAAC,EAAE,AAClC,CAIAva,2BAA2B9F,CAAO,CAAE,CAChC,IAAM0gB,EAAiC,SAAU/W,CAAI,EACjDA,EAAK7D,0BAA0B,CAAC9F,EACpC,EACA,IAAI,CAACgD,aAAa,CAAChN,OAAO,CAAC,AAACyN,IACxBA,EAAa1D,aAAa,CAACC,EAC/B,GACA,IAAI,CAACtB,MAAM,CAAC1I,OAAO,CAAC0qB,GACpB,IAAI,CAACjpB,MAAM,CAACzB,OAAO,CAAC0qB,EACxB,CAIArB,mBAAoB,CAChB,IAAMxqB,EAAa,IAAI,AACvBA,CAAAA,EAAWiqB,cAAc,CAAG,WACxB,OAAOjqB,EAAW4C,MAAM,CAAC8oB,MAAM,CAAC,SAAU9oB,CAAM,CAAEC,CAAK,EAInD,OAHKA,EAAM3C,OAAO,CAAC4rB,YAAY,EAC3BlpB,EAAOxC,IAAI,CAACyC,EAAMxC,OAAO,EAEtBuC,CACX,EAAG,EAAE,CACT,EACA5C,EAAWS,KAAK,CAACupB,eAAe,CAAC5pB,IAAI,CAACJ,EAAWiqB,cAAc,CACnE,CAOAG,WAAWtqB,CAAW,CAAE,CACpB,IAAI,CAACI,OAAO,CAAGuoB,GAAiB,IAAI,CAACU,cAAc,CAAErpB,EACzD,CAQAoL,cAAcC,CAAO,CAAE,CACnB,IAAMjL,EAAU,IAAI,CAACA,OAAO,CAAE6iB,EAAa,IAAI,CAACtiB,KAAK,CAACqkB,kBAAkB,CAAEiG,EAAarC,GAAgBvd,EAAS,CAACjL,EAAQiL,OAAO,EAEhI,GADA,IAAI,CAAC9K,OAAO,CAACC,IAAI,CAAC,aAAcyqB,EAAa,UAAY,UACrD,CAACA,EAAY,CACb,IAAMc,EAAiC,SAAU/W,CAAI,EACjDA,EAAK7D,0BAA0B,CAAC8Z,EACpC,EACA,IAAI,CAAClhB,MAAM,CAAC1I,OAAO,CAAC0qB,GACpB,IAAI,CAACjpB,MAAM,CAACzB,OAAO,CAAC0qB,GAChB9I,EAAW8B,gBAAgB,GAAK,IAAI,EACpC9B,EAAW/G,KAAK,EAChB+G,AAA0B,uBAA1BA,EAAW/G,KAAK,CAACnW,IAAI,EACrB2iB,GAAqBzF,EAAY,aAEzC,CACA7iB,EAAQiL,OAAO,CAAG4f,CACtB,CAUA3f,OAAOtL,CAAW,CAAEC,CAAM,CAAE,CACxB,IAAMU,EAAQ,IAAI,CAACA,KAAK,CAAE2oB,EAAkBT,GAA0B,IAAI,CAAC7oB,WAAW,CAAEA,GAAcisB,EAAmBtrB,EAAMN,WAAW,CAACqH,OAAO,CAAC,IAAI,EAAGtH,EAAUuoB,GAAiB,CAAA,EAAM,IAAI,CAAC3oB,WAAW,CAAEA,EAC7MI,CAAAA,EAAQ0C,MAAM,CAAGwmB,EAAgBxmB,MAAM,CACvC1C,EAAQ2J,MAAM,CAAGuf,EAAgBvf,MAAM,CACvC,IAAI,CAACrI,OAAO,GACZ,IAAI,CAAC2oB,cAAc,CAAC1pB,EAAOP,GAC3B,IAAI,CAACoQ,IAAI,CAAC7P,EAAOP,GAEjBO,EAAMP,OAAO,CAACC,WAAW,CAAC4rB,EAAiB,CAAG,IAAI,CAAC7rB,OAAO,CAC1D,IAAI,CAACgI,UAAU,CAAG,CAAA,EACdwgB,GAAgB3oB,EAAQ,CAAA,IACxBU,EAAMc,eAAe,GAEzBinB,GAAqB,IAAI,CAAE,eAC3B,IAAI,CAACtgB,UAAU,CAAG,CAAA,CACtB,CACJ,CASA+gB,GAAW+C,YAAY,CAAGvhB,EAI1Bwe,GAAWvd,SAAS,CAxuHwCA,EAgvH5Dud,GAAWwB,SAAS,CAAG,CACnB,KAlrFiEnV,GAmrFjE,OAjkFmEK,GAkkFnE,QA32EoEM,GA42EpE,KA/xFiEpC,GAgyFjE,MAz5DkEqH,EA05DtE,EAIA+N,GAAWrjB,KAAK,CAAG,CAAC,EACpBqjB,GAAWjqB,SAAS,CAACmqB,cAAc,CA1mKR,CAevBhe,QAAS,CAAA,EAiBTR,UAAW,CAAC,EAUZmQ,KAAM,CAAA,EAmBNpT,UAAW,KAQXhF,aAAc,CAUVqW,MAAO,SASP+S,aAAc,CAAA,EAUdlS,gBAAiB,sBASjBmB,YAAa,UAObE,aAAc,EAOdD,YAAa,EASbjK,UAAW,wBAQX+J,KAAM,CAAA,EA8CNd,UAAW,WACP,OAAOjU,EAAQ,IAAI,CAACgC,CAAC,EAAI,GAAK,IAAI,CAACA,CAAC,CAAG,kBAC3C,EAWApF,oBAAqB,CAAA,EAWrBkY,SAAU,UAQVvB,QAAS,EAWTO,OAAQ,CAAA,EASR/P,MAAO,UAWPmB,MAAO,CAEHghB,SAAU,QAEVC,WAAY,SAEZ3Y,MAAO,UACX,EAKAxM,QAAS,CAAA,EASTiS,cAAe,SASf9V,EAAG,EASH6E,EAAG,GACP,EAkHAsJ,aAAc,CAuFViC,OAAQ,sBAORiB,YAAa,EASbf,KAAM,sBAONoC,EAAG,EAKHlB,KAAM,CACV,EAUArG,oBAAqB,CAQjBpH,OAAQ,CAAC,EAITgE,MAAO,CACHnD,OAAQ,UACR0L,KAAM,UACNF,OAAQ,UACR,eAAgB,CACpB,EACAtI,OAAQ,GACRF,OAAQ,SACRK,QAAS,CAAA,EACTJ,MAAO,EACX,EAyCA9D,OAAQ,CAAC,EAITjG,OAAQ,CACZ,EA+hJAioB,GAAWjqB,SAAS,CAACuI,YAAY,CAAG,CAAC,MAAO,cAAe,OAAQ,SAAS,CAC5E0I,EAA0BhL,OAAO,CAACgkB,IAML,IAAMkD,GAA0BlD,IAmE7D,AAAC,SAAUtrB,CAA0B,EAqBjCA,EAA2BsH,OAAO,CANlC,SAAiBxE,CAAK,EAIlB,OAHKA,EAAMsiB,UAAU,EACjBtiB,CAAAA,EAAMsiB,UAAU,CAAG,IAAIqJ,EAAU3rB,EAAK,EAEnCA,CACX,CAYA,OAAM2rB,EAMF1hB,YAAYjK,CAAK,CAAE,CACf,IAAI,CAAC4rB,OAAO,CAAG,EAAE,CACjB,IAAI,CAAC5rB,KAAK,CAAGA,CACjB,CAaA6rB,UAAUC,CAAQ,CAAE,CAChB,IAAI,CAAC9rB,KAAK,CAACsiB,UAAU,CAACsJ,OAAO,CAACjsB,IAAI,CAACmsB,EACvC,CAIAnhB,OAAOlL,CAAO,CAAEH,CAAM,CAAE,CACpB,IAAI,CAACssB,OAAO,CAAClrB,OAAO,CAAC,AAACorB,IAClBA,EAASrtB,IAAI,CAAC,IAAI,CAACuB,KAAK,CAAEP,EAASH,EACvC,EACJ,CACJ,CACApC,EAA2ByuB,SAAS,CAAGA,CAC3C,EAAGzuB,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAMnC,IAAM6uB,GAAoC7uB,EAcjE,CAAEoI,QAAS0mB,EAAmC,CAAEvU,SAAUwU,EAAoC,CAAE/sB,KAAMgtB,EAAgC,CAAE,CAAIrtB,IAW5IstB,GAAyB,CAC3BhT,gBAAiB,SACjBmB,YAAa,SACbE,aAAc,SACd1H,MAAO,SACPC,KAAM,SACNyY,SAAU,SACVrpB,OAAQ,SACR8c,KAAM,SACNpM,OAAQ,SACRuZ,MAAO,QACX,EAgEmCC,GALA,CAC/BF,uBAAAA,GACAG,gBA3CJ,SAAyBC,CAAM,EAC3B,OAAOA,EAAO/M,MAAM,CAAC,AAACgN,IAClB,IAAMC,EAAWD,EAAME,IAAI,CAACC,WAAW,GAAIC,EAAUH,EAASI,GAAG,CAAEC,EAAUL,EAASrpB,GAAG,CAGzF2pB,EAAiBb,GAAiCM,EAAME,IAAI,CAACK,cAAc,CAAE,GAC7E,OAAOd,GAAqCW,IAAYX,GAAqCa,IACzFN,EAAMha,KAAK,EAAKoa,EAAUG,GAC1BP,EAAMha,KAAK,EAAKsa,EAAUC,GAE1B,CAACP,EAAME,IAAI,CAACjtB,OAAO,CAACutB,UAAU,AACtC,EAAE,CAAC,EAAE,AACT,EAgCIC,aApBJ,SAAsBlvB,CAAG,CAAEyU,CAAK,EAC5B,IAAM0a,EAAiBf,EAAsB,CAACpuB,EAAI,CAC9CovB,EAAY,OAAO3a,EAIvB,OAHIwZ,GAAoCkB,IACpCC,CAAAA,EAAYD,CAAa,EAEtB,CAAA,CACH,OAAU,OACV,OAAU,SACV,QAAW,UACf,CAAA,CAAC,CAACC,EAAU,AAChB,CAUA,EAeM,CAAEb,gBAAiBc,EAA0C,CAAE,CAAGf,GAElE,CAAE5U,SAAU4V,EAAmC,CAAEvjB,MAAOwjB,EAAgC,CAAE,CAAIzuB,IA8XjE0uB,GAJD,CAC9B9rB,KAlXS,CAQT6gB,WAAY,CAMR/G,MAAO,CACHiS,aAAc,gBACdC,MAAO,QACPrY,OAAQ,SACRsY,QAAS,UACTC,UAAW,YACXvrB,MAAO,QACPwO,aAAc,gBACdgd,YAAa,UACb7a,KAAM,OACNuE,OAAQ,OACRxD,YAAa,aACbjB,OAAQ,aACRuZ,MAAO,QACPnN,KAAM,OACNhd,aAAc,gBACdE,OAAQ,SACRgX,gBAAiB,mBACjB0U,iBAAkB,oBAClBvT,YAAa,eACbE,aAAc,gBACdD,YAAa,eACb/P,MAAO,QACPqO,QAAS,UACT2S,SAAU,YACV1Y,MAAO,QACPvI,OAAQ,SACRnB,OAAQ,eACZ,CACJ,CACJ,EAuUIkZ,WAlUe,CAWfwL,kBAAmB,gCA6BnBC,SAAU,CAQNC,iBAAkB,CAEd1d,UAAW,+BAEX2d,MAAO,SAAU9nB,CAAC,EACd,IAAMomB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAAIgoB,EAAU5B,GAAUa,GAA2Cb,EAAO3pB,KAAK,EAAGwrB,EAAU7B,GAAUa,GAA2Cb,EAAOnhB,KAAK,EAAGkX,EAAa,IAAI,CAACtiB,KAAK,CAACP,OAAO,CAAC6iB,UAAU,CAE5P,GAAI,AAAC6L,GAAYC,EAGjB,OAAO,IAAI,CAACpuB,KAAK,CAAC6E,aAAa,CAACyoB,GAAiC,CAC7DvH,QAAS,SACT3gB,KAAM,kBACNgE,OAAQ,CAAC,CACDhE,KAAM,SACN+F,MAAO,CACH1I,EAAG0rB,EAAQ3b,KAAK,CAChBlL,EAAG8mB,EAAQ5b,KAAK,CAChB5P,MAAOurB,EAAQzB,IAAI,CAAC7pB,KAAK,CACzBuI,MAAOgjB,EAAQ1B,IAAI,CAAC7pB,KAAK,AAC7B,EACAsS,EAAG,CACP,EAAE,AACV,EAAGmN,EAAW+L,kBAAkB,CAAE/L,EAAWyL,QAAQ,CAACC,gBAAgB,CACjEK,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUnoB,CAAC,CAAE5G,CAAU,EACnB,IAEIwa,EAFE3Q,EAAS7J,EAAWE,OAAO,CAAC2J,MAAM,CAAEmlB,EAAiB,AAACnlB,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC+B,KAAK,EAC9F,CAAC,EAEL,GAAIkiB,GAAoCkB,EAAc3rB,KAAK,GACvDyqB,GAAoCkB,EAAcnjB,KAAK,EAAG,CAC1D,IAAM5C,EAAW,IAAI,CAACxI,KAAK,CAACwI,QAAQ,CAAE/F,EAAI,IAAI,CAACzC,KAAK,CAAC4C,KAAK,CAAC2rB,EAAc3rB,KAAK,CAAC,CAC1E2J,QAAQ,CAACgiB,EAAc9rB,CAAC,EAAG6E,EAAI,IAAI,CAACtH,KAAK,CAACoL,KAAK,CAACmjB,EAAcnjB,KAAK,CAAC,CACpEmB,QAAQ,CAACgiB,EAAcjnB,CAAC,EAC7ByS,EAAW5W,KAAKC,GAAG,CAACD,KAAK6S,IAAI,CAAC7S,KAAKqrB,GAAG,CAAChmB,EAAWlB,EAAInB,EAAEmC,MAAM,CAAG7F,EAAI0D,EAAEmC,MAAM,CAAE,GAC3EnF,KAAKqrB,GAAG,CAAChmB,EAAW/F,EAAI0D,EAAEiC,MAAM,CAAGd,EAAInB,EAAEiC,MAAM,CAAE,IAAK,EAC9D,CACA7I,EAAWoL,MAAM,CAAC,CACdvB,OAAQ,CAAC,CACD+L,EAAG4E,CACP,EAAE,AACV,EACJ,EACH,AACL,EASA0U,kBAAmB,CACfne,UAAW,gCACX2d,MAAO,SAAU9nB,CAAC,EACd,IAAMomB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAAIgoB,EAAU5B,GAAUa,GAA2Cb,EAAO3pB,KAAK,EAAGwrB,EAAU7B,GAAUa,GAA2Cb,EAAOnhB,KAAK,EAAGkX,EAAa,IAAI,CAACtiB,KAAK,CAACP,OAAO,CAAC6iB,UAAU,CAC5P,GAAI,AAAC6L,GAAYC,EAGjB,OAAO,IAAI,CAACpuB,KAAK,CAAC6E,aAAa,CAACyoB,GAAiC,CAC7DvH,QAAS,UACT3gB,KAAM,kBACNgE,OAAQ,CACJ,CACIhE,KAAM,UACNxC,MAAOurB,EAAQzB,IAAI,CAAC7pB,KAAK,CACzBuI,MAAOgjB,EAAQ1B,IAAI,CAAC7pB,KAAK,CACzBN,OAAQ,CAAC,CACDE,EAAG0rB,EAAQ3b,KAAK,CAChBlL,EAAG8mB,EAAQ5b,KAAK,AACpB,EAAG,CACC/P,EAAG0rB,EAAQ3b,KAAK,CAChBlL,EAAG8mB,EAAQ5b,KAAK,AACpB,EAAE,CACNmE,GAAI,CACR,EACH,AACL,EAAG2L,EAAW+L,kBAAkB,CAAE/L,EAAWyL,QAAQ,CAACU,iBAAiB,CAClEJ,kBAAkB,EAC3B,EACAC,MAAO,CACH,SAAUnoB,CAAC,CAAE5G,CAAU,EACnB,IAAMsH,EAAStH,EAAW6J,MAAM,CAAC,EAAE,CAAEuK,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAC3FsE,EAAOwI,cAAc,CAAClJ,EAAEmC,MAAM,CAAGqL,EAASlR,CAAC,CAAE0D,EAAEiC,MAAM,CAAGuL,EAASrM,CAAC,CAAE,GACpET,EAAOvH,MAAM,CAAC,CAAA,EAClB,EACA,SAAU6G,CAAC,CAAE5G,CAAU,EACnB,IAAMsH,EAAStH,EAAW6J,MAAM,CAAC,EAAE,CAAEuK,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAAG2T,EAAYrP,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAAGmsB,EAAO7nB,EAAO6O,mBAAmB,CAAC/B,EAAUuC,EAAW/P,EAAEmC,MAAM,CAAEnC,EAAEiC,MAAM,EAAGgD,EAAQvE,EAAOgQ,QAAQ,GAAI8X,EAAQxrB,KAAK4S,GAAG,CAAC3K,EAAMsB,OAAO,CAAC,GAAKtB,EAAMsB,OAAO,CAACgiB,IACjT7nB,EAAOsQ,UAAU,CAACwX,GAClB9nB,EAAOvH,MAAM,CAAC,CAAA,EAClB,EACH,AACL,EAQAsvB,oBAAqB,CAEjBte,UAAW,kCAEX2d,MAAO,SAAU9nB,CAAC,EACd,IAAMomB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAAIgoB,EAAU5B,GAAUa,GAA2Cb,EAAO3pB,KAAK,EAAGwrB,EAAU7B,GAAUa,GAA2Cb,EAAOnhB,KAAK,EAE/M,GAAI,CAAC+iB,GAAW,CAACC,EACb,OAEJ,IAAM3rB,EAAI0rB,EAAQ3b,KAAK,CAAElL,EAAI8mB,EAAQ5b,KAAK,CAAE5P,EAAQurB,EAAQzB,IAAI,CAAC7pB,KAAK,CAAEuI,EAAQgjB,EAAQ1B,IAAI,CAAC7pB,KAAK,CAAEyf,EAAa,IAAI,CAACtiB,KAAK,CAACP,OAAO,CAAC6iB,UAAU,CAC9I,OAAO,IAAI,CAACtiB,KAAK,CAAC6E,aAAa,CAACyoB,GAAiC,CAC7DvH,QAAS,YACT3gB,KAAM,kBACNgE,OAAQ,CAAC,CACDhE,KAAM,OACN7C,OAAQ,CACJ,CAAEK,MAAAA,EAAOwI,MAAAA,EAAO3I,EAAAA,EAAG6E,EAAAA,CAAE,EACrB,CAAE1E,MAAAA,EAAOwI,MAAAA,EAAO3I,EAAAA,EAAG6E,EAAAA,CAAE,EACrB,CAAE1E,MAAAA,EAAOwI,MAAAA,EAAO3I,EAAAA,EAAG6E,EAAAA,CAAE,EACrB,CAAE1E,MAAAA,EAAOwI,MAAAA,EAAO3I,EAAAA,EAAG6E,EAAAA,CAAE,EACrB,CAAE4E,QAAS,GAAI,EAClB,AACL,EAAE,AACV,EAAGoW,EACE+L,kBAAkB,CAAE/L,EACpByL,QAAQ,CACRa,mBAAmB,CACnBP,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUnoB,CAAC,CAAE5G,CAAU,EACnB,IAAM6J,EAAS7J,EAAWE,OAAO,CAAC2J,MAAM,CAAE7G,EAAU,AAAC6G,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC7G,MAAM,EACxF,EAAE,CAAGgqB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAAIgoB,EAAU5B,GAAUa,GAA2Cb,EAAO3pB,KAAK,EAAGwrB,EAAU7B,GAAUa,GAA2Cb,EAAOnhB,KAAK,EAClN,GAAI+iB,GAAWC,EAAS,CACpB,IAAM3rB,EAAI0rB,EAAQ3b,KAAK,CAAElL,EAAI8mB,EAAQ5b,KAAK,AAE1CjQ,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EAEdF,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EACdF,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGA,EAEd/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGA,EACd/H,EAAWoL,MAAM,CAAC,CACdvB,OAAQ,CAAC,CACD7G,OAAQA,CACZ,EAAE,AACV,EACJ,CACJ,EACH,AACL,EAOAssB,gBAAiB,CAEbve,UAAW,8BAEX2d,MAAO,SAAU9nB,CAAC,EACd,IAAMomB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAAIgoB,EAAU5B,GAAUa,GAA2Cb,EAAO3pB,KAAK,EAAGwrB,EAAU7B,GAAUa,GAA2Cb,EAAOnhB,KAAK,EAAGkX,EAAa,IAAI,CAACtiB,KAAK,CAACP,OAAO,CAAC6iB,UAAU,CAE5P,GAAI,AAAC6L,GAAYC,EAGjB,OAAO,IAAI,CAACpuB,KAAK,CAAC6E,aAAa,CAACyoB,GAAiC,CAC7DvH,QAAS,QACT3gB,KAAM,kBACNnD,aAAc,CACVqV,OAAQ,UACR8C,SAAU,OACVC,KAAM,CAAA,CACV,EACAlY,OAAQ,CAAC,CACDgJ,MAAO,CACHvI,MAAOurB,EAAQzB,IAAI,CAAC7pB,KAAK,CACzBuI,MAAOgjB,EAAQ1B,IAAI,CAAC7pB,KAAK,CACzBJ,EAAG0rB,EAAQ3b,KAAK,CAChBlL,EAAG8mB,EAAQ5b,KAAK,AACpB,CACJ,EAAE,AACV,EAAG8P,EACE+L,kBAAkB,CAAE/L,EACpByL,QAAQ,CACRc,eAAe,CACfR,kBAAkB,EAC3B,CACJ,CACJ,EAmDA7nB,OAAQ,CAAC,EAcT6nB,mBAAoB,CAChBnkB,UAAW,CACP4kB,MAAO,CACX,CACJ,CACJ,CASA,EAgBM,CAAEnF,WAAAA,EAAU,CAAE,CAAI9qB,IAElB,CAAEyY,OAAQyX,EAAyB,CAAE,CAAI1X,KAEzC,CAAEwP,SAAUmI,EAA2B,CAAEzpB,IAAK0pB,EAAsB,CAAEC,IAAAA,EAAG,CAAE,CAAIrwB,IAG/E,CAAEytB,gBAAiB6C,EAAkC,CAAElC,aAAcmC,EAA+B,CAAE,CAAG/C,GAEzG,CAAEvtB,SAAUuwB,EAA2B,CAAExvB,KAAAA,EAAI,CAAEyF,QAASgqB,EAA0B,CAAErwB,UAAWswB,EAA4B,CAAE1S,QAAS2S,EAA0B,CAAEC,WAAAA,EAAU,CAAEhY,SAAUiY,EAA2B,CAAEjhB,SAAUkhB,EAA2B,CAAE7lB,MAAO8lB,EAAwB,CAAEjqB,WAAYkqB,EAA6B,CAAE3wB,KAAM4wB,EAAuB,CAAE/I,WAAYgJ,EAA6B,CAAE,CAAIlxB,IA+B5Z,SAASmxB,KACD,IAAI,CAAChwB,KAAK,CAACqkB,kBAAkB,EAC7B,IAAI,CAACrkB,KAAK,CAACqkB,kBAAkB,CAAC4L,kBAAkB,EAExD,CAIA,SAASC,KACD,IAAI,CAAC7L,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACtjB,OAAO,EAEvC,CAIA,SAASovB,KACL,IAAM1wB,EAAU,IAAI,CAACA,OAAO,CACxBA,GAAWA,EAAQ6iB,UAAU,EAAI7iB,EAAQ6iB,UAAU,CAACyL,QAAQ,GAC5D,IAAI,CAAC1J,kBAAkB,CAAG,IAAI+L,GAAmB,IAAI,CAAE3wB,EAAQ6iB,UAAU,EACzE,IAAI,CAAC+B,kBAAkB,CAACgM,UAAU,GAClC,IAAI,CAAChM,kBAAkB,CAACiM,UAAU,GAE1C,CAIA,SAASC,KACL,IAAMlM,EAAqB,IAAI,CAACA,kBAAkB,CAAEmM,EAAoB,0BACxE,GAAI,IAAI,EAAInM,EAAoB,CAG5B,IAAIoM,EAAiB,CAAA,EAMrB,GALA,IAAI,CAAC9tB,MAAM,CAACjC,OAAO,CAAC,AAACiC,IACb,CAACA,EAAOlD,OAAO,CAACutB,UAAU,EAAIrqB,EAAO+H,OAAO,EAC5C+lB,CAAAA,EAAiB,CAAA,CAAG,CAE5B,GACI,IAAI,CAACpM,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAClJ,SAAS,EACjC,IAAI,CAACkJ,kBAAkB,CAAClJ,SAAS,CAAC,EAAE,CAAE,CACtC,IAAMA,EAAY,IAAI,CAACkJ,kBAAkB,CAAClJ,SAAS,CAAC,EAAE,CACtD0U,GAA8BxL,EAAmBqM,eAAe,CAAE,CAACle,EAAOzU,KAGtE,IAAM4yB,EAAaxV,EAAU0E,gBAAgB,CAAC,IAAM9hB,GACpD,GAAI4yB,EACA,IAAK,IAAI/vB,EAAI,EAAGA,EAAI+vB,EAAW/uB,MAAM,CAAEhB,IAAK,CACxC,IAAM2I,EAASonB,CAAU,CAAC/vB,EAAE,CAAEgwB,EAAMrnB,EAAO+G,SAAS,AAChDkC,AAAsB,CAAA,WAAtBA,EAAMqe,WAAW,CAGsB,KAAnCD,EAAI7pB,OAAO,CAACypB,IACZjnB,EAAO8S,SAAS,CAACC,MAAM,CAACkU,GAGtBC,EAOiC,KAAnCG,EAAI7pB,OAAO,CAACypB,IACZjnB,EAAO8S,SAAS,CAACC,MAAM,CAACkU,GAPW,KAAnCI,EAAI7pB,OAAO,CAACypB,IACZjnB,CAAAA,EAAO+G,SAAS,EAAI,IAAMkgB,CAAgB,CAStD,CAER,EACJ,CACJ,CACJ,CAIA,SAASM,KACL,IAAI,CAACb,kBAAkB,EAC3B,CAIA,SAASc,KACL,IAAI,CAACrM,qBAAqB,CAAG,IACjC,CAKA,SAASsM,GAAqBC,CAAc,EACxC,IAmDIC,EAAaC,EAnDXC,EAAgBH,EAAe1yB,SAAS,CAACmqB,cAAc,CAACliB,MAAM,EAChEyqB,EAAe1yB,SAAS,CAACmqB,cAAc,CAACliB,MAAM,CAAC6qB,KAAK,CAKxD,SAASC,EAAmBC,CAAc,EACtC,IAAMhyB,EAAa,IAAI,CAAE+iB,EAAa/iB,EAAWS,KAAK,CAACqkB,kBAAkB,CAAEmN,EAAiBlP,EAAW8B,gBAAgB,CACnHgN,GACAA,EAAc3yB,IAAI,CAACc,EAAYgyB,GAE/BC,IAAmBjyB,GAEnB+iB,EAAW2N,kBAAkB,GAC7B3N,EAAW8B,gBAAgB,CAAG7kB,EAC9BA,EAAWiR,0BAA0B,CAAC,CAAA,GACtC+e,GAA6BjN,EAAY,YAAa,CAClD/iB,WAAYA,EACZ+nB,SAAU,qBACV7nB,QAAS6iB,EAAWmP,kBAAkB,CAAClyB,GACvCgoB,SAAU,SAAUmK,CAAI,EACpB,GAAIA,AAAoB,WAApBA,EAAKtM,UAAU,CACf9C,EAAW8B,gBAAgB,CAAG,CAAA,EAC9B9B,EAAWtiB,KAAK,CAACkF,gBAAgB,CAAC3F,OAEjC,CACD,IAAM4nB,EAAS,CAAC,EAChB7E,EAAWqP,eAAe,CAACD,EAAK7S,MAAM,CAAEsI,GACxC7E,EAAW2N,kBAAkB,GAC7B,IAAMrC,EAAczG,EAAOyG,WAAW,AACN,CAAA,YAA5BruB,EAAWE,OAAO,CAAC2F,IAAI,GAGvBwoB,EAAYgE,UAAU,CAACC,OAAO,CAAIjE,AACb,IADaA,EAAYgE,UAAU,CACnD9d,WAAW,CAChB8Z,EAAYkE,UAAU,CAACD,OAAO,CAAIjE,AACb,IADaA,EAAYkE,UAAU,CACnDhe,WAAW,EAEpBvU,EAAWoL,MAAM,CAACwc,EACtB,CACJ,CACJ,IAIAoI,GAA6BjN,EAAY,cAG7CiP,EAAenN,gBAAgB,CAAG,CAAA,CACtC,CAoBAwL,GAAyB,CAAA,EAAMqB,EAAe1yB,SAAS,CAACmqB,cAAc,CAACliB,MAAM,CAAE,CAC3E6qB,MAAOC,EACPS,WAhBJ,SAAoB5rB,CAAC,EACjB+qB,EAAc/qB,EAAE6rB,OAAO,CAAC,EAAE,CAACC,OAAO,CAClCd,EAAchrB,EAAE6rB,OAAO,CAAC,EAAE,CAACE,OAAO,AACtC,EAcIC,SAVJ,SAA2BhsB,CAAC,EACP+qB,GAAc/tB,KAAK6S,IAAI,CAAC7S,KAAKqrB,GAAG,CAAC0C,EAAc/qB,EAAEisB,cAAc,CAAC,EAAE,CAACH,OAAO,CAAE,GACzF9uB,KAAKqrB,GAAG,CAAC2C,EAAchrB,EAAEisB,cAAc,CAAC,EAAE,CAACF,OAAO,CAAE,KAAO,GAE3DZ,EAAmB7yB,IAAI,CAAC,IAAI,CAAE0H,EAEtC,CAKA,EACJ,CASA,MAAMiqB,GAMF,OAAO5rB,QAAQC,CAAe,CAAEC,CAAU,CAAE,CACpCqrB,GAA8Bf,GAA6B,wBAC3DK,GAA4B5qB,EAAiB,SAAUurB,IAEvDgB,GAAqBvsB,GAErBorB,GAA8BprB,EAAgBU,KAAK,CAAE,AAAC8rB,IAClDD,GAAqBC,EACzB,GACA5B,GAA4B3qB,EAAY,UAAWwrB,IACnDb,GAA4B3qB,EAAY,OAAQyrB,IAChDd,GAA4B3qB,EAAY,SAAU6rB,IAClDlB,GAA4Be,GAAoB,aAAcU,IAC9DzB,GAA4Be,GAAoB,iBAAkBW,IAClEpH,GAAW4D,IAEnB,CAMAtjB,YAAYjK,CAAK,CAAEP,CAAO,CAAE,CACxB,IAAI,CAACixB,eAAe,CAAG,KAAK,EAC5B,IAAI,CAAC1wB,KAAK,CAAGA,EACb,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAAC4yB,cAAc,CAAG,EAAE,CACxB,IAAI,CAAClX,SAAS,CACV,IAAI,CAACnb,KAAK,CAACmb,SAAS,CAACmX,sBAAsB,CAAC,IAAI,CAAC7yB,OAAO,CAACquB,iBAAiB,EAAI,IAC7E,IAAI,CAAC3S,SAAS,CAACvZ,MAAM,EACtB,CAAA,IAAI,CAACuZ,SAAS,CAAG8T,GAAuBqD,sBAAsB,CAAC,IAAI,CAAC7yB,OAAO,CAACquB,iBAAiB,EAAI,GAAE,CAE3G,CAMAyE,UAAUpsB,CAAC,CAAE,CACT,IAAMomB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAClD,MAAO,CACHomB,GAAU4C,GAAmC5C,EAAO3pB,KAAK,EACzD2pB,GAAU4C,GAAmC5C,EAAOnhB,KAAK,EAC5D,AACL,CAOAilB,YAAa,CACT,IAAM/N,EAAa,IAAI,CAAEtiB,EAAQsiB,EAAWtiB,KAAK,CAAEwyB,EAAoBlQ,EAAWnH,SAAS,CAAE1b,EAAU6iB,EAAW7iB,OAAO,AAEzH6iB,CAAAA,EAAWoO,eAAe,CAAG,CAAC,EAC9Bb,GAA+BpwB,EAAQsuB,QAAQ,EAAI,CAAC,EAAI,AAACvb,IACrD8P,EAAWoO,eAAe,CAACle,EAAMlC,SAAS,CAAC,CAAGkC,CAClD,GAEA,EAAE,CAAC9R,OAAO,CAACjC,IAAI,CAAC+zB,EAAmB,AAACC,IAChCnQ,EAAW+P,cAAc,CAAC1yB,IAAI,CAAC0vB,GAA4BoD,EAAc,QAAS,AAACzxB,IAC/E,IAAM+sB,EAAWzL,EAAWoQ,eAAe,CAACD,EAAczxB,GACtD+sB,GACC,CAACA,EAASxkB,MAAM,CAAC8S,SAAS,CACtBsW,QAAQ,CAAC,4BACdrQ,EAAWsQ,mBAAmB,CAAC7E,EAASxkB,MAAM,CAAEwkB,EAASvnB,MAAM,CAAExF,EAEzE,GACJ,GACA6uB,GAA+BpwB,EAAQ+G,MAAM,EAAI,CAAC,EAAI,CAACqe,EAAUpJ,KACzDgU,GAAW5K,IACXvC,EAAW+P,cAAc,CAAC1yB,IAAI,CAAC0vB,GAA4B/M,EAAY7G,EAAWoJ,EAAU,CAAExe,QAAS,CAAA,CAAM,GAErH,GACAic,EAAW+P,cAAc,CAAC1yB,IAAI,CAAC0vB,GAA4BrvB,EAAMmb,SAAS,CAAE,QAAS,SAAUhV,CAAC,EACxF,CAACnG,EAAM0G,WAAW,EAClB1G,EAAM8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGtI,EAAM+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGpI,EAAMgJ,OAAO,CAAE,CACpEC,gBAAiB,CAAA,CACrB,IACAqZ,EAAWuQ,kBAAkB,CAAC,IAAI,CAAE1sB,EAE5C,IACAmc,EAAW+P,cAAc,CAAC1yB,IAAI,CAAC0vB,GAA4BrvB,EAAMmb,SAAS,CAAE,AAACtc,IAA+E2G,aAAa,CAAG,YAAc,YAAa,SAAUW,CAAC,EAC9Mmc,EAAWwQ,0BAA0B,CAAC,IAAI,CAAE3sB,EAChD,EAAG,AAACtH,IAA+E2G,aAAa,CAAG,CAAEa,QAAS,CAAA,CAAM,EAAI,KAAK,GACjI,CAOAiqB,YAAa,CACT,IAAMhO,EAAa,IAAI,CACvByJ,GACKvnB,OAAO,CAAC,IAAI,CAACxE,KAAK,EAAEsiB,UAAU,CAC9BuJ,SAAS,CAAC,AAACpsB,IACZ6iB,EAAW3X,MAAM,CAAClL,EACtB,EACJ,CAiBAmzB,oBAAoBrpB,CAAM,CAAE/C,CAAM,CAAEusB,CAAU,CAAE,CAC5C,IAAyB/yB,EAAQsiB,AAAd,IAAI,CAAqBtiB,KAAK,CAAEgzB,EAAehzB,EAAME,QAAQ,CAAC+yB,UAAU,CACvFC,EAAqB,CAAA,CACrB5Q,CAFe,IAAI,CAERoC,qBAAqB,GAC5BpC,AAHW,IAAI,CAGJoC,qBAAqB,CAACrI,SAAS,GAAK9S,EAAO8S,SAAS,EAC/D6W,CAAAA,EAAqB,CAAA,CAAI,EAE7B3D,GANe,IAAI,CAMsB,iBAAkB,CAAEhmB,OAAQ+Y,AANtD,IAAI,CAM6DoC,qBAAqB,AAAC,GAClGpC,AAPW,IAAI,CAOJ6Q,SAAS,GAEhB7Q,AATO,IAAI,CASA8Q,kBAAkB,EAC7B9Q,AAAuC,gBAAvCA,AAVO,IAAI,CAUA8Q,kBAAkB,CAACrvB,IAAI,EAClC/D,EAAMkF,gBAAgB,CAACod,AAXhB,IAAI,CAWuB8Q,kBAAkB,EAExD9Q,AAbW,IAAI,CAaJ+Q,cAAc,CAAG/Q,AAbjB,IAAI,CAawB6Q,SAAS,CAAG,CAAA,IAGvDD,GACA5Q,AAjBe,IAAI,CAiBRgR,cAAc,CAAG9sB,EAC5B8b,AAlBe,IAAI,CAkBRoC,qBAAqB,CAAGnb,EACnCgmB,GAnBe,IAAI,CAmBsB,eAAgB,CAAEhmB,OAAQA,CAAO,GAEtE/C,EAAOqJ,IAAI,EACXrJ,EAAOqJ,IAAI,CAACpR,IAAI,CAtBL,IAAI,CAsBc8K,EAAQwpB,GAErCvsB,CAAAA,EAAOynB,KAAK,EAAIznB,EAAO8nB,KAAK,AAAD,GAC3BtuB,EAAME,QAAQ,CAAC+yB,UAAU,CAAC1iB,QAAQ,CAAC,0BAIvCvQ,EAAMonB,UAAU,EAAI7d,EAAO8S,SAAS,CAACC,MAAM,CAAC,qBAC5C0W,EAAaO,WAAW,CAAC,wBACzBjR,AA/Be,IAAI,CA+BR6Q,SAAS,CAAG,CAAA,EACvB7Q,AAhCe,IAAI,CAgCR+Q,cAAc,CAAG,CAAA,EAC5B/Q,AAjCe,IAAI,CAiCRgR,cAAc,CAAG,KAEpC,CAeAT,mBAAmB7yB,CAAK,CAAE+yB,CAAU,CAAE,CAClC/yB,EAAQ,IAAI,CAACA,KAAK,CAClB,IAAyBokB,EAAmB9B,AAAzB,IAAI,CAAgC8B,gBAAgB,CAAEkP,EAAiBhR,AAAvE,IAAI,CAA8EgR,cAAc,CAAEN,EAAehzB,EAAME,QAAQ,CAAC+yB,UAAU,CACzJ7O,IAGI,AAACA,EAAiB1d,WAAW,EAC5BqsB,EAAW3O,gBAAgB,GAE5B2O,EAAWlsB,MAAM,CAACqW,UAAU,EAE3BsW,AA1XjB,SAAyBC,CAAE,CAAElwB,CAAC,EAC1B,IAAMmwB,EAAexE,GAAIyE,OAAO,CAACp1B,SAAS,CAAEq1B,EAAiBF,EAAaG,OAAO,EAC7EH,EAAaI,iBAAiB,EAC9BJ,EAAaK,qBAAqB,CAClCC,EAAM,KACV,GAAIN,EAAaO,OAAO,CACpBD,EAAMN,EAAaO,OAAO,CAACx1B,IAAI,CAACg1B,EAAIlwB,QAGpC,EAAG,CACC,GAAIqwB,EAAen1B,IAAI,CAACg1B,EAAIlwB,GACxB,OAAOkwB,EAEXA,EAAKA,EAAGS,aAAa,EAAIT,EAAGvW,UAAU,AAC1C,OAASuW,AAAO,OAAPA,GAAeA,AAAgB,IAAhBA,EAAGU,QAAQ,CAAQ,CAE/C,OAAOH,CACX,EAyWiCjB,EAAWlsB,MAAM,CAAE,qBAG/Bud,EAAiB1d,WAAW,EAEjC6d,WAAW,KACPH,EAAiB1d,WAAW,CAAG,CAAA,CACnC,EAAG,GANH6oB,GAVW,IAAI,CAU0B,eAS5C+D,GAAmBA,EAAerF,KAAK,GAGvC3L,AAtBc,IAAI,CAsBP6Q,SAAS,EAsBrB7Q,AA5Ce,IAAI,CA4CR6Q,SAAS,CAACJ,EAAYzQ,AA5ClB,IAAI,CA4CyB8Q,kBAAkB,EAC1D9Q,AA7CW,IAAI,CA6CJgM,KAAK,GAChBhM,AA9CW,IAAI,CA8CJ8R,SAAS,GAChBd,EAAehF,KAAK,CAAChM,AA/Cd,IAAI,CA+CqB8R,SAAS,CAAC,CAE1C9R,AAjDO,IAAI,CAiDA+Q,cAAc,CAAG/Q,AAjDrB,IAAI,CAiD4B6Q,SAAS,CAAGG,EAAehF,KAAK,CAAChM,AAjDjE,IAAI,CAiDwE8R,SAAS,CAAC,EAG7F7E,GApDO,IAAI,CAoD8B,iBAAkB,CAAEhmB,OAAQ+Y,AApD9D,IAAI,CAoDqEoC,qBAAqB,AAAC,GACtGsO,EAAaO,WAAW,CAAC,wBAErBD,EAAee,GAAG,EAClBf,EAAee,GAAG,CAAC51B,IAAI,CAxDpB,IAAI,CAwD6Bs0B,EAAYzQ,AAxD7C,IAAI,CAwDoD8Q,kBAAkB,EAEjF9Q,AA1DO,IAAI,CA0DA6Q,SAAS,CAAG,CAAA,EACvB7Q,AA3DO,IAAI,CA2DA+Q,cAAc,CAAG,CAAA,EAC5B/Q,AA5DO,IAAI,CA4DAgR,cAAc,CAAG,SApCpChR,AAxBe,IAAI,CAwBR8Q,kBAAkB,CAAGE,EAAerF,KAAK,CAACxvB,IAAI,CAxB1C,IAAI,CAwBmDs0B,GAElEzQ,AA1BW,IAAI,CA0BJ8Q,kBAAkB,EAAIE,EAAehF,KAAK,EACrDhM,AA3BW,IAAI,CA2BJ8R,SAAS,CAAG,EACvB9R,AA5BW,IAAI,CA4BJgM,KAAK,CAAG,CAAA,EACnBhM,AA7BW,IAAI,CA6BJ+Q,cAAc,CAAG/Q,AA7BjB,IAAI,CA6BwB6Q,SAAS,CAC5CG,EAAehF,KAAK,CAAChM,AA9Bd,IAAI,CA8BqB8R,SAAS,CAAC,GAG9C7E,GAjCW,IAAI,CAiC0B,iBAAkB,CAAEhmB,OAAQ+Y,AAjC1D,IAAI,CAiCiEoC,qBAAqB,AAAC,GACtGsO,EAAaO,WAAW,CAAC,wBACzBjR,AAnCW,IAAI,CAmCJgM,KAAK,CAAG,CAAA,EACnBhM,AApCW,IAAI,CAoCJgR,cAAc,CAAG,KAExBA,EAAee,GAAG,EAClBf,EAAee,GAAG,CAAC51B,IAAI,CAvChB,IAAI,CAuCyBs0B,EAAYzQ,AAvCzC,IAAI,CAuCgD8Q,kBAAkB,IAyB7F,CAaAN,2BAA2BwB,CAAU,CAAEC,CAAS,CAAE,CAC1C,IAAI,CAAClB,cAAc,EACnB,IAAI,CAACA,cAAc,CAACkB,EAAW,IAAI,CAACnB,kBAAkB,CAE9D,CAiBAzB,gBAAgB9S,CAAM,CAAEsI,CAAM,CAAE,CAkC5B,OAjCA0I,GAA8BhR,EAAQ,CAACrM,EAAOgiB,KAC1C,IAAMC,EAAcC,WAAWliB,GAAQwB,EAAOwgB,EAAM7S,KAAK,CAAC,KAAMgT,EAAa3gB,EAAKpS,MAAM,CAAG,EAQ3F,IANI8tB,GAA4B+E,IAC3BjiB,EAAMgJ,KAAK,CAAC,WACZgZ,EAAMhZ,KAAK,CAAC,YACbhJ,CAAAA,EAAQiiB,CAAU,EAGlBjiB,AAAU,cAAVA,EAAuB,CACvB,IAAIuB,EAASoT,EACbnT,EAAKtT,OAAO,CAAC,CAACue,EAAMpc,KAChB,GAAIoc,AAAS,cAATA,GAAwBA,AAAS,gBAATA,EAAwB,CAChD,IAAM2V,EAAW9E,GAAwB9b,CAAI,CAACnR,EAAQ,EAAE,CAAE,GACtD8xB,CAAAA,IAAe9xB,EAEfkR,CAAM,CAACkL,EAAK,CAAGzM,GAETuB,CAAM,CAACkL,EAAK,EAElBlL,CAAAA,CAAM,CAACkL,EAAK,CAAG2V,EAASpZ,KAAK,CAAC,OAC1B,EAAE,CACF,CAAC,CAAA,EAKLzH,EAASA,CAAM,CAACkL,EAAK,CAE7B,CACJ,EACJ,CACJ,GACOkI,CACX,CAMA8I,oBAAqB,CACb,IAAI,CAAC7L,gBAAgB,GACrB,IAAI,CAACA,gBAAgB,CAAC5T,0BAA0B,CAAC,CAAA,GACjD,IAAI,CAAC4T,gBAAgB,CAAG,CAAA,EAEhC,CAaAqN,mBAAmBlyB,CAAU,CAAE,CAC3B,IAAME,EAAUF,EAAWE,OAAO,CAAEo1B,EAAYzE,GAAmB0E,mBAAmB,CAAEC,EAAkBF,EAAUG,aAAa,CAAE5vB,EAAO0qB,GAAwBrwB,EAAQ2F,IAAI,CAAE3F,EAAQ2J,MAAM,EAAI3J,EAAQ2J,MAAM,CAAC,EAAE,EAC/M3J,EAAQ2J,MAAM,CAAC,EAAE,CAAChE,IAAI,CAAE3F,EAAQ0C,MAAM,EAAI1C,EAAQ0C,MAAM,CAAC,EAAE,EAC3D1C,EAAQ0C,MAAM,CAAC,EAAE,CAACiD,IAAI,CAAE,SAAU6vB,EAAe7E,GAAmB8E,sBAAsB,CAACz1B,EAAQsmB,OAAO,CAAC,EAAI,EAAE,CAAEoP,EAAgB,CACnIpP,QAAStmB,EAAQsmB,OAAO,CACxB3gB,KAAMA,CACV,EAoBA,SAASgwB,EAAS5X,CAAM,CAAEzf,CAAG,CAAEs3B,CAAe,CAAEthB,CAAM,CAAEuhB,CAAS,EAC7D,IAAIC,EACAF,GACA/F,GAA2B9R,IAC3ByX,AAA8B,KAA9BA,EAAaluB,OAAO,CAAChJ,IACpB,CAAA,AAACs3B,CAAAA,EAAgBtuB,OAAO,EACrBsuB,EAAgBtuB,OAAO,CAAChJ,EAAG,GAAM,GACjCs3B,CAAe,CAACt3B,EAAI,EACpBs3B,AAAoB,CAAA,IAApBA,CAAuB,IAGvB7F,GAA2BhS,IAC3BzJ,CAAM,CAAChW,EAAI,CAAG,EAAE,CAChByf,EAAO9c,OAAO,CAAC,CAAC80B,EAAa50B,KACpB+uB,GAA4B6F,IAM7BzhB,CAAM,CAAChW,EAAI,CAAC6C,EAAE,CAAG,CAAC,EAClBivB,GAA8B2F,EAAa,CAACC,EAAcC,KACtDN,EAASK,EAAcC,EAAWX,CAAe,CAACh3B,EAAI,CAAEgW,CAAM,CAAChW,EAAI,CAAC6C,EAAE,CAAE7C,EAC5E,IAPAq3B,EAASI,EAAa,EAAGT,CAAe,CAACh3B,EAAI,CAAEgW,CAAM,CAAChW,EAAI,CAAEA,EASpE,IAEK4xB,GAA4BnS,IACjC+X,EAAa,CAAC,EACV/F,GAA2Bzb,IAC3BA,EAAOpU,IAAI,CAAC41B,GACZA,CAAU,CAACx3B,EAAI,CAAG,CAAC,EACnBw3B,EAAaA,CAAU,CAACx3B,EAAI,EAG5BgW,CAAM,CAAChW,EAAI,CAAGw3B,EAElB1F,GAA8BrS,EAAQ,CAACiY,EAAcC,KACjDN,EAASK,EAAcC,EAAW33B,AAAQ,IAARA,EAC9Bs3B,EACAN,CAAe,CAACh3B,EAAI,CAAEw3B,EAAYx3B,EAC1C,IAIIA,AAAQ,WAARA,EACAgW,CAAM,CAAChW,EAAI,CAAG,CACVgxB,GAA0BvR,EAAQje,EAAW4C,MAAM,CAAC,EAAE,CAACI,MAAM,CAAC,EAAE,EAAEozB,QAAQ,GAC1E,OACH,CAEInG,GAA2Bzb,GAChCA,EAAOpU,IAAI,CAAC,CAAC6d,EAAQ4R,GAAgCkG,EAAW9X,GAAQ,EAGxEzJ,CAAM,CAAChW,EAAI,CAAG,CAACyf,EAAQ4R,GAAgCrxB,EAAKyf,GAAQ,CAIpF,CAYA,OAXAqS,GAA8BpwB,EAAS,CAAC+d,EAAQzf,KACxCA,AAAQ,gBAARA,GACAo3B,CAAa,CAACp3B,EAAI,CAAG,CAAC,EACtB8xB,GAA8BpwB,CAAO,CAAC1B,EAAI,CAAE,CAAC63B,EAAYC,KACrDT,EAASQ,EAAYC,EAASd,EAAiBI,CAAa,CAACp3B,EAAI,CAAE83B,EACvE,IAGAT,EAAS5X,EAAQzf,EAAK82B,CAAS,CAACzvB,EAAK,CAAE+vB,EAAep3B,EAE9D,GACOo3B,CACX,CAiBAW,qBAAqB3a,CAAS,CAAEna,CAAK,CAAE,CACnC,IAAIkF,EAAUlF,EAAM6F,MAAM,CAAEkvB,EAAa,EAAE,CAAEC,EAC7C,KAAO9vB,GAAWA,EAAQoL,OAAO,GAC7B0kB,CAAAA,EAAgBn2B,GAAKqG,EAAS,QAAO,GAEjC6vB,CAAAA,EAAaA,EAAW/K,MAAM,CAACgL,EAC1BrU,KAAK,CAAC,KAEN1R,GAAG,CAAC,AAACgP,GAAU,CAACA,EAAM/Y,EAAQ,EAAE,EAGrCA,AADJA,CAAAA,EAAUA,EAAQgX,UAAU,AAAD,IACX/B,KAIpB,OAAO4a,CACX,CAiBArD,gBAAgBvX,CAAS,CAAEna,CAAK,CAAE,CAC9B,IACI+sB,EADEzL,EAAa,IAAI,CAUvB,OARAyT,AAFsC,IAAI,CAACD,oBAAoB,CAAC3a,EAAWna,GAEhEN,OAAO,CAAC,AAAC4P,IACZgS,EAAWoO,eAAe,CAACpgB,CAAS,CAAC,EAAE,CAAC,EAAI,CAACyd,GAC7CA,CAAAA,EAAW,CACPvnB,OAAQ8b,EAAWoO,eAAe,CAACpgB,CAAS,CAAC,EAAE,CAAC,CAChD/G,OAAQ+G,CAAS,CAAC,EAAE,AACxB,CAAA,CAER,GACOyd,CACX,CAQApjB,OAAOlL,CAAO,CAAE,CACZ,IAAI,CAACA,OAAO,CAAGmwB,GAAyB,CAAA,EAAM,IAAI,CAACnwB,OAAO,CAAEA,GAC5D,IAAI,CAACw2B,YAAY,GACjB,IAAI,CAAC5F,UAAU,EACnB,CAOA4F,cAAe,CACX,IAAI,CAAC5D,cAAc,CAAC3xB,OAAO,CAAC,AAACw1B,GAAaA,IAC9C,CAKAn1B,SAAU,CACN,IAAI,CAACk1B,YAAY,EACrB,CACJ,CAOA7F,GAAmB0E,mBAAmB,CAAG,CAGrCE,cAAe,CACX/yB,aAAc,CAAC,QAAS,SAAU,kBAAkB,CACpDE,OAAQ,CAAC,QAAQ,CACjBC,MAAO,CAAC,QAAQ,CAChBoI,MAAO,CAAC,WAAY,QAAQ,CAC5B2rB,WAAY,CAAC,OAAQ,cAAe,SAAS,CAC7CC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDzlB,aAAc,CAAC,OAAQ,cAAe,SAAS,CAC/CxH,OAAQ,CAAC,OAAQ,cAAe,SAAS,CACzCktB,KAAM,CAAC,cAAe,SAAS,CAC/BzI,iBAAkB,CAAC,CAAA,EAAK,CACxBpV,UAAW,CAAC,OAAQ,cAAe,SAAS,CAC5CqZ,WAAY,CAAC,cAAe,SAAS,CACrCF,WAAY,CAAC,cAAe,SAAS,AACzC,EAEAxc,OAAQ,CAAC,SAAS,CAClBsY,QAAS,CAAC,SAAS,CACnB6I,aAAc,EAAE,CAChBn0B,MAAO,CAAC,eAAe,CAEvBo0B,QAAS,CAAC,aAAc,aAAc,aAAa,CAEnDC,UAAW,EAAE,CACbC,OAAQ,CAAC,aAAc,OAAQ,SAAS,CACxCC,UAAW,CAAC,kBAAmB,kBAAkB,CACjD7hB,KAAM,CAAC,SAAS,CAEhB8hB,YAAa,EAAE,CACfC,gBAAiB,CAAC,SAAU,eAAe,AAC/C,EAGAzG,GAAmB8E,sBAAsB,CAAG,CACxCvH,UAAW,CAAC,aAAc,aAAc,eAAe,CACvDD,QAAS,CAAC,eAAe,CACzBtY,OAAQ,CAAC,eAAe,AAC5B,EA8CA,IAAM0hB,GAAKj4B,GACXi4B,CAAAA,GAAEtO,UAAU,CAAGsO,GAAEtO,UAAU,EAAIkD,GAC/BoL,GAAE1G,kBAAkB,CAAG0G,GAAE1G,kBAAkB,EA1C0BA,GA2CrE0G,GAAEtO,UAAU,CAAChkB,OAAO,CAACsyB,GAAEC,KAAK,CAAED,GAAE1G,kBAAkB,CAAE0G,GAAEE,OAAO,CAAEF,GAAEG,WAAW,EAa5E,GAAM,CAAEntB,MAAOotB,EAAqB,CAAE,CAAIr4B,GAM1C,OAAMs4B,WAAwBzL,GAM1Bje,kBAAmB,CACf,IAAMhO,EAAU,IAAI,CAACA,OAAO,CAAEiO,EAAgBypB,GAAgBC,kBAAkB,CAAEnG,EAAiB,IAAI,CAACoG,SAAS,CAGjHC,AAHmI73B,CAAAA,EAAQ0C,MAAM,EAC7I1C,EAAQ2J,MAAM,EACd,EAAE,AAAD,EACQ1I,OAAO,CAAC,AAAC62B,IAClBA,EAAM7pB,aAAa,CAAGA,CAAa,CAACujB,EAAe,AACvD,EACJ,CACAphB,MAAO,CACH,IAAMpQ,EAAU,IAAI,CAACA,OAAO,CAC5B,GAAIA,EAAQ2J,MAAM,CAAE,CAChB,OAAO3J,EAAQwC,YAAY,CAC3B,IAAMmD,EAAO3F,EAAQ2J,MAAM,CAAC,EAAE,CAAChE,IAAI,AACnC3F,CAAAA,EAAQ2J,MAAM,CAAC,EAAE,CAACkH,SAAS,CACvB,AAAC7Q,CAAAA,EAAQ2J,MAAM,CAAC,EAAE,CAACkH,SAAS,EAAI,EAAC,EAAK,0BAGtClL,GAAQA,AAAS,SAATA,EACR,IAAI,CAACiyB,SAAS,CAAGjyB,EAGjB,IAAI,CAACiyB,SAAS,CAAG,WAEzB,MAEI,OAAO53B,EAAQ2J,MAAM,CACrB,IAAI,CAACiuB,SAAS,CAAG,QAErB,KAAK,CAACxnB,KAAKxL,KAAK,CAAC,IAAI,CAAEE,UAC3B,CACJ,CAMA4yB,GAAgBC,kBAAkB,CAAG,CACjCh1B,MAAO,CAAC,CACAiI,OAAQ,gBACRF,WAAY,SAAUtD,CAAM,EACxB,GAAI,CAACA,EAAOjH,OAAO,CAACwU,MAAM,CACtB,MAAO,CACH3R,EAAG,EACH6E,EAAG,IACP,EAEJ,IAAMC,EAAK6H,AA7xKiCnE,EA8xKvCI,aAAa,CAACxE,EAAOtE,MAAM,CAAC,EAAE,EACnC,MAAO,CACHE,EAAG8E,EAAG9E,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EACtChD,EAAGC,EAAGD,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CAC3C,CACJ,EAEA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAMU,EAAK,IAAI,CAACsB,sBAAsB,CAAC1C,GACvCU,EAAOwI,cAAc,CAAC9H,EAAG9E,CAAC,CAAE8E,EAAGD,CAAC,EAChCT,EAAOtH,UAAU,CAACF,WAAW,CAAC8C,MAAM,CAAC,EAAE,CAACgJ,KAAK,CACzCtE,EAAOpH,OAAO,CAAC0L,KAAK,CACxBtE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC+K,OAAQ,SACRF,WAAY,SAAUtD,CAAM,SACxB,AAAKA,EAAOjH,OAAO,CAACwU,MAAM,CAMnB,CACH3R,EAAGoE,EAAOjH,OAAO,CAAC+Y,SAAS,CAAClW,CAAC,CACzB,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAChChD,EAAGT,EAAOjH,OAAO,CAAC+Y,SAAS,CAACrR,CAAC,CACzB,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACrC,EAVW,CACH9H,EAAG,EACH6E,EAAG,IACP,CAQR,EAGAd,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAMU,EAAK,IAAI,CAACsB,sBAAsB,CAAC1C,GACvCU,EAAOsC,SAAS,CAAC5B,EAAG9E,CAAC,CAAE8E,EAAGD,CAAC,EAC3BT,EAAOtH,UAAU,CAACF,WAAW,CAAC8C,MAAM,CAAC,EAAE,CAACgJ,KAAK,CACzCtE,EAAOpH,OAAO,CAAC0L,KAAK,CACxBtE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,CACNquB,UAAW,CAAC,CACJxjB,WAAY,SAAU5K,CAAU,EAC5B,IAAMgI,EAAK6H,AA50KiCnE,EA60KvCI,aAAa,CAAC9L,EAAWgD,MAAM,CAAC,EAAE,EACvC,MAAO,CACHE,EAAG8E,EAAG9E,CAAC,CAAG,EACV6E,EAAGC,EAAGD,CAAC,CAAG,CACd,CACJ,EACAd,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgtB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAEunB,eAAe/nB,GAAI5D,EAASsE,EAAOpH,OAAO,CAAC8C,MAAM,CAAE6G,EAAS7J,EAAWF,WAAW,CAAC+J,MAAM,CAAE1G,EAAanD,EAAWupB,SAAS,EAAEjmB,OAAS,EAAGiU,EAAavX,EAAWwpB,SAAS,EAAElmB,OAAS,EACzP,GAAI0pB,EAAQ,CACR,IAAM9pB,EAAI8pB,EAAO3pB,KAAK,CAACF,EAAW,CAAC8P,KAAK,CAAElL,EAAIilB,EAAOnhB,KAAK,CAAC0L,EAAW,CAACtE,KAAK,AAE5EjQ,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EAEdF,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EACdF,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGA,EAEd/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGA,EACV8B,GAAUA,CAAM,CAAC,EAAE,EACnBA,CAAAA,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAGsE,EAAOpH,OAAO,CAAC8C,MAAM,AAAD,CAE/C,CACAhD,EAAWD,MAAM,CAAC,CAAA,EACtB,CACJ,CACJ,EAAE,CACN8V,OAAQ,CAAC,CACDjL,WAAY,SAAUtD,CAAM,EACxB,IAAMU,EAAK6H,AAz2KiCnE,EAy2KXI,aAAa,CAACxE,EAAOtE,MAAM,CAAC,EAAE,EAAG4S,EAAItO,EAAOpH,OAAO,CAAC0V,CAAC,CACtF,MAAO,CACH1S,EAAG8E,EAAG9E,CAAC,CAAG0S,EAAIhS,KAAK0J,GAAG,CAAC1J,KAAKuT,EAAE,CAAG,GAC7B,AAAC,CAAA,IAAI,CAAC9W,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAChChD,EAAGC,EAAGD,CAAC,CAAG6N,EAAIhS,KAAK2J,GAAG,CAAC3J,KAAKuT,EAAE,CAAG,GAC7B,AAAC,CAAA,IAAI,CAAC9W,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACrC,CACJ,EACA/D,OAAQ,CAGJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEoU,EAAW,IAAI,CAAC9K,sBAAsB,CAAC1C,GAAIiD,EAAS7J,EAAWF,WAAW,CAAC+J,MAAM,CACvHvC,EAAOwO,SAAS,CAAClS,KAAKC,GAAG,CAACyD,EAAOpH,OAAO,CAAC0V,CAAC,CACtCxB,EAASrM,CAAC,CACNnE,KAAK2J,GAAG,CAAC3J,KAAKuT,EAAE,CAAG,GAAI,IAC3BtN,GAAUA,CAAM,CAAC,EAAE,GACnBA,CAAM,CAAC,EAAE,CAAC+L,CAAC,CAAGtO,EAAOpH,OAAO,CAAC0V,CAAC,CAC9B/L,CAAM,CAAC,EAAE,CAAC+B,KAAK,CAAGtE,EAAOpH,OAAO,CAAC0L,KAAK,EAE1CtE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,CACNouB,QAAS,CAAC,CACFvjB,WAAY,SAAUtD,CAAM,EACxB,IAAM8M,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAC5D,MAAO,CACHE,EAAGkR,EAASlR,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGqM,EAASrM,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACjD,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAM8M,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAC5DsE,EAAOwI,cAAc,CAAClJ,EAAEmC,MAAM,CAAGqL,EAASlR,CAAC,CAAE0D,EAAEiC,MAAM,CAAGuL,EAASrM,CAAC,CAAE,GACpET,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC6K,WAAY,SAAUtD,CAAM,EACxB,IAAM8M,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAC5D,MAAO,CACHE,EAAGkR,EAASlR,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGqM,EAASrM,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACjD,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAM8M,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAC5DsE,EAAOwI,cAAc,CAAClJ,EAAEmC,MAAM,CAAGqL,EAASlR,CAAC,CAAE0D,EAAEiC,MAAM,CAAGuL,EAASrM,CAAC,CAAE,GACpET,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC6K,WAAY,SAAUtD,CAAM,EACxB,IAAM8M,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAAG2T,EAAYrP,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAAG4N,EAAQtJ,EAAOoP,QAAQ,CAACtC,EAAUuC,GAC3J,MAAO,CACHzT,EAAG0N,EAAMtI,EAAE,CAAG,AAAC,CAAA,IAAI,CAACjI,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EACtC6F,EAAMwG,EAAE,CAAGxT,KAAK2J,GAAG,CAAC,AAACqD,EAAMqG,KAAK,CAAGrT,KAAKuT,EAAE,CAAI,KAClDpP,EAAG6I,EAAMrI,EAAE,CAAG,AAAC,CAAA,IAAI,CAAClI,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,EACvC4F,EAAMwG,EAAE,CAAGxT,KAAK0J,GAAG,CAAC,AAACsD,EAAMqG,KAAK,CAAGrT,KAAKuT,EAAE,CAAI,IACtD,CACJ,EACAlQ,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAM8M,EAAW9M,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAAG2T,EAAYrP,EAAOkQ,mBAAmB,CAAClQ,EAAOtE,MAAM,CAAC,EAAE,EAAGmsB,EAAO7nB,EAAO6O,mBAAmB,CAAC/B,EAAUuC,EAAW/P,EAAEmC,MAAM,CAAEnC,EAAEiC,MAAM,EAAGgD,EAAQvE,EAAOgQ,QAAQ,GAAI8X,EAAQxrB,KAAK4S,GAAG,CAAC3K,EAAMsB,OAAO,CAAC,GAAKtB,EAAMsB,OAAO,CAACgiB,IAClR7nB,EAAOsQ,UAAU,CAACwX,GAClB9nB,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,AACV,EACA63B,GAAgB54B,SAAS,CAACmqB,cAAc,CAAGwO,GAAsBxL,GAAuBntB,SAAS,CAACmqB,cAAc,CAAE,CAAC,GACnHgD,GAAuBvmB,KAAK,CAAC0xB,eAAe,CAAGM,GAmB/C,GAAM,CAAErtB,MAAO2tB,EAAiB,CAAE,CAAI54B,GAMtC,OAAM64B,WAAoBhM,GAUtB7C,aAAc,CACV,IAAI,CAACC,SAAS,CAAG,IAAI,CAAC9oB,KAAK,CAAC4C,KAAK,CAAC,IAAI,CAACnD,OAAO,CAACmuB,WAAW,CAAChrB,KAAK,CAAC,CACjE,IAAI,CAACmmB,SAAS,CAAG,IAAI,CAAC/oB,KAAK,CAACoL,KAAK,CAAC,IAAI,CAAC3L,OAAO,CAACmuB,WAAW,CAACxiB,KAAK,CAAC,AACrE,CACAgD,kBAAmB,CACf,IAAMwf,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAC5C,MAAO,AAACA,CAAAA,EAAYrrB,MAAM,EAAI,EAAE,AAAD,EAAG0N,GAAG,CAAC,AAACzB,IACnCA,EAAa5L,KAAK,CAAGgrB,EAAYhrB,KAAK,CACtC4L,EAAapD,KAAK,CAAGwiB,EAAYxiB,KAAK,CAC/BoD,GAEf,CACAmpB,yBAA0B,CACtB,OAAO,IAAI,CAACvpB,gBAAgB,EAChC,CACAX,kBAAmB,CACf,IAAI,CAACkqB,uBAAuB,GAAGj3B,OAAO,CAAC,SAAU8N,CAAY,CAAE5N,CAAC,EAC5D,IAAMuN,EAAe,IAAInE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAEy3B,GAAkB,IAAI,CAACh4B,OAAO,CAACmO,mBAAmB,CAAEY,EAAaL,YAAY,EAAGvN,GACpJ,IAAI,CAAC8M,aAAa,CAAC/N,IAAI,CAACwO,GACxBK,EAAaL,YAAY,CAAGA,EAAa1O,OAAO,AACpD,EAAG,IAAI,CACX,CACA2pB,WAAY,CACR,IAAMwE,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAAEvkB,EAAQ,IAAI,CAACggB,SAAS,CAACoO,GAAkB7J,EAAY0I,IAAI,CAAE,CACrGlxB,KAAM,OACNkL,UAAW,2BACX/N,OAAQ,IAAI,CAACA,MAAM,CAAC0N,GAAG,CAAC,CAACf,EAAQtO,IAAO,SAAUiG,CAAM,EACpD,OAAOA,EAAOtH,UAAU,CAACgD,MAAM,CAAC3B,EAAE,AACtC,EACJ,GAAI,EACJgtB,CAAAA,EAAY0I,IAAI,CAAGjtB,EAAM5J,OAAO,AACpC,CACJ,CACAi4B,GAAYn5B,SAAS,CAACmqB,cAAc,CAAG+O,GAAkB/L,GAAuBntB,SAAS,CAACmqB,cAAc,CAUxG,CAYIkF,YAAa,CAMThrB,MAAO,EAMPwI,MAAO,EA2BPkrB,KAAM,CACFvjB,KAAM,MACV,CACJ,EAIAnF,oBAAqB,CACjBzD,WAAY,SAAUtD,CAAM,EACxB,IAAMjH,EAAU,IAAI,CAACA,OAAO,CAAE2H,EAAK6H,AA7jLanE,EA6jLSI,aAAa,CAACxE,EAAOtE,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,EAChG,MAAO,CACHJ,EAAG8E,EAAG9E,CAAC,CAAG,AAAC7C,CAAAA,EAAQ0K,KAAK,EAAI,CAAA,EAAK,EACjChD,EAAGC,EAAGD,CAAC,CAAG,AAAC1H,CAAAA,EAAQ2K,MAAM,EAAI,CAAA,EAAK,CACtC,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,GAAIA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC7FC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAAIynB,EAAc/mB,EAAOpH,OAAO,CAACmuB,WAAW,CAC5F/mB,EAAOwI,cAAc,CAACnG,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,CAAE,IAAI,CAACzE,KAAK,EAE9D+qB,EAAYrrB,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACJ,CAAC,CAC5BoE,EAAOtE,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACJ,CAAC,CAC/BmrB,EAAYrrB,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACyE,CAAC,CAC5BT,EAAOtE,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACyE,CAAC,CAC/BT,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,CACJ,CACJ,GACAosB,GAAuBvmB,KAAK,CAACyxB,WAAW,CAAGc,GAMd,IAAME,GAAqBF,GAYlD,CAAE5tB,MAAO+tB,EAAiB,CAAE,CAAIh5B,GAMtC,OAAMi5B,WAAoBF,GAMtB3O,WAAY,CACR,IAAI,CAAC7a,gBAAgB,GAAG1N,OAAO,CAAC,CAACyK,EAAOvK,KACpC,IAAMgtB,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAAExrB,EAAQ,IAAI,CAAC+mB,SAAS,CAAC0O,GAAkB1sB,EAAM/I,KAAK,CAAE,CAChGC,KAAMurB,EAAYzrB,MAAM,CAACvB,EAAE,CAC3BuK,MAAO,SAAUtE,CAAM,EACnB,OAAOA,EAAOtH,UAAU,CAACgD,MAAM,CAAC3B,EAAE,AACtC,CACJ,GAAI,CAAA,EACJuK,CAAAA,EAAM/I,KAAK,CAAGA,EAAM3C,OAAO,AAC/B,EACJ,CACJ,CACAq4B,GAAYv5B,SAAS,CAACmqB,cAAc,CAAGmP,GAAkBD,GAAkBr5B,SAAS,CAACmqB,cAAc,CAWnG,CACIkF,YAAa,CAQTzrB,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAM,CAClDm0B,KAAM,CACFxiB,YAAa,CACjB,CACJ,EACA7R,aAAc,CACVqW,MAAO,SACP+S,aAAc,CAAA,EACdhR,KAAM,CAAA,EACND,SAAU,OACVhV,KAAM,OACN+T,gBAAiB,OACjBoB,YAAa,EACbjT,EAAG,EACP,CACJ,GACAokB,GAAuBvmB,KAAK,CAAC4yB,WAAW,CAAGD,GAoB3C,GAAM,CAAEhuB,MAAOkuB,EAAY,CAAE,CAAIn5B,GAiBjC,OAAMo5B,WAAeL,GAMjBxpB,kBAAmB,CACf,IAAMG,EAAgBqpB,GAAkBr5B,SAAS,CAAC6P,gBAAgB,CAAC3P,IAAI,CAAC,IAAI,EAAGqY,EAAa,IAAI,CAACrX,OAAO,CAACmuB,WAAW,CAACxiB,KAAK,EAAI,EAAGA,EAAQ,IAAI,CAACpL,KAAK,CAACoL,KAAK,CAAC0L,EAAW,CAIrK,GAHAvI,CAAa,CAAC,EAAE,CAAG,IAAI,CAAC2pB,kBAAkB,CAAC3pB,CAAa,CAAC,EAAE,EAC3DA,CAAa,CAAC,EAAE,CAAG,IAAI,CAAC2pB,kBAAkB,CAAC3pB,CAAa,CAAC,EAAE,EAEvDnD,GAASA,EAAM+sB,WAAW,CAAE,CAE5B,IAAMtgB,EAAIzM,EAAMmB,QAAQ,CAACgC,CAAa,CAAC,EAAE,CAACjH,CAAC,EACvC8D,EAAMmB,QAAQ,CAACgC,CAAa,CAAC,EAAE,CAACjH,CAAC,EAErC8wB,EAAKhtB,EAAMmB,QAAQ,CAACgC,CAAa,CAAC,EAAE,CAACjH,CAAC,EAAIuQ,CAE1CtJ,CAAAA,CAAa,CAAC,EAAE,CAACjH,CAAC,CAAG8D,EAAMsB,OAAO,CAAC0rB,EACvC,CACA,OAAO7pB,CACX,CACAopB,yBAA0B,CACtB,OAAO,IAAI,CAACvpB,gBAAgB,GAAG9J,KAAK,CAAC,EAAG,EAC5C,CACA4zB,mBAAmB1pB,CAAY,CAAE,CAC7B,IAAM0pB,EAAqBF,GAAaxpB,GAAeof,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAE7F,OADAsK,EAAmB5wB,CAAC,EAAIsmB,EAAYrjB,MAAM,CACnC2tB,CACX,CACAzqB,kBAAmB,CACfmqB,GAAkBr5B,SAAS,CAACkP,gBAAgB,CAAChP,IAAI,CAAC,IAAI,EACtD,IAAMgB,EAAU,IAAI,CAACA,OAAO,CAAEmuB,EAAcnuB,EAAQmuB,WAAW,CAAEzf,EAAe,IAAInE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAEg4B,GAAav4B,EAAQmO,mBAAmB,CAAEggB,EAAYyK,kBAAkB,EAAG,GAC1M,IAAI,CAAC3qB,aAAa,CAAC/N,IAAI,CAACwO,GACxByf,EAAYyK,kBAAkB,CAAGlqB,EAAa1O,OAAO,AACzD,CACA2pB,WAAY,CACR,IAAI,CAACkP,OAAO,GACZ,IAAI,CAACC,aAAa,EACtB,CACAD,SAAU,CACN,IAAMhC,EAAO,IAAI,CAACjN,SAAS,CAAC2O,GAAa,IAAI,CAACv4B,OAAO,CAACmuB,WAAW,CAAC0I,IAAI,CAAE,CACpElxB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd,IAAI,CAACA,MAAM,CAAC,EAAE,CACd,SAAUsE,CAAM,EACZ,IAAM2H,EAAeY,AAvvLmBnE,EAuvLGc,cAAc,CAAClF,EAAOtH,UAAU,CAACgD,MAAM,CAAC,EAAE,EAErF,OADAiM,EAAatC,OAAO,CAAG,IAChBsC,CACX,EACA,IAAI,CAACjM,MAAM,CAAC,EAAE,CACjB,CACD+N,UAAW,yBACf,GAAI,EACJ,CAAA,IAAI,CAAC7Q,OAAO,CAACmuB,WAAW,CAAC0I,IAAI,CAAGA,EAAK72B,OAAO,AAChD,CACA84B,eAAgB,CACZ,IAAMpC,EAAa,IAAI,CAAC9M,SAAS,CAAC2O,GAAa,IAAI,CAACv4B,OAAO,CAACmuB,WAAW,CAACuI,UAAU,CAAE,CAChF/wB,KAAM,OACN7C,OAAQ,IAAI,CAACA,MAAM,CAAC+B,KAAK,GACzBgM,UAAW,8BACf,GAAI,EACJ,CAAA,IAAI,CAAC7Q,OAAO,CAACmuB,WAAW,CAACuI,UAAU,CAAGA,EAAW12B,OAAO,AAC5D,CAWA+4B,cAAcnwB,CAAE,CAAEF,CAAE,CAAEksB,CAAG,CAAE,CACvB,IAAMoE,EAAWC,OAAOrE,GACxB,IAAI,CAAChlB,cAAc,CAAChH,EAAIF,EAAIswB,GAC5B,IAAI,CAACppB,cAAc,CAAChH,EAAIF,EAFoBswB,AAAa,IAAbA,EAAiB,EAAI,EAGrE,CAOAE,gBAAgBC,CAAE,CAAE,CAChB,IAAI,CAACvpB,cAAc,CAAC,EAAGupB,EAAI,GAC3B,IAAI,CAACvpB,cAAc,CAAC,EAAGupB,EAAI,GAC3B,IAAI,CAACn5B,OAAO,CAACmuB,WAAW,CAACrjB,MAAM,CAAG,IAAI,CAAChI,MAAM,CAAC,EAAE,CAAC+E,CAAC,CAC9C,IAAI,CAAC/E,MAAM,CAAC,EAAE,CAAC+E,CAAC,CACpB,IAAI,CAACjI,WAAW,CAACuuB,WAAW,CAACrjB,MAAM,CAAG,IAAI,CAAC9K,OAAO,CAACmuB,WAAW,CAACrjB,MAAM,AACzE,CACJ,CACA0tB,GAAO15B,SAAS,CAACmqB,cAAc,CAAGsP,GAAaJ,GAAkBr5B,SAAS,CAACmqB,cAAc,CAUzF,CACIkF,YAAa,CAQTuI,WAAY,CACRpjB,KAAM,2BACNe,YAAa,CACjB,EACAwiB,KAAM,CACFxiB,YAAa,CACjB,EAIAvJ,OAAQ,GAQR8tB,mBAAoB,CAChBluB,WAAY,SAAUtD,CAAM,EACxB,IAAMgyB,EAAUzpB,AA70L4BnE,EA60LNI,aAAa,CAACxE,EAAOtE,MAAM,CAAC,EAAE,EAAGu2B,EAAQ1pB,AA70LnCnE,EA60LyDI,aAAa,CAACxE,EAAOtE,MAAM,CAAC,EAAE,EAAGE,EAAI,AAACo2B,CAAAA,EAAQp2B,CAAC,CAAGq2B,EAAMr2B,CAAC,AAADA,EAAK,EAClK,MAAO,CACHA,EAAGA,EAAI,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EACnChD,EAAGyxB,AA/IX9pB,CAAAA,AA+IwC6pB,EA/IrCxxB,CAAC,CAAG0H,AA+IwB6pB,EA/IrBvxB,CAAC,AAADA,EAAM2H,CAAAA,AA+IwB6pB,EA/IrBr2B,CAAC,CAAGuM,AA+IQ6pB,EA/ILp2B,CAAC,AAADA,EAAMA,CAAAA,AA+IeA,EA/IXuM,AA+IL6pB,EA/IQp2B,CAAC,AAADA,EAAKuM,AA+Ib6pB,EA/IgBvxB,CAAC,CAgJpC,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACrC,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACjBA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC7FC,gBAAiB,CAAA,CACrB,KACIpC,EAAO8xB,eAAe,CAAC,IAAI,CAAC9vB,sBAAsB,CAAC1C,GAAGmB,CAAC,EACvDT,EAAOvH,MAAM,CAAC,CAAA,GAEtB,CACJ,CACJ,CACJ,EAKAsO,oBAAqB,CACjBpH,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,GAAIA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC7FC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAChDU,EAAO2xB,aAAa,CAACtvB,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,CAAE,CAAC,CAAC,IAAI,CAACzE,KAAK,EAC/DgE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,CACJ,CACJ,GACAosB,GAAuBvmB,KAAK,CAACuxB,MAAM,CAAGuB,GAMT,IAAMe,GAAgBf,GAa7C,CAAEnuB,MAAOmvB,EAAkB,CAAE,CAAIp6B,GAMvC,OAAMq6B,WAAqBtB,GAMvB,OAAOuB,UAAUC,CAAU,CAAEC,CAAQ,CAAE,CACnC,OAAO,SAAUxyB,CAAM,EACnB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAE6F,EAAO7F,EAAWE,OAAO,CAACmuB,WAAW,CAACxoB,IAAI,CAC5E7C,EAAShD,EAAWgD,MAAM,CAe9B,MAdI6C,CAAAA,AAAS,mBAATA,GAA6BA,AAAS,iBAATA,CAAsB,GAGnD7C,CAAAA,EAAS,CACLA,CAAM,CAAC,EAAE,CACT,IA15LwC0I,EA05Ld1L,EAAWS,KAAK,CAAEuC,CAAM,CAAC,EAAE,CAACsE,MAAM,CAAE,CAE1DpE,EAAGF,CAAM,CAAC,EAAE,CAACE,CAAC,EAAG,CAAE2C,CAAAA,AAAS,mBAATA,CAAwB,EAC3CkC,EAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,EAAG,CAAElC,CAAAA,AAAS,iBAATA,CAAsB,EACzCxC,MAAOL,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAACmD,KAAK,CAC9BwI,MAAO7I,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAAC2L,KAAK,AAClC,GACH,AAAD,EAEG8tB,GAAaI,aAAa,CAAC/2B,CAAM,CAAC62B,EAAW,CAAE72B,CAAM,CAAC82B,EAAS,CAC1E,CACJ,CACA,OAAOE,mBAAmBC,CAAU,CAAEC,CAAW,CAAEvsB,CAAI,CAAEwsB,CAAwB,CAAE,CAC/E,IAAMC,EAAezsB,AAAS,MAATA,EAAe,IAAM,IAG1C,MAAQ,AAACusB,CAAAA,CAAW,CAACvsB,EAAK,CAAGssB,CAAU,CAACtsB,EAAK,AAAD,EACvCwsB,CAAAA,EAA2BF,CAAU,CAACG,EAAa,AAAD,EAClDF,CAAAA,CAAW,CAACE,EAAa,CAAGH,CAAU,CAACG,EAAa,AAAD,EACpDH,CAAU,CAACtsB,EAAK,AACxB,CACA,OAAOosB,cAAcE,CAAU,CAAEC,CAAW,CAAE,CAC1C,IAIIG,EAAYC,EAAYC,EAJtB95B,EAAQw5B,EAAW72B,MAAM,CAAC3C,KAAK,CAAE4C,EAAQ42B,EAAW72B,MAAM,CAACC,KAAK,CAAEwI,EAAQquB,EAAY92B,MAAM,CAACyI,KAAK,CAAE2uB,EAAmB3qB,AAh7LzEnE,EAg7L+FI,aAAa,CAACmuB,GAAaQ,EAAoB5qB,AAh7L9InE,EAg7LoKI,aAAa,CAACouB,GAAcQ,EAASD,EAAkBv3B,CAAC,CAAGs3B,EAAiBt3B,CAAC,CAAEy3B,EAASF,EAAkB1yB,CAAC,CAAGyyB,EAAiBzyB,CAAC,CAAE6yB,EAAWv3B,EAAM6mB,IAAI,CAAE2Q,EAAWD,EAAWv3B,EAAM0H,KAAK,CAAE+vB,EAAWjvB,EAAM8a,GAAG,CAAEoU,EAAWD,EAAWjvB,EAAMb,MAAM,CAAEgwB,EAASN,EAAS,EAAIE,EAAWC,EAAUI,EAASN,EAAS,EAAIG,EAAWC,EAAUnB,EAAY,CAC5iB12B,EAAGw3B,AAAW,IAAXA,EAAeF,EAAiBt3B,CAAC,CAAG83B,EACvCjzB,EAAG4yB,AAAW,IAAXA,EAAeH,EAAiBzyB,CAAC,CAAGkzB,CAC3C,EAqBA,OAnBe,IAAXP,GAAgBC,AAAW,IAAXA,IAChBL,EAAaX,GAAaK,kBAAkB,CAACQ,EAAkBC,EAAmB,IAAKO,GACvFX,EAAaV,GAAaK,kBAAkB,CAACQ,EAAkBC,EAAmB,IAAKQ,GACnFX,GAAcQ,GAAYR,GAAcS,GACxCnB,EAAU12B,CAAC,CAAG83B,EACdpB,EAAU7xB,CAAC,CAAGuyB,IAGdV,EAAU12B,CAAC,CAAGm3B,EACdT,EAAU7xB,CAAC,CAAGkzB,IAGtBrB,EAAU12B,CAAC,EAAIzC,EAAM+I,QAAQ,CAC7BowB,EAAU7xB,CAAC,EAAItH,EAAMgJ,OAAO,CACxBwwB,EAAW72B,MAAM,CAAC3C,KAAK,CAACwI,QAAQ,GAChCsxB,EAAOX,EAAU12B,CAAC,CAClB02B,EAAU12B,CAAC,CAAG02B,EAAU7xB,CAAC,CACzB6xB,EAAU7xB,CAAC,CAAGwyB,GAEXX,CACX,CAMA/P,WAAY,CACR,IAAMwE,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAAErrB,EAAS,CACnD,IAAI,CAACA,MAAM,CAAC,EAAE,CACd22B,GAAauB,YAAY,CAC5B,CAKG7M,EAAYxoB,IAAI,CAACoW,KAAK,CAAC,WACvBjZ,CAAAA,CAAM,CAAC,EAAE,CAAG22B,GAAawB,cAAc,AAAD,EAE1C,IAAMpE,EAAO,IAAI,CAACjN,SAAS,CAAC4P,GAAmBrL,EAAY0I,IAAI,CAAE,CAC7DlxB,KAAM,OACN7C,OAAQA,EACR+N,UAAW,2BACf,GAAI,EACJsd,CAAAA,EAAY0I,IAAI,CAAGA,EAAK72B,OAAO,AACnC,CACJ,CAMAy5B,GAAauB,YAAY,CAAGvB,GAAaC,SAAS,CAAC,EAAG,GACtDD,GAAawB,cAAc,CAAGxB,GAAaC,SAAS,CAAC,EAAG,GACxDD,GAAa36B,SAAS,CAACmqB,cAAc,CAAGuQ,GAAmBrB,GAAkBr5B,SAAS,CAACmqB,cAAc,CAAE,CAAC,GACxGgD,GAAuBvmB,KAAK,CAACw1B,YAAY,CAAGzB,GAMf,IAAM0B,GAAsB1B,GA+BnD,CAAEpvB,MAAO+wB,EAAgB,CAAEpjB,SAAUqjB,EAAmB,CAAEx1B,QAASy1B,EAAkB,CAAE,CAAIl8B,GA8CjG,OAAMm8B,WAAmBpD,GAMrB/nB,KAAKtQ,CAAU,CAAEF,CAAW,CAAEwD,CAAK,CAAE,CAC7Bk4B,GAAmB17B,EAAY+L,KAAK,GACpC/L,EAAYkD,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACxBA,EAAMC,KAAK,CAAG/L,EAAY+L,KAAK,AACnC,GAEA2vB,GAAmB17B,EAAYuD,KAAK,GACpCvD,EAAYkD,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACxBA,EAAMvI,KAAK,CAAGvD,EAAYuD,KAAK,AACnC,GAEJ,KAAK,CAACiN,KAAKtQ,EAAYF,EAAawD,EACxC,CACAo4B,SAAU,CACN,IAAI,CAAC7xB,MAAM,CAAC,EAAE,CAAC3J,OAAO,CAAC7B,CAAC,CAAG,IAAI,CAACs9B,OAAO,EAC3C,CACAA,SAAU,CACN,MAAO,EAxDH,IAwDoB,IAAI,CAACC,MAAM,CAAE,IAAI,CAAC7zB,CAAC,EAAE,CAAC0jB,MAAM,CAACoQ,AA5C7D,SAAuBC,CAAa,CAAEC,CAAe,CAAEH,CAAM,CAAE7zB,CAAC,EAC5D,IAAM0M,EAAO,EAAE,CACf,IAAK,IAAIpT,EAAI,EAAGA,GAAK06B,EAAiB16B,IAClCoT,EAAKrU,IAAI,CAAC,CACN,IACA07B,EAAgB,EAChBA,EAAgB,EAChB,EACA,EACA,EACAF,EAASv6B,EAAIy6B,EACb/zB,EACH,EAEL,OAAO0M,CACX,EA6B2E,IAAI,CAACqnB,aAAa,CAAE,IAAI,CAACC,eAAe,CAAE,IAAI,CAACH,MAAM,CAAE,IAAI,CAAC7zB,CAAC,EACpI,CACA8hB,WAAY,CACR,IAAMwE,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAC5C,IAAI,CAAC2N,iBAAiB,GACtB,IAAMlyB,EAAQ,IAAI,CAACggB,SAAS,CAACwR,GAAiBjN,EAAY0I,IAAI,CAAE,CAC5DlxB,KAAM,OACNxH,EAAG,IAAI,CAACs9B,OAAO,GACf34B,OAAQ,IAAI,CAAC9C,OAAO,CAAC8C,MAAM,CAC3B+N,UAAW,6BACf,GAAI,EACJsd,CAAAA,EAAY0I,IAAI,CAAGjtB,EAAM5J,OAAO,AACpC,CACAgO,kBAAmB,CACf,IAAMhO,EAAU,IAAI,CAACA,OAAO,CAAEmuB,EAAcnuB,EAAQmuB,WAAW,AAC/DnuB,CAAAA,EAAQmO,mBAAmB,CAACpD,KAAK,CAACnD,MAAM,CAAG,IAAI,CAACrH,KAAK,CAACwI,QAAQ,CAC1D,YACA,YACJolB,EAAYhgB,mBAAmB,CAAClN,OAAO,CAAC,AAAC8c,IACrC,IAAM7P,EAAuBktB,GAAiBp7B,EAAQmO,mBAAmB,CAAE4P,GACrErP,EAAe,IAAInE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE2N,EAAsB,GAC1F,IAAI,CAACD,aAAa,CAAC/N,IAAI,CAACwO,EAC5B,EACJ,CACAotB,mBAAoB,CAChB,IAAM97B,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAErrB,EAAS9C,EAAQ8C,MAAM,CACjE,GAAI,CAACA,EACD,OAEJ,IAAMoT,EAASpT,CAAM,CAAC,EAAE,CAAEqT,EAASrT,CAAM,CAAC,EAAE,CAAEi5B,EAAc/7B,EAAQmD,KAAK,EAAI,EAAG64B,EAAch8B,EAAQ2L,KAAK,EAAI,EAAGxI,EAAQ,IAAI,CAAC5C,KAAK,CAAC4C,KAAK,CAAC44B,EAAY,CAAEpwB,EAAQ,IAAI,CAACpL,KAAK,CAACoL,KAAK,CAACqwB,EAAY,CAAEC,EAAU/lB,EAAOlT,CAAC,CAAEk5B,EAAShmB,EAAOrO,CAAC,CAAEs0B,EAAUhmB,EAAOnT,CAAC,CACzP,GAAI,CAACi5B,GAAW,CAACE,EACb,OAEJ,IAAMt0B,EAAIwzB,GAAoBa,GAC1BvwB,EAAMmB,QAAQ,CAACovB,GACfvwB,EAAM8a,GAAG,CAAG9a,EAAMb,MAAM,CAAE9H,EAAIq4B,GAAoBY,GAAW94B,EAAM2J,QAAQ,CAACmvB,GAAW94B,EAAM6mB,IAAI,CAAEpT,EAAKykB,GAAoBc,GAAWh5B,EAAM2J,QAAQ,CAACqvB,GAAWh5B,EAAM6mB,IAAI,CAAG,GAAIoS,EAAcj5B,EAAM0J,GAAG,CAAE+uB,EAAgBl4B,KAAKkV,KAAK,CAAClV,KAAKC,GAAG,CAACD,KAAK4S,GAAG,CAACM,EAAK5T,GAAI,IAErQ64B,EAAkBn4B,KAAK24B,KAAK,CAACD,EAAcR,GAAiB,EAG5DU,EAAa,AAAC54B,CAAAA,KAAK24B,KAAK,CAAC,AAACr5B,CAAAA,EAAIG,EAAM6mB,IAAI,AAAD,EAAK4R,GAAiB,CAAA,EAAKA,CAClE,CAAA,IAAI,CAACF,MAAM,CAAG14B,EAAIs5B,EAClB,IAAI,CAACz0B,CAAC,CAAGA,EACT,IAAI,CAAC+zB,aAAa,CAAGA,EACrB,IAAI,CAACC,eAAe,CAAGA,CAC3B,CACAh8B,OAAO4K,CAAS,CAAE,CACd,IAAI,CAACqxB,iBAAiB,GACtB,IAAI,CAACN,OAAO,GACZ,KAAK,CAAC37B,OAAO4K,EACjB,CACJ,CACA8wB,GAAWz8B,SAAS,CAACmqB,cAAc,CAAGmS,GAAiBjD,GAAkBr5B,SAAS,CAACmqB,cAAc,CAYjG,CACIkF,YAAa,CAMThgB,oBAAqB,CAAC,CACdzD,WAAY,SAAUtD,CAAM,EACxB,IAAMsE,EAAQtE,EAAOtE,MAAM,CAAC,EAAE,CAC9B,MAAO,CACHE,EAAGkR,AAFoC9M,EAAOyG,MAAM,CAACnC,GAAO8C,gBAAgB,CAEhExL,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGT,EAAOS,CAAC,CAAI,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,CAC1C,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAM8M,EAAW9M,EAAOyG,MAAM,CAACzG,EAAOtE,MAAM,CAAC,EAAE,EAAE0L,gBAAgB,CACjEpH,EAAOwI,cAAc,CAAClJ,EAAEmC,MAAM,CAAGqL,EAASlR,CAAC,CAAE,EAAG,GAChDoE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC6K,WAAY,SAAUtD,CAAM,EACxB,IAAMsE,EAAQtE,EAAOtE,MAAM,CAAC,EAAE,CAC9B,MAAO,CACHE,EAAGkR,AAFoC9M,EAAOyG,MAAM,CAACnC,GAAO8C,gBAAgB,CAEhExL,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGT,EAAOS,CAAC,CAAI,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,CAC1C,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAM8M,EAAW9M,EAAOyG,MAAM,CAACzG,EAAOtE,MAAM,CAAC,EAAE,EAAE0L,gBAAgB,CACjEpH,EAAOwI,cAAc,CAAClJ,EAAEmC,MAAM,CAAGqL,EAASlR,CAAC,CAAE,EAAG,GAChDoE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,AACV,CACJ,GACAosB,GAAuBvmB,KAAK,CAAC62B,UAAU,CAAGhB,GAmB1C,GAAM,CAAElxB,MAAOmyB,EAAe,CAAE,CAAIp9B,IASpC,SAASq9B,GAAqBC,CAAgB,CAAEC,CAAY,EACxD,OAAO,WACH,IAAM78B,EAAa,IAAI,CAACA,UAAU,CAClC,GAAI,CAACA,EAAW88B,iBAAiB,EAAI,CAAC98B,EAAW+8B,eAAe,CAC5D,MAAO,EAAE,CAEb,IAAMC,EAAU,IAAI,CAACjvB,MAAM,CAAC/N,EAAW88B,iBAAiB,CAACF,EAAiB,EAAEluB,gBAAgB,CAAEuuB,EAAW,IAAI,CAAClvB,MAAM,CAAC/N,EAAW+8B,eAAe,CAACH,EAAiB,EAAEluB,gBAAgB,CAAErQ,EAAI,CACrL,CAAC,IAAKuF,KAAKkV,KAAK,CAACkkB,EAAQ95B,CAAC,EAAGU,KAAKkV,KAAK,CAACkkB,EAAQj1B,CAAC,EAAE,CACnD,CAAC,IAAKnE,KAAKkV,KAAK,CAACmkB,EAAS/5B,CAAC,EAAGU,KAAKkV,KAAK,CAACmkB,EAASl1B,CAAC,EAAE,CACxD,CACD,GAAI80B,EAAc,CACd,IAAMK,EAAc,IAAI,CAACnvB,MAAM,CAAC/N,EAAW+8B,eAAe,CAACH,EAAmB,EAAE,EAAEluB,gBAAgB,CAC5FyuB,EAAa,IAAI,CAACpvB,MAAM,CAAC/N,EAAW88B,iBAAiB,CAACF,EAAmB,EAAE,EAAEluB,gBAAgB,CACnGrQ,EAAE+B,IAAI,CAAC,CAAC,IAAKwD,KAAKkV,KAAK,CAACokB,EAAYh6B,CAAC,EAAGU,KAAKkV,KAAK,CAACokB,EAAYn1B,CAAC,EAAE,CAAE,CAAC,IAAKnE,KAAKkV,KAAK,CAACqkB,EAAWj6B,CAAC,EAAGU,KAAKkV,KAAK,CAACqkB,EAAWp1B,CAAC,EAAE,CACjI,CACA,OAAO1J,CACX,CACJ,CAMA,MAAM++B,WAAkB3D,GAMpB1qB,YAAa,CACT,KAAK,CAACA,aACN,IAAI,CAACsuB,sBAAsB,EAE/B,CACAA,wBAAyB,CACrB,IAAMr6B,EAAS,IAAI,CAACA,MAAM,CAAEs6B,EAAYt6B,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAEw1B,EAAUv6B,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAE6zB,EAAS54B,CAAM,CAAC,EAAE,CAACE,CAAC,CAAEs6B,EAAOx6B,CAAM,CAAC,EAAE,CAACE,CAAC,CAChJk6B,GAAUK,MAAM,CAACt8B,OAAO,CAAC,CAACu8B,EAAOr8B,KAC7B,IAAMs8B,EAAmB36B,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGu1B,EAAYI,EAAOE,EAAiB56B,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGw1B,EAAUG,EAAOp6B,EAAQ,IAAI,CAACpD,OAAO,CAACmuB,WAAW,CAACwP,QAAQ,CAC9IT,GAAUK,MAAM,CAACp7B,MAAM,CAAGhB,EAAI,EAAKA,CACxC,CAAA,IAAI,CAACy7B,iBAAiB,CAAG,IAAI,CAACA,iBAAiB,EAAI,EAAE,CACrD,IAAI,CAACC,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,EAAE,CACjD,IAAI,CAACe,oBAAoB,CAACx6B,EAAOs4B,EAAQ+B,EAAkB,IAAI,CAACb,iBAAiB,EACjF,IAAI,CAACgB,oBAAoB,CAACx6B,EAAOk6B,EAAMI,EAAgB,IAAI,CAACb,eAAe,CAC/E,EACJ,CACAe,qBAAqBzpB,CAAU,CAAEnR,CAAC,CAAE6E,CAAC,CAAEg2B,CAAY,CAAE,CACjD,IAAMnyB,EAAQmyB,CAAY,CAAC1pB,EAAW,CAAEga,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CACzEziB,GASDA,EAAM1L,OAAO,CAACgD,CAAC,CAAGA,EAClB0I,EAAM1L,OAAO,CAAC6H,CAAC,CAAGA,EAClB6D,EAAMiB,OAAO,IAVbkxB,CAAY,CAAC1pB,EAAW,CAAG,IAzwMqB3I,EAywMK,IAAI,CAACjL,KAAK,CAAE,IAAI,CAAE,CACnEyC,EAAGA,EACH6E,EAAGA,EACH1E,MAAOgrB,EAAYhrB,KAAK,CACxBwI,MAAOwiB,EAAYxiB,KAAK,AAC5B,EAOR,CACAge,WAAY,CACRuT,GAAUK,MAAM,CAACt8B,OAAO,CAAC,SAAU68B,CAAM,CAAE38B,CAAC,EACxC,GAAM,CAAEitB,iBAAAA,CAAgB,CAAE2P,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAE,CAAG,IAAI,CAACh+B,OAAO,CAACmuB,WAAW,CAC5E,IAAI,CAACvE,SAAS,CAAC,CACXjkB,KAAM,OACNxH,EAAGs+B,GAAqBt7B,GACxBiS,OAAQ4qB,CAAU,CAAC78B,EAAE,EAAI48B,EACzBltB,UAAW,2BACf,EAAG1P,GACCA,EAAI,GACJ,IAAI,CAACyoB,SAAS,CAAC,CACXjkB,KAAM,OACN2N,KAAM8a,CAAgB,CAACjtB,EAAI,EAAE,CAC7BkT,YAAa,EACblW,EAAGs+B,GAAqBt7B,EAAG,CAAA,GAC3B0P,UAAW,mCAAsC1P,CAAAA,EAAI,CAAA,CACzD,EAER,EAAG,IAAI,CACX,CACAqoB,WAAY,CACR0T,GAAUK,MAAM,CAACt8B,OAAO,CAAC,SAAUu8B,CAAK,CAAEr8B,CAAC,EACvC,IAAMnB,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAExrB,EAAQ,IAAI,CAAC+mB,SAAS,CAAC8S,GAAgBx8B,EAAQ0C,MAAM,CAACvB,EAAE,CAAE,CAChGuK,MAAO,SAAUtE,CAAM,EAEnB,OADcuI,AA9yM0BnE,EA8yMJc,cAAc,CAAClF,EAAOtH,UAAU,CAAC88B,iBAAiB,CAACz7B,EAAE,CAE7F,EACAyB,KAAM46B,EAAMtH,QAAQ,EACxB,GACAl2B,CAAAA,EAAQ0C,MAAM,CAACvB,EAAE,CAAGwB,EAAM3C,OAAO,AACrC,EAAG,IAAI,CACX,CACJ,CAMAk9B,GAAUK,MAAM,CAAG,CAAC,EAAG,KAAO,KAAO,GAAK,KAAO,KAAO,EAAE,CAC1DL,GAAUp+B,SAAS,CAACmqB,cAAc,CAAGuT,GAAgBjD,GAAaz6B,SAAS,CAACmqB,cAAc,CAW1F,CACIkF,YAAa,CAWTwP,SAAU,CAAA,EAIV7yB,OAAQ,EAeRsjB,iBAAkB,CACd,2BACA,2BACA,2BACA,2BACA,2BACA,2BACH,CAID2P,UAAW,UAIXC,WAAY,EAAE,CAQdt7B,OAAQ,EAAE,AACd,EACAF,aAAc,CACVopB,aAAc,CAAA,EACd/S,MAAO,QACPa,gBAAiB,OACjBoB,YAAa,EACbF,KAAM,CAAA,EACND,SAAU,OACV/Q,MAAO,OACPmB,MAAO,CACHsI,MAAO,MACX,EACAyF,cAAe,SACfjR,EAAG,CACP,CACJ,GACAokB,GAAuBvmB,KAAK,CAACsxB,SAAS,CAAGkG,GAuBzC,GAAM,CAAE7yB,MAAO4zB,EAAwB,CAAE,CAAI7+B,IAkC7C,SAASs6B,GAAUC,CAAU,CAAEC,CAAQ,CAAEsE,CAAc,EACnD,OAAO,SAAU92B,CAAM,EACnB,IAAM7G,EAAQ6G,EAAOtH,UAAU,CAACS,KAAK,CAAE49B,EAAgB59B,EAAMwI,QAAQ,CAAGxI,EAAMgJ,OAAO,CAAGhJ,EAAM+I,QAAQ,CAClGxG,EAASsE,EAAOtH,UAAU,CAACgD,MAAM,CAC/BK,EAAQL,CAAM,CAAC,EAAE,CAACI,MAAM,CAACC,KAAK,CAEpCq3B,EAAS13B,EAAOX,MAAM,CAAG,EACrBW,CAAM,CAAC,EAAE,CAACgJ,KAAK,CAAGhJ,CAAM,CAAC,EAAE,CAACgJ,KAAK,CAAG,EAExC9I,EAAIG,EAAM8J,OAAO,CAACnK,CAAM,CAAC,EAAE,CAACgJ,KAAK,CAAGqyB,EAAgBD,EAAiB1D,GAgBrE,OAdA13B,EAAS,CACL,IAp9MgD0I,EAo9MtBjL,EAAOuC,CAAM,CAAC,EAAE,CAACsE,MAAM,CAAE,CAC/CpE,EAAGA,EACH6E,EAAG,EACH1E,MAAOL,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAACmD,KAAK,CAC9BwI,MAAO7I,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAAC2L,KAAK,AAClC,GACA,IA19MgDH,EA09MtBjL,EAAOuC,CAAM,CAAC,EAAE,CAACsE,MAAM,CAAE,CAC/CpE,EAAGA,EACH6E,EAAG,EACH1E,MAAOL,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAACmD,KAAK,CAC9BwI,MAAO7I,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAAC2L,KAAK,AAClC,GACH,CACMwvB,GAAmBtB,aAAa,CAAC/2B,CAAM,CAAC62B,EAAW,CAAE72B,CAAM,CAAC82B,EAAS,CAChF,CACJ,CAMA,MAAMwE,WAA2BjG,GAM7BxO,WAAY,CAER,IAAI0U,EAAO,EAAGC,EAAW,EACzB,IAAK,IAAIn9B,EAAI,EAAGA,EAFM,GAEaA,IAAK,CAGpC,IAAMo9B,EAAgB,AAACp9B,EAAQk9B,EAAJ,EAAUv7B,EAAS,CAC1C42B,GAAU,EAAG,EAAG6E,GAChB7E,GAAU,EAAG,EAAG6E,GACnB,CAGDF,EAAOC,AADPA,CAAAA,EAAWD,EAAOC,CAAO,EACPD,EAER,IAANl9B,GACA,CAAA,IAAI,CAACq9B,oBAAoB,CAAG,CAAC17B,CAAM,CAAC,EAAE,CAAEA,CAAM,CAAC,EAAE,CAAC,AAAD,EAErD,IAAI,CAAC8mB,SAAS,CAACqU,GAAyB,IAAI,CAACj+B,OAAO,CAACmuB,WAAW,CAAC0I,IAAI,CAAE,CACnElxB,KAAM,OACN7C,OAAQA,EACR+N,UAAW,sCACf,GAAI1P,EAER,CACJ,CACA6M,kBAAmB,CACf,IAAMhO,EAAU,IAAI,CAACA,OAAO,CAAEmuB,EAAcnuB,EAAQmuB,WAAW,CAAEzf,EAAe,IAAInE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE09B,GAAyBj+B,EAAQmO,mBAAmB,CAAEggB,EAAYhgB,mBAAmB,EAAG,GACvN,IAAI,CAACF,aAAa,CAAC/N,IAAI,CAACwO,GACxByf,EAAYhgB,mBAAmB,CAAGO,EAAa1O,OAAO,AAC1D,CACJ,CACAo+B,GAAmBt/B,SAAS,CAACmqB,cAAc,CAAGgV,GAAyB9F,GAAkBr5B,SAAS,CAACmqB,cAAc,CAYjH,CACIkF,YAAa,CAQT0I,KAAM,CASFzjB,OAAQ,sBASRiB,YAAa,EAEbf,KAAM,KAAK,CACf,EACAnF,oBAAqB,CACjBzD,WAAY,WAER,IAAMtD,EAAS,IAAI,CAACA,MAAM,CAAEjH,EAAU,IAAI,CAACA,OAAO,CAAEs+B,EAAar3B,EAAOo3B,oBAAoB,CAAEE,EAAO,CAAE5+B,WAAYsH,CAAO,EAAGu3B,EAAkBF,CAAU,CAAC,EAAE,CAACC,GAAM72B,CAAC,CAAE+2B,EAAmBH,CAAU,CAAC,EAAE,CAACC,GAAM72B,CAAC,CAAEyB,EAAW,IAAI,CAAC/I,KAAK,CAAC+I,QAAQ,CAAEC,EAAU,IAAI,CAAChJ,KAAK,CAACgJ,OAAO,CACxQvG,EAAIy7B,CAAU,CAAC,EAAE,CAACC,GAAM17B,CAAC,CAAE6E,EAAI,AAAC82B,CAAAA,EAAkBC,CAAe,EAAK,EAI1E,OAHI,IAAI,CAACr+B,KAAK,CAACwI,QAAQ,EACnB,CAAA,CAAC/F,EAAG6E,EAAE,CAAG,CAACA,EAAG7E,EAAE,AAAD,EAEX,CACHA,EAAGsG,EAAWtG,EAAI,AAAC7C,CAAAA,EAAQ0K,KAAK,EAAI,CAAA,EAAK,EACzChD,EAAG0B,EAAU1B,EAAI,AAAC1H,CAAAA,EAAQ2K,MAAM,EAAI,CAAA,EAAK,CAC7C,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EAIrB,GAHqBA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC9GC,gBAAiB,CAAA,CACrB,GACkB,CACd,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAChDU,EAAOwI,cAAc,CAACnG,EAAYzG,CAAC,CAAE,EAAG,GACxCoE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,CACJ,CACJ,CACJ,GACAosB,GAAuBvmB,KAAK,CAACm5B,kBAAkB,CAAGT,GAmBlD,GAAM,CAAE/zB,MAAOy0B,EAAe,CAAE,CAAI1/B,GAMpC,OAAM2/B,WAAkB5D,GAMpB,OAAO6D,mBAAmBC,CAAe,CAAE,CACvC,OAAO,SAAU73B,CAAM,EACnB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAChE,OAAOi8B,GAAUlF,aAAa,CAAC/2B,CAAM,CAACm8B,EAAgB,CAAEn8B,CAAM,CAAC,EAAE,CAAE,IAvnNnB0I,EAunN6C1L,EAAWS,KAAK,CAAE6G,EAAQtH,EAAWo/B,eAAe,IACrJ,CACJ,CACA,OAAOrF,cAAcnuB,CAAK,CAAEyzB,CAAe,CAAEC,CAAgB,CAAE,CAC3D,IAAMroB,EAAQrT,KAAKsF,KAAK,CAAEo2B,EAAiBrzB,KAAK,CAC5CozB,EAAgBpzB,KAAK,CAAGqzB,EAAiBtzB,KAAK,CAAGqzB,EAAgBrzB,KAAK,EAC1E,MAAO,CACH9I,EAAG0I,EAAMI,KAAK,CAAGwO,AAFmE,IAExD5W,KAAK0J,GAAG,CAAC2J,GACrClP,EAAG6D,EAAMK,KAAK,CAAGuO,AAHmE,IAGxD5W,KAAK2J,GAAG,CAAC0J,EACzC,CACJ,CACA,OAAOsoB,oBAAoBj4B,CAAM,CAAE,CAC/B,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAChE,OAAOq4B,GAAmBtB,aAAa,CAAC/2B,CAAM,CAAC,EAAE,CAAE,IApoNC0I,EAooNyB1L,EAAWS,KAAK,CAAE6G,EAAQtH,EAAWo/B,eAAe,IACrI,CAMAA,iBAAkB,CACd,IAAMp8B,EAAS,IAAI,CAACA,MAAM,CAC1B,MAAO,CACHE,EAAG,AAACF,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGF,CAAM,CAAC,EAAE,CAACE,CAAC,AAADA,EAAK,EACjC6E,EAAG,AAAC/E,CAAAA,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,AAADA,EAAK,EACjC1E,MAAOL,CAAM,CAAC,EAAE,CAACI,MAAM,CAACC,KAAK,CAC7BwI,MAAO7I,CAAM,CAAC,EAAE,CAACI,MAAM,CAACyI,KAAK,AACjC,CACJ,CACAge,WAAY,CACR,IAAI,CAAC2V,QAAQ,GACb,IAAI,CAACC,cAAc,EACvB,CACAD,UAAW,CACP,IAAMzuB,EAAY,6BAClB,IAAI,CAAC+Y,SAAS,CAAC,CACXjkB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACdi8B,GAAUM,mBAAmB,CAChC,CACDxuB,UAAAA,CACJ,EAAG,GACH,IAAI,CAAC+Y,SAAS,CAAC,CACXjkB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACdi8B,GAAUS,gBAAgB,CAC7B,CACD3uB,UAAAA,CACJ,EAAG,GACH,IAAI,CAAC+Y,SAAS,CAAC,CACXjkB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACdi8B,GAAUU,mBAAmB,CAChC,CACD5uB,UAAAA,CACJ,EAAG,EACP,CACA0uB,gBAAiB,CACb,IAAM51B,EAAS,IAAI,CAACA,MAAM,CAAEwkB,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAC5DwI,EAAkB,IAAI,CAAC/M,SAAS,CAACkV,GAAgB3Q,EAAYwI,eAAe,CAAE,CAChFhxB,KAAM,OACN7C,OAAQ,CACJ,SAAUsE,CAAM,EACZ,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAAEo8B,EAAkBp/B,EAAWo/B,eAAe,GAC9G,MAAO,CACHl8B,EAAG,AAACF,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGk8B,EAAgBl8B,CAAC,AAADA,EAAK,EACvC6E,EAAG,AAAC/E,CAAAA,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGq3B,EAAgBr3B,CAAC,AAADA,EAAK,EACvC1E,MAAO+7B,EAAgB/7B,KAAK,CAC5BwI,MAAOuzB,EAAgBvzB,KAAK,AAChC,CACJ,EACAhC,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB6G,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB,SAAUsE,CAAM,EACZ,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAAEo8B,EAAkBp/B,EAAWo/B,eAAe,GAC9G,MAAO,CACHl8B,EAAG,AAACk8B,CAAAA,EAAgBl8B,CAAC,CAAGF,CAAM,CAAC,EAAE,CAACE,CAAC,AAADA,EAAK,EACvC6E,EAAG,AAACq3B,CAAAA,EAAgBr3B,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,AAADA,EAAK,EACvC1E,MAAO+7B,EAAgB/7B,KAAK,CAC5BwI,MAAOuzB,EAAgBvzB,KAAK,AAChC,CACJ,EACH,CACDkF,UAAW,uCACf,GAAI,GACE+lB,EAAkB,IAAI,CAAChN,SAAS,CAACkV,GAAgB3Q,EAAYyI,eAAe,CAAE,CAChFjxB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd6G,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB6G,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB,IAAI,CAACA,MAAM,CAAC,EAAE,CACjB,CACD+N,UAAW,uCACf,GAAI,EACJsd,CAAAA,EAAYwI,eAAe,CAAGA,EAAgB32B,OAAO,CACrDmuB,EAAYyI,eAAe,CAAGA,EAAgB52B,OAAO,AACzD,CACJ,CAMA++B,GAAUS,gBAAgB,CAAGT,GAAUC,kBAAkB,CAAC,GAC1DD,GAAUU,mBAAmB,CAAGV,GAAUC,kBAAkB,CAAC,GAC7DD,GAAUjgC,SAAS,CAACmqB,cAAc,CAAG6V,GAAgB3D,GAAmBr8B,SAAS,CAACmqB,cAAc,CAWhG,CACIkF,YAAa,CAOTwI,gBAAiB,CACbrjB,KAAM,2BACNe,YAAa,CACjB,EAOAuiB,gBAAiB,CACbtjB,KAAM,2BACNe,YAAa,CACjB,CACJ,CACJ,GACA4X,GAAuBvmB,KAAK,CAACwxB,SAAS,CAAG6H,GAkBzC,GAAM,CAAE10B,MAAOq1B,EAAkB,CAAEjgC,KAAMkgC,EAAiB,CAAE,CAAIvgC,GAMhE,OAAMwgC,WAAqB3T,GAMvB,OAAO4T,oBAAoBz4B,CAAM,CAAE,CAC/B,IAAMtH,EAAasH,EAAOtH,UAAU,CAAES,EAAQT,EAAWS,KAAK,CAAEwI,EAAWxI,EAAMwI,QAAQ,CAAE2C,EAAQ5L,EAAWgD,MAAM,CAAC,EAAE,CAAEknB,EAAO2V,GAAkBj0B,EAAMxI,MAAM,CAACyI,KAAK,EAAID,EAAMxI,MAAM,CAACyI,KAAK,CAACqe,IAAI,CAAE,GAAIvD,EAAMkZ,GAAkBj0B,EAAMxI,MAAM,CAACyI,KAAK,EAAID,EAAMxI,MAAM,CAACyI,KAAK,CAAC8a,GAAG,CAAE,GAAIqZ,EAAShgC,EAAWE,OAAO,CAACmuB,WAAW,CAACxrB,KAAK,CAACm9B,MAAM,CAAEj4B,EAAI8H,AAtyNrRnE,EAsyN2SI,aAAa,CAACF,EAAO,CAAA,EAAK,CAAC3C,EAAW,IAAM,IAAI,CAC/Y,MAAO,CACH/F,EAAG0I,EAAM1I,CAAC,CACVG,MAAOuI,EAAMxI,MAAM,CAACC,KAAK,CACzB0E,EAAGA,EAAIi4B,EACF/2B,CAAAA,EAAYihB,EAAOzpB,EAAM+I,QAAQ,CAAKmd,EAAMlmB,EAAMgJ,OAAO,CAClE,CACJ,CACA,OAAOw2B,qBAAqB34B,CAAM,CAAE,CAChC,IAAMtH,EAAasH,EAAOtH,UAAU,CAAES,EAAQT,EAAWS,KAAK,CAAEwI,EAAWxI,EAAMwI,QAAQ,CAAEolB,EAAcruB,EAAWE,OAAO,CAACmuB,WAAW,CAAEziB,EAAQ5L,EAAWgD,MAAM,CAAC,EAAE,CAAEknB,EAAO2V,GAAkBj0B,EAAMxI,MAAM,CAACyI,KAAK,EAAID,EAAMxI,MAAM,CAACyI,KAAK,CAACqe,IAAI,CAAE,GAAIvD,EAAMkZ,GAAkBj0B,EAAMxI,MAAM,CAACyI,KAAK,EAAID,EAAMxI,MAAM,CAACyI,KAAK,CAAC8a,GAAG,CAAE,GAAI5e,EAAI8H,AA/yN7QnE,EA+yNmSI,aAAa,CAACF,EAAO,CAAA,EAAK,CAAC3C,EAAW,IAAM,IAAI,CACnYwP,EAAU4V,EAAY5V,OAAO,CAIjC,OAHI4V,EAAYxrB,KAAK,CAACm9B,MAAM,CAAG,GAC3BvnB,CAAAA,GAAW,EAAC,EAET,CACHvV,EAAG0I,EAAM1I,CAAC,CACVG,MAAOuI,EAAMxI,MAAM,CAACC,KAAK,CACzB0E,EAAGA,EAAI0Q,EACFxP,CAAAA,EAAYihB,EAAOzpB,EAAM+I,QAAQ,CAAKmd,EAAMlmB,EAAMgJ,OAAO,CAClE,CACJ,CAMAoF,kBAAmB,CACf,MAAO,CAAC,IAAI,CAAC3O,OAAO,CAACmuB,WAAW,CAACziB,KAAK,CAAC,AAC3C,CACAie,WAAY,CACR,IAAMwE,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAAEnV,EAAY,IAAI,CAAC4Q,SAAS,CAAC8V,GAAmBvR,EAAYnV,SAAS,CAAE,CAC/GrT,KAAM,OACN7C,OAAQ,CACJ88B,GAAaC,mBAAmB,CAChCD,GAAaG,oBAAoB,CACpC,CACDlvB,UAAW,0BACf,GAAI,EACJsd,CAAAA,EAAYnV,SAAS,CAAGA,EAAUhZ,OAAO,CACzC,IAAI,CAACJ,WAAW,CAACuuB,WAAW,CAACziB,KAAK,CAAGyiB,EAAYziB,KAAK,AAC1D,CACA8d,WAAY,CACR,IAAM2E,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAAE3rB,EAAe2rB,EAAYxrB,KAAK,CAC1EK,EAAI,EAAG6E,EAAIrF,EAAas9B,MAAM,CAAEhnB,EAAgBtW,EAAas9B,MAAM,CAAG,EAAI,SAAW,MAAOjnB,EAAQ,QACpG,CAAA,IAAI,CAACtY,KAAK,CAACwI,QAAQ,GACnB/F,EAAIR,EAAas9B,MAAM,CACvBj4B,EAAI,EACJiR,EAAgB,SAChBD,EAAQrW,EAAas9B,MAAM,CAAG,EAAI,QAAU,QAQhD3R,EAAYxrB,KAAK,CAAGA,AANN,IAAI,CAAC+mB,SAAS,CAACgW,GAAmBl9B,EAAc,CAC1DsW,cAAeA,EACfD,MAAOA,EACP7V,EAAGA,EACH6E,EAAGA,CACP,IAC0B7H,OAAO,AACrC,CACJ,CACA4/B,GAAa9gC,SAAS,CAACmqB,cAAc,CAAGyW,GAAmBzT,GAAuBntB,SAAS,CAACmqB,cAAc,CAY1G,CACIkF,YAAa,CAIT5V,QAAS,GAMT5V,MAAO,CACHm9B,OAAQ,IACRp0B,MAAO,SAAUtE,CAAM,EACnB,OAAOA,EAAOtH,UAAU,CAACgD,MAAM,CAAC,EAAE,AACtC,EACA8oB,aAAc,CAAA,EACdlS,gBAAiB,OACjBoB,YAAa,EACbF,KAAM,CAAA,EACND,SAAU,OACV/Q,MAAO,OACPhH,KAAM,SACV,EAOAoW,UAAW,CACP3E,YAAa,EACb2rB,UAAW,OACf,CACJ,CACJ,GACA/T,GAAuBvmB,KAAK,CAACoxB,YAAY,CAAG8I,GAkB5C,GAAM,CAAE/5B,QAASo6B,EAAe,CAAE3tB,OAAQ4tB,EAAc,CAAEloB,SAAUmoB,EAAgB,CAAE91B,MAAO+1B,EAAa,CAAE3gC,KAAM4gC,EAAY,CAAE,CAAIjhC,IAUpI,SAASkhC,KACL,IAAIA,EAAU,EAAGC,EAAc,EAAGC,EAAe,EAC3Ct9B,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEu9B,EAAMvT,GAAY,IAAI,CAACwN,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAgB9G,OAfA33B,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACVg1B,GAAsBh1B,EAAO+0B,IAC7BN,GAAiBz0B,EAAM7D,CAAC,IACxB04B,GAAe70B,EAAM7D,CAAC,CACtB24B,IAER,EAER,GACIA,EAAe,GACfF,CAAAA,EAAUC,EAAcC,CAAW,EAEhCF,CACX,CAIA,SAASI,GAAsBh1B,CAAK,CAAE+0B,CAAG,EACrC,MAAQ,CAAC/0B,EAAMi1B,MAAM,EACjBR,GAAiBz0B,EAAM7D,CAAC,GACxB6D,EAAM1I,CAAC,CAAGy9B,EAAI/F,QAAQ,EACtBhvB,EAAM1I,CAAC,EAAIy9B,EAAI9F,QAAQ,EACvBjvB,EAAM7D,CAAC,CAAG44B,EAAI7F,QAAQ,EACtBlvB,EAAM7D,CAAC,EAAI44B,EAAI5F,QAAQ,AAC/B,CAIA,SAAS+F,KACL,IAAM19B,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEu9B,EAAMvT,GAAY,IAAI,CAACwN,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAC1G+F,EAAO,EAWX,OAVA19B,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACVg1B,GAAsBh1B,EAAO+0B,IAC7BG,GAER,EAER,GACOA,CACX,CAKA,SAASC,KACL,MAAO,QAAU,IAAI,CAACzT,GAAG,CACrB,YAAc,IAAI,CAACzpB,GAAG,CACtB,gBAAkB,IAAI,CAAC28B,OAAO,CAACQ,OAAO,CAAC,GACvC,aAAe,IAAI,CAACF,IAAI,AAChC,CAMA,SAAS1T,GAAYwN,CAAQ,CAAEC,CAAQ,CAAEC,CAAQ,CAAEC,CAAQ,EACvD,MAAO,CACHH,SAAUh3B,KAAK0pB,GAAG,CAACuN,EAAUD,GAC7BC,SAAUj3B,KAAKC,GAAG,CAACg3B,EAAUD,GAC7BE,SAAUl3B,KAAK0pB,GAAG,CAACyN,EAAUD,GAC7BC,SAAUn3B,KAAKC,GAAG,CAACk3B,EAAUD,EACjC,CACJ,CAYA,SAASmG,GAAY9T,CAAI,CAAEla,CAAK,CAAE+sB,CAAM,EACpC,OAAO7S,EAAKhgB,OAAO,CAACggB,EAAKngB,QAAQ,CAACiG,GAAS+sB,EAC/C,CAKA,SAASkB,KACL,IAAMhhC,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAE5tB,EAAQ,IAAI,CAACA,KAAK,CAAEwI,EAAWxI,EAAMwI,QAAQ,CAAE5F,EAAQ5C,EAAM4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEwI,EAAQpL,EAAMoL,KAAK,CAAC3L,EAAQ2L,KAAK,CAAC,CAAEs1B,EAAKjhC,EAAQ02B,UAAU,CAAE7rB,EAAQ9B,EAAWk4B,EAAGn2B,MAAM,CAAGm2B,EAAGp2B,KAAK,CAAEC,EAAS/B,EAAWk4B,EAAGp2B,KAAK,CAAGo2B,EAAGn2B,MAAM,CAAEo2B,EAAalhC,EAAQkhC,UAAU,CAAEza,EAAM1d,EAAW5F,EAAM6mB,IAAI,CAAGre,EAAM8a,GAAG,CACrVuD,EAAOjhB,EAAW4C,EAAM8a,GAAG,CAAGtjB,EAAM6mB,IAAI,AACxC,CAAA,IAAI,CAACmX,SAAS,CAAGnhC,EAAQ0L,KAAK,CAAC1I,CAAC,CAChC,IAAI,CAACo+B,SAAS,CAAGphC,EAAQ0L,KAAK,CAAC7D,CAAC,CAC5Bs4B,GAAiBt1B,GACjB,IAAI,CAACw2B,SAAS,CAAG,IAAI,CAACF,SAAS,CAAGt2B,EAGlC,IAAI,CAACw2B,SAAS,CAAGN,GAAY59B,EAAO,IAAI,CAACg+B,SAAS,CAAElM,WAAWpqB,IAE/Ds1B,GAAiBr1B,GACjB,IAAI,CAACw2B,SAAS,CAAG,IAAI,CAACF,SAAS,CAAGt2B,EAGlC,IAAI,CAACw2B,SAAS,CAAGP,GAAYp1B,EAAO,IAAI,CAACy1B,SAAS,CAAEnM,WAAWnqB,IAG/Do2B,AAAe,MAAfA,GACA,IAAI,CAACE,SAAS,CAAGz1B,EAAMsB,OAAO,CAACwZ,GAC/B,IAAI,CAAC6a,SAAS,CAAG31B,EAAMsB,OAAO,CAACwZ,EAAM9a,EAAMkB,GAAG,GAE1B,MAAfq0B,IACL,IAAI,CAACC,SAAS,CAAGh+B,EAAM8J,OAAO,CAAC+c,GAC/B,IAAI,CAACqX,SAAS,CAAGl+B,EAAM8J,OAAO,CAAC+c,EAAO7mB,EAAM0J,GAAG,EAEvD,CAIA,SAASlJ,KACL,IAAMT,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEu9B,EAAMvT,GAAY,IAAI,CAACwN,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAC1Gl3B,EAAM,CAAC49B,IAAUC,EAAe,CAAA,EAiBpC,OAhBAt+B,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACVy0B,GAAiBz0B,EAAM7D,CAAC,GACxB6D,EAAM7D,CAAC,CAAGlE,GACV+8B,GAAsBh1B,EAAO+0B,KAC7B98B,EAAM+H,EAAM7D,CAAC,CACb25B,EAAe,CAAA,EAEvB,EAER,GACKA,GACD79B,CAAAA,EAAM,CAAA,EAEHA,CACX,CAKA,SAASypB,KACL,IAAMlqB,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEu9B,EAAMvT,GAAY,IAAI,CAACwN,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAC1GzN,EAAMmU,IAAUC,EAAe,CAAA,EAiBnC,OAhBAt+B,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAACyK,IACVy0B,GAAiBz0B,EAAM7D,CAAC,GACxB6D,EAAM7D,CAAC,CAAGulB,GACVsT,GAAsBh1B,EAAO+0B,KAC7BrT,EAAM1hB,EAAM7D,CAAC,CACb25B,EAAe,CAAA,EAEvB,EAER,GACKA,GACDpU,CAAAA,EAAM,CAAA,EAEHA,CACX,CAQA,SAASqU,GAAYC,CAAM,EACvB,IAAM1hC,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAEhrB,EAAQ,IAAI,CAAC5C,KAAK,CAAC4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEwI,EAAQ,IAAI,CAACpL,KAAK,CAACoL,KAAK,CAAC3L,EAAQ2L,KAAK,CAAC,CAAEg2B,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAU,IAAI,CAACA,OAAO,AAC1K,CAAA,IAAI,CAAClH,QAAQ,CAAGqG,GAAY59B,EAAO,IAAI,CAACg+B,SAAS,CAAEQ,GACnD,IAAI,CAAChH,QAAQ,CAAGoG,GAAY59B,EAAO,IAAI,CAACk+B,SAAS,CAAEM,GACnD,IAAI,CAAC/G,QAAQ,CAAGmG,GAAYp1B,EAAO,IAAI,CAACy1B,SAAS,CAAEQ,GACnD,IAAI,CAAC/G,QAAQ,CAAGkG,GAAYp1B,EAAO,IAAI,CAAC21B,SAAS,CAAEM,GACnD,IAAI,CAACxU,GAAG,CAAGA,GAAIpuB,IAAI,CAAC,IAAI,EACxB,IAAI,CAAC2E,GAAG,CAAGA,GAAI3E,IAAI,CAAC,IAAI,EACxB,IAAI,CAACshC,OAAO,CAAGA,GAAQthC,IAAI,CAAC,IAAI,EAChC,IAAI,CAAC4hC,IAAI,CAAGA,GAAK5hC,IAAI,CAAC,IAAI,EACtB0iC,GACA,IAAI,CAACA,MAAM,CAAC,EAAG,EAEvB,CAYA,SAASG,GAAkBhiC,CAAM,CAAE6hC,CAAM,CAAEI,CAAO,CAAEl5B,CAAE,CAAEF,CAAE,EACtD,IAAM1I,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAE+S,EAAalhC,EAAQkhC,UAAU,CAAE/9B,EAAQ,IAAI,CAAC5C,KAAK,CAAC4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEwI,EAAQ,IAAI,CAACpL,KAAK,CAACoL,KAAK,CAAC3L,EAAQ2L,KAAK,CAAC,CAAEw1B,EAAY,IAAI,CAACA,SAAS,CAAEE,EAAY,IAAI,CAACA,SAAS,CAAED,EAAY,IAAI,CAACA,SAAS,CAAEE,EAAY,IAAI,CAACA,SAAS,CAAEK,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAU,IAAI,CAACA,OAAO,CACvTF,IACIR,AAAe,MAAfA,EACIY,AAAY,IAAZA,EACA,IAAI,CAACX,SAAS,CAAGJ,GAAY59B,EAAOg+B,EAAWv4B,GAG/C,IAAI,CAACy4B,SAAS,CAAGN,GAAY59B,EAAOk+B,EAAWz4B,GAG9Cs4B,AAAe,MAAfA,EACDY,AAAY,IAAZA,EACA,IAAI,CAACV,SAAS,CAAGL,GAAYp1B,EAAOy1B,EAAW14B,GAG/C,IAAI,CAAC44B,SAAS,CAAGP,GAAYp1B,EAAO21B,EAAW54B,IAInD,IAAI,CAAC24B,SAAS,CAAGN,GAAY59B,EAAOk+B,EAAWz4B,GAC/C,IAAI,CAAC04B,SAAS,CAAGP,GAAYp1B,EAAO21B,EAAW54B,KAGnD7I,IACA,IAAI,CAACshC,SAAS,CAAGJ,GAAY59B,EAAOg+B,EAAWQ,GAC/C,IAAI,CAACN,SAAS,CAAGN,GAAY59B,EAAOk+B,EAAWM,GAC/C,IAAI,CAACP,SAAS,CAAGL,GAAYp1B,EAAOy1B,EAAWQ,GAC/C,IAAI,CAACN,SAAS,CAAGP,GAAYp1B,EAAO21B,EAAWM,GAC/C,IAAI,CAACD,OAAO,CAAG,EACf,IAAI,CAACC,OAAO,CAAG,GAEnB,IAAI,CAAC5hC,OAAO,CAACmuB,WAAW,CAACziB,KAAK,CAAG,CAC7B1I,EAAG,IAAI,CAACm+B,SAAS,CACjBt5B,EAAG,IAAI,CAACu5B,SAAS,AACrB,EAGA,IAAI,CAACxhC,WAAW,CAACuuB,WAAW,CAACziB,KAAK,CAAG,CACjC1I,EAAG,IAAI,CAACm+B,SAAS,CACjBt5B,EAAG,IAAI,CAACu5B,SAAS,AACrB,CACJ,CAMA,MAAMW,WAAgB9V,GAUlB7b,KAAK4xB,CAAiB,CAAEpiC,CAAW,CAAEwD,CAAK,CAAE,CACxC,KAAK,CAACgN,KAAK4xB,EAAmBpiC,EAAawD,GAC3C,IAAI,CAACu+B,OAAO,CAAG,EACf,IAAI,CAACC,OAAO,CAAG,EACf,IAAI,CAACK,OAAO,CAAG,EACf,IAAI,CAACC,OAAO,CAAG,EACflB,GAAahiC,IAAI,CAAC,IAAI,EACtB,IAAI,CAACmjC,SAAS,GACd,IAAI,CAACxY,SAAS,EAClB,CAKAP,aAAc,CACV,IAAI,CAACC,SAAS,CAAG,IAAI,CAAC9oB,KAAK,CAAC4C,KAAK,CAAC,IAAI,CAACnD,OAAO,CAACmuB,WAAW,CAAChrB,KAAK,CAAC,CACjE,IAAI,CAACmmB,SAAS,CAAG,IAAI,CAAC/oB,KAAK,CAACoL,KAAK,CAAC,IAAI,CAAC3L,OAAO,CAACmuB,WAAW,CAACxiB,KAAK,CAAC,AACrE,CAKAy2B,oBAAqB,CACjB,IAAMpiC,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAEhrB,EAAQnD,EAAQmD,KAAK,CAAEwI,EAAQ3L,EAAQ2L,KAAK,CACtF,MAAO,CACH,CACI3I,EAAG,IAAI,CAAC03B,QAAQ,CAChB7yB,EAAG,IAAI,CAAC+yB,QAAQ,CAChBz3B,MAAOA,EACPwI,MAAOA,CACX,EACA,CACI3I,EAAG,IAAI,CAAC23B,QAAQ,CAChB9yB,EAAG,IAAI,CAAC+yB,QAAQ,CAChBz3B,MAAOA,EACPwI,MAAOA,CACX,EACA,CACI3I,EAAG,IAAI,CAAC23B,QAAQ,CAChB9yB,EAAG,IAAI,CAACgzB,QAAQ,CAChB13B,MAAOA,EACPwI,MAAOA,CACX,EACA,CACI3I,EAAG,IAAI,CAAC03B,QAAQ,CAChB7yB,EAAG,IAAI,CAACgzB,QAAQ,CAChB13B,MAAOA,EACPwI,MAAOA,CACX,EACA,CACIc,QAAS,GACb,EACH,AACL,CACAuB,kBAAmB,CACf,IAAMjF,EAAW,IAAI,CAACxI,KAAK,CAACwI,QAAQ,CAAE/I,EAAU,IAAI,CAACA,OAAO,CAACmO,mBAAmB,CAAE+yB,EAAa,IAAI,CAAClhC,OAAO,CAACmuB,WAAW,CAAC+S,UAAU,CAC7HjB,GAAgB,IAAI,CAACrgC,WAAW,CAACuO,mBAAmB,EAAEpD,OAAOnD,UAC1Ds5B,AAAe,MAAfA,EACAlhC,EAAQ+K,KAAK,CAACnD,MAAM,CAAGmB,EAAW,YAAc,YAE5B,MAAfm4B,GACLlhC,CAAAA,EAAQ+K,KAAK,CAACnD,MAAM,CAAGmB,EAAW,YAAc,WAAU,GAGlE,IAAI2F,EAAe,IAAInE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE,IAAI,CAACP,OAAO,CAACmO,mBAAmB,CAAE,GACpG,IAAI,CAACF,aAAa,CAAC/N,IAAI,CAACwO,GAEL,OAAfwyB,IACAxyB,EAAe,IAAInE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE,IAAI,CAACP,OAAO,CAACmO,mBAAmB,CAAE,GAChG,IAAI,CAACF,aAAa,CAAC/N,IAAI,CAACwO,GAEhC,CAOAyzB,UAAUT,CAAM,CAAE,CACd,IAAMvT,EAAc,IAAI,CAACnuB,OAAO,CAACmuB,WAAW,CAAErU,EAAYqU,EAAYxrB,KAAK,CAACmX,SAAS,CAErF2nB,GAAYziC,IAAI,CAAC,IAAI,CAAE0iC,GAClBvT,EAAYxrB,KAAK,CAACyvB,OAAO,GAG1B,IAAI,CAAC1vB,MAAM,CAACP,MAAM,CAAG,EACrB,AAAC,IAAI,CAACO,MAAM,CAAC,EAAE,CAAEE,IAAI,CAAI,AAACkX,GAAaA,EAAU9a,IAAI,CAAC,IAAI,GACtD6hC,GAAiB7hC,IAAI,CAAC,IAAI,EAG9B,IAAI,CAAC0qB,SAAS,CAACwW,GAAe,CAC1Bt2B,MAAO,OACP8P,gBAAiB,OACjBrG,MAAO,QACPyH,YAAa,EACb5F,UAAW,OACXyF,SAAU,QACV9B,MAAO,OACPhR,EAAG,EACH7E,EAAG,EACH8V,cAAe,MACf8B,KAAM,CAAA,EACNzX,MAAO,EACPwI,MAAO,EACPD,MAAO,SAAUtE,CAAM,EACnB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEE,EAAUoH,EAAOpH,OAAO,CAC9D,MAAO,CACHgD,EAAGlD,EAAW46B,QAAQ,CACtB7yB,EAAG/H,EAAW86B,QAAQ,CACtBz3B,MAAOk9B,GAAalS,EAAYhrB,KAAK,CAAEnD,EAAQmD,KAAK,EACpDwI,MAAO00B,GAAalS,EAAYxiB,KAAK,CAAE3L,EAAQ2L,KAAK,CACxD,CACJ,EACA/I,KAAO,AAACkX,GAAaA,EAAU9a,IAAI,CAAC,IAAI,GACpC6hC,GAAiB7hC,IAAI,CAAC,IAAI,CAClC,EAAGmvB,EAAYxrB,KAAK,EAAG,KAAK,GAEpC,CAKAgnB,WAAY,CACR,IAAI,CAAC0Y,aAAa,GAClB,IAAI,CAACvJ,aAAa,EACtB,CAKAA,eAAgB,CACZ,IAAMwJ,EAAc,IAAI,CAACF,kBAAkB,EACX,MAAA,IAArBE,CAAW,CAAC,EAAE,CAACt/B,CAAC,EAG3B,IAAI,CAAC4mB,SAAS,CAACsW,GAAe,CAC1Bv6B,KAAM,OACN7C,OAAQw/B,EACRzxB,UAAW,+BACf,EAAG,IAAI,CAAC7Q,OAAO,CAACmuB,WAAW,CAACuI,UAAU,EAAG,EAC7C,CAKA2L,eAAgB,CACZ,IAAM9hC,EAAQ,IAAI,CAACA,KAAK,CAAEP,EAAU,IAAI,CAACA,OAAO,CAACmuB,WAAW,CAAEziB,EAAQ,IAAI,CAAC1L,OAAO,CAACmuB,WAAW,CAACziB,KAAK,CAAEvI,EAAQ5C,EAAM4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEwI,EAAQpL,EAAMoL,KAAK,CAAC3L,EAAQ2L,KAAK,CAAC,CAAE5C,EAAWxI,EAAMwI,QAAQ,CAAEkgB,EAAiB,CACtNvd,MAAOA,EACP/F,KAAM,MACV,EACI+0B,EAAWv3B,EAAM2J,QAAQ,CAAC,IAAI,CAAC4tB,QAAQ,EAAGC,EAAWx3B,EAAM2J,QAAQ,CAAC,IAAI,CAAC6tB,QAAQ,EAAGC,EAAWjvB,EAAMmB,QAAQ,CAAC,IAAI,CAAC8tB,QAAQ,EAAGC,EAAWlvB,EAAMmB,QAAQ,CAAC,IAAI,CAAC+tB,QAAQ,EAAG0H,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAAEC,EAAmBC,EAAmB55B,EACtOC,IACAD,EAAO4xB,EACPA,EAAWE,EACXA,EAAW9xB,EACXA,EAAO6xB,EACPA,EAAWE,EACXA,EAAW/xB,GAGX9I,EAAQqyB,UAAU,CAACD,OAAO,EAC1BmQ,CAAAA,EAAQ,CAAC,CACD,IACA7H,EACAE,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACvC,CAAE,CACC,IACAD,EACAC,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACvC,CAAC,AAAD,EAGL56B,EAAQmyB,UAAU,CAACC,OAAO,EAC1BoQ,CAAAA,EAAQ,CAAC,CACD,IACA9H,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACpCE,EACH,CAAE,CACC,IACAF,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACpCG,EACH,CAAC,AAAD,EAGL,IAAI,CAAClxB,MAAM,CAACxH,MAAM,CAAG,GACrB,IAAI,CAACwH,MAAM,CAAC,EAAE,CAAC3J,OAAO,CAAC7B,CAAC,CAAGokC,EAC3B,IAAI,CAAC54B,MAAM,CAAC,EAAE,CAAC3J,OAAO,CAAC7B,CAAC,CAAGqkC,IAI3BC,EAAoBrC,GAAcnX,EAAgB,CAAEpY,UAAW,gCAAiC,EAAG7Q,EAAQqyB,UAAU,EACrHqQ,EAAoBtC,GAAcnX,EAAgB,CAAEpY,UAAW,gCAAiC,EAAG7Q,EAAQmyB,UAAU,EACrH,IAAI,CAACvI,SAAS,CAACsW,GAAe,CAAE/hC,EAAGokC,CAAM,EAAGE,GAAoB,GAChE,IAAI,CAAC7Y,SAAS,CAACsW,GAAe,CAAE/hC,EAAGqkC,CAAM,EAAGE,GAAoB,GAExE,CACAj7B,OAAOf,CAAC,CAAE,CACN,IAAM+C,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAAIw6B,EAAa,IAAI,CAAClhC,OAAO,CAACmuB,WAAW,CAAC+S,UAAU,CAAEl+B,EAAIk+B,AAAe,MAAfA,EAAqB,EAAIz3B,EAAYzG,CAAC,CAAE6E,EAAIq5B,AAAe,MAAfA,EAAqB,EAAIz3B,EAAY5B,CAAC,CAC5L,IAAI,CAAC6B,SAAS,CAAC1G,EAAG6E,GAClB,IAAI,CAAC85B,OAAO,EAAI3+B,EAChB,IAAI,CAAC4+B,OAAO,EAAI/5B,EAEhB,IAAI,CAAChI,MAAM,CAAC,CAAA,EAAO,CAAA,EAAO,CAAA,EAC9B,CAcA6hC,OAAO94B,CAAE,CAAEF,CAAE,CAAEo5B,CAAO,CAAEZ,CAAU,CAAE,CAEhC,IAAMyB,EAAW,IAAI,CAACh5B,MAAM,CAAC,EAAE,AAC3Bu3B,AAAe,CAAA,MAAfA,EACIY,AAAY,IAAZA,GACAa,EAAS/yB,cAAc,CAAChH,EAAI,EAAG,GAC/B+5B,EAAS/yB,cAAc,CAAChH,EAAIF,EAAI,KAGhCi6B,EAAS/yB,cAAc,CAAChH,EAAI,EAAG,GAC/B+5B,EAAS/yB,cAAc,CAAChH,EAAIF,EAAI,IAG/Bw4B,AAAe,MAAfA,EACDY,AAAY,IAAZA,GACAa,EAAS/yB,cAAc,CAAC,EAAGlH,EAAI,GAC/Bi6B,EAAS/yB,cAAc,CAAC,EAAGlH,EAAI,KAG/Bi6B,EAAS/yB,cAAc,CAAC,EAAGlH,EAAI,GAC/Bi6B,EAAS/yB,cAAc,CAAC,EAAGlH,EAAI,KAInCi6B,EAAS/yB,cAAc,CAAChH,EAAI,EAAG,GAC/B+5B,EAAS/yB,cAAc,CAAChH,EAAIF,EAAI,GAChCi6B,EAAS/yB,cAAc,CAAC,EAAGlH,EAAI,IAEnCm5B,GAAkB7iC,IAAI,CAAC,IAAI,CAAE,CAAA,EAAO,CAAA,EAAM8iC,EAASl5B,EAAIF,GACvD,IAAI,CAAC1I,OAAO,CAACmuB,WAAW,CAACuI,UAAU,CAAC5rB,MAAM,CAAGpH,KAAK4S,GAAG,CAAC,IAAI,CAACgrB,SAAS,CAAG,IAAI,CAACF,SAAS,EACrF,IAAI,CAACphC,OAAO,CAACmuB,WAAW,CAACuI,UAAU,CAAC7rB,KAAK,CAAGnH,KAAK4S,GAAG,CAAC,IAAI,CAAC+qB,SAAS,CAAG,IAAI,CAACF,SAAS,CACxF,CAUAthC,OAAO4K,CAAS,CAAEi3B,CAAM,CAAEkB,CAAc,CAAE,CACtC,IAAI,CAAC/zB,UAAU,GACV,IAAI,CAAC1O,OAAO,EACb,IAAI,CAACwK,MAAM,GAEXi4B,GACAf,GAAkB7iC,IAAI,CAAC,IAAI,CAAE,CAAA,EAAM,CAAA,GAGnC,IAAI,CAAC0B,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,OAAO,CAAC,IAAI,CAACqlB,UAAU,IAEzC,IAAI,CAAC4Y,SAAS,CAACT,GACf,IAAI,CAACW,aAAa,GAClB,IAAI,CAAC7X,WAAW,CAAC,IAAI,CAAC7gB,MAAM,CAAEc,GAC9B,IAAI,CAAC+f,WAAW,CAAC,IAAI,CAAC9nB,MAAM,CAAE+H,GAC9B,IAAMo4B,EAAoB,IAAI,CAAC7iC,OAAO,CAACmuB,WAAW,CAACuI,UAAU,CAC7D,GAAImM,GAAmBxuB,aACnB,IAAI,CAAC1K,MAAM,CAAC,EAAE,EAAExJ,QAAS,CACzB,IAAM2/B,EAAS,AAAC+C,EAAkBxuB,WAAW,CAAI,EAC3CqiB,EAAa,IAAI,CAAC/sB,MAAM,CAAC,EAAE,CAC3B4K,EAAOmiB,EAAWv2B,OAAO,CAAC2iC,SAAS,CACnCvzB,EAAKgF,CAAI,CAAC,EAAE,CACZ/E,EAAK+E,CAAI,CAAC,EAAE,CACZwuB,EAAKxuB,CAAI,CAAC,EAAE,CACZyuB,EAAKzuB,CAAI,CAAC,EAAE,AAClBhF,CAAAA,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKuwB,EACvBtwB,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKswB,EACvBiD,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKjD,EACvBkD,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKlD,EACvBvwB,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKuwB,EACvBtwB,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKswB,EACvBiD,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKjD,EACvBkD,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKlD,EACvBpJ,EAAWv2B,OAAO,CAACC,IAAI,CAAC,CACpBjC,EAAGoW,CACP,EACJ,CAEA,IAAI,CAACtG,aAAa,CAAChN,OAAO,CAAC,AAACyN,GAAiBA,EAAa7O,MAAM,GACpE,CACA6J,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,IAAI,CAACiB,MAAM,CAAC1I,OAAO,CAAC,AAAC2T,GAASA,EAAKlL,SAAS,CAACd,EAAIF,GACrD,CACJ,CACAq5B,GAAQjjC,SAAS,CAACmqB,cAAc,CAAGmX,GAAcnU,GAAuBntB,SAAS,CAACmqB,cAAc,CAWhG,CACIkF,YAAa,CAKT+S,WAAY,KAMZ/9B,MAAO,EAMPwI,MAAO,EACP+qB,WAAY,CAIRpjB,KAAM,2BAINe,YAAa,EAIbjB,OAAQ,KAAK,CACjB,EAMAif,WAAY,CAKRD,QAAS,CAAA,EAITtxB,OAAQ,EASRoU,UAAW,OAQX8qB,UAAW,OACf,EAKA7N,WAAY,CAKRC,QAAS,CAAA,EAITtxB,OAAQ,EAWRoU,UAAW,OAUX8qB,UAAW,OACf,EACAr9B,MAAO,CAOHyvB,QAAS,CAAA,EAOTrnB,MAAO,CACHghB,SAAU,QACV1Y,MAAO,SACX,EAkDAyG,UAAW,KAAK,CACpB,CACJ,EACA3L,oBAAqB,CACjBzD,WAAY,SAAUtD,CAAM,EACxB,IAAM06B,EAAU,IAAI,CAAC1+B,KAAK,CAAE7C,EAAQ6G,EAAO7G,KAAK,CAAEP,EAAUoH,EAAOpH,OAAO,CAAEmuB,EAAcnuB,EAAQmuB,WAAW,CAAE+S,EAAa/S,EAAY+S,UAAU,CAAE/yB,EAAsBnO,EAAQmO,mBAAmB,CAAEpF,EAAWxI,EAAMwI,QAAQ,CAAE5F,EAAQ5C,EAAM4C,KAAK,CAACgrB,EAAYhrB,KAAK,CAAC,CAAEwI,EAAQpL,EAAMoL,KAAK,CAACwiB,EAAYxiB,KAAK,CAAC,CAAE80B,EAAMvT,GAAY9lB,EAAOszB,QAAQ,CAAEtzB,EAAOuzB,QAAQ,CAAEvzB,EAAOwzB,QAAQ,CAAExzB,EAAOyzB,QAAQ,EAClYoI,EAAU77B,EAAOuzB,QAAQ,CAAEuI,EAAU97B,EAAOyzB,QAAQ,CAAE73B,EAAG6E,EAwB7D,MAvBmB,MAAfq5B,IACAgC,EAAU,AAACzC,CAAAA,EAAI5F,QAAQ,CAAG4F,EAAI7F,QAAQ,AAAD,EAAK,EAE1B,IAAZkH,GACAmB,CAAAA,EAAU77B,EAAOszB,QAAQ,AAAD,GAGb,MAAfwG,IACA+B,EAAUxC,EAAI/F,QAAQ,CACjB,AAAC+F,CAAAA,EAAI9F,QAAQ,CAAG8F,EAAI/F,QAAQ,AAAD,EAAK,EAErB,IAAZoH,GACAoB,CAAAA,EAAU97B,EAAOwzB,QAAQ,AAAD,GAG5B7xB,GACA/F,EAAI2I,EAAMmB,QAAQ,CAACo2B,GACnBr7B,EAAI1E,EAAM2J,QAAQ,CAACm2B,KAGnBjgC,EAAIG,EAAM2J,QAAQ,CAACm2B,GACnBp7B,EAAI8D,EAAMmB,QAAQ,CAACo2B,IAEhB,CACHlgC,EAAGA,EAAKmL,EAAoBtD,KAAK,CAAG,EACpChD,EAAGA,EAAKsG,EAAoBrD,MAAM,CAAG,CACzC,CACJ,EACA/D,OAAQ,CACJgxB,KAAM,SAAUrxB,CAAC,CAAEU,CAAM,EACrB,IAAMqC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAAIw6B,EAAa95B,EAAOpH,OAAO,CAACmuB,WAAW,CAAC+S,UAAU,CAAE99B,EAAQ,IAAI,CAACA,KAAK,CAAEJ,EAAIk+B,AAAe,MAAfA,EAAqB,EAAIz3B,EAAYzG,CAAC,CAAE6E,EAAIq5B,AAAe,MAAfA,EAAqB,EAAIz3B,EAAY5B,CAAC,CAClNT,EAAOs6B,MAAM,CAAC1+B,EAAG6E,EAAGzE,EAAO89B,GAC3B95B,EAAO66B,OAAO,EAAIj/B,EAClBoE,EAAO86B,OAAO,EAAIr6B,EAClBT,EAAOvH,MAAM,CAAC,CAAA,EAAO,CAAA,EACzB,CACJ,CACJ,CACJ,GACAosB,GAAuBvmB,KAAK,CAACqxB,OAAO,CAAGgL,GAkCV,IAAM7iC,GAA6BE,IAGtD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}