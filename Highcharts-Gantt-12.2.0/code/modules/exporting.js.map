{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/exporting\n * @requires highcharts\n *\n * Exporting module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/exporting\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"AST\"],amd1[\"Chart\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/exporting\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"AST\"], root[\"_Highcharts\"][\"Chart\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"], root[\"Highcharts\"][\"Chart\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__, __WEBPACK_EXTERNAL_MODULE__960__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ exporting_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\n;// ./code/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(chart) {\n            this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        addUpdate(updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        }\n        /**\n         * @private\n         */\n        update(options, redraw) {\n            this.updates.forEach((updateFn) => {\n                updateFn.call(this.chart, options, redraw);\n            });\n        }\n    }\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es-modules/Extensions/Exporting/ExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isTouchDevice } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  API Options\n *\n * */\n// Add the export related options\n/**\n * Options for the exporting module. For an overview on the matter, see\n * [the docs](https://www.highcharts.com/docs/export-module/export-module-overview) and\n * read our [Fair Usage Policy](https://www.highcharts.com/docs/export-module/privacy-disclaimer-export).\n *\n * @requires     modules/exporting\n * @optionparent exporting\n */\nconst exporting = {\n    /**\n     * Experimental setting to allow HTML inside the chart (added through\n     * the `useHTML` options), directly in the exported image. This allows\n     * you to preserve complicated HTML structures like tables or bi-directional\n     * text in exported charts.\n     *\n     * Disclaimer: The HTML is rendered in a `foreignObject` tag in the\n     * generated SVG. The official export server is based on PhantomJS,\n     * which supports this, but other SVG clients, like Batik, does not\n     * support it. This also applies to downloaded SVG that you want to\n     * open in a desktop client.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.8\n     * @apioption exporting.allowHTML\n     */\n    /**\n     * Allows the end user to sort the data table by clicking on column headers.\n     *\n     * @since 10.3.3\n     * @apioption exporting.allowTableSorting\n     */\n    allowTableSorting: true,\n    /**\n     * Allow exporting a chart retaining any user-applied CSS.\n     *\n     * Note that this is is default behavior in [styledMode](#chart.styledMode).\n     *\n     * @see [styledMode](#chart.styledMode)\n     *\n     * @sample {highcharts} highcharts/exporting/apply-stylesheets/\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since 12.0.0\n     * @apioption exporting.applyStyleSheets\n     */\n    /**\n     * Additional chart options to be merged into the chart before exporting to\n     * an image format. This does not apply to printing the chart via the export\n     * menu.\n     *\n     * For example, a common use case is to add data labels to improve\n     * readability of the exported chart, or to add a printer-friendly color\n     * scheme to exported PDFs.\n     *\n     * @sample {highcharts} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     * @sample {highstock} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     *\n     * @type      {Highcharts.Options}\n     * @apioption exporting.chartOptions\n     */\n    /**\n     * Whether to enable the exporting module. Disabling the module will\n     * hide the context button, but API methods will still be available.\n     *\n     * @sample {highcharts} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     * @sample {highstock} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     2.0\n     * @apioption exporting.enabled\n     */\n    /**\n     * Function to call if the offline-exporting module fails to export\n     * a chart on the client side, and [fallbackToExportServer](\n     * #exporting.fallbackToExportServer) is disabled. If left undefined, an\n     * exception is thrown instead. Receives two parameters, the exporting\n     * options, and the error from the module.\n     *\n     * @see [fallbackToExportServer](#exporting.fallbackToExportServer)\n     *\n     * @type      {Highcharts.ExportingErrorCallbackFunction}\n     * @since     5.0.0\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.error\n     */\n    /**\n     * Whether or not to fall back to the export server if the offline-exporting\n     * module is unable to export the chart on the client side. This happens for\n     * certain browsers, and certain features (e.g.\n     * [allowHTML](#exporting.allowHTML)), depending on the image type exporting\n     * to. For very complex charts, it is possible that export can fail in\n     * browsers that don't support Blob objects, due to data URL length limits.\n     * It is recommended to define the [exporting.error](#exporting.error)\n     * handler if disabling fallback, in order to notify users in case export\n     * fails.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     4.1.8\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.fallbackToExportServer\n     */\n    /**\n     * The filename, without extension, to use for the exported chart.\n     *\n     * @sample {highcharts} highcharts/exporting/filename/\n     *         Custom file name\n     * @sample {highstock} highcharts/exporting/filename/\n     *         Custom file name\n     *\n     * @type      {string}\n     * @default   chart\n     * @since     2.0\n     * @apioption exporting.filename\n     */\n    /**\n     * Highcharts v11.2.0 and older. An object containing additional key value\n     * data for the POST form that sends the SVG to the export server. For\n     * example, a `target` can be set to make sure the generated image is\n     * received in another frame, or a custom `enctype` or `encoding` can be\n     * set.\n     *\n     * With Highcharts v11.3.0, the `fetch` API replaced the old HTML form. To\n     * modify the request, now use [fetchOptions](#exporting.fetchOptions)\n     * instead.\n     *\n     * @deprecated\n     * @type      {Highcharts.HTMLAttributes}\n     * @since     3.0.8\n     * @apioption exporting.formAttributes\n     */\n    /**\n     * Options for the fetch request used when sending the SVG to the export\n     * server.\n     *\n     * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/fetch)\n     * for more information\n     *\n     * @type {Object}\n     * @since 11.3.0\n     * @apioption exporting.fetchOptions\n     */\n    /**\n     * Path where Highcharts will look for export module dependencies to\n     * load on demand if they don't already exist on `window`. Should currently\n     * point to location of [CanVG](https://github.com/canvg/canvg) library,\n     * [jsPDF](https://github.com/parallax/jsPDF) and\n     * [svg2pdf.js](https://github.com/yWorks/svg2pdf.js), required for client\n     * side export in certain browsers.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/{version}/lib\n     * @since     5.0.0\n     * @apioption exporting.libURL\n     */\n    /**\n     * Analogous to [sourceWidth](#exporting.sourceWidth).\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceHeight\n     */\n    /**\n     * The width of the original chart when exported, unless an explicit\n     * [chart.width](#chart.width) is set, or a pixel width is set on the\n     * container. The width exported raster image is then multiplied by\n     * [scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highstock} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highmaps} maps/exporting/sourcewidth/\n     *         Source size demo\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceWidth\n     */\n    /**\n     * The pixel width of charts exported to PNG or JPG. As of Highcharts\n     * 3.0, the default pixel width is a function of the [chart.width](\n     * #chart.width) or [exporting.sourceWidth](#exporting.sourceWidth) and the\n     * [exporting.scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/width/\n     *         Export to 200px wide images\n     * @sample {highstock} highcharts/exporting/width/\n     *         Export to 200px wide images\n     *\n     * @type      {number}\n     * @since     2.0\n     * @apioption exporting.width\n     */\n    /**\n     * Default MIME type for exporting if `chart.exportChart()` is called\n     * without specifying a `type` option. Possible values are `image/png`,\n     *  `image/jpeg`, `application/pdf` and `image/svg+xml`.\n     *\n     * @type  {Highcharts.ExportingMimeTypeValue}\n     * @since 2.0\n     */\n    type: 'image/png',\n    /**\n     * The URL for the server module converting the SVG string to an image\n     * format. By default this points to Highchart's free web service.\n     *\n     * @since 2.0\n     */\n    url: `https://export-svg.highcharts.com?v=${(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).version}`,\n    /**\n     * Settings for a custom font for the exported PDF, when using the\n     * `offline-exporting` module. This is used for languages containing\n     * non-ASCII characters, like Chinese, Russian, Japanese etc.\n     *\n     * As described in the [jsPDF\n     * docs](https://github.com/parallax/jsPDF#use-of-unicode-characters--utf-8),\n     * the 14 standard fonts in PDF are limited to the ASCII-codepage.\n     * Therefore, in order to support other text in the exported PDF, one or\n     * more TTF font files have to be passed on to the exporting module.\n     *\n     * See more in [the\n     * docs](https://www.highcharts.com/docs/export-module/client-side-export).\n     *\n     * @sample {highcharts} highcharts/exporting/offline-download-pdffont/\n     *         Download PDF in a language containing non-Latin characters.\n     *\n     * @since 10.0.0\n     * @requires modules/offline-exporting\n     */\n    pdfFont: {\n        /**\n         * The TTF font file for normal `font-style`. If font variations like\n         * `bold` or `italic` are not defined, the `normal` font will be used\n         * for those too.\n         *\n         * @type string|undefined\n         */\n        normal: void 0,\n        /**\n         * The TTF font file for bold text.\n         *\n         * @type string|undefined\n         */\n        bold: void 0,\n        /**\n         * The TTF font file for bold and italic text.\n         *\n         * @type string|undefined\n         */\n        bolditalic: void 0,\n        /**\n         * The TTF font file for italic text.\n         *\n         * @type string|undefined\n         */\n        italic: void 0\n    },\n    /**\n     * When printing the chart from the menu item in the burger menu, if\n     * the on-screen chart exceeds this width, it is resized. After printing\n     * or cancelled, it is restored. The default width makes the chart\n     * fit into typical paper format. Note that this does not affect the\n     * chart when printing the web page as a whole.\n     *\n     * @since 4.2.5\n     */\n    printMaxWidth: 780,\n    /**\n     * Defines the scale or zoom factor for the exported image compared\n     * to the on-screen display. While for instance a 600px wide chart\n     * may look good on a website, it will look bad in print. The default\n     * scale of 2 makes this chart export to a 1200px PNG or JPG.\n     *\n     * @see [chart.width](#chart.width)\n     * @see [exporting.sourceWidth](#exporting.sourceWidth)\n     *\n     * @sample {highcharts} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highstock} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highmaps} maps/exporting/scale/\n     *         Scale demonstrated\n     *\n     * @since 3.0\n     */\n    scale: 2,\n    /**\n     * Options for the export related buttons, print and export. In addition\n     * to the default buttons listed here, custom buttons can be added.\n     * See [navigation.buttonOptions](#navigation.buttonOptions) for general\n     * options.\n     *\n     * @type     {Highcharts.Dictionary<*>}\n     * @requires modules/exporting\n     */\n    buttons: {\n        /**\n         * Options for the export button.\n         *\n         * In styled mode, export button styles can be applied with the\n         * `.highcharts-contextbutton` class.\n         *\n         * @declare  Highcharts.ExportingButtonsOptionsObject\n         * @extends  navigation.buttonOptions\n         * @requires modules/exporting\n         */\n        contextButton: {\n            /**\n             * A click handler callback to use on the button directly instead of\n             * the popup menu.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-onclick/\n             *         Skip the menu and export the chart directly\n             *\n             * @type      {Function}\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.onclick\n             */\n            /**\n             * See [navigation.buttonOptions.symbolFill](\n             * #navigation.buttonOptions.symbolFill).\n             *\n             * @type      {Highcharts.ColorString}\n             * @default   #666666\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.symbolFill\n             */\n            /**\n             * The horizontal position of the button relative to the `align`\n             * option.\n             *\n             * @type      {number}\n             * @default   -10\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.x\n             */\n            /**\n             * The class name of the context button.\n             */\n            className: 'highcharts-contextbutton',\n            /**\n             * The class name of the menu appearing from the button.\n             */\n            menuClassName: 'highcharts-contextmenu',\n            /**\n             * The symbol for the button. Points to a definition function in\n             * the `Highcharts.Renderer.symbols` collection. The default\n             * `menu` function is part of the exporting module. Possible\n             * values are \"circle\", \"square\", \"diamond\", \"triangle\",\n             * \"triangle-down\", \"menu\", \"menuball\" or custom shape.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-symbol/\n             *         Use a circle for symbol\n             * @sample highcharts/exporting/buttons-contextbutton-symbol-custom/\n             *         Custom shape as symbol\n             *\n             * @type  {Highcharts.SymbolKeyValue|\"menu\"|\"menuball\"|string}\n             * @since 2.0\n             */\n            symbol: 'menu',\n            /**\n             * The key to a [lang](#lang) option setting that is used for the\n             * button's title tooltip. When the key is `contextButtonTitle`, it\n             * refers to [lang.contextButtonTitle](#lang.contextButtonTitle)\n             * that defaults to \"Chart context menu\".\n             *\n             * @since 6.1.4\n             */\n            titleKey: 'contextButtonTitle',\n            /**\n             * A collection of strings pointing to config options for the menu\n             * items. The config options are defined in the\n             * `menuItemDefinitions` option.\n             *\n             * By default, there is the \"View in full screen\" and \"Print\" menu\n             * items, plus one menu item for each of the available export types.\n             *\n             * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             *\n             * @type    {Array<string>}\n             * @default [\"viewFullscreen\", \"printChart\", \"separator\", \"downloadPNG\", \"downloadJPEG\", \"downloadSVG\"]\n             * @since   2.0\n             */\n            menuItems: [\n                'viewFullscreen',\n                'printChart',\n                'separator',\n                'downloadPNG',\n                'downloadJPEG',\n                'downloadSVG'\n            ]\n        }\n    },\n    /**\n     * An object consisting of definitions for the menu items in the context\n     * menu. Each key value pair has a `key` that is referenced in the\n     * [menuItems](#exporting.buttons.contextButton.menuItems) setting,\n     * and a `value`, which is an object with the following properties:\n     *\n     * - **onclick:** The click handler for the menu item\n     *\n     * - **text:** The text for the menu item\n     *\n     * - **textKey:** If internationalization is required, the key to a language\n     *   string\n     *\n     * Custom text for the \"exitFullScreen\" can be set only in lang options\n     * (it is not a separate button).\n     *\n     * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     *\n     *\n     * @type    {Highcharts.Dictionary<Highcharts.ExportingMenuObject>}\n     * @default {\"viewFullscreen\": {}, \"printChart\": {}, \"separator\": {}, \"downloadPNG\": {}, \"downloadJPEG\": {}, \"downloadPDF\": {}, \"downloadSVG\": {}}\n     * @since   5.0.13\n     */\n    menuItemDefinitions: {\n        /**\n         * @ignore\n         */\n        viewFullscreen: {\n            textKey: 'viewFullscreen',\n            onclick: function () {\n                if (this.fullscreen) {\n                    this.fullscreen.toggle();\n                }\n            }\n        },\n        /**\n         * @ignore\n         */\n        printChart: {\n            textKey: 'printChart',\n            onclick: function () {\n                this.print();\n            }\n        },\n        /**\n         * @ignore\n         */\n        separator: {\n            separator: true\n        },\n        /**\n         * @ignore\n         */\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: function () {\n                this.exportChart();\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: function () {\n                this.exportChart({\n                    type: 'application/pdf'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/svg+xml'\n                });\n            }\n        }\n    }\n};\n// Add language\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * Exporting module only. The text for the menu item to view the chart\n     * in full screen.\n     *\n     * @since 8.0.1\n     */\n    viewFullscreen: 'View in full screen',\n    /**\n     * Exporting module only. The text for the menu item to exit the chart\n     * from full screen.\n     *\n     * @since 8.0.1\n     */\n    exitFullscreen: 'Exit from full screen',\n    /**\n     * Exporting module only. The text for the menu item to print the chart.\n     *\n     * @since    3.0.1\n     * @requires modules/exporting\n     */\n    printChart: 'Print chart',\n    /**\n     * Exporting module only. The text for the PNG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPNG: 'Download PNG image',\n    /**\n     * Exporting module only. The text for the JPEG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadJPEG: 'Download JPEG image',\n    /**\n     * Exporting module only. The text for the PDF download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPDF: 'Download PDF document',\n    /**\n     * Exporting module only. The text for the SVG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadSVG: 'Download SVG vector image',\n    /**\n     * Exporting module menu. The tooltip title for the context menu holding\n     * print and export menu items.\n     *\n     * @since    3.0\n     * @requires modules/exporting\n     */\n    contextButtonTitle: 'Chart context menu'\n};\n/**\n * A collection of options for buttons and menus appearing in the exporting\n * module or in Stock Tools.\n *\n * @requires     modules/exporting\n * @optionparent navigation\n */\nconst navigation = {\n    /**\n     * A collection of options for buttons appearing in the exporting\n     * module.\n     *\n     * In styled mode, the buttons are styled with the\n     * `.highcharts-contextbutton` and `.highcharts-button-symbol` classes.\n     *\n     * @requires modules/exporting\n     */\n    buttonOptions: {\n        /**\n         * Whether to enable buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-enabled/\n         *         Exporting module loaded but buttons disabled\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     2.0\n         * @apioption navigation.buttonOptions.enabled\n         */\n        /**\n         * The pixel size of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolSize: 14,\n        /**\n         * The x position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolX: 14.5,\n        /**\n         * The y position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolY: 13.5,\n        /**\n         * Alignment for the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-align/\n         *         Center aligned\n         *\n         * @type  {Highcharts.AlignValue}\n         * @since 2.0\n         */\n        align: 'right',\n        /**\n         * The pixel spacing between buttons, and between the context button and\n         * the title.\n         *\n         * @sample highcharts/title/widthadjust\n         *         Adjust the spacing when using text button\n         * @since 2.0\n         */\n        buttonSpacing: 5,\n        /**\n         * Pixel height of the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        height: 28,\n        /**\n         * A text string to add to the individual button.\n         *\n         * @sample highcharts/exporting/buttons-text/\n         *         Full text button\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         * @sample highcharts/exporting/buttons-text-symbol/\n         *         Combined symbol and text\n         *\n         * @type      {string}\n         * @default   null\n         * @since     3.0\n         * @apioption navigation.buttonOptions.text\n         */\n        /**\n         * Whether to use HTML for rendering the button. HTML allows for things\n         * like inline CSS or image-based icons.\n         *\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         *\n         * @type      boolean\n         * @default   false\n         * @since 10.3.0\n         * @apioption navigation.buttonOptions.useHTML\n         */\n        /**\n         * The vertical offset of the button's position relative to its\n         * `verticalAlign`. By default adjusted for the chart title alignment.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @since     2.0\n         * @apioption navigation.buttonOptions.y\n         */\n        y: -5,\n        /**\n         * The vertical alignment of the buttons. Can be one of `\"top\"`,\n         * `\"middle\"` or `\"bottom\"`.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @type  {Highcharts.VerticalAlignValue}\n         * @since 2.0\n         */\n        verticalAlign: 'top',\n        /**\n         * The pixel width of the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        width: 28,\n        /**\n         * Fill color for the symbol within the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolfill/\n         *         Blue symbol stroke for one of the buttons\n         *\n         * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since 2.0\n         */\n        symbolFill: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The color of the symbol's stroke or line.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolstroke/\n         *         Blue symbol stroke\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 2.0\n         */\n        symbolStroke: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The pixel stroke width of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolStrokeWidth: 3,\n        /**\n         * A configuration object for the button theme. The object accepts\n         * SVG properties like `stroke-width`, `stroke` and `fill`.\n         * Tri-state button styles are supported by the `states.hover` and\n         * `states.select` objects.\n         *\n         * @sample highcharts/navigation/buttonoptions-theme/\n         *         Theming the buttons\n         *\n         * @requires modules/exporting\n         *\n         * @since 3.0\n         */\n        theme: {\n            /**\n             * The default fill exists only to capture hover events.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            /**\n             * Padding for the button.\n             */\n            padding: 5,\n            /**\n             * Default stroke for the buttons.\n             *\n             * @type      {Highcharts.ColorString}\n             */\n            stroke: 'none',\n            /**\n             * Default stroke linecap for the buttons.\n             */\n            'stroke-linecap': 'round'\n        }\n    },\n    /**\n     * CSS styles for the popup menu appearing by default when the export\n     * icon is clicked. This menu is rendered in HTML.\n     *\n     * @see In styled mode, the menu is styled with the `.highcharts-menu`\n     *      class.\n     *\n     * @sample highcharts/navigation/menustyle/\n     *         Light gray menu background\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#ffffff\", \"borderRadius\": \"3px\", \"padding\": \"0.5em\"}\n     * @since   2.0\n     */\n    menuStyle: {\n        /** @ignore-option */\n        border: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        background: \"#ffffff\" /* Palette.backgroundColor */,\n        /** @ignore-option */\n        padding: '0.5em'\n    },\n    /**\n     * CSS styles for the individual items within the popup menu appearing\n     * by default when the export icon is clicked. The menu items are\n     * rendered in HTML. Font size defaults to `11px` on desktop and `14px`\n     * on touch devices.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample {highcharts} highcharts/navigation/menuitemstyle/\n     *         Add a grey stripe to the left\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"padding\": \"0.5em\", \"color\": \"#333333\", \"background\": \"none\", \"borderRadius\": \"3px\", \"fontSize\": \"0.8em\", \"transition\": \"background 250ms, color 250ms\"}\n     * @since   2.0\n     */\n    menuItemStyle: {\n        /** @ignore-option */\n        background: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        color: \"#333333\" /* Palette.neutralColor80 */,\n        /** @ignore-option */\n        padding: '0.5em',\n        /** @ignore-option */\n        fontSize: isTouchDevice ? '0.9em' : '0.8em',\n        /** @ignore-option */\n        transition: 'background 250ms, color 250ms'\n    },\n    /**\n     * CSS styles for the hover state of the individual items within the\n     * popup menu appearing by default when the export icon is clicked. The\n     * menu items are rendered in HTML.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample highcharts/navigation/menuitemhoverstyle/\n     *         Bold text on hover\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#f2f2f2\" }\n     * @since   2.0\n     */\n    menuItemHoverStyle: {\n        /** @ignore-option */\n        background: \"#f2f2f2\" /* Palette.neutralColor5 */\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ExportingDefaults = {\n    exporting,\n    lang,\n    navigation\n};\n/* harmony default export */ const Exporting_ExportingDefaults = (ExportingDefaults);\n\n;// ./code/es-modules/Extensions/Exporting/ExportingSymbols.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ExportingSymbols;\n(function (ExportingSymbols) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    const modifiedClasses = [];\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SVGRendererClass) {\n        if (modifiedClasses.indexOf(SVGRendererClass) === -1) {\n            modifiedClasses.push(SVGRendererClass);\n            const symbols = SVGRendererClass.prototype.symbols;\n            symbols.menu = menu;\n            symbols.menuball = menuball.bind(symbols);\n        }\n    }\n    ExportingSymbols.compose = compose;\n    /**\n     * @private\n     */\n    function menu(x, y, width, height) {\n        const arr = [\n            ['M', x, y + 2.5],\n            ['L', x + width, y + 2.5],\n            ['M', x, y + height / 2 + 0.5],\n            ['L', x + width, y + height / 2 + 0.5],\n            ['M', x, y + height - 1.5],\n            ['L', x + width, y + height - 1.5]\n        ];\n        return arr;\n    }\n    /**\n     * @private\n     */\n    function menuball(x, y, width, height) {\n        const h = (height / 3) - 2;\n        let path = [];\n        path = path.concat(this.circle(width - h, y, h, h), this.circle(width - h, y + h + 4, h, h), this.circle(width - h, y + 2 * (h + 4), h, h));\n        return path;\n    }\n})(ExportingSymbols || (ExportingSymbols = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_ExportingSymbols = (ExportingSymbols);\n\n;// ./code/es-modules/Extensions/Exporting/Fullscreen.js\n/* *\n *\n *  (c) 2009-2025 Rafal Sebestjanski\n *\n *  Full screen for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/**\n * The module allows user to enable display chart in full screen mode.\n * Used in StockTools too.\n * Based on default solutions in browsers.\n */\n\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent, fireEvent, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onChartBeforeRender() {\n    /**\n     * @name Highcharts.Chart#fullscreen\n     * @type {Highcharts.Fullscreen}\n     * @requires modules/full-screen\n     */\n    this.fullscreen = new Fullscreen(this);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles displaying chart's container in the fullscreen mode.\n *\n * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n *\n * @class\n * @name Highcharts.Fullscreen\n *\n * @requires modules/exporting\n */\nclass Fullscreen {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Prepares the chart class to support fullscreen.\n     *\n     * @param {typeof_Highcharts.Chart} ChartClass\n     * The chart class to decorate with fullscreen support.\n     */\n    static compose(ChartClass) {\n        if (pushUnique(composed, 'Fullscreen')) {\n            // Initialize fullscreen\n            addEvent(ChartClass, 'beforeRender', onChartBeforeRender);\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(chart) {\n        /**\n         * Chart managed by the fullscreen controller.\n         * @name Highcharts.Fullscreen#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The flag is set to `true` when the chart is displayed in\n         * the fullscreen mode.\n         *\n         * @name Highcharts.Fullscreen#isOpen\n         * @type {boolean|undefined}\n         * @since 8.0.1\n         */\n        this.isOpen = false;\n        const container = chart.renderTo;\n        // Hold event and methods available only for a current browser.\n        if (!this.browserProps) {\n            if (typeof container.requestFullscreen === 'function') {\n                this.browserProps = {\n                    fullscreenChange: 'fullscreenchange',\n                    requestFullscreen: 'requestFullscreen',\n                    exitFullscreen: 'exitFullscreen'\n                };\n            }\n            else if (container.mozRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'mozfullscreenchange',\n                    requestFullscreen: 'mozRequestFullScreen',\n                    exitFullscreen: 'mozCancelFullScreen'\n                };\n            }\n            else if (container.webkitRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'webkitfullscreenchange',\n                    requestFullscreen: 'webkitRequestFullScreen',\n                    exitFullscreen: 'webkitExitFullscreen'\n                };\n            }\n            else if (container.msRequestFullscreen) {\n                this.browserProps = {\n                    fullscreenChange: 'MSFullscreenChange',\n                    requestFullscreen: 'msRequestFullscreen',\n                    exitFullscreen: 'msExitFullscreen'\n                };\n            }\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Stops displaying the chart in fullscreen mode.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function    Highcharts.Fullscreen#close\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    close() {\n        const fullscreen = this, chart = fullscreen.chart, optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenClose', null, function () {\n            // Don't fire exitFullscreen() when user exited\n            // using 'Escape' button.\n            if (fullscreen.isOpen &&\n                fullscreen.browserProps &&\n                chart.container.ownerDocument instanceof Document) {\n                chart.container.ownerDocument[fullscreen.browserProps.exitFullscreen]();\n            }\n            // Unbind event as it's necessary only before exiting\n            // from fullscreen.\n            if (fullscreen.unbindFullscreenEvent) {\n                fullscreen.unbindFullscreenEvent = fullscreen\n                    .unbindFullscreenEvent();\n            }\n            chart.setSize(fullscreen.origWidth, fullscreen.origHeight, false);\n            fullscreen.origWidth = void 0;\n            fullscreen.origHeight = void 0;\n            optionsChart.width = fullscreen.origWidthOption;\n            optionsChart.height = fullscreen.origHeightOption;\n            fullscreen.origWidthOption = void 0;\n            fullscreen.origHeightOption = void 0;\n            fullscreen.isOpen = false;\n            fullscreen.setButtonText();\n        });\n    }\n    /**\n     * Displays the chart in fullscreen mode.\n     * When fired customly by user before exporting context button is created,\n     * button's text will not be replaced - it's on the user side.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function Highcharts.Fullscreen#open\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    open() {\n        const fullscreen = this, chart = fullscreen.chart, optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenOpen', null, function () {\n            if (optionsChart) {\n                fullscreen.origWidthOption = optionsChart.width;\n                fullscreen.origHeightOption = optionsChart.height;\n            }\n            fullscreen.origWidth = chart.chartWidth;\n            fullscreen.origHeight = chart.chartHeight;\n            // Handle exitFullscreen() method when user clicks 'Escape' button.\n            if (fullscreen.browserProps) {\n                const unbindChange = addEvent(chart.container.ownerDocument, // Chart's document\n                fullscreen.browserProps.fullscreenChange, function () {\n                    // Handle lack of async of browser's\n                    // fullScreenChange event.\n                    if (fullscreen.isOpen) {\n                        fullscreen.isOpen = false;\n                        fullscreen.close();\n                    }\n                    else {\n                        chart.setSize(null, null, false);\n                        fullscreen.isOpen = true;\n                        fullscreen.setButtonText();\n                    }\n                });\n                const unbindDestroy = addEvent(chart, 'destroy', unbindChange);\n                fullscreen.unbindFullscreenEvent = () => {\n                    unbindChange();\n                    unbindDestroy();\n                };\n                const promise = chart.renderTo[fullscreen.browserProps.requestFullscreen]();\n                if (promise) {\n                    promise['catch'](function () {\n                        alert(// eslint-disable-line no-alert\n                        'Full screen is not supported inside a frame.');\n                    });\n                }\n            }\n        });\n    }\n    /**\n     * Replaces the exporting context button's text when toogling the\n     * fullscreen mode.\n     *\n     * @private\n     *\n     * @since 8.0.1\n     *\n     * @requires modules/full-screen\n     */\n    setButtonText() {\n        const chart = this.chart, exportDivElements = chart.exportDivElements, exportingOptions = chart.options.exporting, menuItems = (exportingOptions &&\n            exportingOptions.buttons &&\n            exportingOptions.buttons.contextButton.menuItems), lang = chart.options.lang;\n        if (exportingOptions &&\n            exportingOptions.menuItemDefinitions &&\n            lang &&\n            lang.exitFullscreen &&\n            lang.viewFullscreen &&\n            menuItems &&\n            exportDivElements) {\n            const exportDivElement = exportDivElements[menuItems.indexOf('viewFullscreen')];\n            if (exportDivElement) {\n                highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(exportDivElement, !this.isOpen ?\n                    (exportingOptions.menuItemDefinitions.viewFullscreen\n                        .text ||\n                        lang.viewFullscreen) : lang.exitFullscreen);\n            }\n        }\n    }\n    /**\n     * Toggles displaying the chart in fullscreen mode.\n     * By default, when the exporting module is enabled, a context button with\n     * a drop down menu in the upper right corner accesses this function.\n     * Exporting module required.\n     *\n     * @since 8.0.1\n     *\n     * @sample      highcharts/members/chart-togglefullscreen/\n     *              Toggle fullscreen mode from a HTML button\n     *\n     * @function Highcharts.Fullscreen#toggle\n     * @requires    modules/full-screen\n     */\n    toggle() {\n        const fullscreen = this;\n        if (!fullscreen.isOpen) {\n            fullscreen.open();\n        }\n        else {\n            fullscreen.close();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_Fullscreen = (Fullscreen);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired when closing the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenCloseCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired when opening the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenOpenCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n(''); // Keeps doclets above separated from following code\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires when a fullscreen is closed through the context menu item,\n * or a fullscreen is closed on the `Escape` button click,\n * or the `Chart.fullscreen.close` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenCloseCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenClose\n */\n/**\n * Fires when a fullscreen is opened through the context menu item,\n * or the `Chart.fullscreen.open` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenOpenCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenOpen\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/Core/HttpUtilities.js\n/* *\n *\n *  (c) 2010-2025 Christer Vasseng, Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { discardElement, objectEach } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Perform an Ajax call.\n *\n * @function Highcharts.ajax\n *\n * @param {Highcharts.AjaxSettingsObject} settings\n *        The Ajax settings to use.\n *\n * @return {false|undefined}\n *         Returns false, if error occurred.\n */\nfunction ajax(settings) {\n    const headers = {\n        json: 'application/json',\n        xml: 'application/xml',\n        text: 'text/plain',\n        octet: 'application/octet-stream'\n    }, r = new XMLHttpRequest();\n    /**\n     * Private error handler.\n     * @private\n     * @param {XMLHttpRequest} xhr\n     * Internal request object.\n     * @param {string|Error} err\n     * Occurred error.\n     */\n    function handleError(xhr, err) {\n        if (settings.error) {\n            settings.error(xhr, err);\n        }\n        else {\n            // @todo Maybe emit a highcharts error event here\n        }\n    }\n    if (!settings.url) {\n        return false;\n    }\n    r.open((settings.type || 'get').toUpperCase(), settings.url, true);\n    if (!settings.headers?.['Content-Type']) {\n        r.setRequestHeader('Content-Type', headers[settings.dataType || 'json'] || headers.text);\n    }\n    objectEach(settings.headers, function (val, key) {\n        r.setRequestHeader(key, val);\n    });\n    if (settings.responseType) {\n        r.responseType = settings.responseType;\n    }\n    // @todo lacking timeout handling\n    r.onreadystatechange = function () {\n        let res;\n        if (r.readyState === 4) {\n            if (r.status === 200) {\n                if (settings.responseType !== 'blob') {\n                    res = r.responseText;\n                    if (settings.dataType === 'json') {\n                        try {\n                            res = JSON.parse(res);\n                        }\n                        catch (e) {\n                            if (e instanceof Error) {\n                                return handleError(r, e);\n                            }\n                        }\n                    }\n                }\n                return settings.success?.(res, r);\n            }\n            handleError(r, r.responseText);\n        }\n    };\n    if (settings.data && typeof settings.data !== 'string') {\n        settings.data = JSON.stringify(settings.data);\n    }\n    r.send(settings.data);\n}\n/**\n * Get a JSON resource over XHR, also supporting CORS without preflight.\n *\n * @function Highcharts.getJSON\n * @param {string} url\n *        The URL to load.\n * @param {Function} success\n *        The success callback. For error handling, use the `Highcharts.ajax`\n *        function instead.\n */\nfunction getJSON(url, success) {\n    HttpUtilities.ajax({\n        url: url,\n        success: success,\n        dataType: 'json',\n        headers: {\n            // Override the Content-Type to avoid preflight problems with CORS\n            // in the Highcharts demos\n            'Content-Type': 'text/plain'\n        }\n    });\n}\n/**\n * The post utility\n *\n * @private\n * @function Highcharts.post\n *\n * @param {string} url\n * Post URL\n *\n * @param {Object} data\n * Post data\n *\n * @param {RequestInit} [fetchOptions]\n * Additional attributes for the post request\n */\n/**\n *\n */\nfunction post(url, data, fetchOptions) {\n    const formData = new win.FormData();\n    // Add the data\n    objectEach(data, function (val, name) {\n        formData.append(name, val);\n    });\n    formData.append('b64', 'true');\n    const { filename, type } = data;\n    return win.fetch(url, {\n        method: 'POST',\n        body: formData,\n        ...fetchOptions\n    }).then((res) => {\n        if (res.ok) {\n            res.text().then((text) => {\n                const link = document.createElement('a');\n                link.href = `data:${type};base64,${text}`;\n                link.download = filename;\n                link.click();\n                discardElement(link);\n            });\n        }\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst HttpUtilities = {\n    ajax,\n    getJSON,\n    post\n};\n/* harmony default export */ const Core_HttpUtilities = (HttpUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @interface Highcharts.AjaxSettingsObject\n */ /**\n* The payload to send.\n*\n* @name Highcharts.AjaxSettingsObject#data\n* @type {string|Highcharts.Dictionary<any>|undefined}\n*/ /**\n* The data type expected.\n* @name Highcharts.AjaxSettingsObject#dataType\n* @type {\"json\"|\"xml\"|\"text\"|\"octet\"|undefined}\n*/ /**\n* Function to call on error.\n* @name Highcharts.AjaxSettingsObject#error\n* @type {Function|undefined}\n*/ /**\n* The headers; keyed on header name.\n* @name Highcharts.AjaxSettingsObject#headers\n* @type {Highcharts.Dictionary<string>|undefined}\n*/ /**\n* Function to call on success.\n* @name Highcharts.AjaxSettingsObject#success\n* @type {Function|undefined}\n*/ /**\n* The HTTP method to use. For example GET or POST.\n* @name Highcharts.AjaxSettingsObject#type\n* @type {string|undefined}\n*/ /**\n* The URL to call.\n* @name Highcharts.AjaxSettingsObject#url\n* @type {string}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Exporting/Exporting.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\n\nconst { doc, SVG_NS, win: Exporting_win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { addEvent: Exporting_addEvent, css, createElement, discardElement: Exporting_discardElement, extend, find, fireEvent: Exporting_fireEvent, isObject, merge, objectEach: Exporting_objectEach, pick, removeEvent, splat, uniqueKey } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar Exporting;\n(function (Exporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // These CSS properties are not inlined. Remember camelCase.\n    const inlineDenylist = [\n        /-/, // In Firefox, both hyphened and camelCased names are listed\n        /^(clipPath|cssText|d|height|width)$/, // Full words\n        /^font$/, // More specific props are set\n        /[lL]ogical(Width|Height)$/,\n        /^parentRule$/,\n        /^(cssRules|ownerRules)$/, // #19516 read-only properties\n        /perspective/,\n        /TapHighlightColor/,\n        /^transition/,\n        /^length$/, // #7700\n        /^\\d+$/ // #17538\n    ];\n    // These ones are translated to attributes rather than styles\n    const inlineToAttributes = [\n        'fill',\n        'stroke',\n        'strokeLinecap',\n        'strokeLinejoin',\n        'strokeWidth',\n        'textAnchor',\n        'x',\n        'y'\n    ];\n    Exporting.inlineAllowlist = [];\n    const unstyledElements = [\n        'clipPath',\n        'defs',\n        'desc'\n    ];\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let printingChart;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add the export button to the chart, with options.\n     *\n     * @private\n     * @function Highcharts.Chart#addButton\n     * @param {Highcharts.NavigationButtonOptions} options\n     * @requires modules/exporting\n     */\n    function addButton(options) {\n        const chart = this, renderer = chart.renderer, btnOptions = merge(chart.options.navigation.buttonOptions, options), onclick = btnOptions.onclick, menuItems = btnOptions.menuItems, symbolSize = btnOptions.symbolSize || 12;\n        let symbol;\n        if (!chart.btnCount) {\n            chart.btnCount = 0;\n        }\n        // Keeps references to the button elements\n        if (!chart.exportDivElements) {\n            chart.exportDivElements = [];\n            chart.exportSVGElements = [];\n        }\n        if (btnOptions.enabled === false || !btnOptions.theme) {\n            return;\n        }\n        const theme = chart.styledMode ? {} : btnOptions.theme;\n        let callback;\n        if (onclick) {\n            callback = function (e) {\n                if (e) {\n                    e.stopPropagation();\n                }\n                onclick.call(chart, e);\n            };\n        }\n        else if (menuItems) {\n            callback = function (e) {\n                // Consistent with onclick call (#3495)\n                if (e) {\n                    e.stopPropagation();\n                }\n                chart.contextMenu(button.menuClassName, menuItems, button.translateX || 0, button.translateY || 0, button.width || 0, button.height || 0, button);\n                button.setState(2);\n            };\n        }\n        if (btnOptions.text && btnOptions.symbol) {\n            theme.paddingLeft = pick(theme.paddingLeft, 30);\n        }\n        else if (!btnOptions.text) {\n            extend(theme, {\n                width: btnOptions.width,\n                height: btnOptions.height,\n                padding: 0\n            });\n        }\n        const button = renderer\n            .button(btnOptions.text, 0, 0, callback, theme, void 0, void 0, void 0, void 0, btnOptions.useHTML)\n            .addClass(options.className)\n            .attr({\n            title: pick(chart.options.lang[btnOptions._titleKey || btnOptions.titleKey], '')\n        });\n        button.menuClassName = (options.menuClassName ||\n            'highcharts-menu-' + chart.btnCount++);\n        if (btnOptions.symbol) {\n            symbol = renderer\n                .symbol(btnOptions.symbol, Math.round((btnOptions.symbolX || 0) - (symbolSize / 2)), Math.round((btnOptions.symbolY || 0) - (symbolSize / 2)), symbolSize, symbolSize\n            // If symbol is an image, scale it (#7957)\n            , {\n                width: symbolSize,\n                height: symbolSize\n            })\n                .addClass('highcharts-button-symbol')\n                .attr({\n                zIndex: 1\n            })\n                .add(button);\n            if (!chart.styledMode) {\n                symbol.attr({\n                    stroke: btnOptions.symbolStroke,\n                    fill: btnOptions.symbolFill,\n                    'stroke-width': btnOptions.symbolStrokeWidth || 1\n                });\n            }\n        }\n        button\n            .add(chart.exportingGroup)\n            .align(extend(btnOptions, {\n            width: button.width,\n            x: pick(btnOptions.x, chart.buttonOffset) // #1654\n        }), true, 'spacingBox');\n        chart.buttonOffset += (((button.width || 0) + btnOptions.buttonSpacing) *\n            (btnOptions.align === 'right' ? -1 : 1));\n        chart.exportSVGElements.push(button, symbol);\n    }\n    /**\n     * Clean up after printing a chart.\n     *\n     * @function Highcharts#afterPrint\n     *\n     * @private\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that was (or suppose to be) printed\n     *\n     * @emits Highcharts.Chart#event:afterPrint\n     */\n    function afterPrint() {\n        const chart = this;\n        if (!chart.printReverseInfo) {\n            return void 0;\n        }\n        const { childNodes, origDisplay, resetParams } = chart.printReverseInfo;\n        // Put the chart back in\n        chart.moveContainers(chart.renderTo);\n        // Restore all body content\n        [].forEach.call(childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                node.style.display = (origDisplay[i] || '');\n            }\n        });\n        chart.isPrinting = false;\n        // Reset printMaxWidth\n        if (resetParams) {\n            chart.setSize.apply(chart, resetParams);\n        }\n        delete chart.printReverseInfo;\n        printingChart = void 0;\n        Exporting_fireEvent(chart, 'afterPrint');\n    }\n    /**\n     * Prepare chart and document before printing a chart.\n     *\n     * @function Highcharts#beforePrint\n     *\n     * @private\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     */\n    function beforePrint() {\n        const chart = this, body = doc.body, printMaxWidth = chart.options.exporting.printMaxWidth, printReverseInfo = {\n            childNodes: body.childNodes,\n            origDisplay: [],\n            resetParams: void 0\n        };\n        chart.isPrinting = true;\n        chart.pointer?.reset(void 0, 0);\n        Exporting_fireEvent(chart, 'beforePrint');\n        // Handle printMaxWidth\n        const handleMaxWidth = printMaxWidth &&\n            chart.chartWidth > printMaxWidth;\n        if (handleMaxWidth) {\n            printReverseInfo.resetParams = [\n                chart.options.chart.width,\n                void 0,\n                false\n            ];\n            chart.setSize(printMaxWidth, void 0, false);\n        }\n        // Hide all body content\n        [].forEach.call(printReverseInfo.childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                printReverseInfo.origDisplay[i] = node.style.display;\n                node.style.display = 'none';\n            }\n        });\n        // Pull out the chart\n        chart.moveContainers(body);\n        // Storage details for undo action after printing\n        chart.printReverseInfo = printReverseInfo;\n    }\n    /**\n     * @private\n     */\n    function chartCallback(chart) {\n        const composition = chart;\n        composition.renderExporting();\n        Exporting_addEvent(chart, 'redraw', composition.renderExporting);\n        // Destroy the export elements at chart destroy\n        Exporting_addEvent(chart, 'destroy', composition.destroyExport);\n        // Uncomment this to see a button directly below the chart, for quick\n        // testing of export\n        /*\n        let button, viewImage, viewSource;\n        if (!chart.renderer.forExport) {\n            viewImage = function () {\n                let div = doc.createElement('div');\n                div.innerHTML = chart.getSVGForExport();\n                chart.renderTo.parentNode.appendChild(div);\n            };\n\n            viewSource = function () {\n                let pre = doc.createElement('pre');\n                pre.innerHTML = chart.getSVGForExport()\n                    .replace(/</g, '\\n&lt;')\n                    .replace(/>/g, '&gt;');\n                chart.renderTo.parentNode.appendChild(pre);\n            };\n\n            viewImage();\n\n            // View SVG Image\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Image';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewImage;\n\n            // View SVG Source\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Source';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewSource;\n        }\n        //*/\n    }\n    /**\n     * @private\n     */\n    function compose(ChartClass, SVGRendererClass) {\n        Exporting_ExportingSymbols.compose(SVGRendererClass);\n        Exporting_Fullscreen.compose(ChartClass);\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.exportChart) {\n            chartProto.afterPrint = afterPrint;\n            chartProto.exportChart = exportChart;\n            chartProto.inlineStyles = inlineStyles;\n            chartProto.print = print;\n            chartProto.sanitizeSVG = sanitizeSVG;\n            chartProto.getChartHTML = getChartHTML;\n            chartProto.getSVG = getSVG;\n            chartProto.getSVGForExport = getSVGForExport;\n            chartProto.getFilename = getFilename;\n            chartProto.moveContainers = moveContainers;\n            chartProto.beforePrint = beforePrint;\n            chartProto.contextMenu = contextMenu;\n            chartProto.addButton = addButton;\n            chartProto.destroyExport = destroyExport;\n            chartProto.renderExporting = renderExporting;\n            chartProto.resolveCSSVariables = resolveCSSVariables;\n            chartProto.callbacks.push(chartCallback);\n            Exporting_addEvent(ChartClass, 'init', onChartInit);\n            Exporting_addEvent(ChartClass, 'layOutTitle', onChartLayOutTitle);\n            if ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n                Exporting_win.matchMedia('print').addListener(function (mqlEvent) {\n                    if (!printingChart) {\n                        return void 0;\n                    }\n                    if (mqlEvent.matches) {\n                        printingChart.beforePrint();\n                    }\n                    else {\n                        printingChart.afterPrint();\n                    }\n                });\n            }\n            defaultOptions.exporting = merge(Exporting_ExportingDefaults.exporting, defaultOptions.exporting);\n            defaultOptions.lang = merge(Exporting_ExportingDefaults.lang, defaultOptions.lang);\n            // Buttons and menus are collected in a separate config option set\n            // called 'navigation'. This can be extended later to add control\n            // buttons like zoom and pan right click menus.\n            defaultOptions.navigation = merge(Exporting_ExportingDefaults.navigation, defaultOptions.navigation);\n        }\n    }\n    Exporting.compose = compose;\n    /**\n     * Display a popup menu for choosing the export type.\n     *\n     * @private\n     * @function Highcharts.Chart#contextMenu\n     * @param {string} className\n     *        An identifier for the menu.\n     * @param {Array<string|Highcharts.ExportingMenuObject>} items\n     *        A collection with text and onclicks for the items.\n     * @param {number} x\n     *        The x position of the opener button\n     * @param {number} y\n     *        The y position of the opener button\n     * @param {number} width\n     *        The width of the opener button\n     * @param {number} height\n     *        The height of the opener button\n     * @requires modules/exporting\n     */\n    function contextMenu(className, items, x, y, width, height, button) {\n        const chart = this, navOptions = chart.options.navigation, chartWidth = chart.chartWidth, chartHeight = chart.chartHeight, cacheName = 'cache-' + className, \n        // For mouse leave detection\n        menuPadding = Math.max(width, height);\n        let innerMenu, menu = chart[cacheName];\n        // Create the menu only the first time\n        if (!menu) {\n            // Create a HTML element above the SVG\n            chart.exportContextMenu = chart[cacheName] = menu =\n                createElement('div', {\n                    className: className\n                }, {\n                    position: 'absolute',\n                    zIndex: 1000,\n                    padding: menuPadding + 'px',\n                    pointerEvents: 'auto',\n                    ...chart.renderer.style\n                }, chart.scrollablePlotArea?.fixedDiv || chart.container);\n            innerMenu = createElement('ul', { className: 'highcharts-menu' }, chart.styledMode ? {} : {\n                listStyle: 'none',\n                margin: 0,\n                padding: 0\n            }, menu);\n            // Presentational CSS\n            if (!chart.styledMode) {\n                css(innerMenu, extend({\n                    MozBoxShadow: '3px 3px 10px #888',\n                    WebkitBoxShadow: '3px 3px 10px #888',\n                    boxShadow: '3px 3px 10px #888'\n                }, navOptions.menuStyle));\n            }\n            // Hide on mouse out\n            menu.hideMenu = function () {\n                css(menu, { display: 'none' });\n                if (button) {\n                    button.setState(0);\n                }\n                chart.openMenu = false;\n                // #10361, #9998\n                css(chart.renderTo, { overflow: 'hidden' });\n                css(chart.container, { overflow: 'hidden' });\n                highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(menu.hideTimer);\n                Exporting_fireEvent(chart, 'exportMenuHidden');\n            };\n            // Hide the menu some time after mouse leave (#1357)\n            chart.exportEvents.push(Exporting_addEvent(menu, 'mouseleave', function () {\n                menu.hideTimer = Exporting_win.setTimeout(menu.hideMenu, 500);\n            }), Exporting_addEvent(menu, 'mouseenter', function () {\n                highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(menu.hideTimer);\n            }), \n            // Hide it on clicking or touching outside the menu (#2258,\n            // #2335, #2407)\n            Exporting_addEvent(doc, 'mouseup', function (e) {\n                if (!chart.pointer?.inClass(e.target, className)) {\n                    menu.hideMenu();\n                }\n            }), Exporting_addEvent(menu, 'click', function () {\n                if (chart.openMenu) {\n                    menu.hideMenu();\n                }\n            }));\n            // Create the items\n            items.forEach(function (item) {\n                if (typeof item === 'string') {\n                    item = chart.options.exporting\n                        .menuItemDefinitions[item];\n                }\n                if (isObject(item, true)) {\n                    let element;\n                    if (item.separator) {\n                        element = createElement('hr', void 0, void 0, innerMenu);\n                    }\n                    else {\n                        // When chart initialized with the table, wrong button\n                        // text displayed, #14352.\n                        if (item.textKey === 'viewData' &&\n                            chart.isDataTableVisible) {\n                            item.textKey = 'hideData';\n                        }\n                        element = createElement('li', {\n                            className: 'highcharts-menu-item',\n                            onclick: function (e) {\n                                if (e) { // IE7\n                                    e.stopPropagation();\n                                }\n                                menu.hideMenu();\n                                if (typeof item !== 'string' && item.onclick) {\n                                    item.onclick.apply(chart, arguments);\n                                }\n                            }\n                        }, void 0, innerMenu);\n                        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(element, item.text ||\n                            chart.options.lang[item.textKey]);\n                        if (!chart.styledMode) {\n                            element.onmouseover = function () {\n                                css(this, navOptions.menuItemHoverStyle);\n                            };\n                            element.onmouseout = function () {\n                                css(this, navOptions.menuItemStyle);\n                            };\n                            css(element, extend({\n                                cursor: 'pointer'\n                            }, navOptions.menuItemStyle || {}));\n                        }\n                    }\n                    // Keep references to menu divs to be able to destroy them\n                    chart.exportDivElements.push(element);\n                }\n            });\n            // Keep references to menu and innerMenu div to be able to destroy\n            // them\n            chart.exportDivElements.push(innerMenu, menu);\n            chart.exportMenuWidth = menu.offsetWidth;\n            chart.exportMenuHeight = menu.offsetHeight;\n        }\n        const menuStyle = { display: 'block' };\n        // If outside right, right align it\n        if (x + (chart.exportMenuWidth || 0) > chartWidth) {\n            menuStyle.right = (chartWidth - x - width - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.left = (x - menuPadding) + 'px';\n        }\n        // If outside bottom, bottom align it\n        if (y + height + (chart.exportMenuHeight || 0) > chartHeight &&\n            button.alignOptions?.verticalAlign !== 'top') {\n            menuStyle.bottom = (chartHeight - y - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.top = (y + height - menuPadding) + 'px';\n        }\n        css(menu, menuStyle);\n        // #10361, #9998\n        css(chart.renderTo, { overflow: '' });\n        css(chart.container, { overflow: '' });\n        chart.openMenu = true;\n        Exporting_fireEvent(chart, 'exportMenuShown');\n    }\n    /**\n     * Destroy the export buttons.\n     * @private\n     * @function Highcharts.Chart#destroyExport\n     * @param {global.Event} [e]\n     * @requires modules/exporting\n     */\n    function destroyExport(e) {\n        const chart = e ? e.target : this, exportSVGElements = chart.exportSVGElements, exportDivElements = chart.exportDivElements, exportEvents = chart.exportEvents;\n        let cacheName;\n        // Destroy the extra buttons added\n        if (exportSVGElements) {\n            exportSVGElements.forEach((elem, i) => {\n                // Destroy and null the svg elements\n                if (elem) { // #1822\n                    elem.onclick = elem.ontouchstart = null;\n                    cacheName = 'cache-' + elem.menuClassName;\n                    if (chart[cacheName]) {\n                        delete chart[cacheName];\n                    }\n                    exportSVGElements[i] = elem.destroy();\n                }\n            });\n            exportSVGElements.length = 0;\n        }\n        // Destroy the exporting group\n        if (chart.exportingGroup) {\n            chart.exportingGroup.destroy();\n            delete chart.exportingGroup;\n        }\n        // Destroy the divs for the menu\n        if (exportDivElements) {\n            exportDivElements.forEach(function (elem, i) {\n                if (elem) {\n                    // Remove the event handler\n                    highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(elem.hideTimer); // #5427\n                    removeEvent(elem, 'mouseleave');\n                    // Remove inline events\n                    // (chart.exportDivElements as any)[i] =\n                    exportDivElements[i] =\n                        elem.onmouseout =\n                            elem.onmouseover =\n                                elem.ontouchstart =\n                                    elem.onclick = null;\n                    // Destroy the div by moving to garbage bin\n                    Exporting_discardElement(elem);\n                }\n            });\n            exportDivElements.length = 0;\n        }\n        if (exportEvents) {\n            exportEvents.forEach(function (unbind) {\n                unbind();\n            });\n            exportEvents.length = 0;\n        }\n    }\n    /**\n     * Exporting module required. Submit an SVG version of the chart to a server\n     * along with some parameters for conversion.\n     *\n     * @sample highcharts/members/chart-exportchart/\n     *         Export with no options\n     * @sample highcharts/members/chart-exportchart-filename/\n     *         PDF type and custom filename\n     * @sample highcharts/members/chart-exportchart-custom-background/\n     *         Different chart background in export\n     * @sample stock/members/chart-exportchart/\n     *         Export with Highcharts Stock\n     *\n     * @function Highcharts.Chart#exportChart\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     *        Exporting options in addition to those defined in\n     *        [exporting](https://api.highcharts.com/highcharts/exporting).\n     *\n     * @param {Highcharts.Options} chartOptions\n     *        Additional chart options for the exported chart. For example a\n     *        different background color can be added here, or `dataLabels` for\n     *        export only.\n     *\n     * @requires modules/exporting\n     */\n    function exportChart(exportingOptions, chartOptions) {\n        const svg = this.getSVGForExport(exportingOptions, chartOptions);\n        // Merge the options\n        exportingOptions = merge(this.options.exporting, exportingOptions);\n        // Do the post\n        Core_HttpUtilities.post(exportingOptions.url, {\n            filename: exportingOptions.filename ?\n                exportingOptions.filename.replace(/\\//g, '-') :\n                this.getFilename(),\n            type: exportingOptions.type,\n            width: exportingOptions.width,\n            scale: exportingOptions.scale,\n            svg: svg\n        }, exportingOptions.fetchOptions);\n    }\n    /**\n     * Return the unfiltered innerHTML of the chart container. Used as hook for\n     * plugins. In styled mode, it also takes care of inlining CSS style rules.\n     *\n     * @see Chart#getSVG\n     *\n     * @function Highcharts.Chart#getChartHTML\n     *\n     * @return {string}\n     * The unfiltered SVG of the chart.\n     *\n     * @requires modules/exporting\n     */\n    function getChartHTML(applyStyleSheets) {\n        if (applyStyleSheets) {\n            this.inlineStyles();\n        }\n        this.resolveCSSVariables();\n        return this.container.innerHTML;\n    }\n    /**\n     * Get the default file name used for exported charts. By default it creates\n     * a file name based on the chart title.\n     *\n     * @function Highcharts.Chart#getFilename\n     *\n     * @return {string} A file name without extension.\n     *\n     * @requires modules/exporting\n     */\n    function getFilename() {\n        const s = this.userOptions.title && this.userOptions.title.text;\n        let filename = this.options.exporting.filename;\n        if (filename) {\n            return filename.replace(/\\//g, '-');\n        }\n        if (typeof s === 'string') {\n            filename = s\n                .toLowerCase()\n                .replace(/<\\/?[^>]+(>|$)/g, '') // Strip HTML tags\n                .replace(/[\\s_]+/g, '-')\n                .replace(/[^a-z\\d\\-]/g, '') // Preserve only latin\n                .replace(/^[\\-]+/g, '') // Dashes in the start\n                .replace(/[\\-]+/g, '-') // Dashes in a row\n                .substr(0, 24)\n                .replace(/[\\-]+$/g, ''); // Dashes in the end;\n        }\n        if (!filename || filename.length < 5) {\n            filename = 'chart';\n        }\n        return filename;\n    }\n    /**\n     * Return an SVG representation of the chart.\n     *\n     * @sample highcharts/members/chart-getsvg/\n     *         View the SVG from a button\n     *\n     * @function Highcharts.Chart#getSVG\n     *\n     * @param {Highcharts.Options} [chartOptions]\n     *        Additional chart options for the generated SVG representation. For\n     *        collections like `xAxis`, `yAxis` or `series`, the additional\n     *        options is either merged in to the original item of the same\n     *        `id`, or to the first item if a common id is not found.\n     *\n     * @return {string}\n     *         The SVG representation of the rendered chart.\n     *\n     * @emits Highcharts.Chart#event:getSVG\n     *\n     * @requires modules/exporting\n     */\n    function getSVG(chartOptions) {\n        const chart = this;\n        let svg, seriesOptions, \n        // Copy the options and add extra options\n        options = merge(chart.options, chartOptions);\n        // Use userOptions to make the options chain in series right (#3881)\n        options.plotOptions = merge(chart.userOptions.plotOptions, chartOptions && chartOptions.plotOptions);\n        // ... and likewise with time, avoid that undefined time properties are\n        // merged over legacy global time options\n        options.time = merge(chart.userOptions.time, chartOptions && chartOptions.time);\n        // Create a sandbox where a new chart will be generated\n        const sandbox = createElement('div', null, {\n            position: 'absolute',\n            top: '-9999em',\n            width: chart.chartWidth + 'px',\n            height: chart.chartHeight + 'px'\n        }, doc.body);\n        // Get the source size\n        const cssWidth = chart.renderTo.style.width, cssHeight = chart.renderTo.style.height, sourceWidth = options.exporting.sourceWidth ||\n            options.chart.width ||\n            (/px$/.test(cssWidth) && parseInt(cssWidth, 10)) ||\n            (options.isGantt ? 800 : 600), sourceHeight = options.exporting.sourceHeight ||\n            options.chart.height ||\n            (/px$/.test(cssHeight) && parseInt(cssHeight, 10)) ||\n            400;\n        // Override some options\n        extend(options.chart, {\n            animation: false,\n            renderTo: sandbox,\n            forExport: true,\n            renderer: 'SVGRenderer',\n            width: sourceWidth,\n            height: sourceHeight\n        });\n        options.exporting.enabled = false; // Hide buttons in print\n        delete options.data; // #3004\n        // prepare for replicating the chart\n        options.series = [];\n        chart.series.forEach(function (serie) {\n            seriesOptions = merge(serie.userOptions, {\n                animation: false, // Turn off animation\n                enableMouseTracking: false,\n                showCheckbox: false,\n                visible: serie.visible\n            });\n            // Used for the navigator series that has its own option set\n            if (!seriesOptions.isInternal) {\n                options.series.push(seriesOptions);\n            }\n        });\n        const colls = {};\n        chart.axes.forEach(function (axis) {\n            // Assign an internal key to ensure a one-to-one mapping (#5924)\n            if (!axis.userOptions.internalKey) { // #6444\n                axis.userOptions.internalKey = uniqueKey();\n            }\n            if (!axis.options.isInternal) {\n                if (!colls[axis.coll]) {\n                    colls[axis.coll] = true;\n                    options[axis.coll] = [];\n                }\n                options[axis.coll].push(merge(axis.userOptions, {\n                    visible: axis.visible,\n                    // Force some options that could have be set directly on\n                    // the axis while missing in the userOptions or options.\n                    type: axis.type,\n                    uniqueNames: axis.uniqueNames\n                }));\n            }\n        });\n        // Make sure the `colorAxis` object of the `defaultOptions` isn't used\n        // in the chart copy's user options, because a color axis should only be\n        // added when the user actually applies it.\n        options.colorAxis = chart.userOptions.colorAxis;\n        // Generate the chart copy\n        const chartCopy = new chart.constructor(options, chart.callback);\n        // Axis options and series options  (#2022, #3900, #5982)\n        if (chartOptions) {\n            ['xAxis', 'yAxis', 'series'].forEach(function (coll) {\n                const collOptions = {};\n                if (chartOptions[coll]) {\n                    collOptions[coll] = chartOptions[coll];\n                    chartCopy.update(collOptions);\n                }\n            });\n        }\n        // Reflect axis extremes in the export (#5924)\n        chart.axes.forEach(function (axis) {\n            const axisCopy = find(chartCopy.axes, (copy) => copy.options.internalKey === axis.userOptions.internalKey);\n            if (axisCopy) {\n                const extremes = axis.getExtremes(), \n                // Make sure min and max overrides in the\n                // `exporting.chartOptions.xAxis` settings are reflected.\n                // These should override user-set extremes via zooming,\n                // scrollbar etc (#7873).\n                exportOverride = splat(chartOptions?.[axis.coll] || {})[0], userMin = 'min' in exportOverride ?\n                    exportOverride.min :\n                    extremes.userMin, userMax = 'max' in exportOverride ?\n                    exportOverride.max :\n                    extremes.userMax;\n                if (((typeof userMin !== 'undefined' &&\n                    userMin !== axisCopy.min) || (typeof userMax !== 'undefined' &&\n                    userMax !== axisCopy.max))) {\n                    axisCopy.setExtremes(userMin ?? void 0, userMax ?? void 0, true, false);\n                }\n            }\n        });\n        // Get the SVG from the container's innerHTML\n        svg = chartCopy.getChartHTML(chart.styledMode ||\n            options.exporting?.applyStyleSheets);\n        Exporting_fireEvent(this, 'getSVG', { chartCopy: chartCopy });\n        svg = chart.sanitizeSVG(svg, options);\n        // Free up memory\n        options = null;\n        chartCopy.destroy();\n        Exporting_discardElement(sandbox);\n        return svg;\n    }\n    /**\n     * @private\n     * @function Highcharts.Chart#getSVGForExport\n     */\n    function getSVGForExport(options, chartOptions) {\n        const chartExportingOptions = this.options.exporting;\n        return this.getSVG(merge({ chart: { borderRadius: 0 } }, chartExportingOptions.chartOptions, chartOptions, {\n            exporting: {\n                sourceWidth: ((options && options.sourceWidth) ||\n                    chartExportingOptions.sourceWidth),\n                sourceHeight: ((options && options.sourceHeight) ||\n                    chartExportingOptions.sourceHeight)\n            }\n        }));\n    }\n    /**\n     * Make hyphenated property names out of camelCase\n     * @private\n     * @param {string} prop\n     * Property name in camelCase\n     * @return {string}\n     * Hyphenated property name\n     */\n    function hyphenate(prop) {\n        return prop.replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n        });\n    }\n    /**\n     * Analyze inherited styles from stylesheets and add them inline\n     *\n     * @private\n     * @function Highcharts.Chart#inlineStyles\n     *\n     * @todo What are the border styles for text about? In general, text has a\n     *       lot of properties.\n     *\n     * @todo Make it work with IE9 and IE10.\n     *\n     * @requires modules/exporting\n     */\n    function inlineStyles() {\n        const denylist = inlineDenylist, allowlist = Exporting.inlineAllowlist, // For IE\n        defaultStyles = {};\n        let dummySVG;\n        // Create an iframe where we read default styles without pollution from\n        // this body\n        const iframe = doc.createElement('iframe');\n        css(iframe, {\n            width: '1px',\n            height: '1px',\n            visibility: 'hidden'\n        });\n        doc.body.appendChild(iframe);\n        const iframeDoc = (iframe.contentWindow && iframe.contentWindow.document);\n        if (iframeDoc) {\n            iframeDoc.body.appendChild(iframeDoc.createElementNS(SVG_NS, 'svg'));\n        }\n        /**\n         * Call this on all elements and recurse to children\n         * @private\n         * @param {Highcharts.HTMLDOMElement} node\n         *        Element child\n             */\n        function recurse(node) {\n            const filteredStyles = {};\n            let styles, parentStyles, dummy, denylisted, allowlisted, i;\n            /**\n             * Check computed styles and whether they are in the allow/denylist\n             * for styles or attributes.\n             * @private\n             * @param {string} val\n             *        Style value\n             * @param {string} prop\n             *        Style property name\n                     */\n            function filterStyles(val, prop) {\n                // Check against allowlist & denylist\n                denylisted = allowlisted = false;\n                if (allowlist.length) {\n                    // Styled mode in IE has a allowlist instead. Exclude all\n                    // props not in this list.\n                    i = allowlist.length;\n                    while (i-- && !allowlisted) {\n                        allowlisted = allowlist[i].test(prop);\n                    }\n                    denylisted = !allowlisted;\n                }\n                // Explicitly remove empty transforms\n                if (prop === 'transform' && val === 'none') {\n                    denylisted = true;\n                }\n                i = denylist.length;\n                while (i-- && !denylisted) {\n                    if (prop.length > 1000 /* RegexLimits.shortLimit */) {\n                        throw new Error('Input too long');\n                    }\n                    denylisted = (denylist[i].test(prop) ||\n                        typeof val === 'function');\n                }\n                if (!denylisted) {\n                    // If parent node has the same style, it gets inherited, no\n                    // need to inline it. Top-level props should be diffed\n                    // against parent (#7687).\n                    if ((parentStyles[prop] !== val ||\n                        node.nodeName === 'svg') &&\n                        defaultStyles[node.nodeName][prop] !== val) {\n                        // Attributes\n                        if (!inlineToAttributes ||\n                            inlineToAttributes.indexOf(prop) !== -1) {\n                            if (val) {\n                                node.setAttribute(hyphenate(prop), val);\n                            }\n                            // Styles\n                        }\n                        else {\n                            filteredStyles[prop] = val;\n                        }\n                    }\n                }\n            }\n            if (iframeDoc &&\n                node.nodeType === 1 &&\n                unstyledElements.indexOf(node.nodeName) === -1) {\n                styles = Exporting_win.getComputedStyle(node, null);\n                parentStyles = node.nodeName === 'svg' ?\n                    {} :\n                    Exporting_win.getComputedStyle(node.parentNode, null);\n                // Get default styles from the browser so that we don't have to\n                // add these\n                if (!defaultStyles[node.nodeName]) {\n                    /*\n                    If (!dummySVG) {\n                        dummySVG = doc.createElementNS(H.SVG_NS, 'svg');\n                        dummySVG.setAttribute('version', '1.1');\n                        doc.body.appendChild(dummySVG);\n                    }\n                    */\n                    dummySVG = iframeDoc.getElementsByTagName('svg')[0];\n                    dummy = iframeDoc.createElementNS(node.namespaceURI, node.nodeName);\n                    dummySVG.appendChild(dummy);\n                    // Get the defaults into a standard object (simple merge\n                    // won't do)\n                    const s = Exporting_win.getComputedStyle(dummy, null), defaults = {};\n                    for (const key in s) {\n                        if (key.length < 1000 /* RegexLimits.shortLimit */ &&\n                            typeof s[key] === 'string' &&\n                            !/^\\d+$/.test(key)) {\n                            defaults[key] = s[key];\n                        }\n                    }\n                    defaultStyles[node.nodeName] = defaults;\n                    // Remove default fill, otherwise text disappears when\n                    // exported\n                    if (node.nodeName === 'text') {\n                        delete defaultStyles.text.fill;\n                    }\n                    dummySVG.removeChild(dummy);\n                }\n                // Loop through all styles and add them inline if they are ok\n                for (const p in styles) {\n                    if (\n                    // Some browsers put lots of styles on the prototype...\n                    (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFirefox ||\n                        (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS ||\n                        (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari || // #16902\n                        // ... Chrome puts them on the instance\n                        Object.hasOwnProperty.call(styles, p)) {\n                        filterStyles(styles[p], p);\n                    }\n                }\n                // Apply styles\n                css(node, filteredStyles);\n                // Set default stroke width (needed at least for IE)\n                if (node.nodeName === 'svg') {\n                    node.setAttribute('stroke-width', '1px');\n                }\n                if (node.nodeName === 'text') {\n                    return;\n                }\n                // Recurse\n                [].forEach.call(node.children || node.childNodes, recurse);\n            }\n        }\n        /**\n         * Remove the dummy objects used to get defaults\n         * @private\n         */\n        function tearDown() {\n            dummySVG.parentNode.removeChild(dummySVG);\n            // Remove trash from DOM that stayed after each exporting\n            iframe.parentNode.removeChild(iframe);\n        }\n        recurse(this.container.querySelector('svg'));\n        tearDown();\n    }\n    /**\n     * Resolve CSS variables into hex colors\n     */\n    function resolveCSSVariables() {\n        const svgElements = this.container.querySelectorAll('*'), colorAttributes = ['color', 'fill', 'stop-color', 'stroke'];\n        Array.from(svgElements).forEach((element) => {\n            colorAttributes.forEach((attr) => {\n                const attrValue = element.getAttribute(attr);\n                if (attrValue?.includes('var(')) {\n                    element.setAttribute(attr, getComputedStyle(element).getPropertyValue(attr));\n                }\n            });\n        });\n    }\n    /**\n     * Move the chart container(s) to another div.\n     *\n     * @function Highcharts#moveContainers\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} moveTo\n     *        Move target\n     */\n    function moveContainers(moveTo) {\n        const { scrollablePlotArea } = this;\n        (\n        // When scrollablePlotArea is active (#9533)\n        scrollablePlotArea ?\n            [\n                scrollablePlotArea.fixedDiv,\n                scrollablePlotArea.scrollingContainer\n            ] :\n            [this.container]).forEach(function (div) {\n            moveTo.appendChild(div);\n        });\n    }\n    /**\n     * Add update methods to handle chart.update and chart.exporting.update and\n     * chart.navigation.update. These must be added to the chart instance rather\n     * than the Chart prototype in order to use the chart instance inside the\n     * update function.\n     * @private\n     */\n    function onChartInit() {\n        const chart = this, \n        /**\n         * @private\n         * @param {\"exporting\"|\"navigation\"} prop\n         *        Property name in option root\n         * @param {Highcharts.ExportingOptions|Highcharts.NavigationOptions} options\n         *        Options to update\n         * @param {boolean} [redraw=true]\n         *        Whether to redraw\n                 */\n        update = (prop, options, redraw) => {\n            chart.isDirtyExporting = true;\n            merge(true, chart.options[prop], options);\n            if (pick(redraw, true)) {\n                chart.redraw();\n            }\n        };\n        chart.exporting = {\n            update: function (options, redraw) {\n                update('exporting', options, redraw);\n            }\n        };\n        // Register update() method for navigation. Cannot be set the same way\n        // as for exporting, because navigation options are shared with bindings\n        // which has separate update() logic.\n        Chart_ChartNavigationComposition\n            .compose(chart).navigation\n            .addUpdate((options, redraw) => {\n            update('navigation', options, redraw);\n        });\n    }\n    /**\n     * On layout of titles (title, subtitle and caption), adjust the `alignTo``\n     * box to avoid the context menu button.\n     * @private\n     */\n    function onChartLayOutTitle({ alignTo, key, textPxLength }) {\n        const exportingOptions = this.options.exporting, { align, buttonSpacing = 0, verticalAlign, width = 0 } = merge(this.options.navigation?.buttonOptions, exportingOptions?.buttons?.contextButton), space = alignTo.width - textPxLength, widthAdjust = width + buttonSpacing;\n        if ((exportingOptions?.enabled ?? true) &&\n            key === 'title' &&\n            align === 'right' &&\n            verticalAlign === 'top') {\n            if (space < 2 * widthAdjust) {\n                if (space < widthAdjust) {\n                    alignTo.width -= widthAdjust;\n                }\n                else if (this.title?.alignValue !== 'left') {\n                    alignTo.x -= widthAdjust - space / 2;\n                }\n            }\n        }\n    }\n    /**\n     * Exporting module required. Clears away other elements in the page and\n     * prints the chart as it is displayed. By default, when the exporting\n     * module is enabled, a context button with a drop down menu in the upper\n     * right corner accesses this function.\n     *\n     * @sample highcharts/members/chart-print/\n     *         Print from a HTML button\n     *\n     * @function Highcharts.Chart#print\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     * @emits Highcharts.Chart#event:afterPrint\n     *\n     * @requires modules/exporting\n     */\n    function print() {\n        const chart = this;\n        if (chart.isPrinting) { // Block the button while in printing mode\n            return;\n        }\n        printingChart = chart;\n        if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n            chart.beforePrint();\n        }\n        // Give the browser time to draw WebGL content, an issue that randomly\n        // appears (at least) in Chrome ~67 on the Mac (#8708).\n        setTimeout(() => {\n            Exporting_win.focus(); // #1510\n            Exporting_win.print();\n            // Allow the browser to prepare before reverting\n            if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n                setTimeout(() => {\n                    chart.afterPrint();\n                }, 1000);\n            }\n        }, 1);\n    }\n    /**\n     * Add the buttons on chart load\n     * @private\n     * @function Highcharts.Chart#renderExporting\n     * @requires modules/exporting\n     */\n    function renderExporting() {\n        const chart = this, exportingOptions = chart.options.exporting, buttons = exportingOptions.buttons, isDirty = chart.isDirtyExporting || !chart.exportSVGElements;\n        chart.buttonOffset = 0;\n        if (chart.isDirtyExporting) {\n            chart.destroyExport();\n        }\n        if (isDirty && exportingOptions.enabled !== false) {\n            chart.exportEvents = [];\n            chart.exportingGroup = chart.exportingGroup ||\n                chart.renderer.g('exporting-group').attr({\n                    zIndex: 3 // #4955, // #8392\n                }).add();\n            Exporting_objectEach(buttons, function (button) {\n                chart.addButton(button);\n            });\n            chart.isDirtyExporting = false;\n        }\n    }\n    /**\n     * Exporting module only. A collection of fixes on the produced SVG to\n     * account for expand properties, browser bugs.\n     * Returns a cleaned SVG.\n     *\n     * @private\n     * @function Highcharts.Chart#sanitizeSVG\n     * @param {string} svg\n     *        SVG code to sanitize\n     * @param {Highcharts.Options} options\n     *        Chart options to apply\n     * @return {string}\n     *         Sanitized SVG code\n     * @requires modules/exporting\n     */\n    function sanitizeSVG(svg, options) {\n        const split = svg.indexOf('</svg>') + 6, useForeignObject = svg.indexOf('<foreignObject') > -1;\n        let html = svg.substr(split);\n        // Remove any HTML added to the container after the SVG (#894, #9087)\n        svg = svg.substr(0, split);\n        if (useForeignObject) {\n            // Some tags needs to be closed in xhtml (#13726)\n            svg = svg.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />');\n            // Move HTML into a foreignObject\n        }\n        else if (html && options?.exporting?.allowHTML) {\n            html = '<foreignObject x=\"0\" y=\"0\" ' +\n                'width=\"' + options.chart.width + '\" ' +\n                'height=\"' + options.chart.height + '\">' +\n                '<body xmlns=\"http://www.w3.org/1999/xhtml\">' +\n                // Some tags needs to be closed in xhtml (#13726)\n                html.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />') +\n                '</body>' +\n                '</foreignObject>';\n            svg = svg.replace('</svg>', html + '</svg>');\n        }\n        svg = svg\n            .replace(/zIndex=\"[^\"]+\"/g, '')\n            .replace(/symbolName=\"[^\"]+\"/g, '')\n            .replace(/jQuery\\d+=\"[^\"]+\"/g, '')\n            .replace(/url\\((\"|&quot;)(.*?)(\"|&quot;)\\;?\\)/g, 'url($2)')\n            .replace(/url\\([^#]+#/g, 'url(#')\n            .replace(/<svg /, '<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" ')\n            .replace(/ (NS\\d+\\:)?href=/g, ' xlink:href=') // #3567\n            .replace(/\\n+/g, ' ')\n            // Replace HTML entities, issue #347\n            .replace(/&nbsp;/g, '\\u00A0') // No-break space\n            .replace(/&shy;/g, '\\u00AD'); // Soft hyphen\n        return svg;\n    }\n})(Exporting || (Exporting = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_Exporting = (Exporting);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired after a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingAfterPrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired before a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingBeforePrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Function to call if the offline-exporting module fails to export a chart on\n * the client side.\n *\n * @callback Highcharts.ExportingErrorCallbackFunction\n *\n * @param {Highcharts.ExportingOptions} options\n *        The exporting options.\n *\n * @param {global.Error} err\n *        The error from the module.\n */\n/**\n * Definition for a menu item in the context menu.\n *\n * @interface Highcharts.ExportingMenuObject\n */ /**\n* The text for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#text\n* @type {string|undefined}\n*/ /**\n* If internationalization is required, the key to a language string.\n*\n* @name Highcharts.ExportingMenuObject#textKey\n* @type {string|undefined}\n*/ /**\n* The click handler for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#onclick\n* @type {Highcharts.EventCallbackFunction<Highcharts.Chart>|undefined}\n*/ /**\n* Indicates a separator line instead of an item.\n*\n* @name Highcharts.ExportingMenuObject#separator\n* @type {boolean|undefined}\n*/\n/**\n * Possible MIME types for exporting.\n *\n * @typedef {\"image/png\"|\"image/jpeg\"|\"application/pdf\"|\"image/svg+xml\"} Highcharts.ExportingMimeTypeValue\n */\n(''); // Keeps doclets above in transpiled file\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after a chart is printed through the context menu item or the\n * `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingAfterPrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.afterPrint\n */\n/**\n * Fires before a chart is printed through the context menu item or\n * the `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingBeforePrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.beforePrint\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/masters/modules/exporting.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.HttpUtilities = G.HttpUtilities || Core_HttpUtilities;\nG.ajax = G.HttpUtilities.ajax;\nG.getJSON = G.HttpUtilities.getJSON;\nG.post = G.HttpUtilities.post;\nExporting_Exporting.compose(G.Chart, G.Renderer);\n/* harmony default export */ const exporting_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__WEBPACK_EXTERNAL_MODULE__960__", "ChartNavigationComposition", "ExportingSymbols", "Exporting", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "exporting_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "compose", "chart", "navigation", "Additions", "constructor", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "for<PERSON>ach", "Chart_ChartNavigationComposition", "isTouchDevice", "Exporting_ExportingDefaults", "exporting", "allowTableSorting", "type", "url", "version", "pdfFont", "normal", "bold", "bolditalic", "italic", "printMaxWidth", "scale", "buttons", "contextButton", "className", "menuClassName", "symbol", "<PERSON><PERSON><PERSON>", "menuItems", "menuItemDefinitions", "viewFullscreen", "<PERSON><PERSON><PERSON>", "onclick", "fullscreen", "toggle", "printChart", "print", "separator", "downloadPNG", "exportChart", "downloadJPEG", "downloadPDF", "downloadSVG", "lang", "exitFullscreen", "contextButtonTitle", "buttonOptions", "symbolSize", "symbolX", "symbolY", "align", "buttonSpacing", "height", "y", "verticalAlign", "width", "symbolFill", "symbolStroke", "symbolStrokeWidth", "theme", "fill", "padding", "stroke", "menuStyle", "border", "borderRadius", "background", "menuItemStyle", "color", "fontSize", "transition", "menuItemHoverStyle", "modifiedClasses", "menu", "x", "menuball", "h", "path", "concat", "circle", "SVGRendererClass", "indexOf", "symbols", "bind", "Exporting_ExportingSymbols", "composed", "addEvent", "fireEvent", "pushUnique", "onChartBeforeRender", "Fullscreen", "ChartClass", "isOpen", "container", "renderTo", "browserProps", "requestFullscreen", "fullscreenChange", "mozRequestFullScreen", "webkitRequestFullScreen", "msRequestFullscreen", "close", "optionsChart", "ownerDocument", "Document", "unbindFullscreenEvent", "setSize", "origWidth", "origHeight", "origWidthOption", "origHeightOption", "setButtonText", "open", "chartWidth", "chartHeight", "unbind<PERSON>hange", "unbind<PERSON><PERSON><PERSON>", "promise", "alert", "exportDivElements", "exportingOptions", "exportDivElement", "setElementHTML", "text", "win", "discardElement", "objectEach", "HttpUtilities", "ajax", "settings", "headers", "json", "xml", "octet", "r", "XMLHttpRequest", "handleError", "xhr", "err", "error", "toUpperCase", "setRequestHeader", "dataType", "val", "responseType", "onreadystatechange", "res", "readyState", "status", "responseText", "JSON", "parse", "e", "Error", "success", "data", "stringify", "send", "getJSON", "post", "fetchOptions", "formData", "FormData", "name", "append", "filename", "fetch", "method", "body", "then", "ok", "link", "document", "createElement", "href", "download", "click", "defaultOptions", "doc", "SVG_NS", "Exporting_win", "Exporting_addEvent", "css", "Exporting_discardElement", "extend", "find", "Exporting_fireEvent", "isObject", "merge", "Exporting_objectEach", "pick", "removeEvent", "splat", "<PERSON><PERSON><PERSON>", "printingChart", "inlineDenylist", "inlineToAttributes", "inlineAllowlist", "unstyledElements", "addButton", "callback", "renderer", "btnOptions", "btnCount", "exportSVGElements", "enabled", "styledMode", "stopPropagation", "contextMenu", "button", "translateX", "translateY", "setState", "paddingLeft", "useHTML", "addClass", "attr", "title", "_title<PERSON>ey", "Math", "round", "zIndex", "add", "exportingGroup", "buttonOffset", "after<PERSON><PERSON>t", "printReverseInfo", "childNodes", "origDisplay", "resetParams", "moveContainers", "node", "i", "nodeType", "style", "display", "isPrinting", "apply", "beforePrint", "pointer", "reset", "chartCallback", "composition", "renderExporting", "destroyExport", "items", "navOptions", "cacheName", "menuPadding", "max", "innerMenu", "exportContextMenu", "position", "pointerEvents", "scrollablePlotArea", "fixedDiv", "listStyle", "margin", "MozBoxShadow", "WebkitBoxShadow", "boxShadow", "hideMenu", "openMenu", "overflow", "clearTimeout", "hide<PERSON><PERSON>r", "exportEvents", "setTimeout", "inClass", "target", "item", "element", "isDataTableVisible", "arguments", "on<PERSON><PERSON>ver", "onmouseout", "cursor", "exportMenuWidth", "offsetWidth", "exportMenuHeight", "offsetHeight", "right", "left", "alignOptions", "bottom", "top", "elem", "ontouchstart", "destroy", "length", "unbind", "chartOptions", "svg", "getSVGForExport", "Core_HttpUtilities", "replace", "getFilename", "getChartHTML", "applyStyleSheets", "inlineStyles", "resolveCSSVariables", "innerHTML", "s", "userOptions", "toLowerCase", "substr", "getSVG", "seriesOptions", "plotOptions", "time", "sandbox", "cssWidth", "cssHeight", "sourceWidth", "test", "parseInt", "isGantt", "sourceHeight", "animation", "forExport", "series", "serie", "enableMouseTracking", "showCheckbox", "visible", "isInternal", "colls", "axes", "axis", "internalKey", "coll", "uniqueNames", "colorAxis", "chartCopy", "collOptions", "axisCopy", "copy", "extremes", "getExtremes", "exportOverride", "userMin", "min", "userMax", "setExtremes", "sanitizeSVG", "chartExportingOptions", "dummySVG", "allowlist", "defaultStyles", "iframe", "visibility", "append<PERSON><PERSON><PERSON>", "iframeDoc", "contentWindow", "createElementNS", "recurse", "styles", "parentStyles", "dummy", "denylisted", "allowlisted", "filteredStyles", "nodeName", "getComputedStyle", "parentNode", "getElementsByTagName", "namespaceURI", "defaults", "<PERSON><PERSON><PERSON><PERSON>", "p", "isFirefox", "isMS", "<PERSON><PERSON><PERSON><PERSON>", "filterStyles", "denylist", "setAttribute", "match", "children", "querySelector", "svgElements", "querySelectorAll", "colorAttributes", "Array", "from", "attrValue", "getAttribute", "includes", "getPropertyValue", "moveTo", "scrollingContainer", "div", "onChartInit", "isDirtyExporting", "onChartLayOutTitle", "alignTo", "textPxLength", "space", "widthAdjust", "alignValue", "focus", "isDirty", "g", "split", "useForeignObject", "html", "allowHTML", "Exporting_Fullscreen", "chartProto", "callbacks", "matchMedia", "addListener", "mqlEvent", "matches", "Exporting_Exporting", "G", "Chart", "<PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAC/F,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,GAAM,CAACA,EAAK,KAAQ,CAAE,GAC3H,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,GAAM,CAAEA,EAAK,WAAc,CAAC,KAAQ,EAE/HA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACzG,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IA+GNC,EAg9BAC,EA+nBAC,EA9rDUC,EAAuB,CAE/B,IACC,AAACX,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,AAACjB,IACxB,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,IAAOnB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACrB,EAASuB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuFzB,EAAoB,KAC3G0B,EAA2G1B,EAAoBI,CAAC,CAACqB,GAElCzB,EAAoB,KAkBvH,AAAC,SAAUL,CAA0B,EAqBjCA,EAA2BgC,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHKA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAIC,EAAUF,EAAK,EAEnCA,CACX,CAYA,OAAME,EAMFC,YAAYH,CAAK,CAAE,CACf,IAAI,CAACI,OAAO,CAAG,EAAE,CACjB,IAAI,CAACJ,KAAK,CAAGA,CACjB,CAaAK,UAAUC,CAAQ,CAAE,CAChB,IAAI,CAACN,KAAK,CAACC,UAAU,CAACG,OAAO,CAACG,IAAI,CAACD,EACvC,CAIAE,OAAOC,CAAO,CAAEC,CAAM,CAAE,CACpB,IAAI,CAACN,OAAO,CAACO,OAAO,CAAC,AAACL,IAClBA,EAASd,IAAI,CAAC,IAAI,CAACQ,KAAK,CAAES,EAASC,EACvC,EACJ,CACJ,CACA3C,EAA2BmC,SAAS,CAAGA,CAC3C,EAAGnC,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAMnC,IAAM6C,EAAoC7C,EAcjE,CAAE8C,cAAAA,CAAa,CAAE,CAAIjB,IAo2BQkB,EALT,CACtBC,UAj1Bc,CAwBdC,kBAAmB,CAAA,EAmLnBC,KAAM,YAONC,IAAK,CAAC,oCAAoC,EAAE,AAACtB,IAA+EuB,OAAO,CAAC,CAAC,CAqBrIC,QAAS,CAQLC,OAAQ,KAAK,EAMbC,KAAM,KAAK,EAMXC,WAAY,KAAK,EAMjBC,OAAQ,KAAK,CACjB,EAUAC,cAAe,IAmBfC,MAAO,EAUPC,QAAS,CAWLC,cAAe,CAiCXC,UAAW,2BAIXC,cAAe,yBAgBfC,OAAQ,OASRC,SAAU,qBAoBVC,UAAW,CACP,iBACA,aACA,YACA,cACA,eACA,cACH,AACL,CACJ,EA6BAC,oBAAqB,CAIjBC,eAAgB,CACZC,QAAS,iBACTC,QAAS,WACD,IAAI,CAACC,UAAU,EACf,IAAI,CAACA,UAAU,CAACC,MAAM,EAE9B,CACJ,EAIAC,WAAY,CACRJ,QAAS,aACTC,QAAS,WACL,IAAI,CAACI,KAAK,EACd,CACJ,EAIAC,UAAW,CACPA,UAAW,CAAA,CACf,EAIAC,YAAa,CACTP,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,EACpB,CACJ,EAIAC,aAAc,CACVT,QAAS,eACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb3B,KAAM,YACV,EACJ,CACJ,EAIA6B,YAAa,CACTV,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb3B,KAAM,iBACV,EACJ,CACJ,EAIA8B,YAAa,CACTX,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb3B,KAAM,eACV,EACJ,CACJ,CACJ,CACJ,EAgWI+B,KA3VS,CAOTb,eAAgB,sBAOhBc,eAAgB,wBAOhBT,WAAY,cAOZG,YAAa,qBAObE,aAAc,sBAOdC,YAAa,wBAObC,YAAa,4BAQbG,mBAAoB,oBACxB,EAkSIjD,WA1Re,CAUfkD,cAAe,CAoBXC,WAAY,GASZC,QAAS,KASTC,QAAS,KAUTC,MAAO,QASPC,cAAe,EASfC,OAAQ,GAsCRC,EAAG,GAWHC,cAAe,MASfC,MAAO,GAUPC,WAAY,UAUZC,aAAc,UASdC,kBAAmB,EAcnBC,MAAO,CAMHC,KAAM,UAINC,QAAS,EAMTC,OAAQ,OAIR,iBAAkB,OACtB,CACJ,EAeAC,UAAW,CAEPC,OAAQ,OAERC,aAAc,MAEdC,WAAY,UAEZL,QAAS,OACb,EAiBAM,cAAe,CAEXD,WAAY,OAEZD,aAAc,MAEdG,MAAO,UAEPP,QAAS,QAETQ,SAAU7D,EAAgB,QAAU,QAEpC8D,WAAY,+BAChB,EAgBAC,mBAAoB,CAEhBL,WAAY,SAChB,CACJ,CAUA,GAsBA,AAAC,SAAUvG,CAAgB,EAMvB,IAAM6G,EAAkB,EAAE,CAsB1B,SAASC,EAAKC,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EAS7B,MARY,CACR,CAAC,IAAKsB,EAAGrB,EAAI,IAAI,CACjB,CAAC,IAAKqB,EAAInB,EAAOF,EAAI,IAAI,CACzB,CAAC,IAAKqB,EAAGrB,EAAID,EAAS,EAAI,GAAI,CAC9B,CAAC,IAAKsB,EAAInB,EAAOF,EAAID,EAAS,EAAI,GAAI,CACtC,CAAC,IAAKsB,EAAGrB,EAAID,EAAS,IAAI,CAC1B,CAAC,IAAKsB,EAAInB,EAAOF,EAAID,EAAS,IAAI,CACrC,AAEL,CAIA,SAASuB,EAASD,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EACjC,IAAMwB,EAAI,AAACxB,EAAS,EAAK,EACrByB,EAAO,EAAE,CAEb,OADOA,EAAKC,MAAM,CAAC,IAAI,CAACC,MAAM,CAACxB,EAAQqB,EAAGvB,EAAGuB,EAAGA,GAAI,IAAI,CAACG,MAAM,CAACxB,EAAQqB,EAAGvB,EAAIuB,EAAI,EAAGA,EAAGA,GAAI,IAAI,CAACG,MAAM,CAACxB,EAAQqB,EAAGvB,EAAI,EAAKuB,CAAAA,EAAI,CAAA,EAAIA,EAAGA,GAE5I,CAvBAjH,EAAiB+B,OAAO,CARxB,SAAiBsF,CAAgB,EAC7B,GAAIR,AAA8C,KAA9CA,EAAgBS,OAAO,CAACD,GAA0B,CAClDR,EAAgBtE,IAAI,CAAC8E,GACrB,IAAME,EAAUF,EAAiB/F,SAAS,CAACiG,OAAO,AAClDA,CAAAA,EAAQT,IAAI,CAAGA,EACfS,EAAQP,QAAQ,CAAGA,EAASQ,IAAI,CAACD,EACrC,CACJ,CAyBJ,EAAGvH,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMyH,EAA8BzH,EAsB3D,CAAE0H,SAAAA,CAAQ,CAAE,CAAI9F,IAEhB,CAAE+F,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAE,CAAIjG,IAS7C,SAASkG,IAML,IAAI,CAACxD,UAAU,CAAG,IAAIyD,EAAW,IAAI,CACzC,CAgBA,MAAMA,EAYF,OAAOhG,QAAQiG,CAAU,CAAE,CACnBH,EAAWH,EAAU,eAErBC,EAASK,EAAY,eAAgBF,EAE7C,CAMA3F,YAAYH,CAAK,CAAE,CAMf,IAAI,CAACA,KAAK,CAAGA,EASb,IAAI,CAACiG,MAAM,CAAG,CAAA,EACd,IAAMC,EAAYlG,EAAMmG,QAAQ,AAE5B,EAAC,IAAI,CAACC,YAAY,GACd,AAAuC,YAAvC,OAAOF,EAAUG,iBAAiB,CAClC,IAAI,CAACD,YAAY,CAAG,CAChBE,iBAAkB,mBAClBD,kBAAmB,oBACnBpD,eAAgB,gBACpB,EAEKiD,EAAUK,oBAAoB,CACnC,IAAI,CAACH,YAAY,CAAG,CAChBE,iBAAkB,sBAClBD,kBAAmB,uBACnBpD,eAAgB,qBACpB,EAEKiD,EAAUM,uBAAuB,CACtC,IAAI,CAACJ,YAAY,CAAG,CAChBE,iBAAkB,yBAClBD,kBAAmB,0BACnBpD,eAAgB,sBACpB,EAEKiD,EAAUO,mBAAmB,EAClC,CAAA,IAAI,CAACL,YAAY,CAAG,CAChBE,iBAAkB,qBAClBD,kBAAmB,sBACnBpD,eAAgB,kBACpB,CAAA,EAGZ,CAgBAyD,OAAQ,CACJ,IAAMpE,EAAa,IAAI,CAAEtC,EAAQsC,EAAWtC,KAAK,CAAE2G,EAAe3G,EAAMS,OAAO,CAACT,KAAK,CACrF4F,EAAU5F,EAAO,kBAAmB,KAAM,WAGlCsC,EAAW2D,MAAM,EACjB3D,EAAW8D,YAAY,EACvBpG,EAAMkG,SAAS,CAACU,aAAa,YAAYC,UACzC7G,EAAMkG,SAAS,CAACU,aAAa,CAACtE,EAAW8D,YAAY,CAACnD,cAAc,CAAC,GAIrEX,EAAWwE,qBAAqB,EAChCxE,CAAAA,EAAWwE,qBAAqB,CAAGxE,EAC9BwE,qBAAqB,EAAC,EAE/B9G,EAAM+G,OAAO,CAACzE,EAAW0E,SAAS,CAAE1E,EAAW2E,UAAU,CAAE,CAAA,GAC3D3E,EAAW0E,SAAS,CAAG,KAAK,EAC5B1E,EAAW2E,UAAU,CAAG,KAAK,EAC7BN,EAAa/C,KAAK,CAAGtB,EAAW4E,eAAe,CAC/CP,EAAalD,MAAM,CAAGnB,EAAW6E,gBAAgB,CACjD7E,EAAW4E,eAAe,CAAG,KAAK,EAClC5E,EAAW6E,gBAAgB,CAAG,KAAK,EACnC7E,EAAW2D,MAAM,CAAG,CAAA,EACpB3D,EAAW8E,aAAa,EAC5B,EACJ,CAaAC,MAAO,CACH,IAAM/E,EAAa,IAAI,CAAEtC,EAAQsC,EAAWtC,KAAK,CAAE2G,EAAe3G,EAAMS,OAAO,CAACT,KAAK,CACrF4F,EAAU5F,EAAO,iBAAkB,KAAM,WAQrC,GAPI2G,IACArE,EAAW4E,eAAe,CAAGP,EAAa/C,KAAK,CAC/CtB,EAAW6E,gBAAgB,CAAGR,EAAalD,MAAM,EAErDnB,EAAW0E,SAAS,CAAGhH,EAAMsH,UAAU,CACvChF,EAAW2E,UAAU,CAAGjH,EAAMuH,WAAW,CAErCjF,EAAW8D,YAAY,CAAE,CACzB,IAAMoB,EAAe7B,EAAS3F,EAAMkG,SAAS,CAACU,aAAa,CAC3DtE,EAAW8D,YAAY,CAACE,gBAAgB,CAAE,WAGlChE,EAAW2D,MAAM,EACjB3D,EAAW2D,MAAM,CAAG,CAAA,EACpB3D,EAAWoE,KAAK,KAGhB1G,EAAM+G,OAAO,CAAC,KAAM,KAAM,CAAA,GAC1BzE,EAAW2D,MAAM,CAAG,CAAA,EACpB3D,EAAW8E,aAAa,GAEhC,GACMK,EAAgB9B,EAAS3F,EAAO,UAAWwH,EACjDlF,CAAAA,EAAWwE,qBAAqB,CAAG,KAC/BU,IACAC,GACJ,EACA,IAAMC,EAAU1H,EAAMmG,QAAQ,CAAC7D,EAAW8D,YAAY,CAACC,iBAAiB,CAAC,GACrEqB,GACAA,EAAQ,KAAQ,CAAC,WACbC,MACA,+CACJ,EAER,CACJ,EACJ,CAWAP,eAAgB,CACZ,IAAMpH,EAAQ,IAAI,CAACA,KAAK,CAAE4H,EAAoB5H,EAAM4H,iBAAiB,CAAEC,EAAmB7H,EAAMS,OAAO,CAACM,SAAS,CAAEkB,EAAa4F,GAC5HA,EAAiBlG,OAAO,EACxBkG,EAAiBlG,OAAO,CAACC,aAAa,CAACK,SAAS,CAAGe,EAAOhD,EAAMS,OAAO,CAACuC,IAAI,CAChF,GAAI6E,GACAA,EAAiB3F,mBAAmB,EACpCc,GACAA,EAAKC,cAAc,EACnBD,EAAKb,cAAc,EACnBF,GACA2F,EAAmB,CACnB,IAAME,EAAmBF,CAAiB,CAAC3F,EAAUqD,OAAO,CAAC,kBAAkB,CAC3EwC,GACAhI,IAA8FiI,cAAc,CAACD,EAAkB,AAAC,IAAI,CAAC7B,MAAM,CAG5GjD,EAAKC,cAAc,CAF7C4E,EAAiB3F,mBAAmB,CAACC,cAAc,CAC/C6F,IAAI,EACLhF,EAAKb,cAAc,CAEnC,CACJ,CAeAI,QAAS,CAEAD,AADc,IAAI,CACP2D,MAAM,CAIlB3D,AALe,IAAI,CAKRoE,KAAK,GAHhBpE,AAFe,IAAI,CAER+E,IAAI,EAKvB,CACJ,CAiFA,GAAM,CAAEY,IAAAA,CAAG,CAAE,CAAIrI,IAEX,CAAEsI,eAAAA,CAAc,CAAEC,WAAAA,CAAU,CAAE,CAAIvI,IAsJlCwI,EAAgB,CAClBC,KAtIJ,SAAcC,CAAQ,EAClB,IAAMC,EAAU,CACZC,KAAM,mBACNC,IAAK,kBACLT,KAAM,aACNU,MAAO,0BACX,EAAGC,EAAI,IAAIC,eASX,SAASC,EAAYC,CAAG,CAAEC,CAAG,EACrBT,EAASU,KAAK,EACdV,EAASU,KAAK,CAACF,EAAKC,EAK5B,CACA,GAAI,CAACT,EAASpH,GAAG,CACb,MAAO,CAAA,EAEXyH,EAAEtB,IAAI,CAAC,AAACiB,CAAAA,EAASrH,IAAI,EAAI,KAAI,EAAGgI,WAAW,GAAIX,EAASpH,GAAG,CAAE,CAAA,GACxDoH,EAASC,OAAO,EAAE,CAAC,eAAe,EACnCI,EAAEO,gBAAgB,CAAC,eAAgBX,CAAO,CAACD,EAASa,QAAQ,EAAI,OAAO,EAAIZ,EAAQP,IAAI,EAE3FG,EAAWG,EAASC,OAAO,CAAE,SAAUa,CAAG,CAAEtK,CAAG,EAC3C6J,EAAEO,gBAAgB,CAACpK,EAAKsK,EAC5B,GACId,EAASe,YAAY,EACrBV,CAAAA,EAAEU,YAAY,CAAGf,EAASe,YAAY,AAAD,EAGzCV,EAAEW,kBAAkB,CAAG,WACnB,IAAIC,EACJ,GAAIZ,AAAiB,IAAjBA,EAAEa,UAAU,CAAQ,CACpB,GAAIb,AAAa,MAAbA,EAAEc,MAAM,CAAU,CAClB,GAAInB,AAA0B,SAA1BA,EAASe,YAAY,GACrBE,EAAMZ,EAAEe,YAAY,CAChBpB,AAAsB,SAAtBA,EAASa,QAAQ,EACjB,GAAI,CACAI,EAAMI,KAAKC,KAAK,CAACL,EACrB,CACA,MAAOM,EAAG,CACN,GAAIA,aAAaC,MACb,OAAOjB,EAAYF,EAAGkB,EAE9B,CAGR,OAAOvB,EAASyB,OAAO,GAAGR,EAAKZ,EACnC,CACAE,EAAYF,EAAGA,EAAEe,YAAY,CACjC,CACJ,EACIpB,EAAS0B,IAAI,EAAI,AAAyB,UAAzB,OAAO1B,EAAS0B,IAAI,EACrC1B,CAAAA,EAAS0B,IAAI,CAAGL,KAAKM,SAAS,CAAC3B,EAAS0B,IAAI,CAAA,EAEhDrB,EAAEuB,IAAI,CAAC5B,EAAS0B,IAAI,CACxB,EAwEIG,QA7DJ,SAAiBjJ,CAAG,CAAE6I,CAAO,EACzB3B,EAAcC,IAAI,CAAC,CACfnH,IAAKA,EACL6I,QAASA,EACTZ,SAAU,OACVZ,QAAS,CAGL,eAAgB,YACpB,CACJ,EACJ,EAmDI6B,KAhCJ,SAAclJ,CAAG,CAAE8I,CAAI,CAAEK,CAAY,EACjC,IAAMC,EAAW,IAAIrC,EAAIsC,QAAQ,CAEjCpC,EAAW6B,EAAM,SAAUZ,CAAG,CAAEoB,CAAI,EAChCF,EAASG,MAAM,CAACD,EAAMpB,EAC1B,GACAkB,EAASG,MAAM,CAAC,MAAO,QACvB,GAAM,CAAEC,SAAAA,CAAQ,CAAEzJ,KAAAA,CAAI,CAAE,CAAG+I,EAC3B,OAAO/B,EAAI0C,KAAK,CAACzJ,EAAK,CAClB0J,OAAQ,OACRC,KAAMP,EACN,GAAGD,CAAY,AACnB,GAAGS,IAAI,CAAC,AAACvB,IACDA,EAAIwB,EAAE,EACNxB,EAAIvB,IAAI,GAAG8C,IAAI,CAAC,AAAC9C,IACb,IAAMgD,EAAOC,SAASC,aAAa,CAAC,IACpCF,CAAAA,EAAKG,IAAI,CAAG,CAAC,KAAK,EAAElK,EAAK,QAAQ,EAAE+G,EAAK,CAAC,CACzCgD,EAAKI,QAAQ,CAAGV,EAChBM,EAAKK,KAAK,GACVnD,EAAe8C,EACnB,EAER,EACJ,CAUA,EA0DM,CAAEM,eAAAA,CAAc,CAAE,CAAI1L,IAKtB,CAAE2L,IAAAA,CAAG,CAAEC,OAAAA,CAAM,CAAEvD,IAAKwD,CAAa,CAAE,CAAI7L,IAGvC,CAAE+F,SAAU+F,CAAkB,CAAEC,IAAAA,CAAG,CAAET,cAAAA,CAAa,CAAEhD,eAAgB0D,CAAwB,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAElG,UAAWmG,CAAmB,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE9D,WAAY+D,CAAoB,CAAEC,KAAAA,CAAI,CAAEC,YAAAA,CAAW,CAAEC,MAAAA,CAAK,CAAEC,UAAAA,CAAS,CAAE,CAAI1M,KAO9O,AAAC,SAAU3B,CAAS,EAYhB,IAmCIsO,EAnCEC,EAAiB,CACnB,IACA,sCACA,SACA,4BACA,eACA,0BACA,cACA,oBACA,cACA,WACA,QACH,CAEKC,EAAqB,CACvB,OACA,SACA,gBACA,iBACA,cACA,aACA,IACA,IACH,AACDxO,CAAAA,EAAUyO,eAAe,CAAG,EAAE,CAC9B,IAAMC,EAAmB,CACrB,WACA,OACA,OACH,CAoBD,SAASC,EAAUnM,CAAO,EACtB,IACIsB,EAaA8K,EAdE7M,EAAQ,IAAI,CAAE8M,EAAW9M,EAAM8M,QAAQ,CAAEC,EAAad,EAAMjM,EAAMS,OAAO,CAACR,UAAU,CAACkD,aAAa,CAAE1C,GAAU4B,EAAU0K,EAAW1K,OAAO,CAAEJ,EAAY8K,EAAW9K,SAAS,CAAEmB,EAAa2J,EAAW3J,UAAU,EAAI,GAU1N,GARKpD,EAAMgN,QAAQ,EACfhN,CAAAA,EAAMgN,QAAQ,CAAG,CAAA,EAGhBhN,EAAM4H,iBAAiB,GACxB5H,EAAM4H,iBAAiB,CAAG,EAAE,CAC5B5H,EAAMiN,iBAAiB,CAAG,EAAE,EAE5BF,AAAuB,CAAA,IAAvBA,EAAWG,OAAO,EAAc,CAACH,EAAW/I,KAAK,CACjD,OAEJ,IAAMA,EAAQhE,EAAMmN,UAAU,CAAG,CAAC,EAAIJ,EAAW/I,KAAK,CAElD3B,EACAwK,EAAW,SAAUhD,CAAC,EACdA,GACAA,EAAEuD,eAAe,GAErB/K,EAAQ7C,IAAI,CAACQ,EAAO6J,EACxB,EAEK5H,GACL4K,CAAAA,EAAW,SAAUhD,CAAC,EAEdA,GACAA,EAAEuD,eAAe,GAErBpN,EAAMqN,WAAW,CAACC,EAAOxL,aAAa,CAAEG,EAAWqL,EAAOC,UAAU,EAAI,EAAGD,EAAOE,UAAU,EAAI,EAAGF,EAAO1J,KAAK,EAAI,EAAG0J,EAAO7J,MAAM,EAAI,EAAG6J,GAC1IA,EAAOG,QAAQ,CAAC,EACpB,CAAA,EAEAV,EAAW/E,IAAI,EAAI+E,EAAWhL,MAAM,CACpCiC,EAAM0J,WAAW,CAAGvB,EAAKnI,EAAM0J,WAAW,CAAE,IAEtCX,EAAW/E,IAAI,EACrB6D,EAAO7H,EAAO,CACVJ,MAAOmJ,EAAWnJ,KAAK,CACvBH,OAAQsJ,EAAWtJ,MAAM,CACzBS,QAAS,CACb,GAEJ,IAAMoJ,EAASR,EACVQ,MAAM,CAACP,EAAW/E,IAAI,CAAE,EAAG,EAAG6E,EAAU7I,EAAO,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG+I,EAAWY,OAAO,EACjGC,QAAQ,CAACnN,EAAQoB,SAAS,EAC1BgM,IAAI,CAAC,CACNC,MAAO3B,EAAKnM,EAAMS,OAAO,CAACuC,IAAI,CAAC+J,EAAWgB,SAAS,EAAIhB,EAAW/K,QAAQ,CAAC,CAAE,GACjF,EACAsL,CAAAA,EAAOxL,aAAa,CAAIrB,EAAQqB,aAAa,EACzC,mBAAqB9B,EAAMgN,QAAQ,GACnCD,EAAWhL,MAAM,GACjBA,EAAS+K,EACJ/K,MAAM,CAACgL,EAAWhL,MAAM,CAAEiM,KAAKC,KAAK,CAAC,AAAClB,CAAAA,EAAW1J,OAAO,EAAI,CAAA,EAAMD,EAAa,GAAK4K,KAAKC,KAAK,CAAC,AAAClB,CAAAA,EAAWzJ,OAAO,EAAI,CAAA,EAAMF,EAAa,GAAKA,EAAYA,EAE7J,CACEQ,MAAOR,EACPK,OAAQL,CACZ,GACKwK,QAAQ,CAAC,4BACTC,IAAI,CAAC,CACNK,OAAQ,CACZ,GACKC,GAAG,CAACb,GACJtN,EAAMmN,UAAU,EACjBpL,EAAO8L,IAAI,CAAC,CACR1J,OAAQ4I,EAAWjJ,YAAY,CAC/BG,KAAM8I,EAAWlJ,UAAU,CAC3B,eAAgBkJ,EAAWhJ,iBAAiB,EAAI,CACpD,IAGRuJ,EACKa,GAAG,CAACnO,EAAMoO,cAAc,EACxB7K,KAAK,CAACsI,EAAOkB,EAAY,CAC1BnJ,MAAO0J,EAAO1J,KAAK,CACnBmB,EAAGoH,EAAKY,EAAWhI,CAAC,CAAE/E,EAAMqO,YAAY,CAC5C,GAAI,CAAA,EAAM,cACVrO,EAAMqO,YAAY,EAAK,AAAC,CAAA,AAACf,CAAAA,EAAO1J,KAAK,EAAI,CAAA,EAAKmJ,EAAWvJ,aAAa,AAAD,EAChEuJ,CAAAA,AAAqB,UAArBA,EAAWxJ,KAAK,CAAe,GAAK,CAAA,EACzCvD,EAAMiN,iBAAiB,CAAC1M,IAAI,CAAC+M,EAAQvL,EACzC,CAaA,SAASuM,IAEL,GAAI,CAACtO,AADS,IAAI,CACPuO,gBAAgB,CACvB,OAEJ,GAAM,CAAEC,WAAAA,CAAU,CAAEC,YAAAA,CAAW,CAAEC,YAAAA,CAAW,CAAE,CAAG1O,AAJnC,IAAI,CAIqCuO,gBAAgB,CAEvEvO,AANc,IAAI,CAMZ2O,cAAc,CAAC3O,AANP,IAAI,CAMSmG,QAAQ,EAEnC,EAAE,CAACxF,OAAO,CAACnB,IAAI,CAACgP,EAAY,SAAUI,CAAI,CAAEC,CAAC,EACnB,IAAlBD,EAAKE,QAAQ,EACbF,CAAAA,EAAKG,KAAK,CAACC,OAAO,CAAIP,CAAW,CAACI,EAAE,EAAI,EAAE,CAElD,GACA7O,AAbc,IAAI,CAaZiP,UAAU,CAAG,CAAA,EAEfP,GACA1O,AAhBU,IAAI,CAgBR+G,OAAO,CAACmI,KAAK,CAhBT,IAAI,CAgBaR,GAE/B,OAAO1O,AAlBO,IAAI,CAkBLuO,gBAAgB,CAC7BhC,EAAgB,KAAK,EACrBR,EApBc,IAAI,CAoBS,aAC/B,CAWA,SAASoD,IACL,IAAoBtE,EAAOU,EAAIV,IAAI,CAAEpJ,EAAgBzB,AAAvC,IAAI,CAAyCS,OAAO,CAACM,SAAS,CAACU,aAAa,CAAE8M,EAAmB,CAC3GC,WAAY3D,EAAK2D,UAAU,CAC3BC,YAAa,EAAE,CACfC,YAAa,KAAK,CACtB,CACA1O,CALc,IAAI,CAKZiP,UAAU,CAAG,CAAA,EACnBjP,AANc,IAAI,CAMZoP,OAAO,EAAEC,MAAM,KAAK,EAAG,GAC7BtD,EAPc,IAAI,CAOS,eAEJtK,GACnBzB,AAVU,IAAI,CAURsH,UAAU,CAAG7F,IAEnB8M,EAAiBG,WAAW,CAAG,CAC3B1O,AAbM,IAAI,CAaJS,OAAO,CAACT,KAAK,CAAC4D,KAAK,CACzB,KAAK,EACL,CAAA,EACH,CACD5D,AAjBU,IAAI,CAiBR+G,OAAO,CAACtF,EAAe,KAAK,EAAG,CAAA,IAGzC,EAAE,CAACd,OAAO,CAACnB,IAAI,CAAC+O,EAAiBC,UAAU,CAAE,SAAUI,CAAI,CAAEC,CAAC,EACpC,IAAlBD,EAAKE,QAAQ,GACbP,EAAiBE,WAAW,CAACI,EAAE,CAAGD,EAAKG,KAAK,CAACC,OAAO,CACpDJ,EAAKG,KAAK,CAACC,OAAO,CAAG,OAE7B,GAEAhP,AA3Bc,IAAI,CA2BZ2O,cAAc,CAAC9D,GAErB7K,AA7Bc,IAAI,CA6BZuO,gBAAgB,CAAGA,CAC7B,CAIA,SAASe,EAActP,CAAK,EAExBuP,AADoBvP,EACRwP,eAAe,GAC3B9D,EAAmB1L,EAAO,SAAUuP,AAFhBvP,EAE4BwP,eAAe,EAE/D9D,EAAmB1L,EAAO,UAAWuP,AAJjBvP,EAI6ByP,aAAa,CAmClE,CAqEA,SAASpC,EAAYxL,CAAS,CAAE6N,CAAK,CAAE3K,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,CAAE6J,CAAM,EAC9D,IAAMtN,EAAQ,IAAI,CAAE2P,EAAa3P,EAAMS,OAAO,CAACR,UAAU,CAAEqH,EAAatH,EAAMsH,UAAU,CAAEC,EAAcvH,EAAMuH,WAAW,CAAEqI,EAAY,SAAW/N,EAElJgO,EAAc7B,KAAK8B,GAAG,CAAClM,EAAOH,GAC1BsM,EAAWjL,EAAO9E,CAAK,CAAC4P,EAAU,CAEjC9K,IAED9E,EAAMgQ,iBAAiB,CAAGhQ,CAAK,CAAC4P,EAAU,CAAG9K,EACzCoG,EAAc,MAAO,CACjBrJ,UAAWA,CACf,EAAG,CACCoO,SAAU,WACV/B,OAAQ,IACRhK,QAAS2L,EAAc,KACvBK,cAAe,OACf,GAAGlQ,EAAM8M,QAAQ,CAACiC,KAAK,AAC3B,EAAG/O,EAAMmQ,kBAAkB,EAAEC,UAAYpQ,EAAMkG,SAAS,EAC5D6J,EAAY7E,EAAc,KAAM,CAAErJ,UAAW,iBAAkB,EAAG7B,EAAMmN,UAAU,CAAG,CAAC,EAAI,CACtFkD,UAAW,OACXC,OAAQ,EACRpM,QAAS,CACb,EAAGY,GAEE9E,EAAMmN,UAAU,EACjBxB,EAAIoE,EAAWlE,EAAO,CAClB0E,aAAc,oBACdC,gBAAiB,oBACjBC,UAAW,mBACf,EAAGd,EAAWvL,SAAS,GAG3BU,EAAK4L,QAAQ,CAAG,WACZ/E,EAAI7G,EAAM,CAAEkK,QAAS,MAAO,GACxB1B,GACAA,EAAOG,QAAQ,CAAC,GAEpBzN,EAAM2Q,QAAQ,CAAG,CAAA,EAEjBhF,EAAI3L,EAAMmG,QAAQ,CAAE,CAAEyK,SAAU,QAAS,GACzCjF,EAAI3L,EAAMkG,SAAS,CAAE,CAAE0K,SAAU,QAAS,GAC1ChR,IAA8EiR,YAAY,CAAC/L,EAAKgM,SAAS,EACzG/E,EAAoB/L,EAAO,mBAC/B,EAEAA,EAAM+Q,YAAY,CAACxQ,IAAI,CAACmL,EAAmB5G,EAAM,aAAc,WAC3DA,EAAKgM,SAAS,CAAGrF,EAAcuF,UAAU,CAAClM,EAAK4L,QAAQ,CAAE,IAC7D,GAAIhF,EAAmB5G,EAAM,aAAc,WACvClF,IAA8EiR,YAAY,CAAC/L,EAAKgM,SAAS,CAC7G,GAGApF,EAAmBH,EAAK,UAAW,SAAU1B,CAAC,EACrC7J,EAAMoP,OAAO,EAAE6B,QAAQpH,EAAEqH,MAAM,CAAErP,IAClCiD,EAAK4L,QAAQ,EAErB,GAAIhF,EAAmB5G,EAAM,QAAS,WAC9B9E,EAAM2Q,QAAQ,EACd7L,EAAK4L,QAAQ,EAErB,IAEAhB,EAAM/O,OAAO,CAAC,SAAUwQ,CAAI,EAKxB,GAJoB,UAAhB,OAAOA,GACPA,CAAAA,EAAOnR,EAAMS,OAAO,CAACM,SAAS,CACzBmB,mBAAmB,CAACiP,EAAK,AAAD,EAE7BnF,EAASmF,EAAM,CAAA,GAAO,CACtB,IAAIC,CACAD,CAAAA,EAAKzO,SAAS,CACd0O,EAAUlG,EAAc,KAAM,KAAK,EAAG,KAAK,EAAG6E,IAKzB,aAAjBoB,EAAK/O,OAAO,EACZpC,EAAMqR,kBAAkB,EACxBF,CAAAA,EAAK/O,OAAO,CAAG,UAAS,EAE5BgP,EAAUlG,EAAc,KAAM,CAC1BrJ,UAAW,uBACXQ,QAAS,SAAUwH,CAAC,EACZA,GACAA,EAAEuD,eAAe,GAErBtI,EAAK4L,QAAQ,GACO,UAAhB,OAAOS,GAAqBA,EAAK9O,OAAO,EACxC8O,EAAK9O,OAAO,CAAC6M,KAAK,CAAClP,EAAOsR,UAElC,CACJ,EAAG,KAAK,EAAGvB,GACXjQ,IAA8FiI,cAAc,CAACqJ,EAASD,EAAKnJ,IAAI,EAC3HhI,EAAMS,OAAO,CAACuC,IAAI,CAACmO,EAAK/O,OAAO,CAAC,EAC/BpC,EAAMmN,UAAU,GACjBiE,EAAQG,WAAW,CAAG,WAClB5F,EAAI,IAAI,CAAEgE,EAAW/K,kBAAkB,CAC3C,EACAwM,EAAQI,UAAU,CAAG,WACjB7F,EAAI,IAAI,CAAEgE,EAAWnL,aAAa,CACtC,EACAmH,EAAIyF,EAASvF,EAAO,CAChB4F,OAAQ,SACZ,EAAG9B,EAAWnL,aAAa,EAAI,CAAC,MAIxCxE,EAAM4H,iBAAiB,CAACrH,IAAI,CAAC6Q,EACjC,CACJ,GAGApR,EAAM4H,iBAAiB,CAACrH,IAAI,CAACwP,EAAWjL,GACxC9E,EAAM0R,eAAe,CAAG5M,EAAK6M,WAAW,CACxC3R,EAAM4R,gBAAgB,CAAG9M,EAAK+M,YAAY,EAE9C,IAAMzN,EAAY,CAAE4K,QAAS,OAAQ,CAEjCjK,CAAAA,EAAK/E,CAAAA,EAAM0R,eAAe,EAAI,CAAA,EAAKpK,EACnClD,EAAU0N,KAAK,CAAG,AAACxK,EAAavC,EAAInB,EAAQiM,EAAe,KAG3DzL,EAAU2N,IAAI,CAAG,AAAChN,EAAI8K,EAAe,KAGrCnM,EAAID,EAAUzD,CAAAA,EAAM4R,gBAAgB,EAAI,CAAA,EAAKrK,GAC7C+F,EAAO0E,YAAY,EAAErO,gBAAkB,MACvCS,EAAU6N,MAAM,CAAG,AAAC1K,EAAc7D,EAAImM,EAAe,KAGrDzL,EAAU8N,GAAG,CAAG,AAACxO,EAAID,EAASoM,EAAe,KAEjDlE,EAAI7G,EAAMV,GAEVuH,EAAI3L,EAAMmG,QAAQ,CAAE,CAAEyK,SAAU,EAAG,GACnCjF,EAAI3L,EAAMkG,SAAS,CAAE,CAAE0K,SAAU,EAAG,GACpC5Q,EAAM2Q,QAAQ,CAAG,CAAA,EACjB5E,EAAoB/L,EAAO,kBAC/B,CAQA,SAASyP,EAAc5F,CAAC,EACpB,IACI+F,EADE5P,EAAQ6J,EAAIA,EAAEqH,MAAM,CAAG,IAAI,CAAEjE,EAAoBjN,EAAMiN,iBAAiB,CAAErF,EAAoB5H,EAAM4H,iBAAiB,CAAEmJ,EAAe/Q,EAAM+Q,YAAY,CAG1J9D,IACAA,EAAkBtM,OAAO,CAAC,CAACwR,EAAMtD,KAEzBsD,IACAA,EAAK9P,OAAO,CAAG8P,EAAKC,YAAY,CAAG,KAE/BpS,CAAK,CADT4P,EAAY,SAAWuC,EAAKrQ,aAAa,CACrB,EAChB,OAAO9B,CAAK,CAAC4P,EAAU,CAE3B3C,CAAiB,CAAC4B,EAAE,CAAGsD,EAAKE,OAAO,GAE3C,GACApF,EAAkBqF,MAAM,CAAG,GAG3BtS,EAAMoO,cAAc,GACpBpO,EAAMoO,cAAc,CAACiE,OAAO,GAC5B,OAAOrS,EAAMoO,cAAc,EAG3BxG,IACAA,EAAkBjH,OAAO,CAAC,SAAUwR,CAAI,CAAEtD,CAAC,EACnCsD,IAEAvS,IAA8EiR,YAAY,CAACsB,EAAKrB,SAAS,EACzG1E,EAAY+F,EAAM,cAGlBvK,CAAiB,CAACiH,EAAE,CAChBsD,EAAKX,UAAU,CACXW,EAAKZ,WAAW,CACZY,EAAKC,YAAY,CACbD,EAAK9P,OAAO,CAAG,KAE/BuJ,EAAyBuG,GAEjC,GACAvK,EAAkB0K,MAAM,CAAG,GAE3BvB,IACAA,EAAapQ,OAAO,CAAC,SAAU4R,CAAM,EACjCA,GACJ,GACAxB,EAAauB,MAAM,CAAG,EAE9B,CA2BA,SAAS1P,EAAYiF,CAAgB,CAAE2K,CAAY,EAC/C,IAAMC,EAAM,IAAI,CAACC,eAAe,CAAC7K,EAAkB2K,GAEnD3K,EAAmBoE,EAAM,IAAI,CAACxL,OAAO,CAACM,SAAS,CAAE8G,GAEjD8K,AAvnBiDvK,EAunB9BgC,IAAI,CAACvC,EAAiB3G,GAAG,CAAE,CAC1CwJ,SAAU7C,EAAiB6C,QAAQ,CAC/B7C,EAAiB6C,QAAQ,CAACkI,OAAO,CAAC,MAAO,KACzC,IAAI,CAACC,WAAW,GACpB5R,KAAM4G,EAAiB5G,IAAI,CAC3B2C,MAAOiE,EAAiBjE,KAAK,CAC7BlC,MAAOmG,EAAiBnG,KAAK,CAC7B+Q,IAAKA,CACT,EAAG5K,EAAiBwC,YAAY,CACpC,CAcA,SAASyI,EAAaC,CAAgB,EAKlC,OAJIA,GACA,IAAI,CAACC,YAAY,GAErB,IAAI,CAACC,mBAAmB,GACjB,IAAI,CAAC/M,SAAS,CAACgN,SAAS,AACnC,CAWA,SAASL,IACL,IAAMM,EAAI,IAAI,CAACC,WAAW,CAACtF,KAAK,EAAI,IAAI,CAACsF,WAAW,CAACtF,KAAK,CAAC9F,IAAI,CAC3D0C,EAAW,IAAI,CAACjK,OAAO,CAACM,SAAS,CAAC2J,QAAQ,QAC9C,AAAIA,EACOA,EAASkI,OAAO,CAAC,MAAO,MAElB,UAAb,OAAOO,GACPzI,CAAAA,EAAWyI,EACNE,WAAW,GACXT,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,UAAW,KACnBA,OAAO,CAAC,cAAe,IACvBA,OAAO,CAAC,UAAW,IACnBA,OAAO,CAAC,SAAU,KAClBU,MAAM,CAAC,EAAG,IACVV,OAAO,CAAC,UAAW,GAAE,EAE1B,CAAA,CAAClI,GAAYA,EAAS4H,MAAM,CAAG,CAAA,GAC/B5H,CAAAA,EAAW,OAAM,EAEdA,EACX,CAsBA,SAAS6I,EAAOf,CAAY,EAExB,IAAIC,EAAKe,EAET/S,EAAUwL,EAAMjM,AAHF,IAAI,CAGIS,OAAO,CAAE+R,EAE/B/R,CAAAA,EAAQgT,WAAW,CAAGxH,EAAMjM,AALd,IAAI,CAKgBoT,WAAW,CAACK,WAAW,CAAEjB,GAAgBA,EAAaiB,WAAW,EAGnGhT,EAAQiT,IAAI,CAAGzH,EAAMjM,AARP,IAAI,CAQSoT,WAAW,CAACM,IAAI,CAAElB,GAAgBA,EAAakB,IAAI,EAE9E,IAAMC,EAAUzI,EAAc,MAAO,KAAM,CACvC+E,SAAU,WACViC,IAAK,UACLtO,MAAO5D,AAbG,IAAI,CAaDsH,UAAU,CAAG,KAC1B7D,OAAQzD,AAdE,IAAI,CAcAuH,WAAW,CAAG,IAChC,EAAGgE,EAAIV,IAAI,EAEL+I,EAAW5T,AAjBH,IAAI,CAiBKmG,QAAQ,CAAC4I,KAAK,CAACnL,KAAK,CAAEiQ,EAAY7T,AAjB3C,IAAI,CAiB6CmG,QAAQ,CAAC4I,KAAK,CAACtL,MAAM,CAAEqQ,EAAcrT,EAAQM,SAAS,CAAC+S,WAAW,EAC7HrT,EAAQT,KAAK,CAAC4D,KAAK,EAClB,MAAMmQ,IAAI,CAACH,IAAaI,SAASJ,EAAU,KAC3CnT,CAAAA,EAAQwT,OAAO,CAAG,IAAM,GAAE,EAAIC,EAAezT,EAAQM,SAAS,CAACmT,YAAY,EAC5EzT,EAAQT,KAAK,CAACyD,MAAM,EACnB,MAAMsQ,IAAI,CAACF,IAAcG,SAASH,EAAW,KAC9C,IAEJhI,EAAOpL,EAAQT,KAAK,CAAE,CAClBmU,UAAW,CAAA,EACXhO,SAAUwN,EACVS,UAAW,CAAA,EACXtH,SAAU,cACVlJ,MAAOkQ,EACPrQ,OAAQyQ,CACZ,GACAzT,EAAQM,SAAS,CAACmM,OAAO,CAAG,CAAA,EAC5B,OAAOzM,EAAQuJ,IAAI,CAEnBvJ,EAAQ4T,MAAM,CAAG,EAAE,CACnBrU,AArCc,IAAI,CAqCZqU,MAAM,CAAC1T,OAAO,CAAC,SAAU2T,CAAK,EAQ3Bd,AAPLA,CAAAA,EAAgBvH,EAAMqI,EAAMlB,WAAW,CAAE,CACrCe,UAAW,CAAA,EACXI,oBAAqB,CAAA,EACrBC,aAAc,CAAA,EACdC,QAASH,EAAMG,OAAO,AAC1B,EAAC,EAEkBC,UAAU,EACzBjU,EAAQ4T,MAAM,CAAC9T,IAAI,CAACiT,EAE5B,GACA,IAAMmB,EAAQ,CAAC,EACf3U,AAlDc,IAAI,CAkDZ4U,IAAI,CAACjU,OAAO,CAAC,SAAUkU,CAAI,EAExBA,EAAKzB,WAAW,CAAC0B,WAAW,EAC7BD,CAAAA,EAAKzB,WAAW,CAAC0B,WAAW,CAAGxI,GAAU,EAExCuI,EAAKpU,OAAO,CAACiU,UAAU,GACnBC,CAAK,CAACE,EAAKE,IAAI,CAAC,GACjBJ,CAAK,CAACE,EAAKE,IAAI,CAAC,CAAG,CAAA,EACnBtU,CAAO,CAACoU,EAAKE,IAAI,CAAC,CAAG,EAAE,EAE3BtU,CAAO,CAACoU,EAAKE,IAAI,CAAC,CAACxU,IAAI,CAAC0L,EAAM4I,EAAKzB,WAAW,CAAE,CAC5CqB,QAASI,EAAKJ,OAAO,CAGrBxT,KAAM4T,EAAK5T,IAAI,CACf+T,YAAaH,EAAKG,WAAW,AACjC,IAER,GAIAvU,EAAQwU,SAAS,CAAGjV,AAxEN,IAAI,CAwEQoT,WAAW,CAAC6B,SAAS,CAE/C,IAAMC,EAAY,IAAIlV,AA1ER,IAAI,CA0EUG,WAAW,CAACM,EAAST,AA1EnC,IAAI,CA0EqC6M,QAAQ,EAyC/D,OAvCI2F,GACA,CAAC,QAAS,QAAS,SAAS,CAAC7R,OAAO,CAAC,SAAUoU,CAAI,EAC/C,IAAMI,EAAc,CAAC,CACjB3C,CAAAA,CAAY,CAACuC,EAAK,GAClBI,CAAW,CAACJ,EAAK,CAAGvC,CAAY,CAACuC,EAAK,CACtCG,EAAU1U,MAAM,CAAC2U,GAEzB,GAGJnV,AAtFc,IAAI,CAsFZ4U,IAAI,CAACjU,OAAO,CAAC,SAAUkU,CAAI,EAC7B,IAAMO,EAAWtJ,EAAKoJ,EAAUN,IAAI,CAAE,AAACS,GAASA,EAAK5U,OAAO,CAACqU,WAAW,GAAKD,EAAKzB,WAAW,CAAC0B,WAAW,EACzG,GAAIM,EAAU,CACV,IAAME,EAAWT,EAAKU,WAAW,GAKjCC,EAAiBnJ,EAAMmG,GAAc,CAACqC,EAAKE,IAAI,CAAC,EAAI,CAAC,EAAE,CAAC,EAAE,CAAEU,EAAU,QAASD,EAC3EA,EAAeE,GAAG,CAClBJ,EAASG,OAAO,CAAEE,EAAU,QAASH,EACrCA,EAAe1F,GAAG,CAClBwF,EAASK,OAAO,CACf,CAAA,AAAoB,KAAA,IAAZF,GACTA,IAAYL,EAASM,GAAG,EAAM,AAAmB,KAAA,IAAZC,GACrCA,IAAYP,EAAStF,GAAG,GACxBsF,EAASQ,WAAW,CAACH,GAAW,KAAK,EAAGE,GAAW,KAAK,EAAG,CAAA,EAAM,CAAA,EAEzE,CACJ,GAEAlD,EAAMyC,EAAUpC,YAAY,CAAC9S,AA3Gf,IAAI,CA2GiBmN,UAAU,EACzC1M,EAAQM,SAAS,EAAEgS,kBACvBhH,EAAoB,IAAI,CAAE,SAAU,CAAEmJ,UAAWA,CAAU,GAC3DzC,EAAMzS,AA9GQ,IAAI,CA8GN6V,WAAW,CAACpD,EAAKhS,GAE7BA,EAAU,KACVyU,EAAU7C,OAAO,GACjBzG,EAAyB+H,GAClBlB,CACX,CAKA,SAASC,EAAgBjS,CAAO,CAAE+R,CAAY,EAC1C,IAAMsD,EAAwB,IAAI,CAACrV,OAAO,CAACM,SAAS,CACpD,OAAO,IAAI,CAACwS,MAAM,CAACtH,EAAM,CAAEjM,MAAO,CAAEsE,aAAc,CAAE,CAAE,EAAGwR,EAAsBtD,YAAY,CAAEA,EAAc,CACvGzR,UAAW,CACP+S,YAAc,AAACrT,GAAWA,EAAQqT,WAAW,EACzCgC,EAAsBhC,WAAW,CACrCI,aAAe,AAACzT,GAAWA,EAAQyT,YAAY,EAC3C4B,EAAsB5B,YAAY,AAC1C,CACJ,GACJ,CA2BA,SAASlB,IACL,IAEI+C,EAF6BC,EAAY/X,EAAUyO,eAAe,CACtEuJ,EAAgB,CAAC,EAIXC,EAAS3K,EAAIL,aAAa,CAAC,UACjCS,EAAIuK,EAAQ,CACRtS,MAAO,MACPH,OAAQ,MACR0S,WAAY,QAChB,GACA5K,EAAIV,IAAI,CAACuL,WAAW,CAACF,GACrB,IAAMG,EAAaH,EAAOI,aAAa,EAAIJ,EAAOI,aAAa,CAACrL,QAAQ,CACpEoL,GACAA,EAAUxL,IAAI,CAACuL,WAAW,CAACC,EAAUE,eAAe,CAAC/K,EAAQ,SAyIjEgL,AAjIA,SAASA,EAAQ5H,CAAI,EACjB,IACI6H,EAAQC,EAAcC,EAAOC,EAAYC,EAAahI,EADpDiI,EAAiB,CAAC,EAwDxB,GAAIT,GACAzH,AAAkB,IAAlBA,EAAKE,QAAQ,EACbnC,AAA4C,KAA5CA,EAAiBrH,OAAO,CAACsJ,EAAKmI,QAAQ,EAAU,CAOhD,GANAN,EAAShL,EAAcuL,gBAAgB,CAACpI,EAAM,MAC9C8H,EAAe9H,AAAkB,QAAlBA,EAAKmI,QAAQ,CACxB,CAAC,EACDtL,EAAcuL,gBAAgB,CAACpI,EAAKqI,UAAU,CAAE,MAGhD,CAAChB,CAAa,CAACrH,EAAKmI,QAAQ,CAAC,CAAE,CAQ/BhB,EAAWM,EAAUa,oBAAoB,CAAC,MAAM,CAAC,EAAE,CACnDP,EAAQN,EAAUE,eAAe,CAAC3H,EAAKuI,YAAY,CAAEvI,EAAKmI,QAAQ,EAClEhB,EAASK,WAAW,CAACO,GAGrB,IAAMxD,EAAI1H,EAAcuL,gBAAgB,CAACL,EAAO,MAAOS,EAAW,CAAC,EACnE,IAAK,IAAMtY,KAAOqU,EACVrU,EAAIwT,MAAM,CAAG,KACb,AAAkB,UAAlB,OAAOa,CAAC,CAACrU,EAAI,EACb,CAAC,QAAQiV,IAAI,CAACjV,IACdsY,CAAAA,CAAQ,CAACtY,EAAI,CAAGqU,CAAC,CAACrU,EAAI,AAAD,CAG7BmX,CAAAA,CAAa,CAACrH,EAAKmI,QAAQ,CAAC,CAAGK,EAGT,SAAlBxI,EAAKmI,QAAQ,EACb,OAAOd,EAAcjO,IAAI,CAAC/D,IAAI,CAElC8R,EAASsB,WAAW,CAACV,EACzB,CAEA,IAAK,IAAMW,KAAKb,EAGZ,CAAA,AAAC7W,IAA+E2X,SAAS,EACrF,AAAC3X,IAA+E4X,IAAI,EACpF,AAAC5X,IAA+E6X,QAAQ,EAExFzY,OAAOO,cAAc,CAACC,IAAI,CAACiX,EAAQa,EAAC,GACpCI,AA5FZ,SAAsBtO,CAAG,CAAE/J,CAAI,EAG3B,GADAuX,EAAaC,EAAc,CAAA,EACvBb,EAAU1D,MAAM,CAAE,CAIlB,IADAzD,EAAImH,EAAU1D,MAAM,CACbzD,KAAO,CAACgI,GACXA,EAAcb,CAAS,CAACnH,EAAE,CAACkF,IAAI,CAAC1U,GAEpCuX,EAAa,CAACC,CAClB,CAMA,IAJa,cAATxX,GAAwB+J,AAAQ,SAARA,GACxBwN,CAAAA,EAAa,CAAA,CAAG,EAEpB/H,EAAI8I,AAlDKnL,EAkDI8F,MAAM,CACZzD,KAAO,CAAC+H,GAAY,CACvB,GAAIvX,EAAKiT,MAAM,CAAG,IACd,MAAM,AAAIxI,MAAM,kBAEpB8M,EAAce,AAvDTnL,CAuDiB,CAACqC,EAAE,CAACkF,IAAI,CAAC1U,IAC3B,AAAe,YAAf,OAAO+J,CACf,CACI,CAACwN,GAIIF,CAAAA,CAAY,CAACrX,EAAK,GAAK+J,GACxBwF,AAAkB,QAAlBA,EAAKmI,QAAQ,AAAS,GACtBd,CAAa,CAACrH,EAAKmI,QAAQ,CAAC,CAAC1X,EAAK,GAAK+J,IAEnC,AAACqD,GACDA,AAAqC,KAArCA,EAAmBnH,OAAO,CAACjG,GAO3ByX,CAAc,CAACzX,EAAK,CAAG+J,EANnBA,GACAwF,EAAKgJ,YAAY,CAvFlCvY,AAuF6CA,EAvFxCuT,OAAO,CAAC,SAAU,SAAUiF,CAAK,EACzC,MAAO,IAAMA,EAAMxE,WAAW,EAClC,GAqF2DjK,GASvD,EAgDyBqN,CAAM,CAACa,EAAE,CAAEA,GAShC,GALA3L,EAAIiD,EAAMkI,GAEY,QAAlBlI,EAAKmI,QAAQ,EACbnI,EAAKgJ,YAAY,CAAC,eAAgB,OAElChJ,AAAkB,SAAlBA,EAAKmI,QAAQ,CACb,OAGJ,EAAE,CAACpW,OAAO,CAACnB,IAAI,CAACoP,EAAKkJ,QAAQ,EAAIlJ,EAAKJ,UAAU,CAAEgI,EACtD,CACJ,EAUQ,IAAI,CAACtQ,SAAS,CAAC6R,aAAa,CAAC,QAJjChC,EAASkB,UAAU,CAACI,WAAW,CAACtB,GAEhCG,EAAOe,UAAU,CAACI,WAAW,CAACnB,EAItC,CAIA,SAASjD,IACL,IAAM+E,EAAc,IAAI,CAAC9R,SAAS,CAAC+R,gBAAgB,CAAC,KAAMC,EAAkB,CAAC,QAAS,OAAQ,aAAc,SAAS,CACrHC,MAAMC,IAAI,CAACJ,GAAarX,OAAO,CAAC,AAACyQ,IAC7B8G,EAAgBvX,OAAO,CAAC,AAACkN,IACrB,IAAMwK,EAAYjH,EAAQkH,YAAY,CAACzK,GACnCwK,GAAWE,SAAS,SACpBnH,EAAQwG,YAAY,CAAC/J,EAAMmJ,iBAAiB5F,GAASoH,gBAAgB,CAAC3K,GAE9E,EACJ,EACJ,CAWA,SAASc,EAAe8J,CAAM,EAC1B,GAAM,CAAEtI,mBAAAA,CAAkB,CAAE,CAAG,IAAI,CACnC,AAEAA,CAAAA,EACI,CACIA,EAAmBC,QAAQ,CAC3BD,EAAmBuI,kBAAkB,CACxC,CACD,CAAC,IAAI,CAACxS,SAAS,CAAC,AAAD,EAAGvF,OAAO,CAAC,SAAUgY,CAAG,EACvCF,EAAOrC,WAAW,CAACuC,EACvB,EACJ,CAQA,SAASC,IACL,IAAM5Y,EAAQ,IAAI,CAUlBQ,EAAS,CAACnB,EAAMoB,EAASC,KACrBV,EAAM6Y,gBAAgB,CAAG,CAAA,EACzB5M,EAAM,CAAA,EAAMjM,EAAMS,OAAO,CAACpB,EAAK,CAAEoB,GAC7B0L,EAAKzL,EAAQ,CAAA,IACbV,EAAMU,MAAM,EAEpB,CACAV,CAAAA,EAAMe,SAAS,CAAG,CACdP,OAAQ,SAAUC,CAAO,CAAEC,CAAM,EAC7BF,EAAO,YAAaC,EAASC,EACjC,CACJ,EAIAE,EACKb,OAAO,CAACC,GAAOC,UAAU,CACzBI,SAAS,CAAC,CAACI,EAASC,KACrBF,EAAO,aAAcC,EAASC,EAClC,EACJ,CAMA,SAASoY,EAAmB,CAAEC,QAAAA,CAAO,CAAEja,IAAAA,CAAG,CAAEka,aAAAA,CAAY,CAAE,EACtD,IAAMnR,EAAmB,IAAI,CAACpH,OAAO,CAACM,SAAS,CAAE,CAAEwC,MAAAA,CAAK,CAAEC,cAAAA,EAAgB,CAAC,CAAEG,cAAAA,CAAa,CAAEC,MAAAA,EAAQ,CAAC,CAAE,CAAGqI,EAAM,IAAI,CAACxL,OAAO,CAACR,UAAU,EAAEkD,cAAe0E,GAAkBlG,SAASC,eAAgBqX,EAAQF,EAAQnV,KAAK,CAAGoV,EAAcE,EAActV,EAAQJ,EAC1PqE,CAAAA,GAAkBqF,SAAW,CAAA,CAAG,GACjCpO,AAAQ,UAARA,GACAyE,AAAU,UAAVA,GACAI,AAAkB,QAAlBA,GACIsV,EAAQ,EAAIC,IACRD,EAAQC,EACRH,EAAQnV,KAAK,EAAIsV,EAEZ,IAAI,CAACpL,KAAK,EAAEqL,aAAe,QAChCJ,CAAAA,EAAQhU,CAAC,EAAImU,EAAcD,EAAQ,CAAA,EAInD,CAkBA,SAASxW,IACL,IAAMzC,EAAQ,IAAI,EACdA,EAAMiP,UAAU,GAGpB1C,EAAgBvM,EACX,AAACJ,IAA+E6X,QAAQ,EACzFzX,EAAMmP,WAAW,GAIrB6B,WAAW,KACPvF,EAAc2N,KAAK,GACnB3N,EAAchJ,KAAK,GAEd,AAAC7C,IAA+E6X,QAAQ,EACzFzG,WAAW,KACPhR,EAAMsO,UAAU,EACpB,EAAG,IAEX,EAAG,GACP,CAOA,SAASkB,IACL,IAAMxP,EAAQ,IAAI,CAAE6H,EAAmB7H,EAAMS,OAAO,CAACM,SAAS,CAAEY,EAAUkG,EAAiBlG,OAAO,CAAE0X,EAAUrZ,EAAM6Y,gBAAgB,EAAI,CAAC7Y,EAAMiN,iBAAiB,AAChKjN,CAAAA,EAAMqO,YAAY,CAAG,EACjBrO,EAAM6Y,gBAAgB,EACtB7Y,EAAMyP,aAAa,GAEnB4J,GAAWxR,AAA6B,CAAA,IAA7BA,EAAiBqF,OAAO,GACnClN,EAAM+Q,YAAY,CAAG,EAAE,CACvB/Q,EAAMoO,cAAc,CAAGpO,EAAMoO,cAAc,EACvCpO,EAAM8M,QAAQ,CAACwM,CAAC,CAAC,mBAAmBzL,IAAI,CAAC,CACrCK,OAAQ,CACZ,GAAGC,GAAG,GACVjC,EAAqBvK,EAAS,SAAU2L,CAAM,EAC1CtN,EAAM4M,SAAS,CAACU,EACpB,GACAtN,EAAM6Y,gBAAgB,CAAG,CAAA,EAEjC,CAgBA,SAAShD,EAAYpD,CAAG,CAAEhS,CAAO,EAC7B,IAAM8Y,EAAQ9G,EAAInN,OAAO,CAAC,UAAY,EAAGkU,EAAmB/G,EAAInN,OAAO,CAAC,kBAAoB,GACxFmU,EAAOhH,EAAIa,MAAM,CAACiG,GA+BtB,OA7BA9G,EAAMA,EAAIa,MAAM,CAAC,EAAGiG,GAChBC,EAEA/G,EAAMA,EAAIG,OAAO,CAAC,2BAA4B,SAGzC6G,GAAQhZ,GAASM,WAAW2Y,YACjCD,EAAO,qCACShZ,EAAQT,KAAK,CAAC4D,KAAK,CAD5B,aAEUnD,EAAQT,KAAK,CAACyD,MAAM,CAF9B,gDAKHgW,EAAK7G,OAAO,CAAC,2BAA4B,SALtC,0BAQPH,EAAMA,EAAIG,OAAO,CAAC,SAAU6G,EAAO,WAEvChH,EAAMA,EACDG,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,sBAAuB,IAC/BA,OAAO,CAAC,qBAAsB,IAC9BA,OAAO,CAAC,uCAAwC,WAChDA,OAAO,CAAC,eAAgB,SACxBA,OAAO,CAAC,QAAS,oDACjBA,OAAO,CAAC,oBAAqB,gBAC7BA,OAAO,CAAC,OAAQ,KAEhBA,OAAO,CAAC,UAAW,QACnBA,OAAO,CAAC,SAAU,OAE3B,CAp1BA3U,EAAU8B,OAAO,CA7CjB,SAAiBiG,CAAU,CAAEX,CAAgB,EACzCI,EAA2B1F,OAAO,CAACsF,GACnCsU,AA9jBmD5T,EA8jB9BhG,OAAO,CAACiG,GAC7B,IAAM4T,EAAa5T,EAAW1G,SAAS,AAClCsa,CAAAA,EAAWhX,WAAW,GACvBgX,EAAWtL,UAAU,CAAGA,EACxBsL,EAAWhX,WAAW,CAAGA,EACzBgX,EAAW5G,YAAY,CAAGA,EAC1B4G,EAAWnX,KAAK,CAAGA,EACnBmX,EAAW/D,WAAW,CAAGA,EACzB+D,EAAW9G,YAAY,CAAGA,EAC1B8G,EAAWrG,MAAM,CAAGA,EACpBqG,EAAWlH,eAAe,CAAGA,EAC7BkH,EAAW/G,WAAW,CAAGA,EACzB+G,EAAWjL,cAAc,CAAGA,EAC5BiL,EAAWzK,WAAW,CAAGA,EACzByK,EAAWvM,WAAW,CAAGA,EACzBuM,EAAWhN,SAAS,CAAGA,EACvBgN,EAAWnK,aAAa,CAAGA,EAC3BmK,EAAWpK,eAAe,CAAGA,EAC7BoK,EAAW3G,mBAAmB,CAAGA,EACjC2G,EAAWC,SAAS,CAACtZ,IAAI,CAAC+O,GAC1B5D,EAAmB1F,EAAY,OAAQ4S,GACvClN,EAAmB1F,EAAY,cAAe8S,GAC1C,AAAClZ,IAA+E6X,QAAQ,EACxFhM,EAAcqO,UAAU,CAAC,SAASC,WAAW,CAAC,SAAUC,CAAQ,EACvDzN,IAGDyN,EAASC,OAAO,CAChB1N,EAAc4C,WAAW,GAGzB5C,EAAc+B,UAAU,GAEhC,GAEJhD,EAAevK,SAAS,CAAGkL,EAAMnL,EAA4BC,SAAS,CAAEuK,EAAevK,SAAS,EAChGuK,EAAetI,IAAI,CAAGiJ,EAAMnL,EAA4BkC,IAAI,CAAEsI,EAAetI,IAAI,EAIjFsI,EAAerL,UAAU,CAAGgM,EAAMnL,EAA4Bb,UAAU,CAAEqL,EAAerL,UAAU,EAE3G,CAs1BJ,EAAGhC,GAAcA,CAAAA,EAAY,CAAC,CAAA,GAMD,IAAMic,EAAuBjc,EAgHpDkc,EAAKva,GACXua,CAAAA,EAAE/R,aAAa,CAAG+R,EAAE/R,aAAa,EA70CwBA,EA80CzD+R,EAAE9R,IAAI,CAAG8R,EAAE/R,aAAa,CAACC,IAAI,CAC7B8R,EAAEhQ,OAAO,CAAGgQ,EAAE/R,aAAa,CAAC+B,OAAO,CACnCgQ,EAAE/P,IAAI,CAAG+P,EAAE/R,aAAa,CAACgC,IAAI,CAC7B8P,EAAoBna,OAAO,CAACoa,EAAEC,KAAK,CAAED,EAAEE,QAAQ,EAClB,IAAM3a,EAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}