{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/mouse-wheel-zoom\n * @requires highcharts\n *\n * Mousewheel zoom module\n *\n * (c) 2023 <PERSON><PERSON> <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/mouse-wheel-zoom\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/mouse-wheel-zoom\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ mouse_wheel_zoom_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/Annotations/NavigationBindingsUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined, isNumber, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define types for editable fields per annotation. There is no need to define\n * numbers, because they won't change their type to string.\n * @private\n */\nconst annotationsFieldsTypes = {\n    backgroundColor: 'string',\n    borderColor: 'string',\n    borderRadius: 'string',\n    color: 'string',\n    fill: 'string',\n    fontSize: 'string',\n    labels: 'string',\n    name: 'string',\n    stroke: 'string',\n    title: 'string'\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns the first xAxis or yAxis that was clicked with its value.\n *\n * @private\n *\n * @param {Array<Highcharts.PointerAxisCoordinateObject>} coords\n *        All the chart's x or y axes with a current pointer's axis value.\n *\n * @return {Highcharts.PointerAxisCoordinateObject}\n *         Object with a first found axis and its value that pointer\n *         is currently pointing.\n */\nfunction getAssignedAxis(coords) {\n    return coords.filter((coord) => {\n        const extremes = coord.axis.getExtremes(), axisMin = extremes.min, axisMax = extremes.max, \n        // Correct axis edges when axis has series\n        // with pointRange (like column)\n        minPointOffset = pick(coord.axis.minPointOffset, 0);\n        return isNumber(axisMin) && isNumber(axisMax) &&\n            coord.value >= (axisMin - minPointOffset) &&\n            coord.value <= (axisMax + minPointOffset) &&\n            // Don't count navigator axis\n            !coord.axis.options.isInternal;\n    })[0]; // If the axes overlap, return the first axis that was found.\n}\n/**\n * Get field type according to value\n *\n * @private\n *\n * @param {'boolean'|'number'|'string'} value\n * Atomic type (one of: string, number, boolean)\n *\n * @return {'checkbox'|'number'|'text'}\n * Field type (one of: text, number, checkbox)\n */\nfunction getFieldType(key, value) {\n    const predefinedType = annotationsFieldsTypes[key];\n    let fieldType = typeof value;\n    if (defined(predefinedType)) {\n        fieldType = predefinedType;\n    }\n    return {\n        'string': 'text',\n        'number': 'number',\n        'boolean': 'checkbox'\n    }[fieldType];\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst NavigationBindingUtilities = {\n    annotationsFieldsTypes,\n    getAssignedAxis,\n    getFieldType\n};\n/* harmony default export */ const NavigationBindingsUtilities = (NavigationBindingUtilities);\n\n;// ./code/es-modules/Extensions/MouseWheelZoom/MouseWheelZoom.js\n/* *\n *\n *  (c) 2023 Torstein Honsi, Askel Eirik Johansson\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent, isObject, pick: MouseWheelZoom_pick, defined: MouseWheelZoom_defined, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { getAssignedAxis: MouseWheelZoom_getAssignedAxis } = NavigationBindingsUtilities;\n/* *\n *\n *  Constants\n *\n * */\nconst composedClasses = [], defaultOptions = {\n    enabled: true,\n    sensitivity: 1.1\n};\nlet wheelTimer;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nconst optionsToObject = (options) => {\n    if (!isObject(options)) {\n        options = {\n            enabled: options ?? true\n        };\n    }\n    return merge(defaultOptions, options);\n};\n/**\n * @private\n */\nconst zoomBy = function (chart, howMuch, xAxis, yAxis, mouseX, mouseY, options) {\n    const type = MouseWheelZoom_pick(options.type, chart.zooming.type, '');\n    let axes = [];\n    if (type === 'x') {\n        axes = xAxis;\n    }\n    else if (type === 'y') {\n        axes = yAxis;\n    }\n    else if (type === 'xy') {\n        axes = chart.axes;\n    }\n    const hasZoomed = chart.transform({\n        axes,\n        // Create imaginary reference and target rectangles around the mouse\n        // point that scales up or down with `howMuch`;\n        to: {\n            x: mouseX - 5,\n            y: mouseY - 5,\n            // Must use 10 to get passed the limit for too small reference.\n            // Below this, the transform will default to a pan.\n            width: 10,\n            height: 10\n        },\n        from: {\n            x: mouseX - 5 * howMuch,\n            y: mouseY - 5 * howMuch,\n            width: 10 * howMuch,\n            height: 10 * howMuch\n        },\n        trigger: 'mousewheel'\n    });\n    if (hasZoomed) {\n        if (MouseWheelZoom_defined(wheelTimer)) {\n            clearTimeout(wheelTimer);\n        }\n        // Some time after the last mousewheel event, run drop. In case any of\n        // the affected axes had `startOnTick` or `endOnTick`, they will be\n        // re-adjusted now.\n        wheelTimer = setTimeout(() => {\n            chart.pointer?.drop();\n        }, 400);\n    }\n    return hasZoomed;\n};\n/**\n * @private\n */\nfunction onAfterGetContainer() {\n    const wheelZoomOptions = optionsToObject(this.zooming.mouseWheel);\n    if (wheelZoomOptions.enabled) {\n        addEvent(this.container, 'wheel', (e) => {\n            e = this.pointer?.normalize(e) || e;\n            const { pointer } = this, allowZoom = pointer && !pointer.inClass(e.target, 'highcharts-no-mousewheel');\n            // Firefox uses e.detail, WebKit and IE uses deltaX, deltaY, deltaZ.\n            if (this.isInsidePlot(e.chartX - this.plotLeft, e.chartY - this.plotTop) && allowZoom) {\n                const wheelSensitivity = wheelZoomOptions.sensitivity || 1.1, delta = e.detail || ((e.deltaY || 0) / 120), xAxisCoords = MouseWheelZoom_getAssignedAxis(pointer.getCoordinates(e).xAxis), yAxisCoords = MouseWheelZoom_getAssignedAxis(pointer.getCoordinates(e).yAxis);\n                const hasZoomed = zoomBy(this, Math.pow(wheelSensitivity, delta), xAxisCoords ? [xAxisCoords.axis] : this.xAxis, yAxisCoords ? [yAxisCoords.axis] : this.yAxis, e.chartX, e.chartY, wheelZoomOptions);\n                // Prevent page scroll\n                if (hasZoomed) {\n                    e.preventDefault?.();\n                }\n            }\n        });\n    }\n}\n/**\n * @private\n */\nfunction compose(ChartClass) {\n    if (composedClasses.indexOf(ChartClass) === -1) {\n        composedClasses.push(ChartClass);\n        addEvent(ChartClass, 'afterGetContainer', onAfterGetContainer);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst MouseWheelZoomComposition = {\n    compose\n};\n/* harmony default export */ const MouseWheelZoom = (MouseWheelZoomComposition);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The mouse wheel zoom is a feature included in Highcharts Stock, but is also\n * available for Highcharts Core as a module. Zooming with the mouse wheel is\n * enabled by default in Highcharts Stock. In Highcharts Core it is enabled if\n * [chart.zooming.type](chart.zooming.type) is set. It can be disabled by\n * setting this option to `false`.\n *\n * @type      {boolean|object}\n * @since 11.1.0\n * @requires  modules/mouse-wheel-zoom\n * @sample    {highcharts} highcharts/mouse-wheel-zoom/enabled\n *            Enable or disable\n * @sample    {highstock} stock/mouse-wheel-zoom/enabled\n *            Enable or disable\n * @apioption chart.zooming.mouseWheel\n */\n/**\n * Zooming with the mouse wheel can be disabled by setting this option to\n * `false`.\n *\n * @type      {boolean}\n * @default   true\n * @since 11.1.0\n * @requires  modules/mouse-wheel-zoom\n * @apioption chart.zooming.mouseWheel.enabled\n */\n/**\n * Adjust the sensitivity of the zoom. Sensitivity of mouse wheel or trackpad\n * scrolling. `1` is no sensitivity, while with `2`, one mouse wheel delta will\n * zoom in `50%`.\n *\n * @type      {number}\n * @default   1.1\n * @since 11.1.0\n * @requires  modules/mouse-wheel-zoom\n * @sample    {highcharts} highcharts/mouse-wheel-zoom/sensitivity\n *            Change mouse wheel zoom sensitivity\n * @sample    {highstock} stock/mouse-wheel-zoom/sensitivity\n *            Change mouse wheel zoom sensitivity\n * @apioption chart.zooming.mouseWheel.sensitivity\n */\n/**\n * Decides in what dimensions the user can zoom scrolling the wheel. Can be one\n * of `x`, `y` or `xy`. In Highcharts Core, if not specified here, it will\n * inherit the type from [chart.zooming.type](chart.zooming.type). In Highcharts\n * Stock, it defaults to `x`.\n *\n * Note that particularly with mouse wheel in the y direction, the zoom is\n * affected by the default [yAxis.startOnTick](#yAxis.startOnTick) and\n * [endOnTick]((#yAxis.endOnTick)) settings. In order to respect these settings,\n * the zoom level will adjust after the user has stopped zooming. To prevent\n * this, consider setting `startOnTick` and `endOnTick` to `false`.\n *\n * @type      {string}\n * @default   {highcharts} undefined\n * @default   {highstock} x\n * @validvalue [\"x\", \"y\", \"xy\"]\n * @since 11.1.0\n * @requires  modules/mouse-wheel-zoom\n * @apioption chart.zooming.mouseWheel.type\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/mouse-wheel-zoom.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.MouseWheelZoom = G.MouseWheelZoom || MouseWheelZoom;\nG.MouseWheelZoom.compose(G.Chart);\n/* harmony default export */ const mouse_wheel_zoom_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "wheelTimer", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "mouse_wheel_zoom_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "defined", "isNumber", "pick", "annotationsFieldsTypes", "backgroundColor", "borderColor", "borderRadius", "color", "fill", "fontSize", "labels", "name", "stroke", "title", "addEvent", "isObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_pick", "MouseWheelZoom_defined", "merge", "getAssignedAxis", "MouseWheelZoom_getAssignedAxis", "coords", "filter", "coord", "extremes", "axis", "getExtremes", "axisMin", "min", "axisMax", "max", "minPointOffset", "value", "options", "isInternal", "getFieldType", "predefinedType", "fieldType", "composedClasses", "defaultOptions", "enabled", "sensitivity", "optionsToObject", "zoomBy", "chart", "<PERSON><PERSON><PERSON>", "xAxis", "yAxis", "mouseX", "mouseY", "type", "zooming", "axes", "has<PERSON><PERSON>ed", "transform", "to", "x", "y", "width", "height", "from", "trigger", "clearTimeout", "setTimeout", "pointer", "drop", "onAfterGetContainer", "wheelZoomOptions", "mouseWheel", "container", "e", "normalize", "allowZoom", "inClass", "target", "isInsidePlot", "chartX", "plotLeft", "chartY", "plotTop", "wheelSensitivity", "delta", "detail", "deltaY", "xAxisCoords", "getCoordinates", "yAxisCoords", "Math", "pow", "preventDefault", "G", "MouseWheelZoom", "compose", "ChartClass", "indexOf", "push", "Chart"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,sCAAuC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACxG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,sCAAsC,CAAGD,EAAQD,EAAK,WAAc,EAE5EA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,iBAyMNC,EAxMM,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAarH,GAAM,CAAEE,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAIH,IAW/BI,EAAyB,CAC3BC,gBAAiB,SACjBC,YAAa,SACbC,aAAc,SACdC,MAAO,SACPC,KAAM,SACNC,SAAU,SACVC,OAAQ,SACRC,KAAM,SACNC,OAAQ,SACRC,MAAO,QACX,EA8EM,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEb,KAAMc,CAAmB,CAAEhB,QAASiB,CAAsB,CAAEC,MAAAA,CAAK,CAAE,CAAInB,IAE7F,CAAEoB,gBAAiBC,CAA8B,CAAE,CArBtB,CAC/BjB,uBAAAA,EACAgB,gBA3CJ,SAAyBE,CAAM,EAC3B,OAAOA,EAAOC,MAAM,CAAC,AAACC,IAClB,IAAMC,EAAWD,EAAME,IAAI,CAACC,WAAW,GAAIC,EAAUH,EAASI,GAAG,CAAEC,EAAUL,EAASM,GAAG,CAGzFC,EAAiB7B,EAAKqB,EAAME,IAAI,CAACM,cAAc,CAAE,GACjD,OAAO9B,EAAS0B,IAAY1B,EAAS4B,IACjCN,EAAMS,KAAK,EAAKL,EAAUI,GAC1BR,EAAMS,KAAK,EAAKH,EAAUE,GAE1B,CAACR,EAAME,IAAI,CAACQ,OAAO,CAACC,UAAU,AACtC,EAAE,CAAC,EAAE,AACT,EAgCIC,aApBJ,SAAsBlD,CAAG,CAAE+C,CAAK,EAC5B,IAAMI,EAAiBjC,CAAsB,CAAClB,EAAI,CAC9CoD,EAAY,OAAOL,EAIvB,OAHIhC,EAAQoC,IACRC,CAAAA,EAAYD,CAAa,EAEtB,CAAA,CACH,OAAU,OACV,OAAU,SACV,QAAW,UACf,CAAA,CAAC,CAACC,EAAU,AAChB,CAUA,EAuBMC,EAAkB,EAAE,CAAEC,EAAiB,CACzCC,QAAS,CAAA,EACTC,YAAa,GACjB,EAUMC,EAAkB,AAACT,IAChBlB,EAASkB,IACVA,CAAAA,EAAU,CACNO,QAASP,GAAW,CAAA,CACxB,CAAA,EAEGf,EAAMqB,EAAgBN,IAK3BU,EAAS,SAAUC,CAAK,CAAEC,CAAO,CAAEC,CAAK,CAAEC,CAAK,CAAEC,CAAM,CAAEC,CAAM,CAAEhB,CAAO,EAC1E,IAAMiB,EAAOlC,EAAoBiB,EAAQiB,IAAI,CAAEN,EAAMO,OAAO,CAACD,IAAI,CAAE,IAC/DE,EAAO,EAAE,AACTF,AAAS,CAAA,MAATA,EACAE,EAAON,EAEFI,AAAS,MAATA,EACLE,EAAOL,EAEO,OAATG,GACLE,CAAAA,EAAOR,EAAMQ,IAAI,AAAD,EAEpB,IAAMC,EAAYT,EAAMU,SAAS,CAAC,CAC9BF,KAAAA,EAGAG,GAAI,CACAC,EAAGR,EAAS,EACZS,EAAGR,EAAS,EAGZS,MAAO,GACPC,OAAQ,EACZ,EACAC,KAAM,CACFJ,EAAGR,EAAS,EAAIH,EAChBY,EAAGR,EAAS,EAAIJ,EAChBa,MAAO,GAAKb,EACZc,OAAQ,GAAKd,CACjB,EACAgB,QAAS,YACb,GAYA,OAXIR,IACIpC,EAAuB7C,IACvB0F,aAAa1F,GAKjBA,EAAa2F,WAAW,KACpBnB,EAAMoB,OAAO,EAAEC,MACnB,EAAG,MAEAZ,CACX,EAIA,SAASa,IACL,IAAMC,EAAmBzB,EAAgB,IAAI,CAACS,OAAO,CAACiB,UAAU,CAC5DD,CAAAA,EAAiB3B,OAAO,EACxB1B,EAAS,IAAI,CAACuD,SAAS,CAAE,QAAS,AAACC,IAC/BA,EAAI,IAAI,CAACN,OAAO,EAAEO,UAAUD,IAAMA,EAClC,GAAM,CAAEN,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAEQ,EAAYR,GAAW,CAACA,EAAQS,OAAO,CAACH,EAAEI,MAAM,CAAE,4BAE5E,GAAI,IAAI,CAACC,YAAY,CAACL,EAAEM,MAAM,CAAG,IAAI,CAACC,QAAQ,CAAEP,EAAEQ,MAAM,CAAG,IAAI,CAACC,OAAO,GAAKP,EAAW,CACnF,IAAMQ,EAAmBb,EAAiB1B,WAAW,EAAI,IAAKwC,EAAQX,EAAEY,MAAM,EAAK,AAACZ,CAAAA,EAAEa,MAAM,EAAI,CAAA,EAAK,IAAMC,EAAchE,EAA+B4C,EAAQqB,cAAc,CAACf,GAAGxB,KAAK,EAAGwC,EAAclE,EAA+B4C,EAAQqB,cAAc,CAACf,GAAGvB,KAAK,EACpPJ,EAAO,IAAI,CAAE4C,KAAKC,GAAG,CAACR,EAAkBC,GAAQG,EAAc,CAACA,EAAY3D,IAAI,CAAC,CAAG,IAAI,CAACqB,KAAK,CAAEwC,EAAc,CAACA,EAAY7D,IAAI,CAAC,CAAG,IAAI,CAACsB,KAAK,CAAEuB,EAAEM,MAAM,CAAEN,EAAEQ,MAAM,CAAEX,IAGhLG,EAAEmB,cAAc,IAExB,CACJ,EAER,CA4FA,IAAMC,EAAK3F,GACX2F,CAAAA,EAAEC,cAAc,CAAGD,EAAEC,cAAc,EA9ED,CAC9BC,QAZJ,SAAiBC,CAAU,EACqB,KAAxCvD,EAAgBwD,OAAO,CAACD,KACxBvD,EAAgByD,IAAI,CAACF,GACrB/E,EAAS+E,EAAY,oBAAqB3B,GAElD,CAQA,EA6EAwB,EAAEC,cAAc,CAACC,OAAO,CAACF,EAAEM,KAAK,EACH,IAAMnG,EAAyBE,IAGlD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}