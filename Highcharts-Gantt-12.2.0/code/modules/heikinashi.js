!function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/heikinashi",["highcharts/highcharts"],function(t){return i(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/heikinashi"]=i(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=i(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,i)=>(()=>{"use strict";var e={512:t=>{t.exports=i},944:i=>{i.exports=t}},s={};function a(t){var i=s[t];if(void 0!==i)return i.exports;var h=s[t]={exports:{}};return e[t](h,h.exports,a),h.exports}a.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return a.d(i,{a:i}),i},a.d=(t,i)=>{for(var e in i)a.o(i,e)&&!a.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},a.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i);var h={};a.d(h,{default:()=>w});var o=a(944),r=a.n(o),n=a(512),p=a.n(n);let{candlestick:{prototype:{pointClass:l}},hlc:{prototype:{pointClass:c}}}=p().seriesTypes,{composed:u}=r(),{candlestick:d}=p().seriesTypes,{addEvent:f,merge:g,pushUnique:y}=r();function k(){this.series.forEach(t=>{t.is("heikinashi")&&(t.heikiashiData.length=0,t.getHeikinashiData())})}function D(){let t=this.points,i=this.heikiashiData,e=this.cropStart||0;for(let s=0;s<t.length;s++){let a=t[s],h=i[s+e];a.open=h[0],a.high=h[1],a.low=h[2],a.close=h[3]}}function m(){this.heikiashiData.length&&(this.heikiashiData.length=0)}class x extends d{constructor(){super(...arguments),this.heikiashiData=[]}static compose(t,i){d.compose(t),y(u,"HeikinAshi")&&(f(i,"postProcessData",k),f(x,"afterTranslate",D),f(x,"updatedData",m))}getHeikinashiData(){let t=this.allGroupedTable||this.dataTable,i=t.rowCount,e=this.heikiashiData;if(!e.length&&i){this.modifyFirstPointValue(t.getRow(0,this.pointArrayMap));for(let s=1;s<i;s++)this.modifyDataPoint(t.getRow(s,this.pointArrayMap),e[s-1])}this.heikiashiData=e}init(){super.init.apply(this,arguments),this.heikiashiData=[]}modifyFirstPointValue(t){let i=(t[0]+t[1]+t[2]+t[3])/4,e=(t[0]+t[3])/2;this.heikiashiData.push([i,t[1],t[2],e])}modifyDataPoint(t,i){let e=(i[0]+i[3])/2,s=(t[0]+t[1]+t[2]+t[3])/4,a=Math.max(t[1],s,e),h=Math.min(t[2],s,e);this.heikiashiData.push([e,a,h,s])}}x.defaultOptions=g(d.defaultOptions,{dataGrouping:{groupAll:!0}}),x.prototype.pointClass=class extends l{},p().registerSeriesType("heikinashi",x);let H=r();x.compose(H.Series,H.Axis);let w=r();return h.default})());