{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/venn\n * @requires highcharts\n *\n * (c) 2017-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/venn\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Color\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/venn\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ venn_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Core/Geometry/GeometryUtilities.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Namespace\n *\n * */\nvar GeometryUtilities;\n(function (GeometryUtilities) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculates the center between a list of points.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.PositionObject>} points\n     * A list of points to calculate the center of.\n     *\n     * @return {Highcharts.PositionObject}\n     * Calculated center\n     */\n    function getCenterOfPoints(points) {\n        const sum = points.reduce((sum, point) => {\n            sum.x += point.x;\n            sum.y += point.y;\n            return sum;\n        }, { x: 0, y: 0 });\n        return {\n            x: sum.x / points.length,\n            y: sum.y / points.length\n        };\n    }\n    GeometryUtilities.getCenterOfPoints = getCenterOfPoints;\n    /**\n     * Calculates the distance between two points based on their x and y\n     * coordinates.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} p1\n     * The x and y coordinates of the first point.\n     *\n     * @param {Highcharts.PositionObject} p2\n     * The x and y coordinates of the second point.\n     *\n     * @return {number}\n     * Returns the distance between the points.\n     */\n    function getDistanceBetweenPoints(p1, p2) {\n        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n    }\n    GeometryUtilities.getDistanceBetweenPoints = getDistanceBetweenPoints;\n    /**\n     * Calculates the angle between two points.\n     * @todo add unit tests.\n     * @private\n     * @param {Highcharts.PositionObject} p1 The first point.\n     * @param {Highcharts.PositionObject} p2 The second point.\n     * @return {number} Returns the angle in radians.\n     */\n    function getAngleBetweenPoints(p1, p2) {\n        return Math.atan2(p2.x - p1.x, p2.y - p1.y);\n    }\n    GeometryUtilities.getAngleBetweenPoints = getAngleBetweenPoints;\n    /**\n     * Test for point in polygon. Polygon defined as array of [x,y] points.\n     * @private\n     * @param {PositionObject} point The point potentially within a polygon.\n     * @param {Array<Array<number>>} polygon The polygon potentially containing the point.\n     */\n    function pointInPolygon({ x, y }, polygon) {\n        const len = polygon.length;\n        let i, j, inside = false;\n        for (i = 0, j = len - 1; i < len; j = i++) {\n            const [x1, y1] = polygon[i], [x2, y2] = polygon[j];\n            if (y1 > y !== y2 > y &&\n                (x < (x2 - x1) *\n                    (y - y1) /\n                    (y2 - y1) +\n                    x1)) {\n                inside = !inside;\n            }\n        }\n        return inside;\n    }\n    GeometryUtilities.pointInPolygon = pointInPolygon;\n})(GeometryUtilities || (GeometryUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Geometry_GeometryUtilities = (GeometryUtilities);\n\n;// ./code/es-modules/Core/Geometry/CircleUtilities.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getAngleBetweenPoints, getCenterOfPoints, getDistanceBetweenPoints } = Geometry_GeometryUtilities;\n/* *\n *\n *  Namespace\n *\n * */\nvar CircleUtilities;\n(function (CircleUtilities) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     *\n     * @param {number} x\n     * Number to round\n     *\n     * @param {number} decimals\n     * Number of decimals to round to\n     *\n     * @return {number}\n     * Rounded number\n     */\n    function round(x, decimals) {\n        const a = Math.pow(10, decimals);\n        return Math.round(x * a) / a;\n    }\n    CircleUtilities.round = round;\n    /**\n     * Calculates the area of a circle based on its radius.\n     *\n     * @private\n     *\n     * @param {number} r\n     * The radius of the circle.\n     *\n     * @return {number}\n     * Returns the area of the circle.\n     */\n    function getAreaOfCircle(r) {\n        if (r <= 0) {\n            throw new Error('radius of circle must be a positive number.');\n        }\n        return Math.PI * r * r;\n    }\n    CircleUtilities.getAreaOfCircle = getAreaOfCircle;\n    /**\n     * Calculates the area of a circular segment based on the radius of the\n     * circle and the height of the segment.\n     *\n     * @see http://mathworld.wolfram.com/CircularSegment.html\n     *\n     * @private\n     *\n     * @param {number} r\n     * The radius of the circle.\n     *\n     * @param {number} h\n     * The height of the circular segment.\n     *\n     * @return {number}\n     * Returns the area of the circular segment.\n     */\n    function getCircularSegmentArea(r, h) {\n        return (r * r * Math.acos(1 - h / r) -\n            (r - h) * Math.sqrt(h * (2 * r - h)));\n    }\n    CircleUtilities.getCircularSegmentArea = getCircularSegmentArea;\n    /**\n     * Calculates the area of overlap between two circles based on their\n     * radiuses and the distance between them.\n     *\n     * @see http://mathworld.wolfram.com/Circle-CircleIntersection.html\n     *\n     * @private\n     *\n     * @param {number} r1\n     * Radius of the first circle.\n     *\n     * @param {number} r2\n     * Radius of the second circle.\n     *\n     * @param {number} d\n     * The distance between the two circles.\n     *\n     * @return {number}\n     * Returns the area of overlap between the two circles.\n     */\n    function getOverlapBetweenCircles(r1, r2, d) {\n        let overlap = 0;\n        // If the distance is larger than the sum of the radiuses then the\n        // circles does not overlap.\n        if (d < r1 + r2) {\n            if (d <= Math.abs(r2 - r1)) {\n                // If the circles are completely overlapping, then the overlap\n                // equals the area of the smallest circle.\n                overlap = getAreaOfCircle(r1 < r2 ? r1 : r2);\n            }\n            else {\n                // Height of first triangle segment.\n                const d1 = (r1 * r1 - r2 * r2 + d * d) / (2 * d), \n                // Height of second triangle segment.\n                d2 = d - d1;\n                overlap = (getCircularSegmentArea(r1, r1 - d1) +\n                    getCircularSegmentArea(r2, r2 - d2));\n            }\n            // Round the result to two decimals.\n            overlap = round(overlap, 14);\n        }\n        return overlap;\n    }\n    CircleUtilities.getOverlapBetweenCircles = getOverlapBetweenCircles;\n    /**\n     * Calculates the intersection points of two circles.\n     *\n     * NOTE: does not handle floating errors well.\n     *\n     * @private\n     *\n     * @param {Highcharts.CircleObject} c1\n     * The first circle.\n     *\n     * @param {Highcharts.CircleObject} c2\n     * The second circle.\n     *\n     * @return {Array<Highcharts.PositionObject>}\n     * Returns the resulting intersection points.\n     */\n    function getCircleCircleIntersection(c1, c2) {\n        const d = getDistanceBetweenPoints(c1, c2), r1 = c1.r, r2 = c2.r;\n        let points = [];\n        if (d < r1 + r2 && d > Math.abs(r1 - r2)) {\n            // If the circles are overlapping, but not completely overlapping,\n            // then it exists intersecting points.\n            const r1Square = r1 * r1, r2Square = r2 * r2, \n            // `d^2 - r^2 + R^2 / 2d`\n            x = (r1Square - r2Square + d * d) / (2 * d), \n            // `y^2 = R^2 - x^2`\n            y = Math.sqrt(r1Square - x * x), x1 = c1.x, x2 = c2.x, y1 = c1.y, y2 = c2.y, x0 = x1 + x * (x2 - x1) / d, y0 = y1 + x * (y2 - y1) / d, rx = -(y2 - y1) * (y / d), ry = -(x2 - x1) * (y / d);\n            points = [\n                { x: round(x0 + rx, 14), y: round(y0 - ry, 14) },\n                { x: round(x0 - rx, 14), y: round(y0 + ry, 14) }\n            ];\n        }\n        return points;\n    }\n    CircleUtilities.getCircleCircleIntersection = getCircleCircleIntersection;\n    /**\n     * Calculates all the intersection points for between a list of circles.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * The circles to calculate the points from.\n     *\n     * @return {Array<Highcharts.GeometryObject>}\n     * Returns a list of intersection points.\n     */\n    function getCirclesIntersectionPoints(circles) {\n        return circles.reduce((points, c1, i, arr) => {\n            const additional = arr\n                .slice(i + 1)\n                .reduce((points, c2, j) => {\n                const indexes = [i, j + i + 1];\n                return points.concat(getCircleCircleIntersection(c1, c2).map((p) => {\n                    p.indexes = indexes;\n                    return p;\n                }));\n            }, []);\n            return points.concat(additional);\n        }, []);\n    }\n    CircleUtilities.getCirclesIntersectionPoints = getCirclesIntersectionPoints;\n    /**\n     * Tests whether the first circle is completely overlapping the second\n     * circle.\n     *\n     * @private\n     *\n     * @param {Highcharts.CircleObject} circle1\n     * The first circle.\n     *\n     * @param {Highcharts.CircleObject} circle2\n     * The second circle.\n     *\n     * @return {boolean}\n     * Returns true if circle1 is completely overlapping circle2, false if not.\n     */\n    function isCircle1CompletelyOverlappingCircle2(circle1, circle2) {\n        return getDistanceBetweenPoints(circle1, circle2) + circle2.r < circle1.r + 1e-10;\n    }\n    CircleUtilities.isCircle1CompletelyOverlappingCircle2 = isCircle1CompletelyOverlappingCircle2;\n    /**\n     * Tests whether a point lies within a given circle.\n     * @private\n     * @param {Highcharts.PositionObject} point\n     * The point to test for.\n     *\n     * @param {Highcharts.CircleObject} circle\n     * The circle to test if the point is within.\n     *\n     * @return {boolean}\n     * Returns true if the point is inside, false if outside.\n     */\n    function isPointInsideCircle(point, circle) {\n        return getDistanceBetweenPoints(point, circle) <= circle.r + 1e-10;\n    }\n    CircleUtilities.isPointInsideCircle = isPointInsideCircle;\n    /**\n     * Tests whether a point lies within a set of circles.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} point\n     * The point to test.\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * The list of circles to test against.\n     *\n     * @return {boolean}\n     * Returns true if the point is inside all the circles, false if not.\n     */\n    function isPointInsideAllCircles(point, circles) {\n        return !circles.some(function (circle) {\n            return !isPointInsideCircle(point, circle);\n        });\n    }\n    CircleUtilities.isPointInsideAllCircles = isPointInsideAllCircles;\n    /**\n     * Tests whether a point lies outside a set of circles.\n     *\n     * TODO: add unit tests.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} point\n     * The point to test.\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * The list of circles to test against.\n     *\n     * @return {boolean}\n     * Returns true if the point is outside all the circles, false if not.\n     */\n    function isPointOutsideAllCircles(point, circles) {\n        return !circles.some(function (circle) {\n            return isPointInsideCircle(point, circle);\n        });\n    }\n    CircleUtilities.isPointOutsideAllCircles = isPointOutsideAllCircles;\n    /**\n     * Calculates the points for the polygon of the intersection area between\n     * a set of circles.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * List of circles to calculate polygon of.\n     *\n     * @return {Array<Highcharts.GeometryObject>}\n     * Return list of points in the intersection polygon.\n     */\n    function getCirclesIntersectionPolygon(circles) {\n        return getCirclesIntersectionPoints(circles)\n            .filter(function (p) {\n            return isPointInsideAllCircles(p, circles);\n        });\n    }\n    CircleUtilities.getCirclesIntersectionPolygon = getCirclesIntersectionPolygon;\n    /**\n     * Calculate the path for the area of overlap between a set of circles.\n     *\n     * @todo handle cases with only 1 or 0 arcs.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * List of circles to calculate area of.\n     *\n     * @return {Highcharts.GeometryIntersectionObject|undefined}\n     * Returns the path for the area of overlap. Returns an empty string if\n     * there are no intersection between all the circles.\n     */\n    function getAreaOfIntersectionBetweenCircles(circles) {\n        let intersectionPoints = getCirclesIntersectionPolygon(circles), result;\n        if (intersectionPoints.length > 1) {\n            // Calculate the center of the intersection points.\n            const center = getCenterOfPoints(intersectionPoints);\n            intersectionPoints = intersectionPoints\n                // Calculate the angle between the center and the points.\n                .map(function (p) {\n                p.angle = getAngleBetweenPoints(center, p);\n                return p;\n            })\n                // Sort the points by the angle to the center.\n                .sort(function (a, b) {\n                return b.angle - a.angle;\n            });\n            const startPoint = intersectionPoints[intersectionPoints.length - 1];\n            const arcs = intersectionPoints\n                .reduce(function (data, p1) {\n                const { startPoint } = data, midPoint = getCenterOfPoints([startPoint, p1]);\n                // Calculate the arc from the intersection points and their\n                // circles.\n                const arc = p1.indexes\n                    // Filter out circles that are not included in both\n                    // intersection points.\n                    .filter(function (index) {\n                    return startPoint.indexes.indexOf(index) > -1;\n                })\n                    // Iterate the circles of the intersection points and\n                    // calculate arcs.\n                    .reduce(function (arc, index) {\n                    const circle = circles[index], angle1 = getAngleBetweenPoints(circle, p1), angle2 = getAngleBetweenPoints(circle, startPoint), angleDiff = angle2 - angle1 +\n                        (angle2 < angle1 ? 2 * Math.PI : 0), angle = angle2 - angleDiff / 2;\n                    let width = getDistanceBetweenPoints(midPoint, {\n                        x: circle.x + circle.r * Math.sin(angle),\n                        y: circle.y + circle.r * Math.cos(angle)\n                    });\n                    const { r } = circle;\n                    // Width can sometimes become to large due to\n                    // floating point errors\n                    if (width > r * 2) {\n                        width = r * 2;\n                    }\n                    // Get the arc with the smallest width.\n                    if (!arc || arc.width > width) {\n                        arc = {\n                            r,\n                            largeArc: width > r ? 1 : 0,\n                            width,\n                            x: p1.x,\n                            y: p1.y\n                        };\n                    }\n                    // Return the chosen arc.\n                    return arc;\n                }, null);\n                // If we find an arc then add it to the list and update p2.\n                if (arc) {\n                    const { r } = arc;\n                    data.arcs.push(['A', r, r, 0, arc.largeArc, 1, arc.x, arc.y]);\n                    data.startPoint = p1;\n                }\n                return data;\n            }, {\n                startPoint: startPoint,\n                arcs: []\n            }).arcs;\n            if (arcs.length === 0) {\n                // Empty\n            }\n            else if (arcs.length === 1) {\n                // Empty\n            }\n            else {\n                arcs.unshift(['M', startPoint.x, startPoint.y]);\n                result = {\n                    center,\n                    d: arcs\n                };\n            }\n        }\n        return result;\n    }\n    CircleUtilities.getAreaOfIntersectionBetweenCircles = getAreaOfIntersectionBetweenCircles;\n})(CircleUtilities || (CircleUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Geometry_CircleUtilities = (CircleUtilities);\n\n;// ./code/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    const { animatableAttribs, onComplete, css, renderer } = params;\n    const animation = (point.series && point.series.chart.hasRendered) ?\n        // Chart-level animation on updates\n        void 0 :\n        // Series-level animation on new points\n        (point.series &&\n            point.series.options.animation);\n    let graphic = point.graphic;\n    params.attribs = {\n        ...params.attribs,\n        'class': point.getClassName()\n    } || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        const destroy = () => {\n            point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, () => destroy());\n        }\n        else {\n            destroy();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DrawPointUtilities = {\n    draw\n};\n/* harmony default export */ const Series_DrawPointUtilities = (DrawPointUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Venn/VennPoint.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { scatter: { prototype: { pointClass: ScatterPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass VennPoint extends ScatterPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    isValid() {\n        return isNumber(this.value);\n    }\n    shouldDraw() {\n        // Only draw points with single sets.\n        return !!this.shapeArgs;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Venn_VennPoint = (VennPoint);\n\n;// ./code/es-modules/Series/Venn/VennSeriesDefaults.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Venn diagram displays all possible logical relations between a\n * collection of different sets. The sets are represented by circles, and\n * the relation between the sets are displayed by the overlap or lack of\n * overlap between them. The venn diagram is a special case of Euler\n * diagrams, which can also be displayed by this series type.\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n * @sample {highcharts} highcharts/series-venn/point-legend/\n *         Venn diagram with a legend\n *\n * @extends      plotOptions.scatter\n * @excluding    connectEnds, connectNulls, cropThreshold, dragDrop,\n *               findNearestPointBy, getExtremesFromAll, jitter, label,\n *               linecap, lineWidth, linkedTo, marker, negativeColor,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointStart, softThreshold, stacking, steps, threshold,\n *               xAxis, yAxis, zoneAxis, zones, dataSorting, boostThreshold,\n *               boostBlending\n * @product      highcharts\n * @requires     modules/venn\n * @optionparent plotOptions.venn\n */\nconst VennSeriesDefaults = {\n    borderColor: \"#cccccc\" /* Palette.neutralColor20 */,\n    borderDashStyle: 'solid',\n    borderWidth: 1,\n    brighten: 0,\n    clip: false,\n    colorByPoint: true,\n    dataLabels: {\n        enabled: true,\n        verticalAlign: 'middle',\n        formatter: function () {\n            return this.point.name;\n        }\n    },\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    /**\n     * @ignore-option\n     * @private\n     */\n    marker: false,\n    opacity: 0.75,\n    showInLegend: false,\n    /**\n     * @ignore-option\n     *\n     * @private\n     */\n    legendType: 'point',\n    states: {\n        /**\n         * @excluding halo\n         */\n        hover: {\n            opacity: 1,\n            borderColor: \"#333333\" /* Palette.neutralColor80 */\n        },\n        /**\n         * @excluding halo\n         */\n        select: {\n            color: \"#cccccc\" /* Palette.neutralColor20 */,\n            borderColor: \"#000000\" /* Palette.neutralColor100 */,\n            animation: false\n        },\n        inactive: {\n            opacity: 0.075\n        }\n    },\n    tooltip: {\n        pointFormat: '{point.name}: {point.value}'\n    },\n    legendSymbol: 'rectangle'\n};\n/**\n * A `venn` series. If the [type](#series.venn.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.venn\n * @excluding connectEnds, connectNulls, cropThreshold, dataParser, dataURL,\n *            findNearestPointBy, getExtremesFromAll, label, linecap, lineWidth,\n *            linkedTo, marker, negativeColor, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointStart, softThreshold, stack, stacking, steps,\n *            threshold, xAxis, yAxis, zoneAxis, zones, dataSorting,\n *            boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/venn\n * @apioption series.venn\n */\n/**\n * @type      {Array<*>}\n * @extends   series.scatter.data\n * @excluding marker, x, y\n * @product   highcharts\n * @apioption series.venn.data\n */\n/**\n * The name of the point. Used in data labels and tooltip. If name is not\n * defined then it will default to the joined values in\n * [sets](#series.venn.sets).\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n *\n * @type      {string}\n * @since     7.0.0\n * @product   highcharts\n * @apioption series.venn.data.name\n */\n/**\n * The value of the point, resulting in a relative area of the circle, or area\n * of overlap between two sets in the venn or euler diagram.\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n *\n * @type      {number}\n * @since     7.0.0\n * @product   highcharts\n * @apioption series.venn.data.value\n */\n/**\n * The set or sets the options will be applied to. If a single entry is defined,\n * then it will create a new set. If more than one entry is defined, then it\n * will define the overlap between the sets in the array.\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n *\n * @type      {Array<string>}\n * @since     7.0.0\n * @product   highcharts\n * @apioption series.venn.data.sets\n */\n/**\n * @excluding halo\n * @apioption series.venn.states.hover\n */\n/**\n * @excluding halo\n * @apioption series.venn.states.select\n */\n''; // Detachs doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Venn_VennSeriesDefaults = (VennSeriesDefaults);\n\n;// ./code/es-modules/Series/Venn/VennUtils.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getAreaOfCircle, getCircleCircleIntersection, getOverlapBetweenCircles: getOverlapBetweenCirclesByDistance, isPointInsideAllCircles, isPointInsideCircle, isPointOutsideAllCircles } = Geometry_CircleUtilities;\n\nconst { getDistanceBetweenPoints: VennUtils_getDistanceBetweenPoints } = Geometry_GeometryUtilities;\n\nconst { extend, isArray, isNumber: VennUtils_isNumber, isObject, isString } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Takes an array of relations and adds the properties `totalOverlap` and\n * `overlapping` to each set. The property `totalOverlap` is the sum of\n * value for each relation where this set is included. The property\n * `overlapping` is a map of how much this set is overlapping another set.\n * NOTE: This algorithm ignores relations consisting of more than 2 sets.\n * @private\n * @param {Array<Highcharts.VennRelationObject>} relations\n * The list of relations that should be sorted.\n * @return {Array<Highcharts.VennRelationObject>}\n * Returns the modified input relations with added properties `totalOverlap`\n * and `overlapping`.\n */\nfunction addOverlapToSets(relations) {\n    // Calculate the amount of overlap per set.\n    const mapOfIdToProps = {};\n    relations\n        // Filter out relations consisting of 2 sets.\n        .filter((relation) => (relation.sets.length === 2))\n        // Sum up the amount of overlap for each set.\n        .forEach((relation) => {\n        relation.sets.forEach((set, i, arr) => {\n            if (!isObject(mapOfIdToProps[set])) {\n                mapOfIdToProps[set] = {\n                    totalOverlap: 0,\n                    overlapping: {}\n                };\n            }\n            mapOfIdToProps[set] = {\n                totalOverlap: (mapOfIdToProps[set].totalOverlap || 0) +\n                    relation.value,\n                overlapping: {\n                    ...(mapOfIdToProps[set].overlapping || {}),\n                    [arr[1 - i]]: relation.value\n                }\n            };\n        });\n    });\n    relations\n        // Filter out single sets\n        .filter(isSet)\n        // Extend the set with the calculated properties.\n        .forEach((set) => {\n        const properties = mapOfIdToProps[set.sets[0]];\n        extend(set, properties);\n    });\n    // Returns the modified relations.\n    return relations;\n}\n/**\n * Finds the root of a given function. The root is the input value needed\n * for a function to return 0.\n *\n * See https://en.wikipedia.org/wiki/Bisection_method#Algorithm\n *\n * TODO: Add unit tests.\n *\n * @param {Function} f\n * The function to find the root of.\n * @param {number} a\n * The lowest number in the search range.\n * @param {number} b\n * The highest number in the search range.\n * @param {number} [tolerance=1e-10]\n * The allowed difference between the returned value and root.\n * @param {number} [maxIterations=100]\n * The maximum iterations allowed.\n * @return {number}\n * Root number.\n */\nfunction bisect(f, a, b, tolerance, maxIterations) {\n    const fA = f(a), fB = f(b), nMax = maxIterations || 100, tol = tolerance || 1e-10;\n    let delta = b - a, x, fX, n = 1;\n    if (a >= b) {\n        throw new Error('a must be smaller than b.');\n    }\n    else if (fA * fB > 0) {\n        throw new Error('f(a) and f(b) must have opposite signs.');\n    }\n    if (fA === 0) {\n        x = a;\n    }\n    else if (fB === 0) {\n        x = b;\n    }\n    else {\n        while (n++ <= nMax && fX !== 0 && delta > tol) {\n            delta = (b - a) / 2;\n            x = a + delta;\n            fX = f(x);\n            // Update low and high for next search interval.\n            if (fA * fX > 0) {\n                a = x;\n            }\n            else {\n                b = x;\n            }\n        }\n    }\n    return x;\n}\n/**\n * @private\n */\nfunction getCentroid(simplex) {\n    const arr = simplex.slice(0, -1), length = arr.length, result = [], sum = (data, point) => {\n        data.sum += point[data.i];\n        return data;\n    };\n    for (let i = 0; i < length; i++) {\n        result[i] = arr.reduce(sum, { sum: 0, i: i }).sum / length;\n    }\n    return result;\n}\n/**\n * Uses the bisection method to make a best guess of the ideal distance\n * between two circles too get the desired overlap.\n * Currently there is no known formula to calculate the distance from the\n * area of overlap, which makes the bisection method preferred.\n * @private\n * @param {number} r1\n * Radius of the first circle.\n * @param {number} r2\n * Radius of the second circle.\n * @param {number} overlap\n * The wanted overlap between the two circles.\n * @return {number}\n * Returns the distance needed to get the wanted overlap between the two\n * circles.\n */\nfunction getDistanceBetweenCirclesByOverlap(r1, r2, overlap) {\n    const maxDistance = r1 + r2;\n    let distance;\n    if (overlap <= 0) {\n        // If overlap is below or equal to zero, then there is no overlap.\n        distance = maxDistance;\n    }\n    else if (getAreaOfCircle(r1 < r2 ? r1 : r2) <= overlap) {\n        // When area of overlap is larger than the area of the smallest\n        // circle, then it is completely overlapping.\n        distance = 0;\n    }\n    else {\n        distance = bisect((x) => {\n            const actualOverlap = getOverlapBetweenCirclesByDistance(r1, r2, x);\n            // Return the difference between wanted and actual overlap.\n            return overlap - actualOverlap;\n        }, 0, maxDistance);\n    }\n    return distance;\n}\n/**\n * Finds the available width for a label, by taking the label position and\n * finding the largest distance, which is inside all internal circles, and\n * outside all external circles.\n *\n * @private\n * @param {Highcharts.PositionObject} pos\n * The x and y coordinate of the label.\n * @param {Array<Highcharts.CircleObject>} internal\n * Internal circles.\n * @param {Array<Highcharts.CircleObject>} external\n * External circles.\n * @return {number}\n * Returns available width for the label.\n */\nfunction getLabelWidth(pos, internal, external) {\n    const radius = internal.reduce((min, circle) => Math.min(circle.r, min), Infinity), \n    // Filter out external circles that are completely overlapping.\n    filteredExternals = external.filter((circle) => !isPointInsideCircle(pos, circle));\n    const findDistance = function (maxDistance, direction) {\n        return bisect((x) => {\n            const testPos = {\n                x: pos.x + (direction * x),\n                y: pos.y\n            }, isValid = (isPointInsideAllCircles(testPos, internal) &&\n                isPointOutsideAllCircles(testPos, filteredExternals));\n            // If the position is valid, then we want to move towards the\n            // max distance. If not, then we want to away from the max distance.\n            return -(maxDistance - x) + (isValid ? 0 : Number.MAX_VALUE);\n        }, 0, maxDistance);\n    };\n    // Find the smallest distance of left and right.\n    return Math.min(findDistance(radius, -1), findDistance(radius, 1)) * 2;\n}\n/**\n * Calculates a margin for a point based on the internal and external\n * circles. The margin describes if the point is well placed within the\n * internal circles, and away from the external.\n * @private\n * @todo add unit tests.\n * @param {Highcharts.PositionObject} point\n * The point to evaluate.\n * @param {Array<Highcharts.CircleObject>} internal\n * The internal circles.\n * @param {Array<Highcharts.CircleObject>} external\n * The external circles.\n * @return {number}\n * Returns the margin.\n */\nfunction getMarginFromCircles(point, internal, external) {\n    let margin = internal.reduce((margin, circle) => {\n        const m = circle.r - VennUtils_getDistanceBetweenPoints(point, circle);\n        return (m <= margin) ? m : margin;\n    }, Number.MAX_VALUE);\n    margin = external.reduce((margin, circle) => {\n        const m = VennUtils_getDistanceBetweenPoints(point, circle) - circle.r;\n        return (m <= margin) ? m : margin;\n    }, margin);\n    return margin;\n}\n/**\n * Calculates the area of overlap between a list of circles.\n * @private\n * @todo add support for calculating overlap between more than 2 circles.\n * @param {Array<Highcharts.CircleObject>} circles\n * List of circles with their given positions.\n * @return {number}\n * Returns the area of overlap between all the circles.\n */\nfunction getOverlapBetweenCircles(circles) {\n    let overlap = 0;\n    // When there is only two circles we can find the overlap by using their\n    // radiuses and the distance between them.\n    if (circles.length === 2) {\n        const circle1 = circles[0];\n        const circle2 = circles[1];\n        overlap = getOverlapBetweenCirclesByDistance(circle1.r, circle2.r, VennUtils_getDistanceBetweenPoints(circle1, circle2));\n    }\n    return overlap;\n}\n// eslint-disable-next-line require-jsdoc\n/**\n *\n */\nfunction isSet(x) {\n    return isArray(x.sets) && x.sets.length === 1;\n}\n// eslint-disable-next-line require-jsdoc\n/**\n *\n */\nfunction isValidRelation(x) {\n    const map = {};\n    return (isObject(x) &&\n        (VennUtils_isNumber(x.value) && x.value > -1) &&\n        (isArray(x.sets) && x.sets.length > 0) &&\n        !x.sets.some(function (set) {\n            let invalid = false;\n            if (!map[set] && isString(set)) {\n                map[set] = true;\n            }\n            else {\n                invalid = true;\n            }\n            return invalid;\n        }));\n}\n// eslint-disable-next-line require-jsdoc\n/**\n *\n */\nfunction isValidSet(x) {\n    return (isValidRelation(x) && isSet(x) && x.value > 0);\n}\n/**\n * Uses a greedy approach to position all the sets. Works well with a small\n * number of sets, and are in these cases a good choice aesthetically.\n * @private\n * @param {Array<object>} relations List of the overlap between two or more\n * sets, or the size of a single set.\n * @return {Array<object>} List of circles and their calculated positions.\n */\nfunction layoutGreedyVenn(relations) {\n    const positionedSets = [], mapOfIdToCircles = {};\n    // Define a circle for each set.\n    relations\n        .filter((relation) => (relation.sets.length === 1))\n        .forEach((relation) => {\n        mapOfIdToCircles[relation.sets[0]] = relation.circle = {\n            x: Number.MAX_VALUE,\n            y: Number.MAX_VALUE,\n            r: Math.sqrt(relation.value / Math.PI)\n        };\n    });\n    /**\n     * Takes a set and updates the position, and add the set to the list of\n     * positioned sets.\n     * @private\n     * @param {Object} set\n     * The set to add to its final position.\n     * @param {Object} coordinates\n     * The coordinates to position the set at.\n     */\n    const positionSet = (set, coordinates) => {\n        const circle = set.circle;\n        if (circle) {\n            circle.x = coordinates.x;\n            circle.y = coordinates.y;\n        }\n        positionedSets.push(set);\n    };\n    // Find overlap between sets. Ignore relations with more then 2 sets.\n    addOverlapToSets(relations);\n    // Sort sets by the sum of their size from large to small.\n    const sortedByOverlap = relations\n        .filter(isSet)\n        .sort(sortByTotalOverlap);\n    // Position the most overlapped set at 0,0.\n    positionSet(sortedByOverlap.shift(), { x: 0, y: 0 });\n    const relationsWithTwoSets = relations.filter((x) => (x.sets.length === 2));\n    // Iterate and position the remaining sets.\n    for (const set of sortedByOverlap) {\n        const circle = set.circle;\n        if (!circle) {\n            continue;\n        }\n        const radius = circle.r, overlapping = set.overlapping;\n        const bestPosition = positionedSets.reduce((best, positionedSet, i) => {\n            const positionedCircle = positionedSet.circle;\n            if (!positionedCircle || !overlapping) {\n                return best;\n            }\n            const overlap = overlapping[positionedSet.sets[0]];\n            // Calculate the distance between the sets to get the\n            // correct overlap\n            const distance = getDistanceBetweenCirclesByOverlap(radius, positionedCircle.r, overlap);\n            // Create a list of possible coordinates calculated from\n            // distance.\n            let possibleCoordinates = [\n                { x: positionedCircle.x + distance, y: positionedCircle.y },\n                { x: positionedCircle.x - distance, y: positionedCircle.y },\n                { x: positionedCircle.x, y: positionedCircle.y + distance },\n                { x: positionedCircle.x, y: positionedCircle.y - distance }\n            ];\n            // If there are more circles overlapping, then add the\n            // intersection points as possible positions.\n            for (const positionedSet2 of positionedSets.slice(i + 1)) {\n                const positionedCircle2 = positionedSet2.circle, overlap2 = overlapping[positionedSet2.sets[0]];\n                if (!positionedCircle2) {\n                    continue;\n                }\n                const distance2 = getDistanceBetweenCirclesByOverlap(radius, positionedCircle2.r, overlap2);\n                // Add intersections to list of coordinates.\n                possibleCoordinates = possibleCoordinates.concat(getCircleCircleIntersection({\n                    x: positionedCircle.x,\n                    y: positionedCircle.y,\n                    r: distance\n                }, {\n                    x: positionedCircle2.x,\n                    y: positionedCircle2.y,\n                    r: distance2\n                }));\n            }\n            // Iterate all suggested coordinates and find the best one.\n            for (const coordinates of possibleCoordinates) {\n                circle.x = coordinates.x;\n                circle.y = coordinates.y;\n                // Calculate loss for the suggested coordinates.\n                const currentLoss = loss(mapOfIdToCircles, relationsWithTwoSets);\n                // If the loss is better, then use these new coordinates\n                if (currentLoss < best.loss) {\n                    best.loss = currentLoss;\n                    best.coordinates = coordinates;\n                }\n            }\n            // Return resulting coordinates.\n            return best;\n        }, {\n            loss: Number.MAX_VALUE,\n            coordinates: void 0\n        });\n        // Add the set to its final position.\n        positionSet(set, bestPosition.coordinates);\n    }\n    // Return the positions of each set.\n    return mapOfIdToCircles;\n}\n/**\n * Calculates the difference between the desired overlap and the actual\n * overlap between two circles.\n * @private\n * @param {Dictionary<Highcharts.CircleObject>} mapOfIdToCircle\n * Map from id to circle.\n * @param {Array<Highcharts.VennRelationObject>} relations\n * List of relations to calculate the loss of.\n * @return {number}\n * Returns the loss between positions of the circles for the given\n * relations.\n */\nfunction loss(mapOfIdToCircle, relations) {\n    const precision = 10e10;\n    // Iterate all the relations and calculate their individual loss.\n    return relations.reduce(function (totalLoss, relation) {\n        let loss = 0;\n        if (relation.sets.length > 1) {\n            const wantedOverlap = relation.value;\n            // Calculate the actual overlap between the sets.\n            const actualOverlap = getOverlapBetweenCircles(\n            // Get the circles for the given sets.\n            relation.sets.map(function (set) {\n                return mapOfIdToCircle[set];\n            }));\n            const diff = wantedOverlap - actualOverlap;\n            loss = Math.round((diff * diff) * precision) / precision;\n        }\n        // Add calculated loss to the sum.\n        return totalLoss + loss;\n    }, 0);\n}\n/**\n * Finds an optimal position for a given point.\n * @todo add unit tests.\n * @todo add constraints to optimize the algorithm.\n * @private\n * @param {Highcharts.NelderMeadTestFunction} fn\n *        The function to test a point.\n * @param {Highcharts.NelderMeadPointArray} initial\n *        The initial point to optimize.\n * @return {Highcharts.NelderMeadPointArray}\n *         Returns the optimized position of a point.\n */\nfunction nelderMead(fn, initial) {\n    const maxIterations = 100, sortByFx = function (a, b) {\n        return a.fx - b.fx;\n    }, pRef = 1, // Reflection parameter\n    pExp = 2, // Expansion parameter\n    pCon = -0.5, // Contraction parameter\n    pOCon = pCon * pRef, // Outwards contraction parameter\n    pShrink = 0.5; // Shrink parameter\n    /**\n     * @private\n     */\n    const weightedSum = (weight1, v1, weight2, v2) => v1.map((x, i) => weight1 * x + weight2 * v2[i]);\n    /**\n     * @private\n     */\n    const getSimplex = (initial) => {\n        const n = initial.length, simplex = new Array(n + 1);\n        // Initial point to the simplex.\n        simplex[0] = initial;\n        simplex[0].fx = fn(initial);\n        // Create a set of extra points based on the initial.\n        for (let i = 0; i < n; ++i) {\n            const point = initial.slice();\n            point[i] = point[i] ? point[i] * 1.05 : 0.001;\n            point.fx = fn(point);\n            simplex[i + 1] = point;\n        }\n        return simplex;\n    };\n    const updateSimplex = (simplex, point) => {\n        point.fx = fn(point);\n        simplex[simplex.length - 1] = point;\n        return simplex;\n    };\n    const shrinkSimplex = (simplex) => {\n        const best = simplex[0];\n        return simplex.map((point) => {\n            const p = weightedSum(1 - pShrink, best, pShrink, point);\n            p.fx = fn(p);\n            return p;\n        });\n    };\n    const getPoint = (centroid, worst, a, b) => {\n        const point = weightedSum(a, centroid, b, worst);\n        point.fx = fn(point);\n        return point;\n    };\n    // Create a simplex\n    let simplex = getSimplex(initial);\n    // Iterate from 0 to max iterations\n    for (let i = 0; i < maxIterations; i++) {\n        // Sort the simplex\n        simplex.sort(sortByFx);\n        // Create a centroid from the simplex\n        const worst = simplex[simplex.length - 1];\n        const centroid = getCentroid(simplex);\n        // Calculate the reflected point.\n        const reflected = getPoint(centroid, worst, 1 + pRef, -pRef);\n        if (reflected.fx < simplex[0].fx) {\n            // If reflected point is the best, then possibly expand.\n            const expanded = getPoint(centroid, worst, 1 + pExp, -pExp);\n            simplex = updateSimplex(simplex, (expanded.fx < reflected.fx) ? expanded : reflected);\n        }\n        else if (reflected.fx >= simplex[simplex.length - 2].fx) {\n            // If the reflected point is worse than the second worse, then\n            // contract.\n            let contracted;\n            if (reflected.fx > worst.fx) {\n                // If the reflected is worse than the worst point, do a\n                // contraction\n                contracted = getPoint(centroid, worst, 1 + pCon, -pCon);\n                if (contracted.fx < worst.fx) {\n                    simplex = updateSimplex(simplex, contracted);\n                }\n                else {\n                    simplex = shrinkSimplex(simplex);\n                }\n            }\n            else {\n                // Otherwise do an outwards contraction\n                contracted = getPoint(centroid, worst, 1 - pOCon, pOCon);\n                if (contracted.fx < reflected.fx) {\n                    simplex = updateSimplex(simplex, contracted);\n                }\n                else {\n                    simplex = shrinkSimplex(simplex);\n                }\n            }\n        }\n        else {\n            simplex = updateSimplex(simplex, reflected);\n        }\n    }\n    return simplex[0];\n}\n/**\n * Prepares the venn data so that it is usable for the layout function.\n * Filter out sets, or intersections that includes sets, that are missing in\n * the data or has (value < 1). Adds missing relations between sets in the\n * data as value = 0.\n * @private\n * @param {Array<object>} data The raw input data.\n * @return {Array<object>} Returns an array of valid venn data.\n */\nfunction processVennData(data, splitter) {\n    const d = isArray(data) ? data : [];\n    const validSets = d\n        .reduce(function (arr, x) {\n        // Check if x is a valid set, and that it is not an duplicate.\n        if (x.sets && isValidSet(x) && arr.indexOf(x.sets[0]) === -1) {\n            arr.push(x.sets[0]);\n        }\n        return arr;\n    }, [])\n        .sort();\n    const mapOfIdToRelation = d.reduce(function (mapOfIdToRelation, relation) {\n        if (relation.sets &&\n            isValidRelation(relation) &&\n            !relation.sets.some(function (set) {\n                return validSets.indexOf(set) === -1;\n            })) {\n            mapOfIdToRelation[relation.sets.sort().join(splitter)] = {\n                sets: relation.sets,\n                value: relation.value || 0\n            };\n        }\n        return mapOfIdToRelation;\n    }, {});\n    validSets.reduce(function (combinations, set, i, arr) {\n        const remaining = arr.slice(i + 1);\n        remaining.forEach(function (set2) {\n            combinations.push(set + splitter + set2);\n        });\n        return combinations;\n    }, []).forEach(function (combination) {\n        if (!mapOfIdToRelation[combination]) {\n            const obj = {\n                sets: combination.split(splitter),\n                value: 0\n            };\n            mapOfIdToRelation[combination] = obj;\n        }\n    });\n    // Transform map into array.\n    return Object\n        .keys(mapOfIdToRelation)\n        .map(function (id) {\n        return mapOfIdToRelation[id];\n    });\n}\n/**\n * Takes two sets and finds the one with the largest total overlap.\n * @private\n * @param {Object} a\n * The first set to compare.\n * @param {Object} b\n * The second set to compare.\n * @return {number}\n * Returns 0 if a and b are equal, <0 if a is greater, >0 if b is greater.\n */\nfunction sortByTotalOverlap(a, b) {\n    if (typeof b.totalOverlap !== 'undefined' &&\n        typeof a.totalOverlap !== 'undefined') {\n        return b.totalOverlap - a.totalOverlap;\n    }\n    return NaN;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst VennUtils = {\n    geometry: Geometry_GeometryUtilities,\n    geometryCircles: Geometry_CircleUtilities,\n    addOverlapToSets,\n    getCentroid,\n    getDistanceBetweenCirclesByOverlap,\n    getLabelWidth,\n    getMarginFromCircles,\n    isSet,\n    layoutGreedyVenn,\n    loss,\n    nelderMead,\n    processVennData,\n    sortByTotalOverlap\n};\n/* harmony default export */ const Venn_VennUtils = (VennUtils);\n\n;// ./code/es-modules/Series/Venn/VennSeries.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { getAreaOfIntersectionBetweenCircles, getCirclesIntersectionPolygon, isCircle1CompletelyOverlappingCircle2, isPointInsideAllCircles: VennSeries_isPointInsideAllCircles, isPointOutsideAllCircles: VennSeries_isPointOutsideAllCircles } = Geometry_CircleUtilities;\n\n\nconst { getCenterOfPoints: VennSeries_getCenterOfPoints } = Geometry_GeometryUtilities;\n\nconst { scatter: ScatterSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\n\n\nconst { addEvent, extend: VennSeries_extend, isArray: VennSeries_isArray, isNumber: VennSeries_isNumber, isObject: VennSeries_isObject, merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.venn\n *\n * @augments Highcharts.Series\n */\nclass VennSeries extends ScatterSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Finds the optimal label position by looking for a position that has a low\n     * distance from the internal circles, and as large possible distance to the\n     * external circles.\n     * @private\n     * @todo Optimize the intial position.\n     * @todo Add unit tests.\n     * @param {Array<Highcharts.CircleObject>} internal\n     * Internal circles.\n     * @param {Array<Highcharts.CircleObject>} external\n     * External circles.\n     * @return {Highcharts.PositionObject}\n     * Returns the found position.\n     */\n    static getLabelPosition(internal, external) {\n        // Get the best label position within the internal circles.\n        let best = internal.reduce((best, circle) => {\n            const d = circle.r / 2;\n            // Give a set of points with the circle to evaluate as the best\n            // label position.\n            return [\n                { x: circle.x, y: circle.y },\n                { x: circle.x + d, y: circle.y },\n                { x: circle.x - d, y: circle.y },\n                { x: circle.x, y: circle.y + d },\n                { x: circle.x, y: circle.y - d }\n            ]\n                // Iterate the given points and return the one with the\n                // largest margin.\n                .reduce((best, point) => {\n                const margin = Venn_VennUtils.getMarginFromCircles(point, internal, external);\n                // If the margin better than the current best, then\n                // update sbest.\n                if (best.margin < margin) {\n                    best.point = point;\n                    best.margin = margin;\n                }\n                return best;\n            }, best);\n        }, {\n            point: void 0,\n            margin: -Number.MAX_VALUE\n        }).point;\n        // Use nelder mead to optimize the initial label position.\n        const optimal = Venn_VennUtils.nelderMead((p) => -(Venn_VennUtils.getMarginFromCircles({ x: p[0], y: p[1] }, internal, external)), [\n            best.x,\n            best.y\n        ]);\n        // Update best to be the point which was found to have the best margin.\n        best = {\n            x: optimal[0],\n            y: optimal[1]\n        };\n        if (!(VennSeries_isPointInsideAllCircles(best, internal) &&\n            VennSeries_isPointOutsideAllCircles(best, external))) {\n            // If point was either outside one of the internal, or inside one of\n            // the external, then it was invalid and should use a fallback.\n            if (internal.length > 1) {\n                best = VennSeries_getCenterOfPoints(getCirclesIntersectionPolygon(internal));\n            }\n            else {\n                best = {\n                    x: internal[0].x,\n                    y: internal[0].y\n                };\n            }\n        }\n        // Return the best point.\n        return best;\n    }\n    /**\n     * Calculates data label values for a given relations object.\n     *\n     * @private\n     * @todo add unit tests\n     * @param {Highcharts.VennRelationObject} relation A relations object.\n     * @param {Array<Highcharts.VennRelationObject>} setRelations The list of\n     * relations that is a set.\n     * @return {Highcharts.VennLabelValuesObject}\n     * Returns an object containing position and width of the label.\n     */\n    static getLabelValues(relation, setRelations) {\n        const sets = relation.sets;\n        // Create a list of internal and external circles.\n        const data = setRelations.reduce((data, set) => {\n            // If the set exists in this relation, then it is internal,\n            // otherwise it will be external.\n            const isInternal = sets.indexOf(set.sets[0]) > -1;\n            const property = isInternal ? 'internal' : 'external';\n            // Add the circle to the list.\n            if (set.circle) {\n                data[property].push(set.circle);\n            }\n            return data;\n        }, {\n            internal: [],\n            external: []\n        });\n        // Filter out external circles that are completely overlapping all\n        // internal\n        data.external = data.external.filter((externalCircle) => data.internal.some((internalCircle) => !isCircle1CompletelyOverlappingCircle2(externalCircle, internalCircle)));\n        // Calculate the label position.\n        const position = VennSeries.getLabelPosition(data.internal, data.external);\n        // Calculate the label width\n        const width = Venn_VennUtils.getLabelWidth(position, data.internal, data.external);\n        return {\n            position,\n            width\n        };\n    }\n    /**\n     * Calculates the positions, and the label values of all the sets in the\n     * venn diagram.\n     *\n     * @private\n     * @todo Add support for constrained MDS.\n     * @param {Array<Highchats.VennRelationObject>} relations\n     * List of the overlap between two or more sets, or the size of a single\n     * set.\n     * @return {Highcharts.Dictionary<*>}\n     * List of circles and their calculated positions.\n     */\n    static layout(relations) {\n        const mapOfIdToShape = {};\n        const mapOfIdToLabelValues = {};\n        // Calculate best initial positions by using greedy layout.\n        if (relations.length > 0) {\n            const mapOfIdToCircles = Venn_VennUtils.layoutGreedyVenn(relations);\n            const setRelations = relations.filter(Venn_VennUtils.isSet);\n            for (const relation of relations) {\n                const sets = relation.sets;\n                const id = sets.join();\n                // Get shape from map of circles, or calculate intersection.\n                const shape = Venn_VennUtils.isSet(relation) ?\n                    mapOfIdToCircles[id] :\n                    getAreaOfIntersectionBetweenCircles(sets.map((set) => mapOfIdToCircles[set]));\n                // Calculate label values if the set has a shape\n                if (shape) {\n                    mapOfIdToShape[id] = shape;\n                    mapOfIdToLabelValues[id] = VennSeries.getLabelValues(relation, setRelations);\n                }\n            }\n        }\n        return { mapOfIdToShape, mapOfIdToLabelValues };\n    }\n    /**\n     * Calculates the proper scale to fit the cloud inside the plotting area.\n     * @private\n     * @todo add unit test\n     * @param {number} targetWidth\n     * Width of target area.\n     * @param {number} targetHeight\n     * Height of target area.\n     * @param {Highcharts.PolygonBoxObject} field\n     * The playing field.\n     * @return {Highcharts.Dictionary<number>}\n     * Returns the value to scale the playing field up to the size of the target\n     * area, and center of x and y.\n     */\n    static getScale(targetWidth, targetHeight, field) {\n        const height = field.bottom - field.top, // Top is smaller than bottom\n        width = field.right - field.left, scaleX = width > 0 ? 1 / width * targetWidth : 1, scaleY = height > 0 ? 1 / height * targetHeight : 1, adjustX = (field.right + field.left) / 2, adjustY = (field.top + field.bottom) / 2, scale = Math.min(scaleX, scaleY);\n        return {\n            scale: scale,\n            centerX: targetWidth / 2 - adjustX * scale,\n            centerY: targetHeight / 2 - adjustY * scale\n        };\n    }\n    /**\n     * If a circle is outside a give field, then the boundaries of the field is\n     * adjusted accordingly. Modifies the field object which is passed as the\n     * first parameter.\n     * @private\n     * @todo NOTE: Copied from wordcloud, can probably be unified.\n     * @param {Highcharts.PolygonBoxObject} field\n     * The bounding box of a playing field.\n     * @param {Highcharts.CircleObject} circle\n     * The bounding box for a placed point.\n     * @return {Highcharts.PolygonBoxObject}\n     * Returns a modified field object.\n     */\n    static updateFieldBoundaries(field, circle) {\n        const left = circle.x - circle.r, right = circle.x + circle.r, bottom = circle.y + circle.r, top = circle.y - circle.r;\n        // TODO improve type checking.\n        if (!VennSeries_isNumber(field.left) || field.left > left) {\n            field.left = left;\n        }\n        if (!VennSeries_isNumber(field.right) || field.right < right) {\n            field.right = right;\n        }\n        if (!VennSeries_isNumber(field.top) || field.top > top) {\n            field.top = top;\n        }\n        if (!VennSeries_isNumber(field.bottom) || field.bottom < bottom) {\n            field.bottom = bottom;\n        }\n        return field;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    animate(init) {\n        if (!init) {\n            const series = this, animOptions = animObject(series.options.animation);\n            for (const point of series.points) {\n                const args = point.shapeArgs;\n                if (point.graphic && args) {\n                    const attr = {}, animate = {};\n                    if (args.d) {\n                        // If shape is a path, then animate opacity.\n                        attr.opacity = 0.001;\n                    }\n                    else {\n                        // If shape is a circle, then animate radius.\n                        attr.r = 0;\n                        animate.r = args.r;\n                    }\n                    point.graphic\n                        .attr(attr)\n                        .animate(animate, animOptions);\n                    // If shape is path, then fade it in after the circles\n                    // animation\n                    if (args.d) {\n                        setTimeout(() => {\n                            point?.graphic?.animate({\n                                opacity: 1\n                            });\n                        }, animOptions.duration);\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Draw the graphics for each point.\n     * @private\n     */\n    drawPoints() {\n        const series = this, \n        // Series properties\n        chart = series.chart, group = series.group, points = series.points || [], \n        // Chart properties\n        renderer = chart.renderer;\n        // Iterate all points and calculate and draw their graphics.\n        for (const point of points) {\n            const attribs = {\n                zIndex: VennSeries_isArray(point.sets) ? point.sets.length : 0\n            }, shapeArgs = point.shapeArgs;\n            // Add point attribs\n            if (!chart.styledMode) {\n                VennSeries_extend(attribs, series.pointAttribs(point, point.state));\n            }\n            // Draw the point graphic.\n            Series_DrawPointUtilities.draw(point, {\n                isNew: !point.graphic,\n                animatableAttribs: shapeArgs,\n                attribs: attribs,\n                group: group,\n                renderer: renderer,\n                shapeType: shapeArgs?.d ? 'path' : 'circle'\n            });\n        }\n    }\n    init() {\n        ScatterSeries.prototype.init.apply(this, arguments);\n        // Venn's opacity is a different option from other series\n        delete this.opacity;\n    }\n    /**\n     * Calculates the style attributes for a point. The attributes can vary\n     * depending on the state of the point.\n     * @private\n     * @param {Highcharts.Point} point\n     * The point which will get the resulting attributes.\n     * @param {string} [state]\n     * The state of the point.\n     * @return {Highcharts.SVGAttributes}\n     * Returns the calculated attributes.\n     */\n    pointAttribs(point, state) {\n        const series = this, seriesOptions = series.options || {}, pointOptions = point?.options || {}, stateOptions = (state && seriesOptions.states[state]) || {}, options = merge(seriesOptions, { color: point?.color }, pointOptions, stateOptions);\n        // Return resulting values for the attributes.\n        return {\n            'fill': color(options.color)\n                .brighten(options.brightness)\n                .get(),\n            // Set opacity directly to the SVG element, not to pattern #14372.\n            opacity: options.opacity,\n            'stroke': options.borderColor,\n            'stroke-width': options.borderWidth,\n            'dashstyle': options.borderDashStyle\n        };\n    }\n    translate() {\n        const chart = this.chart;\n        this.dataTable.modified = this.dataTable;\n        this.generatePoints();\n        // Process the data before passing it into the layout function.\n        const relations = Venn_VennUtils.processVennData(this.options.data, VennSeries.splitter);\n        // Calculate the positions of each circle.\n        const { mapOfIdToShape, mapOfIdToLabelValues } = VennSeries.layout(relations);\n        // Calculate the scale, and center of the plot area.\n        const field = Object.keys(mapOfIdToShape)\n            .filter((key) => {\n            const shape = mapOfIdToShape[key];\n            return shape && VennSeries_isNumber(shape.r);\n        })\n            .reduce((field, key) => VennSeries.updateFieldBoundaries(field, mapOfIdToShape[key]), {\n            top: 0,\n            bottom: 0,\n            left: 0,\n            right: 0\n        }), scaling = VennSeries.getScale(chart.plotWidth, chart.plotHeight, field), scale = scaling.scale, centerX = scaling.centerX, centerY = scaling.centerY;\n        // Iterate all points and calculate and draw their graphics.\n        for (const point of this.points) {\n            const sets = VennSeries_isArray(point.sets) ? point.sets : [], id = sets.join(), shape = mapOfIdToShape[id], dataLabelValues = mapOfIdToLabelValues[id] || {}, dlOptions = point.options?.dataLabels;\n            let shapeArgs, dataLabelWidth = dataLabelValues.width, dataLabelPosition = dataLabelValues.position;\n            if (shape) {\n                if (shape.r) {\n                    shapeArgs = {\n                        x: centerX + shape.x * scale,\n                        y: centerY + shape.y * scale,\n                        r: shape.r * scale\n                    };\n                }\n                else if (shape.d) {\n                    const d = shape.d;\n                    d.forEach((seg) => {\n                        if (seg[0] === 'M') {\n                            seg[1] = centerX + seg[1] * scale;\n                            seg[2] = centerY + seg[2] * scale;\n                        }\n                        else if (seg[0] === 'A') {\n                            seg[1] = seg[1] * scale;\n                            seg[2] = seg[2] * scale;\n                            seg[6] = centerX + seg[6] * scale;\n                            seg[7] = centerY + seg[7] * scale;\n                        }\n                    });\n                    shapeArgs = { d };\n                }\n                // Scale the position for the data label.\n                if (dataLabelPosition) {\n                    dataLabelPosition.x = centerX + dataLabelPosition.x * scale;\n                    dataLabelPosition.y = centerY + dataLabelPosition.y * scale;\n                }\n                else {\n                    dataLabelPosition = {};\n                }\n                if (VennSeries_isNumber(dataLabelWidth)) {\n                    dataLabelWidth = Math.round(dataLabelWidth * scale);\n                }\n            }\n            point.shapeArgs = shapeArgs;\n            // Placement for the data labels\n            if (dataLabelPosition && shapeArgs) {\n                point.plotX = dataLabelPosition.x;\n                point.plotY = dataLabelPosition.y;\n            }\n            // Add width for the data label\n            if (dataLabelWidth && shapeArgs) {\n                point.dlOptions = merge(true, {\n                    style: {\n                        width: dataLabelWidth\n                    }\n                }, VennSeries_isObject(dlOptions, true) ? dlOptions : void 0);\n            }\n            // Set name for usage in tooltip and in data label.\n            point.name = point.options.name || sets.join('∩');\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nVennSeries.splitter = 'highcharts-split';\nVennSeries.defaultOptions = merge(ScatterSeries.defaultOptions, Venn_VennSeriesDefaults);\nVennSeries_extend(VennSeries.prototype, {\n    axisTypes: [],\n    directTouch: true,\n    isCartesian: false,\n    pointArrayMap: ['value'],\n    pointClass: Venn_VennPoint,\n    utils: Venn_VennUtils\n});\n// Modify final series options.\naddEvent(VennSeries, 'afterSetOptions', function (e) {\n    const options = e.options, states = options.states || {};\n    if (this.is('venn')) {\n        // Explicitly disable all halo options.\n        for (const state of Object.keys(states)) {\n            states[state].halo = false;\n        }\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('venn', VennSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Venn_VennSeries = ((/* unused pure expression or super */ null && (VennSeries)));\n\n;// ./code/es-modules/masters/modules/venn.js\n\n\n\n\n/* harmony default export */ const venn_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "GeometryUtilities", "CircleUtilities", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "venn_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "getCenterOfPoints", "points", "sum", "reduce", "point", "x", "y", "length", "getDistanceBetweenPoints", "p1", "p2", "Math", "sqrt", "pow", "getAngleBetweenPoints", "atan2", "pointInPolygon", "polygon", "len", "i", "j", "inside", "x1", "y1", "x2", "y2", "Geometry_GeometryUtilities", "round", "decimals", "getAreaOfCircle", "r", "Error", "PI", "getCircularSegmentArea", "h", "acos", "getCircleCircleIntersection", "c1", "c2", "r1", "r2", "abs", "r1Square", "x0", "y0", "rx", "ry", "getCirclesIntersectionPoints", "circles", "arr", "additional", "slice", "indexes", "concat", "map", "p", "isPointInsideCircle", "circle", "isPointInsideAllCircles", "some", "getCirclesIntersectionPolygon", "filter", "getOverlapBetweenCircles", "overlap", "d1", "isCircle1CompletelyOverlappingCircle2", "circle1", "circle2", "isPointOutsideAllCircles", "getAreaOfIntersectionBetweenCircles", "intersectionPoints", "result", "center", "startPoint", "angle", "sort", "b", "arcs", "data", "midPoint", "arc", "index", "indexOf", "angle1", "angle2", "angleDiff", "width", "sin", "cos", "largeArc", "push", "unshift", "Geometry_CircleUtilities", "Series_DrawPointUtilities", "draw", "params", "animatableAttribs", "onComplete", "css", "renderer", "animation", "series", "chart", "hasRendered", "options", "graphic", "attribs", "getClassName", "shouldDraw", "shapeType", "text", "image", "imageUrl", "attr", "shapeArgs", "add", "group", "animate", "isNew", "destroy", "keys", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "scatter", "pointClass", "ScatterPoint", "seriesTypes", "isNumber", "getOverlapBetweenCirclesByDistance", "VennUtils_getDistanceBetweenPoints", "extend", "isArray", "VennUtils_isNumber", "isObject", "isString", "addOverlapToSets", "relations", "mapOfIdToProps", "relation", "sets", "for<PERSON>ach", "set", "totalOverlap", "overlapping", "value", "isSet", "properties", "bisect", "f", "tolerance", "maxIterations", "fA", "fB", "nMax", "tol", "delta", "fX", "getCentroid", "simplex", "getDistanceBetweenCirclesByOverlap", "distance", "maxDistance", "isValidRelation", "invalid", "loss", "mapOfIdToCircle", "totalLoss", "diff", "wantedOverlap", "sortByTotalOverlap", "NaN", "Venn_VennUtils", "geometry", "geometryCircles", "<PERSON><PERSON><PERSON><PERSON>", "pos", "internal", "external", "radius", "min", "Infinity", "filteredExternals", "findDistance", "direction", "testPos", "<PERSON><PERSON><PERSON><PERSON>", "Number", "MAX_VALUE", "getMarginFromCircles", "margin", "m", "layoutGreedyVenn", "positionedSets", "mapOfIdToCircles", "positionSet", "coordinates", "sortedByOverlap", "shift", "relationsWithTwoSets", "bestPosition", "best", "positionedSet", "positionedCircle", "possibleCoordinates", "positionedSet2", "positionedCircle2", "overlap2", "distance2", "currentLoss", "nelderMead", "fn", "initial", "sortByFx", "fx", "weightedSum", "weight1", "v1", "weight2", "v2", "updateSimplex", "shrinkSimplex", "getPoint", "centroid", "worst", "getSimplex", "Array", "reflected", "expanded", "contracted", "pCon", "processVennData", "splitter", "validSets", "mapOfIdToRelation", "join", "combinations", "remaining", "set2", "combination", "split", "id", "animObject", "parse", "color", "VennSeries_isPointInsideAllCircles", "VennSeries_isPointOutsideAllCircles", "VennSeries_getCenterOfPoints", "ScatterSeries", "addEvent", "VennSeries_extend", "VennSeries_isArray", "VennSeries_isNumber", "VennSeries_isObject", "merge", "VennSeries", "getLabelPosition", "optimal", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setRelations", "isInternal", "externalCircle", "internalCircle", "position", "layout", "mapOfIdToShape", "mapOfIdToLabelValues", "shape", "getScale", "targetWidth", "targetHeight", "field", "height", "bottom", "top", "right", "left", "adjustX", "adjustY", "scale", "centerX", "centerY", "updateFieldBoundaries", "init", "animOptions", "args", "opacity", "setTimeout", "duration", "drawPoints", "zIndex", "styledMode", "pointAttribs", "state", "apply", "arguments", "seriesOptions", "pointOptions", "stateOptions", "states", "brighten", "brightness", "borderColor", "borderWidth", "borderDashStyle", "translate", "dataTable", "modified", "generatePoints", "scaling", "plot<PERSON>id<PERSON>", "plotHeight", "dataLabelValues", "dlOptions", "dataLabels", "dataLabelWidth", "dataLabelPosition", "seg", "plotX", "plotY", "style", "name", "defaultOptions", "clip", "colorByPoint", "enabled", "verticalAlign", "formatter", "inactiveOtherPoints", "marker", "showInLegend", "legendType", "hover", "select", "inactive", "tooltip", "pointFormat", "legendSymbol", "axisTypes", "directTouch", "isCartesian", "pointArrayMap", "utils", "e", "is", "halo", "registerSeriesType"], "mappings": "CAUA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC1G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,0BAA2B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAAE,GACjI,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,0BAA0B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAErIA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACpH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IA6GNC,EA6GAC,EA1NUC,EAAuB,CAE/B,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,AAAChB,IACxB,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,IAAOlB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACpB,EAASsB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,IAkB7I,AAAC,SAAU7B,CAAiB,EA4BxBA,EAAkB+B,iBAAiB,CAXnC,SAA2BC,CAAM,EAC7B,IAAMC,EAAMD,EAAOE,MAAM,CAAC,CAACD,EAAKE,KAC5BF,EAAIG,CAAC,EAAID,EAAMC,CAAC,CAChBH,EAAII,CAAC,EAAIF,EAAME,CAAC,CACTJ,GACR,CAAEG,EAAG,EAAGC,EAAG,CAAE,GAChB,MAAO,CACHD,EAAGH,EAAIG,CAAC,CAAGJ,EAAOM,MAAM,CACxBD,EAAGJ,EAAII,CAAC,CAAGL,EAAOM,MAAM,AAC5B,CACJ,EAoBAtC,EAAkBuC,wBAAwB,CAH1C,SAAkCC,CAAE,CAAEC,CAAE,EACpC,OAAOC,KAAKC,IAAI,CAACD,KAAKE,GAAG,CAACH,EAAGL,CAAC,CAAGI,EAAGJ,CAAC,CAAE,GAAKM,KAAKE,GAAG,CAACH,EAAGJ,CAAC,CAAGG,EAAGH,CAAC,CAAE,GACtE,EAaArC,EAAkB6C,qBAAqB,CAHvC,SAA+BL,CAAE,CAAEC,CAAE,EACjC,OAAOC,KAAKI,KAAK,CAACL,EAAGL,CAAC,CAAGI,EAAGJ,CAAC,CAAEK,EAAGJ,CAAC,CAAGG,EAAGH,CAAC,CAC9C,EAuBArC,EAAkB+C,cAAc,CAfhC,SAAwB,CAAEX,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAEW,CAAO,EACrC,IAAMC,EAAMD,EAAQV,MAAM,CACtBY,EAAGC,EAAGC,EAAS,CAAA,EACnB,IAAKF,EAAI,EAAGC,EAAIF,EAAM,EAAGC,EAAID,EAAKE,EAAID,IAAK,CACvC,GAAM,CAACG,EAAIC,EAAG,CAAGN,CAAO,CAACE,EAAE,CAAE,CAACK,EAAIC,EAAG,CAAGR,CAAO,CAACG,EAAE,CAC9CG,EAAKjB,GAAMmB,EAAKnB,GACfD,EAAI,AAACmB,CAAAA,EAAKF,CAAC,EACPhB,CAAAA,EAAIiB,CAAC,EACLE,CAAAA,EAAKF,CAAC,EACPD,GACJD,CAAAA,EAAS,CAACA,CAAK,CAEvB,CACA,OAAOA,CACX,CAEJ,EAAGpD,GAAsBA,CAAAA,EAAoB,CAAC,CAAA,GAMjB,IAAMyD,EAA8BzD,EAc3D,CAAE6C,sBAAAA,CAAqB,CAAEd,kBAAAA,CAAiB,CAAEQ,yBAAAA,CAAwB,CAAE,CAAGkB,GAO/E,AAAC,SAAUxD,CAAe,EAkBtB,SAASyD,EAAMtB,CAAC,CAAEuB,CAAQ,EACtB,IAAM/C,EAAI8B,KAAKE,GAAG,CAAC,GAAIe,GACvB,OAAOjB,KAAKgB,KAAK,CAACtB,EAAIxB,GAAKA,CAC/B,CAaA,SAASgD,EAAgBC,CAAC,EACtB,GAAIA,GAAK,EACL,MAAM,AAAIC,MAAM,+CAEpB,OAAOpB,KAAKqB,EAAE,CAAGF,EAAIA,CACzB,CAmBA,SAASG,EAAuBH,CAAC,CAAEI,CAAC,EAChC,OAAQJ,EAAIA,EAAInB,KAAKwB,IAAI,CAAC,EAAID,EAAIJ,GAC9B,AAACA,CAAAA,EAAII,CAAAA,EAAKvB,KAAKC,IAAI,CAACsB,EAAK,CAAA,EAAIJ,EAAII,CAAAA,EACzC,CA8DA,SAASE,EAA4BC,CAAE,CAAEC,CAAE,EACvC,IAAM1D,EAAI4B,EAAyB6B,EAAIC,GAAKC,EAAKF,EAAGP,CAAC,CAAEU,EAAKF,EAAGR,CAAC,CAC5D7B,EAAS,EAAE,CACf,GAAIrB,EAAI2D,EAAKC,GAAM5D,EAAI+B,KAAK8B,GAAG,CAACF,EAAKC,GAAK,CAGtC,IAAME,EAAWH,EAAKA,EAEtBlC,EAAI,AAACqC,CAAAA,EAFgCF,EAAKA,EAEf5D,EAAIA,CAAAA,EAAM,CAAA,EAAIA,CAAAA,EAEzC0B,EAAIK,KAAKC,IAAI,CAAC8B,EAAWrC,EAAIA,GAAIiB,EAAKe,EAAGhC,CAAC,CAAEmB,EAAKc,EAAGjC,CAAC,CAAEkB,EAAKc,EAAG/B,CAAC,CAAEmB,EAAKa,EAAGhC,CAAC,CAAEqC,EAAKrB,EAAKjB,EAAKmB,CAAAA,EAAKF,CAAC,EAAK1C,EAAGgE,EAAKrB,EAAKlB,EAAKoB,CAAAA,EAAKF,CAAC,EAAK3C,EAAGiE,EAAK,CAAA,CAAA,AAAcvC,EAAI1B,EAAhB6C,CAAAA,EAAKF,CAAC,CAAW,EAAGuB,EAAK,CAAA,CAAA,AAAcxC,EAAI1B,EAAhB4C,CAAAA,EAAKF,CAAC,CAAW,EAC1LrB,EAAS,CACL,CAAEI,EAAGsB,EAAMgB,EAAKE,EAAI,IAAKvC,EAAGqB,EAAMiB,EAAKE,EAAI,GAAI,EAC/C,CAAEzC,EAAGsB,EAAMgB,EAAKE,EAAI,IAAKvC,EAAGqB,EAAMiB,EAAKE,EAAI,GAAI,EAClD,AACL,CACA,OAAO7C,CACX,CAaA,SAAS8C,EAA6BC,CAAO,EACzC,OAAOA,EAAQ7C,MAAM,CAAC,CAACF,EAAQoC,EAAIlB,EAAG8B,KAClC,IAAMC,EAAaD,EACdE,KAAK,CAAChC,EAAI,GACVhB,MAAM,CAAC,CAACF,EAAQqC,EAAIlB,KACrB,IAAMgC,EAAU,CAACjC,EAAGC,EAAID,EAAI,EAAE,CAC9B,OAAOlB,EAAOoD,MAAM,CAACjB,EAA4BC,EAAIC,GAAIgB,GAAG,CAAC,AAACC,IAC1DA,EAAEH,OAAO,CAAGA,EACLG,IAEf,EAAG,EAAE,EACL,OAAOtD,EAAOoD,MAAM,CAACH,EACzB,EAAG,EAAE,CACT,CAiCA,SAASM,EAAoBpD,CAAK,CAAEqD,CAAM,EACtC,OAAOjD,EAAyBJ,EAAOqD,IAAWA,EAAO3B,CAAC,CAAG,KACjE,CAgBA,SAAS4B,EAAwBtD,CAAK,CAAE4C,CAAO,EAC3C,MAAO,CAACA,EAAQW,IAAI,CAAC,SAAUF,CAAM,EACjC,MAAO,CAACD,EAAoBpD,EAAOqD,EACvC,EACJ,CAoCA,SAASG,EAA8BZ,CAAO,EAC1C,OAAOD,EAA6BC,GAC/Ba,MAAM,CAAC,SAAUN,CAAC,EACnB,OAAOG,EAAwBH,EAAGP,EACtC,EACJ,CAhPA9E,EAAgByD,KAAK,CAAGA,EAkBxBzD,EAAgB2D,eAAe,CAAGA,EAsBlC3D,EAAgB+D,sBAAsB,CAAGA,EA4CzC/D,EAAgB4F,wBAAwB,CAvBxC,SAAkCvB,CAAE,CAAEC,CAAE,CAAE5D,CAAC,EACvC,IAAImF,EAAU,EAGd,GAAInF,EAAI2D,EAAKC,EAAI,CACb,GAAI5D,GAAK+B,KAAK8B,GAAG,CAACD,EAAKD,GAGnBwB,EAAUlC,EAAgBU,EAAKC,EAAKD,EAAKC,OAExC,CAED,IAAMwB,EAAK,AAACzB,CAAAA,EAAKA,EAAKC,EAAKA,EAAK5D,EAAIA,CAAAA,EAAM,CAAA,EAAIA,CAAAA,EAG9CmF,EAAW9B,EAAuBM,EAAIA,EAAKyB,GACvC/B,EAAuBO,EAAIA,EAF1B5D,CAAAA,EAAIoF,CAAC,EAGd,CAEAD,EAAUpC,EAAMoC,EAAS,GAC7B,CACA,OAAOA,CACX,EAoCA7F,EAAgBkE,2BAA2B,CAAGA,EA0B9ClE,EAAgB6E,4BAA4B,CAAGA,EAmB/C7E,EAAgB+F,qCAAqC,CAHrD,SAA+CC,CAAO,CAAEC,CAAO,EAC3D,OAAO3D,EAAyB0D,EAASC,GAAWA,EAAQrC,CAAC,CAAGoC,EAAQpC,CAAC,CAAG,KAChF,EAiBA5D,EAAgBsF,mBAAmB,CAAGA,EAoBtCtF,EAAgBwF,uBAAuB,CAAGA,EAsB1CxF,EAAgBkG,wBAAwB,CALxC,SAAkChE,CAAK,CAAE4C,CAAO,EAC5C,MAAO,CAACA,EAAQW,IAAI,CAAC,SAAUF,CAAM,EACjC,OAAOD,EAAoBpD,EAAOqD,EACtC,EACJ,EAoBAvF,EAAgB0F,6BAA6B,CAAGA,EAiGhD1F,EAAgBmG,mCAAmC,CAlFnD,SAA6CrB,CAAO,EAChD,IAAIsB,EAAqBV,EAA8BZ,GAAUuB,EACjE,GAAID,EAAmB/D,MAAM,CAAG,EAAG,CAE/B,IAAMiE,EAASxE,EAAkBsE,GAW3BG,EAAaH,AAVnBA,CAAAA,EAAqBA,EAEhBhB,GAAG,CAAC,SAAUC,CAAC,EAEhB,OADAA,EAAEmB,KAAK,CAAG5D,EAAsB0D,EAAQjB,GACjCA,CACX,GAEKoB,IAAI,CAAC,SAAU9F,CAAC,CAAE+F,CAAC,EACpB,OAAOA,EAAEF,KAAK,CAAG7F,EAAE6F,KAAK,AAC5B,EAAC,CACoC,CAACJ,EAAmB/D,MAAM,CAAG,EAAE,CAC9DsE,EAAOP,EACRnE,MAAM,CAAC,SAAU2E,CAAI,CAAErE,CAAE,EAC1B,GAAM,CAAEgE,WAAAA,CAAU,CAAE,CAAGK,EAAMC,EAAW/E,EAAkB,CAACyE,EAAYhE,EAAG,EAGpEuE,EAAMvE,EAAG2C,OAAO,CAGjBS,MAAM,CAAC,SAAUoB,CAAK,EACvB,OAAOR,EAAWrB,OAAO,CAAC8B,OAAO,CAACD,GAAS,EAC/C,GAGK9E,MAAM,CAAC,SAAU6E,CAAG,CAAEC,CAAK,EAC5B,IAAMxB,EAAST,CAAO,CAACiC,EAAM,CAAEE,EAASrE,EAAsB2C,EAAQhD,GAAK2E,EAAStE,EAAsB2C,EAAQgB,GAAaY,EAAYD,EAASD,EAC/IC,CAAAA,EAASD,EAAS,EAAIxE,KAAKqB,EAAE,CAAG,CAAA,EAAI0C,EAAQU,EAASC,EAAY,EAClEC,EAAQ9E,EAAyBuE,EAAU,CAC3C1E,EAAGoD,EAAOpD,CAAC,CAAGoD,EAAO3B,CAAC,CAAGnB,KAAK4E,GAAG,CAACb,GAClCpE,EAAGmD,EAAOnD,CAAC,CAAGmD,EAAO3B,CAAC,CAAGnB,KAAK6E,GAAG,CAACd,EACtC,GACM,CAAE5C,EAAAA,CAAC,CAAE,CAAG2B,EAiBd,OAdI6B,EAAQxD,AAAI,EAAJA,GACRwD,CAAAA,EAAQxD,AAAI,EAAJA,CAAI,EAGZ,CAAA,CAACkD,GAAOA,EAAIM,KAAK,CAAGA,CAAI,GACxBN,CAAAA,EAAM,CACFlD,EAAAA,EACA2D,SAAUH,CAAAA,CAAAA,EAAQxD,CAAAA,EAClBwD,MAAAA,EACAjF,EAAGI,EAAGJ,CAAC,CACPC,EAAGG,EAAGH,CAAC,AACX,CAAA,EAGG0E,CACX,EAAG,MAEH,GAAIA,EAAK,CACL,GAAM,CAAElD,EAAAA,CAAC,CAAE,CAAGkD,EACdF,EAAKD,IAAI,CAACa,IAAI,CAAC,CAAC,IAAK5D,EAAGA,EAAG,EAAGkD,EAAIS,QAAQ,CAAE,EAAGT,EAAI3E,CAAC,CAAE2E,EAAI1E,CAAC,CAAC,EAC5DwE,EAAKL,UAAU,CAAGhE,CACtB,CACA,OAAOqE,CACX,EAAG,CACCL,WAAYA,EACZI,KAAM,EAAE,AACZ,GAAGA,IAAI,AACa,CAAA,IAAhBA,EAAKtE,MAAM,EAGNsE,AAAgB,IAAhBA,EAAKtE,MAAM,GAIhBsE,EAAKc,OAAO,CAAC,CAAC,IAAKlB,EAAWpE,CAAC,CAAEoE,EAAWnE,CAAC,CAAC,EAC9CiE,EAAS,CACLC,OAAAA,EACA5F,EAAGiG,CACP,EAER,CACA,OAAON,CACX,CAEJ,EAAGrG,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,GAMb,IAAM0H,EAA4B1H,EAmF5B2H,EAHR,CACvBC,KAzDJ,SAAc1F,CAAK,CAAE2F,CAAM,EACvB,GAAM,CAAEC,kBAAAA,CAAiB,CAAEC,WAAAA,CAAU,CAAEC,IAAAA,CAAG,CAAEC,SAAAA,CAAQ,CAAE,CAAGJ,EACnDK,EAAY,AAAChG,EAAMiG,MAAM,EAAIjG,EAAMiG,MAAM,CAACC,KAAK,CAACC,WAAW,CAE7D,KAAK,EAEJnG,EAAMiG,MAAM,EACTjG,EAAMiG,MAAM,CAACG,OAAO,CAACJ,SAAS,CAClCK,EAAUrG,EAAMqG,OAAO,CAK3B,GAJAV,EAAOW,OAAO,CAAG,CACb,GAAGX,EAAOW,OAAO,CACjB,MAAStG,EAAMuG,YAAY,EAC/B,EACKvG,EAAMwG,UAAU,GACZH,IAWDrG,EAAMqG,OAAO,CATTA,EADAV,AAAqB,SAArBA,EAAOc,SAAS,CACNV,EAASW,IAAI,GAElBf,AAAqB,UAArBA,EAAOc,SAAS,CACXV,EAASY,KAAK,CAAChB,EAAOiB,QAAQ,EAAI,IACvCC,IAAI,CAAClB,EAAOmB,SAAS,EAAI,CAAC,GAGrBf,CAAQ,CAACJ,EAAOc,SAAS,CAAC,CAACd,EAAOmB,SAAS,EAAI,CAAC,GAG9DT,EAAQU,GAAG,CAACpB,EAAOqB,KAAK,GAExBlB,GACAO,EAAQP,GAAG,CAACA,GAEhBO,EACKQ,IAAI,CAAClB,EAAOW,OAAO,EACnBW,OAAO,CAACrB,EAAmBD,CAAAA,EAAOuB,KAAK,EAAWlB,EAAWH,QAEjE,GAAIQ,EAAS,CACd,IAAMc,EAAU,KACZnH,EAAMqG,OAAO,CAAGA,EAAWA,GAAWA,EAAQc,OAAO,GAC3B,YAAtB,OAAOtB,GACPA,GAER,CAEIhH,CAAAA,OAAOuI,IAAI,CAACxB,GAAmBzF,MAAM,CACrCkG,EAAQY,OAAO,CAACrB,EAAmB,KAAK,EAAG,IAAMuB,KAGjDA,GAER,CACJ,CAQA,EAIA,IAAIE,EAAmIpJ,EAAoB,KACvJqJ,EAAuJrJ,EAAoBI,CAAC,CAACgJ,GAoBjL,GAAM,CAAEE,QAAS,CAAEpI,UAAW,CAAEqI,WAAYC,CAAY,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAElN,CAAEC,SAAAA,CAAQ,CAAE,CAAIlI,IA2OhB,CAAEgC,gBAAAA,CAAe,CAAEO,4BAAAA,CAA2B,CAAE0B,yBAA0BkE,CAAkC,CAAEtE,wBAAAA,CAAuB,CAAEF,oBAAAA,CAAmB,CAAEY,yBAAAA,CAAwB,CAAE,CAAGwB,EAEzL,CAAEpF,yBAA0ByH,CAAkC,CAAE,CAAGvG,EAEnE,CAAEwG,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEJ,SAAUK,CAAkB,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAE,CAAIzI,IAmB/E,SAAS0I,EAAiBC,CAAS,EAE/B,IAAMC,EAAiB,CAAC,EAgCxB,OA/BAD,EAEK3E,MAAM,CAAC,AAAC6E,GAAcA,AAAyB,IAAzBA,EAASC,IAAI,CAACpI,MAAM,EAE1CqI,OAAO,CAAC,AAACF,IACVA,EAASC,IAAI,CAACC,OAAO,CAAC,CAACC,EAAK1H,EAAG8B,KACtBoF,EAASI,CAAc,CAACI,EAAI,GAC7BJ,CAAAA,CAAc,CAACI,EAAI,CAAG,CAClBC,aAAc,EACdC,YAAa,CAAC,CAClB,CAAA,EAEJN,CAAc,CAACI,EAAI,CAAG,CAClBC,aAAc,AAACL,CAAAA,CAAc,CAACI,EAAI,CAACC,YAAY,EAAI,CAAA,EAC/CJ,EAASM,KAAK,CAClBD,YAAa,CACT,GAAIN,CAAc,CAACI,EAAI,CAACE,WAAW,EAAI,CAAC,CAAC,CACzC,CAAC9F,CAAG,CAAC,EAAI9B,EAAE,CAAC,CAAEuH,EAASM,KAAK,AAChC,CACJ,CACJ,EACJ,GACAR,EAEK3E,MAAM,CAACoF,GAEPL,OAAO,CAAC,AAACC,IACV,IAAMK,EAAaT,CAAc,CAACI,EAAIF,IAAI,CAAC,EAAE,CAAC,CAC9CT,EAAOW,EAAKK,EAChB,GAEOV,CACX,CAsBA,SAASW,EAAOC,CAAC,CAAEvK,CAAC,CAAE+F,CAAC,CAAEyE,CAAS,CAAEC,CAAa,EAC7C,IAAMC,EAAKH,EAAEvK,GAAI2K,EAAKJ,EAAExE,GAAI6E,EAAOH,GAAiB,IAAKI,EAAML,GAAa,MACxEM,EAAQ/E,EAAI/F,EAAGwB,EAAGuJ,EAAInL,EAAI,EAC9B,GAAII,GAAK+F,EACL,MAAM,AAAI7C,MAAM,6BAEf,GAAIwH,EAAKC,EAAK,EACf,MAAM,AAAIzH,MAAM,2CAEpB,GAAIwH,AAAO,IAAPA,EACAlJ,EAAIxB,OAEH,GAAI2K,AAAO,IAAPA,EACLnJ,EAAIuE,OAGJ,KAAOnG,KAAOgL,GAAQG,AAAO,IAAPA,GAAYD,EAAQD,GACtCC,EAAQ,AAAC/E,CAAAA,EAAI/F,CAAAA,EAAK,EAId0K,EAFJK,CAAAA,EAAKR,EADL/I,EAAIxB,EAAI8K,EACA,EAEM,EACV9K,EAAIwB,EAGJuE,EAAIvE,EAIhB,OAAOA,CACX,CAIA,SAASwJ,EAAYC,CAAO,EACxB,IAAM7G,EAAM6G,EAAQ3G,KAAK,CAAC,EAAG,IAAK5C,EAAS0C,EAAI1C,MAAM,CAAEgE,EAAS,EAAE,CAAErE,EAAM,CAAC4E,EAAM1E,KAC7E0E,EAAK5E,GAAG,EAAIE,CAAK,CAAC0E,EAAK3D,CAAC,CAAC,CAClB2D,GAEX,IAAK,IAAI3D,EAAI,EAAGA,EAAIZ,EAAQY,IACxBoD,CAAM,CAACpD,EAAE,CAAG8B,EAAI9C,MAAM,CAACD,EAAK,CAAEA,IAAK,EAAGiB,EAAGA,CAAE,GAAGjB,GAAG,CAAGK,EAExD,OAAOgE,CACX,CAiBA,SAASwF,EAAmCxH,CAAE,CAAEC,CAAE,CAAEuB,CAAO,EACvD,IACIiG,EADEC,EAAc1H,EAAKC,EAkBzB,OAhBIuB,GAAW,EAEAkG,EAENpI,EAAgBU,EAAKC,EAAKD,EAAKC,IAAOuB,EAGhC,EAGAoF,EAAO,AAAC9I,GAGR0D,EAFeiE,EAAmCzF,EAAIC,EAAInC,GAGlE,EAAG4J,EAGd,CAqFA,SAAShB,EAAM5I,CAAC,EACZ,OAAO8H,EAAQ9H,EAAEsI,IAAI,GAAKtI,AAAkB,IAAlBA,EAAEsI,IAAI,CAACpI,MAAM,AAC3C,CAKA,SAAS2J,EAAgB7J,CAAC,EACtB,IAAMiD,EAAM,CAAC,EACb,OAAQ+E,EAAShI,IACZ+H,EAAmB/H,EAAE2I,KAAK,GAAK3I,EAAE2I,KAAK,CAAG,IACzCb,EAAQ9H,EAAEsI,IAAI,GAAKtI,EAAEsI,IAAI,CAACpI,MAAM,CAAG,GACpC,CAACF,EAAEsI,IAAI,CAAChF,IAAI,CAAC,SAAUkF,CAAG,EACtB,IAAIsB,EAAU,CAAA,EAOd,MANI,CAAC7G,CAAG,CAACuF,EAAI,EAAIP,EAASO,GACtBvF,CAAG,CAACuF,EAAI,CAAG,CAAA,EAGXsB,EAAU,CAAA,EAEPA,CACX,EACR,CAqIA,SAASC,EAAKC,CAAe,CAAE7B,CAAS,EAGpC,OAAOA,EAAUrI,MAAM,CAAC,SAAUmK,CAAS,CAAE5B,CAAQ,EACjD,IAAI0B,EAAO,EACX,GAAI1B,EAASC,IAAI,CAACpI,MAAM,CAAG,EAAG,CAC1B,IAOMgK,EAAOC,AAPS9B,EAASM,KAAK,CAEdlF,AAlLlC,SAAkCd,CAAO,EACrC,IAAIe,EAAU,EAGd,GAAIf,AAAmB,IAAnBA,EAAQzC,MAAM,CAAQ,CACtB,IAAM2D,EAAUlB,CAAO,CAAC,EAAE,CACpBmB,EAAUnB,CAAO,CAAC,EAAE,CAC1Be,EAAUiE,EAAmC9D,EAAQpC,CAAC,CAAEqC,EAAQrC,CAAC,CAAEmG,EAAmC/D,EAASC,GACnH,CACA,OAAOJ,CACX,EA0KY2E,EAASC,IAAI,CAACrF,GAAG,CAAC,SAAUuF,CAAG,EAC3B,OAAOwB,CAAe,CAACxB,EAAI,AAC/B,IAEAuB,EAAOzJ,KAAKgB,KAAK,CAAC,AAAC4I,EAAOA,EAbhB,MAAA,IAcd,CAEA,OAAOD,EAAYF,CACvB,EAAG,EACP,CA6KA,SAASK,EAAmB5L,CAAC,CAAE+F,CAAC,SAC5B,AAAI,AAA0B,KAAA,IAAnBA,EAAEkE,YAAY,EACrB,AAA0B,KAAA,IAAnBjK,EAAEiK,YAAY,CACdlE,EAAEkE,YAAY,CAAGjK,EAAEiK,YAAY,CAEnC4B,GACX,CAqB6B,IAAMC,EAfjB,CACdC,SAAUlJ,EACVmJ,gBAAiBjF,EACjB2C,iBAAAA,EACAsB,YAAAA,EACAE,mCAAAA,EACAe,cAlbJ,SAAuBC,CAAG,CAAEC,CAAQ,CAAEC,CAAQ,EAC1C,IAAMC,EAASF,EAAS7K,MAAM,CAAC,CAACgL,EAAK1H,IAAW9C,KAAKwK,GAAG,CAAC1H,EAAO3B,CAAC,CAAEqJ,GAAMC,KAEzEC,EAAoBJ,EAASpH,MAAM,CAAC,AAACJ,GAAW,CAACD,EAAoBuH,EAAKtH,IACpE6H,EAAe,SAAUrB,CAAW,CAAEsB,CAAS,EACjD,OAAOpC,EAAO,AAAC9I,IACX,IAAMmL,EAAU,CACZnL,EAAG0K,EAAI1K,CAAC,CAAIkL,EAAYlL,EACxBC,EAAGyK,EAAIzK,CAAC,AACZ,EAIA,MAAO,CAAE2J,CAAAA,EAAc5J,CAAAA,EAAMoL,CAAAA,AAJf/H,EAAwB8H,EAASR,IAC3C5G,EAAyBoH,EAASH,GAGC,EAAIK,OAAOC,SAAS,AAAD,CAC9D,EAAG,EAAG1B,EACV,EAEA,OAAOtJ,AAA8D,EAA9DA,KAAKwK,GAAG,CAACG,EAAaJ,EAAQ,IAAKI,EAAaJ,EAAQ,GACnE,EAiaIU,qBAjZJ,SAA8BxL,CAAK,CAAE4K,CAAQ,CAAEC,CAAQ,EACnD,IAAIY,EAASb,EAAS7K,MAAM,CAAC,CAAC0L,EAAQpI,KAClC,IAAMqI,EAAIrI,EAAO3B,CAAC,CAAGmG,EAAmC7H,EAAOqD,GAC/D,OAAO,AAACqI,GAAKD,EAAUC,EAAID,CAC/B,EAAGH,OAAOC,SAAS,EAKnB,OAJSV,EAAS9K,MAAM,CAAC,CAAC0L,EAAQpI,KAC9B,IAAMqI,EAAI7D,EAAmC7H,EAAOqD,GAAUA,EAAO3B,CAAC,CACtE,OAAO,AAACgK,GAAKD,EAAUC,EAAID,CAC/B,EAAGA,EAEP,EAwYI5C,MAAAA,EACA8C,iBA1UJ,SAA0BvD,CAAS,EAC/B,IAAMwD,EAAiB,EAAE,CAAEC,EAAmB,CAAC,EAE/CzD,EACK3E,MAAM,CAAC,AAAC6E,GAAcA,AAAyB,IAAzBA,EAASC,IAAI,CAACpI,MAAM,EAC1CqI,OAAO,CAAC,AAACF,IACVuD,CAAgB,CAACvD,EAASC,IAAI,CAAC,EAAE,CAAC,CAAGD,EAASjF,MAAM,CAAG,CACnDpD,EAAGqL,OAAOC,SAAS,CACnBrL,EAAGoL,OAAOC,SAAS,CACnB7J,EAAGnB,KAAKC,IAAI,CAAC8H,EAASM,KAAK,CAAGrI,KAAKqB,EAAE,CACzC,CACJ,GAUA,IAAMkK,EAAc,CAACrD,EAAKsD,KACtB,IAAM1I,EAASoF,EAAIpF,MAAM,CACrBA,IACAA,EAAOpD,CAAC,CAAG8L,EAAY9L,CAAC,CACxBoD,EAAOnD,CAAC,CAAG6L,EAAY7L,CAAC,EAE5B0L,EAAetG,IAAI,CAACmD,EACxB,EAEAN,EAAiBC,GAEjB,IAAM4D,EAAkB5D,EACnB3E,MAAM,CAACoF,GACPtE,IAAI,CAAC8F,GAEVyB,EAAYE,EAAgBC,KAAK,GAAI,CAAEhM,EAAG,EAAGC,EAAG,CAAE,GAClD,IAAMgM,EAAuB9D,EAAU3E,MAAM,CAAC,AAACxD,GAAOA,AAAkB,IAAlBA,EAAEsI,IAAI,CAACpI,MAAM,EAEnE,IAAK,IAAMsI,KAAOuD,EAAiB,CAC/B,IAAM3I,EAASoF,EAAIpF,MAAM,CACzB,GAAI,CAACA,EACD,SAEJ,IAAMyH,EAASzH,EAAO3B,CAAC,CAAEiH,EAAcF,EAAIE,WAAW,CAwDtDmD,EAAYrD,EAAK0D,AAvDIP,EAAe7L,MAAM,CAAC,CAACqM,EAAMC,EAAetL,KAC7D,IAAMuL,EAAmBD,EAAchJ,MAAM,CAC7C,GAAI,CAACiJ,GAAoB,CAAC3D,EACtB,OAAOyD,EAEX,IAAMzI,EAAUgF,CAAW,CAAC0D,EAAc9D,IAAI,CAAC,EAAE,CAAC,CAG5CqB,EAAWD,EAAmCmB,EAAQwB,EAAiB5K,CAAC,CAAEiC,GAG5E4I,EAAsB,CACtB,CAAEtM,EAAGqM,EAAiBrM,CAAC,CAAG2J,EAAU1J,EAAGoM,EAAiBpM,CAAC,AAAC,EAC1D,CAAED,EAAGqM,EAAiBrM,CAAC,CAAG2J,EAAU1J,EAAGoM,EAAiBpM,CAAC,AAAC,EAC1D,CAAED,EAAGqM,EAAiBrM,CAAC,CAAEC,EAAGoM,EAAiBpM,CAAC,CAAG0J,CAAS,EAC1D,CAAE3J,EAAGqM,EAAiBrM,CAAC,CAAEC,EAAGoM,EAAiBpM,CAAC,CAAG0J,CAAS,EAC7D,CAGD,IAAK,IAAM4C,KAAkBZ,EAAe7I,KAAK,CAAChC,EAAI,GAAI,CACtD,IAAM0L,EAAoBD,EAAenJ,MAAM,CAAEqJ,EAAW/D,CAAW,CAAC6D,EAAejE,IAAI,CAAC,EAAE,CAAC,CAC/F,GAAI,CAACkE,EACD,SAEJ,IAAME,EAAYhD,EAAmCmB,EAAQ2B,EAAkB/K,CAAC,CAAEgL,GAElFH,EAAsBA,EAAoBtJ,MAAM,CAACjB,EAA4B,CACzE/B,EAAGqM,EAAiBrM,CAAC,CACrBC,EAAGoM,EAAiBpM,CAAC,CACrBwB,EAAGkI,CACP,EAAG,CACC3J,EAAGwM,EAAkBxM,CAAC,CACtBC,EAAGuM,EAAkBvM,CAAC,CACtBwB,EAAGiL,CACP,GACJ,CAEA,IAAK,IAAMZ,KAAeQ,EAAqB,CAC3ClJ,EAAOpD,CAAC,CAAG8L,EAAY9L,CAAC,CACxBoD,EAAOnD,CAAC,CAAG6L,EAAY7L,CAAC,CAExB,IAAM0M,EAAc5C,EAAK6B,EAAkBK,GAEvCU,EAAcR,EAAKpC,IAAI,GACvBoC,EAAKpC,IAAI,CAAG4C,EACZR,EAAKL,WAAW,CAAGA,EAE3B,CAEA,OAAOK,CACX,EAAG,CACCpC,KAAMsB,OAAOC,SAAS,CACtBQ,YAAa,KAAK,CACtB,GAE8BA,WAAW,CAC7C,CAEA,OAAOF,CACX,EAmOI7B,KAAAA,EACA6C,WAvLJ,SAAoBC,CAAE,CAAEC,CAAO,EAC3B,IAA2BC,EAAW,SAAUvO,CAAC,CAAE+F,CAAC,EAChD,OAAO/F,EAAEwO,EAAE,CAAGzI,EAAEyI,EAAE,AACtB,EAQMC,EAAc,CAACC,EAASC,EAAIC,EAASC,IAAOF,EAAGlK,GAAG,CAAC,CAACjD,EAAGc,IAAMoM,EAAUlN,EAAIoN,EAAUC,CAAE,CAACvM,EAAE,EAkB1FwM,EAAgB,CAAC7D,EAAS1J,KAC5BA,EAAMiN,EAAE,CAAGH,EAAG9M,GACd0J,CAAO,CAACA,EAAQvJ,MAAM,CAAG,EAAE,CAAGH,EACvB0J,GAEL8D,EAAgB,AAAC9D,IACnB,IAAM0C,EAAO1C,CAAO,CAAC,EAAE,CACvB,OAAOA,EAAQxG,GAAG,CAAC,AAAClD,IAChB,IAAMmD,EAAI+J,EAAY,GAAad,EA9BjC,GA8BgDpM,GAElD,OADAmD,EAAE8J,EAAE,CAAGH,EAAG3J,GACHA,CACX,EACJ,EACMsK,EAAW,CAACC,EAAUC,EAAOlP,EAAG+F,KAClC,IAAMxE,EAAQkN,EAAYzO,EAAGiP,EAAUlJ,EAAGmJ,GAE1C,OADA3N,EAAMiN,EAAE,CAAGH,EAAG9M,GACPA,CACX,EAEI0J,EAAUkE,AAjCK,CAAA,AAACb,IAChB,IAAM1O,EAAI0O,EAAQ5M,MAAM,CAAEuJ,EAAU,AAAImE,MAAMxP,EAAI,EAElDqL,CAAAA,CAAO,CAAC,EAAE,CAAGqD,EACbrD,CAAO,CAAC,EAAE,CAACuD,EAAE,CAAGH,EAAGC,GAEnB,IAAK,IAAIhM,EAAI,EAAGA,EAAI1C,EAAG,EAAE0C,EAAG,CACxB,IAAMf,EAAQ+M,EAAQhK,KAAK,EAC3B/C,CAAAA,CAAK,CAACe,EAAE,CAAGf,CAAK,CAACe,EAAE,CAAGf,AAAW,KAAXA,CAAK,CAACe,EAAE,CAAU,KACxCf,EAAMiN,EAAE,CAAGH,EAAG9M,GACd0J,CAAO,CAAC3I,EAAI,EAAE,CAAGf,CACrB,CACA,OAAO0J,CACX,CAAA,EAoByBqD,GAEzB,IAAK,IAAIhM,EAAI,EAAGA,EAjDM,IAiDaA,IAAK,CAEpC2I,EAAQnF,IAAI,CAACyI,GAEb,IAAMW,EAAQjE,CAAO,CAACA,EAAQvJ,MAAM,CAAG,EAAE,CACnCuN,EAAWjE,EAAYC,GAEvBoE,EAAYL,EAASC,EAAUC,EAAO,EAAU,IACtD,GAAIG,EAAUb,EAAE,CAAGvD,CAAO,CAAC,EAAE,CAACuD,EAAE,CAAE,CAE9B,IAAMc,EAAWN,EAASC,EAAUC,EAAO,EAAU,IACrDjE,EAAU6D,EAAc7D,EAAS,AAACqE,EAASd,EAAE,CAAGa,EAAUb,EAAE,CAAIc,EAAWD,EAC/E,MACK,GAAIA,EAAUb,EAAE,EAAIvD,CAAO,CAACA,EAAQvJ,MAAM,CAAG,EAAE,CAAC8M,EAAE,CAAE,CAGrD,IAAIe,EAMItE,EALJoE,EAAUb,EAAE,CAAGU,EAAMV,EAAE,CAInBe,AADJA,CAAAA,EAAaP,EAASC,EAAUC,EAAO,GAAU,GAAK,EACvCV,EAAE,CAAGU,EAAMV,EAAE,CACdM,EAAc7D,EAASsE,GAGvBR,EAAc9D,GAMxBsE,AADJA,CAAAA,EAAaP,EAASC,EAAUC,EAAO,IA1E3CM,IA0E2D,EACxChB,EAAE,CAAGa,EAAUb,EAAE,CAClBM,EAAc7D,EAASsE,GAGvBR,EAAc9D,EAGpC,MAEIA,EAAU6D,EAAc7D,EAASoE,EAEzC,CACA,OAAOpE,CAAO,CAAC,EAAE,AACrB,EA0FIwE,gBAhFJ,SAAyBxJ,CAAI,CAAEyJ,CAAQ,EACnC,IAAM3P,EAAIuJ,EAAQrD,GAAQA,EAAO,EAAE,CAC7B0J,EAAY5P,EACbuB,MAAM,CAAC,SAAU8C,CAAG,CAAE5C,CAAC,MA3QZA,EAgRZ,OAHIA,EAAEsI,IAAI,EA5QNuB,EADQ7J,EA6QaA,IA5QC4I,EAAM5I,IAAMA,EAAE2I,KAAK,CAAG,GA4QjB/F,AAA2B,KAA3BA,EAAIiC,OAAO,CAAC7E,EAAEsI,IAAI,CAAC,EAAE,GAChD1F,EAAIyC,IAAI,CAACrF,EAAEsI,IAAI,CAAC,EAAE,EAEf1F,CACX,EAAG,EAAE,EACA0B,IAAI,GACH8J,EAAoB7P,EAAEuB,MAAM,CAAC,SAAUsO,CAAiB,CAAE/F,CAAQ,EAWpE,OAVIA,EAASC,IAAI,EACbuB,EAAgBxB,IAChB,CAACA,EAASC,IAAI,CAAChF,IAAI,CAAC,SAAUkF,CAAG,EAC7B,OAAO2F,AAA2B,KAA3BA,EAAUtJ,OAAO,CAAC2D,EAC7B,IACA4F,CAAAA,CAAiB,CAAC/F,EAASC,IAAI,CAAChE,IAAI,GAAG+J,IAAI,CAACH,GAAU,CAAG,CACrD5F,KAAMD,EAASC,IAAI,CACnBK,MAAON,EAASM,KAAK,EAAI,CAC7B,CAAA,EAEGyF,CACX,EAAG,CAAC,GAiBJ,OAhBAD,EAAUrO,MAAM,CAAC,SAAUwO,CAAY,CAAE9F,CAAG,CAAE1H,CAAC,CAAE8B,CAAG,EAKhD,OAHA2L,AADkB3L,EAAIE,KAAK,CAAChC,EAAI,GACtByH,OAAO,CAAC,SAAUiG,CAAI,EAC5BF,EAAajJ,IAAI,CAACmD,EAAM0F,EAAWM,EACvC,GACOF,CACX,EAAG,EAAE,EAAE/F,OAAO,CAAC,SAAUkG,CAAW,EAChC,GAAI,CAACL,CAAiB,CAACK,EAAY,CAAE,CACjC,IAAMzP,EAAM,CACRsJ,KAAMmG,EAAYC,KAAK,CAACR,GACxBvF,MAAO,CACX,CACAyF,CAAAA,CAAiB,CAACK,EAAY,CAAGzP,CACrC,CACJ,GAEOJ,OACFuI,IAAI,CAACiH,GACLnL,GAAG,CAAC,SAAU0L,CAAE,EACjB,OAAOP,CAAiB,CAACO,EAAG,AAChC,EACJ,EAoCIvE,mBAAAA,CACJ,EAsBM,CAAEwE,WAAAA,CAAU,CAAE,CAAIpP,IAElB,CAAEqP,MAAOC,CAAK,CAAE,CAAIpP,IAEpB,CAAEsE,oCAAAA,CAAmC,CAAET,8BAAAA,CAA6B,CAAEK,sCAAAA,CAAqC,CAAEP,wBAAyB0L,CAAkC,CAAEhL,yBAA0BiL,CAAmC,CAAE,CAAGzJ,EAG5O,CAAE5F,kBAAmBsP,CAA4B,CAAE,CAAG5N,EAEtD,CAAEiG,QAAS4H,CAAa,CAAE,CAAG,AAAC7H,IAA2II,WAAW,CAKpL,CAAE0H,SAAAA,CAAQ,CAAEtH,OAAQuH,EAAiB,CAAEtH,QAASuH,EAAkB,CAAE3H,SAAU4H,EAAmB,CAAEtH,SAAUuH,EAAmB,CAAEC,MAAAA,EAAK,CAAE,CAAIhQ,GAanJ,OAAMiQ,WAAmBP,EAoBrB,OAAOQ,iBAAiB/E,CAAQ,CAAEC,CAAQ,CAAE,CAExC,IAAIuB,EAAOxB,EAAS7K,MAAM,CAAC,CAACqM,EAAM/I,KAC9B,IAAM7E,EAAI6E,EAAO3B,CAAC,CAAG,EAGrB,MAAO,CACH,CAAEzB,EAAGoD,EAAOpD,CAAC,CAAEC,EAAGmD,EAAOnD,CAAC,AAAC,EAC3B,CAAED,EAAGoD,EAAOpD,CAAC,CAAGzB,EAAG0B,EAAGmD,EAAOnD,CAAC,AAAC,EAC/B,CAAED,EAAGoD,EAAOpD,CAAC,CAAGzB,EAAG0B,EAAGmD,EAAOnD,CAAC,AAAC,EAC/B,CAAED,EAAGoD,EAAOpD,CAAC,CAAEC,EAAGmD,EAAOnD,CAAC,CAAG1B,CAAE,EAC/B,CAAEyB,EAAGoD,EAAOpD,CAAC,CAAEC,EAAGmD,EAAOnD,CAAC,CAAG1B,CAAE,EAClC,CAGIuB,MAAM,CAAC,CAACqM,EAAMpM,KACf,IAAMyL,EAASlB,EAAeiB,oBAAoB,CAACxL,EAAO4K,EAAUC,GAOpE,OAJIuB,EAAKX,MAAM,CAAGA,IACdW,EAAKpM,KAAK,CAAGA,EACboM,EAAKX,MAAM,CAAGA,GAEXW,CACX,EAAGA,EACP,EAAG,CACCpM,MAAO,KAAK,EACZyL,OAAQ,CAACH,OAAOC,SAAS,AAC7B,GAAGvL,KAAK,CAEF4P,EAAUrF,EAAesC,UAAU,CAAC,AAAC1J,GAAM,CAAEoH,EAAeiB,oBAAoB,CAAC,CAAEvL,EAAGkD,CAAC,CAAC,EAAE,CAAEjD,EAAGiD,CAAC,CAAC,EAAE,AAAC,EAAGyH,EAAUC,GAAY,CAC/HuB,EAAKnM,CAAC,CACNmM,EAAKlM,CAAC,CACT,EAqBD,OAfM8O,EAJN5C,EAAO,CACHnM,EAAG2P,CAAO,CAAC,EAAE,CACb1P,EAAG0P,CAAO,CAAC,EAAE,AACjB,EAC+ChF,IAC3CqE,EAAoC7C,EAAMvB,KAItCuB,EADAxB,EAASzK,MAAM,CAAG,EACX+O,EAA6B1L,EAA8BoH,IAG3D,CACH3K,EAAG2K,CAAQ,CAAC,EAAE,CAAC3K,CAAC,CAChBC,EAAG0K,CAAQ,CAAC,EAAE,CAAC1K,CAAC,AACpB,GAIDkM,CACX,CAYA,OAAOyD,eAAevH,CAAQ,CAAEwH,CAAY,CAAE,CAC1C,IAAMvH,EAAOD,EAASC,IAAI,CAEpB7D,EAAOoL,EAAa/P,MAAM,CAAC,CAAC2E,EAAM+D,KAGpC,IAAMsH,EAAaxH,EAAKzD,OAAO,CAAC2D,EAAIF,IAAI,CAAC,EAAE,EAAI,GAM/C,OAHIE,EAAIpF,MAAM,EACVqB,CAAI,CAHSqL,EAAa,WAAa,WAGzB,CAACzK,IAAI,CAACmD,EAAIpF,MAAM,EAE3BqB,CACX,EAAG,CACCkG,SAAU,EAAE,CACZC,SAAU,EAAE,AAChB,EAGAnG,CAAAA,EAAKmG,QAAQ,CAAGnG,EAAKmG,QAAQ,CAACpH,MAAM,CAAC,AAACuM,GAAmBtL,EAAKkG,QAAQ,CAACrH,IAAI,CAAC,AAAC0M,GAAmB,CAACpM,EAAsCmM,EAAgBC,KAEvJ,IAAMC,EAAWR,GAAWC,gBAAgB,CAACjL,EAAKkG,QAAQ,CAAElG,EAAKmG,QAAQ,EAEnE3F,EAAQqF,EAAeG,aAAa,CAACwF,EAAUxL,EAAKkG,QAAQ,CAAElG,EAAKmG,QAAQ,EACjF,MAAO,CACHqF,SAAAA,EACAhL,MAAAA,CACJ,CACJ,CAaA,OAAOiL,OAAO/H,CAAS,CAAE,CACrB,IAAMgI,EAAiB,CAAC,EAClBC,EAAuB,CAAC,EAE9B,GAAIjI,EAAUjI,MAAM,CAAG,EAAG,CACtB,IAAM0L,EAAmBtB,EAAeoB,gBAAgB,CAACvD,GACnD0H,EAAe1H,EAAU3E,MAAM,CAAC8G,EAAe1B,KAAK,EAC1D,IAAK,IAAMP,KAAYF,EAAW,CAC9B,IAAMG,EAAOD,EAASC,IAAI,CACpBqG,EAAKrG,EAAK+F,IAAI,GAEdgC,EAAQ/F,EAAe1B,KAAK,CAACP,GAC/BuD,CAAgB,CAAC+C,EAAG,CACpB3K,EAAoCsE,EAAKrF,GAAG,CAAC,AAACuF,GAAQoD,CAAgB,CAACpD,EAAI,GAE3E6H,IACAF,CAAc,CAACxB,EAAG,CAAG0B,EACrBD,CAAoB,CAACzB,EAAG,CAAGc,GAAWG,cAAc,CAACvH,EAAUwH,GAEvE,CACJ,CACA,MAAO,CAAEM,eAAAA,EAAgBC,qBAAAA,CAAqB,CAClD,CAeA,OAAOE,SAASC,CAAW,CAAEC,CAAY,CAAEC,CAAK,CAAE,CAC9C,IAAMC,EAASD,EAAME,MAAM,CAAGF,EAAMG,GAAG,CACvC3L,EAAQwL,EAAMI,KAAK,CAAGJ,EAAMK,IAAI,CAAyGC,EAAU,AAACN,CAAAA,EAAMI,KAAK,CAAGJ,EAAMK,IAAI,AAAD,EAAK,EAAGE,EAAU,AAACP,CAAAA,EAAMG,GAAG,CAAGH,EAAME,MAAM,AAAD,EAAK,EAAGM,EAAQ3Q,KAAKwK,GAAG,CAAlM7F,EAAQ,EAAI,EAAIA,EAAQsL,EAAc,EAAYG,EAAS,EAAI,EAAIA,EAASF,EAAe,GACtI,MAAO,CACHS,MAAOA,EACPC,QAASX,EAAc,EAAIQ,EAAUE,EACrCE,QAASX,EAAe,EAAIQ,EAAUC,CAC1C,CACJ,CAcA,OAAOG,sBAAsBX,CAAK,CAAErN,CAAM,CAAE,CACxC,IAAM0N,EAAO1N,EAAOpD,CAAC,CAAGoD,EAAO3B,CAAC,CAAEoP,EAAQzN,EAAOpD,CAAC,CAAGoD,EAAO3B,CAAC,CAAEkP,EAASvN,EAAOnD,CAAC,CAAGmD,EAAO3B,CAAC,CAAEmP,EAAMxN,EAAOnD,CAAC,CAAGmD,EAAO3B,CAAC,CActH,MAZI,CAAA,CAAC6N,GAAoBmB,EAAMK,IAAI,GAAKL,EAAMK,IAAI,CAAGA,CAAG,GACpDL,CAAAA,EAAMK,IAAI,CAAGA,CAAG,EAEhB,CAAA,CAACxB,GAAoBmB,EAAMI,KAAK,GAAKJ,EAAMI,KAAK,CAAGA,CAAI,GACvDJ,CAAAA,EAAMI,KAAK,CAAGA,CAAI,EAElB,CAAA,CAACvB,GAAoBmB,EAAMG,GAAG,GAAKH,EAAMG,GAAG,CAAGA,CAAE,GACjDH,CAAAA,EAAMG,GAAG,CAAGA,CAAE,EAEd,CAAA,CAACtB,GAAoBmB,EAAME,MAAM,GAAKF,EAAME,MAAM,CAAGA,CAAK,GAC1DF,CAAAA,EAAME,MAAM,CAAGA,CAAK,EAEjBF,CACX,CAOAzJ,QAAQqK,CAAI,CAAE,CACV,GAAI,CAACA,EAAM,CACP,IAAqBC,EAAc1C,EAAW5I,AAA/B,IAAI,CAAkCG,OAAO,CAACJ,SAAS,EACtE,IAAK,IAAMhG,KAASiG,AADL,IAAI,CACQpG,MAAM,CAAE,CAC/B,IAAM2R,EAAOxR,EAAM8G,SAAS,CAC5B,GAAI9G,EAAMqG,OAAO,EAAImL,EAAM,CACvB,IAAM3K,EAAO,CAAC,EAAGI,EAAU,CAAC,CACxBuK,CAAAA,EAAKhT,CAAC,CAENqI,EAAK4K,OAAO,CAAG,MAIf5K,EAAKnF,CAAC,CAAG,EACTuF,EAAQvF,CAAC,CAAG8P,EAAK9P,CAAC,EAEtB1B,EAAMqG,OAAO,CACRQ,IAAI,CAACA,GACLI,OAAO,CAACA,EAASsK,GAGlBC,EAAKhT,CAAC,EACNkT,WAAW,KACP1R,GAAOqG,SAASY,QAAQ,CACpBwK,QAAS,CACb,EACJ,EAAGF,EAAYI,QAAQ,CAE/B,CACJ,CACJ,CACJ,CAKAC,YAAa,CACT,IAEA1L,EAAQD,AAFO,IAAI,CAEJC,KAAK,CAAEc,EAAQf,AAFf,IAAI,CAEkBe,KAAK,CAAEnH,EAASoG,AAFtC,IAAI,CAEyCpG,MAAM,EAAI,EAAE,CAExEkG,EAAWG,EAAMH,QAAQ,CAEzB,IAAK,IAAM/F,KAASH,EAAQ,CACxB,IAAMyG,EAAU,CACZuL,OAAQvC,GAAmBtP,EAAMuI,IAAI,EAAIvI,EAAMuI,IAAI,CAACpI,MAAM,CAAG,CACjE,EAAG2G,EAAY9G,EAAM8G,SAAS,AAEzBZ,CAAAA,EAAM4L,UAAU,EACjBzC,GAAkB/I,EAASL,AAZpB,IAAI,CAYuB8L,YAAY,CAAC/R,EAAOA,EAAMgS,KAAK,GAGrEvM,EAA0BC,IAAI,CAAC1F,EAAO,CAClCkH,MAAO,CAAClH,EAAMqG,OAAO,CACrBT,kBAAmBkB,EACnBR,QAASA,EACTU,MAAOA,EACPjB,SAAUA,EACVU,UAAWK,GAAWtI,EAAI,OAAS,QACvC,EACJ,CACJ,CACA8S,MAAO,CACHnC,EAAchQ,SAAS,CAACmS,IAAI,CAACW,KAAK,CAAC,IAAI,CAAEC,WAEzC,OAAO,IAAI,CAACT,OAAO,AACvB,CAYAM,aAAa/R,CAAK,CAAEgS,CAAK,CAAE,CACvB,IAAqBG,EAAgBlM,AAAtB,IAAI,CAAyBG,OAAO,EAAI,CAAC,EAAGgM,EAAepS,GAAOoG,SAAW,CAAC,EAAGiM,EAAe,AAACL,GAASG,EAAcG,MAAM,CAACN,EAAM,EAAK,CAAC,EAAG5L,EAAUqJ,GAAM0C,EAAe,CAAEpD,MAAO/O,GAAO+O,KAAM,EAAGqD,EAAcC,GAEnO,MAAO,CACH,KAAQtD,EAAM3I,EAAQ2I,KAAK,EACtBwD,QAAQ,CAACnM,EAAQoM,UAAU,EAC3BxT,GAAG,GAERyS,QAASrL,EAAQqL,OAAO,CACxB,OAAUrL,EAAQqM,WAAW,CAC7B,eAAgBrM,EAAQsM,WAAW,CACnC,UAAatM,EAAQuM,eAAe,AACxC,CACJ,CACAC,WAAY,CACR,IAAM1M,EAAQ,IAAI,CAACA,KAAK,AACxB,CAAA,IAAI,CAAC2M,SAAS,CAACC,QAAQ,CAAG,IAAI,CAACD,SAAS,CACxC,IAAI,CAACE,cAAc,GAEnB,IAAM3K,EAAYmC,EAAe2D,eAAe,CAAC,IAAI,CAAC9H,OAAO,CAAC1B,IAAI,CAAEgL,GAAWvB,QAAQ,EAEjF,CAAEiC,eAAAA,CAAc,CAAEC,qBAAAA,CAAoB,CAAE,CAAGX,GAAWS,MAAM,CAAC/H,GAE7DsI,EAAQ7R,OAAOuI,IAAI,CAACgJ,GACrB3M,MAAM,CAAC,AAAC9E,IACT,IAAM2R,EAAQF,CAAc,CAACzR,EAAI,CACjC,OAAO2R,GAASf,GAAoBe,EAAM5O,CAAC,CAC/C,GACK3B,MAAM,CAAC,CAAC2Q,EAAO/R,IAAQ+Q,GAAW2B,qBAAqB,CAACX,EAAON,CAAc,CAACzR,EAAI,EAAG,CACtFkS,IAAK,EACLD,OAAQ,EACRG,KAAM,EACND,MAAO,CACX,GAAIkC,EAAUtD,GAAWa,QAAQ,CAACrK,EAAM+M,SAAS,CAAE/M,EAAMgN,UAAU,CAAExC,GAAQQ,EAAQ8B,EAAQ9B,KAAK,CAAEC,EAAU6B,EAAQ7B,OAAO,CAAEC,EAAU4B,EAAQ5B,OAAO,CAExJ,IAAK,IAAMpR,KAAS,IAAI,CAACH,MAAM,CAAE,CAC7B,IAAM0I,EAAO+G,GAAmBtP,EAAMuI,IAAI,EAAIvI,EAAMuI,IAAI,CAAG,EAAE,CAAEqG,EAAKrG,EAAK+F,IAAI,GAAIgC,EAAQF,CAAc,CAACxB,EAAG,CAAEuE,EAAkB9C,CAAoB,CAACzB,EAAG,EAAI,CAAC,EAAGwE,EAAYpT,EAAMoG,OAAO,EAAEiN,WACtLvM,EAAWwM,EAAiBH,EAAgBjO,KAAK,CAAEqO,EAAoBJ,EAAgBjD,QAAQ,CACnG,GAAII,EAAO,CACP,GAAIA,EAAM5O,CAAC,CACPoF,EAAY,CACR7G,EAAGkR,EAAUb,EAAMrQ,CAAC,CAAGiR,EACvBhR,EAAGkR,EAAUd,EAAMpQ,CAAC,CAAGgR,EACvBxP,EAAG4O,EAAM5O,CAAC,CAAGwP,CACjB,OAEC,GAAIZ,EAAM9R,CAAC,CAAE,CACd,IAAMA,EAAI8R,EAAM9R,CAAC,CACjBA,EAAEgK,OAAO,CAAC,AAACgL,IACHA,AAAW,MAAXA,CAAG,CAAC,EAAE,EACNA,CAAG,CAAC,EAAE,CAAGrC,EAAUqC,CAAG,CAAC,EAAE,CAAGtC,EAC5BsC,CAAG,CAAC,EAAE,CAAGpC,EAAUoC,CAAG,CAAC,EAAE,CAAGtC,GAEZ,MAAXsC,CAAG,CAAC,EAAE,GACXA,CAAG,CAAC,EAAE,CAAGA,CAAG,CAAC,EAAE,CAAGtC,EAClBsC,CAAG,CAAC,EAAE,CAAGA,CAAG,CAAC,EAAE,CAAGtC,EAClBsC,CAAG,CAAC,EAAE,CAAGrC,EAAUqC,CAAG,CAAC,EAAE,CAAGtC,EAC5BsC,CAAG,CAAC,EAAE,CAAGpC,EAAUoC,CAAG,CAAC,EAAE,CAAGtC,EAEpC,GACApK,EAAY,CAAEtI,EAAAA,CAAE,CACpB,CAEI+U,GACAA,EAAkBtT,CAAC,CAAGkR,EAAUoC,EAAkBtT,CAAC,CAAGiR,EACtDqC,EAAkBrT,CAAC,CAAGkR,EAAUmC,EAAkBrT,CAAC,CAAGgR,GAGtDqC,EAAoB,CAAC,EAErBhE,GAAoB+D,IACpBA,CAAAA,EAAiB/S,KAAKgB,KAAK,CAAC+R,EAAiBpC,EAAK,CAE1D,CACAlR,EAAM8G,SAAS,CAAGA,EAEdyM,GAAqBzM,IACrB9G,EAAMyT,KAAK,CAAGF,EAAkBtT,CAAC,CACjCD,EAAM0T,KAAK,CAAGH,EAAkBrT,CAAC,EAGjCoT,GAAkBxM,GAClB9G,CAAAA,EAAMoT,SAAS,CAAG3D,GAAM,CAAA,EAAM,CAC1BkE,MAAO,CACHzO,MAAOoO,CACX,CACJ,EAAG9D,GAAoB4D,EAAW,CAAA,GAAQA,EAAY,KAAK,EAAC,EAGhEpT,EAAM4T,IAAI,CAAG5T,EAAMoG,OAAO,CAACwN,IAAI,EAAIrL,EAAK+F,IAAI,CAAC,IACjD,CACJ,CACJ,CAMAoB,GAAWvB,QAAQ,CAAG,mBACtBuB,GAAWmE,cAAc,CAAGpE,GAAMN,EAAc0E,cAAc,CA7rCnC,CACvBpB,YAAa,UACbE,gBAAiB,QACjBD,YAAa,EACbH,SAAU,EACVuB,KAAM,CAAA,EACNC,aAAc,CAAA,EACdV,WAAY,CACRW,QAAS,CAAA,EACTC,cAAe,SACfC,UAAW,WACP,OAAO,IAAI,CAAClU,KAAK,CAAC4T,IAAI,AAC1B,CACJ,EAMAO,oBAAqB,CAAA,EAKrBC,OAAQ,CAAA,EACR3C,QAAS,IACT4C,aAAc,CAAA,EAMdC,WAAY,QACZhC,OAAQ,CAIJiC,MAAO,CACH9C,QAAS,EACTgB,YAAa,SACjB,EAIA+B,OAAQ,CACJzF,MAAO,UACP0D,YAAa,UACbzM,UAAW,CAAA,CACf,EACAyO,SAAU,CACNhD,QAAS,IACb,CACJ,EACAiD,QAAS,CACLC,YAAa,6BACjB,EACAC,aAAc,WAClB,GAqoCAvF,GAAkBK,GAAWvQ,SAAS,CAAE,CACpC0V,UAAW,EAAE,CACbC,YAAa,CAAA,EACbC,YAAa,CAAA,EACbC,cAAe,CAAC,QAAQ,CACxBxN,WAzwCJ,cAAwBC,EAMpB4D,SAAU,CACN,OAAO1D,EAAS,IAAI,CAACiB,KAAK,CAC9B,CACApC,YAAa,CAET,MAAO,CAAC,CAAC,IAAI,CAACM,SAAS,AAC3B,CACJ,EA6vCImO,MAAO1K,CACX,GAEA6E,EAASM,GAAY,kBAAmB,SAAUwF,CAAC,EAC/C,IAA2B5C,EAASlM,AAApB8O,EAAE9O,OAAO,CAAmBkM,MAAM,EAAI,CAAC,EACvD,GAAI,IAAI,CAAC6C,EAAE,CAAC,QAER,IAAK,IAAMnD,KAASnT,OAAOuI,IAAI,CAACkL,GAC5BA,CAAM,CAACN,EAAM,CAACoD,IAAI,CAAG,CAAA,CAGjC,GACA9N,IAA0I+N,kBAAkB,CAAC,OAAQ3F,IAaxI,IAAMnQ,GAAaE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}