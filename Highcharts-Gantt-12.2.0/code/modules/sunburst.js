!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Templating,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.SVGElement,t._Highcharts.Series):"function"==typeof define&&define.amd?define("highcharts/modules/sunburst",["highcharts/highcharts"],function(t){return e(t,t.Templating,t.Color,t.SeriesRegistry,t.SVGElement,t.Series)}):"object"==typeof exports?exports["highcharts/modules/sunburst"]=e(t._Highcharts,t._Highcharts.Templating,t._Highcharts.Color,t._Highcharts.SeriesRegistry,t._Highcharts.SVGElement,t._Highcharts.Series):t.Highcharts=e(t.Highcharts,t.Highcharts.Templating,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.SVGElement,t.Highcharts.Series)}("undefined"==typeof window?this:window,(t,e,i,s,o,r)=>(()=>{"use strict";var a,l,n,h={28:t=>{t.exports=o},512:t=>{t.exports=s},620:t=>{t.exports=i},820:t=>{t.exports=r},944:e=>{e.exports=t},984:t=>{t.exports=e}},d={};function p(t){var e=d[t];if(void 0!==e)return e.exports;var i=d[t]={exports:{}};return h[t](i,i.exports,p),i.exports}p.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return p.d(e,{a:e}),e},p.d=(t,e)=>{for(var i in e)p.o(e,i)&&!p.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},p.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var u={};p.d(u,{default:()=>eS});var c=p(944),g=p.n(c);let v={lang:{mainBreadcrumb:"Main"},options:{buttonTheme:{fill:"none",height:18,padding:2,"stroke-width":0,zIndex:7,states:{select:{fill:"none"}},style:{color:"#334eff"}},buttonSpacing:5,floating:!1,format:void 0,relativeTo:"plotBox",rtl:!1,position:{align:"left",verticalAlign:"top",x:0,y:void 0},separator:{text:"/",style:{color:"#666666",fontSize:"0.8em"}},showFullPath:!0,style:{},useHTML:!1,zIndex:7}};var f=p(984);let{format:b}=p.n(f)(),{composed:m}=g(),{addEvent:x,defined:y,extend:L,fireEvent:P,isString:T,merge:w,objectEach:A,pick:R,pushUnique:S}=g();function O(){if(this.breadcrumbs){let t=this.resetZoomButton&&this.resetZoomButton.getBBox(),e=this.breadcrumbs.options;t&&"right"===e.position.align&&"plotBox"===e.relativeTo&&this.breadcrumbs.alignBreadcrumbsGroup(-t.width-e.buttonSpacing)}}function C(){this.breadcrumbs&&(this.breadcrumbs.destroy(),this.breadcrumbs=void 0)}function M(){let t=this.breadcrumbs;if(t&&!t.options.floating&&t.level){let e=t.options,i=e.buttonTheme,s=(i.height||0)+2*(i.padding||0)+e.buttonSpacing,o=e.position.verticalAlign;"bottom"===o?(this.marginBottom=(this.marginBottom||0)+s,t.yOffset=s):"middle"!==o?(this.plotTop+=s,t.yOffset=-s):t.yOffset=void 0}}function B(){this.breadcrumbs&&this.breadcrumbs.redraw()}function I(t){!0===t.resetSelection&&this.breadcrumbs&&this.breadcrumbs.alignBreadcrumbsGroup()}class N{static compose(t,e){S(m,"Breadcrumbs")&&(x(t,"destroy",C),x(t,"afterShowResetZoom",O),x(t,"getMargins",M),x(t,"redraw",B),x(t,"selection",I),L(e.lang,v.lang))}constructor(t,e){this.elementList={},this.isDirty=!0,this.level=0,this.list=[];let i=w(t.options.drilldown&&t.options.drilldown.drillUpButton,N.defaultOptions,t.options.navigation&&t.options.navigation.breadcrumbs,e);this.chart=t,this.options=i||{}}updateProperties(t){this.setList(t),this.setLevel(),this.isDirty=!0}setList(t){this.list=t}setLevel(){this.level=this.list.length&&this.list.length-1}getLevel(){return this.level}getButtonText(t){let e=this.chart,i=this.options,s=e.options.lang,o=R(i.format,i.showFullPath?"{level.name}":"← {level.name}"),r=s&&R(s.drillUpText,s.mainBreadcrumb),a=i.formatter&&i.formatter(t)||b(o,{level:t.levelOptions},e)||"";return(T(a)&&!a.length||"← "===a)&&y(r)&&(a=i.showFullPath?r:"← "+r),a}redraw(){this.isDirty&&this.render(),this.group&&this.group.align(),this.isDirty=!1}render(){let t=this.chart,e=this.options;!this.group&&e&&(this.group=t.renderer.g("breadcrumbs-group").addClass("highcharts-no-tooltip highcharts-breadcrumbs").attr({zIndex:e.zIndex}).add()),e.showFullPath?this.renderFullPathButtons():this.renderSingleButton(),this.alignBreadcrumbsGroup()}renderFullPathButtons(){this.destroySingleButton(),this.resetElementListState(),this.updateListElements(),this.destroyListElements()}renderSingleButton(){let t=this.chart,e=this.list,i=this.options.buttonSpacing;this.destroyListElements();let s=this.group?this.group.getBBox().width:i,o=e[e.length-2];!t.drillUpButton&&this.level>0?t.drillUpButton=this.renderButton(o,s,i):t.drillUpButton&&(this.level>0?this.updateSingleButton():this.destroySingleButton())}alignBreadcrumbsGroup(t){if(this.group){let e=this.options,i=e.buttonTheme,s=e.position,o="chart"===e.relativeTo||"spacingBox"===e.relativeTo?void 0:"plotBox",r=this.group.getBBox(),a=2*(i.padding||0)+e.buttonSpacing;s.width=r.width+a,s.height=r.height+a;let l=w(s);t&&(l.x+=t),this.options.rtl&&(l.x+=s.width),l.y=R(l.y,this.yOffset,0),this.group.align(l,!0,o)}}renderButton(t,e,i){let s=this,o=this.chart,r=s.options,a=w(r.buttonTheme),l=o.renderer.button(s.getButtonText(t),e,i,function(e){let i,o=r.events&&r.events.click;o&&(i=o.call(s,e,t)),!1!==i&&(r.showFullPath?e.newLevel=t.level:e.newLevel=s.level-1,P(s,"up",e))},a).addClass("highcharts-breadcrumbs-button").add(s.group);return o.styledMode||l.attr(r.style),l}renderSeparator(t,e){let i=this.chart,s=this.options.separator,o=i.renderer.label(s.text,t,e,void 0,void 0,void 0,!1).addClass("highcharts-breadcrumbs-separator").add(this.group);return i.styledMode||o.css(s.style),o}update(t){w(!0,this.options,t),this.destroy(),this.isDirty=!0}updateSingleButton(){let t=this.chart,e=this.list[this.level-1];t.drillUpButton&&t.drillUpButton.attr({text:this.getButtonText(e)})}destroy(){this.destroySingleButton(),this.destroyListElements(!0),this.group&&this.group.destroy(),this.group=void 0}destroyListElements(t){let e=this.elementList;A(e,(i,s)=>{(t||!e[s].updated)&&((i=e[s]).button&&i.button.destroy(),i.separator&&i.separator.destroy(),delete i.button,delete i.separator,delete e[s])}),t&&(this.elementList={})}destroySingleButton(){this.chart.drillUpButton&&(this.chart.drillUpButton.destroy(),this.chart.drillUpButton=void 0)}resetElementListState(){A(this.elementList,t=>{t.updated=!1})}updateListElements(){let t=this.elementList,e=this.options.buttonSpacing,i=this.list,s=this.options.rtl,o=s?-1:1,r=function(t,e){return o*t.getBBox().width+o*e},a=function(t,e,i){t.translate(e-t.getBBox().width,i)},l=this.group?r(this.group,e):e,n,h;for(let d=0,p=i.length;d<p;++d){let u,c,g=d===p-1;t[(h=i[d]).level]?(u=(n=t[h.level]).button,n.separator||g?n.separator&&g&&(n.separator.destroy(),delete n.separator):(l+=o*e,n.separator=this.renderSeparator(l,e),s&&a(n.separator,l,e),l+=r(n.separator,e)),t[h.level].updated=!0):(u=this.renderButton(h,l,e),s&&a(u,l,e),l+=r(u,e),g||(c=this.renderSeparator(l,e),s&&a(c,l,e),l+=r(c,e)),t[h.level]={button:u,separator:c,updated:!0}),u&&u.setState(2*!!g)}}}N.defaultOptions=v.options;var D=p(620),E=p.n(D),H=p(512),G=p.n(H),k=p(28),V=p.n(k);let{column:{prototype:z}}=G().seriesTypes,{addEvent:W,defined:U}=g();!function(t){function e(t){let e=this.series,i=e.chart.renderer;this.moveToTopOnHover&&this.graphic&&(e.stateMarkerGraphic||(e.stateMarkerGraphic=new(V())(i,"use").css({pointerEvents:"none"}).add(this.graphic.parentGroup)),t?.state==="hover"?(this.graphic.attr({id:this.id}),e.stateMarkerGraphic.attr({href:`${i.url}#${this.id}`,visibility:"visible"})):e.stateMarkerGraphic.attr({href:""}))}t.pointMembers={dataLabelOnNull:!0,moveToTopOnHover:!0,isValid:function(){return null!==this.value&&this.value!==1/0&&this.value!==-1/0&&(void 0===this.value||!isNaN(this.value))}},t.seriesMembers={colorKey:"value",axisTypes:["xAxis","yAxis","colorAxis"],parallelArrays:["x","y","value"],pointArrayMap:["value"],trackerGroups:["group","markerGroup","dataLabelsGroup"],colorAttribs:function(t){let e={};return U(t.color)&&(!t.state||"normal"===t.state)&&(e[this.colorProp||"fill"]=t.color),e},pointAttribs:z.pointAttribs},t.compose=function(t){return W(t.prototype.pointClass,"afterSetState",e),t}}(a||(a={}));let F=a;var _=p(820),Y=p.n(_);let j=class{constructor(t,e,i,s){this.height=t,this.width=e,this.plot=s,this.direction=i,this.startDirection=i,this.total=0,this.nW=0,this.lW=0,this.nH=0,this.lH=0,this.elArr=[],this.lP={total:0,lH:0,nH:0,lW:0,nW:0,nR:0,lR:0,aspectRatio:function(t,e){return Math.max(t/e,e/t)}}}addElement(t){this.lP.total=this.elArr[this.elArr.length-1],this.total=this.total+t,0===this.direction?(this.lW=this.nW,this.lP.lH=this.lP.total/this.lW,this.lP.lR=this.lP.aspectRatio(this.lW,this.lP.lH),this.nW=this.total/this.height,this.lP.nH=this.lP.total/this.nW,this.lP.nR=this.lP.aspectRatio(this.nW,this.lP.nH)):(this.lH=this.nH,this.lP.lW=this.lP.total/this.lH,this.lP.lR=this.lP.aspectRatio(this.lP.lW,this.lH),this.nH=this.total/this.width,this.lP.nW=this.lP.total/this.nH,this.lP.nR=this.lP.aspectRatio(this.lP.nW,this.nH)),this.elArr.push(t)}reset(){this.nW=0,this.lW=0,this.elArr=[],this.total=0}},X=class{constructor(){this.childrenTotal=0,this.visible=!1}init(t,e,i,s,o,r,a){return this.id=t,this.i=e,this.children=i,this.height=s,this.level=o,this.series=r,this.parent=a,this}},$={draw:function(t,e){let{animatableAttribs:i,onComplete:s,css:o,renderer:r}=e,a=t.series&&t.series.chart.hasRendered?void 0:t.series&&t.series.options.animation,l=t.graphic;if(e.attribs={...e.attribs,class:t.getClassName()},t.shouldDraw())l||(t.graphic=l="text"===e.shapeType?r.text():"image"===e.shapeType?r.image(e.imageUrl||"").attr(e.shapeArgs||{}):r[e.shapeType](e.shapeArgs||{}),l.add(e.group)),o&&l.css(o),l.attr(e.attribs).animate(i,!e.isNew&&a,s);else if(l){let e=()=>{t.graphic=l=l&&l.destroy(),"function"==typeof s&&s()};Object.keys(i).length?l.animate(i,void 0,()=>e()):e()}}},{pie:{prototype:{pointClass:K}},scatter:{prototype:{pointClass:q}}}=G().seriesTypes,{extend:Z,isNumber:J,pick:Q}=g();class tt extends q{constructor(){super(...arguments),this.groupedPointsAmount=0,this.shapeType="rect"}draw(t){$.draw(this,t)}getClassName(){let t=this.series,e=t.options,i=super.getClassName();return this.node.level<=t.nodeMap[t.rootNode].level&&this.node.children.length?i+=" highcharts-above-level":this.node.isGroup||this.node.isLeaf||t.nodeMap[t.rootNode].isGroup||Q(e.interactByLeaf,!e.allowTraversingTree)?this.node.isGroup||this.node.isLeaf||t.nodeMap[t.rootNode].isGroup||(i+=" highcharts-internal-node"):i+=" highcharts-internal-node-interactive",i}isValid(){return!!(this.id||J(this.value))}setState(t){super.setState.apply(this,arguments),this.graphic&&this.graphic.attr({zIndex:+("hover"===t)})}shouldDraw(){return J(this.plotY)&&null!==this.y}}Z(tt.prototype,{setVisible:K.prototype.setVisible});let{isString:te}=g(),ti={allowTraversingTree:!1,animationLimit:250,borderRadius:0,showInLegend:!1,marker:void 0,colorByPoint:!1,dataLabels:{enabled:!0,formatter:function(){let t=this&&this.point?this.point:{};return te(t.name)?t.name:""},headers:!1,inside:!0,padding:2,verticalAlign:"middle",style:{textOverflow:"ellipsis"}},tooltip:{headerFormat:"",pointFormat:"<b>{point.name}</b>: {point.value}<br/>",clusterFormat:"+ {point.groupedPointsAmount} more...<br/>"},ignoreHiddenPoint:!0,layoutAlgorithm:"sliceAndDice",layoutStartingDirection:"vertical",alternateStartingDirection:!1,levelIsConstant:!0,traverseUpButton:{position:{align:"right",x:-10,y:10}},borderColor:"#e6e6e6",borderWidth:1,colorKey:"colorValue",opacity:.15,states:{hover:{borderColor:"#999999",brightness:.1*!G().seriesTypes.heatmap,halo:!1,opacity:.75,shadow:!1}},legendSymbol:"rectangle",traverseToLeaf:!1,cluster:{className:void 0,color:void 0,enabled:!1,pixelWidth:void 0,pixelHeight:void 0,name:void 0,reductionFactor:void 0,minimumClusterSize:5,layoutAlgorithm:{distance:0,gridSize:0,kmeansThreshold:0},marker:{lineWidth:0,radius:0}}};(l||(l={})).recursive=function t(e,i,s){let o=i.call(s||this,e);!1!==o&&t(o,i,s)};let ts=l,{extend:to,isArray:tr,isNumber:ta,isObject:tl,merge:tn,pick:th,relativeLength:td}=g(),tp={getColor:function(t,e){let i,s,o,r,a,l,n=e.index,h=e.mapOptionsToLevel,d=e.parentColor,p=e.parentColorIndex,u=e.series,c=e.colors,g=e.siblings,v=u.points,f=u.chart.options.chart;return t&&(i=v[t.i],s=h[t.level]||{},i&&s.colorByPoint&&(r=i.index%(c?c.length:f.colorCount),o=c&&c[r]),u.chart.styledMode||(a=th(i&&i.options.color,s&&s.color,o,d&&(t=>{let e=s&&s.colorVariation;return e&&"brightness"===e.key&&n&&g?E().parse(t).brighten(e.to*(n/g)).get():t})(d),u.color)),l=th(i&&i.options.colorIndex,s&&s.colorIndex,r,p,e.colorIndex)),{color:a,colorIndex:l}},getLevelOptions:function(t){let e,i,s,o,r,a,l={};if(tl(t))for(o=ta(t.from)?t.from:1,a=t.levels,i={},e=tl(t.defaults)?t.defaults:{},tr(a)&&(i=a.reduce((t,i)=>{let s,r,a;return tl(i)&&ta(i.level)&&(r=th((a=tn({},i)).levelIsConstant,e.levelIsConstant),delete a.levelIsConstant,delete a.level,tl(t[s=i.level+(r?0:o-1)])?tn(!0,t[s],a):t[s]=a),t},{})),r=ta(t.to)?t.to:1,s=0;s<=r;s++)l[s]=tn({},e,tl(i[s])?i[s]:{});return l},getNodeWidth:function(t,e){let{chart:i,options:s}=t,{nodeDistance:o=0,nodeWidth:r=0}=s,{plotSizeX:a=1}=i;if("auto"===r){if("string"==typeof o&&/%$/.test(o))return a/(e+parseFloat(o)/100*(e-1));let t=Number(o);return(a+t)/(e||1)-t}return td(r,a)},setTreeValues:function t(e,i){let s=i.before,o=i.idRoot,r=i.mapIdToNode[o],a=!1!==i.levelIsConstant,l=i.points[e.i],n=l&&l.options||{},h=[],d=0;e.levelDynamic=e.level-(a?0:r.level),e.name=th(l&&l.name,""),e.visible=o===e.id||!0===i.visible,"function"==typeof s&&(e=s(e,i)),e.children.forEach((s,o)=>{let r=to({},i);to(r,{index:o,siblings:e.children.length,visible:e.visible}),s=t(s,r),h.push(s),s.visible&&(d+=s.val)});let p=th(n.value,d);return e.visible=p>=0&&(d>0||e.visible),e.children=h,e.childrenTotal=d,e.isLeaf=e.visible&&!d,e.val=p,e},updateRootId:function(t){let e,i;return tl(t)&&(i=tl(t.options)?t.options:{},e=th(t.rootNode,i.rootId,""),tl(t.userOptions)&&(t.userOptions.rootId=e),t.rootNode=e),e}},{parse:tu}=E(),{composed:tc,noop:tg}=g(),{column:tv,scatter:tf}=G().seriesTypes,{getColor:tb,getLevelOptions:tm,updateRootId:tx}=tp,{addEvent:ty,arrayMax:tL,clamp:tP,correctFloat:tT,crisp:tw,defined:tA,error:tR,extend:tS,fireEvent:tO,isArray:tC,isNumber:tM,isObject:tB,isString:tI,merge:tN,pick:tD,pushUnique:tE,splat:tH,stableSort:tG}=g();Y().keepProps.push("simulation","hadOutsideDataLabels");let tk=!1;function tV(){let t,e=this.xAxis,i=this.yAxis;e&&i&&(this.is("treemap")?(t={endOnTick:!1,gridLineWidth:0,lineWidth:0,min:0,minPadding:0,max:100,maxPadding:0,startOnTick:!1,title:void 0,tickPositions:[]},tS(i.options,t),tS(e.options,t),tk=!0):tk&&(i.setOptions(i.userOptions),e.setOptions(e.userOptions),tk=!1))}class tz extends tf{constructor(){super(...arguments),this.simulation=0}static compose(t){tE(tc,"TreemapSeries")&&ty(t,"afterBindAxes",tV)}algorithmCalcPoints(t,e,i,s){let o=i.plot,r=i.elArr.length-1,a,l,n,h,d=i.lW,p=i.lH,u,c=0;for(let t of(e?(d=i.nW,p=i.nH):u=i.elArr[r],i.elArr))(e||c<r)&&(0===i.direction?(a=o.x,l=o.y,h=t/(n=d)):(a=o.x,l=o.y,n=t/(h=p)),s.push({x:a,y:l,width:n,height:tT(h)}),0===i.direction?o.y=o.y+h:o.x=o.x+n),c+=1;i.reset(),0===i.direction?i.width=i.width-d:i.height=i.height-p,o.y=o.parent.y+(o.parent.height-i.height),o.x=o.parent.x+(o.parent.width-i.width),t&&(i.direction=1-i.direction),e||i.addElement(u)}algorithmFill(t,e,i){let s=[],o,r=e.direction,a=e.x,l=e.y,n=e.width,h=e.height,d,p,u,c;for(let g of i)o=e.width*e.height*(g.val/e.val),d=a,p=l,0===r?(n-=u=o/(c=h),a+=u):(h-=c=o/(u=n),l+=c),s.push({x:d,y:p,width:u,height:c,direction:0,val:0}),t&&(r=1-r);return s}algorithmLowAspectRatio(t,e,i){let s=[],o={x:e.x,y:e.y,parent:e},r=e.direction,a=i.length-1,l=new j(e.height,e.width,r,o),n,h=0;for(let r of i)n=e.width*e.height*(r.val/e.val),l.addElement(n),l.lP.nR>l.lP.lR&&this.algorithmCalcPoints(t,!1,l,s,o),h===a&&this.algorithmCalcPoints(t,!0,l,s,o),++h;return s}alignDataLabel(t,e,i){tv.prototype.alignDataLabel.apply(this,arguments),t.dataLabel&&t.dataLabel.attr({zIndex:(t.node.zIndex||0)+1})}applyTreeGrouping(){let t=this,e=t.parentList||{},{cluster:i}=t.options,s=i?.minimumClusterSize||5;if(i?.enabled){let o={},r=t=>{if(t?.point?.shapeArgs){let{width:e=0,height:s=0}=t.point.shapeArgs,{pixelWidth:r=0,pixelHeight:a=0}=i,l=tA(a),n=a?r*a:r*r;(e<r||s<(l?a:r)||e*s<n)&&!t.isGroup&&tA(t.parent)&&(o[t.parent]||(o[t.parent]=[]),o[t.parent].push(t))}t?.children.forEach(t=>{r(t)})};for(let a in r(t.tree),o)o[a]&&o[a].length>s&&o[a].forEach(s=>{let o=e[a].indexOf(s.i);if(-1!==o){e[a].splice(o,1);let r=`highcharts-grouped-treemap-points-${s.parent||"root"}`,l=t.points.find(t=>t.id===r);if(!l){let s=t.pointClass,o=t.points.length;tS(l=new s(t,{className:i.className,color:i.color,id:r,index:o,isGroup:!0,value:0}),{formatPrefix:"cluster"}),t.points.push(l),e[a].push(o),e[r]=[]}let n=l.groupedPointsAmount+1,h=t.points[l.index].options.value||0,d=i.name||`+ ${n}`;t.points[l.index].groupedPointsAmount=n,t.points[l.index].options.value=h+(s.point.value||0),t.points[l.index].name=d,e[r].push(s.point.index)}});t.nodeMap={},t.nodeList=[],t.parentList=e;let a=t.buildTree("",-1,0,t.parentList);t.translate(a)}}calculateChildrenAreas(t,e){let i=this.options,s=this.mapOptionsToLevel[t.level+1],o=tD(s?.layoutAlgorithm&&this[s?.layoutAlgorithm]&&s.layoutAlgorithm,i.layoutAlgorithm),r=i.alternateStartingDirection,a=t.children.filter(e=>t.isGroup||!e.ignore),l=s?.groupPadding??i.groupPadding??0,n=this.nodeMap[this.rootNode];if(!o)return;let h=[],d=n.pointValues?.width||0,p=n.pointValues?.height||0;s?.layoutStartingDirection&&(e.direction=+("vertical"!==s.layoutStartingDirection)),h=this[o](e,a);let u=-1;for(let t of a){let i=h[++u];t===n&&(d=d||i.width,p=i.height);let s=l/(this.xAxis.len/p),o=l/(this.yAxis.len/p);if(t.values=tN(i,{val:t.childrenTotal,direction:r?1-e.direction:e.direction}),t.children.length&&t.point.dataLabels?.length){let e=tL(t.point.dataLabels.map(t=>t.options?.headers&&t.height||0))/(this.yAxis.len/p);e<t.values.height/2&&(t.values.y+=e,t.values.height-=e)}if(l){let e=Math.min(s,t.values.width/4),i=Math.min(o,t.values.height/4);t.values.x+=e,t.values.width-=2*e,t.values.y+=i,t.values.height-=2*i}t.pointValues=tN(i,{x:i.x/this.axisRatio,y:100-i.y-i.height,width:i.width/this.axisRatio}),t.children.length&&this.calculateChildrenAreas(t,t.values)}let c=(t,e=[],i=!0)=>(t.children.forEach(t=>{i&&t.isLeaf?e.push(t.point):i||t.isLeaf||e.push(t.point),t.children.length&&c(t,e,i)}),e);if("leaf"===i.nodeSizeBy&&t===n&&this.hasOutsideDataLabels&&!c(n,void 0,!1).some(t=>tM(t.options.value))&&!tM(n.point?.options.value)){let i=c(n),s=i.map(t=>t.options.value||0),o=i.map(({node:{pointValues:t}})=>t?t.width*t.height:0),r=s.reduce((t,e)=>t+e,0),a=o.reduce((t,e)=>t+e,0)/r,l=0,h=0;i.forEach((t,e)=>{let i=tP((s[e]?o[e]/s[e]:1)/a,.8,1.4),r=1-i;t.value&&(o[e]<20&&(r*=o[e]/20),r>h&&(h=r),r<l&&(l=r),t.simulatedValue=(t.simulatedValue||t.value)/i)}),(l<-.05||h>.05)&&this.simulation<10?(this.simulation++,this.setTreeValues(t),e.val=t.val,this.calculateChildrenAreas(t,e)):(i.forEach(t=>{delete t.simulatedValue}),this.setTreeValues(t),this.simulation=0)}}createList(t){let e=this.chart,i=e.breadcrumbs,s=[];if(i){let i=0;s.push({level:i,levelOptions:e.series[0]});let o=t.target.nodeMap[t.newRootId],r=[];for(;o.parent||""===o.parent;)r.push(o),o=t.target.nodeMap[o.parent];for(let t of r.reverse())s.push({level:++i,levelOptions:t});s.length<=1&&(s.length=0)}return s}drawDataLabels(){let t=this.mapOptionsToLevel,e=this.points.filter(function(t){return t.node.visible||tA(t.dataLabel)}),i=tH(this.options.dataLabels||{})[0]?.padding,s=e.some(t=>tM(t.plotY));for(let o of e){let e={},r={style:e},a=t[o.node.level];if((!o.node.isLeaf&&!o.node.isGroup||o.node.isGroup&&o.node.level<=this.nodeMap[this.rootNode].level)&&(r.enabled=!1),a?.dataLabels&&(tN(!0,r,tH(a.dataLabels)[0]),this.hasDataLabels=()=>!0),o.node.isLeaf?r.inside=!0:r.headers&&(r.verticalAlign="top"),o.shapeArgs&&s){let{height:t=0,width:s=0}=o.shapeArgs;if(s>32&&t>16&&o.shouldDraw()){let a=s-2*(r.padding||i||0);e.width=`${a}px`,e.lineClamp??(e.lineClamp=Math.floor(t/16)),e.visibility="inherit",r.headers&&o.dataLabel?.attr({width:a})}else e.width=`${s}px`,e.visibility="hidden"}o.dlOptions=tN(r,o.options.dataLabels)}super.drawDataLabels(e)}drawPoints(t=this.points){let e=this.chart,i=e.renderer,s=e.styledMode,o=this.options,r=s?{}:o.shadow,a=o.borderRadius,l=e.pointCount<o.animationLimit,n=o.allowTraversingTree;for(let e of t){let t=e.node.levelDynamic,h={},d={},p={},u="level-group-"+e.node.level,c=!!e.graphic,g=l&&c,v=e.shapeArgs;e.shouldDraw()&&(e.isInside=!0,a&&(d.r=a),tN(!0,g?h:d,c?v:{},s?{}:this.pointAttribs(e,e.selected?"select":void 0)),this.colorAttribs&&s&&tS(p,this.colorAttribs(e)),this[u]||(this[u]=i.g(u).attr({zIndex:1e3-(t||0)}).add(this.group),this[u].survive=!0)),e.draw({animatableAttribs:h,attribs:d,css:p,group:this[u],imageUrl:e.imageUrl,renderer:i,shadow:r,shapeArgs:v,shapeType:e.shapeType}),n&&e.graphic&&(e.drillId=o.interactByLeaf?this.drillToByLeaf(e):this.drillToByGroup(e))}}drillToByGroup(t){return(!t.node.isLeaf||!!t.node.isGroup)&&t.id}drillToByLeaf(t){let{traverseToLeaf:e}=t.series.options,i=!1,s;if(t.node.parent!==this.rootNode&&t.node.isLeaf){if(e)i=t.id;else for(s=t.node;!i;)void 0!==s.parent&&(s=this.nodeMap[s.parent]),s.parent===this.rootNode&&(i=s.id)}return i}drillToNode(t,e){tR(32,!1,void 0,{"treemap.drillToNode":"use treemap.setRootNode"}),this.setRootNode(t,e)}drillUp(){let t=this.nodeMap[this.rootNode];t&&tI(t.parent)&&this.setRootNode(t.parent,!0,{trigger:"traverseUpButton"})}getExtremes(){let{dataMin:t,dataMax:e}=super.getExtremes(this.colorValueData);return this.valueMin=t,this.valueMax=e,super.getExtremes()}getListOfParents(t,e){let i=tC(t)?t:[],s=tC(e)?e:[],o=i.reduce(function(t,e,i){let s=tD(e.parent,"");return void 0===t[s]&&(t[s]=[]),t[s].push(i),t},{"":[]});for(let t of Object.keys(o)){let e=o[t];if(""!==t&&-1===s.indexOf(t)){for(let t of e)o[""].push(t);delete o[t]}}return o}getTree(){let t=this.data.map(function(t){return t.id});return this.parentList=this.getListOfParents(this.data,t),this.nodeMap={},this.nodeList=[],this.buildTree("",-1,0,this.parentList||{})}buildTree(t,e,i,s,o){let r=[],a=this.points[e],l=0,n;for(let e of s[t]||[])l=Math.max((n=this.buildTree(this.points[e].id,e,i+1,s,t)).height+1,l),r.push(n);let h=new this.NodeClass().init(t,e,r,l,i,this,o);for(let t of r)t.parentNode=h;return this.nodeMap[h.id]=h,this.nodeList.push(h),a&&(a.node=h,h.point=a),h}hasData(){return!!this.dataTable.rowCount}init(t,e){let i=this,s=tN(e.drillUpButton,e.breadcrumbs),o=ty(i,"setOptions",t=>{let e=t.userOptions;tA(e.allowDrillToNode)&&!tA(e.allowTraversingTree)&&(e.allowTraversingTree=e.allowDrillToNode,delete e.allowDrillToNode),tA(e.drillUpButton)&&!tA(e.traverseUpButton)&&(e.traverseUpButton=e.drillUpButton,delete e.drillUpButton);let i=tH(e.dataLabels||{});e.levels?.forEach(t=>{i.push.apply(i,tH(t.dataLabels||{}))}),this.hasOutsideDataLabels=i.some(t=>t.headers)});super.init(t,e),delete i.opacity,i.eventsToUnbind.push(o),i.options.allowTraversingTree&&(i.eventsToUnbind.push(ty(i,"click",i.onClickDrillToNode)),i.eventsToUnbind.push(ty(i,"setRootNode",function(t){let e=i.chart;e.breadcrumbs&&e.breadcrumbs.updateProperties(i.createList(t))})),i.eventsToUnbind.push(ty(i,"update",function(t,e){let i=this.chart.breadcrumbs;i&&t.options.breadcrumbs&&i.update(t.options.breadcrumbs),this.hadOutsideDataLabels=this.hasOutsideDataLabels})),i.eventsToUnbind.push(ty(i,"destroy",function(t){let e=this.chart;e.breadcrumbs&&!t.keepEventsForUpdate&&(e.breadcrumbs.destroy(),e.breadcrumbs=void 0)}))),t.breadcrumbs||(t.breadcrumbs=new N(t,s)),i.eventsToUnbind.push(ty(t.breadcrumbs,"up",function(t){let e=this.level-t.newLevel;for(let t=0;t<e;t++)i.drillUp()}))}onClickDrillToNode(t){let e=t.point,i=e?.drillId;tI(i)&&(e.setState(""),this.setRootNode(i,!0,{trigger:"click"}))}pointAttribs(t,e){let i=tB(this.mapOptionsToLevel)?this.mapOptionsToLevel:{},s=t&&i[t.node.level]||{},o=this.options,r=e&&o.states&&o.states[e]||{},a=t?.getClassName()||"",l={stroke:t&&t.borderColor||s.borderColor||r.borderColor||o.borderColor,"stroke-width":tD(t&&t.borderWidth,s.borderWidth,r.borderWidth,o.borderWidth),dashstyle:t?.borderDashStyle||s.borderDashStyle||r.borderDashStyle||o.borderDashStyle,fill:t?.color||this.color};return -1!==a.indexOf("highcharts-above-level")?(l.fill="none",l["stroke-width"]=0):-1!==a.indexOf("highcharts-internal-node-interactive")?(l["fill-opacity"]=r.opacity??o.opacity??1,l.cursor="pointer"):-1!==a.indexOf("highcharts-internal-node")?l.fill="none":e&&r.brightness&&(l.fill=tu(l.fill).brighten(r.brightness).get()),l}setColorRecursive(t,e,i,s,o){let r=this?.chart,a=r?.options?.colors;if(t){let r=tb(t,{colors:a,index:s,mapOptionsToLevel:this.mapOptionsToLevel,parentColor:e,parentColorIndex:i,series:this,siblings:o}),l=this.points[t.i];l&&(l.color=r.color,l.colorIndex=r.colorIndex);let n=-1;for(let e of t.children||[])this.setColorRecursive(e,r.color,r.colorIndex,++n,t.children.length)}}setPointValues(){let t=this,{points:e,xAxis:i,yAxis:s}=t,o=t.chart.styledMode,r=e=>o?0:t.pointAttribs(e)["stroke-width"]||0;for(let t of e){let{pointValues:e,visible:o}=t.node;if(e&&o){let{height:o,width:a,x:l,y:n}=e,h=r(t),d=i.toPixels(l,!0),p=i.toPixels(l+a,!0),u=s.toPixels(n,!0),c=s.toPixels(n+o,!0),g=0===d?h/2:tw(i.toPixels(l,!0),h,!0),v=p===i.len?i.len-h/2:tw(i.toPixels(l+a,!0),h,!0),f=u===s.len?s.len-h/2:tw(s.toPixels(n,!0),h,!0),b=0===c?h/2:tw(s.toPixels(n+o,!0),h,!0),m={x:Math.min(g,v),y:Math.min(f,b),width:Math.abs(v-g),height:Math.abs(b-f)};t.plotX=m.x+m.width/2,t.plotY=m.y+m.height/2,t.shapeArgs=m}else delete t.plotX,delete t.plotY}}setRootNode(t,e,i){tO(this,"setRootNode",tS({newRootId:t,previousRootId:this.rootNode,redraw:tD(e,!0),series:this},i),function(t){let e=t.series;e.idPreviousRoot=t.previousRootId,e.rootNode=t.newRootId,e.isDirty=!0,t.redraw&&e.chart.redraw()})}setState(t){this.options.inactiveOtherPoints=!0,super.setState(t,!1),this.options.inactiveOtherPoints=!1}setTreeValues(t){let e=this.options,i=this.rootNode,s=this.nodeMap[i],o="boolean"!=typeof e.levelIsConstant||e.levelIsConstant,r=[],a=this.points[t.i],l=0;for(let e of t.children)e=this.setTreeValues(e),r.push(e),e.ignore||(l+=e.val);tG(r,(t,e)=>(t.sortIndex||0)-(e.sortIndex||0));let n=tD(a?.simulatedValue,a?.options.value,l);return a&&(a.value=n),a?.isGroup&&e.cluster?.reductionFactor&&(n/=e.cluster.reductionFactor),t.parentNode?.point?.isGroup&&this.rootNode!==t.parent&&(t.visible=!1),tS(t,{children:r,childrenTotal:l,ignore:!(tD(a?.visible,!0)&&n>0),isLeaf:t.visible&&!l,isGroup:a?.isGroup,levelDynamic:t.level-(o?0:s.level),name:tD(a?.name,""),sortIndex:tD(a?.sortIndex,-n),val:n}),t}sliceAndDice(t,e){return this.algorithmFill(!0,t,e)}squarified(t,e){return this.algorithmLowAspectRatio(!0,t,e)}strip(t,e){return this.algorithmLowAspectRatio(!1,t,e)}stripes(t,e){return this.algorithmFill(!1,t,e)}translate(t){let e=this,i=e.options,s=!t,o=tx(e),r,a,l,n;t||o.startsWith("highcharts-grouped-treemap-points-")||((this.points||[]).forEach(t=>{t.isGroup&&t.destroy()}),super.translate(),t=e.getTree()),e.tree=t=t||e.tree,r=e.nodeMap[o],""===o||r||(e.setRootNode("",!1),o=e.rootNode,r=e.nodeMap[o]),r.point?.isGroup||(e.mapOptionsToLevel=tm({from:r.level+1,levels:i.levels,to:t.height,defaults:{levelIsConstant:e.options.levelIsConstant,colorByPoint:i.colorByPoint}})),ts.recursive(e.nodeMap[e.rootNode],t=>{let i=t.parent,s=!1;return t.visible=!0,(i||""===i)&&(s=e.nodeMap[i]),s}),ts.recursive(e.nodeMap[e.rootNode].children,t=>{let e=!1;for(let i of t)i.visible=!0,i.children.length&&(e=(e||[]).concat(i.children));return e}),e.setTreeValues(t),e.axisRatio=e.xAxis.len/e.yAxis.len,e.nodeMap[""].pointValues=a={x:0,y:0,width:100,height:100},e.nodeMap[""].values=l=tN(a,{width:a.width*e.axisRatio,direction:+("vertical"!==i.layoutStartingDirection),val:t.val}),(this.hasOutsideDataLabels||this.hadOutsideDataLabels)&&this.drawDataLabels(),e.calculateChildrenAreas(t,l),e.colorAxis||i.colorByPoint||e.setColorRecursive(e.tree),i.allowTraversingTree&&r.pointValues&&(n=r.pointValues,e.xAxis.setExtremes(n.x,n.x+n.width,!1),e.yAxis.setExtremes(n.y,n.y+n.height,!1),e.xAxis.setScale(),e.yAxis.setScale()),e.setPointValues(),s&&e.applyTreeGrouping()}}tz.defaultOptions=tN(tf.defaultOptions,ti),tS(tz.prototype,{buildKDTree:tg,colorAttribs:F.seriesMembers.colorAttribs,colorKey:"colorValue",directTouch:!0,getExtremesFromAll:!0,getSymbol:tg,optionalAxis:"colorAxis",parallelArrays:["x","y","value","colorValue"],pointArrayMap:["value","colorValue"],pointClass:tt,NodeClass:X,trackerGroups:["group","dataLabelsGroup"],utils:ts}),F.compose(tz),G().registerSeriesType("treemap",tz);let{deg2rad:tW}=g(),{fireEvent:tU,isNumber:tF,pick:t_,relativeLength:tY}=g();!function(t){t.getCenter=function(){let t=this.options,e=this.chart,i=2*(t.slicedOffset||0),s=e.plotWidth-2*i,o=e.plotHeight-2*i,r=t.center,a=Math.min(s,o),l=t.thickness,n,h=t.size,d=t.innerSize||0,p,u;"string"==typeof h&&(h=parseFloat(h)),"string"==typeof d&&(d=parseFloat(d));let c=[t_(r?.[0],"50%"),t_(r?.[1],"50%"),t_(h&&h<0?void 0:t.size,"100%"),t_(d&&d<0?void 0:t.innerSize||0,"0%")];for(!e.angular||this instanceof Y()||(c[3]=0),p=0;p<4;++p)u=c[p],n=p<2||2===p&&/%$/.test(u),c[p]=tY(u,[s,o,a,c[2]][p])+(n?i:0);return c[3]>c[2]&&(c[3]=c[2]),tF(l)&&2*l<c[2]&&l>0&&(c[3]=c[2]-2*l),tU(this,"afterGetCenter",{positions:c}),c},t.getStartAndEndRadians=function(t,e){let i=tF(t)?t:0,s=tF(e)&&e>i&&e-i<360?e:i+360;return{start:tW*(i+-90),end:tW*(s+-90)}}}(n||(n={}));let tj=n,{series:{prototype:{pointClass:tX}},seriesTypes:{treemap:{prototype:{pointClass:t$}}}}=G(),{correctFloat:tK,extend:tq,pInt:tZ}=g();class tJ extends t${getDataLabelPath(t){let e=this.series.chart.renderer,i=this.shapeExisting,s=i.r+tZ(t.options?.distance||0),o=i.start,r=i.end,a=o+(r-o)/2,l=a<0&&a>-Math.PI||a>Math.PI,n;return o===-Math.PI/2&&tK(r)===tK(1.5*Math.PI)&&(o=-Math.PI+Math.PI/360,r=-Math.PI/360,l=!0),r-o>Math.PI&&(l=!1,n=!0,r-o>2*Math.PI-.01&&(o+=.01,r-=.01)),this.dataLabelPath&&(this.dataLabelPath=this.dataLabelPath.destroy()),this.dataLabelPath=e.arc({open:!0,longArc:+!!n}).attr({start:l?o:r,end:l?r:o,clockwise:+l,x:i.x,y:i.y,r:(s+i.innerR)/2}).add(e.defs),this.dataLabelPath}isValid(){return!0}}tq(tJ.prototype,{getClassName:tX.prototype.getClassName,haloPath:tX.prototype.haloPath,setState:tX.prototype.setState});let{seriesTypes:{treemap:tQ}}=G(),{isNumber:t0,isObject:t1,merge:t2}=g();function t6(t,e){let i=[];if(t0(t)&&t0(e)&&t<=e)for(let s=t;s<=e;s++)i.push(s);return i}let t5={calculateLevelSizes:function(t,e){let i=t1(e)?e:{},s,o=0,r,a,l,n;if(t1(t)){for(let e of(s=t2({},t),a=t6(t0(i.from)?i.from:0,t0(i.to)?i.to:0),l=Object.keys(s).filter(t=>-1===a.indexOf(+t)),r=n=t0(i.diffRadius)?i.diffRadius:0,a)){let t=s[e],i=t.levelSize.unit,a=t.levelSize.value;"weight"===i?o+=a:"percentage"===i?(t.levelSize={unit:"pixels",value:a/100*r},n-=t.levelSize.value):"pixels"===i&&(n-=a)}for(let t of a){let e=s[t];if("weight"===e.levelSize.unit){let i=e.levelSize.value;s[t].levelSize={unit:"pixels",value:i/o*n}}}for(let t of l)s[t].levelSize={value:0,unit:"pixels"}}return s},getLevelFromAndTo:function({level:t,height:e}){return{from:t>0?t:1,to:t+e}},range:t6,recursive:tQ.prototype.utils.recursive},{deg2rad:t3}=g(),{addEvent:t9,merge:t8,uniqueKey:t4,defined:t7,extend:et}=g();function ee(t,e){e=t8(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let i=this.renderer.url,s=this.text||this,o=s.textPath,{attributes:r,enabled:a}=e;if(t=t||o&&o.path,o&&o.undo(),t&&a){let e=t9(s,"afterModifyTree",e=>{if(t&&a){let o=t.attr("id");o||t.attr("id",o=t4());let a={x:0,y:0};t7(r.dx)&&(a.dx=r.dx,delete r.dx),t7(r.dy)&&(a.dy=r.dy,delete r.dy),s.attr(a),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let l=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:et(r,{"text-anchor":r.textAnchor,href:`${i}#${o}`}),children:l}}});s.textPath={path:t,undo:e}}else s.attr({dx:0,dy:0}),delete s.textPath;return this.added&&(s.textCache="",this.renderer.buildText(s)),this}function ei(t){let e=t.bBox,i=this.element?.querySelector("textPath");if(i){let t=[],{b:s,h:o}=this.renderer.fontMetrics(this.element),r=o-s,a=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),l=i.innerHTML.replace(a,"").split(/<tspan class="highcharts-br"[^>]*>/),n=l.length,h=(t,e)=>{let{x:o,y:a}=e,l=(i.getRotationOfChar(t)-90)*t3,n=Math.cos(l),h=Math.sin(l);return[[o-r*n,a-r*h],[o+s*n,a+s*h]]};for(let e=0,s=0;s<n;s++){let o=l[s].length;for(let r=0;r<o;r+=5)try{let o=e+r+s,[a,l]=h(o,i.getStartPositionOfChar(o));0===r?(t.push(l),t.push(a)):(0===s&&t.unshift(l),s===n-1&&t.push(a))}catch(t){break}e+=o-1;try{let o=e+s,r=i.getEndPositionOfChar(o),[a,l]=h(o,r);t.unshift(l),t.unshift(a)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function es(t){let e=t.labelOptions,i=t.point,s=e[i.formatPrefix+"TextPath"]||e.textPath;s&&!e.useHTML&&(this.setTextPath(i.getDataLabelPath?.(this)||i.graphic,s),i.dataLabelPath&&!s.enabled&&(i.dataLabelPath=i.dataLabelPath.destroy()))}let{getCenter:eo,getStartAndEndRadians:er}=tj,{noop:ea}=g(),{column:el,treemap:en}=G().seriesTypes,{getColor:eh,getLevelOptions:ed,setTreeValues:ep,updateRootId:eu}=tp,{defined:ec,error:eg,extend:ev,fireEvent:ef,isNumber:eb,isObject:em,isString:ex,merge:ey,splat:eL}=g();({compose:function(t){t9(t,"afterGetBBox",ei),t9(t,"beforeAddingDataLabel",es);let e=t.prototype;e.setTextPath||(e.setTextPath=ee)}}).compose(V());let eP=180/Math.PI,eT=function(t,e,i,s){return{x:t+Math.cos(i)*s,y:e+Math.sin(i)*s}};function ew(t,e){let i=e.mapIdToNode,s=t.parent,o=s?i[s]:void 0,r=e.series,a=r.chart,l=r.points[t.i],n=eh(t,{colors:r.options.colors||a&&a.options.colors,colorIndex:r.colorIndex,index:e.index,mapOptionsToLevel:e.mapOptionsToLevel,parentColor:o&&o.color,parentColorIndex:o&&o.colorIndex,series:e.series,siblings:e.siblings});return t.color=n.color,t.colorIndex=n.colorIndex,l&&(l.color=t.color,l.colorIndex=t.colorIndex,t.sliced=t.id!==e.idRoot&&l.sliced),t}class eA extends en{alignDataLabel(t,e,i){if(!i.textPath||!i.textPath.enabled)return e.placed=!1,super.alignDataLabel.apply(this,arguments)}animate(t){let e,i=this.chart,s=[i.plotWidth/2,i.plotHeight/2],o=i.plotLeft,r=i.plotTop,a=this.group;t?(e={translateX:s[0]+o,translateY:s[1]+r,scaleX:.001,scaleY:.001,rotation:10,opacity:.01},a.attr(e)):(e={translateX:o,translateY:r,scaleX:1,scaleY:1,rotation:0,opacity:1},a.animate(e,this.options.animation))}drawPoints(){let t=this,e=t.mapOptionsToLevel,i=t.shapeRoot,s=t.group,o=t.hasRendered,r=t.rootNode,a=t.idPreviousRoot,l=t.nodeMap,n=l[a],h=n&&n.shapeArgs,d=t.points,p=t.startAndEndRadians,u=t.chart,c=u&&u.options&&u.options.chart||{},g="boolean"!=typeof c.animation||c.animation,v=t.center,f={x:v[0],y:v[1]},b=v[3]/2,m=t.chart.renderer,x=!!(g&&o&&r!==a&&t.dataLabelsGroup),y,L=!1,P=!1;for(let n of(x&&(t.dataLabelsGroup.attr({opacity:0}),y=function(){L=!0,t.dataLabelsGroup&&t.dataLabelsGroup.animate({opacity:1,visibility:"inherit"})}),d)){let d,c,v=n.node,x=e[v.level],L=n.shapeExisting||{},T=v.shapeArgs||{},w=!!(v.visible&&v.shapeArgs);T.borderRadius=t.options.borderRadius,d=o&&g?function(t,e){let i=e.point,s=e.radians,o=e.innerR,r=e.idRoot,a=e.idPreviousRoot,l=e.shapeExisting,n=e.shapeRoot,h=e.shapePreviousRoot,d=e.visible,p={},u={end:t.end,start:t.start,innerR:t.innerR,r:t.r,x:t.x,y:t.y};return d?!i.graphic&&h&&((p=r===i.id?{start:s.start,end:s.end}:h.end<=t.start?{start:s.end,end:s.end}:{start:s.start,end:s.start}).innerR=p.r=o):i.graphic&&(a===i.id?u={innerR:o,r:o}:n&&(u=n.end<=l.start?{innerR:o,r:o,start:s.end,end:s.end}:{innerR:o,r:o,start:s.start,end:s.start})),{from:p,to:u}}(T,{center:f,point:n,radians:p,innerR:b,idRoot:r,idPreviousRoot:a,shapeExisting:L,shapeRoot:i,shapePreviousRoot:h,visible:w}):{to:T,from:{}},ev(n,{shapeExisting:T,tooltipPos:[T.plotX,T.plotY],drillId:function(t,e,i){let s;return t.node.isLeaf||(s=e===t.id?i[e].parent:t.id),s}(n,r,l),name:""+(n.name||n.id||n.index),plotX:T.plotX,plotY:T.plotY,value:v.val,isInside:w,isNull:!w}),n.dlOptions=function(t){let e=t.point,i=em(t.shapeArgs)?t.shapeArgs:{},{end:s=0,radius:o=0,start:r=0}=i,a=em(t.optionsPoint)?t.optionsPoint.dataLabels:{},l=ey(eL(em(t.level)?t.level.dataLabels:{})[0],a),n=l.style=l.style||{},{innerArcLength:h=0,outerArcLength:d=0}=e,p,u,c=l.rotationMode,g=ec(n.width)?parseInt(n.width||"0",10):void 0;return!eb(l.rotation)&&(("auto"===c||"circular"===c)&&(l.useHTML&&"circular"===c&&(c="auto"),h<1&&d>o?(p=0,e.dataLabelPath&&"circular"===c&&(l.textPath={enabled:!0}),s-r<Math.PI&&(g=.7*o)):h>1&&d>1.5*o?"circular"===c?l.textPath={enabled:!0,attributes:{dy:5}}:c="parallel":(e.dataLabel?.textPath&&"circular"===c&&(l.textPath={enabled:!1}),c="perpendicular")),"auto"!==c&&"circular"!==c&&(e.dataLabel?.textPath&&(l.textPath={enabled:!1}),p=s-(s-r)/2),"parallel"===c?g=Math.min(2.5*o,(d+h)/2):!ec(g)&&o&&(g=1===e.node.level?2*o:o),"perpendicular"===c&&(d<16?g=1:i.radius&&(n.lineClamp=Math.floor(h/16)||1,g=o-(h<16?(16-h)/(d-h)*o:0))),g=Math.max((g||0)-2*(l.padding||0),1),u=(p||0)*eP%180,"parallel"===c&&(u-=90),u>90?u-=180:u<-90&&(u+=180),l.rotation=u),l.textPath&&(0===e.shapeExisting.innerR&&l.textPath.enabled?(l.rotation=0,l.textPath.enabled=!1,g=Math.max(2*e.shapeExisting.r-2*(l.padding||0),1)):e.dlOptions?.textPath&&!e.dlOptions.textPath.enabled&&"circular"===c&&(l.textPath.enabled=!0),l.textPath.enabled&&(l.rotation=0,g=Math.max((d+h)/2-2*(l.padding||0),1),n.whiteSpace="nowrap")),n.width=g+"px",l}({point:n,level:x,optionsPoint:n.options,shapeArgs:T}),!P&&w&&(P=!0,c=y),n.draw({animatableAttribs:d.to,attribs:ev(d.from,!u.styledMode&&t.pointAttribs(n,n.selected&&"select")),onComplete:c,group:s,renderer:m,shapeType:"arc",shapeArgs:T})}x&&P?(t.hasRendered=!1,t.options.dataLabels.defer=!0,el.prototype.drawDataLabels.call(t),t.hasRendered=!0,L&&y()):el.prototype.drawDataLabels.call(t),t.idPreviousRoot=r}layoutAlgorithm(t,e,i){let s=t.start,o=t.end-s,r=t.val,a=t.x,l=t.y,n=i&&em(i.levelSize)&&eb(i.levelSize.value)?i.levelSize.value:0,h=t.r,d=h+n,p=i&&eb(i.slicedOffset)?i.slicedOffset:0;return(e||[]).reduce((t,e)=>{let i=1/r*e.val*o,u=eT(a,l,s+i/2,p),c={x:e.sliced?u.x:a,y:e.sliced?u.y:l,innerR:h,r:d,radius:n,start:s,end:s+i};return t.push(c),s=c.end,t},[])}setRootNode(t,e,i){if(1===this.nodeMap[t].level&&1===this.nodeList.filter(t=>1===t.level).length){if(""===this.idPreviousRoot)return;t=""}super.setRootNode(t,e,i)}setShapeArgs(t,e,i){let s=i[t.level+1],o=t.children.filter(function(t){return t.visible}),r=[];r=this.layoutAlgorithm(e,o,s);let a=-1;for(let t of o){let e=r[++a],s=e.start+(e.end-e.start)/2,o=e.innerR+(e.r-e.innerR)/2,l=e.end-e.start,n=0===e.innerR&&l>6.28?{x:e.x,y:e.y}:eT(e.x,e.y,s,o),h=t.val?t.childrenTotal>t.val?t.childrenTotal:t.val:t.childrenTotal;this.points[t.i]&&(this.points[t.i].innerArcLength=l*e.innerR,this.points[t.i].outerArcLength=l*e.r),t.shapeArgs=ey(e,{plotX:n.x,plotY:n.y}),t.values=ey(e,{val:h}),t.children.length&&this.setShapeArgs(t,t.values,i)}}translate(){let t=this.options,e=this.center=this.getCenter(),i=this.startAndEndRadians=er(t.startAngle,t.endAngle),s=e[3]/2,o=e[2]/2,r=eu(this),a=this.nodeMap,l,n=a&&a[r],h={};this.shapeRoot=n&&n.shapeArgs,this.generatePoints(),ef(this,"afterTranslate");let d=this.tree=this.getTree(),p=ex((n=(a=this.nodeMap)[r]).parent)?n.parent:"",u=a[p],{from:c,to:g}=t5.getLevelFromAndTo(n);l=ed({from:c,levels:this.options.levels,to:g,defaults:{colorByPoint:t.colorByPoint,dataLabels:t.dataLabels,levelIsConstant:t.levelIsConstant,levelSize:t.levelSize,slicedOffset:t.slicedOffset}}),l=t5.calculateLevelSizes(l,{diffRadius:o-s,from:c,to:g}),ep(d,{before:ew,idRoot:r,levelIsConstant:t.levelIsConstant,mapOptionsToLevel:l,mapIdToNode:a,points:this.points,series:this});let v=a[""].shapeArgs={end:i.end,r:s,start:i.start,val:n.val,x:e[0],y:e[1]};for(let t of(this.setShapeArgs(u,v,l),this.mapOptionsToLevel=l,this.points))h[t.id]&&eg(31,!1,this.chart),h[t.id]=!0}}eA.defaultOptions=ey(en.defaultOptions,{center:["50%","50%"],clip:!1,colorByPoint:!1,opacity:1,dataLabels:{allowOverlap:!0,defer:!0,rotationMode:"circular",style:{textOverflow:"ellipsis"}},rootId:void 0,levelIsConstant:!0,levelSize:{value:1,unit:"weight"},slicedOffset:10}),ev(eA.prototype,{axisTypes:[],drawDataLabels:ea,getCenter:eo,isCartesian:!1,onPointSupported:!0,pointAttribs:el.prototype.pointAttribs,pointClass:tJ,NodeClass:class extends X{},utils:t5}),G().registerSeriesType("sunburst",eA);let eR=g();eR.Breadcrumbs=eR.Breadcrumbs||N,eR.Breadcrumbs.compose(eR.Chart,eR.defaultOptions);let eS=g();return u.default})());