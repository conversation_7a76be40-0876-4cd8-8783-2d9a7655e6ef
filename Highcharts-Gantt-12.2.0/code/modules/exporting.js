!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts,e._Highcharts.AST,e._Highcharts.Chart):"function"==typeof define&&define.amd?define("highcharts/modules/exporting",["highcharts/highcharts"],function(e){return t(e,e.AST,e.Chart)}):"object"==typeof exports?exports["highcharts/modules/exporting"]=t(e._Highcharts,e._Highcharts.AST,e._Highcharts.Chart):e.Highcharts=t(e.Highcharts,e.Highcharts.AST,e.Highcharts.Chart)}("undefined"==typeof window?this:window,(e,t,n)=>(()=>{"use strict";var i,o,r,s={660:e=>{e.exports=t},944:t=>{t.exports=e},960:e=>{e.exports=n}},l={};function a(e){var t=l[e];if(void 0!==t)return t.exports;var n=l[e]={exports:{}};return s[e](n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var c={};a.d(c,{default:()=>_});var p=a(944),h=a.n(p),u=a(660),d=a.n(u);a(960),function(e){e.compose=function(e){return e.navigation||(e.navigation=new t(e)),e};class t{constructor(e){this.updates=[],this.chart=e}addUpdate(e){this.chart.navigation.updates.push(e)}update(e,t){this.updates.forEach(n=>{n.call(this.chart,e,t)})}}e.Additions=t}(i||(i={}));let g=i,{isTouchDevice:f}=h(),m={exporting:{allowTableSorting:!0,type:"image/png",url:`https://export-svg.highcharts.com?v=${h().version}`,pdfFont:{normal:void 0,bold:void 0,bolditalic:void 0,italic:void 0},printMaxWidth:780,scale:2,buttons:{contextButton:{className:"highcharts-contextbutton",menuClassName:"highcharts-contextmenu",symbol:"menu",titleKey:"contextButtonTitle",menuItems:["viewFullscreen","printChart","separator","downloadPNG","downloadJPEG","downloadSVG"]}},menuItemDefinitions:{viewFullscreen:{textKey:"viewFullscreen",onclick:function(){this.fullscreen&&this.fullscreen.toggle()}},printChart:{textKey:"printChart",onclick:function(){this.print()}},separator:{separator:!0},downloadPNG:{textKey:"downloadPNG",onclick:function(){this.exportChart()}},downloadJPEG:{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},downloadPDF:{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},downloadSVG:{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}}},lang:{viewFullscreen:"View in full screen",exitFullscreen:"Exit from full screen",printChart:"Print chart",downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"},navigation:{buttonOptions:{symbolSize:14,symbolX:14.5,symbolY:13.5,align:"right",buttonSpacing:5,height:28,y:-5,verticalAlign:"top",width:28,symbolFill:"#666666",symbolStroke:"#666666",symbolStrokeWidth:3,theme:{fill:"#ffffff",padding:5,stroke:"none","stroke-linecap":"round"}},menuStyle:{border:"none",borderRadius:"3px",background:"#ffffff",padding:"0.5em"},menuItemStyle:{background:"none",borderRadius:"3px",color:"#333333",padding:"0.5em",fontSize:f?"0.9em":"0.8em",transition:"background 250ms, color 250ms"},menuItemHoverStyle:{background:"#f2f2f2"}}};!function(e){let t=[];function n(e,t,n,i){return[["M",e,t+2.5],["L",e+n,t+2.5],["M",e,t+i/2+.5],["L",e+n,t+i/2+.5],["M",e,t+i-1.5],["L",e+n,t+i-1.5]]}function i(e,t,n,i){let o=i/3-2,r=[];return r.concat(this.circle(n-o,t,o,o),this.circle(n-o,t+o+4,o,o),this.circle(n-o,t+2*(o+4),o,o))}e.compose=function(e){if(-1===t.indexOf(e)){t.push(e);let o=e.prototype.symbols;o.menu=n,o.menuball=i.bind(o)}}}(o||(o={}));let x=o,{composed:y}=h(),{addEvent:b,fireEvent:v,pushUnique:w}=h();function S(){this.fullscreen=new E(this)}class E{static compose(e){w(y,"Fullscreen")&&b(e,"beforeRender",S)}constructor(e){this.chart=e,this.isOpen=!1;let t=e.renderTo;!this.browserProps&&("function"==typeof t.requestFullscreen?this.browserProps={fullscreenChange:"fullscreenchange",requestFullscreen:"requestFullscreen",exitFullscreen:"exitFullscreen"}:t.mozRequestFullScreen?this.browserProps={fullscreenChange:"mozfullscreenchange",requestFullscreen:"mozRequestFullScreen",exitFullscreen:"mozCancelFullScreen"}:t.webkitRequestFullScreen?this.browserProps={fullscreenChange:"webkitfullscreenchange",requestFullscreen:"webkitRequestFullScreen",exitFullscreen:"webkitExitFullscreen"}:t.msRequestFullscreen&&(this.browserProps={fullscreenChange:"MSFullscreenChange",requestFullscreen:"msRequestFullscreen",exitFullscreen:"msExitFullscreen"}))}close(){let e=this,t=e.chart,n=t.options.chart;v(t,"fullscreenClose",null,function(){e.isOpen&&e.browserProps&&t.container.ownerDocument instanceof Document&&t.container.ownerDocument[e.browserProps.exitFullscreen](),e.unbindFullscreenEvent&&(e.unbindFullscreenEvent=e.unbindFullscreenEvent()),t.setSize(e.origWidth,e.origHeight,!1),e.origWidth=void 0,e.origHeight=void 0,n.width=e.origWidthOption,n.height=e.origHeightOption,e.origWidthOption=void 0,e.origHeightOption=void 0,e.isOpen=!1,e.setButtonText()})}open(){let e=this,t=e.chart,n=t.options.chart;v(t,"fullscreenOpen",null,function(){if(n&&(e.origWidthOption=n.width,e.origHeightOption=n.height),e.origWidth=t.chartWidth,e.origHeight=t.chartHeight,e.browserProps){let n=b(t.container.ownerDocument,e.browserProps.fullscreenChange,function(){e.isOpen?(e.isOpen=!1,e.close()):(t.setSize(null,null,!1),e.isOpen=!0,e.setButtonText())}),i=b(t,"destroy",n);e.unbindFullscreenEvent=()=>{n(),i()};let o=t.renderTo[e.browserProps.requestFullscreen]();o&&o.catch(function(){alert("Full screen is not supported inside a frame.")})}})}setButtonText(){let e=this.chart,t=e.exportDivElements,n=e.options.exporting,i=n&&n.buttons&&n.buttons.contextButton.menuItems,o=e.options.lang;if(n&&n.menuItemDefinitions&&o&&o.exitFullscreen&&o.viewFullscreen&&i&&t){let e=t[i.indexOf("viewFullscreen")];e&&d().setElementHTML(e,this.isOpen?o.exitFullscreen:n.menuItemDefinitions.viewFullscreen.text||o.viewFullscreen)}}toggle(){this.isOpen?this.close():this.open()}}let{win:C}=h(),{discardElement:O,objectEach:T}=h(),F={ajax:function(e){let t={json:"application/json",xml:"application/xml",text:"text/plain",octet:"application/octet-stream"},n=new XMLHttpRequest;function i(t,n){e.error&&e.error(t,n)}if(!e.url)return!1;n.open((e.type||"get").toUpperCase(),e.url,!0),e.headers?.["Content-Type"]||n.setRequestHeader("Content-Type",t[e.dataType||"json"]||t.text),T(e.headers,function(e,t){n.setRequestHeader(t,e)}),e.responseType&&(n.responseType=e.responseType),n.onreadystatechange=function(){let t;if(4===n.readyState){if(200===n.status){if("blob"!==e.responseType&&(t=n.responseText,"json"===e.dataType))try{t=JSON.parse(t)}catch(e){if(e instanceof Error)return i(n,e)}return e.success?.(t,n)}i(n,n.responseText)}},e.data&&"string"!=typeof e.data&&(e.data=JSON.stringify(e.data)),n.send(e.data)},getJSON:function(e,t){F.ajax({url:e,success:t,dataType:"json",headers:{"Content-Type":"text/plain"}})},post:function(e,t,n){let i=new C.FormData;T(t,function(e,t){i.append(t,e)}),i.append("b64","true");let{filename:o,type:r}=t;return C.fetch(e,{method:"POST",body:i,...n}).then(e=>{e.ok&&e.text().then(e=>{let t=document.createElement("a");t.href=`data:${r};base64,${e}`,t.download=o,t.click(),O(t)})})}},{defaultOptions:P}=h(),{doc:M,SVG_NS:k,win:H}=h(),{addEvent:N,css:D,createElement:G,discardElement:I,extend:W,find:R,fireEvent:V,isObject:j,merge:q,objectEach:A,pick:z,removeEvent:L,splat:$,uniqueKey:K}=h();!function(e){let t,n=[/-/,/^(clipPath|cssText|d|height|width)$/,/^font$/,/[lL]ogical(Width|Height)$/,/^parentRule$/,/^(cssRules|ownerRules)$/,/perspective/,/TapHighlightColor/,/^transition/,/^length$/,/^\d+$/],i=["fill","stroke","strokeLinecap","strokeLinejoin","strokeWidth","textAnchor","x","y"];e.inlineAllowlist=[];let o=["clipPath","defs","desc"];function r(e){let t,n,i=this,o=i.renderer,r=q(i.options.navigation.buttonOptions,e),s=r.onclick,l=r.menuItems,a=r.symbolSize||12;if(i.btnCount||(i.btnCount=0),i.exportDivElements||(i.exportDivElements=[],i.exportSVGElements=[]),!1===r.enabled||!r.theme)return;let c=i.styledMode?{}:r.theme;s?n=function(e){e&&e.stopPropagation(),s.call(i,e)}:l&&(n=function(e){e&&e.stopPropagation(),i.contextMenu(p.menuClassName,l,p.translateX||0,p.translateY||0,p.width||0,p.height||0,p),p.setState(2)}),r.text&&r.symbol?c.paddingLeft=z(c.paddingLeft,30):r.text||W(c,{width:r.width,height:r.height,padding:0});let p=o.button(r.text,0,0,n,c,void 0,void 0,void 0,void 0,r.useHTML).addClass(e.className).attr({title:z(i.options.lang[r._titleKey||r.titleKey],"")});p.menuClassName=e.menuClassName||"highcharts-menu-"+i.btnCount++,r.symbol&&(t=o.symbol(r.symbol,Math.round((r.symbolX||0)-a/2),Math.round((r.symbolY||0)-a/2),a,a,{width:a,height:a}).addClass("highcharts-button-symbol").attr({zIndex:1}).add(p),i.styledMode||t.attr({stroke:r.symbolStroke,fill:r.symbolFill,"stroke-width":r.symbolStrokeWidth||1})),p.add(i.exportingGroup).align(W(r,{width:p.width,x:z(r.x,i.buttonOffset)}),!0,"spacingBox"),i.buttonOffset+=((p.width||0)+r.buttonSpacing)*("right"===r.align?-1:1),i.exportSVGElements.push(p,t)}function s(){if(!this.printReverseInfo)return;let{childNodes:e,origDisplay:n,resetParams:i}=this.printReverseInfo;this.moveContainers(this.renderTo),[].forEach.call(e,function(e,t){1===e.nodeType&&(e.style.display=n[t]||"")}),this.isPrinting=!1,i&&this.setSize.apply(this,i),delete this.printReverseInfo,t=void 0,V(this,"afterPrint")}function l(){let e=M.body,t=this.options.exporting.printMaxWidth,n={childNodes:e.childNodes,origDisplay:[],resetParams:void 0};this.isPrinting=!0,this.pointer?.reset(void 0,0),V(this,"beforePrint"),t&&this.chartWidth>t&&(n.resetParams=[this.options.chart.width,void 0,!1],this.setSize(t,void 0,!1)),[].forEach.call(n.childNodes,function(e,t){1===e.nodeType&&(n.origDisplay[t]=e.style.display,e.style.display="none")}),this.moveContainers(e),this.printReverseInfo=n}function a(e){e.renderExporting(),N(e,"redraw",e.renderExporting),N(e,"destroy",e.destroyExport)}function c(e,t,n,i,o,r,s){let l=this,a=l.options.navigation,c=l.chartWidth,p=l.chartHeight,u="cache-"+e,g=Math.max(o,r),f,m=l[u];m||(l.exportContextMenu=l[u]=m=G("div",{className:e},{position:"absolute",zIndex:1e3,padding:g+"px",pointerEvents:"auto",...l.renderer.style},l.scrollablePlotArea?.fixedDiv||l.container),f=G("ul",{className:"highcharts-menu"},l.styledMode?{}:{listStyle:"none",margin:0,padding:0},m),l.styledMode||D(f,W({MozBoxShadow:"3px 3px 10px #888",WebkitBoxShadow:"3px 3px 10px #888",boxShadow:"3px 3px 10px #888"},a.menuStyle)),m.hideMenu=function(){D(m,{display:"none"}),s&&s.setState(0),l.openMenu=!1,D(l.renderTo,{overflow:"hidden"}),D(l.container,{overflow:"hidden"}),h().clearTimeout(m.hideTimer),V(l,"exportMenuHidden")},l.exportEvents.push(N(m,"mouseleave",function(){m.hideTimer=H.setTimeout(m.hideMenu,500)}),N(m,"mouseenter",function(){h().clearTimeout(m.hideTimer)}),N(M,"mouseup",function(t){l.pointer?.inClass(t.target,e)||m.hideMenu()}),N(m,"click",function(){l.openMenu&&m.hideMenu()})),t.forEach(function(e){if("string"==typeof e&&(e=l.options.exporting.menuItemDefinitions[e]),j(e,!0)){let t;e.separator?t=G("hr",void 0,void 0,f):("viewData"===e.textKey&&l.isDataTableVisible&&(e.textKey="hideData"),t=G("li",{className:"highcharts-menu-item",onclick:function(t){t&&t.stopPropagation(),m.hideMenu(),"string"!=typeof e&&e.onclick&&e.onclick.apply(l,arguments)}},void 0,f),d().setElementHTML(t,e.text||l.options.lang[e.textKey]),l.styledMode||(t.onmouseover=function(){D(this,a.menuItemHoverStyle)},t.onmouseout=function(){D(this,a.menuItemStyle)},D(t,W({cursor:"pointer"},a.menuItemStyle||{})))),l.exportDivElements.push(t)}}),l.exportDivElements.push(f,m),l.exportMenuWidth=m.offsetWidth,l.exportMenuHeight=m.offsetHeight);let x={display:"block"};n+(l.exportMenuWidth||0)>c?x.right=c-n-o-g+"px":x.left=n-g+"px",i+r+(l.exportMenuHeight||0)>p&&s.alignOptions?.verticalAlign!=="top"?x.bottom=p-i-g+"px":x.top=i+r-g+"px",D(m,x),D(l.renderTo,{overflow:""}),D(l.container,{overflow:""}),l.openMenu=!0,V(l,"exportMenuShown")}function p(e){let t,n=e?e.target:this,i=n.exportSVGElements,o=n.exportDivElements,r=n.exportEvents;i&&(i.forEach((e,o)=>{e&&(e.onclick=e.ontouchstart=null,n[t="cache-"+e.menuClassName]&&delete n[t],i[o]=e.destroy())}),i.length=0),n.exportingGroup&&(n.exportingGroup.destroy(),delete n.exportingGroup),o&&(o.forEach(function(e,t){e&&(h().clearTimeout(e.hideTimer),L(e,"mouseleave"),o[t]=e.onmouseout=e.onmouseover=e.ontouchstart=e.onclick=null,I(e))}),o.length=0),r&&(r.forEach(function(e){e()}),r.length=0)}function u(e,t){let n=this.getSVGForExport(e,t);e=q(this.options.exporting,e),F.post(e.url,{filename:e.filename?e.filename.replace(/\//g,"-"):this.getFilename(),type:e.type,width:e.width,scale:e.scale,svg:n},e.fetchOptions)}function f(e){return e&&this.inlineStyles(),this.resolveCSSVariables(),this.container.innerHTML}function y(){let e=this.userOptions.title&&this.userOptions.title.text,t=this.options.exporting.filename;return t?t.replace(/\//g,"-"):("string"==typeof e&&(t=e.toLowerCase().replace(/<\/?[^>]+(>|$)/g,"").replace(/[\s_]+/g,"-").replace(/[^a-z\d\-]/g,"").replace(/^[\-]+/g,"").replace(/[\-]+/g,"-").substr(0,24).replace(/[\-]+$/g,"")),(!t||t.length<5)&&(t="chart"),t)}function b(e){let t,n,i=q(this.options,e);i.plotOptions=q(this.userOptions.plotOptions,e&&e.plotOptions),i.time=q(this.userOptions.time,e&&e.time);let o=G("div",null,{position:"absolute",top:"-9999em",width:this.chartWidth+"px",height:this.chartHeight+"px"},M.body),r=this.renderTo.style.width,s=this.renderTo.style.height,l=i.exporting.sourceWidth||i.chart.width||/px$/.test(r)&&parseInt(r,10)||(i.isGantt?800:600),a=i.exporting.sourceHeight||i.chart.height||/px$/.test(s)&&parseInt(s,10)||400;W(i.chart,{animation:!1,renderTo:o,forExport:!0,renderer:"SVGRenderer",width:l,height:a}),i.exporting.enabled=!1,delete i.data,i.series=[],this.series.forEach(function(e){(n=q(e.userOptions,{animation:!1,enableMouseTracking:!1,showCheckbox:!1,visible:e.visible})).isInternal||i.series.push(n)});let c={};this.axes.forEach(function(e){e.userOptions.internalKey||(e.userOptions.internalKey=K()),e.options.isInternal||(c[e.coll]||(c[e.coll]=!0,i[e.coll]=[]),i[e.coll].push(q(e.userOptions,{visible:e.visible,type:e.type,uniqueNames:e.uniqueNames})))}),i.colorAxis=this.userOptions.colorAxis;let p=new this.constructor(i,this.callback);return e&&["xAxis","yAxis","series"].forEach(function(t){let n={};e[t]&&(n[t]=e[t],p.update(n))}),this.axes.forEach(function(t){let n=R(p.axes,e=>e.options.internalKey===t.userOptions.internalKey);if(n){let i=t.getExtremes(),o=$(e?.[t.coll]||{})[0],r="min"in o?o.min:i.userMin,s="max"in o?o.max:i.userMax;(void 0!==r&&r!==n.min||void 0!==s&&s!==n.max)&&n.setExtremes(r??void 0,s??void 0,!0,!1)}}),t=p.getChartHTML(this.styledMode||i.exporting?.applyStyleSheets),V(this,"getSVG",{chartCopy:p}),t=this.sanitizeSVG(t,i),i=null,p.destroy(),I(o),t}function v(e,t){let n=this.options.exporting;return this.getSVG(q({chart:{borderRadius:0}},n.chartOptions,t,{exporting:{sourceWidth:e&&e.sourceWidth||n.sourceWidth,sourceHeight:e&&e.sourceHeight||n.sourceHeight}}))}function w(){let t,r=e.inlineAllowlist,s={},l=M.createElement("iframe");D(l,{width:"1px",height:"1px",visibility:"hidden"}),M.body.appendChild(l);let a=l.contentWindow&&l.contentWindow.document;a&&a.body.appendChild(a.createElementNS(k,"svg")),!function e(l){let c,p,u,d,g,f,m={};if(a&&1===l.nodeType&&-1===o.indexOf(l.nodeName)){if(c=H.getComputedStyle(l,null),p="svg"===l.nodeName?{}:H.getComputedStyle(l.parentNode,null),!s[l.nodeName]){t=a.getElementsByTagName("svg")[0],u=a.createElementNS(l.namespaceURI,l.nodeName),t.appendChild(u);let e=H.getComputedStyle(u,null),n={};for(let t in e)t.length<1e3&&"string"==typeof e[t]&&!/^\d+$/.test(t)&&(n[t]=e[t]);s[l.nodeName]=n,"text"===l.nodeName&&delete s.text.fill,t.removeChild(u)}for(let e in c)(h().isFirefox||h().isMS||h().isSafari||Object.hasOwnProperty.call(c,e))&&function(e,t){if(d=g=!1,r.length){for(f=r.length;f--&&!g;)g=r[f].test(t);d=!g}for("transform"===t&&"none"===e&&(d=!0),f=n.length;f--&&!d;){if(t.length>1e3)throw Error("Input too long");d=n[f].test(t)||"function"==typeof e}!d&&(p[t]!==e||"svg"===l.nodeName)&&s[l.nodeName][t]!==e&&(i&&-1===i.indexOf(t)?m[t]=e:e&&l.setAttribute(t.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}),e))}(c[e],e);if(D(l,m),"svg"===l.nodeName&&l.setAttribute("stroke-width","1px"),"text"===l.nodeName)return;[].forEach.call(l.children||l.childNodes,e)}}(this.container.querySelector("svg")),t.parentNode.removeChild(t),l.parentNode.removeChild(l)}function S(){let e=this.container.querySelectorAll("*"),t=["color","fill","stop-color","stroke"];Array.from(e).forEach(e=>{t.forEach(t=>{let n=e.getAttribute(t);n?.includes("var(")&&e.setAttribute(t,getComputedStyle(e).getPropertyValue(t))})})}function C(e){let{scrollablePlotArea:t}=this;(t?[t.fixedDiv,t.scrollingContainer]:[this.container]).forEach(function(t){e.appendChild(t)})}function O(){let e=this,t=(t,n,i)=>{e.isDirtyExporting=!0,q(!0,e.options[t],n),z(i,!0)&&e.redraw()};e.exporting={update:function(e,n){t("exporting",e,n)}},g.compose(e).navigation.addUpdate((e,n)=>{t("navigation",e,n)})}function T({alignTo:e,key:t,textPxLength:n}){let i=this.options.exporting,{align:o,buttonSpacing:r=0,verticalAlign:s,width:l=0}=q(this.options.navigation?.buttonOptions,i?.buttons?.contextButton),a=e.width-n,c=l+r;(i?.enabled??!0)&&"title"===t&&"right"===o&&"top"===s&&a<2*c&&(a<c?e.width-=c:this.title?.alignValue!=="left"&&(e.x-=c-a/2))}function B(){let e=this;!e.isPrinting&&(t=e,h().isSafari||e.beforePrint(),setTimeout(()=>{H.focus(),H.print(),h().isSafari||setTimeout(()=>{e.afterPrint()},1e3)},1))}function J(){let e=this,t=e.options.exporting,n=t.buttons,i=e.isDirtyExporting||!e.exportSVGElements;e.buttonOffset=0,e.isDirtyExporting&&e.destroyExport(),i&&!1!==t.enabled&&(e.exportEvents=[],e.exportingGroup=e.exportingGroup||e.renderer.g("exporting-group").attr({zIndex:3}).add(),A(n,function(t){e.addButton(t)}),e.isDirtyExporting=!1)}function _(e,t){let n=e.indexOf("</svg>")+6,i=e.indexOf("<foreignObject")>-1,o=e.substr(n);return e=e.substr(0,n),i?e=e.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />"):o&&t?.exporting?.allowHTML&&(o='<foreignObject x="0" y="0" width="'+t.chart.width+'" height="'+t.chart.height+'"><body xmlns="http://www.w3.org/1999/xhtml">'+o.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />")+"</body></foreignObject>",e=e.replace("</svg>",o+"</svg>")),e=e.replace(/zIndex="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery\d+="[^"]+"/g,"").replace(/url\(("|&quot;)(.*?)("|&quot;)\;?\)/g,"url($2)").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ (NS\d+\:)?href=/g," xlink:href=").replace(/\n+/g," ").replace(/&nbsp;/g,"\xa0").replace(/&shy;/g,"\xad")}e.compose=function(e,n){x.compose(n),E.compose(e);let i=e.prototype;i.exportChart||(i.afterPrint=s,i.exportChart=u,i.inlineStyles=w,i.print=B,i.sanitizeSVG=_,i.getChartHTML=f,i.getSVG=b,i.getSVGForExport=v,i.getFilename=y,i.moveContainers=C,i.beforePrint=l,i.contextMenu=c,i.addButton=r,i.destroyExport=p,i.renderExporting=J,i.resolveCSSVariables=S,i.callbacks.push(a),N(e,"init",O),N(e,"layOutTitle",T),h().isSafari&&H.matchMedia("print").addListener(function(e){t&&(e.matches?t.beforePrint():t.afterPrint())}),P.exporting=q(m.exporting,P.exporting),P.lang=q(m.lang,P.lang),P.navigation=q(m.navigation,P.navigation))}}(r||(r={}));let B=r,J=h();J.HttpUtilities=J.HttpUtilities||F,J.ajax=J.HttpUtilities.ajax,J.getJSON=J.HttpUtilities.getJSON,J.post=J.HttpUtilities.post,B.compose(J.Chart,J.Renderer);let _=h();return c.default})());