!function(t,o){"object"==typeof exports&&"object"==typeof module?module.exports=o(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.SVGRenderer):"function"==typeof define&&define.amd?define("highcharts/modules/dumbbell",["highcharts/highcharts"],function(t){return o(t,t.SeriesRegistry,t.SVGRenderer)}):"object"==typeof exports?exports["highcharts/modules/dumbbell"]=o(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.SVGRenderer):t.Highcharts=o(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.SVGRenderer)}("undefined"==typeof window?this:window,(t,o,e)=>(()=>{"use strict";var r={512:t=>{t.exports=o},540:t=>{t.exports=e},944:o=>{o.exports=t}},i={};function s(t){var o=i[t];if(void 0!==o)return o.exports;var e=i[t]={exports:{}};return r[t](e,e.exports,s),e.exports}s.n=t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return s.d(o,{a:o}),o},s.d=(t,o)=>{for(var e in o)s.o(o,e)&&!s.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:o[e]})},s.o=(t,o)=>Object.prototype.hasOwnProperty.call(t,o);var h={};s.d(h,{default:()=>P});var l=s(944),a=s.n(l),n=s(512),p=s.n(n);let{area:{prototype:{pointClass:c,pointClass:{prototype:d}}}}=p().seriesTypes,{defined:g,isNumber:y}=a(),u=class extends c{setState(){let t=this.state,o=this.series,e=o.chart.polar;g(this.plotHigh)||(this.plotHigh=o.yAxis.toPixels(this.high,!0)),g(this.plotLow)||(this.plotLow=this.plotY=o.yAxis.toPixels(this.low,!0)),o.lowerStateMarkerGraphic=o.stateMarkerGraphic,o.stateMarkerGraphic=o.upperStateMarkerGraphic,this.graphic=this.graphics&&this.graphics[1],this.plotY=this.plotHigh,e&&y(this.plotHighX)&&(this.plotX=this.plotHighX),d.setState.apply(this,arguments),this.state=t,this.plotY=this.plotLow,this.graphic=this.graphics&&this.graphics[0],e&&y(this.plotLowX)&&(this.plotX=this.plotLowX),o.upperStateMarkerGraphic=o.stateMarkerGraphic,o.stateMarkerGraphic=o.lowerStateMarkerGraphic,o.lowerStateMarkerGraphic=void 0;let r=o.modifyMarkerSettings();d.setState.apply(this,arguments),o.restoreMarkerSettings(r)}haloPath(){let t=this.series.chart.polar,o=[];return this.plotY=this.plotLow,t&&y(this.plotLowX)&&(this.plotX=this.plotLowX),this.isInside&&(o=d.haloPath.apply(this,arguments)),this.plotY=this.plotHigh,t&&y(this.plotHighX)&&(this.plotX=this.plotHighX),this.isTopInside&&(o=o.concat(d.haloPath.apply(this,arguments))),o}isValid(){return y(this.low)&&y(this.high)}},{extend:f,pick:w}=a();class C extends u{setState(){let t=this.series,o=t.chart,e=t.options.lowColor,r=t.options.marker,i=t.options.lowMarker,s=this.options,h=s.lowColor,l=this.zone&&this.zone.color,a=w(h,i?.fillColor,e,s.color,l,this.color,t.color),n="attr",p,c;if(this.pointSetState.apply(this,arguments),!this.state){n="animate";let[t,e]=this.graphics||[];t&&!o.styledMode&&(t.attr({fill:a}),e&&(c={y:this.y,zone:this.zone},this.y=this.high,this.zone=this.zone?this.getZone():void 0,p=w(this.marker?this.marker.fillColor:void 0,r?r.fillColor:void 0,s.color,this.zone?this.zone.color:void 0,this.color),e.attr({fill:p}),f(this,c)))}this.connector?.[n](t.getConnectorAttribs(this))}destroy(){return this.graphic||(this.graphic=this.connector,this.connector=void 0),super.destroy()}}f(C.prototype,{pointSetState:u.prototype.setState});var m=s(540),S=s.n(m);let{noop:x}=a(),{arearange:k,column:v,columnrange:M}=p().seriesTypes,{extend:b,merge:X,pick:z}=a();class H extends k{getConnectorAttribs(t){let o=this.chart,e=t.options,r=this.options,i=this.xAxis,s=this.yAxis,h=z(r.states&&r.states.hover&&r.states.hover.connectorWidthPlus,1),l=z(e.dashStyle,r.dashStyle),a=s.toPixels(r.threshold||0,!0),n=o.inverted?s.len-a:a,p=z(e.connectorWidth,r.connectorWidth),c=z(e.connectorColor,r.connectorColor,e.color,t.zone?t.zone.color:void 0,t.color),d=z(t.plotLow,t.plotY),g=z(t.plotHigh,n),y;if("number"!=typeof d)return{};t.state&&(p+=h),d<0?d=0:d>=s.len&&(d=s.len),g<0?g=0:g>=s.len&&(g=s.len),(t.plotX<0||t.plotX>i.len)&&(p=0),t.graphics&&t.graphics[1]&&(y={y:t.y,zone:t.zone},t.y=t.high,t.zone=t.zone?t.getZone():void 0,c=z(e.connectorColor,r.connectorColor,e.color,t.zone?t.zone.color:void 0,t.color),b(t,y));let u={d:S().prototype.crispLine([["M",t.plotX,d],["L",t.plotX,g]],p)};return!o.styledMode&&(u.stroke=c,u["stroke-width"]=p,l&&(u.dashstyle=l)),u}drawConnector(t){let o=z(this.options.animationLimit,250),e=t.connector&&this.chart.pointCount<o?"animate":"attr";t.connector||(t.connector=this.chart.renderer.path().addClass("highcharts-lollipop-stem").attr({zIndex:-1}).add(this.group)),t.connector[e](this.getConnectorAttribs(t))}getColumnMetrics(){let t=v.prototype.getColumnMetrics.apply(this,arguments);return t.offset+=t.width/2,t}translate(){let t=this.chart.inverted;for(let o of(this.setShapeArgs.apply(this),this.translatePoint.apply(this,arguments),this.points)){let{pointWidth:e,shapeArgs:r={},tooltipPos:i}=o;o.plotX=r.x||0,r.x=o.plotX-e/2,i&&(t?i[1]=this.xAxis.len-o.plotX:i[0]=o.plotX)}this.columnMetrics.offset-=this.columnMetrics.width/2}drawPoints(){let t=this.chart,o=this.points.length,e=this.lowColor=this.options.lowColor,r=this.options.lowMarker,i=0,s,h,l;for(this.seriesDrawPoints.apply(this,arguments);i<o;){let[o,a]=(h=this.points[i]).graphics||[];this.drawConnector(h),a&&(a.element.point=h,a.addClass("highcharts-lollipop-high")),h.connector&&(h.connector.element.point=h),o&&(l=h.zone&&h.zone.color,s=z(h.options.lowColor,r?.fillColor,e,h.options.color,l,h.color,this.color),t.styledMode||o.attr({fill:s}),o.addClass("highcharts-lollipop-low")),i++}}pointAttribs(t,o){let e=super.pointAttribs.apply(this,arguments);return"hover"===o&&delete e.fill,e}setShapeArgs(){v.prototype.translate.apply(this),M.prototype.afterColumnTranslate.apply(this)}}H.defaultOptions=X(k.defaultOptions,{trackByArea:!1,fillColor:"none",lineWidth:0,pointRange:1,connectorWidth:1,stickyTracking:!1,groupPadding:.2,crisp:!1,pointPadding:.1,legendSymbol:"rectangle",lowColor:"#333333",states:{hover:{lineWidthPlus:0,connectorWidthPlus:1,halo:!1}}}),b(H.prototype,{crispCol:v.prototype.crispCol,drawGraph:x,drawTracker:v.prototype.drawTracker,pointClass:C,seriesDrawPoints:k.prototype.drawPoints,trackerGroups:["group","markerGroup","dataLabelsGroup"],translatePoint:k.prototype.translate}),p().registerSeriesType("dumbbell",H);let P=a();return h.default})());