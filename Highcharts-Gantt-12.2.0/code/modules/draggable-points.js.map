{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/draggable-points\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/draggable-points\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/draggable-points\"] = factory(root[\"_Highcharts\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ draggable_points_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Extensions/DraggablePoints/DragDropUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add multiple event listeners with the same handler to the same element.\n *\n * @private\n * @function addEvents\n * @param {T} el\n *        The element or object to add listeners to.\n * @param {Array<string>} types\n *        Array with the event types this handler should apply to.\n * @param {Function|Highcharts.EventCallbackFunction<T>} fn\n *        The function callback to execute when the events are fired.\n * @param {Highcharts.EventOptionsObject} [options]\n *        Event options:\n *        - `order`: The order the event handler should be called. This opens\n *          for having one handler be called before another, independent of in\n *          which order they were added.\n * @return {Function}\n *         A callback function to remove the added events.\n * @template T\n */\nfunction addEvents(el, types, fn, options) {\n    const removeFuncs = types.map((type) => addEvent(el, type, fn, options));\n    return function () {\n        for (const fn of removeFuncs) {\n            fn();\n        }\n    };\n}\n/**\n * Utility function to count the number of props in an object.\n *\n * @private\n * @function countProps\n *\n * @param {Object} obj\n *        The object to count.\n *\n * @return {number}\n *         Number of own properties on the object.\n */\nfunction countProps(obj) {\n    return Object.keys(obj).length;\n}\n/**\n * Utility function to get the value of the first prop of an object. (Note that\n * the order of keys in an object is usually not guaranteed.)\n *\n * @private\n * @function getFirstProp\n * @param {Highcharts.Dictionary<T>} obj\n *        The object to count.\n * @return {T}\n *         Value of the first prop in the object.\n * @template T\n */\nfunction getFirstProp(obj) {\n    for (const p in obj) {\n        if (Object.hasOwnProperty.call(obj, p)) {\n            return obj[p];\n        }\n    }\n}\n/**\n * Take a mouse/touch event and return the event object with chartX/chartY.\n *\n * @private\n * @function getNormalizedEvent\n * @param {global.PointerEvent} e\n *        The event to normalize.\n * @param {Highcharts.Chart} chart\n *        The related chart.\n * @return {Highcharts.PointerEventLObject}\n *         The normalized event.\n */\nfunction getNormalizedEvent(e, chart) {\n    return (typeof e.chartX === 'undefined' ||\n        typeof e.chartY === 'undefined' ?\n        chart.pointer?.normalize(e) || e :\n        e);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DragDropUtilities = {\n    addEvents,\n    countProps,\n    getFirstProp,\n    getNormalizedEvent\n};\n/* harmony default export */ const DraggablePoints_DragDropUtilities = (DragDropUtilities);\n\n;// ./code/es-modules/Extensions/DraggablePoints/DragDropDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The draggable-points module allows points to be moved around or modified in\n * the chart. In addition to the options mentioned under the `dragDrop` API\n * structure, the module fires three events,\n * [point.dragStart](plotOptions.series.point.events.dragStart),\n * [point.drag](plotOptions.series.point.events.drag) and\n * [point.drop](plotOptions.series.point.events.drop).\n *\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/resize-column\n *         Draggable column and line series\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/bar-series\n *         Draggable bar\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/drag-bubble\n *         Draggable bubbles\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/drag-xrange\n *         Draggable X range series\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/undraggable-points\n *         Dragging disabled for specific points\n * @sample {highmaps}\n *         maps/series/draggable-mappoint\n *         Draggable Map Point series\n *\n * @declare      Highcharts.SeriesDragDropOptionsObject\n * @since        6.2.0\n * @requires     modules/draggable-points\n * @optionparent plotOptions.series.dragDrop\n */\nconst DragDropDefaults = {\n    /**\n     * Set the minimum X value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number|string}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMinX\n     */\n    /**\n     * Set the maximum X value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number|string}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMaxX\n     */\n    /**\n     * Set the minimum Y value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMinY\n     */\n    /**\n     * Set the maximum Y value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMaxY\n     */\n    /**\n     * The X precision value to drag to for this series. Set to 0 to disable. By\n     * default this is disabled, except for category axes, where the default is\n     * `1`.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragPrecisionX\n     */\n    /**\n     * The Y precision value to drag to for this series. Set to 0 to disable. By\n     * default this is disabled, except for category axes, where the default is\n     * `1`.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragPrecisionY\n     */\n    /**\n     * Enable dragging in the X dimension.\n     *\n     * @type      {boolean}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.draggableX\n     */\n    /**\n     * Enable dragging in the Y dimension. Note that this is not supported for\n     * TreeGrid axes (the default axis type in Gantt charts).\n     *\n     * @type      {boolean}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.draggableY\n     */\n    /**\n     * Group the points by a property. Points with the same property value will\n     * be grouped together when moving.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Drag grouped points\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag grouped points\n     *\n     * @type      {string}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.groupBy\n     */\n    /**\n     * Update points as they are dragged. If false, a guide box is drawn to\n     * illustrate the new point size.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         liveRedraw disabled\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         liveRedraw disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.liveRedraw\n     */\n    /**\n     * Set a key to hold when dragging to zoom the chart. This is useful to\n     * avoid zooming while moving points. Should be set different than\n     * [chart.panKey](#chart.panKey).\n     *\n     * @type       {string}\n     * @since      6.2.0\n     * @validvalue [\"alt\", \"ctrl\", \"meta\", \"shift\"]\n     * @deprecated\n     * @requires  modules/draggable-points\n     * @apioption  chart.zoomKey\n     */\n    /**\n     * Callback that fires when starting to drag a point. The mouse event object\n     * is passed in as an argument. If a drag handle is used, `e.updateProp` is\n     * set to the data property being dragged. The `this` context is the point.\n     * See [drag and drop options](plotOptions.series.dragDrop).\n     *\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag events\n     *\n     * @type      {Highcharts.PointDragStartCallbackFunction}\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.series.point.events.dragStart\n     */\n    /**\n     * Callback that fires while dragging a point. The mouse event is passed in\n     * as parameter. The original data can be accessed from `e.origin`, and the\n     * new point values can be accessed from `e.newPoints`. If there is only a\n     * single point being updated, it can be accessed from `e.newPoint` for\n     * simplicity, and its ID can be accessed from `e.newPointId`. The `this`\n     * context is the point being dragged. To stop the default drag action,\n     * return false. See [drag and drop options](plotOptions.series.dragDrop).\n     *\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag events\n     * @sample {highcharts|highstock} highcharts/dragdrop/undraggable-points\n     *         Dragging disabled for specific points\n     *\n     * @type      {Highcharts.PointDragCallbackFunction}\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.series.point.events.drag\n     */\n    /**\n     * Callback that fires when the point is dropped. The parameters passed are\n     * the same as for [drag](#plotOptions.series.point.events.drag). To stop\n     * the default drop action, return false. See\n     * [drag and drop options](plotOptions.series.dragDrop).\n     *\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag events\n     * @sample {highcharts|highstock} highcharts/dragdrop/undraggable-points\n     *         Dragging disabled for specific points\n     *\n     * @type      {Highcharts.PointDropCallbackFunction}\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.series.point.events.drop\n     */\n    /**\n     * Point specific options for the draggable-points module. Overrides options\n     * on `series.dragDrop`.\n     *\n     * @declare   Highcharts.SeriesLineDataDragDropOptions\n     * @extends   plotOptions.series.dragDrop\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption series.line.data.dragDrop\n     */\n    /**\n     * The amount of pixels to drag the pointer before it counts as a drag\n     * operation. This prevents drag/drop to fire when just clicking or\n     * selecting points.\n     *\n     * @type      {number}\n     * @default   2\n     * @since     6.2.0\n     */\n    dragSensitivity: 2,\n    /**\n     * Options for the drag handles available in column series.\n     *\n     * @declare      Highcharts.DragDropHandleOptionsObject\n     * @since        6.2.0\n     * @optionparent plotOptions.column.dragDrop.dragHandle\n     */\n    dragHandle: {\n        /**\n         * Function to define the SVG path to use for the drag handles. Takes\n         * the point as argument. Should return an SVG path in array format. The\n         * SVG path is automatically positioned on the point.\n         *\n         * @type      {Function}\n         * @since     6.2.0\n         * @apioption plotOptions.column.dragDrop.dragHandle.pathFormatter\n         */\n        // pathFormatter: null,\n        /**\n         * The mouse cursor to use for the drag handles. By default this is\n         * intelligently switching between `ew-resize` and `ns-resize` depending on\n         * the direction the point is being dragged.\n         *\n         * @type      {string}\n         * @since     6.2.0\n         * @apioption plotOptions.column.dragDrop.dragHandle.cursor\n         */\n        // cursor: null,\n        /**\n         * The class name of the drag handles. Defaults to `highcharts-drag-handle`.\n         *\n         * @since 6.2.0\n         */\n        className: 'highcharts-drag-handle',\n        /**\n         * The fill color of the drag handles.\n         *\n         * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since 6.2.0\n         */\n        color: '#fff',\n        /**\n         * The line color of the drag handles.\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 6.2.0\n         */\n        lineColor: 'rgba(0, 0, 0, 0.6)',\n        /**\n         * The line width for the drag handles.\n         *\n         * @since 6.2.0\n         */\n        lineWidth: 1,\n        /**\n         * The z index for the drag handles.\n         *\n         * @since 6.2.0\n         */\n        zIndex: 901\n    },\n    /**\n     * Style options for the guide box. The guide box has one state by default,\n     * the `default` state.\n     *\n     * @declare Highcharts.PlotOptionsSeriesDragDropGuideBoxOptions\n     * @since 6.2.0\n     * @type  {Highcharts.Dictionary<Highcharts.DragDropGuideBoxOptionsObject>}\n     */\n    guideBox: {\n        /**\n         * Style options for the guide box default state.\n         *\n         * @declare Highcharts.DragDropGuideBoxOptionsObject\n         * @since   6.2.0\n         */\n        'default': {\n            /**\n             * CSS class name of the guide box in this state. Defaults to\n             * `highcharts-drag-box-default`.\n             *\n             * @since 6.2.0\n             */\n            className: 'highcharts-drag-box-default',\n            /**\n             * Width of the line around the guide box.\n             *\n             * @since 6.2.0\n             */\n            lineWidth: 1,\n            /**\n             * Color of the border around the guide box.\n             *\n             * @type  {Highcharts.ColorString}\n             * @since 6.2.0\n             */\n            lineColor: '#888',\n            /**\n             * Guide box fill color.\n             *\n             * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @since 6.2.0\n             */\n            color: 'rgba(0, 0, 0, 0.1)',\n            /**\n             * Guide box cursor.\n             *\n             * @since 6.2.0\n             */\n            cursor: 'move',\n            /**\n             * Guide box zIndex.\n             *\n             * @since 6.2.0\n             */\n            zIndex: 900\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DraggablePoints_DragDropDefaults = (DragDropDefaults);\n\n;// ./code/es-modules/Extensions/DraggablePoints/DraggableChart.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvents: DraggableChart_addEvents, countProps: DraggableChart_countProps, getFirstProp: DraggableChart_getFirstProp, getNormalizedEvent: DraggableChart_getNormalizedEvent } = DraggablePoints_DragDropUtilities;\n\n\nconst { doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { addEvent: DraggableChart_addEvent, isArray, merge, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add events to document and chart if the chart is draggable.\n *\n * @private\n * @function addDragDropEvents\n * @param {Highcharts.Chart} chart\n *        The chart to add events to.\n */\nfunction addDragDropEvents(chart) {\n    const container = chart.container;\n    // Only enable if we have a draggable chart\n    if (isChartDraggable(chart)) {\n        DraggableChart_addEvents(container, ['mousedown', 'touchstart'], (e) => {\n            mouseDown(DraggableChart_getNormalizedEvent(e, chart), chart);\n        });\n        DraggableChart_addEvents(container, ['mousemove', 'touchmove'], (e) => {\n            mouseMove(DraggableChart_getNormalizedEvent(e, chart), chart);\n        }, {\n            passive: false\n        });\n        DraggableChart_addEvent(container, 'mouseleave', (e) => {\n            mouseUp(DraggableChart_getNormalizedEvent(e, chart), chart);\n        });\n        chart.unbindDragDropMouseUp = DraggableChart_addEvents(doc, ['mouseup', 'touchend'], (e) => {\n            mouseUp(DraggableChart_getNormalizedEvent(e, chart), chart);\n        }, {\n            passive: false\n        });\n        // Add flag to avoid doing this again\n        chart.hasAddedDragDropEvents = true;\n        // Add cleanup to make sure we don't pollute document\n        DraggableChart_addEvent(chart, 'destroy', () => {\n            if (chart.unbindDragDropMouseUp) {\n                chart.unbindDragDropMouseUp();\n            }\n        });\n    }\n}\n/**\n * Remove the chart's drag handles if they exist.\n *\n * @private\n * @function Highcharts.Chart#hideDragHandles\n */\nfunction chartHideDragHandles() {\n    const chart = this, dragHandles = (chart.dragHandles || {});\n    if (dragHandles) {\n        for (const key of Object.keys(dragHandles)) {\n            if (dragHandles[key].destroy) {\n                dragHandles[key].destroy();\n            }\n        }\n        delete chart.dragHandles;\n    }\n}\n/**\n * Set the state of the guide box.\n *\n * @private\n * @function Highcharts.Chart#setGuideBoxState\n * @param {string} state\n *        The state to set the guide box to.\n * @param {Highcharts.Dictionary<Highcharts.DragDropGuideBoxOptionsObject>} [options]\n *        Additional overall guideBox options to consider.\n * @return {Highcharts.SVGElement}\n *         The modified guide box.\n */\nfunction chartSetGuideBoxState(state, options) {\n    const guideBox = this.dragGuideBox, guideBoxOptions = merge(DraggablePoints_DragDropDefaults.guideBox, options), stateOptions = merge(guideBoxOptions['default'], // eslint-disable-line dot-notation\n    guideBoxOptions[state]);\n    return guideBox\n        .attr({\n        'class': stateOptions.className,\n        stroke: stateOptions.lineColor,\n        strokeWidth: stateOptions.lineWidth,\n        fill: stateOptions.color,\n        cursor: stateOptions.cursor,\n        zIndex: stateOptions.zIndex\n    })\n        // Use pointerEvents 'none' to avoid capturing the click event\n        .css({ pointerEvents: 'none' });\n}\n/**\n * Check whether the zoomKey or panKey is pressed.\n *\n * @private\n * @function zoomOrPanKeyPressed\n * @param {global.Event} e\n *        A mouse event.\n * @return {boolean}\n *         True if the zoom or pan keys are pressed. False otherwise.\n */\nfunction chartZoomOrPanKeyPressed(e) {\n    // Check whether the panKey and zoomKey are set in chart.userOptions\n    const chart = this, chartOptions = chart.options.chart || {}, panKey = chartOptions.panKey && chartOptions.panKey + 'Key', zoomKey = chart.zooming.key && chart.zooming.key + 'Key';\n    return (e[zoomKey] || e[panKey]);\n}\n/**\n * Composes the chart class with essential functions to support draggable\n * points.\n *\n * @private\n * @function compose\n *\n * @param {Highcharts.Chart} ChartClass\n *        Class constructor of chart.\n */\nfunction compose(ChartClass) {\n    const chartProto = ChartClass.prototype;\n    if (!chartProto.hideDragHandles) {\n        chartProto.hideDragHandles = chartHideDragHandles;\n        chartProto.setGuideBoxState = chartSetGuideBoxState;\n        chartProto.zoomOrPanKeyPressed = chartZoomOrPanKeyPressed;\n        DraggableChart_addEvent(ChartClass, 'render', onChartRender);\n    }\n}\n/**\n * Default mouse move handler while dragging. Handles updating points or guide\n * box.\n *\n * @private\n * @function dragMove\n * @param {Highcharts.PointerEventObject} e\n *        The mouse move event.\n * @param {Highcharts.Point} point\n *        The point that is dragged.\n */\nfunction dragMove(e, point) {\n    const series = point.series, chart = series.chart, data = chart.dragDropData, options = merge(series.options.dragDrop, point.options.dragDrop), draggableX = options.draggableX, draggableY = options.draggableY, origin = data.origin, updateProp = data.updateProp;\n    let dX = e.chartX - origin.chartX, dY = e.chartY - origin.chartY;\n    const oldDx = dX;\n    // Handle inverted\n    if (chart.inverted) {\n        dX = -dY;\n        dY = -oldDx;\n    }\n    // If we have liveRedraw enabled, update the points immediately. Otherwise\n    // update the guideBox.\n    if (pick(options.liveRedraw, true)) {\n        updatePoints(chart, false);\n        // Update drag handles\n        point.showDragHandles();\n    }\n    else {\n        // No live redraw, update guide box\n        if (updateProp) {\n            // We are resizing, so resize the guide box\n            resizeGuideBox(point, dX, dY);\n        }\n        else {\n            // We are moving, so move the guide box\n            chart.dragGuideBox.translate(draggableX ? dX : 0, draggableY ? dY : 0);\n        }\n    }\n    // Update stored previous dX/Y\n    origin.prevdX = dX;\n    origin.prevdY = dY;\n}\n/**\n * Flip a side property, used with resizeRect. If input side is \"left\", return\n * \"right\" etc.\n *\n * @private\n * @function flipResizeSide\n *\n * @param {string} side\n *        Side prop to flip. Can be `left`, `right`, `top` or `bottom`.\n *\n * @return {\"bottom\"|\"left\"|\"right\"|\"top\"|undefined}\n *         The flipped side.\n */\nfunction flipResizeSide(side) {\n    return {\n        left: 'right',\n        right: 'left',\n        top: 'bottom',\n        bottom: 'top'\n    }[side];\n}\n/**\n * Get a list of points that are grouped with this point. If only one point is\n * in the group, that point is returned by itself in an array.\n *\n * @private\n * @function getGroupedPoints\n * @param {Highcharts.Point} point\n *        Point to find group from.\n * @return {Array<Highcharts.Point>}\n *         Array of points in this group.\n */\nfunction getGroupedPoints(point) {\n    const series = point.series, data = series.options.data || [], groupKey = series.options.dragDrop.groupBy;\n    let points = [];\n    if (series.boosted && isArray(data)) { // #11156\n        for (let i = 0, iEnd = data.length; i < iEnd; ++i) {\n            points.push(new series.pointClass(// eslint-disable-line new-cap\n            series, data[i]));\n            points[points.length - 1].index = i;\n        }\n    }\n    else {\n        points = series.points;\n    }\n    return point.options[groupKey] ?\n        // If we have a grouping option, filter the points by that\n        points.filter((comparePoint) => (comparePoint.options[groupKey] ===\n            point.options[groupKey])) :\n        // Otherwise return the point by itself only\n        [point];\n}\n/**\n * Calculate new point options from points being dragged.\n *\n * @private\n * @function getNewPoints\n *\n * @param {Object} dragDropData\n *        A chart's dragDropData with drag/drop origin information, and info on\n *        which points are being dragged.\n *\n * @param {Highcharts.PointerEventObject} newPos\n *        Event with the new position of the mouse (chartX/Y properties).\n *\n * @return {Highchats.Dictionary<object>}\n *         Hashmap with point.id mapped to an object with the original point\n *         reference, as well as the new data values.\n */\nfunction getNewPoints(dragDropData, newPos) {\n    const point = dragDropData.point, series = point.series, chart = series.chart, options = merge(series.options.dragDrop, point.options.dragDrop), updateProps = {}, resizeProp = dragDropData.updateProp, hashmap = {}, dragDropProps = point.series.dragDropProps;\n    // Go through the data props that can be updated on this series and find out\n    // which ones we want to update.\n    // eslint-disable-next-line guard-for-in\n    for (const key in dragDropProps) {\n        const val = dragDropProps[key];\n        // If we are resizing, skip if this key is not the correct one or it\n        // is not resizable.\n        if (resizeProp && (resizeProp !== key ||\n            !val.resize ||\n            val.optionName && options[val.optionName] === false)) {\n            continue;\n        }\n        // If we are resizing, we now know it is good. If we are moving, check\n        // that moving along this axis is enabled, and the prop is movable.\n        // If this prop is enabled, add it to be updated.\n        if (resizeProp || (val.move &&\n            (val.axis === 'x' && options.draggableX ||\n                val.axis === 'y' && options.draggableY))) {\n            if (chart.mapView) {\n                updateProps[key === 'x' ? 'lon' : 'lat'] = val;\n            }\n            else {\n                updateProps[key] = val;\n            }\n        }\n    }\n    // Go through the points to be updated and get new options for each of them\n    for (const p of \n    // If resizing).forEach(only update the point we are resizing\n    resizeProp ?\n        [point] :\n        dragDropData.groupedPoints) {\n        hashmap[p.id] = {\n            point: p,\n            newValues: p.getDropValues(dragDropData.origin, newPos, updateProps)\n        };\n    }\n    return hashmap;\n}\n/**\n * Get a snapshot of points, mouse position, and guide box dimensions\n *\n * @private\n * @function getPositionSnapshot\n *\n * @param {Highcharts.PointerEventObject} e\n *        Mouse event with mouse position to snapshot.\n *\n * @param {Array<Highcharts.Point>} points\n *        Points to take snapshot of. We store the value of the data properties\n *        defined in each series' dragDropProps.\n *\n * @param {Highcharts.SVGElement} [guideBox]\n *        The guide box to take snapshot of.\n *\n * @return {Object}\n *         Snapshot object. Point properties are placed in a hashmap with IDs as\n *         keys.\n */\nfunction getPositionSnapshot(e, points, guideBox) {\n    const res = {\n        chartX: e.chartX,\n        chartY: e.chartY,\n        guideBox: guideBox && {\n            x: guideBox.attr('x'),\n            y: guideBox.attr('y'),\n            width: guideBox.attr('width'),\n            height: guideBox.attr('height')\n        },\n        points: {}\n    };\n    // Loop over the points and add their props\n    for (const point of points) {\n        const dragDropProps = point.series.dragDropProps || {}, pointProps = {};\n        // Add all of the props defined in the series' dragDropProps to the\n        // snapshot\n        for (const key of Object.keys(dragDropProps)) {\n            const val = dragDropProps[key], axis = point.series[val.axis + 'Axis'];\n            pointProps[key] = point[key];\n            // Record how far cursor was from the point when drag started.\n            // This later will be used to calculate new value according to the\n            // current position of the cursor.\n            // e.g. `high` value is translated to `highOffset`\n            if (point.series.chart.mapView && point.plotX && point.plotY) {\n                pointProps[key + 'Offset'] = key === 'x' ?\n                    point.plotX : point.plotY;\n            }\n            else {\n                pointProps[key + 'Offset'] =\n                    // E.g. yAxis.toPixels(point.high), xAxis.toPixels\n                    // (point.end)\n                    axis.toPixels(point[key]) -\n                        (axis.horiz ? e.chartX : e.chartY);\n            }\n        }\n        pointProps.point = point; // Store reference to point\n        res.points[point.id] = pointProps;\n    }\n    return res;\n}\n/**\n * In mousemove events, check that we have dragged mouse further than the\n * dragSensitivity before we call mouseMove handler.\n *\n * @private\n * @function hasDraggedPastSensitivity\n *\n * @param {Highcharts.PointerEventObject} e\n *        Mouse move event to test.\n *\n * @param {Highcharts.Chart} chart\n *        Chart that has started dragging.\n *\n * @param {number} sensitivity\n *        Pixel sensitivity to test against.\n *\n * @return {boolean}\n *         True if the event is moved past sensitivity relative to the chart's\n *         drag origin.\n */\nfunction hasDraggedPastSensitivity(e, chart, sensitivity) {\n    const orig = chart.dragDropData.origin, oldX = orig.chartX, oldY = orig.chartY, newX = e.chartX, newY = e.chartY, distance = Math.sqrt((newX - oldX) * (newX - oldX) +\n        (newY - oldY) * (newY - oldY));\n    return distance > sensitivity;\n}\n/**\n * Prepare chart.dragDropData with origin info, and show the guide box.\n *\n * @private\n * @function initDragDrop\n * @param {Highcharts.PointerEventObject} e\n *        Mouse event with original mouse position.\n * @param {Highcharts.Point} point\n *        The point the dragging started on.\n * @return {void}\n */\nfunction initDragDrop(e, point) {\n    const groupedPoints = getGroupedPoints(point), series = point.series, chart = series.chart;\n    let guideBox;\n    // If liveRedraw is disabled, show the guide box with the default state\n    if (!pick(series.options.dragDrop && series.options.dragDrop.liveRedraw, true)) {\n        chart.dragGuideBox = guideBox = series.getGuideBox(groupedPoints);\n        chart\n            .setGuideBoxState('default', series.options.dragDrop.guideBox)\n            .add(series.group);\n    }\n    // Store some data on the chart to pick up later\n    chart.dragDropData = {\n        origin: getPositionSnapshot(e, groupedPoints, guideBox),\n        point: point,\n        groupedPoints: groupedPoints,\n        isDragging: true\n    };\n}\n/**\n * Utility function to test if a chart should have drag/drop enabled, looking at\n * its options.\n *\n * @private\n * @function isChartDraggable\n * @param {Highcharts.Chart} chart\n *        The chart to test.\n * @return {boolean}\n *         True if the chart is drag/droppable.\n */\nfunction isChartDraggable(chart) {\n    let i = chart.series ? chart.series.length : 0;\n    if ((chart.hasCartesianSeries && !chart.polar) ||\n        chart.mapView) {\n        while (i--) {\n            if (chart.series[i].options.dragDrop &&\n                isSeriesDraggable(chart.series[i])) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Utility function to test if a point is movable (any of its props can be\n * dragged by a move, not just individually).\n *\n * @private\n * @function isPointMovable\n * @param {Highcharts.Point} point\n *        The point to test.\n * @return {boolean}\n *         True if the point is movable.\n */\nfunction isPointMovable(point) {\n    const series = point.series, chart = series.chart, seriesDragDropOptions = series.options.dragDrop || {}, pointDragDropOptions = point.options && point.options.dragDrop, updateProps = series.dragDropProps;\n    let p, hasMovableX, hasMovableY;\n    // eslint-disable-next-line guard-for-in\n    for (const key in updateProps) {\n        p = updateProps[key];\n        if (p.axis === 'x' && p.move) {\n            hasMovableX = true;\n        }\n        else if (p.axis === 'y' && p.move) {\n            hasMovableY = true;\n        }\n    }\n    // We can only move the point if draggableX/Y is set, even if all the\n    // individual prop options are set.\n    return ((seriesDragDropOptions.draggableX && hasMovableX ||\n        seriesDragDropOptions.draggableY && hasMovableY) &&\n        !(pointDragDropOptions &&\n            pointDragDropOptions.draggableX === false &&\n            pointDragDropOptions.draggableY === false) &&\n        (!!(series.yAxis && series.xAxis) ||\n            chart.mapView));\n}\n/**\n * Utility function to test if a series is using drag/drop, looking at its\n * options.\n *\n * @private\n * @function isSeriesDraggable\n * @param {Highcharts.Series} series\n *        The series to test.\n * @return {boolean}\n *         True if the series is using drag/drop.\n */\nfunction isSeriesDraggable(series) {\n    const props = ['draggableX', 'draggableY'], dragDropProps = series.dragDropProps || {};\n    let val;\n    // Add optionNames from dragDropProps to the array of props to check for\n    for (const key of Object.keys(dragDropProps)) {\n        val = dragDropProps[key];\n        if (val.optionName) {\n            props.push(val.optionName);\n        }\n    }\n    // Loop over all options we have that could enable dragDrop for this\n    // series. If any of them are truthy, this series is draggable.\n    let i = props.length;\n    while (i--) {\n        if (series.options.dragDrop[props[i]]) {\n            return true;\n        }\n    }\n}\n/**\n * On container mouse down. Init dragdrop if conditions are right.\n *\n * @private\n * @function mouseDown\n * @param {Highcharts.PointerEventObject} e\n *        The mouse down event.\n * @param {Highcharts.Chart} chart\n *        The chart we are clicking.\n */\nfunction mouseDown(e, chart) {\n    const dragPoint = chart.hoverPoint, dragDropOptions = merge(dragPoint && dragPoint.series.options.dragDrop, dragPoint && dragPoint.options.dragDrop), draggableX = dragDropOptions.draggableX || false, draggableY = dragDropOptions.draggableY || false;\n    // Reset cancel click\n    chart.cancelClick = false;\n    // Ignore if:\n    if (\n    // Option is disabled for the point\n    !(draggableX || draggableY) ||\n        // Zoom/pan key is pressed\n        chart.zoomOrPanKeyPressed(e) ||\n        // Dragging an annotation\n        chart.hasDraggedAnnotation) {\n        return;\n    }\n    // If we somehow get a mousedown event while we are dragging, cancel\n    if (chart.dragDropData && chart.dragDropData.isDragging) {\n        mouseUp(e, chart);\n        return;\n    }\n    // If this point is movable, start dragging it\n    if (dragPoint && isPointMovable(dragPoint)) {\n        chart.mouseIsDown = false; // Prevent zooming\n        initDragDrop(e, dragPoint);\n        dragPoint.firePointEvent('dragStart', e);\n    }\n}\n/**\n * On container mouse move. Handle drag sensitivity and fire drag event.\n *\n * @private\n * @function mouseMove\n * @param {Highcharts.PointerEventObject} e\n *        The mouse move event.\n * @param {Highcharts.Chart} chart\n *        The chart we are moving across.\n */\nfunction mouseMove(e, chart) {\n    // Ignore if zoom/pan key is pressed\n    if (chart.zoomOrPanKeyPressed(e)) {\n        return;\n    }\n    const dragDropData = chart.dragDropData;\n    let point, seriesDragDropOpts, newPoints, numNewPoints = 0, newPoint;\n    if (dragDropData && dragDropData.isDragging && dragDropData.point.series) {\n        point = dragDropData.point;\n        seriesDragDropOpts = point.series.options.dragDrop;\n        // No tooltip for dragging\n        e.preventDefault();\n        // Update sensitivity test if not passed yet\n        if (!dragDropData.draggedPastSensitivity) {\n            dragDropData.draggedPastSensitivity = hasDraggedPastSensitivity(e, chart, pick(point.options.dragDrop &&\n                point.options.dragDrop.dragSensitivity, seriesDragDropOpts &&\n                seriesDragDropOpts.dragSensitivity, DraggablePoints_DragDropDefaults.dragSensitivity));\n        }\n        // If we have dragged past dragSensitivity, run the mousemove handler\n        // for dragging\n        if (dragDropData.draggedPastSensitivity) {\n            // Find the new point values from the moving\n            dragDropData.newPoints = getNewPoints(dragDropData, e);\n            // If we are only dragging one point, add it to the event\n            newPoints = dragDropData.newPoints;\n            numNewPoints = DraggableChart_countProps(newPoints);\n            newPoint = numNewPoints === 1 ?\n                DraggableChart_getFirstProp(newPoints) :\n                null;\n            // Run the handler\n            point.firePointEvent('drag', {\n                origin: dragDropData.origin,\n                newPoints: dragDropData.newPoints,\n                newPoint: newPoint && newPoint.newValues,\n                newPointId: newPoint && newPoint.point.id,\n                numNewPoints: numNewPoints,\n                chartX: e.chartX,\n                chartY: e.chartY\n            }, function () {\n                dragMove(e, point);\n            });\n        }\n    }\n}\n/**\n * On container mouse up. Fire drop event and reset state.\n *\n * @private\n * @function mouseUp\n * @param {Highcharts.PointerEventObject} e\n *        The mouse up event.\n * @param {Highcharts.Chart} chart\n *        The chart we were dragging in.\n */\nfunction mouseUp(e, chart) {\n    const dragDropData = chart.dragDropData;\n    if (dragDropData &&\n        dragDropData.isDragging &&\n        dragDropData.draggedPastSensitivity &&\n        dragDropData.point.series) {\n        const point = dragDropData.point, newPoints = dragDropData.newPoints, numNewPoints = DraggableChart_countProps(newPoints), newPoint = numNewPoints === 1 ?\n            DraggableChart_getFirstProp(newPoints) :\n            null;\n        // Hide the drag handles\n        if (chart.dragHandles) {\n            chart.hideDragHandles();\n        }\n        // Prevent default action\n        e.preventDefault();\n        chart.cancelClick = true;\n        // Fire the event, with a default handler that updates the points\n        point.firePointEvent('drop', {\n            origin: dragDropData.origin,\n            chartX: e.chartX,\n            chartY: e.chartY,\n            newPoints: newPoints,\n            numNewPoints: numNewPoints,\n            newPoint: newPoint && newPoint.newValues,\n            newPointId: newPoint && newPoint.point.id\n        }, function () {\n            updatePoints(chart);\n        });\n    }\n    // Reset\n    delete chart.dragDropData;\n    // Clean up the drag guide box if it exists. This is always added on\n    // drag start, even if user is overriding events.\n    if (chart.dragGuideBox) {\n        chart.dragGuideBox.destroy();\n        delete chart.dragGuideBox;\n    }\n}\n/**\n * Add event listener to Chart.render that checks whether or not we should add\n * dragdrop.\n * @private\n */\nfunction onChartRender() {\n    // If we don't have dragDrop events, see if we should add them\n    if (!this.hasAddedDragDropEvents) {\n        addDragDropEvents(this);\n    }\n}\n/**\n * Resize the guide box according to point options and a difference in mouse\n * positions. Handles reversed axes.\n *\n * @private\n * @function resizeGuideBox\n * @param {Highcharts.Point} point\n *        The point that is being resized.\n * @param {number} dX\n *        Difference in X position.\n * @param {number} dY\n *        Difference in Y position.\n */\nfunction resizeGuideBox(point, dX, dY) {\n    const series = point.series, chart = series.chart, dragDropData = chart.dragDropData, resizeProp = series.dragDropProps[dragDropData.updateProp], \n    // `dragDropProp.resizeSide` holds info on which side to resize.\n    newPoint = dragDropData.newPoints[point.id].newValues, resizeSide = typeof resizeProp.resizeSide === 'function' ?\n        resizeProp.resizeSide(newPoint, point) : resizeProp.resizeSide;\n    // Call resize hook if it is defined\n    if (resizeProp.beforeResize) {\n        resizeProp.beforeResize(chart.dragGuideBox, newPoint, point);\n    }\n    // Do the resize\n    resizeRect(chart.dragGuideBox, resizeProp.axis === 'x' && series.xAxis.reversed ||\n        resizeProp.axis === 'y' && series.yAxis.reversed ?\n        flipResizeSide(resizeSide) : resizeSide, {\n        x: resizeProp.axis === 'x' ?\n            dX - (dragDropData.origin.prevdX || 0) : 0,\n        y: resizeProp.axis === 'y' ?\n            dY - (dragDropData.origin.prevdY || 0) : 0\n    });\n}\n/**\n * Resize a rect element on one side. The element is modified.\n *\n * @private\n * @function resizeRect\n * @param {Highcharts.SVGElement} rect\n *        Rect element to resize.\n * @param {string} updateSide\n *        Which side of the rect to update. Can be `left`, `right`, `top` or\n *        `bottom`.\n * @param {Highcharts.PositionObject} update\n *        Object with x and y properties, detailing how much to resize each\n *        dimension.\n * @return {void}\n */\nfunction resizeRect(rect, updateSide, update) {\n    let resizeAttrs;\n    switch (updateSide) {\n        case 'left':\n            resizeAttrs = {\n                x: rect.attr('x') + update.x,\n                width: Math.max(1, rect.attr('width') - update.x)\n            };\n            break;\n        case 'right':\n            resizeAttrs = {\n                width: Math.max(1, rect.attr('width') + update.x)\n            };\n            break;\n        case 'top':\n            resizeAttrs = {\n                y: rect.attr('y') + update.y,\n                height: Math.max(1, rect.attr('height') - update.y)\n            };\n            break;\n        case 'bottom':\n            resizeAttrs = {\n                height: Math.max(1, rect.attr('height') + update.y)\n            };\n            break;\n        default:\n    }\n    rect.attr(resizeAttrs);\n}\n/**\n * Update the points in a chart from dragDropData.newPoints.\n *\n * @private\n * @function updatePoints\n * @param {Highcharts.Chart} chart\n *        A chart with dragDropData.newPoints.\n * @param {boolean} [animation=true]\n *        Animate updating points?\n */\nfunction updatePoints(chart, animation) {\n    const newPoints = chart.dragDropData.newPoints, animOptions = animObject(animation);\n    chart.isDragDropAnimating = true;\n    let newPoint;\n    // Update the points\n    for (const key of Object.keys(newPoints)) {\n        newPoint = newPoints[key];\n        newPoint.point.update(newPoint.newValues, false);\n    }\n    chart.redraw(animOptions);\n    // Clear the isAnimating flag after animation duration is complete.\n    // The complete handler for animation seems to have bugs at this time, so\n    // we have to use a timeout instead.\n    setTimeout(() => {\n        delete chart.isDragDropAnimating;\n        if (chart.hoverPoint && !chart.dragHandles) {\n            chart.hoverPoint.showDragHandles();\n        }\n    }, animOptions.duration);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DraggableChart = {\n    compose,\n    flipResizeSide,\n    initDragDrop\n};\n/* harmony default export */ const DraggablePoints_DraggableChart = (DraggableChart);\n\n;// ./code/es-modules/Extensions/DraggablePoints/DragDropProps.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { flipResizeSide: DragDropProps_flipResizeSide } = DraggablePoints_DraggableChart;\n\nconst { isNumber, merge: DragDropProps_merge, pick: DragDropProps_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Constants\n *\n * */\n// Line series - only draggableX/Y, no drag handles\nconst line = {\n    x: {\n        axis: 'x',\n        move: true\n    },\n    y: {\n        axis: 'y',\n        move: true\n    }\n};\n// Flag series - same as line/scatter\nconst flags = line;\n// Column series - x can be moved, y can only be resized. Note extra\n// functionality for handling upside down columns (below threshold).\nconst column = {\n    x: {\n        axis: 'x',\n        move: true\n    },\n    y: {\n        axis: 'y',\n        move: false,\n        resize: true,\n        // Force guideBox start coordinates\n        beforeResize: (guideBox, pointVals, point) => {\n            // We need to ensure that guideBox always starts at threshold.\n            // We flip whether or not we update the top or bottom of the guide\n            // box at threshold, but if we drag the mouse fast, the top has not\n            // reached threshold before we cross over and update the bottom.\n            const plotThreshold = DragDropProps_pick(point.yBottom, // Added support for stacked series. (#18741)\n            point.series.translatedThreshold), plotY = guideBox.attr('y'), threshold = isNumber(point.stackY) ? (point.stackY - (point.y || 0)) : point.series.options.threshold || 0, y = threshold + pointVals.y;\n            let height, diff;\n            if (point.series.yAxis.reversed ? y < threshold : y >= threshold) {\n                // Above threshold - always set height to hit the threshold\n                height = guideBox.attr('height');\n                diff = plotThreshold ? plotThreshold - plotY - height : 0;\n                guideBox.attr({\n                    height: Math.max(0, Math.round(height + diff))\n                });\n            }\n            else {\n                // Below - always set y to start at threshold\n                guideBox.attr({\n                    y: Math.round(plotY + (plotThreshold ? plotThreshold - plotY : 0))\n                });\n            }\n        },\n        // Flip the side of the resize handle if column is below threshold.\n        // Make sure we remove the handle on the other side.\n        resizeSide: (pointVals, point) => {\n            const chart = point.series.chart, dragHandles = chart.dragHandles, side = pointVals.y >= (point.series.options.threshold || 0) ?\n                'top' : 'bottom', flipSide = DragDropProps_flipResizeSide(side);\n            // Force remove handle on other side\n            if (dragHandles && dragHandles[flipSide]) {\n                dragHandles[flipSide].destroy();\n                delete dragHandles[flipSide];\n            }\n            return side;\n        },\n        // Position handle at bottom if column is below threshold\n        handlePositioner: (point) => {\n            const bBox = (point.shapeArgs ||\n                (point.graphic && point.graphic.getBBox()) ||\n                {}), reversed = point.series.yAxis.reversed, threshold = point.series.options.threshold || 0, y = point.y || 0, bottom = (!reversed && y >= threshold) ||\n                (reversed && y < threshold);\n            return {\n                x: bBox.x || 0,\n                y: bottom ? (bBox.y || 0) : (bBox.y || 0) + (bBox.height || 0)\n            };\n        },\n        // Horizontal handle\n        handleFormatter: (point) => {\n            const shapeArgs = point.shapeArgs || {}, radius = shapeArgs.r || 0, // Rounding of bar corners\n            width = shapeArgs.width || 0, centerX = width / 2;\n            return [\n                // Left wick\n                ['M', radius, 0],\n                ['L', centerX - 5, 0],\n                // Circle\n                ['A', 1, 1, 0, 0, 0, centerX + 5, 0],\n                ['A', 1, 1, 0, 0, 0, centerX - 5, 0],\n                // Right wick\n                ['M', centerX + 5, 0],\n                ['L', width - radius, 0]\n            ];\n        }\n    }\n};\n// Boxplot series - move x, resize or move low/q1/q3/high\nconst boxplot = {\n    x: column.x,\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x || 0,\n            y: point.lowPlot\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.q1)\n    },\n    /**\n     * Allow Q1 value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableQ1\n     */\n    q1: {\n        optionName: 'draggableQ1',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x || 0,\n            y: point.q1Plot\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.median && val >= point.low)\n    },\n    median: {\n        // Median cannot be dragged individually, just move the whole\n        // point for this.\n        axis: 'y',\n        move: true\n    },\n    /**\n     * Allow Q3 value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableQ3\n     */\n    q3: {\n        optionName: 'draggableQ3',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x || 0,\n            y: point.q3Plot\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.high && val >= point.median)\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x || 0,\n            y: point.highPlot\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val >= point.q3)\n    }\n};\n// Errorbar series - move x, resize or move low/high\nconst errorbar = {\n    x: column.x,\n    low: {\n        ...boxplot.low,\n        propValidate: (val, point) => (val <= point.high)\n    },\n    high: {\n        ...boxplot.high,\n        propValidate: (val, point) => (val >= point.low)\n    }\n};\n/**\n * @exclude      draggableQ1, draggableQ3\n * @optionparent plotOptions.errorbar.dragDrop\n */\n// Bullet graph, x/y same as column, but also allow target to be dragged.\nconst bullet = {\n    x: column.x,\n    y: column.y,\n    /**\n     * Allow target value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.bullet.dragDrop.draggableTarget\n     */\n    target: {\n        optionName: 'draggableTarget',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: (point) => {\n            const bBox = point.targetGraphic.getBBox();\n            return {\n                x: point.barX,\n                y: bBox.y + bBox.height / 2\n            };\n        },\n        handleFormatter: column.y.handleFormatter\n    }\n};\n// OHLC series - move x, resize or move open/high/low/close\nconst ohlc = {\n    x: column.x,\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x,\n            y: point.plotLow\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.open && val <= point.close)\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x,\n            y: point.plotHigh\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val >= point.open && val >= point.close)\n    },\n    /**\n     * Allow open value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableOpen\n     */\n    open: {\n        optionName: 'draggableOpen',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: (point) => (point.open >= point.close ? 'top' : 'bottom'),\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x,\n            y: point.plotOpen\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.high && val >= point.low)\n    },\n    /**\n     * Allow close value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableClose\n     */\n    close: {\n        optionName: 'draggableClose',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: (point) => (point.open >= point.close ? 'bottom' : 'top'),\n        handlePositioner: (point) => ({\n            x: point.shapeArgs.x,\n            y: point.plotClose\n        }),\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.high && val >= point.low)\n    }\n};\n// Waterfall - mostly as column, but don't show drag handles for sum points\nconst waterfall = {\n    x: column.x,\n    y: DragDropProps_merge(column.y, {\n        handleFormatter: (point) => (point.isSum || point.isIntermediateSum ?\n            null :\n            column?.y?.handleFormatter?.(point) || null)\n    })\n};\n// Columnrange series - move x, resize or move low/high\nconst columnrange = {\n    x: {\n        axis: 'x',\n        move: true\n    },\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.columnrange.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: (point) => {\n            const bBox = (point.shapeArgs || point.graphic.getBBox());\n            return {\n                x: bBox.x || 0,\n                y: (bBox.y || 0) + (bBox.height || 0)\n            };\n        },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val <= point.high)\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.columnrange.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: (point) => {\n            const bBox = (point.shapeArgs || point.graphic.getBBox());\n            return {\n                x: bBox.x || 0,\n                y: bBox.y || 0\n            };\n        },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: (val, point) => (val >= point.low)\n    }\n};\n// Arearange series - move x, resize or move low/high\nconst arearange = {\n    x: columnrange.x,\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.arearange.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: (point) => {\n            const bBox = (point.graphics &&\n                point.graphics[0] &&\n                point.graphics[0].getBBox());\n            return bBox ? {\n                x: bBox.x + bBox.width / 2,\n                y: bBox.y + bBox.height / 2\n            } : { x: -999, y: -999 };\n        },\n        handleFormatter: arearangeHandleFormatter,\n        propValidate: columnrange.low.propValidate\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.arearange.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: (point) => {\n            const bBox = (point.graphics &&\n                point.graphics[1] &&\n                point.graphics[1].getBBox());\n            return bBox ? {\n                x: bBox.x + bBox.width / 2,\n                y: bBox.y + bBox.height / 2\n            } : { x: -999, y: -999 };\n        },\n        handleFormatter: arearangeHandleFormatter,\n        propValidate: columnrange.high.propValidate\n    }\n};\n// Xrange - resize/move x/x2, and move y\nconst xrange = {\n    y: {\n        axis: 'y',\n        move: true\n    },\n    /**\n     * Allow x value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.xrange.dragDrop.draggableX1\n     */\n    x: {\n        optionName: 'draggableX1',\n        axis: 'x',\n        move: true,\n        resize: true,\n        resizeSide: 'left',\n        handlePositioner: (point) => (xrangeHandlePositioner(point, 'x')),\n        handleFormatter: horizHandleFormatter,\n        propValidate: (val, point) => (val <= point.x2)\n    },\n    /**\n     * Allow x2 value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.xrange.dragDrop.draggableX2\n     */\n    x2: {\n        optionName: 'draggableX2',\n        axis: 'x',\n        move: true,\n        resize: true,\n        resizeSide: 'right',\n        handlePositioner: (point) => (xrangeHandlePositioner(point, 'x2')),\n        handleFormatter: horizHandleFormatter,\n        propValidate: (val, point) => (val >= point.x)\n    }\n};\n// Gantt - same as xrange, but with aliases\nconst gantt = {\n    y: xrange.y,\n    /**\n     * Allow start value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.gantt.dragDrop.draggableStart\n     */\n    start: DragDropProps_merge(xrange.x, {\n        optionName: 'draggableStart',\n        // Do not allow individual drag handles for milestones\n        validateIndividualDrag: (point) => (!point.milestone)\n    }),\n    /**\n     * Allow end value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.gantt.dragDrop.draggableEnd\n     */\n    end: DragDropProps_merge(xrange.x2, {\n        optionName: 'draggableEnd',\n        // Do not allow individual drag handles for milestones\n        validateIndividualDrag: (point) => (!point.milestone)\n    })\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Use a circle covering the marker as drag handle.\n * @private\n */\nfunction arearangeHandleFormatter(point) {\n    const radius = point.graphic ?\n        point.graphic.getBBox().width / 2 + 1 :\n        4;\n    return [\n        ['M', 0 - radius, 0],\n        ['a', radius, radius, 0, 1, 0, radius * 2, 0],\n        ['a', radius, radius, 0, 1, 0, radius * -2, 0]\n    ];\n}\n/**\n * 90deg rotated column handle path, used in multiple series types.\n * @private\n */\nfunction horizHandleFormatter(point) {\n    const shapeArgs = point.shapeArgs || point.graphic.getBBox(), top = shapeArgs.r || 0, // Rounding of bar corners\n    bottom = shapeArgs.height - top, centerY = shapeArgs.height / 2;\n    return [\n        // Top wick\n        ['M', 0, top],\n        ['L', 0, centerY - 5],\n        // Circle\n        ['A', 1, 1, 0, 0, 0, 0, centerY + 5],\n        ['A', 1, 1, 0, 0, 0, 0, centerY - 5],\n        // Bottom wick\n        ['M', 0, centerY + 5],\n        ['L', 0, bottom]\n    ];\n}\n/**\n * Handle positioner logic is the same for x and x2 apart from the x value.\n * shapeArgs does not take yAxis reversed etc into account, so we use\n * axis.toPixels to handle positioning.\n * @private\n */\nfunction xrangeHandlePositioner(point, xProp) {\n    const series = point.series, xAxis = series.xAxis, yAxis = series.yAxis, inverted = series.chart.inverted, offsetY = series.columnMetrics ? series.columnMetrics.offset :\n        -point.shapeArgs.height / 2;\n    // Using toPixels handles axis.reversed, but doesn't take\n    // chart.inverted into account.\n    let newX = xAxis.toPixels(point[xProp], true), newY = yAxis.toPixels(point.y, true);\n    // Handle chart inverted\n    if (inverted) {\n        newX = xAxis.len - newX;\n        newY = yAxis.len - newY;\n    }\n    newY += offsetY; // (#12872)\n    return {\n        x: Math.round(newX),\n        y: Math.round(newY)\n    };\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DragDropProps = {\n    arearange,\n    boxplot,\n    bullet,\n    column,\n    columnrange,\n    errorbar,\n    flags,\n    gantt,\n    line,\n    ohlc,\n    waterfall,\n    xrange\n};\n/* harmony default export */ const DraggablePoints_DragDropProps = (DragDropProps);\n\n;// ./code/es-modules/Extensions/DraggablePoints/DraggablePoints.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvents: DraggablePoints_addEvents, getNormalizedEvent: DraggablePoints_getNormalizedEvent } = DraggablePoints_DragDropUtilities;\n\nconst { initDragDrop: DraggablePoints_initDragDrop } = DraggablePoints_DraggableChart;\n\n\n\nconst { addEvent: DraggablePoints_addEvent, clamp, isNumber: DraggablePoints_isNumber, merge: DraggablePoints_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* @todo\nAdd drag/drop support to specific data props for different series types.\n\nThe dragDrop.draggableX/Y user options on series enable/disable all of these per\ndirection unless they are specifically set in options using\ndragDrop.{optionName}. If the prop does not specify an optionName here, it can\nonly be enabled/disabled by the user with draggableX/Y.\n\nSupported options for each prop:\n    optionName: User option in series.dragDrop that enables/disables\n        dragging this prop.\n    axis: Can be 'x' or 'y'. Whether this prop is linked to x or y axis.\n    move: Whether or not this prop should be updated when moving points.\n    resize: Whether or not to draw a drag handle and allow user to drag and\n        update this prop by itself.\n    beforeResize: Hook to perform tasks before a resize is made. Gets\n        the guide box, the new points values, and the point as args.\n    resizeSide: Which side of the guide box to resize when dragging the\n        handle. Can be \"left\", \"right\", \"top\", \"bottom\". Chart.inverted is\n        handled automatically. Can also be a function, taking the new point\n        values as parameter, as well as the point, and returning a string\n        with the side.\n    propValidate: Function that takes the prop value and the point as\n        arguments, and returns true if the prop value is valid, false if\n        not. It is used to prevent e.g. resizing \"low\" above \"high\".\n    handlePositioner: For resizeable props, return 0,0 in SVG plot coords of\n        where to place the dragHandle. Gets point as argument. Should return\n        object with x and y properties.\n    handleFormatter: For resizeable props, return the path of the drag\n        handle as an SVG path array. Gets the point as argument. The handle\n        is translated according to handlePositioner.\n    handleOptions: Options to merge with the default handle options.\n\n    TODO:\n    - It makes sense to have support for resizing the size of bubbles and\n        e.g variwide columns. This requires us to support dragging along a\n        z-axis, somehow computing a relative value from old to new pixel\n        size.\n    - Moving maps could be useful, although we would have to compute new\n        point.path values in order to do it properly (using SVG translate\n        is easier, but won't update the data).\n*/\n/** @private */\nfunction DraggablePoints_compose(ChartClass, SeriesClass) {\n    DraggablePoints_DraggableChart.compose(ChartClass);\n    const seriesProto = SeriesClass.prototype;\n    if (!seriesProto.dragDropProps) {\n        const PointClass = SeriesClass.prototype.pointClass, seriesTypes = SeriesClass.types, pointProto = PointClass.prototype;\n        pointProto.getDropValues = pointGetDropValues;\n        pointProto.showDragHandles = pointShowDragHandles;\n        DraggablePoints_addEvent(PointClass, 'mouseOut', onPointMouseOut);\n        DraggablePoints_addEvent(PointClass, 'mouseOver', onPointMouseOver);\n        DraggablePoints_addEvent(PointClass, 'remove', onPointRemove);\n        seriesProto.dragDropProps = DraggablePoints_DragDropProps.line;\n        seriesProto.getGuideBox = seriesGetGuideBox;\n        // Custom props for certain series types\n        const seriesWithDragDropProps = [\n            'arearange',\n            'boxplot',\n            'bullet',\n            'column',\n            'columnrange',\n            'errorbar',\n            'flags',\n            'gantt',\n            'ohlc',\n            'waterfall',\n            'xrange'\n        ];\n        for (const seriesType of seriesWithDragDropProps) {\n            if (seriesTypes[seriesType]) {\n                seriesTypes[seriesType].prototype.dragDropProps =\n                    DraggablePoints_DragDropProps[seriesType];\n            }\n        }\n        // Don't support certain series types\n        const seriesWithoutDragDropProps = [\n            'bellcurve',\n            'gauge',\n            'histogram',\n            'map',\n            'mapline',\n            'pareto',\n            'pie',\n            'sankey',\n            'sma',\n            'sunburst',\n            'treemap',\n            'vector',\n            'windbarb',\n            'wordcloud'\n        ];\n        for (const seriesType of seriesWithoutDragDropProps) {\n            if (seriesTypes[seriesType]) {\n                seriesTypes[seriesType].prototype.dragDropProps = null;\n            }\n        }\n    }\n}\n/**\n * On point mouse out. Hide drag handles, depending on state.\n *\n * @private\n * @function mouseOut\n * @param {Highcharts.Point} point\n *        The point mousing out of.\n */\nfunction mouseOut(point) {\n    const chart = point.series && point.series.chart, dragDropData = chart && chart.dragDropData;\n    if (chart &&\n        chart.dragHandles &&\n        !(dragDropData &&\n            (dragDropData.isDragging &&\n                dragDropData.draggedPastSensitivity ||\n                dragDropData.isHoveringHandle === point.id))) {\n        chart.hideDragHandles();\n    }\n}\n/**\n * Mouseover on a point. Show drag handles if the conditions are right.\n *\n * @private\n * @function mouseOver\n * @param {Highcharts.Point} point\n *        The point mousing over.\n */\nfunction mouseOver(point) {\n    const series = point.series, chart = series && series.chart, dragDropData = chart && chart.dragDropData, is3d = chart && chart.is3d && chart.is3d();\n    if (chart &&\n        !(dragDropData &&\n            dragDropData.isDragging && // Ignore if dragging a point\n            dragDropData.draggedPastSensitivity) &&\n        !chart.isDragDropAnimating && // Ignore if animating\n        series.options.dragDrop && // No need to compute handles without this\n        !is3d // No 3D support\n    ) {\n        // Hide the handles if they exist on another point already\n        if (chart.dragHandles) {\n            chart.hideDragHandles();\n        }\n        point.showDragHandles();\n    }\n}\n/**\n * Point mouseleave event. See above function for explanation of the timeout.\n * @private\n */\nfunction onPointMouseOut() {\n    const point = this;\n    setTimeout(() => {\n        if (point.series) {\n            mouseOut(point);\n        }\n    }, 10);\n}\n/**\n * Point hover event. We use a short timeout due to issues with coordinating\n * point mouseover/out events on dragHandles and points.\n *\n * Particularly arearange series are finicky since the markers are not\n * individual points. This logic should preferably be improved in the future.\n *\n * Notice that the mouseOut event below must have a shorter timeout to ensure\n * event order.\n */\nfunction onPointMouseOver() {\n    const point = this;\n    setTimeout(() => mouseOver(point), 12);\n}\n/**\n * Hide drag handles on a point if it is removed.\n * @private\n */\nfunction onPointRemove() {\n    const chart = this.series.chart, dragHandles = chart.dragHandles;\n    if (dragHandles && dragHandles.point === this.id) {\n        chart.hideDragHandles();\n    }\n}\n/**\n * Mouseout on resize handle. Handle states, and possibly run mouseOut on point.\n *\n * @private\n * @function onResizeHandleMouseOut\n * @param {Highcharts.Point} point\n *        The point mousing out of.\n */\nfunction onResizeHandleMouseOut(point) {\n    const chart = point.series.chart;\n    if (chart.dragDropData &&\n        point.id === chart.dragDropData.isHoveringHandle) {\n        delete chart.dragDropData.isHoveringHandle;\n    }\n    if (!chart.hoverPoint) {\n        mouseOut(point);\n    }\n}\n/**\n * Mousedown on resize handle. Init a drag if the conditions are right.\n *\n * @private\n * @function onResizeHandleMouseDown\n * @param {Highcharts.PointerEventObject} e\n *        The mousedown event.\n * @param {Highcharts.Point} point\n *        The point mousing down on.\n * @param {string} updateProp\n *        The data property this resize handle is attached to for this point.\n */\nfunction onResizeHandleMouseDown(e, point, updateProp) {\n    const chart = point.series.chart;\n    // Ignore if zoom/pan key is pressed\n    if (chart.zoomOrPanKeyPressed(e)) {\n        return;\n    }\n    // Prevent zooming\n    chart.mouseIsDown = false;\n    // We started a drag\n    DraggablePoints_initDragDrop(e, point);\n    chart.dragDropData.updateProp =\n        e.updateProp = updateProp;\n    point.firePointEvent('dragStart', e);\n    // Prevent default to avoid point click for dragging too\n    e.stopPropagation();\n    e.preventDefault();\n}\n/**\n * Get updated point values when dragging a point.\n *\n * @private\n * @function Highcharts.Point#getDropValues\n *\n * @param {Object} origin\n *        Mouse position (chartX/Y) and point props at current data values.\n *        Point props should be organized per point.id in a hashmap.\n *\n * @param {Highcharts.PointerEventObject} newPos\n *        New mouse position (chartX/Y).\n *\n * @param {Highcharts.Dictionary<Highcharts.Dictionary<Highcharts.Dictionary<string>>>} updateProps\n *        Point props to modify. Map of prop objects where each key refers to\n *        the prop, and the value is an object with an axis property. Example:\n *        {\n *            x: {\n *                axis: 'x'\n *            },\n *            x2: {\n *                axis: 'x'\n *            }\n *        }\n *\n * @return {Highcharts.Dictionary<number>}\n *         An object with updated data values.\n */\nfunction pointGetDropValues(origin, newPos, updateProps) {\n    const point = this, series = point.series, chart = series.chart, mapView = chart.mapView, options = DraggablePoints_merge(series.options.dragDrop, point.options.dragDrop), result = {}, pointOrigin = origin.points[point.id], updateSingleProp = Object.keys(updateProps).length === 1;\n    /**\n     * Utility function to apply precision and limit a value within the\n     * draggable range.\n     * @private\n     * @param {number} val\n     *        Value to limit\n     * @param {string} direction\n     *        Axis direction\n     * @return {number}\n     *         Limited value\n     */\n    const limitToRange = (val, dir) => {\n        const direction = dir.toUpperCase(), time = series.chart.time, defaultPrecision = series[`${dir}Axis`].categories ? 1 : 0, precision = options[`dragPrecision${direction}`] ??\n            defaultPrecision, min = time.parse(options[`dragMin${direction}`]) ??\n            -Infinity, max = time.parse(options[`dragMax${direction}`]) ??\n            Infinity;\n        let res = val;\n        if (precision) {\n            res = Math.round(res / precision) * precision;\n        }\n        return clamp(res, min, max);\n    };\n    /**\n     * Utility function to apply precision and limit a value within the\n     * draggable range used only for Highcharts Maps.\n     * @private\n     * @param {PointerEvent} newPos\n     *        PointerEvent, which is used to get the value\n     * @param {string} direction\n     *        Axis direction\n     * @param {string} key\n     *        Key for choosing between longitude and latitude\n     * @return {number | undefined}\n     *         Limited value\n     */\n    const limitToMapRange = (newPos, dir, key) => {\n        if (mapView) {\n            const direction = dir.toUpperCase(), precision = options[`dragPrecision${direction}`] ?? 0, lonLatMin = mapView.pixelsToLonLat({\n                x: 0,\n                y: 0\n            }), lonLatMax = mapView.pixelsToLonLat({\n                x: chart.plotBox.width,\n                y: chart.plotBox.height\n            });\n            let min = options[`dragMin${direction}`] ??\n                lonLatMin?.[key] ??\n                -Infinity, max = options[`dragMax${direction}`] ??\n                lonLatMax?.[key] ??\n                Infinity, res = newPos[key];\n            if (mapView.projection.options.name === 'Orthographic') {\n                return res;\n            }\n            if (key === 'lat') {\n                // If map is bigger than possible projection range\n                if (isNaN(min) || min > mapView.projection.maxLatitude) {\n                    min = mapView.projection.maxLatitude;\n                }\n                if (isNaN(max) || max < -1 * mapView.projection.maxLatitude) {\n                    max = -1 * mapView.projection.maxLatitude;\n                }\n                // Swap for latitude\n                const temp = max;\n                max = min;\n                min = temp;\n            }\n            if (!mapView.projection.hasCoordinates) {\n                // Establish y value\n                const lonLatRes = mapView.pixelsToLonLat({\n                    x: newPos.chartX - chart.plotLeft,\n                    y: chart.plotHeight - newPos.chartY + chart.plotTop\n                });\n                if (lonLatRes) {\n                    res = lonLatRes[key];\n                }\n            }\n            if (precision) {\n                res = Math.round(res / precision) * precision;\n            }\n            return clamp(res, min, max);\n        }\n    };\n    // Assign new value to property. Adds dX/YValue to the old value, limiting\n    // it within min/max ranges.\n    for (const key of Object.keys(updateProps)) {\n        const val = updateProps[key], oldVal = pointOrigin.point[key], axis = series[val.axis + 'Axis'], newVal = mapView ?\n            limitToMapRange(newPos, val.axis, key) :\n            limitToRange(axis.toValue((axis.horiz ? newPos.chartX : newPos.chartY) +\n                pointOrigin[key + 'Offset']), val.axis);\n        // If we are updating a single prop, and it has a validation function\n        // for the prop, run it. If it fails, don't update the value.\n        if (DraggablePoints_isNumber(newVal) &&\n            !(updateSingleProp &&\n                val.propValidate &&\n                !val.propValidate(newVal, point)) &&\n            typeof oldVal !== 'undefined') {\n            result[key] = newVal;\n        }\n    }\n    return result;\n}\n/**\n * Render drag handles on a point - depending on which handles are enabled - and\n * attach events to them.\n *\n * @private\n * @function Highcharts.Point#showDragHandles\n */\nfunction pointShowDragHandles() {\n    const point = this, series = point.series, chart = series.chart, { inverted, renderer } = chart, options = DraggablePoints_merge(series.options.dragDrop, point.options.dragDrop), dragDropProps = series.dragDropProps || {};\n    let dragHandles = chart.dragHandles;\n    // Go through each updateProp and see if we are supposed to create a handle\n    // for it.\n    for (const key of Object.keys(dragDropProps)) {\n        const val = dragDropProps[key], handleOptions = DraggablePoints_merge(DraggablePoints_DragDropDefaults.dragHandle, val.handleOptions, options.dragHandle), handleAttrs = {\n            'class': handleOptions.className,\n            'stroke-width': handleOptions.lineWidth,\n            fill: handleOptions.color,\n            stroke: handleOptions.lineColor\n        }, pathFormatter = handleOptions.pathFormatter || val.handleFormatter, handlePositioner = val.handlePositioner, \n        // Run validation function on whether or not we allow individual\n        // updating of this prop.\n        validate = val.validateIndividualDrag ?\n            val.validateIndividualDrag(point) : true;\n        let pos, handle, path;\n        if (val.resize &&\n            validate &&\n            val.resizeSide &&\n            pathFormatter &&\n            (options['draggable' + val.axis.toUpperCase()] ||\n                options[val.optionName]) &&\n            options[val.optionName] !== false) {\n            // Create handle if it doesn't exist\n            if (!dragHandles) {\n                dragHandles = chart.dragHandles = {\n                    group: renderer\n                        .g('drag-drop-handles')\n                        .add(series.markerGroup || series.group),\n                    point: point.id\n                };\n                // Store which point this is\n            }\n            else {\n                dragHandles.point = point.id;\n            }\n            // Find position and path of handle\n            pos = handlePositioner(point);\n            handleAttrs.d = path = pathFormatter(point);\n            // Correct left edge value depending on the xAxis' type, #16596\n            const minEdge = point.series.xAxis.categories ? -0.5 : 0;\n            if (!path || pos.x < minEdge || pos.y < 0) {\n                return;\n            }\n            // If cursor is not set explicitly, use axis direction\n            handleAttrs.cursor = handleOptions.cursor ||\n                ((val.axis === 'x') !== !!inverted ?\n                    'ew-resize' : 'ns-resize');\n            // Create and add the handle element if it doesn't exist\n            handle = dragHandles[val.optionName];\n            if (!handle) {\n                handle = dragHandles[val.optionName] = renderer\n                    .path()\n                    .add(dragHandles.group);\n            }\n            // Move and update handle\n            handleAttrs.translateX = inverted ?\n                series.yAxis.len - pos.y :\n                pos.x;\n            handleAttrs.translateY = inverted ?\n                series.xAxis.len - pos.x :\n                pos.y;\n            if (inverted) {\n                handleAttrs.rotation = -90;\n            }\n            handle.attr(handleAttrs);\n            // Add events\n            DraggablePoints_addEvents(handle.element, ['touchstart', 'mousedown'], (e) => {\n                onResizeHandleMouseDown(DraggablePoints_getNormalizedEvent(e, chart), point, key);\n            }, {\n                passive: false\n            });\n            DraggablePoints_addEvent(dragHandles.group.element, 'mouseover', () => {\n                chart.dragDropData = chart.dragDropData || {};\n                chart.dragDropData.isHoveringHandle = point.id;\n            });\n            DraggablePoints_addEvents(dragHandles.group.element, ['touchend', 'mouseout'], () => {\n                onResizeHandleMouseOut(point);\n            });\n        }\n    }\n}\n/**\n * Returns an SVGElement to use as the guide box for a set of points.\n *\n * @private\n * @function Highcharts.Series#getGuideBox\n *\n * @param {Array<Highcharts.Point>} points\n *        The state to set the guide box to.\n *\n * @return {Highcharts.SVGElement}\n *         An SVG element for the guide box, not added to DOM.\n */\nfunction seriesGetGuideBox(points) {\n    const chart = this.chart;\n    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity, changed;\n    // Find bounding box of all points\n    for (const point of points) {\n        const bBox = (point.graphic && point.graphic.getBBox() || point.shapeArgs);\n        if (bBox) {\n            let plotX2;\n            const x2 = point.x2;\n            if (DraggablePoints_isNumber(x2)) {\n                plotX2 = point.series.xAxis.translate(x2, false, false, false, true);\n            }\n            // Avoid a 0 min when some of the points being dragged are\n            // completely outside the plot\n            const skipBBox = !(bBox.width || bBox.height || bBox.x || bBox.y);\n            changed = true;\n            minX = Math.min(point.plotX || 0, plotX2 || 0, skipBBox ? Infinity : bBox.x || 0, minX);\n            maxX = Math.max(point.plotX || 0, plotX2 || 0, (bBox.x || 0) + (bBox.width || 0), maxX);\n            minY = Math.min(point.plotY || 0, skipBBox ? Infinity : bBox.y || 0, minY);\n            maxY = Math.max((bBox.y || 0) + (bBox.height || 0), maxY);\n        }\n    }\n    return changed ? chart.renderer.rect(minX, minY, maxX - minX, maxY - minY) : chart.renderer.g();\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DraggablePoints = {\n    compose: DraggablePoints_compose\n};\n/* harmony default export */ const DraggablePoints_DraggablePoints = (DraggablePoints);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Current drag and drop position.\n *\n * @interface Highcharts.DragDropPositionObject\n */ /**\n* Chart x position\n* @name Highcharts.DragDropPositionObject#chartX\n* @type {number}\n*/ /**\n* Chart y position\n* @name Highcharts.DragDropPositionObject#chartY\n* @type {number}\n*/ /**\n* Drag and drop guide box.\n* @name Highcharts.DragDropPositionObject#guideBox\n* @type {Highcharts.BBoxObject|undefined}\n*/ /**\n* Updated point data.\n* @name Highcharts.DragDropPositionObject#points\n* @type {Highcharts.Dictionary<Highcharts.Dictionary<number>>}\n*/ /**\n* Delta of previous x position.\n* @name Highcharts.DragDropPositionObject#prevdX\n* @type {number|undefined}\n*/ /**\n* Delta of previous y position.\n* @name Highcharts.DragDropPositionObject#prevdY\n* @type {number|undefined}\n*/\n/**\n * Function callback to execute while series points are dragged. Return false to\n * stop the default drag action.\n *\n * @callback Highcharts.PointDragCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Point where the event occurred.\n *\n * @param {Highcharts.PointDragEventObject} event\n *        Event arguments.\n */\n/**\n * Contains information about a points new values.\n *\n * @interface Highcharts.PointDragDropObject\n */ /**\n* New values.\n* @name Highcharts.PointDragDropObject#newValues\n* @type {Highcharts.Dictionary<number>}\n*/ /**\n* Updated point.\n* @name Highcharts.PointDragDropObject#point\n* @type {Highcharts.Point}\n*/\n/**\n * Contains common information for a drag event on series points.\n *\n * @interface Highcharts.PointDragEventObject\n */ /**\n* New point after drag if only a single one.\n* @name Highcharts.PointDropEventObject#newPoint\n* @type {Highcharts.PointDragDropObject|undefined}\n*/ /**\n* New point id after drag if only a single one.\n* @name Highcharts.PointDropEventObject#newPointId\n* @type {string|undefined}\n*/ /**\n* New points during drag.\n* @name Highcharts.PointDragEventObject#newPoints\n* @type {Highcharts.Dictionary<Highcharts.PointDragDropObject>}\n*/ /**\n* Original data.\n* @name Highcharts.PointDragEventObject#origin\n* @type {Highcharts.DragDropPositionObject}\n*/ /**\n* Prevent default drag action.\n* @name Highcharts.PointDragEventObject#preventDefault\n* @type {Function}\n*/ /**\n* Target point that caused the event.\n* @name Highcharts.PointDragEventObject#target\n* @type {Highcharts.Point}\n*/ /**\n* Event type.\n* @name Highcharts.PointDragEventObject#type\n* @type {\"drag\"}\n*/\n/**\n * Function callback to execute when a series point is dragged.\n *\n * @callback Highcharts.PointDragStartCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Point where the event occurred.\n *\n * @param {Highcharts.PointDragStartEventObject} event\n *        Event arguments.\n */\n/**\n * Contains common information for a drag event on series point.\n *\n * @interface Highcharts.PointDragStartEventObject\n * @extends global.MouseEvent\n */ /**\n* Data property being dragged.\n* @name Highcharts.PointDragStartEventObject#updateProp\n* @type {string|undefined}\n*/\n/**\n * Function callback to execute when series points are dropped.\n *\n * @callback Highcharts.PointDropCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Point where the event occurred.\n *\n * @param {Highcharts.PointDropEventObject} event\n *        Event arguments.\n */\n/**\n * Contains common information for a drop event on series points.\n *\n * @interface Highcharts.PointDropEventObject\n */ /**\n* New point after drop if only a single one.\n* @name Highcharts.PointDropEventObject#newPoint\n* @type {Highcharts.PointDragDropObject|undefined}\n*/ /**\n* New point id after drop if only a single one.\n* @name Highcharts.PointDropEventObject#newPointId\n* @type {string|undefined}\n*/ /**\n* New points after drop.\n* @name Highcharts.PointDropEventObject#newPoints\n* @type {Highcharts.Dictionary<Highcharts.PointDragDropObject>}\n*/ /**\n* Number of new points.\n* @name Highcharts.PointDropEventObject#numNewPoints\n* @type {number}\n*/ /**\n* Original data.\n* @name Highcharts.PointDropEventObject#origin\n* @type {Highcharts.DragDropPositionObject}\n*/ /**\n* Prevent default drop action.\n* @name Highcharts.PointDropEventObject#preventDefault\n* @type {Function}\n*/ /**\n* Target point that caused the event.\n* @name Highcharts.PointDropEventObject#target\n* @type {Highcharts.Point}\n*/ /**\n* Event type.\n* @name Highcharts.PointDropEventObject#type\n* @type {\"drop\"}\n*/\n''; // Detaches doclets above\n\n;// ./code/es-modules/masters/modules/draggable-points.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nDraggablePoints_DraggablePoints.compose(G.Chart, G.Series);\n/* harmony default export */ const draggable_points_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "draggable_points_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "DraggablePoints_DragDropUtilities", "addEvents", "el", "types", "fn", "options", "removeFuncs", "map", "type", "countProps", "keys", "length", "getFirstProp", "p", "getNormalizedEvent", "e", "chart", "chartX", "chartY", "pointer", "normalize", "DraggablePoints_DragDropDefaults", "dragSensitivity", "dragHandle", "className", "color", "lineColor", "lineWidth", "zIndex", "guideBox", "cursor", "animObject", "DraggableChart_addEvents", "DraggableChart_countProps", "DraggableChart_getFirstProp", "DraggableChart_getNormalizedEvent", "doc", "DraggableChart_addEvent", "isArray", "merge", "pick", "chartHideDragHandles", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "chartSetGuideBoxState", "state", "dragGuideBox", "guideBoxOptions", "stateOptions", "attr", "stroke", "strokeWidth", "fill", "css", "pointerEvents", "chartZoomOrPanKeyPressed", "chartOptions", "panKey", "zooming", "flipResizeSide", "side", "left", "right", "top", "bottom", "initDragDrop", "point", "groupedPoints", "getGroupedPoints", "series", "data", "groupKey", "dragDrop", "groupBy", "points", "boosted", "i", "iEnd", "push", "pointClass", "index", "filter", "comparePoint", "liveRedraw", "getGuideBox", "setGuideBoxState", "add", "group", "dragDropData", "origin", "getPositionSnapshot", "res", "x", "y", "width", "height", "dragDropProps", "pointProps", "val", "axis", "mapView", "plotX", "plotY", "toPixels", "horiz", "id", "isDragging", "mouseUp", "draggedPastSensitivity", "newPoints", "numNewPoints", "newPoint", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "cancelClick", "firePointEvent", "newValues", "newPointId", "updatePoints", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasAddedDragDropEvents", "addDragDropEvents", "container", "isChartDraggable", "hasCartesianSeries", "polar", "isSeriesDraggable", "props", "optionName", "mouseDown", "dragPoint", "hoverPoint", "dragDropOptions", "draggableX", "draggableY", "zoomOrPanKeyPressed", "hasDraggedAnnotation", "isPointMovable", "hasMovableX", "hasMovableY", "seriesDragDropOptions", "pointDragDropOptions", "updateProps", "move", "yAxis", "xAxis", "mouseIsDown", "mouseMove", "seriesDragDropOpts", "hasDraggedPastSensitivity", "sensitivity", "orig", "oldX", "oldY", "newX", "newY", "distance", "Math", "sqrt", "getNewPoints", "newPos", "resizeProp", "updateProp", "<PERSON><PERSON><PERSON>", "resize", "getDropValues", "dragMove", "dX", "dY", "oldDx", "inverted", "showDragHandles", "resizeGuideBox", "resizeSide", "beforeResize", "resizeRect", "rect", "updateSide", "update", "resizeAttrs", "max", "reversed", "prevdX", "prevdY", "translate", "passive", "unbindDragDropMouseUp", "animation", "animOptions", "isDragDropAnimating", "redraw", "setTimeout", "duration", "DraggablePoints_DraggableChart", "compose", "ChartClass", "chartProto", "DragDropProps_flipResizeSide", "isNumber", "DragDropProps_merge", "DragDropProps_pick", "line", "column", "pointVals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "stackY", "round", "flipSide", "handlePositioner", "bBox", "shapeArgs", "graphic", "getBBox", "handleFormatter", "radius", "r", "centerX", "boxplot", "low", "lowPlot", "propValidate", "q1", "q1Plot", "median", "q3", "q3Plot", "high", "highPlot", "errorbar", "bullet", "target", "targetGraphic", "barX", "ohlc", "plotLow", "open", "close", "plotHigh", "plotOpen", "plotClose", "waterfall", "isSum", "isIntermediateSum", "columnrange", "arearange", "graphics", "arearangeHandleFormatter", "xrange", "xrangeHandlePositioner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x2", "centerY", "xProp", "offsetY", "columnMetrics", "offset", "len", "DraggablePoints_DragDropProps", "flags", "gantt", "start", "validateIndividualDrag", "milestone", "end", "DraggablePoints_addEvents", "DraggablePoints_getNormalizedEvent", "DraggablePoints_initDragDrop", "DraggablePoints_addEvent", "clamp", "DraggablePoints_isNumber", "DraggablePoints_merge", "mouseOut", "isHoveringHandle", "onPointMouseOut", "onPointMouseOver", "mouseOver", "is3d", "onPointRemove", "pointGetDropValues", "result", "<PERSON><PERSON><PERSON><PERSON>", "updateSingleProp", "limitToRange", "dir", "direction", "toUpperCase", "time", "defaultPrecision", "categories", "precision", "min", "parse", "Infinity", "limitToMapRange", "lonLatMin", "pixelsToLonLat", "lonLatMax", "plotBox", "projection", "name", "isNaN", "maxLatitude", "temp", "hasCoordinates", "lonLatRes", "plotLeft", "plotHeight", "plotTop", "oldVal", "newVal", "toValue", "pointShowDragHandles", "renderer", "pos", "handle", "path", "handleOptions", "handleAttrs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validate", "g", "markerGroup", "minEdge", "translateX", "translateY", "rotation", "element", "onResizeHandleMouseDown", "stopPropagation", "onResizeHandleMouseOut", "seriesGetGuideBox", "minX", "maxX", "minY", "maxY", "changed", "plotX2", "skip<PERSON><PERSON>", "G", "DraggablePoints_DraggablePoints", "SeriesClass", "seriesProto", "PointClass", "seriesTypes", "pointProto", "seriesType", "Chart", "Series"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,EACrC,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,sCAAuC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAM,GACxG,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,sCAAsC,CAAGD,EAAQD,EAAK,WAAc,EAE5EA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,AAACC,GACnC,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,AAACZ,IACxB,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,IAAOd,EAAO,OAAU,CACxB,IAAOA,EAER,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAChB,EAASkB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,EAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAerH,GAAM,CAAEE,SAAAA,CAAQ,CAAE,CAAID,IAiGaE,EANT,CACtBC,UAlEJ,SAAmBC,CAAE,CAAEC,CAAK,CAAEC,CAAE,CAAEC,CAAO,EACrC,IAAMC,EAAcH,EAAMI,GAAG,CAAC,AAACC,GAAST,EAASG,EAAIM,EAAMJ,EAAIC,IAC/D,OAAO,WACH,IAAK,IAAMD,KAAME,EACbF,GAER,CACJ,EA4DIK,WA/CJ,SAAoBnB,CAAG,EACnB,OAAOJ,OAAOwB,IAAI,CAACpB,GAAKqB,MAAM,AAClC,EA8CIC,aAjCJ,SAAsBtB,CAAG,EACrB,IAAK,IAAMuB,KAAKvB,EACZ,GAAIJ,OAAOO,cAAc,CAACC,IAAI,CAACJ,EAAKuB,GAChC,OAAOvB,CAAG,CAACuB,EAAE,AAGzB,EA4BIC,mBAfJ,SAA4BC,CAAC,CAAEC,CAAK,EAChC,MAAQ,CAAA,AAAoB,KAAA,IAAbD,EAAEE,MAAM,EACnB,AAAoB,KAAA,IAAbF,EAAEG,MAAM,AAAe,GAC9BF,EAAMG,OAAO,EAAEC,UAAUL,IAAMA,CAEvC,CAWA,EAiXmCM,EA5TV,CA+LrBC,gBAAiB,EAQjBC,WAAY,CA0BRC,UAAW,yBAOXC,MAAO,OAOPC,UAAW,qBAMXC,UAAW,EAMXC,OAAQ,GACZ,EASAC,SAAU,CAON,QAAW,CAOPL,UAAW,8BAMXG,UAAW,EAOXD,UAAW,OAOXD,MAAO,qBAMPK,OAAQ,OAMRF,OAAQ,GACZ,CACJ,CACJ,EAsBM,CAAEG,WAAAA,CAAU,CAAE,CAAIjC,IAElB,CAAEG,UAAW+B,CAAwB,CAAEvB,WAAYwB,CAAyB,CAAErB,aAAcsB,CAA2B,CAAEpB,mBAAoBqB,CAAiC,CAAE,CAAGnC,EAGnL,CAAEoC,IAAAA,CAAG,CAAE,CAAItC,IAEX,CAAEC,SAAUsC,CAAuB,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAI1C,IAkDrE,SAAS2C,IACL,IAAoBC,EAAe1B,AAArB,IAAI,CAAuB0B,WAAW,EAAI,CAAC,EACzD,GAAIA,EAAa,CACb,IAAK,IAAM1D,KAAOE,OAAOwB,IAAI,CAACgC,GACtBA,CAAW,CAAC1D,EAAI,CAAC2D,OAAO,EACxBD,CAAW,CAAC1D,EAAI,CAAC2D,OAAO,EAGhC,QAAO3B,AAPG,IAAI,CAOD0B,WAAW,AAC5B,CACJ,CAaA,SAASE,EAAsBC,CAAK,CAAExC,CAAO,EACzC,IAAMwB,EAAW,IAAI,CAACiB,YAAY,CAAEC,EAAkBR,EAAMlB,EAAiCQ,QAAQ,CAAExB,GAAU2C,EAAeT,EAAMQ,EAAgB,OAAU,CAChKA,CAAe,CAACF,EAAM,EACtB,OAAOhB,EACFoB,IAAI,CAAC,CACN,MAASD,EAAaxB,SAAS,CAC/B0B,OAAQF,EAAatB,SAAS,CAC9ByB,YAAaH,EAAarB,SAAS,CACnCyB,KAAMJ,EAAavB,KAAK,CACxBK,OAAQkB,EAAalB,MAAM,CAC3BF,OAAQoB,EAAapB,MAAM,AAC/B,GAEKyB,GAAG,CAAC,CAAEC,cAAe,MAAO,EACrC,CAWA,SAASC,EAAyBxC,CAAC,EAE/B,IAAoByC,EAAexC,AAArB,IAAI,CAAuBX,OAAO,CAACW,KAAK,EAAI,CAAC,EAAGyC,EAASD,EAAaC,MAAM,EAAID,EAAaC,MAAM,CAAG,MACpH,OAAQ1C,CAAC,CAD4HC,AAAvH,IAAI,CAAyH0C,OAAO,CAAC1E,GAAG,EAAIgC,AAA5I,IAAI,CAA8I0C,OAAO,CAAC1E,GAAG,CAAG,MAC5J,EAAI+B,CAAC,CAAC0C,EAAO,AACnC,CA2EA,SAASE,EAAeC,CAAI,EACxB,MAAO,CAAA,CACHC,KAAM,QACNC,MAAO,OACPC,IAAK,SACLC,OAAQ,KACZ,CAAA,CAAC,CAACJ,EAAK,AACX,CA2LA,SAASK,EAAalD,CAAC,CAAEmD,CAAK,EAC1B,IACIrC,EADEsC,EAAgBC,AAhL1B,SAA0BF,CAAK,EAC3B,IAAMG,EAASH,EAAMG,MAAM,CAAEC,EAAOD,EAAOhE,OAAO,CAACiE,IAAI,EAAI,EAAE,CAAEC,EAAWF,EAAOhE,OAAO,CAACmE,QAAQ,CAACC,OAAO,CACrGC,EAAS,EAAE,CACf,GAAIL,EAAOM,OAAO,EAAIrC,EAAQgC,GAC1B,IAAK,IAAIM,EAAI,EAAGC,EAAOP,EAAK3D,MAAM,CAAEiE,EAAIC,EAAM,EAAED,EAC5CF,EAAOI,IAAI,CAAC,IAAIT,EAAOU,UAAU,CACjCV,EAAQC,CAAI,CAACM,EAAE,GACfF,CAAM,CAACA,EAAO/D,MAAM,CAAG,EAAE,CAACqE,KAAK,CAAGJ,OAItCF,EAASL,EAAOK,MAAM,CAE1B,OAAOR,EAAM7D,OAAO,CAACkE,EAAS,CAE1BG,EAAOO,MAAM,CAAC,AAACC,GAAkBA,EAAa7E,OAAO,CAACkE,EAAS,GAC3DL,EAAM7D,OAAO,CAACkE,EAAS,EAE3B,CAACL,EAAM,AACf,EA6J2CA,GAAQG,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAGrFwB,EAAK6B,EAAOhE,OAAO,CAACmE,QAAQ,EAAIH,EAAOhE,OAAO,CAACmE,QAAQ,CAACW,UAAU,CAAE,CAAA,KACrEnE,EAAM8B,YAAY,CAAGjB,EAAWwC,EAAOe,WAAW,CAACjB,GACnDnD,EACKqE,gBAAgB,CAAC,UAAWhB,EAAOhE,OAAO,CAACmE,QAAQ,CAAC3C,QAAQ,EAC5DyD,GAAG,CAACjB,EAAOkB,KAAK,GAGzBvE,EAAMwE,YAAY,CAAG,CACjBC,OAAQC,AAzFhB,SAA6B3E,CAAC,CAAE2D,CAAM,CAAE7C,CAAQ,EAC5C,IAAM8D,EAAM,CACR1E,OAAQF,EAAEE,MAAM,CAChBC,OAAQH,EAAEG,MAAM,CAChBW,SAAUA,GAAY,CAClB+D,EAAG/D,EAASoB,IAAI,CAAC,KACjB4C,EAAGhE,EAASoB,IAAI,CAAC,KACjB6C,MAAOjE,EAASoB,IAAI,CAAC,SACrB8C,OAAQlE,EAASoB,IAAI,CAAC,SAC1B,EACAyB,OAAQ,CAAC,CACb,EAEA,IAAK,IAAMR,KAASQ,EAAQ,CACxB,IAAMsB,EAAgB9B,EAAMG,MAAM,CAAC2B,aAAa,EAAI,CAAC,EAAGC,EAAa,CAAC,EAGtE,IAAK,IAAMjH,KAAOE,OAAOwB,IAAI,CAACsF,GAAgB,CAC1C,IAAME,EAAMF,CAAa,CAAChH,EAAI,CAAEmH,EAAOjC,EAAMG,MAAM,CAAC6B,EAAIC,IAAI,CAAG,OAAO,AACtEF,CAAAA,CAAU,CAACjH,EAAI,CAAGkF,CAAK,CAAClF,EAAI,CAKxBkF,EAAMG,MAAM,CAACrD,KAAK,CAACoF,OAAO,EAAIlC,EAAMmC,KAAK,EAAInC,EAAMoC,KAAK,CACxDL,CAAU,CAACjH,EAAM,SAAS,CAAGA,AAAQ,MAARA,EACzBkF,EAAMmC,KAAK,CAAGnC,EAAMoC,KAAK,CAG7BL,CAAU,CAACjH,EAAM,SAAS,CAGtBmH,EAAKI,QAAQ,CAACrC,CAAK,CAAClF,EAAI,EACnBmH,CAAAA,EAAKK,KAAK,CAAGzF,EAAEE,MAAM,CAAGF,EAAEG,MAAM,AAAD,CAEhD,CACA+E,EAAW/B,KAAK,CAAGA,EACnByB,EAAIjB,MAAM,CAACR,EAAMuC,EAAE,CAAC,CAAGR,CAC3B,CACA,OAAON,CACX,EAiDoC5E,EAAGoD,EAAetC,GAC9CqC,MAAOA,EACPC,cAAeA,EACfuC,WAAY,CAAA,CAChB,CACJ,CA6LA,SAASC,EAAQ5F,CAAC,CAAEC,CAAK,EACrB,IAAMwE,EAAexE,EAAMwE,YAAY,CACvC,GAAIA,GACAA,EAAakB,UAAU,EACvBlB,EAAaoB,sBAAsB,EACnCpB,EAAatB,KAAK,CAACG,MAAM,CAAE,CAC3B,IAAMH,EAAQsB,EAAatB,KAAK,CAAE2C,EAAYrB,EAAaqB,SAAS,CAAEC,EAAe7E,EAA0B4E,GAAYE,EAAWD,AAAiB,IAAjBA,EAClI5E,EAA4B2E,GAC5B,IAEA7F,CAAAA,EAAM0B,WAAW,EACjB1B,EAAMgG,eAAe,GAGzBjG,EAAEkG,cAAc,GAChBjG,EAAMkG,WAAW,CAAG,CAAA,EAEpBhD,EAAMiD,cAAc,CAAC,OAAQ,CACzB1B,OAAQD,EAAaC,MAAM,CAC3BxE,OAAQF,EAAEE,MAAM,CAChBC,OAAQH,EAAEG,MAAM,CAChB2F,UAAWA,EACXC,aAAcA,EACdC,SAAUA,GAAYA,EAASK,SAAS,CACxCC,WAAYN,GAAYA,EAAS7C,KAAK,CAACuC,EAAE,AAC7C,EAAG,WACCa,EAAatG,EACjB,EACJ,CAEA,OAAOA,EAAMwE,YAAY,CAGrBxE,EAAM8B,YAAY,GAClB9B,EAAM8B,YAAY,CAACH,OAAO,GAC1B,OAAO3B,EAAM8B,YAAY,CAEjC,CAMA,SAASyE,IAEA,IAAI,CAACC,sBAAsB,EAC5BC,AAjmBR,SAA2BzG,CAAK,EAC5B,IAAM0G,EAAY1G,EAAM0G,SAAS,CAE7BC,AA+XR,CAAA,SAA0B3G,CAAK,EAC3B,IAAI4D,EAAI5D,EAAMqD,MAAM,CAAGrD,EAAMqD,MAAM,CAAC1D,MAAM,CAAG,EAC7C,GAAI,AAACK,EAAM4G,kBAAkB,EAAI,CAAC5G,EAAM6G,KAAK,EACzC7G,EAAMoF,OAAO,CACb,CAAA,KAAOxB,KACH,GAAI5D,EAAMqD,MAAM,CAACO,EAAE,CAACvE,OAAO,CAACmE,QAAQ,EAChCsD,AAoDhB,SAA2BzD,CAAM,EAC7B,IACI6B,EADE6B,EAAQ,CAAC,aAAc,aAAa,CAAE/B,EAAgB3B,EAAO2B,aAAa,EAAI,CAAC,EAGrF,IAAK,IAAMhH,KAAOE,OAAOwB,IAAI,CAACsF,GAEtBE,AADJA,CAAAA,EAAMF,CAAa,CAAChH,EAAI,AAAD,EACfgJ,UAAU,EACdD,EAAMjD,IAAI,CAACoB,EAAI8B,UAAU,EAKjC,IAAIpD,EAAImD,EAAMpH,MAAM,CACpB,KAAOiE,KACH,GAAIP,EAAOhE,OAAO,CAACmE,QAAQ,CAACuD,CAAK,CAACnD,EAAE,CAAC,CACjC,MAAO,CAAA,CAGnB,EAtEkC5D,EAAMqD,MAAM,CAACO,EAAE,EACjC,MAAO,CAAA,CAEf,CAEJ,MAAO,CAAA,CACX,CAAA,EA3YyB5D,KACjBgB,EAAyB0F,EAAW,CAAC,YAAa,aAAa,CAAE,AAAC3G,IAC9DkH,AAodZ,CAAA,SAAmBlH,CAAC,CAAEC,CAAK,EACvB,IAAMkH,EAAYlH,EAAMmH,UAAU,CAAEC,EAAkB7F,EAAM2F,GAAaA,EAAU7D,MAAM,CAAChE,OAAO,CAACmE,QAAQ,CAAE0D,GAAaA,EAAU7H,OAAO,CAACmE,QAAQ,EAAG6D,EAAaD,EAAgBC,UAAU,EAAI,CAAA,EAAOC,EAAaF,EAAgBE,UAAU,EAAI,CAAA,EAInP,GAFAtH,EAAMkG,WAAW,CAAG,CAAA,EAIpB,CAAA,CAAA,CAAEmB,CAAAA,GAAcC,CAAS,GAErBtH,EAAMuH,mBAAmB,CAACxH,EAAC,IAE3BC,EAAMwH,oBAAoB,EAI9B,GAAIxH,EAAMwE,YAAY,EAAIxE,EAAMwE,YAAY,CAACkB,UAAU,CAAE,CACrDC,EAAQ5F,EAAGC,GACX,MACJ,CAEIkH,GAAaO,AAnFrB,SAAwBvE,CAAK,EACzB,IACIrD,EAAG6H,EAAaC,EADdtE,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAAE4H,EAAwBvE,EAAOhE,OAAO,CAACmE,QAAQ,EAAI,CAAC,EAAGqE,EAAuB3E,EAAM7D,OAAO,EAAI6D,EAAM7D,OAAO,CAACmE,QAAQ,CAAEsE,EAAczE,EAAO2B,aAAa,CAG5M,IAAK,IAAMhH,KAAO8J,EAEVjI,AAAW,MAAXA,AADJA,CAAAA,EAAIiI,CAAW,CAAC9J,EAAI,AAAD,EACbmH,IAAI,EAAYtF,EAAEkI,IAAI,CACxBL,EAAc,CAAA,EAEE,MAAX7H,EAAEsF,IAAI,EAAYtF,EAAEkI,IAAI,EAC7BJ,CAAAA,EAAc,CAAA,CAAG,EAKzB,MAAQ,AAACC,CAAAA,EAAsBP,UAAU,EAAIK,GACzCE,EAAsBN,UAAU,EAAIK,CAAU,GAC9C,CAAEE,CAAAA,GACEA,AAAoC,CAAA,IAApCA,EAAqBR,UAAU,EAC/BQ,AAAoC,CAAA,IAApCA,EAAqBP,UAAU,AAAS,GAC3C,CAAA,CAAC,CAAEjE,CAAAA,EAAO2E,KAAK,EAAI3E,EAAO4E,KAAK,AAAD,GAC3BjI,EAAMoF,OAAO,AAAD,CACxB,EA6DoC8B,KAC5BlH,EAAMkI,WAAW,CAAG,CAAA,EACpBjF,EAAalD,EAAGmH,GAChBA,EAAUf,cAAc,CAAC,YAAapG,IAE9C,CAAA,EA7esBoB,EAAkCpB,EAAGC,GAAQA,EAC3D,GACAgB,EAAyB0F,EAAW,CAAC,YAAa,YAAY,CAAE,AAAC3G,IAC7DoI,AAqfZ,CAAA,SAAmBpI,CAAC,CAAEC,CAAK,EAEvB,GAAIA,EAAMuH,mBAAmB,CAACxH,GAC1B,OAEJ,IAAMyE,EAAexE,EAAMwE,YAAY,CACnCtB,EAAOkF,EAAoBvC,EAAWC,EAAe,EAAGC,EACxDvB,GAAgBA,EAAakB,UAAU,EAAIlB,EAAatB,KAAK,CAACG,MAAM,GAEpE+E,EAAqBlF,AADrBA,CAAAA,EAAQsB,EAAatB,KAAK,AAAD,EACEG,MAAM,CAAChE,OAAO,CAACmE,QAAQ,CAElDzD,EAAEkG,cAAc,GAEXzB,EAAaoB,sBAAsB,EACpCpB,CAAAA,EAAaoB,sBAAsB,CAAGyC,AAtLlD,SAAmCtI,CAAC,CAAEC,CAAK,CAAEsI,CAAW,EACpD,IAAMC,EAAOvI,EAAMwE,YAAY,CAACC,MAAM,CAAE+D,EAAOD,EAAKtI,MAAM,CAAEwI,EAAOF,EAAKrI,MAAM,CAAEwI,EAAO3I,EAAEE,MAAM,CAAE0I,EAAO5I,EAAEG,MAAM,CAEhH,OAAO0I,AAFsHC,KAAKC,IAAI,CAAC,AAACJ,CAAAA,EAAOF,CAAG,EAAME,CAAAA,EAAOF,CAAG,EAC9J,AAACG,CAAAA,EAAOF,CAAG,EAAME,CAAAA,EAAOF,CAAG,GACbH,CACtB,EAkL4EvI,EAAGC,EAAOwB,EAAK0B,EAAM7D,OAAO,CAACmE,QAAQ,EACjGN,EAAM7D,OAAO,CAACmE,QAAQ,CAAClD,eAAe,CAAE8H,GACxCA,EAAmB9H,eAAe,CAAED,EAAiCC,eAAe,EAAC,EAIzFkE,EAAaoB,sBAAsB,GAEnCpB,EAAaqB,SAAS,CAAGkD,AAxTrC,SAAsBvE,CAAY,CAAEwE,CAAM,EACtC,IAAM9F,EAAQsB,EAAatB,KAAK,CAAEG,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAAEX,EAAUkC,EAAM8B,EAAOhE,OAAO,CAACmE,QAAQ,CAAEN,EAAM7D,OAAO,CAACmE,QAAQ,EAAGsE,EAAc,CAAC,EAAGmB,EAAazE,EAAa0E,UAAU,CAAEC,EAAU,CAAC,EAAGnE,EAAgB9B,EAAMG,MAAM,CAAC2B,aAAa,CAIjQ,IAAK,IAAMhH,KAAOgH,EAAe,CAC7B,IAAME,EAAMF,CAAa,CAAChH,EAAI,CAG1BiL,CAAAA,CAAAA,GAAeA,IAAejL,GAC7BkH,EAAIkE,MAAM,EACXlE,CAAAA,CAAAA,EAAI8B,UAAU,EAAI3H,AAA4B,CAAA,IAA5BA,CAAO,CAAC6F,EAAI8B,UAAU,CAAC,AAAS,CAAC,GAMnDiC,CAAAA,GAAe/D,EAAI6C,IAAI,EACtB7C,CAAAA,AAAa,MAAbA,EAAIC,IAAI,EAAY9F,EAAQgI,UAAU,EACnCnC,AAAa,MAAbA,EAAIC,IAAI,EAAY9F,EAAQiI,UAAU,AAAD,CAAE,IACvCtH,EAAMoF,OAAO,CACb0C,CAAW,CAAC9J,AAAQ,MAARA,EAAc,MAAQ,MAAM,CAAGkH,EAG3C4C,CAAW,CAAC9J,EAAI,CAAGkH,EAG/B,CAEA,IAAK,IAAMrF,KAEXoJ,EACI,CAAC/F,EAAM,CACPsB,EAAarB,aAAa,CAC1BgG,CAAO,CAACtJ,EAAE4F,EAAE,CAAC,CAAG,CACZvC,MAAOrD,EACPuG,UAAWvG,EAAEwJ,aAAa,CAAC7E,EAAaC,MAAM,CAAEuE,EAAQlB,EAC5D,EAEJ,OAAOqB,CACX,EAgRkD3E,EAAczE,GAIpDgG,EAAWD,AAAiB,IAD5BA,CAAAA,EAAe7E,EADf4E,EAAYrB,EAAaqB,SAAS,CACgB,EAE9C3E,EAA4B2E,GAC5B,KAEJ3C,EAAMiD,cAAc,CAAC,OAAQ,CACzB1B,OAAQD,EAAaC,MAAM,CAC3BoB,UAAWrB,EAAaqB,SAAS,CACjCE,SAAUA,GAAYA,EAASK,SAAS,CACxCC,WAAYN,GAAYA,EAAS7C,KAAK,CAACuC,EAAE,CACzCK,aAAcA,EACd7F,OAAQF,EAAEE,MAAM,CAChBC,OAAQH,EAAEG,MAAM,AACpB,EAAG,YACCoJ,AA7ahB,SAAkBvJ,CAAC,CAAEmD,CAAK,EACtB,IAAMG,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAAEsD,EAAOtD,EAAMwE,YAAY,CAAEnF,EAAUkC,EAAM8B,EAAOhE,OAAO,CAACmE,QAAQ,CAAEN,EAAM7D,OAAO,CAACmE,QAAQ,EAAG6D,EAAahI,EAAQgI,UAAU,CAAEC,EAAajI,EAAQiI,UAAU,CAAE7C,EAASnB,EAAKmB,MAAM,CAAEyE,EAAa5F,EAAK4F,UAAU,CAChQK,EAAKxJ,EAAEE,MAAM,CAAGwE,EAAOxE,MAAM,CAAEuJ,EAAKzJ,EAAEG,MAAM,CAAGuE,EAAOvE,MAAM,CAC1DuJ,EAAQF,CAEVvJ,CAAAA,EAAM0J,QAAQ,GACdH,EAAK,CAACC,EACNA,EAAK,CAACC,GAINjI,EAAKnC,EAAQ8E,UAAU,CAAE,CAAA,IACzBmC,EAAatG,EAAO,CAAA,GAEpBkD,EAAMyG,eAAe,IAIjBT,EAEAU,AAseZ,SAAwB1G,CAAK,CAAEqG,CAAE,CAAEC,CAAE,EACjC,IAAMnG,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAAEwE,EAAexE,EAAMwE,YAAY,CAAEyE,EAAa5F,EAAO2B,aAAa,CAACR,EAAa0E,UAAU,CAAC,CAEhJnD,EAAWvB,EAAaqB,SAAS,CAAC3C,EAAMuC,EAAE,CAAC,CAACW,SAAS,CAAEyD,EAAa,AAAiC,YAAjC,OAAOZ,EAAWY,UAAU,CAC5FZ,EAAWY,UAAU,CAAC9D,EAAU7C,GAAS+F,EAAWY,UAAU,AAE9DZ,CAAAA,EAAWa,YAAY,EACvBb,EAAWa,YAAY,CAAC9J,EAAM8B,YAAY,CAAEiE,EAAU7C,GAG1D6G,AAwBJ,SAAoBC,CAAI,CAAEC,CAAU,CAAEC,CAAM,EACxC,IAAIC,EACJ,OAAQF,GACJ,IAAK,OACDE,EAAc,CACVvF,EAAGoF,EAAK/H,IAAI,CAAC,KAAOiI,EAAOtF,CAAC,CAC5BE,MAAO+D,KAAKuB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,SAAWiI,EAAOtF,CAAC,CACpD,EACA,KACJ,KAAK,QACDuF,EAAc,CACVrF,MAAO+D,KAAKuB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,SAAWiI,EAAOtF,CAAC,CACpD,EACA,KACJ,KAAK,MACDuF,EAAc,CACVtF,EAAGmF,EAAK/H,IAAI,CAAC,KAAOiI,EAAOrF,CAAC,CAC5BE,OAAQ8D,KAAKuB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,UAAYiI,EAAOrF,CAAC,CACtD,EACA,KACJ,KAAK,SACDsF,EAAc,CACVpF,OAAQ8D,KAAKuB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,UAAYiI,EAAOrF,CAAC,CACtD,CAGR,CACAmF,EAAK/H,IAAI,CAACkI,EACd,EApDenK,EAAM8B,YAAY,CAAEmH,AAAoB,MAApBA,EAAW9D,IAAI,EAAY9B,EAAO4E,KAAK,CAACoC,QAAQ,EAC3EpB,AAAoB,MAApBA,EAAW9D,IAAI,EAAY9B,EAAO2E,KAAK,CAACqC,QAAQ,CAChD1H,EAAekH,GAAcA,EAAY,CACzCjF,EAAGqE,AAAoB,MAApBA,EAAW9D,IAAI,CACdoE,EAAM/E,CAAAA,EAAaC,MAAM,CAAC6F,MAAM,EAAI,CAAA,EAAK,EAC7CzF,EAAGoE,AAAoB,MAApBA,EAAW9D,IAAI,CACdqE,EAAMhF,CAAAA,EAAaC,MAAM,CAAC8F,MAAM,EAAI,CAAA,EAAK,CACjD,EACJ,EAxf2BrH,EAAOqG,EAAIC,GAI1BxJ,EAAM8B,YAAY,CAAC0I,SAAS,CAACnD,EAAakC,EAAK,EAAGjC,EAAakC,EAAK,GAI5E/E,EAAO6F,MAAM,CAAGf,EAChB9E,EAAO8F,MAAM,CAAGf,CACpB,EA+YyBzJ,EAAGmD,EAChB,IAGZ,CAAA,EAhiBsB/B,EAAkCpB,EAAGC,GAAQA,EAC3D,EAAG,CACCyK,QAAS,CAAA,CACb,GACApJ,EAAwBqF,EAAW,aAAc,AAAC3G,IAC9C4F,EAAQxE,EAAkCpB,EAAGC,GAAQA,EACzD,GACAA,EAAM0K,qBAAqB,CAAG1J,EAAyBI,EAAK,CAAC,UAAW,WAAW,CAAE,AAACrB,IAClF4F,EAAQxE,EAAkCpB,EAAGC,GAAQA,EACzD,EAAG,CACCyK,QAAS,CAAA,CACb,GAEAzK,EAAMwG,sBAAsB,CAAG,CAAA,EAE/BnF,EAAwBrB,EAAO,UAAW,KAClCA,EAAM0K,qBAAqB,EAC3B1K,EAAM0K,qBAAqB,EAEnC,GAER,EAokB0B,IAAI,CAE9B,CAuFA,SAASpE,EAAatG,CAAK,CAAE2K,CAAS,EAClC,IAEI5E,EAFEF,EAAY7F,EAAMwE,YAAY,CAACqB,SAAS,CAAE+E,EAAc7J,EAAW4J,GAIzE,IAAK,IAAM3M,KAHXgC,EAAM6K,mBAAmB,CAAG,CAAA,EAGV3M,OAAOwB,IAAI,CAACmG,IAE1BE,AADAA,CAAAA,EAAWF,CAAS,CAAC7H,EAAI,AAAD,EACfkF,KAAK,CAACgH,MAAM,CAACnE,EAASK,SAAS,CAAE,CAAA,GAE9CpG,EAAM8K,MAAM,CAACF,GAIbG,WAAW,KACP,OAAO/K,EAAM6K,mBAAmB,CAC5B7K,EAAMmH,UAAU,EAAI,CAACnH,EAAM0B,WAAW,EACtC1B,EAAMmH,UAAU,CAACwC,eAAe,EAExC,EAAGiB,EAAYI,QAAQ,CAC3B,CAW6B,IAAMC,EALZ,CACnBC,QAjnBJ,SAAiBC,CAAU,EACvB,IAAMC,EAAaD,EAAW3M,SAAS,AAClC4M,CAAAA,EAAWpF,eAAe,GAC3BoF,EAAWpF,eAAe,CAAGvE,EAC7B2J,EAAW/G,gBAAgB,CAAGzC,EAC9BwJ,EAAW7D,mBAAmB,CAAGhF,EACjClB,EAAwB8J,EAAY,SAAU5E,GAEtD,EA0mBI5D,eAAAA,EACAM,aAAAA,CACJ,EAiBM,CAAEN,eAAgB0I,CAA4B,CAAE,CAAGJ,EAEnD,CAAEK,SAAAA,CAAQ,CAAE/J,MAAOgK,CAAmB,CAAE/J,KAAMgK,CAAkB,CAAE,CAAI1M,IAOtE2M,EAAO,CACT7G,EAAG,CACCO,KAAM,IACN4C,KAAM,CAAA,CACV,EACAlD,EAAG,CACCM,KAAM,IACN4C,KAAM,CAAA,CACV,CACJ,EAKM2D,EAAS,CACX9G,EAAG,CACCO,KAAM,IACN4C,KAAM,CAAA,CACV,EACAlD,EAAG,CACCM,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EAERU,aAAc,CAACjJ,EAAU8K,EAAWzI,KAKhC,IAEI6B,EAFE6G,EAAgBJ,EAAmBtI,EAAM2I,OAAO,CACtD3I,EAAMG,MAAM,CAACyI,mBAAmB,EAAGxG,EAAQzE,EAASoB,IAAI,CAAC,KAAM8J,EAAYT,EAASpI,EAAM8I,MAAM,EAAK9I,EAAM8I,MAAM,CAAI9I,CAAAA,EAAM2B,CAAC,EAAI,CAAA,EAAM3B,EAAMG,MAAM,CAAChE,OAAO,CAAC0M,SAAS,EAAI,EAAGlH,EAAIkH,EAAYJ,EAAU9G,CAAC,CAElM3B,CAAAA,EAAMG,MAAM,CAAC2E,KAAK,CAACqC,QAAQ,CAAGxF,EAAIkH,EAAYlH,GAAKkH,CAAQ,GAE3DhH,EAASlE,EAASoB,IAAI,CAAC,UAEvBpB,EAASoB,IAAI,CAAC,CACV8C,OAAQ8D,KAAKuB,GAAG,CAAC,EAAGvB,KAAKoD,KAAK,CAAClH,EAF5B6G,CAAAA,EAAgBA,EAAgBtG,EAAQP,EAAS,CAAA,GAGxD,IAIAlE,EAASoB,IAAI,CAAC,CACV4C,EAAGgE,KAAKoD,KAAK,CAAC3G,EAASsG,CAAAA,EAAgBA,EAAgBtG,EAAQ,CAAA,EACnE,EAER,EAGAuE,WAAY,CAAC8B,EAAWzI,KACpB,IAAkCxB,EAAc1B,AAAlCkD,EAAMG,MAAM,CAACrD,KAAK,CAAsB0B,WAAW,CAAEkB,EAAO+I,EAAU9G,CAAC,EAAK3B,CAAAA,EAAMG,MAAM,CAAChE,OAAO,CAAC0M,SAAS,EAAI,CAAA,EACxH,MAAQ,SAAUG,EAAWb,EAA6BzI,GAM9D,OAJIlB,GAAeA,CAAW,CAACwK,EAAS,GACpCxK,CAAW,CAACwK,EAAS,CAACvK,OAAO,GAC7B,OAAOD,CAAW,CAACwK,EAAS,EAEzBtJ,CACX,EAEAuJ,iBAAkB,AAACjJ,IACf,IAAMkJ,EAAQlJ,EAAMmJ,SAAS,EACxBnJ,EAAMoJ,OAAO,EAAIpJ,EAAMoJ,OAAO,CAACC,OAAO,IACvC,CAAC,EAAIlC,EAAWnH,EAAMG,MAAM,CAAC2E,KAAK,CAACqC,QAAQ,CAAE0B,EAAY7I,EAAMG,MAAM,CAAChE,OAAO,CAAC0M,SAAS,EAAI,EAAGlH,EAAI3B,EAAM2B,CAAC,EAAI,EAEjH,MAAO,CACHD,EAAGwH,EAAKxH,CAAC,EAAI,EACbC,EAAG7B,AAJsH,AAAC,CAACqH,GAAYxF,GAAKkH,GAC3I1B,GAAYxF,EAAIkH,EAGJK,EAAKvH,CAAC,EAAI,EAAK,AAACuH,CAAAA,EAAKvH,CAAC,EAAI,CAAA,EAAMuH,CAAAA,EAAKrH,MAAM,EAAI,CAAA,CAChE,CACJ,EAEAyH,gBAAiB,AAACtJ,IACd,IAAMmJ,EAAYnJ,EAAMmJ,SAAS,EAAI,CAAC,EAAGI,EAASJ,EAAUK,CAAC,EAAI,EACjE5H,EAAQuH,EAAUvH,KAAK,EAAI,EAAG6H,EAAU7H,EAAQ,EAChD,MAAO,CAEH,CAAC,IAAK2H,EAAQ,EAAE,CAChB,CAAC,IAAKE,EAAU,EAAG,EAAE,CAErB,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAG,EAAE,CACpC,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAG,EAAE,CAEpC,CAAC,IAAKA,EAAU,EAAG,EAAE,CACrB,CAAC,IAAK7H,EAAQ2H,EAAQ,EAAE,CAC3B,AACL,CACJ,CACJ,EAEMG,EAAU,CACZhI,EAAG8G,EAAO9G,CAAC,CASXiI,IAAK,CACD7F,WAAY,eACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,SACZsC,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,EAAI,EACxBC,EAAG3B,EAAM4J,OAAO,AACpB,CAAA,EACAN,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM8J,EAAE,AAClD,EASAA,GAAI,CACAhG,WAAY,cACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,SACZsC,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,EAAI,EACxBC,EAAG3B,EAAM+J,MAAM,AACnB,CAAA,EACAT,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMgK,MAAM,EAAIhI,GAAOhC,EAAM2J,GAAG,AAC1E,EACAK,OAAQ,CAGJ/H,KAAM,IACN4C,KAAM,CAAA,CACV,EASAoF,GAAI,CACAnG,WAAY,cACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,MACZsC,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,EAAI,EACxBC,EAAG3B,EAAMkK,MAAM,AACnB,CAAA,EACAZ,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMmK,IAAI,EAAInI,GAAOhC,EAAMgK,MAAM,AAC3E,EASAG,KAAM,CACFrG,WAAY,gBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,MACZsC,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,EAAI,EACxBC,EAAG3B,EAAMoK,QAAQ,AACrB,CAAA,EACAd,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMiK,EAAE,AAClD,CACJ,EAEMI,EAAW,CACb3I,EAAG8G,EAAO9G,CAAC,CACXiI,IAAK,CACD,GAAGD,EAAQC,GAAG,CACdE,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMmK,IAAI,AACpD,EACAA,KAAM,CACF,GAAGT,EAAQS,IAAI,CACfN,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM2J,GAAG,AACnD,CACJ,EAMMW,EAAS,CACX5I,EAAG8G,EAAO9G,CAAC,CACXC,EAAG6G,EAAO7G,CAAC,CASX4I,OAAQ,CACJzG,WAAY,kBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,MACZsC,iBAAkB,AAACjJ,IACf,IAAMkJ,EAAOlJ,EAAMwK,aAAa,CAACnB,OAAO,GACxC,MAAO,CACH3H,EAAG1B,EAAMyK,IAAI,CACb9I,EAAGuH,EAAKvH,CAAC,CAAGuH,EAAKrH,MAAM,CAAG,CAC9B,CACJ,EACAyH,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,AAC7C,CACJ,EAEMoB,EAAO,CACThJ,EAAG8G,EAAO9G,CAAC,CASXiI,IAAK,CACD7F,WAAY,eACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,SACZsC,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,CACpBC,EAAG3B,EAAM2K,OAAO,AACpB,CAAA,EACArB,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM4K,IAAI,EAAI5I,GAAOhC,EAAM6K,KAAK,AAC1E,EASAV,KAAM,CACFrG,WAAY,gBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,MACZsC,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,CACpBC,EAAG3B,EAAM8K,QAAQ,AACrB,CAAA,EACAxB,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM4K,IAAI,EAAI5I,GAAOhC,EAAM6K,KAAK,AAC1E,EASAD,KAAM,CACF9G,WAAY,gBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,AAAC3G,GAAWA,EAAM4K,IAAI,EAAI5K,EAAM6K,KAAK,CAAG,MAAQ,SAC5D5B,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,CACpBC,EAAG3B,EAAM+K,QAAQ,AACrB,CAAA,EACAzB,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMmK,IAAI,EAAInI,GAAOhC,EAAM2J,GAAG,AACxE,EASAkB,MAAO,CACH/G,WAAY,iBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,AAAC3G,GAAWA,EAAM4K,IAAI,EAAI5K,EAAM6K,KAAK,CAAG,SAAW,MAC/D5B,iBAAkB,AAACjJ,GAAW,CAAA,CAC1B0B,EAAG1B,EAAMmJ,SAAS,CAACzH,CAAC,CACpBC,EAAG3B,EAAMgL,SAAS,AACtB,CAAA,EACA1B,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMmK,IAAI,EAAInI,GAAOhC,EAAM2J,GAAG,AACxE,CACJ,EAEMsB,EAAY,CACdvJ,EAAG8G,EAAO9G,CAAC,CACXC,EAAG0G,EAAoBG,EAAO7G,CAAC,CAAE,CAC7B2H,gBAAiB,AAACtJ,GAAWA,EAAMkL,KAAK,EAAIlL,EAAMmL,iBAAiB,CAC/D,KACA3C,GAAQ7G,GAAG2H,kBAAkBtJ,IAAU,IAC/C,EACJ,EAEMoL,EAAc,CAChB1J,EAAG,CACCO,KAAM,IACN4C,KAAM,CAAA,CACV,EASA8E,IAAK,CACD7F,WAAY,eACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,SACZsC,iBAAkB,AAACjJ,IACf,IAAMkJ,EAAQlJ,EAAMmJ,SAAS,EAAInJ,EAAMoJ,OAAO,CAACC,OAAO,GACtD,MAAO,CACH3H,EAAGwH,EAAKxH,CAAC,EAAI,EACbC,EAAG,AAACuH,CAAAA,EAAKvH,CAAC,EAAI,CAAA,EAAMuH,CAAAA,EAAKrH,MAAM,EAAI,CAAA,CACvC,CACJ,EACAyH,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAMmK,IAAI,AACpD,EASAA,KAAM,CACFrG,WAAY,gBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,MACZsC,iBAAkB,AAACjJ,IACf,IAAMkJ,EAAQlJ,EAAMmJ,SAAS,EAAInJ,EAAMoJ,OAAO,CAACC,OAAO,GACtD,MAAO,CACH3H,EAAGwH,EAAKxH,CAAC,EAAI,EACbC,EAAGuH,EAAKvH,CAAC,EAAI,CACjB,CACJ,EACA2H,gBAAiBd,EAAO7G,CAAC,CAAC2H,eAAe,CACzCO,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM2J,GAAG,AACnD,CACJ,EAEM0B,EAAY,CACd3J,EAAG0J,EAAY1J,CAAC,CAShBiI,IAAK,CACD7F,WAAY,eACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,SACZsC,iBAAkB,AAACjJ,IACf,IAAMkJ,EAAQlJ,EAAMsL,QAAQ,EACxBtL,EAAMsL,QAAQ,CAAC,EAAE,EACjBtL,EAAMsL,QAAQ,CAAC,EAAE,CAACjC,OAAO,GAC7B,OAAOH,EAAO,CACVxH,EAAGwH,EAAKxH,CAAC,CAAGwH,EAAKtH,KAAK,CAAG,EACzBD,EAAGuH,EAAKvH,CAAC,CAAGuH,EAAKrH,MAAM,CAAG,CAC9B,EAAI,CAAEH,EAAG,KAAMC,EAAG,IAAK,CAC3B,EACA2H,gBAAiBiC,EACjB1B,aAAcuB,EAAYzB,GAAG,CAACE,YAAY,AAC9C,EASAM,KAAM,CACFrG,WAAY,gBACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,MACZsC,iBAAkB,AAACjJ,IACf,IAAMkJ,EAAQlJ,EAAMsL,QAAQ,EACxBtL,EAAMsL,QAAQ,CAAC,EAAE,EACjBtL,EAAMsL,QAAQ,CAAC,EAAE,CAACjC,OAAO,GAC7B,OAAOH,EAAO,CACVxH,EAAGwH,EAAKxH,CAAC,CAAGwH,EAAKtH,KAAK,CAAG,EACzBD,EAAGuH,EAAKvH,CAAC,CAAGuH,EAAKrH,MAAM,CAAG,CAC9B,EAAI,CAAEH,EAAG,KAAMC,EAAG,IAAK,CAC3B,EACA2H,gBAAiBiC,EACjB1B,aAAcuB,EAAYjB,IAAI,CAACN,YAAY,AAC/C,CACJ,EAEM2B,EAAS,CACX7J,EAAG,CACCM,KAAM,IACN4C,KAAM,CAAA,CACV,EASAnD,EAAG,CACCoC,WAAY,cACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,OACZsC,iBAAkB,AAACjJ,GAAWyL,EAAuBzL,EAAO,KAC5DsJ,gBAAiBoC,EACjB7B,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM2L,EAAE,AAClD,EASAA,GAAI,CACA7H,WAAY,cACZ7B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRS,WAAY,QACZsC,iBAAkB,AAACjJ,GAAWyL,EAAuBzL,EAAO,MAC5DsJ,gBAAiBoC,EACjB7B,aAAc,CAAC7H,EAAKhC,IAAWgC,GAAOhC,EAAM0B,CAAC,AACjD,CACJ,EAwCA,SAAS6J,EAAyBvL,CAAK,EACnC,IAAMuJ,EAASvJ,EAAMoJ,OAAO,CACxBpJ,EAAMoJ,OAAO,CAACC,OAAO,GAAGzH,KAAK,CAAG,EAAI,EACpC,EACJ,MAAO,CACH,CAAC,IAAK,EAAI2H,EAAQ,EAAE,CACpB,CAAC,IAAKA,EAAQA,EAAQ,EAAG,EAAG,EAAGA,AAAS,EAATA,EAAY,EAAE,CAC7C,CAAC,IAAKA,EAAQA,EAAQ,EAAG,EAAG,EAAGA,AAAS,GAATA,EAAa,EAAE,CACjD,AACL,CAKA,SAASmC,EAAqB1L,CAAK,EAC/B,IAAMmJ,EAAYnJ,EAAMmJ,SAAS,EAAInJ,EAAMoJ,OAAO,CAACC,OAAO,GAAIxJ,EAAMsJ,EAAUK,CAAC,EAAI,EACnF1J,EAASqJ,EAAUtH,MAAM,CAAGhC,EAAK+L,EAAUzC,EAAUtH,MAAM,CAAG,EAC9D,MAAO,CAEH,CAAC,IAAK,EAAGhC,EAAI,CACb,CAAC,IAAK,EAAG+L,EAAU,EAAE,CAErB,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAE,CACpC,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAE,CAEpC,CAAC,IAAK,EAAGA,EAAU,EAAE,CACrB,CAAC,IAAK,EAAG9L,EAAO,CACnB,AACL,CAOA,SAAS2L,EAAuBzL,CAAK,CAAE6L,CAAK,EACxC,IAAM1L,EAASH,EAAMG,MAAM,CAAE4E,EAAQ5E,EAAO4E,KAAK,CAAED,EAAQ3E,EAAO2E,KAAK,CAAE0B,EAAWrG,EAAOrD,KAAK,CAAC0J,QAAQ,CAAEsF,EAAU3L,EAAO4L,aAAa,CAAG5L,EAAO4L,aAAa,CAACC,MAAM,CACnK,CAAChM,EAAMmJ,SAAS,CAACtH,MAAM,CAAG,EAG1B2D,EAAOT,EAAM1C,QAAQ,CAACrC,CAAK,CAAC6L,EAAM,CAAE,CAAA,GAAOpG,EAAOX,EAAMzC,QAAQ,CAACrC,EAAM2B,CAAC,CAAE,CAAA,GAO9E,OALI6E,IACAhB,EAAOT,EAAMkH,GAAG,CAAGzG,EACnBC,EAAOX,EAAMmH,GAAG,CAAGxG,GAGhB,CACH/D,EAAGiE,KAAKoD,KAAK,CAACvD,GACd7D,EAAGgE,KAAKoD,KAAK,CAHjBtD,GAAQqG,EAIR,CACJ,CAoB6B,IAAMI,EAdb,CAClBb,UAAAA,EACA3B,QAAAA,EACAY,OAAAA,EACA9B,OAAAA,EACA4C,YAAAA,EACAf,SAAAA,EACA8B,MAzjBU5D,EA0jBV6D,MAvGU,CACVzK,EAAG6J,EAAO7J,CAAC,CASX0K,MAAOhE,EAAoBmD,EAAO9J,CAAC,CAAE,CACjCoC,WAAY,iBAEZwI,uBAAwB,AAACtM,GAAW,CAACA,EAAMuM,SAAS,AACxD,GASAC,IAAKnE,EAAoBmD,EAAOG,EAAE,CAAE,CAChC7H,WAAY,eAEZwI,uBAAwB,AAACtM,GAAW,CAACA,EAAMuM,SAAS,AACxD,EACJ,EA4EIhE,KAAAA,EACAmC,KAAAA,EACAO,UAAAA,EACAO,OAAAA,CACJ,EAiBM,CAAEzP,UAAW0Q,CAAyB,CAAE7P,mBAAoB8P,CAAkC,CAAE,CAAG5Q,EAEnG,CAAEiE,aAAc4M,CAA4B,CAAE,CAAG5E,EAIjD,CAAElM,SAAU+Q,CAAwB,CAAEC,MAAAA,CAAK,CAAEzE,SAAU0E,CAAwB,CAAEzO,MAAO0O,CAAqB,CAAE,CAAInR,IAiHzH,SAASoR,GAAShN,CAAK,EACnB,IAAMlD,EAAQkD,EAAMG,MAAM,EAAIH,EAAMG,MAAM,CAACrD,KAAK,CAAEwE,EAAexE,GAASA,EAAMwE,YAAY,CACxFxE,GACAA,EAAM0B,WAAW,EACjB,CAAE8C,CAAAA,GACGA,CAAAA,EAAakB,UAAU,EACpBlB,EAAaoB,sBAAsB,EACnCpB,EAAa2L,gBAAgB,GAAKjN,EAAMuC,EAAE,AAAD,CAAC,GAClDzF,EAAMgG,eAAe,EAE7B,CA8BA,SAASoK,KACL,IAAMlN,EAAQ,IAAI,CAClB6H,WAAW,KACH7H,EAAMG,MAAM,EACZ6M,GAAShN,EAEjB,EAAG,GACP,CAWA,SAASmN,KACL,IAAMnN,EAAQ,IAAI,CAClB6H,WAAW,IAAMuF,AAzCrB,CAAA,SAAmBpN,CAAK,EACpB,IAAMG,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,GAAUA,EAAOrD,KAAK,CAAEwE,EAAexE,GAASA,EAAMwE,YAAY,CAAE+L,EAAOvQ,GAASA,EAAMuQ,IAAI,EAAIvQ,EAAMuQ,IAAI,IAC7IvQ,GACEwE,GACEA,EAAakB,UAAU,EACvBlB,EAAaoB,sBAAsB,EACtC5F,EAAM6K,mBAAmB,GAC1BxH,EAAOhE,OAAO,CAACmE,QAAQ,EACtB+M,IAGGvQ,EAAM0B,WAAW,EACjB1B,EAAMgG,eAAe,GAEzB9C,EAAMyG,eAAe,GAE7B,CAAA,EAyB+BzG,GAAQ,GACvC,CAKA,SAASsN,KACL,IAAMxQ,EAAQ,IAAI,CAACqD,MAAM,CAACrD,KAAK,CAAE0B,EAAc1B,EAAM0B,WAAW,CAC5DA,GAAeA,EAAYwB,KAAK,GAAK,IAAI,CAACuC,EAAE,EAC5CzF,EAAMgG,eAAe,EAE7B,CA4EA,SAASyK,GAAmBhM,CAAM,CAAEuE,CAAM,CAAElB,CAAW,EACnD,IAAoBzE,EAASH,AAAf,IAAI,CAAiBG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAAEoF,EAAUpF,EAAMoF,OAAO,CAAE/F,EAAU4Q,EAAsB5M,EAAOhE,OAAO,CAACmE,QAAQ,CAAEN,AAArI,IAAI,CAAuI7D,OAAO,CAACmE,QAAQ,EAAGkN,EAAS,CAAC,EAAGC,EAAclM,EAAOf,MAAM,CAACR,AAAvM,IAAI,CAAyMuC,EAAE,CAAC,CAAEmL,EAAmB1S,AAAoC,IAApCA,OAAOwB,IAAI,CAACoI,GAAanI,MAAM,CAY5QkR,EAAe,CAAC3L,EAAK4L,KACvB,IAAMC,EAAYD,EAAIE,WAAW,GAAIC,EAAO5N,EAAOrD,KAAK,CAACiR,IAAI,CAAEC,EAAmB7N,GAAAA,CAAM,CAAC,CAAC,EAAEyN,EAAI,IAAI,CAAC,CAAC,CAACK,UAAU,CAAUC,EAAY/R,CAAO,CAAC,CAAC,aAAa,EAAE0R,EAAU,CAAC,CAAC,EACvKG,EAAkBG,EAAMJ,EAAKK,KAAK,CAACjS,CAAO,CAAC,CAAC,OAAO,EAAE0R,EAAU,CAAC,CAAC,GACjE,CAACQ,IAAUnH,EAAM6G,EAAKK,KAAK,CAACjS,CAAO,CAAC,CAAC,OAAO,EAAE0R,EAAU,CAAC,CAAC,GAC1DQ,IACA5M,EAAMO,EAIV,OAHIkM,GACAzM,CAAAA,EAAMkE,KAAKoD,KAAK,CAACtH,EAAMyM,GAAaA,CAAQ,EAEzCrB,EAAMpL,EAAK0M,EAAKjH,EAC3B,EAcMoH,EAAkB,CAACxI,EAAQ8H,EAAK9S,KAClC,GAAIoH,EAAS,CACT,IAAM2L,EAAYD,EAAIE,WAAW,GAAII,EAAY/R,CAAO,CAAC,CAAC,aAAa,EAAE0R,EAAU,CAAC,CAAC,EAAI,EAAGU,EAAYrM,EAAQsM,cAAc,CAAC,CAC3H9M,EAAG,EACHC,EAAG,CACP,GAAI8M,EAAYvM,EAAQsM,cAAc,CAAC,CACnC9M,EAAG5E,EAAM4R,OAAO,CAAC9M,KAAK,CACtBD,EAAG7E,EAAM4R,OAAO,CAAC7M,MAAM,AAC3B,GACIsM,EAAMhS,CAAO,CAAC,CAAC,OAAO,EAAE0R,EAAU,CAAC,CAAC,EACpCU,GAAW,CAACzT,EAAI,EAChB,CAACuT,IAAUnH,EAAM/K,CAAO,CAAC,CAAC,OAAO,EAAE0R,EAAU,CAAC,CAAC,EAC/CY,GAAW,CAAC3T,EAAI,EAChBuT,IAAU5M,EAAMqE,CAAM,CAAChL,EAAI,CAC/B,GAAIoH,AAAoC,iBAApCA,EAAQyM,UAAU,CAACxS,OAAO,CAACyS,IAAI,CAC/B,OAAOnN,EAEX,GAAI3G,AAAQ,QAARA,EAAe,CAEX+T,CAAAA,MAAMV,IAAQA,EAAMjM,EAAQyM,UAAU,CAACG,WAAW,AAAD,GACjDX,CAAAA,EAAMjM,EAAQyM,UAAU,CAACG,WAAW,AAAD,EAEnCD,CAAAA,MAAM3H,IAAQA,EAAM,GAAKhF,EAAQyM,UAAU,CAACG,WAAW,AAAD,GACtD5H,CAAAA,EAAM,GAAKhF,EAAQyM,UAAU,CAACG,WAAW,AAAD,EAG5C,IAAMC,EAAO7H,EACbA,EAAMiH,EACNA,EAAMY,CACV,CACA,GAAI,CAAC7M,EAAQyM,UAAU,CAACK,cAAc,CAAE,CAEpC,IAAMC,EAAY/M,EAAQsM,cAAc,CAAC,CACrC9M,EAAGoE,EAAO/I,MAAM,CAAGD,EAAMoS,QAAQ,CACjCvN,EAAG7E,EAAMqS,UAAU,CAAGrJ,EAAO9I,MAAM,CAAGF,EAAMsS,OAAO,AACvD,GACIH,GACAxN,CAAAA,EAAMwN,CAAS,CAACnU,EAAI,AAAD,CAE3B,CAIA,OAHIoT,GACAzM,CAAAA,EAAMkE,KAAKoD,KAAK,CAACtH,EAAMyM,GAAaA,CAAQ,EAEzCrB,EAAMpL,EAAK0M,EAAKjH,EAC3B,CACJ,EAGA,IAAK,IAAMpM,KAAOE,OAAOwB,IAAI,CAACoI,GAAc,CACxC,IAAM5C,EAAM4C,CAAW,CAAC9J,EAAI,CAAEuU,EAAS5B,EAAYzN,KAAK,CAAClF,EAAI,CAAEmH,EAAO9B,CAAM,CAAC6B,EAAIC,IAAI,CAAG,OAAO,CAAEqN,EAASpN,EACtGoM,EAAgBxI,EAAQ9D,EAAIC,IAAI,CAAEnH,GAClC6S,EAAa1L,EAAKsN,OAAO,CAAC,AAACtN,CAAAA,EAAKK,KAAK,CAAGwD,EAAO/I,MAAM,CAAG+I,EAAO9I,MAAM,AAAD,EAChEyQ,CAAW,CAAC3S,EAAM,SAAS,EAAGkH,EAAIC,IAAI,EAG1C6K,EAAyBwC,IACzB,CAAE5B,CAAAA,GACE1L,EAAI6H,YAAY,EAChB,CAAC7H,EAAI6H,YAAY,CAACyF,EA9FhB,IAAI,CA8FyB,GACnC,AAAkB,KAAA,IAAXD,GACP7B,CAAAA,CAAM,CAAC1S,EAAI,CAAGwU,CAAK,CAE3B,CACA,OAAO9B,CACX,CAQA,SAASgC,KACL,IAAMxP,EAAQ,IAAI,CAAEG,EAASH,EAAMG,MAAM,CAAErD,EAAQqD,EAAOrD,KAAK,CAAE,CAAE0J,SAAAA,CAAQ,CAAEiJ,SAAAA,CAAQ,CAAE,CAAG3S,EAAOX,EAAU4Q,EAAsB5M,EAAOhE,OAAO,CAACmE,QAAQ,CAAEN,EAAM7D,OAAO,CAACmE,QAAQ,EAAGwB,EAAgB3B,EAAO2B,aAAa,EAAI,CAAC,EACxNtD,EAAc1B,EAAM0B,WAAW,CAGnC,IAAK,IAAM1D,KAAOE,OAAOwB,IAAI,CAACsF,GAAgB,CAC1C,IAUI4N,EAAKC,EAAQC,EAVX5N,EAAMF,CAAa,CAAChH,EAAI,CAAE+U,EAAgB9C,EAAsB5P,EAAiCE,UAAU,CAAE2E,EAAI6N,aAAa,CAAE1T,EAAQkB,UAAU,EAAGyS,EAAc,CACrK,MAASD,EAAcvS,SAAS,CAChC,eAAgBuS,EAAcpS,SAAS,CACvCyB,KAAM2Q,EAActS,KAAK,CACzByB,OAAQ6Q,EAAcrS,SAAS,AACnC,EAAGuS,EAAgBF,EAAcE,aAAa,EAAI/N,EAAIsH,eAAe,CAAEL,EAAmBjH,EAAIiH,gBAAgB,CAG9G+G,EAAWhO,CAAAA,EAAIsK,sBAAsB,EACjCtK,EAAIsK,sBAAsB,CAACtM,GAE/B,GAAIgC,EAAIkE,MAAM,EACV8J,GACAhO,EAAI2E,UAAU,EACdoJ,GACC5T,CAAAA,CAAO,CAAC,YAAc6F,EAAIC,IAAI,CAAC6L,WAAW,GAAG,EAC1C3R,CAAO,CAAC6F,EAAI8B,UAAU,CAAC,AAAD,GAC1B3H,AAA4B,CAAA,IAA5BA,CAAO,CAAC6F,EAAI8B,UAAU,CAAC,CAAY,CAE9BtF,EAUDA,EAAYwB,KAAK,CAAGA,EAAMuC,EAAE,CAT5B/D,EAAc1B,EAAM0B,WAAW,CAAG,CAC9B6C,MAAOoO,EACFQ,CAAC,CAAC,qBACF7O,GAAG,CAACjB,EAAO+P,WAAW,EAAI/P,EAAOkB,KAAK,EAC3CrB,MAAOA,EAAMuC,EAAE,AACnB,EAOJmN,EAAMzG,EAAiBjJ,GACvB8P,EAAYnV,CAAC,CAAGiV,EAAOG,EAAc/P,GAErC,IAAMmQ,EAAUnQ,EAAMG,MAAM,CAAC4E,KAAK,CAACkJ,UAAU,CAAG,IAAO,EACvD,GAAI,CAAC2B,GAAQF,EAAIhO,CAAC,CAAGyO,GAAWT,EAAI/N,CAAC,CAAG,EACpC,MAGJmO,CAAAA,EAAYlS,MAAM,CAAGiS,EAAcjS,MAAM,EACpC,CAAA,AAAc,MAAboE,EAAIC,IAAI,EAAc,CAAC,CAACuE,EACtB,YAAc,WAAU,EAEhCmJ,CAAAA,EAASnR,CAAW,CAACwD,EAAI8B,UAAU,CAAC,AAAD,GAE/B6L,CAAAA,EAASnR,CAAW,CAACwD,EAAI8B,UAAU,CAAC,CAAG2L,EAClCG,IAAI,GACJxO,GAAG,CAAC5C,EAAY6C,KAAK,CAAA,EAG9ByO,EAAYM,UAAU,CAAG5J,EACrBrG,EAAO2E,KAAK,CAACmH,GAAG,CAAGyD,EAAI/N,CAAC,CACxB+N,EAAIhO,CAAC,CACToO,EAAYO,UAAU,CAAG7J,EACrBrG,EAAO4E,KAAK,CAACkH,GAAG,CAAGyD,EAAIhO,CAAC,CACxBgO,EAAI/N,CAAC,CACL6E,GACAsJ,CAAAA,EAAYQ,QAAQ,CAAG,GAAE,EAE7BX,EAAO5Q,IAAI,CAAC+Q,GAEZrD,EAA0BkD,EAAOY,OAAO,CAAE,CAAC,aAAc,YAAY,CAAE,AAAC1T,KACpE2T,AA/NhB,SAAiC3T,CAAC,CAAEmD,CAAK,CAAEgG,CAAU,EACjD,IAAMlJ,EAAQkD,EAAMG,MAAM,CAACrD,KAAK,EAE5BA,EAAMuH,mBAAmB,CAACxH,KAI9BC,EAAMkI,WAAW,CAAG,CAAA,EAEpB2H,EAA6B9P,EAAGmD,GAChClD,EAAMwE,YAAY,CAAC0E,UAAU,CACzBnJ,EAAEmJ,UAAU,CAAGA,EACnBhG,EAAMiD,cAAc,CAAC,YAAapG,GAElCA,EAAE4T,eAAe,GACjB5T,EAAEkG,cAAc,GACpB,EA+MwC2J,EAAmC7P,EAAGC,GAAQkD,EAAOlF,EACjF,EAAG,CACCyM,QAAS,CAAA,CACb,GACAqF,EAAyBpO,EAAY6C,KAAK,CAACkP,OAAO,CAAE,YAAa,KAC7DzT,EAAMwE,YAAY,CAAGxE,EAAMwE,YAAY,EAAI,CAAC,EAC5CxE,EAAMwE,YAAY,CAAC2L,gBAAgB,CAAGjN,EAAMuC,EAAE,AAClD,GACAkK,EAA0BjO,EAAY6C,KAAK,CAACkP,OAAO,CAAE,CAAC,WAAY,WAAW,CAAE,MAC3EG,AA9PhB,SAAgC1Q,CAAK,EACjC,IAAMlD,EAAQkD,EAAMG,MAAM,CAACrD,KAAK,AAC5BA,CAAAA,EAAMwE,YAAY,EAClBtB,EAAMuC,EAAE,GAAKzF,EAAMwE,YAAY,CAAC2L,gBAAgB,EAChD,OAAOnQ,EAAMwE,YAAY,CAAC2L,gBAAgB,CAEzCnQ,EAAMmH,UAAU,EACjB+I,GAAShN,EAEjB,EAqPuCA,EAC3B,EACJ,CACJ,CACJ,CAaA,SAAS2Q,GAAkBnQ,CAAM,EAC7B,IAAM1D,EAAQ,IAAI,CAACA,KAAK,CACpB8T,EAAOvC,IAAUwC,EAAO,CAACxC,IAAUyC,EAAOzC,IAAU0C,EAAO,CAAC1C,IAAU2C,EAE1E,IAAK,IAAMhR,KAASQ,EAAQ,CACxB,IAAM0I,EAAQlJ,EAAMoJ,OAAO,EAAIpJ,EAAMoJ,OAAO,CAACC,OAAO,IAAMrJ,EAAMmJ,SAAS,CACzE,GAAID,EAAM,CAEN,IADI+H,EACEtF,EAAK3L,EAAM2L,EAAE,CACfmB,EAAyBnB,IACzBsF,CAAAA,EAASjR,EAAMG,MAAM,CAAC4E,KAAK,CAACuC,SAAS,CAACqE,EAAI,CAAA,EAAO,CAAA,EAAO,CAAA,EAAO,CAAA,EAAI,EAIvE,IAAMuF,EAAW,CAAEhI,CAAAA,EAAKtH,KAAK,EAAIsH,EAAKrH,MAAM,EAAIqH,EAAKxH,CAAC,EAAIwH,EAAKvH,CAAC,AAADA,EAC/DqP,EAAU,CAAA,EACVJ,EAAOjL,KAAKwI,GAAG,CAACnO,EAAMmC,KAAK,EAAI,EAAG8O,GAAU,EAAGC,EAAW7C,IAAWnF,EAAKxH,CAAC,EAAI,EAAGkP,GAClFC,EAAOlL,KAAKuB,GAAG,CAAClH,EAAMmC,KAAK,EAAI,EAAG8O,GAAU,EAAG,AAAC/H,CAAAA,EAAKxH,CAAC,EAAI,CAAA,EAAMwH,CAAAA,EAAKtH,KAAK,EAAI,CAAA,EAAIiP,GAClFC,EAAOnL,KAAKwI,GAAG,CAACnO,EAAMoC,KAAK,EAAI,EAAG8O,EAAW7C,IAAWnF,EAAKvH,CAAC,EAAI,EAAGmP,GACrEC,EAAOpL,KAAKuB,GAAG,CAAC,AAACgC,CAAAA,EAAKvH,CAAC,EAAI,CAAA,EAAMuH,CAAAA,EAAKrH,MAAM,EAAI,CAAA,EAAIkP,EACxD,CACJ,CACA,OAAOC,EAAUlU,EAAM2S,QAAQ,CAAC3I,IAAI,CAAC8J,EAAME,EAAMD,EAAOD,EAAMG,EAAOD,GAAQhU,EAAM2S,QAAQ,CAACQ,CAAC,EACjG,CAkLA,IAAMkB,GAAKvV,IACXwV,AA7KwB,CAAA,CACpBpJ,QA7bJ,SAAiCC,CAAU,CAAEoJ,CAAW,EACpDtJ,EAA+BC,OAAO,CAACC,GACvC,IAAMqJ,EAAcD,EAAY/V,SAAS,CACzC,GAAI,CAACgW,EAAYxP,aAAa,CAAE,CAC5B,IAAMyP,EAAaF,EAAY/V,SAAS,CAACuF,UAAU,CAAE2Q,EAAcH,EAAYpV,KAAK,CAAEwV,EAAaF,EAAWjW,SAAS,CAsBvH,IAAK,IAAMoW,KArBXD,EAAWtL,aAAa,CAAGoH,GAC3BkE,EAAWhL,eAAe,CAAG+I,GAC7B5C,EAAyB2E,EAAY,WAAYrE,IACjDN,EAAyB2E,EAAY,YAAapE,IAClDP,EAAyB2E,EAAY,SAAUjE,IAC/CgE,EAAYxP,aAAa,CAAGoK,EAA8B3D,IAAI,CAC9D+I,EAAYpQ,WAAW,CAAGyP,GAEM,CAC5B,YACA,UACA,SACA,SACA,cACA,WACA,QACA,QACA,OACA,YACA,SACH,EAEOa,CAAW,CAACE,EAAW,EACvBF,CAAAA,CAAW,CAACE,EAAW,CAACpW,SAAS,CAACwG,aAAa,CAC3CoK,CAA6B,CAACwF,EAAW,AAAD,EAoBpD,IAAK,IAAMA,IAhBwB,CAC/B,YACA,QACA,YACA,MACA,UACA,SACA,MACA,SACA,MACA,WACA,UACA,SACA,WACA,YACH,CAEOF,CAAW,CAACE,EAAW,EACvBF,CAAAA,CAAW,CAACE,EAAW,CAACpW,SAAS,CAACwG,aAAa,CAAG,IAAG,CAGjE,CACJ,CAuYA,CAAA,EA2KgCkG,OAAO,CAACmJ,GAAEQ,KAAK,CAAER,GAAES,MAAM,EAC5B,IAAMlW,GAAyBE,IAGlD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}