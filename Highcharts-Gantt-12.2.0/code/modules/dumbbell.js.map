{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/dumbbell\n * @requires highcharts\n *\n * (c) 2009-2025 <PERSON>, <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/dumbbell\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"SVGRenderer\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/dumbbell\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__540__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ dumbbell_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/AreaRange/AreaRangePoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { area: { prototype: { pointClass: AreaPoint, pointClass: { prototype: areaProto } } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, isNumber } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass AreaRangePoint extends AreaPoint {\n    /**\n     * Range series only. The high or maximum value for each data point.\n     * @name Highcharts.Point#high\n     * @type {number|undefined}\n     */\n    /**\n     * Range series only. The low or minimum value for each data point.\n     * @name Highcharts.Point#low\n     * @type {number|undefined}\n     */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    setState() {\n        const prevState = this.state, series = this.series, isPolar = series.chart.polar;\n        if (!defined(this.plotHigh)) {\n            // Boost doesn't calculate plotHigh\n            this.plotHigh = series.yAxis.toPixels(this.high, true);\n        }\n        if (!defined(this.plotLow)) {\n            // Boost doesn't calculate plotLow\n            this.plotLow = this.plotY = series.yAxis.toPixels(this.low, true);\n        }\n        series.lowerStateMarkerGraphic = series.stateMarkerGraphic;\n        series.stateMarkerGraphic = series.upperStateMarkerGraphic;\n        // Change state also for the top marker\n        this.graphic = this.graphics && this.graphics[1];\n        this.plotY = this.plotHigh;\n        if (isPolar && isNumber(this.plotHighX)) {\n            this.plotX = this.plotHighX;\n        }\n        // Top state:\n        areaProto.setState.apply(this, arguments);\n        this.state = prevState;\n        // Now restore defaults\n        this.plotY = this.plotLow;\n        this.graphic = this.graphics && this.graphics[0];\n        if (isPolar && isNumber(this.plotLowX)) {\n            this.plotX = this.plotLowX;\n        }\n        series.upperStateMarkerGraphic = series.stateMarkerGraphic;\n        series.stateMarkerGraphic = series.lowerStateMarkerGraphic;\n        // Lower marker is stored at stateMarkerGraphic\n        // to avoid reference duplication (#7021)\n        series.lowerStateMarkerGraphic = void 0;\n        const originalSettings = series.modifyMarkerSettings();\n        // Bottom state\n        areaProto.setState.apply(this, arguments);\n        // Restore previous state\n        series.restoreMarkerSettings(originalSettings);\n    }\n    haloPath() {\n        const isPolar = this.series.chart.polar;\n        let path = [];\n        // Bottom halo\n        this.plotY = this.plotLow;\n        if (isPolar && isNumber(this.plotLowX)) {\n            this.plotX = this.plotLowX;\n        }\n        if (this.isInside) {\n            path = areaProto.haloPath.apply(this, arguments);\n        }\n        // Top halo\n        this.plotY = this.plotHigh;\n        if (isPolar && isNumber(this.plotHighX)) {\n            this.plotX = this.plotHighX;\n        }\n        if (this.isTopInside) {\n            path = path.concat(areaProto.haloPath.apply(this, arguments));\n        }\n        return path;\n    }\n    isValid() {\n        return isNumber(this.low) && isNumber(this.high);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AreaRange_AreaRangePoint = (AreaRangePoint);\n\n;// ./code/es-modules/Series/Dumbbell/DumbbellPoint.js\n/* *\n *\n *  (c) 2010-2025 Sebastian Bochan, Rafal Sebestjanski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass DumbbellPoint extends AreaRange_AreaRangePoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Set the point's state extended by have influence on the connector\n     * (between low and high value).\n     *\n     * @private\n     */\n    setState() {\n        const point = this, series = point.series, chart = series.chart, seriesLowColor = series.options.lowColor, seriesMarker = series.options.marker, seriesLowMarker = series.options.lowMarker, pointOptions = point.options, pointLowColor = pointOptions.lowColor, zoneColor = point.zone && point.zone.color, lowerGraphicColor = pick(pointLowColor, seriesLowMarker?.fillColor, seriesLowColor, pointOptions.color, zoneColor, point.color, series.color);\n        let verb = 'attr', upperGraphicColor, origProps;\n        this.pointSetState.apply(point, arguments);\n        if (!point.state) {\n            verb = 'animate';\n            const [lowerGraphic, upperGraphic] = point.graphics || [];\n            if (lowerGraphic && !chart.styledMode) {\n                lowerGraphic.attr({\n                    fill: lowerGraphicColor\n                });\n                if (upperGraphic) {\n                    origProps = {\n                        y: point.y,\n                        zone: point.zone\n                    };\n                    point.y = point.high;\n                    point.zone = point.zone ? point.getZone() : void 0;\n                    upperGraphicColor = pick(point.marker ? point.marker.fillColor : void 0, seriesMarker ? seriesMarker.fillColor : void 0, pointOptions.color, point.zone ? point.zone.color : void 0, point.color);\n                    upperGraphic.attr({\n                        fill: upperGraphicColor\n                    });\n                    extend(point, origProps);\n                }\n            }\n        }\n        point.connector?.[verb](series.getConnectorAttribs(point));\n    }\n    destroy() {\n        const point = this;\n        // #15560\n        if (!point.graphic) {\n            point.graphic = point.connector;\n            point.connector = void 0;\n        }\n        return super.destroy();\n    }\n}\nextend(DumbbellPoint.prototype, {\n    pointSetState: AreaRange_AreaRangePoint.prototype.setState\n});\n/* *\n *\n *  Default export\n *\n * */\n/* harmony default export */ const Dumbbell_DumbbellPoint = (DumbbellPoint);\n\n;// ./code/es-modules/Series/Dumbbell/DumbbellSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Sebastian Bochan, Rafal Sebestjanski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The dumbbell series is a cartesian series with higher and lower values\n * for each point along an X axis, connected with a line between the\n * values.\n *\n * Requires `highcharts-more.js` and `modules/dumbbell.js`.\n *\n * @sample {highcharts} highcharts/demo/dumbbell/\n *         Dumbbell chart\n * @sample {highcharts} highcharts/series-dumbbell/styled-mode-dumbbell/\n *         Styled mode\n *\n * @extends      plotOptions.arearange\n * @product      highcharts highstock\n * @excluding    boostThreshold, boostBlendingfillColor, fillOpacity,\n *               legendSymbolColor, lineWidth, stack, stacking, stickyTracking,\n *               trackByArea\n * @since 8.0.0\n * @optionparent plotOptions.dumbbell\n */\nconst DumbbellSeriesDefaults = {\n    /** @ignore-option */\n    trackByArea: false,\n    /** @ignore-option */\n    fillColor: 'none',\n    /** @ignore-option */\n    lineWidth: 0,\n    pointRange: 1,\n    /**\n     * Pixel width of the line that connects the dumbbell point's\n     * values.\n     *\n     * @since 8.0.0\n     * @product   highcharts highstock\n     */\n    connectorWidth: 1,\n    /** @ignore-option */\n    stickyTracking: false,\n    groupPadding: 0.2,\n    crisp: false,\n    pointPadding: 0.1,\n    legendSymbol: 'rectangle',\n    /**\n     * Color of the start markers in a dumbbell graph. This option takes\n     * priority over the series color. To avoid this, set `lowColor` to\n     * `undefined`.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since 8.0.0\n     * @product   highcharts highstock\n     */\n    lowColor: \"#333333\" /* Palette.neutralColor80 */,\n    /**\n     * Color of the line that connects the dumbbell point's values.\n     * By default it is the series' color.\n     *\n     * @type      {string}\n     * @product   highcharts highstock\n     * @since 8.0.0\n     * @apioption plotOptions.dumbbell.connectorColor\n     */\n    /**\n     *\n     * @apioption plotOptions.series.lowMarker\n     */\n    states: {\n        hover: {\n            /** @ignore-option */\n            lineWidthPlus: 0,\n            /**\n             * The additional connector line width for a hovered point.\n             *\n             * @since 8.0.0\n             * @product   highcharts highstock\n             */\n            connectorWidthPlus: 1,\n            /** @ignore-option */\n            halo: false\n        }\n    }\n};\n/**\n * The `dumbbell` series. If the [type](#series.dumbbell.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dumbbell\n * @excluding boostThreshold, boostBlending\n * @product   highcharts highstock\n * @requires  highcharts-more\n * @requires  modules/dumbbell\n * @apioption series.dumbbell\n */\n/**\n * An array of data points for the series. For the `dumbbell` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,low,high`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2\\. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 4, 2],\n *        [1, 2, 1],\n *        [2, 9, 10]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.dumbbell.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        low: 0,\n *        high: 4,\n *        name: \"Point2\",\n *        color: \"#00FF00\",\n *        lowColor: \"#00FFFF\",\n *        connectorWidth: 3,\n *        connectorColor: \"#FF00FF\"\n *    }, {\n *        x: 1,\n *        low: 5,\n *        high: 3,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.arearange.data\n * @product   highcharts highstock\n * @apioption series.dumbbell.data\n */\n/**\n * Color of the start markers in a dumbbell graph. This option takes\n * priority over the series color. To avoid this, set `lowColor` to\n * `undefined`.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @since     8.0.0\n * @product   highcharts highstock\n * @apioption  series.dumbbell.lowColor\n */\n/**\n * Options for the lower markers of the dumbbell-like series. When `lowMarker`\n * is not defined, options inherit form the marker.\n *\n * @see [marker](#series.arearange.marker)\n *\n * @declare   Highcharts.PointMarkerOptionsObject\n * @extends   plotOptions.series.marker\n * @default   undefined\n * @product   highcharts highstock\n * @apioption plotOptions.dumbbell.lowMarker\n */\n/**\n *\n * @sample {highcharts} highcharts/demo/dumbbell-markers\n *         Dumbbell chart with lowMarker option\n *\n * @declare   Highcharts.PointMarkerOptionsObject\n * @extends   plotOptions.series.marker.symbol\n * @product   highcharts highstock\n * @apioption plotOptions.dumbbell.lowMarker.symbol\n */\n/**\n * Color of the line that connects the dumbbell point's values.\n * By default it is the series' color.\n *\n * @type        {string}\n * @since       8.0.0\n * @product     highcharts highstock\n * @apioption   series.dumbbell.data.connectorColor\n */\n/**\n * Pixel width of the line that connects the dumbbell point's values.\n *\n * @type        {number}\n * @since       8.0.0\n * @default     1\n * @product     highcharts highstock\n * @apioption   series.dumbbell.data.connectorWidth\n */\n/**\n * Color of the start markers in a dumbbell graph. This option takes\n * priority over the series color. To avoid this, set `lowColor` to\n * `undefined`.\n *\n * @type        {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @since       8.0.0\n * @default     ${palette.neutralColor80}\n * @product     highcharts highstock\n * @apioption   series.dumbbell.data.lowColor\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Dumbbell_DumbbellSeriesDefaults = (DumbbellSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es-modules/Series/Dumbbell/DumbbellSeries.js\n/* *\n *\n *  (c) 2010-2025 Sebastian Bochan, Rafal Sebestjanski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { arearange: AreaRangeSeries, column: ColumnSeries, columnrange: ColumnRangeSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\n\nconst { extend: DumbbellSeries_extend, merge, pick: DumbbellSeries_pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The dumbbell series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dumbbell\n *\n * @augments Highcharts.Series\n */\nclass DumbbellSeries extends AreaRangeSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get connector line path and styles that connects dumbbell point's low and\n     * high values.\n     * @private\n     *\n     * @param {Highcharts.Point} point The point to inspect.\n     *\n     * @return {Highcharts.SVGAttributes} attribs The path and styles.\n     */\n    getConnectorAttribs(point) {\n        const series = this, chart = series.chart, pointOptions = point.options, seriesOptions = series.options, xAxis = series.xAxis, yAxis = series.yAxis, connectorWidthPlus = DumbbellSeries_pick(seriesOptions.states &&\n            seriesOptions.states.hover &&\n            seriesOptions.states.hover.connectorWidthPlus, 1), dashStyle = DumbbellSeries_pick(pointOptions.dashStyle, seriesOptions.dashStyle), pxThreshold = yAxis.toPixels(seriesOptions.threshold || 0, true), pointHeight = chart.inverted ?\n            yAxis.len - pxThreshold : pxThreshold;\n        let connectorWidth = DumbbellSeries_pick(pointOptions.connectorWidth, seriesOptions.connectorWidth), connectorColor = DumbbellSeries_pick(pointOptions.connectorColor, seriesOptions.connectorColor, pointOptions.color, point.zone ? point.zone.color : void 0, point.color), pointTop = DumbbellSeries_pick(point.plotLow, point.plotY), pointBottom = DumbbellSeries_pick(point.plotHigh, pointHeight), origProps;\n        if (typeof pointTop !== 'number') {\n            return {};\n        }\n        if (point.state) {\n            connectorWidth = connectorWidth + connectorWidthPlus;\n        }\n        if (pointTop < 0) {\n            pointTop = 0;\n        }\n        else if (pointTop >= yAxis.len) {\n            pointTop = yAxis.len;\n        }\n        if (pointBottom < 0) {\n            pointBottom = 0;\n        }\n        else if (pointBottom >= yAxis.len) {\n            pointBottom = yAxis.len;\n        }\n        if (point.plotX < 0 || point.plotX > xAxis.len) {\n            connectorWidth = 0;\n        }\n        // Connector should reflect upper marker's zone color\n        if (point.graphics && point.graphics[1]) {\n            origProps = {\n                y: point.y,\n                zone: point.zone\n            };\n            point.y = point.high;\n            point.zone = point.zone ? point.getZone() : void 0;\n            connectorColor = DumbbellSeries_pick(pointOptions.connectorColor, seriesOptions.connectorColor, pointOptions.color, point.zone ? point.zone.color : void 0, point.color);\n            DumbbellSeries_extend(point, origProps);\n        }\n        const attribs = {\n            d: highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default().prototype.crispLine([[\n                    'M',\n                    point.plotX,\n                    pointTop\n                ], [\n                    'L',\n                    point.plotX,\n                    pointBottom\n                ]], connectorWidth)\n        };\n        if (!chart.styledMode) {\n            attribs.stroke = connectorColor;\n            attribs['stroke-width'] = connectorWidth;\n            if (dashStyle) {\n                attribs.dashstyle = dashStyle;\n            }\n        }\n        return attribs;\n    }\n    /**\n     * Draw connector line that connects dumbbell point's low and high values.\n     * @private\n     * @param {Highcharts.Point} point\n     *        The point to inspect.\n     */\n    drawConnector(point) {\n        const series = this, animationLimit = DumbbellSeries_pick(series.options.animationLimit, 250), verb = point.connector && series.chart.pointCount < animationLimit ?\n            'animate' : 'attr';\n        if (!point.connector) {\n            point.connector = series.chart.renderer.path()\n                .addClass('highcharts-lollipop-stem')\n                .attr({\n                zIndex: -1\n            })\n                .add(series.group);\n        }\n        point.connector[verb](this.getConnectorAttribs(point));\n    }\n    /**\n     * Return the width and x offset of the dumbbell adjusted for grouping,\n     * groupPadding, pointPadding, pointWidth etc.\n     * @private\n     */\n    getColumnMetrics() {\n        const metrics = ColumnSeries.prototype\n            .getColumnMetrics.apply(this, arguments);\n        metrics.offset += metrics.width / 2;\n        return metrics;\n    }\n    /**\n     * Translate each point to the plot area coordinate system and find\n     * shape positions\n     * @private\n     */\n    translate() {\n        const series = this, inverted = series.chart.inverted;\n        // Calculate shapeargs\n        this.setShapeArgs.apply(series);\n        // Calculate point low / high values\n        this.translatePoint.apply(series, arguments);\n        // Correct x position\n        for (const point of series.points) {\n            const { pointWidth, shapeArgs = {}, tooltipPos } = point;\n            point.plotX = shapeArgs.x || 0;\n            shapeArgs.x = point.plotX - pointWidth / 2;\n            if (tooltipPos) {\n                if (inverted) {\n                    tooltipPos[1] = series.xAxis.len - point.plotX;\n                }\n                else {\n                    tooltipPos[0] = point.plotX;\n                }\n            }\n        }\n        series.columnMetrics.offset -= series.columnMetrics.width / 2;\n    }\n    /**\n     * Extend the arearange series' drawPoints method by applying a connector\n     * and coloring markers.\n     * @private\n     */\n    drawPoints() {\n        const series = this, chart = series.chart, pointLength = series.points.length, seriesLowColor = series.lowColor = series.options.lowColor, seriesLowMarker = series.options.lowMarker;\n        let i = 0, lowerGraphicColor, point, zoneColor;\n        this.seriesDrawPoints.apply(series, arguments);\n        // Draw connectors and color upper markers\n        while (i < pointLength) {\n            point = series.points[i];\n            const [lowerGraphic, upperGraphic] = point.graphics || [];\n            series.drawConnector(point);\n            if (upperGraphic) {\n                upperGraphic.element.point = point;\n                upperGraphic.addClass('highcharts-lollipop-high');\n            }\n            if (point.connector) {\n                point.connector.element.point = point;\n            }\n            if (lowerGraphic) {\n                zoneColor = point.zone && point.zone.color;\n                lowerGraphicColor = DumbbellSeries_pick(point.options.lowColor, seriesLowMarker?.fillColor, seriesLowColor, point.options.color, zoneColor, point.color, series.color);\n                if (!chart.styledMode) {\n                    lowerGraphic.attr({\n                        fill: lowerGraphicColor\n                    });\n                }\n                lowerGraphic.addClass('highcharts-lollipop-low');\n            }\n            i++;\n        }\n    }\n    /**\n     * Get presentational attributes.\n     *\n     * @private\n     * @function Highcharts.seriesTypes.column#pointAttribs\n     *\n     * @param {Highcharts.Point} point\n     *        The point to inspect.\n     *\n     * @param {string} state\n     *        Current state of point (normal, hover, select).\n     *\n     * @return {Highcharts.SVGAttributes}\n     *         Presentational attributes.\n     */\n    pointAttribs(point, state) {\n        const pointAttribs = super.pointAttribs.apply(this, arguments);\n        if (state === 'hover') {\n            delete pointAttribs.fill;\n        }\n        return pointAttribs;\n    }\n    /**\n     * Set the shape arguments for dummbells.\n     * @private\n     */\n    setShapeArgs() {\n        ColumnSeries.prototype.translate.apply(this);\n        ColumnRangeSeries.prototype.afterColumnTranslate.apply(this);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nDumbbellSeries.defaultOptions = merge(AreaRangeSeries.defaultOptions, Dumbbell_DumbbellSeriesDefaults);\nDumbbellSeries_extend(DumbbellSeries.prototype, {\n    crispCol: ColumnSeries.prototype.crispCol,\n    drawGraph: noop,\n    drawTracker: ColumnSeries.prototype.drawTracker,\n    pointClass: Dumbbell_DumbbellPoint,\n    seriesDrawPoints: AreaRangeSeries.prototype.drawPoints,\n    trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n    translatePoint: AreaRangeSeries.prototype.translate\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('dumbbell', DumbbellSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Dumbbell_DumbbellSeries = ((/* unused pure expression or super */ null && (DumbbellSeries)));\n\n;// ./code/es-modules/masters/modules/dumbbell.js\n\n\n\n\n/* harmony default export */ const dumbbell_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__540__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dumbbell_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "area", "pointClass", "AreaPoint", "areaProto", "seriesTypes", "defined", "isNumber", "AreaRange_AreaRangePoint", "setState", "prevState", "state", "series", "isPolar", "chart", "polar", "plotHigh", "yAxis", "toPixels", "high", "plotLow", "plotY", "low", "lowerStateMarkerGraphic", "stateMarkerGraphic", "upperStateMarkerGraphic", "graphic", "graphics", "plotHighX", "plotX", "apply", "arguments", "plotLowX", "originalSettings", "modifyMarkerSettings", "restoreMarkerSettings", "haloPath", "path", "isInside", "isTopInside", "concat", "<PERSON><PERSON><PERSON><PERSON>", "extend", "pick", "DumbbellPoint", "point", "seriesLowColor", "options", "lowColor", "seriesMarker", "marker", "seriesLowMarker", "lowMark<PERSON>", "pointOptions", "pointLowColor", "zoneColor", "zone", "color", "lowerGraphicColor", "fillColor", "verb", "upperGraphicColor", "origProps", "pointSetState", "lowerGraphic", "upperGraphic", "styledMode", "attr", "fill", "y", "getZone", "connector", "getConnectorAttribs", "destroy", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "noop", "arearange", "AreaRangeSeries", "column", "ColumnSeries", "columnrange", "ColumnRangeSeries", "DumbbellSeries_extend", "merge", "DumbbellSeries_pick", "DumbbellSeries", "seriesOptions", "xAxis", "connectorWidthPlus", "states", "hover", "dashStyle", "px<PERSON><PERSON><PERSON><PERSON>", "threshold", "pointHeight", "inverted", "len", "connectorWidth", "connectorColor", "pointTop", "pointBottom", "attribs", "crispLine", "stroke", "dashstyle", "drawConnector", "animationLimit", "pointCount", "renderer", "addClass", "zIndex", "add", "group", "getColumnMetrics", "metrics", "offset", "width", "translate", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translatePoint", "points", "pointWidth", "shapeArgs", "tooltipPos", "x", "columnMetrics", "drawPoints", "point<PERSON><PERSON><PERSON>", "length", "i", "seriesDrawPoints", "element", "pointAttribs", "afterColumnTranslate", "defaultOptions", "trackByArea", "lineWidth", "pointRange", "stickyTracking", "groupPadding", "crisp", "pointPadding", "legendSymbol", "lineWidthPlus", "halo", "crispCol", "drawGraph", "drawTracker", "trackerGroups", "registerSeriesType"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,WAAc,EAChH,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,WAAc,CAAE,GAC3I,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,WAAc,EAE/IA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,WAAc,CAC1H,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,KAAM,CAAET,UAAW,CAAEU,WAAYC,CAAS,CAAED,WAAY,CAAEV,UAAWY,CAAS,CAAE,CAAE,CAAE,CAAE,CAAG,AAACJ,IAA2IK,WAAW,CAElP,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAE,CAAIT,IA6FIU,EAvFnC,cAA6BL,EAmBzBM,UAAW,CACP,IAAMC,EAAY,IAAI,CAACC,KAAK,CAAEC,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAUD,EAAOE,KAAK,CAACC,KAAK,CAC3ET,EAAQ,IAAI,CAACU,QAAQ,GAEtB,CAAA,IAAI,CAACA,QAAQ,CAAGJ,EAAOK,KAAK,CAACC,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAE,CAAA,EAAI,EAEpDb,EAAQ,IAAI,CAACc,OAAO,GAErB,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACC,KAAK,CAAGT,EAAOK,KAAK,CAACC,QAAQ,CAAC,IAAI,CAACI,GAAG,CAAE,CAAA,EAAI,EAEpEV,EAAOW,uBAAuB,CAAGX,EAAOY,kBAAkB,CAC1DZ,EAAOY,kBAAkB,CAAGZ,EAAOa,uBAAuB,CAE1D,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,QAAQ,EAAI,IAAI,CAACA,QAAQ,CAAC,EAAE,CAChD,IAAI,CAACN,KAAK,CAAG,IAAI,CAACL,QAAQ,CACtBH,GAAWN,EAAS,IAAI,CAACqB,SAAS,GAClC,CAAA,IAAI,CAACC,KAAK,CAAG,IAAI,CAACD,SAAS,AAAD,EAG9BxB,EAAUK,QAAQ,CAACqB,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACpB,KAAK,CAAGD,EAEb,IAAI,CAACW,KAAK,CAAG,IAAI,CAACD,OAAO,CACzB,IAAI,CAACM,OAAO,CAAG,IAAI,CAACC,QAAQ,EAAI,IAAI,CAACA,QAAQ,CAAC,EAAE,CAC5Cd,GAAWN,EAAS,IAAI,CAACyB,QAAQ,GACjC,CAAA,IAAI,CAACH,KAAK,CAAG,IAAI,CAACG,QAAQ,AAAD,EAE7BpB,EAAOa,uBAAuB,CAAGb,EAAOY,kBAAkB,CAC1DZ,EAAOY,kBAAkB,CAAGZ,EAAOW,uBAAuB,CAG1DX,EAAOW,uBAAuB,CAAG,KAAK,EACtC,IAAMU,EAAmBrB,EAAOsB,oBAAoB,GAEpD9B,EAAUK,QAAQ,CAACqB,KAAK,CAAC,IAAI,CAAEC,WAE/BnB,EAAOuB,qBAAqB,CAACF,EACjC,CACAG,UAAW,CACP,IAAMvB,EAAU,IAAI,CAACD,MAAM,CAACE,KAAK,CAACC,KAAK,CACnCsB,EAAO,EAAE,CAiBb,OAfA,IAAI,CAAChB,KAAK,CAAG,IAAI,CAACD,OAAO,CACrBP,GAAWN,EAAS,IAAI,CAACyB,QAAQ,GACjC,CAAA,IAAI,CAACH,KAAK,CAAG,IAAI,CAACG,QAAQ,AAAD,EAEzB,IAAI,CAACM,QAAQ,EACbD,CAAAA,EAAOjC,EAAUgC,QAAQ,CAACN,KAAK,CAAC,IAAI,CAAEC,UAAS,EAGnD,IAAI,CAACV,KAAK,CAAG,IAAI,CAACL,QAAQ,CACtBH,GAAWN,EAAS,IAAI,CAACqB,SAAS,GAClC,CAAA,IAAI,CAACC,KAAK,CAAG,IAAI,CAACD,SAAS,AAAD,EAE1B,IAAI,CAACW,WAAW,EAChBF,CAAAA,EAAOA,EAAKG,MAAM,CAACpC,EAAUgC,QAAQ,CAACN,KAAK,CAAC,IAAI,CAAEC,WAAU,EAEzDM,CACX,CACAI,SAAU,CACN,OAAOlC,EAAS,IAAI,CAACe,GAAG,GAAKf,EAAS,IAAI,CAACY,IAAI,CACnD,CACJ,EAqBM,CAAEuB,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAE,CAAI7C,GAM1B,OAAM8C,UAAsBpC,EAYxBC,UAAW,CACP,IAAoBG,EAASiC,AAAf,IAAI,CAAiBjC,MAAM,CAAEE,EAAQF,EAAOE,KAAK,CAAEgC,EAAiBlC,EAAOmC,OAAO,CAACC,QAAQ,CAAEC,EAAerC,EAAOmC,OAAO,CAACG,MAAM,CAAEC,EAAkBvC,EAAOmC,OAAO,CAACK,SAAS,CAAEC,EAAeR,AAA9L,IAAI,CAAgME,OAAO,CAAEO,EAAgBD,EAAaL,QAAQ,CAAEO,EAAYV,AAAhQ,IAAI,CAAkQW,IAAI,EAAIX,AAA9Q,IAAI,CAAgRW,IAAI,CAACC,KAAK,CAAEC,EAAoBf,EAAKW,EAAeH,GAAiBQ,UAAWb,EAAgBO,EAAaI,KAAK,CAAEF,EAAWV,AAAnZ,IAAI,CAAqZY,KAAK,CAAE7C,EAAO6C,KAAK,EACtbG,EAAO,OAAQC,EAAmBC,EAEtC,GADA,IAAI,CAACC,aAAa,CAACjC,KAAK,CAFV,IAAI,CAEcC,WAC5B,CAACc,AAHS,IAAI,CAGPlC,KAAK,CAAE,CACdiD,EAAO,UACP,GAAM,CAACI,EAAcC,EAAa,CAAGpB,AAL3B,IAAI,CAK6BlB,QAAQ,EAAI,EAAE,CACrDqC,GAAgB,CAAClD,EAAMoD,UAAU,GACjCF,EAAaG,IAAI,CAAC,CACdC,KAAMV,CACV,GACIO,IACAH,EAAY,CACRO,EAAGxB,AAZL,IAAI,CAYOwB,CAAC,CACVb,KAAMX,AAbR,IAAI,CAaUW,IAAI,AACpB,EACAX,AAfE,IAAI,CAeAwB,CAAC,CAAGxB,AAfR,IAAI,CAeU1B,IAAI,CACpB0B,AAhBE,IAAI,CAgBAW,IAAI,CAAGX,AAhBX,IAAI,CAgBaW,IAAI,CAAGX,AAhBxB,IAAI,CAgB0ByB,OAAO,GAAK,KAAK,EACjDT,EAAoBlB,EAAKE,AAjBvB,IAAI,CAiByBK,MAAM,CAAGL,AAjBtC,IAAI,CAiBwCK,MAAM,CAACS,SAAS,CAAG,KAAK,EAAGV,EAAeA,EAAaU,SAAS,CAAG,KAAK,EAAGN,EAAaI,KAAK,CAAEZ,AAjB3I,IAAI,CAiB6IW,IAAI,CAAGX,AAjBxJ,IAAI,CAiB0JW,IAAI,CAACC,KAAK,CAAG,KAAK,EAAGZ,AAjBnL,IAAI,CAiBqLY,KAAK,EAChMQ,EAAaE,IAAI,CAAC,CACdC,KAAMP,CACV,GACAnB,EArBE,IAAI,CAqBQoB,IAG1B,CACAjB,AAzBc,IAAI,CAyBZ0B,SAAS,EAAE,CAACX,EAAK,CAAChD,EAAO4D,mBAAmB,CAzBpC,IAAI,EA0BtB,CACAC,SAAU,CAON,OAJK5B,AAFS,IAAI,CAEPnB,OAAO,GACdmB,AAHU,IAAI,CAGRnB,OAAO,CAAGmB,AAHN,IAAI,CAGQ0B,SAAS,CAC/B1B,AAJU,IAAI,CAIR0B,SAAS,CAAG,KAAK,GAEpB,KAAK,CAACE,SACjB,CACJ,CACA/B,EAAOE,EAAcpD,SAAS,CAAE,CAC5BuE,cAAevD,EAAyBhB,SAAS,CAACiB,QAAQ,AAC9D,GAmPA,IAAIiE,EAAuHpG,EAAoB,KAC3IqG,EAA2IrG,EAAoBI,CAAC,CAACgG,GAerK,GAAM,CAAEE,KAAAA,CAAI,CAAE,CAAI9E,IAEZ,CAAE+E,UAAWC,CAAe,CAAEC,OAAQC,CAAY,CAAEC,YAAaC,CAAiB,CAAE,CAAG,AAAClF,IAA2IK,WAAW,CAG9O,CAAEqC,OAAQyC,CAAqB,CAAEC,MAAAA,CAAK,CAAEzC,KAAM0C,CAAmB,CAAE,CAAIvF,GAe7E,OAAMwF,UAAuBR,EAezBN,oBAAoB3B,CAAK,CAAE,CACvB,IAAqB/B,EAAQF,AAAd,IAAI,CAAiBE,KAAK,CAAEuC,EAAeR,EAAME,OAAO,CAAEwC,EAAgB3E,AAA1E,IAAI,CAA6EmC,OAAO,CAAEyC,EAAQ5E,AAAlG,IAAI,CAAqG4E,KAAK,CAAEvE,EAAQL,AAAxH,IAAI,CAA2HK,KAAK,CAAEwE,EAAqBJ,EAAoBE,EAAcG,MAAM,EAC9MH,EAAcG,MAAM,CAACC,KAAK,EAC1BJ,EAAcG,MAAM,CAACC,KAAK,CAACF,kBAAkB,CAAE,GAAIG,EAAYP,EAAoBhC,EAAauC,SAAS,CAAEL,EAAcK,SAAS,EAAGC,EAAc5E,EAAMC,QAAQ,CAACqE,EAAcO,SAAS,EAAI,EAAG,CAAA,GAAOC,EAAcjF,EAAMkF,QAAQ,CACnO/E,EAAMgF,GAAG,CAAGJ,EAAcA,EAC1BK,EAAiBb,EAAoBhC,EAAa6C,cAAc,CAAEX,EAAcW,cAAc,EAAGC,EAAiBd,EAAoBhC,EAAa8C,cAAc,CAAEZ,EAAcY,cAAc,CAAE9C,EAAaI,KAAK,CAAEZ,EAAMW,IAAI,CAAGX,EAAMW,IAAI,CAACC,KAAK,CAAG,KAAK,EAAGZ,EAAMY,KAAK,EAAG2C,EAAWf,EAAoBxC,EAAMzB,OAAO,CAAEyB,EAAMxB,KAAK,EAAGgF,EAAchB,EAAoBxC,EAAM7B,QAAQ,CAAE+E,GAAcjC,EAC3Y,GAAI,AAAoB,UAApB,OAAOsC,EACP,MAAO,CAAC,CAERvD,CAAAA,EAAMlC,KAAK,EACXuF,CAAAA,GAAkCT,CAAiB,EAEnDW,EAAW,EACXA,EAAW,EAENA,GAAYnF,EAAMgF,GAAG,EAC1BG,CAAAA,EAAWnF,EAAMgF,GAAG,AAAD,EAEnBI,EAAc,EACdA,EAAc,EAETA,GAAepF,EAAMgF,GAAG,EAC7BI,CAAAA,EAAcpF,EAAMgF,GAAG,AAAD,EAEtBpD,CAAAA,EAAMhB,KAAK,CAAG,GAAKgB,EAAMhB,KAAK,CAAG2D,EAAMS,GAAG,AAAD,GACzCC,CAAAA,EAAiB,CAAA,EAGjBrD,EAAMlB,QAAQ,EAAIkB,EAAMlB,QAAQ,CAAC,EAAE,GACnCmC,EAAY,CACRO,EAAGxB,EAAMwB,CAAC,CACVb,KAAMX,EAAMW,IAAI,AACpB,EACAX,EAAMwB,CAAC,CAAGxB,EAAM1B,IAAI,CACpB0B,EAAMW,IAAI,CAAGX,EAAMW,IAAI,CAAGX,EAAMyB,OAAO,GAAK,KAAK,EACjD6B,EAAiBd,EAAoBhC,EAAa8C,cAAc,CAAEZ,EAAcY,cAAc,CAAE9C,EAAaI,KAAK,CAAEZ,EAAMW,IAAI,CAAGX,EAAMW,IAAI,CAACC,KAAK,CAAG,KAAK,EAAGZ,EAAMY,KAAK,EACvK0B,EAAsBtC,EAAOiB,IAEjC,IAAMwC,EAAU,CACZzH,EAAG8F,IAA8HnF,SAAS,CAAC+G,SAAS,CAAC,CAAC,CAC9I,IACA1D,EAAMhB,KAAK,CACXuE,EACH,CAAE,CACC,IACAvD,EAAMhB,KAAK,CACXwE,EACH,CAAC,CAAEH,EACZ,EAQA,MAPI,CAACpF,EAAMoD,UAAU,GACjBoC,EAAQE,MAAM,CAAGL,EACjBG,CAAO,CAAC,eAAe,CAAGJ,EACtBN,GACAU,CAAAA,EAAQG,SAAS,CAAGb,CAAQ,GAG7BU,CACX,CAOAI,cAAc7D,CAAK,CAAE,CACjB,IAAqB8D,EAAiBtB,EAAoBzE,AAA3C,IAAI,CAA8CmC,OAAO,CAAC4D,cAAc,CAAE,KAAM/C,EAAOf,EAAM0B,SAAS,EAAI3D,AAA1G,IAAI,CAA6GE,KAAK,CAAC8F,UAAU,CAAGD,EAC/I,UAAY,MACX9D,CAAAA,EAAM0B,SAAS,EAChB1B,CAAAA,EAAM0B,SAAS,CAAG3D,AAHP,IAAI,CAGUE,KAAK,CAAC+F,QAAQ,CAACxE,IAAI,GACvCyE,QAAQ,CAAC,4BACT3C,IAAI,CAAC,CACN4C,OAAQ,EACZ,GACKC,GAAG,CAACpG,AARE,IAAI,CAQCqG,KAAK,CAAA,EAEzBpE,EAAM0B,SAAS,CAACX,EAAK,CAAC,IAAI,CAACY,mBAAmB,CAAC3B,GACnD,CAMAqE,kBAAmB,CACf,IAAMC,EAAUnC,EAAaxF,SAAS,CACjC0H,gBAAgB,CAACpF,KAAK,CAAC,IAAI,CAAEC,WAElC,OADAoF,EAAQC,MAAM,EAAID,EAAQE,KAAK,CAAG,EAC3BF,CACX,CAMAG,WAAY,CACR,IAAqBtB,EAAWpF,AAAjB,IAAI,CAAoBE,KAAK,CAACkF,QAAQ,CAMrD,IAAK,IAAMnD,KAJX,IAAI,CAAC0E,YAAY,CAACzF,KAAK,CAFR,IAAI,EAInB,IAAI,CAAC0F,cAAc,CAAC1F,KAAK,CAJV,IAAI,CAIeC,WAEdnB,AANL,IAAI,CAMQ6G,MAAM,EAAE,CAC/B,GAAM,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,EAAY,CAAC,CAAC,CAAEC,WAAAA,CAAU,CAAE,CAAG/E,CACnDA,CAAAA,EAAMhB,KAAK,CAAG8F,EAAUE,CAAC,EAAI,EAC7BF,EAAUE,CAAC,CAAGhF,EAAMhB,KAAK,CAAG6F,EAAa,EACrCE,IACI5B,EACA4B,CAAU,CAAC,EAAE,CAAGhH,AAZb,IAAI,CAYgB4E,KAAK,CAACS,GAAG,CAAGpD,EAAMhB,KAAK,CAG9C+F,CAAU,CAAC,EAAE,CAAG/E,EAAMhB,KAAK,CAGvC,CACAjB,AAnBe,IAAI,CAmBZkH,aAAa,CAACV,MAAM,EAAIxG,AAnBhB,IAAI,CAmBmBkH,aAAa,CAACT,KAAK,CAAG,CAChE,CAMAU,YAAa,CACT,IAAqBjH,EAAQF,AAAd,IAAI,CAAiBE,KAAK,CAAEkH,EAAcpH,AAA1C,IAAI,CAA6C6G,MAAM,CAACQ,MAAM,CAAEnF,EAAiBlC,AAAjF,IAAI,CAAoFoC,QAAQ,CAAGpC,AAAnG,IAAI,CAAsGmC,OAAO,CAACC,QAAQ,CAAEG,EAAkBvC,AAA9I,IAAI,CAAiJmC,OAAO,CAACK,SAAS,CACjL8E,EAAI,EAAGxE,EAAmBb,EAAOU,EAGrC,IAFA,IAAI,CAAC4E,gBAAgB,CAACrG,KAAK,CAFZ,IAAI,CAEiBC,WAE7BmG,EAAIF,GAAa,CAEpB,GAAM,CAAChE,EAAcC,EAAa,CAAGpB,AADrCA,CAAAA,EAAQjC,AALG,IAAI,CAKA6G,MAAM,CAACS,EAAE,AAAD,EACoBvG,QAAQ,EAAI,EAAE,CACzDf,AAPW,IAAI,CAOR8F,aAAa,CAAC7D,GACjBoB,IACAA,EAAamE,OAAO,CAACvF,KAAK,CAAGA,EAC7BoB,EAAa6C,QAAQ,CAAC,6BAEtBjE,EAAM0B,SAAS,EACf1B,CAAAA,EAAM0B,SAAS,CAAC6D,OAAO,CAACvF,KAAK,CAAGA,CAAI,EAEpCmB,IACAT,EAAYV,EAAMW,IAAI,EAAIX,EAAMW,IAAI,CAACC,KAAK,CAC1CC,EAAoB2B,EAAoBxC,EAAME,OAAO,CAACC,QAAQ,CAAEG,GAAiBQ,UAAWb,EAAgBD,EAAME,OAAO,CAACU,KAAK,CAAEF,EAAWV,EAAMY,KAAK,CAAE7C,AAjBlJ,IAAI,CAiBqJ6C,KAAK,EAChK3C,EAAMoD,UAAU,EACjBF,EAAaG,IAAI,CAAC,CACdC,KAAMV,CACV,GAEJM,EAAa8C,QAAQ,CAAC,4BAE1BoB,GACJ,CACJ,CAgBAG,aAAaxF,CAAK,CAAElC,CAAK,CAAE,CACvB,IAAM0H,EAAe,KAAK,CAACA,aAAavG,KAAK,CAAC,IAAI,CAAEC,WAIpD,MAHc,UAAVpB,GACA,OAAO0H,EAAajE,IAAI,CAErBiE,CACX,CAKAd,cAAe,CACXvC,EAAaxF,SAAS,CAAC8H,SAAS,CAACxF,KAAK,CAAC,IAAI,EAC3CoD,EAAkB1F,SAAS,CAAC8I,oBAAoB,CAACxG,KAAK,CAAC,IAAI,CAC/D,CACJ,CAMAwD,EAAeiD,cAAc,CAAGnD,EAAMN,EAAgByD,cAAc,CAnbrC,CAE3BC,YAAa,CAAA,EAEb7E,UAAW,OAEX8E,UAAW,EACXC,WAAY,EAQZxC,eAAgB,EAEhByC,eAAgB,CAAA,EAChBC,aAAc,GACdC,MAAO,CAAA,EACPC,aAAc,GACdC,aAAc,YAUd/F,SAAU,UAcV0C,OAAQ,CACJC,MAAO,CAEHqD,cAAe,EAOfvD,mBAAoB,EAEpBwD,KAAM,CAAA,CACV,CACJ,CACJ,GAwXA9D,EAAsBG,EAAe9F,SAAS,CAAE,CAC5C0J,SAAUlE,EAAaxF,SAAS,CAAC0J,QAAQ,CACzCC,UAAWvE,EACXwE,YAAapE,EAAaxF,SAAS,CAAC4J,WAAW,CAC/ClJ,WA9dyD0C,EA+dzDuF,iBAAkBrD,EAAgBtF,SAAS,CAACuI,UAAU,CACtDsB,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1D7B,eAAgB1C,EAAgBtF,SAAS,CAAC8H,SAAS,AACvD,GACAtH,IAA0IsJ,kBAAkB,CAAC,WAAYhE,GAa5I,IAAM1F,EAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}