!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Series,t._Highcharts.SVGRenderer,t._Highcharts.SVGElement):"function"==typeof define&&define.amd?define("highcharts/modules/arc-diagram",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry,t.Series,t.SVGRenderer,t.SVGElement)}):"object"==typeof exports?exports["highcharts/modules/arc-diagram"]=e(t._Highcharts,t._Highcharts.SeriesRegistry,t._Highcharts.Series,t._Highcharts.SVGRenderer,t._Highcharts.SVGElement):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Series,t.Highcharts.SVGRenderer,t.Highcharts.SVGElement)}("undefined"==typeof window?this:window,(t,e,o,s,i)=>(()=>{"use strict";var n,r,a={28:t=>{t.exports=i},512:t=>{t.exports=e},540:t=>{t.exports=s},820:t=>{t.exports=o},944:e=>{e.exports=t}},h={};function l(t){var e=h[t];if(void 0!==e)return e.exports;var o=h[t]={exports:{}};return a[t](o,o.exports,l),o.exports}l.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return l.d(e,{a:e}),e},l.d=(t,e)=>{for(var o in e)l.o(e,o)&&!l.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},l.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var d={};l.d(d,{default:()=>tt});var p=l(944),c=l.n(p),u=l(512),f=l.n(u);let{series:{prototype:m,prototype:{pointClass:{prototype:g}}}}=f(),{defined:y,extend:x,find:k,merge:b,pick:S}=c();!function(t){function e(){return this.data=[].concat(this.points||[],this.nodes),m.destroy.apply(this,arguments)}function o(){this.nodes&&(this.nodes.forEach(t=>{t.destroy()}),this.nodes.length=0),m.setData.apply(this,arguments)}function s(t){let e=arguments,o=this.isNode?this.linksTo.concat(this.linksFrom):[this.fromNode,this.toNode];"select"!==t&&o.forEach(t=>{t&&t.series&&(g.setState.apply(t,e),!t.isNode&&(t.fromNode.graphic&&g.setState.apply(t.fromNode,e),t.toNode&&t.toNode.graphic&&g.setState.apply(t.toNode,e)))}),g.setState.apply(this,e)}function i(t,e,o,s){let i=this.series.options.nodes,n=this.series.options.data,r=n?.length||0,a=n?.[this.index];if(g.update.call(this,t,!this.isNode&&e,o,s),this.isNode){let t=(i||[]).reduce((t,e,o)=>this.id===e.id?o:t,-1),s=b(i&&i[t]||{},n?.[this.index]||{});n&&(a?n[this.index]=a:n.length=r),i?t>=0?i[t]=s:i.push(s):this.series.options.nodes=[s],S(e,!0)&&this.series.chart.redraw(o)}}t.compose=function(t,n){let r=t.prototype,a=n.prototype;return r.setNodeState=s,r.setState=s,r.update=i,a.destroy=e,a.setData=o,n},t.createNode=function(t){let e=this.pointClass,o=(t,e)=>k(t,t=>t.id===e),s=o(this.nodes,t),i;if(!s){i=this.options.nodes&&o(this.options.nodes,t);let n=new e(this,x({className:"highcharts-node",isNode:!0,id:t,y:1},i));n.linksTo=[],n.linksFrom=[],n.getSum=function(){let t=0,e=0;return n.linksTo.forEach(e=>{t+=e.weight||0}),n.linksFrom.forEach(t=>{e+=t.weight||0}),Math.max(t,e)},n.offset=function(t,e){let o=0;for(let s=0;s<n[e].length;s++){if(n[e][s]===t)return o;o+=n[e][s].weight}},n.hasShape=function(){let t=0;return n.linksTo.forEach(e=>{e.outgoing&&t++}),!n.linksTo.length||t!==n.linksTo.length},n.index=this.nodes.push(n)-1,s=n}return s.formatPrefix="node",s.name=s.name||s.options.id||"",s.mass=S(s.options.mass,s.options.marker&&s.options.marker.radius,this.options.marker&&this.options.marker.radius,4),s},t.destroy=e,t.generatePoints=function(){let t=this.chart,e={};m.generatePoints.call(this),this.nodes||(this.nodes=[]),this.colorCounter=0,this.nodes.forEach(t=>{t.linksFrom.length=0,t.linksTo.length=0,t.level=t.options.level}),this.points.forEach(o=>{y(o.from)&&(e[o.from]||(e[o.from]=this.createNode(o.from)),e[o.from].linksFrom.push(o),o.fromNode=e[o.from],t.styledMode?o.colorIndex=S(o.options.colorIndex,e[o.from].colorIndex):o.color=o.options.color||e[o.from].color),y(o.to)&&(e[o.to]||(e[o.to]=this.createNode(o.to)),e[o.to].linksTo.push(o),o.toNode=e[o.to]),o.name=o.name||o.id},this),this.nodeLookup=e},t.setNodeState=s,t.updateNode=i}(n||(n={}));let L=n,{seriesTypes:{sankey:{prototype:{pointClass:P}}}}=f(),{extend:N}=c();class v extends P{isValid(){return!0}}N(v.prototype,{setState:L.setNodeState});let{defined:T,getAlignFactor:C,relativeLength:M}=c();!function(t){t.compose=function(t,o){return t.sankeyColumn=new e(t,o),t};class e{constructor(t,e){this.points=t,this.series=e}getTranslationFactor(t){let e=this.points,o=e.slice(),s=t.chart,i=t.options.minLinkWidth||0,n,r=0,a,h=(s.plotSizeY||0)-(t.options.borderWidth||0)-(e.length-1)*t.nodePadding;for(;e.length;){for(r=h/e.sankeyColumn.sum(),n=!1,a=e.length;a--;)e[a].getSum()*r<i&&(e.splice(a,1),h=Math.max(0,h-i),n=!0);if(!n)break}for(let t of(e.length=0,o))e.push(t);return r}top(t){let e=this.series,o=e.nodePadding,s=this.points.reduce((s,i)=>(s>0&&(s+=o),s+=Math.max(i.getSum()*t,e.options.minLinkWidth||0)),0);return C(e.options.nodeAlignment||"center")*((e.chart.plotSizeY||0)-s)}left(t){let e=this.series,o=e.chart,s=e.options.equalNodes,i=o.inverted?o.plotHeight:o.plotWidth,n=e.nodePadding,r=this.points.reduce((o,r)=>(o>0&&(o+=n),o+=s?i/r.series.nodes.length-n:Math.max(r.getSum()*t,e.options.minLinkWidth||0)),0);return((o.plotSizeX||0)-Math.round(r))/2}sum(){return this.points.reduce((t,e)=>t+e.getSum(),0)}offset(t,e){let o=this.points,s=this.series,i=s.nodePadding,n=0,r;if(s.is("organization")&&t.hangsFrom)return{absoluteTop:t.hangsFrom.nodeY};for(let a=0;a<o.length;a++){let h=o[a].getSum(),l=Math.max(h*e,s.options.minLinkWidth||0),d=t.options[s.chart.inverted?"offsetHorizontal":"offsetVertical"],p=t.options.offset||0;if(r=h?l+i:0,o[a]===t)return{relativeTop:n+(T(d)?M(d,l):M(p,r))};n+=r}}}t.SankeyColumnAdditions=e}(r||(r={}));let H=r;var W=l(820),w=l.n(W),A=l(540),O=l.n(A),E=l(28),z=l.n(E);let{deg2rad:R}=c(),{addEvent:Y,merge:F,uniqueKey:_,defined:X,extend:B}=c();function V(t,e){e=F(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let o=this.renderer.url,s=this.text||this,i=s.textPath,{attributes:n,enabled:r}=e;if(t=t||i&&i.path,i&&i.undo(),t&&r){let e=Y(s,"afterModifyTree",e=>{if(t&&r){let i=t.attr("id");i||t.attr("id",i=_());let r={x:0,y:0};X(n.dx)&&(r.dx=n.dx,delete n.dx),X(n.dy)&&(r.dy=n.dy,delete n.dy),s.attr(r),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let a=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:B(n,{"text-anchor":n.textAnchor,href:`${o}#${i}`}),children:a}}});s.textPath={path:t,undo:e}}else s.attr({dx:0,dy:0}),delete s.textPath;return this.added&&(s.textCache="",this.renderer.buildText(s)),this}function D(t){let e=t.bBox,o=this.element?.querySelector("textPath");if(o){let t=[],{b:s,h:i}=this.renderer.fontMetrics(this.element),n=i-s,r=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),a=o.innerHTML.replace(r,"").split(/<tspan class="highcharts-br"[^>]*>/),h=a.length,l=(t,e)=>{let{x:i,y:r}=e,a=(o.getRotationOfChar(t)-90)*R,h=Math.cos(a),l=Math.sin(a);return[[i-n*h,r-n*l],[i+s*h,r+s*l]]};for(let e=0,s=0;s<h;s++){let i=a[s].length;for(let n=0;n<i;n+=5)try{let i=e+n+s,[r,a]=l(i,o.getStartPositionOfChar(i));0===n?(t.push(a),t.push(r)):(0===s&&t.unshift(a),s===h-1&&t.push(r))}catch(t){break}e+=i-1;try{let i=e+s,n=o.getEndPositionOfChar(i),[r,a]=l(i,n);t.unshift(a),t.unshift(r)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function G(t){let e=t.labelOptions,o=t.point,s=e[o.formatPrefix+"TextPath"]||e.textPath;s&&!e.useHTML&&(this.setTextPath(o.getDataLabelPath?.(this)||o.graphic,s),o.dataLabelPath&&!s.enabled&&(o.dataLabelPath=o.dataLabelPath.destroy()))}({compose:function(t){Y(t,"afterGetBBox",D),Y(t,"beforeAddingDataLabel",G);let e=t.prototype;e.setTextPath||(e.setTextPath=V)}}).compose(z());let{prototype:{symbols:j}}=O(),{seriesTypes:{column:q,sankey:I}}=f(),{crisp:$,extend:Z,merge:J,pick:K,relativeLength:Q}=c();class U extends I{createNodeColumns(){let t=this,e=t.chart,o=H.compose([],t);return o.sankeyColumn.maxLength=e.inverted?e.plotHeight:e.plotWidth,o.sankeyColumn.getTranslationFactor=t=>{let s=o.slice(),i=this.options.minLinkWidth||0,n,r=0,a,h,l=0,d=1,p=0,c=(e.plotSizeX||0)-(t.options.marker&&t.options.marker.lineWidth||0)-(o.length-1)*t.nodePadding;for(;o.length;){for(r=c/o.sankeyColumn.sum(),n=!1,a=o.length;a--;){h=o[a].getSum()*r*d;let t=Math.min(e.plotHeight,e.plotWidth);h>t?d=Math.min(t/h,d):h<i&&(o.splice(a,1),c-=i,h=i,n=!0),p+=h*(1-d)/2,l=Math.max(l,h)}if(!n)break}return o.length=0,s.forEach(t=>{t.scale=d,o.push(t)}),o.sankeyColumn.maxRadius=l,o.sankeyColumn.scale=d,o.sankeyColumn.additionalSpace=p,r},o.sankeyColumn.offset=function(s,i){let n=s.series.options.equalNodes,r=t.nodePadding,a=Math.min(e.plotWidth,e.plotHeight,(o.sankeyColumn.maxLength||0)/t.nodes.length-r),h=o.sankeyColumn.additionalSpace||0,l;for(let e=0;e<o.length;e++){let d=o[e].getSum()*(o.sankeyColumn.scale||0),p=n?a:Math.max(d*i,t.options.minLinkWidth||0);if(l=d?p+r:0,o[e]===s)return{relativeLeft:h+Q(s.options.offset||0,l)};h+=l}},t.nodes.forEach(function(t){t.column=0,o.push(t)}),[o]}translateLink(t){let e=t.fromNode,o=t.toNode,s=this.chart,i=this.translationFactor,n=t.options,r=this.options,a=K(n.linkWeight,r.linkWeight,Math.max((t.weight||0)*i*e.scale,this.options.minLinkWidth||0)),h=t.series.options.centeredLinks,l=e.nodeY,d=(e,o)=>{let s=(e.offset(t,o)||0)*i;return Math.min(e.nodeX+s,e.nodeX+(e.shapeArgs&&e.shapeArgs.height||0)-a)},p=h?e.nodeX+((e.shapeArgs.height||0)-a)/2:d(e,"linksFrom"),c=h?o.nodeX+((o.shapeArgs.height||0)-a)/2:d(o,"linksTo"),u=l;p>c&&([p,c]=[c,p]),r.reversed&&([p,c]=[c,p],u=(s.plotSizeY||0)-u),t.shapeType="path",t.linkBase=[p,p+a,c,c+a];let f=(c+a-p)/Math.abs(c+a-p)*K(r.linkRadius,Math.min(Math.abs(c+a-p)/2,e.nodeY-Math.abs(a)));t.shapeArgs={d:[["M",p,u],["A",(c+a-p)/2,f,0,0,1,c+a,u],["L",c,u],["A",(c-p-a)/2,f-a,0,0,0,p+a,u],["Z"]]},t.dlBox={x:p+(c-p)/2,y:u-f,height:a,width:0},t.tooltipPos=s.inverted?[(s.plotSizeY||0)-t.dlBox.y-a/2,(s.plotSizeX||0)-t.dlBox.x]:[t.dlBox.x,t.dlBox.y+a/2],t.y=t.plotY=1,t.x=t.plotX=1,t.color||(t.color=e.color)}translateNode(t,e){let o=this.translationFactor,s=this.chart,i=s.inverted?s.plotWidth:s.plotHeight,n=this.options,r=Math.min(s.plotWidth,s.plotHeight,i/t.series.nodes.length-this.nodePadding),a=t.getSum()*(e.sankeyColumn.scale||0),h=n.equalNodes?r:Math.max(a*o,this.options.minLinkWidth||0),l=n.marker?.lineWidth||0,d=e.sankeyColumn.offset(t,o),p=$(K(d&&d.absoluteLeft,(e.sankeyColumn.left(o)||0)+(d&&d.relativeLeft||0)),l),c=J(n.marker,t.options.marker),u=c.symbol,f=c.radius,m=parseInt(n.offset,10)*((s.inverted?s.plotWidth:s.plotHeight)-($(this.colDistance*(t.column||0)+(c.lineWidth||0)/2,l)+(e.sankeyColumn.scale||0)*(e.sankeyColumn.maxRadius||0)/2))/100;if(t.sum=a,a){t.nodeX=p,t.nodeY=m;let e=t.options.width||n.width||h,o=t.options.height||n.height||h,i=m;n.reversed&&(i=(s.plotSizeY||0)-m,s.inverted&&(i=(s.plotSizeY||0)-m)),this.mapOptionsToLevel&&(t.dlOptions=I.getDLOptions({level:this.mapOptionsToLevel[t.level],optionsPoint:t.options})),t.plotX=1,t.plotY=1,t.tooltipPos=s.inverted?[(s.plotSizeY||0)-i-o/2,(s.plotSizeX||0)-p-e/2]:[p+e/2,i+o/2],t.shapeType="path",t.shapeArgs={d:j[u||"circle"](p,i-(f||o)/2,f||e,f||o),width:f||e,height:f||o},t.dlBox={x:p+e/2,y:i,height:0,width:0}}else t.dlOptions={enabled:!1}}drawDataLabels(){if(this.options.dataLabels){let t=this.options.dataLabels.textPath;q.prototype.drawDataLabels.call(this,this.nodes),this.options.dataLabels.textPath=this.options.dataLabels.linkTextPath,q.prototype.drawDataLabels.call(this,this.data),this.options.dataLabels.textPath=t}}pointAttribs(t,e){if(t&&t.isNode){let{...t}=w().prototype.pointAttribs.apply(this,arguments);return t}return super.pointAttribs.apply(this,arguments)}markerAttribs(t){return t.isNode?super.markerAttribs.apply(this,arguments):{}}}U.defaultOptions=J(I.defaultOptions,{centeredLinks:!1,equalNodes:!1,dataLabels:{linkTextPath:{attributes:{startOffset:"25%"}}},marker:{fillOpacity:1,lineWidth:0,states:{},symbol:"circle"},offset:"100%",reversed:!1}),Z(U.prototype,{orderNodes:!1}),U.prototype.pointClass=v,f().registerSeriesType("arcdiagram",U);let tt=c();return d.default})());