!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/funnel",["highcharts/highcharts"],function(t){return e(t,t.SeriesRegistry)}):"object"==typeof exports?exports["highcharts/modules/funnel"]=e(t._Highcharts,t._Highcharts.SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var i={512:t=>{t.exports=e},944:e=>{e.exports=t}},a={};function d(t){var e=a[t];if(void 0!==e)return e.exports;var s=a[t]={exports:{}};return i[t](s,s.exports,d),s.exports}d.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return d.d(e,{a:e}),e},d.d=(t,e)=>{for(var i in e)d.o(e,i)&&!d.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},d.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var s={};d.d(s,{default:()=>N});var o=d(944),r=d.n(o);let{defaultOptions:n}=r(),{noop:l}=r(),{addEvent:h,extend:p,isObject:c,merge:u,relativeLength:g}=r(),x={radius:0,scope:"stack",where:void 0};function y(t,e){return c(t)||(t={radius:t||0}),u(x,e,t)}let f={optionsToObject:y};var b=d(512),L=d.n(b);let{composed:m,noop:v}=r(),{column:M,pie:W}=L().seriesTypes,{addEvent:A,correctFloat:C,extend:H,fireEvent:w,isArray:P,merge:k,pick:O,pushUnique:S,relativeLength:D,splat:I}=r(),j=L().series.prototype.alignDataLabel;function R(t,e){return/%$/.test(t)?e*parseInt(t,10)/100:parseInt(t,10)}class T extends W{alignDataLabel(t,e,i,a,d){let s=t.series,o=s.options.reversed,r=t.dlBox||t.shapeArgs,{align:n,padding:l=0,verticalAlign:h}=i,p=((s.options||{}).dataLabels||{}).inside,c=s.center[1],u=t.plotY||0,g=e.height??e.getBBox().height,x=s.getWidthAt((o?2*c-u:u)-r.height/2+g),y="middle"===h?(r.topWidth-r.bottomWidth)/4:(x-r.bottomWidth)/2,f=r.y,b=r.x;"middle"===h?f=r.y-r.height/2+g/2:"top"===h&&(f=r.y-r.height+g+l),("top"===h&&!o||"bottom"===h&&o||"middle"===h)&&("right"===n?b=r.x-l+y:"left"===n&&(b=r.x+l-y)),a={x:b,y:o?f-r.height:f,width:r.bottomWidth,height:r.height},i.verticalAlign="bottom",p&&(i.distance=void 0),p&&t.visible&&j.call(s,t,e,i,a,d),p&&(!t.visible&&t.dataLabel&&(t.dataLabel.placed=!1),t.contrastColor&&e.css({color:t.contrastColor}))}drawDataLabels(){(I(this.options.dataLabels||{})[0].inside?M:W).prototype.drawDataLabels.call(this)}getDataLabelPosition(t,e){let i=t.plotY||0,a=t.half?1:-1,d=this.getX(i,!!t.half,t);return{distance:e,natural:{x:0,y:i},computed:{},alignment:t.half?"right":"left",connectorPosition:{breakAt:{x:d+(e-5)*a,y:i},touchingSliceAt:{x:d+e*a,y:i}}}}translate(){let t=this,e=t.chart,i=t.options,a=i.reversed,d=i.ignoreHiddenPoint,s=f.optionsToObject(i.borderRadius),o=e.plotWidth,r=e.plotHeight,n=i.center,l=R(n[0],o),h=R(n[1],r),p=R(i.width,o),c=R(i.height,r),u=R(i.neckWidth,o),g=R(i.neckHeight,r),x=h-c/2+c-g,y=t.points,b=D(s.radius,p),L=s.scope,m=+("left"===i.dataLabels.position),M=t=>{let e=Math.tan(t/2),i=Math.cos(S),d=Math.sin(S),s=b,o=s/e,r=Math.tan((Math.PI-t)/3.2104);return o>I&&(s=(o=I)*e),{dx:[o*i,(o-(r*=s))*i,o-r,o],dy:[o*d,(o-r)*d,o-r,o].map(t=>a?-t:t)}},W=0,A=0,H,P,k,S,I,j,T,Y,_,B,X,N;for(let e of(t.getWidthAt=function(t){let e=h-c/2;return t>x||c===g?u:u+(p-u)*(1-(t-e)/(c-g))},t.getX=function(e,i,d){return l+(i?-1:1)*(t.getWidthAt(a?2*h-e:e)/2+(d.dataLabel?.dataLabelPosition?.distance??D(this.options.dataLabels?.distance||0,p)))},t.center=[l,h,c],t.centerX=l,y))e.y&&e.isValid()&&(!d||!1!==e.visible)&&(W+=e.y);for(let e of y){if(N=null,k=W?e.y/W:0,B=(T=h-c/2+A*c)+k*c,Y=(j=l-(H=t.getWidthAt(T))/2)+H,X=(_=l-(H=t.getWidthAt(B))/2)+H,C(T)>=x?(j=_=l-u/2,Y=X=l+u/2):B>x&&(N=B,X=(_=l-(H=t.getWidthAt(x))/2)+H,B=x),a&&(T=2*h-T,B=2*h-B,null!==N&&(N=2*h-N)),b&&("point"===L||0===e.index||e.index===y.length-1||null!==N)){let t=Math.abs(B-T),i=Y-X,a=X-_,d=Math.sqrt(i*i+t*t);S=Math.atan(0!==i?t/i:1/0),I=d/2,null!==N&&(I=Math.min(I,Math.abs(N-B)/2)),a>=1&&(I=Math.min(I,a/2));let s=M(S);if(P="stack"===L&&0!==e.index?[["M",j,T],["L",Y,T]]:[["M",j+s.dx[0],T+s.dy[0]],["C",j+s.dx[1],T+s.dy[1],j+s.dx[2],T,j+s.dx[3],T],["L",Y-s.dx[3],T],["C",Y-s.dx[2],T,Y-s.dx[1],T+s.dy[1],Y-s.dx[0],T+s.dy[0]]],null!==N){let t=M(Math.PI/2);s=M(Math.PI/2+S),P.push(["L",X+s.dx[0],B-s.dy[0]],["C",X+s.dx[1],B-s.dy[1],X,B+s.dy[2],X,B+s.dy[3]]),"stack"===L&&e.index!==y.length-1?P.push(["L",X,N],["L",_,N]):P.push(["L",X,N-t.dy[3]],["C",X,N-t.dy[2],X-t.dx[2],N,X-t.dx[3],N],["L",_+t.dx[3],N],["C",_+t.dx[2],N,_,N-t.dy[2],_,N-t.dy[3]]),P.push(["L",_,B+s.dy[3]],["C",_,B+s.dy[2],_-s.dx[1],B-s.dy[1],_-s.dx[0],B-s.dy[0]])}else a>=1?(s=M(Math.PI-S),"stack"===L&&0===e.index?P.push(["L",X,B],["L",_,B]):P.push(["L",X+s.dx[0],B-s.dy[0]],["C",X+s.dx[1],B-s.dy[1],X-s.dx[2],B,X-s.dx[3],B],["L",_+s.dx[3],B],["C",_+s.dx[2],B,_-s.dx[1],B-s.dy[1],_-s.dx[0],B-s.dy[0]])):(s=M(Math.PI-2*S),P.push(["L",_+s.dx[0],B-s.dy[0]],["C",_+s.dx[1],B-s.dy[1],_-s.dx[1],B-s.dy[1],_-s.dx[0],B-s.dy[0]]))}else P=[["M",j,T],["L",Y,T],["L",X,B]],null!==N&&P.push(["L",X,N],["L",_,N]),P.push(["L",_,B]);P.push(["Z"]),e.shapeType="path",e.shapeArgs={d:P},e.percentage=100*k,e.plotX=l,e.plotY=(T+(N||B))/2,e.tooltipPos=[l,e.plotY],e.dlBox={x:_,y:T,topWidth:Y-j,bottomWidth:X-_,height:Math.abs(O(N,B)-T),width:NaN},e.slice=v,e.half=m,e.isValid()&&(!d||!1!==e.visible)&&(A+=k)}w(t,"afterTranslate")}sortByAngle(t){t.sort((t,e)=>t.plotY-e.plotY)}}T.defaultOptions=k(W.defaultOptions,{animation:!1,borderRadius:0,center:["50%","50%"],width:"90%",neckWidth:"30%",height:"100%",neckHeight:"25%",reversed:!1,size:!0,dataLabels:{connectorWidth:1,verticalAlign:"middle"},states:{select:{color:"#cccccc",borderColor:"#000000"}}}),H(T.prototype,{animate:v}),function(t){function e(){for(let t of this.series){let e=t.options&&t.options.dataLabels;P(e)&&(e=e[0]),t.is("pie")&&t.placeDataLabels&&e&&!e.inside&&t.placeDataLabels()}}t.compose=function(t){S(m,"FunnelSeries")&&A(t,"afterHideAllOverlappingLabels",e)}}(T||(T={})),L().registerSeriesType("funnel",T);let Y=T,{merge:_}=r();class B extends Y{}B.defaultOptions=_(Y.defaultOptions,{neckHeight:"0%",neckWidth:"0%",reversed:!0}),L().registerSeriesType("pyramid",B);let X=r();Y.compose(X.Chart);let N=r();return s.default})());