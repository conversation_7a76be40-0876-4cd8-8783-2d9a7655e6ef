{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/bullet\n * @requires highcharts\n *\n * Bullet graph series type for Highcharts\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/bullet\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Series\"],[\"types\"],[\"column\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/bullet\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__448__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 448:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__448__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ bullet_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"column\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"column\"]}\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_ = __webpack_require__(448);\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_);\n;// ./code/es-modules/Series/Bullet/BulletPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\nclass BulletPoint extends (highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default()).prototype.pointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroys target graphic.\n     * @private\n     */\n    destroy() {\n        const series = this;\n        if (series.targetGraphic) {\n            series.targetGraphic = series.targetGraphic.destroy();\n        }\n        super.destroy.apply(series, arguments);\n        return;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bullet_BulletPoint = (BulletPoint);\n\n;// ./code/es-modules/Series/Bullet/BulletSeriesDefaults.js\n/* *\n *\n *  Imports\n *\n * */\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A bullet graph is a variation of a bar graph. The bullet graph features\n * a single measure, compares it to a target, and displays it in the context\n * of qualitative ranges of performance that could be set using\n * [plotBands](#yAxis.plotBands) on [yAxis](#yAxis).\n *\n * @sample {highcharts} highcharts/demo/bullet-graph/\n *         Bullet graph\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts\n * @excluding    allAreas, boostThreshold, colorAxis, compare, compareBase,\n *               dataSorting, boostBlending\n * @requires     modules/bullet\n * @optionparent plotOptions.bullet\n */\nconst BulletSeriesDefaults = {\n    /**\n     * All options related with look and positioning of targets.\n     *\n     * @since 6.0.0\n     */\n    targetOptions: {\n        /**\n         * The width of the rectangle representing the target. Could be set\n         * as a pixel value or as a percentage of a column width.\n         *\n         * @type  {number|string}\n         * @since 6.0.0\n         */\n        width: '140%',\n        /**\n         * The height of the rectangle representing the target.\n         *\n         * @since 6.0.0\n         */\n        height: 3,\n        /**\n         * The border color of the rectangle representing the target. When\n         * not set, the point's border color is used.\n         *\n         * In styled mode, use class `highcharts-bullet-target` instead.\n         *\n         * @type      {Highcharts.ColorString}\n         * @since     6.0.0\n         * @product   highcharts\n         * @apioption plotOptions.bullet.targetOptions.borderColor\n         */\n        /**\n         * The color of the rectangle representing the target. When not set,\n         * point's color (if set in point's options -\n         * [`color`](#series.bullet.data.color)) or zone of the target value\n         * (if [`zones`](#plotOptions.bullet.zones) or\n         * [`negativeColor`](#plotOptions.bullet.negativeColor) are set)\n         * or the same color as the point has is used.\n         *\n         * In styled mode, use class `highcharts-bullet-target` instead.\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     6.0.0\n         * @product   highcharts\n         * @apioption plotOptions.bullet.targetOptions.color\n         */\n        /**\n         * The border width of the rectangle representing the target.\n         *\n         * In styled mode, use class `highcharts-bullet-target` instead.\n         *\n         * @since   6.0.0\n         */\n        borderWidth: 0,\n        /**\n         * The border radius of the rectangle representing the target.\n         */\n        borderRadius: 0\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{series.color}\">\\u25CF</span>' +\n            ' {series.name}: <b>{point.y}</b>. Target: <b>{point.target}' +\n            '</b><br/>'\n    }\n};\n/**\n * A `bullet` series. If the [type](#series.bullet.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.bullet\n * @since     6.0.0\n * @product   highcharts\n * @excluding dataParser, dataURL, marker, boostThreshold,\n *            boostBlending\n * @requires  modules/bullet\n * @apioption series.bullet\n */\n/**\n * An array of data points for the series. For the `bullet` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,y,target`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2\\. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 40, 75],\n *        [1, 50, 50],\n *        [2, 60, 40]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.bullet.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 0,\n *        y: 40,\n *        target: 75,\n *        name: \"Point1\",\n *        color: \"#00FF00\"\n *    }, {\n *         x: 1,\n *        y: 60,\n *        target: 40,\n *        name: \"Point2\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.column.data\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.bullet.data\n */\n/**\n * The target value of a point.\n *\n * @type      {number}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.bullet.data.target\n */\n/**\n * Individual target options for each point.\n *\n * @extends   plotOptions.bullet.targetOptions\n * @product   highcharts\n * @apioption series.bullet.data.targetOptions\n */\n/**\n * @product   highcharts\n * @excluding halo, lineWidth, lineWidthPlus, marker\n * @apioption series.bullet.states.hover\n */\n/**\n * @product   highcharts\n * @excluding halo, lineWidth, lineWidthPlus, marker\n * @apioption series.bullet.states.select\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bullet_BulletSeriesDefaults = (BulletSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Bullet/BulletSeries.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { extend, isNumber, merge, pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The bullet series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.bullet\n *\n * @augments Highcharts.Series\n */\nclass BulletSeries extends (highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default()) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Draws the targets. For inverted chart, the `series.group` is rotated,\n     * so the same coordinates apply. This method is based on column series\n     * drawPoints function.\n     *\n     * @ignore\n     * @function Highcharts.Series#drawPoints\n     */\n    drawPoints() {\n        const series = this, chart = series.chart, options = series.options, animationLimit = options.animationLimit || 250;\n        super.drawPoints.apply(this, arguments);\n        for (const point of series.points) {\n            const pointOptions = point.options, targetVal = point.target, pointVal = point.y;\n            let targetShapeArgs, targetGraphic = point.targetGraphic, width, height, targetOptions, y;\n            if (isNumber(targetVal) && targetVal !== null) {\n                targetOptions = merge(options.targetOptions, pointOptions.targetOptions);\n                height = targetOptions.height;\n                let shapeArgs = point.shapeArgs;\n                // #15547\n                if (point.dlBox && shapeArgs && !isNumber(shapeArgs.width)) {\n                    shapeArgs = point.dlBox;\n                }\n                width = relativeLength(targetOptions.width, shapeArgs.width);\n                y = series.yAxis.translate(targetVal, false, true, false, true) - targetOptions.height / 2 - 0.5;\n                targetShapeArgs = series.crispCol.apply({\n                    // Use fake series object to set borderWidth of target\n                    chart: chart,\n                    borderWidth: targetOptions.borderWidth,\n                    options: {\n                        crisp: options.crisp\n                    }\n                }, [\n                    (shapeArgs.x +\n                        shapeArgs.width / 2 - width / 2),\n                    y,\n                    width,\n                    height\n                ]);\n                if (targetGraphic) {\n                    // Update\n                    targetGraphic[chart.pointCount < animationLimit ?\n                        'animate' :\n                        'attr'](targetShapeArgs);\n                    // Add or remove tooltip reference\n                    if (isNumber(pointVal) && pointVal !== null) {\n                        targetGraphic.element.point = point;\n                    }\n                    else {\n                        targetGraphic.element.point = void 0;\n                    }\n                }\n                else {\n                    point.targetGraphic = targetGraphic = chart.renderer\n                        .rect()\n                        .attr(targetShapeArgs)\n                        .add(series.group);\n                }\n                // Presentational\n                if (!chart.styledMode) {\n                    targetGraphic.attr({\n                        fill: pick(targetOptions.color, pointOptions.color, (series.zones.length && (point.getZone.call({\n                            series: series,\n                            x: point.x,\n                            y: targetVal,\n                            options: {}\n                        }).color || series.color)) || void 0, point.color, series.color),\n                        stroke: pick(targetOptions.borderColor, point.borderColor, series.options.borderColor),\n                        'stroke-width': targetOptions.borderWidth,\n                        r: targetOptions.borderRadius\n                    });\n                }\n                // Add tooltip reference\n                if (isNumber(pointVal) && pointVal !== null) {\n                    targetGraphic.element.point = point;\n                }\n                targetGraphic.addClass(point.getClassName() +\n                    ' highcharts-bullet-target', true);\n            }\n            else if (targetGraphic) {\n                // #1269:\n                point.targetGraphic = targetGraphic.destroy();\n            }\n        }\n    }\n    /**\n     * Includes target values to extend extremes from y values.\n     *\n     * @ignore\n     * @function Highcharts.Series#getExtremes\n     */\n    getExtremes(yData) {\n        const dataExtremes = super.getExtremes.call(this, yData), targetData = this.targetData;\n        if (targetData && targetData.length) {\n            const targetExtremes = super.getExtremes.call(this, targetData);\n            if (isNumber(targetExtremes.dataMin)) {\n                dataExtremes.dataMin = Math.min(pick(dataExtremes.dataMin, Infinity), targetExtremes.dataMin);\n            }\n            if (isNumber(targetExtremes.dataMax)) {\n                dataExtremes.dataMax = Math.max(pick(dataExtremes.dataMax, -Infinity), targetExtremes.dataMax);\n            }\n        }\n        return dataExtremes;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBulletSeries.defaultOptions = merge((highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default()).defaultOptions, Bullet_BulletSeriesDefaults);\nextend(BulletSeries.prototype, {\n    parallelArrays: ['x', 'y', 'target'],\n    pointArrayMap: ['y', 'target']\n});\nBulletSeries.prototype.pointClass = Bullet_BulletPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('bullet', BulletSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bullet_BulletSeries = ((/* unused pure expression or super */ null && (BulletSeries)));\n\n;// ./code/es-modules/masters/modules/bullet.js\n\n\n\n\n/* harmony default export */ const bullet_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__448__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "bullet_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_", "highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_default", "BulletPoint", "pointClass", "destroy", "series", "targetGraphic", "apply", "arguments", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "extend", "isNumber", "merge", "pick", "<PERSON><PERSON><PERSON><PERSON>", "BulletSeries", "drawPoints", "chart", "options", "animationLimit", "point", "points", "pointOptions", "targetVal", "target", "pointVal", "y", "targetShapeArgs", "width", "height", "targetOptions", "shapeArgs", "dlBox", "yAxis", "translate", "crispCol", "borderWidth", "crisp", "x", "pointCount", "element", "renderer", "rect", "attr", "add", "group", "styledMode", "fill", "color", "zones", "length", "getZone", "stroke", "borderColor", "r", "borderRadius", "addClass", "getClassName", "getExtremes", "yData", "dataExtremes", "targetData", "targetExtremes", "dataMin", "Math", "min", "Infinity", "dataMax", "max", "defaultOptions", "tooltip", "pointFormat", "parallelArrays", "pointArrayMap", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC9H,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,MAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAACA,EAAK,cAAiB,CAAE,GACzJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE3JA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACxI,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuJzB,EAAoB,KAC3K0B,EAA2K1B,EAAoBI,CAAC,CAACqB,EAkBrM,OAAME,UAAoB,AAACD,IAA+JR,SAAS,CAACU,UAAU,CAU1MC,SAAU,CAEFC,AADW,IAAI,CACRC,aAAa,EACpBD,CAAAA,AAFW,IAAI,CAERC,aAAa,CAAGD,AAFZ,IAAI,CAEeC,aAAa,CAACF,OAAO,EAAC,EAExD,KAAK,CAACA,QAAQG,KAAK,CAJJ,IAAI,CAISC,UAEhC,CACJ,CAkMA,IAAIC,EAAmIlC,EAAoB,KACvJmC,EAAuJnC,EAAoBI,CAAC,CAAC8B,GAiBjL,GAAM,CAAEE,OAAAA,CAAM,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,eAAAA,CAAc,CAAE,CAAIhB,GAe3D,OAAMiB,UAAsBf,IAcxBgB,YAAa,CACT,IAAqBC,EAAQb,AAAd,IAAI,CAAiBa,KAAK,CAAEC,EAAUd,AAAtC,IAAI,CAAyCc,OAAO,CAAEC,EAAiBD,EAAQC,cAAc,EAAI,IAEhH,IAAK,IAAMC,KADX,KAAK,CAACJ,WAAWV,KAAK,CAAC,IAAI,CAAEC,WACTH,AAFL,IAAI,CAEQiB,MAAM,EAAE,CAC/B,IAAMC,EAAeF,EAAMF,OAAO,CAAEK,EAAYH,EAAMI,MAAM,CAAEC,EAAWL,EAAMM,CAAC,CAC5EC,EAAiBtB,EAAgBe,EAAMf,aAAa,CAAEuB,EAAOC,EAAQC,EAAeJ,EACxF,GAAIf,EAASY,IAAcA,AAAc,OAAdA,EAAoB,CAE3CM,EAASC,AADTA,CAAAA,EAAgBlB,EAAMM,EAAQY,aAAa,CAAER,EAAaQ,aAAa,CAAA,EAChDD,MAAM,CAC7B,IAAIE,EAAYX,EAAMW,SAAS,AAE3BX,CAAAA,EAAMY,KAAK,EAAID,GAAa,CAACpB,EAASoB,EAAUH,KAAK,GACrDG,CAAAA,EAAYX,EAAMY,KAAK,AAAD,EAE1BJ,EAAQd,EAAegB,EAAcF,KAAK,CAAEG,EAAUH,KAAK,EAC3DF,EAAItB,AAdG,IAAI,CAcA6B,KAAK,CAACC,SAAS,CAACX,EAAW,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,GAAQO,EAAcD,MAAM,CAAG,EAAI,GAC7FF,EAAkBvB,AAfX,IAAI,CAec+B,QAAQ,CAAC7B,KAAK,CAAC,CAEpCW,MAAOA,EACPmB,YAAaN,EAAcM,WAAW,CACtClB,QAAS,CACLmB,MAAOnB,EAAQmB,KAAK,AACxB,CACJ,EAAG,CACEN,EAAUO,CAAC,CACRP,EAAUH,KAAK,CAAG,EAAIA,EAAQ,EAClCF,EACAE,EACAC,EACH,EACGxB,GAEAA,CAAa,CAACY,EAAMsB,UAAU,CAAGpB,EAC7B,UACA,OAAO,CAACQ,GAERhB,EAASc,IAAaA,AAAa,OAAbA,EACtBpB,EAAcmC,OAAO,CAACpB,KAAK,CAAGA,EAG9Bf,EAAcmC,OAAO,CAACpB,KAAK,CAAG,KAAK,GAIvCA,EAAMf,aAAa,CAAGA,EAAgBY,EAAMwB,QAAQ,CAC/CC,IAAI,GACJC,IAAI,CAAChB,GACLiB,GAAG,CAACxC,AA9CN,IAAI,CA8CSyC,KAAK,EAGpB5B,EAAM6B,UAAU,EACjBzC,EAAcsC,IAAI,CAAC,CACfI,KAAMlC,EAAKiB,EAAckB,KAAK,CAAE1B,EAAa0B,KAAK,CAAE,AAAC5C,AAnDtD,IAAI,CAmDyD6C,KAAK,CAACC,MAAM,EAAK9B,CAAAA,EAAM+B,OAAO,CAACzD,IAAI,CAAC,CAC5FU,OApDL,IAAI,CAqDCkC,EAAGlB,EAAMkB,CAAC,CACVZ,EAAGH,EACHL,QAAS,CAAC,CACd,GAAG8B,KAAK,EAAI5C,AAxDb,IAAI,CAwDgB4C,KAAK,AAAD,GAAO,KAAK,EAAG5B,EAAM4B,KAAK,CAAE5C,AAxDpD,IAAI,CAwDuD4C,KAAK,EAC/DI,OAAQvC,EAAKiB,EAAcuB,WAAW,CAAEjC,EAAMiC,WAAW,CAAEjD,AAzD5D,IAAI,CAyD+Dc,OAAO,CAACmC,WAAW,EACrF,eAAgBvB,EAAcM,WAAW,CACzCkB,EAAGxB,EAAcyB,YAAY,AACjC,GAGA5C,EAASc,IAAaA,AAAa,OAAbA,GACtBpB,CAAAA,EAAcmC,OAAO,CAACpB,KAAK,CAAGA,CAAI,EAEtCf,EAAcmD,QAAQ,CAACpC,EAAMqC,YAAY,GACrC,4BAA6B,CAAA,EACrC,MACSpD,GAELe,CAAAA,EAAMf,aAAa,CAAGA,EAAcF,OAAO,EAAC,CAEpD,CACJ,CAOAuD,YAAYC,CAAK,CAAE,CACf,IAAMC,EAAe,KAAK,CAACF,YAAYhE,IAAI,CAAC,IAAI,CAAEiE,GAAQE,EAAa,IAAI,CAACA,UAAU,CACtF,GAAIA,GAAcA,EAAWX,MAAM,CAAE,CACjC,IAAMY,EAAiB,KAAK,CAACJ,YAAYhE,IAAI,CAAC,IAAI,CAAEmE,GAChDlD,EAASmD,EAAeC,OAAO,GAC/BH,CAAAA,EAAaG,OAAO,CAAGC,KAAKC,GAAG,CAACpD,EAAK+C,EAAaG,OAAO,CAAEG,KAAWJ,EAAeC,OAAO,CAAA,EAE5FpD,EAASmD,EAAeK,OAAO,GAC/BP,CAAAA,EAAaO,OAAO,CAAGH,KAAKI,GAAG,CAACvD,EAAK+C,EAAaO,OAAO,CAAE,CAACD,KAAWJ,EAAeK,OAAO,CAAA,CAErG,CACA,OAAOP,CACX,CACJ,CAMA7C,EAAasD,cAAc,CAAGzD,EAAM,AAACZ,IAA+JqE,cAAc,CAlTrL,CAMzBvC,cAAe,CAQXF,MAAO,OAMPC,OAAQ,EAkCRO,YAAa,EAIbmB,aAAc,CAClB,EACAe,QAAS,CACLC,YAAa,iHAGjB,CACJ,GAkPA7D,EAAOK,EAAavB,SAAS,CAAE,CAC3BgF,eAAgB,CAAC,IAAK,IAAK,SAAS,CACpCC,cAAe,CAAC,IAAK,SAAS,AAClC,GACA1D,EAAavB,SAAS,CAACU,UAAU,CArVwBD,EAsVzDQ,IAA0IiE,kBAAkB,CAAC,SAAU3D,GAa1I,IAAMnB,EAAeE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}