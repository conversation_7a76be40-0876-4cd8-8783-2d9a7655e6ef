{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/color-axis\n * @requires highcharts\n *\n * ColorAxis module\n *\n * (c) 2012-2025 Pawel <PERSON>ek\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"LegendSymbol\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/coloraxis\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Axis\"],amd1[\"Color\"],amd1[\"LegendSymbol\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/coloraxis\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"LegendSymbol\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"LegendSymbol\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__500__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 500:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__500__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ coloraxis_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es-modules/Core/Axis/Color/ColorAxisComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { addEvent, extend, merge, pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorAxisComposition;\n(function (ColorAxisComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let ColorAxisConstructor;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(ColorAxisClass, ChartClass, FxClass, LegendClass, SeriesClass) {\n        const chartProto = ChartClass.prototype, fxProto = FxClass.prototype, seriesProto = SeriesClass.prototype;\n        if (!chartProto.collectionsWithUpdate.includes('colorAxis')) {\n            ColorAxisConstructor = ColorAxisClass;\n            chartProto.collectionsWithUpdate.push('colorAxis');\n            chartProto.collectionsWithInit.colorAxis = [\n                chartProto.addColorAxis\n            ];\n            addEvent(ChartClass, 'afterCreateAxes', onChartAfterCreateAxes);\n            wrapChartCreateAxis(ChartClass);\n            fxProto.fillSetter = wrapFxFillSetter;\n            fxProto.strokeSetter = wrapFxStrokeSetter;\n            addEvent(LegendClass, 'afterGetAllItems', onLegendAfterGetAllItems);\n            addEvent(LegendClass, 'afterColorizeItem', onLegendAfterColorizeItem);\n            addEvent(LegendClass, 'afterUpdate', onLegendAfterUpdate);\n            extend(seriesProto, {\n                optionalAxis: 'colorAxis',\n                translateColors: seriesTranslateColors\n            });\n            extend(seriesProto.pointClass.prototype, {\n                setVisible: pointSetVisible\n            });\n            addEvent(SeriesClass, 'afterTranslate', onSeriesAfterTranslate, { order: 1 });\n            addEvent(SeriesClass, 'bindAxes', onSeriesBindAxes);\n        }\n    }\n    ColorAxisComposition.compose = compose;\n    /**\n     * Extend the chart createAxes method to also make the color axis.\n     * @private\n     */\n    function onChartAfterCreateAxes() {\n        const { userOptions } = this;\n        this.colorAxis = [];\n        // If a `colorAxis` config is present in the user options (not in a\n        // theme), instanciate it.\n        if (userOptions.colorAxis) {\n            userOptions.colorAxis = splat(userOptions.colorAxis);\n            userOptions.colorAxis.map((axisOptions) => (new ColorAxisConstructor(this, axisOptions)));\n        }\n    }\n    /**\n     * Add the color axis. This also removes the axis' own series to prevent\n     * them from showing up individually.\n     * @private\n     */\n    function onLegendAfterGetAllItems(e) {\n        const colorAxes = this.chart.colorAxis || [], destroyItem = (item) => {\n            const i = e.allItems.indexOf(item);\n            if (i !== -1) {\n                // #15436\n                this.destroyItem(e.allItems[i]);\n                e.allItems.splice(i, 1);\n            }\n        };\n        let colorAxisItems = [], options, i;\n        colorAxes.forEach(function (colorAxis) {\n            options = colorAxis.options;\n            if (options?.showInLegend) {\n                // Data classes\n                if (options.dataClasses && options.visible) {\n                    colorAxisItems = colorAxisItems.concat(colorAxis.getDataClassLegendSymbols());\n                    // Gradient legend\n                }\n                else if (options.visible) {\n                    // Add this axis on top\n                    colorAxisItems.push(colorAxis);\n                }\n                // If dataClasses are defined or showInLegend option is not set\n                // to true, do not add color axis' series to legend.\n                colorAxis.series.forEach(function (series) {\n                    if (!series.options.showInLegend || options.dataClasses) {\n                        if (series.options.legendType === 'point') {\n                            series.points.forEach(function (point) {\n                                destroyItem(point);\n                            });\n                        }\n                        else {\n                            destroyItem(series);\n                        }\n                    }\n                });\n            }\n        });\n        i = colorAxisItems.length;\n        while (i--) {\n            e.allItems.unshift(colorAxisItems[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    function onLegendAfterColorizeItem(e) {\n        if (e.visible && e.item.legendColor) {\n            e.item.legendItem.symbol.attr({\n                fill: e.item.legendColor\n            });\n        }\n    }\n    /**\n     * Updates in the legend need to be reflected in the color axis. (#6888)\n     * @private\n     */\n    function onLegendAfterUpdate(e) {\n        this.chart.colorAxis?.forEach((colorAxis) => {\n            colorAxis.update({}, e.redraw);\n        });\n    }\n    /**\n     * Calculate and set colors for points.\n     * @private\n     */\n    function onSeriesAfterTranslate() {\n        if (this.chart.colorAxis?.length ||\n            this.colorAttribs) {\n            this.translateColors();\n        }\n    }\n    /**\n     * Add colorAxis to series axisTypes.\n     * @private\n     */\n    function onSeriesBindAxes() {\n        const axisTypes = this.axisTypes;\n        if (!axisTypes) {\n            this.axisTypes = ['colorAxis'];\n        }\n        else if (axisTypes.indexOf('colorAxis') === -1) {\n            axisTypes.push('colorAxis');\n        }\n    }\n    /**\n     * Set the visibility of a single point\n     * @private\n     * @function Highcharts.colorPointMixin.setVisible\n     * @param {boolean} visible\n     */\n    function pointSetVisible(vis) {\n        const point = this, method = vis ? 'show' : 'hide';\n        point.visible = point.options.visible = Boolean(vis);\n        // Show and hide associated elements\n        ['graphic', 'dataLabel'].forEach(function (key) {\n            if (point[key]) {\n                point[key][method]();\n            }\n        });\n        this.series.buildKDTree(); // Rebuild kdtree #13195\n    }\n    ColorAxisComposition.pointSetVisible = pointSetVisible;\n    /**\n     * In choropleth maps, the color is a result of the value, so this needs\n     * translation too\n     * @private\n     * @function Highcharts.colorSeriesMixin.translateColors\n     */\n    function seriesTranslateColors() {\n        const series = this, points = this.getPointsCollection(), // #17945\n        nullColor = this.options.nullColor, colorAxis = this.colorAxis, colorKey = this.colorKey;\n        points.forEach((point) => {\n            const value = point.getNestedProperty(colorKey), color = point.options.color || (point.isNull || point.value === null ?\n                nullColor :\n                (colorAxis && typeof value !== 'undefined') ?\n                    colorAxis.toColor(value, point) :\n                    point.color || series.color);\n            if (color && point.color !== color) {\n                point.color = color;\n                if (series.options.legendType === 'point' &&\n                    point.legendItem &&\n                    point.legendItem.label) {\n                    series.chart.legend.colorizeItem(point, point.visible);\n                }\n            }\n        });\n    }\n    /**\n     * @private\n     */\n    function wrapChartCreateAxis(ChartClass) {\n        const superCreateAxis = ChartClass.prototype.createAxis;\n        ChartClass.prototype.createAxis = function (type, options) {\n            const chart = this;\n            if (type !== 'colorAxis') {\n                return superCreateAxis.apply(chart, arguments);\n            }\n            const axis = new ColorAxisConstructor(chart, merge(options.axis, {\n                index: chart[type].length,\n                isX: false\n            }));\n            chart.isDirtyLegend = true;\n            // Clear before 'bindAxes' (#11924)\n            chart.axes.forEach((axis) => {\n                axis.series = [];\n            });\n            chart.series.forEach((series) => {\n                series.bindAxes();\n                series.isDirtyData = true;\n            });\n            if (pick(options.redraw, true)) {\n                chart.redraw(options.animation);\n            }\n            return axis;\n        };\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxFillSetter() {\n        this.elem.attr('fill', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxStrokeSetter() {\n        this.elem.attr('stroke', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n})(ColorAxisComposition || (ColorAxisComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxisComposition = (ColorAxisComposition);\n\n;// ./code/es-modules/Core/Axis/Color/ColorAxisDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A color axis for series. Visually, the color\n * axis will appear as a gradient or as separate items inside the\n * legend, depending on whether the axis is scalar or based on data\n * classes.\n *\n * For supported color formats, see the\n * [docs article about colors](https://www.highcharts.com/docs/chart-design-and-style/colors).\n *\n * A scalar color axis is represented by a gradient. The colors either\n * range between the [minColor](#colorAxis.minColor) and the\n * [maxColor](#colorAxis.maxColor), or for more fine grained control the\n * colors can be defined in [stops](#colorAxis.stops). Often times, the\n * color axis needs to be adjusted to get the right color spread for the\n * data. In addition to stops, consider using a logarithmic\n * [axis type](#colorAxis.type), or setting [min](#colorAxis.min) and\n * [max](#colorAxis.max) to avoid the colors being determined by\n * outliers.\n *\n * When [dataClasses](#colorAxis.dataClasses) are used, the ranges are\n * subdivided into separate classes like categories based on their\n * values. This can be used for ranges between two values, but also for\n * a true category. However, when your data is categorized, it may be as\n * convenient to add each category to a separate series.\n *\n * Color axis does not work with: `sankey`, `sunburst`, `dependencywheel`,\n * `networkgraph`, `wordcloud`, `venn`, `gauge` and `solidgauge` series\n * types.\n *\n * Since v7.2.0 `colorAxis` can also be an array of options objects.\n *\n * See [the Axis object](/class-reference/Highcharts.Axis) for\n * programmatic access to the axis.\n *\n * @sample       {highcharts} highcharts/coloraxis/custom-color-key\n *               Column chart with color axis\n * @sample       {highcharts} highcharts/coloraxis/horizontal-layout\n *               Horizontal layout\n * @sample       {highmaps} maps/coloraxis/dataclasscolor\n *               With data classes\n * @sample       {highmaps} maps/coloraxis/mincolor-maxcolor\n *               Min color and max color\n *\n * @extends      xAxis\n * @excluding    alignTicks, allowDecimals, alternateGridColor, breaks,\n *               categories, crosshair, dateTimeLabelFormats, left,\n *               lineWidth, linkedTo, maxZoom, minRange, minTickInterval,\n *               offset, opposite, pane, plotBands, plotLines,\n *               reversedStacks, scrollbar, showEmpty, title, top,\n *               zoomEnabled\n * @product      highcharts highstock highmaps\n * @type         {*|Array<*>}\n * @optionparent colorAxis\n */\nconst colorAxisDefaults = {\n    /**\n     * Whether to allow decimals on the color axis.\n     * @type      {boolean}\n     * @default   true\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.allowDecimals\n     */\n    /**\n     * Determines how to set each data class' color if no individual\n     * color is set. The default value, `tween`, computes intermediate\n     * colors between `minColor` and `maxColor`. The other possible\n     * value, `category`, pulls colors from the global or chart specific\n     * [colors](#colors) array.\n     *\n     * @sample {highmaps} maps/coloraxis/dataclasscolor/\n     *         Category colors\n     *\n     * @type       {string}\n     * @default    tween\n     * @product    highcharts highstock highmaps\n     * @validvalue [\"tween\", \"category\"]\n     * @apioption  colorAxis.dataClassColor\n     */\n    /**\n     * An array of data classes or ranges for the choropleth map. If\n     * none given, the color axis is scalar and values are distributed\n     * as a gradient between the minimum and maximum colors.\n     *\n     * @sample {highmaps} maps/demo/data-class-ranges/\n     *         Multiple ranges\n     *\n     * @sample {highmaps} maps/demo/data-class-two-ranges/\n     *         Two ranges\n     *\n     * @type      {Array<*>}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses\n     */\n    /**\n     * The layout of the color axis. Can be `'horizontal'` or `'vertical'`.\n     * If none given, the color axis has the same layout as the legend.\n     *\n     * @sample highcharts/coloraxis/horizontal-layout/\n     *         Horizontal color axis layout with vertical legend\n     *\n     * @type      {string|undefined}\n     * @since     7.2.0\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.layout\n     */\n    /**\n     * The color of each data class. If not set, the color is pulled\n     * from the global or chart-specific [colors](#colors) array. In\n     * styled mode, this option is ignored. Instead, use colors defined\n     * in CSS.\n     *\n     * @sample {highmaps} maps/demo/data-class-two-ranges/\n     *         Explicit colors\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.color\n     */\n    /**\n     * The start of the value range that the data class represents,\n     * relating to the point value.\n     *\n     * The range of each `dataClass` is closed in both ends, but can be\n     * overridden by the next `dataClass`.\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.from\n     */\n    /**\n     * The name of the data class as it appears in the legend.\n     * If no name is given, it is automatically created based on the\n     * `from` and `to` values. For full programmatic control,\n     * [legend.labelFormatter](#legend.labelFormatter) can be used.\n     * In the formatter, `this.from` and `this.to` can be accessed.\n     *\n     * @sample {highmaps} maps/coloraxis/dataclasses-name/\n     *         Named data classes\n     *\n     * @sample {highmaps} maps/coloraxis/dataclasses-labelformatter/\n     *         Formatted data classes\n     *\n     * @type      {string}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.name\n     */\n    /**\n     * The end of the value range that the data class represents,\n     * relating to the point value.\n     *\n     * The range of each `dataClass` is closed in both ends, but can be\n     * overridden by the next `dataClass`.\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.dataClasses.to\n     */\n    /** @ignore-option */\n    lineWidth: 0,\n    /**\n     * Padding of the min value relative to the length of the axis. A\n     * padding of 0.05 will make a 100px axis 5px longer.\n     *\n     * @product highcharts highstock highmaps\n     */\n    minPadding: 0,\n    /**\n     * The maximum value of the axis in terms of map point values. If\n     * `null`, the max value is automatically calculated. If the\n     * `endOnTick` option is true, the max value might be rounded up.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Explicit min and max to reduce the effect of outliers\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.max\n     */\n    /**\n     * The minimum value of the axis in terms of map point values. If\n     * `null`, the min value is automatically calculated. If the\n     * `startOnTick` option is true, the min value might be rounded\n     * down.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Explicit min and max to reduce the effect of outliers\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.min\n     */\n    /**\n     * Padding of the max value relative to the length of the axis. A\n     * padding of 0.05 will make a 100px axis 5px longer.\n     *\n     * @product highcharts highstock highmaps\n     */\n    maxPadding: 0,\n    /**\n     * Color of the grid lines extending from the axis across the\n     * gradient.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Grid lines demonstrated\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highcharts highstock highmaps\n     */\n    gridLineColor: \"#ffffff\" /* Palette.backgroundColor */,\n    /**\n     * The width of the grid lines extending from the axis across the\n     * gradient of a scalar color axis.\n     *\n     * @sample {highmaps} maps/coloraxis/gridlines/\n     *         Grid lines demonstrated\n     *\n     * @product highcharts highstock highmaps\n     */\n    gridLineWidth: 1,\n    /**\n     * The interval of the tick marks in axis units. When `null`, the\n     * tick interval is computed to approximately follow the\n     * `tickPixelInterval`.\n     *\n     * @type      {number}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.tickInterval\n     */\n    /**\n     * If [tickInterval](#colorAxis.tickInterval) is `null` this option\n     * sets the approximate pixel interval of the tick marks.\n     *\n     * @product highcharts highstock highmaps\n     */\n    tickPixelInterval: 72,\n    /**\n     * Whether to force the axis to start on a tick. Use this option\n     * with the `maxPadding` option to control the axis start.\n     *\n     * @product highcharts highstock highmaps\n     */\n    startOnTick: true,\n    /**\n     * Whether to force the axis to end on a tick. Use this option with\n     * the [maxPadding](#colorAxis.maxPadding) option to control the\n     * axis end.\n     *\n     * @product highcharts highstock highmaps\n     */\n    endOnTick: true,\n    /** @ignore */\n    offset: 0,\n    /**\n     * The triangular marker on a scalar color axis that points to the\n     * value of the hovered area. To disable the marker, set\n     * `marker: null`.\n     *\n     * @sample {highmaps} maps/coloraxis/marker/\n     *         Black marker\n     *\n     * @declare Highcharts.PointMarkerOptionsObject\n     * @product highcharts highstock highmaps\n     */\n    marker: {\n        /**\n         * Animation for the marker as it moves between values. Set to\n         * `false` to disable animation. Defaults to `{ duration: 50 }`.\n         *\n         * @type    {boolean|Partial<Highcharts.AnimationOptionsObject>}\n         * @product highcharts highstock highmaps\n         */\n        animation: {\n            /** @internal */\n            duration: 50\n        },\n        /** @internal */\n        width: 0.01,\n        /**\n         * The color of the marker.\n         *\n         * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @product highcharts highstock highmaps\n         */\n        color: \"#999999\" /* Palette.neutralColor40 */\n    },\n    /**\n     * The axis labels show the number for each tick.\n     *\n     * For more live examples on label options, see [xAxis.labels in the\n     * Highcharts API.](/highcharts#xAxis.labels)\n     *\n     * @extends xAxis.labels\n     * @product highcharts highstock highmaps\n     */\n    labels: {\n        distance: 8,\n        /**\n         * How to handle overflowing labels on horizontal color axis. If set\n         * to `\"allow\"`, it will not be aligned at all. By default it\n         * `\"justify\"` labels inside the chart area. If there is room to\n         * move it, it will be aligned to the edge, else it will be removed.\n         *\n         * @validvalue [\"allow\", \"justify\"]\n         * @product    highcharts highstock highmaps\n         */\n        overflow: 'justify',\n        rotation: 0\n    },\n    /**\n     * The color to represent the minimum of the color axis. Unless\n     * [dataClasses](#colorAxis.dataClasses) or\n     * [stops](#colorAxis.stops) are set, the gradient starts at this\n     * value.\n     *\n     * If dataClasses are set, the color is based on minColor and\n     * maxColor unless a color is set for each data class, or the\n     * [dataClassColor](#colorAxis.dataClassColor) is set.\n     *\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor/\n     *         Min and max colors on scalar (gradient) axis\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor-dataclasses/\n     *         On data classes\n     *\n     * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product highcharts highstock highmaps\n     */\n    minColor: \"#e6e9ff\" /* Palette.highlightColor10 */,\n    /**\n     * The color to represent the maximum of the color axis. Unless\n     * [dataClasses](#colorAxis.dataClasses) or\n     * [stops](#colorAxis.stops) are set, the gradient ends at this\n     * value.\n     *\n     * If dataClasses are set, the color is based on minColor and\n     * maxColor unless a color is set for each data class, or the\n     * [dataClassColor](#colorAxis.dataClassColor) is set.\n     *\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor/\n     *         Min and max colors on scalar (gradient) axis\n     * @sample {highmaps} maps/coloraxis/mincolor-maxcolor-dataclasses/\n     *         On data classes\n     *\n     * @type    {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product highcharts highstock highmaps\n     */\n    maxColor: \"#0022ff\" /* Palette.highlightColor100 */,\n    /**\n     * Color stops for the gradient of a scalar color axis. Use this in\n     * cases where a linear gradient between a `minColor` and `maxColor`\n     * is not sufficient. The stops is an array of tuples, where the\n     * first item is a float between 0 and 1 assigning the relative\n     * position in the gradient, and the second item is the color.\n     *\n     * @sample highcharts/coloraxis/coloraxis-stops/\n     *         Color axis stops\n     * @sample highcharts/coloraxis/color-key-with-stops/\n     *         Color axis stops with custom colorKey\n     * @sample {highmaps} maps/demo/heatmap/\n     *         Heatmap with three color stops\n     *\n     * @type      {Array<Array<number,Highcharts.ColorString>>}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.stops\n     */\n    /**\n     * The pixel length of the main tick marks on the color axis.\n     */\n    tickLength: 5,\n    /**\n     * The type of interpolation to use for the color axis. Can be\n     * `linear` or `logarithmic`.\n     *\n     * @sample highcharts/coloraxis/logarithmic-with-emulate-negative-values/\n     *         Logarithmic color axis with extension to emulate negative\n     *         values\n     *\n     * @type      {Highcharts.ColorAxisTypeValue}\n     * @default   linear\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.type\n     */\n    /**\n     * Whether to reverse the axis so that the highest number is closest\n     * to the origin. Defaults to `false` in a horizontal legend and\n     * `true` in a vertical legend, where the smallest value starts on\n     * top.\n     *\n     * @type      {boolean}\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.reversed\n     */\n    /**\n     * @product   highcharts highstock highmaps\n     * @excluding afterBreaks, pointBreak, pointInBreak\n     * @apioption colorAxis.events\n     */\n    /**\n     * Fires when the legend item belonging to the colorAxis is clicked.\n     * One parameter, `event`, is passed to the function.\n     *\n     * **Note:** This option is deprecated in favor of\n     * [legend.events.itemClick](#legend.events.itemClick).\n     *\n     * @deprecated 11.4.4\n     * @type       {Function}\n     * @product    highcharts highstock highmaps\n     * @apioption  colorAxis.events.legendItemClick\n     */\n    /**\n     * The width of the color axis. If it's a number, it is interpreted as\n     * pixels.\n     *\n     * If it's a percentage string, it is interpreted as percentages of the\n     * total plot width.\n     *\n     * @sample    highcharts/coloraxis/width-and-height\n     *            Percentage width and pixel height for color axis\n     *\n     * @type      {number|string}\n     * @since     11.3.0\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.width\n     */\n    /**\n     * The height of the color axis. If it's a number, it is interpreted as\n     * pixels.\n     *\n     * If it's a percentage string, it is interpreted as percentages of the\n     * total plot height.\n     *\n     * @sample    highcharts/coloraxis/width-and-height\n     *            Percentage width and pixel height for color axis\n     *\n     * @type      {number|string}\n     * @since     11.3.0\n     * @product   highcharts highstock highmaps\n     * @apioption colorAxis.height\n     */\n    /**\n     * Whether to display the colorAxis in the legend.\n     *\n     * @sample highcharts/coloraxis/hidden-coloraxis-with-3d-chart/\n     *         Hidden color axis with 3d chart\n     *\n     * @see [heatmap.showInLegend](#series.heatmap.showInLegend)\n     *\n     * @since   4.2.7\n     * @product highcharts highstock highmaps\n     */\n    showInLegend: true\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ColorAxisDefaults = (colorAxisDefaults);\n\n;// ./code/es-modules/Core/Axis/Color/ColorAxisLike.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: ColorAxisLike_color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { merge: ColorAxisLike_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Namespace\n *\n * */\nvar ColorAxisLike;\n(function (ColorAxisLike) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize defined data classes.\n     * @private\n     */\n    function initDataClasses(userOptions) {\n        const axis = this, chart = axis.chart, legendItem = axis.legendItem = axis.legendItem || {}, options = axis.options, userDataClasses = userOptions.dataClasses || [];\n        let dataClass, dataClasses, colorCount = chart.options.chart.colorCount, colorCounter = 0, colors;\n        axis.dataClasses = dataClasses = [];\n        legendItem.labels = [];\n        for (let i = 0, iEnd = userDataClasses.length; i < iEnd; ++i) {\n            dataClass = userDataClasses[i];\n            dataClass = ColorAxisLike_merge(dataClass);\n            dataClasses.push(dataClass);\n            if (!chart.styledMode && dataClass.color) {\n                continue;\n            }\n            if (options.dataClassColor === 'category') {\n                if (!chart.styledMode) {\n                    colors = chart.options.colors || [];\n                    colorCount = colors.length;\n                    dataClass.color = colors[colorCounter];\n                }\n                dataClass.colorIndex = colorCounter;\n                // Loop back to zero\n                colorCounter++;\n                if (colorCounter === colorCount) {\n                    colorCounter = 0;\n                }\n            }\n            else {\n                dataClass.color = ColorAxisLike_color(options.minColor).tweenTo(ColorAxisLike_color(options.maxColor), iEnd < 2 ? 0.5 : i / (iEnd - 1) // #3219\n                );\n            }\n        }\n    }\n    ColorAxisLike.initDataClasses = initDataClasses;\n    /**\n     * Create initial color stops.\n     * @private\n     */\n    function initStops() {\n        const axis = this, options = axis.options, stops = axis.stops = options.stops || [\n            [0, options.minColor || ''],\n            [1, options.maxColor || '']\n        ];\n        for (let i = 0, iEnd = stops.length; i < iEnd; ++i) {\n            stops[i].color = ColorAxisLike_color(stops[i][1]);\n        }\n    }\n    ColorAxisLike.initStops = initStops;\n    /**\n     * Normalize logarithmic values.\n     * @private\n     */\n    function normalizedValue(value) {\n        const axis = this, max = axis.max || 0, min = axis.min || 0;\n        if (axis.logarithmic) {\n            value = axis.logarithmic.log2lin(value);\n        }\n        return 1 - ((max - value) /\n            ((max - min) || 1));\n    }\n    ColorAxisLike.normalizedValue = normalizedValue;\n    /**\n     * Translate from a value to a color.\n     * @private\n     */\n    function toColor(value, point) {\n        const axis = this;\n        const dataClasses = axis.dataClasses;\n        const stops = axis.stops;\n        let pos, from, to, color, dataClass, i;\n        if (dataClasses) {\n            i = dataClasses.length;\n            while (i--) {\n                dataClass = dataClasses[i];\n                from = dataClass.from;\n                to = dataClass.to;\n                if ((typeof from === 'undefined' || value >= from) &&\n                    (typeof to === 'undefined' || value <= to)) {\n                    color = dataClass.color;\n                    if (point) {\n                        point.dataClass = i;\n                        point.colorIndex = dataClass.colorIndex;\n                    }\n                    break;\n                }\n            }\n        }\n        else {\n            pos = axis.normalizedValue(value);\n            i = stops.length;\n            while (i--) {\n                if (pos > stops[i][0]) {\n                    break;\n                }\n            }\n            from = stops[i] || stops[i + 1];\n            to = stops[i + 1] || from;\n            // The position within the gradient\n            pos = 1 - (to[0] - pos) / ((to[0] - from[0]) || 1);\n            color = from.color.tweenTo(to.color, pos);\n        }\n        return color;\n    }\n    ColorAxisLike.toColor = toColor;\n})(ColorAxisLike || (ColorAxisLike = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxisLike = (ColorAxisLike);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"LegendSymbol\"],\"commonjs\":[\"highcharts\",\"LegendSymbol\"],\"commonjs2\":[\"highcharts\",\"LegendSymbol\"],\"root\":[\"Highcharts\",\"LegendSymbol\"]}\nvar highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_ = __webpack_require__(500);\nvar highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_default = /*#__PURE__*/__webpack_require__.n(highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Core/Axis/Color/ColorAxis.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { defaultOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { series: Series } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { defined, extend: ColorAxis_extend, fireEvent, isArray, isNumber, merge: ColorAxis_merge, pick: ColorAxis_pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\ndefaultOptions.colorAxis = ColorAxis_merge(defaultOptions.xAxis, ColorAxisDefaults);\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ColorAxis object for inclusion in gradient legends.\n *\n * @class\n * @name Highcharts.ColorAxis\n * @augments Highcharts.Axis\n *\n * @param {Highcharts.Chart} chart\n * The related chart of the color axis.\n *\n * @param {Highcharts.ColorAxisOptions} userOptions\n * The color axis options for initialization.\n */\nclass ColorAxis extends (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()) {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass, FxClass, LegendClass, SeriesClass) {\n        Color_ColorAxisComposition.compose(ColorAxis, ChartClass, FxClass, LegendClass, SeriesClass);\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    /**\n     * @private\n     */\n    constructor(chart, userOptions) {\n        super(chart, userOptions);\n        this.coll = 'colorAxis';\n        this.visible = true;\n        this.init(chart, userOptions);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initializes the color axis.\n     *\n     * @function Highcharts.ColorAxis#init\n     *\n     * @param {Highcharts.Chart} chart\n     * The related chart of the color axis.\n     *\n     * @param {Highcharts.ColorAxisOptions} userOptions\n     * The color axis options for initialization.\n     */\n    init(chart, userOptions) {\n        const axis = this;\n        const legend = chart.options.legend || {}, horiz = userOptions.layout ?\n            userOptions.layout !== 'vertical' :\n            legend.layout !== 'vertical';\n        axis.side = userOptions.side || horiz ? 2 : 1;\n        axis.reversed = userOptions.reversed || !horiz;\n        axis.opposite = !horiz;\n        super.init(chart, userOptions, 'colorAxis');\n        // `super.init` saves the extended user options, now replace it with the\n        // originals\n        this.userOptions = userOptions;\n        if (isArray(chart.userOptions.colorAxis)) {\n            chart.userOptions.colorAxis[this.index] = userOptions;\n        }\n        // Prepare data classes\n        if (userOptions.dataClasses) {\n            axis.initDataClasses(userOptions);\n        }\n        axis.initStops();\n        // Override original axis properties\n        axis.horiz = horiz;\n        axis.zoomEnabled = false;\n    }\n    /**\n     * Returns true if the series has points at all.\n     *\n     * @function Highcharts.ColorAxis#hasData\n     *\n     * @return {boolean}\n     * True, if the series has points, otherwise false.\n     */\n    hasData() {\n        return !!(this.tickPositions || []).length;\n    }\n    /**\n     * Override so that ticks are not added in data class axes (#6914)\n     * @private\n     */\n    setTickPositions() {\n        if (!this.dataClasses) {\n            return super.setTickPositions();\n        }\n    }\n    /**\n     * Extend the setOptions method to process extreme colors and color stops.\n     * @private\n     */\n    setOptions(userOptions) {\n        const options = ColorAxis_merge(defaultOptions.colorAxis, userOptions, \n        // Forced options\n        {\n            showEmpty: false,\n            title: null,\n            visible: this.chart.options.legend.enabled &&\n                userOptions.visible !== false\n        });\n        super.setOptions(options);\n        this.options.crosshair = this.options.marker;\n    }\n    /**\n     * @private\n     */\n    setAxisSize() {\n        const axis = this, chart = axis.chart, symbol = axis.legendItem?.symbol;\n        let { width, height } = axis.getSize();\n        if (symbol) {\n            this.left = +symbol.attr('x');\n            this.top = +symbol.attr('y');\n            this.width = width = +symbol.attr('width');\n            this.height = height = +symbol.attr('height');\n            this.right = chart.chartWidth - this.left - width;\n            this.bottom = chart.chartHeight - this.top - height;\n            this.pos = this.horiz ? this.left : this.top;\n        }\n        // Fake length for disabled legend to avoid tick issues\n        // and such (#5205)\n        this.len = (this.horiz ? width : height) ||\n            ColorAxis.defaultLegendLength;\n    }\n    /**\n     * Override the getOffset method to add the whole axis groups inside the\n     * legend.\n     * @private\n     */\n    getOffset() {\n        const axis = this;\n        const group = axis.legendItem?.group;\n        const sideOffset = axis.chart.axisOffset[axis.side];\n        if (group) {\n            // Hook for the getOffset method to add groups to this parent\n            // group\n            axis.axisParent = group;\n            // Call the base\n            super.getOffset();\n            const legend = this.chart.legend;\n            // Adds `maxLabelLength` needed for label padding corrections done\n            // by `render()` and `getMargins()` (#15551).\n            legend.allItems.forEach(function (item) {\n                if (item instanceof ColorAxis) {\n                    item.drawLegendSymbol(legend, item);\n                }\n            });\n            legend.render();\n            this.chart.getMargins(true);\n            // If not drilling down/up\n            if (!this.chart.series.some((series) => series.isDrilling)) {\n                axis.isDirty = true; // Flag to fire drawChartBox\n            }\n            // First time only\n            if (!axis.added) {\n                axis.added = true;\n                axis.labelLeft = 0;\n                axis.labelRight = axis.width;\n            }\n            // Reset it to avoid color axis reserving space\n            axis.chart.axisOffset[axis.side] = sideOffset;\n        }\n    }\n    /**\n     * Create the color gradient.\n     * @private\n     */\n    setLegendColor() {\n        const axis = this;\n        const horiz = axis.horiz;\n        const reversed = axis.reversed;\n        const one = reversed ? 1 : 0;\n        const zero = reversed ? 0 : 1;\n        const grad = horiz ? [one, 0, zero, 0] : [0, zero, 0, one]; // #3190\n        axis.legendColor = {\n            linearGradient: {\n                x1: grad[0],\n                y1: grad[1],\n                x2: grad[2],\n                y2: grad[3]\n            },\n            stops: axis.stops\n        };\n    }\n    /**\n     * The color axis appears inside the legend and has its own legend symbol.\n     * @private\n     */\n    drawLegendSymbol(legend, item) {\n        const axis = this, legendItem = item.legendItem || {}, padding = legend.padding, legendOptions = legend.options, labelOptions = axis.options.labels, itemDistance = ColorAxis_pick(legendOptions.itemDistance, 10), horiz = axis.horiz, { width, height } = axis.getSize(), labelPadding = ColorAxis_pick(\n        // @todo: This option is not documented, nor implemented when\n        // vertical\n        legendOptions.labelPadding, horiz ? 16 : 30);\n        this.setLegendColor();\n        // Create the gradient\n        if (!legendItem.symbol) {\n            legendItem.symbol = this.chart.renderer.symbol('roundedRect')\n                .attr({\n                r: legendOptions.symbolRadius ?? 3,\n                zIndex: 1\n            }).add(legendItem.group);\n        }\n        legendItem.symbol.attr({\n            x: 0,\n            y: (legend.baseline || 0) - 11,\n            width: width,\n            height: height\n        });\n        // Set how much space this legend item takes up\n        legendItem.labelWidth = (width +\n            padding +\n            (horiz ?\n                itemDistance :\n                ColorAxis_pick(labelOptions.x, labelOptions.distance) +\n                    (this.maxLabelLength || 0)));\n        legendItem.labelHeight = height + padding + (horiz ? labelPadding : 0);\n    }\n    /**\n     * Fool the legend.\n     * @private\n     */\n    setState(state) {\n        this.series.forEach(function (series) {\n            series.setState(state);\n        });\n    }\n    /**\n     * @private\n     */\n    setVisible() {\n    }\n    /**\n     * @private\n     */\n    getSeriesExtremes() {\n        const axis = this;\n        const series = axis.series;\n        let colorValArray, colorKey, calculatedExtremes, cSeries, i = series.length;\n        this.dataMin = Infinity;\n        this.dataMax = -Infinity;\n        while (i--) { // X, y, value, other\n            cSeries = series[i];\n            colorKey = cSeries.colorKey = ColorAxis_pick(cSeries.options.colorKey, cSeries.colorKey, cSeries.pointValKey, cSeries.zoneAxis, 'y');\n            calculatedExtremes = cSeries[colorKey + 'Min'] &&\n                cSeries[colorKey + 'Max'];\n            // Find the first column that has values\n            for (const key of [colorKey, 'value', 'y']) {\n                colorValArray = cSeries.getColumn(key);\n                if (colorValArray.length) {\n                    break;\n                }\n            }\n            // If color key extremes are already calculated, use them.\n            if (calculatedExtremes) {\n                cSeries.minColorValue = cSeries[colorKey + 'Min'];\n                cSeries.maxColorValue = cSeries[colorKey + 'Max'];\n            }\n            else {\n                const cExtremes = Series.prototype.getExtremes.call(cSeries, colorValArray);\n                cSeries.minColorValue = cExtremes.dataMin;\n                cSeries.maxColorValue = cExtremes.dataMax;\n            }\n            if (defined(cSeries.minColorValue) &&\n                defined(cSeries.maxColorValue)) {\n                this.dataMin =\n                    Math.min(this.dataMin, cSeries.minColorValue);\n                this.dataMax =\n                    Math.max(this.dataMax, cSeries.maxColorValue);\n            }\n            if (!calculatedExtremes) {\n                Series.prototype.applyExtremes.call(cSeries);\n            }\n        }\n    }\n    /**\n     * Internal function to draw a crosshair.\n     *\n     * @function Highcharts.ColorAxis#drawCrosshair\n     *\n     * @param {Highcharts.PointerEventObject} [e]\n     *        The event arguments from the modified pointer event, extended with\n     *        `chartX` and `chartY`\n     *\n     * @param {Highcharts.Point} [point]\n     *        The Point object if the crosshair snaps to points.\n     *\n     * @emits Highcharts.ColorAxis#event:afterDrawCrosshair\n     * @emits Highcharts.ColorAxis#event:drawCrosshair\n     */\n    drawCrosshair(e, point) {\n        const axis = this, legendItem = axis.legendItem || {}, plotX = point?.plotX, plotY = point?.plotY, axisPos = axis.pos, axisLen = axis.len;\n        let crossPos;\n        if (point) {\n            crossPos = axis.toPixels(point.getNestedProperty(point.series.colorKey));\n            if (crossPos < axisPos) {\n                crossPos = axisPos - 2;\n            }\n            else if (crossPos > axisPos + axisLen) {\n                crossPos = axisPos + axisLen + 2;\n            }\n            point.plotX = crossPos;\n            point.plotY = axis.len - crossPos;\n            super.drawCrosshair(e, point);\n            point.plotX = plotX;\n            point.plotY = plotY;\n            if (axis.cross &&\n                !axis.cross.addedToColorAxis &&\n                legendItem.group) {\n                axis.cross\n                    .addClass('highcharts-coloraxis-marker')\n                    .add(legendItem.group);\n                axis.cross.addedToColorAxis = true;\n                if (!axis.chart.styledMode &&\n                    typeof axis.crosshair === 'object') {\n                    axis.cross.attr({\n                        fill: axis.crosshair.color\n                    });\n                }\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    getPlotLinePath(options) {\n        const axis = this, left = axis.left, pos = options.translatedValue, top = axis.top;\n        // Crosshairs only\n        return isNumber(pos) ? // `pos` can be 0 (#3969)\n            (axis.horiz ? [\n                ['M', pos - 4, top - 6],\n                ['L', pos + 4, top - 6],\n                ['L', pos, top],\n                ['Z']\n            ] : [\n                ['M', left, pos],\n                ['L', left - 6, pos + 6],\n                ['L', left - 6, pos - 6],\n                ['Z']\n            ]) :\n            super.getPlotLinePath(options);\n    }\n    /**\n     * Updates a color axis instance with a new set of options. The options are\n     * merged with the existing options, so only new or altered options need to\n     * be specified.\n     *\n     * @function Highcharts.ColorAxis#update\n     *\n     * @param {Highcharts.ColorAxisOptions} newOptions\n     * The new options that will be merged in with existing options on the color\n     * axis.\n     *\n     * @param {boolean} [redraw]\n     * Whether to redraw the chart after the color axis is altered. If doing\n     * more operations on the chart, it is a good idea to set redraw to `false`\n     * and call {@link Highcharts.Chart#redraw} after.\n     */\n    update(newOptions, redraw) {\n        const axis = this, chart = axis.chart, legend = chart.legend;\n        this.series.forEach((series) => {\n            // Needed for Axis.update when choropleth colors change\n            series.isDirtyData = true;\n        });\n        // When updating data classes, destroy old items and make sure new\n        // ones are created (#3207)\n        if (newOptions.dataClasses && legend.allItems || axis.dataClasses) {\n            axis.destroyItems();\n        }\n        super.update(newOptions, redraw);\n        if (axis.legendItem?.label) {\n            axis.setLegendColor();\n            legend.colorizeItem(this, true);\n        }\n    }\n    /**\n     * Destroy color axis legend items.\n     * @private\n     */\n    destroyItems() {\n        const axis = this, chart = axis.chart, legendItem = axis.legendItem || {};\n        if (legendItem.label) {\n            chart.legend.destroyItem(axis);\n        }\n        else if (legendItem.labels) {\n            for (const item of legendItem.labels) {\n                chart.legend.destroyItem(item);\n            }\n        }\n        chart.isDirtyLegend = true;\n    }\n    //   Removing the whole axis (#14283)\n    destroy() {\n        this.chart.isDirtyLegend = true;\n        this.destroyItems();\n        super.destroy(...[].slice.call(arguments));\n    }\n    /**\n     * Removes the color axis and the related legend item.\n     *\n     * @function Highcharts.ColorAxis#remove\n     *\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart following the remove.\n     */\n    remove(redraw) {\n        this.destroyItems();\n        super.remove(redraw);\n    }\n    /**\n     * Get the legend item symbols for data classes.\n     * @private\n     */\n    getDataClassLegendSymbols() {\n        const axis = this, chart = axis.chart, legendItems = (axis.legendItem &&\n            axis.legendItem.labels ||\n            []), legendOptions = chart.options.legend, valueDecimals = ColorAxis_pick(legendOptions.valueDecimals, -1), valueSuffix = ColorAxis_pick(legendOptions.valueSuffix, '');\n        const getPointsInDataClass = (i) => axis.series.reduce((points, s) => {\n            points.push(...s.points.filter((point) => point.dataClass === i));\n            return points;\n        }, []);\n        let name;\n        if (!legendItems.length) {\n            axis.dataClasses.forEach((dataClass, i) => {\n                const from = dataClass.from, to = dataClass.to, { numberFormatter } = chart;\n                let vis = true;\n                // Assemble the default name. This can be overridden\n                // by legend.options.labelFormatter\n                name = '';\n                if (typeof from === 'undefined') {\n                    name = '< ';\n                }\n                else if (typeof to === 'undefined') {\n                    name = '> ';\n                }\n                if (typeof from !== 'undefined') {\n                    name += numberFormatter(from, valueDecimals) + valueSuffix;\n                }\n                if (typeof from !== 'undefined' && typeof to !== 'undefined') {\n                    name += ' - ';\n                }\n                if (typeof to !== 'undefined') {\n                    name += numberFormatter(to, valueDecimals) + valueSuffix;\n                }\n                // Add a mock object to the legend items\n                legendItems.push(ColorAxis_extend({\n                    chart,\n                    name,\n                    options: {},\n                    drawLegendSymbol: (highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_default()).rectangle,\n                    visible: true,\n                    isDataClass: true,\n                    // Override setState to set either normal or inactive\n                    // state to all points in this data class\n                    setState: (state) => {\n                        for (const point of getPointsInDataClass(i)) {\n                            point.setState(state);\n                        }\n                    },\n                    // Override setState to show or hide all points in this\n                    // data class\n                    setVisible: function () {\n                        this.visible = vis = axis.visible = !vis;\n                        const affectedSeries = [];\n                        for (const point of getPointsInDataClass(i)) {\n                            point.setVisible(vis);\n                            point.hiddenInDataClass = !vis; // #20441\n                            if (affectedSeries.indexOf(point.series) === -1) {\n                                affectedSeries.push(point.series);\n                            }\n                        }\n                        chart.legend.colorizeItem(this, vis);\n                        affectedSeries.forEach((series) => {\n                            fireEvent(series, 'afterDataClassLegendClick');\n                        });\n                    }\n                }, dataClass));\n            });\n        }\n        return legendItems;\n    }\n    /**\n     * Get size of color axis symbol.\n     * @private\n     */\n    getSize() {\n        const axis = this, { chart, horiz } = axis, { height: colorAxisHeight, width: colorAxisWidth } = axis.options, { legend: legendOptions } = chart.options, width = ColorAxis_pick(defined(colorAxisWidth) ?\n            relativeLength(colorAxisWidth, chart.chartWidth) : void 0, legendOptions?.symbolWidth, horiz ? ColorAxis.defaultLegendLength : 12), height = ColorAxis_pick(defined(colorAxisHeight) ?\n            relativeLength(colorAxisHeight, chart.chartHeight) : void 0, legendOptions?.symbolHeight, horiz ? 12 : ColorAxis.defaultLegendLength);\n        return {\n            width,\n            height\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nColorAxis.defaultLegendLength = 200;\n/**\n * @private\n */\nColorAxis.keepProps = [\n    'legendItem'\n];\nColorAxis_extend(ColorAxis.prototype, Color_ColorAxisLike);\n/* *\n *\n *  Registry\n *\n * */\n// Properties to preserve after destroy, for Axis.update (#5881, #6025).\nArray.prototype.push.apply((highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()).keepProps, ColorAxis.keepProps);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Color_ColorAxis = (ColorAxis);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Color axis types\n *\n * @typedef {\"linear\"|\"logarithmic\"} Highcharts.ColorAxisTypeValue\n */\n''; // Detach doclet above\n\n;// ./code/es-modules/masters/modules/coloraxis.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.ColorAxis = G.ColorAxis || Color_ColorAxis;\nG.ColorAxis.compose(G.Chart, G.Fx, G.Legend, G.Series);\n/* harmony default export */ const coloraxis_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__500__", "__WEBPACK_EXTERNAL_MODULE__512__", "ColorAxisComposition", "ColorAxisLike", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "coloraxis_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "parse", "color", "addEvent", "extend", "merge", "pick", "splat", "ColorAxisConstructor", "onChartAfterCreateAxes", "userOptions", "colorAxis", "map", "axisOptions", "onLegendAfterGetAllItems", "e", "colorAxes", "chart", "destroyItem", "item", "i", "allItems", "indexOf", "splice", "colorAxisItems", "options", "for<PERSON>ach", "showInLegend", "dataClasses", "visible", "concat", "getDataClassLegendSymbols", "push", "series", "legendType", "points", "point", "length", "unshift", "onLegendAfterColorizeItem", "legendColor", "legendItem", "symbol", "attr", "fill", "onLegendAfterUpdate", "update", "redraw", "onSeriesAfterTranslate", "colorAttribs", "translateColors", "onSeriesBindAxes", "axisTypes", "pointSetVisible", "vis", "method", "Boolean", "buildKDTree", "seriesTranslateColors", "getPointsCollection", "nullColor", "colorKey", "value", "getNestedProperty", "isNull", "toColor", "label", "legend", "colorizeItem", "wrapFxFillSetter", "elem", "start", "tweenTo", "end", "pos", "wrapFxStrokeSetter", "compose", "ColorAxisClass", "ChartClass", "FxClass", "LegendClass", "SeriesClass", "chartProto", "fxProto", "seriesProto", "collectionsWithUpdate", "includes", "collectionsWithInit", "addColorAxis", "wrapChartCreateAxis", "superCreateAxis", "createAxis", "type", "apply", "arguments", "axis", "index", "isX", "isDirtyLegend", "axes", "bindAxes", "isDirtyData", "animation", "fillSetter", "strokeSetter", "optionalAxis", "pointClass", "setVisible", "order", "Color_ColorAxisComposition", "ColorAxisLike_color", "ColorAxisLike_merge", "initDataClasses", "userDataClasses", "dataClass", "colorCount", "colorCounter", "colors", "labels", "iEnd", "styledMode", "dataClassColor", "colorIndex", "minColor", "maxColor", "initStops", "stops", "normalizedValue", "max", "min", "logarithmic", "log2lin", "from", "to", "Color_ColorAxisLike", "highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_", "highcharts_LegendSymbol_commonjs_highcharts_LegendSymbol_commonjs2_highcharts_LegendSymbol_root_Highcharts_LegendSymbol_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "defaultOptions", "Series", "defined", "ColorAxis_extend", "fireEvent", "isArray", "isNumber", "ColorAxis_merge", "ColorAxis_pick", "<PERSON><PERSON><PERSON><PERSON>", "xAxis", "lineWidth", "minPadding", "maxPadding", "gridLineColor", "gridLineWidth", "tickPixelInterval", "startOnTick", "endOnTick", "offset", "marker", "duration", "width", "distance", "overflow", "rotation", "tick<PERSON><PERSON>th", "ColorAxis", "constructor", "coll", "init", "horiz", "layout", "side", "reversed", "opposite", "zoomEnabled", "hasData", "tickPositions", "setTickPositions", "setOptions", "showEmpty", "title", "enabled", "crosshair", "setAxisSize", "height", "getSize", "left", "top", "right", "chartWidth", "bottom", "chartHeight", "len", "defaultLegendLength", "getOffset", "group", "sideOffset", "axisOffset", "axisParent", "drawLegendSymbol", "render", "<PERSON><PERSON><PERSON><PERSON>", "some", "isDrilling", "isDirty", "added", "labelLeft", "labelRight", "setLegendColor", "one", "zero", "grad", "linearGradient", "x1", "y1", "x2", "y2", "padding", "legendOptions", "labelOptions", "itemDistance", "labelPadding", "renderer", "r", "symbolRadius", "zIndex", "add", "x", "y", "baseline", "labelWidth", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelHeight", "setState", "state", "getSeriesExtremes", "colorValArray", "calculatedExtremes", "cSeries", "dataMin", "Infinity", "dataMax", "pointVal<PERSON>ey", "zoneAxis", "getColumn", "minColorValue", "maxColorValue", "cExtremes", "getExtremes", "Math", "applyExtremes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "crossPos", "plotX", "plotY", "axisPos", "axisLen", "toPixels", "cross", "addedToColorAxis", "addClass", "getPlotLinePath", "translatedValue", "newOptions", "destroyItems", "destroy", "slice", "remove", "name", "legendItems", "valueDecimals", "valueSuffix", "getPointsInDataClass", "reduce", "s", "filter", "numberF<PERSON>atter", "rectangle", "isDataClass", "affectedSeries", "hiddenInDataClass", "colorAxisHeight", "colorAxis<PERSON>idth", "symbolWidth", "symbolHeight", "keepProps", "Array", "G", "Chart", "Fx", "Legend"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5K,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,IAAO,CAACA,EAAK,KAAQ,CAACA,EAAK,YAAe,CAACA,EAAK,cAAiB,CAAE,GACxK,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,YAAe,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE5MA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,YAAe,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACpL,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC3K,AAAC,CAAA,KACP,aACA,IAkINC,EAouBAC,EAt2BUC,EAAuB,CAE/B,IACC,AAACZ,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,AAAClB,IACxB,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,IAAOpB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACtB,EAASwB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA2FzB,EAAoB,KAC/G0B,EAA+G1B,EAAoBI,CAAC,CAACqB,GAErIE,EAA+F3B,EAAoB,KACnH4B,EAAmH5B,EAAoBI,CAAC,CAACuB,GAa7I,GAAM,CAAEE,MAAOC,CAAK,CAAE,CAAIF,IAEpB,CAAEG,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAE,CAAIX,KAOlD,AAAC,SAAU5B,CAAoB,EAW3B,IAAIwC,EAwCJ,SAASC,IACL,GAAM,CAAEC,YAAAA,CAAW,CAAE,CAAG,IAAI,AAC5B,CAAA,IAAI,CAACC,SAAS,CAAG,EAAE,CAGfD,EAAYC,SAAS,GACrBD,EAAYC,SAAS,CAAGJ,EAAMG,EAAYC,SAAS,EACnDD,EAAYC,SAAS,CAACC,GAAG,CAAC,AAACC,GAAiB,IAAIL,EAAqB,IAAI,CAAEK,IAEnF,CAMA,SAASC,EAAyBC,CAAC,EAC/B,IAAMC,EAAY,IAAI,CAACC,KAAK,CAACN,SAAS,EAAI,EAAE,CAAEO,EAAc,AAACC,IACzD,IAAMC,EAAIL,EAAEM,QAAQ,CAACC,OAAO,CAACH,EACnB,CAAA,KAANC,IAEA,IAAI,CAACF,WAAW,CAACH,EAAEM,QAAQ,CAACD,EAAE,EAC9BL,EAAEM,QAAQ,CAACE,MAAM,CAACH,EAAG,GAE7B,EACII,EAAiB,EAAE,CAAEC,EAASL,EA8BlC,IA7BAJ,EAAUU,OAAO,CAAC,SAAUf,CAAS,EACjCc,EAAUd,EAAUc,OAAO,CACvBA,GAASE,eAELF,EAAQG,WAAW,EAAIH,EAAQI,OAAO,CACtCL,EAAiBA,EAAeM,MAAM,CAACnB,EAAUoB,yBAAyB,IAGrEN,EAAQI,OAAO,EAEpBL,EAAeQ,IAAI,CAACrB,GAIxBA,EAAUsB,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EACjC,CAAA,CAACA,EAAOR,OAAO,CAACE,YAAY,EAAIF,EAAQG,WAAW,AAAD,IAC9CK,AAA8B,UAA9BA,EAAOR,OAAO,CAACS,UAAU,CACzBD,EAAOE,MAAM,CAACT,OAAO,CAAC,SAAUU,CAAK,EACjClB,EAAYkB,EAChB,GAGAlB,EAAYe,GAGxB,GAER,GACAb,EAAII,EAAea,MAAM,CAClBjB,KACHL,EAAEM,QAAQ,CAACiB,OAAO,CAACd,CAAc,CAACJ,EAAE,CAE5C,CAIA,SAASmB,EAA0BxB,CAAC,EAC5BA,EAAEc,OAAO,EAAId,EAAEI,IAAI,CAACqB,WAAW,EAC/BzB,EAAEI,IAAI,CAACsB,UAAU,CAACC,MAAM,CAACC,IAAI,CAAC,CAC1BC,KAAM7B,EAAEI,IAAI,CAACqB,WAAW,AAC5B,EAER,CAKA,SAASK,EAAoB9B,CAAC,EAC1B,IAAI,CAACE,KAAK,CAACN,SAAS,EAAEe,QAAQ,AAACf,IAC3BA,EAAUmC,MAAM,CAAC,CAAC,EAAG/B,EAAEgC,MAAM,CACjC,EACJ,CAKA,SAASC,IACD,CAAA,IAAI,CAAC/B,KAAK,CAACN,SAAS,EAAE0B,QACtB,IAAI,CAACY,YAAY,AAAD,GAChB,IAAI,CAACC,eAAe,EAE5B,CAKA,SAASC,IACL,IAAMC,EAAY,IAAI,CAACA,SAAS,CAC3BA,EAGuC,KAAnCA,EAAU9B,OAAO,CAAC,cACvB8B,EAAUpB,IAAI,CAAC,aAHf,IAAI,CAACoB,SAAS,CAAG,CAAC,YAAY,AAKtC,CAOA,SAASC,EAAgBC,CAAG,EACxB,IAAMlB,EAAQ,IAAI,CAAEmB,EAASD,EAAM,OAAS,MAC5ClB,CAAAA,EAAMP,OAAO,CAAGO,EAAMX,OAAO,CAACI,OAAO,CAAG2B,CAAAA,CAAQF,EAEhD,CAAC,UAAW,YAAY,CAAC5B,OAAO,CAAC,SAAU5C,CAAG,EACtCsD,CAAK,CAACtD,EAAI,EACVsD,CAAK,CAACtD,EAAI,CAACyE,EAAO,EAE1B,GACA,IAAI,CAACtB,MAAM,CAACwB,WAAW,EAC3B,CAQA,SAASC,IACL,IAAMzB,EAAS,IAAI,CAAEE,EAAS,IAAI,CAACwB,mBAAmB,GACtDC,EAAY,IAAI,CAACnC,OAAO,CAACmC,SAAS,CAAEjD,EAAY,IAAI,CAACA,SAAS,CAAEkD,EAAW,IAAI,CAACA,QAAQ,CACxF1B,EAAOT,OAAO,CAAC,AAACU,IACZ,IAAM0B,EAAQ1B,EAAM2B,iBAAiB,CAACF,GAAW3D,EAAQkC,EAAMX,OAAO,CAACvB,KAAK,EAAKkC,CAAAA,EAAM4B,MAAM,EAAI5B,AAAgB,OAAhBA,EAAM0B,KAAK,CACxGF,EACA,AAACjD,GAAa,AAAiB,KAAA,IAAVmD,EACjBnD,EAAUsD,OAAO,CAACH,EAAO1B,GACzBA,EAAMlC,KAAK,EAAI+B,EAAO/B,KAAK,AAAD,EAC9BA,GAASkC,EAAMlC,KAAK,GAAKA,IACzBkC,EAAMlC,KAAK,CAAGA,EACoB,UAA9B+B,EAAOR,OAAO,CAACS,UAAU,EACzBE,EAAMK,UAAU,EAChBL,EAAMK,UAAU,CAACyB,KAAK,EACtBjC,EAAOhB,KAAK,CAACkD,MAAM,CAACC,YAAY,CAAChC,EAAOA,EAAMP,OAAO,EAGjE,EACJ,CAkCA,SAASwC,IACL,IAAI,CAACC,IAAI,CAAC3B,IAAI,CAAC,OAAQzC,EAAM,IAAI,CAACqE,KAAK,EAAEC,OAAO,CAACtE,EAAM,IAAI,CAACuE,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EACzF,CAKA,SAASC,IACL,IAAI,CAACL,IAAI,CAAC3B,IAAI,CAAC,SAAUzC,EAAM,IAAI,CAACqE,KAAK,EAAEC,OAAO,CAACtE,EAAM,IAAI,CAACuE,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EAC3F,CA9LA1G,EAAqB4G,OAAO,CA1B5B,SAAiBC,CAAc,CAAEC,CAAU,CAAEC,CAAO,CAAEC,CAAW,CAAEC,CAAW,EAC1E,IAAMC,EAAaJ,EAAWxF,SAAS,CAAE6F,EAAUJ,EAAQzF,SAAS,CAAE8F,EAAcH,EAAY3F,SAAS,CACpG4F,EAAWG,qBAAqB,CAACC,QAAQ,CAAC,eAC3C9E,EAAuBqE,EACvBK,EAAWG,qBAAqB,CAACrD,IAAI,CAAC,aACtCkD,EAAWK,mBAAmB,CAAC5E,SAAS,CAAG,CACvCuE,EAAWM,YAAY,CAC1B,CACDrF,EAAS2E,EAAY,kBAAmBrE,GACxCgF,AAwKR,SAA6BX,CAAU,EACnC,IAAMY,EAAkBZ,EAAWxF,SAAS,CAACqG,UAAU,AACvDb,CAAAA,EAAWxF,SAAS,CAACqG,UAAU,CAAG,SAAUC,CAAI,CAAEnE,CAAO,EAErD,GAAImE,AAAS,cAATA,EACA,OAAOF,EAAgBG,KAAK,CAFlB,IAAI,CAEsBC,WAExC,IAAMC,EAAO,IAAIvF,EAJH,IAAI,CAI2BH,EAAMoB,EAAQsE,IAAI,CAAE,CAC7DC,MAAO/E,AALG,IAAI,AAKF,CAAC2E,EAAK,CAACvD,MAAM,CACzB4D,IAAK,CAAA,CACT,IAaA,OAZAhF,AARc,IAAI,CAQZiF,aAAa,CAAG,CAAA,EAEtBjF,AAVc,IAAI,CAUZkF,IAAI,CAACzE,OAAO,CAAC,AAACqE,IAChBA,EAAK9D,MAAM,CAAG,EAAE,AACpB,GACAhB,AAbc,IAAI,CAaZgB,MAAM,CAACP,OAAO,CAAC,AAACO,IAClBA,EAAOmE,QAAQ,GACfnE,EAAOoE,WAAW,CAAG,CAAA,CACzB,GACI/F,EAAKmB,EAAQsB,MAAM,CAAE,CAAA,IACrB9B,AAlBU,IAAI,CAkBR8B,MAAM,CAACtB,EAAQ6E,SAAS,EAE3BP,CACX,CACJ,EAjM4BjB,GACpBK,EAAQoB,UAAU,CAAGlC,EACrBc,EAAQqB,YAAY,CAAG7B,EACvBxE,EAAS6E,EAAa,mBAAoBlE,GAC1CX,EAAS6E,EAAa,oBAAqBzC,GAC3CpC,EAAS6E,EAAa,cAAenC,GACrCzC,EAAOgF,EAAa,CAChBqB,aAAc,YACdvD,gBAAiBQ,CACrB,GACAtD,EAAOgF,EAAYsB,UAAU,CAACpH,SAAS,CAAE,CACrCqH,WAAYtD,CAChB,GACAlD,EAAS8E,EAAa,iBAAkBjC,EAAwB,CAAE4D,MAAO,CAAE,GAC3EzG,EAAS8E,EAAa,WAAY9B,GAE1C,EA2HAnF,EAAqBqF,eAAe,CAAGA,CAqE3C,EAAGrF,GAAyBA,CAAAA,EAAuB,CAAC,CAAA,GAMvB,IAAM6I,EAA8B7I,EAwe3D,CAAEiC,MAAO6G,CAAmB,CAAE,CAAI9G,IAElC,CAAEK,MAAO0G,CAAmB,CAAE,CAAInH,KAOxC,AAAC,SAAU3B,CAAa,EA8CpBA,EAAc+I,eAAe,CA/B7B,SAAyBtG,CAAW,EAChC,IAAmBO,EAAQ8E,AAAd,IAAI,CAAe9E,KAAK,CAAEwB,EAAasD,AAAvC,IAAI,CAAwCtD,UAAU,CAAGsD,AAAzD,IAAI,CAA0DtD,UAAU,EAAI,CAAC,EAAGhB,EAAUsE,AAA1F,IAAI,CAA2FtE,OAAO,CAAEwF,EAAkBvG,EAAYkB,WAAW,EAAI,EAAE,CAChKsF,EAAWtF,EAAauF,EAAalG,EAAMQ,OAAO,CAACR,KAAK,CAACkG,UAAU,CAAEC,EAAe,EAAGC,CAC3FtB,CAFa,IAAI,CAEZnE,WAAW,CAAGA,EAAc,EAAE,CACnCa,EAAW6E,MAAM,CAAG,EAAE,CACtB,IAAK,IAAIlG,EAAI,EAAGmG,EAAON,EAAgB5E,MAAM,CAAEjB,EAAImG,EAAM,EAAEnG,EAEvD8F,EAAYH,EADZG,EAAYD,CAAe,CAAC7F,EAAE,EAE9BQ,EAAYI,IAAI,CAACkF,GACb,CAAA,AAACjG,EAAMuG,UAAU,GAAIN,EAAUhH,KAAK,AAAD,IAGnCuB,AAA2B,aAA3BA,EAAQgG,cAAc,EACjBxG,EAAMuG,UAAU,GAEjBL,EAAaE,AADbA,CAAAA,EAASpG,EAAMQ,OAAO,CAAC4F,MAAM,EAAI,EAAE,AAAD,EACdhF,MAAM,CAC1B6E,EAAUhH,KAAK,CAAGmH,CAAM,CAACD,EAAa,EAE1CF,EAAUQ,UAAU,CAAGN,IAGnBA,IAAiBD,GACjBC,CAAAA,EAAe,CAAA,GAInBF,EAAUhH,KAAK,CAAG4G,EAAoBrF,EAAQkG,QAAQ,EAAEnD,OAAO,CAACsC,EAAoBrF,EAAQmG,QAAQ,EAAGL,EAAO,EAAI,GAAMnG,EAAKmG,CAAAA,EAAO,CAAA,GAIhJ,EAeAtJ,EAAc4J,SAAS,CATvB,WACI,IAAmBpG,EAAUsE,AAAhB,IAAI,CAAiBtE,OAAO,CAAEqG,EAAQ/B,AAAtC,IAAI,CAAuC+B,KAAK,CAAGrG,EAAQqG,KAAK,EAAI,CAC7E,CAAC,EAAGrG,EAAQkG,QAAQ,EAAI,GAAG,CAC3B,CAAC,EAAGlG,EAAQmG,QAAQ,EAAI,GAAG,CAC9B,CACD,IAAK,IAAIxG,EAAI,EAAGmG,EAAOO,EAAMzF,MAAM,CAAEjB,EAAImG,EAAM,EAAEnG,EAC7C0G,CAAK,CAAC1G,EAAE,CAAClB,KAAK,CAAG4G,EAAoBgB,CAAK,CAAC1G,EAAE,CAAC,EAAE,CAExD,EAcAnD,EAAc8J,eAAe,CAR7B,SAAyBjE,CAAK,EAC1B,IAAmBkE,EAAMjC,AAAZ,IAAI,CAAaiC,GAAG,EAAI,EAAGC,EAAMlC,AAAjC,IAAI,CAAkCkC,GAAG,EAAI,EAI1D,OAHIlC,AADS,IAAI,CACRmC,WAAW,EAChBpE,CAAAA,EAAQiC,AAFC,IAAI,CAEAmC,WAAW,CAACC,OAAO,CAACrE,EAAK,EAEnC,EAAK,AAACkE,CAAAA,EAAMlE,CAAI,EAClB,CAAA,AAACkE,EAAMC,GAAQ,CAAA,CACxB,EA4CAhK,EAAcgG,OAAO,CAtCrB,SAAiBH,CAAK,CAAE1B,CAAK,EAEzB,IAEIsC,EAAK0D,EAAMC,EAAInI,EAAOgH,EAAW9F,EAF/BQ,EAAcmE,AADP,IAAI,CACQnE,WAAW,CAC9BkG,EAAQ/B,AAFD,IAAI,CAEE+B,KAAK,CAExB,GAAIlG,EAEA,CAAA,IADAR,EAAIQ,EAAYS,MAAM,CACfjB,KAIH,GAFAgH,EAAOlB,AADPA,CAAAA,EAAYtF,CAAW,CAACR,EAAE,AAAD,EACRgH,IAAI,CACrBC,EAAKnB,EAAUmB,EAAE,CACb,AAAC,CAAA,AAAgB,KAAA,IAATD,GAAwBtE,GAASsE,CAAG,GAC3C,CAAA,AAAc,KAAA,IAAPC,GAAsBvE,GAASuE,CAAC,EAAI,CAC5CnI,EAAQgH,EAAUhH,KAAK,CACnBkC,IACAA,EAAM8E,SAAS,CAAG9F,EAClBgB,EAAMsF,UAAU,CAAGR,EAAUQ,UAAU,EAE3C,KACJ,CACJ,KAEC,CAGD,IAFAhD,EAAMqB,AAtBG,IAAI,CAsBFgC,eAAe,CAACjE,GAC3B1C,EAAI0G,EAAMzF,MAAM,CACTjB,MACCsD,CAAAA,EAAMoD,CAAK,CAAC1G,EAAE,CAAC,EAAE,AAAD,IAIxBgH,EAAON,CAAK,CAAC1G,EAAE,EAAI0G,CAAK,CAAC1G,EAAI,EAAE,CAG/BsD,EAAM,EAAI,AAAC2D,CAAAA,AAFXA,CAAAA,EAAKP,CAAK,CAAC1G,EAAI,EAAE,EAAIgH,CAAG,CAEX,CAAC,EAAE,CAAG1D,CAAE,EAAM,CAAA,AAAC2D,CAAE,CAAC,EAAE,CAAGD,CAAI,CAAC,EAAE,EAAK,CAAA,EAChDlI,EAAQkI,EAAKlI,KAAK,CAACsE,OAAO,CAAC6D,EAAGnI,KAAK,CAAEwE,EACzC,CACA,OAAOxE,CACX,CAEJ,EAAGjC,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAMqK,EAAuBrK,EAG1D,IAAIsK,EAA2HnK,EAAoB,KAC/IoK,EAA+IpK,EAAoBI,CAAC,CAAC+J,GAErKE,EAAmIrK,EAAoB,KACvJsK,EAAuJtK,EAAoBI,CAAC,CAACiK,GAiBjL,GAAM,CAAEE,eAAAA,CAAc,CAAE,CAAI/I,IAGtB,CAAEqC,OAAQ2G,CAAM,CAAE,CAAIF,IAEtB,CAAEG,QAAAA,CAAO,CAAEzI,OAAQ0I,CAAgB,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAE5I,MAAO6I,CAAe,CAAE5I,KAAM6I,CAAc,CAAEC,eAAAA,CAAc,CAAE,CAAIxJ,GAC3I+I,CAAAA,EAAehI,SAAS,CAAGuI,EAAgBP,EAAeU,KAAK,CAlkBrC,CAwGtBC,UAAW,EAOXC,WAAY,EAgCZC,WAAY,EAWZC,cAAe,UAUfC,cAAe,EAgBfC,kBAAmB,GAOnBC,YAAa,CAAA,EAQbC,UAAW,CAAA,EAEXC,OAAQ,EAYRC,OAAQ,CAQJzD,UAAW,CAEP0D,SAAU,EACd,EAEAC,MAAO,IAOP/J,MAAO,SACX,EAUAoH,OAAQ,CACJ4C,SAAU,EAUVC,SAAU,UACVC,SAAU,CACd,EAmBAzC,SAAU,UAmBVC,SAAU,UAsBVyC,WAAY,EAkFZ1I,aAAc,CAAA,CAClB,EAyMA,OAAM2I,UAAmBxK,IAMrB,OAAO8E,QAAQE,CAAU,CAAEC,CAAO,CAAEC,CAAW,CAAEC,CAAW,CAAE,CAC1D4B,EAA2BjC,OAAO,CAAC0F,EAAWxF,EAAYC,EAASC,EAAaC,EACpF,CASAsF,YAAYtJ,CAAK,CAAEP,CAAW,CAAE,CAC5B,KAAK,CAACO,EAAOP,GACb,IAAI,CAAC8J,IAAI,CAAG,YACZ,IAAI,CAAC3I,OAAO,CAAG,CAAA,EACf,IAAI,CAAC4I,IAAI,CAACxJ,EAAOP,EACrB,CAiBA+J,KAAKxJ,CAAK,CAAEP,CAAW,CAAE,CAErB,IAAMyD,EAASlD,EAAMQ,OAAO,CAAC0C,MAAM,EAAI,CAAC,EAAGuG,EAAQhK,EAAYiK,MAAM,CACjEjK,AAAuB,aAAvBA,EAAYiK,MAAM,CAClBxG,AAAkB,aAAlBA,EAAOwG,MAAM,AACjB5E,CAJa,IAAI,CAIZ6E,IAAI,CAAGlK,EAAYkK,IAAI,EAAIF,EAAQ,EAAI,EAC5C3E,AALa,IAAI,CAKZ8E,QAAQ,CAAGnK,EAAYmK,QAAQ,EAAI,CAACH,EACzC3E,AANa,IAAI,CAMZ+E,QAAQ,CAAG,CAACJ,EACjB,KAAK,CAACD,KAAKxJ,EAAOP,EAAa,aAG/B,IAAI,CAACA,WAAW,CAAGA,EACfsI,EAAQ/H,EAAMP,WAAW,CAACC,SAAS,GACnCM,CAAAA,EAAMP,WAAW,CAACC,SAAS,CAAC,IAAI,CAACqF,KAAK,CAAC,CAAGtF,CAAU,EAGpDA,EAAYkB,WAAW,EACvBmE,AAhBS,IAAI,CAgBRiB,eAAe,CAACtG,GAEzBqF,AAlBa,IAAI,CAkBZ8B,SAAS,GAEd9B,AApBa,IAAI,CAoBZ2E,KAAK,CAAGA,EACb3E,AArBa,IAAI,CAqBZgF,WAAW,CAAG,CAAA,CACvB,CASAC,SAAU,CACN,MAAO,CAAC,CAAC,AAAC,CAAA,IAAI,CAACC,aAAa,EAAI,EAAE,AAAD,EAAG5I,MAAM,AAC9C,CAKA6I,kBAAmB,CACf,GAAI,CAAC,IAAI,CAACtJ,WAAW,CACjB,OAAO,KAAK,CAACsJ,kBAErB,CAKAC,WAAWzK,CAAW,CAAE,CACpB,IAAMe,EAAUyH,EAAgBP,EAAehI,SAAS,CAAED,EAE1D,CACI0K,UAAW,CAAA,EACXC,MAAO,KACPxJ,QAAS,IAAI,CAACZ,KAAK,CAACQ,OAAO,CAAC0C,MAAM,CAACmH,OAAO,EACtC5K,AAAwB,CAAA,IAAxBA,EAAYmB,OAAO,AAC3B,GACA,KAAK,CAACsJ,WAAW1J,GACjB,IAAI,CAACA,OAAO,CAAC8J,SAAS,CAAG,IAAI,CAAC9J,OAAO,CAACsI,MAAM,AAChD,CAIAyB,aAAc,CACV,IAAmBvK,EAAQ8E,AAAd,IAAI,CAAe9E,KAAK,CAAEyB,EAASqD,AAAnC,IAAI,CAAoCtD,UAAU,EAAEC,OAC7D,CAAEuH,MAAAA,CAAK,CAAEwB,OAAAA,CAAM,CAAE,CAAG1F,AADX,IAAI,CACY2F,OAAO,GAChChJ,IACA,IAAI,CAACiJ,IAAI,CAAG,CAACjJ,EAAOC,IAAI,CAAC,KACzB,IAAI,CAACiJ,GAAG,CAAG,CAAClJ,EAAOC,IAAI,CAAC,KACxB,IAAI,CAACsH,KAAK,CAAGA,EAAQ,CAACvH,EAAOC,IAAI,CAAC,SAClC,IAAI,CAAC8I,MAAM,CAAGA,EAAS,CAAC/I,EAAOC,IAAI,CAAC,UACpC,IAAI,CAACkJ,KAAK,CAAG5K,EAAM6K,UAAU,CAAG,IAAI,CAACH,IAAI,CAAG1B,EAC5C,IAAI,CAAC8B,MAAM,CAAG9K,EAAM+K,WAAW,CAAG,IAAI,CAACJ,GAAG,CAAGH,EAC7C,IAAI,CAAC/G,GAAG,CAAG,IAAI,CAACgG,KAAK,CAAG,IAAI,CAACiB,IAAI,CAAG,IAAI,CAACC,GAAG,EAIhD,IAAI,CAACK,GAAG,CAAG,AAAC,CAAA,IAAI,CAACvB,KAAK,CAAGT,EAAQwB,CAAK,GAClCnB,EAAU4B,mBAAmB,AACrC,CAMAC,WAAY,CAER,IAAMC,EAAQrG,AADD,IAAI,CACEtD,UAAU,EAAE2J,MACzBC,EAAatG,AAFN,IAAI,CAEO9E,KAAK,CAACqL,UAAU,CAACvG,AAF5B,IAAI,CAE6B6E,IAAI,CAAC,CACnD,GAAIwB,EAAO,CAGPrG,AANS,IAAI,CAMRwG,UAAU,CAAGH,EAElB,KAAK,CAACD,YACN,IAAMhI,EAAS,IAAI,CAAClD,KAAK,CAACkD,MAAM,CAGhCA,EAAO9C,QAAQ,CAACK,OAAO,CAAC,SAAUP,CAAI,EAC9BA,aAAgBmJ,GAChBnJ,EAAKqL,gBAAgB,CAACrI,EAAQhD,EAEtC,GACAgD,EAAOsI,MAAM,GACb,IAAI,CAACxL,KAAK,CAACyL,UAAU,CAAC,CAAA,GAEjB,IAAI,CAACzL,KAAK,CAACgB,MAAM,CAAC0K,IAAI,CAAC,AAAC1K,GAAWA,EAAO2K,UAAU,GACrD7G,CAAAA,AArBK,IAAI,CAqBJ8G,OAAO,CAAG,CAAA,CAAG,EAGjB9G,AAxBI,IAAI,CAwBH+G,KAAK,GACX/G,AAzBK,IAAI,CAyBJ+G,KAAK,CAAG,CAAA,EACb/G,AA1BK,IAAI,CA0BJgH,SAAS,CAAG,EACjBhH,AA3BK,IAAI,CA2BJiH,UAAU,CAAGjH,AA3Bb,IAAI,CA2BckE,KAAK,EAGhClE,AA9BS,IAAI,CA8BR9E,KAAK,CAACqL,UAAU,CAACvG,AA9Bb,IAAI,CA8Bc6E,IAAI,CAAC,CAAGyB,CACvC,CACJ,CAKAY,gBAAiB,CAEb,IAAMvC,EAAQ3E,AADD,IAAI,CACE2E,KAAK,CAClBG,EAAW9E,AAFJ,IAAI,CAEK8E,QAAQ,CACxBqC,EAAMrC,GAAAA,EACNsC,EAAOtC,EAAAA,EACPuC,EAAO1C,EAAQ,CAACwC,EAAK,EAAGC,EAAM,EAAE,CAAG,CAAC,EAAGA,EAAM,EAAGD,EAAI,AAC1DnH,CANa,IAAI,CAMZvD,WAAW,CAAG,CACf6K,eAAgB,CACZC,GAAIF,CAAI,CAAC,EAAE,CACXG,GAAIH,CAAI,CAAC,EAAE,CACXI,GAAIJ,CAAI,CAAC,EAAE,CACXK,GAAIL,CAAI,CAAC,EAAE,AACf,EACAtF,MAAO/B,AAbE,IAAI,CAaD+B,KAAK,AACrB,CACJ,CAKA0E,iBAAiBrI,CAAM,CAAEhD,CAAI,CAAE,CAC3B,IAAmBsB,EAAatB,EAAKsB,UAAU,EAAI,CAAC,EAAGiL,EAAUvJ,EAAOuJ,OAAO,CAAEC,EAAgBxJ,EAAO1C,OAAO,CAAEmM,EAAe7H,AAAnH,IAAI,CAAoHtE,OAAO,CAAC6F,MAAM,CAAEuG,EAAe1E,EAAewE,EAAcE,YAAY,CAAE,IAAKnD,EAAQ3E,AAA/M,IAAI,CAAgN2E,KAAK,CAAE,CAAET,MAAAA,CAAK,CAAEwB,OAAAA,CAAM,CAAE,CAAG1F,AAA/O,IAAI,CAAgP2F,OAAO,GAAIoC,EAAe3E,EAG3RwE,EAAcG,YAAY,CAAEpD,EAAQ,GAAK,IACzC,IAAI,CAACuC,cAAc,GAEdxK,EAAWC,MAAM,EAClBD,CAAAA,EAAWC,MAAM,CAAG,IAAI,CAACzB,KAAK,CAAC8M,QAAQ,CAACrL,MAAM,CAAC,eAC1CC,IAAI,CAAC,CACNqL,EAAGL,EAAcM,YAAY,EAAI,EACjCC,OAAQ,CACZ,GAAGC,GAAG,CAAC1L,EAAW2J,KAAK,CAAA,EAE3B3J,EAAWC,MAAM,CAACC,IAAI,CAAC,CACnByL,EAAG,EACHC,EAAG,AAAClK,CAAAA,EAAOmK,QAAQ,EAAI,CAAA,EAAK,GAC5BrE,MAAOA,EACPwB,OAAQA,CACZ,GAEAhJ,EAAW8L,UAAU,CAAItE,EACrByD,EACChD,CAAAA,EACGmD,EACA1E,EAAeyE,EAAaQ,CAAC,CAAER,EAAa1D,QAAQ,EAC/C,CAAA,IAAI,CAACsE,cAAc,EAAI,CAAA,CAAC,EACrC/L,EAAWgM,WAAW,CAAGhD,EAASiC,EAAWhD,CAAAA,EAAQoD,EAAe,CAAA,CACxE,CAKAY,SAASC,CAAK,CAAE,CACZ,IAAI,CAAC1M,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EAChCA,EAAOyM,QAAQ,CAACC,EACpB,EACJ,CAIAhI,YAAa,CACb,CAIAiI,mBAAoB,CAEhB,IAAM3M,EAAS8D,AADF,IAAI,CACG9D,MAAM,CACtB4M,EAAehL,EAAUiL,EAAoBC,EAAS3N,EAAIa,EAAOI,MAAM,CAG3E,IAFA,IAAI,CAAC2M,OAAO,CAAGC,IACf,IAAI,CAACC,OAAO,CAAG,CAACD,IACT7N,KAAK,CAMR,IAAK,IAAMtC,KAJX+E,EAAWkL,AADXA,CAAAA,EAAU9M,CAAM,CAACb,EAAE,AAAD,EACCyC,QAAQ,CAAGsF,EAAe4F,EAAQtN,OAAO,CAACoC,QAAQ,CAAEkL,EAAQlL,QAAQ,CAAEkL,EAAQI,WAAW,CAAEJ,EAAQK,QAAQ,CAAE,KAChIN,EAAqBC,CAAO,CAAClL,EAAW,MAAM,EAC1CkL,CAAO,CAAClL,EAAW,MAAM,CAEX,CAACA,EAAU,QAAS,IAAI,EAEtC,GAAIgL,AADJA,CAAAA,EAAgBE,EAAQM,SAAS,CAACvQ,EAAG,EACnBuD,MAAM,CACpB,MAIR,GAAIyM,EACAC,EAAQO,aAAa,CAAGP,CAAO,CAAClL,EAAW,MAAM,CACjDkL,EAAQQ,aAAa,CAAGR,CAAO,CAAClL,EAAW,MAAM,KAEhD,CACD,IAAM2L,EAAY5G,EAAOtJ,SAAS,CAACmQ,WAAW,CAACjQ,IAAI,CAACuP,EAASF,EAC7DE,CAAAA,EAAQO,aAAa,CAAGE,EAAUR,OAAO,CACzCD,EAAQQ,aAAa,CAAGC,EAAUN,OAAO,AAC7C,CACIrG,EAAQkG,EAAQO,aAAa,GAC7BzG,EAAQkG,EAAQQ,aAAa,IAC7B,IAAI,CAACP,OAAO,CACRU,KAAKzH,GAAG,CAAC,IAAI,CAAC+G,OAAO,CAAED,EAAQO,aAAa,EAChD,IAAI,CAACJ,OAAO,CACRQ,KAAK1H,GAAG,CAAC,IAAI,CAACkH,OAAO,CAAEH,EAAQQ,aAAa,GAE/CT,GACDlG,EAAOtJ,SAAS,CAACqQ,aAAa,CAACnQ,IAAI,CAACuP,EAE5C,CACJ,CAgBAa,cAAc7O,CAAC,CAAEqB,CAAK,CAAE,CACpB,IACIyN,EADepN,EAAasD,AAAnB,IAAI,CAAoBtD,UAAU,EAAI,CAAC,EAAGqN,EAAQ1N,GAAO0N,MAAOC,EAAQ3N,GAAO2N,MAAOC,EAAUjK,AAAhG,IAAI,CAAiGrB,GAAG,CAAEuL,EAAUlK,AAApH,IAAI,CAAqHkG,GAAG,CAErI7J,IAEIyN,AADJA,CAAAA,EAAW9J,AAHF,IAAI,CAGGmK,QAAQ,CAAC9N,EAAM2B,iBAAiB,CAAC3B,EAAMH,MAAM,CAAC4B,QAAQ,EAAC,EACxDmM,EACXH,EAAWG,EAAU,EAEhBH,EAAWG,EAAUC,GAC1BJ,CAAAA,EAAWG,EAAUC,EAAU,CAAA,EAEnC7N,EAAM0N,KAAK,CAAGD,EACdzN,EAAM2N,KAAK,CAAGhK,AAXL,IAAI,CAWMkG,GAAG,CAAG4D,EACzB,KAAK,CAACD,cAAc7O,EAAGqB,GACvBA,EAAM0N,KAAK,CAAGA,EACd1N,EAAM2N,KAAK,CAAGA,EACVhK,AAfK,IAAI,CAeJoK,KAAK,EACV,CAACpK,AAhBI,IAAI,CAgBHoK,KAAK,CAACC,gBAAgB,EAC5B3N,EAAW2J,KAAK,GAChBrG,AAlBK,IAAI,CAkBJoK,KAAK,CACLE,QAAQ,CAAC,+BACTlC,GAAG,CAAC1L,EAAW2J,KAAK,EACzBrG,AArBK,IAAI,CAqBJoK,KAAK,CAACC,gBAAgB,CAAG,CAAA,EACzBrK,AAtBA,IAAI,CAsBC9E,KAAK,CAACuG,UAAU,EACtB,AAA0B,UAA1B,OAAOzB,AAvBN,IAAI,CAuBOwF,SAAS,EACrBxF,AAxBC,IAAI,CAwBAoK,KAAK,CAACxN,IAAI,CAAC,CACZC,KAAMmD,AAzBT,IAAI,CAyBUwF,SAAS,CAACrL,KAAK,AAC9B,IAIhB,CAIAoQ,gBAAgB7O,CAAO,CAAE,CACrB,IAAmBkK,EAAO5F,AAAb,IAAI,CAAc4F,IAAI,CAAEjH,EAAMjD,EAAQ8O,eAAe,CAAE3E,EAAM7F,AAA7D,IAAI,CAA8D6F,GAAG,CAElF,OAAO3C,EAASvE,GACXqB,AAHQ,IAAI,CAGP2E,KAAK,CAAG,CACV,CAAC,IAAKhG,EAAM,EAAGkH,EAAM,EAAE,CACvB,CAAC,IAAKlH,EAAM,EAAGkH,EAAM,EAAE,CACvB,CAAC,IAAKlH,EAAKkH,EAAI,CACf,CAAC,IAAI,CACR,CAAG,CACA,CAAC,IAAKD,EAAMjH,EAAI,CAChB,CAAC,IAAKiH,EAAO,EAAGjH,EAAM,EAAE,CACxB,CAAC,IAAKiH,EAAO,EAAGjH,EAAM,EAAE,CACxB,CAAC,IAAI,CACR,CACD,KAAK,CAAC4L,gBAAgB7O,EAC9B,CAiBAqB,OAAO0N,CAAU,CAAEzN,CAAM,CAAE,CACvB,IAAuCoB,EAASlD,AAArB8E,AAAd,IAAI,CAAe9E,KAAK,CAAiBkD,MAAM,CAC5D,IAAI,CAAClC,MAAM,CAACP,OAAO,CAAC,AAACO,IAEjBA,EAAOoE,WAAW,CAAG,CAAA,CACzB,GAGImK,CAAAA,EAAW5O,WAAW,EAAIuC,EAAO9C,QAAQ,EAAI0E,AAPpC,IAAI,CAOqCnE,WAAW,AAAD,GAC5DmE,AARS,IAAI,CAQR0K,YAAY,GAErB,KAAK,CAAC3N,OAAO0N,EAAYzN,GACrBgD,AAXS,IAAI,CAWRtD,UAAU,EAAEyB,QACjB6B,AAZS,IAAI,CAYRkH,cAAc,GACnB9I,EAAOC,YAAY,CAAC,IAAI,CAAE,CAAA,GAElC,CAKAqM,cAAe,CACX,IAAmBxP,EAAQ8E,AAAd,IAAI,CAAe9E,KAAK,CAAEwB,EAAasD,AAAvC,IAAI,CAAwCtD,UAAU,EAAI,CAAC,EACxE,GAAIA,EAAWyB,KAAK,CAChBjD,EAAMkD,MAAM,CAACjD,WAAW,CAFf,IAAI,OAIZ,GAAIuB,EAAW6E,MAAM,CACtB,IAAK,IAAMnG,KAAQsB,EAAW6E,MAAM,CAChCrG,EAAMkD,MAAM,CAACjD,WAAW,CAACC,EAGjCF,CAAAA,EAAMiF,aAAa,CAAG,CAAA,CAC1B,CAEAwK,SAAU,CACN,IAAI,CAACzP,KAAK,CAACiF,aAAa,CAAG,CAAA,EAC3B,IAAI,CAACuK,YAAY,GACjB,KAAK,CAACC,WAAW,EAAE,CAACC,KAAK,CAACnR,IAAI,CAACsG,WACnC,CASA8K,OAAO7N,CAAM,CAAE,CACX,IAAI,CAAC0N,YAAY,GACjB,KAAK,CAACG,OAAO7N,EACjB,CAKAhB,2BAA4B,CACxB,IAOI8O,EAPE9K,EAAO,IAAI,CAAE9E,EAAQ8E,EAAK9E,KAAK,CAAE6P,EAAe/K,EAAKtD,UAAU,EACjEsD,EAAKtD,UAAU,CAAC6E,MAAM,EACtB,EAAE,CAAGqG,EAAgB1M,EAAMQ,OAAO,CAAC0C,MAAM,CAAE4M,EAAgB5H,EAAewE,EAAcoD,aAAa,CAAE,IAAKC,EAAc7H,EAAewE,EAAcqD,WAAW,CAAE,IAClKC,EAAuB,AAAC7P,GAAM2E,EAAK9D,MAAM,CAACiP,MAAM,CAAC,CAAC/O,EAAQgP,KAC5DhP,EAAOH,IAAI,IAAImP,EAAEhP,MAAM,CAACiP,MAAM,CAAC,AAAChP,GAAUA,EAAM8E,SAAS,GAAK9F,IACvDe,GACR,EAAE,EA2DL,OAzDK2O,EAAYzO,MAAM,EACnB0D,EAAKnE,WAAW,CAACF,OAAO,CAAC,CAACwF,EAAW9F,KACjC,IAAMgH,EAAOlB,EAAUkB,IAAI,CAAEC,EAAKnB,EAAUmB,EAAE,CAAE,CAAEgJ,gBAAAA,CAAe,CAAE,CAAGpQ,EAClEqC,EAAM,CAAA,EAGVuN,EAAO,GACH,AAAgB,KAAA,IAATzI,EACPyI,EAAO,KAEY,KAAA,IAAPxI,GACZwI,CAAAA,EAAO,IAAG,EAEM,KAAA,IAATzI,GACPyI,CAAAA,GAAQQ,EAAgBjJ,EAAM2I,GAAiBC,CAAU,EAEzC,KAAA,IAAT5I,GAAwB,AAAc,KAAA,IAAPC,GACtCwI,CAAAA,GAAQ,KAAI,EAEE,KAAA,IAAPxI,GACPwI,CAAAA,GAAQQ,EAAgBhJ,EAAI0I,GAAiBC,CAAU,EAG3DF,EAAY9O,IAAI,CAAC8G,EAAiB,CAC9B7H,MAAAA,EACA4P,KAAAA,EACApP,QAAS,CAAC,EACV+K,iBAAkB,AAAChE,IAAmI8I,SAAS,CAC/JzP,QAAS,CAAA,EACT0P,YAAa,CAAA,EAGb7C,SAAU,AAACC,IACP,IAAK,IAAMvM,KAAS6O,EAAqB7P,GACrCgB,EAAMsM,QAAQ,CAACC,EAEvB,EAGAhI,WAAY,WACR,IAAI,CAAC9E,OAAO,CAAGyB,EAAMyC,EAAKlE,OAAO,CAAG,CAACyB,EACrC,IAAMkO,EAAiB,EAAE,CACzB,IAAK,IAAMpP,KAAS6O,EAAqB7P,GACrCgB,EAAMuE,UAAU,CAACrD,GACjBlB,EAAMqP,iBAAiB,CAAG,CAACnO,EACkB,KAAzCkO,EAAelQ,OAAO,CAACc,EAAMH,MAAM,GACnCuP,EAAexP,IAAI,CAACI,EAAMH,MAAM,EAGxChB,EAAMkD,MAAM,CAACC,YAAY,CAAC,IAAI,CAAEd,GAChCkO,EAAe9P,OAAO,CAAC,AAACO,IACpB8G,EAAU9G,EAAQ,4BACtB,EACJ,CACJ,EAAGiF,GACP,GAEG4J,CACX,CAKApF,SAAU,CACN,GAAmB,CAAEzK,MAAAA,CAAK,CAAEyJ,MAAAA,CAAK,CAAE,CAAtB,IAAI,CAA2B,CAAEe,OAAQiG,CAAe,CAAEzH,MAAO0H,CAAc,CAAE,CAAG5L,AAApF,IAAI,CAAqFtE,OAAO,CAAE,CAAE0C,OAAQwJ,CAAa,CAAE,CAAG1M,EAAMQ,OAAO,CAGxJ,MAAO,CACHwI,MAJ8Jd,EAAeN,EAAQ8I,GACrLvI,EAAeuI,EAAgB1Q,EAAM6K,UAAU,EAAI,KAAK,EAAG6B,GAAeiE,YAAalH,EAAQJ,EAAU4B,mBAAmB,CAAG,IAI/HT,OAJ6ItC,EAAeN,EAAQ6I,GACpKtI,EAAesI,EAAiBzQ,EAAM+K,WAAW,EAAI,KAAK,EAAG2B,GAAekE,aAAcnH,EAAQ,GAAKJ,EAAU4B,mBAAmB,CAIxI,CACJ,CACJ,CAMA5B,EAAU4B,mBAAmB,CAAG,IAIhC5B,EAAUwH,SAAS,CAAG,CAClB,aACH,CACDhJ,EAAiBwB,EAAUhL,SAAS,CAAEgJ,GAOtCyJ,MAAMzS,SAAS,CAAC0C,IAAI,CAAC6D,KAAK,CAAC,AAAC/F,IAAmGgS,SAAS,CAAExH,EAAUwH,SAAS,EAwB7J,IAAME,EAAKpS,GACXoS,CAAAA,EAAE1H,SAAS,CAAG0H,EAAE1H,SAAS,EAnB6BA,EAoBtD0H,EAAE1H,SAAS,CAAC1F,OAAO,CAACoN,EAAEC,KAAK,CAAED,EAAEE,EAAE,CAAEF,EAAEG,MAAM,CAAEH,EAAEpJ,MAAM,EACxB,IAAMlJ,EAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}