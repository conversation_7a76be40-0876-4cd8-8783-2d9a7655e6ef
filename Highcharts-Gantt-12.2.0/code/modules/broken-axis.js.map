{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/broken-axis\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"StackItem\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/broken-axis\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"StackItem\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/broken-axis\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"StackItem\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"StackItem\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__184__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 184:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__184__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ broken_axis_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"StackItem\"],\"commonjs\":[\"highcharts\",\"StackItem\"],\"commonjs2\":[\"highcharts\",\"StackItem\"],\"root\":[\"Highcharts\",\"StackItem\"]}\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_ = __webpack_require__(184);\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default = /*#__PURE__*/__webpack_require__.n(highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_);\n;// ./code/es-modules/Core/Axis/BrokenAxis.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { addEvent, find, fireEvent, isArray, isNumber, pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\n/**\n * Axis with support of broken data rows.\n * @private\n */\nvar BrokenAxis;\n(function (BrokenAxis) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds support for broken axes.\n     * @private\n     */\n    function compose(AxisClass, SeriesClass) {\n        if (!AxisClass.keepProps.includes('brokenAxis')) {\n            AxisClass.keepProps.push('brokenAxis');\n            addEvent(AxisClass, 'init', onAxisInit);\n            addEvent(AxisClass, 'afterInit', onAxisAfterInit);\n            addEvent(AxisClass, 'afterSetTickPositions', onAxisAfterSetTickPositions);\n            addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n            const seriesProto = SeriesClass.prototype;\n            seriesProto.drawBreaks = seriesDrawBreaks;\n            seriesProto.gappedPath = seriesGappedPath;\n            addEvent(SeriesClass, 'afterGeneratePoints', onSeriesAfterGeneratePoints);\n            addEvent(SeriesClass, 'afterRender', onSeriesAfterRender);\n        }\n        return AxisClass;\n    }\n    BrokenAxis.compose = compose;\n    /**\n     * @private\n     */\n    function onAxisAfterInit() {\n        if (typeof this.brokenAxis !== 'undefined') {\n            this.brokenAxis.setBreaks(this.options.breaks, false);\n        }\n    }\n    /**\n     * Force Axis to be not-ordinal when breaks are defined.\n     * @private\n     */\n    function onAxisAfterSetOptions() {\n        const axis = this;\n        if (axis.brokenAxis?.hasBreaks) {\n            axis.options.ordinal = false;\n        }\n    }\n    /**\n     * @private\n     */\n    function onAxisAfterSetTickPositions() {\n        const axis = this, brokenAxis = axis.brokenAxis;\n        if (brokenAxis?.hasBreaks) {\n            const tickPositions = axis.tickPositions, info = axis.tickPositions.info, newPositions = [];\n            for (let i = 0; i < tickPositions.length; i++) {\n                if (!brokenAxis.isInAnyBreak(tickPositions[i])) {\n                    newPositions.push(tickPositions[i]);\n                }\n            }\n            axis.tickPositions = newPositions;\n            axis.tickPositions.info = info;\n        }\n    }\n    /**\n     * @private\n     */\n    function onAxisInit() {\n        const axis = this;\n        if (!axis.brokenAxis) {\n            axis.brokenAxis = new Additions(axis);\n        }\n    }\n    /**\n     * @private\n     */\n    function onSeriesAfterGeneratePoints() {\n        const { isDirty, options: { connectNulls }, points, xAxis, yAxis } = this;\n        // Set, or reset visibility of the points. Axis.setBreaks marks\n        // the series as isDirty\n        if (isDirty) {\n            let i = points.length;\n            while (i--) {\n                const point = points[i];\n                // Respect nulls inside the break (#4275)\n                const nullGap = point.y === null && connectNulls === false;\n                const isPointInBreak = (!nullGap && (xAxis?.brokenAxis?.isInAnyBreak(point.x, true) ||\n                    yAxis?.brokenAxis?.isInAnyBreak(point.y, true)));\n                // Set point.visible if in any break.\n                // If not in break, reset visible to original value.\n                point.visible = isPointInBreak ?\n                    false :\n                    point.options.visible !== false;\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    function onSeriesAfterRender() {\n        this.drawBreaks(this.xAxis, ['x']);\n        this.drawBreaks(this.yAxis, pick(this.pointArrayMap, ['y']));\n    }\n    /**\n     * @private\n     */\n    function seriesDrawBreaks(axis, keys) {\n        const series = this, points = series.points;\n        let breaks, threshold, y;\n        if (axis?.brokenAxis?.hasBreaks) {\n            const brokenAxis = axis.brokenAxis;\n            keys.forEach(function (key) {\n                breaks = brokenAxis?.breakArray || [];\n                threshold = axis.isXAxis ?\n                    axis.min :\n                    pick(series.options.threshold, axis.min);\n                // Array of breaks that have been \"zoomed-out\" which means that\n                // they were shown previously, but now after zoom, they are not\n                // (#19885).\n                const breaksOutOfRange = axis?.options?.breaks?.filter(function (brk) {\n                    let isOut = true;\n                    // Iterate to see if \"brk\" is in axis range\n                    for (let i = 0; i < breaks.length; i++) {\n                        const otherBreak = breaks[i];\n                        if (otherBreak.from === brk.from &&\n                            otherBreak.to === brk.to) {\n                            isOut = false;\n                            break;\n                        }\n                    }\n                    return isOut;\n                });\n                points.forEach(function (point) {\n                    y = pick(point['stack' + key.toUpperCase()], point[key]);\n                    breaks.forEach(function (brk) {\n                        if (isNumber(threshold) && isNumber(y)) {\n                            let eventName = '';\n                            if ((threshold < brk.from && y > brk.to) ||\n                                (threshold > brk.from && y < brk.from)) {\n                                eventName = 'pointBreak';\n                            }\n                            else if ((threshold < brk.from &&\n                                y > brk.from &&\n                                y < brk.to) || (threshold > brk.from &&\n                                y > brk.to &&\n                                y < brk.from)) {\n                                eventName = 'pointInBreak';\n                            }\n                            if (eventName) {\n                                fireEvent(axis, eventName, { point, brk });\n                            }\n                        }\n                    });\n                    breaksOutOfRange?.forEach(function (brk) {\n                        fireEvent(axis, 'pointOutsideOfBreak', { point, brk });\n                    });\n                });\n            });\n        }\n    }\n    /**\n     * Extend getGraphPath by identifying gaps in the data so that we\n     * can draw a gap in the line or area. This was moved from ordinal\n     * axis module to broken axis module as of #5045.\n     *\n     * @private\n     * @function Highcharts.Series#gappedPath\n     *\n     * @return {Highcharts.SVGPathArray}\n     * Gapped path\n     */\n    function seriesGappedPath() {\n        const currentDataGrouping = this.currentDataGrouping, groupingSize = currentDataGrouping?.gapSize, points = this.points.slice(), yAxis = this.yAxis;\n        let gapSize = this.options.gapSize, i = points.length - 1, stack;\n        /**\n         * Defines when to display a gap in the graph, together with the\n         * [gapUnit](plotOptions.series.gapUnit) option.\n         *\n         * In case when `dataGrouping` is enabled, points can be grouped\n         * into a larger time span. This can make the grouped points to\n         * have a greater distance than the absolute value of `gapSize`\n         * property, which will result in disappearing graph completely.\n         * To prevent this situation the mentioned distance between\n         * grouped points is used instead of previously defined\n         * `gapSize`.\n         *\n         * In practice, this option is most often used to visualize gaps\n         * in time series. In a stock chart, intraday data is available\n         * for daytime hours, while gaps will appear in nights and\n         * weekends.\n         *\n         * @see [gapUnit](plotOptions.series.gapUnit)\n         * @see [xAxis.breaks](#xAxis.breaks)\n         *\n         * @sample {highstock} stock/plotoptions/series-gapsize/\n         * Setting the gap size to 2 introduces gaps for weekends in\n         * daily datasets.\n         *\n         * @type      {number}\n         * @default   0\n         * @product   highstock\n         * @requires  modules/broken-axis\n         * @apioption plotOptions.series.gapSize\n         */\n        /**\n         * Together with [gapSize](plotOptions.series.gapSize), this\n         * option defines where to draw gaps in the graph.\n         *\n         * When the `gapUnit` is `\"relative\"` (default), a gap size of 5\n         * means that if the distance between two points is greater than\n         * 5 times that of the two closest points, the graph will be\n         * broken.\n         *\n         * When the `gapUnit` is `\"value\"`, the gap is based on absolute\n         * axis values, which on a datetime axis is milliseconds. This\n         * also applies to the navigator series that inherits gap\n         * options from the base series.\n         *\n         * @see [gapSize](plotOptions.series.gapSize)\n         *\n         * @type       {string}\n         * @default    relative\n         * @since      5.0.13\n         * @product    highstock\n         * @validvalue [\"relative\", \"value\"]\n         * @requires   modules/broken-axis\n         * @apioption  plotOptions.series.gapUnit\n         */\n        if (gapSize && i > 0) { // #5008\n            // Gap unit is relative\n            if (this.options.gapUnit !== 'value') {\n                gapSize *= this.basePointRange;\n            }\n            // Setting a new gapSize in case dataGrouping is enabled\n            // (#7686)\n            if (groupingSize &&\n                groupingSize > gapSize &&\n                // Except when DG is forced (e.g. from other series)\n                // and has lower granularity than actual points (#11351)\n                groupingSize >= this.basePointRange) {\n                gapSize = groupingSize;\n            }\n            // Extension for ordinal breaks\n            let current, next;\n            while (i--) {\n                // Reassign next if it is not visible\n                if (!(next && next.visible !== false)) {\n                    next = points[i + 1];\n                }\n                current = points[i];\n                // Skip iteration if one of the points is not visible\n                if (next.visible === false || current.visible === false) {\n                    continue;\n                }\n                if (next.x - current.x > gapSize) {\n                    const xRange = (current.x + next.x) / 2;\n                    points.splice(// Insert after this one\n                    i + 1, 0, {\n                        isNull: true,\n                        x: xRange\n                    });\n                    // For stacked chart generate empty stack items, #6546\n                    if (yAxis.stacking && this.options.stacking) {\n                        stack = yAxis.stacking.stacks[this.stackKey][xRange] = new (highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default())(yAxis, yAxis.options.stackLabels, false, xRange, this.stack);\n                        stack.total = 0;\n                    }\n                }\n                // Assign current to next for the upcoming iteration\n                next = current;\n            }\n        }\n        // Call base method\n        return this.getGraphPath(points);\n    }\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Provides support for broken axes.\n     * @private\n     * @class\n     */\n    class Additions {\n        /* *\n         *\n         *  Static Functions\n         *\n         * */\n        /**\n         * @private\n         */\n        static isInBreak(brk, val) {\n            const repeat = brk.repeat || Infinity, from = brk.from, length = brk.to - brk.from, test = (val >= from ?\n                (val - from) % repeat :\n                repeat - ((from - val) % repeat));\n            let ret;\n            if (!brk.inclusive) {\n                ret = test < length && test !== 0;\n            }\n            else {\n                ret = test <= length;\n            }\n            return ret;\n        }\n        /**\n         * @private\n         */\n        static lin2Val(val) {\n            const axis = this;\n            const brokenAxis = axis.brokenAxis;\n            const breakArray = brokenAxis?.breakArray;\n            if (!breakArray || !isNumber(val)) {\n                return val;\n            }\n            let nval = val, brk, i;\n            for (i = 0; i < breakArray.length; i++) {\n                brk = breakArray[i];\n                if (brk.from >= nval) {\n                    break;\n                }\n                else if (brk.to < nval) {\n                    nval += brk.len;\n                }\n                else if (Additions.isInBreak(brk, nval)) {\n                    nval += brk.len;\n                }\n            }\n            return nval;\n        }\n        /**\n         * @private\n         */\n        static val2Lin(val) {\n            const axis = this;\n            const brokenAxis = axis.brokenAxis;\n            const breakArray = brokenAxis?.breakArray;\n            if (!breakArray || !isNumber(val)) {\n                return val;\n            }\n            let nval = val, brk, i;\n            for (i = 0; i < breakArray.length; i++) {\n                brk = breakArray[i];\n                if (brk.to <= val) {\n                    nval -= brk.len;\n                }\n                else if (brk.from >= val) {\n                    break;\n                }\n                else if (Additions.isInBreak(brk, val)) {\n                    nval -= (val - brk.from);\n                    break;\n                }\n            }\n            return nval;\n        }\n        /* *\n         *\n         *  Constructors\n         *\n         * */\n        constructor(axis) {\n            this.hasBreaks = false;\n            this.axis = axis;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Returns the first break found where the x is larger then break.from\n         * and smaller then break.to.\n         *\n         * @param {number} x\n         * The number which should be within a break.\n         *\n         * @param {Array<Highcharts.XAxisBreaksOptions>} breaks\n         * The array of breaks to search within.\n         *\n         * @return {Highcharts.XAxisBreaksOptions|undefined}\n         * Returns the first break found that matches, returns false if no break\n         * is found.\n         */\n        findBreakAt(x, breaks) {\n            return find(breaks, function (b) {\n                return b.from < x && x < b.to;\n            });\n        }\n        /**\n         * @private\n         */\n        isInAnyBreak(val, testKeep) {\n            const brokenAxis = this, axis = brokenAxis.axis, breaks = axis.options.breaks || [];\n            let i = breaks.length, inbrk, keep, ret;\n            if (i && isNumber(val)) {\n                while (i--) {\n                    if (Additions.isInBreak(breaks[i], val)) {\n                        inbrk = true;\n                        if (!keep) {\n                            keep = pick(breaks[i].showPoints, !axis.isXAxis);\n                        }\n                    }\n                }\n                if (inbrk && testKeep) {\n                    ret = inbrk && !keep;\n                }\n                else {\n                    ret = inbrk;\n                }\n            }\n            return ret;\n        }\n        /**\n         * Dynamically set or unset breaks in an axis. This function in lighter\n         * than using Axis.update, and it also preserves animation.\n         *\n         * @private\n         * @function Highcharts.Axis#setBreaks\n         *\n         * @param {Array<Highcharts.XAxisBreaksOptions>} [breaks]\n         * The breaks to add. When `undefined` it removes existing breaks.\n         *\n         * @param {boolean} [redraw=true]\n         * Whether to redraw the chart immediately.\n         */\n        setBreaks(breaks, redraw) {\n            const brokenAxis = this, axis = brokenAxis.axis, time = axis.chart.time, hasBreaks = isArray(breaks) &&\n                !!breaks.length &&\n                !!Object.keys(breaks[0]).length; // Check for [{}], #16368.\n            axis.isDirty = brokenAxis.hasBreaks !== hasBreaks;\n            brokenAxis.hasBreaks = hasBreaks;\n            // Compile string dates\n            breaks?.forEach((brk) => {\n                brk.from = time.parse(brk.from) || 0;\n                brk.to = time.parse(brk.to) || 0;\n            });\n            if (breaks !== axis.options.breaks) {\n                axis.options.breaks = axis.userOptions.breaks = breaks;\n            }\n            axis.forceRedraw = true; // Force recalculation in setScale\n            // Recalculate series related to the axis.\n            axis.series.forEach(function (series) {\n                series.isDirty = true;\n            });\n            if (!hasBreaks && axis.val2lin === Additions.val2Lin) {\n                // Revert to prototype functions\n                delete axis.val2lin;\n                delete axis.lin2val;\n            }\n            if (hasBreaks) {\n                axis.userOptions.ordinal = false;\n                axis.lin2val = Additions.lin2Val;\n                axis.val2lin = Additions.val2Lin;\n                axis.setExtremes = function (newMin, newMax, redraw, animation, eventArguments) {\n                    // If trying to set extremes inside a break, extend min to\n                    // after, and max to before the break ( #3857 )\n                    if (brokenAxis.hasBreaks) {\n                        const breaks = (this.options.breaks || []);\n                        let axisBreak;\n                        while ((axisBreak = brokenAxis.findBreakAt(newMin, breaks))) {\n                            newMin = axisBreak.to;\n                        }\n                        while ((axisBreak = brokenAxis.findBreakAt(newMax, breaks))) {\n                            newMax = axisBreak.from;\n                        }\n                        // If both min and max is within the same break.\n                        if (newMax < newMin) {\n                            newMax = newMin;\n                        }\n                    }\n                    axis.constructor.prototype.setExtremes.call(this, newMin, newMax, redraw, animation, eventArguments);\n                };\n                axis.setAxisTranslation = function () {\n                    axis.constructor.prototype.setAxisTranslation.call(this);\n                    brokenAxis.unitLength = void 0;\n                    if (brokenAxis.hasBreaks) {\n                        const breaks = axis.options.breaks || [], \n                        // Temporary one:\n                        breakArrayT = [], breakArray = [], pointRangePadding = pick(axis.pointRangePadding, 0);\n                        let length = 0, inBrk, repeat, min = axis.userMin || axis.min, max = axis.userMax || axis.max, start, i;\n                        // Min & max check (#4247)\n                        breaks.forEach(function (brk) {\n                            repeat = brk.repeat || Infinity;\n                            if (isNumber(min) && isNumber(max)) {\n                                if (Additions.isInBreak(brk, min)) {\n                                    min += ((brk.to % repeat) -\n                                        (min % repeat));\n                                }\n                                if (Additions.isInBreak(brk, max)) {\n                                    max -= ((max % repeat) -\n                                        (brk.from % repeat));\n                                }\n                            }\n                        });\n                        // Construct an array holding all breaks in the axis\n                        breaks.forEach(function (brk) {\n                            start = brk.from;\n                            repeat = brk.repeat || Infinity;\n                            if (isNumber(min) && isNumber(max)) {\n                                while (start - repeat > min) {\n                                    start -= repeat;\n                                }\n                                while (start < min) {\n                                    start += repeat;\n                                }\n                                for (i = start; i < max; i += repeat) {\n                                    breakArrayT.push({\n                                        value: i,\n                                        move: 'in'\n                                    });\n                                    breakArrayT.push({\n                                        value: i + brk.to - brk.from,\n                                        move: 'out',\n                                        size: brk.breakSize\n                                    });\n                                }\n                            }\n                        });\n                        breakArrayT.sort(function (a, b) {\n                            return ((a.value === b.value) ?\n                                ((a.move === 'in' ? 0 : 1) -\n                                    (b.move === 'in' ? 0 : 1)) :\n                                a.value - b.value);\n                        });\n                        // Simplify the breaks\n                        inBrk = 0;\n                        start = min;\n                        breakArrayT.forEach(function (brk) {\n                            inBrk += (brk.move === 'in' ? 1 : -1);\n                            if (inBrk === 1 && brk.move === 'in') {\n                                start = brk.value;\n                            }\n                            if (inBrk === 0 && isNumber(start)) {\n                                breakArray.push({\n                                    from: start,\n                                    to: brk.value,\n                                    len: brk.value - start - (brk.size || 0)\n                                });\n                                length += (brk.value -\n                                    start -\n                                    (brk.size || 0));\n                            }\n                        });\n                        brokenAxis.breakArray = breakArray;\n                        // Used with staticScale, and below the actual axis\n                        // length, when breaks are subtracted.\n                        if (isNumber(min) &&\n                            isNumber(max) &&\n                            isNumber(axis.min)) {\n                            brokenAxis.unitLength = max - min - length +\n                                pointRangePadding;\n                            fireEvent(axis, 'afterBreaks');\n                            if (axis.staticScale) {\n                                axis.transA = axis.staticScale;\n                            }\n                            else if (brokenAxis.unitLength) {\n                                axis.transA *=\n                                    (max - axis.min + pointRangePadding) /\n                                        brokenAxis.unitLength;\n                            }\n                            if (pointRangePadding) {\n                                axis.minPixelPadding =\n                                    axis.transA * (axis.minPointOffset || 0);\n                            }\n                            axis.min = min;\n                            axis.max = max;\n                        }\n                    }\n                };\n            }\n            if (pick(redraw, true)) {\n                axis.chart.redraw();\n            }\n        }\n    }\n    BrokenAxis.Additions = Additions;\n})(BrokenAxis || (BrokenAxis = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Axis_BrokenAxis = (BrokenAxis);\n\n;// ./code/es-modules/masters/modules/broken-axis.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.BrokenAxis = G.BrokenAxis || Axis_BrokenAxis;\nG.BrokenAxis.compose(G.Axis, G.Series);\n/* harmony default export */ const broken_axis_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__184__", "BrokenAxis", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "broken_axis_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default", "addEvent", "find", "fireEvent", "isArray", "isNumber", "pick", "onAxisAfterInit", "broken<PERSON><PERSON>s", "setBreaks", "options", "breaks", "onAxisAfterSetOptions", "axis", "hasBreaks", "ordinal", "onAxisAfterSetTickPositions", "tickPositions", "info", "newPositions", "i", "length", "isInAnyBreak", "push", "onAxisInit", "Additions", "onSeriesAfterGeneratePoints", "isDirty", "connectNulls", "points", "xAxis", "yAxis", "point", "isPointInBreak", "y", "x", "visible", "onSeriesAfterRender", "drawBreaks", "pointArrayMap", "seriesDrawBreaks", "keys", "threshold", "series", "for<PERSON>ach", "breakArray", "isXAxis", "min", "breaksOutOfRange", "filter", "brk", "isOut", "otherBreak", "from", "to", "toUpperCase", "eventName", "seriesGappedPath", "currentDataGrouping", "groupingSize", "gapSize", "slice", "current", "next", "gapUnit", "basePointRange", "xRange", "splice", "isNull", "stacking", "stack", "stacks", "<PERSON><PERSON><PERSON>", "stackLabels", "total", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "compose", "AxisClass", "SeriesClass", "keepProps", "includes", "seriesProto", "gappedPath", "isInBreak", "val", "ret", "repeat", "Infinity", "test", "inclusive", "lin2Val", "nval", "len", "val2Lin", "constructor", "findBreakAt", "b", "testKeep", "inbrk", "keep", "showPoints", "redraw", "time", "chart", "parse", "userOptions", "forceRedraw", "val2lin", "lin2val", "setExtremes", "newMin", "newMax", "animation", "eventArguments", "axisBreak", "setAxisTranslation", "unitLength", "breakArrayT", "pointRangePadding", "inBrk", "userMin", "max", "userMax", "start", "value", "move", "size", "breakSize", "sort", "staticScale", "transA", "minPixelPadding", "minPointOffset", "Axis_BrokenAxis", "G", "Axis", "Series"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,SAAY,EACvE,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,SAAY,CAAE,GACrH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,SAAY,EAEzGA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,SAAY,CAClF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IA6GNC,EA7GUC,EAAuB,CAE/B,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,AAACd,IACxB,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,IAAOhB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAAClB,EAASoB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+GzB,EAAoB,KACnI0B,EAAmI1B,EAAoBI,CAAC,CAACqB,GAc7J,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAIR,KAWhE,AAAC,SAAU3B,CAAU,EAkCjB,SAASoC,IAC0B,KAAA,IAApB,IAAI,CAACC,UAAU,EACtB,IAAI,CAACA,UAAU,CAACC,SAAS,CAAC,IAAI,CAACC,OAAO,CAACC,MAAM,CAAE,CAAA,EAEvD,CAKA,SAASC,IAEDC,AADS,IAAI,CACRL,UAAU,EAAEM,WACjBD,CAAAA,AAFS,IAAI,CAERH,OAAO,CAACK,OAAO,CAAG,CAAA,CAAI,CAEnC,CAIA,SAASC,IACL,IAAmBR,EAAaK,AAAnB,IAAI,CAAoBL,UAAU,CAC/C,GAAIA,GAAYM,UAAW,CACvB,IAAMG,EAAgBJ,AAFb,IAAI,CAEcI,aAAa,CAAEC,EAAOL,AAFxC,IAAI,CAEyCI,aAAa,CAACC,IAAI,CAAEC,EAAe,EAAE,CAC3F,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAcI,MAAM,CAAED,IACjCZ,EAAWc,YAAY,CAACL,CAAa,CAACG,EAAE,GACzCD,EAAaI,IAAI,CAACN,CAAa,CAACG,EAAE,CAG1CP,CARS,IAAI,CAQRI,aAAa,CAAGE,EACrBN,AATS,IAAI,CASRI,aAAa,CAACC,IAAI,CAAGA,CAC9B,CACJ,CAIA,SAASM,IAEAX,AADQ,IAAI,CACPL,UAAU,EAChBK,CAAAA,AAFS,IAAI,CAERL,UAAU,CAAG,IAAIiB,EAFb,IAAI,CAEuB,CAE5C,CAIA,SAASC,IACL,GAAM,CAAEC,QAAAA,CAAO,CAAEjB,QAAS,CAAEkB,aAAAA,CAAY,CAAE,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG,IAAI,CAGzE,GAAIJ,EAAS,CACT,IAAIP,EAAIS,EAAOR,MAAM,CACrB,KAAOD,KAAK,CACR,IAAMY,EAAQH,CAAM,CAACT,EAAE,CAGjBa,EAAkB,AADRD,CAAAA,AAAY,OAAZA,EAAME,CAAC,EAAaN,AAAiB,CAAA,IAAjBA,CAAqB,GACpBE,CAAAA,GAAOtB,YAAYc,aAAaU,EAAMG,CAAC,CAAE,CAAA,IAC1EJ,GAAOvB,YAAYc,aAAaU,EAAME,CAAC,CAAE,CAAA,EAAI,CAGjDF,CAAAA,EAAMI,OAAO,CAAGH,CAAAA,GAEZD,AAA0B,CAAA,IAA1BA,EAAMtB,OAAO,CAAC0B,OAAO,AAC7B,CACJ,CACJ,CAIA,SAASC,IACL,IAAI,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAAE,CAAC,IAAI,EACjC,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACP,KAAK,CAAEzB,EAAK,IAAI,CAACiC,aAAa,CAAE,CAAC,IAAI,EAC9D,CAIA,SAASC,EAAiB3B,CAAI,CAAE4B,CAAI,EAChC,IACI9B,EAAQ+B,EAAWR,EADjBS,EAAS,IAAI,CAAEd,EAASc,EAAOd,MAAM,CAE3C,GAAIhB,GAAML,YAAYM,UAAW,CAC7B,IAAMN,EAAaK,EAAKL,UAAU,CAClCiC,EAAKG,OAAO,CAAC,SAAU5D,CAAG,EACtB2B,EAASH,GAAYqC,YAAc,EAAE,CACrCH,EAAY7B,EAAKiC,OAAO,CACpBjC,EAAKkC,GAAG,CACRzC,EAAKqC,EAAOjC,OAAO,CAACgC,SAAS,CAAE7B,EAAKkC,GAAG,EAI3C,IAAMC,EAAmBnC,GAAMH,SAASC,QAAQsC,OAAO,SAAUC,CAAG,EAChE,IAAIC,EAAQ,CAAA,EAEZ,IAAK,IAAI/B,EAAI,EAAGA,EAAIT,EAAOU,MAAM,CAAED,IAAK,CACpC,IAAMgC,EAAazC,CAAM,CAACS,EAAE,CAC5B,GAAIgC,EAAWC,IAAI,GAAKH,EAAIG,IAAI,EAC5BD,EAAWE,EAAE,GAAKJ,EAAII,EAAE,CAAE,CAC1BH,EAAQ,CAAA,EACR,KACJ,CACJ,CACA,OAAOA,CACX,GACAtB,EAAOe,OAAO,CAAC,SAAUZ,CAAK,EAC1BE,EAAI5B,EAAK0B,CAAK,CAAC,QAAUhD,EAAIuE,WAAW,GAAG,CAAEvB,CAAK,CAAChD,EAAI,EACvD2B,EAAOiC,OAAO,CAAC,SAAUM,CAAG,EACxB,GAAI7C,EAASqC,IAAcrC,EAAS6B,GAAI,CACpC,IAAIsB,EAAY,EACZ,CAACd,EAAYQ,EAAIG,IAAI,EAAInB,EAAIgB,EAAII,EAAE,EAClCZ,EAAYQ,EAAIG,IAAI,EAAInB,EAAIgB,EAAIG,IAAI,CACrCG,EAAY,aAEP,CAAA,AAACd,EAAYQ,EAAIG,IAAI,EAC1BnB,EAAIgB,EAAIG,IAAI,EACZnB,EAAIgB,EAAII,EAAE,EAAMZ,EAAYQ,EAAIG,IAAI,EACpCnB,EAAIgB,EAAII,EAAE,EACVpB,EAAIgB,EAAIG,IAAI,GACZG,CAAAA,EAAY,cAAa,EAEzBA,GACArD,EAAUU,EAAM2C,EAAW,CAAExB,MAAAA,EAAOkB,IAAAA,CAAI,EAEhD,CACJ,GACAF,GAAkBJ,QAAQ,SAAUM,CAAG,EACnC/C,EAAUU,EAAM,sBAAuB,CAAEmB,MAAAA,EAAOkB,IAAAA,CAAI,EACxD,EACJ,EACJ,EACJ,CACJ,CAYA,SAASO,IACL,IAAMC,EAAsB,IAAI,CAACA,mBAAmB,CAAEC,EAAeD,GAAqBE,QAAS/B,EAAS,IAAI,CAACA,MAAM,CAACgC,KAAK,GAAI9B,EAAQ,IAAI,CAACA,KAAK,CAC/I6B,EAAU,IAAI,CAAClD,OAAO,CAACkD,OAAO,CAAExC,EAAIS,EAAOR,MAAM,CAAG,EAuDxD,GAAIuC,GAAWxC,EAAI,EAAG,KAed0C,EAASC,EACb,IAd6B,UAAzB,IAAI,CAACrD,OAAO,CAACsD,OAAO,EACpBJ,CAAAA,GAAW,IAAI,CAACK,cAAc,AAAD,EAI7BN,GACAA,EAAeC,GAGfD,GAAgB,IAAI,CAACM,cAAc,EACnCL,CAAAA,EAAUD,CAAW,EAIlBvC,KAOH,GALM2C,GAAQA,AAAiB,CAAA,IAAjBA,EAAK3B,OAAO,EACtB2B,CAAAA,EAAOlC,CAAM,CAACT,EAAI,EAAE,AAAD,EAEvB0C,EAAUjC,CAAM,CAACT,EAAE,CAEf2C,AAAiB,CAAA,IAAjBA,EAAK3B,OAAO,EAAc0B,AAAoB,CAAA,IAApBA,EAAQ1B,OAAO,EAG7C,GAAI2B,EAAK5B,CAAC,CAAG2B,EAAQ3B,CAAC,CAAGyB,EAAS,CAC9B,IAAMM,EAAS,AAACJ,CAAAA,EAAQ3B,CAAC,CAAG4B,EAAK5B,CAAC,AAADA,EAAK,EACtCN,EAAOsC,MAAM,CACb/C,EAAI,EAAG,EAAG,CACNgD,OAAQ,CAAA,EACRjC,EAAG+B,CACP,GAEInC,EAAMsC,QAAQ,EAAI,IAAI,CAAC3D,OAAO,CAAC2D,QAAQ,EAEvCC,CAAAA,AADQvC,CAAAA,EAAMsC,QAAQ,CAACE,MAAM,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACN,EAAO,CAAG,GAAKlE,CAAAA,GAAoH,EAAG+B,EAAOA,EAAMrB,OAAO,CAAC+D,WAAW,CAAE,CAAA,EAAOP,EAAQ,IAAI,CAACI,KAAK,CAAA,EACxOI,KAAK,CAAG,CAAA,CAEtB,CAEAX,EAAOD,EAEf,CAEA,OAAO,IAAI,CAACa,YAAY,CAAC9C,EAC7B,CApPA1D,EAAWyG,OAAO,CAflB,SAAiBC,CAAS,CAAEC,CAAW,EACnC,GAAI,CAACD,EAAUE,SAAS,CAACC,QAAQ,CAAC,cAAe,CAC7CH,EAAUE,SAAS,CAACxD,IAAI,CAAC,cACzBtB,EAAS4E,EAAW,OAAQrD,GAC5BvB,EAAS4E,EAAW,YAAatE,GACjCN,EAAS4E,EAAW,wBAAyB7D,GAC7Cf,EAAS4E,EAAW,kBAAmBjE,GACvC,IAAMqE,EAAcH,EAAYtF,SAAS,AACzCyF,CAAAA,EAAY3C,UAAU,CAAGE,EACzByC,EAAYC,UAAU,CAAGzB,EACzBxD,EAAS6E,EAAa,sBAAuBpD,GAC7CzB,EAAS6E,EAAa,cAAezC,EACzC,CACA,OAAOwC,CACX,CAgQA,OAAMpD,EASF,OAAO0D,UAAUjC,CAAG,CAAEkC,CAAG,CAAE,CACvB,IAGIC,EAHEC,EAASpC,EAAIoC,MAAM,EAAIC,IAAUlC,EAAOH,EAAIG,IAAI,CAAEhC,EAAS6B,EAAII,EAAE,CAAGJ,EAAIG,IAAI,CAAEmC,EAAQJ,GAAO/B,EAC/F,AAAC+B,CAAAA,EAAM/B,CAAG,EAAKiC,EACfA,EAAU,AAACjC,CAAAA,EAAO+B,CAAE,EAAKE,EAQ7B,OANKpC,EAAIuC,SAAS,CAIRD,GAAQnE,EAHRmE,EAAOnE,GAAUmE,AAAS,IAATA,CAM/B,CAIA,OAAOE,QAAQN,CAAG,CAAE,CAEhB,IAAM5E,EAAaK,AADN,IAAI,CACOL,UAAU,CAC5BqC,EAAarC,GAAYqC,WAC/B,GAAI,CAACA,GAAc,CAACxC,EAAS+E,GACzB,OAAOA,EAEX,IAAIO,EAAOP,EAAKlC,EAAK9B,EACrB,IAAKA,EAAI,EAEL,AAFQA,EAAIyB,EAAWxB,MAAM,GAEzB6B,CAAAA,AADJA,CAAAA,EAAML,CAAU,CAACzB,EAAE,AAAD,EACViC,IAAI,EAAIsC,CAAG,EAFYvE,IAKtB8B,EAAII,EAAE,CAAGqC,EACdA,GAAQzC,EAAI0C,GAAG,CAEVnE,EAAU0D,SAAS,CAACjC,EAAKyC,IAC9BA,CAAAA,GAAQzC,EAAI0C,GAAG,AAAD,EAGtB,OAAOD,CACX,CAIA,OAAOE,QAAQT,CAAG,CAAE,CAEhB,IAAM5E,EAAaK,AADN,IAAI,CACOL,UAAU,CAC5BqC,EAAarC,GAAYqC,WAC/B,GAAI,CAACA,GAAc,CAACxC,EAAS+E,GACzB,OAAOA,EAEX,IAAIO,EAAOP,EAAKlC,EAAK9B,EACrB,IAAKA,EAAI,EAAGA,EAAIyB,EAAWxB,MAAM,CAAED,IAE/B,GAAI8B,AADJA,CAAAA,EAAML,CAAU,CAACzB,EAAE,AAAD,EACVkC,EAAE,EAAI8B,EACVO,GAAQzC,EAAI0C,GAAG,MAEd,GAAI1C,EAAIG,IAAI,EAAI+B,EACjB,WAEC,GAAI3D,EAAU0D,SAAS,CAACjC,EAAKkC,GAAM,CACpCO,GAASP,EAAMlC,EAAIG,IAAI,CACvB,KACJ,CAEJ,OAAOsC,CACX,CAMAG,YAAYjF,CAAI,CAAE,CACd,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACD,IAAI,CAAGA,CAChB,CAoBAkF,YAAY5D,CAAC,CAAExB,CAAM,CAAE,CACnB,OAAOT,EAAKS,EAAQ,SAAUqF,CAAC,EAC3B,OAAOA,EAAE3C,IAAI,CAAGlB,GAAKA,EAAI6D,EAAE1C,EAAE,AACjC,EACJ,CAIAhC,aAAa8D,CAAG,CAAEa,CAAQ,CAAE,CACxB,IAAyBpF,EAAOL,AAAb,IAAI,CAAoBK,IAAI,CAAEF,EAASE,EAAKH,OAAO,CAACC,MAAM,EAAI,EAAE,CAC/ES,EAAIT,EAAOU,MAAM,CAAE6E,EAAOC,EAAMd,EACpC,GAAIjE,GAAKf,EAAS+E,GAAM,CACpB,KAAOhE,KACCK,EAAU0D,SAAS,CAACxE,CAAM,CAACS,EAAE,CAAEgE,KAC/Bc,EAAQ,CAAA,EACHC,GACDA,CAAAA,EAAO7F,EAAKK,CAAM,CAACS,EAAE,CAACgF,UAAU,CAAE,CAACvF,EAAKiC,OAAO,CAAA,GAKvDuC,EADAa,GAASD,EACHC,GAAS,CAACC,EAGVD,CAEd,CACA,OAAOb,CACX,CAcA5E,UAAUE,CAAM,CAAE0F,CAAM,CAAE,CACtB,IAAM7F,EAAa,IAAI,CAAEK,EAAOL,EAAWK,IAAI,CAAEyF,EAAOzF,EAAK0F,KAAK,CAACD,IAAI,CAAExF,EAAYV,EAAQO,IACzF,CAAC,CAACA,EAAOU,MAAM,EACf,CAAC,CAACnC,OAAOuD,IAAI,CAAC9B,CAAM,CAAC,EAAE,EAAEU,MAAM,AACnCR,CAAAA,EAAKc,OAAO,CAAGnB,EAAWM,SAAS,GAAKA,EACxCN,EAAWM,SAAS,CAAGA,EAEvBH,GAAQiC,QAAQ,AAACM,IACbA,EAAIG,IAAI,CAAGiD,EAAKE,KAAK,CAACtD,EAAIG,IAAI,GAAK,EACnCH,EAAII,EAAE,CAAGgD,EAAKE,KAAK,CAACtD,EAAII,EAAE,GAAK,CACnC,GACI3C,IAAWE,EAAKH,OAAO,CAACC,MAAM,EAC9BE,CAAAA,EAAKH,OAAO,CAACC,MAAM,CAAGE,EAAK4F,WAAW,CAAC9F,MAAM,CAAGA,CAAK,EAEzDE,EAAK6F,WAAW,CAAG,CAAA,EAEnB7F,EAAK8B,MAAM,CAACC,OAAO,CAAC,SAAUD,CAAM,EAChCA,EAAOhB,OAAO,CAAG,CAAA,CACrB,GACKb,GAAaD,EAAK8F,OAAO,GAAKlF,EAAUoE,OAAO,GAEhD,OAAOhF,EAAK8F,OAAO,CACnB,OAAO9F,EAAK+F,OAAO,EAEnB9F,IACAD,EAAK4F,WAAW,CAAC1F,OAAO,CAAG,CAAA,EAC3BF,EAAK+F,OAAO,CAAGnF,EAAUiE,OAAO,CAChC7E,EAAK8F,OAAO,CAAGlF,EAAUoE,OAAO,CAChChF,EAAKgG,WAAW,CAAG,SAAUC,CAAM,CAAEC,CAAM,CAAEV,CAAM,CAAEW,CAAS,CAAEC,CAAc,EAG1E,GAAIzG,EAAWM,SAAS,CAAE,CACtB,IACIoG,EADEvG,EAAU,IAAI,CAACD,OAAO,CAACC,MAAM,EAAI,EAAE,CAEzC,KAAQuG,EAAY1G,EAAWuF,WAAW,CAACe,EAAQnG,IAC/CmG,EAASI,EAAU5D,EAAE,CAEzB,KAAQ4D,EAAY1G,EAAWuF,WAAW,CAACgB,EAAQpG,IAC/CoG,EAASG,EAAU7D,IAAI,CAGvB0D,EAASD,GACTC,CAAAA,EAASD,CAAK,CAEtB,CACAjG,EAAKiF,WAAW,CAACtG,SAAS,CAACqH,WAAW,CAACnH,IAAI,CAAC,IAAI,CAAEoH,EAAQC,EAAQV,EAAQW,EAAWC,EACzF,EACApG,EAAKsG,kBAAkB,CAAG,WAGtB,GAFAtG,EAAKiF,WAAW,CAACtG,SAAS,CAAC2H,kBAAkB,CAACzH,IAAI,CAAC,IAAI,EACvDc,EAAW4G,UAAU,CAAG,KAAK,EACzB5G,EAAWM,SAAS,CAAE,CACtB,IAAMH,EAASE,EAAKH,OAAO,CAACC,MAAM,EAAI,EAAE,CAExC0G,EAAc,EAAE,CAAExE,EAAa,EAAE,CAAEyE,EAAoBhH,EAAKO,EAAKyG,iBAAiB,CAAE,GAChFjG,EAAS,EAAGkG,EAAOjC,EAAQvC,EAAMlC,EAAK2G,OAAO,EAAI3G,EAAKkC,GAAG,CAAE0E,EAAM5G,EAAK6G,OAAO,EAAI7G,EAAK4G,GAAG,CAAEE,EAAOvG,EAEtGT,EAAOiC,OAAO,CAAC,SAAUM,CAAG,EACxBoC,EAASpC,EAAIoC,MAAM,EAAIC,IACnBlF,EAAS0C,IAAQ1C,EAASoH,KACtBhG,EAAU0D,SAAS,CAACjC,EAAKH,IACzBA,CAAAA,GAAQ,AAACG,EAAII,EAAE,CAAGgC,EACbvC,EAAMuC,CAAO,EAElB7D,EAAU0D,SAAS,CAACjC,EAAKuE,IACzBA,CAAAA,GAAQ,AAACA,EAAMnC,EACVpC,EAAIG,IAAI,CAAGiC,CAAO,EAGnC,GAEA3E,EAAOiC,OAAO,CAAC,SAAUM,CAAG,EAGxB,GAFAyE,EAAQzE,EAAIG,IAAI,CAChBiC,EAASpC,EAAIoC,MAAM,EAAIC,IACnBlF,EAAS0C,IAAQ1C,EAASoH,GAAM,CAChC,KAAOE,EAAQrC,EAASvC,GACpB4E,GAASrC,EAEb,KAAOqC,EAAQ5E,GACX4E,GAASrC,EAEb,IAAKlE,EAAIuG,EAAOvG,EAAIqG,EAAKrG,GAAKkE,EAC1B+B,EAAY9F,IAAI,CAAC,CACbqG,MAAOxG,EACPyG,KAAM,IACV,GACAR,EAAY9F,IAAI,CAAC,CACbqG,MAAOxG,EAAI8B,EAAII,EAAE,CAAGJ,EAAIG,IAAI,CAC5BwE,KAAM,MACNC,KAAM5E,EAAI6E,SAAS,AACvB,EAER,CACJ,GACAV,EAAYW,IAAI,CAAC,SAAUlJ,CAAC,CAAEkH,CAAC,EAC3B,OAAQ,AAAClH,EAAE8I,KAAK,GAAK5B,EAAE4B,KAAK,CACvB,AAAC9I,CAAAA,CAAAA,AAAW,OAAXA,EAAE+I,IAAI,AAAQ,EACX7B,CAAAA,CAAAA,AAAW,OAAXA,EAAE6B,IAAI,AAAQ,EACnB/I,EAAE8I,KAAK,CAAG5B,EAAE4B,KAAK,AACzB,GAEAL,EAAQ,EACRI,EAAQ5E,EACRsE,EAAYzE,OAAO,CAAC,SAAUM,CAAG,EAEf,IADdqE,CAAAA,GAAUrE,AAAa,OAAbA,EAAI2E,IAAI,CAAY,EAAI,EAAE,GACjB3E,AAAa,OAAbA,EAAI2E,IAAI,EACvBF,CAAAA,EAAQzE,EAAI0E,KAAK,AAAD,EAEN,IAAVL,GAAelH,EAASsH,KACxB9E,EAAWtB,IAAI,CAAC,CACZ8B,KAAMsE,EACNrE,GAAIJ,EAAI0E,KAAK,CACbhC,IAAK1C,EAAI0E,KAAK,CAAGD,EAASzE,CAAAA,EAAI4E,IAAI,EAAI,CAAA,CAC1C,GACAzG,GAAW6B,EAAI0E,KAAK,CAChBD,EACCzE,CAAAA,EAAI4E,IAAI,EAAI,CAAA,EAEzB,GACAtH,EAAWqC,UAAU,CAAGA,EAGpBxC,EAAS0C,IACT1C,EAASoH,IACTpH,EAASQ,EAAKkC,GAAG,IACjBvC,EAAW4G,UAAU,CAAGK,EAAM1E,EAAM1B,EAChCiG,EACJnH,EAAUU,EAAM,eACZA,EAAKoH,WAAW,CAChBpH,EAAKqH,MAAM,CAAGrH,EAAKoH,WAAW,CAEzBzH,EAAW4G,UAAU,EAC1BvG,CAAAA,EAAKqH,MAAM,EACP,AAACT,CAAAA,EAAM5G,EAAKkC,GAAG,CAAGuE,CAAgB,EAC9B9G,EAAW4G,UAAU,AAAD,EAE5BE,GACAzG,CAAAA,EAAKsH,eAAe,CAChBtH,EAAKqH,MAAM,CAAIrH,CAAAA,EAAKuH,cAAc,EAAI,CAAA,CAAC,EAE/CvH,EAAKkC,GAAG,CAAGA,EACXlC,EAAK4G,GAAG,CAAGA,EAEnB,CACJ,GAEAnH,EAAK+F,EAAQ,CAAA,IACbxF,EAAK0F,KAAK,CAACF,MAAM,EAEzB,CACJ,CACAlI,EAAWsD,SAAS,CAAGA,CAC3B,EAAGtD,GAAeA,CAAAA,EAAa,CAAC,CAAA,GAMH,IAAMkK,EAAmBlK,EAOhDmK,EAAKxI,GACXwI,CAAAA,EAAEnK,UAAU,CAAGmK,EAAEnK,UAAU,EAAIkK,EAC/BC,EAAEnK,UAAU,CAACyG,OAAO,CAAC0D,EAAEC,IAAI,CAAED,EAAEE,MAAM,EACR,IAAM5I,EAAoBE,IAG7C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}