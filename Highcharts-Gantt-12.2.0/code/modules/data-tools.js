!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(e._Highcharts):"function"==typeof define&&define.amd?define("highcharts/modules/data-tools",["highcharts/highcharts"],function(e){return t(e)}):"object"==typeof exports?exports["highcharts/modules/data-tools"]=t(e._Highcharts):e.Highcharts=t(e.Highcharts)}("undefined"==typeof window?this:window,e=>(()=>{"use strict";var t,r={944:t=>{t.exports=e}},s={};function n(e){var t=s[e];if(void 0!==t)return t.exports;var i=s[e]={exports:{}};return r[e](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var i={};n.d(i,{default:()=>tc});var o=n(944),l=n.n(o);let{addEvent:a,fireEvent:u,merge:h}=l();class m{benchmark(e,t){let r=[],s=this,n=()=>{s.modifyTable(e),s.emit({type:"afterBenchmarkIteration"})},{iterations:i}=h({iterations:1},t);s.on("afterBenchmarkIteration",()=>{if(r.length===i){s.emit({type:"afterBenchmark",results:r});return}n()});let o={startTime:0,endTime:0};return s.on("modify",()=>{o.startTime=window.performance.now()}),s.on("afterModify",()=>{o.endTime=window.performance.now(),r.push(o.endTime-o.startTime)}),n(),r}emit(e){u(this,e.type,e)}modify(e,t){let r=this;return new Promise((s,n)=>{e.modified===e&&(e.modified=e.clone(!1,t));try{s(r.modifyTable(e,t))}catch(s){r.emit({type:"error",detail:t,table:e}),n(s)}})}modifyCell(e,t,r,s,n){return this.modifyTable(e)}modifyColumns(e,t,r,s){return this.modifyTable(e)}modifyRows(e,t,r,s){return this.modifyTable(e)}on(e,t){return a(this,e,t)}}!function(e){e.types={},e.registerType=function(t,r){return!!t&&!e.types[t]&&!!(e.types[t]=r)}}(m||(m={}));let f=m;!function(e){e.setLength=function(e,t,r){return Array.isArray(e)?(e.length=t,e):e[r?"subarray":"slice"](0,t)},e.splice=function(e,t,r,s,n=[]){if(Array.isArray(e))return Array.isArray(n)||(n=Array.from(n)),{removed:e.splice(t,r,...n),array:e};let i=Object.getPrototypeOf(e).constructor,o=e[s?"subarray":"slice"](t,t+r),l=new i(e.length-r+n.length);return l.set(e.subarray(0,t),0),l.set(n,t),l.set(e.subarray(t+r),t+n.length),{removed:o,array:l}}}(t||(t={}));let c=t,{setLength:d,splice:g}=c,{fireEvent:p,objectEach:y,uniqueKey:b}=l(),w=class{constructor(e={}){this.autoId=!e.id,this.columns={},this.id=e.id||b(),this.modified=this,this.rowCount=0,this.versionTag=b();let t=0;y(e.columns||{},(e,r)=>{this.columns[r]=e.slice(),t=Math.max(t,e.length)}),this.applyRowCount(t)}applyRowCount(e){this.rowCount=e,y(this.columns,(t,r)=>{t.length!==e&&(this.columns[r]=d(t,e))})}deleteRows(e,t=1){if(t>0&&e<this.rowCount){let r=0;y(this.columns,(s,n)=>{this.columns[n]=g(s,e,t).array,r=s.length}),this.rowCount=r}p(this,"afterDeleteRows",{rowIndex:e,rowCount:t}),this.versionTag=b()}getColumn(e,t){return this.columns[e]}getColumns(e,t){return(e||Object.keys(this.columns)).reduce((e,t)=>(e[t]=this.columns[t],e),{})}getRow(e,t){return(t||Object.keys(this.columns)).map(t=>this.columns[t]?.[e])}setColumn(e,t=[],r=0,s){this.setColumns({[e]:t},r,s)}setColumns(e,t,r){let s=this.rowCount;y(e,(e,t)=>{this.columns[t]=e.slice(),s=e.length}),this.applyRowCount(s),r?.silent||(p(this,"afterSetColumns"),this.versionTag=b())}setRow(e,t=this.rowCount,r,s){let{columns:n}=this,i=r?this.rowCount+1:t+1;y(e,(e,o)=>{let l=n[o]||s?.addColumns!==!1&&Array(i);l&&(r?l=g(l,t,0,!0,[e]).array:l[t]=e,n[o]=l)}),i>this.rowCount&&this.applyRowCount(i),s?.silent||(p(this,"afterSetRows"),this.versionTag=b())}},{addEvent:C,defined:R,extend:N,fireEvent:T,isNumber:v,uniqueKey:O}=l();class A extends w{static isNull(e){if(e===A.NULL)return!0;if(e instanceof Array){if(!e.length)return!1;for(let t=0,r=e.length;t<r;++t)if(null!==e[t])return!1}else{let t=Object.keys(e);if(!t.length)return!1;for(let r=0,s=t.length;r<s;++r)if(null!==e[t[r]])return!1}return!0}constructor(e={}){super(e),this.modified=this}clone(e,t){let r={};this.emit({type:"cloneTable",detail:t}),e||(r.columns=this.columns),this.autoId||(r.id=this.id);let s=new A(r);return e||(s.versionTag=this.versionTag,s.originalRowIndexes=this.originalRowIndexes,s.localRowIndexes=this.localRowIndexes),this.emit({type:"afterCloneTable",detail:t,tableClone:s}),s}deleteColumns(e,t){let r=this.columns,s={},n={},i=this.modifier,o=this.rowCount;if((e=e||Object.keys(r)).length){this.emit({type:"deleteColumns",columnNames:e,detail:t});for(let t=0,i=e.length,l,a;t<i;++t)(l=r[a=e[t]])&&(s[a]=l,n[a]=Array(o)),delete r[a];return Object.keys(r).length||(this.rowCount=0,this.deleteRowIndexReferences()),i&&i.modifyColumns(this,n,0,t),this.emit({type:"afterDeleteColumns",columns:s,columnNames:e,detail:t}),s}}deleteRowIndexReferences(){delete this.originalRowIndexes,delete this.localRowIndexes}deleteRows(e,t=1,r){let s=[],n=[],i=this.modifier;if(this.emit({type:"deleteRows",detail:r,rowCount:t,rowIndex:e||0}),void 0===e&&(e=0,t=this.rowCount),t>0&&e<this.rowCount){let r=this.columns,i=Object.keys(r);for(let o=0,l=i.length,a,u,h;o<l;++o){a=r[h=i[o]];let m=c.splice(a,e,t);u=m.removed,r[h]=a=m.array,o||(this.rowCount=a.length);for(let e=0,t=u.length;e<t;++e)s[e]=s[e]||[],s[e][o]=u[e];n.push(Array(l))}}return i&&i.modifyRows(this,n,e||0,r),this.emit({type:"afterDeleteRows",detail:r,rowCount:t,rowIndex:e||0,rows:s}),s}emit(e){["afterDeleteColumns","afterDeleteRows","afterSetCell","afterSetColumns","afterSetRows"].includes(e.type)&&(this.versionTag=O()),T(this,e.type,e)}getCell(e,t){let r=this.columns[e];if(r)return r[t]}getCellAsBoolean(e,t){let r=this.columns[e];return!!(r&&r[t])}getCellAsNumber(e,t,r){let s=this.columns[e],n=s&&s[t];switch(typeof n){case"boolean":return+!!n;case"number":return isNaN(n)&&!r?null:n}return isNaN(n=parseFloat(`${n??""}`))&&!r?null:n}getCellAsString(e,t){let r=this.columns[e];return`${r&&r[t]}`}getColumn(e,t){return this.getColumns([e],t)[e]}getColumnAsNumbers(e,t){let r=this.columns[e],s=[];if(r){let n=r.length;if(t)for(let t=0;t<n;++t)s.push(this.getCellAsNumber(e,t,!0));else{for(let e=0,t;e<n;++e){if("number"==typeof(t=r[e]))return r.slice();if(null!=t)break}for(let t=0;t<n;++t)s.push(this.getCellAsNumber(e,t))}}return s}getColumnNames(){return Object.keys(this.columns)}getColumns(e,t,r){let s=this.columns,n={};e=e||Object.keys(s);for(let i=0,o=e.length,l,a;i<o;++i)(l=s[a=e[i]])&&(t?n[a]=l:r&&!Array.isArray(l)?n[a]=Array.from(l):n[a]=l.slice());return n}getLocalRowIndex(e){let{localRowIndexes:t}=this;return t?t[e]:e}getModifier(){return this.modifier}getOriginalRowIndex(e){let{originalRowIndexes:t}=this;return t?t[e]:e}getRow(e,t){return this.getRows(e,1,t)[0]}getRowCount(){return this.rowCount}getRowIndexBy(e,t,r){let s=this.columns[e];if(s){let e=-1;if(Array.isArray(s)?e=s.indexOf(t,r):v(t)&&(e=s.indexOf(t,r)),-1!==e)return e}}getRowObject(e,t){return this.getRowObjects(e,1,t)[0]}getRowObjects(e=0,t=this.rowCount-e,r){let s=this.columns,n=Array(t);r=r||Object.keys(s);for(let i=e,o=0,l=Math.min(this.rowCount,e+t),a,u;i<l;++i,++o)for(let e of(u=n[o]={},r))a=s[e],u[e]=a?a[i]:void 0;return n}getRows(e=0,t=this.rowCount-e,r){let s=this.columns,n=Array(t);r=r||Object.keys(s);for(let i=e,o=0,l=Math.min(this.rowCount,e+t),a,u;i<l;++i,++o)for(let e of(u=n[o]=[],r))a=s[e],u.push(a?a[i]:void 0);return n}getVersionTag(){return this.versionTag}hasColumns(e){let t=this.columns;for(let r=0,s=e.length;r<s;++r)if(!t[e[r]])return!1;return!0}hasRowWith(e,t){let r=this.columns[e];return Array.isArray(r)?-1!==r.indexOf(t):!!(R(t)&&Number.isFinite(t))&&-1!==r.indexOf(+t)}on(e,t){return C(this,e,t)}renameColumn(e,t){let r=this.columns;return!!r[e]&&(e!==t&&(r[t]=r[e],delete r[e]),!0)}setCell(e,t,r,s){let n=this.columns,i=this.modifier,o=n[e];(!o||o[t]!==r)&&(this.emit({type:"setCell",cellValue:r,columnName:e,detail:s,rowIndex:t}),o||(o=n[e]=Array(this.rowCount)),t>=this.rowCount&&(this.rowCount=t+1),o[t]=r,i&&i.modifyCell(this,e,t,r),this.emit({type:"afterSetCell",cellValue:r,columnName:e,detail:s,rowIndex:t}))}setColumns(e,t,r,s){let n=this.columns,i=this.modifier,o=Object.keys(e),l=this.rowCount;if(this.emit({type:"setColumns",columns:e,columnNames:o,detail:r,rowIndex:t}),R(t)||s){for(let r=0,i=o.length,a,u,h,m;r<i;++r){a=e[h=o[r]],m=Object.getPrototypeOf((u=n[h])&&s?u:a).constructor,u?m===Array?Array.isArray(u)||(u=Array.from(u)):u.length<l&&(u=new m(l)).set(n[h]):u=new m(l),n[h]=u;for(let e=t||0,r=a.length;e<r;++e)u[e]=a[e];l=Math.max(l,a.length)}this.applyRowCount(l)}else super.setColumns(e,t,N(r,{silent:!0}));i&&i.modifyColumns(this,e,t||0),this.emit({type:"afterSetColumns",columns:e,columnNames:o,detail:r,rowIndex:t})}setModifier(e,t){let r,s=this;return s.emit({type:"setModifier",detail:t,modifier:e,modified:s.modified}),s.modified=s,s.modifier=e,(e?e.modify(s):Promise.resolve(s)).then(r=>(r.emit({type:"afterSetModifier",detail:t,modifier:e,modified:r.modified}),r)).catch(t=>{throw s.emit({type:"setModifierError",error:t,modifier:e,modified:s.modified}),t})}setOriginalRowIndexes(e,t=!1){if(this.originalRowIndexes=e,t)return;let r=this.localRowIndexes=[];for(let t=0,s=e.length,n;t<s;++t)R(n=e[t])&&(r[n]=t)}setRow(e,t,r,s){this.setRows([e],t,r,s)}setRows(e,t=this.rowCount,r,s){let n=this.columns,i=Object.keys(n),o=this.modifier,l=e.length;this.emit({type:"setRows",detail:s,rowCount:l,rowIndex:t,rows:e});for(let s=0,o=t,a;s<l;++s,++o)if((a=e[s])===A.NULL)for(let e=0,t=i.length;e<t;++e){let t=n[i[e]];r?n[i[e]]=c.splice(t,o,0,!0,[null]).array:t[o]=null}else if(a instanceof Array)for(let e=0,t=i.length;e<t;++e)n[i[e]][o]=a[e];else super.setRow(a,o,void 0,{silent:!0});let a=r?l+e.length:t+l;if(a>this.rowCount){this.rowCount=a;for(let e=0,t=i.length;e<t;++e){let t=i[e];n[t]=c.setLength(n[t],a)}}o&&o.modifyRows(this,e,t),this.emit({type:"afterSetRows",detail:s,rowCount:l,rowIndex:t,rows:e})}}A.NULL={},A.version="1.0.0";let{addEvent:x,fireEvent:F,merge:E,pick:M}=l();class I{constructor(e={}){this.table=new A(e.dataTable),this.metadata=e.metadata||{columns:{}}}get polling(){return!!this._polling}describeColumn(e,t){let r=this.metadata.columns;r[e]=E(r[e]||{},t)}describeColumns(e){let t,r=Object.keys(e);for(;"string"==typeof(t=r.pop());)this.describeColumn(t,e[t])}emit(e){F(this,e.type,e)}getColumnOrder(e){let t=this.metadata.columns,r=Object.keys(t||{});if(r.length)return r.sort((e,r)=>M(t[e].index,0)-M(t[r].index,0))}getSortedColumns(e){return this.table.getColumns(this.getColumnOrder(e))}load(){return F(this,"afterLoad",{table:this.table}),Promise.resolve(this)}on(e,t){return x(this,e,t)}save(){return F(this,"saveError",{table:this.table}),Promise.reject(Error("Not implemented"))}setColumnOrder(e){for(let t=0,r=e.length;t<r;++t)this.describeColumn(e[t],{index:t})}setModifierOptions(e){let t=e&&f.types[e.type];return this.table.setModifier(t?new t(e):void 0).then(()=>this)}startPolling(e=1e3){let t=this;window.clearTimeout(t._polling),t._polling=window.setTimeout(()=>t.load().catch(e=>t.emit({type:"loadError",error:e,table:t.table})).then(()=>{t._polling&&t.startPolling(e)}),e)}stopPolling(){window.clearTimeout(this._polling),delete this._polling}whatIs(e){return this.metadata.columns[e]}}!function(e){e.types={},e.registerType=function(t,r){return!!t&&!e.types[t]&&!!(e.types[t]=r)}}(I||(I={}));let D=I,{addEvent:P,fireEvent:j,isNumber:L,merge:S}=l();class Y{constructor(e){this.dateFormats={"YYYY/mm/dd":{regex:/^(\d{4})([\-\.\/])(\d{1,2})\2(\d{1,2})$/,parser:function(e){return e?Date.UTC(+e[1],e[3]-1,+e[4]):NaN}},"dd/mm/YYYY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{4})$/,parser:function(e){return e?Date.UTC(+e[4],e[3]-1,+e[1]):NaN},alternative:"mm/dd/YYYY"},"mm/dd/YYYY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{4})$/,parser:function(e){return e?Date.UTC(+e[4],e[1]-1,+e[3]):NaN}},"dd/mm/YY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{2})$/,parser:function(e){let t=new Date;if(!e)return NaN;let r=+e[4];return r>t.getFullYear()-2e3?r+=1900:r+=2e3,Date.UTC(r,e[3]-1,+e[1])},alternative:"mm/dd/YY"},"mm/dd/YY":{regex:/^(\d{1,2})([\-\.\/])(\d{1,2})\2(\d{2})$/,parser:function(e){return e?Date.UTC(+e[4]+2e3,e[1]-1,+e[3]):NaN}}};let t=S(Y.defaultOptions,e),r=t.decimalPoint;("."===r||","===r)&&(r="."===r?"\\.":",",this.decimalRegExp=RegExp("^(-?[0-9]+)"+r+"([0-9]+)$")),this.options=t}asBoolean(e){return"boolean"==typeof e?e:"string"==typeof e?""!==e&&"0"!==e&&"false"!==e:!!this.asNumber(e)}asDate(e){let t;if("string"==typeof e)t=this.parseDate(e);else if("number"==typeof e)t=e;else{if(e instanceof Date)return e;t=this.parseDate(this.asString(e))}return new Date(t)}asGuessedType(e){return({number:this.asNumber,Date:this.asDate,string:this.asString})[this.guessType(e)].call(this,e)}asNumber(e){if("number"==typeof e)return e;if("boolean"==typeof e)return+!!e;if("string"==typeof e){let t=this.decimalRegExp;if(e.indexOf(" ")>-1&&(e=e.replace(/\s+/g,"")),t){if(!t.test(e))return NaN;e=e.replace(t,"$1.$2")}return parseFloat(e)}return e instanceof Date?e.getDate():e?e.getRowCount():NaN}asString(e){return""+e}deduceDateFormat(e,t,r){let s=[],n=[],i="YYYY/mm/dd",o,l=[],a=0,u=!1,h,m;for((!t||t>e.length)&&(t=e.length);a<t;a++)if(void 0!==e[a]&&e[a]&&e[a].length)for(m=0,o=e[a].trim().replace(/[\-\.\/]/g," ").split(" "),l=["","",""];m<o.length;m++)m<l.length&&(h=parseInt(o[m],10))&&(n[m]=!n[m]||n[m]<h?h:n[m],void 0!==s[m]?s[m]!==h&&(s[m]=!1):s[m]=h,h>31?h<100?l[m]="YY":l[m]="YYYY":h>12&&h<=31?(l[m]="dd",u=!0):l[m].length||(l[m]="mm"));if(u){for(m=0;m<s.length;m++)!1!==s[m]?n[m]>12&&"YY"!==l[m]&&"YYYY"!==l[m]&&(l[m]="YY"):n[m]>12&&"mm"===l[m]&&(l[m]="dd");3===l.length&&"dd"===l[1]&&"dd"===l[2]&&(l[2]="YY"),i=l.join("/")}return r&&(this.options.dateFormat=i),i}emit(e){j(this,e.type,e)}export(e,t){throw this.emit({type:"exportError",columns:[],headers:[]}),Error("Not implemented")}getTable(){throw Error("Not implemented")}guessType(e){let t="string";if("string"==typeof e){let r=this.trim(`${e}`),s=this.decimalRegExp,n=this.trim(r,!0);s&&(n=s.test(n)?n.replace(s,"$1.$2"):"");let i=parseFloat(n);+n===i?e=i:t=L(this.parseDate(e))?"Date":"string"}return"number"==typeof e&&(t=e>31536e6?"Date":"number"),t}on(e,t){return P(this,e,t)}parse(e){throw this.emit({type:"parseError",columns:[],headers:[]}),Error("Not implemented")}parseDate(e,t){let r=this.options,s=t||r.dateFormat,n=NaN,i,o,l;if(r.parseDate)n=r.parseDate(e);else{if(s)(o=this.dateFormats[s])||(o=this.dateFormats["YYYY/mm/dd"]),(l=e.match(o.regex))&&(n=o.parser(l));else for(i in this.dateFormats)if(o=this.dateFormats[i],l=e.match(o.regex)){s=i,n=o.parser(l);break}!l&&("object"==typeof(l=Date.parse(e))&&null!==l&&l.getTime?n=l.getTime()-6e4*l.getTimezoneOffset():L(l)&&(n=l-6e4*new Date(l).getTimezoneOffset(),-1===e.indexOf("2001")&&2001===new Date(n).getFullYear()&&(n=NaN)))}return n}trim(e,t){return"string"==typeof e&&(e=e.replace(/^\s+|\s+$/g,""),t&&/^[\d\s]+$/.test(e)&&(e=e.replace(/\s/g,""))),e}}Y.defaultOptions={dateFormat:"",alternativeFormat:"",startColumn:0,endColumn:Number.MAX_VALUE,startRow:0,endRow:Number.MAX_VALUE,firstRowAsNames:!0,switchRowsAndColumns:!1},function(e){e.types={},e.registerType=function(t,r){return!!t&&!e.types[t]&&!!(e.types[t]=r)},e.getTableFromColumns=function(e=[],t=[]){let r=new A;for(let s=0,n=Math.max(t.length,e.length);s<n;++s)r.setColumn(t[s]||`${s}`,e[s]);return r}}(Y||(Y={}));let V=Y;class k{constructor(e={}){this.emittingRegister=[],this.listenerMap={},this.stateMap=e}addListener(e,t,r){let s=this.listenerMap[e]=this.listenerMap[e]||{};return(s[t]=s[t]||[]).push(r),this}buildEmittingTag(e){return("position"===e.cursor.type?[e.table.id,e.cursor.column,e.cursor.row,e.cursor.state,e.cursor.type]:[e.table.id,e.cursor.columns,e.cursor.firstRow,e.cursor.lastRow,e.cursor.state,e.cursor.type]).join("\0")}emitCursor(e,t,r,s){let n=e.id,i=t.state,o=this.listenerMap[n]&&this.listenerMap[n][i];if(o){let i=this.stateMap[n]=this.stateMap[n]??{},l=i[t.state]||[];s&&(l.length||(i[t.state]=l),-1===k.getIndex(t,l)&&l.push(t));let a={cursor:t,cursors:l,table:e};r&&(a.event=r);let u=this.emittingRegister,h=this.buildEmittingTag(a);if(u.indexOf(h)>=0)return this;try{this.emittingRegister.push(h);for(let e=0,t=o.length;e<t;++e)o[e].call(this,a)}finally{let e=this.emittingRegister.indexOf(h);e>=0&&this.emittingRegister.splice(e,1)}}return this}remitCursor(e,t){let r=this.stateMap[e]&&this.stateMap[e][t.state];if(r){let e=k.getIndex(t,r);e>=0&&r.splice(e,1)}return this}removeListener(e,t,r){let s=this.listenerMap[e]&&this.listenerMap[e][t];if(s){let e=s.indexOf(r);e>=0&&s.splice(e,1)}return this}}k.version="1.0.0",function(e){function t(e,t){if("range"===e.type)return e;let r={type:"range",firstRow:e.row??(t&&t.firstRow)??0,lastRow:e.row??(t&&t.lastRow)??Number.MAX_VALUE,state:e.state};return void 0!==e.column&&(r.columns=[e.column]),r}e.getIndex=function(e,t){if("position"===e.type){for(let r,s=0,n=t.length;s<n;++s)if("position"===(r=t[s]).type&&r.state===e.state&&r.column===e.column&&r.row===e.row)return s}else{let r=JSON.stringify(e.columns);for(let s,n=0,i=t.length;n<i;++n)if("range"===(s=t[n]).type&&s.state===e.state&&s.firstRow===e.firstRow&&s.lastRow===e.lastRow&&JSON.stringify(s.columns)===r)return n}return -1},e.isEqual=function(e,t){return"position"===e.type&&"position"===t.type?e.column===t.column&&e.row===t.row&&e.state===t.state:"range"===e.type&&"range"===t.type&&e.firstRow===t.firstRow&&e.lastRow===t.lastRow&&JSON.stringify(e.columns)===JSON.stringify(t.columns)},e.isInRange=function(e,r){"position"===r.type&&(r=t(r)),"position"===e.type&&(e=t(e,r));let s=e.columns,n=r.columns;return e.firstRow>=r.firstRow&&e.lastRow<=r.lastRow&&(!s||!n||s.every(e=>n.indexOf(e)>=0))},e.toPositions=function(e){if("position"===e.type)return[e];let t=e.columns||[],r=[],s=e.state;for(let n=e.firstRow,i=e.lastRow;n<i;++n){if(!t.length){r.push({type:"position",row:n,state:s});continue}for(let e=0,i=t.length;e<i;++e)r.push({type:"position",column:t[e],row:n,state:s})}return r},e.toRange=t}(k||(k={}));let $=k,U={connectors:[]};class H{constructor(e=U){e.connectors=e.connectors||[],this.connectors={},this.options=e,this.waiting={}}emit(e){l().fireEvent(this,e.type,e)}getConnector(e){let t=this.connectors[e];if(t)return Promise.resolve(t);let r=this.waiting[e];if(!r){r=this.waiting[e]=[];let t=this.getConnectorOptions(e);if(!t)throw Error(`Connector '${e}' not found.`);this.loadConnector(t).then(t=>{delete this.waiting[e];for(let e=0,s=r.length;e<s;++e)r[e][0](t)}).catch(t=>{delete this.waiting[e];for(let e=0,s=r.length;e<s;++e)r[e][1](t)})}return new Promise((e,t)=>{r.push([e,t])})}getConnectorIds(){let e=this.options.connectors,t=[];for(let r=0,s=e.length;r<s;++r)t.push(e[r].id);return t}getConnectorOptions(e){let t=this.options.connectors;for(let r=0,s=t.length;r<s;++r)if(t[r].id===e)return t[r]}getConnectorTable(e){return this.getConnector(e).then(e=>e.table)}isNewConnector(e){return!this.connectors[e]}loadConnector(e){return new Promise((t,r)=>{this.emit({type:"load",options:e});let s=D.types[e.type];if(!s)throw Error(`Connector type not found. (${e.type})`);new s(e.options).load().then(r=>{this.connectors[e.id]=r,this.emit({type:"afterLoad",options:e}),t(r)}).catch(r)})}on(e,t){return l().addEvent(this,e,t)}setConnectorOptions(e){let t=this.options.connectors,r=this.connectors;this.emit({type:"setConnectorOptions",options:e});for(let r=0,s=t.length;r<s;++r)if(t[r].id===e.id){t.splice(r,1);break}r[e.id]&&(r[e.id].stopPolling(),delete r[e.id]),t.push(e),this.emit({type:"afterSetConnectorOptions",options:e})}}H.version="1.0.0";let _=/^(?:FALSE|TRUE)/,B=/^[+\-]?\d+(?:\.\d+)?(?:e[+\-]\d+)?/,G=/^[+\-]?\d+(?:,\d+)?(?:e[+\-]\d+)?/,Z=/^([A-Z][A-Z\d\.]*)\(/,J=/^(?:[+\-*\/^<=>]|<=|=>)/,X=/^(\$?[A-Z]+)(\$?\d+)\:(\$?[A-Z]+)(\$?\d+)/,K=/^R(\d*|\[\d+\])C(\d*|\[\d+\])\:R(\d*|\[\d+\])C(\d*|\[\d+\])/,q=/^(\$?[A-Z]+)(\$?\d+)(?![\:C])/,z=/^R(\d*|\[\d+\])C(\d*|\[\d+\])(?!\:)/;function Q(e){let t=0;for(let r=0,s=e.length,n,i=1;r<s;++r){if("("===(n=e[r])){t||(i=r+1),++t;continue}if(")"===n&&!--t)return e.substring(i,r)}if(t>0){let e=Error("Incomplete parantheses.");throw e.name="FormulaParseError",e}return""}function W(e){let t=-1;for(let r=0,s=e.length,n,i=!1;r<s;++r){if("\\"===(n=e[r])){i=!i;continue}if(i){i=!1;continue}if('"'===n){if(!(t<0))return e.substring(t+1,r);t=r}}let r=Error("Incomplete string.");throw r.name="FormulaParseError",r}function ee(e,t){let r;if(r=e.match(K)){let e=""===r[2]||"["===r[2][0],t=""===r[1]||"["===r[1][0],s=""===r[4]||"["===r[4][0],n=""===r[3]||"["===r[3][0],i={type:"range",beginColumn:e?parseInt(r[2].substring(1,-1)||"0",10):parseInt(r[2],10)-1,beginRow:t?parseInt(r[1].substring(1,-1)||"0",10):parseInt(r[1],10)-1,endColumn:s?parseInt(r[4].substring(1,-1)||"0",10):parseInt(r[4],10)-1,endRow:n?parseInt(r[3].substring(1,-1)||"0",10):parseInt(r[3],10)-1};return e&&(i.beginColumnRelative=!0),t&&(i.beginRowRelative=!0),s&&(i.endColumnRelative=!0),n&&(i.endRowRelative=!0),i}if(r=e.match(X)){let e="$"!==r[1][0],t="$"!==r[2][0],s="$"!==r[3][0],n="$"!==r[4][0],i={type:"range",beginColumn:er(e?r[1]:r[1].substring(1))-1,beginRow:parseInt(t?r[2]:r[2].substring(1),10)-1,endColumn:er(s?r[3]:r[3].substring(1))-1,endRow:parseInt(n?r[4]:r[4].substring(1),10)-1};return e&&(i.beginColumnRelative=!0),t&&(i.beginRowRelative=!0),s&&(i.endColumnRelative=!0),n&&(i.endRowRelative=!0),i}let s=et(e,t);return 1===s.length&&"string"!=typeof s[0]?s[0]:s}function et(e,t){let r=t?G:B,s=[],n,i=("="===e[0]?e.substring(1):e).trim();for(;i;){if(n=i.match(z)){let e=""===n[2]||"["===n[2][0],t=""===n[1]||"["===n[1][0],r={type:"reference",column:e?parseInt(n[2].substring(1,-1)||"0",10):parseInt(n[2],10)-1,row:t?parseInt(n[1].substring(1,-1)||"0",10):parseInt(n[1],10)-1};e&&(r.columnRelative=!0),t&&(r.rowRelative=!0),s.push(r),i=i.substring(n[0].length).trim();continue}if(n=i.match(q)){let e="$"!==n[1][0],t="$"!==n[2][0],r={type:"reference",column:er(e?n[1]:n[1].substring(1))-1,row:parseInt(t?n[2]:n[2].substring(1),10)-1};e&&(r.columnRelative=!0),t&&(r.rowRelative=!0),s.push(r),i=i.substring(n[0].length).trim();continue}if(n=i.match(J)){s.push(n[0]),i=i.substring(n[0].length).trim();continue}if(n=i.match(_)){s.push("TRUE"===n[0]),i=i.substring(n[0].length).trim();continue}if(n=i.match(r)){s.push(parseFloat(n[0])),i=i.substring(n[0].length).trim();continue}if('"'===i[0]){let e=W(i);s.push(e.substring(1,-1)),i=i.substring(e.length+2).trim();continue}if(n=i.match(Z)){let e=Q(i=i.substring(n[1].length).trim());s.push({type:"function",name:n[1],args:function(e,t){let r=[],s=t?";":",",n=0,i="";for(let o=0,l=e.length,a;o<l;++o)if((a=e[o])===s&&!n&&i)r.push(ee(i,t)),i="";else if('"'!==a||n||i)" "!==a&&(i+=a,"("===a?++n:")"===a&&--n);else{let t=W(e.substring(o));r.push(t),o+=t.length+1}return!n&&i&&r.push(ee(i,t)),r}(e,t)}),i=i.substring(e.length+2).trim();continue}if("("===i[0]){let e=Q(i);if(e){s.push(et(e,t)),i=i.substring(e.length+2).trim();continue}}let o=e.length-i.length,l=Error("Unexpected character `"+e.substring(o,o+1)+"` at position "+(o+1)+". (`..."+e.substring(o-5,o+6)+"...`)");throw l.name="FormulaParseError",l}return s}function er(e){let t=0;for(let r=0,s=e.length,n,i=e.length-1;r<s;++r)(n=e.charCodeAt(r))>=65&&n<=90&&(t+=(n-64)*Math.pow(26,i)),--i;return t}let es={parseFormula:et},en=["+","-","*","/","^","=","<","<=",">",">="],ei={isFormula:function(e){return e instanceof Array},isFunction:function(e){return"object"==typeof e&&!(e instanceof Array)&&"function"===e.type},isOperator:function(e){return"string"==typeof e&&en.indexOf(e)>=0},isRange:function(e){return"object"==typeof e&&!(e instanceof Array)&&"range"===e.type},isReference:function(e){return"object"==typeof e&&!(e instanceof Array)&&"reference"===e.type},isValue:function(e){return"boolean"==typeof e||"number"==typeof e||"string"==typeof e}},{isFormula:eo,isFunction:el,isOperator:ea,isRange:eu,isReference:eh,isValue:em}=ei,ef=/ */,ec=Number.MAX_VALUE/1.000000000001,ed=Number.MAX_VALUE/1.000000000002,eg=Number.MAX_VALUE,ep={"^":3,"*":2,"/":2,"+":1,"-":1,"=":0,"<":0,"<=":0,">":0,">=":0},ey={},eb=/^[A-Z][A-Z\.]*$/;function ew(e){switch(typeof e){case"boolean":return e?eg:ec;case"string":return ed;case"number":return e;default:return NaN}}function eC(e){return"string"==typeof e?e.toLowerCase().replace(ef,"\0"):e}function eR(e){switch(typeof e){case"boolean":return+!!e;case"string":return parseFloat(e.replace(",","."));case"number":return e;default:return NaN}}function eN(e,t,r){let s;switch(e){case"=":return eC(t)===eC(r);case"<":if(typeof t==typeof r)return eC(t)<eC(r);return ew(t)<ew(r);case"<=":if(typeof t==typeof r)return eC(t)<=eC(r);return ew(t)<=ew(r);case">":if(typeof t==typeof r)return eC(t)>eC(r);return ew(t)>ew(r);case">=":if(typeof t==typeof r)return eC(t)>=eC(r);return ew(t)>=ew(r)}switch(t=eR(t),r=eR(r),e){case"+":s=t+r;break;case"-":s=t-r;break;case"*":s=t*r;break;case"/":s=t/r;break;case"^":s=Math.pow(t,r);break;default:return NaN}return s%1?Math.round(1e9*s)/1e9:s}function eT(e,t){return em(e)?e:eu(e)?t&&ev(e,t)||[]:el(e)?ex(e,t):eA(eo(e)?e:[e],t)}function ev(e,t){let r=t.getColumnNames().slice(e.beginColumn,e.endColumn+1),s=[];for(let n=0,i=r.length,o;n<i;++n){let i=t.getColumn(r[n],!0)||[];for(let l=e.beginRow,a=e.endRow+1;l<a;++l)"string"==typeof(o=i[l])&&"="===o[0]&&t!==t.modified&&(o=t.modified.getCell(r[n],l)),s.push(em(o)?o:NaN)}return s}function eO(e,t){let r=t.getColumnNames()[e.column];if(r){let s=t.getCell(r,e.row);if("string"==typeof s&&"="===s[0]&&t!==t.modified){let s=t.modified.getCell(r,e.row);return em(s)?s:NaN}return em(s)?s:NaN}return NaN}function eA(e,t){let r;for(let s=0,n=e.length,i,o,l,a;s<n;++s){if(ea(i=e[s])){o=i;continue}if(em(i)?a=i:eo(i)?a=eA(e,t):el(i)?a=em(l=ex(i,t))?l:NaN:eh(i)&&(a=t&&eO(i,t)),void 0!==a){if(void 0===r)r=o?eN(o,0,a):a;else{if(!o)return NaN;let t=e[s+1];ea(t)&&ep[t]>ep[o]&&(a=eN(t,a,eA(e.slice(s+2))),s=n),r=eN(o,r,a)}o=void 0,a=void 0}}return em(r)?r:NaN}function ex(e,t,r){let s=ey[e.name];if(s)try{return s(e.args,t)}catch{return NaN}let n=Error(`Function "${e.name}" not found.`);throw n.name="FormulaProcessError",n}let eF={asNumber:eR,getArgumentValue:eT,getArgumentsValues:function(e,t){let r=[];for(let s=0,n=e.length;s<n;++s)r.push(eT(e[s],t));return r},getRangeValues:ev,getReferenceValue:eO,processFormula:eA,processorFunctions:ey,registerProcessorFunction:function(e,t){return eb.test(e)&&!ey[e]&&!!(ey[e]=t)},translateReferences:function e(t,r=0,s=0){for(let n=0,i=t.length,o;n<i;++n)(o=t[n])instanceof Array?e(o,r,s):el(o)?e(o.args,r,s):eu(o)?(o.beginColumnRelative&&(o.beginColumn+=r),o.beginRowRelative&&(o.beginRow+=s),o.endColumnRelative&&(o.endColumn+=r),o.endRowRelative&&(o.endRow+=s)):eh(o)&&(o.columnRelative&&(o.column+=r),o.rowRelative&&(o.row+=s));return t}},{getArgumentValue:eE}=eF;eF.registerProcessorFunction("ABS",function(e,t){let r=eE(e[0],t);switch(typeof r){case"number":return Math.abs(r);case"object":{let e=[];for(let t=0,s=r.length,n;t<s;++t){if("number"!=typeof(n=r[t]))return NaN;e.push(Math.abs(n))}return e}default:return NaN}});let{getArgumentValue:eM}=eF;eF.registerProcessorFunction("AND",function e(t,r){for(let s=0,n=t.length,i;s<n;++s)if(!(i=eM(t[s],r))||"object"==typeof i&&!e(i,r))return!1;return!0});let{getArgumentsValues:eI}=eF;eF.registerProcessorFunction("AVERAGE",function(e,t){let r=eI(e,t),s=0,n=0;for(let e=0,t=r.length,i;e<t;++e)switch(typeof(i=r[e])){case"number":isNaN(i)||(++s,n+=i);break;case"object":for(let e=0,t=i.length,r;e<t;++e)"number"!=typeof(r=i[e])||isNaN(r)||(++s,n+=r)}return s?n/s:0});let{getArgumentValue:eD}=eF;eF.registerProcessorFunction("AVERAGEA",function(e,t){let r=0,s=0;for(let n=0,i=e.length,o;n<i;++n)switch(typeof(o=eD(e[n],t))){case"boolean":++r,s+=+!!o;continue;case"number":isNaN(o)||(++r,s+=o);continue;case"string":++r;continue;default:for(let e=0,t=o.length,n;e<t;++e)switch(typeof(n=o[e])){case"boolean":++r,s+=+!!n;continue;case"number":isNaN(n)||(++r,s+=n);continue;case"string":++r;continue}continue}return r?s/r:0}),eF.registerProcessorFunction("COUNT",function e(t,r){let s=eF.getArgumentsValues(t,r),n=0;for(let t=0,i=s.length,o;t<i;++t)switch(typeof(o=s[t])){case"number":!isNaN(o)&&++n;break;case"object":n+=e(o,r)}return n}),eF.registerProcessorFunction("COUNTA",function e(t,r){let s=eF.getArgumentsValues(t,r),n=0;for(let t=0,i=s.length,o;t<i;++t){switch(typeof(o=s[t])){case"number":if(isNaN(o))continue;break;case"object":n+=e(o,r);continue;case"string":if(!o)continue}++n}return n});let{getArgumentValue:eP}=eF;eF.registerProcessorFunction("IF",function(e,t){return eP(e[0],t)?eP(e[1],t):eP(e[2],t)});let{getArgumentValue:ej}=eF;eF.registerProcessorFunction("ISNA",function(e,t){let r=ej(e[0],t);return"number"!=typeof r||isNaN(r)});let{getArgumentsValues:eL}=eF;eF.registerProcessorFunction("MAX",function e(t,r){let s=eL(t,r),n=Number.NEGATIVE_INFINITY;for(let t=0,r=s.length,i;t<r;++t)switch(typeof(i=s[t])){case"number":i>n&&(n=i);break;case"object":(i=e(i))>n&&(n=i)}return isFinite(n)?n:0}),eF.registerProcessorFunction("MEDIAN",function(e,t){let r=[],s=eF.getArgumentsValues(e,t);for(let e=0,t=s.length,n;e<t;++e)switch(typeof(n=s[e])){case"number":isNaN(n)||r.push(n);break;case"object":for(let e=0,t=n.length,s;e<t;++e)"number"!=typeof(s=n[e])||isNaN(s)||r.push(s)}let n=r.length;if(!n)return NaN;let i=Math.floor(n/2);return n%2?r[i]:(r[i-1]+r[i])/2});let{getArgumentsValues:eS}=eF;eF.registerProcessorFunction("MIN",function e(t,r){let s=eS(t,r),n=Number.POSITIVE_INFINITY;for(let t=0,r=s.length,i;t<r;++t)switch(typeof(i=s[t])){case"number":i<n&&(n=i);break;case"object":(i=e(i))<n&&(n=i)}return isFinite(n)?n:0});let{getArgumentValue:eY}=eF;function eV(e,t){let r={},s=eF.getArgumentsValues(e,t);for(let e=0,t=s.length,n;e<t;++e)switch(typeof(n=s[e])){case"number":isNaN(n)||(r[n]=(r[n]||0)+1);break;case"object":for(let e=0,t=n.length,s;e<t;++e)"number"!=typeof(s=n[e])||isNaN(s)||(r[s]=(r[s]||0)+1)}return r}function ek(e,t){let r=eV(e,t),s=Object.keys(r);if(!s.length)return NaN;let n=parseFloat(s[0]),i=r[s[0]];for(let e=1,t=s.length,o,l,a;e<t;++e)i<(a=r[o=s[e]])?(n=parseFloat(o),i=a):i===a&&n>(l=parseFloat(o))&&(n=l,i=a);return i>1?n:NaN}eF.registerProcessorFunction("MOD",function(e,t){let r=eY(e[0],t),s=eY(e[1],t);return("object"==typeof r&&(r=r[0]),"object"==typeof s&&(s=s[0]),"number"!=typeof r||"number"!=typeof s||0===s)?NaN:r%s}),eF.registerProcessorFunction("MODE",ek),eF.registerProcessorFunction("MODE.MULT",function(e,t){let r=eV(e,t),s=Object.keys(r);if(!s.length)return NaN;let n=[parseFloat(s[0])],i=r[s[0]];for(let e=1,t=s.length,o,l;e<t;++e)i<(l=r[o=s[e]])?(n=[parseFloat(o)],i=l):i===l&&n.push(parseFloat(o));return i>1?n:NaN}),eF.registerProcessorFunction("MODE.SNGL",ek);let{getArgumentValue:e$}=eF;eF.registerProcessorFunction("NOT",function(e,t){let r=e$(e[0],t);switch("object"==typeof r&&(r=r[0]),typeof r){case"boolean":case"number":return!r}return NaN});let{getArgumentValue:eU}=eF;eF.registerProcessorFunction("OR",function e(t,r){for(let s=0,n=t.length,i;s<n;++s)if("object"==typeof(i=eU(t[s],r))){if(e(i,r))return!0}else if(i)return!0;return!1});let{getArgumentsValues:eH}=eF;eF.registerProcessorFunction("PRODUCT",function e(t,r){let s=eH(t,r),n=1,i=!1;for(let t=0,o=s.length,l;t<o;++t)switch(typeof(l=s[t])){case"number":isNaN(l)||(i=!0,n*=l);break;case"object":i=!0,n*=e(l,r)}return i?n:0}),eF.registerProcessorFunction("SUM",function e(t,r){let s=eF.getArgumentsValues(t,r),n=0;for(let t=0,i=s.length,o;t<i;++t)switch(typeof(o=s[t])){case"number":isNaN(o)||(n+=o);break;case"object":n+=e(o,r)}return n});let{getArgumentValue:e_}=eF;eF.registerProcessorFunction("XOR",function(e,t){for(let r=0,s=e.length,n,i;r<s;++r)switch(typeof(i=e_(e[r],t))){case"boolean":case"number":if(void 0===n)n=!!i;else if(!!i!==n)return!0;break;case"object":for(let e=0,t=i.length,r;e<t;++e)switch(typeof(r=i[e])){case"boolean":case"number":if(void 0===n)n=!!r;else if(!!r!==n)return!0}}return!1});let eB={...es,...eF,...ei},{merge:eG}=l();class eZ extends V{constructor(e){let t=eG(eZ.defaultOptions,e);super(t),this.columns=[],this.headers=[],this.dataTypes=[],this.options=t}export(e,t=this.options){let{useLocalDecimalPoint:r,lineDelimiter:s}=t,n=!1!==this.options.firstRowAsNames,{decimalPoint:i,itemDelimiter:o}=t;i||(i=","!==o&&r?1.1.toLocaleString()[1]:"."),o||(o=","===i?";":",");let l=e.getSortedColumns(t.usePresentationOrder),a=Object.keys(l),u=[],h=a.length,m=[];n&&u.push(a.map(e=>`"${e}"`).join(o));for(let t=0;t<h;t++){let r,s=a[t],n=l[s],f=n.length,c=e.whatIs(s);c&&(r=c.dataType);for(let e=0;e<f;e++){let s=n[e];if(m[e]||(m[e]=[]),"string"===r?s='"'+s+'"':"number"==typeof s?s=String(s).replace(".",i):"string"==typeof s&&(s=`"${s}"`),m[e][t]=s,t===h-1){let r=t;for(;m[e].length>2&&void 0===m[e][r];)m[e].pop(),r--;u.push(m[e].join(o))}}}return u.join(s)}parse(e,t){let r=this.dataTypes,s=eG(this.options,e),{beforeParse:n,lineDelimiter:i,firstRowAsNames:o,itemDelimiter:l}=s,a,u=0,{csv:h,startRow:m,endRow:f}=s,c;if(this.columns=[],this.emit({type:"parse",columns:this.columns,detail:t,headers:this.headers}),h&&n&&(h=n(h)),h){if(a=h.replace(/\r\n|\r/g,"\n").split(i||"\n"),(!m||m<0)&&(m=0),(!f||f>=a.length)&&(f=a.length-1),l||(this.guessedItemDelimiter=this.guessDelimiter(a)),o){let e=a[0].split(l||this.guessedItemDelimiter||",");for(let t=0;t<e.length;t++)e[t]=e[t].trim().replace(/^["']|["']$/g,"");this.headers=e,m++}let e=0;for(u=m;u<=f;u++)"#"===a[u][0]?e++:this.parseCSVRow(a[u],u-m-e);r.length&&r[0].length&&"date"===r[0][1]&&!this.options.dateFormat&&this.deduceDateFormat(this.columns[0],null,!0);for(let e=0,t=this.columns.length;e<t;++e){c=this.columns[e];for(let t=0,r=c.length;t<r;++t)if(c[t]&&"string"==typeof c[t]){let r=this.asGuessedType(c[t]);r instanceof Date&&(r=r.getTime()),this.columns[e][t]=r}}}this.emit({type:"afterParse",columns:this.columns,detail:t,headers:this.headers})}parseCSVRow(e,t){let r=this,s=r.columns||[],n=r.dataTypes,{startColumn:i,endColumn:o}=r.options,l=r.options.itemDelimiter||r.guessedItemDelimiter,{decimalPoint:a}=r.options;a&&a!==l||(a=r.guessedDecimalPoint||".");let u=0,h="",m="",f=0,c=0,d=t=>{h=e[t]},g=e=>{n.length<c+1&&n.push([e]),n[c][n[c].length-1]!==e&&n[c].push(e)},p=()=>{if(i>f||f>o){++f,m="";return}if("string"==typeof m?!isNaN(parseFloat(m))&&isFinite(m)?(m=parseFloat(m),g("number")):isNaN(Date.parse(m))?g("string"):(m=m.replace(/\//g,"-"),g("date")):g("number"),s.length<c+1&&s.push([]),"number"!=typeof m&&"number"!==r.guessType(m)&&a){let e=m;m=m.replace(a,"."),"number"!==r.guessType(m)&&(m=e)}s[c][t]=m,m="",++c,++f};if(e.trim().length&&"#"!==e.trim()[0]){for(;u<e.length;u++){if(d(u),"#"===h&&!/^#[A-F\d]{3,3}|[A-F\d]{6,6}/i.test(e.substring(u))){p();return}if('"'===h)for(d(++u);u<e.length&&'"'!==h;)m+=h,d(++u);else h===l?p():m+=h}p()}}guessDelimiter(e){let t=0,r=0,s,n={",":0,";":0,"	":0},i=e.length;for(let s=0;s<i;s++){let i=!1,o,l,a,u="";if(s>13)break;let h=e[s];for(let e=0;e<h.length&&(o=h[e],l=h[e+1],a=h[e-1],"#"!==o);e++){if('"'===o){if(i){if('"'!==a&&'"'!==l){for(;" "===l&&e<h.length;)l=h[++e];void 0!==n[l]&&n[l]++,i=!1}}else i=!0}else void 0!==n[o]?(isNaN(Date.parse(u=u.trim()))?(isNaN(Number(u))||!isFinite(Number(u)))&&n[o]++:n[o]++,u=""):u+=o;","===o&&r++,"."===o&&t++}}return n[";"]>n[","]?s=";":(n[","],n[";"],s=","),t>r?this.guessedDecimalPoint=".":this.guessedDecimalPoint=",",s}getTable(){return V.getTableFromColumns(this.columns,this.headers)}}eZ.defaultOptions={...V.defaultOptions,lineDelimiter:"\n"},V.registerType("CSV",eZ);let{merge:eJ}=l();class eX extends D{constructor(e){let t=eJ(eX.defaultOptions,e);super(t),this.converter=new eZ(t),this.options=t,t.enablePolling&&this.startPolling(1e3*Math.max(t.dataRefreshRate||0,1))}load(e){let t=this,r=t.converter,s=t.table,{csv:n,csvURL:i,dataModifier:o}=t.options;return t.emit({type:"load",csv:n,detail:e,table:s}),Promise.resolve(i?fetch(i).then(e=>e.text()):n||"").then(e=>(e&&(s.deleteColumns(),r.parse({csv:e}),s.setColumns(r.getTable().getColumns())),t.setModifierOptions(o).then(()=>e))).then(r=>(t.emit({type:"afterLoad",csv:r,detail:e,table:s}),t)).catch(r=>{throw t.emit({type:"loadError",detail:e,error:r,table:s}),r})}}eX.defaultOptions={csv:"",csvURL:"",enablePolling:!1,dataRefreshRate:1,firstRowAsNames:!0},D.registerType("CSV",eX);let{error:eK,isArray:eq,merge:ez,objectEach:eQ}=l();class eW extends V{constructor(e){let t=ez(eW.defaultOptions,e);super(t),this.columns=[],this.headers=[],this.options=t,this.table=new A}parse(e,t){let{beforeParse:r,orientation:s,firstRowAsNames:n,columnNames:i}=e=ez(this.options,e),o=e.data;if(o){if(this.columns=[],this.emit({type:"parse",columns:this.columns,detail:t,headers:this.headers}),r&&(o=r(o)),o=o.slice(),"columns"===s)for(let e=0,t=o.length;e<t;e++){let t=o[e];if(!(t instanceof Array))return;this.headers instanceof Array?(n?this.headers.push(`${t.shift()}`):i&&i instanceof Array&&this.headers.push(i[e]),this.table.setColumn(this.headers[e]||e.toString(),t)):eK("JSONConverter: Invalid `columnNames` option.",!1)}else if("rows"===s){n?this.headers=o.shift():i&&(this.headers=i);for(let e=0,t=o.length;e<t;e++){let t=o[e];if(eq(t))for(let e=0,r=t.length;e<r;e++)this.columns.length<e+1&&this.columns.push([]),this.columns[e].push(t[e]),this.headers instanceof Array?this.table.setColumn(this.headers[e]||e.toString(),this.columns[e]):eK("JSONConverter: Invalid `columnNames` option.",!1);else{let r=this.headers;if(r&&!(r instanceof Array)){let e={};eQ(r,(r,s)=>{e[s]=r.reduce((e,t)=>e[t],t)}),t=e}this.table.setRows([t],e)}}}this.emit({type:"afterParse",columns:this.columns,detail:t,headers:this.headers})}}getTable(){return this.table}}eW.defaultOptions={...V.defaultOptions,data:[],orientation:"rows"},V.registerType("JSON",eW);let{merge:e0}=l();class e1 extends D{constructor(e){let t=e0(e1.defaultOptions,e);super(t),this.converter=new eW(t),this.options=t,t.enablePolling&&this.startPolling(1e3*Math.max(t.dataRefreshRate||0,1))}load(e){let t=this,r=t.converter,s=t.table,{data:n,dataUrl:i,dataModifier:o}=t.options;return t.emit({type:"load",data:n,detail:e,table:s}),Promise.resolve(i?fetch(i).then(e=>e.json()).catch(r=>{t.emit({type:"loadError",detail:e,error:r,table:s}),console.warn(`Unable to fetch data from ${i}.`)}):n||[]).then(e=>(e&&(s.deleteColumns(),r.parse({data:e}),s.setColumns(r.getTable().getColumns())),t.setModifierOptions(o).then(()=>e))).then(r=>(t.emit({type:"afterLoad",data:r,detail:e,table:s}),t)).catch(r=>{throw t.emit({type:"loadError",detail:e,error:r,table:s}),r})}}e1.defaultOptions={data:[],enablePolling:!1,dataRefreshRate:0,firstRowAsNames:!0,orientation:"rows"},D.registerType("JSON",e1);let{merge:e2,uniqueKey:e3}=l();class e4 extends V{constructor(e){let t=e2(e4.defaultOptions,e);super(t),this.columns=[],this.header=[],this.options=t}parse(e,t){let r,s=e2(this.options,e),n=(s.json?.values||[]).map(e=>e.slice());if(0===n.length)return!1;this.header=[],this.columns=[],this.emit({type:"parse",columns:this.columns,detail:t,headers:this.header});let{beforeParse:i,json:o}=s;i&&o&&(n=i(o.values)),this.columns=n;for(let e=0,t=n.length;e<t;e++){r=n[e],this.header[e]=s.firstRowAsNames?`${r.shift()}`:e3();for(let t=0,s=r.length;t<s;++t)if(r[t]&&"string"==typeof r[t]){let s=this.asGuessedType(r[t]);s instanceof Date&&(s=s.getTime()),this.columns[e][t]=s}}this.emit({type:"afterParse",columns:this.columns,detail:t,headers:this.header})}getTable(){return V.getTableFromColumns(this.columns,this.header)}}e4.defaultOptions={...V.defaultOptions},V.registerType("GoogleSheets",e4);let{merge:e6,pick:e9}=l();class e5 extends D{constructor(e){let t=e6(e5.defaultOptions,e);super(t),this.converter=new e4(t),this.options=t}load(e){let t=this,r=t.converter,s=t.table,{dataModifier:n,dataRefreshRate:i,enablePolling:o,firstRowAsNames:l,googleAPIKey:a,googleSpreadsheetKey:u}=t.options,h=e5.buildFetchURL(a,u,t.options);if(t.emit({type:"load",detail:e,table:s,url:h}),!URL.canParse(h))throw Error("Invalid URL: "+h);return fetch(h).then(e=>e.json()).then(e=>{if("object"==typeof e&&e&&"object"==typeof e.error&&e.error&&"number"==typeof e.error.code&&"string"==typeof e.error.message&&"string"==typeof e.error.status)throw Error(e.error.message);return r.parse({firstRowAsNames:l,json:e}),s.deleteColumns(),s.setColumns(r.getTable().getColumns()),t.setModifierOptions(n)}).then(()=>(t.emit({type:"afterLoad",detail:e,table:s,url:h}),o&&setTimeout(()=>t.load(),1e3*Math.max(i||0,1)),t)).catch(r=>{throw t.emit({type:"loadError",detail:e,error:r,table:s}),r})}}e5.defaultOptions={googleAPIKey:"",googleSpreadsheetKey:"",enablePolling:!1,dataRefreshRate:2,firstRowAsNames:!0},function(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZ";function r(e={}){let{endColumn:s,endRow:n,googleSpreadsheetRange:i,startColumn:o,startRow:l}=e;return i||(t[o||0]||"A")+(Math.max(l||0,0)+1)+":"+(t[e9(s,25)]||"Z")+(n?Math.max(n,0):"Z")}e.buildFetchURL=function(e,t,s={}){let n=new URL(`https://sheets.googleapis.com/v4/spreadsheets/${t}/values/`),i=s.onlyColumnNames?"A1:Z1":r(s);n.pathname+=i;let o=n.searchParams;return o.set("alt","json"),s.onlyColumnNames||(o.set("dateTimeRenderOption","FORMATTED_STRING"),o.set("majorDimension","COLUMNS"),o.set("valueRenderOption","UNFORMATTED_VALUE")),o.set("prettyPrint","false"),o.set("key",e),n.href},e.buildQueryRange=r}(e5||(e5={})),D.registerType("GoogleSheets",e5);let{merge:e7}=l();class e8 extends V{constructor(e){let t=e7(e8.defaultOptions,e);super(t),this.columns=[],this.headers=[],this.options=t,t.tableElement&&(this.tableElement=t.tableElement,this.tableElementID=t.tableElement.id)}export(e,t=this.options){let r=!1!==t.firstRowAsNames,s=t.useMultiLevelHeaders,n=e.getSortedColumns(t.usePresentationOrder),i=Object.keys(n),o=[],l=i.length,a=[],u="";if(r){let e=[];if(s){for(let t of i){let r=n[t];Array.isArray(r)||(r=Array.from(r));let s=(r.shift()||"").toString();n[t]=r,e.push(s)}u=this.getTableHeaderHTML(i,e,t)}else u=this.getTableHeaderHTML(void 0,i,t)}for(let e=0;e<l;e++){let t=n[i[e]],r=t.length;for(let s=0;s<r;s++){let r=t[s];a[s]||(a[s]=[]),"string"!=typeof r&&"number"!=typeof r&&void 0!==r&&(r=(r||"").toString()),a[s][e]=this.getCellHTMLFromValue(e?"td":"th",null,e?"":'scope="row"',r),e===l-1&&o.push("<tr>"+a[s].join("")+"</tr>")}}let h="";return t.tableCaption&&(h='<caption class="highcharts-table-caption">'+t.tableCaption+"</caption>"),"<table>"+h+u+"<tbody>"+o.join("")+"</tbody></table>"}getCellHTMLFromValue(e,t,r,s,n){let i=s,o="text"+(t?" "+t:"");return"number"==typeof i?(i=i.toString(),","===n&&(i=i.replace(".",n)),o="number"):s||(i="",o="empty"),"<"+e+(r?" "+r:"")+' class="'+o+'">'+i+"</"+e+">"}getTableHeaderHTML(e=[],t=[],r=this.options){let{useMultiLevelHeaders:s,useRowspanHeaders:n}=r,i="<thead>",o=0,l=t&&t.length,a,u=0,h;if(s&&e&&t&&!function(e,t){let r=e.length;if(t.length!==r)return!1;for(;--r;)if(e[r]!==t[r])return!1;return!0}(e,t)){for(i+="<tr>";o<l;++o)(a=e[o])===e[o+1]?++u:u?(i+=this.getCellHTMLFromValue("th","highcharts-table-topheading",'scope="col" colspan="'+(u+1)+'"',a),u=0):(a===t[o]?n?(h=2,delete t[o]):(h=1,t[o]=""):h=1,i+=this.getCellHTMLFromValue("th","highcharts-table-topheading",'scope="col"'+(h>1?' valign="top" rowspan="'+h+'"':""),a));i+="</tr>"}if(t){for(i+="<tr>",o=0,l=t.length;o<l;++o)void 0!==t[o]&&(i+=this.getCellHTMLFromValue("th",null,'scope="col"',t[o]));i+="</tr>"}return i+"</thead>"}parse(e,t){let r=[],s=[],n=e7(this.options,e),{endRow:i,startColumn:o,endColumn:l,firstRowAsNames:a}=n,u=n.tableElement||this.tableElement;if(!(u instanceof HTMLElement)){this.emit({type:"parseError",columns:r,detail:t,headers:s,error:"Not a valid HTML Table"});return}this.tableElement=u,this.tableElementID=u.id,this.emit({type:"parse",columns:this.columns,detail:t,headers:this.headers});let h=u.getElementsByTagName("tr"),m=h.length,f=0,c,{startRow:d}=n;if(a&&m){let e=h[0].children,t=e.length;for(let r=o;r<t&&!(r>l);r++)("TD"===(c=e[r]).tagName||"TH"===c.tagName)&&s.push(c.innerHTML);d++}for(;f<m;){if(f>=d&&f<=i){let e=h[f].children,t=e.length,s=0;for(;s<t;){let t=s-o,n=r[t];if(("TD"===(c=e[s]).tagName||"TH"===c.tagName)&&s>=o&&s<=l){r[t]||(r[t]=[]);let e=this.asGuessedType(c.innerHTML);e instanceof Date&&(e=e.getTime()),r[t][f-d]=e;let s=1;for(;f-d>=s&&void 0===n[f-d-s];)n[f-d-s]=null,s++}s++}}f++}this.columns=r,this.headers=s,this.emit({type:"afterParse",columns:r,detail:t,headers:s})}getTable(){return V.getTableFromColumns(this.columns,this.headers)}}e8.defaultOptions={...V.defaultOptions,useRowspanHeaders:!0,useMultiLevelHeaders:!0},V.registerType("HTMLTable",e8);let{win:te}=l(),{merge:tt}=l();class tr extends D{constructor(e){let t=tt(tr.defaultOptions,e);super(t),this.converter=new e8(t),this.options=t}load(e){let t,r=this,s=r.converter,n=r.table,{dataModifier:i,table:o}=r.options;if(r.emit({type:"load",detail:e,table:n,tableElement:r.tableElement}),"string"==typeof o?(r.tableID=o,t=te.document.getElementById(o)):r.tableID=(t=o).id,r.tableElement=t||void 0,!r.tableElement){let t="HTML table not provided, or element with ID not found";return r.emit({type:"loadError",detail:e,error:t,table:n}),Promise.reject(Error(t))}return s.parse(tt({tableElement:r.tableElement},r.options),e),n.deleteColumns(),n.setColumns(s.getTable().getColumns()),r.setModifierOptions(i).then(()=>(r.emit({type:"afterLoad",detail:e,table:n,tableElement:r.tableElement}),r))}}tr.defaultOptions={table:""},D.registerType("HTMLTable",tr);let{merge:ts}=l();class tn extends f{constructor(e,...t){super(),this.chain=t,this.options=ts(tn.defaultOptions,e);let r=this.options.chain||[];for(let e=0,s=r.length,n,i;e<s;++e)(n=r[e]).type&&(i=f.types[n.type])&&t.push(new i(n))}add(e,t){this.emit({type:"addModifier",detail:t,modifier:e}),this.chain.push(e),this.emit({type:"addModifier",detail:t,modifier:e})}clear(e){this.emit({type:"clearChain",detail:e}),this.chain.length=0,this.emit({type:"afterClearChain",detail:e})}async modify(e,t){let r=this.options.reverse?this.chain.slice().reverse():this.chain.slice();e.modified===e&&(e.modified=e.clone(!1,t));let s=e;for(let n=0,i=r.length;n<i;++n){try{await r[n].modify(s,t)}catch(r){throw this.emit({type:"error",detail:t,table:e}),r}s=s.modified}return e.modified=s,e}modifyCell(e,t,r,s,n){let i=this.options.reverse?this.chain.reverse():this.chain;if(i.length){let o=e.clone();for(let e=0,l=i.length;e<l;++e)i[e].modifyCell(o,t,r,s,n),o=o.modified;e.modified=o}return e}modifyColumns(e,t,r,s){let n=this.options.reverse?this.chain.reverse():this.chain.slice();if(n.length){let i=e.clone();for(let e=0,o=n.length;e<o;++e)n[e].modifyColumns(i,t,r,s),i=i.modified;e.modified=i}return e}modifyRows(e,t,r,s){let n=this.options.reverse?this.chain.reverse():this.chain.slice();if(n.length){let i=e.clone();for(let e=0,o=n.length;e<o;++e)n[e].modifyRows(i,t,r,s),i=i.modified;e.modified=i}return e}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=this.options.reverse?this.chain.reverse():this.chain.slice(),s=e.modified;for(let e=0,n=r.length;e<n;++e)s=r[e].modifyTable(s,t).modified;return e.modified=s,this.emit({type:"afterModify",detail:t,table:e}),e}remove(e,t){let r=this.chain;this.emit({type:"removeModifier",detail:t,modifier:e}),r.splice(r.indexOf(e),1),this.emit({type:"afterRemoveModifier",detail:t,modifier:e})}}tn.defaultOptions={type:"Chain"},f.registerType("Chain",tn);let{merge:ti}=l();class to extends f{constructor(e){super(),this.options=ti(to.defaultOptions,e)}modifyCell(e,t,r,s,n){let i=e.modified,o=i.getRowIndexBy("columnNames",t);return void 0===o?i.setColumns(this.modifyTable(e.clone()).getColumns(),void 0,n):i.setCell(`${r}`,o,s,n),e}modifyColumns(e,t,r,s){let n=e.modified,i=n.getColumn("columnNames")||[],o=e.getColumnNames(),l=e.getRowCount()!==i.length;if(!l){for(let e=0,t=o.length;e<t;++e)if(o[e]!==i[e]){l=!0;break}}if(l)return this.modifyTable(e,s);o=Object.keys(t);for(let e=0,i=o.length,l,a,u;e<i;++e){l=t[a=o[e]],u=n.getRowIndexBy("columnNames",a)||n.getRowCount();for(let e=0,t=r,i=l.length;e<i;++e,++t)n.setCell(`${t}`,u,l[e],s)}return e}modifyRows(e,t,r,s){let n=e.getColumnNames(),i=e.modified,o=i.getColumn("columnNames")||[],l=e.getRowCount()!==o.length;if(!l){for(let e=0,t=n.length;e<t;++e)if(n[e]!==o[e]){l=!0;break}}if(l)return this.modifyTable(e,s);for(let e=0,o=r,l=t.length,a;e<l;++e,++o)if((a=t[e])instanceof Array)i.setColumn(`${o}`,a);else for(let e=0,t=n.length;e<t;++e)i.setCell(`${o}`,e,a[n[e]],s);return e}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=e.modified;if(e.hasColumns(["columnNames"])){let t=(e.deleteColumns(["columnNames"])||{}).columnNames||[],s={},n=[];for(let e=0,r=t.length;e<r;++e)n.push(""+t[e]);for(let t=0,r=e.getRowCount(),i;t<r;++t)(i=e.getRow(t))&&(s[n[t]]=i);r.deleteColumns(),r.setColumns(s)}else{let t={};for(let r=0,s=e.getRowCount(),n;r<s;++r)(n=e.getRow(r))&&(t[`${r}`]=n);t.columnNames=e.getColumnNames(),r.deleteColumns(),r.setColumns(t)}return this.emit({type:"afterModify",detail:t,table:e}),e}}to.defaultOptions={type:"Invert"},f.registerType("Invert",to);class tl extends f{constructor(e){super(),this.options={...tl.defaultOptions,...e}}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=this.options.alternativeSeparators,s=this.options.formulaColumns||e.getColumnNames(),n=e.modified;for(let t=0,r=s.length,i;t<r;++t)i=s[t],s.indexOf(i)>=0&&n.setColumn(i,this.processColumn(e,i));let i=this.options.columnFormulas||[];for(let t=0,s=i.length,o,l;t<s;++t)o=i[t],l=es.parseFormula(o.formula,r),n.setColumn(o.column,this.processColumnFormula(l,e,o.rowStart,o.rowEnd));return this.emit({type:"afterModify",detail:t,table:e}),e}processColumn(e,t,r=0){let s=this.options.alternativeSeparators,n=(e.getColumn(t,!0)||[]).slice(r>0?r:0);for(let t=0,r=n.length,i=[],o;t<r;++t)if("string"==typeof(o=n[t])&&"="===o[0])try{i=""===o?i:es.parseFormula(o.substring(1),s),n[t]=eF.processFormula(i,e)}catch{n[t]=NaN}return n}processColumnFormula(e,t,r=0,s=t.getRowCount()){r=r>=0?r:0,s=s>=0?s:t.getRowCount()+s;let n=[],i=t.modified;for(let t=0,o=s-r;t<o;++t)try{n[t]=eF.processFormula(e,i)}catch{n[t]=NaN}finally{e=eF.translateReferences(e,0,1)}return n}}tl.defaultOptions={type:"Math",alternativeSeparators:!1},f.registerType("Math",tl);let{merge:ta}=l();class tu extends f{constructor(e){super(),this.options=ta(tu.defaultOptions,e)}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=[],{additive:s,ranges:n,strict:i}=this.options;if(n.length){let t=e.modified,o=e.getColumns(),l=[];for(let a=0,u=n.length,h,m;a<u;++a)if(h=n[a],!i||typeof h.minValue==typeof h.maxValue){a>0&&!s&&(t.deleteRows(),t.setRows(l),t.setOriginalRowIndexes(r,!0),o=t.getColumns(),l=[],r=[]),m=o[h.column]||[];for(let n=0,o=m.length,a,u,f;n<o;++n){switch(typeof(a=m[n])){default:continue;case"boolean":case"number":case"string":}(!i||typeof a==typeof h.minValue)&&a>=h.minValue&&a<=h.maxValue&&(s?(u=e.getRow(n),f=e.getOriginalRowIndex(n)):(u=t.getRow(n),f=t.getOriginalRowIndex(n)),u&&(l.push(u),r.push(f)))}}t.deleteRows(),t.setRows(l),t.setOriginalRowIndexes(r)}return this.emit({type:"afterModify",detail:t,table:e}),e}}tu.defaultOptions={type:"Range",ranges:[]},f.registerType("Range",tu);let{merge:th}=l();class tm extends f{static ascending(e,t){return(e||0)<(t||0)?-1:+((e||0)>(t||0))}static descending(e,t){return(t||0)<(e||0)?-1:+((t||0)>(e||0))}constructor(e){super(),this.options=th(tm.defaultOptions,e)}getRowReferences(e){let t=e.getRows(),r=[];for(let e=0,s=t.length;e<s;++e)r.push({index:e,row:t[e]});return r}modifyCell(e,t,r,s,n){let{orderByColumn:i,orderInColumn:o}=this.options;return t===i&&(o?(e.modified.setCell(t,r,s),e.modified.setColumn(o,this.modifyTable(new A({columns:e.getColumns([i,o])})).modified.getColumn(o))):this.modifyTable(e,n)),e}modifyColumns(e,t,r,s){let{orderByColumn:n,orderInColumn:i}=this.options,o=Object.keys(t);return o.indexOf(n)>-1&&(i&&t[o[0]].length?(e.modified.setColumns(t,r),e.modified.setColumn(i,this.modifyTable(new A({columns:e.getColumns([n,i])})).modified.getColumn(i))):this.modifyTable(e,s)),e}modifyRows(e,t,r,s){let{orderByColumn:n,orderInColumn:i}=this.options;return i&&t.length?(e.modified.setRows(t,r),e.modified.setColumn(i,this.modifyTable(new A({columns:e.getColumns([n,i])})).modified.getColumn(i))):this.modifyTable(e,s),e}modifyTable(e,t){this.emit({type:"modify",detail:t,table:e});let r=e.getColumnNames(),s=e.getRowCount(),n=this.getRowReferences(e),{direction:i,orderByColumn:o,orderInColumn:l}=this.options,a="asc"===i?tm.ascending:tm.descending,u=r.indexOf(o),h=e.modified;if(-1!==u&&n.sort((e,t)=>a(e.row[u],t.row[u])),l){let e=[];for(let t=0;t<s;++t)e[n[t].index]=t;h.setColumns({[l]:e})}else{let e,t=[],r=[];for(let i=0;i<s;++i)e=n[i],t.push(h.getOriginalRowIndex(e.index)),r.push(e.row);h.setRows(r,0),h.setOriginalRowIndexes(t)}return this.emit({type:"afterModify",detail:t,table:e}),e}}tm.defaultOptions={type:"Sort",direction:"desc",orderByColumn:"y"},f.registerType("Sort",tm);let tf=l();tf.DataConnector=tf.DataConnector||D,tf.DataConverter=tf.DataConverter||V,tf.DataCursor=tf.DataCursor||$,tf.DataModifier=tf.DataModifier||f,tf.DataPool=tf.DataPool||H,tf.DataTable=tf.DataTable||A,tf.Formula=tf.Formula||eB;let tc=l();return i.default})());