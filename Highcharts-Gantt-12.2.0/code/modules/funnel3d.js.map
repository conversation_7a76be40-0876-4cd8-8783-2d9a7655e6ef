{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/funnel3d\n * @requires highcharts\n * @requires highcharts/highcharts-3d\n * @requires highcharts/modules/cylinder\n *\n * Highcharts funnel module\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/funnel3d\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Color\"],amd1[\"RendererRegistry\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/funnel3d\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Color\"], root[\"_Highcharts\"][\"RendererRegistry\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"RendererRegistry\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__608__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 608:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 620:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ funnel3d_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n;// ./code/es-modules/Series/Funnel3D/SVGElement3DFunnel.js\n/* *\n *\n *  Highcharts funnel3d series module\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default());\n\nconst { charts } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { Element3D: SVGElement3D } = highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType().prototype;\n\nconst { merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass SVGElement3DFunnel extends SVGElement3D {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.mainParts = ['top', 'bottom'];\n        this.parts = [\n            'top', 'bottom',\n            'frontUpper', 'backUpper',\n            'frontLower', 'backLower',\n            'rightUpper', 'rightLower'\n        ];\n        this.sideGroups = [\n            'upperGroup', 'lowerGroup'\n        ];\n        this.sideParts = {\n            upperGroup: ['frontUpper', 'backUpper', 'rightUpper'],\n            lowerGroup: ['frontLower', 'backLower', 'rightLower']\n        };\n        this.pathType = 'funnel3d';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    // override opacity and color setters to control opacity\n    opacitySetter(value) {\n        const funnel3d = this, opacity = parseFloat(value), parts = funnel3d.parts, chart = charts[funnel3d.renderer.chartIndex], filterId = 'group-opacity-' + opacity + '-' + chart.index;\n        // Use default for top and bottom\n        funnel3d.parts = funnel3d.mainParts;\n        funnel3d.singleSetterForParts('opacity', opacity);\n        // Restore\n        funnel3d.parts = parts;\n        if (!chart.renderer.filterId) {\n            chart.renderer.definition({\n                tagName: 'filter',\n                attributes: {\n                    id: filterId\n                },\n                children: [{\n                        tagName: 'feComponentTransfer',\n                        children: [{\n                                tagName: 'feFuncA',\n                                attributes: {\n                                    type: 'table',\n                                    tableValues: '0 ' + opacity\n                                }\n                            }]\n                    }]\n            });\n            for (const groupName of funnel3d.sideGroups) {\n                funnel3d[groupName].attr({\n                    filter: 'url(#' + filterId + ')'\n                });\n            }\n            // Styled mode\n            if (funnel3d.renderer.styledMode) {\n                chart.renderer.definition({\n                    tagName: 'style',\n                    textContent: '.highcharts-' + filterId +\n                        ' {filter:url(#' + filterId + ')}'\n                });\n                for (const groupName of funnel3d.sideGroups) {\n                    funnel3d[groupName].addClass('highcharts-' + filterId);\n                }\n            }\n        }\n        return funnel3d;\n    }\n    fillSetter(fill) {\n        let fillColor = color(fill);\n        // Extract alpha channel to use the opacitySetter\n        const funnel3d = this, alpha = fillColor.rgba[3], partsWithColor = {\n            // Standard color for top and bottom\n            top: color(fill).brighten(0.1).get(),\n            bottom: color(fill).brighten(-0.2).get()\n        };\n        if (alpha < 1) {\n            fillColor.rgba[3] = 1;\n            fillColor = fillColor.get('rgb');\n            // Set opacity through the opacitySetter\n            funnel3d.attr({\n                opacity: alpha\n            });\n        }\n        else {\n            // Use default for full opacity\n            fillColor = fill;\n        }\n        // Add gradient for sides\n        if (!fillColor.linearGradient &&\n            !fillColor.radialGradient &&\n            funnel3d.gradientForSides) {\n            fillColor = {\n                linearGradient: { x1: 0, x2: 1, y1: 1, y2: 1 },\n                stops: [\n                    [0, color(fill).brighten(-0.2).get()],\n                    [0.5, fill],\n                    [1, color(fill).brighten(-0.2).get()]\n                ]\n            };\n        }\n        // Gradient support\n        if (fillColor.linearGradient) {\n            // Color in steps, as each gradient will generate a key\n            for (const sideGroupName of funnel3d.sideGroups) {\n                const box = funnel3d[sideGroupName].gradientBox, gradient = fillColor.linearGradient, alteredGradient = merge(fillColor, {\n                    linearGradient: {\n                        x1: box.x + gradient.x1 * box.width,\n                        y1: box.y + gradient.y1 * box.height,\n                        x2: box.x + gradient.x2 * box.width,\n                        y2: box.y + gradient.y2 * box.height\n                    }\n                });\n                for (const partName of funnel3d.sideParts[sideGroupName]) {\n                    partsWithColor[partName] = alteredGradient;\n                }\n            }\n        }\n        else {\n            merge(true, partsWithColor, {\n                frontUpper: fillColor,\n                backUpper: fillColor,\n                rightUpper: fillColor,\n                frontLower: fillColor,\n                backLower: fillColor,\n                rightLower: fillColor\n            });\n            if (fillColor.radialGradient) {\n                for (const sideGroupName of funnel3d.sideGroups) {\n                    const gradBox = funnel3d[sideGroupName].gradientBox, centerX = gradBox.x + gradBox.width / 2, centerY = gradBox.y + gradBox.height / 2, diameter = Math.min(gradBox.width, gradBox.height);\n                    for (const partName of funnel3d.sideParts[sideGroupName]) {\n                        funnel3d[partName].setRadialReference([\n                            centerX, centerY, diameter\n                        ]);\n                    }\n                }\n            }\n        }\n        funnel3d.singleSetterForParts('fill', null, partsWithColor);\n        // Fill for animation getter (#6776)\n        funnel3d.color = funnel3d.fill = fill;\n        // Change gradientUnits to userSpaceOnUse for linearGradient\n        if (fillColor.linearGradient) {\n            for (const part of [funnel3d.frontLower, funnel3d.frontUpper]) {\n                const elem = part.element, grad = (elem &&\n                    funnel3d.renderer.gradients[elem.gradient]);\n                if (grad &&\n                    grad.attr('gradientUnits') !== 'userSpaceOnUse') {\n                    grad.attr({\n                        gradientUnits: 'userSpaceOnUse'\n                    });\n                }\n            }\n        }\n        return funnel3d;\n    }\n    adjustForGradient() {\n        const funnel3d = this;\n        let bbox;\n        for (const sideGroupName of funnel3d.sideGroups) {\n            // Use common extremes for groups for matching gradients\n            let topLeftEdge = {\n                x: Number.MAX_VALUE,\n                y: Number.MAX_VALUE\n            }, bottomRightEdge = {\n                x: -Number.MAX_VALUE,\n                y: -Number.MAX_VALUE\n            };\n            // Get extremes\n            for (const partName of funnel3d.sideParts[sideGroupName]) {\n                const part = funnel3d[partName];\n                bbox = part.getBBox(true);\n                topLeftEdge = {\n                    x: Math.min(topLeftEdge.x, bbox.x),\n                    y: Math.min(topLeftEdge.y, bbox.y)\n                };\n                bottomRightEdge = {\n                    x: Math.max(bottomRightEdge.x, bbox.x + bbox.width),\n                    y: Math.max(bottomRightEdge.y, bbox.y + bbox.height)\n                };\n            }\n            // Store for color fillSetter\n            funnel3d[sideGroupName].gradientBox = {\n                x: topLeftEdge.x,\n                width: bottomRightEdge.x - topLeftEdge.x,\n                y: topLeftEdge.y,\n                height: bottomRightEdge.y - topLeftEdge.y\n            };\n        }\n    }\n    zIndexSetter() {\n        // `this.added` won't work, because zIndex is set after the prop is set,\n        // but before the graphic is really added\n        if (this.finishedOnAdd) {\n            this.adjustForGradient();\n        }\n        // Run default\n        return this.renderer.Element.prototype.zIndexSetter.apply(this, arguments);\n    }\n    onAdd() {\n        this.adjustForGradient();\n        this.finishedOnAdd = true;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Funnel3D_SVGElement3DFunnel = (SVGElement3DFunnel);\n\n;// ./code/es-modules/Series/Funnel3D/Funnel3DComposition.js\n/* *\n *\n *  Highcharts funnel3d series module\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { charts: Funnel3DComposition_charts } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { error, extend, merge: Funnel3DComposition_merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction compose(SVGRendererClass) {\n    const rendererProto = SVGRendererClass.prototype;\n    if (!rendererProto.funnel3d) {\n        rendererProto.Element3D.types.funnel3d = Funnel3D_SVGElement3DFunnel;\n        extend(rendererProto, {\n            funnel3d: rendererFunnel3d,\n            funnel3dPath: rendererFunnel3dPath\n        });\n    }\n}\n/** @private */\nfunction rendererFunnel3d(shapeArgs) {\n    const renderer = this, funnel3d = renderer.element3d('funnel3d', shapeArgs), styledMode = renderer.styledMode, \n    // Hide stroke for Firefox\n    strokeAttrs = {\n        'stroke-width': 1,\n        stroke: 'none'\n    };\n    // Create groups for sides for opacity setter\n    funnel3d.upperGroup = renderer.g('funnel3d-upper-group').attr({\n        zIndex: funnel3d.frontUpper.zIndex\n    }).add(funnel3d);\n    for (const upperElem of [funnel3d.frontUpper, funnel3d.backUpper, funnel3d.rightUpper]) {\n        if (!styledMode) {\n            upperElem.attr(strokeAttrs);\n        }\n        upperElem.add(funnel3d.upperGroup);\n    }\n    funnel3d.lowerGroup = renderer.g('funnel3d-lower-group').attr({\n        zIndex: funnel3d.frontLower.zIndex\n    }).add(funnel3d);\n    for (const lowerElem of [funnel3d.frontLower, funnel3d.backLower, funnel3d.rightLower]) {\n        if (!styledMode) {\n            lowerElem.attr(strokeAttrs);\n        }\n        lowerElem.add(funnel3d.lowerGroup);\n    }\n    funnel3d.gradientForSides = shapeArgs.gradientForSides;\n    return funnel3d;\n}\n/**\n * Generates paths and zIndexes.\n * @private\n */\nfunction rendererFunnel3dPath(shapeArgs) {\n    // Check getCylinderEnd for better error message if\n    // the cylinder module is missing\n    if (!this.getCylinderEnd) {\n        error('A required Highcharts module is missing: cylinder.js', true, Funnel3DComposition_charts[this.chartIndex]);\n    }\n    const renderer = this, chart = Funnel3DComposition_charts[renderer.chartIndex], \n    // Adjust angles for visible edges\n    // based on alpha, selected through visual tests\n    alphaCorrection = shapeArgs.alphaCorrection = 90 - Math.abs((chart.options.chart.options3d.alpha % 180) -\n        90), \n    // Set zIndexes of parts based on cuboid logic, for\n    // consistency\n    cuboidData = this.cuboidPath.call(renderer, Funnel3DComposition_merge(shapeArgs, {\n        depth: shapeArgs.width,\n        width: (shapeArgs.width + shapeArgs.bottom.width) / 2\n    })), isTopFirst = cuboidData.isTop, isFrontFirst = !cuboidData.isFront, hasMiddle = !!shapeArgs.middle, \n    //\n    top = renderer.getCylinderEnd(chart, Funnel3DComposition_merge(shapeArgs, {\n        x: shapeArgs.x - shapeArgs.width / 2,\n        z: shapeArgs.z - shapeArgs.width / 2,\n        alphaCorrection: alphaCorrection\n    })), bottomWidth = shapeArgs.bottom.width, bottomArgs = Funnel3DComposition_merge(shapeArgs, {\n        width: bottomWidth,\n        x: shapeArgs.x - bottomWidth / 2,\n        z: shapeArgs.z - bottomWidth / 2,\n        alphaCorrection: alphaCorrection\n    }), bottom = renderer.getCylinderEnd(chart, bottomArgs, true);\n    let middleWidth = bottomWidth, middleTopArgs = bottomArgs, middleTop = bottom, middleBottom = bottom, \n    // Masking for cylinders or a missing part of a side shape\n    useAlphaCorrection;\n    if (hasMiddle) {\n        middleWidth = shapeArgs.middle.width;\n        middleTopArgs = Funnel3DComposition_merge(shapeArgs, {\n            y: (shapeArgs.y +\n                shapeArgs.middle.fraction * shapeArgs.height),\n            width: middleWidth,\n            x: shapeArgs.x - middleWidth / 2,\n            z: shapeArgs.z - middleWidth / 2\n        });\n        middleTop = renderer.getCylinderEnd(chart, middleTopArgs, false);\n        middleBottom = renderer.getCylinderEnd(chart, middleTopArgs, false);\n    }\n    const ret = {\n        top: top,\n        bottom: bottom,\n        frontUpper: renderer.getCylinderFront(top, middleTop),\n        zIndexes: {\n            group: cuboidData.zIndexes.group,\n            top: isTopFirst !== 0 ? 0 : 3,\n            bottom: isTopFirst !== 1 ? 0 : 3,\n            frontUpper: isFrontFirst ? 2 : 1,\n            backUpper: isFrontFirst ? 1 : 2,\n            rightUpper: isFrontFirst ? 2 : 1\n        }\n    };\n    ret.backUpper = renderer.getCylinderBack(top, middleTop);\n    useAlphaCorrection = (Math.min(middleWidth, shapeArgs.width) /\n        Math.max(middleWidth, shapeArgs.width)) !== 1;\n    ret.rightUpper = renderer.getCylinderFront(renderer.getCylinderEnd(chart, Funnel3DComposition_merge(shapeArgs, {\n        x: shapeArgs.x - shapeArgs.width / 2,\n        z: shapeArgs.z - shapeArgs.width / 2,\n        alphaCorrection: useAlphaCorrection ?\n            -alphaCorrection : 0\n    }), false), renderer.getCylinderEnd(chart, Funnel3DComposition_merge(middleTopArgs, {\n        alphaCorrection: useAlphaCorrection ?\n            -alphaCorrection : 0\n    }), !hasMiddle));\n    if (hasMiddle) {\n        useAlphaCorrection = (Math.min(middleWidth, bottomWidth) /\n            Math.max(middleWidth, bottomWidth)) !== 1;\n        Funnel3DComposition_merge(true, ret, {\n            frontLower: renderer.getCylinderFront(middleBottom, bottom),\n            backLower: renderer.getCylinderBack(middleBottom, bottom),\n            rightLower: renderer.getCylinderFront(renderer.getCylinderEnd(chart, Funnel3DComposition_merge(bottomArgs, {\n                alphaCorrection: useAlphaCorrection ?\n                    -alphaCorrection : 0\n            }), true), renderer.getCylinderEnd(chart, Funnel3DComposition_merge(middleTopArgs, {\n                alphaCorrection: useAlphaCorrection ?\n                    -alphaCorrection : 0\n            }), false)),\n            zIndexes: {\n                frontLower: isFrontFirst ? 2 : 1,\n                backLower: isFrontFirst ? 1 : 2,\n                rightLower: isFrontFirst ? 1 : 2\n            }\n        });\n    }\n    return ret;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Funnel3DComposition = {\n    compose\n};\n/* harmony default export */ const Funnel3D_Funnel3DComposition = (Funnel3DComposition);\n\n;// ./code/es-modules/Series/Funnel3D/Funnel3DSeriesDefaults.js\n/* *\n *\n *  Imports\n *\n * */\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A funnel3d is a 3d version of funnel series type. Funnel charts are\n * a type of chart often used to visualize stages in a sales project,\n * where the top are the initial stages with the most clients.\n *\n * It requires that the `highcharts-3d.js`, `cylinder.js` and\n * `funnel3d.js` module are loaded.\n *\n * @sample highcharts/demo/funnel3d/\n *         Funnel3d\n *\n * @extends      plotOptions.column\n * @excluding    allAreas, boostThreshold, colorAxis, compare, compareBase,\n *               dataSorting, boostBlending\n * @product      highcharts\n * @since        7.1.0\n * @requires     highcharts-3d\n * @requires     modules/cylinder\n * @requires     modules/funnel3d\n * @optionparent plotOptions.funnel3d\n */\nconst Funnel3DSeriesDefaults = {\n    /** @ignore-option */\n    center: ['50%', '50%'],\n    /**\n     * The max width of the series compared to the width of the plot area,\n     * or the pixel width if it is a number.\n     *\n     * @type    {number|string}\n     * @sample  {highcharts} highcharts/demo/funnel3d/ Funnel3d demo\n     * @product highcharts\n     */\n    width: '90%',\n    /**\n     * The width of the neck, the lower part of the funnel. A number defines\n     * pixel width, a percentage string defines a percentage of the plot\n     * area width.\n     *\n     * @type    {number|string}\n     * @sample  {highcharts} highcharts/demo/funnel3d/ Funnel3d demo\n     * @product highcharts\n     */\n    neckWidth: '30%',\n    /**\n     * The height of the series. If it is a number it defines\n     * the pixel height, if it is a percentage string it is the percentage\n     * of the plot area height.\n     *\n     * @type    {number|string}\n     * @sample  {highcharts} highcharts/demo/funnel3d/ Funnel3d demo\n     * @product highcharts\n     */\n    height: '100%',\n    /**\n     * The height of the neck, the lower part of the funnel. A number\n     * defines pixel width, a percentage string defines a percentage\n     * of the plot area height.\n     *\n     * @type    {number|string}\n     * @sample  {highcharts} highcharts/demo/funnel3d/ Funnel3d demo\n     * @product highcharts\n     */\n    neckHeight: '25%',\n    /**\n     * A reversed funnel has the widest area down. A reversed funnel with\n     * no neck width and neck height is a pyramid.\n     *\n     * @product highcharts\n     */\n    reversed: false,\n    /**\n     * By default sides fill is set to a gradient through this option being\n     * set to `true`. Set to `false` to get solid color for the sides.\n     *\n     * @product highcharts\n     */\n    gradientForSides: true,\n    animation: false,\n    edgeWidth: 0,\n    colorByPoint: true,\n    showInLegend: false,\n    dataLabels: {\n        align: 'right',\n        crop: false,\n        inside: false,\n        overflow: 'allow'\n    }\n};\n/**\n * A `funnel3d` series. If the [type](#series.funnel3d.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @sample {highcharts} highcharts/demo/funnel3d/\n *         Funnel3d demo\n *\n * @since     7.1.0\n * @extends   series,plotOptions.funnel3d\n * @excluding allAreas,boostThreshold,colorAxis,compare,compareBase\n * @product   highcharts\n * @requires  highcharts-3d\n * @requires  modules/cylinder\n * @requires  modules/funnel3d\n * @apioption series.funnel3d\n */\n/**\n * An array of data points for the series. For the `funnel3d` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of numerical values. In this case, the numerical values\n * will be interpreted as `y` options. The `x` values will be automatically\n * calculated, either starting at 0 and incremented by 1, or from `pointStart`\n * and `pointInterval` given in the series options. If the axis has\n * categories, these will be used. Example:\n *\n *  ```js\n *  data: [0, 5, 3, 5]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.funnel3d.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         y: 2,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         y: 4,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|Array<number>|*>}\n * @extends   series.column.data\n * @product   highcharts\n * @apioption series.funnel3d.data\n */\n/**\n * By default sides fill is set to a gradient through this option being\n * set to `true`. Set to `false` to get solid color for the sides.\n *\n * @type      {boolean}\n * @product   highcharts\n * @apioption series.funnel3d.data.gradientForSides\n */\n''; // Detachs doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Funnel3D_Funnel3DSeriesDefaults = (Funnel3DSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Funnel3D/Funnel3DPoint.js\n/* *\n *\n *  Highcharts funnel3d series module\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { seriesTypes: { column: ColumnSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { extend: Funnel3DPoint_extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass Funnel3DPoint extends ColumnSeries.prototype.pointClass {\n}\nFunnel3DPoint_extend(Funnel3DPoint.prototype, {\n    shapeType: 'funnel3d'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Funnel3D_Funnel3DPoint = (Funnel3DPoint);\n\n;// ./code/es-modules/Core/Math3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { pick } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable max-len */\n/**\n * Apply 3-D rotation\n * Euler Angles (XYZ):\n *     cosA = cos(Alfa|Roll)\n *     cosB = cos(Beta|Pitch)\n *     cosG = cos(Gamma|Yaw)\n *\n * Composite rotation:\n * |          cosB * cosG             |           cosB * sinG            |    -sinB    |\n * | sinA * sinB * cosG - cosA * sinG | sinA * sinB * sinG + cosA * cosG | sinA * cosB |\n * | cosA * sinB * cosG + sinA * sinG | cosA * sinB * sinG - sinA * cosG | cosA * cosB |\n *\n * Now, Gamma/Yaw is not used (angle=0), so we assume cosG = 1 and sinG = 0, so\n * we get:\n * |     cosB    |   0    |   - sinB    |\n * | sinA * sinB |  cosA  | sinA * cosB |\n * | cosA * sinB | - sinA | cosA * cosB |\n *\n * But in browsers, y is reversed, so we get sinA => -sinA. The general result\n * is:\n * |      cosB     |   0    |    - sinB     |     | x |     | px |\n * | - sinA * sinB |  cosA  | - sinA * cosB |  x  | y |  =  | py |\n * |  cosA * sinB  |  sinA  |  cosA * cosB  |     | z |     | pz |\n *\n * @private\n * @function rotate3D\n */\n/* eslint-enable max-len */\n/**\n * Rotates the position as defined in angles.\n * @private\n * @param {number} x\n *        X coordinate\n * @param {number} y\n *        Y coordinate\n * @param {number} z\n *        Z coordinate\n * @param {Highcharts.Rotation3DObject} angles\n *        Rotation angles\n * @return {Highcharts.Position3DObject}\n *         Rotated position\n */\nfunction rotate3D(x, y, z, angles) {\n    return {\n        x: angles.cosB * x - angles.sinB * z,\n        y: -angles.sinA * angles.sinB * x + angles.cosA * y -\n            angles.cosB * angles.sinA * z,\n        z: angles.cosA * angles.sinB * x + angles.sinA * y +\n            angles.cosA * angles.cosB * z\n    };\n}\n/**\n * Transforms a given array of points according to the angles in chart.options.\n *\n * @private\n * @function Highcharts.perspective\n *\n * @param {Array<Highcharts.Position3DObject>} points\n * The array of points\n *\n * @param {Highcharts.Chart} chart\n * The chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @param {boolean} [useInvertedPersp]\n * Whether to use inverted perspective in calculations\n *\n * @return {Array<Highcharts.Position3DObject>}\n * An array of transformed points\n *\n * @requires highcharts-3d\n */\nfunction perspective(points, chart, insidePlotArea, useInvertedPersp) {\n    const options3d = chart.options.chart.options3d, \n    /* The useInvertedPersp argument is used for inverted charts with\n     * already inverted elements, such as dataLabels or tooltip positions.\n     */\n    inverted = pick(useInvertedPersp, insidePlotArea ? chart.inverted : false), origin = {\n        x: chart.plotWidth / 2,\n        y: chart.plotHeight / 2,\n        z: options3d.depth / 2,\n        vd: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0)\n    }, scale = chart.scale3d || 1, beta = deg2rad * options3d.beta * (inverted ? -1 : 1), alpha = deg2rad * options3d.alpha * (inverted ? -1 : 1), angles = {\n        cosA: Math.cos(alpha),\n        cosB: Math.cos(-beta),\n        sinA: Math.sin(alpha),\n        sinB: Math.sin(-beta)\n    };\n    if (!insidePlotArea) {\n        origin.x += chart.plotLeft;\n        origin.y += chart.plotTop;\n    }\n    // Transform each point\n    return points.map(function (point) {\n        const rotated = rotate3D((inverted ? point.y : point.x) - origin.x, (inverted ? point.x : point.y) - origin.y, (point.z || 0) - origin.z, angles), \n        // Apply perspective\n        coordinate = perspective3D(rotated, origin, origin.vd);\n        // Apply translation\n        coordinate.x = coordinate.x * scale + origin.x;\n        coordinate.y = coordinate.y * scale + origin.y;\n        coordinate.z = rotated.z * scale + origin.z;\n        return {\n            x: (inverted ? coordinate.y : coordinate.x),\n            y: (inverted ? coordinate.x : coordinate.y),\n            z: coordinate.z\n        };\n    });\n}\n/**\n * Perspective3D function is available in global Highcharts scope because is\n * needed also outside of perspective() function (#8042).\n * @private\n * @function Highcharts.perspective3D\n *\n * @param {Highcharts.Position3DObject} coordinate\n * 3D position\n *\n * @param {Highcharts.Position3DObject} origin\n * 3D root position\n *\n * @param {number} distance\n * Perspective distance\n *\n * @return {Highcharts.PositionObject}\n * Perspective 3D Position\n *\n * @requires highcharts-3d\n */\nfunction perspective3D(coordinate, origin, distance) {\n    const projection = ((distance > 0) &&\n        (distance < Number.POSITIVE_INFINITY)) ?\n        distance / (coordinate.z + origin.z + distance) :\n        1;\n    return {\n        x: coordinate.x * projection,\n        y: coordinate.y * projection\n    };\n}\n/**\n * Calculate a distance from camera to points - made for calculating zIndex of\n * scatter points.\n *\n * @private\n * @function Highcharts.pointCameraDistance\n *\n * @param {Highcharts.Dictionary<number>} coordinates\n * Coordinates of the specific point\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @return {number}\n * Distance from camera to point\n *\n * @requires highcharts-3d\n */\nfunction pointCameraDistance(coordinates, chart) {\n    const options3d = chart.options.chart.options3d, cameraPosition = {\n        x: chart.plotWidth / 2,\n        y: chart.plotHeight / 2,\n        z: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0) +\n            options3d.depth\n    }, \n    // Added support for objects with plotX or x coordinates.\n    distance = Math.sqrt(Math.pow(cameraPosition.x - pick(coordinates.plotX, coordinates.x), 2) +\n        Math.pow(cameraPosition.y - pick(coordinates.plotY, coordinates.y), 2) +\n        Math.pow(cameraPosition.z - pick(coordinates.plotZ, coordinates.z), 2));\n    return distance;\n}\n/**\n * Calculate area of a 2D polygon using Shoelace algorithm\n * https://en.wikipedia.org/wiki/Shoelace_formula\n *\n * @private\n * @function Highcharts.shapeArea\n *\n * @param {Array<Highcharts.PositionObject>} vertexes\n * 2D Polygon\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea(vertexes) {\n    let area = 0, i, j;\n    for (i = 0; i < vertexes.length; i++) {\n        j = (i + 1) % vertexes.length;\n        area += vertexes[i].x * vertexes[j].y - vertexes[j].x * vertexes[i].y;\n    }\n    return area / 2;\n}\n/**\n * Calculate area of a 3D polygon after perspective projection\n *\n * @private\n * @function Highcharts.shapeArea3d\n *\n * @param {Array<Highcharts.Position3DObject>} vertexes\n * 3D Polygon\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea3D(vertexes, chart, insidePlotArea) {\n    return shapeArea(perspective(vertexes, chart, insidePlotArea));\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst Math3D = {\n    perspective,\n    perspective3D,\n    pointCameraDistance,\n    shapeArea,\n    shapeArea3D\n};\n/* harmony default export */ const Core_Math3D = (Math3D);\n\n;// ./code/es-modules/Series/Funnel3D/Funnel3DSeries.js\n/* *\n *\n *  Highcharts funnel3d series module\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { noop } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { perspective: Funnel3DSeries_perspective } = Core_Math3D;\n\nconst { series: Series, seriesTypes: { column: Funnel3DSeries_ColumnSeries } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { extend: Funnel3DSeries_extend, merge: Funnel3DSeries_merge, pick: Funnel3DSeries_pick, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The funnel3d series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.funnel3d\n * @augments seriesTypes.column\n * @requires highcharts-3d\n * @requires modules/cylinder\n * @requires modules/funnel3d\n */\nclass Funnel3DSeries extends Funnel3DSeries_ColumnSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    alignDataLabel(point, _dataLabel, options) {\n        const series = this, dlBoxRaw = point.dlBoxRaw, inverted = series.chart.inverted, below = point.plotY > Funnel3DSeries_pick(series.translatedThreshold, series.yAxis.len), inside = Funnel3DSeries_pick(options.inside, !!series.options.stacking), dlBox = {\n            x: dlBoxRaw.x,\n            y: dlBoxRaw.y,\n            height: 0\n        };\n        options.align = Funnel3DSeries_pick(options.align, !inverted || inside ? 'center' : below ? 'right' : 'left');\n        options.verticalAlign = Funnel3DSeries_pick(options.verticalAlign, inverted || inside ? 'middle' : below ? 'top' : 'bottom');\n        if (options.verticalAlign !== 'top') {\n            dlBox.y += dlBoxRaw.bottom /\n                (options.verticalAlign === 'bottom' ? 1 : 2);\n        }\n        dlBox.width = series.getWidthAt(dlBox.y);\n        if (series.options.reversed) {\n            dlBox.width = dlBoxRaw.fullWidth - dlBox.width;\n        }\n        if (inside) {\n            dlBox.x -= dlBox.width / 2;\n        }\n        else {\n            // Swap for inside\n            if (options.align === 'left') {\n                options.align = 'right';\n                dlBox.x -= dlBox.width * 1.5;\n            }\n            else if (options.align === 'right') {\n                options.align = 'left';\n                dlBox.x += dlBox.width / 2;\n            }\n            else {\n                dlBox.x -= dlBox.width / 2;\n            }\n        }\n        point.dlBox = dlBox;\n        Funnel3DSeries_ColumnSeries.prototype.alignDataLabel.apply(series, arguments);\n    }\n    /**\n     * Override default axis options with series required options for axes.\n     * @private\n     */\n    bindAxes() {\n        Series.prototype.bindAxes.apply(this, arguments);\n        Funnel3DSeries_extend(this.xAxis.options, {\n            gridLineWidth: 0,\n            lineWidth: 0,\n            title: void 0,\n            tickPositions: []\n        });\n        Funnel3DSeries_merge(true, this.yAxis.options, {\n            gridLineWidth: 0,\n            title: void 0,\n            labels: {\n                enabled: false\n            }\n        });\n    }\n    /**\n     * @private\n     */\n    translate() {\n        Series.prototype.translate.apply(this, arguments);\n        const series = this, chart = series.chart, options = series.options, reversed = options.reversed, ignoreHiddenPoint = options.ignoreHiddenPoint, plotWidth = chart.plotWidth, plotHeight = chart.plotHeight, center = options.center, centerX = relativeLength(center[0], plotWidth), centerY = relativeLength(center[1], plotHeight), width = relativeLength(options.width, plotWidth), height = relativeLength(options.height, plotHeight), neckWidth = relativeLength(options.neckWidth, plotWidth), neckHeight = relativeLength(options.neckHeight, plotHeight), neckY = (centerY - height / 2) + height - neckHeight, points = series.points;\n        let sum = 0, cumulative = 0, // Start at top\n        tempWidth, getWidthAt, fraction, tooltipPos, \n        //\n        y1, y3, y5, \n        //\n        h, shapeArgs; // @todo: Type it. It's an extended SVGAttributes.\n        // Return the width at a specific y coordinate\n        series.getWidthAt = getWidthAt = function (y) {\n            const top = (centerY - height / 2);\n            return (y > neckY || height === neckHeight) ?\n                neckWidth :\n                neckWidth + (width - neckWidth) *\n                    (1 - (y - top) / (height - neckHeight));\n        };\n        // Expose\n        series.center = [centerX, centerY, height];\n        series.centerX = centerX;\n        /*\n            * Individual point coordinate naming:\n            *\n            *  _________centerX,y1________\n            *  \\                         /\n            *   \\                       /\n            *    \\                     /\n            *     \\                   /\n            *      \\                 /\n            *        ___centerX,y3___\n            *\n            * Additional for the base of the neck:\n            *\n            *       |               |\n            *       |               |\n            *       |               |\n            *        ___centerX,y5___\n            */\n        // get the total sum\n        for (const point of points) {\n            if (!ignoreHiddenPoint || point.visible !== false) {\n                sum += point.y;\n            }\n        }\n        for (const point of points) {\n            // Set start and end positions\n            y5 = null;\n            fraction = sum ? point.y / sum : 0;\n            y1 = centerY - height / 2 + cumulative * height;\n            y3 = y1 + fraction * height;\n            tempWidth = getWidthAt(y1);\n            h = y3 - y1;\n            shapeArgs = {\n                // For fill setter\n                gradientForSides: Funnel3DSeries_pick(point.options.gradientForSides, options.gradientForSides),\n                x: centerX,\n                y: y1,\n                height: h,\n                width: tempWidth,\n                z: 1,\n                top: {\n                    width: tempWidth\n                }\n            };\n            tempWidth = getWidthAt(y3);\n            shapeArgs.bottom = {\n                fraction: fraction,\n                width: tempWidth\n            };\n            // The entire point is within the neck\n            if (y1 >= neckY) {\n                shapeArgs.isCylinder = true;\n            }\n            else if (y3 > neckY) {\n                // The base of the neck\n                y5 = y3;\n                tempWidth = getWidthAt(neckY);\n                y3 = neckY;\n                shapeArgs.bottom.width = tempWidth;\n                shapeArgs.middle = {\n                    fraction: h ? (neckY - y1) / h : 0,\n                    width: tempWidth\n                };\n            }\n            if (reversed) {\n                shapeArgs.y = y1 = centerY + height / 2 -\n                    (cumulative + fraction) * height;\n                if (shapeArgs.middle) {\n                    shapeArgs.middle.fraction = 1 -\n                        (h ? shapeArgs.middle.fraction : 0);\n                }\n                tempWidth = shapeArgs.width;\n                shapeArgs.width = shapeArgs.bottom.width;\n                shapeArgs.bottom.width = tempWidth;\n            }\n            point.shapeArgs = Funnel3DSeries_extend(point.shapeArgs, shapeArgs);\n            // For tooltips and data labels context\n            point.percentage = fraction * 100;\n            point.plotX = centerX;\n            if (reversed) {\n                point.plotY = centerY + height / 2 -\n                    (cumulative + fraction / 2) * height;\n            }\n            else {\n                point.plotY = (y1 + (y5 || y3)) / 2;\n            }\n            // Placement of tooltips and data labels in 3D\n            tooltipPos = Funnel3DSeries_perspective([{\n                    x: centerX,\n                    y: point.plotY,\n                    z: reversed ?\n                        -(width - getWidthAt(point.plotY)) / 2 :\n                        -(getWidthAt(point.plotY)) / 2\n                }], chart, true)[0];\n            point.tooltipPos = [tooltipPos.x, tooltipPos.y];\n            // Base to be used when alignment options are known\n            point.dlBoxRaw = {\n                x: centerX,\n                width: getWidthAt(point.plotY),\n                y: y1,\n                bottom: shapeArgs.height || 0,\n                fullWidth: width\n            };\n            if (!ignoreHiddenPoint || point.visible !== false) {\n                cumulative += fraction;\n            }\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nFunnel3DSeries.compose = Funnel3D_Funnel3DComposition.compose;\nFunnel3DSeries.defaultOptions = Funnel3DSeries_merge(Funnel3DSeries_ColumnSeries.defaultOptions, Funnel3D_Funnel3DSeriesDefaults);\nFunnel3DSeries_extend(Funnel3DSeries.prototype, {\n    pointClass: Funnel3D_Funnel3DPoint,\n    translate3dShapes: noop\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('funnel3d', Funnel3DSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Funnel3D_Funnel3DSeries = (Funnel3DSeries);\n\n;// ./code/es-modules/masters/modules/funnel3d.js\n\n\n\n\n\nFunnel3D_Funnel3DSeries.compose(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType());\n/* harmony default export */ const funnel3d_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__608__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "funnel3d_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "parse", "color", "charts", "Element3D", "SVGElement3D", "getRendererType", "merge", "Funnel3D_SVGElement3DFunnel", "constructor", "arguments", "mainParts", "parts", "sideGroups", "sideParts", "upperGroup", "lowerGroup", "pathType", "opacitySetter", "value", "opacity", "parseFloat", "funnel3d", "chart", "renderer", "chartIndex", "filterId", "index", "singleSetterForParts", "groupName", "tagName", "attributes", "id", "children", "type", "tableValues", "attr", "filter", "styledMode", "textContent", "addClass", "fillSetter", "fill", "fillColor", "alpha", "rgba", "partsWithColor", "top", "brighten", "bottom", "linearGradient", "radialGradient", "gradientForSides", "x1", "x2", "y1", "y2", "stops", "sideGroupName", "box", "gradientBox", "gradient", "alteredGradient", "x", "width", "y", "height", "partName", "frontUpper", "backUpper", "rightUpper", "frontLower", "backLower", "<PERSON><PERSON><PERSON><PERSON>", "gradBox", "centerX", "centerY", "diameter", "Math", "min", "setRadialReference", "part", "elem", "element", "grad", "gradients", "gradientUnits", "adjustForGradient", "bbox", "topLeftEdge", "Number", "MAX_VALUE", "bottomRightEdge", "getBBox", "max", "zIndexSetter", "finishedOnAdd", "Element", "apply", "onAdd", "Funnel3DComposition_charts", "error", "extend", "Funnel3DComposition_merge", "rendererFunnel3d", "shapeArgs", "element3d", "strokeAttrs", "stroke", "upperElem", "g", "zIndex", "add", "lowerElem", "rendererFunnel3dPath", "getCylinderEnd", "alphaCorrection", "abs", "options", "options3d", "cuboidData", "cuboidPath", "depth", "isTopFirst", "isTop", "isFrontFirst", "isFront", "<PERSON><PERSON><PERSON>", "middle", "z", "bottomWidth", "bottomArgs", "middleWidth", "middleTopArgs", "middleTop", "middleBottom", "useAlphaCorrection", "fraction", "ret", "getCylinderFront", "zIndexes", "group", "getCylinderBack", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesTypes", "column", "ColumnSeries", "Funnel3DPoint_extend", "Funnel3DPoint", "pointClass", "shapeType", "deg2rad", "pick", "perspective", "points", "insidePlotArea", "useInvertedPersp", "inverted", "origin", "plot<PERSON>id<PERSON>", "plotHeight", "vd", "viewDistance", "scale", "scale3d", "beta", "angles", "cosA", "cos", "cosB", "sinA", "sin", "sinB", "plotLeft", "plotTop", "map", "point", "rotated", "coordinate", "perspective3D", "distance", "projection", "POSITIVE_INFINITY", "shapeArea", "vertexes", "area", "i", "j", "length", "noop", "Funnel3DSeries_perspective", "pointCameraDistance", "coordinates", "cameraPosition", "sqrt", "pow", "plotX", "plotY", "plotZ", "shapeArea3D", "series", "Series", "Funnel3DSeries_ColumnSeries", "Funnel3DSeries_extend", "Funnel3DSeries_merge", "Funnel3DSeries_pick", "<PERSON><PERSON><PERSON><PERSON>", "Funnel3DSeries", "alignDataLabel", "_dataLabel", "dlBoxRaw", "below", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yAxis", "len", "inside", "stacking", "dlBox", "align", "verticalAlign", "getWidthAt", "reversed", "fullWidth", "bindAxes", "xAxis", "gridLineWidth", "lineWidth", "title", "tickPositions", "labels", "enabled", "translate", "ignoreHiddenPoint", "center", "neckWidth", "neckHeight", "neckY", "sum", "cumulative", "temp<PERSON>idth", "tooltipPos", "y3", "y5", "h", "visible", "is<PERSON><PERSON><PERSON>", "percentage", "compose", "SVGRendererClass", "rendererProto", "types", "funnel3dPath", "defaultOptions", "animation", "edgeWidth", "colorByPoint", "showInLegend", "dataLabels", "crop", "overflow", "translate3dShapes", "registerSeriesType", "Funnel3D_Funnel3DSeries"], "mappings": "CAaA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,cAAiB,EACnJ,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,KAAQ,CAACA,EAAK,gBAAmB,CAACA,EAAK,cAAiB,CAAE,GAC9J,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,gBAAmB,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAElLA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAC5J,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,IACzI,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAEzIE,EAA2I3B,EAAoB,KAC/J4B,EAA+J5B,EAAoBI,CAAC,CAACuB,GAiBzL,GAAM,CAAEE,MAAOC,CAAK,CAAE,CAAIJ,IAEpB,CAAEK,OAAAA,CAAM,CAAE,CAAIP,IAEd,CAAEQ,UAAWC,CAAY,CAAE,CAAGL,IAAkJM,eAAe,GAAGhB,SAAS,CAE3M,CAAEiB,MAAAA,CAAK,CAAE,CAAIX,IA4NgBY,EAtNnC,cAAiCH,EAC7BI,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,SAAS,CAAG,CAAC,MAAO,SAAS,CAClC,IAAI,CAACC,KAAK,CAAG,CACT,MAAO,SACP,aAAc,YACd,aAAc,YACd,aAAc,aACjB,CACD,IAAI,CAACC,UAAU,CAAG,CACd,aAAc,aACjB,CACD,IAAI,CAACC,SAAS,CAAG,CACbC,WAAY,CAAC,aAAc,YAAa,aAAa,CACrDC,WAAY,CAAC,aAAc,YAAa,aAAa,AACzD,EACA,IAAI,CAACC,QAAQ,CAAG,UACpB,CAOAC,cAAcC,CAAK,CAAE,CACjB,IAAuBC,EAAUC,WAAWF,GAAQP,EAAQU,AAA3C,IAAI,CAAgDV,KAAK,CAAEW,EAAQpB,CAAM,CAACmB,AAA1E,IAAI,CAA+EE,QAAQ,CAACC,UAAU,CAAC,CAAEC,EAAW,iBAAmBN,EAAU,IAAMG,EAAMI,KAAK,CAMnL,GAJAL,AAFiB,IAAI,CAEZV,KAAK,CAAGU,AAFA,IAAI,CAEKX,SAAS,CACnCW,AAHiB,IAAI,CAGZM,oBAAoB,CAAC,UAAWR,GAEzCE,AALiB,IAAI,CAKZV,KAAK,CAAGA,EACb,CAACW,EAAMC,QAAQ,CAACE,QAAQ,CAAE,CAiB1B,IAAK,IAAMG,KAhBXN,EAAMC,QAAQ,CAAC3C,UAAU,CAAC,CACtBiD,QAAS,SACTC,WAAY,CACRC,GAAIN,CACR,EACAO,SAAU,CAAC,CACHH,QAAS,sBACTG,SAAU,CAAC,CACHH,QAAS,UACTC,WAAY,CACRG,KAAM,QACNC,YAAa,KAAOf,CACxB,CACJ,EAAE,AACV,EAAE,AACV,GACwBE,AAvBX,IAAI,CAuBgBT,UAAU,EACvCS,AAxBS,IAAI,AAwBL,CAACO,EAAU,CAACO,IAAI,CAAC,CACrBC,OAAQ,QAAUX,EAAW,GACjC,GAGJ,GAAIJ,AA7BS,IAAI,CA6BJE,QAAQ,CAACc,UAAU,CAM5B,IAAK,IAAMT,KALXN,EAAMC,QAAQ,CAAC3C,UAAU,CAAC,CACtBiD,QAAS,QACTS,YAAa,eAAiBb,EAC1B,iBAAmBA,EAAW,IACtC,GACwBJ,AAnCf,IAAI,CAmCoBT,UAAU,EACvCS,AApCK,IAAI,AAoCD,CAACO,EAAU,CAACW,QAAQ,CAAC,cAAgBd,EAGzD,CACA,OAxCiB,IAAI,AAyCzB,CACAe,WAAWC,CAAI,CAAE,CACb,IAAIC,EAAYzC,EAAMwC,GAECE,EAAQD,EAAUE,IAAI,CAAC,EAAE,CAAEC,EAAiB,CAE/DC,IAAK7C,EAAMwC,GAAMM,QAAQ,CAAC,IAAK7D,GAAG,GAClC8D,OAAQ/C,EAAMwC,GAAMM,QAAQ,CAAC,KAAM7D,GAAG,EAC1C,EA2BA,GA1BIyD,EAAQ,GACRD,EAAUE,IAAI,CAAC,EAAE,CAAG,EACpBF,EAAYA,EAAUxD,GAAG,CAAC,OAE1BmC,AATa,IAAI,CASRc,IAAI,CAAC,CACVhB,QAASwB,CACb,IAIAD,EAAYD,EAGXC,EAAUO,cAAc,EACxBP,EAAUQ,cAAc,GACzB7B,AApBa,IAAI,CAoBR8B,gBAAgB,EACzBT,CAAAA,EAAY,CACRO,eAAgB,CAAEG,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,CAAE,EAC7CC,MAAO,CACH,CAAC,EAAGvD,EAAMwC,GAAMM,QAAQ,CAAC,KAAM7D,GAAG,GAAG,CACrC,CAAC,GAAKuD,EAAK,CACX,CAAC,EAAGxC,EAAMwC,GAAMM,QAAQ,CAAC,KAAM7D,GAAG,GAAG,CACxC,AACL,CAAA,EAGAwD,EAAUO,cAAc,CAExB,IAAK,IAAMQ,KAAiBpC,AAjCf,IAAI,CAiCoBT,UAAU,CAAE,CAC7C,IAAM8C,EAAMrC,AAlCH,IAAI,AAkCO,CAACoC,EAAc,CAACE,WAAW,CAAEC,EAAWlB,EAAUO,cAAc,CAAEY,EAAkBvD,EAAMoC,EAAW,CACrHO,eAAgB,CACZG,GAAIM,EAAII,CAAC,CAAGF,EAASR,EAAE,CAAGM,EAAIK,KAAK,CACnCT,GAAII,EAAIM,CAAC,CAAGJ,EAASN,EAAE,CAAGI,EAAIO,MAAM,CACpCZ,GAAIK,EAAII,CAAC,CAAGF,EAASP,EAAE,CAAGK,EAAIK,KAAK,CACnCR,GAAIG,EAAIM,CAAC,CAAGJ,EAASL,EAAE,CAAGG,EAAIO,MAAM,AACxC,CACJ,GACA,IAAK,IAAMC,KAAY7C,AA1Cd,IAAI,CA0CmBR,SAAS,CAAC4C,EAAc,CACpDZ,CAAc,CAACqB,EAAS,CAAGL,CAEnC,MAWA,GARAvD,EAAM,CAAA,EAAMuC,EAAgB,CACxBsB,WAAYzB,EACZ0B,UAAW1B,EACX2B,WAAY3B,EACZ4B,WAAY5B,EACZ6B,UAAW7B,EACX8B,WAAY9B,CAChB,GACIA,EAAUQ,cAAc,CACxB,IAAK,IAAMO,KAAiBpC,AAzDnB,IAAI,CAyDwBT,UAAU,CAAE,CAC7C,IAAM6D,EAAUpD,AA1DX,IAAI,AA0De,CAACoC,EAAc,CAACE,WAAW,CAAEe,EAAUD,EAAQX,CAAC,CAAGW,EAAQV,KAAK,CAAG,EAAGY,EAAUF,EAAQT,CAAC,CAAGS,EAAQR,MAAM,CAAG,EAAGW,EAAWC,KAAKC,GAAG,CAACL,EAAQV,KAAK,CAAEU,EAAQR,MAAM,EACzL,IAAK,IAAMC,KAAY7C,AA3DlB,IAAI,CA2DuBR,SAAS,CAAC4C,EAAc,CACpDpC,AA5DC,IAAI,AA4DG,CAAC6C,EAAS,CAACa,kBAAkB,CAAC,CAClCL,EAASC,EAASC,EACrB,CAET,CAOR,GAJAvD,AAnEiB,IAAI,CAmEZM,oBAAoB,CAAC,OAAQ,KAAMkB,GAE5CxB,AArEiB,IAAI,CAqEZpB,KAAK,CAAGoB,AArEA,IAAI,CAqEKoB,IAAI,CAAGA,EAE7BC,EAAUO,cAAc,CACxB,IAAK,IAAM+B,IAAQ,CAAC3D,AAxEP,IAAI,CAwEYiD,UAAU,CAAEjD,AAxE5B,IAAI,CAwEiC8C,UAAU,CAAC,CAAE,CAC3D,IAAMc,EAAOD,EAAKE,OAAO,CAAEC,EAAQF,GAC/B5D,AA1EK,IAAI,CA0EAE,QAAQ,CAAC6D,SAAS,CAACH,EAAKrB,QAAQ,CAAC,CAC1CuB,GACAA,AAA+B,mBAA/BA,EAAKhD,IAAI,CAAC,kBACVgD,EAAKhD,IAAI,CAAC,CACNkD,cAAe,gBACnB,EAER,CAEJ,OAnFiB,IAAI,AAoFzB,CACAC,mBAAoB,KAEZC,EACJ,IAAK,IAAM9B,KAAiBpC,AAFX,IAAI,CAEgBT,UAAU,CAAE,CAE7C,IAAI4E,EAAc,CACd1B,EAAG2B,OAAOC,SAAS,CACnB1B,EAAGyB,OAAOC,SAAS,AACvB,EAAGC,EAAkB,CACjB7B,EAAG,CAAC2B,OAAOC,SAAS,CACpB1B,EAAG,CAACyB,OAAOC,SAAS,AACxB,EAEA,IAAK,IAAMxB,KAAY7C,AAZV,IAAI,CAYeR,SAAS,CAAC4C,EAAc,CAEpD8B,EAAOP,AADM3D,AAbJ,IAAI,AAaQ,CAAC6C,EAAS,CACnB0B,OAAO,CAAC,CAAA,GACpBJ,EAAc,CACV1B,EAAGe,KAAKC,GAAG,CAACU,EAAY1B,CAAC,CAAEyB,EAAKzB,CAAC,EACjCE,EAAGa,KAAKC,GAAG,CAACU,EAAYxB,CAAC,CAAEuB,EAAKvB,CAAC,CACrC,EACA2B,EAAkB,CACd7B,EAAGe,KAAKgB,GAAG,CAACF,EAAgB7B,CAAC,CAAEyB,EAAKzB,CAAC,CAAGyB,EAAKxB,KAAK,EAClDC,EAAGa,KAAKgB,GAAG,CAACF,EAAgB3B,CAAC,CAAEuB,EAAKvB,CAAC,CAAGuB,EAAKtB,MAAM,CACvD,CAGJ5C,CAzBa,IAAI,AAyBT,CAACoC,EAAc,CAACE,WAAW,CAAG,CAClCG,EAAG0B,EAAY1B,CAAC,CAChBC,MAAO4B,EAAgB7B,CAAC,CAAG0B,EAAY1B,CAAC,CACxCE,EAAGwB,EAAYxB,CAAC,CAChBC,OAAQ0B,EAAgB3B,CAAC,CAAGwB,EAAYxB,CAAC,AAC7C,CACJ,CACJ,CACA8B,cAAe,CAOX,OAJI,IAAI,CAACC,aAAa,EAClB,IAAI,CAACT,iBAAiB,GAGnB,IAAI,CAAC/D,QAAQ,CAACyE,OAAO,CAAC3G,SAAS,CAACyG,YAAY,CAACG,KAAK,CAAC,IAAI,CAAExF,UACpE,CACAyF,OAAQ,CACJ,IAAI,CAACZ,iBAAiB,GACtB,IAAI,CAACS,aAAa,CAAG,CAAA,CACzB,CACJ,EAyBM,CAAE7F,OAAQiG,CAA0B,CAAE,CAAIxG,IAE1C,CAAEyG,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE/F,MAAOgG,CAAyB,CAAE,CAAI3G,IAkB7D,SAAS4G,EAAiBC,CAAS,EAC/B,IAAuBnF,EAAWE,AAAjB,IAAI,CAAsBkF,SAAS,CAAC,WAAYD,GAAYnE,EAAad,AAAzE,IAAI,CAA8Ec,UAAU,CAE7GqE,EAAc,CACV,eAAgB,EAChBC,OAAQ,MACZ,EAKA,IAAK,IAAMC,KAHXvF,EAASP,UAAU,CAAGS,AAPL,IAAI,CAOUsF,CAAC,CAAC,wBAAwB1E,IAAI,CAAC,CAC1D2E,OAAQzF,EAAS8C,UAAU,CAAC2C,MAAM,AACtC,GAAGC,GAAG,CAAC1F,GACiB,CAACA,EAAS8C,UAAU,CAAE9C,EAAS+C,SAAS,CAAE/C,EAASgD,UAAU,CAAC,EAC7EhC,GACDuE,EAAUzE,IAAI,CAACuE,GAEnBE,EAAUG,GAAG,CAAC1F,EAASP,UAAU,EAKrC,IAAK,IAAMkG,KAHX3F,EAASN,UAAU,CAAGQ,AAhBL,IAAI,CAgBUsF,CAAC,CAAC,wBAAwB1E,IAAI,CAAC,CAC1D2E,OAAQzF,EAASiD,UAAU,CAACwC,MAAM,AACtC,GAAGC,GAAG,CAAC1F,GACiB,CAACA,EAASiD,UAAU,CAAEjD,EAASkD,SAAS,CAAElD,EAASmD,UAAU,CAAC,EAC7EnC,GACD2E,EAAU7E,IAAI,CAACuE,GAEnBM,EAAUD,GAAG,CAAC1F,EAASN,UAAU,EAGrC,OADAM,EAAS8B,gBAAgB,CAAGqD,EAAUrD,gBAAgB,CAC/C9B,CACX,CAKA,SAAS4F,EAAqBT,CAAS,EAG9B,IAAI,CAACU,cAAc,EACpBd,EAAM,uDAAwD,CAAA,EAAMD,CAA0B,CAAC,IAAI,CAAC3E,UAAU,CAAC,EAEnH,IAAuBF,EAAQ6E,CAA0B,CAAC5E,AAAzC,IAAI,CAA8CC,UAAU,CAAC,CAG9E2F,EAAkBX,EAAUW,eAAe,CAAG,GAAKtC,KAAKuC,GAAG,CAAC,AAAC9F,EAAM+F,OAAO,CAAC/F,KAAK,CAACgG,SAAS,CAAC3E,KAAK,CAAG,IAC/F,IAGJ4E,EAAa,IAAI,CAACC,UAAU,CAACjI,IAAI,CAPhB,IAAI,CAOuB+G,EAA0BE,EAAW,CAC7EiB,MAAOjB,EAAUzC,KAAK,CACtBA,MAAO,AAACyC,CAAAA,EAAUzC,KAAK,CAAGyC,EAAUxD,MAAM,CAACe,KAAK,AAAD,EAAK,CACxD,IAAK2D,EAAaH,EAAWI,KAAK,CAAEC,EAAe,CAACL,EAAWM,OAAO,CAAEC,EAAY,CAAC,CAACtB,EAAUuB,MAAM,CAEtGjF,EAAMvB,AAZW,IAAI,CAYN2F,cAAc,CAAC5F,EAAOgF,EAA0BE,EAAW,CACtE1C,EAAG0C,EAAU1C,CAAC,CAAG0C,EAAUzC,KAAK,CAAG,EACnCiE,EAAGxB,EAAUwB,CAAC,CAAGxB,EAAUzC,KAAK,CAAG,EACnCoD,gBAAiBA,CACrB,IAAKc,EAAczB,EAAUxD,MAAM,CAACe,KAAK,CAAEmE,EAAa5B,EAA0BE,EAAW,CACzFzC,MAAOkE,EACPnE,EAAG0C,EAAU1C,CAAC,CAAGmE,EAAc,EAC/BD,EAAGxB,EAAUwB,CAAC,CAAGC,EAAc,EAC/Bd,gBAAiBA,CACrB,GAAInE,EAASzB,AArBI,IAAI,CAqBC2F,cAAc,CAAC5F,EAAO4G,EAAY,CAAA,GACpDC,EAAcF,EAAaG,EAAgBF,EAAYG,EAAYrF,EAAQsF,EAAetF,EAE9FuF,EACIT,IACAK,EAAc3B,EAAUuB,MAAM,CAAChE,KAAK,CACpCqE,EAAgB9B,EAA0BE,EAAW,CACjDxC,EAAIwC,EAAUxC,CAAC,CACXwC,EAAUuB,MAAM,CAACS,QAAQ,CAAGhC,EAAUvC,MAAM,CAChDF,MAAOoE,EACPrE,EAAG0C,EAAU1C,CAAC,CAAGqE,EAAc,EAC/BH,EAAGxB,EAAUwB,CAAC,CAAGG,EAAc,CACnC,GACAE,EAAY9G,AAlCC,IAAI,CAkCI2F,cAAc,CAAC5F,EAAO8G,EAAe,CAAA,GAC1DE,EAAe/G,AAnCF,IAAI,CAmCO2F,cAAc,CAAC5F,EAAO8G,EAAe,CAAA,IAEjE,IAAMK,EAAM,CACR3F,IAAKA,EACLE,OAAQA,EACRmB,WAAY5C,AAxCC,IAAI,CAwCImH,gBAAgB,CAAC5F,EAAKuF,GAC3CM,SAAU,CACNC,MAAOrB,EAAWoB,QAAQ,CAACC,KAAK,CAChC9F,IAAK4E,AAAuB,EAAvBA,CAAAA,AAAe,IAAfA,CAAe,EACpB1E,OAAQ0E,AAAuB,EAAvBA,CAAAA,AAAe,IAAfA,CAAe,EACvBvD,WAAYyD,EAAe,EAAI,EAC/BxD,UAAWwD,EAAe,EAAI,EAC9BvD,WAAYuD,EAAe,EAAI,CACnC,CACJ,EAiCA,OAhCAa,EAAIrE,SAAS,CAAG7C,AAlDC,IAAI,CAkDIsH,eAAe,CAAC/F,EAAKuF,GAC9CE,EAAqB,AAAC1D,KAAKC,GAAG,CAACqD,EAAa3B,EAAUzC,KAAK,EACvDc,KAAKgB,GAAG,CAACsC,EAAa3B,EAAUzC,KAAK,GAAO,EAChD0E,EAAIpE,UAAU,CAAG9C,AArDA,IAAI,CAqDKmH,gBAAgB,CAACnH,AArD1B,IAAI,CAqD+B2F,cAAc,CAAC5F,EAAOgF,EAA0BE,EAAW,CAC3G1C,EAAG0C,EAAU1C,CAAC,CAAG0C,EAAUzC,KAAK,CAAG,EACnCiE,EAAGxB,EAAUwB,CAAC,CAAGxB,EAAUzC,KAAK,CAAG,EACnCoD,gBAAiBoB,EACb,CAACpB,EAAkB,CAC3B,GAAI,CAAA,GAAQ5F,AA1DK,IAAI,CA0DA2F,cAAc,CAAC5F,EAAOgF,EAA0B8B,EAAe,CAChFjB,gBAAiBoB,EACb,CAACpB,EAAkB,CAC3B,GAAI,CAACW,IACDA,IACAS,EAAqB,AAAC1D,KAAKC,GAAG,CAACqD,EAAaF,GACxCpD,KAAKgB,GAAG,CAACsC,EAAaF,IAAkB,EAC5C3B,EAA0B,CAAA,EAAMmC,EAAK,CACjCnE,WAAY/C,AAlEH,IAAI,CAkEQmH,gBAAgB,CAACJ,EAActF,GACpDuB,UAAWhD,AAnEF,IAAI,CAmEOsH,eAAe,CAACP,EAActF,GAClDwB,WAAYjD,AApEH,IAAI,CAoEQmH,gBAAgB,CAACnH,AApE7B,IAAI,CAoEkC2F,cAAc,CAAC5F,EAAOgF,EAA0B4B,EAAY,CACvGf,gBAAiBoB,EACb,CAACpB,EAAkB,CAC3B,GAAI,CAAA,GAAO5F,AAvEF,IAAI,CAuEO2F,cAAc,CAAC5F,EAAOgF,EAA0B8B,EAAe,CAC/EjB,gBAAiBoB,EACb,CAACpB,EAAkB,CAC3B,GAAI,CAAA,IACJwB,SAAU,CACNrE,WAAYsD,EAAe,EAAI,EAC/BrD,UAAWqD,EAAe,EAAI,EAC9BpD,WAAYoD,EAAe,EAAI,CACnC,CACJ,IAEGa,CACX,CA8LA,IAAIK,EAAmI3K,EAAoB,KACvJ4K,EAAuJ5K,EAAoBI,CAAC,CAACuK,GAiBjL,GAAM,CAAEE,YAAa,CAAEC,OAAQC,CAAY,CAAE,CAAE,CAAIH,IAE7C,CAAE1C,OAAQ8C,CAAoB,CAAE,CAAIxJ,GAM1C,OAAMyJ,UAAsBF,EAAa7J,SAAS,CAACgK,UAAU,CAC7D,CACAF,EAAqBC,EAAc/J,SAAS,CAAE,CAC1CiK,UAAW,UACf,GAoBA,GAAM,CAAEC,QAAAA,CAAO,CAAE,CAAI5J,IAEf,CAAE6J,KAAAA,CAAI,CAAE,CAAI7J,IAiFlB,SAAS8J,EAAYC,CAAM,CAAEpI,CAAK,CAAEqI,CAAc,CAAEC,CAAgB,EAChE,IAAMtC,EAAYhG,EAAM+F,OAAO,CAAC/F,KAAK,CAACgG,SAAS,CAI/CuC,EAAWL,EAAKI,EAAkBD,EAAAA,GAAiBrI,EAAMuI,QAAQ,EAAWC,EAAS,CACjFhG,EAAGxC,EAAMyI,SAAS,CAAG,EACrB/F,EAAG1C,EAAM0I,UAAU,CAAG,EACtBhC,EAAGV,EAAUG,KAAK,CAAG,EACrBwC,GAAIT,EAAKlC,EAAUG,KAAK,CAAE,GAAK+B,EAAKlC,EAAU4C,YAAY,CAAE,EAChE,EAAGC,EAAQ7I,EAAM8I,OAAO,EAAI,EAAGC,EAAOd,EAAUjC,EAAU+C,IAAI,CAAIR,CAAAA,EAAW,GAAK,CAAA,EAAIlH,EAAQ4G,EAAUjC,EAAU3E,KAAK,CAAIkH,CAAAA,EAAW,GAAK,CAAA,EAAIS,EAAS,CACpJC,KAAM1F,KAAK2F,GAAG,CAAC7H,GACf8H,KAAM5F,KAAK2F,GAAG,CAAC,CAACH,GAChBK,KAAM7F,KAAK8F,GAAG,CAAChI,GACfiI,KAAM/F,KAAK8F,GAAG,CAAC,CAACN,EACpB,EAMA,OALKV,IACDG,EAAOhG,CAAC,EAAIxC,EAAMuJ,QAAQ,CAC1Bf,EAAO9F,CAAC,EAAI1C,EAAMwJ,OAAO,EAGtBpB,EAAOqB,GAAG,CAAC,SAAUC,CAAK,MArDnBlH,EAAGE,EAAGgE,EAsDhB,IAAMiD,GAtDInH,EAsDe,AAAC+F,CAAAA,EAAWmB,EAAMhH,CAAC,CAAGgH,EAAMlH,CAAC,AAADA,EAAKgG,EAAOhG,CAAC,CAtDrDE,EAsDuD,AAAC6F,CAAAA,EAAWmB,EAAMlH,CAAC,CAAGkH,EAAMhH,CAAC,AAADA,EAAK8F,EAAO9F,CAAC,CAtD7FgE,EAsD+F,AAACgD,CAAAA,EAAMhD,CAAC,EAAI,CAAA,EAAK8B,EAAO9B,CAAC,CArDrI,CACHlE,EAAGwG,AAoDuIA,EApDhIG,IAAI,CAAG3G,EAAIwG,AAoDqHA,EApD9GM,IAAI,CAAG5C,EACnChE,EAAG,CAACsG,AAmDsIA,EAnD/HI,IAAI,CAAGJ,AAmDwHA,EAnDjHM,IAAI,CAAG9G,EAAIwG,AAmDsGA,EAnD/FC,IAAI,CAAGvG,EAC9CsG,AAkDsIA,EAlD/HG,IAAI,CAAGH,AAkDwHA,EAlDjHI,IAAI,CAAG1C,EAChCA,EAAGsC,AAiDuIA,EAjDhIC,IAAI,CAAGD,AAiDyHA,EAjDlHM,IAAI,CAAG9G,EAAIwG,AAiDuGA,EAjDhGI,IAAI,CAAG1G,EAC7CsG,AAgDsIA,EAhD/HC,IAAI,CAAGD,AAgDwHA,EAhDjHG,IAAI,CAAGzC,CACpC,GAiDIkD,EAAaC,EAAcF,EAASnB,EAAQA,EAAOG,EAAE,EAKrD,OAHAiB,EAAWpH,CAAC,CAAGoH,EAAWpH,CAAC,CAAGqG,EAAQL,EAAOhG,CAAC,CAC9CoH,EAAWlH,CAAC,CAAGkH,EAAWlH,CAAC,CAAGmG,EAAQL,EAAO9F,CAAC,CAC9CkH,EAAWlD,CAAC,CAAGiD,EAAQjD,CAAC,CAAGmC,EAAQL,EAAO9B,CAAC,CACpC,CACHlE,EAAI+F,EAAWqB,EAAWlH,CAAC,CAAGkH,EAAWpH,CAAC,CAC1CE,EAAI6F,EAAWqB,EAAWpH,CAAC,CAAGoH,EAAWlH,CAAC,CAC1CgE,EAAGkD,EAAWlD,CAAC,AACnB,CACJ,EACJ,CAqBA,SAASmD,EAAcD,CAAU,CAAEpB,CAAM,CAAEsB,CAAQ,EAC/C,IAAMC,EAAa,AAAC,AAACD,EAAW,GAC3BA,EAAW3F,OAAO6F,iBAAiB,CACpCF,EAAYF,CAAAA,EAAWlD,CAAC,CAAG8B,EAAO9B,CAAC,CAAGoD,CAAO,EAC7C,EACJ,MAAO,CACHtH,EAAGoH,EAAWpH,CAAC,CAAGuH,EAClBrH,EAAGkH,EAAWlH,CAAC,CAAGqH,CACtB,CACJ,CA+CA,SAASE,EAAUC,CAAQ,EACvB,IAAIC,EAAO,EAAGC,EAAGC,EACjB,IAAKD,EAAI,EAAGA,EAAIF,EAASI,MAAM,CAAEF,IAC7BC,EAAI,AAACD,CAAAA,EAAI,CAAA,EAAKF,EAASI,MAAM,CAC7BH,GAAQD,CAAQ,CAACE,EAAE,CAAC5H,CAAC,CAAG0H,CAAQ,CAACG,EAAE,CAAC3H,CAAC,CAAGwH,CAAQ,CAACG,EAAE,CAAC7H,CAAC,CAAG0H,CAAQ,CAACE,EAAE,CAAC1H,CAAC,CAEzE,OAAOyH,EAAO,CAClB,CAyDA,GAAM,CAAEI,KAAAA,CAAI,CAAE,CAAIlM,IAEZ,CAAE8J,YAAaqC,CAA0B,CAAE,CA9BlC,CACXrC,YAAAA,EACA0B,cAAAA,EACAY,oBAnEJ,SAA6BC,CAAW,CAAE1K,CAAK,EAC3C,IAAMgG,EAAYhG,EAAM+F,OAAO,CAAC/F,KAAK,CAACgG,SAAS,CAAE2E,EAAiB,CAC9DnI,EAAGxC,EAAMyI,SAAS,CAAG,EACrB/F,EAAG1C,EAAM0I,UAAU,CAAG,EACtBhC,EAAGwB,EAAKlC,EAAUG,KAAK,CAAE,GAAK+B,EAAKlC,EAAU4C,YAAY,CAAE,GACvD5C,EAAUG,KAAK,AACvB,EAKA,OAHW5C,KAAKqH,IAAI,CAACrH,KAAKsH,GAAG,CAACF,EAAenI,CAAC,CAAG0F,EAAKwC,EAAYI,KAAK,CAAEJ,EAAYlI,CAAC,EAAG,GACrFe,KAAKsH,GAAG,CAACF,EAAejI,CAAC,CAAGwF,EAAKwC,EAAYK,KAAK,CAAEL,EAAYhI,CAAC,EAAG,GACpEa,KAAKsH,GAAG,CAACF,EAAejE,CAAC,CAAGwB,EAAKwC,EAAYM,KAAK,CAAEN,EAAYhE,CAAC,EAAG,GAE5E,EAwDIuD,UAAAA,EACAgB,YAbJ,SAAqBf,CAAQ,CAAElK,CAAK,CAAEqI,CAAc,EAChD,OAAO4B,EAAU9B,EAAY+B,EAAUlK,EAAOqI,GAClD,CAYA,EA0BM,CAAE6C,OAAQC,CAAM,CAAEzD,YAAa,CAAEC,OAAQyD,CAA2B,CAAE,CAAE,CAAI3D,IAE5E,CAAE1C,OAAQsG,CAAqB,CAAErM,MAAOsM,CAAoB,CAAEpD,KAAMqD,CAAmB,CAAEC,eAAAA,CAAc,CAAE,CAAInN,GAiBnH,OAAMoN,UAAuBL,EASzBM,eAAehC,CAAK,CAAEiC,CAAU,CAAE5F,CAAO,CAAE,CACvC,IAAqB6F,EAAWlC,EAAMkC,QAAQ,CAAErD,EAAW2C,AAA5C,IAAI,CAA+ClL,KAAK,CAACuI,QAAQ,CAAEsD,EAAQnC,EAAMqB,KAAK,CAAGQ,EAAoBL,AAA7G,IAAI,CAAgHY,mBAAmB,CAAEZ,AAAzI,IAAI,CAA4Ia,KAAK,CAACC,GAAG,EAAGC,EAASV,EAAoBxF,EAAQkG,MAAM,CAAE,CAAC,CAACf,AAA3M,IAAI,CAA8MnF,OAAO,CAACmG,QAAQ,EAAGC,EAAQ,CACxP3J,EAAGoJ,EAASpJ,CAAC,CACbE,EAAGkJ,EAASlJ,CAAC,CACbC,OAAQ,CACZ,CACAoD,CAAAA,EAAQqG,KAAK,CAAGb,EAAoBxF,EAAQqG,KAAK,CAAE,CAAC7D,GAAY0D,EAAS,SAAWJ,EAAQ,QAAU,QACtG9F,EAAQsG,aAAa,CAAGd,EAAoBxF,EAAQsG,aAAa,CAAE9D,GAAY0D,EAAS,SAAWJ,EAAQ,MAAQ,UACrF,QAA1B9F,EAAQsG,aAAa,EACrBF,CAAAA,EAAMzJ,CAAC,EAAIkJ,EAASlK,MAAM,CACrBqE,CAAAA,AAA0B,WAA1BA,EAAQsG,aAAa,CAAgB,EAAI,CAAA,CAAC,EAEnDF,EAAM1J,KAAK,CAAGyI,AAXC,IAAI,CAWEoB,UAAU,CAACH,EAAMzJ,CAAC,EACnCwI,AAZW,IAAI,CAYRnF,OAAO,CAACwG,QAAQ,EACvBJ,CAAAA,EAAM1J,KAAK,CAAGmJ,EAASY,SAAS,CAAGL,EAAM1J,KAAK,AAAD,EAE7CwJ,EACAE,EAAM3J,CAAC,EAAI2J,EAAM1J,KAAK,CAAG,EAIrBsD,AAAkB,SAAlBA,EAAQqG,KAAK,EACbrG,EAAQqG,KAAK,CAAG,QAChBD,EAAM3J,CAAC,EAAI2J,AAAc,IAAdA,EAAM1J,KAAK,EAEjBsD,AAAkB,UAAlBA,EAAQqG,KAAK,EAClBrG,EAAQqG,KAAK,CAAG,OAChBD,EAAM3J,CAAC,EAAI2J,EAAM1J,KAAK,CAAG,GAGzB0J,EAAM3J,CAAC,EAAI2J,EAAM1J,KAAK,CAAG,EAGjCiH,EAAMyC,KAAK,CAAGA,EACdf,EAA4BrN,SAAS,CAAC2N,cAAc,CAAC/G,KAAK,CAjC3C,IAAI,CAiCgDxF,UACvE,CAKAsN,UAAW,CACPtB,EAAOpN,SAAS,CAAC0O,QAAQ,CAAC9H,KAAK,CAAC,IAAI,CAAExF,WACtCkM,EAAsB,IAAI,CAACqB,KAAK,CAAC3G,OAAO,CAAE,CACtC4G,cAAe,EACfC,UAAW,EACXC,MAAO,KAAK,EACZC,cAAe,EAAE,AACrB,GACAxB,EAAqB,CAAA,EAAM,IAAI,CAACS,KAAK,CAAChG,OAAO,CAAE,CAC3C4G,cAAe,EACfE,MAAO,KAAK,EACZE,OAAQ,CACJC,QAAS,CAAA,CACb,CACJ,EACJ,CAIAC,WAAY,CACR9B,EAAOpN,SAAS,CAACkP,SAAS,CAACtI,KAAK,CAAC,IAAI,CAAExF,WACvC,IAAqBa,EAAQkL,AAAd,IAAI,CAAiBlL,KAAK,CAAE+F,EAAUmF,AAAtC,IAAI,CAAyCnF,OAAO,CAAEwG,EAAWxG,EAAQwG,QAAQ,CAAEW,EAAoBnH,EAAQmH,iBAAiB,CAAEzE,EAAYzI,EAAMyI,SAAS,CAAEC,EAAa1I,EAAM0I,UAAU,CAAEyE,EAASpH,EAAQoH,MAAM,CAAE/J,EAAUoI,EAAe2B,CAAM,CAAC,EAAE,CAAE1E,GAAYpF,EAAUmI,EAAe2B,CAAM,CAAC,EAAE,CAAEzE,GAAajG,EAAQ+I,EAAezF,EAAQtD,KAAK,CAAEgG,GAAY9F,EAAS6I,EAAezF,EAAQpD,MAAM,CAAE+F,GAAa0E,EAAY5B,EAAezF,EAAQqH,SAAS,CAAE3E,GAAY4E,EAAa7B,EAAezF,EAAQsH,UAAU,CAAE3E,GAAa4E,EAAQ,AAACjK,EAAUV,EAAS,EAAKA,EAAS0K,EAAYjF,EAAS8C,AAArlB,IAAI,CAAwlB9C,MAAM,CAC7mBmF,EAAM,EAAGC,EAAa,EAC1BC,EAAWnB,EAAYpF,EAAUwG,EAEjC1L,EAAI2L,EAAIC,EAERC,EAAG3I,EA+BH,IAAK,IAAMwE,KA7BXwB,AARe,IAAI,CAQZoB,UAAU,CAAGA,EAAa,SAAU5J,CAAC,EAExC,OAAO,AAACA,EAAI4K,GAAS3K,IAAW0K,EAC5BD,EACAA,EAAY,AAAC3K,CAAAA,EAAQ2K,CAAQ,EACxB,CAAA,EAAI,AAAC1K,CAAAA,EAJDW,CAAAA,EAAUV,EAAS,CAAA,CAIZ,EAAMA,CAAAA,EAAS0K,CAAS,CAAC,CACjD,EAEAnC,AAhBe,IAAI,CAgBZiC,MAAM,CAAG,CAAC/J,EAASC,EAASV,EAAO,CAC1CuI,AAjBe,IAAI,CAiBZ9H,OAAO,CAAGA,EAoBGgF,GACX8E,GAAqBxD,AAAkB,CAAA,IAAlBA,EAAMoE,OAAO,EACnCP,CAAAA,GAAO7D,EAAMhH,CAAC,AAADA,EAGrB,IAAK,IAAMgH,KAAStB,EAEhBwF,EAAK,KACL1G,EAAWqG,EAAM7D,EAAMhH,CAAC,CAAG6K,EAAM,EAEjCI,EAAK3L,AADLA,CAAAA,EAAKqB,EAAUV,EAAS,EAAI6K,EAAa7K,CAAK,EACpCuE,EAAWvE,EACrB8K,EAAYnB,EAAWtK,GACvB6L,EAAIF,EAAK3L,EAcTkD,AAbAA,CAAAA,EAAY,CAERrD,iBAAkB0J,EAAoB7B,EAAM3D,OAAO,CAAClE,gBAAgB,CAAEkE,EAAQlE,gBAAgB,EAC9FW,EAAGY,EACHV,EAAGV,EACHW,OAAQkL,EACRpL,MAAOgL,EACP/G,EAAG,EACHlF,IAAK,CACDiB,MAAOgL,CACX,CACJ,CAAA,EAEU/L,MAAM,CAAG,CACfwF,SAAUA,EACVzE,MAHJgL,EAAYnB,EAAWqB,EAIvB,EAEI3L,GAAMsL,EACNpI,EAAU6I,UAAU,CAAG,CAAA,EAElBJ,EAAKL,IAEVM,EAAKD,EACLF,EAAYnB,EAAWgB,GACvBK,EAAKL,EACLpI,EAAUxD,MAAM,CAACe,KAAK,CAAGgL,EACzBvI,EAAUuB,MAAM,CAAG,CACfS,SAAU2G,EAAI,AAACP,CAAAA,EAAQtL,CAAC,EAAK6L,EAAI,EACjCpL,MAAOgL,CACX,GAEAlB,IACArH,EAAUxC,CAAC,CAAGV,EAAKqB,EAAUV,EAAS,EAClC,AAAC6K,CAAAA,EAAatG,CAAO,EAAKvE,EAC1BuC,EAAUuB,MAAM,EAChBvB,CAAAA,EAAUuB,MAAM,CAACS,QAAQ,CAAG,EACvB2G,CAAAA,EAAI3I,EAAUuB,MAAM,CAACS,QAAQ,CAAG,CAAA,CAAC,EAE1CuG,EAAYvI,EAAUzC,KAAK,CAC3ByC,EAAUzC,KAAK,CAAGyC,EAAUxD,MAAM,CAACe,KAAK,CACxCyC,EAAUxD,MAAM,CAACe,KAAK,CAAGgL,GAE7B/D,EAAMxE,SAAS,CAAGmG,EAAsB3B,EAAMxE,SAAS,CAAEA,GAEzDwE,EAAMsE,UAAU,CAAG9G,AAAW,IAAXA,EACnBwC,EAAMoB,KAAK,CAAG1H,EACVmJ,EACA7C,EAAMqB,KAAK,CAAG1H,EAAUV,EAAS,EAC7B,AAAC6K,CAAAA,EAAatG,EAAW,CAAA,EAAKvE,EAGlC+G,EAAMqB,KAAK,CAAG,AAAC/I,CAAAA,EAAM4L,CAAAA,GAAMD,CAAC,CAAC,EAAK,EAGtCD,EAAalD,EAA2B,CAAC,CACjChI,EAAGY,EACHV,EAAGgH,EAAMqB,KAAK,CACdrE,EAAG6F,EACC,CAAE9J,CAAAA,EAAQ6J,EAAW5C,EAAMqB,KAAK,CAAA,EAAK,EACrC,CAAEuB,EAAW5C,EAAMqB,KAAK,EAAK,CACrC,EAAE,CAAE/K,EAAO,CAAA,EAAK,CAAC,EAAE,CACvB0J,EAAMgE,UAAU,CAAG,CAACA,EAAWlL,CAAC,CAAEkL,EAAWhL,CAAC,CAAC,CAE/CgH,EAAMkC,QAAQ,CAAG,CACbpJ,EAAGY,EACHX,MAAO6J,EAAW5C,EAAMqB,KAAK,EAC7BrI,EAAGV,EACHN,OAAQwD,EAAUvC,MAAM,EAAI,EAC5B6J,UAAW/J,CACf,EACKyK,GAAqBxD,AAAkB,CAAA,IAAlBA,EAAMoE,OAAO,EACnCN,CAAAA,GAActG,CAAO,CAGjC,CACJ,CAMAuE,EAAewC,OAAO,CAz1BtB,SAAiBC,CAAgB,EAC7B,IAAMC,EAAgBD,EAAiBnQ,SAAS,AAC3CoQ,CAAAA,EAAcpO,QAAQ,GACvBoO,EAActP,SAAS,CAACuP,KAAK,CAACrO,QAAQ,CAAGd,EACzC8F,EAAOoJ,EAAe,CAClBpO,SAAUkF,EACVoJ,aAAc1I,CAClB,GAER,EAi1BA8F,EAAe6C,cAAc,CAAGhD,EAAqBF,EAA4BkD,cAAc,CA1qBhE,CAE3BnB,OAAQ,CAAC,MAAO,MAAM,CAStB1K,MAAO,MAUP2K,UAAW,MAUXzK,OAAQ,OAUR0K,WAAY,MAOZd,SAAU,CAAA,EAOV1K,iBAAkB,CAAA,EAClB0M,UAAW,CAAA,EACXC,UAAW,EACXC,aAAc,CAAA,EACdC,aAAc,CAAA,EACdC,WAAY,CACRvC,MAAO,QACPwC,KAAM,CAAA,EACN3C,OAAQ,CAAA,EACR4C,SAAU,OACd,CACJ,GAymBAxD,EAAsBI,EAAe1N,SAAS,CAAE,CAC5CgK,WArfyDD,EAsfzDgH,kBAAmBvE,CACvB,GACA9C,IAA0IsH,kBAAkB,CAAC,WAAYtD,GAczKuD,AAR8DvD,EAQtCwC,OAAO,CAACxP,IAAkJM,eAAe,IACpK,IAAMZ,EAAiBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}