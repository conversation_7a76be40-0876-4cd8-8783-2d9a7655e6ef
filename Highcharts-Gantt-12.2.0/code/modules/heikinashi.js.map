{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/heikinashi\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * HeikinAshi series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/heikinashi\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/heikinashi\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ heikinashi_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/HeikinAshi/HeikinAshiPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { candlestick: { prototype: { pointClass: CandlestickPoint } }, hlc: { prototype: { \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\npointClass: HLCPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n/* *\n *\n *  Class\n *\n * */\nclass HeikinAshiPoint extends CandlestickPoint {\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const HeikinAshi_HeikinAshiPoint = (HeikinAshiPoint);\n\n;// ./code/es-modules/Series/HeikinAshi/HeikinAshiSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An HeikinAshi series is a style of financial chart used to describe price\n * movements over time. It displays open, high, low and close values per\n * data point.\n *\n * @sample stock/demo/heikinashi/\n *         Heikin Ashi series\n *\n * @extends      plotOptions.candlestick\n * @product      highstock\n * @requires     modules/heikinashi\n * @optionparent plotOptions.heikinashi\n */\nconst HeikinAshiDefaults = {\n    dataGrouping: {\n        groupAll: true\n    }\n};\n/**\n * A `heikinashi` series. If the [type](#series.heikinashi.type)\n * option is not specified, it is inherited from [chart.type](\n * #chart.type).\n *\n * @type      {*}\n * @extends   series,plotOptions.heikinashi\n * @excluding dataParser, dataURL, marker\n * @product   highstock\n * @requires  modules/heikinashi\n * @apioption series.heikinashi\n */\n/**\n * An array of data points for the series. For the `heikinashi` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 5 or 4 values. In this case, the values correspond\n *    to `x,open,high,low,close`. If the first value is a string, it is applied\n *    as the name of the point, and the `x` value is inferred. The `x` value can\n *    also be omitted, in which case the inner arrays should be of length 4.\n *    Then the `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 7, 2, 0, 4],\n *        [1, 1, 4, 2, 8],\n *        [2, 3, 3, 9, 3]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.heikinashi.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        open: 9,\n *        high: 2,\n *        low: 4,\n *        close: 6,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        open: 1,\n *        high: 4,\n *        low: 7,\n *        close: 7,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @type      {Array<Array<(number|string),number,number,number>|Array<(number|string),number,number,number,number>|*>}\n * @extends   series.candlestick.data\n * @excluding y\n * @product   highstock\n * @apioption series.heikinashi.data\n */\n''; // Adds doclets above to transpiled\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const HeikinAshiSeriesDefaults = (HeikinAshiDefaults);\n\n;// ./code/es-modules/Series/HeikinAshi/HeikinAshiSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { candlestick: CandlestickSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent, merge, pushUnique } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * After processing and grouping the data, calculate how the heikeinashi data\n * set should look like.\n * @private\n */\nfunction onAxisPostProcessData() {\n    const series = this.series;\n    series.forEach((series) => {\n        if (series.is('heikinashi')) {\n            const heikinashiSeries = series;\n            heikinashiSeries.heikiashiData.length = 0;\n            heikinashiSeries.getHeikinashiData();\n        }\n    });\n}\n/**\n * Assign heikinashi data into the points.\n * @private\n * @todo move to HeikinAshiPoint class\n */\nfunction onHeikinAshiSeriesAfterTranslate() {\n    const series = this, points = series.points, heikiashiData = series.heikiashiData, cropStart = series.cropStart || 0;\n    // Modify points.\n    for (let i = 0; i < points.length; i++) {\n        const point = points[i], heikiashiDataPoint = heikiashiData[i + cropStart];\n        point.open = heikiashiDataPoint[0];\n        point.high = heikiashiDataPoint[1];\n        point.low = heikiashiDataPoint[2];\n        point.close = heikiashiDataPoint[3];\n    }\n}\n/**\n * Force to recalculate the heikinashi data set after updating data.\n * @private\n */\nfunction onHeikinAshiSeriesUpdatedData() {\n    if (this.heikiashiData.length) {\n        this.heikiashiData.length = 0;\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Heikin Ashi series.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.heikinashi\n *\n * @augments Highcharts.Series\n */\nclass HeikinAshiSeries extends CandlestickSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.heikiashiData = [];\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SeriesClass, AxisClass) {\n        CandlestickSeries.compose(SeriesClass);\n        if (pushUnique(composed, 'HeikinAshi')) {\n            addEvent(AxisClass, 'postProcessData', onAxisPostProcessData);\n            addEvent(HeikinAshiSeries, 'afterTranslate', onHeikinAshiSeriesAfterTranslate);\n            addEvent(HeikinAshiSeries, 'updatedData', onHeikinAshiSeriesUpdatedData);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculate data set for the heikinashi series before creating the points.\n     * @private\n     */\n    getHeikinashiData() {\n        const series = this, table = series.allGroupedTable || series.dataTable, dataLength = table.rowCount, heikiashiData = series.heikiashiData;\n        if (!heikiashiData.length && dataLength) {\n            // Modify the first point.\n            this.modifyFirstPointValue(table.getRow(0, this.pointArrayMap));\n            // Modify other points.\n            for (let i = 1; i < dataLength; i++) {\n                this.modifyDataPoint(table.getRow(i, this.pointArrayMap), heikiashiData[i - 1]);\n            }\n        }\n        series.heikiashiData = heikiashiData;\n    }\n    /**\n     * @private\n     */\n    init() {\n        super.init.apply(this, arguments);\n        this.heikiashiData = [];\n    }\n    /**\n     * Calculate and modify the first data point value.\n     * @private\n     * @param {Array<(number)>} dataPoint\n     *        Current data point.\n     */\n    modifyFirstPointValue(dataPoint) {\n        const open = (dataPoint[0] +\n            dataPoint[1] +\n            dataPoint[2] +\n            dataPoint[3]) / 4, close = (dataPoint[0] + dataPoint[3]) / 2;\n        this.heikiashiData.push([open, dataPoint[1], dataPoint[2], close]);\n    }\n    /**\n     * Calculate and modify the data point's value.\n     * @private\n     * @param {Array<(number)>} dataPoint\n     *        Current data point.\n     * @param {Array<(number)>} previousDataPoint\n     *        Previous data point.\n     */\n    modifyDataPoint(dataPoint, previousDataPoint) {\n        const newOpen = (previousDataPoint[0] + previousDataPoint[3]) / 2, newClose = (dataPoint[0] +\n            dataPoint[1] +\n            dataPoint[2] +\n            dataPoint[3]) / 4, newHigh = Math.max(dataPoint[1], newClose, newOpen), newLow = Math.min(dataPoint[2], newClose, newOpen);\n        // Add new points to the array in order to properly calculate extremes.\n        this.heikiashiData.push([newOpen, newHigh, newLow, newClose]);\n    }\n}\nHeikinAshiSeries.defaultOptions = merge(CandlestickSeries.defaultOptions, HeikinAshiSeriesDefaults);\n/* *\n *\n *  Class Prototype\n *\n * */\nHeikinAshiSeries.prototype.pointClass = HeikinAshi_HeikinAshiPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('heikinashi', HeikinAshiSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const HeikinAshi_HeikinAshiSeries = (HeikinAshiSeries);\n\n;// ./code/es-modules/masters/modules/heikinashi.js\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nHeikinAshi_HeikinAshiSeries.compose(G.Series, G.Axis);\n/* harmony default export */ const heikinashi_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "heikinashi_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "candlestick", "pointClass", "CandlestickPoint", "hlc", "HLCPoint", "seriesTypes", "composed", "CandlestickSeries", "addEvent", "merge", "pushUnique", "onAxisPostProcessData", "series", "for<PERSON>ach", "is", "heikinashiSeries", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "getHeikinashiData", "onHeikinAshiSeriesAfterTranslate", "points", "cropStart", "i", "point", "heikiashiDataPoint", "open", "high", "low", "close", "onHeikinAshiSeriesUpdatedData", "HeikinAshiSeries", "constructor", "arguments", "compose", "SeriesClass", "AxisClass", "table", "allGroupedTable", "dataTable", "dataLength", "rowCount", "modifyFirstPointValue", "getRow", "pointArrayMap", "modifyDataPoint", "init", "apply", "dataPoint", "push", "previousDataPoint", "newOpen", "newClose", "newHigh", "Math", "max", "newLow", "min", "defaultOptions", "dataGrouping", "groupAll", "registerSeriesType", "G", "HeikinAshi_HeikinAshiSeries", "Series", "Axis"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACzH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE7GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAajL,GAAM,CAAEE,YAAa,CAAET,UAAW,CAAEU,WAAYC,CAAgB,CAAE,CAAE,CAAEC,IAAK,CAAEZ,UAAW,CAExFU,WAAYG,CAAQ,CAAE,CAAE,CAAE,CAAG,AAACL,IAA2IM,WAAW,CAmI9K,CAAEC,SAAAA,CAAQ,CAAE,CAAIT,IAIhB,CAAEG,YAAaO,CAAiB,CAAE,CAAG,AAACR,IAA2IM,WAAW,CAE5L,CAAEG,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAE,CAAIb,IAWzC,SAASc,IAELC,AADe,IAAI,CAACA,MAAM,CACnBC,OAAO,CAAC,AAACD,IACRA,EAAOE,EAAE,CAAC,gBAEVC,AADyBH,EACRI,aAAa,CAACC,MAAM,CAAG,EACxCF,AAFyBH,EAERM,iBAAiB,GAE1C,EACJ,CAMA,SAASC,IACL,IAAqBC,EAASR,AAAf,IAAI,CAAkBQ,MAAM,CAAEJ,EAAgBJ,AAA9C,IAAI,CAAiDI,aAAa,CAAEK,EAAYT,AAAhF,IAAI,CAAmFS,SAAS,EAAI,EAEnH,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAOH,MAAM,CAAEK,IAAK,CACpC,IAAMC,EAAQH,CAAM,CAACE,EAAE,CAAEE,EAAqBR,CAAa,CAACM,EAAID,EAAU,AAC1EE,CAAAA,EAAME,IAAI,CAAGD,CAAkB,CAAC,EAAE,CAClCD,EAAMG,IAAI,CAAGF,CAAkB,CAAC,EAAE,CAClCD,EAAMI,GAAG,CAAGH,CAAkB,CAAC,EAAE,CACjCD,EAAMK,KAAK,CAAGJ,CAAkB,CAAC,EAAE,AACvC,CACJ,CAKA,SAASK,IACD,IAAI,CAACb,aAAa,CAACC,MAAM,EACzB,CAAA,IAAI,CAACD,aAAa,CAACC,MAAM,CAAG,CAAA,CAEpC,CAeA,MAAMa,UAAyBvB,EAC3BwB,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAAChB,aAAa,CAAG,EAAE,AAC3B,CAMA,OAAOiB,QAAQC,CAAW,CAAEC,CAAS,CAAE,CACnC5B,EAAkB0B,OAAO,CAACC,GACtBxB,EAAWJ,EAAU,gBACrBE,EAAS2B,EAAW,kBAAmBxB,GACvCH,EAASsB,EAAkB,iBAAkBX,GAC7CX,EAASsB,EAAkB,cAAeD,GAElD,CAUAX,mBAAoB,CAChB,IAAqBkB,EAAQxB,AAAd,IAAI,CAAiByB,eAAe,EAAIzB,AAAxC,IAAI,CAA2C0B,SAAS,CAAEC,EAAaH,EAAMI,QAAQ,CAAExB,EAAgBJ,AAAvG,IAAI,CAA0GI,aAAa,CAC1I,GAAI,CAACA,EAAcC,MAAM,EAAIsB,EAAY,CAErC,IAAI,CAACE,qBAAqB,CAACL,EAAMM,MAAM,CAAC,EAAG,IAAI,CAACC,aAAa,GAE7D,IAAK,IAAIrB,EAAI,EAAGA,EAAIiB,EAAYjB,IAC5B,IAAI,CAACsB,eAAe,CAACR,EAAMM,MAAM,CAACpB,EAAG,IAAI,CAACqB,aAAa,EAAG3B,CAAa,CAACM,EAAI,EAAE,CAEtF,CACAV,AATe,IAAI,CASZI,aAAa,CAAGA,CAC3B,CAIA6B,MAAO,CACH,KAAK,CAACA,KAAKC,KAAK,CAAC,IAAI,CAAEd,WACvB,IAAI,CAAChB,aAAa,CAAG,EAAE,AAC3B,CAOAyB,sBAAsBM,CAAS,CAAE,CAC7B,IAAMtB,EAAO,AAACsB,CAAAA,CAAS,CAAC,EAAE,CACtBA,CAAS,CAAC,EAAE,CACZA,CAAS,CAAC,EAAE,CACZA,CAAS,CAAC,EAAE,AAAD,EAAK,EAAGnB,EAAQ,AAACmB,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,AAAD,EAAK,EAC/D,IAAI,CAAC/B,aAAa,CAACgC,IAAI,CAAC,CAACvB,EAAMsB,CAAS,CAAC,EAAE,CAAEA,CAAS,CAAC,EAAE,CAAEnB,EAAM,CACrE,CASAgB,gBAAgBG,CAAS,CAAEE,CAAiB,CAAE,CAC1C,IAAMC,EAAU,AAACD,CAAAA,CAAiB,CAAC,EAAE,CAAGA,CAAiB,CAAC,EAAE,AAAD,EAAK,EAAGE,EAAW,AAACJ,CAAAA,CAAS,CAAC,EAAE,CACvFA,CAAS,CAAC,EAAE,CACZA,CAAS,CAAC,EAAE,CACZA,CAAS,CAAC,EAAE,AAAD,EAAK,EAAGK,EAAUC,KAAKC,GAAG,CAACP,CAAS,CAAC,EAAE,CAAEI,EAAUD,GAAUK,EAASF,KAAKG,GAAG,CAACT,CAAS,CAAC,EAAE,CAAEI,EAAUD,GAEtH,IAAI,CAAClC,aAAa,CAACgC,IAAI,CAAC,CAACE,EAASE,EAASG,EAAQJ,EAAS,CAChE,CACJ,CACArB,EAAiB2B,cAAc,CAAGhD,EAAMF,EAAkBkD,cAAc,CA1O7C,CACvBC,aAAc,CACVC,SAAU,CAAA,CACd,CACJ,GA4OA7B,EAAiBvC,SAAS,CAACU,UAAU,CAtRrC,cAA8BC,EAC9B,EAsRAH,IAA0I6D,kBAAkB,CAAC,aAAc9B,GAa3K,IAAM+B,EAAKhE,IACXiE,AARkEhC,EAQtCG,OAAO,CAAC4B,EAAEE,MAAM,CAAEF,EAAEG,IAAI,EACvB,IAAMrE,EAAmBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}