import*as t from"./highcharts.js";var e,i,s={};s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e},s.d=(t,e)=>{for(var i in e)s.o(e,i)&&!s.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let a=t.default;var o=s.n(a);let r=t.default.Color;var l=s.n(r);let{deg2rad:n}=o(),{pick:h}=o();function p(t,e,i,s){let a=e.options.chart.options3d,o=h(s,!!i&&e.inverted),r={x:e.plotWidth/2,y:e.plotHeight/2,z:a.depth/2,vd:h(a.depth,1)*h(a.viewDistance,0)},l=e.scale3d||1,p=n*a.beta*(o?-1:1),c=n*a.alpha*(o?-1:1),x={cosA:Math.cos(c),cosB:Math.cos(-p),sinA:Math.sin(c),sinB:Math.sin(-p)};return i||(r.x+=e.plotLeft,r.y+=e.plotTop),t.map(function(t){var e,i,s;let a=(e=(o?t.y:t.x)-r.x,i=(o?t.x:t.y)-r.y,s=(t.z||0)-r.z,{x:x.cosB*e-x.sinB*s,y:-x.sinA*x.sinB*e+x.cosA*i-x.cosB*x.sinA*s,z:x.cosA*x.sinB*e+x.sinA*i+x.cosA*x.cosB*s}),n=d(a,r,r.vd);return n.x=n.x*l+r.x,n.y=n.y*l+r.y,n.z=a.z*l+r.z,{x:o?n.y:n.x,y:o?n.x:n.y,z:n.z}})}function d(t,e,i){let s=i>0&&i<Number.POSITIVE_INFINITY?i/(t.z+e.z+i):1;return{x:t.x*s,y:t.y*s}}function c(t){let e=0,i,s;for(i=0;i<t.length;i++)s=(i+1)%t.length,e+=t[i].x*t[s].y-t[s].x*t[i].y;return e/2}let x={perspective:p,perspective3D:d,pointCameraDistance:function(t,e){let i=e.options.chart.options3d,s={x:e.plotWidth/2,y:e.plotHeight/2,z:h(i.depth,1)*h(i.viewDistance,0)+i.depth};return Math.sqrt(Math.pow(s.x-h(t.plotX,t.x),2)+Math.pow(s.y-h(t.plotY,t.y),2)+Math.pow(s.z-h(t.plotZ,t.z),2))},shapeArea:c,shapeArea3D:function(t,e,i){return c(p(t,e,i))}},{parse:y}=l(),{defaultOptions:f}=o(),{perspective:u,shapeArea3D:z}=x,{addEvent:b,isArray:g,merge:m,pick:v,wrap:M}=o();!function(t){function e(t){this.is3d()&&"scatter"===t.options.type&&(t.options.type="scatter3d")}function i(){if(this.chart3d&&this.is3d()){let t=this.renderer,e=this.options.chart.options3d,i=this.chart3d.get3dFrame(),s=this.plotLeft,a=this.plotLeft+this.plotWidth,o=this.plotTop,r=this.plotTop+this.plotHeight,l=e.depth,n=s-(i.left.visible?i.left.size:0),h=a+(i.right.visible?i.right.size:0),p=o-(i.top.visible?i.top.size:0),d=r+(i.bottom.visible?i.bottom.size:0),c=0-(i.front.visible?i.front.size:0),x=l+(i.back.visible?i.back.size:0),f=this.hasRendered?"animate":"attr";this.chart3d.frame3d=i,this.frameShapes||(this.frameShapes={bottom:t.polyhedron().add(),top:t.polyhedron().add(),left:t.polyhedron().add(),right:t.polyhedron().add(),back:t.polyhedron().add(),front:t.polyhedron().add()}),this.frameShapes.bottom[f]({class:"highcharts-3d-frame highcharts-3d-frame-bottom",zIndex:i.bottom.frontFacing?-1e3:1e3,faces:[{fill:y(i.bottom.color).brighten(.1).get(),vertexes:[{x:n,y:d,z:c},{x:h,y:d,z:c},{x:h,y:d,z:x},{x:n,y:d,z:x}],enabled:i.bottom.visible},{fill:y(i.bottom.color).brighten(.1).get(),vertexes:[{x:s,y:r,z:l},{x:a,y:r,z:l},{x:a,y:r,z:0},{x:s,y:r,z:0}],enabled:i.bottom.visible},{fill:y(i.bottom.color).brighten(-.1).get(),vertexes:[{x:n,y:d,z:c},{x:n,y:d,z:x},{x:s,y:r,z:l},{x:s,y:r,z:0}],enabled:i.bottom.visible&&!i.left.visible},{fill:y(i.bottom.color).brighten(-.1).get(),vertexes:[{x:h,y:d,z:x},{x:h,y:d,z:c},{x:a,y:r,z:0},{x:a,y:r,z:l}],enabled:i.bottom.visible&&!i.right.visible},{fill:y(i.bottom.color).get(),vertexes:[{x:h,y:d,z:c},{x:n,y:d,z:c},{x:s,y:r,z:0},{x:a,y:r,z:0}],enabled:i.bottom.visible&&!i.front.visible},{fill:y(i.bottom.color).get(),vertexes:[{x:n,y:d,z:x},{x:h,y:d,z:x},{x:a,y:r,z:l},{x:s,y:r,z:l}],enabled:i.bottom.visible&&!i.back.visible}]}),this.frameShapes.top[f]({class:"highcharts-3d-frame highcharts-3d-frame-top",zIndex:i.top.frontFacing?-1e3:1e3,faces:[{fill:y(i.top.color).brighten(.1).get(),vertexes:[{x:n,y:p,z:x},{x:h,y:p,z:x},{x:h,y:p,z:c},{x:n,y:p,z:c}],enabled:i.top.visible},{fill:y(i.top.color).brighten(.1).get(),vertexes:[{x:s,y:o,z:0},{x:a,y:o,z:0},{x:a,y:o,z:l},{x:s,y:o,z:l}],enabled:i.top.visible},{fill:y(i.top.color).brighten(-.1).get(),vertexes:[{x:n,y:p,z:x},{x:n,y:p,z:c},{x:s,y:o,z:0},{x:s,y:o,z:l}],enabled:i.top.visible&&!i.left.visible},{fill:y(i.top.color).brighten(-.1).get(),vertexes:[{x:h,y:p,z:c},{x:h,y:p,z:x},{x:a,y:o,z:l},{x:a,y:o,z:0}],enabled:i.top.visible&&!i.right.visible},{fill:y(i.top.color).get(),vertexes:[{x:n,y:p,z:c},{x:h,y:p,z:c},{x:a,y:o,z:0},{x:s,y:o,z:0}],enabled:i.top.visible&&!i.front.visible},{fill:y(i.top.color).get(),vertexes:[{x:h,y:p,z:x},{x:n,y:p,z:x},{x:s,y:o,z:l},{x:a,y:o,z:l}],enabled:i.top.visible&&!i.back.visible}]}),this.frameShapes.left[f]({class:"highcharts-3d-frame highcharts-3d-frame-left",zIndex:i.left.frontFacing?-1e3:1e3,faces:[{fill:y(i.left.color).brighten(.1).get(),vertexes:[{x:n,y:d,z:c},{x:s,y:r,z:0},{x:s,y:r,z:l},{x:n,y:d,z:x}],enabled:i.left.visible&&!i.bottom.visible},{fill:y(i.left.color).brighten(.1).get(),vertexes:[{x:n,y:p,z:x},{x:s,y:o,z:l},{x:s,y:o,z:0},{x:n,y:p,z:c}],enabled:i.left.visible&&!i.top.visible},{fill:y(i.left.color).brighten(-.1).get(),vertexes:[{x:n,y:d,z:x},{x:n,y:p,z:x},{x:n,y:p,z:c},{x:n,y:d,z:c}],enabled:i.left.visible},{fill:y(i.left.color).brighten(-.1).get(),vertexes:[{x:s,y:o,z:l},{x:s,y:r,z:l},{x:s,y:r,z:0},{x:s,y:o,z:0}],enabled:i.left.visible},{fill:y(i.left.color).get(),vertexes:[{x:n,y:d,z:c},{x:n,y:p,z:c},{x:s,y:o,z:0},{x:s,y:r,z:0}],enabled:i.left.visible&&!i.front.visible},{fill:y(i.left.color).get(),vertexes:[{x:n,y:p,z:x},{x:n,y:d,z:x},{x:s,y:r,z:l},{x:s,y:o,z:l}],enabled:i.left.visible&&!i.back.visible}]}),this.frameShapes.right[f]({class:"highcharts-3d-frame highcharts-3d-frame-right",zIndex:i.right.frontFacing?-1e3:1e3,faces:[{fill:y(i.right.color).brighten(.1).get(),vertexes:[{x:h,y:d,z:x},{x:a,y:r,z:l},{x:a,y:r,z:0},{x:h,y:d,z:c}],enabled:i.right.visible&&!i.bottom.visible},{fill:y(i.right.color).brighten(.1).get(),vertexes:[{x:h,y:p,z:c},{x:a,y:o,z:0},{x:a,y:o,z:l},{x:h,y:p,z:x}],enabled:i.right.visible&&!i.top.visible},{fill:y(i.right.color).brighten(-.1).get(),vertexes:[{x:a,y:o,z:0},{x:a,y:r,z:0},{x:a,y:r,z:l},{x:a,y:o,z:l}],enabled:i.right.visible},{fill:y(i.right.color).brighten(-.1).get(),vertexes:[{x:h,y:d,z:c},{x:h,y:p,z:c},{x:h,y:p,z:x},{x:h,y:d,z:x}],enabled:i.right.visible},{fill:y(i.right.color).get(),vertexes:[{x:h,y:p,z:c},{x:h,y:d,z:c},{x:a,y:r,z:0},{x:a,y:o,z:0}],enabled:i.right.visible&&!i.front.visible},{fill:y(i.right.color).get(),vertexes:[{x:h,y:d,z:x},{x:h,y:p,z:x},{x:a,y:o,z:l},{x:a,y:r,z:l}],enabled:i.right.visible&&!i.back.visible}]}),this.frameShapes.back[f]({class:"highcharts-3d-frame highcharts-3d-frame-back",zIndex:i.back.frontFacing?-1e3:1e3,faces:[{fill:y(i.back.color).brighten(.1).get(),vertexes:[{x:h,y:d,z:x},{x:n,y:d,z:x},{x:s,y:r,z:l},{x:a,y:r,z:l}],enabled:i.back.visible&&!i.bottom.visible},{fill:y(i.back.color).brighten(.1).get(),vertexes:[{x:n,y:p,z:x},{x:h,y:p,z:x},{x:a,y:o,z:l},{x:s,y:o,z:l}],enabled:i.back.visible&&!i.top.visible},{fill:y(i.back.color).brighten(-.1).get(),vertexes:[{x:n,y:d,z:x},{x:n,y:p,z:x},{x:s,y:o,z:l},{x:s,y:r,z:l}],enabled:i.back.visible&&!i.left.visible},{fill:y(i.back.color).brighten(-.1).get(),vertexes:[{x:h,y:p,z:x},{x:h,y:d,z:x},{x:a,y:r,z:l},{x:a,y:o,z:l}],enabled:i.back.visible&&!i.right.visible},{fill:y(i.back.color).get(),vertexes:[{x:s,y:o,z:l},{x:a,y:o,z:l},{x:a,y:r,z:l},{x:s,y:r,z:l}],enabled:i.back.visible},{fill:y(i.back.color).get(),vertexes:[{x:n,y:d,z:x},{x:h,y:d,z:x},{x:h,y:p,z:x},{x:n,y:p,z:x}],enabled:i.back.visible}]}),this.frameShapes.front[f]({class:"highcharts-3d-frame highcharts-3d-frame-front",zIndex:i.front.frontFacing?-1e3:1e3,faces:[{fill:y(i.front.color).brighten(.1).get(),vertexes:[{x:n,y:d,z:c},{x:h,y:d,z:c},{x:a,y:r,z:0},{x:s,y:r,z:0}],enabled:i.front.visible&&!i.bottom.visible},{fill:y(i.front.color).brighten(.1).get(),vertexes:[{x:h,y:p,z:c},{x:n,y:p,z:c},{x:s,y:o,z:0},{x:a,y:o,z:0}],enabled:i.front.visible&&!i.top.visible},{fill:y(i.front.color).brighten(-.1).get(),vertexes:[{x:n,y:p,z:c},{x:n,y:d,z:c},{x:s,y:r,z:0},{x:s,y:o,z:0}],enabled:i.front.visible&&!i.left.visible},{fill:y(i.front.color).brighten(-.1).get(),vertexes:[{x:h,y:d,z:c},{x:h,y:p,z:c},{x:a,y:o,z:0},{x:a,y:r,z:0}],enabled:i.front.visible&&!i.right.visible},{fill:y(i.front.color).get(),vertexes:[{x:a,y:o,z:0},{x:s,y:o,z:0},{x:s,y:r,z:0},{x:a,y:r,z:0}],enabled:i.front.visible},{fill:y(i.front.color).get(),vertexes:[{x:h,y:d,z:c},{x:n,y:d,z:c},{x:n,y:p,z:c},{x:h,y:p,z:c}],enabled:i.front.visible}]})}}function s(){this.styledMode&&[{name:"darker",slope:.6},{name:"brighter",slope:1.4}].forEach(function(t){this.renderer.definition({tagName:"filter",attributes:{id:"highcharts-"+t.name},children:[{tagName:"feComponentTransfer",children:[{tagName:"feFuncR",attributes:{type:"linear",slope:t.slope}},{tagName:"feFuncG",attributes:{type:"linear",slope:t.slope}},{tagName:"feFuncB",attributes:{type:"linear",slope:t.slope}}]}]})},this)}function a(){let t=this.options;this.is3d()&&(t.series||[]).forEach(function(e){"scatter"===(e.type||t.chart.type||t.chart.defaultSeriesType)&&(e.type="scatter3d")})}function o(){let t=this.options.chart.options3d;if(this.chart3d&&this.is3d()){t&&(t.alpha=t.alpha%360+(t.alpha>=0?0:360),t.beta=t.beta%360+(t.beta>=0?0:360));let e=this.inverted,i=this.clipBox,s=this.margin;i[e?"y":"x"]=-(s[3]||0),i[e?"x":"y"]=-(s[0]||0),i[e?"height":"width"]=this.chartWidth+(s[3]||0)+(s[1]||0),i[e?"width":"height"]=this.chartHeight+(s[0]||0)+(s[2]||0),this.scale3d=1,!0===t.fitToPlot&&(this.scale3d=this.chart3d.getScale(t.depth)),this.chart3d.frame3d=this.chart3d.get3dFrame()}}function r(){this.is3d()&&(this.isDirtyBox=!0)}function l(){this.chart3d&&this.is3d()&&(this.chart3d.frame3d=this.chart3d.get3dFrame())}function n(){this.chart3d||(this.chart3d=new c(this))}function h(t){return this.is3d()||t.apply(this,[].slice.call(arguments,1))}function p(t){let e,i=this.series.length;if(this.is3d())for(;i--;)(e=this.series[i]).translate(),e.render();else t.call(this)}function d(t){t.apply(this,[].slice.call(arguments,1)),this.is3d()&&(this.container.className+=" highcharts-3d-chart")}t.defaultOptions={chart:{options3d:{enabled:!1,alpha:0,beta:0,depth:100,fitToPlot:!0,viewDistance:25,axisLabelPosition:null,frame:{visible:"default",size:1,bottom:{},top:{},left:{},right:{},back:{},front:{}}}}},t.compose=function(c,x){let y=c.prototype,u=x.prototype;y.is3d=function(){return!!this.options.chart.options3d?.enabled},y.propsRequireDirtyBox.push("chart.options3d"),y.propsRequireUpdateSeries.push("chart.options3d"),u.matrixSetter=function(){let t;if(this.pos<1&&(g(this.start)||g(this.end))){let e=this.start||[1,0,0,1,0,0],i=this.end||[1,0,0,1,0,0];t=[];for(let s=0;s<6;s++)t.push(this.pos*i[s]+(1-this.pos)*e[s])}else t=this.end;this.elem.attr(this.prop,t,null,!0)},m(!0,f,t.defaultOptions),b(c,"init",n),b(c,"addSeries",e),b(c,"afterDrawChartBox",i),b(c,"afterGetContainer",s),b(c,"afterInit",a),b(c,"afterSetChartSize",o),b(c,"beforeRedraw",r),b(c,"beforeRender",l),M(y,"isInsidePlot",h),M(y,"renderSeries",p),M(y,"setClassName",d)};class c{constructor(t){this.chart=t}get3dFrame(){let t=this.chart,e=t.options.chart.options3d,i=e.frame,s=t.plotLeft,a=t.plotLeft+t.plotWidth,o=t.plotTop,r=t.plotTop+t.plotHeight,l=e.depth,n=function(e){let i=z(e,t);return i>.5?1:i<-.5?-1:0},h=n([{x:s,y:r,z:l},{x:a,y:r,z:l},{x:a,y:r,z:0},{x:s,y:r,z:0}]),p=n([{x:s,y:o,z:0},{x:a,y:o,z:0},{x:a,y:o,z:l},{x:s,y:o,z:l}]),d=n([{x:s,y:o,z:0},{x:s,y:o,z:l},{x:s,y:r,z:l},{x:s,y:r,z:0}]),c=n([{x:a,y:o,z:l},{x:a,y:o,z:0},{x:a,y:r,z:0},{x:a,y:r,z:l}]),x=n([{x:s,y:r,z:0},{x:a,y:r,z:0},{x:a,y:o,z:0},{x:s,y:o,z:0}]),y=n([{x:s,y:o,z:l},{x:a,y:o,z:l},{x:a,y:r,z:l},{x:s,y:r,z:l}]),f=!1,b=!1,g=!1,m=!1;[].concat(t.xAxis,t.yAxis,t.zAxis).forEach(function(t){t&&(t.horiz?t.opposite?b=!0:f=!0:t.opposite?m=!0:g=!0)});let M=function(t,e,i){let s=["size","color","visible"],a={};for(let e=0;e<s.length;e++){let i=s[e];for(let e=0;e<t.length;e++)if("object"==typeof t[e]){let s=t[e][i];if(null!=s){a[i]=s;break}}}let o=i;return!0===a.visible||!1===a.visible?o=a.visible:"auto"===a.visible&&(o=e>0),{size:v(a.size,1),color:v(a.color,"none"),frontFacing:e>0,visible:o}},P={axes:{},bottom:M([i.bottom,i.top,i],h,f),top:M([i.top,i.bottom,i],p,b),left:M([i.left,i.right,i.side,i],d,g),right:M([i.right,i.left,i.side,i],c,m),back:M([i.back,i.front,i],y,!0),front:M([i.front,i.back,i],x,!1)};if("auto"===e.axisLabelPosition){let e=function(t,e){return t.visible!==e.visible||t.visible&&e.visible&&t.frontFacing!==e.frontFacing},i=[];e(P.left,P.front)&&i.push({y:(o+r)/2,x:s,z:0,xDir:{x:1,y:0,z:0}}),e(P.left,P.back)&&i.push({y:(o+r)/2,x:s,z:l,xDir:{x:0,y:0,z:-1}}),e(P.right,P.front)&&i.push({y:(o+r)/2,x:a,z:0,xDir:{x:0,y:0,z:1}}),e(P.right,P.back)&&i.push({y:(o+r)/2,x:a,z:l,xDir:{x:-1,y:0,z:0}});let n=[];e(P.bottom,P.front)&&n.push({x:(s+a)/2,y:r,z:0,xDir:{x:1,y:0,z:0}}),e(P.bottom,P.back)&&n.push({x:(s+a)/2,y:r,z:l,xDir:{x:-1,y:0,z:0}});let h=[];e(P.top,P.front)&&h.push({x:(s+a)/2,y:o,z:0,xDir:{x:1,y:0,z:0}}),e(P.top,P.back)&&h.push({x:(s+a)/2,y:o,z:l,xDir:{x:-1,y:0,z:0}});let p=[];e(P.bottom,P.left)&&p.push({z:(0+l)/2,y:r,x:s,xDir:{x:0,y:0,z:-1}}),e(P.bottom,P.right)&&p.push({z:(0+l)/2,y:r,x:a,xDir:{x:0,y:0,z:1}});let d=[];e(P.top,P.left)&&d.push({z:(0+l)/2,y:o,x:s,xDir:{x:0,y:0,z:-1}}),e(P.top,P.right)&&d.push({z:(0+l)/2,y:o,x:a,xDir:{x:0,y:0,z:1}});let c=function(e,i,s){if(0===e.length)return null;if(1===e.length)return e[0];let a=u(e,t,!1),o=0;for(let t=1;t<a.length;t++)s*a[t][i]>s*a[o][i]?o=t:s*a[t][i]==s*a[o][i]&&a[t].z<a[o].z&&(o=t);return e[o]};P.axes={y:{left:c(i,"x",-1),right:c(i,"x",1)},x:{top:c(h,"y",-1),bottom:c(n,"y",1)},z:{top:c(d,"y",-1),bottom:c(p,"y",1)}}}else P.axes={y:{left:{x:s,z:0,xDir:{x:1,y:0,z:0}},right:{x:a,z:0,xDir:{x:0,y:0,z:1}}},x:{top:{y:o,z:0,xDir:{x:1,y:0,z:0}},bottom:{y:r,z:0,xDir:{x:1,y:0,z:0}}},z:{top:{x:g?a:s,y:o,xDir:g?{x:0,y:0,z:1}:{x:0,y:0,z:-1}},bottom:{x:g?a:s,y:r,xDir:g?{x:0,y:0,z:1}:{x:0,y:0,z:-1}}}};return P}getScale(t){let e=this.chart,i=e.plotLeft,s=e.plotWidth+i,a=e.plotTop,o=e.plotHeight+a,r=i+e.plotWidth/2,l=a+e.plotHeight/2,n={minX:Number.MAX_VALUE,maxX:-Number.MAX_VALUE,minY:Number.MAX_VALUE,maxY:-Number.MAX_VALUE},h,p=1;return h=[{x:i,y:a,z:0},{x:i,y:a,z:t}],[0,1].forEach(function(t){h.push({x:s,y:h[t].y,z:h[t].z})}),[0,1,2,3].forEach(function(t){h.push({x:h[t].x,y:o,z:h[t].z})}),(h=u(h,e,!1)).forEach(function(t){n.minX=Math.min(n.minX,t.x),n.maxX=Math.max(n.maxX,t.x),n.minY=Math.min(n.minY,t.y),n.maxY=Math.max(n.maxY,t.y)}),i>n.minX&&(p=Math.min(p,1-Math.abs((i+r)/(n.minX+r))%1)),s<n.maxX&&(p=Math.min(p,(s-r)/(n.maxX-r))),a>n.minY&&(p=n.minY<0?Math.min(p,(a+l)/(-n.minY+a+l)):Math.min(p,1-(a+l)/(n.minY+l)%1)),o<n.maxY&&(p=Math.min(p,Math.abs((o-l)/(n.maxY-l)))),p}}t.Additions=c}(e||(e={}));let P=e,A=t.default.SeriesRegistry;var k=s.n(A);let{composed:S}=o(),{perspective:w}=x,{line:{prototype:D}}=k().seriesTypes,{pushUnique:L,wrap:I}=o();function T(t){let e=t.apply(this,[].slice.call(arguments,1));if(!this.chart.is3d())return e;let i=D.getGraphPath,s=this.options,a=Math.round(this.yAxis.getThreshold(s.threshold)),o=[];if(this.rawPointsX)for(let t=0;t<this.points.length;t++)o.push({x:this.rawPointsX[t],y:s.stacking?this.points[t].yBottom:a,z:this.zPadding});let r=this.chart.options.chart.options3d;o=w(o,this.chart,!0).map(t=>({plotX:t.x,plotY:t.y,plotZ:t.z})),this.group&&r&&r.depth&&r.beta&&(this.markerGroup&&(this.markerGroup.add(this.group),this.markerGroup.attr({translateX:0,translateY:0})),this.group.attr({zIndex:Math.max(1,r.beta>270||r.beta<90?r.depth-Math.round(this.zPadding||0):Math.round(this.zPadding||0))})),o.reversed=!0;let l=i.call(this,o,!0,!0);if(l[0]&&"M"===l[0][0]&&(l[0]=["L",l[0][1],l[0][2]]),this.areaPath){let t=this.areaPath.splice(0,this.areaPath.length/2).concat(l);t.xMap=this.areaPath.xMap,this.areaPath=t}return this.graphPath=e,e}let X={labels:{position3d:"offset",skew3d:!1},title:{position3d:null,skew3d:null}},{composed:Y}=o(),{addEvent:Z,extend:O,pushUnique:E,wrap:F}=o();function C(t){let e=this.axis.axis3D;e&&O(t.pos,e.fix3dPosition(t.pos))}function W(t){let e=this.axis.axis3D,i=t.apply(this,[].slice.call(arguments,1));if(e){let t=i[0],s=i[1];if("M"===t[0]&&"L"===s[0]){let i=[e.fix3dPosition({x:t[1],y:t[2],z:0}),e.fix3dPosition({x:s[1],y:s[2],z:0})];return this.axis.chart.renderer.toLineSegments(i)}}return i}let B=function(t){E(Y,"Axis.Tick3D")&&(Z(t,"afterGetLabelPosition",C),F(t.prototype,"getMarkPath",W))},{defaultOptions:G}=o(),{deg2rad:R}=o(),{perspective:N,perspective3D:j,shapeArea:H}=x,{addEvent:U,merge:V,pick:_,wrap:q}=o();function J(){let t=this.chart,e=this.options;t.is3d?.()&&"colorAxis"!==this.coll&&(e.tickWidth=_(e.tickWidth,0),e.gridLineWidth=_(e.gridLineWidth,1))}function K(t){this.chart.is3d()&&"colorAxis"!==this.coll&&t.point&&(t.point.crosshairPos=this.isXAxis?t.point.axisXpos:this.len-t.point.axisYpos)}function Q(){this.axis3D||(this.axis3D=new ta(this))}function $(t){return this.chart.is3d()&&"colorAxis"!==this.coll?[]:t.apply(this,[].slice.call(arguments,1))}function tt(t){if(!this.chart.is3d()||"colorAxis"===this.coll)return t.apply(this,[].slice.call(arguments,1));let e=arguments,i=e[1],s=e[2],a=[],o=this.getPlotLinePath({value:i}),r=this.getPlotLinePath({value:s});if(o&&r)for(let t=0;t<o.length;t+=2){let e=o[t],i=o[t+1],s=r[t],l=r[t+1];"M"===e[0]&&"L"===i[0]&&"M"===s[0]&&"L"===l[0]&&a.push(e,i,l,["L",s[1],s[2]],["Z"])}return a}function te(t){let e=this.axis3D,i=this.chart,s=t.apply(this,[].slice.call(arguments,1));if("colorAxis"===this.coll||!i.chart3d||!i.is3d()||null===s)return s;let a=i.options.chart.options3d,o=this.isZAxis?i.plotWidth:a.depth,r=i.chart3d.frame3d,l=s[0],n=s[1],h,p=[];return"M"===l[0]&&"L"===n[0]&&(h=[e.swapZ({x:l[1],y:l[2],z:0}),e.swapZ({x:l[1],y:l[2],z:o}),e.swapZ({x:n[1],y:n[2],z:0}),e.swapZ({x:n[1],y:n[2],z:o})],this.horiz?(this.isZAxis?(r.left.visible&&p.push(h[0],h[2]),r.right.visible&&p.push(h[1],h[3])):(r.front.visible&&p.push(h[0],h[2]),r.back.visible&&p.push(h[1],h[3])),r.top.visible&&p.push(h[0],h[1]),r.bottom.visible&&p.push(h[2],h[3])):(r.front.visible&&p.push(h[0],h[2]),r.back.visible&&p.push(h[1],h[3]),r.left.visible&&p.push(h[0],h[1]),r.right.visible&&p.push(h[2],h[3])),p=N(p,this.chart,!1)),i.renderer.toLineSegments(p)}function ti(t,e){let{chart:i,gridGroup:s,tickPositions:a,ticks:o}=this;if(this.categories&&i.frameShapes&&i.is3d()&&s&&e&&e.label){let t,r,l,n=s.element.childNodes[0].getBBox(),h=i.frameShapes.left.getBBox(),p=i.options.chart.options3d,d={x:i.plotWidth/2,y:i.plotHeight/2,z:p.depth/2,vd:_(p.depth,1)*_(p.viewDistance,0)},c=a.indexOf(e.pos),x=o[a[c-1]],y=o[a[c+1]];return x?.label?.xy&&(r=j({x:x.label.xy.x,y:x.label.xy.y,z:null},d,d.vd)),y?.label?.xy&&(l=j({x:y.label.xy.x,y:y.label.xy.y,z:null},d,d.vd)),t=j(t={x:e.label.xy.x,y:e.label.xy.y,z:null},d,d.vd),Math.abs(r?t.x-r.x:l?l.x-t.x:n.x-h.x)}return t.apply(this,[].slice.call(arguments,1))}function ts(t){let e=t.apply(this,[].slice.call(arguments,1));return this.axis3D?this.axis3D.fix3dPosition(e,!0):e}class ta{static compose(t,e){if(B(e),!t.keepProps.includes("axis3D")){V(!0,G.xAxis,X),t.keepProps.push("axis3D"),U(t,"init",Q),U(t,"afterSetOptions",J),U(t,"drawCrosshair",K);let e=t.prototype;q(e,"getLinePath",$),q(e,"getPlotBandPath",tt),q(e,"getPlotLinePath",te),q(e,"getSlotWidth",ti),q(e,"getTitlePosition",ts)}}constructor(t){this.axis=t}fix3dPosition(t,e){let i=this.axis,s=i.chart;if("colorAxis"===i.coll||!s.chart3d||!s.is3d())return t;let a=R*s.options.chart.options3d.alpha,o=R*s.options.chart.options3d.beta,r=_(e&&i.options.title.position3d,i.options.labels.position3d),l=_(e&&i.options.title.skew3d,i.options.labels.skew3d),n=s.chart3d.frame3d,h=s.plotLeft,p=s.plotWidth+h,d=s.plotTop,c=s.plotHeight+d,x=0,y=0,f,u={x:0,y:1,z:0},z=!1;if(t=i.axis3D.swapZ({x:t.x,y:t.y,z:0}),i.isZAxis){if(i.opposite){if(null===n.axes.z.top)return{};y=t.y-d,t.x=n.axes.z.top.x,t.y=n.axes.z.top.y,f=n.axes.z.top.xDir,z=!n.top.frontFacing}else{if(null===n.axes.z.bottom)return{};y=t.y-c,t.x=n.axes.z.bottom.x,t.y=n.axes.z.bottom.y,f=n.axes.z.bottom.xDir,z=!n.bottom.frontFacing}}else if(i.horiz){if(i.opposite){if(null===n.axes.x.top)return{};y=t.y-d,t.y=n.axes.x.top.y,t.z=n.axes.x.top.z,f=n.axes.x.top.xDir,z=!n.top.frontFacing}else{if(null===n.axes.x.bottom)return{};y=t.y-c,t.y=n.axes.x.bottom.y,t.z=n.axes.x.bottom.z,f=n.axes.x.bottom.xDir,z=!n.bottom.frontFacing}}else if(i.opposite){if(null===n.axes.y.right)return{};x=t.x-p,t.x=n.axes.y.right.x,t.z=n.axes.y.right.z,f={x:(f=n.axes.y.right.xDir).z,y:f.y,z:-f.x}}else{if(null===n.axes.y.left)return{};x=t.x-h,t.x=n.axes.y.left.x,t.z=n.axes.y.left.z,f=n.axes.y.left.xDir}if("chart"===r);else if("flap"===r){if(i.horiz){let t=Math.sin(a),e=Math.cos(a);i.opposite&&(t=-t),z&&(t=-t),u={x:f.z*t,y:e,z:-f.x*t}}else f={x:Math.cos(o),y:0,z:Math.sin(o)}}else if("ortho"===r){if(i.horiz){let t=Math.sin(a),e=Math.cos(a),i={x:Math.sin(o)*e,y:-t,z:-e*Math.cos(o)},s=1/Math.sqrt((u={x:f.y*i.z-f.z*i.y,y:f.z*i.x-f.x*i.z,z:f.x*i.y-f.y*i.x}).x*u.x+u.y*u.y+u.z*u.z);z&&(s=-s),u={x:s*u.x,y:s*u.y,z:s*u.z}}else f={x:Math.cos(o),y:0,z:Math.sin(o)}}else i.horiz?u={x:Math.sin(o)*Math.sin(a),y:Math.cos(a),z:-Math.cos(o)*Math.sin(a)}:f={x:Math.cos(o),y:0,z:Math.sin(o)};t.x+=x*f.x+y*u.x,t.y+=x*f.y+y*u.y,t.z+=x*f.z+y*u.z;let b=N([t],i.chart)[0];if(l){0>H(N([t,{x:t.x+f.x,y:t.y+f.y,z:t.z+f.z},{x:t.x+u.x,y:t.y+u.y,z:t.z+u.z}],i.chart))&&(f={x:-f.x,y:-f.y,z:-f.z});let e=N([{x:t.x,y:t.y,z:t.z},{x:t.x+f.x,y:t.y+f.y,z:t.z+f.z},{x:t.x+u.x,y:t.y+u.y,z:t.z+u.z}],i.chart);b.matrix=[e[1].x-e[0].x,e[1].y-e[0].y,e[2].x-e[0].x,e[2].y-e[0].y,b.x,b.y],b.matrix[4]-=b.x*b.matrix[0]+b.y*b.matrix[2],b.matrix[5]-=b.x*b.matrix[1]+b.y*b.matrix[3]}return b}swapZ(t,e){let i=this.axis;if(i.isZAxis){let s=e?0:i.chart.plotLeft;return{x:s+t.z,y:t.y,z:t.x-s}}return t}}let to=t.default.RendererRegistry;var tr=s.n(to);let tl=t.default.Series;var tn=s.n(tl);let{composed:th}=o(),{perspective:tp}=x,{addEvent:td,extend:tc,isNumber:tx,merge:ty,pick:tf,pushUnique:tu}=o();class tz extends tn(){static compose(t){tu(th,"Core.Series3D")&&(td(t,"afterTranslate",function(){this.chart.is3d()&&this.translate3dPoints()}),tc(t.prototype,{translate3dPoints:tz.prototype.translate3dPoints}))}translate3dPoints(){let t,e,i=this,s=i.options,a=i.chart,o=tf(i.zAxis,a.options.zAxis[0]),r=[],l=[],n=s.stacking?tx(s.stack)?s.stack:0:i.index||0;i.zPadding=n*(s.depth||0+(s.groupZPadding||1)),i.data.forEach(t=>{o?.translate?(e=o.logarithmic&&o.val2lin?o.val2lin(t.z):t.z,t.plotZ=o.translate(e),t.isInside=!!t.isInside&&e>=o.min&&e<=o.max):t.plotZ=i.zPadding,t.axisXpos=t.plotX,t.axisYpos=t.plotY,t.axisZpos=t.plotZ,r.push({x:t.plotX,y:t.plotY,z:t.plotZ}),l.push(t.plotX||0)}),i.rawPointsX=l;let h=tp(r,a,!0);i.data.forEach((e,i)=>{e.plotX=(t=h[i]).x,e.plotY=t.y,e.plotZ=t.z})}}tz.defaultOptions=ty(tn().defaultOptions);let tb=t.default.StackItem;var tg=s.n(tb);let{parse:tm}=l(),{Element:tv}=tr().getRendererType().prototype,{defined:tM,pick:tP}=o();class tA extends tv{constructor(){super(...arguments),this.parts=["front","top","side"],this.pathType="cuboid"}initArgs(t){let e=this.renderer,i=e[this.pathType+"Path"](t),s=i.zIndexes;for(let t of this.parts){let a={class:"highcharts-3d-"+t,zIndex:s[t]||0};e.styledMode&&("top"===t?a.filter="url(#highcharts-brighter)":"side"===t&&(a.filter="url(#highcharts-darker)")),this[t]=e.path(i[t]).attr(a).add(this)}this.attr({"stroke-linejoin":"round",zIndex:s.group}),this.forcedSides=i.forcedSides}singleSetterForParts(t,e,i,s,a,o){let r={},l=[null,null,s||"attr",a,o],n=i?.zIndexes;if(i){for(let e of(n?.group&&this.attr({zIndex:n.group}),Object.keys(i)))r[e]={},r[e][t]=i[e],n&&(r[e].zIndex=i.zIndexes[e]||0);l[1]=r}else r[t]=e,l[0]=r;return this.processParts.apply(this,l)}processParts(t,e,i,s,a){for(let o of this.parts)e&&(t=tP(e[o],!1)),!1!==t&&this[o][i](t,s,a);return this}destroy(){return this.processParts(null,null,"destroy"),super.destroy()}attr(t,e,i,s){if("string"==typeof t&&void 0!==e){let i=t;(t={})[i]=e}return t.shapeArgs||tM(t.x)?this.singleSetterForParts("d",null,this.renderer[this.pathType+"Path"](t.shapeArgs||t)):super.attr(t,void 0,i,s)}animate(t,e,i){if(tM(t.x)&&tM(t.y)){let s=this.renderer[this.pathType+"Path"](t),a=s.forcedSides;this.singleSetterForParts("d",null,s,"animate",e,i),this.attr({zIndex:s.zIndexes.group}),a===this.forcedSides||(this.forcedSides=a,this.renderer.styledMode||this.fillSetter(this.fill))}else super.animate(t,e,i);return this}fillSetter(t){return this.forcedSides=this.forcedSides||[],this.singleSetterForParts("fill",null,{front:t,top:tm(t).brighten(this.forcedSides.indexOf("top")>=0?0:.1).get(),side:tm(t).brighten(this.forcedSides.indexOf("side")>=0?0:-.1).get()}),this.color=this.fill=t,this}}tA.types={base:tA,cuboid:tA};let{animObject:tk}=o(),{parse:tS}=l(),{charts:tw,deg2rad:tD}=o(),{perspective:tL,shapeArea:tI}=x,{defined:tT,extend:tX,merge:tY,pick:tZ}=o(),tO=Math.cos,tE=Math.sin,tF=Math.PI,tC=4*(Math.sqrt(2)-1)/3/(tF/2);function tW(t,e,i,s,a,o,r,l){let n=o-a,h=[];return o>a&&o-a>Math.PI/2+1e-4?h=(h=h.concat(tW(t,e,i,s,a,a+Math.PI/2,r,l))).concat(tW(t,e,i,s,a+Math.PI/2,o,r,l)):o<a&&a-o>Math.PI/2+1e-4?h=(h=h.concat(tW(t,e,i,s,a,a-Math.PI/2,r,l))).concat(tW(t,e,i,s,a-Math.PI/2,o,r,l)):[["C",t+i*Math.cos(a)-i*tC*n*Math.sin(a)+r,e+s*Math.sin(a)+s*tC*n*Math.cos(a)+l,t+i*Math.cos(o)+i*tC*n*Math.sin(o)+r,e+s*Math.sin(o)-s*tC*n*Math.cos(o)+l,t+i*Math.cos(o)+r,e+s*Math.sin(o)+l]]}!function(t){function e(t,e){let i=[];for(let e of t)i.push(["L",e.x,e.y]);return t.length&&(i[0][0]="M",e&&i.push(["Z"])),i}function i(t){let e=[],i=!0;for(let s of t)e.push(i?["M",s.x,s.y]:["L",s.x,s.y]),i=!i;return e}function s(t){let e=this,i=e.Element.prototype,s=e.createElement("path");return s.vertexes=[],s.insidePlotArea=!1,s.enabled=!0,s.attr=function(t){if("object"==typeof t&&(tT(t.enabled)||tT(t.vertexes)||tT(t.insidePlotArea))){this.enabled=tZ(t.enabled,this.enabled),this.vertexes=tZ(t.vertexes,this.vertexes),this.insidePlotArea=tZ(t.insidePlotArea,this.insidePlotArea),delete t.enabled,delete t.vertexes,delete t.insidePlotArea;let i=tw[e.chartIndex],s=tL(this.vertexes,i,this.insidePlotArea),a=e.toLinePath(s,!0),o=tI(s);t.d=a,t.visibility=this.enabled&&o>0?"inherit":"hidden"}return i.attr.apply(this,arguments)},s.animate=function(t){if("object"==typeof t&&(tT(t.enabled)||tT(t.vertexes)||tT(t.insidePlotArea))){this.enabled=tZ(t.enabled,this.enabled),this.vertexes=tZ(t.vertexes,this.vertexes),this.insidePlotArea=tZ(t.insidePlotArea,this.insidePlotArea),delete t.enabled,delete t.vertexes,delete t.insidePlotArea;let i=tw[e.chartIndex],s=tL(this.vertexes,i,this.insidePlotArea),a=e.toLinePath(s,!0),o=tI(s),r=this.enabled&&o>0?"visible":"hidden";t.d=a,this.attr("visibility",r)}return i.animate.apply(this,arguments)},s.attr(t)}function a(t){let e=this,i=e.Element.prototype,s=e.g(),a=s.destroy;return this.styledMode||s.attr({"stroke-linejoin":"round"}),s.faces=[],s.destroy=function(){for(let t=0;t<s.faces.length;t++)s.faces[t].destroy();return a.call(this)},s.attr=function(t,a,o,r){if("object"==typeof t&&tT(t.faces)){for(;s.faces.length>t.faces.length;)s.faces.pop().destroy();for(;s.faces.length<t.faces.length;)s.faces.push(e.face3d().add(s));for(let i=0;i<t.faces.length;i++)e.styledMode&&delete t.faces[i].fill,s.faces[i].attr(t.faces[i],null,o,r);delete t.faces}return i.attr.apply(this,arguments)},s.animate=function(t,a,o){if(t?.faces){for(;s.faces.length>t.faces.length;)s.faces.pop().destroy();for(;s.faces.length<t.faces.length;)s.faces.push(e.face3d().add(s));for(let e=0;e<t.faces.length;e++)s.faces[e].animate(t.faces[e],a,o);delete t.faces}return i.animate.apply(this,arguments)},s.attr(t)}function r(t,e){let i=new tA.types[t](this,"g");return i.initArgs(e),i}function l(t){return this.element3d("cuboid",t)}function n(t){let e=t.x||0,i=t.y||0,s=t.z||0,a=t.height||0,o=t.width||0,r=t.depth||0,l=tw[this.chartIndex],n=l.options.chart.options3d.alpha,h=[],p,d=0,c=[{x:e,y:i,z:s},{x:e+o,y:i,z:s},{x:e+o,y:i+a,z:s},{x:e,y:i+a,z:s},{x:e,y:i+a,z:s+r},{x:e+o,y:i+a,z:s+r},{x:e+o,y:i,z:s+r},{x:e,y:i,z:s+r}];c=tL(c,l,t.insidePlotArea);let x=t=>0===a&&t>1&&t<6?{x:c[t].x,y:c[t].y+10,z:c[t].z}:c[0].x===c[7].x&&t>=4?{x:c[t].x+10,y:c[t].y,z:c[t].z}:0===r&&t<2||t>5?{x:c[t].x,y:c[t].y,z:c[t].z+10}:c[t],y=t=>c[t],f=(t,e,i)=>{let s=t.map(y),a=e.map(y),o=t.map(x),r=e.map(x),l=[[],-1];return 0>tI(s)?l=[s,0]:0>tI(a)?l=[a,1]:i&&(h.push(i),l=0>tI(o)?[s,0]:0>tI(r)?[a,1]:[s,0]),l},u=(p=f([3,2,1,0],[7,6,5,4],"front"))[0],z=p[1],b=(p=f([1,6,7,0],[4,5,2,3],"top"))[0],g=p[1],m=(p=f([1,2,5,6],[0,7,4,3],"side"))[0],v=p[1];return 1===v?d+=1e6*(l.plotWidth-e):v||(d+=1e6*e),d+=10*(!g||n>=0&&n<=180||n<360&&n>357.5?l.plotHeight-i:10+i),1===z?d+=100*s:z||(d+=100*(1e3-s)),{front:this.toLinePath(u,!0),top:this.toLinePath(b,!0),side:this.toLinePath(m,!0),zIndexes:{group:Math.round(d)},forcedSides:h,isFront:z,isTop:g}}function h(t){let e=this.g(),i=this.Element.prototype,s=["alpha","beta","x","y","r","innerR","start","end","depth"];function a(t){let e,i={};for(e in t=tY(t))-1!==s.indexOf(e)&&(i[e]=t[e],delete t[e]);return!!Object.keys(i).length&&[i,t]}for(let i of((t=tY(t)).alpha=(t.alpha||0)*tD,t.beta=(t.beta||0)*tD,e.top=this.path(),e.side1=this.path(),e.side2=this.path(),e.inn=this.path(),e.out=this.path(),e.onAdd=function(){let t=e.parentGroup,i=e.attr("class");for(let s of(e.top.add(e),["out","inn","side1","side2"]))e[s].attr({class:i+" highcharts-3d-side"}).add(t)},["addClass","removeClass"]))e[i]=function(){let t=arguments;for(let s of["top","out","inn","side1","side2"])e[s][i].apply(e[s],t)};for(let i of(e.setPaths=function(t){let i=e.renderer.arc3dPath(t),s=100*i.zTop;e.attribs=t,e.top.attr({d:i.top,zIndex:i.zTop}),e.inn.attr({d:i.inn,zIndex:i.zInn}),e.out.attr({d:i.out,zIndex:i.zOut}),e.side1.attr({d:i.side1,zIndex:i.zSide1}),e.side2.attr({d:i.side2,zIndex:i.zSide2}),e.zIndex=s,e.attr({zIndex:s}),t.center&&(e.top.setRadialReference(t.center),delete t.center)},e.setPaths(t),e.fillSetter=function(t){let e=tS(t).brighten(-.1).get();return this.fill=t,this.side1.attr({fill:e}),this.side2.attr({fill:e}),this.inn.attr({fill:e}),this.out.attr({fill:e}),this.top.attr({fill:t}),this},["opacity","translateX","translateY","visibility"]))e[i+"Setter"]=function(t,i){for(let s of(e[i]=t,["out","inn","side1","side2","top"]))e[s].attr(i,t)};return e.attr=function(t){if("object"==typeof t){let i=a(t);if(i){let t=i[0];arguments[0]=i[1],void 0!==t.alpha&&(t.alpha*=tD),void 0!==t.beta&&(t.beta*=tD),tX(e.attribs,t),e.attribs&&e.setPaths(e.attribs)}}return i.attr.apply(e,arguments)},e.animate=function(t,s,r){let l=this.attribs,n="data-"+Math.random().toString(26).substring(2,9);delete t.center,delete t.z;let h=tk(tZ(s,this.renderer.globalAnimation));if(h.duration){let i=a(t);if(e[n]=0,t[n]=1,e[n+"Setter"]=o().noop,i){let t=i[0],e=(e,i)=>l[e]+(tZ(t[e],l[e])-l[e])*i;h.step=function(t,i){i.prop===n&&i.elem.setPaths(tY(l,{x:e("x",i.pos),y:e("y",i.pos),r:e("r",i.pos),innerR:e("innerR",i.pos),start:e("start",i.pos),end:e("end",i.pos),depth:e("depth",i.pos)}))}}s=h}return i.animate.call(this,t,s,r)},e.destroy=function(){return this.top.destroy(),this.out.destroy(),this.inn.destroy(),this.side1.destroy(),this.side2.destroy(),i.destroy.call(this)},e.hide=function(){this.top.hide(),this.out.hide(),this.inn.hide(),this.side1.hide(),this.side2.hide()},e.show=function(t){this.top.show(t),this.out.show(t),this.inn.show(t),this.side1.show(t),this.side2.show(t)},e}function p(t){let e=t.x||0,i=t.y||0,s=t.start||0,a=(t.end||0)-1e-5,o=t.r||0,r=t.innerR||0,l=t.depth||0,n=t.alpha||0,h=t.beta||0,p=Math.cos(s),d=Math.sin(s),c=Math.cos(a),x=Math.sin(a),y=o*Math.cos(h),f=o*Math.cos(n),u=r*Math.cos(h),z=r*Math.cos(n),b=l*Math.sin(h),g=l*Math.sin(n),m=[["M",e+y*p,i+f*d]];(m=m.concat(tW(e,i,y,f,s,a,0,0))).push(["L",e+u*c,i+z*x]),(m=m.concat(tW(e,i,u,z,a,s,0,0))).push(["Z"]);let v=h>0?Math.PI/2:0,M=n>0?0:Math.PI/2,P=s>-v?s:a>-v?-v:s,A=a<tF-M?a:s<tF-M?tF-M:a,k=2*tF-M,S=[["M",e+y*tO(P),i+f*tE(P)]];S=S.concat(tW(e,i,y,f,P,A,0,0)),a>k&&s<k?(S.push(["L",e+y*tO(A)+b,i+f*tE(A)+g]),(S=S.concat(tW(e,i,y,f,A,k,b,g))).push(["L",e+y*tO(k),i+f*tE(k)]),(S=S.concat(tW(e,i,y,f,k,a,0,0))).push(["L",e+y*tO(a)+b,i+f*tE(a)+g]),(S=S.concat(tW(e,i,y,f,a,k,b,g))).push(["L",e+y*tO(k),i+f*tE(k)]),S=S.concat(tW(e,i,y,f,k,A,0,0))):a>tF-M&&s<tF-M&&(S.push(["L",e+y*Math.cos(A)+b,i+f*Math.sin(A)+g]),(S=S.concat(tW(e,i,y,f,A,a,b,g))).push(["L",e+y*Math.cos(a),i+f*Math.sin(a)]),S=S.concat(tW(e,i,y,f,a,A,0,0))),S.push(["L",e+y*Math.cos(A)+b,i+f*Math.sin(A)+g]),(S=S.concat(tW(e,i,y,f,A,P,b,g))).push(["Z"]);let w=[["M",e+u*p,i+z*d]];(w=w.concat(tW(e,i,u,z,s,a,0,0))).push(["L",e+u*Math.cos(a)+b,i+z*Math.sin(a)+g]),(w=w.concat(tW(e,i,u,z,a,s,b,g))).push(["Z"]);let D=[["M",e+y*p,i+f*d],["L",e+y*p+b,i+f*d+g],["L",e+u*p+b,i+z*d+g],["L",e+u*p,i+z*d],["Z"]],L=[["M",e+y*c,i+f*x],["L",e+y*c+b,i+f*x+g],["L",e+u*c+b,i+z*x+g],["L",e+u*c,i+z*x],["Z"]],I=Math.atan2(g,-b),T=Math.abs(a+I),X=Math.abs(s+I),Y=Math.abs((s+a)/2+I);function Z(t){return(t%=2*Math.PI)>Math.PI&&(t=2*Math.PI-t),t}T=Z(T),X=Z(X);let O=1e5*(Y=Z(Y)),E=1e5*X,F=1e5*T;return{top:m,zTop:1e5*Math.PI+1,out:S,zOut:Math.max(O,E,F),inn:w,zInn:Math.max(O,E,F),side1:D,zSide1:.99*F,side2:L,zSide2:.99*E}}t.compose=function(t){let o=t.prototype;o.element3d||tX(o,{Element3D:tA,arc3d:h,arc3dPath:p,cuboid:l,cuboidPath:n,element3d:r,face3d:s,polyhedron:a,toLinePath:e,toLineSegments:i})}}(i||(i={}));let tB=i,tG=t.default.Axis;var tR=s.n(tG);let{defaultOptions:tN}=o(),{addEvent:tj,merge:tH,pick:tU,splat:tV}=o();function t_(t){return new tJ(this,t)}function tq(){let t=this.options.zAxis=tV(this.options.zAxis||{});this.is3d()&&(this.zAxis=[],t.forEach(t=>{this.addZAxis(t).setScale()}))}class tJ extends tR(){constructor(){super(...arguments),this.isZAxis=!0}static compose(t){let e=t.prototype;e.addZAxis||(tN.zAxis=tH(tN.xAxis,{offset:0,lineWidth:0}),e.addZAxis=t_,e.collectionsWithInit.zAxis=[e.addZAxis],e.collectionsWithUpdate.push("zAxis"),tj(t,"afterCreateAxes",tq))}init(t,e){this.isZAxis=!0,super.init(t,e,"zAxis")}getSeriesExtremes(){this.hasVisibleSeries=!1,this.dataMin=this.dataMax=this.ignoreMinPadding=this.ignoreMaxPadding=void 0,this.stacking&&this.stacking.buildStacks(),this.series.forEach(t=>{if(t.reserveSpace()){let e=t.options.threshold;this.hasVisibleSeries=!0,this.positiveValuesOnly&&e<=0&&(e=void 0);let i=t.getColumn("z");i.length&&(this.dataMin=Math.min(tU(this.dataMin,i[0]),Math.min.apply(null,i)),this.dataMax=Math.max(tU(this.dataMax,i[0]),Math.max.apply(null,i)))}})}setAxisSize(){let t=this.chart;super.setAxisSize(),this.width=this.len=t.options.chart.options3d?.depth||0,this.right=t.chartWidth-this.width-this.left}}let{composed:tK}=o(),{perspective:tQ}=x,{addEvent:t$,extend:t0,pick:t1,pushUnique:t3,wrap:t2}=o();function t5(){let t=this.chart,e=this.options,i=e.depth,s=(e.stacking?e.stack||0:this.index)*(i+(e.groupZPadding||1)),a=this.borderWidth%2?.5:0,o;for(let r of(t.inverted&&!this.yAxis.reversed&&(a*=-1),!1!==e.grouping&&(s=0),s+=e.groupZPadding||1,this.points))if(r.outside3dPlot=null,null!==r.y){let e,l=t0({x:0,y:0,width:0,height:0},r.shapeArgs||{}),n=[["x","width"],["y","height"]],h=r.tooltipPos;for(let t of n)if((e=l[t[0]]-a)<0&&(l[t[1]]+=l[t[0]]+a,l[t[0]]=-a,e=0),e+l[t[1]]>this[t[0]+"Axis"].len&&0!==l[t[1]]&&(l[t[1]]=this[t[0]+"Axis"].len-l[t[0]]),0!==l[t[1]]&&(l[t[0]]>=this[t[0]+"Axis"].len||l[t[0]]+l[t[1]]<=a)){for(let t in l)l[t]="y"===t?-9999:0;r.outside3dPlot=!0}if("roundedRect"===r.shapeType&&(r.shapeType="cuboid"),r.shapeArgs=t0(l,{z:s,depth:i,insidePlotArea:!0}),o={x:l.x+l.width/2,y:l.y,z:s+i/2},t.inverted&&(o.x=l.height,o.y=r.clientX||0),r.axisXpos=o.x,r.axisYpos=o.y,r.axisZpos=o.z,r.plot3d=tQ([o],t,!0,!1)[0],h){let e=tQ([{x:h[0],y:h[1],z:s+i/2}],t,!0,!1)[0];r.tooltipPos=[e.x,e.y]}}this.z=s}function t6(){if(this.chart.is3d()){let t=this.options,e=t.grouping,i=t.stacking,s=this.yAxis.options.reversedStacks,a=0;if(!(void 0!==e&&!e)){let e,o=function(t,e){let i=t.series,s={totalStacks:0},a,o=1;return i.forEach(function(t){s[a=t1(t.options.stack,e?0:i.length-1-t.index)]?s[a].series.push(t):(s[a]={series:[t],position:o},o++)}),s.totalStacks=o+1,s}(this.chart,i),r=t.stack||0;for(e=0;e<o[r].series.length&&o[r].series[e]!==this;e++);a=10*(o.totalStacks-o[r].position)+(s?e:-e),this.xAxis.reversed||(a=10*o.totalStacks-a)}t.depth=t.depth||25,this.z=this.z||0,t.zIndex=a}}function t9(t,...e){return this.series.chart.is3d()?this.graphic&&"g"!==this.graphic.element.nodeName:t.apply(this,e)}function t4(t){if(this.chart.is3d()){let t=arguments,e=t[1],i=this.yAxis,s=this.yAxis.reversed;if(e)for(let t of this.points)null===t.y||(t.height=t.shapeArgs.height,t.shapey=t.shapeArgs.y,t.shapeArgs.height=1,s||(t.stackY?t.shapeArgs.y=t.plotY+i.translate(t.stackY):t.shapeArgs.y=t.plotY+(t.negative?-t.height:t.height)));else{for(let t of this.points)null!==t.y&&(t.shapeArgs.height=t.height,t.shapeArgs.y=t.shapey,t.graphic&&t.graphic[t.outside3dPlot?"attr":"animate"](t.shapeArgs,this.options.animation));this.drawDataLabels()}}else t.apply(this,[].slice.call(arguments,1))}function t7(t,e,i,s,a,o){return"dataLabelsGroup"!==e&&"markerGroup"!==e&&this.chart.is3d()&&(this[e]&&delete this[e],o&&(this.chart.columnGroup||(this.chart.columnGroup=this.chart.renderer.g("columnGroup").add(o)),this[e]=this.chart.columnGroup,this.chart.columnGroup.attr(this.getPlotBox()),this[e].survive=!0,"group"===e&&(arguments[3]="visible"))),t.apply(this,Array.prototype.slice.call(arguments,1))}function t8(t){let e=t.apply(this,[].slice.call(arguments,1));return this.chart.is3d&&this.chart.is3d()&&(e.stroke=this.options.edgeColor||e.fill,e["stroke-width"]=t1(this.options.edgeWidth,1)),e}function et(t,e,i){let s=this.chart.is3d&&this.chart.is3d();s&&(this.options.inactiveOtherPoints=!0),t.call(this,e,i),s&&(this.options.inactiveOtherPoints=!1)}function ee(t,e){if(this.chart.is3d())for(let t of this.points)t.visible=t.options.visible=e=void 0===e?!t1(this.visible,t.visible):e,this.options.data[this.data.indexOf(t)]=t.options,t.graphic&&t.graphic.attr({visibility:e?"visible":"hidden"});t.apply(this,Array.prototype.slice.call(arguments,1))}function ei(t){t.apply(this,[].slice.call(arguments,1)),this.chart.is3d()&&this.translate3dShapes()}function es(t,e,i,s,a){let o=this.chart;if(s.outside3dPlot=e.outside3dPlot,o.is3d()&&this.is("column")){let t=this.options,i=t1(s.inside,!!this.options.stacking),r=o.options.chart.options3d,l=e.pointWidth/2||0,n={x:a.x+l,y:a.y,z:this.z+t.depth/2};o.inverted&&(i&&(a.width=0,n.x+=e.shapeArgs.height/2),r.alpha>=90&&r.alpha<=270&&(n.y+=e.shapeArgs.width)),a.x=(n=tQ([n],o,!0,!1)[0]).x-l,a.y=e.outside3dPlot?-9e9:n.y}t.apply(this,[].slice.call(arguments,1))}function ea(t){return!arguments[2].outside3dPlot&&t.apply(this,[].slice.call(arguments,1))}function eo(t,e){let i=t.apply(this,[].slice.call(arguments,1)),s=this.axis.chart,{width:a}=e;if(s.is3d()&&this.base){let t=+this.base.split(",")[0],e=s.series[t],o=s.options.chart.options3d;if(e&&"column"===e.type){let t={x:i.x+(s.inverted?i.height:a/2),y:i.y,z:e.options.depth/2};s.inverted&&(i.width=0,o.alpha>=90&&o.alpha<=270&&(t.y+=a)),i.x=(t=tQ([t],s,!0,!1)[0]).x-a/2,i.y=t.y}}return i}let{pie:{prototype:{pointClass:er}}}=k().seriesTypes,el=class extends er{haloPath(){return this.series?.chart.is3d()?[]:super.haloPath.apply(this,arguments)}},{composed:en,deg2rad:eh}=o(),{pie:ep}=k().seriesTypes,{extend:ed,pick:ec,pushUnique:ex}=o();class ey extends ep{static compose(t){ex(en,"Pie3D")&&(t.types.pie=ey)}addPoint(){super.addPoint.apply(this,arguments),this.chart.is3d()&&this.update(this.userOptions,!0)}animate(t){if(this.chart.is3d()){let e=this.center,i=this.group,s=this.markerGroup,a=this.options.animation,o;!0===a&&(a={}),t?(i.oldtranslateX=ec(i.oldtranslateX,i.translateX),i.oldtranslateY=ec(i.oldtranslateY,i.translateY),o={translateX:e[0],translateY:e[1],scaleX:.001,scaleY:.001},i.attr(o),s&&(s.attrSetters=i.attrSetters,s.attr(o))):(o={translateX:i.oldtranslateX,translateY:i.oldtranslateY,scaleX:1,scaleY:1},i.animate(o,a),s&&s.animate(o,a))}else super.animate.apply(this,arguments)}getDataLabelPosition(t,e){let i=super.getDataLabelPosition(t,e);if(this.chart.is3d()){let e=this.chart.options.chart.options3d,s=t.shapeArgs,a=s.r,o=(s.alpha||e?.alpha)*eh,r=(s.beta||e?.beta)*eh,l=(s.start+s.end)/2,n=i.connectorPosition,h=-a*(1-Math.cos(o))*Math.sin(l),p=a*(Math.cos(r)-1)*Math.cos(l);for(let t of[i?.natural,n.breakAt,n.touchingSliceAt])t.x+=p,t.y+=h}return i}pointAttribs(t){let e=super.pointAttribs.apply(this,arguments),i=this.options;return this.chart.is3d()&&!this.chart.styledMode&&(e.stroke=i.edgeColor||t.color||this.color,e["stroke-width"]=ec(i.edgeWidth,1)),e}translate(){if(super.translate.apply(this,arguments),!this.chart.is3d())return;let t=this.options,e=t.depth||0,i=this.chart.options.chart.options3d,s=i.alpha,a=i.beta,o=t.stacking?(t.stack||0)*e:this._i*e;for(let i of(o+=e/2,!1!==t.grouping&&(o=0),this.points)){let r=i.shapeArgs;i.shapeType="arc3d",r.z=o,r.depth=.75*e,r.alpha=s,r.beta=a,r.center=this.center;let l=(r.end+r.start)/2;i.slicedTranslation={translateX:Math.round(Math.cos(l)*t.slicedOffset*Math.cos(s*eh)),translateY:Math.round(Math.sin(l)*t.slicedOffset*Math.cos(s*eh))}}}drawTracker(){if(super.drawTracker.apply(this,arguments),this.chart.is3d()){for(let t of this.points)if(t.graphic)for(let e of["out","inn","side1","side2"])t.graphic&&(t.graphic[e].element.point=t)}}}ed(ey.prototype,{pointClass:el});let ef=t.default.Series.types.scatter;var eu=s.n(ef);let{pointClass:ez}=eu().prototype,{defined:eb}=o(),eg=class extends ez{applyOptions(){return super.applyOptions.apply(this,arguments),eb(this.z)||(this.z=0),this}},{pointCameraDistance:em}=x,{extend:ev,merge:eM}=o();class eP extends eu(){pointAttribs(t){let e=super.pointAttribs.apply(this,arguments);return this.chart.is3d()&&t&&(e.zIndex=em(t,this.chart)),e}}eP.defaultOptions=eM(eu().defaultOptions,{tooltip:{pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>"}}),ev(eP.prototype,{axisTypes:["xAxis","yAxis","zAxis"],directTouch:!0,parallelArrays:["x","y","z"],pointArrayMap:["x","y","z"],pointClass:eg}),k().registerSeriesType("scatter3d",eP);let eA=o();({compose:function(t){L(S,"Area3DSeries")&&I(t.prototype,"getGraphPath",T)}}).compose(eA.Series.types.area),ta.compose(eA.Axis,eA.Tick),P.compose(eA.Chart,eA.Fx),({compose:function(t,e){if(t3(tK,"Column3D")){let i=t.prototype,s=e.prototype,{column:a,columnRange:o}=t.types;if(t2(i,"alignDataLabel",es),t2(i,"justifyDataLabel",ea),t2(s,"getStackBox",eo),a){let t=a.prototype,e=t.pointClass.prototype;t.translate3dPoints=()=>void 0,t.translate3dShapes=t5,t$(t,"afterInit",t6),t2(e,"hasNewShapeType",t9),t2(t,"animate",t4),t2(t,"plotGroup",t7),t2(t,"pointAttribs",t8),t2(t,"setState",et),t2(t,"setVisible",ee),t2(t,"translate",ei)}if(o){let t=o.prototype;t2(t.pointClass.prototype,"hasNewShapeType",t9),t2(t,"plotGroup",t7),t2(t,"pointAttribs",t8),t2(t,"setState",et),t2(t,"setVisible",ee)}}}}).compose(eA.Series,tg()),ey.compose(eA.Series),tz.compose(eA.Series),tB.compose(tr().getRendererType()),tJ.compose(eA.Chart);let ek=eA;export{ek as default};