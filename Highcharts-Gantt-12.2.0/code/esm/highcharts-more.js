import*as t from"./highcharts.js";var e,i,s,a,o={};o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let r=t.default;var n=o.n(r);let l=t.default.SeriesRegistry;var h=o.n(l);let p=t.default.Series;var d=o.n(p);let{deg2rad:c}=n(),{fireEvent:u,isNumber:g,pick:f,relativeLength:m}=n();(e=i||(i={})).getCenter=function(){let t=this.options,e=this.chart,i=2*(t.slicedOffset||0),s=e.plotWidth-2*i,a=e.plotHeight-2*i,o=t.center,r=Math.min(s,a),n=t.thickness,l,h=t.size,p=t.innerSize||0,c,b;"string"==typeof h&&(h=parseFloat(h)),"string"==typeof p&&(p=parseFloat(p));let y=[f(o?.[0],"50%"),f(o?.[1],"50%"),f(h&&h<0?void 0:t.size,"100%"),f(p&&p<0?void 0:t.innerSize||0,"0%")];for(!e.angular||this instanceof d()||(y[3]=0),c=0;c<4;++c)b=y[c],l=c<2||2===c&&/%$/.test(b),y[c]=m(b,[s,a,r,y[2]][c])+(l?i:0);return y[3]>y[2]&&(y[3]=y[2]),g(n)&&2*n<y[2]&&n>0&&(y[3]=y[2]-2*n),u(this,"afterGetCenter",{positions:y}),y},e.getStartAndEndRadians=function(t,e){let i=g(t)?t:0,s=g(e)&&e>i&&e-i<360?e:i+360;return{start:c*(i+-90),end:c*(s+-90)}};let b=i,{addEvent:y,correctFloat:x,defined:P,pick:v}=n();function M(t){let e,i=this;return t&&i.pane.forEach(s=>{L(t.chartX-i.plotLeft,t.chartY-i.plotTop,s.center)&&(e=s)}),e}function L(t,e,i,s,a){let o=!0,r=i[0],n=i[1],l=Math.sqrt(Math.pow(t-r,2)+Math.pow(e-n,2));if(P(s)&&P(a)){let i=Math.atan2(x(e-n,8),x(t-r,8));a!==s&&(o=s>a?i>=s&&i<=Math.PI||i<=a&&i>=-Math.PI:i>=s&&i<=x(a,8))}return l<=Math.ceil(i[2]/2)&&o}function k(t){this.polar&&(t.options.inverted&&([t.x,t.y]=[t.y,t.x]),t.isInsidePlot=this.pane.some(e=>L(t.x,t.y,e.center,e.axis&&e.axis.normalizedStartAngleRad,e.axis&&e.axis.normalizedEndAngleRad)))}function w(t){let e=this.chart;t.hoverPoint&&t.hoverPoint.plotX&&t.hoverPoint.plotY&&e.hoverPane&&!L(t.hoverPoint.plotX,t.hoverPoint.plotY,e.hoverPane.center)&&(t.hoverPoint=void 0)}function A(t){let e=this.chart;e.polar?(e.hoverPane=e.getHoverPane(t),t.filter=function(i){return i.visible&&!(!t.shared&&i.directTouch)&&v(i.options.enableMouseTracking,!0)&&(!e.hoverPane||i.xAxis.pane===e.hoverPane)}):e.hoverPane=void 0}let S={pane:{center:["50%","50%"],size:"85%",innerSize:"0%",startAngle:0},background:{shape:"circle",borderRadius:0,borderWidth:1,borderColor:"#cccccc",backgroundColor:{linearGradient:{x1:0,y1:0,x2:0,y2:1},stops:[[0,"#ffffff"],[1,"#e6e6e6"]]},from:-Number.MAX_VALUE,innerRadius:0,to:Number.MAX_VALUE,outerRadius:"105%"}},{extend:N,merge:T,splat:C}=n();class X{constructor(t,e){this.coll="pane",this.init(t,e)}init(t,e){this.chart=e,this.background=[],e.pane.push(this),this.setOptions(t)}setOptions(t){this.options=t=T(S.pane,this.chart.angular?{background:{}}:void 0,t)}render(){let t=this.options,e=this.chart.renderer;this.group||(this.group=e.g("pane-group").attr({zIndex:t.zIndex||0}).add()),this.updateCenter();let i=this.options.background;if(i){let t=Math.max((i=C(i)).length,this.background.length||0);for(let e=0;e<t;e++)i[e]&&this.axis?this.renderBackground(T(S.background,i[e]),e):this.background[e]&&(this.background[e]=this.background[e].destroy(),this.background.splice(e,1))}}renderBackground(t,e){let i={class:"highcharts-pane "+(t.className||"")},s="animate";this.chart.styledMode||N(i,{fill:t.backgroundColor,stroke:t.borderColor,"stroke-width":t.borderWidth}),this.background[e]||(this.background[e]=this.chart.renderer.path().add(this.group),s="attr"),this.background[e][s]({d:this.axis.getPlotBandPath(t.from,t.to,t)}).attr(i)}updateCenter(t){this.center=(t||this.axis||{}).center=b.getCenter.call(this)}update(t,e){T(!0,this.options,t),this.setOptions(this.options),this.render(),this.chart.axes.forEach(function(t){t.pane===this&&(t.pane=null,t.update({},e))},this)}}X.compose=function(t,e){let i=t.prototype;i.getHoverPane||(i.collectionsWithUpdate.push("pane"),i.getHoverPane=M,y(t,"afterIsInsidePlot",k),y(e,"afterGetHoverData",w),y(e,"beforeGetHoverData",A))};let{area:{prototype:{pointClass:Y,pointClass:{prototype:I}}}}=h().seriesTypes,{defined:R,isNumber:E}=n(),z=class extends Y{setState(){let t=this.state,e=this.series,i=e.chart.polar;R(this.plotHigh)||(this.plotHigh=e.yAxis.toPixels(this.high,!0)),R(this.plotLow)||(this.plotLow=this.plotY=e.yAxis.toPixels(this.low,!0)),e.lowerStateMarkerGraphic=e.stateMarkerGraphic,e.stateMarkerGraphic=e.upperStateMarkerGraphic,this.graphic=this.graphics&&this.graphics[1],this.plotY=this.plotHigh,i&&E(this.plotHighX)&&(this.plotX=this.plotHighX),I.setState.apply(this,arguments),this.state=t,this.plotY=this.plotLow,this.graphic=this.graphics&&this.graphics[0],i&&E(this.plotLowX)&&(this.plotX=this.plotLowX),e.upperStateMarkerGraphic=e.stateMarkerGraphic,e.stateMarkerGraphic=e.lowerStateMarkerGraphic,e.lowerStateMarkerGraphic=void 0;let s=e.modifyMarkerSettings();I.setState.apply(this,arguments),e.restoreMarkerSettings(s)}haloPath(){let t=this.series.chart.polar,e=[];return this.plotY=this.plotLow,t&&E(this.plotLowX)&&(this.plotX=this.plotLowX),this.isInside&&(e=I.haloPath.apply(this,arguments)),this.plotY=this.plotHigh,t&&E(this.plotHighX)&&(this.plotX=this.plotHighX),this.isTopInside&&(e=e.concat(I.haloPath.apply(this,arguments))),e}isValid(){return E(this.low)&&E(this.high)}},{noop:D}=n(),{area:O,area:{prototype:B},column:{prototype:W}}=h().seriesTypes,{addEvent:F,defined:H,extend:G,isArray:q,isNumber:V,pick:U,merge:K}=n();class _ extends O{toYData(t){return[t.low,t.high]}highToXY(t){let e=this.chart,i=this.xAxis.postTranslate(t.rectPlotX||0,this.yAxis.len-(t.plotHigh||0));t.plotHighX=i.x-e.plotLeft,t.plotHigh=i.y-e.plotTop,t.plotLowX=t.plotX}getGraphPath(t){let e=[],i=[],s=B.getGraphPath,a=this.options,o=this.chart.polar,r=o&&!1!==a.connectEnds,n=a.connectNulls,l,h,p,d=a.step;for(l=(t=t||this.points).length;l--;){h=t[l];let s=o?{plotX:h.rectPlotX,plotY:h.yBottom,doCurve:!1}:{plotX:h.plotX,plotY:h.plotY,doCurve:!1};h.isNull||r||n||t[l+1]&&!t[l+1].isNull||i.push(s),p={polarPlotY:h.polarPlotY,rectPlotX:h.rectPlotX,yBottom:h.yBottom,plotX:U(h.plotHighX,h.plotX),plotY:h.plotHigh,isNull:h.isNull},i.push(p),e.push(p),h.isNull||r||n||t[l-1]&&!t[l-1].isNull||i.push(s)}let c=s.call(this,t);d&&(!0===d&&(d="left"),a.step=({left:"right",center:"center",right:"left"})[d]);let u=s.call(this,e),g=s.call(this,i);a.step=d;let f=[].concat(c,u);return!this.chart.polar&&g[0]&&"M"===g[0][0]&&(g[0]=["L",g[0][1],g[0][2]]),this.graphPath=f,this.areaPath=c.concat(g),f.isArea=!0,f.xMap=c.xMap,this.areaPath.xMap=c.xMap,f}drawDataLabels(){let t,e,i,s,a,o=this.points,r=o.length,n=[],l=this.options.dataLabels,h=this.chart.inverted;if(l){if(q(l)?(s=l[0]||{enabled:!1},a=l[1]||{enabled:!1}):((s=G({},l)).x=l.xHigh,s.y=l.yHigh,(a=G({},l)).x=l.xLow,a.y=l.yLow),s.enabled||this.hasDataLabels?.()){for(t=r;t--;)if(e=o[t]){let{plotHigh:a=0,plotLow:o=0}=e;i=s.inside?a<o:a>o,e.y=e.high,e._plotY=e.plotY,e.plotY=a,n[t]=e.dataLabel,e.dataLabel=e.dataLabelUpper,e.below=i,h?s.align||(s.align=i?"right":"left"):s.verticalAlign||(s.verticalAlign=i?"top":"bottom")}for(this.options.dataLabels=s,B.drawDataLabels&&B.drawDataLabels.apply(this,arguments),t=r;t--;)(e=o[t])&&(e.dataLabelUpper=e.dataLabel,e.dataLabel=n[t],delete e.dataLabels,e.y=e.low,e.plotY=e._plotY)}if(a.enabled||this.hasDataLabels?.()){for(t=r;t--;)if(e=o[t]){let{plotHigh:t=0,plotLow:s=0}=e;i=a.inside?t<s:t>s,e.below=!i,h?a.align||(a.align=i?"left":"right"):a.verticalAlign||(a.verticalAlign=i?"bottom":"top")}this.options.dataLabels=a,B.drawDataLabels&&B.drawDataLabels.apply(this,arguments)}if(s.enabled)for(t=r;t--;)(e=o[t])&&(e.dataLabels=[e.dataLabelUpper,e.dataLabel].filter(function(t){return!!t}));this.options.dataLabels=l}}alignDataLabel(){W.alignDataLabel.apply(this,arguments)}modifyMarkerSettings(){let t={marker:this.options.marker,symbol:this.symbol};if(this.options.lowMarker){let{options:{marker:t,lowMarker:e}}=this;this.options.marker=K(t,e),e.symbol&&(this.symbol=e.symbol)}return t}restoreMarkerSettings(t){this.options.marker=t.marker,this.symbol=t.symbol}drawPoints(){let t,e,i=this.points.length,s=this.modifyMarkerSettings();for(B.drawPoints.apply(this,arguments),this.restoreMarkerSettings(s),t=0;t<i;)(e=this.points[t]).graphics=e.graphics||[],e.origProps={plotY:e.plotY,plotX:e.plotX,isInside:e.isInside,negative:e.negative,zone:e.zone,y:e.y},(e.graphic||e.graphics[0])&&(e.graphics[0]=e.graphic),e.graphic=e.graphics[1],e.plotY=e.plotHigh,H(e.plotHighX)&&(e.plotX=e.plotHighX),e.y=U(e.high,e.origProps.y),e.negative=e.y<(this.options.threshold||0),this.zones.length&&(e.zone=e.getZone()),this.chart.polar||(e.isInside=e.isTopInside=void 0!==e.plotY&&e.plotY>=0&&e.plotY<=this.yAxis.len&&e.plotX>=0&&e.plotX<=this.xAxis.len),t++;for(B.drawPoints.apply(this,arguments),t=0;t<i;)(e=this.points[t]).graphics=e.graphics||[],(e.graphic||e.graphics[1])&&(e.graphics[1]=e.graphic),e.graphic=e.graphics[0],e.origProps&&(G(e,e.origProps),delete e.origProps),t++}hasMarkerChanged(t,e){let i=t.lowMarker,s=e.lowMarker||{};return i&&(!1===i.enabled||s.symbol!==i.symbol||s.height!==i.height||s.width!==i.width)||super.hasMarkerChanged(t,e)}}_.defaultOptions=K(O.defaultOptions,{lineWidth:1,threshold:null,tooltip:{pointFormat:'<span style="color:{series.color}">●</span> {series.name}: <b>{point.low}</b> - <b>{point.high}</b><br/>'},trackByArea:!0,dataLabels:{align:void 0,verticalAlign:void 0,xLow:0,xHigh:0,yLow:0,yHigh:0}}),F(_,"afterTranslate",function(){"low,high"===this.pointArrayMap.join(",")&&this.points.forEach(t=>{let e=t.high,i=t.plotY;t.isNull?t.plotY=void 0:(t.plotLow=i,t.plotHigh=V(e)?this.yAxis.translate(this.dataModify?this.dataModify.modifyValue(e):e,!1,!0,void 0,!0):void 0,this.dataModify&&(t.yBottom=t.plotHigh))})},{order:0}),F(_,"afterTranslate",function(){this.points.forEach(t=>{if(this.chart.polar)this.highToXY(t),t.plotLow=t.plotY,t.tooltipPos=[((t.plotHighX||0)+(t.plotLowX||0))/2,((t.plotHigh||0)+(t.plotLow||0))/2];else{let e=t.pos(!1,t.plotLow),i=t.pos(!1,t.plotHigh);e&&i&&(e[0]=(e[0]+i[0])/2,e[1]=(e[1]+i[1])/2),t.tooltipPos=e}})},{order:3}),G(_.prototype,{deferTranslatePolar:!0,pointArrayMap:["low","high"],pointClass:z,pointValKey:"low",setStackedPoints:D}),h().registerSeriesType("arearange",_);let Z=_,{spline:{prototype:j}}=h().seriesTypes,{merge:$,extend:Q}=n();class J extends Z{}J.defaultOptions=$(Z.defaultOptions),Q(J.prototype,{getPointSpline:j.getPointSpline}),h().registerSeriesType("areasplinerange",J);let tt=t.default.Series.types.column;var te=o.n(tt);let{noop:ti}=n(),{crisp:ts,extend:ta,merge:to,pick:tr,relativeLength:tn}=n();class tl extends te(){pointAttribs(){return{}}getWhiskerPair(t,e,i,s,a){let o=a.whiskers.strokeWidth(),r=(i,s)=>{let a=tn(i,2*t)/2,r=ts(s,o);return[["M",ts(e-a),r],["L",ts(e+a),r]]};return[...r(i,a.highPlot),...r(s,a.lowPlot)]}translate(){let t=this.yAxis,e=this.pointArrayMap;super.translate.apply(this),this.points.forEach(function(i){e.forEach(function(e){null!==i[e]&&(i[e+"Plot"]=t.translate(i[e],0,1,0,1))}),i.plotHigh=i.highPlot})}drawPoints(){let t,e,i,s,a,o,r,n,l,h,p,d=this.points,c=this.options,u=this.chart,g=u.renderer,f=!1!==this.doQuartiles,m=this.options.whiskerLength;for(let b of d){let d=(n=b.graphic)?"animate":"attr",y=b.shapeArgs,x={},P={},v={},M={},L=b.color||this.color,k=b.options.whiskerLength||m;if(void 0!==b.plotY){let w;l=y.width,p=(h=y.x)+l,t=f?b.q1Plot:b.lowPlot,e=f?b.q3Plot:b.lowPlot,i=b.highPlot,s=b.lowPlot,n||(b.graphic=n=g.g("point").add(this.group),b.stem=g.path().addClass("highcharts-boxplot-stem").add(n),m&&(b.whiskers=g.path().addClass("highcharts-boxplot-whisker").add(n)),f&&(b.box=g.path(r).addClass("highcharts-boxplot-box").add(n)),b.medianShape=g.path(o).addClass("highcharts-boxplot-median").add(n)),u.styledMode||(P.stroke=b.stemColor||c.stemColor||L,P["stroke-width"]=tr(b.stemWidth,c.stemWidth,c.lineWidth),P.dashstyle=b.stemDashStyle||c.stemDashStyle||c.dashStyle,b.stem.attr(P),k&&(v.stroke=b.whiskerColor||c.whiskerColor||L,v["stroke-width"]=tr(b.whiskerWidth,c.whiskerWidth,c.lineWidth),v.dashstyle=b.whiskerDashStyle||c.whiskerDashStyle||c.dashStyle,b.whiskers.attr(v)),f&&(x.fill=b.fillColor||c.fillColor||L,x.stroke=c.lineColor||L,x["stroke-width"]=c.lineWidth||0,x.dashstyle=b.boxDashStyle||c.boxDashStyle||c.dashStyle,b.box.attr(x)),M.stroke=b.medianColor||c.medianColor||L,M["stroke-width"]=tr(b.medianWidth,c.medianWidth,c.lineWidth),M.dashstyle=b.medianDashStyle||c.medianDashStyle||c.dashStyle,b.medianShape.attr(M));let A=ts((b.plotX||0)+(this.pointXOffset||0)+(this.barW||0)/2,b.stem.strokeWidth());if(w=[["M",A,e],["L",A,i],["M",A,t],["L",A,s]],b.stem[d]({d:w}),f){let i=b.box.strokeWidth();t=ts(t,i),e=ts(e,i),w=[["M",h=ts(h,i),e],["L",h,t],["L",p=ts(p,i),t],["L",p,e],["L",h,e],["Z"]],b.box[d]({d:w})}if(k){let t=l/2,e=this.getWhiskerPair(t,A,b.upperWhiskerLength??c.upperWhiskerLength??k,b.lowerWhiskerLength??c.lowerWhiskerLength??k,b);b.whiskers[d]({d:e})}w=[["M",h,a=ts(b.medianPlot,b.medianShape.strokeWidth())],["L",p,a]],b.medianShape[d]({d:w})}}}toYData(t){return[t.low,t.q1,t.median,t.q3,t.high]}}tl.defaultOptions=to(te().defaultOptions,{threshold:null,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b>{series.name}</b><br/>Maximum: {point.high}<br/>Upper quartile: {point.q3}<br/>Median: {point.median}<br/>Lower quartile: {point.q1}<br/>Minimum: {point.low}<br/>'},whiskerLength:"50%",fillColor:"#ffffff",lineWidth:1,medianWidth:2,whiskerWidth:2}),ta(tl.prototype,{pointArrayMap:["low","q1","median","q3","high"],pointValKey:"high",drawDataLabels:ti,setStackedPoints:ti}),h().registerSeriesType("boxplot",tl);let th=tl,tp={borderColor:void 0,borderWidth:2,className:void 0,color:void 0,connectorClassName:void 0,connectorColor:void 0,connectorDistance:60,connectorWidth:1,enabled:!1,labels:{className:void 0,allowOverlap:!1,format:"",formatter:void 0,align:"right",style:{fontSize:"0.9em",color:"#000000"},x:0,y:0},maxSize:60,minSize:10,legendIndex:0,ranges:{value:void 0,borderColor:void 0,color:void 0,connectorColor:void 0},sizeBy:"area",sizeByAbsoluteValue:!1,zIndex:1,zThreshold:0},td=t.default.Templating;var tc=o.n(td);let{noop:tu}=n(),{arrayMax:tg,arrayMin:tf,isNumber:tm,merge:tb,pick:ty,stableSort:tx}=n(),tP=class{constructor(t,e){this.setState=tu,this.init(t,e)}init(t,e){this.options=t,this.visible=!0,this.chart=e.chart,this.legend=e}addToLegend(t){t.splice(this.options.legendIndex,0,this)}drawLegendSymbol(t){let e,i=ty(t.options.itemDistance,20),s=this.legendItem||{},a=this.options,o=a.ranges,r=a.connectorDistance;if(!o||!o.length||!tm(o[0].value)){t.options.bubbleLegend.autoRanges=!0;return}tx(o,function(t,e){return e.value-t.value}),this.ranges=o,this.setOptions(),this.render();let n=this.getMaxLabelSize(),l=this.ranges[0].radius,h=2*l;e=(e=r-l+n.width)>0?e:0,this.maxLabel=n,this.movementX="left"===a.labels.align?e:0,s.labelWidth=h+e+i,s.labelHeight=h+n.height/2}setOptions(){let t=this.ranges,e=this.options,i=this.chart.series[e.seriesIndex],s=this.legend.baseline,a={zIndex:e.zIndex,"stroke-width":e.borderWidth},o={zIndex:e.zIndex,"stroke-width":e.connectorWidth},r={align:this.legend.options.rtl||"left"===e.labels.align?"right":"left",zIndex:e.zIndex},n=i.options.marker.fillOpacity,l=this.chart.styledMode;t.forEach(function(h,p){l||(a.stroke=ty(h.borderColor,e.borderColor,i.color),a.fill=h.color||e.color,a.fill||(a.fill=i.color,a["fill-opacity"]=n??1),o.stroke=ty(h.connectorColor,e.connectorColor,i.color)),t[p].radius=this.getRangeRadius(h.value),t[p]=tb(t[p],{center:t[0].radius-t[p].radius+s}),l||tb(!0,t[p],{bubbleAttribs:tb(a),connectorAttribs:tb(o),labelAttribs:r})},this)}getRangeRadius(t){let e=this.options,i=this.options.seriesIndex,s=this.chart.series[i],a=e.ranges[0].value,o=e.ranges[e.ranges.length-1].value,r=e.minSize,n=e.maxSize;return s.getRadius.call(this,o,a,r,n,t)}render(){let t=this.legendItem||{},e=this.chart.renderer,i=this.options.zThreshold;for(let s of(this.symbols||(this.symbols={connectors:[],bubbleItems:[],labels:[]}),t.symbol=e.g("bubble-legend"),t.label=e.g("bubble-legend-item").css(this.legend.itemStyle||{}),t.symbol.translateX=0,t.symbol.translateY=0,t.symbol.add(t.label),t.label.add(t.group),this.ranges))s.value>=i&&this.renderRange(s);this.hideOverlappingLabels()}renderRange(t){let e=this.ranges[0],i=this.legend,s=this.options,a=s.labels,o=this.chart,r=o.series[s.seriesIndex],n=o.renderer,l=this.symbols,h=l.labels,p=t.center,d=Math.abs(t.radius),c=s.connectorDistance||0,u=a.align,g=i.options.rtl,f=s.borderWidth,m=s.connectorWidth,b=e.radius||0,y=p-d-f/2+m/2,x=(y%1?1:.5)-(m%2?0:.5),P=n.styledMode,v=g||"left"===u?-c:c;"center"===u&&(v=0,s.connectorDistance=0,t.labelAttribs.align="center"),l.bubbleItems.push(n.circle(b,p+x,d).attr(P?{}:t.bubbleAttribs).addClass((P?"highcharts-color-"+r.colorIndex+" ":"")+"highcharts-bubble-legend-symbol "+(s.className||"")).add(this.legendItem.symbol)),l.connectors.push(n.path(n.crispLine([["M",b,y],["L",b+v,y]],s.connectorWidth)).attr(P?{}:t.connectorAttribs).addClass((P?"highcharts-color-"+this.options.seriesIndex+" ":"")+"highcharts-bubble-legend-connectors "+(s.connectorClassName||"")).add(this.legendItem.symbol));let M=n.text(this.formatLabel(t)).attr(P?{}:t.labelAttribs).css(P?{}:a.style).addClass("highcharts-bubble-legend-labels "+(s.labels.className||"")).add(this.legendItem.symbol),L={x:b+v+s.labels.x,y:y+s.labels.y+.4*M.getBBox().height};M.attr(L),h.push(M),M.placed=!0,M.alignAttr=L}getMaxLabelSize(){let t,e;return this.symbols.labels.forEach(function(i){e=i.getBBox(!0),t=t?e.width>t.width?e:t:e}),t||{}}formatLabel(t){let e=this.options,i=e.labels.formatter,s=e.labels.format,{numberFormatter:a}=this.chart;return s?tc().format(s,t,this.chart):i?i.call(t):a(t.value,1)}hideOverlappingLabels(){let t=this.chart,e=this.options.labels.allowOverlap,i=this.symbols;!e&&i&&(t.hideOverlappingLabels(i.labels),i.labels.forEach(function(t,e){t.newOpacity?t.newOpacity!==t.oldOpacity&&i.connectors[e].show():i.connectors[e].hide()}))}getRanges(){let t=this.legend.bubbleLegend,e=t.chart.series,i=t.options.ranges,s,a,o=Number.MAX_VALUE,r=-Number.MAX_VALUE;return e.forEach(function(t){t.isBubble&&!t.ignoreSeries&&(a=t.getColumn("z").filter(tm)).length&&(o=ty(t.options.zMin,Math.min(o,Math.max(tf(a),!1===t.options.displayNegative?t.options.zThreshold:-Number.MAX_VALUE))),r=ty(t.options.zMax,Math.max(r,tg(a))))}),s=o===r?[{value:r}]:[{value:o},{value:(o+r)/2},{value:r,autoRanges:!0}],i.length&&i[0].radius&&s.reverse(),s.forEach(function(t,e){i&&i[e]&&(s[e]=tb(i[e],t))}),s}predictBubbleSizes(){let t=this.chart,e=t.legend.options,i=e.floating,s="horizontal"===e.layout,a=s?t.legend.lastLineHeight:0,o=t.plotSizeX,r=t.plotSizeY,n=t.series[this.options.seriesIndex],l=n.getPxExtremes(),h=Math.ceil(l.minPxSize),p=Math.ceil(l.maxPxSize),d=Math.min(r,o),c,u=n.options.maxSize;return i||!/%$/.test(u)?c=p:(c=(d+a)*(u=parseFloat(u))/100/(u/100+1),(s&&r-c>=o||!s&&o-c>=r)&&(c=p)),[h,Math.ceil(c)]}updateRanges(t,e){let i=this.legend.options.bubbleLegend;i.minSize=t,i.maxSize=e,i.ranges=this.getRanges()}correctSizes(){let t=this.legend,e=this.chart.series[this.options.seriesIndex].getPxExtremes();Math.abs(Math.ceil(e.maxPxSize)-this.options.maxSize)>1&&(this.updateRanges(this.options.minSize,e.maxPxSize),t.render())}},{setOptions:tv}=n(),{composed:tM}=n(),{addEvent:tL,objectEach:tk,pushUnique:tw,wrap:tA}=n();function tS(t,e,i){let s,a,o,r=this.legend,n=tN(this)>=0;r&&r.options.enabled&&r.bubbleLegend&&r.options.bubbleLegend.autoRanges&&n?(s=r.bubbleLegend.options,a=r.bubbleLegend.predictBubbleSizes(),r.bubbleLegend.updateRanges(a[0],a[1]),s.placed||(r.group.placed=!1,r.allItems.forEach(t=>{(o=t.legendItem||{}).group&&(o.group.translateY=void 0)})),r.render(),s.placed||(this.getMargins(),this.axes.forEach(t=>{t.setScale(),t.updateNames(),tk(t.ticks,function(t){t.isNew=!0,t.isNewLabel=!0})}),this.getMargins()),s.placed=!0,t.call(this,e,i),r.bubbleLegend.correctSizes(),tY(r,tT(r))):(t.call(this,e,i),r&&r.options.enabled&&r.bubbleLegend&&(r.render(),tY(r,tT(r))))}function tN(t){let e=t.series,i=0;for(;i<e.length;){if(e[i]&&e[i].isBubble&&e[i].visible&&e[i].dataTable.rowCount)return i;i++}return -1}function tT(t){let e=t.allItems,i=[],s=e.length,a,o,r,n=0,l=0;for(n=0;n<s;n++)if(o=e[n].legendItem||{},r=(e[n+1]||{}).legendItem||{},o.labelHeight&&(e[n].itemHeight=o.labelHeight),e[n]===e[s-1]||o.y!==r.y){for(i.push({height:0}),a=i[i.length-1];l<=n;l++)e[l].itemHeight>a.height&&(a.height=e[l].itemHeight);a.step=n}return i}function tC(t){let e=this.bubbleLegend,i=this.options,s=i.bubbleLegend,a=tN(this.chart);e&&e.ranges&&e.ranges.length&&(s.ranges.length&&(s.autoRanges=!!s.ranges[0].autoRanges),this.destroyItem(e)),a>=0&&i.enabled&&s.enabled&&(s.seriesIndex=a,this.bubbleLegend=new tP(s,this),this.bubbleLegend.addToLegend(t.allItems))}function tX(t){let e;if(t.defaultPrevented)return!1;let i=t.legendItem,s=this.chart,a=i.visible;this&&this.bubbleLegend&&(i.visible=!a,i.ignoreSeries=a,e=tN(s)>=0,this.bubbleLegend.visible!==e&&(this.update({bubbleLegend:{enabled:e}}),this.bubbleLegend.visible=e),i.visible=a)}function tY(t,e){let i=t.allItems,s=t.options.rtl,a,o,r,n,l=0;i.forEach((t,i)=>{(n=t.legendItem||{}).group&&(a=n.group.translateX||0,o=n.y||0,((r=t.movementX)||s&&t.ranges)&&(r=s?a-t.options.maxSize/2:a+r,n.group.attr({translateX:r})),i>e[l].step&&l++,n.group.attr({translateY:Math.round(o+e[l].height/2)}),n.y=o+e[l].height/2)})}let tI=function(t,e){tw(tM,"Series.BubbleLegend")&&(tv({legend:{bubbleLegend:tp}}),tA(t.prototype,"drawChartBox",tS),tL(e,"afterGetAllItems",tC),tL(e,"itemClick",tX))},tR=t.default.Point;var tE=o.n(tR);let{seriesTypes:{scatter:{prototype:{pointClass:tz}}}}=h(),{extend:tD}=n();class tO extends tz{haloPath(t){let e=(t&&this.marker&&this.marker.radius||0)+t;if(this.series.chart.inverted){let t=this.pos()||[0,0],{xAxis:i,yAxis:s,chart:a}=this.series;return a.renderer.symbols.circle(i.len-t[1]-e,s.len-t[0]-e,2*e,2*e)}return tE().prototype.haloPath.call(this,e)}}tD(tO.prototype,{ttBelow:!1});let{composed:tB,noop:tW}=n(),{series:tF,seriesTypes:{column:{prototype:tH},scatter:tG}}=h(),{addEvent:tq,arrayMax:tV,arrayMin:tU,clamp:tK,extend:t_,isNumber:tZ,merge:tj,pick:t$,pushUnique:tQ}=n();function tJ(){let t=this.len,{coll:e,isXAxis:i,min:s}=this,a=(this.max||0)-(s||0),o=0,r=t,n=t/a,l;("xAxis"===e||"yAxis"===e)&&(this.series.forEach(t=>{if(t.bubblePadding&&t.reserveSpace()){this.allowZoomOutside=!0,l=!0;let e=t.getColumn(i?"x":"y");if(i&&((t.onPoint||t).getRadii(0,0,t),t.onPoint&&(t.radii=t.onPoint.radii)),a>0){let i=e.length;for(;i--;)if(tZ(e[i])&&this.dataMin<=e[i]&&e[i]<=this.max){let a=t.radii&&t.radii[i]||0;o=Math.min((e[i]-s)*n-a,o),r=Math.max((e[i]-s)*n+a,r)}}}}),l&&a>0&&!this.logarithmic&&(r-=t,n*=(t+Math.max(0,o)-Math.min(r,t))/t,[["min","userMin",o],["max","userMax",r]].forEach(t=>{void 0===t$(this.options[t[0]],this[t[1]])&&(this[t[0]]+=t[2]/n)})))}function t0(){let{ticks:t,tickPositions:e,dataMin:i=0,dataMax:s=0,categories:a}=this,o=this.options.type;if((a?.length||"category"===o)&&this.series.find(t=>t.bubblePadding)){let a=e.length;for(;a--;){let o=t[e[a]],r=o.pos||0;(r>s||r<i)&&o.label?.hide()}}}class t1 extends tG{static compose(t,e,i){tI(e,i),tQ(tB,"Series.Bubble")&&(tq(t,"foundExtremes",tJ),tq(t,"afterRender",t0))}animate(t){!t&&this.points.length<this.options.animationLimit&&this.points.forEach(function(t){let{graphic:e,plotX:i=0,plotY:s=0}=t;e&&e.width&&(this.hasRendered||e.attr({x:i,y:s,width:1,height:1}),e.animate(this.markerAttribs(t),this.options.animation))},this)}getRadii(){let t=this.getColumn("z"),e=this.getColumn("y"),i=[],s,a,o,r=this.chart.bubbleZExtremes,{minPxSize:n,maxPxSize:l}=this.getPxExtremes();if(!r){let t,e=Number.MAX_VALUE,i=-Number.MAX_VALUE;this.chart.series.forEach(s=>{if(s.bubblePadding&&s.reserveSpace()){let a=(s.onPoint||s).getZExtremes();a&&(e=Math.min(t$(e,a.zMin),a.zMin),i=Math.max(t$(i,a.zMax),a.zMax),t=!0)}}),t?(r={zMin:e,zMax:i},this.chart.bubbleZExtremes=r):r={zMin:0,zMax:0}}for(a=0,s=t.length;a<s;a++)o=t[a],i.push(this.getRadius(r.zMin,r.zMax,n,l,o,e&&e[a]));this.radii=i}getRadius(t,e,i,s,a,o){let r=this.options,n="width"!==r.sizeBy,l=r.zThreshold,h=e-t,p=.5;if(null===o||null===a)return null;if(tZ(a)){if(r.sizeByAbsoluteValue&&(a=Math.abs(a-l),e=h=Math.max(e-l,Math.abs(t-l)),t=0),a<t)return i/2-1;h>0&&(p=(a-t)/h)}return n&&p>=0&&(p=Math.sqrt(p)),Math.ceil(i+p*(s-i))/2}hasData(){return!!this.dataTable.rowCount}markerAttribs(t,e){let i=super.markerAttribs(t,e),{height:s=0,width:a=0}=i;return this.chart.inverted?t_(i,{x:(t.plotX||0)-a/2,y:(t.plotY||0)-s/2}):i}pointAttribs(t,e){let i=this.options.marker,s=i?.fillOpacity,a=tF.prototype.pointAttribs.call(this,t,e);return a["fill-opacity"]=s??1,a}translate(){super.translate.call(this),this.getRadii(),this.translateBubble()}translateBubble(){let{data:t,options:e,radii:i}=this,{minPxSize:s}=this.getPxExtremes(),a=t.length;for(;a--;){let o=t[a],r=i?i[a]:0;"z"===this.zoneAxis&&(o.negative=(o.z||0)<(e.zThreshold||0)),tZ(r)&&r>=s/2?(o.marker=t_(o.marker,{radius:r,width:2*r,height:2*r}),o.dlBox={x:o.plotX-r,y:o.plotY-r,width:2*r,height:2*r}):(o.shapeArgs=o.plotY=o.dlBox=void 0,o.isInside=!1)}}getPxExtremes(){let t=Math.min(this.chart.plotWidth,this.chart.plotHeight),e=e=>{let i;return"string"==typeof e&&(i=/%$/.test(e),e=parseInt(e,10)),i?t*e/100:e},i=e(t$(this.options.minSize,8)),s=Math.max(e(t$(this.options.maxSize,"20%")),i);return{minPxSize:i,maxPxSize:s}}getZExtremes(){let t=this.options,e=this.getColumn("z").filter(tZ);if(e.length){let i=t$(t.zMin,tK(tU(e),!1===t.displayNegative?t.zThreshold||0:-Number.MAX_VALUE,Number.MAX_VALUE)),s=t$(t.zMax,tV(e));if(tZ(i)&&tZ(s))return{zMin:i,zMax:s}}}searchKDTree(t,e,i,s=tW,a=tW){return s=(t,e,i)=>{let s=t[i]||0,a=e[i]||0,o,r=!1;return s===a?o=t.index>e.index?t:e:s<0&&a<0?(o=s-(t.marker?.radius||0)>=a-(e.marker?.radius||0)?t:e,r=!0):o=s<a?t:e,[o,r]},a=(t,e,i)=>!i&&t>e||t<e,super.searchKDTree(t,e,i,s,a)}}t1.defaultOptions=tj(tG.defaultOptions,{dataLabels:{formatter:function(){let{numberFormatter:t}=this.series.chart,{z:e}=this.point;return tZ(e)?t(e,-1):""},inside:!0,verticalAlign:"middle"},animationLimit:250,marker:{lineColor:null,lineWidth:1,fillOpacity:.5,radius:null,states:{hover:{radiusPlus:0}},symbol:"circle"},minSize:8,maxSize:"20%",softThreshold:!1,states:{hover:{halo:{size:5}}},tooltip:{pointFormat:"({point.x}, {point.y}), Size: {point.z}"},turboThreshold:0,zThreshold:0,zoneAxis:"z"}),t_(t1.prototype,{alignDataLabel:tH.alignDataLabel,applyZones:tW,bubblePadding:!0,isBubble:!0,keysAffectYAxis:["y"],pointArrayMap:["y","z"],pointClass:tO,parallelArrays:["x","y","z"],trackerGroups:["group","dataLabelsGroup"],specialGroup:"group",zoneAxis:"z"}),tq(t1,"updatedData",t=>{delete t.target.chart.bubbleZExtremes}),tq(t1,"remove",t=>{delete t.target.chart.bubbleZExtremes}),h().registerSeriesType("bubble",t1);let{seriesTypes:{column:{prototype:{pointClass:{prototype:t2}}},arearange:{prototype:{pointClass:t3}}}}=h(),{extend:t5,isNumber:t8}=n();class t6 extends t3{isValid(){return t8(this.low)}}t5(t6.prototype,{setState:t2.setState});let{noop:t9}=n(),{seriesTypes:{arearange:t4,column:t7,column:{prototype:et}}}=h(),{addEvent:ee,clamp:ei,extend:es,isNumber:ea,merge:eo,pick:er}=n();class en extends t4{setOptions(){return eo(!0,arguments[0],{stacking:void 0}),t4.prototype.setOptions.apply(this,arguments)}translate(){return et.translate.apply(this)}pointAttribs(){return et.pointAttribs.apply(this,arguments)}translate3dPoints(){return et.translate3dPoints.apply(this,arguments)}translate3dShapes(){return et.translate3dShapes.apply(this,arguments)}afterColumnTranslate(){let t,e,i,s,a=this.yAxis,o=this.xAxis,r=o.startAngleRad,n=this.chart,l=this.xAxis.isRadial,h=Math.max(n.chartWidth,n.chartHeight)+999;this.points.forEach(p=>{let d=p.shapeArgs||{},c=this.options.minPointLength,u=p.plotY,g=a.translate(p.high,0,1,0,1);if(ea(g)&&ea(u)){if(p.plotHigh=ei(g,-h,h),p.plotLow=ei(u,-h,h),s=p.plotHigh,Math.abs(t=er(p.rectPlotY,p.plotY)-p.plotHigh)<c?(e=c-t,t+=e,s-=e/2):t<0&&(t*=-1,s-=t),l&&this.polar)i=p.barX+r,p.shapeType="arc",p.shapeArgs=this.polar.arc(s+t,s,i,i+p.pointWidth);else{d.height=t,d.y=s;let{x:e=0,width:i=0}=d;p.shapeArgs=eo(p.shapeArgs,this.crispCol(e,s,i,t)),p.tooltipPos=n.inverted?[a.len+a.pos-n.plotLeft-s-t/2,o.len+o.pos-n.plotTop-e-i/2,t]:[o.left-n.plotLeft+e+i/2,a.pos-n.plotTop+s+t/2,t]}}})}}en.defaultOptions=eo(t7.defaultOptions,t4.defaultOptions,{borderRadius:{where:"all"},pointRange:null,legendSymbol:"rectangle",marker:null,states:{hover:{halo:!1}}}),ee(en,"afterColumnTranslate",function(){en.prototype.afterColumnTranslate.apply(this)},{order:5}),es(en.prototype,{directTouch:!0,pointClass:t6,trackerGroups:["group","dataLabelsGroup"],adjustForMissingColumns:et.adjustForMissingColumns,animate:et.animate,crispCol:et.crispCol,drawGraph:t9,drawPoints:et.drawPoints,getSymbol:t9,drawTracker:et.drawTracker,getColumnMetrics:et.getColumnMetrics}),h().registerSeriesType("columnrange",en);let{column:el}=h().seriesTypes,{clamp:eh,merge:ep,pick:ed}=n();class ec extends el{translate(){let t=this.chart,e=this.options,i=this.dense=this.closestPointRange*this.xAxis.transA<2,s=this.borderWidth=ed(e.borderWidth,+!i),a=this.yAxis,o=e.threshold,r=ed(e.minPointLength,5),n=this.getColumnMetrics(),l=n.width,h=this.pointXOffset=n.offset,p=this.translatedThreshold=a.getThreshold(o),d=this.barW=Math.max(l,1+2*s);for(let i of(t.inverted&&(p-=.5),e.pointPadding&&(d=Math.ceil(d)),super.translate(),this.points)){let s=ed(i.yBottom,p),c=999+Math.abs(s),u=eh(i.plotY,-c,a.len+c),g=d/2,f=Math.min(u,s),m=Math.max(u,s)-f,b=i.plotX+h,y,x,P,v,M,L,k,w,A,S,N;e.centerInCategory&&(b=this.adjustForMissingColumns(b,l,i,n)),i.barX=b,i.pointWidth=l,i.tooltipPos=t.inverted?[a.len+a.pos-t.plotLeft-u,this.xAxis.len-b-g,m]:[b+g,u+a.pos-t.plotTop,m],y=o+(i.total||i.y),"percent"===e.stacking&&(y=o+(i.y<0)?-100:100);let T=a.toPixels(y,!0);P=(x=t.plotHeight-T-(t.plotHeight-p))?g*(f-T)/x:0,v=x?g*(f+m-T)/x:0,L=b-P+g,k=b+P+g,w=b+v+g,A=b-v+g,S=f-r,N=f+m,i.y<0&&(S=f,N=f+m+r),t.inverted&&(M=a.width-f,x=T-(a.width-p),P=g*(T-M)/x,v=g*(T-(M-m))/x,k=(L=b+g+P)-2*P,w=b-v+g,A=b+v+g,S=f,N=f+m-r,i.y<0&&(N=f+m+r)),i.shapeType="path",i.shapeArgs={x:L,y:S,width:k-L,height:m,d:[["M",L,S],["L",k,S],["L",w,N],["L",A,N],["Z"]]}}}}ec.defaultOptions=ep(el.defaultOptions,{}),h().registerSeriesType("columnpyramid",ec);let{arearange:eu}=h().seriesTypes,{addEvent:eg,merge:ef,extend:em}=n();class eb extends th{getColumnMetrics(){return this.linkedParent&&this.linkedParent.columnMetrics||te().prototype.getColumnMetrics.call(this)}drawDataLabels(){let t=this.pointValKey;if(eu)for(let e of(eu.prototype.drawDataLabels.call(this),this.points))e.y=e[t]}toYData(t){return[t.low,t.high]}}eb.defaultOptions=ef(th.defaultOptions,{color:"#000000",grouping:!1,linkedTo:":previous",tooltip:{pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.low}</b> - <b>{point.high}</b><br/>'},whiskerWidth:null}),eg(eb,"afterTranslate",function(){for(let t of this.points)t.plotLow=t.plotY},{order:0}),em(eb.prototype,{pointArrayMap:["low","high"],pointValKey:"high",doQuartiles:!1}),h().registerSeriesType("errorbar",eb);let{series:{prototype:{pointClass:ey}}}=h(),{noop:ex}=n(),{series:eP,seriesTypes:{column:ev}}=h(),{clamp:eM,isNumber:eL,extend:ek,merge:ew,pick:eA,pInt:eS,defined:eN}=n();class eT extends eP{translate(){let t=this.yAxis,e=this.options,i=t.center;this.generatePoints(),this.points.forEach(s=>{let a=ew(e.dial,s.dial),o=eS(a.radius)*i[2]/200,r=eS(a.baseLength)*o/100,n=eS(a.rearLength)*o/100,l=a.baseWidth,h=a.topWidth,p=e.overshoot,d=t.startAngleRad+t.translate(s.y,void 0,void 0,void 0,!0);(eL(p)||!1===e.wrap)&&(p=eL(p)?p/180*Math.PI:0,d=eM(d,t.startAngleRad-p,t.endAngleRad+p)),d=180*d/Math.PI,s.shapeType="path",s.shapeArgs={d:a.path||[["M",-n,-l/2],["L",r,-l/2],["L",o,-h/2],["L",o,h/2],["L",r,l/2],["L",-n,l/2],["Z"]],translateX:i[0],translateY:i[1],rotation:d},s.plotX=i[0],s.plotY=i[1],eN(s.y)&&t.max-t.min&&(s.percentage=(s.y-t.min)/(t.max-t.min)*100)})}drawPoints(){let t=this,e=t.chart,i=t.yAxis.center,s=t.pivot,a=t.options,o=a.pivot,r=e.renderer;t.points.forEach(i=>{let s=i.graphic,o=i.shapeArgs,n=o.d,l=ew(a.dial,i.dial);s?(s.animate(o),o.d=n):i.graphic=r[i.shapeType](o).addClass("highcharts-dial").add(t.group),e.styledMode||i.graphic[s?"animate":"attr"]({stroke:l.borderColor,"stroke-width":l.borderWidth,fill:l.backgroundColor})}),s?s.animate({translateX:i[0],translateY:i[1]}):o&&(t.pivot=r.circle(0,0,o.radius).attr({zIndex:2}).addClass("highcharts-pivot").translate(i[0],i[1]).add(t.group),e.styledMode||t.pivot.attr({fill:o.backgroundColor,stroke:o.borderColor,"stroke-width":o.borderWidth}))}animate(t){let e=this;t||e.points.forEach(t=>{let i=t.graphic;i&&(i.attr({rotation:180*e.yAxis.startAngleRad/Math.PI}),i.animate({rotation:t.shapeArgs.rotation},e.options.animation))})}render(){this.group=this.plotGroup("group","series",this.visible?"inherit":"hidden",this.options.zIndex,this.chart.seriesGroup),eP.prototype.render.call(this),this.group.clip(this.chart.clipRect)}setData(t,e){eP.prototype.setData.call(this,t,!1),this.processData(),this.generatePoints(),eA(e,!0)&&this.chart.redraw()}hasData(){return!!this.points.length}}eT.defaultOptions=ew(eP.defaultOptions,{dataLabels:{borderColor:"#cccccc",borderRadius:3,borderWidth:1,crop:!1,defer:!1,enabled:!0,verticalAlign:"top",y:15,zIndex:2},dial:{backgroundColor:"#000000",baseLength:"70%",baseWidth:3,borderColor:"#cccccc",borderWidth:0,radius:"80%",rearLength:"10%",topWidth:1},pivot:{radius:5,borderWidth:0,borderColor:"#cccccc",backgroundColor:"#000000"},tooltip:{headerFormat:""},showInLegend:!1}),ek(eT.prototype,{angular:!0,directTouch:!0,drawGraph:ex,drawTracker:ev.prototype.drawTracker,fixedBox:!0,forceDL:!0,noSharedTooltip:!0,pointClass:class extends ey{setState(t){this.state=t}},trackerGroups:["group","dataLabelsGroup"]}),h().registerSeriesType("gauge",eT);let eC=t.default.Color;var eX=o.n(eC);let{composed:eY}=n(),{addEvent:eI,pushUnique:eR}=n();function eE(){let t,e,i,s,a=this;a.container&&(t=eI(a.container,"mousedown",t=>{e&&e(),i&&i(),(s=a.hoverPoint)&&s.series&&s.series.hasDraggableNodes&&s.series.options.draggable&&(s.series.onMouseDown(s,t),e=eI(a.container,"mousemove",t=>s&&s.series&&s.series.onMouseMove(s,t)),i=eI(a.container.ownerDocument,"mouseup",t=>(e(),i(),s&&s.series&&s.series.onMouseUp(s,t))))})),eI(a,"destroy",function(){t()})}let ez={compose:function(t){eR(eY,"DragNodes")&&eI(t,"load",eE)},onMouseDown:function(t,e){let i=this.chart.pointer?.normalize(e)||e;t.fixedPosition={chartX:i.chartX,chartY:i.chartY,plotX:t.plotX,plotY:t.plotY},t.inDragMode=!0},onMouseMove:function(t,e){if(t.fixedPosition&&t.inDragMode){let i,s,a=this.chart,o=a.pointer?.normalize(e)||e,r=t.fixedPosition.chartX-o.chartX,n=t.fixedPosition.chartY-o.chartY,l=a.graphLayoutsLookup;(Math.abs(r)>5||Math.abs(n)>5)&&(i=t.fixedPosition.plotX-r,s=t.fixedPosition.plotY-n,a.isInsidePlot(i,s)&&(t.plotX=i,t.plotY=s,t.hasDragged=!0,this.redrawHalo(t),l.forEach(t=>{t.restartSimulation()})))}},onMouseUp:function(t){t.fixedPosition&&(t.hasDragged&&(this.layout.enableSimulation?this.layout.start():this.chart.redraw()),t.inDragMode=t.hasDragged=!1,this.options.fixedDraggable||delete t.fixedPosition)},redrawHalo:function(t){t&&this.halo&&this.halo.attr({d:t.haloPath(this.options.states.hover.halo.size)})}},{setAnimation:eD}=n(),{composed:eO}=n(),{addEvent:eB,pushUnique:eW}=n();function eF(){this.graphLayoutsLookup&&(this.graphLayoutsLookup.forEach(t=>{t.updateSimulation()}),this.redraw())}function eH(){this.graphLayoutsLookup&&(this.graphLayoutsLookup.forEach(t=>{t.updateSimulation(!1)}),this.redraw())}function eG(){this.graphLayoutsLookup&&this.graphLayoutsLookup.forEach(t=>{t.stop()})}function eq(){let t,e=!1,i=i=>{i.maxIterations--&&isFinite(i.temperature)&&!i.isStable()&&!i.enableSimulation&&(i.beforeStep&&i.beforeStep(),i.step(),t=!1,e=!0)};if(this.graphLayoutsLookup){for(eD(!1,this),this.graphLayoutsLookup.forEach(t=>t.start());!t;)t=!0,this.graphLayoutsLookup.forEach(i);e&&this.series.forEach(t=>{t&&t.layout&&t.render()})}}let eV={compose:function(t){eW(eO,"GraphLayout")&&(eB(t,"afterPrint",eF),eB(t,"beforePrint",eH),eB(t,"predraw",eG),eB(t,"render",eq))},integrations:{},layouts:{}},eU=t.default.Chart;var eK=o.n(eU);let{seriesTypes:{bubble:{prototype:{pointClass:e_}}}}=h(),eZ=class extends e_{destroy(){return this.series?.layout&&this.series.layout.removeElementFromCollection(this,this.series.layout.nodes),tE().prototype.destroy.apply(this,arguments)}firePointEvent(){let t=this.series.options;if(this.isParentNode&&t.parentNode){let e=t.allowPointSelect;t.allowPointSelect=t.parentNode.allowPointSelect,tE().prototype.firePointEvent.apply(this,arguments),t.allowPointSelect=e}else tE().prototype.firePointEvent.apply(this,arguments)}select(){let t=this.series.chart;this.isParentNode?(t.getSelectedPoints=t.getSelectedParentNodes,tE().prototype.select.apply(this,arguments),t.getSelectedPoints=eK().prototype.getSelectedPoints):tE().prototype.select.apply(this,arguments)}},{isNumber:ej}=n(),e$={attractive:function(t,e,i){let s=t.getMass(),a=-i.x*e*this.diffTemperature,o=-i.y*e*this.diffTemperature;t.fromNode.fixedPosition||(t.fromNode.plotX-=a*s.fromNode/t.fromNode.degree,t.fromNode.plotY-=o*s.fromNode/t.fromNode.degree),t.toNode.fixedPosition||(t.toNode.plotX+=a*s.toNode/t.toNode.degree,t.toNode.plotY+=o*s.toNode/t.toNode.degree)},attractiveForceFunction:function(t,e){return(e-t)/t},barycenter:function(){let t=this.options.gravitationalConstant||0,e=(this.barycenter.xFactor-(this.box.left+this.box.width)/2)*t,i=(this.barycenter.yFactor-(this.box.top+this.box.height)/2)*t;this.nodes.forEach(function(t){t.fixedPosition||(t.plotX-=e/t.mass/t.degree,t.plotY-=i/t.mass/t.degree)})},getK:function(t){return Math.pow(t.box.width*t.box.height/t.nodes.length,.5)},integrate:function(t,e){let i=-t.options.friction,s=t.options.maxSpeed,a=e.prevX,o=e.prevY,r=(e.plotX+e.dispX-a)*i,n=(e.plotY+e.dispY-o)*i,l=Math.abs,h=l(r)/(r||1),p=l(n)/(n||1),d=h*Math.min(s,Math.abs(r)),c=p*Math.min(s,Math.abs(n));e.prevX=e.plotX+e.dispX,e.prevY=e.plotY+e.dispY,e.plotX+=d,e.plotY+=c,e.temperature=t.vectorLength({x:d,y:c})},repulsive:function(t,e,i){let s=e*this.diffTemperature/t.mass/t.degree;t.fixedPosition||(t.plotX+=i.x*s,t.plotY+=i.y*s)},repulsiveForceFunction:function(t,e){return(e-t)/t*+(e>t)}},{noop:eQ}=n(),eJ={barycenter:function(){let t,e,i=this.options.gravitationalConstant||0,s=this.box,a=this.nodes,o=Math.sqrt(a.length);for(let r of a)if(!r.fixedPosition){let a=r.mass*o,n=r.plotX||0,l=r.plotY||0,h=r.series,p=h.parentNode;this.resolveSplitSeries(r)&&p&&!r.isParentNode?(t=p.plotX||0,e=p.plotY||0):(t=s.width/2,e=s.height/2),r.plotX=n-(n-t)*i/a,r.plotY=l-(l-e)*i/a,h.chart.hoverPoint===r&&h.redrawHalo&&h.halo&&h.redrawHalo(r)}},getK:eQ,integrate:e$.integrate,repulsive:function(t,e,i,s){let a=e*this.diffTemperature/t.mass/t.degree,o=i.x*a,r=i.y*a;t.fixedPosition||(t.plotX+=o,t.plotY+=r),s.fixedPosition||(s.plotX-=o,s.plotY-=r)},repulsiveForceFunction:function(t,e,i,s){return Math.min(t,(i.marker.radius+s.marker.radius)/2)}},e0={attractive:function(t,e,i,s){let a=t.getMass(),o=i.x/s*e,r=i.y/s*e;t.fromNode.fixedPosition||(t.fromNode.dispX-=o*a.fromNode/t.fromNode.degree,t.fromNode.dispY-=r*a.fromNode/t.fromNode.degree),t.toNode.fixedPosition||(t.toNode.dispX+=o*a.toNode/t.toNode.degree,t.toNode.dispY+=r*a.toNode/t.toNode.degree)},attractiveForceFunction:function(t,e){return t*t/e},barycenter:function(){let t=this.options.gravitationalConstant,e=this.barycenter.xFactor,i=this.barycenter.yFactor;this.nodes.forEach(function(s){if(!s.fixedPosition){let a=s.getDegree(),o=a*(1+a/2);s.dispX+=(e-s.plotX)*t*o/s.degree,s.dispY+=(i-s.plotY)*t*o/s.degree}})},getK:function(t){return Math.pow(t.box.width*t.box.height/t.nodes.length,.3)},integrate:function(t,e){e.dispX+=e.dispX*t.options.friction,e.dispY+=e.dispY*t.options.friction;let i=e.temperature=t.vectorLength({x:e.dispX,y:e.dispY});0!==i&&(e.plotX+=e.dispX/i*Math.min(Math.abs(e.dispX),t.temperature),e.plotY+=e.dispY/i*Math.min(Math.abs(e.dispY),t.temperature))},repulsive:function(t,e,i,s){t.dispX+=i.x/s*e/t.degree,t.dispY+=i.y/s*e/t.degree},repulsiveForceFunction:function(t,e){return e*e/t}};class e1{constructor(t){this.body=!1,this.isEmpty=!1,this.isInternal=!1,this.nodes=[],this.box=t,this.boxSize=Math.min(t.width,t.height)}divideBox(){let t=this.box.width/2,e=this.box.height/2;this.nodes[0]=new e1({left:this.box.left,top:this.box.top,width:t,height:e}),this.nodes[1]=new e1({left:this.box.left+t,top:this.box.top,width:t,height:e}),this.nodes[2]=new e1({left:this.box.left+t,top:this.box.top+e,width:t,height:e}),this.nodes[3]=new e1({left:this.box.left,top:this.box.top+e,width:t,height:e})}getBoxPosition(t){let e=t.plotX<this.box.left+this.box.width/2,i=t.plotY<this.box.top+this.box.height/2;return e?3*!i:i?1:2}insert(t,e){let i;this.isInternal?this.nodes[this.getBoxPosition(t)].insert(t,e-1):(this.isEmpty=!1,this.body?e?(this.isInternal=!0,this.divideBox(),!0!==this.body&&(this.nodes[this.getBoxPosition(this.body)].insert(this.body,e-1),this.body=!0),this.nodes[this.getBoxPosition(t)].insert(t,e-1)):((i=new e1({top:t.plotX||NaN,left:t.plotY||NaN,width:.1,height:.1})).body=t,i.isInternal=!1,this.nodes.push(i)):(this.isInternal=!1,this.body=t))}updateMassAndCenter(){let t=0,e=0,i=0;if(this.isInternal){for(let s of this.nodes)s.isEmpty||(t+=s.mass,e+=s.plotX*s.mass,i+=s.plotY*s.mass);e/=t,i/=t}else this.body&&(t=this.body.mass,e=this.body.plotX,i=this.body.plotY);this.mass=t,this.plotX=e,this.plotY=i}}let e2=class{constructor(t,e,i,s){this.box={left:t,top:e,width:i,height:s},this.maxDepth=25,this.root=new e1(this.box),this.root.isInternal=!0,this.root.isRoot=!0,this.root.divideBox()}calculateMassAndCenter(){this.visitNodeRecursive(null,null,function(t){t.updateMassAndCenter()})}insertNodes(t){for(let e of t)this.root.insert(e,this.maxDepth)}visitNodeRecursive(t,e,i){let s;if(t||(t=this.root),t===this.root&&e&&(s=e(t)),!1!==s){for(let a of t.nodes){if(a.isInternal){if(e&&(s=e(a)),!1===s)continue;this.visitNodeRecursive(a,e,i)}else a.body&&e&&e(a.body);i&&i(a)}t===this.root&&i&&i(t)}}},{win:e3}=n(),{clamp:e5,defined:e8,isFunction:e6,fireEvent:e9,pick:e4}=n();class e7{constructor(){this.box={},this.currentStep=0,this.initialRendering=!0,this.links=[],this.nodes=[],this.series=[],this.simulation=!1}static compose(t){eV.compose(t),eV.integrations.euler=e0,eV.integrations.verlet=e$,eV.layouts["reingold-fruchterman"]=e7}init(t){this.options=t,this.nodes=[],this.links=[],this.series=[],this.box={x:0,y:0,width:0,height:0},this.setInitialRendering(!0),this.integration=eV.integrations[t.integration],this.enableSimulation=t.enableSimulation,this.attractiveForce=e4(t.attractiveForce,this.integration.attractiveForceFunction),this.repulsiveForce=e4(t.repulsiveForce,this.integration.repulsiveForceFunction),this.approximation=t.approximation}updateSimulation(t){this.enableSimulation=e4(t,this.options.enableSimulation)}start(){let t=this.series,e=this.options;this.currentStep=0,this.forces=t[0]&&t[0].forces||[],this.chart=t[0]&&t[0].chart,this.initialRendering&&(this.initPositions(),t.forEach(function(t){t.finishedAnimating=!0,t.render()})),this.setK(),this.resetSimulation(e),this.enableSimulation&&this.step()}step(){let t=this.series;for(let t of(this.currentStep++,"barnes-hut"===this.approximation&&(this.createQuadTree(),this.quadTree.calculateMassAndCenter()),this.forces||[]))this[t+"Forces"](this.temperature);if(this.applyLimits(),this.temperature=this.coolDown(this.startTemperature,this.diffTemperature,this.currentStep),this.prevSystemTemperature=this.systemTemperature,this.systemTemperature=this.getSystemTemperature(),this.enableSimulation){for(let e of t)e.chart&&e.render();this.maxIterations--&&isFinite(this.temperature)&&!this.isStable()?(this.simulation&&e3.cancelAnimationFrame(this.simulation),this.simulation=e3.requestAnimationFrame(()=>this.step())):(this.simulation=!1,this.series.forEach(t=>{e9(t,"afterSimulation")}))}}stop(){this.simulation&&e3.cancelAnimationFrame(this.simulation)}setArea(t,e,i,s){this.box={left:t,top:e,width:i,height:s}}setK(){this.k=this.options.linkLength||this.integration.getK(this)}addElementsToCollection(t,e){for(let i of t)-1===e.indexOf(i)&&e.push(i)}removeElementFromCollection(t,e){let i=e.indexOf(t);-1!==i&&e.splice(i,1)}clear(){this.nodes.length=0,this.links.length=0,this.series.length=0,this.resetSimulation()}resetSimulation(){this.forcedStop=!1,this.systemTemperature=0,this.setMaxIterations(),this.setTemperature(),this.setDiffTemperature()}restartSimulation(){this.simulation?this.resetSimulation():(this.setInitialRendering(!1),this.enableSimulation?this.start():this.setMaxIterations(1),this.chart&&this.chart.redraw(),this.setInitialRendering(!0))}setMaxIterations(t){this.maxIterations=e4(t,this.options.maxIterations)}setTemperature(){this.temperature=this.startTemperature=Math.sqrt(this.nodes.length)}setDiffTemperature(){this.diffTemperature=this.startTemperature/(this.options.maxIterations+1)}setInitialRendering(t){this.initialRendering=t}createQuadTree(){this.quadTree=new e2(this.box.left,this.box.top,this.box.width,this.box.height),this.quadTree.insertNodes(this.nodes)}initPositions(){let t=this.options.initialPositions;if(e6(t))for(let e of(t.call(this),this.nodes))e8(e.prevX)||(e.prevX=e.plotX),e8(e.prevY)||(e.prevY=e.plotY),e.dispX=0,e.dispY=0;else"circle"===t?this.setCircularPositions():this.setRandomPositions()}setCircularPositions(){let t,e=this.box,i=this.nodes,s=2*Math.PI/(i.length+1),a=i.filter(function(t){return 0===t.linksTo.length}),o={},r=this.options.initialPositionRadius,n=t=>{for(let e of t.linksFrom||[])o[e.toNode.id]||(o[e.toNode.id]=!0,l.push(e.toNode),n(e.toNode))},l=[];for(let t of a)l.push(t),n(t);if(l.length)for(let t of i)-1===l.indexOf(t)&&l.push(t);else l=i;for(let i=0,a=l.length;i<a;++i)(t=l[i]).plotX=t.prevX=e4(t.plotX,e.width/2+r*Math.cos(i*s)),t.plotY=t.prevY=e4(t.plotY,e.height/2+r*Math.sin(i*s)),t.dispX=0,t.dispY=0}setRandomPositions(){let t,e=this.box,i=this.nodes,s=i.length+1,a=t=>{let e=t*t/Math.PI;return e-Math.floor(e)};for(let o=0,r=i.length;o<r;++o)(t=i[o]).plotX=t.prevX=e4(t.plotX,e.width*a(o)),t.plotY=t.prevY=e4(t.plotY,e.height*a(s+o)),t.dispX=0,t.dispY=0}force(t,...e){this.integration[t].apply(this,e)}barycenterForces(){this.getBarycenter(),this.force("barycenter")}getBarycenter(){let t=0,e=0,i=0;for(let s of this.nodes)e+=s.plotX*s.mass,i+=s.plotY*s.mass,t+=s.mass;return this.barycenter={x:e,y:i,xFactor:e/t,yFactor:i/t},this.barycenter}barnesHutApproximation(t,e){let i,s,a=this.getDistXY(t,e),o=this.vectorLength(a);return t!==e&&0!==o&&(e.isInternal?e.boxSize/o<this.options.theta&&0!==o?(s=this.repulsiveForce(o,this.k),this.force("repulsive",t,s*e.mass,a,o),i=!1):i=!0:(s=this.repulsiveForce(o,this.k),this.force("repulsive",t,s*e.mass,a,o))),i}repulsiveForces(){if("barnes-hut"===this.approximation)for(let t of this.nodes)this.quadTree.visitNodeRecursive(null,e=>this.barnesHutApproximation(t,e));else{let t,e,i;for(let s of this.nodes)for(let a of this.nodes)s===a||s.fixedPosition||(i=this.getDistXY(s,a),0!==(e=this.vectorLength(i))&&(t=this.repulsiveForce(e,this.k),this.force("repulsive",s,t*a.mass,i,e)))}}attractiveForces(){let t,e,i;for(let s of this.links)s.fromNode&&s.toNode&&(t=this.getDistXY(s.fromNode,s.toNode),0!==(e=this.vectorLength(t))&&(i=this.attractiveForce(e,this.k),this.force("attractive",s,i,t,e)))}applyLimits(){for(let t of this.nodes)!t.fixedPosition&&(this.integration.integrate(this,t),this.applyLimitBox(t,this.box),t.dispX=0,t.dispY=0)}applyLimitBox(t,e){let i=t.radius;t.plotX=e5(t.plotX,e.left+i,e.width-i),t.plotY=e5(t.plotY,e.top+i,e.height-i)}coolDown(t,e,i){return t-e*i}isStable(){return 1e-5>Math.abs(this.systemTemperature-this.prevSystemTemperature)||this.temperature<=0}getSystemTemperature(){let t=0;for(let e of this.nodes)t+=e.temperature;return t}vectorLength(t){return Math.sqrt(t.x*t.x+t.y*t.y)}getDistR(t,e){let i=this.getDistXY(t,e);return this.vectorLength(i)}getDistXY(t,e){let i=t.plotX-e.plotX,s=t.plotY-e.plotY;return{x:i,y:s,absX:Math.abs(i),absY:Math.abs(s)}}}let it=e7,{addEvent:ie,defined:ii,pick:is}=n();function ia(){let t=this.series,e=[];return t.forEach(t=>{t.parentNode&&t.parentNode.selected&&e.push(t.parentNode)}),e}function io(){this.allDataPoints&&delete this.allDataPoints}class ir extends it{constructor(){super(...arguments),this.index=NaN,this.nodes=[],this.series=[]}static compose(t){it.compose(t),eV.integrations.packedbubble=eJ,eV.layouts.packedbubble=ir;let e=t.prototype;e.getSelectedParentNodes||(ie(t,"beforeRedraw",io),e.getSelectedParentNodes=ia),e.allParentNodes||(e.allParentNodes=[])}beforeStep(){this.options.marker&&this.series.forEach(t=>{t&&t.calculateParentRadius()})}isStable(){let t=Math.abs(this.prevSystemTemperature-this.systemTemperature);return 1>Math.abs(10*this.systemTemperature/Math.sqrt(this.nodes.length))&&t<1e-5||this.temperature<=0}setCircularPositions(){let t=this.box,e=[...this.nodes,...this?.chart?.allParentNodes||[]],i=2*Math.PI/(e.length+1),s=this.options.initialPositionRadius,a,o,r=0;for(let n of e)this.resolveSplitSeries(n)&&!n.isParentNode?(a=n.series.parentNode.plotX,o=n.series.parentNode.plotY):(a=t.width/2,o=t.height/2),n.plotX=n.prevX=is(n.plotX,a+s*Math.cos(n.index||r*i)),n.plotY=n.prevY=is(n.plotY,o+s*Math.sin(n.index||r*i)),n.dispX=0,n.dispY=0,r++}repulsiveForces(){let{options:t,k:e}=this,{bubblePadding:i=0,seriesInteraction:s}=t,a=[...this.nodes,...this?.chart?.allParentNodes||[]];for(let t of a){let o=t.series,r=t.fixedPosition,n=(t.marker?.radius||0)+i;for(let i of(t.degree=t.mass,t.neighbours=0,a)){let a=i.series;if(t!==i&&!r&&(s||o===a)&&!(o===a&&(i.isParentNode||t.isParentNode))){let s,a=this.getDistXY(t,i),o=this.vectorLength(a)-(n+(i.marker?.radius||0));o<0&&(t.degree+=.01,s=this.repulsiveForce(-o/Math.sqrt(++t.neighbours),e,t,i)*i.mass),this.force("repulsive",t,s||0,a,i,o)}}}}resolveSplitSeries(t){let e=t.series?.options?.layoutAlgorithm?.splitSeries;return!ii(e)&&t.series.chart?.options?.plotOptions?.packedbubble?.layoutAlgorithm?.splitSeries||e||!1}applyLimitBox(t,e){let i,s;this.resolveSplitSeries(t)&&!t.isParentNode&&this.options.parentNodeLimit&&(i=this.getDistXY(t,t.series.parentNode),(s=t.series.parentNodeRadius-t.marker.radius-this.vectorLength(i))<0&&s>-2*t.marker.radius&&(t.plotX-=.01*i.x,t.plotY-=.01*i.y)),super.applyLimitBox(t,e)}}eV.layouts.packedbubble=ir;let{merge:il,syncTimeout:ih}=n(),{animObject:ip}=n(),id=t.default.SVGElement;var ic=o.n(id);let{deg2rad:iu}=n(),{addEvent:ig,merge:im,uniqueKey:ib,defined:iy,extend:ix}=n();function iP(t,e){e=im(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let i=this.renderer.url,s=this.text||this,a=s.textPath,{attributes:o,enabled:r}=e;if(t=t||a&&a.path,a&&a.undo(),t&&r){let e=ig(s,"afterModifyTree",e=>{if(t&&r){let a=t.attr("id");a||t.attr("id",a=ib());let r={x:0,y:0};iy(o.dx)&&(r.dx=o.dx,delete o.dx),iy(o.dy)&&(r.dy=o.dy,delete o.dy),s.attr(r),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let n=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:ix(o,{"text-anchor":o.textAnchor,href:`${i}#${a}`}),children:n}}});s.textPath={path:t,undo:e}}else s.attr({dx:0,dy:0}),delete s.textPath;return this.added&&(s.textCache="",this.renderer.buildText(s)),this}function iv(t){let e=t.bBox,i=this.element?.querySelector("textPath");if(i){let t=[],{b:s,h:a}=this.renderer.fontMetrics(this.element),o=a-s,r=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),n=i.innerHTML.replace(r,"").split(/<tspan class="highcharts-br"[^>]*>/),l=n.length,h=(t,e)=>{let{x:a,y:r}=e,n=(i.getRotationOfChar(t)-90)*iu,l=Math.cos(n),h=Math.sin(n);return[[a-o*l,r-o*h],[a+s*l,r+s*h]]};for(let e=0,s=0;s<l;s++){let a=n[s].length;for(let o=0;o<a;o+=5)try{let a=e+o+s,[r,n]=h(a,i.getStartPositionOfChar(a));0===o?(t.push(n),t.push(r)):(0===s&&t.unshift(n),s===l-1&&t.push(r))}catch(t){break}e+=a-1;try{let a=e+s,o=i.getEndPositionOfChar(a),[r,n]=h(a,o);t.unshift(n),t.unshift(r)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function iM(t){let e=t.labelOptions,i=t.point,s=e[i.formatPrefix+"TextPath"]||e.textPath;s&&!e.useHTML&&(this.setTextPath(i.getDataLabelPath?.(this)||i.graphic,s),i.dataLabelPath&&!s.enabled&&(i.dataLabelPath=i.dataLabelPath.destroy()))}let{parse:iL}=eX(),{noop:ik}=n(),{series:{prototype:iw},seriesTypes:{bubble:iA}}=h(),{initDataLabels:iS,initDataLabelsDefer:iN}={initDataLabels:function(){let t=this.options.dataLabels;if(!this.dataLabelsGroup){let e=this.initDataLabelsGroup();return!this.chart.styledMode&&t?.style&&e.css(t.style),e.attr({opacity:0}),this.visible&&e.show(),e}return this.dataLabelsGroup.attr(il({opacity:1},this.getPlotBox("data-labels"))),this.dataLabelsGroup},initDataLabelsDefer:function(){let t=this.options.dataLabels;t?.defer&&this.options.layoutAlgorithm?.enableSimulation?ih(()=>{this.deferDataLabels=!1},t?ip(t.animation).defer:0):this.deferDataLabels=!1}},{addEvent:iT,clamp:iC,defined:iX,extend:iY,fireEvent:iI,isArray:iR,isNumber:iE,merge:iz,pick:iD}=n();({compose:function(t){ig(t,"afterGetBBox",iv),ig(t,"beforeAddingDataLabel",iM);let e=t.prototype;e.setTextPath||(e.setTextPath=iP)}}).compose(ic());class iO extends iA{constructor(){super(...arguments),this.parentNodeMass=0,this.deferDataLabels=!0}static compose(t,e,i){iA.compose(t,e,i),ez.compose(e),ir.compose(e)}accumulateAllPoints(){let t=this.chart,e=[];for(let i of t.series)if(i.is("packedbubble")&&i.reserveSpace()){let t=i.getColumn("value");for(let s=0;s<t.length;s++)e.push([null,null,t[s],i.index,s,{id:s,marker:{radius:0}}])}return e}addLayout(){let t=this.options.layoutAlgorithm=this.options.layoutAlgorithm||{},e=t.type||"packedbubble",i=this.chart.options.chart,s=this.chart.graphLayoutsStorage,a=this.chart.graphLayoutsLookup,o;s||(this.chart.graphLayoutsStorage=s={},this.chart.graphLayoutsLookup=a=[]),(o=s[e])||(t.enableSimulation=iX(i.forExport)?!i.forExport:t.enableSimulation,s[e]=o=new eV.layouts[e],o.init(t),a.splice(o.index,0,o)),this.layout=o,this.points.forEach(t=>{t.mass=2,t.degree=1,t.collisionNmb=1}),o.setArea(0,0,this.chart.plotWidth,this.chart.plotHeight),o.addElementsToCollection([this],o.series),o.addElementsToCollection(this.points,o.nodes)}addSeriesLayout(){let t=this.options.layoutAlgorithm=this.options.layoutAlgorithm||{},e=t.type||"packedbubble",i=this.chart.graphLayoutsStorage,s=this.chart.graphLayoutsLookup,a=iz(t,t.parentNodeOptions,{enableSimulation:this.layout.options.enableSimulation}),o=i[e+"-series"];o||(i[e+"-series"]=o=new eV.layouts[e],o.init(a),s.splice(o.index,0,o)),this.parentNodeLayout=o,this.createParentNodes()}calculateParentRadius(){let t=this.seriesBox();this.parentNodeRadius=iC(Math.sqrt(2*this.parentNodeMass/Math.PI)+20,20,t?Math.max(Math.sqrt(Math.pow(t.width,2)+Math.pow(t.height,2))/2+20,20):Math.sqrt(2*this.parentNodeMass/Math.PI)+20),this.parentNode&&(this.parentNode.marker.radius=this.parentNode.radius=this.parentNodeRadius)}calculateZExtremes(){let t=this.chart.series,e=this.options.zMin,i=this.options.zMax,s=1/0,a=-1/0;return e&&i?[e,i]:(t.forEach(t=>{t.getColumn("value").forEach(t=>{iX(t)&&(t>a&&(a=t),t<s&&(s=t))})}),[e=iD(e,s),i=iD(i,a)])}checkOverlap(t,e){let i=t[0]-e[0],s=t[1]-e[1];return Math.sqrt(i*i+s*s)-Math.abs(t[2]+e[2])<-.001}createParentNodes(){let t=this.pointClass,e=this.chart,i=this.parentNodeLayout,s=this.layout.options,a,o=this.parentNode,r={radius:this.parentNodeRadius,lineColor:this.color,fillColor:iL(this.color).brighten(.4).get()};s.parentNodeOptions&&(r=iz(s.parentNodeOptions.marker||{},r)),this.parentNodeMass=0,this.points.forEach(t=>{this.parentNodeMass+=Math.PI*Math.pow(t.marker.radius,2)}),this.calculateParentRadius(),i.nodes.forEach(t=>{t.seriesIndex===this.index&&(a=!0)}),i.setArea(0,0,e.plotWidth,e.plotHeight),a||(o||(o=new t(this,{mass:this.parentNodeRadius/2,marker:r,dataLabels:{inside:!1},states:{normal:{marker:r},hover:{marker:r}},dataLabelOnNull:!0,degree:this.parentNodeRadius,isParentNode:!0,seriesIndex:this.index}),this.chart.allParentNodes.push(o)),this.parentNode&&(o.plotX=this.parentNode.plotX,o.plotY=this.parentNode.plotY),this.parentNode=o,i.addElementsToCollection([this],i.series),i.addElementsToCollection([o],i.nodes))}deferLayout(){let t=this.options.layoutAlgorithm;this.visible&&(this.addLayout(),t.splitSeries&&this.addSeriesLayout())}destroy(){this.chart.graphLayoutsLookup&&this.chart.graphLayoutsLookup.forEach(t=>{t.removeElementFromCollection(this,t.series)},this),this.parentNode&&this.parentNodeLayout&&(this.parentNodeLayout.removeElementFromCollection(this.parentNode,this.parentNodeLayout.nodes),this.parentNode.dataLabel&&(this.parentNode.dataLabel=this.parentNode.dataLabel.destroy())),iw.destroy.apply(this,arguments)}drawDataLabels(){!this.deferDataLabels&&(iw.drawDataLabels.call(this,this.points),this.parentNode&&(this.parentNode.formatPrefix="parentNode",iw.drawDataLabels.call(this,[this.parentNode])))}drawGraph(){if(!this.layout||!this.layout.options.splitSeries)return;let t=this.chart,e=this.layout.options.parentNodeOptions.marker,i={fill:e.fillColor||iL(this.color).brighten(.4).get(),opacity:e.fillOpacity,stroke:e.lineColor||this.color,"stroke-width":iD(e.lineWidth,this.options.lineWidth)},s={};this.parentNodesGroup=this.plotGroup("parentNodesGroup","parentNode",this.visible?"inherit":"hidden",.1,t.seriesGroup),this.group?.attr({zIndex:2}),this.calculateParentRadius(),this.parentNode&&iX(this.parentNode.plotX)&&iX(this.parentNode.plotY)&&iX(this.parentNodeRadius)&&(s=iz({x:this.parentNode.plotX-this.parentNodeRadius,y:this.parentNode.plotY-this.parentNodeRadius,width:2*this.parentNodeRadius,height:2*this.parentNodeRadius},i),this.parentNode.graphic||(this.graph=this.parentNode.graphic=t.renderer.symbol(i.symbol).add(this.parentNodesGroup)),this.parentNode.graphic.attr(s))}drawTracker(){let t,e=this.parentNode;super.drawTracker(),e&&(t=iR(e.dataLabels)?e.dataLabels:e.dataLabel?[e.dataLabel]:[],e.graphic&&(e.graphic.element.point=e),t.forEach(t=>{(t.div||t.element).point=e}))}getPointRadius(){let t,e,i,s,a=this.chart,o=a.plotWidth,r=a.plotHeight,n=this.options,l=n.useSimulation,h=Math.min(o,r),p={},d=[],c=a.allDataPoints||[],u=c.length;["minSize","maxSize"].forEach(t=>{let e=parseInt(n[t],10),i=/%$/.test(n[t]);p[t]=i?h*e/100:e*Math.sqrt(u)}),a.minRadius=t=p.minSize/Math.sqrt(u),a.maxRadius=e=p.maxSize/Math.sqrt(u);let g=l?this.calculateZExtremes():[t,e];c.forEach((a,o)=>{i=l?iC(a[2],g[0],g[1]):a[2],0===(s=this.getRadius(g[0],g[1],t,e,i))&&(s=null),c[o][2]=s,d.push(s)}),this.radii=d}init(){return iw.init.apply(this,arguments),iN.call(this),this.eventsToUnbind.push(iT(this,"updatedData",function(){this.chart.series.forEach(t=>{t.type===this.type&&(t.isDirty=!0)},this)})),this}onMouseUp(t){if(t.fixedPosition&&!t.removed){let e,i=this.layout,s=this.parentNodeLayout;!t.isParentNode&&s&&i.options.dragBetweenSeries&&s.nodes.forEach(s=>{t&&t.marker&&s!==t.series.parentNode&&(e=i.getDistXY(t,s),i.vectorLength(e)-s.marker.radius-t.marker.radius<0&&(s.series.addPoint(iz(t.options,{plotX:t.plotX,plotY:t.plotY}),!1),i.removeElementFromCollection(t,i.nodes),t.remove()))}),ez.onMouseUp.apply(this,arguments)}}placeBubbles(t){let e=this.checkOverlap,i=this.positionBubble,s=[],a=1,o=0,r=0,n,l=[],h,p=t.sort((t,e)=>e[2]-t[2]);if(p.length){if(s.push([[0,0,p[0][2],p[0][3],p[0][4]]]),p.length>1)for(s.push([[0,0-p[1][2]-p[0][2],p[1][2],p[1][3],p[1][4]]]),h=2;h<p.length;h++)p[h][2]=p[h][2]||1,e(n=i(s[a][o],s[a-1][r],p[h]),s[a][0])?(s.push([]),r=0,s[a+1].push(i(s[a][o],s[a][0],p[h])),a++,o=0):a>1&&s[a-1][r+1]&&e(n,s[a-1][r+1])?(r++,s[a].push(i(s[a][o],s[a-1][r],p[h])),o++):(o++,s[a].push(n));this.chart.stages=s,this.chart.rawPositions=[].concat.apply([],s),this.resizeRadius(),l=this.chart.rawPositions}return l}pointAttribs(t,e){let i=this.options,s=t&&t.isParentNode,a=i.marker;s&&i.layoutAlgorithm&&i.layoutAlgorithm.parentNodeOptions&&(a=i.layoutAlgorithm.parentNodeOptions.marker);let o=a.fillOpacity,r=iw.pointAttribs.call(this,t,e);return 1!==o&&(r["fill-opacity"]=o),r}positionBubble(t,e,i){let s=Math.asin,a=Math.acos,o=Math.pow,r=Math.abs,n=(0,Math.sqrt)(o(t[0]-e[0],2)+o(t[1]-e[1],2)),l=a((o(n,2)+o(i[2]+e[2],2)-o(i[2]+t[2],2))/(2*(i[2]+e[2])*n)),h=s(r(t[0]-e[0])/n),p=(t[1]-e[1]<0?0:Math.PI)+l+h*((t[0]-e[0])*(t[1]-e[1])<0?1:-1),d=Math.cos(p),c=Math.sin(p);return[e[0]+(e[2]+i[2])*c,e[1]-(e[2]+i[2])*d,i[2],i[3],i[4]]}render(){let t=[];iw.render.apply(this,arguments),!this.options.dataLabels.allowOverlap&&(this.data.forEach(e=>{iR(e.dataLabels)&&e.dataLabels.forEach(e=>{t.push(e)})}),this.options.useSimulation&&this.chart.hideOverlappingLabels(t))}resizeRadius(){let t,e,i,s,a,o=this.chart,r=o.rawPositions,n=Math.min,l=Math.max,h=o.plotLeft,p=o.plotTop,d=o.plotHeight,c=o.plotWidth;for(let o of(t=i=Number.POSITIVE_INFINITY,e=s=Number.NEGATIVE_INFINITY,r))a=o[2],t=n(t,o[0]-a),e=l(e,o[0]+a),i=n(i,o[1]-a),s=l(s,o[1]+a);let u=[e-t,s-i],g=[(c-h)/u[0],(d-p)/u[1]],f=n.apply([],g);if(Math.abs(f-1)>1e-10){for(let t of r)t[2]*=f;this.placeBubbles(r)}else o.diffY=d/2+p-i-(s-i)/2,o.diffX=c/2+h-t-(e-t)/2}seriesBox(){let t,e=this.chart,i=this.data,s=Math.max,a=Math.min,o=[e.plotLeft,e.plotLeft+e.plotWidth,e.plotTop,e.plotTop+e.plotHeight];return i.forEach(e=>{iX(e.plotX)&&iX(e.plotY)&&e.marker.radius&&(t=e.marker.radius,o[0]=a(o[0],e.plotX-t),o[1]=s(o[1],e.plotX+t),o[2]=a(o[2],e.plotY-t),o[3]=s(o[3],e.plotY+t))}),iE(o.width/o.height)?o:null}setVisible(){let t=this;iw.setVisible.apply(t,arguments),t.parentNodeLayout&&t.graph?t.visible?(t.graph.show(),t.parentNode.dataLabel&&t.parentNode.dataLabel.show()):(t.graph.hide(),t.parentNodeLayout.removeElementFromCollection(t.parentNode,t.parentNodeLayout.nodes),t.parentNode.dataLabel&&t.parentNode.dataLabel.hide()):t.layout&&(t.visible?t.layout.addElementsToCollection(t.points,t.layout.nodes):t.points.forEach(e=>{t.layout.removeElementFromCollection(e,t.layout.nodes)}))}translate(){let t,e,i,s=this.chart,a=this.data,o=this.index,r=this.options.useSimulation;for(let n of(this.generatePoints(),iX(s.allDataPoints)||(s.allDataPoints=this.accumulateAllPoints(),this.getPointRadius()),r?i=s.allDataPoints:(i=this.placeBubbles(s.allDataPoints),this.options.draggable=!1),i))n[3]===o&&(t=a[n[4]],e=iD(n[2],void 0),r||(t.plotX=n[0]-s.plotLeft+s.diffX,t.plotY=n[1]-s.plotTop+s.diffY),iE(e)&&(t.marker=iY(t.marker,{radius:e,width:2*e,height:2*e}),t.radius=e));r&&this.deferLayout(),iI(this,"afterTranslate")}}iO.defaultOptions=iz(iA.defaultOptions,{minSize:"10%",maxSize:"50%",sizeBy:"area",zoneAxis:"y",crisp:!1,tooltip:{pointFormat:"Value: {point.value}"},draggable:!0,useSimulation:!0,parentNode:{allowPointSelect:!1},dataLabels:{formatter:function(){let{numberFormatter:t}=this.series.chart,{value:e}=this.point;return ej(e)?t(e,-1):""},parentNodeFormatter:function(){return this.name||""},parentNodeTextPath:{enabled:!0},padding:0,style:{transition:"opacity 2000ms"}},layoutAlgorithm:{initialPositions:"circle",initialPositionRadius:20,bubblePadding:5,parentNodeLimit:!1,seriesInteraction:!0,dragBetweenSeries:!1,parentNodeOptions:{maxIterations:400,gravitationalConstant:.03,maxSpeed:50,initialPositionRadius:100,seriesInteraction:!0,marker:{fillColor:null,fillOpacity:1,lineWidth:null,lineColor:null,symbol:"circle"}},enableSimulation:!0,type:"packedbubble",integration:"packedbubble",maxIterations:1e3,splitSeries:!1,maxSpeed:5,gravitationalConstant:.01,friction:-.981},stickyTracking:!1}),iY(iO.prototype,{pointClass:eZ,axisTypes:[],directTouch:!0,forces:["barycenter","repulsive"],hasDraggableNodes:!0,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointArrayMap:["value"],pointValKey:"value",requireSorting:!1,trackerGroups:["group","dataLabelsGroup","parentNodesGroup"],initDataLabels:iS,alignDataLabel:iw.alignDataLabel,indexateNodes:ik,onMouseDown:ez.onMouseDown,onMouseMove:ez.onMouseMove,redrawHalo:ez.redrawHalo,searchPoint:ik}),h().registerSeriesType("packedbubble",iO);let{noop:iB}=n(),{area:iW,line:iF,scatter:iH}=h().seriesTypes,{extend:iG,merge:iq}=n();class iV extends iH{getGraphPath(){let t=iF.prototype.getGraphPath.call(this),e=t.length+1;for(;e--;)(e===t.length||"M"===t[e][0])&&e>0&&t.splice(e,0,["Z"]);return this.areaPath=t,t}drawGraph(){this.options.fillColor=this.color,iW.prototype.drawGraph.call(this)}}iV.defaultOptions=iq(iH.defaultOptions,{marker:{enabled:!1,states:{hover:{enabled:!1}}},stickyTracking:!1,tooltip:{followPointer:!0,pointFormat:""},trackByArea:!0,legendSymbol:"rectangle"}),iG(iV.prototype,{type:"polygon",drawTracker:iF.prototype.drawTracker,setStackedPoints:iB}),h().registerSeriesType("polygon",iV);let iU={circular:{gridLineWidth:1,labels:{align:void 0,x:0,y:void 0},maxPadding:0,minPadding:0,showLastLabel:!1,tickLength:0},radial:{gridLineInterpolation:"circle",gridLineWidth:1,labels:{align:"right",padding:5,x:-3,y:-2},showLastLabel:!1,title:{x:4,text:null,rotation:90}},radialGauge:{endOnTick:!1,gridLineWidth:0,labels:{align:"center",distance:-25,x:0,y:void 0},lineWidth:1,minorGridLineWidth:0,minorTickInterval:"auto",minorTickLength:10,minorTickPosition:"inside",minorTickWidth:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickPosition:"inside",tickWidth:2,title:{rotation:0,text:""},zIndex:2}},{defaultOptions:iK}=n(),{composed:i_,noop:iZ}=n(),{addEvent:ij,correctFloat:i$,defined:iQ,extend:iJ,fireEvent:i0,isObject:i1,merge:i2,pick:i3,pushUnique:i5,relativeLength:i8,wrap:i6}=n();!function(t){function e(){this.autoConnect=this.isCircular&&void 0===i3(this.userMax,this.options.max)&&i$(this.endAngleRad-this.startAngleRad)===i$(2*Math.PI),!this.isCircular&&this.chart.inverted&&this.max++,this.autoConnect&&(this.max+=this.categories&&1||this.pointRange||this.closestPointRange||0)}function i(){return()=>{if(this.isRadial&&this.tickPositions&&this.options.labels&&!0!==this.options.labels.allowOverlap)return this.tickPositions.map(t=>this.ticks[t]?.label).filter(t=>!!t)}}function s(){return iZ}function a(t,e,i){let s=this.pane.center,a=t.value,o,r,n;return this.isCircular?(iQ(a)?t.point&&(t.point.shapeArgs||{}).start&&(a=this.chart.inverted?this.translate(t.point.rectPlotY,!0):t.point.x):(r=t.chartX||0,n=t.chartY||0,a=this.translate(Math.atan2(n-i,r-e)-this.startAngleRad,!0)),r=(o=this.getPosition(a)).x,n=o.y):(iQ(a)||(r=t.chartX,n=t.chartY),iQ(r)&&iQ(n)&&(i=s[1]+this.chart.plotTop,a=this.translate(Math.min(Math.sqrt(Math.pow(r-e,2)+Math.pow(n-i,2)),s[2]/2)-s[3]/2,!0))),[a,r||0,n||0]}function o(t,e,i){let s=this.pane.center,a=this.chart,o=this.left||0,r=this.top||0,n,l=i3(e,s[2]/2-this.offset),h;return void 0===i&&(i=this.horiz?0:this.center&&-this.center[3]/2),i&&(l+=i),this.isCircular||void 0!==e?((h=this.chart.renderer.symbols.arc(o+s[0],r+s[1],l,l,{start:this.startAngleRad,end:this.endAngleRad,open:!0,innerR:0})).xBounds=[o+s[0]],h.yBounds=[r+s[1]-l]):(n=this.postTranslate(this.angleRad,l),h=[["M",this.center[0]+a.plotLeft,this.center[1]+a.plotTop],["L",n.x,n.y]]),h}function r(){this.constructor.prototype.getOffset.call(this),this.chart.axisOffset[this.side]=0}function l(t,e,i){let s=this.chart,a=t=>{if("string"==typeof t){let e=parseInt(t,10);return d.test(t)&&(e=e*n/100),e}return t},o=this.center,r=this.startAngleRad,n=o[2]/2,l=Math.min(this.offset,0),h=this.left||0,p=this.top||0,d=/%$/,c=this.isCircular,u,g,f,m,b,y,x=i3(a(i.outerRadius),n),P=a(i.innerRadius),v=i3(a(i.thickness),10);if("polygon"===this.options.gridLineInterpolation)y=this.getPlotLinePath({value:t}).concat(this.getPlotLinePath({value:e,reverse:!0}));else{t=Math.max(t,this.min),e=Math.min(e,this.max);let a=this.translate(t),n=this.translate(e);c||(x=a||0,P=n||0),"circle"!==i.shape&&c?(u=r+(a||0),g=r+(n||0)):(u=-Math.PI/2,g=1.5*Math.PI,b=!0),x-=l,v-=l,y=s.renderer.symbols.arc(h+o[0],p+o[1],x,x,{start:Math.min(u,g),end:Math.max(u,g),innerR:i3(P,x-v),open:b,borderRadius:i.borderRadius}),c&&(f=(g+u)/2,m=h+o[0]+o[2]/2*Math.cos(f),y.xBounds=f>-Math.PI/2&&f<Math.PI/2?[m,s.plotWidth]:[0,m],y.yBounds=[p+o[1]+o[2]/2*Math.sin(f)],y.yBounds[0]+=f>-Math.PI&&f<0||f>Math.PI?-10:10)}return y}function h(t){let e=this.pane.center,i=this.chart,s=i.inverted,a=t.reverse,o=this.pane.options.background?this.pane.options.background[0]||this.pane.options.background:{},r=o.innerRadius||"0%",n=o.outerRadius||"100%",l=e[0]+i.plotLeft,h=e[1]+i.plotTop,p=this.height,d=t.isCrosshair,c=e[3]/2,u=t.value,g,f,m,b,y,x,P,v,M,L=this.getPosition(u),k=L.x,w=L.y;if(d&&(u=(v=this.getCrosshairPosition(t,l,h))[0],k=v[1],w=v[2]),this.isCircular)f=Math.sqrt(Math.pow(k-l,2)+Math.pow(w-h,2)),m="string"==typeof r?i8(r,1):r/f,b="string"==typeof n?i8(n,1):n/f,e&&c&&(m<(g=c/f)&&(m=g),b<g&&(b=g)),M=[["M",l+m*(k-l),h-m*(h-w)],["L",k-(1-b)*(k-l),w+(1-b)*(h-w)]];else if((u=this.translate(u))&&(u<0||u>p)&&(u=0),"circle"===this.options.gridLineInterpolation)M=this.getLinePath(0,u,c);else if(M=[],i[s?"yAxis":"xAxis"].forEach(t=>{t.pane===this.pane&&(y=t)}),y){P=y.tickPositions,y.autoConnect&&(P=P.concat([P[0]])),a&&(P=P.slice().reverse()),u&&(u+=c);for(let t=0;t<P.length;t++)x=y.getPosition(P[t],u),M.push(t?["L",x.x,x.y]:["M",x.x,x.y])}return M}function p(t,e){let i=this.translate(t);return this.postTranslate(this.isCircular?i:this.angleRad,i3(this.isCircular?e:i<0?0:i,this.center[2]/2)-this.offset)}function d(){let t=this.center,e=this.chart,i=this.options.title;return{x:e.plotLeft+t[0]+(i.x||0),y:e.plotTop+t[1]-({high:.5,middle:.25,low:0})[i.align]*t[2]+(i.y||0)}}function c(t){t.beforeSetTickPositions=e,t.createLabelCollector=i,t.getCrosshairPosition=a,t.getLinePath=o,t.getOffset=r,t.getPlotBandPath=l,t.getPlotLinePath=h,t.getPosition=p,t.getTitlePosition=d,t.postTranslate=v,t.setAxisSize=L,t.setAxisTranslation=k,t.setOptions=w}function u(){let t=this.chart,e=this.options,i=t.angular&&this.isXAxis,s=this.pane,a=s?.options;if(!i&&s&&(t.angular||t.polar)){let t=2*Math.PI,i=(i3(a.startAngle,0)-90)*Math.PI/180,s=(i3(a.endAngle,i3(a.startAngle,0)+360)-90)*Math.PI/180;this.angleRad=(e.angle||0)*Math.PI/180,this.startAngleRad=i,this.endAngleRad=s,this.offset=e.offset||0;let o=(i%t+t)%t,r=(s%t+t)%t;o>Math.PI&&(o-=t),r>Math.PI&&(r-=t),this.normalizedStartAngleRad=o,this.normalizedEndAngleRad=r}}function g(t){this.isRadial&&(t.align=void 0,t.preventDefault())}function f(){if(this.chart?.labelCollectors){let t=this.labelCollector?this.chart.labelCollectors.indexOf(this.labelCollector):-1;t>=0&&this.chart.labelCollectors.splice(t,1)}}function m(t){let e,i=this.chart,a=i.angular,o=i.polar,r=this.isXAxis,n=this.coll,l=t.userOptions.pane||0,h=this.pane=i.pane&&i.pane[l];if("colorAxis"===n){this.isRadial=!1;return}if(a){if(a&&r)this.isHidden=!0,this.createLabelCollector=s,this.getOffset=iZ,this.redraw=M,this.render=M,this.setScale=iZ,this.setCategories=iZ,this.setTitle=iZ;else c(this);e=!r}else o&&(c(this),e=this.horiz);a||o?(this.isRadial=!0,this.labelCollector||(this.labelCollector=this.createLabelCollector()),this.labelCollector&&i.labelCollectors.push(this.labelCollector)):this.isRadial=!1,h&&e&&(h.axis=this),this.isCircular=e}function b(){this.isRadial&&this.beforeSetTickPositions()}function y(t){let e=this.label;if(!e)return;let i=this.axis,s=e.getBBox(),a=i.options.labels,o=(i.translate(this.pos)+i.startAngleRad+Math.PI/2)/Math.PI*180%360,r=Math.round(o),n=iQ(a.y)?0:-(.3*s.height),l=a.y,h,p=20,d=a.align,c="end",u=r<0?r+360:r,g=u,f=0,m=0;i.isRadial&&(h=i.getPosition(this.pos,i.center[2]/2+i8(i3(a.distance,-25),i.center[2]/2,-i.center[2]/2)),"auto"===a.rotation?e.attr({rotation:o}):iQ(l)||(l=i.chart.renderer.fontMetrics(e).b-s.height/2),iQ(d)||(i.isCircular?(s.width>i.len*i.tickInterval/(i.max-i.min)&&(p=0),d=o>p&&o<180-p?"left":o>180+p&&o<360-p?"right":"center"):d="center",e.attr({align:d})),"auto"===d&&2===i.tickPositions.length&&i.isCircular&&(u>90&&u<180?u=180-u:u>270&&u<=360&&(u=540-u),g>180&&g<=360&&(g=360-g),(i.pane.options.startAngle===r||i.pane.options.startAngle===r+360||i.pane.options.startAngle===r-360)&&(c="start"),d=r>=-90&&r<=90||r>=-360&&r<=-270||r>=270&&r<=360?"start"===c?"right":"left":"start"===c?"left":"right",g>70&&g<110&&(d="center"),u<15||u>=180&&u<195?f=.3*s.height:u>=15&&u<=35?f="start"===c?0:.75*s.height:u>=195&&u<=215?f="start"===c?.75*s.height:0:u>35&&u<=90?f="start"===c?-(.25*s.height):s.height:u>215&&u<=270&&(f="start"===c?s.height:-(.25*s.height)),g<15?m="start"===c?-(.15*s.height):.15*s.height:g>165&&g<=180&&(m="start"===c?.15*s.height:-(.15*s.height)),e.attr({align:d}),e.translate(m,f+n)),t.pos.x=h.x+(a.x||0),t.pos.y=h.y+(l||0))}function x(t){this.axis.getPosition&&iJ(t.pos,this.axis.getPosition(this.pos))}function P({options:e}){e.xAxis&&i2(!0,t.radialDefaultOptions.circular,e.xAxis),e.yAxis&&i2(!0,t.radialDefaultOptions.radialGauge,e.yAxis)}function v(t,e){let i=this.chart,s=this.center;return t=this.startAngleRad+t,{x:i.plotLeft+s[0]+Math.cos(t)*e,y:i.plotTop+s[1]+Math.sin(t)*e}}function M(){this.isDirty=!1}function L(){let t,e;this.constructor.prototype.setAxisSize.call(this),this.isRadial&&(this.pane.updateCenter(this),t=this.center=this.pane.center.slice(),this.isCircular?this.sector=this.endAngleRad-this.startAngleRad:(e=this.postTranslate(this.angleRad,t[3]/2),t[0]=e.x-this.chart.plotLeft,t[1]=e.y-this.chart.plotTop),this.len=this.width=this.height=(t[2]-t[3])*i3(this.sector,1)/2)}function k(){this.constructor.prototype.setAxisTranslation.call(this),this.center&&(this.isCircular?this.transA=(this.endAngleRad-this.startAngleRad)/(this.max-this.min||1):this.transA=(this.center[2]-this.center[3])/2/(this.max-this.min||1),this.isXAxis?this.minPixelPadding=this.transA*this.minPointOffset:this.minPixelPadding=0)}function w(e){let{coll:i}=this,{angular:s,inverted:a,polar:o}=this.chart,r={};s?this.isXAxis||(r=i2(iK.yAxis,t.radialDefaultOptions.radialGauge)):o&&(r=this.horiz?i2(iK.xAxis,t.radialDefaultOptions.circular):i2("xAxis"===i?iK.xAxis:iK.yAxis,t.radialDefaultOptions.radial)),a&&"yAxis"===i&&(r.stackLabels=i1(iK.yAxis,!0)?iK.yAxis.stackLabels:{},r.reversedStacks=!0);let n=this.options=i2(r,e);n.plotBands||(n.plotBands=[]),i0(this,"afterSetOptions")}function A(t,e,i,s,a,o,r){let n,l,h=this.axis;return h.isRadial?["M",e,i,"L",(n=h.getPosition(this.pos,h.center[2]/2+s)).x,n.y]:t.call(this,e,i,s,a,o,r)}t.radialDefaultOptions=i2(iU),t.compose=function(t,e){return i5(i_,"Axis.Radial")&&(ij(t,"afterInit",u),ij(t,"autoLabelAlign",g),ij(t,"destroy",f),ij(t,"init",m),ij(t,"initialAxisTranslation",b),ij(e,"afterGetLabelPosition",y),ij(e,"afterGetPosition",x),ij(n(),"setOptions",P),i6(e.prototype,"getMarkPath",A)),t}}(s||(s={}));let i9=s,{animObject:i4}=n(),{composed:i7}=n(),{addEvent:st,defined:se,find:si,isNumber:ss,merge:sa,pick:so,pushUnique:sr,relativeLength:sn,splat:sl,uniqueKey:sh,wrap:sp}=n();function sd(){(this.pane||[]).forEach(t=>{t.render()})}function sc(t){let e=t.args[0].xAxis,i=t.args[0].yAxis,s=t.args[0].chart;e&&i&&("polygon"===i.gridLineInterpolation?(e.startOnTick=!0,e.endOnTick=!0):"polygon"===e.gridLineInterpolation&&s.inverted&&(i.startOnTick=!0,i.endOnTick=!0))}function su(){this.pane||(this.pane=[]),this.options.pane=sl(this.options.pane||{}),this.options.pane.forEach(t=>{new X(t,this)},this)}function sg(t){let e=t.args.marker,i=this.chart.xAxis[0],s=this.chart.yAxis[0],a=this.chart.inverted,o=a?s:i,r=a?i:s;if(this.chart.polar){t.preventDefault();let i=(e.attr?e.attr("start"):e.start)-o.startAngleRad,s=e.attr?e.attr("r"):e.r,a=(e.attr?e.attr("end"):e.end)-o.startAngleRad,n=e.attr?e.attr("innerR"):e.innerR;t.result.x=i+o.pos,t.result.width=a-i,t.result.y=r.len+r.pos-s,t.result.height=s-n}}function sf(t){let e=this.chart;if(e.polar&&e.hoverPane&&e.hoverPane.axis){t.preventDefault();let i=e.hoverPane.center,s=e.mouseDownX||0,a=e.mouseDownY||0,o=t.args.chartY,r=t.args.chartX,n=2*Math.PI,l=e.hoverPane.axis.startAngleRad,h=e.hoverPane.axis.endAngleRad,p=e.inverted?e.xAxis[0]:e.yAxis[0],d={},c="arc";if(d.x=i[0]+e.plotLeft,d.y=i[1]+e.plotTop,this.zoomHor){let t=l>0?h-l:Math.abs(l)+Math.abs(h),u=Math.atan2(a-e.plotTop-i[1],s-e.plotLeft-i[0])-l,g=Math.atan2(o-e.plotTop-i[1],r-e.plotLeft-i[0])-l;d.r=i[2]/2,d.innerR=i[3]/2,u<=0&&(u+=n),g<=0&&(g+=n),g<u&&(g=[u,u=g][0]),t<n&&l+g>h+(n-t)/2&&(g=u,u=l<=0?l:0);let f=d.start=Math.max(u+l,l),m=d.end=Math.min(g+l,h);if("polygon"===p.options.gridLineInterpolation){let t=e.hoverPane.axis,s=f-t.startAngleRad+t.pos,a=p.getPlotLinePath({value:p.max}),o=t.toValue(s),r=t.toValue(s+(m-f));if(o<t.getExtremes().min){let{min:e,max:i}=t.getExtremes();o=i-(e-o)}if(r<t.getExtremes().min){let{min:e,max:i}=t.getExtremes();r=i-(e-r)}r<o&&(r=[o,o=r][0]),(a=sx(a,o,r,t)).push(["L",i[0]+e.plotLeft,e.plotTop+i[1]]),d.d=a,c="path"}}if(this.zoomVert){let t=e.inverted?e.xAxis[0]:e.yAxis[0],n=Math.sqrt(Math.pow(s-e.plotLeft-i[0],2)+Math.pow(a-e.plotTop-i[1],2)),p=Math.sqrt(Math.pow(r-e.plotLeft-i[0],2)+Math.pow(o-e.plotTop-i[1],2));if(p<n&&(n=[p,p=n][0]),p>i[2]/2&&(p=i[2]/2),n<i[3]/2&&(n=i[3]/2),this.zoomHor||(d.start=l,d.end=h),d.r=p,d.innerR=n,"polygon"===t.options.gridLineInterpolation){let e=t.toValue(t.len+t.pos-n),i=t.toValue(t.len+t.pos-p);d.d=t.getPlotLinePath({value:i}).concat(t.getPlotLinePath({value:e,reverse:!0})),c="path"}}if(this.zoomHor&&this.zoomVert&&"polygon"===p.options.gridLineInterpolation){let t=e.hoverPane.axis,i=d.start||0,s=d.end||0,a=i-t.startAngleRad+t.pos,o=t.toValue(a),r=t.toValue(a+(s-i));if(d.d instanceof Array){let t=d.d.slice(0,d.d.length/2),i=d.d.slice(d.d.length/2,d.d.length);i=[...i].reverse();let s=e.hoverPane.axis;t=sx(t,o,r,s),(i=sx(i,o,r,s))&&(i[0][0]="L"),i=[...i].reverse(),d.d=t.concat(i),c="path"}}t.attrs=d,t.shapeType=c}}function sm(){let t=this.chart;t.polar&&(this.polar=new sT(this),t.inverted&&(this.isRadialSeries=!0,this.is("column")&&(this.isRadialBar=!0)))}function sb(){if(this.chart.polar&&this.xAxis){let{xAxis:t,yAxis:e}=this,i=this.chart;this.kdByAngle=i.tooltip&&i.tooltip.shared,this.kdByAngle||i.inverted?this.searchPoint=sy:this.options.findNearestPointBy="xy";let s=this.points,a=s.length;for(;a--;)this.is("column")||this.is("columnrange")||this.polar.toXY(s[a]),i.hasParallelCoordinates||this.yAxis.reversed||(so(s[a].y,Number.MIN_VALUE)<e.min||s[a].x<t.min||s[a].x>t.max?(s[a].isNull=!0,s[a].plotY=NaN):s[a].isNull=s[a].isValid&&!s[a].isValid());this.hasClipCircleSetter||(this.hasClipCircleSetter=!!this.eventsToUnbind.push(st(this,"afterRender",function(){let t;i.polar&&!1!==this.options.clip&&(t=this.yAxis.pane.center,this.clipCircle?this.clipCircle.animate({x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2}):this.clipCircle=function(t,e,i,s,a){let o=sh(),r=t.createElement("clipPath").attr({id:o}).add(t.defs),n=a?t.arc(e,i,s,a,0,2*Math.PI).add(r):t.circle(e,i,s).add(r);return n.id=o,n.clipPath=r,n}(i.renderer,t[0],t[1],t[2]/2,t[3]/2),this.group.clip(this.clipCircle),this.setClip=n().noop)})))}}function sy(t){let e=this.chart,i=this.xAxis,s=this.yAxis,a=i.pane&&i.pane.center,o=t.chartX-(a&&a[0]||0)-e.plotLeft,r=t.chartY-(a&&a[1]||0)-e.plotTop,n=e.inverted?{clientX:t.chartX-s.pos,plotY:t.chartY-i.pos}:{clientX:180+-180/Math.PI*Math.atan2(o,r)};return this.searchKDTree(n)}function sx(t,e,i,s){let a=s.tickInterval,o=s.tickPositions,r=si(o,t=>t>=i),n=si([...o].reverse(),t=>t<=e);return se(r)||(r=o[o.length-1]),se(n)||(n=o[0],r+=a,t[0][0]="L",t.unshift(t[t.length-3])),(t=t.slice(o.indexOf(n),o.indexOf(r)+1))[0][0]="M",t}function sP(t,e){return si(this.pane||[],t=>t.options.id===e)||t.call(this,e)}function sv(t,e,i,s,a,o){let r,n,l,h=this.chart,p=so(s.inside,!!this.options.stacking);if(h.polar){if(r=e.rectPlotX/Math.PI*180,h.inverted)this.forceDL=h.isInsidePlot(e.plotX,e.plotY),p&&e.shapeArgs?(n=e.shapeArgs,a=sa(a,{x:(l=this.yAxis.postTranslate(((n.start||0)+(n.end||0))/2-this.xAxis.startAngleRad,e.barX+e.pointWidth/2)).x-h.plotLeft,y:l.y-h.plotTop})):e.tooltipPos&&(a=sa(a,{x:e.tooltipPos[0],y:e.tooltipPos[1]})),s.align=so(s.align,"center"),s.verticalAlign=so(s.verticalAlign,"middle");else{var c;null===(c=s).align&&(c.align=r>20&&r<160?"left":r>200&&r<340?"right":"center"),null===c.verticalAlign&&(c.verticalAlign=r<45||r>315?"bottom":r>135&&r<225?"top":"middle"),s=c}d().prototype.alignDataLabel.call(this,e,i,s,a,o),this.isRadialBar&&e.shapeArgs&&e.shapeArgs.start===e.shapeArgs.end?i.hide():i.show()}else t.call(this,e,i,s,a,o)}function sM(){let t=this.options,e=t.stacking,i=this.chart,s=this.xAxis,a=this.yAxis,o=a.reversed,r=a.center,l=s.startAngleRad,h=s.endAngleRad-l,p=t.threshold,d=0,c,u,g,f,m,b=0,y=0,x,P,v,M,L,k,w,A;if(s.isRadial)for(g=(c=this.points).length,f=a.translate(a.min),m=a.translate(a.max),p=t.threshold||0,i.inverted&&ss(p)&&se(d=a.translate(p))&&(d<0?d=0:d>h&&(d=h),this.translatedThreshold=d+l);g--;){if(k=(u=c[g]).barX,P=u.x,v=u.y,u.shapeType="arc",i.inverted){u.plotY=a.translate(v),e&&a.stacking?(L=a.stacking.stacks[(v<0?"-":"")+this.stackKey],this.visible&&L&&L[P]&&!u.isNull&&(M=L[P].points[this.getStackIndicator(void 0,P,this.index).key],b=a.translate(M[0]),y=a.translate(M[1]),se(b)&&(b=n().clamp(b,0,h)))):(b=d,y=u.plotY),b>y&&(y=[b,b=y][0]),o?y>f?y=f:b<m?b=m:(b>f||y<m)&&(b=y=h):b<f?b=f:y>m?y=m:(y<f||b>m)&&(b=y=0),a.min>a.max&&(b=y=o?h:0),b+=l,y+=l,r&&(u.barX=k+=r[3]/2),w=Math.max(k,0),A=Math.max(k+u.pointWidth,0);let i=t.borderRadius,s=sn(("object"==typeof i?i.radius:i)||0,A-w);u.shapeArgs={x:r[0],y:r[1],r:A,innerR:w,start:b,end:y,borderRadius:s},u.opacity=b===y?0:void 0,u.plotY=(se(this.translatedThreshold)&&(b<this.translatedThreshold?b:y))-l}else b=k+l,u.shapeArgs=this.polar.arc(u.yBottom,u.plotY,b,b+u.pointWidth),u.shapeArgs.borderRadius=0;this.polar.toXY(u),i.inverted?(x=a.postTranslate(u.rectPlotY,k+u.pointWidth/2),u.tooltipPos=[x.x-i.plotLeft,x.y-i.plotTop]):u.tooltipPos=[u.plotX,u.plotY],r&&(u.ttBelow=u.plotY>r[1])}}function sL(t,e){let i,s,a=this;if(this.chart.polar){e=e||this.points;for(let t=0;t<e.length;t++)if(!e[t].isNull){i=t;break}!1!==this.options.connectEnds&&void 0!==i&&(this.connectEnds=!0,e.splice(e.length,0,e[i]),s=!0),e.forEach(t=>{void 0===t.polarPlotY&&a.polar.toXY(t)})}let o=t.apply(this,[].slice.call(arguments,1));return s&&e.pop(),o}function sk(t,e){let i=this.chart,s={xAxis:[],yAxis:[]};return i.polar?i.axes.forEach(t=>{if("colorAxis"===t.coll)return;let a=t.isXAxis,o=t.center,r=e.chartX-o[0]-i.plotLeft,n=e.chartY-o[1]-i.plotTop;s[a?"xAxis":"yAxis"].push({axis:t,value:t.translate(a?Math.PI-Math.atan2(r,n):Math.sqrt(Math.pow(r,2)+Math.pow(n,2)),!0)})}):s=t.call(this,e),s}function sw(t,e){!this.chart.polar&&t.call(this,e)}function sA(t,e){let i=this,s=this.chart,a=this.group,o=this.markerGroup,r=this.xAxis&&this.xAxis.center,l=s.plotLeft,h=s.plotTop,p=this.options.animation,d,c,u,g,f,m;s.polar?i.isRadialBar?e||(i.startAngleRad=so(i.translatedThreshold,i.xAxis.startAngleRad),n().seriesTypes.pie.prototype.animate.call(i,e)):(p=i4(p),i.is("column")?e||(c=r[3]/2,i.points.forEach(t=>{u=t.graphic,f=(g=t.shapeArgs)&&g.r,m=g&&g.innerR,u&&g&&(u.attr({r:c,innerR:c}),u.animate({r:f,innerR:m},i.options.animation))})):e?(d={translateX:r[0]+l,translateY:r[1]+h,scaleX:.001,scaleY:.001},a.attr(d),o&&o.attr(d)):(d={translateX:l,translateY:h,scaleX:1,scaleY:1},a.animate(d,p),o&&o.animate(d,p))):t.call(this,e)}function sS(t,e,i,s){let a,o;if(this.chart.polar){if(s){let t=(o=function t(e,i,s,a){let o,r,n,l,h,p,d=+!!a,c=(o=i>=0&&i<=e.length-1?i:i<0?e.length-1+i:0)-1<0?e.length-(1+d):o-1,u=o+1>e.length-1?d:o+1,g=e[c],f=e[u],m=g.plotX,b=g.plotY,y=f.plotX,x=f.plotY,P=e[o].plotX,v=e[o].plotY;r=(1.5*P+m)/2.5,n=(1.5*v+b)/2.5,l=(1.5*P+y)/2.5,h=(1.5*v+x)/2.5;let M=Math.sqrt(Math.pow(r-P,2)+Math.pow(n-v,2)),L=Math.sqrt(Math.pow(l-P,2)+Math.pow(h-v,2)),k=Math.atan2(n-v,r-P);p=Math.PI/2+(k+Math.atan2(h-v,l-P))/2,Math.abs(k-p)>Math.PI/2&&(p-=Math.PI),r=P+Math.cos(p)*M,n=v+Math.sin(p)*M;let w={rightContX:l=P+Math.cos(Math.PI+p)*L,rightContY:h=v+Math.sin(Math.PI+p)*L,leftContX:r,leftContY:n,plotX:P,plotY:v};return s&&(w.prevPointCont=t(e,c,!1,a)),w}(e,s,!0,this.connectEnds)).prevPointCont&&o.prevPointCont.rightContX,i=o.prevPointCont&&o.prevPointCont.rightContY;a=["C",ss(t)?t:o.plotX,ss(i)?i:o.plotY,ss(o.leftContX)?o.leftContX:o.plotX,ss(o.leftContY)?o.leftContY:o.plotY,o.plotX,o.plotY]}else a=["M",i.plotX,i.plotY]}else a=t.call(this,e,i,s);return a}function sN(t,e,i=this.plotY){if(!this.destroyed){let{plotX:s,series:a}=this,{chart:o}=a;return o.polar&&ss(s)&&ss(i)?[s+(e?o.plotLeft:0),i+(e?o.plotTop:0)]:t.call(this,e,i)}}class sT{static compose(t,e,i,s,a,o,r,n,l,h){if(X.compose(e,i),i9.compose(t,a),sr(i7,"Polar")){let t=e.prototype,a=o.prototype,p=i.prototype,d=s.prototype;if(st(e,"afterDrawChartBox",sd),st(e,"createAxes",su),st(e,"init",sc),sp(t,"get",sP),sp(p,"getCoordinates",sk),sp(p,"pinch",sw),st(i,"getSelectionMarkerAttrs",sf),st(i,"getSelectionBox",sg),st(s,"afterInit",sm),st(s,"afterTranslate",sb,{order:2}),st(s,"afterColumnTranslate",sM,{order:4}),sp(d,"animate",sA),sp(a,"pos",sN),n){let t=n.prototype;sp(t,"alignDataLabel",sv),sp(t,"animate",sA)}if(l&&sp(l.prototype,"getGraphPath",sL),h){let t=h.prototype;sp(t,"getPointSpline",sS),r&&(r.prototype.getPointSpline=t.getPointSpline)}}}constructor(t){this.series=t}arc(t,e,i,s){let a=this.series,o=a.xAxis.center,r=a.yAxis.len,n=o[3]/2,l=r-e+n,h=r-so(t,r)+n;return a.yAxis.reversed&&(l<0&&(l=n),h<0&&(h=n)),{x:o[0],y:o[1],r:l,innerR:h,start:i,end:s}}toXY(t){let e=this.series,i=e.chart,s=e.xAxis,a=e.yAxis,o=t.plotX,r=i.inverted,n=t.y,l=t.plotY,h=r?o:a.len-l,p;if(r&&e&&!e.isRadialBar&&(t.plotY=l=ss(n)?a.translate(n):0),t.rectPlotX=o,t.rectPlotY=l,a.center&&(h+=a.center[3]/2),ss(l)){let e=r?a.postTranslate(l,h):s.postTranslate(o,h);t.plotX=t.polarPlotX=e.x-i.plotLeft,t.plotY=t.polarPlotY=e.y-i.plotTop}e.kdByAngle?((p=(o/Math.PI*180+s.pane.options.startAngle)%360)<0&&(p+=360),t.clientX=p):t.clientX=t.plotX}}let sC=t.default.StackItem;var sX=o.n(sC);let{composed:sY}=n(),{addEvent:sI,objectEach:sR,pushUnique:sE}=n();!function(t){function e(){let t=this.waterfall.stacks;t&&(t.changed=!1,delete t.alreadyChanged)}function i(){let t=this.options.stackLabels;t?.enabled&&this.waterfall.stacks&&this.waterfall.renderStackTotals()}function s(){this.waterfall||(this.waterfall=new o(this))}function a(){let t=this.axes;for(let e of this.series)if(e.options.stacking){for(let e of t)e.isXAxis||(e.waterfall.stacks.changed=!0);break}}t.compose=function(t,o){sE(sY,"Axis.Waterfall")&&(sI(t,"init",s),sI(t,"afterBuildStacks",e),sI(t,"afterRender",i),sI(o,"beforeRedraw",a))};class o{constructor(t){this.axis=t,this.stacks={changed:!1}}renderStackTotals(){let t=this.axis,e=t.waterfall.stacks,i=t.stacking?.stackTotalGroup,s=new(sX())(t,t.options.stackLabels||{},!1,0,void 0);this.dummyStackItem=s,i&&sR(e,t=>{sR(t,(t,e)=>{s.total=t.stackTotal,s.x=+e,t.label&&(s.label=t.label),sX().prototype.render.call(s,i),t.label=s.label,delete s.label})}),s.total=null}}t.Composition=o}(a||(a={}));let sz=a,{isNumber:sD}=n();class sO extends te().prototype.pointClass{getClassName(){let t=tE().prototype.getClassName.call(this);return this.isSum?t+=" highcharts-sum":this.isIntermediateSum&&(t+=" highcharts-intermediate-sum"),t}isValid(){return sD(this.y)||this.isSum||!!this.isIntermediateSum}}let{column:sB,line:sW}=h().seriesTypes,{addEvent:sF,arrayMax:sH,arrayMin:sG,correctFloat:sq,crisp:sV,extend:sU,isNumber:sK,merge:s_,objectEach:sZ,pick:sj}=n();function s$(t,e){return Object.hasOwnProperty.call(t,e)}class sQ extends sB{generatePoints(){sB.prototype.generatePoints.apply(this);let t=this.getColumn("y",!0);for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e],s=t[e];sK(s)&&(i.isIntermediateSum||i.isSum)&&(i.y=sq(s))}}processData(t){let e,i,s,a,o,r,n=this.options,l=this.getColumn("y"),h=n.data,p=l.length,d=n.threshold||0;s=i=a=o=0;for(let t=0;t<p;t++)r=l[t],e=h?.[t]||{},"sum"===r||e.isSum?l[t]=sq(s):"intermediateSum"===r||e.isIntermediateSum?(l[t]=sq(i),i=0):(s+=r,i+=r),a=Math.min(s,a),o=Math.max(s,o);super.processData.call(this,t),n.stacking||(this.dataMin=a+d,this.dataMax=o)}toYData(t){return t.isSum?"sum":t.isIntermediateSum?"intermediateSum":t.y}pointAttribs(t,e){let i=this.options.upColor;i&&!t.options.color&&sK(t.y)&&(t.color=t.y>0?i:void 0);let s=sB.prototype.pointAttribs.call(this,t,e);return delete s.dashstyle,s}getGraphPath(){return[["M",0,0]]}getCrispPath(){let t=this.data.filter(t=>sK(t.y)),e=this.yAxis,i=t.length,s=this.graph?.strokeWidth()||0,a=this.xAxis.reversed,o=this.yAxis.reversed,r=this.options.stacking,n=[];for(let l=1;l<i;l++){if(!(this.options.connectNulls||sK(this.data[t[l].index-1].y)))continue;let i=t[l].box,h=t[l-1],p=h.y||0,d=t[l-1].box;if(!i||!d)continue;let c=e.waterfall.stacks[this.stackKey],u=p>0?-d.height:0;if(c&&d&&i){let t,p=c[l-1];if(r){let i=p.connectorThreshold;t=sV(e.translate(i,!1,!0,!1,!0)+(o?u:0),s)}else t=sV(d.y+(h.minPointLengthOffset||0),s);n.push(["M",(d.x||0)+(a?0:d.width||0),t],["L",(i.x||0)+(a&&i.width||0),t])}if(d&&n.length&&(!r&&p<0&&!o||p>0&&o)){let t=n[n.length-2];t&&"number"==typeof t[2]&&(t[2]+=d.height||0);let e=n[n.length-1];e&&"number"==typeof e[2]&&(e[2]+=d.height||0)}}return n}drawGraph(){sW.prototype.drawGraph.call(this),this.graph&&this.graph.attr({d:this.getCrispPath()})}setStackedPoints(t){let e=this.options,i=t.waterfall?.stacks,s=e.threshold||0,a=this.stackKey,o=this.getColumn("x"),r=this.getColumn("y"),n=o.length,l=s,h=l,p,d=0,c=0,u=0,g,f,m,b,y,x,P,v,M=(t,e,i,s)=>{if(p){if(g)for(;i<g;i++)p.stackState[i]+=s;else p.stackState[0]=t,g=p.stackState.length;p.stackState.push(p.stackState[g-1]+e)}};if(t.stacking&&i&&this.reserveSpace()){v=i.changed,(P=i.alreadyChanged)&&0>P.indexOf(a)&&(v=!0),i[a]||(i[a]={});let t=i[a];if(t)for(let i=0;i<n;i++)(!t[x=o[i]]||v)&&(t[x]={negTotal:0,posTotal:0,stackTotal:0,threshold:0,stateIndex:0,stackState:[],label:v&&t[x]?t[x].label:void 0}),p=t[x],(y=r[i])>=0?p.posTotal+=y:p.negTotal+=y,b=e.data[i],f=p.absolutePos=p.posTotal,m=p.absoluteNeg=p.negTotal,p.stackTotal=f+m,g=p.stackState.length,b?.isIntermediateSum?(M(u,c,0,u),u=c,c=s,l^=h,h^=l,l^=h):b?.isSum?(M(s,d,g,0),l=s):(M(l,y,0,d),b&&(d+=y,c+=y)),p.stateIndex++,p.threshold=l,l+=p.stackTotal;i.changed=!1,i.alreadyChanged||(i.alreadyChanged=[]),i.alreadyChanged.push(a)}}getExtremes(){let t,e,i,s=this.options.stacking;return s?(t=this.yAxis.waterfall.stacks,e=this.stackedYNeg=[],i=this.stackedYPos=[],"overlap"===s?sZ(t[this.stackKey],function(t){e.push(sG(t.stackState)),i.push(sH(t.stackState))}):sZ(t[this.stackKey],function(t){e.push(t.negTotal+t.threshold),i.push(t.posTotal+t.threshold)}),{dataMin:sG(e),dataMax:sH(i)}):{dataMin:this.dataMin,dataMax:this.dataMax}}}sQ.defaultOptions=s_(sB.defaultOptions,{dataLabels:{inside:!0},lineWidth:1,lineColor:"#333333",dashStyle:"Dot",borderColor:"#333333",states:{hover:{lineWidthPlus:0}}}),sQ.compose=sz.compose,sU(sQ.prototype,{pointValKey:"y",showLine:!0,pointClass:sO}),sF(sQ,"afterColumnTranslate",function(){let{options:t,points:e,yAxis:i}=this,s=sj(t.minPointLength,5),a=s/2,o=t.threshold||0,r=t.stacking,n=i.waterfall.stacks[this.stackKey],l=this.getColumn("y",!0),h=o,p=o,d,c,u,g;for(let t=0;t<e.length;t++){let f=e[t],m=l[t],b=sU({x:0,y:0,width:0,height:0},f.shapeArgs||{});f.box=b;let y=[0,m],x=f.y||0;if(r){if(n){let e=n[t];"overlap"===r?(c=e.stackState[e.stateIndex--],d=x>=0?c:c-x,s$(e,"absolutePos")&&delete e.absolutePos,s$(e,"absoluteNeg")&&delete e.absoluteNeg):(x>=0?(c=e.threshold+e.posTotal,e.posTotal-=x,d=c):(c=e.threshold+e.negTotal,e.negTotal-=x,d=c-x),!e.posTotal&&sK(e.absolutePos)&&s$(e,"absolutePos")&&(e.posTotal=e.absolutePos,delete e.absolutePos),!e.negTotal&&sK(e.absoluteNeg)&&s$(e,"absoluteNeg")&&(e.negTotal=e.absoluteNeg,delete e.absoluteNeg)),f.isSum||(e.connectorThreshold=e.threshold+e.stackTotal),i.reversed?(u=x>=0?d-x:d+x,g=d):(u=d,g=d-x),f.below=u<=o,b.y=i.translate(u,!1,!0,!1,!0),b.height=Math.abs(b.y-i.translate(g,!1,!0,!1,!0));let s=i.waterfall.dummyStackItem;s&&(s.x=t,s.label=n[t].label,s.setOffset(this.pointXOffset||0,this.barW||0,this.stackedYNeg[t],this.stackedYPos[t],void 0,this.xAxis))}}else d=Math.max(p,p+x)+y[0],b.y=i.translate(d,!1,!0,!1,!0),f.isSum?(b.y=i.translate(y[1],!1,!0,!1,!0),b.height=Math.min(i.translate(y[0],!1,!0,!1,!0),i.len)-b.y,f.below=y[1]<=o):f.isIntermediateSum?(x>=0?(u=y[1]+h,g=h):(u=h,g=y[1]+h),i.reversed&&(u^=g,g^=u,u^=g),b.y=i.translate(u,!1,!0,!1,!0),b.height=Math.abs(b.y-Math.min(i.translate(g,!1,!0,!1,!0),i.len)),h+=y[1],f.below=u<=o):(b.height=m>0?i.translate(p,!1,!0,!1,!0)-b.y:i.translate(p,!1,!0,!1,!0)-i.translate(p-m,!1,!0,!1,!0),f.below=(p+=m)<o),b.height<0&&(b.y+=b.height,b.height*=-1);f.plotY=b.y,f.yBottom=b.y+b.height,b.height<=s&&!f.isNull?(b.height=s,b.y-=a,f.yBottom=b.y+b.height,f.plotY=b.y,x<0?f.minPointLengthOffset=-a:f.minPointLengthOffset=a):(f.isNull&&(b.width=0),f.minPointLengthOffset=0);let P=f.plotY+(f.negative?b.height:0);f.below&&(f.plotY+=b.height),f.tooltipPos&&(this.chart.inverted?f.tooltipPos[0]=i.len-P:f.tooltipPos[1]=P),f.isInside=this.isPointInside(f);let v=sV(f.yBottom,this.borderWidth);b.y=sV(b.y,this.borderWidth),b.height=v-b.y,s_(!0,f.shapeArgs,b)}},{order:2}),h().registerSeriesType("waterfall",sQ);let sJ=n();sJ.RadialAxis=i9,t1.compose(sJ.Axis,sJ.Chart,sJ.Legend),iO.compose(sJ.Axis,sJ.Chart,sJ.Legend),X.compose(sJ.Chart,sJ.Pointer),sT.compose(sJ.Axis,sJ.Chart,sJ.Pointer,sJ.Series,sJ.Tick,sJ.Point,h().seriesTypes.areasplinerange,h().seriesTypes.column,h().seriesTypes.line,h().seriesTypes.spline),sQ.compose(sJ.Axis,sJ.Chart);let s0=sJ;export{s0 as default};