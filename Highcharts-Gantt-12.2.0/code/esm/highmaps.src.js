/**
 * @license Highmaps JS v12.2.0 (2025-04-07)
 * @module highcharts/highmaps
 *
 * (c) 2011-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */
import * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_c57973fa__ from "./highcharts.src.js";
import * as __WEBPACK_EXTERNAL_MODULE__modules_map_src_js_1d091a0b__ from "./modules/map.src.js";
/******/ // The require scope
/******/ var __webpack_require__ = {};
/******/ 
/************************************************************************/
/******/ /* webpack/runtime/define property getters */
/******/ (() => {
/******/ 	// define getter functions for harmony exports
/******/ 	__webpack_require__.d = (exports, definition) => {
/******/ 		for(var key in definition) {
/******/ 			if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 				Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 			}
/******/ 		}
/******/ 	};
/******/ })();
/******/ 
/******/ /* webpack/runtime/hasOwnProperty shorthand */
/******/ (() => {
/******/ 	__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ })();
/******/ 
/************************************************************************/

;// external "./highcharts.src.js"
var x = (y) => {
	var x = {}; __webpack_require__.d(x,
    	y); return x
    } 
    var y = (x) => (() => (x))
    const external_highcharts_src_js_namespaceObject = x({ ["default"]: () => (__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_c57973fa__["default"]) });
;// external "./modules/map.src.js"
var map_src_js_x = (y) => {
	var x = {}; __webpack_require__.d(x,
    	y); return x
    } 
    var map_src_js_y = (x) => (() => (x))
    const map_src_js_namespaceObject = map_src_js_x({  });
;// ./code/es-modules/masters/highmaps.src.js




external_highcharts_src_js_namespaceObject["default"].product = 'Highmaps';
/* harmony default export */ const highmaps_src = (external_highcharts_src_js_namespaceObject["default"]);

export { highmaps_src as default };
