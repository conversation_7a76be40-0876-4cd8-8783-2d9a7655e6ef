/**
 * @license Highstock JS v12.2.0 (2025-04-07)
 * @module highcharts/highstock
 *
 * (c) 2009-2025 Torstein Honsi
 *
 * License: www.highcharts.com/license
 */
import * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_c57973fa__ from "./highcharts.src.js";
import * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_7a16137d__ from "./modules/stock.src.js";
/******/ // The require scope
/******/ var __webpack_require__ = {};
/******/ 
/************************************************************************/
/******/ /* webpack/runtime/define property getters */
/******/ (() => {
/******/ 	// define getter functions for harmony exports
/******/ 	__webpack_require__.d = (exports, definition) => {
/******/ 		for(var key in definition) {
/******/ 			if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 				Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 			}
/******/ 		}
/******/ 	};
/******/ })();
/******/ 
/******/ /* webpack/runtime/hasOwnProperty shorthand */
/******/ (() => {
/******/ 	__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ })();
/******/ 
/************************************************************************/

;// external "./highcharts.src.js"
var x = (y) => {
	var x = {}; __webpack_require__.d(x,
    	y); return x
    } 
    var y = (x) => (() => (x))
    const external_highcharts_src_js_namespaceObject = x({ ["default"]: () => (__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_c57973fa__["default"]) });
;// external "./modules/stock.src.js"
var stock_src_js_x = (y) => {
	var x = {}; __webpack_require__.d(x,
    	y); return x
    } 
    var stock_src_js_y = (x) => (() => (x))
    const stock_src_js_namespaceObject = stock_src_js_x({  });
;// ./code/es-modules/masters/highstock.src.js




external_highcharts_src_js_namespaceObject["default"].product = 'Highstock';
/* harmony default export */ const highstock_src = (external_highcharts_src_js_namespaceObject["default"]);

export { highstock_src as default };
