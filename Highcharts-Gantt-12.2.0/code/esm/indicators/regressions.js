import*as e from"../highcharts.js";import"../modules/stock.js";var t={};t.n=e=>{var s=e&&e.__esModule?()=>e.default:()=>e;return t.d(s,{a:s}),s},t.d=(e,s)=>{for(var r in s)t.o(s,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:s[r]})},t.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let s=e.default;var r=t.n(s);t.d({},{});let n=e.default.SeriesRegistry;var a=t.n(n);let{sma:i}=a().seriesTypes,{isArray:o,extend:l,merge:p}=r();class g extends i{getRegressionLineParameters(e,t){let s=this.options.params.index,r=function(e,t){return o(e)?e[t]:e},n=e.reduce(function(e,t){return t+e},0),a=t.reduce(function(e,t){return r(t,s)+e},0),i=n/e.length,l=a/t.length,p,g,u=0,d=0;for(g=0;g<e.length;g++)u+=(p=e[g]-i)*(r(t[g],s)-l),d+=Math.pow(p,2);let c=d?u/d:0;return{slope:c,intercept:l-c*i}}getEndPointY(e,t){return e.slope*t+e.intercept}transformXData(e,t){let s=e[0];return e.map(function(e){return(e-s)/t})}findClosestDistance(e){let t,s,r;for(r=1;r<e.length-1;r++)(t=e[r]-e[r-1])>0&&(void 0===s||t<s)&&(s=t);return s}getValues(e,t){let s,r,n,a,i,l,p,g,u,d=e.xData,c=e.yData,f=t.period,m={xData:[],yData:[],values:[]},h=this.options.params.xAxisUnit||this.findClosestDistance(d);for(r=f-1;r<=d.length-1;r++)n=r-f+1,a=r+1,i=d[r],p=d.slice(n,a),g=c.slice(n,a),u=this.transformXData(p,h),s=this.getRegressionLineParameters(u,g),l=this.getEndPointY(s,u[u.length-1]),m.values.push({regressionLineParameters:s,x:i,y:l}),o(m.xData)&&m.xData.push(i),o(m.yData)&&m.yData.push(l);return m}}g.defaultOptions=p(i.defaultOptions,{params:{xAxisUnit:null},tooltip:{valueDecimals:4}}),l(g.prototype,{nameBase:"Linear Regression Indicator"}),a().registerSeriesType("linearRegression",g);let{linearRegression:u}=a().seriesTypes,{extend:d,merge:c}=r();class f extends u{getEndPointY(e){return e.slope}}f.defaultOptions=c(u.defaultOptions),d(f.prototype,{nameBase:"Linear Regression Slope Indicator"}),a().registerSeriesType("linearRegressionSlope",f);let{linearRegression:m}=a().seriesTypes,{extend:h,merge:y}=r();class x extends m{getEndPointY(e){return e.intercept}}x.defaultOptions=y(m.defaultOptions),h(x.prototype,{nameBase:"Linear Regression Intercept Indicator"}),a().registerSeriesType("linearRegressionIntercept",x);let{linearRegression:R}=a().seriesTypes,{extend:D,merge:O}=r();class P extends R{slopeToAngle(e){return 180/Math.PI*Math.atan(e)}getEndPointY(e){return this.slopeToAngle(e.slope)}}P.defaultOptions=O(R.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span>{series.name}: <b>{point.y}\xb0</b><br/>'}}),D(P.prototype,{nameBase:"Linear Regression Angle Indicator"}),a().registerSeriesType("linearRegressionAngle",P);let T=r();export{T as default};