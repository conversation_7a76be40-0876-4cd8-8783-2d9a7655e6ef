{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/acceleration-bands\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 <PERSON>\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_b3d80146__ from \"../modules/stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"../modules/stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const stock_src_js_namespaceObject = x({  });\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/MultipleLinesComposition.js\n/**\n *\n *  (c) 2010-2025 Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: { prototype: smaProto } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, error, merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar MultipleLinesComposition;\n(function (MultipleLinesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Additional lines DOCS names. Elements of linesApiNames array should\n     * be consistent with DOCS line names defined in your implementation.\n     * Notice that linesApiNames should have decreased amount of elements\n     * relative to pointArrayMap (without pointValKey).\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const linesApiNames = ['bottomLine'];\n    /**\n     * Lines ids. Required to plot appropriate amount of lines.\n     * Notice that pointArrayMap should have more elements than\n     * linesApiNames, because it contains main line and additional lines ids.\n     * Also it should be consistent with amount of lines calculated in\n     * getValues method from your implementation.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const pointArrayMap = ['top', 'bottom'];\n    /**\n     * Names of the lines, between which the area should be plotted.\n     * If the drawing of the area should\n     * be disabled for some indicators, leave this option as an empty array.\n     * Names should be the same as the names in the pointArrayMap.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const areaLinesNames = ['top'];\n    /**\n     * Main line id.\n     *\n     * @private\n     * @type {string}\n     */\n    const pointValKey = 'top';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition useful for all indicators that have more than one line.\n     * Compose it with your implementation where you will provide the\n     * `getValues` method appropriate to your indicator and `pointArrayMap`,\n     * `pointValKey`, `linesApiNames` properties. Notice that `pointArrayMap`\n     * should be consistent with the amount of lines calculated in the\n     * `getValues` method.\n     *\n     * @private\n     */\n    function compose(IndicatorClass) {\n        const proto = IndicatorClass.prototype;\n        proto.linesApiNames = (proto.linesApiNames ||\n            linesApiNames.slice());\n        proto.pointArrayMap = (proto.pointArrayMap ||\n            pointArrayMap.slice());\n        proto.pointValKey = (proto.pointValKey ||\n            pointValKey);\n        proto.areaLinesNames = (proto.areaLinesNames ||\n            areaLinesNames.slice());\n        proto.drawGraph = indicatorDrawGraph;\n        proto.getGraphPath = indicatorGetGraphPath;\n        proto.toYData = indicatorToYData;\n        proto.translate = indicatorTranslate;\n        return IndicatorClass;\n    }\n    MultipleLinesComposition.compose = compose;\n    /**\n     * Generate the API name of the line\n     *\n     * @private\n     * @param propertyName name of the line\n     */\n    function getLineName(propertyName) {\n        return ('plot' +\n            propertyName.charAt(0).toUpperCase() +\n            propertyName.slice(1));\n    }\n    /**\n     * Create translatedLines Collection based on pointArrayMap.\n     *\n     * @private\n     * @param {string} [excludedValue]\n     *        Main line id\n     * @return {Array<string>}\n     *         Returns translated lines names without excluded value.\n     */\n    function getTranslatedLinesNames(indicator, excludedValue) {\n        const translatedLines = [];\n        (indicator.pointArrayMap || []).forEach((propertyName) => {\n            if (propertyName !== excludedValue) {\n                translatedLines.push(getLineName(propertyName));\n            }\n        });\n        return translatedLines;\n    }\n    /**\n     * Draw main and additional lines.\n     *\n     * @private\n     */\n    function indicatorDrawGraph() {\n        const indicator = this, pointValKey = indicator.pointValKey, linesApiNames = indicator.linesApiNames, areaLinesNames = indicator.areaLinesNames, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, \n        // Additional lines point place holders:\n        secondaryLines = [], secondaryLinesNames = getTranslatedLinesNames(indicator, pointValKey);\n        let pointsLength = mainLinePoints.length, point;\n        // Generate points for additional lines:\n        secondaryLinesNames.forEach((plotLine, index) => {\n            // Create additional lines point place holders\n            secondaryLines[index] = [];\n            while (pointsLength--) {\n                point = mainLinePoints[pointsLength];\n                secondaryLines[index].push({\n                    x: point.x,\n                    plotX: point.plotX,\n                    plotY: point[plotLine],\n                    isNull: !defined(point[plotLine])\n                });\n            }\n            pointsLength = mainLinePoints.length;\n        });\n        // Modify options and generate area fill:\n        if (indicator.userOptions.fillColor && areaLinesNames.length) {\n            const index = secondaryLinesNames.indexOf(getLineName(areaLinesNames[0])), secondLinePoints = secondaryLines[index], firstLinePoints = areaLinesNames.length === 1 ?\n                mainLinePoints :\n                secondaryLines[secondaryLinesNames.indexOf(getLineName(areaLinesNames[1]))], originalColor = indicator.color;\n            indicator.points = firstLinePoints;\n            indicator.nextPoints = secondLinePoints;\n            indicator.color = indicator.userOptions.fillColor;\n            indicator.options = merge(mainLinePoints, gappedExtend);\n            indicator.graph = indicator.area;\n            indicator.fillGraph = true;\n            smaProto.drawGraph.call(indicator);\n            indicator.area = indicator.graph;\n            // Clean temporary properties:\n            delete indicator.nextPoints;\n            delete indicator.fillGraph;\n            indicator.color = originalColor;\n        }\n        // Modify options and generate additional lines:\n        linesApiNames.forEach((lineName, i) => {\n            if (secondaryLines[i]) {\n                indicator.points = secondaryLines[i];\n                if (mainLineOptions[lineName]) {\n                    indicator.options = merge(mainLineOptions[lineName].styles, gappedExtend);\n                }\n                else {\n                    error('Error: \"There is no ' + lineName +\n                        ' in DOCS options declared. Check if linesApiNames' +\n                        ' are consistent with your DOCS line names.\"');\n                }\n                indicator.graph = indicator['graph' + lineName];\n                smaProto.drawGraph.call(indicator);\n                // Now save lines:\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            else {\n                error('Error: \"' + lineName + ' doesn\\'t have equivalent ' +\n                    'in pointArrayMap. To many elements in linesApiNames ' +\n                    'relative to pointArrayMap.\"');\n            }\n        });\n        // Restore options and draw a main line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        smaProto.drawGraph.call(indicator);\n    }\n    /**\n     * Create the path based on points provided as argument.\n     * If indicator.nextPoints option is defined, create the areaFill.\n     *\n     * @private\n     * @param points Points on which the path should be created\n     */\n    function indicatorGetGraphPath(points) {\n        let areaPath, path = [], higherAreaPath = [];\n        points = points || this.points;\n        // Render Span\n        if (this.fillGraph && this.nextPoints) {\n            areaPath = smaProto.getGraphPath.call(this, this.nextPoints);\n            if (areaPath && areaPath.length) {\n                areaPath[0][0] = 'L';\n                path = smaProto.getGraphPath.call(this, points);\n                higherAreaPath = areaPath.slice(0, path.length);\n                // Reverse points, so that the areaFill will start from the end:\n                for (let i = higherAreaPath.length - 1; i >= 0; i--) {\n                    path.push(higherAreaPath[i]);\n                }\n            }\n        }\n        else {\n            path = smaProto.getGraphPath.apply(this, arguments);\n        }\n        return path;\n    }\n    /**\n     * @private\n     * @param {Highcharts.Point} point\n     *        Indicator point\n     * @return {Array<number>}\n     *         Returns point Y value for all lines\n     */\n    function indicatorToYData(point) {\n        const pointColl = [];\n        (this.pointArrayMap || []).forEach((propertyName) => {\n            pointColl.push(point[propertyName]);\n        });\n        return pointColl;\n    }\n    /**\n     * Add lines plot pixel values.\n     *\n     * @private\n     */\n    function indicatorTranslate() {\n        const pointArrayMap = this.pointArrayMap;\n        let LinesNames = [], value;\n        LinesNames = getTranslatedLinesNames(this);\n        smaProto.translate.apply(this, arguments);\n        this.points.forEach((point) => {\n            pointArrayMap.forEach((propertyName, i) => {\n                value = point[propertyName];\n                // If the modifier, like for example compare exists,\n                // modified the original value by that method, #15867.\n                if (this.dataModify) {\n                    value = this.dataModify.modifyValue(value);\n                }\n                if (value !== null) {\n                    point[LinesNames[i]] = this.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n})(MultipleLinesComposition || (MultipleLinesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Indicators_MultipleLinesComposition = (MultipleLinesComposition);\n\n;// ./code/es-modules/Stock/Indicators/ABands/ABandsIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, extend, merge: ABandsIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction getBaseForBand(low, high, factor) {\n    return (((correctFloat(high - low)) /\n        ((correctFloat(high + low)) / 2)) * 1000) * factor;\n}\n/**\n * @private\n */\nfunction getPointUB(high, base) {\n    return high * (correctFloat(1 + 2 * base));\n}\n/**\n * @private\n */\nfunction getPointLB(low, base) {\n    return low * (correctFloat(1 - 2 * base));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ABands series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.abands\n *\n * @augments Highcharts.Series\n */\nclass ABandsIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, factor = params.factor, index = params.index, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // Upperbands\n        UB = [], \n        // Lowerbands\n        LB = [], \n        // ABANDS array structure:\n        // 0-date, 1-top line, 2-middle line, 3-bottom line\n        ABANDS = [], low = 2, high = 1, xData = [], yData = [];\n        // Middle line, top line and bottom line\n        let ML, TL, BL, date, bandBase, pointSMA, ubSMA, lbSMA, slicedX, slicedY, i;\n        if (yValLen < period) {\n            return;\n        }\n        for (i = 0; i <= yValLen; i++) {\n            // Get UB and LB values of every point. This condition\n            // is necessary, because there is a need to calculate current\n            // UB nad LB values simultaneously with given period SMA\n            // in one for loop.\n            if (i < yValLen) {\n                bandBase = getBaseForBand(yVal[i][low], yVal[i][high], factor);\n                UB.push(getPointUB(yVal[i][high], bandBase));\n                LB.push(getPointLB(yVal[i][low], bandBase));\n            }\n            if (i >= period) {\n                slicedX = xVal.slice(i - period, i);\n                slicedY = yVal.slice(i - period, i);\n                ubSMA = super.getValues.call(this, {\n                    xData: slicedX,\n                    yData: UB.slice(i - period, i)\n                }, {\n                    period: period\n                });\n                lbSMA = super.getValues.call(this, {\n                    xData: slicedX,\n                    yData: LB.slice(i - period, i)\n                }, {\n                    period: period\n                });\n                pointSMA = super.getValues.call(this, {\n                    xData: slicedX,\n                    yData: slicedY\n                }, {\n                    period: period,\n                    index: index\n                });\n                date = pointSMA.xData[0];\n                TL = ubSMA.yData[0];\n                BL = lbSMA.yData[0];\n                ML = pointSMA.yData[0];\n                ABANDS.push([date, TL, ML, BL]);\n                xData.push(date);\n                yData.push([TL, ML, BL]);\n            }\n        }\n        return {\n            values: ABANDS,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Acceleration bands (ABANDS). This series requires the `linkedTo` option\n * to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/acceleration-bands\n *         Acceleration Bands\n *\n * @extends      plotOptions.sma\n * @mixes        Highcharts.MultipleLinesMixin\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking,\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/acceleration-bands\n * @optionparent plotOptions.abands\n */\nABandsIndicator.defaultOptions = ABandsIndicator_merge(SMAIndicator.defaultOptions, {\n    /**\n     * Option for fill color between lines in Accelleration bands Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      Background fill between lines.\n     *\n     * @type {Highcharts.Color}\n     * @since 9.3.2\n     * @apioption plotOptions.abands.fillColor\n     *\n     */\n    params: {\n        period: 20,\n        /**\n         * The algorithms factor value used to calculate bands.\n         *\n         * @product highstock\n         */\n        factor: 0.001,\n        index: 3\n    },\n    lineWidth: 1,\n    topLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1\n        }\n    },\n    bottomLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nextend(ABandsIndicator.prototype, {\n    areaLinesNames: ['top', 'bottom'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    nameBase: 'Acceleration Bands',\n    nameComponents: ['period', 'factor'],\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(ABandsIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('abands', ABandsIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ABands_ABandsIndicator = ((/* unused pure expression or super */ null && (ABandsIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An Acceleration bands indicator. If the [type](#series.abands.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.abands\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *            stacking, showInNavigator,\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/acceleration-bands\n * @apioption series.abands\n */\n''; // To include the above in jsdoc\n\n;// ./code/es-modules/masters/indicators/acceleration-bands.js\n\n\n\n\n\n/* harmony default export */ const acceleration_bands_src = ((external_highcharts_src_js_default_default()));\n\nexport { acceleration_bands_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "MultipleLinesComposition", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "sma", "sma<PERSON><PERSON><PERSON>", "seriesTypes", "defined", "error", "merge", "linesApiNames", "pointArrayMap", "areaLinesNames", "getLineName", "propertyName", "char<PERSON>t", "toUpperCase", "slice", "getTranslatedLinesNames", "indicator", "excludedValue", "translatedLines", "for<PERSON>ach", "push", "indicatorDrawGraph", "pointVal<PERSON>ey", "mainLinePoints", "points", "mainLineOptions", "options", "mainLinePath", "graph", "gappedExtend", "gapSize", "secondaryLines", "secondaryLinesNames", "pointsLength", "length", "point", "plotLine", "index", "x", "plotX", "plotY", "isNull", "userOptions", "fillColor", "secondLinePoints", "indexOf", "firstLinePoints", "originalColor", "color", "nextPoints", "area", "fillGraph", "drawGraph", "lineName", "i", "styles", "indicatorGetGraphPath", "areaPath", "path", "higherAreaPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "arguments", "indicatorToYData", "pointColl", "indicatorTranslate", "LinesNames", "value", "translate", "dataModify", "modifyValue", "yAxis", "toPixels", "compose", "IndicatorClass", "proto", "toYData", "Indicators_MultipleLinesComposition", "SMAIndicator", "correctFloat", "extend", "ABandsIndicator_merge", "ABandsIndicator", "getV<PERSON>ues", "series", "params", "ML", "TL", "BL", "date", "bandBase", "pointSMA", "ubSMA", "lbSMA", "slicedX", "slicedY", "period", "factor", "xVal", "xData", "yVal", "yData", "yValLen", "UB", "LB", "ABANDS", "low", "high", "values", "defaultOptions", "lineWidth", "topLine", "bottomLine", "dataGrouping", "approximation", "nameBase", "nameComponents", "registerSeriesType", "acceleration_bands_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,AAChG,OAA4E,yBAA0B,CAE7F,IAmELC,EAnESC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDtB,EAAwD,OAAU,CAC7H,IAAIuB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAIiC,CAAG,GAE9C,IAAMiB,EAAoExB,EAAwD,OAAU,CAACyB,cAAc,CAC3J,IAAIC,EAAyExB,EAAoBC,CAAC,CAACqB,GAanG,GAAM,CAAEG,IAAK,CAAER,UAAWS,CAAQ,CAAE,CAAE,CAAG,AAACF,IAA6DG,WAAW,CAE5G,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAIT,KAOnC,AAAC,SAAUtB,CAAwB,EAoB/B,IAAMgC,EAAgB,CAAC,aAAa,CAW9BC,EAAgB,CAAC,MAAO,SAAS,CAUjCC,EAAiB,CAAC,MAAM,CA8C9B,SAASC,EAAYC,CAAY,EAC7B,MAAQ,OACJA,EAAaC,MAAM,CAAC,GAAGC,WAAW,GAClCF,EAAaG,KAAK,CAAC,EAC3B,CAUA,SAASC,EAAwBC,CAAS,CAAEC,CAAa,EACrD,IAAMC,EAAkB,EAAE,CAM1B,MALA,AAACF,CAAAA,EAAUR,aAAa,EAAI,EAAE,AAAD,EAAGW,OAAO,CAAC,AAACR,IACjCA,IAAiBM,GACjBC,EAAgBE,IAAI,CAACV,EAAYC,GAEzC,GACOO,CACX,CAMA,SAASG,IACL,IAAML,EAAY,IAAI,CAAEM,EAAcN,EAAUM,WAAW,CAAEf,EAAgBS,EAAUT,aAAa,CAAEE,EAAiBO,EAAUP,cAAc,CAAEc,EAAiBP,EAAUQ,MAAM,CAAEC,EAAkBT,EAAUU,OAAO,CAAEC,EAAeX,EAAUY,KAAK,CAAEC,EAAe,CACpQH,QAAS,CACLI,QAASL,EAAgBK,OAAO,AACpC,CACJ,EAEAC,EAAiB,EAAE,CAAEC,EAAsBjB,EAAwBC,EAAWM,GAC1EW,EAAeV,EAAeW,MAAM,CAAEC,EAiB1C,GAfAH,EAAoBb,OAAO,CAAC,CAACiB,EAAUC,KAGnC,IADAN,CAAc,CAACM,EAAM,CAAG,EAAE,CACnBJ,KACHE,EAAQZ,CAAc,CAACU,EAAa,CACpCF,CAAc,CAACM,EAAM,CAACjB,IAAI,CAAC,CACvBkB,EAAGH,EAAMG,CAAC,CACVC,MAAOJ,EAAMI,KAAK,CAClBC,MAAOL,CAAK,CAACC,EAAS,CACtBK,OAAQ,CAACrC,EAAQ+B,CAAK,CAACC,EAAS,CACpC,GAEJH,EAAeV,EAAeW,MAAM,AACxC,GAEIlB,EAAU0B,WAAW,CAACC,SAAS,EAAIlC,EAAeyB,MAAM,CAAE,CAC1D,IAA2EU,EAAmBb,CAAc,CAA9FC,EAAoBa,OAAO,CAACnC,EAAYD,CAAc,CAAC,EAAE,GAA4C,CAAEqC,EAAkBrC,AAA0B,IAA1BA,EAAeyB,MAAM,CACxJX,EACAQ,CAAc,CAACC,EAAoBa,OAAO,CAACnC,EAAYD,CAAc,CAAC,EAAE,GAAG,CAAEsC,EAAgB/B,EAAUgC,KAAK,AAChHhC,CAAAA,EAAUQ,MAAM,CAAGsB,EACnB9B,EAAUiC,UAAU,CAAGL,EACvB5B,EAAUgC,KAAK,CAAGhC,EAAU0B,WAAW,CAACC,SAAS,CACjD3B,EAAUU,OAAO,CAAGpB,EAAMiB,EAAgBM,GAC1Cb,EAAUY,KAAK,CAAGZ,EAAUkC,IAAI,CAChClC,EAAUmC,SAAS,CAAG,CAAA,EACtBjD,EAASkD,SAAS,CAACzD,IAAI,CAACqB,GACxBA,EAAUkC,IAAI,CAAGlC,EAAUY,KAAK,CAEhC,OAAOZ,EAAUiC,UAAU,CAC3B,OAAOjC,EAAUmC,SAAS,CAC1BnC,EAAUgC,KAAK,CAAGD,CACtB,CAEAxC,EAAcY,OAAO,CAAC,CAACkC,EAAUC,KACzBvB,CAAc,CAACuB,EAAE,EACjBtC,EAAUQ,MAAM,CAAGO,CAAc,CAACuB,EAAE,CAChC7B,CAAe,CAAC4B,EAAS,CACzBrC,EAAUU,OAAO,CAAGpB,EAAMmB,CAAe,CAAC4B,EAAS,CAACE,MAAM,CAAE1B,GAG5DxB,EAAM,uBAAyBgD,EAAzB,gGAIVrC,EAAUY,KAAK,CAAGZ,CAAS,CAAC,QAAUqC,EAAS,CAC/CnD,EAASkD,SAAS,CAACzD,IAAI,CAACqB,GAExBA,CAAS,CAAC,QAAUqC,EAAS,CAAGrC,EAAUY,KAAK,EAG/CvB,EAAM,WAAagD,EAAb,4GAId,GAEArC,EAAUQ,MAAM,CAAGD,EACnBP,EAAUU,OAAO,CAAGD,EACpBT,EAAUY,KAAK,CAAGD,EAClBzB,EAASkD,SAAS,CAACzD,IAAI,CAACqB,EAC5B,CAQA,SAASwC,EAAsBhC,CAAM,EACjC,IAAIiC,EAAUC,EAAO,EAAE,CAAEC,EAAiB,EAAE,CAG5C,GAFAnC,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1B,IAAI,CAAC2B,SAAS,EAAI,IAAI,CAACF,UAAU,CAEjC,CAAA,GAAIQ,AADJA,CAAAA,EAAWvD,EAAS0D,YAAY,CAACjE,IAAI,CAAC,IAAI,CAAE,IAAI,CAACsD,UAAU,CAAA,GAC3CQ,EAASvB,MAAM,CAAE,CAC7BuB,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAG,IACjBC,EAAOxD,EAAS0D,YAAY,CAACjE,IAAI,CAAC,IAAI,CAAE6B,GACxCmC,EAAiBF,EAAS3C,KAAK,CAAC,EAAG4C,EAAKxB,MAAM,EAE9C,IAAK,IAAIoB,EAAIK,EAAezB,MAAM,CAAG,EAAGoB,GAAK,EAAGA,IAC5CI,EAAKtC,IAAI,CAACuC,CAAc,CAACL,EAAE,CAEnC,CAAA,MAGAI,EAAOxD,EAAS0D,YAAY,CAACC,KAAK,CAAC,IAAI,CAAEC,WAE7C,OAAOJ,CACX,CAQA,SAASK,EAAiB5B,CAAK,EAC3B,IAAM6B,EAAY,EAAE,CAIpB,MAHA,AAAC,CAAA,IAAI,CAACxD,aAAa,EAAI,EAAE,AAAD,EAAGW,OAAO,CAAC,AAACR,IAChCqD,EAAU5C,IAAI,CAACe,CAAK,CAACxB,EAAa,CACtC,GACOqD,CACX,CAMA,SAASC,IACL,IAAMzD,EAAgB,IAAI,CAACA,aAAa,CACpC0D,EAAa,EAAE,CAAEC,EACrBD,EAAanD,EAAwB,IAAI,EACzCb,EAASkE,SAAS,CAACP,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACtC,MAAM,CAACL,OAAO,CAAC,AAACgB,IACjB3B,EAAcW,OAAO,CAAC,CAACR,EAAc2C,KACjCa,EAAQhC,CAAK,CAACxB,EAAa,CAGvB,IAAI,CAAC0D,UAAU,EACfF,CAAAA,EAAQ,IAAI,CAACE,UAAU,CAACC,WAAW,CAACH,EAAK,EAE/B,OAAVA,GACAhC,CAAAA,CAAK,CAAC+B,CAAU,CAACZ,EAAE,CAAC,CAAG,IAAI,CAACiB,KAAK,CAACC,QAAQ,CAACL,EAAO,CAAA,EAAI,CAE9D,EACJ,EACJ,CA3KA5F,EAAyBkG,OAAO,CAhBhC,SAAiBC,CAAc,EAC3B,IAAMC,EAAQD,EAAejF,SAAS,CAatC,OAZAkF,EAAMpE,aAAa,CAAIoE,EAAMpE,aAAa,EACtCA,EAAcO,KAAK,GACvB6D,EAAMnE,aAAa,CAAImE,EAAMnE,aAAa,EACtCA,EAAcM,KAAK,GACvB6D,EAAMrD,WAAW,CAAIqD,EAAMrD,WAAW,EAtBtB,MAwBhBqD,EAAMlE,cAAc,CAAIkE,EAAMlE,cAAc,EACxCA,EAAeK,KAAK,GACxB6D,EAAMvB,SAAS,CAAG/B,EAClBsD,EAAMf,YAAY,CAAGJ,EACrBmB,EAAMC,OAAO,CAAGb,EAChBY,EAAMP,SAAS,CAAGH,EACXS,CACX,CA6KJ,EAAGnG,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GAM/B,IAAMsG,EAAuCtG,EAapE,CAAE0B,IAAK6E,CAAY,CAAE,CAAG,AAAC9E,IAA6DG,WAAW,CAEjG,CAAE4E,aAAAA,CAAY,CAAEC,OAAAA,CAAM,CAAE1E,MAAO2E,CAAqB,CAAE,CAAIpF,GAuChE,OAAMqF,UAAwBJ,EAM1BK,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IASIC,EAAIC,EAAIC,EAAIC,EAAMC,EAAUC,EAAUC,EAAOC,EAAOC,EAASC,EAASzC,EATpE0C,EAASX,EAAOW,MAAM,CAAEC,EAASZ,EAAOY,MAAM,CAAE5D,EAAQgD,EAAOhD,KAAK,CAAE6D,EAAOd,EAAOe,KAAK,CAAEC,EAAOhB,EAAOiB,KAAK,CAAEC,EAAUF,EAAOA,EAAKlE,MAAM,CAAG,EAErJqE,EAAK,EAAE,CAEPC,EAAK,EAAE,CAGPC,EAAS,EAAE,CAAqBN,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CAGtD,IAAIC,CAAAA,EAAUN,CAAK,GAGnB,IAAK1C,EAAI,EAAGA,GAAKgD,EAAShD,IAAK,CAK3B,GAAIA,EAAIgD,EAAS,KAvDLI,EAAKC,EAALD,EAwDkBN,CAAI,CAAC9C,EAAE,CAZtB,EAY2B,CAAtCoC,EAvDL,AAAGX,EAAa4B,CADEA,EAwD2BP,CAAI,CAAC9C,EAAE,CAZ1B,EAYgC,EAvDnCoD,GACzB,CAAA,AAAC3B,EAAa4B,EAAOD,GAAQ,CAAA,EAAM,IAsD2BT,EACvDM,EAAGnF,IAAI,CAAYgF,CAAI,CAAC9C,EAAE,CAbL,EAaW,CAjD7ByB,EAAa,EAAI,EAiDcW,IAClCc,EAAGpF,IAAI,CAAYgF,CAAI,CAAC9C,EAAE,CAdf,EAcoB,CA5C7ByB,EAAa,EAAI,EA4CcW,GACrC,CACIpC,GAAK0C,IACLF,EAAUI,EAAKpF,KAAK,CAACwC,EAAI0C,EAAQ1C,GACjCyC,EAAUK,EAAKtF,KAAK,CAACwC,EAAI0C,EAAQ1C,GACjCsC,EAAQ,KAAK,CAACT,UAAUxF,IAAI,CAAC,IAAI,CAAE,CAC/BwG,MAAOL,EACPO,MAAOE,EAAGzF,KAAK,CAACwC,EAAI0C,EAAQ1C,EAChC,EAAG,CACC0C,OAAQA,CACZ,GACAH,EAAQ,KAAK,CAACV,UAAUxF,IAAI,CAAC,IAAI,CAAE,CAC/BwG,MAAOL,EACPO,MAAOG,EAAG1F,KAAK,CAACwC,EAAI0C,EAAQ1C,EAChC,EAAG,CACC0C,OAAQA,CACZ,GAQAP,EAAOE,AAPPA,CAAAA,EAAW,KAAK,CAACR,UAAUxF,IAAI,CAAC,IAAI,CAAE,CAClCwG,MAAOL,EACPO,MAAON,CACX,EAAG,CACCC,OAAQA,EACR3D,MAAOA,CACX,EAAC,EACe8D,KAAK,CAAC,EAAE,CACxBZ,EAAKK,EAAMS,KAAK,CAAC,EAAE,CACnBb,EAAKK,EAAMQ,KAAK,CAAC,EAAE,CACnBf,EAAKK,EAASU,KAAK,CAAC,EAAE,CACtBI,EAAOrF,IAAI,CAAC,CAACqE,EAAMF,EAAID,EAAIE,EAAG,EAC9BW,EAAM/E,IAAI,CAACqE,GACXY,EAAMjF,IAAI,CAAC,CAACmE,EAAID,EAAIE,EAAG,EAE/B,CACA,MAAO,CACHoB,OAAQH,EACRN,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CA0BAnB,EAAgB2B,cAAc,CAAG5B,EAAsBH,EAAa+B,cAAc,CAAE,CAYhFxB,OAAQ,CACJW,OAAQ,GAMRC,OAAQ,KACR5D,MAAO,CACX,EACAyE,UAAW,EACXC,QAAS,CACLxD,OAAQ,CAIJuD,UAAW,CACf,CACJ,EACAE,WAAY,CACRzD,OAAQ,CAIJuD,UAAW,CACf,CACJ,EACAG,aAAc,CACVC,cAAe,UACnB,CACJ,GACAlC,EAAOE,EAAgBzF,SAAS,CAAE,CAC9BgB,eAAgB,CAAC,MAAO,SAAS,CACjCF,cAAe,CAAC,UAAW,aAAa,CACxC4G,SAAU,qBACVC,eAAgB,CAAC,SAAU,SAAS,CACpC5G,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1Cc,YAAa,QACjB,GACAuD,EAAoCJ,OAAO,CAACS,GAC5ClF,IAA4DqH,kBAAkB,CAAC,SAAUnC,GAmC5D,IAAMoC,EAA2BzH,WAErDyH,KAA0BC,OAAO"}