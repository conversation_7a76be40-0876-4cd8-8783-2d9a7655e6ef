{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/volume-by-price\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Paweł Dalek\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_b3d80146__ from \"../modules/stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"../modules/stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const stock_src_js_namespaceObject = x({  });\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/VBP/VBPPoint.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { sma: { prototype: { pointClass: SMAPoint } } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n/* *\n *\n *  Class\n *\n * */\nclass VBPPoint extends SMAPoint {\n    // Required for destroying negative part of volume\n    destroy() {\n        // @todo: this.negativeGraphic doesn't seem to be used anywhere\n        if (this.negativeGraphic) {\n            this.negativeGraphic = this.negativeGraphic.destroy();\n        }\n        super.destroy.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VBP_VBPPoint = (VBPPoint);\n\n;// ./code/es-modules/Stock/Indicators/VBP/VBPIndicator.js\n/* *\n *\n *  (c) 2010-2025 Paweł Dalek\n *\n *  Volume By Price (VBP) indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { animObject } = (external_highcharts_src_js_default_default());\n\nconst { noop } = (external_highcharts_src_js_default_default());\n\nconst { column: { prototype: columnProto }, sma: SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent, arrayMax, arrayMin, correctFloat, defined, error, extend, isArray, merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst abs = Math.abs;\n/* *\n *\n *  Functions\n *\n * */\n// Utils\n/**\n * @private\n */\nfunction arrayExtremesOHLC(data) {\n    const dataLength = data.length;\n    let min = data[0][3], max = min, i = 1, currentPoint;\n    for (; i < dataLength; i++) {\n        currentPoint = data[i][3];\n        if (currentPoint < min) {\n            min = currentPoint;\n        }\n        if (currentPoint > max) {\n            max = currentPoint;\n        }\n    }\n    return {\n        min: min,\n        max: max\n    };\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Volume By Price (VBP) series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.vbp\n *\n * @augments Highcharts.Series\n */\nclass VBPIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(chart, options) {\n        const indicator = this;\n        // Series.update() sends data that is not necessary as everything is\n        // calculated in getValues(), #17007\n        delete options.data;\n        super.init.apply(indicator, arguments);\n        // Only after series are linked add some additional logic/properties.\n        const unbinder = addEvent(this.chart.constructor, 'afterLinkSeries', function () {\n            // Protection for a case where the indicator is being updated,\n            // for a brief moment the indicator is deleted.\n            if (indicator.options) {\n                const params = indicator.options.params, baseSeries = indicator.linkedParent, volumeSeries = chart.get(params.volumeSeriesID);\n                indicator.addCustomEvents(baseSeries, volumeSeries);\n            }\n            unbinder();\n        }, {\n            order: 1\n        });\n        return indicator;\n    }\n    // Adds events related with removing series\n    addCustomEvents(baseSeries, volumeSeries) {\n        const indicator = this, toEmptyIndicator = () => {\n            indicator.chart.redraw();\n            indicator.setData([]);\n            indicator.zoneStarts = [];\n            if (indicator.zoneLinesSVG) {\n                indicator.zoneLinesSVG = indicator.zoneLinesSVG.destroy();\n            }\n        };\n        // If base series is deleted, indicator series data is filled with\n        // an empty array\n        indicator.dataEventsToUnbind.push(addEvent(baseSeries, 'remove', function () {\n            toEmptyIndicator();\n        }));\n        // If volume series is deleted, indicator series data is filled with\n        // an empty array\n        if (volumeSeries) {\n            indicator.dataEventsToUnbind.push(addEvent(volumeSeries, 'remove', function () {\n                toEmptyIndicator();\n            }));\n        }\n        return indicator;\n    }\n    // Initial animation\n    animate(init) {\n        const series = this, inverted = series.chart.inverted, group = series.group, attr = {};\n        if (!init && group) {\n            const position = inverted ? series.yAxis.top : series.xAxis.left;\n            if (inverted) {\n                group['forceAnimate:translateY'] = true;\n                attr.translateY = position;\n            }\n            else {\n                group['forceAnimate:translateX'] = true;\n                attr.translateX = position;\n            }\n            group.animate(attr, extend(animObject(series.options.animation), {\n                step: function (val, fx) {\n                    series.group.attr({\n                        scaleX: Math.max(0.001, fx.pos)\n                    });\n                }\n            }));\n        }\n    }\n    drawPoints() {\n        const indicator = this;\n        if (indicator.options.volumeDivision.enabled) {\n            indicator.posNegVolume(true, true);\n            columnProto.drawPoints.apply(indicator, arguments);\n            indicator.posNegVolume(false, false);\n        }\n        columnProto.drawPoints.apply(indicator, arguments);\n    }\n    // Function responsible for dividing volume into positive and negative\n    posNegVolume(initVol, pos) {\n        const indicator = this, signOrder = pos ?\n            ['positive', 'negative'] :\n            ['negative', 'positive'], volumeDivision = indicator.options.volumeDivision, pointLength = indicator.points.length;\n        let posWidths = [], negWidths = [], i = 0, pointWidth, priceZone, wholeVol, point;\n        if (initVol) {\n            indicator.posWidths = posWidths;\n            indicator.negWidths = negWidths;\n        }\n        else {\n            posWidths = indicator.posWidths;\n            negWidths = indicator.negWidths;\n        }\n        for (; i < pointLength; i++) {\n            point = indicator.points[i];\n            point[signOrder[0] + 'Graphic'] = point.graphic;\n            point.graphic = point[signOrder[1] + 'Graphic'];\n            if (initVol) {\n                pointWidth = point.shapeArgs.width;\n                priceZone = indicator.priceZones[i];\n                wholeVol = priceZone.wholeVolumeData;\n                if (wholeVol) {\n                    posWidths.push(pointWidth / wholeVol * priceZone.positiveVolumeData);\n                    negWidths.push(pointWidth / wholeVol * priceZone.negativeVolumeData);\n                }\n                else {\n                    posWidths.push(0);\n                    negWidths.push(0);\n                }\n            }\n            point.color = pos ?\n                volumeDivision.styles.positiveColor :\n                volumeDivision.styles.negativeColor;\n            point.shapeArgs.width = pos ?\n                indicator.posWidths[i] :\n                indicator.negWidths[i];\n            point.shapeArgs.x = pos ?\n                point.shapeArgs.x :\n                indicator.posWidths[i];\n        }\n    }\n    translate() {\n        const indicator = this, options = indicator.options, chart = indicator.chart, yAxis = indicator.yAxis, yAxisMin = yAxis.min, zoneLinesOptions = indicator.options.zoneLines, priceZones = (indicator.priceZones);\n        let yBarOffset = 0, volumeDataArray, maxVolume, primalBarWidth, barHeight, barHeightP, oldBarHeight, barWidth, pointPadding, chartPlotTop, barX, barY;\n        columnProto.translate.apply(indicator);\n        const indicatorPoints = indicator.points;\n        // Do translate operation when points exist\n        if (indicatorPoints.length) {\n            pointPadding = options.pointPadding < 0.5 ?\n                options.pointPadding :\n                0.1;\n            volumeDataArray = indicator.volumeDataArray;\n            maxVolume = arrayMax(volumeDataArray);\n            primalBarWidth = chart.plotWidth / 2;\n            chartPlotTop = chart.plotTop;\n            barHeight = abs(yAxis.toPixels(yAxisMin) -\n                yAxis.toPixels(yAxisMin + indicator.rangeStep));\n            oldBarHeight = abs(yAxis.toPixels(yAxisMin) -\n                yAxis.toPixels(yAxisMin + indicator.rangeStep));\n            if (pointPadding) {\n                barHeightP = abs(barHeight * (1 - 2 * pointPadding));\n                yBarOffset = abs((barHeight - barHeightP) / 2);\n                barHeight = abs(barHeightP);\n            }\n            indicatorPoints.forEach(function (point, index) {\n                barX = point.barX = point.plotX = 0;\n                barY = point.plotY = (yAxis.toPixels(priceZones[index].start) -\n                    chartPlotTop -\n                    (yAxis.reversed ?\n                        (barHeight - oldBarHeight) :\n                        barHeight) -\n                    yBarOffset);\n                barWidth = correctFloat(primalBarWidth *\n                    priceZones[index].wholeVolumeData / maxVolume);\n                point.pointWidth = barWidth;\n                point.shapeArgs = indicator.crispCol.apply(// eslint-disable-line no-useless-call\n                indicator, [barX, barY, barWidth, barHeight]);\n                point.volumeNeg = priceZones[index].negativeVolumeData;\n                point.volumePos = priceZones[index].positiveVolumeData;\n                point.volumeAll = priceZones[index].wholeVolumeData;\n            });\n            if (zoneLinesOptions.enabled) {\n                indicator.drawZones(chart, yAxis, indicator.zoneStarts, zoneLinesOptions.styles);\n            }\n        }\n    }\n    getExtremes() {\n        const prevCompare = this.options.compare, prevCumulative = this.options.cumulative;\n        let ret;\n        // Temporarily disable cumulative and compare while getting the extremes\n        if (this.options.compare) {\n            this.options.compare = void 0;\n            ret = super.getExtremes();\n            this.options.compare = prevCompare;\n        }\n        else if (this.options.cumulative) {\n            this.options.cumulative = false;\n            ret = super.getExtremes();\n            this.options.cumulative = prevCumulative;\n        }\n        else {\n            ret = super.getExtremes();\n        }\n        return ret;\n    }\n    getValues(series, params) {\n        const indicator = this, xValues = series.getColumn('x', true), yValues = series.processedYData, chart = indicator.chart, ranges = params.ranges, VBP = [], xData = [], yData = [], volumeSeries = chart.get(params.volumeSeriesID);\n        // Checks if base series exists\n        if (!series.chart) {\n            error('Base series not found! In case it has been removed, add ' +\n                'a new one.', true, chart);\n            return;\n        }\n        // Checks if volume series exists and if it has data\n        if (!volumeSeries ||\n            !volumeSeries.getColumn('x', true).length) {\n            const errorMessage = volumeSeries &&\n                !volumeSeries.getColumn('x', true).length ?\n                ' does not contain any data.' :\n                ' not found! Check `volumeSeriesID`.';\n            error('Series ' +\n                params.volumeSeriesID + errorMessage, true, chart);\n            return;\n        }\n        // Checks if series data fits the OHLC format\n        const isOHLC = isArray(yValues[0]);\n        if (isOHLC && yValues[0].length !== 4) {\n            error('Type of ' +\n                series.name +\n                ' series is different than line, OHLC or candlestick.', true, chart);\n            return;\n        }\n        // Price zones contains all the information about the zones (index,\n        // start, end, volumes, etc.)\n        const priceZones = indicator.priceZones = indicator.specifyZones(isOHLC, xValues, yValues, ranges, volumeSeries);\n        priceZones.forEach(function (zone, index) {\n            VBP.push([zone.x, zone.end]);\n            xData.push(VBP[index][0]);\n            yData.push(VBP[index][1]);\n        });\n        return {\n            values: VBP,\n            xData: xData,\n            yData: yData\n        };\n    }\n    // Specifying where each zone should start ans end\n    specifyZones(isOHLC, xValues, yValues, ranges, volumeSeries) {\n        const indicator = this, rangeExtremes = (isOHLC ? arrayExtremesOHLC(yValues) : false), zoneStarts = indicator.zoneStarts = [], priceZones = [];\n        let lowRange = rangeExtremes ?\n            rangeExtremes.min :\n            arrayMin(yValues), highRange = rangeExtremes ?\n            rangeExtremes.max :\n            arrayMax(yValues), i = 0, j = 1;\n        // If the compare mode is set on the main series, change the VBP\n        // zones to fit new extremes, #16277.\n        const mainSeries = indicator.linkedParent;\n        if (!indicator.options.compareToMain &&\n            mainSeries.dataModify) {\n            lowRange = mainSeries.dataModify.modifyValue(lowRange);\n            highRange = mainSeries.dataModify.modifyValue(highRange);\n        }\n        if (!defined(lowRange) || !defined(highRange)) {\n            if (this.points.length) {\n                this.setData([]);\n                this.zoneStarts = [];\n                if (this.zoneLinesSVG) {\n                    this.zoneLinesSVG = this.zoneLinesSVG.destroy();\n                }\n            }\n            return [];\n        }\n        const rangeStep = indicator.rangeStep =\n            correctFloat(highRange - lowRange) / ranges;\n        zoneStarts.push(lowRange);\n        for (; i < ranges - 1; i++) {\n            zoneStarts.push(correctFloat(zoneStarts[i] + rangeStep));\n        }\n        zoneStarts.push(highRange);\n        const zoneStartsLength = zoneStarts.length;\n        //    Creating zones\n        for (; j < zoneStartsLength; j++) {\n            priceZones.push({\n                index: j - 1,\n                x: xValues[0],\n                start: zoneStarts[j - 1],\n                end: zoneStarts[j]\n            });\n        }\n        return indicator.volumePerZone(isOHLC, priceZones, volumeSeries, xValues, yValues);\n    }\n    // Calculating sum of volume values for a specific zone\n    volumePerZone(isOHLC, priceZones, volumeSeries, xValues, yValues) {\n        const indicator = this, volumeXData = volumeSeries.getColumn('x', true), volumeYData = volumeSeries.getColumn('y', true), lastZoneIndex = priceZones.length - 1, baseSeriesLength = yValues.length, volumeSeriesLength = volumeYData.length;\n        let previousValue, startFlag, endFlag, value, i;\n        // Checks if each point has a corresponding volume value\n        if (abs(baseSeriesLength - volumeSeriesLength)) {\n            // If the first point don't have volume, add 0 value at the\n            // beginning of the volume array\n            if (xValues[0] !== volumeXData[0]) {\n                volumeYData.unshift(0);\n            }\n            // If the last point don't have volume, add 0 value at the end\n            // of the volume array\n            if (xValues[baseSeriesLength - 1] !==\n                volumeXData[volumeSeriesLength - 1]) {\n                volumeYData.push(0);\n            }\n        }\n        indicator.volumeDataArray = [];\n        priceZones.forEach(function (zone) {\n            zone.wholeVolumeData = 0;\n            zone.positiveVolumeData = 0;\n            zone.negativeVolumeData = 0;\n            for (i = 0; i < baseSeriesLength; i++) {\n                startFlag = false;\n                endFlag = false;\n                value = isOHLC ? yValues[i][3] : yValues[i];\n                previousValue = i ?\n                    (isOHLC ?\n                        yValues[i - 1][3] :\n                        yValues[i - 1]) :\n                    value;\n                // If the compare mode is set on the main series,\n                // change the VBP zones to fit new extremes, #16277.\n                const mainSeries = indicator.linkedParent;\n                if (!indicator.options.compareToMain &&\n                    mainSeries.dataModify) {\n                    value = mainSeries.dataModify.modifyValue(value);\n                    previousValue = mainSeries.dataModify\n                        .modifyValue(previousValue);\n                }\n                // Checks if this is the point with the\n                // lowest close value and if so, adds it calculations\n                if (value <= zone.start && zone.index === 0) {\n                    startFlag = true;\n                }\n                // Checks if this is the point with the highest\n                // close value and if so, adds it calculations\n                if (value >= zone.end && zone.index === lastZoneIndex) {\n                    endFlag = true;\n                }\n                if ((value > zone.start || startFlag) &&\n                    (value < zone.end || endFlag)) {\n                    zone.wholeVolumeData += volumeYData[i];\n                    if (previousValue > value) {\n                        zone.negativeVolumeData += volumeYData[i];\n                    }\n                    else {\n                        zone.positiveVolumeData += volumeYData[i];\n                    }\n                }\n            }\n            indicator.volumeDataArray.push(zone.wholeVolumeData);\n        });\n        return priceZones;\n    }\n    // Function responsible for drawing additional lines indicating zones\n    drawZones(chart, yAxis, zonesValues, zonesStyles) {\n        const indicator = this, renderer = chart.renderer, leftLinePos = 0, rightLinePos = chart.plotWidth, verticalOffset = chart.plotTop;\n        let zoneLinesSVG = indicator.zoneLinesSVG, zoneLinesPath = [], verticalLinePos;\n        zonesValues.forEach(function (value) {\n            verticalLinePos = yAxis.toPixels(value) - verticalOffset;\n            zoneLinesPath = zoneLinesPath.concat(chart.renderer.crispLine([[\n                    'M',\n                    leftLinePos,\n                    verticalLinePos\n                ], [\n                    'L',\n                    rightLinePos,\n                    verticalLinePos\n                ]], zonesStyles.lineWidth));\n        });\n        // Create zone lines one path or update it while animating\n        if (zoneLinesSVG) {\n            zoneLinesSVG.animate({\n                d: zoneLinesPath\n            });\n        }\n        else {\n            zoneLinesSVG = indicator.zoneLinesSVG =\n                renderer\n                    .path(zoneLinesPath)\n                    .attr({\n                    'stroke-width': zonesStyles.lineWidth,\n                    'stroke': zonesStyles.color,\n                    'dashstyle': zonesStyles.dashStyle,\n                    'zIndex': indicator.group.zIndex + 0.1\n                })\n                    .add(indicator.group);\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Volume By Price indicator.\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/volume-by-price\n *         Volume By Price indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/volume-by-price\n * @optionparent plotOptions.vbp\n */\nVBPIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index, period\n     */\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0,\n        /**\n         * The number of price zones.\n         */\n        ranges: 12,\n        /**\n         * The id of volume series which is mandatory. For example using\n         * OHLC data, volumeSeriesID='volume' means the indicator will be\n         * calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume'\n    },\n    /**\n     * The styles for lines which determine price zones.\n     */\n    zoneLines: {\n        /**\n         * Enable/disable zone lines.\n         */\n        enabled: true,\n        /**\n         * Specify the style of zone lines.\n         *\n         * @type    {Highcharts.CSSObject}\n         * @default {\"color\": \"#0A9AC9\", \"dashStyle\": \"LongDash\", \"lineWidth\": 1}\n         */\n        styles: {\n            /** @ignore-option */\n            color: '#0A9AC9',\n            /** @ignore-option */\n            dashStyle: 'LongDash',\n            /** @ignore-option */\n            lineWidth: 1\n        }\n    },\n    /**\n     * The styles for bars when volume is divided into positive/negative.\n     */\n    volumeDivision: {\n        /**\n         * Option to control if volume is divided.\n         */\n        enabled: true,\n        styles: {\n            /**\n             * Color of positive volume bars.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            positiveColor: 'rgba(144, 237, 125, 0.8)',\n            /**\n             * Color of negative volume bars.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            negativeColor: 'rgba(244, 91, 91, 0.8)'\n        }\n    },\n    // To enable series animation; must be animationLimit > pointCount\n    animationLimit: 1000,\n    enableMouseTracking: false,\n    pointPadding: 0,\n    zIndex: -1,\n    crisp: true,\n    dataGrouping: {\n        enabled: false\n    },\n    dataLabels: {\n        align: 'left',\n        allowOverlap: true,\n        enabled: true,\n        format: 'P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}',\n        padding: 0,\n        style: {\n            /** @internal */\n            fontSize: '0.5em'\n        },\n        verticalAlign: 'top'\n    }\n});\nextend(VBPIndicator.prototype, {\n    nameBase: 'Volume by Price',\n    nameComponents: ['ranges'],\n    calculateOn: {\n        chart: 'render',\n        xAxis: 'afterSetExtremes'\n    },\n    pointClass: VBP_VBPPoint,\n    markerAttribs: noop,\n    drawGraph: noop,\n    getColumnMetrics: columnProto.getColumnMetrics,\n    crispCol: columnProto.crispCol\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('vbp', VBPIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VBP_VBPIndicator = ((/* unused pure expression or super */ null && (VBPIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Volume By Price (VBP)` series. If the [type](#series.vbp.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.vbp\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL, compare, compareBase, compareStart\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/volume-by-price\n * @apioption series.vbp\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/volume-by-price.js\n\n\n\n\n\n/* harmony default export */ const volume_by_price_src = ((external_highcharts_src_js_default_default()));\n\nexport { volume_by_price_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "sma", "pointClass", "SMAPoint", "seriesTypes", "VBP_VBPPoint", "destroy", "negativeGraphic", "apply", "arguments", "animObject", "noop", "column", "columnProto", "SMAIndicator", "addEvent", "arrayMax", "arrayMin", "correctFloat", "defined", "error", "extend", "isArray", "merge", "abs", "Math", "VBPIndicator", "init", "chart", "options", "indicator", "data", "unbinder", "constructor", "params", "baseSeries", "linkedParent", "volumeSeries", "volumeSeriesID", "addCustomEvents", "order", "toEmptyIndicator", "redraw", "setData", "zoneStarts", "zoneLinesSVG", "dataEventsToUnbind", "push", "animate", "series", "inverted", "group", "attr", "position", "yAxis", "top", "xAxis", "left", "translateY", "translateX", "animation", "step", "val", "fx", "scaleX", "max", "pos", "drawPoints", "volumeDivision", "enabled", "posNegVolume", "initVol", "signOrder", "point<PERSON><PERSON><PERSON>", "points", "length", "posWidths", "neg<PERSON><PERSON><PERSON>", "i", "pointWidth", "priceZone", "wholeVol", "point", "graphic", "shapeArgs", "width", "priceZones", "wholeVolumeData", "positiveVolumeData", "negativeVolumeData", "color", "styles", "positiveColor", "negativeColor", "x", "translate", "yAxisMin", "min", "zoneLinesOptions", "zoneLines", "yBarOffset", "maxVolume", "primal<PERSON><PERSON><PERSON><PERSON><PERSON>", "barHeight", "barHeightP", "oldBarHeight", "<PERSON><PERSON><PERSON><PERSON>", "pointPadding", "chartPlotTop", "barX", "barY", "indicatorPoints", "volumeDataArray", "plot<PERSON>id<PERSON>", "plotTop", "toPixels", "rangeStep", "for<PERSON>ach", "index", "plotX", "plotY", "start", "reversed", "crispCol", "volumeNeg", "volumePos", "volumeAll", "drawZones", "getExtremes", "ret", "prevCompare", "compare", "prevCumulative", "cumulative", "getV<PERSON>ues", "xValues", "getColumn", "yV<PERSON><PERSON>", "processedYData", "ranges", "VBP", "xData", "yData", "errorMessage", "isOHLC", "name", "specifyZones", "zone", "end", "values", "rangeExtremes", "arrayExtremesOHLC", "dataLength", "currentPoint", "lowRange", "highRange", "j", "mainSeries", "compareToMain", "dataModify", "modifyValue", "zoneStartsLength", "volumePerZone", "previousValue", "startFlag", "endFlag", "value", "volumeXData", "volumeYData", "lastZoneIndex", "baseSeriesLength", "volumeSeriesLength", "unshift", "zonesValues", "zonesStyles", "renderer", "rightLinePos", "verticalOffset", "zoneLinesPath", "verticalLinePos", "concat", "crispLine", "lineWidth", "path", "dashStyle", "zIndex", "add", "defaultOptions", "period", "animationLimit", "enableMouseTracking", "crisp", "dataGrouping", "dataLabels", "align", "allowOverlap", "format", "padding", "style", "fontSize", "verticalAlign", "nameBase", "nameComponents", "calculateOn", "markerAttribs", "drawGraph", "getColumnMetrics", "registerSeriesType", "volume_by_price_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,AAChG,OAA4E,yBAA0B,CAE7F,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAIiC,CAAG,GAE9C,IAAMiB,EAAoEvB,EAAwD,OAAU,CAACwB,cAAc,CAC3J,IAAIC,EAAyExB,EAAoBC,CAAC,CAACqB,GAgBnG,GAAM,CAAEG,IAAK,CAAER,UAAW,CAAES,WAAYC,CAAQ,CAAE,CAAE,CAAE,CAAG,AAACH,IAA6DI,WAAW,CAqB/FC,EAfnC,cAAuBF,EAEnBG,SAAU,CAEF,IAAI,CAACC,eAAe,EACpB,CAAA,IAAI,CAACA,eAAe,CAAG,IAAI,CAACA,eAAe,CAACD,OAAO,EAAC,EAExD,KAAK,CAACA,QAAQE,KAAK,CAAC,IAAI,CAAEC,UAC9B,CACJ,EAuBM,CAAEC,WAAAA,CAAU,CAAE,CAAIb,IAElB,CAAEc,KAAAA,CAAI,CAAE,CAAId,IAEZ,CAAEe,OAAQ,CAAEnB,UAAWoB,CAAW,CAAE,CAAEZ,IAAKa,CAAY,CAAE,CAAG,AAACd,IAA6DI,WAAW,CAErI,CAAEW,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,aAAAA,CAAY,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAE,CAAI1B,IAM1F2B,EAAMC,KAAKD,GAAG,AAyCpB,OAAME,UAAqBZ,EAMvBa,KAAKC,CAAK,CAAEC,CAAO,CAAE,CACjB,IAAMC,EAAY,IAAI,AAGtB,QAAOD,EAAQE,IAAI,CACnB,KAAK,CAACJ,KAAKnB,KAAK,CAACsB,EAAWrB,WAE5B,IAAMuB,EAAWjB,EAAS,IAAI,CAACa,KAAK,CAACK,WAAW,CAAE,kBAAmB,WAGjE,GAAIH,EAAUD,OAAO,CAAE,CACnB,IAAMK,EAASJ,EAAUD,OAAO,CAACK,MAAM,CAAEC,EAAaL,EAAUM,YAAY,CAAEC,EAAeT,EAAMtC,GAAG,CAAC4C,EAAOI,cAAc,EAC5HR,EAAUS,eAAe,CAACJ,EAAYE,EAC1C,CACAL,GACJ,EAAG,CACCQ,MAAO,CACX,GACA,OAAOV,CACX,CAEAS,gBAAgBJ,CAAU,CAAEE,CAAY,CAAE,CACtC,IAAMP,EAAY,IAAI,CAAEW,EAAmB,KACvCX,EAAUF,KAAK,CAACc,MAAM,GACtBZ,EAAUa,OAAO,CAAC,EAAE,EACpBb,EAAUc,UAAU,CAAG,EAAE,CACrBd,EAAUe,YAAY,EACtBf,CAAAA,EAAUe,YAAY,CAAGf,EAAUe,YAAY,CAACvC,OAAO,EAAC,CAEhE,EAaA,OAVAwB,EAAUgB,kBAAkB,CAACC,IAAI,CAAChC,EAASoB,EAAY,SAAU,WAC7DM,GACJ,IAGIJ,GACAP,EAAUgB,kBAAkB,CAACC,IAAI,CAAChC,EAASsB,EAAc,SAAU,WAC/DI,GACJ,IAEGX,CACX,CAEAkB,QAAQrB,CAAI,CAAE,CACV,IAAMsB,EAAS,IAAI,CAAEC,EAAWD,EAAOrB,KAAK,CAACsB,QAAQ,CAAEC,EAAQF,EAAOE,KAAK,CAAEC,EAAO,CAAC,EACrF,GAAI,CAACzB,GAAQwB,EAAO,CAChB,IAAME,EAAWH,EAAWD,EAAOK,KAAK,CAACC,GAAG,CAAGN,EAAOO,KAAK,CAACC,IAAI,CAC5DP,GACAC,CAAK,CAAC,0BAA0B,CAAG,CAAA,EACnCC,EAAKM,UAAU,CAAGL,IAGlBF,CAAK,CAAC,0BAA0B,CAAG,CAAA,EACnCC,EAAKO,UAAU,CAAGN,GAEtBF,EAAMH,OAAO,CAACI,EAAM/B,EAAOX,EAAWuC,EAAOpB,OAAO,CAAC+B,SAAS,EAAG,CAC7DC,KAAM,SAAUC,CAAG,CAAEC,CAAE,EACnBd,EAAOE,KAAK,CAACC,IAAI,CAAC,CACdY,OAAQvC,KAAKwC,GAAG,CAAC,KAAOF,EAAGG,GAAG,CAClC,EACJ,CACJ,GACJ,CACJ,CACAC,YAAa,CAELrC,AADc,IAAI,CACRD,OAAO,CAACuC,cAAc,CAACC,OAAO,GACxCvC,AAFc,IAAI,CAERwC,YAAY,CAAC,CAAA,EAAM,CAAA,GAC7BzD,EAAYsD,UAAU,CAAC3D,KAAK,CAHd,IAAI,CAGsBC,WACxCqB,AAJc,IAAI,CAIRwC,YAAY,CAAC,CAAA,EAAO,CAAA,IAElCzD,EAAYsD,UAAU,CAAC3D,KAAK,CANV,IAAI,CAMkBC,UAC5C,CAEA6D,aAAaC,CAAO,CAAEL,CAAG,CAAE,CACvB,IAAwBM,EAAYN,EAChC,CAAC,WAAY,WAAW,CACxB,CAAC,WAAY,WAAW,CAAEE,EAAiBtC,AAF7B,IAAI,CAEmCD,OAAO,CAACuC,cAAc,CAAEK,EAAc3C,AAF7E,IAAI,CAEmF4C,MAAM,CAACC,MAAM,CAClHC,EAAY,EAAE,CAAEC,EAAY,EAAE,CAAEC,EAAI,EAAGC,EAAYC,EAAWC,EAAUC,EAS5E,IARIX,GACAzC,AALc,IAAI,CAKR8C,SAAS,CAAGA,EACtB9C,AANc,IAAI,CAMR+C,SAAS,CAAGA,IAGtBD,EAAY9C,AATE,IAAI,CASI8C,SAAS,CAC/BC,EAAY/C,AAVE,IAAI,CAUI+C,SAAS,EAE5BC,EAAIL,EAAaK,IAEpBI,AADAA,CAAAA,EAAQpD,AAbM,IAAI,CAaA4C,MAAM,CAACI,EAAE,AAAD,CACrB,CAACN,CAAS,CAAC,EAAE,CAAG,UAAU,CAAGU,EAAMC,OAAO,CAC/CD,EAAMC,OAAO,CAAGD,CAAK,CAACV,CAAS,CAAC,EAAE,CAAG,UAAU,CAC3CD,IACAQ,EAAaG,EAAME,SAAS,CAACC,KAAK,CAElCJ,CAAAA,EAAWD,AADXA,CAAAA,EAAYlD,AAlBF,IAAI,CAkBQwD,UAAU,CAACR,EAAE,AAAD,EACbS,eAAe,AAAD,GAE/BX,EAAU7B,IAAI,CAACgC,EAAaE,EAAWD,EAAUQ,kBAAkB,EACnEX,EAAU9B,IAAI,CAACgC,EAAaE,EAAWD,EAAUS,kBAAkB,IAGnEb,EAAU7B,IAAI,CAAC,GACf8B,EAAU9B,IAAI,CAAC,KAGvBmC,EAAMQ,KAAK,CAAGxB,EACVE,EAAeuB,MAAM,CAACC,aAAa,CACnCxB,EAAeuB,MAAM,CAACE,aAAa,CACvCX,EAAME,SAAS,CAACC,KAAK,CAAGnB,EACpBpC,AAjCU,IAAI,CAiCJ8C,SAAS,CAACE,EAAE,CACtBhD,AAlCU,IAAI,CAkCJ+C,SAAS,CAACC,EAAE,CAC1BI,EAAME,SAAS,CAACU,CAAC,CAAG5B,EAChBgB,EAAME,SAAS,CAACU,CAAC,CACjBhE,AArCU,IAAI,CAqCJ8C,SAAS,CAACE,EAAE,AAElC,CACAiB,WAAY,CACR,IAAMjE,EAAY,IAAI,CAAED,EAAUC,EAAUD,OAAO,CAAED,EAAQE,EAAUF,KAAK,CAAE0B,EAAQxB,EAAUwB,KAAK,CAAE0C,EAAW1C,EAAM2C,GAAG,CAAEC,EAAmBpE,EAAUD,OAAO,CAACsE,SAAS,CAAEb,EAAcxD,EAAUwD,UAAU,CAC3Mc,EAAa,EAAoBC,EAAWC,EAAgBC,EAAWC,EAAYC,EAAcC,EAAUC,EAAcC,EAAcC,EAAMC,EACjJjG,EAAYkF,SAAS,CAACvF,KAAK,CAACsB,GAC5B,IAAMiF,EAAkBjF,EAAU4C,MAAM,AAEpCqC,CAAAA,EAAgBpC,MAAM,GACtBgC,EAAe9E,EAAQ8E,YAAY,CAAG,GAClC9E,EAAQ8E,YAAY,CACpB,GAEJN,EAAYrF,EADMc,EAAUkF,eAAe,EAE3CV,EAAiB1E,EAAMqF,SAAS,CAAG,EACnCL,EAAehF,EAAMsF,OAAO,CAC5BX,EAAY/E,EAAI8B,EAAM6D,QAAQ,CAACnB,GAC3B1C,EAAM6D,QAAQ,CAACnB,EAAWlE,EAAUsF,SAAS,GACjDX,EAAejF,EAAI8B,EAAM6D,QAAQ,CAACnB,GAC9B1C,EAAM6D,QAAQ,CAACnB,EAAWlE,EAAUsF,SAAS,GAC7CT,IACAH,EAAahF,EAAI+E,EAAa,CAAA,EAAI,EAAII,CAAW,GACjDP,EAAa5E,EAAI,AAAC+E,CAAAA,EAAYC,CAAS,EAAK,GAC5CD,EAAY/E,EAAIgF,IAEpBO,EAAgBM,OAAO,CAAC,SAAUnC,CAAK,CAAEoC,CAAK,EAC1CT,EAAO3B,EAAM2B,IAAI,CAAG3B,EAAMqC,KAAK,CAAG,EAClCT,EAAO5B,EAAMsC,KAAK,CAAIlE,EAAM6D,QAAQ,CAAC7B,CAAU,CAACgC,EAAM,CAACG,KAAK,EACxDb,EACCtD,CAAAA,EAAMoE,QAAQ,CACVnB,EAAYE,EACbF,CAAQ,EACZH,EAGJlB,EAAMH,UAAU,CAFhB2B,EAAWxF,EAAaoF,EACpBhB,CAAU,CAACgC,EAAM,CAAC/B,eAAe,CAAGc,GAExCnB,EAAME,SAAS,CAAGtD,EAAU6F,QAAQ,CAACnH,KAAK,CAC1CsB,EAAW,CAAC+E,EAAMC,EAAMJ,EAAUH,EAAU,EAC5CrB,EAAM0C,SAAS,CAAGtC,CAAU,CAACgC,EAAM,CAAC7B,kBAAkB,CACtDP,EAAM2C,SAAS,CAAGvC,CAAU,CAACgC,EAAM,CAAC9B,kBAAkB,CACtDN,EAAM4C,SAAS,CAAGxC,CAAU,CAACgC,EAAM,CAAC/B,eAAe,AACvD,GACIW,EAAiB7B,OAAO,EACxBvC,EAAUiG,SAAS,CAACnG,EAAO0B,EAAOxB,EAAUc,UAAU,CAAEsD,EAAiBP,MAAM,EAG3F,CACAqC,aAAc,CACV,IACIC,EADEC,EAAc,IAAI,CAACrG,OAAO,CAACsG,OAAO,CAAEC,EAAiB,IAAI,CAACvG,OAAO,CAACwG,UAAU,CAgBlF,OAbI,IAAI,CAACxG,OAAO,CAACsG,OAAO,EACpB,IAAI,CAACtG,OAAO,CAACsG,OAAO,CAAG,KAAK,EAC5BF,EAAM,KAAK,CAACD,cACZ,IAAI,CAACnG,OAAO,CAACsG,OAAO,CAAGD,GAElB,IAAI,CAACrG,OAAO,CAACwG,UAAU,EAC5B,IAAI,CAACxG,OAAO,CAACwG,UAAU,CAAG,CAAA,EAC1BJ,EAAM,KAAK,CAACD,cACZ,IAAI,CAACnG,OAAO,CAACwG,UAAU,CAAGD,GAG1BH,EAAM,KAAK,CAACD,cAETC,CACX,CACAK,UAAUrF,CAAM,CAAEf,CAAM,CAAE,CACtB,IAAwBqG,EAAUtF,EAAOuF,SAAS,CAAC,IAAK,CAAA,GAAOC,EAAUxF,EAAOyF,cAAc,CAAE9G,EAAQE,AAAtF,IAAI,CAA4FF,KAAK,CAAE+G,EAASzG,EAAOyG,MAAM,CAAEC,EAAM,EAAE,CAAEC,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAAEzG,EAAeT,EAAMtC,GAAG,CAAC4C,EAAOI,cAAc,EAEjO,GAAI,CAACW,EAAOrB,KAAK,CAAE,CACfR,EAAM,qEACY,CAAA,EAAMQ,GACxB,MACJ,CAEA,GAAI,CAACS,GACD,CAACA,EAAamG,SAAS,CAAC,IAAK,CAAA,GAAM7D,MAAM,CAAE,CAC3C,IAAMoE,EAAe1G,GACjB,CAACA,EAAamG,SAAS,CAAC,IAAK,CAAA,GAAM7D,MAAM,CACzC,8BACA,sCACJvD,EAAM,UACFc,EAAOI,cAAc,CAAGyG,EAAc,CAAA,EAAMnH,GAChD,MACJ,CAEA,IAAMoH,EAAS1H,EAAQmH,CAAO,CAAC,EAAE,EACjC,GAAIO,GAAUP,AAAsB,IAAtBA,CAAO,CAAC,EAAE,CAAC9D,MAAM,CAAQ,CACnCvD,EAAM,WACF6B,EAAOgG,IAAI,CACX,uDAAwD,CAAA,EAAMrH,GAClE,MACJ,CASA,MALA0D,AADmBxD,CAAAA,AA5BD,IAAI,CA4BOwD,UAAU,CAAGxD,AA5BxB,IAAI,CA4B8BoH,YAAY,CAACF,EAAQT,EAASE,EAASE,EAAQtG,EAAY,EACpGgF,OAAO,CAAC,SAAU8B,CAAI,CAAE7B,CAAK,EACpCsB,EAAI7F,IAAI,CAAC,CAACoG,EAAKrD,CAAC,CAAEqD,EAAKC,GAAG,CAAC,EAC3BP,EAAM9F,IAAI,CAAC6F,CAAG,CAACtB,EAAM,CAAC,EAAE,EACxBwB,EAAM/F,IAAI,CAAC6F,CAAG,CAACtB,EAAM,CAAC,EAAE,CAC5B,GACO,CACH+B,OAAQT,EACRC,MAAOA,EACPC,MAAOA,CACX,CACJ,CAEAI,aAAaF,CAAM,CAAET,CAAO,CAAEE,CAAO,CAAEE,CAAM,CAAEtG,CAAY,CAAE,CACzD,IAAwBiH,EAAiBN,EAAAA,GAASO,AArQ1D,SAA2BxH,CAAI,EAC3B,IAAMyH,EAAazH,EAAK4C,MAAM,CAC1BsB,EAAMlE,CAAI,CAAC,EAAE,CAAC,EAAE,CAAEkC,EAAMgC,EAAKnB,EAAI,EAAG2E,EACxC,KAAO3E,EAAI0E,EAAY1E,IACnB2E,CAAAA,EAAe1H,CAAI,CAAC+C,EAAE,CAAC,EAAE,AAAD,EACLmB,GACfA,CAAAA,EAAMwD,CAAW,EAEjBA,EAAexF,GACfA,CAAAA,EAAMwF,CAAW,EAGzB,MAAO,CACHxD,IAAKA,EACLhC,IAAKA,CACT,CACJ,EAqP4EwE,GAAmB7F,EAAad,AAAlF,IAAI,CAAwFc,UAAU,CAAG,EAAE,CAAE0C,EAAa,EAAE,CAC1IoE,EAAWJ,EACXA,EAAcrD,GAAG,CACjBhF,EAASwH,GAAUkB,EAAYL,EAC/BA,EAAcrF,GAAG,CACjBjD,EAASyH,GAAU3D,EAAI,EAAG8E,EAAI,EAG5BC,EAAa/H,AARD,IAAI,CAQOM,YAAY,CAMzC,GALI,CAACN,AATa,IAAI,CASPD,OAAO,CAACiI,aAAa,EAChCD,EAAWE,UAAU,GACrBL,EAAWG,EAAWE,UAAU,CAACC,WAAW,CAACN,GAC7CC,EAAYE,EAAWE,UAAU,CAACC,WAAW,CAACL,IAE9C,CAACxI,EAAQuI,IAAa,CAACvI,EAAQwI,GAQ/B,OAPI,IAAI,CAACjF,MAAM,CAACC,MAAM,GAClB,IAAI,CAAChC,OAAO,CAAC,EAAE,EACf,IAAI,CAACC,UAAU,CAAG,EAAE,CAChB,IAAI,CAACC,YAAY,EACjB,CAAA,IAAI,CAACA,YAAY,CAAG,IAAI,CAACA,YAAY,CAACvC,OAAO,EAAC,GAG/C,EAAE,CAEb,IAAM8G,EAAYtF,AAxBA,IAAI,CAwBMsF,SAAS,CACjClG,EAAayI,EAAYD,GAAYf,EAEzC,IADA/F,EAAWG,IAAI,CAAC2G,GACT5E,EAAI6D,EAAS,EAAG7D,IACnBlC,EAAWG,IAAI,CAAC7B,EAAa0B,CAAU,CAACkC,EAAE,CAAGsC,IAEjDxE,EAAWG,IAAI,CAAC4G,GAChB,IAAMM,EAAmBrH,EAAW+B,MAAM,CAE1C,KAAOiF,EAAIK,EAAkBL,IACzBtE,EAAWvC,IAAI,CAAC,CACZuE,MAAOsC,EAAI,EACX9D,EAAGyC,CAAO,CAAC,EAAE,CACbd,MAAO7E,CAAU,CAACgH,EAAI,EAAE,CACxBR,IAAKxG,CAAU,CAACgH,EAAE,AACtB,GAEJ,OAAO9H,AAzCW,IAAI,CAyCLoI,aAAa,CAAClB,EAAQ1D,EAAYjD,EAAckG,EAASE,EAC9E,CAEAyB,cAAclB,CAAM,CAAE1D,CAAU,CAAEjD,CAAY,CAAEkG,CAAO,CAAEE,CAAO,CAAE,CAC9D,IACI0B,EAAeC,EAAWC,EAASC,EAAOxF,EADxChD,EAAY,IAAI,CAAEyI,EAAclI,EAAamG,SAAS,CAAC,IAAK,CAAA,GAAOgC,EAAcnI,EAAamG,SAAS,CAAC,IAAK,CAAA,GAAOiC,EAAgBnF,EAAWX,MAAM,CAAG,EAAG+F,EAAmBjC,EAAQ9D,MAAM,CAAEgG,EAAqBH,EAAY7F,MAAM,CA8D3O,OA3DInD,EAAIkJ,EAAmBC,KAGnBpC,CAAO,CAAC,EAAE,GAAKgC,CAAW,CAAC,EAAE,EAC7BC,EAAYI,OAAO,CAAC,GAIpBrC,CAAO,CAACmC,EAAmB,EAAE,GAC7BH,CAAW,CAACI,EAAqB,EAAE,EACnCH,EAAYzH,IAAI,CAAC,IAGzBjB,EAAUkF,eAAe,CAAG,EAAE,CAC9B1B,EAAW+B,OAAO,CAAC,SAAU8B,CAAI,EAI7B,IAAKrE,EAAI,EAHTqE,EAAK5D,eAAe,CAAG,EACvB4D,EAAK3D,kBAAkB,CAAG,EAC1B2D,EAAK1D,kBAAkB,CAAG,EACdX,EAAI4F,EAAkB5F,IAAK,CACnCsF,EAAY,CAAA,EACZC,EAAU,CAAA,EACVC,EAAQtB,EAASP,CAAO,CAAC3D,EAAE,CAAC,EAAE,CAAG2D,CAAO,CAAC3D,EAAE,CAC3CqF,EAAgBrF,EACXkE,EACGP,CAAO,CAAC3D,EAAI,EAAE,CAAC,EAAE,CACjB2D,CAAO,CAAC3D,EAAI,EAAE,CAClBwF,EAGJ,IAAMT,EAAa/H,EAAUM,YAAY,AACrC,EAACN,EAAUD,OAAO,CAACiI,aAAa,EAChCD,EAAWE,UAAU,GACrBO,EAAQT,EAAWE,UAAU,CAACC,WAAW,CAACM,GAC1CH,EAAgBN,EAAWE,UAAU,CAChCC,WAAW,CAACG,IAIjBG,GAASnB,EAAK1B,KAAK,EAAI0B,AAAe,IAAfA,EAAK7B,KAAK,EACjC8C,CAAAA,EAAY,CAAA,CAAG,EAIfE,GAASnB,EAAKC,GAAG,EAAID,EAAK7B,KAAK,GAAKmD,GACpCJ,CAAAA,EAAU,CAAA,CAAG,EAEZC,CAAAA,EAAQnB,EAAK1B,KAAK,EAAI2C,CAAQ,GAC9BE,CAAAA,EAAQnB,EAAKC,GAAG,EAAIiB,CAAM,IAC3BlB,EAAK5D,eAAe,EAAIiF,CAAW,CAAC1F,EAAE,CAClCqF,EAAgBG,EAChBnB,EAAK1D,kBAAkB,EAAI+E,CAAW,CAAC1F,EAAE,CAGzCqE,EAAK3D,kBAAkB,EAAIgF,CAAW,CAAC1F,EAAE,CAGrD,CACAhD,EAAUkF,eAAe,CAACjE,IAAI,CAACoG,EAAK5D,eAAe,CACvD,GACOD,CACX,CAEAyC,UAAUnG,CAAK,CAAE0B,CAAK,CAAEuH,CAAW,CAAEC,CAAW,CAAE,CAC9C,IAAwBC,EAAWnJ,EAAMmJ,QAAQ,CAAmBC,EAAepJ,EAAMqF,SAAS,CAAEgE,EAAiBrJ,EAAMsF,OAAO,CAC9HrE,EAAef,AADD,IAAI,CACOe,YAAY,CAAEqI,EAAgB,EAAE,CAAEC,EAC/DN,EAAYxD,OAAO,CAAC,SAAUiD,CAAK,EAC/Ba,EAAkB7H,EAAM6D,QAAQ,CAACmD,GAASW,EAC1CC,EAAgBA,EAAcE,MAAM,CAACxJ,EAAMmJ,QAAQ,CAACM,SAAS,CAAC,CAAC,CACvD,IALqD,EAOrDF,EACH,CAAE,CACC,IACAH,EACAG,EACH,CAAC,CAAEL,EAAYQ,SAAS,EACjC,GAEIzI,EACAA,EAAaG,OAAO,CAAC,CACjBnE,EAAGqM,CACP,GAGArI,EAAef,AArBD,IAAI,CAqBOe,YAAY,CACjCkI,EACKQ,IAAI,CAACL,GACL9H,IAAI,CAAC,CACN,eAAgB0H,EAAYQ,SAAS,CACrC,OAAUR,EAAYpF,KAAK,CAC3B,UAAaoF,EAAYU,SAAS,CAClC,OAAU1J,AA5BJ,IAAI,CA4BUqB,KAAK,CAACsI,MAAM,CAAG,EACvC,GACKC,GAAG,CAAC5J,AA9BC,IAAI,CA8BKqB,KAAK,CAEpC,CACJ,CAqBAzB,EAAaiK,cAAc,CAAGpK,EAAMT,EAAa6K,cAAc,CAAE,CAI7DzJ,OAAQ,CAEJoF,MAAO,KAAK,EACZsE,OAAQ,KAAK,EAIbjD,OAAQ,GAMRrG,eAAgB,QACpB,EAIA6D,UAAW,CAIP9B,QAAS,CAAA,EAOTsB,OAAQ,CAEJD,MAAO,UAEP8F,UAAW,WAEXF,UAAW,CACf,CACJ,EAIAlH,eAAgB,CAIZC,QAAS,CAAA,EACTsB,OAAQ,CAMJC,cAAe,2BAMfC,cAAe,wBACnB,CACJ,EAEAgG,eAAgB,IAChBC,oBAAqB,CAAA,EACrBnF,aAAc,EACd8E,OAAQ,GACRM,MAAO,CAAA,EACPC,aAAc,CACV3H,QAAS,CAAA,CACb,EACA4H,WAAY,CACRC,MAAO,OACPC,aAAc,CAAA,EACd9H,QAAS,CAAA,EACT+H,OAAQ,sDACRC,QAAS,EACTC,MAAO,CAEHC,SAAU,OACd,EACAC,cAAe,KACnB,CACJ,GACAnL,EAAOK,EAAajC,SAAS,CAAE,CAC3BgN,SAAU,kBACVC,eAAgB,CAAC,SAAS,CAC1BC,YAAa,CACT/K,MAAO,SACP4B,MAAO,kBACX,EACAtD,WAAYG,EACZuM,cAAejM,EACfkM,UAAWlM,EACXmM,iBAAkBjM,EAAYiM,gBAAgB,CAC9CnF,SAAU9G,EAAY8G,QAAQ,AAClC,GACA3H,IAA4D+M,kBAAkB,CAAC,MAAOrL,GAgCzD,IAAMsL,EAAwBnN,WAElDmN,KAAuBC,OAAO"}