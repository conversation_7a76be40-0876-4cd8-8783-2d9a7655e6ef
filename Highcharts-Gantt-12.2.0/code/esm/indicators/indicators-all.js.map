{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/indicators-all\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * All technical indicators for Highcharts Stock\n *\n * (c) 2010-2025 Pawel Fus\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_datagrouping_src_js_b7a4250c__ from \"../modules/datagrouping.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_b3d80146__ from \"../modules/stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"Chart\"]\nconst external_highcharts_src_js_default_Chart_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Chart;\nvar external_highcharts_src_js_default_Chart_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Chart_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/SMA/SMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { line: LineSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent, fireEvent, error, extend, isArray, merge, pick } = (external_highcharts_src_js_default_default());\n/**\n *\n * Return the parent series values in the legacy two-dimensional yData\n * format\n * @private\n */\nconst tableToMultiYData = (series, processed) => {\n    const yData = [], pointArrayMap = series.pointArrayMap, table = processed && series.dataTable.modified || series.dataTable;\n    if (!pointArrayMap) {\n        return series.getColumn('y', processed);\n    }\n    const columns = pointArrayMap.map((key) => series.getColumn(key, processed));\n    for (let i = 0; i < table.rowCount; i++) {\n        const values = pointArrayMap.map((key, colIndex) => columns[colIndex]?.[i] || 0);\n        yData.push(values);\n    }\n    return yData;\n};\n/* *\n *\n *  Class\n *\n * */\n/**\n * The SMA series type.\n *\n * @private\n */\nclass SMAIndicator extends LineSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    destroy() {\n        this.dataEventsToUnbind.forEach(function (unbinder) {\n            unbinder();\n        });\n        super.destroy.apply(this, arguments);\n    }\n    /**\n     * @private\n     */\n    getName() {\n        const params = [];\n        let name = this.name;\n        if (!name) {\n            (this.nameComponents || []).forEach(function (component, index) {\n                params.push(this.options.params[component] +\n                    pick(this.nameSuffixes[index], ''));\n            }, this);\n            name = (this.nameBase || this.type.toUpperCase()) +\n                (this.nameComponents ? ' (' + params.join(', ') + ')' : '');\n        }\n        return name;\n    }\n    /**\n     * @private\n     */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData || [], yVal = series.yData, yValLen = yVal.length, SMA = [], xData = [], yData = [];\n        let i, index = -1, range = 0, SMAPoint, sum = 0;\n        if (xVal.length < period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        while (range < period - 1) {\n            sum += index < 0 ? yVal[range] : yVal[range][index];\n            range++;\n        }\n        // Calculate value one-by-one for each period in visible data\n        for (i = range; i < yValLen; i++) {\n            sum += index < 0 ? yVal[i] : yVal[i][index];\n            SMAPoint = [xVal[i], sum / period];\n            SMA.push(SMAPoint);\n            xData.push(SMAPoint[0]);\n            yData.push(SMAPoint[1]);\n            sum -= (index < 0 ?\n                yVal[i - range] :\n                yVal[i - range][index]);\n        }\n        return {\n            values: SMA,\n            xData: xData,\n            yData: yData\n        };\n    }\n    /**\n     * @private\n     */\n    init(chart, options) {\n        const indicator = this;\n        super.init.call(indicator, chart, options);\n        // Only after series are linked indicator can be processed.\n        const linkedSeriesUnbiner = addEvent((external_highcharts_src_js_default_Chart_default()), 'afterLinkSeries', function ({ isUpdating }) {\n            // #18643 indicator shouldn't recalculate\n            // values while series updating.\n            if (isUpdating) {\n                return;\n            }\n            const hasEvents = !!indicator.dataEventsToUnbind.length;\n            if (indicator.linkedParent) {\n                if (!hasEvents) {\n                    // No matter which indicator, always recalculate after\n                    // updating the data.\n                    indicator.dataEventsToUnbind.push(addEvent(indicator.linkedParent, 'updatedData', function () {\n                        indicator.recalculateValues();\n                    }));\n                    // Some indicators (like VBP) requires an additional\n                    // event (afterSetExtremes) to properly show the data.\n                    if (indicator.calculateOn.xAxis) {\n                        indicator.dataEventsToUnbind.push(addEvent(indicator.linkedParent.xAxis, indicator.calculateOn.xAxis, function () {\n                            indicator.recalculateValues();\n                        }));\n                    }\n                }\n                // Most indicators are being calculated on chart's init.\n                if (indicator.calculateOn.chart === 'init') {\n                    // When closestPointRange is set, it is an indication\n                    // that `Series.processData` has run. If it hasn't we\n                    // need to `recalculateValues`.\n                    if (!indicator.closestPointRange) {\n                        indicator.recalculateValues();\n                    }\n                }\n                else if (!hasEvents) {\n                    // Some indicators (like VBP) has to recalculate their\n                    // values after other chart's events (render).\n                    const unbinder = addEvent(indicator.chart, indicator.calculateOn.chart, function () {\n                        indicator.recalculateValues();\n                        // Call this just once.\n                        unbinder();\n                    });\n                }\n            }\n            else {\n                return error('Series ' +\n                    indicator.options.linkedTo +\n                    ' not found! Check `linkedTo`.', false, chart);\n            }\n        }, {\n            order: 0\n        });\n        // Make sure we find series which is a base for an indicator\n        // chart.linkSeries();\n        indicator.dataEventsToUnbind = [];\n        indicator.eventsToUnbind.push(linkedSeriesUnbiner);\n    }\n    /**\n     * @private\n     */\n    recalculateValues() {\n        const croppedDataValues = [], indicator = this, table = this.dataTable, oldData = indicator.points || [], oldDataLength = indicator.dataTable.rowCount, emptySet = {\n            values: [],\n            xData: [],\n            yData: []\n        };\n        let overwriteData = true, oldFirstPointIndex, oldLastPointIndex, min, max;\n        // For the newer data table, temporarily set the parent series `yData`\n        // to the legacy format that is documented for custom indicators, and\n        // get the xData from the data table\n        const yData = indicator.linkedParent.yData, processedYData = indicator.linkedParent.processedYData;\n        indicator.linkedParent.xData = indicator.linkedParent\n            .getColumn('x');\n        indicator.linkedParent.yData = tableToMultiYData(indicator.linkedParent);\n        indicator.linkedParent.processedYData = tableToMultiYData(indicator.linkedParent, true);\n        // Updating an indicator with redraw=false may destroy data.\n        // If there will be a following update for the parent series,\n        // we will try to access Series object without any properties\n        // (except for prototyped ones). This is what happens\n        // for example when using Axis.setDataGrouping(). See #16670\n        const processedData = indicator.linkedParent.options &&\n            // #18176, #18177 indicators should work with empty dataset\n            indicator.linkedParent.dataTable.rowCount ?\n            (indicator.getValues(indicator.linkedParent, indicator.options.params) || emptySet) : emptySet;\n        // Reset\n        delete indicator.linkedParent.xData;\n        indicator.linkedParent.yData = yData;\n        indicator.linkedParent.processedYData = processedYData;\n        const pointArrayMap = indicator.pointArrayMap || ['y'], valueColumns = {};\n        // Split legacy twodimensional values into value columns\n        processedData.yData\n            .forEach((values) => {\n            pointArrayMap.forEach((key, index) => {\n                const column = valueColumns[key] || [];\n                column.push(isArray(values) ? values[index] : values);\n                if (!valueColumns[key]) {\n                    valueColumns[key] = column;\n                }\n            });\n        });\n        // We need to update points to reflect changes in all,\n        // x and y's, values. However, do it only for non-grouped\n        // data - grouping does it for us (#8572)\n        if (oldDataLength &&\n            !indicator.hasGroupedData &&\n            indicator.visible &&\n            indicator.points) {\n            // When data is cropped update only avaliable points (#9493)\n            if (indicator.cropped) {\n                if (indicator.xAxis) {\n                    min = indicator.xAxis.min;\n                    max = indicator.xAxis.max;\n                }\n                const croppedData = indicator.cropData(table, min, max);\n                const keys = ['x', ...(indicator.pointArrayMap || ['y'])];\n                for (let i = 0; i < (croppedData.modified?.rowCount || 0); i++) {\n                    const values = keys.map((key) => this.getColumn(key)[i] || 0);\n                    croppedDataValues.push(values);\n                }\n                const indicatorXData = indicator.getColumn('x');\n                oldFirstPointIndex = processedData.xData.indexOf(indicatorXData[0]);\n                oldLastPointIndex = processedData.xData.indexOf(indicatorXData[indicatorXData.length - 1]);\n                // Check if indicator points should be shifted (#8572)\n                if (oldFirstPointIndex === -1 &&\n                    oldLastPointIndex === processedData.xData.length - 2) {\n                    if (croppedDataValues[0][0] === oldData[0].x) {\n                        croppedDataValues.shift();\n                    }\n                }\n                indicator.updateData(croppedDataValues);\n            }\n            else if (indicator.updateAllPoints || // #18710\n                // Omit addPoint() and removePoint() cases\n                processedData.xData.length !== oldDataLength - 1 &&\n                    processedData.xData.length !== oldDataLength + 1) {\n                overwriteData = false;\n                indicator.updateData(processedData.values);\n            }\n        }\n        if (overwriteData) {\n            table.setColumns({\n                ...valueColumns,\n                x: processedData.xData\n            });\n            indicator.options.data = processedData.values;\n        }\n        if (indicator.calculateOn.xAxis &&\n            indicator.getColumn('x', true).length) {\n            indicator.isDirty = true;\n            indicator.redraw();\n        }\n        indicator.isDirtyData = !!indicator.linkedSeries.length;\n        fireEvent(indicator, 'updatedData'); // #18689\n    }\n    /**\n     * @private\n     */\n    processData() {\n        const series = this, compareToMain = series.options.compareToMain, linkedParent = series.linkedParent;\n        super.processData.apply(series, arguments);\n        if (series.dataModify &&\n            linkedParent &&\n            linkedParent.dataModify &&\n            linkedParent.dataModify.compareValue &&\n            compareToMain) {\n            series.dataModify.compareValue =\n                linkedParent.dataModify.compareValue;\n        }\n        return;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * The parameter allows setting line series type and use OHLC indicators.\n * Data in OHLC format is required.\n *\n * @sample {highstock} stock/indicators/use-ohlc-data\n *         Use OHLC data format to plot line chart\n *\n * @type      {boolean}\n * @product   highstock\n * @apioption plotOptions.line.useOhlcData\n */\n/**\n * Simple moving average indicator (SMA). This series requires `linkedTo`\n * option to be set.\n *\n * @sample stock/indicators/sma\n *         Simple moving average indicator\n *\n * @extends      plotOptions.line\n * @since        6.0.0\n * @excluding    allAreas, colorAxis, dragDrop, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking, useOhlcData\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @optionparent plotOptions.sma\n */\nSMAIndicator.defaultOptions = merge(LineSeries.defaultOptions, {\n    /**\n     * The name of the series as shown in the legend, tooltip etc. If not\n     * set, it will be based on a technical indicator type and default\n     * params.\n     *\n     * @type {string}\n     */\n    name: void 0,\n    tooltip: {\n        /**\n         * Number of decimals in indicator series.\n         */\n        valueDecimals: 4\n    },\n    /**\n     * The main series ID that indicator will be based on. Required for this\n     * indicator.\n     *\n     * @type {string}\n     */\n    linkedTo: void 0,\n    /**\n     * Whether to compare indicator to the main series values\n     * or indicator values.\n     *\n     * @sample {highstock} stock/plotoptions/series-comparetomain/\n     *         Difference between comparing SMA values to the main series\n     *         and its own values.\n     *\n     * @type {boolean}\n     */\n    compareToMain: false,\n    /**\n     * Parameters used in calculation of regression series' points.\n     */\n    params: {\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         */\n        index: 3,\n        /**\n         * The base period for indicator calculations. This is the number of\n         * data points which are taken into account for the indicator\n         * calculations.\n         */\n        period: 14\n    }\n});\nextend(SMAIndicator.prototype, {\n    calculateOn: {\n        chart: 'init'\n    },\n    hasDerivedData: true,\n    nameComponents: ['period'],\n    nameSuffixes: [], // E.g. Zig Zag uses extra '%'' in the legend name\n    useCommonDataGrouping: true\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('sma', SMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SMA_SMAIndicator = ((/* unused pure expression or super */ null && (SMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `SMA` series. If the [type](#series.sma.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.sma\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL, useOhlcData\n * @requires  stock/indicators/indicators\n * @apioption series.sma\n */\n(''); // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/EMA/EMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: EMAIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat, isArray: EMAIndicator_isArray, merge: EMAIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The EMA series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ema\n *\n * @augments Highcharts.Series\n */\nclass EMAIndicator extends EMAIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    accumulatePeriodPoints(period, index, yVal) {\n        let sum = 0, i = 0, y = 0;\n        while (i < period) {\n            y = index < 0 ? yVal[i] : yVal[i][index];\n            sum = sum + y;\n            i++;\n        }\n        return sum;\n    }\n    calculateEma(xVal, yVal, i, EMApercent, calEMA, index, SMA) {\n        const x = xVal[i - 1], yValue = index < 0 ?\n            yVal[i - 1] :\n            yVal[i - 1][index], y = typeof calEMA === 'undefined' ?\n            SMA : correctFloat((yValue * EMApercent) +\n            (calEMA * (1 - EMApercent)));\n        return [x, y];\n    }\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, EMApercent = 2 / (period + 1), EMA = [], xData = [], yData = [];\n        let calEMA, EMAPoint, i, index = -1, sum = 0, SMA = 0;\n        // Check period, if bigger than points length, skip\n        if (yValLen < period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (EMAIndicator_isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        sum = this.accumulatePeriodPoints(period, index, yVal);\n        // First point\n        SMA = sum / period;\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen + 1; i++) {\n            EMAPoint = this.calculateEma(xVal, yVal, i, EMApercent, calEMA, index, SMA);\n            EMA.push(EMAPoint);\n            xData.push(EMAPoint[0]);\n            yData.push(EMAPoint[1]);\n            calEMA = EMAPoint[1];\n        }\n        return {\n            values: EMA,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Exponential moving average indicator (EMA). This series requires the\n * `linkedTo` option to be set.\n *\n * @sample stock/indicators/ema\n * Exponential moving average indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @optionparent plotOptions.ema\n */\nEMAIndicator.defaultOptions = EMAIndicator_merge(EMAIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         *\n         * By default index value used to be set to 0. Since\n         * Highcharts Stock 7 by default index is set to 3\n         * which means that the ema indicator will be\n         * calculated using Close values.\n         */\n        index: 3,\n        period: 9 // @merge 14 in v6.2\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('ema', EMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const EMA_EMAIndicator = ((/* unused pure expression or super */ null && (EMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `EMA` series. If the [type](#series.ema.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ema\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @apioption series.ema\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/AD/ADIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { sma: ADIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { error: ADIndicator_error, extend: ADIndicator_extend, merge: ADIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The AD series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ad\n *\n * @augments Highcharts.Series\n */\nclass ADIndicator extends ADIndicator_SMAIndicator {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static populateAverage(xVal, yVal, yValVolume, i, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _period) {\n        const high = yVal[i][1], low = yVal[i][2], close = yVal[i][3], volume = yValVolume[i], adY = close === high && close === low || high === low ?\n            0 :\n            ((2 * close - low - high) / (high - low)) * volume, adX = xVal[i];\n        return [adX, adY];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, volumeSeriesID = params.volumeSeriesID, volumeSeries = series.chart.get(volumeSeriesID), yValVolume = volumeSeries?.getColumn('y'), yValLen = yVal ? yVal.length : 0, AD = [], xData = [], yData = [];\n        let len, i, ADPoint;\n        if (xVal.length <= period &&\n            yValLen &&\n            yVal[0].length !== 4) {\n            return;\n        }\n        if (!volumeSeries) {\n            ADIndicator_error('Series ' +\n                volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        // When i = period <-- skip first N-points\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen; i++) {\n            len = AD.length;\n            ADPoint = ADIndicator.populateAverage(xVal, yVal, yValVolume, i, period);\n            if (len > 0) {\n                ADPoint[1] += AD[len - 1][1];\n            }\n            AD.push(ADPoint);\n            xData.push(ADPoint[0]);\n            yData.push(ADPoint[1]);\n        }\n        return {\n            values: AD,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Accumulation Distribution (AD). This series requires `linkedTo` option to\n * be set.\n *\n * @sample stock/indicators/accumulation-distribution\n *         Accumulation/Distribution indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/accumulation-distribution\n * @optionparent plotOptions.ad\n */\nADIndicator.defaultOptions = ADIndicator_merge(ADIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         *\n         * @since 6.0.0\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nADIndicator_extend(ADIndicator.prototype, {\n    nameComponents: false,\n    nameBase: 'Accumulation/Distribution'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('ad', ADIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AD_ADIndicator = (ADIndicator);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `AD` series. If the [type](#series.ad.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ad\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/accumulation-distribution\n * @apioption series.ad\n */\n''; // Add doclet above to transpiled file\n\n;// ./code/es-modules/Stock/Indicators/AO/AOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop } = (external_highcharts_src_js_default_default());\n\nconst { column: { prototype: columnProto }, sma: AOIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: AOIndicator_extend, merge: AOIndicator_merge, correctFloat: AOIndicator_correctFloat, isArray: AOIndicator_isArray } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The AO series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ao\n *\n * @augments Highcharts.Series\n */\nclass AOIndicator extends AOIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    drawGraph() {\n        const indicator = this, options = indicator.options, points = indicator.points, userColor = indicator.userOptions.color, positiveColor = options.greaterBarColor, negativeColor = options.lowerBarColor, firstPoint = points[0];\n        let i;\n        if (!userColor && firstPoint) {\n            firstPoint.color = positiveColor;\n            for (i = 1; i < points.length; i++) {\n                if (points[i].y > points[i - 1].y) {\n                    points[i].color = positiveColor;\n                }\n                else if (points[i].y < points[i - 1].y) {\n                    points[i].color = negativeColor;\n                }\n                else {\n                    points[i].color = points[i - 1].color;\n                }\n            }\n        }\n    }\n    getValues(series) {\n        const shortPeriod = 5, longPeriod = 34, xVal = series.xData || [], yVal = series.yData || [], yValLen = yVal.length, AO = [], // 0- date, 1- Awesome Oscillator\n        xData = [], yData = [], high = 1, low = 2;\n        let shortSMA, // Shorter Period SMA\n        longSMA, // Longer Period SMA\n        awesome, shortLastIndex, longLastIndex, price, i, j, longSum = 0, shortSum = 0;\n        if (xVal.length <= longPeriod ||\n            !AOIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = 0; i < longPeriod - 1; i++) {\n            price = (yVal[i][high] + yVal[i][low]) / 2;\n            if (i >= longPeriod - shortPeriod) {\n                shortSum = AOIndicator_correctFloat(shortSum + price);\n            }\n            longSum = AOIndicator_correctFloat(longSum + price);\n        }\n        for (j = longPeriod - 1; j < yValLen; j++) {\n            price = (yVal[j][high] + yVal[j][low]) / 2;\n            shortSum = AOIndicator_correctFloat(shortSum + price);\n            longSum = AOIndicator_correctFloat(longSum + price);\n            shortSMA = shortSum / shortPeriod;\n            longSMA = longSum / longPeriod;\n            awesome = AOIndicator_correctFloat(shortSMA - longSMA);\n            AO.push([xVal[j], awesome]);\n            xData.push(xVal[j]);\n            yData.push(awesome);\n            shortLastIndex = j + 1 - shortPeriod;\n            longLastIndex = j + 1 - longPeriod;\n            shortSum = AOIndicator_correctFloat(shortSum -\n                (yVal[shortLastIndex][high] +\n                    yVal[shortLastIndex][low]) / 2);\n            longSum = AOIndicator_correctFloat(longSum -\n                (yVal[longLastIndex][high] +\n                    yVal[longLastIndex][low]) / 2);\n        }\n        return {\n            values: AO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Awesome Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`\n *\n * @sample {highstock} stock/indicators/ao\n *         Awesome\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               params, pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/ao\n * @optionparent plotOptions.ao\n */\nAOIndicator.defaultOptions = AOIndicator_merge(AOIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0\n    },\n    /**\n     * Color of the Awesome oscillator series bar that is greater than the\n     * previous one. Note that if a `color` is defined, the `color`\n     * takes precedence and the `greaterBarColor` is ignored.\n     *\n     * @sample {highstock} stock/indicators/ao/\n     *         greaterBarColor\n     *\n     * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since 7.0.0\n     */\n    greaterBarColor: \"#06b535\" /* Palette.positiveColor */,\n    /**\n     * Color of the Awesome oscillator series bar that is lower than the\n     * previous one. Note that if a `color` is defined, the `color`\n     * takes precedence and the `lowerBarColor` is ignored.\n     *\n     * @sample {highstock} stock/indicators/ao/\n     *         lowerBarColor\n     *\n     * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since 7.0.0\n     */\n    lowerBarColor: \"#f21313\" /* Palette.negativeColor */,\n    threshold: 0,\n    groupPadding: 0.2,\n    pointPadding: 0.2,\n    crisp: false,\n    states: {\n        hover: {\n            halo: {\n                size: 0\n            }\n        }\n    }\n});\nAOIndicator_extend(AOIndicator.prototype, {\n    nameBase: 'AO',\n    nameComponents: void 0,\n    // Columns support:\n    markerAttribs: noop,\n    getColumnMetrics: columnProto.getColumnMetrics,\n    crispCol: columnProto.crispCol,\n    translate: columnProto.translate,\n    drawPoints: columnProto.drawPoints\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('ao', AOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AO_AOIndicator = ((/* unused pure expression or super */ null && (AOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An `AO` series. If the [type](#series.ao.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ao\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/ao\n * @apioption series.ao\n */\n''; // For including the above in the doclets\n\n;// ./code/es-modules/Stock/Indicators/MultipleLinesComposition.js\n/**\n *\n *  (c) 2010-2025 Wojciech Chmiel\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: { prototype: smaProto } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { defined, error: MultipleLinesComposition_error, merge: MultipleLinesComposition_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar MultipleLinesComposition;\n(function (MultipleLinesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Additional lines DOCS names. Elements of linesApiNames array should\n     * be consistent with DOCS line names defined in your implementation.\n     * Notice that linesApiNames should have decreased amount of elements\n     * relative to pointArrayMap (without pointValKey).\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const linesApiNames = ['bottomLine'];\n    /**\n     * Lines ids. Required to plot appropriate amount of lines.\n     * Notice that pointArrayMap should have more elements than\n     * linesApiNames, because it contains main line and additional lines ids.\n     * Also it should be consistent with amount of lines calculated in\n     * getValues method from your implementation.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const pointArrayMap = ['top', 'bottom'];\n    /**\n     * Names of the lines, between which the area should be plotted.\n     * If the drawing of the area should\n     * be disabled for some indicators, leave this option as an empty array.\n     * Names should be the same as the names in the pointArrayMap.\n     *\n     * @private\n     * @type {Array<string>}\n     */\n    const areaLinesNames = ['top'];\n    /**\n     * Main line id.\n     *\n     * @private\n     * @type {string}\n     */\n    const pointValKey = 'top';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Composition useful for all indicators that have more than one line.\n     * Compose it with your implementation where you will provide the\n     * `getValues` method appropriate to your indicator and `pointArrayMap`,\n     * `pointValKey`, `linesApiNames` properties. Notice that `pointArrayMap`\n     * should be consistent with the amount of lines calculated in the\n     * `getValues` method.\n     *\n     * @private\n     */\n    function compose(IndicatorClass) {\n        const proto = IndicatorClass.prototype;\n        proto.linesApiNames = (proto.linesApiNames ||\n            linesApiNames.slice());\n        proto.pointArrayMap = (proto.pointArrayMap ||\n            pointArrayMap.slice());\n        proto.pointValKey = (proto.pointValKey ||\n            pointValKey);\n        proto.areaLinesNames = (proto.areaLinesNames ||\n            areaLinesNames.slice());\n        proto.drawGraph = indicatorDrawGraph;\n        proto.getGraphPath = indicatorGetGraphPath;\n        proto.toYData = indicatorToYData;\n        proto.translate = indicatorTranslate;\n        return IndicatorClass;\n    }\n    MultipleLinesComposition.compose = compose;\n    /**\n     * Generate the API name of the line\n     *\n     * @private\n     * @param propertyName name of the line\n     */\n    function getLineName(propertyName) {\n        return ('plot' +\n            propertyName.charAt(0).toUpperCase() +\n            propertyName.slice(1));\n    }\n    /**\n     * Create translatedLines Collection based on pointArrayMap.\n     *\n     * @private\n     * @param {string} [excludedValue]\n     *        Main line id\n     * @return {Array<string>}\n     *         Returns translated lines names without excluded value.\n     */\n    function getTranslatedLinesNames(indicator, excludedValue) {\n        const translatedLines = [];\n        (indicator.pointArrayMap || []).forEach((propertyName) => {\n            if (propertyName !== excludedValue) {\n                translatedLines.push(getLineName(propertyName));\n            }\n        });\n        return translatedLines;\n    }\n    /**\n     * Draw main and additional lines.\n     *\n     * @private\n     */\n    function indicatorDrawGraph() {\n        const indicator = this, pointValKey = indicator.pointValKey, linesApiNames = indicator.linesApiNames, areaLinesNames = indicator.areaLinesNames, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, \n        // Additional lines point place holders:\n        secondaryLines = [], secondaryLinesNames = getTranslatedLinesNames(indicator, pointValKey);\n        let pointsLength = mainLinePoints.length, point;\n        // Generate points for additional lines:\n        secondaryLinesNames.forEach((plotLine, index) => {\n            // Create additional lines point place holders\n            secondaryLines[index] = [];\n            while (pointsLength--) {\n                point = mainLinePoints[pointsLength];\n                secondaryLines[index].push({\n                    x: point.x,\n                    plotX: point.plotX,\n                    plotY: point[plotLine],\n                    isNull: !defined(point[plotLine])\n                });\n            }\n            pointsLength = mainLinePoints.length;\n        });\n        // Modify options and generate area fill:\n        if (indicator.userOptions.fillColor && areaLinesNames.length) {\n            const index = secondaryLinesNames.indexOf(getLineName(areaLinesNames[0])), secondLinePoints = secondaryLines[index], firstLinePoints = areaLinesNames.length === 1 ?\n                mainLinePoints :\n                secondaryLines[secondaryLinesNames.indexOf(getLineName(areaLinesNames[1]))], originalColor = indicator.color;\n            indicator.points = firstLinePoints;\n            indicator.nextPoints = secondLinePoints;\n            indicator.color = indicator.userOptions.fillColor;\n            indicator.options = MultipleLinesComposition_merge(mainLinePoints, gappedExtend);\n            indicator.graph = indicator.area;\n            indicator.fillGraph = true;\n            smaProto.drawGraph.call(indicator);\n            indicator.area = indicator.graph;\n            // Clean temporary properties:\n            delete indicator.nextPoints;\n            delete indicator.fillGraph;\n            indicator.color = originalColor;\n        }\n        // Modify options and generate additional lines:\n        linesApiNames.forEach((lineName, i) => {\n            if (secondaryLines[i]) {\n                indicator.points = secondaryLines[i];\n                if (mainLineOptions[lineName]) {\n                    indicator.options = MultipleLinesComposition_merge(mainLineOptions[lineName].styles, gappedExtend);\n                }\n                else {\n                    MultipleLinesComposition_error('Error: \"There is no ' + lineName +\n                        ' in DOCS options declared. Check if linesApiNames' +\n                        ' are consistent with your DOCS line names.\"');\n                }\n                indicator.graph = indicator['graph' + lineName];\n                smaProto.drawGraph.call(indicator);\n                // Now save lines:\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            else {\n                MultipleLinesComposition_error('Error: \"' + lineName + ' doesn\\'t have equivalent ' +\n                    'in pointArrayMap. To many elements in linesApiNames ' +\n                    'relative to pointArrayMap.\"');\n            }\n        });\n        // Restore options and draw a main line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        smaProto.drawGraph.call(indicator);\n    }\n    /**\n     * Create the path based on points provided as argument.\n     * If indicator.nextPoints option is defined, create the areaFill.\n     *\n     * @private\n     * @param points Points on which the path should be created\n     */\n    function indicatorGetGraphPath(points) {\n        let areaPath, path = [], higherAreaPath = [];\n        points = points || this.points;\n        // Render Span\n        if (this.fillGraph && this.nextPoints) {\n            areaPath = smaProto.getGraphPath.call(this, this.nextPoints);\n            if (areaPath && areaPath.length) {\n                areaPath[0][0] = 'L';\n                path = smaProto.getGraphPath.call(this, points);\n                higherAreaPath = areaPath.slice(0, path.length);\n                // Reverse points, so that the areaFill will start from the end:\n                for (let i = higherAreaPath.length - 1; i >= 0; i--) {\n                    path.push(higherAreaPath[i]);\n                }\n            }\n        }\n        else {\n            path = smaProto.getGraphPath.apply(this, arguments);\n        }\n        return path;\n    }\n    /**\n     * @private\n     * @param {Highcharts.Point} point\n     *        Indicator point\n     * @return {Array<number>}\n     *         Returns point Y value for all lines\n     */\n    function indicatorToYData(point) {\n        const pointColl = [];\n        (this.pointArrayMap || []).forEach((propertyName) => {\n            pointColl.push(point[propertyName]);\n        });\n        return pointColl;\n    }\n    /**\n     * Add lines plot pixel values.\n     *\n     * @private\n     */\n    function indicatorTranslate() {\n        const pointArrayMap = this.pointArrayMap;\n        let LinesNames = [], value;\n        LinesNames = getTranslatedLinesNames(this);\n        smaProto.translate.apply(this, arguments);\n        this.points.forEach((point) => {\n            pointArrayMap.forEach((propertyName, i) => {\n                value = point[propertyName];\n                // If the modifier, like for example compare exists,\n                // modified the original value by that method, #15867.\n                if (this.dataModify) {\n                    value = this.dataModify.modifyValue(value);\n                }\n                if (value !== null) {\n                    point[LinesNames[i]] = this.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n})(MultipleLinesComposition || (MultipleLinesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Indicators_MultipleLinesComposition = (MultipleLinesComposition);\n\n;// ./code/es-modules/Stock/Indicators/Aroon/AroonIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: AroonIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: AroonIndicator_extend, merge: AroonIndicator_merge, pick: AroonIndicator_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils\n// Index of element with extreme value from array (min or max)\n/**\n * @private\n */\nfunction getExtremeIndexInArray(arr, extreme) {\n    let extremeValue = arr[0], valueIndex = 0, i;\n    for (i = 1; i < arr.length; i++) {\n        if (extreme === 'max' && arr[i] >= extremeValue ||\n            extreme === 'min' && arr[i] <= extremeValue) {\n            extremeValue = arr[i];\n            valueIndex = i;\n        }\n    }\n    return valueIndex;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Aroon series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.aroon\n *\n * @augments Highcharts.Series\n */\nclass AroonIndicator extends AroonIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // 0- date, 1- Aroon Up, 2- Aroon Down\n        AR = [], xData = [], yData = [], low = 2, high = 1;\n        let aroonUp, aroonDown, xLow, xHigh, i, slicedY;\n        // For a N-period, we start from N-1 point, to calculate Nth point\n        // That is why we later need to comprehend slice() elements list\n        // with (+1)\n        for (i = period - 1; i < yValLen; i++) {\n            slicedY = yVal.slice(i - period + 1, i + 2);\n            xLow = getExtremeIndexInArray(slicedY.map(function (elem) {\n                return AroonIndicator_pick(elem[low], elem);\n            }), 'min');\n            xHigh = getExtremeIndexInArray(slicedY.map(function (elem) {\n                return AroonIndicator_pick(elem[high], elem);\n            }), 'max');\n            aroonUp = (xHigh / period) * 100;\n            aroonDown = (xLow / period) * 100;\n            if (xVal[i + 1]) {\n                AR.push([xVal[i + 1], aroonUp, aroonDown]);\n                xData.push(xVal[i + 1]);\n                yData.push([aroonUp, aroonDown]);\n            }\n        }\n        return {\n            values: AR,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Aroon. This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/aroon\n *         Aroon\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/aroon\n * @optionparent plotOptions.aroon\n */\nAroonIndicator.defaultOptions = AroonIndicator_merge(AroonIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of aroon series points.\n     *\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        period: 25\n    },\n    marker: {\n        enabled: false\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b><br/>Aroon Up: {point.y}<br/>Aroon Down: {point.aroonDown}<br/>'\n    },\n    /**\n     * AroonDown line options.\n     */\n    aroonDown: {\n        /**\n         * Styles for an aroonDown line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.aroon.color](#plotOptions.aroon.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nAroonIndicator_extend(AroonIndicator.prototype, {\n    areaLinesNames: [],\n    linesApiNames: ['aroonDown'],\n    nameBase: 'Aroon',\n    pointArrayMap: ['y', 'aroonDown'],\n    pointValKey: 'y'\n});\nIndicators_MultipleLinesComposition.compose(AroonIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('aroon', AroonIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Aroon_AroonIndicator = ((/* unused pure expression or super */ null && (AroonIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Aroon indicator. If the [type](#series.aroon.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.aroon\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/aroon\n * @apioption series.aroon\n */\n''; // To avoid removal of the above jsdoc\n\n;// ./code/es-modules/Stock/Indicators/AroonOscillator/AroonOscillatorIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { aroon: AroonOscillatorIndicator_AroonIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: AroonOscillatorIndicator_extend, merge: AroonOscillatorIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Aroon Oscillator series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.aroonoscillator\n *\n * @augments Highcharts.Series\n */\nclass AroonOscillatorIndicator extends AroonOscillatorIndicator_AroonIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        // 0- date, 1- Aroon Oscillator\n        const ARO = [], xData = [], yData = [];\n        let aroonUp, aroonDown, oscillator, i;\n        const aroon = super.getValues.call(this, series, params);\n        for (i = 0; i < aroon.yData.length; i++) {\n            aroonUp = aroon.yData[i][0];\n            aroonDown = aroon.yData[i][1];\n            oscillator = aroonUp - aroonDown;\n            ARO.push([aroon.xData[i], oscillator]);\n            xData.push(aroon.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: ARO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Aroon Oscillator. This series requires the `linkedTo` option to be set\n * and should be loaded after the `stock/indicators/indicators.js` and\n * `stock/indicators/aroon.js`.\n *\n * @sample {highstock} stock/indicators/aroon-oscillator\n *         Aroon Oscillator\n *\n * @extends      plotOptions.aroon\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, aroonDown, colorAxis, compare, compareBase,\n *               joinBy, keys, navigatorOptions, pointInterval,\n *               pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *               showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/aroon\n * @requires     stock/indicators/aroon-oscillator\n * @optionparent plotOptions.aroonoscillator\n */\nAroonOscillatorIndicator.defaultOptions = AroonOscillatorIndicator_merge(AroonOscillatorIndicator_AroonIndicator.defaultOptions, {\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b>: {point.y}'\n    }\n});\nAroonOscillatorIndicator_extend(AroonOscillatorIndicator.prototype, {\n    nameBase: 'Aroon Oscillator',\n    linesApiNames: [],\n    pointArrayMap: ['y'],\n    pointValKey: 'y'\n});\nIndicators_MultipleLinesComposition.compose(AroonOscillatorIndicator_AroonIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('aroonoscillator', AroonOscillatorIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const AroonOscillator_AroonOscillatorIndicator = ((/* unused pure expression or super */ null && (AroonOscillatorIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An `Aroon Oscillator` series. If the [type](#series.aroonoscillator.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.aroonoscillator\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, aroonDown, colorAxis, compare, compareBase, dataParser,\n *            dataURL, joinBy, keys, navigatorOptions, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *            showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/aroon\n * @requires  stock/indicators/aroon-oscillator\n * @apioption series.aroonoscillator\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/ATR/ATRIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: ATRIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray: ATRIndicator_isArray, merge: ATRIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction accumulateAverage(points, xVal, yVal, i) {\n    const xValue = xVal[i], yValue = yVal[i];\n    points.push([xValue, yValue]);\n}\n/**\n * @private\n */\nfunction getTR(currentPoint, prevPoint) {\n    const pointY = currentPoint, prevY = prevPoint, HL = pointY[1] - pointY[2], HCp = typeof prevY === 'undefined' ? 0 : Math.abs(pointY[1] - prevY[3]), LCp = typeof prevY === 'undefined' ? 0 : Math.abs(pointY[2] - prevY[3]), TR = Math.max(HL, HCp, LCp);\n    return TR;\n}\n/**\n * @private\n */\nfunction populateAverage(points, xVal, yVal, i, period, prevATR) {\n    const x = xVal[i - 1], TR = getTR(yVal[i - 1], yVal[i - 2]), y = (((prevATR * (period - 1)) + TR) / period);\n    return [x, y];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ATR series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.atr\n *\n * @augments Highcharts.Series\n */\nclass ATRIndicator extends ATRIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, xValue = xVal[0], yValue = yVal[0], points = [[xValue, yValue]], ATR = [], xData = [], yData = [];\n        let point, i, prevATR = 0, range = 1, TR = 0;\n        if ((xVal.length <= period) ||\n            !ATRIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = 1; i <= yValLen; i++) {\n            accumulateAverage(points, xVal, yVal, i);\n            if (period < range) {\n                point = populateAverage(points, xVal, yVal, i, period, prevATR);\n                prevATR = point[1];\n                ATR.push(point);\n                xData.push(point[0]);\n                yData.push(point[1]);\n            }\n            else if (period === range) {\n                prevATR = TR / (i - 1);\n                ATR.push([xVal[i - 1], prevATR]);\n                xData.push(xVal[i - 1]);\n                yData.push(prevATR);\n                range++;\n            }\n            else {\n                TR += getTR(yVal[i - 1], yVal[i - 2]);\n                range++;\n            }\n        }\n        return {\n            values: ATR,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Average true range indicator (ATR). This series requires `linkedTo`\n * option to be set.\n *\n * @sample stock/indicators/atr\n *         ATR indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/atr\n * @optionparent plotOptions.atr\n */\nATRIndicator.defaultOptions = ATRIndicator_merge(ATRIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0 // Unused index, do not inherit (#15362)\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('atr', ATRIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ATR_ATRIndicator = ((/* unused pure expression or super */ null && (ATRIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `ATR` series. If the [type](#series.atr.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.atr\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/atr\n * @apioption series.atr\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/BB/BBIndicator.js\n/**\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: BBIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: BBIndicator_extend, isArray: BBIndicator_isArray, merge: BBIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction getStandardDeviation(arr, index, isOHLC, mean) {\n    const arrLen = arr.length;\n    let i = 0, std = 0, value, variance = 0;\n    for (; i < arrLen; i++) {\n        value = (isOHLC ? arr[i][index] : arr[i]) - mean;\n        variance += value * value;\n    }\n    variance = variance / (arrLen - 1);\n    std = Math.sqrt(variance);\n    return std;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Bollinger Bands series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.bb\n *\n * @augments Highcharts.Series\n */\nclass BBIndicator extends BBIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = BBIndicator_merge({\n            topLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            bottomLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            }\n        }, this.options);\n    }\n    getValues(series, params) {\n        const period = params.period, standardDeviation = params.standardDeviation, xData = [], yData = [], xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // 0- date, 1-middle line, 2-top line, 3-bottom line\n        BB = [];\n        // Middle line, top line and bottom line\n        let ML, TL, BL, date, slicedX, slicedY, stdDev, point, i;\n        if (xVal.length < period) {\n            return;\n        }\n        const isOHLC = BBIndicator_isArray(yVal[0]);\n        for (i = period; i <= yValLen; i++) {\n            slicedX = xVal.slice(i - period, i);\n            slicedY = yVal.slice(i - period, i);\n            point = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.getValues.call(this, {\n                xData: slicedX,\n                yData: slicedY\n            }, params);\n            date = point.xData[0];\n            ML = point.yData[0];\n            stdDev = getStandardDeviation(slicedY, params.index, isOHLC, ML);\n            TL = ML + standardDeviation * stdDev;\n            BL = ML - standardDeviation * stdDev;\n            BB.push([date, TL, ML, BL]);\n            xData.push(date);\n            yData.push([TL, ML, BL]);\n        }\n        return {\n            values: BB,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Bollinger bands (BB). This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/bollinger-bands\n *         Bollinger bands\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/bollinger-bands\n * @optionparent plotOptions.bb\n */\nBBIndicator.defaultOptions = BBIndicator_merge(BBIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Option for fill color between lines in Bollinger Bands Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      Background fill between lines.\n     *\n     * @type      {Highcharts.ColorType}\n     * @since     9.3.2\n     * @apioption plotOptions.bb.fillColor\n     */\n    /**\n     * Parameters used in calculation of the regression points.\n     */\n    params: {\n        period: 20,\n        /**\n         * Standard deviation for top and bottom bands.\n         */\n        standardDeviation: 2,\n        index: 3\n    },\n    /**\n     * Bottom line options.\n     */\n    bottomLine: {\n        /**\n         * Styles for the bottom line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.bb.color](#plotOptions.bb.color).\n             *\n             * @type  {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * Top line options.\n     *\n     * @extends plotOptions.bb.bottomLine\n     */\n    topLine: {\n        /**\n         * Styles for the top line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.bb.color](#plotOptions.bb.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'\n    },\n    marker: {\n        enabled: false\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nBBIndicator_extend(BBIndicator.prototype, {\n    areaLinesNames: ['top', 'bottom'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    nameComponents: ['period', 'standardDeviation'],\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(BBIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('bb', BBIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const BB_BBIndicator = ((/* unused pure expression or super */ null && (BBIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A bollinger bands indicator. If the [type](#series.bb.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.bb\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/bollinger-bands\n * @apioption series.bb\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/CCI/CCIIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nconst { sma: CCIIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray: CCIIndicator_isArray, merge: CCIIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction sumArray(array) {\n    return array.reduce(function (prev, cur) {\n        return prev + cur;\n    }, 0);\n}\n/**\n * @private\n */\nfunction meanDeviation(arr, sma) {\n    const len = arr.length;\n    let sum = 0, i;\n    for (i = 0; i < len; i++) {\n        sum += Math.abs(sma - (arr[i]));\n    }\n    return sum;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CCI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cci\n *\n * @augments Highcharts.Series\n */\nclass CCIIndicator extends CCIIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, TP = [], CCI = [], xData = [], yData = [];\n        let CCIPoint, p, periodTP = [], len, range = 1, smaTP, TPtemp, meanDev, i;\n        // CCI requires close value\n        if (xVal.length <= period ||\n            !CCIIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // Accumulate first N-points\n        while (range < period) {\n            p = yVal[range - 1];\n            TP.push((p[1] + p[2] + p[3]) / 3);\n            range++;\n        }\n        for (i = period; i <= yValLen; i++) {\n            p = yVal[i - 1];\n            TPtemp = (p[1] + p[2] + p[3]) / 3;\n            len = TP.push(TPtemp);\n            periodTP = TP.slice(len - period);\n            smaTP = sumArray(periodTP) / period;\n            meanDev = meanDeviation(periodTP, smaTP) / period;\n            CCIPoint = ((TPtemp - smaTP) / (0.015 * meanDev));\n            CCI.push([xVal[i - 1], CCIPoint]);\n            xData.push(xVal[i - 1]);\n            yData.push(CCIPoint);\n        }\n        return {\n            values: CCI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Commodity Channel Index (CCI). This series requires `linkedTo` option to\n * be set.\n *\n * @sample stock/indicators/cci\n *         CCI indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cci\n * @optionparent plotOptions.cci\n */\nCCIIndicator.defaultOptions = CCIIndicator_merge(CCIIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0 // Unused index, do not inherit (#15362)\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('cci', CCIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CCI_CCIIndicator = ((/* unused pure expression or super */ null && (CCIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CCI` series. If the [type](#series.cci.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cci\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cci\n * @apioption series.cci\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/CMF/CMFIndicator.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Sebastian Domas\n *\n *  Chaikin Money Flow indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: CMFIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge: CMFIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMF series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmf\n *\n * @augments Highcharts.Series\n */\nclass CMFIndicator extends CMFIndicator_SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nameBase = 'Chaikin Money Flow';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Checks if the series and volumeSeries are accessible, number of\n     * points.x is longer than period, is series has OHLC data\n     * @private\n     * @param {Highcharts.CMFIndicator} this indicator to use.\n     * @return {boolean} True if series is valid and can be computed,\n     * otherwise false.\n     */\n    isValid() {\n        const chart = this.chart, options = this.options, series = this.linkedParent, volumeSeries = (this.volumeSeries ||\n            (this.volumeSeries =\n                chart.get(options.params.volumeSeriesID))), isSeriesOHLC = (series?.pointArrayMap?.length === 4);\n        /**\n         * @private\n         * @param {Highcharts.Series} serie to check length validity on.\n         * @return {boolean|undefined} true if length is valid.\n         */\n        function isLengthValid(serie) {\n            return serie.dataTable.rowCount >=\n                options.params.period;\n        }\n        return !!(series &&\n            volumeSeries &&\n            isLengthValid(series) &&\n            isLengthValid(volumeSeries) && isSeriesOHLC);\n    }\n    /**\n     * Returns indicator's data.\n     * @private\n     * @param {Highcharts.CMFIndicator} this indicator to use.\n     * @param {Highcharts.Series} series to calculate values from\n     * @param {Highcharts.CMFIndicatorParamsOptions} params to pass\n     * @return {boolean|Highcharts.IndicatorNullableValuesObject} Returns false if the\n     * indicator is not valid, otherwise returns Values object.\n     */\n    getValues(series, params) {\n        if (!this.isValid()) {\n            return;\n        }\n        return this.getMoneyFlow(series.xData, series.yData, this.volumeSeries.getColumn('y'), params.period);\n    }\n    /**\n     * @private\n     *\n     * @param {Array<number>} xData\n     * x timestamp values\n     *\n     * @param {Array<number>} seriesYData\n     * yData of basic series\n     *\n     * @param {Array<number>} volumeSeriesYData\n     * yData of volume series\n     *\n     * @param {number} period\n     * indicator's param\n     *\n     * @return {Highcharts.IndicatorNullableValuesObject}\n     * object containing computed money flow data\n     */\n    getMoneyFlow(xData, seriesYData, volumeSeriesYData, period) {\n        const len = seriesYData.length, moneyFlowVolume = [], moneyFlowXData = [], moneyFlowYData = [], values = [];\n        let i, point, nullIndex = -1, sumVolume = 0, sumMoneyFlowVolume = 0;\n        /**\n         * Calculates money flow volume, changes i, nullIndex vars from\n         * upper scope!\n         *\n         * @private\n         *\n         * @param {Array<number>} ohlc\n         * OHLC point\n         *\n         * @param {number} volume\n         * Volume point's y value\n         *\n         * @return {number|null}\n         * Volume * moneyFlowMultiplier\n         */\n        function getMoneyFlowVolume(ohlc, volume) {\n            const high = ohlc[1], low = ohlc[2], close = ohlc[3], isValid = volume !== null &&\n                high !== null &&\n                low !== null &&\n                close !== null &&\n                high !== low;\n            /**\n             * @private\n             * @param {number} h\n             * High value\n             * @param {number} l\n             * Low value\n             * @param {number} c\n             * Close value\n             * @return {number}\n             * Calculated multiplier for the point\n             */\n            function getMoneyFlowMultiplier(h, l, c) {\n                return ((c - l) - (h - c)) / (h - l);\n            }\n            return isValid ?\n                getMoneyFlowMultiplier(high, low, close) * volume :\n                ((nullIndex = i), null);\n        }\n        if (period > 0 && period <= len) {\n            for (i = 0; i < period; i++) {\n                moneyFlowVolume[i] = getMoneyFlowVolume(seriesYData[i], volumeSeriesYData[i]);\n                sumVolume += volumeSeriesYData[i];\n                sumMoneyFlowVolume += moneyFlowVolume[i];\n            }\n            moneyFlowXData.push(xData[i - 1]);\n            moneyFlowYData.push(i - nullIndex >= period && sumVolume !== 0 ?\n                sumMoneyFlowVolume / sumVolume :\n                null);\n            values.push([moneyFlowXData[0], moneyFlowYData[0]]);\n            for (; i < len; i++) {\n                moneyFlowVolume[i] = getMoneyFlowVolume(seriesYData[i], volumeSeriesYData[i]);\n                sumVolume -= volumeSeriesYData[i - period];\n                sumVolume += volumeSeriesYData[i];\n                sumMoneyFlowVolume -= moneyFlowVolume[i - period];\n                sumMoneyFlowVolume += moneyFlowVolume[i];\n                point = [\n                    xData[i],\n                    i - nullIndex >= period ?\n                        sumMoneyFlowVolume / sumVolume :\n                        null\n                ];\n                moneyFlowXData.push(point[0]);\n                moneyFlowYData.push(point[1]);\n                values.push([point[0], point[1]]);\n            }\n        }\n        return {\n            values: values,\n            xData: moneyFlowXData,\n            yData: moneyFlowYData\n        };\n    }\n}\n/**\n * Chaikin Money Flow indicator (cmf).\n *\n * @sample stock/indicators/cmf/\n *         Chaikin Money Flow indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @excluding    animationLimit\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cmf\n * @optionparent plotOptions.cmf\n */\nCMFIndicator.defaultOptions = CMFIndicator_merge(CMFIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of another series to use its data as volume data for the\n         * indicator calculation.\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('cmf', CMFIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CMF_CMFIndicator = ((/* unused pure expression or super */ null && (CMFIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMF` series. If the [type](#series.cmf.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmf\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmf\n * @apioption series.cmf\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/DMI/DMIIndicator.js\n/* *\n *  (c) 2010-2025 Rafal Sebestjanski\n *\n *  Directional Movement Index (DMI) indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: DMIIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: DMIIndicator_correctFloat, extend: DMIIndicator_extend, isArray: DMIIndicator_isArray, merge: DMIIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Directional Movement Index (DMI) series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dmi\n *\n * @augments Highcharts.Series\n */\nclass DMIIndicator extends DMIIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    calculateDM(yVal, i, isPositiveDM) {\n        const currentHigh = yVal[i][1], currentLow = yVal[i][2], previousHigh = yVal[i - 1][1], previousLow = yVal[i - 1][2];\n        let DM;\n        if (currentHigh - previousHigh > previousLow - currentLow) {\n            // For +DM\n            DM = isPositiveDM ? Math.max(currentHigh - previousHigh, 0) : 0;\n        }\n        else {\n            // For -DM\n            DM = !isPositiveDM ? Math.max(previousLow - currentLow, 0) : 0;\n        }\n        return DMIIndicator_correctFloat(DM);\n    }\n    calculateDI(smoothedDM, tr) {\n        return smoothedDM / tr * 100;\n    }\n    calculateDX(plusDI, minusDI) {\n        return DMIIndicator_correctFloat(Math.abs(plusDI - minusDI) / Math.abs(plusDI + minusDI) * 100);\n    }\n    smoothValues(accumulatedValues, currentValue, period) {\n        return DMIIndicator_correctFloat(accumulatedValues - accumulatedValues / period + currentValue);\n    }\n    getTR(currentPoint, prevPoint) {\n        return DMIIndicator_correctFloat(Math.max(\n        // `currentHigh - currentLow`\n        currentPoint[1] - currentPoint[2], \n        // `currentHigh - previousClose`\n        !prevPoint ? 0 : Math.abs(currentPoint[1] - prevPoint[3]), \n        // `currentLow - previousClose`\n        !prevPoint ? 0 : Math.abs(currentPoint[2] - prevPoint[3])));\n    }\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, DMI = [], xData = [], yData = [];\n        if (\n        // Check period, if bigger than points length, skip\n        (xVal.length <= period) ||\n            // Only ohlc data is valid\n            !DMIIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        let prevSmoothedPlusDM = 0, prevSmoothedMinusDM = 0, prevSmoothedTR = 0, i;\n        for (i = 1; i < yValLen; i++) {\n            let smoothedPlusDM, smoothedMinusDM, smoothedTR, plusDM, // +DM\n            minusDM, // -DM\n            TR, plusDI, // +DI\n            minusDI, // -DI\n            DX;\n            if (i <= period) {\n                plusDM = this.calculateDM(yVal, i, true);\n                minusDM = this.calculateDM(yVal, i);\n                TR = this.getTR(yVal[i], yVal[i - 1]);\n                // Accumulate first period values to smooth them later\n                prevSmoothedPlusDM += plusDM;\n                prevSmoothedMinusDM += minusDM;\n                prevSmoothedTR += TR;\n                // Get all values for the first point\n                if (i === period) {\n                    plusDI = this.calculateDI(prevSmoothedPlusDM, prevSmoothedTR);\n                    minusDI = this.calculateDI(prevSmoothedMinusDM, prevSmoothedTR);\n                    DX = this.calculateDX(prevSmoothedPlusDM, prevSmoothedMinusDM);\n                    DMI.push([xVal[i], DX, plusDI, minusDI]);\n                    xData.push(xVal[i]);\n                    yData.push([DX, plusDI, minusDI]);\n                }\n            }\n            else {\n                // Calculate current values\n                plusDM = this.calculateDM(yVal, i, true);\n                minusDM = this.calculateDM(yVal, i);\n                TR = this.getTR(yVal[i], yVal[i - 1]);\n                // Smooth +DM, -DM and TR\n                smoothedPlusDM = this.smoothValues(prevSmoothedPlusDM, plusDM, period);\n                smoothedMinusDM = this.smoothValues(prevSmoothedMinusDM, minusDM, period);\n                smoothedTR = this.smoothValues(prevSmoothedTR, TR, period);\n                // Save current smoothed values for the next step\n                prevSmoothedPlusDM = smoothedPlusDM;\n                prevSmoothedMinusDM = smoothedMinusDM;\n                prevSmoothedTR = smoothedTR;\n                // Get all next points (except the first one calculated above)\n                plusDI = this.calculateDI(prevSmoothedPlusDM, prevSmoothedTR);\n                minusDI = this.calculateDI(prevSmoothedMinusDM, prevSmoothedTR);\n                DX = this.calculateDX(prevSmoothedPlusDM, prevSmoothedMinusDM);\n                DMI.push([xVal[i], DX, plusDI, minusDI]);\n                xData.push(xVal[i]);\n                yData.push([DX, plusDI, minusDI]);\n            }\n        }\n        return {\n            values: DMI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Directional Movement Index (DMI).\n * This series requires the `linkedTo` option to be set and should\n * be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/dmi\n *         DMI indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/dmi\n * @optionparent plotOptions.dmi\n */\nDMIIndicator.defaultOptions = DMIIndicator_merge(DMIIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0 // Unused index, do not inherit (#15362)\n    },\n    marker: {\n        enabled: false\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color: {point.color}\">' +\n            '\\u25CF</span><b> {series.name}</b><br/>' +\n            '<span style=\"color: {point.color}\">DX</span>: {point.y}<br/>' +\n            '<span style=\"color: ' +\n            '{point.series.options.plusDILine.styles.lineColor}\">' +\n            '+DI</span>: {point.plusDI}<br/>' +\n            '<span style=\"color: ' +\n            '{point.series.options.minusDILine.styles.lineColor}\">' +\n            '-DI</span>: {point.minusDI}<br/>'\n    },\n    /**\n     * +DI line options.\n     */\n    plusDILine: {\n        /**\n         * Styles for the +DI line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: \"#06b535\" /* Palette.positiveColor */ // Green-ish\n        }\n    },\n    /**\n     * -DI line options.\n     */\n    minusDILine: {\n        /**\n         * Styles for the -DI line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: \"#f21313\" /* Palette.negativeColor */ // Red-ish\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nDMIIndicator_extend(DMIIndicator.prototype, {\n    areaLinesNames: [],\n    nameBase: 'DMI',\n    linesApiNames: ['plusDILine', 'minusDILine'],\n    pointArrayMap: ['y', 'plusDI', 'minusDI'],\n    parallelArrays: ['x', 'y', 'plusDI', 'minusDI'],\n    pointValKey: 'y'\n});\nIndicators_MultipleLinesComposition.compose(DMIIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('dmi', DMIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DMI_DMIIndicator = ((/* unused pure expression or super */ null && (DMIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The Directional Movement Index (DMI) indicator series.\n * If the [type](#series.dmi.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dmi\n * @since 9.1.0\n * @product   highstock\n * @excluding allAreas, colorAxis,  dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/dmi\n * @apioption series.dmi\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/DPO/DPOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: DPOIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: DPOIndicator_extend, merge: DPOIndicator_merge, correctFloat: DPOIndicator_correctFloat, pick: DPOIndicator_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction accumulatePoints(sum, yVal, i, index, subtract) {\n    const price = DPOIndicator_pick(yVal[i][index], yVal[i]);\n    if (subtract) {\n        return DPOIndicator_correctFloat(sum - price);\n    }\n    return DPOIndicator_correctFloat(sum + price);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The DPO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dpo\n *\n * @augments Highcharts.Series\n */\nclass DPOIndicator extends DPOIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, index = params.index, offset = Math.floor(period / 2 + 1), range = period + offset, xVal = series.xData || [], yVal = series.yData || [], yValLen = yVal.length, \n        // 0- date, 1- Detrended Price Oscillator\n        DPO = [], xData = [], yData = [];\n        let oscillator, periodIndex, rangeIndex, price, i, j, sum = 0;\n        if (xVal.length <= range) {\n            return;\n        }\n        // Accumulate first N-points for SMA\n        for (i = 0; i < period - 1; i++) {\n            sum = accumulatePoints(sum, yVal, i, index);\n        }\n        // Detrended Price Oscillator formula:\n        // DPO = Price - Simple moving average [from (n / 2 + 1) days ago]\n        for (j = 0; j <= yValLen - range; j++) {\n            periodIndex = j + period - 1;\n            rangeIndex = j + range - 1;\n            // Adding the last period point\n            sum = accumulatePoints(sum, yVal, periodIndex, index);\n            price = DPOIndicator_pick(yVal[rangeIndex][index], yVal[rangeIndex]);\n            oscillator = price - sum / period;\n            // Subtracting the first period point\n            sum = accumulatePoints(sum, yVal, j, index, true);\n            DPO.push([xVal[rangeIndex], oscillator]);\n            xData.push(xVal[rangeIndex]);\n            yData.push(oscillator);\n        }\n        return {\n            values: DPO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Detrended Price Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/dpo\n *         Detrended Price Oscillator\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/dpo\n * @optionparent plotOptions.dpo\n */\nDPOIndicator.defaultOptions = DPOIndicator_merge(DPOIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Detrended Price Oscillator series\n     * points.\n     */\n    params: {\n        index: 0,\n        /**\n         * Period for Detrended Price Oscillator\n         */\n        period: 21\n    }\n});\nDPOIndicator_extend(DPOIndicator.prototype, {\n    nameBase: 'DPO'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('dpo', DPOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DPO_DPOIndicator = ((/* unused pure expression or super */ null && (DPOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Detrended Price Oscillator. If the [type](#series.dpo.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dpo\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/dpo\n * @apioption series.dpo\n */\n''; // To include the above in the js output'\n\n;// ./code/es-modules/Stock/Indicators/Chaikin/ChaikinIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n // For historic reasons, AD is built into Chaikin\n\nconst { ema: ChaikinIndicator_EMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: ChaikinIndicator_correctFloat, extend: ChaikinIndicator_extend, merge: ChaikinIndicator_merge, error: ChaikinIndicator_error } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Chaikin series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.chaikin\n *\n * @augments Highcharts.Series\n */\nclass ChaikinIndicator extends ChaikinIndicator_EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, period = params.period, \n        // 0- date, 1- Chaikin Oscillator\n        CHA = [], xData = [], yData = [];\n        let oscillator, i;\n        // Check if periods are correct\n        if (periods.length !== 2 || periods[1] <= periods[0]) {\n            ChaikinIndicator_error('Error: \"Chaikin requires two periods. Notice, first ' +\n                'period should be lower than the second one.\"');\n            return;\n        }\n        // Accumulation Distribution Line data\n        const ADL = AD_ADIndicator.prototype.getValues.call(this, series, {\n            volumeSeriesID: params.volumeSeriesID,\n            period: period\n        });\n        // Check if adl is calculated properly, if not skip\n        if (!ADL) {\n            return;\n        }\n        // Shorter Period EMA\n        const SPE = super.getValues.call(this, ADL, {\n            period: periods[0]\n        });\n        // Longer Period EMA\n        const LPE = super.getValues.call(this, ADL, {\n            period: periods[1]\n        });\n        // Check if ema is calculated properly, if not skip\n        if (!SPE || !LPE) {\n            return;\n        }\n        const periodsOffset = periods[1] - periods[0];\n        for (i = 0; i < LPE.yData.length; i++) {\n            oscillator = ChaikinIndicator_correctFloat(SPE.yData[i + periodsOffset] -\n                LPE.yData[i]);\n            CHA.push([LPE.xData[i], oscillator]);\n            xData.push(LPE.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: CHA,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Chaikin Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/chaikin\n *         Chaikin Oscillator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/chaikin\n * @optionparent plotOptions.chaikin\n */\nChaikinIndicator.defaultOptions = ChaikinIndicator_merge(ChaikinIndicator_EMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Chaikin Oscillator\n     * series points.\n     *\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume',\n        /**\n         * Parameter used indirectly for calculating the `AD` indicator.\n         * Decides about the number of data points that are taken\n         * into account for the indicator calculations.\n         */\n        period: 9,\n        /**\n         * Periods for Chaikin Oscillator calculations.\n         *\n         * @type    {Array<number>}\n         * @default [3, 10]\n         */\n        periods: [3, 10]\n    }\n});\nChaikinIndicator_extend(ChaikinIndicator.prototype, {\n    nameBase: 'Chaikin Osc',\n    nameComponents: ['periods']\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('chaikin', ChaikinIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chaikin_ChaikinIndicator = ((/* unused pure expression or super */ null && (ChaikinIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Chaikin Oscillator` series. If the [type](#series.chaikin.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.chaikin\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, stacking, showInNavigator\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/chaikin\n * @apioption series.chaikin\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/CMO/CMOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: CMOIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber, merge: CMOIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmo\n *\n * @augments Highcharts.Series\n */\nclass CMOIndicator extends CMOIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, CMO = [], xData = [], yData = [];\n        let i, index = params.index, values;\n        if (xVal.length < period) {\n            return;\n        }\n        if (isNumber(yVal[0])) {\n            values = yVal;\n        }\n        else {\n            // In case of the situation, where the series type has data length\n            // shorter then 4 (HLC, range), this ensures that we are not trying\n            // to reach the index out of bounds\n            index = Math.min(index, yVal[0].length - 1);\n            values = yVal.map((value) => value[index]);\n        }\n        let firstAddedSum = 0, sumOfHigherValues = 0, sumOfLowerValues = 0, y;\n        // Calculate first point, check if the first value\n        // was added to sum of higher/lower values, and what was the value.\n        for (let j = period; j > 0; j--) {\n            if (values[j] > values[j - 1]) {\n                sumOfHigherValues += values[j] - values[j - 1];\n            }\n            else if (values[j] < values[j - 1]) {\n                sumOfLowerValues += values[j - 1] - values[j];\n            }\n        }\n        // You might divide by 0 if all values are equal,\n        // so return 0 in this case.\n        y =\n            sumOfHigherValues + sumOfLowerValues > 0 ?\n                (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                    (sumOfHigherValues + sumOfLowerValues) :\n                0;\n        xData.push(xVal[period]);\n        yData.push(y);\n        CMO.push([xVal[period], y]);\n        for (i = period + 1; i < yValLen; i++) {\n            firstAddedSum = Math.abs(values[i - period - 1] - values[i - period]);\n            if (values[i] > values[i - 1]) {\n                sumOfHigherValues += values[i] - values[i - 1];\n            }\n            else if (values[i] < values[i - 1]) {\n                sumOfLowerValues += values[i - 1] - values[i];\n            }\n            // Check, to which sum was the first value added to,\n            // and subtract this value from given sum.\n            if (values[i - period] > values[i - period - 1]) {\n                sumOfHigherValues -= firstAddedSum;\n            }\n            else {\n                sumOfLowerValues -= firstAddedSum;\n            }\n            // Same as above.\n            y =\n                sumOfHigherValues + sumOfLowerValues > 0 ?\n                    (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                        (sumOfHigherValues + sumOfLowerValues) :\n                    0;\n            xData.push(xVal[i]);\n            yData.push(y);\n            CMO.push([xVal[i], y]);\n        }\n        return {\n            values: CMO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Chande Momentum Oscilator (CMO) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/cmo\n *         CMO indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cmo\n * @optionparent plotOptions.cmo\n */\nCMOIndicator.defaultOptions = CMOIndicator_merge(CMOIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        period: 20,\n        index: 3\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('cmo', CMOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CMO_CMOIndicator = ((/* unused pure expression or super */ null && (CMOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMO` series. If the [type](#series.cmo.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmo\n * @since 9.1.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmo\n * @apioption series.cmo\n */\n(''); // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/DEMA/DEMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { ema: DEMAIndicator_EMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: DEMAIndicator_correctFloat, isArray: DEMAIndicator_isArray, merge: DEMAIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The DEMA series Type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dema\n *\n * @augments Highcharts.Series\n */\nclass DEMAIndicator extends DEMAIndicator_EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEMA(yVal, prevEMA, SMA, index, i, xVal) {\n        return super.calculateEma(xVal || [], yVal, typeof i === 'undefined' ? 1 : i, this.EMApercent, prevEMA, typeof index === 'undefined' ? -1 : index, SMA);\n    }\n    getValues(series, params) {\n        const period = params.period, EMAvalues = [], doubledPeriod = 2 * period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, DEMA = [], xDataDema = [], yDataDema = [];\n        let accumulatePeriodPoints = 0, EMA = 0, \n        // EMA(EMA)\n        EMAlevel2, \n        // EMA of previous point\n        prevEMA, prevEMAlevel2, \n        // EMA values array\n        i, index = -1, DEMAPoint, SMA = 0;\n        this.EMApercent = (2 / (period + 1));\n        // Check period, if bigger than EMA points length, skip\n        if (yValLen < 2 * period - 1) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (DEMAIndicator_isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        accumulatePeriodPoints =\n            super.accumulatePeriodPoints(period, index, yVal);\n        // First point\n        SMA = accumulatePeriodPoints / period;\n        accumulatePeriodPoints = 0;\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen + 2; i++) {\n            if (i < yValLen + 1) {\n                EMA = this.getEMA(yVal, prevEMA, SMA, index, i)[1];\n                EMAvalues.push(EMA);\n            }\n            prevEMA = EMA;\n            // Summing first period points for EMA(EMA)\n            if (i < doubledPeriod) {\n                accumulatePeriodPoints += EMA;\n            }\n            else {\n                // Calculate DEMA\n                // First DEMA point\n                if (i === doubledPeriod) {\n                    SMA = accumulatePeriodPoints / period;\n                }\n                EMA = EMAvalues[i - period - 1];\n                EMAlevel2 = this.getEMA([EMA], prevEMAlevel2, SMA)[1];\n                DEMAPoint = [\n                    xVal[i - 2],\n                    DEMAIndicator_correctFloat(2 * EMA - EMAlevel2)\n                ];\n                DEMA.push(DEMAPoint);\n                xDataDema.push(DEMAPoint[0]);\n                yDataDema.push(DEMAPoint[1]);\n                prevEMAlevel2 = EMAlevel2;\n            }\n        }\n        return {\n            values: DEMA,\n            xData: xDataDema,\n            yData: yDataDema\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Double exponential moving average (DEMA) indicator. This series requires\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/dema\n *         DEMA indicator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/dema\n * @optionparent plotOptions.dema\n */\nDEMAIndicator.defaultOptions = DEMAIndicator_merge(DEMAIndicator_EMAIndicator.defaultOptions);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('dema', DEMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DEMA_DEMAIndicator = ((/* unused pure expression or super */ null && (DEMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `DEMA` series. If the [type](#series.dema.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dema\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/dema\n * @apioption series.dema\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/TEMA/TEMAIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { ema: TEMAIndicator_EMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: TEMAIndicator_correctFloat, isArray: TEMAIndicator_isArray, merge: TEMAIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The TEMA series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.tema\n *\n * @augments Highcharts.Series\n */\nclass TEMAIndicator extends TEMAIndicator_EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEMA(yVal, prevEMA, SMA, index, i, xVal) {\n        return super.calculateEma(xVal || [], yVal, typeof i === 'undefined' ? 1 : i, this.EMApercent, prevEMA, typeof index === 'undefined' ? -1 : index, SMA);\n    }\n    getTemaPoint(xVal, tripledPeriod, EMAlevels, i) {\n        const TEMAPoint = [\n            xVal[i - 3],\n            TEMAIndicator_correctFloat(3 * EMAlevels.level1 -\n                3 * EMAlevels.level2 + EMAlevels.level3)\n        ];\n        return TEMAPoint;\n    }\n    getValues(series, params) {\n        const period = params.period, doubledPeriod = 2 * period, tripledPeriod = 3 * period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, tema = [], xDataTema = [], yDataTema = [], \n        // EMA values array\n        emaValues = [], emaLevel2Values = [], \n        // This object contains all EMA EMAlevels calculated like below\n        // EMA = level1\n        // EMA(EMA) = level2,\n        // EMA(EMA(EMA)) = level3,\n        emaLevels = {};\n        let index = -1, accumulatePeriodPoints = 0, sma = 0, \n        // EMA of previous point\n        prevEMA, prevEMAlevel2, i, temaPoint;\n        this.EMApercent = (2 / (period + 1));\n        // Check period, if bigger than EMA points length, skip\n        if (yValLen < 3 * period - 2) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (TEMAIndicator_isArray(yVal[0])) {\n            index = params.index ? params.index : 0;\n        }\n        // Accumulate first N-points\n        accumulatePeriodPoints = super.accumulatePeriodPoints(period, index, yVal);\n        // First point\n        sma = accumulatePeriodPoints / period;\n        accumulatePeriodPoints = 0;\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen + 3; i++) {\n            if (i < yValLen + 1) {\n                emaLevels.level1 = this.getEMA(yVal, prevEMA, sma, index, i)[1];\n                emaValues.push(emaLevels.level1);\n            }\n            prevEMA = emaLevels.level1;\n            // Summing first period points for ema(ema)\n            if (i < doubledPeriod) {\n                accumulatePeriodPoints += emaLevels.level1;\n            }\n            else {\n                // Calculate dema\n                // First dema point\n                if (i === doubledPeriod) {\n                    sma = accumulatePeriodPoints / period;\n                    accumulatePeriodPoints = 0;\n                }\n                emaLevels.level1 = emaValues[i - period - 1];\n                emaLevels.level2 = this.getEMA([emaLevels.level1], prevEMAlevel2, sma)[1];\n                emaLevel2Values.push(emaLevels.level2);\n                prevEMAlevel2 = emaLevels.level2;\n                // Summing first period points for ema(ema(ema))\n                if (i < tripledPeriod) {\n                    accumulatePeriodPoints += emaLevels.level2;\n                }\n                else {\n                    // Calculate tema\n                    // First tema point\n                    if (i === tripledPeriod) {\n                        sma = accumulatePeriodPoints / period;\n                    }\n                    if (i === yValLen + 1) {\n                        // Calculate the last ema and emaEMA points\n                        emaLevels.level1 = emaValues[i - period - 1];\n                        emaLevels.level2 = this.getEMA([emaLevels.level1], prevEMAlevel2, sma)[1];\n                        emaLevel2Values.push(emaLevels.level2);\n                    }\n                    emaLevels.level1 = emaValues[i - period - 2];\n                    emaLevels.level2 = emaLevel2Values[i - 2 * period - 1];\n                    emaLevels.level3 = this.getEMA([emaLevels.level2], emaLevels.prevLevel3, sma)[1];\n                    temaPoint = this.getTemaPoint(xVal, tripledPeriod, emaLevels, i);\n                    // Make sure that point exists (for TRIX oscillator)\n                    if (temaPoint) {\n                        tema.push(temaPoint);\n                        xDataTema.push(temaPoint[0]);\n                        yDataTema.push(temaPoint[1]);\n                    }\n                    emaLevels.prevLevel3 = emaLevels.level3;\n                }\n            }\n        }\n        return {\n            values: tema,\n            xData: xDataTema,\n            yData: yDataTema\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Triple exponential moving average (TEMA) indicator. This series requires\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/tema\n *         TEMA indicator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/tema\n * @optionparent plotOptions.tema\n */\nTEMAIndicator.defaultOptions = TEMAIndicator_merge(TEMAIndicator_EMAIndicator.defaultOptions);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('tema', TEMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TEMA_TEMAIndicator = ((/* unused pure expression or super */ null && (TEMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `TEMA` series. If the [type](#series.tema.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.tema\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/tema\n * @apioption series.tema\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/TRIX/TRIXIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { tema: TRIXIndicator_TEMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: TRIXIndicator_correctFloat, merge: TRIXIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The TRIX series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.trix\n *\n * @augments Highcharts.Series\n */\nclass TRIXIndicator extends TRIXIndicator_TEMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    // TRIX is calculated using TEMA so we just extend getTemaPoint method.\n    getTemaPoint(xVal, tripledPeriod, EMAlevels, i) {\n        if (i > tripledPeriod) {\n            return [\n                xVal[i - 3],\n                EMAlevels.prevLevel3 !== 0 ?\n                    TRIXIndicator_correctFloat(EMAlevels.level3 - EMAlevels.prevLevel3) /\n                        EMAlevels.prevLevel3 * 100 : null\n            ];\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Triple exponential average (TRIX) oscillator. This series requires\n * `linkedTo` option to be set.\n *\n * @sample {highstock} stock/indicators/trix\n * TRIX indicator\n *\n * @extends      plotOptions.tema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/tema\n * @requires     stock/indicators/trix\n * @optionparent plotOptions.trix\n */\nTRIXIndicator.defaultOptions = TRIXIndicator_merge(TRIXIndicator_TEMAIndicator.defaultOptions);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('trix', TRIXIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TRIX_TRIXIndicator = ((/* unused pure expression or super */ null && (TRIXIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `TRIX` series. If the [type](#series.trix.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.trix\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/tema\n * @apioption series.trix\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/APO/APOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { ema: APOIndicator_EMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: APOIndicator_extend, merge: APOIndicator_merge, error: APOIndicator_error } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The APO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.apo\n *\n * @augments Highcharts.Series\n */\nclass APOIndicator extends APOIndicator_EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, index = params.index, \n        // 0- date, 1- Absolute price oscillator\n        APO = [], xData = [], yData = [];\n        let oscillator, i;\n        // Check if periods are correct\n        if (periods.length !== 2 || periods[1] <= periods[0]) {\n            APOIndicator_error('Error: \"APO requires two periods. Notice, first period ' +\n                'should be lower than the second one.\"');\n            return;\n        }\n        // Shorter Period EMA\n        const SPE = super.getValues.call(this, series, {\n            index: index,\n            period: periods[0]\n        });\n        // Longer Period EMA\n        const LPE = super.getValues.call(this, series, {\n            index: index,\n            period: periods[1]\n        });\n        // Check if ema is calculated properly, if not skip\n        if (!SPE || !LPE) {\n            return;\n        }\n        const periodsOffset = periods[1] - periods[0];\n        for (i = 0; i < LPE.yData.length; i++) {\n            oscillator = (SPE.yData[i + periodsOffset] -\n                LPE.yData[i]);\n            APO.push([LPE.xData[i], oscillator]);\n            xData.push(LPE.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: APO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Absolute Price Oscillator. This series requires the `linkedTo` option to\n * be set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/apo\n *         Absolute Price Oscillator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/apo\n * @optionparent plotOptions.apo\n */\nAPOIndicator.defaultOptions = APOIndicator_merge(APOIndicator_EMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Absolute Price Oscillator\n     * series points.\n     *\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * Periods for Absolute Price Oscillator calculations.\n         *\n         * @type    {Array<number>}\n         * @default [10, 20]\n         * @since   7.0.0\n         */\n        periods: [10, 20]\n    }\n});\nAPOIndicator_extend(APOIndicator.prototype, {\n    nameBase: 'APO',\n    nameComponents: ['periods']\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('apo', APOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const APO_APOIndicator = ((/* unused pure expression or super */ null && (APOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An `Absolute Price Oscillator` series. If the [type](#series.apo.type) option\n * is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.apo\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/apo\n * @apioption series.apo\n */\n''; // To include the above in the js output\n\n;// external [\"../modules/datagrouping.js\",\"default\",\"dataGrouping\",\"approximations\"]\nconst datagrouping_src_js_default_dataGrouping_approximations_namespaceObject = __WEBPACK_EXTERNAL_MODULE__modules_datagrouping_src_js_b7a4250c__[\"default\"].dataGrouping.approximations;\nvar datagrouping_src_js_default_dataGrouping_approximations_default = /*#__PURE__*/__webpack_require__.n(datagrouping_src_js_default_dataGrouping_approximations_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"Color\"]\nconst external_highcharts_src_js_default_Color_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Color;\nvar external_highcharts_src_js_default_Color_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Color_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/IKH/IKHIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { parse: color } = (external_highcharts_src_js_default_Color_default());\n\nconst { sma: IKHIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { defined: IKHIndicator_defined, extend: IKHIndicator_extend, isArray: IKHIndicator_isArray, isNumber: IKHIndicator_isNumber, getClosestDistance, merge: IKHIndicator_merge, objectEach } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction maxHigh(arr) {\n    return arr.reduce(function (max, res) {\n        return Math.max(max, res[1]);\n    }, -Infinity);\n}\n/**\n * @private\n */\nfunction minLow(arr) {\n    return arr.reduce(function (min, res) {\n        return Math.min(min, res[2]);\n    }, Infinity);\n}\n/**\n * @private\n */\nfunction highlowLevel(arr) {\n    return {\n        high: maxHigh(arr),\n        low: minLow(arr)\n    };\n}\n/**\n * Check two lines intersection (line a1-a2 and b1-b2)\n * Source: https://en.wikipedia.org/wiki/Line%E2%80%93line_intersection\n * @private\n */\nfunction checkLineIntersection(a1, a2, b1, b2) {\n    if (a1 && a2 && b1 && b2) {\n        const saX = a2.plotX - a1.plotX, // Auxiliary section a2-a1 X\n        saY = a2.plotY - a1.plotY, // Auxiliary section a2-a1 Y\n        sbX = b2.plotX - b1.plotX, // Auxiliary section b2-b1 X\n        sbY = b2.plotY - b1.plotY, // Auxiliary section b2-b1 Y\n        sabX = a1.plotX - b1.plotX, // Auxiliary section a1-b1 X\n        sabY = a1.plotY - b1.plotY, // Auxiliary section a1-b1 Y\n        // First degree Bézier parameters\n        u = (-saY * sabX + saX * sabY) / (-sbX * saY + saX * sbY), t = (sbX * sabY - sbY * sabX) / (-sbX * saY + saX * sbY);\n        if (u >= 0 && u <= 1 && t >= 0 && t <= 1) {\n            return {\n                plotX: a1.plotX + t * saX,\n                plotY: a1.plotY + t * saY\n            };\n        }\n    }\n}\n/**\n * Parameter opt (indicator options object) include indicator, points,\n * nextPoints, color, options, gappedExtend and graph properties\n * @private\n */\nfunction drawSenkouSpan(opt) {\n    const indicator = opt.indicator;\n    indicator.points = opt.points;\n    indicator.nextPoints = opt.nextPoints;\n    indicator.color = opt.color;\n    indicator.options = IKHIndicator_merge(opt.options.senkouSpan.styles, opt.gap);\n    indicator.graph = opt.graph;\n    indicator.fillGraph = true;\n    external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.drawGraph.call(indicator);\n}\n/**\n * Data integrity in Ichimoku is different than default 'averages':\n * Point: [undefined, value, value, ...] is correct\n * Point: [undefined, undefined, undefined, ...] is incorrect\n * @private\n */\nfunction ichimokuAverages() {\n    const ret = [];\n    let isEmptyRange;\n    [].forEach.call(arguments, function (arr, i) {\n        ret.push(datagrouping_src_js_default_dataGrouping_approximations_default().average(arr));\n        isEmptyRange = !isEmptyRange && typeof ret[i] === 'undefined';\n    });\n    // Return undefined when first elem. is undefined and let\n    // sum method handle null (#7377)\n    return isEmptyRange ? void 0 : ret;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The IKH series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ikh\n *\n * @augments Highcharts.Series\n */\nclass IKHIndicator extends IKHIndicator_SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.data = [];\n        this.options = {};\n        this.points = [];\n        this.graphCollection = [];\n    }\n    /* *\n     *\n     * Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = IKHIndicator_merge({\n            tenkanLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            kijunLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            chikouLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            senkouSpanA: {\n                styles: {\n                    lineColor: this.color,\n                    fill: color(this.color).setOpacity(0.5).get()\n                }\n            },\n            senkouSpanB: {\n                styles: {\n                    lineColor: this.color,\n                    fill: color(this.color).setOpacity(0.5).get()\n                }\n            },\n            senkouSpan: {\n                styles: {\n                    fill: color(this.color).setOpacity(0.2).get()\n                }\n            }\n        }, this.options);\n    }\n    toYData(point) {\n        return [\n            point.tenkanSen,\n            point.kijunSen,\n            point.chikouSpan,\n            point.senkouSpanA,\n            point.senkouSpanB\n        ];\n    }\n    translate() {\n        const indicator = this;\n        external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.translate.apply(indicator);\n        for (const point of indicator.points) {\n            for (const key of indicator.pointArrayMap) {\n                const pointValue = point[key];\n                if (IKHIndicator_isNumber(pointValue)) {\n                    point['plot' + key] = indicator.yAxis.toPixels(pointValue, true);\n                    // Add extra parameters for support tooltip in moved\n                    // lines\n                    point.plotY = point['plot' + key];\n                    point.tooltipPos = [\n                        point.plotX,\n                        point['plot' + key]\n                    ];\n                    point.isNull = false;\n                }\n            }\n        }\n    }\n    drawGraph() {\n        const indicator = this, mainLinePoints = indicator.points, mainLineOptions = indicator.options, mainLinePath = indicator.graph, mainColor = indicator.color, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, pointArrayMapLength = indicator.pointArrayMap.length, allIchimokuPoints = [\n            [],\n            [],\n            [],\n            [],\n            [],\n            []\n        ], ikhMap = {\n            tenkanLine: allIchimokuPoints[0],\n            kijunLine: allIchimokuPoints[1],\n            chikouLine: allIchimokuPoints[2],\n            senkouSpanA: allIchimokuPoints[3],\n            senkouSpanB: allIchimokuPoints[4],\n            senkouSpan: allIchimokuPoints[5]\n        }, intersectIndexColl = [], senkouSpanOptions = indicator\n            .options.senkouSpan, color = senkouSpanOptions.color ||\n            senkouSpanOptions.styles.fill, negativeColor = senkouSpanOptions.negativeColor, \n        // Points to create color and negativeColor senkouSpan\n        points = [\n            [], // Points color\n            [] // Points negative color\n        ], \n        // For span, we need an access to the next points, used in\n        // getGraphPath()\n        nextPoints = [\n            [], // Next points color\n            [] // Next points negative color\n        ];\n        let pointsLength = mainLinePoints.length, lineIndex = 0, position, point, i, startIntersect, endIntersect, sectionPoints, sectionNextPoints, pointsPlotYSum, nextPointsPlotYSum, senkouSpanTempColor, concatArrIndex, j, k;\n        indicator.ikhMap = ikhMap;\n        // Generate points for all lines and spans lines:\n        while (pointsLength--) {\n            point = mainLinePoints[pointsLength];\n            for (i = 0; i < pointArrayMapLength; i++) {\n                position = indicator.pointArrayMap[i];\n                if (IKHIndicator_defined(point[position])) {\n                    allIchimokuPoints[i].push({\n                        plotX: point.plotX,\n                        plotY: point['plot' + position],\n                        isNull: false\n                    });\n                }\n            }\n            if (negativeColor && pointsLength !== mainLinePoints.length - 1) {\n                // Check if lines intersect\n                const index = ikhMap.senkouSpanB.length - 1, intersect = checkLineIntersection(ikhMap.senkouSpanA[index - 1], ikhMap.senkouSpanA[index], ikhMap.senkouSpanB[index - 1], ikhMap.senkouSpanB[index]);\n                if (intersect) {\n                    const intersectPointObj = {\n                        plotX: intersect.plotX,\n                        plotY: intersect.plotY,\n                        isNull: false,\n                        intersectPoint: true\n                    };\n                    // Add intersect point to ichimoku points collection\n                    // Create senkouSpan sections\n                    ikhMap.senkouSpanA.splice(index, 0, intersectPointObj);\n                    ikhMap.senkouSpanB.splice(index, 0, intersectPointObj);\n                    intersectIndexColl.push(index);\n                }\n            }\n        }\n        // Modify options and generate lines:\n        objectEach(ikhMap, (values, lineName) => {\n            if (mainLineOptions[lineName] &&\n                lineName !== 'senkouSpan') {\n                // First line is rendered by default option\n                indicator.points = allIchimokuPoints[lineIndex];\n                indicator.options = IKHIndicator_merge(mainLineOptions[lineName].styles, gappedExtend);\n                indicator.graph = indicator['graph' + lineName];\n                indicator.fillGraph = false;\n                indicator.color = mainColor;\n                external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.drawGraph.call(indicator);\n                // Now save line\n                indicator['graph' + lineName] = indicator.graph;\n            }\n            lineIndex++;\n        });\n        // Generate senkouSpan area:\n        // If graphCollection exist then remove svg\n        // element and indicator property\n        if (indicator.graphCollection) {\n            for (const graphName of indicator.graphCollection) {\n                indicator[graphName].destroy();\n                delete indicator[graphName];\n            }\n        }\n        // Clean graphCollection or initialize it\n        indicator.graphCollection = [];\n        // When user set negativeColor property\n        if (negativeColor && ikhMap.senkouSpanA[0] && ikhMap.senkouSpanB[0]) {\n            // Add first and last point to senkouSpan area sections\n            intersectIndexColl.unshift(0);\n            intersectIndexColl.push(ikhMap.senkouSpanA.length - 1);\n            // Populate points and nextPoints arrays\n            for (j = 0; j < intersectIndexColl.length - 1; j++) {\n                startIntersect = intersectIndexColl[j];\n                endIntersect = intersectIndexColl[j + 1];\n                sectionPoints = ikhMap.senkouSpanB.slice(startIntersect, endIntersect + 1);\n                sectionNextPoints = ikhMap.senkouSpanA.slice(startIntersect, endIntersect + 1);\n                // Add points to color or negativeColor arrays\n                // Check the middle point (if exist)\n                if (Math.floor(sectionPoints.length / 2) >= 1) {\n                    const x = Math.floor(sectionPoints.length / 2);\n                    // When middle points has equal values\n                    // Compare all points plotY value sum\n                    if (sectionPoints[x].plotY === sectionNextPoints[x].plotY) {\n                        pointsPlotYSum = 0;\n                        nextPointsPlotYSum = 0;\n                        for (k = 0; k < sectionPoints.length; k++) {\n                            pointsPlotYSum += sectionPoints[k].plotY;\n                            nextPointsPlotYSum += sectionNextPoints[k].plotY;\n                        }\n                        concatArrIndex =\n                            pointsPlotYSum > nextPointsPlotYSum ? 0 : 1;\n                        points[concatArrIndex] = points[concatArrIndex].concat(sectionPoints);\n                        nextPoints[concatArrIndex] = nextPoints[concatArrIndex].concat(sectionNextPoints);\n                    }\n                    else {\n                        // Compare middle point of the section\n                        concatArrIndex = (sectionPoints[x].plotY > sectionNextPoints[x].plotY) ? 0 : 1;\n                        points[concatArrIndex] = points[concatArrIndex].concat(sectionPoints);\n                        nextPoints[concatArrIndex] = nextPoints[concatArrIndex].concat(sectionNextPoints);\n                    }\n                }\n                else {\n                    // Compare first point of the section\n                    concatArrIndex = (sectionPoints[0].plotY > sectionNextPoints[0].plotY) ? 0 : 1;\n                    points[concatArrIndex] = points[concatArrIndex].concat(sectionPoints);\n                    nextPoints[concatArrIndex] = nextPoints[concatArrIndex].concat(sectionNextPoints);\n                }\n            }\n            // Render color and negativeColor paths\n            ['graphsenkouSpanColor', 'graphsenkouSpanNegativeColor'].forEach(function (areaName, i) {\n                if (points[i].length && nextPoints[i].length) {\n                    senkouSpanTempColor = i === 0 ? color : negativeColor;\n                    drawSenkouSpan({\n                        indicator: indicator,\n                        points: points[i],\n                        nextPoints: nextPoints[i],\n                        color: senkouSpanTempColor,\n                        options: mainLineOptions,\n                        gap: gappedExtend,\n                        graph: indicator[areaName]\n                    });\n                    // Now save line\n                    indicator[areaName] = indicator.graph;\n                    indicator.graphCollection.push(areaName);\n                }\n            });\n        }\n        else {\n            // When user set only senkouSpan style.fill property\n            drawSenkouSpan({\n                indicator: indicator,\n                points: ikhMap.senkouSpanB,\n                nextPoints: ikhMap.senkouSpanA,\n                color: color,\n                options: mainLineOptions,\n                gap: gappedExtend,\n                graph: indicator.graphsenkouSpan\n            });\n            // Now save line\n            indicator.graphsenkouSpan = indicator.graph;\n        }\n        // Clean temporary properties:\n        delete indicator.nextPoints;\n        delete indicator.fillGraph;\n        // Restore options and draw the Tenkan line:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.graph = mainLinePath;\n        indicator.color = mainColor;\n    }\n    getGraphPath(points) {\n        const indicator = this;\n        let path = [], spanA, spanAarr = [];\n        points = points || this.points;\n        // Render Senkou Span\n        if (indicator.fillGraph && indicator.nextPoints) {\n            spanA = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.getGraphPath.call(indicator, \n            // Reverse points, so Senkou Span A will start from the end:\n            indicator.nextPoints);\n            if (spanA && spanA.length) {\n                spanA[0][0] = 'L';\n                path = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.getGraphPath\n                    .call(indicator, points);\n                spanAarr = spanA.slice(0, path.length);\n                for (let i = spanAarr.length - 1; i >= 0; i--) {\n                    path.push(spanAarr[i]);\n                }\n            }\n        }\n        else {\n            path = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.getGraphPath\n                .apply(indicator, arguments);\n        }\n        return path;\n    }\n    getValues(series, params) {\n        const period = params.period, periodTenkan = params.periodTenkan, periodSenkouSpanB = params.periodSenkouSpanB, xVal = series.xData, yVal = series.yData, xAxis = series.xAxis, yValLen = (yVal && yVal.length) || 0, closestPointRange = getClosestDistance(xAxis.series.map((s) => s.getColumn('x'))), IKH = [], xData = [];\n        let date, slicedTSY, slicedKSY, slicedSSBY, pointTS, pointKS, pointSSB, i, TS, KS, CS, SSA, SSB;\n        // Ikh requires close value\n        if (xVal.length <= period ||\n            !IKHIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // Add timestamps at the beginning\n        const dateStart = xVal[0] - period * closestPointRange;\n        for (i = 0; i < period; i++) {\n            xData.push(dateStart + i * closestPointRange);\n        }\n        for (i = 0; i < yValLen; i++) {\n            // Tenkan Sen\n            if (i >= periodTenkan) {\n                slicedTSY = yVal.slice(i - periodTenkan, i);\n                pointTS = highlowLevel(slicedTSY);\n                TS = (pointTS.high + pointTS.low) / 2;\n            }\n            if (i >= period) {\n                slicedKSY = yVal.slice(i - period, i);\n                pointKS = highlowLevel(slicedKSY);\n                KS = (pointKS.high + pointKS.low) / 2;\n                SSA = (TS + KS) / 2;\n            }\n            if (i >= periodSenkouSpanB) {\n                slicedSSBY = yVal.slice(i - periodSenkouSpanB, i);\n                pointSSB = highlowLevel(slicedSSBY);\n                SSB = (pointSSB.high + pointSSB.low) / 2;\n            }\n            CS = yVal[i][3];\n            date = xVal[i];\n            if (typeof IKH[i] === 'undefined') {\n                IKH[i] = [];\n            }\n            if (typeof IKH[i + period - 1] === 'undefined') {\n                IKH[i + period - 1] = [];\n            }\n            IKH[i + period - 1][0] = TS;\n            IKH[i + period - 1][1] = KS;\n            IKH[i + period - 1][2] = void 0;\n            if (typeof IKH[i + 1] === 'undefined') {\n                IKH[i + 1] = [];\n            }\n            IKH[i + 1][2] = CS;\n            if (i <= period) {\n                IKH[i + period - 1][3] = void 0;\n                IKH[i + period - 1][4] = void 0;\n            }\n            if (typeof IKH[i + 2 * period - 2] === 'undefined') {\n                IKH[i + 2 * period - 2] = [];\n            }\n            IKH[i + 2 * period - 2][3] = SSA;\n            IKH[i + 2 * period - 2][4] = SSB;\n            xData.push(date);\n        }\n        // Add timestamps for further points\n        for (i = 1; i <= period; i++) {\n            xData.push(date + i * closestPointRange);\n        }\n        return {\n            values: IKH,\n            xData: xData,\n            yData: IKH\n        };\n    }\n}\n/**\n * Ichimoku Kinko Hyo (IKH). This series requires `linkedTo` option to be\n * set.\n *\n * @sample stock/indicators/ichimoku-kinko-hyo\n *         Ichimoku Kinko Hyo indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/ichimoku-kinko-hyo\n * @optionparent plotOptions.ikh\n */\nIKHIndicator.defaultOptions = IKHIndicator_merge(IKHIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        period: 26,\n        /**\n         * The base period for Tenkan calculations.\n         */\n        periodTenkan: 9,\n        /**\n         * The base period for Senkou Span B calculations\n         */\n        periodSenkouSpanB: 52\n    },\n    marker: {\n        enabled: false\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> <b> {series.name}</b><br/>' +\n            'TENKAN SEN: {point.tenkanSen:.3f}<br/>' +\n            'KIJUN SEN: {point.kijunSen:.3f}<br/>' +\n            'CHIKOU SPAN: {point.chikouSpan:.3f}<br/>' +\n            'SENKOU SPAN A: {point.senkouSpanA:.3f}<br/>' +\n            'SENKOU SPAN B: {point.senkouSpanB:.3f}<br/>'\n    },\n    /**\n     * The styles for Tenkan line\n     */\n    tenkanLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Kijun line\n     */\n    kijunLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Chikou line\n     */\n    chikouLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Senkou Span A line\n     */\n    senkouSpanA: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for Senkou Span B line\n     */\n    senkouSpanB: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for area between Senkou Span A and B.\n     */\n    senkouSpan: {\n        /**\n         * Color of the area between Senkou Span A and B,\n         * when Senkou Span A is above Senkou Span B. Note that if\n         * a `style.fill` is defined, the `color` takes precedence and\n         * the `style.fill` is ignored.\n         *\n         * @see [senkouSpan.styles.fill](#series.ikh.senkouSpan.styles.fill)\n         *\n         * @sample stock/indicators/ichimoku-kinko-hyo\n         *         Ichimoku Kinko Hyo color\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     7.0.0\n         * @apioption plotOptions.ikh.senkouSpan.color\n         */\n        /**\n         * Color of the area between Senkou Span A and B,\n         * when Senkou Span A is under Senkou Span B.\n         *\n         * @sample stock/indicators/ikh-negative-color\n         *         Ichimoku Kinko Hyo negativeColor\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since     7.0.0\n         * @apioption plotOptions.ikh.senkouSpan.negativeColor\n         */\n        styles: {\n            /**\n             * Color of the area between Senkou Span A and B.\n             *\n             * @deprecated\n             * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: 'rgba(255, 0, 0, 0.5)'\n        }\n    },\n    dataGrouping: {\n        approximation: 'ichimoku-averages'\n    }\n});\nIKHIndicator_extend(IKHIndicator.prototype, {\n    pointArrayMap: [\n        'tenkanSen',\n        'kijunSen',\n        'chikouSpan',\n        'senkouSpanA',\n        'senkouSpanB'\n    ],\n    pointValKey: 'tenkanSen',\n    nameComponents: ['periodSenkouSpanB', 'period', 'periodTenkan']\n});\n/* *\n *\n *  Registry\n *\n * */\n(datagrouping_src_js_default_dataGrouping_approximations_default())[\"ichimoku-averages\"] = ichimokuAverages;\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('ikh', IKHIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const IKH_IKHIndicator = ((/* unused pure expression or super */ null && (IKHIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `IKH` series. If the [type](#series.ikh.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ikh\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/ichimoku-kinko-hyo\n * @apioption series.ikh\n */\n(''); // Add doclet above to transpiled file\n\n;// ./code/es-modules/Stock/Indicators/KeltnerChannels/KeltnerChannelsIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: KeltnerChannelsIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: KeltnerChannelsIndicator_correctFloat, extend: KeltnerChannelsIndicator_extend, merge: KeltnerChannelsIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Keltner Channels series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.keltnerchannels\n *\n * @augments Highcharts.Series\n */\nclass KeltnerChannelsIndicator extends KeltnerChannelsIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = KeltnerChannelsIndicator_merge({\n            topLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            bottomLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            }\n        }, this.options);\n    }\n    getValues(series, params) {\n        const period = params.period, periodATR = params.periodATR, multiplierATR = params.multiplierATR, index = params.index, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // Keltner Channels array structure:\n        // 0-date, 1-top line, 2-middle line, 3-bottom line\n        KC = [], seriesEMA = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.ema.prototype.getValues(series, {\n            period: period,\n            index: index\n        }), seriesATR = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.atr.prototype.getValues(series, {\n            period: periodATR\n        }), xData = [], yData = [];\n        // Middle line, top line and bottom lineI\n        let ML, TL, BL, date, pointEMA, pointATR, i;\n        if (yValLen < period) {\n            return;\n        }\n        for (i = period; i <= yValLen; i++) {\n            pointEMA = seriesEMA.values[i - period];\n            pointATR = seriesATR.values[i - periodATR];\n            date = pointEMA[0];\n            TL = KeltnerChannelsIndicator_correctFloat(pointEMA[1] + (multiplierATR * pointATR[1]));\n            BL = KeltnerChannelsIndicator_correctFloat(pointEMA[1] - (multiplierATR * pointATR[1]));\n            ML = pointEMA[1];\n            KC.push([date, TL, ML, BL]);\n            xData.push(date);\n            yData.push([TL, ML, BL]);\n        }\n        return {\n            values: KC,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Keltner Channels. This series requires the `linkedTo` option to be set\n * and should be loaded after the `stock/indicators/indicators.js`,\n * `stock/indicators/atr.js`, and `stock/ema/.js`.\n *\n * @sample {highstock} stock/indicators/keltner-channels\n *         Keltner Channels\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart,showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/keltner-channels\n * @optionparent plotOptions.keltnerchannels\n */\nKeltnerChannelsIndicator.defaultOptions = KeltnerChannelsIndicator_merge(KeltnerChannelsIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Option for fill color between lines in Keltner Channels Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      Background fill between lines.\n     *\n     * @type {Highcharts.Color}\n     * @since 9.3.2\n     * @apioption plotOptions.keltnerchannels.fillColor\n     *\n     */\n    params: {\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         */\n        index: 0,\n        period: 20,\n        /**\n         * The ATR period.\n         */\n        periodATR: 10,\n        /**\n         * The ATR multiplier.\n         */\n        multiplierATR: 2\n    },\n    /**\n     * Bottom line options.\n     *\n     */\n    bottomLine: {\n        /**\n         * Styles for a bottom line.\n         *\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * `plotOptions.keltnerchannels.color`\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * Top line options.\n     *\n     * @extends plotOptions.keltnerchannels.bottomLine\n     */\n    topLine: {\n        styles: {\n            lineWidth: 1,\n            lineColor: void 0\n        }\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b><br/>Upper Channel: {point.top}<br/>EMA({series.options.params.period}): {point.middle}<br/>Lower Channel: {point.bottom}<br/>'\n    },\n    marker: {\n        enabled: false\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    },\n    lineWidth: 1\n});\nKeltnerChannelsIndicator_extend(KeltnerChannelsIndicator.prototype, {\n    nameBase: 'Keltner Channels',\n    areaLinesNames: ['top', 'bottom'],\n    nameComponents: ['period', 'periodATR', 'multiplierATR'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(KeltnerChannelsIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('keltnerchannels', KeltnerChannelsIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const KeltnerChannels_KeltnerChannelsIndicator = ((/* unused pure expression or super */ null && (KeltnerChannelsIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Keltner Channels indicator. If the [type](#series.keltnerchannels.type)\n * option is not specified, it is inherited from[chart.type](#chart.type).\n *\n * @extends      series,plotOptions.keltnerchannels\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *               joinBy, keys, navigatorOptions, pointInterval,\n *               pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *               stacking, showInNavigator\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/keltner-channels\n * @apioption    series.keltnerchannels\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/Klinger/KlingerIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { ema: KlingerIndicator_EMAIndicator, sma: KlingerIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: KlingerIndicator_correctFloat, error: KlingerIndicator_error, extend: KlingerIndicator_extend, isArray: KlingerIndicator_isArray, merge: KlingerIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Klinger oscillator series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.klinger\n *\n * @augments Highcharts.Series\n */\nclass KlingerIndicator extends KlingerIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    calculateTrend(yVal, i) {\n        const isUpward = yVal[i][1] + yVal[i][2] + yVal[i][3] >\n            yVal[i - 1][1] + yVal[i - 1][2] + yVal[i - 1][3];\n        return isUpward ? 1 : -1;\n    }\n    // Checks if the series and volumeSeries are accessible, number of\n    // points.x is longer than period, is series has OHLC data\n    isValidData(firstYVal) {\n        const chart = this.chart, options = this.options, series = this.linkedParent, isSeriesOHLC = KlingerIndicator_isArray(firstYVal) &&\n            firstYVal.length === 4, volumeSeries = this.volumeSeries ||\n            (this.volumeSeries =\n                chart.get(options.params.volumeSeriesID));\n        if (!volumeSeries) {\n            KlingerIndicator_error('Series ' +\n                options.params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n        }\n        const isLengthValid = [series, volumeSeries].every(function (series) {\n            return series && series.dataTable.rowCount >=\n                options.params.slowAvgPeriod;\n        });\n        return !!(isLengthValid && isSeriesOHLC);\n    }\n    getCM(previousCM, DM, trend, previousTrend, prevoiusDM) {\n        return KlingerIndicator_correctFloat(DM + (trend === previousTrend ? previousCM : prevoiusDM));\n    }\n    getDM(high, low) {\n        return KlingerIndicator_correctFloat(high - low);\n    }\n    getVolumeForce(yVal) {\n        const volumeForce = [];\n        let CM = 0, // Cumulative measurement\n        DM, // Daily measurement\n        force, i = 1, // Start from second point\n        previousCM = 0, previousDM = yVal[0][1] - yVal[0][2], // Initial DM\n        previousTrend = 0, trend;\n        for (i; i < yVal.length; i++) {\n            trend = this.calculateTrend(yVal, i);\n            DM = this.getDM(yVal[i][1], yVal[i][2]);\n            // For the first iteration when the previousTrend doesn't exist,\n            // previousCM doesn't exist either, but it doesn't matter becouse\n            // it's filltered out in the getCM method in else statement,\n            // (in this iteration, previousCM can be raplaced with the DM).\n            CM = this.getCM(previousCM, DM, trend, previousTrend, previousDM);\n            force = this.volumeSeries.getColumn('y')[i] *\n                trend * Math.abs(2 * ((DM / CM) - 1)) * 100;\n            volumeForce.push([force]);\n            // Before next iteration, assign the current as the previous.\n            previousTrend = trend;\n            previousCM = CM;\n            previousDM = DM;\n        }\n        return volumeForce;\n    }\n    getEMA(yVal, prevEMA, SMA, EMApercent, index, i, xVal) {\n        return KlingerIndicator_EMAIndicator.prototype.calculateEma(xVal || [], yVal, typeof i === 'undefined' ? 1 : i, EMApercent, prevEMA, typeof index === 'undefined' ? -1 : index, SMA);\n    }\n    getSMA(period, index, values) {\n        return KlingerIndicator_EMAIndicator.prototype\n            .accumulatePeriodPoints(period, index, values) / period;\n    }\n    getValues(series, params) {\n        const Klinger = [], xVal = series.xData, yVal = series.yData, xData = [], yData = [], calcSingal = [];\n        let KO, i = 0, fastEMA = 0, slowEMA, previousFastEMA = void 0, previousSlowEMA = void 0, signal = null;\n        // If the necessary conditions are not fulfilled, don't proceed.\n        if (!this.isValidData(yVal[0])) {\n            return;\n        }\n        // Calculate the Volume Force array.\n        const volumeForce = this.getVolumeForce(yVal);\n        // Calculate SMA for the first points.\n        const SMAFast = this.getSMA(params.fastAvgPeriod, 0, volumeForce), SMASlow = this.getSMA(params.slowAvgPeriod, 0, volumeForce);\n        // Calculate EMApercent for the first points.\n        const fastEMApercent = 2 / (params.fastAvgPeriod + 1), slowEMApercent = 2 / (params.slowAvgPeriod + 1);\n        // Calculate KO\n        for (i; i < yVal.length; i++) {\n            // Get EMA for fast period.\n            if (i >= params.fastAvgPeriod) {\n                fastEMA = this.getEMA(volumeForce, previousFastEMA, SMAFast, fastEMApercent, 0, i, xVal)[1];\n                previousFastEMA = fastEMA;\n            }\n            // Get EMA for slow period.\n            if (i >= params.slowAvgPeriod) {\n                slowEMA = this.getEMA(volumeForce, previousSlowEMA, SMASlow, slowEMApercent, 0, i, xVal)[1];\n                previousSlowEMA = slowEMA;\n                KO = KlingerIndicator_correctFloat(fastEMA - slowEMA);\n                calcSingal.push(KO);\n                // Calculate signal SMA\n                if (calcSingal.length >= params.signalPeriod) {\n                    signal = calcSingal.slice(-params.signalPeriod)\n                        .reduce((prev, curr) => prev + curr) / params.signalPeriod;\n                }\n                Klinger.push([xVal[i], KO, signal]);\n                xData.push(xVal[i]);\n                yData.push([KO, signal]);\n            }\n        }\n        return {\n            values: Klinger,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Klinger oscillator. This series requires the `linkedTo` option to be set\n * and should be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/klinger\n *         Klinger oscillator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/klinger\n * @optionparent plotOptions.klinger\n */\nKlingerIndicator.defaultOptions = KlingerIndicator_merge(KlingerIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Klinger Oscillator.\n     *\n     * @excluding index, period\n     */\n    params: {\n        /**\n         * The fast period for indicator calculations.\n         */\n        fastAvgPeriod: 34,\n        /**\n         * The slow period for indicator calculations.\n         */\n        slowAvgPeriod: 55,\n        /**\n         * The base period for signal calculations.\n         */\n        signalPeriod: 13,\n        /**\n         * The id of another series to use its data as volume data for the\n         * indiator calculation.\n         */\n        volumeSeriesID: 'volume'\n    },\n    signalLine: {\n        /**\n         * Styles for a signal line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.klinger.color\n             * ](#plotOptions.klinger.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: '#ff0000'\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color: {point.color}\">\\u25CF</span>' +\n            '<b> {series.name}</b><br/>' +\n            '<span style=\"color: {point.color}\">Klinger</span>: ' +\n            '{point.y}<br/>' +\n            '<span style=\"color: ' +\n            '{point.series.options.signalLine.styles.lineColor}\">' +\n            'Signal</span>' +\n            ': {point.signal}<br/>'\n    }\n});\nKlingerIndicator_extend(KlingerIndicator.prototype, {\n    areaLinesNames: [],\n    linesApiNames: ['signalLine'],\n    nameBase: 'Klinger',\n    nameComponents: ['fastAvgPeriod', 'slowAvgPeriod'],\n    pointArrayMap: ['y', 'signal'],\n    parallelArrays: ['x', 'y', 'signal'],\n    pointValKey: 'y'\n});\nIndicators_MultipleLinesComposition.compose(KlingerIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('klinger', KlingerIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Klinger_KlingerIndicator = ((/* unused pure expression or super */ null && (KlingerIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Klinger oscillator. If the [type](#series.klinger.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.klinger\n * @since 9.1.0\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/klinger\n * @apioption series.klinger\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/MACD/MACDIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop: MACDIndicator_noop } = (external_highcharts_src_js_default_default());\n\nconst { column: ColumnSeries, sma: MACDIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: MACDIndicator_extend, correctFloat: MACDIndicator_correctFloat, defined: MACDIndicator_defined, merge: MACDIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The MACD series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.macd\n *\n * @augments Highcharts.Series\n */\nclass MACDIndicator extends MACDIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.init.apply(this, arguments);\n        const originalColor = this.color;\n        // Check whether series is initialized. It may be not initialized,\n        // when any of required indicators is missing.\n        if (this.options) {\n            // If the default colour doesn't set, get the next available from\n            // the array and apply it #15608.\n            if (MACDIndicator_defined(this.colorIndex)) {\n                if (this.options.signalLine &&\n                    this.options.signalLine.styles &&\n                    !this.options.signalLine.styles.lineColor) {\n                    this.options.colorIndex = this.colorIndex + 1;\n                    this.getCyclic('color', void 0, this.chart.options.colors);\n                    this.options.signalLine.styles.lineColor =\n                        this.color;\n                }\n                if (this.options.macdLine &&\n                    this.options.macdLine.styles &&\n                    !this.options.macdLine.styles.lineColor) {\n                    this.options.colorIndex = this.colorIndex + 1;\n                    this.getCyclic('color', void 0, this.chart.options.colors);\n                    this.options.macdLine.styles.lineColor =\n                        this.color;\n                }\n            }\n            // Zones have indexes automatically calculated, we need to\n            // translate them to support multiple lines within one indicator\n            this.macdZones = {\n                zones: this.options.macdLine.zones,\n                startIndex: 0\n            };\n            this.signalZones = {\n                zones: this.macdZones.zones.concat(this.options.signalLine.zones),\n                startIndex: this.macdZones.zones.length\n            };\n        }\n        // Reset color and index #15608.\n        this.color = originalColor;\n    }\n    toYData(point) {\n        return [point.y, point.signal, point.MACD];\n    }\n    translate() {\n        const indicator = this, plotNames = ['plotSignal', 'plotMACD'];\n        external_highcharts_src_js_default_default().seriesTypes.column.prototype.translate.apply(indicator);\n        indicator.points.forEach(function (point) {\n            [point.signal, point.MACD].forEach(function (value, i) {\n                if (value !== null) {\n                    point[plotNames[i]] =\n                        indicator.yAxis.toPixels(value, true);\n                }\n            });\n        });\n    }\n    destroy() {\n        // This.graph is null due to removing two times the same SVG element\n        this.graph = null;\n        this.graphmacd = this.graphmacd && this.graphmacd.destroy();\n        this.graphsignal = this.graphsignal && this.graphsignal.destroy();\n        external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.destroy.apply(this, arguments);\n    }\n    drawGraph() {\n        const indicator = this, mainLinePoints = indicator.points, mainLineOptions = indicator.options, histogramZones = indicator.zones, gappedExtend = {\n            options: {\n                gapSize: mainLineOptions.gapSize\n            }\n        }, otherSignals = [[], []];\n        let point, pointsLength = mainLinePoints.length;\n        // Generate points for top and bottom lines:\n        while (pointsLength--) {\n            point = mainLinePoints[pointsLength];\n            if (MACDIndicator_defined(point.plotMACD)) {\n                otherSignals[0].push({\n                    plotX: point.plotX,\n                    plotY: point.plotMACD,\n                    isNull: !MACDIndicator_defined(point.plotMACD)\n                });\n            }\n            if (MACDIndicator_defined(point.plotSignal)) {\n                otherSignals[1].push({\n                    plotX: point.plotX,\n                    plotY: point.plotSignal,\n                    isNull: !MACDIndicator_defined(point.plotMACD)\n                });\n            }\n        }\n        // Modify options and generate smoothing line:\n        ['macd', 'signal'].forEach((lineName, i) => {\n            indicator.points = otherSignals[i];\n            indicator.options = MACDIndicator_merge(mainLineOptions[`${lineName}Line`]?.styles || {}, gappedExtend);\n            indicator.graph = indicator[`graph${lineName}`];\n            // Zones extension:\n            indicator.zones = (indicator[`${lineName}Zones`].zones || []).slice(indicator[`${lineName}Zones`].startIndex || 0);\n            external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.drawGraph.call(indicator);\n            indicator[`graph${lineName}`] = indicator.graph;\n        });\n        // Restore options:\n        indicator.points = mainLinePoints;\n        indicator.options = mainLineOptions;\n        indicator.zones = histogramZones;\n    }\n    applyZones() {\n        // Histogram zones are handled by drawPoints method\n        // Here we need to apply zones for all lines\n        const histogramZones = this.zones;\n        // `signalZones.zones` contains all zones:\n        this.zones = this.signalZones.zones;\n        external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.sma.prototype.applyZones.call(this);\n        // `applyZones` hides only main series.graph, hide macd line manually\n        if (this.graphmacd && this.options.macdLine.zones.length) {\n            this.graphmacd.hide();\n        }\n        this.zones = histogramZones;\n    }\n    getValues(series, params) {\n        const indexToShift = (params.longPeriod - params.shortPeriod), // #14197\n        MACD = [], xMACD = [], yMACD = [];\n        let shortEMA, longEMA, i, j = 0, signalLine = [];\n        if (series.xData.length <\n            params.longPeriod + params.signalPeriod) {\n            return;\n        }\n        // Calculating the short and long EMA used when calculating the MACD\n        shortEMA = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.ema.prototype.getValues(series, {\n            period: params.shortPeriod,\n            index: params.index\n        });\n        longEMA = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.ema.prototype.getValues(series, {\n            period: params.longPeriod,\n            index: params.index\n        });\n        shortEMA = shortEMA.values;\n        longEMA = longEMA.values;\n        // Subtract each Y value from the EMA's and create the new dataset\n        // (MACD)\n        for (i = 0; i <= shortEMA.length; i++) {\n            if (MACDIndicator_defined(longEMA[i]) &&\n                MACDIndicator_defined(longEMA[i][1]) &&\n                MACDIndicator_defined(shortEMA[i + indexToShift]) &&\n                MACDIndicator_defined(shortEMA[i + indexToShift][0])) {\n                MACD.push([\n                    shortEMA[i + indexToShift][0],\n                    0,\n                    null,\n                    shortEMA[i + indexToShift][1] -\n                        longEMA[i][1]\n                ]);\n            }\n        }\n        // Set the Y and X data of the MACD. This is used in calculating the\n        // signal line.\n        for (i = 0; i < MACD.length; i++) {\n            xMACD.push(MACD[i][0]);\n            yMACD.push([0, null, MACD[i][3]]);\n        }\n        // Setting the signalline (Signal Line: X-day EMA of MACD line).\n        signalLine = external_highcharts_src_js_default_SeriesRegistry_default().seriesTypes.ema.prototype.getValues({\n            xData: xMACD,\n            yData: yMACD\n        }, {\n            period: params.signalPeriod,\n            index: 2\n        });\n        signalLine = signalLine.values;\n        // Setting the MACD Histogram. In comparison to the loop with pure\n        // MACD this loop uses MACD x value not xData.\n        for (i = 0; i < MACD.length; i++) {\n            // Detect the first point\n            if (MACD[i][0] >= signalLine[0][0]) {\n                MACD[i][2] = signalLine[j][1];\n                yMACD[i] = [0, signalLine[j][1], MACD[i][3]];\n                if (MACD[i][3] === null) {\n                    MACD[i][1] = 0;\n                    yMACD[i][0] = 0;\n                }\n                else {\n                    MACD[i][1] = MACDIndicator_correctFloat(MACD[i][3] -\n                        signalLine[j][1]);\n                    yMACD[i][0] = MACDIndicator_correctFloat(MACD[i][3] -\n                        signalLine[j][1]);\n                }\n                j++;\n            }\n        }\n        return {\n            values: MACD,\n            xData: xMACD,\n            yData: yMACD\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Moving Average Convergence Divergence (MACD). This series requires\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample stock/indicators/macd\n *         MACD indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/macd\n * @optionparent plotOptions.macd\n */\nMACDIndicator.defaultOptions = MACDIndicator_merge(MACDIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * The short period for indicator calculations.\n         */\n        shortPeriod: 12,\n        /**\n         * The long period for indicator calculations.\n         */\n        longPeriod: 26,\n        /**\n         * The base period for signal calculations.\n         */\n        signalPeriod: 9,\n        period: 26\n    },\n    /**\n     * The styles for signal line\n     */\n    signalLine: {\n        /**\n         * @sample stock/indicators/macd-zones\n         *         Zones in MACD\n         *\n         * @extends plotOptions.macd.zones\n         */\n        zones: [],\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type  {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * The styles for macd line\n     */\n    macdLine: {\n        /**\n         * @sample stock/indicators/macd-zones\n         *         Zones in MACD\n         *\n         * @extends plotOptions.macd.zones\n         */\n        zones: [],\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type  {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * @type {number|null}\n     */\n    threshold: 0,\n    groupPadding: 0.1,\n    pointPadding: 0.1,\n    crisp: false,\n    states: {\n        hover: {\n            halo: {\n                size: 0\n            }\n        }\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> <b> {series.name}</b><br/>' +\n            'Value: {point.MACD}<br/>' +\n            'Signal: {point.signal}<br/>' +\n            'Histogram: {point.y}<br/>'\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    },\n    minPointLength: 0\n});\nMACDIndicator_extend(MACDIndicator.prototype, {\n    nameComponents: ['longPeriod', 'shortPeriod', 'signalPeriod'],\n    // \"y\" value is treated as Histogram data\n    pointArrayMap: ['y', 'signal', 'MACD'],\n    parallelArrays: ['x', 'y', 'signal', 'MACD'],\n    pointValKey: 'y',\n    // Columns support:\n    markerAttribs: MACDIndicator_noop,\n    getColumnMetrics: (external_highcharts_src_js_default_default()).seriesTypes.column.prototype.getColumnMetrics,\n    crispCol: (external_highcharts_src_js_default_default()).seriesTypes.column.prototype.crispCol,\n    drawPoints: (external_highcharts_src_js_default_default()).seriesTypes.column.prototype.drawPoints\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('macd', MACDIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MACD_MACDIndicator = ((/* unused pure expression or super */ null && (MACDIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `MACD` series. If the [type](#series.macd.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.macd\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/macd\n * @apioption series.macd\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/MFI/MFIIndicator.js\n/* *\n *\n *  Money Flow Index indicator for Highcharts Stock\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: MFIIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: MFIIndicator_extend, merge: MFIIndicator_merge, error: MFIIndicator_error, isArray: MFIIndicator_isArray } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction MFIIndicator_sumArray(array) {\n    return array.reduce(function (prev, cur) {\n        return prev + cur;\n    });\n}\n/**\n *\n */\nfunction toFixed(a, n) {\n    return parseFloat(a.toFixed(n));\n}\n/**\n *\n */\nfunction calculateTypicalPrice(point) {\n    return (point[1] + point[2] + point[3]) / 3;\n}\n/**\n *\n */\nfunction calculateRawMoneyFlow(typicalPrice, volume) {\n    return typicalPrice * volume;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The MFI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.mfi\n *\n * @augments Highcharts.Series\n */\nclass MFIIndicator extends MFIIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, decimals = params.decimals, volumeSeries = series.chart.get(params.volumeSeriesID), yValVolume = volumeSeries?.getColumn('y') || [], MFI = [], xData = [], yData = [], positiveMoneyFlow = [], negativeMoneyFlow = [];\n        let newTypicalPrice, oldTypicalPrice, rawMoneyFlow, negativeMoneyFlowSum, positiveMoneyFlowSum, moneyFlowRatio, MFIPoint, i, isUp = false, \n        // MFI starts calculations from the second point\n        // Cause we need to calculate change between two points\n        range = 1;\n        if (!volumeSeries) {\n            MFIIndicator_error('Series ' +\n                params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        // MFI requires high low and close values\n        if ((xVal.length <= period) || !MFIIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4 ||\n            !yValVolume) {\n            return;\n        }\n        // Calculate first typical price\n        newTypicalPrice = calculateTypicalPrice(yVal[range]);\n        // Accumulate first N-points\n        while (range < period + 1) {\n            // Calculate if up or down\n            oldTypicalPrice = newTypicalPrice;\n            newTypicalPrice = calculateTypicalPrice(yVal[range]);\n            isUp = newTypicalPrice >= oldTypicalPrice;\n            // Calculate raw money flow\n            rawMoneyFlow = calculateRawMoneyFlow(newTypicalPrice, yValVolume[range]);\n            // Add to array\n            positiveMoneyFlow.push(isUp ? rawMoneyFlow : 0);\n            negativeMoneyFlow.push(isUp ? 0 : rawMoneyFlow);\n            range++;\n        }\n        for (i = range - 1; i < yValLen; i++) {\n            if (i > range - 1) {\n                // Remove first point from array\n                positiveMoneyFlow.shift();\n                negativeMoneyFlow.shift();\n                // Calculate if up or down\n                oldTypicalPrice = newTypicalPrice;\n                newTypicalPrice = calculateTypicalPrice(yVal[i]);\n                isUp = newTypicalPrice > oldTypicalPrice;\n                // Calculate raw money flow\n                rawMoneyFlow = calculateRawMoneyFlow(newTypicalPrice, yValVolume[i]);\n                // Add to array\n                positiveMoneyFlow.push(isUp ? rawMoneyFlow : 0);\n                negativeMoneyFlow.push(isUp ? 0 : rawMoneyFlow);\n            }\n            // Calculate sum of negative and positive money flow:\n            negativeMoneyFlowSum = MFIIndicator_sumArray(negativeMoneyFlow);\n            positiveMoneyFlowSum = MFIIndicator_sumArray(positiveMoneyFlow);\n            moneyFlowRatio = positiveMoneyFlowSum / negativeMoneyFlowSum;\n            MFIPoint = toFixed(100 - (100 / (1 + moneyFlowRatio)), decimals);\n            MFI.push([xVal[i], MFIPoint]);\n            xData.push(xVal[i]);\n            yData.push(MFIPoint);\n        }\n        return {\n            values: MFI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Money Flow Index. This series requires `linkedTo` option to be set and\n * should be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/mfi\n *         Money Flow Index Indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/mfi\n * @optionparent plotOptions.mfi\n */\nMFIIndicator.defaultOptions = MFIIndicator_merge(MFIIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        /**\n         * The id of volume series which is mandatory.\n         * For example using OHLC data, volumeSeriesID='volume' means\n         * the indicator will be calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume',\n        /**\n         * Number of maximum decimals that are used in MFI calculations.\n         */\n        decimals: 4\n    }\n});\nMFIIndicator_extend(MFIIndicator.prototype, {\n    nameBase: 'Money Flow Index'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('mfi', MFIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MFI_MFIIndicator = ((/* unused pure expression or super */ null && (MFIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `MFI` series. If the [type](#series.mfi.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.mfi\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/mfi\n * @apioption series.mfi\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/Momentum/MomentumIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: MomentumIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: MomentumIndicator_extend, isArray: MomentumIndicator_isArray, merge: MomentumIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction MomentumIndicator_populateAverage(xVal, yVal, i, period, index) {\n    const mmY = yVal[i - 1][index] - yVal[i - period - 1][index], mmX = xVal[i - 1];\n    return [mmX, mmY];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Momentum series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.momentum\n *\n * @augments Highcharts.Series\n */\nclass MomentumIndicator extends MomentumIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, index = params.index, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, MM = [], xData = [], yData = [];\n        let i, MMPoint;\n        if (xVal.length <= period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (!MomentumIndicator_isArray(yVal[0])) {\n            return;\n        }\n        // Calculate value one-by-one for each period in visible data\n        for (i = (period + 1); i < yValLen; i++) {\n            MMPoint = MomentumIndicator_populateAverage(xVal, yVal, i, period, index);\n            MM.push(MMPoint);\n            xData.push(MMPoint[0]);\n            yData.push(MMPoint[1]);\n        }\n        MMPoint = MomentumIndicator_populateAverage(xVal, yVal, i, period, index);\n        MM.push(MMPoint);\n        xData.push(MMPoint[0]);\n        yData.push(MMPoint[1]);\n        return {\n            values: MM,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Momentum. This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/momentum\n *         Momentum indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/momentum\n * @optionparent plotOptions.momentum\n */\nMomentumIndicator.defaultOptions = MomentumIndicator_merge(MomentumIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        index: 3\n    }\n});\nMomentumIndicator_extend(MomentumIndicator.prototype, {\n    nameBase: 'Momentum'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('momentum', MomentumIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Momentum_MomentumIndicator = ((/* unused pure expression or super */ null && (MomentumIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Momentum` series. If the [type](#series.momentum.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.momentum\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/momentum\n * @apioption series.momentum\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/NATR/NATRIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { atr: NATRIndicator_ATRIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge: NATRIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The NATR series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.natr\n *\n * @augments Highcharts.Series\n */\nclass NATRIndicator extends NATRIndicator_ATRIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const atrData = (super.getValues.apply(this, arguments)), atrLength = atrData.values.length, yVal = series.yData;\n        let i = 0, period = params.period - 1;\n        if (!atrData) {\n            return;\n        }\n        for (; i < atrLength; i++) {\n            atrData.yData[i] = (atrData.values[i][1] / yVal[period][3] * 100);\n            atrData.values[i][1] = atrData.yData[i];\n            period++;\n        }\n        return atrData;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Normalized average true range indicator (NATR). This series requires\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js` and `stock/indicators/atr.js`.\n *\n * @sample {highstock} stock/indicators/natr\n *         NATR indicator\n *\n * @extends      plotOptions.atr\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/natr\n * @optionparent plotOptions.natr\n */\nNATRIndicator.defaultOptions = NATRIndicator_merge(NATRIndicator_ATRIndicator.defaultOptions, {\n    tooltip: {\n        valueSuffix: '%'\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('natr', NATRIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const NATR_NATRIndicator = ((/* unused pure expression or super */ null && (NATRIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `NATR` series. If the [type](#series.natr.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.natr\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/atr\n * @requires  stock/indicators/natr\n * @apioption series.natr\n */\n''; // To include the above in the js output'\n\n;// ./code/es-modules/Stock/Indicators/OBV/OBVIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: OBVIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber: OBVIndicator_isNumber, error: OBVIndicator_error, extend: OBVIndicator_extend, merge: OBVIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The OBV series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.obv\n *\n * @augments Highcharts.Series\n */\nclass OBVIndicator extends OBVIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const volumeSeries = series.chart.get(params.volumeSeriesID), xVal = series.xData, yVal = series.yData, OBV = [], xData = [], yData = [], hasOHLC = !OBVIndicator_isNumber(yVal[0]);\n        let OBVPoint = [], i = 1, previousOBV = 0, curentOBV = 0, previousClose = 0, curentClose = 0, volume;\n        // Checks if volume series exists.\n        if (volumeSeries) {\n            volume = volumeSeries.getColumn('y');\n            // Add first point and get close value.\n            OBVPoint = [xVal[0], previousOBV];\n            previousClose = hasOHLC ?\n                yVal[0][3] : yVal[0];\n            OBV.push(OBVPoint);\n            xData.push(xVal[0]);\n            yData.push(OBVPoint[1]);\n            for (i; i < yVal.length; i++) {\n                curentClose = hasOHLC ?\n                    yVal[i][3] : yVal[i];\n                if (curentClose > previousClose) { // Up\n                    curentOBV = previousOBV + volume[i];\n                }\n                else if (curentClose === previousClose) { // Constant\n                    curentOBV = previousOBV;\n                }\n                else { // Down\n                    curentOBV = previousOBV - volume[i];\n                }\n                // Add point.\n                OBVPoint = [xVal[i], curentOBV];\n                // Assign current as previous for next iteration.\n                previousOBV = curentOBV;\n                previousClose = curentClose;\n                OBV.push(OBVPoint);\n                xData.push(xVal[i]);\n                yData.push(OBVPoint[1]);\n            }\n        }\n        else {\n            OBVIndicator_error('Series ' +\n                params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, series.chart);\n            return;\n        }\n        return {\n            values: OBV,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * On-Balance Volume (OBV) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file. Through the `volumeSeriesID`\n * there also should be linked the volume series.\n *\n * @sample stock/indicators/obv\n *         OBV indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/obv\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @optionparent plotOptions.obv\n */\nOBVIndicator.defaultOptions = OBVIndicator_merge(OBVIndicator_SMAIndicator.defaultOptions, {\n    marker: {\n        enabled: false\n    },\n    /**\n     * @excluding index, period\n     */\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0,\n        /**\n         * The id of another series to use its data as volume data for the\n         * indicator calculation.\n         */\n        volumeSeriesID: 'volume'\n    },\n    tooltip: {\n        valueDecimals: 0\n    }\n});\nOBVIndicator_extend(OBVIndicator.prototype, {\n    nameComponents: void 0\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('obv', OBVIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const OBV_OBVIndicator = ((/* unused pure expression or super */ null && (OBVIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `OBV` series. If the [type](#series.obv.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.obv\n * @since 9.1.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/obv\n * @apioption series.obv\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/PivotPoints/PivotPointsPoint.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst SMAPoint = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes.sma.prototype.pointClass;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction destroyExtraLabels(point, functionName) {\n    const props = point.series.pointArrayMap;\n    let prop, i = props.length;\n    (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes.sma.prototype.pointClass.prototype[functionName].call(point);\n    while (i--) {\n        prop = 'dataLabel' + props[i];\n        // S4 dataLabel could be removed by parent method:\n        if (point[prop] && point[prop].element) {\n            point[prop].destroy();\n        }\n        point[prop] = null;\n    }\n}\n/* *\n *\n *  Class\n *\n * */\nclass PivotPointsPoint extends SMAPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    destroyElements() {\n        destroyExtraLabels(this, 'destroyElements');\n    }\n    // This method is called when removing points, e.g. series.update()\n    destroy() {\n        destroyExtraLabels(this, 'destroyElements');\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PivotPoints_PivotPointsPoint = (PivotPointsPoint);\n\n;// ./code/es-modules/Stock/Indicators/PivotPoints/PivotPointsIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: PivotPointsIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge: PivotPointsIndicator_merge, extend: PivotPointsIndicator_extend, defined: PivotPointsIndicator_defined, isArray: PivotPointsIndicator_isArray } = (external_highcharts_src_js_default_default());\n/**\n *\n *  Class\n *\n **/\n/**\n * The Pivot Points series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pivotpoints\n *\n * @augments Highcharts.Series\n */\nclass PivotPointsIndicator extends PivotPointsIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    toYData(point) {\n        return [point.P]; // The rest should not affect extremes\n    }\n    translate() {\n        const indicator = this;\n        super.translate.apply(indicator);\n        indicator.points.forEach(function (point) {\n            indicator.pointArrayMap.forEach(function (value) {\n                if (PivotPointsIndicator_defined(point[value])) {\n                    point['plot' + value] = (indicator.yAxis.toPixels(point[value], true));\n                }\n            });\n        });\n        // Pivot points are rendered as horizontal lines\n        // And last point start not from the next one (as it's the last one)\n        // But from the approximated last position in a given range\n        indicator.plotEndPoint = indicator.xAxis.toPixels(indicator.endPoint, true);\n    }\n    getGraphPath(points) {\n        const indicator = this, allPivotPoints = ([[], [], [], [], [], [], [], [], []]), pointArrayMapLength = indicator.pointArrayMap.length;\n        let endPoint = indicator.plotEndPoint, path = [], position, point, pointsLength = points.length, i;\n        while (pointsLength--) {\n            point = points[pointsLength];\n            for (i = 0; i < pointArrayMapLength; i++) {\n                position = indicator.pointArrayMap[i];\n                if (PivotPointsIndicator_defined(point[position])) {\n                    allPivotPoints[i].push({\n                        // Start left:\n                        plotX: point.plotX,\n                        plotY: point['plot' + position],\n                        isNull: false\n                    }, {\n                        // Go to right:\n                        plotX: endPoint,\n                        plotY: point['plot' + position],\n                        isNull: false\n                    }, {\n                        // And add null points in path to generate breaks:\n                        plotX: endPoint,\n                        plotY: null,\n                        isNull: true\n                    });\n                }\n            }\n            endPoint = point.plotX;\n        }\n        allPivotPoints.forEach((pivotPoints) => {\n            path = path.concat(super.getGraphPath.call(indicator, pivotPoints));\n        });\n        return path;\n    }\n    // TODO: Rewrite this logic to use multiple datalabels\n    drawDataLabels() {\n        const indicator = this, pointMapping = indicator.pointArrayMap;\n        let currentLabel, pointsLength, point, i;\n        if (indicator.options.dataLabels.enabled) {\n            pointsLength = indicator.points.length;\n            // For every Resistance/Support group we need to render labels.\n            // Add one more item, which will just store dataLabels from\n            // previous iteration\n            pointMapping.concat([false]).forEach((position, k) => {\n                i = pointsLength;\n                while (i--) {\n                    point = indicator.points[i];\n                    if (!position) {\n                        // Store S4 dataLabel too:\n                        point['dataLabel' + pointMapping[k - 1]] =\n                            point.dataLabel;\n                    }\n                    else {\n                        point.y = point[position];\n                        point.pivotLine = position;\n                        point.plotY = point['plot' + position];\n                        currentLabel = point['dataLabel' + position];\n                        // Store previous label\n                        if (k) {\n                            point['dataLabel' + pointMapping[k - 1]] = point.dataLabel;\n                        }\n                        if (!point.dataLabels) {\n                            point.dataLabels = [];\n                        }\n                        point.dataLabels[0] = point.dataLabel =\n                            currentLabel =\n                                currentLabel && currentLabel.element ?\n                                    currentLabel :\n                                    null;\n                    }\n                }\n                super.drawDataLabels\n                    .call(indicator);\n            });\n        }\n    }\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, placement = this[params.algorithm + 'Placement'], \n        // 0- from, 1- to, 2- R1, 3- R2, 4- pivot, 5- S1 etc.\n        PP = [], xData = [], yData = [];\n        let endTimestamp, slicedXLen, slicedX, slicedY, lastPP, pivot, avg, i;\n        // Pivot Points requires high, low and close values\n        if (xVal.length < period ||\n            !PivotPointsIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = period + 1; i <= yValLen + period; i += period) {\n            slicedX = xVal.slice(i - period - 1, i);\n            slicedY = yVal.slice(i - period - 1, i);\n            slicedXLen = slicedX.length;\n            endTimestamp = slicedX[slicedXLen - 1];\n            pivot = this.getPivotAndHLC(slicedY);\n            avg = placement(pivot);\n            lastPP = PP.push([endTimestamp]\n                .concat(avg));\n            xData.push(endTimestamp);\n            yData.push(PP[lastPP - 1].slice(1));\n        }\n        // We don't know exact position in ordinal axis\n        // So we use simple logic:\n        // Get first point in last range, calculate visible average range\n        // and multiply by period\n        this.endPoint = slicedX[0] + ((endTimestamp - slicedX[0]) /\n            slicedXLen) * period;\n        return {\n            values: PP,\n            xData: xData,\n            yData: yData\n        };\n    }\n    getPivotAndHLC(values) {\n        const close = values[values.length - 1][3];\n        let high = -Infinity, low = Infinity;\n        values.forEach(function (p) {\n            high = Math.max(high, p[1]);\n            low = Math.min(low, p[2]);\n        });\n        const pivot = (high + low + close) / 3;\n        return [pivot, high, low, close];\n    }\n    standardPlacement(values) {\n        const diff = values[1] - values[2], avg = [\n            null,\n            null,\n            values[0] + diff,\n            values[0] * 2 - values[2],\n            values[0],\n            values[0] * 2 - values[1],\n            values[0] - diff,\n            null,\n            null\n        ];\n        return avg;\n    }\n    camarillaPlacement(values) {\n        const diff = values[1] - values[2], avg = [\n            values[3] + diff * 1.5,\n            values[3] + diff * 1.25,\n            values[3] + diff * 1.1666,\n            values[3] + diff * 1.0833,\n            values[0],\n            values[3] - diff * 1.0833,\n            values[3] - diff * 1.1666,\n            values[3] - diff * 1.25,\n            values[3] - diff * 1.5\n        ];\n        return avg;\n    }\n    fibonacciPlacement(values) {\n        const diff = values[1] - values[2], avg = [\n            null,\n            values[0] + diff,\n            values[0] + diff * 0.618,\n            values[0] + diff * 0.382,\n            values[0],\n            values[0] - diff * 0.382,\n            values[0] - diff * 0.618,\n            values[0] - diff,\n            null\n        ];\n        return avg;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Pivot points indicator. This series requires the `linkedTo` option to be\n * set and should be loaded after `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/pivot-points\n *         Pivot points\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/pivot-points\n * @optionparent plotOptions.pivotpoints\n */\nPivotPointsIndicator.defaultOptions = PivotPointsIndicator_merge(PivotPointsIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        period: 28,\n        /**\n         * Algorithm used to calculate resistance and support lines based\n         * on pivot points. Implemented algorithms: `'standard'`,\n         * `'fibonacci'` and `'camarilla'`\n         */\n        algorithm: 'standard'\n    },\n    marker: {\n        enabled: false\n    },\n    enableMouseTracking: false,\n    dataLabels: {\n        enabled: true,\n        format: '{point.pivotLine}'\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nPivotPointsIndicator_extend(PivotPointsIndicator.prototype, {\n    nameBase: 'Pivot Points',\n    pointArrayMap: ['R4', 'R3', 'R2', 'R1', 'P', 'S1', 'S2', 'S3', 'S4'],\n    pointValKey: 'P',\n    pointClass: PivotPoints_PivotPointsPoint\n});\n/* *\n *\n *  Registry\n *\n * */\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('pivotpoints', PivotPointsIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PivotPoints_PivotPointsIndicator = ((/* unused pure expression or super */ null && (PivotPointsIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A pivot points indicator. If the [type](#series.pivotpoints.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.pivotpoints\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/pivot-points\n * @apioption series.pivotpoints\n */\n''; // To include the above in the js output'\n\n;// ./code/es-modules/Stock/Indicators/PPO/PPOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { ema: PPOIndicator_EMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: PPOIndicator_correctFloat, extend: PPOIndicator_extend, merge: PPOIndicator_merge, error: PPOIndicator_error } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The PPO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.ppo\n *\n * @augments Highcharts.Series\n */\nclass PPOIndicator extends PPOIndicator_EMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, index = params.index, \n        // 0- date, 1- Percentage Price Oscillator\n        PPO = [], xData = [], yData = [];\n        let oscillator, i;\n        // Check if periods are correct\n        if (periods.length !== 2 || periods[1] <= periods[0]) {\n            PPOIndicator_error('Error: \"PPO requires two periods. Notice, first period ' +\n                'should be lower than the second one.\"');\n            return;\n        }\n        // Shorter Period EMA\n        const SPE = super.getValues.call(this, series, {\n            index: index,\n            period: periods[0]\n        });\n        // Longer Period EMA\n        const LPE = super.getValues.call(this, series, {\n            index: index,\n            period: periods[1]\n        });\n        // Check if ema is calculated properly, if not skip\n        if (!SPE || !LPE) {\n            return;\n        }\n        const periodsOffset = periods[1] - periods[0];\n        for (i = 0; i < LPE.yData.length; i++) {\n            oscillator = PPOIndicator_correctFloat((SPE.yData[i + periodsOffset] -\n                LPE.yData[i]) /\n                LPE.yData[i] *\n                100);\n            PPO.push([LPE.xData[i], oscillator]);\n            xData.push(LPE.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: PPO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Percentage Price Oscillator. This series requires the\n * `linkedTo` option to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/ppo\n *         Percentage Price Oscillator\n *\n * @extends      plotOptions.ema\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/ppo\n * @optionparent plotOptions.ppo\n */\nPPOIndicator.defaultOptions = PPOIndicator_merge(PPOIndicator_EMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Percentage Price Oscillator series\n     * points.\n     *\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * Periods for Percentage Price Oscillator calculations.\n         *\n         * @type    {Array<number>}\n         * @default [12, 26]\n         */\n        periods: [12, 26]\n    }\n});\nPPOIndicator_extend(PPOIndicator.prototype, {\n    nameBase: 'PPO',\n    nameComponents: ['periods']\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('ppo', PPOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PPO_PPOIndicator = ((/* unused pure expression or super */ null && (PPOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Percentage Price Oscillator` series. If the [type](#series.ppo.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.ppo\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/ppo\n * @apioption series.ppo\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/ArrayUtilities.js\n/**\n *\n *  (c) 2010-2025 Pawel Fus & Daniel Studencki\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get extremes of array filled by OHLC data.\n *\n * @private\n *\n * @param {Array<Array<number>>} arr\n * Array of OHLC points (arrays).\n *\n * @param {number} minIndex\n * Index of \"low\" value in point array.\n *\n * @param {number} maxIndex\n * Index of \"high\" value in point array.\n *\n * @return {Array<number,number>}\n * Returns array with min and max value.\n */\nfunction getArrayExtremes(arr, minIndex, maxIndex) {\n    return arr.reduce((prev, target) => [\n        Math.min(prev[0], target[minIndex]),\n        Math.max(prev[1], target[maxIndex])\n    ], [Number.MAX_VALUE, -Number.MAX_VALUE]);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst ArrayUtilities = {\n    getArrayExtremes\n};\n/* harmony default export */ const Indicators_ArrayUtilities = (ArrayUtilities);\n\n;// ./code/es-modules/Core/Color/Palettes.js\n/**\n * Series palettes for Highcharts. Series colors are defined in highcharts.css.\n * **Do not edit this file!** This file is generated using the 'gulp palette' task.\n * @private\n */\nconst SeriesPalettes = {\n    /**\n     * Colors for data series and points\n     */\n    colors: [\n        '#2caffe',\n        '#544fc5',\n        '#00e272',\n        '#fe6a35',\n        '#6b8abc',\n        '#d568fb',\n        '#2ee0ca',\n        '#fa4b42',\n        '#feb56a',\n        '#91e8e1'\n    ],\n};\n/* harmony default export */ const Palettes = (SeriesPalettes);\n\n;// ./code/es-modules/Stock/Indicators/PC/PCIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { sma: PCIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge: PCIndicator_merge, extend: PCIndicator_extend } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Price Channel series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pc\n *\n * @augments Highcharts.Series\n */\nclass PCIndicator extends PCIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // 0- date, 1-top line, 2-middle line, 3-bottom line\n        PC = [], \n        // Middle line, top line and bottom line\n        low = 2, high = 1, xData = [], yData = [];\n        let ML, TL, BL, date, slicedY, extremes, i;\n        if (yValLen < period) {\n            return;\n        }\n        for (i = period; i <= yValLen; i++) {\n            date = xVal[i - 1];\n            slicedY = yVal.slice(i - period, i);\n            extremes = Indicators_ArrayUtilities.getArrayExtremes(slicedY, low, high);\n            TL = extremes[1];\n            BL = extremes[0];\n            ML = (TL + BL) / 2;\n            PC.push([date, TL, ML, BL]);\n            xData.push(date);\n            yData.push([TL, ML, BL]);\n        }\n        return {\n            values: PC,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Price channel (PC). This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/price-channel\n *         Price Channel\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/price-channel\n * @optionparent plotOptions.pc\n */\nPCIndicator.defaultOptions = PCIndicator_merge(PCIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Option for fill color between lines in Price channel Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      background fill between lines\n     *\n     * @type {Highcharts.Color}\n     * @apioption plotOptions.pc.fillColor\n     *\n     */\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        period: 20\n    },\n    lineWidth: 1,\n    topLine: {\n        styles: {\n            /**\n             * Color of the top line. If not set, it's inherited from\n             * [plotOptions.pc.color](#plotOptions.pc.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: Palettes.colors[2],\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1\n        }\n    },\n    bottomLine: {\n        styles: {\n            /**\n             * Color of the bottom line. If not set, it's inherited from\n             * [plotOptions.pc.color](#plotOptions.pc.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: Palettes.colors[8],\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nPCIndicator_extend(PCIndicator.prototype, {\n    areaLinesNames: ['top', 'bottom'],\n    nameBase: 'Price Channel',\n    nameComponents: ['period'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(PCIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('pc', PCIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PC_PCIndicator = ((/* unused pure expression or super */ null && (PCIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Price channel indicator. If the [type](#series.pc.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends      series,plotOptions.pc\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *               joinBy, keys, navigatorOptions, pointInterval,\n *               pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *               showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/price-channel\n * @apioption    series.pc\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/PriceEnvelopes/PriceEnvelopesIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: PriceEnvelopesIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: PriceEnvelopesIndicator_extend, isArray: PriceEnvelopesIndicator_isArray, merge: PriceEnvelopesIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Price Envelopes series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.priceenvelopes\n *\n * @augments Highcharts.Series\n */\nclass PriceEnvelopesIndicator extends PriceEnvelopesIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = PriceEnvelopesIndicator_merge({\n            topLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            },\n            bottomLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            }\n        }, this.options);\n    }\n    getValues(series, params) {\n        const period = params.period, topPercent = params.topBand, botPercent = params.bottomBand, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // 0- date, 1-top line, 2-middle line, 3-bottom line\n        PE = [], \n        // Middle line, top line and bottom line\n        xData = [], yData = [];\n        let ML, TL, BL, date, slicedX, slicedY, point, i;\n        // Price envelopes requires close value\n        if (xVal.length < period ||\n            !PriceEnvelopesIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        for (i = period; i <= yValLen; i++) {\n            slicedX = xVal.slice(i - period, i);\n            slicedY = yVal.slice(i - period, i);\n            point = super.getValues({\n                xData: slicedX,\n                yData: slicedY\n            }, params);\n            date = point.xData[0];\n            ML = point.yData[0];\n            TL = ML * (1 + topPercent);\n            BL = ML * (1 - botPercent);\n            PE.push([date, TL, ML, BL]);\n            xData.push(date);\n            yData.push([TL, ML, BL]);\n        }\n        return {\n            values: PE,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Price envelopes indicator based on [SMA](#plotOptions.sma) calculations.\n * This series requires the `linkedTo` option to be set and should be loaded\n * after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/price-envelopes\n *         Price envelopes\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/price-envelopes\n * @optionparent plotOptions.priceenvelopes\n */\nPriceEnvelopesIndicator.defaultOptions = PriceEnvelopesIndicator_merge(PriceEnvelopesIndicator_SMAIndicator.defaultOptions, {\n    marker: {\n        enabled: false\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'\n    },\n    params: {\n        period: 20,\n        /**\n         * Percentage above the moving average that should be displayed.\n         * 0.1 means 110%. Relative to the calculated value.\n         */\n        topBand: 0.1,\n        /**\n         * Percentage below the moving average that should be displayed.\n         * 0.1 means 90%. Relative to the calculated value.\n         */\n        bottomBand: 0.1\n    },\n    /**\n     * Bottom line options.\n     */\n    bottomLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.priceenvelopes.color](\n             * #plotOptions.priceenvelopes.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    /**\n     * Top line options.\n     *\n     * @extends plotOptions.priceenvelopes.bottomLine\n     */\n    topLine: {\n        styles: {\n            lineWidth: 1\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n    /**\n     * Option for fill color between lines in Price Envelopes Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      Background fill between lines.\n     *\n     * @type      {Highcharts.Color}\n     * @since 11.0.0\n     * @apioption plotOptions.priceenvelopes.fillColor\n     *\n     */\n});\nPriceEnvelopesIndicator_extend(PriceEnvelopesIndicator.prototype, {\n    areaLinesNames: ['top', 'bottom'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    nameComponents: ['period', 'topBand', 'bottomBand'],\n    nameBase: 'Price envelopes',\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    parallelArrays: ['x', 'y', 'top', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(PriceEnvelopesIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('priceenvelopes', PriceEnvelopesIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PriceEnvelopes_PriceEnvelopesIndicator = ((/* unused pure expression or super */ null && (PriceEnvelopesIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A price envelopes indicator. If the [type](#series.priceenvelopes.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.priceenvelopes\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/price-envelopes\n * @apioption series.priceenvelopes\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/PSAR/PSARIndicator.js\n/* *\n *\n *  Parabolic SAR indicator for Highcharts Stock\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: PSARIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge: PSARIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction PSARIndicator_toFixed(a, n) {\n    return parseFloat(a.toFixed(n));\n}\n/**\n *\n */\nfunction calculateDirection(previousDirection, low, high, PSAR) {\n    if ((previousDirection === 1 && low > PSAR) ||\n        (previousDirection === -1 && high > PSAR)) {\n        return 1;\n    }\n    return -1;\n}\n/* *\n * Method for calculating acceleration factor\n * dir - direction\n * pDir - previous Direction\n * eP - extreme point\n * pEP - previous extreme point\n * inc - increment for acceleration factor\n * maxAcc - maximum acceleration factor\n * initAcc - initial acceleration factor\n */\n/**\n *\n */\nfunction getAccelerationFactor(dir, pDir, eP, pEP, pAcc, inc, maxAcc, initAcc) {\n    if (dir === pDir) {\n        if (dir === 1 && (eP > pEP)) {\n            return (pAcc === maxAcc) ? maxAcc : PSARIndicator_toFixed(pAcc + inc, 2);\n        }\n        if (dir === -1 && (eP < pEP)) {\n            return (pAcc === maxAcc) ? maxAcc : PSARIndicator_toFixed(pAcc + inc, 2);\n        }\n        return pAcc;\n    }\n    return initAcc;\n}\n/**\n *\n */\nfunction getExtremePoint(high, low, previousDirection, previousExtremePoint) {\n    if (previousDirection === 1) {\n        return (high > previousExtremePoint) ? high : previousExtremePoint;\n    }\n    return (low < previousExtremePoint) ? low : previousExtremePoint;\n}\n/**\n *\n */\nfunction getEPMinusPSAR(EP, PSAR) {\n    return EP - PSAR;\n}\n/**\n *\n */\nfunction getAccelerationFactorMultiply(accelerationFactor, EPMinusSAR) {\n    return accelerationFactor * EPMinusSAR;\n}\n/* *\n * Method for calculating PSAR\n * pdir - previous direction\n * sDir - second previous Direction\n * PSAR - previous PSAR\n * pACCMultiply - previous acceleration factor multiply\n * sLow - second previous low\n * pLow - previous low\n * sHigh - second previous high\n * pHigh - previous high\n * pEP - previous extreme point\n */\n/**\n *\n */\nfunction getPSAR(pdir, sDir, PSAR, pACCMulti, sLow, pLow, pHigh, sHigh, pEP) {\n    if (pdir === sDir) {\n        if (pdir === 1) {\n            return (PSAR + pACCMulti < Math.min(sLow, pLow)) ?\n                PSAR + pACCMulti :\n                Math.min(sLow, pLow);\n        }\n        return (PSAR + pACCMulti > Math.max(sHigh, pHigh)) ?\n            PSAR + pACCMulti :\n            Math.max(sHigh, pHigh);\n    }\n    return pEP;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Parabolic SAR series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.psar\n *\n * @augments Highcharts.Series\n */\nclass PSARIndicator extends PSARIndicator_SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nameComponents = void 0;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const xVal = series.xData, yVal = series.yData, maxAccelerationFactor = params.maxAccelerationFactor, increment = params.increment, \n        // Set initial acc factor (for every new trend!)\n        initialAccelerationFactor = params.initialAccelerationFactor, decimals = params.decimals, index = params.index, PSARArr = [], xData = [], yData = [];\n        let accelerationFactor = params.initialAccelerationFactor, direction, \n        // Extreme point is the lowest low for falling and highest high\n        // for rising psar - and we are starting with falling\n        extremePoint = yVal[0][1], EPMinusPSAR, accelerationFactorMultiply, newDirection, previousDirection = 1, prevLow, prevPrevLow, prevHigh, prevPrevHigh, PSAR = yVal[0][2], newExtremePoint, high, low, ind;\n        if (index >= yVal.length) {\n            return;\n        }\n        for (ind = 0; ind < index; ind++) {\n            extremePoint = Math.max(yVal[ind][1], extremePoint);\n            PSAR = Math.min(yVal[ind][2], PSARIndicator_toFixed(PSAR, decimals));\n        }\n        direction = (yVal[ind][1] > PSAR) ? 1 : -1;\n        EPMinusPSAR = getEPMinusPSAR(extremePoint, PSAR);\n        accelerationFactor = params.initialAccelerationFactor;\n        accelerationFactorMultiply = getAccelerationFactorMultiply(accelerationFactor, EPMinusPSAR);\n        PSARArr.push([xVal[index], PSAR]);\n        xData.push(xVal[index]);\n        yData.push(PSARIndicator_toFixed(PSAR, decimals));\n        for (ind = index + 1; ind < yVal.length; ind++) {\n            prevLow = yVal[ind - 1][2];\n            prevPrevLow = yVal[ind - 2][2];\n            prevHigh = yVal[ind - 1][1];\n            prevPrevHigh = yVal[ind - 2][1];\n            high = yVal[ind][1];\n            low = yVal[ind][2];\n            // Null points break PSAR\n            if (prevPrevLow !== null &&\n                prevPrevHigh !== null &&\n                prevLow !== null &&\n                prevHigh !== null &&\n                high !== null &&\n                low !== null) {\n                PSAR = getPSAR(direction, previousDirection, PSAR, accelerationFactorMultiply, prevPrevLow, prevLow, prevHigh, prevPrevHigh, extremePoint);\n                newExtremePoint = getExtremePoint(high, low, direction, extremePoint);\n                newDirection = calculateDirection(previousDirection, low, high, PSAR);\n                accelerationFactor = getAccelerationFactor(newDirection, direction, newExtremePoint, extremePoint, accelerationFactor, increment, maxAccelerationFactor, initialAccelerationFactor);\n                EPMinusPSAR = getEPMinusPSAR(newExtremePoint, PSAR);\n                accelerationFactorMultiply = getAccelerationFactorMultiply(accelerationFactor, EPMinusPSAR);\n                PSARArr.push([xVal[ind], PSARIndicator_toFixed(PSAR, decimals)]);\n                xData.push(xVal[ind]);\n                yData.push(PSARIndicator_toFixed(PSAR, decimals));\n                previousDirection = direction;\n                direction = newDirection;\n                extremePoint = newExtremePoint;\n            }\n        }\n        return {\n            values: PSARArr,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/**\n * Parabolic SAR. This series requires `linkedTo`\n * option to be set and should be loaded\n * after `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/psar\n *         Parabolic SAR Indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/psar\n * @optionparent plotOptions.psar\n */\nPSARIndicator.defaultOptions = PSARIndicator_merge(PSARIndicator_SMAIndicator.defaultOptions, {\n    lineWidth: 0,\n    marker: {\n        enabled: true\n    },\n    states: {\n        hover: {\n            lineWidthPlus: 0\n        }\n    },\n    /**\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * The initial value for acceleration factor.\n         * Acceleration factor is starting with this value\n         * and increases by specified increment each time\n         * the extreme point makes a new high.\n         * AF can reach a maximum of maxAccelerationFactor,\n         * no matter how long the uptrend extends.\n         */\n        initialAccelerationFactor: 0.02,\n        /**\n         * The Maximum value for acceleration factor.\n         * AF can reach a maximum of maxAccelerationFactor,\n         * no matter how long the uptrend extends.\n         */\n        maxAccelerationFactor: 0.2,\n        /**\n         * Acceleration factor increases by increment each time\n         * the extreme point makes a new high.\n         *\n         * @since 6.0.0\n         */\n        increment: 0.02,\n        /**\n         * Index from which PSAR is starting calculation\n         *\n         * @since 6.0.0\n         */\n        index: 2,\n        /**\n         * Number of maximum decimals that are used in PSAR calculations.\n         *\n         * @since 6.0.0\n         */\n        decimals: 4\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('psar', PSARIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const PSAR_PSARIndicator = ((/* unused pure expression or super */ null && (PSARIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `PSAR` series. If the [type](#series.psar.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.psar\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/psar\n * @apioption series.psar\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/ROC/ROCIndicator.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: ROCIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray: ROCIndicator_isArray, merge: ROCIndicator_merge, extend: ROCIndicator_extend } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction ROCIndicator_populateAverage(xVal, yVal, i, period, index) {\n    /* Calculated as:\n\n       (Closing Price [today] - Closing Price [n days ago]) /\n        Closing Price [n days ago] * 100\n\n       Return y as null when avoiding division by zero */\n    let nDaysAgoY, rocY;\n    if (index < 0) {\n        // Y data given as an array of values\n        nDaysAgoY = yVal[i - period];\n        rocY = nDaysAgoY ?\n            (yVal[i] - nDaysAgoY) / nDaysAgoY * 100 :\n            null;\n    }\n    else {\n        // Y data given as an array of arrays and the index should be used\n        nDaysAgoY = yVal[i - period][index];\n        rocY = nDaysAgoY ?\n            (yVal[i][index] - nDaysAgoY) / nDaysAgoY * 100 :\n            null;\n    }\n    return [xVal[i], rocY];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ROC series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.roc\n *\n * @augments Highcharts.Series\n */\nclass ROCIndicator extends ROCIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, ROC = [], xData = [], yData = [];\n        let i, index = -1, ROCPoint;\n        // Period is used as a number of time periods ago, so we need more\n        // (at least 1 more) data than the period value\n        if (xVal.length <= period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (ROCIndicator_isArray(yVal[0])) {\n            index = params.index;\n        }\n        // I = period <-- skip first N-points\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen; i++) {\n            ROCPoint = ROCIndicator_populateAverage(xVal, yVal, i, period, index);\n            ROC.push(ROCPoint);\n            xData.push(ROCPoint[0]);\n            yData.push(ROCPoint[1]);\n        }\n        return {\n            values: ROC,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Rate of change indicator (ROC). The indicator value for each point\n * is defined as:\n *\n * `(C - Cn) / Cn * 100`\n *\n * where: `C` is the close value of the point of the same x in the\n * linked series and `Cn` is the close value of the point `n` periods\n * ago. `n` is set through [period](#plotOptions.roc.params.period).\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/roc\n *         Rate of change indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/roc\n * @optionparent plotOptions.roc\n */\nROCIndicator.defaultOptions = ROCIndicator_merge(ROCIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        index: 3,\n        period: 9\n    }\n});\nROCIndicator_extend(ROCIndicator.prototype, {\n    nameBase: 'Rate of Change'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('roc', ROCIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ROC_ROCIndicator = ((/* unused pure expression or super */ null && (ROCIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `ROC` series. If the [type](#series.wma.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * Rate of change indicator (ROC). The indicator value for each point\n * is defined as:\n *\n * `(C - Cn) / Cn * 100`\n *\n * where: `C` is the close value of the point of the same x in the\n * linked series and `Cn` is the close value of the point `n` periods\n * ago. `n` is set through [period](#series.roc.params.period).\n *\n * This series requires `linkedTo` option to be set.\n *\n * @extends   series,plotOptions.roc\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/roc\n * @apioption series.roc\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/RSI/RSIIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: RSIIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber: RSIIndicator_isNumber, merge: RSIIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction RSIIndicator_toFixed(a, n) {\n    return parseFloat(a.toFixed(n));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The RSI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.rsi\n *\n * @augments Highcharts.Series\n */\nclass RSIIndicator extends RSIIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, decimals = params.decimals, \n        // RSI starts calculations from the second point\n        // Cause we need to calculate change between two points\n        RSI = [], xData = [], yData = [];\n        let gain = 0, loss = 0, index = params.index, range = 1, RSIPoint, change, avgGain, avgLoss, i, values;\n        if ((xVal.length < period)) {\n            return;\n        }\n        if (RSIIndicator_isNumber(yVal[0])) {\n            values = yVal;\n        }\n        else {\n            // In case of the situation, where the series type has data length\n            // longer then 4 (HLC, range), this ensures that we are not trying\n            // to reach the index out of bounds\n            index = Math.min(index, yVal[0].length - 1);\n            values = yVal\n                .map((value) => value[index]);\n        }\n        // Calculate changes for first N points\n        while (range < period) {\n            change = RSIIndicator_toFixed(values[range] - values[range - 1], decimals);\n            if (change > 0) {\n                gain += change;\n            }\n            else {\n                loss += Math.abs(change);\n            }\n            range++;\n        }\n        // Average for first n-1 points:\n        avgGain = RSIIndicator_toFixed(gain / (period - 1), decimals);\n        avgLoss = RSIIndicator_toFixed(loss / (period - 1), decimals);\n        for (i = range; i < yValLen; i++) {\n            change = RSIIndicator_toFixed(values[i] - values[i - 1], decimals);\n            if (change > 0) {\n                gain = change;\n                loss = 0;\n            }\n            else {\n                gain = 0;\n                loss = Math.abs(change);\n            }\n            // Calculate smoothed averages, RS, RSI values:\n            avgGain = RSIIndicator_toFixed((avgGain * (period - 1) + gain) / period, decimals);\n            avgLoss = RSIIndicator_toFixed((avgLoss * (period - 1) + loss) / period, decimals);\n            // If average-loss is equal zero, then by definition RSI is set\n            // to 100:\n            if (avgLoss === 0) {\n                RSIPoint = 100;\n                // If average-gain is equal zero, then by definition RSI is set\n                // to 0:\n            }\n            else if (avgGain === 0) {\n                RSIPoint = 0;\n            }\n            else {\n                RSIPoint = RSIIndicator_toFixed(100 - (100 / (1 + (avgGain / avgLoss))), decimals);\n            }\n            RSI.push([xVal[i], RSIPoint]);\n            xData.push(xVal[i]);\n            yData.push(RSIPoint);\n        }\n        return {\n            values: RSI,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Relative strength index (RSI) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/rsi\n *         RSI indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/rsi\n * @optionparent plotOptions.rsi\n */\nRSIIndicator.defaultOptions = RSIIndicator_merge(RSIIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        decimals: 4,\n        index: 3\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('rsi', RSIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const RSI_RSIIndicator = ((/* unused pure expression or super */ null && (RSIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `RSI` series. If the [type](#series.rsi.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.rsi\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/rsi\n * @apioption series.rsi\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/Stochastic/StochasticIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { sma: StochasticIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: StochasticIndicator_extend, isArray: StochasticIndicator_isArray, merge: StochasticIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Stochastic series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.stochastic\n *\n * @augments Highcharts.Series\n */\nclass StochasticIndicator extends StochasticIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        // Set default color for lines:\n        this.options = StochasticIndicator_merge({\n            smoothedLine: {\n                styles: {\n                    lineColor: this.color\n                }\n            }\n        }, this.options);\n    }\n    getValues(series, params) {\n        const periodK = params.periods[0], periodD = params.periods[1], xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // 0- date, 1-%K, 2-%D\n        SO = [], xData = [], yData = [], close = 3, low = 2, high = 1;\n        let slicedY, CL, HL, LL, K, D = null, points, extremes, i;\n        // Stochastic requires close value\n        if (yValLen < periodK ||\n            !StochasticIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // If the value of initial points is constant, wait until it changes\n        // to calculate correct Stochastic values\n        let constantValues = true, j = 0;\n        // For a N-period, we start from N-1 point, to calculate Nth point\n        // That is why we later need to comprehend slice() elements list\n        // with (+1)\n        for (i = periodK - 1; i < yValLen; i++) {\n            slicedY = yVal.slice(i - periodK + 1, i + 1);\n            // Calculate %K\n            extremes = Indicators_ArrayUtilities.getArrayExtremes(slicedY, low, high);\n            LL = extremes[0]; // Lowest low in %K periods\n            CL = yVal[i][close] - LL;\n            HL = extremes[1] - LL;\n            K = CL / HL * 100;\n            if (isNaN(K) && constantValues) {\n                j++;\n                continue;\n            }\n            else if (constantValues && !isNaN(K)) {\n                constantValues = false;\n            }\n            const length = xData.push(xVal[i]);\n            // If N-period previous values are constant which results in NaN %K,\n            // we need to use previous %K value if it is a number,\n            // otherwise we should use null\n            if (isNaN(K)) {\n                yData.push([\n                    yData[length - 2] &&\n                        typeof yData[length - 2][0] === 'number' ?\n                        yData[length - 2][0] : null,\n                    null\n                ]);\n            }\n            else {\n                yData.push([K, null]);\n            }\n            // Calculate smoothed %D, which is SMA of %K\n            if (i >= j + (periodK - 1) + (periodD - 1)) {\n                points = super.getValues({\n                    xData: xData.slice(-periodD),\n                    yData: yData.slice(-periodD)\n                }, {\n                    period: periodD\n                });\n                D = points.yData[0];\n            }\n            SO.push([xVal[i], K, D]);\n            yData[length - 1][1] = D;\n        }\n        return {\n            values: SO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Stochastic oscillator. This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/stochastic\n *         Stochastic oscillator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/stochastic\n * @optionparent plotOptions.stochastic\n */\nStochasticIndicator.defaultOptions = StochasticIndicator_merge(StochasticIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index, period\n     */\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0,\n        /**\n         * Periods for Stochastic oscillator: [%K, %D].\n         *\n         * @type    {Array<number,number>}\n         * @default [14, 3]\n         */\n        periods: [14, 3]\n    },\n    marker: {\n        enabled: false\n    },\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span><b> {series.name}</b><br/>%K: {point.y}<br/>%D: {point.smoothed}<br/>'\n    },\n    /**\n     * Smoothed line options.\n     */\n    smoothedLine: {\n        /**\n         * Styles for a smoothed line.\n         */\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line. If not set, it's inherited from\n             * [plotOptions.stochastic.color\n             * ](#plotOptions.stochastic.color).\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: void 0\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nStochasticIndicator_extend(StochasticIndicator.prototype, {\n    areaLinesNames: [],\n    nameComponents: ['periods'],\n    nameBase: 'Stochastic',\n    pointArrayMap: ['y', 'smoothed'],\n    parallelArrays: ['x', 'y', 'smoothed'],\n    pointValKey: 'y',\n    linesApiNames: ['smoothedLine']\n});\nIndicators_MultipleLinesComposition.compose(StochasticIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('stochastic', StochasticIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Stochastic_StochasticIndicator = ((/* unused pure expression or super */ null && (StochasticIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Stochastic indicator. If the [type](#series.stochastic.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.stochastic\n * @since     6.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis,  dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/stochastic\n * @apioption series.stochastic\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/SlowStochastic/SlowStochasticIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SlowStochasticIndicator_SMAIndicator, stochastic: SlowStochasticIndicator_StochasticIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: SlowStochasticIndicator_extend, merge: SlowStochasticIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Slow Stochastic series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.slowstochastic\n *\n * @augments Highcharts.Series\n */\nclass SlowStochasticIndicator extends SlowStochasticIndicator_StochasticIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const periods = params.periods, fastValues = super.getValues.call(this, series, params), slowValues = {\n            values: [],\n            xData: [],\n            yData: []\n        };\n        if (!fastValues) {\n            return;\n        }\n        slowValues.xData = fastValues.xData.slice(periods[1] - 1);\n        const fastYData = fastValues.yData.slice(periods[1] - 1);\n        // Get SMA(%D)\n        const smoothedValues = SlowStochasticIndicator_SMAIndicator.prototype.getValues.call(this, {\n            xData: slowValues.xData,\n            yData: fastYData\n        }, {\n            index: 1,\n            period: periods[2]\n        });\n        if (!smoothedValues) {\n            return;\n        }\n        // Format data\n        for (let i = 0, xDataLen = slowValues.xData.length; i < xDataLen; i++) {\n            slowValues.yData[i] = [\n                fastYData[i][1],\n                smoothedValues.yData[i - periods[2] + 1] || null\n            ];\n            slowValues.values[i] = [\n                slowValues.xData[i],\n                fastYData[i][1],\n                smoothedValues.yData[i - periods[2] + 1] || null\n            ];\n        }\n        return slowValues;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Slow Stochastic oscillator. This series requires the `linkedTo` option\n * to be set and should be loaded after `stock/indicators/indicators.js`\n * and `stock/indicators/stochastic.js` files.\n *\n * @sample stock/indicators/slow-stochastic\n *         Slow Stochastic oscillator\n *\n * @extends      plotOptions.stochastic\n * @since        8.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/stochastic\n * @requires     stock/indicators/slow-stochastic\n * @optionparent plotOptions.slowstochastic\n */\nSlowStochasticIndicator.defaultOptions = SlowStochasticIndicator_merge(SlowStochasticIndicator_StochasticIndicator.defaultOptions, {\n    params: {\n        /**\n         * Periods for Slow Stochastic oscillator: [%K, %D, SMA(%D)].\n         *\n         * @type    {Array<number,number,number>}\n         * @default [14, 3, 3]\n         */\n        periods: [14, 3, 3]\n    }\n});\nSlowStochasticIndicator_extend(SlowStochasticIndicator.prototype, {\n    nameBase: 'Slow Stochastic'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('slowstochastic', SlowStochasticIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const SlowStochastic_SlowStochasticIndicator = ((/* unused pure expression or super */ null && (SlowStochasticIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Slow Stochastic indicator. If the [type](#series.slowstochastic.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.slowstochastic\n * @since     8.0.0\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/stochastic\n * @requires  stock/indicators/slow-stochastic\n * @apioption series.slowstochastic\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/Supertrend/SupertrendIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { atr: SupertrendIndicator_ATRIndicator, sma: SupertrendIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent: SupertrendIndicator_addEvent, correctFloat: SupertrendIndicator_correctFloat, isArray: SupertrendIndicator_isArray, isNumber: SupertrendIndicator_isNumber, extend: SupertrendIndicator_extend, merge: SupertrendIndicator_merge, objectEach: SupertrendIndicator_objectEach } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction createPointObj(mainSeries, index) {\n    return {\n        index,\n        close: mainSeries.getColumn('close')[index],\n        x: mainSeries.getColumn('x')[index]\n    };\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Supertrend series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.supertrend\n *\n * @augments Highcharts.Series\n */\nclass SupertrendIndicator extends SupertrendIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        const indicator = this;\n        super.init.apply(indicator, arguments);\n        // Only after series are linked add some additional logic/properties.\n        const unbinder = SupertrendIndicator_addEvent(this.chart.constructor, 'afterLinkSeries', () => {\n            // Protection for a case where the indicator is being updated,\n            // for a brief moment the indicator is deleted.\n            if (indicator.options) {\n                const options = indicator.options, parentOptions = indicator.linkedParent.options;\n                // Indicator cropThreshold has to be equal linked series one\n                // reduced by period due to points comparison in drawGraph\n                // (#9787)\n                options.cropThreshold = (parentOptions.cropThreshold -\n                    (options.params.period - 1));\n            }\n            unbinder();\n        }, {\n            order: 1\n        });\n    }\n    drawGraph() {\n        const indicator = this, indicOptions = indicator.options, \n        // Series that indicator is linked to\n        mainSeries = indicator.linkedParent, mainXData = mainSeries.getColumn('x'), mainLinePoints = (mainSeries ? mainSeries.points : []), indicPoints = indicator.points, indicPath = indicator.graph, \n        // Points offset between lines\n        tempOffset = mainLinePoints.length - indicPoints.length, offset = tempOffset > 0 ? tempOffset : 0, \n        // @todo: fix when ichi-moku indicator is merged to master.\n        gappedExtend = {\n            options: {\n                gapSize: indicOptions.gapSize\n            }\n        }, \n        // Sorted supertrend points array\n        groupedPoints = {\n            top: [], // Rising trend line points\n            bottom: [], // Falling trend line points\n            intersect: [] // Change trend line points\n        }, \n        // Options for trend lines\n        supertrendLineOptions = {\n            top: {\n                styles: {\n                    lineWidth: indicOptions.lineWidth,\n                    lineColor: (indicOptions.fallingTrendColor ||\n                        indicOptions.color),\n                    dashStyle: indicOptions.dashStyle\n                }\n            },\n            bottom: {\n                styles: {\n                    lineWidth: indicOptions.lineWidth,\n                    lineColor: (indicOptions.risingTrendColor ||\n                        indicOptions.color),\n                    dashStyle: indicOptions.dashStyle\n                }\n            },\n            intersect: indicOptions.changeTrendLine\n        };\n        let // Supertrend line point\n        point, \n        // Supertrend line next point (has smaller x pos than point)\n        nextPoint, \n        // Main series points\n        mainPoint, nextMainPoint, \n        // Used when supertrend and main points are shifted\n        // relative to each other\n        prevMainPoint, prevPrevMainPoint, \n        // Used when particular point color is set\n        pointColor, \n        // Temporary points that fill groupedPoints array\n        newPoint, newNextPoint, indicPointsLen = indicPoints.length;\n        // Loop which sort supertrend points\n        while (indicPointsLen--) {\n            point = indicPoints[indicPointsLen];\n            nextPoint = indicPoints[indicPointsLen - 1];\n            mainPoint = mainLinePoints[indicPointsLen - 1 + offset];\n            nextMainPoint = mainLinePoints[indicPointsLen - 2 + offset];\n            prevMainPoint = mainLinePoints[indicPointsLen + offset];\n            prevPrevMainPoint = mainLinePoints[indicPointsLen + offset + 1];\n            pointColor = point.options.color;\n            newPoint = {\n                x: point.x,\n                plotX: point.plotX,\n                plotY: point.plotY,\n                isNull: false\n            };\n            // When mainPoint is the last one (left plot area edge)\n            // but supertrend has additional one\n            if (!nextMainPoint &&\n                mainPoint &&\n                SupertrendIndicator_isNumber(mainXData[mainPoint.index - 1])) {\n                nextMainPoint = createPointObj(mainSeries, mainPoint.index - 1);\n            }\n            // When prevMainPoint is the last one (right plot area edge)\n            // but supertrend has additional one (and points are shifted)\n            if (!prevPrevMainPoint &&\n                prevMainPoint &&\n                SupertrendIndicator_isNumber(mainXData[prevMainPoint.index + 1])) {\n                prevPrevMainPoint = createPointObj(mainSeries, prevMainPoint.index + 1);\n            }\n            // When points are shifted (right or left plot area edge)\n            if (!mainPoint &&\n                nextMainPoint &&\n                SupertrendIndicator_isNumber(mainXData[nextMainPoint.index + 1])) {\n                mainPoint = createPointObj(mainSeries, nextMainPoint.index + 1);\n            }\n            else if (!mainPoint &&\n                prevMainPoint &&\n                SupertrendIndicator_isNumber(mainXData[prevMainPoint.index - 1])) {\n                mainPoint = createPointObj(mainSeries, prevMainPoint.index - 1);\n            }\n            // Check if points are shifted relative to each other\n            if (point &&\n                mainPoint &&\n                prevMainPoint &&\n                nextMainPoint &&\n                point.x !== mainPoint.x) {\n                if (point.x === prevMainPoint.x) {\n                    nextMainPoint = mainPoint;\n                    mainPoint = prevMainPoint;\n                }\n                else if (point.x === nextMainPoint.x) {\n                    mainPoint = nextMainPoint;\n                    nextMainPoint = {\n                        close: mainSeries.getColumn('close')[mainPoint.index - 1],\n                        x: mainXData[mainPoint.index - 1]\n                    };\n                }\n                else if (prevPrevMainPoint && point.x === prevPrevMainPoint.x) {\n                    mainPoint = prevPrevMainPoint;\n                    nextMainPoint = prevMainPoint;\n                }\n            }\n            if (nextPoint && nextMainPoint && mainPoint) {\n                newNextPoint = {\n                    x: nextPoint.x,\n                    plotX: nextPoint.plotX,\n                    plotY: nextPoint.plotY,\n                    isNull: false\n                };\n                if (point.y >= mainPoint.close &&\n                    nextPoint.y >= nextMainPoint.close) {\n                    point.color = (pointColor || indicOptions.fallingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.top.push(newPoint);\n                }\n                else if (point.y < mainPoint.close &&\n                    nextPoint.y < nextMainPoint.close) {\n                    point.color = (pointColor || indicOptions.risingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.bottom.push(newPoint);\n                }\n                else {\n                    groupedPoints.intersect.push(newPoint);\n                    groupedPoints.intersect.push(newNextPoint);\n                    // Additional null point to make a gap in line\n                    groupedPoints.intersect.push(SupertrendIndicator_merge(newNextPoint, {\n                        isNull: true\n                    }));\n                    if (point.y >= mainPoint.close &&\n                        nextPoint.y < nextMainPoint.close) {\n                        point.color = (pointColor || indicOptions.fallingTrendColor ||\n                            indicOptions.color);\n                        nextPoint.color = (pointColor || indicOptions.risingTrendColor ||\n                            indicOptions.color);\n                        groupedPoints.top.push(newPoint);\n                        groupedPoints.top.push(SupertrendIndicator_merge(newNextPoint, {\n                            isNull: true\n                        }));\n                    }\n                    else if (point.y < mainPoint.close &&\n                        nextPoint.y >= nextMainPoint.close) {\n                        point.color = (pointColor || indicOptions.risingTrendColor ||\n                            indicOptions.color);\n                        nextPoint.color = (pointColor || indicOptions.fallingTrendColor ||\n                            indicOptions.color);\n                        groupedPoints.bottom.push(newPoint);\n                        groupedPoints.bottom.push(SupertrendIndicator_merge(newNextPoint, {\n                            isNull: true\n                        }));\n                    }\n                }\n            }\n            else if (mainPoint) {\n                if (point.y >= mainPoint.close) {\n                    point.color = (pointColor || indicOptions.fallingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.top.push(newPoint);\n                }\n                else {\n                    point.color = (pointColor || indicOptions.risingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.bottom.push(newPoint);\n                }\n            }\n        }\n        // Generate lines:\n        SupertrendIndicator_objectEach(groupedPoints, function (values, lineName) {\n            indicator.points = values;\n            indicator.options = SupertrendIndicator_merge(supertrendLineOptions[lineName].styles, gappedExtend);\n            indicator.graph = indicator['graph' + lineName + 'Line'];\n            SupertrendIndicator_SMAIndicator.prototype.drawGraph.call(indicator);\n            // Now save line\n            indicator['graph' + lineName + 'Line'] = indicator.graph;\n        });\n        // Restore options:\n        indicator.points = indicPoints;\n        indicator.options = indicOptions;\n        indicator.graph = indicPath;\n    }\n    // Supertrend (Multiplier, Period) Formula:\n    // BASIC UPPERBAND = (HIGH + LOW) / 2 + Multiplier * ATR(Period)\n    // BASIC LOWERBAND = (HIGH + LOW) / 2 - Multiplier * ATR(Period)\n    // FINAL UPPERBAND =\n    //     IF(\n    //      Current BASICUPPERBAND  < Previous FINAL UPPERBAND AND\n    //      Previous Close > Previous FINAL UPPERBAND\n    //     ) THEN (Current BASIC UPPERBAND)\n    //     ELSE (Previous FINALUPPERBAND)\n    // FINAL LOWERBAND =\n    //     IF(\n    //      Current BASIC LOWERBAND  > Previous FINAL LOWERBAND AND\n    //      Previous Close < Previous FINAL LOWERBAND\n    //     ) THEN (Current BASIC LOWERBAND)\n    //     ELSE (Previous FINAL LOWERBAND)\n    // SUPERTREND =\n    //     IF(\n    //      Previous Supertrend == Previous FINAL UPPERBAND AND\n    //      Current Close < Current FINAL UPPERBAND\n    //     ) THAN Current FINAL UPPERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL LOWERBAND AND\n    //      Current Close < Current FINAL LOWERBAND\n    //     ) THAN Current FINAL UPPERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL UPPERBAND AND\n    //      Current Close > Current FINAL UPPERBAND\n    //     ) THAN Current FINAL LOWERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL LOWERBAND AND\n    //      Current Close > Current FINAL LOWERBAND\n    //     ) THAN Current FINAL LOWERBAND\n    getValues(series, params) {\n        const period = params.period, multiplier = params.multiplier, xVal = series.xData, yVal = series.yData, \n        // 0- date, 1- Supertrend indicator\n        st = [], xData = [], yData = [], close = 3, low = 2, high = 1, periodsOffset = (period === 0) ? 0 : period - 1, finalUp = [], finalDown = [];\n        let atrData = [], basicUp, basicDown, supertrend, prevFinalUp, prevFinalDown, prevST, // Previous Supertrend\n        prevY, y, i;\n        if ((xVal.length <= period) || !SupertrendIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4 || period < 0) {\n            return;\n        }\n        atrData = SupertrendIndicator_ATRIndicator.prototype.getValues.call(this, series, {\n            period: period\n        }).yData;\n        for (i = 0; i < atrData.length; i++) {\n            y = yVal[periodsOffset + i];\n            prevY = yVal[periodsOffset + i - 1] || [];\n            prevFinalUp = finalUp[i - 1];\n            prevFinalDown = finalDown[i - 1];\n            prevST = yData[i - 1];\n            if (i === 0) {\n                prevFinalUp = prevFinalDown = prevST = 0;\n            }\n            basicUp = SupertrendIndicator_correctFloat((y[high] + y[low]) / 2 + multiplier * atrData[i]);\n            basicDown = SupertrendIndicator_correctFloat((y[high] + y[low]) / 2 - multiplier * atrData[i]);\n            if ((basicUp < prevFinalUp) ||\n                (prevY[close] > prevFinalUp)) {\n                finalUp[i] = basicUp;\n            }\n            else {\n                finalUp[i] = prevFinalUp;\n            }\n            if ((basicDown > prevFinalDown) ||\n                (prevY[close] < prevFinalDown)) {\n                finalDown[i] = basicDown;\n            }\n            else {\n                finalDown[i] = prevFinalDown;\n            }\n            if (prevST === prevFinalUp && y[close] < finalUp[i] ||\n                prevST === prevFinalDown && y[close] < finalDown[i]) {\n                supertrend = finalUp[i];\n            }\n            else if (prevST === prevFinalUp && y[close] > finalUp[i] ||\n                prevST === prevFinalDown && y[close] > finalDown[i]) {\n                supertrend = finalDown[i];\n            }\n            st.push([xVal[periodsOffset + i], supertrend]);\n            xData.push(xVal[periodsOffset + i]);\n            yData.push(supertrend);\n        }\n        return {\n            values: st,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Supertrend indicator. This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js` and\n * `stock/indicators/sma.js`.\n *\n * @sample {highstock} stock/indicators/supertrend\n *         Supertrend indicator\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, cropThreshold, negativeColor, colorAxis, joinBy,\n *               keys, navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking, threshold\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/supertrend\n * @optionparent plotOptions.supertrend\n */\nSupertrendIndicator.defaultOptions = SupertrendIndicator_merge(SupertrendIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Supertrend indicator series points.\n     *\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        /**\n         * Multiplier for Supertrend Indicator.\n         */\n        multiplier: 3,\n        /**\n         * The base period for indicator Supertrend Indicator calculations.\n         * This is the number of data points which are taken into account\n         * for the indicator calculations.\n         */\n        period: 10\n    },\n    /**\n     * Color of the Supertrend series line that is beneath the main series.\n     *\n     * @sample {highstock} stock/indicators/supertrend/\n     *         Example with risingTrendColor\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    risingTrendColor: \"#06b535\" /* Palette.positiveColor */,\n    /**\n     * Color of the Supertrend series line that is above the main series.\n     *\n     * @sample {highstock} stock/indicators/supertrend/\n     *         Example with fallingTrendColor\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    fallingTrendColor: \"#f21313\" /* Palette.negativeColor */,\n    /**\n     * The styles for the Supertrend line that intersect main series.\n     *\n     * @sample {highstock} stock/indicators/supertrend/\n     *         Example with changeTrendLine\n     */\n    changeTrendLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1,\n            /**\n             * Color of the line.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            lineColor: \"#333333\" /* Palette.neutralColor80 */,\n            /**\n             * The dash or dot style of the grid lines. For possible\n             * values, see\n             * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n             *\n             * @sample {highcharts} highcharts/yaxis/gridlinedashstyle/\n             *         Long dashes\n             * @sample {highstock} stock/xaxis/gridlinedashstyle/\n             *         Long dashes\n             *\n             * @type  {Highcharts.DashStyleValue}\n             * @since 7.0.0\n             */\n            dashStyle: 'LongDash'\n        }\n    }\n});\nSupertrendIndicator_extend(SupertrendIndicator.prototype, {\n    nameBase: 'Supertrend',\n    nameComponents: ['multiplier', 'period']\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('supertrend', SupertrendIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Supertrend_SupertrendIndicator = ((/* unused pure expression or super */ null && (SupertrendIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Supertrend indicator` series. If the [type](#series.supertrend.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.supertrend\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, cropThreshold, data, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, negativeColor, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *            showInNavigator, stacking, threshold\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/supertrend\n * @apioption series.supertrend\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/VBP/VBPPoint.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { sma: { prototype: { pointClass: VBPPoint_SMAPoint } } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n/* *\n *\n *  Class\n *\n * */\nclass VBPPoint extends VBPPoint_SMAPoint {\n    // Required for destroying negative part of volume\n    destroy() {\n        // @todo: this.negativeGraphic doesn't seem to be used anywhere\n        if (this.negativeGraphic) {\n            this.negativeGraphic = this.negativeGraphic.destroy();\n        }\n        super.destroy.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VBP_VBPPoint = (VBPPoint);\n\n;// ./code/es-modules/Stock/Indicators/VBP/VBPIndicator.js\n/* *\n *\n *  (c) 2010-2025 Paweł Dalek\n *\n *  Volume By Price (VBP) indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { animObject } = (external_highcharts_src_js_default_default());\n\nconst { noop: VBPIndicator_noop } = (external_highcharts_src_js_default_default());\n\nconst { column: { prototype: VBPIndicator_columnProto }, sma: VBPIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { addEvent: VBPIndicator_addEvent, arrayMax, arrayMin, correctFloat: VBPIndicator_correctFloat, defined: VBPIndicator_defined, error: VBPIndicator_error, extend: VBPIndicator_extend, isArray: VBPIndicator_isArray, merge: VBPIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst abs = Math.abs;\n/* *\n *\n *  Functions\n *\n * */\n// Utils\n/**\n * @private\n */\nfunction arrayExtremesOHLC(data) {\n    const dataLength = data.length;\n    let min = data[0][3], max = min, i = 1, currentPoint;\n    for (; i < dataLength; i++) {\n        currentPoint = data[i][3];\n        if (currentPoint < min) {\n            min = currentPoint;\n        }\n        if (currentPoint > max) {\n            max = currentPoint;\n        }\n    }\n    return {\n        min: min,\n        max: max\n    };\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Volume By Price (VBP) series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.vbp\n *\n * @augments Highcharts.Series\n */\nclass VBPIndicator extends VBPIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(chart, options) {\n        const indicator = this;\n        // Series.update() sends data that is not necessary as everything is\n        // calculated in getValues(), #17007\n        delete options.data;\n        super.init.apply(indicator, arguments);\n        // Only after series are linked add some additional logic/properties.\n        const unbinder = VBPIndicator_addEvent(this.chart.constructor, 'afterLinkSeries', function () {\n            // Protection for a case where the indicator is being updated,\n            // for a brief moment the indicator is deleted.\n            if (indicator.options) {\n                const params = indicator.options.params, baseSeries = indicator.linkedParent, volumeSeries = chart.get(params.volumeSeriesID);\n                indicator.addCustomEvents(baseSeries, volumeSeries);\n            }\n            unbinder();\n        }, {\n            order: 1\n        });\n        return indicator;\n    }\n    // Adds events related with removing series\n    addCustomEvents(baseSeries, volumeSeries) {\n        const indicator = this, toEmptyIndicator = () => {\n            indicator.chart.redraw();\n            indicator.setData([]);\n            indicator.zoneStarts = [];\n            if (indicator.zoneLinesSVG) {\n                indicator.zoneLinesSVG = indicator.zoneLinesSVG.destroy();\n            }\n        };\n        // If base series is deleted, indicator series data is filled with\n        // an empty array\n        indicator.dataEventsToUnbind.push(VBPIndicator_addEvent(baseSeries, 'remove', function () {\n            toEmptyIndicator();\n        }));\n        // If volume series is deleted, indicator series data is filled with\n        // an empty array\n        if (volumeSeries) {\n            indicator.dataEventsToUnbind.push(VBPIndicator_addEvent(volumeSeries, 'remove', function () {\n                toEmptyIndicator();\n            }));\n        }\n        return indicator;\n    }\n    // Initial animation\n    animate(init) {\n        const series = this, inverted = series.chart.inverted, group = series.group, attr = {};\n        if (!init && group) {\n            const position = inverted ? series.yAxis.top : series.xAxis.left;\n            if (inverted) {\n                group['forceAnimate:translateY'] = true;\n                attr.translateY = position;\n            }\n            else {\n                group['forceAnimate:translateX'] = true;\n                attr.translateX = position;\n            }\n            group.animate(attr, VBPIndicator_extend(animObject(series.options.animation), {\n                step: function (val, fx) {\n                    series.group.attr({\n                        scaleX: Math.max(0.001, fx.pos)\n                    });\n                }\n            }));\n        }\n    }\n    drawPoints() {\n        const indicator = this;\n        if (indicator.options.volumeDivision.enabled) {\n            indicator.posNegVolume(true, true);\n            VBPIndicator_columnProto.drawPoints.apply(indicator, arguments);\n            indicator.posNegVolume(false, false);\n        }\n        VBPIndicator_columnProto.drawPoints.apply(indicator, arguments);\n    }\n    // Function responsible for dividing volume into positive and negative\n    posNegVolume(initVol, pos) {\n        const indicator = this, signOrder = pos ?\n            ['positive', 'negative'] :\n            ['negative', 'positive'], volumeDivision = indicator.options.volumeDivision, pointLength = indicator.points.length;\n        let posWidths = [], negWidths = [], i = 0, pointWidth, priceZone, wholeVol, point;\n        if (initVol) {\n            indicator.posWidths = posWidths;\n            indicator.negWidths = negWidths;\n        }\n        else {\n            posWidths = indicator.posWidths;\n            negWidths = indicator.negWidths;\n        }\n        for (; i < pointLength; i++) {\n            point = indicator.points[i];\n            point[signOrder[0] + 'Graphic'] = point.graphic;\n            point.graphic = point[signOrder[1] + 'Graphic'];\n            if (initVol) {\n                pointWidth = point.shapeArgs.width;\n                priceZone = indicator.priceZones[i];\n                wholeVol = priceZone.wholeVolumeData;\n                if (wholeVol) {\n                    posWidths.push(pointWidth / wholeVol * priceZone.positiveVolumeData);\n                    negWidths.push(pointWidth / wholeVol * priceZone.negativeVolumeData);\n                }\n                else {\n                    posWidths.push(0);\n                    negWidths.push(0);\n                }\n            }\n            point.color = pos ?\n                volumeDivision.styles.positiveColor :\n                volumeDivision.styles.negativeColor;\n            point.shapeArgs.width = pos ?\n                indicator.posWidths[i] :\n                indicator.negWidths[i];\n            point.shapeArgs.x = pos ?\n                point.shapeArgs.x :\n                indicator.posWidths[i];\n        }\n    }\n    translate() {\n        const indicator = this, options = indicator.options, chart = indicator.chart, yAxis = indicator.yAxis, yAxisMin = yAxis.min, zoneLinesOptions = indicator.options.zoneLines, priceZones = (indicator.priceZones);\n        let yBarOffset = 0, volumeDataArray, maxVolume, primalBarWidth, barHeight, barHeightP, oldBarHeight, barWidth, pointPadding, chartPlotTop, barX, barY;\n        VBPIndicator_columnProto.translate.apply(indicator);\n        const indicatorPoints = indicator.points;\n        // Do translate operation when points exist\n        if (indicatorPoints.length) {\n            pointPadding = options.pointPadding < 0.5 ?\n                options.pointPadding :\n                0.1;\n            volumeDataArray = indicator.volumeDataArray;\n            maxVolume = arrayMax(volumeDataArray);\n            primalBarWidth = chart.plotWidth / 2;\n            chartPlotTop = chart.plotTop;\n            barHeight = abs(yAxis.toPixels(yAxisMin) -\n                yAxis.toPixels(yAxisMin + indicator.rangeStep));\n            oldBarHeight = abs(yAxis.toPixels(yAxisMin) -\n                yAxis.toPixels(yAxisMin + indicator.rangeStep));\n            if (pointPadding) {\n                barHeightP = abs(barHeight * (1 - 2 * pointPadding));\n                yBarOffset = abs((barHeight - barHeightP) / 2);\n                barHeight = abs(barHeightP);\n            }\n            indicatorPoints.forEach(function (point, index) {\n                barX = point.barX = point.plotX = 0;\n                barY = point.plotY = (yAxis.toPixels(priceZones[index].start) -\n                    chartPlotTop -\n                    (yAxis.reversed ?\n                        (barHeight - oldBarHeight) :\n                        barHeight) -\n                    yBarOffset);\n                barWidth = VBPIndicator_correctFloat(primalBarWidth *\n                    priceZones[index].wholeVolumeData / maxVolume);\n                point.pointWidth = barWidth;\n                point.shapeArgs = indicator.crispCol.apply(// eslint-disable-line no-useless-call\n                indicator, [barX, barY, barWidth, barHeight]);\n                point.volumeNeg = priceZones[index].negativeVolumeData;\n                point.volumePos = priceZones[index].positiveVolumeData;\n                point.volumeAll = priceZones[index].wholeVolumeData;\n            });\n            if (zoneLinesOptions.enabled) {\n                indicator.drawZones(chart, yAxis, indicator.zoneStarts, zoneLinesOptions.styles);\n            }\n        }\n    }\n    getExtremes() {\n        const prevCompare = this.options.compare, prevCumulative = this.options.cumulative;\n        let ret;\n        // Temporarily disable cumulative and compare while getting the extremes\n        if (this.options.compare) {\n            this.options.compare = void 0;\n            ret = super.getExtremes();\n            this.options.compare = prevCompare;\n        }\n        else if (this.options.cumulative) {\n            this.options.cumulative = false;\n            ret = super.getExtremes();\n            this.options.cumulative = prevCumulative;\n        }\n        else {\n            ret = super.getExtremes();\n        }\n        return ret;\n    }\n    getValues(series, params) {\n        const indicator = this, xValues = series.getColumn('x', true), yValues = series.processedYData, chart = indicator.chart, ranges = params.ranges, VBP = [], xData = [], yData = [], volumeSeries = chart.get(params.volumeSeriesID);\n        // Checks if base series exists\n        if (!series.chart) {\n            VBPIndicator_error('Base series not found! In case it has been removed, add ' +\n                'a new one.', true, chart);\n            return;\n        }\n        // Checks if volume series exists and if it has data\n        if (!volumeSeries ||\n            !volumeSeries.getColumn('x', true).length) {\n            const errorMessage = volumeSeries &&\n                !volumeSeries.getColumn('x', true).length ?\n                ' does not contain any data.' :\n                ' not found! Check `volumeSeriesID`.';\n            VBPIndicator_error('Series ' +\n                params.volumeSeriesID + errorMessage, true, chart);\n            return;\n        }\n        // Checks if series data fits the OHLC format\n        const isOHLC = VBPIndicator_isArray(yValues[0]);\n        if (isOHLC && yValues[0].length !== 4) {\n            VBPIndicator_error('Type of ' +\n                series.name +\n                ' series is different than line, OHLC or candlestick.', true, chart);\n            return;\n        }\n        // Price zones contains all the information about the zones (index,\n        // start, end, volumes, etc.)\n        const priceZones = indicator.priceZones = indicator.specifyZones(isOHLC, xValues, yValues, ranges, volumeSeries);\n        priceZones.forEach(function (zone, index) {\n            VBP.push([zone.x, zone.end]);\n            xData.push(VBP[index][0]);\n            yData.push(VBP[index][1]);\n        });\n        return {\n            values: VBP,\n            xData: xData,\n            yData: yData\n        };\n    }\n    // Specifying where each zone should start ans end\n    specifyZones(isOHLC, xValues, yValues, ranges, volumeSeries) {\n        const indicator = this, rangeExtremes = (isOHLC ? arrayExtremesOHLC(yValues) : false), zoneStarts = indicator.zoneStarts = [], priceZones = [];\n        let lowRange = rangeExtremes ?\n            rangeExtremes.min :\n            arrayMin(yValues), highRange = rangeExtremes ?\n            rangeExtremes.max :\n            arrayMax(yValues), i = 0, j = 1;\n        // If the compare mode is set on the main series, change the VBP\n        // zones to fit new extremes, #16277.\n        const mainSeries = indicator.linkedParent;\n        if (!indicator.options.compareToMain &&\n            mainSeries.dataModify) {\n            lowRange = mainSeries.dataModify.modifyValue(lowRange);\n            highRange = mainSeries.dataModify.modifyValue(highRange);\n        }\n        if (!VBPIndicator_defined(lowRange) || !VBPIndicator_defined(highRange)) {\n            if (this.points.length) {\n                this.setData([]);\n                this.zoneStarts = [];\n                if (this.zoneLinesSVG) {\n                    this.zoneLinesSVG = this.zoneLinesSVG.destroy();\n                }\n            }\n            return [];\n        }\n        const rangeStep = indicator.rangeStep =\n            VBPIndicator_correctFloat(highRange - lowRange) / ranges;\n        zoneStarts.push(lowRange);\n        for (; i < ranges - 1; i++) {\n            zoneStarts.push(VBPIndicator_correctFloat(zoneStarts[i] + rangeStep));\n        }\n        zoneStarts.push(highRange);\n        const zoneStartsLength = zoneStarts.length;\n        //    Creating zones\n        for (; j < zoneStartsLength; j++) {\n            priceZones.push({\n                index: j - 1,\n                x: xValues[0],\n                start: zoneStarts[j - 1],\n                end: zoneStarts[j]\n            });\n        }\n        return indicator.volumePerZone(isOHLC, priceZones, volumeSeries, xValues, yValues);\n    }\n    // Calculating sum of volume values for a specific zone\n    volumePerZone(isOHLC, priceZones, volumeSeries, xValues, yValues) {\n        const indicator = this, volumeXData = volumeSeries.getColumn('x', true), volumeYData = volumeSeries.getColumn('y', true), lastZoneIndex = priceZones.length - 1, baseSeriesLength = yValues.length, volumeSeriesLength = volumeYData.length;\n        let previousValue, startFlag, endFlag, value, i;\n        // Checks if each point has a corresponding volume value\n        if (abs(baseSeriesLength - volumeSeriesLength)) {\n            // If the first point don't have volume, add 0 value at the\n            // beginning of the volume array\n            if (xValues[0] !== volumeXData[0]) {\n                volumeYData.unshift(0);\n            }\n            // If the last point don't have volume, add 0 value at the end\n            // of the volume array\n            if (xValues[baseSeriesLength - 1] !==\n                volumeXData[volumeSeriesLength - 1]) {\n                volumeYData.push(0);\n            }\n        }\n        indicator.volumeDataArray = [];\n        priceZones.forEach(function (zone) {\n            zone.wholeVolumeData = 0;\n            zone.positiveVolumeData = 0;\n            zone.negativeVolumeData = 0;\n            for (i = 0; i < baseSeriesLength; i++) {\n                startFlag = false;\n                endFlag = false;\n                value = isOHLC ? yValues[i][3] : yValues[i];\n                previousValue = i ?\n                    (isOHLC ?\n                        yValues[i - 1][3] :\n                        yValues[i - 1]) :\n                    value;\n                // If the compare mode is set on the main series,\n                // change the VBP zones to fit new extremes, #16277.\n                const mainSeries = indicator.linkedParent;\n                if (!indicator.options.compareToMain &&\n                    mainSeries.dataModify) {\n                    value = mainSeries.dataModify.modifyValue(value);\n                    previousValue = mainSeries.dataModify\n                        .modifyValue(previousValue);\n                }\n                // Checks if this is the point with the\n                // lowest close value and if so, adds it calculations\n                if (value <= zone.start && zone.index === 0) {\n                    startFlag = true;\n                }\n                // Checks if this is the point with the highest\n                // close value and if so, adds it calculations\n                if (value >= zone.end && zone.index === lastZoneIndex) {\n                    endFlag = true;\n                }\n                if ((value > zone.start || startFlag) &&\n                    (value < zone.end || endFlag)) {\n                    zone.wholeVolumeData += volumeYData[i];\n                    if (previousValue > value) {\n                        zone.negativeVolumeData += volumeYData[i];\n                    }\n                    else {\n                        zone.positiveVolumeData += volumeYData[i];\n                    }\n                }\n            }\n            indicator.volumeDataArray.push(zone.wholeVolumeData);\n        });\n        return priceZones;\n    }\n    // Function responsible for drawing additional lines indicating zones\n    drawZones(chart, yAxis, zonesValues, zonesStyles) {\n        const indicator = this, renderer = chart.renderer, leftLinePos = 0, rightLinePos = chart.plotWidth, verticalOffset = chart.plotTop;\n        let zoneLinesSVG = indicator.zoneLinesSVG, zoneLinesPath = [], verticalLinePos;\n        zonesValues.forEach(function (value) {\n            verticalLinePos = yAxis.toPixels(value) - verticalOffset;\n            zoneLinesPath = zoneLinesPath.concat(chart.renderer.crispLine([[\n                    'M',\n                    leftLinePos,\n                    verticalLinePos\n                ], [\n                    'L',\n                    rightLinePos,\n                    verticalLinePos\n                ]], zonesStyles.lineWidth));\n        });\n        // Create zone lines one path or update it while animating\n        if (zoneLinesSVG) {\n            zoneLinesSVG.animate({\n                d: zoneLinesPath\n            });\n        }\n        else {\n            zoneLinesSVG = indicator.zoneLinesSVG =\n                renderer\n                    .path(zoneLinesPath)\n                    .attr({\n                    'stroke-width': zonesStyles.lineWidth,\n                    'stroke': zonesStyles.color,\n                    'dashstyle': zonesStyles.dashStyle,\n                    'zIndex': indicator.group.zIndex + 0.1\n                })\n                    .add(indicator.group);\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Volume By Price indicator.\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/volume-by-price\n *         Volume By Price indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/volume-by-price\n * @optionparent plotOptions.vbp\n */\nVBPIndicator.defaultOptions = VBPIndicator_merge(VBPIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index, period\n     */\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0,\n        /**\n         * The number of price zones.\n         */\n        ranges: 12,\n        /**\n         * The id of volume series which is mandatory. For example using\n         * OHLC data, volumeSeriesID='volume' means the indicator will be\n         * calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume'\n    },\n    /**\n     * The styles for lines which determine price zones.\n     */\n    zoneLines: {\n        /**\n         * Enable/disable zone lines.\n         */\n        enabled: true,\n        /**\n         * Specify the style of zone lines.\n         *\n         * @type    {Highcharts.CSSObject}\n         * @default {\"color\": \"#0A9AC9\", \"dashStyle\": \"LongDash\", \"lineWidth\": 1}\n         */\n        styles: {\n            /** @ignore-option */\n            color: '#0A9AC9',\n            /** @ignore-option */\n            dashStyle: 'LongDash',\n            /** @ignore-option */\n            lineWidth: 1\n        }\n    },\n    /**\n     * The styles for bars when volume is divided into positive/negative.\n     */\n    volumeDivision: {\n        /**\n         * Option to control if volume is divided.\n         */\n        enabled: true,\n        styles: {\n            /**\n             * Color of positive volume bars.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            positiveColor: 'rgba(144, 237, 125, 0.8)',\n            /**\n             * Color of negative volume bars.\n             *\n             * @type {Highcharts.ColorString}\n             */\n            negativeColor: 'rgba(244, 91, 91, 0.8)'\n        }\n    },\n    // To enable series animation; must be animationLimit > pointCount\n    animationLimit: 1000,\n    enableMouseTracking: false,\n    pointPadding: 0,\n    zIndex: -1,\n    crisp: true,\n    dataGrouping: {\n        enabled: false\n    },\n    dataLabels: {\n        align: 'left',\n        allowOverlap: true,\n        enabled: true,\n        format: 'P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}',\n        padding: 0,\n        style: {\n            /** @internal */\n            fontSize: '0.5em'\n        },\n        verticalAlign: 'top'\n    }\n});\nVBPIndicator_extend(VBPIndicator.prototype, {\n    nameBase: 'Volume by Price',\n    nameComponents: ['ranges'],\n    calculateOn: {\n        chart: 'render',\n        xAxis: 'afterSetExtremes'\n    },\n    pointClass: VBP_VBPPoint,\n    markerAttribs: VBPIndicator_noop,\n    drawGraph: VBPIndicator_noop,\n    getColumnMetrics: VBPIndicator_columnProto.getColumnMetrics,\n    crispCol: VBPIndicator_columnProto.crispCol\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('vbp', VBPIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VBP_VBPIndicator = ((/* unused pure expression or super */ null && (VBPIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Volume By Price (VBP)` series. If the [type](#series.vbp.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.vbp\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL, compare, compareBase, compareStart\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/volume-by-price\n * @apioption series.vbp\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/VWAP/VWAPIndicator.js\n/* *\n *\n *  (c) 2010-2025 Paweł Dalek\n *\n *  Volume Weighted Average Price (VWAP) indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: VWAPIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { error: VWAPIndicator_error, isArray: VWAPIndicator_isArray, merge: VWAPIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Volume Weighted Average Price (VWAP) series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.vwap\n *\n * @augments Highcharts.Series\n */\nclass VWAPIndicator extends VWAPIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const indicator = this, chart = series.chart, xValues = series.xData, yValues = series.yData, period = params.period;\n        let isOHLC = true, volumeSeries;\n        // Checks if volume series exists\n        if (!(volumeSeries = (chart.get(params.volumeSeriesID)))) {\n            VWAPIndicator_error('Series ' +\n                params.volumeSeriesID +\n                ' not found! Check `volumeSeriesID`.', true, chart);\n            return;\n        }\n        // Checks if series data fits the OHLC format\n        if (!(VWAPIndicator_isArray(yValues[0]))) {\n            isOHLC = false;\n        }\n        return indicator.calculateVWAPValues(isOHLC, xValues, yValues, volumeSeries, period);\n    }\n    /**\n     * Main algorithm used to calculate Volume Weighted Average Price (VWAP)\n     * values\n     *\n     * @private\n     *\n     * @param {boolean} isOHLC\n     * Says if data has OHLC format\n     *\n     * @param {Array<number>} xValues\n     * Array of timestamps\n     *\n     * @param {Array<number|Array<number,number,number,number>>} yValues\n     * Array of yValues, can be an array of a four arrays (OHLC) or array of\n     * values (line)\n     *\n     * @param {Array<*>} volumeSeries\n     * Volume series\n     *\n     * @param {number} period\n     * Number of points to be calculated\n     *\n     * @return {Object}\n     * Object contains computed VWAP\n     **/\n    calculateVWAPValues(isOHLC, xValues, yValues, volumeSeries, period) {\n        const volumeValues = volumeSeries.getColumn('y'), volumeLength = volumeValues.length, pointsLength = xValues.length, cumulativePrice = [], cumulativeVolume = [], xData = [], yData = [], VWAP = [];\n        let commonLength, typicalPrice, cPrice, cVolume, i, j;\n        if (pointsLength <= volumeLength) {\n            commonLength = pointsLength;\n        }\n        else {\n            commonLength = volumeLength;\n        }\n        for (i = 0, j = 0; i < commonLength; i++) {\n            // Depending on whether series is OHLC or line type, price is\n            // average of the high, low and close or a simple value\n            typicalPrice = isOHLC ?\n                ((yValues[i][1] + yValues[i][2] +\n                    yValues[i][3]) / 3) :\n                yValues[i];\n            typicalPrice *= volumeValues[i];\n            cPrice = j ?\n                (cumulativePrice[i - 1] + typicalPrice) :\n                typicalPrice;\n            cVolume = j ?\n                (cumulativeVolume[i - 1] + volumeValues[i]) :\n                volumeValues[i];\n            cumulativePrice.push(cPrice);\n            cumulativeVolume.push(cVolume);\n            VWAP.push([xValues[i], (cPrice / cVolume)]);\n            xData.push(VWAP[i][0]);\n            yData.push(VWAP[i][1]);\n            j++;\n            if (j === period) {\n                j = 0;\n            }\n        }\n        return {\n            values: VWAP,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Volume Weighted Average Price indicator.\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/vwap\n *         Volume Weighted Average Price indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/vwap\n * @optionparent plotOptions.vwap\n */\nVWAPIndicator.defaultOptions = VWAPIndicator_merge(VWAPIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        period: 30,\n        /**\n         * The id of volume series which is mandatory. For example using\n         * OHLC data, volumeSeriesID='volume' means the indicator will be\n         * calculated using OHLC and volume values.\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('vwap', VWAPIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const VWAP_VWAPIndicator = ((/* unused pure expression or super */ null && (VWAPIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Volume Weighted Average Price (VWAP)` series. If the\n * [type](#series.vwap.type) option is not specified, it is inherited from\n * [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.vwap\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/vwap\n * @apioption series.vwap\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/WilliamsR/WilliamsRIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: WilliamsRIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: WilliamsRIndicator_extend, isArray: WilliamsRIndicator_isArray, merge: WilliamsRIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Williams %R series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.williamsr\n *\n * @augments Highcharts.Series\n */\nclass WilliamsRIndicator extends WilliamsRIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, WR = [], // 0- date, 1- Williams %R\n        xData = [], yData = [], close = 3, low = 2, high = 1;\n        let slicedY, extremes, R, HH, // Highest high value in period\n        LL, // Lowest low value in period\n        CC, // Current close value\n        i;\n        // Williams %R requires close value\n        if (xVal.length < period ||\n            !WilliamsRIndicator_isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // For a N-period, we start from N-1 point, to calculate Nth point\n        // That is why we later need to comprehend slice() elements list\n        // with (+1)\n        for (i = period - 1; i < yValLen; i++) {\n            slicedY = yVal.slice(i - period + 1, i + 1);\n            extremes = Indicators_ArrayUtilities.getArrayExtremes(slicedY, low, high);\n            LL = extremes[0];\n            HH = extremes[1];\n            CC = yVal[i][close];\n            R = ((HH - CC) / (HH - LL)) * -100;\n            if (xVal[i]) {\n                WR.push([xVal[i], R]);\n                xData.push(xVal[i]);\n                yData.push(R);\n            }\n        }\n        return {\n            values: WR,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Williams %R. This series requires the `linkedTo` option to be\n * set and should be loaded after the `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/williams-r\n *         Williams %R\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/williams-r\n * @optionparent plotOptions.williamsr\n */\nWilliamsRIndicator.defaultOptions = WilliamsRIndicator_merge(WilliamsRIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Parameters used in calculation of Williams %R series points.\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unchangeable index, do not inherit (#15362)\n        /**\n         * Period for Williams %R oscillator\n         */\n        period: 14\n    }\n});\nWilliamsRIndicator_extend(WilliamsRIndicator.prototype, {\n    nameBase: 'Williams %R'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('williamsr', WilliamsRIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const WilliamsR_WilliamsRIndicator = ((/* unused pure expression or super */ null && (WilliamsRIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Williams %R Oscillator` series. If the [type](#series.williamsr.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.williamsr\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/williams-r\n * @apioption series.williamsr\n */\n''; // Adds doclets above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/WMA/WMAIndicator.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: WMAIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray: WMAIndicator_isArray, merge: WMAIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction WMAIndicator_accumulateAverage(points, xVal, yVal, i, index) {\n    const xValue = xVal[i], yValue = index < 0 ? yVal[i] : yVal[i][index];\n    points.push([xValue, yValue]);\n}\n/**\n * @private\n */\nfunction weightedSumArray(array, pLen) {\n    // The denominator is the sum of the number of days as a triangular number.\n    // If there are 5 days, the triangular numbers are 5, 4, 3, 2, and 1.\n    // The sum is 5 + 4 + 3 + 2 + 1 = 15.\n    const denominator = (pLen + 1) / 2 * pLen;\n    // Reduce VS loop => reduce\n    return array.reduce(function (prev, cur, i) {\n        return [null, prev[1] + cur[1] * (i + 1)];\n    })[1] / denominator;\n}\n/**\n * @private\n */\nfunction WMAIndicator_populateAverage(points, xVal, yVal, i) {\n    const pLen = points.length, wmaY = weightedSumArray(points, pLen), wmaX = xVal[i - 1];\n    points.shift(); // Remove point until range < period\n    return [wmaX, wmaY];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The SMA series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.wma\n *\n * @augments Highcharts.Series\n */\nclass WMAIndicator extends WMAIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, xValue = xVal[0], wma = [], xData = [], yData = [];\n        let range = 1, index = -1, i, wmaPoint, yValue = yVal[0];\n        if (xVal.length < period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick\n        if (WMAIndicator_isArray(yVal[0])) {\n            index = params.index;\n            yValue = yVal[0][index];\n        }\n        // Starting point\n        const points = [[xValue, yValue]];\n        // Accumulate first N-points\n        while (range !== period) {\n            WMAIndicator_accumulateAverage(points, xVal, yVal, range, index);\n            range++;\n        }\n        // Calculate value one-by-one for each period in visible data\n        for (i = range; i < yValLen; i++) {\n            wmaPoint = WMAIndicator_populateAverage(points, xVal, yVal, i);\n            wma.push(wmaPoint);\n            xData.push(wmaPoint[0]);\n            yData.push(wmaPoint[1]);\n            WMAIndicator_accumulateAverage(points, xVal, yVal, i, index);\n        }\n        wmaPoint = WMAIndicator_populateAverage(points, xVal, yVal, i);\n        wma.push(wmaPoint);\n        xData.push(wmaPoint[0]);\n        yData.push(wmaPoint[1]);\n        return {\n            values: wma,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Weighted moving average indicator (WMA). This series requires `linkedTo`\n * option to be set.\n *\n * @sample stock/indicators/wma\n *         Weighted moving average indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/wma\n * @optionparent plotOptions.wma\n */\nWMAIndicator.defaultOptions = WMAIndicator_merge(WMAIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        index: 3,\n        period: 9\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('wma', WMAIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const WMA_WMAIndicator = ((/* unused pure expression or super */ null && (WMAIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `WMA` series. If the [type](#series.wma.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.wma\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/wma\n * @apioption series.wma\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/Stock/Indicators/Zigzag/ZigzagIndicator.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: ZigzagIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge: ZigzagIndicator_merge, extend: ZigzagIndicator_extend } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Zig Zag series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.zigzag\n *\n * @augments Highcharts.Series\n */\nclass ZigzagIndicator extends ZigzagIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const lowIndex = params.lowIndex, highIndex = params.highIndex, deviation = params.deviation / 100, deviations = {\n            'low': 1 + deviation,\n            'high': 1 - deviation\n        }, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, zigzag = [], xData = [], yData = [];\n        let i, j, zigzagPoint, directionUp, exitLoop = false, yIndex = false;\n        // Exit if not enough points or no low or high values\n        if (!xVal || xVal.length <= 1 ||\n            (yValLen &&\n                (typeof yVal[0][lowIndex] === 'undefined' ||\n                    typeof yVal[0][highIndex] === 'undefined'))) {\n            return;\n        }\n        // Set first zigzag point candidate\n        const firstZigzagLow = yVal[0][lowIndex], firstZigzagHigh = yVal[0][highIndex];\n        // Search for a second zigzag point candidate,\n        // this will also set first zigzag point\n        for (i = 1; i < yValLen; i++) {\n            // Required change to go down\n            if (yVal[i][lowIndex] <= firstZigzagHigh * deviations.high) {\n                zigzag.push([xVal[0], firstZigzagHigh]);\n                // Second zigzag point candidate\n                zigzagPoint = [xVal[i], yVal[i][lowIndex]];\n                // Next line will be going up\n                directionUp = true;\n                exitLoop = true;\n                // Required change to go up\n            }\n            else if (yVal[i][highIndex] >= firstZigzagLow * deviations.low) {\n                zigzag.push([xVal[0], firstZigzagLow]);\n                // Second zigzag point candidate\n                zigzagPoint = [xVal[i], yVal[i][highIndex]];\n                // Next line will be going down\n                directionUp = false;\n                exitLoop = true;\n            }\n            if (exitLoop) {\n                xData.push(zigzag[0][0]);\n                yData.push(zigzag[0][1]);\n                j = i++;\n                i = yValLen;\n            }\n        }\n        // Search for next zigzags\n        for (i = j; i < yValLen; i++) {\n            if (directionUp) { // Next line up\n                // lower when going down -> change zigzag candidate\n                if (yVal[i][lowIndex] <= zigzagPoint[1]) {\n                    zigzagPoint = [xVal[i], yVal[i][lowIndex]];\n                }\n                // Required change to go down -> new zigzagpoint and\n                // direction change\n                if (yVal[i][highIndex] >=\n                    zigzagPoint[1] * deviations.low) {\n                    yIndex = highIndex;\n                }\n            }\n            else { // Next line down\n                // higher when going up -> change zigzag candidate\n                if (yVal[i][highIndex] >= zigzagPoint[1]) {\n                    zigzagPoint = [xVal[i], yVal[i][highIndex]];\n                }\n                // Required change to go down -> new zigzagpoint and\n                // direction change\n                if (yVal[i][lowIndex] <=\n                    zigzagPoint[1] * deviations.high) {\n                    yIndex = lowIndex;\n                }\n            }\n            if (yIndex !== false) { // New zigzag point and direction change\n                zigzag.push(zigzagPoint);\n                xData.push(zigzagPoint[0]);\n                yData.push(zigzagPoint[1]);\n                zigzagPoint = [xVal[i], yVal[i][yIndex]];\n                directionUp = !directionUp;\n                yIndex = false;\n            }\n        }\n        const zigzagLen = zigzag.length;\n        // No zigzag for last point\n        if (zigzagLen !== 0 &&\n            zigzag[zigzagLen - 1][0] < xVal[yValLen - 1]) {\n            // Set last point from zigzag candidate\n            zigzag.push(zigzagPoint);\n            xData.push(zigzagPoint[0]);\n            yData.push(zigzagPoint[1]);\n        }\n        return {\n            values: zigzag,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Zig Zag indicator.\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/zigzag\n *         Zig Zag indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/zigzag\n * @optionparent plotOptions.zigzag\n */\nZigzagIndicator.defaultOptions = ZigzagIndicator_merge(ZigzagIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index, period\n     */\n    params: {\n        // Index and period are unchangeable, do not inherit (#15362)\n        index: void 0,\n        period: void 0,\n        /**\n         * The point index which indicator calculations will base - low\n         * value.\n         *\n         * For example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         */\n        lowIndex: 2,\n        /**\n         * The point index which indicator calculations will base - high\n         * value.\n         *\n         * For example using OHLC data, index=1 means the indicator will be\n         * calculated using High values.\n         */\n        highIndex: 1,\n        /**\n         * The threshold for the value change.\n         *\n         * For example deviation=1 means the indicator will ignore all price\n         * movements less than 1%.\n         */\n        deviation: 1\n    }\n});\nZigzagIndicator_extend(ZigzagIndicator.prototype, {\n    nameComponents: ['deviation'],\n    nameSuffixes: ['%'],\n    nameBase: 'Zig Zag'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('zigzag', ZigzagIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Zigzag_ZigzagIndicator = ((/* unused pure expression or super */ null && (ZigzagIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Zig Zag` series. If the [type](#series.zigzag.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.zigzag\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/zigzag\n * @apioption series.zigzag\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/Stock/Indicators/LinearRegression/LinearRegressionIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: LinearRegressionIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray: LinearRegressionIndicator_isArray, extend: LinearRegressionIndicator_extend, merge: LinearRegressionIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Linear regression series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearregression\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionIndicator extends LinearRegressionIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Return the slope and intercept of a straight line function.\n     *\n     * @private\n     *\n     * @param {Array<number>} xData\n     * List of all x coordinates in a period.\n     *\n     * @param {Array<number>} yData\n     * List of all y coordinates in a period.\n     *\n     * @return {Highcharts.RegressionLineParametersObject}\n     * Object that contains the slope and the intercept of a straight line\n     * function.\n     */\n    getRegressionLineParameters(xData, yData) {\n        // Least squares method\n        const yIndex = this.options.params.index, getSingleYValue = function (yValue, yIndex) {\n            return LinearRegressionIndicator_isArray(yValue) ? yValue[yIndex] : yValue;\n        }, xSum = xData.reduce(function (accX, val) {\n            return val + accX;\n        }, 0), ySum = yData.reduce(function (accY, val) {\n            return getSingleYValue(val, yIndex) + accY;\n        }, 0), xMean = xSum / xData.length, yMean = ySum / yData.length;\n        let xError, yError, i, formulaNumerator = 0, formulaDenominator = 0;\n        for (i = 0; i < xData.length; i++) {\n            xError = xData[i] - xMean;\n            yError = getSingleYValue(yData[i], yIndex) - yMean;\n            formulaNumerator += xError * yError;\n            formulaDenominator += Math.pow(xError, 2);\n        }\n        const slope = formulaDenominator ?\n            formulaNumerator / formulaDenominator : 0; // Don't divide by 0\n        return {\n            slope: slope,\n            intercept: yMean - slope * xMean\n        };\n    }\n    /**\n     * Return the y value on a straight line.\n     *\n     * @private\n     *\n     * @param {Highcharts.RegressionLineParametersObject} lineParameters\n     * Object that contains the slope and the intercept of a straight line\n     * function.\n     *\n     * @param {number} endPointX\n     * X coordinate of the point.\n     *\n     * @return {number}\n     * Y value of the point that lies on the line.\n     */\n    getEndPointY(lineParameters, endPointX) {\n        return lineParameters.slope * endPointX + lineParameters.intercept;\n    }\n    /**\n     * Transform the coordinate system so that x values start at 0 and\n     * apply xAxisUnit.\n     *\n     * @private\n     *\n     * @param {Array<number>} xData\n     * List of all x coordinates in a period\n     *\n     * @param {number} xAxisUnit\n     * Option (see the API)\n     *\n     * @return {Array<number>}\n     * Array of transformed x data\n     */\n    transformXData(xData, xAxisUnit) {\n        const xOffset = xData[0];\n        return xData.map(function (xValue) {\n            return (xValue - xOffset) / xAxisUnit;\n        });\n    }\n    /**\n     * Find the closest distance between points in the base series.\n     * @private\n     * @param {Array<number>} xData list of all x coordinates in the base series\n     * @return {number} - closest distance between points in the base series\n     */\n    findClosestDistance(xData) {\n        let distance, closestDistance, i;\n        for (i = 1; i < xData.length - 1; i++) {\n            distance = xData[i] - xData[i - 1];\n            if (distance > 0 &&\n                (typeof closestDistance === 'undefined' ||\n                    distance < closestDistance)) {\n                closestDistance = distance;\n            }\n        }\n        return closestDistance;\n    }\n    // Required to be implemented - starting point for indicator's logic\n    getValues(baseSeries, regressionSeriesParams) {\n        const xData = baseSeries.xData, yData = baseSeries.yData, period = regressionSeriesParams.period, \n        // Format required to be returned\n        indicatorData = {\n            xData: [], // By getValues() method\n            yData: [],\n            values: []\n        }, xAxisUnit = this.options.params.xAxisUnit ||\n            this.findClosestDistance(xData);\n        let lineParameters, i, periodStart, periodEnd, endPointX, endPointY, periodXData, periodYData, periodTransformedXData;\n        // Iteration logic: x value of the last point within the period\n        // (end point) is used to represent the y value (regression)\n        // of the entire period.\n        for (i = period - 1; i <= xData.length - 1; i++) {\n            periodStart = i - period + 1; // Adjusted for slice() function\n            periodEnd = i + 1; // (as above)\n            endPointX = xData[i];\n            periodXData = xData.slice(periodStart, periodEnd);\n            periodYData = yData.slice(periodStart, periodEnd);\n            periodTransformedXData = this.transformXData(periodXData, xAxisUnit);\n            lineParameters = this.getRegressionLineParameters(periodTransformedXData, periodYData);\n            endPointY = this.getEndPointY(lineParameters, periodTransformedXData[periodTransformedXData.length - 1]);\n            // @todo this is probably not used anywhere\n            indicatorData.values.push({\n                regressionLineParameters: lineParameters,\n                x: endPointX,\n                y: endPointY\n            });\n            if (LinearRegressionIndicator_isArray(indicatorData.xData)) {\n                indicatorData.xData.push(endPointX);\n            }\n            if (LinearRegressionIndicator_isArray(indicatorData.yData)) {\n                indicatorData.yData.push(endPointY);\n            }\n        }\n        return indicatorData;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression indicator. This series requires `linkedTo` option to be\n * set.\n *\n * @sample {highstock} stock/indicators/linear-regression\n *         Linear regression indicator\n *\n * @extends      plotOptions.sma\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/regressions\n * @optionparent plotOptions.linearregression\n */\nLinearRegressionIndicator.defaultOptions = LinearRegressionIndicator_merge(LinearRegressionIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * Unit (in milliseconds) for the x axis distances used to\n         * compute the regression line parameters (slope & intercept)\n         * for every range. In Highcharts Stock the x axis values are\n         * always represented in milliseconds which may cause that\n         * distances between points are \"big\" integer numbers.\n         *\n         * Highcharts Stock's linear regression algorithm (least squares\n         * method) will utilize these \"big\" integers for finding the\n         * slope and the intercept of the regression line for each\n         * period. In consequence, this value may be a very \"small\"\n         * decimal number that's hard to interpret by a human.\n         *\n         * For instance: `xAxisUnit` equaled to `86400000` ms (1 day)\n         * forces the algorithm to treat `86400000` as `1` while\n         * computing the slope and the intercept. This may enhance the\n         * legibility of the indicator's values.\n         *\n         * Default value is the closest distance between two data\n         * points.\n         *\n         * In `v9.0.2`, the default value has been changed\n         * from `undefined` to `null`.\n         *\n         * @sample {highstock} stock/plotoptions/linear-regression-xaxisunit\n         *         xAxisUnit set to 1 minute\n         *\n         * @example\n         * // In Liniear Regression Slope Indicator series `xAxisUnit`is\n         * // `86400000` (1 day) and period is `3`. There're 3 points in\n         * // the base series:\n         *\n         * data: [\n         *   [Date.UTC(2020, 0, 1), 1],\n         *   [Date.UTC(2020, 0, 2), 3],\n         *   [Date.UTC(2020, 0, 3), 5]\n         * ]\n         *\n         * // This will produce one point in the indicator series that\n         * // has a `y` value of `2` (slope of the regression line). If\n         * // we change the `xAxisUnit` to `1` (ms) the value of the\n         * // indicator's point will be `2.3148148148148148e-8` which is\n         * // harder to interpert for a human.\n         *\n         * @type    {null|number}\n         * @product highstock\n         */\n        xAxisUnit: null\n    },\n    tooltip: {\n        valueDecimals: 4\n    }\n});\nLinearRegressionIndicator_extend(LinearRegressionIndicator.prototype, {\n    nameBase: 'Linear Regression Indicator'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('linearRegression', LinearRegressionIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegression_LinearRegressionIndicator = ((/* unused pure expression or super */ null && (LinearRegressionIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A linear regression series. If the\n * [type](#series.linearregression.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregression\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregression\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/LinearRegressionSlopes/LinearRegressionSlopesIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { linearRegression: LinearRegressionSlopesIndicator_LinearRegressionIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: LinearRegressionSlopesIndicator_extend, merge: LinearRegressionSlopesIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Linear Regression Slope series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearRegressionSlope\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionSlopesIndicator extends LinearRegressionSlopesIndicator_LinearRegressionIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEndPointY(lineParameters) {\n        return lineParameters.slope;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression slope indicator. This series requires `linkedTo`\n * option to be set.\n *\n * @sample {highstock} stock/indicators/linear-regression-slope\n *         Linear regression slope indicator\n *\n * @extends      plotOptions.linearregression\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @optionparent plotOptions.linearregressionslope\n */\nLinearRegressionSlopesIndicator.defaultOptions = LinearRegressionSlopesIndicator_merge(LinearRegressionSlopesIndicator_LinearRegressionIndicator.defaultOptions);\nLinearRegressionSlopesIndicator_extend(LinearRegressionSlopesIndicator.prototype, {\n    nameBase: 'Linear Regression Slope Indicator'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('linearRegressionSlope', LinearRegressionSlopesIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegressionSlopes_LinearRegressionSlopesIndicator = ((/* unused pure expression or super */ null && (LinearRegressionSlopesIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A linear regression intercept series. If the\n * [type](#series.linearregressionslope.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregressionslope\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregressionslope\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/LinearRegressionIntercept/LinearRegressionInterceptIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { linearRegression: LinearRegressionInterceptIndicator_LinearRegressionIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: LinearRegressionInterceptIndicator_extend, merge: LinearRegressionInterceptIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Linear Regression Intercept series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearRegressionIntercept\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionInterceptIndicator extends LinearRegressionInterceptIndicator_LinearRegressionIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getEndPointY(lineParameters) {\n        return lineParameters.intercept;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression intercept indicator. This series requires `linkedTo`\n * option to be set.\n *\n * @sample {highstock} stock/indicators/linear-regression-intercept\n *         Linear intercept slope indicator\n *\n * @extends      plotOptions.linearregression\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @optionparent plotOptions.linearregressionintercept\n */\nLinearRegressionInterceptIndicator.defaultOptions = LinearRegressionInterceptIndicator_merge(LinearRegressionInterceptIndicator_LinearRegressionIndicator.defaultOptions);\nLinearRegressionInterceptIndicator_extend(LinearRegressionInterceptIndicator.prototype, {\n    nameBase: 'Linear Regression Intercept Indicator'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('linearRegressionIntercept', LinearRegressionInterceptIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegressionIntercept_LinearRegressionInterceptIndicator = ((/* unused pure expression or super */ null && (LinearRegressionInterceptIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A linear regression intercept series. If the\n * [type](#series.linearregressionintercept.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregressionintercept\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregressionintercept\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/LinearRegressionAngle/LinearRegressionAngleIndicator.js\n/**\n *\n *  (c) 2010-2025 Kamil Kulig\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { linearRegression: LinearRegressionAngleIndicator_LinearRegressionIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: LinearRegressionAngleIndicator_extend, merge: LinearRegressionAngleIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Linear Regression Angle series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.linearRegressionAngle\n *\n * @augments Highcharts.Series\n */\nclass LinearRegressionAngleIndicator extends LinearRegressionAngleIndicator_LinearRegressionIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Convert a slope of a line to angle (in degrees) between\n     * the line and x axis\n     * @private\n     * @param {number} slope of the straight line function\n     * @return {number} angle in degrees\n     */\n    slopeToAngle(slope) {\n        return Math.atan(slope) * (180 / Math.PI); // Rad to deg\n    }\n    getEndPointY(lineParameters) {\n        return this.slopeToAngle(lineParameters.slope);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Linear regression angle indicator. This series requires `linkedTo`\n * option to be set.\n *\n * @sample {highstock} stock/indicators/linear-regression-angle\n *         Linear intercept angle indicator\n *\n * @extends      plotOptions.linearregression\n * @since        7.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @optionparent plotOptions.linearregressionangle\n */\nLinearRegressionAngleIndicator.defaultOptions = LinearRegressionAngleIndicator_merge(LinearRegressionAngleIndicator_LinearRegressionIndicator.defaultOptions, {\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span>' +\n            '{series.name}: <b>{point.y}°</b><br/>'\n    }\n});\nLinearRegressionAngleIndicator_extend(LinearRegressionAngleIndicator.prototype, {\n    nameBase: 'Linear Regression Angle Indicator'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('linearRegressionAngle', LinearRegressionAngleIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const LinearRegressionAngle_LinearRegressionAngleIndicator = ((/* unused pure expression or super */ null && (LinearRegressionAngleIndicator)));\n/**\n * A linear regression intercept series. If the\n * [type](#series.linearregressionangle.type) option is not specified, it is\n * inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.linearregressionangle\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser,dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/regressions\n * @apioption series.linearregressionangle\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/ABands/ABandsIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { sma: ABandsIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: ABandsIndicator_correctFloat, extend: ABandsIndicator_extend, merge: ABandsIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction getBaseForBand(low, high, factor) {\n    return (((ABandsIndicator_correctFloat(high - low)) /\n        ((ABandsIndicator_correctFloat(high + low)) / 2)) * 1000) * factor;\n}\n/**\n * @private\n */\nfunction getPointUB(high, base) {\n    return high * (ABandsIndicator_correctFloat(1 + 2 * base));\n}\n/**\n * @private\n */\nfunction getPointLB(low, base) {\n    return low * (ABandsIndicator_correctFloat(1 - 2 * base));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ABands series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.abands\n *\n * @augments Highcharts.Series\n */\nclass ABandsIndicator extends ABandsIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, factor = params.factor, index = params.index, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, \n        // Upperbands\n        UB = [], \n        // Lowerbands\n        LB = [], \n        // ABANDS array structure:\n        // 0-date, 1-top line, 2-middle line, 3-bottom line\n        ABANDS = [], low = 2, high = 1, xData = [], yData = [];\n        // Middle line, top line and bottom line\n        let ML, TL, BL, date, bandBase, pointSMA, ubSMA, lbSMA, slicedX, slicedY, i;\n        if (yValLen < period) {\n            return;\n        }\n        for (i = 0; i <= yValLen; i++) {\n            // Get UB and LB values of every point. This condition\n            // is necessary, because there is a need to calculate current\n            // UB nad LB values simultaneously with given period SMA\n            // in one for loop.\n            if (i < yValLen) {\n                bandBase = getBaseForBand(yVal[i][low], yVal[i][high], factor);\n                UB.push(getPointUB(yVal[i][high], bandBase));\n                LB.push(getPointLB(yVal[i][low], bandBase));\n            }\n            if (i >= period) {\n                slicedX = xVal.slice(i - period, i);\n                slicedY = yVal.slice(i - period, i);\n                ubSMA = super.getValues.call(this, {\n                    xData: slicedX,\n                    yData: UB.slice(i - period, i)\n                }, {\n                    period: period\n                });\n                lbSMA = super.getValues.call(this, {\n                    xData: slicedX,\n                    yData: LB.slice(i - period, i)\n                }, {\n                    period: period\n                });\n                pointSMA = super.getValues.call(this, {\n                    xData: slicedX,\n                    yData: slicedY\n                }, {\n                    period: period,\n                    index: index\n                });\n                date = pointSMA.xData[0];\n                TL = ubSMA.yData[0];\n                BL = lbSMA.yData[0];\n                ML = pointSMA.yData[0];\n                ABANDS.push([date, TL, ML, BL]);\n                xData.push(date);\n                yData.push([TL, ML, BL]);\n            }\n        }\n        return {\n            values: ABANDS,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Acceleration bands (ABANDS). This series requires the `linkedTo` option\n * to be set and should be loaded after the\n * `stock/indicators/indicators.js`.\n *\n * @sample {highstock} stock/indicators/acceleration-bands\n *         Acceleration Bands\n *\n * @extends      plotOptions.sma\n * @mixes        Highcharts.MultipleLinesMixin\n * @since        7.0.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, compare, compareBase, joinBy, keys,\n *               navigatorOptions, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, showInNavigator,\n *               stacking,\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/acceleration-bands\n * @optionparent plotOptions.abands\n */\nABandsIndicator.defaultOptions = ABandsIndicator_merge(ABandsIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * Option for fill color between lines in Accelleration bands Indicator.\n     *\n     * @sample {highstock} stock/indicators/indicator-area-fill\n     *      Background fill between lines.\n     *\n     * @type {Highcharts.Color}\n     * @since 9.3.2\n     * @apioption plotOptions.abands.fillColor\n     *\n     */\n    params: {\n        period: 20,\n        /**\n         * The algorithms factor value used to calculate bands.\n         *\n         * @product highstock\n         */\n        factor: 0.001,\n        index: 3\n    },\n    lineWidth: 1,\n    topLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1\n        }\n    },\n    bottomLine: {\n        styles: {\n            /**\n             * Pixel width of the line.\n             */\n            lineWidth: 1\n        }\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nABandsIndicator_extend(ABandsIndicator.prototype, {\n    areaLinesNames: ['top', 'bottom'],\n    linesApiNames: ['topLine', 'bottomLine'],\n    nameBase: 'Acceleration Bands',\n    nameComponents: ['period', 'factor'],\n    pointArrayMap: ['top', 'middle', 'bottom'],\n    pointValKey: 'middle'\n});\nIndicators_MultipleLinesComposition.compose(ABandsIndicator);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('abands', ABandsIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ABands_ABandsIndicator = ((/* unused pure expression or super */ null && (ABandsIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An Acceleration bands indicator. If the [type](#series.abands.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.abands\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, compare, compareBase, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *            stacking, showInNavigator,\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/acceleration-bands\n * @apioption series.abands\n */\n''; // To include the above in jsdoc\n\n;// ./code/es-modules/Stock/Indicators/TrendLine/TrendLineIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: TrendLineIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: TrendLineIndicator_extend, merge: TrendLineIndicator_merge, isArray: TrendLineIndicator_isArray } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Trend line series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.trendline\n *\n * @augments Highcharts.Series\n */\nclass TrendLineIndicator extends TrendLineIndicator_SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.updateAllPoints = true;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const orgXVal = series.xData, yVal = series.yData, xVal = [], LR = [], xData = [], yData = [], index = params.index;\n        let numerator = 0, denominator = 0, xValSum = 0, yValSum = 0, counter = 0;\n        // Create an array of consecutive xValues, (don't remove duplicates)\n        for (let i = 0; i < orgXVal.length; i++) {\n            if (i === 0 || orgXVal[i] !== orgXVal[i - 1]) {\n                counter++;\n            }\n            xVal.push(counter);\n        }\n        for (let i = 0; i < xVal.length; i++) {\n            xValSum += xVal[i];\n            yValSum += TrendLineIndicator_isArray(yVal[i]) ? yVal[i][index] : yVal[i];\n        }\n        const meanX = xValSum / xVal.length, meanY = yValSum / yVal.length;\n        for (let i = 0; i < xVal.length; i++) {\n            const y = TrendLineIndicator_isArray(yVal[i]) ? yVal[i][index] : yVal[i];\n            numerator += (xVal[i] - meanX) * (y - meanY);\n            denominator += Math.pow(xVal[i] - meanX, 2);\n        }\n        // Calculate linear regression:\n        for (let i = 0; i < xVal.length; i++) {\n            // Check if the xVal is already used\n            if (orgXVal[i] === xData[xData.length - 1]) {\n                continue;\n            }\n            const x = orgXVal[i], y = meanY + (numerator / denominator) * (xVal[i] - meanX);\n            LR.push([x, y]);\n            xData.push(x);\n            yData.push(y);\n        }\n        return {\n            xData: xData,\n            yData: yData,\n            values: LR\n        };\n    }\n}\n/**\n * Trendline (linear regression) fits a straight line to the selected data\n * using a method called the Sum Of Least Squares. This series requires the\n * `linkedTo` option to be set.\n *\n * @sample stock/indicators/trendline\n *         Trendline indicator\n *\n * @extends      plotOptions.sma\n * @since        7.1.3\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/trendline\n * @optionparent plotOptions.trendline\n */\nTrendLineIndicator.defaultOptions = TrendLineIndicator_merge(TrendLineIndicator_SMAIndicator.defaultOptions, {\n    /**\n     * @excluding period\n     */\n    params: {\n        period: void 0, // Unchangeable period, do not inherit (#15362)\n        /**\n         * The point index which indicator calculations will base. For\n         * example using OHLC data, index=2 means the indicator will be\n         * calculated using Low values.\n         *\n         * @default 3\n         */\n        index: 3\n    }\n});\nTrendLineIndicator_extend(TrendLineIndicator.prototype, {\n    nameBase: 'Trendline',\n    nameComponents: void 0\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('trendline', TrendLineIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TrendLine_TrendLineIndicator = ((/* unused pure expression or super */ null && (TrendLineIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `TrendLine` series. If the [type](#series.trendline.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.trendline\n * @since     7.1.3\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/trendline\n * @apioption series.trendline\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/Stock/Indicators/DisparityIndex/DisparityIndexIndicator.js\n/* *\n *  (c) 2010-2025 Rafal Sebestjanski\n *\n *  Disparity Index technical indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: DisparityIndexIndicator_SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { correctFloat: DisparityIndexIndicator_correctFloat, defined: DisparityIndexIndicator_defined, extend: DisparityIndexIndicator_extend, isArray: DisparityIndexIndicator_isArray, merge: DisparityIndexIndicator_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Disparity Index series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.disparityindex\n *\n * @augments Highcharts.Series\n */\nclass DisparityIndexIndicator extends DisparityIndexIndicator_SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        const args = arguments, ctx = this, // Disparity Index indicator\n        params = args[1].params, // Options.params\n        averageType = params && params.average ? params.average : void 0;\n        ctx.averageIndicator = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes[averageType] || DisparityIndexIndicator_SMAIndicator;\n        ctx.averageIndicator.prototype.init.apply(ctx, args);\n    }\n    calculateDisparityIndex(curPrice, periodAverage) {\n        return DisparityIndexIndicator_correctFloat(curPrice - periodAverage) / periodAverage * 100;\n    }\n    getValues(series, params) {\n        const index = params.index, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, disparityIndexPoint = [], xData = [], yData = [], \n        // \"as any\" because getValues doesn't exist on typeof Series\n        averageIndicator = this.averageIndicator, isOHLC = DisparityIndexIndicator_isArray(yVal[0]), \n        // Get the average indicator's values\n        values = averageIndicator.prototype.getValues(series, params), yValues = values.yData, start = xVal.indexOf(values.xData[0]);\n        // Check period, if bigger than points length, skip\n        if (!yValues || yValues.length === 0 ||\n            !DisparityIndexIndicator_defined(index) ||\n            yVal.length <= start) {\n            return;\n        }\n        // Get the Disparity Index indicator's values\n        for (let i = start; i < yValLen; i++) {\n            const disparityIndexValue = this.calculateDisparityIndex(isOHLC ? yVal[i][index] : yVal[i], yValues[i - start]);\n            disparityIndexPoint.push([\n                xVal[i],\n                disparityIndexValue\n            ]);\n            xData.push(xVal[i]);\n            yData.push(disparityIndexValue);\n        }\n        return {\n            values: disparityIndexPoint,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Disparity Index.\n * This series requires the `linkedTo` option to be set and should\n * be loaded after the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/disparity-index\n *         Disparity Index indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointRange, pointStart, showInNavigator, stacking\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/disparity-index\n * @optionparent plotOptions.disparityindex\n */\nDisparityIndexIndicator.defaultOptions = DisparityIndexIndicator_merge(DisparityIndexIndicator_SMAIndicator.defaultOptions, {\n    params: {\n        /**\n         * The average used to calculate the Disparity Index indicator.\n         * By default it uses SMA, with EMA as an option. To use other\n         * averages, e.g. TEMA, the `stock/indicators/tema.js` file needs to\n         * be loaded.\n         *\n         * If value is different than `ema`, `dema`, `tema` or `wma`,\n         * then sma is used.\n         */\n        average: 'sma',\n        index: 3\n    },\n    marker: {\n        enabled: false\n    },\n    dataGrouping: {\n        approximation: 'averages'\n    }\n});\nDisparityIndexIndicator_extend(DisparityIndexIndicator.prototype, {\n    nameBase: 'Disparity Index',\n    nameComponents: ['period', 'average']\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('disparityindex', DisparityIndexIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DisparityIndex_DisparityIndexIndicator = ((/* unused pure expression or super */ null && (DisparityIndexIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The Disparity Index indicator series.\n * If the [type](#series.disparityindex.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.disparityindex\n * @since 9.1.0\n * @product   highstock\n * @excluding allAreas, colorAxis,  dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/disparity-index\n * @apioption series.disparityindex\n */\n''; // To include the above in the js output\n\n;// external \"../modules/stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const stock_src_js_namespaceObject = x({  });\n;// ./code/es-modules/masters/indicators/indicators-all.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nG.MultipleLinesComposition =\n    G.MultipleLinesComposition || Indicators_MultipleLinesComposition;\n/* harmony default export */ const indicators_all_src = ((external_highcharts_src_js_default_default()));\n\nexport { indicators_all_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__WEBPACK_EXTERNAL_MODULE__modules_datagrouping_src_js_b7a4250c__", "MultipleLinesComposition", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_Chart_namespaceObject", "Chart", "external_highcharts_src_js_default_Chart_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "line", "LineSeries", "seriesTypes", "addEvent", "fireEvent", "error", "extend", "isArray", "merge", "pick", "tableToMultiYData", "series", "processed", "yData", "pointArrayMap", "table", "dataTable", "modified", "getColumn", "columns", "map", "i", "rowCount", "values", "colIndex", "push", "SMAIndicator", "destroy", "dataEventsToUnbind", "for<PERSON>ach", "unbinder", "apply", "arguments", "getName", "params", "name", "nameComponents", "component", "index", "options", "nameSuffixes", "nameBase", "type", "toUpperCase", "join", "getV<PERSON>ues", "period", "xVal", "xData", "yVal", "yValLen", "length", "SMA", "range", "SMAPoint", "sum", "init", "chart", "indicator", "linkedSeriesUnbiner", "isUpdating", "hasEvents", "linkedParent", "linkedTo", "recalculateValues", "calculateOn", "xAxis", "closestPointRange", "order", "eventsToUnbind", "croppedDataValues", "oldData", "points", "oldDataLength", "overwriteData", "oldFirstPointIndex", "oldLastPointIndex", "min", "max", "processedYData", "processedData", "valueColumns", "column", "hasGroupedData", "visible", "cropped", "croppedData", "cropData", "keys", "indicatorXData", "indexOf", "x", "shift", "updateData", "updateAllPoints", "setColumns", "data", "isDirty", "redraw", "isDirtyData", "linkedSeries", "processData", "compareToMain", "dataModify", "compareValue", "defaultOptions", "tooltip", "valueDecimals", "hasDerivedData", "useCommonDataGrouping", "registerSeriesType", "sma", "EMAIndicator_SMAIndicator", "correctFloat", "EMAIndicator_isArray", "EMAIndicator_merge", "EMAIndicator", "accumulatePeriodPoints", "y", "calculateEma", "EMApercent", "calEMA", "yValue", "EMA", "EMAPoint", "ADIndicator_SMAIndicator", "ADIndicator_error", "ADIndicator_extend", "ADIndicator_merge", "ADIndicator", "populateAverage", "yValVolume", "_period", "high", "low", "close", "volume", "adY", "len", "ADPoint", "volumeSeriesID", "volumeSeries", "AD", "noop", "columnProto", "AOIndicator_SMAIndicator", "AOIndicator_extend", "AOIndicator_merge", "AOIndicator_correctFloat", "AOIndicator_isArray", "AOIndicator", "drawGraph", "userColor", "userOptions", "color", "positiveColor", "greaterBarColor", "negativeColor", "lowerBarColor", "firstPoint", "AO", "awesome", "shortLastIndex", "longLastIndex", "price", "j", "longSum", "shortSum", "<PERSON>P<PERSON><PERSON>", "shortSMA", "threshold", "groupPadding", "pointPadding", "crisp", "states", "hover", "halo", "size", "markerAttribs", "getColumnMetrics", "crispCol", "translate", "drawPoints", "sma<PERSON><PERSON><PERSON>", "defined", "MultipleLinesComposition_error", "MultipleLinesComposition_merge", "linesApiNames", "areaLinesNames", "getLineName", "propertyName", "char<PERSON>t", "slice", "getTranslatedLinesNames", "excludedValue", "translatedLines", "indicatorDrawGraph", "pointVal<PERSON>ey", "mainLinePoints", "mainLineOptions", "mainLinePath", "graph", "gappedExtend", "gapSize", "secondaryLines", "secondaryLinesNames", "pointsLength", "point", "plotLine", "plotX", "plotY", "isNull", "fillColor", "secondLinePoints", "firstLinePoints", "originalColor", "nextPoints", "area", "fillGraph", "lineName", "styles", "indicatorGetGraphPath", "areaPath", "path", "higherAreaPath", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "indicatorToYData", "pointColl", "indicatorTranslate", "LinesNames", "value", "modifyValue", "yAxis", "toPixels", "compose", "IndicatorClass", "proto", "toYData", "Indicators_MultipleLinesComposition", "AroonIndicator_SMAIndicator", "AroonIndicator_extend", "AroonIndicator_merge", "AroonIndicator_pick", "getExtremeIndexInArray", "arr", "extreme", "extremeValue", "valueIndex", "AroonIndicator", "aroonUp", "aroonDown", "xLow", "slicedY", "AR", "elem", "marker", "enabled", "pointFormat", "lineWidth", "lineColor", "dataGrouping", "approximation", "aroon", "AroonOscillatorIndicator_AroonIndicator", "AroonOscillatorIndicator_extend", "AroonOscillatorIndicator_merge", "AroonOscillatorIndicator", "oscillator", "ARO", "ATRIndicator_SMAIndicator", "ATRIndicator_isArray", "ATRIndicator_merge", "getTR", "currentPoint", "prevPoint", "HL", "pointY", "Math", "abs", "prevY", "ATRIndicator", "ATR", "prevATR", "TR", "accumulateAverage", "xValue", "BBIndicator_SMAIndicator", "BBIndicator_extend", "BBIndicator_isArray", "BBIndicator_merge", "BBIndicator", "topLine", "bottomLine", "ML", "TL", "BL", "date", "slicedX", "stdDev", "standardDeviation", "BB", "isOHLC", "getStandardDeviation", "mean", "arr<PERSON>en", "std", "variance", "sqrt", "CCIIndicator_SMAIndicator", "CCIIndicator_isArray", "CCIIndicator_merge", "CCIIndicator", "TP", "CCI", "CCIPoint", "p", "periodTP", "smaTP", "TPtemp", "meanDev", "sumArray", "array", "reduce", "prev", "cur", "meanDeviation", "CMFIndicator_SMAIndicator", "CMFIndicator_merge", "CMFIndicator", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "isSeriesOHLC", "is<PERSON>ength<PERSON><PERSON>d", "serie", "getMoneyFlow", "seriesYData", "volumeSeriesYData", "moneyFlowVolume", "moneyFlowXData", "moneyFlowYData", "nullIndex", "sumVolume", "sumMoneyFlowVolume", "getMoneyFlowVolume", "ohlc", "getMoneyFlowMultiplier", "h", "c", "l", "DMIIndicator_SMAIndicator", "DMIIndicator_correctFloat", "DMIIndicator_extend", "DMIIndicator_isArray", "DMIIndicator_merge", "DMIIndicator", "calculateDM", "isPositiveDM", "DM", "currentHigh", "currentLow", "previousHigh", "previousLow", "calculateDI", "smoothedDM", "tr", "calculateDX", "plusDI", "minusDI", "smoothValues", "accumulatedValues", "currentValue", "DMI", "prevSmoothedPlusDM", "prevSmoothedMinusDM", "prevSmoothedTR", "smoothedPlusDM", "smoothedMinusDM", "smoothedTR", "plusDM", "minusDM", "DX", "plusDILine", "minusDILine", "parallelArrays", "DPOIndicator_SMAIndicator", "DPOIndicator_extend", "DPOIndicator_merge", "DPOIndicator_correctFloat", "DPOIndicator_pick", "accumulatePoints", "subtract", "DPOIndicator", "offset", "floor", "DPO", "periodIndex", "rangeIndex", "ema", "ChaikinIndicator_EMAIndicator", "ChaikinIndicator_correctFloat", "ChaikinIndicator_extend", "ChaikinIndicator_merge", "ChaikinIndicator_error", "ChaikinIndicator", "periods", "CHA", "ADL", "AD_ADIndicator", "SPE", "LPE", "periodsOffset", "CMOIndicator_SMAIndicator", "isNumber", "CMOIndicator_merge", "CMOIndicator", "CMO", "firstAddedSum", "sumOfHigher<PERSON><PERSON><PERSON>", "sumOfLowerValues", "DEMAIndicator_EMAIndicator", "DEMAIndicator_correctFloat", "DEMAIndicator_isArray", "DEMAIndicator_merge", "DEMAIndicator", "getEMA", "prevEMA", "EMAvalues", "doubledPeriod", "DEMA", "xDataDema", "yDataDema", "EMAlevel2", "prevEMAlevel2", "DEMAPoint", "TEMAIndicator_EMAIndicator", "TEMAIndicator_correctFloat", "TEMAIndicator_isArray", "TEMAIndicator_merge", "TEMAIndicator", "getTemaPoint", "tripled<PERSON>eri<PERSON>", "EMAlevels", "level1", "level2", "level3", "tema", "xDataTema", "yDataTema", "<PERSON>a<PERSON><PERSON><PERSON>", "emaLevel2Values", "emaLevels", "temaPoint", "prevLevel3", "TRIXIndicator_TEMAIndicator", "TRIXIndicator_correctFloat", "TRIXIndicator_merge", "TRIXIndicator", "APOIndicator_EMAIndicator", "APOIndicator_extend", "APOIndicator_merge", "APOIndicator_error", "APOIndicator", "APO", "datagrouping_src_js_default_dataGrouping_approximations_namespaceObject", "approximations", "datagrouping_src_js_default_dataGrouping_approximations_default", "external_highcharts_src_js_default_Color_namespaceObject", "Color", "parse", "external_highcharts_src_js_default_Color_default", "IKHIndicator_SMAIndicator", "IKHIndicator_defined", "IKHIndicator_extend", "IKHIndicator_isArray", "IKHIndicator_isNumber", "getClosestDistance", "IKHIndicator_merge", "objectEach", "highlowLevel", "res", "Infinity", "drawSenkouSpan", "opt", "senkouSpan", "gap", "IKHIndicator", "graphCollection", "tenkanLine", "kijunLine", "chikouLine", "senkouSpanA", "fill", "setOpacity", "senkouSpanB", "tenkanSen", "kijunSen", "chikouSpan", "pointValue", "tooltipPos", "mainColor", "pointArrayMapLength", "allIchimokuPoints", "ikhMap", "intersectIndexColl", "senkouSpanOptions", "lineIndex", "position", "startIntersect", "endIntersect", "sectionPoints", "sectionNextPoints", "pointsPlotYSum", "nextPointsPlotYSum", "senkouSpanTempColor", "concatArrIndex", "k", "intersect", "checkLineIntersection", "a1", "a2", "b1", "b2", "saX", "saY", "sbX", "sbY", "sabX", "sabY", "u", "t", "intersectPointObj", "intersectPoint", "splice", "graphName", "unshift", "concat", "areaName", "graphsenkouSpan", "spanA", "spanAarr", "pointTS", "pointKS", "pointSSB", "TS", "KS", "CS", "SSA", "SSB", "periodTenkan", "periodSenkouSpanB", "s", "IKH", "dateStart", "isEmptyRange", "ret", "average", "KeltnerChannelsIndicator_SMAIndicator", "KeltnerChannelsIndicator_correctFloat", "KeltnerChannelsIndicator_extend", "KeltnerChannelsIndicator_merge", "KeltnerChannelsIndicator", "pointEMA", "pointATR", "periodATR", "multiplierATR", "KC", "seriesEMA", "seriesATR", "atr", "KlingerIndicator_EMAIndicator", "KlingerIndicator_SMAIndicator", "KlingerIndicator_correctFloat", "KlingerIndicator_error", "KlingerIndicator_extend", "KlingerIndicator_isArray", "KlingerIndicator_merge", "KlingerIndicator", "calculateTrend", "isUpward", "isValidData", "firstYVal", "every", "slowAvgPeriod", "getCM", "previousCM", "trend", "previousTrend", "prevoiusDM", "getDM", "getVolumeForce", "volumeForce", "CM", "previousDM", "getSMA", "<PERSON><PERSON>", "calcSingal", "KO", "fastEMA", "slowEMA", "previousFastEMA", "previousSlowEMA", "signal", "SMAFast", "fastAvgPeriod", "SMASlow", "fastEMApercent", "slowEMApercent", "signalPeriod", "curr", "signalLine", "MACDIndicator_noop", "ColumnSeries", "MACDIndicator_SMAIndicator", "MACDIndicator_extend", "MACDIndicator_correctFloat", "MACDIndicator_defined", "MACDIndicator_merge", "MACDIndicator", "colorIndex", "getCyclic", "colors", "macdLine", "macdZones", "zones", "startIndex", "signalZones", "MACD", "plotNames", "graphmacd", "graphsignal", "histogramZones", "otherSignals", "plotMACD", "plotSignal", "applyZones", "hide", "indexToShift", "shortPeriod", "xMACD", "yMACD", "shortEMA", "longEMA", "minP<PERSON><PERSON><PERSON>th", "MFIIndicator_SMAIndicator", "MFIIndicator_extend", "MFIIndicator_merge", "MFIIndicator_error", "MFIIndicator_isArray", "MFIIndicator_sumArray", "calculateTypicalPrice", "MFIIndicator", "decimals", "MFI", "positiveMoneyFlow", "negativeMoneyFlow", "newTypicalPrice", "oldTypicalPrice", "rawMoneyFlow", "negativeMoneyFlowSum", "MFIPoint", "isUp", "typicalPrice", "parseFloat", "positiveMoneyFlowSum", "toFixed", "MomentumIndicator_SMAIndicator", "MomentumIndicator_extend", "MomentumIndicator_isArray", "MomentumIndicator_merge", "MomentumIndicator_populateAverage", "mmY", "MomentumIndicator", "MMPoint", "MM", "NATRIndicator_ATRIndicator", "NATRIndicator_merge", "NATRIndicator", "atrData", "at<PERSON><PERSON><PERSON><PERSON>", "valueSuffix", "OBVIndicator_SMAIndicator", "OBVIndicator_isNumber", "OBVIndicator_error", "OBVIndicator_extend", "OBVIndicator_merge", "OBVIndicator", "OBV", "hasOHLC", "OBVPoint", "previousOBV", "curentOBV", "previousClose", "curentClose", "pointClass", "destroyExtraLabels", "functionName", "props", "element", "PivotPointsIndicator_SMAIndicator", "PivotPointsIndicator_merge", "PivotPointsIndicator_extend", "PivotPointsIndicator_defined", "PivotPointsIndicator_isArray", "PivotPointsIndicator", "P", "plotEndPoint", "endPoint", "allPivotPoints", "pivotPoints", "drawDataLabels", "current<PERSON><PERSON><PERSON>", "pointMapping", "dataLabels", "pivotLine", "dataLabel", "endTimestamp", "slicedXLen", "lastPP", "avg", "placement", "algorithm", "PP", "getPivotAndHLC", "standardPlacement", "diff", "camarillaPlacement", "fibonacciPlacement", "enableMouseTracking", "format", "destroyElements", "PPOIndicator_EMAIndicator", "PPOIndicator_correctFloat", "PPOIndicator_extend", "PPOIndicator_merge", "PPOIndicator_error", "PPOIndicator", "PPO", "Indicators_ArrayUtilities", "getArrayExtremes", "minIndex", "maxIndex", "target", "Number", "MAX_VALUE", "PCIndicator_SMAIndicator", "PCIndicator_merge", "PCIndicator_extend", "PCIndicator", "extremes", "PC", "PriceEnvelopesIndicator_SMAIndicator", "PriceEnvelopesIndicator_extend", "PriceEnvelopesIndicator_isArray", "PriceEnvelopesIndicator_merge", "PriceEnvelopesIndicator", "topPercent", "topBand", "botPercent", "bottomBand", "PE", "PSARIndicator_SMAIndicator", "PSARIndicator_merge", "PSARIndicator_toFixed", "PSARIndicator", "maxAccelerationFactor", "increment", "initialAccelerationFactor", "PSARArr", "accelerationFactor", "direction", "extremePoint", "EPMinusPSAR", "accelerationFactorMultiply", "newDirection", "previousDirection", "prevLow", "prevPrevLow", "prevHigh", "prevPrevHigh", "PSAR", "newExtremePoint", "ind", "EP", "previousExtremePoint", "pdir", "sDir", "pACCMulti", "sLow", "pLow", "pHigh", "sHigh", "pEP", "dir", "pDir", "eP", "pAcc", "inc", "maxAcc", "initAcc", "lineWidthPlus", "ROCIndicator_SMAIndicator", "ROCIndicator_isArray", "ROCIndicator_merge", "ROCIndicator_extend", "ROCIndicator", "ROC", "ROCPoint", "ROCIndicator_populateAverage", "nDaysAgoY", "rocY", "RSIIndicator_SMAIndicator", "RSIIndicator_isNumber", "RSIIndicator_merge", "RSIIndicator_toFixed", "RSIIndicator", "RSI", "gain", "loss", "RSIPoint", "change", "avgGain", "avgLoss", "StochasticIndicator_SMAIndicator", "StochasticIndicator_extend", "StochasticIndicator_isArray", "StochasticIndicator_merge", "StochasticIndicator", "smoothedLine", "periodK", "periodD", "SO", "LL", "K", "D", "<PERSON><PERSON><PERSON><PERSON>", "isNaN", "CL", "SlowStochasticIndicator_SMAIndicator", "stochastic", "SlowStochasticIndicator_StochasticIndicator", "SlowStochasticIndicator_extend", "SlowStochasticIndicator_merge", "SlowStochasticIndicator", "fastValues", "slowValues", "fastYData", "smoothedValues", "xDataLen", "SupertrendIndicator_ATRIndicator", "SupertrendIndicator_SMAIndicator", "SupertrendIndicator_addEvent", "SupertrendIndicator_correctFloat", "SupertrendIndicator_isArray", "SupertrendIndicator_isNumber", "SupertrendIndicator_extend", "SupertrendIndicator_merge", "SupertrendIndicator_objectEach", "createPointObj", "mainSeries", "SupertrendIndicator", "cropThreshold", "parentOptions", "indicOptions", "mainXData", "indicPoints", "indicPath", "tempOffset", "groupedPoints", "top", "bottom", "supertrendLineOptions", "fallingTrendColor", "dashStyle", "risingTrendColor", "changeTrendLine", "nextPoint", "mainPoint", "nextMainPoint", "prevMainPoint", "prevPrevMainPoint", "pointColor", "newPoint", "newNextPoint", "indicPointsLen", "multiplier", "st", "finalUp", "finalDown", "basicUp", "basicDown", "supertrend", "prevFinalUp", "prevFinalDown", "prevST", "VBPPoint_SMAPoint", "VBP_VBPPoint", "negativeGraphic", "animObject", "VBPIndicator_noop", "VBPIndicator_columnProto", "VBPIndicator_SMAIndicator", "VBPIndicator_addEvent", "arrayMax", "arrayMin", "VBPIndicator_correctFloat", "VBPIndicator_defined", "VBPIndicator_error", "VBPIndicator_extend", "VBPIndicator_isArray", "VBPIndicator_merge", "VBPIndicator", "baseSeries", "addCustomEvents", "toEmptyIndicator", "setData", "zoneStarts", "zoneLinesSVG", "animate", "inverted", "group", "attr", "left", "translateY", "translateX", "animation", "step", "val", "fx", "scaleX", "pos", "volumeDivision", "posNegVolume", "initVol", "signOrder", "point<PERSON><PERSON><PERSON>", "posWidths", "neg<PERSON><PERSON><PERSON>", "pointWidth", "priceZone", "wholeVol", "graphic", "shapeArgs", "width", "priceZones", "wholeVolumeData", "positiveVolumeData", "negativeVolumeData", "yAxisMin", "zoneLinesOptions", "zoneLines", "yBarOffset", "maxVolume", "primal<PERSON><PERSON><PERSON><PERSON><PERSON>", "barHeight", "barHeightP", "oldBarHeight", "<PERSON><PERSON><PERSON><PERSON>", "chartPlotTop", "barX", "barY", "indicatorPoints", "volumeDataArray", "plot<PERSON>id<PERSON>", "plotTop", "rangeStep", "start", "reversed", "volumeNeg", "volumePos", "volumeAll", "drawZones", "getExtremes", "prevCompare", "compare", "prevCumulative", "cumulative", "xValues", "yV<PERSON><PERSON>", "ranges", "VBP", "errorMessage", "specifyZones", "zone", "end", "rangeExtremes", "arrayExtremesOHLC", "dataLength", "lowRange", "highRange", "zoneStartsLength", "volumePerZone", "previousValue", "startFlag", "endFlag", "volumeXData", "volumeYData", "lastZoneIndex", "baseSeriesLength", "volumeSeriesLength", "zonesValues", "zonesStyles", "renderer", "rightLinePos", "verticalOffset", "zoneLinesPath", "verticalLinePos", "crispLine", "zIndex", "add", "animationLimit", "align", "allowOverlap", "padding", "style", "fontSize", "verticalAlign", "VWAPIndicator_SMAIndicator", "VWAPIndicator_error", "VWAPIndicator_isArray", "VWAPIndicator_merge", "VWAPIndicator", "calculateVWAPValues", "common<PERSON><PERSON>th", "cPrice", "cVolume", "volumeValues", "volumeLength", "cumulativePrice", "cumulativeVolume", "VWAP", "WilliamsRIndicator_SMAIndicator", "WilliamsRIndicator_extend", "WilliamsRIndicator_isArray", "WilliamsRIndicator_merge", "WilliamsRIndicator", "R", "HH", "WR", "WMAIndicator_SMAIndicator", "WMAIndicator_isArray", "WMAIndicator_merge", "WMAIndicator_accumulateAverage", "WMAIndicator_populateAverage", "pLen", "wmaY", "wmaX", "WMAIndicator", "wma", "wmaPoint", "ZigzagIndicator_SMAIndicator", "ZigzagIndicator_merge", "ZigzagIndicator_extend", "ZigzagIndicator", "lowIndex", "highIndex", "deviation", "deviations", "zigzag", "zigzagPoint", "directionUp", "exitLoop", "yIndex", "firstZigzagLow", "firstZigzagHigh", "zigzagLen", "LinearRegressionIndicator_SMAIndicator", "LinearRegressionIndicator_isArray", "LinearRegressionIndicator_extend", "LinearRegressionIndicator_merge", "LinearRegressionIndicator", "getRegressionLineParameters", "getSingleYValue", "xSum", "accX", "ySum", "accY", "xMean", "y<PERSON><PERSON>", "xError", "formulaNumerator", "formulaDenominator", "pow", "slope", "intercept", "getEndPointY", "lineParameters", "endPointX", "transformXData", "xAxisUnit", "xOffset", "findClosestDistance", "distance", "closestDistance", "regressionSeriesParams", "periodStart", "periodEnd", "endPointY", "periodXData", "periodYData", "periodTransformedXData", "indicatorData", "regressionLineParameters", "linearRegression", "LinearRegressionSlopesIndicator_LinearRegressionIndicator", "LinearRegressionSlopesIndicator_extend", "LinearRegressionSlopesIndicator_merge", "LinearRegressionSlopesIndicator", "LinearRegressionInterceptIndicator_LinearRegressionIndicator", "LinearRegressionInterceptIndicator_extend", "LinearRegressionInterceptIndicator_merge", "LinearRegressionInterceptIndicator", "LinearRegressionAngleIndicator_LinearRegressionIndicator", "LinearRegressionAngleIndicator_extend", "LinearRegressionAngleIndicator_merge", "LinearRegressionAngleIndicator", "slopeToAngle", "PI", "atan", "ABandsIndicator_SMAIndicator", "ABandsIndicator_correctFloat", "ABandsIndicator_extend", "ABandsIndicator_merge", "ABandsIndicator", "bandBase", "pointSMA", "ubSMA", "lbSMA", "factor", "UB", "LB", "ABANDS", "TrendLineIndicator_SMAIndicator", "TrendLineIndicator_extend", "TrendLineIndicator_merge", "TrendLineIndicator_isArray", "TrendLineIndicator", "orgXVal", "LR", "numerator", "denominator", "xValSum", "yValSum", "counter", "meanX", "meanY", "DisparityIndexIndicator_SMAIndicator", "DisparityIndexIndicator_correctFloat", "DisparityIndexIndicator_defined", "DisparityIndexIndicator_extend", "DisparityIndexIndicator_isArray", "DisparityIndexIndicator_merge", "DisparityIndexIndicator", "args", "averageType", "ctx", "averageIndicator", "calculateDisparityIndex", "curPrice", "periodAverage", "disparityIndexPoint", "disparityIndexValue", "G", "indicators_all_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,AAChG,WAAYC,MAAuE,gCAAiC,AACpH,OAA4E,yBAA0B,CAE7F,IAi7BLC,EAj7BSC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDvB,EAAwD,OAAU,CAC7H,IAAIwB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAEpF,IAAME,EAA2DzB,EAAwD,OAAU,CAAC0B,KAAK,CACzI,IAAIC,EAAgExB,EAAoBC,CAAC,CAACqB,GAE1F,IAAMG,EAAoE5B,EAAwD,OAAU,CAAC6B,cAAc,CAC3J,IAAIC,EAAyE3B,EAAoBC,CAAC,CAACwB,GAYnG,GAAM,CAAEG,KAAMC,CAAU,CAAE,CAAG,AAACF,IAA6DG,WAAW,CAEhG,CAAEC,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIhB,IAOhEiB,EAAoB,CAACC,EAAQC,KAC/B,IAAMC,EAAQ,EAAE,CAAEC,EAAgBH,EAAOG,aAAa,CAAEC,EAAQH,GAAaD,EAAOK,SAAS,CAACC,QAAQ,EAAIN,EAAOK,SAAS,CAC1H,GAAI,CAACF,EACD,OAAOH,EAAOO,SAAS,CAAC,IAAKN,GAEjC,IAAMO,EAAUL,EAAcM,GAAG,CAAC,AAACvC,GAAQ8B,EAAOO,SAAS,CAACrC,EAAK+B,IACjE,IAAK,IAAIS,EAAI,EAAGA,EAAIN,EAAMO,QAAQ,CAAED,IAAK,CACrC,IAAME,EAAST,EAAcM,GAAG,CAAC,CAACvC,EAAK2C,IAAaL,CAAO,CAACK,EAAS,EAAE,CAACH,EAAE,EAAI,GAC9ER,EAAMY,IAAI,CAACF,EACf,CACA,OAAOV,CACX,CAWA,OAAMa,UAAqBzB,EASvB0B,SAAU,CACN,IAAI,CAACC,kBAAkB,CAACC,OAAO,CAAC,SAAUC,CAAQ,EAC9CA,GACJ,GACA,KAAK,CAACH,QAAQI,KAAK,CAAC,IAAI,CAAEC,UAC9B,CAIAC,SAAU,CACN,IAAMC,EAAS,EAAE,CACbC,EAAO,IAAI,CAACA,IAAI,CASpB,OARKA,IACD,AAAC,CAAA,IAAI,CAACC,cAAc,EAAI,EAAE,AAAD,EAAGP,OAAO,CAAC,SAAUQ,CAAS,CAAEC,CAAK,EAC1DJ,EAAOT,IAAI,CAAC,IAAI,CAACc,OAAO,CAACL,MAAM,CAACG,EAAU,CACtC5B,EAAK,IAAI,CAAC+B,YAAY,CAACF,EAAM,CAAE,IACvC,EAAG,IAAI,EACPH,EAAO,AAAC,CAAA,IAAI,CAACM,QAAQ,EAAI,IAAI,CAACC,IAAI,CAACC,WAAW,EAAC,EAC1C,CAAA,IAAI,CAACP,cAAc,CAAG,KAAOF,EAAOU,IAAI,CAAC,MAAQ,IAAM,EAAC,GAE1DT,CACX,CAIAU,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,EAAI,EAAE,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAKE,MAAM,CAAEC,EAAM,EAAE,CAAEJ,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACjIQ,EAAGiB,EAAQ,GAAIe,EAAQ,EAAGC,EAAUC,EAAM,EAC9C,IAAIR,CAAAA,EAAKI,MAAM,CAAGL,CAAK,GAQvB,IAJIvC,EAAQ0C,CAAI,CAAC,EAAE,GACfX,CAAAA,EAAQJ,EAAOI,KAAK,CAAGJ,EAAOI,KAAK,CAAG,CAAA,EAGnCe,EAAQP,EAAS,GACpBS,GAAOjB,EAAQ,EAAIW,CAAI,CAACI,EAAM,CAAGJ,CAAI,CAACI,EAAM,CAACf,EAAM,CACnDe,IAGJ,IAAKhC,EAAIgC,EAAOhC,EAAI6B,EAAS7B,IACzBkC,GAAOjB,EAAQ,EAAIW,CAAI,CAAC5B,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAC3CgB,EAAW,CAACP,CAAI,CAAC1B,EAAE,CAAEkC,EAAMT,EAAO,CAClCM,EAAI3B,IAAI,CAAC6B,GACTN,EAAMvB,IAAI,CAAC6B,CAAQ,CAAC,EAAE,EACtBzC,EAAMY,IAAI,CAAC6B,CAAQ,CAAC,EAAE,EACtBC,GAAQjB,EAAQ,EACZW,CAAI,CAAC5B,EAAIgC,EAAM,CACfJ,CAAI,CAAC5B,EAAIgC,EAAM,CAACf,EAAM,CAE9B,MAAO,CACHf,OAAQ6B,EACRJ,MAAOA,EACPnC,MAAOA,CACX,EACJ,CAIA2C,KAAKC,CAAK,CAAElB,CAAO,CAAE,CACjB,IAAMmB,EAAY,IAAI,CACtB,KAAK,CAACF,KAAKjE,IAAI,CAACmE,EAAWD,EAAOlB,GAElC,IAAMoB,EAAsBxD,EAAUP,IAAqD,kBAAmB,SAAU,CAAEgE,WAAAA,CAAU,CAAE,EAGlI,GAAIA,EACA,OAEJ,IAAMC,EAAY,CAAC,CAACH,EAAU9B,kBAAkB,CAACuB,MAAM,CACvD,IAAIO,EAAUI,YAAY,CAmCtB,OAAOzD,EAAM,UACTqD,EAAUnB,OAAO,CAACwB,QAAQ,CAC1B,gCAAiC,CAAA,EAAON,GArB5C,GAfI,CAACI,IAGDH,EAAU9B,kBAAkB,CAACH,IAAI,CAACtB,EAASuD,EAAUI,YAAY,CAAE,cAAe,WAC9EJ,EAAUM,iBAAiB,EAC/B,IAGIN,EAAUO,WAAW,CAACC,KAAK,EAC3BR,EAAU9B,kBAAkB,CAACH,IAAI,CAACtB,EAASuD,EAAUI,YAAY,CAACI,KAAK,CAAER,EAAUO,WAAW,CAACC,KAAK,CAAE,WAClGR,EAAUM,iBAAiB,EAC/B,KAIJN,AAAgC,SAAhCA,EAAUO,WAAW,CAACR,KAAK,CAItBC,EAAUS,iBAAiB,EAC5BT,EAAUM,iBAAiB,QAG9B,GAAI,CAACH,EAAW,CAGjB,IAAM/B,EAAW3B,EAASuD,EAAUD,KAAK,CAAEC,EAAUO,WAAW,CAACR,KAAK,CAAE,WACpEC,EAAUM,iBAAiB,GAE3BlC,GACJ,EACJ,CAOR,EAAG,CACCsC,MAAO,CACX,EAGAV,CAAAA,EAAU9B,kBAAkB,CAAG,EAAE,CACjC8B,EAAUW,cAAc,CAAC5C,IAAI,CAACkC,EAClC,CAIAK,mBAAoB,CAChB,IAAMM,EAAoB,EAAE,CAAoBvD,EAAQ,IAAI,CAACC,SAAS,CAAEuD,EAAUb,AAAxC,IAAI,CAA8Cc,MAAM,EAAI,EAAE,CAAEC,EAAgBf,AAAhF,IAAI,CAAsF1C,SAAS,CAACM,QAAQ,CAKlJoD,EAAgB,CAAA,EAAMC,EAAoBC,EAAmBC,EAAKC,EAIhEjE,EAAQ6C,AAT4B,IAAI,CAStBI,YAAY,CAACjD,KAAK,CAAEkE,EAAiBrB,AATnB,IAAI,CASyBI,YAAY,CAACiB,cAAc,AAClGrB,CAV0C,IAAI,CAUpCI,YAAY,CAACd,KAAK,CAAGU,AAVW,IAAI,CAULI,YAAY,CAChD5C,SAAS,CAAC,KACfwC,AAZ0C,IAAI,CAYpCI,YAAY,CAACjD,KAAK,CAAGH,EAAkBgD,AAZP,IAAI,CAYaI,YAAY,EACvEJ,AAb0C,IAAI,CAapCI,YAAY,CAACiB,cAAc,CAAGrE,EAAkBgD,AAbhB,IAAI,CAasBI,YAAY,CAAE,CAAA,GAMlF,IAAMkB,EAAgBtB,AAnBoB,IAAI,CAmBdI,YAAY,CAACvB,OAAO,EAEhDmB,AArBsC,IAAI,CAqBhCI,YAAY,CAAC9C,SAAS,CAACM,QAAQ,EACxCoC,AAtBqC,IAAI,CAsB/Bb,SAAS,CAACa,AAtBiB,IAAI,CAsBXI,YAAY,CAAEJ,AAtBP,IAAI,CAsBanB,OAAO,CAACL,MAAM,GAtB0F,CAC/JX,OAAQ,EAAE,CACVyB,MAAO,EAAE,CACTnC,MAAO,EAAE,AACb,CAoBA,QAAO6C,AAxBmC,IAAI,CAwB7BI,YAAY,CAACd,KAAK,CACnCU,AAzB0C,IAAI,CAyBpCI,YAAY,CAACjD,KAAK,CAAGA,EAC/B6C,AA1B0C,IAAI,CA0BpCI,YAAY,CAACiB,cAAc,CAAGA,EACxC,IAAMjE,EAAgB4C,AA3BoB,IAAI,CA2Bd5C,aAAa,EAAI,CAAC,IAAI,CAAEmE,EAAe,CAAC,EAexE,GAbAD,EAAcnE,KAAK,CACdgB,OAAO,CAAC,AAACN,IACVT,EAAce,OAAO,CAAC,CAAChD,EAAKyD,KACxB,IAAM4C,EAASD,CAAY,CAACpG,EAAI,EAAI,EAAE,CACtCqG,EAAOzD,IAAI,CAAClB,EAAQgB,GAAUA,CAAM,CAACe,EAAM,CAAGf,GACzC0D,CAAY,CAACpG,EAAI,EAClBoG,CAAAA,CAAY,CAACpG,EAAI,CAAGqG,CAAK,CAEjC,EACJ,GAIIT,GACA,CAACf,AA3CqC,IAAI,CA2C/ByB,cAAc,EACzBzB,AA5CsC,IAAI,CA4ChC0B,OAAO,EACjB1B,AA7CsC,IAAI,CA6ChCc,MAAM,EAEhB,GAAId,AA/CkC,IAAI,CA+C5B2B,OAAO,CAAE,CACf3B,AAhD8B,IAAI,CAgDxBQ,KAAK,GACfW,EAAMnB,AAjDwB,IAAI,CAiDlBQ,KAAK,CAACW,GAAG,CACzBC,EAAMpB,AAlDwB,IAAI,CAkDlBQ,KAAK,CAACY,GAAG,EAE7B,IAAMQ,EAAc5B,AApDc,IAAI,CAoDR6B,QAAQ,CAACxE,EAAO8D,EAAKC,GAC7CU,EAAO,CAAC,OAAS9B,AArDW,IAAI,CAqDL5C,aAAa,EAAI,CAAC,IAAI,CAAE,CACzD,IAAK,IAAIO,EAAI,EAAGA,EAAKiE,CAAAA,EAAYrE,QAAQ,EAAEK,UAAY,CAAA,EAAID,IAAK,CAC5D,IAAME,EAASiE,EAAKpE,GAAG,CAAC,AAACvC,GAAQ,IAAI,CAACqC,SAAS,CAACrC,EAAI,CAACwC,EAAE,EAAI,GAC3DiD,EAAkB7C,IAAI,CAACF,EAC3B,CACA,IAAMkE,EAAiB/B,AA1DW,IAAI,CA0DLxC,SAAS,CAAC,KAC3CyD,EAAqBK,EAAchC,KAAK,CAAC0C,OAAO,CAACD,CAAc,CAAC,EAAE,EAClEb,EAAoBI,EAAchC,KAAK,CAAC0C,OAAO,CAACD,CAAc,CAACA,EAAetC,MAAM,CAAG,EAAE,EAE9D,KAAvBwB,GACAC,IAAsBI,EAAchC,KAAK,CAACG,MAAM,CAAG,GAC/CmB,CAAiB,CAAC,EAAE,CAAC,EAAE,GAAKC,CAAO,CAAC,EAAE,CAACoB,CAAC,EACxCrB,EAAkBsB,KAAK,GAG/BlC,AApEkC,IAAI,CAoE5BmC,UAAU,CAACvB,EACzB,KACSZ,CAAAA,AAtE6B,IAAI,CAsEvBoC,eAAe,EAE9Bd,EAAchC,KAAK,CAACG,MAAM,GAAKsB,EAAgB,GAC3CO,EAAchC,KAAK,CAACG,MAAM,GAAKsB,EAAgB,CAAA,IACnDC,EAAgB,CAAA,EAChBhB,AA3EkC,IAAI,CA2E5BmC,UAAU,CAACb,EAAczD,MAAM,GAG7CmD,IACA3D,EAAMgF,UAAU,CAAC,CACb,GAAGd,CAAY,CACfU,EAAGX,EAAchC,KAAK,AAC1B,GACAU,AAnFsC,IAAI,CAmFhCnB,OAAO,CAACyD,IAAI,CAAGhB,EAAczD,MAAM,EAE7CmC,AArFsC,IAAI,CAqFhCO,WAAW,CAACC,KAAK,EAC3BR,AAtFsC,IAAI,CAsFhCxC,SAAS,CAAC,IAAK,CAAA,GAAMiC,MAAM,GACrCO,AAvFsC,IAAI,CAuFhCuC,OAAO,CAAG,CAAA,EACpBvC,AAxFsC,IAAI,CAwFhCwC,MAAM,IAEpBxC,AA1F0C,IAAI,CA0FpCyC,WAAW,CAAG,CAAC,CAACzC,AA1FgB,IAAI,CA0FV0C,YAAY,CAACjD,MAAM,CACvD/C,EA3F0C,IAAI,CA2FzB,cACzB,CAIAiG,aAAc,CACV,IAAqBC,EAAgB3F,AAAtB,IAAI,CAAyB4B,OAAO,CAAC+D,aAAa,CAAExC,EAAenD,AAAnE,IAAI,CAAsEmD,YAAY,CACrG,KAAK,CAACuC,YAAYtE,KAAK,CADR,IAAI,CACaC,WAC5BrB,AAFW,IAAI,CAER4F,UAAU,EACjBzC,GACAA,EAAayC,UAAU,EACvBzC,EAAayC,UAAU,CAACC,YAAY,EACpCF,GACA3F,CAAAA,AAPW,IAAI,CAOR4F,UAAU,CAACC,YAAY,CAC1B1C,EAAayC,UAAU,CAACC,YAAY,AAAD,CAG/C,CACJ,CAkCA9E,EAAa+E,cAAc,CAAGjG,EAAMP,EAAWwG,cAAc,CAAE,CAQ3DtE,KAAM,KAAK,EACXuE,QAAS,CAILC,cAAe,CACnB,EAOA5C,SAAU,KAAK,EAWfuC,cAAe,CAAA,EAIfpE,OAAQ,CAMJI,MAAO,EAMPQ,OAAQ,EACZ,CACJ,GACAxC,EAAOoB,EAAarC,SAAS,CAAE,CAC3B4E,YAAa,CACTR,MAAO,MACX,EACAmD,eAAgB,CAAA,EAChBxE,eAAgB,CAAC,SAAS,CAC1BI,aAAc,EAAE,CAChBqE,sBAAuB,CAAA,CAC3B,GACA9G,IAA4D+G,kBAAkB,CAAC,MAAOpF,GAmCtF,GAAM,CAAEqF,IAAKC,CAAyB,CAAE,CAAG,AAACjH,IAA6DG,WAAW,CAE9G,CAAE+G,aAAAA,CAAY,CAAE1G,QAAS2G,CAAoB,CAAE1G,MAAO2G,CAAkB,CAAE,CAAI1H,GAepF,OAAM2H,UAAqBJ,EAMvBK,uBAAuBvE,CAAM,CAAER,CAAK,CAAEW,CAAI,CAAE,CACxC,IAAIM,EAAM,EAAGlC,EAAI,EAAGiG,EAAI,EACxB,KAAOjG,EAAIyB,GAEPS,GADIjB,EAAQ,EAAIW,CAAI,CAAC5B,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAExCjB,IAEJ,OAAOkC,CACX,CACAgE,aAAaxE,CAAI,CAAEE,CAAI,CAAE5B,CAAC,CAAEmG,CAAU,CAAEC,CAAM,CAAEnF,CAAK,CAAEc,CAAG,CAAE,CACxD,IAAMuC,EAAI5C,CAAI,CAAC1B,EAAI,EAAE,CAAEqG,EAASpF,EAAQ,EACpCW,CAAI,CAAC5B,EAAI,EAAE,CACX4B,CAAI,CAAC5B,EAAI,EAAE,CAACiB,EAAM,CAGtB,MAAO,CAACqD,EAHoB,AAAkB,KAAA,IAAX8B,EAC/BrE,EAAM6D,EAAa,AAACS,EAASF,EAC5BC,EAAU,CAAA,EAAID,CAAS,GACf,AACjB,CACA3E,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGqE,EAAa,EAAK1E,CAAAA,EAAS,CAAA,EAAI6E,EAAM,EAAE,CAAE3E,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACrK4G,EAAQG,EAAUvG,EAAGiB,EAAQ,GAAIiB,EAAM,EAAGH,EAAM,EAEpD,IAAIF,CAAAA,EAAUJ,CAAK,GAYnB,IARIoE,EAAqBjE,CAAI,CAAC,EAAE,GAC5BX,CAAAA,EAAQJ,EAAOI,KAAK,CAAGJ,EAAOI,KAAK,CAAG,CAAA,EAK1Cc,EAAMG,AAFA,IAAI,CAAC8D,sBAAsB,CAACvE,EAAQR,EAAOW,GAErCH,EAEPzB,EAAIyB,EAAQzB,EAAI6B,EAAU,EAAG7B,IAC9BuG,EAAW,IAAI,CAACL,YAAY,CAACxE,EAAME,EAAM5B,EAAGmG,EAAYC,EAAQnF,EAAOc,GACvEuE,EAAIlG,IAAI,CAACmG,GACT5E,EAAMvB,IAAI,CAACmG,CAAQ,CAAC,EAAE,EACtB/G,EAAMY,IAAI,CAACmG,CAAQ,CAAC,EAAE,EACtBH,EAASG,CAAQ,CAAC,EAAE,CAExB,MAAO,CACHrG,OAAQoG,EACR3E,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAmBAuG,EAAaX,cAAc,CAAGU,EAAmBH,EAA0BP,cAAc,CAAE,CACvFvE,OAAQ,CAWJI,MAAO,EACPQ,OAAQ,CACZ,CACJ,GACA/C,IAA4D+G,kBAAkB,CAAC,MAAOM,GAkCtF,GAAM,CAAEL,IAAKc,CAAwB,CAAE,CAAG,AAAC9H,IAA6DG,WAAW,CAE7G,CAAEG,MAAOyH,CAAiB,CAAExH,OAAQyH,CAAkB,CAAEvH,MAAOwH,CAAiB,CAAE,CAAIvI,GAe5F,OAAMwI,UAAoBJ,EAMtB,OAAOK,gBAAgBnF,CAAI,CAAEE,CAAI,CAAEkF,CAAU,CAAE9G,CAAC,CAEhD+G,CAAO,CAAE,CACL,IAAMC,EAAOpF,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAEiH,EAAMrF,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAEkH,EAAQtF,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAEmH,EAASL,CAAU,CAAC9G,EAAE,CAAEoH,EAAMF,IAAUF,GAAQE,IAAUD,GAAOD,IAASC,EACrI,EACA,AAAE,CAAA,EAAIC,EAAQD,EAAMD,CAAG,EAAMA,CAAAA,EAAOC,CAAE,EAAME,EAChD,MAAO,CADuDzF,CAAI,CAAC1B,EAAE,CACxDoH,EAAI,AACrB,CAMA5F,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IACIwG,EAAKrH,EAAGsH,EADN7F,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAE+H,EAAiB1G,EAAO0G,cAAc,CAAEC,EAAelI,EAAO8C,KAAK,CAACvE,GAAG,CAAC0J,GAAiBT,EAAaU,GAAc3H,UAAU,KAAMgC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG2F,EAAK,EAAE,CAAE9F,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAE7Q,GAAIkC,CAAAA,CAAAA,EAAKI,MAAM,EAAIL,CAAK,IACpBI,GACAD,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAGlB,GAAI,CAAC0F,EAAc,CACff,EAAkB,UACdc,EACA,sCAAuC,CAAA,EAAMjI,EAAO8C,KAAK,EAC7D,MACJ,CAGA,IAAKpC,EAAIyB,EAAQzB,EAAI6B,EAAS7B,IAC1BqH,EAAMI,EAAG3F,MAAM,CACfwF,EAAUV,EAAYC,eAAe,CAACnF,EAAME,EAAMkF,EAAY9G,EAAGyB,GAC7D4F,EAAM,GACNC,CAAAA,CAAO,CAAC,EAAE,EAAIG,CAAE,CAACJ,EAAM,EAAE,CAAC,EAAE,AAAD,EAE/BI,EAAGrH,IAAI,CAACkH,GACR3F,EAAMvB,IAAI,CAACkH,CAAO,CAAC,EAAE,EACrB9H,EAAMY,IAAI,CAACkH,CAAO,CAAC,EAAE,EAEzB,MAAO,CACHpH,OAAQuH,EACR9F,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAoBAoH,EAAYxB,cAAc,CAAGuB,EAAkBH,EAAyBpB,cAAc,CAAE,CAIpFvE,OAAQ,CACJI,MAAO,KAAK,EAQZsG,eAAgB,QACpB,CACJ,GACAb,EAAmBE,EAAY5I,SAAS,CAAE,CACtC+C,eAAgB,CAAA,EAChBK,SAAU,2BACd,GACA1C,IAA4D+G,kBAAkB,CAAC,KAAMmB,GAoCrF,GAAM,CAAEc,KAAAA,CAAI,CAAE,CAAItJ,IAEZ,CAAEyF,OAAQ,CAAE7F,UAAW2J,CAAW,CAAE,CAAEjC,IAAKkC,CAAwB,CAAE,CAAG,AAAClJ,IAA6DG,WAAW,CAEjJ,CAAEI,OAAQ4I,CAAkB,CAAE1I,MAAO2I,CAAiB,CAAElC,aAAcmC,CAAwB,CAAE7I,QAAS8I,CAAmB,CAAE,CAAI5J,GAexI,OAAM6J,UAAoBL,EAMtBM,WAAY,CACR,IACIlI,EADoBkB,EAAUmB,AAAhB,IAAI,CAAsBnB,OAAO,CAAEiC,EAASd,AAA5C,IAAI,CAAkDc,MAAM,CAAEgF,EAAY9F,AAA1E,IAAI,CAAgF+F,WAAW,CAACC,KAAK,CAAEC,EAAgBpH,EAAQqH,eAAe,CAAEC,EAAgBtH,EAAQuH,aAAa,CAAEC,EAAavF,CAAM,CAAC,EAAE,CAE/N,GAAI,CAACgF,GAAaO,EAEd,IAAK1I,EAAI,EADT0I,EAAWL,KAAK,CAAGC,EACPtI,EAAImD,EAAOrB,MAAM,CAAE9B,IACvBmD,CAAM,CAACnD,EAAE,CAACiG,CAAC,CAAG9C,CAAM,CAACnD,EAAI,EAAE,CAACiG,CAAC,CAC7B9C,CAAM,CAACnD,EAAE,CAACqI,KAAK,CAAGC,EAEbnF,CAAM,CAACnD,EAAE,CAACiG,CAAC,CAAG9C,CAAM,CAACnD,EAAI,EAAE,CAACiG,CAAC,CAClC9C,CAAM,CAACnD,EAAE,CAACqI,KAAK,CAAGG,EAGlBrF,CAAM,CAACnD,EAAE,CAACqI,KAAK,CAAGlF,CAAM,CAACnD,EAAI,EAAE,CAACqI,KAAK,AAIrD,CACA7G,UAAUlC,CAAM,CAAE,CACd,IAAwCoC,EAAOpC,EAAOqC,KAAK,EAAI,EAAE,CAAEC,EAAOtC,EAAOE,KAAK,EAAI,EAAE,CAAEqC,EAAUD,EAAKE,MAAM,CAAE6G,EAAK,EAAE,CAC5HhH,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAGtBoJ,EAASC,EAAgBC,EAAeC,EAAO/I,EAAGgJ,EAAGC,EAAU,EAAGC,EAAW,EAC7E,GAAIxH,CAAAA,CAAAA,EAAKI,MAAM,EALqB,EAKR,GACvBkG,EAAoBpG,CAAI,CAAC,EAAE,GAC5BA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAGlB,IAAK9B,EAAI,EAAGA,EAAImJ,GAAgBnJ,IAC5B+I,EAAQ,AAACnH,CAAAA,CAAI,CAAC5B,EAAE,CAVW,EAUL,CAAG4B,CAAI,CAAC5B,EAAE,CAVI,EAUC,AAAD,EAAK,EACrCA,GAAKmJ,IACLD,CAAAA,EAAWnB,EAAyBmB,EAAWH,EAAK,EAExDE,EAAUlB,EAAyBkB,EAAUF,GAEjD,IAAKC,EAAIG,GAAgBH,EAAInH,EAASmH,IAElCE,EAAWnB,EAAyBmB,EADpCH,CAAAA,EAAQ,AAACnH,CAAAA,CAAI,CAACoH,EAAE,CAjBW,EAiBL,CAAGpH,CAAI,CAACoH,EAAE,CAjBI,EAiBC,AAAD,EAAK,CAAA,GAEzCC,EAAUlB,EAAyBkB,EAAUF,GAG7CH,EAAUb,EAAyBqB,AAFxBF,EArBK,EAsBND,EAtBsB,IAwBhCN,EAAGvI,IAAI,CAAC,CAACsB,CAAI,CAACsH,EAAE,CAAEJ,EAAQ,EAC1BjH,EAAMvB,IAAI,CAACsB,CAAI,CAACsH,EAAE,EAClBxJ,EAAMY,IAAI,CAACwI,GACXC,EAAiBG,EAAI,EA3BL,EA4BhBF,EAAgBE,EAAI,EA5BY,GA6BhCE,EAAWnB,EAAyBmB,EAChC,AAACtH,CAAAA,CAAI,CAACiH,EAAe,CA7BE,EA6BI,CACvBjH,CAAI,CAACiH,EAAe,CA9BQ,EA8BH,AAAD,EAAK,GACrCI,EAAUlB,EAAyBkB,EAC/B,AAACrH,CAAAA,CAAI,CAACkH,EAAc,CAhCG,EAgCG,CACtBlH,CAAI,CAACkH,EAAc,CAjCS,EAiCJ,AAAD,EAAK,GAExC,MAAO,CACH5I,OAAQyI,EACRhH,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAuBAyI,EAAY7C,cAAc,CAAG0C,EAAkBF,EAAyBxC,cAAc,CAAE,CACpFvE,OAAQ,CAEJI,MAAO,KAAK,EACZQ,OAAQ,KAAK,CACjB,EAYA8G,gBAAiB,UAYjBE,cAAe,UACfY,UAAW,EACXC,aAAc,GACdC,aAAc,GACdC,MAAO,CAAA,EACPC,OAAQ,CACJC,MAAO,CACHC,KAAM,CACFC,KAAM,CACV,CACJ,CACJ,CACJ,GACA/B,EAAmBI,EAAYjK,SAAS,CAAE,CACtCoD,SAAU,KACVL,eAAgB,KAAK,EAErB8I,cAAenC,EACfoC,iBAAkBnC,EAAYmC,gBAAgB,CAC9CC,SAAUpC,EAAYoC,QAAQ,CAC9BC,UAAWrC,EAAYqC,SAAS,CAChCC,WAAYtC,EAAYsC,UAAU,AACtC,GACAvL,IAA4D+G,kBAAkB,CAAC,KAAMwC,GAwCrF,GAAM,CAAEvC,IAAK,CAAE1H,UAAWkM,CAAQ,CAAE,CAAE,CAAG,AAACxL,IAA6DG,WAAW,CAE5G,CAAEsL,QAAAA,CAAO,CAAEnL,MAAOoL,CAA8B,CAAEjL,MAAOkL,CAA8B,CAAE,CAAIjM,KAOnG,AAAC,SAAUtB,CAAwB,EAoB/B,IAAMwN,EAAgB,CAAC,aAAa,CAW9B7K,EAAgB,CAAC,MAAO,SAAS,CAUjC8K,EAAiB,CAAC,MAAM,CA8C9B,SAASC,EAAYC,CAAY,EAC7B,MAAQ,OACJA,EAAaC,MAAM,CAAC,GAAGpJ,WAAW,GAClCmJ,EAAaE,KAAK,CAAC,EAC3B,CAUA,SAASC,EAAwBvI,CAAS,CAAEwI,CAAa,EACrD,IAAMC,EAAkB,EAAE,CAM1B,MALA,AAACzI,CAAAA,EAAU5C,aAAa,EAAI,EAAE,AAAD,EAAGe,OAAO,CAAC,AAACiK,IACjCA,IAAiBI,GACjBC,EAAgB1K,IAAI,CAACoK,EAAYC,GAEzC,GACOK,CACX,CAMA,SAASC,IACL,IAAM1I,EAAY,IAAI,CAAE2I,EAAc3I,EAAU2I,WAAW,CAAEV,EAAgBjI,EAAUiI,aAAa,CAAEC,EAAiBlI,EAAUkI,cAAc,CAAEU,EAAiB5I,EAAUc,MAAM,CAAE+H,EAAkB7I,EAAUnB,OAAO,CAAEiK,EAAe9I,EAAU+I,KAAK,CAAEC,EAAe,CACpQnK,QAAS,CACLoK,QAASJ,EAAgBI,OAAO,AACpC,CACJ,EAEAC,EAAiB,EAAE,CAAEC,EAAsBZ,EAAwBvI,EAAW2I,GAC1ES,EAAeR,EAAenJ,MAAM,CAAE4J,EAiB1C,GAfAF,EAAoBhL,OAAO,CAAC,CAACmL,EAAU1K,KAGnC,IADAsK,CAAc,CAACtK,EAAM,CAAG,EAAE,CACnBwK,KACHC,EAAQT,CAAc,CAACQ,EAAa,CACpCF,CAAc,CAACtK,EAAM,CAACb,IAAI,CAAC,CACvBkE,EAAGoH,EAAMpH,CAAC,CACVsH,MAAOF,EAAME,KAAK,CAClBC,MAAOH,CAAK,CAACC,EAAS,CACtBG,OAAQ,CAAC3B,EAAQuB,CAAK,CAACC,EAAS,CACpC,GAEJF,EAAeR,EAAenJ,MAAM,AACxC,GAEIO,EAAU+F,WAAW,CAAC2D,SAAS,EAAIxB,EAAezI,MAAM,CAAE,CAC1D,IAA2EkK,EAAmBT,CAAc,CAA9FC,EAAoBnH,OAAO,CAACmG,EAAYD,CAAc,CAAC,EAAE,GAA4C,CAAE0B,EAAkB1B,AAA0B,IAA1BA,EAAezI,MAAM,CACxJmJ,EACAM,CAAc,CAACC,EAAoBnH,OAAO,CAACmG,EAAYD,CAAc,CAAC,EAAE,GAAG,CAAE2B,EAAgB7J,EAAUgG,KAAK,AAChHhG,CAAAA,EAAUc,MAAM,CAAG8I,EACnB5J,EAAU8J,UAAU,CAAGH,EACvB3J,EAAUgG,KAAK,CAAGhG,EAAU+F,WAAW,CAAC2D,SAAS,CACjD1J,EAAUnB,OAAO,CAAGmJ,EAA+BY,EAAgBI,GACnEhJ,EAAU+I,KAAK,CAAG/I,EAAU+J,IAAI,CAChC/J,EAAUgK,SAAS,CAAG,CAAA,EACtBnC,EAAShC,SAAS,CAAChK,IAAI,CAACmE,GACxBA,EAAU+J,IAAI,CAAG/J,EAAU+I,KAAK,CAEhC,OAAO/I,EAAU8J,UAAU,CAC3B,OAAO9J,EAAUgK,SAAS,CAC1BhK,EAAUgG,KAAK,CAAG6D,CACtB,CAEA5B,EAAc9J,OAAO,CAAC,CAAC8L,EAAUtM,KACzBuL,CAAc,CAACvL,EAAE,EACjBqC,EAAUc,MAAM,CAAGoI,CAAc,CAACvL,EAAE,CAChCkL,CAAe,CAACoB,EAAS,CACzBjK,EAAUnB,OAAO,CAAGmJ,EAA+Ba,CAAe,CAACoB,EAAS,CAACC,MAAM,CAAElB,GAGrFjB,EAA+B,uBAAyBkC,EAAzB,gGAInCjK,EAAU+I,KAAK,CAAG/I,CAAS,CAAC,QAAUiK,EAAS,CAC/CpC,EAAShC,SAAS,CAAChK,IAAI,CAACmE,GAExBA,CAAS,CAAC,QAAUiK,EAAS,CAAGjK,EAAU+I,KAAK,EAG/ChB,EAA+B,WAAakC,EAAb,4GAIvC,GAEAjK,EAAUc,MAAM,CAAG8H,EACnB5I,EAAUnB,OAAO,CAAGgK,EACpB7I,EAAU+I,KAAK,CAAGD,EAClBjB,EAAShC,SAAS,CAAChK,IAAI,CAACmE,EAC5B,CAQA,SAASmK,EAAsBrJ,CAAM,EACjC,IAAIsJ,EAAUC,EAAO,EAAE,CAAEC,EAAiB,EAAE,CAG5C,GAFAxJ,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1B,IAAI,CAACkJ,SAAS,EAAI,IAAI,CAACF,UAAU,CAEjC,CAAA,GAAIM,AADJA,CAAAA,EAAWvC,EAAS0C,YAAY,CAAC1O,IAAI,CAAC,IAAI,CAAE,IAAI,CAACiO,UAAU,CAAA,GAC3CM,EAAS3K,MAAM,CAAE,CAC7B2K,CAAQ,CAAC,EAAE,CAAC,EAAE,CAAG,IACjBC,EAAOxC,EAAS0C,YAAY,CAAC1O,IAAI,CAAC,IAAI,CAAEiF,GACxCwJ,EAAiBF,EAAS9B,KAAK,CAAC,EAAG+B,EAAK5K,MAAM,EAE9C,IAAK,IAAI9B,EAAI2M,EAAe7K,MAAM,CAAG,EAAG9B,GAAK,EAAGA,IAC5C0M,EAAKtM,IAAI,CAACuM,CAAc,CAAC3M,EAAE,CAEnC,CAAA,MAGA0M,EAAOxC,EAAS0C,YAAY,CAAClM,KAAK,CAAC,IAAI,CAAEC,WAE7C,OAAO+L,CACX,CAQA,SAASG,EAAiBnB,CAAK,EAC3B,IAAMoB,EAAY,EAAE,CAIpB,MAHA,AAAC,CAAA,IAAI,CAACrN,aAAa,EAAI,EAAE,AAAD,EAAGe,OAAO,CAAC,AAACiK,IAChCqC,EAAU1M,IAAI,CAACsL,CAAK,CAACjB,EAAa,CACtC,GACOqC,CACX,CAMA,SAASC,IACL,IAAMtN,EAAgB,IAAI,CAACA,aAAa,CACpCuN,EAAa,EAAE,CAAEC,EACrBD,EAAapC,EAAwB,IAAI,EACzCV,EAASF,SAAS,CAACtJ,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACwC,MAAM,CAAC3C,OAAO,CAAC,AAACkL,IACjBjM,EAAce,OAAO,CAAC,CAACiK,EAAczK,KACjCiN,EAAQvB,CAAK,CAACjB,EAAa,CAGvB,IAAI,CAACvF,UAAU,EACf+H,CAAAA,EAAQ,IAAI,CAAC/H,UAAU,CAACgI,WAAW,CAACD,EAAK,EAE/B,OAAVA,GACAvB,CAAAA,CAAK,CAACsB,CAAU,CAAChN,EAAE,CAAC,CAAG,IAAI,CAACmN,KAAK,CAACC,QAAQ,CAACH,EAAO,CAAA,EAAI,CAE9D,EACJ,EACJ,CA3KAnQ,EAAyBuQ,OAAO,CAhBhC,SAAiBC,CAAc,EAC3B,IAAMC,EAAQD,EAAetP,SAAS,CAatC,OAZAuP,EAAMjD,aAAa,CAAIiD,EAAMjD,aAAa,EACtCA,EAAcK,KAAK,GACvB4C,EAAM9N,aAAa,CAAI8N,EAAM9N,aAAa,EACtCA,EAAckL,KAAK,GACvB4C,EAAMvC,WAAW,CAAIuC,EAAMvC,WAAW,EAtBtB,MAwBhBuC,EAAMhD,cAAc,CAAIgD,EAAMhD,cAAc,EACxCA,EAAeI,KAAK,GACxB4C,EAAMrF,SAAS,CAAG6C,EAClBwC,EAAMX,YAAY,CAAGJ,EACrBe,EAAMC,OAAO,CAAGX,EAChBU,EAAMvD,SAAS,CAAG+C,EACXO,CACX,CA6KJ,EAAGxQ,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GAM/B,IAAM2Q,EAAuC3Q,EAapE,CAAE4I,IAAKgI,CAA2B,CAAE,CAAG,AAAChP,IAA6DG,WAAW,CAEhH,CAAEI,OAAQ0O,CAAqB,CAAExO,MAAOyO,CAAoB,CAAExO,KAAMyO,CAAmB,CAAE,CAAIzP,IAWnG,SAAS0P,EAAuBC,CAAG,CAAEC,CAAO,EACxC,IAAIC,EAAeF,CAAG,CAAC,EAAE,CAAEG,EAAa,EAAGlO,EAC3C,IAAKA,EAAI,EAAGA,EAAI+N,EAAIjM,MAAM,CAAE9B,IACpBgO,CAAAA,AAAY,QAAZA,GAAqBD,CAAG,CAAC/N,EAAE,EAAIiO,GAC/BD,AAAY,QAAZA,GAAqBD,CAAG,CAAC/N,EAAE,EAAIiO,CAAW,IAC1CA,EAAeF,CAAG,CAAC/N,EAAE,CACrBkO,EAAalO,GAGrB,OAAOkO,CACX,CAeA,MAAMC,UAAuBT,EAMzBlM,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAGIuN,EAASC,EAAWC,EAAatO,EAAGuO,EAHlC9M,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAEvG0M,EAAK,EAAE,CAAE7M,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAK/B,IAAKQ,EAAIyB,EAAS,EAAGzB,EAAI6B,EAAS7B,IAE9BsO,EAAOR,EAAuBS,AAD9BA,CAAAA,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAS,EAAGzB,EAAI,EAAC,EACJD,GAAG,CAAC,SAAU0O,CAAI,EACpD,OAAOZ,EAAoBY,CAAI,CARA,EAQK,CAAEA,EAC1C,GAAI,OAIJL,EAAU,AAHFN,EAAuBS,EAAQxO,GAAG,CAAC,SAAU0O,CAAI,EACrD,OAAOZ,EAAoBY,CAAI,CAXU,EAWJ,CAAEA,EAC3C,GAAI,OACehN,EAAU,IAC7B4M,EAAY,AAACC,EAAO7M,EAAU,IAC1BC,CAAI,CAAC1B,EAAI,EAAE,GACXwO,EAAGpO,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAI,EAAE,CAAEoO,EAASC,EAAU,EACzC1M,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAI,EAAE,EACtBR,EAAMY,IAAI,CAAC,CAACgO,EAASC,EAAU,GAGvC,MAAO,CACHnO,OAAQsO,EACR7M,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAwBA2O,EAAe/I,cAAc,CAAGwI,EAAqBF,EAA4BtI,cAAc,CAAE,CAM7FvE,OAAQ,CACJI,MAAO,KAAK,EACZQ,OAAQ,EACZ,EACAiN,OAAQ,CACJC,QAAS,CAAA,CACb,EACAtJ,QAAS,CACLuJ,YAAa,gIACjB,EAIAP,UAAW,CAIP9B,OAAQ,CAIJsC,UAAW,EAOXC,UAAW,KAAK,CACpB,CACJ,EACAC,aAAc,CACVC,cAAe,UACnB,CACJ,GACArB,EAAsBQ,EAAenQ,SAAS,CAAE,CAC5CuM,eAAgB,EAAE,CAClBD,cAAe,CAAC,YAAY,CAC5BlJ,SAAU,QACV3B,cAAe,CAAC,IAAK,YAAY,CACjCuL,YAAa,GACjB,GACAyC,EAAoCJ,OAAO,CAACc,GAC5CzP,IAA4D+G,kBAAkB,CAAC,QAAS0I,GAuCxF,GAAM,CAAEc,MAAOC,CAAuC,CAAE,CAAG,AAACxQ,IAA6DG,WAAW,CAE9H,CAAEI,OAAQkQ,CAA+B,CAAEhQ,MAAOiQ,CAA8B,CAAE,CAAIhR,GAe5F,OAAMiR,UAAiCH,EAMnC1N,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CAEtB,IACwByO,EAAYtP,EAD9BuP,EAAM,EAAE,CAAE5N,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAEhCyP,EAAQ,KAAK,CAACzN,UAAUtD,IAAI,CAAC,IAAI,CAAEoB,EAAQuB,GACjD,IAAKb,EAAI,EAAGA,EAAIiP,EAAMzP,KAAK,CAACsC,MAAM,CAAE9B,IAGhCsP,EAAalB,AAFHa,EAAMzP,KAAK,CAACQ,EAAE,CAAC,EAAE,CACfiP,EAAMzP,KAAK,CAACQ,EAAE,CAAC,EAAE,CAE7BuP,EAAInP,IAAI,CAAC,CAAC6O,EAAMtN,KAAK,CAAC3B,EAAE,CAAEsP,EAAW,EACrC3N,EAAMvB,IAAI,CAAC6O,EAAMtN,KAAK,CAAC3B,EAAE,EACzBR,EAAMY,IAAI,CAACkP,GAEf,MAAO,CACHpP,OAAQqP,EACR5N,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CA0BA6P,EAAyBjK,cAAc,CAAGgK,EAA+BF,EAAwC9J,cAAc,CAAE,CAC7HC,QAAS,CACLuJ,YAAa,4EACjB,CACJ,GACAO,EAAgCE,EAAyBrR,SAAS,CAAE,CAChEoD,SAAU,mBACVkJ,cAAe,EAAE,CACjB7K,cAAe,CAAC,IAAI,CACpBuL,YAAa,GACjB,GACAyC,EAAoCJ,OAAO,CAAC6B,GAC5CxQ,IAA4D+G,kBAAkB,CAAC,kBAAmB4J,GAwClG,GAAM,CAAE3J,IAAK8J,CAAyB,CAAE,CAAG,AAAC9Q,IAA6DG,WAAW,CAE9G,CAAEK,QAASuQ,EAAoB,CAAEtQ,MAAOuQ,EAAkB,CAAE,CAAItR,IAiBtE,SAASuR,GAAMC,CAAY,CAAEC,CAAS,EAClC,IAAgDC,EAAKC,AAAtCH,CAA4C,CAAC,EAAE,CAAGG,AAAlDH,CAAwD,CAAC,EAAE,CAC1E,OADmOI,KAAKvM,GAAG,CAACqM,EAA1J,AAAiB,KAAA,IAA9DD,EAA4E,EAAIG,KAAKC,GAAG,CAACF,AAA/GH,CAAqH,CAAC,EAAE,CAAGM,AAArGL,CAA0G,CAAC,EAAE,EAAS,AAAiB,KAAA,IAAvIA,EAAqJ,EAAIG,KAAKC,GAAG,CAACF,AAAxLH,CAA8L,CAAC,EAAE,CAAGM,AAA9KL,CAAmL,CAAC,EAAE,EAE/N,CAsBA,MAAMM,WAAqBX,EAMvBhO,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAuCqB,EAAS,CAAC,CAArCzB,CAAI,CAAC,EAAE,CAAWE,CAAI,CAAC,EAAE,CAA4B,CAAC,CAAEwO,EAAM,EAAE,CAAEzO,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACvMkM,EAAO1L,EAAGqQ,EAAU,EAAGrO,EAAQ,EAAGsO,EAAK,EAC3C,GAAI,CAAC5O,CAAAA,EAAKI,MAAM,EAAIL,CAAK,GACpBgO,GAAqB7N,CAAI,CAAC,EAAE,GAC7BA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAGlB,IAAK9B,EAAI,EAAGA,GAAK6B,EAAS7B,SAhCTmD,EAAQzB,EAAME,EAAM5B,EAAGyB,EAAQ4O,GAiC5CE,AA/CZ,SAA2BpN,CAAM,CAAEzB,CAAI,CAAEE,CAAI,CAAE5B,CAAC,EAC5C,IAAMwQ,EAAS9O,CAAI,CAAC1B,EAAE,CAAEqG,EAASzE,CAAI,CAAC5B,EAAE,CACxCmD,EAAO/C,IAAI,CAAC,CAACoQ,EAAQnK,EAAO,CAChC,EA4C8BlD,EAAQzB,EAAME,EAAM5B,GAClCyB,EAASO,GAETqO,EAAU3E,CApCDvI,EAmCeA,EAnCPzB,EAmCeA,EAnCTE,EAmCeA,EAnCT5B,EAmCeA,EAnCZyB,EAmCeA,EAnCP4O,EAmCeA,EAAvD3E,EAjCL,CADGhK,CAAI,CAAC1B,EAAI,EAAE,CAA6C,AAAC,CAAA,AAACqQ,EAAW5O,CAAAA,EAAS,CAAA,EAA5DkO,GAAM/N,CAAI,CAAC5B,EAAI,EAAE,CAAE4B,CAAI,CAAC5B,EAAI,EAAE,CAAqC,EAAKyB,EACvF,CAkCc,CAAC,EAAE,CAClB2O,EAAIhQ,IAAI,CAACsL,GACT/J,EAAMvB,IAAI,CAACsL,CAAK,CAAC,EAAE,EACnBlM,EAAMY,IAAI,CAACsL,CAAK,CAAC,EAAE,IAEdjK,IAAWO,GAChBqO,EAAUC,EAAMtQ,CAAAA,EAAI,CAAA,EACpBoQ,EAAIhQ,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAI,EAAE,CAAEqQ,EAAQ,EAC/B1O,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAI,EAAE,EACtBR,EAAMY,IAAI,CAACiQ,IAIXC,GAAMX,GAAM/N,CAAI,CAAC5B,EAAI,EAAE,CAAE4B,CAAI,CAAC5B,EAAI,EAAE,EACpCgC,KAGR,MAAO,CACH9B,OAAQkQ,EACRzO,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAoBA2Q,GAAa/K,cAAc,CAAGsK,GAAmBF,EAA0BpK,cAAc,CAAE,CAIvFvE,OAAQ,CACJI,MAAO,KAAK,CAChB,CACJ,GACAvC,IAA4D+G,kBAAkB,CAAC,MAAO0K,IAqCtF,GAAM,CAAEzK,IAAK+K,EAAwB,CAAE,CAAG,AAAC/R,IAA6DG,WAAW,CAE7G,CAAEI,OAAQyR,EAAkB,CAAExR,QAASyR,EAAmB,CAAExR,MAAOyR,EAAiB,CAAE,CAAIxS,GAmChG,OAAMyS,WAAoBJ,GAMtBtO,MAAO,CACHzD,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACmE,IAAI,CAACzB,KAAK,CAAC,IAAI,CAAEC,WAEvG,IAAI,CAACO,OAAO,CAAG0P,GAAkB,CAC7BE,QAAS,CACLvE,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,EACA0I,WAAY,CACRxE,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,CACJ,EAAG,IAAI,CAACnH,OAAO,CACnB,CACAM,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAIImQ,EAAIC,EAAIC,EAAIC,EAAMC,EAAS7C,EAAS8C,EAAQ3F,EAAO1L,EAJjDyB,EAASZ,EAAOY,MAAM,CAAE6P,EAAoBzQ,EAAOyQ,iBAAiB,CAAE3P,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEkC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAE7KyP,EAAK,EAAE,CAGP,GAAI7P,EAAKI,MAAM,CAAGL,EACd,OAEJ,IAAM+P,EAASb,GAAoB/O,CAAI,CAAC,EAAE,EAC1C,IAAK5B,EAAIyB,EAAQzB,GAAK6B,EAAS7B,IAC3BoR,EAAU1P,EAAKiJ,KAAK,CAAC3K,EAAIyB,EAAQzB,GACjCuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAQzB,GAKjCmR,EAAOzF,AAJPA,CAAAA,EAAQhN,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACwD,SAAS,CAACtD,IAAI,CAAC,IAAI,CAAE,CAC/GyD,MAAOyP,EACP5R,MAAO+O,CACX,EAAG1N,EAAM,EACIc,KAAK,CAAC,EAAE,CACrBqP,EAAKtF,EAAMlM,KAAK,CAAC,EAAE,CACnB6R,EAASI,AAlErB,SAA8B1D,CAAG,CAAE9M,CAAK,CAAEuQ,CAAM,CAAEE,CAAI,EAClD,IAAMC,EAAS5D,EAAIjM,MAAM,CACrB9B,EAAI,EAAG4R,EAAM,EAAG3E,EAAO4E,EAAW,EACtC,KAAO7R,EAAI2R,EAAQ3R,IAEf6R,GAAY5E,AADZA,CAAAA,EAAQ,AAACuE,CAAAA,EAASzD,CAAG,CAAC/N,EAAE,CAACiB,EAAM,CAAG8M,CAAG,CAAC/N,EAAE,AAAD,EAAK0R,CAAG,EAC3BzE,EAIxB,OADM+C,KAAK8B,IAAI,CADfD,GAAuBF,EAAS,EAGpC,EAwD0CpD,EAAS1N,EAAOI,KAAK,CAAEuQ,EAAQR,GAC7DC,EAAKD,EAAKM,EAAoBD,EAC9BH,EAAKF,EAAKM,EAAoBD,EAC9BE,EAAGnR,IAAI,CAAC,CAAC+Q,EAAMF,EAAID,EAAIE,EAAG,EAC1BvP,EAAMvB,IAAI,CAAC+Q,GACX3R,EAAMY,IAAI,CAAC,CAAC6Q,EAAID,EAAIE,EAAG,EAE3B,MAAO,CACHhR,OAAQqR,EACR5P,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAoBAqR,GAAYzL,cAAc,CAAGwL,GAAkBH,GAAyBrL,cAAc,CAAE,CAcpFvE,OAAQ,CACJY,OAAQ,GAIR6P,kBAAmB,EACnBrQ,MAAO,CACX,EAIA8P,WAAY,CAIRxE,OAAQ,CAIJsC,UAAW,EAOXC,UAAW,KAAK,CACpB,CACJ,EAMAgC,QAAS,CAILvE,OAAQ,CAIJsC,UAAW,EAOXC,UAAW,KAAK,CACpB,CACJ,EACAzJ,QAAS,CACLuJ,YAAa,iJACjB,EACAF,OAAQ,CACJC,QAAS,CAAA,CACb,EACAI,aAAc,CACVC,cAAe,UACnB,CACJ,GACA0B,GAAmBG,GAAY7S,SAAS,CAAE,CACtCuM,eAAgB,CAAC,MAAO,SAAS,CACjCD,cAAe,CAAC,UAAW,aAAa,CACxCvJ,eAAgB,CAAC,SAAU,oBAAoB,CAC/CtB,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1CuL,YAAa,QACjB,GACAyC,EAAoCJ,OAAO,CAACwD,IAC5CnS,IAA4D+G,kBAAkB,CAAC,KAAMoL,IAmCrF,GAAM,CAAEnL,IAAKqM,EAAyB,CAAE,CAAG,AAACrT,IAA6DG,WAAW,CAE9G,CAAEK,QAAS8S,EAAoB,CAAE7S,MAAO8S,EAAkB,CAAE,CAAI7T,GAwCtE,OAAM8T,WAAqBH,GAMvBvQ,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGqQ,EAAK,EAAE,CAAEC,EAAM,EAAE,CAAEzQ,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAC/I6S,EAAUC,EAAGC,EAAW,EAAE,CAAElL,EAAKrF,EAAQ,EAAGwQ,EAAOC,EAAQC,EAAS1S,EAExE,GAAI0B,CAAAA,CAAAA,EAAKI,MAAM,EAAIL,CAAK,GACnBuQ,GAAqBpQ,CAAI,CAAC,EAAE,GAC7BA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAIlB,KAAOE,EAAQP,GACX6Q,EAAI1Q,CAAI,CAACI,EAAQ,EAAE,CACnBmQ,EAAG/R,IAAI,CAAC,AAACkS,CAAAA,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,AAAD,EAAK,GAC/BtQ,IAEJ,IAAKhC,EAAIyB,EAAQzB,GAAK6B,EAAS7B,IAE3ByS,EAAS,AAACH,CAAAA,AADVA,CAAAA,EAAI1Q,CAAI,CAAC5B,EAAI,EAAE,AAAD,CACH,CAAC,EAAE,CAAGsS,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,AAAD,EAAK,EAChCjL,EAAM8K,EAAG/R,IAAI,CAACqS,GAEdD,EAAQG,AAvDTC,AAsDCL,CAAAA,EAAWJ,EAAGxH,KAAK,CAACtD,EAAM5F,EAAM,EAtD3BoR,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,EACnC,OAAOD,EAAOC,CAClB,EAAG,GAqDkCtR,EAC7BiR,EAAUM,AAjDtB,SAAuBjF,CAAG,CAAErI,CAAG,EAC3B,IAAM2B,EAAM0G,EAAIjM,MAAM,CAClBI,EAAM,EAAGlC,EACb,IAAKA,EAAI,EAAGA,EAAIqH,EAAKrH,IACjBkC,GAAO8N,KAAKC,GAAG,CAACvK,EAAOqI,CAAG,CAAC/N,EAAE,EAEjC,OAAOkC,CACX,EA0CoCqQ,EAAUC,GAAS/Q,EAC3C4Q,EAAY,AAACI,CAAAA,EAASD,CAAI,EAAM,CAAA,KAAQE,CAAM,EAC9CN,EAAIhS,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAI,EAAE,CAAEqS,EAAS,EAChC1Q,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAI,EAAE,EACtBR,EAAMY,IAAI,CAACiS,GAEf,MAAO,CACHnS,OAAQkS,EACRzQ,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAoBA0S,GAAa9M,cAAc,CAAG6M,GAAmBF,GAA0B3M,cAAc,CAAE,CAIvFvE,OAAQ,CACJI,MAAO,KAAK,CAChB,CACJ,GACAvC,IAA4D+G,kBAAkB,CAAC,MAAOyM,IA0CtF,GAAM,CAAExM,IAAKuN,EAAyB,CAAE,CAAG,AAACvU,IAA6DG,WAAW,CAE9G,CAAEM,MAAO+T,EAAkB,CAAE,CAAI9U,GAevC,OAAM+U,WAAqBF,GACvBG,aAAc,CAMV,KAAK,IAAIzS,WACT,IAAI,CAACS,QAAQ,CAAG,oBACpB,CAcAiS,SAAU,CACN,IAAMjR,EAAQ,IAAI,CAACA,KAAK,CAAElB,EAAU,IAAI,CAACA,OAAO,CAAE5B,EAAS,IAAI,CAACmD,YAAY,CAAE+E,EAAgB,IAAI,CAACA,YAAY,EAC1G,CAAA,IAAI,CAACA,YAAY,CACdpF,EAAMvE,GAAG,CAACqD,EAAQL,MAAM,CAAC0G,cAAc,CAAA,EAAK+L,EAAgBhU,GAAQG,eAAeqC,SAAW,EAMtG,SAASyR,EAAcC,CAAK,EACxB,OAAOA,EAAM7T,SAAS,CAACM,QAAQ,EAC3BiB,EAAQL,MAAM,CAACY,MAAM,AAC7B,CACA,MAAO,CAAC,CAAEnC,CAAAA,GACNkI,GACA+L,EAAcjU,IACdiU,EAAc/L,IAAiB8L,CAAW,CAClD,CAUA9R,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,GAAK,IAAI,CAACwS,OAAO,GAGjB,OAAO,IAAI,CAACI,YAAY,CAACnU,EAAOqC,KAAK,CAAErC,EAAOE,KAAK,CAAE,IAAI,CAACgI,YAAY,CAAC3H,SAAS,CAAC,KAAMgB,EAAOY,MAAM,CACxG,CAmBAgS,aAAa9R,CAAK,CAAE+R,CAAW,CAAEC,CAAiB,CAAElS,CAAM,CAAE,CACxD,IAAM4F,EAAMqM,EAAY5R,MAAM,CAAE8R,EAAkB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAiB,EAAE,CAAE5T,EAAS,EAAE,CACvGF,EAAG0L,EAAOqI,EAAY,GAAIC,EAAY,EAAGC,EAAqB,EAgBlE,SAASC,EAAmBC,CAAI,CAAEhN,CAAM,EACpC,IAAMH,EAAOmN,CAAI,CAAC,EAAE,CAAElN,EAAMkN,CAAI,CAAC,EAAE,CAAEjN,EAAQiN,CAAI,CAAC,EAAE,CAmBpD,OAAOd,AAnByDlM,AAAW,OAAXA,GAC5DH,AAAS,OAATA,GACAC,AAAQ,OAARA,GACAC,AAAU,OAAVA,GACAF,IAASC,EAgBTmN,AAHQ,CAAA,AAG0BlN,EAALD,EAHVoN,CAAAA,AAGIrN,EAAWE,CAHXoN,CAAC,EAAMD,CAAAA,AAGPrN,EAAMC,CAHKsN,EAGSpN,EAC1C,CAAA,AAAC4M,EAAY/T,EAAI,IAAG,CAC7B,CACA,GAAIyB,EAAS,GAAKA,GAAU4F,EAAK,CAC7B,IAAKrH,EAAI,EAAGA,EAAIyB,EAAQzB,IACpB4T,CAAe,CAAC5T,EAAE,CAAGkU,EAAmBR,CAAW,CAAC1T,EAAE,CAAE2T,CAAiB,CAAC3T,EAAE,EAC5EgU,GAAaL,CAAiB,CAAC3T,EAAE,CACjCiU,GAAsBL,CAAe,CAAC5T,EAAE,CAO5C,IALA6T,EAAezT,IAAI,CAACuB,CAAK,CAAC3B,EAAI,EAAE,EAChC8T,EAAe1T,IAAI,CAACJ,EAAI+T,GAAatS,GAAUuS,AAAc,IAAdA,EAC3CC,EAAqBD,EACrB,MACJ9T,EAAOE,IAAI,CAAC,CAACyT,CAAc,CAAC,EAAE,CAAEC,CAAc,CAAC,EAAE,CAAC,EAC3C9T,EAAIqH,EAAKrH,IACZ4T,CAAe,CAAC5T,EAAE,CAAGkU,EAAmBR,CAAW,CAAC1T,EAAE,CAAE2T,CAAiB,CAAC3T,EAAE,EAC5EgU,GAAaL,CAAiB,CAAC3T,EAAIyB,EAAO,CAC1CuS,GAAaL,CAAiB,CAAC3T,EAAE,CACjCiU,GAAsBL,CAAe,CAAC5T,EAAIyB,EAAO,CACjDwS,GAAsBL,CAAe,CAAC5T,EAAE,CACxC0L,EAAQ,CACJ/J,CAAK,CAAC3B,EAAE,CACRA,EAAI+T,GAAatS,EACbwS,EAAqBD,EACrB,KACP,CACDH,EAAezT,IAAI,CAACsL,CAAK,CAAC,EAAE,EAC5BoI,EAAe1T,IAAI,CAACsL,CAAK,CAAC,EAAE,EAC5BxL,EAAOE,IAAI,CAAC,CAACsL,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,CAExC,CACA,MAAO,CACHxL,OAAQA,EACRyB,MAAOkS,EACPrU,MAAOsU,CACX,CACJ,CACJ,CAeAX,GAAa/N,cAAc,CAAG8N,GAAmBD,GAA0B7N,cAAc,CAAE,CAIvFvE,OAAQ,CACJI,MAAO,KAAK,EAKZsG,eAAgB,QACpB,CACJ,GACA7I,IAA4D+G,kBAAkB,CAAC,MAAO0N,IAwCtF,GAAM,CAAEzN,IAAK8O,EAAyB,CAAE,CAAG,AAAC9V,IAA6DG,WAAW,CAE9G,CAAE+G,aAAc6O,EAAyB,CAAExV,OAAQyV,EAAmB,CAAExV,QAASyV,EAAoB,CAAExV,MAAOyV,EAAkB,CAAE,CAAIxW,GAe5I,OAAMyW,WAAqBL,GAMvBM,YAAYlT,CAAI,CAAE5B,CAAC,CAAE+U,CAAY,CAAE,CAC/B,IACIC,EADEC,EAAcrT,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAEkV,EAAatT,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAEmV,EAAevT,CAAI,CAAC5B,EAAI,EAAE,CAAC,EAAE,CAAEoV,EAAcxT,CAAI,CAAC5B,EAAI,EAAE,CAAC,EAAE,CAUpH,OAAOyU,GARHQ,EAAcE,EAAeC,EAAcF,EAEtCH,EAAe/E,KAAKvM,GAAG,CAACwR,EAAcE,EAAc,GAAK,EAIzD,AAACJ,EAAuD,EAAxC/E,KAAKvM,GAAG,CAAC2R,EAAcF,EAAY,GAGhE,CACAG,YAAYC,CAAU,CAAEC,CAAE,CAAE,CACxB,OAAOD,EAAaC,EAAK,GAC7B,CACAC,YAAYC,CAAM,CAAEC,CAAO,CAAE,CACzB,OAAOjB,GAA0BzE,KAAKC,GAAG,CAACwF,EAASC,GAAW1F,KAAKC,GAAG,CAACwF,EAASC,GAAW,IAC/F,CACAC,aAAaC,CAAiB,CAAEC,CAAY,CAAEpU,CAAM,CAAE,CAClD,OAAOgT,GAA0BmB,EAAoBA,EAAoBnU,EAASoU,EACtF,CACAlG,MAAMC,CAAY,CAAEC,CAAS,CAAE,CAC3B,OAAO4E,GAA0BzE,KAAKvM,GAAG,CAEzCmM,CAAY,CAAC,EAAE,CAAGA,CAAY,CAAC,EAAE,CAEjC,AAACC,EAAgBG,KAAKC,GAAG,CAACL,CAAY,CAAC,EAAE,CAAGC,CAAS,CAAC,EAAE,EAA3C,EAEb,AAACA,EAAgBG,KAAKC,GAAG,CAACL,CAAY,CAAC,EAAE,CAAGC,CAAS,CAAC,EAAE,EAA3C,GACjB,CACArO,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGgU,EAAM,EAAE,CAAEnU,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAC1I,GAEA,AAACkC,EAAKI,MAAM,EAAIL,GAEZ,CAACkT,GAAqB/S,CAAI,CAAC,EAAE,GAC7BA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,CACd,OAEJ,IAAIiU,EAAqB,EAAGC,EAAsB,EAAGC,EAAiB,EAAGjW,EACzE,IAAKA,EAAI,EAAGA,EAAI6B,EAAS7B,IAAK,CAC1B,IAAIkW,EAAgBC,EAAiBC,EAAYC,EACjDC,EACAhG,EAAImF,EACJC,EACAa,CACIvW,CAAAA,GAAKyB,GACL4U,EAAS,IAAI,CAACvB,WAAW,CAAClT,EAAM5B,EAAG,CAAA,GACnCsW,EAAU,IAAI,CAACxB,WAAW,CAAClT,EAAM5B,GACjCsQ,EAAK,IAAI,CAACX,KAAK,CAAC/N,CAAI,CAAC5B,EAAE,CAAE4B,CAAI,CAAC5B,EAAI,EAAE,EAEpC+V,GAAsBM,EACtBL,GAAuBM,EACvBL,GAAkB3F,EAEdtQ,IAAMyB,IACNgU,EAAS,IAAI,CAACJ,WAAW,CAACU,EAAoBE,GAC9CP,EAAU,IAAI,CAACL,WAAW,CAACW,EAAqBC,GAChDM,EAAK,IAAI,CAACf,WAAW,CAACO,EAAoBC,GAC1CF,EAAI1V,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAEuW,EAAId,EAAQC,EAAQ,EACvC/T,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAAC,CAACmW,EAAId,EAAQC,EAAQ,KAKpCW,EAAS,IAAI,CAACvB,WAAW,CAAClT,EAAM5B,EAAG,CAAA,GACnCsW,EAAU,IAAI,CAACxB,WAAW,CAAClT,EAAM5B,GACjCsQ,EAAK,IAAI,CAACX,KAAK,CAAC/N,CAAI,CAAC5B,EAAE,CAAE4B,CAAI,CAAC5B,EAAI,EAAE,EAEpCkW,EAAiB,IAAI,CAACP,YAAY,CAACI,EAAoBM,EAAQ5U,GAC/D0U,EAAkB,IAAI,CAACR,YAAY,CAACK,EAAqBM,EAAS7U,GAClE2U,EAAa,IAAI,CAACT,YAAY,CAACM,EAAgB3F,EAAI7O,GAEnDsU,EAAqBG,EACrBF,EAAsBG,EACtBF,EAAiBG,EAEjBX,EAAS,IAAI,CAACJ,WAAW,CAACU,EAAoBE,GAC9CP,EAAU,IAAI,CAACL,WAAW,CAACW,EAAqBC,GAChDM,EAAK,IAAI,CAACf,WAAW,CAACO,EAAoBC,GAC1CF,EAAI1V,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAEuW,EAAId,EAAQC,EAAQ,EACvC/T,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAAC,CAACmW,EAAId,EAAQC,EAAQ,EAExC,CACA,MAAO,CACHxV,OAAQ4V,EACRnU,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAwBAqV,GAAazP,cAAc,CAAGwP,GAAmBJ,GAA0BpP,cAAc,CAAE,CAIvFvE,OAAQ,CACJI,MAAO,KAAK,CAChB,EACAyN,OAAQ,CACJC,QAAS,CAAA,CACb,EACAtJ,QAAS,CACLuJ,YAAa,mVASjB,EAIA4H,WAAY,CAIRjK,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,SACf,CACJ,EAIA2H,YAAa,CAITlK,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,SACf,CACJ,EACAC,aAAc,CACVC,cAAe,UACnB,CACJ,GACA0F,GAAoBG,GAAa7W,SAAS,CAAE,CACxCuM,eAAgB,EAAE,CAClBnJ,SAAU,MACVkJ,cAAe,CAAC,aAAc,cAAc,CAC5C7K,cAAe,CAAC,IAAK,SAAU,UAAU,CACzCiX,eAAgB,CAAC,IAAK,IAAK,SAAU,UAAU,CAC/C1L,YAAa,GACjB,GACAyC,EAAoCJ,OAAO,CAACwH,IAC5CnW,IAA4D+G,kBAAkB,CAAC,MAAOoP,IAuCtF,GAAM,CAAEnP,IAAKiR,EAAyB,CAAE,CAAG,AAACjY,IAA6DG,WAAW,CAE9G,CAAEI,OAAQ2X,EAAmB,CAAEzX,MAAO0X,EAAkB,CAAEjR,aAAckR,EAAyB,CAAE1X,KAAM2X,EAAiB,CAAE,CAAI3Y,IAUtI,SAAS4Y,GAAiB9U,CAAG,CAAEN,CAAI,CAAE5B,CAAC,CAAEiB,CAAK,CAAEgW,CAAQ,EACnD,IAAMlO,EAAQgO,GAAkBnV,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAAEW,CAAI,CAAC5B,EAAE,SACvD,AAAIiX,EACOH,GAA0B5U,EAAM6G,GAEpC+N,GAA0B5U,EAAM6G,EAC3C,CAeA,MAAMmO,WAAqBP,GAMvBnV,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAER,EAAQJ,EAAOI,KAAK,CAAEkW,EAASnH,KAAKoH,KAAK,CAAC3V,EAAS,EAAI,GAAIO,EAAQP,EAAS0V,EAAQzV,EAAOpC,EAAOqC,KAAK,EAAI,EAAE,CAAEC,EAAOtC,EAAOE,KAAK,EAAI,EAAE,CAAEqC,EAAUD,EAAKE,MAAM,CAE7LuV,EAAM,EAAE,CAAE1V,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAC5B8P,EAAYgI,EAAaC,EAAmBvX,EAAGgJ,EAAG9G,EAAM,EAC5D,IAAIR,CAAAA,EAAKI,MAAM,EAAIE,CAAI,GAIvB,IAAKhC,EAAI,EAAGA,EAAIyB,EAAS,EAAGzB,IACxBkC,EAAM8U,GAAiB9U,EAAKN,EAAM5B,EAAGiB,GAIzC,IAAK+H,EAAI,EAAGA,GAAKnH,EAAUG,EAAOgH,IAC9BsO,EAActO,EAAIvH,EAAS,EAC3B8V,EAAavO,EAAIhH,EAAQ,EAEzBE,EAAM8U,GAAiB9U,EAAKN,EAAM0V,EAAarW,GAE/CqO,EAAavG,AADLgO,GAAkBnV,CAAI,CAAC2V,EAAW,CAACtW,EAAM,CAAEW,CAAI,CAAC2V,EAAW,EAC9CrV,EAAMT,EAE3BS,EAAM8U,GAAiB9U,EAAKN,EAAMoH,EAAG/H,EAAO,CAAA,GAC5CoW,EAAIjX,IAAI,CAAC,CAACsB,CAAI,CAAC6V,EAAW,CAAEjI,EAAW,EACvC3N,EAAMvB,IAAI,CAACsB,CAAI,CAAC6V,EAAW,EAC3B/X,EAAMY,IAAI,CAACkP,GAEf,MAAO,CACHpP,OAAQmX,EACR1V,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAwBA0X,GAAa9R,cAAc,CAAGyR,GAAmBF,GAA0BvR,cAAc,CAAE,CAKvFvE,OAAQ,CACJI,MAAO,EAIPQ,OAAQ,EACZ,CACJ,GACAmV,GAAoBM,GAAalZ,SAAS,CAAE,CACxCoD,SAAU,KACd,GACA1C,IAA4D+G,kBAAkB,CAAC,MAAOyR,IAuCtF,GAAM,CAAEM,IAAKC,EAA6B,CAAE,CAAG,AAAC/Y,IAA6DG,WAAW,CAElH,CAAE+G,aAAc8R,EAA6B,CAAEzY,OAAQ0Y,EAAuB,CAAExY,MAAOyY,EAAsB,CAAE5Y,MAAO6Y,EAAsB,CAAE,CAAIzZ,GAexJ,OAAM0Z,WAAyBL,GAM3BjW,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAGIyO,EAAYtP,EAHV+X,EAAUlX,EAAOkX,OAAO,CAAEtW,EAASZ,EAAOY,MAAM,CAEtDuW,EAAM,EAAE,CAAErW,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAGhC,GAAIuY,AAAmB,IAAnBA,EAAQjW,MAAM,EAAUiW,CAAO,CAAC,EAAE,EAAIA,CAAO,CAAC,EAAE,CAAE,CAClDF,GAAuB,oGAEvB,MACJ,CAEA,IAAMI,EAAMC,AAv+DiCtR,EAu+DlB5I,SAAS,CAACwD,SAAS,CAACtD,IAAI,CAAC,IAAI,CAAEoB,EAAQ,CAC9DiI,eAAgB1G,EAAO0G,cAAc,CACrC9F,OAAQA,CACZ,GAEA,GAAI,CAACwW,EACD,OAGJ,IAAME,EAAM,KAAK,CAAC3W,UAAUtD,IAAI,CAAC,IAAI,CAAE+Z,EAAK,CACxCxW,OAAQsW,CAAO,CAAC,EAAE,AACtB,GAEMK,EAAM,KAAK,CAAC5W,UAAUtD,IAAI,CAAC,IAAI,CAAE+Z,EAAK,CACxCxW,OAAQsW,CAAO,CAAC,EAAE,AACtB,GAEA,GAAI,CAACI,GAAO,CAACC,EACT,OAEJ,IAAMC,EAAgBN,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAC7C,IAAK/X,EAAI,EAAGA,EAAIoY,EAAI5Y,KAAK,CAACsC,MAAM,CAAE9B,IAC9BsP,EAAaoI,GAA8BS,EAAI3Y,KAAK,CAACQ,EAAIqY,EAAc,CACnED,EAAI5Y,KAAK,CAACQ,EAAE,EAChBgY,EAAI5X,IAAI,CAAC,CAACgY,EAAIzW,KAAK,CAAC3B,EAAE,CAAEsP,EAAW,EACnC3N,EAAMvB,IAAI,CAACgY,EAAIzW,KAAK,CAAC3B,EAAE,EACvBR,EAAMY,IAAI,CAACkP,GAEf,MAAO,CACHpP,OAAQ8X,EACRrW,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAuBAsY,GAAiB1S,cAAc,CAAGwS,GAAuBH,GAA8BrS,cAAc,CAAE,CAOnGvE,OAAQ,CACJI,MAAO,KAAK,EAMZsG,eAAgB,SAMhB9F,OAAQ,EAORsW,QAAS,CAAC,EAAG,GAAG,AACpB,CACJ,GACAJ,GAAwBG,GAAiB9Z,SAAS,CAAE,CAChDoD,SAAU,cACVL,eAAgB,CAAC,UAAU,AAC/B,GACArC,IAA4D+G,kBAAkB,CAAC,UAAWqS,IAsC1F,GAAM,CAAEpS,IAAK4S,EAAyB,CAAE,CAAG,AAAC5Z,IAA6DG,WAAW,CAE9G,CAAE0Z,SAAAA,EAAQ,CAAEpZ,MAAOqZ,EAAkB,CAAE,CAAIpa,GAejD,OAAMqa,WAAqBH,GAMvB9W,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG4W,EAAM,EAAE,CAAE/W,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACtIQ,EAAGiB,EAAQJ,EAAOI,KAAK,CAAEf,EAC7B,GAAIwB,EAAKI,MAAM,CAAGL,EACd,OAEA8W,GAAS3W,CAAI,CAAC,EAAE,EAChB1B,EAAS0B,GAMTX,EAAQ+O,KAAKxM,GAAG,CAACvC,EAAOW,CAAI,CAAC,EAAE,CAACE,MAAM,CAAG,GACzC5B,EAAS0B,EAAK7B,GAAG,CAAC,AAACkN,GAAUA,CAAK,CAAChM,EAAM,GAE7C,IAAI0X,EAAgB,EAAGC,EAAoB,EAAGC,EAAmB,EAAG5S,EAGpE,IAAK,IAAI+C,EAAIvH,EAAQuH,EAAI,EAAGA,IACpB9I,CAAM,CAAC8I,EAAE,CAAG9I,CAAM,CAAC8I,EAAI,EAAE,CACzB4P,GAAqB1Y,CAAM,CAAC8I,EAAE,CAAG9I,CAAM,CAAC8I,EAAI,EAAE,CAEzC9I,CAAM,CAAC8I,EAAE,CAAG9I,CAAM,CAAC8I,EAAI,EAAE,EAC9B6P,CAAAA,GAAoB3Y,CAAM,CAAC8I,EAAI,EAAE,CAAG9I,CAAM,CAAC8I,EAAE,AAAD,EAapD,IARA/C,EACI2S,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRlX,EAAMvB,IAAI,CAACsB,CAAI,CAACD,EAAO,EACvBjC,EAAMY,IAAI,CAAC6F,GACXyS,EAAItY,IAAI,CAAC,CAACsB,CAAI,CAACD,EAAO,CAAEwE,EAAE,EACrBjG,EAAIyB,EAAS,EAAGzB,EAAI6B,EAAS7B,IAC9B2Y,EAAgB3I,KAAKC,GAAG,CAAC/P,CAAM,CAACF,EAAIyB,EAAS,EAAE,CAAGvB,CAAM,CAACF,EAAIyB,EAAO,EAChEvB,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CACzB4Y,GAAqB1Y,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CAEzCE,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,EAC9B6Y,CAAAA,GAAoB3Y,CAAM,CAACF,EAAI,EAAE,CAAGE,CAAM,CAACF,EAAE,AAAD,EAI5CE,CAAM,CAACF,EAAIyB,EAAO,CAAGvB,CAAM,CAACF,EAAIyB,EAAS,EAAE,CAC3CmX,GAAqBD,EAGrBE,GAAoBF,EAGxB1S,EACI2S,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRlX,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAAC6F,GACXyS,EAAItY,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAEiG,EAAE,EAEzB,MAAO,CACH/F,OAAQwY,EACR/W,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAqBAiZ,GAAarT,cAAc,CAAGoT,GAAmBF,GAA0BlT,cAAc,CAAE,CACvFvE,OAAQ,CACJY,OAAQ,GACRR,MAAO,CACX,CACJ,GACAvC,IAA4D+G,kBAAkB,CAAC,MAAOgT,IAoCtF,GAAM,CAAEjB,IAAKsB,EAA0B,CAAE,CAAG,AAACpa,IAA6DG,WAAW,CAE/G,CAAE+G,aAAcmT,EAA0B,CAAE7Z,QAAS8Z,EAAqB,CAAE7Z,MAAO8Z,EAAmB,CAAE,CAAI7a,GAelH,OAAM8a,WAAsBJ,GAMxBK,OAAOvX,CAAI,CAAEwX,CAAO,CAAErX,CAAG,CAAEd,CAAK,CAAEjB,CAAC,CAAE0B,CAAI,CAAE,CACvC,OAAO,KAAK,CAACwE,aAAaxE,GAAQ,EAAE,CAAEE,EAAM,AAAa,KAAA,IAAN5B,EAAoB,EAAIA,EAAG,IAAI,CAACmG,UAAU,CAAEiT,EAAS,AAAiB,KAAA,IAAVnY,EAAwB,GAAKA,EAAOc,EACvJ,CACAP,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAE4X,EAAY,EAAE,CAAEC,EAAgB,EAAI7X,EAAQC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGyX,EAAO,EAAE,CAAEC,EAAY,EAAE,CAAEC,EAAY,EAAE,CAC3LzT,EAAyB,EAAGM,EAAM,EAEtCoT,EAEAN,EAASO,EAET3Z,EAAGiB,EAAQ,GAAI2Y,EAAW7X,EAAM,EAGhC,GAFA,IAAI,CAACoE,UAAU,CAAI,EAAK1E,CAAAA,EAAS,CAAA,GAE7BI,CAAAA,EAAU,EAAIJ,EAAS,CAAA,GAc3B,IAVIuX,GAAsBpX,CAAI,CAAC,EAAE,GAC7BX,CAAAA,EAAQJ,EAAOI,KAAK,CAAGJ,EAAOI,KAAK,CAAG,CAAA,EAM1Cc,EAAMiE,AAHNA,CAAAA,EACI,KAAK,CAACA,uBAAuBvE,EAAQR,EAAOW,EAAI,EAErBH,EAC/BuE,EAAyB,EAEpBhG,EAAIyB,EAAQzB,EAAI6B,EAAU,EAAG7B,IAC1BA,EAAI6B,EAAU,IACdyE,EAAM,IAAI,CAAC6S,MAAM,CAACvX,EAAMwX,EAASrX,EAAKd,EAAOjB,EAAE,CAAC,EAAE,CAClDqZ,EAAUjZ,IAAI,CAACkG,IAEnB8S,EAAU9S,EAENtG,EAAIsZ,EACJtT,GAA0BM,GAKtBtG,IAAMsZ,GACNvX,CAAAA,EAAMiE,EAAyBvE,CAAK,EAExC6E,EAAM+S,CAAS,CAACrZ,EAAIyB,EAAS,EAAE,CAC/BiY,EAAY,IAAI,CAACP,MAAM,CAAC,CAAC7S,EAAI,CAAEqT,EAAe5X,EAAI,CAAC,EAAE,CACrD6X,EAAY,CACRlY,CAAI,CAAC1B,EAAI,EAAE,CACX+Y,GAA2B,EAAIzS,EAAMoT,GACxC,CACDH,EAAKnZ,IAAI,CAACwZ,GACVJ,EAAUpZ,IAAI,CAACwZ,CAAS,CAAC,EAAE,EAC3BH,EAAUrZ,IAAI,CAACwZ,CAAS,CAAC,EAAE,EAC3BD,EAAgBD,GAGxB,MAAO,CACHxZ,OAAQqZ,EACR5X,MAAO6X,EACPha,MAAOia,CACX,EACJ,CACJ,CAyBAP,GAAc9T,cAAc,CAAG6T,GAAoBH,GAA2B1T,cAAc,EAC5F1G,IAA4D+G,kBAAkB,CAAC,OAAQyT,IAsCvF,GAAM,CAAE1B,IAAKqC,EAA0B,CAAE,CAAG,AAACnb,IAA6DG,WAAW,CAE/G,CAAE+G,aAAckU,EAA0B,CAAE5a,QAAS6a,EAAqB,CAAE5a,MAAO6a,EAAmB,CAAE,CAAI5b,GAelH,OAAM6b,WAAsBJ,GAMxBV,OAAOvX,CAAI,CAAEwX,CAAO,CAAErX,CAAG,CAAEd,CAAK,CAAEjB,CAAC,CAAE0B,CAAI,CAAE,CACvC,OAAO,KAAK,CAACwE,aAAaxE,GAAQ,EAAE,CAAEE,EAAM,AAAa,KAAA,IAAN5B,EAAoB,EAAIA,EAAG,IAAI,CAACmG,UAAU,CAAEiT,EAAS,AAAiB,KAAA,IAAVnY,EAAwB,GAAKA,EAAOc,EACvJ,CACAmY,aAAaxY,CAAI,CAAEyY,CAAa,CAAEC,CAAS,CAAEpa,CAAC,CAAE,CAM5C,MALkB,CACd0B,CAAI,CAAC1B,EAAI,EAAE,CACX8Z,GAA2B,EAAIM,EAAUC,MAAM,CAC3C,EAAID,EAAUE,MAAM,CAAGF,EAAUG,MAAM,EAC9C,AAEL,CACA/Y,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAE6X,EAAgB,EAAI7X,EAAQ0Y,EAAgB,EAAI1Y,EAAQC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG0Y,EAAO,EAAE,CAAEC,EAAY,EAAE,CAAEC,EAAY,EAAE,CAE3MC,EAAY,EAAE,CAAEC,EAAkB,EAAE,CAKpCC,EAAY,CAAC,EACT5Z,EAAQ,GAAI+E,EAAyB,EAAGN,EAAM,EAElD0T,EAASO,EAAe3Z,EAAG8a,EAG3B,GAFA,IAAI,CAAC3U,UAAU,CAAI,EAAK1E,CAAAA,EAAS,CAAA,GAE7BI,CAAAA,EAAU,EAAIJ,EAAS,CAAA,GAa3B,IATIsY,GAAsBnY,CAAI,CAAC,EAAE,GAC7BX,CAAAA,EAAQJ,EAAOI,KAAK,CAAGJ,EAAOI,KAAK,CAAG,CAAA,EAK1CyE,EAAMM,AAFNA,CAAAA,EAAyB,KAAK,CAACA,uBAAuBvE,EAAQR,EAAOW,EAAI,EAE1CH,EAC/BuE,EAAyB,EAEpBhG,EAAIyB,EAAQzB,EAAI6B,EAAU,EAAG7B,IAC1BA,EAAI6B,EAAU,IACdgZ,EAAUR,MAAM,CAAG,IAAI,CAAClB,MAAM,CAACvX,EAAMwX,EAAS1T,EAAKzE,EAAOjB,EAAE,CAAC,EAAE,CAC/D2a,EAAUva,IAAI,CAACya,EAAUR,MAAM,GAEnCjB,EAAUyB,EAAUR,MAAM,CAEtBra,EAAIsZ,EACJtT,GAA0B6U,EAAUR,MAAM,EAKtCra,IAAMsZ,IACN5T,EAAMM,EAAyBvE,EAC/BuE,EAAyB,GAE7B6U,EAAUR,MAAM,CAAGM,CAAS,CAAC3a,EAAIyB,EAAS,EAAE,CAC5CoZ,EAAUP,MAAM,CAAG,IAAI,CAACnB,MAAM,CAAC,CAAC0B,EAAUR,MAAM,CAAC,CAAEV,EAAejU,EAAI,CAAC,EAAE,CACzEkV,EAAgBxa,IAAI,CAACya,EAAUP,MAAM,EACrCX,EAAgBkB,EAAUP,MAAM,CAE5Bta,EAAIma,EACJnU,GAA0B6U,EAAUP,MAAM,EAKtCta,IAAMma,GACNzU,CAAAA,EAAMM,EAAyBvE,CAAK,EAEpCzB,IAAM6B,EAAU,IAEhBgZ,EAAUR,MAAM,CAAGM,CAAS,CAAC3a,EAAIyB,EAAS,EAAE,CAC5CoZ,EAAUP,MAAM,CAAG,IAAI,CAACnB,MAAM,CAAC,CAAC0B,EAAUR,MAAM,CAAC,CAAEV,EAAejU,EAAI,CAAC,EAAE,CACzEkV,EAAgBxa,IAAI,CAACya,EAAUP,MAAM,GAEzCO,EAAUR,MAAM,CAAGM,CAAS,CAAC3a,EAAIyB,EAAS,EAAE,CAC5CoZ,EAAUP,MAAM,CAAGM,CAAe,CAAC5a,EAAI,EAAIyB,EAAS,EAAE,CACtDoZ,EAAUN,MAAM,CAAG,IAAI,CAACpB,MAAM,CAAC,CAAC0B,EAAUP,MAAM,CAAC,CAAEO,EAAUE,UAAU,CAAErV,EAAI,CAAC,EAAE,CAChFoV,CAAAA,EAAY,IAAI,CAACZ,YAAY,CAACxY,EAAMyY,EAAeU,EAAW7a,EAAC,IAG3Dwa,EAAKpa,IAAI,CAAC0a,GACVL,EAAUra,IAAI,CAAC0a,CAAS,CAAC,EAAE,EAC3BJ,EAAUta,IAAI,CAAC0a,CAAS,CAAC,EAAE,GAE/BD,EAAUE,UAAU,CAAGF,EAAUN,MAAM,GAInD,MAAO,CACHra,OAAQsa,EACR7Y,MAAO8Y,EACPjb,MAAOkb,CACX,EACJ,CACJ,CAyBAT,GAAc7U,cAAc,CAAG4U,GAAoBH,GAA2BzU,cAAc,EAC5F1G,IAA4D+G,kBAAkB,CAAC,OAAQwU,IAsCvF,GAAM,CAAEO,KAAMQ,EAA2B,CAAE,CAAG,AAACtc,IAA6DG,WAAW,CAEjH,CAAE+G,aAAcqV,EAA0B,CAAE9b,MAAO+b,EAAmB,CAAE,CAAI9c,GAelF,OAAM+c,WAAsBH,GAOxBd,aAAaxY,CAAI,CAAEyY,CAAa,CAAEC,CAAS,CAAEpa,CAAC,CAAE,CAC5C,GAAIA,EAAIma,EACJ,MAAO,CACHzY,CAAI,CAAC1B,EAAI,EAAE,CACXoa,AAAyB,IAAzBA,EAAUW,UAAU,CAChBE,GAA2Bb,EAAUG,MAAM,CAAGH,EAAUW,UAAU,EAC9DX,EAAUW,UAAU,CAAG,IAAM,KACxC,AAET,CACJ,CAyBAI,GAAc/V,cAAc,CAAG8V,GAAoBF,GAA4B5V,cAAc,EAC7F1G,IAA4D+G,kBAAkB,CAAC,OAAQ0V,IAsCvF,GAAM,CAAE3D,IAAK4D,EAAyB,CAAE,CAAG,AAAC1c,IAA6DG,WAAW,CAE9G,CAAEI,OAAQoc,EAAmB,CAAElc,MAAOmc,EAAkB,CAAEtc,MAAOuc,EAAkB,CAAE,CAAInd,GAe/F,OAAMod,WAAqBJ,GAMvB5Z,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAGIyO,EAAYtP,EAHV+X,EAAUlX,EAAOkX,OAAO,CAAE9W,EAAQJ,EAAOI,KAAK,CAEpDwa,EAAM,EAAE,CAAE9Z,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAGhC,GAAIuY,AAAmB,IAAnBA,EAAQjW,MAAM,EAAUiW,CAAO,CAAC,EAAE,EAAIA,CAAO,CAAC,EAAE,CAAE,CAClDwD,GAAmB,gGAEnB,MACJ,CAEA,IAAMpD,EAAM,KAAK,CAAC3W,UAAUtD,IAAI,CAAC,IAAI,CAAEoB,EAAQ,CAC3C2B,MAAOA,EACPQ,OAAQsW,CAAO,CAAC,EAAE,AACtB,GAEMK,EAAM,KAAK,CAAC5W,UAAUtD,IAAI,CAAC,IAAI,CAAEoB,EAAQ,CAC3C2B,MAAOA,EACPQ,OAAQsW,CAAO,CAAC,EAAE,AACtB,GAEA,GAAI,CAACI,GAAO,CAACC,EACT,OAEJ,IAAMC,EAAgBN,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAC7C,IAAK/X,EAAI,EAAGA,EAAIoY,EAAI5Y,KAAK,CAACsC,MAAM,CAAE9B,IAC9BsP,EAAc6I,EAAI3Y,KAAK,CAACQ,EAAIqY,EAAc,CACtCD,EAAI5Y,KAAK,CAACQ,EAAE,CAChByb,EAAIrb,IAAI,CAAC,CAACgY,EAAIzW,KAAK,CAAC3B,EAAE,CAAEsP,EAAW,EACnC3N,EAAMvB,IAAI,CAACgY,EAAIzW,KAAK,CAAC3B,EAAE,EACvBR,EAAMY,IAAI,CAACkP,GAEf,MAAO,CACHpP,OAAQub,EACR9Z,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAuBAgc,GAAapW,cAAc,CAAGkW,GAAmBF,GAA0BhW,cAAc,CAAE,CAOvFvE,OAAQ,CACJY,OAAQ,KAAK,EAQbsW,QAAS,CAAC,GAAI,GAAG,AACrB,CACJ,GACAsD,GAAoBG,GAAaxd,SAAS,CAAE,CACxCoD,SAAU,MACVL,eAAgB,CAAC,UAAU,AAC/B,GACArC,IAA4D+G,kBAAkB,CAAC,MAAO+V,IA6BtF,IAAME,GAA0E7e,EAAkE,OAAU,CAACkS,YAAY,CAAC4M,cAAc,CACxL,IAAIC,GAA+E7e,EAAoBC,CAAC,CAAC0e,IAEzG,IAAMG,GAA2Djf,EAAwD,OAAU,CAACkf,KAAK,CAanI,CAAEC,MAAO1T,EAAK,CAAE,CAAI2T,AAZ0Cjf,EAAoBC,CAAC,CAAC6e,MAcpF,CAAEnW,IAAKuW,EAAyB,CAAE,CAAG,AAACvd,IAA6DG,WAAW,CAE9G,CAAEsL,QAAS+R,EAAoB,CAAEjd,OAAQkd,EAAmB,CAAEjd,QAASkd,EAAoB,CAAE7D,SAAU8D,EAAqB,CAAEC,mBAAAA,EAAkB,CAAEnd,MAAOod,EAAkB,CAAEC,WAAAA,EAAU,CAAE,CAAIpe,IAyBnM,SAASqe,GAAa1O,CAAG,EACrB,MAAO,CACH/G,KAjBG+G,AAiBWA,EAjBP8E,MAAM,CAAC,SAAUpP,CAAG,CAAEiZ,CAAG,EAChC,OAAO1M,KAAKvM,GAAG,CAACA,EAAKiZ,CAAG,CAAC,EAAE,CAC/B,EAAG,CAACC,KAgBA1V,IAVG8G,AAUSA,EAVL8E,MAAM,CAAC,SAAUrP,CAAG,CAAEkZ,CAAG,EAChC,OAAO1M,KAAKxM,GAAG,CAACA,EAAKkZ,CAAG,CAAC,EAAE,CAC/B,EAAGC,IASH,CACJ,CA6BA,SAASC,GAAeC,CAAG,EACvB,IAAMxa,EAAYwa,EAAIxa,SAAS,AAC/BA,CAAAA,EAAUc,MAAM,CAAG0Z,EAAI1Z,MAAM,CAC7Bd,EAAU8J,UAAU,CAAG0Q,EAAI1Q,UAAU,CACrC9J,EAAUgG,KAAK,CAAGwU,EAAIxU,KAAK,CAC3BhG,EAAUnB,OAAO,CAAGqb,GAAmBM,EAAI3b,OAAO,CAAC4b,UAAU,CAACvQ,MAAM,CAAEsQ,EAAIE,GAAG,EAC7E1a,EAAU+I,KAAK,CAAGyR,EAAIzR,KAAK,CAC3B/I,EAAUgK,SAAS,CAAG,CAAA,EACtB3N,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACkK,SAAS,CAAChK,IAAI,CAACmE,EACzG,CAgCA,MAAM2a,WAAqBf,GACvB7I,aAAc,CAMV,KAAK,IAAIzS,WAMT,IAAI,CAACgE,IAAI,CAAG,EAAE,CACd,IAAI,CAACzD,OAAO,CAAG,CAAC,EAChB,IAAI,CAACiC,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC8Z,eAAe,CAAG,EAAE,AAC7B,CAMA9a,MAAO,CACH,KAAK,CAACA,KAAKzB,KAAK,CAAC,IAAI,CAAEC,WAEvB,IAAI,CAACO,OAAO,CAAGqb,GAAmB,CAC9BW,WAAY,CACR3Q,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,EACA8U,UAAW,CACP5Q,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,EACA+U,WAAY,CACR7Q,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,EACAgV,YAAa,CACT9Q,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,CACrBiV,KAAMjV,GAAM,IAAI,CAACA,KAAK,EAAEkV,UAAU,CAAC,IAAK1f,GAAG,EAC/C,CACJ,EACA2f,YAAa,CACTjR,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,CACrBiV,KAAMjV,GAAM,IAAI,CAACA,KAAK,EAAEkV,UAAU,CAAC,IAAK1f,GAAG,EAC/C,CACJ,EACAif,WAAY,CACRvQ,OAAQ,CACJ+Q,KAAMjV,GAAM,IAAI,CAACA,KAAK,EAAEkV,UAAU,CAAC,IAAK1f,GAAG,EAC/C,CACJ,CACJ,EAAG,IAAI,CAACqD,OAAO,CACnB,CACAsM,QAAQ9B,CAAK,CAAE,CACX,MAAO,CACHA,EAAM+R,SAAS,CACf/R,EAAMgS,QAAQ,CACdhS,EAAMiS,UAAU,CAChBjS,EAAM2R,WAAW,CACjB3R,EAAM8R,WAAW,CACpB,AACL,CACAxT,WAAY,CAGR,IAAK,IAAM0B,KADXhN,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACgM,SAAS,CAACtJ,KAAK,CADnF,IAAI,EAEF2B,AAFF,IAAI,CAEQc,MAAM,EAChC,IAAK,IAAM3F,KAAO6E,AAHJ,IAAI,CAGU5C,aAAa,CAAE,CACvC,IAAMme,EAAalS,CAAK,CAAClO,EAAI,CACzB6e,GAAsBuB,KACtBlS,CAAK,CAAC,OAASlO,EAAI,CAAG6E,AANhB,IAAI,CAMsB8K,KAAK,CAACC,QAAQ,CAACwQ,EAAY,CAAA,GAG3DlS,EAAMG,KAAK,CAAGH,CAAK,CAAC,OAASlO,EAAI,CACjCkO,EAAMmS,UAAU,CAAG,CACfnS,EAAME,KAAK,CACXF,CAAK,CAAC,OAASlO,EAAI,CACtB,CACDkO,EAAMI,MAAM,CAAG,CAAA,EAEvB,CAER,CACA5D,WAAY,CACR,IAAM7F,EAAY,IAAI,CAAE4I,EAAiB5I,EAAUc,MAAM,CAAE+H,EAAkB7I,EAAUnB,OAAO,CAAEiK,EAAe9I,EAAU+I,KAAK,CAAE0S,EAAYzb,EAAUgG,KAAK,CAAEgD,EAAe,CACxKnK,QAAS,CACLoK,QAASJ,EAAgBI,OAAO,AACpC,CACJ,EAAGyS,EAAsB1b,EAAU5C,aAAa,CAACqC,MAAM,CAAEkc,EAAoB,CACzE,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACL,CAAEC,EAAS,CACRf,WAAYc,CAAiB,CAAC,EAAE,CAChCb,UAAWa,CAAiB,CAAC,EAAE,CAC/BZ,WAAYY,CAAiB,CAAC,EAAE,CAChCX,YAAaW,CAAiB,CAAC,EAAE,CACjCR,YAAaQ,CAAiB,CAAC,EAAE,CACjClB,WAAYkB,CAAiB,CAAC,EAAE,AACpC,EAAGE,EAAqB,EAAE,CAAEC,EAAoB9b,EAC3CnB,OAAO,CAAC4b,UAAU,CAAEzU,EAAQ8V,EAAkB9V,KAAK,EACpD8V,EAAkB5R,MAAM,CAAC+Q,IAAI,CAAE9U,EAAgB2V,EAAkB3V,aAAa,CAElFrF,EAAS,CACL,EAAE,CACF,EAAE,CACL,CAGDgJ,EAAa,CACT,EAAE,CACF,EAAE,CACL,CACGV,EAAeR,EAAenJ,MAAM,CAAEsc,EAAY,EAAGC,EAAU3S,EAAO1L,EAAGse,EAAgBC,EAAcC,EAAeC,EAAmBC,EAAgBC,EAAoBC,EAAqBC,EAAgB7V,EAAG8V,EAGzN,IAFAzc,EAAU4b,MAAM,CAAGA,EAEZxS,KAAgB,CAEnB,IAAKzL,EAAI,EADT0L,EAAQT,CAAc,CAACQ,EAAa,CACxBzL,EAAI+d,EAAqB/d,IAE7Bkc,GAAqBxQ,CAAK,CAD9B2S,EAAWhc,EAAU5C,aAAa,CAACO,EAAE,CACG,GACpCge,CAAiB,CAAChe,EAAE,CAACI,IAAI,CAAC,CACtBwL,MAAOF,EAAME,KAAK,CAClBC,MAAOH,CAAK,CAAC,OAAS2S,EAAS,CAC/BvS,OAAQ,CAAA,CACZ,GAGR,GAAItD,GAAiBiD,IAAiBR,EAAenJ,MAAM,CAAG,EAAG,CAE7D,IAAMb,EAAQgd,EAAOT,WAAW,CAAC1b,MAAM,CAAG,EAAGid,EAAYC,AA5MzE,SAA+BC,CAAE,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAE,EACzC,GAAIH,GAAMC,GAAMC,GAAMC,EAAI,CACtB,IAAMC,EAAMH,EAAGtT,KAAK,CAAGqT,EAAGrT,KAAK,CAC/B0T,EAAMJ,EAAGrT,KAAK,CAAGoT,EAAGpT,KAAK,CACzB0T,EAAMH,EAAGxT,KAAK,CAAGuT,EAAGvT,KAAK,CACzB4T,EAAMJ,EAAGvT,KAAK,CAAGsT,EAAGtT,KAAK,CACzB4T,EAAOR,EAAGrT,KAAK,CAAGuT,EAAGvT,KAAK,CAC1B8T,EAAOT,EAAGpT,KAAK,CAAGsT,EAAGtT,KAAK,CAE1B8T,EAAI,AAAC,CAAA,CAACL,EAAMG,EAAOJ,EAAMK,CAAG,EAAM,CAAA,CAACH,EAAMD,EAAMD,EAAMG,CAAE,EAAII,EAAI,AAACL,CAAAA,EAAMG,EAAOF,EAAMC,CAAG,EAAM,CAAA,CAACF,EAAMD,EAAMD,EAAMG,CAAE,EACjH,GAAIG,GAAK,GAAKA,GAAK,GAAKC,GAAK,GAAKA,GAAK,EACnC,MAAO,CACHhU,MAAOqT,EAAGrT,KAAK,CAAGgU,EAAIP,EACtBxT,MAAOoT,EAAGpT,KAAK,CAAG+T,EAAIN,CAC1B,CAER,CACJ,EA2L+FrB,EAAOZ,WAAW,CAACpc,EAAQ,EAAE,CAAEgd,EAAOZ,WAAW,CAACpc,EAAM,CAAEgd,EAAOT,WAAW,CAACvc,EAAQ,EAAE,CAAEgd,EAAOT,WAAW,CAACvc,EAAM,EACjM,GAAI8d,EAAW,CACX,IAAMc,EAAoB,CACtBjU,MAAOmT,EAAUnT,KAAK,CACtBC,MAAOkT,EAAUlT,KAAK,CACtBC,OAAQ,CAAA,EACRgU,eAAgB,CAAA,CACpB,EAGA7B,EAAOZ,WAAW,CAAC0C,MAAM,CAAC9e,EAAO,EAAG4e,GACpC5B,EAAOT,WAAW,CAACuC,MAAM,CAAC9e,EAAO,EAAG4e,GACpC3B,EAAmB9d,IAAI,CAACa,EAC5B,CACJ,CACJ,CAoBA,GAlBAub,GAAWyB,EAAQ,CAAC/d,EAAQoM,KACpBpB,CAAe,CAACoB,EAAS,EACzBA,AAAa,eAAbA,IAEAjK,EAAUc,MAAM,CAAG6a,CAAiB,CAACI,EAAU,CAC/C/b,EAAUnB,OAAO,CAAGqb,GAAmBrR,CAAe,CAACoB,EAAS,CAACC,MAAM,CAAElB,GACzEhJ,EAAU+I,KAAK,CAAG/I,CAAS,CAAC,QAAUiK,EAAS,CAC/CjK,EAAUgK,SAAS,CAAG,CAAA,EACtBhK,EAAUgG,KAAK,CAAGyV,EAClBpf,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACkK,SAAS,CAAChK,IAAI,CAACmE,GAErGA,CAAS,CAAC,QAAUiK,EAAS,CAAGjK,EAAU+I,KAAK,EAEnDgT,GACJ,GAII/b,EAAU4a,eAAe,CACzB,IAAK,IAAM+C,KAAa3d,EAAU4a,eAAe,CAC7C5a,CAAS,CAAC2d,EAAU,CAAC1f,OAAO,GAC5B,OAAO+B,CAAS,CAAC2d,EAAU,CAMnC,GAFA3d,EAAU4a,eAAe,CAAG,EAAE,CAE1BzU,GAAiByV,EAAOZ,WAAW,CAAC,EAAE,EAAIY,EAAOT,WAAW,CAAC,EAAE,CAAE,CAKjE,IAHAU,EAAmB+B,OAAO,CAAC,GAC3B/B,EAAmB9d,IAAI,CAAC6d,EAAOZ,WAAW,CAACvb,MAAM,CAAG,GAE/CkH,EAAI,EAAGA,EAAIkV,EAAmBpc,MAAM,CAAG,EAAGkH,IAO3C,GANAsV,EAAiBJ,CAAkB,CAAClV,EAAE,CACtCuV,EAAeL,CAAkB,CAAClV,EAAI,EAAE,CACxCwV,EAAgBP,EAAOT,WAAW,CAAC7S,KAAK,CAAC2T,EAAgBC,EAAe,GACxEE,EAAoBR,EAAOZ,WAAW,CAAC1S,KAAK,CAAC2T,EAAgBC,EAAe,GAGxEvO,KAAKoH,KAAK,CAACoH,EAAc1c,MAAM,CAAG,IAAM,EAAG,CAC3C,IAAMwC,EAAI0L,KAAKoH,KAAK,CAACoH,EAAc1c,MAAM,CAAG,GAG5C,GAAI0c,CAAa,CAACla,EAAE,CAACuH,KAAK,GAAK4S,CAAiB,CAACna,EAAE,CAACuH,KAAK,CAAE,CAGvD,IAAKiT,EAAI,EAFTJ,EAAiB,EACjBC,EAAqB,EACTG,EAAIN,EAAc1c,MAAM,CAAEgd,IAClCJ,GAAkBF,CAAa,CAACM,EAAE,CAACjT,KAAK,CACxC8S,GAAsBF,CAAiB,CAACK,EAAE,CAACjT,KAAK,AAIpD1I,CAAAA,CAAM,CAFN0b,EACIH,EAAiBC,EAAqB,EAAI,EACxB,CAAGxb,CAAM,CAAC0b,EAAe,CAACqB,MAAM,CAAC1B,GACvDrS,CAAU,CAAC0S,EAAe,CAAG1S,CAAU,CAAC0S,EAAe,CAACqB,MAAM,CAACzB,EACnE,MAIItb,CAAM,CADN0b,EAAiB,AAACL,CAAa,CAACla,EAAE,CAACuH,KAAK,CAAG4S,CAAiB,CAACna,EAAE,CAACuH,KAAK,CAAI,EAAI,EACvD,CAAG1I,CAAM,CAAC0b,EAAe,CAACqB,MAAM,CAAC1B,GACvDrS,CAAU,CAAC0S,EAAe,CAAG1S,CAAU,CAAC0S,EAAe,CAACqB,MAAM,CAACzB,EAEvE,MAIItb,CAAM,CADN0b,EAAiB,AAACL,CAAa,CAAC,EAAE,CAAC3S,KAAK,CAAG4S,CAAiB,CAAC,EAAE,CAAC5S,KAAK,CAAI,EAAI,EACvD,CAAG1I,CAAM,CAAC0b,EAAe,CAACqB,MAAM,CAAC1B,GACvDrS,CAAU,CAAC0S,EAAe,CAAG1S,CAAU,CAAC0S,EAAe,CAACqB,MAAM,CAACzB,GAIvE,CAAC,uBAAwB,+BAA+B,CAACje,OAAO,CAAC,SAAU2f,CAAQ,CAAEngB,CAAC,EAC9EmD,CAAM,CAACnD,EAAE,CAAC8B,MAAM,EAAIqK,CAAU,CAACnM,EAAE,CAAC8B,MAAM,GACxC8c,EAAsB5e,AAAM,IAANA,EAAUqI,EAAQG,EACxCoU,GAAe,CACXva,UAAWA,EACXc,OAAQA,CAAM,CAACnD,EAAE,CACjBmM,WAAYA,CAAU,CAACnM,EAAE,CACzBqI,MAAOuW,EACP1d,QAASgK,EACT6R,IAAK1R,EACLD,MAAO/I,CAAS,CAAC8d,EAAS,AAC9B,GAEA9d,CAAS,CAAC8d,EAAS,CAAG9d,EAAU+I,KAAK,CACrC/I,EAAU4a,eAAe,CAAC7c,IAAI,CAAC+f,GAEvC,EACJ,MAGIvD,GAAe,CACXva,UAAWA,EACXc,OAAQ8a,EAAOT,WAAW,CAC1BrR,WAAY8R,EAAOZ,WAAW,CAC9BhV,MAAOA,EACPnH,QAASgK,EACT6R,IAAK1R,EACLD,MAAO/I,EAAU+d,eAAe,AACpC,GAEA/d,EAAU+d,eAAe,CAAG/d,EAAU+I,KAAK,AAG/C,QAAO/I,EAAU8J,UAAU,CAC3B,OAAO9J,EAAUgK,SAAS,CAE1BhK,EAAUc,MAAM,CAAG8H,EACnB5I,EAAUnB,OAAO,CAAGgK,EACpB7I,EAAU+I,KAAK,CAAGD,EAClB9I,EAAUgG,KAAK,CAAGyV,CACtB,CACAlR,aAAazJ,CAAM,CAAE,CAEjB,IAAIuJ,EAAO,EAAE,CAAE2T,EAAOC,EAAW,EAAE,CAGnC,GAFAnd,EAASA,GAAU,IAAI,CAACA,MAAM,CAE1Bd,AAJc,IAAI,CAIRgK,SAAS,EAAIhK,AAJT,IAAI,CAIe8J,UAAU,CAI3C,CAAA,GAAIkU,AAHJA,CAAAA,EAAQ3hB,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAAC4O,YAAY,CAAC1O,IAAI,CALjG,IAAI,CAOlBmE,AAPc,IAAI,CAOR8J,UAAU,CAAA,GACPkU,EAAMve,MAAM,CAAE,CACvBue,CAAK,CAAC,EAAE,CAAC,EAAE,CAAG,IACd3T,EAAOhO,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAAC4O,YAAY,CACpG1O,IAAI,CAXC,IAAI,CAWOiF,GACrBmd,EAAWD,EAAM1V,KAAK,CAAC,EAAG+B,EAAK5K,MAAM,EACrC,IAAK,IAAI9B,EAAIsgB,EAASxe,MAAM,CAAG,EAAG9B,GAAK,EAAGA,IACtC0M,EAAKtM,IAAI,CAACkgB,CAAQ,CAACtgB,EAAE,CAE7B,CAAA,MAGA0M,EAAOhO,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAAC4O,YAAY,CACpGlM,KAAK,CApBI,IAAI,CAoBIC,WAE1B,OAAO+L,CACX,CACAlL,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IACIsQ,EAAwCoP,EAASC,EAASC,EAAUzgB,EAAG0gB,EAAIC,EAAIC,EAAIC,EAAKC,EADtFrf,EAASZ,EAAOY,MAAM,CAAEsf,EAAelgB,EAAOkgB,YAAY,CAAEC,EAAoBngB,EAAOmgB,iBAAiB,CAAEtf,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqD,EAAQvD,EAAOuD,KAAK,CAAEhB,EAAU,AAACD,GAAQA,EAAKE,MAAM,EAAK,EAAGgB,EAAoBwZ,GAAmBzZ,EAAMvD,MAAM,CAACS,GAAG,CAAC,AAACkhB,GAAMA,EAAEphB,SAAS,CAAC,OAAQqhB,EAAM,EAAE,CAAEvf,EAAQ,EAAE,CAG7T,GAAID,EAAKI,MAAM,EAAIL,GACf,CAAC2a,GAAqBxa,CAAI,CAAC,EAAE,GAC7BA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,CACd,OAGJ,IAAMqf,EAAYzf,CAAI,CAAC,EAAE,CAAGD,EAASqB,EACrC,IAAK9C,EAAI,EAAGA,EAAIyB,EAAQzB,IACpB2B,EAAMvB,IAAI,CAAC+gB,EAAYnhB,EAAI8C,GAE/B,IAAK9C,EAAI,EAAGA,EAAI6B,EAAS7B,IAEjBA,GAAK+gB,GAGLL,CAAAA,EAAK,AAACH,CAAAA,AADNA,CAAAA,EAAU9D,GADE7a,EAAK+I,KAAK,CAAC3K,EAAI+gB,EAAc/gB,GACT,EAClBgH,IAAI,CAAGuZ,EAAQtZ,GAAG,AAAD,EAAK,CAAA,EAEpCjH,GAAKyB,GAILof,CAAAA,EAAM,AAACH,CAAAA,EADPC,CAAAA,EAAK,AAACH,CAAAA,AADNA,CAAAA,EAAU/D,GADE7a,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAQzB,GACH,EAClBgH,IAAI,CAAGwZ,EAAQvZ,GAAG,AAAD,EAAK,CAAA,CACvB,EAAK,CAAA,EAElBjH,GAAKghB,GAGLF,CAAAA,EAAM,AAACL,CAAAA,AADPA,CAAAA,EAAWhE,GADE7a,EAAK+I,KAAK,CAAC3K,EAAIghB,EAAmBhhB,GACb,EAClBgH,IAAI,CAAGyZ,EAASxZ,GAAG,AAAD,EAAK,CAAA,EAE3C2Z,EAAKhf,CAAI,CAAC5B,EAAE,CAAC,EAAE,CACfmR,EAAOzP,CAAI,CAAC1B,EAAE,CACQ,KAAA,IAAXkhB,CAAG,CAAClhB,EAAE,EACbkhB,CAAAA,CAAG,CAAClhB,EAAE,CAAG,EAAE,AAAD,EAEqB,KAAA,IAAxBkhB,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,EAC1Byf,CAAAA,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,CAAG,EAAE,AAAD,EAE3Byf,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAGif,EACzBQ,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAGkf,EACzBO,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAG,KAAK,EACJ,KAAA,IAAfyf,CAAG,CAAClhB,EAAI,EAAE,EACjBkhB,CAAAA,CAAG,CAAClhB,EAAI,EAAE,CAAG,EAAE,AAAD,EAElBkhB,CAAG,CAAClhB,EAAI,EAAE,CAAC,EAAE,CAAG4gB,EACZ5gB,GAAKyB,IACLyf,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAG,KAAK,EAC9Byf,CAAG,CAAClhB,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAG,KAAK,GAEK,KAAA,IAA5Byf,CAAG,CAAClhB,EAAI,EAAIyB,EAAS,EAAE,EAC9Byf,CAAAA,CAAG,CAAClhB,EAAI,EAAIyB,EAAS,EAAE,CAAG,EAAE,AAAD,EAE/Byf,CAAG,CAAClhB,EAAI,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAGof,EAC7BK,CAAG,CAAClhB,EAAI,EAAIyB,EAAS,EAAE,CAAC,EAAE,CAAGqf,EAC7Bnf,EAAMvB,IAAI,CAAC+Q,GAGf,IAAKnR,EAAI,EAAGA,GAAKyB,EAAQzB,IACrB2B,EAAMvB,IAAI,CAAC+Q,EAAOnR,EAAI8C,GAE1B,MAAO,CACH5C,OAAQghB,EACRvf,MAAOA,EACPnC,MAAO0hB,CACX,CACJ,CACJ,CAmBAlE,GAAa5X,cAAc,CAAGmX,GAAmBN,GAA0B7W,cAAc,CAAE,CAIvFvE,OAAQ,CACJI,MAAO,KAAK,EACZQ,OAAQ,GAIRsf,aAAc,EAIdC,kBAAmB,EACvB,EACAtS,OAAQ,CACJC,QAAS,CAAA,CACb,EACAtJ,QAAS,CACLuJ,YAAa,+QAMjB,EAIAsO,WAAY,CACR3Q,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIAqO,UAAW,CACP5Q,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIAsO,WAAY,CACR7Q,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIAuO,YAAa,CACT9Q,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIA0O,YAAa,CACTjR,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIAgO,WAAY,CA2BRvQ,OAAQ,CAOJ+Q,KAAM,sBACV,CACJ,EACAvO,aAAc,CACVC,cAAe,mBACnB,CACJ,GACAmN,GAAoBa,GAAahf,SAAS,CAAE,CACxCyB,cAAe,CACX,YACA,WACA,aACA,cACA,cACH,CACDuL,YAAa,YACbjK,eAAgB,CAAC,oBAAqB,SAAU,eAAe,AACnE,GAMA,AAAC6a,IAAkE,CAAC,oBAAoB,CAjkBxF,WACI,IACIwF,EADEC,EAAM,EAAE,CAQd,MANA,EAAE,CAAC7gB,OAAO,CAACtC,IAAI,CAACyC,UAAW,SAAUoN,CAAG,CAAE/N,CAAC,EACvCqhB,EAAIjhB,IAAI,CAACwb,KAAkE0F,OAAO,CAACvT,IACnFqT,EAAe,CAACA,GAAgB,AAAkB,KAAA,IAAXC,CAAG,CAACrhB,EAAE,AACjD,GAGOohB,EAAe,KAAK,EAAIC,CACnC,EAwjBA3iB,IAA4D+G,kBAAkB,CAAC,MAAOuX,IAqCtF,GAAM,CAAEtX,IAAK6b,EAAqC,CAAE,CAAG,AAAC7iB,IAA6DG,WAAW,CAE1H,CAAE+G,aAAc4b,EAAqC,CAAEviB,OAAQwiB,EAA+B,CAAEtiB,MAAOuiB,EAA8B,CAAE,CAAItjB,GAejJ,OAAMujB,WAAiCJ,GAMnCpf,MAAO,CACHzD,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACmE,IAAI,CAACzB,KAAK,CAAC,IAAI,CAAEC,WAEvG,IAAI,CAACO,OAAO,CAAGwgB,GAA+B,CAC1C5Q,QAAS,CACLvE,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,EACA0I,WAAY,CACRxE,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,CACJ,EAAG,IAAI,CAACnH,OAAO,CACnB,CACAM,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAUImQ,EAAIC,EAAIC,EAAIC,EAAMyQ,EAAUC,EAAU7hB,EAVpCyB,EAASZ,EAAOY,MAAM,CAAEqgB,EAAYjhB,EAAOihB,SAAS,CAAEC,EAAgBlhB,EAAOkhB,aAAa,CAAE9gB,EAAQJ,EAAOI,KAAK,CAAEW,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAG5KkgB,EAAK,EAAE,CAAEC,EAAYvjB,IAA4DG,WAAW,CAAC2Y,GAAG,CAACxZ,SAAS,CAACwD,SAAS,CAAClC,EAAQ,CACzHmC,OAAQA,EACRR,MAAOA,CACX,GAAIihB,EAAYxjB,IAA4DG,WAAW,CAACsjB,GAAG,CAACnkB,SAAS,CAACwD,SAAS,CAAClC,EAAQ,CACpHmC,OAAQqgB,CACZ,GAAIngB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAG1B,IAAIqC,CAAAA,EAAUJ,CAAK,GAGnB,IAAKzB,EAAIyB,EAAQzB,GAAK6B,EAAS7B,IAC3B4hB,EAAWK,EAAU/hB,MAAM,CAACF,EAAIyB,EAAO,CACvCogB,EAAWK,EAAUhiB,MAAM,CAACF,EAAI8hB,EAAU,CAC1C3Q,EAAOyQ,CAAQ,CAAC,EAAE,CAClB3Q,EAAKuQ,GAAsCI,CAAQ,CAAC,EAAE,CAAIG,EAAgBF,CAAQ,CAAC,EAAE,EACrF3Q,EAAKsQ,GAAsCI,CAAQ,CAAC,EAAE,CAAIG,EAAgBF,CAAQ,CAAC,EAAE,EACrF7Q,EAAK4Q,CAAQ,CAAC,EAAE,CAChBI,EAAG5hB,IAAI,CAAC,CAAC+Q,EAAMF,EAAID,EAAIE,EAAG,EAC1BvP,EAAMvB,IAAI,CAAC+Q,GACX3R,EAAMY,IAAI,CAAC,CAAC6Q,EAAID,EAAIE,EAAG,EAE3B,MAAO,CACHhR,OAAQ8hB,EACRrgB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAyBAmiB,GAAyBvc,cAAc,CAAGsc,GAA+BH,GAAsCnc,cAAc,CAAE,CAY3HvE,OAAQ,CAMJI,MAAO,EACPQ,OAAQ,GAIRqgB,UAAW,GAIXC,cAAe,CACnB,EAKAhR,WAAY,CAKRxE,OAAQ,CAIJsC,UAAW,EAKXC,UAAW,KAAK,CACpB,CACJ,EAMAgC,QAAS,CACLvE,OAAQ,CACJsC,UAAW,EACXC,UAAW,KAAK,CACpB,CACJ,EACAzJ,QAAS,CACLuJ,YAAa,+LACjB,EACAF,OAAQ,CACJC,QAAS,CAAA,CACb,EACAI,aAAc,CACVC,cAAe,UACnB,EACAH,UAAW,CACf,GACA4S,GAAgCE,GAAyB3jB,SAAS,CAAE,CAChEoD,SAAU,mBACVmJ,eAAgB,CAAC,MAAO,SAAS,CACjCxJ,eAAgB,CAAC,SAAU,YAAa,gBAAgB,CACxDuJ,cAAe,CAAC,UAAW,aAAa,CACxC7K,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1CuL,YAAa,QACjB,GACAyC,EAAoCJ,OAAO,CAACsU,IAC5CjjB,IAA4D+G,kBAAkB,CAAC,kBAAmBkc,IAwClG,GAAM,CAAEnK,IAAK4K,EAA6B,CAAE1c,IAAK2c,EAA6B,CAAE,CAAG,AAAC3jB,IAA6DG,WAAW,CAEtJ,CAAE+G,aAAc0c,EAA6B,CAAEtjB,MAAOujB,EAAsB,CAAEtjB,OAAQujB,EAAuB,CAAEtjB,QAASujB,EAAwB,CAAEtjB,MAAOujB,EAAsB,CAAE,CAAItkB,GAe3L,OAAMukB,WAAyBN,GAM3BO,eAAehhB,CAAI,CAAE5B,CAAC,CAAE,CAGpB,OAAO6iB,AAFUjhB,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAAC,EAAE,CACjD4B,CAAI,CAAC5B,EAAI,EAAE,CAAC,EAAE,CAAG4B,CAAI,CAAC5B,EAAI,EAAE,CAAC,EAAE,CAAG4B,CAAI,CAAC5B,EAAI,EAAE,CAAC,EAAE,CAClC,EAAI,EAC1B,CAGA8iB,YAAYC,CAAS,CAAE,CACnB,IAAM3gB,EAAQ,IAAI,CAACA,KAAK,CAAElB,EAAU,IAAI,CAACA,OAAO,CAAE5B,EAAS,IAAI,CAACmD,YAAY,CAAE6Q,EAAemP,GAAyBM,IAClHA,AAAqB,IAArBA,EAAUjhB,MAAM,CAAQ0F,EAAe,IAAI,CAACA,YAAY,EACvD,CAAA,IAAI,CAACA,YAAY,CACdpF,EAAMvE,GAAG,CAACqD,EAAQL,MAAM,CAAC0G,cAAc,CAAA,EAU/C,OATKC,GACD+a,GAAuB,UACnBrhB,EAAQL,MAAM,CAAC0G,cAAc,CAC7B,sCAAuC,CAAA,EAAMjI,EAAO8C,KAAK,EAM1D,CAAC,CAAEmR,CAAAA,AAJY,CAACjU,EAAQkI,EAAa,CAACwb,KAAK,CAAC,SAAU1jB,CAAM,EAC/D,OAAOA,GAAUA,EAAOK,SAAS,CAACM,QAAQ,EACtCiB,EAAQL,MAAM,CAACoiB,aAAa,AACpC,IAC2B3P,CAAW,CAC1C,CACA4P,MAAMC,CAAU,CAAEnO,CAAE,CAAEoO,CAAK,CAAEC,CAAa,CAAEC,CAAU,CAAE,CACpD,OAAOhB,GAA8BtN,EAAMoO,CAAAA,IAAUC,EAAgBF,EAAaG,CAAS,EAC/F,CACAC,MAAMvc,CAAI,CAAEC,CAAG,CAAE,CACb,OAAOqb,GAA8Btb,EAAOC,EAChD,CACAuc,eAAe5hB,CAAI,CAAE,CACjB,IAAM6hB,EAAc,EAAE,CAClBC,EAAK,EACT1O,EACOhV,EAAI,EACXmjB,EAAa,EAAGQ,EAAa/hB,CAAI,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAAC,EAAE,CACpDyhB,EAAgB,EAAGD,EACnB,KAAQpjB,EAAI4B,EAAKE,MAAM,CAAE9B,IACrBojB,EAAQ,IAAI,CAACR,cAAc,CAAChhB,EAAM5B,GAClCgV,EAAK,IAAI,CAACuO,KAAK,CAAC3hB,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAE4B,CAAI,CAAC5B,EAAE,CAAC,EAAE,EAKtC0jB,EAAK,IAAI,CAACR,KAAK,CAACC,EAAYnO,EAAIoO,EAAOC,EAAeM,GAGtDF,EAAYrjB,IAAI,CAAC,CAFT,IAAI,CAACoH,YAAY,CAAC3H,SAAS,CAAC,IAAI,CAACG,EAAE,CACvCojB,EAAQpT,KAAKC,GAAG,CAAC,EAAK,CAAA,AAAC+E,EAAK0O,EAAM,CAAA,GAAM,IACpB,EAExBL,EAAgBD,EAChBD,EAAaO,EACbC,EAAa3O,EAEjB,OAAOyO,CACX,CACAtK,OAAOvX,CAAI,CAAEwX,CAAO,CAAErX,CAAG,CAAEoE,CAAU,CAAElF,CAAK,CAAEjB,CAAC,CAAE0B,CAAI,CAAE,CACnD,OAAO0gB,GAA8BpkB,SAAS,CAACkI,YAAY,CAACxE,GAAQ,EAAE,CAAEE,EAAM,AAAa,KAAA,IAAN5B,EAAoB,EAAIA,EAAGmG,EAAYiT,EAAS,AAAiB,KAAA,IAAVnY,EAAwB,GAAKA,EAAOc,EACpL,CACA6hB,OAAOniB,CAAM,CAAER,CAAK,CAAEf,CAAM,CAAE,CAC1B,OAAOkiB,GAA8BpkB,SAAS,CACzCgI,sBAAsB,CAACvE,EAAQR,EAAOf,GAAUuB,CACzD,CACAD,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMgjB,EAAU,EAAE,CAAEniB,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEmC,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEskB,EAAa,EAAE,CACjGC,EAAI/jB,EAAI,EAAGgkB,EAAU,EAAGC,EAASC,EAA0BC,EAA0BC,EAAS,KAElG,GAAI,CAAC,IAAI,CAACtB,WAAW,CAAClhB,CAAI,CAAC,EAAE,EACzB,OAGJ,IAAM6hB,EAAc,IAAI,CAACD,cAAc,CAAC5hB,GAElCyiB,EAAU,IAAI,CAACT,MAAM,CAAC/iB,EAAOyjB,aAAa,CAAE,EAAGb,GAAcc,EAAU,IAAI,CAACX,MAAM,CAAC/iB,EAAOoiB,aAAa,CAAE,EAAGQ,GAE5Ge,EAAiB,EAAK3jB,CAAAA,EAAOyjB,aAAa,CAAG,CAAA,EAAIG,EAAiB,EAAK5jB,CAAAA,EAAOoiB,aAAa,CAAG,CAAA,EAEpG,KAAQjjB,EAAI4B,EAAKE,MAAM,CAAE9B,IAEjBA,GAAKa,EAAOyjB,aAAa,EAEzBJ,CAAAA,EADAF,EAAU,IAAI,CAAC7K,MAAM,CAACsK,EAAaS,EAAiBG,EAASG,EAAgB,EAAGxkB,EAAG0B,EAAK,CAAC,EAAE,AACnE,EAGxB1B,GAAKa,EAAOoiB,aAAa,GAEzBkB,EADAF,EAAU,IAAI,CAAC9K,MAAM,CAACsK,EAAaU,EAAiBI,EAASE,EAAgB,EAAGzkB,EAAG0B,EAAK,CAAC,EAAE,CAG3FoiB,EAAW1jB,IAAI,CADf2jB,EAAKzB,GAA8B0B,EAAUC,IAGzCH,EAAWhiB,MAAM,EAAIjB,EAAO6jB,YAAY,EACxCN,CAAAA,EAASN,EAAWnZ,KAAK,CAAC,CAAC9J,EAAO6jB,YAAY,EACzC7R,MAAM,CAAC,CAACC,EAAM6R,IAAS7R,EAAO6R,GAAQ9jB,EAAO6jB,YAAY,AAAD,EAEjEb,EAAQzjB,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAE+jB,EAAIK,EAAO,EAClCziB,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAAC,CAAC2jB,EAAIK,EAAO,GAG/B,MAAO,CACHlkB,OAAQ2jB,EACRliB,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAoBAmjB,GAAiBvd,cAAc,CAAGsd,GAAuBL,GAA8Bjd,cAAc,CAAE,CAMnGvE,OAAQ,CAIJyjB,cAAe,GAIfrB,cAAe,GAIfyB,aAAc,GAKdnd,eAAgB,QACpB,EACAqd,WAAY,CAIRrY,OAAQ,CAIJsC,UAAW,EAQXC,UAAW,SACf,CACJ,EACAC,aAAc,CACVC,cAAe,UACnB,EACA3J,QAAS,CACLuJ,YAAa,kPAQjB,CACJ,GACA4T,GAAwBG,GAAiB3kB,SAAS,CAAE,CAChDuM,eAAgB,EAAE,CAClBD,cAAe,CAAC,aAAa,CAC7BlJ,SAAU,UACVL,eAAgB,CAAC,gBAAiB,gBAAgB,CAClDtB,cAAe,CAAC,IAAK,SAAS,CAC9BiX,eAAgB,CAAC,IAAK,IAAK,SAAS,CACpC1L,YAAa,GACjB,GACAyC,EAAoCJ,OAAO,CAACsV,IAC5CjkB,IAA4D+G,kBAAkB,CAAC,UAAWkd,IAmC1F,GAAM,CAAEjb,KAAMmd,EAAkB,CAAE,CAAIzmB,IAEhC,CAAEyF,OAAQihB,EAAY,CAAEpf,IAAKqf,EAA0B,CAAE,CAAG,AAACrmB,IAA6DG,WAAW,CAErI,CAAEI,OAAQ+lB,EAAoB,CAAEpf,aAAcqf,EAA0B,CAAE9a,QAAS+a,EAAqB,CAAE/lB,MAAOgmB,EAAmB,CAAE,CAAI/mB,GAehJ,OAAMgnB,WAAsBL,GAMxB5iB,MAAO,CACHzD,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACmE,IAAI,CAACzB,KAAK,CAAC,IAAI,CAAEC,WACvG,IAAMuL,EAAgB,IAAI,CAAC7D,KAAK,AAG5B,CAAA,IAAI,CAACnH,OAAO,GAGRgkB,GAAsB,IAAI,CAACG,UAAU,IACjC,IAAI,CAACnkB,OAAO,CAAC0jB,UAAU,EACvB,IAAI,CAAC1jB,OAAO,CAAC0jB,UAAU,CAACrY,MAAM,EAC9B,CAAC,IAAI,CAACrL,OAAO,CAAC0jB,UAAU,CAACrY,MAAM,CAACuC,SAAS,GACzC,IAAI,CAAC5N,OAAO,CAACmkB,UAAU,CAAG,IAAI,CAACA,UAAU,CAAG,EAC5C,IAAI,CAACC,SAAS,CAAC,QAAS,KAAK,EAAG,IAAI,CAACljB,KAAK,CAAClB,OAAO,CAACqkB,MAAM,EACzD,IAAI,CAACrkB,OAAO,CAAC0jB,UAAU,CAACrY,MAAM,CAACuC,SAAS,CACpC,IAAI,CAACzG,KAAK,EAEd,IAAI,CAACnH,OAAO,CAACskB,QAAQ,EACrB,IAAI,CAACtkB,OAAO,CAACskB,QAAQ,CAACjZ,MAAM,EAC5B,CAAC,IAAI,CAACrL,OAAO,CAACskB,QAAQ,CAACjZ,MAAM,CAACuC,SAAS,GACvC,IAAI,CAAC5N,OAAO,CAACmkB,UAAU,CAAG,IAAI,CAACA,UAAU,CAAG,EAC5C,IAAI,CAACC,SAAS,CAAC,QAAS,KAAK,EAAG,IAAI,CAACljB,KAAK,CAAClB,OAAO,CAACqkB,MAAM,EACzD,IAAI,CAACrkB,OAAO,CAACskB,QAAQ,CAACjZ,MAAM,CAACuC,SAAS,CAClC,IAAI,CAACzG,KAAK,GAKtB,IAAI,CAACod,SAAS,CAAG,CACbC,MAAO,IAAI,CAACxkB,OAAO,CAACskB,QAAQ,CAACE,KAAK,CAClCC,WAAY,CAChB,EACA,IAAI,CAACC,WAAW,CAAG,CACfF,MAAO,IAAI,CAACD,SAAS,CAACC,KAAK,CAACxF,MAAM,CAAC,IAAI,CAAChf,OAAO,CAAC0jB,UAAU,CAACc,KAAK,EAChEC,WAAY,IAAI,CAACF,SAAS,CAACC,KAAK,CAAC5jB,MAAM,AAC3C,GAGJ,IAAI,CAACuG,KAAK,CAAG6D,CACjB,CACAsB,QAAQ9B,CAAK,CAAE,CACX,MAAO,CAACA,EAAMzF,CAAC,CAAEyF,EAAM0Y,MAAM,CAAE1Y,EAAMma,IAAI,CAAC,AAC9C,CACA7b,WAAY,CACR,IAAM3H,EAAY,IAAI,CAAEyjB,EAAY,CAAC,aAAc,WAAW,CAC9D1nB,IAA6CS,WAAW,CAACgF,MAAM,CAAC7F,SAAS,CAACgM,SAAS,CAACtJ,KAAK,CAAC2B,GAC1FA,EAAUc,MAAM,CAAC3C,OAAO,CAAC,SAAUkL,CAAK,EACpC,CAACA,EAAM0Y,MAAM,CAAE1Y,EAAMma,IAAI,CAAC,CAACrlB,OAAO,CAAC,SAAUyM,CAAK,CAAEjN,CAAC,EACnC,OAAViN,GACAvB,CAAAA,CAAK,CAACoa,CAAS,CAAC9lB,EAAE,CAAC,CACfqC,EAAU8K,KAAK,CAACC,QAAQ,CAACH,EAAO,CAAA,EAAI,CAEhD,EACJ,EACJ,CACA3M,SAAU,CAEN,IAAI,CAAC8K,KAAK,CAAG,KACb,IAAI,CAAC2a,SAAS,CAAG,IAAI,CAACA,SAAS,EAAI,IAAI,CAACA,SAAS,CAACzlB,OAAO,GACzD,IAAI,CAAC0lB,WAAW,CAAG,IAAI,CAACA,WAAW,EAAI,IAAI,CAACA,WAAW,CAAC1lB,OAAO,GAC/D5B,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACsC,OAAO,CAACI,KAAK,CAAC,IAAI,CAAEC,UAC9G,CACAuH,WAAY,CACR,IAAM7F,EAAY,IAAI,CAAE4I,EAAiB5I,EAAUc,MAAM,CAAE+H,EAAkB7I,EAAUnB,OAAO,CAAE+kB,EAAiB5jB,EAAUqjB,KAAK,CAAEra,EAAe,CAC7InK,QAAS,CACLoK,QAASJ,EAAgBI,OAAO,AACpC,CACJ,EAAG4a,EAAe,CAAC,EAAE,CAAE,EAAE,CAAC,CACtBxa,EAAOD,EAAeR,EAAenJ,MAAM,CAE/C,KAAO2J,KAECyZ,GAAsBxZ,AAD1BA,CAAAA,EAAQT,CAAc,CAACQ,EAAa,AAAD,EACH0a,QAAQ,GACpCD,CAAY,CAAC,EAAE,CAAC9lB,IAAI,CAAC,CACjBwL,MAAOF,EAAME,KAAK,CAClBC,MAAOH,EAAMya,QAAQ,CACrBra,OAAQ,CAACoZ,GAAsBxZ,EAAMya,QAAQ,CACjD,GAEAjB,GAAsBxZ,EAAM0a,UAAU,GACtCF,CAAY,CAAC,EAAE,CAAC9lB,IAAI,CAAC,CACjBwL,MAAOF,EAAME,KAAK,CAClBC,MAAOH,EAAM0a,UAAU,CACvBta,OAAQ,CAACoZ,GAAsBxZ,EAAMya,QAAQ,CACjD,GAIR,CAAC,OAAQ,SAAS,CAAC3lB,OAAO,CAAC,CAAC8L,EAAUtM,KAClCqC,EAAUc,MAAM,CAAG+iB,CAAY,CAAClmB,EAAE,CAClCqC,EAAUnB,OAAO,CAAGikB,GAAoBja,CAAe,CAAC,CAAC,EAAEoB,EAAS,IAAI,CAAC,CAAC,EAAEC,QAAU,CAAC,EAAGlB,GAC1FhJ,EAAU+I,KAAK,CAAG/I,CAAS,CAAC,CAAC,KAAK,EAAEiK,EAAS,CAAC,CAAC,CAE/CjK,EAAUqjB,KAAK,CAAG,AAACrjB,CAAAA,CAAS,CAAC,CAAC,EAAEiK,EAAS,KAAK,CAAC,CAAC,CAACoZ,KAAK,EAAI,EAAE,AAAD,EAAG/a,KAAK,CAACtI,CAAS,CAAC,CAAC,EAAEiK,EAAS,KAAK,CAAC,CAAC,CAACqZ,UAAU,EAAI,GAChHjnB,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACkK,SAAS,CAAChK,IAAI,CAACmE,GACrGA,CAAS,CAAC,CAAC,KAAK,EAAEiK,EAAS,CAAC,CAAC,CAAGjK,EAAU+I,KAAK,AACnD,GAEA/I,EAAUc,MAAM,CAAG8H,EACnB5I,EAAUnB,OAAO,CAAGgK,EACpB7I,EAAUqjB,KAAK,CAAGO,CACtB,CACAI,YAAa,CAGT,IAAMJ,EAAiB,IAAI,CAACP,KAAK,AAEjC,CAAA,IAAI,CAACA,KAAK,CAAG,IAAI,CAACE,WAAW,CAACF,KAAK,CACnChnB,IAA4DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACqoB,UAAU,CAACnoB,IAAI,CAAC,IAAI,EAEtG,IAAI,CAAC6nB,SAAS,EAAI,IAAI,CAAC7kB,OAAO,CAACskB,QAAQ,CAACE,KAAK,CAAC5jB,MAAM,EACpD,IAAI,CAACikB,SAAS,CAACO,IAAI,GAEvB,IAAI,CAACZ,KAAK,CAAGO,CACjB,CACAzkB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAM0lB,EAAgB1lB,EAAOsI,UAAU,CAAGtI,EAAO2lB,WAAW,CAC5DX,EAAO,EAAE,CAAEY,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAC7BC,EAAUC,EAAS5mB,EAAGgJ,EAAI,EAAG4b,EAAa,EAAE,CAChD,IAAItlB,CAAAA,EAAOqC,KAAK,CAACG,MAAM,CACnBjB,EAAOsI,UAAU,CAAGtI,EAAO6jB,YAAY,AAAD,GAgB1C,IAAK1kB,EAAI,EAZT2mB,EAAWjoB,IAA4DG,WAAW,CAAC2Y,GAAG,CAACxZ,SAAS,CAACwD,SAAS,CAAClC,EAAQ,CAC/GmC,OAAQZ,EAAO2lB,WAAW,CAC1BvlB,MAAOJ,EAAOI,KAAK,AACvB,GACA2lB,EAAUloB,IAA4DG,WAAW,CAAC2Y,GAAG,CAACxZ,SAAS,CAACwD,SAAS,CAAClC,EAAQ,CAC9GmC,OAAQZ,EAAOsI,UAAU,CACzBlI,MAAOJ,EAAOI,KAAK,AACvB,GACA0lB,EAAWA,EAASzmB,MAAM,CAC1B0mB,EAAUA,EAAQ1mB,MAAM,CAGZF,GAAK2mB,EAAS7kB,MAAM,CAAE9B,IAC1BklB,GAAsB0B,CAAO,CAAC5mB,EAAE,GAChCklB,GAAsB0B,CAAO,CAAC5mB,EAAE,CAAC,EAAE,GACnCklB,GAAsByB,CAAQ,CAAC3mB,EAAIumB,EAAa,GAChDrB,GAAsByB,CAAQ,CAAC3mB,EAAIumB,EAAa,CAAC,EAAE,GACnDV,EAAKzlB,IAAI,CAAC,CACNumB,CAAQ,CAAC3mB,EAAIumB,EAAa,CAAC,EAAE,CAC7B,EACA,KACAI,CAAQ,CAAC3mB,EAAIumB,EAAa,CAAC,EAAE,CACzBK,CAAO,CAAC5mB,EAAE,CAAC,EAAE,CACpB,EAKT,IAAKA,EAAI,EAAGA,EAAI6lB,EAAK/jB,MAAM,CAAE9B,IACzBymB,EAAMrmB,IAAI,CAACylB,CAAI,CAAC7lB,EAAE,CAAC,EAAE,EACrB0mB,EAAMtmB,IAAI,CAAC,CAAC,EAAG,KAAMylB,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAAC,EAapC,IAAKA,EAAI,EAHT4kB,EAAaA,AAPbA,CAAAA,EAAalmB,IAA4DG,WAAW,CAAC2Y,GAAG,CAACxZ,SAAS,CAACwD,SAAS,CAAC,CACzGG,MAAO8kB,EACPjnB,MAAOknB,CACX,EAAG,CACCjlB,OAAQZ,EAAO6jB,YAAY,CAC3BzjB,MAAO,CACX,EAAC,EACuBf,MAAM,CAGlBF,EAAI6lB,EAAK/jB,MAAM,CAAE9B,IAErB6lB,CAAI,CAAC7lB,EAAE,CAAC,EAAE,EAAI4kB,CAAU,CAAC,EAAE,CAAC,EAAE,GAC9BiB,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAAG4kB,CAAU,CAAC5b,EAAE,CAAC,EAAE,CAC7B0d,CAAK,CAAC1mB,EAAE,CAAG,CAAC,EAAG4kB,CAAU,CAAC5b,EAAE,CAAC,EAAE,CAAE6c,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAAC,CACxC6lB,AAAe,OAAfA,CAAI,CAAC7lB,EAAE,CAAC,EAAE,EACV6lB,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAAG,EACb0mB,CAAK,CAAC1mB,EAAE,CAAC,EAAE,CAAG,IAGd6lB,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAAGilB,GAA2BY,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAC9C4kB,CAAU,CAAC5b,EAAE,CAAC,EAAE,EACpB0d,CAAK,CAAC1mB,EAAE,CAAC,EAAE,CAAGilB,GAA2BY,CAAI,CAAC7lB,EAAE,CAAC,EAAE,CAC/C4kB,CAAU,CAAC5b,EAAE,CAAC,EAAE,GAExBA,KAGR,MAAO,CACH9I,OAAQ2lB,EACRlkB,MAAO8kB,EACPjnB,MAAOknB,CACX,EACJ,CACJ,CAqBAtB,GAAchgB,cAAc,CAAG+f,GAAoBJ,GAA2B3f,cAAc,CAAE,CAC1FvE,OAAQ,CAIJ2lB,YAAa,GAIbrd,WAAY,GAIZub,aAAc,EACdjjB,OAAQ,EACZ,EAIAmjB,WAAY,CAORc,MAAO,EAAE,CACTnZ,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIA0W,SAAU,CAONE,MAAO,EAAE,CACTnZ,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,KAAK,CACpB,CACJ,EAIAzF,UAAW,EACXC,aAAc,GACdC,aAAc,GACdC,MAAO,CAAA,EACPC,OAAQ,CACJC,MAAO,CACHC,KAAM,CACFC,KAAM,CACV,CACJ,CACJ,EACAvE,QAAS,CACLuJ,YAAa,mJAIjB,EACAG,aAAc,CACVC,cAAe,UACnB,EACA6X,eAAgB,CACpB,GACA7B,GAAqBI,GAAcpnB,SAAS,CAAE,CAC1C+C,eAAgB,CAAC,aAAc,cAAe,eAAe,CAE7DtB,cAAe,CAAC,IAAK,SAAU,OAAO,CACtCiX,eAAgB,CAAC,IAAK,IAAK,SAAU,OAAO,CAC5C1L,YAAa,IAEbnB,cAAegb,GACf/a,iBAAkB,AAAC1L,IAA8CS,WAAW,CAACgF,MAAM,CAAC7F,SAAS,CAAC8L,gBAAgB,CAC9GC,SAAU,AAAC3L,IAA8CS,WAAW,CAACgF,MAAM,CAAC7F,SAAS,CAAC+L,QAAQ,CAC9FE,WAAY,AAAC7L,IAA8CS,WAAW,CAACgF,MAAM,CAAC7F,SAAS,CAACiM,UAAU,AACtG,GACAvL,IAA4D+G,kBAAkB,CAAC,OAAQ2f,IAwCvF,GAAM,CAAE1f,IAAKohB,EAAyB,CAAE,CAAG,AAACpoB,IAA6DG,WAAW,CAE9G,CAAEI,OAAQ8nB,EAAmB,CAAE5nB,MAAO6nB,EAAkB,CAAEhoB,MAAOioB,EAAkB,CAAE/nB,QAASgoB,EAAoB,CAAE,CAAI9oB,IAU9H,SAAS+oB,GAAsBvU,CAAK,EAChC,OAAOA,EAAMC,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,EACnC,OAAOD,EAAOC,CAClB,EACJ,CAUA,SAASqU,GAAsB1b,CAAK,EAChC,MAAO,AAACA,CAAAA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,CAAGA,CAAK,CAAC,EAAE,AAAD,EAAK,CAC9C,CAqBA,MAAM2b,WAAqBP,GAMvBtlB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGwlB,EAAWzmB,EAAOymB,QAAQ,CAAE9f,EAAelI,EAAO8C,KAAK,CAACvE,GAAG,CAACgD,EAAO0G,cAAc,EAAGT,EAAaU,GAAc3H,UAAU,MAAQ,EAAE,CAAE0nB,EAAM,EAAE,CAAE5lB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEgoB,EAAoB,EAAE,CAAEC,EAAoB,EAAE,CAC3TC,EAAiBC,EAAiBC,EAAcC,EAA4DC,EAAU9nB,EAAG+nB,EAAO,CAAA,EAGpI/lB,EAAQ,EACR,GAAI,CAACwF,EAAc,CACfyf,GAAmB,UACfpmB,EAAO0G,cAAc,CACrB,sCAAuC,CAAA,EAAMjI,EAAO8C,KAAK,EAC7D,MACJ,CAEA,GAAI,CAACV,CAAAA,EAAKI,MAAM,EAAIL,CAAK,GAAOylB,GAAqBtlB,CAAI,CAAC,EAAE,GACxDA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EACbgF,GAML,IAFA4gB,EAAkBN,GAAsBxlB,CAAI,CAACI,EAAM,EAE5CA,EAAQP,EAAS,GAEpBkmB,EAAkBD,EAElBK,EAAOL,AADPA,CAAAA,EAAkBN,GAAsBxlB,CAAI,CAACI,EAAM,CAAA,GACzB2lB,EAE1BC,EAjDDI,AAiDsCN,EAAiB5gB,CAAU,CAAC9E,EAAM,CAEvEwlB,EAAkBpnB,IAAI,CAAC2nB,EAAOH,EAAe,GAC7CH,EAAkBrnB,IAAI,CAAC2nB,EAAO,EAAIH,GAClC5lB,IAEJ,IAAKhC,EAAIgC,EAAQ,EAAGhC,EAAI6B,EAAS7B,IACzBA,EAAIgC,EAAQ,IAEZwlB,EAAkBjjB,KAAK,GACvBkjB,EAAkBljB,KAAK,GAEvBojB,EAAkBD,EAElBK,EAAOL,AADPA,CAAAA,EAAkBN,GAAsBxlB,CAAI,CAAC5B,EAAE,CAAA,EACtB2nB,EAEzBC,EAjELI,AAiE0CN,EAAiB5gB,CAAU,CAAC9G,EAAE,CAEnEwnB,EAAkBpnB,IAAI,CAAC2nB,EAAOH,EAAe,GAC7CH,EAAkBrnB,IAAI,CAAC2nB,EAAO,EAAIH,IAGtCC,EAAuBV,GAAsBM,GAG7CK,EAtFDG,WAAW5qB,AAsFS,CAAA,IAAO,IAAO,CAAA,EADhB6qB,AADMf,GAAsBK,GACLK,CACU,CAAE,EAtFxCM,OAAO,CAsFoCb,IACvDC,EAAInnB,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAE8nB,EAAS,EAC5BnmB,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAAC0nB,GAEf,MAAO,CACH5nB,OAAQqnB,EACR5lB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAoBA6nB,GAAajiB,cAAc,CAAG4hB,GAAmBF,GAA0B1hB,cAAc,CAAE,CAIvFvE,OAAQ,CACJI,MAAO,KAAK,EAMZsG,eAAgB,SAIhB+f,SAAU,CACd,CACJ,GACAP,GAAoBM,GAAarpB,SAAS,CAAE,CACxCoD,SAAU,kBACd,GACA1C,IAA4D+G,kBAAkB,CAAC,MAAO4hB,IAoCtF,GAAM,CAAE3hB,IAAK0iB,EAA8B,CAAE,CAAG,AAAC1pB,IAA6DG,WAAW,CAEnH,CAAEI,OAAQopB,EAAwB,CAAEnpB,QAASopB,EAAyB,CAAEnpB,MAAOopB,EAAuB,CAAE,CAAInqB,IASlH,SAASoqB,GAAkC9mB,CAAI,CAAEE,CAAI,CAAE5B,CAAC,CAAEyB,CAAM,CAAER,CAAK,EACnE,IAAMwnB,EAAM7mB,CAAI,CAAC5B,EAAI,EAAE,CAACiB,EAAM,CAAGW,CAAI,CAAC5B,EAAIyB,EAAS,EAAE,CAACR,EAAM,CAC5D,MAAO,CAD6DS,CAAI,CAAC1B,EAAI,EAAE,CAClEyoB,EAAI,AACrB,CAeA,MAAMC,WAA0BN,GAM5B5mB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IACIb,EAAG2oB,EADDlnB,EAASZ,EAAOY,MAAM,CAAER,EAAQJ,EAAOI,KAAK,CAAES,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG8mB,EAAK,EAAE,CAAEjnB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAE/J,IAAIkC,CAAAA,EAAKI,MAAM,EAAIL,CAAK,GAInB6mB,GAA0B1mB,CAAI,CAAC,EAAE,GAItC,IAAK5B,EAAKyB,EAAS,EAAIzB,EAAI6B,EAAS7B,IAChC2oB,EAAUH,GAAkC9mB,EAAME,EAAM5B,EAAGyB,EAAQR,GACnE2nB,EAAGxoB,IAAI,CAACuoB,GACRhnB,EAAMvB,IAAI,CAACuoB,CAAO,CAAC,EAAE,EACrBnpB,EAAMY,IAAI,CAACuoB,CAAO,CAAC,EAAE,EAMzB,OAJAA,EAAUH,GAAkC9mB,EAAME,EAAM5B,EAAGyB,EAAQR,GACnE2nB,EAAGxoB,IAAI,CAACuoB,GACRhnB,EAAMvB,IAAI,CAACuoB,CAAO,CAAC,EAAE,EACrBnpB,EAAMY,IAAI,CAACuoB,CAAO,CAAC,EAAE,EACd,CACHzoB,OAAQ0oB,EACRjnB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAmBAkpB,GAAkBtjB,cAAc,CAAGmjB,GAAwBH,GAA+BhjB,cAAc,CAAE,CACtGvE,OAAQ,CACJI,MAAO,CACX,CACJ,GACAonB,GAAyBK,GAAkB1qB,SAAS,CAAE,CAClDoD,SAAU,UACd,GACA1C,IAA4D+G,kBAAkB,CAAC,WAAYijB,IAoC3F,GAAM,CAAEvG,IAAK0G,EAA0B,CAAE,CAAG,AAACnqB,IAA6DG,WAAW,CAE/G,CAAEM,MAAO2pB,EAAmB,CAAE,CAAI1qB,GAexC,OAAM2qB,WAAsBF,GAMxBrnB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMmoB,EAAW,KAAK,CAACxnB,UAAUd,KAAK,CAAC,IAAI,CAAEC,WAAasoB,EAAYD,EAAQ9oB,MAAM,CAAC4B,MAAM,CAAEF,EAAOtC,EAAOE,KAAK,CAC5GQ,EAAI,EAAGyB,EAASZ,EAAOY,MAAM,CAAG,EACpC,GAAKunB,GAGL,KAAOhpB,EAAIipB,EAAWjpB,IAClBgpB,EAAQxpB,KAAK,CAACQ,EAAE,CAAIgpB,EAAQ9oB,MAAM,CAACF,EAAE,CAAC,EAAE,CAAG4B,CAAI,CAACH,EAAO,CAAC,EAAE,CAAG,IAC7DunB,EAAQ9oB,MAAM,CAACF,EAAE,CAAC,EAAE,CAAGgpB,EAAQxpB,KAAK,CAACQ,EAAE,CACvCyB,IAEJ,OAAOunB,EACX,CACJ,CAqBAD,GAAc3jB,cAAc,CAAG0jB,GAAoBD,GAA2BzjB,cAAc,CAAE,CAC1FC,QAAS,CACL6jB,YAAa,GACjB,CACJ,GACAxqB,IAA4D+G,kBAAkB,CAAC,OAAQsjB,IAqCvF,GAAM,CAAErjB,IAAKyjB,EAAyB,CAAE,CAAG,AAACzqB,IAA6DG,WAAW,CAE9G,CAAE0Z,SAAU6Q,EAAqB,CAAEpqB,MAAOqqB,EAAkB,CAAEpqB,OAAQqqB,EAAmB,CAAEnqB,MAAOoqB,EAAkB,CAAE,CAAInrB,GAehI,OAAMorB,WAAqBL,GAMvB3nB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAM2G,EAAelI,EAAO8C,KAAK,CAACvE,GAAG,CAACgD,EAAO0G,cAAc,EAAG7F,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEiqB,EAAM,EAAE,CAAE9nB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEkqB,EAAU,CAACN,GAAsBxnB,CAAI,CAAC,EAAE,EAC9K+nB,EAAW,EAAE,CAAE3pB,EAAI,EAAG4pB,EAAc,EAAGC,EAAY,EAAGC,EAAgB,EAAGC,EAAc,EAAG5iB,EAE9F,GAAIK,EASA,IARAL,EAASK,EAAa3H,SAAS,CAAC,KAEhC8pB,EAAW,CAACjoB,CAAI,CAAC,EAAE,CAAEkoB,EAAY,CACjCE,EAAgBJ,EACZ9nB,CAAI,CAAC,EAAE,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CACxB6nB,EAAIrpB,IAAI,CAACupB,GACThoB,EAAMvB,IAAI,CAACsB,CAAI,CAAC,EAAE,EAClBlC,EAAMY,IAAI,CAACupB,CAAQ,CAAC,EAAE,EACd3pB,EAAI4B,EAAKE,MAAM,CAAE9B,IAIjB6pB,EADAE,AAFJA,CAAAA,EAAcL,EACV9nB,CAAI,CAAC5B,EAAE,CAAC,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,AAAD,EACL8pB,EACFF,EAAcziB,CAAM,CAACnH,EAAE,CAE9B+pB,IAAgBD,EACTF,EAGAA,EAAcziB,CAAM,CAACnH,EAAE,CAGvC2pB,EAAW,CAACjoB,CAAI,CAAC1B,EAAE,CAAE6pB,EAAU,CAE/BD,EAAcC,EACdC,EAAgBC,EAChBN,EAAIrpB,IAAI,CAACupB,GACThoB,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAACupB,CAAQ,CAAC,EAAE,MAGzB,CACDN,GAAmB,UACfxoB,EAAO0G,cAAc,CACrB,sCAAuC,CAAA,EAAMjI,EAAO8C,KAAK,EAC7D,MACJ,CACA,MAAO,CACHlC,OAAQupB,EACR9nB,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAyBAgqB,GAAapkB,cAAc,CAAGmkB,GAAmBJ,GAA0B/jB,cAAc,CAAE,CACvFsJ,OAAQ,CACJC,QAAS,CAAA,CACb,EAIA9N,OAAQ,CAEJI,MAAO,KAAK,EACZQ,OAAQ,KAAK,EAKb8F,eAAgB,QACpB,EACAlC,QAAS,CACLC,cAAe,CACnB,CACJ,GACAgkB,GAAoBE,GAAaxrB,SAAS,CAAE,CACxC+C,eAAgB,KAAK,CACzB,GACArC,IAA4D+G,kBAAkB,CAAC,MAAO+jB,IAoCtF,IAAMvnB,GAAW,AAACvD,IAA6DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACgsB,UAAU,CASnH,SAASC,GAAmBve,CAAK,CAAEwe,CAAY,EAC3C,IAAMC,EAAQze,EAAMpM,MAAM,CAACG,aAAa,CACpC1B,EAAMiC,EAAImqB,EAAMroB,MAAM,CAE1B,IADA,AAACpD,IAA6DG,WAAW,CAAC6G,GAAG,CAAC1H,SAAS,CAACgsB,UAAU,CAAChsB,SAAS,CAACksB,EAAa,CAAChsB,IAAI,CAACwN,GACzH1L,KAGC0L,CAAK,CAFT3N,EAAO,YAAcosB,CAAK,CAACnqB,EAAE,CAEd,EAAI0L,CAAK,CAAC3N,EAAK,CAACqsB,OAAO,EAClC1e,CAAK,CAAC3N,EAAK,CAACuC,OAAO,GAEvBoL,CAAK,CAAC3N,EAAK,CAAG,IAEtB,CAsCA,GAAM,CAAE2H,IAAK2kB,EAAiC,CAAE,CAAG,AAAC3rB,IAA6DG,WAAW,CAEtH,CAAEM,MAAOmrB,EAA0B,CAAErrB,OAAQsrB,EAA2B,CAAEpgB,QAASqgB,EAA4B,CAAEtrB,QAASurB,EAA4B,CAAE,CAAIrsB,GAelK,OAAMssB,WAA6BL,GAM/B7c,QAAQ9B,CAAK,CAAE,CACX,MAAO,CAACA,EAAMif,CAAC,CAAC,AACpB,CACA3gB,WAAY,CACR,IAAM3H,EAAY,IAAI,CACtB,KAAK,CAAC2H,UAAUtJ,KAAK,CAAC2B,GACtBA,EAAUc,MAAM,CAAC3C,OAAO,CAAC,SAAUkL,CAAK,EACpCrJ,EAAU5C,aAAa,CAACe,OAAO,CAAC,SAAUyM,CAAK,EACvCud,GAA6B9e,CAAK,CAACuB,EAAM,GACzCvB,CAAAA,CAAK,CAAC,OAASuB,EAAM,CAAI5K,EAAU8K,KAAK,CAACC,QAAQ,CAAC1B,CAAK,CAACuB,EAAM,CAAE,CAAA,EAAK,CAE7E,EACJ,GAIA5K,EAAUuoB,YAAY,CAAGvoB,EAAUQ,KAAK,CAACuK,QAAQ,CAAC/K,EAAUwoB,QAAQ,CAAE,CAAA,EAC1E,CACAje,aAAazJ,CAAM,CAAE,CACjB,IAAMd,EAAY,IAAI,CAAEyoB,EAAkB,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAG/M,EAAsB1b,EAAU5C,aAAa,CAACqC,MAAM,CACjI+oB,EAAWxoB,EAAUuoB,YAAY,CAAEle,EAAO,EAAE,CAAE2R,EAAU3S,EAAOD,EAAetI,EAAOrB,MAAM,CAAE9B,EACjG,KAAOyL,KAAgB,CAEnB,IAAKzL,EAAI,EADT0L,EAAQvI,CAAM,CAACsI,EAAa,CAChBzL,EAAI+d,EAAqB/d,IAE7BwqB,GAA6B9e,CAAK,CADtC2S,EAAWhc,EAAU5C,aAAa,CAACO,EAAE,CACW,GAC5C8qB,CAAc,CAAC9qB,EAAE,CAACI,IAAI,CAAC,CAEnBwL,MAAOF,EAAME,KAAK,CAClBC,MAAOH,CAAK,CAAC,OAAS2S,EAAS,CAC/BvS,OAAQ,CAAA,CACZ,EAAG,CAECF,MAAOif,EACPhf,MAAOH,CAAK,CAAC,OAAS2S,EAAS,CAC/BvS,OAAQ,CAAA,CACZ,EAAG,CAECF,MAAOif,EACPhf,MAAO,KACPC,OAAQ,CAAA,CACZ,GAGR+e,EAAWnf,EAAME,KAAK,AAC1B,CAIA,OAHAkf,EAAetqB,OAAO,CAAC,AAACuqB,IACpBre,EAAOA,EAAKwT,MAAM,CAAC,KAAK,CAACtT,aAAa1O,IAAI,CAACmE,EAAW0oB,GAC1D,GACOre,CACX,CAEAse,gBAAiB,CACb,IACIC,EAAcxf,EAAcC,EAAO1L,EADjCqC,EAAY,IAAI,CAAE6oB,EAAe7oB,EAAU5C,aAAa,AAE1D4C,CAAAA,EAAUnB,OAAO,CAACiqB,UAAU,CAACxc,OAAO,GACpClD,EAAepJ,EAAUc,MAAM,CAACrB,MAAM,CAItCopB,EAAahL,MAAM,CAAC,CAAC,CAAA,EAAM,EAAE1f,OAAO,CAAC,CAAC6d,EAAUS,KAE5C,IADA9e,EAAIyL,EACGzL,KACH0L,EAAQrJ,EAAUc,MAAM,CAACnD,EAAE,CACtBqe,GAMD3S,EAAMzF,CAAC,CAAGyF,CAAK,CAAC2S,EAAS,CACzB3S,EAAM0f,SAAS,CAAG/M,EAClB3S,EAAMG,KAAK,CAAGH,CAAK,CAAC,OAAS2S,EAAS,CACtC4M,EAAevf,CAAK,CAAC,YAAc2S,EAAS,CAExCS,GACApT,CAAAA,CAAK,CAAC,YAAcwf,CAAY,CAACpM,EAAI,EAAE,CAAC,CAAGpT,EAAM2f,SAAS,AAAD,EAExD3f,EAAMyf,UAAU,EACjBzf,CAAAA,EAAMyf,UAAU,CAAG,EAAE,AAAD,EAExBzf,EAAMyf,UAAU,CAAC,EAAE,CAAGzf,EAAM2f,SAAS,CACjCJ,EACIA,GAAgBA,EAAab,OAAO,CAChCa,EACA,MAnBZvf,CAAK,CAAC,YAAcwf,CAAY,CAACpM,EAAI,EAAE,CAAC,CACpCpT,EAAM2f,SAAS,CAqB3B,KAAK,CAACL,eACD9sB,IAAI,CAACmE,EACd,GAER,CACAb,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAGIyqB,EAAcC,EAAYna,EAAS7C,EAASid,EAAeC,EAAKzrB,EAH9DyB,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG4pB,EAAY,IAAI,CAAC7qB,EAAO8qB,SAAS,CAAG,YAAY,CAE1JC,EAAK,EAAE,CAAEjqB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAG/B,GAAIkC,CAAAA,CAAAA,EAAKI,MAAM,CAAGL,CAAK,GAClBgpB,GAA6B7oB,CAAI,CAAC,EAAE,GACrCA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAGlB,IAAK9B,EAAIyB,EAAS,EAAGzB,GAAK6B,EAAUJ,EAAQzB,GAAKyB,EAC7C2P,EAAU1P,EAAKiJ,KAAK,CAAC3K,EAAIyB,EAAS,EAAGzB,GACrCuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAS,EAAGzB,GACrCurB,EAAana,EAAQtP,MAAM,CAC3BwpB,EAAela,CAAO,CAACma,EAAa,EAAE,CAEtCE,EAAMC,EADE,IAAI,CAACG,cAAc,CAACtd,IAE5Bid,EAASI,EAAGxrB,IAAI,CAAC,CAACkrB,EAAa,CAC1BpL,MAAM,CAACuL,IACZ9pB,EAAMvB,IAAI,CAACkrB,GACX9rB,EAAMY,IAAI,CAACwrB,CAAE,CAACJ,EAAS,EAAE,CAAC7gB,KAAK,CAAC,IAQpC,OAFA,IAAI,CAACkgB,QAAQ,CAAGzZ,CAAO,CAAC,EAAE,CAAG,AAAEka,CAAAA,EAAela,CAAO,CAAC,EAAE,AAAD,EACnDma,EAAc9pB,EACX,CACHvB,OAAQ0rB,EACRjqB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACAqsB,eAAe3rB,CAAM,CAAE,CACnB,IAAMgH,EAAQhH,CAAM,CAACA,EAAO4B,MAAM,CAAG,EAAE,CAAC,EAAE,CACtCkF,EAAO,CAAC2V,IAAU1V,EAAM0V,IAM5B,OALAzc,EAAOM,OAAO,CAAC,SAAU8R,CAAC,EACtBtL,EAAOgJ,KAAKvM,GAAG,CAACuD,EAAMsL,CAAC,CAAC,EAAE,EAC1BrL,EAAM+I,KAAKxM,GAAG,CAACyD,EAAKqL,CAAC,CAAC,EAAE,CAC5B,GAEO,CADO,AAACtL,CAAAA,EAAOC,EAAMC,CAAI,EAAK,EACtBF,EAAMC,EAAKC,EAAM,AACpC,CACA4kB,kBAAkB5rB,CAAM,CAAE,CACtB,IAAM6rB,EAAO7rB,CAAM,CAAC,EAAE,CAAGA,CAAM,CAAC,EAAE,CAWlC,MAX0C,CACtC,KACA,KACAA,CAAM,CAAC,EAAE,CAAG6rB,EACZ7rB,AAAY,EAAZA,CAAM,CAAC,EAAE,CAAOA,CAAM,CAAC,EAAE,CACzBA,CAAM,CAAC,EAAE,CACTA,AAAY,EAAZA,CAAM,CAAC,EAAE,CAAOA,CAAM,CAAC,EAAE,CACzBA,CAAM,CAAC,EAAE,CAAG6rB,EACZ,KACA,KACH,AAEL,CACAC,mBAAmB9rB,CAAM,CAAE,CACvB,IAAM6rB,EAAO7rB,CAAM,CAAC,EAAE,CAAGA,CAAM,CAAC,EAAE,CAWlC,MAX0C,CACtCA,CAAM,CAAC,EAAE,CAAG6rB,AAAO,IAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,KAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,OAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,OAAPA,EACZ7rB,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CAAG6rB,AAAO,OAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,OAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,KAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,IAAPA,EACf,AAEL,CACAE,mBAAmB/rB,CAAM,CAAE,CACvB,IAAM6rB,EAAO7rB,CAAM,CAAC,EAAE,CAAGA,CAAM,CAAC,EAAE,CAWlC,MAX0C,CACtC,KACAA,CAAM,CAAC,EAAE,CAAG6rB,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,KAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,KAAPA,EACZ7rB,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CAAG6rB,AAAO,KAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,AAAO,KAAPA,EACZ7rB,CAAM,CAAC,EAAE,CAAG6rB,EACZ,KACH,AAEL,CACJ,CAoBArB,GAAqBtlB,cAAc,CAAGklB,GAA2BD,GAAkCjlB,cAAc,CAAE,CAI/GvE,OAAQ,CACJI,MAAO,KAAK,EACZQ,OAAQ,GAMRkqB,UAAW,UACf,EACAjd,OAAQ,CACJC,QAAS,CAAA,CACb,EACAud,oBAAqB,CAAA,EACrBf,WAAY,CACRxc,QAAS,CAAA,EACTwd,OAAQ,mBACZ,EACApd,aAAc,CACVC,cAAe,UACnB,CACJ,GACAub,GAA4BG,GAAqB1sB,SAAS,CAAE,CACxDoD,SAAU,eACV3B,cAAe,CAAC,KAAM,KAAM,KAAM,KAAM,IAAK,KAAM,KAAM,KAAM,KAAK,CACpEuL,YAAa,IACbgf,WA7RJ,cAA+B/nB,GAM3BmqB,iBAAkB,CACdnC,GAAmB,IAAI,CAAE,kBAC7B,CAEA3pB,SAAU,CACN2pB,GAAmB,IAAI,CAAE,kBAC7B,CACJ,CAiRA,GAMAvrB,IAA4D+G,kBAAkB,CAAC,cAAeilB,IAoC9F,GAAM,CAAElT,IAAK6U,EAAyB,CAAE,CAAG,AAAC3tB,IAA6DG,WAAW,CAE9G,CAAE+G,aAAc0mB,EAAyB,CAAErtB,OAAQstB,EAAmB,CAAEptB,MAAOqtB,EAAkB,CAAExtB,MAAOytB,EAAkB,CAAE,CAAIruB,GAexI,OAAMsuB,WAAqBL,GAMvB7qB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAGIyO,EAAYtP,EAHV+X,EAAUlX,EAAOkX,OAAO,CAAE9W,EAAQJ,EAAOI,KAAK,CAEpD0rB,EAAM,EAAE,CAAEhrB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAGhC,GAAIuY,AAAmB,IAAnBA,EAAQjW,MAAM,EAAUiW,CAAO,CAAC,EAAE,EAAIA,CAAO,CAAC,EAAE,CAAE,CAClD0U,GAAmB,gGAEnB,MACJ,CAEA,IAAMtU,EAAM,KAAK,CAAC3W,UAAUtD,IAAI,CAAC,IAAI,CAAEoB,EAAQ,CAC3C2B,MAAOA,EACPQ,OAAQsW,CAAO,CAAC,EAAE,AACtB,GAEMK,EAAM,KAAK,CAAC5W,UAAUtD,IAAI,CAAC,IAAI,CAAEoB,EAAQ,CAC3C2B,MAAOA,EACPQ,OAAQsW,CAAO,CAAC,EAAE,AACtB,GAEA,GAAI,CAACI,GAAO,CAACC,EACT,OAEJ,IAAMC,EAAgBN,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAC7C,IAAK/X,EAAI,EAAGA,EAAIoY,EAAI5Y,KAAK,CAACsC,MAAM,CAAE9B,IAC9BsP,EAAagd,GAA0B,AAACnU,CAAAA,EAAI3Y,KAAK,CAACQ,EAAIqY,EAAc,CAChED,EAAI5Y,KAAK,CAACQ,EAAE,AAAD,EACXoY,EAAI5Y,KAAK,CAACQ,EAAE,CACZ,KACJ2sB,EAAIvsB,IAAI,CAAC,CAACgY,EAAIzW,KAAK,CAAC3B,EAAE,CAAEsP,EAAW,EACnC3N,EAAMvB,IAAI,CAACgY,EAAIzW,KAAK,CAAC3B,EAAE,EACvBR,EAAMY,IAAI,CAACkP,GAEf,MAAO,CACHpP,OAAQysB,EACRhrB,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAwBAktB,GAAatnB,cAAc,CAAGonB,GAAmBH,GAA0BjnB,cAAc,CAAE,CAOvFvE,OAAQ,CACJY,OAAQ,KAAK,EAObsW,QAAS,CAAC,GAAI,GAAG,AACrB,CACJ,GACAwU,GAAoBG,GAAa1uB,SAAS,CAAE,CACxCoD,SAAU,MACVL,eAAgB,CAAC,UAAU,AAC/B,GACArC,IAA4D+G,kBAAkB,CAAC,MAAOinB,IA2EzD,IAAME,GAHZ,CACnBC,iBAZJ,SAA0B9e,CAAG,CAAE+e,CAAQ,CAAEC,CAAQ,EAC7C,OAAOhf,EAAI8E,MAAM,CAAC,CAACC,EAAMka,IAAW,CAChChd,KAAKxM,GAAG,CAACsP,CAAI,CAAC,EAAE,CAAEka,CAAM,CAACF,EAAS,EAClC9c,KAAKvM,GAAG,CAACqP,CAAI,CAAC,EAAE,CAAEka,CAAM,CAACD,EAAS,EACrC,CAAE,CAACE,OAAOC,SAAS,CAAE,CAACD,OAAOC,SAAS,CAAC,CAC5C,CAQA,KAaY,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACH,CAiBC,CAAExnB,IAAKynB,EAAwB,CAAE,CAAG,AAACzuB,IAA6DG,WAAW,CAE7G,CAAEM,MAAOiuB,EAAiB,CAAEnuB,OAAQouB,EAAkB,CAAE,CAAIjvB,GAelE,OAAMkvB,WAAoBH,GAMtB3rB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAKImQ,EAAIC,EAAIC,EAAIC,EAAM5C,EAASgf,EAAUvtB,EALnCyB,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAEvG0rB,EAAK,EAAE,CAEY7rB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAEzC,IAAIqC,CAAAA,EAAUJ,CAAK,GAGnB,IAAKzB,EAAIyB,EAAQzB,GAAK6B,EAAS7B,IAC3BmR,EAAOzP,CAAI,CAAC1B,EAAI,EAAE,CAClBuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAQzB,GAIjCgR,EAAK,AAACC,CAAAA,AAFNA,CAAAA,EAAKsc,AADLA,CAAAA,EAAWX,GAA0BC,gBAAgB,CAACte,EARpD,EAAU,EAQ4D,CAC3D,CAAC,EAAE,AAAD,EACf2C,CAAAA,EAAKqc,CAAQ,CAAC,EAAE,AAAD,CACH,EAAK,EACjBC,EAAGptB,IAAI,CAAC,CAAC+Q,EAAMF,EAAID,EAAIE,EAAG,EAC1BvP,EAAMvB,IAAI,CAAC+Q,GACX3R,EAAMY,IAAI,CAAC,CAAC6Q,EAAID,EAAIE,EAAG,EAE3B,MAAO,CACHhR,OAAQstB,EACR7rB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAwBA8tB,GAAYloB,cAAc,CAAGgoB,GAAkBD,GAAyB/nB,cAAc,CAAE,CAcpFvE,OAAQ,CACJI,MAAO,KAAK,EACZQ,OAAQ,EACZ,EACAoN,UAAW,EACXiC,QAAS,CACLvE,OAAQ,CAOJuC,UA9HJ,UAkIID,UAAW,CACf,CACJ,EACAkC,WAAY,CACRxE,OAAQ,CAOJuC,UAvIJ,UA2IID,UAAW,CACf,CACJ,EACAE,aAAc,CACVC,cAAe,UACnB,CACJ,GACAqe,GAAmBC,GAAYtvB,SAAS,CAAE,CACtCuM,eAAgB,CAAC,MAAO,SAAS,CACjCnJ,SAAU,gBACVL,eAAgB,CAAC,SAAS,CAC1BuJ,cAAe,CAAC,UAAW,aAAa,CACxC7K,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1CuL,YAAa,QACjB,GACAyC,EAAoCJ,OAAO,CAACigB,IAC5C5uB,IAA4D+G,kBAAkB,CAAC,KAAM6nB,IAwCrF,GAAM,CAAE5nB,IAAK+nB,EAAoC,CAAE,CAAG,AAAC/uB,IAA6DG,WAAW,CAEzH,CAAEI,OAAQyuB,EAA8B,CAAExuB,QAASyuB,EAA+B,CAAExuB,MAAOyuB,EAA6B,CAAE,CAAIxvB,GAepI,OAAMyvB,WAAgCJ,GAMlCtrB,MAAO,CACH,KAAK,CAACA,KAAKzB,KAAK,CAAC,IAAI,CAAEC,WAEvB,IAAI,CAACO,OAAO,CAAG0sB,GAA8B,CACzC9c,QAAS,CACLvE,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,EACA0I,WAAY,CACRxE,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,CACJ,EAAG,IAAI,CAACnH,OAAO,CACnB,CACAM,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAKImQ,EAAIC,EAAIC,EAAIC,EAAMC,EAAS7C,EAAS7C,EAAO1L,EALzCyB,EAASZ,EAAOY,MAAM,CAAEqsB,EAAajtB,EAAOktB,OAAO,CAAEC,EAAantB,EAAOotB,UAAU,CAAEvsB,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAEpKosB,EAAK,EAAE,CAEPvsB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAGtB,GAAIkC,CAAAA,CAAAA,EAAKI,MAAM,CAAGL,CAAK,GAClBksB,GAAgC/rB,CAAI,CAAC,EAAE,GACxCA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAGlB,IAAK9B,EAAIyB,EAAQzB,GAAK6B,EAAS7B,IAC3BoR,EAAU1P,EAAKiJ,KAAK,CAAC3K,EAAIyB,EAAQzB,GACjCuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAQzB,GAKjCmR,EAAOzF,AAJPA,CAAAA,EAAQ,KAAK,CAAClK,UAAU,CACpBG,MAAOyP,EACP5R,MAAO+O,CACX,EAAG1N,EAAM,EACIc,KAAK,CAAC,EAAE,CAErBsP,EAAKD,AADLA,CAAAA,EAAKtF,EAAMlM,KAAK,CAAC,EAAE,AAAD,EACP,CAAA,EAAIsuB,CAAS,EACxB5c,EAAKF,EAAM,CAAA,EAAIgd,CAAS,EACxBE,EAAG9tB,IAAI,CAAC,CAAC+Q,EAAMF,EAAID,EAAIE,EAAG,EAC1BvP,EAAMvB,IAAI,CAAC+Q,GACX3R,EAAMY,IAAI,CAAC,CAAC6Q,EAAID,EAAIE,EAAG,EAE3B,MAAO,CACHhR,OAAQguB,EACRvsB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAqBAquB,GAAwBzoB,cAAc,CAAGwoB,GAA8BH,GAAqCroB,cAAc,CAAE,CACxHsJ,OAAQ,CACJC,QAAS,CAAA,CACb,EACAtJ,QAAS,CACLuJ,YAAa,iJACjB,EACA/N,OAAQ,CACJY,OAAQ,GAKRssB,QAAS,GAKTE,WAAY,EAChB,EAIAld,WAAY,CACRxE,OAAQ,CAIJsC,UAAW,EAQXC,UAAW,KAAK,CACpB,CACJ,EAMAgC,QAAS,CACLvE,OAAQ,CACJsC,UAAW,CACf,CACJ,EACAE,aAAc,CACVC,cAAe,UACnB,CAYJ,GACA0e,GAA+BG,GAAwB7vB,SAAS,CAAE,CAC9DuM,eAAgB,CAAC,MAAO,SAAS,CACjCD,cAAe,CAAC,UAAW,aAAa,CACxCvJ,eAAgB,CAAC,SAAU,UAAW,aAAa,CACnDK,SAAU,kBACV3B,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1CiX,eAAgB,CAAC,IAAK,IAAK,MAAO,SAAS,CAC3C1L,YAAa,QACjB,GACAyC,EAAoCJ,OAAO,CAACwgB,IAC5CnvB,IAA4D+G,kBAAkB,CAAC,iBAAkBooB,IAwCjG,GAAM,CAAEnoB,IAAKyoB,EAA0B,CAAE,CAAG,AAACzvB,IAA6DG,WAAW,CAE/G,CAAEM,MAAOivB,EAAmB,CAAE,CAAIhwB,IAUxC,SAASiwB,GAAsBhxB,CAAC,CAAEL,CAAC,EAC/B,OAAOirB,WAAW5qB,EAAE8qB,OAAO,CAACnrB,GAChC,CAmGA,MAAMsxB,WAAsBH,GACxB/a,aAAc,CAMV,KAAK,IAAIzS,WACT,IAAI,CAACI,cAAc,CAAG,KAAK,CAC/B,CAMAS,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMa,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAE+uB,EAAwB1tB,EAAO0tB,qBAAqB,CAAEC,EAAY3tB,EAAO2tB,SAAS,CAElIC,EAA4B5tB,EAAO4tB,yBAAyB,CAAEnH,EAAWzmB,EAAOymB,QAAQ,CAAErmB,EAAQJ,EAAOI,KAAK,CAAEytB,EAAU,EAAE,CAAE/sB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAChJmvB,EAAqB9tB,EAAO4tB,yBAAyB,CAAEG,EAG3DC,EAAejtB,CAAI,CAAC,EAAE,CAAC,EAAE,CAAEktB,EAAaC,EAA4BC,EAAcC,EAAoB,EAAGC,EAASC,EAAaC,EAAUC,EAAcC,EAAO1tB,CAAI,CAAC,EAAE,CAAC,EAAE,CAAE2tB,EAAiBvoB,EAAMC,EAAKuoB,EACtM,IAAIvuB,CAAAA,GAASW,EAAKE,MAAM,AAAD,GAGvB,IAAK0tB,EAAM,EAAGA,EAAMvuB,EAAOuuB,IACvBX,EAAe7e,KAAKvM,GAAG,CAAC7B,CAAI,CAAC4tB,EAAI,CAAC,EAAE,CAAEX,GACtCS,EAAOtf,KAAKxM,GAAG,CAAC5B,CAAI,CAAC4tB,EAAI,CAAC,EAAE,CAAEnB,GAAsBiB,EAAMhI,IAS9D,IAPAsH,EAAY,AAAChtB,CAAI,CAAC4tB,EAAI,CAAC,EAAE,CAAGF,EAAQ,EAAI,GACxCR,EAjFGW,AAiF0BZ,EAAcS,EAE3CP,EA7EGJ,AA4EHA,CAAAA,EAAqB9tB,EAAO4tB,yBAAyB,AAAD,EAC2BK,EAC/EJ,EAAQtuB,IAAI,CAAC,CAACsB,CAAI,CAACT,EAAM,CAAEquB,EAAK,EAChC3tB,EAAMvB,IAAI,CAACsB,CAAI,CAACT,EAAM,EACtBzB,EAAMY,IAAI,CAACiuB,GAAsBiB,EAAMhI,IAClCkI,EAAMvuB,EAAQ,EAAGuuB,EAAM5tB,EAAKE,MAAM,CAAE0tB,IAQrC,GAPAN,EAAUttB,CAAI,CAAC4tB,EAAM,EAAE,CAAC,EAAE,CAC1BL,EAAcvtB,CAAI,CAAC4tB,EAAM,EAAE,CAAC,EAAE,CAC9BJ,EAAWxtB,CAAI,CAAC4tB,EAAM,EAAE,CAAC,EAAE,CAC3BH,EAAeztB,CAAI,CAAC4tB,EAAM,EAAE,CAAC,EAAE,CAC/BxoB,EAAOpF,CAAI,CAAC4tB,EAAI,CAAC,EAAE,CACnBvoB,EAAMrF,CAAI,CAAC4tB,EAAI,CAAC,EAAE,CAEdL,AAAgB,OAAhBA,GACAE,AAAiB,OAAjBA,GACAH,AAAY,OAAZA,GACAE,AAAa,OAAbA,GACApoB,AAAS,OAATA,GACAC,AAAQ,OAARA,EAAc,KA9GMgoB,EAAmBS,EAnC3BT,EAA8BK,EAoEzCK,EAAMC,EAAMN,EAAMO,EAAWC,EAAMC,EAAMC,EAAOC,EAAOC,EAhDzCC,EAAKC,EAAMC,EAAIH,EAAKI,EAAMC,EAAKC,EAAQC,EAgDrDd,EA8Ecf,EA9ERgB,EA8EmBX,EA9EbK,EA8EgCA,EA9E1BO,EA8EgCd,EA9ErBe,EA8EiDX,EA9E3CY,EA8EwDb,EA9ElDc,EA8E2DZ,EA9EpDa,EA8E8DZ,EA9EvDa,EA8EqErB,EAA7HS,EA7EZ,AAAIK,IAASC,EACT,AAAID,AAAS,IAATA,EACO,AAACL,EAAOO,EAAY7f,KAAKxM,GAAG,CAACssB,EAAMC,GACtCT,EAAOO,EACP7f,KAAKxM,GAAG,CAACssB,EAAMC,GAEhB,AAACT,EAAOO,EAAY7f,KAAKvM,GAAG,CAACwsB,EAAOD,GACvCV,EAAOO,EACP7f,KAAKvM,GAAG,CAACwsB,EAAOD,GAEjBE,EA5CyBjB,EAgHyBL,EAhHNc,EAgHiBb,EAAxDU,EA/GZ,AAAIN,AAAsB,IAAtBA,EACO,AAACjoB,AA8GkCA,EA9G3B0oB,EA8G2B1oB,EA9GI0oB,EAE3C,AAACzoB,AA4G4CA,EA5GtCyoB,EA4GsCzoB,EA5GRyoB,EAvCpBT,EAoJsBA,EApJQK,EAoJsBA,EAhIjDa,EAgIfnB,EAnJZ,AAAI,AAAuB,IAAtBC,GAA2BhoB,AAmJiCA,EAnJ3BqoB,GACjCL,AAAsB,KAAtBA,GAA4BjoB,AAkJqCA,EAlJ9BsoB,EAC7B,EAEJ,GAeyBc,EAiIqCxB,EAjI/ByB,EAiI0Cd,EAjItCW,EAiIuDrB,EAjIlDyB,EAiIgE3B,EAjI1D4B,EAiI8E/B,EAjIzEgC,EAiIoFjC,EAjI5EkC,EAiImGhC,EAEzJM,EApGLJ,AAkGKA,CAAAA,EAhIZ,AAAIwB,IAAQC,EACR,AAAY,IAARD,GAAcE,EAAKH,GAGnBC,AAAQ,KAARA,GAAeE,EAAKH,EAFb,AAACI,IAASE,EAAUA,EAASnC,GAAsBiC,EAAOC,EAAK,GAKnED,EAEJG,CAuHuL,EAClL3B,CAAAA,EAzGLW,AAyGkCF,EAAiBD,CAAI,EAElDZ,EAAQtuB,IAAI,CAAC,CAACsB,CAAI,CAAC8tB,EAAI,CAAEnB,GAAsBiB,EAAMhI,GAAU,EAC/D3lB,EAAMvB,IAAI,CAACsB,CAAI,CAAC8tB,EAAI,EACpBhwB,EAAMY,IAAI,CAACiuB,GAAsBiB,EAAMhI,IACvC2H,EAAoBL,EACpBA,EAAYI,EACZH,EAAeU,CACnB,CAEJ,MAAO,CACHrvB,OAAQwuB,EACR/sB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAgBA8uB,GAAclpB,cAAc,CAAGgpB,GAAoBD,GAA2B/oB,cAAc,CAAE,CAC1FyJ,UAAW,EACXH,OAAQ,CACJC,QAAS,CAAA,CACb,EACAlF,OAAQ,CACJC,MAAO,CACHgnB,cAAe,CACnB,CACJ,EAIA7vB,OAAQ,CACJY,OAAQ,KAAK,EASbgtB,0BAA2B,IAM3BF,sBAAuB,GAOvBC,UAAW,IAMXvtB,MAAO,EAMPqmB,SAAU,CACd,CACJ,GACA5oB,IAA4D+G,kBAAkB,CAAC,OAAQ6oB,IAsCvF,GAAM,CAAE5oB,IAAKirB,EAAyB,CAAE,CAAG,AAACjyB,IAA6DG,WAAW,CAE9G,CAAEK,QAAS0xB,EAAoB,CAAEzxB,MAAO0xB,EAAkB,CAAE5xB,OAAQ6xB,EAAmB,CAAE,CAAI1yB,GAgDnG,OAAM2yB,WAAqBJ,GAMvBnvB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGkvB,EAAM,EAAE,CAAErvB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACtIQ,EAAGiB,EAAQ,GAAIgwB,EAGnB,IAAIvvB,CAAAA,EAAKI,MAAM,EAAIL,CAAK,GASxB,IALImvB,GAAqBhvB,CAAI,CAAC,EAAE,GAC5BX,CAAAA,EAAQJ,EAAOI,KAAK,AAAD,EAIlBjB,EAAIyB,EAAQzB,EAAI6B,EAAS7B,IAC1BixB,EAAWC,AA3DvB,SAAsCxvB,CAAI,CAAEE,CAAI,CAAE5B,CAAC,CAAEyB,CAAM,CAAER,CAAK,EAO9D,IAAIkwB,EAAWC,EAef,OAXIA,EAHAnwB,EAAQ,EAGDkwB,AADPA,CAAAA,EAAYvvB,CAAI,CAAC5B,EAAIyB,EAAO,AAAD,EAEvB,AAACG,CAAAA,CAAI,CAAC5B,EAAE,CAAGmxB,CAAQ,EAAKA,EAAY,IACpC,KAKGA,AADPA,CAAAA,EAAYvvB,CAAI,CAAC5B,EAAIyB,EAAO,CAACR,EAAM,AAAD,EAE9B,AAACW,CAAAA,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAAGkwB,CAAQ,EAAKA,EAAY,IAC3C,KAED,CAACzvB,CAAI,CAAC1B,EAAE,CAAEoxB,EAAK,AAC1B,EAoCoD1vB,EAAME,EAAM5B,EAAGyB,EAAQR,GAC/D+vB,EAAI5wB,IAAI,CAAC6wB,GACTtvB,EAAMvB,IAAI,CAAC6wB,CAAQ,CAAC,EAAE,EACtBzxB,EAAMY,IAAI,CAAC6wB,CAAQ,CAAC,EAAE,EAE1B,MAAO,CACH/wB,OAAQ8wB,EACRrvB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CA4BAuxB,GAAa3rB,cAAc,CAAGyrB,GAAmBF,GAA0BvrB,cAAc,CAAE,CACvFvE,OAAQ,CACJI,MAAO,EACPQ,OAAQ,CACZ,CACJ,GACAqvB,GAAoBC,GAAa/yB,SAAS,CAAE,CACxCoD,SAAU,gBACd,GACA1C,IAA4D+G,kBAAkB,CAAC,MAAOsrB,IA+CtF,GAAM,CAAErrB,IAAK2rB,EAAyB,CAAE,CAAG,AAAC3yB,IAA6DG,WAAW,CAE9G,CAAE0Z,SAAU+Y,EAAqB,CAAEnyB,MAAOoyB,EAAkB,CAAE,CAAInzB,IAUxE,SAASozB,GAAqBn0B,CAAC,CAAEL,CAAC,EAC9B,OAAOirB,WAAW5qB,EAAE8qB,OAAO,CAACnrB,GAChC,CAeA,MAAMy0B,WAAqBJ,GAMvB7vB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGwlB,EAAWzmB,EAAOymB,QAAQ,CAGpIoK,EAAM,EAAE,CAAE/vB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAC5BmyB,EAAO,EAAGC,EAAO,EAAG3wB,EAAQJ,EAAOI,KAAK,CAAEe,EAAQ,EAAG6vB,EAAUC,EAAQC,EAASC,EAAShyB,EAAGE,EAChG,IAAKwB,CAAAA,EAAKI,MAAM,CAAGL,CAAK,GAexB,IAZI6vB,GAAsB1vB,CAAI,CAAC,EAAE,EAC7B1B,EAAS0B,GAMTX,EAAQ+O,KAAKxM,GAAG,CAACvC,EAAOW,CAAI,CAAC,EAAE,CAACE,MAAM,CAAG,GACzC5B,EAAS0B,EACJ7B,GAAG,CAAC,AAACkN,GAAUA,CAAK,CAAChM,EAAM,GAG7Be,EAAQP,GAEPqwB,AADJA,CAAAA,EAASN,GAAqBtxB,CAAM,CAAC8B,EAAM,CAAG9B,CAAM,CAAC8B,EAAQ,EAAE,CAAEslB,EAAQ,EAC5D,EACTqK,GAAQG,EAGRF,GAAQ5hB,KAAKC,GAAG,CAAC6hB,GAErB9vB,IAKJ,IAFA+vB,EAAUP,GAAqBG,EAAQlwB,CAAAA,EAAS,CAAA,EAAI6lB,GACpD0K,EAAUR,GAAqBI,EAAQnwB,CAAAA,EAAS,CAAA,EAAI6lB,GAC/CtnB,EAAIgC,EAAOhC,EAAI6B,EAAS7B,IAErB8xB,AADJA,CAAAA,EAASN,GAAqBtxB,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CAAEsnB,EAAQ,EACpD,GACTqK,EAAOG,EACPF,EAAO,IAGPD,EAAO,EACPC,EAAO5hB,KAAKC,GAAG,CAAC6hB,IAGpBC,EAAUP,GAAqB,AAACO,CAAAA,EAAWtwB,CAAAA,EAAS,CAAA,EAAKkwB,CAAG,EAAKlwB,EAAQ6lB,GAKrEuK,EADAG,AAAY,IAHhBA,CAAAA,EAAUR,GAAqB,AAACQ,CAAAA,EAAWvwB,CAAAA,EAAS,CAAA,EAAKmwB,CAAG,EAAKnwB,EAAQ6lB,EAAQ,EAIlE,IAINyK,AAAY,IAAZA,EACM,EAGAP,GAAqB,IAAO,IAAO,CAAA,EAAKO,EAAUC,CAAO,EAAK1K,GAE7EoK,EAAItxB,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAE6xB,EAAS,EAC5BlwB,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAACyxB,GAEf,MAAO,CACH3xB,OAAQwxB,EACR/vB,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAqBAiyB,GAAarsB,cAAc,CAAGmsB,GAAmBF,GAA0BjsB,cAAc,CAAE,CACvFvE,OAAQ,CACJymB,SAAU,EACVrmB,MAAO,CACX,CACJ,GACAvC,IAA4D+G,kBAAkB,CAAC,MAAOgsB,IAsCtF,GAAM,CAAE/rB,IAAKusB,EAAgC,CAAE,CAAG,AAACvzB,IAA6DG,WAAW,CAErH,CAAEI,OAAQizB,EAA0B,CAAEhzB,QAASizB,EAA2B,CAAEhzB,MAAOizB,EAAyB,CAAE,CAAIh0B,GAexH,OAAMi0B,WAA4BJ,GAM9B9vB,MAAO,CACH,KAAK,CAACA,KAAKzB,KAAK,CAAC,IAAI,CAAEC,WAEvB,IAAI,CAACO,OAAO,CAAGkxB,GAA0B,CACrCE,aAAc,CACV/lB,OAAQ,CACJuC,UAAW,IAAI,CAACzG,KAAK,AACzB,CACJ,CACJ,EAAG,IAAI,CAACnH,OAAO,CACnB,CACAM,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAM0xB,EAAU1xB,EAAOkX,OAAO,CAAC,EAAE,CAAEya,EAAU3xB,EAAOkX,OAAO,CAAC,EAAE,CAAErW,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAEzI2wB,EAAK,EAAE,CAAE9wB,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAC3B+O,EAAiBmkB,EAAIC,EAAGC,EAAI,KAAcrF,EAAUvtB,EAExD,GAAI6B,EAAU0wB,GACV,CAACJ,GAA4BvwB,CAAI,CAAC,EAAE,GACpCA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,CACd,OAIJ,IAAI+wB,EAAiB,CAAA,EAAM7pB,EAAI,EAI/B,IAAKhJ,EAAIuyB,EAAU,EAAGvyB,EAAI6B,EAAS7B,IAAK,CAQpC,GAPAuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIuyB,EAAU,EAAGvyB,EAAI,GAG1C0yB,EAAKnF,AADLA,CAAAA,EAAWX,GAA0BC,gBAAgB,CAACte,EAjBR,EAAU,EAiBgB,CAC3D,CAAC,EAAE,CAIZukB,MADJH,EAAII,AAFCnxB,CAAAA,CAAI,CAAC5B,EAAE,CAnByB,EAmBlB,CAAG0yB,CAAC,EAClBnF,CAAAA,CAAQ,CAAC,EAAE,CAAGmF,CAAC,EACN,MACEG,EAAgB,CAC5B7pB,IACA,QACJ,CACS6pB,GAAkB,CAACC,MAAMH,IAC9BE,CAAAA,EAAiB,CAAA,CAAI,EAEzB,IAAM/wB,EAASH,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAI7B8yB,MAAMH,GACNnzB,EAAMY,IAAI,CAAC,CACPZ,CAAK,CAACsC,EAAS,EAAE,EACb,AAAgC,UAAhC,OAAOtC,CAAK,CAACsC,EAAS,EAAE,CAAC,EAAE,CAC3BtC,CAAK,CAACsC,EAAS,EAAE,CAAC,EAAE,CAAG,KAC3B,KACH,EAGDtC,EAAMY,IAAI,CAAC,CAACuyB,EAAG,KAAK,EAGpB3yB,GAAKgJ,EAAKupB,CAAAA,EAAU,CAAA,EAAMC,CAAAA,EAAU,CAAA,GAOpCI,CAAAA,EAAIzvB,AANK,KAAK,CAAC3B,UAAU,CACrBG,MAAOA,EAAMgJ,KAAK,CAAC,CAAC6nB,GACpBhzB,MAAOA,EAAMmL,KAAK,CAAC,CAAC6nB,EACxB,EAAG,CACC/wB,OAAQ+wB,CACZ,GACWhzB,KAAK,CAAC,EAAE,AAAD,EAEtBizB,EAAGryB,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAE2yB,EAAGC,EAAE,EACvBpzB,CAAK,CAACsC,EAAS,EAAE,CAAC,EAAE,CAAG8wB,CAC3B,CACA,MAAO,CACH1yB,OAAQuyB,EACR9wB,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAuBA6yB,GAAoBjtB,cAAc,CAAGgtB,GAA0BH,GAAiC7sB,cAAc,CAAE,CAI5GvE,OAAQ,CAEJI,MAAO,KAAK,EACZQ,OAAQ,KAAK,EAObsW,QAAS,CAAC,GAAI,EAAE,AACpB,EACArJ,OAAQ,CACJC,QAAS,CAAA,CACb,EACAtJ,QAAS,CACLuJ,YAAa,iHACjB,EAIA0jB,aAAc,CAIV/lB,OAAQ,CAIJsC,UAAW,EAQXC,UAAW,KAAK,CACpB,CACJ,EACAC,aAAc,CACVC,cAAe,UACnB,CACJ,GACAkjB,GAA2BG,GAAoBr0B,SAAS,CAAE,CACtDuM,eAAgB,EAAE,CAClBxJ,eAAgB,CAAC,UAAU,CAC3BK,SAAU,aACV3B,cAAe,CAAC,IAAK,WAAW,CAChCiX,eAAgB,CAAC,IAAK,IAAK,WAAW,CACtC1L,YAAa,IACbV,cAAe,CAAC,eAAe,AACnC,GACAmD,EAAoCJ,OAAO,CAACglB,IAC5C3zB,IAA4D+G,kBAAkB,CAAC,aAAc4sB,IAsC7F,GAAM,CAAE3sB,IAAKstB,EAAoC,CAAEC,WAAYC,EAA2C,CAAE,CAAG,AAACx0B,IAA6DG,WAAW,CAElL,CAAEI,OAAQk0B,EAA8B,CAAEh0B,MAAOi0B,EAA6B,CAAE,CAAIh1B,GAe1F,OAAMi1B,WAAgCH,GAMlC1xB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMkX,EAAUlX,EAAOkX,OAAO,CAAEub,EAAa,KAAK,CAAC9xB,UAAUtD,IAAI,CAAC,IAAI,CAAEoB,EAAQuB,GAAS0yB,EAAa,CAClGrzB,OAAQ,EAAE,CACVyB,MAAO,EAAE,CACTnC,MAAO,EAAE,AACb,EACA,GAAI,CAAC8zB,EACD,MAEJC,CAAAA,EAAW5xB,KAAK,CAAG2xB,EAAW3xB,KAAK,CAACgJ,KAAK,CAACoN,CAAO,CAAC,EAAE,CAAG,GACvD,IAAMyb,EAAYF,EAAW9zB,KAAK,CAACmL,KAAK,CAACoN,CAAO,CAAC,EAAE,CAAG,GAEhD0b,EAAiBT,GAAqCh1B,SAAS,CAACwD,SAAS,CAACtD,IAAI,CAAC,IAAI,CAAE,CACvFyD,MAAO4xB,EAAW5xB,KAAK,CACvBnC,MAAOg0B,CACX,EAAG,CACCvyB,MAAO,EACPQ,OAAQsW,CAAO,CAAC,EAAE,AACtB,GACA,GAAK0b,GAIL,IAAK,IAAIzzB,EAAI,EAAG0zB,EAAWH,EAAW5xB,KAAK,CAACG,MAAM,CAAE9B,EAAI0zB,EAAU1zB,IAC9DuzB,EAAW/zB,KAAK,CAACQ,EAAE,CAAG,CAClBwzB,CAAS,CAACxzB,EAAE,CAAC,EAAE,CACfyzB,EAAej0B,KAAK,CAACQ,EAAI+X,CAAO,CAAC,EAAE,CAAG,EAAE,EAAI,KAC/C,CACDwb,EAAWrzB,MAAM,CAACF,EAAE,CAAG,CACnBuzB,EAAW5xB,KAAK,CAAC3B,EAAE,CACnBwzB,CAAS,CAACxzB,EAAE,CAAC,EAAE,CACfyzB,EAAej0B,KAAK,CAACQ,EAAI+X,CAAO,CAAC,EAAE,CAAG,EAAE,EAAI,KAC/C,CAEL,OAAOwb,EACX,CACJ,CAsBAF,GAAwBjuB,cAAc,CAAGguB,GAA8BF,GAA4C9tB,cAAc,CAAE,CAC/HvE,OAAQ,CAOJkX,QAAS,CAAC,GAAI,EAAG,EAAE,AACvB,CACJ,GACAob,GAA+BE,GAAwBr1B,SAAS,CAAE,CAC9DoD,SAAU,iBACd,GACA1C,IAA4D+G,kBAAkB,CAAC,iBAAkB4tB,IAoCjG,GAAM,CAAElR,IAAKwR,EAAgC,CAAEjuB,IAAKkuB,EAAgC,CAAE,CAAG,AAACl1B,IAA6DG,WAAW,CAE5J,CAAEC,SAAU+0B,EAA4B,CAAEjuB,aAAckuB,EAAgC,CAAE50B,QAAS60B,EAA2B,CAAExb,SAAUyb,EAA4B,CAAE/0B,OAAQg1B,EAA0B,CAAE90B,MAAO+0B,EAAyB,CAAE1X,WAAY2X,EAA8B,CAAE,CAAI/1B,IAUpS,SAASg2B,GAAeC,CAAU,CAAEpzB,CAAK,EACrC,MAAO,CACHA,MAAAA,EACAiG,MAAOmtB,EAAWx0B,SAAS,CAAC,QAAQ,CAACoB,EAAM,CAC3CqD,EAAG+vB,EAAWx0B,SAAS,CAAC,IAAI,CAACoB,EAAM,AACvC,CACJ,CAeA,MAAMqzB,WAA4BV,GAM9BzxB,MAAO,CACH,IAAME,EAAY,IAAI,CACtB,KAAK,CAACF,KAAKzB,KAAK,CAAC2B,EAAW1B,WAE5B,IAAMF,EAAWozB,GAA6B,IAAI,CAACzxB,KAAK,CAACgR,WAAW,CAAE,kBAAmB,KAGrF,GAAI/Q,EAAUnB,OAAO,CAAE,CACnB,IAAMA,EAAUmB,EAAUnB,OAAO,AAIjCA,CAAAA,EAAQqzB,aAAa,CAAIC,AAJ0BnyB,EAAUI,YAAY,CAACvB,OAAO,CAI1CqzB,aAAa,CAC/CrzB,CAAAA,EAAQL,MAAM,CAACY,MAAM,CAAG,CAAA,CACjC,CACAhB,GACJ,EAAG,CACCsC,MAAO,CACX,EACJ,CACAmF,WAAY,CACR,IAAM7F,EAAY,IAAI,CAAEoyB,EAAepyB,EAAUnB,OAAO,CAExDmzB,EAAahyB,EAAUI,YAAY,CAAEiyB,EAAYL,EAAWx0B,SAAS,CAAC,KAAMoL,EAAkBopB,EAAaA,EAAWlxB,MAAM,CAAG,EAAE,CAAGwxB,EAActyB,EAAUc,MAAM,CAAEyxB,EAAYvyB,EAAU+I,KAAK,CAE/LypB,EAAa5pB,EAAenJ,MAAM,CAAG6yB,EAAY7yB,MAAM,CAAEqV,EAAS0d,EAAa,EAAIA,EAAa,EAEhGxpB,EAAe,CACXnK,QAAS,CACLoK,QAASmpB,EAAanpB,OAAO,AACjC,CACJ,EAEAwpB,EAAgB,CACZC,IAAK,EAAE,CACPC,OAAQ,EAAE,CACVjW,UAAW,EAAE,AACjB,EAEAkW,EAAwB,CACpBF,IAAK,CACDxoB,OAAQ,CACJsC,UAAW4lB,EAAa5lB,SAAS,CACjCC,UAAY2lB,EAAaS,iBAAiB,EACtCT,EAAapsB,KAAK,CACtB8sB,UAAWV,EAAaU,SAAS,AACrC,CACJ,EACAH,OAAQ,CACJzoB,OAAQ,CACJsC,UAAW4lB,EAAa5lB,SAAS,CACjCC,UAAY2lB,EAAaW,gBAAgB,EACrCX,EAAapsB,KAAK,CACtB8sB,UAAWV,EAAaU,SAAS,AACrC,CACJ,EACApW,UAAW0V,EAAaY,eAAe,AAC3C,EAEA3pB,EAEA4pB,EAEAC,EAAWC,EAGXC,EAAeC,EAEfC,EAEAC,EAAUC,EAAcC,EAAiBnB,EAAY7yB,MAAM,CAE3D,KAAOg0B,KACHpqB,EAAQipB,CAAW,CAACmB,EAAe,CACnCR,EAAYX,CAAW,CAACmB,EAAiB,EAAE,CAC3CP,EAAYtqB,CAAc,CAAC6qB,EAAiB,EAAI3e,EAAO,CACvDqe,EAAgBvqB,CAAc,CAAC6qB,EAAiB,EAAI3e,EAAO,CAC3Dse,EAAgBxqB,CAAc,CAAC6qB,EAAiB3e,EAAO,CACvDue,EAAoBzqB,CAAc,CAAC6qB,EAAiB3e,EAAS,EAAE,CAC/Dwe,EAAajqB,EAAMxK,OAAO,CAACmH,KAAK,CAChCutB,EAAW,CACPtxB,EAAGoH,EAAMpH,CAAC,CACVsH,MAAOF,EAAME,KAAK,CAClBC,MAAOH,EAAMG,KAAK,CAClBC,OAAQ,CAAA,CACZ,EAGI,CAAC0pB,GACDD,GACAvB,GAA6BU,CAAS,CAACa,EAAUt0B,KAAK,CAAG,EAAE,GAC3Du0B,CAAAA,EAAgBpB,GAAeC,EAAYkB,EAAUt0B,KAAK,CAAG,EAAC,EAI9D,CAACy0B,GACDD,GACAzB,GAA6BU,CAAS,CAACe,EAAcx0B,KAAK,CAAG,EAAE,GAC/Dy0B,CAAAA,EAAoBtB,GAAeC,EAAYoB,EAAcx0B,KAAK,CAAG,EAAC,EAGtE,CAACs0B,GACDC,GACAxB,GAA6BU,CAAS,CAACc,EAAcv0B,KAAK,CAAG,EAAE,EAC/Ds0B,EAAYnB,GAAeC,EAAYmB,EAAcv0B,KAAK,CAAG,GAExD,CAACs0B,GACNE,GACAzB,GAA6BU,CAAS,CAACe,EAAcx0B,KAAK,CAAG,EAAE,GAC/Ds0B,CAAAA,EAAYnB,GAAeC,EAAYoB,EAAcx0B,KAAK,CAAG,EAAC,EAG9DyK,GACA6pB,GACAE,GACAD,GACA9pB,EAAMpH,CAAC,GAAKixB,EAAUjxB,CAAC,GACnBoH,EAAMpH,CAAC,GAAKmxB,EAAcnxB,CAAC,EAC3BkxB,EAAgBD,EAChBA,EAAYE,GAEP/pB,EAAMpH,CAAC,GAAKkxB,EAAclxB,CAAC,EAChCixB,EAAYC,EACZA,EAAgB,CACZtuB,MAAOmtB,EAAWx0B,SAAS,CAAC,QAAQ,CAAC01B,EAAUt0B,KAAK,CAAG,EAAE,CACzDqD,EAAGowB,CAAS,CAACa,EAAUt0B,KAAK,CAAG,EAAE,AACrC,GAEKy0B,GAAqBhqB,EAAMpH,CAAC,GAAKoxB,EAAkBpxB,CAAC,GACzDixB,EAAYG,EACZF,EAAgBC,IAGpBH,GAAaE,GAAiBD,GAC9BM,EAAe,CACXvxB,EAAGgxB,EAAUhxB,CAAC,CACdsH,MAAO0pB,EAAU1pB,KAAK,CACtBC,MAAOypB,EAAUzpB,KAAK,CACtBC,OAAQ,CAAA,CACZ,EACIJ,EAAMzF,CAAC,EAAIsvB,EAAUruB,KAAK,EAC1BouB,EAAUrvB,CAAC,EAAIuvB,EAActuB,KAAK,EAClCwE,EAAMrD,KAAK,CAAIstB,GAAclB,EAAaS,iBAAiB,EACvDT,EAAapsB,KAAK,CACtBysB,EAAcC,GAAG,CAAC30B,IAAI,CAACw1B,IAElBlqB,EAAMzF,CAAC,CAAGsvB,EAAUruB,KAAK,EAC9BouB,EAAUrvB,CAAC,CAAGuvB,EAActuB,KAAK,EACjCwE,EAAMrD,KAAK,CAAIstB,GAAclB,EAAaW,gBAAgB,EACtDX,EAAapsB,KAAK,CACtBysB,EAAcE,MAAM,CAAC50B,IAAI,CAACw1B,KAG1Bd,EAAc/V,SAAS,CAAC3e,IAAI,CAACw1B,GAC7Bd,EAAc/V,SAAS,CAAC3e,IAAI,CAACy1B,GAE7Bf,EAAc/V,SAAS,CAAC3e,IAAI,CAAC8zB,GAA0B2B,EAAc,CACjE/pB,OAAQ,CAAA,CACZ,IACIJ,EAAMzF,CAAC,EAAIsvB,EAAUruB,KAAK,EAC1BouB,EAAUrvB,CAAC,CAAGuvB,EAActuB,KAAK,EACjCwE,EAAMrD,KAAK,CAAIstB,GAAclB,EAAaS,iBAAiB,EACvDT,EAAapsB,KAAK,CACtBitB,EAAUjtB,KAAK,CAAIstB,GAAclB,EAAaW,gBAAgB,EAC1DX,EAAapsB,KAAK,CACtBysB,EAAcC,GAAG,CAAC30B,IAAI,CAACw1B,GACvBd,EAAcC,GAAG,CAAC30B,IAAI,CAAC8zB,GAA0B2B,EAAc,CAC3D/pB,OAAQ,CAAA,CACZ,KAEKJ,EAAMzF,CAAC,CAAGsvB,EAAUruB,KAAK,EAC9BouB,EAAUrvB,CAAC,EAAIuvB,EAActuB,KAAK,GAClCwE,EAAMrD,KAAK,CAAIstB,GAAclB,EAAaW,gBAAgB,EACtDX,EAAapsB,KAAK,CACtBitB,EAAUjtB,KAAK,CAAIstB,GAAclB,EAAaS,iBAAiB,EAC3DT,EAAapsB,KAAK,CACtBysB,EAAcE,MAAM,CAAC50B,IAAI,CAACw1B,GAC1Bd,EAAcE,MAAM,CAAC50B,IAAI,CAAC8zB,GAA0B2B,EAAc,CAC9D/pB,OAAQ,CAAA,CACZ,OAIHypB,IACD7pB,EAAMzF,CAAC,EAAIsvB,EAAUruB,KAAK,EAC1BwE,EAAMrD,KAAK,CAAIstB,GAAclB,EAAaS,iBAAiB,EACvDT,EAAapsB,KAAK,CACtBysB,EAAcC,GAAG,CAAC30B,IAAI,CAACw1B,KAGvBlqB,EAAMrD,KAAK,CAAIstB,GAAclB,EAAaW,gBAAgB,EACtDX,EAAapsB,KAAK,CACtBysB,EAAcE,MAAM,CAAC50B,IAAI,CAACw1B,KAKtCzB,GAA+BW,EAAe,SAAU50B,CAAM,CAAEoM,CAAQ,EACpEjK,EAAUc,MAAM,CAAGjD,EACnBmC,EAAUnB,OAAO,CAAGgzB,GAA0Be,CAAqB,CAAC3oB,EAAS,CAACC,MAAM,CAAElB,GACtFhJ,EAAU+I,KAAK,CAAG/I,CAAS,CAAC,QAAUiK,EAAW,OAAO,CACxDsnB,GAAiC51B,SAAS,CAACkK,SAAS,CAAChK,IAAI,CAACmE,GAE1DA,CAAS,CAAC,QAAUiK,EAAW,OAAO,CAAGjK,EAAU+I,KAAK,AAC5D,GAEA/I,EAAUc,MAAM,CAAGwxB,EACnBtyB,EAAUnB,OAAO,CAAGuzB,EACpBpyB,EAAU+I,KAAK,CAAGwpB,CACtB,CAiCApzB,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEs0B,EAAal1B,EAAOk1B,UAAU,CAAEr0B,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAEtGw2B,EAAK,EAAE,CAAEr0B,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAgC6Y,EAAgB,AAAC5W,AAAW,IAAXA,EAAgB,EAAIA,EAAS,EAAGw0B,EAAU,EAAE,CAAEC,EAAY,EAAE,CACxIlN,EAAU,EAAE,CAAEmN,EAASC,EAAWC,EAAYC,EAAaC,EAAeC,EAC9EtmB,EAAOjK,EAAGjG,EACV,GAAI,CAAC0B,CAAAA,EAAKI,MAAM,EAAIL,CAAK,GAAOsyB,GAA4BnyB,CAAI,CAAC,EAAE,GAC/DA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,GAAUL,CAAAA,EAAS,CAAA,GAMrC,IAAKzB,EAAI,EAHTgpB,EAAU2K,GAAiC31B,SAAS,CAACwD,SAAS,CAACtD,IAAI,CAAC,IAAI,CAAEoB,EAAQ,CAC9EmC,OAAQA,CACZ,GAAGjC,KAAK,CACIQ,EAAIgpB,EAAQlnB,MAAM,CAAE9B,IAC5BiG,EAAIrE,CAAI,CAACyW,EAAgBrY,EAAE,CAC3BkQ,EAAQtO,CAAI,CAACyW,EAAgBrY,EAAI,EAAE,EAAI,EAAE,CACzCs2B,EAAcL,CAAO,CAACj2B,EAAI,EAAE,CAC5Bu2B,EAAgBL,CAAS,CAACl2B,EAAI,EAAE,CAChCw2B,EAASh3B,CAAK,CAACQ,EAAI,EAAE,CACX,IAANA,GACAs2B,CAAAA,EAAcC,EAAgBC,EAAS,CAAA,EAE3CL,EAAUrC,GAAiC,AAAC7tB,CAAAA,CAAC,CAnBW,EAmBL,CAAGA,CAAC,CAnBT,EAmBc,AAAD,EAAK,EAAI8vB,EAAa/M,CAAO,CAAChpB,EAAE,EAC3Fo2B,EAAYtC,GAAiC,AAAC7tB,CAAAA,CAAC,CApBS,EAoBH,CAAGA,CAAC,CApBX,EAoBgB,AAAD,EAAK,EAAI8vB,EAAa/M,CAAO,CAAChpB,EAAE,EACzF,AAACm2B,EAAUG,GACVpmB,CAAK,CAtB2B,EAsBpB,CAAGomB,EAChBL,CAAO,CAACj2B,EAAE,CAAGm2B,EAGbF,CAAO,CAACj2B,EAAE,CAAGs2B,EAEb,AAACF,EAAYG,GACZrmB,CAAK,CA7B2B,EA6BpB,CAAGqmB,EAChBL,CAAS,CAACl2B,EAAE,CAAGo2B,EAGfF,CAAS,CAACl2B,EAAE,CAAGu2B,EAEfC,IAAWF,GAAerwB,CAAC,CAnCM,EAmCC,CAAGgwB,CAAO,CAACj2B,EAAE,EAC/Cw2B,IAAWD,GAAiBtwB,CAAC,CApCI,EAoCG,CAAGiwB,CAAS,CAACl2B,EAAE,CACnDq2B,EAAaJ,CAAO,CAACj2B,EAAE,CAElBw2B,CAAAA,IAAWF,GAAerwB,CAAC,CAvCC,EAuCM,CAAGgwB,CAAO,CAACj2B,EAAE,EACpDw2B,IAAWD,GAAiBtwB,CAAC,CAxCI,EAwCG,CAAGiwB,CAAS,CAACl2B,EAAE,AAAD,GAClDq2B,CAAAA,EAAaH,CAAS,CAACl2B,EAAE,AAAD,EAE5Bg2B,EAAG51B,IAAI,CAAC,CAACsB,CAAI,CAAC2W,EAAgBrY,EAAE,CAAEq2B,EAAW,EAC7C10B,EAAMvB,IAAI,CAACsB,CAAI,CAAC2W,EAAgBrY,EAAE,EAClCR,EAAMY,IAAI,CAACi2B,GAEf,MAAO,CACHn2B,OAAQ81B,EACRr0B,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAyBA80B,GAAoBlvB,cAAc,CAAG8uB,GAA0BN,GAAiCxuB,cAAc,CAAE,CAM5GvE,OAAQ,CACJI,MAAO,KAAK,EAIZ80B,WAAY,EAMZt0B,OAAQ,EACZ,EASA2zB,iBAAkB,UASlBF,kBAAmB,UAOnBG,gBAAiB,CACb9oB,OAAQ,CAIJsC,UAAW,EAMXC,UAAW,UAcXqmB,UAAW,UACf,CACJ,CACJ,GACAlB,GAA2BK,GAAoBt2B,SAAS,CAAE,CACtDoD,SAAU,aACVL,eAAgB,CAAC,aAAc,SAAS,AAC5C,GACArC,IAA4D+G,kBAAkB,CAAC,aAAc6uB,IA4C7F,GAAM,CAAE5uB,IAAK,CAAE1H,UAAW,CAAEgsB,WAAYyM,EAAiB,CAAE,CAAE,CAAE,CAAG,AAAC/3B,IAA6DG,WAAW,CAqBxG63B,GAfnC,cAAuBD,GAEnBn2B,SAAU,CAEF,IAAI,CAACq2B,eAAe,EACpB,CAAA,IAAI,CAACA,eAAe,CAAG,IAAI,CAACA,eAAe,CAACr2B,OAAO,EAAC,EAExD,KAAK,CAACA,QAAQI,KAAK,CAAC,IAAI,CAAEC,UAC9B,CACJ,EAuBM,CAAEi2B,WAAAA,EAAU,CAAE,CAAIx4B,IAElB,CAAEsJ,KAAMmvB,EAAiB,CAAE,CAAIz4B,IAE/B,CAAEyF,OAAQ,CAAE7F,UAAW84B,EAAwB,CAAE,CAAEpxB,IAAKqxB,EAAyB,CAAE,CAAG,AAACr4B,IAA6DG,WAAW,CAE/J,CAAEC,SAAUk4B,EAAqB,CAAEC,SAAAA,EAAQ,CAAEC,SAAAA,EAAQ,CAAEtxB,aAAcuxB,EAAyB,CAAEhtB,QAASitB,EAAoB,CAAEp4B,MAAOq4B,EAAkB,CAAEp4B,OAAQq4B,EAAmB,CAAEp4B,QAASq4B,EAAoB,CAAEp4B,MAAOq4B,EAAkB,CAAE,CAAIp5B,IAMrP6R,GAAMD,KAAKC,GAAG,AAyCpB,OAAMwnB,WAAqBV,GAMvB50B,KAAKC,CAAK,CAAElB,CAAO,CAAE,CACjB,IAAMmB,EAAY,IAAI,AAGtB,QAAOnB,EAAQyD,IAAI,CACnB,KAAK,CAACxC,KAAKzB,KAAK,CAAC2B,EAAW1B,WAE5B,IAAMF,EAAWu2B,GAAsB,IAAI,CAAC50B,KAAK,CAACgR,WAAW,CAAE,kBAAmB,WAG9E,GAAI/Q,EAAUnB,OAAO,CAAE,CACnB,IAAML,EAASwB,EAAUnB,OAAO,CAACL,MAAM,CAAE62B,EAAar1B,EAAUI,YAAY,CAAE+E,EAAepF,EAAMvE,GAAG,CAACgD,EAAO0G,cAAc,EAC5HlF,EAAUs1B,eAAe,CAACD,EAAYlwB,EAC1C,CACA/G,GACJ,EAAG,CACCsC,MAAO,CACX,GACA,OAAOV,CACX,CAEAs1B,gBAAgBD,CAAU,CAAElwB,CAAY,CAAE,CACtC,IAAMnF,EAAY,IAAI,CAAEu1B,EAAmB,KACvCv1B,EAAUD,KAAK,CAACyC,MAAM,GACtBxC,EAAUw1B,OAAO,CAAC,EAAE,EACpBx1B,EAAUy1B,UAAU,CAAG,EAAE,CACrBz1B,EAAU01B,YAAY,EACtB11B,CAAAA,EAAU01B,YAAY,CAAG11B,EAAU01B,YAAY,CAACz3B,OAAO,EAAC,CAEhE,EAaA,OAVA+B,EAAU9B,kBAAkB,CAACH,IAAI,CAAC42B,GAAsBU,EAAY,SAAU,WAC1EE,GACJ,IAGIpwB,GACAnF,EAAU9B,kBAAkB,CAACH,IAAI,CAAC42B,GAAsBxvB,EAAc,SAAU,WAC5EowB,GACJ,IAEGv1B,CACX,CAEA21B,QAAQ71B,CAAI,CAAE,CACV,IAAM7C,EAAS,IAAI,CAAE24B,EAAW34B,EAAO8C,KAAK,CAAC61B,QAAQ,CAAEC,EAAQ54B,EAAO44B,KAAK,CAAEC,EAAO,CAAC,EACrF,GAAI,CAACh2B,GAAQ+1B,EAAO,CAChB,IAAM7Z,EAAW4Z,EAAW34B,EAAO6N,KAAK,CAAC4nB,GAAG,CAAGz1B,EAAOuD,KAAK,CAACu1B,IAAI,CAC5DH,GACAC,CAAK,CAAC,0BAA0B,CAAG,CAAA,EACnCC,EAAKE,UAAU,CAAGha,IAGlB6Z,CAAK,CAAC,0BAA0B,CAAG,CAAA,EACnCC,EAAKG,UAAU,CAAGja,GAEtB6Z,EAAMF,OAAO,CAACG,EAAMb,GAAoBV,GAAWt3B,EAAO4B,OAAO,CAACq3B,SAAS,EAAG,CAC1EC,KAAM,SAAUC,CAAG,CAAEC,CAAE,EACnBp5B,EAAO44B,KAAK,CAACC,IAAI,CAAC,CACdQ,OAAQ3oB,KAAKvM,GAAG,CAAC,KAAOi1B,EAAGE,GAAG,CAClC,EACJ,CACJ,GACJ,CACJ,CACA3uB,YAAa,CAEL5H,AADc,IAAI,CACRnB,OAAO,CAAC23B,cAAc,CAAClqB,OAAO,GACxCtM,AAFc,IAAI,CAERy2B,YAAY,CAAC,CAAA,EAAM,CAAA,GAC7BhC,GAAyB7sB,UAAU,CAACvJ,KAAK,CAH3B,IAAI,CAGmCC,WACrD0B,AAJc,IAAI,CAIRy2B,YAAY,CAAC,CAAA,EAAO,CAAA,IAElChC,GAAyB7sB,UAAU,CAACvJ,KAAK,CANvB,IAAI,CAM+BC,UACzD,CAEAm4B,aAAaC,CAAO,CAAEH,CAAG,CAAE,CACvB,IAAwBI,EAAYJ,EAChC,CAAC,WAAY,WAAW,CACxB,CAAC,WAAY,WAAW,CAAEC,EAAiBx2B,AAF7B,IAAI,CAEmCnB,OAAO,CAAC23B,cAAc,CAAEI,EAAc52B,AAF7E,IAAI,CAEmFc,MAAM,CAACrB,MAAM,CAClHo3B,EAAY,EAAE,CAAEC,EAAY,EAAE,CAAEn5B,EAAI,EAAGo5B,EAAYC,EAAWC,EAAU5tB,EAS5E,IARIqtB,GACA12B,AALc,IAAI,CAKR62B,SAAS,CAAGA,EACtB72B,AANc,IAAI,CAMR82B,SAAS,CAAGA,IAGtBD,EAAY72B,AATE,IAAI,CASI62B,SAAS,CAC/BC,EAAY92B,AAVE,IAAI,CAUI82B,SAAS,EAE5Bn5B,EAAIi5B,EAAaj5B,IAEpB0L,AADAA,CAAAA,EAAQrJ,AAbM,IAAI,CAaAc,MAAM,CAACnD,EAAE,AAAD,CACrB,CAACg5B,CAAS,CAAC,EAAE,CAAG,UAAU,CAAGttB,EAAM6tB,OAAO,CAC/C7tB,EAAM6tB,OAAO,CAAG7tB,CAAK,CAACstB,CAAS,CAAC,EAAE,CAAG,UAAU,CAC3CD,IACAK,EAAa1tB,EAAM8tB,SAAS,CAACC,KAAK,CAElCH,CAAAA,EAAWD,AADXA,CAAAA,EAAYh3B,AAlBF,IAAI,CAkBQq3B,UAAU,CAAC15B,EAAE,AAAD,EACb25B,eAAe,AAAD,GAE/BT,EAAU94B,IAAI,CAACg5B,EAAaE,EAAWD,EAAUO,kBAAkB,EACnET,EAAU/4B,IAAI,CAACg5B,EAAaE,EAAWD,EAAUQ,kBAAkB,IAGnEX,EAAU94B,IAAI,CAAC,GACf+4B,EAAU/4B,IAAI,CAAC,KAGvBsL,EAAMrD,KAAK,CAAGuwB,EACVC,EAAetsB,MAAM,CAACjE,aAAa,CACnCuwB,EAAetsB,MAAM,CAAC/D,aAAa,CACvCkD,EAAM8tB,SAAS,CAACC,KAAK,CAAGb,EACpBv2B,AAjCU,IAAI,CAiCJ62B,SAAS,CAACl5B,EAAE,CACtBqC,AAlCU,IAAI,CAkCJ82B,SAAS,CAACn5B,EAAE,CAC1B0L,EAAM8tB,SAAS,CAACl1B,CAAC,CAAGs0B,EAChBltB,EAAM8tB,SAAS,CAACl1B,CAAC,CACjBjC,AArCU,IAAI,CAqCJ62B,SAAS,CAACl5B,EAAE,AAElC,CACAgK,WAAY,CACR,IAAM3H,EAAY,IAAI,CAAEnB,EAAUmB,EAAUnB,OAAO,CAAEkB,EAAQC,EAAUD,KAAK,CAAE+K,EAAQ9K,EAAU8K,KAAK,CAAE2sB,EAAW3sB,EAAM3J,GAAG,CAAEu2B,EAAmB13B,EAAUnB,OAAO,CAAC84B,SAAS,CAAEN,EAAcr3B,EAAUq3B,UAAU,CAC3MO,EAAa,EAAoBC,EAAWC,EAAgBC,EAAWC,EAAYC,EAAcC,EAAUhxB,EAAcixB,EAAcC,EAAMC,EACjJ5D,GAAyB9sB,SAAS,CAACtJ,KAAK,CAAC2B,GACzC,IAAMs4B,EAAkBt4B,EAAUc,MAAM,AAEpCw3B,CAAAA,EAAgB74B,MAAM,GACtByH,EAAerI,EAAQqI,YAAY,CAAG,GAClCrI,EAAQqI,YAAY,CACpB,GAEJ2wB,EAAYjD,GADM50B,EAAUu4B,eAAe,EAE3CT,EAAiB/3B,EAAMy4B,SAAS,CAAG,EACnCL,EAAep4B,EAAM04B,OAAO,CAC5BV,EAAYnqB,GAAI9C,EAAMC,QAAQ,CAAC0sB,GAC3B3sB,EAAMC,QAAQ,CAAC0sB,EAAWz3B,EAAU04B,SAAS,GACjDT,EAAerqB,GAAI9C,EAAMC,QAAQ,CAAC0sB,GAC9B3sB,EAAMC,QAAQ,CAAC0sB,EAAWz3B,EAAU04B,SAAS,GAC7CxxB,IACA8wB,EAAapqB,GAAImqB,EAAa,CAAA,EAAI,EAAI7wB,CAAW,GACjD0wB,EAAahqB,GAAI,AAACmqB,CAAAA,EAAYC,CAAS,EAAK,GAC5CD,EAAYnqB,GAAIoqB,IAEpBM,EAAgBn6B,OAAO,CAAC,SAAUkL,CAAK,CAAEzK,CAAK,EAC1Cw5B,EAAO/uB,EAAM+uB,IAAI,CAAG/uB,EAAME,KAAK,CAAG,EAClC8uB,EAAOhvB,EAAMG,KAAK,CAAIsB,EAAMC,QAAQ,CAACssB,CAAU,CAACz4B,EAAM,CAAC+5B,KAAK,EACxDR,EACCrtB,CAAAA,EAAM8tB,QAAQ,CACVb,EAAYE,EACbF,CAAQ,EACZH,EAGJvuB,EAAM0tB,UAAU,CAFhBmB,EAAWpD,GAA0BgD,EACjCT,CAAU,CAACz4B,EAAM,CAAC04B,eAAe,CAAGO,GAExCxuB,EAAM8tB,SAAS,CAAGn3B,EAAU0H,QAAQ,CAACrJ,KAAK,CAC1C2B,EAAW,CAACo4B,EAAMC,EAAMH,EAAUH,EAAU,EAC5C1uB,EAAMwvB,SAAS,CAAGxB,CAAU,CAACz4B,EAAM,CAAC44B,kBAAkB,CACtDnuB,EAAMyvB,SAAS,CAAGzB,CAAU,CAACz4B,EAAM,CAAC24B,kBAAkB,CACtDluB,EAAM0vB,SAAS,CAAG1B,CAAU,CAACz4B,EAAM,CAAC04B,eAAe,AACvD,GACII,EAAiBprB,OAAO,EACxBtM,EAAUg5B,SAAS,CAACj5B,EAAO+K,EAAO9K,EAAUy1B,UAAU,CAAEiC,EAAiBxtB,MAAM,EAG3F,CACA+uB,aAAc,CACV,IACIja,EADEka,EAAc,IAAI,CAACr6B,OAAO,CAACs6B,OAAO,CAAEC,EAAiB,IAAI,CAACv6B,OAAO,CAACw6B,UAAU,CAgBlF,OAbI,IAAI,CAACx6B,OAAO,CAACs6B,OAAO,EACpB,IAAI,CAACt6B,OAAO,CAACs6B,OAAO,CAAG,KAAK,EAC5Bna,EAAM,KAAK,CAACia,cACZ,IAAI,CAACp6B,OAAO,CAACs6B,OAAO,CAAGD,GAElB,IAAI,CAACr6B,OAAO,CAACw6B,UAAU,EAC5B,IAAI,CAACx6B,OAAO,CAACw6B,UAAU,CAAG,CAAA,EAC1Bra,EAAM,KAAK,CAACia,cACZ,IAAI,CAACp6B,OAAO,CAACw6B,UAAU,CAAGD,GAG1Bpa,EAAM,KAAK,CAACia,cAETja,CACX,CACA7f,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAwB86B,EAAUr8B,EAAOO,SAAS,CAAC,IAAK,CAAA,GAAO+7B,EAAUt8B,EAAOoE,cAAc,CAAEtB,EAAQC,AAAtF,IAAI,CAA4FD,KAAK,CAAEy5B,EAASh7B,EAAOg7B,MAAM,CAAEC,EAAM,EAAE,CAAEn6B,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEgI,EAAepF,EAAMvE,GAAG,CAACgD,EAAO0G,cAAc,EAEjO,GAAI,CAACjI,EAAO8C,KAAK,CAAE,CACfi1B,GAAmB,qEACD,CAAA,EAAMj1B,GACxB,MACJ,CAEA,GAAI,CAACoF,GACD,CAACA,EAAa3H,SAAS,CAAC,IAAK,CAAA,GAAMiC,MAAM,CAAE,CAC3C,IAAMi6B,EAAev0B,GACjB,CAACA,EAAa3H,SAAS,CAAC,IAAK,CAAA,GAAMiC,MAAM,CACzC,8BACA,sCACJu1B,GAAmB,UACfx2B,EAAO0G,cAAc,CAAGw0B,EAAc,CAAA,EAAM35B,GAChD,MACJ,CAEA,IAAMoP,EAAS+lB,GAAqBqE,CAAO,CAAC,EAAE,EAC9C,GAAIpqB,GAAUoqB,AAAsB,IAAtBA,CAAO,CAAC,EAAE,CAAC95B,MAAM,CAAQ,CACnCu1B,GAAmB,WACf/3B,EAAOwB,IAAI,CACX,uDAAwD,CAAA,EAAMsB,GAClE,MACJ,CASA,MALAs3B,AADmBr3B,CAAAA,AA5BD,IAAI,CA4BOq3B,UAAU,CAAGr3B,AA5BxB,IAAI,CA4B8B25B,YAAY,CAACxqB,EAAQmqB,EAASC,EAASC,EAAQr0B,EAAY,EACpGhH,OAAO,CAAC,SAAUy7B,CAAI,CAAEh7B,CAAK,EACpC66B,EAAI17B,IAAI,CAAC,CAAC67B,EAAK33B,CAAC,CAAE23B,EAAKC,GAAG,CAAC,EAC3Bv6B,EAAMvB,IAAI,CAAC07B,CAAG,CAAC76B,EAAM,CAAC,EAAE,EACxBzB,EAAMY,IAAI,CAAC07B,CAAG,CAAC76B,EAAM,CAAC,EAAE,CAC5B,GACO,CACHf,OAAQ47B,EACRn6B,MAAOA,EACPnC,MAAOA,CACX,CACJ,CAEAw8B,aAAaxqB,CAAM,CAAEmqB,CAAO,CAAEC,CAAO,CAAEC,CAAM,CAAEr0B,CAAY,CAAE,CACzD,IAAwB20B,EAAiB3qB,EAAAA,GAAS4qB,AArQ1D,SAA2Bz3B,CAAI,EAC3B,IAAM03B,EAAa13B,EAAK7C,MAAM,CAC1B0B,EAAMmB,CAAI,CAAC,EAAE,CAAC,EAAE,CAAElB,EAAMD,EAAKxD,EAAI,EAAG4P,EACxC,KAAO5P,EAAIq8B,EAAYr8B,IACnB4P,CAAAA,EAAejL,CAAI,CAAC3E,EAAE,CAAC,EAAE,AAAD,EACLwD,GACfA,CAAAA,EAAMoM,CAAW,EAEjBA,EAAenM,GACfA,CAAAA,EAAMmM,CAAW,EAGzB,MAAO,CACHpM,IAAKA,EACLC,IAAKA,CACT,CACJ,EAqP4Em4B,GAAmB9D,EAAaz1B,AAAlF,IAAI,CAAwFy1B,UAAU,CAAG,EAAE,CAAE4B,EAAa,EAAE,CAC1I4C,EAAWH,EACXA,EAAc34B,GAAG,CACjB0zB,GAAS0E,GAAUW,EAAYJ,EAC/BA,EAAc14B,GAAG,CACjBwzB,GAAS2E,GAAU57B,EAAI,EAAGgJ,EAAI,EAG5BqrB,EAAahyB,AARD,IAAI,CAQOI,YAAY,CAMzC,GALI,CAACJ,AATa,IAAI,CASPnB,OAAO,CAAC+D,aAAa,EAChCovB,EAAWnvB,UAAU,GACrBo3B,EAAWjI,EAAWnvB,UAAU,CAACgI,WAAW,CAACovB,GAC7CC,EAAYlI,EAAWnvB,UAAU,CAACgI,WAAW,CAACqvB,IAE9C,CAACnF,GAAqBkF,IAAa,CAAClF,GAAqBmF,GAQzD,OAPI,IAAI,CAACp5B,MAAM,CAACrB,MAAM,GAClB,IAAI,CAAC+1B,OAAO,CAAC,EAAE,EACf,IAAI,CAACC,UAAU,CAAG,EAAE,CAChB,IAAI,CAACC,YAAY,EACjB,CAAA,IAAI,CAACA,YAAY,CAAG,IAAI,CAACA,YAAY,CAACz3B,OAAO,EAAC,GAG/C,EAAE,CAEb,IAAMy6B,EAAY14B,AAxBA,IAAI,CAwBM04B,SAAS,CACjC5D,GAA0BoF,EAAYD,GAAYT,EAEtD,IADA/D,EAAW13B,IAAI,CAACk8B,GACTt8B,EAAI67B,EAAS,EAAG77B,IACnB83B,EAAW13B,IAAI,CAAC+2B,GAA0BW,CAAU,CAAC93B,EAAE,CAAG+6B,IAE9DjD,EAAW13B,IAAI,CAACm8B,GAChB,IAAMC,EAAmB1E,EAAWh2B,MAAM,CAE1C,KAAOkH,EAAIwzB,EAAkBxzB,IACzB0wB,EAAWt5B,IAAI,CAAC,CACZa,MAAO+H,EAAI,EACX1E,EAAGq3B,CAAO,CAAC,EAAE,CACbX,MAAOlD,CAAU,CAAC9uB,EAAI,EAAE,CACxBkzB,IAAKpE,CAAU,CAAC9uB,EAAE,AACtB,GAEJ,OAAO3G,AAzCW,IAAI,CAyCLo6B,aAAa,CAACjrB,EAAQkoB,EAAYlyB,EAAcm0B,EAASC,EAC9E,CAEAa,cAAcjrB,CAAM,CAAEkoB,CAAU,CAAElyB,CAAY,CAAEm0B,CAAO,CAAEC,CAAO,CAAE,CAC9D,IACIc,EAAeC,EAAWC,EAAS3vB,EAAOjN,EADxCqC,EAAY,IAAI,CAAEw6B,EAAcr1B,EAAa3H,SAAS,CAAC,IAAK,CAAA,GAAOi9B,EAAct1B,EAAa3H,SAAS,CAAC,IAAK,CAAA,GAAOk9B,EAAgBrD,EAAW53B,MAAM,CAAG,EAAGk7B,EAAmBpB,EAAQ95B,MAAM,CAAEm7B,EAAqBH,EAAYh7B,MAAM,CA8D3O,OA3DImO,GAAI+sB,EAAmBC,KAGnBtB,CAAO,CAAC,EAAE,GAAKkB,CAAW,CAAC,EAAE,EAC7BC,EAAY7c,OAAO,CAAC,GAIpB0b,CAAO,CAACqB,EAAmB,EAAE,GAC7BH,CAAW,CAACI,EAAqB,EAAE,EACnCH,EAAY18B,IAAI,CAAC,IAGzBiC,EAAUu4B,eAAe,CAAG,EAAE,CAC9BlB,EAAWl5B,OAAO,CAAC,SAAUy7B,CAAI,EAI7B,IAAKj8B,EAAI,EAHTi8B,EAAKtC,eAAe,CAAG,EACvBsC,EAAKrC,kBAAkB,CAAG,EAC1BqC,EAAKpC,kBAAkB,CAAG,EACd75B,EAAIg9B,EAAkBh9B,IAAK,CACnC28B,EAAY,CAAA,EACZC,EAAU,CAAA,EACV3vB,EAAQuE,EAASoqB,CAAO,CAAC57B,EAAE,CAAC,EAAE,CAAG47B,CAAO,CAAC57B,EAAE,CAC3C08B,EAAgB18B,EACXwR,EACGoqB,CAAO,CAAC57B,EAAI,EAAE,CAAC,EAAE,CACjB47B,CAAO,CAAC57B,EAAI,EAAE,CAClBiN,EAGJ,IAAMonB,EAAahyB,EAAUI,YAAY,AACrC,EAACJ,EAAUnB,OAAO,CAAC+D,aAAa,EAChCovB,EAAWnvB,UAAU,GACrB+H,EAAQonB,EAAWnvB,UAAU,CAACgI,WAAW,CAACD,GAC1CyvB,EAAgBrI,EAAWnvB,UAAU,CAChCgI,WAAW,CAACwvB,IAIjBzvB,GAASgvB,EAAKjB,KAAK,EAAIiB,AAAe,IAAfA,EAAKh7B,KAAK,EACjC07B,CAAAA,EAAY,CAAA,CAAG,EAIf1vB,GAASgvB,EAAKC,GAAG,EAAID,EAAKh7B,KAAK,GAAK87B,GACpCH,CAAAA,EAAU,CAAA,CAAG,EAEZ3vB,CAAAA,EAAQgvB,EAAKjB,KAAK,EAAI2B,CAAQ,GAC9B1vB,CAAAA,EAAQgvB,EAAKC,GAAG,EAAIU,CAAM,IAC3BX,EAAKtC,eAAe,EAAImD,CAAW,CAAC98B,EAAE,CAClC08B,EAAgBzvB,EAChBgvB,EAAKpC,kBAAkB,EAAIiD,CAAW,CAAC98B,EAAE,CAGzCi8B,EAAKrC,kBAAkB,EAAIkD,CAAW,CAAC98B,EAAE,CAGrD,CACAqC,EAAUu4B,eAAe,CAACx6B,IAAI,CAAC67B,EAAKtC,eAAe,CACvD,GACOD,CACX,CAEA2B,UAAUj5B,CAAK,CAAE+K,CAAK,CAAE+vB,CAAW,CAAEC,CAAW,CAAE,CAC9C,IAAwBC,EAAWh7B,EAAMg7B,QAAQ,CAAmBC,EAAej7B,EAAMy4B,SAAS,CAAEyC,EAAiBl7B,EAAM04B,OAAO,CAC9H/C,EAAe11B,AADD,IAAI,CACO01B,YAAY,CAAEwF,EAAgB,EAAE,CAAEC,EAC/DN,EAAY18B,OAAO,CAAC,SAAUyM,CAAK,EAC/BuwB,EAAkBrwB,EAAMC,QAAQ,CAACH,GAASqwB,EAC1CC,EAAgBA,EAAcrd,MAAM,CAAC9d,EAAMg7B,QAAQ,CAACK,SAAS,CAAC,CAAC,CACvD,IALqD,EAOrDD,EACH,CAAE,CACC,IACAH,EACAG,EACH,CAAC,CAAEL,EAAYtuB,SAAS,EACjC,GAEIkpB,EACAA,EAAaC,OAAO,CAAC,CACjB56B,EAAGmgC,CACP,GAGAxF,EAAe11B,AArBD,IAAI,CAqBO01B,YAAY,CACjCqF,EACK1wB,IAAI,CAAC6wB,GACLpF,IAAI,CAAC,CACN,eAAgBgF,EAAYtuB,SAAS,CACrC,OAAUsuB,EAAY90B,KAAK,CAC3B,UAAa80B,EAAYhI,SAAS,CAClC,OAAU9yB,AA5BJ,IAAI,CA4BU61B,KAAK,CAACwF,MAAM,CAAG,EACvC,GACKC,GAAG,CAACt7B,AA9BC,IAAI,CA8BK61B,KAAK,CAEpC,CACJ,CAqBAT,GAAaryB,cAAc,CAAGoyB,GAAmBT,GAA0B3xB,cAAc,CAAE,CAIvFvE,OAAQ,CAEJI,MAAO,KAAK,EACZQ,OAAQ,KAAK,EAIbo6B,OAAQ,GAMRt0B,eAAgB,QACpB,EAIAyyB,UAAW,CAIPrrB,QAAS,CAAA,EAOTpC,OAAQ,CAEJlE,MAAO,UAEP8sB,UAAW,WAEXtmB,UAAW,CACf,CACJ,EAIAgqB,eAAgB,CAIZlqB,QAAS,CAAA,EACTpC,OAAQ,CAMJjE,cAAe,2BAMfE,cAAe,wBACnB,CACJ,EAEAo1B,eAAgB,IAChB1R,oBAAqB,CAAA,EACrB3iB,aAAc,EACdm0B,OAAQ,GACRl0B,MAAO,CAAA,EACPuF,aAAc,CACVJ,QAAS,CAAA,CACb,EACAwc,WAAY,CACR0S,MAAO,OACPC,aAAc,CAAA,EACdnvB,QAAS,CAAA,EACTwd,OAAQ,sDACR4R,QAAS,EACTC,MAAO,CAEHC,SAAU,OACd,EACAC,cAAe,KACnB,CACJ,GACA5G,GAAoBG,GAAaz5B,SAAS,CAAE,CACxCoD,SAAU,kBACVL,eAAgB,CAAC,SAAS,CAC1B6B,YAAa,CACTR,MAAO,SACPS,MAAO,kBACX,EACAmnB,WAAY0M,GACZ7sB,cAAegtB,GACf3uB,UAAW2uB,GACX/sB,iBAAkBgtB,GAAyBhtB,gBAAgB,CAC3DC,SAAU+sB,GAAyB/sB,QAAQ,AAC/C,GACArL,IAA4D+G,kBAAkB,CAAC,MAAOgyB,IAwCtF,GAAM,CAAE/xB,IAAKy4B,EAA0B,CAAE,CAAG,AAACz/B,IAA6DG,WAAW,CAE/G,CAAEG,MAAOo/B,EAAmB,CAAEl/B,QAASm/B,EAAqB,CAAEl/B,MAAOm/B,EAAmB,CAAE,CAAIlgC,GAepG,OAAMmgC,WAAsBJ,GAMxB38B,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAwBuB,EAAQ9C,EAAO8C,KAAK,CAAEu5B,EAAUr8B,EAAOqC,KAAK,CAAEi6B,EAAUt8B,EAAOE,KAAK,CAAEiC,EAASZ,EAAOY,MAAM,CAChH+P,EAAS,CAAA,EAAMhK,EAEnB,GAAI,CAAEA,CAAAA,EAAgBpF,EAAMvE,GAAG,CAACgD,EAAO0G,cAAc,CAAC,EAAI,CACtD62B,GAAoB,UAChBv9B,EAAO0G,cAAc,CACrB,sCAAuC,CAAA,EAAMnF,GACjD,MACJ,CAKA,OAHMi8B,GAAsBzC,CAAO,CAAC,EAAE,GAClCpqB,CAAAA,EAAS,CAAA,CAAI,EAEVnP,AAbW,IAAI,CAaLm8B,mBAAmB,CAAChtB,EAAQmqB,EAASC,EAASp0B,EAAc/F,EACjF,CA0BA+8B,oBAAoBhtB,CAAM,CAAEmqB,CAAO,CAAEC,CAAO,CAAEp0B,CAAY,CAAE/F,CAAM,CAAE,CAChE,IACIg9B,EAAczW,EAAc0W,EAAQC,EAAS3+B,EAAGgJ,EAD9C41B,EAAep3B,EAAa3H,SAAS,CAAC,KAAMg/B,EAAeD,EAAa98B,MAAM,CAAE2J,EAAekwB,EAAQ75B,MAAM,CAAEg9B,EAAkB,EAAE,CAAEC,EAAmB,EAAE,CAAEp9B,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEw/B,EAAO,EAAE,CAQnM,IAAKh/B,EAAI,EALLy+B,EADAhzB,GAAgBozB,EACDpzB,EAGAozB,EAEP71B,EAAI,EAAGhJ,EAAIy+B,EAAcz+B,IAOjCgoB,EAJexW,CAAAA,EACV,AAACoqB,CAAAA,CAAO,CAAC57B,EAAE,CAAC,EAAE,CAAG47B,CAAO,CAAC57B,EAAE,CAAC,EAAE,CAC3B47B,CAAO,CAAC57B,EAAE,CAAC,EAAE,AAAD,EAAK,EACrB47B,CAAO,CAAC57B,EAAE,AAAD,EACG4+B,CAAY,CAAC5+B,EAAE,CAC/B0+B,EAAS11B,EACJ81B,CAAe,CAAC9+B,EAAI,EAAE,CAAGgoB,EAC1BA,EACJ2W,EAAU31B,EACL+1B,CAAgB,CAAC/+B,EAAI,EAAE,CAAG4+B,CAAY,CAAC5+B,EAAE,CAC1C4+B,CAAY,CAAC5+B,EAAE,CACnB8+B,EAAgB1+B,IAAI,CAACs+B,GACrBK,EAAiB3+B,IAAI,CAACu+B,GACtBK,EAAK5+B,IAAI,CAAC,CAACu7B,CAAO,CAAC37B,EAAE,CAAG0+B,EAASC,EAAS,EAC1Ch9B,EAAMvB,IAAI,CAAC4+B,CAAI,CAACh/B,EAAE,CAAC,EAAE,EACrBR,EAAMY,IAAI,CAAC4+B,CAAI,CAACh/B,EAAE,CAAC,EAAE,IAEjBgJ,IAAMvH,GACNuH,CAAAA,EAAI,CAAA,EAGZ,MAAO,CACH9I,OAAQ8+B,EACRr9B,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAqBA++B,GAAcn5B,cAAc,CAAGk5B,GAAoBH,GAA2B/4B,cAAc,CAAE,CAI1FvE,OAAQ,CACJI,MAAO,KAAK,EACZQ,OAAQ,GAMR8F,eAAgB,QACpB,CACJ,GACA7I,IAA4D+G,kBAAkB,CAAC,OAAQ84B,IAsCvF,GAAM,CAAE74B,IAAKu5B,EAA+B,CAAE,CAAG,AAACvgC,IAA6DG,WAAW,CAEpH,CAAEI,OAAQigC,EAAyB,CAAEhgC,QAASigC,EAA0B,CAAEhgC,MAAOigC,EAAwB,CAAE,CAAIhhC,GAerH,OAAMihC,WAA2BJ,GAM7Bz9B,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAEI0N,EAASgf,EAAU+R,EAAGC,EAC1B7M,EAEA1yB,EALMyB,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG09B,EAAK,EAAE,CACjH79B,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAMtB,GAAIkC,CAAAA,CAAAA,EAAKI,MAAM,CAAGL,CAAK,GAClB09B,GAA2Bv9B,CAAI,CAAC,EAAE,GACnCA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACE,MAAM,EAMlB,IAAK9B,EAAIyB,EAAS,EAAGzB,EAAI6B,EAAS7B,IAC9BuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAS,EAAGzB,EAAI,GAEzC0yB,EAAKnF,AADLA,CAAAA,EAAWX,GAA0BC,gBAAgB,CAACte,EAhBjB,EAAU,EAgByB,CAC3D,CAAC,EAAE,CAGhB+wB,EAAI,CAAA,CAAA,AAAEC,CAAAA,AAFNA,CAAAA,EAAKhS,CAAQ,CAAC,EAAE,AAAD,EACV3rB,CAAI,CAAC5B,EAAE,CAnBgB,EAmBT,AACP,EAAMu/B,CAAAA,EAAK7M,CAAC,EAAM,GAAG,EAC7BhxB,CAAI,CAAC1B,EAAE,GACPw/B,EAAGp/B,IAAI,CAAC,CAACsB,CAAI,CAAC1B,EAAE,CAAEs/B,EAAE,EACpB39B,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAACk/B,IAGnB,MAAO,CACHp/B,OAAQs/B,EACR79B,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAuBA6/B,GAAmBj6B,cAAc,CAAGg6B,GAAyBH,GAAgC75B,cAAc,CAAE,CAKzGvE,OAAQ,CACJI,MAAO,KAAK,EAIZQ,OAAQ,EACZ,CACJ,GACAy9B,GAA0BG,GAAmBrhC,SAAS,CAAE,CACpDoD,SAAU,aACd,GACA1C,IAA4D+G,kBAAkB,CAAC,YAAa45B,IAwC5F,GAAM,CAAE35B,IAAK+5B,EAAyB,CAAE,CAAG,AAAC/gC,IAA6DG,WAAW,CAE9G,CAAEK,QAASwgC,EAAoB,CAAEvgC,MAAOwgC,EAAkB,CAAE,CAAIvhC,IAUtE,SAASwhC,GAA+Bz8B,CAAM,CAAEzB,CAAI,CAAEE,CAAI,CAAE5B,CAAC,CAAEiB,CAAK,EAChE,IAAMuP,EAAS9O,CAAI,CAAC1B,EAAE,CAAEqG,EAASpF,EAAQ,EAAIW,CAAI,CAAC5B,EAAE,CAAG4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CACrEkC,EAAO/C,IAAI,CAAC,CAACoQ,EAAQnK,EAAO,CAChC,CAiBA,SAASw5B,GAA6B18B,CAAM,CAAEzB,CAAI,CAAEE,CAAI,CAAE5B,CAAC,EACvD,IAAM8/B,EAAO38B,EAAOrB,MAAM,CAAEi+B,EARrBntB,AAQ6CzP,EARvC0P,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,CAAE/S,CAAC,EACtC,MAAO,CAAC,KAAM8S,CAAI,CAAC,EAAE,CAAGC,CAAG,CAAC,EAAE,CAAI/S,CAAAA,EAAI,CAAA,EAAG,AAC7C,EAAE,CAAC,EAAE,CAJe,CAAA,AAAC8/B,CAAAA,AAUuCA,EAVhC,CAAA,EAAK,EAU2BA,CAVpB,EAU2BE,EAAOt+B,CAAI,CAAC1B,EAAI,EAAE,CAErF,OADAmD,EAAOoB,KAAK,GACL,CAACy7B,EAAMD,EAAK,AACvB,CAeA,MAAME,WAAqBR,GAMvBj+B,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMY,EAASZ,EAAOY,MAAM,CAAEC,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG0O,EAAS9O,CAAI,CAAC,EAAE,CAAEw+B,EAAM,EAAE,CAAEv+B,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CACxJwC,EAAQ,EAAGf,EAAQ,GAAIjB,EAAGmgC,EAAU95B,EAASzE,CAAI,CAAC,EAAE,CACxD,GAAIF,EAAKI,MAAM,CAAGL,EACd,OAGAi+B,GAAqB99B,CAAI,CAAC,EAAE,IAC5BX,EAAQJ,EAAOI,KAAK,CACpBoF,EAASzE,CAAI,CAAC,EAAE,CAACX,EAAM,EAG3B,IAAMkC,EAAS,CAAC,CAACqN,EAAQnK,EAAO,CAAC,CAEjC,KAAOrE,IAAUP,GACbm+B,GAA+Bz8B,EAAQzB,EAAME,EAAMI,EAAOf,GAC1De,IAGJ,IAAKhC,EAAIgC,EAAOhC,EAAI6B,EAAS7B,IAEzBkgC,EAAI9/B,IAAI,CADR+/B,EAAWN,GAA6B18B,EAAQzB,EAAME,EAAM5B,IAE5D2B,EAAMvB,IAAI,CAAC+/B,CAAQ,CAAC,EAAE,EACtB3gC,EAAMY,IAAI,CAAC+/B,CAAQ,CAAC,EAAE,EACtBP,GAA+Bz8B,EAAQzB,EAAME,EAAM5B,EAAGiB,GAM1D,OAHAi/B,EAAI9/B,IAAI,CADR+/B,EAAWN,GAA6B18B,EAAQzB,EAAME,EAAM5B,IAE5D2B,EAAMvB,IAAI,CAAC+/B,CAAQ,CAAC,EAAE,EACtB3gC,EAAMY,IAAI,CAAC+/B,CAAQ,CAAC,EAAE,EACf,CACHjgC,OAAQggC,EACRv+B,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAoBAygC,GAAa76B,cAAc,CAAGu6B,GAAmBF,GAA0Br6B,cAAc,CAAE,CACvFvE,OAAQ,CACJI,MAAO,EACPQ,OAAQ,CACZ,CACJ,GACA/C,IAA4D+G,kBAAkB,CAAC,MAAOw6B,IAsCtF,GAAM,CAAEv6B,IAAK06B,EAA4B,CAAE,CAAG,AAAC1hC,IAA6DG,WAAW,CAEjH,CAAEM,MAAOkhC,EAAqB,CAAEphC,OAAQqhC,EAAsB,CAAE,CAAIliC,GAe1E,OAAMmiC,WAAwBH,GAM1B5+B,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAM2/B,EAAW3/B,EAAO2/B,QAAQ,CAAEC,EAAY5/B,EAAO4/B,SAAS,CAAEC,EAAY7/B,EAAO6/B,SAAS,CAAG,IAAKC,EAAa,CAC7G,IAAO,EAAID,EACX,KAAQ,EAAIA,CAChB,EAAGh/B,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAG8+B,EAAS,EAAE,CAAEj/B,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAC9GQ,EAAGgJ,EAAG63B,EAAaC,EAAaC,EAAW,CAAA,EAAOC,EAAS,CAAA,EAE/D,GAAI,CAACt/B,GAAQA,EAAKI,MAAM,EAAI,GACvBD,GACI,CAAA,AAA6B,KAAA,IAAtBD,CAAI,CAAC,EAAE,CAAC4+B,EAAS,EACrB,AAA8B,KAAA,IAAvB5+B,CAAI,CAAC,EAAE,CAAC6+B,EAAU,AAAe,EAChD,OAGJ,IAAMQ,EAAiBr/B,CAAI,CAAC,EAAE,CAAC4+B,EAAS,CAAEU,EAAkBt/B,CAAI,CAAC,EAAE,CAAC6+B,EAAU,CAG9E,IAAKzgC,EAAI,EAAGA,EAAI6B,EAAS7B,IAEjB4B,CAAI,CAAC5B,EAAE,CAACwgC,EAAS,EAAIU,EAAkBP,EAAW35B,IAAI,EACtD45B,EAAOxgC,IAAI,CAAC,CAACsB,CAAI,CAAC,EAAE,CAAEw/B,EAAgB,EAEtCL,EAAc,CAACn/B,CAAI,CAAC1B,EAAE,CAAE4B,CAAI,CAAC5B,EAAE,CAACwgC,EAAS,CAAC,CAE1CM,EAAc,CAAA,EACdC,EAAW,CAAA,GAGNn/B,CAAI,CAAC5B,EAAE,CAACygC,EAAU,EAAIQ,EAAiBN,EAAW15B,GAAG,GAC1D25B,EAAOxgC,IAAI,CAAC,CAACsB,CAAI,CAAC,EAAE,CAAEu/B,EAAe,EAErCJ,EAAc,CAACn/B,CAAI,CAAC1B,EAAE,CAAE4B,CAAI,CAAC5B,EAAE,CAACygC,EAAU,CAAC,CAE3CK,EAAc,CAAA,EACdC,EAAW,CAAA,GAEXA,IACAp/B,EAAMvB,IAAI,CAACwgC,CAAM,CAAC,EAAE,CAAC,EAAE,EACvBphC,EAAMY,IAAI,CAACwgC,CAAM,CAAC,EAAE,CAAC,EAAE,EACvB53B,EAAIhJ,IACJA,EAAI6B,GAIZ,IAAK7B,EAAIgJ,EAAGhJ,EAAI6B,EAAS7B,IACjB8gC,GAEIl/B,CAAI,CAAC5B,EAAE,CAACwgC,EAAS,EAAIK,CAAW,CAAC,EAAE,EACnCA,CAAAA,EAAc,CAACn/B,CAAI,CAAC1B,EAAE,CAAE4B,CAAI,CAAC5B,EAAE,CAACwgC,EAAS,CAAC,AAAD,EAIzC5+B,CAAI,CAAC5B,EAAE,CAACygC,EAAU,EAClBI,CAAW,CAAC,EAAE,CAAGF,EAAW15B,GAAG,EAC/B+5B,CAAAA,EAASP,CAAQ,IAKjB7+B,CAAI,CAAC5B,EAAE,CAACygC,EAAU,EAAII,CAAW,CAAC,EAAE,EACpCA,CAAAA,EAAc,CAACn/B,CAAI,CAAC1B,EAAE,CAAE4B,CAAI,CAAC5B,EAAE,CAACygC,EAAU,CAAC,AAAD,EAI1C7+B,CAAI,CAAC5B,EAAE,CAACwgC,EAAS,EACjBK,CAAW,CAAC,EAAE,CAAGF,EAAW35B,IAAI,EAChCg6B,CAAAA,EAASR,CAAO,GAGT,CAAA,IAAXQ,IACAJ,EAAOxgC,IAAI,CAACygC,GACZl/B,EAAMvB,IAAI,CAACygC,CAAW,CAAC,EAAE,EACzBrhC,EAAMY,IAAI,CAACygC,CAAW,CAAC,EAAE,EACzBA,EAAc,CAACn/B,CAAI,CAAC1B,EAAE,CAAE4B,CAAI,CAAC5B,EAAE,CAACghC,EAAO,CAAC,CACxCF,EAAc,CAACA,EACfE,EAAS,CAAA,GAGjB,IAAMG,EAAYP,EAAO9+B,MAAM,CAS/B,OAPkB,IAAdq/B,GACAP,CAAM,CAACO,EAAY,EAAE,CAAC,EAAE,CAAGz/B,CAAI,CAACG,EAAU,EAAE,GAE5C++B,EAAOxgC,IAAI,CAACygC,GACZl/B,EAAMvB,IAAI,CAACygC,CAAW,CAAC,EAAE,EACzBrhC,EAAMY,IAAI,CAACygC,CAAW,CAAC,EAAE,GAEtB,CACH3gC,OAAQ0gC,EACRj/B,MAAOA,EACPnC,MAAOA,CACX,CACJ,CACJ,CAqBA+gC,GAAgBn7B,cAAc,CAAGi7B,GAAsBD,GAA6Bh7B,cAAc,CAAE,CAIhGvE,OAAQ,CAEJI,MAAO,KAAK,EACZQ,OAAQ,KAAK,EAQb++B,SAAU,EAQVC,UAAW,EAOXC,UAAW,CACf,CACJ,GACAJ,GAAuBC,GAAgBviC,SAAS,CAAE,CAC9C+C,eAAgB,CAAC,YAAY,CAC7BI,aAAc,CAAC,IAAI,CACnBC,SAAU,SACd,GACA1C,IAA4D+G,kBAAkB,CAAC,SAAU86B,IAsCzF,GAAM,CAAE76B,IAAK07B,EAAsC,CAAE,CAAG,AAAC1iC,IAA6DG,WAAW,CAE3H,CAAEK,QAASmiC,EAAiC,CAAEpiC,OAAQqiC,EAAgC,CAAEniC,MAAOoiC,EAA+B,CAAE,CAAInjC,GAe1I,OAAMojC,WAAkCJ,GAqBpCK,4BAA4B9/B,CAAK,CAAEnC,CAAK,CAAE,CAEtC,IAAMwhC,EAAS,IAAI,CAAC9/B,OAAO,CAACL,MAAM,CAACI,KAAK,CAAEygC,EAAkB,SAAUr7B,CAAM,CAAE26B,CAAM,EAChF,OAAOK,GAAkCh7B,GAAUA,CAAM,CAAC26B,EAAO,CAAG36B,CACxE,EAAGs7B,EAAOhgC,EAAMkR,MAAM,CAAC,SAAU+uB,CAAI,CAAEnJ,CAAG,EACtC,OAAOA,EAAMmJ,CACjB,EAAG,GAAIC,EAAOriC,EAAMqT,MAAM,CAAC,SAAUivB,CAAI,CAAErJ,CAAG,EAC1C,OAAOiJ,EAAgBjJ,EAAKuI,GAAUc,CAC1C,EAAG,GAAIC,EAAQJ,EAAOhgC,EAAMG,MAAM,CAAEkgC,EAAQH,EAAOriC,EAAMsC,MAAM,CAC3DmgC,EAAgBjiC,EAAGkiC,EAAmB,EAAGC,EAAqB,EAClE,IAAKniC,EAAI,EAAGA,EAAI2B,EAAMG,MAAM,CAAE9B,IAG1BkiC,GAAoBD,AAFpBA,CAAAA,EAAStgC,CAAK,CAAC3B,EAAE,CAAG+hC,CAAI,EACfL,CAAAA,EAAgBliC,CAAK,CAACQ,EAAE,CAAEghC,GAAUgB,CAAI,EAEjDG,GAAsBnyB,KAAKoyB,GAAG,CAACH,EAAQ,GAE3C,IAAMI,EAAQF,EACVD,EAAmBC,EAAqB,EAC5C,MAAO,CACHE,MAAOA,EACPC,UAAWN,EAAQK,EAAQN,CAC/B,CACJ,CAgBAQ,aAAaC,CAAc,CAAEC,CAAS,CAAE,CACpC,OAAOD,EAAeH,KAAK,CAAGI,EAAYD,EAAeF,SAAS,AACtE,CAgBAI,eAAe/gC,CAAK,CAAEghC,CAAS,CAAE,CAC7B,IAAMC,EAAUjhC,CAAK,CAAC,EAAE,CACxB,OAAOA,EAAM5B,GAAG,CAAC,SAAUyQ,CAAM,EAC7B,MAAO,AAACA,CAAAA,EAASoyB,CAAM,EAAKD,CAChC,EACJ,CAOAE,oBAAoBlhC,CAAK,CAAE,CACvB,IAAImhC,EAAUC,EAAiB/iC,EAC/B,IAAKA,EAAI,EAAGA,EAAI2B,EAAMG,MAAM,CAAG,EAAG9B,IAC9B8iC,CAAAA,EAAWnhC,CAAK,CAAC3B,EAAE,CAAG2B,CAAK,CAAC3B,EAAI,EAAE,AAAD,EAClB,GACV,CAAA,AAA2B,KAAA,IAApB+iC,GACJD,EAAWC,CAAc,GAC7BA,CAAAA,EAAkBD,CAAO,EAGjC,OAAOC,CACX,CAEAvhC,UAAUk2B,CAAU,CAAEsL,CAAsB,CAAE,CAC1C,IAQIR,EAAgBxiC,EAAGijC,EAAaC,EAAWT,EAAWU,EAAWC,EAAaC,EAAaC,EARzF3hC,EAAQ+1B,EAAW/1B,KAAK,CAAEnC,EAAQk4B,EAAWl4B,KAAK,CAAEiC,EAASuhC,EAAuBvhC,MAAM,CAEhG8hC,EAAgB,CACZ5hC,MAAO,EAAE,CACTnC,MAAO,EAAE,CACTU,OAAQ,EAAE,AACd,EAAGyiC,EAAY,IAAI,CAACzhC,OAAO,CAACL,MAAM,CAAC8hC,SAAS,EACxC,IAAI,CAACE,mBAAmB,CAAClhC,GAK7B,IAAK3B,EAAIyB,EAAS,EAAGzB,GAAK2B,EAAMG,MAAM,CAAG,EAAG9B,IACxCijC,EAAcjjC,EAAIyB,EAAS,EAC3ByhC,EAAYljC,EAAI,EAChByiC,EAAY9gC,CAAK,CAAC3B,EAAE,CACpBojC,EAAczhC,EAAMgJ,KAAK,CAACs4B,EAAaC,GACvCG,EAAc7jC,EAAMmL,KAAK,CAACs4B,EAAaC,GACvCI,EAAyB,IAAI,CAACZ,cAAc,CAACU,EAAaT,GAC1DH,EAAiB,IAAI,CAACf,2BAA2B,CAAC6B,EAAwBD,GAC1EF,EAAY,IAAI,CAACZ,YAAY,CAACC,EAAgBc,CAAsB,CAACA,EAAuBxhC,MAAM,CAAG,EAAE,EAEvGyhC,EAAcrjC,MAAM,CAACE,IAAI,CAAC,CACtBojC,yBAA0BhB,EAC1Bl+B,EAAGm+B,EACHx8B,EAAGk9B,CACP,GACI9B,GAAkCkC,EAAc5hC,KAAK,GACrD4hC,EAAc5hC,KAAK,CAACvB,IAAI,CAACqiC,GAEzBpB,GAAkCkC,EAAc/jC,KAAK,GACrD+jC,EAAc/jC,KAAK,CAACY,IAAI,CAAC+iC,GAGjC,OAAOI,CACX,CACJ,CAoBA/B,GAA0Bp8B,cAAc,CAAGm8B,GAAgCH,GAAuCh8B,cAAc,CAAE,CAC9HvE,OAAQ,CAgDJ8hC,UAAW,IACf,EACAt9B,QAAS,CACLC,cAAe,CACnB,CACJ,GACAg8B,GAAiCE,GAA0BxjC,SAAS,CAAE,CAClEoD,SAAU,6BACd,GACA1C,IAA4D+G,kBAAkB,CAAC,mBAAoB+7B,IAuCnG,GAAM,CAAEiC,iBAAkBC,EAAyD,CAAE,CAAG,AAAChlC,IAA6DG,WAAW,CAE3J,CAAEI,OAAQ0kC,EAAsC,CAAExkC,MAAOykC,EAAqC,CAAE,CAAIxlC,GAe1G,OAAMylC,WAAwCH,GAM1CnB,aAAaC,CAAc,CAAE,CACzB,OAAOA,EAAeH,KAAK,AAC/B,CACJ,CAoBAwB,GAAgCz+B,cAAc,CAAGw+B,GAAsCF,GAA0Dt+B,cAAc,EAC/Ju+B,GAAuCE,GAAgC7lC,SAAS,CAAE,CAC9EoD,SAAU,mCACd,GACA1C,IAA4D+G,kBAAkB,CAAC,wBAAyBo+B,IAuCxG,GAAM,CAAEJ,iBAAkBK,EAA4D,CAAE,CAAG,AAACplC,IAA6DG,WAAW,CAE9J,CAAEI,OAAQ8kC,EAAyC,CAAE5kC,MAAO6kC,EAAwC,CAAE,CAAI5lC,GAehH,OAAM6lC,WAA2CH,GAM7CvB,aAAaC,CAAc,CAAE,CACzB,OAAOA,EAAeF,SAAS,AACnC,CACJ,CAoBA2B,GAAmC7+B,cAAc,CAAG4+B,GAAyCF,GAA6D1+B,cAAc,EACxK2+B,GAA0CE,GAAmCjmC,SAAS,CAAE,CACpFoD,SAAU,uCACd,GACA1C,IAA4D+G,kBAAkB,CAAC,4BAA6Bw+B,IAuC5G,GAAM,CAAER,iBAAkBS,EAAwD,CAAE,CAAG,AAACxlC,IAA6DG,WAAW,CAE1J,CAAEI,OAAQklC,EAAqC,CAAEhlC,MAAOilC,EAAoC,CAAE,CAAIhmC,GAexG,OAAMimC,WAAuCH,GAazCI,aAAajC,CAAK,CAAE,CAChB,OAAOryB,AAAoB,IAAMA,KAAKu0B,EAAE,CAAjCv0B,KAAKw0B,IAAI,CAACnC,EACrB,CACAE,aAAaC,CAAc,CAAE,CACzB,OAAO,IAAI,CAAC8B,YAAY,CAAC9B,EAAeH,KAAK,CACjD,CACJ,CAoBAgC,GAA+Bj/B,cAAc,CAAGg/B,GAAqCF,GAAyD9+B,cAAc,CAAE,CAC1JC,QAAS,CACLuJ,YAAa,oFAEjB,CACJ,GACAu1B,GAAsCE,GAA+BrmC,SAAS,CAAE,CAC5EoD,SAAU,mCACd,GACA1C,IAA4D+G,kBAAkB,CAAC,wBAAyB4+B,IAiCxG,GAAM,CAAE3+B,IAAK++B,EAA4B,CAAE,CAAG,AAAC/lC,IAA6DG,WAAW,CAEjH,CAAE+G,aAAc8+B,EAA4B,CAAEzlC,OAAQ0lC,EAAsB,CAAExlC,MAAOylC,EAAqB,CAAE,CAAIxmC,GAuCtH,OAAMymC,WAAwBJ,GAM1BjjC,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IASImQ,EAAIC,EAAIC,EAAIC,EAAM2zB,EAAUC,EAAUC,EAAOC,EAAO7zB,EAAS7C,EAASvO,EATpEyB,EAASZ,EAAOY,MAAM,CAAEyjC,EAASrkC,EAAOqkC,MAAM,CAAEjkC,EAAQJ,EAAOI,KAAK,CAAES,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAErJqjC,EAAK,EAAE,CAEPC,EAAK,EAAE,CAGPC,EAAS,EAAE,CAAqB1jC,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAGtD,IAAIqC,CAAAA,EAAUJ,CAAK,GAGnB,IAAKzB,EAAI,EAAGA,GAAK6B,EAAS7B,IAAK,CAK3B,GAAIA,EAAI6B,EAAS,KAvDLoF,EAAKD,EAALC,EAwDkBrF,CAAI,CAAC5B,EAAE,CAZtB,EAY2B,CAAtC8kC,EAvDL,AAAGJ,GAA6B19B,CADdA,EAwD2BpF,CAAI,CAAC5B,EAAE,CAZ1B,EAYgC,EAvDnBiH,GACzC,CAAA,AAACy9B,GAA6B19B,EAAOC,GAAQ,CAAA,EAAM,IAsDWi+B,EACvDC,EAAG/kC,IAAI,CAAYwB,CAAI,CAAC5B,EAAE,CAbL,EAaW,CAjD7B0kC,GAA6B,EAAI,EAiDFI,IAClCM,EAAGhlC,IAAI,CAAYwB,CAAI,CAAC5B,EAAE,CAdf,EAcoB,CA5C7B0kC,GAA6B,EAAI,EA4CFI,GACrC,CACI9kC,GAAKyB,IACL2P,EAAU1P,EAAKiJ,KAAK,CAAC3K,EAAIyB,EAAQzB,GACjCuO,EAAU3M,EAAK+I,KAAK,CAAC3K,EAAIyB,EAAQzB,GACjCglC,EAAQ,KAAK,CAACxjC,UAAUtD,IAAI,CAAC,IAAI,CAAE,CAC/ByD,MAAOyP,EACP5R,MAAO2lC,EAAGx6B,KAAK,CAAC3K,EAAIyB,EAAQzB,EAChC,EAAG,CACCyB,OAAQA,CACZ,GACAwjC,EAAQ,KAAK,CAACzjC,UAAUtD,IAAI,CAAC,IAAI,CAAE,CAC/ByD,MAAOyP,EACP5R,MAAO4lC,EAAGz6B,KAAK,CAAC3K,EAAIyB,EAAQzB,EAChC,EAAG,CACCyB,OAAQA,CACZ,GAQA0P,EAAO4zB,AAPPA,CAAAA,EAAW,KAAK,CAACvjC,UAAUtD,IAAI,CAAC,IAAI,CAAE,CAClCyD,MAAOyP,EACP5R,MAAO+O,CACX,EAAG,CACC9M,OAAQA,EACRR,MAAOA,CACX,EAAC,EACeU,KAAK,CAAC,EAAE,CACxBsP,EAAK+zB,EAAMxlC,KAAK,CAAC,EAAE,CACnB0R,EAAK+zB,EAAMzlC,KAAK,CAAC,EAAE,CACnBwR,EAAK+zB,EAASvlC,KAAK,CAAC,EAAE,CACtB6lC,EAAOjlC,IAAI,CAAC,CAAC+Q,EAAMF,EAAID,EAAIE,EAAG,EAC9BvP,EAAMvB,IAAI,CAAC+Q,GACX3R,EAAMY,IAAI,CAAC,CAAC6Q,EAAID,EAAIE,EAAG,EAE/B,CACA,MAAO,CACHhR,OAAQmlC,EACR1jC,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CA0BAqlC,GAAgBz/B,cAAc,CAAGw/B,GAAsBH,GAA6Br/B,cAAc,CAAE,CAYhGvE,OAAQ,CACJY,OAAQ,GAMRyjC,OAAQ,KACRjkC,MAAO,CACX,EACA4N,UAAW,EACXiC,QAAS,CACLvE,OAAQ,CAIJsC,UAAW,CACf,CACJ,EACAkC,WAAY,CACRxE,OAAQ,CAIJsC,UAAW,CACf,CACJ,EACAE,aAAc,CACVC,cAAe,UACnB,CACJ,GACA21B,GAAuBE,GAAgB7mC,SAAS,CAAE,CAC9CuM,eAAgB,CAAC,MAAO,SAAS,CACjCD,cAAe,CAAC,UAAW,aAAa,CACxClJ,SAAU,qBACVL,eAAgB,CAAC,SAAU,SAAS,CACpCtB,cAAe,CAAC,MAAO,SAAU,SAAS,CAC1CuL,YAAa,QACjB,GACAyC,EAAoCJ,OAAO,CAACw3B,IAC5CnmC,IAA4D+G,kBAAkB,CAAC,SAAUo/B,IAuCzF,GAAM,CAAEn/B,IAAK4/B,EAA+B,CAAE,CAAG,AAAC5mC,IAA6DG,WAAW,CAEpH,CAAEI,OAAQsmC,EAAyB,CAAEpmC,MAAOqmC,EAAwB,CAAEtmC,QAASumC,EAA0B,CAAE,CAAIrnC,GAerH,OAAMsnC,WAA2BJ,GAC7BlyB,aAAc,CAMV,KAAK,IAAIzS,WACT,IAAI,CAAC8D,eAAe,CAAG,CAAA,CAC3B,CAMAjD,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAM8kC,EAAUrmC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEkC,EAAO,EAAE,CAAEkkC,EAAK,EAAE,CAAEjkC,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAAEyB,EAAQJ,EAAOI,KAAK,CAC/G4kC,EAAY,EAAGC,EAAc,EAAGC,EAAU,EAAGC,EAAU,EAAGC,EAAU,EAExE,IAAK,IAAIjmC,EAAI,EAAGA,EAAI2lC,EAAQ7jC,MAAM,CAAE9B,IAC5BA,CAAAA,AAAM,IAANA,GAAW2lC,CAAO,CAAC3lC,EAAE,GAAK2lC,CAAO,CAAC3lC,EAAI,EAAE,AAAD,GACvCimC,IAEJvkC,EAAKtB,IAAI,CAAC6lC,GAEd,IAAK,IAAIjmC,EAAI,EAAGA,EAAI0B,EAAKI,MAAM,CAAE9B,IAC7B+lC,GAAWrkC,CAAI,CAAC1B,EAAE,CAClBgmC,GAAWP,GAA2B7jC,CAAI,CAAC5B,EAAE,EAAI4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAAGW,CAAI,CAAC5B,EAAE,CAE7E,IAAMkmC,EAAQH,EAAUrkC,EAAKI,MAAM,CAAEqkC,EAAQH,EAAUpkC,EAAKE,MAAM,CAClE,IAAK,IAAI9B,EAAI,EAAGA,EAAI0B,EAAKI,MAAM,CAAE9B,IAAK,CAClC,IAAMiG,EAAIw/B,GAA2B7jC,CAAI,CAAC5B,EAAE,EAAI4B,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAAGW,CAAI,CAAC5B,EAAE,CACxE6lC,GAAa,AAACnkC,CAAAA,CAAI,CAAC1B,EAAE,CAAGkmC,CAAI,EAAMjgC,CAAAA,EAAIkgC,CAAI,EAC1CL,GAAe91B,KAAKoyB,GAAG,CAAC1gC,CAAI,CAAC1B,EAAE,CAAGkmC,EAAO,EAC7C,CAEA,IAAK,IAAIlmC,EAAI,EAAGA,EAAI0B,EAAKI,MAAM,CAAE9B,IAAK,CAElC,GAAI2lC,CAAO,CAAC3lC,EAAE,GAAK2B,CAAK,CAACA,EAAMG,MAAM,CAAG,EAAE,CACtC,SAEJ,IAAMwC,EAAIqhC,CAAO,CAAC3lC,EAAE,CAAEiG,EAAIkgC,EAAQ,AAACN,EAAYC,EAAgBpkC,CAAAA,CAAI,CAAC1B,EAAE,CAAGkmC,CAAI,EAC7EN,EAAGxlC,IAAI,CAAC,CAACkE,EAAG2B,EAAE,EACdtE,EAAMvB,IAAI,CAACkE,GACX9E,EAAMY,IAAI,CAAC6F,EACf,CACA,MAAO,CACHtE,MAAOA,EACPnC,MAAOA,EACPU,OAAQ0lC,CACZ,CACJ,CACJ,CAgBAF,GAAmBtgC,cAAc,CAAGogC,GAAyBF,GAAgClgC,cAAc,CAAE,CAIzGvE,OAAQ,CACJY,OAAQ,KAAK,EAQbR,MAAO,CACX,CACJ,GACAskC,GAA0BG,GAAmB1nC,SAAS,CAAE,CACpDoD,SAAU,YACVL,eAAgB,KAAK,CACzB,GACArC,IAA4D+G,kBAAkB,CAAC,YAAaigC,IAuC5F,GAAM,CAAEhgC,IAAK0gC,EAAoC,CAAE,CAAG,AAAC1nC,IAA6DG,WAAW,CAEzH,CAAE+G,aAAcygC,EAAoC,CAAEl8B,QAASm8B,EAA+B,CAAErnC,OAAQsnC,EAA8B,CAAErnC,QAASsnC,EAA+B,CAAErnC,MAAOsnC,EAA6B,CAAE,CAAIroC,GAelO,OAAMsoC,WAAgCN,GAMlCjkC,MAAO,CACH,IAAMwkC,EAAOhmC,UACbE,EAAS8lC,CAAI,CAAC,EAAE,CAAC9lC,MAAM,CACvB+lC,EAAc/lC,GAAUA,EAAOygB,OAAO,CAAGzgB,EAAOygB,OAAO,CAAG,KAAK,CAC/DulB,CAH8B,IAAI,CAG9BC,gBAAgB,CAAG,AAACpoC,IAA6DG,WAAW,CAAC+nC,EAAY,EAAIR,GACjHS,AAJ8B,IAAI,CAI9BC,gBAAgB,CAAC9oC,SAAS,CAACmE,IAAI,CAACzB,KAAK,CAJX,IAAI,CAIaimC,EACnD,CACAI,wBAAwBC,CAAQ,CAAEC,CAAa,CAAE,CAC7C,OAAOZ,GAAqCW,EAAWC,GAAiBA,EAAgB,GAC5F,CACAzlC,UAAUlC,CAAM,CAAEuB,CAAM,CAAE,CACtB,IAAMI,EAAQJ,EAAOI,KAAK,CAAES,EAAOpC,EAAOqC,KAAK,CAAEC,EAAOtC,EAAOE,KAAK,CAAEqC,EAAUD,EAAOA,EAAKE,MAAM,CAAG,EAAGolC,EAAsB,EAAE,CAAEvlC,EAAQ,EAAE,CAAEnC,EAAQ,EAAE,CAExJsnC,EAAmB,IAAI,CAACA,gBAAgB,CAAEt1B,EAASg1B,GAAgC5kC,CAAI,CAAC,EAAE,EAE1F1B,EAAS4mC,EAAiB9oC,SAAS,CAACwD,SAAS,CAAClC,EAAQuB,GAAS+6B,EAAU17B,EAAOV,KAAK,CAAEw7B,EAAQt5B,EAAK2C,OAAO,CAACnE,EAAOyB,KAAK,CAAC,EAAE,EAE3H,GAAI,AAACi6B,GAAWA,AAAmB,IAAnBA,EAAQ95B,MAAM,EACzBwkC,GAAgCrlC,KACjCW,CAAAA,EAAKE,MAAM,EAAIk5B,CAAI,GAIvB,IAAK,IAAIh7B,EAAIg7B,EAAOh7B,EAAI6B,EAAS7B,IAAK,CAClC,IAAMmnC,EAAsB,IAAI,CAACJ,uBAAuB,CAACv1B,EAAS5P,CAAI,CAAC5B,EAAE,CAACiB,EAAM,CAAGW,CAAI,CAAC5B,EAAE,CAAE47B,CAAO,CAAC57B,EAAIg7B,EAAM,EAC9GkM,EAAoB9mC,IAAI,CAAC,CACrBsB,CAAI,CAAC1B,EAAE,CACPmnC,EACH,EACDxlC,EAAMvB,IAAI,CAACsB,CAAI,CAAC1B,EAAE,EAClBR,EAAMY,IAAI,CAAC+mC,EACf,CACA,MAAO,CACHjnC,OAAQgnC,EACRvlC,MAAOA,EACPnC,MAAOA,CACX,EACJ,CACJ,CAwBAknC,GAAwBthC,cAAc,CAAGqhC,GAA8BL,GAAqChhC,cAAc,CAAE,CACxHvE,OAAQ,CAUJygB,QAAS,MACTrgB,MAAO,CACX,EACAyN,OAAQ,CACJC,QAAS,CAAA,CACb,EACAI,aAAc,CACVC,cAAe,UACnB,CACJ,GACAu3B,GAA+BG,GAAwB1oC,SAAS,CAAE,CAC9DoD,SAAU,kBACVL,eAAgB,CAAC,SAAU,UAAU,AACzC,GACArC,IAA4D+G,kBAAkB,CAAC,iBAAkBihC,IA+BpF3pC,EAAoBK,CAAC,CAAzB,CAAC,EAIiC,CAAG,GAuD9C,IAAMgqC,GAAKhpC,GACXgpC,CAAAA,GAAEtqC,wBAAwB,CACtBsqC,GAAEtqC,wBAAwB,EAAI2Q,EACL,IAAM45B,GAAuBjpC,WAEjDipC,MAAsBC,OAAO"}