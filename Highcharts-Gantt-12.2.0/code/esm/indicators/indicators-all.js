import*as e from"../highcharts.js";import*as t from"../modules/datagrouping.js";import"../modules/stock.js";var s,a={};a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var s in t)a.o(t,s)&&!a.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let o=e.default;var i=a.n(o);let n=e.default.Chart;var r=a.n(n);let l=e.default.SeriesRegistry;var p=a.n(l);let{line:u}=p().seriesTypes,{addEvent:h,fireEvent:d,error:c,extend:m,isArray:g,merge:y,pick:f}=i(),x=(e,t)=>{let s=[],a=e.pointArrayMap,o=t&&e.dataTable.modified||e.dataTable;if(!a)return e.getColumn("y",t);let i=a.map(s=>e.getColumn(s,t));for(let e=0;e<o.rowCount;e++){let t=a.map((t,s)=>i[s]?.[e]||0);s.push(t)}return s};class D extends u{destroy(){this.dataEventsToUnbind.forEach(function(e){e()}),super.destroy.apply(this,arguments)}getName(){let e=[],t=this.name;return t||((this.nameComponents||[]).forEach(function(t,s){e.push(this.options.params[t]+f(this.nameSuffixes[s],""))},this),t=(this.nameBase||this.type.toUpperCase())+(this.nameComponents?" ("+e.join(", ")+")":"")),t}getValues(e,t){let s=t.period,a=e.xData||[],o=e.yData,i=o.length,n=[],r=[],l=[],p,u=-1,h=0,d,c=0;if(!(a.length<s)){for(g(o[0])&&(u=t.index?t.index:0);h<s-1;)c+=u<0?o[h]:o[h][u],h++;for(p=h;p<i;p++)c+=u<0?o[p]:o[p][u],d=[a[p],c/s],n.push(d),r.push(d[0]),l.push(d[1]),c-=u<0?o[p-h]:o[p-h][u];return{values:n,xData:r,yData:l}}}init(e,t){let s=this;super.init.call(s,e,t);let a=h(r(),"afterLinkSeries",function({isUpdating:t}){if(t)return;let a=!!s.dataEventsToUnbind.length;if(!s.linkedParent)return c("Series "+s.options.linkedTo+" not found! Check `linkedTo`.",!1,e);if(!a&&(s.dataEventsToUnbind.push(h(s.linkedParent,"updatedData",function(){s.recalculateValues()})),s.calculateOn.xAxis&&s.dataEventsToUnbind.push(h(s.linkedParent.xAxis,s.calculateOn.xAxis,function(){s.recalculateValues()}))),"init"===s.calculateOn.chart)s.closestPointRange||s.recalculateValues();else if(!a){let e=h(s.chart,s.calculateOn.chart,function(){s.recalculateValues(),e()})}},{order:0});s.dataEventsToUnbind=[],s.eventsToUnbind.push(a)}recalculateValues(){let e=[],t=this.dataTable,s=this.points||[],a=this.dataTable.rowCount,o=!0,i,n,r,l,p=this.linkedParent.yData,u=this.linkedParent.processedYData;this.linkedParent.xData=this.linkedParent.getColumn("x"),this.linkedParent.yData=x(this.linkedParent),this.linkedParent.processedYData=x(this.linkedParent,!0);let h=this.linkedParent.options&&this.linkedParent.dataTable.rowCount&&this.getValues(this.linkedParent,this.options.params)||{values:[],xData:[],yData:[]};delete this.linkedParent.xData,this.linkedParent.yData=p,this.linkedParent.processedYData=u;let c=this.pointArrayMap||["y"],m={};if(h.yData.forEach(e=>{c.forEach((t,s)=>{let a=m[t]||[];a.push(g(e)?e[s]:e),m[t]||(m[t]=a)})}),a&&!this.hasGroupedData&&this.visible&&this.points){if(this.cropped){this.xAxis&&(r=this.xAxis.min,l=this.xAxis.max);let a=this.cropData(t,r,l),o=["x",...this.pointArrayMap||["y"]];for(let t=0;t<(a.modified?.rowCount||0);t++){let s=o.map(e=>this.getColumn(e)[t]||0);e.push(s)}let p=this.getColumn("x");i=h.xData.indexOf(p[0]),n=h.xData.indexOf(p[p.length-1]),-1===i&&n===h.xData.length-2&&e[0][0]===s[0].x&&e.shift(),this.updateData(e)}else(this.updateAllPoints||h.xData.length!==a-1&&h.xData.length!==a+1)&&(o=!1,this.updateData(h.values))}o&&(t.setColumns({...m,x:h.xData}),this.options.data=h.values),this.calculateOn.xAxis&&this.getColumn("x",!0).length&&(this.isDirty=!0,this.redraw()),this.isDirtyData=!!this.linkedSeries.length,d(this,"updatedData")}processData(){let e=this.options.compareToMain,t=this.linkedParent;super.processData.apply(this,arguments),this.dataModify&&t&&t.dataModify&&t.dataModify.compareValue&&e&&(this.dataModify.compareValue=t.dataModify.compareValue)}}D.defaultOptions=y(u.defaultOptions,{name:void 0,tooltip:{valueDecimals:4},linkedTo:void 0,compareToMain:!1,params:{index:3,period:14}}),m(D.prototype,{calculateOn:{chart:"init"},hasDerivedData:!0,nameComponents:["period"],nameSuffixes:[],useCommonDataGrouping:!0}),p().registerSeriesType("sma",D);let{sma:v}=p().seriesTypes,{correctFloat:A,isArray:S,merge:b}=i();class T extends v{accumulatePeriodPoints(e,t,s){let a=0,o=0,i=0;for(;o<e;)a+=t<0?s[o]:s[o][t],o++;return a}calculateEma(e,t,s,a,o,i,n){let r=e[s-1],l=i<0?t[s-1]:t[s-1][i];return[r,void 0===o?n:A(l*a+o*(1-a))]}getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=2/(s+1),r=[],l=[],p=[],u,h,d,c=-1,m=0,g=0;if(!(i<s)){for(S(o[0])&&(c=t.index?t.index:0),g=this.accumulatePeriodPoints(s,c,o)/s,d=s;d<i+1;d++)h=this.calculateEma(a,o,d,n,u,c,g),r.push(h),l.push(h[0]),p.push(h[1]),u=h[1];return{values:r,xData:l,yData:p}}}}T.defaultOptions=b(v.defaultOptions,{params:{index:3,period:9}}),p().registerSeriesType("ema",T);let{sma:C}=p().seriesTypes,{error:P,extend:M,merge:V}=i();class L extends C{static populateAverage(e,t,s,a,o){let i=t[a][1],n=t[a][2],r=t[a][3],l=s[a],p=r===i&&r===n||i===n?0:(2*r-n-i)/(i-n)*l;return[e[a],p]}getValues(e,t){let s,a,o,i=t.period,n=e.xData,r=e.yData,l=t.volumeSeriesID,p=e.chart.get(l),u=p?.getColumn("y"),h=r?r.length:0,d=[],c=[],m=[];if(!(n.length<=i)||!h||4===r[0].length){if(!p){P("Series "+l+" not found! Check `volumeSeriesID`.",!0,e.chart);return}for(a=i;a<h;a++)s=d.length,o=L.populateAverage(n,r,u,a,i),s>0&&(o[1]+=d[s-1][1]),d.push(o),c.push(o[0]),m.push(o[1]);return{values:d,xData:c,yData:m}}}}L.defaultOptions=V(C.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),M(L.prototype,{nameComponents:!1,nameBase:"Accumulation/Distribution"}),p().registerSeriesType("ad",L);let{noop:k}=i(),{column:{prototype:O},sma:E}=p().seriesTypes,{extend:w,merge:I,correctFloat:N,isArray:B}=i();class G extends E{drawGraph(){let e,t=this.options,s=this.points,a=this.userOptions.color,o=t.greaterBarColor,i=t.lowerBarColor,n=s[0];if(!a&&n)for(e=1,n.color=o;e<s.length;e++)s[e].y>s[e-1].y?s[e].color=o:s[e].y<s[e-1].y?s[e].color=i:s[e].color=s[e-1].color}getValues(e){let t=e.xData||[],s=e.yData||[],a=s.length,o=[],i=[],n=[],r,l,p,u,h,d,c=0,m=0;if(!(t.length<=34)&&B(s[0])&&4===s[0].length){for(h=0;h<33;h++)u=(s[h][1]+s[h][2])/2,h>=29&&(m=N(m+u)),c=N(c+u);for(d=33;d<a;d++)m=N(m+(u=(s[d][1]+s[d][2])/2)),c=N(c+u),r=N(m/5-c/34),o.push([t[d],r]),i.push(t[d]),n.push(r),l=d+1-5,p=d+1-34,m=N(m-(s[l][1]+s[l][2])/2),c=N(c-(s[p][1]+s[p][2])/2);return{values:o,xData:i,yData:n}}}}G.defaultOptions=I(E.defaultOptions,{params:{index:void 0,period:void 0},greaterBarColor:"#06b535",lowerBarColor:"#f21313",threshold:0,groupPadding:.2,pointPadding:.2,crisp:!1,states:{hover:{halo:{size:0}}}}),w(G.prototype,{nameBase:"AO",nameComponents:void 0,markerAttribs:k,getColumnMetrics:O.getColumnMetrics,crispCol:O.crispCol,translate:O.translate,drawPoints:O.drawPoints}),p().registerSeriesType("ao",G);let{sma:{prototype:W}}=p().seriesTypes,{defined:z,error:Y,merge:F}=i();!function(e){let t=["bottomLine"],s=["top","bottom"],a=["top"];function o(e){return"plot"+e.charAt(0).toUpperCase()+e.slice(1)}function i(e,t){let s=[];return(e.pointArrayMap||[]).forEach(e=>{e!==t&&s.push(o(e))}),s}function n(){let e=this,t=e.pointValKey,s=e.linesApiNames,a=e.areaLinesNames,n=e.points,r=e.options,l=e.graph,p={options:{gapSize:r.gapSize}},u=[],h=i(e,t),d=n.length,c;if(h.forEach((e,t)=>{for(u[t]=[];d--;)c=n[d],u[t].push({x:c.x,plotX:c.plotX,plotY:c[e],isNull:!z(c[e])});d=n.length}),e.userOptions.fillColor&&a.length){let t=u[h.indexOf(o(a[0]))],s=1===a.length?n:u[h.indexOf(o(a[1]))],i=e.color;e.points=s,e.nextPoints=t,e.color=e.userOptions.fillColor,e.options=F(n,p),e.graph=e.area,e.fillGraph=!0,W.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=i}s.forEach((t,s)=>{u[s]?(e.points=u[s],r[t]?e.options=F(r[t].styles,p):Y('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],W.drawGraph.call(e),e["graph"+t]=e.graph):Y('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=n,e.options=r,e.graph=l,W.drawGraph.call(e)}function r(e){let t,s=[],a=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((t=W.getGraphPath.call(this,this.nextPoints))&&t.length){t[0][0]="L",s=W.getGraphPath.call(this,e),a=t.slice(0,s.length);for(let e=a.length-1;e>=0;e--)s.push(a[e])}}else s=W.getGraphPath.apply(this,arguments);return s}function l(e){let t=[];return(this.pointArrayMap||[]).forEach(s=>{t.push(e[s])}),t}function p(){let e=this.pointArrayMap,t=[],s;t=i(this),W.translate.apply(this,arguments),this.points.forEach(a=>{e.forEach((e,o)=>{s=a[e],this.dataModify&&(s=this.dataModify.modifyValue(s)),null!==s&&(a[t[o]]=this.yAxis.toPixels(s,!0))})})}e.compose=function(e){let o=e.prototype;return o.linesApiNames=o.linesApiNames||t.slice(),o.pointArrayMap=o.pointArrayMap||s.slice(),o.pointValKey=o.pointValKey||"top",o.areaLinesNames=o.areaLinesNames||a.slice(),o.drawGraph=n,o.getGraphPath=r,o.toYData=l,o.translate=p,e}}(s||(s={}));let X=s,{sma:R}=p().seriesTypes,{extend:K,merge:U,pick:Z}=i();function j(e,t){let s=e[0],a=0,o;for(o=1;o<e.length;o++)("max"===t&&e[o]>=s||"min"===t&&e[o]<=s)&&(s=e[o],a=o);return a}class _ extends R{getValues(e,t){let s,a,o,i,n,r=t.period,l=e.xData,p=e.yData,u=p?p.length:0,h=[],d=[],c=[];for(i=r-1;i<u;i++)o=j((n=p.slice(i-r+1,i+2)).map(function(e){return Z(e[2],e)}),"min"),s=j(n.map(function(e){return Z(e[1],e)}),"max")/r*100,a=o/r*100,l[i+1]&&(h.push([l[i+1],s,a]),d.push(l[i+1]),c.push([s,a]));return{values:h,xData:d,yData:c}}}_.defaultOptions=U(R.defaultOptions,{params:{index:void 0,period:25},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Aroon Up: {point.y}<br/>Aroon Down: {point.aroonDown}<br/>'},aroonDown:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),K(_.prototype,{areaLinesNames:[],linesApiNames:["aroonDown"],nameBase:"Aroon",pointArrayMap:["y","aroonDown"],pointValKey:"y"}),X.compose(_),p().registerSeriesType("aroon",_);let{aroon:q}=p().seriesTypes,{extend:H,merge:$}=i();class J extends q{getValues(e,t){let s,a,o=[],i=[],n=[],r=super.getValues.call(this,e,t);for(a=0;a<r.yData.length;a++)s=r.yData[a][0]-r.yData[a][1],o.push([r.xData[a],s]),i.push(r.xData[a]),n.push(s);return{values:o,xData:i,yData:n}}}J.defaultOptions=$(q.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b>: {point.y}'}}),H(J.prototype,{nameBase:"Aroon Oscillator",linesApiNames:[],pointArrayMap:["y"],pointValKey:"y"}),X.compose(q),p().registerSeriesType("aroonoscillator",J);let{sma:Q}=p().seriesTypes,{isArray:ee,merge:et}=i();function es(e,t){let s=e[1]-e[2];return Math.max(s,void 0===t?0:Math.abs(e[1]-t[3]),void 0===t?0:Math.abs(e[2]-t[3]))}class ea extends Q{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=[[a[0],o[0]]],r=[],l=[],p=[],u,h,d=0,c=1,m=0;if(!(a.length<=s)&&ee(o[0])&&4===o[0].length){for(h=1;h<=i;h++){var g,y,f,x,D,v;!function(e,t,s,a){let o=t[a],i=s[a];e.push([o,i])}(n,a,o,h),s<c?(d=(g=0,y=a,f=o,x=h,D=s,v=d,u=[y[x-1],(v*(D-1)+es(f[x-1],f[x-2]))/D])[1],r.push(u),l.push(u[0]),p.push(u[1])):(s===c?(d=m/(h-1),r.push([a[h-1],d]),l.push(a[h-1]),p.push(d)):m+=es(o[h-1],o[h-2]),c++)}return{values:r,xData:l,yData:p}}}}ea.defaultOptions=et(Q.defaultOptions,{params:{index:void 0}}),p().registerSeriesType("atr",ea);let{sma:eo}=p().seriesTypes,{extend:ei,isArray:en,merge:er}=i();class el extends eo{init(){p().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=er({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s,a,o,i,n,r,l,u,h,d=t.period,c=t.standardDeviation,m=[],g=[],y=e.xData,f=e.yData,x=f?f.length:0,D=[];if(y.length<d)return;let v=en(f[0]);for(h=d;h<=x;h++)n=y.slice(h-d,h),r=f.slice(h-d,h),i=(u=p().seriesTypes.sma.prototype.getValues.call(this,{xData:n,yData:r},t)).xData[0],s=u.yData[0],l=function(e,t,s,a){let o=e.length,i=0,n=0,r,l=0;for(;i<o;i++)l+=(r=(s?e[i][t]:e[i])-a)*r;return Math.sqrt(l/=o-1)}(r,t.index,v,s),a=s+c*l,o=s-c*l,D.push([i,a,s,o]),m.push(i),g.push([a,s,o]);return{values:D,xData:m,yData:g}}}el.defaultOptions=er(eo.defaultOptions,{params:{period:20,standardDeviation:2,index:3},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),ei(el.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","standardDeviation"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),X.compose(el),p().registerSeriesType("bb",el);let{sma:ep}=p().seriesTypes,{isArray:eu,merge:eh}=i();class ed extends ep{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=[],r=[],l=[],p=[],u,h,d=[],c,m=1,g,y,f,x;if(!(a.length<=s)&&eu(o[0])&&4===o[0].length){for(;m<s;)h=o[m-1],n.push((h[1]+h[2]+h[3])/3),m++;for(x=s;x<=i;x++)y=((h=o[x-1])[1]+h[2]+h[3])/3,c=n.push(y),g=(d=n.slice(c-s)).reduce(function(e,t){return e+t},0)/s,f=function(e,t){let s=e.length,a=0,o;for(o=0;o<s;o++)a+=Math.abs(t-e[o]);return a}(d,g)/s,u=(y-g)/(.015*f),r.push([a[x-1],u]),l.push(a[x-1]),p.push(u);return{values:r,xData:l,yData:p}}}}ed.defaultOptions=eh(ep.defaultOptions,{params:{index:void 0}}),p().registerSeriesType("cci",ed);let{sma:ec}=p().seriesTypes,{merge:em}=i();class eg extends ec{constructor(){super(...arguments),this.nameBase="Chaikin Money Flow"}isValid(){let e=this.chart,t=this.options,s=this.linkedParent,a=this.volumeSeries||(this.volumeSeries=e.get(t.params.volumeSeriesID)),o=s?.pointArrayMap?.length===4;function i(e){return e.dataTable.rowCount>=t.params.period}return!!(s&&a&&i(s)&&i(a)&&o)}getValues(e,t){if(this.isValid())return this.getMoneyFlow(e.xData,e.yData,this.volumeSeries.getColumn("y"),t.period)}getMoneyFlow(e,t,s,a){let o=t.length,i=[],n=[],r=[],l=[],p,u,h=-1,d=0,c=0;function m(e,t){let s=e[1],a=e[2],o=e[3];return null!==t&&null!==s&&null!==a&&null!==o&&s!==a?(o-a-(s-o))/(s-a)*t:(h=p,null)}if(a>0&&a<=o){for(p=0;p<a;p++)i[p]=m(t[p],s[p]),d+=s[p],c+=i[p];for(n.push(e[p-1]),r.push(p-h>=a&&0!==d?c/d:null),l.push([n[0],r[0]]);p<o;p++)i[p]=m(t[p],s[p]),d-=s[p-a],d+=s[p],c-=i[p-a],c+=i[p],u=[e[p],p-h>=a?c/d:null],n.push(u[0]),r.push(u[1]),l.push([u[0],u[1]])}return{values:l,xData:n,yData:r}}}eg.defaultOptions=em(ec.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),p().registerSeriesType("cmf",eg);let{sma:ey}=p().seriesTypes,{correctFloat:ef,extend:ex,isArray:eD,merge:ev}=i();class eA extends ey{calculateDM(e,t,s){let a,o=e[t][1],i=e[t][2],n=e[t-1][1],r=e[t-1][2];return ef(o-n>r-i?s?Math.max(o-n,0):0:s?0:Math.max(r-i,0))}calculateDI(e,t){return e/t*100}calculateDX(e,t){return ef(Math.abs(e-t)/Math.abs(e+t)*100)}smoothValues(e,t,s){return ef(e-e/s+t)}getTR(e,t){return ef(Math.max(e[1]-e[2],t?Math.abs(e[1]-t[3]):0,t?Math.abs(e[2]-t[3]):0))}getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=[],r=[],l=[];if(a.length<=s||!eD(o[0])||4!==o[0].length)return;let p=0,u=0,h=0,d;for(d=1;d<i;d++){let e,t,i,c,m,g,y,f,x;d<=s?(c=this.calculateDM(o,d,!0),m=this.calculateDM(o,d),g=this.getTR(o[d],o[d-1]),p+=c,u+=m,h+=g,d===s&&(y=this.calculateDI(p,h),f=this.calculateDI(u,h),x=this.calculateDX(p,u),n.push([a[d],x,y,f]),r.push(a[d]),l.push([x,y,f]))):(c=this.calculateDM(o,d,!0),m=this.calculateDM(o,d),g=this.getTR(o[d],o[d-1]),e=this.smoothValues(p,c,s),t=this.smoothValues(u,m,s),i=this.smoothValues(h,g,s),p=e,u=t,h=i,y=this.calculateDI(p,h),f=this.calculateDI(u,h),x=this.calculateDX(p,u),n.push([a[d],x,y,f]),r.push(a[d]),l.push([x,y,f]))}return{values:n,xData:r,yData:l}}}eA.defaultOptions=ev(ey.defaultOptions,{params:{index:void 0},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">DX</span>: {point.y}<br/><span style="color: {point.series.options.plusDILine.styles.lineColor}">+DI</span>: {point.plusDI}<br/><span style="color: {point.series.options.minusDILine.styles.lineColor}">-DI</span>: {point.minusDI}<br/>'},plusDILine:{styles:{lineWidth:1,lineColor:"#06b535"}},minusDILine:{styles:{lineWidth:1,lineColor:"#f21313"}},dataGrouping:{approximation:"averages"}}),ex(eA.prototype,{areaLinesNames:[],nameBase:"DMI",linesApiNames:["plusDILine","minusDILine"],pointArrayMap:["y","plusDI","minusDI"],parallelArrays:["x","y","plusDI","minusDI"],pointValKey:"y"}),X.compose(eA),p().registerSeriesType("dmi",eA);let{sma:eS}=p().seriesTypes,{extend:eb,merge:eT,correctFloat:eC,pick:eP}=i();function eM(e,t,s,a,o){let i=eP(t[s][a],t[s]);return o?eC(e-i):eC(e+i)}class eV extends eS{getValues(e,t){let s=t.period,a=t.index,o=Math.floor(s/2+1),i=s+o,n=e.xData||[],r=e.yData||[],l=r.length,p=[],u=[],h=[],d,c,m,g,y,f=0;if(!(n.length<=i)){for(g=0;g<s-1;g++)f=eM(f,r,g,a);for(y=0;y<=l-i;y++)c=y+s-1,m=y+i-1,f=eM(f,r,c,a),d=eP(r[m][a],r[m])-f/s,f=eM(f,r,y,a,!0),p.push([n[m],d]),u.push(n[m]),h.push(d);return{values:p,xData:u,yData:h}}}}eV.defaultOptions=eT(eS.defaultOptions,{params:{index:0,period:21}}),eb(eV.prototype,{nameBase:"DPO"}),p().registerSeriesType("dpo",eV);let{ema:eL}=p().seriesTypes,{correctFloat:ek,extend:eO,merge:eE,error:ew}=i();class eI extends eL{getValues(e,t){let s,a,o=t.periods,i=t.period,n=[],r=[],l=[];if(2!==o.length||o[1]<=o[0]){ew('Error: "Chaikin requires two periods. Notice, first period should be lower than the second one."');return}let p=L.prototype.getValues.call(this,e,{volumeSeriesID:t.volumeSeriesID,period:i});if(!p)return;let u=super.getValues.call(this,p,{period:o[0]}),h=super.getValues.call(this,p,{period:o[1]});if(!u||!h)return;let d=o[1]-o[0];for(a=0;a<h.yData.length;a++)s=ek(u.yData[a+d]-h.yData[a]),n.push([h.xData[a],s]),r.push(h.xData[a]),l.push(s);return{values:n,xData:r,yData:l}}}eI.defaultOptions=eE(eL.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",period:9,periods:[3,10]}}),eO(eI.prototype,{nameBase:"Chaikin Osc",nameComponents:["periods"]}),p().registerSeriesType("chaikin",eI);let{sma:eN}=p().seriesTypes,{isNumber:eB,merge:eG}=i();class eW extends eN{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=[],r=[],l=[],p,u=t.index,h;if(a.length<s)return;eB(o[0])?h=o:(u=Math.min(u,o[0].length-1),h=o.map(e=>e[u]));let d=0,c=0,m=0,g;for(let e=s;e>0;e--)h[e]>h[e-1]?c+=h[e]-h[e-1]:h[e]<h[e-1]&&(m+=h[e-1]-h[e]);for(g=c+m>0?100*(c-m)/(c+m):0,r.push(a[s]),l.push(g),n.push([a[s],g]),p=s+1;p<i;p++)d=Math.abs(h[p-s-1]-h[p-s]),h[p]>h[p-1]?c+=h[p]-h[p-1]:h[p]<h[p-1]&&(m+=h[p-1]-h[p]),h[p-s]>h[p-s-1]?c-=d:m-=d,g=c+m>0?100*(c-m)/(c+m):0,r.push(a[p]),l.push(g),n.push([a[p],g]);return{values:n,xData:r,yData:l}}}eW.defaultOptions=eG(eN.defaultOptions,{params:{period:20,index:3}}),p().registerSeriesType("cmo",eW);let{ema:ez}=p().seriesTypes,{correctFloat:eY,isArray:eF,merge:eX}=i();class eR extends ez{getEMA(e,t,s,a,o,i){return super.calculateEma(i||[],e,void 0===o?1:o,this.EMApercent,t,void 0===a?-1:a,s)}getValues(e,t){let s=t.period,a=[],o=2*s,i=e.xData,n=e.yData,r=n?n.length:0,l=[],p=[],u=[],h=0,d=0,c,m,g,y,f=-1,x,D=0;if(this.EMApercent=2/(s+1),!(r<2*s-1)){for(eF(n[0])&&(f=t.index?t.index:0),D=(h=super.accumulatePeriodPoints(s,f,n))/s,h=0,y=s;y<r+2;y++)y<r+1&&(d=this.getEMA(n,m,D,f,y)[1],a.push(d)),m=d,y<o?h+=d:(y===o&&(D=h/s),d=a[y-s-1],c=this.getEMA([d],g,D)[1],x=[i[y-2],eY(2*d-c)],l.push(x),p.push(x[0]),u.push(x[1]),g=c);return{values:l,xData:p,yData:u}}}}eR.defaultOptions=eX(ez.defaultOptions),p().registerSeriesType("dema",eR);let{ema:eK}=p().seriesTypes,{correctFloat:eU,isArray:eZ,merge:ej}=i();class e_ extends eK{getEMA(e,t,s,a,o,i){return super.calculateEma(i||[],e,void 0===o?1:o,this.EMApercent,t,void 0===a?-1:a,s)}getTemaPoint(e,t,s,a){return[e[a-3],eU(3*s.level1-3*s.level2+s.level3)]}getValues(e,t){let s=t.period,a=2*s,o=3*s,i=e.xData,n=e.yData,r=n?n.length:0,l=[],p=[],u=[],h=[],d=[],c={},m=-1,g=0,y=0,f,x,D,v;if(this.EMApercent=2/(s+1),!(r<3*s-2)){for(eZ(n[0])&&(m=t.index?t.index:0),y=(g=super.accumulatePeriodPoints(s,m,n))/s,g=0,D=s;D<r+3;D++)D<r+1&&(c.level1=this.getEMA(n,f,y,m,D)[1],h.push(c.level1)),f=c.level1,D<a?g+=c.level1:(D===a&&(y=g/s,g=0),c.level1=h[D-s-1],c.level2=this.getEMA([c.level1],x,y)[1],d.push(c.level2),x=c.level2,D<o?g+=c.level2:(D===o&&(y=g/s),D===r+1&&(c.level1=h[D-s-1],c.level2=this.getEMA([c.level1],x,y)[1],d.push(c.level2)),c.level1=h[D-s-2],c.level2=d[D-2*s-1],c.level3=this.getEMA([c.level2],c.prevLevel3,y)[1],(v=this.getTemaPoint(i,o,c,D))&&(l.push(v),p.push(v[0]),u.push(v[1])),c.prevLevel3=c.level3));return{values:l,xData:p,yData:u}}}}e_.defaultOptions=ej(eK.defaultOptions),p().registerSeriesType("tema",e_);let{tema:eq}=p().seriesTypes,{correctFloat:eH,merge:e$}=i();class eJ extends eq{getTemaPoint(e,t,s,a){if(a>t)return[e[a-3],0!==s.prevLevel3?eH(s.level3-s.prevLevel3)/s.prevLevel3*100:null]}}eJ.defaultOptions=e$(eq.defaultOptions),p().registerSeriesType("trix",eJ);let{ema:eQ}=p().seriesTypes,{extend:e0,merge:e1,error:e2}=i();class e3 extends eQ{getValues(e,t){let s,a,o=t.periods,i=t.index,n=[],r=[],l=[];if(2!==o.length||o[1]<=o[0]){e2('Error: "APO requires two periods. Notice, first period should be lower than the second one."');return}let p=super.getValues.call(this,e,{index:i,period:o[0]}),u=super.getValues.call(this,e,{index:i,period:o[1]});if(!p||!u)return;let h=o[1]-o[0];for(a=0;a<u.yData.length;a++)s=p.yData[a+h]-u.yData[a],n.push([u.xData[a],s]),r.push(u.xData[a]),l.push(s);return{values:n,xData:r,yData:l}}}e3.defaultOptions=e1(eQ.defaultOptions,{params:{period:void 0,periods:[10,20]}}),e0(e3.prototype,{nameBase:"APO",nameComponents:["periods"]}),p().registerSeriesType("apo",e3);let e4=t.default.dataGrouping.approximations;var e5=a.n(e4);let e6=e.default.Color,{parse:e8}=a.n(e6)(),{sma:e9}=p().seriesTypes,{defined:e7,extend:te,isArray:tt,isNumber:ts,getClosestDistance:ta,merge:to,objectEach:ti}=i();function tn(e){return{high:e.reduce(function(e,t){return Math.max(e,t[1])},-1/0),low:e.reduce(function(e,t){return Math.min(e,t[2])},1/0)}}function tr(e){let t=e.indicator;t.points=e.points,t.nextPoints=e.nextPoints,t.color=e.color,t.options=to(e.options.senkouSpan.styles,e.gap),t.graph=e.graph,t.fillGraph=!0,p().seriesTypes.sma.prototype.drawGraph.call(t)}class tl extends e9{constructor(){super(...arguments),this.data=[],this.options={},this.points=[],this.graphCollection=[]}init(){super.init.apply(this,arguments),this.options=to({tenkanLine:{styles:{lineColor:this.color}},kijunLine:{styles:{lineColor:this.color}},chikouLine:{styles:{lineColor:this.color}},senkouSpanA:{styles:{lineColor:this.color,fill:e8(this.color).setOpacity(.5).get()}},senkouSpanB:{styles:{lineColor:this.color,fill:e8(this.color).setOpacity(.5).get()}},senkouSpan:{styles:{fill:e8(this.color).setOpacity(.2).get()}}},this.options)}toYData(e){return[e.tenkanSen,e.kijunSen,e.chikouSpan,e.senkouSpanA,e.senkouSpanB]}translate(){for(let e of(p().seriesTypes.sma.prototype.translate.apply(this),this.points))for(let t of this.pointArrayMap){let s=e[t];ts(s)&&(e["plot"+t]=this.yAxis.toPixels(s,!0),e.plotY=e["plot"+t],e.tooltipPos=[e.plotX,e["plot"+t]],e.isNull=!1)}}drawGraph(){let e=this,t=e.points,s=e.options,a=e.graph,o=e.color,i={options:{gapSize:s.gapSize}},n=e.pointArrayMap.length,r=[[],[],[],[],[],[]],l={tenkanLine:r[0],kijunLine:r[1],chikouLine:r[2],senkouSpanA:r[3],senkouSpanB:r[4],senkouSpan:r[5]},u=[],h=e.options.senkouSpan,d=h.color||h.styles.fill,c=h.negativeColor,m=[[],[]],g=[[],[]],y=t.length,f=0,x,D,v,A,S,b,T,C,P,M,V,L,k;for(e.ikhMap=l;y--;){for(v=0,D=t[y];v<n;v++)e7(D[x=e.pointArrayMap[v]])&&r[v].push({plotX:D.plotX,plotY:D["plot"+x],isNull:!1});if(c&&y!==t.length-1){let e=l.senkouSpanB.length-1,t=function(e,t,s,a){if(e&&t&&s&&a){let o=t.plotX-e.plotX,i=t.plotY-e.plotY,n=a.plotX-s.plotX,r=a.plotY-s.plotY,l=e.plotX-s.plotX,p=e.plotY-s.plotY,u=(-i*l+o*p)/(-n*i+o*r),h=(n*p-r*l)/(-n*i+o*r);if(u>=0&&u<=1&&h>=0&&h<=1)return{plotX:e.plotX+h*o,plotY:e.plotY+h*i}}}(l.senkouSpanA[e-1],l.senkouSpanA[e],l.senkouSpanB[e-1],l.senkouSpanB[e]);if(t){let s={plotX:t.plotX,plotY:t.plotY,isNull:!1,intersectPoint:!0};l.senkouSpanA.splice(e,0,s),l.senkouSpanB.splice(e,0,s),u.push(e)}}}if(ti(l,(t,a)=>{s[a]&&"senkouSpan"!==a&&(e.points=r[f],e.options=to(s[a].styles,i),e.graph=e["graph"+a],e.fillGraph=!1,e.color=o,p().seriesTypes.sma.prototype.drawGraph.call(e),e["graph"+a]=e.graph),f++}),e.graphCollection)for(let t of e.graphCollection)e[t].destroy(),delete e[t];if(e.graphCollection=[],c&&l.senkouSpanA[0]&&l.senkouSpanB[0]){for(u.unshift(0),u.push(l.senkouSpanA.length-1),L=0;L<u.length-1;L++)if(A=u[L],S=u[L+1],b=l.senkouSpanB.slice(A,S+1),T=l.senkouSpanA.slice(A,S+1),Math.floor(b.length/2)>=1){let e=Math.floor(b.length/2);if(b[e].plotY===T[e].plotY){for(k=0,C=0,P=0;k<b.length;k++)C+=b[k].plotY,P+=T[k].plotY;m[V=C>P?0:1]=m[V].concat(b),g[V]=g[V].concat(T)}else m[V=b[e].plotY>T[e].plotY?0:1]=m[V].concat(b),g[V]=g[V].concat(T)}else m[V=b[0].plotY>T[0].plotY?0:1]=m[V].concat(b),g[V]=g[V].concat(T);["graphsenkouSpanColor","graphsenkouSpanNegativeColor"].forEach(function(t,a){m[a].length&&g[a].length&&(M=0===a?d:c,tr({indicator:e,points:m[a],nextPoints:g[a],color:M,options:s,gap:i,graph:e[t]}),e[t]=e.graph,e.graphCollection.push(t))})}else tr({indicator:e,points:l.senkouSpanB,nextPoints:l.senkouSpanA,color:d,options:s,gap:i,graph:e.graphsenkouSpan}),e.graphsenkouSpan=e.graph;delete e.nextPoints,delete e.fillGraph,e.points=t,e.options=s,e.graph=a,e.color=o}getGraphPath(e){let t=[],s,a=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((s=p().seriesTypes.sma.prototype.getGraphPath.call(this,this.nextPoints))&&s.length){s[0][0]="L",t=p().seriesTypes.sma.prototype.getGraphPath.call(this,e),a=s.slice(0,t.length);for(let e=a.length-1;e>=0;e--)t.push(a[e])}}else t=p().seriesTypes.sma.prototype.getGraphPath.apply(this,arguments);return t}getValues(e,t){let s,a,o,i,n,r,l,p,u,h,d=t.period,c=t.periodTenkan,m=t.periodSenkouSpanB,g=e.xData,y=e.yData,f=e.xAxis,x=y&&y.length||0,D=ta(f.series.map(e=>e.getColumn("x"))),v=[],A=[];if(g.length<=d||!tt(y[0])||4!==y[0].length)return;let S=g[0]-d*D;for(n=0;n<d;n++)A.push(S+n*D);for(n=0;n<x;n++)n>=c&&(r=((a=tn(y.slice(n-c,n))).high+a.low)/2),n>=d&&(u=(r+(l=((o=tn(y.slice(n-d,n))).high+o.low)/2))/2),n>=m&&(h=((i=tn(y.slice(n-m,n))).high+i.low)/2),p=y[n][3],s=g[n],void 0===v[n]&&(v[n]=[]),void 0===v[n+d-1]&&(v[n+d-1]=[]),v[n+d-1][0]=r,v[n+d-1][1]=l,v[n+d-1][2]=void 0,void 0===v[n+1]&&(v[n+1]=[]),v[n+1][2]=p,n<=d&&(v[n+d-1][3]=void 0,v[n+d-1][4]=void 0),void 0===v[n+2*d-2]&&(v[n+2*d-2]=[]),v[n+2*d-2][3]=u,v[n+2*d-2][4]=h,A.push(s);for(n=1;n<=d;n++)A.push(s+n*D);return{values:v,xData:A,yData:v}}}tl.defaultOptions=to(e9.defaultOptions,{params:{index:void 0,period:26,periodTenkan:9,periodSenkouSpanB:52},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>TENKAN SEN: {point.tenkanSen:.3f}<br/>KIJUN SEN: {point.kijunSen:.3f}<br/>CHIKOU SPAN: {point.chikouSpan:.3f}<br/>SENKOU SPAN A: {point.senkouSpanA:.3f}<br/>SENKOU SPAN B: {point.senkouSpanB:.3f}<br/>'},tenkanLine:{styles:{lineWidth:1,lineColor:void 0}},kijunLine:{styles:{lineWidth:1,lineColor:void 0}},chikouLine:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanA:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanB:{styles:{lineWidth:1,lineColor:void 0}},senkouSpan:{styles:{fill:"rgba(255, 0, 0, 0.5)"}},dataGrouping:{approximation:"ichimoku-averages"}}),te(tl.prototype,{pointArrayMap:["tenkanSen","kijunSen","chikouSpan","senkouSpanA","senkouSpanB"],pointValKey:"tenkanSen",nameComponents:["periodSenkouSpanB","period","periodTenkan"]}),e5()["ichimoku-averages"]=function(){let e,t=[];return[].forEach.call(arguments,function(s,a){t.push(e5().average(s)),e=!e&&void 0===t[a]}),e?void 0:t},p().registerSeriesType("ikh",tl);let{sma:tp}=p().seriesTypes,{correctFloat:tu,extend:th,merge:td}=i();class tc extends tp{init(){p().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=td({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s,a,o,i,n,r,l,u=t.period,h=t.periodATR,d=t.multiplierATR,c=t.index,m=e.yData,g=m?m.length:0,y=[],f=p().seriesTypes.ema.prototype.getValues(e,{period:u,index:c}),x=p().seriesTypes.atr.prototype.getValues(e,{period:h}),D=[],v=[];if(!(g<u)){for(l=u;l<=g;l++)n=f.values[l-u],r=x.values[l-h],i=n[0],a=tu(n[1]+d*r[1]),o=tu(n[1]-d*r[1]),s=n[1],y.push([i,a,s,o]),D.push(i),v.push([a,s,o]);return{values:y,xData:D,yData:v}}}}tc.defaultOptions=td(tp.defaultOptions,{params:{index:0,period:20,periodATR:10,multiplierATR:2},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Upper Channel: {point.top}<br/>EMA({series.options.params.period}): {point.middle}<br/>Lower Channel: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"},lineWidth:1}),th(tc.prototype,{nameBase:"Keltner Channels",areaLinesNames:["top","bottom"],nameComponents:["period","periodATR","multiplierATR"],linesApiNames:["topLine","bottomLine"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),X.compose(tc),p().registerSeriesType("keltnerchannels",tc);let{ema:tm,sma:tg}=p().seriesTypes,{correctFloat:ty,error:tf,extend:tx,isArray:tD,merge:tv}=i();class tA extends tg{calculateTrend(e,t){return e[t][1]+e[t][2]+e[t][3]>e[t-1][1]+e[t-1][2]+e[t-1][3]?1:-1}isValidData(e){let t=this.chart,s=this.options,a=this.linkedParent,o=tD(e)&&4===e.length,i=this.volumeSeries||(this.volumeSeries=t.get(s.params.volumeSeriesID));return i||tf("Series "+s.params.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,a.chart),!!([a,i].every(function(e){return e&&e.dataTable.rowCount>=s.params.slowAvgPeriod})&&o)}getCM(e,t,s,a,o){return ty(t+(s===a?e:o))}getDM(e,t){return ty(e-t)}getVolumeForce(e){let t=[],s=0,a,o=1,i=0,n=e[0][1]-e[0][2],r=0,l;for(;o<e.length;o++)l=this.calculateTrend(e,o),a=this.getDM(e[o][1],e[o][2]),s=this.getCM(i,a,l,r,n),t.push([this.volumeSeries.getColumn("y")[o]*l*Math.abs(2*(a/s-1))*100]),r=l,i=s,n=a;return t}getEMA(e,t,s,a,o,i,n){return tm.prototype.calculateEma(n||[],e,void 0===i?1:i,a,t,void 0===o?-1:o,s)}getSMA(e,t,s){return tm.prototype.accumulatePeriodPoints(e,t,s)/e}getValues(e,t){let s=[],a=e.xData,o=e.yData,i=[],n=[],r=[],l,p=0,u=0,h,d,c,m=null;if(!this.isValidData(o[0]))return;let g=this.getVolumeForce(o),y=this.getSMA(t.fastAvgPeriod,0,g),f=this.getSMA(t.slowAvgPeriod,0,g),x=2/(t.fastAvgPeriod+1),D=2/(t.slowAvgPeriod+1);for(;p<o.length;p++)p>=t.fastAvgPeriod&&(d=u=this.getEMA(g,d,y,x,0,p,a)[1]),p>=t.slowAvgPeriod&&(c=h=this.getEMA(g,c,f,D,0,p,a)[1],r.push(l=ty(u-h)),r.length>=t.signalPeriod&&(m=r.slice(-t.signalPeriod).reduce((e,t)=>e+t)/t.signalPeriod),s.push([a[p],l,m]),i.push(a[p]),n.push([l,m]));return{values:s,xData:i,yData:n}}}tA.defaultOptions=tv(tg.defaultOptions,{params:{fastAvgPeriod:34,slowAvgPeriod:55,signalPeriod:13,volumeSeriesID:"volume"},signalLine:{styles:{lineWidth:1,lineColor:"#ff0000"}},dataGrouping:{approximation:"averages"},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">Klinger</span>: {point.y}<br/><span style="color: {point.series.options.signalLine.styles.lineColor}">Signal</span>: {point.signal}<br/>'}}),tx(tA.prototype,{areaLinesNames:[],linesApiNames:["signalLine"],nameBase:"Klinger",nameComponents:["fastAvgPeriod","slowAvgPeriod"],pointArrayMap:["y","signal"],parallelArrays:["x","y","signal"],pointValKey:"y"}),X.compose(tA),p().registerSeriesType("klinger",tA);let{noop:tS}=i(),{column:tb,sma:tT}=p().seriesTypes,{extend:tC,correctFloat:tP,defined:tM,merge:tV}=i();class tL extends tT{init(){p().seriesTypes.sma.prototype.init.apply(this,arguments);let e=this.color;this.options&&(tM(this.colorIndex)&&(this.options.signalLine&&this.options.signalLine.styles&&!this.options.signalLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.signalLine.styles.lineColor=this.color),this.options.macdLine&&this.options.macdLine.styles&&!this.options.macdLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.macdLine.styles.lineColor=this.color)),this.macdZones={zones:this.options.macdLine.zones,startIndex:0},this.signalZones={zones:this.macdZones.zones.concat(this.options.signalLine.zones),startIndex:this.macdZones.zones.length}),this.color=e}toYData(e){return[e.y,e.signal,e.MACD]}translate(){let e=this,t=["plotSignal","plotMACD"];i().seriesTypes.column.prototype.translate.apply(e),e.points.forEach(function(s){[s.signal,s.MACD].forEach(function(a,o){null!==a&&(s[t[o]]=e.yAxis.toPixels(a,!0))})})}destroy(){this.graph=null,this.graphmacd=this.graphmacd&&this.graphmacd.destroy(),this.graphsignal=this.graphsignal&&this.graphsignal.destroy(),p().seriesTypes.sma.prototype.destroy.apply(this,arguments)}drawGraph(){let e=this,t=e.points,s=e.options,a=e.zones,o={options:{gapSize:s.gapSize}},i=[[],[]],n,r=t.length;for(;r--;)tM((n=t[r]).plotMACD)&&i[0].push({plotX:n.plotX,plotY:n.plotMACD,isNull:!tM(n.plotMACD)}),tM(n.plotSignal)&&i[1].push({plotX:n.plotX,plotY:n.plotSignal,isNull:!tM(n.plotMACD)});["macd","signal"].forEach((t,a)=>{e.points=i[a],e.options=tV(s[`${t}Line`]?.styles||{},o),e.graph=e[`graph${t}`],e.zones=(e[`${t}Zones`].zones||[]).slice(e[`${t}Zones`].startIndex||0),p().seriesTypes.sma.prototype.drawGraph.call(e),e[`graph${t}`]=e.graph}),e.points=t,e.options=s,e.zones=a}applyZones(){let e=this.zones;this.zones=this.signalZones.zones,p().seriesTypes.sma.prototype.applyZones.call(this),this.graphmacd&&this.options.macdLine.zones.length&&this.graphmacd.hide(),this.zones=e}getValues(e,t){let s=t.longPeriod-t.shortPeriod,a=[],o=[],i=[],n,r,l,u=0,h=[];if(!(e.xData.length<t.longPeriod+t.signalPeriod)){for(l=0,n=p().seriesTypes.ema.prototype.getValues(e,{period:t.shortPeriod,index:t.index}),r=p().seriesTypes.ema.prototype.getValues(e,{period:t.longPeriod,index:t.index}),n=n.values,r=r.values;l<=n.length;l++)tM(r[l])&&tM(r[l][1])&&tM(n[l+s])&&tM(n[l+s][0])&&a.push([n[l+s][0],0,null,n[l+s][1]-r[l][1]]);for(l=0;l<a.length;l++)o.push(a[l][0]),i.push([0,null,a[l][3]]);for(l=0,h=(h=p().seriesTypes.ema.prototype.getValues({xData:o,yData:i},{period:t.signalPeriod,index:2})).values;l<a.length;l++)a[l][0]>=h[0][0]&&(a[l][2]=h[u][1],i[l]=[0,h[u][1],a[l][3]],null===a[l][3]?(a[l][1]=0,i[l][0]=0):(a[l][1]=tP(a[l][3]-h[u][1]),i[l][0]=tP(a[l][3]-h[u][1])),u++);return{values:a,xData:o,yData:i}}}}tL.defaultOptions=tV(tT.defaultOptions,{params:{shortPeriod:12,longPeriod:26,signalPeriod:9,period:26},signalLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},macdLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},threshold:0,groupPadding:.1,pointPadding:.1,crisp:!1,states:{hover:{halo:{size:0}}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>Value: {point.MACD}<br/>Signal: {point.signal}<br/>Histogram: {point.y}<br/>'},dataGrouping:{approximation:"averages"},minPointLength:0}),tC(tL.prototype,{nameComponents:["longPeriod","shortPeriod","signalPeriod"],pointArrayMap:["y","signal","MACD"],parallelArrays:["x","y","signal","MACD"],pointValKey:"y",markerAttribs:tS,getColumnMetrics:i().seriesTypes.column.prototype.getColumnMetrics,crispCol:i().seriesTypes.column.prototype.crispCol,drawPoints:i().seriesTypes.column.prototype.drawPoints}),p().registerSeriesType("macd",tL);let{sma:tk}=p().seriesTypes,{extend:tO,merge:tE,error:tw,isArray:tI}=i();function tN(e){return e.reduce(function(e,t){return e+t})}function tB(e){return(e[1]+e[2]+e[3])/3}class tG extends tk{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=t.decimals,r=e.chart.get(t.volumeSeriesID),l=r?.getColumn("y")||[],p=[],u=[],h=[],d=[],c=[],m,g,y,f,x,D,v=!1,A=1;if(!r){tw("Series "+t.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,e.chart);return}if(!(a.length<=s)&&tI(o[0])&&4===o[0].length&&l){for(m=tB(o[A]);A<s+1;)g=m,v=(m=tB(o[A]))>=g,y=m*l[A],d.push(v?y:0),c.push(v?0:y),A++;for(D=A-1;D<i;D++)D>A-1&&(d.shift(),c.shift(),g=m,v=(m=tB(o[D]))>g,y=m*l[D],d.push(v?y:0),c.push(v?0:y)),f=tN(c),x=parseFloat((100-100/(1+tN(d)/f)).toFixed(n)),p.push([a[D],x]),u.push(a[D]),h.push(x);return{values:p,xData:u,yData:h}}}}tG.defaultOptions=tE(tk.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",decimals:4}}),tO(tG.prototype,{nameBase:"Money Flow Index"}),p().registerSeriesType("mfi",tG);let{sma:tW}=p().seriesTypes,{extend:tz,isArray:tY,merge:tF}=i();function tX(e,t,s,a,o){let i=t[s-1][o]-t[s-a-1][o];return[e[s-1],i]}class tR extends tW{getValues(e,t){let s,a,o=t.period,i=t.index,n=e.xData,r=e.yData,l=r?r.length:0,p=[],u=[],h=[];if(!(n.length<=o)&&tY(r[0])){for(s=o+1;s<l;s++)a=tX(n,r,s,o,i),p.push(a),u.push(a[0]),h.push(a[1]);return a=tX(n,r,s,o,i),p.push(a),u.push(a[0]),h.push(a[1]),{values:p,xData:u,yData:h}}}}tR.defaultOptions=tF(tW.defaultOptions,{params:{index:3}}),tz(tR.prototype,{nameBase:"Momentum"}),p().registerSeriesType("momentum",tR);let{atr:tK}=p().seriesTypes,{merge:tU}=i();class tZ extends tK{getValues(e,t){let s=super.getValues.apply(this,arguments),a=s.values.length,o=e.yData,i=0,n=t.period-1;if(s){for(;i<a;i++)s.yData[i]=s.values[i][1]/o[n][3]*100,s.values[i][1]=s.yData[i],n++;return s}}}tZ.defaultOptions=tU(tK.defaultOptions,{tooltip:{valueSuffix:"%"}}),p().registerSeriesType("natr",tZ);let{sma:tj}=p().seriesTypes,{isNumber:t_,error:tq,extend:tH,merge:t$}=i();class tJ extends tj{getValues(e,t){let s=e.chart.get(t.volumeSeriesID),a=e.xData,o=e.yData,i=[],n=[],r=[],l=!t_(o[0]),p=[],u=1,h=0,d=0,c=0,m=0,g;if(s)for(g=s.getColumn("y"),p=[a[0],h],c=l?o[0][3]:o[0],i.push(p),n.push(a[0]),r.push(p[1]);u<o.length;u++)d=(m=l?o[u][3]:o[u])>c?h+g[u]:m===c?h:h-g[u],p=[a[u],d],h=d,c=m,i.push(p),n.push(a[u]),r.push(p[1]);else{tq("Series "+t.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,e.chart);return}return{values:i,xData:n,yData:r}}}tJ.defaultOptions=t$(tj.defaultOptions,{marker:{enabled:!1},params:{index:void 0,period:void 0,volumeSeriesID:"volume"},tooltip:{valueDecimals:0}}),tH(tJ.prototype,{nameComponents:void 0}),p().registerSeriesType("obv",tJ);let tQ=p().seriesTypes.sma.prototype.pointClass;function t0(e,t){let s=e.series.pointArrayMap,a,o=s.length;for(p().seriesTypes.sma.prototype.pointClass.prototype[t].call(e);o--;)e[a="dataLabel"+s[o]]&&e[a].element&&e[a].destroy(),e[a]=null}let{sma:t1}=p().seriesTypes,{merge:t2,extend:t3,defined:t4,isArray:t5}=i();class t6 extends t1{toYData(e){return[e.P]}translate(){let e=this;super.translate.apply(e),e.points.forEach(function(t){e.pointArrayMap.forEach(function(s){t4(t[s])&&(t["plot"+s]=e.yAxis.toPixels(t[s],!0))})}),e.plotEndPoint=e.xAxis.toPixels(e.endPoint,!0)}getGraphPath(e){let t=this,s=[[],[],[],[],[],[],[],[],[]],a=t.pointArrayMap.length,o=t.plotEndPoint,i=[],n,r,l=e.length,p;for(;l--;){for(p=0,r=e[l];p<a;p++)t4(r[n=t.pointArrayMap[p]])&&s[p].push({plotX:r.plotX,plotY:r["plot"+n],isNull:!1},{plotX:o,plotY:r["plot"+n],isNull:!1},{plotX:o,plotY:null,isNull:!0});o=r.plotX}return s.forEach(e=>{i=i.concat(super.getGraphPath.call(t,e))}),i}drawDataLabels(){let e,t,s,a,o=this,i=o.pointArrayMap;o.options.dataLabels.enabled&&(t=o.points.length,i.concat([!1]).forEach((n,r)=>{for(a=t;a--;)s=o.points[a],n?(s.y=s[n],s.pivotLine=n,s.plotY=s["plot"+n],e=s["dataLabel"+n],r&&(s["dataLabel"+i[r-1]]=s.dataLabel),s.dataLabels||(s.dataLabels=[]),s.dataLabels[0]=s.dataLabel=e=e&&e.element?e:null):s["dataLabel"+i[r-1]]=s.dataLabel;super.drawDataLabels.call(o)}))}getValues(e,t){let s,a,o,i,n,r,l,p=t.period,u=e.xData,h=e.yData,d=h?h.length:0,c=this[t.algorithm+"Placement"],m=[],g=[],y=[];if(!(u.length<p)&&t5(h[0])&&4===h[0].length){for(l=p+1;l<=d+p;l+=p)o=u.slice(l-p-1,l),i=h.slice(l-p-1,l),a=o.length,s=o[a-1],r=c(this.getPivotAndHLC(i)),n=m.push([s].concat(r)),g.push(s),y.push(m[n-1].slice(1));return this.endPoint=o[0]+(s-o[0])/a*p,{values:m,xData:g,yData:y}}}getPivotAndHLC(e){let t=e[e.length-1][3],s=-1/0,a=1/0;return e.forEach(function(e){s=Math.max(s,e[1]),a=Math.min(a,e[2])}),[(s+a+t)/3,s,a,t]}standardPlacement(e){let t=e[1]-e[2];return[null,null,e[0]+t,2*e[0]-e[2],e[0],2*e[0]-e[1],e[0]-t,null,null]}camarillaPlacement(e){let t=e[1]-e[2];return[e[3]+1.5*t,e[3]+1.25*t,e[3]+1.1666*t,e[3]+1.0833*t,e[0],e[3]-1.0833*t,e[3]-1.1666*t,e[3]-1.25*t,e[3]-1.5*t]}fibonacciPlacement(e){let t=e[1]-e[2];return[null,e[0]+t,e[0]+.618*t,e[0]+.382*t,e[0],e[0]-.382*t,e[0]-.618*t,e[0]-t,null]}}t6.defaultOptions=t2(t1.defaultOptions,{params:{index:void 0,period:28,algorithm:"standard"},marker:{enabled:!1},enableMouseTracking:!1,dataLabels:{enabled:!0,format:"{point.pivotLine}"},dataGrouping:{approximation:"averages"}}),t3(t6.prototype,{nameBase:"Pivot Points",pointArrayMap:["R4","R3","R2","R1","P","S1","S2","S3","S4"],pointValKey:"P",pointClass:class extends tQ{destroyElements(){t0(this,"destroyElements")}destroy(){t0(this,"destroyElements")}}}),p().registerSeriesType("pivotpoints",t6);let{ema:t8}=p().seriesTypes,{correctFloat:t9,extend:t7,merge:se,error:st}=i();class ss extends t8{getValues(e,t){let s,a,o=t.periods,i=t.index,n=[],r=[],l=[];if(2!==o.length||o[1]<=o[0]){st('Error: "PPO requires two periods. Notice, first period should be lower than the second one."');return}let p=super.getValues.call(this,e,{index:i,period:o[0]}),u=super.getValues.call(this,e,{index:i,period:o[1]});if(!p||!u)return;let h=o[1]-o[0];for(a=0;a<u.yData.length;a++)s=t9((p.yData[a+h]-u.yData[a])/u.yData[a]*100),n.push([u.xData[a],s]),r.push(u.xData[a]),l.push(s);return{values:n,xData:r,yData:l}}}ss.defaultOptions=se(t8.defaultOptions,{params:{period:void 0,periods:[12,26]}}),t7(ss.prototype,{nameBase:"PPO",nameComponents:["periods"]}),p().registerSeriesType("ppo",ss);let sa={getArrayExtremes:function(e,t,s){return e.reduce((e,a)=>[Math.min(e[0],a[t]),Math.max(e[1],a[s])],[Number.MAX_VALUE,-Number.MAX_VALUE])}},so=["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],{sma:si}=p().seriesTypes,{merge:sn,extend:sr}=i();class sl extends si{getValues(e,t){let s,a,o,i,n,r,l,p=t.period,u=e.xData,h=e.yData,d=h?h.length:0,c=[],m=[],g=[];if(!(d<p)){for(l=p;l<=d;l++)i=u[l-1],n=h.slice(l-p,l),s=((a=(r=sa.getArrayExtremes(n,2,1))[1])+(o=r[0]))/2,c.push([i,a,s,o]),m.push(i),g.push([a,s,o]);return{values:c,xData:m,yData:g}}}}sl.defaultOptions=sn(si.defaultOptions,{params:{index:void 0,period:20},lineWidth:1,topLine:{styles:{lineColor:"#00e272",lineWidth:1}},bottomLine:{styles:{lineColor:"#feb56a",lineWidth:1}},dataGrouping:{approximation:"averages"}}),sr(sl.prototype,{areaLinesNames:["top","bottom"],nameBase:"Price Channel",nameComponents:["period"],linesApiNames:["topLine","bottomLine"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),X.compose(sl),p().registerSeriesType("pc",sl);let{sma:sp}=p().seriesTypes,{extend:su,isArray:sh,merge:sd}=i();class sc extends sp{init(){super.init.apply(this,arguments),this.options=sd({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s,a,o,i,n,r,l,p,u=t.period,h=t.topBand,d=t.bottomBand,c=e.xData,m=e.yData,g=m?m.length:0,y=[],f=[],x=[];if(!(c.length<u)&&sh(m[0])&&4===m[0].length){for(p=u;p<=g;p++)n=c.slice(p-u,p),r=m.slice(p-u,p),i=(l=super.getValues({xData:n,yData:r},t)).xData[0],a=(s=l.yData[0])*(1+h),o=s*(1-d),y.push([i,a,s,o]),f.push(i),x.push([a,s,o]);return{values:y,xData:f,yData:x}}}}sc.defaultOptions=sd(sp.defaultOptions,{marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},params:{period:20,topBand:.1,bottomBand:.1},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),su(sc.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","topBand","bottomBand"],nameBase:"Price envelopes",pointArrayMap:["top","middle","bottom"],parallelArrays:["x","y","top","bottom"],pointValKey:"middle"}),X.compose(sc),p().registerSeriesType("priceenvelopes",sc);let{sma:sm}=p().seriesTypes,{merge:sg}=i();function sy(e,t){return parseFloat(e.toFixed(t))}class sf extends sm{constructor(){super(...arguments),this.nameComponents=void 0}getValues(e,t){let s=e.xData,a=e.yData,o=t.maxAccelerationFactor,i=t.increment,n=t.initialAccelerationFactor,r=t.decimals,l=t.index,p=[],u=[],h=[],d=t.initialAccelerationFactor,c,m=a[0][1],g,y,f,x=1,D,v,A,S,b=a[0][2],T,C,P,M;if(!(l>=a.length)){for(M=0;M<l;M++)m=Math.max(a[M][1],m),b=Math.min(a[M][2],sy(b,r));for(c=a[M][1]>b?1:-1,g=m-b,y=(d=t.initialAccelerationFactor)*g,p.push([s[l],b]),u.push(s[l]),h.push(sy(b,r)),M=l+1;M<a.length;M++)if(D=a[M-1][2],v=a[M-2][2],A=a[M-1][1],S=a[M-2][1],C=a[M][1],P=a[M][2],null!==v&&null!==S&&null!==D&&null!==A&&null!==C&&null!==P){var V,L,k,O,E,w,I,N,B,G,W,z,Y,F,X,R,K,U,Z,j,_;E=c,w=x,I=b,N=y,B=v,G=D,W=A,z=S,Y=m,b=E===w?1===E?I+N<Math.min(B,G)?I+N:Math.min(B,G):I+N>Math.max(z,W)?I+N:Math.max(z,W):Y,V=c,L=m,T=1===V?C>L?C:L:P<L?P:L,k=x,O=b,F=f=1===k&&P>O||-1===k&&C>O?1:-1,X=c,R=T,K=m,U=d,Z=i,j=o,_=n,y=(d=F===X?1===F&&R>K||-1===F&&R<K?U===j?j:sy(U+Z,2):U:_)*(g=T-b),p.push([s[M],sy(b,r)]),u.push(s[M]),h.push(sy(b,r)),x=c,c=f,m=T}return{values:p,xData:u,yData:h}}}}sf.defaultOptions=sg(sm.defaultOptions,{lineWidth:0,marker:{enabled:!0},states:{hover:{lineWidthPlus:0}},params:{period:void 0,initialAccelerationFactor:.02,maxAccelerationFactor:.2,increment:.02,index:2,decimals:4}}),p().registerSeriesType("psar",sf);let{sma:sx}=p().seriesTypes,{isArray:sD,merge:sv,extend:sA}=i();class sS extends sx{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=[],r=[],l=[],p,u=-1,h;if(!(a.length<=s)){for(sD(o[0])&&(u=t.index),p=s;p<i;p++)h=function(e,t,s,a,o){let i,n;return n=o<0?(i=t[s-a])?(t[s]-i)/i*100:null:(i=t[s-a][o])?(t[s][o]-i)/i*100:null,[e[s],n]}(a,o,p,s,u),n.push(h),r.push(h[0]),l.push(h[1]);return{values:n,xData:r,yData:l}}}}sS.defaultOptions=sv(sx.defaultOptions,{params:{index:3,period:9}}),sA(sS.prototype,{nameBase:"Rate of Change"}),p().registerSeriesType("roc",sS);let{sma:sb}=p().seriesTypes,{isNumber:sT,merge:sC}=i();function sP(e,t){return parseFloat(e.toFixed(t))}class sM extends sb{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=t.decimals,r=[],l=[],p=[],u=0,h=0,d=t.index,c=1,m,g,y,f,x,D;if(!(a.length<s)){for(sT(o[0])?D=o:(d=Math.min(d,o[0].length-1),D=o.map(e=>e[d]));c<s;)(g=sP(D[c]-D[c-1],n))>0?u+=g:h+=Math.abs(g),c++;for(y=sP(u/(s-1),n),f=sP(h/(s-1),n),x=c;x<i;x++)(g=sP(D[x]-D[x-1],n))>0?(u=g,h=0):(u=0,h=Math.abs(g)),y=sP((y*(s-1)+u)/s,n),m=0===(f=sP((f*(s-1)+h)/s,n))?100:0===y?0:sP(100-100/(1+y/f),n),r.push([a[x],m]),l.push(a[x]),p.push(m);return{values:r,xData:l,yData:p}}}}sM.defaultOptions=sC(sb.defaultOptions,{params:{decimals:4,index:3}}),p().registerSeriesType("rsi",sM);let{sma:sV}=p().seriesTypes,{extend:sL,isArray:sk,merge:sO}=i();class sE extends sV{init(){super.init.apply(this,arguments),this.options=sO({smoothedLine:{styles:{lineColor:this.color}}},this.options)}getValues(e,t){let s=t.periods[0],a=t.periods[1],o=e.xData,i=e.yData,n=i?i.length:0,r=[],l=[],p=[],u,h,d,c=null,m,g;if(n<s||!sk(i[0])||4!==i[0].length)return;let y=!0,f=0;for(g=s-1;g<n;g++){if(u=i.slice(g-s+1,g+1),h=(m=sa.getArrayExtremes(u,2,1))[0],isNaN(d=(i[g][3]-h)/(m[1]-h)*100)&&y){f++;continue}y&&!isNaN(d)&&(y=!1);let e=l.push(o[g]);isNaN(d)?p.push([p[e-2]&&"number"==typeof p[e-2][0]?p[e-2][0]:null,null]):p.push([d,null]),g>=f+(s-1)+(a-1)&&(c=super.getValues({xData:l.slice(-a),yData:p.slice(-a)},{period:a}).yData[0]),r.push([o[g],d,c]),p[e-1][1]=c}return{values:r,xData:l,yData:p}}}sE.defaultOptions=sO(sV.defaultOptions,{params:{index:void 0,period:void 0,periods:[14,3]},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>%K: {point.y}<br/>%D: {point.smoothed}<br/>'},smoothedLine:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),sL(sE.prototype,{areaLinesNames:[],nameComponents:["periods"],nameBase:"Stochastic",pointArrayMap:["y","smoothed"],parallelArrays:["x","y","smoothed"],pointValKey:"y",linesApiNames:["smoothedLine"]}),X.compose(sE),p().registerSeriesType("stochastic",sE);let{sma:sw,stochastic:sI}=p().seriesTypes,{extend:sN,merge:sB}=i();class sG extends sI{getValues(e,t){let s=t.periods,a=super.getValues.call(this,e,t),o={values:[],xData:[],yData:[]};if(!a)return;o.xData=a.xData.slice(s[1]-1);let i=a.yData.slice(s[1]-1),n=sw.prototype.getValues.call(this,{xData:o.xData,yData:i},{index:1,period:s[2]});if(n){for(let e=0,t=o.xData.length;e<t;e++)o.yData[e]=[i[e][1],n.yData[e-s[2]+1]||null],o.values[e]=[o.xData[e],i[e][1],n.yData[e-s[2]+1]||null];return o}}}sG.defaultOptions=sB(sI.defaultOptions,{params:{periods:[14,3,3]}}),sN(sG.prototype,{nameBase:"Slow Stochastic"}),p().registerSeriesType("slowstochastic",sG);let{atr:sW,sma:sz}=p().seriesTypes,{addEvent:sY,correctFloat:sF,isArray:sX,isNumber:sR,extend:sK,merge:sU,objectEach:sZ}=i();function sj(e,t){return{index:t,close:e.getColumn("close")[t],x:e.getColumn("x")[t]}}class s_ extends sz{init(){let e=this;super.init.apply(e,arguments);let t=sY(this.chart.constructor,"afterLinkSeries",()=>{if(e.options){let t=e.options;t.cropThreshold=e.linkedParent.options.cropThreshold-(t.params.period-1)}t()},{order:1})}drawGraph(){let e=this,t=e.options,s=e.linkedParent,a=s.getColumn("x"),o=s?s.points:[],i=e.points,n=e.graph,r=o.length-i.length,l=r>0?r:0,p={options:{gapSize:t.gapSize}},u={top:[],bottom:[],intersect:[]},h={top:{styles:{lineWidth:t.lineWidth,lineColor:t.fallingTrendColor||t.color,dashStyle:t.dashStyle}},bottom:{styles:{lineWidth:t.lineWidth,lineColor:t.risingTrendColor||t.color,dashStyle:t.dashStyle}},intersect:t.changeTrendLine},d,c,m,g,y,f,x,D,v,A=i.length;for(;A--;)d=i[A],c=i[A-1],m=o[A-1+l],g=o[A-2+l],y=o[A+l],f=o[A+l+1],x=d.options.color,D={x:d.x,plotX:d.plotX,plotY:d.plotY,isNull:!1},!g&&m&&sR(a[m.index-1])&&(g=sj(s,m.index-1)),!f&&y&&sR(a[y.index+1])&&(f=sj(s,y.index+1)),!m&&g&&sR(a[g.index+1])?m=sj(s,g.index+1):!m&&y&&sR(a[y.index-1])&&(m=sj(s,y.index-1)),d&&m&&y&&g&&d.x!==m.x&&(d.x===y.x?(g=m,m=y):d.x===g.x?(m=g,g={close:s.getColumn("close")[m.index-1],x:a[m.index-1]}):f&&d.x===f.x&&(m=f,g=y)),c&&g&&m?(v={x:c.x,plotX:c.plotX,plotY:c.plotY,isNull:!1},d.y>=m.close&&c.y>=g.close?(d.color=x||t.fallingTrendColor||t.color,u.top.push(D)):d.y<m.close&&c.y<g.close?(d.color=x||t.risingTrendColor||t.color,u.bottom.push(D)):(u.intersect.push(D),u.intersect.push(v),u.intersect.push(sU(v,{isNull:!0})),d.y>=m.close&&c.y<g.close?(d.color=x||t.fallingTrendColor||t.color,c.color=x||t.risingTrendColor||t.color,u.top.push(D),u.top.push(sU(v,{isNull:!0}))):d.y<m.close&&c.y>=g.close&&(d.color=x||t.risingTrendColor||t.color,c.color=x||t.fallingTrendColor||t.color,u.bottom.push(D),u.bottom.push(sU(v,{isNull:!0}))))):m&&(d.y>=m.close?(d.color=x||t.fallingTrendColor||t.color,u.top.push(D)):(d.color=x||t.risingTrendColor||t.color,u.bottom.push(D)));sZ(u,function(t,s){e.points=t,e.options=sU(h[s].styles,p),e.graph=e["graph"+s+"Line"],sz.prototype.drawGraph.call(e),e["graph"+s+"Line"]=e.graph}),e.points=i,e.options=t,e.graph=n}getValues(e,t){let s=t.period,a=t.multiplier,o=e.xData,i=e.yData,n=[],r=[],l=[],p=0===s?0:s-1,u=[],h=[],d=[],c,m,g,y,f,x,D,v,A;if(!(o.length<=s)&&sX(i[0])&&4===i[0].length&&!(s<0)){for(A=0,d=sW.prototype.getValues.call(this,e,{period:s}).yData;A<d.length;A++)v=i[p+A],D=i[p+A-1]||[],y=u[A-1],f=h[A-1],x=l[A-1],0===A&&(y=f=x=0),c=sF((v[1]+v[2])/2+a*d[A]),m=sF((v[1]+v[2])/2-a*d[A]),c<y||D[3]>y?u[A]=c:u[A]=y,m>f||D[3]<f?h[A]=m:h[A]=f,x===y&&v[3]<u[A]||x===f&&v[3]<h[A]?g=u[A]:(x===y&&v[3]>u[A]||x===f&&v[3]>h[A])&&(g=h[A]),n.push([o[p+A],g]),r.push(o[p+A]),l.push(g);return{values:n,xData:r,yData:l}}}}s_.defaultOptions=sU(sz.defaultOptions,{params:{index:void 0,multiplier:3,period:10},risingTrendColor:"#06b535",fallingTrendColor:"#f21313",changeTrendLine:{styles:{lineWidth:1,lineColor:"#333333",dashStyle:"LongDash"}}}),sK(s_.prototype,{nameBase:"Supertrend",nameComponents:["multiplier","period"]}),p().registerSeriesType("supertrend",s_);let{sma:{prototype:{pointClass:sq}}}=p().seriesTypes,sH=class extends sq{destroy(){this.negativeGraphic&&(this.negativeGraphic=this.negativeGraphic.destroy()),super.destroy.apply(this,arguments)}},{animObject:s$}=i(),{noop:sJ}=i(),{column:{prototype:sQ},sma:s0}=p().seriesTypes,{addEvent:s1,arrayMax:s2,arrayMin:s3,correctFloat:s4,defined:s5,error:s6,extend:s8,isArray:s9,merge:s7}=i(),ae=Math.abs;class at extends s0{init(e,t){let s=this;delete t.data,super.init.apply(s,arguments);let a=s1(this.chart.constructor,"afterLinkSeries",function(){if(s.options){let t=s.options.params,a=s.linkedParent,o=e.get(t.volumeSeriesID);s.addCustomEvents(a,o)}a()},{order:1});return s}addCustomEvents(e,t){let s=this,a=()=>{s.chart.redraw(),s.setData([]),s.zoneStarts=[],s.zoneLinesSVG&&(s.zoneLinesSVG=s.zoneLinesSVG.destroy())};return s.dataEventsToUnbind.push(s1(e,"remove",function(){a()})),t&&s.dataEventsToUnbind.push(s1(t,"remove",function(){a()})),s}animate(e){let t=this,s=t.chart.inverted,a=t.group,o={};if(!e&&a){let e=s?t.yAxis.top:t.xAxis.left;s?(a["forceAnimate:translateY"]=!0,o.translateY=e):(a["forceAnimate:translateX"]=!0,o.translateX=e),a.animate(o,s8(s$(t.options.animation),{step:function(e,s){t.group.attr({scaleX:Math.max(.001,s.pos)})}}))}}drawPoints(){this.options.volumeDivision.enabled&&(this.posNegVolume(!0,!0),sQ.drawPoints.apply(this,arguments),this.posNegVolume(!1,!1)),sQ.drawPoints.apply(this,arguments)}posNegVolume(e,t){let s=t?["positive","negative"]:["negative","positive"],a=this.options.volumeDivision,o=this.points.length,i=[],n=[],r=0,l,p,u,h;for(e?(this.posWidths=i,this.negWidths=n):(i=this.posWidths,n=this.negWidths);r<o;r++)(h=this.points[r])[s[0]+"Graphic"]=h.graphic,h.graphic=h[s[1]+"Graphic"],e&&(l=h.shapeArgs.width,(u=(p=this.priceZones[r]).wholeVolumeData)?(i.push(l/u*p.positiveVolumeData),n.push(l/u*p.negativeVolumeData)):(i.push(0),n.push(0))),h.color=t?a.styles.positiveColor:a.styles.negativeColor,h.shapeArgs.width=t?this.posWidths[r]:this.negWidths[r],h.shapeArgs.x=t?h.shapeArgs.x:this.posWidths[r]}translate(){let e=this,t=e.options,s=e.chart,a=e.yAxis,o=a.min,i=e.options.zoneLines,n=e.priceZones,r=0,l,p,u,h,d,c,m,g,y,f;sQ.translate.apply(e);let x=e.points;x.length&&(m=t.pointPadding<.5?t.pointPadding:.1,l=s2(e.volumeDataArray),p=s.plotWidth/2,g=s.plotTop,u=ae(a.toPixels(o)-a.toPixels(o+e.rangeStep)),d=ae(a.toPixels(o)-a.toPixels(o+e.rangeStep)),m&&(h=ae(u*(1-2*m)),r=ae((u-h)/2),u=ae(h)),x.forEach(function(t,s){y=t.barX=t.plotX=0,f=t.plotY=a.toPixels(n[s].start)-g-(a.reversed?u-d:u)-r,t.pointWidth=c=s4(p*n[s].wholeVolumeData/l),t.shapeArgs=e.crispCol.apply(e,[y,f,c,u]),t.volumeNeg=n[s].negativeVolumeData,t.volumePos=n[s].positiveVolumeData,t.volumeAll=n[s].wholeVolumeData}),i.enabled&&e.drawZones(s,a,e.zoneStarts,i.styles))}getExtremes(){let e,t=this.options.compare,s=this.options.cumulative;return this.options.compare?(this.options.compare=void 0,e=super.getExtremes(),this.options.compare=t):this.options.cumulative?(this.options.cumulative=!1,e=super.getExtremes(),this.options.cumulative=s):e=super.getExtremes(),e}getValues(e,t){let s=e.getColumn("x",!0),a=e.processedYData,o=this.chart,i=t.ranges,n=[],r=[],l=[],p=o.get(t.volumeSeriesID);if(!e.chart){s6("Base series not found! In case it has been removed, add a new one.",!0,o);return}if(!p||!p.getColumn("x",!0).length){let e=p&&!p.getColumn("x",!0).length?" does not contain any data.":" not found! Check `volumeSeriesID`.";s6("Series "+t.volumeSeriesID+e,!0,o);return}let u=s9(a[0]);if(u&&4!==a[0].length){s6("Type of "+e.name+" series is different than line, OHLC or candlestick.",!0,o);return}return(this.priceZones=this.specifyZones(u,s,a,i,p)).forEach(function(e,t){n.push([e.x,e.end]),r.push(n[t][0]),l.push(n[t][1])}),{values:n,xData:r,yData:l}}specifyZones(e,t,s,a,o){let i=!!e&&function(e){let t=e.length,s=e[0][3],a=s,o=1,i;for(;o<t;o++)(i=e[o][3])<s&&(s=i),i>a&&(a=i);return{min:s,max:a}}(s),n=this.zoneStarts=[],r=[],l=i?i.min:s3(s),p=i?i.max:s2(s),u=0,h=1,d=this.linkedParent;if(!this.options.compareToMain&&d.dataModify&&(l=d.dataModify.modifyValue(l),p=d.dataModify.modifyValue(p)),!s5(l)||!s5(p))return this.points.length&&(this.setData([]),this.zoneStarts=[],this.zoneLinesSVG&&(this.zoneLinesSVG=this.zoneLinesSVG.destroy())),[];let c=this.rangeStep=s4(p-l)/a;for(n.push(l);u<a-1;u++)n.push(s4(n[u]+c));n.push(p);let m=n.length;for(;h<m;h++)r.push({index:h-1,x:t[0],start:n[h-1],end:n[h]});return this.volumePerZone(e,r,o,t,s)}volumePerZone(e,t,s,a,o){let i,n,r,l,p,u=this,h=s.getColumn("x",!0),d=s.getColumn("y",!0),c=t.length-1,m=o.length,g=d.length;return ae(m-g)&&(a[0]!==h[0]&&d.unshift(0),a[m-1]!==h[g-1]&&d.push(0)),u.volumeDataArray=[],t.forEach(function(t){for(p=0,t.wholeVolumeData=0,t.positiveVolumeData=0,t.negativeVolumeData=0;p<m;p++){n=!1,r=!1,l=e?o[p][3]:o[p],i=p?e?o[p-1][3]:o[p-1]:l;let s=u.linkedParent;!u.options.compareToMain&&s.dataModify&&(l=s.dataModify.modifyValue(l),i=s.dataModify.modifyValue(i)),l<=t.start&&0===t.index&&(n=!0),l>=t.end&&t.index===c&&(r=!0),(l>t.start||n)&&(l<t.end||r)&&(t.wholeVolumeData+=d[p],i>l?t.negativeVolumeData+=d[p]:t.positiveVolumeData+=d[p])}u.volumeDataArray.push(t.wholeVolumeData)}),t}drawZones(e,t,s,a){let o=e.renderer,i=e.plotWidth,n=e.plotTop,r=this.zoneLinesSVG,l=[],p;s.forEach(function(s){p=t.toPixels(s)-n,l=l.concat(e.renderer.crispLine([["M",0,p],["L",i,p]],a.lineWidth))}),r?r.animate({d:l}):r=this.zoneLinesSVG=o.path(l).attr({"stroke-width":a.lineWidth,stroke:a.color,dashstyle:a.dashStyle,zIndex:this.group.zIndex+.1}).add(this.group)}}at.defaultOptions=s7(s0.defaultOptions,{params:{index:void 0,period:void 0,ranges:12,volumeSeriesID:"volume"},zoneLines:{enabled:!0,styles:{color:"#0A9AC9",dashStyle:"LongDash",lineWidth:1}},volumeDivision:{enabled:!0,styles:{positiveColor:"rgba(144, 237, 125, 0.8)",negativeColor:"rgba(244, 91, 91, 0.8)"}},animationLimit:1e3,enableMouseTracking:!1,pointPadding:0,zIndex:-1,crisp:!0,dataGrouping:{enabled:!1},dataLabels:{align:"left",allowOverlap:!0,enabled:!0,format:"P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}",padding:0,style:{fontSize:"0.5em"},verticalAlign:"top"}}),s8(at.prototype,{nameBase:"Volume by Price",nameComponents:["ranges"],calculateOn:{chart:"render",xAxis:"afterSetExtremes"},pointClass:sH,markerAttribs:sJ,drawGraph:sJ,getColumnMetrics:sQ.getColumnMetrics,crispCol:sQ.crispCol}),p().registerSeriesType("vbp",at);let{sma:as}=p().seriesTypes,{error:aa,isArray:ao,merge:ai}=i();class an extends as{getValues(e,t){let s=e.chart,a=e.xData,o=e.yData,i=t.period,n=!0,r;if(!(r=s.get(t.volumeSeriesID))){aa("Series "+t.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,s);return}return ao(o[0])||(n=!1),this.calculateVWAPValues(n,a,o,r,i)}calculateVWAPValues(e,t,s,a,o){let i,n,r,l,p,u,h=a.getColumn("y"),d=h.length,c=t.length,m=[],g=[],y=[],f=[],x=[];for(p=0,i=c<=d?c:d,u=0;p<i;p++)n=(e?(s[p][1]+s[p][2]+s[p][3])/3:s[p])*h[p],r=u?m[p-1]+n:n,l=u?g[p-1]+h[p]:h[p],m.push(r),g.push(l),x.push([t[p],r/l]),y.push(x[p][0]),f.push(x[p][1]),++u===o&&(u=0);return{values:x,xData:y,yData:f}}}an.defaultOptions=ai(as.defaultOptions,{params:{index:void 0,period:30,volumeSeriesID:"volume"}}),p().registerSeriesType("vwap",an);let{sma:ar}=p().seriesTypes,{extend:al,isArray:ap,merge:au}=i();class ah extends ar{getValues(e,t){let s,a,o,i,n,r,l=t.period,p=e.xData,u=e.yData,h=u?u.length:0,d=[],c=[],m=[];if(!(p.length<l)&&ap(u[0])&&4===u[0].length){for(r=l-1;r<h;r++)s=u.slice(r-l+1,r+1),n=(a=sa.getArrayExtremes(s,2,1))[0],o=-(((i=a[1])-u[r][3])/(i-n)*100),p[r]&&(d.push([p[r],o]),c.push(p[r]),m.push(o));return{values:d,xData:c,yData:m}}}}ah.defaultOptions=au(ar.defaultOptions,{params:{index:void 0,period:14}}),al(ah.prototype,{nameBase:"Williams %R"}),p().registerSeriesType("williamsr",ah);let{sma:ad}=p().seriesTypes,{isArray:ac,merge:am}=i();function ag(e,t,s,a,o){let i=t[a],n=o<0?s[a]:s[a][o];e.push([i,n])}function ay(e,t,s,a){let o=e.length,i=e.reduce(function(e,t,s){return[null,e[1]+t[1]*(s+1)]})[1]/((o+1)/2*o),n=t[a-1];return e.shift(),[n,i]}class af extends ad{getValues(e,t){let s=t.period,a=e.xData,o=e.yData,i=o?o.length:0,n=a[0],r=[],l=[],p=[],u=1,h=-1,d,c,m=o[0];if(a.length<s)return;ac(o[0])&&(h=t.index,m=o[0][h]);let g=[[n,m]];for(;u!==s;)ag(g,a,o,u,h),u++;for(d=u;d<i;d++)r.push(c=ay(g,a,o,d)),l.push(c[0]),p.push(c[1]),ag(g,a,o,d,h);return r.push(c=ay(g,a,o,d)),l.push(c[0]),p.push(c[1]),{values:r,xData:l,yData:p}}}af.defaultOptions=am(ad.defaultOptions,{params:{index:3,period:9}}),p().registerSeriesType("wma",af);let{sma:ax}=p().seriesTypes,{merge:aD,extend:av}=i();class aA extends ax{getValues(e,t){let s=t.lowIndex,a=t.highIndex,o=t.deviation/100,i={low:1+o,high:1-o},n=e.xData,r=e.yData,l=r?r.length:0,p=[],u=[],h=[],d,c,m,g,y=!1,f=!1;if(!n||n.length<=1||l&&(void 0===r[0][s]||void 0===r[0][a]))return;let x=r[0][s],D=r[0][a];for(d=1;d<l;d++)r[d][s]<=D*i.high?(p.push([n[0],D]),m=[n[d],r[d][s]],g=!0,y=!0):r[d][a]>=x*i.low&&(p.push([n[0],x]),m=[n[d],r[d][a]],g=!1,y=!0),y&&(u.push(p[0][0]),h.push(p[0][1]),c=d++,d=l);for(d=c;d<l;d++)g?(r[d][s]<=m[1]&&(m=[n[d],r[d][s]]),r[d][a]>=m[1]*i.low&&(f=a)):(r[d][a]>=m[1]&&(m=[n[d],r[d][a]]),r[d][s]<=m[1]*i.high&&(f=s)),!1!==f&&(p.push(m),u.push(m[0]),h.push(m[1]),m=[n[d],r[d][f]],g=!g,f=!1);let v=p.length;return 0!==v&&p[v-1][0]<n[l-1]&&(p.push(m),u.push(m[0]),h.push(m[1])),{values:p,xData:u,yData:h}}}aA.defaultOptions=aD(ax.defaultOptions,{params:{index:void 0,period:void 0,lowIndex:2,highIndex:1,deviation:1}}),av(aA.prototype,{nameComponents:["deviation"],nameSuffixes:["%"],nameBase:"Zig Zag"}),p().registerSeriesType("zigzag",aA);let{sma:aS}=p().seriesTypes,{isArray:ab,extend:aT,merge:aC}=i();class aP extends aS{getRegressionLineParameters(e,t){let s=this.options.params.index,a=function(e,t){return ab(e)?e[t]:e},o=e.reduce(function(e,t){return t+e},0),i=t.reduce(function(e,t){return a(t,s)+e},0),n=o/e.length,r=i/t.length,l,p,u=0,h=0;for(p=0;p<e.length;p++)u+=(l=e[p]-n)*(a(t[p],s)-r),h+=Math.pow(l,2);let d=h?u/h:0;return{slope:d,intercept:r-d*n}}getEndPointY(e,t){return e.slope*t+e.intercept}transformXData(e,t){let s=e[0];return e.map(function(e){return(e-s)/t})}findClosestDistance(e){let t,s,a;for(a=1;a<e.length-1;a++)(t=e[a]-e[a-1])>0&&(void 0===s||t<s)&&(s=t);return s}getValues(e,t){let s,a,o,i,n,r,l,p,u,h=e.xData,d=e.yData,c=t.period,m={xData:[],yData:[],values:[]},g=this.options.params.xAxisUnit||this.findClosestDistance(h);for(a=c-1;a<=h.length-1;a++)o=a-c+1,i=a+1,n=h[a],l=h.slice(o,i),p=d.slice(o,i),u=this.transformXData(l,g),s=this.getRegressionLineParameters(u,p),r=this.getEndPointY(s,u[u.length-1]),m.values.push({regressionLineParameters:s,x:n,y:r}),ab(m.xData)&&m.xData.push(n),ab(m.yData)&&m.yData.push(r);return m}}aP.defaultOptions=aC(aS.defaultOptions,{params:{xAxisUnit:null},tooltip:{valueDecimals:4}}),aT(aP.prototype,{nameBase:"Linear Regression Indicator"}),p().registerSeriesType("linearRegression",aP);let{linearRegression:aM}=p().seriesTypes,{extend:aV,merge:aL}=i();class ak extends aM{getEndPointY(e){return e.slope}}ak.defaultOptions=aL(aM.defaultOptions),aV(ak.prototype,{nameBase:"Linear Regression Slope Indicator"}),p().registerSeriesType("linearRegressionSlope",ak);let{linearRegression:aO}=p().seriesTypes,{extend:aE,merge:aw}=i();class aI extends aO{getEndPointY(e){return e.intercept}}aI.defaultOptions=aw(aO.defaultOptions),aE(aI.prototype,{nameBase:"Linear Regression Intercept Indicator"}),p().registerSeriesType("linearRegressionIntercept",aI);let{linearRegression:aN}=p().seriesTypes,{extend:aB,merge:aG}=i();class aW extends aN{slopeToAngle(e){return 180/Math.PI*Math.atan(e)}getEndPointY(e){return this.slopeToAngle(e.slope)}}aW.defaultOptions=aG(aN.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span>{series.name}: <b>{point.y}\xb0</b><br/>'}}),aB(aW.prototype,{nameBase:"Linear Regression Angle Indicator"}),p().registerSeriesType("linearRegressionAngle",aW);let{sma:az}=p().seriesTypes,{correctFloat:aY,extend:aF,merge:aX}=i();class aR extends az{getValues(e,t){let s,a,o,i,n,r,l,p,u,h,d,c=t.period,m=t.factor,g=t.index,y=e.xData,f=e.yData,x=f?f.length:0,D=[],v=[],A=[],S=[],b=[];if(!(x<c)){for(d=0;d<=x;d++){if(d<x){var T,C;T=f[d][2],n=aY((C=f[d][1])-T)/(aY(C+T)/2)*1e3*m,D.push(f[d][1]*aY(1+2*n)),v.push(f[d][2]*aY(1-2*n))}d>=c&&(u=y.slice(d-c,d),h=f.slice(d-c,d),l=super.getValues.call(this,{xData:u,yData:D.slice(d-c,d)},{period:c}),p=super.getValues.call(this,{xData:u,yData:v.slice(d-c,d)},{period:c}),i=(r=super.getValues.call(this,{xData:u,yData:h},{period:c,index:g})).xData[0],a=l.yData[0],o=p.yData[0],s=r.yData[0],A.push([i,a,s,o]),S.push(i),b.push([a,s,o]))}return{values:A,xData:S,yData:b}}}}aR.defaultOptions=aX(az.defaultOptions,{params:{period:20,factor:.001,index:3},lineWidth:1,topLine:{styles:{lineWidth:1}},bottomLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),aF(aR.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameBase:"Acceleration Bands",nameComponents:["period","factor"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),X.compose(aR),p().registerSeriesType("abands",aR);let{sma:aK}=p().seriesTypes,{extend:aU,merge:aZ,isArray:aj}=i();class a_ extends aK{constructor(){super(...arguments),this.updateAllPoints=!0}getValues(e,t){let s=e.xData,a=e.yData,o=[],i=[],n=[],r=[],l=t.index,p=0,u=0,h=0,d=0,c=0;for(let e=0;e<s.length;e++)(0===e||s[e]!==s[e-1])&&c++,o.push(c);for(let e=0;e<o.length;e++)h+=o[e],d+=aj(a[e])?a[e][l]:a[e];let m=h/o.length,g=d/a.length;for(let e=0;e<o.length;e++){let t=aj(a[e])?a[e][l]:a[e];p+=(o[e]-m)*(t-g),u+=Math.pow(o[e]-m,2)}for(let e=0;e<o.length;e++){if(s[e]===n[n.length-1])continue;let t=s[e],a=g+p/u*(o[e]-m);i.push([t,a]),n.push(t),r.push(a)}return{xData:n,yData:r,values:i}}}a_.defaultOptions=aZ(aK.defaultOptions,{params:{period:void 0,index:3}}),aU(a_.prototype,{nameBase:"Trendline",nameComponents:void 0}),p().registerSeriesType("trendline",a_);let{sma:aq}=p().seriesTypes,{correctFloat:aH,defined:a$,extend:aJ,isArray:aQ,merge:a0}=i();class a1 extends aq{init(){let e=arguments,t=e[1].params,s=t&&t.average?t.average:void 0;this.averageIndicator=p().seriesTypes[s]||aq,this.averageIndicator.prototype.init.apply(this,e)}calculateDisparityIndex(e,t){return aH(e-t)/t*100}getValues(e,t){let s=t.index,a=e.xData,o=e.yData,i=o?o.length:0,n=[],r=[],l=[],p=this.averageIndicator,u=aQ(o[0]),h=p.prototype.getValues(e,t),d=h.yData,c=a.indexOf(h.xData[0]);if(d&&0!==d.length&&a$(s)&&!(o.length<=c)){for(let e=c;e<i;e++){let t=this.calculateDisparityIndex(u?o[e][s]:o[e],d[e-c]);n.push([a[e],t]),r.push(a[e]),l.push(t)}return{values:n,xData:r,yData:l}}}}a1.defaultOptions=a0(aq.defaultOptions,{params:{average:"sma",index:3},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),aJ(a1.prototype,{nameBase:"Disparity Index",nameComponents:["period","average"]}),p().registerSeriesType("disparityindex",a1),a.d({},{});let a2=i();a2.MultipleLinesComposition=a2.MultipleLinesComposition||X;let a3=i();export{a3 as default};