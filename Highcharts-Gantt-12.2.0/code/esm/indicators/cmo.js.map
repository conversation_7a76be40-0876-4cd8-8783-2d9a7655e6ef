{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/cmo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Pawel Lysy\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_b3d80146__ from \"../modules/stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"../modules/stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const stock_src_js_namespaceObject = x({  });\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/CMO/CMOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber, merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmo\n *\n * @augments Highcharts.Series\n */\nclass CMOIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, CMO = [], xData = [], yData = [];\n        let i, index = params.index, values;\n        if (xVal.length < period) {\n            return;\n        }\n        if (isNumber(yVal[0])) {\n            values = yVal;\n        }\n        else {\n            // In case of the situation, where the series type has data length\n            // shorter then 4 (HLC, range), this ensures that we are not trying\n            // to reach the index out of bounds\n            index = Math.min(index, yVal[0].length - 1);\n            values = yVal.map((value) => value[index]);\n        }\n        let firstAddedSum = 0, sumOfHigherValues = 0, sumOfLowerValues = 0, y;\n        // Calculate first point, check if the first value\n        // was added to sum of higher/lower values, and what was the value.\n        for (let j = period; j > 0; j--) {\n            if (values[j] > values[j - 1]) {\n                sumOfHigherValues += values[j] - values[j - 1];\n            }\n            else if (values[j] < values[j - 1]) {\n                sumOfLowerValues += values[j - 1] - values[j];\n            }\n        }\n        // You might divide by 0 if all values are equal,\n        // so return 0 in this case.\n        y =\n            sumOfHigherValues + sumOfLowerValues > 0 ?\n                (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                    (sumOfHigherValues + sumOfLowerValues) :\n                0;\n        xData.push(xVal[period]);\n        yData.push(y);\n        CMO.push([xVal[period], y]);\n        for (i = period + 1; i < yValLen; i++) {\n            firstAddedSum = Math.abs(values[i - period - 1] - values[i - period]);\n            if (values[i] > values[i - 1]) {\n                sumOfHigherValues += values[i] - values[i - 1];\n            }\n            else if (values[i] < values[i - 1]) {\n                sumOfLowerValues += values[i - 1] - values[i];\n            }\n            // Check, to which sum was the first value added to,\n            // and subtract this value from given sum.\n            if (values[i - period] > values[i - period - 1]) {\n                sumOfHigherValues -= firstAddedSum;\n            }\n            else {\n                sumOfLowerValues -= firstAddedSum;\n            }\n            // Same as above.\n            y =\n                sumOfHigherValues + sumOfLowerValues > 0 ?\n                    (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                        (sumOfHigherValues + sumOfLowerValues) :\n                    0;\n            xData.push(xVal[i]);\n            yData.push(y);\n            CMO.push([xVal[i], y]);\n        }\n        return {\n            values: CMO,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Chande Momentum Oscilator (CMO) technical indicator. This series\n * requires the `linkedTo` option to be set and should be loaded after\n * the `stock/indicators/indicators.js` file.\n *\n * @sample stock/indicators/cmo\n *         CMO indicator\n *\n * @extends      plotOptions.sma\n * @since 9.1.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cmo\n * @optionparent plotOptions.cmo\n */\nCMOIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        period: 20,\n        index: 3\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('cmo', CMOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CMO_CMOIndicator = ((/* unused pure expression or super */ null && (CMOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMO` series. If the [type](#series.cmo.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmo\n * @since 9.1.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmo\n * @apioption series.cmo\n */\n(''); // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/cmo.js\n\n\n\n\n\n/* harmony default export */ const cmo_src = ((external_highcharts_src_js_default_default()));\n\nexport { cmo_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isNumber", "merge", "CMOIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "CMO", "i", "index", "values", "Math", "min", "map", "value", "firstAddedSum", "sumOfHigher<PERSON><PERSON><PERSON>", "sumOfLowerValues", "y", "j", "push", "abs", "defaultOptions", "registerSeriesType", "cmo_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,AAChG,OAA4E,yBAA0B,CAE7F,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAIiC,CAAG,GAE9C,IAAMiB,EAAoEvB,EAAwD,OAAU,CAACwB,cAAc,CAC3J,IAAIC,EAAyExB,EAAoBC,CAAC,CAACqB,GAWnG,GAAM,CAAEG,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA6DG,WAAW,CAEjG,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE,CAAIR,GAe7B,OAAMS,UAAqBJ,EAMvBK,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAM,EAAE,CAAEL,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CACtII,EAAGC,EAAQV,EAAOU,KAAK,CAAEC,EAC7B,GAAIT,EAAKK,MAAM,CAAGN,EACd,OAEAN,EAASS,CAAI,CAAC,EAAE,EAChBO,EAASP,GAMTM,EAAQE,KAAKC,GAAG,CAACH,EAAON,CAAI,CAAC,EAAE,CAACG,MAAM,CAAG,GACzCI,EAASP,EAAKU,GAAG,CAAC,AAACC,GAAUA,CAAK,CAACL,EAAM,GAE7C,IAAIM,EAAgB,EAAGC,EAAoB,EAAGC,EAAmB,EAAGC,EAGpE,IAAK,IAAIC,EAAInB,EAAQmB,EAAI,EAAGA,IACpBT,CAAM,CAACS,EAAE,CAAGT,CAAM,CAACS,EAAI,EAAE,CACzBH,GAAqBN,CAAM,CAACS,EAAE,CAAGT,CAAM,CAACS,EAAI,EAAE,CAEzCT,CAAM,CAACS,EAAE,CAAGT,CAAM,CAACS,EAAI,EAAE,EAC9BF,CAAAA,GAAoBP,CAAM,CAACS,EAAI,EAAE,CAAGT,CAAM,CAACS,EAAE,AAAD,EAapD,IARAD,EACIF,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRf,EAAMkB,IAAI,CAACnB,CAAI,CAACD,EAAO,EACvBI,EAAMgB,IAAI,CAACF,GACXX,EAAIa,IAAI,CAAC,CAACnB,CAAI,CAACD,EAAO,CAAEkB,EAAE,EACrBV,EAAIR,EAAS,EAAGQ,EAAIH,EAASG,IAC9BO,EAAgBJ,KAAKU,GAAG,CAACX,CAAM,CAACF,EAAIR,EAAS,EAAE,CAAGU,CAAM,CAACF,EAAIR,EAAO,EAChEU,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CACzBQ,GAAqBN,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,CAEzCE,CAAM,CAACF,EAAE,CAAGE,CAAM,CAACF,EAAI,EAAE,EAC9BS,CAAAA,GAAoBP,CAAM,CAACF,EAAI,EAAE,CAAGE,CAAM,CAACF,EAAE,AAAD,EAI5CE,CAAM,CAACF,EAAIR,EAAO,CAAGU,CAAM,CAACF,EAAIR,EAAS,EAAE,CAC3CgB,GAAqBD,EAGrBE,GAAoBF,EAGxBG,EACIF,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRf,EAAMkB,IAAI,CAACnB,CAAI,CAACO,EAAE,EAClBJ,EAAMgB,IAAI,CAACF,GACXX,EAAIa,IAAI,CAAC,CAACnB,CAAI,CAACO,EAAE,CAAEU,EAAE,EAEzB,MAAO,CACHR,OAAQH,EACRL,MAAOA,EACPE,MAAOA,CACX,CACJ,CACJ,CAqBAR,EAAa0B,cAAc,CAAG3B,EAAMH,EAAa8B,cAAc,CAAE,CAC7DvB,OAAQ,CACJC,OAAQ,GACRS,MAAO,CACX,CACJ,GACAnB,IAA4DiC,kBAAkB,CAAC,MAAO3B,GAgCzD,IAAM4B,EAAYrC,WAEtCqC,KAAWC,OAAO"}