/**
 * @license Highcharts Gantt JS v12.2.0 (2025-04-07)
 * @module highcharts/highcharts-gantt
 *
 * (c) 2017-2025 <PERSON>, <PERSON><PERSON>, <PERSON> & O<PERSON><PERSON>g
 *
 * License: www.highcharts.com/license
 */
import * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_c57973fa__ from "./highcharts.src.js";
import * as __WEBPACK_EXTERNAL_MODULE__modules_gantt_src_js_671dcdef__ from "./modules/gantt.src.js";
/******/ // The require scope
/******/ var __webpack_require__ = {};
/******/ 
/************************************************************************/
/******/ /* webpack/runtime/define property getters */
/******/ (() => {
/******/ 	// define getter functions for harmony exports
/******/ 	__webpack_require__.d = (exports, definition) => {
/******/ 		for(var key in definition) {
/******/ 			if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 				Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 			}
/******/ 		}
/******/ 	};
/******/ })();
/******/ 
/******/ /* webpack/runtime/hasOwnProperty shorthand */
/******/ (() => {
/******/ 	__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ })();
/******/ 
/************************************************************************/

;// external "./highcharts.src.js"
var x = (y) => {
	var x = {}; __webpack_require__.d(x,
    	y); return x
    } 
    var y = (x) => (() => (x))
    const external_highcharts_src_js_namespaceObject = x({ ["default"]: () => (__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_c57973fa__["default"]) });
;// external "./modules/gantt.src.js"
var gantt_src_js_x = (y) => {
	var x = {}; __webpack_require__.d(x,
    	y); return x
    } 
    var gantt_src_js_y = (x) => (() => (x))
    const gantt_src_js_namespaceObject = gantt_src_js_x({  });
;// ./code/es-modules/masters/highcharts-gantt.src.js




external_highcharts_src_js_namespaceObject["default"].product = 'Highcharts Gantt';
/* harmony default export */ const highcharts_gantt_src = (external_highcharts_src_js_namespaceObject["default"]);

export { highcharts_gantt_src as default };
