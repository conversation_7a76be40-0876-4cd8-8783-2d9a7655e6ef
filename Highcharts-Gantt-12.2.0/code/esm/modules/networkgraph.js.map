{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/networkgraph\n * @requires highcharts\n *\n * Force directed graph module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SVGElement\"]\nconst external_highcharts_src_js_default_SVGElement_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGElement;\nvar external_highcharts_src_js_default_SVGElement_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGElement_namespaceObject);\n;// ./code/es-modules/Series/DragNodesComposition.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass) {\n    if (pushUnique(composed, 'DragNodes')) {\n        addEvent(ChartClass, 'load', onChartLoad);\n    }\n}\n/**\n * Draggable mode:\n * @private\n */\nfunction onChartLoad() {\n    const chart = this;\n    let mousedownUnbinder, mousemoveUnbinder, mouseupUnbinder, point;\n    if (chart.container) {\n        mousedownUnbinder = addEvent(chart.container, 'mousedown', (event) => {\n            if (mousemoveUnbinder) {\n                mousemoveUnbinder();\n            }\n            if (mouseupUnbinder) {\n                mouseupUnbinder();\n            }\n            point = chart.hoverPoint;\n            if (point &&\n                point.series &&\n                point.series.hasDraggableNodes &&\n                point.series.options.draggable) {\n                point.series.onMouseDown(point, event);\n                mousemoveUnbinder = addEvent(chart.container, 'mousemove', (e) => (point &&\n                    point.series &&\n                    point.series.onMouseMove(point, e)));\n                mouseupUnbinder = addEvent(chart.container.ownerDocument, 'mouseup', (e) => {\n                    mousemoveUnbinder();\n                    mouseupUnbinder();\n                    return point &&\n                        point.series &&\n                        point.series.onMouseUp(point, e);\n                });\n            }\n        });\n    }\n    addEvent(chart, 'destroy', function () {\n        mousedownUnbinder();\n    });\n}\n/**\n * Mouse down action, initializing drag&drop mode.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n * @param {Highcharts.PointerEventObject} event\n *        Browser event, before normalization.\n */\nfunction onMouseDown(point, event) {\n    const normalizedEvent = this.chart.pointer?.normalize(event) || event;\n    point.fixedPosition = {\n        chartX: normalizedEvent.chartX,\n        chartY: normalizedEvent.chartY,\n        plotX: point.plotX,\n        plotY: point.plotY\n    };\n    point.inDragMode = true;\n}\n/**\n * Mouse move action during drag&drop.\n *\n * @private\n *\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n * @param {global.Event} event\n *        Browser event, before normalization.\n */\nfunction onMouseMove(point, event) {\n    if (point.fixedPosition && point.inDragMode) {\n        const series = this, chart = series.chart, normalizedEvent = chart.pointer?.normalize(event) || event, diffX = point.fixedPosition.chartX - normalizedEvent.chartX, diffY = point.fixedPosition.chartY - normalizedEvent.chartY, graphLayoutsLookup = chart.graphLayoutsLookup;\n        let newPlotX, newPlotY;\n        // At least 5px to apply change (avoids simple click):\n        if (Math.abs(diffX) > 5 || Math.abs(diffY) > 5) {\n            newPlotX = point.fixedPosition.plotX - diffX;\n            newPlotY = point.fixedPosition.plotY - diffY;\n            if (chart.isInsidePlot(newPlotX, newPlotY)) {\n                point.plotX = newPlotX;\n                point.plotY = newPlotY;\n                point.hasDragged = true;\n                this.redrawHalo(point);\n                graphLayoutsLookup.forEach((layout) => {\n                    layout.restartSimulation();\n                });\n            }\n        }\n    }\n}\n/**\n * Mouse up action, finalizing drag&drop.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n */\nfunction onMouseUp(point) {\n    if (point.fixedPosition) {\n        if (point.hasDragged) {\n            if (this.layout.enableSimulation) {\n                this.layout.start();\n            }\n            else {\n                this.chart.redraw();\n            }\n        }\n        point.inDragMode = point.hasDragged = false;\n        if (!this.options.fixedDraggable) {\n            delete point.fixedPosition;\n        }\n    }\n}\n/**\n * Redraw halo on mousemove during the drag&drop action.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that should show halo.\n */\nfunction redrawHalo(point) {\n    if (point && this.halo) {\n        this.halo.attr({\n            d: point.haloPath(this.options.states.hover.halo.size)\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DragNodesComposition = {\n    compose,\n    onMouseDown,\n    onMouseMove,\n    onMouseUp,\n    redrawHalo\n};\n/* harmony default export */ const Series_DragNodesComposition = (DragNodesComposition);\n\n;// ./code/es-modules/Series/GraphLayoutComposition.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setAnimation } = (external_highcharts_src_js_default_default());\n\nconst { composed: GraphLayoutComposition_composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent: GraphLayoutComposition_addEvent, pushUnique: GraphLayoutComposition_pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst integrations = {};\nconst layouts = {};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction GraphLayoutComposition_compose(ChartClass) {\n    if (GraphLayoutComposition_pushUnique(GraphLayoutComposition_composed, 'GraphLayout')) {\n        GraphLayoutComposition_addEvent(ChartClass, 'afterPrint', onChartAfterPrint);\n        GraphLayoutComposition_addEvent(ChartClass, 'beforePrint', onChartBeforePrint);\n        GraphLayoutComposition_addEvent(ChartClass, 'predraw', onChartPredraw);\n        GraphLayoutComposition_addEvent(ChartClass, 'render', onChartRender);\n    }\n}\n/**\n * Re-enable simulation after print.\n * @private\n */\nfunction onChartAfterPrint() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach((layout) => {\n            // Return to default simulation\n            layout.updateSimulation();\n        });\n        this.redraw();\n    }\n}\n/**\n * Disable simulation before print if enabled.\n * @private\n */\nfunction onChartBeforePrint() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach((layout) => {\n            layout.updateSimulation(false);\n        });\n        this.redraw();\n    }\n}\n/**\n * Clear previous layouts.\n * @private\n */\nfunction onChartPredraw() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach((layout) => {\n            layout.stop();\n        });\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    let systemsStable, afterRender = false;\n    const layoutStep = (layout) => {\n        if (layout.maxIterations-- &&\n            isFinite(layout.temperature) &&\n            !layout.isStable() &&\n            !layout.enableSimulation) {\n            // Hook similar to build-in addEvent, but instead of\n            // creating whole events logic, use just a function.\n            // It's faster which is important for rAF code.\n            // Used e.g. in packed-bubble series for bubble radius\n            // calculations\n            if (layout.beforeStep) {\n                layout.beforeStep();\n            }\n            layout.step();\n            systemsStable = false;\n            afterRender = true;\n        }\n    };\n    if (this.graphLayoutsLookup) {\n        setAnimation(false, this);\n        // Start simulation\n        this.graphLayoutsLookup.forEach((layout) => layout.start());\n        // Just one sync step, to run different layouts similar to\n        // async mode.\n        while (!systemsStable) {\n            systemsStable = true;\n            this.graphLayoutsLookup.forEach(layoutStep);\n        }\n        if (afterRender) {\n            this.series.forEach((series) => {\n                if (series && series.layout) {\n                    series.render();\n                }\n            });\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst GraphLayoutComposition = {\n    compose: GraphLayoutComposition_compose,\n    integrations,\n    layouts\n};\n/* harmony default export */ const Series_GraphLayoutComposition = (GraphLayoutComposition);\n\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Series/NodesComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto, prototype: { pointClass: { prototype: pointProto } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { defined, extend, find, merge, pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar NodesComposition;\n(function (NodesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(PointClass, SeriesClass) {\n        const pointProto = PointClass.prototype, seriesProto = SeriesClass.prototype;\n        pointProto.setNodeState = setNodeState;\n        pointProto.setState = setNodeState;\n        pointProto.update = updateNode;\n        seriesProto.destroy = destroy;\n        seriesProto.setData = setData;\n        return SeriesClass;\n    }\n    NodesComposition.compose = compose;\n    /**\n     * Create a single node that holds information on incoming and outgoing\n     * links.\n     * @private\n     */\n    function createNode(id) {\n        const PointClass = this.pointClass, findById = (nodes, id) => find(nodes, (node) => node.id === id);\n        let node = findById(this.nodes, id), options;\n        if (!node) {\n            options = this.options.nodes && findById(this.options.nodes, id);\n            const newNode = new PointClass(this, extend({\n                className: 'highcharts-node',\n                isNode: true,\n                id: id,\n                y: 1 // Pass isNull test\n            }, options));\n            newNode.linksTo = [];\n            newNode.linksFrom = [];\n            /**\n             * Return the largest sum of either the incoming or outgoing links.\n             * @private\n             */\n            newNode.getSum = function () {\n                let sumTo = 0, sumFrom = 0;\n                newNode.linksTo.forEach((link) => {\n                    sumTo += link.weight || 0;\n                });\n                newNode.linksFrom.forEach((link) => {\n                    sumFrom += link.weight || 0;\n                });\n                return Math.max(sumTo, sumFrom);\n            };\n            /**\n             * Get the offset in weight values of a point/link.\n             * @private\n             */\n            newNode.offset = function (point, coll) {\n                let offset = 0;\n                for (let i = 0; i < newNode[coll].length; i++) {\n                    if (newNode[coll][i] === point) {\n                        return offset;\n                    }\n                    offset += newNode[coll][i].weight;\n                }\n            };\n            // Return true if the node has a shape, otherwise all links are\n            // outgoing.\n            newNode.hasShape = function () {\n                let outgoing = 0;\n                newNode.linksTo.forEach((link) => {\n                    if (link.outgoing) {\n                        outgoing++;\n                    }\n                });\n                return (!newNode.linksTo.length ||\n                    outgoing !== newNode.linksTo.length);\n            };\n            newNode.index = this.nodes.push(newNode) - 1;\n            node = newNode;\n        }\n        node.formatPrefix = 'node';\n        // For use in formats\n        node.name = node.name || node.options.id || '';\n        // Mass is used in networkgraph:\n        node.mass = pick(\n        // Node:\n        node.options.mass, node.options.marker && node.options.marker.radius, \n        // Series:\n        this.options.marker && this.options.marker.radius, \n        // Default:\n        4);\n        return node;\n    }\n    NodesComposition.createNode = createNode;\n    /**\n     * Destroy all nodes and links.\n     * @private\n     */\n    function destroy() {\n        // Nodes must also be destroyed (#8682, #9300)\n        this.data = []\n            .concat(this.points || [], this.nodes);\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    NodesComposition.destroy = destroy;\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects but\n     * pushed to the this.nodes array.\n     * @private\n     */\n    function generatePoints() {\n        const chart = this.chart, nodeLookup = {};\n        seriesProto.generatePoints.call(this);\n        if (!this.nodes) {\n            this.nodes = []; // List of Point-like node items\n        }\n        this.colorCounter = 0;\n        // Reset links from previous run\n        this.nodes.forEach((node) => {\n            node.linksFrom.length = 0;\n            node.linksTo.length = 0;\n            node.level = node.options.level;\n        });\n        // Create the node list and set up links\n        this.points.forEach((point) => {\n            if (defined(point.from)) {\n                if (!nodeLookup[point.from]) {\n                    nodeLookup[point.from] = this.createNode(point.from);\n                }\n                nodeLookup[point.from].linksFrom.push(point);\n                point.fromNode = nodeLookup[point.from];\n                // Point color defaults to the fromNode's color\n                if (chart.styledMode) {\n                    point.colorIndex = pick(point.options.colorIndex, nodeLookup[point.from].colorIndex);\n                }\n                else {\n                    point.color =\n                        point.options.color || nodeLookup[point.from].color;\n                }\n            }\n            if (defined(point.to)) {\n                if (!nodeLookup[point.to]) {\n                    nodeLookup[point.to] = this.createNode(point.to);\n                }\n                nodeLookup[point.to].linksTo.push(point);\n                point.toNode = nodeLookup[point.to];\n            }\n            point.name = point.name || point.id; // For use in formats\n        }, this);\n        // Store lookup table for later use\n        this.nodeLookup = nodeLookup;\n    }\n    NodesComposition.generatePoints = generatePoints;\n    /**\n     * Destroy all nodes on setting new data\n     * @private\n     */\n    function setData() {\n        if (this.nodes) {\n            this.nodes.forEach((node) => {\n                node.destroy();\n            });\n            this.nodes.length = 0;\n        }\n        seriesProto.setData.apply(this, arguments);\n    }\n    /**\n     * When hovering node, highlight all connected links. When hovering a link,\n     * highlight all connected nodes.\n     * @private\n     */\n    function setNodeState(state) {\n        const args = arguments, others = this.isNode ? this.linksTo.concat(this.linksFrom) :\n            [this.fromNode, this.toNode];\n        if (state !== 'select') {\n            others.forEach((linkOrNode) => {\n                if (linkOrNode && linkOrNode.series) {\n                    pointProto.setState.apply(linkOrNode, args);\n                    if (!linkOrNode.isNode) {\n                        if (linkOrNode.fromNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.fromNode, args);\n                        }\n                        if (linkOrNode.toNode && linkOrNode.toNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.toNode, args);\n                        }\n                    }\n                }\n            });\n        }\n        pointProto.setState.apply(this, args);\n    }\n    NodesComposition.setNodeState = setNodeState;\n    /**\n     * When updating a node, don't update `series.options.data`, but\n     * `series.options.nodes`\n     * @private\n     */\n    function updateNode(options, redraw, animation, runEvent) {\n        const nodes = this.series.options.nodes, data = this.series.options.data, dataLength = data?.length || 0, linkConfig = data?.[this.index];\n        pointProto.update.call(this, options, this.isNode ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        if (this.isNode) {\n            // `this.index` refers to `series.nodes`, not `options.nodes` array\n            const nodeIndex = (nodes || [])\n                .reduce(// Array.findIndex needs a polyfill\n            (prevIndex, n, index) => (this.id === n.id ? index : prevIndex), -1), \n            // Merge old config with new config. New config is stored in\n            // options.data, because of default logic in point.update()\n            nodeConfig = merge(nodes && nodes[nodeIndex] || {}, data?.[this.index] || {});\n            // Restore link config\n            if (data) {\n                if (linkConfig) {\n                    data[this.index] = linkConfig;\n                }\n                else {\n                    // Remove node from config if there's more nodes than links\n                    data.length = dataLength;\n                }\n            }\n            // Set node config\n            if (nodes) {\n                if (nodeIndex >= 0) {\n                    nodes[nodeIndex] = nodeConfig;\n                }\n                else {\n                    nodes.push(nodeConfig);\n                }\n            }\n            else {\n                this.series.options.nodes = [nodeConfig];\n            }\n            if (pick(redraw, true)) {\n                this.series.chart.redraw(animation);\n            }\n        }\n    }\n    NodesComposition.updateNode = updateNode;\n})(NodesComposition || (NodesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_NodesComposition = (NodesComposition);\n\n;// ./code/es-modules/Series/Networkgraph/NetworkgraphPoint.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { series: { prototype: NetworkgraphPoint_seriesProto, prototype: { pointClass: Point } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { addEvent: NetworkgraphPoint_addEvent, css, defined: NetworkgraphPoint_defined, extend: NetworkgraphPoint_extend, pick: NetworkgraphPoint_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass NetworkgraphPoint extends Point {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy point. If it's a node, remove all links coming out of this\n     * node. Then remove point from the layout.\n     * @private\n     */\n    destroy() {\n        if (this.isNode) {\n            this.linksFrom.concat(this.linksTo).forEach(function (link) {\n                // Removing multiple nodes at the same time\n                // will try to remove link between nodes twice\n                if (link.destroyElements) {\n                    link.destroyElements();\n                }\n            });\n        }\n        this.series.layout.removeElementFromCollection(this, this.series.layout[this.isNode ? 'nodes' : 'links']);\n        return Point.prototype.destroy.apply(this, arguments);\n    }\n    /**\n     * Return degree of a node. If node has no connections, it still has\n     * deg=1.\n     * @private\n     */\n    getDegree() {\n        const deg = this.isNode ?\n            this.linksFrom.length + this.linksTo.length :\n            0;\n        return deg === 0 ? 1 : deg;\n    }\n    /**\n     * Get presentational attributes of link connecting two nodes.\n     * @private\n     */\n    getLinkAttributes() {\n        const linkOptions = this.series.options.link, pointOptions = this.options;\n        return {\n            'stroke-width': NetworkgraphPoint_pick(pointOptions.width, linkOptions.width),\n            stroke: (pointOptions.color || linkOptions.color),\n            dashstyle: (pointOptions.dashStyle || linkOptions.dashStyle),\n            opacity: NetworkgraphPoint_pick(pointOptions.opacity, linkOptions.opacity, 1)\n        };\n    }\n    /**\n     * Get link path connecting two nodes.\n     * @private\n     * @return {Array<Highcharts.SVGPathArray>}\n     *         Path: `['M', x, y, 'L', x, y]`\n     */\n    getLinkPath() {\n        let left = this.fromNode, right = this.toNode;\n        // Start always from left to the right node, to prevent rendering\n        // labels upside down\n        if (left.plotX > right.plotX) {\n            left = this.toNode;\n            right = this.fromNode;\n        }\n        return [\n            ['M', left.plotX || 0, left.plotY || 0],\n            ['L', right.plotX || 0, right.plotY || 0]\n        ];\n        /*\n        IDEA: different link shapes?\n        return [\n            'M',\n            from.plotX,\n            from.plotY,\n            'Q',\n            (to.plotX + from.plotX) / 2,\n            (to.plotY + from.plotY) / 2 + 15,\n            to.plotX,\n            to.plotY\n        ];*/\n    }\n    /**\n     * Get mass fraction applied on two nodes connected to each other. By\n     * default, when mass is equal to `1`, mass fraction for both nodes\n     * equal to 0.5.\n     * @private\n     * @return {Highcharts.Dictionary<number>}\n     *         For example `{ fromNode: 0.5, toNode: 0.5 }`\n     */\n    getMass() {\n        const m1 = this.fromNode.mass, m2 = this.toNode.mass, sum = m1 + m2;\n        return {\n            fromNode: 1 - m1 / sum,\n            toNode: 1 - m2 / sum\n        };\n    }\n    /**\n     * Basic `point.init()` and additional styles applied when\n     * `series.draggable` is enabled.\n     * @private\n     */\n    constructor(series, options, x) {\n        super(series, options, x);\n        if (this.series.options.draggable &&\n            !this.series.chart.styledMode) {\n            NetworkgraphPoint_addEvent(this, 'mouseOver', function () {\n                css(this.series.chart.container, { cursor: 'move' });\n            });\n            NetworkgraphPoint_addEvent(this, 'mouseOut', function () {\n                css(this.series.chart.container, { cursor: 'default' });\n            });\n        }\n    }\n    /**\n     * @private\n     */\n    isValid() {\n        return !this.isNode || NetworkgraphPoint_defined(this.id);\n    }\n    /**\n     * Redraw link's path.\n     * @private\n     */\n    redrawLink() {\n        const path = this.getLinkPath();\n        let attribs;\n        if (this.graphic) {\n            this.shapeArgs = {\n                d: path\n            };\n            if (!this.series.chart.styledMode) {\n                attribs = this.series.pointAttribs(this);\n                this.graphic.attr(attribs);\n                (this.dataLabels || []).forEach(function (label) {\n                    if (label) {\n                        label.attr({\n                            opacity: attribs.opacity\n                        });\n                    }\n                });\n            }\n            this.graphic.animate(this.shapeArgs);\n            // Required for dataLabels\n            const start = path[0];\n            const end = path[1];\n            if (start[0] === 'M' && end[0] === 'L') {\n                this.plotX = (start[1] + end[1]) / 2;\n                this.plotY = (start[2] + end[2]) / 2;\n            }\n        }\n    }\n    /**\n     * Common method for removing points and nodes in networkgraph. To\n     * remove `link`, use `series.data[index].remove()`. To remove `node`\n     * with all connections, use `series.nodes[index].remove()`.\n     * @private\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart or wait for an explicit call. When\n     *        doing more operations on the chart, for example running\n     *        `point.remove()` in a loop, it is best practice to set\n     *        `redraw` to false and call `chart.redraw()` after.\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation=false]\n     *        Whether to apply animation, and optionally animation\n     *        configuration.\n     */\n    remove(redraw, animation) {\n        const point = this, series = point.series, nodesOptions = series.options.nodes || [];\n        let index, i = nodesOptions.length;\n        // For nodes, remove all connected links:\n        if (point.isNode) {\n            // Temporary disable series.points array, because\n            // Series.removePoint() modifies it\n            series.points = [];\n            // Remove link from all nodes collections:\n            []\n                .concat(point.linksFrom)\n                .concat(point.linksTo)\n                .forEach(function (linkFromTo) {\n                // Incoming links\n                index = linkFromTo.fromNode.linksFrom.indexOf(linkFromTo);\n                if (index > -1) {\n                    linkFromTo.fromNode.linksFrom.splice(index, 1);\n                }\n                // Outcoming links\n                index = linkFromTo.toNode.linksTo.indexOf(linkFromTo);\n                if (index > -1) {\n                    linkFromTo.toNode.linksTo.splice(index, 1);\n                }\n                // Remove link from data/points collections\n                NetworkgraphPoint_seriesProto.removePoint.call(series, series.data.indexOf(linkFromTo), false, false);\n            });\n            // Restore points array, after links are removed\n            series.points = series.data.slice();\n            // Proceed with removing node. It's similar to\n            // Series.removePoint() method, but doesn't modify other arrays\n            series.nodes.splice(series.nodes.indexOf(point), 1);\n            // Remove node options from config\n            while (i--) {\n                if (nodesOptions[i].id === point.options.id) {\n                    series.options.nodes.splice(i, 1);\n                    break;\n                }\n            }\n            if (point) {\n                point.destroy();\n            }\n            // Run redraw if requested\n            series.isDirty = true;\n            series.isDirtyData = true;\n            if (redraw) {\n                series.chart.redraw(redraw);\n            }\n        }\n        else {\n            series.removePoint(series.data.indexOf(point), redraw, animation);\n        }\n    }\n    /**\n     * Render link and add it to the DOM.\n     * @private\n     */\n    renderLink() {\n        let attribs;\n        if (!this.graphic) {\n            this.graphic = this.series.chart.renderer\n                .path(this.getLinkPath())\n                .addClass(this.getClassName(), true)\n                .add(this.series.group);\n            if (!this.series.chart.styledMode) {\n                attribs = this.series.pointAttribs(this);\n                this.graphic.attr(attribs);\n                (this.dataLabels || []).forEach(function (label) {\n                    if (label) {\n                        label.attr({\n                            opacity: attribs.opacity\n                        });\n                    }\n                });\n            }\n        }\n    }\n}\nNetworkgraphPoint_extend(NetworkgraphPoint.prototype, {\n    setState: Series_NodesComposition.setNodeState\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_NetworkgraphPoint = (NetworkgraphPoint);\n\n;// ./code/es-modules/Series/Networkgraph/NetworkgraphSeriesDefaults.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * A networkgraph is a type of relationship chart, where connnections\n * (links) attracts nodes (points) and other nodes repulse each other.\n *\n * @extends      plotOptions.line\n * @product      highcharts\n * @sample       highcharts/demo/network-graph/\n *               Networkgraph\n * @since        7.0.0\n * @excluding    boostThreshold, animation, animationLimit, connectEnds,\n *               colorAxis, colorKey, connectNulls, cropThreshold, dragDrop,\n *               getExtremesFromAll, label, linecap, negativeColor,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointStart, softThreshold, stack, stacking, step,\n *               threshold, xAxis, yAxis, zoneAxis, dataSorting,\n *               boostBlending\n * @requires     modules/networkgraph\n * @optionparent plotOptions.networkgraph\n *\n * @private\n */\nconst NetworkgraphSeriesDefaults = {\n    stickyTracking: false,\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    marker: {\n        enabled: true,\n        states: {\n            /**\n             * The opposite state of a hover for a single point node.\n             * Applied to all not connected nodes to the hovered one.\n             *\n             * @declare Highcharts.PointStatesInactiveOptionsObject\n             */\n            inactive: {\n                /**\n                 * Opacity of inactive markers.\n                 */\n                opacity: 0.3,\n                /**\n                 * Animation when not hovering over the node.\n                 *\n                 * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n                 */\n                animation: {\n                    /** @internal */\n                    duration: 50\n                }\n            }\n        }\n    },\n    states: {\n        /**\n         * The opposite state of a hover for a single point link. Applied\n         * to all links that are not coming from the hovered node.\n         *\n         * @declare Highcharts.SeriesStatesInactiveOptionsObject\n         */\n        inactive: {\n            /**\n             * Opacity of inactive links.\n             */\n            linkOpacity: 0.3,\n            /**\n             * Animation when not hovering over the node.\n             *\n             * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             */\n            animation: {\n                /** @internal */\n                duration: 50\n            }\n        }\n    },\n    /**\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Networkgraph with labels on links\n     * @sample highcharts/series-networkgraph/textpath-datalabels\n     *         Networkgraph with labels around nodes\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Data labels moved into the nodes\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Data labels moved under the links\n     *\n     * @declare Highcharts.SeriesNetworkgraphDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _node_ in the networkgraph. In v7.0\n         * defaults to `{key}`, since v7.1 defaults to `undefined` and\n         * `formatter` is used instead.\n         *\n         * @type      {string}\n         * @since     7.0.0\n         * @apioption plotOptions.networkgraph.dataLabels.format\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback JavaScript function to format the data label for a node.\n         * Note that if a `format` is defined, the format takes precedence\n         * and the formatter is ignored.\n         *\n         * @since 7.0.0\n         */\n        formatter: function () {\n            return String(this.key ?? '');\n        },\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _links_ in the networkgraph.\n         * (Default: `undefined`)\n         *\n         * @type      {string}\n         * @since     7.1.0\n         * @apioption plotOptions.networkgraph.dataLabels.linkFormat\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback to format data labels for _links_ in the sankey diagram.\n         * The `linkFormat` option takes precedence over the\n         * `linkFormatter`.\n         *\n         * @since 7.1.0\n         */\n        linkFormatter: function () {\n            return (this.fromNode.name +\n                '<br>' +\n                this.toNode.name);\n        },\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label that\n         * follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option. Setting\n         * `useHTML` to true will disable this option.\n         *\n         * @extends plotOptions.networkgraph.dataLabels.textPath\n         * @since   7.1.0\n         */\n        linkTextPath: {\n            enabled: true\n        },\n        textPath: {\n            enabled: false\n        },\n        style: {\n            transition: 'opacity 2000ms'\n        },\n        defer: true,\n        animation: {\n            defer: 1000\n        }\n    },\n    /**\n     * Link style options\n     * @private\n     */\n    link: {\n        /**\n         * A name for the dash style to use for links.\n         *\n         * @type      {string}\n         * @apioption plotOptions.networkgraph.link.dashStyle\n         */\n        /**\n         * Opacity of the link between two nodes.\n         *\n         * @type      {number}\n         * @default   1\n         * @apioption plotOptions.networkgraph.link.opacity\n         */\n        /**\n         * Color of the link between two nodes.\n         */\n        color: 'rgba(100, 100, 100, 0.5)',\n        /**\n         * Width (px) of the link between two nodes.\n         */\n        width: 1\n    },\n    /**\n     * Flag to determine if nodes are draggable or not.\n     * @private\n     */\n    draggable: true,\n    layoutAlgorithm: {\n        /**\n         * Repulsive force applied on a node. Passed are two arguments:\n         * - `d` - which is current distance between two nodes\n         * - `k` - which is desired distance between two nodes\n         *\n         * In `verlet` integration, defaults to:\n         * `function (d, k) { return (k - d) / d * (k > d ? 1 : 0) }`\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         * @sample highcharts/series-networkgraph/cuboids/\n         *         Custom forces with Verlet integration\n         *\n         * @type      {Function}\n         * @default   function (d, k) { return k * k / d; }\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.repulsiveForce\n         */\n        /**\n         * Attraction force applied on a node which is conected to another\n         * node by a link. Passed are two arguments:\n         * - `d` - which is current distance between two nodes\n         * - `k` - which is desired distance between two nodes\n         *\n         * In `verlet` integration, defaults to:\n         * `function (d, k) { return (k - d) / d; }`\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         * @sample highcharts/series-networkgraph/cuboids/\n         *         Custom forces with Verlet integration\n         *\n         * @type      {Function}\n         * @default   function (d, k) { return k * k / d; }\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.attractiveForce\n         */\n        /**\n         * Ideal length (px) of the link between two nodes. When not\n         * defined, length is calculated as:\n         * `Math.pow(availableWidth * availableHeight / nodesLength, 0.4);`\n         *\n         * Note: Because of the algorithm specification, length of each link\n         * might be not exactly as specified.\n         *\n         * @sample highcharts/series-networkgraph/styled-links/\n         *         Numerical values\n         *\n         * @type      {number}\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.linkLength\n         */\n        /**\n         * Initial layout algorithm for positioning nodes. Can be one of\n         * built-in options (\"circle\", \"random\") or a function where\n         * positions should be set on each node (`this.nodes`) as\n         * `node.plotX` and `node.plotY`\n         *\n         * @sample highcharts/series-networkgraph/initial-positions/\n         *         Initial positions with callback\n         *\n         * @type {\"circle\"|\"random\"|Function}\n         */\n        initialPositions: 'circle',\n        /**\n         * When `initialPositions` are set to 'circle',\n         * `initialPositionRadius` is a distance from the center of circle,\n         * in which nodes are created.\n         *\n         * @type    {number}\n         * @default 1\n         * @since   7.1.0\n         */\n        initialPositionRadius: 1,\n        /**\n         * Experimental. Enables live simulation of the algorithm\n         * implementation. All nodes are animated as the forces applies on\n         * them.\n         *\n         * @sample highcharts/demo/network-graph/\n         *         Live simulation enabled\n         */\n        enableSimulation: false,\n        /**\n         * Barnes-Hut approximation only.\n         * Deteremines when distance between cell and node is small enough\n         * to calculate forces. Value of `theta` is compared directly with\n         * quotient `s / d`, where `s` is the size of the cell, and `d` is\n         * distance between center of cell's mass and currently compared\n         * node.\n         *\n         * @see [layoutAlgorithm.approximation](#series.networkgraph.layoutAlgorithm.approximation)\n         *\n         * @since 7.1.0\n         */\n        theta: 0.5,\n        /**\n         * Verlet integration only.\n         * Max speed that node can get in one iteration. In terms of\n         * simulation, it's a maximum translation (in pixels) that node can\n         * move (in both, x and y, dimensions). While `friction` is applied\n         * on all nodes, max speed is applied only for nodes that move very\n         * fast, for example small or disconnected ones.\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         * @see [layoutAlgorithm.friction](#series.networkgraph.layoutAlgorithm.friction)\n         *\n         * @since 7.1.0\n         */\n        maxSpeed: 10,\n        /**\n         * Approximation used to calculate repulsive forces affecting nodes.\n         * By default, when calculating net force, nodes are compared\n         * against each other, which gives O(N^2) complexity. Using\n         * Barnes-Hut approximation, we decrease this to O(N log N), but the\n         * resulting graph will have different layout. Barnes-Hut\n         * approximation divides space into rectangles via quad tree, where\n         * forces exerted on nodes are calculated directly for nearby cells,\n         * and for all others, cells are treated as a separate node with\n         * center of mass.\n         *\n         * @see [layoutAlgorithm.theta](#series.networkgraph.layoutAlgorithm.theta)\n         *\n         * @sample highcharts/series-networkgraph/barnes-hut-approximation/\n         *         A graph with Barnes-Hut approximation\n         *\n         * @type       {string}\n         * @validvalue [\"barnes-hut\", \"none\"]\n         * @since      7.1.0\n         */\n        approximation: 'none',\n        /**\n         * Type of the algorithm used when positioning nodes.\n         *\n         * @type       {string}\n         * @validvalue [\"reingold-fruchterman\"]\n         */\n        type: 'reingold-fruchterman',\n        /**\n         * Integration type. Available options are `'euler'` and `'verlet'`.\n         * Integration determines how forces are applied on particles. In\n         * Euler integration, force is applied direct as\n         * `newPosition += velocity;`.\n         * In Verlet integration, new position is based on a previous\n         * position without velocity:\n         * `newPosition += previousPosition - newPosition`.\n         *\n         * Note that different integrations give different results as forces\n         * are different.\n         *\n         * In Highcharts v7.0.x only `'euler'` integration was supported.\n         *\n         * @sample highcharts/series-networkgraph/integration-comparison/\n         *         Comparison of Verlet and Euler integrations\n         *\n         * @type       {string}\n         * @validvalue [\"euler\", \"verlet\"]\n         * @since      7.1.0\n         */\n        integration: 'euler',\n        /**\n         * Max number of iterations before algorithm will stop. In general,\n         * algorithm should find positions sooner, but when rendering huge\n         * number of nodes, it is recommended to increase this value as\n         * finding perfect graph positions can require more time.\n         */\n        maxIterations: 1000,\n        /**\n         * Gravitational const used in the barycenter force of the\n         * algorithm.\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         */\n        gravitationalConstant: 0.0625,\n        /**\n         * Friction applied on forces to prevent nodes rushing to fast to\n         * the desired positions.\n         */\n        friction: -0.981\n    },\n    showInLegend: false\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_NetworkgraphSeriesDefaults = (NetworkgraphSeriesDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after the simulation is ended and the layout is stable.\n *\n * @type      {Highcharts.NetworkgraphAfterSimulationCallbackFunction}\n * @product   highcharts\n * @apioption series.networkgraph.events.afterSimulation\n */\n/**\n * A `networkgraph` series. If the [type](#series.networkgraph.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.networkgraph\n * @excluding boostThreshold, animation, animationLimit, connectEnds,\n *            connectNulls, cropThreshold, dragDrop, getExtremesFromAll, label,\n *            linecap, negativeColor, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointStart, softThreshold, stack, stacking,\n *            step, threshold, xAxis, yAxis, zoneAxis, dataSorting,\n *            boostBlending\n * @product   highcharts\n * @requires  modules/networkgraph\n * @apioption series.networkgraph\n */\n/**\n * An array of data points for the series. For the `networkgraph` series type,\n * points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of\n * data points exceeds the series'\n * [turboThreshold](#series.area.turboThreshold), this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2'\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3'\n *     }]\n *  ```\n *\n * @type      {Array<Object|Array|number>}\n * @extends   series.line.data\n * @excluding drilldown,marker,x,y,dragDrop\n * @sample    {highcharts} highcharts/chart/reflow-true/\n *            Numerical values\n * @sample    {highcharts} highcharts/series/data-array-of-arrays/\n *            Arrays of numeric x and y\n * @sample    {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *            Arrays of datetime x and y\n * @sample    {highcharts} highcharts/series/data-array-of-name-value/\n *            Arrays of point.name and y\n * @sample    {highcharts} highcharts/series/data-array-of-objects/\n *            Config objects\n * @product   highcharts\n * @apioption series.networkgraph.data\n */\n/**\n * @type      {Highcharts.SeriesNetworkgraphDataLabelsOptionsObject|Array<Highcharts.SeriesNetworkgraphDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.networkgraph.data.dataLabels\n */\n/**\n * The node that the link runs from.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.data.from\n */\n/**\n * The node that the link runs to.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.data.to\n */\n/**\n * A collection of options for the individual nodes. The nodes in a\n * networkgraph diagram are auto-generated instances of `Highcharts.Point`,\n * but options can be applied here and linked by the `id`.\n *\n * @sample highcharts/series-networkgraph/data-options/\n *         Networkgraph diagram with node options\n *\n * @type      {Array<*>}\n * @product   highcharts\n * @apioption series.networkgraph.nodes\n */\n/**\n * The id of the auto-generated node, referring to the `from` or `to` setting of\n * the link.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.id\n */\n/**\n * The color of the auto generated node.\n *\n * @type      {Highcharts.ColorString}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.color\n */\n/**\n * The color index of the auto generated node, especially for use in styled\n * mode.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.colorIndex\n */\n/**\n * The name to display for the node in data labels and tooltips. Use this when\n * the name is different from the `id`. Where the id must be unique for each\n * node, this is not necessary for the name.\n *\n * @sample highcharts/series-networkgraph/data-options/\n *         Networkgraph diagram with node options\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.name\n */\n/**\n * Mass of the node. By default, each node has mass equal to it's marker radius\n * . Mass is used to determine how two connected nodes should affect\n * each other:\n *\n * Attractive force is multiplied by the ratio of two connected\n * nodes; if a big node has weights twice as the small one, then the small one\n * will move towards the big one twice faster than the big one to the small one\n * .\n *\n * @sample highcharts/series-networkgraph/ragdoll/\n *         Mass determined by marker.radius\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.mass\n */\n/**\n * Options for the node markers.\n *\n * @extends   plotOptions.networkgraph.marker\n * @apioption series.networkgraph.nodes.marker\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.networkgraph.dataLabels](#series.networkgraph.dataLabels).\n *\n * @type      {Highcharts.SeriesNetworkgraphDataLabelsOptionsObject|Array<Highcharts.SeriesNetworkgraphDataLabelsOptionsObject>}\n *\n * @apioption series.networkgraph.nodes.dataLabels\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/Series/Networkgraph/EulerIntegration.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Attractive force.\n *\n * In Euler integration, force is stored in a node, not changing it's\n * position. Later, in `integrate()` forces are applied on nodes.\n *\n * @private\n * @param {Highcharts.Point} link\n *        Link that connects two nodes\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n * @param {number} distanceR\n     */\nfunction attractive(link, force, distanceXY, distanceR) {\n    const massFactor = link.getMass(), translatedX = (distanceXY.x / distanceR) * force, translatedY = (distanceXY.y / distanceR) * force;\n    if (!link.fromNode.fixedPosition) {\n        link.fromNode.dispX -=\n            translatedX * massFactor.fromNode / link.fromNode.degree;\n        link.fromNode.dispY -=\n            translatedY * massFactor.fromNode / link.fromNode.degree;\n    }\n    if (!link.toNode.fixedPosition) {\n        link.toNode.dispX +=\n            translatedX * massFactor.toNode / link.toNode.degree;\n        link.toNode.dispY +=\n            translatedY * massFactor.toNode / link.toNode.degree;\n    }\n}\n/**\n * Attractive force function. Can be replaced by API's\n * `layoutAlgorithm.attractiveForce`\n *\n * Other forces that can be used:\n *\n * basic, not recommended:\n *    `function (d, k) { return d / k }`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction attractiveForceFunction(d, k) {\n    return d * d / k;\n}\n/**\n * Barycenter force. Calculate and applys barycenter forces on the\n * nodes. Making them closer to the center of their barycenter point.\n *\n * In Euler integration, force is stored in a node, not changing it's\n * position. Later, in `integrate()` forces are applied on nodes.\n *\n * @private\n */\nfunction barycenter() {\n    const gravitationalConstant = this.options.gravitationalConstant, xFactor = this.barycenter.xFactor, yFactor = this.barycenter.yFactor;\n    this.nodes.forEach(function (node) {\n        if (!node.fixedPosition) {\n            const degree = node.getDegree(), phi = degree * (1 + degree / 2);\n            node.dispX += ((xFactor - node.plotX) *\n                gravitationalConstant *\n                phi / node.degree);\n            node.dispY += ((yFactor - node.plotY) *\n                gravitationalConstant *\n                phi / node.degree);\n        }\n    });\n}\n/**\n * Estimate the best possible distance between two nodes, making graph\n * readable.\n * @private\n */\nfunction getK(layout) {\n    return Math.pow(layout.box.width * layout.box.height / layout.nodes.length, 0.3);\n}\n/**\n * Integration method.\n *\n * In Euler integration, force were stored in a node, not changing it's\n * position. Now, in the integrator method, we apply changes.\n *\n * Euler:\n *\n * Basic form: `x(n+1) = x(n) + v(n)`\n *\n * With Rengoild-Fruchterman we get:\n * `x(n+1) = x(n) + v(n) / length(v(n)) * min(v(n), temperature(n))`\n * where:\n * - `x(n+1)`: next position\n * - `x(n)`: current position\n * - `v(n)`: velocity (comes from net force)\n * - `temperature(n)`: current temperature\n *\n * Known issues:\n * Oscillations when force vector has the same magnitude but opposite\n * direction in the next step. Potentially solved by decreasing force by\n * `v * (1 / node.degree)`\n *\n * Note:\n * Actually `min(v(n), temperature(n))` replaces simulated annealing.\n *\n * @private\n * @param {Highcharts.NetworkgraphLayout} layout\n *        Layout object\n * @param {Highcharts.Point} node\n *        Node that should be translated\n */\nfunction integrate(layout, node) {\n    node.dispX +=\n        node.dispX * layout.options.friction;\n    node.dispY +=\n        node.dispY * layout.options.friction;\n    const distanceR = node.temperature = layout.vectorLength({\n        x: node.dispX,\n        y: node.dispY\n    });\n    if (distanceR !== 0) {\n        node.plotX += (node.dispX / distanceR *\n            Math.min(Math.abs(node.dispX), layout.temperature));\n        node.plotY += (node.dispY / distanceR *\n            Math.min(Math.abs(node.dispY), layout.temperature));\n    }\n}\n/**\n * Repulsive force.\n *\n * @private\n * @param {Highcharts.Point} node\n *        Node that should be translated by force.\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction repulsive(node, force, distanceXY, distanceR) {\n    node.dispX +=\n        (distanceXY.x / distanceR) * force / node.degree;\n    node.dispY +=\n        (distanceXY.y / distanceR) * force / node.degree;\n}\n/**\n * Repulsive force function. Can be replaced by API's\n * `layoutAlgorithm.repulsiveForce`.\n *\n * Other forces that can be used:\n *\n * basic, not recommended:\n *    `function (d, k) { return k / d }`\n *\n * standard:\n *    `function (d, k) { return k * k / d }`\n *\n * grid-variant:\n *    `function (d, k) { return k * k / d * (2 * k - d > 0 ? 1 : 0) }`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction repulsiveForceFunction(d, k) {\n    return k * k / d;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst EulerIntegration = {\n    attractive,\n    attractiveForceFunction,\n    barycenter,\n    getK,\n    integrate,\n    repulsive,\n    repulsiveForceFunction\n};\n/* harmony default export */ const Networkgraph_EulerIntegration = (EulerIntegration);\n\n;// ./code/es-modules/Series/Networkgraph/QuadTreeNode.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The QuadTree node class. Used in Networkgraph chart as a base for Barnes-Hut\n * approximation.\n *\n * @private\n * @class\n * @name Highcharts.QuadTreeNode\n *\n * @param {Highcharts.Dictionary<number>} box\n *        Available space for the node\n */\nclass QuadTreeNode {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(box) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        /**\n         * Read only. If QuadTreeNode is an external node, Point is stored in\n         * `this.body`.\n         *\n         * @name Highcharts.QuadTreeNode#body\n         * @type {boolean|Highcharts.Point}\n         */\n        this.body = false;\n        /**\n         * Read only. Internal nodes when created are empty to reserve the\n         * space. If Point is added to this QuadTreeNode, QuadTreeNode is no\n         * longer empty.\n         *\n         * @name Highcharts.QuadTreeNode#isEmpty\n         * @type {boolean}\n         */\n        this.isEmpty = false;\n        /**\n         * Read only. Flag to determine if QuadTreeNode is internal (and has\n         * subnodes with mass and central position) or external (bound to\n         * Point).\n         *\n         * @name Highcharts.QuadTreeNode#isInternal\n         * @type {boolean}\n         */\n        this.isInternal = false;\n        /**\n         * Read only. Array of subnodes. Empty if QuadTreeNode has just one\n         * Point. When added another Point to this QuadTreeNode, array is\n         * filled with four subnodes.\n         *\n         * @name Highcharts.QuadTreeNode#nodes\n         * @type {Array<Highcharts.QuadTreeNode>}\n         */\n        this.nodes = [];\n        /**\n         * Read only. The available space for node.\n         *\n         * @name Highcharts.QuadTreeNode#box\n         * @type {Highcharts.Dictionary<number>}\n         */\n        this.box = box;\n        /**\n         * Read only. The minium of width and height values.\n         *\n         * @name Highcharts.QuadTreeNode#boxSize\n         * @type {number}\n         */\n        this.boxSize = Math.min(box.width, box.height);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * When inserting another node into the box, that already hove one node,\n     * divide the available space into another four quadrants.\n     *\n     * Indexes of quadrants are:\n     * ```\n     * -------------               -------------\n     * |           |               |     |     |\n     * |           |               |  0  |  1  |\n     * |           |   divide()    |     |     |\n     * |     1     | ----------->  -------------\n     * |           |               |     |     |\n     * |           |               |  3  |  2  |\n     * |           |               |     |     |\n     * -------------               -------------\n     * ```\n     */\n    divideBox() {\n        const halfWidth = this.box.width / 2, halfHeight = this.box.height / 2;\n        // Top left\n        this.nodes[0] = new QuadTreeNode({\n            left: this.box.left,\n            top: this.box.top,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Top right\n        this.nodes[1] = new QuadTreeNode({\n            left: this.box.left + halfWidth,\n            top: this.box.top,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Bottom right\n        this.nodes[2] = new QuadTreeNode({\n            left: this.box.left + halfWidth,\n            top: this.box.top + halfHeight,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Bottom left\n        this.nodes[3] = new QuadTreeNode({\n            left: this.box.left,\n            top: this.box.top + halfHeight,\n            width: halfWidth,\n            height: halfHeight\n        });\n    }\n    /**\n     * Determine which of the quadrants should be used when placing node in\n     * the QuadTree. Returned index is always in range `< 0 , 3 >`.\n     * @private\n     */\n    getBoxPosition(point) {\n        const left = point.plotX < this.box.left + this.box.width / 2, top = point.plotY < this.box.top + this.box.height / 2;\n        let index;\n        if (left) {\n            if (top) {\n                // Top left\n                index = 0;\n            }\n            else {\n                // Bottom left\n                index = 3;\n            }\n        }\n        else {\n            if (top) {\n                // Top right\n                index = 1;\n            }\n            else {\n                // Bottom right\n                index = 2;\n            }\n        }\n        return index;\n    }\n    /**\n     * Insert recursively point(node) into the QuadTree. If the given\n     * quadrant is already occupied, divide it into smaller quadrants.\n     *\n     * @param {Highcharts.Point} point\n     *        Point/node to be inserted\n     * @param {number} depth\n     *        Max depth of the QuadTree\n     */\n    insert(point, depth) {\n        let newQuadTreeNode;\n        if (this.isInternal) {\n            // Internal node:\n            this.nodes[this.getBoxPosition(point)].insert(point, depth - 1);\n        }\n        else {\n            this.isEmpty = false;\n            if (!this.body) {\n                // First body in a quadrant:\n                this.isInternal = false;\n                this.body = point;\n            }\n            else {\n                if (depth) {\n                    // Every other body in a quadrant:\n                    this.isInternal = true;\n                    this.divideBox();\n                    // Reinsert main body only once:\n                    if (this.body !== true) {\n                        this.nodes[this.getBoxPosition(this.body)]\n                            .insert(this.body, depth - 1);\n                        this.body = true;\n                    }\n                    // Add second body:\n                    this.nodes[this.getBoxPosition(point)]\n                        .insert(point, depth - 1);\n                }\n                else {\n                    // We are below max allowed depth. That means either:\n                    // - really huge number of points\n                    // - falling two points into exactly the same position\n                    // In this case, create another node in the QuadTree.\n                    //\n                    // Alternatively we could add some noise to the\n                    // position, but that could result in different\n                    // rendered chart in exporting.\n                    newQuadTreeNode = new QuadTreeNode({\n                        top: point.plotX || NaN,\n                        left: point.plotY || NaN,\n                        // Width/height below 1px\n                        width: 0.1,\n                        height: 0.1\n                    });\n                    newQuadTreeNode.body = point;\n                    newQuadTreeNode.isInternal = false;\n                    this.nodes.push(newQuadTreeNode);\n                }\n            }\n        }\n    }\n    /**\n     * Each quad node requires it's mass and center position. That mass and\n     * position is used to imitate real node in the layout by approximation.\n     */\n    updateMassAndCenter() {\n        let mass = 0, plotX = 0, plotY = 0;\n        if (this.isInternal) {\n            // Calculate weightened mass of the quad node:\n            for (const pointMass of this.nodes) {\n                if (!pointMass.isEmpty) {\n                    mass += pointMass.mass;\n                    plotX += pointMass.plotX * pointMass.mass;\n                    plotY += pointMass.plotY * pointMass.mass;\n                }\n            }\n            plotX /= mass;\n            plotY /= mass;\n        }\n        else if (this.body) {\n            // Just one node, use coordinates directly:\n            mass = this.body.mass;\n            plotX = this.body.plotX;\n            plotY = this.body.plotY;\n        }\n        // Store details:\n        this.mass = mass;\n        this.plotX = plotX;\n        this.plotY = plotY;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_QuadTreeNode = (QuadTreeNode);\n\n;// ./code/es-modules/Series/Networkgraph/QuadTree.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The QuadTree class. Used in Networkgraph chart as a base for Barnes-Hut\n * approximation.\n *\n * @private\n * @class\n * @name Highcharts.QuadTree\n *\n * @param {number} x\n *        Left position of the plotting area\n * @param {number} y\n *        Top position of the plotting area\n * @param {number} width\n *        Width of the plotting area\n * @param {number} height\n *        Height of the plotting area\n */\nclass QuadTree {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(x, y, width, height) {\n        // Boundary rectangle:\n        this.box = {\n            left: x,\n            top: y,\n            width: width,\n            height: height\n        };\n        this.maxDepth = 25;\n        this.root = new Networkgraph_QuadTreeNode(this.box);\n        this.root.isInternal = true;\n        this.root.isRoot = true;\n        this.root.divideBox();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculate mass of the each QuadNode in the tree.\n     */\n    calculateMassAndCenter() {\n        this.visitNodeRecursive(null, null, function (node) {\n            node.updateMassAndCenter();\n        });\n    }\n    /**\n     * Insert nodes into the QuadTree\n     *\n     * @param {Array<Highcharts.Point>} points\n     *        Points as nodes\n     */\n    insertNodes(points) {\n        for (const point of points) {\n            this.root.insert(point, this.maxDepth);\n        }\n    }\n    /**\n     * Depth first treversal (DFS). Using `before` and `after` callbacks,\n     * we can get two results: preorder and postorder traversals, reminder:\n     *\n     * ```\n     *     (a)\n     *     / \\\n     *   (b) (c)\n     *   / \\\n     * (d) (e)\n     * ```\n     *\n     * DFS (preorder): `a -> b -> d -> e -> c`\n     *\n     * DFS (postorder): `d -> e -> b -> c -> a`\n     *\n     * @param {Highcharts.QuadTreeNode|null} node\n     *        QuadTree node\n     * @param {Function} [beforeCallback]\n     *        Function to be called before visiting children nodes.\n     * @param {Function} [afterCallback]\n     *        Function to be called after visiting children nodes.\n     */\n    visitNodeRecursive(node, beforeCallback, afterCallback) {\n        let goFurther;\n        if (!node) {\n            node = this.root;\n        }\n        if (node === this.root && beforeCallback) {\n            goFurther = beforeCallback(node);\n        }\n        if (goFurther === false) {\n            return;\n        }\n        for (const qtNode of node.nodes) {\n            if (qtNode.isInternal) {\n                if (beforeCallback) {\n                    goFurther = beforeCallback(qtNode);\n                }\n                if (goFurther === false) {\n                    continue;\n                }\n                this.visitNodeRecursive(qtNode, beforeCallback, afterCallback);\n            }\n            else if (qtNode.body) {\n                if (beforeCallback) {\n                    beforeCallback(qtNode.body);\n                }\n            }\n            if (afterCallback) {\n                afterCallback(qtNode);\n            }\n        }\n        if (node === this.root && afterCallback) {\n            afterCallback(node);\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_QuadTree = (QuadTree);\n\n;// ./code/es-modules/Series/Networkgraph/VerletIntegration.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Attractive force.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n * @param {Highcharts.Point} link\n *        Link that connects two nodes\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction VerletIntegration_attractive(link, force, distanceXY) {\n    const massFactor = link.getMass(), translatedX = -distanceXY.x * force * this.diffTemperature, translatedY = -distanceXY.y * force * this.diffTemperature;\n    if (!link.fromNode.fixedPosition) {\n        link.fromNode.plotX -=\n            translatedX * massFactor.fromNode / link.fromNode.degree;\n        link.fromNode.plotY -=\n            translatedY * massFactor.fromNode / link.fromNode.degree;\n    }\n    if (!link.toNode.fixedPosition) {\n        link.toNode.plotX +=\n            translatedX * massFactor.toNode / link.toNode.degree;\n        link.toNode.plotY +=\n            translatedY * massFactor.toNode / link.toNode.degree;\n    }\n}\n/**\n * Attractive force function. Can be replaced by API's\n * `layoutAlgorithm.attractiveForce`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction VerletIntegration_attractiveForceFunction(d, k) {\n    // Used in API:\n    return (k - d) / d;\n}\n/**\n * Barycenter force. Calculate and applys barycenter forces on the\n * nodes. Making them closer to the center of their barycenter point.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n */\nfunction VerletIntegration_barycenter() {\n    const gravitationalConstant = this.options.gravitationalConstant || 0, xFactor = (this.barycenter.xFactor -\n        (this.box.left + this.box.width) / 2) * gravitationalConstant, yFactor = (this.barycenter.yFactor -\n        (this.box.top + this.box.height) / 2) * gravitationalConstant;\n    this.nodes.forEach(function (node) {\n        if (!node.fixedPosition) {\n            node.plotX -=\n                xFactor / node.mass / node.degree;\n            node.plotY -=\n                yFactor / node.mass / node.degree;\n        }\n    });\n}\n/**\n * Estiamte the best possible distance between two nodes, making graph\n * readable.\n * @private\n */\nfunction VerletIntegration_getK(layout) {\n    return Math.pow(layout.box.width * layout.box.height / layout.nodes.length, 0.5);\n}\n/**\n * Integration method.\n *\n * In Verlet integration, forces are applied on node immediately to it's\n * `plotX` and `plotY` position.\n *\n * Verlet without velocity:\n *\n *    x(n+1) = 2 * x(n) - x(n-1) + A(T) * deltaT ^ 2\n *\n * where:\n *     - x(n+1) - new position\n *     - x(n) - current position\n *     - x(n-1) - previous position\n *\n * Assuming A(t) = 0 (no acceleration) and (deltaT = 1) we get:\n *\n *     x(n+1) = x(n) + (x(n) - x(n-1))\n *\n * where:\n *     - (x(n) - x(n-1)) - position change\n *\n * TO DO:\n * Consider Verlet with velocity to support additional\n * forces. Or even Time-Corrected Verlet by Jonathan\n * \"lonesock\" Dummer\n *\n * @private\n * @param {Highcharts.NetworkgraphLayout} layout layout object\n * @param {Highcharts.Point} node node that should be translated\n */\nfunction VerletIntegration_integrate(layout, node) {\n    const friction = -layout.options.friction, maxSpeed = layout.options.maxSpeed, prevX = node.prevX, prevY = node.prevY, \n    // Apply friction:\n    frictionX = ((node.plotX + node.dispX -\n        prevX) * friction), frictionY = ((node.plotY + node.dispY -\n        prevY) * friction), abs = Math.abs, signX = abs(frictionX) / (frictionX || 1), // Need to deal with 0\n    signY = abs(frictionY) / (frictionY || 1), \n    // Apply max speed:\n    diffX = signX * Math.min(maxSpeed, Math.abs(frictionX)), diffY = signY * Math.min(maxSpeed, Math.abs(frictionY));\n    // Store for the next iteration:\n    node.prevX = node.plotX + node.dispX;\n    node.prevY = node.plotY + node.dispY;\n    // Update positions:\n    node.plotX += diffX;\n    node.plotY += diffY;\n    node.temperature = layout.vectorLength({\n        x: diffX,\n        y: diffY\n    });\n}\n/**\n * Repulsive force.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n * @param {Highcharts.Point} node\n *        Node that should be translated by force.\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction VerletIntegration_repulsive(node, force, distanceXY) {\n    const factor = force * this.diffTemperature / node.mass / node.degree;\n    if (!node.fixedPosition) {\n        node.plotX += distanceXY.x * factor;\n        node.plotY += distanceXY.y * factor;\n    }\n}\n/**\n * Repulsive force function. Can be replaced by API's\n * `layoutAlgorithm.repulsiveForce`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction VerletIntegration_repulsiveForceFunction(d, k) {\n    // Used in API:\n    return (k - d) / d * (k > d ? 1 : 0); // Force only for close nodes\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst VerletIntegration = {\n    attractive: VerletIntegration_attractive,\n    attractiveForceFunction: VerletIntegration_attractiveForceFunction,\n    barycenter: VerletIntegration_barycenter,\n    getK: VerletIntegration_getK,\n    integrate: VerletIntegration_integrate,\n    repulsive: VerletIntegration_repulsive,\n    repulsiveForceFunction: VerletIntegration_repulsiveForceFunction\n};\n/* harmony default export */ const Networkgraph_VerletIntegration = (VerletIntegration);\n\n;// ./code/es-modules/Series/Networkgraph/ReingoldFruchtermanLayout.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { win } = (external_highcharts_src_js_default_default());\n\n\n\nconst { clamp, defined: ReingoldFruchtermanLayout_defined, isFunction, fireEvent, pick: ReingoldFruchtermanLayout_pick } = (external_highcharts_src_js_default_default());\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Reingold-Fruchterman algorithm from\n * \"Graph Drawing by Force-directed Placement\" paper.\n * @private\n */\nclass ReingoldFruchtermanLayout {\n    constructor() {\n        /* *\n         *\n         *  Static Functions\n         *\n         * */\n        this.box = {};\n        this.currentStep = 0;\n        this.initialRendering = true;\n        this.links = [];\n        this.nodes = [];\n        this.series = [];\n        this.simulation = false;\n    }\n    static compose(ChartClass) {\n        Series_GraphLayoutComposition.compose(ChartClass);\n        Series_GraphLayoutComposition.integrations.euler = Networkgraph_EulerIntegration;\n        Series_GraphLayoutComposition.integrations.verlet = Networkgraph_VerletIntegration;\n        Series_GraphLayoutComposition.layouts['reingold-fruchterman'] =\n            ReingoldFruchtermanLayout;\n    }\n    init(options) {\n        this.options = options;\n        this.nodes = [];\n        this.links = [];\n        this.series = [];\n        this.box = {\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0\n        };\n        this.setInitialRendering(true);\n        this.integration =\n            Series_GraphLayoutComposition.integrations[options.integration];\n        this.enableSimulation = options.enableSimulation;\n        this.attractiveForce = ReingoldFruchtermanLayout_pick(options.attractiveForce, this.integration.attractiveForceFunction);\n        this.repulsiveForce = ReingoldFruchtermanLayout_pick(options.repulsiveForce, this.integration.repulsiveForceFunction);\n        this.approximation = options.approximation;\n    }\n    updateSimulation(enable) {\n        this.enableSimulation = ReingoldFruchtermanLayout_pick(enable, this.options.enableSimulation);\n    }\n    start() {\n        const layout = this, series = this.series, options = this.options;\n        layout.currentStep = 0;\n        layout.forces = series[0] && series[0].forces || [];\n        layout.chart = series[0] && series[0].chart;\n        if (layout.initialRendering) {\n            layout.initPositions();\n            // Render elements in initial positions:\n            series.forEach(function (s) {\n                s.finishedAnimating = true; // #13169\n                s.render();\n            });\n        }\n        layout.setK();\n        layout.resetSimulation(options);\n        if (layout.enableSimulation) {\n            layout.step();\n        }\n    }\n    step() {\n        const anyLayout = this, allSeries = this.series;\n        // Algorithm:\n        this.currentStep++;\n        if (this.approximation === 'barnes-hut') {\n            this.createQuadTree();\n            this.quadTree.calculateMassAndCenter();\n        }\n        for (const forceName of this.forces || []) {\n            anyLayout[forceName + 'Forces'](this.temperature);\n        }\n        // Limit to the plotting area and cool down:\n        this.applyLimits();\n        // Cool down the system:\n        this.temperature = this.coolDown(this.startTemperature, this.diffTemperature, this.currentStep);\n        this.prevSystemTemperature = this.systemTemperature;\n        this.systemTemperature = this.getSystemTemperature();\n        if (this.enableSimulation) {\n            for (const series of allSeries) {\n                // Chart could be destroyed during the simulation\n                if (series.chart) {\n                    series.render();\n                }\n            }\n            if (this.maxIterations-- &&\n                isFinite(this.temperature) &&\n                !this.isStable()) {\n                if (this.simulation) {\n                    win.cancelAnimationFrame(this.simulation);\n                }\n                this.simulation = win.requestAnimationFrame(() => this.step());\n            }\n            else {\n                this.simulation = false;\n                this.series.forEach((s) => {\n                    fireEvent(s, 'afterSimulation');\n                });\n            }\n        }\n    }\n    stop() {\n        if (this.simulation) {\n            win.cancelAnimationFrame(this.simulation);\n        }\n    }\n    setArea(x, y, w, h) {\n        this.box = {\n            left: x,\n            top: y,\n            width: w,\n            height: h\n        };\n    }\n    setK() {\n        // Optimal distance between nodes,\n        // available space around the node:\n        this.k = this.options.linkLength || this.integration.getK(this);\n    }\n    addElementsToCollection(elements, collection) {\n        for (const element of elements) {\n            if (collection.indexOf(element) === -1) {\n                collection.push(element);\n            }\n        }\n    }\n    removeElementFromCollection(element, collection) {\n        const index = collection.indexOf(element);\n        if (index !== -1) {\n            collection.splice(index, 1);\n        }\n    }\n    clear() {\n        this.nodes.length = 0;\n        this.links.length = 0;\n        this.series.length = 0;\n        this.resetSimulation();\n    }\n    resetSimulation() {\n        this.forcedStop = false;\n        this.systemTemperature = 0;\n        this.setMaxIterations();\n        this.setTemperature();\n        this.setDiffTemperature();\n    }\n    restartSimulation() {\n        if (!this.simulation) {\n            // When dragging nodes, we don't need to calculate\n            // initial positions and rendering nodes:\n            this.setInitialRendering(false);\n            // Start new simulation:\n            if (!this.enableSimulation) {\n                // Run only one iteration to speed things up:\n                this.setMaxIterations(1);\n            }\n            else {\n                this.start();\n            }\n            if (this.chart) {\n                this.chart.redraw();\n            }\n            // Restore defaults:\n            this.setInitialRendering(true);\n        }\n        else {\n            // Extend current simulation:\n            this.resetSimulation();\n        }\n    }\n    setMaxIterations(maxIterations) {\n        this.maxIterations = ReingoldFruchtermanLayout_pick(maxIterations, this.options.maxIterations);\n    }\n    setTemperature() {\n        this.temperature = this.startTemperature =\n            Math.sqrt(this.nodes.length);\n    }\n    setDiffTemperature() {\n        this.diffTemperature = this.startTemperature /\n            (this.options.maxIterations + 1);\n    }\n    setInitialRendering(enable) {\n        this.initialRendering = enable;\n    }\n    createQuadTree() {\n        this.quadTree = new Networkgraph_QuadTree(this.box.left, this.box.top, this.box.width, this.box.height);\n        this.quadTree.insertNodes(this.nodes);\n    }\n    initPositions() {\n        const initialPositions = this.options.initialPositions;\n        if (isFunction(initialPositions)) {\n            initialPositions.call(this);\n            for (const node of this.nodes) {\n                if (!ReingoldFruchtermanLayout_defined(node.prevX)) {\n                    node.prevX = node.plotX;\n                }\n                if (!ReingoldFruchtermanLayout_defined(node.prevY)) {\n                    node.prevY = node.plotY;\n                }\n                node.dispX = 0;\n                node.dispY = 0;\n            }\n        }\n        else if (initialPositions === 'circle') {\n            this.setCircularPositions();\n        }\n        else {\n            this.setRandomPositions();\n        }\n    }\n    setCircularPositions() {\n        const box = this.box, nodes = this.nodes, nodesLength = nodes.length + 1, angle = 2 * Math.PI / nodesLength, rootNodes = nodes.filter(function (node) {\n            return node.linksTo.length === 0;\n        }), visitedNodes = {}, radius = this.options.initialPositionRadius, addToNodes = (node) => {\n            for (const link of node.linksFrom || []) {\n                if (!visitedNodes[link.toNode.id]) {\n                    visitedNodes[link.toNode.id] = true;\n                    sortedNodes.push(link.toNode);\n                    addToNodes(link.toNode);\n                }\n            }\n        };\n        let sortedNodes = [];\n        // Start with identified root nodes an sort the nodes by their\n        // hierarchy. In trees, this ensures that branches don't cross\n        // eachother.\n        for (const rootNode of rootNodes) {\n            sortedNodes.push(rootNode);\n            addToNodes(rootNode);\n        }\n        // Cyclic tree, no root node found\n        if (!sortedNodes.length) {\n            sortedNodes = nodes;\n            // Dangling, cyclic trees\n        }\n        else {\n            for (const node of nodes) {\n                if (sortedNodes.indexOf(node) === -1) {\n                    sortedNodes.push(node);\n                }\n            }\n        }\n        let node;\n        // Initial positions are laid out along a small circle, appearing\n        // as a cluster in the middle\n        for (let i = 0, iEnd = sortedNodes.length; i < iEnd; ++i) {\n            node = sortedNodes[i];\n            node.plotX = node.prevX = ReingoldFruchtermanLayout_pick(node.plotX, box.width / 2 + radius * Math.cos(i * angle));\n            node.plotY = node.prevY = ReingoldFruchtermanLayout_pick(node.plotY, box.height / 2 + radius * Math.sin(i * angle));\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    }\n    setRandomPositions() {\n        const box = this.box, nodes = this.nodes, nodesLength = nodes.length + 1, \n        /**\n         * Return a repeatable, quasi-random number based on an integer\n         * input. For the initial positions\n         * @private\n         */\n        unrandom = (n) => {\n            let rand = n * n / Math.PI;\n            rand = rand - Math.floor(rand);\n            return rand;\n        };\n        let node;\n        // Initial positions:\n        for (let i = 0, iEnd = nodes.length; i < iEnd; ++i) {\n            node = nodes[i];\n            node.plotX = node.prevX = ReingoldFruchtermanLayout_pick(node.plotX, box.width * unrandom(i));\n            node.plotY = node.prevY = ReingoldFruchtermanLayout_pick(node.plotY, box.height * unrandom(nodesLength + i));\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    }\n    force(name, ...args) {\n        this.integration[name].apply(this, args);\n    }\n    barycenterForces() {\n        this.getBarycenter();\n        this.force('barycenter');\n    }\n    getBarycenter() {\n        let systemMass = 0, cx = 0, cy = 0;\n        for (const node of this.nodes) {\n            cx += node.plotX * node.mass;\n            cy += node.plotY * node.mass;\n            systemMass += node.mass;\n        }\n        this.barycenter = {\n            x: cx,\n            y: cy,\n            xFactor: cx / systemMass,\n            yFactor: cy / systemMass\n        };\n        return this.barycenter;\n    }\n    barnesHutApproximation(node, quadNode) {\n        const distanceXY = this.getDistXY(node, quadNode), distanceR = this.vectorLength(distanceXY);\n        let goDeeper, force;\n        if (node !== quadNode && distanceR !== 0) {\n            if (quadNode.isInternal) {\n                // Internal node:\n                if (quadNode.boxSize / distanceR <\n                    this.options.theta &&\n                    distanceR !== 0) {\n                    // Treat as an external node:\n                    force = this.repulsiveForce(distanceR, this.k);\n                    this.force('repulsive', node, force * quadNode.mass, distanceXY, distanceR);\n                    goDeeper = false;\n                }\n                else {\n                    // Go deeper:\n                    goDeeper = true;\n                }\n            }\n            else {\n                // External node, direct force:\n                force = this.repulsiveForce(distanceR, this.k);\n                this.force('repulsive', node, force * quadNode.mass, distanceXY, distanceR);\n            }\n        }\n        return goDeeper;\n    }\n    repulsiveForces() {\n        if (this.approximation === 'barnes-hut') {\n            for (const node of this.nodes) {\n                this.quadTree.visitNodeRecursive(null, (quadNode) => (this.barnesHutApproximation(node, quadNode)));\n            }\n        }\n        else {\n            let force, distanceR, distanceXY;\n            for (const node of this.nodes) {\n                for (const repNode of this.nodes) {\n                    if (\n                    // Node cannot repulse itself:\n                    node !== repNode &&\n                        // Only close nodes affect each other:\n                        // layout.getDistR(node, repNode) < 2 * k &&\n                        // Not dragged:\n                        !node.fixedPosition) {\n                        distanceXY = this.getDistXY(node, repNode);\n                        distanceR = this.vectorLength(distanceXY);\n                        if (distanceR !== 0) {\n                            force = this.repulsiveForce(distanceR, this.k);\n                            this.force('repulsive', node, force * repNode.mass, distanceXY, distanceR);\n                        }\n                    }\n                }\n            }\n        }\n    }\n    attractiveForces() {\n        let distanceXY, distanceR, force;\n        for (const link of this.links) {\n            if (link.fromNode && link.toNode) {\n                distanceXY = this.getDistXY(link.fromNode, link.toNode);\n                distanceR = this.vectorLength(distanceXY);\n                if (distanceR !== 0) {\n                    force = this.attractiveForce(distanceR, this.k);\n                    this.force('attractive', link, force, distanceXY, distanceR);\n                }\n            }\n        }\n    }\n    applyLimits() {\n        const nodes = this.nodes;\n        for (const node of nodes) {\n            if (node.fixedPosition) {\n                continue;\n            }\n            this.integration.integrate(this, node);\n            this.applyLimitBox(node, this.box);\n            // Reset displacement:\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    }\n    /**\n     * External box that nodes should fall. When hitting an edge, node\n     * should stop or bounce.\n     * @private\n     */\n    applyLimitBox(node, box) {\n        const radius = node.radius;\n        /*\n        TO DO: Consider elastic collision instead of stopping.\n        o' means end position when hitting plotting area edge:\n\n        - \"inelastic\":\n        o\n            \\\n        ______\n        |  o'\n        |   \\\n        |    \\\n\n        - \"elastic\"/\"bounced\":\n        o\n            \\\n        ______\n        |  ^\n        | / \\\n        |o'  \\\n\n        Euler sample:\n        if (plotX < 0) {\n            plotX = 0;\n            dispX *= -1;\n        }\n\n        if (plotX > box.width) {\n            plotX = box.width;\n            dispX *= -1;\n        }\n\n        */\n        // Limit X-coordinates:\n        node.plotX = clamp(node.plotX, box.left + radius, box.width - radius);\n        // Limit Y-coordinates:\n        node.plotY = clamp(node.plotY, box.top + radius, box.height - radius);\n    }\n    /**\n     * From \"A comparison of simulated annealing cooling strategies\" by\n     * Nourani and Andresen work.\n     * @private\n     */\n    coolDown(temperature, temperatureStep, currentStep) {\n        // Logarithmic:\n        /*\n        return Math.sqrt(this.nodes.length) -\n            Math.log(\n                currentStep * layout.diffTemperature\n            );\n        */\n        // Exponential:\n        /*\n        let alpha = 0.1;\n        layout.temperature = Math.sqrt(layout.nodes.length) *\n            Math.pow(alpha, layout.diffTemperature);\n        */\n        // Linear:\n        return temperature - temperatureStep * currentStep;\n    }\n    isStable() {\n        return Math.abs(this.systemTemperature -\n            this.prevSystemTemperature) < 0.00001 || this.temperature <= 0;\n    }\n    getSystemTemperature() {\n        let value = 0;\n        for (const node of this.nodes) {\n            value += node.temperature;\n        }\n        return value;\n    }\n    vectorLength(vector) {\n        return Math.sqrt(vector.x * vector.x + vector.y * vector.y);\n    }\n    getDistR(nodeA, nodeB) {\n        const distance = this.getDistXY(nodeA, nodeB);\n        return this.vectorLength(distance);\n    }\n    getDistXY(nodeA, nodeB) {\n        const xDist = nodeA.plotX - nodeB.plotX, yDist = nodeA.plotY - nodeB.plotY;\n        return {\n            x: xDist,\n            y: yDist,\n            absX: Math.abs(xDist),\n            absY: Math.abs(yDist)\n        };\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_ReingoldFruchtermanLayout = (ReingoldFruchtermanLayout);\n\n;// ./code/es-modules/Series/SimulationSeriesUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { merge: SimulationSeriesUtilities_merge, syncTimeout } = (external_highcharts_src_js_default_default());\n\nconst { animObject } = (external_highcharts_src_js_default_default());\n/**\n * Create a setTimeout for the first drawDataLabels()\n * based on the dataLabels.animation.defer value\n * for series which have enabled simulation.\n * @private\n */\nfunction initDataLabelsDefer() {\n    const dlOptions = this.options.dataLabels;\n    // Method drawDataLabels() fires for the first time after\n    // dataLabels.animation.defer time unless\n    // the dataLabels.animation = false or dataLabels.defer = false\n    // or if the simulation is disabled\n    if (!dlOptions?.defer ||\n        !this.options.layoutAlgorithm?.enableSimulation) {\n        this.deferDataLabels = false;\n    }\n    else {\n        syncTimeout(() => {\n            this.deferDataLabels = false;\n        }, dlOptions ? animObject(dlOptions.animation).defer : 0);\n    }\n}\n/**\n * Initialize the SVG group for the DataLabels with correct opacities\n * and correct styles so that the animation for the series that have\n * simulation enabled works fine.\n * @private\n */\nfunction initDataLabels() {\n    const series = this, dlOptions = series.options.dataLabels;\n    if (!series.dataLabelsGroup) {\n        const dataLabelsGroup = this.initDataLabelsGroup();\n        // Apply the dataLabels.style not only to the\n        // individual dataLabels but also to the entire group\n        if (!series.chart.styledMode && dlOptions?.style) {\n            dataLabelsGroup.css(dlOptions.style);\n        }\n        // Initialize the opacity of the group to 0 (start of animation)\n        dataLabelsGroup.attr({ opacity: 0 });\n        if (series.visible) { // #2597, #3023, #3024\n            dataLabelsGroup.show();\n        }\n        return dataLabelsGroup;\n    }\n    // Place it on first and subsequent (redraw) calls\n    series.dataLabelsGroup.attr(SimulationSeriesUtilities_merge({ opacity: 1 }, this.getPlotBox('data-labels')));\n    return series.dataLabelsGroup;\n}\nconst DataLabelsDeferUtils = {\n    initDataLabels,\n    initDataLabelsDefer\n};\n/* harmony default export */ const SimulationSeriesUtilities = (DataLabelsDeferUtils);\n\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (external_highcharts_src_js_default_default());\nconst { addEvent: TextPath_addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (external_highcharts_src_js_default_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = TextPath_addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction TextPath_compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose: TextPath_compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Networkgraph/NetworkgraphSeries.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { noop } = (external_highcharts_src_js_default_default());\n\n\n\n\n\nconst { series: Series, seriesTypes: { column: { prototype: columnProto }, line: { prototype: lineProto } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { initDataLabels: NetworkgraphSeries_initDataLabels, initDataLabelsDefer: NetworkgraphSeries_initDataLabelsDefer } = SimulationSeriesUtilities;\n\nconst { addEvent: NetworkgraphSeries_addEvent, defined: NetworkgraphSeries_defined, extend: NetworkgraphSeries_extend, merge: NetworkgraphSeries_merge, pick: NetworkgraphSeries_pick } = (external_highcharts_src_js_default_default());\n\nExtensions_TextPath.compose((external_highcharts_src_js_default_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.networkgraph\n *\n * @extends Highcharts.Series\n */\nclass NetworkgraphSeries extends Series {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.deferDataLabels = true;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass) {\n        Series_DragNodesComposition.compose(ChartClass);\n        Networkgraph_ReingoldFruchtermanLayout.compose(ChartClass);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Defer the layout.\n     * Each series first registers all nodes and links, then layout\n     * calculates all nodes positions and calls `series.render()` in every\n     * simulation step.\n     *\n     * Note:\n     * Animation is done through `requestAnimationFrame` directly, without\n     * `Highcharts.animate()` use.\n     * @private\n     */\n    deferLayout() {\n        const layoutOptions = this.options.layoutAlgorithm, chartOptions = this.chart.options.chart;\n        let layout, graphLayoutsStorage = this.chart.graphLayoutsStorage, graphLayoutsLookup = this.chart.graphLayoutsLookup;\n        if (!this.visible) {\n            return;\n        }\n        if (!graphLayoutsStorage) {\n            this.chart.graphLayoutsStorage = graphLayoutsStorage = {};\n            this.chart.graphLayoutsLookup = graphLayoutsLookup = [];\n        }\n        layout = graphLayoutsStorage[layoutOptions.type];\n        if (!layout) {\n            layoutOptions.enableSimulation =\n                !NetworkgraphSeries_defined(chartOptions.forExport) ?\n                    layoutOptions.enableSimulation :\n                    !chartOptions.forExport;\n            graphLayoutsStorage[layoutOptions.type] = layout =\n                new Series_GraphLayoutComposition.layouts[layoutOptions.type]();\n            layout.init(layoutOptions);\n            graphLayoutsLookup.splice(layout.index, 0, layout);\n        }\n        this.layout = layout;\n        layout.setArea(0, 0, this.chart.plotWidth, this.chart.plotHeight);\n        layout.addElementsToCollection([this], layout.series);\n        layout.addElementsToCollection(this.nodes, layout.nodes);\n        layout.addElementsToCollection(this.points, layout.links);\n    }\n    /**\n     * @private\n     */\n    destroy() {\n        if (this.layout) {\n            this.layout.removeElementFromCollection(this, this.layout.series);\n        }\n        Series_NodesComposition.destroy.call(this);\n    }\n    /**\n     * Networkgraph has two separate collections of nodes and lines, render\n     * dataLabels for both sets:\n     * @private\n     */\n    drawDataLabels() {\n        // We defer drawing the dataLabels\n        // until dataLabels.animation.defer time passes\n        if (this.deferDataLabels) {\n            return;\n        }\n        const dlOptions = this.options.dataLabels;\n        let textPath;\n        if (dlOptions?.textPath) {\n            textPath = dlOptions.textPath;\n        }\n        // Render node labels:\n        Series.prototype.drawDataLabels.call(this, this.nodes);\n        // Render link labels:\n        if (dlOptions?.linkTextPath) {\n            // If linkTextPath is set, render link labels with linkTextPath\n            dlOptions.textPath = dlOptions.linkTextPath;\n        }\n        Series.prototype.drawDataLabels.call(this, this.data);\n        // Go back to textPath for nodes\n        if (dlOptions?.textPath) {\n            dlOptions.textPath = textPath;\n        }\n    }\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects\n     * but pushed to the this.nodes array.\n     * @private\n     */\n    generatePoints() {\n        let node, i;\n        Series_NodesComposition.generatePoints.apply(this, arguments);\n        // In networkgraph, it's fine to define standalone nodes, create\n        // them:\n        if (this.options.nodes) {\n            this.options.nodes.forEach(function (nodeOptions) {\n                if (!this.nodeLookup[nodeOptions.id]) {\n                    this.nodeLookup[nodeOptions.id] =\n                        this.createNode(nodeOptions.id);\n                }\n            }, this);\n        }\n        for (i = this.nodes.length - 1; i >= 0; i--) {\n            node = this.nodes[i];\n            node.degree = node.getDegree();\n            node.radius = NetworkgraphSeries_pick(node.marker && node.marker.radius, this.options.marker && this.options.marker.radius, 0);\n            node.key = node.name;\n            // If node exists, but it's not available in nodeLookup,\n            // then it's leftover from previous runs (e.g. setData)\n            if (!this.nodeLookup[node.id]) {\n                node.remove();\n            }\n        }\n        this.data.forEach(function (link) {\n            link.formatPrefix = 'link';\n        });\n        this.indexateNodes();\n    }\n    /**\n     * In networkgraph, series.points refers to links,\n     * but series.nodes refers to actual points.\n     * @private\n     */\n    getPointsCollection() {\n        return this.nodes || [];\n    }\n    /**\n     * Set index for each node. Required for proper `node.update()`.\n     * Note that links are indexated out of the box in `generatePoints()`.\n     *\n     * @private\n     */\n    indexateNodes() {\n        this.nodes.forEach(function (node, index) {\n            node.index = index;\n        });\n    }\n    /**\n     * Extend init with base event, which should stop simulation during\n     * update. After data is updated, `chart.render` resumes the simulation.\n     * @private\n     */\n    init(chart, options) {\n        super.init(chart, options);\n        NetworkgraphSeries_initDataLabelsDefer.call(this);\n        NetworkgraphSeries_addEvent(this, 'updatedData', () => {\n            if (this.layout) {\n                this.layout.stop();\n            }\n        });\n        NetworkgraphSeries_addEvent(this, 'afterUpdate', () => {\n            this.nodes.forEach((node) => {\n                if (node && node.series) {\n                    node.resolveColor();\n                }\n            });\n        });\n        // If the dataLabels.animation.defer time is longer than\n        // the time it takes for the layout to become stable then\n        // drawDataLabels would never be called (that's why we force it here)\n        NetworkgraphSeries_addEvent(this, 'afterSimulation', function () {\n            this.deferDataLabels = false;\n            this.drawDataLabels();\n        });\n        return this;\n    }\n    /**\n     * Extend the default marker attribs by using a non-rounded X position,\n     * otherwise the nodes will jump from pixel to pixel which looks a bit\n     * jaggy when approaching equilibrium.\n     * @private\n     */\n    markerAttribs(point, state) {\n        const attribs = Series.prototype.markerAttribs.call(this, point, state);\n        // Series.render() is called before initial positions are set:\n        if (!NetworkgraphSeries_defined(point.plotY)) {\n            attribs.y = 0;\n        }\n        attribs.x = (point.plotX || 0) - (attribs.width || 0) / 2;\n        return attribs;\n    }\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        // By default, only `selected` state is passed on\n        const pointState = state || point && point.state || 'normal', stateOptions = this.options.states[pointState];\n        let attribs = Series.prototype.pointAttribs.call(this, point, pointState);\n        if (point && !point.isNode) {\n            attribs = point.getLinkAttributes();\n            // For link, get prefixed names:\n            if (stateOptions) {\n                attribs = {\n                    // TO DO: API?\n                    stroke: stateOptions.linkColor || attribs.stroke,\n                    dashstyle: (stateOptions.linkDashStyle || attribs.dashstyle),\n                    opacity: NetworkgraphSeries_pick(stateOptions.linkOpacity, attribs.opacity),\n                    'stroke-width': stateOptions.linkColor ||\n                        attribs['stroke-width']\n                };\n            }\n        }\n        return attribs;\n    }\n    /**\n     * Extend the render function to also render this.nodes together with\n     * the points.\n     * @private\n     */\n    render() {\n        const series = this, points = series.points, hoverPoint = series.chart.hoverPoint, dataLabels = [];\n        // Render markers:\n        series.points = series.nodes;\n        lineProto.render.call(this);\n        series.points = points;\n        points.forEach(function (point) {\n            if (point.fromNode && point.toNode) {\n                point.renderLink();\n                point.redrawLink();\n            }\n        });\n        if (hoverPoint && hoverPoint.series === series) {\n            series.redrawHalo(hoverPoint);\n        }\n        if (series.chart.hasRendered &&\n            !series.options.dataLabels.allowOverlap) {\n            series.nodes.concat(series.points).forEach(function (node) {\n                if (node.dataLabel) {\n                    dataLabels.push(node.dataLabel);\n                }\n            });\n            series.chart.hideOverlappingLabels(dataLabels);\n        }\n    }\n    /**\n     * When state should be passed down to all points, concat nodes and\n     * links and apply this state to all of them.\n     * @private\n     */\n    setState(state, inherit) {\n        if (inherit) {\n            this.points = this.nodes.concat(this.data);\n            Series.prototype.setState.apply(this, arguments);\n            this.points = this.data;\n        }\n        else {\n            Series.prototype.setState.apply(this, arguments);\n        }\n        // If simulation is done, re-render points with new states:\n        if (!this.layout.simulation && !state) {\n            this.render();\n        }\n    }\n    /**\n     * Run pre-translation and register nodes&links to the deffered layout.\n     * @private\n     */\n    translate() {\n        this.generatePoints();\n        this.deferLayout();\n        this.nodes.forEach(function (node) {\n            // Draw the links from this node\n            node.isInside = true;\n            node.linksFrom.forEach(function (point) {\n                point.shapeType = 'path';\n                // Pass test in drawPoints\n                point.y = 1;\n            });\n        });\n    }\n}\nNetworkgraphSeries.defaultOptions = NetworkgraphSeries_merge(Series.defaultOptions, Networkgraph_NetworkgraphSeriesDefaults);\nNetworkgraphSeries_extend(NetworkgraphSeries.prototype, {\n    pointClass: Networkgraph_NetworkgraphPoint,\n    animate: void 0, // Animation is run in `series.simulation`\n    directTouch: true,\n    drawGraph: void 0,\n    forces: ['barycenter', 'repulsive', 'attractive'],\n    hasDraggableNodes: true,\n    isCartesian: false,\n    noSharedTooltip: true,\n    pointArrayMap: ['from', 'to'],\n    requireSorting: false,\n    trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n    initDataLabels: NetworkgraphSeries_initDataLabels,\n    buildKDTree: noop,\n    createNode: Series_NodesComposition.createNode,\n    drawTracker: columnProto.drawTracker,\n    onMouseDown: Series_DragNodesComposition.onMouseDown,\n    onMouseMove: Series_DragNodesComposition.onMouseMove,\n    onMouseUp: Series_DragNodesComposition.onMouseUp,\n    redrawHalo: Series_DragNodesComposition.redrawHalo\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('networkgraph', NetworkgraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Networkgraph_NetworkgraphSeries = (NetworkgraphSeries);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback that fires after the end of Networkgraph series simulation\n * when the layout is stable.\n *\n * @callback Highcharts.NetworkgraphAfterSimulationCallbackFunction\n *\n * @param {Highcharts.Series} this\n *        The series where the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/networkgraph.js\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nNetworkgraph_NetworkgraphSeries.compose(G.Chart);\n/* harmony default export */ const networkgraph_src = ((external_highcharts_src_js_default_default()));\n\nexport { networkgraph_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "NodesComposition", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SVGElement_namespaceObject", "SVGElement", "external_highcharts_src_js_default_SVGElement_default", "composed", "addEvent", "pushUnique", "onChartLoad", "mousedownUnbinder", "mousemoveUnbinder", "mouseupUnbinder", "point", "chart", "container", "event", "hoverPoint", "series", "hasDraggableNodes", "options", "draggable", "onMouseDown", "e", "onMouseMove", "ownerDocument", "onMouseUp", "Series_DragNodesComposition", "compose", "ChartClass", "normalizedEvent", "pointer", "normalize", "fixedPosition", "chartX", "chartY", "plotX", "plotY", "inDragMode", "newPlotX", "newPlotY", "diffX", "diffY", "graphLayoutsLookup", "Math", "abs", "isInsidePlot", "hasDragged", "redrawHalo", "for<PERSON>ach", "layout", "restartSimulation", "enableSimulation", "start", "redraw", "fixedDraggable", "halo", "attr", "haloPath", "states", "hover", "size", "setAnimation", "GraphLayoutComposition_composed", "GraphLayoutComposition_addEvent", "GraphLayoutComposition_pushUnique", "onChartAfterPrint", "updateSimulation", "onChartBeforePrint", "onChartPredraw", "stop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "systemsStable", "afterRender", "layoutStep", "maxIterations", "isFinite", "temperature", "isStable", "beforeStep", "step", "render", "Series_GraphLayoutComposition", "integrations", "layouts", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "seriesProto", "pointClass", "pointProto", "defined", "extend", "find", "merge", "pick", "destroy", "data", "concat", "points", "nodes", "apply", "arguments", "setData", "node", "length", "setNodeState", "state", "args", "others", "isNode", "linksTo", "linksFrom", "fromNode", "toNode", "linkOrNode", "setState", "graphic", "updateNode", "animation", "runEvent", "dataLength", "linkConfig", "index", "update", "nodeIndex", "reduce", "prevIndex", "id", "nodeConfig", "push", "PointClass", "SeriesClass", "createNode", "findById", "newNode", "className", "y", "getSum", "sumTo", "sumFrom", "link", "weight", "max", "offset", "coll", "i", "<PERSON><PERSON><PERSON><PERSON>", "outgoing", "formatPrefix", "name", "mass", "marker", "radius", "generatePoints", "nodeLookup", "colorCounter", "level", "from", "styledMode", "colorIndex", "color", "to", "Series_NodesComposition", "NetworkgraphPoint_seriesProto", "Point", "NetworkgraphPoint_addEvent", "css", "NetworkgraphPoint_defined", "NetworkgraphPoint_extend", "NetworkgraphPoint_pick", "NetworkgraphPoint", "destroyElements", "removeElementFromCollection", "getDegree", "deg", "getLinkAttributes", "linkOptions", "pointOptions", "width", "stroke", "dashstyle", "dashStyle", "opacity", "getLinkPath", "left", "right", "getMass", "m1", "m2", "sum", "constructor", "x", "cursor", "<PERSON><PERSON><PERSON><PERSON>", "redrawLink", "attribs", "path", "shapeArgs", "pointAttribs", "dataLabels", "label", "animate", "end", "remove", "nodesOptions", "linkFromTo", "indexOf", "splice", "removePoint", "slice", "isDirty", "isDirtyData", "renderLink", "renderer", "addClass", "getClassName", "add", "group", "Networkgraph_EulerIntegration", "attractive", "force", "distanceXY", "distanceR", "massFactor", "translatedX", "translatedY", "dispX", "degree", "dispY", "attractiveForceFunction", "k", "barycenter", "gravitationalConstant", "xFactor", "yFactor", "phi", "getK", "pow", "box", "height", "integrate", "friction", "vectorLength", "min", "repulsive", "repulsiveForceFunction", "QuadTreeNode", "body", "isEmpty", "isInternal", "boxSize", "divideBox", "halfWidth", "halfHeight", "top", "getBoxPosition", "insert", "depth", "newQuadTreeNode", "NaN", "updateMassAndCenter", "pointMass", "Networkgraph_QuadTree", "max<PERSON><PERSON><PERSON>", "root", "isRoot", "calculateMassAndCenter", "visitNodeRecursive", "insertNodes", "beforeCallback", "afterCallback", "<PERSON><PERSON><PERSON><PERSON>", "qtNode", "Networkgraph_VerletIntegration", "diffTemperature", "maxSpeed", "prevX", "prevY", "frictionX", "frictionY", "signX", "signY", "factor", "win", "clamp", "ReingoldFruchtermanLayout_defined", "isFunction", "fireEvent", "ReingoldFruchtermanLayout_pick", "ReingoldFruchtermanLayout", "currentStep", "initialRendering", "links", "simulation", "euler", "verlet", "init", "setInitialRendering", "integration", "attractive<PERSON><PERSON><PERSON>", "repulsiveForce", "approximation", "enable", "forces", "initPositions", "s", "finishedAnimating", "setK", "resetSimulation", "allSeries", "forceName", "createQuadTree", "quadTree", "anyLayout", "applyLimits", "coolDown", "startTemperature", "prevSystemTemperature", "systemTemperature", "getSystemTemperature", "cancelAnimationFrame", "requestAnimationFrame", "<PERSON><PERSON><PERSON>", "w", "h", "linkLength", "addElementsToCollection", "elements", "collection", "element", "clear", "forcedStop", "setMaxIterations", "setTemperature", "setDiffTemperature", "sqrt", "initialPositions", "setCircularPositions", "setRandomPositions", "angle", "PI", "rootNodes", "filter", "visitedNodes", "initialPositionRadius", "addToNodes", "sortedNodes", "rootNode", "iEnd", "cos", "sin", "<PERSON><PERSON><PERSON><PERSON>", "unrandom", "rand", "floor", "barycenterForces", "getBarycenter", "systemMass", "cx", "cy", "barnesHutApproximation", "quadNode", "goDeeper", "getDistXY", "theta", "repulsiveForces", "repNode", "attractiveForces", "applyLimitBox", "temperatureStep", "value", "vector", "getDistR", "nodeA", "nodeB", "distance", "xDist", "yDist", "absX", "absY", "SimulationSeriesUtilities_merge", "syncTimeout", "animObject", "deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "textWrapper", "text", "textPath", "undo", "textPathId", "textAttribs", "dx", "transform", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "bBox", "tp", "querySelector", "polygon", "b", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "sinRot", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "noop", "Series", "seriesTypes", "column", "columnProto", "lineProto", "initDataLabels", "NetworkgraphSeries_initDataLabels", "initDataLabelsDefer", "NetworkgraphSeries_initDataLabelsDefer", "dlOptions", "dataLabelsGroup", "initDataLabelsGroup", "style", "visible", "show", "getPlotBox", "defer", "layoutAlgorithm", "deferDataLabels", "NetworkgraphSeries_addEvent", "NetworkgraphSeries_defined", "NetworkgraphSeries_extend", "NetworkgraphSeries_merge", "NetworkgraphSeries_pick", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "NetworkgraphSeries", "Networkgraph_ReingoldFruchtermanLayout", "deferLayout", "layoutOptions", "chartOptions", "graphLayoutsStorage", "type", "forExport", "plot<PERSON>id<PERSON>", "plotHeight", "drawDataLabels", "linkTextPath", "nodeOptions", "indexateNodes", "getPointsCollection", "resolveColor", "markerAttribs", "pointState", "stateOptions", "linkColor", "linkDashStyle", "linkOpacity", "hasRendered", "allowOverlap", "dataLabel", "hideOverlappingLabels", "inherit", "translate", "isInside", "shapeType", "defaultOptions", "stickyTracking", "inactiveOtherPoints", "inactive", "duration", "formatter", "String", "linkFormatter", "transition", "showInLegend", "directTouch", "drawGraph", "isCartesian", "noSharedTooltip", "pointArrayMap", "requireSorting", "trackerGroups", "buildKDTree", "drawTracker", "registerSeriesType", "G", "Networkgraph_NetworkgraphSeries", "Chart", "networkgraph_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,CAEvF,IAuWLC,EAvWSC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDtB,EAAwD,OAAU,CAC7H,IAAIuB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAEpF,IAAME,EAAgExB,EAAwD,OAAU,CAACyB,UAAU,CACnJ,IAAIC,EAAqExB,EAAoBC,CAAC,CAACqB,GAe/F,GAAM,CAAEG,SAAAA,CAAQ,CAAE,CAAIJ,IAEhB,CAAEK,SAAAA,CAAQ,CAAEC,WAAAA,CAAU,CAAE,CAAIN,IAkBlC,SAASO,IACL,IACIC,EAAmBC,EAAmBC,EAAiBC,EADrDC,EAAQ,IAAI,AAEdA,CAAAA,EAAMC,SAAS,EACfL,CAAAA,EAAoBH,EAASO,EAAMC,SAAS,CAAE,YAAa,AAACC,IACpDL,GACAA,IAEAC,GACAA,IAEJC,CAAAA,EAAQC,EAAMG,UAAU,AAAD,GAEnBJ,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACC,iBAAiB,EAC9BN,EAAMK,MAAM,CAACE,OAAO,CAACC,SAAS,GAC9BR,EAAMK,MAAM,CAACI,WAAW,CAACT,EAAOG,GAChCL,EAAoBJ,EAASO,EAAMC,SAAS,CAAE,YAAa,AAACQ,GAAOV,GAC/DA,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACM,WAAW,CAACX,EAAOU,IACpCX,EAAkBL,EAASO,EAAMC,SAAS,CAACU,aAAa,CAAE,UAAW,AAACF,IAClEZ,IACAC,IACOC,GACHA,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACQ,SAAS,CAACb,EAAOU,KAG9C,EAAC,EAELhB,EAASO,EAAO,UAAW,WACvBJ,GACJ,EACJ,CAmG6B,IAAMiB,EAPN,CACzBC,QAvIJ,SAAiBC,CAAU,EACnBrB,EAAWF,EAAU,cACrBC,EAASsB,EAAY,OAAQpB,EAErC,EAoIIa,YApFJ,SAAqBT,CAAK,CAAEG,CAAK,EAC7B,IAAMc,EAAkB,IAAI,CAAChB,KAAK,CAACiB,OAAO,EAAEC,UAAUhB,IAAUA,CAChEH,CAAAA,EAAMoB,aAAa,CAAG,CAClBC,OAAQJ,EAAgBI,MAAM,CAC9BC,OAAQL,EAAgBK,MAAM,CAC9BC,MAAOvB,EAAMuB,KAAK,CAClBC,MAAOxB,EAAMwB,KAAK,AACtB,EACAxB,EAAMyB,UAAU,CAAG,CAAA,CACvB,EA4EId,YAjEJ,SAAqBX,CAAK,CAAEG,CAAK,EAC7B,GAAIH,EAAMoB,aAAa,EAAIpB,EAAMyB,UAAU,CAAE,CACzC,IACIC,EAAUC,EADO1B,EAAQI,AAAd,IAAI,CAAiBJ,KAAK,CAAEgB,EAAkBhB,EAAMiB,OAAO,EAAEC,UAAUhB,IAAUA,EAAOyB,EAAQ5B,EAAMoB,aAAa,CAACC,MAAM,CAAGJ,EAAgBI,MAAM,CAAEQ,EAAQ7B,EAAMoB,aAAa,CAACE,MAAM,CAAGL,EAAgBK,MAAM,CAAEQ,EAAqB7B,EAAM6B,kBAAkB,CAG1QC,CAAAA,KAAKC,GAAG,CAACJ,GAAS,GAAKG,KAAKC,GAAG,CAACH,GAAS,CAAA,IACzCH,EAAW1B,EAAMoB,aAAa,CAACG,KAAK,CAAGK,EACvCD,EAAW3B,EAAMoB,aAAa,CAACI,KAAK,CAAGK,EACnC5B,EAAMgC,YAAY,CAACP,EAAUC,KAC7B3B,EAAMuB,KAAK,CAAGG,EACd1B,EAAMwB,KAAK,CAAGG,EACd3B,EAAMkC,UAAU,CAAG,CAAA,EACnB,IAAI,CAACC,UAAU,CAACnC,GAChB8B,EAAmBM,OAAO,CAAC,AAACC,IACxBA,EAAOC,iBAAiB,EAC5B,IAGZ,CACJ,EA+CIzB,UAvCJ,SAAmBb,CAAK,EAChBA,EAAMoB,aAAa,GACfpB,EAAMkC,UAAU,GACZ,IAAI,CAACG,MAAM,CAACE,gBAAgB,CAC5B,IAAI,CAACF,MAAM,CAACG,KAAK,GAGjB,IAAI,CAACvC,KAAK,CAACwC,MAAM,IAGzBzC,EAAMyB,UAAU,CAAGzB,EAAMkC,UAAU,CAAG,CAAA,EACjC,IAAI,CAAC3B,OAAO,CAACmC,cAAc,EAC5B,OAAO1C,EAAMoB,aAAa,CAGtC,EAyBIe,WAjBJ,SAAoBnC,CAAK,EACjBA,GAAS,IAAI,CAAC2C,IAAI,EAClB,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CACXvE,EAAG2B,EAAM6C,QAAQ,CAAC,IAAI,CAACtC,OAAO,CAACuC,MAAM,CAACC,KAAK,CAACJ,IAAI,CAACK,IAAI,CACzD,EAER,CAYA,EAiBM,CAAEC,aAAAA,CAAY,CAAE,CAAI5D,IAEpB,CAAEI,SAAUyD,CAA+B,CAAE,CAAI7D,IAEjD,CAAEK,SAAUyD,CAA+B,CAAExD,WAAYyD,CAAiC,CAAE,CAAI/D,IA4BtG,SAASgE,IACD,IAAI,CAACvB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACM,OAAO,CAAC,AAACC,IAE7BA,EAAOiB,gBAAgB,EAC3B,GACA,IAAI,CAACb,MAAM,GAEnB,CAKA,SAASc,IACD,IAAI,CAACzB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACM,OAAO,CAAC,AAACC,IAC7BA,EAAOiB,gBAAgB,CAAC,CAAA,EAC5B,GACA,IAAI,CAACb,MAAM,GAEnB,CAKA,SAASe,IACD,IAAI,CAAC1B,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACM,OAAO,CAAC,AAACC,IAC7BA,EAAOoB,IAAI,EACf,EAER,CAIA,SAASC,IACL,IAAIC,EAAeC,EAAc,CAAA,EAC3BC,EAAa,AAACxB,IACZA,EAAOyB,aAAa,IACpBC,SAAS1B,EAAO2B,WAAW,GAC3B,CAAC3B,EAAO4B,QAAQ,IAChB,CAAC5B,EAAOE,gBAAgB,GAMpBF,EAAO6B,UAAU,EACjB7B,EAAO6B,UAAU,GAErB7B,EAAO8B,IAAI,GACXR,EAAgB,CAAA,EAChBC,EAAc,CAAA,EAEtB,EACA,GAAI,IAAI,CAAC9B,kBAAkB,CAAE,CAMzB,IALAmB,EAAa,CAAA,EAAO,IAAI,EAExB,IAAI,CAACnB,kBAAkB,CAACM,OAAO,CAAC,AAACC,GAAWA,EAAOG,KAAK,IAGjD,CAACmB,GACJA,EAAgB,CAAA,EAChB,IAAI,CAAC7B,kBAAkB,CAACM,OAAO,CAACyB,GAEhCD,GACA,IAAI,CAACvD,MAAM,CAAC+B,OAAO,CAAC,AAAC/B,IACbA,GAAUA,EAAOgC,MAAM,EACvBhC,EAAO+D,MAAM,EAErB,EAER,CACJ,CAW6B,IAAMC,EALJ,CAC3BtD,QA5FJ,SAAwCC,CAAU,EAC1CoC,EAAkCF,EAAiC,iBACnEC,EAAgCnC,EAAY,aAAcqC,GAC1DF,EAAgCnC,EAAY,cAAeuC,GAC3DJ,EAAgCnC,EAAY,UAAWwC,GACvDL,EAAgCnC,EAAY,SAAU0C,GAE9D,EAsFIY,aAvGiB,CAAC,EAwGlBC,QAvGY,CAAC,CAwGjB,EAIMC,EAAoE1G,EAAwD,OAAU,CAAC2G,cAAc,CAC3J,IAAIC,EAAyE1G,EAAoBC,CAAC,CAACuG,GASnG,GAAM,CAAEnE,OAAQ,CAAEpB,UAAW0F,CAAW,CAAE1F,UAAW,CAAE2F,WAAY,CAAE3F,UAAW4F,CAAU,CAAE,CAAE,CAAE,CAAE,CAAIH,IAEhG,CAAEI,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAI7F,KAOhD,AAAC,SAAUtB,CAAgB,EAsGvB,SAASoH,IAIL,OAFA,IAAI,CAACC,IAAI,CAAG,EAAE,CACTC,MAAM,CAAC,IAAI,CAACC,MAAM,EAAI,EAAE,CAAE,IAAI,CAACC,KAAK,EAClCZ,EAAYQ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC3C,CAsDA,SAASC,IACD,IAAI,CAACH,KAAK,GACV,IAAI,CAACA,KAAK,CAACnD,OAAO,CAAC,AAACuD,IAChBA,EAAKR,OAAO,EAChB,GACA,IAAI,CAACI,KAAK,CAACK,MAAM,CAAG,GAExBjB,EAAYe,OAAO,CAACF,KAAK,CAAC,IAAI,CAAEC,UACpC,CAMA,SAASI,EAAaC,CAAK,EACvB,IAAMC,EAAON,UAAWO,EAAS,IAAI,CAACC,MAAM,CAAG,IAAI,CAACC,OAAO,CAACb,MAAM,CAAC,IAAI,CAACc,SAAS,EAC7E,CAAC,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,MAAM,CAAC,AAClB,CAAA,WAAVP,GACAE,EAAO5D,OAAO,CAAC,AAACkE,IACRA,GAAcA,EAAWjG,MAAM,GAC/BwE,EAAW0B,QAAQ,CAACf,KAAK,CAACc,EAAYP,GAClC,CAACO,EAAWL,MAAM,GACdK,EAAWF,QAAQ,CAACI,OAAO,EAC3B3B,EAAW0B,QAAQ,CAACf,KAAK,CAACc,EAAWF,QAAQ,CAAEL,GAE/CO,EAAWD,MAAM,EAAIC,EAAWD,MAAM,CAACG,OAAO,EAC9C3B,EAAW0B,QAAQ,CAACf,KAAK,CAACc,EAAWD,MAAM,CAAEN,IAI7D,GAEJlB,EAAW0B,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEO,EACpC,CAOA,SAASU,EAAWlG,CAAO,CAAEkC,CAAM,CAAEiE,CAAS,CAAEC,CAAQ,EACpD,IAAMpB,EAAQ,IAAI,CAAClF,MAAM,CAACE,OAAO,CAACgF,KAAK,CAAEH,EAAO,IAAI,CAAC/E,MAAM,CAACE,OAAO,CAAC6E,IAAI,CAAEwB,EAAaxB,GAAMQ,QAAU,EAAGiB,EAAazB,GAAM,CAAC,IAAI,CAAC0B,KAAK,CAAC,CAGzI,GAFAjC,EAAWkC,MAAM,CAAC5H,IAAI,CAAC,IAAI,CAAEoB,EAAS,CAAA,IAAI,CAAC0F,MAAM,EAAWxD,EAC5DiE,EAAWC,GACP,IAAI,CAACV,MAAM,CAAE,CAEb,IAAMe,EAAY,AAACzB,CAAAA,GAAS,EAAE,AAAD,EACxB0B,MAAM,CACX,CAACC,EAAWjJ,EAAG6I,IAAW,IAAI,CAACK,EAAE,GAAKlJ,EAAEkJ,EAAE,CAAGL,EAAQI,EAAY,IAGjEE,EAAanC,EAAMM,GAASA,CAAK,CAACyB,EAAU,EAAI,CAAC,EAAG5B,GAAM,CAAC,IAAI,CAAC0B,KAAK,CAAC,EAAI,CAAC,GAEvE1B,IACIyB,EACAzB,CAAI,CAAC,IAAI,CAAC0B,KAAK,CAAC,CAAGD,EAInBzB,EAAKQ,MAAM,CAAGgB,GAIlBrB,EACIyB,GAAa,EACbzB,CAAK,CAACyB,EAAU,CAAGI,EAGnB7B,EAAM8B,IAAI,CAACD,GAIf,IAAI,CAAC/G,MAAM,CAACE,OAAO,CAACgF,KAAK,CAAG,CAAC6B,EAAW,CAExClC,EAAKzC,EAAQ,CAAA,IACb,IAAI,CAACpC,MAAM,CAACJ,KAAK,CAACwC,MAAM,CAACiE,EAEjC,CACJ,CAxNA3I,EAAiBgD,OAAO,CATxB,SAAiBuG,CAAU,CAAEC,CAAW,EACpC,IAAM1C,EAAayC,EAAWrI,SAAS,CAAE0F,EAAc4C,EAAYtI,SAAS,CAM5E,OALA4F,EAAWgB,YAAY,CAAGA,EAC1BhB,EAAW0B,QAAQ,CAAGV,EACtBhB,EAAWkC,MAAM,CAAGN,EACpB9B,EAAYQ,OAAO,CAAGA,EACtBR,EAAYe,OAAO,CAAGA,EACf6B,CACX,EA2EAxJ,EAAiByJ,UAAU,CApE3B,SAAoBL,CAAE,EAClB,IAAMG,EAAa,IAAI,CAAC1C,UAAU,CAAE6C,EAAW,CAAClC,EAAO4B,IAAOnC,EAAKO,EAAO,AAACI,GAASA,EAAKwB,EAAE,GAAKA,GAC5FxB,EAAO8B,EAAS,IAAI,CAAClC,KAAK,CAAE4B,GAAK5G,EACrC,GAAI,CAACoF,EAAM,CACPpF,EAAU,IAAI,CAACA,OAAO,CAACgF,KAAK,EAAIkC,EAAS,IAAI,CAAClH,OAAO,CAACgF,KAAK,CAAE4B,GAC7D,IAAMO,EAAU,IAAIJ,EAAW,IAAI,CAAEvC,EAAO,CACxC4C,UAAW,kBACX1B,OAAQ,CAAA,EACRkB,GAAIA,EACJS,EAAG,CACP,EAAGrH,GACHmH,CAAAA,EAAQxB,OAAO,CAAG,EAAE,CACpBwB,EAAQvB,SAAS,CAAG,EAAE,CAKtBuB,EAAQG,MAAM,CAAG,WACb,IAAIC,EAAQ,EAAGC,EAAU,EAOzB,OANAL,EAAQxB,OAAO,CAAC9D,OAAO,CAAC,AAAC4F,IACrBF,GAASE,EAAKC,MAAM,EAAI,CAC5B,GACAP,EAAQvB,SAAS,CAAC/D,OAAO,CAAC,AAAC4F,IACvBD,GAAWC,EAAKC,MAAM,EAAI,CAC9B,GACOlG,KAAKmG,GAAG,CAACJ,EAAOC,EAC3B,EAKAL,EAAQS,MAAM,CAAG,SAAUnI,CAAK,CAAEoI,CAAI,EAClC,IAAID,EAAS,EACb,IAAK,IAAIE,EAAI,EAAGA,EAAIX,CAAO,CAACU,EAAK,CAACxC,MAAM,CAAEyC,IAAK,CAC3C,GAAIX,CAAO,CAACU,EAAK,CAACC,EAAE,GAAKrI,EACrB,OAAOmI,EAEXA,GAAUT,CAAO,CAACU,EAAK,CAACC,EAAE,CAACJ,MAAM,AACrC,CACJ,EAGAP,EAAQY,QAAQ,CAAG,WACf,IAAIC,EAAW,EAMf,OALAb,EAAQxB,OAAO,CAAC9D,OAAO,CAAC,AAAC4F,IACjBA,EAAKO,QAAQ,EACbA,GAER,GACQ,CAACb,EAAQxB,OAAO,CAACN,MAAM,EAC3B2C,IAAab,EAAQxB,OAAO,CAACN,MAAM,AAC3C,EACA8B,EAAQZ,KAAK,CAAG,IAAI,CAACvB,KAAK,CAAC8B,IAAI,CAACK,GAAW,EAC3C/B,EAAO+B,CACX,CAYA,OAXA/B,EAAK6C,YAAY,CAAG,OAEpB7C,EAAK8C,IAAI,CAAG9C,EAAK8C,IAAI,EAAI9C,EAAKpF,OAAO,CAAC4G,EAAE,EAAI,GAE5CxB,EAAK+C,IAAI,CAAGxD,EAEZS,EAAKpF,OAAO,CAACmI,IAAI,CAAE/C,EAAKpF,OAAO,CAACoI,MAAM,EAAIhD,EAAKpF,OAAO,CAACoI,MAAM,CAACC,MAAM,CAEpE,IAAI,CAACrI,OAAO,CAACoI,MAAM,EAAI,IAAI,CAACpI,OAAO,CAACoI,MAAM,CAACC,MAAM,CAEjD,GACOjD,CACX,EAYA5H,EAAiBoH,OAAO,CAAGA,EAgD3BpH,EAAiB8K,cAAc,CA1C/B,WACI,IAAM5I,EAAQ,IAAI,CAACA,KAAK,CAAE6I,EAAa,CAAC,EACxCnE,EAAYkE,cAAc,CAAC1J,IAAI,CAAC,IAAI,EAC/B,IAAI,CAACoG,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAElB,IAAI,CAACwD,YAAY,CAAG,EAEpB,IAAI,CAACxD,KAAK,CAACnD,OAAO,CAAC,AAACuD,IAChBA,EAAKQ,SAAS,CAACP,MAAM,CAAG,EACxBD,EAAKO,OAAO,CAACN,MAAM,CAAG,EACtBD,EAAKqD,KAAK,CAAGrD,EAAKpF,OAAO,CAACyI,KAAK,AACnC,GAEA,IAAI,CAAC1D,MAAM,CAAClD,OAAO,CAAC,AAACpC,IACb8E,EAAQ9E,EAAMiJ,IAAI,IACbH,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,EACvBH,CAAAA,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAAG,IAAI,CAACzB,UAAU,CAACxH,EAAMiJ,IAAI,CAAA,EAEvDH,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAAC9C,SAAS,CAACkB,IAAI,CAACrH,GACtCA,EAAMoG,QAAQ,CAAG0C,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAEnChJ,EAAMiJ,UAAU,CAChBlJ,EAAMmJ,UAAU,CAAGjE,EAAKlF,EAAMO,OAAO,CAAC4I,UAAU,CAAEL,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAACE,UAAU,EAGnFnJ,EAAMoJ,KAAK,CACPpJ,EAAMO,OAAO,CAAC6I,KAAK,EAAIN,CAAU,CAAC9I,EAAMiJ,IAAI,CAAC,CAACG,KAAK,EAG3DtE,EAAQ9E,EAAMqJ,EAAE,IACXP,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,EACrBP,CAAAA,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,CAAG,IAAI,CAAC7B,UAAU,CAACxH,EAAMqJ,EAAE,CAAA,EAEnDP,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,CAACnD,OAAO,CAACmB,IAAI,CAACrH,GAClCA,EAAMqG,MAAM,CAAGyC,CAAU,CAAC9I,EAAMqJ,EAAE,CAAC,EAEvCrJ,EAAMyI,IAAI,CAAGzI,EAAMyI,IAAI,EAAIzI,EAAMmH,EAAE,AACvC,EAAG,IAAI,EAEP,IAAI,CAAC2B,UAAU,CAAGA,CACtB,EAwCA/K,EAAiB8H,YAAY,CAAGA,EA6ChC9H,EAAiB0I,UAAU,CAAGA,CAClC,EAAG1I,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMuL,EAA2BvL,EAiBxD,CAAEsC,OAAQ,CAAEpB,UAAWsK,CAA6B,CAAEtK,UAAW,CAAE2F,WAAY4E,CAAK,CAAE,CAAE,CAAE,CAAI9E,IAE9F,CAAEhF,SAAU+J,CAA0B,CAAEC,IAAAA,CAAG,CAAE5E,QAAS6E,CAAyB,CAAE5E,OAAQ6E,CAAwB,CAAE1E,KAAM2E,CAAsB,CAAE,CAAIxK,GAM3J,OAAMyK,UAA0BN,EAW5BrE,SAAU,CAWN,OAVI,IAAI,CAACc,MAAM,EACX,IAAI,CAACE,SAAS,CAACd,MAAM,CAAC,IAAI,CAACa,OAAO,EAAE9D,OAAO,CAAC,SAAU4F,CAAI,EAGlDA,EAAK+B,eAAe,EACpB/B,EAAK+B,eAAe,EAE5B,GAEJ,IAAI,CAAC1J,MAAM,CAACgC,MAAM,CAAC2H,2BAA2B,CAAC,IAAI,CAAE,IAAI,CAAC3J,MAAM,CAACgC,MAAM,CAAC,IAAI,CAAC4D,MAAM,CAAG,QAAU,QAAQ,EACjGuD,EAAMvK,SAAS,CAACkG,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC/C,CAMAwE,WAAY,CACR,IAAMC,EAAM,IAAI,CAACjE,MAAM,CACnB,IAAI,CAACE,SAAS,CAACP,MAAM,CAAG,IAAI,CAACM,OAAO,CAACN,MAAM,CAC3C,EACJ,OAAOsE,AAAQ,IAARA,EAAY,EAAIA,CAC3B,CAKAC,mBAAoB,CAChB,IAAMC,EAAc,IAAI,CAAC/J,MAAM,CAACE,OAAO,CAACyH,IAAI,CAAEqC,EAAe,IAAI,CAAC9J,OAAO,CACzE,MAAO,CACH,eAAgBsJ,EAAuBQ,EAAaC,KAAK,CAAEF,EAAYE,KAAK,EAC5EC,OAASF,EAAajB,KAAK,EAAIgB,EAAYhB,KAAK,CAChDoB,UAAYH,EAAaI,SAAS,EAAIL,EAAYK,SAAS,CAC3DC,QAASb,EAAuBQ,EAAaK,OAAO,CAAEN,EAAYM,OAAO,CAAE,EAC/E,CACJ,CAOAC,aAAc,CACV,IAAIC,EAAO,IAAI,CAACxE,QAAQ,CAAEyE,EAAQ,IAAI,CAACxE,MAAM,CAO7C,OAJIuE,EAAKrJ,KAAK,CAAGsJ,EAAMtJ,KAAK,GACxBqJ,EAAO,IAAI,CAACvE,MAAM,CAClBwE,EAAQ,IAAI,CAACzE,QAAQ,EAElB,CACH,CAAC,IAAKwE,EAAKrJ,KAAK,EAAI,EAAGqJ,EAAKpJ,KAAK,EAAI,EAAE,CACvC,CAAC,IAAKqJ,EAAMtJ,KAAK,EAAI,EAAGsJ,EAAMrJ,KAAK,EAAI,EAAE,CAC5C,AAaL,CASAsJ,SAAU,CACN,IAAMC,EAAK,IAAI,CAAC3E,QAAQ,CAACsC,IAAI,CAAEsC,EAAK,IAAI,CAAC3E,MAAM,CAACqC,IAAI,CAAEuC,EAAMF,EAAKC,EACjE,MAAO,CACH5E,SAAU,EAAI2E,EAAKE,EACnB5E,OAAQ,EAAI2E,EAAKC,CACrB,CACJ,CAMAC,YAAY7K,CAAM,CAAEE,CAAO,CAAE4K,CAAC,CAAE,CAC5B,KAAK,CAAC9K,EAAQE,EAAS4K,GACnB,IAAI,CAAC9K,MAAM,CAACE,OAAO,CAACC,SAAS,EAC7B,CAAC,IAAI,CAACH,MAAM,CAACJ,KAAK,CAACiJ,UAAU,GAC7BO,EAA2B,IAAI,CAAE,YAAa,WAC1CC,EAAI,IAAI,CAACrJ,MAAM,CAACJ,KAAK,CAACC,SAAS,CAAE,CAAEkL,OAAQ,MAAO,EACtD,GACA3B,EAA2B,IAAI,CAAE,WAAY,WACzCC,EAAI,IAAI,CAACrJ,MAAM,CAACJ,KAAK,CAACC,SAAS,CAAE,CAAEkL,OAAQ,SAAU,EACzD,GAER,CAIAC,SAAU,CACN,MAAO,CAAC,IAAI,CAACpF,MAAM,EAAI0D,EAA0B,IAAI,CAACxC,EAAE,CAC5D,CAKAmE,YAAa,CACT,IACIC,EADEC,EAAO,IAAI,CAACb,WAAW,GAE7B,GAAI,IAAI,CAACnE,OAAO,CAAE,CACd,IAAI,CAACiF,SAAS,CAAG,CACbpN,EAAGmN,CACP,EACK,IAAI,CAACnL,MAAM,CAACJ,KAAK,CAACiJ,UAAU,GAC7BqC,EAAU,IAAI,CAAClL,MAAM,CAACqL,YAAY,CAAC,IAAI,EACvC,IAAI,CAAClF,OAAO,CAAC5D,IAAI,CAAC2I,GAClB,AAAC,CAAA,IAAI,CAACI,UAAU,EAAI,EAAE,AAAD,EAAGvJ,OAAO,CAAC,SAAUwJ,CAAK,EACvCA,GACAA,EAAMhJ,IAAI,CAAC,CACP8H,QAASa,EAAQb,OAAO,AAC5B,EAER,IAEJ,IAAI,CAAClE,OAAO,CAACqF,OAAO,CAAC,IAAI,CAACJ,SAAS,EAEnC,IAAMjJ,EAAQgJ,CAAI,CAAC,EAAE,CACfM,EAAMN,CAAI,CAAC,EAAE,AACF,CAAA,MAAbhJ,CAAK,CAAC,EAAE,EAAYsJ,AAAW,MAAXA,CAAG,CAAC,EAAE,GAC1B,IAAI,CAACvK,KAAK,CAAG,AAACiB,CAAAA,CAAK,CAAC,EAAE,CAAGsJ,CAAG,CAAC,EAAE,AAAD,EAAK,EACnC,IAAI,CAACtK,KAAK,CAAG,AAACgB,CAAAA,CAAK,CAAC,EAAE,CAAGsJ,CAAG,CAAC,EAAE,AAAD,EAAK,EAE3C,CACJ,CAeAC,OAAOtJ,CAAM,CAAEiE,CAAS,CAAE,CACtB,IAAoBrG,EAASL,AAAf,IAAI,CAAiBK,MAAM,CAAE2L,EAAe3L,EAAOE,OAAO,CAACgF,KAAK,EAAI,EAAE,CAChFuB,EAAOuB,EAAI2D,EAAapG,MAAM,CAElC,GAAI5F,AAHU,IAAI,CAGRiG,MAAM,CAAE,CA4Bd,IAzBA5F,EAAOiF,MAAM,CAAG,EAAE,CAElB,EAAE,CACGD,MAAM,CAACrF,AATF,IAAI,CASImG,SAAS,EACtBd,MAAM,CAACrF,AAVF,IAAI,CAUIkG,OAAO,EACpB9D,OAAO,CAAC,SAAU6J,CAAU,EAE7BnF,CAAAA,EAAQmF,EAAW7F,QAAQ,CAACD,SAAS,CAAC+F,OAAO,CAACD,EAAU,EAC5C,IACRA,EAAW7F,QAAQ,CAACD,SAAS,CAACgG,MAAM,CAACrF,EAAO,GAGhDA,CAAAA,EAAQmF,EAAW5F,MAAM,CAACH,OAAO,CAACgG,OAAO,CAACD,EAAU,EACxC,IACRA,EAAW5F,MAAM,CAACH,OAAO,CAACiG,MAAM,CAACrF,EAAO,GAG5CyC,EAA8B6C,WAAW,CAACjN,IAAI,CAACkB,EAAQA,EAAO+E,IAAI,CAAC8G,OAAO,CAACD,GAAa,CAAA,EAAO,CAAA,EACnG,GAEA5L,EAAOiF,MAAM,CAAGjF,EAAO+E,IAAI,CAACiH,KAAK,GAGjChM,EAAOkF,KAAK,CAAC4G,MAAM,CAAC9L,EAAOkF,KAAK,CAAC2G,OAAO,CA7B9B,IAAI,EA6BmC,GAE1C7D,KACH,GAAI2D,CAAY,CAAC3D,EAAE,CAAClB,EAAE,GAAKnH,AAhCrB,IAAI,CAgCuBO,OAAO,CAAC4G,EAAE,CAAE,CACzC9G,EAAOE,OAAO,CAACgF,KAAK,CAAC4G,MAAM,CAAC9D,EAAG,GAC/B,KACJ,CAnCM,IAAI,EAsCVrI,AAtCM,IAAI,CAsCJmF,OAAO,GAGjB9E,EAAOiM,OAAO,CAAG,CAAA,EACjBjM,EAAOkM,WAAW,CAAG,CAAA,EACjB9J,GACApC,EAAOJ,KAAK,CAACwC,MAAM,CAACA,EAE5B,MAEIpC,EAAO+L,WAAW,CAAC/L,EAAO+E,IAAI,CAAC8G,OAAO,CAhD5B,IAAI,EAgDiCzJ,EAAQiE,EAE/D,CAKA8F,YAAa,CACT,IAAIjB,CACC,CAAA,IAAI,CAAC/E,OAAO,GACb,IAAI,CAACA,OAAO,CAAG,IAAI,CAACnG,MAAM,CAACJ,KAAK,CAACwM,QAAQ,CACpCjB,IAAI,CAAC,IAAI,CAACb,WAAW,IACrB+B,QAAQ,CAAC,IAAI,CAACC,YAAY,GAAI,CAAA,GAC9BC,GAAG,CAAC,IAAI,CAACvM,MAAM,CAACwM,KAAK,EACrB,IAAI,CAACxM,MAAM,CAACJ,KAAK,CAACiJ,UAAU,GAC7BqC,EAAU,IAAI,CAAClL,MAAM,CAACqL,YAAY,CAAC,IAAI,EACvC,IAAI,CAAClF,OAAO,CAAC5D,IAAI,CAAC2I,GAClB,AAAC,CAAA,IAAI,CAACI,UAAU,EAAI,EAAE,AAAD,EAAGvJ,OAAO,CAAC,SAAUwJ,CAAK,EACvCA,GACAA,EAAMhJ,IAAI,CAAC,CACP8H,QAASa,EAAQb,OAAO,AAC5B,EAER,IAGZ,CACJ,CACAd,EAAyBE,EAAkB7K,SAAS,CAAE,CAClDsH,SAAU+C,EAAwBzD,YAAY,AAClD,GAmwB6B,IAAMiH,EATV,CACrBC,WA7JJ,SAAoB/E,CAAI,CAAEgF,CAAK,CAAEC,CAAU,CAAEC,CAAS,EAClD,IAAMC,EAAanF,EAAK8C,OAAO,GAAIsC,EAAc,AAACH,EAAW9B,CAAC,CAAG+B,EAAaF,EAAOK,EAAc,AAACJ,EAAWrF,CAAC,CAAGsF,EAAaF,CAC3HhF,CAAAA,EAAK5B,QAAQ,CAAChF,aAAa,GAC5B4G,EAAK5B,QAAQ,CAACkH,KAAK,EACfF,EAAcD,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,CAC5DvF,EAAK5B,QAAQ,CAACoH,KAAK,EACfH,EAAcF,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,EAE3DvF,EAAK3B,MAAM,CAACjF,aAAa,GAC1B4G,EAAK3B,MAAM,CAACiH,KAAK,EACbF,EAAcD,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CACxDvF,EAAK3B,MAAM,CAACmH,KAAK,EACbH,EAAcF,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CAEhE,EAgJIE,wBAjIJ,SAAiCpP,CAAC,CAAEqP,CAAC,EACjC,OAAOrP,EAAIA,EAAIqP,CACnB,EAgIIC,WAtHJ,WACI,IAAMC,EAAwB,IAAI,CAACrN,OAAO,CAACqN,qBAAqB,CAAEC,EAAU,IAAI,CAACF,UAAU,CAACE,OAAO,CAAEC,EAAU,IAAI,CAACH,UAAU,CAACG,OAAO,CACtI,IAAI,CAACvI,KAAK,CAACnD,OAAO,CAAC,SAAUuD,CAAI,EAC7B,GAAI,CAACA,EAAKvE,aAAa,CAAE,CACrB,IAAMmM,EAAS5H,EAAKsE,SAAS,GAAI8D,EAAMR,EAAU,CAAA,EAAIA,EAAS,CAAA,CAC9D5H,CAAAA,EAAK2H,KAAK,EAAK,AAACO,CAAAA,EAAUlI,EAAKpE,KAAK,AAAD,EAC/BqM,EACAG,EAAMpI,EAAK4H,MAAM,CACrB5H,EAAK6H,KAAK,EAAK,AAACM,CAAAA,EAAUnI,EAAKnE,KAAK,AAAD,EAC/BoM,EACAG,EAAMpI,EAAK4H,MAAM,AACzB,CACJ,EACJ,EA0GIS,KApGJ,SAAc3L,CAAM,EAChB,OAAON,KAAKkM,GAAG,CAAC5L,EAAO6L,GAAG,CAAC5D,KAAK,CAAGjI,EAAO6L,GAAG,CAACC,MAAM,CAAG9L,EAAOkD,KAAK,CAACK,MAAM,CAAE,GAChF,EAmGIwI,UAlEJ,SAAmB/L,CAAM,CAAEsD,CAAI,EAC3BA,EAAK2H,KAAK,EACN3H,EAAK2H,KAAK,CAAGjL,EAAO9B,OAAO,CAAC8N,QAAQ,CACxC1I,EAAK6H,KAAK,EACN7H,EAAK6H,KAAK,CAAGnL,EAAO9B,OAAO,CAAC8N,QAAQ,CACxC,IAAMnB,EAAYvH,EAAK3B,WAAW,CAAG3B,EAAOiM,YAAY,CAAC,CACrDnD,EAAGxF,EAAK2H,KAAK,CACb1F,EAAGjC,EAAK6H,KAAK,AACjB,EACkB,CAAA,IAAdN,IACAvH,EAAKpE,KAAK,EAAKoE,EAAK2H,KAAK,CAAGJ,EACxBnL,KAAKwM,GAAG,CAACxM,KAAKC,GAAG,CAAC2D,EAAK2H,KAAK,EAAGjL,EAAO2B,WAAW,EACrD2B,EAAKnE,KAAK,EAAKmE,EAAK6H,KAAK,CAAGN,EACxBnL,KAAKwM,GAAG,CAACxM,KAAKC,GAAG,CAAC2D,EAAK6H,KAAK,EAAGnL,EAAO2B,WAAW,EAE7D,EAoDIwK,UAxCJ,SAAmB7I,CAAI,CAAEqH,CAAK,CAAEC,CAAU,CAAEC,CAAS,EACjDvH,EAAK2H,KAAK,EACN,AAACL,EAAW9B,CAAC,CAAG+B,EAAaF,EAAQrH,EAAK4H,MAAM,CACpD5H,EAAK6H,KAAK,EACN,AAACP,EAAWrF,CAAC,CAAGsF,EAAaF,EAAQrH,EAAK4H,MAAM,AACxD,EAoCIkB,uBAfJ,SAAgCpQ,CAAC,CAAEqP,CAAC,EAChC,OAAOA,EAAIA,EAAIrP,CACnB,CAcA,CAgCA,OAAMqQ,EAMFxD,YAAYgD,CAAG,CAAE,CAab,IAAI,CAACS,IAAI,CAAG,CAAA,EASZ,IAAI,CAACC,OAAO,CAAG,CAAA,EASf,IAAI,CAACC,UAAU,CAAG,CAAA,EASlB,IAAI,CAACtJ,KAAK,CAAG,EAAE,CAOf,IAAI,CAAC2I,GAAG,CAAGA,EAOX,IAAI,CAACY,OAAO,CAAG/M,KAAKwM,GAAG,CAACL,EAAI5D,KAAK,CAAE4D,EAAIC,MAAM,CACjD,CAuBAY,WAAY,CACR,IAAMC,EAAY,IAAI,CAACd,GAAG,CAAC5D,KAAK,CAAG,EAAG2E,EAAa,IAAI,CAACf,GAAG,CAACC,MAAM,CAAG,CAErE,CAAA,IAAI,CAAC5I,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CACnBsE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CACjB5E,MAAO0E,EACPb,OAAQc,CACZ,GAEA,IAAI,CAAC1J,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CAAGoE,EACtBE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CACjB5E,MAAO0E,EACPb,OAAQc,CACZ,GAEA,IAAI,CAAC1J,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CAAGoE,EACtBE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CAAGD,EACpB3E,MAAO0E,EACPb,OAAQc,CACZ,GAEA,IAAI,CAAC1J,KAAK,CAAC,EAAE,CAAG,IAAImJ,EAAa,CAC7B9D,KAAM,IAAI,CAACsD,GAAG,CAACtD,IAAI,CACnBsE,IAAK,IAAI,CAAChB,GAAG,CAACgB,GAAG,CAAGD,EACpB3E,MAAO0E,EACPb,OAAQc,CACZ,EACJ,CAMAE,eAAenP,CAAK,CAAE,CAClB,IAAM4K,EAAO5K,EAAMuB,KAAK,CAAG,IAAI,CAAC2M,GAAG,CAACtD,IAAI,CAAG,IAAI,CAACsD,GAAG,CAAC5D,KAAK,CAAG,EAAG4E,EAAMlP,EAAMwB,KAAK,CAAG,IAAI,CAAC0M,GAAG,CAACgB,GAAG,CAAG,IAAI,CAAChB,GAAG,CAACC,MAAM,CAAG,EAsBpH,OApBIvD,EAOY,GANRsE,EAUAA,EAEQ,EAIA,CAIpB,CAUAE,OAAOpP,CAAK,CAAEqP,CAAK,CAAE,CACjB,IAAIC,CACA,CAAA,IAAI,CAACT,UAAU,CAEf,IAAI,CAACtJ,KAAK,CAAC,IAAI,CAAC4J,cAAc,CAACnP,GAAO,CAACoP,MAAM,CAACpP,EAAOqP,EAAQ,IAG7D,IAAI,CAACT,OAAO,CAAG,CAAA,EACV,IAAI,CAACD,IAAI,CAMNU,GAEA,IAAI,CAACR,UAAU,CAAG,CAAA,EAClB,IAAI,CAACE,SAAS,GAEI,CAAA,IAAd,IAAI,CAACJ,IAAI,GACT,IAAI,CAACpJ,KAAK,CAAC,IAAI,CAAC4J,cAAc,CAAC,IAAI,CAACR,IAAI,EAAE,CACrCS,MAAM,CAAC,IAAI,CAACT,IAAI,CAAEU,EAAQ,GAC/B,IAAI,CAACV,IAAI,CAAG,CAAA,GAGhB,IAAI,CAACpJ,KAAK,CAAC,IAAI,CAAC4J,cAAc,CAACnP,GAAO,CACjCoP,MAAM,CAACpP,EAAOqP,EAAQ,KAkB3BC,AAPAA,CAAAA,EAAkB,IAAIZ,EAAa,CAC/BQ,IAAKlP,EAAMuB,KAAK,EAAIgO,IACpB3E,KAAM5K,EAAMwB,KAAK,EAAI+N,IAErBjF,MAAO,GACP6D,OAAQ,EACZ,EAAC,EACeQ,IAAI,CAAG3O,EACvBsP,EAAgBT,UAAU,CAAG,CAAA,EAC7B,IAAI,CAACtJ,KAAK,CAAC8B,IAAI,CAACiI,KApCpB,IAAI,CAACT,UAAU,CAAG,CAAA,EAClB,IAAI,CAACF,IAAI,CAAG3O,GAuCxB,CAKAwP,qBAAsB,CAClB,IAAI9G,EAAO,EAAGnH,EAAQ,EAAGC,EAAQ,EACjC,GAAI,IAAI,CAACqN,UAAU,CAAE,CAEjB,IAAK,IAAMY,KAAa,IAAI,CAAClK,KAAK,CACzBkK,EAAUb,OAAO,GAClBlG,GAAQ+G,EAAU/G,IAAI,CACtBnH,GAASkO,EAAUlO,KAAK,CAAGkO,EAAU/G,IAAI,CACzClH,GAASiO,EAAUjO,KAAK,CAAGiO,EAAU/G,IAAI,EAGjDnH,GAASmH,EACTlH,GAASkH,CACb,MACS,IAAI,CAACiG,IAAI,GAEdjG,EAAO,IAAI,CAACiG,IAAI,CAACjG,IAAI,CACrBnH,EAAQ,IAAI,CAACoN,IAAI,CAACpN,KAAK,CACvBC,EAAQ,IAAI,CAACmN,IAAI,CAACnN,KAAK,CAG3B,CAAA,IAAI,CAACkH,IAAI,CAAGA,EACZ,IAAI,CAACnH,KAAK,CAAGA,EACb,IAAI,CAACC,KAAK,CAAGA,CACjB,CACJ,CAuJ6B,IAAMkO,EA3GnC,MAMIxE,YAAYC,CAAC,CAAEvD,CAAC,CAAE0C,CAAK,CAAE6D,CAAM,CAAE,CAE7B,IAAI,CAACD,GAAG,CAAG,CACPtD,KAAMO,EACN+D,IAAKtH,EACL0C,MAAOA,EACP6D,OAAQA,CACZ,EACA,IAAI,CAACwB,QAAQ,CAAG,GAChB,IAAI,CAACC,IAAI,CAAG,IArD4ClB,EAqDd,IAAI,CAACR,GAAG,EAClD,IAAI,CAAC0B,IAAI,CAACf,UAAU,CAAG,CAAA,EACvB,IAAI,CAACe,IAAI,CAACC,MAAM,CAAG,CAAA,EACnB,IAAI,CAACD,IAAI,CAACb,SAAS,EACvB,CASAe,wBAAyB,CACrB,IAAI,CAACC,kBAAkB,CAAC,KAAM,KAAM,SAAUpK,CAAI,EAC9CA,EAAK6J,mBAAmB,EAC5B,EACJ,CAOAQ,YAAY1K,CAAM,CAAE,CAChB,IAAK,IAAMtF,KAASsF,EAChB,IAAI,CAACsK,IAAI,CAACR,MAAM,CAACpP,EAAO,IAAI,CAAC2P,QAAQ,CAE7C,CAwBAI,mBAAmBpK,CAAI,CAAEsK,CAAc,CAAEC,CAAa,CAAE,CACpD,IAAIC,EAOJ,GANKxK,GACDA,CAAAA,EAAO,IAAI,CAACiK,IAAI,AAAD,EAEfjK,IAAS,IAAI,CAACiK,IAAI,EAAIK,GACtBE,CAAAA,EAAYF,EAAetK,EAAI,EAE/BwK,AAAc,CAAA,IAAdA,GAGJ,IAAK,IAAMC,KAAUzK,EAAKJ,KAAK,CAAE,CAC7B,GAAI6K,EAAOvB,UAAU,CAAE,CAInB,GAHIoB,GACAE,CAAAA,EAAYF,EAAeG,EAAM,EAEjCD,AAAc,CAAA,IAAdA,EACA,SAEJ,IAAI,CAACJ,kBAAkB,CAACK,EAAQH,EAAgBC,EACpD,MACSE,EAAOzB,IAAI,EACZsB,GACAA,EAAeG,EAAOzB,IAAI,EAG9BuB,GACAA,EAAcE,EAEtB,CACIzK,IAAS,IAAI,CAACiK,IAAI,EAAIM,GACtBA,EAAcvK,GAEtB,CACJ,EAqMmC0K,EATT,CACtBtD,WArJJ,SAAsC/E,CAAI,CAAEgF,CAAK,CAAEC,CAAU,EACzD,IAAME,EAAanF,EAAK8C,OAAO,GAAIsC,EAAc,CAACH,EAAW9B,CAAC,CAAG6B,EAAQ,IAAI,CAACsD,eAAe,CAAEjD,EAAc,CAACJ,EAAWrF,CAAC,CAAGoF,EAAQ,IAAI,CAACsD,eAAe,AACpJtI,CAAAA,EAAK5B,QAAQ,CAAChF,aAAa,GAC5B4G,EAAK5B,QAAQ,CAAC7E,KAAK,EACf6L,EAAcD,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,CAC5DvF,EAAK5B,QAAQ,CAAC5E,KAAK,EACf6L,EAAcF,EAAW/G,QAAQ,CAAG4B,EAAK5B,QAAQ,CAACmH,MAAM,EAE3DvF,EAAK3B,MAAM,CAACjF,aAAa,GAC1B4G,EAAK3B,MAAM,CAAC9E,KAAK,EACb6L,EAAcD,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CACxDvF,EAAK3B,MAAM,CAAC7E,KAAK,EACb6L,EAAcF,EAAW9G,MAAM,CAAG2B,EAAK3B,MAAM,CAACkH,MAAM,CAEhE,EAwIIE,wBA9HJ,SAAmDpP,CAAC,CAAEqP,CAAC,EAEnD,MAAO,AAACA,CAAAA,EAAIrP,CAAAA,EAAKA,CACrB,EA4HIsP,WAlHJ,WACI,IAAMC,EAAwB,IAAI,CAACrN,OAAO,CAACqN,qBAAqB,EAAI,EAAGC,EAAU,AAAC,CAAA,IAAI,CAACF,UAAU,CAACE,OAAO,CACrG,AAAC,CAAA,IAAI,CAACK,GAAG,CAACtD,IAAI,CAAG,IAAI,CAACsD,GAAG,CAAC5D,KAAK,AAAD,EAAK,CAAA,EAAKsD,EAAuBE,EAAU,AAAC,CAAA,IAAI,CAACH,UAAU,CAACG,OAAO,CACjG,AAAC,CAAA,IAAI,CAACI,GAAG,CAACgB,GAAG,CAAG,IAAI,CAAChB,GAAG,CAACC,MAAM,AAAD,EAAK,CAAA,EAAKP,EAC5C,IAAI,CAACrI,KAAK,CAACnD,OAAO,CAAC,SAAUuD,CAAI,EACxBA,EAAKvE,aAAa,GACnBuE,EAAKpE,KAAK,EACNsM,EAAUlI,EAAK+C,IAAI,CAAG/C,EAAK4H,MAAM,CACrC5H,EAAKnE,KAAK,EACNsM,EAAUnI,EAAK+C,IAAI,CAAG/C,EAAK4H,MAAM,CAE7C,EACJ,EAuGIS,KAjGJ,SAAgC3L,CAAM,EAClC,OAAON,KAAKkM,GAAG,CAAC5L,EAAO6L,GAAG,CAAC5D,KAAK,CAAGjI,EAAO6L,GAAG,CAACC,MAAM,CAAG9L,EAAOkD,KAAK,CAACK,MAAM,CAAE,GAChF,EAgGIwI,UAhEJ,SAAqC/L,CAAM,CAAEsD,CAAI,EAC7C,IAAM0I,EAAW,CAAChM,EAAO9B,OAAO,CAAC8N,QAAQ,CAAEkC,EAAWlO,EAAO9B,OAAO,CAACgQ,QAAQ,CAAEC,EAAQ7K,EAAK6K,KAAK,CAAEC,EAAQ9K,EAAK8K,KAAK,CAErHC,EAAa,AAAC/K,CAAAA,EAAKpE,KAAK,CAAGoE,EAAK2H,KAAK,CACjCkD,CAAI,EAAKnC,EAAWsC,EAAa,AAAChL,CAAAA,EAAKnE,KAAK,CAAGmE,EAAK6H,KAAK,CACzDiD,CAAI,EAAKpC,EAAWrM,EAAMD,KAAKC,GAAG,CAAE4O,EAAQ5O,EAAI0O,GAAcA,CAAAA,GAAa,CAAA,EAC/EG,EAAQ7O,EAAI2O,GAAcA,CAAAA,GAAa,CAAA,EAEvC/O,EAAQgP,EAAQ7O,KAAKwM,GAAG,CAACgC,EAAUxO,KAAKC,GAAG,CAAC0O,IAAa7O,EAAQgP,EAAQ9O,KAAKwM,GAAG,CAACgC,EAAUxO,KAAKC,GAAG,CAAC2O,GAErGhL,CAAAA,EAAK6K,KAAK,CAAG7K,EAAKpE,KAAK,CAAGoE,EAAK2H,KAAK,CACpC3H,EAAK8K,KAAK,CAAG9K,EAAKnE,KAAK,CAAGmE,EAAK6H,KAAK,CAEpC7H,EAAKpE,KAAK,EAAIK,EACd+D,EAAKnE,KAAK,EAAIK,EACd8D,EAAK3B,WAAW,CAAG3B,EAAOiM,YAAY,CAAC,CACnCnD,EAAGvJ,EACHgG,EAAG/F,CACP,EACJ,EA8CI2M,UA/BJ,SAAqC7I,CAAI,CAAEqH,CAAK,CAAEC,CAAU,EACxD,IAAM6D,EAAS9D,EAAQ,IAAI,CAACsD,eAAe,CAAG3K,EAAK+C,IAAI,CAAG/C,EAAK4H,MAAM,AAChE5H,CAAAA,EAAKvE,aAAa,GACnBuE,EAAKpE,KAAK,EAAI0L,EAAW9B,CAAC,CAAG2F,EAC7BnL,EAAKnE,KAAK,EAAIyL,EAAWrF,CAAC,CAAGkJ,EAErC,EA0BIrC,uBAhBJ,SAAkDpQ,CAAC,CAAEqP,CAAC,EAElD,MAAO,AAACA,CAAAA,EAAIrP,CAAAA,EAAKA,EAAKqP,CAAAA,CAAAA,EAAIrP,CAAAA,CAC9B,CAcA,EAkBM,CAAE0S,IAAAA,CAAG,CAAE,CAAI1R,IAIX,CAAE2R,MAAAA,CAAK,CAAElM,QAASmM,CAAiC,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAEjM,KAAMkM,CAA8B,CAAE,CAAI/R,GAY5H,OAAMgS,EACFnG,aAAc,CAMV,IAAI,CAACgD,GAAG,CAAG,CAAC,EACZ,IAAI,CAACoD,WAAW,CAAG,EACnB,IAAI,CAACC,gBAAgB,CAAG,CAAA,EACxB,IAAI,CAACC,KAAK,CAAG,EAAE,CACf,IAAI,CAACjM,KAAK,CAAG,EAAE,CACf,IAAI,CAAClF,MAAM,CAAG,EAAE,CAChB,IAAI,CAACoR,UAAU,CAAG,CAAA,CACtB,CACA,OAAO1Q,QAAQC,CAAU,CAAE,CACvBqD,EAA8BtD,OAAO,CAACC,GACtCqD,EAA8BC,YAAY,CAACoN,KAAK,CAAG5E,EACnDzI,EAA8BC,YAAY,CAACqN,MAAM,CAAGtB,EACpDhM,EAA8BE,OAAO,CAAC,uBAAuB,CACzD8M,CACR,CACAO,KAAKrR,CAAO,CAAE,CACV,IAAI,CAACA,OAAO,CAAGA,EACf,IAAI,CAACgF,KAAK,CAAG,EAAE,CACf,IAAI,CAACiM,KAAK,CAAG,EAAE,CACf,IAAI,CAACnR,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC6N,GAAG,CAAG,CACP/C,EAAG,EACHvD,EAAG,EACH0C,MAAO,EACP6D,OAAQ,CACZ,EACA,IAAI,CAAC0D,mBAAmB,CAAC,CAAA,GACzB,IAAI,CAACC,WAAW,CACZzN,EAA8BC,YAAY,CAAC/D,EAAQuR,WAAW,CAAC,CACnE,IAAI,CAACvP,gBAAgB,CAAGhC,EAAQgC,gBAAgB,CAChD,IAAI,CAACwP,eAAe,CAAGX,EAA+B7Q,EAAQwR,eAAe,CAAE,IAAI,CAACD,WAAW,CAACrE,uBAAuB,EACvH,IAAI,CAACuE,cAAc,CAAGZ,EAA+B7Q,EAAQyR,cAAc,CAAE,IAAI,CAACF,WAAW,CAACrD,sBAAsB,EACpH,IAAI,CAACwD,aAAa,CAAG1R,EAAQ0R,aAAa,AAC9C,CACA3O,iBAAiB4O,CAAM,CAAE,CACrB,IAAI,CAAC3P,gBAAgB,CAAG6O,EAA+Bc,EAAQ,IAAI,CAAC3R,OAAO,CAACgC,gBAAgB,CAChG,CACAC,OAAQ,CACJ,IAAqBnC,EAAS,IAAI,CAACA,MAAM,CAAEE,EAAU,IAAI,CAACA,OAAO,AACjE8B,CADe,IAAI,CACZiP,WAAW,CAAG,EACrBjP,AAFe,IAAI,CAEZ8P,MAAM,CAAG9R,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC8R,MAAM,EAAI,EAAE,CACnD9P,AAHe,IAAI,CAGZpC,KAAK,CAAGI,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACJ,KAAK,CACvCoC,AAJW,IAAI,CAIRkP,gBAAgB,GACvBlP,AALW,IAAI,CAKR+P,aAAa,GAEpB/R,EAAO+B,OAAO,CAAC,SAAUiQ,CAAC,EACtBA,EAAEC,iBAAiB,CAAG,CAAA,EACtBD,EAAEjO,MAAM,EACZ,IAEJ/B,AAZe,IAAI,CAYZkQ,IAAI,GACXlQ,AAbe,IAAI,CAaZmQ,eAAe,CAACjS,GACnB8B,AAdW,IAAI,CAcRE,gBAAgB,EACvBF,AAfW,IAAI,CAeR8B,IAAI,EAEnB,CACAA,MAAO,CACH,IAAwBsO,EAAY,IAAI,CAACpS,MAAM,CAO/C,IAAK,IAAMqS,KALX,IAAI,CAACpB,WAAW,GACW,eAAvB,IAAI,CAACW,aAAa,GAClB,IAAI,CAACU,cAAc,GACnB,IAAI,CAACC,QAAQ,CAAC9C,sBAAsB,IAEhB,IAAI,CAACqC,MAAM,EAAI,EAAE,EACrCU,AARc,IAAI,AAQT,CAACH,EAAY,SAAS,CAAC,IAAI,CAAC1O,WAAW,EAQpD,GALA,IAAI,CAAC8O,WAAW,GAEhB,IAAI,CAAC9O,WAAW,CAAG,IAAI,CAAC+O,QAAQ,CAAC,IAAI,CAACC,gBAAgB,CAAE,IAAI,CAAC1C,eAAe,CAAE,IAAI,CAACgB,WAAW,EAC9F,IAAI,CAAC2B,qBAAqB,CAAG,IAAI,CAACC,iBAAiB,CACnD,IAAI,CAACA,iBAAiB,CAAG,IAAI,CAACC,oBAAoB,GAC9C,IAAI,CAAC5Q,gBAAgB,CAAE,CACvB,IAAK,IAAMlC,KAAUoS,EAEbpS,EAAOJ,KAAK,EACZI,EAAO+D,MAAM,EAGjB,CAAA,IAAI,CAACN,aAAa,IAClBC,SAAS,IAAI,CAACC,WAAW,GACzB,CAAC,IAAI,CAACC,QAAQ,IACV,IAAI,CAACwN,UAAU,EACfV,EAAIqC,oBAAoB,CAAC,IAAI,CAAC3B,UAAU,EAE5C,IAAI,CAACA,UAAU,CAAGV,EAAIsC,qBAAqB,CAAC,IAAM,IAAI,CAAClP,IAAI,MAG3D,IAAI,CAACsN,UAAU,CAAG,CAAA,EAClB,IAAI,CAACpR,MAAM,CAAC+B,OAAO,CAAC,AAACiQ,IACjBlB,EAAUkB,EAAG,kBACjB,GAER,CACJ,CACA5O,MAAO,CACC,IAAI,CAACgO,UAAU,EACfV,EAAIqC,oBAAoB,CAAC,IAAI,CAAC3B,UAAU,CAEhD,CACA6B,QAAQnI,CAAC,CAAEvD,CAAC,CAAE2L,CAAC,CAAEC,CAAC,CAAE,CAChB,IAAI,CAACtF,GAAG,CAAG,CACPtD,KAAMO,EACN+D,IAAKtH,EACL0C,MAAOiJ,EACPpF,OAAQqF,CACZ,CACJ,CACAjB,MAAO,CAGH,IAAI,CAAC7E,CAAC,CAAG,IAAI,CAACnN,OAAO,CAACkT,UAAU,EAAI,IAAI,CAAC3B,WAAW,CAAC9D,IAAI,CAAC,IAAI,CAClE,CACA0F,wBAAwBC,CAAQ,CAAEC,CAAU,CAAE,CAC1C,IAAK,IAAMC,KAAWF,EACkB,KAAhCC,EAAW1H,OAAO,CAAC2H,IACnBD,EAAWvM,IAAI,CAACwM,EAG5B,CACA7J,4BAA4B6J,CAAO,CAAED,CAAU,CAAE,CAC7C,IAAM9M,EAAQ8M,EAAW1H,OAAO,CAAC2H,EACnB,CAAA,KAAV/M,GACA8M,EAAWzH,MAAM,CAACrF,EAAO,EAEjC,CACAgN,OAAQ,CACJ,IAAI,CAACvO,KAAK,CAACK,MAAM,CAAG,EACpB,IAAI,CAAC4L,KAAK,CAAC5L,MAAM,CAAG,EACpB,IAAI,CAACvF,MAAM,CAACuF,MAAM,CAAG,EACrB,IAAI,CAAC4M,eAAe,EACxB,CACAA,iBAAkB,CACd,IAAI,CAACuB,UAAU,CAAG,CAAA,EAClB,IAAI,CAACb,iBAAiB,CAAG,EACzB,IAAI,CAACc,gBAAgB,GACrB,IAAI,CAACC,cAAc,GACnB,IAAI,CAACC,kBAAkB,EAC3B,CACA5R,mBAAoB,CACX,IAAI,CAACmP,UAAU,CAoBhB,IAAI,CAACe,eAAe,IAjBpB,IAAI,CAACX,mBAAmB,CAAC,CAAA,GAEpB,IAAI,CAACtP,gBAAgB,CAKtB,IAAI,CAACC,KAAK,GAHV,IAAI,CAACwR,gBAAgB,CAAC,GAKtB,IAAI,CAAC/T,KAAK,EACV,IAAI,CAACA,KAAK,CAACwC,MAAM,GAGrB,IAAI,CAACoP,mBAAmB,CAAC,CAAA,GAMjC,CACAmC,iBAAiBlQ,CAAa,CAAE,CAC5B,IAAI,CAACA,aAAa,CAAGsN,EAA+BtN,EAAe,IAAI,CAACvD,OAAO,CAACuD,aAAa,CACjG,CACAmQ,gBAAiB,CACb,IAAI,CAACjQ,WAAW,CAAG,IAAI,CAACgP,gBAAgB,CACpCjR,KAAKoS,IAAI,CAAC,IAAI,CAAC5O,KAAK,CAACK,MAAM,CACnC,CACAsO,oBAAqB,CACjB,IAAI,CAAC5D,eAAe,CAAG,IAAI,CAAC0C,gBAAgB,CACvC,CAAA,IAAI,CAACzS,OAAO,CAACuD,aAAa,CAAG,CAAA,CACtC,CACA+N,oBAAoBK,CAAM,CAAE,CACxB,IAAI,CAACX,gBAAgB,CAAGW,CAC5B,CACAS,gBAAiB,CACb,IAAI,CAACC,QAAQ,CAAG,IAAIlD,EAAsB,IAAI,CAACxB,GAAG,CAACtD,IAAI,CAAE,IAAI,CAACsD,GAAG,CAACgB,GAAG,CAAE,IAAI,CAAChB,GAAG,CAAC5D,KAAK,CAAE,IAAI,CAAC4D,GAAG,CAACC,MAAM,EACtG,IAAI,CAACyE,QAAQ,CAAC5C,WAAW,CAAC,IAAI,CAACzK,KAAK,CACxC,CACA6M,eAAgB,CACZ,IAAMgC,EAAmB,IAAI,CAAC7T,OAAO,CAAC6T,gBAAgB,CACtD,GAAIlD,EAAWkD,GAEX,IAAK,IAAMzO,KADXyO,EAAiBjV,IAAI,CAAC,IAAI,EACP,IAAI,CAACoG,KAAK,EACpB0L,EAAkCtL,EAAK6K,KAAK,GAC7C7K,CAAAA,EAAK6K,KAAK,CAAG7K,EAAKpE,KAAK,AAAD,EAErB0P,EAAkCtL,EAAK8K,KAAK,GAC7C9K,CAAAA,EAAK8K,KAAK,CAAG9K,EAAKnE,KAAK,AAAD,EAE1BmE,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,MAGZ4G,AAAqB,WAArBA,EACL,IAAI,CAACC,oBAAoB,GAGzB,IAAI,CAACC,kBAAkB,EAE/B,CACAD,sBAAuB,CACnB,IA+BI1O,EA/BEuI,EAAM,IAAI,CAACA,GAAG,CAAE3I,EAAQ,IAAI,CAACA,KAAK,CAAkCgP,EAAQ,EAAIxS,KAAKyS,EAAE,CAArCjP,CAAAA,EAAMK,MAAM,CAAG,CAAA,EAAsC6O,EAAYlP,EAAMmP,MAAM,CAAC,SAAU/O,CAAI,EAChJ,OAAOA,AAAwB,IAAxBA,EAAKO,OAAO,CAACN,MAAM,AAC9B,GAAI+O,EAAe,CAAC,EAAG/L,EAAS,IAAI,CAACrI,OAAO,CAACqU,qBAAqB,CAAEC,EAAa,AAAClP,IAC9E,IAAK,IAAMqC,KAAQrC,EAAKQ,SAAS,EAAI,EAAE,CAC9BwO,CAAY,CAAC3M,EAAK3B,MAAM,CAACc,EAAE,CAAC,GAC7BwN,CAAY,CAAC3M,EAAK3B,MAAM,CAACc,EAAE,CAAC,CAAG,CAAA,EAC/B2N,EAAYzN,IAAI,CAACW,EAAK3B,MAAM,EAC5BwO,EAAW7M,EAAK3B,MAAM,EAGlC,EACIyO,EAAc,EAAE,CAIpB,IAAK,IAAMC,KAAYN,EACnBK,EAAYzN,IAAI,CAAC0N,GACjBF,EAAWE,GAGf,GAAKD,EAAYlP,MAAM,CAKnB,IAAK,IAAMD,KAAQJ,EACmB,KAA9BuP,EAAY5I,OAAO,CAACvG,IACpBmP,EAAYzN,IAAI,CAAC1B,QANzBmP,EAAcvP,EAalB,IAAK,IAAI8C,EAAI,EAAG2M,EAAOF,EAAYlP,MAAM,CAAEyC,EAAI2M,EAAM,EAAE3M,EAEnD1C,AADAA,CAAAA,EAAOmP,CAAW,CAACzM,EAAE,AAAD,EACf9G,KAAK,CAAGoE,EAAK6K,KAAK,CAAGY,EAA+BzL,EAAKpE,KAAK,CAAE2M,EAAI5D,KAAK,CAAG,EAAI1B,EAAS7G,KAAKkT,GAAG,CAAC5M,EAAIkM,IAC3G5O,EAAKnE,KAAK,CAAGmE,EAAK8K,KAAK,CAAGW,EAA+BzL,EAAKnE,KAAK,CAAE0M,EAAIC,MAAM,CAAG,EAAIvF,EAAS7G,KAAKmT,GAAG,CAAC7M,EAAIkM,IAC5G5O,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,CAErB,CACA8G,oBAAqB,CACjB,IAWI3O,EAXEuI,EAAM,IAAI,CAACA,GAAG,CAAE3I,EAAQ,IAAI,CAACA,KAAK,CAAE4P,EAAc5P,EAAMK,MAAM,CAAG,EAMvEwP,EAAW,AAACnX,IACR,IAAIoX,EAAOpX,EAAIA,EAAI8D,KAAKyS,EAAE,CAE1B,OADAa,EAActT,KAAKuT,KAAK,CAACD,EAE7B,EAGA,IAAK,IAAIhN,EAAI,EAAG2M,EAAOzP,EAAMK,MAAM,CAAEyC,EAAI2M,EAAM,EAAE3M,EAE7C1C,AADAA,CAAAA,EAAOJ,CAAK,CAAC8C,EAAE,AAAD,EACT9G,KAAK,CAAGoE,EAAK6K,KAAK,CAAGY,EAA+BzL,EAAKpE,KAAK,CAAE2M,EAAI5D,KAAK,CAAG8K,EAAS/M,IAC1F1C,EAAKnE,KAAK,CAAGmE,EAAK8K,KAAK,CAAGW,EAA+BzL,EAAKnE,KAAK,CAAE0M,EAAIC,MAAM,CAAGiH,EAASD,EAAc9M,IACzG1C,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,CAErB,CACAR,MAAMvE,CAAI,CAAE,GAAG1C,CAAI,CAAE,CACjB,IAAI,CAAC+L,WAAW,CAACrJ,EAAK,CAACjD,KAAK,CAAC,IAAI,CAAEO,EACvC,CACAwP,kBAAmB,CACf,IAAI,CAACC,aAAa,GAClB,IAAI,CAACxI,KAAK,CAAC,aACf,CACAwI,eAAgB,CACZ,IAAIC,EAAa,EAAGC,EAAK,EAAGC,EAAK,EACjC,IAAK,IAAMhQ,KAAQ,IAAI,CAACJ,KAAK,CACzBmQ,GAAM/P,EAAKpE,KAAK,CAAGoE,EAAK+C,IAAI,CAC5BiN,GAAMhQ,EAAKnE,KAAK,CAAGmE,EAAK+C,IAAI,CAC5B+M,GAAc9P,EAAK+C,IAAI,CAQ3B,OANA,IAAI,CAACiF,UAAU,CAAG,CACdxC,EAAGuK,EACH9N,EAAG+N,EACH9H,QAAS6H,EAAKD,EACd3H,QAAS6H,EAAKF,CAClB,EACO,IAAI,CAAC9H,UAAU,AAC1B,CACAiI,uBAAuBjQ,CAAI,CAAEkQ,CAAQ,CAAE,CACnC,IACIC,EAAU9I,EADRC,EAAa,IAAI,CAAC8I,SAAS,CAACpQ,EAAMkQ,GAAW3I,EAAY,IAAI,CAACoB,YAAY,CAACrB,GAwBjF,OAtBItH,IAASkQ,GAAY3I,AAAc,IAAdA,IACjB2I,EAAShH,UAAU,CAEfgH,EAAS/G,OAAO,CAAG5B,EACnB,IAAI,CAAC3M,OAAO,CAACyV,KAAK,EAClB9I,AAAc,IAAdA,GAEAF,EAAQ,IAAI,CAACgF,cAAc,CAAC9E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAarH,EAAMqH,EAAQ6I,EAASnN,IAAI,CAAEuE,EAAYC,GACjE4I,EAAW,CAAA,GAIXA,EAAW,CAAA,GAKf9I,EAAQ,IAAI,CAACgF,cAAc,CAAC9E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAarH,EAAMqH,EAAQ6I,EAASnN,IAAI,CAAEuE,EAAYC,KAGlE4I,CACX,CACAG,iBAAkB,CACd,GAAI,AAAuB,eAAvB,IAAI,CAAChE,aAAa,CAClB,IAAK,IAAMtM,KAAQ,IAAI,CAACJ,KAAK,CACzB,IAAI,CAACqN,QAAQ,CAAC7C,kBAAkB,CAAC,KAAM,AAAC8F,GAAc,IAAI,CAACD,sBAAsB,CAACjQ,EAAMkQ,QAG3F,CACD,IAAI7I,EAAOE,EAAWD,EACtB,IAAK,IAAMtH,KAAQ,IAAI,CAACJ,KAAK,CACzB,IAAK,IAAM2Q,KAAW,IAAI,CAAC3Q,KAAK,CAG5BI,IAASuQ,GAIJvQ,EAAKvE,aAAa,GACnB6L,EAAa,IAAI,CAAC8I,SAAS,CAACpQ,EAAMuQ,GAEhB,IADlBhJ,CAAAA,EAAY,IAAI,CAACoB,YAAY,CAACrB,EAAU,IAEpCD,EAAQ,IAAI,CAACgF,cAAc,CAAC9E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAarH,EAAMqH,EAAQkJ,EAAQxN,IAAI,CAAEuE,EAAYC,IAKpF,CACJ,CACAiJ,kBAAmB,CACf,IAAIlJ,EAAYC,EAAWF,EAC3B,IAAK,IAAMhF,KAAQ,IAAI,CAACwJ,KAAK,CACrBxJ,EAAK5B,QAAQ,EAAI4B,EAAK3B,MAAM,GAC5B4G,EAAa,IAAI,CAAC8I,SAAS,CAAC/N,EAAK5B,QAAQ,CAAE4B,EAAK3B,MAAM,EAEpC,IADlB6G,CAAAA,EAAY,IAAI,CAACoB,YAAY,CAACrB,EAAU,IAEpCD,EAAQ,IAAI,CAAC+E,eAAe,CAAC7E,EAAW,IAAI,CAACQ,CAAC,EAC9C,IAAI,CAACV,KAAK,CAAC,aAAchF,EAAMgF,EAAOC,EAAYC,IAIlE,CACA4F,aAAc,CAEV,IAAK,IAAMnN,KADG,IAAI,CAACJ,KAAK,EAEhBI,EAAKvE,aAAa,GAGtB,IAAI,CAAC0Q,WAAW,CAAC1D,SAAS,CAAC,IAAI,CAAEzI,GACjC,IAAI,CAACyQ,aAAa,CAACzQ,EAAM,IAAI,CAACuI,GAAG,EAEjCvI,EAAK2H,KAAK,CAAG,EACb3H,EAAK6H,KAAK,CAAG,EAErB,CAMA4I,cAAczQ,CAAI,CAAEuI,CAAG,CAAE,CACrB,IAAMtF,EAASjD,EAAKiD,MAAM,AAkC1BjD,CAAAA,EAAKpE,KAAK,CAAGyP,EAAMrL,EAAKpE,KAAK,CAAE2M,EAAItD,IAAI,CAAGhC,EAAQsF,EAAI5D,KAAK,CAAG1B,GAE9DjD,EAAKnE,KAAK,CAAGwP,EAAMrL,EAAKnE,KAAK,CAAE0M,EAAIgB,GAAG,CAAGtG,EAAQsF,EAAIC,MAAM,CAAGvF,EAClE,CAMAmK,SAAS/O,CAAW,CAAEqS,CAAe,CAAE/E,CAAW,CAAE,CAehD,OAAOtN,EAAcqS,EAAkB/E,CAC3C,CACArN,UAAW,CACP,OAAOlC,AAC2B,KAD3BA,KAAKC,GAAG,CAAC,IAAI,CAACkR,iBAAiB,CAClC,IAAI,CAACD,qBAAqB,GAAe,IAAI,CAACjP,WAAW,EAAI,CACrE,CACAmP,sBAAuB,CACnB,IAAImD,EAAQ,EACZ,IAAK,IAAM3Q,KAAQ,IAAI,CAACJ,KAAK,CACzB+Q,GAAS3Q,EAAK3B,WAAW,CAE7B,OAAOsS,CACX,CACAhI,aAAaiI,CAAM,CAAE,CACjB,OAAOxU,KAAKoS,IAAI,CAACoC,EAAOpL,CAAC,CAAGoL,EAAOpL,CAAC,CAAGoL,EAAO3O,CAAC,CAAG2O,EAAO3O,CAAC,CAC9D,CACA4O,SAASC,CAAK,CAAEC,CAAK,CAAE,CACnB,IAAMC,EAAW,IAAI,CAACZ,SAAS,CAACU,EAAOC,GACvC,OAAO,IAAI,CAACpI,YAAY,CAACqI,EAC7B,CACAZ,UAAUU,CAAK,CAAEC,CAAK,CAAE,CACpB,IAAME,EAAQH,EAAMlV,KAAK,CAAGmV,EAAMnV,KAAK,CAAEsV,EAAQJ,EAAMjV,KAAK,CAAGkV,EAAMlV,KAAK,CAC1E,MAAO,CACH2J,EAAGyL,EACHhP,EAAGiP,EACHC,KAAM/U,KAAKC,GAAG,CAAC4U,GACfG,KAAMhV,KAAKC,GAAG,CAAC6U,EACnB,CACJ,CACJ,CAgBA,GAAM,CAAE5R,MAAO+R,CAA+B,CAAEC,YAAAA,CAAW,CAAE,CAAI5X,IAE3D,CAAE6X,WAAAA,CAAU,CAAE,CAAI7X,IAsElB,CAAE8X,QAAAA,CAAO,CAAE,CAAI9X,IACf,CAAEK,SAAU0X,EAAiB,CAAEnS,MAAOoS,EAAc,CAAEC,UAAAA,EAAS,CAAExS,QAASyS,EAAgB,CAAExS,OAAQyS,EAAe,CAAE,CAAInY,IAyB/H,SAASoY,GAAYjM,CAAI,CAAEkM,CAAe,EAEtCA,EAAkBL,GAAe,CAAA,EAAM,CACnCM,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAACvL,QAAQ,CAACuL,GAAG,CAAEC,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAEP,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAlM,EAAOA,GAAS2M,GAAYA,EAAS3M,IAAI,CAErC2M,GACAA,EAASC,IAAI,GAEb5M,GAAQmM,EAAS,CACjB,IAAMS,EAAOhB,GAAkBa,EAAa,kBAAmB,AAACvX,IAC5D,GAAI8K,GAAQmM,EAAS,CAEjB,IAAIU,EAAa7M,EAAK5I,IAAI,CAAC,MACtByV,GACD7M,EAAK5I,IAAI,CAAC,KAAMyV,EAAaf,MAGjC,IAAMgB,EAAc,CAGhBnN,EAAG,EACHvD,EAAG,CACP,EACI2P,GAAiBK,EAAWW,EAAE,IAC9BD,EAAYC,EAAE,CAAGX,EAAWW,EAAE,CAC9B,OAAOX,EAAWW,EAAE,EAEpBhB,GAAiBK,EAAWC,EAAE,IAC9BS,EAAYT,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBI,EAAYrV,IAAI,CAAC0V,GAEjB,IAAI,CAAC1V,IAAI,CAAC,CAAE4V,UAAW,EAAG,GACtB,IAAI,CAACtK,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAC/I,OAAO,EAAC,EAGhC,IAAMsT,EAAW/X,EAAE6E,KAAK,CAAC8G,KAAK,CAAC,EAC/B3L,CAAAA,EAAE6E,KAAK,CAACK,MAAM,CAAG,EACjBlF,EAAE6E,KAAK,CAAC,EAAE,CAAG,CACTmT,QAAS,WACTd,WAAYJ,GAAgBI,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCY,KAAM,CAAC,EAAEX,EAAI,CAAC,EAAEK,EAAW,CAAC,AAChC,GACAI,SAAAA,CACJ,CACJ,CACJ,EAEAR,CAAAA,EAAYE,QAAQ,CAAG,CAAE3M,KAAAA,EAAM4M,KAAAA,CAAK,CACxC,MAEIH,EAAYrV,IAAI,CAAC,CAAE2V,GAAI,EAAGV,GAAI,CAAE,GAChC,OAAOI,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACS,KAAK,GAEVX,EAAYY,SAAS,CAAG,GACxB,IAAI,CAACpM,QAAQ,CAACqM,SAAS,CAACb,IAErB,IAAI,AACf,CAWA,SAASc,GAAW5Y,CAAK,EACrB,IAAM6Y,EAAO7Y,EAAM6Y,IAAI,CAAEC,EAAK,IAAI,CAACpF,OAAO,EAAEqF,cAAc,YAC1D,GAAID,EAAI,CACJ,IAAME,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAE5F,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC/G,QAAQ,CAAC4M,WAAW,CAAC,IAAI,CAACxF,OAAO,EAAGyF,EAAY9F,EAAI4F,EAAGG,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQR,EAC5BS,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAM7T,MAAM,CAIrEkU,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAE7O,EAAAA,CAAC,CAAEvD,EAAAA,CAAC,CAAE,CAAGoS,EAAgBC,EAAW,AAAChB,CAAAA,EAAGiB,iBAAiB,CAACH,GAAa,EAAC,EAAK5C,EAASgD,EAASpY,KAAKkT,GAAG,CAACgF,GAAWG,EAASrY,KAAKmT,GAAG,CAAC+E,GAC7I,MAAO,CACH,CACI9O,EAAImO,EAAYa,EAChBvS,EAAI0R,EAAYc,EACnB,CACD,CACIjP,EAAIiO,EAAIe,EACRvS,EAAIwR,EAAIgB,EACX,CACJ,AACL,EACA,IAAK,IAAI/R,EAAI,EAAGgS,EAAY,EAAGA,EAAYR,EAAYQ,IAAa,CAChE,IAA+BC,EAAUC,AAA5Bd,CAAK,CAACY,EAAU,CAAiBzU,MAAM,CACpD,IAAK,IAAI4U,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBpS,EAClBmS,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGb,EAAmBW,EAAcxB,EAAG2B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACArB,EAAQ9R,IAAI,CAACsT,GACbxB,EAAQ9R,IAAI,CAACqT,KAGK,IAAdL,GACAlB,EAAQ0B,OAAO,CAACF,GAEhBN,IAAcR,EAAa,GAC3BV,EAAQ9R,IAAI,CAACqT,GAGzB,CACA,MAAOha,EAAG,CAGN,KACJ,CAEJ2H,GAAKiS,EAAU,EACf,GAAI,CACA,IAAMG,EAAepS,EAAIgS,EAAWS,EAAU7B,EAAG8B,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGb,EAAmBW,EAAcK,GACvI3B,EAAQ0B,OAAO,CAACF,GAChBxB,EAAQ0B,OAAO,CAACH,EACpB,CACA,MAAOha,EAAG,CAGN,KACJ,CACJ,CAEIyY,EAAQvT,MAAM,EACduT,EAAQ9R,IAAI,CAAC8R,CAAO,CAAC,EAAE,CAAC9M,KAAK,IAEjC2M,EAAKG,OAAO,CAAGA,CACnB,CACA,OAAOH,CACX,CAWA,SAASgC,GAAa7a,CAAK,EACvB,IAAM8a,EAAe9a,EAAM8a,YAAY,CAAEjb,EAAQG,EAAMH,KAAK,CAAE0X,EAAmBuD,CAAY,CAACjb,EAAMwI,YAAY,CAAG,WAAW,EAC1HyS,EAAa9C,QAAQ,CACrBT,GAAmB,CAACuD,EAAaC,OAAO,GACxC,IAAI,CAACzD,WAAW,CAACzX,EAAMmb,gBAAgB,GAAG,IAAI,GAAKnb,EAAMwG,OAAO,CAAEkR,GAC9D1X,EAAMob,aAAa,EACnB,CAAC1D,EAAgBC,OAAO,EAExB3X,CAAAA,EAAMob,aAAa,CAAIpb,EAAMob,aAAa,CAACjW,OAAO,EAAE,EAGhE,CA+BA,GAAM,CAAEkW,KAAAA,EAAI,CAAE,CAAIhc,IAMZ,CAAEgB,OAAQib,EAAM,CAAEC,YAAa,CAAEC,OAAQ,CAAEvc,UAAWwc,EAAW,CAAE,CAAElB,KAAM,CAAEtb,UAAWyc,EAAS,CAAE,CAAE,CAAE,CAAIhX,IAE3G,CAAEiX,eAAgBC,EAAiC,CAAEC,oBAAqBC,EAAsC,CAAE,CApQ3F,CACzBH,eArBJ,WACI,IAAqBI,EAAY1b,AAAlB,IAAI,CAAqBE,OAAO,CAACoL,UAAU,CAC1D,GAAI,CAACtL,AADU,IAAI,CACP2b,eAAe,CAAE,CACzB,IAAMA,EAAkB,IAAI,CAACC,mBAAmB,GAWhD,MARI,CAAC5b,AALM,IAAI,CAKHJ,KAAK,CAACiJ,UAAU,EAAI6S,GAAWG,OACvCF,EAAgBtS,GAAG,CAACqS,EAAUG,KAAK,EAGvCF,EAAgBpZ,IAAI,CAAC,CAAE8H,QAAS,CAAE,GAC9BrK,AAVO,IAAI,CAUJ8b,OAAO,EACdH,EAAgBI,IAAI,GAEjBJ,CACX,CAGA,OADA3b,AAhBe,IAAI,CAgBZ2b,eAAe,CAACpZ,IAAI,CAACoU,EAAgC,CAAEtM,QAAS,CAAE,EAAG,IAAI,CAAC2R,UAAU,CAAC,iBACrFhc,AAjBQ,IAAI,CAiBL2b,eAAe,AACjC,EAGIH,oBA5CJ,WACI,IAAME,EAAY,IAAI,CAACxb,OAAO,CAACoL,UAAU,AAKrC,CAACoQ,GAAWO,OACX,IAAI,CAAC/b,OAAO,CAACgc,eAAe,EAAEha,iBAI/B0U,EAAY,KACR,IAAI,CAACuF,eAAe,CAAG,CAAA,CAC3B,EAAGT,EAAY7E,EAAW6E,EAAUrV,SAAS,EAAE4V,KAAK,CAAG,GALvD,IAAI,CAACE,eAAe,CAAG,CAAA,CAO/B,CA8BA,EAmQM,CAAE9c,SAAU+c,EAA2B,CAAE3X,QAAS4X,EAA0B,CAAE3X,OAAQ4X,EAAyB,CAAE1X,MAAO2X,EAAwB,CAAE1X,KAAM2X,EAAuB,CAAE,CAAIxd,IAE3Lyd,AAlCiB,CAAA,CACb/b,QATJ,SAA0Bgc,CAAe,EACrC3F,GAAkB2F,EAAiB,eAAgBhE,IACnD3B,GAAkB2F,EAAiB,wBAAyB/B,IAC5D,IAAMgC,EAAkBD,EAAgB9d,SAAS,AAC5C+d,CAAAA,EAAgBvF,WAAW,EAC5BuF,CAAAA,EAAgBvF,WAAW,CAAGA,EAAU,CAEhD,CAGA,CAAA,EAgCoB1W,OAAO,CAAEvB,IAa7B,OAAMyd,WAA2B3B,GAC7BpQ,aAAc,CAMV,KAAK,IAAIzF,WACT,IAAI,CAAC+W,eAAe,CAAG,CAAA,CAC3B,CAMA,OAAOzb,QAAQC,CAAU,CAAE,CACvBF,EAA4BC,OAAO,CAACC,GACpCkc,AAnWqE7L,EAmW9BtQ,OAAO,CAACC,EACnD,CAiBAmc,aAAc,CACV,IAAMC,EAAgB,IAAI,CAAC7c,OAAO,CAACgc,eAAe,CAAEc,EAAe,IAAI,CAACpd,KAAK,CAACM,OAAO,CAACN,KAAK,CACvFoC,EAAQib,EAAsB,IAAI,CAACrd,KAAK,CAACqd,mBAAmB,CAAExb,EAAqB,IAAI,CAAC7B,KAAK,CAAC6B,kBAAkB,AAC/G,CAAA,IAAI,CAACqa,OAAO,GAGZmB,IACD,IAAI,CAACrd,KAAK,CAACqd,mBAAmB,CAAGA,EAAsB,CAAC,EACxD,IAAI,CAACrd,KAAK,CAAC6B,kBAAkB,CAAGA,EAAqB,EAAE,EAE3DO,CAAAA,EAASib,CAAmB,CAACF,EAAcG,IAAI,CAAC,AAAD,IAE3CH,EAAc7a,gBAAgB,CAC1B,AAACma,GAA2BW,EAAaG,SAAS,EAE9C,CAACH,EAAaG,SAAS,CADvBJ,EAAc7a,gBAAgB,CAEtC+a,CAAmB,CAACF,EAAcG,IAAI,CAAC,CAAGlb,EACtC,IAAIgC,EAA8BE,OAAO,CAAC6Y,EAAcG,IAAI,CAAC,CACjElb,EAAOuP,IAAI,CAACwL,GACZtb,EAAmBqK,MAAM,CAAC9J,EAAOyE,KAAK,CAAE,EAAGzE,IAE/C,IAAI,CAACA,MAAM,CAAGA,EACdA,EAAOiR,OAAO,CAAC,EAAG,EAAG,IAAI,CAACrT,KAAK,CAACwd,SAAS,CAAE,IAAI,CAACxd,KAAK,CAACyd,UAAU,EAChErb,EAAOqR,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAErR,EAAOhC,MAAM,EACpDgC,EAAOqR,uBAAuB,CAAC,IAAI,CAACnO,KAAK,CAAElD,EAAOkD,KAAK,EACvDlD,EAAOqR,uBAAuB,CAAC,IAAI,CAACpO,MAAM,CAAEjD,EAAOmP,KAAK,EAC5D,CAIArM,SAAU,CACF,IAAI,CAAC9C,MAAM,EACX,IAAI,CAACA,MAAM,CAAC2H,2BAA2B,CAAC,IAAI,CAAE,IAAI,CAAC3H,MAAM,CAAChC,MAAM,EAEpEiJ,EAAwBnE,OAAO,CAAChG,IAAI,CAAC,IAAI,CAC7C,CAMAwe,gBAAiB,KAOTxF,EAJJ,GAAI,IAAI,CAACqE,eAAe,CACpB,OAEJ,IAAMT,EAAY,IAAI,CAACxb,OAAO,CAACoL,UAAU,CAErCoQ,GAAW5D,UACXA,CAAAA,EAAW4D,EAAU5D,QAAQ,AAAD,EAGhCmD,GAAOrc,SAAS,CAAC0e,cAAc,CAACxe,IAAI,CAAC,IAAI,CAAE,IAAI,CAACoG,KAAK,EAEjDwW,GAAW6B,cAEX7B,CAAAA,EAAU5D,QAAQ,CAAG4D,EAAU6B,YAAY,AAAD,EAE9CtC,GAAOrc,SAAS,CAAC0e,cAAc,CAACxe,IAAI,CAAC,IAAI,CAAE,IAAI,CAACiG,IAAI,EAEhD2W,GAAW5D,UACX4D,CAAAA,EAAU5D,QAAQ,CAAGA,CAAO,CAEpC,CAMAtP,gBAAiB,CACb,IAAIlD,EAAM0C,EAYV,IAXAiB,EAAwBT,cAAc,CAACrD,KAAK,CAAC,IAAI,CAAEC,WAG/C,IAAI,CAAClF,OAAO,CAACgF,KAAK,EAClB,IAAI,CAAChF,OAAO,CAACgF,KAAK,CAACnD,OAAO,CAAC,SAAUyb,CAAW,EACvC,IAAI,CAAC/U,UAAU,CAAC+U,EAAY1W,EAAE,CAAC,EAChC,CAAA,IAAI,CAAC2B,UAAU,CAAC+U,EAAY1W,EAAE,CAAC,CAC3B,IAAI,CAACK,UAAU,CAACqW,EAAY1W,EAAE,CAAA,CAE1C,EAAG,IAAI,EAENkB,EAAI,IAAI,CAAC9C,KAAK,CAACK,MAAM,CAAG,EAAGyC,GAAK,EAAGA,IAEpC1C,AADAA,CAAAA,EAAO,IAAI,CAACJ,KAAK,CAAC8C,EAAE,AAAD,EACdkF,MAAM,CAAG5H,EAAKsE,SAAS,GAC5BtE,EAAKiD,MAAM,CAAGiU,GAAwBlX,EAAKgD,MAAM,EAAIhD,EAAKgD,MAAM,CAACC,MAAM,CAAE,IAAI,CAACrI,OAAO,CAACoI,MAAM,EAAI,IAAI,CAACpI,OAAO,CAACoI,MAAM,CAACC,MAAM,CAAE,GAC5HjD,EAAKlH,GAAG,CAAGkH,EAAK8C,IAAI,CAGf,IAAI,CAACK,UAAU,CAACnD,EAAKwB,EAAE,CAAC,EACzBxB,EAAKoG,MAAM,GAGnB,IAAI,CAAC3G,IAAI,CAAChD,OAAO,CAAC,SAAU4F,CAAI,EAC5BA,EAAKQ,YAAY,CAAG,MACxB,GACA,IAAI,CAACsV,aAAa,EACtB,CAMAC,qBAAsB,CAClB,OAAO,IAAI,CAACxY,KAAK,EAAI,EAAE,AAC3B,CAOAuY,eAAgB,CACZ,IAAI,CAACvY,KAAK,CAACnD,OAAO,CAAC,SAAUuD,CAAI,CAAEmB,CAAK,EACpCnB,EAAKmB,KAAK,CAAGA,CACjB,EACJ,CAMA8K,KAAK3R,CAAK,CAAEM,CAAO,CAAE,CAsBjB,OArBA,KAAK,CAACqR,KAAK3R,EAAOM,GAClBub,GAAuC3c,IAAI,CAAC,IAAI,EAChDsd,GAA4B,IAAI,CAAE,cAAe,KACzC,IAAI,CAACpa,MAAM,EACX,IAAI,CAACA,MAAM,CAACoB,IAAI,EAExB,GACAgZ,GAA4B,IAAI,CAAE,cAAe,KAC7C,IAAI,CAAClX,KAAK,CAACnD,OAAO,CAAC,AAACuD,IACZA,GAAQA,EAAKtF,MAAM,EACnBsF,EAAKqY,YAAY,EAEzB,EACJ,GAIAvB,GAA4B,IAAI,CAAE,kBAAmB,WACjD,IAAI,CAACD,eAAe,CAAG,CAAA,EACvB,IAAI,CAACmB,cAAc,EACvB,GACO,IAAI,AACf,CAOAM,cAAcje,CAAK,CAAE8F,CAAK,CAAE,CACxB,IAAMyF,EAAU+P,GAAOrc,SAAS,CAACgf,aAAa,CAAC9e,IAAI,CAAC,IAAI,CAAEa,EAAO8F,GAMjE,OAJK4W,GAA2B1c,EAAMwB,KAAK,GACvC+J,CAAAA,EAAQ3D,CAAC,CAAG,CAAA,EAEhB2D,EAAQJ,CAAC,CAAG,AAACnL,CAAAA,EAAMuB,KAAK,EAAI,CAAA,EAAK,AAACgK,CAAAA,EAAQjB,KAAK,EAAI,CAAA,EAAK,EACjDiB,CACX,CAKAG,aAAa1L,CAAK,CAAE8F,CAAK,CAAE,CAEvB,IAAMoY,EAAapY,GAAS9F,GAASA,EAAM8F,KAAK,EAAI,SAAUqY,EAAe,IAAI,CAAC5d,OAAO,CAACuC,MAAM,CAACob,EAAW,CACxG3S,EAAU+P,GAAOrc,SAAS,CAACyM,YAAY,CAACvM,IAAI,CAAC,IAAI,CAAEa,EAAOke,GAe9D,OAdIle,GAAS,CAACA,EAAMiG,MAAM,GACtBsF,EAAUvL,EAAMmK,iBAAiB,GAE7BgU,GACA5S,CAAAA,EAAU,CAENhB,OAAQ4T,EAAaC,SAAS,EAAI7S,EAAQhB,MAAM,CAChDC,UAAY2T,EAAaE,aAAa,EAAI9S,EAAQf,SAAS,CAC3DE,QAASmS,GAAwBsB,EAAaG,WAAW,CAAE/S,EAAQb,OAAO,EAC1E,eAAgByT,EAAaC,SAAS,EAClC7S,CAAO,CAAC,eAAe,AAC/B,CAAA,GAGDA,CACX,CAMAnH,QAAS,CACL,IAAqBkB,EAASjF,AAAf,IAAI,CAAkBiF,MAAM,CAAElF,EAAaC,AAA3C,IAAI,CAA8CJ,KAAK,CAACG,UAAU,CAAEuL,EAAa,EAAE,AAElGtL,CAFe,IAAI,CAEZiF,MAAM,CAAGjF,AAFD,IAAI,CAEIkF,KAAK,CAC5BmW,GAAUtX,MAAM,CAACjF,IAAI,CAAC,IAAI,EAC1BkB,AAJe,IAAI,CAIZiF,MAAM,CAAGA,EAChBA,EAAOlD,OAAO,CAAC,SAAUpC,CAAK,EACtBA,EAAMoG,QAAQ,EAAIpG,EAAMqG,MAAM,GAC9BrG,EAAMwM,UAAU,GAChBxM,EAAMsL,UAAU,GAExB,GACIlL,GAAcA,EAAWC,MAAM,GAXpB,IAAI,EAYfA,AAZW,IAAI,CAYR8B,UAAU,CAAC/B,GAElBC,AAdW,IAAI,CAcRJ,KAAK,CAACse,WAAW,EACxB,CAACle,AAfU,IAAI,CAePE,OAAO,CAACoL,UAAU,CAAC6S,YAAY,GACvCne,AAhBW,IAAI,CAgBRkF,KAAK,CAACF,MAAM,CAAChF,AAhBT,IAAI,CAgBYiF,MAAM,EAAElD,OAAO,CAAC,SAAUuD,CAAI,EACjDA,EAAK8Y,SAAS,EACd9S,EAAWtE,IAAI,CAAC1B,EAAK8Y,SAAS,CAEtC,GACApe,AArBW,IAAI,CAqBRJ,KAAK,CAACye,qBAAqB,CAAC/S,GAE3C,CAMApF,SAAST,CAAK,CAAE6Y,CAAO,CAAE,CACjBA,GACA,IAAI,CAACrZ,MAAM,CAAG,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,EACzCkW,GAAOrc,SAAS,CAACsH,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEC,WACtC,IAAI,CAACH,MAAM,CAAG,IAAI,CAACF,IAAI,EAGvBkW,GAAOrc,SAAS,CAACsH,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEC,WAGrC,IAAI,CAACpD,MAAM,CAACoP,UAAU,EAAK3L,GAC5B,IAAI,CAAC1B,MAAM,EAEnB,CAKAwa,WAAY,CACR,IAAI,CAAC/V,cAAc,GACnB,IAAI,CAACsU,WAAW,GAChB,IAAI,CAAC5X,KAAK,CAACnD,OAAO,CAAC,SAAUuD,CAAI,EAE7BA,EAAKkZ,QAAQ,CAAG,CAAA,EAChBlZ,EAAKQ,SAAS,CAAC/D,OAAO,CAAC,SAAUpC,CAAK,EAClCA,EAAM8e,SAAS,CAAG,OAElB9e,EAAM4H,CAAC,CAAG,CACd,EACJ,EACJ,CACJ,CACAqV,GAAmB8B,cAAc,CAAGnC,GAAyBtB,GAAOyD,cAAc,CAp6E/C,CAC/BC,eAAgB,CAAA,EAMhBC,oBAAqB,CAAA,EACrBtW,OAAQ,CACJgP,QAAS,CAAA,EACT7U,OAAQ,CAOJoc,SAAU,CAINxU,QAAS,GAMThE,UAAW,CAEPyY,SAAU,EACd,CACJ,CACJ,CACJ,EACArc,OAAQ,CAOJoc,SAAU,CAINZ,YAAa,GAMb5X,UAAW,CAEPyY,SAAU,EACd,CACJ,CACJ,EAeAxT,WAAY,CAoBRyT,UAAW,WACP,OAAOC,OAAO,IAAI,CAAC5gB,GAAG,EAAI,GAC9B,EAmBA6gB,cAAe,WACX,OAAQ,IAAI,CAAClZ,QAAQ,CAACqC,IAAI,CACtB,OACA,IAAI,CAACpC,MAAM,CAACoC,IAAI,AACxB,EAYAmV,aAAc,CACVjG,QAAS,CAAA,CACb,EACAQ,SAAU,CACNR,QAAS,CAAA,CACb,EACAuE,MAAO,CACHqD,WAAY,gBAChB,EACAjD,MAAO,CAAA,EACP5V,UAAW,CACP4V,MAAO,GACX,CACJ,EAKAtU,KAAM,CAiBFoB,MAAO,2BAIPkB,MAAO,CACX,EAKA9J,UAAW,CAAA,EACX+b,gBAAiB,CAiEbnI,iBAAkB,SAUlBQ,sBAAuB,EASvBrS,iBAAkB,CAAA,EAalByT,MAAO,GAcPzF,SAAU,GAqBV0B,cAAe,OAOfsL,KAAM,uBAsBNzL,YAAa,QAObhO,cAAe,IAQf8J,sBAAuB,MAKvBS,SAAU,KACd,EACAmR,aAAc,CAAA,CAClB,GA+jEA7C,GAA0BM,GAAmBhe,SAAS,CAAE,CACpD2F,WA/8EiEkF,EAg9EjE+B,QAAS,KAAK,EACd4T,YAAa,CAAA,EACbC,UAAW,KAAK,EAChBvN,OAAQ,CAAC,aAAc,YAAa,aAAa,CACjD7R,kBAAmB,CAAA,EACnBqf,YAAa,CAAA,EACbC,gBAAiB,CAAA,EACjBC,cAAe,CAAC,OAAQ,KAAK,CAC7BC,eAAgB,CAAA,EAChBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DpE,eAAgBC,GAChBoE,YAAa3E,GACb7T,WAAY8B,EAAwB9B,UAAU,CAC9CyY,YAAaxE,GAAYwE,WAAW,CACpCxf,YAAaK,EAA4BL,WAAW,CACpDE,YAAaG,EAA4BH,WAAW,CACpDE,UAAWC,EAA4BD,SAAS,CAChDsB,WAAYrB,EAA4BqB,UAAU,AACtD,GACAuC,IAA4Dwb,kBAAkB,CAAC,eAAgBjD,IA+B/F,IAAMkD,GAAK9gB,IACX+gB,AA1BsEnD,GA0BtClc,OAAO,CAACof,GAAEE,KAAK,EAClB,IAAMC,GAAqBjhB,WAE/CihB,MAAoBC,OAAO"}