import*as t from"../highcharts.js";import"./treemap.js";var e={};e.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},e.d=(t,i)=>{for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},e.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let i=t.default;var o=e.n(i);function s(t,e){let i=[];for(let o=0;o<t.length;o++){let s=t[o][1],l=t[o][2];if("number"==typeof s&&"number"==typeof l){if(0===o)i.push(["M",s,l]);else if(o===t.length-1)i.push(["L",s,l]);else if(e){let r=t[o-1],n=t[o+1];if(r&&n){let t=r[1],o=r[2],a=n[1],h=n[2];if("number"==typeof t&&"number"==typeof a&&"number"==typeof o&&"number"==typeof h&&t!==a&&o!==h){let r=t<a?1:-1,n=o<h?1:-1;i.push(["L",s-r*Math.min(Math.abs(s-t),e),l-n*Math.min(Math.abs(l-o),e)],["C",s,l,s,l,s+r*Math.min(Math.abs(s-a),e),l+n*Math.min(Math.abs(l-h),e)])}}}else i.push(["L",s,l])}}return i}e.d({},{});let l=t.default.SeriesRegistry;var r=e.n(l);let n=t.default.SVGRenderer;var a=e.n(n);let{seriesTypes:{treemap:{prototype:{NodeClass:h}}}}=r(),d=class extends h{constructor(){super(...arguments),this.mod=0,this.shift=0,this.change=0,this.children=[],this.preX=0,this.hidden=!1,this.wasVisited=!1,this.collapsed=!1}nextLeft(){return this.getLeftMostChild()||this.thread}nextRight(){return this.getRightMostChild()||this.thread}getAncestor(t,e){return t.ancestor.children[0]===this.children[0]?t.ancestor:e}getLeftMostSibling(){let t=this.getParent();if(t){for(let e of t.children)if(e&&e.point.visible)return e}}hasChildren(){let t=this.children;for(let e=0;e<t.length;e++)if(t[e].point.visible)return!0;return!1}getLeftSibling(){let t=this.getParent();if(t){let e=t.children;for(let t=this.relativeXPosition-1;t>=0;t--)if(e[t]&&e[t].point.visible)return e[t]}}getLeftMostChild(){let t=this.children;for(let e=0;e<t.length;e++)if(t[e].point.visible)return t[e]}getRightMostChild(){let t=this.children;for(let e=t.length-1;e>=0;e--)if(t[e].point.visible)return t[e]}getParent(){return this.parentNode}getFirstChild(){let t=this.children;for(let e=0;e<t.length;e++)if(t[e].point.visible)return t[e]}},p=t.default.Point;var c=e.n(p);let{seriesTypes:{treemap:{prototype:{pointClass:f}}}}=r(),{addEvent:u,fireEvent:g,merge:v}=o();class b extends f{constructor(){super(...arguments),this.dataLabelOnHidden=!0,this.isLink=!1,this.setState=c().prototype.setState}draw(){super.draw.apply(this,arguments);let t=this.graphic;t&&t.animate({visibility:this.visible?"inherit":"hidden"}),this.renderCollapseButton()}renderCollapseButton(){let t=this.series,e=this.graphic&&this.graphic.parentGroup,i=t.mapOptionsToLevel[this.node.level||0]||{},o=v(t.options.collapseButton,i.collapseButton,this.options.collapseButton),{width:s,height:l,shape:r,style:n}=o,a=this.series.chart,h=this.visible&&(this.collapsed||!o.onlyOnHover||"hover"===this.state)?1:0;if(this.shapeArgs){if(this.collapseButtonOptions=o,this.collapseButton){if(this.node.children.length&&o.enabled){let{x:t,y:e}=this.getCollapseBtnPosition(o);this.collapseButton.attr({text:this.collapsed?"+":"-",rotation:90*!!a.inverted,rotationOriginX:s/2,rotationOriginY:l/2,visibility:this.visible?"inherit":"hidden"}).animate({x:t,y:e,opacity:h})}else this.collapseButton.destroy(),delete this.collapseButton}else{if(!this.node.children.length||!o.enabled)return;let{x:t,y:i}=this.getCollapseBtnPosition(o),d=o.fillColor||this.color||"#cccccc";this.collapseButton=a.renderer.label(this.collapsed?"+":"-",t,i,r).attr({height:l-4,width:s-4,padding:2,fill:d,rotation:90*!!a.inverted,rotationOriginX:s/2,rotationOriginY:l/2,stroke:o.lineColor||"#ffffff","stroke-width":o.lineWidth,"text-align":"center",align:"center",zIndex:1,opacity:h,visibility:this.visible?"inherit":"hidden"}).addClass("highcharts-tracker").addClass("highcharts-collapse-button").removeClass("highcharts-no-tooltip").css(v({color:"string"==typeof d?a.renderer.getContrast(d):"#333333"},n)).add(e),this.collapseButton.element.point=this}}}toggleCollapse(t){let e=this.series;this.update({collapsed:t??!this.collapsed},!1,void 0,!1),g(e,"toggleCollapse"),e.redraw()}destroy(){this.collapseButton&&(this.collapseButton.destroy(),delete this.collapseButton,this.collapseButton=void 0),this.linkToParent&&(this.linkToParent.destroy(),delete this.linkToParent),super.destroy.apply(this,arguments)}getCollapseBtnPosition(t){let e=this.series.chart.inverted,i=t.width,o=t.height,{x:s=0,y:l=0,width:r=0,height:n=0}=this.shapeArgs||{};return{x:s+t.x+(e?-(.3*o):r+-.3*i),y:l+n/2-o/2+t.y}}}u(b,"mouseOut",function(){let t=this.collapseButton,e=this.collapseButtonOptions;t&&e?.onlyOnHover&&!this.collapsed&&t.animate({opacity:0})}),u(b,"mouseOver",function(){this.collapseButton&&this.visible&&this.collapseButton.animate({opacity:1},this.series.options.states?.hover?.animation)}),u(b,"click",function(){this.toggleCollapse()});let m=t.default.Color;var y=e.n(m);let{extend:x,isArray:L,isNumber:k,isObject:P,merge:C,pick:T,relativeLength:M}=o(),X={getColor:function(t,e){let i,o,s,l,r,n,a=e.index,h=e.mapOptionsToLevel,d=e.parentColor,p=e.parentColorIndex,c=e.series,f=e.colors,u=e.siblings,g=c.points,v=c.chart.options.chart;return t&&(i=g[t.i],o=h[t.level]||{},i&&o.colorByPoint&&(l=i.index%(f?f.length:v.colorCount),s=f&&f[l]),c.chart.styledMode||(r=T(i&&i.options.color,o&&o.color,s,d&&(t=>{let e=o&&o.colorVariation;return e&&"brightness"===e.key&&a&&u?y().parse(t).brighten(e.to*(a/u)).get():t})(d),c.color)),n=T(i&&i.options.colorIndex,o&&o.colorIndex,l,p,e.colorIndex)),{color:r,colorIndex:n}},getLevelOptions:function(t){let e,i,o,s,l,r,n={};if(P(t))for(s=k(t.from)?t.from:1,r=t.levels,i={},e=P(t.defaults)?t.defaults:{},L(r)&&(i=r.reduce((t,i)=>{let o,l,r;return P(i)&&k(i.level)&&(l=T((r=C({},i)).levelIsConstant,e.levelIsConstant),delete r.levelIsConstant,delete r.level,P(t[o=i.level+(l?0:s-1)])?C(!0,t[o],r):t[o]=r),t},{})),l=k(t.to)?t.to:1,o=0;o<=l;o++)n[o]=C({},e,P(i[o])?i[o]:{});return n},getNodeWidth:function(t,e){let{chart:i,options:o}=t,{nodeDistance:s=0,nodeWidth:l=0}=o,{plotSizeX:r=1}=i;if("auto"===l){if("string"==typeof s&&/%$/.test(s))return r/(e+parseFloat(s)/100*(e-1));let t=Number(s);return(r+t)/(e||1)-t}return M(l,r)},setTreeValues:function t(e,i){let o=i.before,s=i.idRoot,l=i.mapIdToNode[s],r=!1!==i.levelIsConstant,n=i.points[e.i],a=n&&n.options||{},h=[],d=0;e.levelDynamic=e.level-(r?0:l.level),e.name=T(n&&n.name,""),e.visible=s===e.id||!0===i.visible,"function"==typeof o&&(e=o(e,i)),e.children.forEach((o,s)=>{let l=x({},i);x(l,{index:s,siblings:e.children.length,visible:e.visible}),o=t(o,l),h.push(o),o.visible&&(d+=o.val)});let p=T(a.value,d);return e.visible=p>=0&&(d>0||e.visible),e.children=h,e.childrenTotal=d,e.isLeaf=e.visible&&!d,e.val=p,e},updateRootId:function(t){let e,i;return P(t)&&(i=P(t.options)?t.options:{},e=T(t.rootNode,i.rootId,""),P(t.userOptions)&&(t.userOptions.rootId=e),t.rootNode=e),e}},{pick:N,extend:O}=o(),{seriesTypes:{column:{prototype:{pointClass:B}}}}=r(),S=class extends B{constructor(t,e,i,o){super(t,e,i),this.dataLabelOnNull=!0,this.formatPrefix="link",this.isLink=!0,this.node={},this.formatPrefix="link",this.dataLabelOnNull=!0,o&&(this.fromNode=o.node.parentNode.point,this.visible=o.visible,this.toNode=o,this.id=this.toNode.id+"-"+this.fromNode.id)}update(t,e,i,o){let s={id:this.id,formatPrefix:this.formatPrefix};c().prototype.update.call(this,t,!this.isLink&&e,i,o),this.visible=this.toNode.visible,O(this,s),N(e,!0)&&this.series.chart.redraw(i)}};class w{static createDummyNode(t,e,i){let o=new d;return o.id=t.id+"-"+i,o.ancestor=t,o.children.push(e),o.parent=t.id,o.parentNode=t,o.point=e.point,o.level=e.level-i,o.relativeXPosition=e.relativeXPosition,o.visible=e.visible,t.children[e.relativeXPosition]=o,e.oldParentNode=t,e.relativeXPosition=0,e.parentNode=o,e.parent=o.id,o}calculatePositions(t){let e=t.nodeList;this.resetValues(e);let i=t.tree;i&&(this.calculateRelativeX(i,0),this.beforeLayout(e),this.firstWalk(i),this.secondWalk(i,-i.preX),this.afterLayout(e))}beforeLayout(t){for(let e of t)for(let t of e.children)if(t&&t.level-e.level>1){let i=t.level-e.level-1;for(;i>0;)t=w.createDummyNode(e,t,i),i--}}resetValues(t){for(let e of t)e.mod=0,e.ancestor=e,e.shift=0,e.thread=void 0,e.change=0,e.preX=0}calculateRelativeX(t,e){let i=t.children;for(let t=0,e=i.length;t<e;++t)this.calculateRelativeX(i[t],t);t.relativeXPosition=e}firstWalk(t){let e;if(t.hasChildren()){let i=t.getLeftMostChild();for(let e of t.children)this.firstWalk(e),i=this.apportion(e,i);this.executeShifts(t);let o=t.getLeftMostChild(),s=t.getRightMostChild(),l=(o.preX+s.preX)/2;(e=t.getLeftSibling())?(t.preX=e.preX+1,t.mod=t.preX-l):t.preX=l}else(e=t.getLeftSibling())?(t.preX=e.preX+1,t.mod=t.preX):t.preX=0}secondWalk(t,e){for(let i of(t.yPosition=t.preX+e,t.xPosition=t.level,t.children))this.secondWalk(i,e+t.mod)}executeShifts(t){let e=0,i=0;for(let o=t.children.length-1;o>=0;o--){let s=t.children[o];s.preX+=e,s.mod+=e,i+=s.change,e+=s.shift+i}}apportion(t,e){let i=t.getLeftSibling();if(i){let o=t,s=t,l=i,r=o.getLeftMostSibling(),n=o.mod,a=s.mod,h=l.mod,d=r.mod;for(;l&&l.nextRight()&&o&&o.nextLeft();){l=l.nextRight(),r=r.nextLeft(),o=o.nextLeft(),(s=s.nextRight()).ancestor=t;let i=l.preX+h-(o.preX+n)+1;i>0&&(this.moveSubtree(t.getAncestor(l,e),t,i),n+=i,a+=i),h+=l.mod,n+=o.mod,d+=r.mod,a+=s.mod}l&&l.nextRight()&&!s.nextRight()&&(s.thread=l.nextRight(),s.mod+=h-a),o&&o.nextLeft()&&!r.nextLeft()&&(r.thread=o.nextLeft(),r.mod+=n-d),e=t}return e}moveSubtree(t,e,i){let o=e.relativeXPosition-t.relativeXPosition;e.change-=i/o,e.shift+=i,e.preX+=i,e.mod+=i,t.change+=i/o}afterLayout(t){for(let e of t)e.oldParentNode&&(e.relativeXPosition=e.parentNode.relativeXPosition,e.parent=e.oldParentNode.parent,e.parentNode=e.oldParentNode,delete e.oldParentNode.children[e.relativeXPosition],e.oldParentNode.children[e.relativeXPosition]=e,e.oldParentNode=void 0)}}let A=t.default.SVGElement;var R=e.n(A);let{deg2rad:W}=o(),{addEvent:I,merge:z,uniqueKey:D,defined:Y,extend:E}=o();function V(t,e){e=z(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let i=this.renderer.url,o=this.text||this,s=o.textPath,{attributes:l,enabled:r}=e;if(t=t||s&&s.path,s&&s.undo(),t&&r){let e=I(o,"afterModifyTree",e=>{if(t&&r){let s=t.attr("id");s||t.attr("id",s=D());let r={x:0,y:0};Y(l.dx)&&(r.dx=l.dx,delete l.dx),Y(l.dy)&&(r.dy=l.dy,delete l.dy),o.attr(r),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let n=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:E(l,{"text-anchor":l.textAnchor,href:`${i}#${s}`}),children:n}}});o.textPath={path:t,undo:e}}else o.attr({dx:0,dy:0}),delete o.textPath;return this.added&&(o.textCache="",this.renderer.buildText(o)),this}function F(t){let e=t.bBox,i=this.element?.querySelector("textPath");if(i){let t=[],{b:o,h:s}=this.renderer.fontMetrics(this.element),l=s-o,r=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),n=i.innerHTML.replace(r,"").split(/<tspan class="highcharts-br"[^>]*>/),a=n.length,h=(t,e)=>{let{x:s,y:r}=e,n=(i.getRotationOfChar(t)-90)*W,a=Math.cos(n),h=Math.sin(n);return[[s-l*a,r-l*h],[s+o*a,r+o*h]]};for(let e=0,o=0;o<a;o++){let s=n[o].length;for(let l=0;l<s;l+=5)try{let s=e+l+o,[r,n]=h(s,i.getStartPositionOfChar(s));0===l?(t.push(n),t.push(r)):(0===o&&t.unshift(n),o===a-1&&t.push(r))}catch(t){break}e+=s-1;try{let s=e+o,l=i.getEndPositionOfChar(s),[r,n]=h(s,l);t.unshift(n),t.unshift(r)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function H(t){let e=t.labelOptions,i=t.point,o=e[i.formatPrefix+"TextPath"]||e.textPath;o&&!e.useHTML&&(this.setTextPath(i.getDataLabelPath?.(this)||i.graphic,o),i.dataLabelPath&&!o.enabled&&(i.dataLabelPath=i.dataLabelPath.destroy()))}let{getLinkPath:$}={applyRadius:s,getLinkPath:{default:function(t){let{x1:e,y1:i,x2:o,y2:l,width:r=0,inverted:n=!1,radius:a,parentVisible:h}=t,d=[["M",e,i],["L",e,i],["C",e,i,e,l,e,l],["L",e,l],["C",e,i,e,l,e,l],["L",e,l]];return h?s([["M",e,i],["L",e+r*(n?-.5:.5),i],["L",e+r*(n?-.5:.5),l],["L",o,l]],a):d},straight:function(t){let{x1:e,y1:i,x2:o,y2:s,width:l=0,inverted:r=!1,parentVisible:n}=t;return n?[["M",e,i],["L",e+l*(r?-1:1),s],["L",o,s]]:[["M",e,i],["L",e,s],["L",e,s]]},curved:function(t){let{x1:e,y1:i,x2:o,y2:s,offset:l=0,width:r=0,inverted:n=!1,parentVisible:a}=t;return a?[["M",e,i],["C",e+l,i,e-l+r*(n?-1:1),s,e+r*(n?-1:1),s],["L",o,s]]:[["M",e,i],["C",e,i,e,s,e,s],["L",o,s]]}}},{series:{prototype:j},seriesTypes:{treemap:G,column:_}}=r(),{prototype:{symbols:q}}=a(),{getLevelOptions:U,getNodeWidth:J}=X,{arrayMax:K,crisp:Q,extend:Z,merge:tt,pick:te,relativeLength:ti,splat:to}=o();({compose:function(t){I(t,"afterGetBBox",F),I(t,"beforeAddingDataLabel",H);let e=t.prototype;e.setTextPath||(e.setTextPath=V)}}).compose(R());class ts extends G{constructor(){super(...arguments),this.nodeList=[],this.links=[]}init(){super.init.apply(this,arguments),this.layoutAlgorythm=new w;let t=this,e=this.chart.labelCollectors;e.some(t=>"collectorFunc"===t.name)||e.push(function(){let e=[];if(t.options.dataLabels&&!to(t.options.dataLabels)[0].allowOverlap)for(let i of t.links||[])i.dataLabel&&e.push(i.dataLabel);return e})}getLayoutModifiers(){let t=this.chart,e=this,i=t.plotSizeX,o=t.plotSizeY,s=K(this.points.map(t=>t.node.xPosition)),l=1/0,r=-1/0,n=1/0,a=-1/0,h=0,d=0,p=0,c=0;this.points.forEach(t=>{let f;if(this.options.fillSpace&&!t.visible)return;let u=t.node,g=e.mapOptionsToLevel[t.node.level]||{},v=tt(this.options.marker,g.marker,t.options.marker),b=v.width??J(this,s),m=ti(v.radius||0,Math.min(i,o)),y=v.symbol,x="circle"!==y&&v.height?ti(v.height,o):2*m,L="circle"!==y&&b?ti(b,i):2*m;u.nodeSizeX=L,u.nodeSizeY=x,u.xPosition<=l&&(l=u.xPosition,d=Math.max(L+(v.lineWidth||0),d)),u.xPosition>=r&&(r=u.xPosition,h=Math.max(L+(v.lineWidth||0),h)),u.yPosition<=n&&(n=u.yPosition,c=Math.max(x+(v.lineWidth||0),c)),u.yPosition>=a&&(a=u.yPosition,p=Math.max(x+(v.lineWidth||0),p))});let f=a===n?1:(o-(c+p)/2)/(a-n),u=a===n?o/2:-f*n+c/2,g=r===l?1:(i-(h+h)/2)/(r-l),v=r===l?i/2:-g*l+d/2;return{ax:g,bx:v,ay:f,by:u}}getLinks(){let t=this,e=[];return this.data.forEach(i=>{let o=t.mapOptionsToLevel[i.node.level||0]||{};if(i.node.parent){let s=tt(o,i.options);if(!i.linkToParent||i.linkToParent.destroyed){let e=new t.LinkClass(t,s,void 0,i);i.linkToParent=e}else i.collapsed=te(i.collapsed,(this.mapOptionsToLevel[i.node.level]||{}).collapsed),i.linkToParent.visible=i.linkToParent.toNode.visible;i.linkToParent.index=e.push(i.linkToParent)-1}else i.linkToParent&&(t.links.splice(i.linkToParent.index),i.linkToParent.destroy(),delete i.linkToParent)}),e}buildTree(t,e,i,o,s){let l=this.points[e];return i=l&&l.level||i,super.buildTree.call(this,t,e,i,o,s)}markerAttribs(){return{}}setCollapsedStatus(t,e){let i=t.point;i&&(i.collapsed=te(i.collapsed,(this.mapOptionsToLevel[t.level]||{}).collapsed),i.visible=e,e=!1!==e&&!i.collapsed),t.children.forEach(t=>{this.setCollapsedStatus(t,e)})}drawTracker(){_.prototype.drawTracker.apply(this,arguments),_.prototype.drawTracker.call(this,this.links)}translate(){let t=this.options,e=X.updateRootId(this),i;j.translate.call(this);let o=this.tree=this.getTree();i=this.nodeMap[e],""===e||i&&i.children.length||(this.setRootNode("",!1),e=this.rootNode,i=this.nodeMap[e]),this.mapOptionsToLevel=U({from:i.level+1,levels:t.levels,to:o.height,defaults:{levelIsConstant:this.options.levelIsConstant,colorByPoint:t.colorByPoint}}),this.setCollapsedStatus(o,!0),this.links=this.getLinks(),this.setTreeValues(o),this.layoutAlgorythm.calculatePositions(this),this.layoutModifier=this.getLayoutModifiers(),this.points.forEach(t=>{this.translateNode(t)}),this.points.forEach(t=>{t.linkToParent&&this.translateLink(t.linkToParent)}),t.colorByPoint||this.setColorRecursive(this.tree)}translateLink(t){let e=t.fromNode,i=t.toNode,o=this.options.link?.lineWidth||0,s=te(this.options.link?.curveFactor,.5),l=te(t.options.link?.type,this.options.link?.type,"default");if(e.shapeArgs&&i.shapeArgs){let r=e.shapeArgs.width||0,n=this.chart.inverted,a=Q((e.shapeArgs.y||0)+(e.shapeArgs.height||0)/2,o),h=Q((i.shapeArgs.y||0)+(i.shapeArgs.height||0)/2,o),d=Q((e.shapeArgs.x||0)+r,o),p=Q(i.shapeArgs.x||0,o);n&&(d-=r,p+=i.shapeArgs.width||0);let c=i.node.xPosition-e.node.xPosition;t.shapeType="path";let f=(Math.abs(p-d)+r)/c-r;t.plotX=Q((p+d)/2,o),t.plotY=h,t.shapeArgs={d:$[l]({x1:d,y1:a,x2:p,y2:h,width:f,offset:f*s*(n?-1:1),inverted:n,parentVisible:i.visible,radius:this.options.link?.radius})},t.dlBox={x:(d+p)/2,y:(a+h)/2,height:o,width:0},t.tooltipPos=n?[(this.chart.plotSizeY||0)-t.dlBox.y,(this.chart.plotSizeX||0)-t.dlBox.x]:[t.dlBox.x,t.dlBox.y]}}drawNodeLabels(t){let e,i,o=this.mapOptionsToLevel;for(let s of t){if(i=o[s.node.level],e={style:{}},i&&i.dataLabels&&(e=tt(e,i.dataLabels),this.hasDataLabels=()=>!0),s.shapeArgs&&this.options.dataLabels){let t={},{width:i=0,height:o=0}=s.shapeArgs;this.chart.inverted&&([i,o]=[o,i]),to(this.options.dataLabels)[0].style?.width||(t.width=`${i}px`),to(this.options.dataLabels)[0].style?.lineClamp||(t.lineClamp=Math.floor(o/16)),Z(e.style,t),s.dataLabel?.css(t)}s.dlOptions=tt(e,s.options.dataLabels)}j.drawDataLabels.call(this,t)}alignDataLabel(t,e){let i=t.visible;t.visible=!0,super.alignDataLabel.apply(this,arguments),e.animate({opacity:+(!1!==i)},void 0,function(){i||e.hide()}),t.visible=i}drawDataLabels(){this.options.dataLabels&&(this.options.dataLabels=to(this.options.dataLabels),this.drawNodeLabels(this.points),j.drawDataLabels.call(this,this.links))}destroy(){if(this.links){for(let t of this.links)t.destroy();this.links.length=0}return j.destroy.apply(this,arguments)}pointAttribs(t,e){let i=t&&this.mapOptionsToLevel[t.node.level||0]||{},o=t&&t.options,s=i.states&&i.states[e]||{};t&&(t.options.marker=tt(this.options.marker,i.marker,t.options.marker));let l=te(s&&s.link&&s.link.color,o&&o.link&&o.link.color,i&&i.link&&i.link.color,this.options.link&&this.options.link.color),r=te(s&&s.link&&s.link.lineWidth,o&&o.link&&o.link.lineWidth,i&&i.link&&i.link.lineWidth,this.options.link&&this.options.link.lineWidth),n=j.pointAttribs.call(this,t,e);return t&&(t.isLink&&(n.stroke=l,n["stroke-width"]=r,delete n.fill),t.visible||(n.opacity=0)),n}drawPoints(){G.prototype.drawPoints.apply(this,arguments),_.prototype.drawPoints.call(this,this.links)}translateNode(t){let e=this.chart,i=t.node,o=e.plotSizeY,s=e.plotSizeX,{ax:l,bx:r,ay:n,by:a}=this.layoutModifier,h=l*i.xPosition+r,d=n*i.yPosition+a,p=this.mapOptionsToLevel[i.level]||{},c=tt(this.options.marker,p.marker,t.options.marker).symbol,f=i.nodeSizeY,u=i.nodeSizeX,g=this.options.reversed,v=i.x=e.inverted?s-u/2-h:h-u/2,b=i.y=g?d-f/2:o-d-f/2,m=te(t.options.borderRadius,p.borderRadius,this.options.borderRadius),y=q[c||"circle"];if(void 0===y?(t.hasImage=!0,t.shapeType="image",t.imageUrl=c.match(/^url\((.*?)\)$/)[1]):t.shapeType="path",!t.visible&&t.linkToParent){let e=t.linkToParent.fromNode;if(e){let{x:i=0,y:o=0,width:s=0,height:l=0}=e.shapeArgs||{};t.shapeArgs||(t.shapeArgs={}),t.hasImage||Z(t.shapeArgs,{d:y(i,o,s,l,m?{r:m}:void 0)}),Z(t.shapeArgs,{x:i,y:o}),t.plotX=e.plotX,t.plotY=e.plotY}}else t.plotX=v,t.plotY=b,t.shapeArgs={x:v,y:b,width:u,height:f,cursor:t.node.isLeaf?"default":"pointer"},t.hasImage||(t.shapeArgs.d=y(v,b,u,f,m?{r:m}:void 0));t.tooltipPos=e.inverted?[o-b-f/2,s-v-u/2]:[v+u/2,b]}}ts.defaultOptions=tt(G.defaultOptions,{reversed:!1,marker:{radius:10,lineWidth:0,symbol:"circle",fillOpacity:1,states:{}},link:{color:"#666666",lineWidth:1,radius:10,cursor:"default",type:"curved"},collapseButton:{onlyOnHover:!0,enabled:!0,lineWidth:1,x:0,y:0,height:18,width:18,shape:"circle",style:{cursor:"pointer",fontWeight:"bold",fontSize:"1em"}},fillSpace:!1,tooltip:{linkFormat:"{point.fromNode.id} → {point.toNode.id}",pointFormat:"{point.id}"},dataLabels:{defer:!0,linkTextPath:{attributes:{startOffset:"50%"}},enabled:!0,linkFormatter:()=>"",padding:5,style:{textOverflow:"none"}},nodeDistance:30,nodeWidth:void 0}),Z(ts.prototype,{pointClass:b,NodeClass:d,LinkClass:S}),r().registerSeriesType("treegraph",ts);let tl=o();export{tl as default};