{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * Treegraph chart series type\n * @module highcharts/modules/treegraph\n * @requires highcharts\n * @requires highcharts/modules/treemap\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__treemap_src_js_d9a26ac3__ from \"./treemap.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"./treemap.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const external_treemap_src_js_namespaceObject = x({  });\n;// ./code/es-modules/Series/PathUtilities.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst getLinkPath = {\n    'default': getDefaultPath,\n    straight: getStraightPath,\n    curved: getCurvedPath\n};\n/**\n *\n */\nfunction getDefaultPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, radius, parentVisible } = pathParams;\n    const path = [\n        ['M', x1, y1],\n        ['L', x1, y1],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2],\n        ['C', x1, y1, x1, y2, x1, y2],\n        ['L', x1, y2]\n    ];\n    return parentVisible ?\n        applyRadius([\n            ['M', x1, y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y2],\n            ['L', x2, y2]\n        ], radius) :\n        path;\n}\n/**\n *\n */\nfunction getStraightPath(pathParams) {\n    const { x1, y1, x2, y2, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ? [\n        ['M', x1, y1],\n        ['L', x1 + width * (inverted ? -1 : 1), y2],\n        ['L', x2, y2]\n    ] : [\n        ['M', x1, y1],\n        ['L', x1, y2],\n        ['L', x1, y2]\n    ];\n}\n/**\n *\n */\nfunction getCurvedPath(pathParams) {\n    const { x1, y1, x2, y2, offset = 0, width = 0, inverted = false, parentVisible } = pathParams;\n    return parentVisible ?\n        [\n            ['M', x1, y1],\n            [\n                'C',\n                x1 + offset,\n                y1,\n                x1 - offset + width * (inverted ? -1 : 1),\n                y2,\n                x1 + width * (inverted ? -1 : 1),\n                y2\n            ],\n            ['L', x2, y2]\n        ] :\n        [\n            ['M', x1, y1],\n            ['C', x1, y1, x1, y2, x1, y2],\n            ['L', x2, y2]\n        ];\n}\n/**\n * General function to apply corner radius to a path\n * @private\n */\nfunction applyRadius(path, r) {\n    const d = [];\n    for (let i = 0; i < path.length; i++) {\n        const x = path[i][1];\n        const y = path[i][2];\n        if (typeof x === 'number' && typeof y === 'number') {\n            // MoveTo\n            if (i === 0) {\n                d.push(['M', x, y]);\n            }\n            else if (i === path.length - 1) {\n                d.push(['L', x, y]);\n                // CurveTo\n            }\n            else if (r) {\n                const prevSeg = path[i - 1];\n                const nextSeg = path[i + 1];\n                if (prevSeg && nextSeg) {\n                    const x1 = prevSeg[1], y1 = prevSeg[2], x2 = nextSeg[1], y2 = nextSeg[2];\n                    // Only apply to breaks\n                    if (typeof x1 === 'number' &&\n                        typeof x2 === 'number' &&\n                        typeof y1 === 'number' &&\n                        typeof y2 === 'number' &&\n                        x1 !== x2 &&\n                        y1 !== y2) {\n                        const directionX = x1 < x2 ? 1 : -1, directionY = y1 < y2 ? 1 : -1;\n                        d.push([\n                            'L',\n                            x - directionX * Math.min(Math.abs(x - x1), r),\n                            y - directionY * Math.min(Math.abs(y - y1), r)\n                        ], [\n                            'C',\n                            x,\n                            y,\n                            x,\n                            y,\n                            x + directionX * Math.min(Math.abs(x - x2), r),\n                            y + directionY * Math.min(Math.abs(y - y2), r)\n                        ]);\n                    }\n                }\n                // LineTo\n            }\n            else {\n                d.push(['L', x, y]);\n            }\n        }\n    }\n    return d;\n}\nconst PathUtilities = {\n    applyRadius,\n    getLinkPath\n};\n/* harmony default export */ const Series_PathUtilities = (PathUtilities);\n\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SVGRenderer\"]\nconst external_highcharts_src_js_default_SVGRenderer_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGRenderer;\nvar external_highcharts_src_js_default_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGRenderer_namespaceObject);\n;// ./code/es-modules/Series/Treegraph/TreegraphNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { seriesTypes: { treemap: { prototype: { NodeClass: TreemapNode } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass TreegraphNode extends TreemapNode {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.mod = 0;\n        this.shift = 0;\n        this.change = 0;\n        this.children = [];\n        this.preX = 0;\n        this.hidden = false;\n        this.wasVisited = false;\n        this.collapsed = false;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get the next left node which is either first child or thread.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Next left node child or thread.\n     */\n    nextLeft() {\n        return this.getLeftMostChild() || this.thread;\n    }\n    /**\n     * Get the next right node which is either last child or thread.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Next right node child or thread.\n     */\n    nextRight() {\n        return this.getRightMostChild() || this.thread;\n    }\n    /**\n     * Return the left one of the greatest uncommon ancestors of a\n     * leftInternal node and it's right neighbor.\n     *\n     * @param {TreegraphNode} leftIntNode\n     * @param {TreegraphNode} defaultAncestor\n     * @return {TreegraphNode}\n     *         Left one of the greatest uncommon ancestors of a leftInternal\n     *         node and it's right neighbor.\n     *\n     */\n    getAncestor(leftIntNode, defaultAncestor) {\n        const leftAnc = leftIntNode.ancestor;\n        if (leftAnc.children[0] === this.children[0]) {\n            return leftIntNode.ancestor;\n        }\n        return defaultAncestor;\n    }\n    /**\n     * Get node's first sibling, which is not hidden.\n     *\n     * @return {TreegraphNode|undefined}\n     *         First sibling of the node which is not hidden or undefined, if it\n     *         does not exists.\n     */\n    getLeftMostSibling() {\n        const parent = this.getParent();\n        if (parent) {\n            for (const child of parent.children) {\n                if (child && child.point.visible) {\n                    return child;\n                }\n            }\n        }\n    }\n    /**\n     * Check if the node is a leaf (if it has any children).\n     *\n     * @return {boolean}\n     *         If the node has no visible children return true.\n     */\n    hasChildren() {\n        const children = this.children;\n        for (let i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Get node's left sibling (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Left sibling of the node\n     */\n    getLeftSibling() {\n        const parent = this.getParent();\n        if (parent) {\n            const children = parent.children;\n            for (let i = this.relativeXPosition - 1; i >= 0; i--) {\n                if (children[i] && children[i].point.visible) {\n                    return children[i];\n                }\n            }\n        }\n    }\n    /**\n     * Get the node's first child (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's first child which isn't hidden.\n     */\n    getLeftMostChild() {\n        const children = this.children;\n        for (let i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    }\n    /**\n     * Get the node's last child (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's last child which isn't hidden.\n     */\n    getRightMostChild() {\n        const children = this.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    }\n    /**\n     * Get the parent of current node or return undefined for root of the\n     * tree.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's parent or undefined for root.\n     */\n    getParent() {\n        return this.parentNode;\n    }\n    /**\n     * Get node's first child which is not hidden.\n     *\n     * @return {TreegraphNode|undefined}\n     *         First child.\n     */\n    getFirstChild() {\n        const children = this.children;\n        for (let i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphNode = (TreegraphNode);\n\n;// external [\"../highcharts.js\",\"default\",\"Point\"]\nconst external_highcharts_src_js_default_Point_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Point;\nvar external_highcharts_src_js_default_Point_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Point_namespaceObject);\n;// ./code/es-modules/Series/Treegraph/TreegraphPoint.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { seriesTypes: { treemap: { prototype: { pointClass: TreemapPoint } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { addEvent, fireEvent, merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass TreegraphPoint extends TreemapPoint {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.dataLabelOnHidden = true;\n        this.isLink = false;\n        this.setState = (external_highcharts_src_js_default_Point_default()).prototype.setState;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    draw() {\n        super.draw.apply(this, arguments);\n        // Run animation of hiding/showing of the point.\n        const graphic = this.graphic;\n        if (graphic) {\n            graphic.animate({\n                visibility: this.visible ? 'inherit' : 'hidden'\n            });\n        }\n        this.renderCollapseButton();\n    }\n    renderCollapseButton() {\n        const point = this, series = point.series, parentGroup = point.graphic && point.graphic.parentGroup, levelOptions = series.mapOptionsToLevel[point.node.level || 0] || {}, btnOptions = merge(series.options.collapseButton, levelOptions.collapseButton, point.options.collapseButton), { width, height, shape, style } = btnOptions, padding = 2, chart = this.series.chart, calculatedOpacity = (point.visible &&\n            (point.collapsed ||\n                !btnOptions.onlyOnHover ||\n                point.state === 'hover')) ? 1 : 0;\n        if (!point.shapeArgs) {\n            return;\n        }\n        this.collapseButtonOptions = btnOptions;\n        if (!point.collapseButton) {\n            if (!point.node.children.length || !btnOptions.enabled) {\n                return;\n            }\n            const { x, y } = this.getCollapseBtnPosition(btnOptions), fill = (btnOptions.fillColor ||\n                point.color ||\n                \"#cccccc\" /* Palette.neutralColor20 */);\n            point.collapseButton = chart.renderer\n                .label(point.collapsed ? '+' : '-', x, y, shape)\n                .attr({\n                height: height - 2 * padding,\n                width: width - 2 * padding,\n                padding: padding,\n                fill,\n                rotation: chart.inverted ? 90 : 0,\n                rotationOriginX: width / 2,\n                rotationOriginY: height / 2,\n                stroke: btnOptions.lineColor || \"#ffffff\" /* Palette.backgroundColor */,\n                'stroke-width': btnOptions.lineWidth,\n                'text-align': 'center',\n                align: 'center',\n                zIndex: 1,\n                opacity: calculatedOpacity,\n                visibility: point.visible ? 'inherit' : 'hidden'\n            })\n                .addClass('highcharts-tracker')\n                .addClass('highcharts-collapse-button')\n                .removeClass('highcharts-no-tooltip')\n                .css(merge({\n                color: typeof fill === 'string' ?\n                    chart.renderer.getContrast(fill) :\n                    \"#333333\" /* Palette.neutralColor80 */\n            }, style))\n                .add(parentGroup);\n            point.collapseButton.element.point = point;\n        }\n        else {\n            if (!point.node.children.length || !btnOptions.enabled) {\n                point.collapseButton.destroy();\n                delete point.collapseButton;\n            }\n            else {\n                const { x, y } = this.getCollapseBtnPosition(btnOptions);\n                point.collapseButton\n                    .attr({\n                    text: point.collapsed ? '+' : '-',\n                    rotation: chart.inverted ? 90 : 0,\n                    rotationOriginX: width / 2,\n                    rotationOriginY: height / 2,\n                    visibility: point.visible ? 'inherit' : 'hidden'\n                })\n                    .animate({\n                    x,\n                    y,\n                    opacity: calculatedOpacity\n                });\n            }\n        }\n    }\n    toggleCollapse(state) {\n        const series = this.series;\n        this.update({\n            collapsed: state ?? !this.collapsed\n        }, false, void 0, false);\n        fireEvent(series, 'toggleCollapse');\n        series.redraw();\n    }\n    destroy() {\n        if (this.collapseButton) {\n            this.collapseButton.destroy();\n            delete this.collapseButton;\n            this.collapseButton = void 0;\n        }\n        if (this.linkToParent) {\n            this.linkToParent.destroy();\n            delete this.linkToParent;\n        }\n        super.destroy.apply(this, arguments);\n    }\n    getCollapseBtnPosition(btnOptions) {\n        const point = this, chart = point.series.chart, inverted = chart.inverted, btnWidth = btnOptions.width, btnHeight = btnOptions.height, { x = 0, y = 0, width = 0, height = 0 } = point.shapeArgs || {};\n        return {\n            x: x +\n                btnOptions.x +\n                (inverted ? -btnHeight * 0.3 : width + btnWidth * -0.3),\n            y: y + height / 2 - btnHeight / 2 + btnOptions.y\n        };\n    }\n}\naddEvent(TreegraphPoint, 'mouseOut', function () {\n    const btn = this.collapseButton, btnOptions = this.collapseButtonOptions;\n    if (btn && btnOptions?.onlyOnHover && !this.collapsed) {\n        btn.animate({ opacity: 0 });\n    }\n});\naddEvent(TreegraphPoint, 'mouseOver', function () {\n    if (this.collapseButton && this.visible) {\n        this.collapseButton.animate({ opacity: 1 }, this.series.options.states?.hover?.animation);\n    }\n});\n// Handle showing and hiding of the points\naddEvent(TreegraphPoint, 'click', function () {\n    this.toggleCollapse();\n});\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphPoint = (TreegraphPoint);\n\n;// external [\"../highcharts.js\",\"default\",\"Color\"]\nconst external_highcharts_src_js_default_Color_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Color;\nvar external_highcharts_src_js_default_Color_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Color_namespaceObject);\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend, isArray, isNumber, isObject, merge: TreeUtilities_merge, pick, relativeLength } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return external_highcharts_src_js_default_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = extend({}, options);\n        extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphLink.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { pick: TreegraphLink_pick, extend: TreegraphLink_extend } = (external_highcharts_src_js_default_default());\n\nconst { seriesTypes: { column: { prototype: { pointClass: ColumnPoint } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass LinkPoint extends ColumnPoint {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(series, options, x, point) {\n        super(series, options, x);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.dataLabelOnNull = true;\n        this.formatPrefix = 'link';\n        this.isLink = true;\n        this.node = {};\n        this.formatPrefix = 'link';\n        this.dataLabelOnNull = true;\n        if (point) {\n            this.fromNode = point.node.parentNode.point;\n            this.visible = point.visible;\n            this.toNode = point;\n            this.id = this.toNode.id + '-' + this.fromNode.id;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    update(options, redraw, animation, runEvent) {\n        const oldOptions = {\n            id: this.id,\n            formatPrefix: this.formatPrefix\n        };\n        external_highcharts_src_js_default_Point_default().prototype.update.call(this, options, this.isLink ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        this.visible = this.toNode.visible;\n        TreegraphLink_extend(this, oldOptions);\n        if (TreegraphLink_pick(redraw, true)) {\n            this.series.chart.redraw(animation);\n        }\n    }\n}\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ const TreegraphLink = (LinkPoint);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphLayout.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nclass TreegraphLayout {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create dummy node, which allows to manually set the level of the node.\n     *\n     * @param {TreegraphNode} parent\n     *        Parent node, to which the dummyNode should be connected.\n     * @param {TreegraphNode} child\n     *        Child node, which should be connected to dummyNode.\n     * @param {number} gapSize\n     *        Remaining gap size.\n     *\n     * @return {TreegraphNode}\n     *         DummyNode as a parent of nodes, which column changes.\n     */\n    static createDummyNode(parent, child, gapSize) {\n        // Initialise dummy node.\n        const dummyNode = new Treegraph_TreegraphNode();\n        dummyNode.id = parent.id + '-' + gapSize;\n        dummyNode.ancestor = parent;\n        // Add connection from new node to the previous points.\n        // First connection to itself.\n        dummyNode.children.push(child);\n        dummyNode.parent = parent.id;\n        dummyNode.parentNode = parent;\n        dummyNode.point = child.point;\n        dummyNode.level = child.level - gapSize;\n        dummyNode.relativeXPosition = child.relativeXPosition;\n        dummyNode.visible = child.visible;\n        // Then connection from parent to dummyNode.\n        parent.children[child.relativeXPosition] = dummyNode;\n        child.oldParentNode = parent;\n        child.relativeXPosition = 0;\n        // Then connection from child to dummyNode.\n        child.parentNode = dummyNode;\n        child.parent = dummyNode.id;\n        return dummyNode;\n    }\n    /**\n     * Walker algorithm of positioning the nodes in the treegraph improved by\n     * Buchheim to run in the linear time. Basic algorithm consists of post\n     * order traversal, which starts from going bottom up (first walk), and then\n     * pre order traversal top to bottom (second walk) where adding all of the\n     * modifiers is performed.\n     * link to the paper: http://dirk.jivas.de/papers/buchheim02improving.pdf\n     *\n     * @param {TreegraphSeries} series the Treegraph series\n     */\n    calculatePositions(series) {\n        const treeLayout = this;\n        const nodes = series.nodeList;\n        this.resetValues(nodes);\n        const root = series.tree;\n        if (root) {\n            treeLayout.calculateRelativeX(root, 0);\n            treeLayout.beforeLayout(nodes);\n            treeLayout.firstWalk(root);\n            treeLayout.secondWalk(root, -root.preX);\n            treeLayout.afterLayout(nodes);\n        }\n    }\n    /**\n     * Create dummyNodes as parents for nodes, which column is changed.\n     *\n     * @param {Array<TreegraphNode>} nodes\n     *        All of the nodes.\n     */\n    beforeLayout(nodes) {\n        for (const node of nodes) {\n            for (let child of node.children) {\n                // Support for children placed in distant columns.\n                if (child && child.level - node.level > 1) {\n                    // For further columns treat the nodes as a\n                    // single parent-child pairs till the column is achieved.\n                    let gapSize = child.level - node.level - 1;\n                    // Parent -> dummyNode -> child\n                    while (gapSize > 0) {\n                        child = TreegraphLayout.createDummyNode(node, child, gapSize);\n                        gapSize--;\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Reset the calculated values from the previous run.\n     * @param {TreegraphNode[]} nodes all of the nodes.\n     */\n    resetValues(nodes) {\n        for (const node of nodes) {\n            node.mod = 0;\n            node.ancestor = node;\n            node.shift = 0;\n            node.thread = void 0;\n            node.change = 0;\n            node.preX = 0;\n        }\n    }\n    /**\n     * Assigns the value to each node, which indicates, what is his sibling\n     * number.\n     *\n     * @param {TreegraphNode} node\n     *        Root node\n     * @param {number} index\n     *        Index to which the nodes position should be set\n     */\n    calculateRelativeX(node, index) {\n        const treeLayout = this, children = node.children;\n        for (let i = 0, iEnd = children.length; i < iEnd; ++i) {\n            treeLayout.calculateRelativeX(children[i], i);\n        }\n        node.relativeXPosition = index;\n    }\n    /**\n     * Recursive post order traversal of the tree, where the initial position\n     * of the nodes is calculated.\n     *\n     * @param {TreegraphNode} node\n     *        The node for which the position should be calculated.\n     */\n    firstWalk(node) {\n        const treeLayout = this, \n        // Arbitrary value used to position nodes in respect to each other.\n        siblingDistance = 1;\n        let leftSibling;\n        // If the node is a leaf, set it's position based on the left siblings.\n        if (!node.hasChildren()) {\n            leftSibling = node.getLeftSibling();\n            if (leftSibling) {\n                node.preX = leftSibling.preX + siblingDistance;\n                node.mod = node.preX;\n            }\n            else {\n                node.preX = 0;\n            }\n        }\n        else {\n            // If the node has children, perform the recursive first walk for\n            // its children, and then calculate its shift in the apportion\n            // function (most crucial part of the algorithm).\n            let defaultAncestor = node.getLeftMostChild();\n            for (const child of node.children) {\n                treeLayout.firstWalk(child);\n                defaultAncestor = treeLayout.apportion(child, defaultAncestor);\n            }\n            treeLayout.executeShifts(node);\n            const leftChild = node.getLeftMostChild(), rightChild = node.getRightMostChild(), \n            // Set the position of the parent as a middle point of its\n            // children and move it by the value of the leftSibling (if it\n            // exists).\n            midPoint = (leftChild.preX + rightChild.preX) / 2;\n            leftSibling = node.getLeftSibling();\n            if (leftSibling) {\n                node.preX = leftSibling.preX + siblingDistance;\n                node.mod = node.preX - midPoint;\n            }\n            else {\n                node.preX = midPoint;\n            }\n        }\n    }\n    /**\n     * Pre order traversal of the tree, which sets the final xPosition of the\n     * node as its preX value and sum of all if it's parents' modifiers.\n     *\n     * @param {TreegraphNode} node\n     *        The node, for which the final position should be calculated.\n     * @param {number} modSum\n     *        The sum of modifiers of all of the parents.\n     */\n    secondWalk(node, modSum) {\n        const treeLayout = this;\n        // When the chart is not inverted we want the tree to be positioned from\n        // left to right with root node close to the chart border, this is why\n        // x and y positions are switched.\n        node.yPosition = node.preX + modSum;\n        node.xPosition = node.level;\n        for (const child of node.children) {\n            treeLayout.secondWalk(child, modSum + node.mod);\n        }\n    }\n    /**\n     *  Shift all children of the current node from right to left.\n     *\n     * @param {TreegraphNode} node\n     *        The parent node.\n     */\n    executeShifts(node) {\n        let shift = 0, change = 0;\n        for (let i = node.children.length - 1; i >= 0; i--) {\n            const childNode = node.children[i];\n            childNode.preX += shift;\n            childNode.mod += shift;\n            change += childNode.change;\n            shift += childNode.shift + change;\n        }\n    }\n    /**\n     * The core of the algorithm. The new subtree is combined with the previous\n     * subtrees. Threads are used to traverse the inside and outside contours of\n     * the left and right subtree up to the highest common level. The vertecies\n     * are left(right)Int(Out)node where Int means internal and Out means\n     * outernal. For summing up the modifiers along the contour we use the\n     * `left(right)Int(Out)mod` variable. Whenever two nodes of the inside\n     * contours are in conflict we commute the left one of the greatest uncommon\n     * ancestors using the getAncestor function and we call the moveSubtree\n     * method to shift the subtree and prepare the shifts of smaller subtrees.\n     * Finally we add a new thread (if necessary) and we adjust ancestor of\n     * right outernal node or defaultAncestor.\n     *\n     * @param {TreegraphNode} node\n     * @param {TreegraphNode} defaultAncestor\n     *        The default ancestor of the passed node.\n     */\n    apportion(node, defaultAncestor) {\n        const treeLayout = this, leftSibling = node.getLeftSibling();\n        if (leftSibling) {\n            let rightIntNode = node, rightOutNode = node, leftIntNode = leftSibling, leftOutNode = rightIntNode.getLeftMostSibling(), rightIntMod = rightIntNode.mod, rightOutMod = rightOutNode.mod, leftIntMod = leftIntNode.mod, leftOutMod = leftOutNode.mod;\n            while (leftIntNode &&\n                leftIntNode.nextRight() &&\n                rightIntNode &&\n                rightIntNode.nextLeft()) {\n                leftIntNode = leftIntNode.nextRight();\n                leftOutNode = leftOutNode.nextLeft();\n                rightIntNode = rightIntNode.nextLeft();\n                rightOutNode = rightOutNode.nextRight();\n                rightOutNode.ancestor = node;\n                const siblingDistance = 1, shift = leftIntNode.preX +\n                    leftIntMod -\n                    (rightIntNode.preX + rightIntMod) +\n                    siblingDistance;\n                if (shift > 0) {\n                    treeLayout.moveSubtree(node.getAncestor(leftIntNode, defaultAncestor), node, shift);\n                    rightIntMod += shift;\n                    rightOutMod += shift;\n                }\n                leftIntMod += leftIntNode.mod;\n                rightIntMod += rightIntNode.mod;\n                leftOutMod += leftOutNode.mod;\n                rightOutMod += rightOutNode.mod;\n            }\n            if (leftIntNode &&\n                leftIntNode.nextRight() &&\n                !rightOutNode.nextRight()) {\n                rightOutNode.thread = leftIntNode.nextRight();\n                rightOutNode.mod += leftIntMod - rightOutMod;\n            }\n            if (rightIntNode &&\n                rightIntNode.nextLeft() &&\n                !leftOutNode.nextLeft()) {\n                leftOutNode.thread = rightIntNode.nextLeft();\n                leftOutNode.mod += rightIntMod - leftOutMod;\n            }\n            defaultAncestor = node;\n        }\n        return defaultAncestor;\n    }\n    /**\n     * Shifts the subtree from leftNode to rightNode.\n     *\n     * @param {TreegraphNode} leftNode\n     * @param {TreegraphNode} rightNode\n     * @param {number} shift\n     *        The value, by which the subtree should be moved.\n     */\n    moveSubtree(leftNode, rightNode, shift) {\n        const subtrees = rightNode.relativeXPosition - leftNode.relativeXPosition;\n        rightNode.change -= shift / subtrees;\n        rightNode.shift += shift;\n        rightNode.preX += shift;\n        rightNode.mod += shift;\n        leftNode.change += shift / subtrees;\n    }\n    /**\n     * Clear values created in a beforeLayout.\n     *\n     * @param {TreegraphNode[]} nodes\n     *        All of the nodes of the Treegraph Series.\n     */\n    afterLayout(nodes) {\n        for (const node of nodes) {\n            if (node.oldParentNode) {\n                // Restore default connections\n                node.relativeXPosition = node.parentNode.relativeXPosition;\n                node.parent = node.oldParentNode.parent;\n                node.parentNode = node.oldParentNode;\n                // Delete dummyNode\n                delete node.oldParentNode.children[node.relativeXPosition];\n                node.oldParentNode.children[node.relativeXPosition] = node;\n                node.oldParentNode = void 0;\n            }\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphLayout = (TreegraphLayout);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * A treegraph series is a diagram, which shows a relation between ancestors\n * and descendants with a clear parent - child relation.\n * The best examples of the dataStructures, which best reflect this chart\n * are e.g. genealogy tree or directory structure.\n *\n * TODO change back the demo path\n * @sample highcharts/demo/treegraph-chart\n *         Treegraph Chart\n *\n * @extends      plotOptions.treemap\n * @excluding    layoutAlgorithm, dashStyle, linecap, lineWidth,\n *               negativeColor, threshold, zones, zoneAxis, colorAxis,\n *               colorKey, compare, dataGrouping, endAngle, gapSize, gapUnit,\n *               ignoreHiddenPoint, innerSize, joinBy, legendType, linecap,\n *               minSize, navigatorOptions, pointRange, allowTraversingTree,\n *               alternateStartingDirection, borderRadius, breadcrumbs,\n *               interactByLeaf, layoutStartingDirection, levelIsConstant,\n *               lineWidth, negativeColor, nodes, sortIndex, zoneAxis,\n *               zones, cluster\n *\n * @product      highcharts\n * @since 10.3.0\n * @requires     modules/treemap\n * @requires     modules/treegraph\n * @optionparent plotOptions.treegraph\n */\nconst TreegraphSeriesDefaults = {\n    /**\n     * Flips the positions of the nodes of a treegraph along the\n     * horizontal axis (vertical if chart is inverted).\n     *\n     * @sample highcharts/series-treegraph/reversed-nodes\n     *         Treegraph series with reversed nodes.\n     *\n     * @type    {boolean}\n     * @default false\n     * @product highcharts\n     * @since 10.3.0\n     */\n    reversed: false,\n    /**\n     * @extends   plotOptions.series.marker\n     * @excluding enabled, enabledThreshold\n     */\n    marker: {\n        radius: 10,\n        lineWidth: 0,\n        symbol: 'circle',\n        fillOpacity: 1,\n        states: {}\n    },\n    link: {\n        /**\n         * Modifier of the shape of the curved link. Works best for\n         * values between 0 and 1, where 0 is a straight line, and 1 is\n         * a shape close to the default one.\n         *\n         * @type      {number}\n         * @default   0.5\n         * @product   highcharts\n         * @since 10.3.0\n         * @apioption series.treegraph.link.curveFactor\n         */\n        /**\n         * The color of the links between nodes.\n         *\n         * @type {Highcharts.ColorString}\n         * @private\n         */\n        color: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The line width of the links connecting nodes, in pixels.\n         * @type {number}\n         *\n         * @private\n         */\n        lineWidth: 1,\n        /**\n         * Radius for the rounded corners of the links between nodes.\n         * Works for `default` link type.\n         *\n         * @private\n         */\n        radius: 10,\n        cursor: 'default',\n        /**\n         * Type of the link shape.\n         *\n         * @sample   highcharts/series-treegraph/link-types\n         *           Different link types\n         *\n         * @type {'default' | 'curved' | 'straight'}\n         * @product highcharts\n         *\n         */\n        type: 'curved'\n    },\n    /**\n     * Options applied to collapse Button. The collape button is the\n     * small button which indicates, that the node is collapsable.\n     */\n    collapseButton: {\n        /**\n         * Whether the button should be visible only when the node is\n         * hovered. When set to true, the button is hidden for nodes,\n         * which are not collapsed, and shown for the collapsed ones.\n         */\n        onlyOnHover: true,\n        /**\n         * Whether the button should be visible.\n         */\n        enabled: true,\n        /**\n         * The line width of the button in pixels\n         */\n        lineWidth: 1,\n        /**\n         * Offset of the button in the x direction.\n         */\n        x: 0,\n        /**\n         * Offset of the button in the y direction.\n         */\n        y: 0,\n        /**\n         * Height of the button.\n         */\n        height: 18,\n        /**\n         * Width of the button.\n         */\n        width: 18,\n        /**\n         * The symbol of the collapse button.\n         */\n        shape: 'circle',\n        /**\n         * CSS styles for the collapse button.\n         *\n         * In styled mode, the collapse button style is given in the\n         * `.highcharts-collapse-button` class.\n         */\n        style: {\n            cursor: 'pointer',\n            fontWeight: 'bold',\n            fontSize: '1em'\n        }\n    },\n    /**\n     * Whether the treegraph series should fill the entire plot area in the X\n     * axis direction, even when there are collapsed points.\n     *\n     * @sample  highcharts/series-treegraph/fillspace\n     *          Fill space demonstrated\n     *\n     * @product highcharts\n     */\n    fillSpace: false,\n    /**\n     * @extends plotOptions.series.tooltip\n     * @excluding clusterFormat\n     */\n    tooltip: {\n        /**\n         * The HTML of the point's line in the tooltip. Variables are\n         * enclosed by curly brackets. Available variables are\n         * `point.id`,  `point.fromNode.id`, `point.toNode.id`,\n         * `series.name`, `series.color` and other properties on the\n         * same form. Furthermore, This can also be overridden for each\n         * series, which makes it a good hook for displaying units. In\n         * styled mode, the dot is colored by a class name rather than\n         * the point color.\n         *\n         * @type {string}\n         * @since 10.3.0\n         * @product highcharts\n         */\n        linkFormat: '{point.fromNode.id} \\u2192 {point.toNode.id}',\n        pointFormat: '{point.id}'\n        /**\n         * A callback function for formatting the HTML output for a\n         * single link in the tooltip. Like the `linkFormat` string,\n         * but with more flexibility.\n         *\n         * @type {Highcharts.FormatterCallbackFunction.<Highcharts.Point>}\n         * @apioption series.treegraph.tooltip.linkFormatter\n         *\n         */\n    },\n    /**\n     * Options for the data labels appearing on top of the nodes and\n     * links. For treegraph charts, data labels are visible for the\n     * nodes by default, but hidden for links. This is controlled by\n     * modifying the `nodeFormat`, and the `format` that applies to\n     * links and is an empty string by default.\n     *\n     * @declare Highcharts.SeriesTreegraphDataLabelsOptionsObject\n     */\n    dataLabels: {\n        defer: true,\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label\n         * that follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option.\n         * Setting `useHTML` to true will disable this option.\n         *\n         * @sample highcharts/series-treegraph/link-text-path\n         *         Treegraph series with link text path dataLabels.\n         *\n         * @extends plotOptions.treegraph.dataLabels.textPath\n         * @since 10.3.0\n         */\n        linkTextPath: {\n            attributes: {\n                startOffset: '50%'\n            }\n        },\n        enabled: true,\n        linkFormatter: () => '',\n        padding: 5,\n        style: {\n            textOverflow: 'none'\n        }\n    },\n    /**\n     * The distance between nodes in a tree graph in the longitudinal direction.\n     * The longitudinal direction means the direction that the chart flows - in\n     * a horizontal chart the distance is horizontal, in an inverted chart\n     * (vertical), the distance is vertical.\n     *\n     * If a number is given, it denotes pixels. If a percentage string is given,\n     * the distance is a percentage of the rendered node width. A `nodeDistance`\n     * of `100%` will render equal widths for the nodes and the gaps between\n     * them.\n     *\n     * This option applies only when the `nodeWidth` option is `auto`, making\n     * the node width respond to the number of columns.\n     *\n     * @since 11.4.0\n     * @sample highcharts/series-treegraph/node-distance\n     *         Node distance of 100% means equal to node width\n     * @type   {number|string}\n     */\n    nodeDistance: 30,\n    /**\n     * The pixel width of each node in a, or the height in case the chart is\n     * inverted. For tree graphs, the node width is only applied if the marker\n     * symbol is `rect`, otherwise the `marker` sizing options apply.\n     *\n     * Can be a number or a percentage string, or `auto`. If `auto`, the nodes\n     * are sized to fill up the plot area in the longitudinal direction,\n     * regardless of the number of levels.\n     *\n     * @since 11.4.0\n     * @see    [treegraph.nodeDistance](#nodeDistance)\n     * @sample highcharts/series-treegraph/node-distance\n     *         Node width is auto and combined with node distance\n     *\n     * @type {number|string}\n     */\n    nodeWidth: void 0\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphSeriesDefaults = (TreegraphSeriesDefaults);\n\n;// external [\"../highcharts.js\",\"default\",\"SVGElement\"]\nconst external_highcharts_src_js_default_SVGElement_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGElement;\nvar external_highcharts_src_js_default_SVGElement_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGElement_namespaceObject);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (external_highcharts_src_js_default_default());\nconst { addEvent: TextPath_addEvent, merge: TextPath_merge, uniqueKey, defined, extend: TextPath_extend } = (external_highcharts_src_js_default_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = TextPath_addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Treegraph/TreegraphSeries.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getLinkPath: TreegraphSeries_getLinkPath } = Series_PathUtilities;\n\nconst { series: { prototype: seriesProto }, seriesTypes: { treemap: TreemapSeries, column: ColumnSeries } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { prototype: { symbols } } = (external_highcharts_src_js_default_SVGRenderer_default());\n\n\n\nconst { getLevelOptions: TreegraphSeries_getLevelOptions, getNodeWidth: TreegraphSeries_getNodeWidth } = Series_TreeUtilities;\n\nconst { arrayMax, crisp, extend: TreegraphSeries_extend, merge: TreegraphSeries_merge, pick: TreegraphSeries_pick, relativeLength: TreegraphSeries_relativeLength, splat } = (external_highcharts_src_js_default_default());\n\n\n\n\n\nExtensions_TextPath.compose((external_highcharts_src_js_default_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Treegraph series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.treegraph\n *\n * @augments Highcharts.Series\n */\nclass TreegraphSeries extends TreemapSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nodeList = [];\n        this.links = [];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init() {\n        super.init.apply(this, arguments);\n        this.layoutAlgorythm = new Treegraph_TreegraphLayout();\n        // Register the link data labels in the label collector for overlap\n        // detection.\n        const series = this, collectors = this.chart.labelCollectors, collectorFunc = function () {\n            const linkLabels = [];\n            // Check links for overlap\n            if (series.options.dataLabels &&\n                !splat(series.options.dataLabels)[0].allowOverlap) {\n                for (const link of (series.links || [])) {\n                    if (link.dataLabel) {\n                        linkLabels.push(link.dataLabel);\n                    }\n                }\n            }\n            return linkLabels;\n        };\n        // Only add the collector function if it is not present\n        if (!collectors.some((f) => f.name === 'collectorFunc')) {\n            collectors.push(collectorFunc);\n        }\n    }\n    /**\n     * Calculate `a` and `b` parameters of linear transformation, where\n     * `finalPosition = a * calculatedPosition + b`.\n     *\n     * @return {LayoutModifiers} `a` and `b` parameter for x and y direction.\n     */\n    getLayoutModifiers() {\n        const chart = this.chart, series = this, plotSizeX = chart.plotSizeX, plotSizeY = chart.plotSizeY, columnCount = arrayMax(this.points.map((p) => p.node.xPosition));\n        let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity, maxXSize = 0, minXSize = 0, maxYSize = 0, minYSize = 0;\n        this.points.forEach((point) => {\n            // When fillSpace is on, stop the layout calculation when the hidden\n            // points are reached. (#19038)\n            if (this.options.fillSpace && !point.visible) {\n                return;\n            }\n            const node = point.node, level = series.mapOptionsToLevel[point.node.level] || {}, markerOptions = TreegraphSeries_merge(this.options.marker, level.marker, point.options.marker), nodeWidth = markerOptions.width ?? TreegraphSeries_getNodeWidth(this, columnCount), radius = TreegraphSeries_relativeLength(markerOptions.radius || 0, Math.min(plotSizeX, plotSizeY)), symbol = markerOptions.symbol, nodeSizeY = (symbol === 'circle' || !markerOptions.height) ?\n                radius * 2 :\n                TreegraphSeries_relativeLength(markerOptions.height, plotSizeY), nodeSizeX = symbol === 'circle' || !nodeWidth ?\n                radius * 2 :\n                TreegraphSeries_relativeLength(nodeWidth, plotSizeX);\n            node.nodeSizeX = nodeSizeX;\n            node.nodeSizeY = nodeSizeY;\n            let lineWidth;\n            if (node.xPosition <= minX) {\n                minX = node.xPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                minXSize = Math.max(nodeSizeX + lineWidth, minXSize);\n            }\n            if (node.xPosition >= maxX) {\n                maxX = node.xPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                maxXSize = Math.max(nodeSizeX + lineWidth, maxXSize);\n            }\n            if (node.yPosition <= minY) {\n                minY = node.yPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                minYSize = Math.max(nodeSizeY + lineWidth, minYSize);\n            }\n            if (node.yPosition >= maxY) {\n                maxY = node.yPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                maxYSize = Math.max(nodeSizeY + lineWidth, maxYSize);\n            }\n        });\n        // Calculate the values of linear transformation, which will later be\n        // applied as `nodePosition = a * x + b` for each direction.\n        const ay = maxY === minY ?\n            1 :\n            (plotSizeY - (minYSize + maxYSize) / 2) / (maxY - minY), by = maxY === minY ? plotSizeY / 2 : -ay * minY + minYSize / 2, ax = maxX === minX ?\n            1 :\n            (plotSizeX - (maxXSize + maxXSize) / 2) / (maxX - minX), bx = maxX === minX ? plotSizeX / 2 : -ax * minX + minXSize / 2;\n        return { ax, bx, ay, by };\n    }\n    getLinks() {\n        const series = this;\n        const links = [];\n        this.data.forEach((point) => {\n            const levelOptions = series.mapOptionsToLevel[point.node.level || 0] || {};\n            if (point.node.parent) {\n                const pointOptions = TreegraphSeries_merge(levelOptions, point.options);\n                if (!point.linkToParent || point.linkToParent.destroyed) {\n                    const link = new series.LinkClass(series, pointOptions, void 0, point);\n                    point.linkToParent = link;\n                }\n                else {\n                    // #19552\n                    point.collapsed = TreegraphSeries_pick(point.collapsed, (this.mapOptionsToLevel[point.node.level] || {}).collapsed);\n                    point.linkToParent.visible =\n                        point.linkToParent.toNode.visible;\n                }\n                point.linkToParent.index = links.push(point.linkToParent) - 1;\n            }\n            else {\n                if (point.linkToParent) {\n                    series.links.splice(point.linkToParent.index);\n                    point.linkToParent.destroy();\n                    delete point.linkToParent;\n                }\n            }\n        });\n        return links;\n    }\n    buildTree(id, index, level, list, parent) {\n        const point = this.points[index];\n        level = (point && point.level) || level;\n        return super.buildTree.call(this, id, index, level, list, parent);\n    }\n    markerAttribs() {\n        // The super Series.markerAttribs returns { width: NaN, height: NaN },\n        // so just disable this for now.\n        return {};\n    }\n    setCollapsedStatus(node, visibility) {\n        const point = node.point;\n        if (point) {\n            // Take the level options into account.\n            point.collapsed = TreegraphSeries_pick(point.collapsed, (this.mapOptionsToLevel[node.level] || {}).collapsed);\n            point.visible = visibility;\n            visibility = visibility === false ? false : !point.collapsed;\n        }\n        node.children.forEach((childNode) => {\n            this.setCollapsedStatus(childNode, visibility);\n        });\n    }\n    drawTracker() {\n        ColumnSeries.prototype.drawTracker.apply(this, arguments);\n        ColumnSeries.prototype.drawTracker.call(this, this.links);\n    }\n    /**\n     * Run pre-translation by generating the nodeColumns.\n     * @private\n     */\n    translate() {\n        const series = this, options = series.options;\n        // NOTE: updateRootId modifies series.\n        let rootId = Series_TreeUtilities.updateRootId(series), rootNode;\n        // Call prototype function\n        seriesProto.translate.call(series);\n        const tree = series.tree = series.getTree();\n        rootNode = series.nodeMap[rootId];\n        if (rootId !== '' && (!rootNode || !rootNode.children.length)) {\n            series.setRootNode('', false);\n            rootId = series.rootNode;\n            rootNode = series.nodeMap[rootId];\n        }\n        series.mapOptionsToLevel = TreegraphSeries_getLevelOptions({\n            from: rootNode.level + 1,\n            levels: options.levels,\n            to: tree.height,\n            defaults: {\n                levelIsConstant: series.options.levelIsConstant,\n                colorByPoint: options.colorByPoint\n            }\n        });\n        this.setCollapsedStatus(tree, true);\n        series.links = series.getLinks();\n        series.setTreeValues(tree);\n        this.layoutAlgorythm.calculatePositions(series);\n        series.layoutModifier = this.getLayoutModifiers();\n        this.points.forEach((point) => {\n            this.translateNode(point);\n        });\n        this.points.forEach((point) => {\n            if (point.linkToParent) {\n                this.translateLink(point.linkToParent);\n            }\n        });\n        if (!options.colorByPoint) {\n            series.setColorRecursive(series.tree);\n        }\n    }\n    translateLink(link) {\n        const fromNode = link.fromNode, toNode = link.toNode, linkWidth = this.options.link?.lineWidth || 0, factor = TreegraphSeries_pick(this.options.link?.curveFactor, 0.5), type = TreegraphSeries_pick(link.options.link?.type, this.options.link?.type, 'default');\n        if (fromNode.shapeArgs && toNode.shapeArgs) {\n            const fromNodeWidth = (fromNode.shapeArgs.width || 0), inverted = this.chart.inverted, y1 = crisp((fromNode.shapeArgs.y || 0) +\n                (fromNode.shapeArgs.height || 0) / 2, linkWidth), y2 = crisp((toNode.shapeArgs.y || 0) +\n                (toNode.shapeArgs.height || 0) / 2, linkWidth);\n            let x1 = crisp((fromNode.shapeArgs.x || 0) + fromNodeWidth, linkWidth), x2 = crisp(toNode.shapeArgs.x || 0, linkWidth);\n            if (inverted) {\n                x1 -= fromNodeWidth;\n                x2 += (toNode.shapeArgs.width || 0);\n            }\n            const diff = toNode.node.xPosition - fromNode.node.xPosition;\n            link.shapeType = 'path';\n            const fullWidth = Math.abs(x2 - x1) + fromNodeWidth, width = (fullWidth / diff) - fromNodeWidth, offset = width * factor * (inverted ? -1 : 1);\n            const xMiddle = crisp((x2 + x1) / 2, linkWidth);\n            link.plotX = xMiddle;\n            link.plotY = y2;\n            link.shapeArgs = {\n                d: TreegraphSeries_getLinkPath[type]({\n                    x1,\n                    y1,\n                    x2,\n                    y2,\n                    width,\n                    offset,\n                    inverted,\n                    parentVisible: toNode.visible,\n                    radius: this.options.link?.radius\n                })\n            };\n            link.dlBox = {\n                x: (x1 + x2) / 2,\n                y: (y1 + y2) / 2,\n                height: linkWidth,\n                width: 0\n            };\n            link.tooltipPos = inverted ? [\n                (this.chart.plotSizeY || 0) - link.dlBox.y,\n                (this.chart.plotSizeX || 0) - link.dlBox.x\n            ] : [\n                link.dlBox.x,\n                link.dlBox.y\n            ];\n        }\n    }\n    /**\n     * Private method responsible for adjusting the dataLabel options for each\n     * node-point individually.\n     */\n    drawNodeLabels(points) {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel;\n        let options, level;\n        for (const point of points) {\n            level = mapOptionsToLevel[point.node.level];\n            // Set options to new object to avoid problems with scope\n            options = { style: {} };\n            // If options for level exists, include them as well\n            if (level && level.dataLabels) {\n                options = TreegraphSeries_merge(options, level.dataLabels);\n                series.hasDataLabels = () => true;\n            }\n            // Set dataLabel width to the width of the point shape.\n            if (point.shapeArgs &&\n                series.options.dataLabels) {\n                const css = {};\n                let { width = 0, height = 0 } = point.shapeArgs;\n                if (series.chart.inverted) {\n                    [width, height] = [height, width];\n                }\n                if (!splat(series.options.dataLabels)[0].style?.width) {\n                    css.width = `${width}px`;\n                }\n                if (!splat(series.options.dataLabels)[0].style?.lineClamp) {\n                    css.lineClamp = Math.floor(height / 16);\n                }\n                TreegraphSeries_extend(options.style, css);\n                point.dataLabel?.css(css);\n            }\n            // Merge custom options with point options\n            point.dlOptions = TreegraphSeries_merge(options, point.options.dataLabels);\n        }\n        seriesProto.drawDataLabels.call(this, points);\n    }\n    /**\n     * Override alignDataLabel so that position is always calculated and the\n     * label is faded in and out instead of hidden/shown when collapsing and\n     * expanding nodes.\n     */\n    alignDataLabel(point, dataLabel) {\n        const visible = point.visible;\n        // Force position calculation and visibility\n        point.visible = true;\n        super.alignDataLabel.apply(this, arguments);\n        // Fade in or out\n        dataLabel.animate({\n            opacity: visible === false ? 0 : 1\n        }, void 0, function () {\n            // Hide data labels that belong to hidden points (#18891)\n            visible || dataLabel.hide();\n        });\n        // Reset\n        point.visible = visible;\n    }\n    /**\n     * Treegraph has two separate collecions of nodes and lines,\n     * render dataLabels for both sets.\n     */\n    drawDataLabels() {\n        if (this.options.dataLabels) {\n            this.options.dataLabels = splat(this.options.dataLabels);\n            // Render node labels.\n            this.drawNodeLabels(this.points);\n            // Render link labels.\n            seriesProto.drawDataLabels.call(this, this.links);\n        }\n    }\n    destroy() {\n        // Links must also be destroyed.\n        if (this.links) {\n            for (const link of this.links) {\n                link.destroy();\n            }\n            this.links.length = 0;\n        }\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, levelOptions = point &&\n            series.mapOptionsToLevel[point.node.level || 0] || {}, options = point && point.options, stateOptions = (levelOptions.states &&\n            levelOptions.states[state]) ||\n            {};\n        if (point) {\n            point.options.marker = TreegraphSeries_merge(series.options.marker, levelOptions.marker, point.options.marker);\n        }\n        const linkColor = TreegraphSeries_pick(stateOptions && stateOptions.link && stateOptions.link.color, options && options.link && options.link.color, levelOptions && levelOptions.link && levelOptions.link.color, series.options.link && series.options.link.color), linkLineWidth = TreegraphSeries_pick(stateOptions && stateOptions.link &&\n            stateOptions.link.lineWidth, options && options.link && options.link.lineWidth, levelOptions && levelOptions.link &&\n            levelOptions.link.lineWidth, series.options.link && series.options.link.lineWidth), attribs = seriesProto.pointAttribs.call(series, point, state);\n        if (point) {\n            if (point.isLink) {\n                attribs.stroke = linkColor;\n                attribs['stroke-width'] = linkLineWidth;\n                delete attribs.fill;\n            }\n            if (!point.visible) {\n                attribs.opacity = 0;\n            }\n        }\n        return attribs;\n    }\n    drawPoints() {\n        TreemapSeries.prototype.drawPoints.apply(this, arguments);\n        ColumnSeries.prototype.drawPoints.call(this, this.links);\n    }\n    /**\n     * Run translation operations for one node.\n     * @private\n     */\n    translateNode(point) {\n        const chart = this.chart, node = point.node, plotSizeY = chart.plotSizeY, plotSizeX = chart.plotSizeX, \n        // Get the layout modifiers which are common for all nodes.\n        { ax, bx, ay, by } = this.layoutModifier, x = ax * node.xPosition + bx, y = ay * node.yPosition + by, level = this.mapOptionsToLevel[node.level] || {}, markerOptions = TreegraphSeries_merge(this.options.marker, level.marker, point.options.marker), symbol = markerOptions.symbol, height = node.nodeSizeY, width = node.nodeSizeX, reversed = this.options.reversed, nodeX = node.x = (chart.inverted ?\n            plotSizeX - width / 2 - x :\n            x - width / 2), nodeY = node.y = (!reversed ?\n            plotSizeY - y - height / 2 :\n            y - height / 2), borderRadius = TreegraphSeries_pick(point.options.borderRadius, level.borderRadius, this.options.borderRadius), symbolFn = symbols[symbol || 'circle'];\n        if (symbolFn === void 0) {\n            point.hasImage = true;\n            point.shapeType = 'image';\n            point.imageUrl = symbol.match(/^url\\((.*?)\\)$/)[1];\n        }\n        else {\n            point.shapeType = 'path';\n        }\n        if (!point.visible && point.linkToParent) {\n            const parentNode = point.linkToParent.fromNode;\n            if (parentNode) {\n                const parentShapeArgs = parentNode.shapeArgs || {}, { x = 0, y = 0, width = 0, height = 0 } = parentShapeArgs;\n                if (!point.shapeArgs) {\n                    point.shapeArgs = {};\n                }\n                if (!point.hasImage) {\n                    TreegraphSeries_extend(point.shapeArgs, {\n                        d: symbolFn(x, y, width, height, borderRadius ? { r: borderRadius } : void 0)\n                    });\n                }\n                TreegraphSeries_extend(point.shapeArgs, { x, y });\n                point.plotX = parentNode.plotX;\n                point.plotY = parentNode.plotY;\n            }\n        }\n        else {\n            point.plotX = nodeX;\n            point.plotY = nodeY;\n            point.shapeArgs = {\n                x: nodeX,\n                y: nodeY,\n                width,\n                height,\n                cursor: !point.node.isLeaf ? 'pointer' : 'default'\n            };\n            if (!point.hasImage) {\n                point.shapeArgs.d = symbolFn(nodeX, nodeY, width, height, borderRadius ? { r: borderRadius } : void 0);\n            }\n        }\n        // Set the anchor position for tooltip.\n        point.tooltipPos = chart.inverted ?\n            [plotSizeY - nodeY - height / 2, plotSizeX - nodeX - width / 2] :\n            [nodeX + width / 2, nodeY];\n    }\n}\nTreegraphSeries.defaultOptions = TreegraphSeries_merge(TreemapSeries.defaultOptions, Treegraph_TreegraphSeriesDefaults);\nTreegraphSeries_extend(TreegraphSeries.prototype, {\n    pointClass: Treegraph_TreegraphPoint,\n    NodeClass: Treegraph_TreegraphNode,\n    LinkClass: TreegraphLink\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('treegraph', TreegraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treegraph_TreegraphSeries = ((/* unused pure expression or super */ null && (TreegraphSeries)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `treegraph` series. If the [type](#series.treegraph.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.treegraph\n * @exclude   allowDrillToNode, boostBlending, boostThreshold, curveFactor,\n * centerInCategory, connectEnds, connectNulls, colorAxis, colorKey,\n * dataSorting, dragDrop, findNearestPointBy, getExtremesFromAll, groupPadding,\n * headers, layout, nodePadding, nodeSizeBy, pointInterval, pointIntervalUnit,\n * pointPlacement, pointStart, relativeXValue, softThreshold, stack, stacking,\n * step, traverseUpButton, xAxis, yAxis, zoneAxis, zones\n * @product   highcharts\n * @requires  modules/treemap\n * @requires  modules/treegraph\n * @apioption series.treegraph\n */\n/**\n * @extends   plotOptions.series.marker\n * @excluding enabled, enabledThreshold\n * @apioption series.treegraph.marker\n */\n/**\n * @type      {Highcharts.SeriesTreegraphDataLabelsOptionsObject|Array<Highcharts.SeriesTreegraphDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.treegraph.data.dataLabels\n */\n/**\n * @sample highcharts/series-treegraph/level-options\n *          Treegraph chart with level options applied\n *\n * @type      {Array<*>}\n * @excluding layoutStartingDirection, layoutAlgorithm\n * @apioption series.treegraph.levels\n */\n/**\n * Set collapsed status for nodes level-wise.\n * @type {boolean}\n * @apioption series.treegraph.levels.collapsed\n */\n/**\n * Set marker options for nodes at the level.\n * @extends   series.treegraph.marker\n * @apioption series.treegraph.levels.marker\n */\n/**\n * An array of data points for the series. For the `treegraph` series type,\n * points can be given in the following ways:\n *\n * 1. The array of arrays, with `keys` property, which defines how the fields in\n *     array should be interpreted\n *    ```js\n *       keys: ['id', 'parent'],\n *       data: [\n *           ['Category1'],\n *           ['Category1', 'Category2']\n *       ]\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the\n *    series' [turboThreshold](#series.area.turboThreshold),\n *    this option is not available.\n *    The data of the treegraph series needs to be formatted in such a way, that\n *    there are no circular dependencies on the nodes\n *\n *  ```js\n *     data: [{\n *         id: 'Category1'\n *     }, {\n *         id: 'Category1',\n *         parent: 'Category2',\n *     }]\n *  ```\n *\n * @type      {Array<*>}\n * @extends   series.treemap.data\n * @product   highcharts\n * @excluding outgoing, weight, value\n * @apioption series.treegraph.data\n */\n/**\n * Options used for button, which toggles the collapse status of the node.\n *\n *\n * @apioption series.treegraph.data.collapseButton\n */\n/**\n * If point's children should be initially hidden\n *\n * @sample highcharts/series-treegraph/level-options\n *          Treegraph chart with initially hidden children\n *\n * @type {boolean}\n * @apioption series.treegraph.data.collapsed\n */\n''; // Gets doclets above into transpiled version\n\n;// ./code/es-modules/masters/modules/treegraph.js\n\n\n\n\n\n/* harmony default export */ const treegraph_src = ((external_highcharts_src_js_default_default()));\n\nexport { treegraph_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "applyRadius", "path", "r", "i", "length", "x", "y", "push", "prevSeg", "nextSeg", "x1", "y1", "x2", "y2", "directionX", "directionY", "Math", "min", "abs", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "external_highcharts_src_js_default_SVGRenderer_namespaceObject", "<PERSON><PERSON><PERSON><PERSON>", "external_highcharts_src_js_default_SVGRenderer_default", "seriesTypes", "treemap", "NodeClass", "TreemapNode", "Treegraph_TreegraphNode", "constructor", "arguments", "mod", "shift", "change", "children", "preX", "hidden", "wasVisited", "collapsed", "nextLeft", "getLeftMostChild", "thread", "nextRight", "getRightMostChild", "getAncestor", "leftIntNode", "defaultAncestor", "leftAnc", "ancestor", "getLeftMostSibling", "parent", "getParent", "child", "point", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getLeftSibling", "relativeXPosition", "parentNode", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "external_highcharts_src_js_default_Point_namespaceObject", "Point", "external_highcharts_src_js_default_Point_default", "pointClass", "TreemapPoint", "addEvent", "fireEvent", "merge", "TreegraphPoint", "dataLabelOnHidden", "isLink", "setState", "draw", "apply", "graphic", "animate", "visibility", "renderCollapseButton", "series", "parentGroup", "levelOptions", "mapOptionsToLevel", "node", "level", "btnOptions", "options", "collapseButton", "width", "height", "shape", "style", "chart", "calculatedOpacity", "onlyOnHover", "state", "shapeArgs", "collapseButtonOptions", "enabled", "getCollapseBtnPosition", "attr", "text", "rotation", "inverted", "rotationOriginX", "rotationOriginY", "opacity", "destroy", "fill", "fillColor", "color", "renderer", "label", "padding", "stroke", "lineColor", "lineWidth", "align", "zIndex", "addClass", "removeClass", "css", "getContrast", "add", "element", "toggleCollapse", "update", "redraw", "linkToParent", "btnWidth", "btnHeight", "btn", "states", "hover", "animation", "external_highcharts_src_js_default_Color_namespaceObject", "Color", "external_highcharts_src_js_default_Color_default", "extend", "isArray", "isNumber", "isObject", "TreeUtilities_merge", "pick", "<PERSON><PERSON><PERSON><PERSON>", "Series_TreeUtilities", "getColor", "colorByPoint", "colorIndexByPoint", "colorIndex", "index", "parentColor", "parentColorIndex", "colors", "siblings", "points", "chartOptionsChart", "colorCount", "styledMode", "variateColor", "colorVariation", "parse", "brighten", "to", "getLevelOptions", "params", "defaults", "converted", "from", "levels", "result", "reduce", "item", "levelIsConstant", "getNodeWidth", "columnCount", "nodeDistance", "nodeWidth", "plotSizeX", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "childrenTotal", "levelDynamic", "name", "id", "for<PERSON>ach", "newOptions", "val", "value", "<PERSON><PERSON><PERSON><PERSON>", "updateRootId", "rootId", "rootNode", "userOptions", "TreegraphLink_pick", "TreegraphLink_extend", "column", "ColumnPoint", "TreegraphLink", "dataLabelOnNull", "formatPrefix", "fromNode", "toNode", "runEvent", "oldOptions", "TreegraphLayout", "createDummyNode", "gapSize", "dummy<PERSON>ode", "oldParentNode", "calculatePositions", "nodes", "nodeList", "resetValues", "root", "treeLayout", "calculateRelativeX", "beforeLayout", "firstWalk", "secondWalk", "afterLayout", "iEnd", "leftSibling", "apportion", "executeShifts", "leftChild", "<PERSON><PERSON><PERSON><PERSON>", "midPoint", "modSum", "yPosition", "xPosition", "childNode", "rightIntNode", "rightOutNode", "leftOutNode", "rightIntMod", "rightOutMod", "leftIntMod", "leftOutMod", "moveSubtree", "leftNode", "rightNode", "subtrees", "external_highcharts_src_js_default_SVGElement_namespaceObject", "SVGElement", "external_highcharts_src_js_default_SVGElement_default", "deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "defined", "TextPath_extend", "setTextPath", "textPathOptions", "attributes", "dy", "startOffset", "textAnchor", "url", "textWrapper", "textPath", "undo", "e", "textPathId", "textAttribs", "dx", "transform", "box", "slice", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "getLinkPath", "TreegraphSeries_getLinkPath", "pathParams", "radius", "parentVisible", "straight", "curved", "offset", "seriesProto", "TreemapSeries", "ColumnSeries", "symbols", "TreegraphSeries_getLevelOptions", "TreegraphSeries_getNodeWidth", "arrayMax", "crisp", "TreegraphSeries_extend", "TreegraphSeries_merge", "TreegraphSeries_pick", "TreegraphSeries_relativeLength", "splat", "Extensions_TextPath", "compose", "SVGElementClass", "svgElementProto", "TreegraphSeries", "links", "init", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectors", "labelCollectors", "some", "f", "linkLabels", "dataLabels", "allowOverlap", "link", "dataLabel", "getLayoutModifiers", "plotSizeY", "map", "p", "minX", "Infinity", "maxX", "minY", "maxY", "maxXSize", "minXSize", "maxYSize", "minYSize", "fillSpace", "markerOptions", "marker", "symbol", "nodeSizeY", "nodeSizeX", "max", "ay", "by", "ax", "bx", "getLinks", "data", "pointOptions", "destroyed", "LinkClass", "splice", "buildTree", "list", "markerAttribs", "setCollapsedStatus", "drawTracker", "translate", "getTree", "nodeMap", "setRootNode", "layoutModifier", "translateNode", "translateLink", "setColorRecursive", "linkWidth", "factor", "curveFactor", "type", "fromNodeWidth", "diff", "shapeType", "plotX", "plotY", "dlBox", "tooltipPos", "drawNodeLabels", "hasDataLabels", "lineClamp", "floor", "dlOptions", "drawDataLabels", "alignDataLabel", "hide", "pointAttribs", "stateOptions", "linkColor", "linkLineWidth", "attribs", "drawPoints", "reversed", "nodeX", "nodeY", "borderRadius", "symbolFn", "hasImage", "imageUrl", "match", "cursor", "defaultOptions", "fillOpacity", "fontWeight", "fontSize", "tooltip", "linkFormat", "pointFormat", "defer", "linkTextPath", "linkFormatter", "textOverflow", "registerSeriesType", "treegraph_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,AAChG,OAAsE,kBAAmB,CAEhF,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GA0FpF,SAASE,EAAYC,CAAI,CAAEC,CAAC,EACxB,IAAMnB,EAAI,EAAE,CACZ,IAAK,IAAIoB,EAAI,EAAGA,EAAIF,EAAKG,MAAM,CAAED,IAAK,CAClC,IAAME,EAAIJ,CAAI,CAACE,EAAE,CAAC,EAAE,CACdG,EAAIL,CAAI,CAACE,EAAE,CAAC,EAAE,CACpB,GAAI,AAAa,UAAb,OAAOE,GAAkB,AAAa,UAAb,OAAOC,GAEhC,GAAIH,AAAM,IAANA,EACApB,EAAEwB,IAAI,CAAC,CAAC,IAAKF,EAAGC,EAAE,OAEjB,GAAIH,IAAMF,EAAKG,MAAM,CAAG,EACzBrB,EAAEwB,IAAI,CAAC,CAAC,IAAKF,EAAGC,EAAE,OAGjB,GAAIJ,EAAG,CACR,IAAMM,EAAUP,CAAI,CAACE,EAAI,EAAE,CACrBM,EAAUR,CAAI,CAACE,EAAI,EAAE,CAC3B,GAAIK,GAAWC,EAAS,CACpB,IAAMC,EAAKF,CAAO,CAAC,EAAE,CAAEG,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKH,CAAO,CAAC,EAAE,CAAEI,EAAKJ,CAAO,CAAC,EAAE,CAExE,GAAI,AAAc,UAAd,OAAOC,GACP,AAAc,UAAd,OAAOE,GACP,AAAc,UAAd,OAAOD,GACP,AAAc,UAAd,OAAOE,GACPH,IAAOE,GACPD,IAAOE,EAAI,CACX,IAAMC,EAAaJ,EAAKE,EAAK,EAAI,GAAIG,EAAaJ,EAAKE,EAAK,EAAI,GAChE9B,EAAEwB,IAAI,CAAC,CACH,IACAF,EAAIS,EAAaE,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACb,EAAIK,GAAKR,GAC5CI,EAAIS,EAAaC,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACZ,EAAIK,GAAKT,GAC/C,CAAE,CACC,IACAG,EACAC,EACAD,EACAC,EACAD,EAAIS,EAAaE,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACb,EAAIO,GAAKV,GAC5CI,EAAIS,EAAaC,KAAKC,GAAG,CAACD,KAAKE,GAAG,CAACZ,EAAIO,GAAKX,GAC/C,CACL,CACJ,CAEJ,MAEInB,EAAEwB,IAAI,CAAC,CAAC,IAAKF,EAAGC,EAAE,EAG9B,CACA,OAAOvB,CACX,CAzIaL,EAAoBK,CAAC,CAAzB,CAAC,EAI4C,CAAG,GA6IzD,IAAMoC,EAAoE1C,EAAwD,OAAU,CAAC2C,cAAc,CAC3J,IAAIC,EAAyE3C,EAAoBC,CAAC,CAACwC,GAEnG,IAAMG,EAAiE7C,EAAwD,OAAU,CAAC8C,WAAW,CACrJ,IAAIC,EAAsE9C,EAAoBC,CAAC,CAAC2C,GAahG,GAAM,CAAEG,YAAa,CAAEC,QAAS,CAAE/B,UAAW,CAAEgC,UAAWC,CAAW,CAAE,CAAE,CAAE,CAAE,CAAIP,IA+K9CQ,EArKnC,cAA4BD,EACxBE,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,GAAG,CAAG,EACX,IAAI,CAACC,KAAK,CAAG,EACb,IAAI,CAACC,MAAM,CAAG,EACd,IAAI,CAACC,QAAQ,CAAG,EAAE,CAClB,IAAI,CAACC,IAAI,CAAG,EACZ,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,UAAU,CAAG,CAAA,EAClB,IAAI,CAACC,SAAS,CAAG,CAAA,CACrB,CAYAC,UAAW,CACP,OAAO,IAAI,CAACC,gBAAgB,IAAM,IAAI,CAACC,MAAM,AACjD,CAOAC,WAAY,CACR,OAAO,IAAI,CAACC,iBAAiB,IAAM,IAAI,CAACF,MAAM,AAClD,CAYAG,YAAYC,CAAW,CAAEC,CAAe,CAAE,QAEtC,AAAIC,AADYF,EAAYG,QAAQ,CACxBd,QAAQ,CAAC,EAAE,GAAK,IAAI,CAACA,QAAQ,CAAC,EAAE,CACjCW,EAAYG,QAAQ,CAExBF,CACX,CAQAG,oBAAqB,CACjB,IAAMC,EAAS,IAAI,CAACC,SAAS,GAC7B,GAAID,EACA,CAAA,IAAK,IAAME,KAASF,EAAOhB,QAAQ,CAC/B,GAAIkB,GAASA,EAAMC,KAAK,CAACC,OAAO,CAC5B,OAAOF,CAEf,CAER,CAOAG,aAAc,CACV,IAAMrB,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAIhC,EAAI,EAAGA,EAAIgC,EAAS/B,MAAM,CAAED,IACjC,GAAIgC,CAAQ,CAAChC,EAAE,CAACmD,KAAK,CAACC,OAAO,CACzB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,CAOAE,gBAAiB,CACb,IAAMN,EAAS,IAAI,CAACC,SAAS,GAC7B,GAAID,EAAQ,CACR,IAAMhB,EAAWgB,EAAOhB,QAAQ,CAChC,IAAK,IAAIhC,EAAI,IAAI,CAACuD,iBAAiB,CAAG,EAAGvD,GAAK,EAAGA,IAC7C,GAAIgC,CAAQ,CAAChC,EAAE,EAAIgC,CAAQ,CAAChC,EAAE,CAACmD,KAAK,CAACC,OAAO,CACxC,OAAOpB,CAAQ,CAAChC,EAAE,AAG9B,CACJ,CAOAsC,kBAAmB,CACf,IAAMN,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAIhC,EAAI,EAAGA,EAAIgC,EAAS/B,MAAM,CAAED,IACjC,GAAIgC,CAAQ,CAAChC,EAAE,CAACmD,KAAK,CAACC,OAAO,CACzB,OAAOpB,CAAQ,CAAChC,EAAE,AAG9B,CAOAyC,mBAAoB,CAChB,IAAMT,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAIhC,EAAIgC,EAAS/B,MAAM,CAAG,EAAGD,GAAK,EAAGA,IACtC,GAAIgC,CAAQ,CAAChC,EAAE,CAACmD,KAAK,CAACC,OAAO,CACzB,OAAOpB,CAAQ,CAAChC,EAAE,AAG9B,CAQAiD,WAAY,CACR,OAAO,IAAI,CAACO,UAAU,AAC1B,CAOAC,eAAgB,CACZ,IAAMzB,EAAW,IAAI,CAACA,QAAQ,CAC9B,IAAK,IAAIhC,EAAI,EAAGA,EAAIgC,EAAS/B,MAAM,CAAED,IACjC,GAAIgC,CAAQ,CAAChC,EAAE,CAACmD,KAAK,CAACC,OAAO,CACzB,OAAOpB,CAAQ,CAAChC,EAAE,AAG9B,CACJ,EASM0D,EAA2DpF,EAAwD,OAAU,CAACqF,KAAK,CACzI,IAAIC,EAAgErF,EAAoBC,CAAC,CAACkF,GAc1F,GAAM,CAAEpC,YAAa,CAAEC,QAAS,CAAE/B,UAAW,CAAEqE,WAAYC,CAAY,CAAE,CAAE,CAAE,CAAE,CAAI5C,IAE7E,CAAE6C,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAE,CAAIrE,GAUxC,OAAMsE,UAAuBJ,EACzBnC,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACuC,iBAAiB,CAAG,CAAA,EACzB,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,QAAQ,CAAG,AAACT,IAAoDpE,SAAS,CAAC6E,QAAQ,AAC3F,CAMAC,MAAO,CACH,KAAK,CAACA,KAAKC,KAAK,CAAC,IAAI,CAAE3C,WAEvB,IAAM4C,EAAU,IAAI,CAACA,OAAO,CACxBA,GACAA,EAAQC,OAAO,CAAC,CACZC,WAAY,IAAI,CAACtB,OAAO,CAAG,UAAY,QAC3C,GAEJ,IAAI,CAACuB,oBAAoB,EAC7B,CACAA,sBAAuB,CACnB,IAAoBC,EAASzB,AAAf,IAAI,CAAiByB,MAAM,CAAEC,EAAc1B,AAA3C,IAAI,CAA6CqB,OAAO,EAAIrB,AAA5D,IAAI,CAA8DqB,OAAO,CAACK,WAAW,CAAEC,EAAeF,EAAOG,iBAAiB,CAAC5B,AAA/H,IAAI,CAAiI6B,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EAAGC,EAAajB,EAAMW,EAAOO,OAAO,CAACC,cAAc,CAAEN,EAAaM,cAAc,CAAEjC,AAA5O,IAAI,CAA8OgC,OAAO,CAACC,cAAc,EAAG,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAGN,EAAyBO,EAAQ,IAAI,CAACb,MAAM,CAACa,KAAK,CAAEC,EAAoB,AAACvC,AAAtX,IAAI,CAAwXC,OAAO,EAC5YD,CAAAA,AADS,IAAI,CACPf,SAAS,EACZ,CAAC8C,EAAWS,WAAW,EACvBxC,AAAgB,UAAhBA,AAHM,IAAI,CAGJyC,KAAK,AAAW,EAAM,EAAI,EACxC,GAAKzC,AAJS,IAAI,CAIP0C,SAAS,EAIpB,GADA,IAAI,CAACC,qBAAqB,CAAGZ,EACxB/B,AARS,IAAI,CAQPiC,cAAc,EAqCrB,GAAI,AAACjC,AA7CK,IAAI,CA6CH6B,IAAI,CAAChD,QAAQ,CAAC/B,MAAM,EAAKiF,EAAWa,OAAO,CAIjD,CACD,GAAM,CAAE7F,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC6F,sBAAsB,CAACd,GAC7C/B,AAnDM,IAAI,CAmDJiC,cAAc,CACfa,IAAI,CAAC,CACNC,KAAM/C,AArDJ,IAAI,CAqDMf,SAAS,CAAG,IAAM,IAC9B+D,SAAUV,AAAiB,KAAjBA,EAAMW,QAAQ,CACxBC,gBAAiBhB,EAAQ,EACzBiB,gBAAiBhB,EAAS,EAC1BZ,WAAYvB,AAzDV,IAAI,CAyDYC,OAAO,CAAG,UAAY,QAC5C,GACKqB,OAAO,CAAC,CACTvE,EAAAA,EACAC,EAAAA,EACAoG,QAASb,CACb,EACJ,MAlBIvC,AA9CM,IAAI,CA8CJiC,cAAc,CAACoB,OAAO,GAC5B,OAAOrD,AA/CD,IAAI,CA+CGiC,cAAc,KAvCR,CACvB,GAAI,CAACjC,AATK,IAAI,CASH6B,IAAI,CAAChD,QAAQ,CAAC/B,MAAM,EAAI,CAACiF,EAAWa,OAAO,CAClD,OAEJ,GAAM,CAAE7F,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC6F,sBAAsB,CAACd,GAAauB,EAAQvB,EAAWwB,SAAS,EAClFvD,AAbM,IAAI,CAaJwD,KAAK,EACX,SACJxD,CAfU,IAAI,CAeRiC,cAAc,CAAGK,EAAMmB,QAAQ,CAChCC,KAAK,CAAC1D,AAhBD,IAAI,CAgBGf,SAAS,CAAG,IAAM,IAAKlC,EAAGC,EAAGoF,GACzCU,IAAI,CAAC,CACNX,OAAQA,EAAS,EACjBD,MAAOA,EAAQ,EACfyB,QApByU,EAqBzUL,KAAAA,EACAN,SAAUV,AAAiB,KAAjBA,EAAMW,QAAQ,CACxBC,gBAAiBhB,EAAQ,EACzBiB,gBAAiBhB,EAAS,EAC1ByB,OAAQ7B,EAAW8B,SAAS,EAAI,UAChC,eAAgB9B,EAAW+B,SAAS,CACpC,aAAc,SACdC,MAAO,SACPC,OAAQ,EACRZ,QAASb,EACThB,WAAYvB,AA/BN,IAAI,CA+BQC,OAAO,CAAG,UAAY,QAC5C,GACKgE,QAAQ,CAAC,sBACTA,QAAQ,CAAC,8BACTC,WAAW,CAAC,yBACZC,GAAG,CAACrD,EAAM,CACX0C,MAAO,AAAgB,UAAhB,OAAOF,EACVhB,EAAMmB,QAAQ,CAACW,WAAW,CAACd,GAC3B,SACR,EAAGjB,IACEgC,GAAG,CAAC3C,GACT1B,AA1CU,IAAI,CA0CRiC,cAAc,CAACqC,OAAO,CAACtE,KAAK,CA1CxB,IAAI,AA2ClB,EAuBJ,CACAuE,eAAe9B,CAAK,CAAE,CAClB,IAAMhB,EAAS,IAAI,CAACA,MAAM,CAC1B,IAAI,CAAC+C,MAAM,CAAC,CACRvF,UAAWwD,GAAS,CAAC,IAAI,CAACxD,SAAS,AACvC,EAAG,CAAA,EAAO,KAAK,EAAG,CAAA,GAClB4B,EAAUY,EAAQ,kBAClBA,EAAOgD,MAAM,EACjB,CACApB,SAAU,CACF,IAAI,CAACpB,cAAc,GACnB,IAAI,CAACA,cAAc,CAACoB,OAAO,GAC3B,OAAO,IAAI,CAACpB,cAAc,CAC1B,IAAI,CAACA,cAAc,CAAG,KAAK,GAE3B,IAAI,CAACyC,YAAY,GACjB,IAAI,CAACA,YAAY,CAACrB,OAAO,GACzB,OAAO,IAAI,CAACqB,YAAY,EAE5B,KAAK,CAACrB,QAAQjC,KAAK,CAAC,IAAI,CAAE3C,UAC9B,CACAoE,uBAAuBd,CAAU,CAAE,CAC/B,IAAgDkB,EAAWX,AAA/BtC,AAAd,IAAI,CAAgByB,MAAM,CAACa,KAAK,CAAmBW,QAAQ,CAAE0B,EAAW5C,EAAWG,KAAK,CAAE0C,EAAY7C,EAAWI,MAAM,CAAE,CAAEpF,EAAAA,EAAI,CAAC,CAAEC,EAAAA,EAAI,CAAC,CAAEkF,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAGnC,AAAnK,IAAI,CAAqK0C,SAAS,EAAI,CAAC,EACrM,MAAO,CACH3F,EAAGA,EACCgF,EAAWhF,CAAC,CACXkG,CAAAA,EAAW,CAAA,CAAA,AAAa,GAAZ2B,CAAc,EAAI1C,EAAQyC,AAAW,IAAXA,CAAc,EACzD3H,EAAGA,EAAImF,EAAS,EAAIyC,EAAY,EAAI7C,EAAW/E,CAAC,AACpD,CACJ,CACJ,CACA4D,EAASG,EAAgB,WAAY,WACjC,IAAM8D,EAAM,IAAI,CAAC5C,cAAc,CAAEF,EAAa,IAAI,CAACY,qBAAqB,CACpEkC,GAAO9C,GAAYS,aAAe,CAAC,IAAI,CAACvD,SAAS,EACjD4F,EAAIvD,OAAO,CAAC,CAAE8B,QAAS,CAAE,EAEjC,GACAxC,EAASG,EAAgB,YAAa,WAC9B,IAAI,CAACkB,cAAc,EAAI,IAAI,CAAChC,OAAO,EACnC,IAAI,CAACgC,cAAc,CAACX,OAAO,CAAC,CAAE8B,QAAS,CAAE,EAAG,IAAI,CAAC3B,MAAM,CAACO,OAAO,CAAC8C,MAAM,EAAEC,OAAOC,UAEvF,GAEApE,EAASG,EAAgB,QAAS,WAC9B,IAAI,CAACwD,cAAc,EACvB,GASA,IAAMU,EAA2D9J,EAAwD,OAAU,CAAC+J,KAAK,CACzI,IAAIC,EAAgE/J,EAAoBC,CAAC,CAAC4J,GAgB1F,GAAM,CAAEG,OAAAA,CAAM,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEzE,MAAO0E,CAAmB,CAAEC,KAAAA,CAAI,CAAEC,eAAAA,CAAc,CAAE,CAAIjJ,IAuMhEkJ,EAPb,CAClBC,SAvLJ,SAAkB/D,CAAI,CAAEG,CAAO,EAC3B,IACqBhC,EAAO8B,EAAO+D,EAAcC,EAAmBtC,EAAOuC,EADrEC,EAAQhE,EAAQgE,KAAK,CAAEpE,EAAoBI,EAAQJ,iBAAiB,CAAEqE,EAAcjE,EAAQiE,WAAW,CAAEC,EAAmBlE,EAAQkE,gBAAgB,CAAEzE,EAASO,EAAQP,MAAM,CAAE0E,EAASnE,EAAQmE,MAAM,CAAEC,EAAWpE,EAAQoE,QAAQ,CAAEC,EAAS5E,EAAO4E,MAAM,CAAEC,EAAoB7E,EAAOa,KAAK,CAACN,OAAO,CAACM,KAAK,CA+BjT,OAhBIT,IACA7B,EAAQqG,CAAM,CAACxE,EAAKhF,CAAC,CAAC,CACtBiF,EAAQF,CAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,EACxB9B,GAAS8B,EAAM+D,YAAY,GAEzCC,EAAoB9F,EAAMgG,KAAK,CAAIG,CAAAA,EAC/BA,EAAOrJ,MAAM,CACbwJ,EAAkBC,UAAU,AAAD,EAC/BV,EAAeM,GAAUA,CAAM,CAACL,EAAkB,EAGjDrE,EAAOa,KAAK,CAACkE,UAAU,EACxBhD,CAAAA,EAAQiC,EAAKzF,GAASA,EAAMgC,OAAO,CAACwB,KAAK,CAAE1B,GAASA,EAAM0B,KAAK,CAAEqC,EAAcI,GAAeQ,AAtBjF,CAAA,AAACjD,IAClB,IAAMkD,EAAiB5E,GAASA,EAAM4E,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAe7K,GAAG,EAClBmK,GACAI,EACOjB,IAAmDwB,KAAK,CAACnD,GAAOoD,QAAQ,CAACF,EAAeG,EAAE,CAAIb,CAAAA,EAAQI,CAAO,GAAIlK,GAAG,GAExHsH,CACX,CAAA,EAamHyC,GAAcxE,EAAO+B,KAAK,CAAA,EAEzIuC,EAAaN,EAAKzF,GAASA,EAAMgC,OAAO,CAAC+D,UAAU,CAAEjE,GAASA,EAAMiE,UAAU,CAAED,EAAmBI,EAAkBlE,EAAQ+D,UAAU,GAEpI,CACHvC,MAAOA,EACPuC,WAAYA,CAChB,CACJ,EAoJIe,gBAlIJ,SAAyBC,CAAM,EAC3B,IACIC,EAAUC,EAAWpK,EAAGqK,EAAML,EAAIM,EADhCC,EAAS,CAAC,EAEhB,GAAI7B,EAASwB,GA2BT,IA1BAG,EAAO5B,EAASyB,EAAOG,IAAI,EAAIH,EAAOG,IAAI,CAAG,EAC7CC,EAASJ,EAAOI,MAAM,CACtBF,EAAY,CAAC,EACbD,EAAWzB,EAASwB,EAAOC,QAAQ,EAAID,EAAOC,QAAQ,CAAG,CAAC,EACtD3B,EAAQ8B,IACRF,CAAAA,EAAYE,EAAOE,MAAM,CAAC,CAAClL,EAAKmL,KAC5B,IAAIxF,EAAOyF,EAAiBvF,EAgB5B,OAfIuD,EAAS+B,IAAShC,EAASgC,EAAKxF,KAAK,IAErCyF,EAAkB9B,EAAKzD,AADvBA,CAAAA,EAAUwD,EAAoB,CAAC,EAAG8B,EAAI,EACPC,eAAe,CAAEP,EAASO,eAAe,EAExE,OAAOvF,EAAQuF,eAAe,CAC9B,OAAOvF,EAAQF,KAAK,CAGhByD,EAASpJ,CAAG,CADhB2F,EAAQwF,EAAKxF,KAAK,CAAIyF,CAAAA,EAAkB,EAAIL,EAAO,CAAA,EAC5B,EACnB1B,EAAoB,CAAA,EAAMrJ,CAAG,CAAC2F,EAAM,CAAEE,GAGtC7F,CAAG,CAAC2F,EAAM,CAAGE,GAGd7F,CACX,EAAG,CAAC,EAAC,EAET0K,EAAKvB,EAASyB,EAAOF,EAAE,EAAIE,EAAOF,EAAE,CAAG,EAClChK,EAAI,EAAGA,GAAKgK,EAAIhK,IACjBuK,CAAM,CAACvK,EAAE,CAAG2I,EAAoB,CAAC,EAAGwB,EAAUzB,EAAS0B,CAAS,CAACpK,EAAE,EAAIoK,CAAS,CAACpK,EAAE,CAAG,CAAC,GAG/F,OAAOuK,CACX,EAgGII,aAvBJ,SAAsB/F,CAAM,CAAEgG,CAAW,EACrC,GAAM,CAAEnF,MAAAA,CAAK,CAAEN,QAAAA,CAAO,CAAE,CAAGP,EAAQ,CAAEiG,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAG3F,EAAS,CAAE4F,UAAAA,EAAY,CAAC,CAAE,CAAGtF,EAGtG,GAAIqF,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKG,IAAI,CAACH,GAE9C,OAAOE,EADkDH,CAAAA,EAAcK,AAAtDC,WAAWL,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMO,EAAYC,OAAOP,GACzB,MAAO,AAAEE,CAAAA,EAAYI,CAAQ,EACxBP,CAAAA,GAAe,CAAA,EAAMO,CAC9B,CACA,OAAOtC,EAAeiC,EAAWC,EACrC,EAUIM,cA3FJ,SAASA,EAAcC,CAAI,CAAEnG,CAAO,EAChC,IAAMoG,EAASpG,EAAQoG,MAAM,CAAEC,EAASrG,EAAQqG,MAAM,CAAqCC,EAAWC,AAAhCvG,EAAQuG,WAAW,AAAwB,CAACF,EAAO,CAAEd,EAAmBvF,AAA4B,CAAA,IAA5BA,EAAQuF,eAAe,CAAsCvH,EAAQqG,AAAxBrE,EAAQqE,MAAM,AAAgB,CAAC8B,EAAKtL,CAAC,CAAC,CAAE2L,EAAexI,GAASA,EAAMgC,OAAO,EAAI,CAAC,EAAGnD,EAAW,EAAE,CACzR4J,EAAgB,CACpBN,CAAAA,EAAKO,YAAY,CAAGP,EAAKrG,KAAK,CAAIyF,CAAAA,EAAkB,EAAIe,EAASxG,KAAK,AAAD,EACrEqG,EAAKQ,IAAI,CAAGlD,EAAKzF,GAASA,EAAM2I,IAAI,CAAE,IACtCR,EAAKlI,OAAO,CAAIoI,IAAWF,EAAKS,EAAE,EAC9B5G,AAAoB,CAAA,IAApBA,EAAQ/B,OAAO,CACG,YAAlB,OAAOmI,GACPD,CAAAA,EAAOC,EAAOD,EAAMnG,EAAO,EAG/BmG,EAAKtJ,QAAQ,CAACgK,OAAO,CAAC,CAAC9I,EAAOlD,KAC1B,IAAMiM,EAAa1D,EAAO,CAAC,EAAGpD,GAC9BoD,EAAO0D,EAAY,CACf9C,MAAOnJ,EACPuJ,SAAU+B,EAAKtJ,QAAQ,CAAC/B,MAAM,CAC9BmD,QAASkI,EAAKlI,OAAO,AACzB,GACAF,EAAQmI,EAAcnI,EAAO+I,GAC7BjK,EAAS5B,IAAI,CAAC8C,GACVA,EAAME,OAAO,EACbwI,CAAAA,GAAiB1I,EAAMgJ,GAAG,AAAD,CAEjC,GAEA,IAAMC,EAAQvD,EAAK+C,EAAaQ,KAAK,CAAEP,GAMvC,OALAN,EAAKlI,OAAO,CAAG+I,GAAS,GAAMP,CAAAA,EAAgB,GAAKN,EAAKlI,OAAO,AAAD,EAC9DkI,EAAKtJ,QAAQ,CAAGA,EAChBsJ,EAAKM,aAAa,CAAGA,EACrBN,EAAKc,MAAM,CAAGd,EAAKlI,OAAO,EAAI,CAACwI,EAC/BN,EAAKY,GAAG,CAAGC,EACJb,CACX,EA4DIe,aA/CJ,SAAsBzH,CAAM,EACxB,IAAI0H,EAAQnH,EAaZ,OAZIuD,EAAS9D,KAETO,EAAUuD,EAAS9D,EAAOO,OAAO,EAAIP,EAAOO,OAAO,CAAG,CAAC,EAEvDmH,EAAS1D,EAAKhE,EAAO2H,QAAQ,CAAEpH,EAAQmH,MAAM,CAAE,IAE3C5D,EAAS9D,EAAO4H,WAAW,GAC3B5H,CAAAA,EAAO4H,WAAW,CAACF,MAAM,CAAGA,CAAK,EAGrC1H,EAAO2H,QAAQ,CAAGD,GAEfA,CACX,CAiCA,EAgBM,CAAE1D,KAAM6D,CAAkB,CAAElE,OAAQmE,CAAoB,CAAE,CAAI9M,IAE9D,CAAE0B,YAAa,CAAEqL,OAAQ,CAAEnN,UAAW,CAAEqE,WAAY+I,CAAW,CAAE,CAAE,CAAE,CAAE,CAAI1L,IA4D9C2L,EAlDnC,cAAwBD,EAMpBjL,YAAYiD,CAAM,CAAEO,CAAO,CAAEjF,CAAC,CAAEiD,CAAK,CAAE,CACnC,KAAK,CAACyB,EAAQO,EAASjF,GAMvB,IAAI,CAAC4M,eAAe,CAAG,CAAA,EACvB,IAAI,CAACC,YAAY,CAAG,OACpB,IAAI,CAAC3I,MAAM,CAAG,CAAA,EACd,IAAI,CAACY,IAAI,CAAG,CAAC,EACb,IAAI,CAAC+H,YAAY,CAAG,OACpB,IAAI,CAACD,eAAe,CAAG,CAAA,EACnB3J,IACA,IAAI,CAAC6J,QAAQ,CAAG7J,EAAM6B,IAAI,CAACxB,UAAU,CAACL,KAAK,CAC3C,IAAI,CAACC,OAAO,CAAGD,EAAMC,OAAO,CAC5B,IAAI,CAAC6J,MAAM,CAAG9J,EACd,IAAI,CAAC4I,EAAE,CAAG,IAAI,CAACkB,MAAM,CAAClB,EAAE,CAAG,IAAM,IAAI,CAACiB,QAAQ,CAACjB,EAAE,CAEzD,CAMApE,OAAOxC,CAAO,CAAEyC,CAAM,CAAEO,CAAS,CAAE+E,CAAQ,CAAE,CACzC,IAAMC,EAAa,CACfpB,GAAI,IAAI,CAACA,EAAE,CACXgB,aAAc,IAAI,CAACA,YAAY,AACnC,EACAnJ,IAAmDpE,SAAS,CAACmI,MAAM,CAACjI,IAAI,CAAC,IAAI,CAAEyF,EAAS,CAAA,IAAI,CAACf,MAAM,EAAWwD,EAC9GO,EAAW+E,GACX,IAAI,CAAC9J,OAAO,CAAG,IAAI,CAAC6J,MAAM,CAAC7J,OAAO,CAClCsJ,EAAqB,IAAI,CAAES,GACvBV,EAAmB7E,EAAQ,CAAA,IAC3B,IAAI,CAAChD,MAAM,CAACa,KAAK,CAACmC,MAAM,CAACO,EAEjC,CACJ,CA6BA,OAAMiF,EAmBF,OAAOC,gBAAgBrK,CAAM,CAAEE,CAAK,CAAEoK,CAAO,CAAE,CAE3C,IAAMC,EAAY,IAAI7L,EAmBtB,OAlBA6L,EAAUxB,EAAE,CAAG/I,EAAO+I,EAAE,CAAG,IAAMuB,EACjCC,EAAUzK,QAAQ,CAAGE,EAGrBuK,EAAUvL,QAAQ,CAAC5B,IAAI,CAAC8C,GACxBqK,EAAUvK,MAAM,CAAGA,EAAO+I,EAAE,CAC5BwB,EAAU/J,UAAU,CAAGR,EACvBuK,EAAUpK,KAAK,CAAGD,EAAMC,KAAK,CAC7BoK,EAAUtI,KAAK,CAAG/B,EAAM+B,KAAK,CAAGqI,EAChCC,EAAUhK,iBAAiB,CAAGL,EAAMK,iBAAiB,CACrDgK,EAAUnK,OAAO,CAAGF,EAAME,OAAO,CAEjCJ,EAAOhB,QAAQ,CAACkB,EAAMK,iBAAiB,CAAC,CAAGgK,EAC3CrK,EAAMsK,aAAa,CAAGxK,EACtBE,EAAMK,iBAAiB,CAAG,EAE1BL,EAAMM,UAAU,CAAG+J,EACnBrK,EAAMF,MAAM,CAAGuK,EAAUxB,EAAE,CACpBwB,CACX,CAWAE,mBAAmB7I,CAAM,CAAE,CAEvB,IAAM8I,EAAQ9I,EAAO+I,QAAQ,CAC7B,IAAI,CAACC,WAAW,CAACF,GACjB,IAAMG,EAAOjJ,EAAO0G,IAAI,CACpBuC,IACAC,AALe,IAAI,CAKRC,kBAAkB,CAACF,EAAM,GACpCC,AANe,IAAI,CAMRE,YAAY,CAACN,GACxBI,AAPe,IAAI,CAORG,SAAS,CAACJ,GACrBC,AARe,IAAI,CAQRI,UAAU,CAACL,EAAM,CAACA,EAAK5L,IAAI,EACtC6L,AATe,IAAI,CASRK,WAAW,CAACT,GAE/B,CAOAM,aAAaN,CAAK,CAAE,CAChB,IAAK,IAAM1I,KAAQ0I,EACf,IAAK,IAAIxK,KAAS8B,EAAKhD,QAAQ,CAE3B,GAAIkB,GAASA,EAAM+B,KAAK,CAAGD,EAAKC,KAAK,CAAG,EAAG,CAGvC,IAAIqI,EAAUpK,EAAM+B,KAAK,CAAGD,EAAKC,KAAK,CAAG,EAEzC,KAAOqI,EAAU,GACbpK,EAAQkK,EAAgBC,eAAe,CAACrI,EAAM9B,EAAOoK,GACrDA,GAER,CAGZ,CAKAM,YAAYF,CAAK,CAAE,CACf,IAAK,IAAM1I,KAAQ0I,EACf1I,EAAKnD,GAAG,CAAG,EACXmD,EAAKlC,QAAQ,CAAGkC,EAChBA,EAAKlD,KAAK,CAAG,EACbkD,EAAKzC,MAAM,CAAG,KAAK,EACnByC,EAAKjD,MAAM,CAAG,EACdiD,EAAK/C,IAAI,CAAG,CAEpB,CAUA8L,mBAAmB/I,CAAI,CAAEmE,CAAK,CAAE,CAC5B,IAAyBnH,EAAWgD,EAAKhD,QAAQ,CACjD,IAAK,IAAIhC,EAAI,EAAGoO,EAAOpM,EAAS/B,MAAM,CAAED,EAAIoO,EAAM,EAAEpO,EAChD8N,AAFe,IAAI,CAERC,kBAAkB,CAAC/L,CAAQ,CAAChC,EAAE,CAAEA,EAE/CgF,CAAAA,EAAKzB,iBAAiB,CAAG4F,CAC7B,CAQA8E,UAAUjJ,CAAI,CAAE,KAIRqJ,EAEJ,GAAKrJ,EAAK3B,WAAW,GAUhB,CAID,IAAIT,EAAkBoC,EAAK1C,gBAAgB,GAC3C,IAAK,IAAMY,KAAS8B,EAAKhD,QAAQ,CAC7B8L,AArBW,IAAI,CAqBJG,SAAS,CAAC/K,GACrBN,EAAkBkL,AAtBP,IAAI,CAsBcQ,SAAS,CAACpL,EAAON,GAElDkL,AAxBe,IAAI,CAwBRS,aAAa,CAACvJ,GACzB,IAAMwJ,EAAYxJ,EAAK1C,gBAAgB,GAAImM,EAAazJ,EAAKvC,iBAAiB,GAI9EiM,EAAW,AAACF,CAAAA,EAAUvM,IAAI,CAAGwM,EAAWxM,IAAI,AAAD,EAAK,EAChDoM,CAAAA,EAAcrJ,EAAK1B,cAAc,EAAC,GAE9B0B,EAAK/C,IAAI,CAAGoM,EAAYpM,IAAI,CA9BlB,EA+BV+C,EAAKnD,GAAG,CAAGmD,EAAK/C,IAAI,CAAGyM,GAGvB1J,EAAK/C,IAAI,CAAGyM,CAEpB,KAhCIL,CAAAA,EAAcrJ,EAAK1B,cAAc,EAAC,GAE9B0B,EAAK/C,IAAI,CAAGoM,EAAYpM,IAAI,CANlB,EAOV+C,EAAKnD,GAAG,CAAGmD,EAAK/C,IAAI,EAGpB+C,EAAK/C,IAAI,CAAG,CA2BxB,CAUAiM,WAAWlJ,CAAI,CAAE2J,CAAM,CAAE,CAOrB,IAAK,IAAMzL,KAFX8B,EAAK4J,SAAS,CAAG5J,EAAK/C,IAAI,CAAG0M,EAC7B3J,EAAK6J,SAAS,CAAG7J,EAAKC,KAAK,CACPD,EAAKhD,QAAQ,EAC7B8L,AAPe,IAAI,CAORI,UAAU,CAAChL,EAAOyL,EAAS3J,EAAKnD,GAAG,CAEtD,CAOA0M,cAAcvJ,CAAI,CAAE,CAChB,IAAIlD,EAAQ,EAAGC,EAAS,EACxB,IAAK,IAAI/B,EAAIgF,EAAKhD,QAAQ,CAAC/B,MAAM,CAAG,EAAGD,GAAK,EAAGA,IAAK,CAChD,IAAM8O,EAAY9J,EAAKhD,QAAQ,CAAChC,EAAE,AAClC8O,CAAAA,EAAU7M,IAAI,EAAIH,EAClBgN,EAAUjN,GAAG,EAAIC,EACjBC,GAAU+M,EAAU/M,MAAM,CAC1BD,GAASgN,EAAUhN,KAAK,CAAGC,CAC/B,CACJ,CAkBAuM,UAAUtJ,CAAI,CAAEpC,CAAe,CAAE,CAC7B,IAAyByL,EAAcrJ,EAAK1B,cAAc,GAC1D,GAAI+K,EAAa,CACb,IAAIU,EAAe/J,EAAMgK,EAAehK,EAAMrC,EAAc0L,EAAaY,EAAcF,EAAahM,kBAAkB,GAAImM,EAAcH,EAAalN,GAAG,CAAEsN,EAAcH,EAAanN,GAAG,CAAEuN,EAAazM,EAAYd,GAAG,CAAEwN,EAAaJ,EAAYpN,GAAG,CACpP,KAAOc,GACHA,EAAYH,SAAS,IACrBuM,GACAA,EAAa1M,QAAQ,IAAI,CACzBM,EAAcA,EAAYH,SAAS,GACnCyM,EAAcA,EAAY5M,QAAQ,GAClC0M,EAAeA,EAAa1M,QAAQ,GAEpC2M,AADAA,CAAAA,EAAeA,EAAaxM,SAAS,EAAC,EACzBM,QAAQ,CAAGkC,EACxB,IAA2BlD,EAAQa,EAAYV,IAAI,CAC/CmN,EACCL,CAAAA,EAAa9M,IAAI,CAAGiN,CAAU,EAFX,EAIpBpN,EAAQ,IACRgM,AAjBO,IAAI,CAiBAwB,WAAW,CAACtK,EAAKtC,WAAW,CAACC,EAAaC,GAAkBoC,EAAMlD,GAC7EoN,GAAepN,EACfqN,GAAerN,GAEnBsN,GAAczM,EAAYd,GAAG,CAC7BqN,GAAeH,EAAalN,GAAG,CAC/BwN,GAAcJ,EAAYpN,GAAG,CAC7BsN,GAAeH,EAAanN,GAAG,AACnC,CACIc,GACAA,EAAYH,SAAS,IACrB,CAACwM,EAAaxM,SAAS,KACvBwM,EAAazM,MAAM,CAAGI,EAAYH,SAAS,GAC3CwM,EAAanN,GAAG,EAAIuN,EAAaD,GAEjCJ,GACAA,EAAa1M,QAAQ,IACrB,CAAC4M,EAAY5M,QAAQ,KACrB4M,EAAY1M,MAAM,CAAGwM,EAAa1M,QAAQ,GAC1C4M,EAAYpN,GAAG,EAAIqN,EAAcG,GAErCzM,EAAkBoC,CACtB,CACA,OAAOpC,CACX,CASA0M,YAAYC,CAAQ,CAAEC,CAAS,CAAE1N,CAAK,CAAE,CACpC,IAAM2N,EAAWD,EAAUjM,iBAAiB,CAAGgM,EAAShM,iBAAiB,AACzEiM,CAAAA,EAAUzN,MAAM,EAAID,EAAQ2N,EAC5BD,EAAU1N,KAAK,EAAIA,EACnB0N,EAAUvN,IAAI,EAAIH,EAClB0N,EAAU3N,GAAG,EAAIC,EACjByN,EAASxN,MAAM,EAAID,EAAQ2N,CAC/B,CAOAtB,YAAYT,CAAK,CAAE,CACf,IAAK,IAAM1I,KAAQ0I,EACX1I,EAAKwI,aAAa,GAElBxI,EAAKzB,iBAAiB,CAAGyB,EAAKxB,UAAU,CAACD,iBAAiB,CAC1DyB,EAAKhC,MAAM,CAAGgC,EAAKwI,aAAa,CAACxK,MAAM,CACvCgC,EAAKxB,UAAU,CAAGwB,EAAKwI,aAAa,CAEpC,OAAOxI,EAAKwI,aAAa,CAACxL,QAAQ,CAACgD,EAAKzB,iBAAiB,CAAC,CAC1DyB,EAAKwI,aAAa,CAACxL,QAAQ,CAACgD,EAAKzB,iBAAiB,CAAC,CAAGyB,EACtDA,EAAKwI,aAAa,CAAG,KAAK,EAGtC,CACJ,CAySA,IAAMkC,EAAgEpR,EAAwD,OAAU,CAACqR,UAAU,CACnJ,IAAIC,EAAqErR,EAAoBC,CAAC,CAACkR,GAgB/F,GAAM,CAAEG,QAAAA,CAAO,CAAE,CAAIjQ,IACf,CAAEmE,SAAU+L,CAAiB,CAAE7L,MAAO8L,CAAc,CAAEC,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAE1H,OAAQ2H,CAAe,CAAE,CAAItQ,IAyB7G,SAASuQ,EAAYrQ,CAAI,CAAEsQ,CAAe,EAEtCA,EAAkBL,EAAe,CAAA,EAAM,CACnChK,QAAS,CAAA,EACTsK,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGJ,GACH,IAAMK,EAAM,IAAI,CAAC7J,QAAQ,CAAC6J,GAAG,CAAEC,EAAc,IAAI,CAACxK,IAAI,EAAI,IAAI,CAAEyK,EAAWD,EAAYC,QAAQ,CAAE,CAAEN,WAAAA,CAAU,CAAEtK,QAAAA,CAAO,CAAE,CAAGqK,EAM3H,GALAtQ,EAAOA,GAAS6Q,GAAYA,EAAS7Q,IAAI,CAErC6Q,GACAA,EAASC,IAAI,GAEb9Q,GAAQiG,EAAS,CACjB,IAAM6K,EAAOd,EAAkBY,EAAa,kBAAmB,AAACG,IAC5D,GAAI/Q,GAAQiG,EAAS,CAEjB,IAAI+K,EAAahR,EAAKmG,IAAI,CAAC,MACtB6K,GACDhR,EAAKmG,IAAI,CAAC,KAAM6K,EAAad,KAGjC,IAAMe,EAAc,CAGhB7Q,EAAG,EACHC,EAAG,CACP,EACI8P,EAAQI,EAAWW,EAAE,IACrBD,EAAYC,EAAE,CAAGX,EAAWW,EAAE,CAC9B,OAAOX,EAAWW,EAAE,EAEpBf,EAAQI,EAAWC,EAAE,IACrBS,EAAYT,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBI,EAAYzK,IAAI,CAAC8K,GAEjB,IAAI,CAAC9K,IAAI,CAAC,CAAEgL,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAC1K,OAAO,EAAC,EAGhC,IAAMxE,EAAW6O,EAAEnD,KAAK,CAACyD,KAAK,CAAC,EAC/BN,CAAAA,EAAEnD,KAAK,CAACzN,MAAM,CAAG,EACjB4Q,EAAEnD,KAAK,CAAC,EAAE,CAAG,CACT0D,QAAS,WACTf,WAAYH,EAAgBG,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCa,KAAM,CAAC,EAAEZ,EAAI,CAAC,EAAEK,EAAW,CAAC,AAChC,GACA9O,SAAAA,CACJ,CACJ,CACJ,EAEA0O,CAAAA,EAAYC,QAAQ,CAAG,CAAE7Q,KAAAA,EAAM8Q,KAAAA,CAAK,CACxC,MAEIF,EAAYzK,IAAI,CAAC,CAAE+K,GAAI,EAAGV,GAAI,CAAE,GAChC,OAAOI,EAAYC,QAAQ,CAO/B,OALI,IAAI,CAACW,KAAK,GAEVZ,EAAYa,SAAS,CAAG,GACxB,IAAI,CAAC3K,QAAQ,CAAC4K,SAAS,CAACd,IAErB,IAAI,AACf,CAWA,SAASe,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAACnK,OAAO,EAAEoK,cAAc,YAC1D,GAAID,EAAI,CACJ,IAAME,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAACpL,QAAQ,CAACqL,WAAW,CAAC,IAAI,CAACxK,OAAO,EAAGyK,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQT,EAC5BU,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMpS,MAAM,CAIrEyS,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAE1S,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGyS,EAAgBzM,EAAW,AAACyL,CAAAA,EAAGiB,iBAAiB,CAACF,GAAa,EAAC,EAAK9C,EAASiD,EAASjS,KAAKkS,GAAG,CAAC5M,GAAW6M,EAASnS,KAAKoS,GAAG,CAAC9M,GAC7I,MAAO,CACH,CACIjG,EAAIgS,EAAYY,EAChB3S,EAAI+R,EAAYc,EACnB,CACD,CACI9S,EAAI6R,EAAIe,EACR3S,EAAI4R,EAAIiB,EACX,CACJ,AACL,EACA,IAAK,IAAIhT,EAAI,EAAGkT,EAAY,EAAGA,EAAYT,EAAYS,IAAa,CAChE,IAA+BC,EAAUC,AAA5Bf,CAAK,CAACa,EAAU,CAAiBjT,MAAM,CACpD,IAAK,IAAIoT,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBtT,EAClBqT,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGd,EAAmBY,EAAc1B,EAAG6B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAvB,EAAQ1R,IAAI,CAACoT,GACb1B,EAAQ1R,IAAI,CAACmT,KAGK,IAAdL,GACApB,EAAQ4B,OAAO,CAACF,GAEhBN,IAAcT,EAAa,GAC3BX,EAAQ1R,IAAI,CAACmT,GAGzB,CACA,MAAO1C,EAAG,CAGN,KACJ,CAEJ7Q,GAAKmT,EAAU,EACf,GAAI,CACA,IAAMG,EAAetT,EAAIkT,EAAWS,EAAU/B,EAAGgC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGd,EAAmBY,EAAcK,GACvI7B,EAAQ4B,OAAO,CAACF,GAChB1B,EAAQ4B,OAAO,CAACH,EACpB,CACA,MAAO1C,EAAG,CAGN,KACJ,CACJ,CAEIiB,EAAQ7R,MAAM,EACd6R,EAAQ1R,IAAI,CAAC0R,CAAO,CAAC,EAAE,CAACX,KAAK,IAEjCQ,EAAKG,OAAO,CAAGA,CACnB,CACA,OAAOH,CACX,CAWA,SAASkC,EAAanC,CAAK,EACvB,IAAMoC,EAAepC,EAAMoC,YAAY,CAAE3Q,EAAQuO,EAAMvO,KAAK,CAAEiN,EAAmB0D,CAAY,CAAC3Q,EAAM4J,YAAY,CAAG,WAAW,EAC1H+G,EAAanD,QAAQ,CACrBP,GAAmB,CAAC0D,EAAaC,OAAO,GACxC,IAAI,CAAC5D,WAAW,CAAChN,EAAM6Q,gBAAgB,GAAG,IAAI,GAAK7Q,EAAMqB,OAAO,CAAE4L,GAC9DjN,EAAM8Q,aAAa,EACnB,CAAC7D,EAAgBrK,OAAO,EAExB5C,CAAAA,EAAM8Q,aAAa,CAAI9Q,EAAM8Q,aAAa,CAACzN,OAAO,EAAE,EAGhE,CA0BA,GAAM,CAAE0N,YAAaC,CAA2B,CAAE,CA5/C5B,CAClBtU,YAAAA,EACAqU,YA5HgB,CAChB,QAOJ,SAAwBE,CAAU,EAC9B,GAAM,CAAE7T,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAE2E,MAAAA,EAAQ,CAAC,CAAEe,SAAAA,EAAW,CAAA,CAAK,CAAEiO,OAAAA,CAAM,CAAEC,cAAAA,CAAa,CAAE,CAAGF,EACzEtU,EAAO,CACT,CAAC,IAAKS,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKH,EAAIG,EAAG,CACb,CAAC,IAAKH,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKH,EAAIG,EAAG,CAChB,CACD,OAAO4T,EACHzU,EAAY,CACR,CAAC,IAAKU,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAK8E,EAASe,CAAAA,EAAW,IAAO,EAAE,EAAI5F,EAAG,CAC/C,CAAC,IAAKD,EAAK8E,EAASe,CAAAA,EAAW,IAAO,EAAE,EAAI1F,EAAG,CAC/C,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAE2T,GACHvU,CACR,EAxBIyU,SA4BJ,SAAyBH,CAAU,EAC/B,GAAM,CAAE7T,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAE2E,MAAAA,EAAQ,CAAC,CAAEe,SAAAA,EAAW,CAAA,CAAK,CAAEkO,cAAAA,CAAa,CAAE,CAAGF,EACvE,OAAOE,EAAgB,CACnB,CAAC,IAAK/T,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAK8E,EAASe,CAAAA,EAAW,GAAK,CAAA,EAAI1F,EAAG,CAC3C,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAG,CACA,CAAC,IAAKH,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIG,EAAG,CACb,CAAC,IAAKH,EAAIG,EAAG,CAChB,AACL,EAtCI8T,OA0CJ,SAAuBJ,CAAU,EAC7B,GAAM,CAAE7T,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAE+T,OAAAA,EAAS,CAAC,CAAEpP,MAAAA,EAAQ,CAAC,CAAEe,SAAAA,EAAW,CAAA,CAAK,CAAEkO,cAAAA,CAAa,CAAE,CAAGF,EACnF,OAAOE,EACH,CACI,CAAC,IAAK/T,EAAIC,EAAG,CACb,CACI,IACAD,EAAKkU,EACLjU,EACAD,EAAKkU,EAASpP,EAASe,CAAAA,EAAW,GAAK,CAAA,EACvC1F,EACAH,EAAK8E,EAASe,CAAAA,EAAW,GAAK,CAAA,EAC9B1F,EACH,CACD,CAAC,IAAKD,EAAIC,EAAG,CAChB,CACD,CACI,CAAC,IAAKH,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKD,EAAIC,EAAG,CAChB,AACT,CA9DA,CAyHA,EA2/CM,CAAEkE,OAAQ,CAAEpF,UAAWkV,CAAW,CAAE,CAAEpT,YAAa,CAAEC,QAASoT,CAAa,CAAEhI,OAAQiI,CAAY,CAAE,CAAE,CAAI1T,IAEzG,CAAE1B,UAAW,CAAEqV,QAAAA,CAAO,CAAE,CAAE,CAAIxT,IAI9B,CAAE4I,gBAAiB6K,CAA+B,CAAEnK,aAAcoK,CAA4B,CAAE,CAAGjM,EAEnG,CAAEkM,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAE1M,OAAQ2M,CAAsB,CAAEjR,MAAOkR,EAAqB,CAAEvM,KAAMwM,EAAoB,CAAEvM,eAAgBwM,EAA8B,CAAEC,MAAAA,EAAK,CAAE,CAAI1V,IAM9K2V,AAjCiB,CAAA,CACbC,QATJ,SAAiBC,CAAe,EAC5B3F,EAAkB2F,EAAiB,eAAgBhE,GACnD3B,EAAkB2F,EAAiB,wBAAyB5B,GAC5D,IAAM6B,EAAkBD,EAAgBjW,SAAS,AAC5CkW,CAAAA,EAAgBvF,WAAW,EAC5BuF,CAAAA,EAAgBvF,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EA+BoBqF,OAAO,CAAE5F,IAe7B,OAAM+F,WAAwBhB,EAC1BhT,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAAC+L,QAAQ,CAAG,EAAE,CAClB,IAAI,CAACiI,KAAK,CAAG,EAAE,AACnB,CAMAC,MAAO,CACH,KAAK,CAACA,KAAKtR,KAAK,CAAC,IAAI,CAAE3C,WACvB,IAAI,CAACkU,eAAe,CAAG,IAvkBiC1I,EA0kBxD,IAAMxI,EAAS,IAAI,CAAEmR,EAAa,IAAI,CAACtQ,KAAK,CAACuQ,eAAe,CAcvDD,EAAWE,IAAI,CAAC,AAACC,GAAMA,AAAW,kBAAXA,EAAEpK,IAAI,GAC9BiK,EAAW3V,IAAI,CAf2D,WAC1E,IAAM+V,EAAa,EAAE,CAErB,GAAIvR,EAAOO,OAAO,CAACiR,UAAU,EACzB,CAACd,GAAM1Q,EAAOO,OAAO,CAACiR,UAAU,CAAC,CAAC,EAAE,CAACC,YAAY,CACjD,IAAK,IAAMC,KAAS1R,EAAOgR,KAAK,EAAI,EAAE,CAC9BU,EAAKC,SAAS,EACdJ,EAAW/V,IAAI,CAACkW,EAAKC,SAAS,EAI1C,OAAOJ,CACX,EAKJ,CAOAK,oBAAqB,CACjB,IAAM/Q,EAAQ,IAAI,CAACA,KAAK,CAAEb,EAAS,IAAI,CAAEmG,EAAYtF,EAAMsF,SAAS,CAAE0L,EAAYhR,EAAMgR,SAAS,CAAE7L,EAAcoK,EAAS,IAAI,CAACxL,MAAM,CAACkN,GAAG,CAAC,AAACC,GAAMA,EAAE3R,IAAI,CAAC6J,SAAS,GAC7J+H,EAAOC,IAAUC,EAAO,CAACD,IAAUE,EAAOF,IAAUG,EAAO,CAACH,IAAUI,EAAW,EAAGC,EAAW,EAAGC,EAAW,EAAGC,EAAW,EAC/H,IAAI,CAAC5N,MAAM,CAACwC,OAAO,CAAC,AAAC7I,QAab8D,EAVJ,GAAI,IAAI,CAAC9B,OAAO,CAACkS,SAAS,EAAI,CAAClU,EAAMC,OAAO,CACxC,OAEJ,IAAM4B,EAAO7B,EAAM6B,IAAI,CAAEC,EAAQL,EAAOG,iBAAiB,CAAC5B,EAAM6B,IAAI,CAACC,KAAK,CAAC,EAAI,CAAC,EAAGqS,EAAgBnC,GAAsB,IAAI,CAAChQ,OAAO,CAACoS,MAAM,CAAEtS,EAAMsS,MAAM,CAAEpU,EAAMgC,OAAO,CAACoS,MAAM,EAAGzM,EAAYwM,EAAcjS,KAAK,EAAI0P,EAA6B,IAAI,CAAEnK,GAAcyJ,EAASgB,GAA+BiC,EAAcjD,MAAM,EAAI,EAAGxT,KAAKC,GAAG,CAACiK,EAAW0L,IAAae,EAASF,EAAcE,MAAM,CAAEC,EAAY,AAACD,AAAW,WAAXA,GAAwBF,EAAchS,MAAM,CAE/b+P,GAA+BiC,EAAchS,MAAM,CAAEmR,GADrDpC,AAAS,EAATA,EACiEqD,EAAYF,AAAW,WAAXA,GAAwB1M,EAErGuK,GAA+BvK,EAAWC,GAD1CsJ,AAAS,EAATA,CAEJrP,CAAAA,EAAK0S,SAAS,CAAGA,EACjB1S,EAAKyS,SAAS,CAAGA,EAEbzS,EAAK6J,SAAS,EAAI+H,IAClBA,EAAO5R,EAAK6J,SAAS,CAErBqI,EAAWrW,KAAK8W,GAAG,CAACD,EADRJ,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACIiQ,IAE3ClS,EAAK6J,SAAS,EAAIiI,IAClBA,EAAO9R,EAAK6J,SAAS,CAErBoI,EAAWpW,KAAK8W,GAAG,CAACD,EADRJ,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACIgQ,IAE3CjS,EAAK4J,SAAS,EAAImI,IAClBA,EAAO/R,EAAK4J,SAAS,CAErBwI,EAAWvW,KAAK8W,GAAG,CAACF,EADRH,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACImQ,IAE3CpS,EAAK4J,SAAS,EAAIoI,IAClBA,EAAOhS,EAAK4J,SAAS,CAErBuI,EAAWtW,KAAK8W,GAAG,CAACF,EADRH,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACIkQ,GAEnD,GAGA,IAAMS,EAAKZ,IAASD,EAChB,EACA,AAACN,CAAAA,EAAY,AAACW,CAAAA,EAAWD,CAAO,EAAK,CAAA,EAAMH,CAAAA,EAAOD,CAAG,EAAIc,EAAKb,IAASD,EAAON,EAAY,EAAI,CAACmB,EAAKb,EAAOK,EAAW,EAAGU,EAAKhB,IAASF,EACvI,EACA,AAAC7L,CAAAA,EAAY,AAACkM,CAAAA,EAAWA,CAAO,EAAK,CAAA,EAAMH,CAAAA,EAAOF,CAAG,EAAImB,EAAKjB,IAASF,EAAO7L,EAAY,EAAI,CAAC+M,EAAKlB,EAAOM,EAAW,EAC1H,MAAO,CAAEY,GAAAA,EAAIC,GAAAA,EAAIH,GAAAA,EAAIC,GAAAA,CAAG,CAC5B,CACAG,UAAW,CACP,IAAMpT,EAAS,IAAI,CACbgR,EAAQ,EAAE,CAyBhB,OAxBA,IAAI,CAACqC,IAAI,CAACjM,OAAO,CAAC,AAAC7I,IACf,IAAM2B,EAAeF,EAAOG,iBAAiB,CAAC5B,EAAM6B,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EACzE,GAAI9B,EAAM6B,IAAI,CAAChC,MAAM,CAAE,CACnB,IAAMkV,EAAe/C,GAAsBrQ,EAAc3B,EAAMgC,OAAO,EACtE,GAAI,CAAChC,EAAM0E,YAAY,EAAI1E,EAAM0E,YAAY,CAACsQ,SAAS,CAAE,CACrD,IAAM7B,EAAO,IAAI1R,EAAOwT,SAAS,CAACxT,EAAQsT,EAAc,KAAK,EAAG/U,EAChEA,CAAAA,EAAM0E,YAAY,CAAGyO,CACzB,MAGInT,EAAMf,SAAS,CAAGgT,GAAqBjS,EAAMf,SAAS,CAAE,AAAC,CAAA,IAAI,CAAC2C,iBAAiB,CAAC5B,EAAM6B,IAAI,CAACC,KAAK,CAAC,EAAI,CAAC,CAAA,EAAG7C,SAAS,EAClHe,EAAM0E,YAAY,CAACzE,OAAO,CACtBD,EAAM0E,YAAY,CAACoF,MAAM,CAAC7J,OAAO,AAEzCD,CAAAA,EAAM0E,YAAY,CAACsB,KAAK,CAAGyM,EAAMxV,IAAI,CAAC+C,EAAM0E,YAAY,EAAI,CAChE,MAEQ1E,EAAM0E,YAAY,GAClBjD,EAAOgR,KAAK,CAACyC,MAAM,CAAClV,EAAM0E,YAAY,CAACsB,KAAK,EAC5ChG,EAAM0E,YAAY,CAACrB,OAAO,GAC1B,OAAOrD,EAAM0E,YAAY,CAGrC,GACO+N,CACX,CACA0C,UAAUvM,CAAE,CAAE5C,CAAK,CAAElE,CAAK,CAAEsT,CAAI,CAAEvV,CAAM,CAAE,CACtC,IAAMG,EAAQ,IAAI,CAACqG,MAAM,CAACL,EAAM,CAEhC,OADAlE,EAAQ,AAAC9B,GAASA,EAAM8B,KAAK,EAAKA,EAC3B,KAAK,CAACqT,UAAU5Y,IAAI,CAAC,IAAI,CAAEqM,EAAI5C,EAAOlE,EAAOsT,EAAMvV,EAC9D,CACAwV,eAAgB,CAGZ,MAAO,CAAC,CACZ,CACAC,mBAAmBzT,CAAI,CAAEN,CAAU,CAAE,CACjC,IAAMvB,EAAQ6B,EAAK7B,KAAK,CACpBA,IAEAA,EAAMf,SAAS,CAAGgT,GAAqBjS,EAAMf,SAAS,CAAE,AAAC,CAAA,IAAI,CAAC2C,iBAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,CAAA,EAAG7C,SAAS,EAC5Ge,EAAMC,OAAO,CAAGsB,EAChBA,EAAaA,AAAe,CAAA,IAAfA,GAA+B,CAACvB,EAAMf,SAAS,EAEhE4C,EAAKhD,QAAQ,CAACgK,OAAO,CAAC,AAAC8C,IACnB,IAAI,CAAC2J,kBAAkB,CAAC3J,EAAWpK,EACvC,EACJ,CACAgU,aAAc,CACV9D,EAAapV,SAAS,CAACkZ,WAAW,CAACnU,KAAK,CAAC,IAAI,CAAE3C,WAC/CgT,EAAapV,SAAS,CAACkZ,WAAW,CAAChZ,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkW,KAAK,CAC5D,CAKA+C,WAAY,CACR,IAAqBxT,EAAUP,AAAhB,IAAI,CAAmBO,OAAO,CAEzCmH,EAASxD,EAAqBuD,YAAY,CAF/B,IAAI,EAEqCE,EAExDmI,EAAYiE,SAAS,CAACjZ,IAAI,CAJX,IAAI,EAKnB,IAAM4L,EAAO1G,AALE,IAAI,CAKC0G,IAAI,CAAG1G,AALZ,IAAI,CAKegU,OAAO,GACzCrM,EAAW3H,AANI,IAAI,CAMDiU,OAAO,CAACvM,EAAO,CAClB,KAAXA,GAAkB,AAACC,GAAaA,EAASvK,QAAQ,CAAC/B,MAAM,GACxD2E,AARW,IAAI,CAQRkU,WAAW,CAAC,GAAI,CAAA,GACvBxM,EAAS1H,AATE,IAAI,CASC2H,QAAQ,CACxBA,EAAW3H,AAVA,IAAI,CAUGiU,OAAO,CAACvM,EAAO,EAErC1H,AAZe,IAAI,CAYZG,iBAAiB,CAAG+P,EAAgC,CACvDzK,KAAMkC,EAAStH,KAAK,CAAG,EACvBqF,OAAQnF,EAAQmF,MAAM,CACtBN,GAAIsB,EAAKhG,MAAM,CACf6E,SAAU,CACNO,gBAAiB9F,AAjBV,IAAI,CAiBaO,OAAO,CAACuF,eAAe,CAC/C1B,aAAc7D,EAAQ6D,YAAY,AACtC,CACJ,GACA,IAAI,CAACyP,kBAAkB,CAACnN,EAAM,CAAA,GAC9B1G,AAtBe,IAAI,CAsBZgR,KAAK,CAAGhR,AAtBA,IAAI,CAsBGoT,QAAQ,GAC9BpT,AAvBe,IAAI,CAuBZyG,aAAa,CAACC,GACrB,IAAI,CAACwK,eAAe,CAACrI,kBAAkB,CAxBxB,IAAI,EAyBnB7I,AAzBe,IAAI,CAyBZmU,cAAc,CAAG,IAAI,CAACvC,kBAAkB,GAC/C,IAAI,CAAChN,MAAM,CAACwC,OAAO,CAAC,AAAC7I,IACjB,IAAI,CAAC6V,aAAa,CAAC7V,EACvB,GACA,IAAI,CAACqG,MAAM,CAACwC,OAAO,CAAC,AAAC7I,IACbA,EAAM0E,YAAY,EAClB,IAAI,CAACoR,aAAa,CAAC9V,EAAM0E,YAAY,CAE7C,GACK1C,EAAQ6D,YAAY,EACrBpE,AAnCW,IAAI,CAmCRsU,iBAAiB,CAACtU,AAnCd,IAAI,CAmCiB0G,IAAI,CAE5C,CACA2N,cAAc3C,CAAI,CAAE,CAChB,IAAMtJ,EAAWsJ,EAAKtJ,QAAQ,CAAEC,EAASqJ,EAAKrJ,MAAM,CAAEkM,EAAY,IAAI,CAAChU,OAAO,CAACmR,IAAI,EAAErP,WAAa,EAAGmS,EAAShE,GAAqB,IAAI,CAACjQ,OAAO,CAACmR,IAAI,EAAE+C,YAAa,IAAMC,EAAOlE,GAAqBkB,EAAKnR,OAAO,CAACmR,IAAI,EAAEgD,KAAM,IAAI,CAACnU,OAAO,CAACmR,IAAI,EAAEgD,KAAM,WACvP,GAAItM,EAASnH,SAAS,EAAIoH,EAAOpH,SAAS,CAAE,CACxC,IAAM0T,EAAiBvM,EAASnH,SAAS,CAACR,KAAK,EAAI,EAAIe,EAAW,IAAI,CAACX,KAAK,CAACW,QAAQ,CAAE5F,EAAKyU,EAAM,AAACjI,CAAAA,EAASnH,SAAS,CAAC1F,CAAC,EAAI,CAAA,EACvH,AAAC6M,CAAAA,EAASnH,SAAS,CAACP,MAAM,EAAI,CAAA,EAAK,EAAG6T,GAAYzY,EAAKuU,EAAM,AAAChI,CAAAA,EAAOpH,SAAS,CAAC1F,CAAC,EAAI,CAAA,EACpF,AAAC8M,CAAAA,EAAOpH,SAAS,CAACP,MAAM,EAAI,CAAA,EAAK,EAAG6T,GACpC5Y,EAAK0U,EAAM,AAACjI,CAAAA,EAASnH,SAAS,CAAC3F,CAAC,EAAI,CAAA,EAAKqZ,EAAeJ,GAAY1Y,EAAKwU,EAAMhI,EAAOpH,SAAS,CAAC3F,CAAC,EAAI,EAAGiZ,GACxG/S,IACA7F,GAAMgZ,EACN9Y,GAAOwM,EAAOpH,SAAS,CAACR,KAAK,EAAI,GAErC,IAAMmU,EAAOvM,EAAOjI,IAAI,CAAC6J,SAAS,CAAG7B,EAAShI,IAAI,CAAC6J,SAAS,AAC5DyH,CAAAA,EAAKmD,SAAS,CAAG,OACjB,IAAqDpU,EAAQ,AAA3CxE,CAAAA,KAAKE,GAAG,CAACN,EAAKF,GAAMgZ,CAAY,EAAwBC,EAAQD,CAElFjD,CAAAA,EAAKoD,KAAK,CADMzE,EAAM,AAACxU,CAAAA,EAAKF,CAAC,EAAK,EAAG4Y,GAErC7C,EAAKqD,KAAK,CAAGjZ,EACb4V,EAAKzQ,SAAS,CAAG,CACbjH,EAAGuV,CAA2B,CAACmF,EAAK,CAAC,CACjC/Y,GAAAA,EACAC,GAAAA,EACAC,GAAAA,EACAC,GAAAA,EACA2E,MAAAA,EACAoP,OAXkGpP,EAAQ+T,EAAUhT,CAAAA,EAAW,GAAK,CAAA,EAYpIA,SAAAA,EACAkO,cAAerH,EAAO7J,OAAO,CAC7BiR,OAAQ,IAAI,CAAClP,OAAO,CAACmR,IAAI,EAAEjC,MAC/B,EACJ,EACAiC,EAAKsD,KAAK,CAAG,CACT1Z,EAAG,AAACK,CAAAA,EAAKE,CAAC,EAAK,EACfN,EAAG,AAACK,CAAAA,EAAKE,CAAC,EAAK,EACf4E,OAAQ6T,EACR9T,MAAO,CACX,EACAiR,EAAKuD,UAAU,CAAGzT,EAAW,CACzB,AAAC,CAAA,IAAI,CAACX,KAAK,CAACgR,SAAS,EAAI,CAAA,EAAKH,EAAKsD,KAAK,CAACzZ,CAAC,CAC1C,AAAC,CAAA,IAAI,CAACsF,KAAK,CAACsF,SAAS,EAAI,CAAA,EAAKuL,EAAKsD,KAAK,CAAC1Z,CAAC,CAC7C,CAAG,CACAoW,EAAKsD,KAAK,CAAC1Z,CAAC,CACZoW,EAAKsD,KAAK,CAACzZ,CAAC,CACf,AACL,CACJ,CAKA2Z,eAAetQ,CAAM,CAAE,CACnB,IACIrE,EAASF,EADQF,EAAoBH,AAA1B,IAAI,CAA6BG,iBAAiB,CAEjE,IAAK,IAAM5B,KAASqG,EAAQ,CAUxB,GATAvE,EAAQF,CAAiB,CAAC5B,EAAM6B,IAAI,CAACC,KAAK,CAAC,CAE3CE,EAAU,CAAEK,MAAO,CAAC,CAAE,EAElBP,GAASA,EAAMmR,UAAU,GACzBjR,EAAUgQ,GAAsBhQ,EAASF,EAAMmR,UAAU,EACzDxR,AATO,IAAI,CASJmV,aAAa,CAAG,IAAM,CAAA,GAG7B5W,EAAM0C,SAAS,EACfjB,AAbO,IAAI,CAaJO,OAAO,CAACiR,UAAU,CAAE,CAC3B,IAAM9O,EAAM,CAAC,EACT,CAAEjC,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAGnC,EAAM0C,SAAS,AAC3CjB,CAhBG,IAAI,CAgBAa,KAAK,CAACW,QAAQ,EACrB,CAAA,CAACf,EAAOC,EAAO,CAAG,CAACA,EAAQD,EAAM,AAAD,EAE/BiQ,GAAM1Q,AAnBJ,IAAI,CAmBOO,OAAO,CAACiR,UAAU,CAAC,CAAC,EAAE,CAAC5Q,KAAK,EAAEH,OAC5CiC,CAAAA,EAAIjC,KAAK,CAAG,CAAC,EAAEA,EAAM,EAAE,CAAC,AAAD,EAEtBiQ,GAAM1Q,AAtBJ,IAAI,CAsBOO,OAAO,CAACiR,UAAU,CAAC,CAAC,EAAE,CAAC5Q,KAAK,EAAEwU,WAC5C1S,CAAAA,EAAI0S,SAAS,CAAGnZ,KAAKoZ,KAAK,CAAC3U,EAAS,GAAE,EAE1C4P,EAAuB/P,EAAQK,KAAK,CAAE8B,GACtCnE,EAAMoT,SAAS,EAAEjP,IAAIA,EACzB,CAEAnE,EAAM+W,SAAS,CAAG/E,GAAsBhQ,EAAShC,EAAMgC,OAAO,CAACiR,UAAU,CAC7E,CACA1B,EAAYyF,cAAc,CAACza,IAAI,CAAC,IAAI,CAAE8J,EAC1C,CAMA4Q,eAAejX,CAAK,CAAEoT,CAAS,CAAE,CAC7B,IAAMnT,EAAUD,EAAMC,OAAO,AAE7BD,CAAAA,EAAMC,OAAO,CAAG,CAAA,EAChB,KAAK,CAACgX,eAAe7V,KAAK,CAAC,IAAI,CAAE3C,WAEjC2U,EAAU9R,OAAO,CAAC,CACd8B,QAASnD,CAAAA,CAAAA,AAAY,CAAA,IAAZA,CAAgB,CAC7B,EAAG,KAAK,EAAG,WAEPA,GAAWmT,EAAU8D,IAAI,EAC7B,GAEAlX,EAAMC,OAAO,CAAGA,CACpB,CAKA+W,gBAAiB,CACT,IAAI,CAAChV,OAAO,CAACiR,UAAU,GACvB,IAAI,CAACjR,OAAO,CAACiR,UAAU,CAAGd,GAAM,IAAI,CAACnQ,OAAO,CAACiR,UAAU,EAEvD,IAAI,CAAC0D,cAAc,CAAC,IAAI,CAACtQ,MAAM,EAE/BkL,EAAYyF,cAAc,CAACza,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkW,KAAK,EAExD,CACApP,SAAU,CAEN,GAAI,IAAI,CAACoP,KAAK,CAAE,CACZ,IAAK,IAAMU,KAAQ,IAAI,CAACV,KAAK,CACzBU,EAAK9P,OAAO,EAEhB,CAAA,IAAI,CAACoP,KAAK,CAAC3V,MAAM,CAAG,CACxB,CACA,OAAOyU,EAAYlO,OAAO,CAACjC,KAAK,CAAC,IAAI,CAAE3C,UAC3C,CAKA0Y,aAAanX,CAAK,CAAEyC,CAAK,CAAE,CACvB,IAAqBd,EAAe3B,GAChCyB,AADW,IAAI,CACRG,iBAAiB,CAAC5B,EAAM6B,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EAAGE,EAAUhC,GAASA,EAAMgC,OAAO,CAAEoV,EAAe,AAACzV,EAAamD,MAAM,EAC5HnD,EAAamD,MAAM,CAACrC,EAAM,EAC1B,CAAC,EACDzC,GACAA,CAAAA,EAAMgC,OAAO,CAACoS,MAAM,CAAGpC,GAAsBvQ,AALlC,IAAI,CAKqCO,OAAO,CAACoS,MAAM,CAAEzS,EAAayS,MAAM,CAAEpU,EAAMgC,OAAO,CAACoS,MAAM,CAAA,EAEjH,IAAMiD,EAAYpF,GAAqBmF,GAAgBA,EAAajE,IAAI,EAAIiE,EAAajE,IAAI,CAAC3P,KAAK,CAAExB,GAAWA,EAAQmR,IAAI,EAAInR,EAAQmR,IAAI,CAAC3P,KAAK,CAAE7B,GAAgBA,EAAawR,IAAI,EAAIxR,EAAawR,IAAI,CAAC3P,KAAK,CAAE/B,AAPnM,IAAI,CAOsMO,OAAO,CAACmR,IAAI,EAAI1R,AAP1N,IAAI,CAO6NO,OAAO,CAACmR,IAAI,CAAC3P,KAAK,EAAG8T,EAAgBrF,GAAqBmF,GAAgBA,EAAajE,IAAI,EACvUiE,EAAajE,IAAI,CAACrP,SAAS,CAAE9B,GAAWA,EAAQmR,IAAI,EAAInR,EAAQmR,IAAI,CAACrP,SAAS,CAAEnC,GAAgBA,EAAawR,IAAI,EACjHxR,EAAawR,IAAI,CAACrP,SAAS,CAAErC,AATlB,IAAI,CASqBO,OAAO,CAACmR,IAAI,EAAI1R,AATzC,IAAI,CAS4CO,OAAO,CAACmR,IAAI,CAACrP,SAAS,EAAGyT,EAAUhG,EAAY4F,YAAY,CAAC5a,IAAI,CAThH,IAAI,CASqHyD,EAAOyC,GAW/I,OAVIzC,IACIA,EAAMiB,MAAM,GACZsW,EAAQ3T,MAAM,CAAGyT,EACjBE,CAAO,CAAC,eAAe,CAAGD,EAC1B,OAAOC,EAAQjU,IAAI,EAElBtD,EAAMC,OAAO,EACdsX,CAAAA,EAAQnU,OAAO,CAAG,CAAA,GAGnBmU,CACX,CACAC,YAAa,CACThG,EAAcnV,SAAS,CAACmb,UAAU,CAACpW,KAAK,CAAC,IAAI,CAAE3C,WAC/CgT,EAAapV,SAAS,CAACmb,UAAU,CAACjb,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkW,KAAK,CAC3D,CAKAoD,cAAc7V,CAAK,CAAE,CACjB,IAAMsC,EAAQ,IAAI,CAACA,KAAK,CAAET,EAAO7B,EAAM6B,IAAI,CAAEyR,EAAYhR,EAAMgR,SAAS,CAAE1L,EAAYtF,EAAMsF,SAAS,CAErG,CAAE+M,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAEH,GAAAA,CAAE,CAAEC,GAAAA,CAAE,CAAE,CAAG,IAAI,CAACkB,cAAc,CAAE7Y,EAAI4X,EAAK9S,EAAK6J,SAAS,CAAGkJ,EAAI5X,EAAIyX,EAAK5S,EAAK4J,SAAS,CAAGiJ,EAAI5S,EAAQ,IAAI,CAACF,iBAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,EAAmGuS,EAASF,AAAzFnC,GAAsB,IAAI,CAAChQ,OAAO,CAACoS,MAAM,CAAEtS,EAAMsS,MAAM,CAAEpU,EAAMgC,OAAO,CAACoS,MAAM,EAA0BC,MAAM,CAAElS,EAASN,EAAKyS,SAAS,CAAEpS,EAAQL,EAAK0S,SAAS,CAAEkD,EAAW,IAAI,CAACzV,OAAO,CAACyV,QAAQ,CAAEC,EAAQ7V,EAAK9E,CAAC,CAAIuF,EAAMW,QAAQ,CACtY2E,EAAY1F,EAAQ,EAAInF,EACxBA,EAAImF,EAAQ,EAAIyV,EAAQ9V,EAAK7E,CAAC,CAAI,AAACya,EAEnCza,EAAImF,EAAS,EADbmR,EAAYtW,EAAImF,EAAS,EACRyV,EAAe3F,GAAqBjS,EAAMgC,OAAO,CAAC4V,YAAY,CAAE9V,EAAM8V,YAAY,CAAE,IAAI,CAAC5V,OAAO,CAAC4V,YAAY,EAAGC,EAAWnG,CAAO,CAAC2C,GAAU,SAAS,CAS3K,GARIwD,AAAa,KAAK,IAAlBA,GACA7X,EAAM8X,QAAQ,CAAG,CAAA,EACjB9X,EAAMsW,SAAS,CAAG,QAClBtW,EAAM+X,QAAQ,CAAG1D,EAAO2D,KAAK,CAAC,iBAAiB,CAAC,EAAE,EAGlDhY,EAAMsW,SAAS,CAAG,OAElB,CAACtW,EAAMC,OAAO,EAAID,EAAM0E,YAAY,CAAE,CACtC,IAAMrE,EAAaL,EAAM0E,YAAY,CAACmF,QAAQ,CAC9C,GAAIxJ,EAAY,CACZ,GAAoD,CAAEtD,EAAAA,EAAI,CAAC,CAAEC,EAAAA,EAAI,CAAC,CAAEkF,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAnE9B,EAAWqC,SAAS,EAAI,CAAC,CAC5C1C,CAAAA,EAAM0C,SAAS,EAChB1C,CAAAA,EAAM0C,SAAS,CAAG,CAAC,CAAA,EAElB1C,EAAM8X,QAAQ,EACf/F,EAAuB/R,EAAM0C,SAAS,CAAE,CACpCjH,EAAGoc,EAAS9a,EAAGC,EAAGkF,EAAOC,EAAQyV,EAAe,CAAEhb,EAAGgb,CAAa,EAAI,KAAK,EAC/E,GAEJ7F,EAAuB/R,EAAM0C,SAAS,CAAE,CAAE3F,EAAAA,EAAGC,EAAAA,CAAE,GAC/CgD,EAAMuW,KAAK,CAAGlW,EAAWkW,KAAK,CAC9BvW,EAAMwW,KAAK,CAAGnW,EAAWmW,KAAK,AAClC,CACJ,MAEIxW,EAAMuW,KAAK,CAAGmB,EACd1X,EAAMwW,KAAK,CAAGmB,EACd3X,EAAM0C,SAAS,CAAG,CACd3F,EAAG2a,EACH1a,EAAG2a,EACHzV,MAAAA,EACAC,OAAAA,EACA8V,OAAQ,AAACjY,EAAM6B,IAAI,CAACoH,MAAM,CAAe,UAAZ,SACjC,EACKjJ,EAAM8X,QAAQ,EACf9X,CAAAA,EAAM0C,SAAS,CAACjH,CAAC,CAAGoc,EAASH,EAAOC,EAAOzV,EAAOC,EAAQyV,EAAe,CAAEhb,EAAGgb,CAAa,EAAI,KAAK,EAAC,CAI7G5X,CAAAA,EAAM0W,UAAU,CAAGpU,EAAMW,QAAQ,CAC7B,CAACqQ,EAAYqE,EAAQxV,EAAS,EAAGyF,EAAY8P,EAAQxV,EAAQ,EAAE,CAC/D,CAACwV,EAAQxV,EAAQ,EAAGyV,EAAM,AAClC,CACJ,CACAnF,GAAgB0F,cAAc,CAAGlG,GAAsBR,EAAc0G,cAAc,CA55BnD,CAa5BT,SAAU,CAAA,EAKVrD,OAAQ,CACJlD,OAAQ,GACRpN,UAAW,EACXuQ,OAAQ,SACR8D,YAAa,EACbrT,OAAQ,CAAC,CACb,EACAqO,KAAM,CAkBF3P,MAAO,UAOPM,UAAW,EAOXoN,OAAQ,GACR+G,OAAQ,UAWR9B,KAAM,QACV,EAKAlU,eAAgB,CAMZO,YAAa,CAAA,EAIbI,QAAS,CAAA,EAITkB,UAAW,EAIX/G,EAAG,EAIHC,EAAG,EAIHmF,OAAQ,GAIRD,MAAO,GAIPE,MAAO,SAOPC,MAAO,CACH4V,OAAQ,UACRG,WAAY,OACZC,SAAU,KACd,CACJ,EAUAnE,UAAW,CAAA,EAKXoE,QAAS,CAeLC,WAAY,0CACZC,YAAa,YAUjB,EAUAvF,WAAY,CACRwF,MAAO,CAAA,EAePC,aAAc,CACVxL,WAAY,CACRE,YAAa,KACjB,CACJ,EACAxK,QAAS,CAAA,EACT+V,cAAe,IAAM,GACrBhV,QAAS,EACTtB,MAAO,CACHuW,aAAc,MAClB,CACJ,EAoBAlR,aAAc,GAiBdC,UAAW,KAAK,CACpB,GAgrBAoK,EAAuBS,GAAgBnW,SAAS,CAAE,CAC9CqE,WA1jD2DK,EA2jD3D1C,UAAWE,EACX0W,UAAWvL,CACf,GACA3L,IAA4D8a,kBAAkB,CAAC,YAAarG,IAmH/D,IAAMsG,GAAkBrc,WAE5Cqc,MAAiBC,OAAO"}