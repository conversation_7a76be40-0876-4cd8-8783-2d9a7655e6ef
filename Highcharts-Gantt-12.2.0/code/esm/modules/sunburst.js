import*as t from"../highcharts.js";var e,i,s,o,r={};r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let l=t.default;var a=r.n(l);let n={lang:{mainBreadcrumb:"Main"},options:{buttonTheme:{fill:"none",height:18,padding:2,"stroke-width":0,zIndex:7,states:{select:{fill:"none"}},style:{color:"#334eff"}},buttonSpacing:5,floating:!1,format:void 0,relativeTo:"plotBox",rtl:!1,position:{align:"left",verticalAlign:"top",x:0,y:void 0},separator:{text:"/",style:{color:"#666666",fontSize:"0.8em"}},showFullPath:!0,style:{},useHTML:!1,zIndex:7}},h=t.default.Templating,{format:d}=r.n(h)(),{composed:p}=a(),{addEvent:u,defined:c,extend:g,fireEvent:v,isString:f,merge:b,objectEach:m,pick:x,pushUnique:y}=a();function L(){if(this.breadcrumbs){let t=this.resetZoomButton&&this.resetZoomButton.getBBox(),e=this.breadcrumbs.options;t&&"right"===e.position.align&&"plotBox"===e.relativeTo&&this.breadcrumbs.alignBreadcrumbsGroup(-t.width-e.buttonSpacing)}}function P(){this.breadcrumbs&&(this.breadcrumbs.destroy(),this.breadcrumbs=void 0)}function T(){let t=this.breadcrumbs;if(t&&!t.options.floating&&t.level){let e=t.options,i=e.buttonTheme,s=(i.height||0)+2*(i.padding||0)+e.buttonSpacing,o=e.position.verticalAlign;"bottom"===o?(this.marginBottom=(this.marginBottom||0)+s,t.yOffset=s):"middle"!==o?(this.plotTop+=s,t.yOffset=-s):t.yOffset=void 0}}function w(){this.breadcrumbs&&this.breadcrumbs.redraw()}function A(t){!0===t.resetSelection&&this.breadcrumbs&&this.breadcrumbs.alignBreadcrumbsGroup()}class R{static compose(t,e){y(p,"Breadcrumbs")&&(u(t,"destroy",P),u(t,"afterShowResetZoom",L),u(t,"getMargins",T),u(t,"redraw",w),u(t,"selection",A),g(e.lang,n.lang))}constructor(t,e){this.elementList={},this.isDirty=!0,this.level=0,this.list=[];let i=b(t.options.drilldown&&t.options.drilldown.drillUpButton,R.defaultOptions,t.options.navigation&&t.options.navigation.breadcrumbs,e);this.chart=t,this.options=i||{}}updateProperties(t){this.setList(t),this.setLevel(),this.isDirty=!0}setList(t){this.list=t}setLevel(){this.level=this.list.length&&this.list.length-1}getLevel(){return this.level}getButtonText(t){let e=this.chart,i=this.options,s=e.options.lang,o=x(i.format,i.showFullPath?"{level.name}":"← {level.name}"),r=s&&x(s.drillUpText,s.mainBreadcrumb),l=i.formatter&&i.formatter(t)||d(o,{level:t.levelOptions},e)||"";return(f(l)&&!l.length||"← "===l)&&c(r)&&(l=i.showFullPath?r:"← "+r),l}redraw(){this.isDirty&&this.render(),this.group&&this.group.align(),this.isDirty=!1}render(){let t=this.chart,e=this.options;!this.group&&e&&(this.group=t.renderer.g("breadcrumbs-group").addClass("highcharts-no-tooltip highcharts-breadcrumbs").attr({zIndex:e.zIndex}).add()),e.showFullPath?this.renderFullPathButtons():this.renderSingleButton(),this.alignBreadcrumbsGroup()}renderFullPathButtons(){this.destroySingleButton(),this.resetElementListState(),this.updateListElements(),this.destroyListElements()}renderSingleButton(){let t=this.chart,e=this.list,i=this.options.buttonSpacing;this.destroyListElements();let s=this.group?this.group.getBBox().width:i,o=e[e.length-2];!t.drillUpButton&&this.level>0?t.drillUpButton=this.renderButton(o,s,i):t.drillUpButton&&(this.level>0?this.updateSingleButton():this.destroySingleButton())}alignBreadcrumbsGroup(t){if(this.group){let e=this.options,i=e.buttonTheme,s=e.position,o="chart"===e.relativeTo||"spacingBox"===e.relativeTo?void 0:"plotBox",r=this.group.getBBox(),l=2*(i.padding||0)+e.buttonSpacing;s.width=r.width+l,s.height=r.height+l;let a=b(s);t&&(a.x+=t),this.options.rtl&&(a.x+=s.width),a.y=x(a.y,this.yOffset,0),this.group.align(a,!0,o)}}renderButton(t,e,i){let s=this,o=this.chart,r=s.options,l=b(r.buttonTheme),a=o.renderer.button(s.getButtonText(t),e,i,function(e){let i,o=r.events&&r.events.click;o&&(i=o.call(s,e,t)),!1!==i&&(r.showFullPath?e.newLevel=t.level:e.newLevel=s.level-1,v(s,"up",e))},l).addClass("highcharts-breadcrumbs-button").add(s.group);return o.styledMode||a.attr(r.style),a}renderSeparator(t,e){let i=this.chart,s=this.options.separator,o=i.renderer.label(s.text,t,e,void 0,void 0,void 0,!1).addClass("highcharts-breadcrumbs-separator").add(this.group);return i.styledMode||o.css(s.style),o}update(t){b(!0,this.options,t),this.destroy(),this.isDirty=!0}updateSingleButton(){let t=this.chart,e=this.list[this.level-1];t.drillUpButton&&t.drillUpButton.attr({text:this.getButtonText(e)})}destroy(){this.destroySingleButton(),this.destroyListElements(!0),this.group&&this.group.destroy(),this.group=void 0}destroyListElements(t){let e=this.elementList;m(e,(i,s)=>{(t||!e[s].updated)&&((i=e[s]).button&&i.button.destroy(),i.separator&&i.separator.destroy(),delete i.button,delete i.separator,delete e[s])}),t&&(this.elementList={})}destroySingleButton(){this.chart.drillUpButton&&(this.chart.drillUpButton.destroy(),this.chart.drillUpButton=void 0)}resetElementListState(){m(this.elementList,t=>{t.updated=!1})}updateListElements(){let t=this.elementList,e=this.options.buttonSpacing,i=this.list,s=this.options.rtl,o=s?-1:1,r=function(t,e){return o*t.getBBox().width+o*e},l=function(t,e,i){t.translate(e-t.getBBox().width,i)},a=this.group?r(this.group,e):e,n,h;for(let d=0,p=i.length;d<p;++d){let u,c,g=d===p-1;t[(h=i[d]).level]?(u=(n=t[h.level]).button,n.separator||g?n.separator&&g&&(n.separator.destroy(),delete n.separator):(a+=o*e,n.separator=this.renderSeparator(a,e),s&&l(n.separator,a,e),a+=r(n.separator,e)),t[h.level].updated=!0):(u=this.renderButton(h,a,e),s&&l(u,a,e),a+=r(u,e),g||(c=this.renderSeparator(a,e),s&&l(c,a,e),a+=r(c,e)),t[h.level]={button:u,separator:c,updated:!0}),u&&u.setState(2*!!g)}}}R.defaultOptions=n.options;let O=t.default.Color;var S=r.n(O);let M=t.default.SeriesRegistry;var C=r.n(M);let B=t.default.SVGElement;var I=r.n(B);let{column:{prototype:N}}=C().seriesTypes,{addEvent:D,defined:E}=a();!function(t){function e(t){let e=this.series,i=e.chart.renderer;this.moveToTopOnHover&&this.graphic&&(e.stateMarkerGraphic||(e.stateMarkerGraphic=new(I())(i,"use").css({pointerEvents:"none"}).add(this.graphic.parentGroup)),t?.state==="hover"?(this.graphic.attr({id:this.id}),e.stateMarkerGraphic.attr({href:`${i.url}#${this.id}`,visibility:"visible"})):e.stateMarkerGraphic.attr({href:""}))}t.pointMembers={dataLabelOnNull:!0,moveToTopOnHover:!0,isValid:function(){return null!==this.value&&this.value!==1/0&&this.value!==-1/0&&(void 0===this.value||!isNaN(this.value))}},t.seriesMembers={colorKey:"value",axisTypes:["xAxis","yAxis","colorAxis"],parallelArrays:["x","y","value"],pointArrayMap:["value"],trackerGroups:["group","markerGroup","dataLabelsGroup"],colorAttribs:function(t){let e={};return E(t.color)&&(!t.state||"normal"===t.state)&&(e[this.colorProp||"fill"]=t.color),e},pointAttribs:N.pointAttribs},t.compose=function(t){return D(t.prototype.pointClass,"afterSetState",e),t}}(i||(i={}));let G=i,k=t.default.Series;var z=r.n(k);let V=class{constructor(t,e,i,s){this.height=t,this.width=e,this.plot=s,this.direction=i,this.startDirection=i,this.total=0,this.nW=0,this.lW=0,this.nH=0,this.lH=0,this.elArr=[],this.lP={total:0,lH:0,nH:0,lW:0,nW:0,nR:0,lR:0,aspectRatio:function(t,e){return Math.max(t/e,e/t)}}}addElement(t){this.lP.total=this.elArr[this.elArr.length-1],this.total=this.total+t,0===this.direction?(this.lW=this.nW,this.lP.lH=this.lP.total/this.lW,this.lP.lR=this.lP.aspectRatio(this.lW,this.lP.lH),this.nW=this.total/this.height,this.lP.nH=this.lP.total/this.nW,this.lP.nR=this.lP.aspectRatio(this.nW,this.lP.nH)):(this.lH=this.nH,this.lP.lW=this.lP.total/this.lH,this.lP.lR=this.lP.aspectRatio(this.lP.lW,this.lH),this.nH=this.total/this.width,this.lP.nW=this.lP.total/this.nH,this.lP.nR=this.lP.aspectRatio(this.lP.nW,this.nH)),this.elArr.push(t)}reset(){this.nW=0,this.lW=0,this.elArr=[],this.total=0}},W=class{constructor(){this.childrenTotal=0,this.visible=!1}init(t,e,i,s,o,r,l){return this.id=t,this.i=e,this.children=i,this.height=s,this.level=o,this.series=r,this.parent=l,this}},U=function(t,e){let{animatableAttribs:i,onComplete:s,css:o,renderer:r}=e,l=t.series&&t.series.chart.hasRendered?void 0:t.series&&t.series.options.animation,a=t.graphic;if(e.attribs={...e.attribs,class:t.getClassName()},t.shouldDraw())a||(t.graphic=a="text"===e.shapeType?r.text():"image"===e.shapeType?r.image(e.imageUrl||"").attr(e.shapeArgs||{}):r[e.shapeType](e.shapeArgs||{}),a.add(e.group)),o&&a.css(o),a.attr(e.attribs).animate(i,!e.isNew&&l,s);else if(a){let e=()=>{t.graphic=a=a&&a.destroy(),"function"==typeof s&&s()};Object.keys(i).length?a.animate(i,void 0,()=>e()):e()}},{pie:{prototype:{pointClass:H}},scatter:{prototype:{pointClass:F}}}=C().seriesTypes,{extend:Y,isNumber:X,pick:$}=a();class j extends F{constructor(){super(...arguments),this.groupedPointsAmount=0,this.shapeType="rect"}draw(t){U(this,t)}getClassName(){let t=this.series,e=t.options,i=super.getClassName();return this.node.level<=t.nodeMap[t.rootNode].level&&this.node.children.length?i+=" highcharts-above-level":this.node.isGroup||this.node.isLeaf||t.nodeMap[t.rootNode].isGroup||$(e.interactByLeaf,!e.allowTraversingTree)?this.node.isGroup||this.node.isLeaf||t.nodeMap[t.rootNode].isGroup||(i+=" highcharts-internal-node"):i+=" highcharts-internal-node-interactive",i}isValid(){return!!(this.id||X(this.value))}setState(t){super.setState.apply(this,arguments),this.graphic&&this.graphic.attr({zIndex:+("hover"===t)})}shouldDraw(){return X(this.plotY)&&null!==this.y}}Y(j.prototype,{setVisible:H.prototype.setVisible});let{isString:K}=a(),q={allowTraversingTree:!1,animationLimit:250,borderRadius:0,showInLegend:!1,marker:void 0,colorByPoint:!1,dataLabels:{enabled:!0,formatter:function(){let t=this&&this.point?this.point:{};return K(t.name)?t.name:""},headers:!1,inside:!0,padding:2,verticalAlign:"middle",style:{textOverflow:"ellipsis"}},tooltip:{headerFormat:"",pointFormat:"<b>{point.name}</b>: {point.value}<br/>",clusterFormat:"+ {point.groupedPointsAmount} more...<br/>"},ignoreHiddenPoint:!0,layoutAlgorithm:"sliceAndDice",layoutStartingDirection:"vertical",alternateStartingDirection:!1,levelIsConstant:!0,traverseUpButton:{position:{align:"right",x:-10,y:10}},borderColor:"#e6e6e6",borderWidth:1,colorKey:"colorValue",opacity:.15,states:{hover:{borderColor:"#999999",brightness:.1*!C().seriesTypes.heatmap,halo:!1,opacity:.75,shadow:!1}},legendSymbol:"rectangle",traverseToLeaf:!1,cluster:{className:void 0,color:void 0,enabled:!1,pixelWidth:void 0,pixelHeight:void 0,name:void 0,reductionFactor:void 0,minimumClusterSize:5,layoutAlgorithm:{distance:0,gridSize:0,kmeansThreshold:0},marker:{lineWidth:0,radius:0}}};(s||(s={})).recursive=function t(e,i,s){let o=i.call(s||this,e);!1!==o&&t(o,i,s)};let Z=s,{extend:_,isArray:J,isNumber:Q,isObject:tt,merge:te,pick:ti,relativeLength:ts}=a(),to={getColor:function(t,e){let i,s,o,r,l,a,n=e.index,h=e.mapOptionsToLevel,d=e.parentColor,p=e.parentColorIndex,u=e.series,c=e.colors,g=e.siblings,v=u.points,f=u.chart.options.chart;return t&&(i=v[t.i],s=h[t.level]||{},i&&s.colorByPoint&&(r=i.index%(c?c.length:f.colorCount),o=c&&c[r]),u.chart.styledMode||(l=ti(i&&i.options.color,s&&s.color,o,d&&(t=>{let e=s&&s.colorVariation;return e&&"brightness"===e.key&&n&&g?S().parse(t).brighten(e.to*(n/g)).get():t})(d),u.color)),a=ti(i&&i.options.colorIndex,s&&s.colorIndex,r,p,e.colorIndex)),{color:l,colorIndex:a}},getLevelOptions:function(t){let e,i,s,o,r,l,a={};if(tt(t))for(o=Q(t.from)?t.from:1,l=t.levels,i={},e=tt(t.defaults)?t.defaults:{},J(l)&&(i=l.reduce((t,i)=>{let s,r,l;return tt(i)&&Q(i.level)&&(r=ti((l=te({},i)).levelIsConstant,e.levelIsConstant),delete l.levelIsConstant,delete l.level,tt(t[s=i.level+(r?0:o-1)])?te(!0,t[s],l):t[s]=l),t},{})),r=Q(t.to)?t.to:1,s=0;s<=r;s++)a[s]=te({},e,tt(i[s])?i[s]:{});return a},getNodeWidth:function(t,e){let{chart:i,options:s}=t,{nodeDistance:o=0,nodeWidth:r=0}=s,{plotSizeX:l=1}=i;if("auto"===r){if("string"==typeof o&&/%$/.test(o))return l/(e+parseFloat(o)/100*(e-1));let t=Number(o);return(l+t)/(e||1)-t}return ts(r,l)},setTreeValues:function t(e,i){let s=i.before,o=i.idRoot,r=i.mapIdToNode[o],l=!1!==i.levelIsConstant,a=i.points[e.i],n=a&&a.options||{},h=[],d=0;e.levelDynamic=e.level-(l?0:r.level),e.name=ti(a&&a.name,""),e.visible=o===e.id||!0===i.visible,"function"==typeof s&&(e=s(e,i)),e.children.forEach((s,o)=>{let r=_({},i);_(r,{index:o,siblings:e.children.length,visible:e.visible}),s=t(s,r),h.push(s),s.visible&&(d+=s.val)});let p=ti(n.value,d);return e.visible=p>=0&&(d>0||e.visible),e.children=h,e.childrenTotal=d,e.isLeaf=e.visible&&!d,e.val=p,e},updateRootId:function(t){let e,i;return tt(t)&&(i=tt(t.options)?t.options:{},e=ti(t.rootNode,i.rootId,""),tt(t.userOptions)&&(t.userOptions.rootId=e),t.rootNode=e),e}},{parse:tr}=S(),{composed:tl,noop:ta}=a(),{column:tn,scatter:th}=C().seriesTypes,{getColor:td,getLevelOptions:tp,updateRootId:tu}=to,{addEvent:tc,arrayMax:tg,clamp:tv,correctFloat:tf,crisp:tb,defined:tm,error:tx,extend:ty,fireEvent:tL,isArray:tP,isNumber:tT,isObject:tw,isString:tA,merge:tR,pick:tO,pushUnique:tS,splat:tM,stableSort:tC}=a();z().keepProps.push("simulation","hadOutsideDataLabels");let tB=!1;function tI(){let t,e=this.xAxis,i=this.yAxis;e&&i&&(this.is("treemap")?(t={endOnTick:!1,gridLineWidth:0,lineWidth:0,min:0,minPadding:0,max:100,maxPadding:0,startOnTick:!1,title:void 0,tickPositions:[]},ty(i.options,t),ty(e.options,t),tB=!0):tB&&(i.setOptions(i.userOptions),e.setOptions(e.userOptions),tB=!1))}class tN extends th{constructor(){super(...arguments),this.simulation=0}static compose(t){tS(tl,"TreemapSeries")&&tc(t,"afterBindAxes",tI)}algorithmCalcPoints(t,e,i,s){let o=i.plot,r=i.elArr.length-1,l,a,n,h,d=i.lW,p=i.lH,u,c=0;for(let t of(e?(d=i.nW,p=i.nH):u=i.elArr[r],i.elArr))(e||c<r)&&(0===i.direction?(l=o.x,a=o.y,h=t/(n=d)):(l=o.x,a=o.y,n=t/(h=p)),s.push({x:l,y:a,width:n,height:tf(h)}),0===i.direction?o.y=o.y+h:o.x=o.x+n),c+=1;i.reset(),0===i.direction?i.width=i.width-d:i.height=i.height-p,o.y=o.parent.y+(o.parent.height-i.height),o.x=o.parent.x+(o.parent.width-i.width),t&&(i.direction=1-i.direction),e||i.addElement(u)}algorithmFill(t,e,i){let s=[],o,r=e.direction,l=e.x,a=e.y,n=e.width,h=e.height,d,p,u,c;for(let g of i)o=e.width*e.height*(g.val/e.val),d=l,p=a,0===r?(n-=u=o/(c=h),l+=u):(h-=c=o/(u=n),a+=c),s.push({x:d,y:p,width:u,height:c,direction:0,val:0}),t&&(r=1-r);return s}algorithmLowAspectRatio(t,e,i){let s=[],o={x:e.x,y:e.y,parent:e},r=e.direction,l=i.length-1,a=new V(e.height,e.width,r,o),n,h=0;for(let r of i)n=e.width*e.height*(r.val/e.val),a.addElement(n),a.lP.nR>a.lP.lR&&this.algorithmCalcPoints(t,!1,a,s,o),h===l&&this.algorithmCalcPoints(t,!0,a,s,o),++h;return s}alignDataLabel(t,e,i){tn.prototype.alignDataLabel.apply(this,arguments),t.dataLabel&&t.dataLabel.attr({zIndex:(t.node.zIndex||0)+1})}applyTreeGrouping(){let t=this,e=t.parentList||{},{cluster:i}=t.options,s=i?.minimumClusterSize||5;if(i?.enabled){let o={},r=t=>{if(t?.point?.shapeArgs){let{width:e=0,height:s=0}=t.point.shapeArgs,{pixelWidth:r=0,pixelHeight:l=0}=i,a=tm(l),n=l?r*l:r*r;(e<r||s<(a?l:r)||e*s<n)&&!t.isGroup&&tm(t.parent)&&(o[t.parent]||(o[t.parent]=[]),o[t.parent].push(t))}t?.children.forEach(t=>{r(t)})};for(let l in r(t.tree),o)o[l]&&o[l].length>s&&o[l].forEach(s=>{let o=e[l].indexOf(s.i);if(-1!==o){e[l].splice(o,1);let r=`highcharts-grouped-treemap-points-${s.parent||"root"}`,a=t.points.find(t=>t.id===r);if(!a){let s=t.pointClass,o=t.points.length;ty(a=new s(t,{className:i.className,color:i.color,id:r,index:o,isGroup:!0,value:0}),{formatPrefix:"cluster"}),t.points.push(a),e[l].push(o),e[r]=[]}let n=a.groupedPointsAmount+1,h=t.points[a.index].options.value||0,d=i.name||`+ ${n}`;t.points[a.index].groupedPointsAmount=n,t.points[a.index].options.value=h+(s.point.value||0),t.points[a.index].name=d,e[r].push(s.point.index)}});t.nodeMap={},t.nodeList=[],t.parentList=e;let l=t.buildTree("",-1,0,t.parentList);t.translate(l)}}calculateChildrenAreas(t,e){let i=this.options,s=this.mapOptionsToLevel[t.level+1],o=tO(s?.layoutAlgorithm&&this[s?.layoutAlgorithm]&&s.layoutAlgorithm,i.layoutAlgorithm),r=i.alternateStartingDirection,l=t.children.filter(e=>t.isGroup||!e.ignore),a=s?.groupPadding??i.groupPadding??0,n=this.nodeMap[this.rootNode];if(!o)return;let h=[],d=n.pointValues?.width||0,p=n.pointValues?.height||0;s?.layoutStartingDirection&&(e.direction=+("vertical"!==s.layoutStartingDirection)),h=this[o](e,l);let u=-1;for(let t of l){let i=h[++u];t===n&&(d=d||i.width,p=i.height);let s=a/(this.xAxis.len/p),o=a/(this.yAxis.len/p);if(t.values=tR(i,{val:t.childrenTotal,direction:r?1-e.direction:e.direction}),t.children.length&&t.point.dataLabels?.length){let e=tg(t.point.dataLabels.map(t=>t.options?.headers&&t.height||0))/(this.yAxis.len/p);e<t.values.height/2&&(t.values.y+=e,t.values.height-=e)}if(a){let e=Math.min(s,t.values.width/4),i=Math.min(o,t.values.height/4);t.values.x+=e,t.values.width-=2*e,t.values.y+=i,t.values.height-=2*i}t.pointValues=tR(i,{x:i.x/this.axisRatio,y:100-i.y-i.height,width:i.width/this.axisRatio}),t.children.length&&this.calculateChildrenAreas(t,t.values)}let c=(t,e=[],i=!0)=>(t.children.forEach(t=>{i&&t.isLeaf?e.push(t.point):i||t.isLeaf||e.push(t.point),t.children.length&&c(t,e,i)}),e);if("leaf"===i.nodeSizeBy&&t===n&&this.hasOutsideDataLabels&&!c(n,void 0,!1).some(t=>tT(t.options.value))&&!tT(n.point?.options.value)){let i=c(n),s=i.map(t=>t.options.value||0),o=i.map(({node:{pointValues:t}})=>t?t.width*t.height:0),r=s.reduce((t,e)=>t+e,0),l=o.reduce((t,e)=>t+e,0)/r,a=0,h=0;i.forEach((t,e)=>{let i=tv((s[e]?o[e]/s[e]:1)/l,.8,1.4),r=1-i;t.value&&(o[e]<20&&(r*=o[e]/20),r>h&&(h=r),r<a&&(a=r),t.simulatedValue=(t.simulatedValue||t.value)/i)}),(a<-.05||h>.05)&&this.simulation<10?(this.simulation++,this.setTreeValues(t),e.val=t.val,this.calculateChildrenAreas(t,e)):(i.forEach(t=>{delete t.simulatedValue}),this.setTreeValues(t),this.simulation=0)}}createList(t){let e=this.chart,i=e.breadcrumbs,s=[];if(i){let i=0;s.push({level:i,levelOptions:e.series[0]});let o=t.target.nodeMap[t.newRootId],r=[];for(;o.parent||""===o.parent;)r.push(o),o=t.target.nodeMap[o.parent];for(let t of r.reverse())s.push({level:++i,levelOptions:t});s.length<=1&&(s.length=0)}return s}drawDataLabels(){let t=this.mapOptionsToLevel,e=this.points.filter(function(t){return t.node.visible||tm(t.dataLabel)}),i=tM(this.options.dataLabels||{})[0]?.padding,s=e.some(t=>tT(t.plotY));for(let o of e){let e={},r={style:e},l=t[o.node.level];if((!o.node.isLeaf&&!o.node.isGroup||o.node.isGroup&&o.node.level<=this.nodeMap[this.rootNode].level)&&(r.enabled=!1),l?.dataLabels&&(tR(!0,r,tM(l.dataLabels)[0]),this.hasDataLabels=()=>!0),o.node.isLeaf?r.inside=!0:r.headers&&(r.verticalAlign="top"),o.shapeArgs&&s){let{height:t=0,width:s=0}=o.shapeArgs;if(s>32&&t>16&&o.shouldDraw()){let l=s-2*(r.padding||i||0);e.width=`${l}px`,e.lineClamp??(e.lineClamp=Math.floor(t/16)),e.visibility="inherit",r.headers&&o.dataLabel?.attr({width:l})}else e.width=`${s}px`,e.visibility="hidden"}o.dlOptions=tR(r,o.options.dataLabels)}super.drawDataLabels(e)}drawPoints(t=this.points){let e=this.chart,i=e.renderer,s=e.styledMode,o=this.options,r=s?{}:o.shadow,l=o.borderRadius,a=e.pointCount<o.animationLimit,n=o.allowTraversingTree;for(let e of t){let t=e.node.levelDynamic,h={},d={},p={},u="level-group-"+e.node.level,c=!!e.graphic,g=a&&c,v=e.shapeArgs;e.shouldDraw()&&(e.isInside=!0,l&&(d.r=l),tR(!0,g?h:d,c?v:{},s?{}:this.pointAttribs(e,e.selected?"select":void 0)),this.colorAttribs&&s&&ty(p,this.colorAttribs(e)),this[u]||(this[u]=i.g(u).attr({zIndex:1e3-(t||0)}).add(this.group),this[u].survive=!0)),e.draw({animatableAttribs:h,attribs:d,css:p,group:this[u],imageUrl:e.imageUrl,renderer:i,shadow:r,shapeArgs:v,shapeType:e.shapeType}),n&&e.graphic&&(e.drillId=o.interactByLeaf?this.drillToByLeaf(e):this.drillToByGroup(e))}}drillToByGroup(t){return(!t.node.isLeaf||!!t.node.isGroup)&&t.id}drillToByLeaf(t){let{traverseToLeaf:e}=t.series.options,i=!1,s;if(t.node.parent!==this.rootNode&&t.node.isLeaf){if(e)i=t.id;else for(s=t.node;!i;)void 0!==s.parent&&(s=this.nodeMap[s.parent]),s.parent===this.rootNode&&(i=s.id)}return i}drillToNode(t,e){tx(32,!1,void 0,{"treemap.drillToNode":"use treemap.setRootNode"}),this.setRootNode(t,e)}drillUp(){let t=this.nodeMap[this.rootNode];t&&tA(t.parent)&&this.setRootNode(t.parent,!0,{trigger:"traverseUpButton"})}getExtremes(){let{dataMin:t,dataMax:e}=super.getExtremes(this.colorValueData);return this.valueMin=t,this.valueMax=e,super.getExtremes()}getListOfParents(t,e){let i=tP(t)?t:[],s=tP(e)?e:[],o=i.reduce(function(t,e,i){let s=tO(e.parent,"");return void 0===t[s]&&(t[s]=[]),t[s].push(i),t},{"":[]});for(let t of Object.keys(o)){let e=o[t];if(""!==t&&-1===s.indexOf(t)){for(let t of e)o[""].push(t);delete o[t]}}return o}getTree(){let t=this.data.map(function(t){return t.id});return this.parentList=this.getListOfParents(this.data,t),this.nodeMap={},this.nodeList=[],this.buildTree("",-1,0,this.parentList||{})}buildTree(t,e,i,s,o){let r=[],l=this.points[e],a=0,n;for(let e of s[t]||[])a=Math.max((n=this.buildTree(this.points[e].id,e,i+1,s,t)).height+1,a),r.push(n);let h=new this.NodeClass().init(t,e,r,a,i,this,o);for(let t of r)t.parentNode=h;return this.nodeMap[h.id]=h,this.nodeList.push(h),l&&(l.node=h,h.point=l),h}hasData(){return!!this.dataTable.rowCount}init(t,e){let i=this,s=tR(e.drillUpButton,e.breadcrumbs),o=tc(i,"setOptions",t=>{let e=t.userOptions;tm(e.allowDrillToNode)&&!tm(e.allowTraversingTree)&&(e.allowTraversingTree=e.allowDrillToNode,delete e.allowDrillToNode),tm(e.drillUpButton)&&!tm(e.traverseUpButton)&&(e.traverseUpButton=e.drillUpButton,delete e.drillUpButton);let i=tM(e.dataLabels||{});e.levels?.forEach(t=>{i.push.apply(i,tM(t.dataLabels||{}))}),this.hasOutsideDataLabels=i.some(t=>t.headers)});super.init(t,e),delete i.opacity,i.eventsToUnbind.push(o),i.options.allowTraversingTree&&(i.eventsToUnbind.push(tc(i,"click",i.onClickDrillToNode)),i.eventsToUnbind.push(tc(i,"setRootNode",function(t){let e=i.chart;e.breadcrumbs&&e.breadcrumbs.updateProperties(i.createList(t))})),i.eventsToUnbind.push(tc(i,"update",function(t,e){let i=this.chart.breadcrumbs;i&&t.options.breadcrumbs&&i.update(t.options.breadcrumbs),this.hadOutsideDataLabels=this.hasOutsideDataLabels})),i.eventsToUnbind.push(tc(i,"destroy",function(t){let e=this.chart;e.breadcrumbs&&!t.keepEventsForUpdate&&(e.breadcrumbs.destroy(),e.breadcrumbs=void 0)}))),t.breadcrumbs||(t.breadcrumbs=new R(t,s)),i.eventsToUnbind.push(tc(t.breadcrumbs,"up",function(t){let e=this.level-t.newLevel;for(let t=0;t<e;t++)i.drillUp()}))}onClickDrillToNode(t){let e=t.point,i=e?.drillId;tA(i)&&(e.setState(""),this.setRootNode(i,!0,{trigger:"click"}))}pointAttribs(t,e){let i=tw(this.mapOptionsToLevel)?this.mapOptionsToLevel:{},s=t&&i[t.node.level]||{},o=this.options,r=e&&o.states&&o.states[e]||{},l=t?.getClassName()||"",a={stroke:t&&t.borderColor||s.borderColor||r.borderColor||o.borderColor,"stroke-width":tO(t&&t.borderWidth,s.borderWidth,r.borderWidth,o.borderWidth),dashstyle:t?.borderDashStyle||s.borderDashStyle||r.borderDashStyle||o.borderDashStyle,fill:t?.color||this.color};return -1!==l.indexOf("highcharts-above-level")?(a.fill="none",a["stroke-width"]=0):-1!==l.indexOf("highcharts-internal-node-interactive")?(a["fill-opacity"]=r.opacity??o.opacity??1,a.cursor="pointer"):-1!==l.indexOf("highcharts-internal-node")?a.fill="none":e&&r.brightness&&(a.fill=tr(a.fill).brighten(r.brightness).get()),a}setColorRecursive(t,e,i,s,o){let r=this?.chart,l=r?.options?.colors;if(t){let r=td(t,{colors:l,index:s,mapOptionsToLevel:this.mapOptionsToLevel,parentColor:e,parentColorIndex:i,series:this,siblings:o}),a=this.points[t.i];a&&(a.color=r.color,a.colorIndex=r.colorIndex);let n=-1;for(let e of t.children||[])this.setColorRecursive(e,r.color,r.colorIndex,++n,t.children.length)}}setPointValues(){let t=this,{points:e,xAxis:i,yAxis:s}=t,o=t.chart.styledMode,r=e=>o?0:t.pointAttribs(e)["stroke-width"]||0;for(let t of e){let{pointValues:e,visible:o}=t.node;if(e&&o){let{height:o,width:l,x:a,y:n}=e,h=r(t),d=i.toPixels(a,!0),p=i.toPixels(a+l,!0),u=s.toPixels(n,!0),c=s.toPixels(n+o,!0),g=0===d?h/2:tb(i.toPixels(a,!0),h,!0),v=p===i.len?i.len-h/2:tb(i.toPixels(a+l,!0),h,!0),f=u===s.len?s.len-h/2:tb(s.toPixels(n,!0),h,!0),b=0===c?h/2:tb(s.toPixels(n+o,!0),h,!0),m={x:Math.min(g,v),y:Math.min(f,b),width:Math.abs(v-g),height:Math.abs(b-f)};t.plotX=m.x+m.width/2,t.plotY=m.y+m.height/2,t.shapeArgs=m}else delete t.plotX,delete t.plotY}}setRootNode(t,e,i){tL(this,"setRootNode",ty({newRootId:t,previousRootId:this.rootNode,redraw:tO(e,!0),series:this},i),function(t){let e=t.series;e.idPreviousRoot=t.previousRootId,e.rootNode=t.newRootId,e.isDirty=!0,t.redraw&&e.chart.redraw()})}setState(t){this.options.inactiveOtherPoints=!0,super.setState(t,!1),this.options.inactiveOtherPoints=!1}setTreeValues(t){let e=this.options,i=this.rootNode,s=this.nodeMap[i],o="boolean"!=typeof e.levelIsConstant||e.levelIsConstant,r=[],l=this.points[t.i],a=0;for(let e of t.children)e=this.setTreeValues(e),r.push(e),e.ignore||(a+=e.val);tC(r,(t,e)=>(t.sortIndex||0)-(e.sortIndex||0));let n=tO(l?.simulatedValue,l?.options.value,a);return l&&(l.value=n),l?.isGroup&&e.cluster?.reductionFactor&&(n/=e.cluster.reductionFactor),t.parentNode?.point?.isGroup&&this.rootNode!==t.parent&&(t.visible=!1),ty(t,{children:r,childrenTotal:a,ignore:!(tO(l?.visible,!0)&&n>0),isLeaf:t.visible&&!a,isGroup:l?.isGroup,levelDynamic:t.level-(o?0:s.level),name:tO(l?.name,""),sortIndex:tO(l?.sortIndex,-n),val:n}),t}sliceAndDice(t,e){return this.algorithmFill(!0,t,e)}squarified(t,e){return this.algorithmLowAspectRatio(!0,t,e)}strip(t,e){return this.algorithmLowAspectRatio(!1,t,e)}stripes(t,e){return this.algorithmFill(!1,t,e)}translate(t){let e=this,i=e.options,s=!t,o=tu(e),r,l,a,n;t||o.startsWith("highcharts-grouped-treemap-points-")||((this.points||[]).forEach(t=>{t.isGroup&&t.destroy()}),super.translate(),t=e.getTree()),e.tree=t=t||e.tree,r=e.nodeMap[o],""===o||r||(e.setRootNode("",!1),o=e.rootNode,r=e.nodeMap[o]),r.point?.isGroup||(e.mapOptionsToLevel=tp({from:r.level+1,levels:i.levels,to:t.height,defaults:{levelIsConstant:e.options.levelIsConstant,colorByPoint:i.colorByPoint}})),Z.recursive(e.nodeMap[e.rootNode],t=>{let i=t.parent,s=!1;return t.visible=!0,(i||""===i)&&(s=e.nodeMap[i]),s}),Z.recursive(e.nodeMap[e.rootNode].children,t=>{let e=!1;for(let i of t)i.visible=!0,i.children.length&&(e=(e||[]).concat(i.children));return e}),e.setTreeValues(t),e.axisRatio=e.xAxis.len/e.yAxis.len,e.nodeMap[""].pointValues=l={x:0,y:0,width:100,height:100},e.nodeMap[""].values=a=tR(l,{width:l.width*e.axisRatio,direction:+("vertical"!==i.layoutStartingDirection),val:t.val}),(this.hasOutsideDataLabels||this.hadOutsideDataLabels)&&this.drawDataLabels(),e.calculateChildrenAreas(t,a),e.colorAxis||i.colorByPoint||e.setColorRecursive(e.tree),i.allowTraversingTree&&r.pointValues&&(n=r.pointValues,e.xAxis.setExtremes(n.x,n.x+n.width,!1),e.yAxis.setExtremes(n.y,n.y+n.height,!1),e.xAxis.setScale(),e.yAxis.setScale()),e.setPointValues(),s&&e.applyTreeGrouping()}}tN.defaultOptions=tR(th.defaultOptions,q),ty(tN.prototype,{buildKDTree:ta,colorAttribs:G.seriesMembers.colorAttribs,colorKey:"colorValue",directTouch:!0,getExtremesFromAll:!0,getSymbol:ta,optionalAxis:"colorAxis",parallelArrays:["x","y","value","colorValue"],pointArrayMap:["value","colorValue"],pointClass:j,NodeClass:W,trackerGroups:["group","dataLabelsGroup"],utils:Z}),G.compose(tN),C().registerSeriesType("treemap",tN);let{deg2rad:tD}=a(),{fireEvent:tE,isNumber:tG,pick:tk,relativeLength:tz}=a();(e=o||(o={})).getCenter=function(){let t=this.options,e=this.chart,i=2*(t.slicedOffset||0),s=e.plotWidth-2*i,o=e.plotHeight-2*i,r=t.center,l=Math.min(s,o),a=t.thickness,n,h=t.size,d=t.innerSize||0,p,u;"string"==typeof h&&(h=parseFloat(h)),"string"==typeof d&&(d=parseFloat(d));let c=[tk(r?.[0],"50%"),tk(r?.[1],"50%"),tk(h&&h<0?void 0:t.size,"100%"),tk(d&&d<0?void 0:t.innerSize||0,"0%")];for(!e.angular||this instanceof z()||(c[3]=0),p=0;p<4;++p)u=c[p],n=p<2||2===p&&/%$/.test(u),c[p]=tz(u,[s,o,l,c[2]][p])+(n?i:0);return c[3]>c[2]&&(c[3]=c[2]),tG(a)&&2*a<c[2]&&a>0&&(c[3]=c[2]-2*a),tE(this,"afterGetCenter",{positions:c}),c},e.getStartAndEndRadians=function(t,e){let i=tG(t)?t:0,s=tG(e)&&e>i&&e-i<360?e:i+360;return{start:tD*(i+-90),end:tD*(s+-90)}};let tV=o,{series:{prototype:{pointClass:tW}},seriesTypes:{treemap:{prototype:{pointClass:tU}}}}=C(),{correctFloat:tH,extend:tF,pInt:tY}=a();class tX extends tU{getDataLabelPath(t){let e=this.series.chart.renderer,i=this.shapeExisting,s=i.r+tY(t.options?.distance||0),o=i.start,r=i.end,l=o+(r-o)/2,a=l<0&&l>-Math.PI||l>Math.PI,n;return o===-Math.PI/2&&tH(r)===tH(1.5*Math.PI)&&(o=-Math.PI+Math.PI/360,r=-Math.PI/360,a=!0),r-o>Math.PI&&(a=!1,n=!0,r-o>2*Math.PI-.01&&(o+=.01,r-=.01)),this.dataLabelPath&&(this.dataLabelPath=this.dataLabelPath.destroy()),this.dataLabelPath=e.arc({open:!0,longArc:+!!n}).attr({start:a?o:r,end:a?r:o,clockwise:+a,x:i.x,y:i.y,r:(s+i.innerR)/2}).add(e.defs),this.dataLabelPath}isValid(){return!0}}tF(tX.prototype,{getClassName:tW.prototype.getClassName,haloPath:tW.prototype.haloPath,setState:tW.prototype.setState});let{seriesTypes:{treemap:t$}}=C(),{isNumber:tj,isObject:tK,merge:tq}=a();function tZ(t,e){let i=[];if(tj(t)&&tj(e)&&t<=e)for(let s=t;s<=e;s++)i.push(s);return i}let t_={calculateLevelSizes:function(t,e){let i=tK(e)?e:{},s,o=0,r,l,a,n;if(tK(t)){for(let e of(s=tq({},t),l=tZ(tj(i.from)?i.from:0,tj(i.to)?i.to:0),a=Object.keys(s).filter(t=>-1===l.indexOf(+t)),r=n=tj(i.diffRadius)?i.diffRadius:0,l)){let t=s[e],i=t.levelSize.unit,l=t.levelSize.value;"weight"===i?o+=l:"percentage"===i?(t.levelSize={unit:"pixels",value:l/100*r},n-=t.levelSize.value):"pixels"===i&&(n-=l)}for(let t of l){let e=s[t];if("weight"===e.levelSize.unit){let i=e.levelSize.value;s[t].levelSize={unit:"pixels",value:i/o*n}}}for(let t of a)s[t].levelSize={value:0,unit:"pixels"}}return s},getLevelFromAndTo:function({level:t,height:e}){return{from:t>0?t:1,to:t+e}},range:tZ,recursive:t$.prototype.utils.recursive},{deg2rad:tJ}=a(),{addEvent:tQ,merge:t0,uniqueKey:t1,defined:t2,extend:t6}=a();function t5(t,e){e=t0(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);let i=this.renderer.url,s=this.text||this,o=s.textPath,{attributes:r,enabled:l}=e;if(t=t||o&&o.path,o&&o.undo(),t&&l){let e=tQ(s,"afterModifyTree",e=>{if(t&&l){let o=t.attr("id");o||t.attr("id",o=t1());let l={x:0,y:0};t2(r.dx)&&(l.dx=r.dx,delete r.dx),t2(r.dy)&&(l.dy=r.dy,delete r.dy),s.attr(l),this.attr({transform:""}),this.box&&(this.box=this.box.destroy());let a=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:t6(r,{"text-anchor":r.textAnchor,href:`${i}#${o}`}),children:a}}});s.textPath={path:t,undo:e}}else s.attr({dx:0,dy:0}),delete s.textPath;return this.added&&(s.textCache="",this.renderer.buildText(s)),this}function t3(t){let e=t.bBox,i=this.element?.querySelector("textPath");if(i){let t=[],{b:s,h:o}=this.renderer.fontMetrics(this.element),r=o-s,l=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),a=i.innerHTML.replace(l,"").split(/<tspan class="highcharts-br"[^>]*>/),n=a.length,h=(t,e)=>{let{x:o,y:l}=e,a=(i.getRotationOfChar(t)-90)*tJ,n=Math.cos(a),h=Math.sin(a);return[[o-r*n,l-r*h],[o+s*n,l+s*h]]};for(let e=0,s=0;s<n;s++){let o=a[s].length;for(let r=0;r<o;r+=5)try{let o=e+r+s,[l,a]=h(o,i.getStartPositionOfChar(o));0===r?(t.push(a),t.push(l)):(0===s&&t.unshift(a),s===n-1&&t.push(l))}catch(t){break}e+=o-1;try{let o=e+s,r=i.getEndPositionOfChar(o),[l,a]=h(o,r);t.unshift(a),t.unshift(l)}catch(t){break}}t.length&&t.push(t[0].slice()),e.polygon=t}return e}function t9(t){let e=t.labelOptions,i=t.point,s=e[i.formatPrefix+"TextPath"]||e.textPath;s&&!e.useHTML&&(this.setTextPath(i.getDataLabelPath?.(this)||i.graphic,s),i.dataLabelPath&&!s.enabled&&(i.dataLabelPath=i.dataLabelPath.destroy()))}let{getCenter:t8,getStartAndEndRadians:t4}=tV,{noop:t7}=a(),{column:et,treemap:ee}=C().seriesTypes,{getColor:ei,getLevelOptions:es,setTreeValues:eo,updateRootId:er}=to,{defined:el,error:ea,extend:en,fireEvent:eh,isNumber:ed,isObject:ep,isString:eu,merge:ec,splat:eg}=a();({compose:function(t){tQ(t,"afterGetBBox",t3),tQ(t,"beforeAddingDataLabel",t9);let e=t.prototype;e.setTextPath||(e.setTextPath=t5)}}).compose(I());let ev=180/Math.PI,ef=function(t,e,i,s){return{x:t+Math.cos(i)*s,y:e+Math.sin(i)*s}};function eb(t,e){let i=e.mapIdToNode,s=t.parent,o=s?i[s]:void 0,r=e.series,l=r.chart,a=r.points[t.i],n=ei(t,{colors:r.options.colors||l&&l.options.colors,colorIndex:r.colorIndex,index:e.index,mapOptionsToLevel:e.mapOptionsToLevel,parentColor:o&&o.color,parentColorIndex:o&&o.colorIndex,series:e.series,siblings:e.siblings});return t.color=n.color,t.colorIndex=n.colorIndex,a&&(a.color=t.color,a.colorIndex=t.colorIndex,t.sliced=t.id!==e.idRoot&&a.sliced),t}class em extends ee{alignDataLabel(t,e,i){if(!i.textPath||!i.textPath.enabled)return e.placed=!1,super.alignDataLabel.apply(this,arguments)}animate(t){let e,i=this.chart,s=[i.plotWidth/2,i.plotHeight/2],o=i.plotLeft,r=i.plotTop,l=this.group;t?(e={translateX:s[0]+o,translateY:s[1]+r,scaleX:.001,scaleY:.001,rotation:10,opacity:.01},l.attr(e)):(e={translateX:o,translateY:r,scaleX:1,scaleY:1,rotation:0,opacity:1},l.animate(e,this.options.animation))}drawPoints(){let t=this,e=t.mapOptionsToLevel,i=t.shapeRoot,s=t.group,o=t.hasRendered,r=t.rootNode,l=t.idPreviousRoot,a=t.nodeMap,n=a[l],h=n&&n.shapeArgs,d=t.points,p=t.startAndEndRadians,u=t.chart,c=u&&u.options&&u.options.chart||{},g="boolean"!=typeof c.animation||c.animation,v=t.center,f={x:v[0],y:v[1]},b=v[3]/2,m=t.chart.renderer,x=!!(g&&o&&r!==l&&t.dataLabelsGroup),y,L=!1,P=!1;for(let n of(x&&(t.dataLabelsGroup.attr({opacity:0}),y=function(){L=!0,t.dataLabelsGroup&&t.dataLabelsGroup.animate({opacity:1,visibility:"inherit"})}),d)){let d,c,v=n.node,x=e[v.level],L=n.shapeExisting||{},T=v.shapeArgs||{},w=!!(v.visible&&v.shapeArgs);T.borderRadius=t.options.borderRadius,d=o&&g?function(t,e){let i=e.point,s=e.radians,o=e.innerR,r=e.idRoot,l=e.idPreviousRoot,a=e.shapeExisting,n=e.shapeRoot,h=e.shapePreviousRoot,d=e.visible,p={},u={end:t.end,start:t.start,innerR:t.innerR,r:t.r,x:t.x,y:t.y};return d?!i.graphic&&h&&((p=r===i.id?{start:s.start,end:s.end}:h.end<=t.start?{start:s.end,end:s.end}:{start:s.start,end:s.start}).innerR=p.r=o):i.graphic&&(l===i.id?u={innerR:o,r:o}:n&&(u=n.end<=a.start?{innerR:o,r:o,start:s.end,end:s.end}:{innerR:o,r:o,start:s.start,end:s.start})),{from:p,to:u}}(T,{center:f,point:n,radians:p,innerR:b,idRoot:r,idPreviousRoot:l,shapeExisting:L,shapeRoot:i,shapePreviousRoot:h,visible:w}):{to:T,from:{}},en(n,{shapeExisting:T,tooltipPos:[T.plotX,T.plotY],drillId:function(t,e,i){let s;return t.node.isLeaf||(s=e===t.id?i[e].parent:t.id),s}(n,r,a),name:""+(n.name||n.id||n.index),plotX:T.plotX,plotY:T.plotY,value:v.val,isInside:w,isNull:!w}),n.dlOptions=function(t){let e=t.point,i=ep(t.shapeArgs)?t.shapeArgs:{},{end:s=0,radius:o=0,start:r=0}=i,l=ep(t.optionsPoint)?t.optionsPoint.dataLabels:{},a=ec(eg(ep(t.level)?t.level.dataLabels:{})[0],l),n=a.style=a.style||{},{innerArcLength:h=0,outerArcLength:d=0}=e,p,u,c=a.rotationMode,g=el(n.width)?parseInt(n.width||"0",10):void 0;return!ed(a.rotation)&&(("auto"===c||"circular"===c)&&(a.useHTML&&"circular"===c&&(c="auto"),h<1&&d>o?(p=0,e.dataLabelPath&&"circular"===c&&(a.textPath={enabled:!0}),s-r<Math.PI&&(g=.7*o)):h>1&&d>1.5*o?"circular"===c?a.textPath={enabled:!0,attributes:{dy:5}}:c="parallel":(e.dataLabel?.textPath&&"circular"===c&&(a.textPath={enabled:!1}),c="perpendicular")),"auto"!==c&&"circular"!==c&&(e.dataLabel?.textPath&&(a.textPath={enabled:!1}),p=s-(s-r)/2),"parallel"===c?g=Math.min(2.5*o,(d+h)/2):!el(g)&&o&&(g=1===e.node.level?2*o:o),"perpendicular"===c&&(d<16?g=1:i.radius&&(n.lineClamp=Math.floor(h/16)||1,g=o-(h<16?(16-h)/(d-h)*o:0))),g=Math.max((g||0)-2*(a.padding||0),1),u=(p||0)*ev%180,"parallel"===c&&(u-=90),u>90?u-=180:u<-90&&(u+=180),a.rotation=u),a.textPath&&(0===e.shapeExisting.innerR&&a.textPath.enabled?(a.rotation=0,a.textPath.enabled=!1,g=Math.max(2*e.shapeExisting.r-2*(a.padding||0),1)):e.dlOptions?.textPath&&!e.dlOptions.textPath.enabled&&"circular"===c&&(a.textPath.enabled=!0),a.textPath.enabled&&(a.rotation=0,g=Math.max((d+h)/2-2*(a.padding||0),1),n.whiteSpace="nowrap")),n.width=g+"px",a}({point:n,level:x,optionsPoint:n.options,shapeArgs:T}),!P&&w&&(P=!0,c=y),n.draw({animatableAttribs:d.to,attribs:en(d.from,!u.styledMode&&t.pointAttribs(n,n.selected&&"select")),onComplete:c,group:s,renderer:m,shapeType:"arc",shapeArgs:T})}x&&P?(t.hasRendered=!1,t.options.dataLabels.defer=!0,et.prototype.drawDataLabels.call(t),t.hasRendered=!0,L&&y()):et.prototype.drawDataLabels.call(t),t.idPreviousRoot=r}layoutAlgorithm(t,e,i){let s=t.start,o=t.end-s,r=t.val,l=t.x,a=t.y,n=i&&ep(i.levelSize)&&ed(i.levelSize.value)?i.levelSize.value:0,h=t.r,d=h+n,p=i&&ed(i.slicedOffset)?i.slicedOffset:0;return(e||[]).reduce((t,e)=>{let i=1/r*e.val*o,u=ef(l,a,s+i/2,p),c={x:e.sliced?u.x:l,y:e.sliced?u.y:a,innerR:h,r:d,radius:n,start:s,end:s+i};return t.push(c),s=c.end,t},[])}setRootNode(t,e,i){if(1===this.nodeMap[t].level&&1===this.nodeList.filter(t=>1===t.level).length){if(""===this.idPreviousRoot)return;t=""}super.setRootNode(t,e,i)}setShapeArgs(t,e,i){let s=i[t.level+1],o=t.children.filter(function(t){return t.visible}),r=[];r=this.layoutAlgorithm(e,o,s);let l=-1;for(let t of o){let e=r[++l],s=e.start+(e.end-e.start)/2,o=e.innerR+(e.r-e.innerR)/2,a=e.end-e.start,n=0===e.innerR&&a>6.28?{x:e.x,y:e.y}:ef(e.x,e.y,s,o),h=t.val?t.childrenTotal>t.val?t.childrenTotal:t.val:t.childrenTotal;this.points[t.i]&&(this.points[t.i].innerArcLength=a*e.innerR,this.points[t.i].outerArcLength=a*e.r),t.shapeArgs=ec(e,{plotX:n.x,plotY:n.y}),t.values=ec(e,{val:h}),t.children.length&&this.setShapeArgs(t,t.values,i)}}translate(){let t=this.options,e=this.center=this.getCenter(),i=this.startAndEndRadians=t4(t.startAngle,t.endAngle),s=e[3]/2,o=e[2]/2,r=er(this),l=this.nodeMap,a,n=l&&l[r],h={};this.shapeRoot=n&&n.shapeArgs,this.generatePoints(),eh(this,"afterTranslate");let d=this.tree=this.getTree(),p=eu((n=(l=this.nodeMap)[r]).parent)?n.parent:"",u=l[p],{from:c,to:g}=t_.getLevelFromAndTo(n);a=es({from:c,levels:this.options.levels,to:g,defaults:{colorByPoint:t.colorByPoint,dataLabels:t.dataLabels,levelIsConstant:t.levelIsConstant,levelSize:t.levelSize,slicedOffset:t.slicedOffset}}),a=t_.calculateLevelSizes(a,{diffRadius:o-s,from:c,to:g}),eo(d,{before:eb,idRoot:r,levelIsConstant:t.levelIsConstant,mapOptionsToLevel:a,mapIdToNode:l,points:this.points,series:this});let v=l[""].shapeArgs={end:i.end,r:s,start:i.start,val:n.val,x:e[0],y:e[1]};for(let t of(this.setShapeArgs(u,v,a),this.mapOptionsToLevel=a,this.points))h[t.id]&&ea(31,!1,this.chart),h[t.id]=!0}}em.defaultOptions=ec(ee.defaultOptions,{center:["50%","50%"],clip:!1,colorByPoint:!1,opacity:1,dataLabels:{allowOverlap:!0,defer:!0,rotationMode:"circular",style:{textOverflow:"ellipsis"}},rootId:void 0,levelIsConstant:!0,levelSize:{value:1,unit:"weight"},slicedOffset:10}),en(em.prototype,{axisTypes:[],drawDataLabels:t7,getCenter:t8,isCartesian:!1,onPointSupported:!0,pointAttribs:et.prototype.pointAttribs,pointClass:tX,NodeClass:class extends W{},utils:t_}),C().registerSeriesType("sunburst",em);let ex=a();ex.Breadcrumbs=ex.Breadcrumbs||R,ex.Breadcrumbs.compose(ex.Chart,ex.defaultOptions);let ey=a();export{ey as default};