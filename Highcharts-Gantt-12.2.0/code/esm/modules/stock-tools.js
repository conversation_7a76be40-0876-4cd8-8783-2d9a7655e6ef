import*as t from"../highcharts.js";import"./stock.js";var i,e={};e.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},e.d=(t,i)=>{for(var s in i)e.o(i,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:i[s]})},e.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i);let s=t.default;var n=e.n(s);!function(t){t.compose=function(t){return t.navigation||(t.navigation=new i(t)),t};class i{constructor(t){this.updates=[],this.chart=t}addUpdate(t){this.chart.navigation.updates.push(t)}update(t,i){this.updates.forEach(e=>{e.call(this.chart,t,i)})}}t.Additions=i}(i||(i={}));let o=i,a=t.default.Templating;var r=e.n(a);let{defined:l,isNumber:h,pick:c}=n(),p={backgroundColor:"string",borderColor:"string",borderRadius:"string",color:"string",fill:"string",fontSize:"string",labels:"string",name:"string",stroke:"string",title:"string"},d={annotationsFieldsTypes:p,getAssignedAxis:function(t){return t.filter(t=>{let i=t.axis.getExtremes(),e=i.min,s=i.max,n=c(t.axis.minPointOffset,0);return h(e)&&h(s)&&t.value>=e-n&&t.value<=s+n&&!t.axis.options.isInternal})[0]},getFieldType:function(t,i){let e=p[t],s=typeof i;return l(e)&&(s=e),({string:"text",number:"number",boolean:"checkbox"})[s]}},{getAssignedAxis:g}=d,{isNumber:u,merge:m}=n(),y={lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&g(i.xAxis),s=i&&g(i.yAxis),n=this.chart.options.navigation;if(e&&s)return this.chart.addAnnotation(m({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{x:e.value,y:s.value,xAxis:e.axis.index,yAxis:s.axis.index},r:5}]},n.annotationsOptions,n.bindings.circleAnnotation.annotationsOptions))},steps:[function(t,i){let e,s=i.options.shapes,n=s&&s[0]&&s[0].point||{};if(u(n.xAxis)&&u(n.yAxis)){let i=this.chart.inverted,s=this.chart.xAxis[n.xAxis].toPixels(n.x),o=this.chart.yAxis[n.yAxis].toPixels(n.y);e=Math.max(Math.sqrt(Math.pow(i?o-t.chartX:s-t.chartX,2)+Math.pow(i?s-t.chartY:o-t.chartY,2)),5)}i.update({shapes:[{r:e}]})}]},ellipseAnnotation:{className:"highcharts-ellipse-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&g(i.xAxis),s=i&&g(i.yAxis),n=this.chart.options.navigation;if(e&&s)return this.chart.addAnnotation(m({langKey:"ellipse",type:"basicAnnotation",shapes:[{type:"ellipse",xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}],ry:1}]},n.annotationsOptions,n.bindings.ellipseAnnotation.annotationsOptions))},steps:[function(t,i){let e=i.shapes[0],s=e.getAbsolutePosition(e.points[1]);e.translatePoint(t.chartX-s.x,t.chartY-s.y,1),e.redraw(!1)},function(t,i){let e=i.shapes[0],s=e.getAbsolutePosition(e.points[0]),n=e.getAbsolutePosition(e.points[1]),o=e.getDistanceFromLine(s,n,t.chartX,t.chartY),a=e.getYAxis(),r=Math.abs(a.toValue(0)-a.toValue(o));e.setYRadius(r),e.redraw(!1)}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&g(i.xAxis),s=i&&g(i.yAxis);if(!e||!s)return;let n=e.value,o=s.value,a=e.axis.index,r=s.axis.index,l=this.chart.options.navigation;return this.chart.addAnnotation(m({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:a,yAxis:r,x:n,y:o},{xAxis:a,yAxis:r,x:n,y:o},{xAxis:a,yAxis:r,x:n,y:o},{xAxis:a,yAxis:r,x:n,y:o},{command:"Z"}]}]},l.annotationsOptions,l.bindings.rectangleAnnotation.annotationsOptions))},steps:[function(t,i){let e=i.options.shapes,s=e&&e[0]&&e[0].points||[],n=this.chart.pointer?.getCoordinates(t),o=n&&g(n.xAxis),a=n&&g(n.yAxis);if(o&&a){let t=o.value,e=a.value;s[1].x=t,s[2].x=t,s[2].y=e,s[3].y=e,i.update({shapes:[{points:s}]})}}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(t){let i=this.chart.pointer?.getCoordinates(t),e=i&&g(i.xAxis),s=i&&g(i.yAxis),n=this.chart.options.navigation;if(e&&s)return this.chart.addAnnotation(m({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}",overflow:"none",crop:!0},labels:[{point:{xAxis:e.axis.index,yAxis:s.axis.index,x:e.value,y:s.value}}]},n.annotationsOptions,n.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}},{setOptions:f}=n(),{format:v}=r(),{composed:b,doc:x,win:A}=n(),{getAssignedAxis:k,getFieldType:C}=d,{addEvent:w,attr:O,defined:N,fireEvent:L,isArray:T,isFunction:B,isNumber:E,isObject:S,merge:I,objectEach:z,pick:P,pushUnique:W}=n();function Y(){this.chart.navigationBindings&&this.chart.navigationBindings.deselectAnnotation()}function R(){this.navigationBindings&&this.navigationBindings.destroy()}function M(){let t=this.options;t&&t.navigation&&t.navigation.bindings&&(this.navigationBindings=new F(this,t.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())}function X(){let t=this.navigationBindings,i="highcharts-disabled-btn";if(this&&t){let e=!1;if(this.series.forEach(t=>{!t.options.isInternal&&t.visible&&(e=!0)}),this.navigationBindings&&this.navigationBindings.container&&this.navigationBindings.container[0]){let s=this.navigationBindings.container[0];z(t.boundClassNames,(t,n)=>{let o=s.querySelectorAll("."+n);if(o)for(let s=0;s<o.length;s++){let n=o[s],a=n.className;"normal"===t.noDataState?-1!==a.indexOf(i)&&n.classList.remove(i):e?-1!==a.indexOf(i)&&n.classList.remove(i):-1===a.indexOf(i)&&(n.className+=" "+i)}})}}}function H(){this.deselectAnnotation()}function U(){this.selectedButtonElement=null}function D(t){let i,e,s=t.prototype.defaultOptions.events&&t.prototype.defaultOptions.events.click;function n(t){let i=this,e=i.chart.navigationBindings,n=e.activeAnnotation;s&&s.call(i,t),n!==i?(e.deselectAnnotation(),e.activeAnnotation=i,i.setControlPointsVisibility(!0),L(e,"showPopup",{annotation:i,formType:"annotation-toolbar",options:e.annotationToFields(i),onSubmit:function(t){if("remove"===t.actionType)e.activeAnnotation=!1,e.chart.removeAnnotation(i);else{let s={};e.fieldsToOptions(t.fields,s),e.deselectAnnotation();let n=s.typeOptions;"measure"===i.options.type&&(n.crosshairY.enabled=0!==n.crosshairY.strokeWidth,n.crosshairX.enabled=0!==n.crosshairX.strokeWidth),i.update(s)}}})):L(e,"closePopup"),t.activeAnnotation=!0}I(!0,t.prototype.defaultOptions.events,{click:n,touchstart:function(t){i=t.touches[0].clientX,e=t.touches[0].clientY},touchend:function(t){i&&Math.sqrt(Math.pow(i-t.changedTouches[0].clientX,2)+Math.pow(e-t.changedTouches[0].clientY,2))>=4||n.call(this,t)}})}class F{static compose(t,i){W(b,"NavigationBindings")&&(w(t,"remove",Y),D(t),z(t.types,t=>{D(t)}),w(i,"destroy",R),w(i,"load",M),w(i,"render",X),w(F,"closePopup",H),w(F,"deselectButton",U),f(y))}constructor(t,i){this.boundClassNames=void 0,this.chart=t,this.options=i,this.eventsToUnbind=[],this.container=this.chart.container.getElementsByClassName(this.options.bindingsClassName||""),this.container.length||(this.container=x.getElementsByClassName(this.options.bindingsClassName||""))}getCoords(t){let i=this.chart.pointer?.getCoordinates(t);return[i&&k(i.xAxis),i&&k(i.yAxis)]}initEvents(){let t=this,i=t.chart,e=t.container,s=t.options;t.boundClassNames={},z(s.bindings||{},i=>{t.boundClassNames[i.className]=i}),[].forEach.call(e,i=>{t.eventsToUnbind.push(w(i,"click",e=>{let s=t.getButtonEvents(i,e);s&&!s.button.classList.contains("highcharts-disabled-btn")&&t.bindingsButtonClick(s.button,s.events,e)}))}),z(s.events||{},(i,e)=>{B(i)&&t.eventsToUnbind.push(w(t,e,i,{passive:!1}))}),t.eventsToUnbind.push(w(i.container,"click",function(e){!i.cancelClick&&i.isInsidePlot(e.chartX-i.plotLeft,e.chartY-i.plotTop,{visiblePlotOnly:!0})&&t.bindingsChartClick(this,e)})),t.eventsToUnbind.push(w(i.container,n().isTouchDevice?"touchmove":"mousemove",function(i){t.bindingsContainerMouseMove(this,i)},n().isTouchDevice?{passive:!1}:void 0))}initUpdate(){let t=this;o.compose(this.chart).navigation.addUpdate(i=>{t.update(i)})}bindingsButtonClick(t,i,e){let s=this.chart,n=s.renderer.boxWrapper,o=!0;this.selectedButtonElement&&(this.selectedButtonElement.classList===t.classList&&(o=!1),L(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&s.removeAnnotation(this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1)),o?(this.selectedButton=i,this.selectedButtonElement=t,L(this,"selectButton",{button:t}),i.init&&i.init.call(this,t,e),(i.start||i.steps)&&s.renderer.boxWrapper.addClass("highcharts-draw-mode")):(s.stockTools&&t.classList.remove("highcharts-active"),n.removeClass("highcharts-draw-mode"),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null)}bindingsChartClick(t,i){t=this.chart;let e=this.activeAnnotation,s=this.selectedButton,n=t.renderer.boxWrapper;e&&(e.cancelClick||i.activeAnnotation||!i.target.parentNode||function(t,i){let e=A.Element.prototype,s=e.matches||e.msMatchesSelector||e.webkitMatchesSelector,n=null;if(e.closest)n=e.closest.call(t,i);else do{if(s.call(t,i))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return n}(i.target,".highcharts-popup")?e.cancelClick&&setTimeout(()=>{e.cancelClick=!1},0):L(this,"closePopup")),s&&s.start&&(this.nextEvent?(this.nextEvent(i,this.currentUserDetails),this.steps&&(this.stepIndex++,s.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=s.steps[this.stepIndex]:(L(this,"deselectButton",{button:this.selectedButtonElement}),n.removeClass("highcharts-draw-mode"),s.end&&s.end.call(this,i,this.currentUserDetails),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null))):(this.currentUserDetails=s.start.call(this,i),this.currentUserDetails&&s.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=s.steps[this.stepIndex]):(L(this,"deselectButton",{button:this.selectedButtonElement}),n.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,s.end&&s.end.call(this,i,this.currentUserDetails))))}bindingsContainerMouseMove(t,i){this.mouseMoveEvent&&this.mouseMoveEvent(i,this.currentUserDetails)}fieldsToOptions(t,i){return z(t,(t,e)=>{let s=parseFloat(t),n=e.split("."),o=n.length-1;if(!E(s)||t.match(/px|em/g)||e.match(/format/g)||(t=s),"undefined"!==t){let e=i;n.forEach((i,s)=>{if("__proto__"!==i&&"constructor"!==i){let a=P(n[s+1],"");o===s?e[i]=t:(e[i]||(e[i]=a.match(/\d/g)?[]:{}),e=e[i])}})}}),i}deselectAnnotation(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)}annotationToFields(t){let i=t.options,e=F.annotationsEditable,s=e.nestedOptions,n=P(i.type,i.shapes&&i.shapes[0]&&i.shapes[0].type,i.labels&&i.labels[0]&&i.labels[0].type,"label"),o=F.annotationsNonEditable[i.langKey]||[],a={langKey:i.langKey,type:n};function r(i,e,n,a,l){let h;n&&N(i)&&-1===o.indexOf(e)&&((n.indexOf&&n.indexOf(e))>=0||n[e]||!0===n)&&(T(i)?(a[e]=[],i.forEach((t,i)=>{S(t)?(a[e][i]={},z(t,(t,n)=>{r(t,n,s[e],a[e][i],e)})):r(t,0,s[e],a[e],e)})):S(i)?(h={},T(a)?(a.push(h),h[e]={},h=h[e]):a[e]=h,z(i,(t,i)=>{r(t,i,0===e?n:s[e],h,e)})):"format"===e?a[e]=[v(i,t.labels[0].points[0]).toString(),"text"]:T(a)?a.push([i,C(l,i)]):a[e]=[i,C(e,i)])}return z(i,(t,o)=>{"typeOptions"===o?(a[o]={},z(i[o],(t,i)=>{r(t,i,s,a[o],i)})):r(t,o,e[n],a,o)}),a}getClickedClassNames(t,i){let e=i.target,s=[],n;for(;e&&e.tagName&&((n=O(e,"class"))&&(s=s.concat(n.split(" ").map(t=>[t,e]))),(e=e.parentNode)!==t););return s}getButtonEvents(t,i){let e,s=this;return this.getClickedClassNames(t,i).forEach(t=>{s.boundClassNames[t[0]]&&!e&&(e={events:s.boundClassNames[t[0]],button:t[1]})}),e}update(t){this.options=I(!0,this.options,t),this.removeEvents(),this.initEvents()}removeEvents(){this.eventsToUnbind.forEach(t=>t())}destroy(){this.removeEvents()}}F.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],label:["style"],style:["fontSize","color"],background:["fill","strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],ellipse:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background","crosshairY","crosshairX"],fibonacci:[],tunnel:["background","line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]},F.annotationsNonEditable={rectangle:["crosshairX","crosshairY","labelOptions"],ellipse:["labelOptions"],circle:["labelOptions"]};let K=t.default.Series;var V=e.n(K);let{getOptions:q}=n(),{getAssignedAxis:Z,getFieldType:_}=d,{defined:G,fireEvent:j,isNumber:J,uniqueKey:Q}=n(),$=["apo","ad","aroon","aroonoscillator","atr","ao","cci","chaikin","cmf","cmo","disparityindex","dmi","dpo","linearRegressionAngle","linearRegressionIntercept","linearRegressionSlope","klinger","macd","mfi","momentum","natr","obv","ppo","roc","rsi","slowstochastic","stochastic","trix","williamsr"],tt=["ad","cmf","klinger","mfi","obv","vbp","vwap"];function ti(t,i){let e=i.pointer?.getCoordinates(t),s,n,o=Number.MAX_VALUE,a;if(i.navigationBindings&&e&&(s=Z(e.xAxis),n=Z(e.yAxis)),!s||!n)return;let r=s.value,l=n.value;if(n.axis.series.forEach(i=>{if(i.points){let e=i.searchPoint(t,!0);e&&o>Math.abs(e.x-r)&&(o=Math.abs(e.x-r),a=e)}}),a&&a.x&&a.y)return{x:a.x,y:a.y,below:l<a.y,series:a.series,xAxis:a.series.xAxis.index||0,yAxis:a.series.yAxis.index||0}}let te={indicatorsWithAxes:$,indicatorsWithVolume:tt,addFlagFromForm:function(t){return function(i){let e=this,s=e.chart,n=s.stockTools,o=ti(i,s);if(!o)return;let a={x:o.x,y:o.y},r={type:"flags",onSeries:o.series.id,shape:t,data:[a],xAxis:o.xAxis,yAxis:o.yAxis,point:{events:{click:function(){let t=this,i=t.options;j(e,"showPopup",{point:t,formType:"annotation-toolbar",options:{langKey:"flags",type:"flags",title:[i.title,_("title",i.title)],name:[i.name,_("name",i.name)]},onSubmit:function(i){"remove"===i.actionType?t.remove():t.update(e.fieldsToOptions(i.fields,{}))}})}}}};n&&n.guiEnabled||s.addSeries(r),j(e,"showPopup",{formType:"flag",options:{langKey:"flags",type:"flags",title:["A",_("label","A")],name:["Flag A",_("label","Flag A")]},onSubmit:function(t){e.fieldsToOptions(t.fields,r.data[0]),s.addSeries(r)}})}},attractToPoint:ti,getAssignedAxis:Z,isNotNavigatorYAxis:function(t){return"highcharts-navigator-yaxis"!==t.userOptions.className},isPriceIndicatorEnabled:function(t){return t.some(t=>t.lastVisiblePrice||t.lastPrice)},manageIndicators:function(t){let i,e,s,n,o=this.chart,a={linkedTo:t.linkedTo,type:t.type};if("edit"===t.actionType)this.fieldsToOptions(t.fields,a),(n=o.get(t.seriesId))&&n.update(a,!1);else if("remove"===t.actionType){if((n=o.get(t.seriesId))&&(i=n.yAxis,n.linkedSeries&&n.linkedSeries.forEach(t=>{t.remove(!1)}),n.remove(!1),$.indexOf(n.type)>=0)){let t={height:i.options.height,top:i.options.top};i.remove(!1),this.resizeYAxes(t)}}else a.id=Q(),this.fieldsToOptions(t.fields,a),e=o.get(a.linkedTo),s=q().plotOptions,void 0!==e&&e instanceof V()&&"sum"===e.getDGApproximation()&&!G(s&&s[a.type]&&s.dataGrouping&&s.dataGrouping.approximation)&&(a.dataGrouping={approximation:"sum"}),$.indexOf(t.type)>=0?(a.yAxis=(i=o.addAxis({id:Q(),offset:0,opposite:!0,title:{text:""},tickPixelInterval:40,showLastLabel:!1,labels:{align:"left",y:-2}},!1,!1)).options.id,this.resizeYAxes()):a.yAxis=o.get(t.linkedTo).options.yAxis,tt.indexOf(t.type)>=0&&(a.params.volumeSeriesID=o.series.filter(function(t){return"column"===t.options.type})[0].options.id),o.addSeries(a,!1);j(this,"deselectButton",{button:this.selectedButtonElement}),o.redraw()},shallowArraysEqual:function(t,i){if(!G(t)||!G(i)||t.length!==i.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==i[e])return!1;return!0},updateHeight:function(t,i){let e=i.options.typeOptions,s=J(e.yAxis)&&this.chart.yAxis[e.yAxis];s&&e.points&&i.update({typeOptions:{height:s.toValue(t[s.horiz?"chartX":"chartY"])-(e.points[1].y||0)}})},updateNthPoint:function(t){return function(i,e){let s=e.options.typeOptions,n=J(s.xAxis)&&this.chart.xAxis[s.xAxis],o=J(s.yAxis)&&this.chart.yAxis[s.yAxis];n&&o&&(s.points.forEach((e,s)=>{s>=t&&(e.x=n.toValue(i[n.horiz?"chartX":"chartY"]),e.y=o.toValue(i[o.horiz?"chartX":"chartY"]))}),e.update({typeOptions:{points:s.points}}))}},updateRectSize:function(t,i){let e=i.chart,s=i.options.typeOptions,n=J(s.xAxis)&&e.xAxis[s.xAxis],o=J(s.yAxis)&&e.yAxis[s.yAxis];if(n&&o){let a=n.toValue(t[n.horiz?"chartX":"chartY"]),r=o.toValue(t[o.horiz?"chartX":"chartY"]),l=a-s.point.x,h=s.point.y-r;i.update({typeOptions:{background:{width:e.inverted?h:l,height:e.inverted?l:h}}})}}},{addFlagFromForm:ts,attractToPoint:tn,isNotNavigatorYAxis:to,isPriceIndicatorEnabled:ta,manageIndicators:tr,updateHeight:tl,updateNthPoint:th,updateRectSize:tc}=te,{fireEvent:tp,merge:td}=n(),tg={segment:{className:"highcharts-segment",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"segment",type:"crookedLine",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.segment.annotationsOptions);return this.chart.addAnnotation(n)},steps:[th(1)]},arrowSegment:{className:"highcharts-arrow-segment",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"arrowSegment",type:"crookedLine",typeOptions:{line:{markerEnd:"arrow"},xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.arrowSegment.annotationsOptions);return this.chart.addAnnotation(n)},steps:[th(1)]},ray:{className:"highcharts-ray",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"ray",type:"infinityLine",typeOptions:{type:"ray",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.ray.annotationsOptions);return this.chart.addAnnotation(n)},steps:[th(1)]},arrowRay:{className:"highcharts-arrow-ray",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"arrowRay",type:"infinityLine",typeOptions:{type:"ray",line:{markerEnd:"arrow"},xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.arrowRay.annotationsOptions);return this.chart.addAnnotation(n)},steps:[th(1)]},infinityLine:{className:"highcharts-infinity-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"infinityLine",type:"infinityLine",typeOptions:{type:"line",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.infinityLine.annotationsOptions);return this.chart.addAnnotation(n)},steps:[th(1)]},arrowInfinityLine:{className:"highcharts-arrow-infinity-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"arrowInfinityLine",type:"infinityLine",typeOptions:{type:"line",line:{markerEnd:"arrow"},xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value},{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.arrowInfinityLine.annotationsOptions);return this.chart.addAnnotation(n)},steps:[th(1)]},horizontalLine:{className:"highcharts-horizontal-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"horizontalLine",type:"infinityLine",draggable:"y",typeOptions:{type:"horizontalLine",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.horizontalLine.annotationsOptions);this.chart.addAnnotation(n)}},verticalLine:{className:"highcharts-vertical-line",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({langKey:"verticalLine",type:"infinityLine",draggable:"x",typeOptions:{type:"verticalLine",xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value}]}},s.annotationsOptions,s.bindings.verticalLine.annotationsOptions);this.chart.addAnnotation(n)}},crooked3:{className:"highcharts-crooked3",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"crooked3",type:"crookedLine",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings.crooked3.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),th(2)]},crooked5:{className:"highcharts-crooked5",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"crooked5",type:"crookedLine",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings.crooked5.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),th(2),th(3),th(4)]},elliott3:{className:"highcharts-elliott3",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"elliott3",type:"elliottWave",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n}]},labelOptions:{style:{color:"#666666"}}},o.annotationsOptions,o.bindings.elliott3.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),th(2),th(3)]},elliott5:{className:"highcharts-elliott5",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"elliott5",type:"elliottWave",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n},{x:s,y:n}]},labelOptions:{style:{color:"#666666"}}},o.annotationsOptions,o.bindings.elliott5.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),th(2),th(3),th(4),th(5)]},measureX:{className:"highcharts-measure-x",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"measure",type:"measure",typeOptions:{selectType:"x",xAxis:i.axis.index,yAxis:e.axis.index,point:{x:s,y:n},crosshairX:{strokeWidth:1,stroke:"#000000"},crosshairY:{enabled:!1,strokeWidth:0,stroke:"#000000"},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"}},labelOptions:{style:{color:"#666666"}}},o.annotationsOptions,o.bindings.measureX.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tc]},measureY:{className:"highcharts-measure-y",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"measure",type:"measure",typeOptions:{selectType:"y",xAxis:i.axis.index,yAxis:e.axis.index,point:{x:s,y:n},crosshairX:{enabled:!1,strokeWidth:0,stroke:"#000000"},crosshairY:{strokeWidth:1,stroke:"#000000"},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"}},labelOptions:{style:{color:"#666666"}}},o.annotationsOptions,o.bindings.measureY.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tc]},measureXY:{className:"highcharts-measure-xy",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"measure",type:"measure",typeOptions:{selectType:"xy",xAxis:i.axis.index,yAxis:e.axis.index,point:{x:s,y:n},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"},crosshairX:{strokeWidth:1,stroke:"#000000"},crosshairY:{strokeWidth:1,stroke:"#000000"}},labelOptions:{style:{color:"#666666"}}},o.annotationsOptions,o.bindings.measureXY.annotationsOptions);return this.chart.addAnnotation(a)},steps:[tc]},fibonacci:{className:"highcharts-fibonacci",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"fibonacci",type:"fibonacci",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n}]},labelOptions:{style:{color:"#666666"}}},o.annotationsOptions,o.bindings.fibonacci.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),tl]},parallelChannel:{className:"highcharts-parallel-channel",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"parallelChannel",type:"tunnel",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:s,y:n},{x:s,y:n}]}},o.annotationsOptions,o.bindings.parallelChannel.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),tl]},pitchfork:{className:"highcharts-pitchfork",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=i.value,n=e.value,o=this.chart.options.navigation,a=td({langKey:"pitchfork",type:"pitchfork",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value,y:e.value,controlPoint:{style:{fill:"#f21313"}}},{x:s,y:n},{x:s,y:n}],innerBackground:{fill:"rgba(100, 170, 255, 0.8)"}},shapeOptions:{strokeWidth:2}},o.annotationsOptions,o.bindings.pitchfork.annotationsOptions);return this.chart.addAnnotation(a)},steps:[th(1),th(2)]},verticalCounter:{className:"highcharts-vertical-counter",start:function(t){let i=tn(t,this.chart);if(!i)return;this.verticalCounter=this.verticalCounter||0;let e=this.chart.options.navigation,s=td({langKey:"verticalCounter",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40,text:this.verticalCounter.toString()}},labelOptions:{style:{color:"#666666",fontSize:"0.7em"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},e.annotationsOptions,e.bindings.verticalCounter.annotationsOptions),n=this.chart.addAnnotation(s);this.verticalCounter++,n.options.events.click.call(n,{})}},timeCycles:{className:"highcharts-time-cycles",start:function(t){let i=tn(t,this.chart);if(!i)return;let e=this.chart.options.navigation,s=td({langKey:"timeCycles",type:"timeCycles",typeOptions:{xAxis:i.xAxis,yAxis:i.yAxis,points:[{x:i.x},{x:i.x}],line:{stroke:"rgba(0, 0, 0, 0.75)",fill:"transparent",strokeWidth:2}}},e.annotationsOptions,e.bindings.timeCycles.annotationsOptions),n=this.chart.addAnnotation(s);return n.options.events.click.call(n,{}),n},steps:[th(1)]},verticalLabel:{className:"highcharts-vertical-label",start:function(t){let i=tn(t,this.chart);if(!i)return;let e=this.chart.options.navigation,s=td({langKey:"verticalLabel",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40}},labelOptions:{style:{color:"#666666",fontSize:"0.7em"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},e.annotationsOptions,e.bindings.verticalLabel.annotationsOptions),n=this.chart.addAnnotation(s);n.options.events.click.call(n,{})}},verticalArrow:{className:"highcharts-vertical-arrow",start:function(t){let i=tn(t,this.chart);if(!i)return;let e=this.chart.options.navigation,s=td({langKey:"verticalArrow",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40,format:" "},connector:{fill:"none",stroke:i.below?"#f21313":"#06b535"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},e.annotationsOptions,e.bindings.verticalArrow.annotationsOptions),n=this.chart.addAnnotation(s);n.options.events.click.call(n,{})}},fibonacciTimeZones:{className:"highcharts-fibonacci-time-zones",start:function(t){let[i,e]=this.getCoords(t);if(!i||!e)return;let s=this.chart.options.navigation,n=td({type:"fibonacciTimeZones",langKey:"fibonacciTimeZones",typeOptions:{xAxis:i.axis.index,yAxis:e.axis.index,points:[{x:i.value}]}},s.annotationsOptions,s.bindings.fibonacciTimeZones.annotationsOptions);return this.chart.addAnnotation(n)},steps:[function(t,i){let e=i.options.typeOptions.points,s=e&&e[0].x,[n,o]=this.getCoords(t);n&&o&&i.update({typeOptions:{xAxis:n.axis.index,yAxis:o.axis.index,points:[{x:s},{x:n.value}]}})}]},flagCirclepin:{className:"highcharts-flag-circlepin",start:ts("circlepin")},flagDiamondpin:{className:"highcharts-flag-diamondpin",start:ts("flag")},flagSquarepin:{className:"highcharts-flag-squarepin",start:ts("squarepin")},flagSimplepin:{className:"highcharts-flag-simplepin",start:ts("nopin")},zoomX:{className:"highcharts-zoom-x",init:function(t){this.chart.update({chart:{zooming:{type:"x"}}}),tp(this,"deselectButton",{button:t})}},zoomY:{className:"highcharts-zoom-y",init:function(t){this.chart.update({chart:{zooming:{type:"y"}}}),tp(this,"deselectButton",{button:t})}},zoomXY:{className:"highcharts-zoom-xy",init:function(t){this.chart.update({chart:{zooming:{type:"xy"}}}),tp(this,"deselectButton",{button:t})}},seriesTypeLine:{className:"highcharts-series-type-line",init:function(t){this.chart.series[0].update({type:"line",useOhlcData:!0}),tp(this,"deselectButton",{button:t})}},seriesTypeOhlc:{className:"highcharts-series-type-ohlc",init:function(t){this.chart.series[0].update({type:"ohlc"}),tp(this,"deselectButton",{button:t})}},seriesTypeCandlestick:{className:"highcharts-series-type-candlestick",init:function(t){this.chart.series[0].update({type:"candlestick"}),tp(this,"deselectButton",{button:t})}},seriesTypeHeikinAshi:{className:"highcharts-series-type-heikinashi",init:function(t){this.chart.series[0].update({type:"heikinashi"}),tp(this,"deselectButton",{button:t})}},seriesTypeHLC:{className:"highcharts-series-type-hlc",init:function(t){this.chart.series[0].update({type:"hlc",useOhlcData:!0}),tp(this,"deselectButton",{button:t})}},seriesTypeHollowCandlestick:{className:"highcharts-series-type-hollowcandlestick",init:function(t){this.chart.series[0].update({type:"hollowcandlestick"}),tp(this,"deselectButton",{button:t})}},fullScreen:{className:"highcharts-full-screen",noDataState:"normal",init:function(t){this.chart.fullscreen&&this.chart.fullscreen.toggle(),tp(this,"deselectButton",{button:t})}},currentPriceIndicator:{className:"highcharts-current-price-indicator",init:function(t){let i=this.chart,e=i.series,s=i.stockTools,n=ta(i.series);s&&s.guiEnabled&&(e.forEach(function(t){t.update({lastPrice:{enabled:!n},lastVisiblePrice:{enabled:!n,label:{enabled:!0}}},!1)}),i.redraw()),tp(this,"deselectButton",{button:t})}},indicators:{className:"highcharts-indicators",init:function(){let t=this;tp(t,"showPopup",{formType:"indicators",options:{},onSubmit:function(i){tr.call(t,i)}})}},toggleAnnotations:{className:"highcharts-toggle-annotations",init:function(t){let i=this.chart,e=i.stockTools,s=e.getIconsURL();this.toggledAnnotations=!this.toggledAnnotations,(i.annotations||[]).forEach(function(t){t.setVisibility(!this.toggledAnnotations)},this),e&&e.guiEnabled&&(this.toggledAnnotations?t.firstChild.style["background-image"]='url("'+s+'annotations-hidden.svg")':t.firstChild.style["background-image"]='url("'+s+'annotations-visible.svg")'),tp(this,"deselectButton",{button:t})}},saveChart:{className:"highcharts-save-chart",noDataState:"normal",init:function(t){let i=this.chart,e=[],s=[],o=[],a=[];i.annotations.forEach(function(t,i){e[i]=t.userOptions}),i.series.forEach(function(t){t.is("sma")?s.push(t.userOptions):"flags"===t.type&&o.push(t.userOptions)}),i.yAxis.forEach(function(t){to(t)&&a.push(t.options)}),n().win.localStorage.setItem("highcharts-chart",JSON.stringify({annotations:e,indicators:s,flags:o,yAxes:a})),tp(this,"deselectButton",{button:t})}}},tu={lang:{stockTools:{gui:{simpleShapes:"Simple shapes",lines:"Lines",crookedLines:"Crooked lines",measure:"Measure",advanced:"Advanced",toggleAnnotations:"Toggle annotations",verticalLabels:"Vertical labels",flags:"Flags",zoomChange:"Zoom change",typeChange:"Type change",saveChart:"Save chart",indicators:"Indicators",currentPriceIndicator:"Current Price Indicators",zoomX:"Zoom X",zoomY:"Zoom Y",zoomXY:"Zooom XY",fullScreen:"Fullscreen",typeOHLC:"OHLC",typeLine:"Line",typeCandlestick:"Candlestick",typeHLC:"HLC",typeHollowCandlestick:"Hollow Candlestick",typeHeikinAshi:"Heikin Ashi",circle:"Circle",ellipse:"Ellipse",label:"Label",rectangle:"Rectangle",flagCirclepin:"Flag circle",flagDiamondpin:"Flag diamond",flagSquarepin:"Flag square",flagSimplepin:"Flag simple",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowInfinityLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",infinityLine:"Infinity line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",fibonacciTimeZones:"Fibonacci Time Zones",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",timeCycles:"Time Cycles"}},navigation:{popup:{circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowInfinityLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",fibonacciTimeZones:"Fibonacci Time Zones",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",infinityLine:"Infinity line",measure:"Measure",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",timeCycles:"Time Cycles",flags:"Flags",addButton:"Add",saveButton:"Save",editButton:"Edit",removeButton:"Remove",series:"Series",volume:"Volume",connector:"Connector",innerBackground:"Inner background",outerBackground:"Outer background",crosshairX:"Crosshair X",crosshairY:"Crosshair Y",tunnel:"Tunnel",background:"Background",noFilterMatch:"No match",searchIndicators:"Search Indicators",clearFilter:"✕ clear filter",index:"Index",period:"Period",periods:"Periods",standardDeviation:"Standard deviation",periodTenkan:"Tenkan period",periodSenkouSpanB:"Senkou Span B period",periodATR:"ATR period",multiplierATR:"ATR multiplier",shortPeriod:"Short period",longPeriod:"Long period",signalPeriod:"Signal period",decimals:"Decimals",algorithm:"Algorithm",topBand:"Top band",bottomBand:"Bottom band",initialAccelerationFactor:"Initial acceleration factor",maxAccelerationFactor:"Max acceleration factor",increment:"Increment",multiplier:"Multiplier",ranges:"Ranges",highIndex:"High index",lowIndex:"Low index",deviation:"Deviation",xAxisUnit:"x-axis unit",factor:"Factor",fastAvgPeriod:"Fast average period",slowAvgPeriod:"Slow average period",average:"Average",indicatorAliases:{abands:["Acceleration Bands"],bb:["Bollinger Bands"],dema:["Double Exponential Moving Average"],ema:["Exponential Moving Average"],ikh:["Ichimoku Kinko Hyo"],keltnerchannels:["Keltner Channels"],linearRegression:["Linear Regression"],pivotpoints:["Pivot Points"],pc:["Price Channel"],priceenvelopes:["Price Envelopes"],psar:["Parabolic SAR"],sma:["Simple Moving Average"],supertrend:["Super Trend"],tema:["Triple Exponential Moving Average"],vbp:["Volume by Price"],vwap:["Volume Weighted Moving Average"],wma:["Weighted Moving Average"],zigzag:["Zig Zag"],apo:["Absolute price indicator"],ad:["Accumulation/Distribution"],aroon:["Aroon"],aroonoscillator:["Aroon oscillator"],atr:["Average True Range"],ao:["Awesome oscillator"],cci:["Commodity Channel Index"],chaikin:["Chaikin"],cmf:["Chaikin Money Flow"],cmo:["Chande Momentum Oscillator"],disparityindex:["Disparity Index"],dmi:["Directional Movement Index"],dpo:["Detrended price oscillator"],klinger:["Klinger Oscillator"],linearRegressionAngle:["Linear Regression Angle"],linearRegressionIntercept:["Linear Regression Intercept"],linearRegressionSlope:["Linear Regression Slope"],macd:["Moving Average Convergence Divergence"],mfi:["Money Flow Index"],momentum:["Momentum"],natr:["Normalized Average True Range"],obv:["On-Balance Volume"],ppo:["Percentage Price oscillator"],roc:["Rate of Change"],rsi:["Relative Strength Index"],slowstochastic:["Slow Stochastic"],stochastic:["Stochastic"],trix:["TRIX"],williamsr:["Williams %R"]}}}},stockTools:{gui:{enabled:!0,className:"highcharts-bindings-wrapper",toolbarClassName:"stocktools-toolbar",buttons:["indicators","separator","simpleShapes","lines","crookedLines","measure","advanced","toggleAnnotations","separator","verticalLabels","flags","separator","zoomChange","fullScreen","typeChange","separator","currentPriceIndicator","saveChart"],definitions:{separator:{elementType:"span",symbol:"separator.svg"},simpleShapes:{items:["label","circle","ellipse","rectangle"],circle:{symbol:"circle.svg"},ellipse:{symbol:"ellipse.svg"},rectangle:{symbol:"rectangle.svg"},label:{symbol:"label.svg"}},flags:{items:["flagCirclepin","flagDiamondpin","flagSquarepin","flagSimplepin"],flagSimplepin:{symbol:"flag-basic.svg"},flagDiamondpin:{symbol:"flag-diamond.svg"},flagSquarepin:{symbol:"flag-trapeze.svg"},flagCirclepin:{symbol:"flag-elipse.svg"}},lines:{items:["segment","arrowSegment","ray","arrowRay","line","arrowInfinityLine","horizontalLine","verticalLine"],segment:{symbol:"segment.svg"},arrowSegment:{symbol:"arrow-segment.svg"},ray:{symbol:"ray.svg"},arrowRay:{symbol:"arrow-ray.svg"},line:{symbol:"line.svg"},arrowInfinityLine:{symbol:"arrow-line.svg"},verticalLine:{symbol:"vertical-line.svg"},horizontalLine:{symbol:"horizontal-line.svg"}},crookedLines:{items:["elliott3","elliott5","crooked3","crooked5"],crooked3:{symbol:"crooked-3.svg"},crooked5:{symbol:"crooked-5.svg"},elliott3:{symbol:"elliott-3.svg"},elliott5:{symbol:"elliott-5.svg"}},verticalLabels:{items:["verticalCounter","verticalLabel","verticalArrow"],verticalCounter:{symbol:"vertical-counter.svg"},verticalLabel:{symbol:"vertical-label.svg"},verticalArrow:{symbol:"vertical-arrow.svg"}},advanced:{items:["fibonacci","fibonacciTimeZones","pitchfork","parallelChannel","timeCycles"],pitchfork:{symbol:"pitchfork.svg"},fibonacci:{symbol:"fibonacci.svg"},fibonacciTimeZones:{symbol:"fibonacci-timezone.svg"},parallelChannel:{symbol:"parallel-channel.svg"},timeCycles:{symbol:"time-cycles.svg"}},measure:{items:["measureXY","measureX","measureY"],measureX:{symbol:"measure-x.svg"},measureY:{symbol:"measure-y.svg"},measureXY:{symbol:"measure-xy.svg"}},toggleAnnotations:{symbol:"annotations-visible.svg"},currentPriceIndicator:{symbol:"current-price-show.svg"},indicators:{symbol:"indicators.svg"},zoomChange:{items:["zoomX","zoomY","zoomXY"],zoomX:{symbol:"zoom-x.svg"},zoomY:{symbol:"zoom-y.svg"},zoomXY:{symbol:"zoom-xy.svg"}},typeChange:{items:["typeOHLC","typeLine","typeCandlestick","typeHollowCandlestick","typeHLC","typeHeikinAshi"],typeOHLC:{symbol:"series-ohlc.svg"},typeLine:{symbol:"series-line.svg"},typeCandlestick:{symbol:"series-candlestick.svg"},typeHLC:{symbol:"series-hlc.svg"},typeHeikinAshi:{symbol:"series-heikin-ashi.svg"},typeHollowCandlestick:{symbol:"series-hollow-candlestick.svg"}},fullScreen:{symbol:"fullscreen.svg"},saveChart:{symbol:"save-chart.svg"}},visible:!0}}},{setOptions:tm}=n(),{getAssignedAxis:ty}=d,{isNotNavigatorYAxis:tf,isPriceIndicatorEnabled:tv}=te,{correctFloat:tb,defined:tx,isNumber:tA,pick:tk}=n();function tC(t,i,e,s){let n=0,o,a,r;function l(t){return tx(t)&&!tA(t)&&t.match("%")}return s&&(r=tb(parseFloat(s.top)/100),a=tb(parseFloat(s.height)/100)),{positions:t.map((s,h)=>{let c=tb(l(s.options.height)?parseFloat(s.options.height)/100:s.height/i),p=tb(l(s.options.top)?parseFloat(s.options.top)/100:(s.top-s.chart.plotTop)/i);return a?(p>r&&(p-=a),n=Math.max(n,(p||0)+(c||0))):(tA(c)||(c=t[h-1].series.every(t=>t.is("sma"))?o:e/100),tA(p)||(p=n),o=c,n=tb(Math.max(n,(p||0)+(c||0)))),{height:100*c,top:100*p}}),allAxesHeight:n}}function tw(t){let i=[];return t.forEach(function(e,s){let n=t[s+1];n?i[s]={enabled:!0,controlledAxis:{next:[tk(n.options.id,n.index)]}}:i[s]={enabled:!1}}),i}function tO(t,i,e,s){return t.forEach(function(n,o){let a=t[o-1];n.top=a?tb(a.height+a.top):0,e&&(n.height=tb(n.height+s*i))}),t}function tN(t){let i=this.chart,e=i.yAxis.filter(tf),s=i.plotHeight,{positions:n,allAxesHeight:o}=this.getYAxisPositions(e,s,20,t),a=this.getYAxisResizers(e);!t&&o<=tb(1)?n[n.length-1]={height:20,top:tb(100*o-20)}:n.forEach(function(t){t.height=t.height/(100*o)*100,t.top=t.top/(100*o)*100}),n.forEach(function(t,i){e[i].update({height:t.height+"%",top:t.top+"%",resize:a[i],offset:0},!1)})}let tL=t.default.AST;var tT=e.n(tL);let{addEvent:tB,createElement:tE,css:tS,defined:tI,fireEvent:tz,getStyle:tP,isArray:tW,merge:tY,pick:tR}=n(),{shallowArraysEqual:tM}=te;class tX{constructor(t,i,e){this.width=0,this.isDirty=!1,this.chart=e,this.options=t,this.lang=i,this.iconsURL=this.getIconsURL(),this.guiEnabled=t.enabled,this.visible=tR(t.visible,!0),this.guiClassName=t.className,this.toolbarClassName=t.toolbarClassName,this.eventsToUnbind=[],this.guiEnabled&&(this.createContainer(),this.createButtons(),this.showHideNavigation()),tz(this,"afterInit")}createButtons(){let t=this.lang,i=this.options,e=this.toolbar,s=i.buttons,n=i.definitions,o=e.childNodes;this.buttonList=s,s.forEach(i=>{let s=this.addButton(e,n,i,t);this.eventsToUnbind.push(tB(s.buttonWrapper,"click",()=>this.eraseActiveButtons(o,s.buttonWrapper))),tW(n[i].items)&&this.addSubmenu(s,n[i])})}addSubmenu(t,i){let e=t.submenuArrow,s=t.buttonWrapper,n=tP(s,"width"),o=this.wrapper,a=this.listWrapper,r=this.toolbar.childNodes,l=this.submenu=tE("ul",{className:"highcharts-submenu-wrapper"},void 0,s);this.addSubmenuItems(s,i),this.eventsToUnbind.push(tB(e,"click",t=>{if(t.stopPropagation(),this.eraseActiveButtons(r,s),s.className.indexOf("highcharts-current")>=0)a.style.width=a.startWidth+"px",s.classList.remove("highcharts-current"),l.style.display="none";else{l.style.display="block";let t=l.offsetHeight-s.offsetHeight-3;l.offsetHeight+s.offsetTop>o.offsetHeight&&s.offsetTop>t||(t=0),tS(l,{top:-t+"px",left:n+3+"px"}),s.className+=" highcharts-current",a.startWidth=o.offsetWidth,a.style.width=a.startWidth+tP(a,"padding-left")+l.offsetWidth+3+"px"}}))}addSubmenuItems(t,i){let e,s=this,n=this.submenu,o=this.lang,a=this.listWrapper;i.items.forEach(r=>{e=this.addButton(n,i,r,o),this.eventsToUnbind.push(tB(e.mainButton,"click",function(){s.switchSymbol(this,t,!0),a.style.width=a.startWidth+"px",n.style.display="none"}))});let r=n.querySelectorAll("li > .highcharts-menu-item-btn")[0];this.switchSymbol(r,!1)}eraseActiveButtons(t,i,e){[].forEach.call(t,t=>{t!==i&&(t.classList.remove("highcharts-current"),t.classList.remove("highcharts-active"),(e=t.querySelectorAll(".highcharts-submenu-wrapper")).length>0&&(e[0].style.display="none"))})}addButton(t,i,e,s={}){let n=i[e],o=n.items,a=tX.prototype.classMapping,r=n.className||"",l=tE("li",{className:tR(a[e],"")+" "+r,title:s[e]||e},void 0,t),h=tE(n.elementType||"button",{className:"highcharts-menu-item-btn"},void 0,l);if(o&&o.length){let t=tE("button",{className:"highcharts-submenu-item-arrow highcharts-arrow-right"},void 0,l);return t.style.backgroundImage="url("+this.iconsURL+"arrow-bottom.svg)",{buttonWrapper:l,mainButton:h,submenuArrow:t}}return h.style.backgroundImage="url("+this.iconsURL+n.symbol+")",{buttonWrapper:l,mainButton:h}}addNavigation(){let t=this.wrapper;this.arrowWrapper=tE("div",{className:"highcharts-arrow-wrapper"}),this.arrowUp=tE("div",{className:"highcharts-arrow-up"},void 0,this.arrowWrapper),this.arrowUp.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",this.arrowDown=tE("div",{className:"highcharts-arrow-down"},void 0,this.arrowWrapper),this.arrowDown.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",t.insertBefore(this.arrowWrapper,t.childNodes[0]),this.scrollButtons()}scrollButtons(){let t=this.wrapper,i=this.toolbar,e=.1*t.offsetHeight,s=0;this.eventsToUnbind.push(tB(this.arrowUp,"click",()=>{s>0&&(s-=e,i.style.marginTop=-s+"px")})),this.eventsToUnbind.push(tB(this.arrowDown,"click",()=>{t.offsetHeight+s<=i.offsetHeight+e&&(s+=e,i.style.marginTop=-s+"px")}))}createContainer(){let t,i,e=this.chart,s=this.options,n=e.container,o=e.options.navigation,a=o?.bindingsClassName,r=this,l=this.wrapper=tE("div",{className:"highcharts-stocktools-wrapper "+s.className+" "+a});n.appendChild(l),this.showHideBtn=tE("div",{className:"highcharts-toggle-toolbar highcharts-arrow-left"},void 0,l),this.eventsToUnbind.push(tB(this.showHideBtn,"click",()=>{this.update({gui:{visible:!r.visible}})})),["mousedown","mousemove","click","touchstart"].forEach(t=>{tB(l,t,t=>t.stopPropagation())}),tB(l,"mouseover",t=>e.pointer?.onContainerMouseLeave(t)),this.toolbar=i=tE("ul",{className:"highcharts-stocktools-toolbar "+s.toolbarClassName}),this.listWrapper=t=tE("div",{className:"highcharts-menu-wrapper"}),l.insertBefore(t,l.childNodes[0]),t.insertBefore(i,t.childNodes[0]),this.showHideToolbar(),this.addNavigation()}showHideNavigation(){this.visible&&this.toolbar.offsetHeight>this.wrapper.offsetHeight-50?this.arrowWrapper.style.display="block":(this.toolbar.style.marginTop="0px",this.arrowWrapper.style.display="none")}showHideToolbar(){let t=this.wrapper,i=this.listWrapper,e=this.submenu,s=this.showHideBtn,n=this.visible;s.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",n?(t.style.height="100%",i.classList.remove("highcharts-hide"),s.classList.remove("highcharts-arrow-right"),s.style.top=tP(i,"padding-top")+"px",s.style.left=t.offsetWidth+tP(i,"padding-left")+"px"):(e&&(e.style.display="none"),s.style.left="0px",n=this.visible=!1,i.classList.add("highcharts-hide"),s.classList.add("highcharts-arrow-right"),t.style.height=s.offsetHeight+"px")}switchSymbol(t,i){let e=t.parentNode,s=e.className,n=e.parentNode.parentNode;!(s.indexOf("highcharts-disabled-btn")>-1)&&(n.className="",s&&n.classList.add(s.trim()),n.querySelectorAll(".highcharts-menu-item-btn")[0].style.backgroundImage=t.style.backgroundImage,i&&this.toggleButtonActiveClass(n))}toggleButtonActiveClass(t){let i=t.classList;i.contains("highcharts-active")?i.remove("highcharts-active"):i.add("highcharts-active")}unselectAllButtons(t){let i=t.parentNode.querySelectorAll(".highcharts-active");[].forEach.call(i,i=>{i!==t&&i.classList.remove("highcharts-active")})}update(t,i){this.isDirty=!!t.gui.definitions,tY(!0,this.chart.options.stockTools,t),tY(!0,this.options,t.gui),this.visible=tR(this.options.visible&&this.options.enabled,!0),this.chart.navigationBindings&&this.chart.navigationBindings.update(),this.chart.isDirtyBox=!0,tR(i,!0)&&this.chart.redraw()}destroy(){let t=this.wrapper,i=t&&t.parentNode;this.eventsToUnbind.forEach(t=>t()),i&&i.removeChild(t)}redraw(){if(this.options.enabled!==this.guiEnabled)this.handleGuiEnabledChange();else{if(!this.guiEnabled)return;this.updateClassNames(),this.updateButtons(),this.updateVisibility(),this.showHideNavigation(),this.showHideToolbar()}}handleGuiEnabledChange(){!1===this.options.enabled&&(this.destroy(),this.visible=!1),!0===this.options.enabled&&(this.createContainer(),this.createButtons()),this.guiEnabled=this.options.enabled}updateClassNames(){this.options.className!==this.guiClassName&&(this.guiClassName&&this.wrapper.classList.remove(this.guiClassName),this.options.className&&this.wrapper.classList.add(this.options.className),this.guiClassName=this.options.className),this.options.toolbarClassName!==this.toolbarClassName&&(this.toolbarClassName&&this.toolbar.classList.remove(this.toolbarClassName),this.options.toolbarClassName&&this.toolbar.classList.add(this.options.toolbarClassName),this.toolbarClassName=this.options.toolbarClassName)}updateButtons(){(!tM(this.options.buttons,this.buttonList)||this.isDirty)&&(this.toolbar.innerHTML=tT().emptyHTML,this.createButtons())}updateVisibility(){tI(this.options.visible)&&(this.visible=this.options.visible)}getIconsURL(){return this.chart.options.navigation.iconsURL||this.options.iconsURL||"https://code.highcharts.com/12.2.0/gfx/stock-icons/"}}tX.prototype.classMapping={circle:"highcharts-circle-annotation",ellipse:"highcharts-ellipse-annotation",rectangle:"highcharts-rectangle-annotation",label:"highcharts-label-annotation",segment:"highcharts-segment",arrowSegment:"highcharts-arrow-segment",ray:"highcharts-ray",arrowRay:"highcharts-arrow-ray",line:"highcharts-infinity-line",arrowInfinityLine:"highcharts-arrow-infinity-line",verticalLine:"highcharts-vertical-line",horizontalLine:"highcharts-horizontal-line",crooked3:"highcharts-crooked3",crooked5:"highcharts-crooked5",elliott3:"highcharts-elliott3",elliott5:"highcharts-elliott5",pitchfork:"highcharts-pitchfork",fibonacci:"highcharts-fibonacci",fibonacciTimeZones:"highcharts-fibonacci-time-zones",parallelChannel:"highcharts-parallel-channel",measureX:"highcharts-measure-x",measureY:"highcharts-measure-y",measureXY:"highcharts-measure-xy",timeCycles:"highcharts-time-cycles",verticalCounter:"highcharts-vertical-counter",verticalLabel:"highcharts-vertical-label",verticalArrow:"highcharts-vertical-arrow",currentPriceIndicator:"highcharts-current-price-indicator",indicators:"highcharts-indicators",flagCirclepin:"highcharts-flag-circlepin",flagDiamondpin:"highcharts-flag-diamondpin",flagSquarepin:"highcharts-flag-squarepin",flagSimplepin:"highcharts-flag-simplepin",zoomX:"highcharts-zoom-x",zoomY:"highcharts-zoom-y",zoomXY:"highcharts-zoom-xy",typeLine:"highcharts-series-type-line",typeOHLC:"highcharts-series-type-ohlc",typeHLC:"highcharts-series-type-hlc",typeCandlestick:"highcharts-series-type-candlestick",typeHollowCandlestick:"highcharts-series-type-hollowcandlestick",typeHeikinAshi:"highcharts-series-type-heikinashi",fullScreen:"highcharts-full-screen",toggleAnnotations:"highcharts-toggle-annotations",saveChart:"highcharts-save-chart",separator:"highcharts-separator"};let{setOptions:tH}=n(),{addEvent:tU,getStyle:tD,merge:tF,pick:tK}=n();function tV(t){let i=this.options,e=i.lang,s=tF(i.stockTools&&i.stockTools.gui,t&&t.gui),n=e&&e.stockTools&&e.stockTools.gui;this.stockTools=new tX(s,n,this),this.stockTools.guiEnabled&&(this.isDirtyBox=!0)}function tq(){this.setStockTools()}function tZ(){this.stockTools&&(this.stockTools.redraw(),function(t){if(t.stockTools?.guiEnabled){let i=t.options.chart,e=t.stockTools.listWrapper,s=e&&(e.startWidth+tD(e,"padding-left")+tD(e,"padding-right")||e.offsetWidth);t.stockTools.width=s;let n=!1;if(s<t.plotWidth){let e=tK(i.spacingLeft,i.spacing&&i.spacing[3],0)+s,o=e-t.spacingBox.x;t.spacingBox.x=e,t.spacingBox.width-=o,n=!0}else 0===s&&(n=!0);s!==t.stockTools.prevOffsetWidth&&(t.stockTools.prevOffsetWidth=s,n&&(t.isDirtyLegend=!0))}}(this))}function t_(){this.stockTools&&this.stockTools.destroy()}function tG(){let t=this.stockTools?.visible&&this.stockTools.guiEnabled?this.stockTools.width:0;t&&t<this.plotWidth&&(this.plotLeft+=t,this.spacing[3]+=t)}function tj(){let t=this.stockTools,i=t&&t.toolbar&&t.toolbar.querySelector(".highcharts-current-price-indicator");t&&this.navigationBindings&&this.options.series&&i&&(this.navigationBindings.utils?.isPriceIndicatorEnabled?.(this.series)?i.firstChild.style["background-image"]='url("'+t.getIconsURL()+'current-price-hide.svg")':i.firstChild.style["background-image"]='url("'+t.getIconsURL()+'current-price-show.svg")')}function tJ(t){let i=this.chart.stockTools;if(i&&i.guiEnabled){let i=t.button;i.parentNode.className.indexOf("highcharts-submenu-wrapper")>=0&&(i=i.parentNode.parentNode),i.classList.remove("highcharts-active")}}function tQ(t){let i=this.chart.stockTools;if(i&&i.guiEnabled){let e=t.button;i.unselectAllButtons(t.button),e.parentNode.className.indexOf("highcharts-submenu-wrapper")>=0&&(e=e.parentNode.parentNode),i.toggleButtonActiveClass(e)}}e.d({},{});let t$=n();t$.NavigationBindings=t$.NavigationBindings||F,t$.Toolbar=tX,({compose:function(t){let i=t.prototype;i.utils?.manageIndicators||(i.getYAxisPositions=tC,i.getYAxisResizers=tw,i.recalculateYAxisPositions=tO,i.resizeYAxes=tN,i.utils=i.utils||{},i.utils.indicatorsWithAxes=te.indicatorsWithAxes,i.utils.indicatorsWithVolume=te.indicatorsWithVolume,i.utils.getAssignedAxis=ty,i.utils.isPriceIndicatorEnabled=tv,i.utils.manageIndicators=te.manageIndicators,tm(tu),tm({navigation:{bindings:tg}}))}}).compose(t$.NavigationBindings),({compose:function(t,i){let e=t.prototype;e.setStockTools||(tU(t,"afterGetContainer",tq),tU(t,"beforeRedraw",tZ),tU(t,"beforeRender",tZ),tU(t,"destroy",t_),tU(t,"getMargins",tG,{order:0}),tU(t,"render",tj),e.setStockTools=tV,tU(i,"deselectButton",tJ),tU(i,"selectButton",tQ),tH(tu))}}).compose(t$.Chart,t$.NavigationBindings);let t0=n();export{t0 as default};