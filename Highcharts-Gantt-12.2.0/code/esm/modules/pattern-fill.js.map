{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/pattern-fill\n * @requires highcharts\n *\n * Module for adding patterns and images as point fills.\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON><PERSON>, <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Extensions/PatternFill.js\n/* *\n *\n *  Module for using patterns or images as point fills.\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Torstein Hønsi, Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (external_highcharts_src_js_default_default());\n\nconst { getOptions } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, defined, erase, extend, merge, pick, removeEvent, wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst patterns = createPatterns();\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction compose(ChartClass, SeriesClass, SVGRendererClass) {\n    const PointClass = SeriesClass.prototype.pointClass, pointProto = PointClass.prototype;\n    if (!pointProto.calculatePatternDimensions) {\n        addEvent(ChartClass, 'endResize', onChartEndResize);\n        addEvent(ChartClass, 'redraw', onChartRedraw);\n        extend(pointProto, {\n            calculatePatternDimensions: pointCalculatePatternDimensions\n        });\n        addEvent(PointClass, 'afterInit', onPointAfterInit);\n        addEvent(SeriesClass, 'render', onSeriesRender);\n        wrap(SeriesClass.prototype, 'getColor', wrapSeriesGetColor);\n        // Pattern scale corrections\n        addEvent(SeriesClass, 'afterRender', onPatternScaleCorrection);\n        addEvent(SeriesClass, 'mapZoomComplete', onPatternScaleCorrection);\n        extend(SVGRendererClass.prototype, {\n            addPattern: rendererAddPattern\n        });\n        addEvent(SVGRendererClass, 'complexColor', onRendererComplexColor);\n    }\n}\n/**\n * Add the predefined patterns.\n * @private\n */\nfunction createPatterns() {\n    const patterns = [], colors = getOptions().colors;\n    // Start with subtle patterns\n    let i = 0;\n    for (const pattern of [\n        'M 0 0 L 5 5 M 4.5 -0.5 L 5.5 0.5 M -0.5 4.5 L 0.5 5.5',\n        'M 0 5 L 5 0 M -0.5 0.5 L 0.5 -0.5 M 4.5 5.5 L 5.5 4.5',\n        'M 2 0 L 2 5 M 4 0 L 4 5',\n        'M 0 2 L 5 2 M 0 4 L 5 4',\n        'M 0 1.5 L 2.5 1.5 L 2.5 0 M 2.5 5 L 2.5 3.5 L 5 3.5'\n    ]) {\n        patterns.push({\n            path: pattern,\n            color: colors[i++],\n            width: 5,\n            height: 5,\n            patternTransform: 'scale(1.4 1.4)'\n        });\n    }\n    // Then add the more drastic ones\n    i = 5;\n    for (const pattern of [\n        'M 0 0 L 5 10 L 10 0',\n        'M 3 3 L 8 3 L 8 8 L 3 8 Z',\n        'M 5 5 m -4 0 a 4 4 0 1 1 8 0 a 4 4 0 1 1 -8 0',\n        'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',\n        'M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9'\n    ]) {\n        patterns.push({\n            path: pattern,\n            color: colors[i],\n            width: 10,\n            height: 10\n        });\n        i = i + 5;\n    }\n    return patterns;\n}\n/**\n * Utility function to compute a hash value from an object. Modified Java\n * String.hashCode implementation in JS. Use the preSeed parameter to add an\n * additional seeding step.\n *\n * @private\n * @function hashFromObject\n *\n * @param {Object} obj\n *        The javascript object to compute the hash from.\n *\n * @param {boolean} [preSeed=false]\n *        Add an optional preSeed stage.\n *\n * @return {string}\n *         The computed hash.\n */\nfunction hashFromObject(obj, preSeed) {\n    const str = JSON.stringify(obj), strLen = str.length || 0;\n    let hash = 0, i = 0, char, seedStep;\n    if (preSeed) {\n        seedStep = Math.max(Math.floor(strLen / 500), 1);\n        for (let a = 0; a < strLen; a += seedStep) {\n            hash += str.charCodeAt(a);\n        }\n        hash = hash & hash;\n    }\n    for (; i < strLen; ++i) {\n        char = str.charCodeAt(i);\n        hash = ((hash << 5) - hash) + char;\n        hash = hash & hash;\n    }\n    return hash.toString(16).replace('-', '1');\n}\n/**\n * When animation is used, we have to recalculate pattern dimensions after\n * resize, as the bounding boxes are not available until then.\n * @private\n */\nfunction onChartEndResize() {\n    if (this.renderer &&\n        (this.renderer.defIds || []).filter((id) => (id &&\n            id.indexOf &&\n            id.indexOf('highcharts-pattern-') === 0)).length) {\n        // We have non-default patterns to fix. Find them by looping through\n        // all points.\n        for (const series of this.series) {\n            if (series.visible) {\n                for (const point of series.points) {\n                    const colorOptions = point.options && point.options.color;\n                    if (colorOptions &&\n                        colorOptions.pattern) {\n                        colorOptions.pattern\n                            ._width = 'defer';\n                        colorOptions.pattern\n                            ._height = 'defer';\n                    }\n                }\n            }\n        }\n        // Redraw without animation\n        this.redraw(false);\n    }\n}\n/**\n * Add a garbage collector to delete old patterns with autogenerated hashes that\n * are no longer being referenced.\n * @private\n */\nfunction onChartRedraw() {\n    const usedIds = {}, renderer = this.renderer, \n    // Get the autocomputed patterns - these are the ones we might delete\n    patterns = (renderer.defIds || []).filter((pattern) => (pattern.indexOf &&\n        pattern.indexOf('highcharts-pattern-') === 0));\n    if (patterns.length) {\n        // Look through the DOM for usage of the patterns. This can be points,\n        // series, tooltips etc.\n        [].forEach.call(this.renderTo.querySelectorAll('[color^=\"url(\"], [fill^=\"url(\"], [stroke^=\"url(\"]'), (node) => {\n            const id = node.getAttribute('fill') ||\n                node.getAttribute('color') ||\n                node.getAttribute('stroke');\n            if (id) {\n                const sanitizedId = id\n                    .replace(renderer.url, '')\n                    .replace('url(#', '')\n                    .replace(')', '');\n                usedIds[sanitizedId] = true;\n            }\n        });\n        // Loop through the patterns that exist and see if they are used\n        for (const id of patterns) {\n            if (!usedIds[id]) {\n                // Remove id from used id list\n                erase(renderer.defIds, id);\n                // Remove pattern element\n                if (renderer.patternElements[id]) {\n                    renderer.patternElements[id].destroy();\n                    delete renderer.patternElements[id];\n                }\n            }\n        }\n    }\n}\n/**\n * Merge series color options to points.\n * @private\n */\nfunction onPointAfterInit() {\n    const point = this, colorOptions = point.options.color;\n    // Only do this if we have defined a specific color on this point. Otherwise\n    // we will end up trying to re-add the series color for each point.\n    if (colorOptions && colorOptions.pattern) {\n        // Move path definition to object, allows for merge with series path\n        // definition\n        if (typeof colorOptions.pattern.path === 'string') {\n            colorOptions.pattern.path = {\n                d: colorOptions.pattern.path\n            };\n        }\n        // Merge with series options\n        point.color = point.options.color = merge(point.series.options.color, colorOptions);\n    }\n}\n/**\n * Add functionality to SVG renderer to handle patterns as complex colors.\n * @private\n */\nfunction onRendererComplexColor(args) {\n    const color = args.args[0], prop = args.args[1], element = args.args[2], chartIndex = (this.chartIndex || 0);\n    let pattern = color.pattern, value = \"#333333\" /* Palette.neutralColor80 */;\n    // Handle patternIndex\n    if (typeof color.patternIndex !== 'undefined' && patterns) {\n        pattern = patterns[color.patternIndex];\n    }\n    // Skip and call default if there is no pattern\n    if (!pattern) {\n        return true;\n    }\n    // We have a pattern.\n    if (pattern.image ||\n        typeof pattern.path === 'string' ||\n        pattern.path && pattern.path.d) {\n        // Real pattern. Add it and set the color value to be a reference.\n        // Force Hash-based IDs for legend items, as they are drawn before\n        // point render, meaning they are drawn before autocalculated image\n        // width/heights. We don't want them to highjack the width/height for\n        // this ID if it is defined by users.\n        let forceHashId = element.parentNode &&\n            element.parentNode.getAttribute('class');\n        forceHashId = forceHashId &&\n            forceHashId.indexOf('highcharts-legend') > -1;\n        // If we don't have a width/height yet, handle it. Try faking a point\n        // and running the algorithm again.\n        if (pattern._width === 'defer' || pattern._height === 'defer') {\n            pointCalculatePatternDimensions.call({ graphic: { element: element } }, pattern);\n        }\n        // If we don't have an explicit ID, compute a hash from the\n        // definition and use that as the ID. This ensures that points with\n        // the same pattern definition reuse existing pattern elements by\n        // default. We combine two hashes, the second with an additional\n        // preSeed algorithm, to minimize collision probability.\n        if (forceHashId || !pattern.id) {\n            // Make a copy so we don't accidentally edit options when setting ID\n            pattern = merge({}, pattern);\n            pattern.id = 'highcharts-pattern-' + chartIndex + '-' +\n                hashFromObject(pattern) + hashFromObject(pattern, true);\n        }\n        // Add it. This function does nothing if an element with this ID\n        // already exists.\n        this.addPattern(pattern, !this.forExport && pick(pattern.animation, this.globalAnimation, { duration: 100 }));\n        value = `url(${this.url}#${pattern.id + (this.forExport ? '-export' : '')})`;\n    }\n    else {\n        // Not a full pattern definition, just add color\n        value = pattern.color || value;\n    }\n    // Set the fill/stroke prop on the element\n    element.setAttribute(prop, value);\n    // Allow the color to be concatenated into tooltips formatters etc.\n    color.toString = function () {\n        return value;\n    };\n    // Skip default handler\n    return false;\n}\n/**\n * Calculate pattern dimensions on points that have their own pattern.\n * @private\n */\nfunction onSeriesRender() {\n    const isResizing = this.chart.isResizing;\n    if (this.isDirtyData || isResizing || !this.chart.hasRendered) {\n        for (const point of this.points) {\n            const colorOptions = point.options && point.options.color;\n            if (colorOptions &&\n                colorOptions.pattern) {\n                // For most points we want to recalculate the dimensions on\n                // render, where we have the shape args and bbox. But if we\n                // are resizing and don't have the shape args, defer it, since\n                // the bounding box is still not resized.\n                if (isResizing &&\n                    !(point.shapeArgs &&\n                        point.shapeArgs.width &&\n                        point.shapeArgs.height)) {\n                    colorOptions\n                        .pattern._width = 'defer';\n                    colorOptions\n                        .pattern._height = 'defer';\n                }\n                else {\n                    point.calculatePatternDimensions(colorOptions.pattern);\n                }\n            }\n        }\n    }\n}\n/**\n * Set dimensions on pattern from point. This function will set internal\n * pattern._width/_height properties if width and height are not both already\n * set. We only do this on image patterns. The _width/_height properties are set\n * to the size of the bounding box of the point, optionally taking aspect ratio\n * into account. If only one of width or height are supplied as options, the\n * undefined option is calculated as above.\n *\n * @private\n * @function Highcharts.Point#calculatePatternDimensions\n *\n * @param {Highcharts.PatternOptionsObject} pattern\n *        The pattern to set dimensions on.\n *\n * @return {void}\n *\n * @requires modules/pattern-fill\n */\nfunction pointCalculatePatternDimensions(pattern) {\n    if (pattern.width && pattern.height) {\n        return;\n    }\n    const bBox = this.graphic && (this.graphic.getBBox &&\n        this.graphic.getBBox(true) ||\n        this.graphic.element &&\n            this.graphic.element.getBBox()) || {}, shapeArgs = this.shapeArgs;\n    // Prefer using shapeArgs, as it is animation agnostic\n    if (shapeArgs) {\n        bBox.width = shapeArgs.width || bBox.width;\n        bBox.height = shapeArgs.height || bBox.height;\n        bBox.x = shapeArgs.x || bBox.x;\n        bBox.y = shapeArgs.y || bBox.y;\n    }\n    // For images we stretch to bounding box\n    if (pattern.image) {\n        // If we do not have a bounding box at this point, simply add a defer\n        // key and pick this up in the fillSetter handler, where the bounding\n        // box should exist.\n        if (!bBox.width || !bBox.height) {\n            pattern._width = 'defer';\n            pattern._height = 'defer';\n            // Mark the pattern to be flipped later if upside down (#16810)\n            const scaleY = this.series.chart.mapView &&\n                this.series.chart.mapView.getSVGTransform().scaleY;\n            if (defined(scaleY) && scaleY < 0) {\n                pattern._inverted = true;\n            }\n            return;\n        }\n        // Handle aspect ratio filling\n        if (pattern.aspectRatio) {\n            bBox.aspectRatio = bBox.width / bBox.height;\n            if (pattern.aspectRatio > bBox.aspectRatio) {\n                // Height of bBox will determine width\n                bBox.aspectWidth = bBox.height * pattern.aspectRatio;\n            }\n            else {\n                // Width of bBox will determine height\n                bBox.aspectHeight = bBox.width / pattern.aspectRatio;\n            }\n        }\n        // We set the width/height on internal properties to differentiate\n        // between the options set by a user and by this function.\n        pattern._width = pattern.width ||\n            Math.ceil(bBox.aspectWidth || bBox.width);\n        pattern._height = pattern.height ||\n            Math.ceil(bBox.aspectHeight || bBox.height);\n    }\n    // Set x/y accordingly, centering if using aspect ratio, otherwise adjusting\n    // so bounding box corner is 0,0 of pattern.\n    if (!pattern.width) {\n        pattern._x = pattern.x || 0;\n        pattern._x += bBox.x - Math.round(bBox.aspectWidth ?\n            Math.abs(bBox.aspectWidth - bBox.width) / 2 :\n            0);\n    }\n    if (!pattern.height) {\n        pattern._y = pattern.y || 0;\n        pattern._y += bBox.y - Math.round(bBox.aspectHeight ?\n            Math.abs(bBox.aspectHeight - bBox.height) / 2 :\n            0);\n    }\n}\n/**\n * Add a pattern to the renderer.\n *\n * @private\n * @function Highcharts.SVGRenderer#addPattern\n *\n * @param {Highcharts.PatternObject} options\n * The pattern options.\n *\n * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n * The animation options.\n *\n * @return {Highcharts.SVGElement|undefined}\n * The added pattern. Undefined if the pattern already exists.\n *\n * @requires modules/pattern-fill\n */\nfunction rendererAddPattern(options, animation) {\n    const animate = pick(animation, true), animationOptions = animObject(animate), color = options.color || \"#333333\" /* Palette.neutralColor80 */, defaultSize = 32, height = options.height ||\n        (typeof options._height === 'number' ? options._height : 0) ||\n        defaultSize, rect = (fill) => this\n        .rect(0, 0, width, height)\n        .attr({ fill })\n        .add(pattern), width = options.width ||\n        (typeof options._width === 'number' ? options._width : 0) ||\n        defaultSize;\n    let attribs, id = options.id, path;\n    if (!id) {\n        this.idCounter = this.idCounter || 0;\n        id = ('highcharts-pattern-' +\n            this.idCounter +\n            '-' +\n            (this.chartIndex || 0));\n        ++this.idCounter;\n    }\n    if (this.forExport) {\n        id += '-export';\n    }\n    // Do nothing if ID already exists\n    this.defIds = this.defIds || [];\n    if (this.defIds.indexOf(id) > -1) {\n        return;\n    }\n    // Store ID in list to avoid duplicates\n    this.defIds.push(id);\n    // Calculate pattern element attributes\n    const attrs = {\n        id: id,\n        patternUnits: 'userSpaceOnUse',\n        patternContentUnits: options.patternContentUnits || 'userSpaceOnUse',\n        width: width,\n        height: height,\n        x: options._x || options.x || 0,\n        y: options._y || options.y || 0\n    };\n    if (options._inverted) {\n        attrs.patternTransform = 'scale(1, -1)'; // (#16810)\n        if (options.patternTransform) {\n            options.patternTransform += ' scale(1, -1)';\n        }\n    }\n    if (options.patternTransform) {\n        attrs.patternTransform = options.patternTransform;\n    }\n    const pattern = this.createElement('pattern').attr(attrs).add(this.defs);\n    // Set id on the SVGRenderer object\n    pattern.id = id;\n    // Use an SVG path for the pattern\n    if (options.path) {\n        path = external_highcharts_src_js_default_default().isObject(options.path) ?\n            options.path :\n            { d: options.path };\n        // The background\n        if (options.backgroundColor) {\n            rect(options.backgroundColor);\n        }\n        // The pattern\n        attribs = {\n            'd': path.d\n        };\n        if (!this.styledMode) {\n            attribs.stroke = path.stroke || color;\n            attribs['stroke-width'] = pick(path.strokeWidth, 2);\n            attribs.fill = path.fill || 'none';\n        }\n        if (path.transform) {\n            attribs.transform = path.transform;\n        }\n        this.createElement('path').attr(attribs).add(pattern);\n        pattern.color = color;\n        // Image pattern\n    }\n    else if (options.image) {\n        if (animate) {\n            this.image(options.image, 0, 0, width, height, function () {\n                // Onload\n                this.animate({\n                    opacity: pick(options.opacity, 1)\n                }, animationOptions);\n                removeEvent(this.element, 'load');\n            }).attr({ opacity: 0 }).add(pattern);\n        }\n        else {\n            this.image(options.image, 0, 0, width, height).add(pattern);\n        }\n    }\n    // For non-animated patterns, set opacity now\n    if (!(options.image && animate) && typeof options.opacity !== 'undefined') {\n        [].forEach.call(pattern.element.childNodes, (child) => {\n            child.setAttribute('opacity', options.opacity);\n        });\n    }\n    // Store for future reference\n    this.patternElements = this.patternElements || {};\n    this.patternElements[id] = pattern;\n    return pattern;\n}\n/**\n * Make sure we have a series color.\n * @private\n */\nfunction wrapSeriesGetColor(proceed) {\n    const oldColor = this.options.color;\n    // Temporarily remove color options to get defaults\n    if (oldColor &&\n        oldColor.pattern &&\n        !oldColor.pattern.color) {\n        delete this.options.color;\n        // Get default\n        proceed.apply(this, [].slice.call(arguments, 1));\n        // Replace with old, but add default color\n        oldColor.pattern.color =\n            this.color;\n        this.color = this.options.color = oldColor;\n    }\n    else {\n        // We have a color, no need to do anything special\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Scale patterns inversely to the series it's used in.\n * Maintains a visual (1,1) scale regardless of size.\n * @private\n */\nfunction onPatternScaleCorrection() {\n    const series = this;\n    // If not a series used in a map chart, skip it.\n    if (!series.chart?.mapView) {\n        return;\n    }\n    const chart = series.chart, renderer = chart.renderer, patterns = renderer.patternElements;\n    // Only scale if we have patterns to scale.\n    if (renderer.defIds?.length && patterns) {\n        // Filter for points which have patterns that don't use images assigned\n        // and has a group scale available.\n        series.points.filter(function (p) {\n            const point = p;\n            // No graphic we can fetch id from, filter out this point.\n            if (!point.graphic) {\n                return false;\n            }\n            return (point.graphic.element.hasAttribute('fill') ||\n                point.graphic.element.hasAttribute('color') ||\n                point.graphic.element.hasAttribute('stroke')) &&\n                !point.options.color?.pattern?.image &&\n                !!point.group?.scaleX &&\n                !!point.group?.scaleY;\n        })\n            // Map up pattern id's and their scales.\n            .map(function (p) {\n            const point = p;\n            // Parse the id from the graphic element of the point.\n            const id = (point.graphic?.element.getAttribute('fill') ||\n                point.graphic?.element.getAttribute('color') ||\n                point.graphic?.element.getAttribute('stroke') || '')\n                .replace(renderer.url, '')\n                .replace('url(#', '')\n                .replace(')', '');\n            return {\n                id,\n                x: point.group?.scaleX || 1,\n                y: point.group?.scaleY || 1\n            };\n        })\n            // Filter out colors and other non-patterns, as well as duplicates.\n            .filter(function (pointInfo, index, arr) {\n            return pointInfo.id !== '' &&\n                pointInfo.id.indexOf('highcharts-pattern-') !== -1 &&\n                !arr.some(function (otherInfo, otherIndex) {\n                    return otherInfo.id === pointInfo.id && otherIndex < index;\n                });\n        })\n            .forEach(function (pointInfo) {\n            const id = pointInfo.id;\n            patterns[id].scaleX = 1 / pointInfo.x;\n            patterns[id].scaleY = 1 / pointInfo.y;\n            patterns[id].updateTransform('patternTransform');\n        });\n    }\n}\n/* *\n *\n *  Export\n *\n * */\nconst PatternFill = {\n    compose,\n    patterns\n};\n/* harmony default export */ const Extensions_PatternFill = (PatternFill);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Pattern options\n *\n * @interface Highcharts.PatternOptionsObject\n */ /**\n* Background color for the pattern if a `path` is set (not images).\n* @name Highcharts.PatternOptionsObject#backgroundColor\n* @type {Highcharts.ColorString|undefined}\n*/ /**\n* URL to an image to use as the pattern.\n* @name Highcharts.PatternOptionsObject#image\n* @type {string|undefined}\n*/ /**\n* Width of the pattern. For images this is automatically set to the width of\n* the element bounding box if not supplied. For non-image patterns the default\n* is 32px. Note that automatic resizing of image patterns to fill a bounding\n* box dynamically is only supported for patterns with an automatically\n* calculated ID.\n* @name Highcharts.PatternOptionsObject#width\n* @type {number|undefined}\n*/ /**\n* Analogous to pattern.width.\n* @name Highcharts.PatternOptionsObject#height\n* @type {number|undefined}\n*/ /**\n* For automatically calculated width and height on images, it is possible to\n* set an aspect ratio. The image will be zoomed to fill the bounding box,\n* maintaining the aspect ratio defined.\n* @name Highcharts.PatternOptionsObject#aspectRatio\n* @type {number|undefined}\n*/ /**\n* Horizontal offset of the pattern. Defaults to 0.\n* @name Highcharts.PatternOptionsObject#x\n* @type {number|undefined}\n*/ /**\n* Vertical offset of the pattern. Defaults to 0.\n* @name Highcharts.PatternOptionsObject#y\n* @type {number|undefined}\n*/ /**\n* Either an SVG path as string, or an object. As an object, supply the path\n* string in the `path.d` property. Other supported properties are standard SVG\n* attributes like `path.stroke` and `path.fill`. If a path is supplied for the\n* pattern, the `image` property is ignored.\n* @name Highcharts.PatternOptionsObject#path\n* @type {string|Highcharts.SVGAttributes|undefined}\n*/ /**\n* SVG `patternTransform` to apply to the entire pattern.\n* @name Highcharts.PatternOptionsObject#patternTransform\n* @type {string|undefined}\n* @see [patternTransform demo](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/series/pattern-fill-transform)\n*/ /**\n* Pattern color, used as default path stroke.\n* @name Highcharts.PatternOptionsObject#color\n* @type {Highcharts.ColorString|undefined}\n*/ /**\n* Opacity of the pattern as a float value from 0 to 1.\n* @name Highcharts.PatternOptionsObject#opacity\n* @type {number|undefined}\n*/ /**\n* ID to assign to the pattern. This is automatically computed if not added, and\n* identical patterns are reused. To refer to an existing pattern for a\n* Highcharts color, use `color: \"url(#pattern-id)\"`.\n* @name Highcharts.PatternOptionsObject#id\n* @type {string|undefined}\n*/\n/**\n * Holds a pattern definition.\n *\n * @sample highcharts/series/pattern-fill-area/\n *         Define a custom path pattern\n * @sample highcharts/series/pattern-fill-pie/\n *         Default patterns and a custom image pattern\n * @sample maps/demo/pattern-fill-map/\n *         Custom images on map\n *\n * @example\n * // Pattern used as a color option\n * color: {\n *     pattern: {\n *            path: {\n *                 d: 'M 3 3 L 8 3 L 8 8 Z',\n *                fill: '#102045'\n *            },\n *            width: 12,\n *            height: 12,\n *            color: '#907000',\n *            opacity: 0.5\n *     }\n * }\n *\n * @interface Highcharts.PatternObject\n */ /**\n* Pattern options\n* @name Highcharts.PatternObject#pattern\n* @type {Highcharts.PatternOptionsObject}\n*/ /**\n* Animation options for the image pattern loading.\n* @name Highcharts.PatternObject#animation\n* @type {boolean|Partial<Highcharts.AnimationOptionsObject>|undefined}\n*/ /**\n* Optionally an index referencing which pattern to use. Highcharts adds\n* 10 default patterns to the `Highcharts.patterns` array. Additional\n* pattern definitions can be pushed to this array if desired. This option\n* is an index into this array.\n* @name Highcharts.PatternObject#patternIndex\n* @type {number|undefined}\n*/\n''; // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/masters/modules/pattern-fill.js\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nG.patterns = Extensions_PatternFill.patterns;\nExtensions_PatternFill.compose(G.Chart, G.Series, G.SVGRenderer);\n/* harmony default export */ const pattern_fill_src = ((external_highcharts_src_js_default_default()));\n\nexport { pattern_fill_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "animObject", "getOptions", "addEvent", "defined", "erase", "extend", "merge", "pick", "removeEvent", "wrap", "patterns", "createPatterns", "colors", "i", "pattern", "push", "path", "color", "width", "height", "patternTransform", "hashFromObject", "preSeed", "str", "JSON", "stringify", "strLen", "length", "hash", "seedStep", "Math", "max", "floor", "charCodeAt", "toString", "replace", "onChartEndResize", "renderer", "defIds", "filter", "id", "indexOf", "series", "visible", "point", "points", "colorOptions", "options", "_width", "_height", "redraw", "onChartRedraw", "usedIds", "for<PERSON>ach", "renderTo", "querySelectorAll", "node", "getAttribute", "url", "patternElements", "destroy", "onPointAfterInit", "onRendererComplexColor", "args", "element", "chartIndex", "value", "patternIndex", "image", "forceHashId", "parentNode", "pointCalculatePatternDimensions", "graphic", "addPattern", "forExport", "animation", "globalAnimation", "duration", "setAttribute", "onSeriesRender", "isResizing", "chart", "isDirtyData", "hasRendered", "shapeArgs", "calculatePatternDimensions", "bBox", "getBBox", "x", "y", "scaleY", "mapView", "getSVGTransform", "_inverted", "aspectRatio", "aspectWidth", "aspectHeight", "ceil", "_x", "round", "abs", "_y", "rendererAddPattern", "animate", "animationOptions", "attribs", "idCounter", "attrs", "patternUnits", "patternContentUnits", "createElement", "attr", "add", "defs", "isObject", "backgroundColor", "fill", "rect", "styledMode", "stroke", "strokeWidth", "transform", "opacity", "childNodes", "child", "wrapSeriesGetColor", "proceed", "oldColor", "apply", "slice", "arguments", "onPatternScaleCorrection", "p", "hasAttribute", "group", "scaleX", "map", "pointInfo", "index", "arr", "some", "otherInfo", "otherIndex", "updateTransform", "G", "Extensions_PatternFill", "ChartClass", "SeriesClass", "SVGRendererClass", "PointClass", "pointClass", "pointProto", "Chart", "Series", "<PERSON><PERSON><PERSON><PERSON>", "pattern_fill_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,CAEvF,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAgBpF,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAID,IAElB,CAAEE,WAAAA,CAAU,CAAE,CAAIF,IAElB,CAAEG,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,YAAAA,CAAW,CAAEC,KAAAA,CAAI,CAAE,CAAIV,IAMxEW,EAAWC,AA+BjB,WACI,IAAMD,EAAW,EAAE,CAAEE,EAASX,IAAaW,MAAM,CAE7CC,EAAI,EACR,IAAK,IAAMC,IAAW,CAClB,wDACA,wDACA,0BACA,0BACA,sDACH,CACGJ,EAASK,IAAI,CAAC,CACVC,KAAMF,EACNG,MAAOL,CAAM,CAACC,IAAI,CAClBK,MAAO,EACPC,OAAQ,EACRC,iBAAkB,gBACtB,GAIJ,IAAK,IAAMN,KADXD,EAAI,EACkB,CAClB,sBACA,4BACA,gDACA,4CACA,4CACH,EACGH,EAASK,IAAI,CAAC,CACVC,KAAMF,EACNG,MAAOL,CAAM,CAACC,EAAE,CAChBK,MAAO,GACPC,OAAQ,EACZ,GACAN,GAAQ,EAEZ,OAAOH,CACX,IAkBA,SAASW,EAAe5B,CAAG,CAAE6B,CAAO,EAChC,IAAMC,EAAMC,KAAKC,SAAS,CAAChC,GAAMiC,EAASH,EAAII,MAAM,EAAI,EACpDC,EAAO,EAAGf,EAAI,EAASgB,EAC3B,GAAIP,EAAS,CACTO,EAAWC,KAAKC,GAAG,CAACD,KAAKE,KAAK,CAACN,EAAS,KAAM,GAC9C,IAAK,IAAI1C,EAAI,EAAGA,EAAI0C,EAAQ1C,GAAK6C,EAC7BD,GAAQL,EAAIU,UAAU,CAACjD,GAE3B4C,GAAcA,CAClB,CACA,KAAOf,EAAIa,EAAQ,EAAEb,EAEjBe,EAAO,AAAEA,CAAAA,GAAQ,CAAA,EAAKA,EADfL,EAAIU,UAAU,CAACpB,GAEtBe,GAAcA,EAElB,OAAOA,EAAKM,QAAQ,CAAC,IAAIC,OAAO,CAAC,IAAK,IAC1C,CAMA,SAASC,IACL,GAAI,IAAI,CAACC,QAAQ,EACb,AAAC,CAAA,IAAI,CAACA,QAAQ,CAACC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,AAACC,GAAQA,GACzCA,EAAGC,OAAO,EACVD,AAAsC,IAAtCA,EAAGC,OAAO,CAAC,wBAA+Bd,MAAM,CAAE,CAGtD,IAAK,IAAMe,KAAU,IAAI,CAACA,MAAM,CAC5B,GAAIA,EAAOC,OAAO,CACd,IAAK,IAAMC,KAASF,EAAOG,MAAM,CAAE,CAC/B,IAAMC,EAAeF,EAAMG,OAAO,EAAIH,EAAMG,OAAO,CAAC9B,KAAK,CACrD6B,GACAA,EAAahC,OAAO,GACpBgC,EAAahC,OAAO,CACfkC,MAAM,CAAG,QACdF,EAAahC,OAAO,CACfmC,OAAO,CAAG,QAEvB,CAIR,IAAI,CAACC,MAAM,CAAC,CAAA,EAChB,CACJ,CAMA,SAASC,IACL,IAAMC,EAAU,CAAC,EAAGf,EAAW,IAAI,CAACA,QAAQ,CAE5C3B,EAAW,AAAC2B,CAAAA,EAASC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,AAACzB,GAAaA,EAAQ2B,OAAO,EACnE3B,AAA2C,IAA3CA,EAAQ2B,OAAO,CAAC,wBACpB,GAAI/B,EAASiB,MAAM,CAgBf,IAAK,IAAMa,KAbX,EAAE,CAACa,OAAO,CAACxD,IAAI,CAAC,IAAI,CAACyD,QAAQ,CAACC,gBAAgB,CAAC,qDAAsD,AAACC,IAClG,IAAMhB,EAAKgB,EAAKC,YAAY,CAAC,SACzBD,EAAKC,YAAY,CAAC,UAClBD,EAAKC,YAAY,CAAC,UAClBjB,GAKAY,CAAAA,CAAO,CAJaZ,EACfL,OAAO,CAACE,EAASqB,GAAG,CAAE,IACtBvB,OAAO,CAAC,QAAS,IACjBA,OAAO,CAAC,IAAK,IACE,CAAG,CAAA,CAAG,CAElC,GAEiBzB,GACT,CAAC0C,CAAO,CAACZ,EAAG,GAEZpC,EAAMiC,EAASC,MAAM,CAAEE,GAEnBH,EAASsB,eAAe,CAACnB,EAAG,GAC5BH,EAASsB,eAAe,CAACnB,EAAG,CAACoB,OAAO,GACpC,OAAOvB,EAASsB,eAAe,CAACnB,EAAG,EAKvD,CAKA,SAASqB,IACL,IAAoBf,EAAeF,AAArB,IAAI,CAAuBG,OAAO,CAAC9B,KAAK,CAGlD6B,GAAgBA,EAAahC,OAAO,GAGK,UAArC,OAAOgC,EAAahC,OAAO,CAACE,IAAI,EAChC8B,CAAAA,EAAahC,OAAO,CAACE,IAAI,CAAG,CACxBjC,EAAG+D,EAAahC,OAAO,CAACE,IAAI,AAChC,CAAA,EAGJ4B,AAZU,IAAI,CAYR3B,KAAK,CAAG2B,AAZJ,IAAI,CAYMG,OAAO,CAAC9B,KAAK,CAAGX,EAAMsC,AAZhC,IAAI,CAYkCF,MAAM,CAACK,OAAO,CAAC9B,KAAK,CAAE6B,GAE9E,CAKA,SAASgB,EAAuBC,CAAI,EAChC,IAAM9C,EAAQ8C,EAAKA,IAAI,CAAC,EAAE,CAAErE,EAAOqE,EAAKA,IAAI,CAAC,EAAE,CAAEC,EAAUD,EAAKA,IAAI,CAAC,EAAE,CAAEE,EAAc,IAAI,CAACA,UAAU,EAAI,EACtGnD,EAAUG,EAAMH,OAAO,CAAEoD,EAAQ,UAMrC,GAJkC,KAAA,IAAvBjD,EAAMkD,YAAY,EAAoBzD,GAC7CI,CAAAA,EAAUJ,CAAQ,CAACO,EAAMkD,YAAY,CAAC,AAAD,EAGrC,CAACrD,EACD,MAAO,CAAA,EAGX,GAAIA,EAAQsD,KAAK,EACb,AAAwB,UAAxB,OAAOtD,EAAQE,IAAI,EACnBF,EAAQE,IAAI,EAAIF,EAAQE,IAAI,CAACjC,CAAC,CAAE,CAMhC,IAAIsF,EAAcL,EAAQM,UAAU,EAChCN,EAAQM,UAAU,CAACb,YAAY,CAAC,SACpCY,EAAcA,GACVA,EAAY5B,OAAO,CAAC,qBAAuB,GAG3C3B,CAAAA,AAAmB,UAAnBA,EAAQkC,MAAM,EAAgBlC,AAAoB,UAApBA,EAAQmC,OAAO,AAAW,GACxDsB,EAAgC1E,IAAI,CAAC,CAAE2E,QAAS,CAAER,QAASA,CAAQ,CAAE,EAAGlD,GAOxEuD,CAAAA,GAAe,CAACvD,EAAQ0B,EAAE,AAAD,GAGzB1B,CAAAA,AADAA,CAAAA,EAAUR,EAAM,CAAC,EAAGQ,EAAO,EACnB0B,EAAE,CAAG,sBAAwByB,EAAa,IAC9C5C,EAAeP,GAAWO,EAAeP,EAAS,CAAA,EAAI,EAI9D,IAAI,CAAC2D,UAAU,CAAC3D,EAAS,CAAC,IAAI,CAAC4D,SAAS,EAAInE,EAAKO,EAAQ6D,SAAS,CAAE,IAAI,CAACC,eAAe,CAAE,CAAEC,SAAU,GAAI,IAC1GX,EAAQ,CAAC,IAAI,EAAE,IAAI,CAACR,GAAG,CAAC,CAAC,EAAE5C,EAAQ0B,EAAE,CAAI,CAAA,IAAI,CAACkC,SAAS,CAAG,UAAY,EAAC,EAAG,CAAC,CAAC,AAChF,MAGIR,EAAQpD,EAAQG,KAAK,EAAIiD,EAS7B,OANAF,EAAQc,YAAY,CAACpF,EAAMwE,GAE3BjD,EAAMiB,QAAQ,CAAG,WACb,OAAOgC,CACX,EAEO,CAAA,CACX,CAKA,SAASa,IACL,IAAMC,EAAa,IAAI,CAACC,KAAK,CAACD,UAAU,CACxC,GAAI,IAAI,CAACE,WAAW,EAAIF,GAAc,CAAC,IAAI,CAACC,KAAK,CAACE,WAAW,CACzD,IAAK,IAAMvC,KAAS,IAAI,CAACC,MAAM,CAAE,CAC7B,IAAMC,EAAeF,EAAMG,OAAO,EAAIH,EAAMG,OAAO,CAAC9B,KAAK,CACrD6B,GACAA,EAAahC,OAAO,GAKhBkE,GACA,CAAEpC,CAAAA,EAAMwC,SAAS,EACbxC,EAAMwC,SAAS,CAAClE,KAAK,EACrB0B,EAAMwC,SAAS,CAACjE,MAAM,AAAD,GACzB2B,EACKhC,OAAO,CAACkC,MAAM,CAAG,QACtBF,EACKhC,OAAO,CAACmC,OAAO,CAAG,SAGvBL,EAAMyC,0BAA0B,CAACvC,EAAahC,OAAO,EAGjE,CAER,CAmBA,SAASyD,EAAgCzD,CAAO,EAC5C,GAAIA,EAAQI,KAAK,EAAIJ,EAAQK,MAAM,CAC/B,OAEJ,IAAMmE,EAAO,IAAI,CAACd,OAAO,EAAK,CAAA,IAAI,CAACA,OAAO,CAACe,OAAO,EAC9C,IAAI,CAACf,OAAO,CAACe,OAAO,CAAC,CAAA,IACrB,IAAI,CAACf,OAAO,CAACR,OAAO,EAChB,IAAI,CAACQ,OAAO,CAACR,OAAO,CAACuB,OAAO,EAAC,GAAM,CAAC,EAAGH,EAAY,IAAI,CAACA,SAAS,CASzE,GAPIA,IACAE,EAAKpE,KAAK,CAAGkE,EAAUlE,KAAK,EAAIoE,EAAKpE,KAAK,CAC1CoE,EAAKnE,MAAM,CAAGiE,EAAUjE,MAAM,EAAImE,EAAKnE,MAAM,CAC7CmE,EAAKE,CAAC,CAAGJ,EAAUI,CAAC,EAAIF,EAAKE,CAAC,CAC9BF,EAAKG,CAAC,CAAGL,EAAUK,CAAC,EAAIH,EAAKG,CAAC,EAG9B3E,EAAQsD,KAAK,CAAE,CAIf,GAAI,CAACkB,EAAKpE,KAAK,EAAI,CAACoE,EAAKnE,MAAM,CAAE,CAC7BL,EAAQkC,MAAM,CAAG,QACjBlC,EAAQmC,OAAO,CAAG,QAElB,IAAMyC,EAAS,IAAI,CAAChD,MAAM,CAACuC,KAAK,CAACU,OAAO,EACpC,IAAI,CAACjD,MAAM,CAACuC,KAAK,CAACU,OAAO,CAACC,eAAe,GAAGF,MAAM,CAClDvF,EAAQuF,IAAWA,EAAS,GAC5B5E,CAAAA,EAAQ+E,SAAS,CAAG,CAAA,CAAG,EAE3B,MACJ,CAEI/E,EAAQgF,WAAW,GACnBR,EAAKQ,WAAW,CAAGR,EAAKpE,KAAK,CAAGoE,EAAKnE,MAAM,CACvCL,EAAQgF,WAAW,CAAGR,EAAKQ,WAAW,CAEtCR,EAAKS,WAAW,CAAGT,EAAKnE,MAAM,CAAGL,EAAQgF,WAAW,CAIpDR,EAAKU,YAAY,CAAGV,EAAKpE,KAAK,CAAGJ,EAAQgF,WAAW,EAK5DhF,EAAQkC,MAAM,CAAGlC,EAAQI,KAAK,EAC1BY,KAAKmE,IAAI,CAACX,EAAKS,WAAW,EAAIT,EAAKpE,KAAK,EAC5CJ,EAAQmC,OAAO,CAAGnC,EAAQK,MAAM,EAC5BW,KAAKmE,IAAI,CAACX,EAAKU,YAAY,EAAIV,EAAKnE,MAAM,CAClD,CAGKL,EAAQI,KAAK,GACdJ,EAAQoF,EAAE,CAAGpF,EAAQ0E,CAAC,EAAI,EAC1B1E,EAAQoF,EAAE,EAAIZ,EAAKE,CAAC,CAAG1D,KAAKqE,KAAK,CAACb,EAAKS,WAAW,CAC9CjE,KAAKsE,GAAG,CAACd,EAAKS,WAAW,CAAGT,EAAKpE,KAAK,EAAI,EAC1C,IAEHJ,EAAQK,MAAM,GACfL,EAAQuF,EAAE,CAAGvF,EAAQ2E,CAAC,EAAI,EAC1B3E,EAAQuF,EAAE,EAAIf,EAAKG,CAAC,CAAG3D,KAAKqE,KAAK,CAACb,EAAKU,YAAY,CAC/ClE,KAAKsE,GAAG,CAACd,EAAKU,YAAY,CAAGV,EAAKnE,MAAM,EAAI,EAC5C,GAEZ,CAkBA,SAASmF,EAAmBvD,CAAO,CAAE4B,CAAS,EAC1C,IAAM4B,EAAUhG,EAAKoE,EAAW,CAAA,GAAO6B,EAAmBxG,EAAWuG,GAAUtF,EAAQ8B,EAAQ9B,KAAK,EAAI,UAA0DE,EAAS4B,EAAQ5B,MAAM,EACpL,CAAA,AAA2B,UAA3B,OAAO4B,EAAQE,OAAO,CAAgBF,EAAQE,OAAO,CAAG,CAAA,GADiG,GAK3I/B,EAAQ6B,EAAQ7B,KAAK,EACnC,CAAA,AAA0B,UAA1B,OAAO6B,EAAQC,MAAM,CAAgBD,EAAQC,MAAM,CAAG,CAAA,GANmG,GAQ1JyD,EAASjE,EAAKO,EAAQP,EAAE,CAAExB,EAc9B,GAbI,CAACwB,IACD,IAAI,CAACkE,SAAS,CAAG,IAAI,CAACA,SAAS,EAAI,EACnClE,EAAM,sBACF,IAAI,CAACkE,SAAS,CACd,IACC,CAAA,IAAI,CAACzC,UAAU,EAAI,CAAA,EACxB,EAAE,IAAI,CAACyC,SAAS,EAEhB,IAAI,CAAChC,SAAS,EACdlC,CAAAA,GAAM,SAAQ,EAGlB,IAAI,CAACF,MAAM,CAAG,IAAI,CAACA,MAAM,EAAI,EAAE,CAC3B,IAAI,CAACA,MAAM,CAACG,OAAO,CAACD,GAAM,GAC1B,OAGJ,IAAI,CAACF,MAAM,CAACvB,IAAI,CAACyB,GAEjB,IAAMmE,EAAQ,CACVnE,GAAIA,EACJoE,aAAc,iBACdC,oBAAqB9D,EAAQ8D,mBAAmB,EAAI,iBACpD3F,MAAOA,EACPC,OAAQA,EACRqE,EAAGzC,EAAQmD,EAAE,EAAInD,EAAQyC,CAAC,EAAI,EAC9BC,EAAG1C,EAAQsD,EAAE,EAAItD,EAAQ0C,CAAC,EAAI,CAClC,CACI1C,CAAAA,EAAQ8C,SAAS,GACjBc,EAAMvF,gBAAgB,CAAG,eACrB2B,EAAQ3B,gBAAgB,EACxB2B,CAAAA,EAAQ3B,gBAAgB,EAAI,eAAc,GAG9C2B,EAAQ3B,gBAAgB,EACxBuF,CAAAA,EAAMvF,gBAAgB,CAAG2B,EAAQ3B,gBAAgB,AAAD,EAEpD,IAAMN,EAAU,IAAI,CAACgG,aAAa,CAAC,WAAWC,IAAI,CAACJ,GAAOK,GAAG,CAAC,IAAI,CAACC,IAAI,EAIvE,GAFAnG,EAAQ0B,EAAE,CAAGA,EAETO,EAAQ/B,IAAI,CAAE,CAKd,GAJAA,EAAOjB,IAA6CmH,QAAQ,CAACnE,EAAQ/B,IAAI,EACrE+B,EAAQ/B,IAAI,CACZ,CAAEjC,EAAGgE,EAAQ/B,IAAI,AAAC,EAElB+B,EAAQoE,eAAe,CAAE,KArDRC,EAAAA,EAsDZrE,EAAQoE,eAAe,CAtDF,IAAI,CACjCE,IAAI,CAAC,EAAG,EAAGnG,EAAOC,GAClB4F,IAAI,CAAC,CAAEK,KAAAA,CAAK,GACZJ,GAAG,CAAClG,EAoDL,CAEA2F,EAAU,CACN,EAAKzF,EAAKjC,CAAC,AACf,EACK,IAAI,CAACuI,UAAU,GAChBb,EAAQc,MAAM,CAAGvG,EAAKuG,MAAM,EAAItG,EAChCwF,CAAO,CAAC,eAAe,CAAGlG,EAAKS,EAAKwG,WAAW,CAAE,GACjDf,EAAQW,IAAI,CAAGpG,EAAKoG,IAAI,EAAI,QAE5BpG,EAAKyG,SAAS,EACdhB,CAAAA,EAAQgB,SAAS,CAAGzG,EAAKyG,SAAS,AAAD,EAErC,IAAI,CAACX,aAAa,CAAC,QAAQC,IAAI,CAACN,GAASO,GAAG,CAAClG,GAC7CA,EAAQG,KAAK,CAAGA,CAEpB,MACS8B,EAAQqB,KAAK,GACdmC,EACA,IAAI,CAACnC,KAAK,CAACrB,EAAQqB,KAAK,CAAE,EAAG,EAAGlD,EAAOC,EAAQ,WAE3C,IAAI,CAACoF,OAAO,CAAC,CACTmB,QAASnH,EAAKwC,EAAQ2E,OAAO,CAAE,EACnC,EAAGlB,GACHhG,EAAY,IAAI,CAACwD,OAAO,CAAE,OAC9B,GAAG+C,IAAI,CAAC,CAAEW,QAAS,CAAE,GAAGV,GAAG,CAAClG,GAG5B,IAAI,CAACsD,KAAK,CAACrB,EAAQqB,KAAK,CAAE,EAAG,EAAGlD,EAAOC,GAAQ6F,GAAG,CAAClG,IAY3D,OARMiC,EAAQqB,KAAK,EAAImC,GAAY,AAA2B,KAAA,IAApBxD,EAAQ2E,OAAO,EACrD,EAAE,CAACrE,OAAO,CAACxD,IAAI,CAACiB,EAAQkD,OAAO,CAAC2D,UAAU,CAAE,AAACC,IACzCA,EAAM9C,YAAY,CAAC,UAAW/B,EAAQ2E,OAAO,CACjD,GAGJ,IAAI,CAAC/D,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,CAAC,EAChD,IAAI,CAACA,eAAe,CAACnB,EAAG,CAAG1B,EACpBA,CACX,CAKA,SAAS+G,EAAmBC,CAAO,EAC/B,IAAMC,EAAW,IAAI,CAAChF,OAAO,CAAC9B,KAAK,AAE/B8G,CAAAA,GACAA,EAASjH,OAAO,EAChB,CAACiH,EAASjH,OAAO,CAACG,KAAK,EACvB,OAAO,IAAI,CAAC8B,OAAO,CAAC9B,KAAK,CAEzB6G,EAAQE,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAACpI,IAAI,CAACqI,UAAW,IAE7CH,EAASjH,OAAO,CAACG,KAAK,CAClB,IAAI,CAACA,KAAK,CACd,IAAI,CAACA,KAAK,CAAG,IAAI,CAAC8B,OAAO,CAAC9B,KAAK,CAAG8G,GAIlCD,EAAQE,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAACpI,IAAI,CAACqI,UAAW,GAErD,CAMA,SAASC,IAGL,GAAI,CAACzF,AAFU,IAAI,CAEPuC,KAAK,EAAEU,QACf,OAEJ,IAA4BtD,EAAW4C,AAAzBvC,AALC,IAAI,CAKEuC,KAAK,CAAmB5C,QAAQ,CAAE3B,EAAW2B,EAASsB,eAAe,AAEtFtB,CAAAA,EAASC,MAAM,EAAEX,QAAUjB,GAG3BgC,AAVW,IAAI,CAURG,MAAM,CAACN,MAAM,CAAC,SAAU6F,CAAC,QAG5B,EAAKxF,AAFSwF,EAEH5D,OAAO,EAGX,AAAC5B,CAAAA,AALMwF,EAKA5D,OAAO,CAACR,OAAO,CAACqE,YAAY,CAAC,SACvCzF,AANUwF,EAMJ5D,OAAO,CAACR,OAAO,CAACqE,YAAY,CAAC,UACnCzF,AAPUwF,EAOJ5D,OAAO,CAACR,OAAO,CAACqE,YAAY,CAAC,SAAQ,GAC3C,CAACzF,AARSwF,EAQHrF,OAAO,CAAC9B,KAAK,EAAEH,SAASsD,OAC/B,CAAC,CAACxB,AATQwF,EASFE,KAAK,EAAEC,QACf,CAAC,CAAC3F,AAVQwF,EAUFE,KAAK,EAAE5C,MACvB,GAEK8C,GAAG,CAAC,SAAUJ,CAAC,EAShB,MAAO,CACH5F,GAPO,AAACI,CAAAA,AAFEwF,EAEI5D,OAAO,EAAER,QAAQP,aAAa,SAC5Cb,AAHUwF,EAGJ5D,OAAO,EAAER,QAAQP,aAAa,UACpCb,AAJUwF,EAIJ5D,OAAO,EAAER,QAAQP,aAAa,WAAa,EAAC,EACjDtB,OAAO,CAACE,EAASqB,GAAG,CAAE,IACtBvB,OAAO,CAAC,QAAS,IACjBA,OAAO,CAAC,IAAK,IAGdqD,EAAG5C,AAVOwF,EAUDE,KAAK,EAAEC,QAAU,EAC1B9C,EAAG7C,AAXOwF,EAWDE,KAAK,EAAE5C,QAAU,CAC9B,CACJ,GAEKnD,MAAM,CAAC,SAAUkG,CAAS,CAAEC,CAAK,CAAEC,CAAG,EACvC,MAAOF,AAAiB,KAAjBA,EAAUjG,EAAE,EACfiG,AAAgD,KAAhDA,EAAUjG,EAAE,CAACC,OAAO,CAAC,wBACrB,CAACkG,EAAIC,IAAI,CAAC,SAAUC,CAAS,CAAEC,CAAU,EACrC,OAAOD,EAAUrG,EAAE,GAAKiG,EAAUjG,EAAE,EAAIsG,EAAaJ,CACzD,EACR,GACKrF,OAAO,CAAC,SAAUoF,CAAS,EAC5B,IAAMjG,EAAKiG,EAAUjG,EAAE,AACvB9B,CAAAA,CAAQ,CAAC8B,EAAG,CAAC+F,MAAM,CAAG,EAAIE,EAAUjD,CAAC,CACrC9E,CAAQ,CAAC8B,EAAG,CAACkD,MAAM,CAAG,EAAI+C,EAAUhD,CAAC,CACrC/E,CAAQ,CAAC8B,EAAG,CAACuG,eAAe,CAAC,mBACjC,EAER,CAU6B,IAwHvBC,EAAKjJ,GACXiJ,CAAAA,EAAEtI,QAAQ,CA3HNA,EA4HJuI,AArrBA,SAAiBC,CAAU,CAAEC,CAAW,CAAEC,CAAgB,EACtD,IAAMC,EAAaF,EAAYxJ,SAAS,CAAC2J,UAAU,CAAEC,EAAaF,EAAW1J,SAAS,AACjF4J,CAAAA,EAAWlE,0BAA0B,GACtCnF,EAASgJ,EAAY,YAAa9G,GAClClC,EAASgJ,EAAY,SAAU/F,GAC/B9C,EAAOkJ,EAAY,CACflE,2BAA4Bd,CAChC,GACArE,EAASmJ,EAAY,YAAaxF,GAClC3D,EAASiJ,EAAa,SAAUpE,GAChCtE,EAAK0I,EAAYxJ,SAAS,CAAE,WAAYkI,GAExC3H,EAASiJ,EAAa,cAAehB,GACrCjI,EAASiJ,EAAa,kBAAmBhB,GACzC9H,EAAO+I,EAAiBzJ,SAAS,CAAE,CAC/B8E,WAAY6B,CAChB,GACApG,EAASkJ,EAAkB,eAAgBtF,GAEnD,EAkqB+BkF,EAAEQ,KAAK,CAAER,EAAES,MAAM,CAAET,EAAEU,WAAW,EAClC,IAAMC,EAAqB5J,WAE/C4J,KAAoBC,OAAO"}