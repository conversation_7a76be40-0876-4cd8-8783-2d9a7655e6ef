{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/sankey\n * @requires highcharts\n *\n * Sankey diagram module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Series/NodesComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto, prototype: { pointClass: { prototype: pointProto } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { defined, extend, find, merge, pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar NodesComposition;\n(function (NodesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(PointClass, SeriesClass) {\n        const pointProto = PointClass.prototype, seriesProto = SeriesClass.prototype;\n        pointProto.setNodeState = setNodeState;\n        pointProto.setState = setNodeState;\n        pointProto.update = updateNode;\n        seriesProto.destroy = destroy;\n        seriesProto.setData = setData;\n        return SeriesClass;\n    }\n    NodesComposition.compose = compose;\n    /**\n     * Create a single node that holds information on incoming and outgoing\n     * links.\n     * @private\n     */\n    function createNode(id) {\n        const PointClass = this.pointClass, findById = (nodes, id) => find(nodes, (node) => node.id === id);\n        let node = findById(this.nodes, id), options;\n        if (!node) {\n            options = this.options.nodes && findById(this.options.nodes, id);\n            const newNode = new PointClass(this, extend({\n                className: 'highcharts-node',\n                isNode: true,\n                id: id,\n                y: 1 // Pass isNull test\n            }, options));\n            newNode.linksTo = [];\n            newNode.linksFrom = [];\n            /**\n             * Return the largest sum of either the incoming or outgoing links.\n             * @private\n             */\n            newNode.getSum = function () {\n                let sumTo = 0, sumFrom = 0;\n                newNode.linksTo.forEach((link) => {\n                    sumTo += link.weight || 0;\n                });\n                newNode.linksFrom.forEach((link) => {\n                    sumFrom += link.weight || 0;\n                });\n                return Math.max(sumTo, sumFrom);\n            };\n            /**\n             * Get the offset in weight values of a point/link.\n             * @private\n             */\n            newNode.offset = function (point, coll) {\n                let offset = 0;\n                for (let i = 0; i < newNode[coll].length; i++) {\n                    if (newNode[coll][i] === point) {\n                        return offset;\n                    }\n                    offset += newNode[coll][i].weight;\n                }\n            };\n            // Return true if the node has a shape, otherwise all links are\n            // outgoing.\n            newNode.hasShape = function () {\n                let outgoing = 0;\n                newNode.linksTo.forEach((link) => {\n                    if (link.outgoing) {\n                        outgoing++;\n                    }\n                });\n                return (!newNode.linksTo.length ||\n                    outgoing !== newNode.linksTo.length);\n            };\n            newNode.index = this.nodes.push(newNode) - 1;\n            node = newNode;\n        }\n        node.formatPrefix = 'node';\n        // For use in formats\n        node.name = node.name || node.options.id || '';\n        // Mass is used in networkgraph:\n        node.mass = pick(\n        // Node:\n        node.options.mass, node.options.marker && node.options.marker.radius, \n        // Series:\n        this.options.marker && this.options.marker.radius, \n        // Default:\n        4);\n        return node;\n    }\n    NodesComposition.createNode = createNode;\n    /**\n     * Destroy all nodes and links.\n     * @private\n     */\n    function destroy() {\n        // Nodes must also be destroyed (#8682, #9300)\n        this.data = []\n            .concat(this.points || [], this.nodes);\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    NodesComposition.destroy = destroy;\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects but\n     * pushed to the this.nodes array.\n     * @private\n     */\n    function generatePoints() {\n        const chart = this.chart, nodeLookup = {};\n        seriesProto.generatePoints.call(this);\n        if (!this.nodes) {\n            this.nodes = []; // List of Point-like node items\n        }\n        this.colorCounter = 0;\n        // Reset links from previous run\n        this.nodes.forEach((node) => {\n            node.linksFrom.length = 0;\n            node.linksTo.length = 0;\n            node.level = node.options.level;\n        });\n        // Create the node list and set up links\n        this.points.forEach((point) => {\n            if (defined(point.from)) {\n                if (!nodeLookup[point.from]) {\n                    nodeLookup[point.from] = this.createNode(point.from);\n                }\n                nodeLookup[point.from].linksFrom.push(point);\n                point.fromNode = nodeLookup[point.from];\n                // Point color defaults to the fromNode's color\n                if (chart.styledMode) {\n                    point.colorIndex = pick(point.options.colorIndex, nodeLookup[point.from].colorIndex);\n                }\n                else {\n                    point.color =\n                        point.options.color || nodeLookup[point.from].color;\n                }\n            }\n            if (defined(point.to)) {\n                if (!nodeLookup[point.to]) {\n                    nodeLookup[point.to] = this.createNode(point.to);\n                }\n                nodeLookup[point.to].linksTo.push(point);\n                point.toNode = nodeLookup[point.to];\n            }\n            point.name = point.name || point.id; // For use in formats\n        }, this);\n        // Store lookup table for later use\n        this.nodeLookup = nodeLookup;\n    }\n    NodesComposition.generatePoints = generatePoints;\n    /**\n     * Destroy all nodes on setting new data\n     * @private\n     */\n    function setData() {\n        if (this.nodes) {\n            this.nodes.forEach((node) => {\n                node.destroy();\n            });\n            this.nodes.length = 0;\n        }\n        seriesProto.setData.apply(this, arguments);\n    }\n    /**\n     * When hovering node, highlight all connected links. When hovering a link,\n     * highlight all connected nodes.\n     * @private\n     */\n    function setNodeState(state) {\n        const args = arguments, others = this.isNode ? this.linksTo.concat(this.linksFrom) :\n            [this.fromNode, this.toNode];\n        if (state !== 'select') {\n            others.forEach((linkOrNode) => {\n                if (linkOrNode && linkOrNode.series) {\n                    pointProto.setState.apply(linkOrNode, args);\n                    if (!linkOrNode.isNode) {\n                        if (linkOrNode.fromNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.fromNode, args);\n                        }\n                        if (linkOrNode.toNode && linkOrNode.toNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.toNode, args);\n                        }\n                    }\n                }\n            });\n        }\n        pointProto.setState.apply(this, args);\n    }\n    NodesComposition.setNodeState = setNodeState;\n    /**\n     * When updating a node, don't update `series.options.data`, but\n     * `series.options.nodes`\n     * @private\n     */\n    function updateNode(options, redraw, animation, runEvent) {\n        const nodes = this.series.options.nodes, data = this.series.options.data, dataLength = data?.length || 0, linkConfig = data?.[this.index];\n        pointProto.update.call(this, options, this.isNode ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        if (this.isNode) {\n            // `this.index` refers to `series.nodes`, not `options.nodes` array\n            const nodeIndex = (nodes || [])\n                .reduce(// Array.findIndex needs a polyfill\n            (prevIndex, n, index) => (this.id === n.id ? index : prevIndex), -1), \n            // Merge old config with new config. New config is stored in\n            // options.data, because of default logic in point.update()\n            nodeConfig = merge(nodes && nodes[nodeIndex] || {}, data?.[this.index] || {});\n            // Restore link config\n            if (data) {\n                if (linkConfig) {\n                    data[this.index] = linkConfig;\n                }\n                else {\n                    // Remove node from config if there's more nodes than links\n                    data.length = dataLength;\n                }\n            }\n            // Set node config\n            if (nodes) {\n                if (nodeIndex >= 0) {\n                    nodes[nodeIndex] = nodeConfig;\n                }\n                else {\n                    nodes.push(nodeConfig);\n                }\n            }\n            else {\n                this.series.options.nodes = [nodeConfig];\n            }\n            if (pick(redraw, true)) {\n                this.series.chart.redraw(animation);\n            }\n        }\n    }\n    NodesComposition.updateNode = updateNode;\n})(NodesComposition || (NodesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_NodesComposition = (NodesComposition);\n\n;// external [\"../highcharts.js\",\"default\",\"Point\"]\nconst external_highcharts_src_js_default_Point_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Point;\nvar external_highcharts_src_js_default_Point_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Point_namespaceObject);\n;// ./code/es-modules/Series/Sankey/SankeyPoint.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { column: ColumnSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { defined: SankeyPoint_defined } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass SankeyPoint extends ColumnSeries.prototype.pointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    applyOptions(options, x) {\n        external_highcharts_src_js_default_Point_default().prototype.applyOptions.call(this, options, x);\n        // Treat point.level as a synonym of point.column\n        if (SankeyPoint_defined(this.options.level)) {\n            this.options.column = this.column = this.options.level;\n        }\n        return this;\n    }\n    /**\n     * @private\n     */\n    getClassName() {\n        return (this.isNode ? 'highcharts-node ' : 'highcharts-link ') +\n            external_highcharts_src_js_default_Point_default().prototype.getClassName.call(this);\n    }\n    /**\n     * If there are incoming links, place it to the right of the\n     * highest order column that links to this one.\n     *\n     * @private\n     */\n    getFromNode() {\n        const node = this;\n        let fromColumn = -1, fromNode;\n        for (let i = 0; i < node.linksTo.length; i++) {\n            const point = node.linksTo[i];\n            if (point.fromNode.column > fromColumn &&\n                point.fromNode !== node // #16080\n            ) {\n                fromNode = point.fromNode;\n                fromColumn = fromNode.column;\n            }\n        }\n        return { fromNode, fromColumn };\n    }\n    /**\n     * Calculate node.column if it's not set by user\n     * @private\n     */\n    setNodeColumn() {\n        const node = this;\n        if (!SankeyPoint_defined(node.options.column)) {\n            // No links to this node, place it left\n            if (node.linksTo.length === 0) {\n                node.column = 0;\n            }\n            else {\n                node.column = node.getFromNode().fromColumn + 1;\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    isValid() {\n        return this.isNode || typeof this.weight === 'number';\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeyPoint = (SankeyPoint);\n\n;// ./code/es-modules/Series/Sankey/SankeySeriesDefaults.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A sankey diagram is a type of flow diagram, in which the width of the\n * link between two nodes is shown proportionally to the flow quantity.\n *\n * @sample highcharts/demo/sankey-diagram/\n *         Sankey diagram\n * @sample highcharts/plotoptions/sankey-inverted/\n *         Inverted sankey diagram\n * @sample highcharts/plotoptions/sankey-outgoing\n *         Sankey diagram with outgoing links\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts\n * @excluding    animationLimit, boostBlending, boostThreshold, borderRadius,\n *               crisp, cropThreshold, colorAxis, colorKey, dataSorting, depth,\n *               dragDrop, edgeColor, edgeWidth, findNearestPointBy, grouping,\n *               groupPadding, groupZPadding, legendSymbolColor, maxPointWidth,\n *               minPointLength, negativeColor, pointInterval,\n *               pointIntervalUnit, pointPadding, pointPlacement, pointRange,\n *               pointStart, pointWidth, shadow, softThreshold, stacking,\n *               threshold, zoneAxis, zones\n * @requires     modules/sankey\n * @optionparent plotOptions.sankey\n *\n * @private\n */\nconst SankeySeriesDefaults = {\n    borderWidth: 0,\n    colorByPoint: true,\n    /**\n     * Higher numbers makes the links in a sankey diagram or dependency\n     * wheelrender more curved. A `curveFactor` of 0 makes the lines\n     * straight.\n     *\n     * @private\n     */\n    curveFactor: 0.33,\n    /**\n     * Options for the data labels appearing on top of the nodes and links.\n     * For sankey charts, data labels are visible for the nodes by default,\n     * but hidden for links. This is controlled by modifying the\n     * `nodeFormat`, and the `format` that applies to links and is an empty\n     * string by default.\n     *\n     * @declare Highcharts.SeriesSankeyDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        enabled: true,\n        backgroundColor: 'none', // Enable padding\n        crop: false,\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _nodes_ in the sankey diagram. By\n         * default the `nodeFormatter` returns `{point.name}`.\n         *\n         * @sample highcharts/plotoptions/sankey-link-datalabels/\n         *         Node and link data labels\n         *\n         * @type {string}\n         */\n        nodeFormat: void 0,\n        /**\n         * Callback to format data labels for _nodes_ in the sankey diagram.\n         * The `nodeFormat` option takes precedence over the\n         * `nodeFormatter`.\n         *\n         * @type  {Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction}\n         * @since 6.0.2\n         */\n        nodeFormatter: function () {\n            return this.point.name;\n        },\n        format: void 0,\n        /**\n         * @type {Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction}\n         */\n        formatter: function () {\n            return;\n        },\n        inside: true\n    },\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not node and link options.\n     *\n     * @sample highcharts/demo/sunburst\n     *         Sunburst chart\n     *\n     * @type      {Array<*>}\n     * @since     7.1.0\n     * @apioption plotOptions.sankey.levels\n     */\n    /**\n     * Can set `borderColor` on all nodes which lay on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @apioption plotOptions.sankey.levels.borderColor\n     */\n    /**\n     * Can set `borderWidth` on all nodes which lay on the same level.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sankey.levels.borderWidth\n     */\n    /**\n     * Can set `color` on all nodes which lay on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.sankey.levels.color\n     */\n    /**\n     * Can set `colorByPoint` on all nodes which lay on the same level.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption plotOptions.sankey.levels.colorByPoint\n     */\n    /**\n     * Can set `dataLabels` on all points which lay on the same level.\n     *\n     * @extends   plotOptions.sankey.dataLabels\n     * @apioption plotOptions.sankey.levels.dataLabels\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sankey.levels.level\n     */\n    /**\n     * Can set `linkOpacity` on all points which lay on the same level.\n     *\n     * @type      {number}\n     * @default   0.5\n     * @apioption plotOptions.sankey.levels.linkOpacity\n     */\n    /**\n     * Can set `states` on all nodes and points which lay on the same level.\n     *\n     * @extends   plotOptions.sankey.states\n     * @apioption plotOptions.sankey.levels.states\n     */\n    /**\n     * Determines color mode for sankey links. Available options:\n     *\n     * - `from` color of the sankey link will be the same as the 'from node'\n     *\n     * - `gradient` color of the sankey link will be set to gradient between\n     * colors of 'from node' and 'to node'\n     *\n     * - `to` color of the sankey link will be same as the 'to node'.\n     *\n     * @sample highcharts/demo/vertical-sankey\n     *         Vertical sankey diagram with gradients\n     * @sample highcharts/series-sankey/link-color-mode\n     *         Sankey diagram with gradients and explanation\n     *\n     * @type      {('from'|'gradient'|'to')}\n     * @since     11.2.0\n     */\n    linkColorMode: 'from',\n    /**\n     * Opacity for the links between nodes in the sankey diagram.\n     *\n     * @private\n     */\n    linkOpacity: 0.5,\n    /**\n     * Opacity for the nodes in the sankey diagram.\n     *\n     * @private\n     */\n    opacity: 1,\n    /**\n     * The minimal width for a line of a sankey. By default,\n     * 0 values are not shown.\n     *\n     * @sample highcharts/plotoptions/sankey-minlinkwidth\n     *         Sankey diagram with minimal link height\n     *\n     * @type      {number}\n     * @since     7.1.3\n     * @default   0\n     * @apioption plotOptions.sankey.minLinkWidth\n     *\n     * @private\n     */\n    minLinkWidth: 0,\n    /**\n     * Determines which side of the chart the nodes are to be aligned to. When\n     * the chart is inverted, `top` aligns to the left and `bottom` to the\n     * right.\n     *\n     * @sample highcharts/plotoptions/sankey-nodealignment\n     *         Node alignment demonstrated\n     *\n     * @type      {'top'|'center'|'bottom'}\n     * @apioption plotOptions.sankey.nodeAlignment\n     */\n    nodeAlignment: 'center',\n    /**\n     * The pixel width of each node in a sankey diagram or dependency wheel, or\n     * the height in case the chart is inverted.\n     *\n     * Can be a number or a percentage string.\n     *\n     * Sankey series also support setting it to `auto`. With this setting, the\n     * nodes are sized to fill up the plot area in the longitudinal direction,\n     * regardless of the number of levels.\n     *\n     * @see    [sankey.nodeDistance](#nodeDistance)\n     * @sample highcharts/series-sankey/node-distance\n     *         Sankey with auto node width combined with node distance\n     * @sample highcharts/series-organization/node-distance\n     *         Organization chart with node distance of 50%\n     *\n     * @type {number|string}\n     */\n    nodeWidth: 20,\n    /**\n     * The padding between nodes in a sankey diagram or dependency wheel, in\n     * pixels. For sankey charts, this applies to the nodes of the same column,\n     * so vertical distance by default, or horizontal distance in an inverted\n     * (vertical) sankey.\n     *\n     * If the number of nodes is so great that it is impossible to lay them out\n     * within the plot area with the given `nodePadding`, they will be rendered\n     * with a smaller padding as a strategy to avoid overflow.\n     */\n    nodePadding: 10,\n    /**\n     * The distance between nodes in a sankey diagram in the longitudinal\n     * direction. The longitudinal direction means the direction that the chart\n     * flows - in a horizontal chart the distance is horizontal, in an inverted\n     * chart (vertical), the distance is vertical.\n     *\n     * If a number is given, it denotes pixels. If a percentage string is given,\n     * the distance is a percentage of the rendered node width. A `nodeDistance`\n     * of `100%` will render equal widths for the nodes and the gaps between\n     * them.\n     *\n     * This option applies only when the `nodeWidth` option is `auto`, making\n     * the node width respond to the number of columns.\n     *\n     * @since 11.4.0\n     * @sample highcharts/series-sankey/node-distance\n     *         Sankey with dnode distance of 100% means equal to node width\n     * @sample highcharts/series-organization/node-distance\n     *         Organization chart with node distance of 50%\n     * @type   {number|string}\n     */\n    nodeDistance: 30,\n    showInLegend: false,\n    states: {\n        hover: {\n            /**\n             * Opacity for the links between nodes in the sankey diagram in\n             * hover mode.\n             */\n            linkOpacity: 1,\n            /**\n             * Opacity for the nodes in the sankey diagram in hover mode.\n             */\n            opacity: 1\n        },\n        /**\n         * The opposite state of a hover for a single point node/link.\n         *\n         * @declare Highcharts.SeriesStatesInactiveOptionsObject\n         */\n        inactive: {\n            /**\n             * Opacity for the links between nodes in the sankey diagram in\n             * inactive mode.\n             */\n            linkOpacity: 0.1,\n            /**\n             * Opacity of the nodes in the sankey diagram in inactive mode.\n             */\n            opacity: 0.1,\n            /**\n             * Animation when not hovering over the marker.\n             *\n             * @type      {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             * @apioption plotOptions.series.states.inactive.animation\n             */\n            animation: {\n                /** @internal */\n                duration: 50\n            }\n        }\n    },\n    tooltip: {\n        /**\n         * A callback for defining the format for _nodes_ in the chart's\n         * tooltip, as opposed to links.\n         *\n         * @type      {Highcharts.FormatterCallbackFunction<Highcharts.SankeyNodeObject>}\n         * @since     6.0.2\n         * @apioption plotOptions.sankey.tooltip.nodeFormatter\n         */\n        /**\n         * Whether the tooltip should follow the pointer or stay fixed on\n         * the item.\n         */\n        followPointer: true,\n        headerFormat: '<span style=\"font-size: 0.8em\">{series.name}</span><br/>',\n        pointFormat: '{point.fromNode.name} \\u2192 {point.toNode.name}: <b>{point.weight}</b><br/>',\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _nodes_ in tooltip of a diagram\n         * series, as opposed to links.\n         */\n        nodeFormat: '{point.name}: <b>{point.sum}</b><br/>'\n    }\n};\n/**\n * A `sankey` series. If the [type](#series.sankey.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.sankey\n * @excluding animationLimit, boostBlending, boostThreshold, borderColor,\n *            borderRadius, borderWidth, crisp, cropThreshold, dataParser,\n *            dataURL, depth, dragDrop, edgeColor, edgeWidth,\n *            findNearestPointBy, getExtremesFromAll, grouping, groupPadding,\n *            groupZPadding, label, maxPointWidth, negativeColor, pointInterval,\n *            pointIntervalUnit, pointPadding, pointPlacement, pointRange,\n *            pointStart, pointWidth, shadow, softThreshold, stacking,\n *            threshold, zoneAxis, zones, dataSorting\n * @product   highcharts\n * @requires  modules/sankey\n * @apioption series.sankey\n */\n/**\n * A collection of options for the individual nodes. The nodes in a sankey\n * diagram are auto-generated instances of `Highcharts.Point`, but options can\n * be applied here and linked by the `id`.\n *\n * @sample highcharts/css/sankey/\n *         Sankey diagram with node options\n *\n * @declare   Highcharts.SeriesSankeyNodesOptionsObject\n * @type      {Array<*>}\n * @product   highcharts\n * @apioption series.sankey.nodes\n */\n/**\n * The id of the auto-generated node, referring to the `from` or `to` setting of\n * the link.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.nodes.id\n */\n/**\n * The color of the auto generated node.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts\n * @apioption series.sankey.nodes.color\n */\n/**\n * The color index of the auto generated node, especially for use in styled\n * mode.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.sankey.nodes.colorIndex\n */\n/**\n * An optional column index of where to place the node. The default behaviour is\n * to place it next to the preceding node. Note that this option name is\n * counter intuitive in inverted charts, like for example an organization chart\n * rendered top down. In this case the \"columns\" are horizontal.\n *\n * @sample highcharts/plotoptions/sankey-node-column/\n *         Specified node column\n *\n * @type      {number}\n * @since     6.0.5\n * @product   highcharts\n * @apioption series.sankey.nodes.column\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.sankey.dataLabels](#series.sankey.dataLabels).\n *\n * @extends   plotOptions.sankey.dataLabels\n * @apioption series.sankey.nodes.dataLabels\n */\n/**\n * The height of the node.\n *\n * @sample highcharts/series-sankey/height/\n *         Sankey diagram with height options\n *\n * @type      {number}\n * @since     11.3.0\n * @apioption series.sankey.nodes.height\n */\n/**\n * An optional level index of where to place the node. The default behaviour is\n * to place it next to the preceding node. Alias of `nodes.column`, but in\n * inverted sankeys and org charts, the levels are laid out as rows.\n *\n * @type      {number}\n * @since     7.1.0\n * @product   highcharts\n * @apioption series.sankey.nodes.level\n */\n/**\n * The name to display for the node in data labels and tooltips. Use this when\n * the name is different from the `id`. Where the id must be unique for each\n * node, this is not necessary for the name.\n *\n * @sample highcharts/css/sankey/\n *         Sankey diagram with node options\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.nodes.name\n */\n/**\n * This option is deprecated, use\n * [offsetHorizontal](#series.sankey.nodes.offsetHorizontal) and\n * [offsetVertical](#series.sankey.nodes.offsetVertical) instead.\n *\n * In a horizontal layout, the vertical offset of a node in terms of weight.\n * Positive values shift the node downwards, negative shift it upwards. In a\n * vertical layout, like organization chart, the offset is horizontal.\n *\n * If a percentage string is given, the node is offset by the percentage of the\n * node size plus `nodePadding`.\n *\n * @deprecated\n * @type      {number|string}\n * @default   0\n * @since     6.0.5\n * @product   highcharts\n * @apioption series.sankey.nodes.offset\n */\n/**\n * The horizontal offset of a node. Positive values shift the node right,\n * negative shift it left.\n *\n * If a percentage string is given, the node is offset by the percentage of the\n * node size.\n *\n * @sample highcharts/plotoptions/sankey-node-column/\n *         Specified node offset\n *\n * @type      {number|string}\n * @since 9.3.0\n * @product   highcharts\n * @apioption series.sankey.nodes.offsetHorizontal\n */\n/**\n * The vertical offset of a node. Positive values shift the node down,\n * negative shift it up.\n *\n * If a percentage string is given, the node is offset by the percentage of the\n * node size.\n *\n * @sample highcharts/plotoptions/sankey-node-column/\n *         Specified node offset\n *\n * @type      {number|string}\n * @since 9.3.0\n * @product   highcharts\n * @apioption series.sankey.nodes.offsetVertical\n */\n/**\n * An array of data points for the series. For the `sankey` series type,\n * points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.area.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2',\n *         weight: 2\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3',\n *         weight: 5\n *     }]\n *  ```\n *\n *  When you provide the data as tuples, the keys option has to be set as well.\n *\n *  ```js\n *     keys: ['from', 'to', 'weight'],\n *     data: [\n *         ['Category1', 'Category2', 2],\n *         ['Category1', 'Category3', 5]\n *     ]\n *  ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @declare   Highcharts.SeriesSankeyPointOptionsObject\n * @type      {Array<*>|Array<Array<(string|number)>>}\n * @extends   series.line.data\n * @excluding dragDrop, drilldown, marker, x, y\n * @product   highcharts\n * @apioption series.sankey.data\n */\n/**\n * The color for the individual _link_. By default, the link color is the same\n * as the node it extends from. The `series.fillOpacity` option also applies to\n * the points, so when setting a specific link color, consider setting the\n * `fillOpacity` to 1.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts\n * @apioption series.sankey.data.color\n */\n/**\n * @type      {Highcharts.SeriesSankeyDataLabelsOptionsObject|Array<Highcharts.SeriesSankeyDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.sankey.data.dataLabels\n */\n/**\n * The node that the link runs from.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.data.from\n */\n/**\n * The node that the link runs to.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.sankey.data.to\n */\n/**\n * Whether the link goes out of the system.\n *\n * @sample highcharts/plotoptions/sankey-outgoing\n *         Sankey chart with outgoing links\n *\n * @type      {boolean}\n * @default   false\n * @product   highcharts\n * @apioption series.sankey.data.outgoing\n */\n/**\n * The weight of the link.\n *\n * @type      {number|null}\n * @product   highcharts\n * @apioption series.sankey.data.weight\n */\n''; // Adds doclets above to transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeySeriesDefaults = (SankeySeriesDefaults);\n\n;// ./code/es-modules/Series/Sankey/SankeyColumnComposition.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined: SankeyColumnComposition_defined, getAlignFactor, relativeLength } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar SankeyColumnComposition;\n(function (SankeyColumnComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * SankeyColumn Composition\n     * @private\n     * @function Highcharts.SankeyColumn#compose\n     *\n     * @param {Array<SankeyPoint>} points\n     * The array of nodes\n     * @param {SankeySeries} series\n     * Series connected to column\n     * @return {ArrayComposition} SankeyColumnArray\n     */\n    function compose(points, series) {\n        const sankeyColumnArray = points;\n        sankeyColumnArray.sankeyColumn =\n            new SankeyColumnAdditions(sankeyColumnArray, series);\n        return sankeyColumnArray;\n    }\n    SankeyColumnComposition.compose = compose;\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    class SankeyColumnAdditions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(points, series) {\n            this.points = points;\n            this.series = series;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Calculate translation factor used in column and nodes distribution\n         * @private\n         * @function Highcharts.SankeyColumn#getTranslationFactor\n         *\n         * @param {SankeySeries} series\n         * The Series\n         * @return {number} TranslationFactor\n         * Translation Factor\n         */\n        getTranslationFactor(series) {\n            const column = this.points, nodes = column.slice(), chart = series.chart, minLinkWidth = series.options.minLinkWidth || 0;\n            let skipPoint, factor = 0, i, remainingHeight = ((chart.plotSizeY || 0) -\n                (series.options.borderWidth || 0) -\n                (column.length - 1) * series.nodePadding);\n            // Because the minLinkWidth option doesn't obey the direct\n            // translation, we need to run translation iteratively, check\n            // node heights, remove those nodes affected by minLinkWidth,\n            // check again, etc.\n            while (column.length) {\n                factor = remainingHeight / column.sankeyColumn.sum();\n                skipPoint = false;\n                i = column.length;\n                while (i--) {\n                    if (column[i].getSum() * factor < minLinkWidth) {\n                        column.splice(i, 1);\n                        remainingHeight =\n                            Math.max(0, remainingHeight - minLinkWidth);\n                        skipPoint = true;\n                    }\n                }\n                if (!skipPoint) {\n                    break;\n                }\n            }\n            // Re-insert original nodes\n            column.length = 0;\n            for (const node of nodes) {\n                column.push(node);\n            }\n            return factor;\n        }\n        /**\n         * Get the top position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} top\n         * The top position of the column\n         */\n        top(factor) {\n            const series = this.series, nodePadding = series.nodePadding, height = this.points.reduce((height, node) => {\n                if (height > 0) {\n                    height += nodePadding;\n                }\n                const nodeHeight = Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                height += nodeHeight;\n                return height;\n            }, 0);\n            // Node alignment option handling #19096\n            return getAlignFactor(series.options.nodeAlignment || 'center') * ((series.chart.plotSizeY || 0) - height);\n        }\n        /**\n         * Get the left position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} left\n         * The left position of the column\n         */\n        left(factor) {\n            const series = this.series, chart = series.chart, equalNodes = series.options.equalNodes, maxNodesLength = (chart.inverted ? chart.plotHeight : chart.plotWidth), nodePadding = series.nodePadding, width = this.points.reduce((width, node) => {\n                if (width > 0) {\n                    width += nodePadding;\n                }\n                const nodeWidth = equalNodes ?\n                    maxNodesLength / node.series.nodes.length -\n                        nodePadding :\n                    Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                width += nodeWidth;\n                return width;\n            }, 0);\n            return ((chart.plotSizeX || 0) - Math.round(width)) / 2;\n        }\n        /**\n         * Calculate sum of all nodes inside specific column\n         * @private\n         * @function Highcharts.SankeyColumn#sum\n         *\n         * @param {ArrayComposition} this\n         * Sankey Column Array\n         *\n         * @return {number} sum\n         * Sum of all nodes inside column\n         */\n        sum() {\n            return this.points.reduce((sum, node) => (sum + node.getSum()), 0);\n        }\n        /**\n         * Get the offset in pixels of a node inside the column\n         * @private\n         * @function Highcharts.SankeyColumn#offset\n         *\n         * @param {SankeyPoint} node\n         * Sankey node\n         * @param {number} factor\n         * Translation Factor\n         * @return {number} offset\n         * Offset of a node inside column\n         */\n        offset(node, factor) {\n            const column = this.points, series = this.series, nodePadding = series.nodePadding;\n            let offset = 0, totalNodeOffset;\n            if (series.is('organization') && node.hangsFrom) {\n                return {\n                    absoluteTop: node.hangsFrom.nodeY\n                };\n            }\n            for (let i = 0; i < column.length; i++) {\n                const sum = column[i].getSum();\n                const height = Math.max(sum * factor, series.options.minLinkWidth || 0);\n                const directionOffset = node.options[series.chart.inverted ?\n                    'offsetHorizontal' :\n                    'offsetVertical'], optionOffset = node.options.offset || 0;\n                if (sum) {\n                    totalNodeOffset = height + nodePadding;\n                }\n                else {\n                    // If node sum equals 0 nodePadding is missed #12453\n                    totalNodeOffset = 0;\n                }\n                if (column[i] === node) {\n                    return {\n                        relativeTop: offset + (SankeyColumnComposition_defined(directionOffset) ?\n                            // `directionOffset` is a percent of the node\n                            // height\n                            relativeLength(directionOffset, height) :\n                            relativeLength(optionOffset, totalNodeOffset))\n                    };\n                }\n                offset += totalNodeOffset;\n            }\n        }\n    }\n    SankeyColumnComposition.SankeyColumnAdditions = SankeyColumnAdditions;\n})(SankeyColumnComposition || (SankeyColumnComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeyColumnComposition = (SankeyColumnComposition);\n\n;// external [\"../highcharts.js\",\"default\",\"Color\"]\nconst external_highcharts_src_js_default_Color_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Color;\nvar external_highcharts_src_js_default_Color_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Color_namespaceObject);\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend: TreeUtilities_extend, isArray, isNumber, isObject, merge: TreeUtilities_merge, pick: TreeUtilities_pick, relativeLength: TreeUtilities_relativeLength } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return external_highcharts_src_js_default_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = TreeUtilities_pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = TreeUtilities_pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = TreeUtilities_pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = TreeUtilities_pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = TreeUtilities_extend({}, options);\n        TreeUtilities_extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = TreeUtilities_pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = TreeUtilities_pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return TreeUtilities_relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n;// external [\"../highcharts.js\",\"default\",\"SVGElement\"]\nconst external_highcharts_src_js_default_SVGElement_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGElement;\nvar external_highcharts_src_js_default_SVGElement_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGElement_namespaceObject);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (external_highcharts_src_js_default_default());\nconst { addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (external_highcharts_src_js_default_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Sankey/SankeySeries.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\nconst { column: SankeySeries_ColumnSeries, line: LineSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { parse: color } = (external_highcharts_src_js_default_Color_default());\n\nconst { getLevelOptions: SankeySeries_getLevelOptions, getNodeWidth: SankeySeries_getNodeWidth } = Series_TreeUtilities;\n\nconst { clamp, crisp, extend: SankeySeries_extend, isObject: SankeySeries_isObject, merge: SankeySeries_merge, pick: SankeySeries_pick, relativeLength: SankeySeries_relativeLength, stableSort } = (external_highcharts_src_js_default_default());\n\n\nExtensions_TextPath.compose((external_highcharts_src_js_default_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.sankey\n *\n * @augments Highcharts.Series\n */\nclass SankeySeries extends SankeySeries_ColumnSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    static getDLOptions(params) {\n        const optionsPoint = (SankeySeries_isObject(params.optionsPoint) ?\n            params.optionsPoint.dataLabels :\n            {}), optionsLevel = (SankeySeries_isObject(params.level) ?\n            params.level.dataLabels :\n            {}), options = SankeySeries_merge({\n            style: {}\n        }, optionsLevel, optionsPoint);\n        return options;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create node columns by analyzing the nodes and the relations between\n     * incoming and outgoing links.\n     * @private\n     */\n    createNodeColumns() {\n        const columns = [];\n        for (const node of this.nodes) {\n            node.setNodeColumn();\n            if (!columns[node.column]) {\n                columns[node.column] =\n                    Sankey_SankeyColumnComposition.compose([], this);\n            }\n            columns[node.column].push(node);\n        }\n        // Fill in empty columns (#8865)\n        for (let i = 0; i < columns.length; i++) {\n            if (typeof columns[i] === 'undefined') {\n                columns[i] =\n                    Sankey_SankeyColumnComposition.compose([], this);\n            }\n        }\n        return columns;\n    }\n    /**\n     * Order the nodes, starting with the root node(s). (#9818)\n     * @private\n     */\n    order(node, level) {\n        const series = this;\n        // Prevents circular recursion:\n        if (typeof node.level === 'undefined') {\n            node.level = level;\n            for (const link of node.linksFrom) {\n                if (link.toNode) {\n                    series.order(link.toNode, level + 1);\n                }\n            }\n        }\n    }\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects\n     * but pushed to the this.nodes array.\n     * @private\n     */\n    generatePoints() {\n        Series_NodesComposition.generatePoints.apply(this, arguments);\n        if (this.orderNodes) {\n            for (const node of this.nodes) {\n                // Identify the root node(s)\n                if (node.linksTo.length === 0) {\n                    // Start by the root node(s) and recursively set the level\n                    // on all following nodes.\n                    this.order(node, 0);\n                }\n            }\n            stableSort(this.nodes, (a, b) => (a.level - b.level));\n        }\n    }\n    /**\n     * Overridable function to get node padding, overridden in dependency\n     * wheel series type.\n     * @private\n     */\n    getNodePadding() {\n        let nodePadding = this.options.nodePadding || 0;\n        // If the number of columns is so great that they will overflow with\n        // the given nodePadding, we sacrifice the padding in order to\n        // render all nodes within the plot area (#11917).\n        if (this.nodeColumns) {\n            const maxLength = this.nodeColumns.reduce((acc, col) => Math.max(acc, col.length), 0);\n            if (maxLength * nodePadding > this.chart.plotSizeY) {\n                nodePadding = this.chart.plotSizeY / maxLength;\n            }\n        }\n        return nodePadding;\n    }\n    /**\n     * Define hasData function for non-cartesian series.\n     * @private\n     * @return {boolean}\n     *         Returns true if the series has points at all.\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    pointAttribs(point, state) {\n        if (!point) {\n            return {};\n        }\n        const series = this, level = point.isNode ? point.level : point.fromNode.level, levelOptions = series.mapOptionsToLevel[level || 0] || {}, options = point.options, stateOptions = (levelOptions.states && levelOptions.states[state || '']) || {}, values = [\n            'colorByPoint',\n            'borderColor',\n            'borderWidth',\n            'linkOpacity',\n            'opacity'\n        ].reduce((obj, key) => {\n            obj[key] = SankeySeries_pick(stateOptions[key], options[key], levelOptions[key], series.options[key]);\n            return obj;\n        }, {}), color = SankeySeries_pick(stateOptions.color, options.color, values.colorByPoint ? point.color : levelOptions.color);\n        // Node attributes\n        if (point.isNode) {\n            return {\n                fill: color,\n                stroke: values.borderColor,\n                'stroke-width': values.borderWidth,\n                opacity: values.opacity\n            };\n        }\n        // Link attributes\n        return {\n            fill: color,\n            'fill-opacity': values.linkOpacity\n        };\n    }\n    drawTracker() {\n        SankeySeries_ColumnSeries.prototype.drawTracker.call(this, this.points);\n        SankeySeries_ColumnSeries.prototype.drawTracker.call(this, this.nodes);\n    }\n    drawPoints() {\n        SankeySeries_ColumnSeries.prototype.drawPoints.call(this, this.points);\n        SankeySeries_ColumnSeries.prototype.drawPoints.call(this, this.nodes);\n    }\n    drawDataLabels() {\n        SankeySeries_ColumnSeries.prototype.drawDataLabels.call(this, this.points);\n        SankeySeries_ColumnSeries.prototype.drawDataLabels.call(this, this.nodes);\n    }\n    /**\n     * Run pre-translation by generating the nodeColumns.\n     * @private\n     */\n    translate() {\n        this.generatePoints();\n        this.nodeColumns = this.createNodeColumns();\n        const series = this, chart = this.chart, options = this.options, nodeColumns = this.nodeColumns, columnCount = nodeColumns.length;\n        this.nodeWidth = SankeySeries_getNodeWidth(this, columnCount);\n        this.nodePadding = this.getNodePadding();\n        // Find out how much space is needed. Base it on the translation\n        // factor of the most spacious column.\n        this.translationFactor = nodeColumns.reduce((translationFactor, column) => Math.min(translationFactor, column.sankeyColumn.getTranslationFactor(series)), Infinity);\n        this.colDistance =\n            (chart.plotSizeX - this.nodeWidth -\n                options.borderWidth) / Math.max(1, nodeColumns.length - 1);\n        // Calculate level options used in sankey and organization\n        series.mapOptionsToLevel = SankeySeries_getLevelOptions({\n            // NOTE: if support for allowTraversingTree is added, then from\n            // should be the level of the root node.\n            from: 1,\n            levels: options.levels,\n            to: nodeColumns.length - 1, // Height of the tree\n            defaults: {\n                borderColor: options.borderColor,\n                borderRadius: options.borderRadius, // Organization series\n                borderWidth: options.borderWidth,\n                color: series.color,\n                colorByPoint: options.colorByPoint,\n                // NOTE: if support for allowTraversingTree is added, then\n                // levelIsConstant should be optional.\n                levelIsConstant: true,\n                linkColor: options.linkColor, // Organization series\n                linkLineWidth: options.linkLineWidth, // Organization series\n                linkOpacity: options.linkOpacity,\n                states: options.states\n            }\n        });\n        // First translate all nodes so we can use them when drawing links\n        for (const column of nodeColumns) {\n            for (const node of column) {\n                series.translateNode(node, column);\n            }\n        }\n        // Then translate links\n        for (const node of this.nodes) {\n            // Translate the links from this node\n            for (const linkPoint of node.linksFrom) {\n                // If weight is 0 - don't render the link path #12453,\n                // render null points (for organization chart)\n                if ((linkPoint.weight || linkPoint.isNull) && linkPoint.to) {\n                    series.translateLink(linkPoint);\n                    linkPoint.allowShadow = false;\n                }\n            }\n        }\n    }\n    /**\n     * Run translation operations for one link.\n     * @private\n     */\n    translateLink(point) {\n        const getY = (node, fromOrTo) => {\n            const linkTop = (node.offset(point, fromOrTo) *\n                translationFactor);\n            const y = Math.min(node.nodeY + linkTop, \n            // Prevent links from spilling below the node (#12014)\n            node.nodeY + (node.shapeArgs && node.shapeArgs.height || 0) - linkHeight);\n            return y;\n        };\n        const fromNode = point.fromNode, toNode = point.toNode, chart = this.chart, { inverted } = chart, translationFactor = this.translationFactor, options = this.options, linkColorMode = SankeySeries_pick(point.linkColorMode, options.linkColorMode), curvy = ((chart.inverted ? -this.colDistance : this.colDistance) *\n            options.curveFactor), nodeLeft = fromNode.nodeX, right = toNode.nodeX, outgoing = point.outgoing;\n        let linkHeight = Math.max(point.weight * translationFactor, this.options.minLinkWidth), fromY = getY(fromNode, 'linksFrom'), toY = getY(toNode, 'linksTo'), nodeW = this.nodeWidth, straight = right > nodeLeft + nodeW;\n        if (chart.inverted) {\n            fromY = chart.plotSizeY - fromY;\n            toY = (chart.plotSizeY || 0) - toY;\n            nodeW = -nodeW;\n            linkHeight = -linkHeight;\n            straight = nodeLeft > right;\n        }\n        point.shapeType = 'path';\n        point.linkBase = [\n            fromY,\n            fromY + linkHeight,\n            toY,\n            toY + linkHeight\n        ];\n        // Links going from left to right\n        if (straight && typeof toY === 'number') {\n            point.shapeArgs = {\n                d: [\n                    ['M', nodeLeft + nodeW, fromY],\n                    [\n                        'C',\n                        nodeLeft + nodeW + curvy,\n                        fromY,\n                        right - curvy,\n                        toY,\n                        right,\n                        toY\n                    ],\n                    ['L', right + (outgoing ? nodeW : 0), toY + linkHeight / 2],\n                    ['L', right, toY + linkHeight],\n                    [\n                        'C',\n                        right - curvy,\n                        toY + linkHeight,\n                        nodeLeft + nodeW + curvy,\n                        fromY + linkHeight,\n                        nodeLeft + nodeW, fromY + linkHeight\n                    ],\n                    ['Z']\n                ]\n            };\n            // Experimental: Circular links pointing backwards. In\n            // v6.1.0 this breaks the rendering completely, so even\n            // this experimental rendering is an improvement. #8218.\n            // @todo\n            // - Make room for the link in the layout\n            // - Automatically determine if the link should go up or\n            //   down.\n        }\n        else if (typeof toY === 'number') {\n            const bend = 20, vDist = chart.plotHeight - fromY - linkHeight, x1 = right - bend - linkHeight, x2 = right - bend, x3 = right, x4 = nodeLeft + nodeW, x5 = x4 + bend, x6 = x5 + linkHeight, fy1 = fromY, fy2 = fromY + linkHeight, fy3 = fy2 + bend, y4 = fy3 + vDist, y5 = y4 + bend, y6 = y5 + linkHeight, ty1 = toY, ty2 = ty1 + linkHeight, ty3 = ty2 + bend, cfy1 = fy2 - linkHeight * 0.7, cy2 = y5 + linkHeight * 0.7, cty1 = ty2 - linkHeight * 0.7, cx1 = x3 - linkHeight * 0.7, cx2 = x4 + linkHeight * 0.7;\n            point.shapeArgs = {\n                d: [\n                    ['M', x4, fy1],\n                    ['C', cx2, fy1, x6, cfy1, x6, fy3],\n                    ['L', x6, y4],\n                    ['C', x6, cy2, cx2, y6, x4, y6],\n                    ['L', x3, y6],\n                    ['C', cx1, y6, x1, cy2, x1, y4],\n                    ['L', x1, ty3],\n                    ['C', x1, cty1, cx1, ty1, x3, ty1],\n                    ['L', x3, ty2],\n                    ['C', x2, ty2, x2, ty2, x2, ty3],\n                    ['L', x2, y4],\n                    ['C', x2, y5, x2, y5, x3, y5],\n                    ['L', x4, y5],\n                    ['C', x5, y5, x5, y5, x5, y4],\n                    ['L', x5, fy3],\n                    ['C', x5, fy2, x5, fy2, x4, fy2],\n                    ['Z']\n                ]\n            };\n        }\n        // Place data labels in the middle\n        point.dlBox = {\n            x: nodeLeft + (right - nodeLeft + nodeW) / 2,\n            y: fromY + (toY - fromY) / 2,\n            height: linkHeight,\n            width: 0\n        };\n        // And set the tooltip anchor in the middle\n        point.tooltipPos = chart.inverted ? [\n            chart.plotSizeY - point.dlBox.y - linkHeight / 2,\n            chart.plotSizeX - point.dlBox.x\n        ] : [\n            point.dlBox.x,\n            point.dlBox.y + linkHeight / 2\n        ];\n        // Pass test in drawPoints. plotX/Y needs to be defined for dataLabels.\n        // #15863\n        point.y = point.plotY = 1;\n        point.x = point.plotX = 1;\n        if (!point.options.color) {\n            if (linkColorMode === 'from') {\n                point.color = fromNode.color;\n            }\n            else if (linkColorMode === 'to') {\n                point.color = toNode.color;\n            }\n            else if (linkColorMode === 'gradient') {\n                const fromColor = color(fromNode.color).get(), toColor = color(toNode.color).get();\n                point.color = {\n                    linearGradient: {\n                        x1: 1,\n                        x2: 0,\n                        y1: 0,\n                        y2: 0\n                    },\n                    stops: [\n                        [0, inverted ? fromColor : toColor],\n                        [1, inverted ? toColor : fromColor]\n                    ]\n                };\n            }\n        }\n    }\n    /**\n     * Run translation operations for one node.\n     * @private\n     */\n    translateNode(node, column) {\n        const translationFactor = this.translationFactor, chart = this.chart, options = this.options, { borderRadius, borderWidth = 0 } = options, sum = node.getSum(), nodeHeight = Math.max(Math.round(sum * translationFactor), this.options.minLinkWidth), nodeWidth = Math.round(this.nodeWidth), nodeOffset = column.sankeyColumn.offset(node, translationFactor), fromNodeTop = crisp(SankeySeries_pick(nodeOffset.absoluteTop, (column.sankeyColumn.top(translationFactor) +\n            nodeOffset.relativeTop)), borderWidth), left = crisp(this.colDistance * node.column +\n            borderWidth / 2, borderWidth) + SankeySeries_relativeLength(node.options[chart.inverted ?\n            'offsetVertical' :\n            'offsetHorizontal'] || 0, nodeWidth), nodeLeft = chart.inverted ?\n            chart.plotSizeX - left :\n            left;\n        node.sum = sum;\n        // If node sum is 0, don't render the rect #12453\n        if (sum) {\n            // Draw the node\n            node.shapeType = 'roundedRect';\n            node.nodeX = nodeLeft;\n            node.nodeY = fromNodeTop;\n            let x = nodeLeft, y = fromNodeTop, width = node.options.width || options.width || nodeWidth, height = node.options.height || options.height || nodeHeight;\n            // Border radius should not greater than half the height of the node\n            // #18956\n            const r = clamp(SankeySeries_relativeLength((typeof borderRadius === 'object' ?\n                borderRadius.radius :\n                borderRadius || 0), width), 0, nodeHeight / 2);\n            if (chart.inverted) {\n                x = nodeLeft - nodeWidth;\n                y = chart.plotSizeY - fromNodeTop - nodeHeight;\n                width = node.options.height || options.height || nodeWidth;\n                height = node.options.width || options.width || nodeHeight;\n            }\n            // Calculate data label options for the point\n            node.dlOptions = SankeySeries.getDLOptions({\n                level: this.mapOptionsToLevel[node.level],\n                optionsPoint: node.options\n            });\n            // Pass test in drawPoints\n            node.plotX = 1;\n            node.plotY = 1;\n            // Set the anchor position for tooltips\n            node.tooltipPos = chart.inverted ? [\n                chart.plotSizeY - y - height / 2,\n                chart.plotSizeX - x - width / 2\n            ] : [\n                x + width / 2,\n                y + height / 2\n            ];\n            node.shapeArgs = {\n                x,\n                y,\n                width,\n                height,\n                r,\n                display: node.hasShape() ? '' : 'none'\n            };\n        }\n        else {\n            node.dlOptions = {\n                enabled: false\n            };\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nSankeySeries.defaultOptions = SankeySeries_merge(SankeySeries_ColumnSeries.defaultOptions, Sankey_SankeySeriesDefaults);\nSeries_NodesComposition.compose(Sankey_SankeyPoint, SankeySeries);\nSankeySeries_extend(SankeySeries.prototype, {\n    animate: LineSeries.prototype.animate,\n    // Create a single node that holds information on incoming and outgoing\n    // links.\n    createNode: Series_NodesComposition.createNode,\n    forceDL: true,\n    invertible: true,\n    isCartesian: false,\n    orderNodes: true,\n    noSharedTooltip: true,\n    pointArrayMap: ['from', 'to', 'weight'],\n    pointClass: Sankey_SankeyPoint,\n    searchPoint: (external_highcharts_src_js_default_default()).noop\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('sankey', SankeySeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeySeries = ((/* unused pure expression or super */ null && (SankeySeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A node in a sankey diagram.\n *\n * @interface Highcharts.SankeyNodeObject\n * @extends Highcharts.Point\n * @product highcharts\n */ /**\n* The color of the auto generated node.\n*\n* @name Highcharts.SankeyNodeObject#color\n* @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n*/ /**\n* The color index of the auto generated node, especially for use in styled\n* mode.\n*\n* @name Highcharts.SankeyNodeObject#colorIndex\n* @type {number}\n*/ /**\n* An optional column index of where to place the node. The default behaviour is\n* to place it next to the preceding node.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*      Specified node column\n*\n* @name Highcharts.SankeyNodeObject#column\n* @type {number}\n* @since 6.0.5\n*/ /**\n* The id of the auto-generated node, refering to the `from` or `to` setting of\n* the link.\n*\n* @name Highcharts.SankeyNodeObject#id\n* @type {string}\n*/ /**\n* The name to display for the node in data labels and tooltips. Use this when\n* the name is different from the `id`. Where the id must be unique for each\n* node, this is not necessary for the name.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/css/sankey/|Highcharts-Demo:}\n*         Sankey diagram with node options\n*\n* @name Highcharts.SankeyNodeObject#name\n* @type {string}\n* @product highcharts\n*/ /**\n* This option is deprecated, use\n* {@link Highcharts.SankeyNodeObject#offsetHorizontal} and\n* {@link Highcharts.SankeyNodeObject#offsetVertical} instead.\n*\n* The vertical offset of a node in terms of weight. Positive values shift the\n* node downwards, negative shift it upwards.\n*\n* If a percentage string is given, the node is offset by the percentage of the\n* node size plus `nodePadding`.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*         Specified node offset\n*\n* @deprecated\n* @name Highcharts.SankeyNodeObject#offset\n* @type {number|string}\n* @default 0\n* @since 6.0.5\n*/ /**\n* The horizontal offset of a node. Positive values shift the node right,\n* negative shift it left.\n*\n* If a percentage string is given, the node is offset by the percentage of the\n* node size.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*         Specified node offset\n*\n* @name Highcharts.SankeyNodeObject#offsetHorizontal\n* @type {number|string}\n* @since 9.3.0\n*/ /**\n* The vertical offset of a node. Positive values shift the node down,\n* negative shift it up.\n*\n* If a percentage string is given, the node is offset by the percentage of the\n* node size.\n*\n* @see {@link https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/sankey-node-column/|Highcharts-Demo:}\n*         Specified node offset\n*\n* @name Highcharts.SankeyNodeObject#offsetVertical\n* @type {number|string}\n* @since 9.3.0\n*/\n/**\n * Formatter callback function.\n *\n * @callback Highcharts.SeriesSankeyDataLabelsFormatterCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Data label context to format\n *\n * @return {string|undefined}\n *         Formatted data label text\n */\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/sankey.js\n\n\n\n\n/* harmony default export */ const sankey_src = ((external_highcharts_src_js_default_default()));\n\nexport { sankey_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "NodesComposition", "SankeyColumnComposition", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "series", "seriesProto", "pointClass", "pointProto", "defined", "extend", "find", "merge", "pick", "destroy", "data", "concat", "points", "nodes", "apply", "arguments", "setData", "for<PERSON>ach", "node", "length", "setNodeState", "state", "args", "others", "isNode", "linksTo", "linksFrom", "fromNode", "toNode", "linkOrNode", "setState", "graphic", "updateNode", "options", "redraw", "animation", "runEvent", "dataLength", "linkConfig", "index", "update", "nodeIndex", "reduce", "prevIndex", "id", "nodeConfig", "push", "chart", "compose", "PointClass", "SeriesClass", "createNode", "findById", "newNode", "className", "y", "getSum", "sumTo", "sumFrom", "link", "weight", "Math", "max", "offset", "point", "coll", "i", "<PERSON><PERSON><PERSON><PERSON>", "outgoing", "formatPrefix", "name", "mass", "marker", "radius", "generatePoints", "nodeLookup", "colorCounter", "level", "from", "styledMode", "colorIndex", "color", "to", "Series_NodesComposition", "external_highcharts_src_js_default_Point_namespaceObject", "Point", "external_highcharts_src_js_default_Point_default", "column", "ColumnSeries", "seriesTypes", "SankeyPoint_defined", "SankeyPoint", "applyOptions", "x", "getClassName", "getFromNode", "fromColumn", "setNodeColumn", "<PERSON><PERSON><PERSON><PERSON>", "SankeyColumnComposition_defined", "getAlignFactor", "<PERSON><PERSON><PERSON><PERSON>", "sankeyColumnArray", "sankeyColumn", "SankeyColumnAdditions", "constructor", "getTranslationFactor", "slice", "minLinkWidth", "skipPoint", "factor", "remainingHeight", "plotSizeY", "borderWidth", "nodePadding", "sum", "splice", "top", "height", "nodeAlignment", "left", "equalNodes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inverted", "plotHeight", "plot<PERSON>id<PERSON>", "width", "plotSizeX", "round", "totalNodeOffset", "is", "hangsFrom", "absoluteTop", "nodeY", "directionOffset", "optionOffset", "relativeTop", "Sankey_SankeyColumnComposition", "external_highcharts_src_js_default_Color_namespaceObject", "Color", "external_highcharts_src_js_default_Color_default", "TreeUtilities_extend", "isArray", "isNumber", "isObject", "TreeUtilities_merge", "TreeUtilities_pick", "TreeUtilities_relativeLength", "external_highcharts_src_js_default_SVGElement_namespaceObject", "SVGElement", "external_highcharts_src_js_default_SVGElement_default", "deg2rad", "addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "path", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "renderer", "textWrapper", "text", "textPath", "undo", "e", "textPathId", "attr", "textAttribs", "dx", "transform", "box", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "element", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "SankeySeries_ColumnSeries", "LineSeries", "parse", "getLevelOptions", "SankeySeries_getLevelOptions", "getNodeWidth", "SankeySeries_getNodeWidth", "getColor", "colorByPoint", "colorIndexByPoint", "mapOptionsToLevel", "parentColor", "parentColorIndex", "colors", "siblings", "chartOptionsChart", "colorCount", "variateColor", "colorVariation", "brighten", "params", "defaults", "converted", "levels", "result", "item", "levelIsConstant", "columnCount", "nodeDistance", "nodeWidth", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "childrenTotal", "levelDynamic", "visible", "child", "newOptions", "val", "value", "<PERSON><PERSON><PERSON><PERSON>", "updateRootId", "rootId", "rootNode", "userOptions", "clamp", "crisp", "SankeySeries_extend", "SankeySeries_isObject", "SankeySeries_merge", "SankeySeries_pick", "SankeySeries_relativeLength", "stableSort", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "SankeySeries", "getDLOptions", "dataLabels", "style", "createNodeColumns", "columns", "order", "orderNodes", "getNodePadding", "nodeColumns", "max<PERSON><PERSON><PERSON>", "acc", "col", "hasData", "dataTable", "rowCount", "pointAttribs", "levelOptions", "stateOptions", "states", "values", "fill", "stroke", "borderColor", "opacity", "linkOpacity", "drawTracker", "drawPoints", "drawDataLabels", "translate", "translationFactor", "min", "Infinity", "colDistance", "borderRadius", "linkColor", "linkLineWidth", "translateNode", "linkPoint", "isNull", "translateLink", "allowShadow", "getY", "fromOrTo", "linkTop", "shapeArgs", "linkHeight", "linkColorMode", "curvy", "curveFactor", "nodeLeft", "nodeX", "right", "fromY", "toY", "nodeW", "straight", "shapeType", "linkBase", "vDist", "x1", "x2", "x4", "x5", "x6", "fy1", "fy2", "fy3", "y4", "y5", "y6", "ty1", "ty2", "ty3", "cfy1", "cy2", "cty1", "cx1", "x3", "cx2", "dlBox", "tooltipPos", "plotY", "plotX", "fromColor", "toColor", "linearGradient", "y1", "y2", "stops", "nodeHeight", "nodeOffset", "fromNodeTop", "r", "dlOptions", "display", "defaultOptions", "backgroundColor", "crop", "nodeFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "format", "formatter", "inside", "inactiveOtherPoints", "showInLegend", "hover", "inactive", "duration", "tooltip", "followPointer", "headerFormat", "pointFormat", "animate", "forceDL", "invertible", "isCartesian", "noSharedTooltip", "pointArrayMap", "searchPoint", "noop", "registerSeriesType", "sankey_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,CAEvF,IAwDLC,EAu8BAC,EA//BSC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDvB,EAAwD,OAAU,CAC7H,IAAIwB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAEpF,IAAME,EAAoEzB,EAAwD,OAAU,CAAC0B,cAAc,CAC3J,IAAIC,EAAyExB,EAAoBC,CAAC,CAACqB,GASnG,GAAM,CAAEG,OAAQ,CAAER,UAAWS,CAAW,CAAET,UAAW,CAAEU,WAAY,CAAEV,UAAWW,CAAU,CAAE,CAAE,CAAE,CAAE,CAAIJ,IAEhG,CAAEK,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAIZ,KAOhD,AAAC,SAAUvB,CAAgB,EAsGvB,SAASoC,IAIL,OAFA,IAAI,CAACC,IAAI,CAAG,EAAE,CACTC,MAAM,CAAC,IAAI,CAACC,MAAM,EAAI,EAAE,CAAE,IAAI,CAACC,KAAK,EAClCZ,EAAYQ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC3C,CAsDA,SAASC,IACD,IAAI,CAACH,KAAK,GACV,IAAI,CAACA,KAAK,CAACI,OAAO,CAAC,AAACC,IAChBA,EAAKT,OAAO,EAChB,GACA,IAAI,CAACI,KAAK,CAACM,MAAM,CAAG,GAExBlB,EAAYe,OAAO,CAACF,KAAK,CAAC,IAAI,CAAEC,UACpC,CAMA,SAASK,EAAaC,CAAK,EACvB,IAAMC,EAAOP,UAAWQ,EAAS,IAAI,CAACC,MAAM,CAAG,IAAI,CAACC,OAAO,CAACd,MAAM,CAAC,IAAI,CAACe,SAAS,EAC7E,CAAC,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,MAAM,CAAC,AAClB,CAAA,WAAVP,GACAE,EAAON,OAAO,CAAC,AAACY,IACRA,GAAcA,EAAW7B,MAAM,GAC/BG,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAYP,GAClC,CAACO,EAAWL,MAAM,GACdK,EAAWF,QAAQ,CAACI,OAAO,EAC3B5B,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAWF,QAAQ,CAAEL,GAE/CO,EAAWD,MAAM,EAAIC,EAAWD,MAAM,CAACG,OAAO,EAC9C5B,EAAW2B,QAAQ,CAAChB,KAAK,CAACe,EAAWD,MAAM,CAAEN,IAI7D,GAEJnB,EAAW2B,QAAQ,CAAChB,KAAK,CAAC,IAAI,CAAEQ,EACpC,CAOA,SAASU,EAAWC,CAAO,CAAEC,CAAM,CAAEC,CAAS,CAAEC,CAAQ,EACpD,IAAMvB,EAAQ,IAAI,CAACb,MAAM,CAACiC,OAAO,CAACpB,KAAK,CAAEH,EAAO,IAAI,CAACV,MAAM,CAACiC,OAAO,CAACvB,IAAI,CAAE2B,EAAa3B,GAAMS,QAAU,EAAGmB,EAAa5B,GAAM,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAGzI,GAFApC,EAAWqC,MAAM,CAAC9C,IAAI,CAAC,IAAI,CAAEuC,EAAS,CAAA,IAAI,CAACT,MAAM,EAAWU,EAC5DC,EAAWC,GACP,IAAI,CAACZ,MAAM,CAAE,CAEb,IAAMiB,EAAY,AAAC5B,CAAAA,GAAS,EAAE,AAAD,EACxB6B,MAAM,CACX,CAACC,EAAWnE,EAAG+D,IAAW,IAAI,CAACK,EAAE,GAAKpE,EAAEoE,EAAE,CAAGL,EAAQI,EAAY,IAGjEE,EAAatC,EAAMM,GAASA,CAAK,CAAC4B,EAAU,EAAI,CAAC,EAAG/B,GAAM,CAAC,IAAI,CAAC6B,KAAK,CAAC,EAAI,CAAC,GAEvE7B,IACI4B,EACA5B,CAAI,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAAGD,EAInB5B,EAAKS,MAAM,CAAGkB,GAIlBxB,EACI4B,GAAa,EACb5B,CAAK,CAAC4B,EAAU,CAAGI,EAGnBhC,EAAMiC,IAAI,CAACD,GAIf,IAAI,CAAC7C,MAAM,CAACiC,OAAO,CAACpB,KAAK,CAAG,CAACgC,EAAW,CAExCrC,EAAK0B,EAAQ,CAAA,IACb,IAAI,CAAClC,MAAM,CAAC+C,KAAK,CAACb,MAAM,CAACC,EAEjC,CACJ,CAxNA9D,EAAiB2E,OAAO,CATxB,SAAiBC,CAAU,CAAEC,CAAW,EACpC,IAAM/C,EAAa8C,EAAWzD,SAAS,CAAES,EAAciD,EAAY1D,SAAS,CAM5E,OALAW,EAAWiB,YAAY,CAAGA,EAC1BjB,EAAW2B,QAAQ,CAAGV,EACtBjB,EAAWqC,MAAM,CAAGR,EACpB/B,EAAYQ,OAAO,CAAGA,EACtBR,EAAYe,OAAO,CAAGA,EACfkC,CACX,EA2EA7E,EAAiB8E,UAAU,CApE3B,SAAoBP,CAAE,EAClB,IAAMK,EAAa,IAAI,CAAC/C,UAAU,CAAEkD,EAAW,CAACvC,EAAO+B,IAAOtC,EAAKO,EAAO,AAACK,GAASA,EAAK0B,EAAE,GAAKA,GAC5F1B,EAAOkC,EAAS,IAAI,CAACvC,KAAK,CAAE+B,GAAKX,EACrC,GAAI,CAACf,EAAM,CACPe,EAAU,IAAI,CAACA,OAAO,CAACpB,KAAK,EAAIuC,EAAS,IAAI,CAACnB,OAAO,CAACpB,KAAK,CAAE+B,GAC7D,IAAMS,EAAU,IAAIJ,EAAW,IAAI,CAAE5C,EAAO,CACxCiD,UAAW,kBACX9B,OAAQ,CAAA,EACRoB,GAAIA,EACJW,EAAG,CACP,EAAGtB,GACHoB,CAAAA,EAAQ5B,OAAO,CAAG,EAAE,CACpB4B,EAAQ3B,SAAS,CAAG,EAAE,CAKtB2B,EAAQG,MAAM,CAAG,WACb,IAAIC,EAAQ,EAAGC,EAAU,EAOzB,OANAL,EAAQ5B,OAAO,CAACR,OAAO,CAAC,AAAC0C,IACrBF,GAASE,EAAKC,MAAM,EAAI,CAC5B,GACAP,EAAQ3B,SAAS,CAACT,OAAO,CAAC,AAAC0C,IACvBD,GAAWC,EAAKC,MAAM,EAAI,CAC9B,GACOC,KAAKC,GAAG,CAACL,EAAOC,EAC3B,EAKAL,EAAQU,MAAM,CAAG,SAAUC,CAAK,CAAEC,CAAI,EAClC,IAAIF,EAAS,EACb,IAAK,IAAIG,EAAI,EAAGA,EAAIb,CAAO,CAACY,EAAK,CAAC9C,MAAM,CAAE+C,IAAK,CAC3C,GAAIb,CAAO,CAACY,EAAK,CAACC,EAAE,GAAKF,EACrB,OAAOD,EAEXA,GAAUV,CAAO,CAACY,EAAK,CAACC,EAAE,CAACN,MAAM,AACrC,CACJ,EAGAP,EAAQc,QAAQ,CAAG,WACf,IAAIC,EAAW,EAMf,OALAf,EAAQ5B,OAAO,CAACR,OAAO,CAAC,AAAC0C,IACjBA,EAAKS,QAAQ,EACbA,GAER,GACQ,CAACf,EAAQ5B,OAAO,CAACN,MAAM,EAC3BiD,IAAaf,EAAQ5B,OAAO,CAACN,MAAM,AAC3C,EACAkC,EAAQd,KAAK,CAAG,IAAI,CAAC1B,KAAK,CAACiC,IAAI,CAACO,GAAW,EAC3CnC,EAAOmC,CACX,CAYA,OAXAnC,EAAKmD,YAAY,CAAG,OAEpBnD,EAAKoD,IAAI,CAAGpD,EAAKoD,IAAI,EAAIpD,EAAKe,OAAO,CAACW,EAAE,EAAI,GAE5C1B,EAAKqD,IAAI,CAAG/D,EAEZU,EAAKe,OAAO,CAACsC,IAAI,CAAErD,EAAKe,OAAO,CAACuC,MAAM,EAAItD,EAAKe,OAAO,CAACuC,MAAM,CAACC,MAAM,CAEpE,IAAI,CAACxC,OAAO,CAACuC,MAAM,EAAI,IAAI,CAACvC,OAAO,CAACuC,MAAM,CAACC,MAAM,CAEjD,GACOvD,CACX,EAYA7C,EAAiBoC,OAAO,CAAGA,EAgD3BpC,EAAiBqG,cAAc,CA1C/B,WACI,IAAM3B,EAAQ,IAAI,CAACA,KAAK,CAAE4B,EAAa,CAAC,EACxC1E,EAAYyE,cAAc,CAAChF,IAAI,CAAC,IAAI,EAC/B,IAAI,CAACmB,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAElB,IAAI,CAAC+D,YAAY,CAAG,EAEpB,IAAI,CAAC/D,KAAK,CAACI,OAAO,CAAC,AAACC,IAChBA,EAAKQ,SAAS,CAACP,MAAM,CAAG,EACxBD,EAAKO,OAAO,CAACN,MAAM,CAAG,EACtBD,EAAK2D,KAAK,CAAG3D,EAAKe,OAAO,CAAC4C,KAAK,AACnC,GAEA,IAAI,CAACjE,MAAM,CAACK,OAAO,CAAC,AAAC+C,IACb5D,EAAQ4D,EAAMc,IAAI,IACbH,CAAU,CAACX,EAAMc,IAAI,CAAC,EACvBH,CAAAA,CAAU,CAACX,EAAMc,IAAI,CAAC,CAAG,IAAI,CAAC3B,UAAU,CAACa,EAAMc,IAAI,CAAA,EAEvDH,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACpD,SAAS,CAACoB,IAAI,CAACkB,GACtCA,EAAMrC,QAAQ,CAAGgD,CAAU,CAACX,EAAMc,IAAI,CAAC,CAEnC/B,EAAMgC,UAAU,CAChBf,EAAMgB,UAAU,CAAGxE,EAAKwD,EAAM/B,OAAO,CAAC+C,UAAU,CAAEL,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACE,UAAU,EAGnFhB,EAAMiB,KAAK,CACPjB,EAAM/B,OAAO,CAACgD,KAAK,EAAIN,CAAU,CAACX,EAAMc,IAAI,CAAC,CAACG,KAAK,EAG3D7E,EAAQ4D,EAAMkB,EAAE,IACXP,CAAU,CAACX,EAAMkB,EAAE,CAAC,EACrBP,CAAAA,CAAU,CAACX,EAAMkB,EAAE,CAAC,CAAG,IAAI,CAAC/B,UAAU,CAACa,EAAMkB,EAAE,CAAA,EAEnDP,CAAU,CAACX,EAAMkB,EAAE,CAAC,CAACzD,OAAO,CAACqB,IAAI,CAACkB,GAClCA,EAAMpC,MAAM,CAAG+C,CAAU,CAACX,EAAMkB,EAAE,CAAC,EAEvClB,EAAMM,IAAI,CAAGN,EAAMM,IAAI,EAAIN,EAAMpB,EAAE,AACvC,EAAG,IAAI,EAEP,IAAI,CAAC+B,UAAU,CAAGA,CACtB,EAwCAtG,EAAiB+C,YAAY,CAAGA,EA6ChC/C,EAAiB2D,UAAU,CAAGA,CAClC,EAAG3D,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAM8G,EAA2B9G,EAGxD+G,EAA2DhH,EAAwD,OAAU,CAACiH,KAAK,CACzI,IAAIC,EAAgE/G,EAAoBC,CAAC,CAAC4G,GAgB1F,GAAM,CAAEG,OAAQC,CAAY,CAAE,CAAG,AAACzF,IAA6D0F,WAAW,CAEpG,CAAErF,QAASsF,CAAmB,CAAE,CAAI9F,GAM1C,OAAM+F,UAAoBH,EAAahG,SAAS,CAACU,UAAU,CASvD0F,aAAa3D,CAAO,CAAE4D,CAAC,CAAE,CAMrB,OALAP,IAAmD9F,SAAS,CAACoG,YAAY,CAAClG,IAAI,CAAC,IAAI,CAAEuC,EAAS4D,GAE1FH,EAAoB,IAAI,CAACzD,OAAO,CAAC4C,KAAK,GACtC,CAAA,IAAI,CAAC5C,OAAO,CAACsD,MAAM,CAAG,IAAI,CAACA,MAAM,CAAG,IAAI,CAACtD,OAAO,CAAC4C,KAAK,AAAD,EAElD,IAAI,AACf,CAIAiB,cAAe,CACX,MAAO,AAAC,CAAA,IAAI,CAACtE,MAAM,CAAG,mBAAqB,kBAAiB,EACxD8D,IAAmD9F,SAAS,CAACsG,YAAY,CAACpG,IAAI,CAAC,IAAI,CAC3F,CAOAqG,aAAc,CAEV,IAAIC,EAAa,GAAIrE,EACrB,IAAK,IAAIuC,EAAI,EAAGA,EAAIhD,AAFP,IAAI,CAEQO,OAAO,CAACN,MAAM,CAAE+C,IAAK,CAC1C,IAAMF,EAAQ9C,AAHL,IAAI,CAGMO,OAAO,CAACyC,EAAE,AACzBF,CAAAA,EAAMrC,QAAQ,CAAC4D,MAAM,CAAGS,GACxBhC,EAAMrC,QAAQ,GALT,IAAI,EAQTqE,CAAAA,EAAarE,AADbA,CAAAA,EAAWqC,EAAMrC,QAAQ,AAAD,EACF4D,MAAM,AAAD,CAEnC,CACA,MAAO,CAAE5D,SAAAA,EAAUqE,WAAAA,CAAW,CAClC,CAKAC,eAAgB,CAEPP,EAAoBxE,AADZ,IAAI,CACae,OAAO,CAACsD,MAAM,IAEpCrE,AAAwB,IAAxBA,AAHK,IAAI,CAGJO,OAAO,CAACN,MAAM,CACnBD,AAJK,IAAI,CAIJqE,MAAM,CAAG,EAGdrE,AAPK,IAAI,CAOJqE,MAAM,CAAGrE,AAPT,IAAI,CAOU6E,WAAW,GAAGC,UAAU,CAAG,EAG1D,CAIAE,SAAU,CACN,OAAO,IAAI,CAAC1E,MAAM,EAAI,AAAuB,UAAvB,OAAO,IAAI,CAACoC,MAAM,AAC5C,CACJ,CA2mBA,GAAM,CAAExD,QAAS+F,CAA+B,CAAEC,eAAAA,CAAc,CAAEC,eAAAA,CAAc,CAAE,CAAIzG,KAOtF,AAAC,SAAUtB,CAAuB,EA4B9BA,EAAwB0E,OAAO,CAN/B,SAAiBpC,CAAM,CAAEZ,CAAM,EAI3B,OAFAsG,AAD0B1F,EACR2F,YAAY,CAC1B,IAAIC,EAFkB5F,EAEuBZ,GAFvBY,CAI9B,CAOA,OAAM4F,EAMFC,YAAY7F,CAAM,CAAEZ,CAAM,CAAE,CACxB,IAAI,CAACY,MAAM,CAAGA,EACd,IAAI,CAACZ,MAAM,CAAGA,CAClB,CAgBA0G,qBAAqB1G,CAAM,CAAE,CACzB,IAAMuF,EAAS,IAAI,CAAC3E,MAAM,CAAEC,EAAQ0E,EAAOoB,KAAK,GAAI5D,EAAQ/C,EAAO+C,KAAK,CAAE6D,EAAe5G,EAAOiC,OAAO,CAAC2E,YAAY,EAAI,EACpHC,EAAWC,EAAS,EAAG5C,EAAG6C,EAAmB,AAAChE,CAAAA,EAAMiE,SAAS,EAAI,CAAA,EAChEhH,CAAAA,EAAOiC,OAAO,CAACgF,WAAW,EAAI,CAAA,EAC/B,AAAC1B,CAAAA,EAAOpE,MAAM,CAAG,CAAA,EAAKnB,EAAOkH,WAAW,CAK5C,KAAO3B,EAAOpE,MAAM,EAAE,CAIlB,IAHA2F,EAASC,EAAkBxB,EAAOgB,YAAY,CAACY,GAAG,GAClDN,EAAY,CAAA,EACZ3C,EAAIqB,EAAOpE,MAAM,CACV+C,KACCqB,CAAM,CAACrB,EAAE,CAACV,MAAM,GAAKsD,EAASF,IAC9BrB,EAAO6B,MAAM,CAAClD,EAAG,GACjB6C,EACIlD,KAAKC,GAAG,CAAC,EAAGiD,EAAkBH,GAClCC,EAAY,CAAA,GAGpB,GAAI,CAACA,EACD,KAER,CAGA,IAAK,IAAM3F,KADXqE,EAAOpE,MAAM,CAAG,EACGN,GACf0E,EAAOzC,IAAI,CAAC5B,GAEhB,OAAO4F,CACX,CAWAO,IAAIP,CAAM,CAAE,CACR,IAAM9G,EAAS,IAAI,CAACA,MAAM,CAAEkH,EAAclH,EAAOkH,WAAW,CAAEI,EAAS,IAAI,CAAC1G,MAAM,CAAC8B,MAAM,CAAC,CAAC4E,EAAQpG,KAC3FoG,EAAS,GACTA,CAAAA,GAAUJ,CAAU,EAGxBI,GADmBzD,KAAKC,GAAG,CAAC5C,EAAKsC,MAAM,GAAKsD,EAAQ9G,EAAOiC,OAAO,CAAC2E,YAAY,EAAI,IAGpF,GAEH,OAAOR,EAAepG,EAAOiC,OAAO,CAACsF,aAAa,EAAI,UAAa,CAAA,AAACvH,CAAAA,EAAO+C,KAAK,CAACiE,SAAS,EAAI,CAAA,EAAKM,CAAK,CAC5G,CAWAE,KAAKV,CAAM,CAAE,CACT,IAAM9G,EAAS,IAAI,CAACA,MAAM,CAAE+C,EAAQ/C,EAAO+C,KAAK,CAAE0E,EAAazH,EAAOiC,OAAO,CAACwF,UAAU,CAAEC,EAAkB3E,EAAM4E,QAAQ,CAAG5E,EAAM6E,UAAU,CAAG7E,EAAM8E,SAAS,CAAGX,EAAclH,EAAOkH,WAAW,CAAEY,EAAQ,IAAI,CAAClH,MAAM,CAAC8B,MAAM,CAAC,CAACoF,EAAO5G,KAC/N4G,EAAQ,GACRA,CAAAA,GAASZ,CAAU,EAMvBY,GAJkBL,EACdC,EAAiBxG,EAAKlB,MAAM,CAACa,KAAK,CAACM,MAAM,CACrC+F,EACJrD,KAAKC,GAAG,CAAC5C,EAAKsC,MAAM,GAAKsD,EAAQ9G,EAAOiC,OAAO,CAAC2E,YAAY,EAAI,IAGrE,GACH,MAAO,AAAC,CAAA,AAAC7D,CAAAA,EAAMgF,SAAS,EAAI,CAAA,EAAKlE,KAAKmE,KAAK,CAACF,EAAK,EAAK,CAC1D,CAYAX,KAAM,CACF,OAAO,IAAI,CAACvG,MAAM,CAAC8B,MAAM,CAAC,CAACyE,EAAKjG,IAAUiG,EAAMjG,EAAKsC,MAAM,GAAK,EACpE,CAaAO,OAAO7C,CAAI,CAAE4F,CAAM,CAAE,CACjB,IAAMvB,EAAS,IAAI,CAAC3E,MAAM,CAAEZ,EAAS,IAAI,CAACA,MAAM,CAAEkH,EAAclH,EAAOkH,WAAW,CAC9EnD,EAAS,EAAGkE,EAChB,GAAIjI,EAAOkI,EAAE,CAAC,iBAAmBhH,EAAKiH,SAAS,CAC3C,MAAO,CACHC,YAAalH,EAAKiH,SAAS,CAACE,KAAK,AACrC,EAEJ,IAAK,IAAInE,EAAI,EAAGA,EAAIqB,EAAOpE,MAAM,CAAE+C,IAAK,CACpC,IAAMiD,EAAM5B,CAAM,CAACrB,EAAE,CAACV,MAAM,GACtB8D,EAASzD,KAAKC,GAAG,CAACqD,EAAML,EAAQ9G,EAAOiC,OAAO,CAAC2E,YAAY,EAAI,GAC/D0B,EAAkBpH,EAAKe,OAAO,CAACjC,EAAO+C,KAAK,CAAC4E,QAAQ,CACtD,mBACA,iBAAiB,CAAEY,EAAerH,EAAKe,OAAO,CAAC8B,MAAM,EAAI,EAQ7D,GANIkE,EADAd,EACkBG,EAASJ,EAIT,EAElB3B,CAAM,CAACrB,EAAE,GAAKhD,EACd,MAAO,CACHsH,YAAazE,EAAUoC,CAAAA,EAAgCmC,GAGnDjC,EAAeiC,EAAiBhB,GAChCjB,EAAekC,EAAcN,EAAe,CACpD,EAEJlE,GAAUkE,CACd,CACJ,CACJ,CACA3J,EAAwBkI,qBAAqB,CAAGA,CACpD,EAAGlI,GAA4BA,CAAAA,EAA0B,CAAC,CAAA,GAM7B,IAAMmK,EAAkCnK,EAG/DoK,EAA2DtK,EAAwD,OAAU,CAACuK,KAAK,CACzI,IAAIC,EAAgErK,EAAoBC,CAAC,CAACkK,GAgB1F,GAAM,CAAErI,OAAQwI,CAAoB,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEzI,MAAO0I,CAAmB,CAAEzI,KAAM0I,CAAkB,CAAE7C,eAAgB8C,CAA4B,CAAE,CAAIvJ,IA0MrKwJ,EAAgEhL,EAAwD,OAAU,CAACiL,UAAU,CACnJ,IAAIC,EAAqE/K,EAAoBC,CAAC,CAAC4K,GAgB/F,GAAM,CAAEG,QAAAA,CAAO,CAAE,CAAI3J,IACf,CAAE4J,SAAAA,CAAQ,CAAEjJ,MAAOkJ,CAAc,CAAEC,UAAAA,CAAS,CAAEtJ,QAASuJ,CAAgB,CAAEtJ,OAAQuJ,CAAe,CAAE,CAAIhK,IAyB5G,SAASiK,EAAYC,CAAI,CAAEC,CAAe,EAEtCA,EAAkBN,EAAe,CAAA,EAAM,CACnCO,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAACC,QAAQ,CAACD,GAAG,CAAEE,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAER,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAD,EAAOA,GAASW,GAAYA,EAASX,IAAI,CAErCW,GACAA,EAASC,IAAI,GAEbZ,GAAQE,EAAS,CACjB,IAAMU,EAAOlB,EAASe,EAAa,kBAAmB,AAACI,IACnD,GAAIb,GAAQE,EAAS,CAEjB,IAAIY,EAAad,EAAKe,IAAI,CAAC,MACtBD,GACDd,EAAKe,IAAI,CAAC,KAAMD,EAAalB,KAGjC,IAAMoB,EAAc,CAGhBjF,EAAG,EACHtC,EAAG,CACP,EACIoG,EAAiBM,EAAWc,EAAE,IAC9BD,EAAYC,EAAE,CAAGd,EAAWc,EAAE,CAC9B,OAAOd,EAAWc,EAAE,EAEpBpB,EAAiBM,EAAWC,EAAE,IAC9BY,EAAYZ,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBK,EAAYM,IAAI,CAACC,GAEjB,IAAI,CAACD,IAAI,CAAC,CAAEG,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAACxK,OAAO,EAAC,EAGhC,IAAMyK,EAAWP,EAAE9J,KAAK,CAAC8F,KAAK,CAAC,EAC/BgE,CAAAA,EAAE9J,KAAK,CAACM,MAAM,CAAG,EACjBwJ,EAAE9J,KAAK,CAAC,EAAE,CAAG,CACTsK,QAAS,WACTlB,WAAYL,EAAgBK,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCgB,KAAM,CAAC,EAAEf,EAAI,CAAC,EAAEO,EAAW,CAAC,AAChC,GACAM,SAAAA,CACJ,CACJ,CACJ,EAEAX,CAAAA,EAAYE,QAAQ,CAAG,CAAEX,KAAAA,EAAMY,KAAAA,CAAK,CACxC,MAEIH,EAAYM,IAAI,CAAC,CAAEE,GAAI,EAAGb,GAAI,CAAE,GAChC,OAAOK,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACY,KAAK,GAEVd,EAAYe,SAAS,CAAG,GACxB,IAAI,CAAChB,QAAQ,CAACiB,SAAS,CAAChB,IAErB,IAAI,AACf,CAWA,SAASiB,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAACC,OAAO,EAAEC,cAAc,YAC1D,GAAIF,EAAI,CACJ,IAAMG,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC1B,QAAQ,CAAC2B,WAAW,CAAC,IAAI,CAACL,OAAO,EAAGM,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQV,EAC5BW,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMlL,MAAM,CAIrEuL,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAE/G,EAAAA,CAAC,CAAEtC,EAAAA,CAAC,CAAE,CAAGqJ,EAAgBC,EAAW,AAAClB,CAAAA,EAAGmB,iBAAiB,CAACH,GAAa,EAAC,EAAKpD,EAASwD,EAASlJ,KAAKmJ,GAAG,CAACH,GAAWI,EAASpJ,KAAKqJ,GAAG,CAACL,GAC7I,MAAO,CACH,CACIhH,EAAIqG,EAAYa,EAChBxJ,EAAI2I,EAAYe,EACnB,CACD,CACIpH,EAAIkG,EAAIgB,EACRxJ,EAAIwI,EAAIkB,EACX,CACJ,AACL,EACA,IAAK,IAAI/I,EAAI,EAAGiJ,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiBhM,MAAM,CACpD,IAAK,IAAImM,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBrJ,EAClBoJ,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAc5B,EAAG+B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAxB,EAAQhJ,IAAI,CAAC2K,GACb3B,EAAQhJ,IAAI,CAAC0K,KAGK,IAAdL,GACArB,EAAQ6B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BX,EAAQhJ,IAAI,CAAC0K,GAGzB,CACA,MAAO7C,EAAG,CAGN,KACJ,CAEJzG,GAAKkJ,EAAU,EACf,GAAI,CACA,IAAMG,EAAerJ,EAAIiJ,EAAWS,EAAUjC,EAAGkC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI9B,EAAQ6B,OAAO,CAACF,GAChB3B,EAAQ6B,OAAO,CAACH,EACpB,CACA,MAAO7C,EAAG,CAGN,KACJ,CACJ,CAEImB,EAAQ3K,MAAM,EACd2K,EAAQhJ,IAAI,CAACgJ,CAAO,CAAC,EAAE,CAACnF,KAAK,IAEjC+E,EAAKI,OAAO,CAAGA,CACnB,CACA,OAAOJ,CACX,CAWA,SAASoC,EAAarC,CAAK,EACvB,IAAMsC,EAAetC,EAAMsC,YAAY,CAAE/J,EAAQyH,EAAMzH,KAAK,CAAE+F,EAAmBgE,CAAY,CAAC/J,EAAMK,YAAY,CAAG,WAAW,EAC1H0J,EAAatD,QAAQ,CACrBV,GAAmB,CAACgE,EAAaC,OAAO,GACxC,IAAI,CAACnE,WAAW,CAAC7F,EAAMiK,gBAAgB,GAAG,IAAI,GAAKjK,EAAMjC,OAAO,CAAEgI,GAC9D/F,EAAMkK,aAAa,EACnB,CAACnE,EAAgBC,OAAO,EAExBhG,CAAAA,EAAMkK,aAAa,CAAIlK,EAAMkK,aAAa,CAACzN,OAAO,EAAE,EAGhE,CAiCA,GAAM,CAAE8E,OAAQ4I,CAAyB,CAAEd,KAAMe,CAAU,CAAE,CAAG,AAACrO,IAA6D0F,WAAW,CAEnI,CAAE4I,MAAOpJ,CAAK,CAAE,CAAI2D,IAEpB,CAAE0F,gBAAiBC,CAA4B,CAAEC,aAAcC,CAAyB,CAAE,CAxQ1E,CAClBC,SAvLJ,SAAkBxN,CAAI,CAAEe,CAAO,EAC3B,IACqB+B,EAAOa,EAAO8J,EAAcC,EAAmB3J,EAAOD,EADrEzC,EAAQN,EAAQM,KAAK,CAAEsM,EAAoB5M,EAAQ4M,iBAAiB,CAAEC,EAAc7M,EAAQ6M,WAAW,CAAEC,EAAmB9M,EAAQ8M,gBAAgB,CAAE/O,EAASiC,EAAQjC,MAAM,CAAEgP,EAAS/M,EAAQ+M,MAAM,CAAEC,EAAWhN,EAAQgN,QAAQ,CAAErO,EAASZ,EAAOY,MAAM,CAAEsO,EAAoBlP,EAAO+C,KAAK,CAACd,OAAO,CAACc,KAAK,CA+BjT,OAhBI7B,IACA8C,EAAQpD,CAAM,CAACM,EAAKgD,CAAC,CAAC,CACtBW,EAAQgK,CAAiB,CAAC3N,EAAK2D,KAAK,CAAC,EAAI,CAAC,EACxBb,GAASa,EAAM8J,YAAY,GAEzCC,EAAoB5K,EAAMzB,KAAK,CAAIyM,CAAAA,EAC/BA,EAAO7N,MAAM,CACb+N,EAAkBC,UAAU,AAAD,EAC/BR,EAAeK,GAAUA,CAAM,CAACJ,EAAkB,EAGjD5O,EAAO+C,KAAK,CAACgC,UAAU,EACxBE,CAAAA,EAAQiE,EAAmBlF,GAASA,EAAM/B,OAAO,CAACgD,KAAK,CAAEJ,GAASA,EAAMI,KAAK,CAAE0J,EAAcG,GAAeM,AAtB/F,CAAA,AAACnK,IAClB,IAAMoK,EAAiBxK,GAASA,EAAMwK,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAerQ,GAAG,EAClBuD,GACA0M,EACOrG,IAAmDyF,KAAK,CAACpJ,GAAOqK,QAAQ,CAACD,EAAenK,EAAE,CAAI3C,CAAAA,EAAQ0M,CAAO,GAAI5P,GAAG,GAExH4F,CACX,CAAA,EAaiI6J,GAAc9O,EAAOiF,KAAK,CAAA,EAEvJD,EAAakE,EAAmBlF,GAASA,EAAM/B,OAAO,CAAC+C,UAAU,CAAEH,GAASA,EAAMG,UAAU,CAAE4J,EAAmBG,EAAkB9M,EAAQ+C,UAAU,GAElJ,CACHC,MAAOA,EACPD,WAAYA,CAChB,CACJ,EAoJIsJ,gBAlIJ,SAAyBiB,CAAM,EAC3B,IACIC,EAAUC,EAAWvL,EAAGY,EAAMI,EAAIwK,EADhCC,EAAS,CAAC,EAEhB,GAAI3G,EAASuG,GA2BT,IA1BAzK,EAAOiE,EAASwG,EAAOzK,IAAI,EAAIyK,EAAOzK,IAAI,CAAG,EAC7C4K,EAASH,EAAOG,MAAM,CACtBD,EAAY,CAAC,EACbD,EAAWxG,EAASuG,EAAOC,QAAQ,EAAID,EAAOC,QAAQ,CAAG,CAAC,EACtD1G,EAAQ4G,IACRD,CAAAA,EAAYC,EAAOhN,MAAM,CAAC,CAACpD,EAAKsQ,KAC5B,IAAI/K,EAAOgL,EAAiB5N,EAgB5B,OAfI+G,EAAS4G,IAAS7G,EAAS6G,EAAK/K,KAAK,IAErCgL,EAAkB3G,EAAmBjH,AADrCA,CAAAA,EAAUgH,EAAoB,CAAC,EAAG2G,EAAI,EACOC,eAAe,CAAEL,EAASK,eAAe,EAEtF,OAAO5N,EAAQ4N,eAAe,CAC9B,OAAO5N,EAAQ4C,KAAK,CAGhBmE,EAAS1J,CAAG,CADhBuF,EAAQ+K,EAAK/K,KAAK,CAAIgL,CAAAA,EAAkB,EAAI/K,EAAO,CAAA,EAC5B,EACnBmE,EAAoB,CAAA,EAAM3J,CAAG,CAACuF,EAAM,CAAE5C,GAGtC3C,CAAG,CAACuF,EAAM,CAAG5C,GAGd3C,CACX,EAAG,CAAC,EAAC,EAET4F,EAAK6D,EAASwG,EAAOrK,EAAE,EAAIqK,EAAOrK,EAAE,CAAG,EAClChB,EAAI,EAAGA,GAAKgB,EAAIhB,IACjByL,CAAM,CAACzL,EAAE,CAAG+E,EAAoB,CAAC,EAAGuG,EAAUxG,EAASyG,CAAS,CAACvL,EAAE,EAAIuL,CAAS,CAACvL,EAAE,CAAG,CAAC,GAG/F,OAAOyL,CACX,EAgGInB,aAvBJ,SAAsBxO,CAAM,CAAE8P,CAAW,EACrC,GAAM,CAAE/M,MAAAA,CAAK,CAAEd,QAAAA,CAAO,CAAE,CAAGjC,EAAQ,CAAE+P,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAG/N,EAAS,CAAE8F,UAAAA,EAAY,CAAC,CAAE,CAAGhF,EAGtG,GAAIiN,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKE,IAAI,CAACF,GAE9C,OAAOhI,EADkD+H,CAAAA,EAAcI,AAAtDC,WAAWJ,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMM,EAAYC,OAAON,GACzB,MAAO,AAAEhI,CAAAA,EAAYqI,CAAQ,EACxBN,CAAAA,GAAe,CAAA,EAAMM,CAC9B,CACA,OAAOjH,EAA6B6G,EAAWjI,EACnD,EAUIuI,cA3FJ,SAASA,EAAcC,CAAI,CAAEtO,CAAO,EAChC,IAAMuO,EAASvO,EAAQuO,MAAM,CAAEC,EAASxO,EAAQwO,MAAM,CAAqCC,EAAWC,AAAhC1O,EAAQ0O,WAAW,AAAwB,CAACF,EAAO,CAAEZ,EAAmB5N,AAA4B,CAAA,IAA5BA,EAAQ4N,eAAe,CAAsC7L,EAAQpD,AAAxBqB,EAAQrB,MAAM,AAAgB,CAAC2P,EAAKrM,CAAC,CAAC,CAAE0M,EAAe5M,GAASA,EAAM/B,OAAO,EAAI,CAAC,EAAGiJ,EAAW,EAAE,CACzR2F,EAAgB,CACpBN,CAAAA,EAAKO,YAAY,CAAGP,EAAK1L,KAAK,CAAIgL,CAAAA,EAAkB,EAAIa,EAAS7L,KAAK,AAAD,EACrE0L,EAAKjM,IAAI,CAAG4E,EAAmBlF,GAASA,EAAMM,IAAI,CAAE,IACpDiM,EAAKQ,OAAO,CAAIN,IAAWF,EAAK3N,EAAE,EAC9BX,AAAoB,CAAA,IAApBA,EAAQ8O,OAAO,CACG,YAAlB,OAAOP,GACPD,CAAAA,EAAOC,EAAOD,EAAMtO,EAAO,EAG/BsO,EAAKrF,QAAQ,CAACjK,OAAO,CAAC,CAAC+P,EAAO9M,KAC1B,IAAM+M,EAAapI,EAAqB,CAAC,EAAG5G,GAC5C4G,EAAqBoI,EAAY,CAC7B1O,MAAO2B,EACP+K,SAAUsB,EAAKrF,QAAQ,CAAC/J,MAAM,CAC9B4P,QAASR,EAAKQ,OAAO,AACzB,GACAC,EAAQV,EAAcU,EAAOC,GAC7B/F,EAASpI,IAAI,CAACkO,GACVA,EAAMD,OAAO,EACbF,CAAAA,GAAiBG,EAAME,GAAG,AAAD,CAEjC,GAEA,IAAMC,EAAQjI,EAAmB0H,EAAaO,KAAK,CAAEN,GAMrD,OALAN,EAAKQ,OAAO,CAAGI,GAAS,GAAMN,CAAAA,EAAgB,GAAKN,EAAKQ,OAAO,AAAD,EAC9DR,EAAKrF,QAAQ,CAAGA,EAChBqF,EAAKM,aAAa,CAAGA,EACrBN,EAAKa,MAAM,CAAGb,EAAKQ,OAAO,EAAI,CAACF,EAC/BN,EAAKW,GAAG,CAAGC,EACJZ,CACX,EA4DIc,aA/CJ,SAAsBrR,CAAM,EACxB,IAAIsR,EAAQrP,EAaZ,OAZI+G,EAAShJ,KAETiC,EAAU+G,EAAShJ,EAAOiC,OAAO,EAAIjC,EAAOiC,OAAO,CAAG,CAAC,EAEvDqP,EAASpI,EAAmBlJ,EAAOuR,QAAQ,CAAEtP,EAAQqP,MAAM,CAAE,IAEzDtI,EAAShJ,EAAOwR,WAAW,GAC3BxR,CAAAA,EAAOwR,WAAW,CAACF,MAAM,CAAGA,CAAK,EAGrCtR,EAAOuR,QAAQ,CAAGD,GAEfA,CACX,CAiCA,EAoQM,CAAEG,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAErR,OAAQsR,CAAmB,CAAE3I,SAAU4I,CAAqB,CAAErR,MAAOsR,EAAkB,CAAErR,KAAMsR,EAAiB,CAAEzL,eAAgB0L,EAA2B,CAAEC,WAAAA,EAAU,CAAE,CAAIpS,IAGrMqS,AAjCiB,CAAA,CACbjP,QATJ,SAAiBkP,CAAe,EAC5B1I,EAAS0I,EAAiB,eAAgB1G,GAC1ChC,EAAS0I,EAAiB,wBAAyBpE,GACnD,IAAMqE,EAAkBD,EAAgB1S,SAAS,AAC5C2S,CAAAA,EAAgBtI,WAAW,EAC5BsI,CAAAA,EAAgBtI,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EA+BoB7G,OAAO,CAAEsG,IAa7B,OAAM8I,WAAqBjE,EASvB,OAAOkE,aAAa9C,CAAM,CAAE,CACxB,IAAMqB,EAAgBgB,EAAsBrC,EAAOqB,YAAY,EAC3DrB,EAAOqB,YAAY,CAAC0B,UAAU,CAC9B,CAAC,EAKL,OAHmBT,GAAmB,CAClCU,MAAO,CAAC,CACZ,EAJyBX,EAAsBrC,EAAO1K,KAAK,EACvD0K,EAAO1K,KAAK,CAACyN,UAAU,CACvB,CAAC,EAEY1B,EAErB,CAWA4B,mBAAoB,CAChB,IAAMC,EAAU,EAAE,CAClB,IAAK,IAAMvR,KAAQ,IAAI,CAACL,KAAK,CACzBK,EAAK+E,aAAa,GACbwM,CAAO,CAACvR,EAAKqE,MAAM,CAAC,EACrBkN,CAAAA,CAAO,CAACvR,EAAKqE,MAAM,CAAC,CAChBkD,EAA+BzF,OAAO,CAAC,EAAE,CAAE,IAAI,CAAA,EAEvDyP,CAAO,CAACvR,EAAKqE,MAAM,CAAC,CAACzC,IAAI,CAAC5B,GAG9B,IAAK,IAAIgD,EAAI,EAAGA,EAAIuO,EAAQtR,MAAM,CAAE+C,IACN,KAAA,IAAfuO,CAAO,CAACvO,EAAE,EACjBuO,CAAAA,CAAO,CAACvO,EAAE,CACNuE,EAA+BzF,OAAO,CAAC,EAAE,CAAE,IAAI,CAAA,EAG3D,OAAOyP,CACX,CAKAC,MAAMxR,CAAI,CAAE2D,CAAK,CAAE,CAGf,GAAI,AAAsB,KAAA,IAAf3D,EAAK2D,KAAK,CAEjB,IAAK,IAAMlB,KADXzC,EAAK2D,KAAK,CAAGA,EACM3D,EAAKQ,SAAS,EACzBiC,EAAK/B,MAAM,EACX5B,AANG,IAAI,CAMA0S,KAAK,CAAC/O,EAAK/B,MAAM,CAAEiD,EAAQ,EAIlD,CAMAH,gBAAiB,CAEb,GADAS,EAAwBT,cAAc,CAAC5D,KAAK,CAAC,IAAI,CAAEC,WAC/C,IAAI,CAAC4R,UAAU,CAAE,CACjB,IAAK,IAAMzR,KAAQ,IAAI,CAACL,KAAK,CAEG,IAAxBK,EAAKO,OAAO,CAACN,MAAM,EAGnB,IAAI,CAACuR,KAAK,CAACxR,EAAM,GAGzB8Q,GAAW,IAAI,CAACnR,KAAK,CAAE,CAAChC,EAAGkN,IAAOlN,EAAEgG,KAAK,CAAGkH,EAAElH,KAAK,CACvD,CACJ,CAMA+N,gBAAiB,CACb,IAAI1L,EAAc,IAAI,CAACjF,OAAO,CAACiF,WAAW,EAAI,EAI9C,GAAI,IAAI,CAAC2L,WAAW,CAAE,CAClB,IAAMC,EAAY,IAAI,CAACD,WAAW,CAACnQ,MAAM,CAAC,CAACqQ,EAAKC,IAAQnP,KAAKC,GAAG,CAACiP,EAAKC,EAAI7R,MAAM,EAAG,GAC/E2R,EAAY5L,EAAc,IAAI,CAACnE,KAAK,CAACiE,SAAS,EAC9CE,CAAAA,EAAc,IAAI,CAACnE,KAAK,CAACiE,SAAS,CAAG8L,CAAQ,CAErD,CACA,OAAO5L,CACX,CAOA+L,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CAKAC,aAAapP,CAAK,CAAE3C,CAAK,CAAE,CACvB,GAAI,CAAC2C,EACD,MAAO,CAAC,EAEZ,IAAMhE,EAAS,IAAI,CAAE6E,EAAQb,EAAMxC,MAAM,CAAGwC,EAAMa,KAAK,CAAGb,EAAMrC,QAAQ,CAACkD,KAAK,CAAEwO,EAAerT,EAAO6O,iBAAiB,CAAChK,GAAS,EAAE,EAAI,CAAC,EAAG5C,EAAU+B,EAAM/B,OAAO,CAAEqR,EAAe,AAACD,EAAaE,MAAM,EAAIF,EAAaE,MAAM,CAAClS,GAAS,GAAG,EAAK,CAAC,EAAGmS,EAAS,CACzP,eACA,cACA,cACA,cACA,UACH,CAAC9Q,MAAM,CAAC,CAACpD,EAAKN,KACXM,CAAG,CAACN,EAAI,CAAG8S,GAAkBwB,CAAY,CAACtU,EAAI,CAAEiD,CAAO,CAACjD,EAAI,CAAEqU,CAAY,CAACrU,EAAI,CAAEgB,EAAOiC,OAAO,CAACjD,EAAI,EAC7FM,GACR,CAAC,GAAI2F,EAAQ6M,GAAkBwB,EAAarO,KAAK,CAAEhD,EAAQgD,KAAK,CAAEuO,EAAO7E,YAAY,CAAG3K,EAAMiB,KAAK,CAAGoO,EAAapO,KAAK,SAE3H,AAAIjB,EAAMxC,MAAM,CACL,CACHiS,KAAMxO,EACNyO,OAAQF,EAAOG,WAAW,CAC1B,eAAgBH,EAAOvM,WAAW,CAClC2M,QAASJ,EAAOI,OAAO,AAC3B,EAGG,CACHH,KAAMxO,EACN,eAAgBuO,EAAOK,WAAW,AACtC,CACJ,CACAC,aAAc,CACV3F,EAA0B3O,SAAS,CAACsU,WAAW,CAACpU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkB,MAAM,EACtEuN,EAA0B3O,SAAS,CAACsU,WAAW,CAACpU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmB,KAAK,CACzE,CACAkT,YAAa,CACT5F,EAA0B3O,SAAS,CAACuU,UAAU,CAACrU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkB,MAAM,EACrEuN,EAA0B3O,SAAS,CAACuU,UAAU,CAACrU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmB,KAAK,CACxE,CACAmT,gBAAiB,CACb7F,EAA0B3O,SAAS,CAACwU,cAAc,CAACtU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkB,MAAM,EACzEuN,EAA0B3O,SAAS,CAACwU,cAAc,CAACtU,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmB,KAAK,CAC5E,CAKAoT,WAAY,CACR,IAAI,CAACvP,cAAc,GACnB,IAAI,CAACmO,WAAW,CAAG,IAAI,CAACL,iBAAiB,GACzC,IAAMxS,EAAS,IAAI,CAAE+C,EAAQ,IAAI,CAACA,KAAK,CAAEd,EAAU,IAAI,CAACA,OAAO,CAAE4Q,EAAc,IAAI,CAACA,WAAW,CAAE/C,EAAc+C,EAAY1R,MAAM,CAgCjI,IAAK,IAAMoE,KA/BX,IAAI,CAACyK,SAAS,CAAGvB,EAA0B,IAAI,CAAEqB,GACjD,IAAI,CAAC5I,WAAW,CAAG,IAAI,CAAC0L,cAAc,GAGtC,IAAI,CAACsB,iBAAiB,CAAGrB,EAAYnQ,MAAM,CAAC,CAACwR,EAAmB3O,IAAW1B,KAAKsQ,GAAG,CAACD,EAAmB3O,EAAOgB,YAAY,CAACG,oBAAoB,CAAC1G,IAAUoU,KAC1J,IAAI,CAACC,WAAW,CACZ,AAACtR,CAAAA,EAAMgF,SAAS,CAAG,IAAI,CAACiI,SAAS,CAC7B/N,EAAQgF,WAAW,AAAD,EAAKpD,KAAKC,GAAG,CAAC,EAAG+O,EAAY1R,MAAM,CAAG,GAEhEnB,EAAO6O,iBAAiB,CAAGN,EAA6B,CAGpDzJ,KAAM,EACN4K,OAAQzN,EAAQyN,MAAM,CACtBxK,GAAI2N,EAAY1R,MAAM,CAAG,EACzBqO,SAAU,CACNmE,YAAa1R,EAAQ0R,WAAW,CAChCW,aAAcrS,EAAQqS,YAAY,CAClCrN,YAAahF,EAAQgF,WAAW,CAChChC,MAAOjF,EAAOiF,KAAK,CACnB0J,aAAc1M,EAAQ0M,YAAY,CAGlCkB,gBAAiB,CAAA,EACjB0E,UAAWtS,EAAQsS,SAAS,CAC5BC,cAAevS,EAAQuS,aAAa,CACpCX,YAAa5R,EAAQ4R,WAAW,CAChCN,OAAQtR,EAAQsR,MAAM,AAC1B,CACJ,GAEqBV,GACjB,IAAK,IAAM3R,KAAQqE,EACfvF,EAAOyU,aAAa,CAACvT,EAAMqE,GAInC,IAAK,IAAMrE,KAAQ,IAAI,CAACL,KAAK,CAEzB,IAAK,IAAM6T,KAAaxT,EAAKQ,SAAS,CAG7BgT,CAAAA,EAAU9Q,MAAM,EAAI8Q,EAAUC,MAAM,AAAD,GAAMD,EAAUxP,EAAE,GACtDlF,EAAO4U,aAAa,CAACF,GACrBA,EAAUG,WAAW,CAAG,CAAA,EAIxC,CAKAD,cAAc5Q,CAAK,CAAE,CACjB,IAAM8Q,EAAO,CAAC5T,EAAM6T,KAChB,IAAMC,EAAW9T,EAAK6C,MAAM,CAACC,EAAO+Q,GAChCb,EAIJ,OAHUrQ,KAAKsQ,GAAG,CAACjT,EAAKmH,KAAK,CAAG2M,EAEhC9T,EAAKmH,KAAK,CAAInH,CAAAA,EAAK+T,SAAS,EAAI/T,EAAK+T,SAAS,CAAC3N,MAAM,EAAI,CAAA,EAAK4N,EAElE,EACMvT,EAAWqC,EAAMrC,QAAQ,CAAEC,EAASoC,EAAMpC,MAAM,CAAEmB,EAAQ,IAAI,CAACA,KAAK,CAAE,CAAE4E,SAAAA,CAAQ,CAAE,CAAG5E,EAAOmR,EAAoB,IAAI,CAACA,iBAAiB,CAAEjS,EAAU,IAAI,CAACA,OAAO,CAAEkT,EAAgBrD,GAAkB9N,EAAMmR,aAAa,CAAElT,EAAQkT,aAAa,EAAGC,EAAS,AAACrS,CAAAA,EAAM4E,QAAQ,CAAG,CAAC,IAAI,CAAC0M,WAAW,CAAG,IAAI,CAACA,WAAW,AAAD,EAC/SpS,EAAQoT,WAAW,CAAGC,EAAW3T,EAAS4T,KAAK,CAAEC,EAAQ5T,EAAO2T,KAAK,CAAEnR,EAAWJ,EAAMI,QAAQ,CAChG8Q,EAAarR,KAAKC,GAAG,CAACE,EAAMJ,MAAM,CAAGsQ,EAAmB,IAAI,CAACjS,OAAO,CAAC2E,YAAY,EAAG6O,EAAQX,EAAKnT,EAAU,aAAc+T,EAAMZ,EAAKlT,EAAQ,WAAY+T,EAAQ,IAAI,CAAC3F,SAAS,CAAE4F,EAAWJ,EAAQF,EAAWK,EAgBlN,GAfI5S,EAAM4E,QAAQ,GACd8N,EAAQ1S,EAAMiE,SAAS,CAAGyO,EAC1BC,EAAM,AAAC3S,CAAAA,EAAMiE,SAAS,EAAI,CAAA,EAAK0O,EAC/BC,EAAQ,CAACA,EACTT,EAAa,CAACA,EACdU,EAAWN,EAAWE,GAE1BxR,EAAM6R,SAAS,CAAG,OAClB7R,EAAM8R,QAAQ,CAAG,CACbL,EACAA,EAAQP,EACRQ,EACAA,EAAMR,EACT,CAEGU,GAAY,AAAe,UAAf,OAAOF,EACnB1R,EAAMiR,SAAS,CAAG,CACdrW,EAAG,CACC,CAAC,IAAK0W,EAAWK,EAAOF,EAAM,CAC9B,CACI,IACAH,EAAWK,EAAQP,EACnBK,EACAD,EAAQJ,EACRM,EACAF,EACAE,EACH,CACD,CAAC,IAAKF,EAASpR,CAAAA,EAAWuR,EAAQ,CAAA,EAAID,EAAMR,EAAa,EAAE,CAC3D,CAAC,IAAKM,EAAOE,EAAMR,EAAW,CAC9B,CACI,IACAM,EAAQJ,EACRM,EAAMR,EACNI,EAAWK,EAAQP,EACnBK,EAAQP,EACRI,EAAWK,EAAOF,EAAQP,EAC7B,CACD,CAAC,IAAI,CACR,AACL,OASC,GAAI,AAAe,UAAf,OAAOQ,EAAkB,CAC9B,IAAiBK,EAAQhT,EAAM6E,UAAU,CAAG6N,EAAQP,EAAYc,EAAKR,EAAxD,GAAuEN,EAAYe,EAAKT,EAAxF,GAAkHU,EAAKZ,EAAWK,EAAOQ,EAAKD,EAA9I,GAAyJE,EAAKD,EAAKjB,EAAYmB,EAAMZ,EAAOa,EAAMb,EAAQP,EAAYqB,EAAMD,EAA5N,GAAwOE,EAAKD,EAAMR,EAAOU,EAAKD,EAA/P,GAA0QE,EAAKD,EAAKvB,EAAYyB,EAAMjB,EAAKkB,EAAMD,EAAMzB,EAAY2B,EAAMD,EAAzU,GAAqVE,EAAOR,EAAMpB,AAAa,GAAbA,EAAkB6B,EAAMN,EAAKvB,AAAa,GAAbA,EAAkB8B,EAAOJ,EAAM1B,AAAa,GAAbA,EAAkB+B,EAAMC,AAA3U1B,EAAgVN,AAAa,GAAbA,EAAkBiC,EAAMjB,EAAKhB,AAAa,GAAbA,CACrelR,CAAAA,EAAMiR,SAAS,CAAG,CACdrW,EAAG,CACC,CAAC,IAAKsX,EAAIG,EAAI,CACd,CAAC,IAAKc,EAAKd,EAAKD,EAAIU,EAAMV,EAAIG,EAAI,CAClC,CAAC,IAAKH,EAAII,EAAG,CACb,CAAC,IAAKJ,EAAIW,EAAKI,EAAKT,EAAIR,EAAIQ,EAAG,CAC/B,CAAC,IAP+GlB,EAOtGkB,EAAG,CACb,CAAC,IAAKO,EAAKP,EAAIV,EAAIe,EAAKf,EAAIQ,EAAG,CAC/B,CAAC,IAAKR,EAAIa,EAAI,CACd,CAAC,IAAKb,EAAIgB,EAAMC,EAAKN,EAV2FnB,EAUlFmB,EAAI,CAClC,CAAC,IAX+GnB,EAWtGoB,EAAI,CACd,CAAC,IAAKX,EAAIW,EAAKX,EAAIW,EAAKX,EAAIY,EAAI,CAChC,CAAC,IAAKZ,EAAIO,EAAG,CACb,CAAC,IAAKP,EAAIQ,EAAIR,EAAIQ,EAd8FjB,EActFiB,EAAG,CAC7B,CAAC,IAAKP,EAAIO,EAAG,CACb,CAAC,IAAKN,EAAIM,EAAIN,EAAIM,EAAIN,EAAIK,EAAG,CAC7B,CAAC,IAAKL,EAAII,EAAI,CACd,CAAC,IAAKJ,EAAIG,EAAKH,EAAIG,EAAKJ,EAAII,EAAI,CAChC,CAAC,IAAI,CACR,AACL,CACJ,CAoBA,GAlBAtS,EAAMoT,KAAK,CAAG,CACVvR,EAAGyP,EAAW,AAACE,CAAAA,EAAQF,EAAWK,CAAI,EAAK,EAC3CpS,EAAGkS,EAAQ,AAACC,CAAAA,EAAMD,CAAI,EAAK,EAC3BnO,OAAQ4N,EACRpN,MAAO,CACX,EAEA9D,EAAMqT,UAAU,CAAGtU,EAAM4E,QAAQ,CAAG,CAChC5E,EAAMiE,SAAS,CAAGhD,EAAMoT,KAAK,CAAC7T,CAAC,CAAG2R,EAAa,EAC/CnS,EAAMgF,SAAS,CAAG/D,EAAMoT,KAAK,CAACvR,CAAC,CAClC,CAAG,CACA7B,EAAMoT,KAAK,CAACvR,CAAC,CACb7B,EAAMoT,KAAK,CAAC7T,CAAC,CAAG2R,EAAa,EAChC,CAGDlR,EAAMT,CAAC,CAAGS,EAAMsT,KAAK,CAAG,EACxBtT,EAAM6B,CAAC,CAAG7B,EAAMuT,KAAK,CAAG,EACpB,CAACvT,EAAM/B,OAAO,CAACgD,KAAK,EACpB,GAAIkQ,AAAkB,SAAlBA,EACAnR,EAAMiB,KAAK,CAAGtD,EAASsD,KAAK,MAE3B,GAAIkQ,AAAkB,OAAlBA,EACLnR,EAAMiB,KAAK,CAAGrD,EAAOqD,KAAK,MAEzB,GAAIkQ,AAAkB,aAAlBA,EAA8B,CACnC,IAAMqC,EAAYvS,EAAMtD,EAASsD,KAAK,EAAE5F,GAAG,GAAIoY,EAAUxS,EAAMrD,EAAOqD,KAAK,EAAE5F,GAAG,EAChF2E,CAAAA,EAAMiB,KAAK,CAAG,CACVyS,eAAgB,CACZ1B,GAAI,EACJC,GAAI,EACJ0B,GAAI,EACJC,GAAI,CACR,EACAC,MAAO,CACH,CAAC,EAAGlQ,EAAW6P,EAAYC,EAAQ,CACnC,CAAC,EAAG9P,EAAW8P,EAAUD,EAAU,CACtC,AACL,CACJ,EAER,CAKA/C,cAAcvT,CAAI,CAAEqE,CAAM,CAAE,CACxB,IAAM2O,EAAoB,IAAI,CAACA,iBAAiB,CAAEnR,EAAQ,IAAI,CAACA,KAAK,CAAEd,EAAU,IAAI,CAACA,OAAO,CAAE,CAAEqS,aAAAA,CAAY,CAAErN,YAAAA,EAAc,CAAC,CAAE,CAAGhF,EAASkF,EAAMjG,EAAKsC,MAAM,GAAIsU,EAAajU,KAAKC,GAAG,CAACD,KAAKmE,KAAK,CAACb,EAAM+M,GAAoB,IAAI,CAACjS,OAAO,CAAC2E,YAAY,EAAGoJ,EAAYnM,KAAKmE,KAAK,CAAC,IAAI,CAACgI,SAAS,EAAG+H,EAAaxS,EAAOgB,YAAY,CAACxC,MAAM,CAAC7C,EAAMgT,GAAoB8D,EAActG,EAAMI,GAAkBiG,EAAW3P,WAAW,CAAG7C,EAAOgB,YAAY,CAACc,GAAG,CAAC6M,GACpb6D,EAAWvP,WAAW,EAAIvB,GAAcO,EAAOkK,EAAM,IAAI,CAAC2C,WAAW,CAAGnT,EAAKqE,MAAM,CACnF0B,EAAc,EAAGA,GAAe8K,GAA4B7Q,EAAKe,OAAO,CAACc,EAAM4E,QAAQ,CACvF,iBACA,mBAAmB,EAAI,EAAGqI,GAAYsF,EAAWvS,EAAM4E,QAAQ,CAC/D5E,EAAMgF,SAAS,CAAGP,EAClBA,EAGJ,GAFAtG,EAAKiG,GAAG,CAAGA,EAEPA,EAAK,CAELjG,EAAK2U,SAAS,CAAG,cACjB3U,EAAKqU,KAAK,CAAGD,EACbpU,EAAKmH,KAAK,CAAG2P,EACb,IAAInS,EAAIyP,EAAU/R,EAAIyU,EAAalQ,EAAQ5G,EAAKe,OAAO,CAAC6F,KAAK,EAAI7F,EAAQ6F,KAAK,EAAIkI,EAAW1I,EAASpG,EAAKe,OAAO,CAACqF,MAAM,EAAIrF,EAAQqF,MAAM,EAAIwQ,EAGzIG,EAAIxG,EAAMM,GAA6B,AAAwB,UAAxB,OAAOuC,EAChDA,EAAa7P,MAAM,CACnB6P,GAAgB,EAAIxM,GAAQ,EAAGgQ,EAAa,EAC5C/U,CAAAA,EAAM4E,QAAQ,GACd9B,EAAIyP,EAAWtF,EACfzM,EAAIR,EAAMiE,SAAS,CAAGgR,EAAcF,EACpChQ,EAAQ5G,EAAKe,OAAO,CAACqF,MAAM,EAAIrF,EAAQqF,MAAM,EAAI0I,EACjD1I,EAASpG,EAAKe,OAAO,CAAC6F,KAAK,EAAI7F,EAAQ6F,KAAK,EAAIgQ,GAGpD5W,EAAKgX,SAAS,CAAG9F,GAAaC,YAAY,CAAC,CACvCxN,MAAO,IAAI,CAACgK,iBAAiB,CAAC3N,EAAK2D,KAAK,CAAC,CACzC+L,aAAc1P,EAAKe,OAAO,AAC9B,GAEAf,EAAKqW,KAAK,CAAG,EACbrW,EAAKoW,KAAK,CAAG,EAEbpW,EAAKmW,UAAU,CAAGtU,EAAM4E,QAAQ,CAAG,CAC/B5E,EAAMiE,SAAS,CAAGzD,EAAI+D,EAAS,EAC/BvE,EAAMgF,SAAS,CAAGlC,EAAIiC,EAAQ,EACjC,CAAG,CACAjC,EAAIiC,EAAQ,EACZvE,EAAI+D,EAAS,EAChB,CACDpG,EAAK+T,SAAS,CAAG,CACbpP,EAAAA,EACAtC,EAAAA,EACAuE,MAAAA,EACAR,OAAAA,EACA2Q,EAAAA,EACAE,QAASjX,EAAKiD,QAAQ,GAAK,GAAK,MACpC,CACJ,MAEIjD,EAAKgX,SAAS,CAAG,CACblO,QAAS,CAAA,CACb,CAER,CACJ,CAMAoI,GAAagG,cAAc,CAAGvG,GAAmB1D,EAA0BiK,cAAc,CAnpD5D,CACzBnR,YAAa,EACb0H,aAAc,CAAA,EAQd0G,YAAa,IAYb/C,WAAY,CACRtI,QAAS,CAAA,EACTqO,gBAAiB,OACjBC,KAAM,CAAA,EAYNC,WAAY,KAAK,EASjBC,cAAe,WACX,OAAO,IAAI,CAACxU,KAAK,CAACM,IAAI,AAC1B,EACAmU,OAAQ,KAAK,EAIbC,UAAW,WAEX,EACAC,OAAQ,CAAA,CACZ,EAMAC,oBAAqB,CAAA,EAiFrBzD,cAAe,OAMftB,YAAa,GAMbD,QAAS,EAeThN,aAAc,EAYdW,cAAe,SAmBfyI,UAAW,GAWX9I,YAAa,GAsBb6I,aAAc,GACd8I,aAAc,CAAA,EACdtF,OAAQ,CACJuF,MAAO,CAKHjF,YAAa,EAIbD,QAAS,CACb,EAMAmF,SAAU,CAKNlF,YAAa,GAIbD,QAAS,GAOTzR,UAAW,CAEP6W,SAAU,EACd,CACJ,CACJ,EACAC,QAAS,CAaLC,cAAe,CAAA,EACfC,aAAc,2DACdC,YAAa,0EAObb,WAAY,uCAChB,CACJ,GAw2CApT,EAAwBnC,OAAO,CAnsD0B2C,EAmsDLyM,IACpDT,EAAoBS,GAAa5S,SAAS,CAAE,CACxC6Z,QAASjL,EAAW5O,SAAS,CAAC6Z,OAAO,CAGrClW,WAAYgC,EAAwBhC,UAAU,CAC9CmW,QAAS,CAAA,EACTC,WAAY,CAAA,EACZC,YAAa,CAAA,EACb7G,WAAY,CAAA,EACZ8G,gBAAiB,CAAA,EACjBC,cAAe,CAAC,OAAQ,KAAM,SAAS,CACvCxZ,WA/sDqDyF,EAgtDrDgU,YAAa,AAAC/Z,IAA8Cga,IAAI,AACpE,GACA7Z,IAA4D8Z,kBAAkB,CAAC,SAAUzH,IAwH5D,IAAM0H,GAAela,WAEzCka,MAAcC,OAAO"}