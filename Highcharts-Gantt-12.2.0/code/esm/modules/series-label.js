import*as t from"../highcharts.js";var e={};e.n=t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},e.d=(t,o)=>{for(var r in o)e.o(o,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:o[r]})},e.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let o=t.default;var r=e.n(o);let a=t.default.Templating;var i=e.n(a);let h={enabled:!0,connectorAllowed:!1,connectorNeighbourDistance:24,format:void 0,formatter:void 0,minFontSize:null,maxFontSize:null,onArea:null,style:{fontSize:"0.8em",fontWeight:"bold"},useHTML:!1,boxesToAvoid:[]};function n(t,e,o,r,a,i){let h=(i-e)*(o-t)-(r-e)*(a-t);return h>0||!(h<0)}function l(t,e,o,r,a,i,h,l){return n(t,e,a,i,h,l)!==n(o,r,a,i,h,l)&&n(t,e,o,r,a,i)!==n(t,e,o,r,h,l)}let{animObject:s}=r(),{format:c}=i(),{setOptions:p}=r(),{composed:d}=r(),{boxIntersectLine:f,intersectRect:u}={boxIntersectLine:function(t,e,o,r,a,i,h,n){return l(t,e,t+o,e,a,i,h,n)||l(t+o,e,t+o,e+r,a,i,h,n)||l(t,e+r,t+o,e+r,a,i,h,n)||l(t,e,t,e+r,a,i,h,n)},intersectRect:function(t,e){return!(e.left>t.right||e.right<t.left||e.top>t.bottom||e.bottom<t.top)}},{addEvent:x,extend:b,fireEvent:g,isNumber:m,pick:y,pushUnique:X,syncTimeout:M}=r();function Y(t,e,o,r,a){let i=t.chart,h=t.options.label||{},n=y(h.onArea,!!t.area),l=n||h.connectorAllowed,s=i.boxesToAvoid,c=Number.MAX_VALUE,p=Number.MAX_VALUE,d,x,b,g,m,X,M;for(X=0;s&&X<s.length;X+=1)if(u(s[X],{left:e,right:e+r.width,top:o,bottom:o+r.height}))return!1;for(X=0;X<i.series.length;X+=1){let s=i.series[X],u=s.interpolatedPoints&&[...s.interpolatedPoints];if(s.visible&&u){let y=i.plotHeight/10;for(let t=i.plotTop;t<=i.plotTop+i.plotHeight;t+=y)u.unshift({chartX:i.plotLeft,chartY:t}),u.push({chartX:i.plotLeft+i.plotWidth,chartY:t});for(M=1;M<u.length;M+=1){if(u[M].chartX>=e-16&&u[M-1].chartX<=e+r.width+16){if(f(e,o,r.width,r.height,u[M-1].chartX,u[M-1].chartY,u[M].chartX,u[M].chartY))return!1;t===s&&!b&&a&&(b=f(e-16,o-16,r.width+32,r.height+32,u[M-1].chartX,u[M-1].chartY,u[M].chartX,u[M].chartY))}(l||b)&&(t!==s||n)&&(c=Math.min(c,(g=e+r.width/2-u[M].chartX)*g+(m=o+r.height/2-u[M].chartY)*m))}if(!n&&l&&t===s&&(a&&!b||c<Math.pow(h.connectorNeighbourDistance||1,2))){for(M=1;M<u.length;M+=1)(d=Math.min(Math.pow(e+r.width/2-u[M].chartX,2)+Math.pow(o+r.height/2-u[M].chartY,2),Math.pow(e-u[M].chartX,2)+Math.pow(o-u[M].chartY,2),Math.pow(e+r.width-u[M].chartX,2)+Math.pow(o-u[M].chartY,2),Math.pow(e+r.width-u[M].chartX,2)+Math.pow(o+r.height-u[M].chartY,2),Math.pow(e-u[M].chartX,2)+Math.pow(o+r.height-u[M].chartY,2)))<p&&(p=d,x=u[M]);b=!0}}}return(!a||!!b)&&{x:e,y:o,weight:c-(x?p:0),connectorPoint:x}}function w(t){if(this.renderer){let e=this,o=s(e.renderer.globalAnimation).duration;e.labelSeries=[],e.labelSeriesMaxSum=0,e.seriesLabelTimer&&r().clearTimeout(e.seriesLabelTimer),e.series.forEach(function(r){let a=r.options.label||{},i=r.labelBySeries,h=i&&i.closest,n=r.getColumn("y");a.enabled&&r.visible&&(r.graph||r.area)&&!r.boosted&&e.labelSeries&&(e.labelSeries.push(r),a.minFontSize&&a.maxFontSize&&n.length&&(r.sum=n.reduce((t,e)=>(t||0)+(e||0),0),e.labelSeriesMaxSum=Math.max(e.labelSeriesMaxSum||0,r.sum||0)),"load"===t.type&&(o=Math.max(o,s(r.options.animation).duration)),h&&(void 0!==h[0].plotX?i.animate({x:h[0].plotX+h[1],y:h[0].plotY+h[2]}):i.attr({opacity:0})))}),e.seriesLabelTimer=M(function(){e.series&&e.labelSeries&&function(t){t.boxesToAvoid=[];let e=t.labelSeries||[],o=t.boxesToAvoid;t.series.forEach(t=>(t.points||[]).forEach(e=>(e.dataLabels||[]).forEach(e=>{let{width:r,height:a}=e.getBBox(),i=(e.translateX||0)+(t.xAxis?t.xAxis.pos:t.chart.plotLeft),h=(e.translateY||0)+(t.yAxis?t.yAxis.pos:t.chart.plotTop);o.push({left:i,top:h,right:i+r,bottom:h+a})}))),e.forEach(function(t){let e=t.options.label||{};t.interpolatedPoints=function(t){let e,o,r,a,i;if(!t.xAxis&&!t.yAxis)return;let h=t.points,n=[],l=t.graph||t.area,s=l&&l.element,c=t.chart.inverted,p=t.xAxis,d=t.yAxis,f=c?d.pos:p.pos,u=c?p.pos:d.pos,x=c?p.len:d.len,b=c?d.len:p.len,g=y((t.options.label||{}).onArea,!!t.area),X=d.getThreshold(t.options.threshold),M={},Y=c?"chartCenterX":"chartCenterY";function w(t){let e=Math.round((t.plotX||0)/8)+","+Math.round((t.plotY||0)/8);M[e]||(M[e]=1,n.push(t))}if(t.getPointSpline&&s&&s.getPointAtLength&&!g&&h.length<(t.chart.plotSizeX||0)/16){let t=l.toD&&l.attr("d");for(l.toD&&l.attr({d:l.toD}),r=s.getTotalLength(),e=0;e<r;e+=16){let t=s.getPointAtLength(e),o=c?b-t.y:t.x,r=c?x-t.x:t.y;w({chartX:f+o,chartY:u+r,plotX:o,plotY:r})}t&&l.attr({d:t});let o=h[h.length-1].pos();w({chartX:f+(o?.[0]||0),chartY:u+(o?.[1]||0)})}else{let t;for(e=0,r=h.length;e<r;e+=1){let r=h[e],[n,l]=r.pos()||[],{plotHigh:s}=r;if(m(n)&&m(l)){let e={plotX:n,plotY:l,chartX:f+n,chartY:u+l};if(g&&(s&&(e.plotY=s,e.chartY=u+s),c?e.chartCenterX=f+b-((s||r.plotY||0)+y(r.yBottom,X))/2:e.chartCenterY=u+((s||l)+y(r.yBottom,X))/2),t&&(o=Math.max(Math.abs(e.chartX-t.chartX),Math.abs(e.chartY-t.chartY)))>16&&o<999)for(i=1,a=Math.ceil(o/16);i<a;i+=1)w({chartX:t.chartX+(e.chartX-t.chartX)*(i/a),chartY:t.chartY+(e.chartY-t.chartY)*(i/a),[Y]:(t[Y]||0)+((e[Y]||0)-(t[Y]||0))*(i/a),plotX:(t.plotX||0)+(n-(t.plotX||0))*(i/a),plotY:(t.plotY||0)+(l-(t.plotY||0))*(i/a)});w(e),t=e}}}return n}(t),o.push(...e.boxesToAvoid||[])}),t.series.forEach(function(e){let o=e.options.label;if(!o||!e.xAxis&&!e.yAxis)return;let r="highcharts-color-"+y(e.colorIndex,"none"),a=!e.labelBySeries,i=o.minFontSize,h=o.maxFontSize,n=t.inverted,l=n?e.yAxis.pos:e.xAxis.pos,p=n?e.xAxis.pos:e.yAxis.pos,d=t.inverted?e.yAxis.len:e.xAxis.len,f=t.inverted?e.xAxis.len:e.yAxis.len,u=e.interpolatedPoints,x=y(o.onArea,!!e.area),g=[],m=e.getColumn("x"),X,M,w,A,S,v,L=e.labelBySeries,T,P,B;function C(t,e,o){let r=Math.max(l,y(P,-1/0)),a=Math.min(l+d,y(B,1/0));return t>r&&t<=a-o.width&&e>=p&&e<=p+f-o.height}function E(){L&&(e.labelBySeries=L.destroy())}if(x&&!n&&(T=[e.xAxis.toPixels(m[0]),e.xAxis.toPixels(m[m.length-1])],P=Math.min.apply(Math,T),B=Math.max.apply(Math,T)),e.visible&&!e.boosted&&u){if(!L){var z,D,F;let a=e.name;if("string"==typeof o.format?a=c(o.format,e,t):o.formatter&&(a=o.formatter.call(e)),e.labelBySeries=L=t.renderer.label(a,0,0,"connector",0,0,o.useHTML).addClass("highcharts-series-label highcharts-series-label-"+e.index+" "+(e.options.className||"")+" "+r),!t.renderer.styledMode){let r="string"==typeof e.color?e.color:"#666666";L.css(b({color:x?t.renderer.getContrast(r):r},o.style||{})),L.attr({opacity:+!!t.renderer.forExport,stroke:e.color,"stroke-width":1})}i&&h&&L.css({fontSize:(z=e,D=i,F=h,D+(z.sum||0)/(z.chart.labelSeriesMaxSum||0)*(F-D)+"px")}),L.attr({padding:0,zIndex:3}).add()}for((X=L.getBBox()).width=Math.round(X.width),S=u.length-1;S>0;S-=1)x?C(M=(u[S].chartCenterX??u[S].chartX)-X.width/2,w=(u[S].chartCenterY??u[S].chartY)-X.height/2,X)&&(v=Y(e,M,w,X)):(C(M=u[S].chartX+3,w=u[S].chartY-X.height-3,X)&&(v=Y(e,M,w,X,!0)),v&&g.push(v),C(M=u[S].chartX+3,w=u[S].chartY+3,X)&&(v=Y(e,M,w,X,!0)),v&&g.push(v),C(M=u[S].chartX-X.width-3,w=u[S].chartY+3,X)&&(v=Y(e,M,w,X,!0)),v&&g.push(v),C(M=u[S].chartX-X.width-3,w=u[S].chartY-X.height-3,X)&&(v=Y(e,M,w,X,!0))),v&&g.push(v);if(o.connectorAllowed&&!g.length&&!x)for(M=l+d-X.width;M>=l;M-=16)for(w=p;w<p+f-X.height;w+=16)(A=Y(e,M,w,X,!0))&&g.push(A);if(g.length){g.sort((t,e)=>e.weight-t.weight),v=g[0],(t.boxesToAvoid||[]).push({left:v.x,right:v.x+X.width,top:v.y,bottom:v.y+X.height});let o=Math.sqrt(Math.pow(Math.abs(v.x-(L.x||0)),2)+Math.pow(Math.abs(v.y-(L.y||0)),2));if(o&&e.labelBySeries){let r,i={opacity:+!!t.renderer.forExport,x:v.x,y:v.y},h={opacity:1};o<=10&&(h={x:i.x,y:i.y},i={}),a&&(r=s(e.options.animation),r.duration*=.2),e.labelBySeries.attr(b(i,{anchorX:v.connectorPoint&&(v.connectorPoint.plotX||0)+l,anchorY:v.connectorPoint&&(v.connectorPoint.plotY||0)+p})).animate(h,r),e.options.kdNow=!0,e.buildKDTree();let n=e.searchPoint({chartX:v.x,chartY:v.y},!0);n&&(L.closest=[n,v.x-(n.plotX||0),v.y-(n.plotY||0)])}}else E()}else E()}),g(t,"afterDrawSeriesLabels")}(e)},e.renderer.forExport||!o?0:o)}}function A(t,e,o,r,a){let i=a&&a.anchorX,h=a&&a.anchorY,n,l,s=o/2;return m(i)&&m(h)&&(n=[["M",i,h]],(l=e-h)<0&&(l=-r-l),l<o&&(s=i<t+o/2?l:o-l),h>e+r?n.push(["L",t+s,e+r]):h<e?n.push(["L",t+s,e]):i<t?n.push(["L",t,e+r/2]):i>t+o&&n.push(["L",t+o,e+r/2])),n||[]}let S=r();({compose:function(t,e){X(d,"SeriesLabel")&&(x(t,"load",w),x(t,"redraw",w),e.prototype.symbols.connector=A,p({plotOptions:{series:{label:h}}}))}}).compose(S.Chart,S.SVGRenderer);let v=r();export{v as default};