{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/annotations-advanced\n * @requires highcharts\n *\n * Annotations module\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__annotations_src_js_518a1ad8__ from \"./annotations.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"./annotations.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const external_annotations_src_js_namespaceObject = x({  });\n;// ./code/es-modules/Extensions/Annotations/AnnotationChart.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent, erase, find, fireEvent, pick, wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add an annotation to the chart after render time.\n *\n * @sample highcharts/annotations/add-annotation/\n *         Add annotation\n *\n * @function Highcharts.Chart#addAnnotation\n *\n * @param  {Highcharts.AnnotationsOptions} options\n *         The annotation options for the new, detailed annotation.\n *\n * @param {boolean} [redraw]\n *\n * @return {Highcharts.Annotation}\n *         The newly generated annotation.\n */\nfunction chartAddAnnotation(userOptions, redraw) {\n    const annotation = this.initAnnotation(userOptions);\n    this.options.annotations.push(annotation.options);\n    if (pick(redraw, true)) {\n        annotation.redraw();\n        annotation.graphic.attr({\n            opacity: 1\n        });\n    }\n    return annotation;\n}\n/**\n * @private\n */\nfunction chartCallback() {\n    const chart = this;\n    chart.plotBoxClip = this.renderer.clipRect(this.plotBox);\n    chart.controlPointsGroup = chart.renderer\n        .g('control-points')\n        .attr({ zIndex: 99 })\n        .clip(chart.plotBoxClip)\n        .add();\n    chart.options.annotations.forEach((annotationOptions, i) => {\n        if (\n        // Verify that it has not been previously added in a responsive rule\n        !chart.annotations.some((annotation) => annotation.options === annotationOptions)) {\n            const annotation = chart.initAnnotation(annotationOptions);\n            chart.options.annotations[i] = annotation.options;\n        }\n    });\n    chart.drawAnnotations();\n    addEvent(chart, 'redraw', chart.drawAnnotations);\n    addEvent(chart, 'destroy', function () {\n        chart.plotBoxClip.destroy();\n        chart.controlPointsGroup.destroy();\n    });\n    addEvent(chart, 'exportData', function (event) {\n        const annotations = chart.annotations, csvColumnHeaderFormatter = ((this.options.exporting &&\n            this.options.exporting.csv) ||\n            {}).columnHeaderFormatter, \n        // If second row doesn't have xValues\n        // then it is a title row thus multiple level header is in use.\n        multiLevelHeaders = !event.dataRows[1].xValues, annotationHeader = (chart.options.lang &&\n            chart.options.lang.exportData &&\n            chart.options.lang.exportData.annotationHeader), columnHeaderFormatter = function (index) {\n            let s;\n            if (csvColumnHeaderFormatter) {\n                s = csvColumnHeaderFormatter(index);\n                if (s !== false) {\n                    return s;\n                }\n            }\n            s = annotationHeader + ' ' + index;\n            if (multiLevelHeaders) {\n                return {\n                    columnTitle: s,\n                    topLevelColumnTitle: s\n                };\n            }\n            return s;\n        }, startRowLength = event.dataRows[0].length, annotationSeparator = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.itemDelimiter), joinAnnotations = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.join);\n        annotations.forEach((annotation) => {\n            if (annotation.options.labelOptions &&\n                annotation.options.labelOptions.includeInDataExport) {\n                annotation.labels.forEach((label) => {\n                    if (label.options.text) {\n                        const annotationText = label.options.text;\n                        label.points.forEach((points) => {\n                            const annotationX = points.x, xAxisIndex = points.series.xAxis ?\n                                points.series.xAxis.index :\n                                -1;\n                            let wasAdded = false;\n                            // Annotation not connected to any xAxis -\n                            // add new row.\n                            if (xAxisIndex === -1) {\n                                const n = event.dataRows[0].length, newRow = new Array(n);\n                                for (let i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow.push(annotationText);\n                                newRow.xValues = [];\n                                newRow.xValues[xAxisIndex] = annotationX;\n                                event.dataRows.push(newRow);\n                                wasAdded = true;\n                            }\n                            // Annotation placed on a exported data point\n                            // - add new column\n                            if (!wasAdded) {\n                                event.dataRows.forEach((row) => {\n                                    if (!wasAdded &&\n                                        row.xValues &&\n                                        xAxisIndex !== void 0 &&\n                                        annotationX === row.xValues[xAxisIndex]) {\n                                        if (joinAnnotations &&\n                                            row.length > startRowLength) {\n                                            row[row.length - 1] += (annotationSeparator +\n                                                annotationText);\n                                        }\n                                        else {\n                                            row.push(annotationText);\n                                        }\n                                        wasAdded = true;\n                                    }\n                                });\n                            }\n                            // Annotation not placed on any exported data point,\n                            // but connected to the xAxis - add new row\n                            if (!wasAdded) {\n                                const n = event.dataRows[0].length, newRow = new Array(n);\n                                for (let i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow[0] = annotationX;\n                                newRow.push(annotationText);\n                                newRow.xValues = [];\n                                if (xAxisIndex !== void 0) {\n                                    newRow.xValues[xAxisIndex] = annotationX;\n                                }\n                                event.dataRows.push(newRow);\n                            }\n                        });\n                    }\n                });\n            }\n        });\n        let maxRowLen = 0;\n        event.dataRows.forEach((row) => {\n            maxRowLen = Math.max(maxRowLen, row.length);\n        });\n        const newRows = maxRowLen - event.dataRows[0].length;\n        for (let i = 0; i < newRows; i++) {\n            const header = columnHeaderFormatter(i + 1);\n            if (multiLevelHeaders) {\n                event.dataRows[0].push(header.topLevelColumnTitle);\n                event.dataRows[1].push(header.columnTitle);\n            }\n            else {\n                event.dataRows[0].push(header);\n            }\n        }\n    });\n}\n/**\n * @private\n */\nfunction chartDrawAnnotations() {\n    this.plotBoxClip.attr(this.plotBox);\n    this.annotations.forEach((annotation) => {\n        annotation.redraw();\n        annotation.graphic.animate({\n            opacity: 1\n        }, annotation.animationConfig);\n    });\n}\n/**\n * Remove an annotation from the chart.\n *\n * @function Highcharts.Chart#removeAnnotation\n *\n * @param {number|string|Highcharts.Annotation} idOrAnnotation\n *        The annotation's id or direct annotation object.\n */\nfunction chartRemoveAnnotation(idOrAnnotation) {\n    const annotations = this.annotations, annotation = (idOrAnnotation.coll === 'annotations') ?\n        idOrAnnotation :\n        find(annotations, function (annotation) {\n            return annotation.options.id === idOrAnnotation;\n        });\n    if (annotation) {\n        fireEvent(annotation, 'remove');\n        erase(this.options.annotations, annotation.options);\n        erase(annotations, annotation);\n        annotation.destroy();\n    }\n}\n/**\n * Create lookups initially\n * @private\n */\nfunction onChartAfterInit() {\n    const chart = this;\n    chart.annotations = [];\n    if (!this.options.annotations) {\n        this.options.annotations = [];\n    }\n}\n/**\n * @private\n */\nfunction wrapPointerOnContainerMouseDown(proceed) {\n    if (!this.chart.hasDraggedAnnotation) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\n/**\n * @private\n */\nvar AnnotationChart;\n(function (AnnotationChart) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(AnnotationClass, ChartClass, PointerClass) {\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.addAnnotation) {\n            const pointerProto = PointerClass.prototype;\n            addEvent(ChartClass, 'afterInit', onChartAfterInit);\n            chartProto.addAnnotation = chartAddAnnotation;\n            chartProto.callbacks.push(chartCallback);\n            chartProto.collectionsWithInit.annotations = [chartAddAnnotation];\n            chartProto.collectionsWithUpdate.push('annotations');\n            chartProto.drawAnnotations = chartDrawAnnotations;\n            chartProto.removeAnnotation = chartRemoveAnnotation;\n            chartProto.initAnnotation = function chartInitAnnotation(userOptions) {\n                const Constructor = (AnnotationClass.types[userOptions.type] ||\n                    AnnotationClass), annotation = new Constructor(this, userOptions);\n                this.annotations.push(annotation);\n                return annotation;\n            };\n            wrap(pointerProto, 'onContainerMouseDown', wrapPointerOnContainerMouseDown);\n        }\n    }\n    AnnotationChart.compose = compose;\n})(AnnotationChart || (AnnotationChart = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_AnnotationChart = (AnnotationChart);\n\n;// ./code/es-modules/Extensions/Annotations/AnnotationDefaults.js\n/* *\n *\n *  Imports\n *\n * */\n\nconst { defined } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A basic type of an annotation. It allows to add custom labels\n * or shapes. The items can be tied to points, axis coordinates\n * or chart pixel coordinates.\n *\n * @sample highcharts/annotations/basic/\n *         Basic annotations\n * @sample highcharts/demo/annotations/\n *         Advanced annotations\n * @sample highcharts/css/annotations\n *         Styled mode\n * @sample highcharts/annotations-advanced/controllable\n *         Controllable items\n * @sample {highstock} stock/annotations/fibonacci-retracements\n *         Custom annotation, Fibonacci retracement\n *\n * @type         {Array<*>}\n * @since        6.0.0\n * @requires     modules/annotations\n * @optionparent annotations\n */\nconst AnnotationDefaults = {\n    /**\n     * Sets an ID for an annotation. Can be user later when\n     * removing an annotation in [Chart#removeAnnotation(id)](\n     * /class-reference/Highcharts.Chart#removeAnnotation) method.\n     *\n     * @type      {number|string}\n     * @apioption annotations.id\n     */\n    /**\n     * Whether the annotation is visible.\n     *\n     * @sample highcharts/annotations/visible/\n     *         Set annotation visibility\n     */\n    visible: true,\n    /**\n     * Enable or disable the initial animation when a series is\n     * displayed for the `annotation`. The animation can also be set\n     * as a configuration object. Please note that this option only\n     * applies to the initial animation.\n     * For other animations, see [chart.animation](#chart.animation)\n     * and the animation parameter under the API methods.\n     * The following properties are supported:\n     *\n     * - `defer`: The animation delay time in milliseconds.\n     *\n     * @sample {highcharts} highcharts/annotations/defer/\n     *          Animation defer settings\n     * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n     * @since 8.2.0\n     */\n    animation: {},\n    /**\n     * Whether to hide the part of the annotation\n     * that is outside the plot area.\n     *\n     * @sample highcharts/annotations/label-crop-overflow/\n     *         Crop line annotation\n     * @type  {boolean}\n     * @since 9.3.0\n     */\n    crop: true,\n    /**\n     * The animation delay time in milliseconds.\n     * Set to `0` renders annotation immediately.\n     * As `undefined` inherits defer time from the [series.animation.defer](#plotOptions.series.animation.defer).\n     *\n     * @type      {number}\n     * @since 8.2.0\n     * @apioption annotations.animation.defer\n     */\n    /**\n     * Allow an annotation to be draggable by a user. Possible\n     * values are `'x'`, `'xy'`, `'y'` and `''` (disabled).\n     *\n     * @sample highcharts/annotations/draggable/\n     *         Annotations draggable: 'xy'\n     *\n     * @type {Highcharts.AnnotationDraggableValue}\n     */\n    draggable: 'xy',\n    /**\n     * Options for annotation's labels. Each label inherits options\n     * from the labelOptions object. An option from the labelOptions\n     * can be overwritten by config for a specific label.\n     *\n     * @requires modules/annotations\n     */\n    labelOptions: {\n        /**\n         * The alignment of the annotation's label. If right,\n         * the right side of the label should be touching the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'center',\n        /**\n         * Whether to allow the annotation's labels to overlap.\n         * To make the labels less sensitive for overlapping,\n         * the can be set to 0.\n         *\n         * @sample highcharts/annotations/tooltip-like/\n         *         Hide overlapping labels\n         */\n        allowOverlap: false,\n        /**\n         * The background color or gradient for the annotation's\n         * label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        backgroundColor: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The border color for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString}\n         */\n        borderColor: \"#000000\" /* Palette.neutralColor100 */,\n        /**\n         * The border radius in pixels for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderRadius: 3,\n        /**\n         * The border width in pixels for the annotation's label\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderWidth: 1,\n        /**\n         * A class name for styling by CSS.\n         *\n         * @sample highcharts/css/annotations\n         *         Styled mode annotations\n         *\n         * @since 6.0.5\n         */\n        className: 'highcharts-no-tooltip',\n        /**\n         * Whether to hide the annotation's label\n         * that is outside the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         */\n        crop: false,\n        /**\n         * The label's pixel distance from the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type      {number}\n         * @apioption annotations.labelOptions.distance\n         */\n        /**\n         * A\n         * [format](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * string for the data label.\n         *\n         * @see [plotOptions.series.dataLabels.format](plotOptions.series.dataLabels.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.format\n         */\n        /**\n         * Alias for the format option.\n         *\n         * @see [format](annotations.labelOptions.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.text\n         */\n        /**\n         * Callback JavaScript function to format the annotation's\n         * label. Note that if a `format` or `text` are defined,\n         * the format or text take precedence and the formatter is\n         * ignored. `This` refers to a point object.\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type    {Highcharts.FormatterCallbackFunction<Highcharts.Point>}\n         * @default function () { return defined(this.y) ? this.y : 'Annotation label'; }\n         */\n        formatter: function () {\n            return defined(this.y) ? '' + this.y : 'Annotation label';\n        },\n        /**\n         * Whether the annotation is visible in the exported data\n         * table.\n         *\n         * @sample highcharts/annotations/include-in-data-export/\n         *         Do not include in the data export\n         *\n         * @since 8.2.0\n         * @requires modules/export-data\n         */\n        includeInDataExport: true,\n        /**\n         * How to handle the annotation's label that flow outside\n         * the plot area. The justify option aligns the label inside\n         * the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         *\n         * @validvalue [\"allow\", \"justify\"]\n         */\n        overflow: 'justify',\n        /**\n         * When either the borderWidth or the backgroundColor is\n         * set, this is the padding within the box.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        padding: 5,\n        /**\n         * The shadow of the box. The shadow can be an object\n         * configuration containing `color`, `offsetX`, `offsetY`,\n         * `opacity` and `width`.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {boolean|Highcharts.ShadowOptionsObject}\n         */\n        shadow: false,\n        /**\n         * The name of a symbol to use for the border around the\n         * label. Symbols are predefined functions on the Renderer\n         * object.\n         *\n         * @sample highcharts/annotations/shapes/\n         *         Available shapes for labels\n         */\n        shape: 'callout',\n        /**\n         * Styles for the annotation's label.\n         *\n         * @see [plotOptions.series.dataLabels.style](plotOptions.series.dataLabels.style.html)\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @ignore */\n            fontSize: '0.7em',\n            /** @ignore */\n            fontWeight: 'normal',\n            /** @ignore */\n            color: 'contrast'\n        },\n        /**\n         * Whether to [use HTML](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting#html)\n         * to render the annotation's label.\n         */\n        useHTML: false,\n        /**\n         * The vertical alignment of the annotation's label.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'bottom',\n        /**\n         * The x position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        x: 0,\n        /**\n         * The y position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        y: -16\n    },\n    /**\n     * An array of labels for the annotation. For options that apply\n     * to multiple labels, they can be added to the\n     * [labelOptions](annotations.labelOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.labelOptions\n     * @apioption annotations.labels\n     */\n    /**\n     * This option defines the point to which the label will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-point/\n     *         Attach annotation to a mock point\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @requires  modules/annotations\n     * @apioption annotations.labels.point\n     */\n    /**\n     * An array of shapes for the annotation. For options that apply\n     * to multiple shapes, then can be added to the\n     * [shapeOptions](annotations.shapeOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.shapeOptions\n     * @apioption annotations.shapes\n     */\n    /**\n     * This option defines the point to which the shape will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @extends   annotations.labels.point\n     * @requires  modules/annotations\n     * @apioption annotations.shapes.point\n     */\n    /**\n     * An array of points for the shape\n     * or a callback function that returns that shape point.\n     *\n     * This option is available\n     * for shapes which can use multiple points such as path. A\n     * point can be either a point object or a point's id.\n     *\n     * @see [annotations.shapes.point](annotations.shapes.point.html)\n     *\n     * @type      {Array<Highcharts.AnnotationShapePointOptions>}\n     * @extends   annotations.labels.point\n     * @apioption annotations.shapes.points\n     */\n    /**\n     * The URL for an image to use as the annotation shape. Note,\n     * type has to be set to `'image'`.\n     *\n     * @see [annotations.shapes.type](annotations.shapes.type)\n     * @sample highcharts/annotations/shape-src/\n     *         Define a marker image url for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.src\n     */\n    /**\n     * Id of the marker which will be drawn at the final vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerEnd\n     */\n    /**\n     * Id of the marker which will be drawn at the first vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample {highcharts} highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerStart\n     */\n    /**\n     * Options for annotation's shapes. Each shape inherits options\n     * from the shapeOptions object. An option from the shapeOptions\n     * can be overwritten by config for a specific shape.\n     *\n     * @requires  modules/annotations\n     */\n    shapeOptions: {\n        /**\n         *\n         * The radius of the shape in y direction.\n         * Used for the ellipse.\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.ry\n         **/\n        /**\n         *\n         * The xAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.xAxis\n         **/\n        /**\n         * The yAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.yAxis\n         **/\n        /**\n         * The width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.width\n         **/\n        /**\n         * The height of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.height\n         */\n        /**\n         * The type of the shape.\n         * Available options are circle, rect and ellipse.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {string}\n         * @default   rect\n         * @apioption annotations.shapeOptions.type\n         */\n        /**\n         * The URL for an image to use as the annotation shape.\n         * Note, type has to be set to `'image'`.\n         *\n         * @see [annotations.shapeOptions.type](annotations.shapeOptions.type)\n         * @sample highcharts/annotations/shape-src/\n         *         Define a marker image url for annotations\n         *\n         * @type      {string}\n         * @apioption annotations.shapeOptions.src\n         */\n        /**\n         * Name of the dash style to use for the shape's stroke.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-dashstyle-all/\n         *         Possible values demonstrated\n         *\n         * @type      {Highcharts.DashStyleValue}\n         * @apioption annotations.shapeOptions.dashStyle\n         */\n        /**\n         * The color of the shape's stroke.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString}\n         */\n        stroke: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The pixel stroke width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        strokeWidth: 1,\n        /**\n         * The color of the shape's fill.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        fill: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The radius of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        r: 0,\n        /**\n         * Defines additional snapping area around an annotation\n         * making this annotation to focus. Defined in pixels.\n         */\n        snap: 2\n    },\n    /**\n     * Options for annotation's control points. Each control point\n     * inherits options from controlPointOptions object.\n     * Options from the controlPointOptions can be overwritten\n     * by options in a specific control point.\n     *\n     * @declare  Highcharts.AnnotationControlPointOptionsObject\n     * @requires modules/annotations\n     */\n    controlPointOptions: {\n        /**\n         * @type      {Highcharts.AnnotationControlPointPositionerFunction}\n         * @apioption annotations.controlPointOptions.positioner\n         */\n        /**\n         * @type {Highcharts.Dictionary<Function>}\n         */\n        events: {},\n        /**\n         * @type {Highcharts.SVGAttributes}\n         */\n        style: {\n            cursor: 'pointer',\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            stroke: \"#000000\" /* Palette.neutralColor100 */,\n            'stroke-width': 2\n        },\n        height: 10,\n        symbol: 'circle',\n        visible: false,\n        width: 10\n    },\n    /**\n     * Event callback when annotation is added to the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.add\n     */\n    /**\n     * Event callback when annotation is updated (e.g. drag and\n     * dropped or resized by control points).\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.afterUpdate\n     */\n    /**\n     * Fires when the annotation is clicked.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.click\n     */\n    /**\n     * Fires when the annotation is dragged.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @apioption annotations.events.drag\n     */\n    /**\n     * Event callback when annotation is removed from the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.remove\n     */\n    /**\n     * Events available in annotations.\n     *\n     * @requires modules/annotations\n     */\n    events: {},\n    /**\n     * The Z index of the annotation.\n     */\n    zIndex: 6\n}; // Type options are expected but not set\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_AnnotationDefaults = (AnnotationDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/EventEmitter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc, isTouchDevice } = (external_highcharts_src_js_default_default());\n\nconst { addEvent: EventEmitter_addEvent, fireEvent: EventEmitter_fireEvent, objectEach, pick: EventEmitter_pick, removeEvent } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nclass EventEmitter {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add emitter events.\n     * @private\n     */\n    addEvents() {\n        const emitter = this, addMouseDownEvent = function (element) {\n            EventEmitter_addEvent(element, isTouchDevice ? 'touchstart' : 'mousedown', (e) => {\n                emitter.onMouseDown(e);\n            }, { passive: false });\n        };\n        addMouseDownEvent(this.graphic.element);\n        (emitter.labels || []).forEach((label) => {\n            if (label.options.useHTML &&\n                label.graphic.text &&\n                !label.graphic.text.foreignObject) {\n                // Mousedown event bound to HTML element (#13070).\n                addMouseDownEvent(label.graphic.text.element);\n            }\n        });\n        objectEach(emitter.options.events, (event, type) => {\n            const eventHandler = function (e) {\n                if (type !== 'click' || !emitter.cancelClick) {\n                    event.call(emitter, emitter.chart.pointer?.normalize(e), emitter.target);\n                }\n            };\n            if ((emitter.nonDOMEvents || []).indexOf(type) === -1) {\n                EventEmitter_addEvent(emitter.graphic.element, type, eventHandler, { passive: false });\n                if (emitter.graphic.div) {\n                    EventEmitter_addEvent(emitter.graphic.div, type, eventHandler, { passive: false });\n                }\n            }\n            else {\n                EventEmitter_addEvent(emitter, type, eventHandler, { passive: false });\n            }\n        });\n        if (emitter.options.draggable) {\n            EventEmitter_addEvent(emitter, 'drag', emitter.onDrag);\n            if (!emitter.graphic.renderer.styledMode) {\n                const cssPointer = {\n                    cursor: {\n                        x: 'ew-resize',\n                        y: 'ns-resize',\n                        xy: 'move'\n                    }[emitter.options.draggable]\n                };\n                emitter.graphic.css(cssPointer);\n                (emitter.labels || []).forEach((label) => {\n                    if (label.options.useHTML &&\n                        label.graphic.text &&\n                        !label.graphic.text.foreignObject) {\n                        label.graphic.text.css(cssPointer);\n                    }\n                });\n            }\n        }\n        if (!emitter.isUpdating) {\n            EventEmitter_fireEvent(emitter, 'add');\n        }\n    }\n    /**\n     * Destroy the event emitter.\n     */\n    destroy() {\n        this.removeDocEvents();\n        removeEvent(this);\n        this.hcEvents = null;\n    }\n    /**\n     * Map mouse move event to the radians.\n     * @private\n     */\n    mouseMoveToRadians(e, cx, cy) {\n        let prevDy = e.prevChartY - cy, prevDx = e.prevChartX - cx, dy = e.chartY - cy, dx = e.chartX - cx, temp;\n        if (this.chart.inverted) {\n            temp = prevDx;\n            prevDx = prevDy;\n            prevDy = temp;\n            temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        return Math.atan2(dy, dx) - Math.atan2(prevDy, prevDx);\n    }\n    /**\n     * Map mouse move to the scale factors.\n     * @private\n     */\n    mouseMoveToScale(e, cx, cy) {\n        const prevDx = e.prevChartX - cx, prevDy = e.prevChartY - cy, dx = e.chartX - cx, dy = e.chartY - cy;\n        let sx = (dx || 1) / (prevDx || 1), sy = (dy || 1) / (prevDy || 1);\n        if (this.chart.inverted) {\n            const temp = sy;\n            sy = sx;\n            sx = temp;\n        }\n        return {\n            x: sx,\n            y: sy\n        };\n    }\n    /**\n     * Map mouse move event to the distance between two following events.\n     * @private\n     */\n    mouseMoveToTranslation(e) {\n        let dx = e.chartX - e.prevChartX, dy = e.chartY - e.prevChartY, temp;\n        if (this.chart.inverted) {\n            temp = dy;\n            dy = dx;\n            dx = temp;\n        }\n        return {\n            x: dx,\n            y: dy\n        };\n    }\n    /**\n     * Drag and drop event. All basic annotations should share this\n     * capability as well as the extended ones.\n     * @private\n     */\n    onDrag(e) {\n        if (this.chart.isInsidePlot(e.chartX - this.chart.plotLeft, e.chartY - this.chart.plotTop, {\n            visiblePlotOnly: true\n        })) {\n            const translation = this.mouseMoveToTranslation(e);\n            if (this.options.draggable === 'x') {\n                translation.y = 0;\n            }\n            if (this.options.draggable === 'y') {\n                translation.x = 0;\n            }\n            const emitter = this;\n            if (emitter.points.length) {\n                emitter.translate(translation.x, translation.y);\n            }\n            else {\n                emitter.shapes.forEach((shape) => shape.translate(translation.x, translation.y));\n                emitter.labels.forEach((label) => label.translate(translation.x, translation.y));\n            }\n            this.redraw(false);\n        }\n    }\n    /**\n     * Mouse down handler.\n     * @private\n     */\n    onMouseDown(e) {\n        if (e.preventDefault) {\n            e.preventDefault();\n        }\n        // On right click, do nothing:\n        if (e.button === 2) {\n            return;\n        }\n        const emitter = this, pointer = emitter.chart.pointer, \n        // Using experimental property on event object to check if event was\n        // created by touch on screen on hybrid device (#18122)\n        firesTouchEvents = (e?.sourceCapabilities?.firesTouchEvents) || false;\n        e = pointer?.normalize(e) || e;\n        let prevChartX = e.chartX, prevChartY = e.chartY;\n        emitter.cancelClick = false;\n        emitter.chart.hasDraggedAnnotation = true;\n        emitter.removeDrag = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchmove' : 'mousemove', function (e) {\n            emitter.hasDragged = true;\n            e = pointer?.normalize(e) || e;\n            e.prevChartX = prevChartX;\n            e.prevChartY = prevChartY;\n            EventEmitter_fireEvent(emitter, 'drag', e);\n            prevChartX = e.chartX;\n            prevChartY = e.chartY;\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n        emitter.removeMouseUp = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchend' : 'mouseup', function () {\n            // Sometimes the target is the annotation and sometimes its the\n            // controllable\n            const annotation = EventEmitter_pick(emitter.target && emitter.target.annotation, emitter.target);\n            if (annotation) {\n                // Keep annotation selected after dragging control point\n                annotation.cancelClick = emitter.hasDragged;\n            }\n            emitter.cancelClick = emitter.hasDragged;\n            emitter.chart.hasDraggedAnnotation = false;\n            if (emitter.hasDragged) {\n                // ControlPoints vs Annotation:\n                EventEmitter_fireEvent(EventEmitter_pick(annotation, // #15952\n                emitter), 'afterUpdate');\n            }\n            emitter.hasDragged = false;\n            emitter.onMouseUp();\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n    }\n    /**\n     * Mouse up handler.\n     */\n    onMouseUp() {\n        this.removeDocEvents();\n    }\n    /**\n     * Remove emitter document events.\n     * @private\n     */\n    removeDocEvents() {\n        if (this.removeDrag) {\n            this.removeDrag = this.removeDrag();\n        }\n        if (this.removeMouseUp) {\n            this.removeMouseUp = this.removeMouseUp();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_EventEmitter = (EventEmitter);\n\n;// ./code/es-modules/Extensions/Annotations/ControlPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge, pick: ControlPoint_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A control point class which is a connection between controllable\n * transform methods and a user actions.\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.AnnotationControlPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * A chart instance.\n *\n * @param {Highcharts.AnnotationControllable} target\n * A controllable instance which is a target for a control point.\n *\n * @param {Highcharts.AnnotationControlPointOptionsObject} options\n * An options object.\n *\n * @param {number} [index]\n * Point index.\n */\nclass ControlPoint extends Annotations_EventEmitter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, target, options, index) {\n        super();\n        /**\n         * List of events for `annotation.options.events` that should not be\n         * added to `annotation.graphic` but to the `annotation`.\n         * @private\n         * @name Highcharts.AnnotationControlPoint#nonDOMEvents\n         * @type {Array<string>}\n         */\n        this.nonDOMEvents = ['drag'];\n        this.chart = chart;\n        this.target = target;\n        this.options = options;\n        this.index = ControlPoint_pick(options.index, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy the control point.\n     * @private\n     */\n    destroy() {\n        super.destroy();\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        this.chart = null;\n        this.target = null;\n        this.options = null;\n    }\n    /**\n     * Redraw the control point.\n     * @private\n     * @param {boolean} [animation]\n     */\n    redraw(animation) {\n        this.graphic[animation ? 'animate' : 'attr'](this.options.positioner.call(this, this.target));\n    }\n    /**\n     * Render the control point.\n     * @private\n     */\n    render() {\n        const chart = this.chart, options = this.options;\n        this.graphic = chart.renderer\n            .symbol(options.symbol, 0, 0, options.width, options.height)\n            .add(chart.controlPointsGroup)\n            .css(options.style);\n        this.setVisibility(options.visible);\n        // `npm test -- --tests \"@highcharts/highcharts/annotations-advanced/*\"`\n        this.addEvents();\n    }\n    /**\n     * Set the visibility of the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#setVisibility\n     *\n     * @param {boolean} visible\n     * Visibility of the control point.\n     *\n     */\n    setVisibility(visible) {\n        this.graphic[visible ? 'show' : 'hide']();\n        this.options.visible = visible;\n    }\n    /**\n     * Update the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#update\n     *\n     * @param {Partial<Highcharts.AnnotationControlPointOptionsObject>} userOptions\n     * New options for the control point.\n     */\n    update(userOptions) {\n        const chart = this.chart, target = this.target, index = this.index, options = merge(true, this.options, userOptions);\n        this.destroy();\n        this.constructor(chart, target, options, index);\n        this.render(chart.controlPointsGroup);\n        this.redraw();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_ControlPoint = (ControlPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback to modify annotation's positioner controls.\n *\n * @callback Highcharts.AnnotationControlPointPositionerFunction\n * @param {Highcharts.AnnotationControlPoint} this\n * @param {Highcharts.AnnotationControllable} target\n * @return {Highcharts.PositionObject}\n */\n(''); // Keeps doclets above in JS file\n\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Extensions/Annotations/MockPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: seriesProto } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { defined: MockPoint_defined, fireEvent: MockPoint_fireEvent } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A trimmed point object which imitates {@link Highchart.Point} class. It is\n * created when there is a need of pointing to some chart's position using axis\n * values or pixel values\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationMockPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * The chart instance.\n *\n * @param {Highcharts.AnnotationControllable|null} target\n * The related controllable.\n *\n * @param {Highcharts.AnnotationMockPointOptionsObject|Function} options\n * The options object.\n */\nclass MockPoint {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Create a mock point from a real Highcharts point.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.Point} point\n     *\n     * @return {Highcharts.AnnotationMockPoint}\n     * A mock point instance.\n     */\n    static fromPoint(point) {\n        return new MockPoint(point.series.chart, null, {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        });\n    }\n    /**\n     * Get the pixel position from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @param {boolean} [paneCoordinates]\n     *        Whether the pixel position should be relative\n     *\n     * @return {Highcharts.PositionObject} pixel position\n     */\n    static pointToPixels(point, paneCoordinates) {\n        const series = point.series, chart = series.chart;\n        let x = point.plotX || 0, y = point.plotY || 0, plotBox;\n        if (chart.inverted) {\n            if (point.mock) {\n                x = point.plotY;\n                y = point.plotX;\n            }\n            else {\n                x = chart.plotWidth - (point.plotY || 0);\n                y = chart.plotHeight - (point.plotX || 0);\n            }\n        }\n        if (series && !paneCoordinates) {\n            plotBox = series.getPlotBox();\n            x += plotBox.translateX;\n            y += plotBox.translateY;\n        }\n        return {\n            x: x,\n            y: y\n        };\n    }\n    /**\n     * Get fresh mock point options from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * A mock point's options.\n     */\n    static pointToOptions(point) {\n        return {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        };\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, target, options) {\n        /* *\n         *\n         * Functions\n         *\n         * */\n        /**\n         * A flag indicating that a point is not the real one.\n         *\n         * @type {boolean}\n         * @default true\n         */\n        this.mock = true;\n        // Circular reference for formats and formatters\n        this.point = this;\n        /**\n         * A mock series instance imitating a real series from a real point.\n         *\n         * @name Annotation.AnnotationMockPoint#series\n         * @type {Highcharts.AnnotationMockSeries}\n         */\n        this.series = {\n            visible: true,\n            chart: chart,\n            getPlotBox: seriesProto.getPlotBox\n        };\n        /**\n         * @name Annotation.AnnotationMockPoint#target\n         * @type {Highcharts.AnnotationControllable|null}\n         */\n        this.target = target || null;\n        /**\n         * Options for the mock point.\n         *\n         * @name Annotation.AnnotationMockPoint#options\n         * @type {Highcharts.AnnotationsMockPointOptionsObject}\n         */\n        this.options = options;\n        /**\n         * If an xAxis is set it represents the point's value in terms of the\n         * xAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#x\n         * @type {number|undefined}\n         */\n        /**\n         * If an yAxis is set it represents the point's value in terms of the\n         * yAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#y\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel x coordinate relative to its plot\n         * box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotX\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel y position relative to its plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotY\n         * @type {number|undefined}\n         */\n        /**\n         * Whether the point is inside the plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#isInside\n         * @type {boolean|undefined}\n         */\n        this.applyOptions(this.getOptions());\n    }\n    /**\n     * Apply options for the point.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     */\n    applyOptions(options) {\n        this.command = options.command;\n        this.setAxis(options, 'x');\n        this.setAxis(options, 'y');\n        this.refresh();\n    }\n    /**\n     * Get the point's options.\n     * @private\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * The mock point's options.\n     */\n    getOptions() {\n        return this.hasDynamicOptions() ?\n            this.options(this.target) :\n            this.options;\n    }\n    /**\n     * Check if the point has dynamic options.\n     * @private\n     * @return {boolean}\n     * A positive flag if the point has dynamic options.\n     */\n    hasDynamicOptions() {\n        return typeof this.options === 'function';\n    }\n    /**\n     * Check if the point is inside its pane.\n     * @private\n     * @return {boolean} A flag indicating whether the point is inside the pane.\n     */\n    isInsidePlot() {\n        const plotX = this.plotX, plotY = this.plotY, xAxis = this.series.xAxis, yAxis = this.series.yAxis, e = {\n            x: plotX,\n            y: plotY,\n            isInsidePlot: true,\n            options: {}\n        };\n        if (xAxis) {\n            e.isInsidePlot = MockPoint_defined(plotX) && plotX >= 0 && plotX <= xAxis.len;\n        }\n        if (yAxis) {\n            e.isInsidePlot =\n                e.isInsidePlot &&\n                    MockPoint_defined(plotY) &&\n                    plotY >= 0 && plotY <= yAxis.len;\n        }\n        MockPoint_fireEvent(this.series.chart, 'afterIsInsidePlot', e);\n        return e.isInsidePlot;\n    }\n    /**\n     * Refresh point values and coordinates based on its options.\n     * @private\n     */\n    refresh() {\n        const series = this.series, xAxis = series.xAxis, yAxis = series.yAxis, options = this.getOptions();\n        if (xAxis) {\n            this.x = options.x;\n            this.plotX = xAxis.toPixels(options.x, true);\n        }\n        else {\n            this.x = void 0;\n            this.plotX = options.x;\n        }\n        if (yAxis) {\n            this.y = options.y;\n            this.plotY = yAxis.toPixels(options.y, true);\n        }\n        else {\n            this.y = null;\n            this.plotY = options.y;\n        }\n        this.isInside = this.isInsidePlot();\n    }\n    /**\n     * Refresh point options based on its plot coordinates.\n     * @private\n     */\n    refreshOptions() {\n        const series = this.series, xAxis = series.xAxis, yAxis = series.yAxis;\n        this.x = this.options.x = xAxis ?\n            this.options.x = xAxis.toValue(this.plotX, true) :\n            this.plotX;\n        this.y = this.options.y = yAxis ?\n            yAxis.toValue(this.plotY, true) :\n            this.plotY;\n    }\n    /**\n     * Rotate the point.\n     * @private\n     * @param {number} cx origin x rotation\n     * @param {number} cy origin y rotation\n     * @param {number} radians\n     */\n    rotate(cx, cy, radians) {\n        if (!this.hasDynamicOptions()) {\n            const cos = Math.cos(radians), sin = Math.sin(radians), x = this.plotX - cx, y = this.plotY - cy, tx = x * cos - y * sin, ty = x * sin + y * cos;\n            this.plotX = tx + cx;\n            this.plotY = ty + cy;\n            this.refreshOptions();\n        }\n    }\n    /**\n     * Scale the point.\n     *\n     * @private\n     *\n     * @param {number} cx\n     * Origin x transformation.\n     *\n     * @param {number} cy\n     * Origin y transformation.\n     *\n     * @param {number} sx\n     * Scale factor x.\n     *\n     * @param {number} sy\n     * Scale factor y.\n     */\n    scale(cx, cy, sx, sy) {\n        if (!this.hasDynamicOptions()) {\n            const x = this.plotX * sx, y = this.plotY * sy, tx = (1 - sx) * cx, ty = (1 - sy) * cy;\n            this.plotX = tx + x;\n            this.plotY = ty + y;\n            this.refreshOptions();\n        }\n    }\n    /**\n     * Set x or y axis.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     * @param {string} xOrY\n     * 'x' or 'y' string literal\n     */\n    setAxis(options, xOrY) {\n        const axisName = (xOrY + 'Axis'), axisOptions = options[axisName], chart = this.series.chart;\n        this.series[axisName] =\n            typeof axisOptions === 'object' ?\n                axisOptions :\n                MockPoint_defined(axisOptions) ?\n                    (chart[axisName][axisOptions] ||\n                        // @todo v--- (axisName)[axisOptions] ?\n                        chart.get(axisOptions)) :\n                    null;\n    }\n    /**\n     * Transform the mock point to an anchor (relative position on the chart).\n     * @private\n     * @return {Array<number>}\n     * A quadruple of numbers which denotes x, y, width and height of the box\n     **/\n    toAnchor() {\n        const anchor = [this.plotX, this.plotY, 0, 0];\n        if (this.series.chart.inverted) {\n            anchor[0] = this.plotY;\n            anchor[1] = this.plotX;\n        }\n        return anchor;\n    }\n    /**\n     * Translate the point.\n     *\n     * @private\n     *\n     * @param {number|undefined} cx\n     * Origin x transformation.\n     *\n     * @param {number|undefined} cy\n     * Origin y transformation.\n     *\n     * @param {number} dx\n     * Translation for x coordinate.\n     *\n     * @param {number} dy\n     * Translation for y coordinate.\n     **/\n    translate(_cx, _cy, dx, dy) {\n        if (!this.hasDynamicOptions()) {\n            this.plotX += dx;\n            this.plotY += dy;\n            this.refreshOptions();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_MockPoint = (MockPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.AnnotationMockLabelOptionsObject\n */ /**\n* Point instance of the point.\n* @name Highcharts.AnnotationMockLabelOptionsObject#point\n* @type {Highcharts.AnnotationMockPoint}\n*/ /**\n* X value translated to x axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#x\n* @type {number|null}\n*/ /**\n* Y value translated to y axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#y\n* @type {number|null}\n*/\n/**\n * Object of shape point.\n *\n * @interface Highcharts.AnnotationMockPointOptionsObject\n */ /**\n* The x position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.x\n*/ /**\n* The y position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.y\n*/ /**\n* This number defines which xAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the xAxis array. If the option is not configured or the axis\n* is not found the point's x coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.xAxis\n*/ /**\n* This number defines which yAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the yAxis array. If the option is not configured or the axis\n* is not found the point's y coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.yAxis\n*/\n/**\n * Callback function that returns the annotation shape point.\n *\n * @callback Highcharts.AnnotationMockPointFunction\n *\n * @param  {Highcharts.Annotation} annotation\n *         An annotation instance.\n *\n * @return {Highcharts.AnnotationMockPointOptionsObject}\n *         Annotations shape point.\n */\n/**\n * A mock series instance imitating a real series from a real point.\n * @private\n * @interface Highcharts.AnnotationMockSeries\n */ /**\n* Whether a series is visible.\n* @name Highcharts.AnnotationMockSeries#visible\n* @type {boolean}\n*/ /**\n* A chart instance.\n* @name Highcharts.AnnotationMockSeries#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationMockSeries#getPlotBox\n* @type {Function}\n*/\n/**\n * Indicates if this is a mock point for an annotation.\n * @name Highcharts.Point#mock\n * @type {boolean|undefined}\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/ControlTarget.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n/* *\n *\n *  Composition Namespace\n *\n * */\nvar ControlTarget;\n(function (ControlTarget) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add control points.\n     * @private\n     */\n    function addControlPoints() {\n        const controlPoints = this.controlPoints, controlPointsOptions = this.options.controlPoints || [];\n        controlPointsOptions.forEach((controlPointOptions, i) => {\n            const options = external_highcharts_src_js_default_default().merge(this.options.controlPointOptions, controlPointOptions);\n            if (!options.index) {\n                options.index = i;\n            }\n            controlPointsOptions[i] = options;\n            controlPoints.push(new Annotations_ControlPoint(this.chart, this, options));\n        });\n    }\n    /**\n     * Returns object which denotes anchor position - relative and absolute.\n     * @private\n     * @param {Highcharts.AnnotationPointType} point\n     * An annotation point.\n     *\n     * @return {Highcharts.AnnotationAnchorObject}\n     * An annotation anchor.\n     */\n    function anchor(point) {\n        const plotBox = point.series.getPlotBox(), chart = point.series.chart, box = point.mock ?\n            point.toAnchor() :\n            chart.tooltip &&\n                chart.tooltip.getAnchor.call({\n                    chart: point.series.chart\n                }, point) ||\n                [0, 0, 0, 0], anchor = {\n            x: box[0] + (this.options.x || 0),\n            y: box[1] + (this.options.y || 0),\n            height: box[2] || 0,\n            width: box[3] || 0\n        };\n        return {\n            relativePosition: anchor,\n            absolutePosition: external_highcharts_src_js_default_default().merge(anchor, {\n                x: anchor.x + (point.mock ? plotBox.translateX : chart.plotLeft),\n                y: anchor.y + (point.mock ? plotBox.translateY : chart.plotTop)\n            })\n        };\n    }\n    /**\n     * Adds shared functions to be used with targets of ControlPoint.\n     * @private\n     */\n    function compose(ControlTargetClass) {\n        const controlProto = ControlTargetClass.prototype;\n        if (!controlProto.addControlPoints) {\n            external_highcharts_src_js_default_default().merge(true, controlProto, {\n                addControlPoints,\n                anchor,\n                destroyControlTarget,\n                getPointsOptions,\n                linkPoints,\n                point,\n                redrawControlPoints,\n                renderControlPoints,\n                transform,\n                transformPoint,\n                translate,\n                translatePoint\n            });\n        }\n    }\n    ControlTarget.compose = compose;\n    /**\n     * Destroy control points.\n     * @private\n     */\n    function destroyControlTarget() {\n        this.controlPoints.forEach((controlPoint) => controlPoint.destroy());\n        this.chart = null;\n        this.controlPoints = null;\n        this.points = null;\n        this.options = null;\n        if (this.annotation) {\n            this.annotation = null;\n        }\n    }\n    /**\n     * Get the points options.\n     * @private\n     * @return {Array<Highcharts.PointOptionsObject>}\n     * An array of points' options.\n     */\n    function getPointsOptions() {\n        const options = this.options;\n        return (options.points ||\n            (options.point && external_highcharts_src_js_default_default().splat(options.point)));\n    }\n    /**\n     * Find point-like objects based on points options.\n     * @private\n     * @return {Array<Annotation.PointLike>}\n     *         An array of point-like objects.\n     */\n    function linkPoints() {\n        const pointsOptions = this.getPointsOptions(), points = this.points, len = (pointsOptions && pointsOptions.length) || 0;\n        let i, point;\n        for (i = 0; i < len; i++) {\n            point = this.point(pointsOptions[i], points[i]);\n            if (!point) {\n                points.length = 0;\n                return;\n            }\n            if (point.mock) {\n                point.refresh();\n            }\n            points[i] = point;\n        }\n        return points;\n    }\n    /**\n     * Map point's options to a point-like object.\n     * @private\n     * @param {string|Function|Highcharts.AnnotationMockPointOptionsObject|Highcharts.AnnotationPointType} pointOptions\n     *        Point's options.\n     * @param {Highcharts.AnnotationPointType} point\n     *        A point-like instance.\n     * @return {Highcharts.AnnotationPointType|null}\n     *         If the point is found/set returns this point, otherwise null\n     */\n    function point(pointOptions, point) {\n        if (pointOptions && pointOptions.series) {\n            return pointOptions;\n        }\n        if (!point || point.series === null) {\n            if (external_highcharts_src_js_default_default().isObject(pointOptions)) {\n                point = new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n            else if (external_highcharts_src_js_default_default().isString(pointOptions)) {\n                point = this.chart.get(pointOptions) || null;\n            }\n            else if (typeof pointOptions === 'function') {\n                const pointConfig = pointOptions.call(point, this);\n                point = pointConfig.series ?\n                    pointConfig :\n                    new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n        }\n        return point;\n    }\n    /**\n     * Redraw control points.\n     * @private\n     */\n    function redrawControlPoints(animation) {\n        this.controlPoints.forEach((controlPoint) => controlPoint.redraw(animation));\n    }\n    /**\n     * Render control points.\n     * @private\n     */\n    function renderControlPoints() {\n        this.controlPoints.forEach((controlPoint) => controlPoint.render());\n    }\n    /**\n     * Transform control points with a specific transformation.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number} [p2]\n     *        Param for the transformation\n     */\n    function transform(transformation, cx, cy, p1, p2) {\n        if (this.chart.inverted) {\n            const temp = cx;\n            cx = cy;\n            cy = temp;\n        }\n        this.points.forEach((_point, i) => (this.transformPoint(transformation, cx, cy, p1, p2, i)), this);\n    }\n    /**\n     * Transform a point with a specific transformation\n     * If a transformed point is a real point it is replaced with\n     * the mock point.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number|undefined} p2\n     *        Param for the transformation\n     * @param {number} i\n     *        Index of the point\n     */\n    function transformPoint(transformation, cx, cy, p1, p2, i) {\n        let point = this.points[i];\n        if (!point.mock) {\n            point = this.points[i] = Annotations_MockPoint.fromPoint(point);\n        }\n        point[transformation](cx, cy, p1, p2);\n    }\n    /**\n     * Translate control points.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     **/\n    function translate(dx, dy) {\n        this.transform('translate', null, null, dx, dy);\n    }\n    /**\n     * Translate a specific control point.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {number} i\n     *        Index of the point\n     **/\n    function translatePoint(dx, dy, i) {\n        this.transformPoint('translate', null, null, dx, dy, i);\n    }\n})(ControlTarget || (ControlTarget = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_ControlTarget = (ControlTarget);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/Controllable.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { merge: Controllable_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * It provides methods for handling points, control points\n * and points transformations.\n * @private\n */\nclass Controllable {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(annotation, options, index, itemType) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.collection = (itemType === 'label' ? 'labels' : 'shapes');\n        this.controlPoints = [];\n        this.options = options;\n        this.points = [];\n        this.index = index;\n        this.itemType = itemType;\n        this.init(annotation, options, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Redirect attr usage on the controllable graphic element.\n     * @private\n     */\n    attr(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    ..._args) {\n        this.graphic.attr.apply(this.graphic, arguments);\n    }\n    /**\n     * Utility function for mapping item's options\n     * to element's attribute\n     * @private\n     * @param {Highcharts.AnnotationsLabelsOptions|Highcharts.AnnotationsShapesOptions} options\n     * @return {Highcharts.SVGAttributes}\n     *         Mapped options.\n     */\n    attrsFromOptions(options) {\n        const map = this.constructor.attrsMap, attrs = {}, styledMode = this.chart.styledMode;\n        let key, mappedKey;\n        for (key in options) { // eslint-disable-line guard-for-in\n            mappedKey = map[key];\n            if (typeof map[key] !== 'undefined' &&\n                (!styledMode ||\n                    ['fill', 'stroke', 'stroke-width']\n                        .indexOf(mappedKey) === -1)) {\n                attrs[mappedKey] = options[key];\n            }\n        }\n        return attrs;\n    }\n    /**\n     * Destroy a controllable.\n     * @private\n     */\n    destroy() {\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        if (this.tracker) {\n            this.tracker = this.tracker.destroy();\n        }\n        this.destroyControlTarget();\n    }\n    /**\n     * Init the controllable\n     * @private\n     */\n    init(annotation, options, index) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.options = options;\n        this.points = [];\n        this.controlPoints = [];\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n    }\n    /**\n     * Redraw a controllable.\n     * @private\n     */\n    redraw(animation) {\n        this.redrawControlPoints(animation);\n    }\n    /**\n     * Render a controllable.\n     * @private\n     */\n    render(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _parentGroup) {\n        if (this.options.className && this.graphic) {\n            this.graphic.addClass(this.options.className);\n        }\n        this.renderControlPoints();\n    }\n    /**\n     * Rotate a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} radians\n     **/\n    rotate(cx, cy, radians) {\n        this.transform('rotate', cx, cy, radians);\n    }\n    /**\n     * Scale a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} sx\n     *        Scale factor x\n     * @param {number} sy\n     *        Scale factor y\n     */\n    scale(cx, cy, sx, sy) {\n        this.transform('scale', cx, cy, sx, sy);\n    }\n    /**\n     * Set control points' visibility.\n     * @private\n     */\n    setControlPointsVisibility(visible) {\n        this.controlPoints.forEach((controlPoint) => {\n            controlPoint.setVisibility(visible);\n        });\n    }\n    /**\n     * Check if a controllable should be rendered/redrawn.\n     * @private\n     * @return {boolean}\n     *         Whether a controllable should be drawn.\n     */\n    shouldBeDrawn() {\n        return !!this.points.length;\n    }\n    /**\n     * Translate shape within controllable item.\n     * Replaces `controllable.translate` method.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {boolean|undefined} translateSecondPoint\n     *        If the shape has two points attached to it, this option allows you\n     *        to translate also the second point.\n     */\n    translateShape(dx, dy, translateSecondPoint) {\n        const chart = this.annotation.chart, \n        // Annotation.options\n        shapeOptions = this.annotation.userOptions, \n        // Chart.options.annotations\n        annotationIndex = chart.annotations.indexOf(this.annotation), chartOptions = chart.options.annotations[annotationIndex];\n        this.translatePoint(dx, dy, 0);\n        if (translateSecondPoint) {\n            this.translatePoint(dx, dy, 1);\n        }\n        // Options stored in:\n        // - chart (for exporting)\n        // - current config (for redraws)\n        chartOptions[this.collection][this.index]\n            .point = this.options.point;\n        shapeOptions[this.collection][this.index]\n            .point = this.options.point;\n    }\n    /**\n     * Update a controllable.\n     * @private\n     */\n    update(newOptions) {\n        const annotation = this.annotation, options = Controllable_merge(true, this.options, newOptions), parentGroup = this.graphic.parentGroup, Constructor = this.constructor;\n        this.destroy();\n        const newControllable = new Constructor(annotation, options, this.index, this.itemType);\n        Controllable_merge(true, this, newControllable);\n        this.render(parentGroup);\n        this.redraw();\n    }\n}\nAnnotations_ControlTarget.compose(Controllable);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_Controllable = (Controllable);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * An object which denotes a controllable's anchor positions - relative and\n * absolute.\n *\n * @private\n * @interface Highcharts.AnnotationAnchorObject\n */ /**\n* Relative to the plot area position\n* @name Highcharts.AnnotationAnchorObject#relativePosition\n* @type {Highcharts.BBoxObject}\n*/ /**\n* Absolute position\n* @name Highcharts.AnnotationAnchorObject#absolutePosition\n* @type {Highcharts.BBoxObject}\n*/\n/**\n * @interface Highcharts.AnnotationControllable\n */ /**\n* @name Highcharts.AnnotationControllable#annotation\n* @type {Highcharts.Annotation}\n*/ /**\n* @name Highcharts.AnnotationControllable#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationControllable#collection\n* @type {string}\n*/ /**\n* @private\n* @name Highcharts.AnnotationControllable#controlPoints\n* @type {Array<Highcharts.AnnotationControlPoint>}\n*/ /**\n* @name Highcharts.AnnotationControllable#points\n* @type {Array<Highcharts.Point>}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableDefaults.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/**\n * Options for configuring markers for annotations.\n *\n * An example of the arrow marker:\n * <pre>\n * {\n *   arrow: {\n *     id: 'arrow',\n *     tagName: 'marker',\n *     refY: 5,\n *     refX: 5,\n *     markerWidth: 10,\n *     markerHeight: 10,\n *     children: [{\n *       tagName: 'path',\n *       attrs: {\n *         d: 'M 0 0 L 10 5 L 0 10 Z',\n *         'stroke-width': 0\n *       }\n *     }]\n *   }\n * }\n * </pre>\n *\n * @sample highcharts/annotations/custom-markers/\n *         Define a custom marker for annotations\n *\n * @sample highcharts/css/annotations-markers/\n *         Define markers in a styled mode\n *\n * @type         {Highcharts.Dictionary<Highcharts.ASTNode>}\n * @since        6.0.0\n * @optionparent defs\n */\nconst defaultMarkers = {\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    arrow: {\n        tagName: 'marker',\n        attributes: {\n            id: 'arrow',\n            refY: 5,\n            refX: 9,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        /**\n         * @type {Array<Highcharts.DefsOptions>}\n         */\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    d: 'M 0 0 L 10 5 L 0 10 Z', // Triangle (used as an arrow)\n                    'stroke-width': 0\n                }\n            }]\n    },\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    'reverse-arrow': {\n        tagName: 'marker',\n        attributes: {\n            id: 'reverse-arrow',\n            refY: 5,\n            refX: 1,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    // Reverse triangle (used as an arrow)\n                    d: 'M 0 5 L 10 0 L 10 10 Z',\n                    'stroke-width': 0\n                }\n            }]\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ControllableDefaults = {\n    defaultMarkers\n};\n/* harmony default export */ const Controllables_ControllableDefaults = (ControllableDefaults);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllablePath.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { defaultMarkers: ControllablePath_defaultMarkers } = Controllables_ControllableDefaults;\n\n\nconst { addEvent: ControllablePath_addEvent, defined: ControllablePath_defined, extend, merge: ControllablePath_merge, uniqueKey } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst markerEndSetter = createMarkerSetter('marker-end');\nconst markerStartSetter = createMarkerSetter('marker-start');\n// See TRACKER_FILL in highcharts.js\nconst TRACKER_FILL = 'rgba(192,192,192,' + ((external_highcharts_src_js_default_default()).svg ? 0.0001 : 0.002) + ')';\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction createMarkerSetter(markerType) {\n    return function (value) {\n        this.attr(markerType, 'url(#' + value + ')');\n    };\n}\n/**\n * @private\n */\nfunction onChartAfterGetContainer() {\n    this.options.defs = ControllablePath_merge(ControllablePath_defaultMarkers, this.options.defs || {});\n    ///  objectEach(this.options.defs, function (def): void {\n    //     const attributes = def.attributes;\n    //     if (\n    //         def.tagName === 'marker' &&\n    //         attributes &&\n    //         attributes.id &&\n    //         attributes.display !== 'none'\n    //     ) {\n    //         this.renderer.addMarker(attributes.id, def);\n    //     }\n    // }, this);\n}\n/**\n * @private\n */\nfunction svgRendererAddMarker(id, markerOptions) {\n    const options = { attributes: { id } };\n    const attrs = {\n        stroke: markerOptions.color || 'none',\n        fill: markerOptions.color || 'rgba(0, 0, 0, 0.75)'\n    };\n    options.children = (markerOptions.children &&\n        markerOptions.children.map(function (child) {\n            return ControllablePath_merge(attrs, child);\n        }));\n    const ast = ControllablePath_merge(true, {\n        attributes: {\n            markerWidth: 20,\n            markerHeight: 20,\n            refX: 0,\n            refY: 0,\n            orient: 'auto'\n        }\n    }, markerOptions, options);\n    const marker = this.definition(ast);\n    marker.id = id;\n    return marker;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable path class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllablePath\n *\n * @param {Highcharts.Annotation}\n * Related annotation.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A path's options object.\n *\n * @param {number} index\n * Index of the path.\n */\nclass ControllablePath extends Controllables_Controllable {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(ChartClass, SVGRendererClass) {\n        const svgRendererProto = SVGRendererClass.prototype;\n        if (!svgRendererProto.addMarker) {\n            ControllablePath_addEvent(ChartClass, 'afterGetContainer', onChartAfterGetContainer);\n            svgRendererProto.addMarker = svgRendererAddMarker;\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'path';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Map the controllable path to 'd' path attribute.\n     *\n     * @return {Highcharts.SVGPathArray|null}\n     * A path's d attribute.\n     */\n    toD() {\n        const dOption = this.options.d;\n        if (dOption) {\n            return typeof dOption === 'function' ?\n                dOption.call(this) :\n                dOption;\n        }\n        const points = this.points, len = points.length, d = [];\n        let showPath = len, point = points[0], position = showPath && this.anchor(point).absolutePosition, pointIndex = 0, command;\n        if (position) {\n            d.push(['M', position.x, position.y]);\n            while (++pointIndex < len && showPath) {\n                point = points[pointIndex];\n                command = point.command || 'L';\n                position = this.anchor(point).absolutePosition;\n                if (command === 'M') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'L') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'Z') {\n                    d.push([command]);\n                }\n                showPath = point.series.visible;\n            }\n        }\n        return (showPath && this.graphic ?\n            this.chart.renderer.crispLine(d, this.graphic.strokeWidth()) :\n            null);\n    }\n    shouldBeDrawn() {\n        return super.shouldBeDrawn() || !!this.options.d;\n    }\n    render(parent) {\n        const options = this.options, attrs = this.attrsFromOptions(options);\n        this.graphic = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .attr(attrs)\n            .add(parent);\n        this.tracker = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .addClass('highcharts-tracker-line')\n            .attr({\n            zIndex: 2\n        })\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            this.tracker.attr({\n                'stroke-linejoin': 'round', // #1225\n                stroke: TRACKER_FILL,\n                fill: TRACKER_FILL,\n                'stroke-width': this.graphic.strokeWidth() +\n                    options.snap * 2\n            });\n        }\n        super.render();\n        extend(this.graphic, { markerStartSetter, markerEndSetter });\n        this.setMarkers(this);\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const d = this.toD(), action = animation ? 'animate' : 'attr';\n            if (d) {\n                this.graphic[action]({ d: d });\n                this.tracker[action]({ d: d });\n            }\n            else {\n                this.graphic.attr({ d: 'M 0 ' + -9e9 });\n                this.tracker.attr({ d: 'M 0 ' + -9e9 });\n            }\n            this.graphic.placed = this.tracker.placed = !!d;\n        }\n        super.redraw(animation);\n    }\n    /**\n     * Set markers.\n     * @private\n     * @param {Highcharts.AnnotationControllablePath} item\n     */\n    setMarkers(item) {\n        const itemOptions = item.options, chart = item.chart, defs = chart.options.defs, fill = itemOptions.fill, color = ControllablePath_defined(fill) && fill !== 'none' ?\n            fill :\n            itemOptions.stroke;\n        const setMarker = function (markerType) {\n            const markerId = itemOptions[markerType];\n            let def, predefinedMarker, key, marker;\n            if (markerId) {\n                for (key in defs) { // eslint-disable-line guard-for-in\n                    def = defs[key];\n                    if ((markerId === (def.attributes && def.attributes.id) ||\n                        // Legacy, for\n                        // unit-tests/annotations/annotations-shapes\n                        markerId === def.id) &&\n                        def.tagName === 'marker') {\n                        predefinedMarker = def;\n                        break;\n                    }\n                }\n                if (predefinedMarker) {\n                    marker = item[markerType] = chart.renderer\n                        .addMarker((itemOptions.id || uniqueKey()) + '-' + markerId, ControllablePath_merge(predefinedMarker, { color: color }));\n                    item.attr(markerType, marker.getAttribute('id'));\n                }\n            }\n        };\n        ['markerStart', 'markerEnd']\n            .forEach(setMarker);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @name Highcharts.AnnotationControllablePath.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllablePath.attrsMap = {\n    dashStyle: 'dashstyle',\n    strokeWidth: 'stroke-width',\n    stroke: 'stroke',\n    fill: 'fill',\n    zIndex: 'zIndex'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllablePath = (ControllablePath);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableRect.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableRect_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable rect class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableRect\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A rect's options.\n *\n * @param {number} index\n * Index of the rectangle\n */\nclass ControllableRect extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'rect';\n        this.translate = super.translateShape;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .rect(0, -9e9, 0, 0)\n            .attr(attrs)\n            .add(parent);\n        super.render();\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    width: this.options.width,\n                    height: this.options.height\n                });\n            }\n            else {\n                this.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @type {Annotation.ControllableRect.AttrsMap}\n */\nControllableRect.attrsMap = ControllableRect_merge(Controllables_ControllablePath.attrsMap, {\n    width: 'width',\n    height: 'height'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableRect = (ControllableRect);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableCircle.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableCircle_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable circle class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableCircle\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the circle\n */\nclass ControllableCircle extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'circle';\n        this.translate = super.translateShape;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    r: this.options.r\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = !!position;\n        }\n        super.redraw.call(this, animation);\n    }\n    /**\n     * @private\n     */\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .circle(0, -9e9, 0)\n            .attr(attrs)\n            .add(parent);\n        super.render();\n    }\n    /**\n     * Set the radius.\n     * @private\n     * @param {number} r\n     *        A radius to be set\n     */\n    setRadius(r) {\n        this.options.r = r;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element\n * attributes.\n *\n * @name Highcharts.AnnotationControllableCircle.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableCircle.attrsMap = ControllableCircle_merge(Controllables_ControllablePath.attrsMap, { r: 'r' });\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableCircle = (ControllableCircle);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableEllipse.js\n/* *\n *\n * Author: Pawel Lysy\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ControllableEllipse_merge, defined: ControllableEllipse_defined } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable ellipse class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableEllipse\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the Ellipse\n */\nclass ControllableEllipse extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'ellipse';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    init(annotation, options, index) {\n        if (ControllableEllipse_defined(options.yAxis)) {\n            options.points.forEach((point) => {\n                point.yAxis = options.yAxis;\n            });\n        }\n        if (ControllableEllipse_defined(options.xAxis)) {\n            options.points.forEach((point) => {\n                point.xAxis = options.xAxis;\n            });\n        }\n        super.init(annotation, options, index);\n    }\n    /**\n     * Render the element\n     * @private\n     * @param parent\n     *        Parent SVG element.\n     */\n    render(parent) {\n        this.graphic = this.annotation.chart.renderer.createElement('ellipse')\n            .attr(this.attrsFromOptions(this.options))\n            .add(parent);\n        super.render();\n    }\n    /**\n     * Translate the points. Mostly used to handle dragging of the ellipse.\n     * @private\n     */\n    translate(dx, dy) {\n        super.translateShape(dx, dy, true);\n    }\n    /**\n     * Get the distance from the line to the point.\n     * @private\n     * @param point1\n     *        First point which is on the line\n     * @param point2\n     *        Second point\n     * @param x0\n     *        Point's x value from which you want to calculate the distance from\n     * @param y0\n     *        Point's y value from which you want to calculate the distance from\n     */\n    getDistanceFromLine(point1, point2, x0, y0) {\n        return Math.abs((point2.y - point1.y) * x0 - (point2.x - point1.x) * y0 +\n            point2.x * point1.y - point2.y * point1.x) / Math.sqrt((point2.y - point1.y) * (point2.y - point1.y) +\n            (point2.x - point1.x) * (point2.x - point1.x));\n    }\n    /**\n     * The function calculates the svg attributes of the ellipse, and returns\n     * all parameters necessary to draw the ellipse.\n     * @private\n     * @param position\n     *        Absolute position of the first point in points array\n     * @param position2\n     *        Absolute position of the second point in points array\n     */\n    getAttrs(position, position2) {\n        const x1 = position.x, y1 = position.y, x2 = position2.x, y2 = position2.y, cx = (x1 + x2) / 2, cy = (y1 + y2) / 2, rx = Math.sqrt((x1 - x2) * (x1 - x2) / 4 + (y1 - y2) * (y1 - y2) / 4), tan = (y2 - y1) / (x2 - x1);\n        let angle = Math.atan(tan) * 180 / Math.PI;\n        if (cx < x1) {\n            angle += 180;\n        }\n        const ry = this.getRY();\n        return { cx, cy, rx, ry, angle };\n    }\n    /**\n     * Get the value of minor radius of the ellipse.\n     * @private\n     */\n    getRY() {\n        const yAxis = this.getYAxis();\n        return ControllableEllipse_defined(yAxis) ?\n            Math.abs(yAxis.toPixels(this.options.ry) - yAxis.toPixels(0)) :\n            this.options.ry;\n    }\n    /**\n     * Get the yAxis object to which the ellipse is pinned.\n     * @private\n     */\n    getYAxis() {\n        const yAxisIndex = this.options.yAxis;\n        return this.chart.yAxis[yAxisIndex];\n    }\n    /**\n     * Get the absolute coordinates of the MockPoint\n     * @private\n     * @param point\n     *        MockPoint that is added through options\n     */\n    getAbsolutePosition(point) {\n        return this.anchor(point).absolutePosition;\n    }\n    /**\n     * Redraw the element\n     * @private\n     * @param animation\n     *        Display an animation\n     */\n    redraw(animation) {\n        if (this.graphic) {\n            const position = this.getAbsolutePosition(this.points[0]), position2 = this.getAbsolutePosition(this.points[1]), attrs = this.getAttrs(position, position2);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    cx: attrs.cx,\n                    cy: attrs.cy,\n                    rx: attrs.rx,\n                    ry: attrs.ry,\n                    rotation: attrs.angle,\n                    rotationOriginX: attrs.cx,\n                    rotationOriginY: attrs.cy\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n    /**\n     * Set the radius Y.\n     * @private\n     * @param {number} ry\n     *        A radius in y direction to be set\n     */\n    setYRadius(ry) {\n        const shapes = this.annotation.userOptions.shapes;\n        this.options.ry = ry;\n        if (shapes && shapes[0]) {\n            shapes[0].ry = ry;\n            shapes[0].ry = ry;\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element\n * attributes.\n *\n * @name Highcharts.AnnotationControllableEllipse.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableEllipse.attrsMap = ControllableEllipse_merge(Controllables_ControllablePath.attrsMap, {\n    ry: 'ry'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableEllipse = (ControllableEllipse);\n\n;// external [\"../highcharts.js\",\"default\",\"Templating\"]\nconst external_highcharts_src_js_default_Templating_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Templating;\nvar external_highcharts_src_js_default_Templating_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Templating_namespaceObject);\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableLabel.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { format } = (external_highcharts_src_js_default_Templating_default());\n\n\nconst { extend: ControllableLabel_extend, getAlignFactor, isNumber, pick: ControllableLabel_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * General symbol definition for labels with connector\n * @private\n */\nfunction symbolConnector(x, y, w, h, options) {\n    const anchorX = options && options.anchorX, anchorY = options && options.anchorY;\n    let path, yOffset, lateral = w / 2;\n    if (isNumber(anchorX) && isNumber(anchorY)) {\n        path = [['M', anchorX, anchorY]];\n        // Prefer 45 deg connectors\n        yOffset = y - anchorY;\n        if (yOffset < 0) {\n            yOffset = -h - yOffset;\n        }\n        if (yOffset < w) {\n            lateral = anchorX < x + (w / 2) ? yOffset : w - yOffset;\n        }\n        // Anchor below label\n        if (anchorY > y + h) {\n            path.push(['L', x + lateral, y + h]);\n            // Anchor above label\n        }\n        else if (anchorY < y) {\n            path.push(['L', x + lateral, y]);\n            // Anchor left of label\n        }\n        else if (anchorX < x) {\n            path.push(['L', x, y + h / 2]);\n            // Anchor right of label\n        }\n        else if (anchorX > x + w) {\n            path.push(['L', x + w, y + h / 2]);\n        }\n    }\n    return path || [];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable label class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableLabel\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n * @param {Highcharts.AnnotationsLabelOptions} options\n * A label's options.\n * @param {number} index\n * Index of the label.\n */\nclass ControllableLabel extends Controllables_Controllable {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns new aligned position based alignment options and box to align to.\n     * It is almost a one-to-one copy from SVGElement.prototype.align\n     * except it does not use and mutate an element\n     *\n     * @param {Highcharts.AnnotationAlignObject} alignOptions\n     *\n     * @param {Highcharts.BBoxObject} box\n     *\n     * @return {Highcharts.PositionObject}\n     * Aligned position.\n     */\n    static alignedPosition(alignOptions, box) {\n        return {\n            x: Math.round((box.x || 0) + (alignOptions.x || 0) +\n                (box.width - (alignOptions.width || 0)) *\n                    getAlignFactor(alignOptions.align)),\n            y: Math.round((box.y || 0) + (alignOptions.y || 0) +\n                (box.height - (alignOptions.height || 0)) *\n                    getAlignFactor(alignOptions.verticalAlign))\n        };\n    }\n    static compose(SVGRendererClass) {\n        const symbols = SVGRendererClass.prototype.symbols;\n        symbols.connector = symbolConnector;\n    }\n    /**\n     * Returns new alignment options for a label if the label is outside the\n     * plot area. It is almost a one-to-one copy from\n     * Series.prototype.justifyDataLabel except it does not mutate the label and\n     * it works with absolute instead of relative position.\n     */\n    static justifiedOptions(chart, label, alignOptions, alignAttr) {\n        const align = alignOptions.align, verticalAlign = alignOptions.verticalAlign, padding = label.box ? 0 : (label.padding || 0), bBox = label.getBBox(), \n        //\n        options = {\n            align: align,\n            verticalAlign: verticalAlign,\n            x: alignOptions.x,\n            y: alignOptions.y,\n            width: label.width,\n            height: label.height\n        }, \n        //\n        x = (alignAttr.x || 0) - chart.plotLeft, y = (alignAttr.y || 0) - chart.plotTop;\n        let off;\n        // Off left\n        off = x + padding;\n        if (off < 0) {\n            if (align === 'right') {\n                options.align = 'left';\n            }\n            else {\n                options.x = (options.x || 0) - off;\n            }\n        }\n        // Off right\n        off = x + bBox.width - padding;\n        if (off > chart.plotWidth) {\n            if (align === 'left') {\n                options.align = 'right';\n            }\n            else {\n                options.x = (options.x || 0) + chart.plotWidth - off;\n            }\n        }\n        // Off top\n        off = y + padding;\n        if (off < 0) {\n            if (verticalAlign === 'bottom') {\n                options.verticalAlign = 'top';\n            }\n            else {\n                options.y = (options.y || 0) - off;\n            }\n        }\n        // Off bottom\n        off = y + bBox.height - padding;\n        if (off > chart.plotHeight) {\n            if (verticalAlign === 'top') {\n                options.verticalAlign = 'bottom';\n            }\n            else {\n                options.y = (options.y || 0) + chart.plotHeight - off;\n            }\n        }\n        return options;\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'label');\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Translate the point of the label by deltaX and deltaY translations.\n     * The point is the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    translatePoint(dx, dy) {\n        super.translatePoint(dx, dy, 0);\n    }\n    /**\n     * Translate x and y position relative to the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    translate(dx, dy) {\n        const chart = this.annotation.chart, \n        // Annotation.options\n        labelOptions = this.annotation.userOptions, \n        // Chart.options.annotations\n        annotationIndex = chart.annotations.indexOf(this.annotation), chartAnnotations = chart.options.annotations, chartOptions = chartAnnotations[annotationIndex];\n        if (chart.inverted) {\n            const temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        // Local options:\n        this.options.x += dx;\n        this.options.y += dy;\n        // Options stored in chart:\n        chartOptions[this.collection][this.index].x = this.options.x;\n        chartOptions[this.collection][this.index].y = this.options.y;\n        labelOptions[this.collection][this.index].x = this.options.x;\n        labelOptions[this.collection][this.index].y = this.options.y;\n    }\n    render(parent) {\n        const options = this.options, attrs = this.attrsFromOptions(options), style = options.style;\n        this.graphic = this.annotation.chart.renderer\n            .label('', 0, -9999, // #10055\n        options.shape, null, null, options.useHTML, null, 'annotation-label')\n            .attr(attrs)\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            if (style.color === 'contrast') {\n                style.color = this.annotation.chart.renderer.getContrast(ControllableLabel.shapesWithoutBackground.indexOf(options.shape) > -1 ? '#FFFFFF' : options.backgroundColor);\n            }\n            this.graphic\n                .css(options.style)\n                .shadow(options.shadow);\n        }\n        this.graphic.labelrank = options.labelrank;\n        super.render();\n    }\n    redraw(animation) {\n        const options = this.options, text = this.text || options.format || options.text, label = this.graphic, point = this.points[0];\n        if (!label) {\n            this.redraw(animation);\n            return;\n        }\n        label.attr({\n            text: text ?\n                format(String(text), point, this.annotation.chart) :\n                options.formatter.call(point, this)\n        });\n        const anchor = this.anchor(point);\n        const attrs = this.position(anchor);\n        if (attrs) {\n            label.alignAttr = attrs;\n            attrs.anchorX = anchor.absolutePosition.x;\n            attrs.anchorY = anchor.absolutePosition.y;\n            label[animation ? 'animate' : 'attr'](attrs);\n        }\n        else {\n            label.attr({\n                x: 0,\n                y: -9999 // #10055\n            });\n        }\n        label.placed = !!attrs;\n        super.redraw(animation);\n    }\n    /**\n     * All basic shapes don't support alignTo() method except label.\n     * For a controllable label, we need to subtract translation from\n     * options.\n     */\n    anchor(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _point) {\n        const anchor = super.anchor.apply(this, arguments), x = this.options.x || 0, y = this.options.y || 0;\n        anchor.absolutePosition.x -= x;\n        anchor.absolutePosition.y -= y;\n        anchor.relativePosition.x -= x;\n        anchor.relativePosition.y -= y;\n        return anchor;\n    }\n    /**\n     * Returns the label position relative to its anchor.\n     */\n    position(anchor) {\n        const item = this.graphic, chart = this.annotation.chart, tooltip = chart.tooltip, point = this.points[0], itemOptions = this.options, anchorAbsolutePosition = anchor.absolutePosition, anchorRelativePosition = anchor.relativePosition;\n        let itemPosition, alignTo, itemPosRelativeX, itemPosRelativeY, showItem = point.series.visible &&\n            Annotations_MockPoint.prototype.isInsidePlot.call(point);\n        if (item && showItem) {\n            const { width = 0, height = 0 } = item;\n            if (itemOptions.distance && tooltip) {\n                itemPosition = tooltip.getPosition.call({\n                    chart,\n                    distance: ControllableLabel_pick(itemOptions.distance, 16),\n                    getPlayingField: tooltip.getPlayingField,\n                    pointer: tooltip.pointer\n                }, width, height, {\n                    plotX: anchorRelativePosition.x,\n                    plotY: anchorRelativePosition.y,\n                    negative: point.negative,\n                    ttBelow: point.ttBelow,\n                    h: (anchorRelativePosition.height ||\n                        anchorRelativePosition.width)\n                });\n            }\n            else if (itemOptions.positioner) {\n                itemPosition = itemOptions.positioner.call(this);\n            }\n            else {\n                alignTo = {\n                    x: anchorAbsolutePosition.x,\n                    y: anchorAbsolutePosition.y,\n                    width: 0,\n                    height: 0\n                };\n                itemPosition = ControllableLabel.alignedPosition(ControllableLabel_extend(itemOptions, {\n                    width,\n                    height\n                }), alignTo);\n                if (this.options.overflow === 'justify') {\n                    itemPosition = ControllableLabel.alignedPosition(ControllableLabel.justifiedOptions(chart, item, itemOptions, itemPosition), alignTo);\n                }\n            }\n            if (itemOptions.crop) {\n                itemPosRelativeX = itemPosition.x - chart.plotLeft;\n                itemPosRelativeY = itemPosition.y - chart.plotTop;\n                showItem =\n                    chart.isInsidePlot(itemPosRelativeX, itemPosRelativeY) &&\n                        chart.isInsidePlot(itemPosRelativeX + width, itemPosRelativeY + height);\n            }\n        }\n        return showItem ? itemPosition : null;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @type {Highcharts.Dictionary<string>}\n */\nControllableLabel.attrsMap = {\n    backgroundColor: 'fill',\n    borderColor: 'stroke',\n    borderWidth: 'stroke-width',\n    zIndex: 'zIndex',\n    borderRadius: 'r',\n    padding: 'padding'\n};\n/**\n * Shapes which do not have background - the object is used for proper\n * setting of the contrast color.\n *\n * @type {Array<string>}\n */\nControllableLabel.shapesWithoutBackground = ['connector'];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableLabel = (ControllableLabel);\n\n;// ./code/es-modules/Extensions/Annotations/Controllables/ControllableImage.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable image class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableImage\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A controllable's options.\n *\n * @param {number} index\n * Index of the image.\n */\nclass ControllableImage extends Controllables_Controllable {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(annotation, options, index) {\n        super(annotation, options, index, 'shape');\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.type = 'image';\n        this.translate = super.translateShape;\n    }\n    render(parent) {\n        const attrs = this.attrsFromOptions(this.options), options = this.options;\n        this.graphic = this.annotation.chart.renderer\n            .image(options.src, 0, -9e9, options.width, options.height)\n            .attr(attrs)\n            .add(parent);\n        this.graphic.width = options.width;\n        this.graphic.height = options.height;\n        super.render();\n    }\n    redraw(animation) {\n        if (this.graphic) {\n            const anchor = this.anchor(this.points[0]), position = Controllables_ControllableLabel.prototype.position.call(this, anchor);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        super.redraw(animation);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A map object which allows to map options attributes to element attributes\n *\n * @name Highcharts.AnnotationControllableImage.attrsMap\n * @type {Highcharts.Dictionary<string>}\n */\nControllableImage.attrsMap = {\n    width: 'width',\n    height: 'height',\n    zIndex: 'zIndex'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Controllables_ControllableImage = (ControllableImage);\n\n;// external [\"../highcharts.js\",\"default\",\"AST\"]\nconst external_highcharts_src_js_default_AST_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].AST;\nvar external_highcharts_src_js_default_AST_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_AST_namespaceObject);\n;// ./code/es-modules/Shared/BaseForm.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\nconst { addEvent: BaseForm_addEvent, createElement } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass BaseForm {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(parentDiv, iconsURL) {\n        this.iconsURL = iconsURL;\n        this.container = this.createPopupContainer(parentDiv);\n        this.closeButton = this.addCloseButton();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create popup div container.\n     *\n     * @param {HTMLElement} parentDiv\n     * Parent div to attach popup.\n     *\n     * @param  {string} className\n     * Class name of the popup.\n     *\n     * @return {HTMLElement}\n     * Popup div.\n     */\n    createPopupContainer(parentDiv, className = 'highcharts-popup highcharts-no-tooltip') {\n        return createElement('div', { className }, void 0, parentDiv);\n    }\n    /**\n     * Create HTML element and attach click event to close popup.\n     *\n     * @param {string} className\n     * Class name of the close button.\n     *\n     * @return {HTMLElement}\n     * Close button.\n     */\n    addCloseButton(className = 'highcharts-popup-close') {\n        const popup = this, iconsURL = this.iconsURL;\n        // Create close popup button.\n        const closeButton = createElement('button', { className }, void 0, this.container);\n        closeButton.style['background-image'] = 'url(' +\n            (iconsURL.match(/png|svg|jpeg|jpg|gif/ig) ?\n                iconsURL : iconsURL + 'close.svg') + ')';\n        ['click', 'touchstart'].forEach((eventName) => {\n            BaseForm_addEvent(closeButton, eventName, popup.closeButtonEvents.bind(popup));\n        });\n        // Close popup when press ESC\n        BaseForm_addEvent(document, 'keydown', function (event) {\n            if (event.code === 'Escape') {\n                popup.closeButtonEvents();\n            }\n        });\n        return closeButton;\n    }\n    /**\n     * Close button events.\n     * @return {void}\n     */\n    closeButtonEvents() {\n        this.closePopup();\n    }\n    /**\n     * Reset content of the current popup and show.\n     *\n     * @param {string} toolbarClass\n     * Class name of the toolbar which styles should be reset.\n     */\n    showPopup(toolbarClass = 'highcharts-annotation-toolbar') {\n        const popupDiv = this.container, popupCloseButton = this.closeButton;\n        this.type = void 0;\n        // Reset content.\n        popupDiv.innerHTML = (external_highcharts_src_js_default_AST_default()).emptyHTML;\n        // Reset toolbar styles if exists.\n        if (popupDiv.className.indexOf(toolbarClass) >= 0) {\n            popupDiv.classList.remove(toolbarClass);\n            // Reset toolbar inline styles\n            popupDiv.removeAttribute('style');\n        }\n        // Add close button.\n        popupDiv.appendChild(popupCloseButton);\n        popupDiv.style.display = 'block';\n        popupDiv.style.height = '';\n    }\n    /**\n     * Hide popup.\n     */\n    closePopup() {\n        this.container.style.display = 'none';\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Shared_BaseForm = (BaseForm);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupAnnotations.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc: PopupAnnotations_doc, isFirefox } = (external_highcharts_src_js_default_default());\n\nconst { createElement: PopupAnnotations_createElement, isArray, isObject, objectEach: PopupAnnotations_objectEach, pick: PopupAnnotations_pick, stableSort } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create annotation simple form.\n * It contains fields with param names.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Object} options\n * Options\n * @param {Function} callback\n * On click callback\n * @param {boolean} [isInit]\n * If it is a form declared for init annotation\n */\nfunction addForm(chart, options, callback, isInit) {\n    if (!chart) {\n        return;\n    }\n    const popupDiv = this.container, lang = this.lang;\n    // Create title of annotations\n    let lhsCol = PopupAnnotations_createElement('h2', {\n        className: 'highcharts-popup-main-title'\n    }, void 0, popupDiv);\n    lhsCol.appendChild(PopupAnnotations_doc.createTextNode(lang[options.langKey] || options.langKey || ''));\n    // Left column\n    lhsCol = PopupAnnotations_createElement('div', {\n        className: ('highcharts-popup-lhs-col highcharts-popup-lhs-full')\n    }, void 0, popupDiv);\n    const bottomRow = PopupAnnotations_createElement('div', {\n        className: 'highcharts-popup-bottom-row'\n    }, void 0, popupDiv);\n    addFormFields.call(this, lhsCol, chart, '', options, [], true);\n    this.addButton(bottomRow, isInit ?\n        (lang.addButton || 'Add') :\n        (lang.saveButton || 'Save'), isInit ? 'add' : 'save', popupDiv, callback);\n}\n/**\n * Create annotation simple form. It contains two buttons\n * (edit / remove) and text label.\n * @private\n * @param {Highcharts.Chart} - chart\n * @param {Highcharts.AnnotationsOptions} - options\n * @param {Function} - on click callback\n */\nfunction addToolbar(chart, options, callback) {\n    const lang = this.lang, popupDiv = this.container, showForm = this.showForm, toolbarClass = 'highcharts-annotation-toolbar';\n    // Set small size\n    if (popupDiv.className.indexOf(toolbarClass) === -1) {\n        popupDiv.className += ' ' + toolbarClass + ' highcharts-no-mousewheel';\n    }\n    // Set position\n    if (chart) {\n        popupDiv.style.top = chart.plotTop + 10 + 'px';\n    }\n    // Create label\n    const label = PopupAnnotations_createElement('p', {\n        className: 'highcharts-annotation-label'\n    }, void 0, popupDiv);\n    label.setAttribute('aria-label', 'Annotation type');\n    label.appendChild(PopupAnnotations_doc.createTextNode(PopupAnnotations_pick(\n    // Advanced annotations:\n    lang[options.langKey] || options.langKey, \n    // Basic shapes:\n    options.shapes && options.shapes[0].type, '')));\n    // Add buttons\n    let button = this.addButton(popupDiv, lang.editButton || 'Edit', 'edit', popupDiv, () => {\n        showForm.call(this, 'annotation-edit', chart, options, callback);\n    });\n    button.className += ' highcharts-annotation-edit-button';\n    button.style['background-image'] = 'url(' +\n        this.iconsURL + 'edit.svg)';\n    button = this.addButton(popupDiv, lang.removeButton || 'Remove', 'remove', popupDiv, callback);\n    button.className += ' highcharts-annotation-remove-button';\n    button.style['background-image'] = 'url(' +\n        this.iconsURL + 'destroy.svg)';\n}\n/**\n * Create annotation's form fields.\n * @private\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Div where inputs are placed\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.AnnotationsOptions} options\n * Options\n * @param {Array<unknown>} storage\n * Array where all items are stored\n * @param {boolean} [isRoot]\n * Recursive flag for root\n */\nfunction addFormFields(parentDiv, chart, parentNode, options, storage, isRoot) {\n    if (!chart) {\n        return;\n    }\n    const addInput = this.addInput, lang = this.lang;\n    let parentFullName, titleName;\n    PopupAnnotations_objectEach(options, (value, option) => {\n        // Create name like params.styles.fontSize\n        parentFullName = parentNode !== '' ? parentNode + '.' + option : option;\n        if (isObject(value)) {\n            if (\n            // Value is object of options\n            !isArray(value) ||\n                // Array of objects with params. i.e labels in Fibonacci\n                (isArray(value) && isObject(value[0]))) {\n                titleName = lang[option] || option;\n                if (!titleName.match(/\\d/g)) {\n                    storage.push([\n                        true,\n                        titleName,\n                        parentDiv\n                    ]);\n                }\n                addFormFields.call(this, parentDiv, chart, parentFullName, value, storage, false);\n            }\n            else {\n                storage.push([\n                    this,\n                    parentFullName,\n                    'annotation',\n                    parentDiv,\n                    value\n                ]);\n            }\n        }\n    });\n    if (isRoot) {\n        stableSort(storage, (a) => (a[1].match(/format/g) ? -1 : 1));\n        if (isFirefox) {\n            storage.reverse(); // (#14691)\n        }\n        storage.forEach((genInput) => {\n            if (genInput[0] === true) {\n                PopupAnnotations_createElement('span', {\n                    className: 'highcharts-annotation-title'\n                }, void 0, genInput[2]).appendChild(PopupAnnotations_doc.createTextNode(genInput[1]));\n            }\n            else {\n                genInput[4] = {\n                    value: genInput[4][0],\n                    type: genInput[4][1]\n                };\n                addInput.apply(genInput[0], genInput.splice(1));\n            }\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupAnnotations = {\n    addForm,\n    addToolbar\n};\n/* harmony default export */ const Popup_PopupAnnotations = (PopupAnnotations);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupIndicators.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { doc: PopupIndicators_doc } = (external_highcharts_src_js_default_default());\n\nconst { seriesTypes } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { addEvent: PopupIndicators_addEvent, createElement: PopupIndicators_createElement, defined: PopupIndicators_defined, isArray: PopupIndicators_isArray, isObject: PopupIndicators_isObject, objectEach: PopupIndicators_objectEach, stableSort: PopupIndicators_stableSort } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Enums\n *\n * */\n/**\n * Enum for properties which should have dropdown list.\n * @private\n */\nvar DropdownProperties;\n(function (DropdownProperties) {\n    DropdownProperties[DropdownProperties[\"params.algorithm\"] = 0] = \"params.algorithm\";\n    DropdownProperties[DropdownProperties[\"params.average\"] = 1] = \"params.average\";\n})(DropdownProperties || (DropdownProperties = {}));\n/**\n * List of available algorithms for the specific indicator.\n * @private\n */\nconst dropdownParameters = {\n    'algorithm-pivotpoints': ['standard', 'fibonacci', 'camarilla'],\n    'average-disparityindex': ['sma', 'ema', 'dema', 'tema', 'wma']\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create two columns (divs) in HTML.\n * @private\n * @param {Highcharts.HTMLDOMElement} container\n * Container of columns\n * @return {Highcharts.Dictionary<Highcharts.HTMLDOMElement>}\n * Reference to two HTML columns (lhsCol, rhsCol)\n */\nfunction addColsContainer(container) {\n    // Left column\n    const lhsCol = PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-lhs-col'\n    }, void 0, container);\n    // Right column\n    const rhsCol = PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col'\n    }, void 0, container);\n    // Wrapper content\n    PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col-wrapper'\n    }, void 0, rhsCol);\n    return {\n        lhsCol: lhsCol,\n        rhsCol: rhsCol\n    };\n}\n/**\n * Create indicator's form. It contains two tabs (ADD and EDIT) with\n * content.\n * @private\n */\nfunction PopupIndicators_addForm(chart, _options, callback) {\n    const lang = this.lang;\n    let buttonParentDiv;\n    if (!chart) {\n        return;\n    }\n    // Add tabs\n    this.tabs.init.call(this, chart);\n    // Get all tabs content divs\n    const tabsContainers = this.container\n        .querySelectorAll('.highcharts-tab-item-content');\n    // ADD tab\n    addColsContainer(tabsContainers[0]);\n    addSearchBox.call(this, chart, tabsContainers[0]);\n    addIndicatorList.call(this, chart, tabsContainers[0], 'add');\n    buttonParentDiv = tabsContainers[0]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.addButton || 'add', 'add', buttonParentDiv, callback);\n    // EDIT tab\n    addColsContainer(tabsContainers[1]);\n    addIndicatorList.call(this, chart, tabsContainers[1], 'edit');\n    buttonParentDiv = tabsContainers[1]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.saveButton || 'save', 'edit', buttonParentDiv, callback);\n    this.addButton(buttonParentDiv, lang.removeButton || 'remove', 'remove', buttonParentDiv, callback);\n}\n/**\n * Create typical inputs for chosen indicator. Fields are extracted from\n * defaultOptions (ADD mode) or current indicator (ADD mode). Two extra\n * fields are added:\n * - hidden input - contains indicator type (required for callback)\n * - select - list of series which can be linked with indicator\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Highcharts.Series} series\n * Indicator\n * @param {string} seriesType\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} rhsColWrapper\n * Element where created HTML list is added\n */\nfunction PopupIndicators_addFormFields(chart, series, seriesType, rhsColWrapper) {\n    const fields = series.params || series.options.params;\n    // Reset current content\n    rhsColWrapper.innerHTML = (external_highcharts_src_js_default_AST_default()).emptyHTML;\n    // Create title (indicator name in the right column)\n    PopupIndicators_createElement('h3', {\n        className: 'highcharts-indicator-title'\n    }, void 0, rhsColWrapper).appendChild(PopupIndicators_doc.createTextNode(getNameType(series, seriesType).indicatorFullName));\n    // Input type\n    PopupIndicators_createElement('input', {\n        type: 'hidden',\n        name: 'highcharts-type-' + seriesType,\n        value: seriesType\n    }, void 0, rhsColWrapper);\n    // List all series with id\n    listAllSeries.call(this, seriesType, 'series', chart, rhsColWrapper, series, series.linkedParent && series.linkedParent.options.id);\n    if (fields.volumeSeriesID) {\n        listAllSeries.call(this, seriesType, 'volume', chart, rhsColWrapper, series, series.linkedParent && fields.volumeSeriesID);\n    }\n    // Add param fields\n    addParamInputs.call(this, chart, 'params', fields, seriesType, rhsColWrapper);\n}\n/**\n * Create HTML list of all indicators (ADD mode) or added indicators\n * (EDIT mode).\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string} listType\n *        Type of list depending on the selected bookmark.\n *        Might be 'add' or 'edit'.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n */\nfunction addIndicatorList(chart, parentDiv, listType, filter) {\n    /**\n     *\n     */\n    function selectIndicator(series, indicatorType) {\n        const button = rhsColWrapper.parentNode\n            .children[1];\n        PopupIndicators_addFormFields.call(popup, chart, series, indicatorType, rhsColWrapper);\n        if (button) {\n            button.style.display = 'block';\n        }\n        // Add hidden input with series.id\n        if (isEdit && series.options) {\n            PopupIndicators_createElement('input', {\n                type: 'hidden',\n                name: 'highcharts-id-' + indicatorType,\n                value: series.options.id\n            }, void 0, rhsColWrapper).setAttribute('highcharts-data-series-id', series.options.id);\n        }\n    }\n    const popup = this, lang = popup.lang, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], rhsCol = parentDiv.querySelectorAll('.highcharts-popup-rhs-col')[0], isEdit = listType === 'edit', series = (isEdit ?\n        chart.series : // EDIT mode\n        chart.options.plotOptions || {} // ADD mode\n    );\n    if (!chart && series) {\n        return;\n    }\n    let item, filteredSeriesArray = [];\n    // Filter and sort the series.\n    if (!isEdit && !PopupIndicators_isArray(series)) {\n        // Apply filters only for the 'add' indicator list.\n        filteredSeriesArray = filterSeries.call(this, series, filter);\n    }\n    else if (PopupIndicators_isArray(series)) {\n        filteredSeriesArray = filterSeriesArray.call(this, series);\n    }\n    // Sort indicators alphabetically.\n    PopupIndicators_stableSort(filteredSeriesArray, (a, b) => {\n        const seriesAName = a.indicatorFullName.toLowerCase(), seriesBName = b.indicatorFullName.toLowerCase();\n        return (seriesAName < seriesBName) ?\n            -1 : (seriesAName > seriesBName) ? 1 : 0;\n    });\n    // If the list exists remove it from the DOM\n    // in order to create a new one with different filters.\n    if (lhsCol.children[1]) {\n        lhsCol.children[1].remove();\n    }\n    // Create wrapper for list.\n    const indicatorList = PopupIndicators_createElement('ul', {\n        className: 'highcharts-indicator-list'\n    }, void 0, lhsCol);\n    const rhsColWrapper = rhsCol.querySelectorAll('.highcharts-popup-rhs-col-wrapper')[0];\n    filteredSeriesArray.forEach((seriesSet) => {\n        const { indicatorFullName, indicatorType, series } = seriesSet;\n        item = PopupIndicators_createElement('li', {\n            className: 'highcharts-indicator-list'\n        }, void 0, indicatorList);\n        const btn = PopupIndicators_createElement('button', {\n            className: 'highcharts-indicator-list-item',\n            textContent: indicatorFullName\n        }, void 0, item);\n        ['click', 'touchstart'].forEach((eventName) => {\n            PopupIndicators_addEvent(btn, eventName, function () {\n                selectIndicator(series, indicatorType);\n            });\n        });\n    });\n    // Select first item from the list\n    if (filteredSeriesArray.length > 0) {\n        const { series, indicatorType } = filteredSeriesArray[0];\n        selectIndicator(series, indicatorType);\n    }\n    else if (!isEdit) {\n        external_highcharts_src_js_default_AST_default().setElementHTML(rhsColWrapper.parentNode.children[0], lang.noFilterMatch || '');\n        rhsColWrapper.parentNode.children[1]\n            .style.display = 'none';\n    }\n}\n/**\n * Recurrent function which lists all fields, from params object and\n * create them as inputs. Each input has unique `data-name` attribute,\n * which keeps chain of fields i.e params.styles.fontSize.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.PopupFieldsDictionary<string>} fields\n * Params which are based for input create\n * @param {string} type\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Element where created HTML list is added\n */\nfunction addParamInputs(chart, parentNode, fields, type, parentDiv) {\n    if (!chart) {\n        return;\n    }\n    const addInput = this.addInput;\n    PopupIndicators_objectEach(fields, (value, fieldName) => {\n        // Create name like params.styles.fontSize\n        const parentFullName = parentNode + '.' + fieldName;\n        if (PopupIndicators_defined(value) && // Skip if field is unnecessary, #15362\n            parentFullName) {\n            if (PopupIndicators_isObject(value)) {\n                // (15733) 'Periods' has an arrayed value. Label must be\n                // created here.\n                addInput.call(this, parentFullName, type, parentDiv, {});\n                addParamInputs.call(this, chart, parentFullName, value, type, parentDiv);\n            }\n            // If the option is listed in dropdown enum,\n            // add the selection box for it.\n            if (parentFullName in DropdownProperties) {\n                // Add selection boxes.\n                const selectBox = addSelection.call(this, type, parentFullName, parentDiv);\n                // Add possible dropdown options.\n                addSelectionOptions.call(this, chart, parentNode, selectBox, type, fieldName, value);\n            }\n            else if (\n            // Skip volume field which is created by addFormFields.\n            parentFullName !== 'params.volumeSeriesID' &&\n                !PopupIndicators_isArray(value) // Skip params declared in array.\n            ) {\n                addInput.call(this, parentFullName, type, parentDiv, {\n                    value: value,\n                    type: 'number'\n                } // All inputs are text type\n                );\n            }\n        }\n    });\n}\n/**\n * Add searchbox HTML element and its' label.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} parentDiv\n *        HTML parent element.\n */\nfunction addSearchBox(chart, parentDiv) {\n    const popup = this, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], options = 'searchIndicators', inputAttributes = {\n        value: '',\n        type: 'text',\n        htmlFor: 'search-indicators',\n        labelClassName: 'highcharts-input-search-indicators-label'\n    }, clearFilterText = this.lang.clearFilter, inputWrapper = PopupIndicators_createElement('div', {\n        className: 'highcharts-input-wrapper'\n    }, void 0, lhsCol);\n    const handleInputChange = function (inputText) {\n        // Apply some filters.\n        addIndicatorList.call(popup, chart, popup.container, 'add', inputText);\n    };\n    // Add input field with the label and button.\n    const input = this.addInput(options, 'input', inputWrapper, inputAttributes), button = PopupIndicators_createElement('a', {\n        textContent: clearFilterText\n    }, void 0, inputWrapper);\n    input.classList.add('highcharts-input-search-indicators');\n    button.classList.add('clear-filter-button');\n    // Add input change events.\n    PopupIndicators_addEvent(input, 'input', function () {\n        handleInputChange(this.value);\n        // Show clear filter button.\n        if (this.value.length) {\n            button.style.display = 'inline-block';\n        }\n        else {\n            button.style.display = 'none';\n        }\n    });\n    // Add clear filter click event.\n    ['click', 'touchstart'].forEach((eventName) => {\n        PopupIndicators_addEvent(button, eventName, function () {\n            // Clear the input.\n            input.value = '';\n            handleInputChange('');\n            // Hide clear filter button- no longer necessary.\n            button.style.display = 'none';\n        });\n    });\n}\n/**\n * Add selection HTML element and its' label.\n *\n * @private\n *\n * @param {string} indicatorType\n * Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n * Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n * HTML parent element.\n */\nfunction addSelection(indicatorType, optionName, parentDiv) {\n    const optionParamList = optionName.split('.'), labelText = optionParamList[optionParamList.length - 1], selectName = 'highcharts-' + optionName + '-type-' + indicatorType, lang = this.lang;\n    // Add a label for the selection box.\n    PopupIndicators_createElement('label', {\n        htmlFor: selectName\n    }, null, parentDiv).appendChild(PopupIndicators_doc.createTextNode(lang[labelText] || optionName));\n    // Create a selection box.\n    const selectBox = PopupIndicators_createElement('select', {\n        name: selectName,\n        className: 'highcharts-popup-field',\n        id: 'highcharts-select-' + optionName\n    }, null, parentDiv);\n    selectBox.setAttribute('id', 'highcharts-select-' + optionName);\n    return selectBox;\n}\n/**\n * Get and add selection options.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLSelectElement} [selectBox]\n *        HTML select box element to which the options are being added.\n *\n * @param {string|undefined} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string|undefined} parameterName\n *        Name of the parameter which should be applied.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction addSelectionOptions(chart, optionName, selectBox, indicatorType, parameterName, selectedOption, currentSeries) {\n    // Get and apply selection options for the possible series.\n    if (optionName === 'series' || optionName === 'volume') {\n        // List all series which have id - mandatory for indicator.\n        chart.series.forEach((series) => {\n            const seriesOptions = series.options, seriesName = seriesOptions.name ||\n                seriesOptions.params ?\n                series.name :\n                seriesOptions.id || '';\n            if (seriesOptions.id !== 'highcharts-navigator-series' &&\n                seriesOptions.id !== (currentSeries &&\n                    currentSeries.options &&\n                    currentSeries.options.id)) {\n                if (!PopupIndicators_defined(selectedOption) &&\n                    optionName === 'volume' &&\n                    series.type === 'column') {\n                    selectedOption = seriesOptions.id;\n                }\n                PopupIndicators_createElement('option', {\n                    value: seriesOptions.id\n                }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(seriesName));\n            }\n        });\n    }\n    else if (indicatorType && parameterName) {\n        // Get and apply options for the possible parameters.\n        const dropdownKey = parameterName + '-' + indicatorType, parameterOption = dropdownParameters[dropdownKey];\n        parameterOption.forEach((element) => {\n            PopupIndicators_createElement('option', {\n                value: element\n            }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(element));\n        });\n    }\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/**\n * Filter object of series which are not indicators.\n * If the filter string exists, check against it.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series are available in the plotOptions.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeries(series, filter) {\n    const popup = this, lang = popup.chart && popup.chart.options.lang, indicatorAliases = lang &&\n        lang.navigation &&\n        lang.navigation.popup &&\n        lang.navigation.popup.indicatorAliases, filteredSeriesArray = [];\n    let filteredSeries;\n    PopupIndicators_objectEach(series, (series, value) => {\n        const seriesOptions = series && series.options;\n        // Allow only indicators.\n        if (series.params || seriesOptions &&\n            seriesOptions.params) {\n            const { indicatorFullName, indicatorType } = getNameType(series, value);\n            if (filter) {\n                // Replace invalid characters.\n                const validFilter = filter.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n                const regex = new RegExp(validFilter, 'i'), alias = indicatorAliases &&\n                    indicatorAliases[indicatorType] &&\n                    indicatorAliases[indicatorType].join(' ') || '';\n                if (indicatorFullName.match(regex) ||\n                    alias.match(regex)) {\n                    filteredSeries = {\n                        indicatorFullName,\n                        indicatorType,\n                        series: series\n                    };\n                    filteredSeriesArray.push(filteredSeries);\n                }\n            }\n            else {\n                filteredSeries = {\n                    indicatorFullName,\n                    indicatorType,\n                    series: series\n                };\n                filteredSeriesArray.push(filteredSeries);\n            }\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Filter an array of series and map its names and types.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series that are available in the plotOptions.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeriesArray(series) {\n    const filteredSeriesArray = [];\n    // Allow only indicators.\n    series.forEach((series) => {\n        if (series.is('sma')) {\n            filteredSeriesArray.push({\n                indicatorFullName: series.name,\n                indicatorType: series.type,\n                series: series\n            });\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Get amount of indicators added to chart.\n * @private\n * @return {number} - Amount of indicators\n */\nfunction getAmount() {\n    let counter = 0;\n    this.series.forEach((serie) => {\n        if (serie.params ||\n            serie.options.params) {\n            counter++;\n        }\n    });\n    return counter;\n}\n/**\n * Extract full name and type of requested indicator.\n *\n * @private\n *\n * @param {Highcharts.Series} series\n * Series which name is needed(EDITmode - defaultOptions.series,\n * ADDmode - indicator series).\n *\n * @param {string} [indicatorType]\n * Type of the indicator i.e. sma, ema...\n *\n * @return {Highcharts.Dictionary<string>}\n * Full name and series type.\n */\nfunction getNameType(series, indicatorType) {\n    const options = series.options;\n    // Add mode\n    let seriesName = (seriesTypes[indicatorType] &&\n        seriesTypes[indicatorType].prototype.nameBase) ||\n        indicatorType.toUpperCase(), seriesType = indicatorType;\n    // Edit\n    if (options && options.type) {\n        seriesType = series.options.type;\n        seriesName = series.name;\n    }\n    return {\n        indicatorFullName: seriesName,\n        indicatorType: seriesType\n    };\n}\n/**\n * Create the selection box for the series,\n * add options and apply the default one.\n *\n * @private\n *\n * @param {string} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction listAllSeries(indicatorType, optionName, chart, parentDiv, currentSeries, selectedOption) {\n    const popup = this;\n    // Won't work without the chart.\n    if (!chart) {\n        return;\n    }\n    // Add selection boxes.\n    const selectBox = addSelection.call(popup, indicatorType, optionName, parentDiv);\n    // Add possible dropdown options.\n    addSelectionOptions.call(popup, chart, optionName, selectBox, void 0, void 0, void 0, currentSeries);\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupIndicators = {\n    addForm: PopupIndicators_addForm,\n    getAmount\n};\n/* harmony default export */ const Popup_PopupIndicators = (PopupIndicators);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupTabs.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc: PopupTabs_doc } = (external_highcharts_src_js_default_default());\n\nconst { addEvent: PopupTabs_addEvent, createElement: PopupTabs_createElement } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create tab content\n * @private\n * @return {HTMLDOMElement} - created HTML tab-content element\n */\nfunction addContentItem() {\n    const popupDiv = this.container;\n    return PopupTabs_createElement('div', {\n        // #12100\n        className: 'highcharts-tab-item-content highcharts-no-mousewheel'\n    }, void 0, popupDiv);\n}\n/**\n * Create tab menu item\n * @private\n * @param {string} tabName\n * `add` or `edit`\n * @param {number} [disableTab]\n * Disable tab when 0\n * @return {Highcharts.HTMLDOMElement}\n * Created HTML tab-menu element\n */\nfunction addMenuItem(tabName, disableTab) {\n    const popupDiv = this.container, lang = this.lang;\n    let className = 'highcharts-tab-item';\n    if (disableTab === 0) {\n        className += ' highcharts-tab-disabled';\n    }\n    // Tab 1\n    const menuItem = PopupTabs_createElement('button', {\n        className\n    }, void 0, popupDiv);\n    menuItem.appendChild(PopupTabs_doc.createTextNode(lang[tabName + 'Button'] || tabName));\n    menuItem.setAttribute('highcharts-data-tab-type', tabName);\n    return menuItem;\n}\n/**\n * Set all tabs as invisible.\n * @private\n */\nfunction deselectAll() {\n    const popupDiv = this.container, tabs = popupDiv\n        .querySelectorAll('.highcharts-tab-item'), tabsContent = popupDiv\n        .querySelectorAll('.highcharts-tab-item-content');\n    for (let i = 0; i < tabs.length; i++) {\n        tabs[i].classList.remove('highcharts-tab-item-active');\n        tabsContent[i].classList.remove('highcharts-tab-item-show');\n    }\n}\n/**\n * Init tabs. Create tab menu items, tabs containers\n * @private\n * @param {Highcharts.Chart} chart\n * Reference to current chart\n */\nfunction init(chart) {\n    if (!chart) {\n        return;\n    }\n    const indicatorsCount = this.indicators.getAmount.call(chart);\n    // Create menu items\n    const firstTab = addMenuItem.call(this, 'add'); // Run by default\n    addMenuItem.call(this, 'edit', indicatorsCount);\n    // Create tabs containers\n    addContentItem.call(this);\n    addContentItem.call(this);\n    switchTabs.call(this, indicatorsCount);\n    // Activate first tab\n    selectTab.call(this, firstTab, 0);\n}\n/**\n * Set tab as visible\n * @private\n * @param {globals.Element} - current tab\n * @param {number} - Index of tab in menu\n */\nfunction selectTab(tab, index) {\n    const allTabs = this.container\n        .querySelectorAll('.highcharts-tab-item-content');\n    tab.className += ' highcharts-tab-item-active';\n    allTabs[index].className += ' highcharts-tab-item-show';\n}\n/**\n * Add click event to each tab\n * @private\n * @param {number} disableTab\n * Disable tab when 0\n */\nfunction switchTabs(disableTab) {\n    const popup = this, popupDiv = this.container, tabs = popupDiv.querySelectorAll('.highcharts-tab-item');\n    tabs.forEach((tab, i) => {\n        if (disableTab === 0 &&\n            tab.getAttribute('highcharts-data-tab-type') === 'edit') {\n            return;\n        }\n        ['click', 'touchstart'].forEach((eventName) => {\n            PopupTabs_addEvent(tab, eventName, function () {\n                // Reset class on other elements\n                deselectAll.call(popup);\n                selectTab.call(popup, this, i);\n            });\n        });\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupTabs = {\n    init\n};\n/* harmony default export */ const Popup_PopupTabs = (PopupTabs);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/Popup.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { doc: Popup_doc } = (external_highcharts_src_js_default_default());\n\nconst { getOptions } = (external_highcharts_src_js_default_default());\n\n\n\n\nconst { addEvent: Popup_addEvent, createElement: Popup_createElement, extend: Popup_extend, fireEvent: Popup_fireEvent, pick: Popup_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get values from all inputs and selections then create JSON.\n *\n * @private\n *\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * The container where inputs and selections are created.\n *\n * @param {string} type\n * Type of the popup bookmark (add|edit|remove).\n */\nfunction getFields(parentDiv, type) {\n    const inputList = Array.prototype.slice.call(parentDiv.querySelectorAll('input')), selectList = Array.prototype.slice.call(parentDiv.querySelectorAll('select')), optionSeries = '#highcharts-select-series > option:checked', optionVolume = '#highcharts-select-volume > option:checked', linkedTo = parentDiv.querySelectorAll(optionSeries)[0], volumeTo = parentDiv.querySelectorAll(optionVolume)[0];\n    const fieldsOutput = {\n        actionType: type,\n        linkedTo: linkedTo && linkedTo.getAttribute('value') || '',\n        fields: {}\n    };\n    inputList.forEach((input) => {\n        const param = input.getAttribute('highcharts-data-name'), seriesId = input.getAttribute('highcharts-data-series-id');\n        // Params\n        if (seriesId) {\n            fieldsOutput.seriesId = input.value;\n        }\n        else if (param) {\n            fieldsOutput.fields[param] = input.value;\n        }\n        else {\n            // Type like sma / ema\n            fieldsOutput.type = input.value;\n        }\n    });\n    selectList.forEach((select) => {\n        const id = select.id;\n        // Get inputs only for the parameters, not for series and volume.\n        if (id !== 'highcharts-select-series' &&\n            id !== 'highcharts-select-volume') {\n            const parameter = id.split('highcharts-select-')[1];\n            fieldsOutput.fields[parameter] = select.value;\n        }\n    });\n    if (volumeTo) {\n        fieldsOutput.fields['params.volumeSeriesID'] = volumeTo\n            .getAttribute('value') || '';\n    }\n    return fieldsOutput;\n}\n/* *\n *\n *  Class\n *\n * */\nclass Popup extends Shared_BaseForm {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(parentDiv, iconsURL, chart) {\n        super(parentDiv, iconsURL);\n        this.chart = chart;\n        this.lang = (getOptions().lang.navigation || {}).popup || {};\n        Popup_addEvent(this.container, 'mousedown', () => {\n            const activeAnnotation = chart &&\n                chart.navigationBindings &&\n                chart.navigationBindings.activeAnnotation;\n            if (activeAnnotation) {\n                activeAnnotation.cancelClick = true;\n                const unbind = Popup_addEvent(Popup_doc, 'click', () => {\n                    setTimeout(() => {\n                        activeAnnotation.cancelClick = false;\n                    }, 0);\n                    unbind();\n                });\n            }\n        });\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create input with label.\n     *\n     * @private\n     *\n     * @param {string} option\n     *        Chain of fields i.e params.styles.fontSize separated by the dot.\n     *\n     * @param {string} indicatorType\n     *        Type of the indicator i.e. sma, ema...\n     *\n     * @param {HTMLDOMElement} parentDiv\n     *        HTML parent element.\n     *\n     * @param {Highcharts.InputAttributes} inputAttributes\n     *        Attributes of the input.\n     *\n     * @return {HTMLInputElement}\n     *         Return created input element.\n     */\n    addInput(option, indicatorType, parentDiv, inputAttributes) {\n        const optionParamList = option.split('.'), optionName = optionParamList[optionParamList.length - 1], lang = this.lang, inputName = 'highcharts-' + indicatorType + '-' + Popup_pick(inputAttributes.htmlFor, optionName);\n        if (!optionName.match(/^\\d+$/)) {\n            // Add label\n            Popup_createElement('label', {\n                htmlFor: inputName,\n                className: inputAttributes.labelClassName\n            }, void 0, parentDiv).appendChild(Popup_doc.createTextNode(lang[optionName] || optionName));\n        }\n        // Add input\n        const input = Popup_createElement('input', {\n            name: inputName,\n            value: inputAttributes.value,\n            type: inputAttributes.type,\n            className: 'highcharts-popup-field'\n        }, void 0, parentDiv);\n        input.setAttribute('highcharts-data-name', option);\n        return input;\n    }\n    closeButtonEvents() {\n        if (this.chart) {\n            const navigationBindings = this.chart.navigationBindings;\n            Popup_fireEvent(navigationBindings, 'closePopup');\n            if (navigationBindings &&\n                navigationBindings.selectedButtonElement) {\n                Popup_fireEvent(navigationBindings, 'deselectButton', { button: navigationBindings.selectedButtonElement });\n            }\n        }\n        else {\n            super.closeButtonEvents();\n        }\n    }\n    /**\n     * Create button.\n     * @private\n     * @param {Highcharts.HTMLDOMElement} parentDiv\n     * Container where elements should be added\n     * @param {string} label\n     * Text placed as button label\n     * @param {string} type\n     * add | edit | remove\n     * @param {Function} callback\n     * On click callback\n     * @param {Highcharts.HTMLDOMElement} fieldsDiv\n     * Container where inputs are generated\n     * @return {Highcharts.HTMLDOMElement}\n     * HTML button\n     */\n    addButton(parentDiv, label, type, fieldsDiv, callback) {\n        const button = Popup_createElement('button', void 0, void 0, parentDiv);\n        button.appendChild(Popup_doc.createTextNode(label));\n        if (callback) {\n            ['click', 'touchstart'].forEach((eventName) => {\n                Popup_addEvent(button, eventName, () => {\n                    this.closePopup();\n                    return callback(getFields(fieldsDiv, type));\n                });\n            });\n        }\n        return button;\n    }\n    /**\n     * Create content and show popup.\n     * @private\n     * @param {string} - type of popup i.e indicators\n     * @param {Highcharts.Chart} - chart\n     * @param {Highcharts.AnnotationsOptions} - options\n     * @param {Function} - on click callback\n     */\n    showForm(type, chart, options, callback) {\n        if (!chart) {\n            return;\n        }\n        // Show blank popup\n        this.showPopup();\n        // Indicator form\n        if (type === 'indicators') {\n            this.indicators.addForm.call(this, chart, options, callback);\n        }\n        // Annotation small toolbar\n        if (type === 'annotation-toolbar') {\n            this.annotations.addToolbar.call(this, chart, options, callback);\n        }\n        // Annotation edit form\n        if (type === 'annotation-edit') {\n            this.annotations.addForm.call(this, chart, options, callback);\n        }\n        // Flags form - add / edit\n        if (type === 'flag') {\n            this.annotations.addForm.call(this, chart, options, callback, true);\n        }\n        this.type = type;\n        // Explicit height is needed to make inner elements scrollable\n        this.container.style.height = this.container.offsetHeight + 'px';\n    }\n}\nPopup_extend(Popup.prototype, {\n    annotations: Popup_PopupAnnotations,\n    indicators: Popup_PopupIndicators,\n    tabs: Popup_PopupTabs\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Popup_Popup = (Popup);\n\n;// ./code/es-modules/Extensions/Annotations/Popup/PopupComposition.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\n\nconst { addEvent: PopupComposition_addEvent, pushUnique, wrap: PopupComposition_wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(NagivationBindingsClass, PointerClass) {\n    if (pushUnique(composed, 'Popup')) {\n        PopupComposition_addEvent(NagivationBindingsClass, 'closePopup', onNavigationBindingsClosePopup);\n        PopupComposition_addEvent(NagivationBindingsClass, 'showPopup', onNavigationBindingsShowPopup);\n        PopupComposition_wrap(PointerClass.prototype, 'onContainerMouseDown', wrapPointerOnContainerMouserDown);\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsClosePopup() {\n    if (this.popup) {\n        this.popup.closePopup();\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsShowPopup(config) {\n    if (!this.popup) {\n        // Add popup to main container\n        this.popup = new Popup_Popup(this.chart.container, (this.chart.options.navigation.iconsURL ||\n            (this.chart.options.stockTools &&\n                this.chart.options.stockTools.gui.iconsURL) ||\n            'https://code.highcharts.com/12.2.0/gfx/stock-icons/'), this.chart);\n    }\n    this.popup.showForm(config.formType, this.chart, config.options, config.onSubmit);\n}\n/**\n * `onContainerMouseDown` blocks internal popup events, due to e.preventDefault.\n * Related issue #4606\n * @private\n */\nfunction wrapPointerOnContainerMouserDown(proceed, e) {\n    // Elements is not in popup\n    if (!this.inClass(e.target, 'highcharts-popup')) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PopupComposition = {\n    compose\n};\n/* harmony default export */ const Popup_PopupComposition = (PopupComposition);\n\n;// ./code/es-modules/Extensions/Annotations/Annotation.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getDeferredAnimation } = (external_highcharts_src_js_default_default());\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst { destroyObjectProperties, erase: Annotation_erase, fireEvent: Annotation_fireEvent, merge: Annotation_merge, pick: Annotation_pick, splat } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Hide or show annotation attached to points.\n * @private\n */\nfunction adjustVisibility(item) {\n    const label = item.graphic, hasVisiblePoints = item.points.some((point) => (point.series.visible !== false &&\n        point.visible !== false));\n    if (label) {\n        if (!hasVisiblePoints) {\n            label.hide();\n        }\n        else if (label.visibility === 'hidden') {\n            label.show();\n        }\n    }\n}\n/**\n * @private\n */\nfunction getLabelsAndShapesOptions(baseOptions, newOptions) {\n    const mergedOptions = {};\n    ['labels', 'shapes'].forEach((name) => {\n        const someBaseOptions = baseOptions[name], newOptionsValue = newOptions[name];\n        if (someBaseOptions) {\n            if (newOptionsValue) {\n                mergedOptions[name] = splat(newOptionsValue).map((basicOptions, i) => Annotation_merge(someBaseOptions[i], basicOptions));\n            }\n            else {\n                mergedOptions[name] = baseOptions[name];\n            }\n        }\n    });\n    return mergedOptions;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * An annotation class which serves as a container for items like labels or\n * shapes. Created items are positioned on the chart either by linking them to\n * existing points or created mock points\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.Annotation\n *\n * @param {Highcharts.Chart} chart\n *        A chart instance\n * @param {Highcharts.AnnotationsOptions} userOptions\n *        The annotation options\n */\nclass Annotation extends Annotations_EventEmitter {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    static compose(ChartClass, NavigationBindingsClass, PointerClass, SVGRendererClass) {\n        Annotations_AnnotationChart.compose(Annotation, ChartClass, PointerClass);\n        Controllables_ControllableLabel.compose(SVGRendererClass);\n        Controllables_ControllablePath.compose(ChartClass, SVGRendererClass);\n        NavigationBindingsClass.compose(Annotation, ChartClass);\n        Popup_PopupComposition.compose(NavigationBindingsClass, PointerClass);\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(chart, userOptions) {\n        super();\n        this.coll = 'annotations';\n        /**\n         * The chart that the annotation belongs to.\n         *\n         * @name Highcharts.Annotation#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The array of points which defines the annotation.\n         * @private\n         * @name Highcharts.Annotation#points\n         * @type {Array<Highcharts.Point>}\n         */\n        this.points = [];\n        /**\n         * The array of control points.\n         * @private\n         * @name Highcharts.Annotation#controlPoints\n         * @type {Array<Annotation.ControlPoint>}\n         */\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.index = -1;\n        /**\n         * The array of labels which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#labels\n         * @type {Array<Highcharts.AnnotationLabelType>}\n         */\n        this.labels = [];\n        /**\n         * The array of shapes which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#shapes\n         * @type {Array<Highcharts.AnnotationShapeType>}\n         */\n        this.shapes = [];\n        /**\n         * The options for the annotations.\n         *\n         * @name Highcharts.Annotation#options\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        this.options = Annotation_merge(this.defaultOptions, userOptions);\n        /**\n         * The user options for the annotations.\n         *\n         * @name Highcharts.Annotation#userOptions\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        this.userOptions = userOptions;\n        // Handle labels and shapes - those are arrays\n        // Merging does not work with arrays (stores reference)\n        const labelsAndShapes = getLabelsAndShapesOptions(this.options, userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        /**\n         * The callback that reports to the overlapping labels logic which\n         * labels it should account for.\n         * @private\n         * @name Highcharts.Annotation#labelCollector\n         * @type {Function}\n         */\n        /**\n         * The group svg element.\n         *\n         * @name Highcharts.Annotation#group\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's shapes.\n         *\n         * @name Highcharts.Annotation#shapesGroup\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's labels.\n         *\n         * @name Highcharts.Annotation#labelsGroup\n         * @type {Highcharts.SVGElement}\n         */\n        this.init(chart, this.options);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    addClipPaths() {\n        this.setClipAxes();\n        if (this.clipXAxis &&\n            this.clipYAxis &&\n            this.options.crop // #15399\n        ) {\n            this.clipRect = this.chart.renderer.clipRect(this.getClipBox());\n        }\n    }\n    /**\n     * @private\n     */\n    addLabels() {\n        const labelsOptions = (this.options.labels || []);\n        labelsOptions.forEach((labelOptions, i) => {\n            const label = this.initLabel(labelOptions, i);\n            Annotation_merge(true, labelsOptions[i], label.options);\n        });\n    }\n    /**\n     * @private\n     */\n    addShapes() {\n        const shapes = this.options.shapes || [];\n        shapes.forEach((shapeOptions, i) => {\n            const shape = this.initShape(shapeOptions, i);\n            Annotation_merge(true, shapes[i], shape.options);\n        });\n    }\n    /**\n     * Destroy the annotation. This function does not touch the chart\n     * that the annotation belongs to (all annotations are kept in\n     * the chart.annotations array) - it is recommended to use\n     * {@link Highcharts.Chart#removeAnnotation} instead.\n     * @private\n     */\n    destroy() {\n        const chart = this.chart, destroyItem = function (item) {\n            item.destroy();\n        };\n        this.labels.forEach(destroyItem);\n        this.shapes.forEach(destroyItem);\n        this.clipXAxis = null;\n        this.clipYAxis = null;\n        Annotation_erase(chart.labelCollectors, this.labelCollector);\n        super.destroy();\n        this.destroyControlTarget();\n        destroyObjectProperties(this, chart);\n    }\n    /**\n     * Destroy a single item.\n     * @private\n     */\n    destroyItem(item) {\n        // Erase from shapes or labels array\n        Annotation_erase(this[item.itemType + 's'], item);\n        item.destroy();\n    }\n    /**\n     * @private\n     */\n    getClipBox() {\n        if (this.clipXAxis && this.clipYAxis) {\n            return {\n                x: this.clipXAxis.left,\n                y: this.clipYAxis.top,\n                width: this.clipXAxis.width,\n                height: this.clipYAxis.height\n            };\n        }\n    }\n    /**\n     * Initialize the annotation properties.\n     * @private\n     */\n    initProperties(chart, userOptions) {\n        this.setOptions(userOptions);\n        const labelsAndShapes = getLabelsAndShapesOptions(this.options, userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        this.chart = chart;\n        this.points = [];\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.userOptions = userOptions;\n        this.labels = [];\n        this.shapes = [];\n    }\n    /**\n     * Initialize the annotation.\n     * @private\n     */\n    init(_annotationOrChart, _userOptions, index = this.index) {\n        const chart = this.chart, animOptions = this.options.animation;\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n        this.addShapes();\n        this.addLabels();\n        this.setLabelCollector();\n        this.animationConfig = getDeferredAnimation(chart, animOptions);\n    }\n    /**\n     * Initialisation of a single label\n     * @private\n     */\n    initLabel(labelOptions, index) {\n        const options = Annotation_merge(this.options.labelOptions, {\n            controlPointOptions: this.options.controlPointOptions\n        }, labelOptions), label = new Controllables_ControllableLabel(this, options, index);\n        label.itemType = 'label';\n        this.labels.push(label);\n        return label;\n    }\n    /**\n     * Initialisation of a single shape\n     * @private\n     * @param {Object} shapeOptions\n     * a config object for a single shape\n     * @param {number} index\n     * annotation may have many shapes, this is the shape's index saved in\n     * shapes.index.\n     */\n    initShape(shapeOptions, index) {\n        const options = Annotation_merge(this.options.shapeOptions, {\n            controlPointOptions: this.options.controlPointOptions\n        }, shapeOptions), shape = new (Annotation.shapesMap[options.type])(this, options, index);\n        shape.itemType = 'shape';\n        this.shapes.push(shape);\n        return shape;\n    }\n    /**\n     * @private\n     */\n    redraw(animation) {\n        this.linkPoints();\n        if (!this.graphic) {\n            this.render();\n        }\n        if (this.clipRect) {\n            this.clipRect.animate(this.getClipBox());\n        }\n        this.redrawItems(this.shapes, animation);\n        this.redrawItems(this.labels, animation);\n        this.redrawControlPoints(animation);\n    }\n    /**\n     * Redraw a single item.\n     * @private\n     */\n    redrawItem(item, animation) {\n        item.linkPoints();\n        if (!item.shouldBeDrawn()) {\n            this.destroyItem(item);\n        }\n        else {\n            if (!item.graphic) {\n                this.renderItem(item);\n            }\n            item.redraw(Annotation_pick(animation, true) && item.graphic.placed);\n            if (item.points.length) {\n                adjustVisibility(item);\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    redrawItems(items, animation) {\n        let i = items.length;\n        // Needs a backward loop. Labels/shapes array might be modified due to\n        // destruction of the item\n        while (i--) {\n            this.redrawItem(items[i], animation);\n        }\n    }\n    /**\n     * See {@link Highcharts.Chart#removeAnnotation}.\n     * @private\n     */\n    remove() {\n        // Let chart.update() remove annotations on demand\n        return this.chart.removeAnnotation(this);\n    }\n    /**\n     * @private\n     */\n    render() {\n        const renderer = this.chart.renderer;\n        this.graphic = renderer\n            .g('annotation')\n            .attr({\n            opacity: 0,\n            zIndex: this.options.zIndex,\n            visibility: this.options.visible ?\n                'inherit' :\n                'hidden'\n        })\n            .add();\n        this.shapesGroup = renderer\n            .g('annotation-shapes')\n            .add(this.graphic);\n        if (this.options.crop) { // #15399\n            this.shapesGroup.clip(this.chart.plotBoxClip);\n        }\n        this.labelsGroup = renderer\n            .g('annotation-labels')\n            .attr({\n            // `hideOverlappingLabels` requires translation\n            translateX: 0,\n            translateY: 0\n        })\n            .add(this.graphic);\n        this.addClipPaths();\n        if (this.clipRect) {\n            this.graphic.clip(this.clipRect);\n        }\n        // Render shapes and labels before adding events (#13070).\n        this.renderItems(this.shapes);\n        this.renderItems(this.labels);\n        this.addEvents();\n        this.renderControlPoints();\n    }\n    /**\n     * @private\n     */\n    renderItem(item) {\n        item.render(item.itemType === 'label' ?\n            this.labelsGroup :\n            this.shapesGroup);\n    }\n    /**\n     * @private\n     */\n    renderItems(items) {\n        let i = items.length;\n        while (i--) {\n            this.renderItem(items[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    setClipAxes() {\n        const xAxes = this.chart.xAxis, yAxes = this.chart.yAxis, linkedAxes = (this.options.labels || [])\n            .concat(this.options.shapes || [])\n            .reduce((axes, labelOrShape) => {\n            const point = labelOrShape &&\n                (labelOrShape.point ||\n                    (labelOrShape.points && labelOrShape.points[0]));\n            return [\n                xAxes[point && point.xAxis] || axes[0],\n                yAxes[point && point.yAxis] || axes[1]\n            ];\n        }, []);\n        this.clipXAxis = linkedAxes[0];\n        this.clipYAxis = linkedAxes[1];\n    }\n    /**\n     * @private\n     */\n    setControlPointsVisibility(visible) {\n        const setItemControlPointsVisibility = function (item) {\n            item.setControlPointsVisibility(visible);\n        };\n        this.controlPoints.forEach((controlPoint) => {\n            controlPoint.setVisibility(visible);\n        });\n        this.shapes.forEach(setItemControlPointsVisibility);\n        this.labels.forEach(setItemControlPointsVisibility);\n    }\n    /**\n     * @private\n     */\n    setLabelCollector() {\n        const annotation = this;\n        annotation.labelCollector = function () {\n            return annotation.labels.reduce(function (labels, label) {\n                if (!label.options.allowOverlap) {\n                    labels.push(label.graphic);\n                }\n                return labels;\n            }, []);\n        };\n        annotation.chart.labelCollectors.push(annotation.labelCollector);\n    }\n    /**\n     * Set an annotation options.\n     * @private\n     * @param {Highcharts.AnnotationsOptions} userOptions\n     *        User options for an annotation\n     */\n    setOptions(userOptions) {\n        this.options = Annotation_merge(this.defaultOptions, userOptions);\n    }\n    /**\n     * Set the annotation's visibility.\n     * @private\n     * @param {boolean} [visible]\n     * Whether to show or hide an annotation. If the param is omitted, the\n     * annotation's visibility is toggled.\n     */\n    setVisibility(visible) {\n        const options = this.options, navigation = this.chart.navigationBindings, visibility = Annotation_pick(visible, !options.visible);\n        this.graphic.attr('visibility', visibility ? 'inherit' : 'hidden');\n        if (!visibility) {\n            const setItemControlPointsVisibility = function (item) {\n                item.setControlPointsVisibility(visibility);\n            };\n            this.shapes.forEach(setItemControlPointsVisibility);\n            this.labels.forEach(setItemControlPointsVisibility);\n            if (navigation.activeAnnotation === this &&\n                navigation.popup &&\n                navigation.popup.type === 'annotation-toolbar') {\n                Annotation_fireEvent(navigation, 'closePopup');\n            }\n        }\n        options.visible = visibility;\n    }\n    /**\n     * Updates an annotation.\n     *\n     * @function Highcharts.Annotation#update\n     *\n     * @param {Partial<Highcharts.AnnotationsOptions>} userOptions\n     *        New user options for the annotation.\n     *\n     */\n    update(userOptions, redraw) {\n        const chart = this.chart, labelsAndShapes = getLabelsAndShapesOptions(this.userOptions, userOptions), userOptionsIndex = chart.annotations.indexOf(this), options = Annotation_merge(true, this.userOptions, userOptions);\n        options.labels = labelsAndShapes.labels;\n        options.shapes = labelsAndShapes.shapes;\n        this.destroy();\n        this.initProperties(chart, options);\n        this.init(chart, options);\n        // Update options in chart options, used in exporting (#9767, #21507):\n        chart.options.annotations[userOptionsIndex] = this.options;\n        this.isUpdating = true;\n        if (Annotation_pick(redraw, true)) {\n            chart.drawAnnotations();\n        }\n        Annotation_fireEvent(this, 'afterUpdate');\n        this.isUpdating = false;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * @private\n */\nAnnotation.ControlPoint = Annotations_ControlPoint;\n/**\n * @private\n */\nAnnotation.MockPoint = Annotations_MockPoint;\n/**\n * An object uses for mapping between a shape type and a constructor.\n * To add a new shape type extend this object with type name as a key\n * and a constructor as its value.\n *\n * @private\n */\nAnnotation.shapesMap = {\n    'rect': Controllables_ControllableRect,\n    'circle': Controllables_ControllableCircle,\n    'ellipse': Controllables_ControllableEllipse,\n    'path': Controllables_ControllablePath,\n    'image': Controllables_ControllableImage\n};\n/**\n * @private\n */\nAnnotation.types = {};\nAnnotation.prototype.defaultOptions = Annotations_AnnotationDefaults;\n/**\n * List of events for `annotation.options.events` that should not be\n * added to `annotation.graphic` but to the `annotation`.\n *\n * @private\n * @type {Array<string>}\n */\nAnnotation.prototype.nonDOMEvents = ['add', 'afterUpdate', 'drag', 'remove'];\nAnnotations_ControlTarget.compose(Annotation);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Annotations_Annotation = (Annotation);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Possible directions for draggable annotations. An empty string (`''`)\n * makes the annotation undraggable.\n *\n * @typedef {''|'x'|'xy'|'y'} Highcharts.AnnotationDraggableValue\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableCircle|\n *          Highcharts.AnnotationControllableImage|\n *          Highcharts.AnnotationControllablePath|\n *          Highcharts.AnnotationControllableRect\n *     } Highcharts.AnnotationShapeType\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableLabel\n *     } Highcharts.AnnotationLabelType\n * @requires modules/annotations\n */\n/**\n * A point-like object, a mock point or a point used in series.\n * @private\n * @typedef {\n *          Highcharts.AnnotationMockPoint|\n *          Highcharts.Point\n *     } Highcharts.AnnotationPointType\n * @requires modules/annotations\n */\n/**\n * Shape point as string, object or function.\n *\n * @typedef {\n *          string|\n *          Highcharts.AnnotationMockPointOptionsObject|\n *          Highcharts.AnnotationMockPointFunction\n *     } Highcharts.AnnotationShapePointOptions\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Annotations/Types/BasicAnnotation.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: BasicAnnotation_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass BasicAnnotation extends Annotations_Annotation {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addControlPoints() {\n        const options = this.options, controlPoints = BasicAnnotation.basicControlPoints, annotationType = this.basicType, optionsGroup = (options.labels ||\n            options.shapes ||\n            []);\n        optionsGroup.forEach((group) => {\n            group.controlPoints = controlPoints[annotationType];\n        });\n    }\n    init() {\n        const options = this.options;\n        if (options.shapes) {\n            delete options.labelOptions;\n            const type = options.shapes[0].type;\n            options.shapes[0].className =\n                (options.shapes[0].className || '') + ' highcharts-basic-shape';\n            // The rectangle is rendered as a path, whereas other basic shapes\n            // are rendered as their respective SVG shapes.\n            if (type && type !== 'path') {\n                this.basicType = type;\n            }\n            else {\n                this.basicType = 'rectangle';\n            }\n        }\n        else {\n            delete options.shapes;\n            this.basicType = 'label';\n        }\n        super.init.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBasicAnnotation.basicControlPoints = {\n    label: [{\n            symbol: 'triangle-down',\n            positioner: function (target) {\n                if (!target.graphic.placed) {\n                    return {\n                        x: 0,\n                        y: -9e7\n                    };\n                }\n                const xy = Annotations_MockPoint\n                    .pointToPixels(target.points[0]);\n                return {\n                    x: xy.x - (this.graphic.width || 0) / 2,\n                    y: xy.y - (this.graphic.height || 0) / 2\n                };\n            },\n            // TRANSLATE POINT/ANCHOR\n            events: {\n                drag: function (e, target) {\n                    const xy = this.mouseMoveToTranslation(e);\n                    target.translatePoint(xy.x, xy.y);\n                    target.annotation.userOptions.labels[0].point =\n                        target.options.point;\n                    target.redraw(false);\n                }\n            }\n        }, {\n            symbol: 'square',\n            positioner: function (target) {\n                if (!target.graphic.placed) {\n                    return {\n                        x: 0,\n                        y: -9e7\n                    };\n                }\n                return {\n                    x: target.graphic.alignAttr.x -\n                        (this.graphic.width || 0) / 2,\n                    y: target.graphic.alignAttr.y -\n                        (this.graphic.height || 0) / 2\n                };\n            },\n            // TRANSLATE POSITION WITHOUT CHANGING THE\n            // ANCHOR\n            events: {\n                drag: function (e, target) {\n                    const xy = this.mouseMoveToTranslation(e);\n                    target.translate(xy.x, xy.y);\n                    target.annotation.userOptions.labels[0].point =\n                        target.options.point;\n                    target.redraw(false);\n                }\n            }\n        }],\n    rectangle: [{\n            positioner: function (annotation) {\n                const xy = Annotations_MockPoint\n                    .pointToPixels(annotation.points[2]);\n                return {\n                    x: xy.x - 4,\n                    y: xy.y - 4\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const annotation = target.annotation, coords = this.chart.pointer?.getCoordinates(e), points = target.options.points, shapes = annotation.userOptions.shapes, xAxisIndex = annotation.clipXAxis?.index || 0, yAxisIndex = annotation.clipYAxis?.index || 0;\n                    if (coords) {\n                        const x = coords.xAxis[xAxisIndex].value, y = coords.yAxis[yAxisIndex].value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        if (shapes && shapes[0]) {\n                            shapes[0].points = target.options.points;\n                        }\n                    }\n                    annotation.redraw(false);\n                }\n            }\n        }],\n    circle: [{\n            positioner: function (target) {\n                const xy = Annotations_MockPoint.pointToPixels(target.points[0]), r = target.options.r;\n                return {\n                    x: xy.x + r * Math.cos(Math.PI / 4) -\n                        (this.graphic.width || 0) / 2,\n                    y: xy.y + r * Math.sin(Math.PI / 4) -\n                        (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                // TRANSFORM RADIUS ACCORDING TO Y\n                // TRANSLATION\n                drag: function (e, target) {\n                    const annotation = target.annotation, position = this.mouseMoveToTranslation(e), shapes = annotation.userOptions.shapes;\n                    target.setRadius(Math.max(target.options.r +\n                        position.y /\n                            Math.sin(Math.PI / 4), 5));\n                    if (shapes && shapes[0]) {\n                        shapes[0].r = target.options.r;\n                        shapes[0].point = target.options.point;\n                    }\n                    target.redraw(false);\n                }\n            }\n        }],\n    ellipse: [{\n            positioner: function (target) {\n                const position = target.getAbsolutePosition(target.points[0]);\n                return {\n                    x: position.x - (this.graphic.width || 0) / 2,\n                    y: position.y - (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const position = target.getAbsolutePosition(target.points[0]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 0);\n                    target.redraw(false);\n                }\n            }\n        }, {\n            positioner: function (target) {\n                const position = target.getAbsolutePosition(target.points[1]);\n                return {\n                    x: position.x - (this.graphic.width || 0) / 2,\n                    y: position.y - (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                }\n            }\n        }, {\n            positioner: function (target) {\n                const position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), attrs = target.getAttrs(position, position2);\n                return {\n                    x: attrs.cx - (this.graphic.width || 0) / 2 +\n                        attrs.ry * Math.sin((attrs.angle * Math.PI) / 180),\n                    y: attrs.cy - (this.graphic.height || 0) / 2 -\n                        attrs.ry * Math.cos((attrs.angle * Math.PI) / 180)\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const position = target.getAbsolutePosition(target.points[0]), position2 = target.getAbsolutePosition(target.points[1]), newR = target.getDistanceFromLine(position, position2, e.chartX, e.chartY), yAxis = target.getYAxis(), newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            }\n        }]\n};\nBasicAnnotation.prototype.defaultOptions = BasicAnnotation_merge(Annotations_Annotation.prototype.defaultOptions, {});\nAnnotations_Annotation.types.basicAnnotation = BasicAnnotation;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_BasicAnnotation = ((/* unused pure expression or super */ null && (BasicAnnotation)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/CrookedLine.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: CrookedLine_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass CrookedLine extends Annotations_Annotation {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Overrides default setter to get axes from typeOptions.\n     * @private\n     */\n    setClipAxes() {\n        this.clipXAxis = this.chart.xAxis[this.options.typeOptions.xAxis];\n        this.clipYAxis = this.chart.yAxis[this.options.typeOptions.yAxis];\n    }\n    getPointsOptions() {\n        const typeOptions = this.options.typeOptions;\n        return (typeOptions.points || []).map((pointOptions) => {\n            pointOptions.xAxis = typeOptions.xAxis;\n            pointOptions.yAxis = typeOptions.yAxis;\n            return pointOptions;\n        });\n    }\n    getControlPointsOptions() {\n        return this.getPointsOptions();\n    }\n    addControlPoints() {\n        this.getControlPointsOptions().forEach(function (pointOptions, i) {\n            const controlPoint = new Annotations_ControlPoint(this.chart, this, CrookedLine_merge(this.options.controlPointOptions, pointOptions.controlPoint), i);\n            this.controlPoints.push(controlPoint);\n            pointOptions.controlPoint = controlPoint.options;\n        }, this);\n    }\n    addShapes() {\n        const typeOptions = this.options.typeOptions, shape = this.initShape(CrookedLine_merge(typeOptions.line, {\n            type: 'path',\n            className: 'highcharts-crooked-lines',\n            points: this.points.map((_point, i) => (function (target) {\n                return target.annotation.points[i];\n            }))\n        }), 0);\n        typeOptions.line = shape.options;\n    }\n}\nCrookedLine.prototype.defaultOptions = CrookedLine_merge(Annotations_Annotation.prototype.defaultOptions, \n/**\n * A crooked line annotation.\n *\n * @sample highcharts/annotations-advanced/crooked-line/\n *         Crooked line\n *\n * @product      highstock\n * @optionparent annotations.crookedLine\n */\n{\n    /**\n     * @extends   annotations.labelOptions\n     * @apioption annotations.crookedLine.labelOptions\n     */\n    /**\n     * @extends   annotations.shapeOptions\n     * @apioption annotations.crookedLine.shapeOptions\n     */\n    /**\n     * Additional options for an annotation with the type.\n     */\n    typeOptions: {\n        /**\n         * This number defines which xAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the xAxis array.\n         */\n        xAxis: 0,\n        /**\n         * This number defines which yAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the xAxis array.\n         */\n        yAxis: 0,\n        /**\n         * @type      {Array<*>}\n         * @apioption annotations.crookedLine.typeOptions.points\n         */\n        /**\n         * The x position of the point.\n         *\n         * @type      {number}\n         * @apioption annotations.crookedLine.typeOptions.points.x\n         */\n        /**\n         * The y position of the point.\n         *\n         * @type      {number}\n         * @apioption annotations.crookedLine.typeOptions.points.y\n         */\n        /**\n         * @type      {number}\n         * @excluding positioner, events\n         * @apioption annotations.crookedLine.typeOptions.points.controlPoint\n         */\n        /**\n         * Line options.\n         *\n         * @excluding height, point, points, r, type, width\n         */\n        line: {\n            fill: 'none'\n        }\n    },\n    /**\n     * @excluding positioner, events\n     */\n    controlPointOptions: {\n        positioner: function (target) {\n            const graphic = this.graphic, xy = Annotations_MockPoint.pointToPixels(target.points[this.index]);\n            return {\n                x: xy.x - (graphic.width || 0) / 2,\n                y: xy.y - (graphic.height || 0) / 2\n            };\n        },\n        events: {\n            drag: function (e, target) {\n                if (target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                    const translation = this.mouseMoveToTranslation(e), typeOptions = target.options.typeOptions;\n                    target.translatePoint(translation.x, translation.y, this.index);\n                    // Update options:\n                    typeOptions.points[this.index].x =\n                        target.points[this.index].x;\n                    typeOptions.points[this.index].y =\n                        target.points[this.index].y;\n                    target.redraw(false);\n                }\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.crookedLine = CrookedLine;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_CrookedLine = (CrookedLine);\n\n;// ./code/es-modules/Extensions/Annotations/Types/ElliottWave.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: ElliottWave_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass ElliottWave extends Types_CrookedLine {\n    /* *\n     *\n     * Functions\n     *\n     * */\n    addLabels() {\n        this.getPointsOptions().forEach((point, i) => {\n            const typeOptions = this.options.typeOptions, label = this.initLabel(ElliottWave_merge(point.label, {\n                text: typeOptions.labels[i],\n                point: function (target) {\n                    return target.annotation.points[i];\n                }\n            }), false);\n            point.label = label.options;\n        });\n    }\n}\nElliottWave.prototype.defaultOptions = ElliottWave_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * An elliott wave annotation.\n *\n * @sample highcharts/annotations-advanced/elliott-wave/\n *         Elliott wave\n *\n * @extends      annotations.crookedLine\n * @product      highstock\n * @optionparent annotations.elliottWave\n */\n{\n    typeOptions: {\n        /**\n         * @extends   annotations.crookedLine.labelOptions\n         * @apioption annotations.elliottWave.typeOptions.points.label\n         */\n        /**\n         * @ignore-option\n         */\n        labels: ['(0)', '(A)', '(B)', '(C)', '(D)', '(E)'],\n        line: {\n            strokeWidth: 1\n        }\n    },\n    labelOptions: {\n        align: 'center',\n        allowOverlap: true,\n        crop: true,\n        overflow: 'none',\n        type: 'rect',\n        backgroundColor: 'none',\n        borderWidth: 0,\n        y: -5\n    }\n});\nAnnotations_Annotation.types.elliottWave = ElliottWave;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_ElliottWave = ((/* unused pure expression or super */ null && (ElliottWave)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Tunnel.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { merge: Tunnel_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction getSecondCoordinate(p1, p2, x) {\n    return (p2.y - p1.y) / (p2.x - p1.x) * (x - p1.x) + p1.y;\n}\n/* *\n *\n *  Class\n *\n * */\nclass Tunnel extends Types_CrookedLine {\n    /* *\n     *\n     * Functions\n     *\n     * */\n    getPointsOptions() {\n        const pointsOptions = Types_CrookedLine.prototype.getPointsOptions.call(this), yAxisIndex = this.options.typeOptions.yAxis || 0, yAxis = this.chart.yAxis[yAxisIndex];\n        pointsOptions[2] = this.heightPointOptions(pointsOptions[1]);\n        pointsOptions[3] = this.heightPointOptions(pointsOptions[0]);\n        // In case of log axis, translate the bottom left point again, #16769\n        if (yAxis && yAxis.logarithmic) {\n            // Get the height in pixels\n            const h = yAxis.toPixels(pointsOptions[2].y) -\n                yAxis.toPixels(pointsOptions[1].y), \n            // Get the pixel position of the last point\n            y3 = yAxis.toPixels(pointsOptions[0].y) + h;\n            // Set the new value\n            pointsOptions[3].y = yAxis.toValue(y3);\n        }\n        return pointsOptions;\n    }\n    getControlPointsOptions() {\n        return this.getPointsOptions().slice(0, 2);\n    }\n    heightPointOptions(pointOptions) {\n        const heightPointOptions = Tunnel_merge(pointOptions), typeOptions = this.options.typeOptions;\n        heightPointOptions.y += typeOptions.height;\n        return heightPointOptions;\n    }\n    addControlPoints() {\n        Types_CrookedLine.prototype.addControlPoints.call(this);\n        const options = this.options, typeOptions = options.typeOptions, controlPoint = new Annotations_ControlPoint(this.chart, this, Tunnel_merge(options.controlPointOptions, typeOptions.heightControlPoint), 2);\n        this.controlPoints.push(controlPoint);\n        typeOptions.heightControlPoint = controlPoint.options;\n    }\n    addShapes() {\n        this.addLine();\n        this.addBackground();\n    }\n    addLine() {\n        const line = this.initShape(Tunnel_merge(this.options.typeOptions.line, {\n            type: 'path',\n            points: [\n                this.points[0],\n                this.points[1],\n                function (target) {\n                    const pointOptions = Annotations_MockPoint.pointToOptions(target.annotation.points[2]);\n                    pointOptions.command = 'M';\n                    return pointOptions;\n                },\n                this.points[3]\n            ],\n            className: 'highcharts-tunnel-lines'\n        }), 0);\n        this.options.typeOptions.line = line.options;\n    }\n    addBackground() {\n        const background = this.initShape(Tunnel_merge(this.options.typeOptions.background, {\n            type: 'path',\n            points: this.points.slice(),\n            className: 'highcharts-tunnel-background'\n        }), 1);\n        this.options.typeOptions.background = background.options;\n    }\n    /**\n     * Translate start or end (\"left\" or \"right\") side of the tunnel.\n     * @private\n     * @param {number} dx\n     * the amount of x translation\n     * @param {number} dy\n     * the amount of y translation\n     * @param {boolean} [end]\n     * whether to translate start or end side\n     */\n    translateSide(dx, dy, end) {\n        const topIndex = Number(end), bottomIndex = topIndex === 0 ? 3 : 2;\n        this.translatePoint(dx, dy, topIndex);\n        this.translatePoint(dx, dy, bottomIndex);\n    }\n    /**\n     * Translate height of the tunnel.\n     * @private\n     * @param {number} dh\n     * the amount of height translation\n     */\n    translateHeight(dh) {\n        this.translatePoint(0, dh, 2);\n        this.translatePoint(0, dh, 3);\n        this.options.typeOptions.height = this.points[3].y -\n            this.points[0].y;\n        this.userOptions.typeOptions.height = this.options.typeOptions.height;\n    }\n}\nTunnel.prototype.defaultOptions = Tunnel_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * A tunnel annotation.\n *\n * @extends annotations.crookedLine\n * @sample highcharts/annotations-advanced/tunnel/\n *         Tunnel\n * @product highstock\n * @optionparent annotations.tunnel\n */\n{\n    typeOptions: {\n        /**\n         * Background options.\n         *\n         * @type {Object}\n         * @excluding height, point, points, r, type, width, markerEnd,\n         *            markerStart\n         */\n        background: {\n            fill: 'rgba(130, 170, 255, 0.4)',\n            strokeWidth: 0\n        },\n        line: {\n            strokeWidth: 1\n        },\n        /**\n         * The height of the annotation in terms of yAxis.\n         */\n        height: -2,\n        /**\n         * Options for the control point which controls\n         * the annotation's height.\n         *\n         * @extends annotations.crookedLine.controlPointOptions\n         * @excluding positioner, events\n         */\n        heightControlPoint: {\n            positioner: function (target) {\n                const startXY = Annotations_MockPoint.pointToPixels(target.points[2]), endXY = Annotations_MockPoint.pointToPixels(target.points[3]), x = (startXY.x + endXY.x) / 2;\n                return {\n                    x: x - (this.graphic.width || 0) / 2,\n                    y: getSecondCoordinate(startXY, endXY, x) -\n                        (this.graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    if (target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                        visiblePlotOnly: true\n                    })) {\n                        target.translateHeight(this.mouseMoveToTranslation(e).y);\n                        target.redraw(false);\n                    }\n                }\n            }\n        }\n    },\n    /**\n     * @extends annotations.crookedLine.controlPointOptions\n     * @excluding positioner, events\n     */\n    controlPointOptions: {\n        events: {\n            drag: function (e, target) {\n                if (target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                    const translation = this.mouseMoveToTranslation(e);\n                    target.translateSide(translation.x, translation.y, !!this.index);\n                    target.redraw(false);\n                }\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.tunnel = Tunnel;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Tunnel = (Tunnel);\n\n;// ./code/es-modules/Extensions/Annotations/Types/InfinityLine.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: InfinityLine_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass InfinityLine extends Types_CrookedLine {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static edgePoint(startIndex, endIndex) {\n        return function (target) {\n            const annotation = target.annotation, type = annotation.options.typeOptions.type;\n            let points = annotation.points;\n            if (type === 'horizontalLine' || type === 'verticalLine') {\n                // Horizontal and vertical lines have only one point,\n                // make a copy of it:\n                points = [\n                    points[0],\n                    new Annotations_MockPoint(annotation.chart, points[0].target, {\n                        // Add 0 or 1 to x or y depending on type\n                        x: points[0].x + +(type === 'horizontalLine'),\n                        y: points[0].y + +(type === 'verticalLine'),\n                        xAxis: points[0].options.xAxis,\n                        yAxis: points[0].options.yAxis\n                    })\n                ];\n            }\n            return InfinityLine.findEdgePoint(points[startIndex], points[endIndex]);\n        };\n    }\n    static findEdgeCoordinate(firstPoint, secondPoint, xOrY, edgePointFirstCoordinate) {\n        const xOrYOpposite = xOrY === 'x' ? 'y' : 'x';\n        // Solves equation for x or y\n        // y - y1 = (y2 - y1) / (x2 - x1) * (x - x1)\n        return ((secondPoint[xOrY] - firstPoint[xOrY]) *\n            (edgePointFirstCoordinate - firstPoint[xOrYOpposite]) /\n            (secondPoint[xOrYOpposite] - firstPoint[xOrYOpposite]) +\n            firstPoint[xOrY]);\n    }\n    static findEdgePoint(firstPoint, secondPoint) {\n        const chart = firstPoint.series.chart, xAxis = firstPoint.series.xAxis, yAxis = secondPoint.series.yAxis, firstPointPixels = Annotations_MockPoint.pointToPixels(firstPoint), secondPointPixels = Annotations_MockPoint.pointToPixels(secondPoint), deltaX = secondPointPixels.x - firstPointPixels.x, deltaY = secondPointPixels.y - firstPointPixels.y, xAxisMin = xAxis.left, xAxisMax = xAxisMin + xAxis.width, yAxisMin = yAxis.top, yAxisMax = yAxisMin + yAxis.height, xLimit = deltaX < 0 ? xAxisMin : xAxisMax, yLimit = deltaY < 0 ? yAxisMin : yAxisMax, edgePoint = {\n            x: deltaX === 0 ? firstPointPixels.x : xLimit,\n            y: deltaY === 0 ? firstPointPixels.y : yLimit\n        };\n        let edgePointX, edgePointY, swap;\n        if (deltaX !== 0 && deltaY !== 0) {\n            edgePointY = InfinityLine.findEdgeCoordinate(firstPointPixels, secondPointPixels, 'y', xLimit);\n            edgePointX = InfinityLine.findEdgeCoordinate(firstPointPixels, secondPointPixels, 'x', yLimit);\n            if (edgePointY >= yAxisMin && edgePointY <= yAxisMax) {\n                edgePoint.x = xLimit;\n                edgePoint.y = edgePointY;\n            }\n            else {\n                edgePoint.x = edgePointX;\n                edgePoint.y = yLimit;\n            }\n        }\n        edgePoint.x -= chart.plotLeft;\n        edgePoint.y -= chart.plotTop;\n        if (firstPoint.series.chart.inverted) {\n            swap = edgePoint.x;\n            edgePoint.x = edgePoint.y;\n            edgePoint.y = swap;\n        }\n        return edgePoint;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addShapes() {\n        const typeOptions = this.options.typeOptions, points = [\n            this.points[0],\n            InfinityLine.endEdgePoint\n        ];\n        // Be case-insensitive (#15155) e.g.:\n        // - line\n        // - horizontalLine\n        // - verticalLine\n        if (typeOptions.type.match(/line/gi)) {\n            points[0] = InfinityLine.startEdgePoint;\n        }\n        const line = this.initShape(InfinityLine_merge(typeOptions.line, {\n            type: 'path',\n            points: points,\n            className: 'highcharts-infinity-lines'\n        }), 0);\n        typeOptions.line = line.options;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nInfinityLine.endEdgePoint = InfinityLine.edgePoint(0, 1);\nInfinityLine.startEdgePoint = InfinityLine.edgePoint(1, 0);\nInfinityLine.prototype.defaultOptions = InfinityLine_merge(Types_CrookedLine.prototype.defaultOptions, {});\nAnnotations_Annotation.types.infinityLine = InfinityLine;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_InfinityLine = (InfinityLine);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * An infinity line annotation.\n *\n * @sample highcharts/annotations-advanced/infinity-line/\n *         Infinity Line\n *\n * @extends   annotations.crookedLine\n * @product   highstock\n * @apioption annotations.infinityLine\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/Extensions/Annotations/Types/TimeCycles.js\n/* *\n *\n *  Authors: <AUTHORS>\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: TimeCycles_merge, isNumber: TimeCycles_isNumber, defined: TimeCycles_defined } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Function to create start of the path.\n * @param {number} x x position of the TimeCycles\n * @param {number} y y position of the TimeCycles\n * @return {string} path\n */\nfunction getStartingPath(x, y) {\n    return ['M', x, y];\n}\n/**\n * Function which generates the path of the halfcircle.\n *\n * @param {number} pixelInterval diameter of the circle in pixels\n * @param {number} numberOfCircles number of cricles\n * @param {number} startX x position of the first circle\n * @param {number} y y position of the bottom of the timeCycles\n * @return {string} path\n *\n */\nfunction getCirclePath(pixelInterval, numberOfCircles, startX, y) {\n    const path = [];\n    for (let i = 1; i <= numberOfCircles; i++) {\n        path.push([\n            'A',\n            pixelInterval / 2,\n            pixelInterval / 2,\n            0,\n            1,\n            1,\n            startX + i * pixelInterval,\n            y\n        ]);\n    }\n    return path;\n}\n/* *\n *\n *  Class\n *\n * */\nclass TimeCycles extends Types_CrookedLine {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(annotation, userOptions, index) {\n        if (TimeCycles_defined(userOptions.yAxis)) {\n            userOptions.points.forEach((point) => {\n                point.yAxis = userOptions.yAxis;\n            });\n        }\n        if (TimeCycles_defined(userOptions.xAxis)) {\n            userOptions.points.forEach((point) => {\n                point.xAxis = userOptions.xAxis;\n            });\n        }\n        super.init(annotation, userOptions, index);\n    }\n    setPath() {\n        this.shapes[0].options.d = this.getPath();\n    }\n    getPath() {\n        return [getStartingPath(this.startX, this.y)].concat(getCirclePath(this.pixelInterval, this.numberOfCircles, this.startX, this.y));\n    }\n    addShapes() {\n        const typeOptions = this.options.typeOptions;\n        this.setPathProperties();\n        const shape = this.initShape(TimeCycles_merge(typeOptions.line, {\n            type: 'path',\n            d: this.getPath(),\n            points: this.options.points,\n            className: 'highcharts-timecycles-lines'\n        }), 0);\n        typeOptions.line = shape.options;\n    }\n    addControlPoints() {\n        const options = this.options, typeOptions = options.typeOptions;\n        options.controlPointOptions.style.cursor = this.chart.inverted ?\n            'ns-resize' :\n            'ew-resize';\n        typeOptions.controlPointOptions.forEach((option) => {\n            const controlPointsOptions = TimeCycles_merge(options.controlPointOptions, option);\n            const controlPoint = new Annotations_ControlPoint(this.chart, this, controlPointsOptions, 0);\n            this.controlPoints.push(controlPoint);\n        });\n    }\n    setPathProperties() {\n        const options = this.options.typeOptions, points = options.points;\n        if (!points) {\n            return;\n        }\n        const point1 = points[0], point2 = points[1], xAxisNumber = options.xAxis || 0, yAxisNumber = options.yAxis || 0, xAxis = this.chart.xAxis[xAxisNumber], yAxis = this.chart.yAxis[yAxisNumber], xValue1 = point1.x, yValue = point1.y, xValue2 = point2.x;\n        if (!xValue1 || !xValue2) {\n            return;\n        }\n        const y = TimeCycles_isNumber(yValue) ?\n            yAxis.toPixels(yValue) :\n            yAxis.top + yAxis.height, x = TimeCycles_isNumber(xValue1) ? xAxis.toPixels(xValue1) : xAxis.left, x2 = TimeCycles_isNumber(xValue2) ? xAxis.toPixels(xValue2) : xAxis.left + 30, xAxisLength = xAxis.len, pixelInterval = Math.round(Math.max(Math.abs(x2 - x), 2)), \n        // There can be 2 not full circles on the chart, so add 2.\n        numberOfCircles = Math.floor(xAxisLength / pixelInterval) + 2, \n        // Calculate where the annotation should start drawing relative to\n        // first point.\n        pixelShift = (Math.floor((x - xAxis.left) / pixelInterval) + 1) * pixelInterval;\n        this.startX = x - pixelShift;\n        this.y = y;\n        this.pixelInterval = pixelInterval;\n        this.numberOfCircles = numberOfCircles;\n    }\n    redraw(animation) {\n        this.setPathProperties();\n        this.setPath();\n        super.redraw(animation);\n    }\n}\nTimeCycles.prototype.defaultOptions = TimeCycles_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * The TimeCycles Annotation\n *\n * @sample highcharts/annotations-advanced/time-cycles/\n *         Time Cycles annotation\n *\n * @extends      annotations.crookedLine\n * @product      highstock\n * @exclude      labelOptions\n * @optionparent annotations.timeCycles\n */\n{\n    typeOptions: {\n        /**\n         * @exclude   y\n         * @product   highstock\n         * @apioption annotations.timeCycles.typeOptions.points\n         */\n        controlPointOptions: [{\n                positioner: function (target) {\n                    const point = target.points[0], position = target.anchor(point).absolutePosition;\n                    return {\n                        x: position.x - (this.graphic.width || 0) / 2,\n                        y: target.y - (this.graphic.height || 0)\n                    };\n                },\n                events: {\n                    drag: function (e, target) {\n                        const position = target.anchor(target.points[0]).absolutePosition;\n                        target.translatePoint(e.chartX - position.x, 0, 0);\n                        target.redraw(false);\n                    }\n                }\n            }, {\n                positioner: function (target) {\n                    const point = target.points[1], position = target.anchor(point).absolutePosition;\n                    return {\n                        x: position.x - (this.graphic.width || 0) / 2,\n                        y: target.y - (this.graphic.height || 0)\n                    };\n                },\n                events: {\n                    drag: function (e, target) {\n                        const position = target.anchor(target.points[1]).absolutePosition;\n                        target.translatePoint(e.chartX - position.x, 0, 1);\n                        target.redraw(false);\n                    }\n                }\n            }]\n    }\n});\nAnnotations_Annotation.types.timeCycles = TimeCycles;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_TimeCycles = ((/* unused pure expression or super */ null && (TimeCycles)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Fibonacci.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: Fibonacci_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction createPathDGenerator(retracementIndex, isBackground) {\n    return function () {\n        const annotation = this.annotation;\n        if (!annotation.startRetracements || !annotation.endRetracements) {\n            return [];\n        }\n        const leftTop = this.anchor(annotation.startRetracements[retracementIndex]).absolutePosition, rightTop = this.anchor(annotation.endRetracements[retracementIndex]).absolutePosition, d = [\n            ['M', Math.round(leftTop.x), Math.round(leftTop.y)],\n            ['L', Math.round(rightTop.x), Math.round(rightTop.y)]\n        ];\n        if (isBackground) {\n            const rightBottom = this.anchor(annotation.endRetracements[retracementIndex - 1]).absolutePosition;\n            const leftBottom = this.anchor(annotation.startRetracements[retracementIndex - 1]).absolutePosition;\n            d.push(['L', Math.round(rightBottom.x), Math.round(rightBottom.y)], ['L', Math.round(leftBottom.x), Math.round(leftBottom.y)]);\n        }\n        return d;\n    };\n}\n/* *\n *\n *  Class\n *\n * */\nclass Fibonacci extends Types_Tunnel {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    linkPoints() {\n        super.linkPoints();\n        this.linkRetracementsPoints();\n        return;\n    }\n    linkRetracementsPoints() {\n        const points = this.points, startDiff = points[0].y - points[3].y, endDiff = points[1].y - points[2].y, startX = points[0].x, endX = points[1].x;\n        Fibonacci.levels.forEach((level, i) => {\n            const startRetracement = points[0].y - startDiff * level, endRetracement = points[1].y - endDiff * level, index = this.options.typeOptions.reversed ?\n                (Fibonacci.levels.length - i - 1) : i;\n            this.startRetracements = this.startRetracements || [];\n            this.endRetracements = this.endRetracements || [];\n            this.linkRetracementPoint(index, startX, startRetracement, this.startRetracements);\n            this.linkRetracementPoint(index, endX, endRetracement, this.endRetracements);\n        });\n    }\n    linkRetracementPoint(pointIndex, x, y, retracements) {\n        const point = retracements[pointIndex], typeOptions = this.options.typeOptions;\n        if (!point) {\n            retracements[pointIndex] = new Annotations_MockPoint(this.chart, this, {\n                x: x,\n                y: y,\n                xAxis: typeOptions.xAxis,\n                yAxis: typeOptions.yAxis\n            });\n        }\n        else {\n            point.options.x = x;\n            point.options.y = y;\n            point.refresh();\n        }\n    }\n    addShapes() {\n        Fibonacci.levels.forEach(function (_level, i) {\n            const { backgroundColors, lineColor, lineColors } = this.options.typeOptions;\n            this.initShape({\n                type: 'path',\n                d: createPathDGenerator(i),\n                stroke: lineColors[i] || lineColor,\n                className: 'highcharts-fibonacci-line'\n            }, i);\n            if (i > 0) {\n                this.initShape({\n                    type: 'path',\n                    fill: backgroundColors[i - 1],\n                    strokeWidth: 0,\n                    d: createPathDGenerator(i, true),\n                    className: 'highcharts-fibonacci-background-' + (i - 1)\n                });\n            }\n        }, this);\n    }\n    addLabels() {\n        Fibonacci.levels.forEach(function (level, i) {\n            const options = this.options.typeOptions, label = this.initLabel(Fibonacci_merge(options.labels[i], {\n                point: function (target) {\n                    const point = Annotations_MockPoint.pointToOptions(target.annotation.startRetracements[i]);\n                    return point;\n                },\n                text: level.toString()\n            }));\n            options.labels[i] = label.options;\n        }, this);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nFibonacci.levels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1];\nFibonacci.prototype.defaultOptions = Fibonacci_merge(Types_Tunnel.prototype.defaultOptions, \n/**\n * A fibonacci annotation.\n *\n * @sample highcharts/annotations-advanced/fibonacci/\n *         Fibonacci\n *\n * @extends      annotations.crookedLine\n * @product      highstock\n * @optionparent annotations.fibonacci\n */\n{\n    typeOptions: {\n        /**\n         * Whether the annotation levels should be reversed. By default they\n         * start from 0 and go to 1.\n         *\n         * @sample highcharts/annotations-advanced/fibonacci-reversed/\n         *         Fibonacci annotation reversed\n         *\n         * @type {boolean}\n         * @apioption annotations.fibonacci.typeOptions.reversed\n         */\n        reversed: false,\n        /**\n         * The height of the fibonacci in terms of yAxis.\n         */\n        height: 2,\n        /**\n         * An array of background colors:\n         * Default to:\n         * ```\n         * [\n         * 'rgba(130, 170, 255, 0.4)',\n         * 'rgba(139, 191, 216, 0.4)',\n         * 'rgba(150, 216, 192, 0.4)',\n         * 'rgba(156, 229, 161, 0.4)',\n         * 'rgba(162, 241, 130, 0.4)',\n         * 'rgba(169, 255, 101, 0.4)'\n         * ]\n         * ```\n         */\n        backgroundColors: [\n            'rgba(130, 170, 255, 0.4)',\n            'rgba(139, 191, 216, 0.4)',\n            'rgba(150, 216, 192, 0.4)',\n            'rgba(156, 229, 161, 0.4)',\n            'rgba(162, 241, 130, 0.4)',\n            'rgba(169, 255, 101, 0.4)'\n        ],\n        /**\n         * The color of line.\n         */\n        lineColor: \"#999999\" /* Palette.neutralColor40 */,\n        /**\n         * An array of colors for the lines.\n         */\n        lineColors: [],\n        /**\n         * An array with options for the labels.\n         *\n         * @type      {Array<*>}\n         * @extends   annotations.crookedLine.labelOptions\n         * @apioption annotations.fibonacci.typeOptions.labels\n         */\n        labels: []\n    },\n    labelOptions: {\n        allowOverlap: true,\n        align: 'right',\n        backgroundColor: 'none',\n        borderWidth: 0,\n        crop: false,\n        overflow: 'none',\n        shape: 'rect',\n        style: {\n            color: 'grey'\n        },\n        verticalAlign: 'middle',\n        y: 0\n    }\n});\nAnnotations_Annotation.types.fibonacci = Fibonacci;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Fibonacci = ((/* unused pure expression or super */ null && (Fibonacci)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/FibonacciTimeZones.js\n/* *\n *\n *  Author: Rafal Sebestjanski\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\n\nconst { merge: FibonacciTimeZones_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\nMethod taken (and slightly changed) from the InfinityLine annotation.\n\nIt uses x coordinate to create two mock points on the same x. Then,\nit uses some logic from InfinityLine to find equation of the line passing\nthrough our two points and, using that equation, it finds and returns\nthe coordinates of where the line intersects the plot area edges.\n\nThis is being done for each fibonacci time zone line.\n\n\n        this point here is found\n            |\n            v\n    |---------*--------------------------------------------------------|\n    |                                                                  |\n    |                                                                  |\n    |                                                                  |\n    |                                                                  |\n    |         *   copy of the primary point                            |\n    |                                                                  |\n    |         *   primary point (e.g. the one given in options)        |\n    |                                                                  |\n    |---------*--------------------------------------------------------|\n        and this point here is found (intersection with the plot area edge)\n\n* @private\n*/\nfunction edgePoint(startIndex, endIndex, fibonacciIndex) {\n    return function (target) {\n        const chart = target.annotation.chart, plotLeftOrTop = chart.inverted ? chart.plotTop : chart.plotLeft;\n        let points = target.annotation.points;\n        const xAxis = points[0].series.xAxis, \n        // Distance between the two first lines in pixels\n        deltaX = points.length > 1 ?\n            points[1].plotX - points[0].plotX : 0, \n        // `firstLine.x + fibb * offset`\n        x = xAxis.toValue(points[0].plotX + plotLeftOrTop + fibonacciIndex * deltaX);\n        // We need 2 mock points with the same x coordinate, different y\n        points = [\n            new Annotations_MockPoint(chart, points[0].target, {\n                x: x,\n                y: 0,\n                xAxis: points[0].options.xAxis,\n                yAxis: points[0].options.yAxis\n            }),\n            new Annotations_MockPoint(chart, points[0].target, {\n                x: x,\n                y: 1,\n                xAxis: points[0].options.xAxis,\n                yAxis: points[0].options.yAxis\n            })\n        ];\n        return Types_InfinityLine.findEdgePoint(points[startIndex], points[endIndex]);\n    };\n}\n/* *\n *\n *  Class\n *\n * */\nclass FibonacciTimeZones extends Types_CrookedLine {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addShapes() {\n        const numberOfLines = 11;\n        let fibb = 1, nextFibb = 1;\n        for (let i = 0; i < numberOfLines; i++) {\n            // The fibb variable equals to 1 twice - correct it in the first\n            // iteration so the lines don't overlap\n            const correctedFibb = !i ? 0 : fibb, points = [\n                edgePoint(1, 0, correctedFibb),\n                edgePoint(0, 1, correctedFibb)\n            ];\n            // Calculate fibonacci\n            nextFibb = fibb + nextFibb;\n            fibb = nextFibb - fibb;\n            // Save the second line for the control point\n            if (i === 1) {\n                this.secondLineEdgePoints = [points[0], points[1]];\n            }\n            this.initShape(FibonacciTimeZones_merge(this.options.typeOptions.line, {\n                type: 'path',\n                points: points,\n                className: 'highcharts-fibonacci-timezones-lines'\n            }), i // Shape's index. Can be found in annotation.shapes[i].index\n            );\n        }\n    }\n    addControlPoints() {\n        const options = this.options, typeOptions = options.typeOptions, controlPoint = new Annotations_ControlPoint(this.chart, this, FibonacciTimeZones_merge(options.controlPointOptions, typeOptions.controlPointOptions), 0);\n        this.controlPoints.push(controlPoint);\n        typeOptions.controlPointOptions = controlPoint.options;\n    }\n}\nFibonacciTimeZones.prototype.defaultOptions = FibonacciTimeZones_merge(Types_CrookedLine.prototype.defaultOptions, \n/**\n * The Fibonacci Time Zones annotation.\n *\n * @sample highcharts/annotations-advanced/fibonacci-time-zones/\n *         Fibonacci Time Zones\n *\n * @extends      annotations.crookedLine\n * @since        9.3.0\n * @product      highstock\n * @optionparent annotations.fibonacciTimeZones\n */\n{\n    typeOptions: {\n        /**\n         * @exclude   y\n         * @since     9.3.0\n         * @product   highstock\n         * @apioption annotations.fibonacciTimeZones.typeOptions.points\n         */\n        // Options for showing in popup edit\n        line: {\n            /**\n             * The color of the lines.\n             *\n             * @type      {string}\n             * @since     9.3.0\n             * @default   'rgba(0, 0, 0, 0.75)'\n             * @apioption annotations.fibonacciTimeZones.typeOptions.line.stroke\n             */\n            stroke: 'rgba(0, 0, 0, 0.75)',\n            /**\n             * The width of the lines.\n             *\n             * @type      {number}\n             * @since     9.3.0\n             * @default   1\n             * @apioption annotations.fibonacciTimeZones.typeOptions.line.strokeWidth\n             */\n            strokeWidth: 1,\n            // Don't inherit fill (don't display in popup edit)\n            fill: void 0\n        },\n        controlPointOptions: {\n            positioner: function () {\n                // The control point is in the middle of the second line\n                const target = this.target, graphic = this.graphic, edgePoints = target.secondLineEdgePoints, args = { annotation: target }, firstEdgePointY = edgePoints[0](args).y, secondEdgePointY = edgePoints[1](args).y, plotLeft = this.chart.plotLeft, plotTop = this.chart.plotTop;\n                let x = edgePoints[0](args).x, y = (firstEdgePointY + secondEdgePointY) / 2;\n                if (this.chart.inverted) {\n                    [x, y] = [y, x];\n                }\n                return {\n                    x: plotLeft + x - (graphic.width || 0) / 2,\n                    y: plotTop + y - (graphic.height || 0) / 2\n                };\n            },\n            events: {\n                drag: function (e, target) {\n                    const isInsidePlot = target.chart.isInsidePlot(e.chartX - target.chart.plotLeft, e.chartY - target.chart.plotTop, {\n                        visiblePlotOnly: true\n                    });\n                    if (isInsidePlot) {\n                        const translation = this.mouseMoveToTranslation(e);\n                        target.translatePoint(translation.x, 0, 1);\n                        target.redraw(false);\n                    }\n                }\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.fibonacciTimeZones = FibonacciTimeZones;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_FibonacciTimeZones = ((/* unused pure expression or super */ null && (FibonacciTimeZones)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Pitchfork.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { merge: Pitchfork_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass Pitchfork extends Types_InfinityLine {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static outerLineEdgePoint(firstPointIndex) {\n        return function (target) {\n            const annotation = target.annotation, points = annotation.points;\n            return Pitchfork.findEdgePoint(points[firstPointIndex], points[0], new Annotations_MockPoint(annotation.chart, target, annotation.midPointOptions()));\n        };\n    }\n    static findEdgePoint(point, firstAnglePoint, secondAnglePoint) {\n        const angle = Math.atan2((secondAnglePoint.plotY -\n            firstAnglePoint.plotY), secondAnglePoint.plotX - firstAnglePoint.plotX), distance = 1e7;\n        return {\n            x: point.plotX + distance * Math.cos(angle),\n            y: point.plotY + distance * Math.sin(angle)\n        };\n    }\n    static middleLineEdgePoint(target) {\n        const annotation = target.annotation, points = annotation.points;\n        return Types_InfinityLine.findEdgePoint(points[0], new Annotations_MockPoint(annotation.chart, target, annotation.midPointOptions()));\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    midPointOptions() {\n        const points = this.points;\n        return {\n            x: (points[1].x + points[2].x) / 2,\n            y: (points[1].y + points[2].y) / 2,\n            xAxis: points[0].series.xAxis,\n            yAxis: points[0].series.yAxis\n        };\n    }\n    addShapes() {\n        this.addLines();\n        this.addBackgrounds();\n    }\n    addLines() {\n        const className = 'highcharts-pitchfork-lines';\n        this.initShape({\n            type: 'path',\n            points: [\n                this.points[0],\n                Pitchfork.middleLineEdgePoint\n            ],\n            className\n        }, 0);\n        this.initShape({\n            type: 'path',\n            points: [\n                this.points[1],\n                Pitchfork.topLineEdgePoint\n            ],\n            className\n        }, 1);\n        this.initShape({\n            type: 'path',\n            points: [\n                this.points[2],\n                Pitchfork.bottomLineEdgePoint\n            ],\n            className\n        }, 2);\n    }\n    addBackgrounds() {\n        const shapes = this.shapes, typeOptions = this.options.typeOptions;\n        const innerBackground = this.initShape(Pitchfork_merge(typeOptions.innerBackground, {\n            type: 'path',\n            points: [\n                function (target) {\n                    const annotation = target.annotation, points = annotation.points, midPointOptions = annotation.midPointOptions();\n                    return {\n                        x: (points[1].x + midPointOptions.x) / 2,\n                        y: (points[1].y + midPointOptions.y) / 2,\n                        xAxis: midPointOptions.xAxis,\n                        yAxis: midPointOptions.yAxis\n                    };\n                },\n                shapes[1].points[1],\n                shapes[2].points[1],\n                function (target) {\n                    const annotation = target.annotation, points = annotation.points, midPointOptions = annotation.midPointOptions();\n                    return {\n                        x: (midPointOptions.x + points[2].x) / 2,\n                        y: (midPointOptions.y + points[2].y) / 2,\n                        xAxis: midPointOptions.xAxis,\n                        yAxis: midPointOptions.yAxis\n                    };\n                }\n            ],\n            className: 'highcharts-pitchfork-inner-background'\n        }), 3);\n        const outerBackground = this.initShape(Pitchfork_merge(typeOptions.outerBackground, {\n            type: 'path',\n            points: [\n                this.points[1],\n                shapes[1].points[1],\n                shapes[2].points[1],\n                this.points[2]\n            ],\n            className: 'highcharts-pitchfork-outer-background'\n        }), 4);\n        typeOptions.innerBackground = innerBackground.options;\n        typeOptions.outerBackground = outerBackground.options;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nPitchfork.topLineEdgePoint = Pitchfork.outerLineEdgePoint(1);\nPitchfork.bottomLineEdgePoint = Pitchfork.outerLineEdgePoint(0);\nPitchfork.prototype.defaultOptions = Pitchfork_merge(Types_InfinityLine.prototype.defaultOptions, \n/**\n * A pitchfork annotation.\n *\n * @sample highcharts/annotations-advanced/pitchfork/\n *         Pitchfork\n *\n * @extends      annotations.infinityLine\n * @product      highstock\n * @optionparent annotations.pitchfork\n */\n{\n    typeOptions: {\n        /**\n         * Inner background options.\n         *\n         * @extends   annotations.crookedLine.shapeOptions\n         * @excluding height, r, type, width\n         */\n        innerBackground: {\n            fill: 'rgba(130, 170, 255, 0.4)',\n            strokeWidth: 0\n        },\n        /**\n         * Outer background options.\n         *\n         * @extends   annotations.crookedLine.shapeOptions\n         * @excluding height, r, type, width\n         */\n        outerBackground: {\n            fill: 'rgba(156, 229, 161, 0.4)',\n            strokeWidth: 0\n        }\n    }\n});\nAnnotations_Annotation.types.pitchfork = Pitchfork;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Pitchfork = ((/* unused pure expression or super */ null && (Pitchfork)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/VerticalLine.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { merge: VerticalLine_merge, pick: VerticalLine_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass VerticalLine extends Annotations_Annotation {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static connectorFirstPoint(target) {\n        const annotation = target.annotation, chart = annotation.chart, inverted = chart.inverted, point = annotation.points[0], left = VerticalLine_pick(point.series.yAxis && point.series.yAxis.left, 0), top = VerticalLine_pick(point.series.yAxis && point.series.yAxis.top, 0), offset = annotation.options.typeOptions.label.offset, y = Annotations_MockPoint.pointToPixels(point, true)[inverted ? 'x' : 'y'];\n        return {\n            x: point.x,\n            xAxis: point.series.xAxis,\n            y: y + offset +\n                (inverted ? (left - chart.plotLeft) : (top - chart.plotTop))\n        };\n    }\n    static connectorSecondPoint(target) {\n        const annotation = target.annotation, chart = annotation.chart, inverted = chart.inverted, typeOptions = annotation.options.typeOptions, point = annotation.points[0], left = VerticalLine_pick(point.series.yAxis && point.series.yAxis.left, 0), top = VerticalLine_pick(point.series.yAxis && point.series.yAxis.top, 0), y = Annotations_MockPoint.pointToPixels(point, true)[inverted ? 'x' : 'y'];\n        let yOffset = typeOptions.yOffset;\n        if (typeOptions.label.offset < 0) {\n            yOffset *= -1;\n        }\n        return {\n            x: point.x,\n            xAxis: point.series.xAxis,\n            y: y + yOffset +\n                (inverted ? (left - chart.plotLeft) : (top - chart.plotTop))\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getPointsOptions() {\n        return [this.options.typeOptions.point];\n    }\n    addShapes() {\n        const typeOptions = this.options.typeOptions, connector = this.initShape(VerticalLine_merge(typeOptions.connector, {\n            type: 'path',\n            points: [\n                VerticalLine.connectorFirstPoint,\n                VerticalLine.connectorSecondPoint\n            ],\n            className: 'highcharts-vertical-line'\n        }), 0);\n        typeOptions.connector = connector.options;\n        this.userOptions.typeOptions.point = typeOptions.point;\n    }\n    addLabels() {\n        const typeOptions = this.options.typeOptions, labelOptions = typeOptions.label;\n        let x = 0, y = labelOptions.offset, verticalAlign = labelOptions.offset < 0 ? 'bottom' : 'top', align = 'center';\n        if (this.chart.inverted) {\n            x = labelOptions.offset;\n            y = 0;\n            verticalAlign = 'middle';\n            align = labelOptions.offset < 0 ? 'right' : 'left';\n        }\n        const label = this.initLabel(VerticalLine_merge(labelOptions, {\n            verticalAlign: verticalAlign,\n            align: align,\n            x: x,\n            y: y\n        }));\n        typeOptions.label = label.options;\n    }\n}\nVerticalLine.prototype.defaultOptions = VerticalLine_merge(Annotations_Annotation.prototype.defaultOptions, \n/**\n * A vertical line annotation.\n *\n * @sample highcharts/annotations-advanced/vertical-line/\n *         Vertical line\n *\n * @extends      annotations.crookedLine\n * @excluding    labels, shapes, controlPointOptions\n * @product      highstock\n * @optionparent annotations.verticalLine\n */\n{\n    typeOptions: {\n        /**\n         * @ignore\n         */\n        yOffset: 10,\n        /**\n         * Label options.\n         *\n         * @extends annotations.crookedLine.labelOptions\n         */\n        label: {\n            offset: -40,\n            point: function (target) {\n                return target.annotation.points[0];\n            },\n            allowOverlap: true,\n            backgroundColor: 'none',\n            borderWidth: 0,\n            crop: true,\n            overflow: 'none',\n            shape: 'rect',\n            text: '{y:.2f}'\n        },\n        /**\n         * Connector options.\n         *\n         * @extends   annotations.crookedLine.shapeOptions\n         * @excluding height, r, type, width\n         */\n        connector: {\n            strokeWidth: 1,\n            markerEnd: 'arrow'\n        }\n    }\n});\nAnnotations_Annotation.types.verticalLine = VerticalLine;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_VerticalLine = ((/* unused pure expression or super */ null && (VerticalLine)));\n\n;// ./code/es-modules/Extensions/Annotations/Types/Measure.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { defined: Measure_defined, extend: Measure_extend, isNumber: Measure_isNumber, merge: Measure_merge, pick: Measure_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction average() {\n    let average = 0, pointsTotal = 0, pointsAmount = 0;\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (isPointWithinExtremes(point, ext) &&\n                    Measure_isNumber(point.y)) {\n                    pointsTotal += point.y;\n                    pointsAmount++;\n                }\n            });\n        }\n    });\n    if (pointsAmount > 0) {\n        average = pointsTotal / pointsAmount;\n    }\n    return average;\n}\n/**\n * @private\n */\nfunction isPointWithinExtremes(point, ext) {\n    return (!point.isNull &&\n        Measure_isNumber(point.y) &&\n        point.x > ext.xAxisMin &&\n        point.x <= ext.xAxisMax &&\n        point.y > ext.yAxisMin &&\n        point.y <= ext.yAxisMax);\n}\n/**\n * @private\n */\nfunction bins() {\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    let bins = 0;\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (isPointWithinExtremes(point, ext)) {\n                    bins++;\n                }\n            });\n        }\n    });\n    return bins;\n}\n/**\n * Default formatter of label's content\n * @private\n */\nfunction defaultFormatter() {\n    return 'Min: ' + this.min +\n        '<br>Max: ' + this.max +\n        '<br>Average: ' + this.average.toFixed(2) +\n        '<br>Bins: ' + this.bins;\n}\n/**\n * Set values for xAxisMin, xAxisMax, yAxisMin, yAxisMax, also\n * when chart is inverted\n * @private\n */\nfunction getExtremes(xAxisMin, xAxisMax, yAxisMin, yAxisMax) {\n    return {\n        xAxisMin: Math.min(xAxisMax, xAxisMin),\n        xAxisMax: Math.max(xAxisMax, xAxisMin),\n        yAxisMin: Math.min(yAxisMax, yAxisMin),\n        yAxisMax: Math.max(yAxisMax, yAxisMin)\n    };\n}\n/**\n * Set current xAxisMin, xAxisMax, yAxisMin, yAxisMax.\n * Calculations of measure values (min, max, average, bins).\n * @private\n * @param {Highcharts.Axis} axis\n *        X or y axis reference\n * @param {number} value\n *        Point's value (x or y)\n * @param {number} offset\n *        Amount of pixels\n */\nfunction getPointPos(axis, value, offset) {\n    return axis.toValue(axis.toPixels(value) + offset);\n}\n/**\n * Set starting points\n * @private\n */\nfunction Measure_init() {\n    const options = this.options.typeOptions, chart = this.chart, inverted = chart.inverted, xAxis = chart.xAxis[options.xAxis], yAxis = chart.yAxis[options.yAxis], bg = options.background, width = inverted ? bg.height : bg.width, height = inverted ? bg.width : bg.height, selectType = options.selectType, top = inverted ? xAxis.left : yAxis.top, // #13664\n    left = inverted ? yAxis.top : xAxis.left; // #13664\n    this.startXMin = options.point.x;\n    this.startYMin = options.point.y;\n    if (Measure_isNumber(width)) {\n        this.startXMax = this.startXMin + width;\n    }\n    else {\n        this.startXMax = getPointPos(xAxis, this.startXMin, parseFloat(width));\n    }\n    if (Measure_isNumber(height)) {\n        this.startYMax = this.startYMin - height;\n    }\n    else {\n        this.startYMax = getPointPos(yAxis, this.startYMin, parseFloat(height));\n    }\n    // X / y selection type\n    if (selectType === 'x') {\n        this.startYMin = yAxis.toValue(top);\n        this.startYMax = yAxis.toValue(top + yAxis.len);\n    }\n    else if (selectType === 'y') {\n        this.startXMin = xAxis.toValue(left);\n        this.startXMax = xAxis.toValue(left + xAxis.len);\n    }\n}\n/**\n * @private\n */\nfunction max() {\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    let max = -Infinity, isCalculated = false; // To avoid Infinity in formatter\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (Measure_isNumber(point.y) &&\n                    point.y > max &&\n                    isPointWithinExtremes(point, ext)) {\n                    max = point.y;\n                    isCalculated = true;\n                }\n            });\n        }\n    });\n    if (!isCalculated) {\n        max = 0;\n    }\n    return max;\n}\n/**\n * Definitions of calculations (min, max, average, bins)\n * @private\n */\nfunction min() {\n    const series = this.chart.series, ext = getExtremes(this.xAxisMin, this.xAxisMax, this.yAxisMin, this.yAxisMax);\n    let min = Infinity, isCalculated = false; // To avoid Infinity in formatter\n    series.forEach((s) => {\n        if (s.visible &&\n            s.options.id !== 'highcharts-navigator-series') {\n            s.points.forEach((point) => {\n                if (Measure_isNumber(point.y) &&\n                    point.y < min &&\n                    isPointWithinExtremes(point, ext)) {\n                    min = point.y;\n                    isCalculated = true;\n                }\n            });\n        }\n    });\n    if (!isCalculated) {\n        min = 0;\n    }\n    return min;\n}\n/**\n * Set current xAxisMin, xAxisMax, yAxisMin, yAxisMax.\n * Calculations of measure values (min, max, average, bins).\n * @private\n * @param {boolean} [resize]\n *        Flag if shape is resized.\n */\nfunction recalculate(resize) {\n    const options = this.options.typeOptions, xAxis = this.chart.xAxis[options.xAxis], yAxis = this.chart.yAxis[options.yAxis], offsetX = this.offsetX, offsetY = this.offsetY;\n    this.xAxisMin = getPointPos(xAxis, this.startXMin, offsetX);\n    this.xAxisMax = getPointPos(xAxis, this.startXMax, offsetX);\n    this.yAxisMin = getPointPos(yAxis, this.startYMin, offsetY);\n    this.yAxisMax = getPointPos(yAxis, this.startYMax, offsetY);\n    this.min = min.call(this);\n    this.max = max.call(this);\n    this.average = average.call(this);\n    this.bins = bins.call(this);\n    if (resize) {\n        this.resize(0, 0);\n    }\n}\n/**\n * Update position of start points\n * (startXMin, startXMax, startYMin, startYMax)\n * @private\n * @param {boolean} redraw\n *        Flag if shape is redraw\n * @param {boolean} resize\n *        Flag if shape is resized\n * @param {number} cpIndex\n *        Index of controlPoint\n */\nfunction updateStartPoints(redraw, resize, cpIndex, dx, dy) {\n    const options = this.options.typeOptions, selectType = options.selectType, xAxis = this.chart.xAxis[options.xAxis], yAxis = this.chart.yAxis[options.yAxis], startXMin = this.startXMin, startXMax = this.startXMax, startYMin = this.startYMin, startYMax = this.startYMax, offsetX = this.offsetX, offsetY = this.offsetY;\n    if (resize) {\n        if (selectType === 'x') {\n            if (cpIndex === 0) {\n                this.startXMin = getPointPos(xAxis, startXMin, dx);\n            }\n            else {\n                this.startXMax = getPointPos(xAxis, startXMax, dx);\n            }\n        }\n        else if (selectType === 'y') {\n            if (cpIndex === 0) {\n                this.startYMin = getPointPos(yAxis, startYMin, dy);\n            }\n            else {\n                this.startYMax = getPointPos(yAxis, startYMax, dy);\n            }\n        }\n        else {\n            this.startXMax = getPointPos(xAxis, startXMax, dx);\n            this.startYMax = getPointPos(yAxis, startYMax, dy);\n        }\n    }\n    if (redraw) {\n        this.startXMin = getPointPos(xAxis, startXMin, offsetX);\n        this.startXMax = getPointPos(xAxis, startXMax, offsetX);\n        this.startYMin = getPointPos(yAxis, startYMin, offsetY);\n        this.startYMax = getPointPos(yAxis, startYMax, offsetY);\n        this.offsetX = 0;\n        this.offsetY = 0;\n    }\n    this.options.typeOptions.point = {\n        x: this.startXMin,\n        y: this.startYMin\n    };\n    // We need to update userOptions as well as they are used in\n    // the Annotation.update() method to initialize the annotation, #19121.\n    this.userOptions.typeOptions.point = {\n        x: this.startXMin,\n        y: this.startYMin\n    };\n}\n/* *\n *\n *  Class\n *\n * */\nclass Measure extends Annotations_Annotation {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Init annotation object.\n     * @private\n     */\n    init(annotationOrChart, userOptions, index) {\n        super.init(annotationOrChart, userOptions, index);\n        this.offsetX = 0;\n        this.offsetY = 0;\n        this.resizeX = 0;\n        this.resizeY = 0;\n        Measure_init.call(this);\n        this.addValues();\n        this.addShapes();\n    }\n    /**\n     * Overrides default setter to get axes from typeOptions.\n     * @private\n     */\n    setClipAxes() {\n        this.clipXAxis = this.chart.xAxis[this.options.typeOptions.xAxis];\n        this.clipYAxis = this.chart.yAxis[this.options.typeOptions.yAxis];\n    }\n    /**\n     * Get points configuration objects for shapes.\n     * @private\n     */\n    shapePointsOptions() {\n        const options = this.options.typeOptions, xAxis = options.xAxis, yAxis = options.yAxis;\n        return [\n            {\n                x: this.xAxisMin,\n                y: this.yAxisMin,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                x: this.xAxisMax,\n                y: this.yAxisMin,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                x: this.xAxisMax,\n                y: this.yAxisMax,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                x: this.xAxisMin,\n                y: this.yAxisMax,\n                xAxis: xAxis,\n                yAxis: yAxis\n            },\n            {\n                command: 'Z'\n            }\n        ];\n    }\n    addControlPoints() {\n        const inverted = this.chart.inverted, options = this.options.controlPointOptions, selectType = this.options.typeOptions.selectType;\n        if (!Measure_defined(this.userOptions.controlPointOptions?.style?.cursor)) {\n            if (selectType === 'x') {\n                options.style.cursor = inverted ? 'ns-resize' : 'ew-resize';\n            }\n            else if (selectType === 'y') {\n                options.style.cursor = inverted ? 'ew-resize' : 'ns-resize';\n            }\n        }\n        let controlPoint = new Annotations_ControlPoint(this.chart, this, this.options.controlPointOptions, 0);\n        this.controlPoints.push(controlPoint);\n        // Add extra controlPoint for horizontal and vertical range\n        if (selectType !== 'xy') {\n            controlPoint = new Annotations_ControlPoint(this.chart, this, this.options.controlPointOptions, 1);\n            this.controlPoints.push(controlPoint);\n        }\n    }\n    /**\n     * Add label with calculated values (min, max, average, bins).\n     * @private\n     * @param {boolean} [resize]\n     * The flag for resize shape\n     */\n    addValues(resize) {\n        const typeOptions = this.options.typeOptions, formatter = typeOptions.label.formatter;\n        // Set xAxisMin, xAxisMax, yAxisMin, yAxisMax\n        recalculate.call(this, resize);\n        if (!typeOptions.label.enabled) {\n            return;\n        }\n        if (this.labels.length > 0) {\n            (this.labels[0]).text = ((formatter && formatter.call(this)) ||\n                defaultFormatter.call(this));\n        }\n        else {\n            this.initLabel(Measure_extend({\n                shape: 'rect',\n                backgroundColor: 'none',\n                color: 'black',\n                borderWidth: 0,\n                dashStyle: 'Dash',\n                overflow: 'allow',\n                align: 'left',\n                y: 0,\n                x: 0,\n                verticalAlign: 'top',\n                crop: true,\n                xAxis: 0,\n                yAxis: 0,\n                point: function (target) {\n                    const annotation = target.annotation, options = target.options;\n                    return {\n                        x: annotation.xAxisMin,\n                        y: annotation.yAxisMin,\n                        xAxis: Measure_pick(typeOptions.xAxis, options.xAxis),\n                        yAxis: Measure_pick(typeOptions.yAxis, options.yAxis)\n                    };\n                },\n                text: ((formatter && formatter.call(this)) ||\n                    defaultFormatter.call(this))\n            }, typeOptions.label), void 0);\n        }\n    }\n    /**\n     * Crosshair, background (rect).\n     * @private\n     */\n    addShapes() {\n        this.addCrosshairs();\n        this.addBackground();\n    }\n    /**\n     * Add background shape.\n     * @private\n     */\n    addBackground() {\n        const shapePoints = this.shapePointsOptions();\n        if (typeof shapePoints[0].x === 'undefined') {\n            return;\n        }\n        this.initShape(Measure_extend({\n            type: 'path',\n            points: shapePoints,\n            className: 'highcharts-measure-background'\n        }, this.options.typeOptions.background), 2);\n    }\n    /**\n     * Add internal crosshair shapes (on top and bottom).\n     * @private\n     */\n    addCrosshairs() {\n        const chart = this.chart, options = this.options.typeOptions, point = this.options.typeOptions.point, xAxis = chart.xAxis[options.xAxis], yAxis = chart.yAxis[options.yAxis], inverted = chart.inverted, defaultOptions = {\n            point: point,\n            type: 'path'\n        };\n        let xAxisMin = xAxis.toPixels(this.xAxisMin), xAxisMax = xAxis.toPixels(this.xAxisMax), yAxisMin = yAxis.toPixels(this.yAxisMin), yAxisMax = yAxis.toPixels(this.yAxisMax), pathH = [], pathV = [], crosshairOptionsX, crosshairOptionsY, temp;\n        if (inverted) {\n            temp = xAxisMin;\n            xAxisMin = yAxisMin;\n            yAxisMin = temp;\n            temp = xAxisMax;\n            xAxisMax = yAxisMax;\n            yAxisMax = temp;\n        }\n        // Horizontal line\n        if (options.crosshairX.enabled) {\n            pathH = [[\n                    'M',\n                    xAxisMin,\n                    yAxisMin + ((yAxisMax - yAxisMin) / 2)\n                ], [\n                    'L',\n                    xAxisMax,\n                    yAxisMin + ((yAxisMax - yAxisMin) / 2)\n                ]];\n        }\n        // Vertical line\n        if (options.crosshairY.enabled) {\n            pathV = [[\n                    'M',\n                    xAxisMin + ((xAxisMax - xAxisMin) / 2),\n                    yAxisMin\n                ], [\n                    'L',\n                    xAxisMin + ((xAxisMax - xAxisMin) / 2),\n                    yAxisMax\n                ]];\n        }\n        // Update existed crosshair\n        if (this.shapes.length > 0) {\n            this.shapes[0].options.d = pathH;\n            this.shapes[1].options.d = pathV;\n        }\n        else {\n            // Add new crosshairs\n            crosshairOptionsX = Measure_merge(defaultOptions, { className: 'highcharts-measure-crosshair-x' }, options.crosshairX);\n            crosshairOptionsY = Measure_merge(defaultOptions, { className: 'highcharts-measure-crosshair-y' }, options.crosshairY);\n            this.initShape(Measure_extend({ d: pathH }, crosshairOptionsX), 0);\n            this.initShape(Measure_extend({ d: pathV }, crosshairOptionsY), 1);\n        }\n    }\n    onDrag(e) {\n        const translation = this.mouseMoveToTranslation(e), selectType = this.options.typeOptions.selectType, x = selectType === 'y' ? 0 : translation.x, y = selectType === 'x' ? 0 : translation.y;\n        this.translate(x, y);\n        this.offsetX += x;\n        this.offsetY += y;\n        // Animation, resize, setStartPoints\n        this.redraw(false, false, true);\n    }\n    /**\n     * Translate start or end (\"left\" or \"right\") side of the measure.\n     * Update start points (startXMin, startXMax, startYMin, startYMax)\n     * @private\n     * @param {number} dx\n     * the amount of x translation\n     * @param {number} dy\n     * the amount of y translation\n     * @param {number} cpIndex\n     * index of control point\n     * @param {Highcharts.AnnotationDraggableValue} selectType\n     * x / y / xy\n     */\n    resize(dx, dy, cpIndex, selectType) {\n        // Background shape\n        const bckShape = this.shapes[2];\n        if (selectType === 'x') {\n            if (cpIndex === 0) {\n                bckShape.translatePoint(dx, 0, 0);\n                bckShape.translatePoint(dx, dy, 3);\n            }\n            else {\n                bckShape.translatePoint(dx, 0, 1);\n                bckShape.translatePoint(dx, dy, 2);\n            }\n        }\n        else if (selectType === 'y') {\n            if (cpIndex === 0) {\n                bckShape.translatePoint(0, dy, 0);\n                bckShape.translatePoint(0, dy, 1);\n            }\n            else {\n                bckShape.translatePoint(0, dy, 2);\n                bckShape.translatePoint(0, dy, 3);\n            }\n        }\n        else {\n            bckShape.translatePoint(dx, 0, 1);\n            bckShape.translatePoint(dx, dy, 2);\n            bckShape.translatePoint(0, dy, 3);\n        }\n        updateStartPoints.call(this, false, true, cpIndex, dx, dy);\n        this.options.typeOptions.background.height = Math.abs(this.startYMax - this.startYMin);\n        this.options.typeOptions.background.width = Math.abs(this.startXMax - this.startXMin);\n    }\n    /**\n     * Redraw event which render elements and update start points if needed.\n     * @private\n     * @param {boolean} animation\n     * @param {boolean} [resize]\n     * flag if resized\n     * @param {boolean} [setStartPoints]\n     * update position of start points\n     */\n    redraw(animation, resize, setStartPoints) {\n        this.linkPoints();\n        if (!this.graphic) {\n            this.render();\n        }\n        if (setStartPoints) {\n            updateStartPoints.call(this, true, false);\n        }\n        // #11174 - clipBox was not recalculate during resize / redraw\n        if (this.clipRect) {\n            this.clipRect.animate(this.getClipBox());\n        }\n        this.addValues(resize);\n        this.addCrosshairs();\n        this.redrawItems(this.shapes, animation);\n        this.redrawItems(this.labels, animation);\n        const backgroundOptions = this.options.typeOptions.background;\n        if (backgroundOptions?.strokeWidth &&\n            this.shapes[2]?.graphic) {\n            const offset = (backgroundOptions.strokeWidth) / 2;\n            const background = this.shapes[2];\n            const path = background.graphic.pathArray;\n            const p1 = path[0];\n            const p2 = path[1];\n            const p3 = path[2];\n            const p4 = path[3];\n            p1[1] = (p1[1] || 0) + offset;\n            p2[1] = (p2[1] || 0) - offset;\n            p3[1] = (p3[1] || 0) - offset;\n            p4[1] = (p4[1] || 0) + offset;\n            p1[2] = (p1[2] || 0) + offset;\n            p2[2] = (p2[2] || 0) + offset;\n            p3[2] = (p3[2] || 0) - offset;\n            p4[2] = (p4[2] || 0) - offset;\n            background.graphic.attr({\n                d: path\n            });\n        }\n        // Redraw control point to run positioner\n        this.controlPoints.forEach((controlPoint) => controlPoint.redraw());\n    }\n    translate(dx, dy) {\n        this.shapes.forEach((item) => item.translate(dx, dy));\n    }\n}\nMeasure.prototype.defaultOptions = Measure_merge(Annotations_Annotation.prototype.defaultOptions, \n/**\n * A measure annotation.\n *\n * @extends annotations.crookedLine\n * @excluding labels, labelOptions, shapes, shapeOptions\n * @sample highcharts/annotations-advanced/measure/\n *         Measure\n * @product highstock\n * @optionparent annotations.measure\n */\n{\n    typeOptions: {\n        /**\n         * Decides in what dimensions the user can resize by dragging the\n         * mouse. Can be one of x, y or xy.\n         */\n        selectType: 'xy',\n        /**\n         * This number defines which xAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the xAxis array.\n         */\n        xAxis: 0,\n        /**\n         * This number defines which yAxis the point is connected to.\n         * It refers to either the axis id or the index of the axis\n         * in the yAxis array.\n         */\n        yAxis: 0,\n        background: {\n            /**\n             * The color of the rectangle.\n             */\n            fill: 'rgba(130, 170, 255, 0.4)',\n            /**\n             * The width of border.\n             */\n            strokeWidth: 0,\n            /**\n             * The color of border.\n             */\n            stroke: void 0\n        },\n        /**\n         * Configure a crosshair that is horizontally placed in middle of\n         * rectangle.\n         *\n         */\n        crosshairX: {\n            /**\n             * Enable or disable the horizontal crosshair.\n             *\n             */\n            enabled: true,\n            /**\n             * The Z index of the crosshair in annotation.\n             */\n            zIndex: 6,\n            /**\n             * The dash or dot style of the crosshair's line. For possible\n             * values, see\n             * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n             *\n             * @type    {Highcharts.DashStyleValue}\n             * @default Dash\n             */\n            dashStyle: 'Dash',\n            /**\n             * The marker-end defines the arrowhead that will be drawn\n             * at the final vertex of the given crosshair's path.\n             *\n             * @type       {string}\n             * @default    arrow\n             */\n            markerEnd: 'arrow'\n        },\n        /**\n         * Configure a crosshair that is vertically placed in middle of\n         * rectangle.\n         */\n        crosshairY: {\n            /**\n             * Enable or disable the vertical crosshair.\n             *\n             */\n            enabled: true,\n            /**\n             * The Z index of the crosshair in annotation.\n             */\n            zIndex: 6,\n            /**\n             * The dash or dot style of the crosshair's line. For possible\n             * values, see\n             * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n             *\n             * @type      {Highcharts.DashStyleValue}\n             * @default   Dash\n             * @apioption annotations.measure.typeOptions.crosshairY.dashStyle\n             *\n             */\n            dashStyle: 'Dash',\n            /**\n             * The marker-end defines the arrowhead that will be drawn\n             * at the final vertex of the given crosshair's path.\n             *\n             * @type       {string}\n             * @default    arrow\n             * @validvalue [\"none\", \"arrow\"]\n             *\n             */\n            markerEnd: 'arrow'\n        },\n        label: {\n            /**\n             * Enable or disable the label text (min, max, average,\n             * bins values).\n             *\n             * Defaults to true.\n             */\n            enabled: true,\n            /**\n             * CSS styles for the measure label.\n             *\n             * @type    {Highcharts.CSSObject}\n             * @default {\"color\": \"#666666\", \"fontSize\": \"11px\"}\n             */\n            style: {\n                fontSize: '0.7em',\n                color: \"#666666\" /* Palette.neutralColor60 */\n            },\n            /**\n             * Formatter function for the label text.\n             *\n             * Available data are:\n             *\n             * <table>\n             *\n             * <tbody>\n             *\n             * <tr>\n             *\n             * <td>`this.min`</td>\n             *\n             * <td>The minimum value of the points in the selected\n             * range.</td>\n             *\n             * </tr>\n             *\n             * <tr>\n             *\n             * <td>`this.max`</td>\n             *\n             * <td>The maximum value of the points in the selected\n             * range.</td>\n             *\n             * </tr>\n             *\n             * <tr>\n             *\n             * <td>`this.average`</td>\n             *\n             * <td>The average value of the points in the selected\n             * range.</td>\n             *\n             * </tr>\n             *\n             * <tr>\n             *\n             * <td>`this.bins`</td>\n             *\n             * <td>The amount of the points in the selected range.</td>\n             *\n             * </tr>\n             *\n             * </table>\n             *\n             * @type {Function}\n             *\n             */\n            formatter: void 0\n        }\n    },\n    controlPointOptions: {\n        positioner: function (target) {\n            const cpIndex = this.index, chart = target.chart, options = target.options, typeOptions = options.typeOptions, selectType = typeOptions.selectType, controlPointOptions = options.controlPointOptions, inverted = chart.inverted, xAxis = chart.xAxis[typeOptions.xAxis], yAxis = chart.yAxis[typeOptions.yAxis], ext = getExtremes(target.xAxisMin, target.xAxisMax, target.yAxisMin, target.yAxisMax);\n            let targetX = target.xAxisMax, targetY = target.yAxisMax, x, y;\n            if (selectType === 'x') {\n                targetY = (ext.yAxisMax + ext.yAxisMin) / 2;\n                // First control point\n                if (cpIndex === 0) {\n                    targetX = target.xAxisMin;\n                }\n            }\n            if (selectType === 'y') {\n                targetX = ext.xAxisMin +\n                    ((ext.xAxisMax - ext.xAxisMin) / 2);\n                // First control point\n                if (cpIndex === 0) {\n                    targetY = target.yAxisMin;\n                }\n            }\n            if (inverted) {\n                x = yAxis.toPixels(targetY);\n                y = xAxis.toPixels(targetX);\n            }\n            else {\n                x = xAxis.toPixels(targetX);\n                y = yAxis.toPixels(targetY);\n            }\n            return {\n                x: x - (controlPointOptions.width / 2),\n                y: y - (controlPointOptions.height / 2)\n            };\n        },\n        events: {\n            drag: function (e, target) {\n                const translation = this.mouseMoveToTranslation(e), selectType = target.options.typeOptions.selectType, index = this.index, x = selectType === 'y' ? 0 : translation.x, y = selectType === 'x' ? 0 : translation.y;\n                target.resize(x, y, index, selectType);\n                target.resizeX += x;\n                target.resizeY += y;\n                target.redraw(false, true);\n            }\n        }\n    }\n});\nAnnotations_Annotation.types.measure = Measure;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Types_Measure = ((/* unused pure expression or super */ null && (Measure)));\n\n;// ./code/es-modules/masters/modules/annotations-advanced.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const annotations_advanced_src = ((external_highcharts_src_js_default_default()));\n\nexport { annotations_advanced_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "DropdownProperties", "AnnotationChart", "ControlTarget", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "addEvent", "erase", "find", "fireEvent", "pick", "wrap", "chartAddAnnotation", "userOptions", "redraw", "annotation", "initAnnotation", "options", "annotations", "push", "graphic", "attr", "opacity", "chartCallback", "chart", "plotBoxClip", "renderer", "clipRect", "plotBox", "controlPointsGroup", "g", "zIndex", "clip", "add", "for<PERSON>ach", "annotationOptions", "i", "some", "drawAnnotations", "destroy", "event", "csvColumnHeaderFormatter", "exporting", "csv", "columnHeaderFormatter", "multiLevelHeaders", "dataRows", "xValues", "annotationHeader", "lang", "exportData", "startRowLength", "length", "annotationSeparator", "itemDelimiter", "joinAnnotations", "join", "labelOptions", "includeInDataExport", "labels", "label", "text", "annotationText", "points", "annotationX", "x", "xAxisIndex", "series", "xAxis", "index", "wasAdded", "newRow", "Array", "row", "maxRowLen", "Math", "max", "newRows", "header", "s", "columnTitle", "topLevelColumnTitle", "chartDrawAnnotations", "animate", "animationConfig", "chartRemoveAnnotation", "idOrAnnotation", "coll", "id", "onChartAfterInit", "wrapPointerOnContainerMouseDown", "proceed", "hasDraggedAnnotation", "apply", "slice", "arguments", "compose", "AnnotationClass", "ChartClass", "PointerClass", "chartProto", "addAnnotation", "pointer<PERSON><PERSON><PERSON>", "callbacks", "collectionsWithInit", "collectionsWithUpdate", "removeAnnotation", "types", "type", "Annotations_AnnotationChart", "defined", "doc", "isTouchDevice", "EventEmitter_addEvent", "EventEmitter_fireEvent", "objectEach", "EventEmitter_pick", "removeEvent", "Annotations_EventEmitter", "addEvents", "emitter", "addMouseDownEvent", "element", "e", "onMouseDown", "passive", "useHTML", "foreignObject", "events", "<PERSON><PERSON><PERSON><PERSON>", "cancelClick", "pointer", "normalize", "target", "nonDOMEvents", "indexOf", "div", "draggable", "onDrag", "styledMode", "cssPointer", "cursor", "y", "xy", "css", "isUpdating", "removeDocEvents", "hcEvents", "mouseMoveToRadians", "cx", "cy", "prevDy", "prevChartY", "prevDx", "prevChartX", "dy", "chartY", "dx", "chartX", "temp", "inverted", "atan2", "mouseMoveToScale", "sx", "sy", "mouseMoveToTranslation", "isInsidePlot", "plotLeft", "plotTop", "visiblePlotOnly", "translation", "translate", "shapes", "shape", "preventDefault", "button", "firesTouchEvents", "sourceCapabilities", "removeDrag", "hasDragged", "removeMouseUp", "onMouseUp", "merge", "ControlPoint_pick", "Annotations_ControlPoint", "constructor", "animation", "positioner", "render", "symbol", "width", "height", "style", "setVisibility", "visible", "update", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "seriesProto", "MockPoint_defined", "MockPoint_fireEvent", "MockPoint", "fromPoint", "point", "yAxis", "pointToPixels", "paneCoordinates", "plotX", "plotY", "mock", "plot<PERSON>id<PERSON>", "plotHeight", "getPlotBox", "translateX", "translateY", "pointToOptions", "applyOptions", "getOptions", "command", "setAxis", "refresh", "hasDynamicOptions", "len", "toPixels", "isInside", "refreshOptions", "toValue", "rotate", "radians", "cos", "sin", "tx", "ty", "scale", "xOrY", "axisName", "axisOptions", "toAnchor", "anchor", "_cx", "_cy", "addControlPoints", "controlPoints", "controlPointsOptions", "controlPointOptions", "box", "tooltip", "getAnchor", "relativePosition", "absolutePosition", "destroyControlTarget", "controlPoint", "getPointsOptions", "splat", "linkPoints", "pointsOptions", "pointOptions", "isObject", "isString", "pointConfig", "redrawControlPoints", "renderControlPoints", "transform", "transformation", "p1", "p2", "_point", "transformPoint", "Annotations_MockPoint", "translatePoint", "ControlTargetClass", "controlProto", "Annotations_ControlTarget", "Controllable_merge", "Controllable", "itemType", "collection", "init", "_args", "attrsFromOptions", "<PERSON><PERSON><PERSON>", "map", "attrsMap", "attrs", "tracker", "_parentGroup", "className", "addClass", "setControlPointsVisibility", "shouldBeDrawn", "translateShape", "translateSecondPoint", "shapeOptions", "annotationIndex", "chartOptions", "newOptions", "parentGroup", "<PERSON><PERSON><PERSON><PERSON>", "Controllables_Controllable", "defaultMarkers", "ControllablePath_defaultMarkers", "arrow", "tagName", "attributes", "refY", "refX", "marker<PERSON>id<PERSON>", "markerHeight", "children", "ControllablePath_addEvent", "ControllablePath_defined", "extend", "ControllablePath_merge", "<PERSON><PERSON><PERSON>", "markerEndSetter", "createMarkerSetter", "markerStartSetter", "TRACKER_FILL", "svg", "markerType", "value", "onChartAfterGetContainer", "defs", "svgRendererAddMarker", "markerOptions", "stroke", "color", "fill", "child", "ast", "orient", "marker", "ControllablePath", "SVGRendererClass", "svgRendererProto", "add<PERSON><PERSON><PERSON>", "toD", "dOption", "showPath", "position", "pointIndex", "crispLine", "strokeWidth", "parent", "path", "snap", "setMarkers", "action", "placed", "item", "itemOptions", "def", "predefined<PERSON>ark<PERSON>", "markerId", "getAttribute", "dashStyle", "ControllableRect_merge", "ControllableRect", "rect", "Boolean", "Controllables_ControllablePath", "ControllableCircle_merge", "ControllableCircle", "r", "circle", "setRadius", "ControllableEllipse_merge", "ControllableEllipse_defined", "ControllableEllipse", "createElement", "getDistanceFromLine", "point1", "point2", "x0", "y0", "abs", "sqrt", "getAttrs", "position2", "x1", "y1", "x2", "y2", "rx", "angle", "atan", "PI", "ry", "getRY", "getYAxis", "yAxisIndex", "getAbsolutePosition", "rotation", "rotationOriginX", "rotationOriginY", "setYRadius", "external_highcharts_src_js_default_Templating_namespaceObject", "Templating", "format", "external_highcharts_src_js_default_Templating_default", "ControllableLabel_extend", "getAlignFactor", "isNumber", "ControllableLabel_pick", "symbolConnector", "w", "h", "anchorX", "anchorY", "yOffset", "lateral", "ControllableLabel", "alignedPosition", "alignOptions", "round", "align", "verticalAlign", "symbols", "connector", "justifiedOptions", "alignAttr", "off", "padding", "bBox", "getBBox", "chartAnnotations", "getContrast", "shapesWithoutBackground", "backgroundColor", "shadow", "labelrank", "String", "formatter", "anchorAbsolutePosition", "anchorRelativePosition", "itemPosition", "alignTo", "itemPosRelativeX", "itemPosRelativeY", "showItem", "distance", "getPosition", "getPlayingField", "negative", "ttBelow", "overflow", "crop", "borderColor", "borderWidth", "borderRadius", "ControllableImage", "image", "src", "Controllables_ControllableLabel", "external_highcharts_src_js_default_AST_namespaceObject", "AST", "external_highcharts_src_js_default_AST_default", "BaseForm_addEvent", "Shared_BaseForm", "parentDiv", "iconsURL", "container", "createPopupContainer", "closeButton", "addCloseButton", "popup", "match", "eventName", "closeButtonEvents", "bind", "document", "code", "closePopup", "showPopup", "toolbarClass", "popupDiv", "popupClose<PERSON><PERSON>on", "innerHTML", "emptyHTML", "classList", "remove", "removeAttribute", "append<PERSON><PERSON><PERSON>", "display", "PopupAnnotations_doc", "isFirefox", "PopupAnnotations_createElement", "isArray", "PopupAnnotations_objectEach", "PopupAnnotations_pick", "stableSort", "addFormFields", "parentNode", "storage", "isRoot", "parentFullName", "<PERSON><PERSON><PERSON>", "addInput", "option", "reverse", "genInput", "createTextNode", "splice", "PopupIndicators_doc", "seriesTypes", "PopupIndicators_addEvent", "PopupIndicators_createElement", "PopupIndicators_defined", "PopupIndicators_isArray", "PopupIndicators_isObject", "PopupIndicators_objectEach", "PopupIndicators_stableSort", "dropdownParameters", "addColsContainer", "lhsCol", "rhsCol", "PopupIndicators_addFormFields", "seriesType", "rhsColWrapper", "fields", "params", "getNameType", "indicatorFullName", "name", "listAllSeries", "linkedParent", "volumeSeriesID", "addParamInputs", "addIndicatorList", "listType", "filter", "selectIndicator", "indicatorType", "isEdit", "setAttribute", "querySelectorAll", "plotOptions", "filteredSeriesArray", "filterSeriesArray", "filterSeries", "b", "seriesAName", "toLowerCase", "seriesBName", "indicatorList", "seriesSet", "btn", "textContent", "setElementHTML", "noFilterMatch", "fieldName", "selectBox", "addSelection", "addSelectionOptions", "addSearchBox", "clearFilterText", "clearFilter", "inputWrapper", "handleInputChange", "inputText", "input", "htmlFor", "labelClassName", "optionName", "optionParamList", "split", "labelText", "selectName", "parameterName", "selectedOption", "currentSeries", "seriesOptions", "seriesName", "parameterOption", "filteredSeries", "indicatorAliases", "navigation", "regex", "RegExp", "replace", "alias", "is", "nameBase", "toUpperCase", "PopupTabs_doc", "PopupTabs_addEvent", "PopupTabs_createElement", "addContentItem", "addMenuItem", "tabName", "disableTab", "menuItem", "deselectAll", "tabs", "tabsContent", "selectTab", "tab", "allTabs", "switchTabs", "Popup_doc", "Popup_addEvent", "Popup_createElement", "Popup_extend", "Popup_fireEvent", "Popup_pick", "Popup", "activeAnnotation", "navigationBindings", "unbind", "setTimeout", "inputAttributes", "inputName", "selectedButtonElement", "addButton", "fieldsDiv", "callback", "getFields", "inputList", "selectList", "linkedTo", "volumeTo", "fieldsOutput", "actionType", "param", "seriesId", "select", "parameter", "showForm", "indicators", "addForm", "addToolbar", "offsetHeight", "isInit", "lang<PERSON><PERSON>", "bottomRow", "saveButton", "top", "edit<PERSON><PERSON><PERSON>", "removeButton", "_options", "buttonParentDiv", "tabsContainers", "getAmount", "counter", "serie", "indicatorsCount", "firstTab", "composed", "PopupComposition_addEvent", "pushUnique", "PopupComposition_wrap", "onNavigationBindingsClosePopup", "onNavigationBindingsShowPopup", "config", "stockTools", "gui", "formType", "onSubmit", "wrapPointerOnContainerMouserDown", "inClass", "NagivationBindingsClass", "getDeferredAnimation", "destroyObjectProperties", "Annotation_erase", "Annotation_fireEvent", "Annotation_merge", "Annotation_pick", "getLabelsAndShapesOptions", "baseOptions", "mergedOptions", "someBaseOptions", "newOptionsValue", "basicOptions", "Annotation", "NavigationBindingsClass", "Popup_PopupComposition", "defaultOptions", "labelsAndShapes", "addClipPaths", "setClipAxes", "clipXAxis", "clipYAxis", "getClipBox", "addLabels", "labelsOptions", "initLabel", "addShapes", "initShape", "destroyItem", "labelCollectors", "labelCollector", "left", "initProperties", "setOptions", "_annotation<PERSON>r<PERSON>hart", "_userOptions", "animOptions", "set<PERSON>abelCollector", "shapesMap", "redrawItems", "redrawItem", "renderItem", "adjustVisibility", "hasVisiblePoints", "visibility", "show", "hide", "items", "shapesGroup", "labelsGroup", "renderItems", "xAxes", "yAxes", "linkedAxes", "concat", "reduce", "axes", "labelOrShape", "setItemControlPointsVisibility", "allowOverlap", "userOptionsIndex", "ControlPoint", "fontSize", "fontWeight", "Annotations_Annotation", "BasicAnnotation_merge", "BasicAnnotation", "basicControlPoints", "annotationType", "basicType", "optionsGroup", "group", "drag", "rectangle", "coords", "getCoordinates", "ellipse", "newR", "newRY", "basicAnnotation", "CrookedLine_merge", "Crooked<PERSON>ine", "typeOptions", "getControlPointsOptions", "line", "crookedLine", "Types_CrookedLine", "ElliottWave_merge", "Elliott<PERSON><PERSON>", "<PERSON><PERSON>tt<PERSON>ave", "Tunnel_merge", "Tunnel", "heightPointOptions", "logarithmic", "y3", "heightControlPoint", "addLine", "addBackground", "background", "translateSide", "end", "topIndex", "Number", "translateHeight", "dh", "startXY", "endXY", "getSecondCoordinate", "tunnel", "Types_Tunnel", "InfinityLine_merge", "InfinityLine", "edgePoint", "startIndex", "endIndex", "findEdgePoint", "findEdgeCoordinate", "firstPoint", "secondPoint", "edgePointFirstCoordinate", "xOrYOpposite", "edgePointX", "edgePointY", "swap", "firstPointPixels", "secondPointPixels", "deltaX", "deltaY", "xAxisMin", "xAxisMax", "yAxisMin", "yAxisMax", "xLimit", "yLimit", "endEdgePoint", "startEdgePoint", "infinityLine", "Types_InfinityLine", "TimeCycles_merge", "TimeCycles_isNumber", "TimeCycles_defined", "TimeCycles", "set<PERSON>ath", "<PERSON><PERSON><PERSON>", "startX", "getCircle<PERSON>ath", "pixelInterval", "numberOfCircles", "setPathProperties", "xAxisNumber", "yAxisNumber", "xValue1", "yValue", "xValue2", "xAxisLength", "floor", "pixelShift", "timeCycles", "Fibonacci_merge", "createPathDGenerator", "retracementIndex", "isBackground", "startRetracements", "endRetracements", "leftTop", "rightTop", "rightBottom", "leftBottom", "<PERSON><PERSON><PERSON><PERSON>", "linkRetracementsPoints", "startDiff", "endDiff", "endX", "levels", "level", "startRetracement", "endRetracement", "reversed", "linkRetracementPoint", "retracements", "_level", "backgroundColors", "lineColor", "lineColors", "toString", "<PERSON><PERSON><PERSON><PERSON>", "FibonacciTimeZones_merge", "fibonacciIndex", "plotLeftOrTop", "FibonacciTimeZones", "fibb", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "secondLineEdgePoints", "edgePoints", "args", "firstEdgePointY", "secondEdgePointY", "fibonacciTimeZones", "Pitchfork_merge", "Pitchfork", "outerLineEdgePoint", "firstPointIndex", "midPointOptions", "firstAnglePoint", "secondAnglePoint", "middleLineEdgePoint", "addLines", "addBackgrounds", "topLineEdgePoint", "bottomLineEdgePoint", "innerBackground", "outerBackground", "pitchfork", "VerticalLine_merge", "VerticalLine_pick", "VerticalLine", "connectorFirstPoint", "offset", "connectorSecondPoint", "markerEnd", "verticalLine", "Measure_defined", "Measure_extend", "Measure_isNumber", "Measure_merge", "Measure_pick", "average", "pointsTotal", "pointsAmount", "ext", "getExtremes", "isPointWithinExtremes", "isNull", "bins", "defaultFormatter", "min", "toFixed", "getPointPos", "axis", "Measure_init", "bg", "selectType", "startXMin", "startYMin", "startXMax", "parseFloat", "startYMax", "Infinity", "isCalculated", "recalculate", "resize", "offsetX", "offsetY", "updateStartPoints", "cpIndex", "Measure", "annotation<PERSON>r<PERSON>hart", "resizeX", "resizeY", "addValues", "shapePointsOptions", "enabled", "addCrosshairs", "shapePoints", "pathH", "pathV", "crosshairOptionsX", "crosshairOptionsY", "crosshairX", "crosshairY", "bckShape", "setStartPoints", "backgroundOptions", "pathArray", "p3", "p4", "targetX", "targetY", "measure", "annotations_advanced_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,AAChG,OAA0E,sBAAuB,CAExF,IAu4HEC,EAzmHPC,EAkiDAC,EAskEAF,EAt4HSG,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDxB,EAAwD,OAAU,CAC7H,IAAIyB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAIgD,CAAG,GAa7D,GAAM,CAAEiB,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAEC,UAAAA,CAAS,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAIN,IAsB1D,SAASO,EAAmBC,CAAW,CAAEC,CAAM,EAC3C,IAAMC,EAAa,IAAI,CAACC,cAAc,CAACH,GAQvC,OAPA,IAAI,CAACI,OAAO,CAACC,WAAW,CAACC,IAAI,CAACJ,EAAWE,OAAO,EAC5CP,EAAKI,EAAQ,CAAA,KACbC,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAACC,IAAI,CAAC,CACpBC,QAAS,CACb,IAEGP,CACX,CAIA,SAASQ,IACL,IAAMC,EAAQ,IAAI,AAClBA,CAAAA,EAAMC,WAAW,CAAG,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACC,OAAO,EACvDJ,EAAMK,kBAAkB,CAAGL,EAAME,QAAQ,CACpCI,CAAC,CAAC,kBACFT,IAAI,CAAC,CAAEU,OAAQ,EAAG,GAClBC,IAAI,CAACR,EAAMC,WAAW,EACtBQ,GAAG,GACRT,EAAMP,OAAO,CAACC,WAAW,CAACgB,OAAO,CAAC,CAACC,EAAmBC,KAClD,GAEA,CAACZ,EAAMN,WAAW,CAACmB,IAAI,CAAC,AAACtB,GAAeA,EAAWE,OAAO,GAAKkB,GAAoB,CAC/E,IAAMpB,EAAaS,EAAMR,cAAc,CAACmB,EACxCX,CAAAA,EAAMP,OAAO,CAACC,WAAW,CAACkB,EAAE,CAAGrB,EAAWE,OAAO,AACrD,CACJ,GACAO,EAAMc,eAAe,GACrBhC,EAASkB,EAAO,SAAUA,EAAMc,eAAe,EAC/ChC,EAASkB,EAAO,UAAW,WACvBA,EAAMC,WAAW,CAACc,OAAO,GACzBf,EAAMK,kBAAkB,CAACU,OAAO,EACpC,GACAjC,EAASkB,EAAO,aAAc,SAAUgB,CAAK,EACzC,IAAMtB,EAAcM,EAAMN,WAAW,CAAEuB,EAA2B,AAAC,CAAA,AAAC,IAAI,CAACxB,OAAO,CAACyB,SAAS,EACtF,IAAI,CAACzB,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC1B,CAAC,CAAA,EAAGC,qBAAqB,CAG7BC,EAAoB,CAACL,EAAMM,QAAQ,CAAC,EAAE,CAACC,OAAO,CAAEC,EAAoBxB,EAAMP,OAAO,CAACgC,IAAI,EAClFzB,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,EAC7B1B,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,CAACF,gBAAgB,CAgB/CG,EAAiBX,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEC,EAAuB7B,EAAMP,OAAO,CAACyB,SAAS,EACxFlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACoC,aAAa,CAAGC,EAAmB/B,EAAMP,OAAO,CAACyB,SAAS,EAClGlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACsC,IAAI,CAChDtC,EAAYgB,OAAO,CAAC,AAACnB,IACbA,EAAWE,OAAO,CAACwC,YAAY,EAC/B1C,EAAWE,OAAO,CAACwC,YAAY,CAACC,mBAAmB,EACnD3C,EAAW4C,MAAM,CAACzB,OAAO,CAAC,AAAC0B,IACvB,GAAIA,EAAM3C,OAAO,CAAC4C,IAAI,CAAE,CACpB,IAAMC,EAAiBF,EAAM3C,OAAO,CAAC4C,IAAI,CACzCD,EAAMG,MAAM,CAAC7B,OAAO,CAAC,AAAC6B,IAClB,IAAMC,EAAcD,EAAOE,CAAC,CAAEC,EAAaH,EAAOI,MAAM,CAACC,KAAK,CAC1DL,EAAOI,MAAM,CAACC,KAAK,CAACC,KAAK,CACzB,GACAC,EAAW,CAAA,EAGf,GAAIJ,AAAe,KAAfA,EAAmB,CACnB,IAAMjF,EAAIuD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEmB,EAAS,AAAIC,MAAMvF,GACvD,IAAK,IAAImD,EAAI,EAAGA,EAAInD,EAAG,EAAEmD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,GAEhBmC,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACnBwB,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,EAC7BxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,GACpBD,EAAW,CAAA,CACf,CAuBA,GApBKA,GACD9B,EAAMM,QAAQ,CAACZ,OAAO,CAAC,AAACuC,IAChB,CAACH,GACDG,EAAI1B,OAAO,EACXmB,AAAe,KAAK,IAApBA,GACAF,IAAgBS,EAAI1B,OAAO,CAACmB,EAAW,GACnCX,GACAkB,EAAIrB,MAAM,CAAGD,EACbsB,CAAG,CAACA,EAAIrB,MAAM,CAAG,EAAE,EAAKC,EACpBS,EAGJW,EAAItD,IAAI,CAAC2C,GAEbQ,EAAW,CAAA,EAEnB,GAIA,CAACA,EAAU,CACX,IAAMrF,EAAIuD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEmB,EAAS,AAAIC,MAAMvF,GACvD,IAAK,IAAImD,EAAI,EAAGA,EAAInD,EAAG,EAAEmD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,EAEhBmC,CAAAA,CAAM,CAAC,EAAE,CAAGP,EACZO,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACA,KAAK,IAApBmB,GACAK,CAAAA,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,CAAU,EAE3CxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,EACxB,CACJ,EACJ,CACJ,EAER,GACA,IAAIG,EAAY,EAChBlC,EAAMM,QAAQ,CAACZ,OAAO,CAAC,AAACuC,IACpBC,EAAYC,KAAKC,GAAG,CAACF,EAAWD,EAAIrB,MAAM,CAC9C,GACA,IAAMyB,EAAUH,EAAYlC,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CACpD,IAAK,IAAIhB,EAAI,EAAGA,EAAIyC,EAASzC,IAAK,CAC9B,IAAM0C,EAASlC,AA7F0D,SAAUyB,CAAK,EACxF,IAAIU,SACJ,AAAItC,GAEIsC,AAAM,CAAA,IADVA,CAAAA,EAAItC,EAAyB4B,EAAK,EAEvBU,GAGfA,EAAI/B,EAAmB,IAAMqB,EACzBxB,GACO,CACHmC,YAAaD,EACbE,oBAAqBF,CACzB,EAEGA,CACX,EA6EyC3C,EAAI,GACrCS,GACAL,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOG,mBAAmB,EACjDzC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOE,WAAW,GAGzCxC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAE/B,CACJ,EACJ,CAIA,SAASI,IACL,IAAI,CAACzD,WAAW,CAACJ,IAAI,CAAC,IAAI,CAACO,OAAO,EAClC,IAAI,CAACV,WAAW,CAACgB,OAAO,CAAC,AAACnB,IACtBA,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAAC+D,OAAO,CAAC,CACvB7D,QAAS,CACb,EAAGP,EAAWqE,eAAe,CACjC,EACJ,CASA,SAASC,EAAsBC,CAAc,EACzC,IAAMpE,EAAc,IAAI,CAACA,WAAW,CAAEH,EAAa,AAACuE,AAAwB,gBAAxBA,EAAeC,IAAI,CACnED,EACA9E,EAAKU,EAAa,SAAUH,CAAU,EAClC,OAAOA,EAAWE,OAAO,CAACuE,EAAE,GAAKF,CACrC,GACAvE,IACAN,EAAUM,EAAY,UACtBR,EAAM,IAAI,CAACU,OAAO,CAACC,WAAW,CAAEH,EAAWE,OAAO,EAClDV,EAAMW,EAAaH,GACnBA,EAAWwB,OAAO,GAE1B,CAKA,SAASkD,IAELjE,AADc,IAAI,CACZN,WAAW,CAAG,EAAE,CACjB,IAAI,CAACD,OAAO,CAACC,WAAW,EACzB,CAAA,IAAI,CAACD,OAAO,CAACC,WAAW,CAAG,EAAE,AAAD,CAEpC,CAIA,SAASwE,EAAgCC,CAAO,EACvC,IAAI,CAACnE,KAAK,CAACoE,oBAAoB,EAChCD,EAAQE,KAAK,CAAC,IAAI,CAAErB,MAAMvE,SAAS,CAAC6F,KAAK,CAAC3F,IAAI,CAAC4F,UAAW,GAElE,CAuCIjH,AACDA,CAAAA,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,CAAC,EADvBkH,OAAO,CApBvB,SAAiBC,CAAe,CAAEC,CAAU,CAAEC,CAAY,EACtD,IAAMC,EAAaF,EAAWjG,SAAS,CACvC,GAAI,CAACmG,EAAWC,aAAa,CAAE,CAC3B,IAAMC,EAAeH,EAAalG,SAAS,CAC3CK,EAAS4F,EAAY,YAAaT,GAClCW,EAAWC,aAAa,CAAGzF,EAC3BwF,EAAWG,SAAS,CAACpF,IAAI,CAACI,GAC1B6E,EAAWI,mBAAmB,CAACtF,WAAW,CAAG,CAACN,EAAmB,CACjEwF,EAAWK,qBAAqB,CAACtF,IAAI,CAAC,eACtCiF,EAAW9D,eAAe,CAAG4C,EAC7BkB,EAAWM,gBAAgB,CAAGrB,EAC9Be,EAAWpF,cAAc,CAAG,SAA6BH,CAAW,EAChE,IACsBE,EAAa,GADdkF,CAAAA,EAAgBU,KAAK,CAAC9F,EAAY+F,IAAI,CAAC,EACxDX,CAAc,EAAiC,IAAI,CAAEpF,GAEzD,OADA,IAAI,CAACK,WAAW,CAACC,IAAI,CAACJ,GACfA,CACX,EACAJ,EAAK2F,EAAc,uBAAwBZ,EAC/C,CACJ,EAQyB,IAAMmB,EAA+B/H,EAS5D,CAAEgI,QAAAA,CAAO,CAAE,CAAIzG,IAkoBf,CAAE0G,IAAAA,CAAG,CAAEC,cAAAA,CAAa,CAAE,CAAI3G,IAE1B,CAAEC,SAAU2G,CAAqB,CAAExG,UAAWyG,CAAsB,CAAEC,WAAAA,CAAU,CAAEzG,KAAM0G,CAAiB,CAAEC,YAAAA,CAAW,CAAE,CAAIhH,IAsO/FiH,EA7NnC,MAUIC,WAAY,CACR,IAAMC,EAAU,IAAI,CAAEC,EAAoB,SAAUC,CAAO,EACvDT,EAAsBS,EAASV,EAAgB,aAAe,YAAa,AAACW,IACxEH,EAAQI,WAAW,CAACD,EACxB,EAAG,CAAEE,QAAS,CAAA,CAAM,EACxB,EA0BA,GAzBAJ,EAAkB,IAAI,CAACrG,OAAO,CAACsG,OAAO,EACtC,AAACF,CAAAA,EAAQ7D,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,AAAC0B,IACxBA,EAAM3C,OAAO,CAAC6G,OAAO,EACrBlE,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACkE,aAAa,EAEjCN,EAAkB7D,EAAMxC,OAAO,CAACyC,IAAI,CAAC6D,OAAO,CAEpD,GACAP,EAAWK,EAAQvG,OAAO,CAAC+G,MAAM,CAAE,CAACxF,EAAOoE,KACvC,IAAMqB,EAAe,SAAUN,CAAC,EACf,UAATf,GAAqBY,EAAQU,WAAW,EACxC1F,EAAMrC,IAAI,CAACqH,EAASA,EAAQhG,KAAK,CAAC2G,OAAO,EAAEC,UAAUT,GAAIH,EAAQa,MAAM,CAE/E,CACI,AAA+C,CAAA,KAA/C,AAACb,CAAAA,EAAQc,YAAY,EAAI,EAAE,AAAD,EAAGC,OAAO,CAAC3B,IACrCK,EAAsBO,EAAQpG,OAAO,CAACsG,OAAO,CAAEd,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,GAChFL,EAAQpG,OAAO,CAACoH,GAAG,EACnBvB,EAAsBO,EAAQpG,OAAO,CAACoH,GAAG,CAAE5B,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,IAIpFZ,EAAsBO,EAASZ,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,EAE5E,GACIL,EAAQvG,OAAO,CAACwH,SAAS,GACzBxB,EAAsBO,EAAS,OAAQA,EAAQkB,MAAM,EACjD,CAAClB,EAAQpG,OAAO,CAACM,QAAQ,CAACiH,UAAU,EAAE,CACtC,IAAMC,EAAa,CACfC,OAAQ,CACJ5E,EAAG,YACH6E,EAAG,YACHC,GAAI,MACR,CAAC,CAACvB,EAAQvG,OAAO,CAACwH,SAAS,CAAC,AAChC,EACAjB,EAAQpG,OAAO,CAAC4H,GAAG,CAACJ,GACpB,AAACpB,CAAAA,EAAQ7D,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,AAAC0B,IACxBA,EAAM3C,OAAO,CAAC6G,OAAO,EACrBlE,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACkE,aAAa,EACjCnE,EAAMxC,OAAO,CAACyC,IAAI,CAACmF,GAAG,CAACJ,EAE/B,EACJ,CAECpB,EAAQyB,UAAU,EACnB/B,EAAuBM,EAAS,MAExC,CAIAjF,SAAU,CACN,IAAI,CAAC2G,eAAe,GACpB7B,EAAY,IAAI,EAChB,IAAI,CAAC8B,QAAQ,CAAG,IACpB,CAKAC,mBAAmBzB,CAAC,CAAE0B,CAAE,CAAEC,CAAE,CAAE,CAC1B,IAAIC,EAAS5B,EAAE6B,UAAU,CAAGF,EAAIG,EAAS9B,EAAE+B,UAAU,CAAGL,EAAIM,EAAKhC,EAAEiC,MAAM,CAAGN,EAAIO,EAAKlC,EAAEmC,MAAM,CAAGT,EAAIU,EASpG,OARI,IAAI,CAACvI,KAAK,CAACwI,QAAQ,GACnBD,EAAON,EACPA,EAASF,EACTA,EAASQ,EACTA,EAAOF,EACPA,EAAKF,EACLA,EAAKI,GAEFpF,KAAKsF,KAAK,CAACN,EAAIE,GAAMlF,KAAKsF,KAAK,CAACV,EAAQE,EACnD,CAKAS,iBAAiBvC,CAAC,CAAE0B,CAAE,CAAEC,CAAE,CAAE,CACxB,IAAMG,EAAS9B,EAAE+B,UAAU,CAAGL,EAAIE,EAAS5B,EAAE6B,UAAU,CAAGF,EAAIO,EAAKlC,EAAEmC,MAAM,CAAGT,EAAIM,EAAKhC,EAAEiC,MAAM,CAAGN,EAC9Fa,EAAK,AAACN,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAAIW,EAAK,AAACT,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAChE,GAAI,IAAI,CAAC/H,KAAK,CAACwI,QAAQ,CAAE,CACrB,IAAMD,EAAOK,EACbA,EAAKD,EACLA,EAAKJ,CACT,CACA,MAAO,CACH9F,EAAGkG,EACHrB,EAAGsB,CACP,CACJ,CAKAC,uBAAuB1C,CAAC,CAAE,CACtB,IAAIkC,EAAKlC,EAAEmC,MAAM,CAAGnC,EAAE+B,UAAU,CAAEC,EAAKhC,EAAEiC,MAAM,CAAGjC,EAAE6B,UAAU,CAAEO,EAMhE,OALI,IAAI,CAACvI,KAAK,CAACwI,QAAQ,GACnBD,EAAOJ,EACPA,EAAKE,EACLA,EAAKE,GAEF,CACH9F,EAAG4F,EACHf,EAAGa,CACP,CACJ,CAMAjB,OAAOf,CAAC,CAAE,CACN,GAAI,IAAI,CAACnG,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAG,IAAI,CAACtI,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAG,IAAI,CAACpI,KAAK,CAACgJ,OAAO,CAAE,CACvFC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,EACjB,CAAA,MAA3B,IAAI,CAAC1G,OAAO,CAACwH,SAAS,EACtBiC,CAAAA,EAAY5B,CAAC,CAAG,CAAA,EAEW,MAA3B,IAAI,CAAC7H,OAAO,CAACwH,SAAS,EACtBiC,CAAAA,EAAYzG,CAAC,CAAG,CAAA,EAGhBuD,AADY,IAAI,CACRzD,MAAM,CAACX,MAAM,CACrBoE,AAFY,IAAI,CAERmD,SAAS,CAACD,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,GAG9CtB,AALY,IAAI,CAKRoD,MAAM,CAAC1I,OAAO,CAAC,AAAC2I,GAAUA,EAAMF,SAAS,CAACD,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,GAC9EtB,AANY,IAAI,CAMR7D,MAAM,CAACzB,OAAO,CAAC,AAAC0B,GAAUA,EAAM+G,SAAS,CAACD,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,IAElF,IAAI,CAAChI,MAAM,CAAC,CAAA,EAChB,CACJ,CAKA8G,YAAYD,CAAC,CAAE,CAKX,GAJIA,EAAEmD,cAAc,EAChBnD,EAAEmD,cAAc,GAGhBnD,AAAa,IAAbA,EAAEoD,MAAM,CACR,OAEJ,IAAMvD,EAAU,IAAI,CAAEW,EAAUX,EAAQhG,KAAK,CAAC2G,OAAO,CAGrD6C,EAAmB,AAACrD,GAAGsD,oBAAoBD,kBAAqB,CAAA,EAE5DtB,EAAa/B,AADjBA,CAAAA,EAAIQ,GAASC,UAAUT,IAAMA,CAAAA,EACVmC,MAAM,CAAEN,EAAa7B,EAAEiC,MAAM,AAChDpC,CAAAA,EAAQU,WAAW,CAAG,CAAA,EACtBV,EAAQhG,KAAK,CAACoE,oBAAoB,CAAG,CAAA,EACrC4B,EAAQ0D,UAAU,CAAGjE,EAAsBF,EAAKC,GAAiBgE,EAAmB,YAAc,YAAa,SAAUrD,CAAC,EACtHH,EAAQ2D,UAAU,CAAG,CAAA,EAErBxD,AADAA,CAAAA,EAAIQ,GAASC,UAAUT,IAAMA,CAAAA,EAC3B+B,UAAU,CAAGA,EACf/B,EAAE6B,UAAU,CAAGA,EACftC,EAAuBM,EAAS,OAAQG,GACxC+B,EAAa/B,EAAEmC,MAAM,CACrBN,EAAa7B,EAAEiC,MAAM,AACzB,EAAG5C,GAAiBgE,EAAmB,CAAEnD,QAAS,CAAA,CAAM,EAAI,KAAK,GACjEL,EAAQ4D,aAAa,CAAGnE,EAAsBF,EAAKC,GAAiBgE,EAAmB,WAAa,UAAW,WAG3G,IAAMjK,EAAaqG,EAAkBI,EAAQa,MAAM,EAAIb,EAAQa,MAAM,CAACtH,UAAU,CAAEyG,EAAQa,MAAM,EAC5FtH,GAEAA,CAAAA,EAAWmH,WAAW,CAAGV,EAAQ2D,UAAU,AAAD,EAE9C3D,EAAQU,WAAW,CAAGV,EAAQ2D,UAAU,CACxC3D,EAAQhG,KAAK,CAACoE,oBAAoB,CAAG,CAAA,EACjC4B,EAAQ2D,UAAU,EAElBjE,EAAuBE,EAAkBrG,EACzCyG,GAAU,eAEdA,EAAQ2D,UAAU,CAAG,CAAA,EACrB3D,EAAQ6D,SAAS,EACrB,EAAGrE,GAAiBgE,EAAmB,CAAEnD,QAAS,CAAA,CAAM,EAAI,KAAK,EACrE,CAIAwD,WAAY,CACR,IAAI,CAACnC,eAAe,EACxB,CAKAA,iBAAkB,CACV,IAAI,CAACgC,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAI,CAACA,UAAU,EAAC,EAElC,IAAI,CAACE,aAAa,EAClB,CAAA,IAAI,CAACA,aAAa,CAAG,IAAI,CAACA,aAAa,EAAC,CAEhD,CACJ,EAiBM,CAAEE,MAAAA,CAAK,CAAE5K,KAAM6K,CAAiB,CAAE,CAAIlL,IA4HTmL,EA/FnC,cAA2BlE,EAMvBmE,YAAYjK,CAAK,CAAE6G,CAAM,CAAEpH,CAAO,CAAEoD,CAAK,CAAE,CACvC,KAAK,GAQL,IAAI,CAACiE,YAAY,CAAG,CAAC,OAAO,CAC5B,IAAI,CAAC9G,KAAK,CAAGA,EACb,IAAI,CAAC6G,MAAM,CAAGA,EACd,IAAI,CAACpH,OAAO,CAAGA,EACf,IAAI,CAACoD,KAAK,CAAGkH,EAAkBtK,EAAQoD,KAAK,CAAEA,EAClD,CAUA9B,SAAU,CACN,KAAK,CAACA,UACF,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAExC,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAAC6G,MAAM,CAAG,KACd,IAAI,CAACpH,OAAO,CAAG,IACnB,CAMAH,OAAO4K,CAAS,CAAE,CACd,IAAI,CAACtK,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,IAAI,CAACzK,OAAO,CAAC0K,UAAU,CAACxL,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkI,MAAM,EAC/F,CAKAuD,QAAS,CACL,IAAMpK,EAAQ,IAAI,CAACA,KAAK,CAAEP,EAAU,IAAI,CAACA,OAAO,AAChD,CAAA,IAAI,CAACG,OAAO,CAAGI,EAAME,QAAQ,CACxBmK,MAAM,CAAC5K,EAAQ4K,MAAM,CAAE,EAAG,EAAG5K,EAAQ6K,KAAK,CAAE7K,EAAQ8K,MAAM,EAC1D9J,GAAG,CAACT,EAAMK,kBAAkB,EAC5BmH,GAAG,CAAC/H,EAAQ+K,KAAK,EACtB,IAAI,CAACC,aAAa,CAAChL,EAAQiL,OAAO,EAElC,IAAI,CAAC3E,SAAS,EAClB,CAUA0E,cAAcC,CAAO,CAAE,CACnB,IAAI,CAAC9K,OAAO,CAAC8K,EAAU,OAAS,OAAO,GACvC,IAAI,CAACjL,OAAO,CAACiL,OAAO,CAAGA,CAC3B,CASAC,OAAOtL,CAAW,CAAE,CAChB,IAAMW,EAAQ,IAAI,CAACA,KAAK,CAAE6G,EAAS,IAAI,CAACA,MAAM,CAAEhE,EAAQ,IAAI,CAACA,KAAK,CAAEpD,EAAUqK,EAAM,CAAA,EAAM,IAAI,CAACrK,OAAO,CAAEJ,GACxG,IAAI,CAAC0B,OAAO,GACZ,IAAI,CAACkJ,WAAW,CAACjK,EAAO6G,EAAQpH,EAASoD,GACzC,IAAI,CAACuH,MAAM,CAACpK,EAAMK,kBAAkB,EACpC,IAAI,CAACf,MAAM,EACf,CACJ,EAuBMsL,EAAoExN,EAAwD,OAAU,CAACyN,cAAc,CAC3J,IAAIC,EAAyEtN,EAAoBC,CAAC,CAACmN,GASnG,GAAM,CAAEjI,OAAQ,CAAElE,UAAWsM,CAAW,CAAE,CAAE,CAAID,IAE1C,CAAExF,QAAS0F,CAAiB,CAAE/L,UAAWgM,CAAmB,CAAE,CAAIpM,GA4BxE,OAAMqM,EAiBF,OAAOC,UAAUC,CAAK,CAAE,CACpB,OAAO,IAAIF,EAAUE,EAAMzI,MAAM,CAAC3C,KAAK,CAAE,KAAM,CAC3CyC,EAAG2I,EAAM3I,CAAC,CACV6E,EAAG8D,EAAM9D,CAAC,CACV1E,MAAOwI,EAAMzI,MAAM,CAACC,KAAK,CACzByI,MAAOD,EAAMzI,MAAM,CAAC0I,KAAK,AAC7B,EACJ,CAcA,OAAOC,cAAcF,CAAK,CAAEG,CAAe,CAAE,CACzC,IAAM5I,EAASyI,EAAMzI,MAAM,CAAE3C,EAAQ2C,EAAO3C,KAAK,CAC7CyC,EAAI2I,EAAMI,KAAK,EAAI,EAAGlE,EAAI8D,EAAMK,KAAK,EAAI,EAAGrL,EAgBhD,OAfIJ,EAAMwI,QAAQ,GACV4C,EAAMM,IAAI,EACVjJ,EAAI2I,EAAMK,KAAK,CACfnE,EAAI8D,EAAMI,KAAK,GAGf/I,EAAIzC,EAAM2L,SAAS,CAAIP,CAAAA,EAAMK,KAAK,EAAI,CAAA,EACtCnE,EAAItH,EAAM4L,UAAU,CAAIR,CAAAA,EAAMI,KAAK,EAAI,CAAA,IAG3C7I,GAAU,CAAC4I,IAEX9I,GAAKrC,AADLA,CAAAA,EAAUuC,EAAOkJ,UAAU,EAAC,EACfC,UAAU,CACvBxE,GAAKlH,EAAQ2L,UAAU,EAEpB,CACHtJ,EAAGA,EACH6E,EAAGA,CACP,CACJ,CAYA,OAAO0E,eAAeZ,CAAK,CAAE,CACzB,MAAO,CACH3I,EAAG2I,EAAM3I,CAAC,CACV6E,EAAG8D,EAAM9D,CAAC,CACV1E,MAAOwI,EAAMzI,MAAM,CAACC,KAAK,CACzByI,MAAOD,EAAMzI,MAAM,CAAC0I,KAAK,AAC7B,CACJ,CAMApB,YAAYjK,CAAK,CAAE6G,CAAM,CAAEpH,CAAO,CAAE,CAYhC,IAAI,CAACiM,IAAI,CAAG,CAAA,EAEZ,IAAI,CAACN,KAAK,CAAG,IAAI,CAOjB,IAAI,CAACzI,MAAM,CAAG,CACV+H,QAAS,CAAA,EACT1K,MAAOA,EACP6L,WAAYd,EAAYc,UAAU,AACtC,EAKA,IAAI,CAAChF,MAAM,CAAGA,GAAU,KAOxB,IAAI,CAACpH,OAAO,CAAGA,EAkCf,IAAI,CAACwM,YAAY,CAAC,IAAI,CAACC,UAAU,GACrC,CAMAD,aAAaxM,CAAO,CAAE,CAClB,IAAI,CAAC0M,OAAO,CAAG1M,EAAQ0M,OAAO,CAC9B,IAAI,CAACC,OAAO,CAAC3M,EAAS,KACtB,IAAI,CAAC2M,OAAO,CAAC3M,EAAS,KACtB,IAAI,CAAC4M,OAAO,EAChB,CAOAH,YAAa,CACT,OAAO,IAAI,CAACI,iBAAiB,GACzB,IAAI,CAAC7M,OAAO,CAAC,IAAI,CAACoH,MAAM,EACxB,IAAI,CAACpH,OAAO,AACpB,CAOA6M,mBAAoB,CAChB,MAAO,AAAwB,YAAxB,OAAO,IAAI,CAAC7M,OAAO,AAC9B,CAMAqJ,cAAe,CACX,IAAM0C,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAQ,IAAI,CAACA,KAAK,CAAE7I,EAAQ,IAAI,CAACD,MAAM,CAACC,KAAK,CAAEyI,EAAQ,IAAI,CAAC1I,MAAM,CAAC0I,KAAK,CAAElF,EAAI,CACpG1D,EAAG+I,EACHlE,EAAGmE,EACH3C,aAAc,CAAA,EACdrJ,QAAS,CAAC,CACd,EAWA,OAVImD,GACAuD,CAAAA,EAAE2C,YAAY,CAAGkC,EAAkBQ,IAAUA,GAAS,GAAKA,GAAS5I,EAAM2J,GAAG,AAAD,EAE5ElB,GACAlF,CAAAA,EAAE2C,YAAY,CACV3C,EAAE2C,YAAY,EACVkC,EAAkBS,IAClBA,GAAS,GAAKA,GAASJ,EAAMkB,GAAG,AAAD,EAE3CtB,EAAoB,IAAI,CAACtI,MAAM,CAAC3C,KAAK,CAAE,oBAAqBmG,GACrDA,EAAE2C,YAAY,AACzB,CAKAuD,SAAU,CACN,IAAM1J,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAEyI,EAAQ1I,EAAO0I,KAAK,CAAE5L,EAAU,IAAI,CAACyM,UAAU,GAC7FtJ,GACA,IAAI,CAACH,CAAC,CAAGhD,EAAQgD,CAAC,CAClB,IAAI,CAAC+I,KAAK,CAAG5I,EAAM4J,QAAQ,CAAC/M,EAAQgD,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KAAK,EACd,IAAI,CAAC+I,KAAK,CAAG/L,EAAQgD,CAAC,EAEtB4I,GACA,IAAI,CAAC/D,CAAC,CAAG7H,EAAQ6H,CAAC,CAClB,IAAI,CAACmE,KAAK,CAAGJ,EAAMmB,QAAQ,CAAC/M,EAAQ6H,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KACT,IAAI,CAACmE,KAAK,CAAGhM,EAAQ6H,CAAC,EAE1B,IAAI,CAACmF,QAAQ,CAAG,IAAI,CAAC3D,YAAY,EACrC,CAKA4D,gBAAiB,CACb,IAAM/J,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAEyI,EAAQ1I,EAAO0I,KAAK,AACtE,CAAA,IAAI,CAAC5I,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAAGG,EACtB,IAAI,CAACnD,OAAO,CAACgD,CAAC,CAAGG,EAAM+J,OAAO,CAAC,IAAI,CAACnB,KAAK,CAAE,CAAA,GAC3C,IAAI,CAACA,KAAK,CACd,IAAI,CAAClE,CAAC,CAAG,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,CAAG+D,EACtBA,EAAMsB,OAAO,CAAC,IAAI,CAAClB,KAAK,CAAE,CAAA,GAC1B,IAAI,CAACA,KAAK,AAClB,CAQAmB,OAAO/E,CAAE,CAAEC,CAAE,CAAE+E,CAAO,CAAE,CACpB,GAAI,CAAC,IAAI,CAACP,iBAAiB,GAAI,CAC3B,IAAMQ,EAAM3J,KAAK2J,GAAG,CAACD,GAAUE,EAAM5J,KAAK4J,GAAG,CAACF,GAAUpK,EAAI,IAAI,CAAC+I,KAAK,CAAG3D,EAAIP,EAAI,IAAI,CAACmE,KAAK,CAAG3D,CAC9F,CAAA,IAAI,CAAC0D,KAAK,CAAGwB,AAD0FvK,EAAIqK,EAAMxF,EAAIyF,EACnGlF,EAClB,IAAI,CAAC4D,KAAK,CAAGwB,AAFkHxK,EAAIsK,EAAMzF,EAAIwF,EAE3HhF,EAClB,IAAI,CAAC4E,cAAc,EACvB,CACJ,CAkBAQ,MAAMrF,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,CAAE,CAClB,GAAI,CAAC,IAAI,CAAC0D,iBAAiB,GAAI,CAC3B,IAAM7J,EAAI,IAAI,CAAC+I,KAAK,CAAG7C,EAAIrB,EAAI,IAAI,CAACmE,KAAK,CAAG7C,CAC5C,CAAA,IAAI,CAAC4C,KAAK,CAAGwB,AADyC,CAAA,EAAIrE,CAAC,EAAKd,EAC9CpF,EAClB,IAAI,CAACgJ,KAAK,CAAGwB,AAF6D,CAAA,EAAIrE,CAAC,EAAKd,EAElER,EAClB,IAAI,CAACoF,cAAc,EACvB,CACJ,CAQAN,QAAQ3M,CAAO,CAAE0N,CAAI,CAAE,CACnB,IAAMC,EAAYD,EAAO,OAASE,EAAc5N,CAAO,CAAC2N,EAAS,CAAEpN,EAAQ,IAAI,CAAC2C,MAAM,CAAC3C,KAAK,AAC5F,CAAA,IAAI,CAAC2C,MAAM,CAACyK,EAAS,CACjB,AAAuB,UAAvB,OAAOC,EACHA,EACArC,EAAkBqC,GACbrN,CAAK,CAACoN,EAAS,CAACC,EAAY,EAEzBrN,EAAM1B,GAAG,CAAC+O,GACd,IAChB,CAOAC,UAAW,CACP,IAAMC,EAAS,CAAC,IAAI,CAAC/B,KAAK,CAAE,IAAI,CAACC,KAAK,CAAE,EAAG,EAAE,CAK7C,OAJI,IAAI,CAAC9I,MAAM,CAAC3C,KAAK,CAACwI,QAAQ,GAC1B+E,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC9B,KAAK,CACtB8B,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC/B,KAAK,EAEnB+B,CACX,CAkBApE,UAAUqE,CAAG,CAAEC,CAAG,CAAEpF,CAAE,CAAEF,CAAE,CAAE,CACnB,IAAI,CAACmE,iBAAiB,KACvB,IAAI,CAACd,KAAK,EAAInD,EACd,IAAI,CAACoD,KAAK,EAAItD,EACd,IAAI,CAACuE,cAAc,GAE3B,CACJ,EAiHA,AAAC,SAAUnP,CAAa,EAepB,SAASmQ,IACL,IAAMC,EAAgB,IAAI,CAACA,aAAa,CAAEC,EAAuB,IAAI,CAACnO,OAAO,CAACkO,aAAa,EAAI,EAAE,CACjGC,EAAqBlN,OAAO,CAAC,CAACmN,EAAqBjN,KAC/C,IAAMnB,EAAUZ,IAA6CiL,KAAK,CAAC,IAAI,CAACrK,OAAO,CAACoO,mBAAmB,CAAEA,EAChGpO,CAAAA,EAAQoD,KAAK,EACdpD,CAAAA,EAAQoD,KAAK,CAAGjC,CAAAA,EAEpBgN,CAAoB,CAAChN,EAAE,CAAGnB,EAC1BkO,EAAchO,IAAI,CAAC,IAAIqK,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAEP,GACtE,EACJ,CAUA,SAAS8N,EAAOnC,CAAK,EACjB,IAAMhL,EAAUgL,EAAMzI,MAAM,CAACkJ,UAAU,GAAI7L,EAAQoL,EAAMzI,MAAM,CAAC3C,KAAK,CAAE8N,EAAM1C,EAAMM,IAAI,CACnFN,EAAMkC,QAAQ,GACdtN,EAAM+N,OAAO,EACT/N,EAAM+N,OAAO,CAACC,SAAS,CAACrP,IAAI,CAAC,CACzBqB,MAAOoL,EAAMzI,MAAM,CAAC3C,KAAK,AAC7B,EAAGoL,IACH,CAAC,EAAG,EAAG,EAAG,EAAE,CAAEmC,EAAS,CAC3B9K,EAAGqL,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAACrO,OAAO,CAACgD,CAAC,EAAI,CAAA,EAC/B6E,EAAGwG,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAACrO,OAAO,CAAC6H,CAAC,EAAI,CAAA,EAC/BiD,OAAQuD,CAAG,CAAC,EAAE,EAAI,EAClBxD,MAAOwD,CAAG,CAAC,EAAE,EAAI,CACrB,EACA,MAAO,CACHG,iBAAkBV,EAClBW,iBAAkBrP,IAA6CiL,KAAK,CAACyD,EAAQ,CACzE9K,EAAG8K,EAAO9K,CAAC,CAAI2I,CAAAA,EAAMM,IAAI,CAAGtL,EAAQ0L,UAAU,CAAG9L,EAAM+I,QAAQ,AAAD,EAC9DzB,EAAGiG,EAAOjG,CAAC,CAAI8D,CAAAA,EAAMM,IAAI,CAAGtL,EAAQ2L,UAAU,CAAG/L,EAAMgJ,OAAO,AAAD,CACjE,EACJ,CACJ,CA6BA,SAASmF,IACL,IAAI,CAACR,aAAa,CAACjN,OAAO,CAAC,AAAC0N,GAAiBA,EAAarN,OAAO,IACjE,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAAC2N,aAAa,CAAG,KACrB,IAAI,CAACpL,MAAM,CAAG,KACd,IAAI,CAAC9C,OAAO,CAAG,KACX,IAAI,CAACF,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAG,CAE7B,CAOA,SAAS8O,IACL,IAAM5O,EAAU,IAAI,CAACA,OAAO,CAC5B,OAAQA,EAAQ8C,MAAM,EACjB9C,EAAQ2L,KAAK,EAAIvM,IAA6CyP,KAAK,CAAC7O,EAAQ2L,KAAK,CAC1F,CAOA,SAASmD,IACL,IACI3N,EAAGwK,EADDoD,EAAgB,IAAI,CAACH,gBAAgB,GAAI9L,EAAS,IAAI,CAACA,MAAM,CAAEgK,EAAM,AAACiC,GAAiBA,EAAc5M,MAAM,EAAK,EAEtH,IAAKhB,EAAI,EAAGA,EAAI2L,EAAK3L,IAAK,CAEtB,GAAI,CADJwK,CAAAA,EAAQ,IAAI,CAACA,KAAK,CAACoD,CAAa,CAAC5N,EAAE,CAAE2B,CAAM,CAAC3B,EAAE,CAAA,EAClC,CACR2B,EAAOX,MAAM,CAAG,EAChB,MACJ,CACIwJ,EAAMM,IAAI,EACVN,EAAMiB,OAAO,GAEjB9J,CAAM,CAAC3B,EAAE,CAAGwK,CAChB,CACA,OAAO7I,CACX,CAWA,SAAS6I,EAAMqD,CAAY,CAAErD,CAAK,EAC9B,GAAIqD,GAAgBA,EAAa9L,MAAM,CACnC,OAAO8L,EAEX,GAAI,CAACrD,GAASA,AAAiB,OAAjBA,EAAMzI,MAAM,EACtB,GAAI9D,IAA6C6P,QAAQ,CAACD,GACtDrD,EAAQ,IA1PoCF,EA0PV,IAAI,CAAClL,KAAK,CAAE,IAAI,CAAEyO,QAEnD,GAAI5P,IAA6C8P,QAAQ,CAACF,GAC3DrD,EAAQ,IAAI,CAACpL,KAAK,CAAC1B,GAAG,CAACmQ,IAAiB,UAEvC,GAAI,AAAwB,YAAxB,OAAOA,EAA6B,CACzC,IAAMG,EAAcH,EAAa9P,IAAI,CAACyM,EAAO,IAAI,EACjDA,EAAQwD,EAAYjM,MAAM,CACtBiM,EACA,IAnQwC1D,EAmQd,IAAI,CAAClL,KAAK,CAAE,IAAI,CAAEyO,EACpD,EAEJ,OAAOrD,CACX,CAKA,SAASyD,EAAoB3E,CAAS,EAClC,IAAI,CAACyD,aAAa,CAACjN,OAAO,CAAC,AAAC0N,GAAiBA,EAAa9O,MAAM,CAAC4K,GACrE,CAKA,SAAS4E,IACL,IAAI,CAACnB,aAAa,CAACjN,OAAO,CAAC,AAAC0N,GAAiBA,EAAahE,MAAM,GACpE,CAeA,SAAS2E,EAAUC,CAAc,CAAEnH,CAAE,CAAEC,CAAE,CAAEmH,CAAE,CAAEC,CAAE,EAC7C,GAAI,IAAI,CAAClP,KAAK,CAACwI,QAAQ,CAAE,CACrB,IAAMD,EAAOV,EACbA,EAAKC,EACLA,EAAKS,CACT,CACA,IAAI,CAAChG,MAAM,CAAC7B,OAAO,CAAC,CAACyO,EAAQvO,IAAO,IAAI,CAACwO,cAAc,CAACJ,EAAgBnH,EAAIC,EAAImH,EAAIC,EAAItO,GAAK,IAAI,CACrG,CAmBA,SAASwO,EAAeJ,CAAc,CAAEnH,CAAE,CAAEC,CAAE,CAAEmH,CAAE,CAAEC,CAAE,CAAEtO,CAAC,EACrD,IAAIwK,EAAQ,IAAI,CAAC7I,MAAM,CAAC3B,EAAE,AACrBwK,CAAAA,EAAMM,IAAI,EACXN,CAAAA,EAAQ,IAAI,CAAC7I,MAAM,CAAC3B,EAAE,CAAGyO,AAjUuBnE,EAiUDC,SAAS,CAACC,EAAK,EAElEA,CAAK,CAAC4D,EAAe,CAACnH,EAAIC,EAAImH,EAAIC,EACtC,CASA,SAAS/F,EAAUd,CAAE,CAAEF,CAAE,EACrB,IAAI,CAAC4G,SAAS,CAAC,YAAa,KAAM,KAAM1G,EAAIF,EAChD,CAWA,SAASmH,EAAejH,CAAE,CAAEF,CAAE,CAAEvH,CAAC,EAC7B,IAAI,CAACwO,cAAc,CAAC,YAAa,KAAM,KAAM/G,EAAIF,EAAIvH,EACzD,CAlKArD,EAAciH,OAAO,CAnBrB,SAAiB+K,CAAkB,EAC/B,IAAMC,EAAeD,EAAmB9Q,SAAS,AAC5C+Q,CAAAA,EAAa9B,gBAAgB,EAC9B7O,IAA6CiL,KAAK,CAAC,CAAA,EAAM0F,EAAc,CACnE9B,iBAAAA,EACAH,OAAAA,EACAY,qBAAAA,EACAE,iBAAAA,EACAE,WAAAA,EACAnD,MAAAA,EACAyD,oBAAAA,EACAC,oBAAAA,EACAC,UAAAA,EACAK,eAAAA,EACAjG,UAAAA,EACAmG,eAAAA,CACJ,EAER,CAoKJ,EAAG/R,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAMkS,EAA6BlS,EAW1D,CAAEuM,MAAO4F,CAAkB,CAAE,CAAI7Q,GAWvC,OAAM8Q,EAMF1F,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE+M,CAAQ,CAAE,CAC9C,IAAI,CAACrQ,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAAC6P,UAAU,CAAID,AAAa,UAAbA,EAAuB,SAAW,SACrD,IAAI,CAACjC,aAAa,CAAG,EAAE,CACvB,IAAI,CAAClO,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACM,KAAK,CAAGA,EACb,IAAI,CAAC+M,QAAQ,CAAGA,EAChB,IAAI,CAACE,IAAI,CAACvQ,EAAYE,EAASoD,EACnC,CAUAhD,KAEA,GAAGkQ,CAAK,CAAE,CACN,IAAI,CAACnQ,OAAO,CAACC,IAAI,CAACwE,KAAK,CAAC,IAAI,CAACzE,OAAO,CAAE2E,UAC1C,CASAyL,iBAAiBvQ,CAAO,CAAE,CACtB,IACIxB,EAAKgS,EADHC,EAAM,IAAI,CAACjG,WAAW,CAACkG,QAAQ,CAAEC,EAAQ,CAAC,EAAGjJ,EAAa,IAAI,CAACnH,KAAK,CAACmH,UAAU,CAErF,IAAKlJ,KAAOwB,EACRwQ,EAAYC,CAAG,CAACjS,EAAI,CACI,KAAA,IAAbiS,CAAG,CAACjS,EAAI,EACd,AAACkJ,GACE,AAC4B,KAD5B,CAAC,OAAQ,SAAU,eAAe,CAC7BJ,OAAO,CAACkJ,IACjBG,CAAAA,CAAK,CAACH,EAAU,CAAGxQ,CAAO,CAACxB,EAAI,AAAD,EAGtC,OAAOmS,CACX,CAKArP,SAAU,CACF,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAEpC,IAAI,CAACsP,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACtP,OAAO,EAAC,EAExC,IAAI,CAACoN,oBAAoB,EAC7B,CAKA2B,KAAKvQ,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CAC7B,IAAI,CAACtD,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACoL,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC9K,KAAK,CAAGA,EACb,IAAI,CAAC0L,UAAU,GACf,IAAI,CAACb,gBAAgB,EACzB,CAKApO,OAAO4K,CAAS,CAAE,CACd,IAAI,CAAC2E,mBAAmB,CAAC3E,EAC7B,CAKAE,OAEAkG,CAAY,CAAE,CACN,IAAI,CAAC7Q,OAAO,CAAC8Q,SAAS,EAAI,IAAI,CAAC3Q,OAAO,EACtC,IAAI,CAACA,OAAO,CAAC4Q,QAAQ,CAAC,IAAI,CAAC/Q,OAAO,CAAC8Q,SAAS,EAEhD,IAAI,CAACzB,mBAAmB,EAC5B,CAUAlC,OAAO/E,CAAE,CAAEC,CAAE,CAAE+E,CAAO,CAAE,CACpB,IAAI,CAACkC,SAAS,CAAC,SAAUlH,EAAIC,EAAI+E,EACrC,CAaAK,MAAMrF,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,CAAE,CAClB,IAAI,CAACmG,SAAS,CAAC,QAASlH,EAAIC,EAAIa,EAAIC,EACxC,CAKA6H,2BAA2B/F,CAAO,CAAE,CAChC,IAAI,CAACiD,aAAa,CAACjN,OAAO,CAAC,AAAC0N,IACxBA,EAAa3D,aAAa,CAACC,EAC/B,EACJ,CAOAgG,eAAgB,CACZ,MAAO,CAAC,CAAC,IAAI,CAACnO,MAAM,CAACX,MAAM,AAC/B,CAaA+O,eAAetI,CAAE,CAAEF,CAAE,CAAEyI,CAAoB,CAAE,CACzC,IAAM5Q,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAEnC6Q,EAAe,IAAI,CAACtR,UAAU,CAACF,WAAW,CAE1CyR,EAAkB9Q,EAAMN,WAAW,CAACqH,OAAO,CAAC,IAAI,CAACxH,UAAU,EAAGwR,EAAe/Q,EAAMP,OAAO,CAACC,WAAW,CAACoR,EAAgB,CACvH,IAAI,CAACxB,cAAc,CAACjH,EAAIF,EAAI,GACxByI,GACA,IAAI,CAACtB,cAAc,CAACjH,EAAIF,EAAI,GAKhC4I,CAAY,CAAC,IAAI,CAAClB,UAAU,CAAC,CAAC,IAAI,CAAChN,KAAK,CAAC,CACpCuI,KAAK,CAAG,IAAI,CAAC3L,OAAO,CAAC2L,KAAK,CAC/ByF,CAAY,CAAC,IAAI,CAAChB,UAAU,CAAC,CAAC,IAAI,CAAChN,KAAK,CAAC,CACpCuI,KAAK,CAAG,IAAI,CAAC3L,OAAO,CAAC2L,KAAK,AACnC,CAKAT,OAAOqG,CAAU,CAAE,CACf,IAAMzR,EAAa,IAAI,CAACA,UAAU,CAAEE,EAAUiQ,EAAmB,CAAA,EAAM,IAAI,CAACjQ,OAAO,CAAEuR,GAAaC,EAAc,IAAI,CAACrR,OAAO,CAACqR,WAAW,CAAEC,EAAc,IAAI,CAACjH,WAAW,CACxK,IAAI,CAAClJ,OAAO,GAEZ2O,EAAmB,CAAA,EAAM,IAAI,CADL,IAAIwB,EAAY3R,EAAYE,EAAS,IAAI,CAACoD,KAAK,CAAE,IAAI,CAAC+M,QAAQ,GAEtF,IAAI,CAACxF,MAAM,CAAC6G,GACZ,IAAI,CAAC3R,MAAM,EACf,CACJ,CACAmQ,EAA0BjL,OAAO,CAACmL,GAML,IAAMwB,EAA8BxB,EAoJ3D,CAAEyB,eAAgBC,CAA+B,CAAE,CAd5B,CACzBD,eApDmB,CAInBE,MAAO,CACHC,QAAS,SACTC,WAAY,CACRxN,GAAI,QACJyN,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EAIAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CACR3T,EAAG,wBACH,eAAgB,CACpB,CACJ,EAAE,AACV,EAIA,gBAAiB,CACb0T,QAAS,SACTC,WAAY,CACRxN,GAAI,gBACJyN,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EACAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CAER3T,EAAG,yBACH,eAAgB,CACpB,CACJ,EAAE,AACV,CACJ,CAQA,EAeM,CAAEiB,SAAUgT,CAAyB,CAAExM,QAASyM,CAAwB,CAAEC,OAAAA,CAAM,CAAElI,MAAOmI,CAAsB,CAAEC,UAAAA,CAAS,CAAE,CAAIrT,IAMhIsT,EAAkBC,EAAmB,cACrCC,EAAoBD,EAAmB,gBAEvCE,EAAe,oBAAuB,CAAA,AAACzT,IAA8C0T,GAAG,CAAG,KAAS,IAAI,EAAK,IASnH,SAASH,EAAmBI,CAAU,EAClC,OAAO,SAAUC,CAAK,EAClB,IAAI,CAAC5S,IAAI,CAAC2S,EAAY,QAAUC,EAAQ,IAC5C,CACJ,CAIA,SAASC,IACL,IAAI,CAACjT,OAAO,CAACkT,IAAI,CAAGV,EAAuBZ,EAAiC,IAAI,CAAC5R,OAAO,CAACkT,IAAI,EAAI,CAAC,EAYtG,CAIA,SAASC,GAAqB5O,CAAE,CAAE6O,CAAa,EAC3C,IAAMpT,EAAU,CAAE+R,WAAY,CAAExN,GAAAA,CAAG,CAAE,EAC/BoM,EAAQ,CACV0C,OAAQD,EAAcE,KAAK,EAAI,OAC/BC,KAAMH,EAAcE,KAAK,EAAI,qBACjC,CACAtT,CAAAA,EAAQoS,QAAQ,CAAIgB,EAAchB,QAAQ,EACtCgB,EAAchB,QAAQ,CAAC3B,GAAG,CAAC,SAAU+C,CAAK,EACtC,OAAOhB,EAAuB7B,EAAO6C,EACzC,GACJ,IAAMC,EAAMjB,EAAuB,CAAA,EAAM,CACrCT,WAAY,CACRG,YAAa,GACbC,aAAc,GACdF,KAAM,EACND,KAAM,EACN0B,OAAQ,MACZ,CACJ,EAAGN,EAAepT,GACZ2T,EAAS,IAAI,CAACpV,UAAU,CAACkV,GAE/B,OADAE,EAAOpP,EAAE,CAAGA,EACLoP,CACX,CAwBA,MAAMC,WAAyBlC,EAM3B,OAAO3M,QAAQE,CAAU,CAAE4O,CAAgB,CAAE,CACzC,IAAMC,EAAmBD,EAAiB7U,SAAS,AAC9C8U,CAAAA,EAAiBC,SAAS,GAC3B1B,EAA0BpN,EAAY,oBAAqBgO,GAC3Da,EAAiBC,SAAS,CAAGZ,GAErC,CAMA3I,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,MAChB,CAYAqO,KAAM,CACF,IAAMC,EAAU,IAAI,CAACjU,OAAO,CAAC5B,CAAC,CAC9B,GAAI6V,EACA,MAAO,AAAmB,YAAnB,OAAOA,EACVA,EAAQ/U,IAAI,CAAC,IAAI,EACjB+U,EAER,IAAMnR,EAAS,IAAI,CAACA,MAAM,CAAEgK,EAAMhK,EAAOX,MAAM,CAAE/D,EAAI,EAAE,CACnD8V,EAAWpH,EAAKnB,EAAQ7I,CAAM,CAAC,EAAE,CAAEqR,EAAWD,GAAY,IAAI,CAACpG,MAAM,CAACnC,GAAO8C,gBAAgB,CAAE2F,EAAa,EAAG1H,EACnH,GAAIyH,EAEA,IADA/V,EAAE8B,IAAI,CAAC,CAAC,IAAKiU,EAASnR,CAAC,CAAEmR,EAAStM,CAAC,CAAC,EAC7B,EAAEuM,EAAatH,GAAOoH,GAEzBxH,EAAUf,AADVA,CAAAA,EAAQ7I,CAAM,CAACsR,EAAW,AAAD,EACT1H,OAAO,EAAI,IAC3ByH,EAAW,IAAI,CAACrG,MAAM,CAACnC,GAAO8C,gBAAgB,CAC1C/B,AAAY,MAAZA,EACAtO,EAAE8B,IAAI,CAAC,CAACwM,EAASyH,EAASnR,CAAC,CAAEmR,EAAStM,CAAC,CAAC,EAEnC6E,AAAY,MAAZA,EACLtO,EAAE8B,IAAI,CAAC,CAACwM,EAASyH,EAASnR,CAAC,CAAEmR,EAAStM,CAAC,CAAC,EAEvB,MAAZ6E,GACLtO,EAAE8B,IAAI,CAAC,CAACwM,EAAQ,EAEpBwH,EAAWvI,EAAMzI,MAAM,CAAC+H,OAAO,CAGvC,OAAQiJ,GAAY,IAAI,CAAC/T,OAAO,CAC5B,IAAI,CAACI,KAAK,CAACE,QAAQ,CAAC4T,SAAS,CAACjW,EAAG,IAAI,CAAC+B,OAAO,CAACmU,WAAW,IACzD,IACR,CACArD,eAAgB,CACZ,OAAO,KAAK,CAACA,iBAAmB,CAAC,CAAC,IAAI,CAACjR,OAAO,CAAC5B,CAAC,AACpD,CACAuM,OAAO4J,CAAM,CAAE,CACX,IAAMvU,EAAU,IAAI,CAACA,OAAO,CAAE2Q,EAAQ,IAAI,CAACJ,gBAAgB,CAACvQ,EAC5D,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC+T,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBpU,IAAI,CAACuQ,GACL3P,GAAG,CAACuT,GACT,IAAI,CAAC3D,OAAO,CAAG,IAAI,CAAC9Q,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC+T,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBzD,QAAQ,CAAC,2BACT3Q,IAAI,CAAC,CACNU,OAAQ,CACZ,GACKE,GAAG,CAACuT,GACJ,IAAI,CAACzU,UAAU,CAACS,KAAK,CAACmH,UAAU,EACjC,IAAI,CAACkJ,OAAO,CAACxQ,IAAI,CAAC,CACd,kBAAmB,QACnBiT,OAAQR,EACRU,KAAMV,EACN,eAAgB,IAAI,CAAC1S,OAAO,CAACmU,WAAW,GACpCtU,AAAe,EAAfA,EAAQyU,IAAI,AACpB,GAEJ,KAAK,CAAC9J,SACN4H,EAAO,IAAI,CAACpS,OAAO,CAAE,CAAEyS,kBAAAA,EAAmBF,gBAAAA,CAAgB,GAC1D,IAAI,CAACgC,UAAU,CAAC,IAAI,CACxB,CACA7U,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAM/B,EAAI,IAAI,CAAC4V,GAAG,GAAIW,EAASlK,EAAY,UAAY,OACnDrM,GACA,IAAI,CAAC+B,OAAO,CAACwU,EAAO,CAAC,CAAEvW,EAAGA,CAAE,GAC5B,IAAI,CAACwS,OAAO,CAAC+D,EAAO,CAAC,CAAEvW,EAAGA,CAAE,KAG5B,IAAI,CAAC+B,OAAO,CAACC,IAAI,CAAC,CAAEhC,EAAG,iBAAc,GACrC,IAAI,CAACwS,OAAO,CAACxQ,IAAI,CAAC,CAAEhC,EAAG,iBAAc,IAEzC,IAAI,CAAC+B,OAAO,CAACyU,MAAM,CAAG,IAAI,CAAChE,OAAO,CAACgE,MAAM,CAAG,CAAC,CAACxW,CAClD,CACA,KAAK,CAACyB,OAAO4K,EACjB,CAMAiK,WAAWG,CAAI,CAAE,CACb,IAAMC,EAAcD,EAAK7U,OAAO,CAAEO,EAAQsU,EAAKtU,KAAK,CAAE2S,EAAO3S,EAAMP,OAAO,CAACkT,IAAI,CAAEK,EAAOuB,EAAYvB,IAAI,CAAED,EAAQhB,EAAyBiB,IAASA,AAAS,SAATA,EAChJA,EACAuB,EAAYzB,MAAM,CAuBtB,CAAC,cAAe,YAAY,CACvBpS,OAAO,CAvBM,SAAU8R,CAAU,EAClC,IACIgC,EAAKC,EAAkBxW,EAAKmV,EAD1BsB,EAAWH,CAAW,CAAC/B,EAAW,CAExC,GAAIkC,EAAU,CACV,IAAKzW,KAAO0U,EAER,GAAI,AAAC+B,CAAAA,IAAcF,CAAAA,AADnBA,CAAAA,EAAM7B,CAAI,CAAC1U,EAAI,AAAD,EACSuT,UAAU,EAAIgD,EAAIhD,UAAU,CAACxN,EAAE,AAAD,GAGjD0Q,IAAaF,EAAIxQ,EAAE,AAAD,GAClBwQ,AAAgB,WAAhBA,EAAIjD,OAAO,CAAe,CAC1BkD,EAAmBD,EACnB,KACJ,CAEAC,IACArB,EAASkB,CAAI,CAAC9B,EAAW,CAAGxS,EAAME,QAAQ,CACrCsT,SAAS,CAAC,AAACe,CAAAA,EAAYvQ,EAAE,EAAIkO,GAAU,EAAK,IAAMwC,EAAUzC,EAAuBwC,EAAkB,CAAE1B,MAAOA,CAAM,IACzHuB,EAAKzU,IAAI,CAAC2S,EAAYY,EAAOuB,YAAY,CAAC,OAElD,CACJ,EAGJ,CACJ,CAYAtB,GAAiBlD,QAAQ,CAAG,CACxByE,UAAW,YACXb,YAAa,eACbjB,OAAQ,SACRE,KAAM,OACNzS,OAAQ,QACZ,EAkBA,GAAM,CAAEuJ,MAAO+K,EAAsB,CAAE,CAAIhW,GAwB3C,OAAMiW,WAAyB3D,EAM3BlH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,OACZ,IAAI,CAAC+D,SAAS,CAAG,KAAK,CAACwH,cAC3B,CAMAvG,OAAO4J,CAAM,CAAE,CACX,IAAM5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACvQ,OAAO,CAChD,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC6U,IAAI,CAAC,EAAG,KAAM,EAAG,GACjBlV,IAAI,CAACuQ,GACL3P,GAAG,CAACuT,GACT,KAAK,CAAC5J,QACV,CACA9K,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAMgU,EAAW,IAAI,CAACrG,MAAM,CAAC,IAAI,CAAChL,MAAM,CAAC,EAAE,EAAE2L,gBAAgB,CACzD0F,EACA,IAAI,CAAChU,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCzH,EAAGmR,EAASnR,CAAC,CACb6E,EAAGsM,EAAStM,CAAC,CACbgD,MAAO,IAAI,CAAC7K,OAAO,CAAC6K,KAAK,CACzBC,OAAQ,IAAI,CAAC9K,OAAO,CAAC8K,MAAM,AAC/B,GAGA,IAAI,CAAC1K,IAAI,CAAC,CACN4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACyU,MAAM,CAAGW,CAAAA,CAAQpB,CAClC,CACA,KAAK,CAACtU,OAAO4K,EACjB,CACJ,CAWA4K,GAAiB3E,QAAQ,CAAG0E,GAAuBI,AAjGkB5B,GAiGalD,QAAQ,CAAE,CACxF7F,MAAO,QACPC,OAAQ,QACZ,GAkBA,GAAM,CAAET,MAAOoL,EAAwB,CAAE,CAAIrW,GAmB7C,OAAMsW,WAA2BhE,EAM7BlH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,SACZ,IAAI,CAAC+D,SAAS,CAAG,KAAK,CAACwH,cAC3B,CASArR,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAMgU,EAAW,IAAI,CAACrG,MAAM,CAAC,IAAI,CAAChL,MAAM,CAAC,EAAE,EAAE2L,gBAAgB,CACzD0F,EACA,IAAI,CAAChU,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCzH,EAAGmR,EAASnR,CAAC,CACb6E,EAAGsM,EAAStM,CAAC,CACb8N,EAAG,IAAI,CAAC3V,OAAO,CAAC2V,CAAC,AACrB,GAGA,IAAI,CAACxV,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACyU,MAAM,CAAG,CAAC,CAACT,CAC5B,CACA,KAAK,CAACtU,OAAOX,IAAI,CAAC,IAAI,CAAEuL,EAC5B,CAIAE,OAAO4J,CAAM,CAAE,CACX,IAAM5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACvQ,OAAO,CAChD,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCmV,MAAM,CAAC,EAAG,KAAM,GAChBxV,IAAI,CAACuQ,GACL3P,GAAG,CAACuT,GACT,KAAK,CAAC5J,QACV,CAOAkL,UAAUF,CAAC,CAAE,CACT,IAAI,CAAC3V,OAAO,CAAC2V,CAAC,CAAGA,CACrB,CACJ,CAaAD,GAAmBhF,QAAQ,CAAG+E,GAAyBD,AAtNc5B,GAsNiBlD,QAAQ,CAAE,CAAEiF,EAAG,GAAI,GAoBzG,GAAM,CAAEtL,MAAOyL,EAAyB,CAAEjQ,QAASkQ,EAA2B,CAAE,CAAI3W,GAmBpF,OAAM4W,WAA4BtE,EAM9BlH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,SAChB,CASA0K,KAAKvQ,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACzB2S,GAA4B/V,EAAQ4L,KAAK,GACzC5L,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACpBA,EAAMC,KAAK,CAAG5L,EAAQ4L,KAAK,AAC/B,GAEAmK,GAA4B/V,EAAQmD,KAAK,GACzCnD,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACpBA,EAAMxI,KAAK,CAAGnD,EAAQmD,KAAK,AAC/B,GAEJ,KAAK,CAACkN,KAAKvQ,EAAYE,EAASoD,EACpC,CAOAuH,OAAO4J,CAAM,CAAE,CACX,IAAI,CAACpU,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CAACwV,aAAa,CAAC,WACvD7V,IAAI,CAAC,IAAI,CAACmQ,gBAAgB,CAAC,IAAI,CAACvQ,OAAO,GACvCgB,GAAG,CAACuT,GACT,KAAK,CAAC5J,QACV,CAKAjB,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,KAAK,CAACwI,eAAetI,EAAIF,EAAI,CAAA,EACjC,CAaAwN,oBAAoBC,CAAM,CAAEC,CAAM,CAAEC,CAAE,CAAEC,CAAE,CAAE,CACxC,OAAO5S,KAAK6S,GAAG,CAAC,AAACH,CAAAA,EAAOvO,CAAC,CAAGsO,EAAOtO,CAAC,AAADA,EAAKwO,EAAK,AAACD,CAAAA,EAAOpT,CAAC,CAAGmT,EAAOnT,CAAC,AAADA,EAAKsT,EACjEF,EAAOpT,CAAC,CAAGmT,EAAOtO,CAAC,CAAGuO,EAAOvO,CAAC,CAAGsO,EAAOnT,CAAC,EAAIU,KAAK8S,IAAI,CAAC,AAACJ,CAAAA,EAAOvO,CAAC,CAAGsO,EAAOtO,CAAC,AAADA,EAAMuO,CAAAA,EAAOvO,CAAC,CAAGsO,EAAOtO,CAAC,AAADA,EAClG,AAACuO,CAAAA,EAAOpT,CAAC,CAAGmT,EAAOnT,CAAC,AAADA,EAAMoT,CAAAA,EAAOpT,CAAC,CAAGmT,EAAOnT,CAAC,AAADA,EACnD,CAUAyT,SAAStC,CAAQ,CAAEuC,CAAS,CAAE,CAC1B,IAAMC,EAAKxC,EAASnR,CAAC,CAAE4T,EAAKzC,EAAStM,CAAC,CAAEgP,EAAKH,EAAU1T,CAAC,CAAE8T,EAAKJ,EAAU7O,CAAC,CAAEO,EAAK,AAACuO,CAAAA,EAAKE,CAAC,EAAK,EAAuBE,EAAKrT,KAAK8S,IAAI,CAAC,AAACG,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,EAAI,AAACD,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,GACnLE,EAAQtT,AAAiB,IAAjBA,KAAKuT,IAAI,CAD4K,AAACH,CAAAA,EAAKF,CAAC,EAAMC,CAAAA,EAAKF,CAAC,GACjLjT,KAAKwT,EAAE,CAK1C,OAJI9O,EAAKuO,GACLK,CAAAA,GAAS,GAAE,EAGR,CAAE5O,GAAAA,EAAIC,GANwF,AAACuO,CAAAA,EAAKE,CAAC,EAAK,EAMhGC,GAAAA,EAAII,GADV,IAAI,CAACC,KAAK,GACIJ,MAAAA,CAAM,CACnC,CAKAI,OAAQ,CACJ,IAAMxL,EAAQ,IAAI,CAACyL,QAAQ,GAC3B,OAAOtB,GAA4BnK,GAC/BlI,KAAK6S,GAAG,CAAC3K,EAAMmB,QAAQ,CAAC,IAAI,CAAC/M,OAAO,CAACmX,EAAE,EAAIvL,EAAMmB,QAAQ,CAAC,IAC1D,IAAI,CAAC/M,OAAO,CAACmX,EAAE,AACvB,CAKAE,UAAW,CACP,IAAMC,EAAa,IAAI,CAACtX,OAAO,CAAC4L,KAAK,CACrC,OAAO,IAAI,CAACrL,KAAK,CAACqL,KAAK,CAAC0L,EAAW,AACvC,CAOAC,oBAAoB5L,CAAK,CAAE,CACvB,OAAO,IAAI,CAACmC,MAAM,CAACnC,GAAO8C,gBAAgB,AAC9C,CAOA5O,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAMgU,EAAW,IAAI,CAACoD,mBAAmB,CAAC,IAAI,CAACzU,MAAM,CAAC,EAAE,EAAG4T,EAAY,IAAI,CAACa,mBAAmB,CAAC,IAAI,CAACzU,MAAM,CAAC,EAAE,EAAG6N,EAAQ,IAAI,CAAC8F,QAAQ,CAACtC,EAAUuC,GAC7IvC,EACA,IAAI,CAAChU,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCrC,GAAIuI,EAAMvI,EAAE,CACZC,GAAIsI,EAAMtI,EAAE,CACZ0O,GAAIpG,EAAMoG,EAAE,CACZI,GAAIxG,EAAMwG,EAAE,CACZK,SAAU7G,EAAMqG,KAAK,CACrBS,gBAAiB9G,EAAMvI,EAAE,CACzBsP,gBAAiB/G,EAAMtI,EAAE,AAC7B,GAGA,IAAI,CAAClI,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACyU,MAAM,CAAGW,CAAAA,CAAQpB,CAClC,CACA,KAAK,CAACtU,OAAO4K,EACjB,CAOAkN,WAAWR,CAAE,CAAE,CACX,IAAMxN,EAAS,IAAI,CAAC7J,UAAU,CAACF,WAAW,CAAC+J,MAAM,AACjD,CAAA,IAAI,CAAC3J,OAAO,CAACmX,EAAE,CAAGA,EACdxN,GAAUA,CAAM,CAAC,EAAE,GACnBA,CAAM,CAAC,EAAE,CAACwN,EAAE,CAAGA,EACfxN,CAAM,CAAC,EAAE,CAACwN,EAAE,CAAGA,EAEvB,CACJ,CAaAnB,GAAoBtF,QAAQ,CAAGoF,GAA0BN,AA3aY5B,GA2amBlD,QAAQ,CAAE,CAC9FyG,GAAI,IACR,GASA,IAAMS,GAAgEja,EAAwD,OAAU,CAACka,UAAU,CAW7I,CAAEC,OAAAA,EAAM,CAAE,CAAIC,AAVqDha,EAAoBC,CAAC,CAAC4Z,MAazF,CAAErF,OAAQyF,EAAwB,CAAEC,eAAAA,EAAc,CAAEC,SAAAA,EAAQ,CAAEzY,KAAM0Y,EAAsB,CAAE,CAAI/Y,IAUtG,SAASgZ,GAAgBpV,CAAC,CAAE6E,CAAC,CAAEwQ,CAAC,CAAEC,CAAC,CAAEtY,CAAO,EACxC,IAAMuY,EAAUvY,GAAWA,EAAQuY,OAAO,CAAEC,EAAUxY,GAAWA,EAAQwY,OAAO,CAC5EhE,EAAMiE,EAASC,EAAUL,EAAI,EA4BjC,OA3BIH,GAASK,IAAYL,GAASM,KAC9BhE,EAAO,CAAC,CAAC,IAAK+D,EAASC,EAAQ,CAAC,CAEhCC,CAAAA,EAAU5Q,EAAI2Q,CAAM,EACN,GACVC,CAAAA,EAAU,CAACH,EAAIG,CAAM,EAErBA,EAAUJ,GACVK,CAAAA,EAAUH,EAAUvV,EAAKqV,EAAI,EAAKI,EAAUJ,EAAII,CAAM,EAGtDD,EAAU3Q,EAAIyQ,EACd9D,EAAKtU,IAAI,CAAC,CAAC,IAAK8C,EAAI0V,EAAS7Q,EAAIyQ,EAAE,EAG9BE,EAAU3Q,EACf2M,EAAKtU,IAAI,CAAC,CAAC,IAAK8C,EAAI0V,EAAS7Q,EAAE,EAG1B0Q,EAAUvV,EACfwR,EAAKtU,IAAI,CAAC,CAAC,IAAK8C,EAAG6E,EAAIyQ,EAAI,EAAE,EAGxBC,EAAUvV,EAAIqV,GACnB7D,EAAKtU,IAAI,CAAC,CAAC,IAAK8C,EAAIqV,EAAGxQ,EAAIyQ,EAAI,EAAE,GAGlC9D,GAAQ,EAAE,AACrB,CAsBA,MAAMmE,WAA0BjH,EAkB5B,OAAOkH,gBAAgBC,CAAY,CAAExK,CAAG,CAAE,CACtC,MAAO,CACHrL,EAAGU,KAAKoV,KAAK,CAAC,AAACzK,CAAAA,EAAIrL,CAAC,EAAI,CAAA,EAAM6V,CAAAA,EAAa7V,CAAC,EAAI,CAAA,EAC5C,AAACqL,CAAAA,EAAIxD,KAAK,CAAIgO,CAAAA,EAAahO,KAAK,EAAI,CAAA,CAAC,EACjCoN,GAAeY,EAAaE,KAAK,GACzClR,EAAGnE,KAAKoV,KAAK,CAAC,AAACzK,CAAAA,EAAIxG,CAAC,EAAI,CAAA,EAAMgR,CAAAA,EAAahR,CAAC,EAAI,CAAA,EAC5C,AAACwG,CAAAA,EAAIvD,MAAM,CAAI+N,CAAAA,EAAa/N,MAAM,EAAI,CAAA,CAAC,EACnCmN,GAAeY,EAAaG,aAAa,EACrD,CACJ,CACA,OAAOjU,QAAQ8O,CAAgB,CAAE,CAE7BoF,AADgBpF,EAAiB7U,SAAS,CAACia,OAAO,CAC1CC,SAAS,CAAGd,EACxB,CAOA,OAAOe,iBAAiB5Y,CAAK,CAAEoC,CAAK,CAAEkW,CAAY,CAAEO,CAAS,CAAE,CAC3D,IAYIC,EAZEN,EAAQF,EAAaE,KAAK,CAAEC,EAAgBH,EAAaG,aAAa,CAAEM,EAAU3W,EAAM0L,GAAG,CAAG,EAAK1L,EAAM2W,OAAO,EAAI,EAAIC,EAAO5W,EAAM6W,OAAO,GAElJxZ,EAAU,CACN+Y,MAAOA,EACPC,cAAeA,EACfhW,EAAG6V,EAAa7V,CAAC,CACjB6E,EAAGgR,EAAahR,CAAC,CACjBgD,MAAOlI,EAAMkI,KAAK,CAClBC,OAAQnI,EAAMmI,MAAM,AACxB,EAEA9H,EAAI,AAACoW,CAAAA,EAAUpW,CAAC,EAAI,CAAA,EAAKzC,EAAM+I,QAAQ,CAAEzB,EAAI,AAACuR,CAAAA,EAAUvR,CAAC,EAAI,CAAA,EAAKtH,EAAMgJ,OAAO,CA0C/E,MAvCA8P,CAAAA,EAAMrW,EAAIsW,CAAM,EACN,IACFP,AAAU,UAAVA,EACA/Y,EAAQ+Y,KAAK,CAAG,OAGhB/Y,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKqW,GAIvCA,CAAAA,EAAMrW,EAAIuW,EAAK1O,KAAK,CAAGyO,CAAM,EACnB/Y,EAAM2L,SAAS,GACjB6M,AAAU,SAAVA,EACA/Y,EAAQ+Y,KAAK,CAAG,QAGhB/Y,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKzC,EAAM2L,SAAS,CAAGmN,GAIzDA,CAAAA,EAAMxR,EAAIyR,CAAM,EACN,IACFN,AAAkB,WAAlBA,EACAhZ,EAAQgZ,aAAa,CAAG,MAGxBhZ,EAAQ6H,CAAC,CAAG,AAAC7H,CAAAA,EAAQ6H,CAAC,EAAI,CAAA,EAAKwR,GAIvCA,CAAAA,EAAMxR,EAAI0R,EAAKzO,MAAM,CAAGwO,CAAM,EACpB/Y,EAAM4L,UAAU,GAClB6M,AAAkB,QAAlBA,EACAhZ,EAAQgZ,aAAa,CAAG,SAGxBhZ,EAAQ6H,CAAC,CAAG,AAAC7H,CAAAA,EAAQ6H,CAAC,EAAI,CAAA,EAAKtH,EAAM4L,UAAU,CAAGkN,GAGnDrZ,CACX,CAMAwK,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,QACtC,CAaAyM,eAAejH,CAAE,CAAEF,CAAE,CAAE,CACnB,KAAK,CAACmH,eAAejH,EAAIF,EAAI,EACjC,CAOAgB,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,IAAMnI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAEnCiC,EAAe,IAAI,CAAC1C,UAAU,CAACF,WAAW,CAE1CyR,EAAkB9Q,EAAMN,WAAW,CAACqH,OAAO,CAAC,IAAI,CAACxH,UAAU,EAAiDwR,EAAemI,AAA1ClZ,EAAMP,OAAO,CAACC,WAAW,AAAiC,CAACoR,EAAgB,CAC5J,GAAI9Q,EAAMwI,QAAQ,CAAE,CAChB,IAAMD,EAAOF,EACbA,EAAKF,EACLA,EAAKI,CACT,CAEA,IAAI,CAAC9I,OAAO,CAACgD,CAAC,EAAI4F,EAClB,IAAI,CAAC5I,OAAO,CAAC6H,CAAC,EAAIa,EAElB4I,CAAY,CAAC,IAAI,CAAClB,UAAU,CAAC,CAAC,IAAI,CAAChN,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DsO,CAAY,CAAC,IAAI,CAAClB,UAAU,CAAC,CAAC,IAAI,CAAChN,KAAK,CAAC,CAACyE,CAAC,CAAG,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,CAC5DrF,CAAY,CAAC,IAAI,CAAC4N,UAAU,CAAC,CAAC,IAAI,CAAChN,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DR,CAAY,CAAC,IAAI,CAAC4N,UAAU,CAAC,CAAC,IAAI,CAAChN,KAAK,CAAC,CAACyE,CAAC,CAAG,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,AAChE,CACA8C,OAAO4J,CAAM,CAAE,CACX,IAAMvU,EAAU,IAAI,CAACA,OAAO,CAAE2Q,EAAQ,IAAI,CAACJ,gBAAgB,CAACvQ,GAAU+K,EAAQ/K,EAAQ+K,KAAK,AAC3F,CAAA,IAAI,CAAC5K,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCkC,KAAK,CAAC,GAAI,EAAG,MAClB3C,EAAQ4J,KAAK,CAAE,KAAM,KAAM5J,EAAQ6G,OAAO,CAAE,KAAM,oBAC7CzG,IAAI,CAACuQ,GACL3P,GAAG,CAACuT,GACJ,IAAI,CAACzU,UAAU,CAACS,KAAK,CAACmH,UAAU,GACb,aAAhBqD,EAAMuI,KAAK,EACXvI,CAAAA,EAAMuI,KAAK,CAAG,IAAI,CAACxT,UAAU,CAACS,KAAK,CAACE,QAAQ,CAACiZ,WAAW,CAACf,GAAkBgB,uBAAuB,CAACrS,OAAO,CAACtH,EAAQ4J,KAAK,EAAI,GAAK,UAAY5J,EAAQ4Z,eAAe,CAAA,EAExK,IAAI,CAACzZ,OAAO,CACP4H,GAAG,CAAC/H,EAAQ+K,KAAK,EACjB8O,MAAM,CAAC7Z,EAAQ6Z,MAAM,GAE9B,IAAI,CAAC1Z,OAAO,CAAC2Z,SAAS,CAAG9Z,EAAQ8Z,SAAS,CAC1C,KAAK,CAACnP,QACV,CACA9K,OAAO4K,CAAS,CAAE,CACd,IAAMzK,EAAU,IAAI,CAACA,OAAO,CAAE4C,EAAO,IAAI,CAACA,IAAI,EAAI5C,EAAQ8X,MAAM,EAAI9X,EAAQ4C,IAAI,CAAED,EAAQ,IAAI,CAACxC,OAAO,CAAEwL,EAAQ,IAAI,CAAC7I,MAAM,CAAC,EAAE,CAC9H,GAAI,CAACH,EAAO,CACR,IAAI,CAAC9C,MAAM,CAAC4K,GACZ,MACJ,CACA9H,EAAMvC,IAAI,CAAC,CACPwC,KAAMA,EACFkV,GAAOiC,OAAOnX,GAAO+I,EAAO,IAAI,CAAC7L,UAAU,CAACS,KAAK,EACjDP,EAAQga,SAAS,CAAC9a,IAAI,CAACyM,EAAO,IAAI,CAC1C,GACA,IAAMmC,EAAS,IAAI,CAACA,MAAM,CAACnC,GACrBgF,EAAQ,IAAI,CAACwD,QAAQ,CAACrG,GACxB6C,GACAhO,EAAMyW,SAAS,CAAGzI,EAClBA,EAAM4H,OAAO,CAAGzK,EAAOW,gBAAgB,CAACzL,CAAC,CACzC2N,EAAM6H,OAAO,CAAG1K,EAAOW,gBAAgB,CAAC5G,CAAC,CACzClF,CAAK,CAAC8H,EAAY,UAAY,OAAO,CAACkG,IAGtChO,EAAMvC,IAAI,CAAC,CACP4C,EAAG,EACH6E,EAAG,KACP,GAEJlF,EAAMiS,MAAM,CAAG,CAAC,CAACjE,EACjB,KAAK,CAAC9Q,OAAO4K,EACjB,CAMAqD,OAEA4B,CAAM,CAAE,CACJ,IAAM5B,EAAS,KAAK,CAACA,OAAOlJ,KAAK,CAAC,IAAI,CAAEE,WAAY9B,EAAI,IAAI,CAAChD,OAAO,CAACgD,CAAC,EAAI,EAAG6E,EAAI,IAAI,CAAC7H,OAAO,CAAC6H,CAAC,EAAI,EAKnG,OAJAiG,EAAOW,gBAAgB,CAACzL,CAAC,EAAIA,EAC7B8K,EAAOW,gBAAgB,CAAC5G,CAAC,EAAIA,EAC7BiG,EAAOU,gBAAgB,CAACxL,CAAC,EAAIA,EAC7B8K,EAAOU,gBAAgB,CAAC3G,CAAC,EAAIA,EACtBiG,CACX,CAIAqG,SAASrG,CAAM,CAAE,CACb,IAAM+G,EAAO,IAAI,CAAC1U,OAAO,CAAEI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAAE+N,EAAU/N,EAAM+N,OAAO,CAAE3C,EAAQ,IAAI,CAAC7I,MAAM,CAAC,EAAE,CAAEgS,EAAc,IAAI,CAAC9U,OAAO,CAAEia,EAAyBnM,EAAOW,gBAAgB,CAAEyL,EAAyBpM,EAAOU,gBAAgB,CACrO2L,EAAcC,EAASC,EAAkBC,EAAkBC,EAAW5O,EAAMzI,MAAM,CAAC+H,OAAO,EAC1F2E,AA1qDgDnE,EA0qD1BzM,SAAS,CAACqK,YAAY,CAACnK,IAAI,CAACyM,GACtD,GAAIkJ,GAAQ0F,EAAU,CAClB,GAAM,CAAE1P,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAG+J,CAC9BC,CAAAA,EAAY0F,QAAQ,EAAIlM,EACxB6L,EAAe7L,EAAQmM,WAAW,CAACvb,IAAI,CAAC,CACpCqB,MAAAA,EACAia,SAAUrC,GAAuBrD,EAAY0F,QAAQ,CAAE,IACvDE,gBAAiBpM,EAAQoM,eAAe,CACxCxT,QAASoH,EAAQpH,OAAO,AAC5B,EAAG2D,EAAOC,EAAQ,CACdiB,MAAOmO,EAAuBlX,CAAC,CAC/BgJ,MAAOkO,EAAuBrS,CAAC,CAC/B8S,SAAUhP,EAAMgP,QAAQ,CACxBC,QAASjP,EAAMiP,OAAO,CACtBtC,EAAI4B,EAAuBpP,MAAM,EAC7BoP,EAAuBrP,KAAK,AACpC,GAEKiK,EAAYpK,UAAU,CAC3ByP,EAAerF,EAAYpK,UAAU,CAACxL,IAAI,CAAC,IAAI,GAG/Ckb,EAAU,CACNpX,EAAGiX,EAAuBjX,CAAC,CAC3B6E,EAAGoS,EAAuBpS,CAAC,CAC3BgD,MAAO,EACPC,OAAQ,CACZ,EACAqP,EAAexB,GAAkBC,eAAe,CAACZ,GAAyBlD,EAAa,CACnFjK,MAAAA,EACAC,OAAAA,CACJ,GAAIsP,GAC0B,YAA1B,IAAI,CAACpa,OAAO,CAAC6a,QAAQ,EACrBV,CAAAA,EAAexB,GAAkBC,eAAe,CAACD,GAAkBQ,gBAAgB,CAAC5Y,EAAOsU,EAAMC,EAAaqF,GAAeC,EAAO,GAGxItF,EAAYgG,IAAI,GAChBT,EAAmBF,EAAanX,CAAC,CAAGzC,EAAM+I,QAAQ,CAClDgR,EAAmBH,EAAatS,CAAC,CAAGtH,EAAMgJ,OAAO,CACjDgR,EACIha,EAAM8I,YAAY,CAACgR,EAAkBC,IACjC/Z,EAAM8I,YAAY,CAACgR,EAAmBxP,EAAOyP,EAAmBxP,GAEhF,CACA,OAAOyP,EAAWJ,EAAe,IACrC,CACJ,CAWAxB,GAAkBjI,QAAQ,CAAG,CACzBkJ,gBAAiB,OACjBmB,YAAa,SACbC,YAAa,eACbla,OAAQ,SACRma,aAAc,IACd3B,QAAS,SACb,EAOAX,GAAkBgB,uBAAuB,CAAG,CAAC,YAAY,AAwCzD,OAAMuB,WAA0BxJ,EAM5BlH,YAAY1K,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAE,CACpC,KAAK,CAACtD,EAAYE,EAASoD,EAAO,SAMlC,IAAI,CAACuC,IAAI,CAAG,QACZ,IAAI,CAAC+D,SAAS,CAAG,KAAK,CAACwH,cAC3B,CACAvG,OAAO4J,CAAM,CAAE,CACX,IAAM5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACvQ,OAAO,EAAGA,EAAU,IAAI,CAACA,OAAO,AACzE,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC0a,KAAK,CAACnb,EAAQob,GAAG,CAAE,EAAG,KAAMpb,EAAQ6K,KAAK,CAAE7K,EAAQ8K,MAAM,EACzD1K,IAAI,CAACuQ,GACL3P,GAAG,CAACuT,GACT,IAAI,CAACpU,OAAO,CAAC0K,KAAK,CAAG7K,EAAQ6K,KAAK,CAClC,IAAI,CAAC1K,OAAO,CAAC2K,MAAM,CAAG9K,EAAQ8K,MAAM,CACpC,KAAK,CAACH,QACV,CACA9K,OAAO4K,CAAS,CAAE,CACd,GAAI,IAAI,CAACtK,OAAO,CAAE,CACd,IAAM2N,EAAS,IAAI,CAACA,MAAM,CAAC,IAAI,CAAChL,MAAM,CAAC,EAAE,EAAGqR,EAAWkH,AA9DG1C,GA8D6B3Z,SAAS,CAACmV,QAAQ,CAACjV,IAAI,CAAC,IAAI,CAAE4O,GACjHqG,EACA,IAAI,CAAChU,OAAO,CAACsK,EAAY,UAAY,OAAO,CAAC,CACzCzH,EAAGmR,EAASnR,CAAC,CACb6E,EAAGsM,EAAStM,CAAC,AACjB,GAGA,IAAI,CAAC1H,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH6E,EAAG,IACP,GAEJ,IAAI,CAAC1H,OAAO,CAACyU,MAAM,CAAGW,CAAAA,CAAQpB,CAClC,CACA,KAAK,CAACtU,OAAO4K,EACjB,CACJ,CAYAyQ,GAAkBxK,QAAQ,CAAG,CACzB7F,MAAO,QACPC,OAAQ,SACRhK,OAAQ,QACZ,EASA,IAAMwa,GAAyD3d,EAAwD,OAAU,CAAC4d,GAAG,CACrI,IAAIC,GAA8Dzd,EAAoBC,CAAC,CAACsd,IAmBxF,GAAM,CAAEjc,SAAUoc,EAAiB,CAAExF,cAAAA,EAAa,CAAE,CAAI7W,IAyGrBsc,GAnGnC,MAMIlR,YAAYmR,CAAS,CAAEC,CAAQ,CAAE,CAC7B,IAAI,CAACA,QAAQ,CAAGA,EAChB,IAAI,CAACC,SAAS,CAAG,IAAI,CAACC,oBAAoB,CAACH,GAC3C,IAAI,CAACI,WAAW,CAAG,IAAI,CAACC,cAAc,EAC1C,CAkBAF,qBAAqBH,CAAS,CAAE7K,EAAY,wCAAwC,CAAE,CAClF,OAAOmF,GAAc,MAAO,CAAEnF,UAAAA,CAAU,EAAG,KAAK,EAAG6K,EACvD,CAUAK,eAAelL,EAAY,wBAAwB,CAAE,CACjD,IAAMmL,EAAQ,IAAI,CAAEL,EAAW,IAAI,CAACA,QAAQ,CAEtCG,EAAc9F,GAAc,SAAU,CAAEnF,UAAAA,CAAU,EAAG,KAAK,EAAG,IAAI,CAAC+K,SAAS,EAajF,OAZAE,EAAYhR,KAAK,CAAC,mBAAmB,CAAG,OACnC6Q,CAAAA,EAASM,KAAK,CAAC,0BACZN,EAAWA,EAAW,WAAU,EAAK,IAC7C,CAAC,QAAS,aAAa,CAAC3a,OAAO,CAAC,AAACkb,IAC7BV,GAAkBM,EAAaI,EAAWF,EAAMG,iBAAiB,CAACC,IAAI,CAACJ,GAC3E,GAEAR,GAAkBa,SAAU,UAAW,SAAU/a,CAAK,EAC/B,WAAfA,EAAMgb,IAAI,EACVN,EAAMG,iBAAiB,EAE/B,GACOL,CACX,CAKAK,mBAAoB,CAChB,IAAI,CAACI,UAAU,EACnB,CAOAC,UAAUC,EAAe,+BAA+B,CAAE,CACtD,IAAMC,EAAW,IAAI,CAACd,SAAS,CAAEe,EAAmB,IAAI,CAACb,WAAW,AACpE,CAAA,IAAI,CAACpW,IAAI,CAAG,KAAK,EAEjBgX,EAASE,SAAS,CAAG,AAACrB,KAAkDsB,SAAS,CAE7EH,EAAS7L,SAAS,CAACxJ,OAAO,CAACoV,IAAiB,IAC5CC,EAASI,SAAS,CAACC,MAAM,CAACN,GAE1BC,EAASM,eAAe,CAAC,UAG7BN,EAASO,WAAW,CAACN,GACrBD,EAAS5R,KAAK,CAACoS,OAAO,CAAG,QACzBR,EAAS5R,KAAK,CAACD,MAAM,CAAG,EAC5B,CAIA0R,YAAa,CACT,IAAI,CAACX,SAAS,CAAC9Q,KAAK,CAACoS,OAAO,CAAG,MACnC,CACJ,EAsBM,CAAErX,IAAKsX,EAAoB,CAAEC,UAAAA,EAAS,CAAE,CAAIje,IAE5C,CAAE6W,cAAeqH,EAA8B,CAAEC,QAAAA,EAAO,CAAEtO,SAAAA,EAAQ,CAAE/I,WAAYsX,EAA2B,CAAE/d,KAAMge,EAAqB,CAAEC,WAAAA,EAAU,CAAE,CAAIte,IAiGhK,SAASue,GAAchC,CAAS,CAAEpb,CAAK,CAAEqd,CAAU,CAAE5d,CAAO,CAAE6d,CAAO,CAAEC,CAAM,MAKrEC,EAAgBC,EAJpB,GAAI,CAACzd,EACD,OAEJ,IAAM0d,EAAW,IAAI,CAACA,QAAQ,CAAEjc,EAAO,IAAI,CAACA,IAAI,CAEhDwb,GAA4Bxd,EAAS,CAACgT,EAAOkL,KAEzCH,EAAiBH,AAAe,KAAfA,EAAoBA,EAAa,IAAMM,EAASA,EAC7DjP,GAAS+D,KAGT,CAACuK,GAAQvK,IAEJuK,GAAQvK,IAAU/D,GAAS+D,CAAK,CAAC,EAAE,GAE/BgL,AADLA,CAAAA,EAAYhc,CAAI,CAACkc,EAAO,EAAIA,CAAK,EAClBhC,KAAK,CAAC,QACjB2B,EAAQ3d,IAAI,CAAC,CACT,CAAA,EACA8d,EACArC,EACH,EAELgC,GAAcze,IAAI,CAAC,IAAI,CAAEyc,EAAWpb,EAAOwd,EAAgB/K,EAAO6K,EAAS,CAAA,IAG3EA,EAAQ3d,IAAI,CAAC,CACT,IAAI,CACJ6d,EACA,aACApC,EACA3I,EACH,EAGb,GACI8K,IACAJ,GAAWG,EAAS,AAACxf,GAAOA,CAAC,CAAC,EAAE,CAAC6d,KAAK,CAAC,WAAa,GAAK,GACrDmB,IACAQ,EAAQM,OAAO,GAEnBN,EAAQ5c,OAAO,CAAC,AAACmd,IACTA,AAAgB,CAAA,IAAhBA,CAAQ,CAAC,EAAE,CACXd,GAA+B,OAAQ,CACnCxM,UAAW,6BACf,EAAG,KAAK,EAAGsN,CAAQ,CAAC,EAAE,EAAElB,WAAW,CAACE,GAAqBiB,cAAc,CAACD,CAAQ,CAAC,EAAE,IAGnFA,CAAQ,CAAC,EAAE,CAAG,CACVpL,MAAOoL,CAAQ,CAAC,EAAE,CAAC,EAAE,CACrBzY,KAAMyY,CAAQ,CAAC,EAAE,CAAC,EAAE,AACxB,EACAH,EAASrZ,KAAK,CAACwZ,CAAQ,CAAC,EAAE,CAAEA,EAASE,MAAM,CAAC,IAEpD,GAER,CA2BA,GAAM,CAAExY,IAAKyY,EAAmB,CAAE,CAAInf,IAEhC,CAAEof,YAAAA,EAAW,CAAE,CAAInT,IAEnB,CAAEhM,SAAUof,EAAwB,CAAExI,cAAeyI,EAA6B,CAAE7Y,QAAS8Y,EAAuB,CAAEpB,QAASqB,EAAuB,CAAE3P,SAAU4P,EAAwB,CAAE3Y,WAAY4Y,EAA0B,CAAEpB,WAAYqB,EAA0B,CAAE,CAAI3f,GAYlRxB,EADOA,EAGRA,GAAuBA,CAAAA,EAAqB,CAAC,CAAA,EAF1B,CAACA,CAAkB,CAAC,mBAAmB,CAAG,EAAE,CAAG,mBACjEA,CAAkB,CAACA,CAAkB,CAAC,iBAAiB,CAAG,EAAE,CAAG,iBAMnE,IAAMohB,GAAqB,CACvB,wBAAyB,CAAC,WAAY,YAAa,YAAY,CAC/D,yBAA0B,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAM,AACnE,EAcA,SAASC,GAAiBpD,CAAS,EAE/B,IAAMqD,EAASR,GAA8B,MAAO,CAChD5N,UAAW,0BACf,EAAG,KAAK,EAAG+K,GAELsD,EAAST,GAA8B,MAAO,CAChD5N,UAAW,0BACf,EAAG,KAAK,EAAG+K,GAKX,OAHA6C,GAA8B,MAAO,CACjC5N,UAAW,kCACf,EAAG,KAAK,EAAGqO,GACJ,CACHD,OAAQA,EACRC,OAAQA,CACZ,CACJ,CAgDA,SAASC,GAA8B7e,CAAK,CAAE2C,CAAM,CAAEmc,CAAU,CAAEC,CAAa,EAC3E,IAAMC,EAASrc,EAAOsc,MAAM,EAAItc,EAAOlD,OAAO,CAACwf,MAAM,AAErDF,CAAAA,EAAczC,SAAS,CAAG,AAACrB,KAAkDsB,SAAS,CAEtF4B,GAA8B,KAAM,CAChC5N,UAAW,4BACf,EAAG,KAAK,EAAGwO,GAAepC,WAAW,CAACqB,GAAoBF,cAAc,CAACoB,GAAYvc,EAAQmc,GAAYK,iBAAiB,GAE1HhB,GAA8B,QAAS,CACnC/Y,KAAM,SACNga,KAAM,mBAAqBN,EAC3BrM,MAAOqM,CACX,EAAG,KAAK,EAAGC,GAEXM,GAAc1gB,IAAI,CAAC,IAAI,CAAEmgB,EAAY,SAAU9e,EAAO+e,EAAepc,EAAQA,EAAO2c,YAAY,EAAI3c,EAAO2c,YAAY,CAAC7f,OAAO,CAACuE,EAAE,EAC9Hgb,EAAOO,cAAc,EACrBF,GAAc1gB,IAAI,CAAC,IAAI,CAAEmgB,EAAY,SAAU9e,EAAO+e,EAAepc,EAAQA,EAAO2c,YAAY,EAAIN,EAAOO,cAAc,EAG7HC,GAAe7gB,IAAI,CAAC,IAAI,CAAEqB,EAAO,SAAUgf,EAAQF,EAAYC,EACnE,CAwBA,SAASU,GAAiBzf,CAAK,CAAEob,CAAS,CAAEsE,CAAQ,CAAEC,CAAM,EAIxD,SAASC,EAAgBjd,CAAM,CAAEkd,CAAa,EAC1C,IAAMtW,EAASwV,EAAc1B,UAAU,CAClCxL,QAAQ,CAAC,EAAE,CAChBgN,GAA8BlgB,IAAI,CAAC+c,EAAO1b,EAAO2C,EAAQkd,EAAed,GACpExV,GACAA,CAAAA,EAAOiB,KAAK,CAACoS,OAAO,CAAG,OAAM,EAG7BkD,GAAUnd,EAAOlD,OAAO,EACxB0e,GAA8B,QAAS,CACnC/Y,KAAM,SACNga,KAAM,iBAAmBS,EACzBpN,MAAO9P,EAAOlD,OAAO,CAACuE,EAAE,AAC5B,EAAG,KAAK,EAAG+a,GAAegB,YAAY,CAAC,4BAA6Bpd,EAAOlD,OAAO,CAACuE,EAAE,CAE7F,CACA,IAAM0X,EAAQ,IAAI,CAAEja,EAAOia,EAAMja,IAAI,CAAEkd,EAASvD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEpB,EAASxD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEF,EAASJ,AAAa,SAAbA,EAAqB/c,EAAUmd,EACrN9f,EAAM2C,MAAM,CACZ3C,EAAMP,OAAO,CAACwgB,WAAW,EAAI,CAAC,EAElC,GAAI,CAACjgB,GAAS2C,EACV,OAEJ,IAAI2R,EAAM4L,EAAsB,EAAE,AAE9B,CAACJ,GAAWzB,GAAwB1b,GAI/B0b,GAAwB1b,IAC7Bud,CAAAA,EAAsBC,GAAkBxhB,IAAI,CAAC,IAAI,CAAEgE,EAAM,EAHzDud,EAAsBE,GAAazhB,IAAI,CAAC,IAAI,CAAEgE,EAAQgd,GAM1DnB,GAA2B0B,EAAqB,CAACpiB,EAAGuiB,KAChD,IAAMC,EAAcxiB,EAAEqhB,iBAAiB,CAACoB,WAAW,GAAIC,EAAcH,EAAElB,iBAAiB,CAACoB,WAAW,GACpG,OAAO,AAACD,EAAcE,EAClB,GAAK,CAACF,CAAAA,EAAcE,CAAU,CACtC,GAGI7B,EAAO9M,QAAQ,CAAC,EAAE,EAClB8M,EAAO9M,QAAQ,CAAC,EAAE,CAAC4K,MAAM,GAG7B,IAAMgE,EAAgBtC,GAA8B,KAAM,CACtD5N,UAAW,2BACf,EAAG,KAAK,EAAGoO,GACLI,EAAgBH,EAAOoB,gBAAgB,CAAC,oCAAoC,CAAC,EAAE,CAiBrF,GAhBAE,EAAoBxf,OAAO,CAAC,AAACggB,IACzB,GAAM,CAAEvB,kBAAAA,CAAiB,CAAEU,cAAAA,CAAa,CAAEld,OAAAA,CAAM,CAAE,CAAG+d,EACrDpM,EAAO6J,GAA8B,KAAM,CACvC5N,UAAW,2BACf,EAAG,KAAK,EAAGkQ,GACX,IAAME,EAAMxC,GAA8B,SAAU,CAChD5N,UAAW,iCACXqQ,YAAazB,CACjB,EAAG,KAAK,EAAG7K,GACX,CAAC,QAAS,aAAa,CAAC5T,OAAO,CAAC,AAACkb,IAC7BsC,GAAyByC,EAAK/E,EAAW,WACrCgE,EAAgBjd,EAAQkd,EAC5B,EACJ,EACJ,GAEIK,EAAoBte,MAAM,CAAG,EAAG,CAChC,GAAM,CAAEe,OAAAA,CAAM,CAAEkd,cAAAA,CAAa,CAAE,CAAGK,CAAmB,CAAC,EAAE,CACxDN,EAAgBjd,EAAQkd,EAC5B,MACUC,IACN7E,KAAiD4F,cAAc,CAAC9B,EAAc1B,UAAU,CAACxL,QAAQ,CAAC,EAAE,CAAEpQ,EAAKqf,aAAa,EAAI,IAC5H/B,EAAc1B,UAAU,CAACxL,QAAQ,CAAC,EAAE,CAC/BrH,KAAK,CAACoS,OAAO,CAAG,OAE7B,CAiBA,SAAS4C,GAAexf,CAAK,CAAEqd,CAAU,CAAE2B,CAAM,CAAE5Z,CAAI,CAAEgW,CAAS,EAC9D,GAAI,CAACpb,EACD,OAEJ,IAAM0d,EAAW,IAAI,CAACA,QAAQ,CAC9Ba,GAA2BS,EAAQ,CAACvM,EAAOsO,KAEvC,IAAMvD,EAAiBH,EAAa,IAAM0D,EAC1C,GAAI3C,GAAwB3L,IACxB+K,GASA,GARIc,GAAyB7L,KAGzBiL,EAAS/e,IAAI,CAAC,IAAI,CAAE6e,EAAgBpY,EAAMgW,EAAW,CAAC,GACtDoE,GAAe7gB,IAAI,CAAC,IAAI,CAAEqB,EAAOwd,EAAgB/K,EAAOrN,EAAMgW,IAI9DoC,KAAkBngB,EAAoB,CAEtC,IAAM2jB,EAAYC,GAAatiB,IAAI,CAAC,IAAI,CAAEyG,EAAMoY,EAAgBpC,GAEhE8F,GAAoBviB,IAAI,CAAC,IAAI,CAAEqB,EAAOqd,EAAY2D,EAAW5b,EAAM2b,EAAWtO,EAClF,KAGmB,0BAAnB+K,GACKa,GAAwB5L,IAEzBiL,EAAS/e,IAAI,CAAC,IAAI,CAAE6e,EAAgBpY,EAAMgW,EAAW,CACjD3I,MAAOA,EACPrN,KAAM,QACV,GAIZ,EACJ,CAYA,SAAS+b,GAAanhB,CAAK,CAAEob,CAAS,EAClC,IAAMM,EAAQ,IAAI,CAAEiD,EAASvD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAKpFoB,EAAkB,IAAI,CAAC3f,IAAI,CAAC4f,WAAW,CAAEC,EAAenD,GAA8B,MAAO,CAC5F5N,UAAW,0BACf,EAAG,KAAK,EAAGoO,GACL4C,EAAoB,SAAUC,CAAS,EAEzC/B,GAAiB9gB,IAAI,CAAC+c,EAAO1b,EAAO0b,EAAMJ,SAAS,CAAE,MAAOkG,EAChE,EAEMC,EAAQ,IAAI,CAAC/D,QAAQ,CAbwE,mBAa9D,QAAS4D,EAb2F,CACrI7O,MAAO,GACPrN,KAAM,OACNsc,QAAS,oBACTC,eAAgB,0CACpB,GAQ8EpY,EAAS4U,GAA8B,IAAK,CACtHyC,YAAaQ,CACjB,EAAG,KAAK,EAAGE,GACXG,EAAMjF,SAAS,CAAC/b,GAAG,CAAC,sCACpB8I,EAAOiT,SAAS,CAAC/b,GAAG,CAAC,uBAErByd,GAAyBuD,EAAO,QAAS,WACrCF,EAAkB,IAAI,CAAC9O,KAAK,EAExB,IAAI,CAACA,KAAK,CAAC7Q,MAAM,CACjB2H,EAAOiB,KAAK,CAACoS,OAAO,CAAG,eAGvBrT,EAAOiB,KAAK,CAACoS,OAAO,CAAG,MAE/B,GAEA,CAAC,QAAS,aAAa,CAAClc,OAAO,CAAC,AAACkb,IAC7BsC,GAAyB3U,EAAQqS,EAAW,WAExC6F,EAAMhP,KAAK,CAAG,GACd8O,EAAkB,IAElBhY,EAAOiB,KAAK,CAACoS,OAAO,CAAG,MAC3B,EACJ,EACJ,CAeA,SAASqE,GAAapB,CAAa,CAAE+B,CAAU,CAAExG,CAAS,EACtD,IAAMyG,EAAkBD,EAAWE,KAAK,CAAC,KAAMC,EAAYF,CAAe,CAACA,EAAgBjgB,MAAM,CAAG,EAAE,CAAEogB,EAAa,cAAgBJ,EAAa,SAAW/B,EAAepe,EAAO,IAAI,CAACA,IAAI,CAE5L0c,GAA8B,QAAS,CACnCuD,QAASM,CACb,EAAG,KAAM5G,GAAWuB,WAAW,CAACqB,GAAoBF,cAAc,CAACrc,CAAI,CAACsgB,EAAU,EAAIH,IAEtF,IAAMZ,EAAY7C,GAA8B,SAAU,CACtDiB,KAAM4C,EACNzR,UAAW,yBACXvM,GAAI,qBAAuB4d,CAC/B,EAAG,KAAMxG,GAET,OADA4F,EAAUjB,YAAY,CAAC,KAAM,qBAAuB6B,GAC7CZ,CACX,CAwBA,SAASE,GAAoBlhB,CAAK,CAAE4hB,CAAU,CAAEZ,CAAS,CAAEnB,CAAa,CAAEoC,CAAa,CAAEC,CAAc,CAAEC,CAAa,EAE9GP,AAAe,WAAfA,GAA2BA,AAAe,WAAfA,EAE3B5hB,EAAM2C,MAAM,CAACjC,OAAO,CAAC,AAACiC,IAClB,IAAMyf,EAAgBzf,EAAOlD,OAAO,CAAE4iB,EAAaD,EAAchD,IAAI,EACjEgD,EAAcnD,MAAM,CACpBtc,EAAOyc,IAAI,CACXgD,EAAcpe,EAAE,EAAI,EACC,CAAA,gCAArBoe,EAAcpe,EAAE,EAChBoe,EAAcpe,EAAE,GAAMme,CAAAA,GAClBA,EAAc1iB,OAAO,EACrB0iB,EAAc1iB,OAAO,CAACuE,EAAE,AAAD,IACtBoa,GAAwB8D,IACzBN,AAAe,WAAfA,GACAjf,AAAgB,WAAhBA,EAAOyC,IAAI,EACX8c,CAAAA,EAAiBE,EAAcpe,EAAE,AAAD,EAEpCma,GAA8B,SAAU,CACpC1L,MAAO2P,EAAcpe,EAAE,AAC3B,EAAG,KAAK,EAAGgd,GAAWrE,WAAW,CAACqB,GAAoBF,cAAc,CAACuE,IAE7E,GAEKxC,GAAiBoC,GAGtBK,AAD2E7D,EAAkB,CAAzEwD,EAAgB,IAAMpC,EAAgE,CAC1Fnf,OAAO,CAAC,AAACwF,IACrBiY,GAA8B,SAAU,CACpC1L,MAAOvM,CACX,EAAG,KAAK,EAAG8a,GAAWrE,WAAW,CAACqB,GAAoBF,cAAc,CAAC5X,GACzE,GAGAkY,GAAwB8D,IACxBlB,CAAAA,EAAUvO,KAAK,CAAGyP,CAAa,CAEvC,CAiBA,SAAS9B,GAAazd,CAAM,CAAEgd,CAAM,EAChC,IAII4C,EAJgB9gB,EAAOia,AAAb,IAAI,CAAe1b,KAAK,EAAI0b,AAA5B,IAAI,CAA8B1b,KAAK,CAACP,OAAO,CAACgC,IAAI,CAAE+gB,EAAmB/gB,GACnFA,EAAKghB,UAAU,EACfhhB,EAAKghB,UAAU,CAAC/G,KAAK,EACrBja,EAAKghB,UAAU,CAAC/G,KAAK,CAAC8G,gBAAgB,CAAEtC,EAAsB,EAAE,CAkCpE,OAhCA3B,GAA2B5b,EAAQ,CAACA,EAAQ8P,KACxC,IAAM2P,EAAgBzf,GAAUA,EAAOlD,OAAO,CAE9C,GAAIkD,EAAOsc,MAAM,EAAImD,GACjBA,EAAcnD,MAAM,CAAE,CACtB,GAAM,CAAEE,kBAAAA,CAAiB,CAAEU,cAAAA,CAAa,CAAE,CAAGX,GAAYvc,EAAQ8P,GACjE,GAAIkN,EAAQ,CAGR,IAAM+C,EAAQ,AAAIC,OADEhD,EAAOiD,OAAO,CAAC,sBAAuB,QACpB,KAAMC,EAAQL,GAChDA,CAAgB,CAAC3C,EAAc,EAC/B2C,CAAgB,CAAC3C,EAAc,CAAC7d,IAAI,CAAC,MAAQ,GAC7Cmd,CAAAA,EAAkBxD,KAAK,CAAC+G,IACxBG,EAAMlH,KAAK,CAAC+G,EAAK,IACjBH,EAAiB,CACbpD,kBAAAA,EACAU,cAAAA,EACAld,OAAQA,CACZ,EACAud,EAAoBvgB,IAAI,CAAC4iB,GAEjC,MAEIA,EAAiB,CACbpD,kBAAAA,EACAU,cAAAA,EACAld,OAAQA,CACZ,EACAud,EAAoBvgB,IAAI,CAAC4iB,EAEjC,CACJ,GACOrC,CACX,CAYA,SAASC,GAAkBxd,CAAM,EAC7B,IAAMud,EAAsB,EAAE,CAW9B,OATAvd,EAAOjC,OAAO,CAAC,AAACiC,IACRA,EAAOmgB,EAAE,CAAC,QACV5C,EAAoBvgB,IAAI,CAAC,CACrBwf,kBAAmBxc,EAAOyc,IAAI,CAC9BS,cAAeld,EAAOyC,IAAI,CAC1BzC,OAAQA,CACZ,EAER,GACOud,CACX,CA+BA,SAAShB,GAAYvc,CAAM,CAAEkd,CAAa,EACtC,IAAMpgB,EAAUkD,EAAOlD,OAAO,CAE1B4iB,EAAa,AAACpE,EAAW,CAAC4B,EAAc,EACxC5B,EAAW,CAAC4B,EAAc,CAACphB,SAAS,CAACskB,QAAQ,EAC7ClD,EAAcmD,WAAW,GAAIlE,EAAae,EAM9C,OAJIpgB,GAAWA,EAAQ2F,IAAI,GACvB0Z,EAAanc,EAAOlD,OAAO,CAAC2F,IAAI,CAChCid,EAAa1f,EAAOyc,IAAI,EAErB,CACHD,kBAAmBkD,EACnBxC,cAAef,CACnB,CACJ,CAsBA,SAASO,GAAcQ,CAAa,CAAE+B,CAAU,CAAE5hB,CAAK,CAAEob,CAAS,CAAE+G,CAAa,CAAED,CAAc,EAG7F,GAAI,CAACliB,EACD,OAGJ,IAAMghB,EAAYC,GAAatiB,IAAI,CANrB,IAAI,CAMyBkhB,EAAe+B,EAAYxG,GAEtE8F,GAAoBviB,IAAI,CARV,IAAI,CAQcqB,EAAO4hB,EAAYZ,EAAW,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGmB,GAElF/D,GAAwB8D,IACxBlB,CAAAA,EAAUvO,KAAK,CAAGyP,CAAa,CAEvC,CA0BA,GAAM,CAAE3c,IAAK0d,EAAa,CAAE,CAAIpkB,IAE1B,CAAEC,SAAUokB,EAAkB,CAAExN,cAAeyN,EAAuB,CAAE,CAAItkB,IAWlF,SAASukB,KAEL,OAAOD,GAAwB,MAAO,CAElC5S,UAAW,sDACf,EAAG,KAAK,EAJS,IAAI,CAAC+K,SAAS,CAKnC,CAWA,SAAS+H,GAAYC,CAAO,CAAEC,CAAU,EACpC,IAAMnH,EAAW,IAAI,CAACd,SAAS,CAAE7Z,EAAO,IAAI,CAACA,IAAI,CAC7C8O,EAAY,qBACG,CAAA,IAAfgT,GACAhT,CAAAA,GAAa,0BAAyB,EAG1C,IAAMiT,EAAWL,GAAwB,SAAU,CAC/C5S,UAAAA,CACJ,EAAG,KAAK,EAAG6L,GAGX,OAFAoH,EAAS7G,WAAW,CAACsG,GAAcnF,cAAc,CAACrc,CAAI,CAAC6hB,EAAU,SAAS,EAAIA,IAC9EE,EAASzD,YAAY,CAAC,2BAA4BuD,GAC3CE,CACX,CAKA,SAASC,KACL,IAAMrH,EAAW,IAAI,CAACd,SAAS,CAAEoI,EAAOtH,EACnC4D,gBAAgB,CAAC,wBAAyB2D,EAAcvH,EACxD4D,gBAAgB,CAAC,gCACtB,IAAK,IAAIpf,EAAI,EAAGA,EAAI8iB,EAAK9hB,MAAM,CAAEhB,IAC7B8iB,CAAI,CAAC9iB,EAAE,CAAC4b,SAAS,CAACC,MAAM,CAAC,8BACzBkH,CAAW,CAAC/iB,EAAE,CAAC4b,SAAS,CAACC,MAAM,CAAC,2BAExC,CA4BA,SAASmH,GAAUC,CAAG,CAAEhhB,CAAK,EACzB,IAAMihB,EAAU,IAAI,CAACxI,SAAS,CACzB0E,gBAAgB,CAAC,+BACtB6D,CAAAA,EAAItT,SAAS,EAAI,8BACjBuT,CAAO,CAACjhB,EAAM,CAAC0N,SAAS,EAAI,2BAChC,CAOA,SAASwT,GAAWR,CAAU,EAC1B,IAAM7H,EAAQ,IAAI,CAClBgI,AADsDtH,AAAvB,IAAI,CAACd,SAAS,CAAkB0E,gBAAgB,CAAC,wBAC3Etf,OAAO,CAAC,CAACmjB,EAAKjjB,KACX2iB,CAAAA,AAAe,IAAfA,GACAM,AAAiD,SAAjDA,EAAIlP,YAAY,CAAC,2BAAqC,GAG1D,CAAC,QAAS,aAAa,CAACjU,OAAO,CAAC,AAACkb,IAC7BsH,GAAmBW,EAAKjI,EAAW,WAE/B6H,GAAY9kB,IAAI,CAAC+c,GACjBkI,GAAUjlB,IAAI,CAAC+c,EAAO,IAAI,CAAE9a,EAChC,EACJ,EACJ,EACJ,CA0BA,GAAM,CAAE2E,IAAKye,EAAS,CAAE,CAAInlB,IAEtB,CAAEqN,WAAAA,EAAU,CAAE,CAAIrN,IAKlB,CAAEC,SAAUmlB,EAAc,CAAEvO,cAAewO,EAAmB,CAAElS,OAAQmS,EAAY,CAAEllB,UAAWmlB,EAAe,CAAEllB,KAAMmlB,EAAU,CAAE,CAAIxlB,GA0D9I,OAAMylB,WAAcnJ,GAMhBlR,YAAYmR,CAAS,CAAEC,CAAQ,CAAErb,CAAK,CAAE,CACpC,KAAK,CAACob,EAAWC,GACjB,IAAI,CAACrb,KAAK,CAAGA,EACb,IAAI,CAACyB,IAAI,CAAG,AAACyK,CAAAA,KAAazK,IAAI,CAACghB,UAAU,EAAI,CAAC,CAAA,EAAG/G,KAAK,EAAI,CAAC,EAC3DuI,GAAe,IAAI,CAAC3I,SAAS,CAAE,YAAa,KACxC,IAAMiJ,EAAmBvkB,GACrBA,EAAMwkB,kBAAkB,EACxBxkB,EAAMwkB,kBAAkB,CAACD,gBAAgB,CAC7C,GAAIA,EAAkB,CAClBA,EAAiB7d,WAAW,CAAG,CAAA,EAC/B,IAAM+d,EAASR,GAAeD,GAAW,QAAS,KAC9CU,WAAW,KACPH,EAAiB7d,WAAW,CAAG,CAAA,CACnC,EAAG,GACH+d,GACJ,EACJ,CACJ,EACJ,CA0BA/G,SAASC,CAAM,CAAEkC,CAAa,CAAEzE,CAAS,CAAEuJ,CAAe,CAAE,CACxD,IAAM9C,EAAkBlE,EAAOmE,KAAK,CAAC,KAAMF,EAAaC,CAAe,CAACA,EAAgBjgB,MAAM,CAAG,EAAE,CAAEH,EAAO,IAAI,CAACA,IAAI,CAAEmjB,EAAY,cAAgB/E,EAAgB,IAAMwE,GAAWM,EAAgBjD,OAAO,CAAEE,GACxMA,EAAWjG,KAAK,CAAC,UAElBuI,GAAoB,QAAS,CACzBxC,QAASkD,EACTrU,UAAWoU,EAAgBhD,cAAc,AAC7C,EAAG,KAAK,EAAGvG,GAAWuB,WAAW,CAACqH,GAAUlG,cAAc,CAACrc,CAAI,CAACmgB,EAAW,EAAIA,IAGnF,IAAMH,EAAQyC,GAAoB,QAAS,CACvC9E,KAAMwF,EACNnS,MAAOkS,EAAgBlS,KAAK,CAC5BrN,KAAMuf,EAAgBvf,IAAI,CAC1BmL,UAAW,wBACf,EAAG,KAAK,EAAG6K,GAEX,OADAqG,EAAM1B,YAAY,CAAC,uBAAwBpC,GACpC8D,CACX,CACA5F,mBAAoB,CAChB,GAAI,IAAI,CAAC7b,KAAK,CAAE,CACZ,IAAMwkB,EAAqB,IAAI,CAACxkB,KAAK,CAACwkB,kBAAkB,CACxDJ,GAAgBI,EAAoB,cAChCA,GACAA,EAAmBK,qBAAqB,EACxCT,GAAgBI,EAAoB,iBAAkB,CAAEjb,OAAQib,EAAmBK,qBAAqB,AAAC,EAEjH,MAEI,KAAK,CAAChJ,mBAEd,CAiBAiJ,UAAU1J,CAAS,CAAEhZ,CAAK,CAAEgD,CAAI,CAAE2f,CAAS,CAAEC,CAAQ,CAAE,CACnD,IAAMzb,EAAS2a,GAAoB,SAAU,KAAK,EAAG,KAAK,EAAG9I,GAU7D,OATA7R,EAAOoT,WAAW,CAACqH,GAAUlG,cAAc,CAAC1b,IACxC4iB,GACA,CAAC,QAAS,aAAa,CAACtkB,OAAO,CAAC,AAACkb,IAC7BqI,GAAe1a,EAAQqS,EAAW,KAC9B,IAAI,CAACK,UAAU,GACR+I,EAASC,AAlJpC,SAAmB7J,CAAS,CAAEhW,CAAI,EAC9B,IAAM8f,EAAYliB,MAAMvE,SAAS,CAAC6F,KAAK,CAAC3F,IAAI,CAACyc,EAAU4E,gBAAgB,CAAC,UAAWmF,EAAaniB,MAAMvE,SAAS,CAAC6F,KAAK,CAAC3F,IAAI,CAACyc,EAAU4E,gBAAgB,CAAC,WAAsIoF,EAAWhK,EAAU4E,gBAAgB,CAAhJ,6CAA8J,CAAC,EAAE,CAAEqF,EAAWjK,EAAU4E,gBAAgB,CAA3I,6CAAyJ,CAAC,EAAE,CACpYsF,EAAe,CACjBC,WAAYngB,EACZggB,SAAUA,GAAYA,EAASzQ,YAAY,CAAC,UAAY,GACxDqK,OAAQ,CAAC,CACb,EA4BA,OA3BAkG,EAAUxkB,OAAO,CAAC,AAAC+gB,IACf,IAAM+D,EAAQ/D,EAAM9M,YAAY,CAAC,wBAAoC8M,EAAM9M,YAAY,CAAC,6BAGpF2Q,EAAaG,QAAQ,CAAGhE,EAAMhP,KAAK,CAE9B+S,EACLF,EAAatG,MAAM,CAACwG,EAAM,CAAG/D,EAAMhP,KAAK,CAIxC6S,EAAalgB,IAAI,CAAGqc,EAAMhP,KAAK,AAEvC,GACA0S,EAAWzkB,OAAO,CAAC,AAACglB,IAChB,IAAM1hB,EAAK0hB,EAAO1hB,EAAE,CAEpB,GAAIA,AAAO,6BAAPA,GACAA,AAAO,6BAAPA,EAAmC,CACnC,IAAM2hB,EAAY3hB,EAAG8d,KAAK,CAAC,qBAAqB,CAAC,EAAE,AACnDwD,CAAAA,EAAatG,MAAM,CAAC2G,EAAU,CAAGD,EAAOjT,KAAK,AACjD,CACJ,GACI4S,GACAC,CAAAA,EAAatG,MAAM,CAAC,wBAAwB,CAAGqG,EAC1C1Q,YAAY,CAAC,UAAY,EAAC,EAE5B2Q,CACX,EA+G8CP,EAAW3f,KAE7C,GAEGmE,CACX,CASAqc,SAASxgB,CAAI,CAAEpF,CAAK,CAAEP,CAAO,CAAEulB,CAAQ,CAAE,CAChChlB,IAIL,IAAI,CAACkc,SAAS,GAED,eAAT9W,GACA,IAAI,CAACygB,UAAU,CAACC,OAAO,CAACnnB,IAAI,CAAC,IAAI,CAAEqB,EAAOP,EAASulB,GAG1C,uBAAT5f,GACA,IAAI,CAAC1F,WAAW,CAACqmB,UAAU,CAACpnB,IAAI,CAAC,IAAI,CAAEqB,EAAOP,EAASulB,GAG9C,oBAAT5f,GACA,IAAI,CAAC1F,WAAW,CAAComB,OAAO,CAACnnB,IAAI,CAAC,IAAI,CAAEqB,EAAOP,EAASulB,GAG3C,SAAT5f,GACA,IAAI,CAAC1F,WAAW,CAAComB,OAAO,CAACnnB,IAAI,CAAC,IAAI,CAAEqB,EAAOP,EAASulB,EAAU,CAAA,GAElE,IAAI,CAAC5f,IAAI,CAAGA,EAEZ,IAAI,CAACkW,SAAS,CAAC9Q,KAAK,CAACD,MAAM,CAAG,IAAI,CAAC+Q,SAAS,CAAC0K,YAAY,CAAG,KAChE,CACJ,CACA7B,GAAaG,GAAM7lB,SAAS,CAAE,CAC1BiB,YAt9BqB,CACrBomB,QA7IJ,SAAiB9lB,CAAK,CAAEP,CAAO,CAAEulB,CAAQ,CAAEiB,CAAM,EAC7C,GAAI,CAACjmB,EACD,OAEJ,IAAMoc,EAAW,IAAI,CAACd,SAAS,CAAE7Z,EAAO,IAAI,CAACA,IAAI,CAE7Ckd,EAAS5B,GAA+B,KAAM,CAC9CxM,UAAW,6BACf,EAAG,KAAK,EAAG6L,GACXuC,EAAOhC,WAAW,CAACE,GAAqBiB,cAAc,CAACrc,CAAI,CAAChC,EAAQymB,OAAO,CAAC,EAAIzmB,EAAQymB,OAAO,EAAI,KAEnGvH,EAAS5B,GAA+B,MAAO,CAC3CxM,UAAY,oDAChB,EAAG,KAAK,EAAG6L,GACX,IAAM+J,EAAYpJ,GAA+B,MAAO,CACpDxM,UAAW,6BACf,EAAG,KAAK,EAAG6L,GACXgB,GAAcze,IAAI,CAAC,IAAI,CAAEggB,EAAQ3e,EAAO,GAAIP,EAAS,EAAE,CAAE,CAAA,GACzD,IAAI,CAACqlB,SAAS,CAACqB,EAAWF,EACrBxkB,EAAKqjB,SAAS,EAAI,MAClBrjB,EAAK2kB,UAAU,EAAI,OAASH,EAAS,MAAQ,OAAQ7J,EAAU4I,EACxE,EAyHIe,WAhHJ,SAAoB/lB,CAAK,CAAEP,CAAO,CAAEulB,CAAQ,EACxC,IAAMvjB,EAAO,IAAI,CAACA,IAAI,CAAE2a,EAAW,IAAI,CAACd,SAAS,CAAEsK,EAAW,IAAI,CAACA,QAAQ,CAAEzJ,EAAe,+BAE3C,CAAA,KAA7CC,EAAS7L,SAAS,CAACxJ,OAAO,CAACoV,IAC3BC,CAAAA,EAAS7L,SAAS,EAAI,IAAM4L,EAAe,2BAA0B,EAGrEnc,GACAoc,CAAAA,EAAS5R,KAAK,CAAC6b,GAAG,CAAGrmB,EAAMgJ,OAAO,CAAG,GAAK,IAAG,EAGjD,IAAM5G,EAAQ2a,GAA+B,IAAK,CAC9CxM,UAAW,6BACf,EAAG,KAAK,EAAG6L,GACXha,EAAM2d,YAAY,CAAC,aAAc,mBACjC3d,EAAMua,WAAW,CAACE,GAAqBiB,cAAc,CAACZ,GAEtDzb,CAAI,CAAChC,EAAQymB,OAAO,CAAC,EAAIzmB,EAAQymB,OAAO,CAExCzmB,EAAQ2J,MAAM,EAAI3J,EAAQ2J,MAAM,CAAC,EAAE,CAAChE,IAAI,CAAE,MAE1C,IAAImE,EAAS,IAAI,CAACub,SAAS,CAAC1I,EAAU3a,EAAK6kB,UAAU,EAAI,OAAQ,OAAQlK,EAAU,KAC/EwJ,EAASjnB,IAAI,CAAC,IAAI,CAAE,kBAAmBqB,EAAOP,EAASulB,EAC3D,EACAzb,CAAAA,EAAOgH,SAAS,EAAI,qCACpBhH,EAAOiB,KAAK,CAAC,mBAAmB,CAAG,OAC/B,IAAI,CAAC6Q,QAAQ,CAAG,YACpB9R,EAAS,IAAI,CAACub,SAAS,CAAC1I,EAAU3a,EAAK8kB,YAAY,EAAI,SAAU,SAAUnK,EAAU4I,GACrFzb,EAAOgH,SAAS,EAAI,uCACpBhH,EAAOiB,KAAK,CAAC,mBAAmB,CAAG,OAC/B,IAAI,CAAC6Q,QAAQ,CAAG,cACxB,CAkFA,EAo9BIwK,WAlXoB,CACpBC,QAlhBJ,SAAiC9lB,CAAK,CAAEwmB,CAAQ,CAAExB,CAAQ,EACtD,IACIyB,EADEhlB,EAAO,IAAI,CAACA,IAAI,CAEtB,GAAI,CAACzB,EACD,OAGJ,IAAI,CAAC0jB,IAAI,CAAC5T,IAAI,CAACnR,IAAI,CAAC,IAAI,CAAEqB,GAE1B,IAAM0mB,EAAiB,IAAI,CAACpL,SAAS,CAChC0E,gBAAgB,CAAC,gCAEtBtB,GAAiBgI,CAAc,CAAC,EAAE,EAClCvF,GAAaxiB,IAAI,CAAC,IAAI,CAAEqB,EAAO0mB,CAAc,CAAC,EAAE,EAChDjH,GAAiB9gB,IAAI,CAAC,IAAI,CAAEqB,EAAO0mB,CAAc,CAAC,EAAE,CAAE,OACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9B1G,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC2B,EAAiBhlB,EAAKqjB,SAAS,EAAI,MAAO,MAAO2B,EAAiBzB,GAEjFtG,GAAiBgI,CAAc,CAAC,EAAE,EAClCjH,GAAiB9gB,IAAI,CAAC,IAAI,CAAEqB,EAAO0mB,CAAc,CAAC,EAAE,CAAE,QACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9B1G,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC2B,EAAiBhlB,EAAK2kB,UAAU,EAAI,OAAQ,OAAQK,EAAiBzB,GACpF,IAAI,CAACF,SAAS,CAAC2B,EAAiBhlB,EAAK8kB,YAAY,EAAI,SAAU,SAAUE,EAAiBzB,EAC9F,EA0fI2B,UApFJ,WACI,IAAIC,EAAU,EAOd,OANA,IAAI,CAACjkB,MAAM,CAACjC,OAAO,CAAC,AAACmmB,IACbA,CAAAA,EAAM5H,MAAM,EACZ4H,EAAMpnB,OAAO,CAACwf,MAAM,AAAD,GACnB2H,GAER,GACOA,CACX,CA4EA,EAgXIlD,KA1Oc,CACd5T,KAvDJ,SAAc9P,CAAK,EACf,GAAI,CAACA,EACD,OAEJ,IAAM8mB,EAAkB,IAAI,CAACjB,UAAU,CAACc,SAAS,CAAChoB,IAAI,CAACqB,GAEjD+mB,EAAW1D,GAAY1kB,IAAI,CAAC,IAAI,CAAE,OACxC0kB,GAAY1kB,IAAI,CAAC,IAAI,CAAE,OAAQmoB,GAE/B1D,GAAezkB,IAAI,CAAC,IAAI,EACxBykB,GAAezkB,IAAI,CAAC,IAAI,EACxBolB,GAAWplB,IAAI,CAAC,IAAI,CAAEmoB,GAEtBlD,GAAUjlB,IAAI,CAAC,IAAI,CAAEooB,EAAU,EACnC,CA0CA,CAyOA,GAsBA,GAAM,CAAEC,SAAAA,EAAQ,CAAE,CAAInoB,IAGhB,CAAEC,SAAUmoB,EAAyB,CAAEC,WAAAA,EAAU,CAAE/nB,KAAMgoB,EAAqB,CAAE,CAAItoB,IAmB1F,SAASuoB,KACD,IAAI,CAAC1L,KAAK,EACV,IAAI,CAACA,KAAK,CAACO,UAAU,EAE7B,CAIA,SAASoL,GAA8BC,CAAM,EACpC,IAAI,CAAC5L,KAAK,EAEX,CAAA,IAAI,CAACA,KAAK,CAAG,IAjD6B4I,GAiDb,IAAI,CAACtkB,KAAK,CAACsb,SAAS,CAAG,IAAI,CAACtb,KAAK,CAACP,OAAO,CAACgjB,UAAU,CAACpH,QAAQ,EACrF,IAAI,CAACrb,KAAK,CAACP,OAAO,CAAC8nB,UAAU,EAC1B,IAAI,CAACvnB,KAAK,CAACP,OAAO,CAAC8nB,UAAU,CAACC,GAAG,CAACnM,QAAQ,EAC9C,sDAAwD,IAAI,CAACrb,KAAK,CAAA,EAE1E,IAAI,CAAC0b,KAAK,CAACkK,QAAQ,CAAC0B,EAAOG,QAAQ,CAAE,IAAI,CAACznB,KAAK,CAAEsnB,EAAO7nB,OAAO,CAAE6nB,EAAOI,QAAQ,CACpF,CAMA,SAASC,GAAiCxjB,CAAO,CAAEgC,CAAC,EAE3C,IAAI,CAACyhB,OAAO,CAACzhB,EAAEU,MAAM,CAAE,qBACxB1C,EAAQE,KAAK,CAAC,IAAI,CAAErB,MAAMvE,SAAS,CAAC6F,KAAK,CAAC3F,IAAI,CAAC4F,UAAW,GAElE,CAS6B,OA/C7B,SAAiBsjB,CAAuB,CAAEljB,CAAY,EAC9CuiB,GAAWF,GAAU,WACrBC,GAA0BY,EAAyB,aAAcT,IACjEH,GAA0BY,EAAyB,YAAaR,IAChEF,GAAsBxiB,EAAalG,SAAS,CAAE,uBAAwBkpB,IAE9E,EAuDM,CAAEG,qBAAAA,EAAoB,CAAE,CAAIjpB,IAe5B,CAAEkpB,wBAAAA,EAAuB,CAAEhpB,MAAOipB,EAAgB,CAAE/oB,UAAWgpB,EAAoB,CAAEne,MAAOoe,EAAgB,CAAEhpB,KAAMipB,EAAe,CAAE7Z,MAAAA,EAAK,CAAE,CAAIzP,IAyBtJ,SAASupB,GAA0BC,CAAW,CAAErX,CAAU,EACtD,IAAMsX,EAAgB,CAAC,EAYvB,MAXA,CAAC,SAAU,SAAS,CAAC5nB,OAAO,CAAC,AAAC0e,IAC1B,IAAMmJ,EAAkBF,CAAW,CAACjJ,EAAK,CAAEoJ,EAAkBxX,CAAU,CAACoO,EAAK,CACzEmJ,IACIC,EACAF,CAAa,CAAClJ,EAAK,CAAG9Q,GAAMka,GAAiBtY,GAAG,CAAC,CAACuY,EAAc7nB,IAAMsnB,GAAiBK,CAAe,CAAC3nB,EAAE,CAAE6nB,IAG3GH,CAAa,CAAClJ,EAAK,CAAGiJ,CAAW,CAACjJ,EAAK,CAGnD,GACOkJ,CACX,CAqBA,MAAMI,WAAmB5iB,EASrB,OAAOtB,QAAQE,CAAU,CAAEikB,CAAuB,CAAEhkB,CAAY,CAAE2O,CAAgB,CAAE,CAChFjO,EAA4Bb,OAAO,CAACkkB,GAAYhkB,EAAYC,GAC5DmW,AApiD8D1C,GAoiD9B5T,OAAO,CAAC8O,GACxC2B,AAv0E6D5B,GAu0E9B7O,OAAO,CAACE,EAAY4O,GACnDqV,EAAwBnkB,OAAO,CAACkkB,GAAYhkB,GAC5CkkB,GAA+BD,EAAyBhkB,EAC5D,CAMAsF,YAAYjK,CAAK,CAAEX,CAAW,CAAE,CAC5B,KAAK,GACL,IAAI,CAAC0E,IAAI,CAAG,cAOZ,IAAI,CAAC/D,KAAK,CAAGA,EAOb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAOhB,IAAI,CAACoL,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC5J,IAAI,CAAG,cACZ,IAAI,CAAClB,KAAK,CAAG,GAOb,IAAI,CAACV,MAAM,CAAG,EAAE,CAOhB,IAAI,CAACiH,MAAM,CAAG,EAAE,CAOhB,IAAI,CAAC3J,OAAO,CAAGyoB,GAAiB,IAAI,CAACW,cAAc,CAAExpB,GAOrD,IAAI,CAACA,WAAW,CAAGA,EAGnB,IAAMypB,EAAkBV,GAA0B,IAAI,CAAC3oB,OAAO,CAAEJ,EAChE,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAG2mB,EAAgB3mB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC2J,MAAM,CAAG0f,EAAgB1f,MAAM,CA0B5C,IAAI,CAAC0G,IAAI,CAAC9P,EAAO,IAAI,CAACP,OAAO,CACjC,CASAspB,cAAe,CACX,IAAI,CAACC,WAAW,GACZ,IAAI,CAACC,SAAS,EACd,IAAI,CAACC,SAAS,EACd,IAAI,CAACzpB,OAAO,CAAC8a,IAAI,EAEjB,CAAA,IAAI,CAACpa,QAAQ,CAAG,IAAI,CAACH,KAAK,CAACE,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACgpB,UAAU,GAAE,CAEtE,CAIAC,WAAY,CACR,IAAMC,EAAiB,IAAI,CAAC5pB,OAAO,CAAC0C,MAAM,EAAI,EAAE,CAChDknB,EAAc3oB,OAAO,CAAC,CAACuB,EAAcrB,KACjC,IAAMwB,EAAQ,IAAI,CAACknB,SAAS,CAACrnB,EAAcrB,GAC3CsnB,GAAiB,CAAA,EAAMmB,CAAa,CAACzoB,EAAE,CAAEwB,EAAM3C,OAAO,CAC1D,EACJ,CAIA8pB,WAAY,CACR,IAAMngB,EAAS,IAAI,CAAC3J,OAAO,CAAC2J,MAAM,EAAI,EAAE,CACxCA,EAAO1I,OAAO,CAAC,CAACmQ,EAAcjQ,KAC1B,IAAMyI,EAAQ,IAAI,CAACmgB,SAAS,CAAC3Y,EAAcjQ,GAC3CsnB,GAAiB,CAAA,EAAM9e,CAAM,CAACxI,EAAE,CAAEyI,EAAM5J,OAAO,CACnD,EACJ,CAQAsB,SAAU,CACN,IAAMf,EAAQ,IAAI,CAACA,KAAK,CAAEypB,EAAc,SAAUnV,CAAI,EAClDA,EAAKvT,OAAO,EAChB,EACA,IAAI,CAACoB,MAAM,CAACzB,OAAO,CAAC+oB,GACpB,IAAI,CAACrgB,MAAM,CAAC1I,OAAO,CAAC+oB,GACpB,IAAI,CAACR,SAAS,CAAG,KACjB,IAAI,CAACC,SAAS,CAAG,KACjBlB,GAAiBhoB,EAAM0pB,eAAe,CAAE,IAAI,CAACC,cAAc,EAC3D,KAAK,CAAC5oB,UACN,IAAI,CAACoN,oBAAoB,GACzB4Z,GAAwB,IAAI,CAAE/nB,EAClC,CAKAypB,YAAYnV,CAAI,CAAE,CAEd0T,GAAiB,IAAI,CAAC1T,EAAK1E,QAAQ,CAAG,IAAI,CAAE0E,GAC5CA,EAAKvT,OAAO,EAChB,CAIAooB,YAAa,CACT,GAAI,IAAI,CAACF,SAAS,EAAI,IAAI,CAACC,SAAS,CAChC,MAAO,CACHzmB,EAAG,IAAI,CAACwmB,SAAS,CAACW,IAAI,CACtBtiB,EAAG,IAAI,CAAC4hB,SAAS,CAAC7C,GAAG,CACrB/b,MAAO,IAAI,CAAC2e,SAAS,CAAC3e,KAAK,CAC3BC,OAAQ,IAAI,CAAC2e,SAAS,CAAC3e,MAAM,AACjC,CAER,CAKAsf,eAAe7pB,CAAK,CAAEX,CAAW,CAAE,CAC/B,IAAI,CAACyqB,UAAU,CAACzqB,GAChB,IAAMypB,EAAkBV,GAA0B,IAAI,CAAC3oB,OAAO,CAAEJ,EAChE,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAG2mB,EAAgB3mB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC2J,MAAM,CAAG0f,EAAgB1f,MAAM,CAC5C,IAAI,CAACpJ,KAAK,CAAGA,EACb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAChB,IAAI,CAACoL,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC5J,IAAI,CAAG,cACZ,IAAI,CAAC1E,WAAW,CAAGA,EACnB,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACiH,MAAM,CAAG,EAAE,AACpB,CAKA0G,KAAKia,CAAkB,CAAEC,CAAY,CAAEnnB,EAAQ,IAAI,CAACA,KAAK,CAAE,CACvD,IAAM7C,EAAQ,IAAI,CAACA,KAAK,CAAEiqB,EAAc,IAAI,CAACxqB,OAAO,CAACyK,SAAS,AAC9D,CAAA,IAAI,CAACrH,KAAK,CAAGA,EACb,IAAI,CAAC0L,UAAU,GACf,IAAI,CAACb,gBAAgB,GACrB,IAAI,CAAC6b,SAAS,GACd,IAAI,CAACH,SAAS,GACd,IAAI,CAACc,iBAAiB,GACtB,IAAI,CAACtmB,eAAe,CAAGkkB,GAAqB9nB,EAAOiqB,EACvD,CAKAX,UAAUrnB,CAAY,CAAEY,CAAK,CAAE,CAC3B,IAEkBT,EAAQ,IAzvDoCgW,GAyvDA,IAAI,CAFlD8P,GAAiB,IAAI,CAACzoB,OAAO,CAACwC,YAAY,CAAE,CACxD4L,oBAAqB,IAAI,CAACpO,OAAO,CAACoO,mBAAmB,AACzD,EAAG5L,GAA0EY,GAG7E,OAFAT,EAAMwN,QAAQ,CAAG,QACjB,IAAI,CAACzN,MAAM,CAACxC,IAAI,CAACyC,GACVA,CACX,CAUAonB,UAAU3Y,CAAY,CAAEhO,CAAK,CAAE,CAC3B,IAAMpD,EAAUyoB,GAAiB,IAAI,CAACzoB,OAAO,CAACoR,YAAY,CAAE,CACxDhD,oBAAqB,IAAI,CAACpO,OAAO,CAACoO,mBAAmB,AACzD,EAAGgD,GAAexH,EAAQ,IAAKqf,GAAWyB,SAAS,CAAC1qB,EAAQ2F,IAAI,CAAC,CAAE,IAAI,CAAE3F,EAASoD,GAGlF,OAFAwG,EAAMuG,QAAQ,CAAG,QACjB,IAAI,CAACxG,MAAM,CAACzJ,IAAI,CAAC0J,GACVA,CACX,CAIA/J,OAAO4K,CAAS,CAAE,CACd,IAAI,CAACqE,UAAU,GACV,IAAI,CAAC3O,OAAO,EACb,IAAI,CAACwK,MAAM,GAEX,IAAI,CAACjK,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,OAAO,CAAC,IAAI,CAACwlB,UAAU,IAEzC,IAAI,CAACiB,WAAW,CAAC,IAAI,CAAChhB,MAAM,CAAEc,GAC9B,IAAI,CAACkgB,WAAW,CAAC,IAAI,CAACjoB,MAAM,CAAE+H,GAC9B,IAAI,CAAC2E,mBAAmB,CAAC3E,EAC7B,CAKAmgB,WAAW/V,CAAI,CAAEpK,CAAS,CAAE,CACxBoK,EAAK/F,UAAU,GACV+F,EAAK5D,aAAa,IAId4D,EAAK1U,OAAO,EACb,IAAI,CAAC0qB,UAAU,CAAChW,GAEpBA,EAAKhV,MAAM,CAAC6oB,GAAgBje,EAAW,CAAA,IAASoK,EAAK1U,OAAO,CAACyU,MAAM,EAC/DC,EAAK/R,MAAM,CAACX,MAAM,EAClB2oB,AAtUhB,SAA0BjW,CAAI,EAC1B,IAAMlS,EAAQkS,EAAK1U,OAAO,CAAE4qB,EAAmBlW,EAAK/R,MAAM,CAAC1B,IAAI,CAAC,AAACuK,GAAWA,AAAyB,CAAA,IAAzBA,EAAMzI,MAAM,CAAC+H,OAAO,EAC5FU,AAAkB,CAAA,IAAlBA,EAAMV,OAAO,EACbtI,IACKooB,EAGyB,WAArBpoB,EAAMqoB,UAAU,EACrBroB,EAAMsoB,IAAI,GAHVtoB,EAAMuoB,IAAI,GAMtB,EA2TiCrW,IARrB,IAAI,CAACmV,WAAW,CAACnV,EAWzB,CAIA8V,YAAYQ,CAAK,CAAE1gB,CAAS,CAAE,CAC1B,IAAItJ,EAAIgqB,EAAMhpB,MAAM,CAGpB,KAAOhB,KACH,IAAI,CAACypB,UAAU,CAACO,CAAK,CAAChqB,EAAE,CAAEsJ,EAElC,CAKAuS,QAAS,CAEL,OAAO,IAAI,CAACzc,KAAK,CAACkF,gBAAgB,CAAC,IAAI,CAC3C,CAIAkF,QAAS,CACL,IAAMlK,EAAW,IAAI,CAACF,KAAK,CAACE,QAAQ,AACpC,CAAA,IAAI,CAACN,OAAO,CAAGM,EACVI,CAAC,CAAC,cACFT,IAAI,CAAC,CACNC,QAAS,EACTS,OAAQ,IAAI,CAACd,OAAO,CAACc,MAAM,CAC3BkqB,WAAY,IAAI,CAAChrB,OAAO,CAACiL,OAAO,CAC5B,UACA,QACR,GACKjK,GAAG,GACR,IAAI,CAACoqB,WAAW,CAAG3qB,EACdI,CAAC,CAAC,qBACFG,GAAG,CAAC,IAAI,CAACb,OAAO,EACjB,IAAI,CAACH,OAAO,CAAC8a,IAAI,EACjB,IAAI,CAACsQ,WAAW,CAACrqB,IAAI,CAAC,IAAI,CAACR,KAAK,CAACC,WAAW,EAEhD,IAAI,CAAC6qB,WAAW,CAAG5qB,EACdI,CAAC,CAAC,qBACFT,IAAI,CAAC,CAENiM,WAAY,EACZC,WAAY,CAChB,GACKtL,GAAG,CAAC,IAAI,CAACb,OAAO,EACrB,IAAI,CAACmpB,YAAY,GACb,IAAI,CAAC5oB,QAAQ,EACb,IAAI,CAACP,OAAO,CAACY,IAAI,CAAC,IAAI,CAACL,QAAQ,EAGnC,IAAI,CAAC4qB,WAAW,CAAC,IAAI,CAAC3hB,MAAM,EAC5B,IAAI,CAAC2hB,WAAW,CAAC,IAAI,CAAC5oB,MAAM,EAC5B,IAAI,CAAC4D,SAAS,GACd,IAAI,CAAC+I,mBAAmB,EAC5B,CAIAwb,WAAWhW,CAAI,CAAE,CACbA,EAAKlK,MAAM,CAACkK,AAAkB,UAAlBA,EAAK1E,QAAQ,CACrB,IAAI,CAACkb,WAAW,CAChB,IAAI,CAACD,WAAW,CACxB,CAIAE,YAAYH,CAAK,CAAE,CACf,IAAIhqB,EAAIgqB,EAAMhpB,MAAM,CACpB,KAAOhB,KACH,IAAI,CAAC0pB,UAAU,CAACM,CAAK,CAAChqB,EAAE,CAEhC,CAIAooB,aAAc,CACV,IAAMgC,EAAQ,IAAI,CAAChrB,KAAK,CAAC4C,KAAK,CAAEqoB,EAAQ,IAAI,CAACjrB,KAAK,CAACqL,KAAK,CAAE6f,EAAa,AAAC,CAAA,IAAI,CAACzrB,OAAO,CAAC0C,MAAM,EAAI,EAAE,AAAD,EAC3FgpB,MAAM,CAAC,IAAI,CAAC1rB,OAAO,CAAC2J,MAAM,EAAI,EAAE,EAChCgiB,MAAM,CAAC,CAACC,EAAMC,KACf,IAAMlgB,EAAQkgB,GACTA,CAAAA,EAAalgB,KAAK,EACdkgB,EAAa/oB,MAAM,EAAI+oB,EAAa/oB,MAAM,CAAC,EAAE,EACtD,MAAO,CACHyoB,CAAK,CAAC5f,GAASA,EAAMxI,KAAK,CAAC,EAAIyoB,CAAI,CAAC,EAAE,CACtCJ,CAAK,CAAC7f,GAASA,EAAMC,KAAK,CAAC,EAAIggB,CAAI,CAAC,EAAE,CACzC,AACL,EAAG,EAAE,CACL,CAAA,IAAI,CAACpC,SAAS,CAAGiC,CAAU,CAAC,EAAE,CAC9B,IAAI,CAAChC,SAAS,CAAGgC,CAAU,CAAC,EAAE,AAClC,CAIAza,2BAA2B/F,CAAO,CAAE,CAChC,IAAM6gB,EAAiC,SAAUjX,CAAI,EACjDA,EAAK7D,0BAA0B,CAAC/F,EACpC,EACA,IAAI,CAACiD,aAAa,CAACjN,OAAO,CAAC,AAAC0N,IACxBA,EAAa3D,aAAa,CAACC,EAC/B,GACA,IAAI,CAACtB,MAAM,CAAC1I,OAAO,CAAC6qB,GACpB,IAAI,CAACppB,MAAM,CAACzB,OAAO,CAAC6qB,EACxB,CAIArB,mBAAoB,CAChB,IAAM3qB,EAAa,IAAI,AACvBA,CAAAA,EAAWoqB,cAAc,CAAG,WACxB,OAAOpqB,EAAW4C,MAAM,CAACipB,MAAM,CAAC,SAAUjpB,CAAM,CAAEC,CAAK,EAInD,OAHKA,EAAM3C,OAAO,CAAC+rB,YAAY,EAC3BrpB,EAAOxC,IAAI,CAACyC,EAAMxC,OAAO,EAEtBuC,CACX,EAAG,EAAE,CACT,EACA5C,EAAWS,KAAK,CAAC0pB,eAAe,CAAC/pB,IAAI,CAACJ,EAAWoqB,cAAc,CACnE,CAOAG,WAAWzqB,CAAW,CAAE,CACpB,IAAI,CAACI,OAAO,CAAGyoB,GAAiB,IAAI,CAACW,cAAc,CAAExpB,EACzD,CAQAoL,cAAcC,CAAO,CAAE,CACnB,IAAMjL,EAAU,IAAI,CAACA,OAAO,CAAEgjB,EAAa,IAAI,CAACziB,KAAK,CAACwkB,kBAAkB,CAAEiG,EAAatC,GAAgBzd,EAAS,CAACjL,EAAQiL,OAAO,EAEhI,GADA,IAAI,CAAC9K,OAAO,CAACC,IAAI,CAAC,aAAc4qB,EAAa,UAAY,UACrD,CAACA,EAAY,CACb,IAAMc,EAAiC,SAAUjX,CAAI,EACjDA,EAAK7D,0BAA0B,CAACga,EACpC,EACA,IAAI,CAACrhB,MAAM,CAAC1I,OAAO,CAAC6qB,GACpB,IAAI,CAACppB,MAAM,CAACzB,OAAO,CAAC6qB,GAChB9I,EAAW8B,gBAAgB,GAAK,IAAI,EACpC9B,EAAW/G,KAAK,EAChB+G,AAA0B,uBAA1BA,EAAW/G,KAAK,CAACtW,IAAI,EACrB6iB,GAAqBxF,EAAY,aAEzC,CACAhjB,EAAQiL,OAAO,CAAG+f,CACtB,CAUA9f,OAAOtL,CAAW,CAAEC,CAAM,CAAE,CACxB,IAAMU,EAAQ,IAAI,CAACA,KAAK,CAAE8oB,EAAkBV,GAA0B,IAAI,CAAC/oB,WAAW,CAAEA,GAAcosB,EAAmBzrB,EAAMN,WAAW,CAACqH,OAAO,CAAC,IAAI,EAAGtH,EAAUyoB,GAAiB,CAAA,EAAM,IAAI,CAAC7oB,WAAW,CAAEA,EAC7MI,CAAAA,EAAQ0C,MAAM,CAAG2mB,EAAgB3mB,MAAM,CACvC1C,EAAQ2J,MAAM,CAAG0f,EAAgB1f,MAAM,CACvC,IAAI,CAACrI,OAAO,GACZ,IAAI,CAAC8oB,cAAc,CAAC7pB,EAAOP,GAC3B,IAAI,CAACqQ,IAAI,CAAC9P,EAAOP,GAEjBO,EAAMP,OAAO,CAACC,WAAW,CAAC+rB,EAAiB,CAAG,IAAI,CAAChsB,OAAO,CAC1D,IAAI,CAACgI,UAAU,CAAG,CAAA,EACd0gB,GAAgB7oB,EAAQ,CAAA,IACxBU,EAAMc,eAAe,GAEzBmnB,GAAqB,IAAI,CAAE,eAC3B,IAAI,CAACxgB,UAAU,CAAG,CAAA,CACtB,CACJ,CASAihB,GAAWgD,YAAY,CAAG1hB,EAI1B0e,GAAWxd,SAAS,CAxuHwCA,EAgvH5Dwd,GAAWyB,SAAS,CAAG,CACnB,KAlrFiErV,GAmrFjE,OAjkFmEK,GAkkFnE,QA32EoEM,GA42EpE,KA/xFiEpC,GAgyFjE,MAz5DkEsH,EA05DtE,EAIA+N,GAAWvjB,KAAK,CAAG,CAAC,EACpBujB,GAAWjqB,SAAS,CAACoqB,cAAc,CA1mKR,CAevBne,QAAS,CAAA,EAiBTR,UAAW,CAAC,EAUZqQ,KAAM,CAAA,EAmBNtT,UAAW,KAQXhF,aAAc,CAUVuW,MAAO,SASPgT,aAAc,CAAA,EAUdnS,gBAAiB,sBASjBmB,YAAa,UAObE,aAAc,EAOdD,YAAa,EASblK,UAAW,wBAQXgK,KAAM,CAAA,EA8CNd,UAAW,WACP,OAAOnU,EAAQ,IAAI,CAACgC,CAAC,EAAI,GAAK,IAAI,CAACA,CAAC,CAAG,kBAC3C,EAWApF,oBAAqB,CAAA,EAWrBoY,SAAU,UAQVvB,QAAS,EAWTO,OAAQ,CAAA,EASRjQ,MAAO,UAWPmB,MAAO,CAEHmhB,SAAU,QAEVC,WAAY,SAEZ7Y,MAAO,UACX,EAKAzM,QAAS,CAAA,EASTmS,cAAe,SASfhW,EAAG,EASH6E,EAAG,GACP,EAkHAuJ,aAAc,CAuFViC,OAAQ,sBAORiB,YAAa,EASbf,KAAM,sBAONoC,EAAG,EAKHlB,KAAM,CACV,EAUArG,oBAAqB,CAQjBrH,OAAQ,CAAC,EAITgE,MAAO,CACHnD,OAAQ,UACR2L,KAAM,UACNF,OAAQ,UACR,eAAgB,CACpB,EACAvI,OAAQ,GACRF,OAAQ,SACRK,QAAS,CAAA,EACTJ,MAAO,EACX,EAyCA9D,OAAQ,CAAC,EAITjG,OAAQ,CACZ,EA+hJAmoB,GAAWjqB,SAAS,CAACqI,YAAY,CAAG,CAAC,MAAO,cAAe,OAAQ,SAAS,CAC5E2I,EAA0BjL,OAAO,CAACkkB,IAML,IAAMmD,GAA0BnD,GA4DvD,CAAE5e,MAAOgiB,EAAqB,CAAE,CAAIjtB,GAM1C,OAAMktB,WAAwBF,GAM1Bne,kBAAmB,CACf,IAAMjO,EAAU,IAAI,CAACA,OAAO,CAAEkO,EAAgBoe,GAAgBC,kBAAkB,CAAEC,EAAiB,IAAI,CAACC,SAAS,CAGjHC,AAHmI1sB,CAAAA,EAAQ0C,MAAM,EAC7I1C,EAAQ2J,MAAM,EACd,EAAE,AAAD,EACQ1I,OAAO,CAAC,AAAC0rB,IAClBA,EAAMze,aAAa,CAAGA,CAAa,CAACse,EAAe,AACvD,EACJ,CACAnc,MAAO,CACH,IAAMrQ,EAAU,IAAI,CAACA,OAAO,CAC5B,GAAIA,EAAQ2J,MAAM,CAAE,CAChB,OAAO3J,EAAQwC,YAAY,CAC3B,IAAMmD,EAAO3F,EAAQ2J,MAAM,CAAC,EAAE,CAAChE,IAAI,AACnC3F,CAAAA,EAAQ2J,MAAM,CAAC,EAAE,CAACmH,SAAS,CACvB,AAAC9Q,CAAAA,EAAQ2J,MAAM,CAAC,EAAE,CAACmH,SAAS,EAAI,EAAC,EAAK,0BAGtCnL,GAAQA,AAAS,SAATA,EACR,IAAI,CAAC8mB,SAAS,CAAG9mB,EAGjB,IAAI,CAAC8mB,SAAS,CAAG,WAEzB,MAEI,OAAOzsB,EAAQ2J,MAAM,CACrB,IAAI,CAAC8iB,SAAS,CAAG,QAErB,KAAK,CAACpc,KAAKzL,KAAK,CAAC,IAAI,CAAEE,UAC3B,CACJ,CAMAwnB,GAAgBC,kBAAkB,CAAG,CACjC5pB,MAAO,CAAC,CACAiI,OAAQ,gBACRF,WAAY,SAAUtD,CAAM,EACxB,GAAI,CAACA,EAAOjH,OAAO,CAACyU,MAAM,CACtB,MAAO,CACH5R,EAAG,EACH6E,EAAG,IACP,EAEJ,IAAMC,EAAK8H,AAh4HiCnE,EAi4HvCI,aAAa,CAACzE,EAAOtE,MAAM,CAAC,EAAE,EACnC,MAAO,CACHE,EAAG8E,EAAG9E,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EACtChD,EAAGC,EAAGD,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CAC3C,CACJ,EAEA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAMU,EAAK,IAAI,CAACsB,sBAAsB,CAAC1C,GACvCU,EAAOyI,cAAc,CAAC/H,EAAG9E,CAAC,CAAE8E,EAAGD,CAAC,EAChCT,EAAOtH,UAAU,CAACF,WAAW,CAAC8C,MAAM,CAAC,EAAE,CAACiJ,KAAK,CACzCvE,EAAOpH,OAAO,CAAC2L,KAAK,CACxBvE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC+K,OAAQ,SACRF,WAAY,SAAUtD,CAAM,SACxB,AAAKA,EAAOjH,OAAO,CAACyU,MAAM,CAMnB,CACH5R,EAAGoE,EAAOjH,OAAO,CAACiZ,SAAS,CAACpW,CAAC,CACzB,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAChChD,EAAGT,EAAOjH,OAAO,CAACiZ,SAAS,CAACvR,CAAC,CACzB,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACrC,EAVW,CACH9H,EAAG,EACH6E,EAAG,IACP,CAQR,EAGAd,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAMU,EAAK,IAAI,CAACsB,sBAAsB,CAAC1C,GACvCU,EAAOsC,SAAS,CAAC5B,EAAG9E,CAAC,CAAE8E,EAAGD,CAAC,EAC3BT,EAAOtH,UAAU,CAACF,WAAW,CAAC8C,MAAM,CAAC,EAAE,CAACiJ,KAAK,CACzCvE,EAAOpH,OAAO,CAAC2L,KAAK,CACxBvE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,CACNgtB,UAAW,CAAC,CACJniB,WAAY,SAAU5K,CAAU,EAC5B,IAAMgI,EAAK8H,AA/6HiCnE,EAg7HvCI,aAAa,CAAC/L,EAAWgD,MAAM,CAAC,EAAE,EACvC,MAAO,CACHE,EAAG8E,EAAG9E,CAAC,CAAG,EACV6E,EAAGC,EAAGD,CAAC,CAAG,CACd,CACJ,EACAd,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgtB,EAAS,IAAI,CAACvsB,KAAK,CAAC2G,OAAO,EAAE6lB,eAAermB,GAAI5D,EAASsE,EAAOpH,OAAO,CAAC8C,MAAM,CAAE6G,EAAS7J,EAAWF,WAAW,CAAC+J,MAAM,CAAE1G,EAAanD,EAAW0pB,SAAS,EAAEpmB,OAAS,EAAGkU,EAAaxX,EAAW2pB,SAAS,EAAErmB,OAAS,EACzP,GAAI0pB,EAAQ,CACR,IAAM9pB,EAAI8pB,EAAO3pB,KAAK,CAACF,EAAW,CAAC+P,KAAK,CAAEnL,EAAIilB,EAAOlhB,KAAK,CAAC0L,EAAW,CAACtE,KAAK,AAE5ElQ,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EAEdF,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EACdF,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGA,EAEd/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGA,EACV8B,GAAUA,CAAM,CAAC,EAAE,EACnBA,CAAAA,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAGsE,EAAOpH,OAAO,CAAC8C,MAAM,AAAD,CAE/C,CACAhD,EAAWD,MAAM,CAAC,CAAA,EACtB,CACJ,CACJ,EAAE,CACN+V,OAAQ,CAAC,CACDlL,WAAY,SAAUtD,CAAM,EACxB,IAAMU,EAAK8H,AA58HiCnE,EA48HXI,aAAa,CAACzE,EAAOtE,MAAM,CAAC,EAAE,EAAG6S,EAAIvO,EAAOpH,OAAO,CAAC2V,CAAC,CACtF,MAAO,CACH3S,EAAG8E,EAAG9E,CAAC,CAAG2S,EAAIjS,KAAK2J,GAAG,CAAC3J,KAAKwT,EAAE,CAAG,GAC7B,AAAC,CAAA,IAAI,CAAC/W,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAChChD,EAAGC,EAAGD,CAAC,CAAG8N,EAAIjS,KAAK4J,GAAG,CAAC5J,KAAKwT,EAAE,CAAG,GAC7B,AAAC,CAAA,IAAI,CAAC/W,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACrC,CACJ,EACA/D,OAAQ,CAGJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEqU,EAAW,IAAI,CAAC/K,sBAAsB,CAAC1C,GAAIiD,EAAS7J,EAAWF,WAAW,CAAC+J,MAAM,CACvHvC,EAAOyO,SAAS,CAACnS,KAAKC,GAAG,CAACyD,EAAOpH,OAAO,CAAC2V,CAAC,CACtCxB,EAAStM,CAAC,CACNnE,KAAK4J,GAAG,CAAC5J,KAAKwT,EAAE,CAAG,GAAI,IAC3BvN,GAAUA,CAAM,CAAC,EAAE,GACnBA,CAAM,CAAC,EAAE,CAACgM,CAAC,CAAGvO,EAAOpH,OAAO,CAAC2V,CAAC,CAC9BhM,CAAM,CAAC,EAAE,CAACgC,KAAK,CAAGvE,EAAOpH,OAAO,CAAC2L,KAAK,EAE1CvE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,CACNmtB,QAAS,CAAC,CACFtiB,WAAY,SAAUtD,CAAM,EACxB,IAAM+M,EAAW/M,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAC5D,MAAO,CACHE,EAAGmR,EAASnR,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGsM,EAAStM,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACjD,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAM+M,EAAW/M,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAC5DsE,EAAOyI,cAAc,CAACnJ,EAAEmC,MAAM,CAAGsL,EAASnR,CAAC,CAAE0D,EAAEiC,MAAM,CAAGwL,EAAStM,CAAC,CAAE,GACpET,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC6K,WAAY,SAAUtD,CAAM,EACxB,IAAM+M,EAAW/M,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAC5D,MAAO,CACHE,EAAGmR,EAASnR,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGsM,EAAStM,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACjD,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAM+M,EAAW/M,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAC5DsE,EAAOyI,cAAc,CAACnJ,EAAEmC,MAAM,CAAGsL,EAASnR,CAAC,CAAE0D,EAAEiC,MAAM,CAAGwL,EAAStM,CAAC,CAAE,GACpET,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC6K,WAAY,SAAUtD,CAAM,EACxB,IAAM+M,EAAW/M,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAAG4T,EAAYtP,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAAG6N,EAAQvJ,EAAOqP,QAAQ,CAACtC,EAAUuC,GAC3J,MAAO,CACH1T,EAAG2N,EAAMvI,EAAE,CAAG,AAAC,CAAA,IAAI,CAACjI,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EACtC8F,EAAMwG,EAAE,CAAGzT,KAAK4J,GAAG,CAAC,AAACqD,EAAMqG,KAAK,CAAGtT,KAAKwT,EAAE,CAAI,KAClDrP,EAAG8I,EAAMtI,EAAE,CAAG,AAAC,CAAA,IAAI,CAAClI,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,EACvC6F,EAAMwG,EAAE,CAAGzT,KAAK2J,GAAG,CAAC,AAACsD,EAAMqG,KAAK,CAAGtT,KAAKwT,EAAE,CAAI,IACtD,CACJ,EACAnQ,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAM+M,EAAW/M,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAAG4T,EAAYtP,EAAOmQ,mBAAmB,CAACnQ,EAAOtE,MAAM,CAAC,EAAE,EAAGmqB,EAAO7lB,EAAO8O,mBAAmB,CAAC/B,EAAUuC,EAAWhQ,EAAEmC,MAAM,CAAEnC,EAAEiC,MAAM,EAAGiD,EAAQxE,EAAOiQ,QAAQ,GAAI6V,EAAQxpB,KAAK6S,GAAG,CAAC3K,EAAMsB,OAAO,CAAC,GAAKtB,EAAMsB,OAAO,CAAC+f,IAClR7lB,EAAOuQ,UAAU,CAACuV,GAClB9lB,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,AACV,EACAysB,GAAgBttB,SAAS,CAACoqB,cAAc,CAAGiD,GAAsBD,GAAuBptB,SAAS,CAACoqB,cAAc,CAAE,CAAC,GACnHgD,GAAuB1mB,KAAK,CAACynB,eAAe,CAAGb,GAmB/C,GAAM,CAAEjiB,MAAO+iB,EAAiB,CAAE,CAAIhuB,GAMtC,OAAMiuB,WAAoBjB,GAUtB7C,aAAc,CACV,IAAI,CAACC,SAAS,CAAG,IAAI,CAACjpB,KAAK,CAAC4C,KAAK,CAAC,IAAI,CAACnD,OAAO,CAACstB,WAAW,CAACnqB,KAAK,CAAC,CACjE,IAAI,CAACsmB,SAAS,CAAG,IAAI,CAAClpB,KAAK,CAACqL,KAAK,CAAC,IAAI,CAAC5L,OAAO,CAACstB,WAAW,CAAC1hB,KAAK,CAAC,AACrE,CACAgD,kBAAmB,CACf,IAAM0e,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAC5C,MAAO,AAACA,CAAAA,EAAYxqB,MAAM,EAAI,EAAE,AAAD,EAAG2N,GAAG,CAAC,AAACzB,IACnCA,EAAa7L,KAAK,CAAGmqB,EAAYnqB,KAAK,CACtC6L,EAAapD,KAAK,CAAG0hB,EAAY1hB,KAAK,CAC/BoD,GAEf,CACAue,yBAA0B,CACtB,OAAO,IAAI,CAAC3e,gBAAgB,EAChC,CACAX,kBAAmB,CACf,IAAI,CAACsf,uBAAuB,GAAGtsB,OAAO,CAAC,SAAU+N,CAAY,CAAE7N,CAAC,EAC5D,IAAMwN,EAAe,IAAIpE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE6sB,GAAkB,IAAI,CAACptB,OAAO,CAACoO,mBAAmB,CAAEY,EAAaL,YAAY,EAAGxN,GACpJ,IAAI,CAAC+M,aAAa,CAAChO,IAAI,CAACyO,GACxBK,EAAaL,YAAY,CAAGA,EAAa3O,OAAO,AACpD,EAAG,IAAI,CACX,CACA8pB,WAAY,CACR,IAAMwD,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAAE1jB,EAAQ,IAAI,CAACmgB,SAAS,CAACqD,GAAkBE,EAAYE,IAAI,CAAE,CACrG7nB,KAAM,OACNmL,UAAW,2BACXhO,OAAQ,IAAI,CAACA,MAAM,CAAC2N,GAAG,CAAC,CAACf,EAAQvO,IAAO,SAAUiG,CAAM,EACpD,OAAOA,EAAOtH,UAAU,CAACgD,MAAM,CAAC3B,EAAE,AACtC,EACJ,GAAI,EACJmsB,CAAAA,EAAYE,IAAI,CAAG5jB,EAAM5J,OAAO,AACpC,CACJ,CACAqtB,GAAYruB,SAAS,CAACoqB,cAAc,CAAGgE,GAAkBhB,GAAuBptB,SAAS,CAACoqB,cAAc,CAUxG,CAYIkE,YAAa,CAMTnqB,MAAO,EAMPyI,MAAO,EA2BP4hB,KAAM,CACFja,KAAM,MACV,CACJ,EAIAnF,oBAAqB,CACjB1D,WAAY,SAAUtD,CAAM,EACxB,IAAMjH,EAAU,IAAI,CAACA,OAAO,CAAE2H,EAAK8H,AAhqIanE,EAgqISI,aAAa,CAACzE,EAAOtE,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,EAChG,MAAO,CACHJ,EAAG8E,EAAG9E,CAAC,CAAG,AAAC7C,CAAAA,EAAQ0K,KAAK,EAAI,CAAA,EAAK,EACjChD,EAAGC,EAAGD,CAAC,CAAG,AAAC1H,CAAAA,EAAQ2K,MAAM,EAAI,CAAA,EAAK,CACtC,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,GAAIA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC7FC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAAI4mB,EAAclmB,EAAOpH,OAAO,CAACstB,WAAW,CAC5FlmB,EAAOyI,cAAc,CAACpG,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,CAAE,IAAI,CAACzE,KAAK,EAE9DkqB,EAAYxqB,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACJ,CAAC,CAC5BoE,EAAOtE,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACJ,CAAC,CAC/BsqB,EAAYxqB,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACyE,CAAC,CAC5BT,EAAOtE,MAAM,CAAC,IAAI,CAACM,KAAK,CAAC,CAACyE,CAAC,CAC/BT,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,CACJ,CACJ,GACAusB,GAAuB1mB,KAAK,CAAC+nB,WAAW,CAAGJ,GAMd,IAAMK,GAAqBL,GAYlD,CAAEhjB,MAAOsjB,EAAiB,CAAE,CAAIvuB,GAMtC,OAAMwuB,WAAoBF,GAMtB/D,WAAY,CACR,IAAI,CAAC/a,gBAAgB,GAAG3N,OAAO,CAAC,CAAC0K,EAAOxK,KACpC,IAAMmsB,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAAE3qB,EAAQ,IAAI,CAACknB,SAAS,CAAC8D,GAAkBhiB,EAAMhJ,KAAK,CAAE,CAChGC,KAAM0qB,EAAY5qB,MAAM,CAACvB,EAAE,CAC3BwK,MAAO,SAAUvE,CAAM,EACnB,OAAOA,EAAOtH,UAAU,CAACgD,MAAM,CAAC3B,EAAE,AACtC,CACJ,GAAI,CAAA,EACJwK,CAAAA,EAAMhJ,KAAK,CAAGA,EAAM3C,OAAO,AAC/B,EACJ,CACJ,CACA4tB,GAAY5uB,SAAS,CAACoqB,cAAc,CAAGuE,GAAkBD,GAAkB1uB,SAAS,CAACoqB,cAAc,CAWnG,CACIkE,YAAa,CAQT5qB,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAM,CAClD8qB,KAAM,CACFlZ,YAAa,CACjB,CACJ,EACA9R,aAAc,CACVuW,MAAO,SACPgT,aAAc,CAAA,EACdjR,KAAM,CAAA,EACND,SAAU,OACVlV,KAAM,OACNiU,gBAAiB,OACjBoB,YAAa,EACbnT,EAAG,EACP,CACJ,GACAukB,GAAuB1mB,KAAK,CAACmoB,WAAW,CAAGD,GAoB3C,GAAM,CAAEvjB,MAAOyjB,EAAY,CAAE,CAAI1uB,GAiBjC,OAAM2uB,WAAeL,GAMjB9e,kBAAmB,CACf,IAAMG,EAAgB2e,GAAkB1uB,SAAS,CAAC4P,gBAAgB,CAAC1P,IAAI,CAAC,IAAI,EAAGoY,EAAa,IAAI,CAACtX,OAAO,CAACstB,WAAW,CAAC1hB,KAAK,EAAI,EAAGA,EAAQ,IAAI,CAACrL,KAAK,CAACqL,KAAK,CAAC0L,EAAW,CAIrK,GAHAvI,CAAa,CAAC,EAAE,CAAG,IAAI,CAACif,kBAAkB,CAACjf,CAAa,CAAC,EAAE,EAC3DA,CAAa,CAAC,EAAE,CAAG,IAAI,CAACif,kBAAkB,CAACjf,CAAa,CAAC,EAAE,EAEvDnD,GAASA,EAAMqiB,WAAW,CAAE,CAE5B,IAAM3V,EAAI1M,EAAMmB,QAAQ,CAACgC,CAAa,CAAC,EAAE,CAAClH,CAAC,EACvC+D,EAAMmB,QAAQ,CAACgC,CAAa,CAAC,EAAE,CAAClH,CAAC,EAErCqmB,EAAKtiB,EAAMmB,QAAQ,CAACgC,CAAa,CAAC,EAAE,CAAClH,CAAC,EAAIyQ,CAE1CvJ,CAAAA,CAAa,CAAC,EAAE,CAAClH,CAAC,CAAG+D,EAAMsB,OAAO,CAACghB,EACvC,CACA,OAAOnf,CACX,CACAwe,yBAA0B,CACtB,OAAO,IAAI,CAAC3e,gBAAgB,GAAG/J,KAAK,CAAC,EAAG,EAC5C,CACAmpB,mBAAmBhf,CAAY,CAAE,CAC7B,IAAMgf,EAAqBF,GAAa9e,GAAese,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAE7F,OADAU,EAAmBnmB,CAAC,EAAIylB,EAAYxiB,MAAM,CACnCkjB,CACX,CACA/f,kBAAmB,CACfyf,GAAkB1uB,SAAS,CAACiP,gBAAgB,CAAC/O,IAAI,CAAC,IAAI,EACtD,IAAMc,EAAU,IAAI,CAACA,OAAO,CAAEstB,EAActtB,EAAQstB,WAAW,CAAE3e,EAAe,IAAIpE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAEutB,GAAa9tB,EAAQoO,mBAAmB,CAAEkf,EAAYa,kBAAkB,EAAG,GAC1M,IAAI,CAACjgB,aAAa,CAAChO,IAAI,CAACyO,GACxB2e,EAAYa,kBAAkB,CAAGxf,EAAa3O,OAAO,AACzD,CACA8pB,WAAY,CACR,IAAI,CAACsE,OAAO,GACZ,IAAI,CAACC,aAAa,EACtB,CACAD,SAAU,CACN,IAAMZ,EAAO,IAAI,CAACzD,SAAS,CAAC+D,GAAa,IAAI,CAAC9tB,OAAO,CAACstB,WAAW,CAACE,IAAI,CAAE,CACpE7nB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd,IAAI,CAACA,MAAM,CAAC,EAAE,CACd,SAAUsE,CAAM,EACZ,IAAM4H,EAAeY,AA11ImBnE,EA01IGc,cAAc,CAACnF,EAAOtH,UAAU,CAACgD,MAAM,CAAC,EAAE,EAErF,OADAkM,EAAatC,OAAO,CAAG,IAChBsC,CACX,EACA,IAAI,CAAClM,MAAM,CAAC,EAAE,CACjB,CACDgO,UAAW,yBACf,GAAI,EACJ,CAAA,IAAI,CAAC9Q,OAAO,CAACstB,WAAW,CAACE,IAAI,CAAGA,EAAKxtB,OAAO,AAChD,CACAquB,eAAgB,CACZ,IAAMC,EAAa,IAAI,CAACvE,SAAS,CAAC+D,GAAa,IAAI,CAAC9tB,OAAO,CAACstB,WAAW,CAACgB,UAAU,CAAE,CAChF3oB,KAAM,OACN7C,OAAQ,IAAI,CAACA,MAAM,CAAC+B,KAAK,GACzBiM,UAAW,8BACf,GAAI,EACJ,CAAA,IAAI,CAAC9Q,OAAO,CAACstB,WAAW,CAACgB,UAAU,CAAGA,EAAWtuB,OAAO,AAC5D,CAWAuuB,cAAc3lB,CAAE,CAAEF,CAAE,CAAE8lB,CAAG,CAAE,CACvB,IAAMC,EAAWC,OAAOF,GACxB,IAAI,CAAC3e,cAAc,CAACjH,EAAIF,EAAI+lB,GAC5B,IAAI,CAAC5e,cAAc,CAACjH,EAAIF,EAFoB+lB,AAAa,IAAbA,EAAiB,EAAI,EAGrE,CAOAE,gBAAgBC,CAAE,CAAE,CAChB,IAAI,CAAC/e,cAAc,CAAC,EAAG+e,EAAI,GAC3B,IAAI,CAAC/e,cAAc,CAAC,EAAG+e,EAAI,GAC3B,IAAI,CAAC5uB,OAAO,CAACstB,WAAW,CAACxiB,MAAM,CAAG,IAAI,CAAChI,MAAM,CAAC,EAAE,CAAC+E,CAAC,CAC9C,IAAI,CAAC/E,MAAM,CAAC,EAAE,CAAC+E,CAAC,CACpB,IAAI,CAACjI,WAAW,CAAC0tB,WAAW,CAACxiB,MAAM,CAAG,IAAI,CAAC9K,OAAO,CAACstB,WAAW,CAACxiB,MAAM,AACzE,CACJ,CACAijB,GAAO/uB,SAAS,CAACoqB,cAAc,CAAG0E,GAAaJ,GAAkB1uB,SAAS,CAACoqB,cAAc,CAUzF,CACIkE,YAAa,CAQTgB,WAAY,CACR/a,KAAM,2BACNe,YAAa,CACjB,EACAkZ,KAAM,CACFlZ,YAAa,CACjB,EAIAxJ,OAAQ,GAQRqjB,mBAAoB,CAChBzjB,WAAY,SAAUtD,CAAM,EACxB,IAAMynB,EAAUjf,AAh7I4BnE,EAg7INI,aAAa,CAACzE,EAAOtE,MAAM,CAAC,EAAE,EAAGgsB,EAAQlf,AAh7InCnE,EAg7IyDI,aAAa,CAACzE,EAAOtE,MAAM,CAAC,EAAE,EAAGE,EAAI,AAAC6rB,CAAAA,EAAQ7rB,CAAC,CAAG8rB,EAAM9rB,CAAC,AAADA,EAAK,EAClK,MAAO,CACHA,EAAGA,EAAI,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EACnChD,EAAGknB,AA/IXtf,CAAAA,AA+IwCqf,EA/IrCjnB,CAAC,CAAG2H,AA+IwBqf,EA/IrBhnB,CAAC,AAADA,EAAM4H,CAAAA,AA+IwBqf,EA/IrB9rB,CAAC,CAAGwM,AA+IQqf,EA/IL7rB,CAAC,AAADA,EAAMA,CAAAA,AA+IeA,EA/IXwM,AA+ILqf,EA/IQ7rB,CAAC,AAADA,EAAKwM,AA+Ibqf,EA/IgBhnB,CAAC,CAgJpC,AAAC,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,EAAK,CACrC,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACjBA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC7FC,gBAAiB,CAAA,CACrB,KACIpC,EAAOunB,eAAe,CAAC,IAAI,CAACvlB,sBAAsB,CAAC1C,GAAGmB,CAAC,EACvDT,EAAOvH,MAAM,CAAC,CAAA,GAEtB,CACJ,CACJ,CACJ,EAKAuO,oBAAqB,CACjBrH,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,GAAIA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC7FC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAChDU,EAAOmnB,aAAa,CAAC9kB,EAAYzG,CAAC,CAAEyG,EAAY5B,CAAC,CAAE,CAAC,CAAC,IAAI,CAACzE,KAAK,EAC/DgE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,CACJ,CACJ,GACAusB,GAAuB1mB,KAAK,CAACspB,MAAM,CAAGjB,GAMT,IAAMkB,GAAgBlB,GAa7C,CAAE1jB,MAAO6kB,EAAkB,CAAE,CAAI9vB,GAMvC,OAAM+vB,WAAqBzB,GAMvB,OAAO0B,UAAUC,CAAU,CAAEC,CAAQ,CAAE,CACnC,OAAO,SAAUloB,CAAM,EACnB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAE6F,EAAO7F,EAAWE,OAAO,CAACstB,WAAW,CAAC3nB,IAAI,CAC5E7C,EAAShD,EAAWgD,MAAM,CAe9B,MAdI6C,CAAAA,AAAS,mBAATA,GAA6BA,AAAS,iBAATA,CAAsB,GAGnD7C,CAAAA,EAAS,CACLA,CAAM,CAAC,EAAE,CACT,IA7/IwC2I,EA6/Id3L,EAAWS,KAAK,CAAEuC,CAAM,CAAC,EAAE,CAACsE,MAAM,CAAE,CAE1DpE,EAAGF,CAAM,CAAC,EAAE,CAACE,CAAC,EAAG,CAAE2C,CAAAA,AAAS,mBAATA,CAAwB,EAC3CkC,EAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,EAAG,CAAElC,CAAAA,AAAS,iBAATA,CAAsB,EACzCxC,MAAOL,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAACmD,KAAK,CAC9ByI,MAAO9I,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAAC4L,KAAK,AAClC,GACH,AAAD,EAEGujB,GAAaI,aAAa,CAACzsB,CAAM,CAACusB,EAAW,CAAEvsB,CAAM,CAACwsB,EAAS,CAC1E,CACJ,CACA,OAAOE,mBAAmBC,CAAU,CAAEC,CAAW,CAAEhiB,CAAI,CAAEiiB,CAAwB,CAAE,CAC/E,IAAMC,EAAeliB,AAAS,MAATA,EAAe,IAAM,IAG1C,MAAQ,AAACgiB,CAAAA,CAAW,CAAChiB,EAAK,CAAG+hB,CAAU,CAAC/hB,EAAK,AAAD,EACvCiiB,CAAAA,EAA2BF,CAAU,CAACG,EAAa,AAAD,EAClDF,CAAAA,CAAW,CAACE,EAAa,CAAGH,CAAU,CAACG,EAAa,AAAD,EACpDH,CAAU,CAAC/hB,EAAK,AACxB,CACA,OAAO6hB,cAAcE,CAAU,CAAEC,CAAW,CAAE,CAC1C,IAIIG,EAAYC,EAAYC,EAJtBxvB,EAAQkvB,EAAWvsB,MAAM,CAAC3C,KAAK,CAAE4C,EAAQssB,EAAWvsB,MAAM,CAACC,KAAK,CAAEyI,EAAQ8jB,EAAYxsB,MAAM,CAAC0I,KAAK,CAAEokB,EAAmBpgB,AAnhJzEnE,EAmhJ+FI,aAAa,CAAC4jB,GAAaQ,EAAoBrgB,AAnhJ9InE,EAmhJoKI,aAAa,CAAC6jB,GAAcQ,EAASD,EAAkBjtB,CAAC,CAAGgtB,EAAiBhtB,CAAC,CAAEmtB,EAASF,EAAkBpoB,CAAC,CAAGmoB,EAAiBnoB,CAAC,CAAEuoB,EAAWjtB,EAAMgnB,IAAI,CAAEkG,EAAWD,EAAWjtB,EAAM0H,KAAK,CAAEylB,EAAW1kB,EAAMgb,GAAG,CAAE2J,EAAWD,EAAW1kB,EAAMd,MAAM,CAAE0lB,EAASN,EAAS,EAAIE,EAAWC,EAAUI,EAASN,EAAS,EAAIG,EAAWC,EAAUnB,EAAY,CAC5iBpsB,EAAGktB,AAAW,IAAXA,EAAeF,EAAiBhtB,CAAC,CAAGwtB,EACvC3oB,EAAGsoB,AAAW,IAAXA,EAAeH,EAAiBnoB,CAAC,CAAG4oB,CAC3C,EAqBA,OAnBe,IAAXP,GAAgBC,AAAW,IAAXA,IAChBL,EAAaX,GAAaK,kBAAkB,CAACQ,EAAkBC,EAAmB,IAAKO,GACvFX,EAAaV,GAAaK,kBAAkB,CAACQ,EAAkBC,EAAmB,IAAKQ,GACnFX,GAAcQ,GAAYR,GAAcS,GACxCnB,EAAUpsB,CAAC,CAAGwtB,EACdpB,EAAUvnB,CAAC,CAAGioB,IAGdV,EAAUpsB,CAAC,CAAG6sB,EACdT,EAAUvnB,CAAC,CAAG4oB,IAGtBrB,EAAUpsB,CAAC,EAAIzC,EAAM+I,QAAQ,CAC7B8lB,EAAUvnB,CAAC,EAAItH,EAAMgJ,OAAO,CACxBkmB,EAAWvsB,MAAM,CAAC3C,KAAK,CAACwI,QAAQ,GAChCgnB,EAAOX,EAAUpsB,CAAC,CAClBosB,EAAUpsB,CAAC,CAAGosB,EAAUvnB,CAAC,CACzBunB,EAAUvnB,CAAC,CAAGkoB,GAEXX,CACX,CAMAtF,WAAY,CACR,IAAMwD,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAAExqB,EAAS,CACnD,IAAI,CAACA,MAAM,CAAC,EAAE,CACdqsB,GAAauB,YAAY,CAC5B,CAKGpD,EAAY3nB,IAAI,CAACuW,KAAK,CAAC,WACvBpZ,CAAAA,CAAM,CAAC,EAAE,CAAGqsB,GAAawB,cAAc,AAAD,EAE1C,IAAMnD,EAAO,IAAI,CAACzD,SAAS,CAACmF,GAAmB5B,EAAYE,IAAI,CAAE,CAC7D7nB,KAAM,OACN7C,OAAQA,EACRgO,UAAW,2BACf,GAAI,EACJwc,CAAAA,EAAYE,IAAI,CAAGA,EAAKxtB,OAAO,AACnC,CACJ,CAMAmvB,GAAauB,YAAY,CAAGvB,GAAaC,SAAS,CAAC,EAAG,GACtDD,GAAawB,cAAc,CAAGxB,GAAaC,SAAS,CAAC,EAAG,GACxDD,GAAanwB,SAAS,CAACoqB,cAAc,CAAG8F,GAAmBxB,GAAkB1uB,SAAS,CAACoqB,cAAc,CAAE,CAAC,GACxGgD,GAAuB1mB,KAAK,CAACkrB,YAAY,CAAGzB,GAMf,IAAM0B,GAAsB1B,GA+BnD,CAAE9kB,MAAOymB,EAAgB,CAAE5Y,SAAU6Y,EAAmB,CAAElrB,QAASmrB,EAAkB,CAAE,CAAI5xB,GA8CjG,OAAM6xB,WAAmBvD,GAMrBrd,KAAKvQ,CAAU,CAAEF,CAAW,CAAEwD,CAAK,CAAE,CAC7B4tB,GAAmBpxB,EAAYgM,KAAK,GACpChM,EAAYkD,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACxBA,EAAMC,KAAK,CAAGhM,EAAYgM,KAAK,AACnC,GAEAolB,GAAmBpxB,EAAYuD,KAAK,GACpCvD,EAAYkD,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACxBA,EAAMxI,KAAK,CAAGvD,EAAYuD,KAAK,AACnC,GAEJ,KAAK,CAACkN,KAAKvQ,EAAYF,EAAawD,EACxC,CACA8tB,SAAU,CACN,IAAI,CAACvnB,MAAM,CAAC,EAAE,CAAC3J,OAAO,CAAC5B,CAAC,CAAG,IAAI,CAAC+yB,OAAO,EAC3C,CACAA,SAAU,CACN,MAAO,EAxDH,IAwDoB,IAAI,CAACC,MAAM,CAAE,IAAI,CAACvpB,CAAC,EAAE,CAAC6jB,MAAM,CAAC2F,AA5C7D,SAAuBC,CAAa,CAAEC,CAAe,CAAEH,CAAM,CAAEvpB,CAAC,EAC5D,IAAM2M,EAAO,EAAE,CACf,IAAK,IAAIrT,EAAI,EAAGA,GAAKowB,EAAiBpwB,IAClCqT,EAAKtU,IAAI,CAAC,CACN,IACAoxB,EAAgB,EAChBA,EAAgB,EAChB,EACA,EACA,EACAF,EAASjwB,EAAImwB,EACbzpB,EACH,EAEL,OAAO2M,CACX,EA6B2E,IAAI,CAAC8c,aAAa,CAAE,IAAI,CAACC,eAAe,CAAE,IAAI,CAACH,MAAM,CAAE,IAAI,CAACvpB,CAAC,EACpI,CACAiiB,WAAY,CACR,IAAMwD,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAC5C,IAAI,CAACkE,iBAAiB,GACtB,IAAM5nB,EAAQ,IAAI,CAACmgB,SAAS,CAAC+G,GAAiBxD,EAAYE,IAAI,CAAE,CAC5D7nB,KAAM,OACNvH,EAAG,IAAI,CAAC+yB,OAAO,GACfruB,OAAQ,IAAI,CAAC9C,OAAO,CAAC8C,MAAM,CAC3BgO,UAAW,6BACf,GAAI,EACJwc,CAAAA,EAAYE,IAAI,CAAG5jB,EAAM5J,OAAO,AACpC,CACAiO,kBAAmB,CACf,IAAMjO,EAAU,IAAI,CAACA,OAAO,CAAEstB,EAActtB,EAAQstB,WAAW,AAC/DttB,CAAAA,EAAQoO,mBAAmB,CAACrD,KAAK,CAACnD,MAAM,CAAG,IAAI,CAACrH,KAAK,CAACwI,QAAQ,CAC1D,YACA,YACJukB,EAAYlf,mBAAmB,CAACnN,OAAO,CAAC,AAACid,IACrC,IAAM/P,EAAuB2iB,GAAiB9wB,EAAQoO,mBAAmB,CAAE8P,GACrEvP,EAAe,IAAIpE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE4N,EAAsB,GAC1F,IAAI,CAACD,aAAa,CAAChO,IAAI,CAACyO,EAC5B,EACJ,CACA6iB,mBAAoB,CAChB,IAAMxxB,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAExqB,EAAS9C,EAAQ8C,MAAM,CACjE,GAAI,CAACA,EACD,OAEJ,IAAMqT,EAASrT,CAAM,CAAC,EAAE,CAAEsT,EAAStT,CAAM,CAAC,EAAE,CAAE2uB,EAAczxB,EAAQmD,KAAK,EAAI,EAAGuuB,EAAc1xB,EAAQ4L,KAAK,EAAI,EAAGzI,EAAQ,IAAI,CAAC5C,KAAK,CAAC4C,KAAK,CAACsuB,EAAY,CAAE7lB,EAAQ,IAAI,CAACrL,KAAK,CAACqL,KAAK,CAAC8lB,EAAY,CAAEC,EAAUxb,EAAOnT,CAAC,CAAE4uB,EAASzb,EAAOtO,CAAC,CAAEgqB,EAAUzb,EAAOpT,CAAC,CACzP,GAAI,CAAC2uB,GAAW,CAACE,EACb,OAEJ,IAAMhqB,EAAIkpB,GAAoBa,GAC1BhmB,EAAMmB,QAAQ,CAAC6kB,GACfhmB,EAAMgb,GAAG,CAAGhb,EAAMd,MAAM,CAAE9H,EAAI+tB,GAAoBY,GAAWxuB,EAAM4J,QAAQ,CAAC4kB,GAAWxuB,EAAMgnB,IAAI,CAAEtT,EAAKka,GAAoBc,GAAW1uB,EAAM4J,QAAQ,CAAC8kB,GAAW1uB,EAAMgnB,IAAI,CAAG,GAAI2H,EAAc3uB,EAAM2J,GAAG,CAAEwkB,EAAgB5tB,KAAKoV,KAAK,CAACpV,KAAKC,GAAG,CAACD,KAAK6S,GAAG,CAACM,EAAK7T,GAAI,IAErQuuB,EAAkB7tB,KAAKquB,KAAK,CAACD,EAAcR,GAAiB,EAG5DU,EAAa,AAACtuB,CAAAA,KAAKquB,KAAK,CAAC,AAAC/uB,CAAAA,EAAIG,EAAMgnB,IAAI,AAAD,EAAKmH,GAAiB,CAAA,EAAKA,CAClE,CAAA,IAAI,CAACF,MAAM,CAAGpuB,EAAIgvB,EAClB,IAAI,CAACnqB,CAAC,CAAGA,EACT,IAAI,CAACypB,aAAa,CAAGA,EACrB,IAAI,CAACC,eAAe,CAAGA,CAC3B,CACA1xB,OAAO4K,CAAS,CAAE,CACd,IAAI,CAAC+mB,iBAAiB,GACtB,IAAI,CAACN,OAAO,GACZ,KAAK,CAACrxB,OAAO4K,EACjB,CACJ,CACAwmB,GAAWjyB,SAAS,CAACoqB,cAAc,CAAG0H,GAAiBpD,GAAkB1uB,SAAS,CAACoqB,cAAc,CAYjG,CACIkE,YAAa,CAMTlf,oBAAqB,CAAC,CACd1D,WAAY,SAAUtD,CAAM,EACxB,IAAMuE,EAAQvE,EAAOtE,MAAM,CAAC,EAAE,CAC9B,MAAO,CACHE,EAAGmR,AAFoC/M,EAAO0G,MAAM,CAACnC,GAAO8C,gBAAgB,CAEhEzL,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGT,EAAOS,CAAC,CAAI,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,CAC1C,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAM+M,EAAW/M,EAAO0G,MAAM,CAAC1G,EAAOtE,MAAM,CAAC,EAAE,EAAE2L,gBAAgB,CACjErH,EAAOyI,cAAc,CAACnJ,EAAEmC,MAAM,CAAGsL,EAASnR,CAAC,CAAE,EAAG,GAChDoE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAG,CACC6K,WAAY,SAAUtD,CAAM,EACxB,IAAMuE,EAAQvE,EAAOtE,MAAM,CAAC,EAAE,CAC9B,MAAO,CACHE,EAAGmR,AAFoC/M,EAAO0G,MAAM,CAACnC,GAAO8C,gBAAgB,CAEhEzL,CAAC,CAAG,AAAC,CAAA,IAAI,CAAC7C,OAAO,CAAC0K,KAAK,EAAI,CAAA,EAAK,EAC5ChD,EAAGT,EAAOS,CAAC,CAAI,CAAA,IAAI,CAAC1H,OAAO,CAAC2K,MAAM,EAAI,CAAA,CAC1C,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAM+M,EAAW/M,EAAO0G,MAAM,CAAC1G,EAAOtE,MAAM,CAAC,EAAE,EAAE2L,gBAAgB,CACjErH,EAAOyI,cAAc,CAACnJ,EAAEmC,MAAM,CAAGsL,EAASnR,CAAC,CAAE,EAAG,GAChDoE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,EAAE,AACV,CACJ,GACAusB,GAAuB1mB,KAAK,CAACusB,UAAU,CAAGhB,GAmB1C,GAAM,CAAE5mB,MAAO6nB,EAAe,CAAE,CAAI9yB,IASpC,SAAS+yB,GAAqBC,CAAgB,CAAEC,CAAY,EACxD,OAAO,WACH,IAAMvyB,EAAa,IAAI,CAACA,UAAU,CAClC,GAAI,CAACA,EAAWwyB,iBAAiB,EAAI,CAACxyB,EAAWyyB,eAAe,CAC5D,MAAO,EAAE,CAEb,IAAMC,EAAU,IAAI,CAAC1kB,MAAM,CAAChO,EAAWwyB,iBAAiB,CAACF,EAAiB,EAAE3jB,gBAAgB,CAAEgkB,EAAW,IAAI,CAAC3kB,MAAM,CAAChO,EAAWyyB,eAAe,CAACH,EAAiB,EAAE3jB,gBAAgB,CAAErQ,EAAI,CACrL,CAAC,IAAKsF,KAAKoV,KAAK,CAAC0Z,EAAQxvB,CAAC,EAAGU,KAAKoV,KAAK,CAAC0Z,EAAQ3qB,CAAC,EAAE,CACnD,CAAC,IAAKnE,KAAKoV,KAAK,CAAC2Z,EAASzvB,CAAC,EAAGU,KAAKoV,KAAK,CAAC2Z,EAAS5qB,CAAC,EAAE,CACxD,CACD,GAAIwqB,EAAc,CACd,IAAMK,EAAc,IAAI,CAAC5kB,MAAM,CAAChO,EAAWyyB,eAAe,CAACH,EAAmB,EAAE,EAAE3jB,gBAAgB,CAC5FkkB,EAAa,IAAI,CAAC7kB,MAAM,CAAChO,EAAWwyB,iBAAiB,CAACF,EAAmB,EAAE,EAAE3jB,gBAAgB,CACnGrQ,EAAE8B,IAAI,CAAC,CAAC,IAAKwD,KAAKoV,KAAK,CAAC4Z,EAAY1vB,CAAC,EAAGU,KAAKoV,KAAK,CAAC4Z,EAAY7qB,CAAC,EAAE,CAAE,CAAC,IAAKnE,KAAKoV,KAAK,CAAC6Z,EAAW3vB,CAAC,EAAGU,KAAKoV,KAAK,CAAC6Z,EAAW9qB,CAAC,EAAE,CACjI,CACA,OAAOzJ,CACX,CACJ,CAMA,MAAMw0B,WAAkB3D,GAMpBngB,YAAa,CACT,KAAK,CAACA,aACN,IAAI,CAAC+jB,sBAAsB,EAE/B,CACAA,wBAAyB,CACrB,IAAM/vB,EAAS,IAAI,CAACA,MAAM,CAAEgwB,EAAYhwB,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAEkrB,EAAUjwB,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAEupB,EAAStuB,CAAM,CAAC,EAAE,CAACE,CAAC,CAAEgwB,EAAOlwB,CAAM,CAAC,EAAE,CAACE,CAAC,CAChJ4vB,GAAUK,MAAM,CAAChyB,OAAO,CAAC,CAACiyB,EAAO/xB,KAC7B,IAAMgyB,EAAmBrwB,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGirB,EAAYI,EAAOE,EAAiBtwB,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGkrB,EAAUG,EAAO9vB,EAAQ,IAAI,CAACpD,OAAO,CAACstB,WAAW,CAAC+F,QAAQ,CAC9IT,GAAUK,MAAM,CAAC9wB,MAAM,CAAGhB,EAAI,EAAKA,CACxC,CAAA,IAAI,CAACmxB,iBAAiB,CAAG,IAAI,CAACA,iBAAiB,EAAI,EAAE,CACrD,IAAI,CAACC,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,EAAE,CACjD,IAAI,CAACe,oBAAoB,CAAClwB,EAAOguB,EAAQ+B,EAAkB,IAAI,CAACb,iBAAiB,EACjF,IAAI,CAACgB,oBAAoB,CAAClwB,EAAO4vB,EAAMI,EAAgB,IAAI,CAACb,eAAe,CAC/E,EACJ,CACAe,qBAAqBlf,CAAU,CAAEpR,CAAC,CAAE6E,CAAC,CAAE0rB,CAAY,CAAE,CACjD,IAAM5nB,EAAQ4nB,CAAY,CAACnf,EAAW,CAAEkZ,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CACzE3hB,GASDA,EAAM3L,OAAO,CAACgD,CAAC,CAAGA,EAClB2I,EAAM3L,OAAO,CAAC6H,CAAC,CAAGA,EAClB8D,EAAMiB,OAAO,IAVb2mB,CAAY,CAACnf,EAAW,CAAG,IA52JqB3I,EA42JK,IAAI,CAAClL,KAAK,CAAE,IAAI,CAAE,CACnEyC,EAAGA,EACH6E,EAAGA,EACH1E,MAAOmqB,EAAYnqB,KAAK,CACxByI,MAAO0hB,EAAY1hB,KAAK,AAC5B,EAOR,CACAke,WAAY,CACR8I,GAAUK,MAAM,CAAChyB,OAAO,CAAC,SAAUuyB,CAAM,CAAEryB,CAAC,EACxC,GAAM,CAAEsyB,iBAAAA,CAAgB,CAAEC,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAE,CAAG,IAAI,CAAC3zB,OAAO,CAACstB,WAAW,CAC5E,IAAI,CAACvD,SAAS,CAAC,CACXpkB,KAAM,OACNvH,EAAG+zB,GAAqBhxB,GACxBkS,OAAQsgB,CAAU,CAACxyB,EAAE,EAAIuyB,EACzB5iB,UAAW,2BACf,EAAG3P,GACCA,EAAI,GACJ,IAAI,CAAC4oB,SAAS,CAAC,CACXpkB,KAAM,OACN4N,KAAMkgB,CAAgB,CAACtyB,EAAI,EAAE,CAC7BmT,YAAa,EACblW,EAAG+zB,GAAqBhxB,EAAG,CAAA,GAC3B2P,UAAW,mCAAsC3P,CAAAA,EAAI,CAAA,CACzD,EAER,EAAG,IAAI,CACX,CACAwoB,WAAY,CACRiJ,GAAUK,MAAM,CAAChyB,OAAO,CAAC,SAAUiyB,CAAK,CAAE/xB,CAAC,EACvC,IAAMnB,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAE3qB,EAAQ,IAAI,CAACknB,SAAS,CAACqI,GAAgBlyB,EAAQ0C,MAAM,CAACvB,EAAE,CAAE,CAChGwK,MAAO,SAAUvE,CAAM,EAEnB,OADcwI,AAj5J0BnE,EAi5JJc,cAAc,CAACnF,EAAOtH,UAAU,CAACwyB,iBAAiB,CAACnxB,EAAE,CAE7F,EACAyB,KAAMswB,EAAMU,QAAQ,EACxB,GACA5zB,CAAAA,EAAQ0C,MAAM,CAACvB,EAAE,CAAGwB,EAAM3C,OAAO,AACrC,EAAG,IAAI,CACX,CACJ,CAMA4yB,GAAUK,MAAM,CAAG,CAAC,EAAG,KAAO,KAAO,GAAK,KAAO,KAAO,EAAE,CAC1DL,GAAU5zB,SAAS,CAACoqB,cAAc,CAAG8I,GAAgBjD,GAAajwB,SAAS,CAACoqB,cAAc,CAW1F,CACIkE,YAAa,CAWT+F,SAAU,CAAA,EAIVvoB,OAAQ,EAeR2oB,iBAAkB,CACd,2BACA,2BACA,2BACA,2BACA,2BACA,2BACH,CAIDC,UAAW,UAIXC,WAAY,EAAE,CAQdjxB,OAAQ,EAAE,AACd,EACAF,aAAc,CACVupB,aAAc,CAAA,EACdhT,MAAO,QACPa,gBAAiB,OACjBoB,YAAa,EACbF,KAAM,CAAA,EACND,SAAU,OACVjR,MAAO,OACPmB,MAAO,CACHuI,MAAO,MACX,EACA0F,cAAe,SACfnR,EAAG,CACP,CACJ,GACAukB,GAAuB1mB,KAAK,CAACmuB,SAAS,CAAGjB,GAuBzC,GAAM,CAAEvoB,MAAOypB,EAAwB,CAAE,CAAI10B,IAkC7C,SAASgwB,GAAUC,CAAU,CAAEC,CAAQ,CAAEyE,CAAc,EACnD,OAAO,SAAU3sB,CAAM,EACnB,IAAM7G,EAAQ6G,EAAOtH,UAAU,CAACS,KAAK,CAAEyzB,EAAgBzzB,EAAMwI,QAAQ,CAAGxI,EAAMgJ,OAAO,CAAGhJ,EAAM+I,QAAQ,CAClGxG,EAASsE,EAAOtH,UAAU,CAACgD,MAAM,CAC/BK,EAAQL,CAAM,CAAC,EAAE,CAACI,MAAM,CAACC,KAAK,CAEpC+sB,EAASptB,EAAOX,MAAM,CAAG,EACrBW,CAAM,CAAC,EAAE,CAACiJ,KAAK,CAAGjJ,CAAM,CAAC,EAAE,CAACiJ,KAAK,CAAG,EAExC/I,EAAIG,EAAM+J,OAAO,CAACpK,CAAM,CAAC,EAAE,CAACiJ,KAAK,CAAGioB,EAAgBD,EAAiB7D,GAgBrE,OAdAptB,EAAS,CACL,IAvjKgD2I,EAujKtBlL,EAAOuC,CAAM,CAAC,EAAE,CAACsE,MAAM,CAAE,CAC/CpE,EAAGA,EACH6E,EAAG,EACH1E,MAAOL,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAACmD,KAAK,CAC9ByI,MAAO9I,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAAC4L,KAAK,AAClC,GACA,IA7jKgDH,EA6jKtBlL,EAAOuC,CAAM,CAAC,EAAE,CAACsE,MAAM,CAAE,CAC/CpE,EAAGA,EACH6E,EAAG,EACH1E,MAAOL,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAACmD,KAAK,CAC9ByI,MAAO9I,CAAM,CAAC,EAAE,CAAC9C,OAAO,CAAC4L,KAAK,AAClC,GACH,CACMilB,GAAmBtB,aAAa,CAACzsB,CAAM,CAACusB,EAAW,CAAEvsB,CAAM,CAACwsB,EAAS,CAChF,CACJ,CAMA,MAAM2E,WAA2BvG,GAM7B5D,WAAY,CAER,IAAIoK,EAAO,EAAGC,EAAW,EACzB,IAAK,IAAIhzB,EAAI,EAAGA,EAFM,GAEaA,IAAK,CAGpC,IAAMizB,EAAgB,AAACjzB,EAAQ+yB,EAAJ,EAAUpxB,EAAS,CAC1CssB,GAAU,EAAG,EAAGgF,GAChBhF,GAAU,EAAG,EAAGgF,GACnB,CAGDF,EAAOC,AADPA,CAAAA,EAAWD,EAAOC,CAAO,EACPD,EAER,IAAN/yB,GACA,CAAA,IAAI,CAACkzB,oBAAoB,CAAG,CAACvxB,CAAM,CAAC,EAAE,CAAEA,CAAM,CAAC,EAAE,CAAC,AAAD,EAErD,IAAI,CAACinB,SAAS,CAAC+J,GAAyB,IAAI,CAAC9zB,OAAO,CAACstB,WAAW,CAACE,IAAI,CAAE,CACnE7nB,KAAM,OACN7C,OAAQA,EACRgO,UAAW,sCACf,GAAI3P,EAER,CACJ,CACA8M,kBAAmB,CACf,IAAMjO,EAAU,IAAI,CAACA,OAAO,CAAEstB,EAActtB,EAAQstB,WAAW,CAAE3e,EAAe,IAAIpE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAEuzB,GAAyB9zB,EAAQoO,mBAAmB,CAAEkf,EAAYlf,mBAAmB,EAAG,GACvN,IAAI,CAACF,aAAa,CAAChO,IAAI,CAACyO,GACxB2e,EAAYlf,mBAAmB,CAAGO,EAAa3O,OAAO,AAC1D,CACJ,CACAi0B,GAAmBj1B,SAAS,CAACoqB,cAAc,CAAG0K,GAAyBpG,GAAkB1uB,SAAS,CAACoqB,cAAc,CAYjH,CACIkE,YAAa,CAQTE,KAAM,CASFna,OAAQ,sBASRiB,YAAa,EAEbf,KAAM,KAAK,CACf,EACAnF,oBAAqB,CACjB1D,WAAY,WAER,IAAMtD,EAAS,IAAI,CAACA,MAAM,CAAEjH,EAAU,IAAI,CAACA,OAAO,CAAEm0B,EAAaltB,EAAOitB,oBAAoB,CAAEE,EAAO,CAAEz0B,WAAYsH,CAAO,EAAGotB,EAAkBF,CAAU,CAAC,EAAE,CAACC,GAAM1sB,CAAC,CAAE4sB,EAAmBH,CAAU,CAAC,EAAE,CAACC,GAAM1sB,CAAC,CAAEyB,EAAW,IAAI,CAAC/I,KAAK,CAAC+I,QAAQ,CAAEC,EAAU,IAAI,CAAChJ,KAAK,CAACgJ,OAAO,CACxQvG,EAAIsxB,CAAU,CAAC,EAAE,CAACC,GAAMvxB,CAAC,CAAE6E,EAAI,AAAC2sB,CAAAA,EAAkBC,CAAe,EAAK,EAI1E,OAHI,IAAI,CAACl0B,KAAK,CAACwI,QAAQ,EACnB,CAAA,CAAC/F,EAAG6E,EAAE,CAAG,CAACA,EAAG7E,EAAE,AAAD,EAEX,CACHA,EAAGsG,EAAWtG,EAAI,AAAC7C,CAAAA,EAAQ0K,KAAK,EAAI,CAAA,EAAK,EACzChD,EAAG0B,EAAU1B,EAAI,AAAC1H,CAAAA,EAAQ2K,MAAM,EAAI,CAAA,EAAK,CAC7C,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EAIrB,GAHqBA,EAAO7G,KAAK,CAAC8I,YAAY,CAAC3C,EAAEmC,MAAM,CAAGzB,EAAO7G,KAAK,CAAC+I,QAAQ,CAAE5C,EAAEiC,MAAM,CAAGvB,EAAO7G,KAAK,CAACgJ,OAAO,CAAE,CAC9GC,gBAAiB,CAAA,CACrB,GACkB,CACd,IAAMC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAChDU,EAAOyI,cAAc,CAACpG,EAAYzG,CAAC,CAAE,EAAG,GACxCoE,EAAOvH,MAAM,CAAC,CAAA,EAClB,CACJ,CACJ,CACJ,CACJ,CACJ,GACAusB,GAAuB1mB,KAAK,CAACgvB,kBAAkB,CAAGT,GAmBlD,GAAM,CAAE5pB,MAAOsqB,EAAe,CAAE,CAAIv1B,GAMpC,OAAMw1B,WAAkB/D,GAMpB,OAAOgE,mBAAmBC,CAAe,CAAE,CACvC,OAAO,SAAU1tB,CAAM,EACnB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAChE,OAAO8xB,GAAUrF,aAAa,CAACzsB,CAAM,CAACgyB,EAAgB,CAAEhyB,CAAM,CAAC,EAAE,CAAE,IA1tKnB2I,EA0tK6C3L,EAAWS,KAAK,CAAE6G,EAAQtH,EAAWi1B,eAAe,IACrJ,CACJ,CACA,OAAOxF,cAAc5jB,CAAK,CAAEqpB,CAAe,CAAEC,CAAgB,CAAE,CAC3D,IAAMje,EAAQtT,KAAKsF,KAAK,CAAEisB,EAAiBjpB,KAAK,CAC5CgpB,EAAgBhpB,KAAK,CAAGipB,EAAiBlpB,KAAK,CAAGipB,EAAgBjpB,KAAK,EAC1E,MAAO,CACH/I,EAAG2I,EAAMI,KAAK,CAAGyO,AAFmE,IAExD9W,KAAK2J,GAAG,CAAC2J,GACrCnP,EAAG8D,EAAMK,KAAK,CAAGwO,AAHmE,IAGxD9W,KAAK4J,GAAG,CAAC0J,EACzC,CACJ,CACA,OAAOke,oBAAoB9tB,CAAM,CAAE,CAC/B,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAChE,OAAO+tB,GAAmBtB,aAAa,CAACzsB,CAAM,CAAC,EAAE,CAAE,IAvuKC2I,EAuuKyB3L,EAAWS,KAAK,CAAE6G,EAAQtH,EAAWi1B,eAAe,IACrI,CAMAA,iBAAkB,CACd,IAAMjyB,EAAS,IAAI,CAACA,MAAM,CAC1B,MAAO,CACHE,EAAG,AAACF,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGF,CAAM,CAAC,EAAE,CAACE,CAAC,AAADA,EAAK,EACjC6E,EAAG,AAAC/E,CAAAA,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,AAADA,EAAK,EACjC1E,MAAOL,CAAM,CAAC,EAAE,CAACI,MAAM,CAACC,KAAK,CAC7ByI,MAAO9I,CAAM,CAAC,EAAE,CAACI,MAAM,CAAC0I,KAAK,AACjC,CACJ,CACAke,WAAY,CACR,IAAI,CAACqL,QAAQ,GACb,IAAI,CAACC,cAAc,EACvB,CACAD,UAAW,CACP,IAAMrkB,EAAY,6BAClB,IAAI,CAACiZ,SAAS,CAAC,CACXpkB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd8xB,GAAUM,mBAAmB,CAChC,CACDpkB,UAAAA,CACJ,EAAG,GACH,IAAI,CAACiZ,SAAS,CAAC,CACXpkB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd8xB,GAAUS,gBAAgB,CAC7B,CACDvkB,UAAAA,CACJ,EAAG,GACH,IAAI,CAACiZ,SAAS,CAAC,CACXpkB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd8xB,GAAUU,mBAAmB,CAChC,CACDxkB,UAAAA,CACJ,EAAG,EACP,CACAskB,gBAAiB,CACb,IAAMzrB,EAAS,IAAI,CAACA,MAAM,CAAE2jB,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAC5DiI,EAAkB,IAAI,CAACxL,SAAS,CAAC4K,GAAgBrH,EAAYiI,eAAe,CAAE,CAChF5vB,KAAM,OACN7C,OAAQ,CACJ,SAAUsE,CAAM,EACZ,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAAEiyB,EAAkBj1B,EAAWi1B,eAAe,GAC9G,MAAO,CACH/xB,EAAG,AAACF,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAG+xB,EAAgB/xB,CAAC,AAADA,EAAK,EACvC6E,EAAG,AAAC/E,CAAAA,CAAM,CAAC,EAAE,CAAC+E,CAAC,CAAGktB,EAAgBltB,CAAC,AAADA,EAAK,EACvC1E,MAAO4xB,EAAgB5xB,KAAK,CAC5ByI,MAAOmpB,EAAgBnpB,KAAK,AAChC,CACJ,EACAjC,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB6G,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB,SAAUsE,CAAM,EACZ,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEgD,EAAShD,EAAWgD,MAAM,CAAEiyB,EAAkBj1B,EAAWi1B,eAAe,GAC9G,MAAO,CACH/xB,EAAG,AAAC+xB,CAAAA,EAAgB/xB,CAAC,CAAGF,CAAM,CAAC,EAAE,CAACE,CAAC,AAADA,EAAK,EACvC6E,EAAG,AAACktB,CAAAA,EAAgBltB,CAAC,CAAG/E,CAAM,CAAC,EAAE,CAAC+E,CAAC,AAADA,EAAK,EACvC1E,MAAO4xB,EAAgB5xB,KAAK,CAC5ByI,MAAOmpB,EAAgBnpB,KAAK,AAChC,CACJ,EACH,CACDkF,UAAW,uCACf,GAAI,GACE0kB,EAAkB,IAAI,CAACzL,SAAS,CAAC4K,GAAgBrH,EAAYkI,eAAe,CAAE,CAChF7vB,KAAM,OACN7C,OAAQ,CACJ,IAAI,CAACA,MAAM,CAAC,EAAE,CACd6G,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB6G,CAAM,CAAC,EAAE,CAAC7G,MAAM,CAAC,EAAE,CACnB,IAAI,CAACA,MAAM,CAAC,EAAE,CACjB,CACDgO,UAAW,uCACf,GAAI,EACJwc,CAAAA,EAAYiI,eAAe,CAAGA,EAAgBv1B,OAAO,CACrDstB,EAAYkI,eAAe,CAAGA,EAAgBx1B,OAAO,AACzD,CACJ,CAMA40B,GAAUS,gBAAgB,CAAGT,GAAUC,kBAAkB,CAAC,GAC1DD,GAAUU,mBAAmB,CAAGV,GAAUC,kBAAkB,CAAC,GAC7DD,GAAU51B,SAAS,CAACoqB,cAAc,CAAGuL,GAAgB9D,GAAmB7xB,SAAS,CAACoqB,cAAc,CAWhG,CACIkE,YAAa,CAOTiI,gBAAiB,CACbhiB,KAAM,2BACNe,YAAa,CACjB,EAOAkhB,gBAAiB,CACbjiB,KAAM,2BACNe,YAAa,CACjB,CACJ,CACJ,GACA8X,GAAuB1mB,KAAK,CAAC+vB,SAAS,CAAGb,GAkBzC,GAAM,CAAEvqB,MAAOqrB,EAAkB,CAAEj2B,KAAMk2B,EAAiB,CAAE,CAAIv2B,GAMhE,OAAMw2B,WAAqBxJ,GAMvB,OAAOyJ,oBAAoBzuB,CAAM,CAAE,CAC/B,IAAMtH,EAAasH,EAAOtH,UAAU,CAAES,EAAQT,EAAWS,KAAK,CAAEwI,EAAWxI,EAAMwI,QAAQ,CAAE4C,EAAQ7L,EAAWgD,MAAM,CAAC,EAAE,CAAEqnB,EAAOwL,GAAkBhqB,EAAMzI,MAAM,CAAC0I,KAAK,EAAID,EAAMzI,MAAM,CAAC0I,KAAK,CAACue,IAAI,CAAE,GAAIvD,EAAM+O,GAAkBhqB,EAAMzI,MAAM,CAAC0I,KAAK,EAAID,EAAMzI,MAAM,CAAC0I,KAAK,CAACgb,GAAG,CAAE,GAAIkP,EAASh2B,EAAWE,OAAO,CAACstB,WAAW,CAAC3qB,KAAK,CAACmzB,MAAM,CAAEjuB,EAAI+H,AAz4KrRnE,EAy4K2SI,aAAa,CAACF,EAAO,CAAA,EAAK,CAAC5C,EAAW,IAAM,IAAI,CAC/Y,MAAO,CACH/F,EAAG2I,EAAM3I,CAAC,CACVG,MAAOwI,EAAMzI,MAAM,CAACC,KAAK,CACzB0E,EAAGA,EAAIiuB,EACF/sB,CAAAA,EAAYohB,EAAO5pB,EAAM+I,QAAQ,CAAKsd,EAAMrmB,EAAMgJ,OAAO,CAClE,CACJ,CACA,OAAOwsB,qBAAqB3uB,CAAM,CAAE,CAChC,IAAMtH,EAAasH,EAAOtH,UAAU,CAAES,EAAQT,EAAWS,KAAK,CAAEwI,EAAWxI,EAAMwI,QAAQ,CAAEukB,EAAcxtB,EAAWE,OAAO,CAACstB,WAAW,CAAE3hB,EAAQ7L,EAAWgD,MAAM,CAAC,EAAE,CAAEqnB,EAAOwL,GAAkBhqB,EAAMzI,MAAM,CAAC0I,KAAK,EAAID,EAAMzI,MAAM,CAAC0I,KAAK,CAACue,IAAI,CAAE,GAAIvD,EAAM+O,GAAkBhqB,EAAMzI,MAAM,CAAC0I,KAAK,EAAID,EAAMzI,MAAM,CAAC0I,KAAK,CAACgb,GAAG,CAAE,GAAI/e,EAAI+H,AAl5K7QnE,EAk5KmSI,aAAa,CAACF,EAAO,CAAA,EAAK,CAAC5C,EAAW,IAAM,IAAI,CACnY0P,EAAU6U,EAAY7U,OAAO,CAIjC,OAHI6U,EAAY3qB,KAAK,CAACmzB,MAAM,CAAG,GAC3Brd,CAAAA,GAAW,EAAC,EAET,CACHzV,EAAG2I,EAAM3I,CAAC,CACVG,MAAOwI,EAAMzI,MAAM,CAACC,KAAK,CACzB0E,EAAGA,EAAI4Q,EACF1P,CAAAA,EAAYohB,EAAO5pB,EAAM+I,QAAQ,CAAKsd,EAAMrmB,EAAMgJ,OAAO,CAClE,CACJ,CAMAqF,kBAAmB,CACf,MAAO,CAAC,IAAI,CAAC5O,OAAO,CAACstB,WAAW,CAAC3hB,KAAK,CAAC,AAC3C,CACAme,WAAY,CACR,IAAMwD,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAAEpU,EAAY,IAAI,CAAC6Q,SAAS,CAAC2L,GAAmBpI,EAAYpU,SAAS,CAAE,CAC/GvT,KAAM,OACN7C,OAAQ,CACJ8yB,GAAaC,mBAAmB,CAChCD,GAAaG,oBAAoB,CACpC,CACDjlB,UAAW,0BACf,GAAI,EACJwc,CAAAA,EAAYpU,SAAS,CAAGA,EAAUlZ,OAAO,CACzC,IAAI,CAACJ,WAAW,CAAC0tB,WAAW,CAAC3hB,KAAK,CAAG2hB,EAAY3hB,KAAK,AAC1D,CACAge,WAAY,CACR,IAAM2D,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAAE9qB,EAAe8qB,EAAY3qB,KAAK,CAC1EK,EAAI,EAAG6E,EAAIrF,EAAaszB,MAAM,CAAE9c,EAAgBxW,EAAaszB,MAAM,CAAG,EAAI,SAAW,MAAO/c,EAAQ,QACpG,CAAA,IAAI,CAACxY,KAAK,CAACwI,QAAQ,GACnB/F,EAAIR,EAAaszB,MAAM,CACvBjuB,EAAI,EACJmR,EAAgB,SAChBD,EAAQvW,EAAaszB,MAAM,CAAG,EAAI,QAAU,QAQhDxI,EAAY3qB,KAAK,CAAGA,AANN,IAAI,CAACknB,SAAS,CAAC6L,GAAmBlzB,EAAc,CAC1DwW,cAAeA,EACfD,MAAOA,EACP/V,EAAGA,EACH6E,EAAGA,CACP,IAC0B7H,OAAO,AACrC,CACJ,CACA41B,GAAa52B,SAAS,CAACoqB,cAAc,CAAGsM,GAAmBtJ,GAAuBptB,SAAS,CAACoqB,cAAc,CAY1G,CACIkE,YAAa,CAIT7U,QAAS,GAMT9V,MAAO,CACHmzB,OAAQ,IACRnqB,MAAO,SAAUvE,CAAM,EACnB,OAAOA,EAAOtH,UAAU,CAACgD,MAAM,CAAC,EAAE,AACtC,EACAipB,aAAc,CAAA,EACdnS,gBAAiB,OACjBoB,YAAa,EACbF,KAAM,CAAA,EACND,SAAU,OACVjR,MAAO,OACPhH,KAAM,SACV,EAOAsW,UAAW,CACP5E,YAAa,EACb0hB,UAAW,OACf,CACJ,CACJ,GACA5J,GAAuB1mB,KAAK,CAACuwB,YAAY,CAAGL,GAkB5C,GAAM,CAAE/vB,QAASqwB,EAAe,CAAE3jB,OAAQ4jB,EAAc,CAAEje,SAAUke,EAAgB,CAAE/rB,MAAOgsB,EAAa,CAAE52B,KAAM62B,EAAY,CAAE,CAAIl3B,IAUpI,SAASm3B,KACL,IAAIA,EAAU,EAAGC,EAAc,EAAGC,EAAe,EAC3CvzB,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEwzB,EAAMC,GAAY,IAAI,CAACvG,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAgB9G,OAfArtB,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACVirB,GAAsBjrB,EAAO+qB,IAC7BN,GAAiBzqB,EAAM9D,CAAC,IACxB2uB,GAAe7qB,EAAM9D,CAAC,CACtB4uB,IAER,EAER,GACIA,EAAe,GACfF,CAAAA,EAAUC,EAAcC,CAAW,EAEhCF,CACX,CAIA,SAASK,GAAsBjrB,CAAK,CAAE+qB,CAAG,EACrC,MAAQ,CAAC/qB,EAAMkrB,MAAM,EACjBT,GAAiBzqB,EAAM9D,CAAC,GACxB8D,EAAM3I,CAAC,CAAG0zB,EAAItG,QAAQ,EACtBzkB,EAAM3I,CAAC,EAAI0zB,EAAIrG,QAAQ,EACvB1kB,EAAM9D,CAAC,CAAG6uB,EAAIpG,QAAQ,EACtB3kB,EAAM9D,CAAC,EAAI6uB,EAAInG,QAAQ,AAC/B,CAIA,SAASuG,KACL,IAAM5zB,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEwzB,EAAMC,GAAY,IAAI,CAACvG,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAC1GuG,EAAO,EAWX,OAVA5zB,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACVirB,GAAsBjrB,EAAO+qB,IAC7BI,GAER,EAER,GACOA,CACX,CAKA,SAASC,KACL,MAAO,QAAU,IAAI,CAACC,GAAG,CACrB,YAAc,IAAI,CAACrzB,GAAG,CACtB,gBAAkB,IAAI,CAAC4yB,OAAO,CAACU,OAAO,CAAC,GACvC,aAAe,IAAI,CAACH,IAAI,AAChC,CAMA,SAASH,GAAYvG,CAAQ,CAAEC,CAAQ,CAAEC,CAAQ,CAAEC,CAAQ,EACvD,MAAO,CACHH,SAAU1sB,KAAKszB,GAAG,CAAC3G,EAAUD,GAC7BC,SAAU3sB,KAAKC,GAAG,CAAC0sB,EAAUD,GAC7BE,SAAU5sB,KAAKszB,GAAG,CAACzG,EAAUD,GAC7BC,SAAU7sB,KAAKC,GAAG,CAAC4sB,EAAUD,EACjC,CACJ,CAYA,SAAS4G,GAAYC,CAAI,CAAEnkB,CAAK,CAAE8iB,CAAM,EACpC,OAAOqB,EAAKjqB,OAAO,CAACiqB,EAAKpqB,QAAQ,CAACiG,GAAS8iB,EAC/C,CAKA,SAASsB,KACL,IAAMp3B,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAE/sB,EAAQ,IAAI,CAACA,KAAK,CAAEwI,EAAWxI,EAAMwI,QAAQ,CAAE5F,EAAQ5C,EAAM4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEyI,EAAQrL,EAAMqL,KAAK,CAAC5L,EAAQ4L,KAAK,CAAC,CAAEyrB,EAAKr3B,EAAQsuB,UAAU,CAAEzjB,EAAQ9B,EAAWsuB,EAAGvsB,MAAM,CAAGusB,EAAGxsB,KAAK,CAAEC,EAAS/B,EAAWsuB,EAAGxsB,KAAK,CAAGwsB,EAAGvsB,MAAM,CAAEwsB,EAAat3B,EAAQs3B,UAAU,CAAE1Q,EAAM7d,EAAW5F,EAAMgnB,IAAI,CAAGve,EAAMgb,GAAG,CACrVuD,EAAOphB,EAAW6C,EAAMgb,GAAG,CAAGzjB,EAAMgnB,IAAI,AACxC,CAAA,IAAI,CAACoN,SAAS,CAAGv3B,EAAQ2L,KAAK,CAAC3I,CAAC,CAChC,IAAI,CAACw0B,SAAS,CAAGx3B,EAAQ2L,KAAK,CAAC9D,CAAC,CAC5BuuB,GAAiBvrB,GACjB,IAAI,CAAC4sB,SAAS,CAAG,IAAI,CAACF,SAAS,CAAG1sB,EAGlC,IAAI,CAAC4sB,SAAS,CAAGP,GAAY/zB,EAAO,IAAI,CAACo0B,SAAS,CAAEG,WAAW7sB,IAE/DurB,GAAiBtrB,GACjB,IAAI,CAAC6sB,SAAS,CAAG,IAAI,CAACH,SAAS,CAAG1sB,EAGlC,IAAI,CAAC6sB,SAAS,CAAGT,GAAYtrB,EAAO,IAAI,CAAC4rB,SAAS,CAAEE,WAAW5sB,IAG/DwsB,AAAe,MAAfA,GACA,IAAI,CAACE,SAAS,CAAG5rB,EAAMsB,OAAO,CAAC0Z,GAC/B,IAAI,CAAC+Q,SAAS,CAAG/rB,EAAMsB,OAAO,CAAC0Z,EAAMhb,EAAMkB,GAAG,GAE1B,MAAfwqB,IACL,IAAI,CAACC,SAAS,CAAGp0B,EAAM+J,OAAO,CAACid,GAC/B,IAAI,CAACsN,SAAS,CAAGt0B,EAAM+J,OAAO,CAACid,EAAOhnB,EAAM2J,GAAG,EAEvD,CAIA,SAASnJ,KACL,IAAMT,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEwzB,EAAMC,GAAY,IAAI,CAACvG,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAC1G5sB,EAAM,CAACi0B,IAAUC,EAAe,CAAA,EAiBpC,OAhBA30B,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACVyqB,GAAiBzqB,EAAM9D,CAAC,GACxB8D,EAAM9D,CAAC,CAAGlE,GACVizB,GAAsBjrB,EAAO+qB,KAC7B/yB,EAAMgI,EAAM9D,CAAC,CACbgwB,EAAe,CAAA,EAEvB,EAER,GACKA,GACDl0B,CAAAA,EAAM,CAAA,EAEHA,CACX,CAKA,SAASqzB,KACL,IAAM9zB,EAAS,IAAI,CAAC3C,KAAK,CAAC2C,MAAM,CAAEwzB,EAAMC,GAAY,IAAI,CAACvG,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,CAAE,IAAI,CAACC,QAAQ,EAC1GyG,EAAMY,IAAUC,EAAe,CAAA,EAiBnC,OAhBA30B,EAAOjC,OAAO,CAAC,AAAC6C,IACRA,EAAEmH,OAAO,EACTnH,AAAiB,gCAAjBA,EAAE9D,OAAO,CAACuE,EAAE,EACZT,EAAEhB,MAAM,CAAC7B,OAAO,CAAC,AAAC0K,IACVyqB,GAAiBzqB,EAAM9D,CAAC,GACxB8D,EAAM9D,CAAC,CAAGmvB,GACVJ,GAAsBjrB,EAAO+qB,KAC7BM,EAAMrrB,EAAM9D,CAAC,CACbgwB,EAAe,CAAA,EAEvB,EAER,GACKA,GACDb,CAAAA,EAAM,CAAA,EAEHA,CACX,CAQA,SAASc,GAAYC,CAAM,EACvB,IAAM/3B,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAEnqB,EAAQ,IAAI,CAAC5C,KAAK,CAAC4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEyI,EAAQ,IAAI,CAACrL,KAAK,CAACqL,KAAK,CAAC5L,EAAQ4L,KAAK,CAAC,CAAEosB,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAU,IAAI,CAACA,OAAO,AAC1K,CAAA,IAAI,CAAC7H,QAAQ,CAAG8G,GAAY/zB,EAAO,IAAI,CAACo0B,SAAS,CAAES,GACnD,IAAI,CAAC3H,QAAQ,CAAG6G,GAAY/zB,EAAO,IAAI,CAACs0B,SAAS,CAAEO,GACnD,IAAI,CAAC1H,QAAQ,CAAG4G,GAAYtrB,EAAO,IAAI,CAAC4rB,SAAS,CAAES,GACnD,IAAI,CAAC1H,QAAQ,CAAG2G,GAAYtrB,EAAO,IAAI,CAAC+rB,SAAS,CAAEM,GACnD,IAAI,CAACjB,GAAG,CAAGA,GAAI93B,IAAI,CAAC,IAAI,EACxB,IAAI,CAACyE,GAAG,CAAGA,GAAIzE,IAAI,CAAC,IAAI,EACxB,IAAI,CAACq3B,OAAO,CAAGA,GAAQr3B,IAAI,CAAC,IAAI,EAChC,IAAI,CAAC43B,IAAI,CAAGA,GAAK53B,IAAI,CAAC,IAAI,EACtB64B,GACA,IAAI,CAACA,MAAM,CAAC,EAAG,EAEvB,CAYA,SAASG,GAAkBr4B,CAAM,CAAEk4B,CAAM,CAAEI,CAAO,CAAEvvB,CAAE,CAAEF,CAAE,EACtD,IAAM1I,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAEgK,EAAat3B,EAAQs3B,UAAU,CAAEn0B,EAAQ,IAAI,CAAC5C,KAAK,CAAC4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEyI,EAAQ,IAAI,CAACrL,KAAK,CAACqL,KAAK,CAAC5L,EAAQ4L,KAAK,CAAC,CAAE2rB,EAAY,IAAI,CAACA,SAAS,CAAEE,EAAY,IAAI,CAACA,SAAS,CAAED,EAAY,IAAI,CAACA,SAAS,CAAEG,EAAY,IAAI,CAACA,SAAS,CAAEK,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAU,IAAI,CAACA,OAAO,CACvTF,IACIT,AAAe,MAAfA,EACIa,AAAY,IAAZA,EACA,IAAI,CAACZ,SAAS,CAAGL,GAAY/zB,EAAOo0B,EAAW3uB,GAG/C,IAAI,CAAC6uB,SAAS,CAAGP,GAAY/zB,EAAOs0B,EAAW7uB,GAG9C0uB,AAAe,MAAfA,EACDa,AAAY,IAAZA,EACA,IAAI,CAACX,SAAS,CAAGN,GAAYtrB,EAAO4rB,EAAW9uB,GAG/C,IAAI,CAACivB,SAAS,CAAGT,GAAYtrB,EAAO+rB,EAAWjvB,IAInD,IAAI,CAAC+uB,SAAS,CAAGP,GAAY/zB,EAAOs0B,EAAW7uB,GAC/C,IAAI,CAAC+uB,SAAS,CAAGT,GAAYtrB,EAAO+rB,EAAWjvB,KAGnD7I,IACA,IAAI,CAAC03B,SAAS,CAAGL,GAAY/zB,EAAOo0B,EAAWS,GAC/C,IAAI,CAACP,SAAS,CAAGP,GAAY/zB,EAAOs0B,EAAWO,GAC/C,IAAI,CAACR,SAAS,CAAGN,GAAYtrB,EAAO4rB,EAAWS,GAC/C,IAAI,CAACN,SAAS,CAAGT,GAAYtrB,EAAO+rB,EAAWM,GAC/C,IAAI,CAACD,OAAO,CAAG,EACf,IAAI,CAACC,OAAO,CAAG,GAEnB,IAAI,CAACj4B,OAAO,CAACstB,WAAW,CAAC3hB,KAAK,CAAG,CAC7B3I,EAAG,IAAI,CAACu0B,SAAS,CACjB1vB,EAAG,IAAI,CAAC2vB,SAAS,AACrB,EAGA,IAAI,CAAC53B,WAAW,CAAC0tB,WAAW,CAAC3hB,KAAK,CAAG,CACjC3I,EAAG,IAAI,CAACu0B,SAAS,CACjB1vB,EAAG,IAAI,CAAC2vB,SAAS,AACrB,CACJ,CAMA,MAAMY,WAAgBhM,GAUlB/b,KAAKgoB,CAAiB,CAAEz4B,CAAW,CAAEwD,CAAK,CAAE,CACxC,KAAK,CAACiN,KAAKgoB,EAAmBz4B,EAAawD,GAC3C,IAAI,CAAC40B,OAAO,CAAG,EACf,IAAI,CAACC,OAAO,CAAG,EACf,IAAI,CAACK,OAAO,CAAG,EACf,IAAI,CAACC,OAAO,CAAG,EACfnB,GAAal4B,IAAI,CAAC,IAAI,EACtB,IAAI,CAACs5B,SAAS,GACd,IAAI,CAAC1O,SAAS,EAClB,CAKAP,aAAc,CACV,IAAI,CAACC,SAAS,CAAG,IAAI,CAACjpB,KAAK,CAAC4C,KAAK,CAAC,IAAI,CAACnD,OAAO,CAACstB,WAAW,CAACnqB,KAAK,CAAC,CACjE,IAAI,CAACsmB,SAAS,CAAG,IAAI,CAAClpB,KAAK,CAACqL,KAAK,CAAC,IAAI,CAAC5L,OAAO,CAACstB,WAAW,CAAC1hB,KAAK,CAAC,AACrE,CAKA6sB,oBAAqB,CACjB,IAAMz4B,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAEnqB,EAAQnD,EAAQmD,KAAK,CAAEyI,EAAQ5L,EAAQ4L,KAAK,CACtF,MAAO,CACH,CACI5I,EAAG,IAAI,CAACotB,QAAQ,CAChBvoB,EAAG,IAAI,CAACyoB,QAAQ,CAChBntB,MAAOA,EACPyI,MAAOA,CACX,EACA,CACI5I,EAAG,IAAI,CAACqtB,QAAQ,CAChBxoB,EAAG,IAAI,CAACyoB,QAAQ,CAChBntB,MAAOA,EACPyI,MAAOA,CACX,EACA,CACI5I,EAAG,IAAI,CAACqtB,QAAQ,CAChBxoB,EAAG,IAAI,CAAC0oB,QAAQ,CAChBptB,MAAOA,EACPyI,MAAOA,CACX,EACA,CACI5I,EAAG,IAAI,CAACotB,QAAQ,CAChBvoB,EAAG,IAAI,CAAC0oB,QAAQ,CAChBptB,MAAOA,EACPyI,MAAOA,CACX,EACA,CACIc,QAAS,GACb,EACH,AACL,CACAuB,kBAAmB,CACf,IAAMlF,EAAW,IAAI,CAACxI,KAAK,CAACwI,QAAQ,CAAE/I,EAAU,IAAI,CAACA,OAAO,CAACoO,mBAAmB,CAAEkpB,EAAa,IAAI,CAACt3B,OAAO,CAACstB,WAAW,CAACgK,UAAU,CAC7HpB,GAAgB,IAAI,CAACt2B,WAAW,CAACwO,mBAAmB,EAAErD,OAAOnD,UAC1D0vB,AAAe,MAAfA,EACAt3B,EAAQ+K,KAAK,CAACnD,MAAM,CAAGmB,EAAW,YAAc,YAE5B,MAAfuuB,GACLt3B,CAAAA,EAAQ+K,KAAK,CAACnD,MAAM,CAAGmB,EAAW,YAAc,WAAU,GAGlE,IAAI4F,EAAe,IAAIpE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE,IAAI,CAACP,OAAO,CAACoO,mBAAmB,CAAE,GACpG,IAAI,CAACF,aAAa,CAAChO,IAAI,CAACyO,GAEL,OAAf2oB,IACA3oB,EAAe,IAAIpE,EAAyB,IAAI,CAAChK,KAAK,CAAE,IAAI,CAAE,IAAI,CAACP,OAAO,CAACoO,mBAAmB,CAAE,GAChG,IAAI,CAACF,aAAa,CAAChO,IAAI,CAACyO,GAEhC,CAOA6pB,UAAUT,CAAM,CAAE,CACd,IAAMzK,EAAc,IAAI,CAACttB,OAAO,CAACstB,WAAW,CAAEtT,EAAYsT,EAAY3qB,KAAK,CAACqX,SAAS,CAErF8d,GAAY54B,IAAI,CAAC,IAAI,CAAE64B,GAClBzK,EAAY3qB,KAAK,CAAC+1B,OAAO,GAG1B,IAAI,CAACh2B,MAAM,CAACP,MAAM,CAAG,EACrB,AAAC,IAAI,CAACO,MAAM,CAAC,EAAE,CAAEE,IAAI,CAAI,AAACoX,GAAaA,EAAU9a,IAAI,CAAC,IAAI,GACtD63B,GAAiB73B,IAAI,CAAC,IAAI,EAG9B,IAAI,CAAC2qB,SAAS,CAACsM,GAAe,CAC1BvsB,MAAO,OACPgQ,gBAAiB,OACjBtG,MAAO,QACP0H,YAAa,EACb7F,UAAW,OACX0F,SAAU,QACV9B,MAAO,OACPlR,EAAG,EACH7E,EAAG,EACHgW,cAAe,MACf8B,KAAM,CAAA,EACN3X,MAAO,EACPyI,MAAO,EACPD,MAAO,SAAUvE,CAAM,EACnB,IAAMtH,EAAasH,EAAOtH,UAAU,CAAEE,EAAUoH,EAAOpH,OAAO,CAC9D,MAAO,CACHgD,EAAGlD,EAAWswB,QAAQ,CACtBvoB,EAAG/H,EAAWwwB,QAAQ,CACtBntB,MAAOmzB,GAAahJ,EAAYnqB,KAAK,CAAEnD,EAAQmD,KAAK,EACpDyI,MAAO0qB,GAAahJ,EAAY1hB,KAAK,CAAE5L,EAAQ4L,KAAK,CACxD,CACJ,EACAhJ,KAAO,AAACoX,GAAaA,EAAU9a,IAAI,CAAC,IAAI,GACpC63B,GAAiB73B,IAAI,CAAC,IAAI,CAClC,EAAGouB,EAAY3qB,KAAK,EAAG,KAAK,GAEpC,CAKAmnB,WAAY,CACR,IAAI,CAAC6O,aAAa,GAClB,IAAI,CAACtK,aAAa,EACtB,CAKAA,eAAgB,CACZ,IAAMuK,EAAc,IAAI,CAACH,kBAAkB,EACX,MAAA,IAArBG,CAAW,CAAC,EAAE,CAAC51B,CAAC,EAG3B,IAAI,CAAC+mB,SAAS,CAACoM,GAAe,CAC1BxwB,KAAM,OACN7C,OAAQ81B,EACR9nB,UAAW,+BACf,EAAG,IAAI,CAAC9Q,OAAO,CAACstB,WAAW,CAACgB,UAAU,EAAG,EAC7C,CAKAqK,eAAgB,CACZ,IAAMp4B,EAAQ,IAAI,CAACA,KAAK,CAAEP,EAAU,IAAI,CAACA,OAAO,CAACstB,WAAW,CAAE3hB,EAAQ,IAAI,CAAC3L,OAAO,CAACstB,WAAW,CAAC3hB,KAAK,CAAExI,EAAQ5C,EAAM4C,KAAK,CAACnD,EAAQmD,KAAK,CAAC,CAAEyI,EAAQrL,EAAMqL,KAAK,CAAC5L,EAAQ4L,KAAK,CAAC,CAAE7C,EAAWxI,EAAMwI,QAAQ,CAAEqgB,EAAiB,CACtNzd,MAAOA,EACPhG,KAAM,MACV,EACIyqB,EAAWjtB,EAAM4J,QAAQ,CAAC,IAAI,CAACqjB,QAAQ,EAAGC,EAAWltB,EAAM4J,QAAQ,CAAC,IAAI,CAACsjB,QAAQ,EAAGC,EAAW1kB,EAAMmB,QAAQ,CAAC,IAAI,CAACujB,QAAQ,EAAGC,EAAW3kB,EAAMmB,QAAQ,CAAC,IAAI,CAACwjB,QAAQ,EAAGsI,EAAQ,EAAE,CAAEC,EAAQ,EAAE,CAAEC,EAAmBC,EAAmBlwB,EACtOC,IACAD,EAAOsnB,EACPA,EAAWE,EACXA,EAAWxnB,EACXA,EAAOunB,EACPA,EAAWE,EACXA,EAAWznB,GAGX9I,EAAQi5B,UAAU,CAACP,OAAO,EAC1BG,CAAAA,EAAQ,CAAC,CACD,IACAzI,EACAE,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACvC,CAAE,CACC,IACAD,EACAC,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACvC,CAAC,AAAD,EAGLtwB,EAAQk5B,UAAU,CAACR,OAAO,EAC1BI,CAAAA,EAAQ,CAAC,CACD,IACA1I,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACpCE,EACH,CAAE,CACC,IACAF,EAAY,AAACC,CAAAA,EAAWD,CAAO,EAAK,EACpCG,EACH,CAAC,AAAD,EAGL,IAAI,CAAC5mB,MAAM,CAACxH,MAAM,CAAG,GACrB,IAAI,CAACwH,MAAM,CAAC,EAAE,CAAC3J,OAAO,CAAC5B,CAAC,CAAGy6B,EAC3B,IAAI,CAAClvB,MAAM,CAAC,EAAE,CAAC3J,OAAO,CAAC5B,CAAC,CAAG06B,IAI3BC,EAAoB1C,GAAcjN,EAAgB,CAAEtY,UAAW,gCAAiC,EAAG9Q,EAAQi5B,UAAU,EACrHD,EAAoB3C,GAAcjN,EAAgB,CAAEtY,UAAW,gCAAiC,EAAG9Q,EAAQk5B,UAAU,EACrH,IAAI,CAACnP,SAAS,CAACoM,GAAe,CAAE/3B,EAAGy6B,CAAM,EAAGE,GAAoB,GAChE,IAAI,CAAChP,SAAS,CAACoM,GAAe,CAAE/3B,EAAG06B,CAAM,EAAGE,GAAoB,GAExE,CACAvxB,OAAOf,CAAC,CAAE,CACN,IAAM+C,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAAI4wB,EAAa,IAAI,CAACt3B,OAAO,CAACstB,WAAW,CAACgK,UAAU,CAAEt0B,EAAIs0B,AAAe,MAAfA,EAAqB,EAAI7tB,EAAYzG,CAAC,CAAE6E,EAAIyvB,AAAe,MAAfA,EAAqB,EAAI7tB,EAAY5B,CAAC,CAC5L,IAAI,CAAC6B,SAAS,CAAC1G,EAAG6E,GAClB,IAAI,CAACmwB,OAAO,EAAIh1B,EAChB,IAAI,CAACi1B,OAAO,EAAIpwB,EAEhB,IAAI,CAAChI,MAAM,CAAC,CAAA,EAAO,CAAA,EAAO,CAAA,EAC9B,CAcAk4B,OAAOnvB,CAAE,CAAEF,CAAE,CAAEyvB,CAAO,CAAEb,CAAU,CAAE,CAEhC,IAAM6B,EAAW,IAAI,CAACxvB,MAAM,CAAC,EAAE,AAC3B2tB,AAAe,CAAA,MAAfA,EACIa,AAAY,IAAZA,GACAgB,EAAStpB,cAAc,CAACjH,EAAI,EAAG,GAC/BuwB,EAAStpB,cAAc,CAACjH,EAAIF,EAAI,KAGhCywB,EAAStpB,cAAc,CAACjH,EAAI,EAAG,GAC/BuwB,EAAStpB,cAAc,CAACjH,EAAIF,EAAI,IAG/B4uB,AAAe,MAAfA,EACDa,AAAY,IAAZA,GACAgB,EAAStpB,cAAc,CAAC,EAAGnH,EAAI,GAC/BywB,EAAStpB,cAAc,CAAC,EAAGnH,EAAI,KAG/BywB,EAAStpB,cAAc,CAAC,EAAGnH,EAAI,GAC/BywB,EAAStpB,cAAc,CAAC,EAAGnH,EAAI,KAInCywB,EAAStpB,cAAc,CAACjH,EAAI,EAAG,GAC/BuwB,EAAStpB,cAAc,CAACjH,EAAIF,EAAI,GAChCywB,EAAStpB,cAAc,CAAC,EAAGnH,EAAI,IAEnCwvB,GAAkBh5B,IAAI,CAAC,IAAI,CAAE,CAAA,EAAO,CAAA,EAAMi5B,EAASvvB,EAAIF,GACvD,IAAI,CAAC1I,OAAO,CAACstB,WAAW,CAACgB,UAAU,CAACxjB,MAAM,CAAGpH,KAAK6S,GAAG,CAAC,IAAI,CAACohB,SAAS,CAAG,IAAI,CAACH,SAAS,EACrF,IAAI,CAACx3B,OAAO,CAACstB,WAAW,CAACgB,UAAU,CAACzjB,KAAK,CAAGnH,KAAK6S,GAAG,CAAC,IAAI,CAACkhB,SAAS,CAAG,IAAI,CAACF,SAAS,CACxF,CAUA13B,OAAO4K,CAAS,CAAEstB,CAAM,CAAEqB,CAAc,CAAE,CACtC,IAAI,CAACtqB,UAAU,GACV,IAAI,CAAC3O,OAAO,EACb,IAAI,CAACwK,MAAM,GAEXyuB,GACAlB,GAAkBh5B,IAAI,CAAC,IAAI,CAAE,CAAA,EAAM,CAAA,GAGnC,IAAI,CAACwB,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,OAAO,CAAC,IAAI,CAACwlB,UAAU,IAEzC,IAAI,CAAC8O,SAAS,CAACT,GACf,IAAI,CAACY,aAAa,GAClB,IAAI,CAAChO,WAAW,CAAC,IAAI,CAAChhB,MAAM,CAAEc,GAC9B,IAAI,CAACkgB,WAAW,CAAC,IAAI,CAACjoB,MAAM,CAAE+H,GAC9B,IAAM4uB,EAAoB,IAAI,CAACr5B,OAAO,CAACstB,WAAW,CAACgB,UAAU,CAC7D,GAAI+K,GAAmB/kB,aACnB,IAAI,CAAC3K,MAAM,CAAC,EAAE,EAAExJ,QAAS,CACzB,IAAM21B,EAAS,AAACuD,EAAkB/kB,WAAW,CAAI,EAC3Cga,EAAa,IAAI,CAAC3kB,MAAM,CAAC,EAAE,CAC3B6K,EAAO8Z,EAAWnuB,OAAO,CAACm5B,SAAS,CACnC9pB,EAAKgF,CAAI,CAAC,EAAE,CACZ/E,EAAK+E,CAAI,CAAC,EAAE,CACZ+kB,EAAK/kB,CAAI,CAAC,EAAE,CACZglB,EAAKhlB,CAAI,CAAC,EAAE,AAClBhF,CAAAA,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKsmB,EACvBrmB,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKqmB,EACvByD,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKzD,EACvB0D,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAK1D,EACvBtmB,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKsmB,EACvBrmB,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKqmB,EACvByD,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAKzD,EACvB0D,CAAE,CAAC,EAAE,CAAG,AAACA,CAAAA,CAAE,CAAC,EAAE,EAAI,CAAA,EAAK1D,EACvBxH,EAAWnuB,OAAO,CAACC,IAAI,CAAC,CACpBhC,EAAGoW,CACP,EACJ,CAEA,IAAI,CAACtG,aAAa,CAACjN,OAAO,CAAC,AAAC0N,GAAiBA,EAAa9O,MAAM,GACpE,CACA6J,UAAUd,CAAE,CAAEF,CAAE,CAAE,CACd,IAAI,CAACiB,MAAM,CAAC1I,OAAO,CAAC,AAAC4T,GAASA,EAAKnL,SAAS,CAACd,EAAIF,GACrD,CACJ,CACA0vB,GAAQp5B,SAAS,CAACoqB,cAAc,CAAGiN,GAAcjK,GAAuBptB,SAAS,CAACoqB,cAAc,CAWhG,CACIkE,YAAa,CAKTgK,WAAY,KAMZn0B,MAAO,EAMPyI,MAAO,EACP0iB,WAAY,CAIR/a,KAAM,2BAINe,YAAa,EAIbjB,OAAQ,KAAK,CACjB,EAMA4lB,WAAY,CAKRP,QAAS,CAAA,EAIT53B,OAAQ,EASRqU,UAAW,OAQX6gB,UAAW,OACf,EAKAkD,WAAY,CAKRR,QAAS,CAAA,EAIT53B,OAAQ,EAWRqU,UAAW,OAUX6gB,UAAW,OACf,EACArzB,MAAO,CAOH+1B,QAAS,CAAA,EAOT3tB,MAAO,CACHmhB,SAAU,QACV5Y,MAAO,SACX,EAkDA0G,UAAW,KAAK,CACpB,CACJ,EACA5L,oBAAqB,CACjB1D,WAAY,SAAUtD,CAAM,EACxB,IAAM+wB,EAAU,IAAI,CAAC/0B,KAAK,CAAE7C,EAAQ6G,EAAO7G,KAAK,CAAEP,EAAUoH,EAAOpH,OAAO,CAAEstB,EAActtB,EAAQstB,WAAW,CAAEgK,EAAahK,EAAYgK,UAAU,CAAElpB,EAAsBpO,EAAQoO,mBAAmB,CAAErF,EAAWxI,EAAMwI,QAAQ,CAAE5F,EAAQ5C,EAAM4C,KAAK,CAACmqB,EAAYnqB,KAAK,CAAC,CAAEyI,EAAQrL,EAAMqL,KAAK,CAAC0hB,EAAY1hB,KAAK,CAAC,CAAE8qB,EAAMC,GAAYvvB,EAAOgpB,QAAQ,CAAEhpB,EAAOipB,QAAQ,CAAEjpB,EAAOkpB,QAAQ,CAAElpB,EAAOmpB,QAAQ,EAClYkJ,EAAUryB,EAAOipB,QAAQ,CAAEqJ,EAAUtyB,EAAOmpB,QAAQ,CAAEvtB,EAAG6E,EAwB7D,MAvBmB,MAAfyvB,IACAoC,EAAU,AAAChD,CAAAA,EAAInG,QAAQ,CAAGmG,EAAIpG,QAAQ,AAAD,EAAK,EAE1B,IAAZ6H,GACAsB,CAAAA,EAAUryB,EAAOgpB,QAAQ,AAAD,GAGb,MAAfkH,IACAmC,EAAU/C,EAAItG,QAAQ,CACjB,AAACsG,CAAAA,EAAIrG,QAAQ,CAAGqG,EAAItG,QAAQ,AAAD,EAAK,EAErB,IAAZ+H,GACAuB,CAAAA,EAAUtyB,EAAOkpB,QAAQ,AAAD,GAG5BvnB,GACA/F,EAAI4I,EAAMmB,QAAQ,CAAC2sB,GACnB7xB,EAAI1E,EAAM4J,QAAQ,CAAC0sB,KAGnBz2B,EAAIG,EAAM4J,QAAQ,CAAC0sB,GACnB5xB,EAAI+D,EAAMmB,QAAQ,CAAC2sB,IAEhB,CACH12B,EAAGA,EAAKoL,EAAoBvD,KAAK,CAAG,EACpChD,EAAGA,EAAKuG,EAAoBtD,MAAM,CAAG,CACzC,CACJ,EACA/D,OAAQ,CACJ6lB,KAAM,SAAUlmB,CAAC,CAAEU,CAAM,EACrB,IAAMqC,EAAc,IAAI,CAACL,sBAAsB,CAAC1C,GAAI4wB,EAAalwB,EAAOpH,OAAO,CAACstB,WAAW,CAACgK,UAAU,CAAEl0B,EAAQ,IAAI,CAACA,KAAK,CAAEJ,EAAIs0B,AAAe,MAAfA,EAAqB,EAAI7tB,EAAYzG,CAAC,CAAE6E,EAAIyvB,AAAe,MAAfA,EAAqB,EAAI7tB,EAAY5B,CAAC,CAClNT,EAAO2wB,MAAM,CAAC/0B,EAAG6E,EAAGzE,EAAOk0B,GAC3BlwB,EAAOkxB,OAAO,EAAIt1B,EAClBoE,EAAOmxB,OAAO,EAAI1wB,EAClBT,EAAOvH,MAAM,CAAC,CAAA,EAAO,CAAA,EACzB,CACJ,CACJ,CACJ,GACAusB,GAAuB1mB,KAAK,CAACi0B,OAAO,CAAGvB,GAwBV,IAAMwB,GAA6Bx6B,WAEvDw6B,MAA4BC,OAAO"}