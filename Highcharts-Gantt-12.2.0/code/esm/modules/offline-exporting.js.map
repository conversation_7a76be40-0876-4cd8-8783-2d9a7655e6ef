{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/offline-exporting\n * @requires highcharts\n * @requires highcharts/modules/exporting\n *\n * Client side exporting module\n *\n * (c) 2015-2025 Torstein Honsi / Oystein Moseng\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__data_src_js_55fab5a0__ from \"./data.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__exporting_src_js_3afc400f__ from \"./exporting.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { isSafari, win, win: { document: doc } } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n * @private\n * @function Highcharts.dataURLtoBlob\n * @param {string} dataURL\n *        URL to convert\n * @return {string|undefined}\n *         Blob\n */\nfunction dataURLtoBlob(dataURL) {\n    const parts = dataURL\n        .replace(/filename=.*;/, '')\n        .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        const binStr = win.atob(parts[3]), buf = new win.ArrayBuffer(binStr.length), binary = new win.Uint8Array(buf);\n        for (let i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n * @param {string|global.URL} dataURL\n *        The dataURL/Blob to download\n * @param {string} filename\n *        The name of the resulting file (w/extension)\n * @return {void}\n */\nfunction downloadURL(dataURL, filename) {\n    const nav = win.navigator, a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    const // Some browsers have limitations for data URL lengths. Try to convert\n    // to Blob or fall back. Edge always needs that blob.\n    isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n    // Safari on iOS needs Blob in order to download PDF\n    safariBlob = (isSafari &&\n        typeof dataURL === 'string' &&\n        dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DownloadURL = {\n    dataURLtoBlob,\n    downloadURL\n};\n/* harmony default export */ const Extensions_DownloadURL = (DownloadURL);\n\n;// external [\"../highcharts.js\",\"default\",\"AST\"]\nconst external_highcharts_src_js_default_AST_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].AST;\nvar external_highcharts_src_js_default_AST_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_AST_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"Chart\"]\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const external_highcharts_src_js_default_Chart_namespaceObject = x({  });\n;// ./code/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    class Additions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(chart) {\n            this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        addUpdate(updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        }\n        /**\n         * @private\n         */\n        update(options, redraw) {\n            this.updates.forEach((updateFn) => {\n                updateFn.call(this.chart, options, redraw);\n            });\n        }\n    }\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es-modules/Extensions/Exporting/ExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isTouchDevice } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  API Options\n *\n * */\n// Add the export related options\n/**\n * Options for the exporting module. For an overview on the matter, see\n * [the docs](https://www.highcharts.com/docs/export-module/export-module-overview) and\n * read our [Fair Usage Policy](https://www.highcharts.com/docs/export-module/privacy-disclaimer-export).\n *\n * @requires     modules/exporting\n * @optionparent exporting\n */\nconst exporting = {\n    /**\n     * Experimental setting to allow HTML inside the chart (added through\n     * the `useHTML` options), directly in the exported image. This allows\n     * you to preserve complicated HTML structures like tables or bi-directional\n     * text in exported charts.\n     *\n     * Disclaimer: The HTML is rendered in a `foreignObject` tag in the\n     * generated SVG. The official export server is based on PhantomJS,\n     * which supports this, but other SVG clients, like Batik, does not\n     * support it. This also applies to downloaded SVG that you want to\n     * open in a desktop client.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.8\n     * @apioption exporting.allowHTML\n     */\n    /**\n     * Allows the end user to sort the data table by clicking on column headers.\n     *\n     * @since 10.3.3\n     * @apioption exporting.allowTableSorting\n     */\n    allowTableSorting: true,\n    /**\n     * Allow exporting a chart retaining any user-applied CSS.\n     *\n     * Note that this is is default behavior in [styledMode](#chart.styledMode).\n     *\n     * @see [styledMode](#chart.styledMode)\n     *\n     * @sample {highcharts} highcharts/exporting/apply-stylesheets/\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since 12.0.0\n     * @apioption exporting.applyStyleSheets\n     */\n    /**\n     * Additional chart options to be merged into the chart before exporting to\n     * an image format. This does not apply to printing the chart via the export\n     * menu.\n     *\n     * For example, a common use case is to add data labels to improve\n     * readability of the exported chart, or to add a printer-friendly color\n     * scheme to exported PDFs.\n     *\n     * @sample {highcharts} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     * @sample {highstock} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     *\n     * @type      {Highcharts.Options}\n     * @apioption exporting.chartOptions\n     */\n    /**\n     * Whether to enable the exporting module. Disabling the module will\n     * hide the context button, but API methods will still be available.\n     *\n     * @sample {highcharts} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     * @sample {highstock} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     2.0\n     * @apioption exporting.enabled\n     */\n    /**\n     * Function to call if the offline-exporting module fails to export\n     * a chart on the client side, and [fallbackToExportServer](\n     * #exporting.fallbackToExportServer) is disabled. If left undefined, an\n     * exception is thrown instead. Receives two parameters, the exporting\n     * options, and the error from the module.\n     *\n     * @see [fallbackToExportServer](#exporting.fallbackToExportServer)\n     *\n     * @type      {Highcharts.ExportingErrorCallbackFunction}\n     * @since     5.0.0\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.error\n     */\n    /**\n     * Whether or not to fall back to the export server if the offline-exporting\n     * module is unable to export the chart on the client side. This happens for\n     * certain browsers, and certain features (e.g.\n     * [allowHTML](#exporting.allowHTML)), depending on the image type exporting\n     * to. For very complex charts, it is possible that export can fail in\n     * browsers that don't support Blob objects, due to data URL length limits.\n     * It is recommended to define the [exporting.error](#exporting.error)\n     * handler if disabling fallback, in order to notify users in case export\n     * fails.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     4.1.8\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.fallbackToExportServer\n     */\n    /**\n     * The filename, without extension, to use for the exported chart.\n     *\n     * @sample {highcharts} highcharts/exporting/filename/\n     *         Custom file name\n     * @sample {highstock} highcharts/exporting/filename/\n     *         Custom file name\n     *\n     * @type      {string}\n     * @default   chart\n     * @since     2.0\n     * @apioption exporting.filename\n     */\n    /**\n     * Highcharts v11.2.0 and older. An object containing additional key value\n     * data for the POST form that sends the SVG to the export server. For\n     * example, a `target` can be set to make sure the generated image is\n     * received in another frame, or a custom `enctype` or `encoding` can be\n     * set.\n     *\n     * With Highcharts v11.3.0, the `fetch` API replaced the old HTML form. To\n     * modify the request, now use [fetchOptions](#exporting.fetchOptions)\n     * instead.\n     *\n     * @deprecated\n     * @type      {Highcharts.HTMLAttributes}\n     * @since     3.0.8\n     * @apioption exporting.formAttributes\n     */\n    /**\n     * Options for the fetch request used when sending the SVG to the export\n     * server.\n     *\n     * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/fetch)\n     * for more information\n     *\n     * @type {Object}\n     * @since 11.3.0\n     * @apioption exporting.fetchOptions\n     */\n    /**\n     * Path where Highcharts will look for export module dependencies to\n     * load on demand if they don't already exist on `window`. Should currently\n     * point to location of [CanVG](https://github.com/canvg/canvg) library,\n     * [jsPDF](https://github.com/parallax/jsPDF) and\n     * [svg2pdf.js](https://github.com/yWorks/svg2pdf.js), required for client\n     * side export in certain browsers.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/{version}/lib\n     * @since     5.0.0\n     * @apioption exporting.libURL\n     */\n    /**\n     * Analogous to [sourceWidth](#exporting.sourceWidth).\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceHeight\n     */\n    /**\n     * The width of the original chart when exported, unless an explicit\n     * [chart.width](#chart.width) is set, or a pixel width is set on the\n     * container. The width exported raster image is then multiplied by\n     * [scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highstock} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highmaps} maps/exporting/sourcewidth/\n     *         Source size demo\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceWidth\n     */\n    /**\n     * The pixel width of charts exported to PNG or JPG. As of Highcharts\n     * 3.0, the default pixel width is a function of the [chart.width](\n     * #chart.width) or [exporting.sourceWidth](#exporting.sourceWidth) and the\n     * [exporting.scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/width/\n     *         Export to 200px wide images\n     * @sample {highstock} highcharts/exporting/width/\n     *         Export to 200px wide images\n     *\n     * @type      {number}\n     * @since     2.0\n     * @apioption exporting.width\n     */\n    /**\n     * Default MIME type for exporting if `chart.exportChart()` is called\n     * without specifying a `type` option. Possible values are `image/png`,\n     *  `image/jpeg`, `application/pdf` and `image/svg+xml`.\n     *\n     * @type  {Highcharts.ExportingMimeTypeValue}\n     * @since 2.0\n     */\n    type: 'image/png',\n    /**\n     * The URL for the server module converting the SVG string to an image\n     * format. By default this points to Highchart's free web service.\n     *\n     * @since 2.0\n     */\n    url: `https://export-svg.highcharts.com?v=${(external_highcharts_src_js_default_default()).version}`,\n    /**\n     * Settings for a custom font for the exported PDF, when using the\n     * `offline-exporting` module. This is used for languages containing\n     * non-ASCII characters, like Chinese, Russian, Japanese etc.\n     *\n     * As described in the [jsPDF\n     * docs](https://github.com/parallax/jsPDF#use-of-unicode-characters--utf-8),\n     * the 14 standard fonts in PDF are limited to the ASCII-codepage.\n     * Therefore, in order to support other text in the exported PDF, one or\n     * more TTF font files have to be passed on to the exporting module.\n     *\n     * See more in [the\n     * docs](https://www.highcharts.com/docs/export-module/client-side-export).\n     *\n     * @sample {highcharts} highcharts/exporting/offline-download-pdffont/\n     *         Download PDF in a language containing non-Latin characters.\n     *\n     * @since 10.0.0\n     * @requires modules/offline-exporting\n     */\n    pdfFont: {\n        /**\n         * The TTF font file for normal `font-style`. If font variations like\n         * `bold` or `italic` are not defined, the `normal` font will be used\n         * for those too.\n         *\n         * @type string|undefined\n         */\n        normal: void 0,\n        /**\n         * The TTF font file for bold text.\n         *\n         * @type string|undefined\n         */\n        bold: void 0,\n        /**\n         * The TTF font file for bold and italic text.\n         *\n         * @type string|undefined\n         */\n        bolditalic: void 0,\n        /**\n         * The TTF font file for italic text.\n         *\n         * @type string|undefined\n         */\n        italic: void 0\n    },\n    /**\n     * When printing the chart from the menu item in the burger menu, if\n     * the on-screen chart exceeds this width, it is resized. After printing\n     * or cancelled, it is restored. The default width makes the chart\n     * fit into typical paper format. Note that this does not affect the\n     * chart when printing the web page as a whole.\n     *\n     * @since 4.2.5\n     */\n    printMaxWidth: 780,\n    /**\n     * Defines the scale or zoom factor for the exported image compared\n     * to the on-screen display. While for instance a 600px wide chart\n     * may look good on a website, it will look bad in print. The default\n     * scale of 2 makes this chart export to a 1200px PNG or JPG.\n     *\n     * @see [chart.width](#chart.width)\n     * @see [exporting.sourceWidth](#exporting.sourceWidth)\n     *\n     * @sample {highcharts} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highstock} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highmaps} maps/exporting/scale/\n     *         Scale demonstrated\n     *\n     * @since 3.0\n     */\n    scale: 2,\n    /**\n     * Options for the export related buttons, print and export. In addition\n     * to the default buttons listed here, custom buttons can be added.\n     * See [navigation.buttonOptions](#navigation.buttonOptions) for general\n     * options.\n     *\n     * @type     {Highcharts.Dictionary<*>}\n     * @requires modules/exporting\n     */\n    buttons: {\n        /**\n         * Options for the export button.\n         *\n         * In styled mode, export button styles can be applied with the\n         * `.highcharts-contextbutton` class.\n         *\n         * @declare  Highcharts.ExportingButtonsOptionsObject\n         * @extends  navigation.buttonOptions\n         * @requires modules/exporting\n         */\n        contextButton: {\n            /**\n             * A click handler callback to use on the button directly instead of\n             * the popup menu.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-onclick/\n             *         Skip the menu and export the chart directly\n             *\n             * @type      {Function}\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.onclick\n             */\n            /**\n             * See [navigation.buttonOptions.symbolFill](\n             * #navigation.buttonOptions.symbolFill).\n             *\n             * @type      {Highcharts.ColorString}\n             * @default   #666666\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.symbolFill\n             */\n            /**\n             * The horizontal position of the button relative to the `align`\n             * option.\n             *\n             * @type      {number}\n             * @default   -10\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.x\n             */\n            /**\n             * The class name of the context button.\n             */\n            className: 'highcharts-contextbutton',\n            /**\n             * The class name of the menu appearing from the button.\n             */\n            menuClassName: 'highcharts-contextmenu',\n            /**\n             * The symbol for the button. Points to a definition function in\n             * the `Highcharts.Renderer.symbols` collection. The default\n             * `menu` function is part of the exporting module. Possible\n             * values are \"circle\", \"square\", \"diamond\", \"triangle\",\n             * \"triangle-down\", \"menu\", \"menuball\" or custom shape.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-symbol/\n             *         Use a circle for symbol\n             * @sample highcharts/exporting/buttons-contextbutton-symbol-custom/\n             *         Custom shape as symbol\n             *\n             * @type  {Highcharts.SymbolKeyValue|\"menu\"|\"menuball\"|string}\n             * @since 2.0\n             */\n            symbol: 'menu',\n            /**\n             * The key to a [lang](#lang) option setting that is used for the\n             * button's title tooltip. When the key is `contextButtonTitle`, it\n             * refers to [lang.contextButtonTitle](#lang.contextButtonTitle)\n             * that defaults to \"Chart context menu\".\n             *\n             * @since 6.1.4\n             */\n            titleKey: 'contextButtonTitle',\n            /**\n             * A collection of strings pointing to config options for the menu\n             * items. The config options are defined in the\n             * `menuItemDefinitions` option.\n             *\n             * By default, there is the \"View in full screen\" and \"Print\" menu\n             * items, plus one menu item for each of the available export types.\n             *\n             * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             *\n             * @type    {Array<string>}\n             * @default [\"viewFullscreen\", \"printChart\", \"separator\", \"downloadPNG\", \"downloadJPEG\", \"downloadSVG\"]\n             * @since   2.0\n             */\n            menuItems: [\n                'viewFullscreen',\n                'printChart',\n                'separator',\n                'downloadPNG',\n                'downloadJPEG',\n                'downloadSVG'\n            ]\n        }\n    },\n    /**\n     * An object consisting of definitions for the menu items in the context\n     * menu. Each key value pair has a `key` that is referenced in the\n     * [menuItems](#exporting.buttons.contextButton.menuItems) setting,\n     * and a `value`, which is an object with the following properties:\n     *\n     * - **onclick:** The click handler for the menu item\n     *\n     * - **text:** The text for the menu item\n     *\n     * - **textKey:** If internationalization is required, the key to a language\n     *   string\n     *\n     * Custom text for the \"exitFullScreen\" can be set only in lang options\n     * (it is not a separate button).\n     *\n     * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     *\n     *\n     * @type    {Highcharts.Dictionary<Highcharts.ExportingMenuObject>}\n     * @default {\"viewFullscreen\": {}, \"printChart\": {}, \"separator\": {}, \"downloadPNG\": {}, \"downloadJPEG\": {}, \"downloadPDF\": {}, \"downloadSVG\": {}}\n     * @since   5.0.13\n     */\n    menuItemDefinitions: {\n        /**\n         * @ignore\n         */\n        viewFullscreen: {\n            textKey: 'viewFullscreen',\n            onclick: function () {\n                if (this.fullscreen) {\n                    this.fullscreen.toggle();\n                }\n            }\n        },\n        /**\n         * @ignore\n         */\n        printChart: {\n            textKey: 'printChart',\n            onclick: function () {\n                this.print();\n            }\n        },\n        /**\n         * @ignore\n         */\n        separator: {\n            separator: true\n        },\n        /**\n         * @ignore\n         */\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: function () {\n                this.exportChart();\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: function () {\n                this.exportChart({\n                    type: 'application/pdf'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/svg+xml'\n                });\n            }\n        }\n    }\n};\n// Add language\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * Exporting module only. The text for the menu item to view the chart\n     * in full screen.\n     *\n     * @since 8.0.1\n     */\n    viewFullscreen: 'View in full screen',\n    /**\n     * Exporting module only. The text for the menu item to exit the chart\n     * from full screen.\n     *\n     * @since 8.0.1\n     */\n    exitFullscreen: 'Exit from full screen',\n    /**\n     * Exporting module only. The text for the menu item to print the chart.\n     *\n     * @since    3.0.1\n     * @requires modules/exporting\n     */\n    printChart: 'Print chart',\n    /**\n     * Exporting module only. The text for the PNG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPNG: 'Download PNG image',\n    /**\n     * Exporting module only. The text for the JPEG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadJPEG: 'Download JPEG image',\n    /**\n     * Exporting module only. The text for the PDF download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPDF: 'Download PDF document',\n    /**\n     * Exporting module only. The text for the SVG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadSVG: 'Download SVG vector image',\n    /**\n     * Exporting module menu. The tooltip title for the context menu holding\n     * print and export menu items.\n     *\n     * @since    3.0\n     * @requires modules/exporting\n     */\n    contextButtonTitle: 'Chart context menu'\n};\n/**\n * A collection of options for buttons and menus appearing in the exporting\n * module or in Stock Tools.\n *\n * @requires     modules/exporting\n * @optionparent navigation\n */\nconst navigation = {\n    /**\n     * A collection of options for buttons appearing in the exporting\n     * module.\n     *\n     * In styled mode, the buttons are styled with the\n     * `.highcharts-contextbutton` and `.highcharts-button-symbol` classes.\n     *\n     * @requires modules/exporting\n     */\n    buttonOptions: {\n        /**\n         * Whether to enable buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-enabled/\n         *         Exporting module loaded but buttons disabled\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     2.0\n         * @apioption navigation.buttonOptions.enabled\n         */\n        /**\n         * The pixel size of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolSize: 14,\n        /**\n         * The x position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolX: 14.5,\n        /**\n         * The y position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolY: 13.5,\n        /**\n         * Alignment for the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-align/\n         *         Center aligned\n         *\n         * @type  {Highcharts.AlignValue}\n         * @since 2.0\n         */\n        align: 'right',\n        /**\n         * The pixel spacing between buttons, and between the context button and\n         * the title.\n         *\n         * @sample highcharts/title/widthadjust\n         *         Adjust the spacing when using text button\n         * @since 2.0\n         */\n        buttonSpacing: 5,\n        /**\n         * Pixel height of the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        height: 28,\n        /**\n         * A text string to add to the individual button.\n         *\n         * @sample highcharts/exporting/buttons-text/\n         *         Full text button\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         * @sample highcharts/exporting/buttons-text-symbol/\n         *         Combined symbol and text\n         *\n         * @type      {string}\n         * @default   null\n         * @since     3.0\n         * @apioption navigation.buttonOptions.text\n         */\n        /**\n         * Whether to use HTML for rendering the button. HTML allows for things\n         * like inline CSS or image-based icons.\n         *\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         *\n         * @type      boolean\n         * @default   false\n         * @since 10.3.0\n         * @apioption navigation.buttonOptions.useHTML\n         */\n        /**\n         * The vertical offset of the button's position relative to its\n         * `verticalAlign`. By default adjusted for the chart title alignment.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @since     2.0\n         * @apioption navigation.buttonOptions.y\n         */\n        y: -5,\n        /**\n         * The vertical alignment of the buttons. Can be one of `\"top\"`,\n         * `\"middle\"` or `\"bottom\"`.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @type  {Highcharts.VerticalAlignValue}\n         * @since 2.0\n         */\n        verticalAlign: 'top',\n        /**\n         * The pixel width of the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        width: 28,\n        /**\n         * Fill color for the symbol within the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolfill/\n         *         Blue symbol stroke for one of the buttons\n         *\n         * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since 2.0\n         */\n        symbolFill: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The color of the symbol's stroke or line.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolstroke/\n         *         Blue symbol stroke\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 2.0\n         */\n        symbolStroke: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The pixel stroke width of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolStrokeWidth: 3,\n        /**\n         * A configuration object for the button theme. The object accepts\n         * SVG properties like `stroke-width`, `stroke` and `fill`.\n         * Tri-state button styles are supported by the `states.hover` and\n         * `states.select` objects.\n         *\n         * @sample highcharts/navigation/buttonoptions-theme/\n         *         Theming the buttons\n         *\n         * @requires modules/exporting\n         *\n         * @since 3.0\n         */\n        theme: {\n            /**\n             * The default fill exists only to capture hover events.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            /**\n             * Padding for the button.\n             */\n            padding: 5,\n            /**\n             * Default stroke for the buttons.\n             *\n             * @type      {Highcharts.ColorString}\n             */\n            stroke: 'none',\n            /**\n             * Default stroke linecap for the buttons.\n             */\n            'stroke-linecap': 'round'\n        }\n    },\n    /**\n     * CSS styles for the popup menu appearing by default when the export\n     * icon is clicked. This menu is rendered in HTML.\n     *\n     * @see In styled mode, the menu is styled with the `.highcharts-menu`\n     *      class.\n     *\n     * @sample highcharts/navigation/menustyle/\n     *         Light gray menu background\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#ffffff\", \"borderRadius\": \"3px\", \"padding\": \"0.5em\"}\n     * @since   2.0\n     */\n    menuStyle: {\n        /** @ignore-option */\n        border: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        background: \"#ffffff\" /* Palette.backgroundColor */,\n        /** @ignore-option */\n        padding: '0.5em'\n    },\n    /**\n     * CSS styles for the individual items within the popup menu appearing\n     * by default when the export icon is clicked. The menu items are\n     * rendered in HTML. Font size defaults to `11px` on desktop and `14px`\n     * on touch devices.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample {highcharts} highcharts/navigation/menuitemstyle/\n     *         Add a grey stripe to the left\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"padding\": \"0.5em\", \"color\": \"#333333\", \"background\": \"none\", \"borderRadius\": \"3px\", \"fontSize\": \"0.8em\", \"transition\": \"background 250ms, color 250ms\"}\n     * @since   2.0\n     */\n    menuItemStyle: {\n        /** @ignore-option */\n        background: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        color: \"#333333\" /* Palette.neutralColor80 */,\n        /** @ignore-option */\n        padding: '0.5em',\n        /** @ignore-option */\n        fontSize: isTouchDevice ? '0.9em' : '0.8em',\n        /** @ignore-option */\n        transition: 'background 250ms, color 250ms'\n    },\n    /**\n     * CSS styles for the hover state of the individual items within the\n     * popup menu appearing by default when the export icon is clicked. The\n     * menu items are rendered in HTML.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample highcharts/navigation/menuitemhoverstyle/\n     *         Bold text on hover\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#f2f2f2\" }\n     * @since   2.0\n     */\n    menuItemHoverStyle: {\n        /** @ignore-option */\n        background: \"#f2f2f2\" /* Palette.neutralColor5 */\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst ExportingDefaults = {\n    exporting,\n    lang,\n    navigation\n};\n/* harmony default export */ const Exporting_ExportingDefaults = (ExportingDefaults);\n\n;// ./code/es-modules/Extensions/Exporting/ExportingSymbols.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ExportingSymbols;\n(function (ExportingSymbols) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    const modifiedClasses = [];\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SVGRendererClass) {\n        if (modifiedClasses.indexOf(SVGRendererClass) === -1) {\n            modifiedClasses.push(SVGRendererClass);\n            const symbols = SVGRendererClass.prototype.symbols;\n            symbols.menu = menu;\n            symbols.menuball = menuball.bind(symbols);\n        }\n    }\n    ExportingSymbols.compose = compose;\n    /**\n     * @private\n     */\n    function menu(x, y, width, height) {\n        const arr = [\n            ['M', x, y + 2.5],\n            ['L', x + width, y + 2.5],\n            ['M', x, y + height / 2 + 0.5],\n            ['L', x + width, y + height / 2 + 0.5],\n            ['M', x, y + height - 1.5],\n            ['L', x + width, y + height - 1.5]\n        ];\n        return arr;\n    }\n    /**\n     * @private\n     */\n    function menuball(x, y, width, height) {\n        const h = (height / 3) - 2;\n        let path = [];\n        path = path.concat(this.circle(width - h, y, h, h), this.circle(width - h, y + h + 4, h, h), this.circle(width - h, y + 2 * (h + 4), h, h));\n        return path;\n    }\n})(ExportingSymbols || (ExportingSymbols = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_ExportingSymbols = (ExportingSymbols);\n\n;// ./code/es-modules/Extensions/Exporting/Fullscreen.js\n/* *\n *\n *  (c) 2009-2025 Rafal Sebestjanski\n *\n *  Full screen for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/**\n * The module allows user to enable display chart in full screen mode.\n * Used in StockTools too.\n * Based on default solutions in browsers.\n */\n\n\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, fireEvent, pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onChartBeforeRender() {\n    /**\n     * @name Highcharts.Chart#fullscreen\n     * @type {Highcharts.Fullscreen}\n     * @requires modules/full-screen\n     */\n    this.fullscreen = new Fullscreen(this);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles displaying chart's container in the fullscreen mode.\n *\n * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n *\n * @class\n * @name Highcharts.Fullscreen\n *\n * @requires modules/exporting\n */\nclass Fullscreen {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Prepares the chart class to support fullscreen.\n     *\n     * @param {typeof_Highcharts.Chart} ChartClass\n     * The chart class to decorate with fullscreen support.\n     */\n    static compose(ChartClass) {\n        if (pushUnique(composed, 'Fullscreen')) {\n            // Initialize fullscreen\n            addEvent(ChartClass, 'beforeRender', onChartBeforeRender);\n        }\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(chart) {\n        /**\n         * Chart managed by the fullscreen controller.\n         * @name Highcharts.Fullscreen#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The flag is set to `true` when the chart is displayed in\n         * the fullscreen mode.\n         *\n         * @name Highcharts.Fullscreen#isOpen\n         * @type {boolean|undefined}\n         * @since 8.0.1\n         */\n        this.isOpen = false;\n        const container = chart.renderTo;\n        // Hold event and methods available only for a current browser.\n        if (!this.browserProps) {\n            if (typeof container.requestFullscreen === 'function') {\n                this.browserProps = {\n                    fullscreenChange: 'fullscreenchange',\n                    requestFullscreen: 'requestFullscreen',\n                    exitFullscreen: 'exitFullscreen'\n                };\n            }\n            else if (container.mozRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'mozfullscreenchange',\n                    requestFullscreen: 'mozRequestFullScreen',\n                    exitFullscreen: 'mozCancelFullScreen'\n                };\n            }\n            else if (container.webkitRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'webkitfullscreenchange',\n                    requestFullscreen: 'webkitRequestFullScreen',\n                    exitFullscreen: 'webkitExitFullscreen'\n                };\n            }\n            else if (container.msRequestFullscreen) {\n                this.browserProps = {\n                    fullscreenChange: 'MSFullscreenChange',\n                    requestFullscreen: 'msRequestFullscreen',\n                    exitFullscreen: 'msExitFullscreen'\n                };\n            }\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Stops displaying the chart in fullscreen mode.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function    Highcharts.Fullscreen#close\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    close() {\n        const fullscreen = this, chart = fullscreen.chart, optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenClose', null, function () {\n            // Don't fire exitFullscreen() when user exited\n            // using 'Escape' button.\n            if (fullscreen.isOpen &&\n                fullscreen.browserProps &&\n                chart.container.ownerDocument instanceof Document) {\n                chart.container.ownerDocument[fullscreen.browserProps.exitFullscreen]();\n            }\n            // Unbind event as it's necessary only before exiting\n            // from fullscreen.\n            if (fullscreen.unbindFullscreenEvent) {\n                fullscreen.unbindFullscreenEvent = fullscreen\n                    .unbindFullscreenEvent();\n            }\n            chart.setSize(fullscreen.origWidth, fullscreen.origHeight, false);\n            fullscreen.origWidth = void 0;\n            fullscreen.origHeight = void 0;\n            optionsChart.width = fullscreen.origWidthOption;\n            optionsChart.height = fullscreen.origHeightOption;\n            fullscreen.origWidthOption = void 0;\n            fullscreen.origHeightOption = void 0;\n            fullscreen.isOpen = false;\n            fullscreen.setButtonText();\n        });\n    }\n    /**\n     * Displays the chart in fullscreen mode.\n     * When fired customly by user before exporting context button is created,\n     * button's text will not be replaced - it's on the user side.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function Highcharts.Fullscreen#open\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    open() {\n        const fullscreen = this, chart = fullscreen.chart, optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenOpen', null, function () {\n            if (optionsChart) {\n                fullscreen.origWidthOption = optionsChart.width;\n                fullscreen.origHeightOption = optionsChart.height;\n            }\n            fullscreen.origWidth = chart.chartWidth;\n            fullscreen.origHeight = chart.chartHeight;\n            // Handle exitFullscreen() method when user clicks 'Escape' button.\n            if (fullscreen.browserProps) {\n                const unbindChange = addEvent(chart.container.ownerDocument, // Chart's document\n                fullscreen.browserProps.fullscreenChange, function () {\n                    // Handle lack of async of browser's\n                    // fullScreenChange event.\n                    if (fullscreen.isOpen) {\n                        fullscreen.isOpen = false;\n                        fullscreen.close();\n                    }\n                    else {\n                        chart.setSize(null, null, false);\n                        fullscreen.isOpen = true;\n                        fullscreen.setButtonText();\n                    }\n                });\n                const unbindDestroy = addEvent(chart, 'destroy', unbindChange);\n                fullscreen.unbindFullscreenEvent = () => {\n                    unbindChange();\n                    unbindDestroy();\n                };\n                const promise = chart.renderTo[fullscreen.browserProps.requestFullscreen]();\n                if (promise) {\n                    promise['catch'](function () {\n                        alert(// eslint-disable-line no-alert\n                        'Full screen is not supported inside a frame.');\n                    });\n                }\n            }\n        });\n    }\n    /**\n     * Replaces the exporting context button's text when toogling the\n     * fullscreen mode.\n     *\n     * @private\n     *\n     * @since 8.0.1\n     *\n     * @requires modules/full-screen\n     */\n    setButtonText() {\n        const chart = this.chart, exportDivElements = chart.exportDivElements, exportingOptions = chart.options.exporting, menuItems = (exportingOptions &&\n            exportingOptions.buttons &&\n            exportingOptions.buttons.contextButton.menuItems), lang = chart.options.lang;\n        if (exportingOptions &&\n            exportingOptions.menuItemDefinitions &&\n            lang &&\n            lang.exitFullscreen &&\n            lang.viewFullscreen &&\n            menuItems &&\n            exportDivElements) {\n            const exportDivElement = exportDivElements[menuItems.indexOf('viewFullscreen')];\n            if (exportDivElement) {\n                external_highcharts_src_js_default_AST_default().setElementHTML(exportDivElement, !this.isOpen ?\n                    (exportingOptions.menuItemDefinitions.viewFullscreen\n                        .text ||\n                        lang.viewFullscreen) : lang.exitFullscreen);\n            }\n        }\n    }\n    /**\n     * Toggles displaying the chart in fullscreen mode.\n     * By default, when the exporting module is enabled, a context button with\n     * a drop down menu in the upper right corner accesses this function.\n     * Exporting module required.\n     *\n     * @since 8.0.1\n     *\n     * @sample      highcharts/members/chart-togglefullscreen/\n     *              Toggle fullscreen mode from a HTML button\n     *\n     * @function Highcharts.Fullscreen#toggle\n     * @requires    modules/full-screen\n     */\n    toggle() {\n        const fullscreen = this;\n        if (!fullscreen.isOpen) {\n            fullscreen.open();\n        }\n        else {\n            fullscreen.close();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_Fullscreen = (Fullscreen);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired when closing the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenCloseCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired when opening the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenOpenCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n(''); // Keeps doclets above separated from following code\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires when a fullscreen is closed through the context menu item,\n * or a fullscreen is closed on the `Escape` button click,\n * or the `Chart.fullscreen.close` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenCloseCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenClose\n */\n/**\n * Fires when a fullscreen is opened through the context menu item,\n * or the `Chart.fullscreen.open` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenOpenCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenOpen\n */\n(''); // Keeps doclets above in transpiled file\n\n;// external [\"./data.js\",\"default\",\"HttpUtilities\"]\nconst external_data_src_js_default_HttpUtilities_namespaceObject = __WEBPACK_EXTERNAL_MODULE__data_src_js_55fab5a0__[\"default\"].HttpUtilities;\nvar external_data_src_js_default_HttpUtilities_default = /*#__PURE__*/__webpack_require__.n(external_data_src_js_default_HttpUtilities_namespaceObject);\n;// ./code/es-modules/Extensions/Exporting/Exporting.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { defaultOptions } = (external_highcharts_src_js_default_default());\n\n\n\n\nconst { doc: Exporting_doc, SVG_NS, win: Exporting_win } = (external_highcharts_src_js_default_default());\n\n\nconst { addEvent: Exporting_addEvent, css, createElement, discardElement, extend, find, fireEvent: Exporting_fireEvent, isObject, merge, objectEach, pick, removeEvent, splat, uniqueKey } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar Exporting;\n(function (Exporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // These CSS properties are not inlined. Remember camelCase.\n    const inlineDenylist = [\n        /-/, // In Firefox, both hyphened and camelCased names are listed\n        /^(clipPath|cssText|d|height|width)$/, // Full words\n        /^font$/, // More specific props are set\n        /[lL]ogical(Width|Height)$/,\n        /^parentRule$/,\n        /^(cssRules|ownerRules)$/, // #19516 read-only properties\n        /perspective/,\n        /TapHighlightColor/,\n        /^transition/,\n        /^length$/, // #7700\n        /^\\d+$/ // #17538\n    ];\n    // These ones are translated to attributes rather than styles\n    const inlineToAttributes = [\n        'fill',\n        'stroke',\n        'strokeLinecap',\n        'strokeLinejoin',\n        'strokeWidth',\n        'textAnchor',\n        'x',\n        'y'\n    ];\n    Exporting.inlineAllowlist = [];\n    const unstyledElements = [\n        'clipPath',\n        'defs',\n        'desc'\n    ];\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let printingChart;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add the export button to the chart, with options.\n     *\n     * @private\n     * @function Highcharts.Chart#addButton\n     * @param {Highcharts.NavigationButtonOptions} options\n     * @requires modules/exporting\n     */\n    function addButton(options) {\n        const chart = this, renderer = chart.renderer, btnOptions = merge(chart.options.navigation.buttonOptions, options), onclick = btnOptions.onclick, menuItems = btnOptions.menuItems, symbolSize = btnOptions.symbolSize || 12;\n        let symbol;\n        if (!chart.btnCount) {\n            chart.btnCount = 0;\n        }\n        // Keeps references to the button elements\n        if (!chart.exportDivElements) {\n            chart.exportDivElements = [];\n            chart.exportSVGElements = [];\n        }\n        if (btnOptions.enabled === false || !btnOptions.theme) {\n            return;\n        }\n        const theme = chart.styledMode ? {} : btnOptions.theme;\n        let callback;\n        if (onclick) {\n            callback = function (e) {\n                if (e) {\n                    e.stopPropagation();\n                }\n                onclick.call(chart, e);\n            };\n        }\n        else if (menuItems) {\n            callback = function (e) {\n                // Consistent with onclick call (#3495)\n                if (e) {\n                    e.stopPropagation();\n                }\n                chart.contextMenu(button.menuClassName, menuItems, button.translateX || 0, button.translateY || 0, button.width || 0, button.height || 0, button);\n                button.setState(2);\n            };\n        }\n        if (btnOptions.text && btnOptions.symbol) {\n            theme.paddingLeft = pick(theme.paddingLeft, 30);\n        }\n        else if (!btnOptions.text) {\n            extend(theme, {\n                width: btnOptions.width,\n                height: btnOptions.height,\n                padding: 0\n            });\n        }\n        const button = renderer\n            .button(btnOptions.text, 0, 0, callback, theme, void 0, void 0, void 0, void 0, btnOptions.useHTML)\n            .addClass(options.className)\n            .attr({\n            title: pick(chart.options.lang[btnOptions._titleKey || btnOptions.titleKey], '')\n        });\n        button.menuClassName = (options.menuClassName ||\n            'highcharts-menu-' + chart.btnCount++);\n        if (btnOptions.symbol) {\n            symbol = renderer\n                .symbol(btnOptions.symbol, Math.round((btnOptions.symbolX || 0) - (symbolSize / 2)), Math.round((btnOptions.symbolY || 0) - (symbolSize / 2)), symbolSize, symbolSize\n            // If symbol is an image, scale it (#7957)\n            , {\n                width: symbolSize,\n                height: symbolSize\n            })\n                .addClass('highcharts-button-symbol')\n                .attr({\n                zIndex: 1\n            })\n                .add(button);\n            if (!chart.styledMode) {\n                symbol.attr({\n                    stroke: btnOptions.symbolStroke,\n                    fill: btnOptions.symbolFill,\n                    'stroke-width': btnOptions.symbolStrokeWidth || 1\n                });\n            }\n        }\n        button\n            .add(chart.exportingGroup)\n            .align(extend(btnOptions, {\n            width: button.width,\n            x: pick(btnOptions.x, chart.buttonOffset) // #1654\n        }), true, 'spacingBox');\n        chart.buttonOffset += (((button.width || 0) + btnOptions.buttonSpacing) *\n            (btnOptions.align === 'right' ? -1 : 1));\n        chart.exportSVGElements.push(button, symbol);\n    }\n    /**\n     * Clean up after printing a chart.\n     *\n     * @function Highcharts#afterPrint\n     *\n     * @private\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that was (or suppose to be) printed\n     *\n     * @emits Highcharts.Chart#event:afterPrint\n     */\n    function afterPrint() {\n        const chart = this;\n        if (!chart.printReverseInfo) {\n            return void 0;\n        }\n        const { childNodes, origDisplay, resetParams } = chart.printReverseInfo;\n        // Put the chart back in\n        chart.moveContainers(chart.renderTo);\n        // Restore all body content\n        [].forEach.call(childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                node.style.display = (origDisplay[i] || '');\n            }\n        });\n        chart.isPrinting = false;\n        // Reset printMaxWidth\n        if (resetParams) {\n            chart.setSize.apply(chart, resetParams);\n        }\n        delete chart.printReverseInfo;\n        printingChart = void 0;\n        Exporting_fireEvent(chart, 'afterPrint');\n    }\n    /**\n     * Prepare chart and document before printing a chart.\n     *\n     * @function Highcharts#beforePrint\n     *\n     * @private\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     */\n    function beforePrint() {\n        const chart = this, body = Exporting_doc.body, printMaxWidth = chart.options.exporting.printMaxWidth, printReverseInfo = {\n            childNodes: body.childNodes,\n            origDisplay: [],\n            resetParams: void 0\n        };\n        chart.isPrinting = true;\n        chart.pointer?.reset(void 0, 0);\n        Exporting_fireEvent(chart, 'beforePrint');\n        // Handle printMaxWidth\n        const handleMaxWidth = printMaxWidth &&\n            chart.chartWidth > printMaxWidth;\n        if (handleMaxWidth) {\n            printReverseInfo.resetParams = [\n                chart.options.chart.width,\n                void 0,\n                false\n            ];\n            chart.setSize(printMaxWidth, void 0, false);\n        }\n        // Hide all body content\n        [].forEach.call(printReverseInfo.childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                printReverseInfo.origDisplay[i] = node.style.display;\n                node.style.display = 'none';\n            }\n        });\n        // Pull out the chart\n        chart.moveContainers(body);\n        // Storage details for undo action after printing\n        chart.printReverseInfo = printReverseInfo;\n    }\n    /**\n     * @private\n     */\n    function chartCallback(chart) {\n        const composition = chart;\n        composition.renderExporting();\n        Exporting_addEvent(chart, 'redraw', composition.renderExporting);\n        // Destroy the export elements at chart destroy\n        Exporting_addEvent(chart, 'destroy', composition.destroyExport);\n        // Uncomment this to see a button directly below the chart, for quick\n        // testing of export\n        /*\n        let button, viewImage, viewSource;\n        if (!chart.renderer.forExport) {\n            viewImage = function () {\n                let div = doc.createElement('div');\n                div.innerHTML = chart.getSVGForExport();\n                chart.renderTo.parentNode.appendChild(div);\n            };\n\n            viewSource = function () {\n                let pre = doc.createElement('pre');\n                pre.innerHTML = chart.getSVGForExport()\n                    .replace(/</g, '\\n&lt;')\n                    .replace(/>/g, '&gt;');\n                chart.renderTo.parentNode.appendChild(pre);\n            };\n\n            viewImage();\n\n            // View SVG Image\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Image';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewImage;\n\n            // View SVG Source\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Source';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewSource;\n        }\n        //*/\n    }\n    /**\n     * @private\n     */\n    function compose(ChartClass, SVGRendererClass) {\n        Exporting_ExportingSymbols.compose(SVGRendererClass);\n        Exporting_Fullscreen.compose(ChartClass);\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.exportChart) {\n            chartProto.afterPrint = afterPrint;\n            chartProto.exportChart = exportChart;\n            chartProto.inlineStyles = inlineStyles;\n            chartProto.print = print;\n            chartProto.sanitizeSVG = sanitizeSVG;\n            chartProto.getChartHTML = getChartHTML;\n            chartProto.getSVG = getSVG;\n            chartProto.getSVGForExport = getSVGForExport;\n            chartProto.getFilename = getFilename;\n            chartProto.moveContainers = moveContainers;\n            chartProto.beforePrint = beforePrint;\n            chartProto.contextMenu = contextMenu;\n            chartProto.addButton = addButton;\n            chartProto.destroyExport = destroyExport;\n            chartProto.renderExporting = renderExporting;\n            chartProto.resolveCSSVariables = resolveCSSVariables;\n            chartProto.callbacks.push(chartCallback);\n            Exporting_addEvent(ChartClass, 'init', onChartInit);\n            Exporting_addEvent(ChartClass, 'layOutTitle', onChartLayOutTitle);\n            if ((external_highcharts_src_js_default_default()).isSafari) {\n                Exporting_win.matchMedia('print').addListener(function (mqlEvent) {\n                    if (!printingChart) {\n                        return void 0;\n                    }\n                    if (mqlEvent.matches) {\n                        printingChart.beforePrint();\n                    }\n                    else {\n                        printingChart.afterPrint();\n                    }\n                });\n            }\n            defaultOptions.exporting = merge(Exporting_ExportingDefaults.exporting, defaultOptions.exporting);\n            defaultOptions.lang = merge(Exporting_ExportingDefaults.lang, defaultOptions.lang);\n            // Buttons and menus are collected in a separate config option set\n            // called 'navigation'. This can be extended later to add control\n            // buttons like zoom and pan right click menus.\n            defaultOptions.navigation = merge(Exporting_ExportingDefaults.navigation, defaultOptions.navigation);\n        }\n    }\n    Exporting.compose = compose;\n    /**\n     * Display a popup menu for choosing the export type.\n     *\n     * @private\n     * @function Highcharts.Chart#contextMenu\n     * @param {string} className\n     *        An identifier for the menu.\n     * @param {Array<string|Highcharts.ExportingMenuObject>} items\n     *        A collection with text and onclicks for the items.\n     * @param {number} x\n     *        The x position of the opener button\n     * @param {number} y\n     *        The y position of the opener button\n     * @param {number} width\n     *        The width of the opener button\n     * @param {number} height\n     *        The height of the opener button\n     * @requires modules/exporting\n     */\n    function contextMenu(className, items, x, y, width, height, button) {\n        const chart = this, navOptions = chart.options.navigation, chartWidth = chart.chartWidth, chartHeight = chart.chartHeight, cacheName = 'cache-' + className, \n        // For mouse leave detection\n        menuPadding = Math.max(width, height);\n        let innerMenu, menu = chart[cacheName];\n        // Create the menu only the first time\n        if (!menu) {\n            // Create a HTML element above the SVG\n            chart.exportContextMenu = chart[cacheName] = menu =\n                createElement('div', {\n                    className: className\n                }, {\n                    position: 'absolute',\n                    zIndex: 1000,\n                    padding: menuPadding + 'px',\n                    pointerEvents: 'auto',\n                    ...chart.renderer.style\n                }, chart.scrollablePlotArea?.fixedDiv || chart.container);\n            innerMenu = createElement('ul', { className: 'highcharts-menu' }, chart.styledMode ? {} : {\n                listStyle: 'none',\n                margin: 0,\n                padding: 0\n            }, menu);\n            // Presentational CSS\n            if (!chart.styledMode) {\n                css(innerMenu, extend({\n                    MozBoxShadow: '3px 3px 10px #888',\n                    WebkitBoxShadow: '3px 3px 10px #888',\n                    boxShadow: '3px 3px 10px #888'\n                }, navOptions.menuStyle));\n            }\n            // Hide on mouse out\n            menu.hideMenu = function () {\n                css(menu, { display: 'none' });\n                if (button) {\n                    button.setState(0);\n                }\n                chart.openMenu = false;\n                // #10361, #9998\n                css(chart.renderTo, { overflow: 'hidden' });\n                css(chart.container, { overflow: 'hidden' });\n                external_highcharts_src_js_default_default().clearTimeout(menu.hideTimer);\n                Exporting_fireEvent(chart, 'exportMenuHidden');\n            };\n            // Hide the menu some time after mouse leave (#1357)\n            chart.exportEvents.push(Exporting_addEvent(menu, 'mouseleave', function () {\n                menu.hideTimer = Exporting_win.setTimeout(menu.hideMenu, 500);\n            }), Exporting_addEvent(menu, 'mouseenter', function () {\n                external_highcharts_src_js_default_default().clearTimeout(menu.hideTimer);\n            }), \n            // Hide it on clicking or touching outside the menu (#2258,\n            // #2335, #2407)\n            Exporting_addEvent(Exporting_doc, 'mouseup', function (e) {\n                if (!chart.pointer?.inClass(e.target, className)) {\n                    menu.hideMenu();\n                }\n            }), Exporting_addEvent(menu, 'click', function () {\n                if (chart.openMenu) {\n                    menu.hideMenu();\n                }\n            }));\n            // Create the items\n            items.forEach(function (item) {\n                if (typeof item === 'string') {\n                    item = chart.options.exporting\n                        .menuItemDefinitions[item];\n                }\n                if (isObject(item, true)) {\n                    let element;\n                    if (item.separator) {\n                        element = createElement('hr', void 0, void 0, innerMenu);\n                    }\n                    else {\n                        // When chart initialized with the table, wrong button\n                        // text displayed, #14352.\n                        if (item.textKey === 'viewData' &&\n                            chart.isDataTableVisible) {\n                            item.textKey = 'hideData';\n                        }\n                        element = createElement('li', {\n                            className: 'highcharts-menu-item',\n                            onclick: function (e) {\n                                if (e) { // IE7\n                                    e.stopPropagation();\n                                }\n                                menu.hideMenu();\n                                if (typeof item !== 'string' && item.onclick) {\n                                    item.onclick.apply(chart, arguments);\n                                }\n                            }\n                        }, void 0, innerMenu);\n                        external_highcharts_src_js_default_AST_default().setElementHTML(element, item.text ||\n                            chart.options.lang[item.textKey]);\n                        if (!chart.styledMode) {\n                            element.onmouseover = function () {\n                                css(this, navOptions.menuItemHoverStyle);\n                            };\n                            element.onmouseout = function () {\n                                css(this, navOptions.menuItemStyle);\n                            };\n                            css(element, extend({\n                                cursor: 'pointer'\n                            }, navOptions.menuItemStyle || {}));\n                        }\n                    }\n                    // Keep references to menu divs to be able to destroy them\n                    chart.exportDivElements.push(element);\n                }\n            });\n            // Keep references to menu and innerMenu div to be able to destroy\n            // them\n            chart.exportDivElements.push(innerMenu, menu);\n            chart.exportMenuWidth = menu.offsetWidth;\n            chart.exportMenuHeight = menu.offsetHeight;\n        }\n        const menuStyle = { display: 'block' };\n        // If outside right, right align it\n        if (x + (chart.exportMenuWidth || 0) > chartWidth) {\n            menuStyle.right = (chartWidth - x - width - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.left = (x - menuPadding) + 'px';\n        }\n        // If outside bottom, bottom align it\n        if (y + height + (chart.exportMenuHeight || 0) > chartHeight &&\n            button.alignOptions?.verticalAlign !== 'top') {\n            menuStyle.bottom = (chartHeight - y - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.top = (y + height - menuPadding) + 'px';\n        }\n        css(menu, menuStyle);\n        // #10361, #9998\n        css(chart.renderTo, { overflow: '' });\n        css(chart.container, { overflow: '' });\n        chart.openMenu = true;\n        Exporting_fireEvent(chart, 'exportMenuShown');\n    }\n    /**\n     * Destroy the export buttons.\n     * @private\n     * @function Highcharts.Chart#destroyExport\n     * @param {global.Event} [e]\n     * @requires modules/exporting\n     */\n    function destroyExport(e) {\n        const chart = e ? e.target : this, exportSVGElements = chart.exportSVGElements, exportDivElements = chart.exportDivElements, exportEvents = chart.exportEvents;\n        let cacheName;\n        // Destroy the extra buttons added\n        if (exportSVGElements) {\n            exportSVGElements.forEach((elem, i) => {\n                // Destroy and null the svg elements\n                if (elem) { // #1822\n                    elem.onclick = elem.ontouchstart = null;\n                    cacheName = 'cache-' + elem.menuClassName;\n                    if (chart[cacheName]) {\n                        delete chart[cacheName];\n                    }\n                    exportSVGElements[i] = elem.destroy();\n                }\n            });\n            exportSVGElements.length = 0;\n        }\n        // Destroy the exporting group\n        if (chart.exportingGroup) {\n            chart.exportingGroup.destroy();\n            delete chart.exportingGroup;\n        }\n        // Destroy the divs for the menu\n        if (exportDivElements) {\n            exportDivElements.forEach(function (elem, i) {\n                if (elem) {\n                    // Remove the event handler\n                    external_highcharts_src_js_default_default().clearTimeout(elem.hideTimer); // #5427\n                    removeEvent(elem, 'mouseleave');\n                    // Remove inline events\n                    // (chart.exportDivElements as any)[i] =\n                    exportDivElements[i] =\n                        elem.onmouseout =\n                            elem.onmouseover =\n                                elem.ontouchstart =\n                                    elem.onclick = null;\n                    // Destroy the div by moving to garbage bin\n                    discardElement(elem);\n                }\n            });\n            exportDivElements.length = 0;\n        }\n        if (exportEvents) {\n            exportEvents.forEach(function (unbind) {\n                unbind();\n            });\n            exportEvents.length = 0;\n        }\n    }\n    /**\n     * Exporting module required. Submit an SVG version of the chart to a server\n     * along with some parameters for conversion.\n     *\n     * @sample highcharts/members/chart-exportchart/\n     *         Export with no options\n     * @sample highcharts/members/chart-exportchart-filename/\n     *         PDF type and custom filename\n     * @sample highcharts/members/chart-exportchart-custom-background/\n     *         Different chart background in export\n     * @sample stock/members/chart-exportchart/\n     *         Export with Highcharts Stock\n     *\n     * @function Highcharts.Chart#exportChart\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     *        Exporting options in addition to those defined in\n     *        [exporting](https://api.highcharts.com/highcharts/exporting).\n     *\n     * @param {Highcharts.Options} chartOptions\n     *        Additional chart options for the exported chart. For example a\n     *        different background color can be added here, or `dataLabels` for\n     *        export only.\n     *\n     * @requires modules/exporting\n     */\n    function exportChart(exportingOptions, chartOptions) {\n        const svg = this.getSVGForExport(exportingOptions, chartOptions);\n        // Merge the options\n        exportingOptions = merge(this.options.exporting, exportingOptions);\n        // Do the post\n        external_data_src_js_default_HttpUtilities_default().post(exportingOptions.url, {\n            filename: exportingOptions.filename ?\n                exportingOptions.filename.replace(/\\//g, '-') :\n                this.getFilename(),\n            type: exportingOptions.type,\n            width: exportingOptions.width,\n            scale: exportingOptions.scale,\n            svg: svg\n        }, exportingOptions.fetchOptions);\n    }\n    /**\n     * Return the unfiltered innerHTML of the chart container. Used as hook for\n     * plugins. In styled mode, it also takes care of inlining CSS style rules.\n     *\n     * @see Chart#getSVG\n     *\n     * @function Highcharts.Chart#getChartHTML\n     *\n     * @return {string}\n     * The unfiltered SVG of the chart.\n     *\n     * @requires modules/exporting\n     */\n    function getChartHTML(applyStyleSheets) {\n        if (applyStyleSheets) {\n            this.inlineStyles();\n        }\n        this.resolveCSSVariables();\n        return this.container.innerHTML;\n    }\n    /**\n     * Get the default file name used for exported charts. By default it creates\n     * a file name based on the chart title.\n     *\n     * @function Highcharts.Chart#getFilename\n     *\n     * @return {string} A file name without extension.\n     *\n     * @requires modules/exporting\n     */\n    function getFilename() {\n        const s = this.userOptions.title && this.userOptions.title.text;\n        let filename = this.options.exporting.filename;\n        if (filename) {\n            return filename.replace(/\\//g, '-');\n        }\n        if (typeof s === 'string') {\n            filename = s\n                .toLowerCase()\n                .replace(/<\\/?[^>]+(>|$)/g, '') // Strip HTML tags\n                .replace(/[\\s_]+/g, '-')\n                .replace(/[^a-z\\d\\-]/g, '') // Preserve only latin\n                .replace(/^[\\-]+/g, '') // Dashes in the start\n                .replace(/[\\-]+/g, '-') // Dashes in a row\n                .substr(0, 24)\n                .replace(/[\\-]+$/g, ''); // Dashes in the end;\n        }\n        if (!filename || filename.length < 5) {\n            filename = 'chart';\n        }\n        return filename;\n    }\n    /**\n     * Return an SVG representation of the chart.\n     *\n     * @sample highcharts/members/chart-getsvg/\n     *         View the SVG from a button\n     *\n     * @function Highcharts.Chart#getSVG\n     *\n     * @param {Highcharts.Options} [chartOptions]\n     *        Additional chart options for the generated SVG representation. For\n     *        collections like `xAxis`, `yAxis` or `series`, the additional\n     *        options is either merged in to the original item of the same\n     *        `id`, or to the first item if a common id is not found.\n     *\n     * @return {string}\n     *         The SVG representation of the rendered chart.\n     *\n     * @emits Highcharts.Chart#event:getSVG\n     *\n     * @requires modules/exporting\n     */\n    function getSVG(chartOptions) {\n        const chart = this;\n        let svg, seriesOptions, \n        // Copy the options and add extra options\n        options = merge(chart.options, chartOptions);\n        // Use userOptions to make the options chain in series right (#3881)\n        options.plotOptions = merge(chart.userOptions.plotOptions, chartOptions && chartOptions.plotOptions);\n        // ... and likewise with time, avoid that undefined time properties are\n        // merged over legacy global time options\n        options.time = merge(chart.userOptions.time, chartOptions && chartOptions.time);\n        // Create a sandbox where a new chart will be generated\n        const sandbox = createElement('div', null, {\n            position: 'absolute',\n            top: '-9999em',\n            width: chart.chartWidth + 'px',\n            height: chart.chartHeight + 'px'\n        }, Exporting_doc.body);\n        // Get the source size\n        const cssWidth = chart.renderTo.style.width, cssHeight = chart.renderTo.style.height, sourceWidth = options.exporting.sourceWidth ||\n            options.chart.width ||\n            (/px$/.test(cssWidth) && parseInt(cssWidth, 10)) ||\n            (options.isGantt ? 800 : 600), sourceHeight = options.exporting.sourceHeight ||\n            options.chart.height ||\n            (/px$/.test(cssHeight) && parseInt(cssHeight, 10)) ||\n            400;\n        // Override some options\n        extend(options.chart, {\n            animation: false,\n            renderTo: sandbox,\n            forExport: true,\n            renderer: 'SVGRenderer',\n            width: sourceWidth,\n            height: sourceHeight\n        });\n        options.exporting.enabled = false; // Hide buttons in print\n        delete options.data; // #3004\n        // prepare for replicating the chart\n        options.series = [];\n        chart.series.forEach(function (serie) {\n            seriesOptions = merge(serie.userOptions, {\n                animation: false, // Turn off animation\n                enableMouseTracking: false,\n                showCheckbox: false,\n                visible: serie.visible\n            });\n            // Used for the navigator series that has its own option set\n            if (!seriesOptions.isInternal) {\n                options.series.push(seriesOptions);\n            }\n        });\n        const colls = {};\n        chart.axes.forEach(function (axis) {\n            // Assign an internal key to ensure a one-to-one mapping (#5924)\n            if (!axis.userOptions.internalKey) { // #6444\n                axis.userOptions.internalKey = uniqueKey();\n            }\n            if (!axis.options.isInternal) {\n                if (!colls[axis.coll]) {\n                    colls[axis.coll] = true;\n                    options[axis.coll] = [];\n                }\n                options[axis.coll].push(merge(axis.userOptions, {\n                    visible: axis.visible,\n                    // Force some options that could have be set directly on\n                    // the axis while missing in the userOptions or options.\n                    type: axis.type,\n                    uniqueNames: axis.uniqueNames\n                }));\n            }\n        });\n        // Make sure the `colorAxis` object of the `defaultOptions` isn't used\n        // in the chart copy's user options, because a color axis should only be\n        // added when the user actually applies it.\n        options.colorAxis = chart.userOptions.colorAxis;\n        // Generate the chart copy\n        const chartCopy = new chart.constructor(options, chart.callback);\n        // Axis options and series options  (#2022, #3900, #5982)\n        if (chartOptions) {\n            ['xAxis', 'yAxis', 'series'].forEach(function (coll) {\n                const collOptions = {};\n                if (chartOptions[coll]) {\n                    collOptions[coll] = chartOptions[coll];\n                    chartCopy.update(collOptions);\n                }\n            });\n        }\n        // Reflect axis extremes in the export (#5924)\n        chart.axes.forEach(function (axis) {\n            const axisCopy = find(chartCopy.axes, (copy) => copy.options.internalKey === axis.userOptions.internalKey);\n            if (axisCopy) {\n                const extremes = axis.getExtremes(), \n                // Make sure min and max overrides in the\n                // `exporting.chartOptions.xAxis` settings are reflected.\n                // These should override user-set extremes via zooming,\n                // scrollbar etc (#7873).\n                exportOverride = splat(chartOptions?.[axis.coll] || {})[0], userMin = 'min' in exportOverride ?\n                    exportOverride.min :\n                    extremes.userMin, userMax = 'max' in exportOverride ?\n                    exportOverride.max :\n                    extremes.userMax;\n                if (((typeof userMin !== 'undefined' &&\n                    userMin !== axisCopy.min) || (typeof userMax !== 'undefined' &&\n                    userMax !== axisCopy.max))) {\n                    axisCopy.setExtremes(userMin ?? void 0, userMax ?? void 0, true, false);\n                }\n            }\n        });\n        // Get the SVG from the container's innerHTML\n        svg = chartCopy.getChartHTML(chart.styledMode ||\n            options.exporting?.applyStyleSheets);\n        Exporting_fireEvent(this, 'getSVG', { chartCopy: chartCopy });\n        svg = chart.sanitizeSVG(svg, options);\n        // Free up memory\n        options = null;\n        chartCopy.destroy();\n        discardElement(sandbox);\n        return svg;\n    }\n    /**\n     * @private\n     * @function Highcharts.Chart#getSVGForExport\n     */\n    function getSVGForExport(options, chartOptions) {\n        const chartExportingOptions = this.options.exporting;\n        return this.getSVG(merge({ chart: { borderRadius: 0 } }, chartExportingOptions.chartOptions, chartOptions, {\n            exporting: {\n                sourceWidth: ((options && options.sourceWidth) ||\n                    chartExportingOptions.sourceWidth),\n                sourceHeight: ((options && options.sourceHeight) ||\n                    chartExportingOptions.sourceHeight)\n            }\n        }));\n    }\n    /**\n     * Make hyphenated property names out of camelCase\n     * @private\n     * @param {string} prop\n     * Property name in camelCase\n     * @return {string}\n     * Hyphenated property name\n     */\n    function hyphenate(prop) {\n        return prop.replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n        });\n    }\n    /**\n     * Analyze inherited styles from stylesheets and add them inline\n     *\n     * @private\n     * @function Highcharts.Chart#inlineStyles\n     *\n     * @todo What are the border styles for text about? In general, text has a\n     *       lot of properties.\n     *\n     * @todo Make it work with IE9 and IE10.\n     *\n     * @requires modules/exporting\n     */\n    function inlineStyles() {\n        const denylist = inlineDenylist, allowlist = Exporting.inlineAllowlist, // For IE\n        defaultStyles = {};\n        let dummySVG;\n        // Create an iframe where we read default styles without pollution from\n        // this body\n        const iframe = Exporting_doc.createElement('iframe');\n        css(iframe, {\n            width: '1px',\n            height: '1px',\n            visibility: 'hidden'\n        });\n        Exporting_doc.body.appendChild(iframe);\n        const iframeDoc = (iframe.contentWindow && iframe.contentWindow.document);\n        if (iframeDoc) {\n            iframeDoc.body.appendChild(iframeDoc.createElementNS(SVG_NS, 'svg'));\n        }\n        /**\n         * Call this on all elements and recurse to children\n         * @private\n         * @param {Highcharts.HTMLDOMElement} node\n         *        Element child\n             */\n        function recurse(node) {\n            const filteredStyles = {};\n            let styles, parentStyles, dummy, denylisted, allowlisted, i;\n            /**\n             * Check computed styles and whether they are in the allow/denylist\n             * for styles or attributes.\n             * @private\n             * @param {string} val\n             *        Style value\n             * @param {string} prop\n             *        Style property name\n                     */\n            function filterStyles(val, prop) {\n                // Check against allowlist & denylist\n                denylisted = allowlisted = false;\n                if (allowlist.length) {\n                    // Styled mode in IE has a allowlist instead. Exclude all\n                    // props not in this list.\n                    i = allowlist.length;\n                    while (i-- && !allowlisted) {\n                        allowlisted = allowlist[i].test(prop);\n                    }\n                    denylisted = !allowlisted;\n                }\n                // Explicitly remove empty transforms\n                if (prop === 'transform' && val === 'none') {\n                    denylisted = true;\n                }\n                i = denylist.length;\n                while (i-- && !denylisted) {\n                    if (prop.length > 1000 /* RegexLimits.shortLimit */) {\n                        throw new Error('Input too long');\n                    }\n                    denylisted = (denylist[i].test(prop) ||\n                        typeof val === 'function');\n                }\n                if (!denylisted) {\n                    // If parent node has the same style, it gets inherited, no\n                    // need to inline it. Top-level props should be diffed\n                    // against parent (#7687).\n                    if ((parentStyles[prop] !== val ||\n                        node.nodeName === 'svg') &&\n                        defaultStyles[node.nodeName][prop] !== val) {\n                        // Attributes\n                        if (!inlineToAttributes ||\n                            inlineToAttributes.indexOf(prop) !== -1) {\n                            if (val) {\n                                node.setAttribute(hyphenate(prop), val);\n                            }\n                            // Styles\n                        }\n                        else {\n                            filteredStyles[prop] = val;\n                        }\n                    }\n                }\n            }\n            if (iframeDoc &&\n                node.nodeType === 1 &&\n                unstyledElements.indexOf(node.nodeName) === -1) {\n                styles = Exporting_win.getComputedStyle(node, null);\n                parentStyles = node.nodeName === 'svg' ?\n                    {} :\n                    Exporting_win.getComputedStyle(node.parentNode, null);\n                // Get default styles from the browser so that we don't have to\n                // add these\n                if (!defaultStyles[node.nodeName]) {\n                    /*\n                    If (!dummySVG) {\n                        dummySVG = doc.createElementNS(H.SVG_NS, 'svg');\n                        dummySVG.setAttribute('version', '1.1');\n                        doc.body.appendChild(dummySVG);\n                    }\n                    */\n                    dummySVG = iframeDoc.getElementsByTagName('svg')[0];\n                    dummy = iframeDoc.createElementNS(node.namespaceURI, node.nodeName);\n                    dummySVG.appendChild(dummy);\n                    // Get the defaults into a standard object (simple merge\n                    // won't do)\n                    const s = Exporting_win.getComputedStyle(dummy, null), defaults = {};\n                    for (const key in s) {\n                        if (key.length < 1000 /* RegexLimits.shortLimit */ &&\n                            typeof s[key] === 'string' &&\n                            !/^\\d+$/.test(key)) {\n                            defaults[key] = s[key];\n                        }\n                    }\n                    defaultStyles[node.nodeName] = defaults;\n                    // Remove default fill, otherwise text disappears when\n                    // exported\n                    if (node.nodeName === 'text') {\n                        delete defaultStyles.text.fill;\n                    }\n                    dummySVG.removeChild(dummy);\n                }\n                // Loop through all styles and add them inline if they are ok\n                for (const p in styles) {\n                    if (\n                    // Some browsers put lots of styles on the prototype...\n                    (external_highcharts_src_js_default_default()).isFirefox ||\n                        (external_highcharts_src_js_default_default()).isMS ||\n                        (external_highcharts_src_js_default_default()).isSafari || // #16902\n                        // ... Chrome puts them on the instance\n                        Object.hasOwnProperty.call(styles, p)) {\n                        filterStyles(styles[p], p);\n                    }\n                }\n                // Apply styles\n                css(node, filteredStyles);\n                // Set default stroke width (needed at least for IE)\n                if (node.nodeName === 'svg') {\n                    node.setAttribute('stroke-width', '1px');\n                }\n                if (node.nodeName === 'text') {\n                    return;\n                }\n                // Recurse\n                [].forEach.call(node.children || node.childNodes, recurse);\n            }\n        }\n        /**\n         * Remove the dummy objects used to get defaults\n         * @private\n         */\n        function tearDown() {\n            dummySVG.parentNode.removeChild(dummySVG);\n            // Remove trash from DOM that stayed after each exporting\n            iframe.parentNode.removeChild(iframe);\n        }\n        recurse(this.container.querySelector('svg'));\n        tearDown();\n    }\n    /**\n     * Resolve CSS variables into hex colors\n     */\n    function resolveCSSVariables() {\n        const svgElements = this.container.querySelectorAll('*'), colorAttributes = ['color', 'fill', 'stop-color', 'stroke'];\n        Array.from(svgElements).forEach((element) => {\n            colorAttributes.forEach((attr) => {\n                const attrValue = element.getAttribute(attr);\n                if (attrValue?.includes('var(')) {\n                    element.setAttribute(attr, getComputedStyle(element).getPropertyValue(attr));\n                }\n            });\n        });\n    }\n    /**\n     * Move the chart container(s) to another div.\n     *\n     * @function Highcharts#moveContainers\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} moveTo\n     *        Move target\n     */\n    function moveContainers(moveTo) {\n        const { scrollablePlotArea } = this;\n        (\n        // When scrollablePlotArea is active (#9533)\n        scrollablePlotArea ?\n            [\n                scrollablePlotArea.fixedDiv,\n                scrollablePlotArea.scrollingContainer\n            ] :\n            [this.container]).forEach(function (div) {\n            moveTo.appendChild(div);\n        });\n    }\n    /**\n     * Add update methods to handle chart.update and chart.exporting.update and\n     * chart.navigation.update. These must be added to the chart instance rather\n     * than the Chart prototype in order to use the chart instance inside the\n     * update function.\n     * @private\n     */\n    function onChartInit() {\n        const chart = this, \n        /**\n         * @private\n         * @param {\"exporting\"|\"navigation\"} prop\n         *        Property name in option root\n         * @param {Highcharts.ExportingOptions|Highcharts.NavigationOptions} options\n         *        Options to update\n         * @param {boolean} [redraw=true]\n         *        Whether to redraw\n                 */\n        update = (prop, options, redraw) => {\n            chart.isDirtyExporting = true;\n            merge(true, chart.options[prop], options);\n            if (pick(redraw, true)) {\n                chart.redraw();\n            }\n        };\n        chart.exporting = {\n            update: function (options, redraw) {\n                update('exporting', options, redraw);\n            }\n        };\n        // Register update() method for navigation. Cannot be set the same way\n        // as for exporting, because navigation options are shared with bindings\n        // which has separate update() logic.\n        Chart_ChartNavigationComposition\n            .compose(chart).navigation\n            .addUpdate((options, redraw) => {\n            update('navigation', options, redraw);\n        });\n    }\n    /**\n     * On layout of titles (title, subtitle and caption), adjust the `alignTo``\n     * box to avoid the context menu button.\n     * @private\n     */\n    function onChartLayOutTitle({ alignTo, key, textPxLength }) {\n        const exportingOptions = this.options.exporting, { align, buttonSpacing = 0, verticalAlign, width = 0 } = merge(this.options.navigation?.buttonOptions, exportingOptions?.buttons?.contextButton), space = alignTo.width - textPxLength, widthAdjust = width + buttonSpacing;\n        if ((exportingOptions?.enabled ?? true) &&\n            key === 'title' &&\n            align === 'right' &&\n            verticalAlign === 'top') {\n            if (space < 2 * widthAdjust) {\n                if (space < widthAdjust) {\n                    alignTo.width -= widthAdjust;\n                }\n                else if (this.title?.alignValue !== 'left') {\n                    alignTo.x -= widthAdjust - space / 2;\n                }\n            }\n        }\n    }\n    /**\n     * Exporting module required. Clears away other elements in the page and\n     * prints the chart as it is displayed. By default, when the exporting\n     * module is enabled, a context button with a drop down menu in the upper\n     * right corner accesses this function.\n     *\n     * @sample highcharts/members/chart-print/\n     *         Print from a HTML button\n     *\n     * @function Highcharts.Chart#print\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     * @emits Highcharts.Chart#event:afterPrint\n     *\n     * @requires modules/exporting\n     */\n    function print() {\n        const chart = this;\n        if (chart.isPrinting) { // Block the button while in printing mode\n            return;\n        }\n        printingChart = chart;\n        if (!(external_highcharts_src_js_default_default()).isSafari) {\n            chart.beforePrint();\n        }\n        // Give the browser time to draw WebGL content, an issue that randomly\n        // appears (at least) in Chrome ~67 on the Mac (#8708).\n        setTimeout(() => {\n            Exporting_win.focus(); // #1510\n            Exporting_win.print();\n            // Allow the browser to prepare before reverting\n            if (!(external_highcharts_src_js_default_default()).isSafari) {\n                setTimeout(() => {\n                    chart.afterPrint();\n                }, 1000);\n            }\n        }, 1);\n    }\n    /**\n     * Add the buttons on chart load\n     * @private\n     * @function Highcharts.Chart#renderExporting\n     * @requires modules/exporting\n     */\n    function renderExporting() {\n        const chart = this, exportingOptions = chart.options.exporting, buttons = exportingOptions.buttons, isDirty = chart.isDirtyExporting || !chart.exportSVGElements;\n        chart.buttonOffset = 0;\n        if (chart.isDirtyExporting) {\n            chart.destroyExport();\n        }\n        if (isDirty && exportingOptions.enabled !== false) {\n            chart.exportEvents = [];\n            chart.exportingGroup = chart.exportingGroup ||\n                chart.renderer.g('exporting-group').attr({\n                    zIndex: 3 // #4955, // #8392\n                }).add();\n            objectEach(buttons, function (button) {\n                chart.addButton(button);\n            });\n            chart.isDirtyExporting = false;\n        }\n    }\n    /**\n     * Exporting module only. A collection of fixes on the produced SVG to\n     * account for expand properties, browser bugs.\n     * Returns a cleaned SVG.\n     *\n     * @private\n     * @function Highcharts.Chart#sanitizeSVG\n     * @param {string} svg\n     *        SVG code to sanitize\n     * @param {Highcharts.Options} options\n     *        Chart options to apply\n     * @return {string}\n     *         Sanitized SVG code\n     * @requires modules/exporting\n     */\n    function sanitizeSVG(svg, options) {\n        const split = svg.indexOf('</svg>') + 6, useForeignObject = svg.indexOf('<foreignObject') > -1;\n        let html = svg.substr(split);\n        // Remove any HTML added to the container after the SVG (#894, #9087)\n        svg = svg.substr(0, split);\n        if (useForeignObject) {\n            // Some tags needs to be closed in xhtml (#13726)\n            svg = svg.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />');\n            // Move HTML into a foreignObject\n        }\n        else if (html && options?.exporting?.allowHTML) {\n            html = '<foreignObject x=\"0\" y=\"0\" ' +\n                'width=\"' + options.chart.width + '\" ' +\n                'height=\"' + options.chart.height + '\">' +\n                '<body xmlns=\"http://www.w3.org/1999/xhtml\">' +\n                // Some tags needs to be closed in xhtml (#13726)\n                html.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />') +\n                '</body>' +\n                '</foreignObject>';\n            svg = svg.replace('</svg>', html + '</svg>');\n        }\n        svg = svg\n            .replace(/zIndex=\"[^\"]+\"/g, '')\n            .replace(/symbolName=\"[^\"]+\"/g, '')\n            .replace(/jQuery\\d+=\"[^\"]+\"/g, '')\n            .replace(/url\\((\"|&quot;)(.*?)(\"|&quot;)\\;?\\)/g, 'url($2)')\n            .replace(/url\\([^#]+#/g, 'url(#')\n            .replace(/<svg /, '<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" ')\n            .replace(/ (NS\\d+\\:)?href=/g, ' xlink:href=') // #3567\n            .replace(/\\n+/g, ' ')\n            // Replace HTML entities, issue #347\n            .replace(/&nbsp;/g, '\\u00A0') // No-break space\n            .replace(/&shy;/g, '\\u00AD'); // Soft hyphen\n        return svg;\n    }\n})(Exporting || (Exporting = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Exporting_Exporting = (Exporting);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired after a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingAfterPrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired before a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingBeforePrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Function to call if the offline-exporting module fails to export a chart on\n * the client side.\n *\n * @callback Highcharts.ExportingErrorCallbackFunction\n *\n * @param {Highcharts.ExportingOptions} options\n *        The exporting options.\n *\n * @param {global.Error} err\n *        The error from the module.\n */\n/**\n * Definition for a menu item in the context menu.\n *\n * @interface Highcharts.ExportingMenuObject\n */ /**\n* The text for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#text\n* @type {string|undefined}\n*/ /**\n* If internationalization is required, the key to a language string.\n*\n* @name Highcharts.ExportingMenuObject#textKey\n* @type {string|undefined}\n*/ /**\n* The click handler for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#onclick\n* @type {Highcharts.EventCallbackFunction<Highcharts.Chart>|undefined}\n*/ /**\n* Indicates a separator line instead of an item.\n*\n* @name Highcharts.ExportingMenuObject#separator\n* @type {boolean|undefined}\n*/\n/**\n * Possible MIME types for exporting.\n *\n * @typedef {\"image/png\"|\"image/jpeg\"|\"application/pdf\"|\"image/svg+xml\"} Highcharts.ExportingMimeTypeValue\n */\n(''); // Keeps doclets above in transpiled file\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after a chart is printed through the context menu item or the\n * `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingAfterPrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.afterPrint\n */\n/**\n * Fires before a chart is printed through the context menu item or\n * the `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingBeforePrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.beforePrint\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es-modules/Extensions/OfflineExporting/OfflineExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n * Declarations\n *\n * */\nconst OfflineExportingDefaults = {\n    libURL: 'https://code.highcharts.com/12.2.0/lib/',\n    // When offline-exporting is loaded, redefine the menu item definitions\n    // related to download.\n    menuItemDefinitions: {\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: function () {\n                this.exportChartLocal();\n            }\n        },\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: function () {\n                this.exportChartLocal({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: function () {\n                this.exportChartLocal({\n                    type: 'image/svg+xml'\n                });\n            }\n        },\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: function () {\n                this.exportChartLocal({\n                    type: 'application/pdf'\n                });\n            }\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const OfflineExporting_OfflineExportingDefaults = (OfflineExportingDefaults);\n\n;// ./code/es-modules/Extensions/OfflineExporting/OfflineExporting.js\n/* *\n *\n *  Client side exporting module\n *\n *  (c) 2015 Torstein Honsi / Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { defaultOptions: OfflineExporting_defaultOptions } = (external_highcharts_src_js_default_default());\n\nconst { downloadURL: OfflineExporting_downloadURL } = Extensions_DownloadURL;\n\n\nconst { doc: OfflineExporting_doc, win: OfflineExporting_win } = (external_highcharts_src_js_default_default());\n\nconst { ajax } = (external_data_src_js_default_HttpUtilities_default());\n\n\nconst { addEvent: OfflineExporting_addEvent, error, extend: OfflineExporting_extend, fireEvent: OfflineExporting_fireEvent, merge: OfflineExporting_merge } = (external_highcharts_src_js_default_default());\nexternal_highcharts_src_js_default_AST_default().allowedAttributes.push('data-z-index', 'fill-opacity', 'filter', 'rx', 'ry', 'stroke-dasharray', 'stroke-linejoin', 'stroke-opacity', 'text-anchor', 'transform', 'version', 'viewBox', 'visibility', 'xmlns', 'xmlns:xlink');\nexternal_highcharts_src_js_default_AST_default().allowedTags.push('desc', 'clippath', 'g');\n/* *\n *\n *  Composition\n *\n * */\nvar OfflineExporting;\n(function (OfflineExporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // Dummy object so we can reuse our canvas-tools.js without errors\n    OfflineExporting.CanVGRenderer = {}, OfflineExporting.domurl = OfflineExporting_win.URL || OfflineExporting_win.webkitURL || OfflineExporting_win, \n    // Milliseconds to defer image load event handlers to offset IE bug\n    OfflineExporting.loadEventDeferDelay = (external_highcharts_src_js_default_default()).isMS ? 150 : 0;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Extends OfflineExporting with Chart.\n     * @private\n     */\n    function compose(ChartClass) {\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.exportChartLocal) {\n            chartProto.getSVGForLocalExport = getSVGForLocalExport;\n            chartProto.exportChartLocal = exportChartLocal;\n            // Extend the default options to use the local exporter logic\n            OfflineExporting_merge(true, OfflineExporting_defaultOptions.exporting, OfflineExporting_OfflineExportingDefaults);\n        }\n        return ChartClass;\n    }\n    OfflineExporting.compose = compose;\n    /**\n     * Get data URL to an image of an SVG and call download on it options\n     * object:\n     * - **filename:** Name of resulting downloaded file without extension.\n     * Default is `chart`.\n     *\n     * - **type:** File type of resulting download. Default is `image/png`.\n     *\n     * - **scale:** Scaling factor of downloaded image compared to source.\n     * Default is `1`.\n     * - **libURL:** URL pointing to location of dependency scripts to download\n     * on demand. Default is the exporting.libURL option of the global\n     * Highcharts options pointing to our server.\n     *\n     * @function Highcharts.downloadSVGLocal\n     *\n     * @param {string} svg\n     * The generated SVG\n     *\n     * @param {Highcharts.ExportingOptions} options\n     * The exporting options\n     *\n     * @param {Function} failCallback\n     * The callback function in case of errors\n     *\n     * @param {Function} [successCallback]\n     * The callback function in case of success\n     *\n     */\n    function downloadSVGLocal(svg, options, failCallback, successCallback) {\n        const dummySVGContainer = OfflineExporting_doc.createElement('div'), imageType = options.type || 'image/png', filename = ((options.filename || 'chart') +\n            '.' +\n            (imageType === 'image/svg+xml' ?\n                'svg' : imageType.split('/')[1])), scale = options.scale || 1;\n        let svgurl, blob, finallyHandler, libURL = (options.libURL || OfflineExporting_defaultOptions.exporting.libURL), objectURLRevoke = true, pdfFont = options.pdfFont;\n        // Allow libURL to end with or without fordward slash\n        libURL = libURL.slice(-1) !== '/' ? libURL + '/' : libURL;\n        /*\n         * Detect if we need to load TTF fonts for the PDF, then load them and\n         * proceed.\n         *\n         * @private\n         */\n        const loadPdfFonts = (svgElement, callback) => {\n            const hasNonASCII = (s) => (\n            // eslint-disable-next-line no-control-regex\n            /[^\\u0000-\\u007F\\u200B]+/.test(s));\n            // Register an event in order to add the font once jsPDF is\n            // initialized\n            const addFont = (variant, base64) => {\n                OfflineExporting_win.jspdf.jsPDF.API.events.push([\n                    'initialized',\n                    function () {\n                        this.addFileToVFS(variant, base64);\n                        this.addFont(variant, 'HighchartsFont', variant);\n                        if (!this.getFontList().HighchartsFont) {\n                            this.setFont('HighchartsFont');\n                        }\n                    }\n                ]);\n            };\n            // If there are no non-ASCII characters in the SVG, do not use\n            // bother downloading the font files\n            if (pdfFont && !hasNonASCII(svgElement.textContent || '')) {\n                pdfFont = void 0;\n            }\n            // Add new font if the URL is declared, #6417.\n            const variants = ['normal', 'italic', 'bold', 'bolditalic'];\n            // Shift the first element off the variants and add as a font.\n            // Then asynchronously trigger the next variant until calling the\n            // callback when the variants are empty.\n            let normalBase64;\n            const shiftAndLoadVariant = () => {\n                const variant = variants.shift();\n                // All variants shifted and possibly loaded, proceed\n                if (!variant) {\n                    return callback();\n                }\n                const url = pdfFont && pdfFont[variant];\n                if (url) {\n                    ajax({\n                        url,\n                        responseType: 'blob',\n                        success: (data, xhr) => {\n                            const reader = new FileReader();\n                            reader.onloadend = function () {\n                                if (typeof this.result === 'string') {\n                                    const base64 = this.result.split(',')[1];\n                                    addFont(variant, base64);\n                                    if (variant === 'normal') {\n                                        normalBase64 = base64;\n                                    }\n                                }\n                                shiftAndLoadVariant();\n                            };\n                            reader.readAsDataURL(xhr.response);\n                        },\n                        error: shiftAndLoadVariant\n                    });\n                }\n                else {\n                    // For other variants, fall back to normal text weight/style\n                    if (normalBase64) {\n                        addFont(variant, normalBase64);\n                    }\n                    shiftAndLoadVariant();\n                }\n            };\n            shiftAndLoadVariant();\n        };\n        /*\n         * @private\n         */\n        const downloadPDF = () => {\n            external_highcharts_src_js_default_AST_default().setElementHTML(dummySVGContainer, svg);\n            const textElements = dummySVGContainer.getElementsByTagName('text'), \n            // Copy style property to element from parents if it's not\n            // there. Searches up hierarchy until it finds prop, or hits the\n            // chart container.\n            setStylePropertyFromParents = function (el, propName) {\n                let curParent = el;\n                while (curParent && curParent !== dummySVGContainer) {\n                    if (curParent.style[propName]) {\n                        let value = curParent.style[propName];\n                        if (propName === 'fontSize' && /em$/.test(value)) {\n                            value = Math.round(parseFloat(value) * 16) + 'px';\n                        }\n                        el.style[propName] = value;\n                        break;\n                    }\n                    curParent = curParent.parentNode;\n                }\n            };\n            let titleElements, outlineElements;\n            // Workaround for the text styling. Making sure it does pick up\n            // settings for parent elements.\n            [].forEach.call(textElements, function (el) {\n                // Workaround for the text styling. making sure it does pick up\n                // the root element\n                ['fontFamily', 'fontSize']\n                    .forEach((property) => {\n                    setStylePropertyFromParents(el, property);\n                });\n                el.style.fontFamily = pdfFont && pdfFont.normal ?\n                    // Custom PDF font\n                    'HighchartsFont' :\n                    // Generic font (serif, sans-serif etc)\n                    String(el.style.fontFamily &&\n                        el.style.fontFamily.split(' ').splice(-1));\n                // Workaround for plotband with width, removing title from text\n                // nodes\n                titleElements = el.getElementsByTagName('title');\n                [].forEach.call(titleElements, function (titleElement) {\n                    el.removeChild(titleElement);\n                });\n                // Remove all .highcharts-text-outline elements, #17170\n                outlineElements =\n                    el.getElementsByClassName('highcharts-text-outline');\n                while (outlineElements.length > 0) {\n                    const outline = outlineElements[0];\n                    if (outline.parentNode) {\n                        outline.parentNode.removeChild(outline);\n                    }\n                }\n            });\n            const svgNode = dummySVGContainer.querySelector('svg');\n            if (svgNode) {\n                loadPdfFonts(svgNode, () => {\n                    svgToPdf(svgNode, 0, scale, (pdfData) => {\n                        try {\n                            OfflineExporting_downloadURL(pdfData, filename);\n                            if (successCallback) {\n                                successCallback();\n                            }\n                        }\n                        catch (e) {\n                            failCallback(e);\n                        }\n                    });\n                });\n            }\n        };\n        // Initiate download depending on file type\n        if (imageType === 'image/svg+xml') {\n            // SVG download. In this case, we want to use Microsoft specific\n            // Blob if available\n            try {\n                if (typeof OfflineExporting_win.MSBlobBuilder !== 'undefined') {\n                    blob = new OfflineExporting_win.MSBlobBuilder();\n                    blob.append(svg);\n                    svgurl = blob.getBlob('image/svg+xml');\n                }\n                else {\n                    svgurl = svgToDataUrl(svg);\n                }\n                OfflineExporting_downloadURL(svgurl, filename);\n                if (successCallback) {\n                    successCallback();\n                }\n            }\n            catch (e) {\n                failCallback(e);\n            }\n        }\n        else if (imageType === 'application/pdf') {\n            if (OfflineExporting_win.jspdf && OfflineExporting_win.jspdf.jsPDF) {\n                downloadPDF();\n            }\n            else {\n                // Must load pdf libraries first. // Don't destroy the object\n                // URL yet since we are doing things asynchronously. A cleaner\n                // solution would be nice, but this will do for now.\n                objectURLRevoke = true;\n                getScript(libURL + 'jspdf.js', function () {\n                    getScript(libURL + 'svg2pdf.js', downloadPDF);\n                });\n            }\n        }\n        else {\n            // PNG/JPEG download - create bitmap from SVG\n            svgurl = svgToDataUrl(svg);\n            finallyHandler = function () {\n                try {\n                    OfflineExporting.domurl.revokeObjectURL(svgurl);\n                }\n                catch (e) {\n                    // Ignore\n                }\n            };\n            // First, try to get PNG by rendering on canvas\n            imageToDataUrl(svgurl, imageType, {}, scale, function (imageURL) {\n                // Success\n                try {\n                    OfflineExporting_downloadURL(imageURL, filename);\n                    if (successCallback) {\n                        successCallback();\n                    }\n                }\n                catch (e) {\n                    failCallback(e);\n                }\n            }, function () {\n                if (svg.length > 100000000 /* RegexLimits.svgLimit */) {\n                    throw new Error('Input too long');\n                }\n                // Failed due to tainted canvas\n                // Create new and untainted canvas\n                const canvas = OfflineExporting_doc.createElement('canvas'), ctx = canvas.getContext('2d'), matchedImageWidth = svg.match(\n                // eslint-disable-next-line max-len\n                /^<svg[^>]*\\s{,1000}width\\s{,1000}=\\s{,1000}\\\"?(\\d+)\\\"?[^>]*>/), matchedImageHeight = svg.match(\n                // eslint-disable-next-line max-len\n                /^<svg[^>]*\\s{0,1000}height\\s{,1000}=\\s{,1000}\\\"?(\\d+)\\\"?[^>]*>/);\n                if (ctx && matchedImageWidth && matchedImageHeight) {\n                    const imageWidth = +matchedImageWidth[1] * scale, imageHeight = +matchedImageHeight[1] * scale, downloadWithCanVG = () => {\n                        const v = OfflineExporting_win.canvg.Canvg.fromString(ctx, svg);\n                        v.start();\n                        try {\n                            OfflineExporting_downloadURL(OfflineExporting_win.navigator.msSaveOrOpenBlob ?\n                                canvas.msToBlob() :\n                                canvas.toDataURL(imageType), filename);\n                            if (successCallback) {\n                                successCallback();\n                            }\n                        }\n                        catch (e) {\n                            failCallback(e);\n                        }\n                        finally {\n                            finallyHandler();\n                        }\n                    };\n                    canvas.width = imageWidth;\n                    canvas.height = imageHeight;\n                    if (OfflineExporting_win.canvg) {\n                        // Use preloaded canvg\n                        downloadWithCanVG();\n                    }\n                    else {\n                        // Must load canVG first.\n                        // Don't destroy the object URL yet since we are\n                        // doing things asynchronously. A cleaner solution\n                        // would be nice, but this will do for now.\n                        objectURLRevoke = true;\n                        getScript(libURL + 'canvg.js', downloadWithCanVG);\n                    }\n                }\n            }, \n            // No canvas support\n            failCallback, \n            // Failed to load image\n            failCallback, \n            // Finally\n            function () {\n                if (objectURLRevoke) {\n                    finallyHandler();\n                }\n            });\n        }\n    }\n    OfflineExporting.downloadSVGLocal = downloadSVGLocal;\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Exporting and offline-exporting modules required. Export a chart to\n     * an image locally in the user's browser.\n     *\n     * @function Highcharts.Chart#exportChartLocal\n     *\n     * @param  {Highcharts.ExportingOptions} [exportingOptions]\n     *         Exporting options, the same as in\n     *         {@link Highcharts.Chart#exportChart}.\n     *\n     * @param  {Highcharts.Options} [chartOptions]\n     *         Additional chart options for the exported chart. For example\n     *         a different background color can be added here, or\n     *         `dataLabels` for export only.\n     *\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    function exportChartLocal(exportingOptions, chartOptions) {\n        const chart = this, options = OfflineExporting_merge(chart.options.exporting, exportingOptions), fallbackToExportServer = function (err) {\n            if (options.fallbackToExportServer === false) {\n                if (options.error) {\n                    options.error(options, err);\n                }\n                else {\n                    error(28, true); // Fallback disabled\n                }\n            }\n            else {\n                chart.exportChart(options);\n            }\n        }, svgSuccess = function (svg) {\n            // If SVG contains foreignObjects PDF fails in all browsers\n            // and all exports except SVG will fail in IE, as both CanVG\n            // and svg2pdf choke on this. Gracefully fall back.\n            if (svg.indexOf('<foreignObject') > -1 &&\n                options.type !== 'image/svg+xml' &&\n                ((external_highcharts_src_js_default_default()).isMS || options.type === 'application/pdf')) {\n                fallbackToExportServer(new Error('Image type not supported for charts with embedded HTML'));\n            }\n            else {\n                OfflineExporting.downloadSVGLocal(svg, OfflineExporting_extend({ filename: chart.getFilename() }, options), fallbackToExportServer, () => OfflineExporting_fireEvent(chart, 'exportChartLocalSuccess'));\n            }\n        }, \n        // Return true if the SVG contains images with external data. With\n        // the boost module there are `image` elements with encoded PNGs,\n        // these are supported by svg2pdf and should pass (#10243).\n        hasExternalImages = function () {\n            return [].some.call(chart.container.getElementsByTagName('image'), function (image) {\n                const href = image.getAttribute('href');\n                return (href !== '' &&\n                    typeof href === 'string' &&\n                    href.indexOf('data:') !== 0);\n            });\n        };\n        // If we are on IE and in styled mode, add an allowlist to the renderer\n        // for inline styles that we want to pass through. There are so many\n        // styles by default in IE that we don't want to denylist them all.\n        if ((external_highcharts_src_js_default_default()).isMS && chart.styledMode && !Exporting_Exporting.inlineAllowlist.length) {\n            Exporting_Exporting.inlineAllowlist.push(/^blockSize/, /^border/, /^caretColor/, /^color/, /^columnRule/, /^columnRuleColor/, /^cssFloat/, /^cursor/, /^fill$/, /^fillOpacity/, /^font/, /^inlineSize/, /^length/, /^lineHeight/, /^opacity/, /^outline/, /^parentRule/, /^rx$/, /^ry$/, /^stroke/, /^textAlign/, /^textAnchor/, /^textDecoration/, /^transform/, /^vectorEffect/, /^visibility/, /^x$/, /^y$/);\n        }\n        // Always fall back on:\n        // - MS browsers: Embedded images JPEG/PNG, or any PDF\n        // - Embedded images and PDF\n        if (((external_highcharts_src_js_default_default()).isMS &&\n            (options.type === 'application/pdf' ||\n                chart.container.getElementsByTagName('image').length &&\n                    options.type !== 'image/svg+xml')) || (options.type === 'application/pdf' &&\n            hasExternalImages())) {\n            fallbackToExportServer(new Error('Image type not supported for this chart/browser.'));\n            return;\n        }\n        chart.getSVGForLocalExport(options, chartOptions || {}, fallbackToExportServer, svgSuccess);\n    }\n    /**\n     * Downloads a script and executes a callback when done.\n     *\n     * @private\n     * @function getScript\n     * @param {string} scriptLocation\n     * @param {Function} callback\n     */\n    function getScript(scriptLocation, callback) {\n        const head = OfflineExporting_doc.getElementsByTagName('head')[0], script = OfflineExporting_doc.createElement('script');\n        script.type = 'text/javascript';\n        script.src = scriptLocation;\n        script.onload = callback;\n        script.onerror = function () {\n            error('Error loading script ' + scriptLocation);\n        };\n        head.appendChild(script);\n    }\n    OfflineExporting.getScript = getScript;\n    /**\n     * Get SVG of chart prepared for client side export. This converts\n     * embedded images in the SVG to data URIs. It requires the regular\n     * exporting module. The options and chartOptions arguments are passed\n     * to the getSVGForExport function.\n     *\n     * @private\n     * @function Highcharts.Chart#getSVGForLocalExport\n     * @param {Highcharts.ExportingOptions} options\n     * @param {Highcharts.Options} chartOptions\n     * @param {Function} failCallback\n     * @param {Function} successCallback\n     */\n    function getSVGForLocalExport(options, chartOptions, failCallback, successCallback) {\n        const chart = this, \n        // After grabbing the SVG of the chart's copy container we need\n        // to do sanitation on the SVG\n        sanitize = (svg) => chart.sanitizeSVG(svg, chartCopyOptions), \n        // When done with last image we have our SVG\n        checkDone = () => {\n            if (images && imagesEmbedded === imagesLength) {\n                successCallback(sanitize(chartCopyContainer.innerHTML));\n            }\n        }, \n        // Success handler, we converted image to base64!\n        embeddedSuccess = (imageURL, imageType, callbackArgs) => {\n            ++imagesEmbedded;\n            // Change image href in chart copy\n            callbackArgs.imageElement.setAttributeNS('http://www.w3.org/1999/xlink', 'href', imageURL);\n            checkDone();\n        };\n        let el, chartCopyContainer, chartCopyOptions, href = null, images, imagesLength = 0, imagesEmbedded = 0;\n        // Hook into getSVG to get a copy of the chart copy's\n        // container (#8273)\n        chart.unbindGetSVG = OfflineExporting_addEvent(chart, 'getSVG', (e) => {\n            chartCopyOptions = e.chartCopy.options;\n            chartCopyContainer = e.chartCopy.container.cloneNode(true);\n            images = chartCopyContainer && chartCopyContainer\n                .getElementsByTagName('image') || [];\n            imagesLength = images.length;\n        });\n        // Trigger hook to get chart copy\n        chart.getSVGForExport(options, chartOptions);\n        try {\n            // If there are no images to embed, the SVG is okay now.\n            if (!images || !images.length) {\n                // Use SVG of chart copy\n                successCallback(sanitize(chartCopyContainer.innerHTML));\n                return;\n            }\n            // Go through the images we want to embed\n            for (let i = 0; i < images.length; i++) {\n                el = images[i];\n                href = el.getAttributeNS('http://www.w3.org/1999/xlink', 'href');\n                if (href) {\n                    OfflineExporting.imageToDataUrl(href, 'image/png', { imageElement: el }, options.scale, embeddedSuccess, \n                    // Tainted canvas\n                    failCallback, \n                    // No canvas support\n                    failCallback, \n                    // Failed to load source\n                    failCallback);\n                    // Hidden, boosted series have blank href (#10243)\n                }\n                else {\n                    imagesEmbedded++;\n                    el.parentNode.removeChild(el);\n                    i--;\n                    checkDone();\n                }\n            }\n        }\n        catch (e) {\n            failCallback(e);\n        }\n        // Clean up\n        chart.unbindGetSVG();\n    }\n    /**\n     * Get data:URL from image URL. Pass in callbacks to handle results.\n     *\n     * @private\n     * @function Highcharts.imageToDataUrl\n     *\n     * @param {string} imageURL\n     *\n     * @param {string} imageType\n     *\n     * @param {*} callbackArgs\n     *        callbackArgs is used only by callbacks.\n     *\n     * @param {number} scale\n     *\n     * @param {Function} successCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} taintedCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} noCanvasSupportCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} failedLoadCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} [finallyCallback]\n     *        finallyCallback is always called at the end of the process. All\n     *        callbacks receive four arguments: imageURL, imageType,\n     *        callbackArgs, and scale.\n     */\n    function imageToDataUrl(imageURL, imageType, callbackArgs, scale, successCallback, taintedCallback, noCanvasSupportCallback, failedLoadCallback, finallyCallback) {\n        let img = new OfflineExporting_win.Image(), taintedHandler;\n        const loadHandler = () => {\n            setTimeout(function () {\n                const canvas = OfflineExporting_doc.createElement('canvas'), ctx = canvas.getContext && canvas.getContext('2d');\n                let dataURL;\n                try {\n                    if (!ctx) {\n                        noCanvasSupportCallback(imageURL, imageType, callbackArgs, scale);\n                    }\n                    else {\n                        canvas.height = img.height * scale;\n                        canvas.width = img.width * scale;\n                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n                        // Now we try to get the contents of the canvas.\n                        try {\n                            dataURL = canvas.toDataURL(imageType);\n                            successCallback(dataURL, imageType, callbackArgs, scale);\n                        }\n                        catch (e) {\n                            taintedHandler(imageURL, imageType, callbackArgs, scale);\n                        }\n                    }\n                }\n                finally {\n                    if (finallyCallback) {\n                        finallyCallback(imageURL, imageType, callbackArgs, scale);\n                    }\n                }\n                // IE bug where image is not always ready despite calling load\n                // event.\n            }, OfflineExporting.loadEventDeferDelay);\n        }, \n        // Image load failed (e.g. invalid URL)\n        errorHandler = () => {\n            failedLoadCallback(imageURL, imageType, callbackArgs, scale);\n            if (finallyCallback) {\n                finallyCallback(imageURL, imageType, callbackArgs, scale);\n            }\n        };\n        // This is called on load if the image drawing to canvas failed with a\n        // security error. We retry the drawing with crossOrigin set to\n        // Anonymous.\n        taintedHandler = () => {\n            img = new OfflineExporting_win.Image();\n            taintedHandler = taintedCallback;\n            // Must be set prior to loading image source\n            img.crossOrigin = 'Anonymous';\n            img.onload = loadHandler;\n            img.onerror = errorHandler;\n            img.src = imageURL;\n        };\n        img.onload = loadHandler;\n        img.onerror = errorHandler;\n        img.src = imageURL;\n    }\n    OfflineExporting.imageToDataUrl = imageToDataUrl;\n    /**\n     * Get blob URL from SVG code. Falls back to normal data URI.\n     *\n     * @private\n     * @function Highcharts.svgToDataURL\n     */\n    function svgToDataUrl(svg) {\n        // Webkit and not chrome\n        const userAgent = OfflineExporting_win.navigator.userAgent;\n        const webKit = (userAgent.indexOf('WebKit') > -1 &&\n            userAgent.indexOf('Chrome') < 0);\n        try {\n            // Safari requires data URI since it doesn't allow navigation to\n            // blob URLs. ForeignObjects also don't work well in Blobs in Chrome\n            // (#14780).\n            if (!webKit && svg.indexOf('<foreignObject') === -1) {\n                return OfflineExporting.domurl.createObjectURL(new OfflineExporting_win.Blob([svg], {\n                    type: 'image/svg+xml;charset-utf-16'\n                }));\n            }\n        }\n        catch (e) {\n            // Ignore\n        }\n        return 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg);\n    }\n    OfflineExporting.svgToDataUrl = svgToDataUrl;\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function svgToPdf(svgElement, margin, scale, callback) {\n        const width = (Number(svgElement.getAttribute('width')) + 2 * margin) *\n            scale, height = (Number(svgElement.getAttribute('height')) + 2 * margin) *\n            scale, pdfDoc = new OfflineExporting_win.jspdf.jsPDF(// eslint-disable-line new-cap\n        // setting orientation to portrait if height exceeds width\n        height > width ? 'p' : 'l', 'pt', [width, height]);\n        // Workaround for #7090, hidden elements were drawn anyway. It comes\n        // down to https://github.com/yWorks/svg2pdf.js/issues/28. Check this\n        // later.\n        [].forEach.call(svgElement.querySelectorAll('*[visibility=\"hidden\"]'), function (node) {\n            node.parentNode.removeChild(node);\n        });\n        // Workaround for #13948, multiple stops in linear gradient set to 0\n        // causing error in Acrobat\n        const gradients = svgElement.querySelectorAll('linearGradient');\n        for (let index = 0; index < gradients.length; index++) {\n            const gradient = gradients[index];\n            const stops = gradient.querySelectorAll('stop');\n            let i = 0;\n            while (i < stops.length &&\n                stops[i].getAttribute('offset') === '0' &&\n                stops[i + 1].getAttribute('offset') === '0') {\n                stops[i].remove();\n                i++;\n            }\n        }\n        // Workaround for #15135, zero width spaces, which Highcharts uses\n        // to break lines, are not correctly rendered in PDF. Replace it\n        // with a regular space and offset by some pixels to compensate.\n        [].forEach.call(svgElement.querySelectorAll('tspan'), (tspan) => {\n            if (tspan.textContent === '\\u200B') {\n                tspan.textContent = ' ';\n                tspan.setAttribute('dx', -5);\n            }\n        });\n        pdfDoc.svg(svgElement, {\n            x: 0,\n            y: 0,\n            width,\n            height,\n            removeInvalid: true\n        }).then(() => callback(pdfDoc.output('datauristring')));\n    }\n    OfflineExporting.svgToPdf = svgToPdf;\n})(OfflineExporting || (OfflineExporting = {}));\n/* *\n *\n * Default Export\n *\n * */\n/* harmony default export */ const OfflineExporting_OfflineExporting = (OfflineExporting);\n\n;// external \"./exporting.js\"\nvar external_exporting_src_js_x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var external_exporting_src_js_y = (x) => (() => (x))\n    const external_exporting_src_js_namespaceObject = external_exporting_src_js_x({  });\n;// ./code/es-modules/masters/modules/offline-exporting.js\n\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\n// Compatibility\nG.dataURLtoBlob = G.dataURLtoBlob || Extensions_DownloadURL.dataURLtoBlob;\nG.downloadSVGLocal = OfflineExporting_OfflineExporting.downloadSVGLocal;\nG.downloadURL = G.downloadURL || Extensions_DownloadURL.downloadURL;\n// Compose\nOfflineExporting_OfflineExporting.compose(G.Chart);\n/* harmony default export */ const offline_exporting_src = ((external_highcharts_src_js_default_default()));\n\nexport { offline_exporting_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__WEBPACK_EXTERNAL_MODULE__data_src_js_55fab5a0__", "ChartNavigationComposition", "ExportingSymbols", "Exporting", "OfflineExporting", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "<PERSON><PERSON><PERSON><PERSON>", "win", "document", "doc", "domurl", "URL", "webkitURL", "dataURLtoBlob", "dataURL", "parts", "replace", "match", "length", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "Blob", "createObjectURL", "binStr", "buf", "binary", "i", "charCodeAt", "Extensions_DownloadURL", "downloadURL", "filename", "nav", "navigator", "createElement", "String", "msSaveOrOpenBlob", "userAgent", "Error", "isOldEdgeBrowser", "test", "safariBlob", "indexOf", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "open", "location", "external_highcharts_src_js_default_AST_namespaceObject", "AST", "external_highcharts_src_js_default_AST_default", "compose", "chart", "navigation", "Additions", "constructor", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "for<PERSON>ach", "Chart_ChartNavigationComposition", "isTouchDevice", "Exporting_ExportingDefaults", "exporting", "allowTableSorting", "type", "url", "version", "pdfFont", "normal", "bold", "bolditalic", "italic", "printMaxWidth", "scale", "buttons", "contextButton", "className", "menuClassName", "symbol", "<PERSON><PERSON><PERSON>", "menuItems", "menuItemDefinitions", "viewFullscreen", "<PERSON><PERSON><PERSON>", "onclick", "fullscreen", "toggle", "printChart", "print", "separator", "downloadPNG", "exportChart", "downloadJPEG", "downloadPDF", "downloadSVG", "lang", "exitFullscreen", "contextButtonTitle", "buttonOptions", "symbolSize", "symbolX", "symbolY", "align", "buttonSpacing", "height", "y", "verticalAlign", "width", "symbolFill", "symbolStroke", "symbolStrokeWidth", "theme", "fill", "padding", "stroke", "menuStyle", "border", "borderRadius", "background", "menuItemStyle", "color", "fontSize", "transition", "menuItemHoverStyle", "modifiedClasses", "menu", "x", "menuball", "h", "path", "concat", "circle", "SVGRendererClass", "symbols", "bind", "Exporting_ExportingSymbols", "composed", "addEvent", "fireEvent", "pushUnique", "onChartBeforeRender", "Fullscreen", "ChartClass", "isOpen", "container", "renderTo", "browserProps", "requestFullscreen", "fullscreenChange", "mozRequestFullScreen", "webkitRequestFullScreen", "msRequestFullscreen", "close", "optionsChart", "ownerDocument", "Document", "unbindFullscreenEvent", "setSize", "origWidth", "origHeight", "origWidthOption", "origHeightOption", "setButtonText", "chartWidth", "chartHeight", "unbind<PERSON>hange", "unbind<PERSON><PERSON><PERSON>", "promise", "alert", "exportDivElements", "exportingOptions", "exportDivElement", "setElementHTML", "text", "external_data_src_js_default_HttpUtilities_namespaceObject", "HttpUtilities", "external_data_src_js_default_HttpUtilities_default", "defaultOptions", "Exporting_doc", "SVG_NS", "Exporting_win", "Exporting_addEvent", "css", "discardElement", "extend", "find", "Exporting_fireEvent", "isObject", "merge", "objectEach", "pick", "removeEvent", "splat", "<PERSON><PERSON><PERSON>", "printingChart", "inlineDenylist", "inlineToAttributes", "inlineAllowlist", "unstyledElements", "addButton", "callback", "renderer", "btnOptions", "btnCount", "exportSVGElements", "enabled", "styledMode", "e", "stopPropagation", "contextMenu", "button", "translateX", "translateY", "setState", "paddingLeft", "useHTML", "addClass", "attr", "title", "_title<PERSON>ey", "Math", "round", "zIndex", "add", "exportingGroup", "buttonOffset", "after<PERSON><PERSON>t", "printReverseInfo", "childNodes", "origDisplay", "resetParams", "moveContainers", "node", "nodeType", "style", "display", "isPrinting", "apply", "beforePrint", "pointer", "reset", "chartCallback", "composition", "renderExporting", "destroyExport", "items", "navOptions", "cacheName", "menuPadding", "max", "innerMenu", "exportContextMenu", "position", "pointerEvents", "scrollablePlotArea", "fixedDiv", "listStyle", "margin", "MozBoxShadow", "WebkitBoxShadow", "boxShadow", "hideMenu", "openMenu", "overflow", "clearTimeout", "hide<PERSON><PERSON>r", "exportEvents", "setTimeout", "inClass", "target", "item", "element", "isDataTableVisible", "arguments", "on<PERSON><PERSON>ver", "onmouseout", "cursor", "exportMenuWidth", "offsetWidth", "exportMenuHeight", "offsetHeight", "right", "left", "alignOptions", "bottom", "top", "elem", "ontouchstart", "destroy", "unbind", "chartOptions", "svg", "getSVGForExport", "post", "getFilename", "fetchOptions", "getChartHTML", "applyStyleSheets", "inlineStyles", "resolveCSSVariables", "innerHTML", "s", "userOptions", "toLowerCase", "substr", "getSVG", "seriesOptions", "plotOptions", "time", "sandbox", "cssWidth", "cssHeight", "sourceWidth", "parseInt", "isGantt", "sourceHeight", "animation", "forExport", "data", "series", "serie", "enableMouseTracking", "showCheckbox", "visible", "isInternal", "colls", "axes", "axis", "internalKey", "coll", "uniqueNames", "colorAxis", "chartCopy", "collOptions", "axisCopy", "copy", "extremes", "getExtremes", "exportOverride", "userMin", "min", "userMax", "setExtremes", "sanitizeSVG", "chartExportingOptions", "dummySVG", "allowlist", "defaultStyles", "iframe", "visibility", "iframeDoc", "contentWindow", "createElementNS", "recurse", "styles", "parentStyles", "dummy", "denylisted", "allowlisted", "filteredStyles", "nodeName", "getComputedStyle", "parentNode", "getElementsByTagName", "namespaceURI", "defaults", "p", "isFirefox", "isMS", "filterStyles", "val", "denylist", "setAttribute", "children", "querySelector", "svgElements", "querySelectorAll", "colorAttributes", "Array", "from", "attrValue", "getAttribute", "includes", "getPropertyValue", "moveTo", "scrollingContainer", "div", "onChartInit", "isDirtyExporting", "onChartLayOutTitle", "alignTo", "textPxLength", "space", "widthAdjust", "alignValue", "focus", "isDirty", "g", "split", "useForeignObject", "html", "allowHTML", "Exporting_Fullscreen", "chartProto", "callbacks", "matchMedia", "addListener", "mqlEvent", "matches", "Exporting_Exporting", "OfflineExporting_OfflineExportingDefaults", "libURL", "exportChartLocal", "OfflineExporting_defaultOptions", "OfflineExporting_downloadURL", "OfflineExporting_doc", "OfflineExporting_win", "ajax", "OfflineExporting_addEvent", "error", "OfflineExporting_extend", "OfflineExporting_fireEvent", "OfflineExporting_merge", "allowedAttributes", "allowedTags", "fallbackToExportServer", "err", "some", "image", "getSVGForLocalExport", "downloadSVGLocal", "getScript", "scriptLocation", "head", "script", "src", "onload", "onerror", "fail<PERSON><PERSON>back", "success<PERSON>allback", "sanitize", "chartCopyOptions", "checkDone", "images", "imagesEmbedded", "images<PERSON><PERSON>th", "chartCopyContainer", "embeddedSuccess", "imageURL", "imageType", "callback<PERSON><PERSON><PERSON>", "imageElement", "setAttributeNS", "el", "unbindGetSVG", "cloneNode", "getAttributeNS", "imageToDataUrl", "taintedCallback", "noCanvasSupportCallback", "failedLoadCallback", "finally<PERSON><PERSON><PERSON>", "img", "Image", "taintedHandler", "loadHandler", "canvas", "ctx", "getContext", "drawImage", "toDataURL", "loadEventDeferDelay", "<PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "svgToDataUrl", "webKit", "encodeURIComponent", "svgToPdf", "svgElement", "Number", "pdfDoc", "jspdf", "jsPDF", "gradients", "index", "stops", "gradient", "remove", "tspan", "textContent", "removeInvalid", "then", "output", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dummy<PERSON><PERSON><PERSON><PERSON>", "svgurl", "blob", "<PERSON><PERSON><PERSON><PERSON>", "objectURLRevoke", "slice", "loadPdfFonts", "normalBase64", "addFont", "variant", "base64", "API", "events", "addFileToVFS", "getFontList", "HighchartsFont", "setFont", "variants", "shiftAndLoadVariant", "shift", "responseType", "success", "xhr", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "response", "titleElements", "outlineElements", "textElements", "setStylePropertyFromParents", "propName", "curParent", "value", "parseFloat", "property", "fontFamily", "splice", "titleElement", "getElementsByClassName", "outline", "svgNode", "pdfData", "MSBlobBuilder", "append", "getBlob", "revokeObjectURL", "matchedImageWidth", "matchedImageHeight", "imageWidth", "imageHeight", "downloadWithCanVG", "v", "canvg", "Canvg", "fromString", "start", "msToBlob", "OfflineExporting_OfflineExporting", "G", "Chart", "offline_exporting_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,AAChG,WAAYC,MAAuD,eAAgB,AACnF,OAAwE,oBAAqB,CAEpF,IAiMLC,EAg9BAC,EAibAC,EA61CAC,EA/5FSC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqD1B,EAAwD,OAAU,CAC7H,IAAI2B,EAA0DrB,EAAoBC,CAAC,CAACmB,GAoBpF,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,IAAAA,CAAG,CAAEA,IAAK,CAAEC,SAAUC,CAAG,CAAE,CAAE,CAAIJ,IAM7CK,EAASH,EAAII,GAAG,EAAIJ,EAAIK,SAAS,EAAIL,EAe3C,SAASM,EAAcC,CAAO,EAC1B,IAAMC,EAAQD,EACTE,OAAO,CAAC,eAAgB,IACxBC,KAAK,CAAC,yCACX,GAAIF,GACAA,EAAMG,MAAM,CAAG,GACdX,EAAIY,IAAI,EACTZ,EAAIa,WAAW,EACfb,EAAIc,UAAU,EACdd,EAAIe,IAAI,EACPZ,EAAOa,eAAe,CAAG,CAE1B,IAAMC,EAASjB,EAAIY,IAAI,CAACJ,CAAK,CAAC,EAAE,EAAGU,EAAM,IAAIlB,EAAIa,WAAW,CAACI,EAAON,MAAM,EAAGQ,EAAS,IAAInB,EAAIc,UAAU,CAACI,GACzG,IAAK,IAAIE,EAAI,EAAGA,EAAID,EAAOR,MAAM,CAAE,EAAES,EACjCD,CAAM,CAACC,EAAE,CAAGH,EAAOI,UAAU,CAACD,GAElC,OAAOjB,EACFa,eAAe,CAAC,IAAIhB,EAAIe,IAAI,CAAC,CAACI,EAAO,CAAE,CAAE,KAAQX,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,CAqE6B,IAAMc,EAJf,CAChBhB,cAAAA,EACAiB,YAvDJ,SAAqBhB,CAAO,CAAEiB,CAAQ,EAClC,IAAMC,EAAMzB,EAAI0B,SAAS,CAAE3C,EAAImB,EAAIyB,aAAa,CAAC,KAGjD,GAAI,AAAmB,UAAnB,OAAOpB,GACP,CAAEA,CAAAA,aAAmBqB,MAAK,GAC1BH,EAAII,gBAAgB,CAAE,CACtBJ,EAAII,gBAAgB,CAACtB,EAASiB,GAC9B,MACJ,CAEA,GADAjB,EAAU,GAAKA,EACXkB,EAAIK,SAAS,CAACnB,MAAM,CAAG,IACvB,MAAM,AAAIoB,MAAM,kBAEpB,IAEAC,EAAmB,YAAYC,IAAI,CAACR,EAAIK,SAAS,EAKjD,GAAII,CAAAA,AAHUnC,GACV,AAAmB,UAAnB,OAAOQ,GACPA,AAA4C,IAA5CA,EAAQ4B,OAAO,CAAC,yBACFH,GAAoBzB,EAAQI,MAAM,CAAG,GAAM,GAErD,CADJJ,CAAAA,EAAUD,EAAcC,IAAY,EAAC,EAEjC,MAAM,AAAIwB,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAfhD,EAAEqD,QAAQ,CACjBrD,EAAEsD,IAAI,CAAG9B,EACTxB,EAAEqD,QAAQ,CAAGZ,EACbtB,EAAIoC,IAAI,CAACC,WAAW,CAACxD,GACrBA,EAAEyD,KAAK,GACPtC,EAAIoC,IAAI,CAACG,WAAW,CAAC1D,QAIrB,GAAI,CACA,GAAI,CAACiB,EAAI0C,IAAI,CAACnC,EAAS,SACnB,MAAM,AAAIwB,MAAM,wBAExB,CACA,KAAM,CAEF/B,EAAI2C,QAAQ,CAACN,IAAI,CAAG9B,CACxB,CAER,CASA,EAIMqC,EAAyDzE,EAAwD,OAAU,CAAC0E,GAAG,CACrI,IAAIC,EAA8DrE,EAAoBC,CAAC,CAACkE,GAG3EnE,EAAoBK,CAAC,CAAzB,CAAC,EAI6D,CAAG,GAkB1E,AAAC,SAAUT,CAA0B,EAqBjCA,EAA2B0E,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHKA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAIC,EAAUF,EAAK,EAEnCA,CACX,CAYA,OAAME,EAMFC,YAAYH,CAAK,CAAE,CACf,IAAI,CAACI,OAAO,CAAG,EAAE,CACjB,IAAI,CAACJ,KAAK,CAAGA,CACjB,CAaAK,UAAUC,CAAQ,CAAE,CAChB,IAAI,CAACN,KAAK,CAACC,UAAU,CAACG,OAAO,CAACG,IAAI,CAACD,EACvC,CAIAE,OAAOC,CAAO,CAAEC,CAAM,CAAE,CACpB,IAAI,CAACN,OAAO,CAACO,OAAO,CAAC,AAACL,IAClBA,EAAS1D,IAAI,CAAC,IAAI,CAACoD,KAAK,CAAES,EAASC,EACvC,EACJ,CACJ,CACArF,EAA2B6E,SAAS,CAAGA,CAC3C,EAAG7E,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAMnC,IAAMuF,EAAoCvF,EAcjE,CAAEwF,cAAAA,CAAa,CAAE,CAAI/D,IAo2BQgE,EALT,CACtBC,UAj1Bc,CAwBdC,kBAAmB,CAAA,EAmLnBC,KAAM,YAONC,IAAK,CAAC,oCAAoC,EAAE,AAACpE,IAA8CqE,OAAO,CAAC,CAAC,CAqBpGC,QAAS,CAQLC,OAAQ,KAAK,EAMbC,KAAM,KAAK,EAMXC,WAAY,KAAK,EAMjBC,OAAQ,KAAK,CACjB,EAUAC,cAAe,IAmBfC,MAAO,EAUPC,QAAS,CAWLC,cAAe,CAiCXC,UAAW,2BAIXC,cAAe,yBAgBfC,OAAQ,OASRC,SAAU,qBAoBVC,UAAW,CACP,iBACA,aACA,YACA,cACA,eACA,cACH,AACL,CACJ,EA6BAC,oBAAqB,CAIjBC,eAAgB,CACZC,QAAS,iBACTC,QAAS,WACD,IAAI,CAACC,UAAU,EACf,IAAI,CAACA,UAAU,CAACC,MAAM,EAE9B,CACJ,EAIAC,WAAY,CACRJ,QAAS,aACTC,QAAS,WACL,IAAI,CAACI,KAAK,EACd,CACJ,EAIAC,UAAW,CACPA,UAAW,CAAA,CACf,EAIAC,YAAa,CACTP,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,EACpB,CACJ,EAIAC,aAAc,CACVT,QAAS,eACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb3B,KAAM,YACV,EACJ,CACJ,EAIA6B,YAAa,CACTV,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb3B,KAAM,iBACV,EACJ,CACJ,EAIA8B,YAAa,CACTX,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb3B,KAAM,eACV,EACJ,CACJ,CACJ,CACJ,EAgWI+B,KA3VS,CAOTb,eAAgB,sBAOhBc,eAAgB,wBAOhBT,WAAY,cAOZG,YAAa,qBAObE,aAAc,sBAOdC,YAAa,wBAObC,YAAa,4BAQbG,mBAAoB,oBACxB,EAkSIjD,WA1Re,CAUfkD,cAAe,CAoBXC,WAAY,GASZC,QAAS,KASTC,QAAS,KAUTC,MAAO,QASPC,cAAe,EASfC,OAAQ,GAsCRC,EAAG,GAWHC,cAAe,MASfC,MAAO,GAUPC,WAAY,UAUZC,aAAc,UASdC,kBAAmB,EAcnBC,MAAO,CAMHC,KAAM,UAINC,QAAS,EAMTC,OAAQ,OAIR,iBAAkB,OACtB,CACJ,EAeAC,UAAW,CAEPC,OAAQ,OAERC,aAAc,MAEdC,WAAY,UAEZL,QAAS,OACb,EAiBAM,cAAe,CAEXD,WAAY,OAEZD,aAAc,MAEdG,MAAO,UAEPP,QAAS,QAETQ,SAAU7D,EAAgB,QAAU,QAEpC8D,WAAY,+BAChB,EAgBAC,mBAAoB,CAEhBL,WAAY,SAChB,CACJ,CAUA,GAsBA,AAAC,SAAUjJ,CAAgB,EAMvB,IAAMuJ,EAAkB,EAAE,CAsB1B,SAASC,EAAKC,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EAS7B,MARY,CACR,CAAC,IAAKsB,EAAGrB,EAAI,IAAI,CACjB,CAAC,IAAKqB,EAAInB,EAAOF,EAAI,IAAI,CACzB,CAAC,IAAKqB,EAAGrB,EAAID,EAAS,EAAI,GAAI,CAC9B,CAAC,IAAKsB,EAAInB,EAAOF,EAAID,EAAS,EAAI,GAAI,CACtC,CAAC,IAAKsB,EAAGrB,EAAID,EAAS,IAAI,CAC1B,CAAC,IAAKsB,EAAInB,EAAOF,EAAID,EAAS,IAAI,CACrC,AAEL,CAIA,SAASuB,EAASD,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EACjC,IAAMwB,EAAI,AAACxB,EAAS,EAAK,EACrByB,EAAO,EAAE,CAEb,OADOA,EAAKC,MAAM,CAAC,IAAI,CAACC,MAAM,CAACxB,EAAQqB,EAAGvB,EAAGuB,EAAGA,GAAI,IAAI,CAACG,MAAM,CAACxB,EAAQqB,EAAGvB,EAAIuB,EAAI,EAAGA,EAAGA,GAAI,IAAI,CAACG,MAAM,CAACxB,EAAQqB,EAAGvB,EAAI,EAAKuB,CAAAA,EAAI,CAAA,EAAIA,EAAGA,GAE5I,CAvBA3J,EAAiByE,OAAO,CARxB,SAAiBsF,CAAgB,EAC7B,GAAIR,AAA8C,KAA9CA,EAAgB1F,OAAO,CAACkG,GAA0B,CAClDR,EAAgBtE,IAAI,CAAC8E,GACrB,IAAMC,EAAUD,EAAiB3I,SAAS,CAAC4I,OAAO,AAClDA,CAAAA,EAAQR,IAAI,CAAGA,EACfQ,EAAQN,QAAQ,CAAGA,EAASO,IAAI,CAACD,EACrC,CACJ,CAyBJ,EAAGhK,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMkK,EAA8BlK,EAsB3D,CAAEmK,SAAAA,CAAQ,CAAE,CAAI3I,IAEhB,CAAE4I,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,WAAAA,CAAU,CAAE,CAAI9I,IAS7C,SAAS+I,IAML,IAAI,CAACvD,UAAU,CAAG,IAAIwD,EAAW,IAAI,CACzC,CAgBA,MAAMA,EAYF,OAAO/F,QAAQgG,CAAU,CAAE,CACnBH,EAAWH,EAAU,eAErBC,EAASK,EAAY,eAAgBF,EAE7C,CAMA1F,YAAYH,CAAK,CAAE,CAMf,IAAI,CAACA,KAAK,CAAGA,EASb,IAAI,CAACgG,MAAM,CAAG,CAAA,EACd,IAAMC,EAAYjG,EAAMkG,QAAQ,AAE5B,EAAC,IAAI,CAACC,YAAY,GACd,AAAuC,YAAvC,OAAOF,EAAUG,iBAAiB,CAClC,IAAI,CAACD,YAAY,CAAG,CAChBE,iBAAkB,mBAClBD,kBAAmB,oBACnBnD,eAAgB,gBACpB,EAEKgD,EAAUK,oBAAoB,CACnC,IAAI,CAACH,YAAY,CAAG,CAChBE,iBAAkB,sBAClBD,kBAAmB,uBACnBnD,eAAgB,qBACpB,EAEKgD,EAAUM,uBAAuB,CACtC,IAAI,CAACJ,YAAY,CAAG,CAChBE,iBAAkB,yBAClBD,kBAAmB,0BACnBnD,eAAgB,sBACpB,EAEKgD,EAAUO,mBAAmB,EAClC,CAAA,IAAI,CAACL,YAAY,CAAG,CAChBE,iBAAkB,qBAClBD,kBAAmB,sBACnBnD,eAAgB,kBACpB,CAAA,EAGZ,CAgBAwD,OAAQ,CACJ,IAAMnE,EAAa,IAAI,CAAEtC,EAAQsC,EAAWtC,KAAK,CAAE0G,EAAe1G,EAAMS,OAAO,CAACT,KAAK,CACrF2F,EAAU3F,EAAO,kBAAmB,KAAM,WAGlCsC,EAAW0D,MAAM,EACjB1D,EAAW6D,YAAY,EACvBnG,EAAMiG,SAAS,CAACU,aAAa,YAAYC,UACzC5G,EAAMiG,SAAS,CAACU,aAAa,CAACrE,EAAW6D,YAAY,CAAClD,cAAc,CAAC,GAIrEX,EAAWuE,qBAAqB,EAChCvE,CAAAA,EAAWuE,qBAAqB,CAAGvE,EAC9BuE,qBAAqB,EAAC,EAE/B7G,EAAM8G,OAAO,CAACxE,EAAWyE,SAAS,CAAEzE,EAAW0E,UAAU,CAAE,CAAA,GAC3D1E,EAAWyE,SAAS,CAAG,KAAK,EAC5BzE,EAAW0E,UAAU,CAAG,KAAK,EAC7BN,EAAa9C,KAAK,CAAGtB,EAAW2E,eAAe,CAC/CP,EAAajD,MAAM,CAAGnB,EAAW4E,gBAAgB,CACjD5E,EAAW2E,eAAe,CAAG,KAAK,EAClC3E,EAAW4E,gBAAgB,CAAG,KAAK,EACnC5E,EAAW0D,MAAM,CAAG,CAAA,EACpB1D,EAAW6E,aAAa,EAC5B,EACJ,CAaAzH,MAAO,CACH,IAAM4C,EAAa,IAAI,CAAEtC,EAAQsC,EAAWtC,KAAK,CAAE0G,EAAe1G,EAAMS,OAAO,CAACT,KAAK,CACrF2F,EAAU3F,EAAO,iBAAkB,KAAM,WAQrC,GAPI0G,IACApE,EAAW2E,eAAe,CAAGP,EAAa9C,KAAK,CAC/CtB,EAAW4E,gBAAgB,CAAGR,EAAajD,MAAM,EAErDnB,EAAWyE,SAAS,CAAG/G,EAAMoH,UAAU,CACvC9E,EAAW0E,UAAU,CAAGhH,EAAMqH,WAAW,CAErC/E,EAAW6D,YAAY,CAAE,CACzB,IAAMmB,EAAe5B,EAAS1F,EAAMiG,SAAS,CAACU,aAAa,CAC3DrE,EAAW6D,YAAY,CAACE,gBAAgB,CAAE,WAGlC/D,EAAW0D,MAAM,EACjB1D,EAAW0D,MAAM,CAAG,CAAA,EACpB1D,EAAWmE,KAAK,KAGhBzG,EAAM8G,OAAO,CAAC,KAAM,KAAM,CAAA,GAC1BxE,EAAW0D,MAAM,CAAG,CAAA,EACpB1D,EAAW6E,aAAa,GAEhC,GACMI,EAAgB7B,EAAS1F,EAAO,UAAWsH,EACjDhF,CAAAA,EAAWuE,qBAAqB,CAAG,KAC/BS,IACAC,GACJ,EACA,IAAMC,EAAUxH,EAAMkG,QAAQ,CAAC5D,EAAW6D,YAAY,CAACC,iBAAiB,CAAC,GACrEoB,GACAA,EAAQ,KAAQ,CAAC,WACbC,MACA,+CACJ,EAER,CACJ,EACJ,CAWAN,eAAgB,CACZ,IAAMnH,EAAQ,IAAI,CAACA,KAAK,CAAE0H,EAAoB1H,EAAM0H,iBAAiB,CAAEC,EAAmB3H,EAAMS,OAAO,CAACM,SAAS,CAAEkB,EAAa0F,GAC5HA,EAAiBhG,OAAO,EACxBgG,EAAiBhG,OAAO,CAACC,aAAa,CAACK,SAAS,CAAGe,EAAOhD,EAAMS,OAAO,CAACuC,IAAI,CAChF,GAAI2E,GACAA,EAAiBzF,mBAAmB,EACpCc,GACAA,EAAKC,cAAc,EACnBD,EAAKb,cAAc,EACnBF,GACAyF,EAAmB,CACnB,IAAME,EAAmBF,CAAiB,CAACzF,EAAU9C,OAAO,CAAC,kBAAkB,CAC3EyI,GACA9H,IAAiD+H,cAAc,CAACD,EAAkB,AAAC,IAAI,CAAC5B,MAAM,CAG/DhD,EAAKC,cAAc,CAF7C0E,EAAiBzF,mBAAmB,CAACC,cAAc,CAC/C2F,IAAI,EACL9E,EAAKb,cAAc,CAEnC,CACJ,CAeAI,QAAS,CAEAD,AADc,IAAI,CACP0D,MAAM,CAIlB1D,AALe,IAAI,CAKRmE,KAAK,GAHhBnE,AAFe,IAAI,CAER5C,IAAI,EAKvB,CACJ,CAsEA,IAAMqI,EAA6D3M,EAAkD,OAAU,CAAC4M,aAAa,CAC7I,IAAIC,EAAkExM,EAAoBC,CAAC,CAACqM,GAkB5F,GAAM,CAAEG,eAAAA,CAAc,CAAE,CAAIpL,IAKtB,CAAEI,IAAKiL,CAAa,CAAEC,OAAAA,CAAM,CAAEpL,IAAKqL,CAAa,CAAE,CAAIvL,IAGtD,CAAE4I,SAAU4C,CAAkB,CAAEC,IAAAA,CAAG,CAAE5J,cAAAA,CAAa,CAAE6J,eAAAA,CAAc,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAE/C,UAAWgD,CAAmB,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAEC,YAAAA,CAAW,CAAEC,MAAAA,CAAK,CAAEC,UAAAA,CAAS,CAAE,CAAIpM,KAO9L,AAAC,SAAUvB,CAAS,EAYhB,IAmCI4N,EAnCEC,EAAiB,CACnB,IACA,sCACA,SACA,4BACA,eACA,0BACA,cACA,oBACA,cACA,WACA,QACH,CAEKC,EAAqB,CACvB,OACA,SACA,gBACA,iBACA,cACA,aACA,IACA,IACH,AACD9N,CAAAA,EAAU+N,eAAe,CAAG,EAAE,CAC9B,IAAMC,EAAmB,CACrB,WACA,OACA,OACH,CAoBD,SAASC,EAAU/I,CAAO,EACtB,IACIsB,EAaA0H,EAdEzJ,EAAQ,IAAI,CAAE0J,EAAW1J,EAAM0J,QAAQ,CAAEC,EAAad,EAAM7I,EAAMS,OAAO,CAACR,UAAU,CAACkD,aAAa,CAAE1C,GAAU4B,EAAUsH,EAAWtH,OAAO,CAAEJ,EAAY0H,EAAW1H,SAAS,CAAEmB,EAAauG,EAAWvG,UAAU,EAAI,GAU1N,GARKpD,EAAM4J,QAAQ,EACf5J,CAAAA,EAAM4J,QAAQ,CAAG,CAAA,EAGhB5J,EAAM0H,iBAAiB,GACxB1H,EAAM0H,iBAAiB,CAAG,EAAE,CAC5B1H,EAAM6J,iBAAiB,CAAG,EAAE,EAE5BF,AAAuB,CAAA,IAAvBA,EAAWG,OAAO,EAAc,CAACH,EAAW3F,KAAK,CACjD,OAEJ,IAAMA,EAAQhE,EAAM+J,UAAU,CAAG,CAAC,EAAIJ,EAAW3F,KAAK,CAElD3B,EACAoH,EAAW,SAAUO,CAAC,EACdA,GACAA,EAAEC,eAAe,GAErB5H,EAAQzF,IAAI,CAACoD,EAAOgK,EACxB,EAEK/H,GACLwH,CAAAA,EAAW,SAAUO,CAAC,EAEdA,GACAA,EAAEC,eAAe,GAErBjK,EAAMkK,WAAW,CAACC,EAAOrI,aAAa,CAAEG,EAAWkI,EAAOC,UAAU,EAAI,EAAGD,EAAOE,UAAU,EAAI,EAAGF,EAAOvG,KAAK,EAAI,EAAGuG,EAAO1G,MAAM,EAAI,EAAG0G,GAC1IA,EAAOG,QAAQ,CAAC,EACpB,CAAA,EAEAX,EAAW7B,IAAI,EAAI6B,EAAW5H,MAAM,CACpCiC,EAAMuG,WAAW,CAAGxB,EAAK/E,EAAMuG,WAAW,CAAE,IAEtCZ,EAAW7B,IAAI,EACrBW,EAAOzE,EAAO,CACVJ,MAAO+F,EAAW/F,KAAK,CACvBH,OAAQkG,EAAWlG,MAAM,CACzBS,QAAS,CACb,GAEJ,IAAMiG,EAAST,EACVS,MAAM,CAACR,EAAW7B,IAAI,CAAE,EAAG,EAAG2B,EAAUzF,EAAO,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG2F,EAAWa,OAAO,EACjGC,QAAQ,CAAChK,EAAQoB,SAAS,EAC1B6I,IAAI,CAAC,CACNC,MAAO5B,EAAK/I,EAAMS,OAAO,CAACuC,IAAI,CAAC2G,EAAWiB,SAAS,EAAIjB,EAAW3H,QAAQ,CAAC,CAAE,GACjF,EACAmI,CAAAA,EAAOrI,aAAa,CAAIrB,EAAQqB,aAAa,EACzC,mBAAqB9B,EAAM4J,QAAQ,GACnCD,EAAW5H,MAAM,GACjBA,EAAS2H,EACJ3H,MAAM,CAAC4H,EAAW5H,MAAM,CAAE8I,KAAKC,KAAK,CAAC,AAACnB,CAAAA,EAAWtG,OAAO,EAAI,CAAA,EAAMD,EAAa,GAAKyH,KAAKC,KAAK,CAAC,AAACnB,CAAAA,EAAWrG,OAAO,EAAI,CAAA,EAAMF,EAAa,GAAKA,EAAYA,EAE7J,CACEQ,MAAOR,EACPK,OAAQL,CACZ,GACKqH,QAAQ,CAAC,4BACTC,IAAI,CAAC,CACNK,OAAQ,CACZ,GACKC,GAAG,CAACb,GACJnK,EAAM+J,UAAU,EACjBhI,EAAO2I,IAAI,CAAC,CACRvG,OAAQwF,EAAW7F,YAAY,CAC/BG,KAAM0F,EAAW9F,UAAU,CAC3B,eAAgB8F,EAAW5F,iBAAiB,EAAI,CACpD,IAGRoG,EACKa,GAAG,CAAChL,EAAMiL,cAAc,EACxB1H,KAAK,CAACkF,EAAOkB,EAAY,CAC1B/F,MAAOuG,EAAOvG,KAAK,CACnBmB,EAAGgE,EAAKY,EAAW5E,CAAC,CAAE/E,EAAMkL,YAAY,CAC5C,GAAI,CAAA,EAAM,cACVlL,EAAMkL,YAAY,EAAK,AAAC,CAAA,AAACf,CAAAA,EAAOvG,KAAK,EAAI,CAAA,EAAK+F,EAAWnG,aAAa,AAAD,EAChEmG,CAAAA,AAAqB,UAArBA,EAAWpG,KAAK,CAAe,GAAK,CAAA,EACzCvD,EAAM6J,iBAAiB,CAACtJ,IAAI,CAAC4J,EAAQpI,EACzC,CAaA,SAASoJ,IAEL,GAAI,CAACnL,AADS,IAAI,CACPoL,gBAAgB,CACvB,OAEJ,GAAM,CAAEC,WAAAA,CAAU,CAAEC,YAAAA,CAAW,CAAEC,YAAAA,CAAW,CAAE,CAAGvL,AAJnC,IAAI,CAIqCoL,gBAAgB,CAEvEpL,AANc,IAAI,CAMZwL,cAAc,CAACxL,AANP,IAAI,CAMSkG,QAAQ,EAEnC,EAAE,CAACvF,OAAO,CAAC/D,IAAI,CAACyO,EAAY,SAAUI,CAAI,CAAErN,CAAC,EACnB,IAAlBqN,EAAKC,QAAQ,EACbD,CAAAA,EAAKE,KAAK,CAACC,OAAO,CAAIN,CAAW,CAAClN,EAAE,EAAI,EAAE,CAElD,GACA4B,AAbc,IAAI,CAaZ6L,UAAU,CAAG,CAAA,EAEfN,GACAvL,AAhBU,IAAI,CAgBR8G,OAAO,CAACgF,KAAK,CAhBT,IAAI,CAgBaP,GAE/B,OAAOvL,AAlBO,IAAI,CAkBLoL,gBAAgB,CAC7BjC,EAAgB,KAAK,EACrBR,EApBc,IAAI,CAoBS,aAC/B,CAWA,SAASoD,IACL,IAAoBzM,EAAO6I,EAAc7I,IAAI,CAAEmC,EAAgBzB,AAAjD,IAAI,CAAmDS,OAAO,CAACM,SAAS,CAACU,aAAa,CAAE2J,EAAmB,CACrHC,WAAY/L,EAAK+L,UAAU,CAC3BC,YAAa,EAAE,CACfC,YAAa,KAAK,CACtB,CACAvL,CALc,IAAI,CAKZ6L,UAAU,CAAG,CAAA,EACnB7L,AANc,IAAI,CAMZgM,OAAO,EAAEC,MAAM,KAAK,EAAG,GAC7BtD,EAPc,IAAI,CAOS,eAEJlH,GACnBzB,AAVU,IAAI,CAURoH,UAAU,CAAG3F,IAEnB2J,EAAiBG,WAAW,CAAG,CAC3BvL,AAbM,IAAI,CAaJS,OAAO,CAACT,KAAK,CAAC4D,KAAK,CACzB,KAAK,EACL,CAAA,EACH,CACD5D,AAjBU,IAAI,CAiBR8G,OAAO,CAACrF,EAAe,KAAK,EAAG,CAAA,IAGzC,EAAE,CAACd,OAAO,CAAC/D,IAAI,CAACwO,EAAiBC,UAAU,CAAE,SAAUI,CAAI,CAAErN,CAAC,EACpC,IAAlBqN,EAAKC,QAAQ,GACbN,EAAiBE,WAAW,CAAClN,EAAE,CAAGqN,EAAKE,KAAK,CAACC,OAAO,CACpDH,EAAKE,KAAK,CAACC,OAAO,CAAG,OAE7B,GAEA5L,AA3Bc,IAAI,CA2BZwL,cAAc,CAAClM,GAErBU,AA7Bc,IAAI,CA6BZoL,gBAAgB,CAAGA,CAC7B,CAIA,SAASc,EAAclM,CAAK,EAExBmM,AADoBnM,EACRoM,eAAe,GAC3B9D,EAAmBtI,EAAO,SAAUmM,AAFhBnM,EAE4BoM,eAAe,EAE/D9D,EAAmBtI,EAAO,UAAWmM,AAJjBnM,EAI6BqM,aAAa,CAmClE,CAqEA,SAASnC,EAAYrI,CAAS,CAAEyK,CAAK,CAAEvH,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,CAAE0G,CAAM,EAC9D,IAAMnK,EAAQ,IAAI,CAAEuM,EAAavM,EAAMS,OAAO,CAACR,UAAU,CAAEmH,EAAapH,EAAMoH,UAAU,CAAEC,EAAcrH,EAAMqH,WAAW,CAAEmF,EAAY,SAAW3K,EAElJ4K,EAAc5B,KAAK6B,GAAG,CAAC9I,EAAOH,GAC1BkJ,EAAW7H,EAAO9E,CAAK,CAACwM,EAAU,CAEjC1H,IAED9E,EAAM4M,iBAAiB,CAAG5M,CAAK,CAACwM,EAAU,CAAG1H,EACzCnG,EAAc,MAAO,CACjBkD,UAAWA,CACf,EAAG,CACCgL,SAAU,WACV9B,OAAQ,IACR7G,QAASuI,EAAc,KACvBK,cAAe,OACf,GAAG9M,EAAM0J,QAAQ,CAACiC,KAAK,AAC3B,EAAG3L,EAAM+M,kBAAkB,EAAEC,UAAYhN,EAAMiG,SAAS,EAC5D0G,EAAYhO,EAAc,KAAM,CAAEkD,UAAW,iBAAkB,EAAG7B,EAAM+J,UAAU,CAAG,CAAC,EAAI,CACtFkD,UAAW,OACXC,OAAQ,EACRhJ,QAAS,CACb,EAAGY,GAEE9E,EAAM+J,UAAU,EACjBxB,EAAIoE,EAAWlE,EAAO,CAClB0E,aAAc,oBACdC,gBAAiB,oBACjBC,UAAW,mBACf,EAAGd,EAAWnI,SAAS,GAG3BU,EAAKwI,QAAQ,CAAG,WACZ/E,EAAIzD,EAAM,CAAE8G,QAAS,MAAO,GACxBzB,GACAA,EAAOG,QAAQ,CAAC,GAEpBtK,EAAMuN,QAAQ,CAAG,CAAA,EAEjBhF,EAAIvI,EAAMkG,QAAQ,CAAE,CAAEsH,SAAU,QAAS,GACzCjF,EAAIvI,EAAMiG,SAAS,CAAE,CAAEuH,SAAU,QAAS,GAC1C1Q,IAA6C2Q,YAAY,CAAC3I,EAAK4I,SAAS,EACxE/E,EAAoB3I,EAAO,mBAC/B,EAEAA,EAAM2N,YAAY,CAACpN,IAAI,CAAC+H,EAAmBxD,EAAM,aAAc,WAC3DA,EAAK4I,SAAS,CAAGrF,EAAcuF,UAAU,CAAC9I,EAAKwI,QAAQ,CAAE,IAC7D,GAAIhF,EAAmBxD,EAAM,aAAc,WACvChI,IAA6C2Q,YAAY,CAAC3I,EAAK4I,SAAS,CAC5E,GAGApF,EAAmBH,EAAe,UAAW,SAAU6B,CAAC,EAC/ChK,EAAMgM,OAAO,EAAE6B,QAAQ7D,EAAE8D,MAAM,CAAEjM,IAClCiD,EAAKwI,QAAQ,EAErB,GAAIhF,EAAmBxD,EAAM,QAAS,WAC9B9E,EAAMuN,QAAQ,EACdzI,EAAKwI,QAAQ,EAErB,IAEAhB,EAAM3L,OAAO,CAAC,SAAUoN,CAAI,EAKxB,GAJoB,UAAhB,OAAOA,GACPA,CAAAA,EAAO/N,EAAMS,OAAO,CAACM,SAAS,CACzBmB,mBAAmB,CAAC6L,EAAK,AAAD,EAE7BnF,EAASmF,EAAM,CAAA,GAAO,CACtB,IAAIC,CACAD,CAAAA,EAAKrL,SAAS,CACdsL,EAAUrP,EAAc,KAAM,KAAK,EAAG,KAAK,EAAGgO,IAKzB,aAAjBoB,EAAK3L,OAAO,EACZpC,EAAMiO,kBAAkB,EACxBF,CAAAA,EAAK3L,OAAO,CAAG,UAAS,EAE5B4L,EAAUrP,EAAc,KAAM,CAC1BkD,UAAW,uBACXQ,QAAS,SAAU2H,CAAC,EACZA,GACAA,EAAEC,eAAe,GAErBnF,EAAKwI,QAAQ,GACO,UAAhB,OAAOS,GAAqBA,EAAK1L,OAAO,EACxC0L,EAAK1L,OAAO,CAACyJ,KAAK,CAAC9L,EAAOkO,UAElC,CACJ,EAAG,KAAK,EAAGvB,GACX7M,IAAiD+H,cAAc,CAACmG,EAASD,EAAKjG,IAAI,EAC9E9H,EAAMS,OAAO,CAACuC,IAAI,CAAC+K,EAAK3L,OAAO,CAAC,EAC/BpC,EAAM+J,UAAU,GACjBiE,EAAQG,WAAW,CAAG,WAClB5F,EAAI,IAAI,CAAEgE,EAAW3H,kBAAkB,CAC3C,EACAoJ,EAAQI,UAAU,CAAG,WACjB7F,EAAI,IAAI,CAAEgE,EAAW/H,aAAa,CACtC,EACA+D,EAAIyF,EAASvF,EAAO,CAChB4F,OAAQ,SACZ,EAAG9B,EAAW/H,aAAa,EAAI,CAAC,MAIxCxE,EAAM0H,iBAAiB,CAACnH,IAAI,CAACyN,EACjC,CACJ,GAGAhO,EAAM0H,iBAAiB,CAACnH,IAAI,CAACoM,EAAW7H,GACxC9E,EAAMsO,eAAe,CAAGxJ,EAAKyJ,WAAW,CACxCvO,EAAMwO,gBAAgB,CAAG1J,EAAK2J,YAAY,EAE9C,IAAMrK,EAAY,CAAEwH,QAAS,OAAQ,CAEjC7G,CAAAA,EAAK/E,CAAAA,EAAMsO,eAAe,EAAI,CAAA,EAAKlH,EACnChD,EAAUsK,KAAK,CAAG,AAACtH,EAAarC,EAAInB,EAAQ6I,EAAe,KAG3DrI,EAAUuK,IAAI,CAAG,AAAC5J,EAAI0H,EAAe,KAGrC/I,EAAID,EAAUzD,CAAAA,EAAMwO,gBAAgB,EAAI,CAAA,EAAKnH,GAC7C8C,EAAOyE,YAAY,EAAEjL,gBAAkB,MACvCS,EAAUyK,MAAM,CAAG,AAACxH,EAAc3D,EAAI+I,EAAe,KAGrDrI,EAAU0K,GAAG,CAAG,AAACpL,EAAID,EAASgJ,EAAe,KAEjDlE,EAAIzD,EAAMV,GAEVmE,EAAIvI,EAAMkG,QAAQ,CAAE,CAAEsH,SAAU,EAAG,GACnCjF,EAAIvI,EAAMiG,SAAS,CAAE,CAAEuH,SAAU,EAAG,GACpCxN,EAAMuN,QAAQ,CAAG,CAAA,EACjB5E,EAAoB3I,EAAO,kBAC/B,CAQA,SAASqM,EAAcrC,CAAC,EACpB,IACIwC,EADExM,EAAQgK,EAAIA,EAAE8D,MAAM,CAAG,IAAI,CAAEjE,EAAoB7J,EAAM6J,iBAAiB,CAAEnC,EAAoB1H,EAAM0H,iBAAiB,CAAEiG,EAAe3N,EAAM2N,YAAY,CAG1J9D,IACAA,EAAkBlJ,OAAO,CAAC,CAACoO,EAAM3Q,KAEzB2Q,IACAA,EAAK1M,OAAO,CAAG0M,EAAKC,YAAY,CAAG,KAE/BhP,CAAK,CADTwM,EAAY,SAAWuC,EAAKjN,aAAa,CACrB,EAChB,OAAO9B,CAAK,CAACwM,EAAU,CAE3B3C,CAAiB,CAACzL,EAAE,CAAG2Q,EAAKE,OAAO,GAE3C,GACApF,EAAkBlM,MAAM,CAAG,GAG3BqC,EAAMiL,cAAc,GACpBjL,EAAMiL,cAAc,CAACgE,OAAO,GAC5B,OAAOjP,EAAMiL,cAAc,EAG3BvD,IACAA,EAAkB/G,OAAO,CAAC,SAAUoO,CAAI,CAAE3Q,CAAC,EACnC2Q,IAEAjS,IAA6C2Q,YAAY,CAACsB,EAAKrB,SAAS,EACxE1E,EAAY+F,EAAM,cAGlBrH,CAAiB,CAACtJ,EAAE,CAChB2Q,EAAKX,UAAU,CACXW,EAAKZ,WAAW,CACZY,EAAKC,YAAY,CACbD,EAAK1M,OAAO,CAAG,KAE/BmG,EAAeuG,GAEvB,GACArH,EAAkB/J,MAAM,CAAG,GAE3BgQ,IACAA,EAAahN,OAAO,CAAC,SAAUuO,CAAM,EACjCA,GACJ,GACAvB,EAAahQ,MAAM,CAAG,EAE9B,CA2BA,SAASiF,EAAY+E,CAAgB,CAAEwH,CAAY,EAC/C,IAAMC,EAAM,IAAI,CAACC,eAAe,CAAC1H,EAAkBwH,GAEnDxH,EAAmBkB,EAAM,IAAI,CAACpI,OAAO,CAACM,SAAS,CAAE4G,GAEjDM,IAAqDqH,IAAI,CAAC3H,EAAiBzG,GAAG,CAAE,CAC5E1C,SAAUmJ,EAAiBnJ,QAAQ,CAC/BmJ,EAAiBnJ,QAAQ,CAACf,OAAO,CAAC,MAAO,KACzC,IAAI,CAAC8R,WAAW,GACpBtO,KAAM0G,EAAiB1G,IAAI,CAC3B2C,MAAO+D,EAAiB/D,KAAK,CAC7BlC,MAAOiG,EAAiBjG,KAAK,CAC7B0N,IAAKA,CACT,EAAGzH,EAAiB6H,YAAY,CACpC,CAcA,SAASC,EAAaC,CAAgB,EAKlC,OAJIA,GACA,IAAI,CAACC,YAAY,GAErB,IAAI,CAACC,mBAAmB,GACjB,IAAI,CAAC3J,SAAS,CAAC4J,SAAS,AACnC,CAWA,SAASN,IACL,IAAMO,EAAI,IAAI,CAACC,WAAW,CAACpF,KAAK,EAAI,IAAI,CAACoF,WAAW,CAACpF,KAAK,CAAC7C,IAAI,CAC3DtJ,EAAW,IAAI,CAACiC,OAAO,CAACM,SAAS,CAACvC,QAAQ,QAC9C,AAAIA,EACOA,EAASf,OAAO,CAAC,MAAO,MAElB,UAAb,OAAOqS,GACPtR,CAAAA,EAAWsR,EACNE,WAAW,GACXvS,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,UAAW,KACnBA,OAAO,CAAC,cAAe,IACvBA,OAAO,CAAC,UAAW,IACnBA,OAAO,CAAC,SAAU,KAClBwS,MAAM,CAAC,EAAG,IACVxS,OAAO,CAAC,UAAW,GAAE,EAE1B,CAAA,CAACe,GAAYA,EAASb,MAAM,CAAG,CAAA,GAC/Ba,CAAAA,EAAW,OAAM,EAEdA,EACX,CAsBA,SAAS0R,EAAOf,CAAY,EAExB,IAAIC,EAAKe,EAET1P,EAAUoI,EAAM7I,AAHF,IAAI,CAGIS,OAAO,CAAE0O,EAE/B1O,CAAAA,EAAQ2P,WAAW,CAAGvH,EAAM7I,AALd,IAAI,CAKgB+P,WAAW,CAACK,WAAW,CAAEjB,GAAgBA,EAAaiB,WAAW,EAGnG3P,EAAQ4P,IAAI,CAAGxH,EAAM7I,AARP,IAAI,CAQS+P,WAAW,CAACM,IAAI,CAAElB,GAAgBA,EAAakB,IAAI,EAE9E,IAAMC,EAAU3R,EAAc,MAAO,KAAM,CACvCkO,SAAU,WACViC,IAAK,UACLlL,MAAO5D,AAbG,IAAI,CAaDoH,UAAU,CAAG,KAC1B3D,OAAQzD,AAdE,IAAI,CAcAqH,WAAW,CAAG,IAChC,EAAGc,EAAc7I,IAAI,EAEfiR,EAAWvQ,AAjBH,IAAI,CAiBKkG,QAAQ,CAACyF,KAAK,CAAC/H,KAAK,CAAE4M,EAAYxQ,AAjB3C,IAAI,CAiB6CkG,QAAQ,CAACyF,KAAK,CAAClI,MAAM,CAAEgN,EAAchQ,EAAQM,SAAS,CAAC0P,WAAW,EAC7HhQ,EAAQT,KAAK,CAAC4D,KAAK,EAClB,MAAM3E,IAAI,CAACsR,IAAaG,SAASH,EAAU,KAC3C9P,CAAAA,EAAQkQ,OAAO,CAAG,IAAM,GAAE,EAAIC,EAAenQ,EAAQM,SAAS,CAAC6P,YAAY,EAC5EnQ,EAAQT,KAAK,CAACyD,MAAM,EACnB,MAAMxE,IAAI,CAACuR,IAAcE,SAASF,EAAW,KAC9C,IAEJ/H,EAAOhI,EAAQT,KAAK,CAAE,CAClB6Q,UAAW,CAAA,EACX3K,SAAUoK,EACVQ,UAAW,CAAA,EACXpH,SAAU,cACV9F,MAAO6M,EACPhN,OAAQmN,CACZ,GACAnQ,EAAQM,SAAS,CAAC+I,OAAO,CAAG,CAAA,EAC5B,OAAOrJ,EAAQsQ,IAAI,CAEnBtQ,EAAQuQ,MAAM,CAAG,EAAE,CACnBhR,AArCc,IAAI,CAqCZgR,MAAM,CAACrQ,OAAO,CAAC,SAAUsQ,CAAK,EAQ3Bd,AAPLA,CAAAA,EAAgBtH,EAAMoI,EAAMlB,WAAW,CAAE,CACrCc,UAAW,CAAA,EACXK,oBAAqB,CAAA,EACrBC,aAAc,CAAA,EACdC,QAASH,EAAMG,OAAO,AAC1B,EAAC,EAEkBC,UAAU,EACzB5Q,EAAQuQ,MAAM,CAACzQ,IAAI,CAAC4P,EAE5B,GACA,IAAMmB,EAAQ,CAAC,EACftR,AAlDc,IAAI,CAkDZuR,IAAI,CAAC5Q,OAAO,CAAC,SAAU6Q,CAAI,EAExBA,EAAKzB,WAAW,CAAC0B,WAAW,EAC7BD,CAAAA,EAAKzB,WAAW,CAAC0B,WAAW,CAAGvI,GAAU,EAExCsI,EAAK/Q,OAAO,CAAC4Q,UAAU,GACnBC,CAAK,CAACE,EAAKE,IAAI,CAAC,GACjBJ,CAAK,CAACE,EAAKE,IAAI,CAAC,CAAG,CAAA,EACnBjR,CAAO,CAAC+Q,EAAKE,IAAI,CAAC,CAAG,EAAE,EAE3BjR,CAAO,CAAC+Q,EAAKE,IAAI,CAAC,CAACnR,IAAI,CAACsI,EAAM2I,EAAKzB,WAAW,CAAE,CAC5CqB,QAASI,EAAKJ,OAAO,CAGrBnQ,KAAMuQ,EAAKvQ,IAAI,CACf0Q,YAAaH,EAAKG,WAAW,AACjC,IAER,GAIAlR,EAAQmR,SAAS,CAAG5R,AAxEN,IAAI,CAwEQ+P,WAAW,CAAC6B,SAAS,CAE/C,IAAMC,EAAY,IAAI7R,AA1ER,IAAI,CA0EUG,WAAW,CAACM,EAAST,AA1EnC,IAAI,CA0EqCyJ,QAAQ,EAyC/D,OAvCI0F,GACA,CAAC,QAAS,QAAS,SAAS,CAACxO,OAAO,CAAC,SAAU+Q,CAAI,EAC/C,IAAMI,EAAc,CAAC,CACjB3C,CAAAA,CAAY,CAACuC,EAAK,GAClBI,CAAW,CAACJ,EAAK,CAAGvC,CAAY,CAACuC,EAAK,CACtCG,EAAUrR,MAAM,CAACsR,GAEzB,GAGJ9R,AAtFc,IAAI,CAsFZuR,IAAI,CAAC5Q,OAAO,CAAC,SAAU6Q,CAAI,EAC7B,IAAMO,EAAWrJ,EAAKmJ,EAAUN,IAAI,CAAE,AAACS,GAASA,EAAKvR,OAAO,CAACgR,WAAW,GAAKD,EAAKzB,WAAW,CAAC0B,WAAW,EACzG,GAAIM,EAAU,CACV,IAAME,EAAWT,EAAKU,WAAW,GAKjCC,EAAiBlJ,EAAMkG,GAAc,CAACqC,EAAKE,IAAI,CAAC,EAAI,CAAC,EAAE,CAAC,EAAE,CAAEU,EAAU,QAASD,EAC3EA,EAAeE,GAAG,CAClBJ,EAASG,OAAO,CAAEE,EAAU,QAASH,EACrCA,EAAezF,GAAG,CAClBuF,EAASK,OAAO,CACf,CAAA,AAAoB,KAAA,IAAZF,GACTA,IAAYL,EAASM,GAAG,EAAM,AAAmB,KAAA,IAAZC,GACrCA,IAAYP,EAASrF,GAAG,GACxBqF,EAASQ,WAAW,CAACH,GAAW,KAAK,EAAGE,GAAW,KAAK,EAAG,CAAA,EAAM,CAAA,EAEzE,CACJ,GAEAlD,EAAMyC,EAAUpC,YAAY,CAACzP,AA3Gf,IAAI,CA2GiB+J,UAAU,EACzCtJ,EAAQM,SAAS,EAAE2O,kBACvB/G,EAAoB,IAAI,CAAE,SAAU,CAAEkJ,UAAWA,CAAU,GAC3DzC,EAAMpP,AA9GQ,IAAI,CA8GNwS,WAAW,CAACpD,EAAK3O,GAE7BA,EAAU,KACVoR,EAAU5C,OAAO,GACjBzG,EAAe8H,GACRlB,CACX,CAKA,SAASC,EAAgB5O,CAAO,CAAE0O,CAAY,EAC1C,IAAMsD,EAAwB,IAAI,CAAChS,OAAO,CAACM,SAAS,CACpD,OAAO,IAAI,CAACmP,MAAM,CAACrH,EAAM,CAAE7I,MAAO,CAAEsE,aAAc,CAAE,CAAE,EAAGmO,EAAsBtD,YAAY,CAAEA,EAAc,CACvGpO,UAAW,CACP0P,YAAc,AAAChQ,GAAWA,EAAQgQ,WAAW,EACzCgC,EAAsBhC,WAAW,CACrCG,aAAe,AAACnQ,GAAWA,EAAQmQ,YAAY,EAC3C6B,EAAsB7B,YAAY,AAC1C,CACJ,GACJ,CA2BA,SAASjB,IACL,IAEI+C,EAF6BC,EAAYpX,EAAU+N,eAAe,CACtEsJ,EAAgB,CAAC,EAIXC,EAAS1K,EAAcxJ,aAAa,CAAC,UAC3C4J,EAAIsK,EAAQ,CACRjP,MAAO,MACPH,OAAQ,MACRqP,WAAY,QAChB,GACA3K,EAAc7I,IAAI,CAACC,WAAW,CAACsT,GAC/B,IAAME,EAAaF,EAAOG,aAAa,EAAIH,EAAOG,aAAa,CAAC/V,QAAQ,CACpE8V,GACAA,EAAUzT,IAAI,CAACC,WAAW,CAACwT,EAAUE,eAAe,CAAC7K,EAAQ,SAyIjE8K,AAjIA,SAASA,EAAQzH,CAAI,EACjB,IACI0H,EAAQC,EAAcC,EAAOC,EAAYC,EAAanV,EADpDoV,EAAiB,CAAC,EAwDxB,GAAIT,GACAtH,AAAkB,IAAlBA,EAAKC,QAAQ,EACbnC,AAA4C,KAA5CA,EAAiBpK,OAAO,CAACsM,EAAKgI,QAAQ,EAAU,CAOhD,GANAN,EAAS9K,EAAcqL,gBAAgB,CAACjI,EAAM,MAC9C2H,EAAe3H,AAAkB,QAAlBA,EAAKgI,QAAQ,CACxB,CAAC,EACDpL,EAAcqL,gBAAgB,CAACjI,EAAKkI,UAAU,CAAE,MAGhD,CAACf,CAAa,CAACnH,EAAKgI,QAAQ,CAAC,CAAE,CAQ/Bf,EAAWK,EAAUa,oBAAoB,CAAC,MAAM,CAAC,EAAE,CACnDP,EAAQN,EAAUE,eAAe,CAACxH,EAAKoI,YAAY,CAAEpI,EAAKgI,QAAQ,EAClEf,EAASnT,WAAW,CAAC8T,GAGrB,IAAMvD,EAAIzH,EAAcqL,gBAAgB,CAACL,EAAO,MAAOS,EAAW,CAAC,EACnE,IAAK,IAAM5X,KAAO4T,EACV5T,EAAIyB,MAAM,CAAG,KACb,AAAkB,UAAlB,OAAOmS,CAAC,CAAC5T,EAAI,EACb,CAAC,QAAQ+C,IAAI,CAAC/C,IACd4X,CAAAA,CAAQ,CAAC5X,EAAI,CAAG4T,CAAC,CAAC5T,EAAI,AAAD,CAG7B0W,CAAAA,CAAa,CAACnH,EAAKgI,QAAQ,CAAC,CAAGK,EAGT,SAAlBrI,EAAKgI,QAAQ,EACb,OAAOb,EAAc9K,IAAI,CAAC7D,IAAI,CAElCyO,EAASjT,WAAW,CAAC4T,EACzB,CAEA,IAAK,IAAMU,KAAKZ,EAGZ,CAAA,AAACrW,IAA8CkX,SAAS,EACpD,AAAClX,IAA8CmX,IAAI,EACnD,AAACnX,IAA8CC,QAAQ,EAEvDX,OAAOO,cAAc,CAACC,IAAI,CAACuW,EAAQY,EAAC,GACpCG,AA5FZ,SAAsBC,CAAG,CAAE1X,CAAI,EAG3B,GADA6W,EAAaC,EAAc,CAAA,EACvBZ,EAAUhV,MAAM,CAAE,CAIlB,IADAS,EAAIuU,EAAUhV,MAAM,CACbS,KAAO,CAACmV,GACXA,EAAcZ,CAAS,CAACvU,EAAE,CAACa,IAAI,CAACxC,GAEpC6W,EAAa,CAACC,CAClB,CAMA,IAJa,cAAT9W,GAAwB0X,AAAQ,SAARA,GACxBb,CAAAA,EAAa,CAAA,CAAG,EAEpBlV,EAAIgW,AAlDKhL,EAkDIzL,MAAM,CACZS,KAAO,CAACkV,GAAY,CACvB,GAAI7W,EAAKkB,MAAM,CAAG,IACd,MAAM,AAAIoB,MAAM,kBAEpBuU,EAAcc,AAvDThL,CAuDiB,CAAChL,EAAE,CAACa,IAAI,CAACxC,IAC3B,AAAe,YAAf,OAAO0X,CACf,CACI,CAACb,GAIIF,CAAAA,CAAY,CAAC3W,EAAK,GAAK0X,GACxB1I,AAAkB,QAAlBA,EAAKgI,QAAQ,AAAS,GACtBb,CAAa,CAACnH,EAAKgI,QAAQ,CAAC,CAAChX,EAAK,GAAK0X,IAEnC,AAAC9K,GACDA,AAAqC,KAArCA,EAAmBlK,OAAO,CAAC1C,GAO3B+W,CAAc,CAAC/W,EAAK,CAAG0X,EANnBA,GACA1I,EAAK4I,YAAY,CAvFlC5X,AAuF6CA,EAvFxCgB,OAAO,CAAC,SAAU,SAAUC,CAAK,EACzC,MAAO,IAAMA,EAAMsS,WAAW,EAClC,GAqF2DmE,GASvD,EAgDyBhB,CAAM,CAACY,EAAE,CAAEA,GAShC,GALAxL,EAAIkD,EAAM+H,GAEY,QAAlB/H,EAAKgI,QAAQ,EACbhI,EAAK4I,YAAY,CAAC,eAAgB,OAElC5I,AAAkB,SAAlBA,EAAKgI,QAAQ,CACb,OAGJ,EAAE,CAAC9S,OAAO,CAAC/D,IAAI,CAAC6O,EAAK6I,QAAQ,EAAI7I,EAAKJ,UAAU,CAAE6H,EACtD,CACJ,EAUQ,IAAI,CAACjN,SAAS,CAACsO,aAAa,CAAC,QAJjC7B,EAASiB,UAAU,CAAClU,WAAW,CAACiT,GAEhCG,EAAOc,UAAU,CAAClU,WAAW,CAACoT,EAItC,CAIA,SAASjD,IACL,IAAM4E,EAAc,IAAI,CAACvO,SAAS,CAACwO,gBAAgB,CAAC,KAAMC,EAAkB,CAAC,QAAS,OAAQ,aAAc,SAAS,CACrHC,MAAMC,IAAI,CAACJ,GAAa7T,OAAO,CAAC,AAACqN,IAC7B0G,EAAgB/T,OAAO,CAAC,AAAC+J,IACrB,IAAMmK,EAAY7G,EAAQ8G,YAAY,CAACpK,GACnCmK,GAAWE,SAAS,SACpB/G,EAAQqG,YAAY,CAAC3J,EAAMgJ,iBAAiB1F,GAASgH,gBAAgB,CAACtK,GAE9E,EACJ,EACJ,CAWA,SAASc,EAAeyJ,CAAM,EAC1B,GAAM,CAAElI,mBAAAA,CAAkB,CAAE,CAAG,IAAI,CACnC,AAEAA,CAAAA,EACI,CACIA,EAAmBC,QAAQ,CAC3BD,EAAmBmI,kBAAkB,CACxC,CACD,CAAC,IAAI,CAACjP,SAAS,CAAC,AAAD,EAAGtF,OAAO,CAAC,SAAUwU,CAAG,EACvCF,EAAO1V,WAAW,CAAC4V,EACvB,EACJ,CAQA,SAASC,IACL,IAAMpV,EAAQ,IAAI,CAUlBQ,EAAS,CAAC/D,EAAMgE,EAASC,KACrBV,EAAMqV,gBAAgB,CAAG,CAAA,EACzBxM,EAAM,CAAA,EAAM7I,EAAMS,OAAO,CAAChE,EAAK,CAAEgE,GAC7BsI,EAAKrI,EAAQ,CAAA,IACbV,EAAMU,MAAM,EAEpB,CACAV,CAAAA,EAAMe,SAAS,CAAG,CACdP,OAAQ,SAAUC,CAAO,CAAEC,CAAM,EAC7BF,EAAO,YAAaC,EAASC,EACjC,CACJ,EAIAE,EACKb,OAAO,CAACC,GAAOC,UAAU,CACzBI,SAAS,CAAC,CAACI,EAASC,KACrBF,EAAO,aAAcC,EAASC,EAClC,EACJ,CAMA,SAAS4U,EAAmB,CAAEC,QAAAA,CAAO,CAAErZ,IAAAA,CAAG,CAAEsZ,aAAAA,CAAY,CAAE,EACtD,IAAM7N,EAAmB,IAAI,CAAClH,OAAO,CAACM,SAAS,CAAE,CAAEwC,MAAAA,CAAK,CAAEC,cAAAA,EAAgB,CAAC,CAAEG,cAAAA,CAAa,CAAEC,MAAAA,EAAQ,CAAC,CAAE,CAAGiF,EAAM,IAAI,CAACpI,OAAO,CAACR,UAAU,EAAEkD,cAAewE,GAAkBhG,SAASC,eAAgB6T,EAAQF,EAAQ3R,KAAK,CAAG4R,EAAcE,EAAc9R,EAAQJ,EAC1PmE,CAAAA,GAAkBmC,SAAW,CAAA,CAAG,GACjC5N,AAAQ,UAARA,GACAqH,AAAU,UAAVA,GACAI,AAAkB,QAAlBA,GACI8R,EAAQ,EAAIC,IACRD,EAAQC,EACRH,EAAQ3R,KAAK,EAAI8R,EAEZ,IAAI,CAAC/K,KAAK,EAAEgL,aAAe,QAChCJ,CAAAA,EAAQxQ,CAAC,EAAI2Q,EAAcD,EAAQ,CAAA,EAInD,CAkBA,SAAShT,IACL,IAAMzC,EAAQ,IAAI,EACdA,EAAM6L,UAAU,GAGpB1C,EAAgBnJ,EACX,AAAClD,IAA8CC,QAAQ,EACxDiD,EAAM+L,WAAW,GAIrB6B,WAAW,KACPvF,EAAcuN,KAAK,GACnBvN,EAAc5F,KAAK,GAEd,AAAC3F,IAA8CC,QAAQ,EACxD6Q,WAAW,KACP5N,EAAMmL,UAAU,EACpB,EAAG,IAEX,EAAG,GACP,CAOA,SAASiB,IACL,IAAMpM,EAAQ,IAAI,CAAE2H,EAAmB3H,EAAMS,OAAO,CAACM,SAAS,CAAEY,EAAUgG,EAAiBhG,OAAO,CAAEkU,EAAU7V,EAAMqV,gBAAgB,EAAI,CAACrV,EAAM6J,iBAAiB,AAChK7J,CAAAA,EAAMkL,YAAY,CAAG,EACjBlL,EAAMqV,gBAAgB,EACtBrV,EAAMqM,aAAa,GAEnBwJ,GAAWlO,AAA6B,CAAA,IAA7BA,EAAiBmC,OAAO,GACnC9J,EAAM2N,YAAY,CAAG,EAAE,CACvB3N,EAAMiL,cAAc,CAAGjL,EAAMiL,cAAc,EACvCjL,EAAM0J,QAAQ,CAACoM,CAAC,CAAC,mBAAmBpL,IAAI,CAAC,CACrCK,OAAQ,CACZ,GAAGC,GAAG,GACVlC,EAAWnH,EAAS,SAAUwI,CAAM,EAChCnK,EAAMwJ,SAAS,CAACW,EACpB,GACAnK,EAAMqV,gBAAgB,CAAG,CAAA,EAEjC,CAgBA,SAAS7C,EAAYpD,CAAG,CAAE3O,CAAO,EAC7B,IAAMsV,EAAQ3G,EAAIjQ,OAAO,CAAC,UAAY,EAAG6W,EAAmB5G,EAAIjQ,OAAO,CAAC,kBAAoB,GACxF8W,EAAO7G,EAAIa,MAAM,CAAC8F,GA+BtB,OA7BA3G,EAAMA,EAAIa,MAAM,CAAC,EAAG8F,GAChBC,EAEA5G,EAAMA,EAAI3R,OAAO,CAAC,2BAA4B,SAGzCwY,GAAQxV,GAASM,WAAWmV,YACjCD,EAAO,qCACSxV,EAAQT,KAAK,CAAC4D,KAAK,CAD5B,aAEUnD,EAAQT,KAAK,CAACyD,MAAM,CAF9B,gDAKHwS,EAAKxY,OAAO,CAAC,2BAA4B,SALtC,0BAQP2R,EAAMA,EAAI3R,OAAO,CAAC,SAAUwY,EAAO,WAEvC7G,EAAMA,EACD3R,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,sBAAuB,IAC/BA,OAAO,CAAC,qBAAsB,IAC9BA,OAAO,CAAC,uCAAwC,WAChDA,OAAO,CAAC,eAAgB,SACxBA,OAAO,CAAC,QAAS,oDACjBA,OAAO,CAAC,oBAAqB,gBAC7BA,OAAO,CAAC,OAAQ,KAEhBA,OAAO,CAAC,UAAW,QACnBA,OAAO,CAAC,SAAU,OAE3B,CAp1BAlC,EAAUwE,OAAO,CA7CjB,SAAiBgG,CAAU,CAAEV,CAAgB,EACzCG,EAA2BzF,OAAO,CAACsF,GACnC8Q,AAhXmDrQ,EAgX9B/F,OAAO,CAACgG,GAC7B,IAAMqQ,EAAarQ,EAAWrJ,SAAS,AAClC0Z,CAAAA,EAAWxT,WAAW,GACvBwT,EAAWjL,UAAU,CAAGA,EACxBiL,EAAWxT,WAAW,CAAGA,EACzBwT,EAAWzG,YAAY,CAAGA,EAC1ByG,EAAW3T,KAAK,CAAGA,EACnB2T,EAAW5D,WAAW,CAAGA,EACzB4D,EAAW3G,YAAY,CAAGA,EAC1B2G,EAAWlG,MAAM,CAAGA,EACpBkG,EAAW/G,eAAe,CAAGA,EAC7B+G,EAAW7G,WAAW,CAAGA,EACzB6G,EAAW5K,cAAc,CAAGA,EAC5B4K,EAAWrK,WAAW,CAAGA,EACzBqK,EAAWlM,WAAW,CAAGA,EACzBkM,EAAW5M,SAAS,CAAGA,EACvB4M,EAAW/J,aAAa,CAAGA,EAC3B+J,EAAWhK,eAAe,CAAGA,EAC7BgK,EAAWxG,mBAAmB,CAAGA,EACjCwG,EAAWC,SAAS,CAAC9V,IAAI,CAAC2L,GAC1B5D,EAAmBvC,EAAY,OAAQqP,GACvC9M,EAAmBvC,EAAY,cAAeuP,GAC1C,AAACxY,IAA8CC,QAAQ,EACvDsL,EAAciO,UAAU,CAAC,SAASC,WAAW,CAAC,SAAUC,CAAQ,EACvDrN,IAGDqN,EAASC,OAAO,CAChBtN,EAAc4C,WAAW,GAGzB5C,EAAcgC,UAAU,GAEhC,GAEJjD,EAAenH,SAAS,CAAG8H,EAAM/H,EAA4BC,SAAS,CAAEmH,EAAenH,SAAS,EAChGmH,EAAelF,IAAI,CAAG6F,EAAM/H,EAA4BkC,IAAI,CAAEkF,EAAelF,IAAI,EAIjFkF,EAAejI,UAAU,CAAG4I,EAAM/H,EAA4Bb,UAAU,CAAEiI,EAAejI,UAAU,EAE3G,CAs1BJ,EAAG1E,GAAcA,CAAAA,EAAY,CAAC,CAAA,GAMD,IAAMmb,EAAuBnb,EAoKvBob,EA1CF,CAC7BC,OAAQ,0CAGR1U,oBAAqB,CACjBS,YAAa,CACTP,QAAS,cACTC,QAAS,WACL,IAAI,CAACwU,gBAAgB,EACzB,CACJ,EACAhU,aAAc,CACVT,QAAS,eACTC,QAAS,WACL,IAAI,CAACwU,gBAAgB,CAAC,CAClB5V,KAAM,YACV,EACJ,CACJ,EACA8B,YAAa,CACTX,QAAS,cACTC,QAAS,WACL,IAAI,CAACwU,gBAAgB,CAAC,CAClB5V,KAAM,eACV,EACJ,CACJ,EACA6B,YAAa,CACTV,QAAS,cACTC,QAAS,WACL,IAAI,CAACwU,gBAAgB,CAAC,CAClB5V,KAAM,iBACV,EACJ,CACJ,CACJ,CACJ,EAwBM,CAAEiH,eAAgB4O,CAA+B,CAAE,CAAIha,IAEvD,CAAEyB,YAAawY,CAA4B,CAAE,CAAGzY,EAGhD,CAAEpB,IAAK8Z,CAAoB,CAAEha,IAAKia,CAAoB,CAAE,CAAIna,IAE5D,CAAEoa,KAAAA,CAAI,CAAE,CAAIjP,IAGZ,CAAEvC,SAAUyR,EAAyB,CAAEC,MAAAA,EAAK,CAAE3O,OAAQ4O,EAAuB,CAAE1R,UAAW2R,EAA0B,CAAEzO,MAAO0O,EAAsB,CAAE,CAAIza,IAC/JgD,IAAiD0X,iBAAiB,CAACjX,IAAI,CAAC,eAAgB,eAAgB,SAAU,KAAM,KAAM,mBAAoB,kBAAmB,iBAAkB,cAAe,YAAa,UAAW,UAAW,aAAc,QAAS,eAChQT,IAAiD2X,WAAW,CAAClX,IAAI,CAAC,OAAQ,WAAY,KAOtF,AAAC,SAAU/E,CAAgB,EAoWvB,SAASqb,EAAiBlP,CAAgB,CAAEwH,CAAY,EACpD,IAAMnP,EAAQ,IAAI,CAAES,EAAU8W,GAAuBvX,EAAMS,OAAO,CAACM,SAAS,CAAE4G,GAAmB+P,EAAyB,SAAUC,CAAG,EAC/HlX,AAAmC,CAAA,IAAnCA,EAAQiX,sBAAsB,CAC1BjX,EAAQ2W,KAAK,CACb3W,EAAQ2W,KAAK,CAAC3W,EAASkX,GAGvBP,GAAM,GAAI,CAAA,GAIdpX,EAAM4C,WAAW,CAACnC,EAE1B,EAiCA,GANI,AAAC3D,IAA8CmX,IAAI,EAAIjU,EAAM+J,UAAU,EAAI,CAAC2M,EAAoBpN,eAAe,CAAC3L,MAAM,EACtH+Y,EAAoBpN,eAAe,CAAC/I,IAAI,CAAC,aAAc,UAAW,cAAe,SAAU,cAAe,mBAAoB,YAAa,UAAW,SAAU,eAAgB,QAAS,cAAe,UAAW,cAAe,WAAY,WAAY,cAAe,OAAQ,OAAQ,UAAW,aAAc,cAAe,kBAAmB,aAAc,gBAAiB,cAAe,MAAO,OAKzY,AAAC,AAACzD,IAA8CmX,IAAI,EACnDxT,CAAAA,AAAiB,oBAAjBA,EAAQQ,IAAI,EACTjB,EAAMiG,SAAS,CAAC2N,oBAAoB,CAAC,SAASjW,MAAM,EAChD8C,AAAiB,kBAAjBA,EAAQQ,IAAI,AAAmB,GAAQR,AAAiB,oBAAjBA,EAAQQ,IAAI,EAnBpD,EAAE,CAAC2W,IAAI,CAAChb,IAAI,CAACoD,EAAMiG,SAAS,CAAC2N,oBAAoB,CAAC,SAAU,SAAUiE,CAAK,EAC9E,IAAMxY,EAAOwY,EAAM/C,YAAY,CAAC,QAChC,MAAQzV,AAAS,KAATA,GACJ,AAAgB,UAAhB,OAAOA,GACPA,AAA0B,IAA1BA,EAAKF,OAAO,CAAC,QACrB,GAesB,CACtBuY,EAAuB,AAAI3Y,MAAM,qDACjC,MACJ,CACAiB,EAAM8X,oBAAoB,CAACrX,EAAS0O,GAAgB,CAAC,EAAGuI,EAzCxC,SAAUtI,CAAG,EAIrBA,EAAIjQ,OAAO,CAAC,kBAAoB,IAChCsB,AAAiB,kBAAjBA,EAAQQ,IAAI,EACX,CAAA,AAACnE,IAA8CmX,IAAI,EAAIxT,AAAiB,oBAAjBA,EAAQQ,IAAI,AAAqB,EACzFyW,EAAuB,AAAI3Y,MAAM,2DAGjCvD,EAAiBuc,gBAAgB,CAAC3I,EAAKiI,GAAwB,CAAE7Y,SAAUwB,EAAMuP,WAAW,EAAG,EAAG9O,GAAUiX,EAAwB,IAAMJ,GAA2BtX,EAAO,2BAEpL,EA8BJ,CASA,SAASgY,EAAUC,CAAc,CAAExO,CAAQ,EACvC,IAAMyO,EAAOlB,EAAqBpD,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAEuE,EAASnB,EAAqBrY,aAAa,CAAC,SAC/GwZ,CAAAA,EAAOlX,IAAI,CAAG,kBACdkX,EAAOC,GAAG,CAAGH,EACbE,EAAOE,MAAM,CAAG5O,EAChB0O,EAAOG,OAAO,CAAG,WACblB,GAAM,wBAA0Ba,EACpC,EACAC,EAAK3Y,WAAW,CAAC4Y,EACrB,CAeA,SAASL,EAAqBrX,CAAO,CAAE0O,CAAY,CAAEoJ,CAAY,CAAEC,CAAe,EAC9E,IAAMxY,EAAQ,IAAI,CAGlByY,EAAW,AAACrJ,GAAQpP,EAAMwS,WAAW,CAACpD,EAAKsJ,GAE3CC,EAAY,KACJC,GAAUC,IAAmBC,GAC7BN,EAAgBC,EAASM,EAAmBlJ,SAAS,EAE7D,EAEAmJ,EAAkB,CAACC,EAAUC,EAAWC,KACpC,EAAEN,EAEFM,EAAaC,YAAY,CAACC,cAAc,CAAC,+BAAgC,OAAQJ,GACjFN,GACJ,EACIW,EAAIP,EAAoBL,EAAkBrZ,EAAO,KAAMuZ,EAAQE,EAAe,EAAGD,EAAiB,CAGtG7Y,CAAAA,EAAMuZ,YAAY,CAAGpC,GAA0BnX,EAAO,SAAU,AAACgK,IAC7D0O,EAAmB1O,EAAE6H,SAAS,CAACpR,OAAO,CAItCqY,EAAeF,AAFfA,CAAAA,EAASG,AADTA,CAAAA,EAAqB/O,EAAE6H,SAAS,CAAC5L,SAAS,CAACuT,SAAS,CAAC,CAAA,EAAI,GAC1BT,EAC1BnF,oBAAoB,CAAC,UAAY,EAAE,AAAD,EACjBjW,MAAM,AAChC,GAEAqC,EAAMqP,eAAe,CAAC5O,EAAS0O,GAC/B,GAAI,CAEA,GAAI,CAACyJ,GAAU,CAACA,EAAOjb,MAAM,CAAE,CAE3B6a,EAAgBC,EAASM,EAAmBlJ,SAAS,GACrD,MACJ,CAEA,IAAK,IAAIzR,EAAI,EAAGA,EAAIwa,EAAOjb,MAAM,CAAES,IAE/BiB,CAAAA,EAAOia,AADPA,CAAAA,EAAKV,CAAM,CAACxa,EAAE,AAAD,EACHqb,cAAc,CAAC,+BAAgC,OAAM,EAE3Dje,EAAiBke,cAAc,CAACra,EAAM,YAAa,CAAE+Z,aAAcE,CAAG,EAAG7Y,EAAQiB,KAAK,CAAEsX,EAExFT,EAEAA,EAEAA,IAIAM,IACAS,EAAG3F,UAAU,CAAClU,WAAW,CAAC6Z,GAC1Blb,IACAua,IAGZ,CACA,MAAO3O,EAAG,CACNuO,EAAavO,EACjB,CAEAhK,EAAMuZ,YAAY,EACtB,CAqCA,SAASG,EAAeT,CAAQ,CAAEC,CAAS,CAAEC,CAAY,CAAEzX,CAAK,CAAE8W,CAAe,CAAEmB,CAAe,CAAEC,CAAuB,CAAEC,CAAkB,CAAEC,CAAe,EAC5J,IAAIC,EAAM,IAAI9C,EAAqB+C,KAAK,CAAIC,EACtCC,EAAc,KAChBtM,WAAW,WACP,IACIrQ,EADE4c,EAASnD,EAAqBrY,aAAa,CAAC,UAAWyb,EAAMD,EAAOE,UAAU,EAAIF,EAAOE,UAAU,CAAC,MAE1G,GAAI,CACA,GAAKD,EAGA,CACDD,EAAO1W,MAAM,CAAGsW,EAAItW,MAAM,CAAG/B,EAC7ByY,EAAOvW,KAAK,CAAGmW,EAAInW,KAAK,CAAGlC,EAC3B0Y,EAAIE,SAAS,CAACP,EAAK,EAAG,EAAGI,EAAOvW,KAAK,CAAEuW,EAAO1W,MAAM,EAEpD,GAAI,CACAlG,EAAU4c,EAAOI,SAAS,CAACrB,GAC3BV,EAAgBjb,EAAS2b,EAAWC,EAAczX,EACtD,CACA,MAAOsI,EAAG,CACNiQ,EAAehB,EAAUC,EAAWC,EAAczX,EACtD,CACJ,MAdIkY,EAAwBX,EAAUC,EAAWC,EAAczX,EAenE,QACQ,CACAoY,GACAA,EAAgBb,EAAUC,EAAWC,EAAczX,EAE3D,CAGJ,EAAGlG,EAAiBgf,mBAAmB,CAC3C,EAEAC,EAAe,KACXZ,EAAmBZ,EAAUC,EAAWC,EAAczX,GAClDoY,GACAA,EAAgBb,EAAUC,EAAWC,EAAczX,EAE3D,EAIAuY,EAAiB,KACbF,EAAM,IAAI9C,EAAqB+C,KAAK,CACpCC,EAAiBN,EAEjBI,EAAIW,WAAW,CAAG,YAClBX,EAAI1B,MAAM,CAAG6B,EACbH,EAAIzB,OAAO,CAAGmC,EACdV,EAAI3B,GAAG,CAAGa,CACd,EACAc,EAAI1B,MAAM,CAAG6B,EACbH,EAAIzB,OAAO,CAAGmC,EACdV,EAAI3B,GAAG,CAAGa,CACd,CAQA,SAAS0B,EAAavL,CAAG,EAErB,IAAMtQ,EAAYmY,EAAqBvY,SAAS,CAACI,SAAS,CACpD8b,EAAU9b,EAAUK,OAAO,CAAC,UAAY,IAC1CL,AAA8B,EAA9BA,EAAUK,OAAO,CAAC,UACtB,GAAI,CAIA,GAAI,CAACyb,GAAUxL,AAAkC,KAAlCA,EAAIjQ,OAAO,CAAC,kBACvB,OAAO3D,EAAiB2B,MAAM,CAACa,eAAe,CAAC,IAAIiZ,EAAqBlZ,IAAI,CAAC,CAACqR,EAAI,CAAE,CAChFnO,KAAM,8BACV,GAER,CACA,MAAO+I,EAAG,CAEV,CACA,MAAO,oCAAsC6Q,mBAAmBzL,EACpE,CAMA,SAAS0L,EAASC,CAAU,CAAE7N,CAAM,CAAExL,CAAK,CAAE+H,CAAQ,EACjD,IAAM7F,EAAQ,AAACoX,CAAAA,OAAOD,EAAWjG,YAAY,CAAC,UAAY,EAAI5H,CAAK,EAC/DxL,EAAO+B,EAAS,AAACuX,CAAAA,OAAOD,EAAWjG,YAAY,CAAC,WAAa,EAAI5H,CAAK,EACtExL,EAAOuZ,EAAS,IAAIhE,EAAqBiE,KAAK,CAACC,KAAK,CAExD1X,EAASG,EAAQ,IAAM,IAAK,KAAM,CAACA,EAAOH,EAAO,EAIjD,EAAE,CAAC9C,OAAO,CAAC/D,IAAI,CAACme,EAAWtG,gBAAgB,CAAC,0BAA2B,SAAUhJ,CAAI,EACjFA,EAAKkI,UAAU,CAAClU,WAAW,CAACgM,EAChC,GAGA,IAAM2P,EAAYL,EAAWtG,gBAAgB,CAAC,kBAC9C,IAAK,IAAI4G,EAAQ,EAAGA,EAAQD,EAAUzd,MAAM,CAAE0d,IAAS,CAEnD,IAAMC,EAAQC,AADGH,CAAS,CAACC,EAAM,CACV5G,gBAAgB,CAAC,QACpCrW,EAAI,EACR,KAAOA,EAAIkd,EAAM3d,MAAM,EACnB2d,AAAoC,MAApCA,CAAK,CAACld,EAAE,CAAC0W,YAAY,CAAC,WACtBwG,AAAwC,MAAxCA,CAAK,CAACld,EAAI,EAAE,CAAC0W,YAAY,CAAC,WAC1BwG,CAAK,CAACld,EAAE,CAACod,MAAM,GACfpd,GAER,CAIA,EAAE,CAACuC,OAAO,CAAC/D,IAAI,CAACme,EAAWtG,gBAAgB,CAAC,SAAU,AAACgH,IACzB,MAAtBA,EAAMC,WAAW,GACjBD,EAAMC,WAAW,CAAG,IACpBD,EAAMpH,YAAY,CAAC,KAAM,IAEjC,GACA4G,EAAO7L,GAAG,CAAC2L,EAAY,CACnBhW,EAAG,EACHrB,EAAG,EACHE,MAAAA,EACAH,OAAAA,EACAkY,cAAe,CAAA,CACnB,GAAGC,IAAI,CAAC,IAAMnS,EAASwR,EAAOY,MAAM,CAAC,kBACzC,CAvpBArgB,EAAiBsgB,aAAa,CAAG,CAAC,EAAGtgB,EAAiB2B,MAAM,CAAG8Z,EAAqB7Z,GAAG,EAAI6Z,EAAqB5Z,SAAS,EAAI4Z,EAE7Hzb,EAAiBgf,mBAAmB,CAAG,AAAsD,MAAtD,AAAC1d,IAA8CmX,IAAI,CAqB1FzY,EAAiBuE,OAAO,CAVxB,SAAiBgG,CAAU,EACvB,IAAMqQ,EAAarQ,EAAWrJ,SAAS,CAOvC,OANK0Z,EAAWS,gBAAgB,GAC5BT,EAAW0B,oBAAoB,CAAGA,EAClC1B,EAAWS,gBAAgB,CAAGA,EAE9BU,GAAuB,CAAA,EAAMT,EAAgC/V,SAAS,CAAE4V,IAErE5Q,CACX,EA6SAvK,EAAiBuc,gBAAgB,CA9QjC,SAA0B3I,CAAG,CAAE3O,CAAO,CAAE8X,CAAY,CAAEC,CAAe,EACjE,IAAMuD,EAAoB/E,EAAqBrY,aAAa,CAAC,OAAQua,EAAYzY,EAAQQ,IAAI,EAAI,YAAazC,EAAY,AAACiC,CAAAA,EAAQjC,QAAQ,EAAI,OAAM,EACjJ,IACC0a,CAAAA,AAAc,kBAAdA,EACG,MAAQA,EAAUnD,KAAK,CAAC,IAAI,CAAC,EAAE,AAAD,EAAKrU,EAAQjB,EAAQiB,KAAK,EAAI,EAChEsa,EAAQC,EAAMC,EAAgBtF,EAAUnW,EAAQmW,MAAM,EAAIE,EAAgC/V,SAAS,CAAC6V,MAAM,CAAGuF,EAAkB,CAAA,EAAM/a,EAAUX,EAAQW,OAAO,CAElKwV,EAASA,AAAqB,MAArBA,EAAOwF,KAAK,CAAC,IAAcxF,EAAS,IAAMA,EAOnD,IAAMyF,EAAe,CAACtB,EAAYtR,KAM9B,IAsBI6S,EA3BiBxM,EAKfyM,EAAU,CAACC,EAASC,KACtBxF,EAAqBiE,KAAK,CAACC,KAAK,CAACuB,GAAG,CAACC,MAAM,CAACpc,IAAI,CAAC,CAC7C,cACA,WACI,IAAI,CAACqc,YAAY,CAACJ,EAASC,GAC3B,IAAI,CAACF,OAAO,CAACC,EAAS,iBAAkBA,GACnC,IAAI,CAACK,WAAW,GAAGC,cAAc,EAClC,IAAI,CAACC,OAAO,CAAC,iBAErB,EACH,CACL,EAGI3b,IAnBiB0O,EAmBOiL,EAAWW,WAAW,EAAI,IAjBtD,0BAA0Bzc,IAAI,CAAC6Q,KAkB3B1O,CAAAA,EAAU,KAAK,CAAA,EAGnB,IAAM4b,EAAW,CAAC,SAAU,SAAU,OAAQ,aAAa,CAKrDC,EAAsB,KACxB,IAAMT,EAAUQ,EAASE,KAAK,GAE9B,GAAI,CAACV,EACD,OAAO/S,IAEX,IAAMvI,EAAME,GAAWA,CAAO,CAACob,EAAQ,CACnCtb,EACAgW,EAAK,CACDhW,IAAAA,EACAic,aAAc,OACdC,QAAS,CAACrM,EAAMsM,KACZ,IAAMC,EAAS,IAAIC,UACnBD,CAAAA,EAAOE,SAAS,CAAG,WACf,GAAI,AAAuB,UAAvB,OAAO,IAAI,CAACC,MAAM,CAAe,CACjC,IAAMhB,EAAS,IAAI,CAACgB,MAAM,CAAC1H,KAAK,CAAC,IAAI,CAAC,EAAE,CACxCwG,EAAQC,EAASC,GACD,WAAZD,GACAF,CAAAA,EAAeG,CAAK,CAE5B,CACAQ,GACJ,EACAK,EAAOI,aAAa,CAACL,EAAIM,QAAQ,CACrC,EACAvG,MAAO6F,CACX,IAIIX,GACAC,EAAQC,EAASF,GAErBW,IAER,EACAA,GACJ,EAIMna,EAAc,SAoBZ8a,EAAeC,EAnBnB/d,IAAiD+H,cAAc,CAACkU,EAAmB3M,GACnF,IAAM0O,EAAe/B,EAAkBnI,oBAAoB,CAAC,QAI5DmK,EAA8B,SAAUzE,CAAE,CAAE0E,CAAQ,EAChD,IAAIC,EAAY3E,EAChB,KAAO2E,GAAaA,IAAclC,GAAmB,CACjD,GAAIkC,EAAUtS,KAAK,CAACqS,EAAS,CAAE,CAC3B,IAAIE,EAAQD,EAAUtS,KAAK,CAACqS,EAAS,AACpB,CAAA,aAAbA,GAA2B,MAAM/e,IAAI,CAACif,IACtCA,CAAAA,EAAQrT,KAAKC,KAAK,CAACqT,AAAoB,GAApBA,WAAWD,IAAe,IAAG,EAEpD5E,EAAG3N,KAAK,CAACqS,EAAS,CAAGE,EACrB,KACJ,CACAD,EAAYA,EAAUtK,UAAU,AACpC,CACJ,EAIA,EAAE,CAAChT,OAAO,CAAC/D,IAAI,CAACkhB,EAAc,SAAUxE,CAAE,EAsBtC,IAnBA,CAAC,aAAc,WAAW,CACrB3Y,OAAO,CAAC,AAACyd,IACVL,EAA4BzE,EAAI8E,EACpC,GACA9E,EAAG3N,KAAK,CAAC0S,UAAU,CAAGjd,GAAWA,EAAQC,MAAM,CAE3C,iBAEAzC,OAAO0a,EAAG3N,KAAK,CAAC0S,UAAU,EACtB/E,EAAG3N,KAAK,CAAC0S,UAAU,CAACtI,KAAK,CAAC,KAAKuI,MAAM,CAAC,KAG9CV,EAAgBtE,EAAG1F,oBAAoB,CAAC,SACxC,EAAE,CAACjT,OAAO,CAAC/D,IAAI,CAACghB,EAAe,SAAUW,CAAY,EACjDjF,EAAG7Z,WAAW,CAAC8e,EACnB,GAEAV,EACIvE,EAAGkF,sBAAsB,CAAC,2BACvBX,EAAgBlgB,MAAM,CAAG,GAAG,CAC/B,IAAM8gB,EAAUZ,CAAe,CAAC,EAAE,AAC9BY,CAAAA,EAAQ9K,UAAU,EAClB8K,EAAQ9K,UAAU,CAAClU,WAAW,CAACgf,EAEvC,CACJ,GACA,IAAMC,EAAU3C,EAAkBxH,aAAa,CAAC,OAC5CmK,GACArC,EAAaqC,EAAS,KAClB5D,EAAS4D,EAAS,EAAGhd,EAAO,AAACid,IACzB,GAAI,CACA5H,EAA6B4H,EAASngB,GAClCga,GACAA,GAER,CACA,MAAOxO,EAAG,CACNuO,EAAavO,EACjB,CACJ,EACJ,EAER,EAEA,GAAIkP,AAAc,kBAAdA,EAGA,GAAI,CACI,AAA8C,KAAA,IAAvCjC,EAAqB2H,aAAa,EAEzC3C,AADAA,CAAAA,EAAO,IAAIhF,EAAqB2H,aAAa,AAAC,EACzCC,MAAM,CAACzP,GACZ4M,EAASC,EAAK6C,OAAO,CAAC,kBAGtB9C,EAASrB,EAAavL,GAE1B2H,EAA6BiF,EAAQxd,GACjCga,GACAA,GAER,CACA,MAAOxO,EAAG,CACNuO,EAAavO,EACjB,KAEKkP,AAAc,oBAAdA,EACDjC,EAAqBiE,KAAK,EAAIjE,EAAqBiE,KAAK,CAACC,KAAK,CAC9DrY,KAMAqZ,EAAkB,CAAA,EAClBnE,EAAUpB,EAAS,WAAY,WAC3BoB,EAAUpB,EAAS,aAAc9T,EACrC,KAKJkZ,EAASrB,EAAavL,GACtB8M,EAAiB,WACb,GAAI,CACA1gB,EAAiB2B,MAAM,CAAC4hB,eAAe,CAAC/C,EAC5C,CACA,MAAOhS,EAAG,CAEV,CACJ,EAEA0P,EAAesC,EAAQ9C,EAAW,CAAC,EAAGxX,EAAO,SAAUuX,CAAQ,EAE3D,GAAI,CACAlC,EAA6BkC,EAAUza,GACnCga,GACAA,GAER,CACA,MAAOxO,EAAG,CACNuO,EAAavO,EACjB,CACJ,EAAG,WACC,GAAIoF,EAAIzR,MAAM,CAAG,IACb,MAAM,AAAIoB,MAAM,kBAIpB,IAAMob,EAASnD,EAAqBrY,aAAa,CAAC,UAAWyb,EAAMD,EAAOE,UAAU,CAAC,MAAO2E,EAAoB5P,EAAI1R,KAAK,CAEzH,gEAAiEuhB,EAAqB7P,EAAI1R,KAAK,CAE/F,kEACA,GAAI0c,GAAO4E,GAAqBC,EAAoB,CAChD,IAAMC,EAAa,CAACF,CAAiB,CAAC,EAAE,CAAGtd,EAAOyd,EAAc,CAACF,CAAkB,CAAC,EAAE,CAAGvd,EAAO0d,EAAoB,KAEhHC,AADUpI,EAAqBqI,KAAK,CAACC,KAAK,CAACC,UAAU,CAACpF,EAAKhL,GACzDqQ,KAAK,GACP,GAAI,CACA1I,EAA6BE,EAAqBvY,SAAS,CAACG,gBAAgB,CACxEsb,EAAOuF,QAAQ,GACfvF,EAAOI,SAAS,CAACrB,GAAY1a,GAC7Bga,GACAA,GAER,CACA,MAAOxO,EAAG,CACNuO,EAAavO,EACjB,QACQ,CACJkS,GACJ,CACJ,CACA/B,CAAAA,EAAOvW,KAAK,CAAGsb,EACf/E,EAAO1W,MAAM,CAAG0b,EACZlI,EAAqBqI,KAAK,CAE1BF,KAOAjD,EAAkB,CAAA,EAClBnE,EAAUpB,EAAS,WAAYwI,GAEvC,CACJ,EAEA7G,EAEAA,EAEA,WACQ4D,GACAD,GAER,GAER,EAgGA1gB,EAAiBwc,SAAS,CAAGA,EA2K7Bxc,EAAiBke,cAAc,CAAGA,EA2BlCle,EAAiBmf,YAAY,CAAGA,EAgDhCnf,EAAiBsf,QAAQ,CAAGA,CAChC,EAAGtf,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAMmkB,GAAqCnkB,EAI3DC,EAAoBK,CAAC,CAAzB,CAAC,EAIwE,CAAG,GAQrF,IAAM8jB,GAAK9iB,GAEX8iB,CAAAA,GAAEtiB,aAAa,CAAGsiB,GAAEtiB,aAAa,EAAIgB,EAAuBhB,aAAa,CACzEsiB,GAAE7H,gBAAgB,CAAG4H,GAAkC5H,gBAAgB,CACvE6H,GAAErhB,WAAW,CAAGqhB,GAAErhB,WAAW,EAAID,EAAuBC,WAAW,CAEnEohB,GAAkC5f,OAAO,CAAC6f,GAAEC,KAAK,EACpB,IAAMC,GAA0BhjB,WAEpDgjB,MAAyBC,OAAO"}