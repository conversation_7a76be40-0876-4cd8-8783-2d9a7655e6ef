import*as e from"../highcharts.js";var t={};t.n=e=>{var i=e&&e.__esModule?()=>e.default:()=>e;return t.d(i,{a:i}),i},t.d=(e,i)=>{for(var n in i)t.o(i,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:i[n]})},t.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let i=e.default;var n=t.n(i);let{clamp:s,defined:o,pick:a}=n();function l(e,t){return(.2414*e-.2414)*Math.log(t)+(3.5-1.7*e)/1.8}function r(e,t,i){e.gain.cancelScheduledValues(t),e.gain.setTargetAtTime(i,t,m.stopRampTime/4),e.gain.setValueAtTime(i,t+m.stopRampTime)}function h(e,t,i,n,s=1){let o="attack"===t,a=n.gain;if(a.cancelScheduledValues(i),!e.length){r(n,i,o?s:0);return}e[0].t>1&&e.unshift({t:0,vol:+!o}),e.forEach((t,n)=>{let o=e[n-1],l=o?(t.t-o.t)/1e3:0,r=i+(o?o.t/1e3+m.stopRampTime:0);a.setTargetAtTime(t.vol*s,r,Math.max(l,m.stopRampTime)/2)})}class c{constructor(e,t){this.pulseWidth=Math.min(Math.max(0,t.pulseWidth||.5));let i=()=>new OscillatorNode(e,{type:"sawtooth",detune:t.detune,frequency:Math.max(1,t.frequency||350)});this.sawOscA=i(),this.sawOscB=i(),this.phaseInverter=new GainNode(e,{gain:-1}),this.masterGain=new GainNode(e),this.delayNode=new DelayNode(e,{delayTime:this.pulseWidth/this.sawOscA.frequency.value}),this.sawOscA.connect(this.masterGain),this.sawOscB.connect(this.phaseInverter),this.phaseInverter.connect(this.delayNode),this.delayNode.connect(this.masterGain)}connect(e){this.masterGain.connect(e)}getFrequencyFacade(){let e=this;return{cancelScheduledValues:t=>(e.sawOscA.frequency.cancelScheduledValues(t),e.sawOscB.frequency.cancelScheduledValues(t),e.delayNode.delayTime.cancelScheduledValues(t),e.sawOscA.frequency),setValueAtTime(t,i){return this.cancelScheduledValues(i),e.sawOscA.frequency.setValueAtTime(t,i),e.sawOscB.frequency.setValueAtTime(t,i),e.delayNode.delayTime.setValueAtTime(Math.round(1e4*e.pulseWidth/t)/1e4,i),e.sawOscA.frequency},setTargetAtTime(t,i,n){return this.cancelScheduledValues(i),e.sawOscA.frequency.setTargetAtTime(t,i,n),e.sawOscB.frequency.setTargetAtTime(t,i,n),e.delayNode.delayTime.setTargetAtTime(Math.round(1e4*e.pulseWidth/t)/1e4,i,n),e.sawOscA.frequency}}}getPWMTarget(){return this.delayNode.delayTime}start(){this.sawOscA.start(),this.sawOscB.start()}stop(e){this.sawOscA.stop(e),this.sawOscB.stop(e)}}class u{constructor(e,t,i){this.audioContext=e,this.options=t,this.fmOscillatorIx=t.fmOscillator,this.vmOscillatorIx=t.vmOscillator,this.createSoundSource(),this.createGain(),this.createFilters(),this.createVolTracking(),i&&this.connect(i)}connect(e){[this.lowpassNode,this.highpassNode,this.volTrackingNode,this.vmNode,this.gainNode,this.whiteNoise,this.pulseNode,this.oscNode].reduce((e,t)=>t?(t.connect(e),t):e,e)}start(){this.oscNode&&this.oscNode.start(),this.whiteNoise&&this.whiteNoise.start(),this.pulseNode&&this.pulseNode.start()}stopAtTime(e){this.oscNode&&this.oscNode.stop(e),this.whiteNoise&&this.whiteNoise.stop(e),this.pulseNode&&this.pulseNode.stop(e)}setFreqAtTime(e,t,i=0){let n=this.options,o=s(a(n.fixedFrequency,t)*(n.freqMultiplier||1),0,21e3),l=this.getOscTarget(),r=i/5e3;l&&(l.cancelScheduledValues(e),i&&e-(this.lastUpdateTime||-1)>.01?(l.setTargetAtTime(o,e,r),l.setValueAtTime(o,e+r)):l.setValueAtTime(o,e)),this.scheduleVolTrackingChange(o,e,i),this.scheduleFilterTrackingChange(o,e,i),this.lastUpdateTime=e}getFMTarget(){return this.oscNode&&this.oscNode.detune||this.whiteNoise&&this.whiteNoise.detune||this.pulseNode&&this.pulseNode.getPWMTarget()}getVMTarget(){return this.vmNode&&this.vmNode.gain}runEnvelopeAtTime(e,t){this.gainNode&&h(("attack"===e?this.options.attackEnvelope:this.options.releaseEnvelope)||[],e,t,this.gainNode,this.options.volume)}cancelScheduled(){this.gainNode&&this.gainNode.gain.cancelScheduledValues(this.audioContext.currentTime);let e=this.getOscTarget();e&&e.cancelScheduledValues(0),this.lowpassNode&&this.lowpassNode.frequency.cancelScheduledValues(0),this.highpassNode&&this.highpassNode.frequency.cancelScheduledValues(0),this.volTrackingNode&&this.volTrackingNode.gain.cancelScheduledValues(0)}scheduleVolTrackingChange(e,t,i){if(this.volTrackingNode){let n=l(this.options.volumePitchTrackingMultiplier||1,e),s=i?i/1e3:m.stopRampTime;this.volTrackingNode.gain.cancelScheduledValues(t),this.volTrackingNode.gain.setTargetAtTime(n,t,s/5),this.volTrackingNode.gain.setValueAtTime(n,t+s)}}scheduleFilterTrackingChange(e,t,i){let n=this.options,o=i?i/1e3:m.stopRampTime,a=(i,n)=>{let a=l(n.frequencyPitchTrackingMultiplier||1,e),r=s((n.frequency||1e3)*a,0,21e3);i.frequency.cancelScheduledValues(t),i.frequency.setTargetAtTime(r,t,o/5),i.frequency.setValueAtTime(r,t+o)};this.lowpassNode&&n.lowpass&&a(this.lowpassNode,n.lowpass),this.highpassNode&&n.highpass&&a(this.highpassNode,n.highpass)}createGain(){let e=this.options;(o(e.volume)||e.attackEnvelope&&e.attackEnvelope.length||e.releaseEnvelope&&e.releaseEnvelope.length)&&(this.gainNode=new GainNode(this.audioContext,{gain:a(e.volume,1)})),this.vmNode=new GainNode(this.audioContext)}createSoundSource(){let e=this.options,t=this.audioContext,i=(e.fixedFrequency||0)*(e.freqMultiplier||1);if("whitenoise"===e.type){let e=2*t.sampleRate,i=t.createBuffer(1,e,t.sampleRate),n=i.getChannelData(0);for(let t=0;t<e;++t)n[t]=1.2*Math.random()-.6;let s=this.whiteNoise=t.createBufferSource();s.buffer=i,s.loop=!0}else"pulse"===e.type?this.pulseNode=new c(t,{detune:e.detune,pulseWidth:e.pulseWidth,frequency:i}):this.oscNode=new OscillatorNode(t,{type:e.type||"sine",detune:e.detune,frequency:i})}createFilters(){let e=this.options;e.lowpass&&e.lowpass.frequency&&(this.lowpassNode=new BiquadFilterNode(this.audioContext,{type:"lowpass",Q:e.lowpass.Q||1,frequency:e.lowpass.frequency})),e.highpass&&e.highpass.frequency&&(this.highpassNode=new BiquadFilterNode(this.audioContext,{type:"highpass",Q:e.highpass.Q||1,frequency:e.highpass.frequency}))}createVolTracking(){let e=this.options;e.volumePitchTrackingMultiplier&&1!==e.volumePitchTrackingMultiplier&&(this.volTrackingNode=new GainNode(this.audioContext,{gain:1}))}getOscTarget(){return this.oscNode?this.oscNode.frequency:this.pulseNode&&this.pulseNode.getFrequencyFacade()}}class m{constructor(e,t){this.audioContext=e,this.options=t,this.eqNodes=[],this.midiInstrument=t.midiInstrument||1,this.outputNode=new GainNode(e,{gain:0}),this.createEqChain(this.outputNode);let i=this.eqNodes.length?this.eqNodes[0]:this.outputNode;this.oscillators=(this.options.oscillators||[]).map(t=>new u(e,t,o(t.fmOscillator)||o(t.vmOscillator)?void 0:i)),this.oscillators.forEach(e=>{let t=(t,i)=>{if(i){let n=i[t]();n&&e.connect(n)}};o(e.fmOscillatorIx)&&t("getFMTarget",this.oscillators[e.fmOscillatorIx]),o(e.vmOscillatorIx)&&t("getVMTarget",this.oscillators[e.vmOscillatorIx])})}startSilently(){this.outputNode.gain.value=0,this.oscillators.forEach(e=>e.start())}stop(){let e=this.audioContext.currentTime,t=e+m.stopRampTime;r(this.outputNode,e,0),this.oscillators.forEach(e=>e.stopAtTime(t)),this.outputNode.disconnect()}silenceAtTime(e){if(!e&&this.outputNode.gain.value<.01){this.outputNode.gain.value=0;return}this.releaseAtTime((e||0)+this.audioContext.currentTime)}mute(){this.cancelScheduled(),r(this.outputNode,this.audioContext.currentTime,0)}playFreqAtTime(e,t,i){let n=(e||0)+this.audioContext.currentTime,s=this.options;this.oscillators.forEach(e=>{e.setFreqAtTime(n,t,s.noteGlideDuration),e.runEnvelopeAtTime("attack",n)}),h(s.masterAttackEnvelope||[],"attack",n,this.outputNode,s.masterVolume),i&&this.releaseAtTime(n+i/1e3)}cancelScheduled(){this.outputNode.gain.cancelScheduledValues(this.audioContext.currentTime),this.oscillators.forEach(e=>e.cancelScheduled())}connect(e){return this.outputNode.connect(e)}createEqChain(e){this.eqNodes=(this.options.eq||[]).map(e=>new BiquadFilterNode(this.audioContext,{type:"peaking",...e})),this.eqNodes.reduceRight((e,t)=>(t.connect(e),t),e)}releaseAtTime(e){let t=0;this.oscillators.forEach(i=>{let n=i.options.releaseEnvelope;n&&n.length&&(t=Math.max(t,n[n.length-1].t),i.runEnvelopeAtTime("release",e))});let i=this.options.masterReleaseEnvelope||[];i.length&&(h(i,"release",e,this.outputNode,this.options.masterVolume),t=Math.max(t,i[i.length-1].t)),r(this.outputNode,e+t/1e3,0)}}m.stopRampTime=.012;let p={piano:{masterVolume:.45,masterAttackEnvelope:[{t:1,vol:.71},{t:40,vol:.79},{t:82,vol:.64},{t:147,vol:.29},{t:260,vol:.15},{t:417,vol:.05},{t:589,vol:0}],eq:[{frequency:200,Q:.7,gain:6},{frequency:450,gain:6},{frequency:1300,gain:2},{frequency:2600,Q:.8,gain:8},{frequency:3500,Q:.8,gain:6},{frequency:6200,Q:.8,gain:10},{frequency:8e3,gain:-23},{frequency:1e4,Q:.4,gain:-12}],oscillators:[{type:"pulse",volume:.5,pulseWidth:.55,volumePitchTrackingMultiplier:.1,lowpass:{frequency:4.5,frequencyPitchTrackingMultiplier:900,Q:-2},highpass:{frequency:270},attackEnvelope:[{t:1,vol:1}],releaseEnvelope:[{t:1,vol:1},{t:282,vol:.64},{t:597,vol:0}]},{type:"whitenoise",volume:.8,lowpass:{frequency:400},highpass:{frequency:300},attackEnvelope:[{t:1,vol:1},{t:19,vol:0}]}]},plucked:{masterVolume:.5,midiInstrument:25,masterAttackEnvelope:[{t:1,vol:.71},{t:4,vol:.71},{t:31,vol:.4},{t:109,vol:.12},{t:234,vol:.04},{t:442,vol:0}],eq:[{frequency:800,gain:-8},{frequency:1400,Q:4,gain:4},{frequency:1600,gain:-14},{frequency:2200,gain:-8},{frequency:3600,gain:-2},{frequency:6400,Q:2,gain:-6}],oscillators:[{type:"sawtooth",volume:.9,volumePitchTrackingMultiplier:.6,highpass:{frequency:100},lowpass:{frequency:8e3},releaseEnvelope:[{t:1,vol:1},{t:315,vol:.56},{t:550,vol:0}]}]},flute:{masterVolume:1.1,midiInstrument:74,noteGlideDuration:30,masterAttackEnvelope:[{t:0,vol:0},{t:29,vol:1},{t:76,vol:.48},{t:600,vol:.36}],masterReleaseEnvelope:[{t:1,vol:.36},{t:24,vol:.15},{t:119,vol:0}],eq:[{frequency:150,Q:.6,gain:-10},{frequency:500,gain:4},{frequency:1100,gain:-4},{frequency:2200,gain:-14},{frequency:5e3,gain:8},{frequency:6400,gain:10},{frequency:8e3,gain:12},{frequency:10800,gain:8}],oscillators:[{type:"triangle",volume:1,volumePitchTrackingMultiplier:.4,lowpass:{frequency:12,frequencyPitchTrackingMultiplier:100},highpass:{frequency:200}},{type:"sine",fixedFrequency:5,volume:.2,vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:48,vol:0},{t:225,vol:.05},{t:600,vol:.77}]},{type:"whitenoise",volume:.13,lowpass:{frequency:9e3,Q:3},highpass:{frequency:6e3,Q:3},vmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:26,vol:1},{t:93,vol:.8}]}]},lead:{masterVolume:1,midiInstrument:20,masterAttackEnvelope:[{t:1,vol:.81},{t:98,vol:.5},{t:201,vol:.18},{t:377,vol:.04},{t:586,vol:0},{t:586,vol:0}],eq:[{frequency:200,gain:-6},{frequency:400,gain:-8},{frequency:800,Q:.5,gain:-10},{frequency:1200,gain:4},{frequency:3600,gain:-4},{frequency:4200,gain:-12},{frequency:7400,gain:-14},{frequency:1e4,gain:2}],oscillators:[{type:"triangle",volume:1.1,volumePitchTrackingMultiplier:.6,lowpass:{frequency:5e3},highpass:{frequency:100}},{type:"sawtooth",volume:.4,lowpass:{frequency:7e3},highpass:{frequency:800,Q:6},releaseEnvelope:[{t:0,vol:.99},{t:200,vol:.83},{t:495,vol:0}]}]},vibraphone:{masterVolume:1,midiInstrument:12,masterAttackEnvelope:[{t:1,vol:0},{t:10,vol:.63},{t:82,vol:.64},{t:149,vol:.26},{t:600,vol:0}],eq:[{frequency:200,Q:.8,gain:-12},{frequency:400,gain:-4},{frequency:1600,Q:.5,gain:6},{frequency:2200,Q:.5,gain:6},{frequency:6400,gain:4},{frequency:12800,gain:4}],oscillators:[{type:"sine",volume:1.5,volumePitchTrackingMultiplier:1e-7,attackEnvelope:[{t:1,vol:1}],releaseEnvelope:[{t:1,vol:1},{t:146,vol:.39},{t:597,vol:0}]},{type:"whitenoise",volume:.03,volumePitchTrackingMultiplier:1e-4,lowpass:{frequency:900},highpass:{frequency:800},attackEnvelope:[{t:1,vol:1},{t:9,vol:0}]},{type:"sine",freqMultiplier:4,volume:.15,volumePitchTrackingMultiplier:1e-4},{type:"sine",fixedFrequency:3,volume:6,fmOscillator:0,releaseEnvelope:[{t:1,vol:1},{t:190,vol:.41},{t:600,vol:0}]},{type:"sine",fixedFrequency:6,volume:3,fmOscillator:2},{type:"sine",freqMultiplier:9,volume:5e-4,volumePitchTrackingMultiplier:1e-4,releaseEnvelope:[{t:1,vol:.97},{t:530,vol:0}]}]},saxophone:{masterVolume:1,midiInstrument:67,noteGlideDuration:10,masterAttackEnvelope:[{t:1,vol:.57},{t:35,vol:1},{t:87,vol:.84},{t:111,vol:.6},{t:296,vol:.49},{t:600,vol:.58}],masterReleaseEnvelope:[{t:1,vol:.58},{t:47,vol:.16},{t:119,vol:0}],eq:[{frequency:200,gain:-2},{frequency:600,gain:2},{frequency:800,gain:-10},{frequency:1100,gain:-2},{frequency:2200,gain:-2},{frequency:3500,gain:10},{frequency:12800,gain:4}],oscillators:[{type:"sawtooth",volume:.45,volumePitchTrackingMultiplier:.06,lowpass:{frequency:18,frequencyPitchTrackingMultiplier:200},highpass:{frequency:300}},{type:"whitenoise",fixedFrequency:1,volume:.4,highpass:{frequency:7e3},vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:51,vol:1},{t:86,vol:.84},{t:500,vol:.78}]},{type:"sine",fixedFrequency:4,volume:2,fmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:15,vol:.94},{t:79,vol:1},{t:172,vol:.47},{t:500,vol:.26}]},{type:"sine",fixedFrequency:7,volume:6,fmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:25,vol:.99},{t:85,vol:0},{t:85,vol:0},{t:387,vol:.02},{t:511,vol:.43},{t:600,vol:0}]}]},trumpet:{masterVolume:.3,midiInstrument:57,noteGlideDuration:40,masterAttackEnvelope:[{t:1,vol:0},{t:17,vol:1},{t:42,vol:.85},{t:76,vol:1},{t:202,vol:.65},{t:226,vol:.86},{t:282,vol:.63}],masterReleaseEnvelope:[{t:1,vol:.62},{t:34,vol:.14},{t:63,vol:.21},{t:96,vol:0}],eq:[{frequency:200,Q:.6,gain:10},{frequency:600,Q:.5,gain:6},{frequency:1500,Q:.7,gain:14},{frequency:3200,Q:2,gain:8},{frequency:3800,Q:.8,gain:10},{frequency:6200,gain:12},{frequency:8400,gain:-20},{frequency:12800,Q:.5,gain:-18}],oscillators:[{type:"sawtooth",volume:.15,pulseWidth:.5,volumePitchTrackingMultiplier:.5,lowpass:{frequency:1900,Q:3}},{type:"sine",fixedFrequency:6,volume:.2,vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:102,vol:.13},{t:556,vol:.24}]},{type:"whitenoise",volume:.45,highpass:{frequency:7e3,Q:9},vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:89,vol:.51},{t:577,vol:.29}]},{type:"sine",fixedFrequency:5.7,volume:20,fmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:89,vol:1},{t:137,vol:.46},{t:283,vol:.15},{t:600,vol:.28}]}]},sawsynth:{masterVolume:.3,midiInstrument:51,noteGlideDuration:40,masterAttackEnvelope:[{t:0,vol:.6},{t:9,vol:1},{t:102,vol:.48}],eq:[{frequency:200,gain:-6}],oscillators:[{type:"sawtooth",volume:.4,volumePitchTrackingMultiplier:.3},{type:"sawtooth",volume:.4,detune:11,volumePitchTrackingMultiplier:.3},{type:"sawtooth",volume:.4,detune:-11,volumePitchTrackingMultiplier:.3}]},basic1:{masterVolume:1,noteGlideDuration:0,masterReleaseEnvelope:[{t:1,vol:.36},{t:24,vol:.15},{t:119,vol:0}],eq:[{frequency:150,Q:.6,gain:-12},{frequency:1100,gain:-2},{frequency:2200,gain:-16},{frequency:5e3,gain:8},{frequency:6400,gain:10},{frequency:8e3,gain:12},{frequency:10800,gain:8}],oscillators:[{type:"triangle",volume:1,volumePitchTrackingMultiplier:.05,lowpass:{frequency:17,frequencyPitchTrackingMultiplier:100},highpass:{frequency:200}},{type:"whitenoise",volume:.04,lowpass:{frequency:9e3,Q:3},highpass:{frequency:6e3,Q:3},vmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:26,vol:1},{t:71,vol:.73}]}]},basic2:{masterVolume:.3,eq:[{frequency:200,Q:.7,gain:6},{frequency:450,gain:2},{frequency:1300,gain:-2},{frequency:2600,Q:.8,gain:6},{frequency:3500,Q:.8,gain:6},{frequency:6200,Q:.8,gain:10},{frequency:8e3,gain:-18},{frequency:1e4,Q:.4,gain:-12}],oscillators:[{type:"pulse",volume:.4,pulseWidth:.55,volumePitchTrackingMultiplier:.1,lowpass:{frequency:4.5,frequencyPitchTrackingMultiplier:900,Q:-2},highpass:{frequency:270}}]},chord:{masterVolume:1,masterAttackEnvelope:[{t:1,vol:.79},{t:27,vol:.86},{t:62,vol:.81},{t:150,vol:.35},{t:408,vol:.04},{t:600,vol:0}],eq:[{frequency:200,gain:-8},{frequency:600,Q:2,gain:4},{frequency:800,gain:-10},{frequency:1600,gain:-2},{frequency:2200,gain:-6},{frequency:3600,Q:.7,gain:-2},{frequency:6400,gain:6},{frequency:12800,gain:6}],oscillators:[{type:"triangle",volume:1.1,volumePitchTrackingMultiplier:.05,lowpass:{frequency:8e3},highpass:{frequency:100},releaseEnvelope:[{t:1,vol:1},{t:315,vol:.56},{t:540,vol:0}]},{type:"triangle",freqMultiplier:1.17,volume:.4,volumePitchTrackingMultiplier:.07,lowpass:{frequency:5e3},highpass:{frequency:100},releaseEnvelope:[{t:0,vol:1},{t:476,vol:0}]},{type:"triangle",freqMultiplier:1.58333,volume:.7,volumePitchTrackingMultiplier:.02,highpass:{frequency:200},releaseEnvelope:[{t:0,vol:1},{t:422,vol:.56},{t:577,vol:0}]},{type:"sine",fixedFrequency:10,volume:4,fmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:157,vol:.65}]},{type:"sine",fixedFrequency:5,volume:.3,vmOscillator:2,attackEnvelope:[{t:1,vol:1},{t:155,vol:.91},{t:289,vol:.78}]}]},wobble:{masterVolume:.9,masterReleaseEnvelope:[{t:1,vol:.36},{t:24,vol:.15},{t:119,vol:0}],eq:[{frequency:150,Q:.6,gain:-12},{frequency:1100,gain:-2},{frequency:2200,gain:-16},{frequency:5e3,gain:8},{frequency:6400,gain:10},{frequency:8e3,gain:12},{frequency:10800,gain:8}],oscillators:[{type:"triangle",volume:.9,volumePitchTrackingMultiplier:.1,lowpass:{frequency:17,frequencyPitchTrackingMultiplier:100},highpass:{frequency:200}},{type:"whitenoise",volume:.04,lowpass:{frequency:9e3,Q:3},highpass:{frequency:6e3,Q:3},vmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:26,vol:1},{t:71,vol:.73}]},{type:"sine",freqMultiplier:.011,volume:30,fmOscillator:0}]},sine:{masterVolume:1,oscillators:[{type:"sine",volumePitchTrackingMultiplier:.07}]},sineGlide:{masterVolume:1,noteGlideDuration:100,oscillators:[{type:"sine",volumePitchTrackingMultiplier:.07}]},triangle:{masterVolume:.5,oscillators:[{type:"triangle",volume:1,volumePitchTrackingMultiplier:.07}]},sawtooth:{masterVolume:.25,midiInstrument:82,oscillators:[{type:"sawtooth",volume:.3,volumePitchTrackingMultiplier:.07}]},square:{masterVolume:.3,midiInstrument:81,oscillators:[{type:"square",volume:.2,volumePitchTrackingMultiplier:.07}]},chop:{masterVolume:1,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:44,vol:0}],oscillators:[{type:"whitenoise",volume:1,lowpass:{frequency:600},highpass:{frequency:200}}]},shaker:{masterVolume:.4,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:44,vol:0}],oscillators:[{type:"whitenoise",volume:1,lowpass:{frequency:6500},highpass:{frequency:5e3}}]},step:{masterVolume:1,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:44,vol:0}],eq:[{frequency:200,gain:-1},{frequency:400,gain:-14},{frequency:800,gain:8},{frequency:1e3,Q:5,gain:-24},{frequency:1600,gain:8},{frequency:2200,gain:-10},{frequency:5400,gain:4},{frequency:12800,gain:-36}],oscillators:[{type:"whitenoise",volume:1.5,lowpass:{frequency:300},highpass:{frequency:100,Q:6}}]},kick:{masterVolume:.55,masterAttackEnvelope:[{t:1,vol:.8},{t:15,vol:1},{t:45,vol:.35},{t:121,vol:.11},{t:242,vol:0}],eq:[{frequency:50,gain:6},{frequency:400,gain:-18},{frequency:1600,gain:18}],oscillators:[{type:"triangle",fixedFrequency:90,volume:1,lowpass:{frequency:300},attackEnvelope:[{t:1,vol:1},{t:6,vol:1},{t:45,vol:.01}]},{type:"whitenoise",volume:.4,lowpass:{frequency:200},attackEnvelope:[{t:1,vol:1},{t:30,vol:0}]},{type:"triangle",freqMultiplier:.1,volume:1,lowpass:{frequency:200}}]},shortnote:{masterVolume:.8,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:15,vol:0}],eq:[{frequency:400,gain:-4},{frequency:800,gain:-12},{frequency:2400,gain:4},{frequency:7200,gain:-20},{frequency:1e3,Q:5,gain:-12},{frequency:5400,gain:-32},{frequency:12800,gain:-14}],oscillators:[{type:"sawtooth",volume:.6,lowpass:{frequency:1e3}},{type:"whitenoise",volume:.2,lowpass:{frequency:1e4},highpass:{frequency:7e3},attackEnvelope:[{t:1,vol:1},{t:10,vol:0}]},{type:"whitenoise",volume:1.3,lowpass:{frequency:700,Q:4},highpass:{frequency:250}}]},noise:{masterVolume:.3,midiInstrument:122,oscillators:[{type:"whitenoise"}]},filteredNoise:{masterVolume:.3,midiInstrument:122,eq:[{frequency:1600,gain:-8},{frequency:2200,gain:-4}],oscillators:[{type:"whitenoise",lowpass:{frequency:5,frequencyPitchTrackingMultiplier:1300,Q:6},highpass:{frequency:5,frequencyPitchTrackingMultiplier:300,Q:6}}]},wind:{masterVolume:.75,midiInstrument:122,noteGlideDuration:150,masterReleaseEnvelope:[{t:0,vol:1},{t:124,vol:.24},{t:281,vol:0}],oscillators:[{type:"whitenoise",volume:1,lowpass:{frequency:100,frequencyPitchTrackingMultiplier:6,Q:23},highpass:{frequency:170,frequencyPitchTrackingMultiplier:6}},{type:"sine",freqMultiplier:.016,volume:1e3,fmOscillator:0}]}},{defined:d,extend:y}=n();class f{constructor(e,t,i){this.audioContext=e,this.curParams={},this.midiTrackName=i.midiTrackName,this.masterVolNode=new GainNode(e),this.masterVolNode.connect(t),this.volumeNode=new GainNode(e),this.createNodesFromCapabilities(y({pan:!0},i.capabilities||{})),this.connectCapabilityNodes(this.volumeNode,this.masterVolNode),this.synthPatch=new m(e,"string"==typeof i.synthPatch?p[i.synthPatch]:i.synthPatch),this.midiInstrument=this.synthPatch.midiInstrument||1,this.synthPatch.startSilently(),this.synthPatch.connect(this.volumeNode)}setMasterVolume(e){this.masterVolNode.gain.setTargetAtTime(e,0,f.rampTime)}scheduleEventAtTime(e,t){let i=y(this.curParams,t),n=d(t.frequency)?t.frequency:d(t.note)?f.musicalNoteToFrequency(t.note):220;d(n)&&this.synthPatch.playFreqAtTime(e,n,i.noteDuration),(d(i.tremoloDepth)||d(i.tremoloSpeed))&&this.setTremoloAtTime(e,i.tremoloDepth,i.tremoloSpeed),d(i.pan)&&this.setPanAtTime(e,i.pan),d(i.volume)&&this.setVolumeAtTime(e,i.volume),(d(i.lowpassFreq)||d(i.lowpassResonance))&&this.setFilterAtTime("lowpass",e,i.lowpassFreq,i.lowpassResonance),(d(i.highpassFreq)||d(i.highpassResonance))&&this.setFilterAtTime("highpass",e,i.highpassFreq,i.highpassResonance)}silenceAtTime(e){this.synthPatch.silenceAtTime(e)}cancel(){this.synthPatch.mute(),[this.tremoloDepth&&this.tremoloDepth.gain,this.tremoloOsc&&this.tremoloOsc.frequency,this.lowpassNode&&this.lowpassNode.frequency,this.lowpassNode&&this.lowpassNode.Q,this.highpassNode&&this.highpassNode.frequency,this.highpassNode&&this.highpassNode.Q,this.panNode&&this.panNode.pan,this.volumeNode.gain].forEach(e=>e&&e.cancelScheduledValues(0))}destroy(){this.cancel(),this.synthPatch.stop(),this.tremoloOsc&&this.tremoloOsc.stop(),[this.tremoloDepth,this.tremoloOsc,this.lowpassNode,this.highpassNode,this.panNode,this.volumeNode,this.masterVolNode].forEach(e=>e&&e.disconnect())}setPanAtTime(e,t){this.panNode&&this.panNode.pan.setTargetAtTime(t,e+this.audioContext.currentTime,f.rampTime)}setFilterAtTime(e,t,i,n){let s=this[e+"Node"],o=this.audioContext.currentTime+t;s&&(d(n)&&s.Q.setTargetAtTime(n,o,f.rampTime),d(i)&&s.frequency.setTargetAtTime(i,o,f.rampTime))}setVolumeAtTime(e,t){this.volumeNode&&this.volumeNode.gain.setTargetAtTime(t,e+this.audioContext.currentTime,f.rampTime)}setTremoloAtTime(e,t,i){let n=this.audioContext.currentTime+e;this.tremoloDepth&&d(t)&&this.tremoloDepth.gain.setTargetAtTime(t,n,f.rampTime),this.tremoloOsc&&d(i)&&this.tremoloOsc.frequency.setTargetAtTime(15*i,n,f.rampTime)}createNodesFromCapabilities(e){let t=this.audioContext;e.pan&&(this.panNode=new StereoPannerNode(t)),e.tremolo&&(this.tremoloOsc=new OscillatorNode(t,{type:"sine",frequency:3}),this.tremoloDepth=new GainNode(t),this.tremoloOsc.connect(this.tremoloDepth),this.tremoloDepth.connect(this.masterVolNode.gain),this.tremoloOsc.start()),e.filters&&(this.lowpassNode=new BiquadFilterNode(t,{type:"lowpass",frequency:2e4}),this.highpassNode=new BiquadFilterNode(t,{type:"highpass",frequency:0}))}connectCapabilityNodes(e,t){[this.panNode,this.lowpassNode,this.highpassNode,e].reduce((e,t)=>t?(t.connect(e),t):e,t)}static noteStringToC0Distance(e){let t=e.match(/^([a-g][#b]?)([0-8])$/i),i=t?t[1]:"a",n=i[0].toLowerCase(),s=i[1];return(({c:0,d:2,e:4,f:5,g:7,a:9,b:11})[n]||0)+("#"===s?1:"b"===s?-1:0)+12*(t?parseInt(t[2],10):4)}static musicalNoteToFrequency(e){return 16.3516*Math.pow(2,Math.min("string"==typeof e?this.noteStringToC0Distance(e):e,107)/12)}}f.rampTime=m.stopRampTime/4;let{pick:g}=n(),v=class{constructor(e){this.options=e,this.masterVolume=1,this.synthesis=window.speechSynthesis,void 0!==speechSynthesis.onvoiceschanged&&(speechSynthesis.onvoiceschanged=this.setVoice.bind(this)),this.setVoice(),this.scheduled=[]}say(e,t){if(this.synthesis){this.synthesis.cancel();let i=new SpeechSynthesisUtterance(e);this.voice&&(i.voice=this.voice),i.rate=t&&t.rate||this.options.rate||1,i.pitch=t&&t.pitch||this.options.pitch||1,i.volume=g(t&&t.volume,this.options.volume,1)*this.masterVolume,this.synthesis.speak(i)}}sayAtTime(e,t,i){this.scheduled.push(setTimeout(this.say.bind(this,t,i),e))}cancel(){this.scheduled.forEach(clearTimeout),this.scheduled=[],this.synthesis.cancel()}destroy(){this.cancel()}setMasterVolume(e){this.masterVolume=e}setVoice(){if(this.synthesis){let e,t=this.options.name,i=this.options.language||"en-US",n=this.synthesis.getVoices(),s=n.length;for(let o=0;o<s;++o){if(t&&n[o].name===t){this.voice=n[o];return}if(!e&&n[o].lang===i&&(e=n[o],!t))break}this.voice=e}}},q=class{constructor(e,t,i=!1,n,s){this.type=e,this.engine=t,this.showPlayMarker=i,this.muted=s,this.events=n||[]}addEvent(e){let t=this.events[this.events.length-1];if(t&&e.time<t.time){let t=this.events.length;for(;t--&&this.events[t].time>e.time;);this.events.splice(t+1,0,e)}else this.events.push(e);return e}mute(){this.muted=!0}unmute(){this.muted=!1}cancel(){this.engine.cancel()}destroy(){this.engine.destroy()}},{pick:T}=n(),w=e=>Math.round(12*Math.log(e)/Math.LN2-48.37632),x=(e,t)=>t>>>8*e&255,N=e=>[77,84,104,100,0,0,0,6,0,+(e>1),x(1,e),x(0,e),1,244],k=[0,255,81,3,7,161,32],P=e=>{let t=127&e,i=[];for(;e>>=7;)t<<=8,t|=127&e|128;for(;;)if(i.push(255&t),128&t)t>>=8;else break;return i},M=e=>{let t,i,n=[],s=e=>{let t=n.length;for(;t--&&n[t].timeMS>e.timeMS;);n.splice(t+1,0,e)};return e.forEach(e=>{let n=e.instrumentEventOptions||{},o=e.time,a=i=T(n.noteDuration,i),l=a&&e.time+a,r=[{valMap:e=>64+63*e&127,data:{10:n.pan,92:n.tremoloDepth,93:n.tremoloSpeed}},{valMap:e=>127*e/2e4&127,data:{74:n.lowpassFreq,75:n.highpassFreq}},{valMap:e=>63*Math.min(18,Math.max(-18,e))/18+63&127,data:{71:n.lowpassResonance,76:n.highpassResonance}}],h=t=void 0===n.volume?T(t,127):127*n.volume&127,c=n.frequency,u=n.note||0,m=12+(c?w(c):"string"==typeof u?f.noteStringToC0Distance(u):u)&127;r.forEach(e=>Object.keys(e.data).forEach(t=>{let i=e.data[t];void 0!==i&&s({timeMS:o,type:"CTRL_CHG",data:[176,parseInt(t,10),e.valMap(i)]})})),l&&(s({timeMS:o,type:"NON",data:[144,m,h]}),s({timeMS:l,type:"NOF",data:[128,m,h]}))}),n},E=(e,t)=>{let i=[];if(t&&i.push(0,192,127&t),e){let t=[];for(let i=0;i<e.length;++i){let n=e.charCodeAt(i);n<128&&t.push(n)}return i.concat([0,255,3],P(t.length),t)}return i},b=(e,t,i,n)=>{let s=0,o=E(i,n),a=M(e).reduce((e,t)=>{let i=P(t.timeMS-s);return s=t.timeMS,e.concat(i,t.data)},[]),l=[0,255,47,0],r=(t?k.length:0)+o.length+a.length+l.length;return[77,84,114,107,x(3,r),x(2,r),x(1,r),x(0,r)].concat(t?k:[],o,a,l)},A=function(e){let t=e.filter(e=>!!e.events.length),i=t.length,n=i>1;return new Uint8Array(N(n?i+1:i).concat(n?b([],!0):[],t.reduce((e,t)=>{let i=t.engine;return e.concat(b(t.events,!n,i.midiTrackName,i.midiInstrument))},[])))},{isSafari:C,win:S,win:{document:O}}=n(),V=S.URL||S.webkitURL||S;function I(e){let t=e.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(t&&t.length>3&&S.atob&&S.ArrayBuffer&&S.Uint8Array&&S.Blob&&V.createObjectURL){let e=S.atob(t[3]),i=new S.ArrayBuffer(e.length),n=new S.Uint8Array(i);for(let t=0;t<n.length;++t)n[t]=e.charCodeAt(t);return V.createObjectURL(new S.Blob([n],{type:t[1]}))}}let{downloadURL:F}={dataURLtoBlob:I,downloadURL:function(e,t){let i=S.navigator,n=O.createElement("a");if("string"!=typeof e&&!(e instanceof String)&&i.msSaveOrOpenBlob){i.msSaveOrOpenBlob(e,t);return}if(e=""+e,i.userAgent.length>1e3)throw Error("Input too long");let s=/Edge\/\d+/.test(i.userAgent);if((C&&"string"==typeof e&&0===e.indexOf("data:application/pdf")||s||e.length>2e6)&&!(e=I(e)||""))throw Error("Failed to convert to blob");if(void 0!==n.download)n.href=e,n.download=t,O.body.appendChild(n),n.click(),O.body.removeChild(n);else try{if(!S.open(e,"chart"))throw Error("Failed to open window")}catch{S.location.href=e}}},{defined:D,find:Q,merge:R}=n(),B=class{constructor(e,t){this.chart=t,this.isPaused=!1,this.isPlaying=!1,this.channels=[],this.scheduledCallbacks=[],this.playTimestamp=0,this.resumeFromTime=0,this.options=e||{}}addChannel(e,t,i=!1,n){if("instrument"===e&&!t.scheduleEventAtTime||"speech"===e&&!t.sayAtTime)throw Error("Highcharts Sonification: Invalid channel engine.");let s=new q(e,t,i,n);return this.channels.push(s),s}play(e,t=!0,i=!0,n){this.isPlaying?this.cancel():this.clearScheduledCallbacks(),this.onEndArgument=n,this.playTimestamp=Date.now(),this.resumeFromTime=0,this.isPaused=!1,this.isPlaying=!0;let s=this.options.skipThreshold||2,o=this.options.onPlay,a=this.options.showTooltip,l=this.options.showCrosshair,r=e?function(e,t){let i=t.map(t=>(t.cancel(),{channel:t,filteredEvents:t.muted?[]:t.events.filter(e)})),n=i.reduce((e,t)=>Math.min(e,t.filteredEvents.length?t.filteredEvents[0].time:1/0),1/0);return i.map(e=>new q(e.channel.type,e.channel.engine,e.channel.showPlayMarker,e.filteredEvents.map(e=>R(e,{time:e.time-n})),e.channel.muted))}(e,this.playingChannels||this.channels):this.channels,h=e=>Object.keys(e.speechOptions||{}).concat(Object.keys(e.instrumentEventOptions||{})).join(),c=[];t&&(this.playingChannels=r),o&&o({chart:this.chart,timeline:this});let u=0;r.forEach(e=>{if(e.muted)return;let t=e.events.length,i=-1/0,n=-1/0,o="";u=Math.max(e.events[t-1]&&e.events[t-1].time||0,u);for(let r=0;r<t;++r){let u=e.events[r],m=h(u);if(m===o&&u.time-n<s)continue;o=m,n=u.time,"instrument"===e.type?e.engine.scheduleEventAtTime(u.time/1e3,u.instrumentEventOptions||{}):e.engine.sayAtTime(u.time,u.message||"",u.speechOptions||{});let p=u.relatedPoint,d=p&&p.series&&p.series.chart,y=u.callback||p&&(a||l)&&!1!==e.showPlayMarker&&(u.time-i>50||r===t-1);p&&c.push(p),y&&(this.scheduledCallbacks.push(setTimeout(()=>{if(u.callback&&u.callback(),p){if(l){let e=p.series;e&&e.xAxis&&e.xAxis.crosshair&&e.xAxis.drawCrosshair(void 0,p),e&&e.yAxis&&e.yAxis.crosshair&&e.yAxis.drawCrosshair(void 0,p)}a&&!(d&&d.hoverPoints&&d.hoverPoints.length>1&&Q(d.hoverPoints,e=>e===p)&&p.onMouseOver)&&p.onMouseOver()}},u.time)),i=u.time)}});let m=this.options.onEnd,p=this.options.onStop;this.scheduledCallbacks.push(setTimeout(()=>{let e=this.chart,t={chart:e,timeline:this,pointsPlayed:c};this.isPlaying=!1,i&&this.resetPlayState(),p&&p(t),m&&m(t),n&&n(t),e&&(e.tooltip&&e.tooltip.hide(0),e.hoverSeries&&e.hoverSeries.onMouseOut(),e.axes.forEach(e=>e.hideCrosshair()))},u+250)),this.resumeFromTime=t?u:this.getLength()}pause(){return this.isPaused=!0,this.cancel(),this.resumeFromTime=Date.now()-this.playTimestamp-10,this.resumeFromTime}getCurrentTime(){return this.isPlaying?Date.now()-this.playTimestamp:this.resumeFromTime}getLength(){return this.channels.reduce((e,t)=>{let i=t.events[t.events.length-1];return i?Math.max(i.time,e):e},0)}resume(){if(this.playingChannels){let e=this.resumeFromTime-50;this.play(t=>t.time>e,!1,!1,this.onEndArgument),this.playTimestamp-=e}else this.play(void 0,!1,!1,this.onEndArgument)}anchorPlayMoment(e,t){this.isPlaying&&this.pause();let i=0;this.play((t,n,s)=>{let o=e(t,n,s);return o&&t.time>i&&(i=t.time),o},!1,!1,t),this.playingChannels=this.playingChannels||this.channels,this.isPaused=!0,this.isPlaying=!1,this.resumeFromTime=i}playAdjacent(e,t,i,n){this.isPlaying&&this.pause();let s=this.resumeFromTime,o=this.channels.reduce((t,i)=>{let o=n?i.events.filter(n):i.events,a=0,l=o.length,r=t;for(;a<l;){let t=a+l>>1,i=o[t].time,n=i-s;n>0?(e&&i<r&&(r=i),l=t):n<0?(!e&&i>r&&(r=i),a=t+1):e?a=t+1:l=t}return r},e?1/0:-1/0);if(o===1/0||o===-1/0){i&&i({chart:this.chart,timeline:this,attemptedNext:e});return}this.anchorPlayMoment((t,i,a)=>{let l=e?t.time>s&&t.time<=o+.02:t.time<s&&t.time>=o-.02;return n?l&&n(t,i,a):l},t)}playClosestToPropValue(e,t,i,n,s){let o=(e,t,i)=>!!(s?s(e,t,i)&&e.relatedPoint:e.relatedPoint),a=1/0,l=null;(this.playingChannels||this.channels).forEach(i=>{let n=i.events,s=n.length;for(;s--;){if(!o(n[s],s,n))continue;let i=n[s].relatedPoint[e],r=D(i)&&Math.abs(t-i);!1!==r&&r<a&&(a=r,l=n[s])}}),l?(this.play(e=>!!(l&&e.time<l.time+1&&e.time>l.time-1&&e.relatedPoint===l.relatedPoint),!1,!1,i),this.playingChannels=this.playingChannels||this.channels,this.isPaused=!0,this.isPlaying=!1,this.resumeFromTime=l.time):n&&n({chart:this.chart,timeline:this})}getEventsForPoint(e){return this.channels.reduce((t,i)=>{let n=i.events.filter(t=>t.relatedPoint===e);return t.concat(n)},[])}playSegment(e,t){let i={first:1/0,last:-1/0};if(this.channels.forEach(e=>{e.events.length&&(i.first=Math.min(e.events[0].time,i.first),i.last=Math.max(e.events[e.events.length-1].time,i.last))}),i.first<1/0){let n=(i.last-i.first)/100,s=i.first+e*n,o=s+n;if(!this.channels.some(e=>{let t=e.events,i=0,n=t.length;for(;i<n;){let e=i+n>>1,a=t[e].time;if(a<s)i=e+1;else{if(!(a>o))return!0;n=e}}return!1}))return;this.play(e=>e.time>=s&&e.time<=o,!1,!1,t),this.playingChannels=this.playingChannels||this.channels,this.isPaused=!0,this.isPlaying=!1,this.resumeFromTime=o}}getLastPlayedPoint(e){let t=this.getCurrentTime(),i=this.playingChannels||this.channels,n=1/0,s=null;return i.forEach(i=>{let o=i.events.filter((i,n,s)=>!!(i.relatedPoint&&i.time<=t&&(!e||e(i,n,s)))),a=o[o.length-1];if(a){let e=Math.abs(a.time-t);e<n&&(n=e,s=a.relatedPoint)}}),s}reset(){this.isPlaying&&this.cancel(),this.resetPlayState()}cancel(){let e=this.options.onStop;e&&e({chart:this.chart,timeline:this}),this.isPlaying=!1,this.channels.forEach(e=>e.cancel()),this.playingChannels&&this.playingChannels!==this.channels&&this.playingChannels.forEach(e=>e.cancel()),this.clearScheduledCallbacks(),this.resumeFromTime=0}destroy(){this.cancel(),this.playingChannels&&this.playingChannels!==this.channels&&this.playingChannels.forEach(e=>e.destroy()),this.channels.forEach(e=>e.destroy())}setMasterVolume(e){this.channels.forEach(t=>t.engine.setMasterVolume(e))}getMIDIData(){return A(this.channels.filter(e=>"instrument"===e.type))}downloadMIDI(e){let t=this.getMIDIData(),i=(e||this.chart&&this.chart.options.title&&this.chart.options.title.text||"chart")+".mid",n=new Blob([t],{type:"application/octet-stream"}),s=window.URL.createObjectURL(n);F(s,i),window.URL.revokeObjectURL(s)}resetPlayState(){delete this.playingChannels,delete this.onEndArgument,this.playTimestamp=this.resumeFromTime=0,this.isPaused=!1}clearScheduledCallbacks(){this.scheduledCallbacks.forEach(clearTimeout),this.scheduledCallbacks=[]}},G=e.default.Templating;var j=t.n(G);let{clamp:U,defined:L,extend:W,getNestedProperty:H,merge:K,pick:_}=n(),{format:$}=j(),Z=e=>/^([a-g][#b]?)[0-8]$/i.test(e);function z(e,t){let i;if(t){if("number"==typeof(i=e[t]))return i;i=H(t,e)}return"number"==typeof i?i:void 0}function J(e,t,i,n,s){let o=t.max-t.min;if(o<=0)return i.min;let a=i.max-i.min,l=a*(e-t.min)/o;if(s){let i=t.min>0?e=>Math.log(e)/Math.LOG10E:e=>{let t=Math.abs(e);t<10&&(t+=(10-t)/10);let i=Math.log(t)/Math.LN10;return e<0?-i:i},n=i(t.min);l=a*(i(e)-n)/(i(t.max)-n)}return U(n?i.max-l:i.min+l,i.min,i.max)}function X(e,t,i,n,s,o,a){return _(function(e,t,i,n,s,o){if("number"==typeof s)return s;if("function"==typeof s)return s(W({time:0},e));let a=s,l=n.mapFunction,r=n.min,h=n.max,c=n.within,u;if("object"==typeof s&&(a=s.mapTo,l=s.mapFunction||l,r=_(s.min,r),h=_(s.max,h),c=s.within||n.within,u=s.scale),!a)return null;let m="-"===a.charAt(0);m&&(a=a.slice(1));let p=e.value,d="value"===a&&void 0!==p&&o;if(!d){let t=s.value;if(void 0!==t)p=t;else{if(!e.point)return null;p=e.point[a]}void 0===p&&(p=H(a,e.point))}if("number"!=typeof p||null===p)return null;let y=null;if(e.point){if("xAxis"===c||"yAxis"===c){let t=e.point.series[c];t&&L(t.dataMin)&&L(t.dataMax)&&(y={min:t.dataMin,max:t.dataMax})}else("series"===c||i)&&e.point.series&&(y=t.seriesExtremes[e.point.series.index][d?o:a])}if(y||(y=t.globalExtremes[d?o:a]),u){let e=[],t=Math.floor(r/12),i=Math.ceil(h/12)+1,n=u.length;for(let s=t;s<i;++s)for(let t=0;t<n;++t){let i=12*s+u[t];i>=r&&i<=h&&e.push(i)}let s=J(p,y,{min:0,max:e.length-1},m,"logarithmic"===l);return e[Math.round(s)]}return J(p,y,{min:r,max:h},m,"logarithmic"===l)}(e,t,i,W({min:0,max:1,mapTo:"y",mapFunction:"linear",within:"chart"},o||{}),n,a),s)}function Y(e,t,i,n){let s=n.mapping||{},o="speech"===n.type?new v({language:n.language,name:n.preferredVoice}):new f(t,i,{capabilities:{pan:!!s.pan,tremolo:!!s.tremolo,filters:!!(s.highpass||s.lowpass)},synthPatch:n.instrument,midiTrackName:n.midiName});return e.addChannel(n.type||"instrument",o,_(n.showPlayMarker,!0))}function ee(e,t,i,n,s,o){let a=(t,s,a,l)=>X(e,n,!1,(l||i)[t],s,a,o),l=[],r={noteDuration:a("noteDuration",200,{min:40,max:1e3}),pan:a("pan",0,{min:-1,max:1}),volume:a("volume",1,{min:.1,max:1})};i.frequency&&(r.frequency=a("frequency",440,{min:50,max:6e3})),i.lowpass&&(r.lowpassFreq=a("frequency",2e4,{min:0,max:2e4},i.lowpass),r.lowpassResonance=a("resonance",0,{min:-6,max:12},i.lowpass)),i.highpass&&(r.highpassFreq=a("frequency",2e4,{min:0,max:2e4},i.highpass),r.highpassResonance=a("resonance",0,{min:-6,max:12},i.highpass)),i.tremolo&&(r.tremoloDepth=a("depth",0,{min:0,max:.8},i.tremolo),r.tremoloSpeed=a("speed",0,{min:0,max:.8},i.tremolo));let h=a("gapBetweenNotes",150,{min:50,max:1e3}),c=a("playDelay",0,{max:200}),u=(i,a=0)=>{let u=i;i.mapTo?("string"==typeof i.min&&(u.min=f.noteStringToC0Distance(i.min)),"string"==typeof i.max&&(u.max=f.noteStringToC0Distance(i.max))):"string"==typeof i&&Z(i)&&(u=f.noteStringToC0Distance(i)),r.note=X(e,n,!1,u,-1,{min:0,max:107},o),r.note>-1&&(s&&(r.note=Math.round(r.note)),l.push(t.addEvent({time:e.time+c+h*a,relatedPoint:e.point,instrumentEventOptions:void 0!==a?W({},r):r})))};return i.pitch&&i.pitch.constructor===Array?i.pitch.forEach(u):i.pitch?u(i.pitch):i.frequency&&l.push(t.addEvent({time:e.time+c,relatedPoint:e.point,instrumentEventOptions:r})),l}function et(e,t,i,n,s){var o;let a=(t,o,a)=>X(e,n,!1,i[t],o,a,s),l=a("playDelay",0,{max:200}),r=a("pitch",1,{min:.3,max:2}),h=a("rate",1,{min:.4,max:4}),c=a("volume",1,{min:.1}),u=$("function"==typeof(o=i.text)?o(e):o,e,e.point&&e.point.series.chart);if(u)return t.addEvent({time:e.time+l,relatedPoint:e.point,speechOptions:{pitch:r,rate:h,volume:c},message:u})}function ei(e,t,i){if("function"==typeof t)return t(e);if("object"==typeof t){let n=t.prop,s=_(e.value,e.point&&z(e.point,n));if("number"!=typeof s)return!1;let o=!0,a=t.crossingUp,l=t.crossingDown,r="number"==typeof i;o=a&&l?r&&(i<a&&s>=a||i>l&&s<=l):(void 0===a||r&&i<a&&s>=a)&&(void 0===l||r&&i>l&&s<=l);let h=_(t.max,1/0),c=_(t.min,-1/0);return s<=h&&s>=c&&o}return!0}let en=function(e,t,i){let n=i.options.sonification||{},s=n.defaultInstrumentOptions,o=n.defaultSpeechOptions,a=K({enabled:!0,groupTimespan:15,algorithm:"minmax",prop:"y"},n.pointGrouping),l=n.globalTracks||[],r=n.globalContextTracks||[],h="sequential"===n.order,c=Math.max(50,n.duration-300),u=n.afterSeriesWait,m=n.events||{},p=function(e){let t=e.options.sonification||{},i=(t.defaultInstrumentOptions||{}).mapping||{time:"x",pitch:"y"},n=t.defaultSpeechOptions&&t.defaultSpeechOptions.mapping||{},s=[],o={},a=(e,t)=>{null!==t?(s[t]=s[t]||{},s[t][e]=!0):o[e]=!0},l={},r={},h=(e,t,i)=>{let n=e=>"-"===e.charAt(0)?e.slice(1):e;if("string"==typeof t&&"text"!==e){if("pitch"===e&&Z(t))return;"time"===e&&(r[t]=!0,a(t,i)),l[n(t)]=!0;return}if(t&&t.mapTo&&"string"==typeof t.mapTo){let s=n(t.mapTo);"time"===e&&a(s,i),("time"===e||"series"===t.within)&&(r[s]=!0),l[s]=!0;return}["tremolo","lowpass","highpass"].indexOf(e)>-1&&"object"==typeof t&&Object.keys(t).forEach(e=>h(e,t[e],i))},c=(e,t)=>{Object.keys(e).forEach(i=>h(i,e[i],t))},u=e=>e.forEach(e=>{l[e.valueProp||"x"]=r[e.valueProp||"x"]=!0});c(i,null),c(n,null),u(t.globalContextTracks||[]);let m=Object.keys(o).length;return e.series.forEach(e=>{let t=e.options.sonification;if(e.visible&&!(t&&!1===t.enabled)&&(m&&(s[e.index]=K(o)),t)){let i=(t.defaultInstrumentOptions||{}).mapping,n=(t.defaultSpeechOptions||{}).mapping;i&&c(i,e.index),n&&c(n,e.index),u(t.contextTracks||[]),(t.tracks||[]).concat(t.contextTracks||[]).forEach(t=>{t.mapping&&c(t.mapping,e.index)})}}),{seriesTimeProps:s,...function(e,t,i){let n=e.series,s=t.length,o=i.length,a=e=>e.reduce((e,t)=>(e[t]={min:1/0,max:-1/0},e),{}),l=(e,t,i)=>{let n=t[i];void 0===n&&(n=H(i,t)),"number"==typeof n&&(e[i].min=Math.min(e[i].min,n),e[i].max=Math.max(e[i].max,n))},r=a(t),h=n.length,c=Array(h);for(;h--;){let e=a(i),u=n[h].options;if(!n[h].visible||u&&u.sonification&&!1===u.sonification.enabled)continue;let m=n[h].points||[],p=m.length;for(;p--;){let n=s;for(;n--;)l(r,m[p],t[n]);for(n=o;n--;)l(e,m[p],i[n])}c[h]=e}return{globalExtremes:r,seriesExtremes:c}}(e,Object.keys(l),Object.keys(r))}}(i),d=new B({onPlay:m.onPlay,onEnd:m.onEnd,onStop:m.onStop,showCrosshair:n.showCrosshair,showTooltip:n.showTooltip},i);i.sonification&&(i.sonification.propMetrics=p);let y=0;return i.series.forEach((i,n)=>{let f=i.options.sonification||{};if(i.visible&&!1!==f.enabled){let g,v=h?function(e,t,i,n){let s,o,a=t-(e.chart.series.length-1)*n;if(i.seriesTimeProps.every(e=>{let t=Object.keys(e);return!(t.length>1)&&(s||(s=t[0]),s===t[0])})){let t=i.seriesExtremes[e.index][s];o=Math.round((t.max-t.min)/i.seriesExtremes.reduce((e,t)=>t[s]?e+t[s].max-t[s].min:e,0)*a)}else{let t=e.chart.series.reduce((e,t)=>e+t.points.length,0);o=Math.round((e.points||[]).length/t*a)}return Math.max(50,o)}(i,c,p,u):c,q=K(s,f.defaultInstrumentOptions),T=K(o,f.defaultSpeechOptions),w=K(a,f.pointGrouping),x=(f.tracks||[q]).concat(l),N=d.channels.length&&!h?f.contextTracks||[]:(f.contextTracks||[]).concat(r),k=[];x.forEach(n=>{let s=K({pointGrouping:w,midiName:n.midiName||i.name},"speech"===n.type?T:q,n),o=s.pointGrouping,a=s.activeWhen,l=e=>{"object"==typeof a&&a.prop&&(g=z(e,a.prop))},r=Y(d,e,t,s),c=e=>k.push(...function(e,t,i,n){let s=[];if("speech"===i.type&&i.mapping){let o=et(e,t,i.mapping,n);o&&(s=[o])}else i.mapping&&(s=ee(e,t,i.mapping,n,_(i.roundToMusicalNotes,!0)));return s}(e,r,s,p)),u=[],m=0,f=e=>{if(1===u.length)c({point:u[0].point,time:m+e/2});else{let t=function(e,t){let i=e.algorithm||"minmax",n=e=>t[e]?[t[e].point]:[];if("first"===i)return n(0);if("last"===i)return n(t.length-1);if("middle"===i)return n(t.length>>1);if("firstlast"===i)return n(0).concat(n(t.length-1));if("minmax"===i){let i,n,s,o,a=e.prop||"y";if(t.forEach(e=>{let t=z(e.point,a);void 0!==t&&((!i||t<s)&&(i=e,s=t),(!n||t>o)&&(n=e,o=t))}),i&&n)return i.point===n.point?[i.point]:i.time>n.time?[n.point,i.point]:[i.point,n.point]}return[]}(o,u),i=e/t.length;t.forEach((e,t)=>c({point:e,time:m+i/2+i*t}))}u=[]};(i.points||[]).forEach((e,t)=>{var n;let r=t===i.points.length-1,d=(n=y,X({point:e,time:0},p,h,s.mapping&&s.mapping.time||0,0,{min:0,max:v,mapTo:"x"})+n),q={point:e,time:d};if(!s.mapping||!ei(q,a,g)){l(e),r&&u.length&&f(u[u.length-1].time-u[0].time);return}if(l(e),o.enabled){let e=d-m,t=o.groupTimespan,i=r&&e<=t?e:t;r||e>t?(e<=t&&u.push(q),f(i),m=Math.floor(d/t)*t,r&&e>t?c({point:q.point,time:m+i/2}):u=[q]):u.push(q)}else c(q)})});let P=k.reduce((e,t)=>t.time<e.time?t:e,{time:1/0}),M=k.reduce((e,t)=>t.time>e.time?t:e,{time:-1/0});P.callback=m.onSeriesStart?m.onSeriesStart.bind(null,{series:i,timeline:d}):void 0,M.callback=m.onSeriesEnd?m.onSeriesEnd.bind(null,{series:i,timeline:d}):void 0,N.forEach(i=>{let a="speech"===i.type?K(o,i):K(s,{mapping:{pitch:{mapTo:"value"}}},i),l=Y(d,e,t,a);g=void 0;let{timeInterval:r,valueInterval:h}=a,c=a.valueProp||"x",u=a.activeWhen,m=p.seriesExtremes[n][c],f=(e,t)=>{if(!a.mapping||!ei({time:e,value:t},"object"==typeof u?W({prop:c},u):u,g)){g=t;return}g=t,"speech"===a.type?et({time:e,value:t},l,a.mapping,p,c):ee({time:e,value:t},l,a.mapping,p,_(a.roundToMusicalNotes,!0),c)};if(r){let e=0;for(;e<=v;){let t=J(e,{min:0,max:v},m);f(e+y,t),e+=r}}if(h){let e=m.min;for(;e<=m.max;)f(J(e,m,{min:0,max:v},!1,"logarithmic"===a.valueMapFunction)+y,e),e+=h}}),h&&(y+=v+u)}}),d},{defaultOptions:es,getOptions:eo}=n(),{addEvent:ea,extend:el,fireEvent:er,merge:eh,pick:ec}=n(),{doc:eu,win:em}=n();class ep{constructor(e){this.chart=e,this.retryContextCounter=0,this.lastUpdate=0,this.unbindKeydown=ea(eu,"keydown",function(t){e&&e.sonification&&("Esc"===t.key||"Escape"===t.key)&&e.sonification.cancel()});try{this.audioContext=new em.AudioContext,this.audioContext.suspend(),this.audioDestination=this.audioContext.destination}catch(e){}}setAudioDestination(e){this.audioDestination=e,this.update()}isPlaying(){return!!this.timeline&&this.timeline.isPlaying}playSegment(e,t){this.ready(this.playSegment.bind(this,e,t))&&this.timeline&&this.timeline.playSegment(e,t)}playAdjacent(e,t,i){if(this.ready(this.playAdjacent.bind(this,e,t,i))&&this.timeline){let n=this.chart.options.sonification,s=n&&n.events&&n.events.onBoundaryHit;s||this.initBoundaryInstrument(),this.timeline.playAdjacent(e,t,s||(()=>{this.defaultBoundaryHit()}),i)}}playAdjacentSeries(e,t="x",i){let n=this.getLastPlayedPoint();if(n){let s=n.series.index+(e?1:-1);return this.playClosestToProp(t,n[t],e=>!!e.relatedPoint&&e.relatedPoint.series.index===s,i),this.chart.series[s]||null}return null}playClosestToProp(e,t,i,n){if(this.ready(this.playClosestToProp.bind(this,e,t,i,n))&&this.timeline){let s=this.chart.options.sonification,o=s&&s.events&&s.events.onBoundaryHit;o||this.initBoundaryInstrument(),this.timeline.playClosestToPropValue(e,t,n,o||(()=>this.defaultBoundaryHit()),i)}}getLastPlayedPoint(){return this.timeline?this.timeline.getLastPlayedPoint():null}playNote(e,t,i=0){if(!this.ready(this.playNote.bind(this,e,t)))return;let n=t.noteDuration=t.noteDuration||500,s=new f(this.audioContext,this.audioDestination,{synthPatch:e,capabilities:{filters:!0,tremolo:!0,pan:!0}});s.scheduleEventAtTime(i/1e3,t),setTimeout(()=>s&&s.destroy(),i+n+500)}speak(e,t,i=0){new v(eh({language:"en-US",rate:1.5,volume:.4},t||{})).sayAtTime(i,e)}cancel(){this.timeline&&this.timeline.cancel(),er(this,"cancel")}downloadMIDI(){this.ready(this.downloadMIDI.bind(this))&&this.timeline&&(this.timeline.reset(),this.timeline.downloadMIDI())}sonifyChart(e,t){this.ready(this.sonifyChart.bind(this,e,t))&&this.timeline&&(this.timeline.reset(),this.beforePlay(),this.timeline.play(void 0,void 0,e,t))}sonifySeries(e,t,i){this.ready(this.sonifySeries.bind(this,e,t,i))&&this.timeline&&(this.timeline.reset(),this.beforePlay(),this.timeline.play(t=>!!t.relatedPoint&&t.relatedPoint.series===e,void 0,t,i))}sonifyPoint(e,t){this.ready(this.sonifyPoint.bind(this,e,t))&&this.timeline&&(this.timeline.reset(),this.beforePlay(),this.timeline.anchorPlayMoment(t=>t.relatedPoint===e,t))}setMasterVolume(e){this.timeline&&this.timeline.setMasterVolume(e)}destroy(){this.unbindKeydown(),this.timeline&&(this.timeline.destroy(),delete this.timeline),this.boundaryInstrument&&this.boundaryInstrument.stop(),this.audioContext&&(this.audioContext.close(),delete this.audioContext)}update(){let e=this.chart.options&&this.chart.options.sonification;if(!this.ready(this.update.bind(this))||!e)return;let t=Date.now(),i=e.updateInterval;if(t-this.lastUpdate<i&&!this.forceReady){clearTimeout(this.scheduledUpdate),this.scheduledUpdate=setTimeout(this.update.bind(this),i/2);return}let n=e.events||{};if(n.beforeUpdate&&n.beforeUpdate({chart:this.chart,timeline:this.timeline}),this.lastUpdate=t,this.timeline&&this.timeline.destroy(),this.audioContext&&this.audioDestination){this.timeline=en(this.audioContext,this.audioDestination,this.chart);let e=this.chart.options.sonification;this.timeline.setMasterVolume(ec(e&&e.masterVolume,1))}n.afterUpdate&&n.afterUpdate({chart:this.chart,timeline:this.timeline})}ready(e){return!!this.audioContext&&!!this.audioDestination&&!!this.chart.options&&(!this.chart.options.sonification||!1!==this.chart.options.sonification.enabled)&&("suspended"!==this.audioContext.state||this.forceReady?(this.retryContextCounter=0,!0):(this.retryContextCounter++<20&&setTimeout(()=>{this.audioContext&&"suspended"===this.audioContext.state?this.audioContext.resume().then(e):e()},5),!1))}beforePlay(){let e=this.chart.options.sonification,t=e&&e.events&&e.events.beforePlay;t&&t({chart:this.chart,timeline:this.timeline})}initBoundaryInstrument(){this.boundaryInstrument||(this.boundaryInstrument=new m(this.audioContext,eh(p.chop,{masterVolume:.3})),this.boundaryInstrument.startSilently(),this.boundaryInstrument.connect(this.audioDestination))}defaultBoundaryHit(){this.boundaryInstrument&&(this.boundaryInstrument.playFreqAtTime(.1,1,200),this.boundaryInstrument.playFreqAtTime(.2,1,200))}}!function(e){let t=[];function i(){let t=this.sonification,i=this.options&&this.options.sonification;i&&i.enabled?t?t.update():(this.sonification=new e(this),this.sonification.update()):t&&(t.destroy(),delete this.sonification)}function n(){this&&this.sonification&&this.sonification.destroy()}function s(){this.updateSonificationEnabled&&this.updateSonificationEnabled()}function o(e){let t=e.options.sonification;t&&(eh(!0,this.options.sonification,t),s.call(this))}e.compose=function(e,a,l){-1===t.indexOf(e)&&(t.push(e),el(e.prototype,{updateSonificationEnabled:i,sonify:function(e){this.sonification&&this.sonification.sonifyChart(!1,e)},toggleSonify:function(e=!0,t){if(!this.sonification)return;let i=this.sonification.timeline;em.speechSynthesis&&em.speechSynthesis.cancel(),i&&this.sonification.isPlaying()?e?this.sonification.cancel():i.pause():i&&i.isPaused?i.resume():this.sonification.sonifyChart(e,t)}}),ea(e,"destroy",n),ea(e,"render",s),ea(e,"update",o)),-1===t.indexOf(a)&&(t.push(a),a.prototype.sonify=function(e){this.chart.sonification&&this.chart.sonification.sonifySeries(this,!1,e)}),-1===t.indexOf(l)&&(t.push(l),l.prototype.sonify=function(e){this.series.chart.sonification&&this.series.chart.sonification.sonifyPoint(this,e)});let r=eo().exporting;r&&r.buttons&&r.buttons.contextButton.menuItems&&r.buttons.contextButton.menuItems.push("separator","downloadMIDI","playAsSound")}}(ep||(ep={})),eh(!0,es,{sonification:{enabled:!0,duration:6e3,afterSeriesWait:700,updateInterval:200,masterVolume:.7,order:"sequential",showTooltip:!0,showCrosshair:!0,pointGrouping:{enabled:!0,groupTimespan:15,algorithm:"minmax",prop:"y"},defaultInstrumentOptions:{roundToMusicalNotes:!0,instrument:"piano",mapping:{time:"x",pan:"x",noteDuration:200,pitch:{mapTo:"y",min:"c2",max:"c6",within:"yAxis"},gapBetweenNotes:100}},defaultSpeechOptions:{language:"en-US",mapping:{time:"x",rate:1.3,volume:.4},pointGrouping:{algorithm:"last"}}},exporting:{menuItemDefinitions:{downloadMIDI:{textKey:"downloadMIDI",onclick:function(){this.sonification&&this.sonification.downloadMIDI()}},playAsSound:{textKey:"playAsSound",onclick:function(){let e=this.sonification;e&&e.isPlaying()?e.cancel():this.sonify()}}}},lang:{downloadMIDI:"Download MIDI",playAsSound:"Play as sound"}});let ed=ep,ey=n();ey.sonification={InstrumentPresets:p,Scales:{minor:[0,2,3,5,7,8,10],dorian:[0,2,3,5,7,9,10],harmonicMinor:[0,2,3,5,7,8,11],phrygian:[0,1,3,5,7,8,11],major:[0,2,4,5,7,9,11],lydian:[0,2,4,6,7,9,11],mixolydian:[0,2,4,5,7,9,10],majorPentatonic:[0,2,4,7,9],minorPentatonic:[0,3,5,7,10]},SynthPatch:m,SonificationInstrument:f,SonificationSpeaker:v,SonificationTimeline:B,Sonification:ed},ed.compose(ey.Chart,ey.Series,ey.Point);let ef=n();export{ef as default};