let t,e,i;import*as s from"../highcharts.js";import"./pathfinder.js";import"./static-scale.js";import"./xrange.js";var o,n,r,a,l,h,d,c,p,u={};u.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return u.d(e,{a:e}),e},u.d=(t,e)=>{for(var i in e)u.o(e,i)&&!u.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},u.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let g=s.default;var x=u.n(g);function f(t,e,i,s){return[["M",t,e+s/2],["L",t+i,e],["L",t,e+s/2],["L",t+i,e+s]]}function m(t,e,i,s){return f(t,e,i/2,s)}function b(t,e,i,s){return[["M",t+i,e],["L",t,e+s/2],["L",t+i,e+s],["Z"]]}function y(t,e,i,s){return b(t,e,i/2,s)}let{defined:v,error:M,merge:k,objectEach:A}=x(),w=x().deg2rad,O=Math.max,E=Math.min,S=class{constructor(t,e,i){this.init(t,e,i)}init(t,e,i){this.fromPoint=t,this.toPoint=e,this.options=i,this.chart=t.series.chart,this.pathfinder=this.chart.pathfinder}renderPath(t,e){let i=this.chart,s=i.styledMode,o=this.pathfinder,n={},r=this.graphics&&this.graphics.path;o.group||(o.group=i.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(i.seriesGroup)),o.group.translate(i.plotLeft,i.plotTop),r&&r.renderer||(r=i.renderer.path().add(o.group),s||r.attr({opacity:0})),r.attr(e),n.d=t,s||(n.opacity=1),r.animate(n),this.graphics=this.graphics||{},this.graphics.path=r}addMarker(t,e,i){let s,o,n,r,a,l,h,d=this.fromPoint.series.chart,c=d.pathfinder,p=d.renderer,u="start"===t?this.fromPoint:this.toPoint,g=u.getPathfinderAnchorPoint(e);e.enabled&&((h="start"===t?i[1]:i[i.length-2])&&"M"===h[0]||"L"===h[0])&&(l={x:h[1],y:h[2]},o=u.getRadiansToVector(l,g),s=u.getMarkerVector(o,e.radius,g),e.width&&e.height?(r=e.width,a=e.height):r=a=2*e.radius,this.graphics=this.graphics||{},n={x:s.x-r/2,y:s.y-a/2,width:r,height:a,rotation:-o/w,rotationOriginX:s.x,rotationOriginY:s.y},this.graphics[t]?this.graphics[t].animate(n):(this.graphics[t]=p.symbol(e.symbol).addClass("highcharts-point-connecting-path-"+t+"-marker highcharts-color-"+this.fromPoint.colorIndex).attr(n).add(c.group),p.styledMode||this.graphics[t].attr({fill:e.color||this.fromPoint.color,stroke:e.lineColor,"stroke-width":e.lineWidth,opacity:0}).animate({opacity:1},u.series.options.animation)))}getPath(t){let e=this.pathfinder,i=this.chart,s=e.algorithms[t.type],o=e.chartObstacles;return"function"!=typeof s?(M('"'+t.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]}):(s.requiresObstacles&&!o&&(o=e.chartObstacles=e.getChartObstacles(t),i.options.connectors.algorithmMargin=t.algorithmMargin,e.chartObstacleMetrics=e.getObstacleMetrics(o)),s(this.fromPoint.getPathfinderAnchorPoint(t.startMarker),this.toPoint.getPathfinderAnchorPoint(t.endMarker),k({chartObstacles:o,lineObstacles:e.lineObstacles||[],obstacleMetrics:e.chartObstacleMetrics,hardBounds:{xMin:0,xMax:i.plotWidth,yMin:0,yMax:i.plotHeight},obstacleOptions:{margin:t.algorithmMargin},startDirectionX:e.getAlgorithmStartDirection(t.startMarker)},t)))}render(){let t=this.fromPoint,e=t.series,i=e.chart,s=i.pathfinder,o={},n=k(i.options.connectors,e.options.connectors,t.options.connectors,this.options);!i.styledMode&&(o.stroke=n.lineColor||t.color,o["stroke-width"]=n.lineWidth,n.dashStyle&&(o.dashstyle=n.dashStyle)),o.class="highcharts-point-connecting-path highcharts-color-"+t.colorIndex,v((n=k(o,n)).marker.radius)||(n.marker.radius=E(O(Math.ceil((n.algorithmMargin||8)/2)-1,1),5));let r=this.getPath(n),a=r.path;r.obstacles&&(s.lineObstacles=s.lineObstacles||[],s.lineObstacles=s.lineObstacles.concat(r.obstacles)),this.renderPath(a,o),this.addMarker("start",k(n.marker,n.startMarker),a),this.addMarker("end",k(n.marker,n.endMarker),a)}destroy(){this.graphics&&(A(this.graphics,function(t){t.destroy()}),delete this.graphics)}},{composed:P}=x(),{addEvent:B,merge:T,pushUnique:C,wrap:I}=x(),D={color:"#ccd3ff",width:2,label:{format:"%[abdYHM]",formatter:function(t,e){return this.axis.chart.time.dateFormat(e||"",t,!0)},rotation:0,style:{fontSize:"0.7em"}}};function R(){let t=this.options,e=t.currentDateIndicator;if(e){let i="object"==typeof e?T(D,e):T(D);i.value=Date.now(),i.className="highcharts-current-date-indicator",t.plotLines||(t.plotLines=[]),t.plotLines.push(i)}}function L(){this.label&&this.label.attr({text:this.getLabelText(this.options.label)})}function G(t,e){let i=this.options;return i&&i.className&&-1!==i.className.indexOf("highcharts-current-date-indicator")&&i.label&&"function"==typeof i.label.formatter?(i.value=Date.now(),i.label.formatter.call(this,i.value,i.label.format)):t.call(this,e)}let z=s.default.Chart;var N=u.n(z);let{defaultOptions:W}=x(),{isArray:F,merge:H,splat:U}=x();class Y extends N(){init(t,e){let i,s=t.xAxis,o=t.yAxis;t.xAxis=t.yAxis=void 0;let n=H(!0,{chart:{type:"gantt"},title:{text:""},legend:{enabled:!1},navigator:{series:{type:"gantt"},yAxis:{type:"category"}}},t,{isGantt:!0});t.xAxis=s,t.yAxis=o,n.xAxis=(F(t.xAxis)?t.xAxis:[t.xAxis||{},{}]).map((t,e)=>(1===e&&(i=0),H({grid:{borderColor:"#cccccc",enabled:!0},opposite:W.xAxis?.opposite??t.opposite??!0,linkedTo:i},t,{type:"datetime"}))),n.yAxis=U(t.yAxis||{}).map(t=>H({grid:{borderColor:"#cccccc",enabled:!0},staticScale:50,reversed:!0,type:t.categories?t.type:"treegrid"},t)),super.init(n,e)}}(o=Y||(Y={})).ganttChart=function(t,e,i){return new o(t,e,i)};let X=Y,V=s.default.Axis;var _=u.n(V);let{isTouchDevice:j}=x(),{addEvent:q,merge:Z,pick:$}=x(),K=[];function J(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function Q(){let t,e,i,s=this.legend,o=this.navigator;if(o){t=s&&s.options,e=o.xAxis,i=o.yAxis;let{scrollbarHeight:n,scrollButtonSize:r}=o;this.inverted?(o.left=o.opposite?this.chartWidth-n-o.height:this.spacing[3]+n,o.top=this.plotTop+r):(o.left=$(e.left,this.plotLeft+r),o.top=o.navigatorOptions.top||this.chartHeight-o.height-n-(this.scrollbar?.options.margin||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(t&&"bottom"===t.verticalAlign&&"proximate"!==t.layout&&t.enabled&&!t.floating?s.legendHeight+$(t.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),e&&i&&(this.inverted?e.options.left=i.options.left=o.left:e.options.top=i.options.top=o.top,e.setAxisSize(),i.setAxisSize())}}function tt(e){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new t(this),$(e.redraw,!0)&&this.redraw(e.animation))}function te(){let e=this.options;(e.navigator.enabled||e.scrollbar.enabled)&&(this.scroller=this.navigator=new t(this))}function ti(){let t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!j&&"x"===this.zooming.type||j&&"x"===this.zooming.pinchType))return!1}function ts(t){let e=t.navigator;if(e&&t.xAxis[0]){let i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function to(t){let e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(Z(!0,this.options.navigator,e),Z(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}let tn=function(e,i){if(x().pushUnique(K,e)){let s=e.prototype;t=i,s.callbacks.push(ts),q(e,"afterAddSeries",J),q(e,"afterSetChartSize",Q),q(e,"afterUpdate",tt),q(e,"beforeRender",te),q(e,"beforeShowResetZoom",ti),q(e,"update",to)}},{isTouchDevice:tr}=x(),{addEvent:ta,correctFloat:tl,defined:th,isNumber:td,pick:tc}=x();function tp(){this.navigatorAxis||(this.navigatorAxis=new tg(this))}function tu(t){let e,i=this.chart,s=i.options,o=s.navigator,n=this.navigatorAxis,r=i.zooming.pinchType,a=s.rangeSelector,l=i.zooming.type;if(this.isXAxis&&(o?.enabled||a?.enabled)){if("y"===l&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===l||tr&&"xy"===r)&&this.options.range){let e=n.previousZoom;th(t.min)?n.previousZoom=[this.min,this.max]:e&&(t.min=e[0],t.max=e[1],n.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}class tg{static compose(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),ta(t,"init",tp),ta(t,"setExtremes",tu))}constructor(t){this.axis=t}destroy(){this.axis=void 0}toFixedRange(t,e,i,s){let o=this.axis,n=(o.pointRange||0)/2,r=tc(i,o.translate(t,!0,!o.horiz)),a=tc(s,o.translate(e,!0,!o.horiz));return th(i)||(r=tl(r+n)),th(s)||(a=tl(a-n)),td(r)&&td(a)||(r=a=void 0),{min:r,max:a}}}let tx=s.default.Color;var tf=u.n(tx);let tm=s.default.SeriesRegistry;var tb=u.n(tm);let{parse:ty}=tf(),{seriesTypes:tv}=tb(),tM={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:ty("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===tv.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},{defined:tk,isNumber:tA,pick:tw}=x(),{relativeLength:tO}=x(),tE={"navigator-handle":function(t,e,i,s,o={}){var n,r,a,l,h;let d=o.width?o.width/2:i,c=tO(o.borderRadius||0,Math.min(2*d,s));return[["M",-1.5,(s=o.height||s)/2-3.5],["L",-1.5,s/2+4.5],["M",.5,s/2-3.5],["L",.5,s/2+4.5],...(n=-d-1,r=.5,a=2*d+1,l=s,h={r:c},h?.r?function(t,e,i,s,o){let n=o?.r||0;return[["M",t+n,e],["L",t+i-n,e],["A",n,n,0,0,1,t+i,e+n],["L",t+i,e+s-n],["A",n,n,0,0,1,t+i-n,e+s],["L",t+n,e+s],["A",n,n,0,0,1,t,e+s-n],["L",t,e+n],["A",n,n,0,0,1,t+n,e],["Z"]]}(n,.5,a,l,h):[["M",n,r],["L",n+a,r],["L",n+a,r+l],["L",n,r+l],["Z"]])]}},tS=s.default.RendererRegistry;var tP=u.n(tS);let{defined:tB}=x(),{setOptions:tT}=x(),{composed:tC}=x(),{getRendererType:tI}=tP(),{setFixedRange:tD}={setFixedRange:function(t){let e=this.xAxis[0];tB(e.dataMax)&&tB(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}},{addEvent:tR,extend:tL,pushUnique:tG}=x();function tz(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}let tN=function(t,e,i){tg.compose(e),tG(tC,"Navigator")&&(t.prototype.setFixedRange=tD,tL(tI().prototype.symbols,tE),tR(i,"afterUpdate",tz),tT({navigator:tM}))},{composed:tW}=x(),{addEvent:tF,defined:tH,pick:tU,pushUnique:tY}=x();!function(t){let e;function i(t){let e=tU(t.options?.min,t.min),i=tU(t.options?.max,t.max);return{axisMin:e,axisMax:i,scrollMin:tH(t.dataMin)?Math.min(e,t.min,t.dataMin,tU(t.threshold,1/0)):e,scrollMax:tH(t.dataMax)?Math.max(i,t.max,t.dataMax,tU(t.threshold,-1/0)):i}}function s(){let t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function o(){let t=this;t.options?.scrollbar?.enabled&&(t.options.scrollbar.vertical=!t.horiz,t.options.startOnTick=t.options.endOnTick=!1,t.scrollbar=new e(t.chart.renderer,t.options.scrollbar,t.chart),tF(t.scrollbar,"changed",function(e){let s,o,{axisMin:n,axisMax:r,scrollMin:a,scrollMax:l}=i(t),h=l-a;if(tH(n)&&tH(r)){if(t.horiz&&!t.reversed||!t.horiz&&t.reversed?(s=a+h*this.to,o=a+h*this.from):(s=a+h*(1-this.from),o=a+h*(1-this.to)),this.shouldUpdateExtremes(e.DOMType)){let i="mousemove"!==e.DOMType&&"touchmove"!==e.DOMType&&void 0;t.setExtremes(o,s,!0,i,e)}else this.setRange(this.from,this.to)}}))}function n(){let t,e,s,{scrollMin:o,scrollMax:n}=i(this),r=this.scrollbar,a=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,h=this.options.margin||0;if(r&&l){if(this.horiz)this.opposite||(l[1]+=a),r.position(this.left,this.top+this.height+2+l[1]-(this.opposite?h:0),this.width,this.height),this.opposite||(l[1]+=h),t=1;else{let e;this.opposite&&(l[0]+=a),e=r.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:h):this.opposite?0:h,r.position(e,this.top,this.width,this.height),this.opposite&&(l[0]+=h),t=0}if(l[t]+=r.size+(r.options.margin||0),isNaN(o)||isNaN(n)||!tH(this.min)||!tH(this.max)||this.dataMin===this.dataMax)r.setRange(0,1);else if(this.min===this.max){let t=this.pointRange/(this.dataMax+1);e=t*this.min,s=t*(this.max+1),r.setRange(e,s)}else e=(this.min-o)/(n-o),s=(this.max-o)/(n-o),this.horiz&&!this.reversed||!this.horiz&&this.reversed?r.setRange(e,s):r.setRange(1-s,1-e)}}t.compose=function(t,i){tY(tW,"Axis.Scrollbar")&&(e=i,tF(t,"afterGetOffset",s),tF(t,"afterInit",o),tF(t,"afterRender",n))}}(a||(a={}));let tX=a,tV={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},{defaultOptions:t_}=x(),{addEvent:tj,correctFloat:tq,crisp:tZ,defined:t$,destroyObjectProperties:tK,fireEvent:tJ,merge:tQ,pick:t0,removeEvent:t1}=x();class t2{static compose(t){tX.compose(t,t2)}static swapXY(t,e){return e&&t.forEach(t=>{let e,i=t.length;for(let s=0;s<i;s+=2)"number"==typeof(e=t[s+1])&&(t[s+1]=t[s+2],t[s+2]=e)}),t}constructor(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}addEvents(){let t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,s=this.track.element,o=this.mouseDownHandler.bind(this),n=this.mouseMoveHandler.bind(this),r=this.mouseUpHandler.bind(this),a=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[s,"click",this.trackClick.bind(this)],[i,"mousedown",o],[i.ownerDocument,"mousemove",n],[i.ownerDocument,"mouseup",r],[i,"touchstart",o],[i.ownerDocument,"touchmove",n],[i.ownerDocument,"touchend",r]];a.forEach(function(t){tj.apply(null,t)}),this._events=a}buttonToMaxClick(t){let e=(this.to-this.from)*t0(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),tJ(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}buttonToMinClick(t){let e=tq(this.to-this.from)*t0(this.options.step,.2);this.updatePosition(tq(this.from-e),tq(this.to-e)),tJ(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}cursorToScrollbarPosition(t){let e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}}destroy(){let t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,tK(e.scrollbarButtons))}drawScrollbarButton(t){let e=this.renderer,i=this.scrollbarButtons,s=this.options,o=this.size,n=e.g().add(this.group);if(i.push(n),s.buttonsEnabled){let r=e.rect().addClass("highcharts-scrollbar-button").add(n);this.chart.styledMode||r.attr({stroke:s.buttonBorderColor,"stroke-width":s.buttonBorderWidth,fill:s.buttonBackgroundColor}),r.attr(r.crisp({x:-.5,y:-.5,width:o,height:o,r:s.buttonBorderRadius},r.strokeWidth()));let a=e.path(t2.swapXY([["M",o/2+(t?-1:1),o/2-3],["L",o/2+(t?-1:1),o/2+3],["L",o/2+(t?2:-2),o/2]],s.vertical)).addClass("highcharts-scrollbar-arrow").add(i[t]);this.chart.styledMode||a.attr({fill:s.buttonArrowColor})}}init(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=tQ(tV,t_.scrollbar,e),this.options.margin=t0(this.options.margin,10),this.chart=i,this.size=t0(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())}mouseDownHandler(t){let e=this.chart.pointer?.normalize(t)||t,i=this.cursorToScrollbarPosition(e);this.chartX=i.chartX,this.chartY=i.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0}mouseMoveHandler(t){let e,i=this.chart.pointer?.normalize(t)||t,s=this.options.vertical?"chartY":"chartX",o=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][s])&&(e=this.cursorToScrollbarPosition(i)[s]-this[s],this.hasDragged=!0,this.updatePosition(o[0]+e,o[1]+e),this.hasDragged&&tJ(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))}mouseUpHandler(t){this.hasDragged&&tJ(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null}position(t,e,i,s){let{buttonsEnabled:o,margin:n=0,vertical:r}=this.options,a=this.rendered?"animate":"attr",l=s,h=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=s,this.xOffset=l,this.yOffset=h,r?(this.width=this.yOffset=i=h=this.size,this.xOffset=l=0,this.yOffset=h=o?this.size:0,this.barWidth=s-(o?2*i:0),this.x=t+=n):(this.height=s=this.size,this.xOffset=l=o?this.size:0,this.barWidth=i-(o?2*s:0),this.y=this.y+n),this.group[a]({translateX:t,translateY:this.y}),this.track[a]({width:i,height:s}),this.scrollbarButtons[1][a]({translateX:r?0:i-l,translateY:r?s-h:0})}removeEvents(){this._events.forEach(function(t){t1.apply(null,t)}),this._events.length=0}render(){let t=this.renderer,e=this.options,i=this.size,s=this.chart.styledMode,o=t.g("scrollbar").attr({zIndex:e.zIndex}).hide().add();this.group=o,this.track=t.rect().addClass("highcharts-scrollbar-track").attr({r:e.trackBorderRadius||0,height:i,width:i}).add(o),s||this.track.attr({fill:e.trackBackgroundColor,stroke:e.trackBorderColor,"stroke-width":e.trackBorderWidth});let n=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-tZ(0,n),y:-tZ(0,n)}),this.scrollbarGroup=t.g().add(o),this.scrollbar=t.rect().addClass("highcharts-scrollbar-thumb").attr({height:i-n,width:i-n,r:e.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=t.path(t2.swapXY([["M",-3,i/4],["L",-3,2*i/3],["M",0,i/4],["L",0,2*i/3],["M",3,i/4],["L",3,2*i/3]],e.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),s||(this.scrollbar.attr({fill:e.barBackgroundColor,stroke:e.barBorderColor,"stroke-width":e.barBorderWidth}),this.scrollbarRifles.attr({stroke:e.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-tZ(0,this.scrollbarStrokeWidth),-tZ(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)}setRange(t,e){let i,s,o=this.options,n=o.vertical,r=o.minWidth,a=this.barWidth,l=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(!t$(a))return;let h=a*Math.min(e,1);i=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=s=tq(h-i),s<r&&(i=(a-r+s)*t,s=r);let d=Math.floor(i+this.xOffset+this.yOffset),c=s/2-.5;this.from=t,this.to=e,n?(this.scrollbarGroup[l]({translateY:d}),this.scrollbar[l]({height:s}),this.scrollbarRifles[l]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[l]({translateX:d}),this.scrollbar[l]({width:s}),this.scrollbarRifles[l]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),s<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===o.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}shouldUpdateExtremes(t){return t0(this.options.liveRedraw,x().svg&&!x().isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!t$(t)}trackClick(t){let e=this.chart.pointer?.normalize(t)||t,i=this.to-this.from,s=this.y+this.scrollbarTop,o=this.x+this.scrollbarLeft;this.options.vertical&&e.chartY>s||!this.options.vertical&&e.chartX>o?this.updatePosition(this.from+i,this.to+i):this.updatePosition(this.from-i,this.to-i),tJ(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}update(t){this.destroy(),this.init(this.chart.renderer,tQ(!0,this.options,t),this.chart)}updatePosition(t,e){e>1&&(t=tq(1-tq(e-t)),e=1),t<0&&(e=tq(e-t),t=0),this.from=t,this.to=e}}t2.defaultOptions=tV,t_.scrollbar=tQ(!0,t2.defaultOptions,t_.scrollbar);let t3=s.default.SVGRenderer;var t5=u.n(t3);let{defaultOptions:t6}=x(),{isTouchDevice:t9}=x(),{prototype:{symbols:t8}}=t5(),{addEvent:t4,clamp:t7,correctFloat:et,defined:ee,destroyObjectProperties:ei,erase:es,extend:eo,find:en,fireEvent:er,isArray:ea,isNumber:el,merge:eh,pick:ed,removeEvent:ec,splat:ep}=x();function eu(t,...e){let i=[].filter.call(e,el);if(i.length)return Math[t].apply(0,i)}class eg{static compose(t,e,i){tn(t,eg),tN(t,e,i)}constructor(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}drawHandle(t,e,i,s){let o=this.navigatorOptions.handles.height;this.handles[e][s](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-o)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-o/2-1)})}drawOutline(t,e,i,s){let o=this.navigatorOptions.maskInside,n=this.outline.strokeWidth(),r=n/2,a=n%2/2,l=this.scrollButtonSize,h=this.size,d=this.top,c=this.height,p=d-r,u=d+c,g=this.left,x,f;i?(x=d+e+a,e=d+t+a,f=[["M",g+c,d-l-a],["L",g+c,x],["L",g,x],["M",g,e],["L",g+c,e],["L",g+c,d+h+l]],o&&f.push(["M",g+c,x-r],["L",g+c,e+r])):(g-=l,t+=g+l-a,e+=g+l-a,f=[["M",g,p],["L",t,p],["L",t,u],["M",e,u],["L",e,p],["L",g+h+2*l,p]],o&&f.push(["M",t-r,p],["L",e+r,p])),this.outline[s]({d:f})}drawMasks(t,e,i,s){let o,n,r,a,l=this.left,h=this.top,d=this.height;i?(r=[l,l,l],a=[h,h+t,h+e],n=[d,d,d],o=[t,e-t,this.size-e]):(r=[l,l+t,l+e],a=[h,h,h],n=[t,e-t,this.size-e],o=[d,d,d]),this.shades.forEach((t,e)=>{t[s]({x:r[e],y:a[e],width:n[e],height:o[e]})})}renderElements(){let t=this,e=t.navigatorOptions,i=e.maskInside,s=t.chart,o=s.inverted,n=s.renderer,r={cursor:o?"ns-resize":"ew-resize"},a=t.navigatorGroup??(t.navigatorGroup=n.g("navigator").attr({zIndex:8,visibility:"hidden"}).add());if([!i,i,!i].forEach((i,o)=>{let l=t.shades[o]??(t.shades[o]=n.rect().addClass("highcharts-navigator-mask"+(1===o?"-inside":"-outside")).add(a));s.styledMode||(l.attr({fill:i?e.maskFill:"rgba(0,0,0,0)"}),1===o&&l.css(r))}),t.outline||(t.outline=n.path().addClass("highcharts-navigator-outline").add(a)),s.styledMode||t.outline.attr({"stroke-width":e.outlineWidth,stroke:e.outlineColor}),e.handles?.enabled){let i=e.handles,{height:o,width:l}=i;[0,1].forEach(e=>{let h=i.symbols[e];if(t.handles[e]&&t.handles[e].symbolUrl===h){if(!t.handles[e].isImg&&t.handles[e].symbolName!==h){let i=t8[h].call(t8,-l/2-1,0,l,o);t.handles[e].attr({d:i}),t.handles[e].symbolName=h}}else t.handles[e]?.destroy(),t.handles[e]=n.symbol(h,-l/2-1,0,l,o,i),t.handles[e].attr({zIndex:7-e}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][e]).add(a),t.addMouseEvents();s.inverted&&t.handles[e].attr({rotation:90,rotationOriginX:Math.floor(-l/2),rotationOriginY:(o+l)/2}),s.styledMode||t.handles[e].attr({fill:i.backgroundColor,stroke:i.borderColor,"stroke-width":i.lineWidth,width:i.width,height:i.height,x:-l/2-1,y:0}).css(r)})}}update(t,e=!1){let i=this.chart,s=i.options.chart.inverted!==i.scrollbar?.options.vertical;if(eh(!0,i.options.navigator,t),this.navigatorOptions=i.options.navigator||{},this.setOpposite(),ee(t.enabled)||s)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(i);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{ec(t,"updatedData",this.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t.eventsToUnbind.push(t4(t,"updatedData",this.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=t.height??this.height;let e=this.getXAxisOffsets();this.xAxis.update({...t.xAxis,offsets:e,[i.inverted?"width":"height"]:this.height,[i.inverted?"height":"width"]:void 0},!1),this.yAxis.update({...t.yAxis,[i.inverted?"width":"height"]:this.height},!1)}e&&i.redraw()}render(t,e,i,s){let o=this.chart,n=this.xAxis,r=n.pointRange||0,a=n.navigatorAxis.fake?o.xAxis[0]:n,l=this.navigatorEnabled,h=this.rendered,d=o.inverted,c=o.xAxis[0].minRange,p=o.xAxis[0].options.maxRange,u=this.scrollButtonSize,g,x,f,m=this.scrollbarHeight,b,y;if(this.hasDragged&&!ee(i))return;if(this.isDirty&&this.renderElements(),t=et(t-r/2),e=et(e+r/2),!el(t)||!el(e)){if(!h)return;i=0,s=ed(n.width,a.width)}this.left=ed(n.left,o.plotLeft+u+(d?o.plotWidth:0));let v=this.size=b=ed(n.len,(d?o.plotHeight:o.plotWidth)-2*u);g=d?m:b+2*u,i=ed(i,n.toPixels(t,!0)),s=ed(s,n.toPixels(e,!0)),el(i)&&Math.abs(i)!==1/0||(i=0,s=g);let M=n.toValue(i,!0),k=n.toValue(s,!0),A=Math.abs(et(k-M));A<c?this.grabbedLeft?i=n.toPixels(k-c-r,!0):this.grabbedRight&&(s=n.toPixels(M+c+r,!0)):ee(p)&&et(A-r)>p&&(this.grabbedLeft?i=n.toPixels(k-p-r,!0):this.grabbedRight&&(s=n.toPixels(M+p+r,!0))),this.zoomedMax=t7(Math.max(i,s),0,v),this.zoomedMin=t7(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,s),0,v),this.range=this.zoomedMax-this.zoomedMin,v=Math.round(this.zoomedMax);let w=Math.round(this.zoomedMin);l&&(this.navigatorGroup.attr({visibility:"inherit"}),y=h&&!this.hasDragged?"animate":"attr",this.drawMasks(w,v,d,y),this.drawOutline(w,v,d,y),this.navigatorOptions.handles.enabled&&(this.drawHandle(w,0,d,y),this.drawHandle(v,1,d,y))),this.scrollbar&&(d?(f=this.top-u,x=this.left-m+(l||!a.opposite?0:(a.titleOffset||0)+a.axisTitleMargin),m=b+2*u):(f=this.top+(l?this.height:-m),x=this.left-u),this.scrollbar.position(x,f,g,m),this.scrollbar.setRange(this.zoomedMin/(b||1),this.zoomedMax/(b||1))),this.rendered=!0,this.isDirty=!1,er(this,"afterRender")}addMouseEvents(){let t=this,e=t.chart,i=e.container,s=[],o,n;t.mouseMoveHandler=o=function(e){t.onMouseMove(e)},t.mouseUpHandler=n=function(e){t.onMouseUp(e)},(s=t.getPartsEvents("mousedown")).push(t4(e.renderTo,"mousemove",o),t4(i.ownerDocument,"mouseup",n),t4(e.renderTo,"touchmove",o),t4(i.ownerDocument,"touchend",n)),s.concat(t.getPartsEvents("touchstart")),t.eventsToUnbind=s,t.series&&t.series[0]&&s.push(t4(t.series[0].xAxis,"foundExtremes",function(){e.navigator.modifyNavigatorAxisExtremes()}))}getPartsEvents(t){let e=this,i=[];return["shades","handles"].forEach(function(s){e[s].forEach(function(o,n){i.push(t4(o.element,t,function(t){e[s+"Mousedown"](t,n)}))})}),i}shadesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=this.xAxis,o=this.zoomedMin,n=this.size,r=this.range,a=this.left,l=t.chartX,h,d,c,p;i.inverted&&(l=t.chartY,a=this.top),1===e?(this.grabbedCenter=l,this.fixedWidth=r,this.dragOffset=l-o):(p=l-a-r/2,0===e?p=Math.max(0,p):2===e&&p+r>=n&&(p=n-r,this.reversedExtremes?(p-=r,d=this.getUnionExtremes().dataMin):h=this.getUnionExtremes().dataMax),p!==o&&(this.fixedWidth=r,ee((c=s.navigatorAxis.toFixedRange(p,p+r,d,h)).min)&&er(this,"setRange",{min:Math.min(c.min,c.max),max:Math.max(c.min,c.max),redraw:!0,eventArguments:{trigger:"navigator"}})))}handlesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=i.xAxis[0],o=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=o?s.min:s.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=o?s.max:s.min),i.setFixedRange(void 0)}onMouseMove(t){let e=this,i=e.chart,s=e.navigatorSize,o=e.range,n=e.dragOffset,r=i.inverted,a=e.left,l;(!t.touches||0!==t.touches[0].pageX)&&(l=(t=i.pointer?.normalize(t)||t).chartX,r&&(a=e.top,l=t.chartY),e.grabbedLeft?(e.hasDragged=!0,e.render(0,0,l-a,e.otherHandlePos)):e.grabbedRight?(e.hasDragged=!0,e.render(0,0,e.otherHandlePos,l-a)):e.grabbedCenter&&(e.hasDragged=!0,l<n?l=n:l>s+n-o&&(l=s+n-o),e.render(0,0,l-n,l-n+o)),e.hasDragged&&e.scrollbar&&ed(e.scrollbar.options.liveRedraw,!t9&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){e.onMouseUp(t)},0)))}onMouseUp(t){let e,i,s,o,n,r,a=this.chart,l=this.xAxis,h=this.scrollbar,d=t.DOMEvent||t,c=a.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!h||!h.hasDragged)||"scrollbar"===t.trigger)&&(s=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?o=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(n=this.fixedExtreme),this.zoomedMax===this.size&&(n=this.reversedExtremes?s.dataMin:s.dataMax),0===this.zoomedMin&&(o=this.reversedExtremes?s.dataMax:s.dataMin),ee((r=l.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,o,n)).min)&&er(this,"setRange",{min:Math.min(r.min,r.max),max:Math.max(r.min,r.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&el(this.zoomedMin)&&el(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,c,p),this.outline&&this.drawOutline(i,e,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,c,p),this.drawHandle(e,1,c,p)))}removeEvents(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()}removeBaseSeriesEvents(){let t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){ec(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&ec(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))}getXAxisOffsets(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]}init(t){let e=t.options,i=e.navigator||{},s=i.enabled,o=e.scrollbar||{},n=o.enabled,r=s&&i.height||0,a=n&&o.height||0,l=o.buttonsEnabled&&a||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=r,this.scrollbarHeight=a,this.scrollButtonSize=l,this.scrollbarEnabled=n,this.navigatorEnabled=s,this.navigatorOptions=i,this.scrollbarOptions=o,this.setOpposite();let h=this,d=h.baseSeries,c=t.xAxis.length,p=t.yAxis.length,u=d&&d[0]&&d[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,h.navigatorEnabled){let e=this.getXAxisOffsets();h.xAxis=new(_())(t,eh({breaks:u.options.breaks,ordinal:u.options.ordinal,overscroll:u.options.overscroll},i.xAxis,{type:"datetime",yAxis:i.yAxis?.id,index:c,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:u.options.ordinal?0:u.options.minPadding,maxPadding:u.options.ordinal?0:u.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:e,width:r}:{offsets:e,height:r}),"xAxis"),h.yAxis=new(_())(t,eh(i.yAxis,{alignTicks:!1,offset:0,index:p,isInternal:!0,reversed:ed(i.yAxis&&i.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:r}:{height:r}),"yAxis"),d||i.series.data?h.updateNavigatorSeries(!1):0===t.series.length&&(h.unbindRedraw=t4(t,"beforeRedraw",function(){t.series.length>0&&!h.series&&(h.setBaseSeries(),h.unbindRedraw())})),h.reversedExtremes=t.inverted&&!h.xAxis.reversed||!t.inverted&&h.xAxis.reversed,h.renderElements(),h.addMouseEvents()}else h.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){let s=t.xAxis[0],o=s.getExtremes(),n=s.len-2*l,r=eu("min",s.options.min,o.dataMin),a=eu("max",s.options.max,o.dataMax)-r;return i?e*a/n+r:n*(e-r)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},h.xAxis.navigatorAxis.axis=h.xAxis,h.xAxis.navigatorAxis.toFixedRange=tg.prototype.toFixedRange.bind(h.xAxis.navigatorAxis);if(t.options.scrollbar?.enabled){let e=eh(t.options.scrollbar,{vertical:t.inverted});el(e.margin)||(e.margin=t.inverted?-3:3),t.scrollbar=h.scrollbar=new t2(t.renderer,e,t),t4(h.scrollbar,"changed",function(t){let e=h.size,i=e*this.to,s=e*this.from;h.hasDragged=h.scrollbar.hasDragged,h.render(0,0,s,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){h.onMouseUp(t)})})}h.addBaseSeriesEvents(),h.addChartEvents()}setOpposite(){let t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=ed(t.opposite,!!(!e&&i.inverted))}getUnionExtremes(t){let e,i=this.chart.xAxis[0],s=this.chart.time,o=this.xAxis,n=o.options,r=i.options;return t&&null===i.dataMin||(e={dataMin:ed(s.parse(n?.min),eu("min",s.parse(r.min),i.dataMin,o.dataMin,o.min)),dataMax:ed(s.parse(n?.max),eu("max",s.parse(r.max),i.dataMax,o.dataMax,o.max))}),e}setBaseSeries(t,e){let i=this.chart,s=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?en(i.series,t=>!t.options.isInternal).index:0),(i.series||[]).forEach((e,i)=>{!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&s.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)}updateNavigatorSeries(t,e){let i=this,s=i.chart,o=i.baseSeries,n={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:this.navigatorOptions.xAxis?.id,yAxis:this.navigatorOptions.yAxis?.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},r=i.series=(i.series||[]).filter(t=>{let e=t.baseSeries;return!(0>o.indexOf(e))||(e&&(ec(e,"updatedData",i.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),a,l,h=i.navigatorOptions.series,d;o&&o.length&&o.forEach(t=>{let c=t.navigatorSeries,p=eo({color:t.color,visible:t.visible},ea(h)?t6.navigator.series:h);if(c&&!1===i.navigatorOptions.adaptToUpdatedData)return;n.name="Navigator "+o.length,d=(a=t.options||{}).navigatorOptions||{},p.dataLabels=ep(p.dataLabels),(l=eh(a,n,p,d)).pointRange=ed(p.pointRange,d.pointRange,t6.plotOptions[l.type||"line"].pointRange);let u=d.data||p.data;i.hasNavigatorData=i.hasNavigatorData||!!u,l.data=u||a.data?.slice(0),c&&c.options?c.update(l,e):(t.navigatorSeries=s.initSeries(l),s.setSortedData(),t.navigatorSeries.baseSeries=t,r.push(t.navigatorSeries))}),(h.data&&!(o&&o.length)||ea(h))&&(i.hasNavigatorData=!1,(h=ep(h)).forEach((t,e)=>{n.name="Navigator "+(r.length+1),(l=eh(t6.navigator.series,{color:s.series[e]&&!s.series[e].options.isInternal&&s.series[e].color||s.options.colors[e]||s.options.colors[0]},n,t)).data=t.data,l.data&&(i.hasNavigatorData=!0,r.push(s.initSeries(l)))})),t&&this.addBaseSeriesEvents()}addBaseSeriesEvents(){let t=this,e=t.baseSeries||[];e[0]&&e[0].xAxis&&e[0].eventsToUnbind.push(t4(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),e.forEach(i=>{i.eventsToUnbind.push(t4(i,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),i.eventsToUnbind.push(t4(i,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==this.navigatorOptions.adaptToUpdatedData&&i.xAxis&&i.eventsToUnbind.push(t4(i,"updatedData",this.updatedDataHandler)),i.eventsToUnbind.push(t4(i,"remove",function(){e&&es(e,i),this.navigatorSeries&&t.series&&(es(t.series,this.navigatorSeries),ee(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})}getBaseSeriesMin(t){return this.baseSeries.reduce(function(t,e){return Math.min(t,e.getColumn("x")[0]??t)},t)}modifyNavigatorAxisExtremes(){let t=this.xAxis;if(void 0!==t.getExtremes){let e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}}modifyBaseAxisExtremes(){let t,e,i=this.chart.navigator,s=this.getExtremes(),o=s.min,n=s.max,r=s.dataMin,a=s.dataMax,l=n-o,h=i.stickToMin,d=i.stickToMax,c=ed(this.ordinal?.convertOverscroll(this.options.overscroll),0),p=i.series&&i.series[0],u=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(h&&(t=(e=r)+l),d&&(t=a+c,h||(e=Math.max(r,t-l,i.getBaseSeriesMin(p&&p.xData?p.xData[0]:-Number.MAX_VALUE)))),u&&(h||d)&&el(e)&&(this.min=this.userMin=e,this.max=this.userMax=t)),i.stickToMin=i.stickToMax=null}updatedDataHandler(){let t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=ed(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))}shouldStickToMin(t,e){let i=e.getBaseSeriesMin(t.getColumn("x")[0]),s=t.xAxis,o=s.max,n=s.min,r=s.options.range,a=!0;return!!(el(o)&&el(n))&&(r&&o-i>0?o-i<r:n<=i)}addChartEvents(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(t4(this.chart,"redraw",function(){let t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),t4(this.chart,"getMargins",function(){let t=this.navigator,e=t.opposite?"plotTop":"marginBottom";this.inverted&&(e=t.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(t.navigatorEnabled||!this.inverted?t.height+(this.scrollbar?.options.margin||0)+t.scrollbarHeight:0)+(t.navigatorOptions.margin||0)}),t4(eg,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))}destroy(){this.removeEvents(),this.xAxis&&(es(this.chart.xAxis,this.xAxis),es(this.chart.axes,this.xAxis)),this.yAxis&&(es(this.chart.yAxis,this.yAxis),es(this.chart.axes,this.yAxis)),(this.series||[]).forEach(t=>{t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(t=>{this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null}),[this.handles].forEach(t=>{ei(t)}),this.baseSeries.forEach(t=>{t.navigatorSeries=void 0}),this.navigatorEnabled=!1}}let ex={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},{defaultOptions:ef}=x(),{composed:em}=x(),{addEvent:eb,defined:ey,extend:ev,isNumber:eM,merge:ek,pick:eA,pushUnique:ew}=x(),eO=[];function eE(){let t,e,i=this.range,s=i.type,o=this.max,n=this.chart.time,r=function(t,e){let i=n.toParts(t),o=i.slice();"year"===s?o[0]+=e:o[1]+=e;let r=n.makeTime.apply(n,o),a=n.toParts(r);return"month"===s&&i[1]===a[1]&&1===Math.abs(e)&&(o[0]=i[0],o[1]=i[1],o[2]=0),(r=n.makeTime.apply(n,o))-t};eM(i)?(t=o-i,e=i):i&&(t=o+r(o,-(i.count||1)),this.chart&&this.chart.setFixedRange(o-t));let a=eA(this.dataMin,Number.MIN_VALUE);return eM(t)||(t=a),t<=a&&(t=a,void 0===e&&(e=r(t,i.count)),this.newMax=Math.min(t+e,eA(this.dataMax,Number.MAX_VALUE))),eM(o)?!eM(i)&&i&&i._offsetMin&&(t+=i._offsetMin):t=void 0,t}function eS(){this.rangeSelector?.redrawElements()}function eP(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new e(this))}function eB(){let t=this.rangeSelector;if(t){eM(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);let e=t.options.verticalAlign;t.options.floating||("bottom"===e?this.extraBottomMargin=!0:"top"!==e||(this.extraTopMargin=!0))}}function eT(){let t,e=this.rangeSelector;if(!e)return;let i=this.xAxis[0].getExtremes(),s=this.legend,o=e&&e.options.verticalAlign;eM(i.min)&&e.render(i.min,i.max),s.display&&"top"===o&&o===s.options.verticalAlign&&(t=ek(this.spacingBox),"vertical"===s.options.layout?t.y=this.plotTop:t.y+=e.getHeight(),s.group.placed=!1,s.align(t))}function eC(){for(let t=0,e=eO.length;t<e;++t){let e=eO[t];if(e[0]===this){e[1].forEach(t=>t()),eO.splice(t,1);return}}}function eI(){let t=this.rangeSelector;if(t?.options?.enabled){let e=t.getHeight(),i=t.options.verticalAlign;t.options.floating||("bottom"===i?this.marginBottom+=e:"middle"===i||(this.plotTop+=e))}}function eD(t){let i=t.options.rangeSelector,s=this.extraBottomMargin,o=this.extraTopMargin,n=this.rangeSelector;if(i&&i.enabled&&!ey(n)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=n=new e(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,n){let t=i&&i.verticalAlign||n.options&&n.options.verticalAlign;n.options.floating||("bottom"===t?this.extraBottomMargin=!0:"middle"===t||(this.extraTopMargin=!0)),(this.extraBottomMargin!==s||this.extraTopMargin!==o)&&(this.isDirtyBox=!0)}}let eR=function(t,i,s){if(e=s,ew(em,"RangeSelector")){let e=i.prototype;t.prototype.minFromRange=eE,eb(i,"afterGetContainer",eP),eb(i,"beforeRender",eB),eb(i,"destroy",eC),eb(i,"getMargins",eI),eb(i,"redraw",eT),eb(i,"update",eD),eb(i,"beforeRedraw",eS),e.callbacks.push(eT),ev(ef,{rangeSelector:ex.rangeSelector}),ev(ef.lang,ex.lang)}},eL=s.default.SVGElement;var eG=u.n(eL);let ez=s.default.Templating;var eN=u.n(ez);(n=l||(l={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},n.splice=function(t,e,i,s,o=[]){if(Array.isArray(t))return Array.isArray(o)||(o=Array.from(o)),{removed:t.splice(e,i,...o),array:t};let n=Object.getPrototypeOf(t).constructor,r=t[s?"subarray":"slice"](e,e+i),a=new n(t.length-i+o.length);return a.set(t.subarray(0,e),0),a.set(o,e),a.set(t.subarray(e+i),e+o.length),{removed:r,array:a}};let{setLength:eW,splice:eF}=l,{fireEvent:eH,objectEach:eU,uniqueKey:eY}=x(),eX=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||eY(),this.modified=this,this.rowCount=0,this.versionTag=eY();let e=0;eU(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,eU(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=eW(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;eU(this.columns,(s,o)=>{this.columns[o]=eF(s,t,e).array,i=s.length}),this.rowCount=i}eH(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=eY()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,s){this.setColumns({[t]:e},i,s)}setColumns(t,e,i){let s=this.rowCount;eU(t,(t,e)=>{this.columns[e]=t.slice(),s=t.length}),this.applyRowCount(s),i?.silent||(eH(this,"afterSetColumns"),this.versionTag=eY())}setRow(t,e=this.rowCount,i,s){let{columns:o}=this,n=i?this.rowCount+1:e+1;eU(t,(t,r)=>{let a=o[r]||s?.addColumns!==!1&&Array(n);a&&(i?a=eF(a,e,0,!0,[t]).array:a[e]=t,o[r]=a)}),n>this.rowCount&&this.applyRowCount(n),s?.silent||(eH(this,"afterSetRows"),this.versionTag=eY())}},{addEvent:eV,correctFloat:e_,css:ej,defined:eq,error:eZ,isNumber:e$,pick:eK,timeUnits:eJ,isString:eQ}=x();!function(t){function e(t,i,s,o,n=[],r=0,a){let l={},h=this.options.tickPixelInterval,d=this.chart.time,c=[],p,u,g,x,f,m=0,b=[],y=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!n||n.length<3||void 0===i)return d.getTimeTicks.apply(d,arguments);let v=n.length;for(p=0;p<v;p++){if(f=p&&n[p-1]>s,n[p]<i&&(m=p),p===v-1||n[p+1]-n[p]>5*r||f){if(n[p]>y){for(u=d.getTimeTicks(t,n[m],n[p],o);u.length&&u[0]<=y;)u.shift();u.length&&(y=u[u.length-1]),c.push(b.length),b=b.concat(u)}m=p+1}if(f)break}if(u){if(x=u.info,a&&x.unitRange<=eJ.hour){for(m=1,p=b.length-1;m<p;m++)d.dateFormat("%d",b[m])!==d.dateFormat("%d",b[m-1])&&(l[b[m]]="day",g=!0);g&&(l[b[0]]="day"),x.higherRanks=l}x.segmentStarts=c,b.info=x}else eZ(12,!1,this.chart);if(a&&eq(h)){let t=b.length,e=[],i=[],o,n,r,a,d,c=t;for(;c--;)n=this.translate(b[c]),r&&(i[c]=r-n),e[c]=r=n;for(i.sort((t,e)=>t-e),(a=i[Math.floor(i.length/2)])<.6*h&&(a=null),c=b[t-1]>s?t-1:t,r=void 0;c--;)d=Math.abs(r-(n=e[c])),r&&d<.8*h&&(null===a||d<.8*a)?(l[b[c]]&&!l[b[c+1]]?(o=c+1,r=n):o=c,b.splice(o,1)):r=n}return b}function i(t){let e=this.ordinal.positions;if(!e)return t;let i=e.length-1,s;return(t<0?t=e[0]:t>i?t=e[i]:(i=Math.floor(t),s=t-i),void 0!==s&&void 0!==e[i])?e[i]+(s?s*(e[i+1]-e[i]):0):t}function s(t){let e=this.ordinal,i=this.old?this.old.min:this.min,s=this.old?this.old.transA:this.transA,o=e.getExtendedPositions();if(o?.length){let n=e_((t-i)*s+this.minPixelPadding),r=e_(e.getIndexOfPoint(n,o)),a=e_(r%1);if(r>=0&&r<=o.length-1){let t=o[Math.floor(r)],e=o[Math.ceil(r)];return o[Math.floor(r)]+a*(e-t)}}return t}function o(e,i){let s=t.Additions.findIndexOf(e,i,!0);if(e[s]===i)return s;let o=(i-e[s])/(e[s+1]-e[s]);return s+o}function n(){this.ordinal||(this.ordinal=new t.Additions(this))}function r(){let{eventArgs:t,options:e}=this;if(this.isXAxis&&eq(e.overscroll)&&0!==e.overscroll&&e$(this.max)&&e$(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&(t?.trigger!=="pan"||this.isInternal)&&t?.trigger!=="navigator")){let i=this.ordinal.convertOverscroll(e.overscroll);this.max+=i,!this.isInternal&&eq(this.userMin)&&t?.trigger!=="mousewheel"&&(this.min+=i)}}function a(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function l(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function h(t){let e=this.xAxis[0],i=e.ordinal.convertOverscroll(e.options.overscroll),s=t.originalEvent.chartX,o=this.options.chart.panning,n=!1;if(o&&"y"!==o.type&&e.options.ordinal&&e.series.length&&(!t.touches||t.touches.length<=1)){let t,o,r=this.mouseDownX,a=e.getExtremes(),l=a.dataMin,h=a.dataMax,d=a.min,c=a.max,p=this.hoverPoints,u=e.closestPointRange||e.ordinal?.overscrollPointsRange,g=Math.round((r-s)/(e.translationSlope*(e.ordinal.slope||u))),x=e.ordinal.getExtendedPositions(),f={ordinal:{positions:x,extendedOrdinalPositions:x}},m=e.index2val,b=e.val2lin;if(d<=l&&g<0||c+i>=h&&g>0)return;f.ordinal.positions?Math.abs(g)>1&&(p&&p.forEach(function(t){t.setState()}),h>(o=f.ordinal.positions)[o.length-1]&&o.push(h),this.setFixedRange(c-d),(t=e.navigatorAxis.toFixedRange(void 0,void 0,m.apply(f,[b.apply(f,[d,!0])+g]),m.apply(f,[b.apply(f,[c,!0])+g]))).min>=Math.min(o[0],d)&&t.max<=Math.max(o[o.length-1],c)+i&&e.setExtremes(t.min,t.max,!0,!1,{trigger:"pan"}),this.mouseDownX=s,ej(this.container,{cursor:"move"})):n=!0}else n=!0;n||o&&/y/.test(o.type)?i&&(e.max=e.dataMax+i):t.preventDefault()}function d(){let t=this.xAxis;t?.options.ordinal&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function c(t,e){let i,s=this.ordinal,n=s.positions,r=s.slope,a;if(!n)return t;let l=n.length;if(n[0]<=t&&n[l-1]>=t)i=o(n,t);else{if(a=s.getExtendedPositions?.(),!a?.length)return t;let l=a.length;r||(r=(a[l-1]-a[0])/l);let h=o(a,n[0]);if(t>=a[0]&&t<=a[l-1])i=o(a,t)-h;else{if(!e)return t;i=t<a[0]?-h-(a[0]-t)/r:(t-a[l-1])/r+l-h}}return e?i:r*(i||0)+s.offset}t.compose=function(t,o,p){let u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=e,u.index2val=i,u.lin2val=s,u.val2lin=c,u.ordinal2lin=u.val2lin,eV(t,"afterInit",n),eV(t,"foundExtremes",r),eV(t,"afterSetScale",a),eV(t,"initialAxisTranslation",l),eV(p,"pan",h),eV(p,"touchpan",h),eV(o,"updatedData",d)),t},t.Additions=class{constructor(t){this.index={},this.axis=t}beforeSetTickPositions(){let t=this.axis,e=t.ordinal,i=t.getExtremes(),s=i.min,o=i.max,n=t.brokenAxis?.hasBreaks,r=t.options.ordinal,a,l,h,d,c,p,u,g=[],x=Number.MAX_VALUE,f=!1,m=!1,b=!1;if(r||n){let i=0;if(t.series.forEach(function(t,e){let s=t.getColumn("x",!0);if(l=[],e>0&&"highcharts-navigator-series"!==t.options.id&&s.length>1&&(m=i!==s[1]-s[0]),i=s[1]-s[0],t.boosted&&(b=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||n)&&(a=(g=g.concat(s)).length,g.sort(function(t,e){return t-e}),x=Math.min(x,eK(t.closestPointRange,x)),a)){for(e=0;e<a-1;)g[e]!==g[e+1]&&l.push(g[e+1]),e++;l[0]!==g[0]&&l.unshift(g[0]),g=l}}),t.ordinal.originalOrdinalRange||(t.ordinal.originalOrdinalRange=(g.length-1)*x),m&&b&&(g.pop(),g.shift()),(a=g.length)>2){for(h=g[1]-g[0],u=a-1;u--&&!f;)g[u+1]-g[u]!==h&&(f=!0);!t.options.keepOrdinalPadding&&(g[0]-s>h||o-g[g.length-1]>h)&&(f=!0)}else t.options.overscroll&&(2===a?x=g[1]-g[0]:1===a?(x=t.ordinal.convertOverscroll(t.options.overscroll),g=[g[0],g[0]+x]):x=e.overscrollPointsRange);f||t.forceOrdinal?(t.options.overscroll&&(e.overscrollPointsRange=x,g=g.concat(e.getOverscrollPositions())),e.positions=g,d=t.ordinal2lin(Math.max(s,g[0]),!0),c=Math.max(t.ordinal2lin(Math.min(o,g[g.length-1]),!0),1),e.slope=p=(o-s)/(c-d),e.offset=s-d*p):(e.overscrollPointsRange=eK(t.closestPointRange,e.overscrollPointsRange),e.positions=t.ordinal.slope=e.offset=void 0)}t.isOrdinal=r&&f,e.groupIntervalFactor=null}static findIndexOf(t,e,i){let s=0,o=t.length-1,n;for(;s<o;)t[n=Math.ceil((s+o)/2)]<=e?s=n:o=n-1;return t[s]===e?s:i?s:-1}getExtendedPositions(t=!0){let e=this,i=e.axis,s=i.constructor.prototype,o=i.chart,n=i.series.reduce((t,e)=>{let i=e.currentDataGrouping;return t+(i?i.count+i.unitName:"raw")},""),r=t?i.ordinal.convertOverscroll(i.options.overscroll):0,a=i.getExtremes(),l,h,d=e.index;return d||(d=e.index={}),!d[n]&&((l={series:[],chart:o,forceOrdinal:!1,getExtremes:function(){return{min:a.dataMin,max:a.dataMax+r}},applyGrouping:s.applyGrouping,getGroupPixelWidth:s.getGroupPixelWidth,getTimeTicks:s.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:s.ordinal2lin,getIndexOfPoint:s.getIndexOfPoint,val2lin:s.val2lin}).ordinal.axis=l,i.series.forEach(i=>{h={xAxis:l,chart:o,groupPixelWidth:i.groupPixelWidth,destroyGroupedData:x().noop,getColumn:i.getColumn,applyGrouping:i.applyGrouping,getProcessedData:i.getProcessedData,reserveSpace:i.reserveSpace,visible:i.visible};let s=i.getColumn("x").concat(t?e.getOverscrollPositions():[]);h.dataTable=new eX({columns:{x:s}}),h.options={...i.options,dataGrouping:i.currentDataGrouping?{firstAnchor:i.options.dataGrouping?.firstAnchor,anchor:i.options.dataGrouping?.anchor,lastAnchor:i.options.dataGrouping?.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[i.currentDataGrouping.unitName,[i.currentDataGrouping.count]]]}:{enabled:!1}},l.series.push(h),i.processData.apply(h)}),l.applyGrouping({hasExtremesChanged:!0}),h?.closestPointRange!==h?.basePointRange&&h.currentDataGrouping&&(l.forceOrdinal=!0),i.ordinal.beforeSetTickPositions.apply({axis:l}),!i.ordinal.originalOrdinalRange&&l.ordinal.originalOrdinalRange&&(i.ordinal.originalOrdinalRange=l.ordinal.originalOrdinalRange),l.ordinal.positions&&(d[n]=l.ordinal.positions)),d[n]}getGroupIntervalFactor(t,e,i){let s=i.getColumn("x",!0),o=s.length,n=[],r,a,l=this.groupIntervalFactor;if(!l){for(a=0;a<o-1;a++)n[a]=s[a+1]-s[a];n.sort(function(t,e){return t-e}),r=n[Math.floor(o/2)],t=Math.max(t,s[0]),e=Math.min(e,s[o-1]),this.groupIntervalFactor=l=o*r/(e-t)}return l}getIndexOfPoint(t,e){let i=this.axis,s=i.min,n=i.minPixelPadding;return o(e,s)+e_((t-n)/(i.translationSlope*(this.slope||i.closestPointRange||this.overscrollPointsRange)))}getOverscrollPositions(){let t=this.axis,e=this.convertOverscroll(t.options.overscroll),i=this.overscrollPointsRange,s=[],o=t.dataMax;if(eq(i))for(;o<t.dataMax+e;)s.push(o+=i);return s}postProcessTickInterval(t){let e,i=this.axis,s=this.slope,o=i.closestPointRange;return s&&o?i.options.breaks?o||t:t/(s/o):t}convertOverscroll(t=0){let e=this,i=e.axis,s=function(t){return eK(e.originalOrdinalRange,eq(i.dataMax)&&eq(i.dataMin)?i.dataMax-i.dataMin:0)*t};if(eQ(t)){let e,o=parseInt(t,10);if(eq(i.min)&&eq(i.max)&&eq(i.dataMin)&&eq(i.dataMax)&&!(e=i.max-i.min==i.dataMax-i.dataMin)&&(this.originalOrdinalRange=i.max-i.min),/%$/.test(t))return s(o/100);if(/px/.test(t)){let t=Math.min(o,.9*i.len)/i.len;return s(t/(e?1-t:1))}return 0}return t}}}(h||(h={}));let e0=h,{defaultOptions:e1}=x(),{format:e2}=eN(),{addEvent:e3,createElement:e5,css:e6,defined:e9,destroyObjectProperties:e8,diffObjects:e4,discardElement:e7,extend:it,fireEvent:ie,isNumber:ii,isString:is,merge:io,objectEach:ir,pick:ia,splat:il}=x();function ih(t){let e=e=>RegExp(`%[[a-zA-Z]*${e}`).test(t);if(is(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";let i=is(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(e):t.dateStyle||t.day||t.month||t.year,s=is(t)?["H","k","I","l","M","S"].some(e):t.timeStyle||t.hour||t.minute||t.second;return i&&s?"datetime-local":i?"date":s?"time":"text"}class id{static compose(t,e){eR(t,e,id)}constructor(t){this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=()=>{let t=0;return this.buttons.forEach(e=>{let i=e.getBBox();i.width>t&&(t=i.width)}),t},this.init(t)}clickButton(t,e){let i=this.chart,s=this.buttonOptions[t],o=i.xAxis[0],n=i.scroller&&i.scroller.getUnionExtremes()||o||{},r=s.type,a=s.dataGrouping,l=n.dataMin,h=n.dataMax,d,c=ii(o?.max)?Math.round(Math.min(o.max,h??o.max)):void 0,p,u=s._range,g,x,f,m=!0;if(null!==l&&null!==h){if(this.setSelected(t),a&&(this.forcedDataGrouping=!0,_().prototype.setDataGrouping.call(o||{chart:this.chart},a,!1),this.frozenStates=s.preserveDataGrouping),"month"===r||"year"===r)o?(x={range:s,max:c,chart:i,dataMin:l,dataMax:h},d=o.minFromRange.call(x),ii(x.newMax)&&(c=x.newMax),m=!1):u=s;else if(u)ii(c)&&(c=Math.min((d=Math.max(c-u,l))+u,h),m=!1);else if("ytd"===r){if(o)!o.hasData()||ii(h)&&ii(l)||(l=Number.MAX_VALUE,h=-Number.MAX_VALUE,i.series.forEach(t=>{let e=t.getColumn("x");e.length&&(l=Math.min(e[0],l),h=Math.max(e[e.length-1],h))}),e=!1),ii(h)&&ii(l)&&(d=g=(f=this.getYTDExtremes(h,l)).min,c=f.max);else{this.deferredYTDClick=t;return}}else"all"===r&&o&&(i.navigator&&i.navigator.baseSeries[0]&&(i.navigator.baseSeries[0].xAxis.options.range=void 0),d=l,c=h);if(m&&s._offsetMin&&e9(d)&&(d+=s._offsetMin),s._offsetMax&&e9(c)&&(c+=s._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),o)ii(d)&&ii(c)&&(o.setExtremes(d,c,ia(e,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:s}),i.setFixedRange(s._range));else{p=il(i.options.xAxis||{})[0];let t=e3(i,"afterCreateAxes",function(){let t=i.xAxis[0];t.range=t.options.range=u,t.min=t.options.min=g});e3(i,"load",function(){let e=i.xAxis[0];i.setFixedRange(s._range),e.options.range=p.range,e.options.min=p.min,t()})}ie(this,"afterBtnClick")}}setSelected(t){this.selected=this.options.selected=t}init(t){let e=this,i=t.options.rangeSelector,s=t.options.lang,o=i.buttons,n=i.selected,r=function(){let t=e.minInput,i=e.maxInput;t&&t.blur&&ie(t,"blur"),i&&i.blur&&ie(i,"blur")};e.chart=t,e.options=i,e.buttons=[],e.buttonOptions=o.map(t=>(t.type&&s.rangeSelector&&(t.text??(t.text=s.rangeSelector[`${t.type}Text`]),t.title??(t.title=s.rangeSelector[`${t.type}Title`])),t.text=e2(t.text,{count:t.count||1}),t.title=e2(t.title,{count:t.count||1}),t)),this.eventsToUnbind=[],this.eventsToUnbind.push(e3(t.container,"mousedown",r)),this.eventsToUnbind.push(e3(t,"resize",r)),o.forEach(e.computeButtonRange),void 0!==n&&o[n]&&this.clickButton(n,!1),this.eventsToUnbind.push(e3(t,"load",function(){t.xAxis&&t.xAxis[0]&&e3(t.xAxis[0],"setExtremes",function(i){ii(this.max)&&ii(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()}updateButtonStates(){let t=this,e=this.chart,i=this.dropdown,s=this.dropdownLabel,o=e.xAxis[0],n=Math.round(o.max-o.min),r=!o.hasVisibleSeries,a=24*36e5,l=e.scroller&&e.scroller.getUnionExtremes()||o,h=l.dataMin,d=l.dataMax,c=t.getYTDExtremes(d,h),p=c.min,u=c.max,g=t.selected,x=t.options.allButtonsEnabled,f=Array(t.buttonOptions.length).fill(0),m=ii(g),b=t.buttons,y=!1,v=null;t.buttonOptions.forEach((e,i)=>{let s=e._range,l=e.type,c=e.count||1,b=e._offsetMax-e._offsetMin,M=i===g,k=s>d-h,A=s<o.minRange,w=!1,O=s===n;if(M&&k&&(y=!0),o.isOrdinal&&o.ordinal?.positions&&s&&n<s){let t=o.ordinal.positions,e=e0.Additions.findIndexOf(t,o.min,!0),i=Math.min(e0.Additions.findIndexOf(t,o.max,!0)+1,t.length-1);t[i]-t[e]>s&&(O=!0)}else("month"===l||"year"===l)&&n+36e5>=({month:28,year:365})[l]*a*c-b&&n-36e5<=({month:31,year:366})[l]*a*c+b?O=!0:"ytd"===l?(O=u-p+b===n,w=!M):"all"===l&&(O=o.max-o.min>=d-h);let E=!x&&!(y&&"all"===l)&&(k||A||r),S=y&&"all"===l||!w&&O||M&&t.frozenStates;E?f[i]=3:S&&(!m||i===g)&&(v=i)}),null!==v?(f[v]=2,t.setSelected(v),this.dropdown&&(this.dropdown.selectedIndex=v+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),s&&(s.setState(0),s.attr({text:(e1.lang.rangeSelectorZoom||"")+" ▾"})));for(let e=0;e<f.length;e++){let o=f[e],n=b[e];if(n.state!==o&&(n.setState(o),i)){i.options[e+1].disabled=3===o,2===o&&(s&&(s.setState(2),s.attr({text:t.buttonOptions[e].text+" ▾"})),i.selectedIndex=e+1);let n=s.getBBox();e6(i,{width:`${n.width}px`,height:`${n.height}px`})}}}computeButtonRange(t){let e=t.type,i=t.count||1,s={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};s[e]?t._range=s[e]*i:("month"===e||"year"===e)&&(t._range=24*({month:30,year:365})[e]*36e5*i),t._offsetMin=ia(t.offsetMin,0),t._offsetMax=ia(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin}getInputValue(t){let e="min"===t?this.minInput:this.maxInput,i=this.chart.options.rangeSelector,s=this.chart.time;return e?("text"===e.type&&i.inputDateParser||this.defaultInputDateParser)(e.value,"UTC"===s.timezone,s):0}setInputValue(t,e){let i=this.options,s=this.chart.time,o="min"===t?this.minInput:this.maxInput,n="min"===t?this.minDateBox:this.maxDateBox;if(o){o.setAttribute("type",ih(i.inputDateFormat||"%e %b %Y"));let t=o.getAttribute("data-hc-time"),r=e9(t)?Number(t):void 0;if(e9(e)){let t=r;e9(t)&&o.setAttribute("data-hc-time-previous",t),o.setAttribute("data-hc-time",e),r=e}o.value=s.dateFormat(this.inputTypeFormats[o.type]||i.inputEditDateFormat,r),n&&n.attr({text:s.dateFormat(i.inputDateFormat,r)})}}setInputExtremes(t,e,i){let s="min"===t?this.minInput:this.maxInput;if(s){let t=this.inputTypeFormats[s.type],o=this.chart.time;if(t){let n=o.dateFormat(t,e);s.min!==n&&(s.min=n);let r=o.dateFormat(t,i);s.max!==r&&(s.max=r)}}}showInput(t){let e="min"===t?this.minDateBox:this.maxDateBox,i="min"===t?this.minInput:this.maxInput;if(i&&e&&this.inputGroup){let t="text"===i.type,{translateX:s=0,translateY:o=0}=this.inputGroup,{x:n=0,width:r=0,height:a=0}=e,{inputBoxWidth:l}=this.options;e6(i,{width:t?r+(l?-2:20)+"px":"auto",height:a-2+"px",border:"2px solid silver"}),t&&l?e6(i,{left:s+n+"px",top:o+"px"}):e6(i,{left:Math.min(Math.round(n+s-(i.offsetWidth-r)/2),this.chart.chartWidth-i.offsetWidth)+"px",top:o-(i.offsetHeight-a)/2+"px"})}}hideInput(t){let e="min"===t?this.minInput:this.maxInput;e&&e6(e,{top:"-9999em",border:0,width:"1px",height:"1px"})}defaultInputDateParser(t,e,i){return i?.parse(t)||0}drawInput(t){let{chart:e,div:i,inputGroup:s}=this,o=this,n=e.renderer.style||{},r=e.renderer,a=e.options.rangeSelector,l=e1.lang,h="min"===t;function d(t){let{maxInput:i,minInput:s}=o,n=e.xAxis[0],r=e.scroller?.getUnionExtremes()||n,a=r.dataMin,l=r.dataMax,d=e.xAxis[0].getExtremes()[t],c=o.getInputValue(t);ii(c)&&c!==d&&(h&&i&&ii(a)?c>Number(i.getAttribute("data-hc-time"))?c=void 0:c<a&&(c=a):s&&ii(l)&&(c<Number(s.getAttribute("data-hc-time"))?c=void 0:c>l&&(c=l)),void 0!==c&&n.setExtremes(h?c:n.min,h?n.max:c,void 0,void 0,{trigger:"rangeSelectorInput"}))}let c=l[h?"rangeSelectorFrom":"rangeSelectorTo"]||"",p=r.label(c,0).addClass("highcharts-range-label").attr({padding:2*!!c,height:c?a.inputBoxHeight:0}).add(s),u=r.label("",0).addClass("highcharts-range-input").attr({padding:2,width:a.inputBoxWidth,height:a.inputBoxHeight,"text-align":"center"}).on("click",function(){o.showInput(t),o[t+"Input"].focus()});e.styledMode||u.attr({stroke:a.inputBoxBorderColor,"stroke-width":1}),u.add(s);let g=e5("input",{name:t,className:"highcharts-range-selector"},void 0,i);g.setAttribute("type",ih(a.inputDateFormat||"%e %b %Y")),e.styledMode||(p.css(io(n,a.labelStyle)),u.css(io({color:"#333333"},n,a.inputStyle)),e6(g,it({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:n.fontSize,fontFamily:n.fontFamily,top:"-9999em"},a.inputStyle))),g.onfocus=()=>{o.showInput(t)},g.onblur=()=>{g===x().doc.activeElement&&d(t),o.hideInput(t),o.setInputValue(t),g.blur()};let f=!1;return g.onchange=()=>{f||(d(t),o.hideInput(t),g.blur())},g.onkeypress=e=>{13===e.keyCode&&d(t)},g.onkeydown=e=>{f=!0,("ArrowUp"===e.key||"ArrowDown"===e.key||"Tab"===e.key)&&d(t)},g.onkeyup=()=>{f=!1},{dateBox:u,input:g,label:p}}getPosition(){let t=this.chart,e=t.options.rangeSelector,i="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:i+e.buttonPosition.y,inputTop:i+e.inputPosition.y-10}}getYTDExtremes(t,e){let i=this.chart.time,s=i.toParts(t)[0];return{max:t,min:Math.max(e,i.makeTime(s,0))}}createElements(){let t=this.chart,e=t.renderer,i=t.container,s=t.options,o=s.rangeSelector,n=o.inputEnabled,r=ia(s.chart.style?.zIndex,0)+1;!1!==o.enabled&&(this.group=e.g("range-selector-group").attr({zIndex:7}).add(),this.div=e5("div",void 0,{position:"relative",height:0,zIndex:r}),this.buttonOptions.length&&this.renderButtons(),i.parentNode&&i.parentNode.insertBefore(this.div,i),n&&this.createInputs())}createInputs(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);let t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;let e=this.drawInput("max");this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input}render(t,e){if(!1===this.options.enabled)return;let i=this.chart,s=i.options.rangeSelector;if(s.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",e),this.chart.styledMode||(this.maxLabel?.css(s.labelStyle),this.minLabel?.css(s.labelStyle));let o=i.scroller&&i.scroller.getUnionExtremes()||i.xAxis[0]||{};if(e9(o.dataMin)&&e9(o.dataMax)){let t=i.xAxis[0].minRange||0;this.setInputExtremes("min",o.dataMin,Math.min(o.dataMax,this.getInputValue("max"))-t),this.setInputExtremes("max",Math.max(o.dataMin,this.getInputValue("min"))+t,o.dataMax)}if(this.inputGroup){let t=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(e=>{if(e){let{width:i}=e.getBBox();i&&(e.attr({x:t}),t+=i+s.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(s.labelStyle),this.alignElements(),this.updateButtonStates()}renderButtons(){var t;let{chart:e,options:i}=this,s=e1.lang,o=e.renderer,n=io(i.buttonTheme),r=n&&n.states;delete n.width,delete n.states,this.buttonGroup=o.g("range-selector-buttons").add(this.group);let a=this.dropdown=e5("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),l=e.userOptions.rangeSelector?.buttonTheme;this.dropdownLabel=o.button("",0,0,()=>{},io(n,{"stroke-width":ia(n["stroke-width"],0),width:"auto",paddingLeft:ia(i.buttonTheme.paddingLeft,l?.padding,8),paddingRight:ia(i.buttonTheme.paddingRight,l?.padding,8)}),r&&r.hover,r&&r.select,r&&r.disabled).hide().add(this.group),e3(a,"touchstart",()=>{a.style.fontSize="16px"});let h=x().isMS?"mouseover":"mouseenter",d=x().isMS?"mouseout":"mouseleave";e3(a,h,()=>{ie(this.dropdownLabel.element,h)}),e3(a,d,()=>{ie(this.dropdownLabel.element,d)}),e3(a,"change",()=>{ie(this.buttons[a.selectedIndex-1].element,"click")}),this.zoomText=o.label(s.rangeSelectorZoom||"",0).attr({padding:i.buttonTheme.padding,height:i.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(i.labelStyle),(t=i.buttonTheme)["stroke-width"]??(t["stroke-width"]=0)),e5("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,a),this.createButtons()}createButtons(){let{options:t}=this,e=io(t.buttonTheme),i=e&&e.states,s=e.width||28;delete e.width,delete e.states,this.buttonOptions.forEach((t,e)=>{this.createButton(t,e,s,i)})}createButton(t,e,i,s){let{dropdown:o,buttons:n,chart:r,options:a}=this,l=r.renderer,h=io(a.buttonTheme);o?.add(e5("option",{textContent:t.title||t.text}),e+2),n[e]=l.button(t.text??"",0,0,i=>{let s,o=t.events&&t.events.click;o&&(s=o.call(t,i)),!1!==s&&this.clickButton(e),this.isActive=!0},h,s&&s.hover,s&&s.select,s&&s.disabled).attr({"text-align":"center",width:i}).add(this.buttonGroup),t.title&&n[e].attr("title",t.title)}alignElements(){let{buttonGroup:t,buttons:e,chart:i,group:s,inputGroup:o,options:n,zoomText:r}=this,a=i.options,l=a.exporting&&!1!==a.exporting.enabled&&a.navigation&&a.navigation.buttonOptions,{buttonPosition:h,inputPosition:d,verticalAlign:c}=n,p=(t,e,s)=>l&&this.titleCollision(i)&&"top"===c&&s&&e.y-t.getBBox().height-12<(l.y||0)+(l.height||0)+i.spacing[0]?-40:0,u=i.plotLeft;if(s&&h&&d){let a=h.x-i.spacing[3];if(t){if(this.positionButtons(),!this.initialButtonGroupWidth){let t=0;r&&(t+=r.getBBox().width+5),e.forEach((i,s)=>{t+=i.width||0,s!==e.length-1&&(t+=n.buttonSpacing)}),this.initialButtonGroupWidth=t}u-=i.spacing[3];let o=p(t,h,"right"===h.align||"right"===d.align);this.alignButtonGroup(o),this.buttonGroup?.translateY&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),s.placed=t.placed=i.hasLoaded}let l=0;n.inputEnabled&&o&&(l=p(o,d,"right"===h.align||"right"===d.align),"left"===d.align?a=u:"right"===d.align&&(a=-Math.max(i.axisOffset[1],-l)),o.align({y:d.y,width:o.getBBox().width,align:d.align,x:d.x+a-2},!0,i.spacingBox),o.placed=i.hasLoaded),this.handleCollision(l),s.align({verticalAlign:c},!0,i.spacingBox);let g=s.alignAttr.translateY,x=s.getBBox().height+20,f=0;if("bottom"===c){let t=i.legend&&i.legend.options;f=g-(x=x+(t&&"bottom"===t.verticalAlign&&t.enabled&&!t.floating?i.legend.legendHeight+ia(t.margin,10):0)-20)-(n.floating?0:n.y)-(i.titleOffset?i.titleOffset[2]:0)-10}"top"===c?(n.floating&&(f=0),i.titleOffset&&i.titleOffset[0]&&(f=i.titleOffset[0]),f+=i.margin[0]-i.spacing[0]||0):"middle"===c&&(d.y===h.y?f=g:(d.y||h.y)&&(d.y<0||h.y<0?f-=Math.min(d.y,h.y):f=g-x)),s.translate(n.x,n.y+Math.floor(f));let{minInput:m,maxInput:b,dropdown:y}=this;n.inputEnabled&&m&&b&&(m.style.marginTop=s.translateY+"px",b.style.marginTop=s.translateY+"px"),y&&(y.style.marginTop=s.translateY+"px")}}redrawElements(){let t=this.chart,{inputBoxHeight:e,inputBoxBorderColor:i}=this.options;if(this.maxDateBox?.attr({height:e}),this.minDateBox?.attr({height:e}),t.styledMode||(this.maxDateBox?.attr({stroke:i}),this.minDateBox?.attr({stroke:i})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;let t=this.options.buttons??[],e=Math.min(t.length,this.buttonOptions.length),{dropdown:i,options:s}=this,o=io(s.buttonTheme),n=o&&o.states,r=o.width||28;if(t.length<this.buttonOptions.length)for(let e=this.buttonOptions.length-1;e>=t.length;e--){let t=this.buttons.pop();t?.destroy(),this.dropdown?.options.remove(e+1)}for(let s=e-1;s>=0;s--)if(0!==Object.keys(e4(t[s],this.buttonOptions[s])).length){let e=t[s];this.buttons[s].destroy(),i?.options.remove(s+1),this.createButton(e,s,r,n),this.computeButtonRange(e)}if(t.length>this.buttonOptions.length)for(let e=this.buttonOptions.length;e<t.length;e++)this.createButton(t[e],e,r,n),this.computeButtonRange(t[e]);this.buttonOptions=this.options.buttons??[],e9(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}}alignButtonGroup(t,e){let{chart:i,options:s,buttonGroup:o,dropdown:n,dropdownLabel:r}=this,{buttonPosition:a}=s,l=i.plotLeft-i.spacing[3],h=a.x-i.spacing[3],d=i.plotLeft;"right"===a.align?(h+=t-l,this.hasVisibleDropdown&&(d=i.chartWidth+t-this.maxButtonWidth()-20)):"center"===a.align&&(h-=l/2,this.hasVisibleDropdown&&(d=i.chartWidth/2-this.maxButtonWidth())),n&&e6(n,{left:d+"px",top:o?.translateY+"px"}),r?.attr({x:d}),o&&o.align({y:a.y,width:ia(e,this.initialButtonGroupWidth),align:a.align,x:h},!0,i.spacingBox)}positionButtons(){let{buttons:t,chart:e,options:i,zoomText:s}=this,o=e.hasLoaded?"animate":"attr",{buttonPosition:n}=i,r=e.plotLeft,a=r;s&&"hidden"!==s.visibility&&(s[o]({x:ia(r+n.x,r)}),a+=n.x+s.getBBox().width+5);for(let e=0,s=this.buttonOptions.length;e<s;++e)"hidden"!==t[e].visibility?(t[e][o]({x:a}),a+=(t[e].width||0)+i.buttonSpacing):t[e][o]({x:r})}handleCollision(t){let{chart:e,buttonGroup:i,inputGroup:s,initialButtonGroupWidth:o}=this,{buttonPosition:n,dropdown:r,inputPosition:a}=this.options,l=()=>{s&&i&&s.attr({translateX:s.alignAttr.translateX+(e.axisOffset[1]>=-t?0:-t),translateY:s.alignAttr.translateY+i.getBBox().height+10})};s&&i?a.align===n.align?(l(),o>e.plotWidth+t-20?this.collapseButtons():this.expandButtons()):o-t+s.getBBox().width>e.plotWidth?"responsive"===r?this.collapseButtons():l():this.expandButtons():i&&"responsive"===r&&(o>e.plotWidth?this.collapseButtons():this.expandButtons()),i&&("always"===r&&this.collapseButtons(),"never"===r&&this.expandButtons()),this.alignButtonGroup(t)}collapseButtons(){let{buttons:t,zoomText:e}=this;!0!==this.isCollapsed&&(this.isCollapsed=!0,e.hide(),t.forEach(t=>void t.hide()),this.showDropdown())}expandButtons(){let{buttons:t,zoomText:e}=this;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),e.show(),t.forEach(t=>void t.show()),this.positionButtons())}showDropdown(){let{buttonGroup:t,dropdownLabel:e,dropdown:i}=this;t&&i&&(e.show(),e6(i,{visibility:"inherit"}),this.hasVisibleDropdown=!0)}hideDropdown(){let{dropdown:t}=this;t&&(this.dropdownLabel.hide(),e6(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)}getHeight(){let t=this.options,e=this.group,i=t.inputPosition,s=t.buttonPosition,o=t.y,n=s.y,r=i.y,a=0;if(t.height)return t.height;this.alignElements(),a=e?e.getBBox(!0).height+13+o:0;let l=Math.min(r,n);return(r<0&&n<0||r>0&&n>0)&&(a+=Math.abs(l)),a}titleCollision(t){return!(t.options.title.text||t.options.subtitle.text)}update(t,e=!0){let i=this.chart;if(io(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,i.options.rangeSelector.selected=void 0),e9(t.enabled))return this.destroy(),this.init(i);this.isDirty=!!t.buttons,e&&this.render()}destroy(){let t=this,e=t.minInput,i=t.maxInput;t.eventsToUnbind&&(t.eventsToUnbind.forEach(t=>t()),t.eventsToUnbind=void 0),e8(t.buttons),e&&(e.onfocus=e.onblur=e.onchange=null),i&&(i.onfocus=i.onblur=i.onchange=null),ir(t,function(e,i){e&&"chart"!==i&&(e instanceof eG()?e.destroy():e instanceof window.HTMLElement&&e7(e),delete t[i]),e!==id.prototype[i]&&(t[i]=null)},this),this.buttons=[]}}it(id.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}}),u.d({},{}),u.d({},{}),u.d({},{});let{xrange:{prototype:{pointClass:ic}}}=tb().seriesTypes;class ip extends ic{static setGanttPointAliases(t,e){t.x=t.start=e.time.parse(t.start??t.x),t.x2=t.end=e.time.parse(t.end??t.x2),t.partialFill=t.completed=t.completed??t.partialFill}applyOptions(t,e){let i=super.applyOptions(t,e);return ip.setGanttPointAliases(i,i.series.chart),this.isNull=!this.isValid?.(),i}isValid(){return("number"==typeof this.start||"number"==typeof this.x)&&("number"==typeof this.end||"number"==typeof this.x2||this.milestone)}}let{isNumber:iu}=x(),ig=function(t,e){let i=[];for(let s=0;s<t.length;s++){let o=t[s][1],n=t[s][2];if("number"==typeof o&&"number"==typeof n){if(0===s)i.push(["M",o,n]);else if(s===t.length-1)i.push(["L",o,n]);else if(e){let r=t[s-1],a=t[s+1];if(r&&a){let t=r[1],s=r[2],l=a[1],h=a[2];if("number"==typeof t&&"number"==typeof l&&"number"==typeof s&&"number"==typeof h&&t!==l&&s!==h){let r=t<l?1:-1,a=s<h?1:-1;i.push(["L",o-r*Math.min(Math.abs(o-t),e),n-a*Math.min(Math.abs(n-s),e)],["C",o,n,o,n,o+r*Math.min(Math.abs(o-l),e),n+a*Math.min(Math.abs(n-h),e)])}}}else i.push(["L",o,n])}}return i},{pick:ix}=x(),{min:im,max:ib,abs:iy}=Math;function iv(t,e,i){let s=e-1e-7,o=i||0,n=t.length-1,r,a;for(;o<=n;)if((a=s-t[r=n+o>>1].xMin)>0)o=r+1;else{if(!(a<0))return r;n=r-1}return o>0?o-1:0}function iM(t,e){let i=iv(t,e.x+1)+1;for(;i--;){var s;if(t[i].xMax>=e.x&&(s=t[i],e.x<=s.xMax&&e.x>=s.xMin&&e.y<=s.yMax&&e.y>=s.yMin))return i}return -1}function ik(t){let e=[];if(t.length){e.push(["M",t[0].start.x,t[0].start.y]);for(let i=0;i<t.length;++i)e.push(["L",t[i].end.x,t[i].end.y])}return e}function iA(t,e){t.yMin=ib(t.yMin,e.yMin),t.yMax=im(t.yMax,e.yMax),t.xMin=ib(t.xMin,e.xMin),t.xMax=im(t.xMax,e.xMax)}let iw=function(t,e,i){let s=[],o=i.chartObstacles,n=iM(o,t),r=iM(o,e),a,l=ix(i.startDirectionX,iy(e.x-t.x)>iy(e.y-t.y))?"x":"y",h,d,c,p;function u(t,e,i,s,o){let n={x:t.x,y:t.y};return n[e]=i[s||e]+(o||0),n}function g(t,e,i){let s=iy(e[i]-t[i+"Min"])>iy(e[i]-t[i+"Max"]);return u(e,i,t,i+(s?"Max":"Min"),s?1:-1)}r>-1?(a={start:d=g(o[r],e,l),end:e},p=d):p=e,n>-1&&(d=g(h=o[n],t,l),s.push({start:t,end:d}),d[l]>=t[l]==d[l]>=p[l]&&(c=t[l="y"===l?"x":"y"]<e[l],s.push({start:d,end:u(d,l,h,l+(c?"Max":"Min"),c?1:-1)}),l="y"===l?"x":"y"));let x=s.length?s[s.length-1].end:t;d=u(x,l,p),s.push({start:x,end:d});let f=u(d,l="y"===l?"x":"y",p);return s.push({start:d,end:f}),s.push(a),{path:ig(ik(s),i.radius),obstacles:s}};function iO(t,e,i){let s=ix(i.startDirectionX,iy(e.x-t.x)>iy(e.y-t.y)),o=s?"x":"y",n=[],r=i.obstacleMetrics,a=im(t.x,e.x)-r.maxWidth-10,l=ib(t.x,e.x)+r.maxWidth+10,h=im(t.y,e.y)-r.maxHeight-10,d=ib(t.y,e.y)+r.maxHeight+10,c,p,u,g=!1,x=i.chartObstacles,f=iv(x,l),m=iv(x,a);function b(t,e,i){let s,o,n,r,a=t.x<e.x?1:-1;t.x<e.x?(s=t,o=e):(s=e,o=t),t.y<e.y?(r=t,n=e):(r=e,n=t);let l=a<0?im(iv(x,o.x),x.length-1):0;for(;x[l]&&(a>0&&x[l].xMin<=o.x||a<0&&x[l].xMax>=s.x);){if(x[l].xMin<=o.x&&x[l].xMax>=s.x&&x[l].yMin<=n.y&&x[l].yMax>=r.y){if(i)return{y:t.y,x:t.x<e.x?x[l].xMin-1:x[l].xMax+1,obstacle:x[l]};return{x:t.x,y:t.y<e.y?x[l].yMin-1:x[l].yMax+1,obstacle:x[l]}}l+=a}return e}function y(t,e,i,s,o){let n=o.soft,r=o.hard,a=s?"x":"y",l={x:e.x,y:e.y},h={x:e.x,y:e.y},d=t[a+"Max"]>=n[a+"Max"],c=t[a+"Min"]<=n[a+"Min"],p=t[a+"Max"]>=r[a+"Max"],u=t[a+"Min"]<=r[a+"Min"],g=iy(t[a+"Min"]-e[a]),x=iy(t[a+"Max"]-e[a]),f=10>iy(g-x)?e[a]<i[a]:x<g;h[a]=t[a+"Min"],l[a]=t[a+"Max"];let m=b(e,h,s)[a]!==h[a],y=b(e,l,s)[a]!==l[a];return f=m?!y||f:!y&&f,f=c?!d||f:!d&&f,f=u?!p||f:!p&&f}for((f=iM(x=x.slice(m,f+1),e))>-1&&(u=function(t,e,s){let o=im(t.xMax-e.x,e.x-t.xMin)<im(t.yMax-e.y,e.y-t.yMin),n=y(t,e,s,o,{soft:i.hardBounds,hard:i.hardBounds});return o?{y:e.y,x:t[n?"xMax":"xMin"]+(n?1:-1)}:{x:e.x,y:t[n?"yMax":"yMin"]+(n?1:-1)}}(x[f],e,t),n.push({end:e,start:u}),e=u);(f=iM(x,e))>-1;)p=e[o]-t[o]<0,(u={x:e.x,y:e.y})[o]=x[f][p?o+"Max":o+"Min"]+(p?1:-1),n.push({end:e,start:u}),e=u;return{path:ik(c=(c=function t(e,s,o){let n,r,c,p,u,f,m;if(e.x===s.x&&e.y===s.y)return[];let v=o?"x":"y",M=i.obstacleOptions.margin,k={soft:{xMin:a,xMax:l,yMin:h,yMax:d},hard:i.hardBounds};return(u=iM(x,e))>-1?(p=y(u=x[u],e,s,o,k),iA(u,i.hardBounds),m=o?{y:e.y,x:u[p?"xMax":"xMin"]+(p?1:-1)}:{x:e.x,y:u[p?"yMax":"yMin"]+(p?1:-1)},(f=iM(x,m))>-1&&(iA(f=x[f],i.hardBounds),m[v]=p?ib(u[v+"Max"]-M+1,(f[v+"Min"]+u[v+"Max"])/2):im(u[v+"Min"]+M-1,(f[v+"Max"]+u[v+"Min"])/2),e.x===m.x&&e.y===m.y?(g&&(m[v]=p?ib(u[v+"Max"],f[v+"Max"])+1:im(u[v+"Min"],f[v+"Min"])-1),g=!g):g=!1),r=[{start:e,end:m}]):(n=b(e,{x:o?s.x:e.x,y:o?e.y:s.y},o),r=[{start:e,end:{x:n.x,y:n.y}}],n[o?"x":"y"]!==s[o?"x":"y"]&&(p=y(n.obstacle,n,s,!o,k),iA(n.obstacle,i.hardBounds),c={x:o?n.x:n.obstacle[p?"xMax":"xMin"]+(p?1:-1),y:o?n.obstacle[p?"yMax":"yMin"]+(p?1:-1):n.y},o=!o,r=r.concat(t({x:n.x,y:n.y},c,o)))),r=r.concat(t(r[r.length-1].end,s,!o))}(t,e,s)).concat(n.reverse())),obstacles:c}}iw.requiresObstacles=!0,iO.requiresObstacles=!0;let iE={connectors:{type:"straight",radius:0,lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}},{setOptions:iS}=x(),{defined:iP,error:iB,merge:iT}=x();function iC(t){let e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};let i=t.graphic&&t.graphic.getBBox();return i?{xMin:t.plotX-i.width/2,xMax:t.plotX+i.width/2,yMin:t.plotY-i.height/2,yMax:t.plotY+i.height/2}:null}!function(t){function e(t){let e,i,s=iC(this);switch(t.align){case"right":e="xMax";break;case"left":e="xMin"}switch(t.verticalAlign){case"top":i="yMin";break;case"bottom":i="yMax"}return{x:e?s[e]:(s.xMin+s.xMax)/2,y:i?s[i]:(s.yMin+s.yMax)/2}}function i(t,e){let i;return!iP(e)&&(i=iC(this))&&(e={x:(i.xMin+i.xMax)/2,y:(i.yMin+i.yMax)/2}),Math.atan2(e.y-t.y,t.x-e.x)}function s(t,e,i){let s=2*Math.PI,o=iC(this),n=o.xMax-o.xMin,r=o.yMax-o.yMin,a=Math.atan2(r,n),l=n/2,h=r/2,d=o.xMin+l,c=o.yMin+h,p={x:d,y:c},u=t,g=1,x=!1,f=1,m=1;for(;u<-Math.PI;)u+=s;for(;u>Math.PI;)u-=s;return g=Math.tan(u),u>-a&&u<=a?(m=-1,x=!0):u>a&&u<=Math.PI-a?m=-1:u>Math.PI-a||u<=-(Math.PI-a)?(f=-1,x=!0):f=-1,x?(p.x+=f*l,p.y+=m*l*g):(p.x+=r/(2*g)*f,p.y+=m*h),i.x!==d&&(p.x=i.x),i.y!==c&&(p.y=i.y),{x:p.x+e*Math.cos(u),y:p.y-e*Math.sin(u)}}t.compose=function(t,o,n){let r=n.prototype;r.getPathfinderAnchorPoint||(t.prototype.callbacks.push(function(t){if(!1!==t.options.connectors.enabled)(t.options.pathfinder||t.series.reduce(function(t,e){return e.options&&iT(!0,e.options.connectors=e.options.connectors||{},e.options.pathfinder),t||e.options&&e.options.pathfinder},!1))&&(iT(!0,t.options.connectors=t.options.connectors||{},t.options.pathfinder),iB('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')),this.pathfinder=new o(this),this.pathfinder.update(!0)}),r.getMarkerVector=s,r.getPathfinderAnchorPoint=e,r.getRadiansToVector=i,iS(iE))}}(d||(d={}));let iI=d,iD=s.default.Point;var iR=u.n(iD);let{addEvent:iL,defined:iG,pick:iz,splat:iN}=x(),iW=Math.max,iF=Math.min;class iH{static compose(t,e){iI.compose(t,iH,e)}constructor(t){this.init(t)}init(t){this.chart=t,this.connections=[],iL(t,"redraw",function(){this.pathfinder.update()})}update(t){let e=this.chart,i=this,s=i.connections;i.connections=[],e.series.forEach(function(t){t.visible&&!t.options.isInternal&&t.points.forEach(function(t){let s,o=t.options;o&&o.dependency&&(o.connect=o.dependency);let n=t.options?.connect?iN(t.options.connect):[];t.visible&&!1!==t.isInside&&n.forEach(o=>{let n="string"==typeof o?o:o.to;n&&(s=e.get(n)),s instanceof iR()&&s.series.visible&&s.visible&&!1!==s.isInside&&i.connections.push(new S(t,s,"string"==typeof o?{}:o))})})});for(let t=0,e,o,n=s.length,r=i.connections.length;t<n;++t){o=!1;let n=s[t];for(e=0;e<r;++e){let t=i.connections[e];if((n.options&&n.options.type)===(t.options&&t.options.type)&&n.fromPoint===t.fromPoint&&n.toPoint===t.toPoint){t.graphics=n.graphics,o=!0;break}}o||n.destroy()}delete this.chartObstacles,delete this.lineObstacles,i.renderConnections(t)}renderConnections(t){t?this.chart.series.forEach(function(t){let e=function(){let e=t.chart.pathfinder;(e&&e.connections||[]).forEach(function(e){e.fromPoint&&e.fromPoint.series===t&&e.render()}),t.pathfinderRemoveRenderEvent&&(t.pathfinderRemoveRenderEvent(),delete t.pathfinderRemoveRenderEvent)};!1===t.options.animation?e():t.pathfinderRemoveRenderEvent=iL(t,"afterAnimate",e)}):this.connections.forEach(function(t){t.render()})}getChartObstacles(t){let e=this.chart.series,i=iz(t.algorithmMargin,0),s=[],o;for(let t=0,o=e.length;t<o;++t)if(e[t].visible&&!e[t].options.isInternal)for(let o=0,n=e[t].points.length,r,a;o<n;++o)(a=e[t].points[o]).visible&&(r=function(t){let e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};let i=t.graphic&&t.graphic.getBBox();return i?{xMin:t.plotX-i.width/2,xMax:t.plotX+i.width/2,yMin:t.plotY-i.height/2,yMax:t.plotY+i.height/2}:null}(a))&&s.push({xMin:r.xMin-i,xMax:r.xMax+i,yMin:r.yMin-i,yMax:r.yMax+i});return s=s.sort(function(t,e){return t.xMin-e.xMin}),iG(t.algorithmMargin)||(o=t.algorithmMargin=function(t){let e,i=t.length,s=[];for(let o=0;o<i;++o)for(let n=o+1;n<i;++n)(e=function t(e,i,s){let o=iz(s,10),n=e.yMax+o>i.yMin-o&&e.yMin-o<i.yMax+o,r=e.xMax+o>i.xMin-o&&e.xMin-o<i.xMax+o,a=n?e.xMin>i.xMax?e.xMin-i.xMax:i.xMin-e.xMax:1/0,l=r?e.yMin>i.yMax?e.yMin-i.yMax:i.yMin-e.yMax:1/0;return r&&n?o?t(e,i,Math.floor(o/2)):1/0:iF(a,l)}(t[o],t[n]))<80&&s.push(e);return s.push(80),iW(Math.floor(s.sort(function(t,e){return t-e})[Math.floor(s.length/10)]/2-1),1)}(s),s.forEach(function(t){t.xMin-=o,t.xMax+=o,t.yMin-=o,t.yMax+=o})),s}getObstacleMetrics(t){let e=0,i=0,s,o,n=t.length;for(;n--;)s=t[n].xMax-t[n].xMin,o=t[n].yMax-t[n].yMin,e<s&&(e=s),i<o&&(i=o);return{maxHeight:i,maxWidth:e}}getAlgorithmStartDirection(t){let e="left"!==t.align&&"right"!==t.align,i="top"!==t.verticalAlign&&"bottom"!==t.verticalAlign;return e?!!i&&void 0:!!i||void 0}}iH.prototype.algorithms={fastAvoid:iO,straight:function(t,e){return{path:[["M",t.x,t.y],["L",e.x,e.y]],obstacles:[{start:t,end:e}]}},simpleConnect:iw};let{addEvent:iU,defined:iY,isNumber:iX,pick:iV}=x();function i_(){let t=this.chart.options.chart;!this.horiz&&iX(this.options.staticScale)&&(!t.height||t.scrollablePlotArea&&t.scrollablePlotArea.minHeight)&&(this.staticScale=this.options.staticScale)}function ij(){if("adjustHeight"!==this.redrawTrigger){for(let t of this.axes||[]){let e=t.chart,i=!!e.initiatedScale&&e.options.animation,s=t.options.staticScale;if(t.staticScale&&iY(t.min)){let o=iV(t.brokenAxis&&t.brokenAxis.unitLength,t.max+t.tickInterval-t.min)*s,n=(o=Math.max(o,s))-e.plotHeight;!e.scrollablePixelsY&&Math.abs(n)>=1&&(e.plotHeight=o,e.redrawTrigger="adjustHeight",e.setSize(void 0,e.chartHeight+n,i)),t.series.forEach(function(t){let i=t.sharedClipKey&&e.sharedClips[t.sharedClipKey];i&&i.attr(e.inverted?{width:e.plotHeight}:{height:e.plotHeight})})}}this.initiatedScale=!0}this.redrawTrigger=null}let iq=function(t,e){let i=e.prototype;i.adjustHeight||(iU(t,"afterSetOptions",i_),i.adjustHeight=ij,iU(e,"render",i.adjustHeight))},iZ=s.default.StackItem;var i$=u.n(iZ);let{addEvent:iK,find:iJ,fireEvent:iQ,isArray:i0,isNumber:i1,pick:i2}=x();!function(t){function e(){void 0!==this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)}function i(){this.brokenAxis?.hasBreaks&&(this.options.ordinal=!1)}function s(){let t=this.brokenAxis;if(t?.hasBreaks){let e=this.tickPositions,i=this.tickPositions.info,s=[];for(let i=0;i<e.length;i++)t.isInAnyBreak(e[i])||s.push(e[i]);this.tickPositions=s,this.tickPositions.info=i}}function o(){this.brokenAxis||(this.brokenAxis=new h(this))}function n(){let{isDirty:t,options:{connectNulls:e},points:i,xAxis:s,yAxis:o}=this;if(t){let t=i.length;for(;t--;){let n=i[t],r=(null!==n.y||!1!==e)&&(s?.brokenAxis?.isInAnyBreak(n.x,!0)||o?.brokenAxis?.isInAnyBreak(n.y,!0));n.visible=!r&&!1!==n.options.visible}}}function r(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,i2(this.pointArrayMap,["y"]))}function a(t,e){let i,s,o,n=this,r=n.points;if(t?.brokenAxis?.hasBreaks){let a=t.brokenAxis;e.forEach(function(e){i=a?.breakArray||[],s=t.isXAxis?t.min:i2(n.options.threshold,t.min);let l=t?.options?.breaks?.filter(function(t){let e=!0;for(let s=0;s<i.length;s++){let o=i[s];if(o.from===t.from&&o.to===t.to){e=!1;break}}return e});r.forEach(function(n){o=i2(n["stack"+e.toUpperCase()],n[e]),i.forEach(function(e){if(i1(s)&&i1(o)){let i="";s<e.from&&o>e.to||s>e.from&&o<e.from?i="pointBreak":(s<e.from&&o>e.from&&o<e.to||s>e.from&&o>e.to&&o<e.from)&&(i="pointInBreak"),i&&iQ(t,i,{point:n,brk:e})}}),l?.forEach(function(e){iQ(t,"pointOutsideOfBreak",{point:n,brk:e})})})})}}function l(){let t=this.currentDataGrouping,e=t?.gapSize,i=this.points.slice(),s=this.yAxis,o=this.options.gapSize,n=i.length-1;if(o&&n>0){let t,r;for("value"!==this.options.gapUnit&&(o*=this.basePointRange),e&&e>o&&e>=this.basePointRange&&(o=e);n--;)if(r&&!1!==r.visible||(r=i[n+1]),t=i[n],!1!==r.visible&&!1!==t.visible){if(r.x-t.x>o){let e=(t.x+r.x)/2;i.splice(n+1,0,{isNull:!0,x:e}),s.stacking&&this.options.stacking&&((s.stacking.stacks[this.stackKey][e]=new(i$())(s,s.options.stackLabels,!1,e,this.stack)).total=0)}r=t}}return this.getGraphPath(i)}t.compose=function(t,h){if(!t.keepProps.includes("brokenAxis")){t.keepProps.push("brokenAxis"),iK(t,"init",o),iK(t,"afterInit",e),iK(t,"afterSetTickPositions",s),iK(t,"afterSetOptions",i);let d=h.prototype;d.drawBreaks=a,d.gappedPath=l,iK(h,"afterGeneratePoints",n),iK(h,"afterRender",r)}return t};class h{static isInBreak(t,e){let i,s=t.repeat||1/0,o=t.from,n=t.to-t.from,r=e>=o?(e-o)%s:s-(o-e)%s;return t.inclusive?r<=n:r<n&&0!==r}static lin2Val(t){let e=this.brokenAxis,i=e?.breakArray;if(!i||!i1(t))return t;let s=t,o,n;for(n=0;n<i.length&&!((o=i[n]).from>=s);n++)o.to<s?s+=o.len:h.isInBreak(o,s)&&(s+=o.len);return s}static val2Lin(t){let e=this.brokenAxis,i=e?.breakArray;if(!i||!i1(t))return t;let s=t,o,n;for(n=0;n<i.length;n++)if((o=i[n]).to<=t)s-=o.len;else if(o.from>=t)break;else if(h.isInBreak(o,t)){s-=t-o.from;break}return s}constructor(t){this.hasBreaks=!1,this.axis=t}findBreakAt(t,e){return iJ(e,function(e){return e.from<t&&t<e.to})}isInAnyBreak(t,e){let i=this.axis,s=i.options.breaks||[],o=s.length,n,r,a;if(o&&i1(t)){for(;o--;)h.isInBreak(s[o],t)&&(n=!0,r||(r=i2(s[o].showPoints,!i.isXAxis)));a=n&&e?n&&!r:n}return a}setBreaks(t,e){let i=this,s=i.axis,o=s.chart.time,n=i0(t)&&!!t.length&&!!Object.keys(t[0]).length;s.isDirty=i.hasBreaks!==n,i.hasBreaks=n,t?.forEach(t=>{t.from=o.parse(t.from)||0,t.to=o.parse(t.to)||0}),t!==s.options.breaks&&(s.options.breaks=s.userOptions.breaks=t),s.forceRedraw=!0,s.series.forEach(function(t){t.isDirty=!0}),n||s.val2lin!==h.val2Lin||(delete s.val2lin,delete s.lin2val),n&&(s.userOptions.ordinal=!1,s.lin2val=h.lin2Val,s.val2lin=h.val2Lin,s.setExtremes=function(t,e,o,n,r){if(i.hasBreaks){let s,o=this.options.breaks||[];for(;s=i.findBreakAt(t,o);)t=s.to;for(;s=i.findBreakAt(e,o);)e=s.from;e<t&&(e=t)}s.constructor.prototype.setExtremes.call(this,t,e,o,n,r)},s.setAxisTranslation=function(){if(s.constructor.prototype.setAxisTranslation.call(this),i.unitLength=void 0,i.hasBreaks){let t=s.options.breaks||[],e=[],o=[],n=i2(s.pointRangePadding,0),r=0,a,l,d=s.userMin||s.min,c=s.userMax||s.max,p,u;t.forEach(function(t){l=t.repeat||1/0,i1(d)&&i1(c)&&(h.isInBreak(t,d)&&(d+=t.to%l-d%l),h.isInBreak(t,c)&&(c-=c%l-t.from%l))}),t.forEach(function(t){if(p=t.from,l=t.repeat||1/0,i1(d)&&i1(c)){for(;p-l>d;)p-=l;for(;p<d;)p+=l;for(u=p;u<c;u+=l)e.push({value:u,move:"in"}),e.push({value:u+t.to-t.from,move:"out",size:t.breakSize})}}),e.sort(function(t,e){return t.value===e.value?+("in"!==t.move)-+("in"!==e.move):t.value-e.value}),a=0,p=d,e.forEach(function(t){1===(a+="in"===t.move?1:-1)&&"in"===t.move&&(p=t.value),0===a&&i1(p)&&(o.push({from:p,to:t.value,len:t.value-p-(t.size||0)}),r+=t.value-p-(t.size||0))}),i.breakArray=o,i1(d)&&i1(c)&&i1(s.min)&&(i.unitLength=c-d-r+n,iQ(s,"afterBreaks"),s.staticScale?s.transA=s.staticScale:i.unitLength&&(s.transA*=(c-s.min+n)/i.unitLength),n&&(s.minPixelPadding=s.transA*(s.minPointOffset||0)),s.min=d,s.max=c)}}),i2(e,!0)&&s.chart.redraw()}}t.Additions=h}(c||(c={}));let i3=c,{dateFormats:i5}=x(),{addEvent:i6,defined:i9,erase:i8,find:i4,isArray:i7,isNumber:st,merge:se,pick:si,timeUnits:ss,wrap:so}=x();function sn(t){return x().isObject(t,!0)}function sr(t,e){let i={width:0,height:0};if(e.forEach(function(e){let s=t[e],o=0,n=0,r;sn(s)&&(o=(r=sn(s.label)?s.label:{}).getBBox?r.getBBox().height:0,r.textStr&&!st(r.textPxLength)&&(r.textPxLength=r.getBBox().width),n=st(r.textPxLength)?Math.round(r.textPxLength):0,r.textStr&&(n=Math.round(r.getBBox().width)),i.height=Math.max(o,i.height),i.width=Math.max(n,i.width))}),"treegrid"===this.type&&this.treeGrid&&this.treeGrid.mapOfPosToGridNode){let t=this.treeGrid.mapOfPosToGridNode[-1].height||0;i.width+=this.options.labels.indentation*(t-1)}return i}function sa(t){let{grid:e}=this,i=3===this.side;if(i||t.apply(this),!e?.isColumn){let t=e?.columns||[];i&&(t=t.slice().reverse()),t.forEach(t=>{t.getOffset()})}i&&t.apply(this)}function sl(t){if(!0===(this.options.grid||{}).enabled){let{axisTitle:e,height:i,horiz:s,left:o,offset:n,opposite:r,options:a,top:l,width:h}=this,d=this.tickSize(),c=e?.getBBox().width,u=a.title.x,g=a.title.y,x=si(a.title.margin,s?5:10),f=e?this.chart.renderer.fontMetrics(e).f:0,m=(s?l+i:o)+(s?1:-1)*(r?-1:1)*(d?d[0]/2:0)+(this.side===p.bottom?f:0);t.titlePosition.x=s?o-(c||0)/2-x+u:m+(r?h:0)+n+u,t.titlePosition.y=s?m-(r?i:0)+(r?f:-f)/2+n+g:l-x+g}}function sh(){let{chart:t,options:{grid:e={}},userOptions:i}=this;if(e.enabled&&function(t){let e=t.options;e.labels.align=si(e.labels.align,"center"),t.categories||(e.showLastLabel=!1),t.labelRotation=0,e.labels.rotation=0,e.minTickInterval=1}(this),e.columns){let s=this.grid.columns=[],o=this.grid.columnIndex=0;for(;++o<e.columns.length;){let n=se(i,e.columns[o],{isInternal:!0,linkedTo:0,scrollbar:{enabled:!1}},{grid:{columns:void 0}}),r=new(_())(this.chart,n,"yAxis");r.grid.isColumn=!0,r.grid.columnIndex=o,i8(t.axes,r),i8(t[this.coll]||[],r),s.push(r)}}}function sd(){let{axisTitle:t,grid:e,options:i}=this;if(!0===(i.grid||{}).enabled){let s=this.min||0,o=this.max||0,n=this.ticks[this.tickPositions[0]];if(t&&!this.chart.styledMode&&n?.slotWidth&&!this.options.title.style.width&&t.css({width:`${n.slotWidth}px`}),this.maxLabelDimensions=this.getMaxLabelDimensions(this.ticks,this.tickPositions),this.rightWall&&this.rightWall.destroy(),this.grid?.isOuterAxis()&&this.axisLine){let t=i.lineWidth;if(t){let e=this.getLinePath(t),n=e[0],r=e[1],a=(this.tickSize("tick")||[1])[0]*(this.side===p.top||this.side===p.left?-1:1);if("M"===n[0]&&"L"===r[0]&&(this.horiz?(n[2]+=a,r[2]+=a):(n[1]+=a,r[1]+=a)),!this.horiz&&this.chart.marginRight){let t=["L",this.left,n[2]||0],e=[n,t],a=["L",this.chart.chartWidth-this.chart.marginRight,this.toPixels(o+this.tickmarkOffset)],l=[["M",r[1]||0,this.toPixels(o+this.tickmarkOffset)],a];this.grid.upperBorder||s%1==0||(this.grid.upperBorder=this.grid.renderBorder(e)),this.grid.upperBorder&&(this.grid.upperBorder.attr({stroke:i.lineColor,"stroke-width":i.lineWidth}),this.grid.upperBorder.animate({d:e})),this.grid.lowerBorder||o%1==0||(this.grid.lowerBorder=this.grid.renderBorder(l)),this.grid.lowerBorder&&(this.grid.lowerBorder.attr({stroke:i.lineColor,"stroke-width":i.lineWidth}),this.grid.lowerBorder.animate({d:l}))}this.grid.axisLineExtra?(this.grid.axisLineExtra.attr({stroke:i.lineColor,"stroke-width":i.lineWidth}),this.grid.axisLineExtra.animate({d:e})):this.grid.axisLineExtra=this.grid.renderBorder(e),this.axisLine[this.showAxis?"show":"hide"]()}}if((e?.columns||[]).forEach(t=>t.render()),!this.horiz&&this.chart.hasRendered&&(this.scrollbar||this.linkedParent?.scrollbar)&&this.tickPositions.length){let t,e,i=this.tickmarkOffset,n=this.tickPositions[this.tickPositions.length-1],r=this.tickPositions[0];for(;(t=this.hiddenLabels.pop())&&t.element;)t.show();for(;(e=this.hiddenMarks.pop())&&e.element;)e.show();(t=this.ticks[r].label)&&(s-r>i?this.hiddenLabels.push(t.hide()):t.show()),(t=this.ticks[n].label)&&(n-o>i?this.hiddenLabels.push(t.hide()):t.show());let a=this.ticks[n].mark;a&&n-o<i&&n-o>0&&this.ticks[n].isLast&&this.hiddenMarks.push(a.hide())}}}function sc(){let t=this.tickPositions?.info,e=this.options,i=e.grid||{},s=this.userOptions.labels||{};i.enabled&&(this.horiz?(this.series.forEach(t=>{t.options.pointRange=0}),t&&e.dateTimeLabelFormats&&e.labels&&!i9(s.align)&&(!1===e.dateTimeLabelFormats[t.unitName].range||t.count>1)&&(e.labels.align="left",i9(s.x)||(e.labels.x=3))):"treegrid"!==this.type&&this.grid&&this.grid.columns&&(this.minPointOffset=this.tickInterval))}function sp(t){let e,i=this.options,s=t.userOptions,o=i&&sn(i.grid)?i.grid:{};!0===o.enabled&&(e=se(!0,{className:"highcharts-grid-axis "+(s.className||""),dateTimeLabelFormats:{hour:{list:["%[HM]","%[H]"]},day:{list:["%[AeB]","%[aeb]","%[E]"]},week:{list:["Week %W","W%W"]},month:{list:["%[B]","%[b]","%o"]}},grid:{borderWidth:1},labels:{padding:2,style:{fontSize:"0.9em"}},margin:0,title:{text:null,reserveSpace:!1,rotation:0,style:{textOverflow:"ellipsis"}},units:[["millisecond",[1,10,100]],["second",[1,10]],["minute",[1,5,15]],["hour",[1,6]],["day",[1]],["week",[1]],["month",[1]],["year",null]]},s),"xAxis"!==this.coll||(i9(s.linkedTo)&&!i9(s.tickPixelInterval)&&(e.tickPixelInterval=350),!(!i9(s.tickPixelInterval)&&i9(s.linkedTo))||i9(s.tickPositioner)||i9(s.tickInterval)||i9(s.units)||(e.tickPositioner=function(t,i){let s=this.linkedParent?.tickPositions?.info;if(s){let o=e.units||[],n,r=1,a="year";for(let t=0;t<o.length;t++){let e=o[t];if(e&&e[0]===s.unitName){n=t;break}}let l=st(n)&&o[n+1];if(l){a=l[0]||"year";let t=l[1];r=t?.[0]||1}else"year"===s.unitName&&(r=10*s.count);let h=ss[a];return this.tickInterval=h*r,this.chart.time.getTimeTicks({unitRange:h,count:r,unitName:a},t,i,this.options.startOfWeek)}})),se(!0,this.options,e),this.horiz&&(i.minPadding=si(s.minPadding,0),i.maxPadding=si(s.maxPadding,0)),st(i.grid.borderWidth)&&(i.tickWidth=i.lineWidth=o.borderWidth))}function su(t){let e=t.userOptions,i=e?.grid||{},s=i.columns;i.enabled&&s&&se(!0,this.options,s[0])}function sg(){(this.grid.columns||[]).forEach(t=>t.setScale())}function sx(t){let{horiz:e,maxLabelDimensions:i,options:{grid:s={}}}=this;if(s.enabled&&i){let o=2*this.options.labels.distance,n=e?s.cellHeight||o+i.height:o+i.width;i7(t.tickSize)?t.tickSize[0]=n:t.tickSize=[n,0]}}function sf(){this.axes.forEach(t=>{(t.grid?.columns||[]).forEach(t=>{t.setAxisSize(),t.setAxisTranslation()})})}function sm(t){let{grid:e}=this;(e.columns||[]).forEach(e=>e.destroy(t.keepEvents)),e.columns=void 0}function sb(t){let e=t.userOptions||{},i=e.grid||{};i.enabled&&i9(i.borderColor)&&(e.tickColor=e.lineColor=i.borderColor),this.grid||(this.grid=new sA(this)),this.hiddenLabels=[],this.hiddenMarks=[]}function sy(t){let e=this.label,i=this.axis,s=i.reversed,o=i.chart,n=i.options.grid||{},r=i.options.labels,a=r.align,l=p[i.side],h=t.tickmarkOffset,d=i.tickPositions,c=this.pos-h,u=st(d[t.index+1])?d[t.index+1]-h:(i.max||0)+h,g=i.tickSize("tick"),x=g?g[0]:0,f=g?g[1]/2:0;if(!0===n.enabled){let n,h,d,p;if("top"===l?h=(n=i.top+i.offset)-x:"bottom"===l?n=(h=o.chartHeight-i.bottom+i.offset)+x:(n=i.top+i.len-(i.translate(s?u:c)||0),h=i.top+i.len-(i.translate(s?c:u)||0)),"right"===l?p=(d=o.chartWidth-i.right+i.offset)+x:"left"===l?d=(p=i.left+i.offset)-x:(d=Math.round(i.left+(i.translate(s?u:c)||0))-f,p=Math.min(Math.round(i.left+(i.translate(s?c:u)||0))-f,i.left+i.len)),this.slotWidth=p-d,t.pos.x="left"===a?d:"right"===a?p:d+(p-d)/2,t.pos.y=h+(n-h)/2,e){let i=o.renderer.fontMetrics(e),s=e.getBBox().height;if(r.useHTML)t.pos.y+=i.b+-(s/2);else{let e=Math.round(s/i.h);t.pos.y+=(i.b-(i.h-i.f))/2+-((e-1)*i.h/2)}}t.pos.x+=i.horiz&&r.x||0}}function sv(t){let{axis:e,value:i}=t;if(e.options.grid?.enabled){let s,o=e.tickPositions,n=(e.linkedParent||e).series[0],r=i===o[0],a=i===o[o.length-1],l=n&&i4(n.options.data,function(t){return t[e.isXAxis?"x":"y"]===i});l&&n.is("gantt")&&(s=se(l),x().seriesTypes.gantt.prototype.pointClass.setGanttPointAliases(s,e.chart)),t.isFirst=r,t.isLast=a,t.point=s}}function sM(){let t=this.options,e=t.grid||{},i=this.categories,s=this.tickPositions,o=s[0],n=s[1],r=s[s.length-1],a=s[s.length-2],l=this.linkedParent?.min,h=this.linkedParent?.max,d=l||this.min,c=h||this.max,p=this.tickInterval,u=st(d)&&d>=o+p&&d<n,g=st(d)&&o<d&&o+p>d,x=st(c)&&r>c&&r-p<c,f=st(c)&&c<=r-p&&c>a;!0===e.enabled&&!i&&(this.isXAxis||this.isLinked)&&((g||u)&&!t.startOnTick&&(s[0]=d),(x||f)&&!t.endOnTick&&(s[s.length-1]=c))}function sk(t){var e;let{options:{grid:i={}}}=this;return!0===i.enabled&&this.categories?this.tickInterval:t.apply(this,(e=arguments,Array.prototype.slice.call(e,1)))}(r=p||(p={}))[r.top=0]="top",r[r.right=1]="right",r[r.bottom=2]="bottom",r[r.left=3]="left";class sA{constructor(t){this.axis=t}isOuterAxis(){let t=this.axis,e=t.chart,i=t.grid.columnIndex,s=t.linkedParent?.grid.columns||t.grid.columns||[],o=i?t.linkedParent:t,n=-1,r=0;return 3===t.side&&!e.inverted&&s.length?!t.linkedParent:((e[t.coll]||[]).forEach((e,i)=>{e.side!==t.side||e.options.isInternal||(r=i,e!==o||(n=i))}),r===n&&(!st(i)||s.length===i))}renderBorder(t){let e=this.axis,i=e.chart.renderer,s=e.options,o=i.path(t).addClass("highcharts-axis-line").add(e.axisGroup);return i.styledMode||o.attr({stroke:s.lineColor,"stroke-width":s.lineWidth,zIndex:7}),o}}i5.E=function(t){return this.dateFormat("%a",t,!0).charAt(0)},i5.W=function(t){let e=this.toParts(t),i=(e[7]+6)%7,s=e.slice(0);s[2]=e[2]-i+3;let o=this.toParts(this.makeTime(s[0],0,1));return 4!==o[7]&&(e[1]=0,e[2]=1+(11-o[7])%7),(1+Math.floor((this.makeTime(s[0],s[1],s[2])-this.makeTime(o[0],o[1],o[2]))/6048e5)).toString()};let{extend:sw,isNumber:sO,pick:sE}=x();function sS(t,e,i,s,o,n){let r=n&&n.after,a=n&&n.before,l={data:s,depth:i-1,id:t,level:i,parent:e||""},h=0,d=0,c,p;"function"==typeof a&&a(l,n);let u=(o[t]||[]).map(e=>{let s=sS(e.id,t,i+1,e,o,n),r=e.start||NaN,a=!0===e.milestone?r:e.end||NaN;return c=!sO(c)||r<c?r:c,p=!sO(p)||a>p?a:p,h=h+1+s.descendants,d=Math.max(s.height+1,d),s});return s&&(s.start=sE(s.start,c),s.end=sE(s.end,p)),sw(l,{children:u,descendants:h,height:d}),"function"==typeof r&&r(l,n),l}let sP={getNode:sS,getTree:function(t,e){return sS("",null,1,null,function(t){let e=[],i=t.reduce((t,i)=>{let{parent:s="",id:o}=i;return void 0===t[s]&&(t[s]=[]),t[s].push(i),o&&e.push(o),t},{});return Object.keys(i).forEach(t=>{if(""!==t&&-1===e.indexOf(t)){let e=i[t].map(function(t){let{...e}=t;return e});i[""].push(...e),delete i[t]}}),i}(t),e)}},{addEvent:sB,removeEvent:sT,isObject:sC,isNumber:sI,pick:sD,wrap:sR}=x();function sL(){this.treeGrid||(this.treeGrid=new sN(this))}function sG(t,e,i,s,o,n,r,a,l){let h,d,c,p=sD(this.options?.labels,n),u=this.pos,g=this.axis,x="treegrid"===g.type,f=t.apply(this,[e,i,s,o,p,r,a,l]);if(x){let{width:t=0,padding:e=5*!g.linkedParent}=p&&sC(p.symbol,!0)?p.symbol:{},i=p&&sI(p.indentation)?p.indentation:0;h=g.treeGrid.mapOfPosToGridNode,d=h?.[u],c=d?.depth||1,f.x+=t+2*e+(c-1)*i}return f}function sz(t){let e,{pos:i,axis:s,label:o,treeGrid:n,options:r}=this,a=n?.labelIcon,l=o?.element,{treeGrid:h,options:d,chart:c,tickPositions:p}=s,u=h.mapOfPosToGridNode,g=sD(r?.labels,d?.labels),x=g&&sC(g.symbol,!0)?g.symbol:{},f=u?.[i],{descendants:m,depth:b}=f||{},y=f&&m&&m>0,v="treegrid"===s.type&&l,M=p.indexOf(i)>-1,k="highcharts-treegrid-node-",A=k+"level-",w=c.styledMode;(v&&f&&o.removeClass(RegExp(A+".*")).addClass(A+b),t.apply(this,Array.prototype.slice.call(arguments,1)),v&&y)?(e=h.isCollapsed(f),function(t,e){let i=t.treeGrid,s=!i.labelIcon,o=e.renderer,n=e.xy,r=e.options,a=r.width||0,l=r.height||0,h=r.padding??t.axis.linkedParent?0:5,d={x:n.x-a/2-h,y:n.y-l/2},c=e.collapsed?90:180,p=e.show&&sI(d.y),u=i.labelIcon;u||(i.labelIcon=u=o.path(o.symbols[r.type](r.x||0,r.y||0,a,l)).addClass("highcharts-label-icon").add(e.group)),u[p?"show":"hide"](),o.styledMode||u.attr({cursor:"pointer",fill:sD(e.color,"#666666"),"stroke-width":1,stroke:r.lineColor,strokeWidth:r.lineWidth||0}),u[s?"attr":"animate"]({translateX:d.x,translateY:d.y,rotation:c})}(this,{color:!w&&o.styles.color||"",collapsed:e,group:o.parentGroup,options:x,renderer:o.renderer,show:M,xy:o.xy}),o.addClass(k+(e?"collapsed":"expanded")).removeClass(k+(e?"expanded":"collapsed")),w||o.css({cursor:"pointer"}),[o,a].forEach(t=>{t&&!t.attachedTreeGridEvents&&(sB(t.element,"mouseover",function(){o.addClass("highcharts-treegrid-node-active"),o.renderer.styledMode||o.css({textDecoration:"underline"})}),sB(t.element,"mouseout",function(){!function(t,e){let i=sC(e.style)?e.style:{};t.removeClass("highcharts-treegrid-node-active"),t.renderer.styledMode||t.css({textDecoration:i.textDecoration||"none"})}(o,g)}),sB(t.element,"click",function(){n.toggleCollapse()}),t.attachedTreeGridEvents=!0)})):a&&(sT(l),o?.css({cursor:"default"}),a.destroy())}class sN{static compose(t){let e=t.prototype;e.toggleCollapse||(sB(t,"init",sL),sR(e,"getLabelPosition",sG),sR(e,"renderLabel",sz),e.collapse=function(t){this.treeGrid.collapse(t)},e.expand=function(t){this.treeGrid.expand(t)},e.toggleCollapse=function(t){this.treeGrid.toggleCollapse(t)})}constructor(t){this.tick=t}collapse(t){let e=this.tick,i=e.axis,s=i.brokenAxis;if(s&&i.treeGrid.mapOfPosToGridNode){let o=e.pos,n=i.treeGrid.mapOfPosToGridNode[o],r=i.treeGrid.collapse(n);s.setBreaks(r,sD(t,!0))}}destroy(){this.labelIcon&&this.labelIcon.destroy()}expand(t){let{pos:e,axis:i}=this.tick,{treeGrid:s,brokenAxis:o}=i,n=s.mapOfPosToGridNode;if(o&&n){let i=n[e],r=s.expand(i);o.setBreaks(r,sD(t,!0))}}toggleCollapse(t){let e=this.tick,i=e.axis,s=i.brokenAxis;if(s&&i.treeGrid.mapOfPosToGridNode){let o=e.pos,n=i.treeGrid.mapOfPosToGridNode[o],r=i.treeGrid.toggleCollapse(n);s.setBreaks(r,sD(t,!0))}}}let{extend:sW,isArray:sF,isNumber:sH,isObject:sU,merge:sY,pick:sX,relativeLength:sV}=x(),{getLevelOptions:s_}={getColor:function(t,e){let i,s,o,n,r,a,l=e.index,h=e.mapOptionsToLevel,d=e.parentColor,c=e.parentColorIndex,p=e.series,u=e.colors,g=e.siblings,x=p.points,f=p.chart.options.chart;return t&&(i=x[t.i],s=h[t.level]||{},i&&s.colorByPoint&&(n=i.index%(u?u.length:f.colorCount),o=u&&u[n]),p.chart.styledMode||(r=sX(i&&i.options.color,s&&s.color,o,d&&(t=>{let e=s&&s.colorVariation;return e&&"brightness"===e.key&&l&&g?tf().parse(t).brighten(e.to*(l/g)).get():t})(d),p.color)),a=sX(i&&i.options.colorIndex,s&&s.colorIndex,n,c,e.colorIndex)),{color:r,colorIndex:a}},getLevelOptions:function(t){let e,i,s,o,n,r,a={};if(sU(t))for(o=sH(t.from)?t.from:1,r=t.levels,i={},e=sU(t.defaults)?t.defaults:{},sF(r)&&(i=r.reduce((t,i)=>{let s,n,r;return sU(i)&&sH(i.level)&&(n=sX((r=sY({},i)).levelIsConstant,e.levelIsConstant),delete r.levelIsConstant,delete r.level,sU(t[s=i.level+(n?0:o-1)])?sY(!0,t[s],r):t[s]=r),t},{})),n=sH(t.to)?t.to:1,s=0;s<=n;s++)a[s]=sY({},e,sU(i[s])?i[s]:{});return a},getNodeWidth:function(t,e){let{chart:i,options:s}=t,{nodeDistance:o=0,nodeWidth:n=0}=s,{plotSizeX:r=1}=i;if("auto"===n){if("string"==typeof o&&/%$/.test(o))return r/(e+parseFloat(o)/100*(e-1));let t=Number(o);return(r+t)/(e||1)-t}return sV(n,r)},setTreeValues:function t(e,i){let s=i.before,o=i.idRoot,n=i.mapIdToNode[o],r=!1!==i.levelIsConstant,a=i.points[e.i],l=a&&a.options||{},h=[],d=0;e.levelDynamic=e.level-(r?0:n.level),e.name=sX(a&&a.name,""),e.visible=o===e.id||!0===i.visible,"function"==typeof s&&(e=s(e,i)),e.children.forEach((s,o)=>{let n=sW({},i);sW(n,{index:o,siblings:e.children.length,visible:e.visible}),s=t(s,n),h.push(s),s.visible&&(d+=s.val)});let c=sX(l.value,d);return e.visible=c>=0&&(d>0||e.visible),e.children=h,e.childrenTotal=d,e.isLeaf=e.visible&&!d,e.val=c,e},updateRootId:function(t){let e,i;return sU(t)&&(i=sU(t.options)?t.options:{},e=sX(t.rootNode,i.rootId,""),sU(t.userOptions)&&(t.userOptions.rootId=e),t.rootNode=e),e}},{addEvent:sj,isArray:sq,splat:sZ,find:s$,fireEvent:sK,isObject:sJ,isString:sQ,merge:s0,pick:s1,removeEvent:s2,wrap:s3}=x();function s5(t,e){let i=t.collapseEnd||0,s=t.collapseStart||0;return i>=e&&(s-=.5),{from:s,to:i,showPoints:!1}}function s6(t,e,i){let s=[],o=[],n={},r=e||!1,a={},l=-1,h=sP.getTree(t,{after:function(t){let e=a[t.pos],i=0,s=0;e.children.forEach(function(t){s+=(t.descendants||0)+1,i=Math.max((t.height||0)+1,i)}),e.descendants=s,e.height=i,e.collapsed&&o.push(e)},before:function(t){let e,i,o=sJ(t.data,!0)?t.data:{},h=sQ(o.name)?o.name:"",d=n[t.parent],c=sJ(d,!0)?a[d.pos]:null;r&&sJ(c,!0)&&(e=s$(c.children,function(t){return t.name===h}))?(i=e.pos,e.nodes.push(t)):i=l++,!a[i]&&(a[i]=e={depth:c?c.depth+1:0,name:h,id:o.id,nodes:[t],children:[],pos:i},-1!==i&&s.push(h),sJ(c,!0)&&c.children.push(e)),sQ(t.id)&&(n[t.id]=t),e&&!0===o.collapsed&&(e.collapsed=!0),t.pos=i}});return{categories:s,mapOfIdToNode:n,mapOfPosToGridNode:a=function(t,e){let i=function(t,s,o){let n=t.nodes,r=s+(-1===s?0:e-1),a=(r-s)/2,l=s+a;return n.forEach(function(t){let e=t.data;sJ(e,!0)&&(e.y=s+(e.seriesIndex||0),delete e.seriesIndex),t.pos=l}),o[l]=t,t.pos=l,t.tickmarkOffset=a+.5,t.collapseStart=r+.5,t.children.forEach(function(t){i(t,r+1,o),r=(t.collapseEnd||0)-.5}),t.collapseEnd=r+.5,o};return i(t["-1"],-1,{})}(a,i),collapsedNodes:o,tree:h}}function s9(t){let e=t.target;e.axes.filter(t=>"treegrid"===t.type).forEach(function(i){let s=i.options||{},o=s.labels,n=i.uniqueNames,r=e.time.parse(s.max),a=!i.treeGrid.mapOfPosToGridNode||i.series.some(function(t){return!t.hasRendered||t.isDirtyData||t.isDirty}),l=0,h,d;if(a){let s=[];if(h=i.series.reduce(function(t,i){let o=i.options.data||[],r=o[0],a=Array.isArray(r)&&!r.find(t=>"object"==typeof t);return s.push(a),i.visible&&(o.forEach(function(s){(a||i.options.keys?.length)&&(s=i.pointClass.prototype.optionsToObject.call({series:i},s),i.pointClass.setGanttPointAliases(s,e)),sJ(s,!0)&&(s.seriesIndex=l,t.push(s))}),!0===n&&l++),t},[]),r&&h.length<r)for(let t=h.length;t<=r;t++)h.push({name:t+"​"});i.categories=(d=s6(h,n||!1,!0===n?l:1)).categories,i.treeGrid.mapOfPosToGridNode=d.mapOfPosToGridNode,i.hasNames=!0,i.treeGrid.tree=d.tree,i.series.forEach(function(t,e){let i=(t.options.data||[]).map(function(i){return(s[e]||sq(i)&&t.options.keys&&t.options.keys.length)&&h.forEach(function(t){let e=sZ(i);e.indexOf(t.x||0)>=0&&e.indexOf(t.x2||0)>=0&&(i=t)}),sJ(i,!0)?s0(i):i});t.visible&&t.setData(i,!1)}),i.treeGrid.mapOptionsToLevel=s_({defaults:o,from:1,levels:o?.levels,to:i.treeGrid.tree?.height}),"beforeRender"===t.type&&(i.treeGrid.collapsedNodes=d.collapsedNodes)}})}function s8(t,e){let s=this.treeGrid.mapOptionsToLevel||{},o="treegrid"===this.type,n=this.ticks,r=n[e],a,l,h;o&&this.treeGrid.mapOfPosToGridNode?((a=s[(h=this.treeGrid.mapOfPosToGridNode[e]).depth])&&(l={labels:a}),!r&&i?n[e]=r=new i(this,e,void 0,void 0,{category:h.name,tickmarkOffset:h.tickmarkOffset,options:l}):(r.parameters.category=h.name,r.options=l,r.addLabel())):t.apply(this,Array.prototype.slice.call(arguments,1))}function s4(t,e,i,s){let o=this,n="treegrid"===i.type;o.treeGrid||(o.treeGrid=new oe(o)),n&&(sj(e,"beforeRender",s9),sj(e,"beforeRedraw",s9),sj(e,"addSeries",function(t){if(t.options.data){let e=s6(t.options.data,i.uniqueNames||!1,1);o.treeGrid.collapsedNodes=(o.treeGrid.collapsedNodes||[]).concat(e.collapsedNodes)}}),sj(o,"foundExtremes",function(){o.treeGrid.collapsedNodes&&o.treeGrid.collapsedNodes.forEach(function(t){let e=o.treeGrid.collapse(t);o.brokenAxis&&(o.brokenAxis.setBreaks(e,!1),o.treeGrid.collapsedNodes&&(o.treeGrid.collapsedNodes=o.treeGrid.collapsedNodes.filter(e=>t.collapseStart!==e.collapseStart||t.collapseEnd!==e.collapseEnd)))})}),sj(o,"afterBreaks",function(){"yAxis"===o.coll&&!o.staticScale&&o.chart.options.chart.height&&(o.isDirty=!0)}),i=s0({grid:{enabled:!0},labels:{align:"left",levels:[{level:void 0},{level:1,style:{fontWeight:"bold"}}],symbol:{type:"triangle",x:-5,y:-5,height:10,width:10}},uniqueNames:!1},i,{reversed:!0})),t.apply(o,[e,i,s]),n&&(o.hasNames=!0,o.options.showLastLabel=!0)}function s7(t){let e=this.options,i=this.chart.time,s="number"==typeof e.linkedTo?this.chart[this.coll]?.[e.linkedTo]:void 0;if("treegrid"===this.type){if(this.min=this.userMin??i.parse(e.min)??this.dataMin,this.max=this.userMax??i.parse(e.max)??this.dataMax,sK(this,"foundExtremes"),this.setAxisTranslation(),this.tickInterval=1,this.tickmarkOffset=.5,this.tickPositions=this.treeGrid.mapOfPosToGridNode?this.treeGrid.getTickPositions():[],s){let t=s.getExtremes();this.min=s1(t.min,t.dataMin),this.max=s1(t.max,t.dataMax),this.tickPositions=s.tickPositions}this.linkedParent=s}else t.apply(this,Array.prototype.slice.call(arguments,1))}function ot(t){let e=this;"treegrid"===this.type&&e.visible&&e.tickPositions.forEach(function(t){let i=e.ticks[t];i.label?.attachedTreeGridEvents&&(s2(i.label.element),i.label.attachedTreeGridEvents=!1)}),t.apply(e,Array.prototype.slice.call(arguments,1))}class oe{static compose(t,e,s,o){var n,r,a;if(!t.keepProps.includes("treeGrid")){let e=t.prototype;t.keepProps.push("treeGrid"),s3(e,"generateTick",s8),s3(e,"init",s4),s3(e,"setTickInterval",s7),s3(e,"redraw",ot),e.utils={getNode:sP.getNode},i||(i=o)}return n=t,r=e,a=o,n.keepProps.includes("grid")||(n.keepProps.push("grid"),n.prototype.getMaxLabelDimensions=sr,so(n.prototype,"unsquish",sk),so(n.prototype,"getOffset",sa),i6(n,"init",sb),i6(n,"afterGetTitlePosition",sl),i6(n,"afterInit",sh),i6(n,"afterRender",sd),i6(n,"afterSetAxisTranslation",sc),i6(n,"afterSetOptions",sp),i6(n,"afterSetOptions",su),i6(n,"afterSetScale",sg),i6(n,"afterTickSize",sx),i6(n,"trimTicks",sM),i6(n,"destroy",sm),i6(r,"afterSetChartSize",sf),i6(a,"afterGetLabelPosition",sy),i6(a,"labelFormat",sv)),i3.compose(t,s),sN.compose(o),t}constructor(t){this.axis=t}setCollapsedStatus(t){let e=this.axis,i=e.chart;e.series.forEach(function(e){let s=e.options.data;if(t.id&&s){let o=i.get(t.id),n=s[e.data.indexOf(o)];o&&n&&(o.collapsed=t.collapsed,n.collapsed=t.collapsed)}})}collapse(t){let e=this.axis,i=e.options.breaks||[],s=s5(t,e.max);return i.push(s),t.collapsed=!0,e.treeGrid.setCollapsedStatus(t),i}expand(t){let e=this.axis,i=e.options.breaks||[],s=s5(t,e.max);return t.collapsed=!1,e.treeGrid.setCollapsedStatus(t),i.reduce(function(t,e){return(e.to!==s.to||e.from!==s.from)&&t.push(e),t},[])}getTickPositions(){let t=this.axis,e=Math.floor(t.min/t.tickInterval)*t.tickInterval,i=Math.ceil(t.max/t.tickInterval)*t.tickInterval;return Object.keys(t.treeGrid.mapOfPosToGridNode||{}).reduce(function(s,o){let n=+o;return n>=e&&n<=i&&!t.brokenAxis?.isInAnyBreak(n)&&s.push(n),s},[])}isCollapsed(t){let e=this.axis,i=e.options.breaks||[],s=s5(t,e.max);return i.some(function(t){return t.from===s.from&&t.to===s.to})}toggleCollapse(t){return this.isCollapsed(t)?this.expand(t):this.collapse(t)}}let{series:oi,seriesTypes:{xrange:os}}=tb(),{extend:oo,isNumber:on,merge:or}=x();class oa extends os{static compose(t,e,i,s){if(os.compose(t),e&&(iq(t,e),i))iH.compose(e,i.prototype.pointClass),s&&oe.compose(t,e,i,s)}drawPoint(t,e){let i=this.options,s=this.chart.renderer,o=t.shapeArgs,n=t.plotY,r=t.selected&&"select",a=i.stacking&&!i.borderRadius,l=t.graphic,h;t.options.milestone?on(n)&&null!==t.y&&!1!==t.visible?(h=s.symbols.diamond(o.x||0,o.y||0,o.width||0,o.height||0),l?l[e]({d:h}):t.graphic=l=s.path(h).addClass(t.getClassName(),!0).add(t.group||this.group),this.chart.styledMode||t.graphic.attr(this.pointAttribs(t,r)).shadow(i.shadow,null,a)):l&&(t.graphic=l.destroy()):super.drawPoint(t,e)}translatePoint(t){let e,i;super.translatePoint(t),t.options.milestone&&(i=(e=t.shapeArgs).height||0,t.shapeArgs={x:(e.x||0)-i/2,y:e.y,width:i,height:i})}}oa.defaultOptions=or(os.defaultOptions,{grouping:!1,dataLabels:{enabled:!0},tooltip:{headerFormat:'<span style="font-size: 0.8em">{series.name}</span><br/>',pointFormat:null,pointFormatter:function(){let t=this.series,e=t.xAxis,i=t.tooltipOptions.dateTimeLabelFormats,s=e.options.startOfWeek,o=t.tooltipOptions,n=this.options.milestone,r=o.xDateFormat,a="<b>"+(this.name||this.yCategory)+"</b>";if(o.pointFormat)return this.tooltipFormatter(o.pointFormat);!r&&iu(this.start)&&(r=t.chart.time.getDateFormat(e.closestPointRange,this.start,s,i||{}));let l=t.chart.time.dateFormat(r,this.start),h=t.chart.time.dateFormat(r,this.end);return a+="<br/>",n?a+=l+"<br/>":a+="Start: "+l+"<br/>"+("End: "+h)+"<br/>",a}},connectors:{type:"simpleConnect",animation:{reversed:!0},radius:0,startMarker:{enabled:!0,symbol:"arrow-filled",radius:4,fill:"#fa0",align:"left"},endMarker:{enabled:!1,align:"right"}}}),oo(oa.prototype,{pointArrayMap:["start","end","y"],pointClass:ip,setData:oi.prototype.setData}),tb().registerSeriesType("gantt",oa);let ol=x();ol.Connection=ol.Connection||S,ol.GanttChart=ol.GanttChart||X,ol.Navigator=ol.Navigator||eg,ol.RangeSelector=ol.RangeSelector||id,ol.Scrollbar=ol.Scrollbar||t2,ol.ganttChart=ol.GanttChart.ganttChart,({compose:function(t){let e=t.prototype.symbols;e.arrow=f,e["arrow-filled"]=b,e["arrow-filled-half"]=y,e["arrow-half"]=m,e["triangle-left"]=b,e["triangle-left-half"]=y}}).compose(ol.SVGRenderer),({compose:function(t,e){C(P,"CurrentDateIndication")&&(B(t,"afterSetOptions",R),B(e,"render",L),I(e.prototype,"getLabelText",G))}}).compose(ol.Axis,ol.PlotLineOrBand),oa.compose(ol.Axis,ol.Chart,ol.Series,ol.Tick),ol.Navigator.compose(ol.Chart,ol.Axis,ol.Series),ol.RangeSelector.compose(ol.Axis,ol.Chart),ol.Scrollbar.compose(ol.Axis);let oh=x();export{oh as default};