{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/data-tools\n * @requires highcharts\n *\n * Highcharts\n *\n * (c) 2010-2025 Highsoft AS\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Data/Modifiers/DataModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *\n * */\n\n\nconst { addEvent, fireEvent, merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Abstract class to provide an interface for modifying a table.\n *\n */\nclass DataModifier {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Runs a timed execution of the modifier on the given datatable.\n     * Can be configured to run multiple times.\n     *\n     * @param {DataTable} dataTable\n     * The datatable to execute\n     *\n     * @param {DataModifier.BenchmarkOptions} options\n     * Options. Currently supports `iterations` for number of iterations.\n     *\n     * @return {Array<number>}\n     * An array of times in milliseconds\n     *\n     */\n    benchmark(dataTable, options) {\n        const results = [];\n        const modifier = this;\n        const execute = () => {\n            modifier.modifyTable(dataTable);\n            modifier.emit({\n                type: 'afterBenchmarkIteration'\n            });\n        };\n        const defaultOptions = {\n            iterations: 1\n        };\n        const { iterations } = merge(defaultOptions, options);\n        modifier.on('afterBenchmarkIteration', () => {\n            if (results.length === iterations) {\n                modifier.emit({\n                    type: 'afterBenchmark',\n                    results\n                });\n                return;\n            }\n            // Run again\n            execute();\n        });\n        const times = {\n            startTime: 0,\n            endTime: 0\n        };\n        // Add timers\n        modifier.on('modify', () => {\n            times.startTime = window.performance.now();\n        });\n        modifier.on('afterModify', () => {\n            times.endTime = window.performance.now();\n            results.push(times.endTime - times.startTime);\n        });\n        // Initial run\n        execute();\n        return results;\n    }\n    /**\n     * Emits an event on the modifier to all registered callbacks of this event.\n     *\n     * @param {DataModifier.Event} [e]\n     * Event object containing additonal event information.\n     */\n    emit(e) {\n        fireEvent(this, e.type, e);\n    }\n    /**\n     * Returns a modified copy of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<Highcharts.DataTable>}\n     * Table with `modified` property as a reference.\n     */\n    modify(table, eventDetail) {\n        const modifier = this;\n        return new Promise((resolve, reject) => {\n            if (table.modified === table) {\n                table.modified = table.clone(false, eventDetail);\n            }\n            try {\n                resolve(modifier.modifyTable(table, eventDetail));\n            }\n            catch (e) {\n                modifier.emit({\n                    type: 'error',\n                    detail: eventDetail,\n                    table\n                });\n                reject(e);\n            }\n        });\n    }\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyCell(table, \n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    columnName, rowIndex, cellValue, eventDetail\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        return this.modifyTable(table);\n    }\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyColumns(table, \n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    columns, rowIndex, eventDetail\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        return this.modifyTable(table);\n    }\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyRows(table, \n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    rows, rowIndex, eventDetail\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        return this.modifyTable(table);\n    }\n    /**\n     * Registers a callback for a specific modifier event.\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {DataEventEmitter.Callback} callback\n     * Function to register for an modifier callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the modifier event.\n     */\n    on(type, callback) {\n        return addEvent(this, type, callback);\n    }\n}\n/* *\n *\n *  Class Namespace\n *\n * */\n/**\n * Additionally provided types for modifier events and options.\n */\n(function (DataModifier) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Registry as a record object with modifier names and their class\n     * constructor.\n     */\n    DataModifier.types = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a modifier class to the registry. The modifier class has to provide\n     * the `DataModifier.options` property and the `DataModifier.modifyTable`\n     * method to modify the table.\n     *\n     * @private\n     *\n     * @param {string} key\n     * Registry key of the modifier class.\n     *\n     * @param {DataModifierType} DataModifierClass\n     * Modifier class (aka class constructor) to register.\n     *\n     * @return {boolean}\n     * Returns true, if the registration was successful. False is returned, if\n     * their is already a modifier registered with this key.\n     */\n    function registerType(key, DataModifierClass) {\n        return (!!key &&\n            !DataModifier.types[key] &&\n            !!(DataModifier.types[key] = DataModifierClass));\n    }\n    DataModifier.registerType = registerType;\n})(DataModifier || (DataModifier = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Modifiers_DataModifier = (DataModifier);\n\n;// ./code/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items = []) {\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice(start, deleteCount, ...items),\n                array: column\n            };\n        }\n        const Constructor = Object.getPrototypeOf(column)\n            .constructor;\n        const removed = column[removedAsSubarray ? 'subarray' : 'slice'](start, start + deleteCount);\n        const newLength = column.length - deleteCount + items.length;\n        const result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nconst { setLength, splice } = Data_ColumnUtils;\n\nconst { fireEvent: DataTableCore_fireEvent, objectEach, uniqueKey } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTableCore {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    constructor(options = {}) {\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        let rowCount = 0;\n        objectEach(options.columns || {}, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    applyRowCount(rowCount) {\n        this.rowCount = rowCount;\n        objectEach(this.columns, (column, columnName) => {\n            if (column.length !== rowCount) {\n                this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    }\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1) {\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            let length = 0;\n            objectEach(this.columns, (column, columnName) => {\n                this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length = column.length;\n            });\n            this.rowCount = length;\n        }\n        DataTableCore_fireEvent(this, 'afterDeleteRows', { rowIndex, rowCount });\n        this.versionTag = uniqueKey();\n    }\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    }\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return (columnNames || Object.keys(this.columns)).reduce((columns, columnName) => {\n            columns[columnName] = this.columns[columnName];\n            return columns;\n        }, {});\n    }\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return (columnNames || Object.keys(this.columns)).map((key) => this.columns[key]?.[rowIndex]);\n    }\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumn(columnName, column = [], rowIndex = 0, eventDetail) {\n        this.setColumns({ [columnName]: column }, rowIndex, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail) {\n        let rowCount = this.rowCount;\n        objectEach(columns, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!eventDetail?.silent) {\n            DataTableCore_fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex = this.rowCount, insert, eventDetail) {\n        const { columns } = this, indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        objectEach(row, (cellValue, columnName) => {\n            let column = columns[columnName] ||\n                eventDetail?.addColumns !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!eventDetail?.silent) {\n            DataTableCore_fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Data/DataTable.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Jomar Hønsi\n *  - Dawid Dragula\n *\n * */\n\n\n\n\nconst { addEvent: DataTable_addEvent, defined, extend, fireEvent: DataTable_fireEvent, isNumber, uniqueKey: DataTable_uniqueKey } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTable extends Data_DataTableCore {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Tests whether a row contains only `null` values or is equal to\n     * DataTable.NULL. If all columns have `null` values, the function returns\n     * `true`. Otherwise, it returns `false` to indicate that the row contains\n     * at least one non-null value.\n     *\n     * @function Highcharts.DataTable.isNull\n     *\n     * @param {Highcharts.DataTableRow|Highcharts.DataTableRowObject} row\n     * Row to test.\n     *\n     * @return {boolean}\n     * Returns `true`, if the row contains only null, otherwise `false`.\n     *\n     * @example\n     * if (DataTable.isNull(row)) {\n     *   // handle null row\n     * }\n     */\n    static isNull(row) {\n        if (row === DataTable.NULL) {\n            return true;\n        }\n        if (row instanceof Array) {\n            if (!row.length) {\n                return false;\n            }\n            for (let i = 0, iEnd = row.length; i < iEnd; ++i) {\n                if (row[i] !== null) {\n                    return false;\n                }\n            }\n        }\n        else {\n            const columnNames = Object.keys(row);\n            if (!columnNames.length) {\n                return false;\n            }\n            for (let i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                if (row[columnNames[i]] !== null) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options = {}) {\n        super(options);\n        this.modified = this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Returns a clone of this table. The cloned table is completely independent\n     * of the original, and any changes made to the clone will not affect\n     * the original table.\n     *\n     * @function Highcharts.DataTable#clone\n     *\n     * @param {boolean} [skipColumns]\n     * Whether to clone columns or not.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Clone of this data table.\n     *\n     * @emits #cloneTable\n     * @emits #afterCloneTable\n     */\n    clone(skipColumns, eventDetail) {\n        const table = this, tableOptions = {};\n        table.emit({ type: 'cloneTable', detail: eventDetail });\n        if (!skipColumns) {\n            tableOptions.columns = table.columns;\n        }\n        if (!table.autoId) {\n            tableOptions.id = table.id;\n        }\n        const tableClone = new DataTable(tableOptions);\n        if (!skipColumns) {\n            tableClone.versionTag = table.versionTag;\n            tableClone.originalRowIndexes = table.originalRowIndexes;\n            tableClone.localRowIndexes = table.localRowIndexes;\n        }\n        table.emit({\n            type: 'afterCloneTable',\n            detail: eventDetail,\n            tableClone\n        });\n        return tableClone;\n    }\n    /**\n     * Deletes columns from the table.\n     *\n     * @function Highcharts.DataTable#deleteColumns\n     *\n     * @param {Array<string>} [columnNames]\n     * Names of columns to delete. If no array is provided, all\n     * columns will be deleted.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTableColumnCollection|undefined}\n     * Returns the deleted columns, if found.\n     *\n     * @emits #deleteColumns\n     * @emits #afterDeleteColumns\n     */\n    deleteColumns(columnNames, eventDetail) {\n        const table = this, columns = table.columns, deletedColumns = {}, modifiedColumns = {}, modifier = table.modifier, rowCount = table.rowCount;\n        columnNames = (columnNames || Object.keys(columns));\n        if (columnNames.length) {\n            table.emit({\n                type: 'deleteColumns',\n                columnNames,\n                detail: eventDetail\n            });\n            for (let i = 0, iEnd = columnNames.length, column, columnName; i < iEnd; ++i) {\n                columnName = columnNames[i];\n                column = columns[columnName];\n                if (column) {\n                    deletedColumns[columnName] = column;\n                    modifiedColumns[columnName] = new Array(rowCount);\n                }\n                delete columns[columnName];\n            }\n            if (!Object.keys(columns).length) {\n                table.rowCount = 0;\n                this.deleteRowIndexReferences();\n            }\n            if (modifier) {\n                modifier.modifyColumns(table, modifiedColumns, 0, eventDetail);\n            }\n            table.emit({\n                type: 'afterDeleteColumns',\n                columns: deletedColumns,\n                columnNames,\n                detail: eventDetail\n            });\n            return deletedColumns;\n        }\n    }\n    /**\n     * Deletes the row index references. This is useful when the original table\n     * is deleted, and the references are no longer needed. This table is\n     * then considered an original table or a table that has the same row's\n     * order as the original table.\n     */\n    deleteRowIndexReferences() {\n        delete this.originalRowIndexes;\n        delete this.localRowIndexes;\n        // Here, in case of future need, can be implemented updating of the\n        // modified tables' row indexes references.\n    }\n    /**\n     * Deletes rows in this table.\n     *\n     * @function Highcharts.DataTable#deleteRows\n     *\n     * @param {number} [rowIndex]\n     * Index to start delete of rows. If not specified, all rows will be\n     * deleted.\n     *\n     * @param {number} [rowCount=1]\n     * Number of rows to delete.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Array<Highcharts.DataTableRow>}\n     * Returns the deleted rows, if found.\n     *\n     * @emits #deleteRows\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1, eventDetail) {\n        const table = this, deletedRows = [], modifiedRows = [], modifier = table.modifier;\n        table.emit({\n            type: 'deleteRows',\n            detail: eventDetail,\n            rowCount,\n            rowIndex: (rowIndex || 0)\n        });\n        if (typeof rowIndex === 'undefined') {\n            rowIndex = 0;\n            rowCount = table.rowCount;\n        }\n        if (rowCount > 0 && rowIndex < table.rowCount) {\n            const columns = table.columns, columnNames = Object.keys(columns);\n            for (let i = 0, iEnd = columnNames.length, column, deletedCells, columnName; i < iEnd; ++i) {\n                columnName = columnNames[i];\n                column = columns[columnName];\n                const result = Data_ColumnUtils.splice(column, rowIndex, rowCount);\n                deletedCells = result.removed;\n                columns[columnName] = column = result.array;\n                if (!i) {\n                    table.rowCount = column.length;\n                }\n                for (let j = 0, jEnd = deletedCells.length; j < jEnd; ++j) {\n                    deletedRows[j] = (deletedRows[j] || []);\n                    deletedRows[j][i] = deletedCells[j];\n                }\n                modifiedRows.push(new Array(iEnd));\n            }\n        }\n        if (modifier) {\n            modifier.modifyRows(table, modifiedRows, (rowIndex || 0), eventDetail);\n        }\n        table.emit({\n            type: 'afterDeleteRows',\n            detail: eventDetail,\n            rowCount,\n            rowIndex: (rowIndex || 0),\n            rows: deletedRows\n        });\n        return deletedRows;\n    }\n    /**\n     * Emits an event on this table to all registered callbacks of the given\n     * event.\n     * @private\n     *\n     * @param {DataTable.Event} e\n     * Event object with event information.\n     */\n    emit(e) {\n        if ([\n            'afterDeleteColumns',\n            'afterDeleteRows',\n            'afterSetCell',\n            'afterSetColumns',\n            'afterSetRows'\n        ].includes(e.type)) {\n            this.versionTag = DataTable_uniqueKey();\n        }\n        DataTable_fireEvent(this, e.type, e);\n    }\n    /**\n     * Fetches a single cell value.\n     *\n     * @function Highcharts.DataTable#getCell\n     *\n     * @param {string} columnName\n     * Column name of the cell to retrieve.\n     *\n     * @param {number} rowIndex\n     * Row index of the cell to retrieve.\n     *\n     * @return {Highcharts.DataTableCellType|undefined}\n     * Returns the cell value or `undefined`.\n     */\n    getCell(columnName, rowIndex) {\n        const table = this;\n        const column = table.columns[columnName];\n        if (column) {\n            return column[rowIndex];\n        }\n    }\n    /**\n     * Fetches a cell value for the given row as a boolean.\n     *\n     * @function Highcharts.DataTable#getCellAsBoolean\n     *\n     * @param {string} columnName\n     * Column name to fetch.\n     *\n     * @param {number} rowIndex\n     * Row index to fetch.\n     *\n     * @return {boolean}\n     * Returns the cell value of the row as a boolean.\n     */\n    getCellAsBoolean(columnName, rowIndex) {\n        const table = this;\n        const column = table.columns[columnName];\n        return !!(column && column[rowIndex]);\n    }\n    /**\n     * Fetches a cell value for the given row as a number.\n     *\n     * @function Highcharts.DataTable#getCellAsNumber\n     *\n     * @param {string} columnName\n     * Column name or to fetch.\n     *\n     * @param {number} rowIndex\n     * Row index to fetch.\n     *\n     * @param {boolean} [useNaN]\n     * Whether to return NaN instead of `null` and `undefined`.\n     *\n     * @return {number|null}\n     * Returns the cell value of the row as a number.\n     */\n    getCellAsNumber(columnName, rowIndex, useNaN) {\n        const table = this;\n        const column = table.columns[columnName];\n        let cellValue = (column && column[rowIndex]);\n        switch (typeof cellValue) {\n            case 'boolean':\n                return (cellValue ? 1 : 0);\n            case 'number':\n                return (isNaN(cellValue) && !useNaN ? null : cellValue);\n        }\n        cellValue = parseFloat(`${cellValue ?? ''}`);\n        return (isNaN(cellValue) && !useNaN ? null : cellValue);\n    }\n    /**\n     * Fetches a cell value for the given row as a string.\n     *\n     * @function Highcharts.DataTable#getCellAsString\n     *\n     * @param {string} columnName\n     * Column name to fetch.\n     *\n     * @param {number} rowIndex\n     * Row index to fetch.\n     *\n     * @return {string}\n     * Returns the cell value of the row as a string.\n     */\n    getCellAsString(columnName, rowIndex) {\n        const table = this;\n        const column = table.columns[columnName];\n        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n        return `${(column && column[rowIndex])}`;\n    }\n    /**\n     * Fetches the given column by the canonical column name.\n     * This function is a simplified wrap of {@link getColumns}.\n     *\n     * @function Highcharts.DataTable#getColumn\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @param {boolean} [asReference]\n     * Whether to return the column as a readonly reference.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, asReference) {\n        return this.getColumns([columnName], asReference)[columnName];\n    }\n    /**\n     * Fetches the given column by the canonical column name, and\n     * validates the type of the first few cells. If the first defined cell is\n     * of type number, it assumes for performance reasons, that all cells are of\n     * type number or `null`. Otherwise it will convert all cells to number\n     * type, except `null`.\n     *\n     * @deprecated\n     *\n     * @function Highcharts.DataTable#getColumnAsNumbers\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @param {boolean} [useNaN]\n     * Whether to use NaN instead of `null` and `undefined`.\n     *\n     * @return {Array<(number|null)>}\n     * A copy of the column, or an empty array if not found.\n     */\n    getColumnAsNumbers(columnName, useNaN) {\n        const table = this, columns = table.columns;\n        const column = columns[columnName], columnAsNumber = [];\n        if (column) {\n            const columnLength = column.length;\n            if (useNaN) {\n                for (let i = 0; i < columnLength; ++i) {\n                    columnAsNumber.push(table.getCellAsNumber(columnName, i, true));\n                }\n            }\n            else {\n                for (let i = 0, cellValue; i < columnLength; ++i) {\n                    cellValue = column[i];\n                    if (typeof cellValue === 'number') {\n                        // Assume unmixed data for performance reasons\n                        return column.slice();\n                    }\n                    if (cellValue !== null &&\n                        typeof cellValue !== 'undefined') {\n                        break;\n                    }\n                }\n                for (let i = 0; i < columnLength; ++i) {\n                    columnAsNumber.push(table.getCellAsNumber(columnName, i));\n                }\n            }\n        }\n        return columnAsNumber;\n    }\n    /**\n     * Fetches all column names.\n     *\n     * @function Highcharts.DataTable#getColumnNames\n     *\n     * @return {Array<string>}\n     * Returns all column names.\n     */\n    getColumnNames() {\n        const table = this, columnNames = Object.keys(table.columns);\n        return columnNames;\n    }\n    /**\n     * Retrieves all or the given columns.\n     *\n     * @function Highcharts.DataTable#getColumns\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @param {boolean} [asReference]\n     * Whether to return columns as a readonly reference.\n     *\n     * @param {boolean} [asBasicColumns]\n     * Whether to transform all typed array columns to normal arrays.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, asReference, asBasicColumns) {\n        const table = this, tableColumns = table.columns, columns = {};\n        columnNames = (columnNames || Object.keys(tableColumns));\n        for (let i = 0, iEnd = columnNames.length, column, columnName; i < iEnd; ++i) {\n            columnName = columnNames[i];\n            column = tableColumns[columnName];\n            if (column) {\n                if (asReference) {\n                    columns[columnName] = column;\n                }\n                else if (asBasicColumns && !Array.isArray(column)) {\n                    columns[columnName] = Array.from(column);\n                }\n                else {\n                    columns[columnName] = column.slice();\n                }\n            }\n        }\n        return columns;\n    }\n    /**\n     * Takes the original row index and returns the local row index in the\n     * modified table for which this function is called.\n     *\n     * @param {number} originalRowIndex\n     * Original row index to get the local row index for.\n     *\n     * @return {number|undefined}\n     * Returns the local row index or `undefined` if not found.\n     */\n    getLocalRowIndex(originalRowIndex) {\n        const { localRowIndexes } = this;\n        if (localRowIndexes) {\n            return localRowIndexes[originalRowIndex];\n        }\n        return originalRowIndex;\n    }\n    /**\n     * Retrieves the modifier for the table.\n     * @private\n     *\n     * @return {Highcharts.DataModifier|undefined}\n     * Returns the modifier or `undefined`.\n     */\n    getModifier() {\n        return this.modifier;\n    }\n    /**\n     * Takes the local row index and returns the index of the corresponding row\n     * in the original table.\n     *\n     * @param {number} rowIndex\n     * Local row index to get the original row index for.\n     *\n     * @return {number|undefined}\n     * Returns the original row index or `undefined` if not found.\n     */\n    getOriginalRowIndex(rowIndex) {\n        const { originalRowIndexes } = this;\n        if (originalRowIndexes) {\n            return originalRowIndexes[rowIndex];\n        }\n        return rowIndex;\n    }\n    /**\n     * Retrieves the row at a given index. This function is a simplified wrap of\n     * {@link getRows}.\n     *\n     * @function Highcharts.DataTable#getRow\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names in order to retrieve.\n     *\n     * @return {Highcharts.DataTableRow}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return this.getRows(rowIndex, 1, columnNames)[0];\n    }\n    /**\n     * Returns the number of rows in this table.\n     *\n     * @function Highcharts.DataTable#getRowCount\n     *\n     * @return {number}\n     * Number of rows in this table.\n     */\n    getRowCount() {\n        // @todo Implement via property getter `.length` browsers supported\n        return this.rowCount;\n    }\n    /**\n     * Retrieves the index of the first row matching a specific cell value.\n     *\n     * @function Highcharts.DataTable#getRowIndexBy\n     *\n     * @param {string} columnName\n     * Column to search in.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Cell value to search for. `NaN` and `undefined` are not supported.\n     *\n     * @param {number} [rowIndexOffset]\n     * Index offset to start searching.\n     *\n     * @return {number|undefined}\n     * Index of the first row matching the cell value.\n     */\n    getRowIndexBy(columnName, cellValue, rowIndexOffset) {\n        const table = this;\n        const column = table.columns[columnName];\n        if (column) {\n            let rowIndex = -1;\n            if (Array.isArray(column)) {\n                // Normal array\n                rowIndex = column.indexOf(cellValue, rowIndexOffset);\n            }\n            else if (isNumber(cellValue)) {\n                // Typed array\n                rowIndex = column.indexOf(cellValue, rowIndexOffset);\n            }\n            if (rowIndex !== -1) {\n                return rowIndex;\n            }\n        }\n    }\n    /**\n     * Retrieves the row at a given index. This function is a simplified wrap of\n     * {@link getRowObjects}.\n     *\n     * @function Highcharts.DataTable#getRowObject\n     *\n     * @param {number} rowIndex\n     * Row index.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names and their order to retrieve.\n     *\n     * @return {Highcharts.DataTableRowObject}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRowObject(rowIndex, columnNames) {\n        return this.getRowObjects(rowIndex, 1, columnNames)[0];\n    }\n    /**\n     * Fetches all or a number of rows.\n     *\n     * @function Highcharts.DataTable#getRowObjects\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to fetch. Defaults to first row at index `0`.\n     *\n     * @param {number} [rowCount]\n     * Number of rows to fetch. Defaults to maximal number of rows.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names and their order to retrieve.\n     *\n     * @return {Highcharts.DataTableRowObject}\n     * Returns retrieved rows.\n     */\n    getRowObjects(rowIndex = 0, rowCount = (this.rowCount - rowIndex), columnNames) {\n        const table = this, columns = table.columns, rows = new Array(rowCount);\n        columnNames = (columnNames || Object.keys(columns));\n        for (let i = rowIndex, i2 = 0, iEnd = Math.min(table.rowCount, (rowIndex + rowCount)), column, row; i < iEnd; ++i, ++i2) {\n            row = rows[i2] = {};\n            for (const columnName of columnNames) {\n                column = columns[columnName];\n                row[columnName] = (column ? column[i] : void 0);\n            }\n        }\n        return rows;\n    }\n    /**\n     * Fetches all or a number of rows.\n     *\n     * @function Highcharts.DataTable#getRows\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to fetch. Defaults to first row at index `0`.\n     *\n     * @param {number} [rowCount]\n     * Number of rows to fetch. Defaults to maximal number of rows.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names and their order to retrieve.\n     *\n     * @return {Highcharts.DataTableRow}\n     * Returns retrieved rows.\n     */\n    getRows(rowIndex = 0, rowCount = (this.rowCount - rowIndex), columnNames) {\n        const table = this, columns = table.columns, rows = new Array(rowCount);\n        columnNames = (columnNames || Object.keys(columns));\n        for (let i = rowIndex, i2 = 0, iEnd = Math.min(table.rowCount, (rowIndex + rowCount)), column, row; i < iEnd; ++i, ++i2) {\n            row = rows[i2] = [];\n            for (const columnName of columnNames) {\n                column = columns[columnName];\n                row.push(column ? column[i] : void 0);\n            }\n        }\n        return rows;\n    }\n    /**\n     * Returns the unique version tag of the current state of the table.\n     *\n     * @function Highcharts.DataTable#getVersionTag\n     *\n     * @return {string}\n     * Unique version tag.\n     */\n    getVersionTag() {\n        return this.versionTag;\n    }\n    /**\n     * Checks for given column names.\n     *\n     * @function Highcharts.DataTable#hasColumns\n     *\n     * @param {Array<string>} columnNames\n     * Column names to check.\n     *\n     * @return {boolean}\n     * Returns `true` if all columns have been found, otherwise `false`.\n     */\n    hasColumns(columnNames) {\n        const table = this, columns = table.columns;\n        for (let i = 0, iEnd = columnNames.length, columnName; i < iEnd; ++i) {\n            columnName = columnNames[i];\n            if (!columns[columnName]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Searches for a specific cell value.\n     *\n     * @function Highcharts.DataTable#hasRowWith\n     *\n     * @param {string} columnName\n     * Column to search in.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Cell value to search for. `NaN` and `undefined` are not supported.\n     *\n     * @return {boolean}\n     * True, if a row has been found, otherwise false.\n     */\n    hasRowWith(columnName, cellValue) {\n        const table = this;\n        const column = table.columns[columnName];\n        // Normal array\n        if (Array.isArray(column)) {\n            return (column.indexOf(cellValue) !== -1);\n        }\n        // Typed array\n        if (defined(cellValue) && Number.isFinite(cellValue)) {\n            return (column.indexOf(+cellValue) !== -1);\n        }\n        return false;\n    }\n    /**\n     * Registers a callback for a specific event.\n     *\n     * @function Highcharts.DataTable#on\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {Highcharts.EventCallbackFunction<Highcharts.DataTable>} callback\n     * Function to register for an event callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the event.\n     */\n    on(type, callback) {\n        return DataTable_addEvent(this, type, callback);\n    }\n    /**\n     * Renames a column of cell values.\n     *\n     * @function Highcharts.DataTable#renameColumn\n     *\n     * @param {string} columnName\n     * Name of the column to be renamed.\n     *\n     * @param {string} newColumnName\n     * New name of the column. An existing column with the same name will be\n     * replaced.\n     *\n     * @return {boolean}\n     * Returns `true` if successful, `false` if the column was not found.\n     */\n    renameColumn(columnName, newColumnName) {\n        const table = this, columns = table.columns;\n        if (columns[columnName]) {\n            if (columnName !== newColumnName) {\n                columns[newColumnName] = columns[columnName];\n                delete columns[columnName];\n            }\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Sets a cell value based on the row index and column.  Will\n     * insert a new column, if not found.\n     *\n     * @function Highcharts.DataTable#setCell\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index to set.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Cell value to set.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setCell\n     * @emits #afterSetCell\n     */\n    setCell(columnName, rowIndex, cellValue, eventDetail) {\n        const table = this, columns = table.columns, modifier = table.modifier;\n        let column = columns[columnName];\n        if (column && column[rowIndex] === cellValue) {\n            return;\n        }\n        table.emit({\n            type: 'setCell',\n            cellValue,\n            columnName: columnName,\n            detail: eventDetail,\n            rowIndex\n        });\n        if (!column) {\n            column = columns[columnName] = new Array(table.rowCount);\n        }\n        if (rowIndex >= table.rowCount) {\n            table.rowCount = (rowIndex + 1);\n        }\n        column[rowIndex] = cellValue;\n        if (modifier) {\n            modifier.modifyCell(table, columnName, rowIndex, cellValue);\n        }\n        table.emit({\n            type: 'afterSetCell',\n            cellValue,\n            columnName: columnName,\n            detail: eventDetail,\n            rowIndex\n        });\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found.\n     *\n     * @function Highcharts.DataTable#setColumns\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Keep undefined to reset.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @param {boolean} [typeAsOriginal=false]\n     * Determines whether the original column retains its type when data\n     * replaced. If `true`, the original column keeps its type. If not\n     * (default), the original column will adopt the type of the replacement\n     * column.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail, typeAsOriginal) {\n        const table = this, tableColumns = table.columns, tableModifier = table.modifier, columnNames = Object.keys(columns);\n        let rowCount = table.rowCount;\n        table.emit({\n            type: 'setColumns',\n            columns,\n            columnNames,\n            detail: eventDetail,\n            rowIndex\n        });\n        if (!defined(rowIndex) && !typeAsOriginal) {\n            super.setColumns(columns, rowIndex, extend(eventDetail, { silent: true }));\n        }\n        else {\n            for (let i = 0, iEnd = columnNames.length, column, tableColumn, columnName, ArrayConstructor; i < iEnd; ++i) {\n                columnName = columnNames[i];\n                column = columns[columnName];\n                tableColumn = tableColumns[columnName];\n                ArrayConstructor = Object.getPrototypeOf((tableColumn && typeAsOriginal) ? tableColumn : column).constructor;\n                if (!tableColumn) {\n                    tableColumn = new ArrayConstructor(rowCount);\n                }\n                else if (ArrayConstructor === Array) {\n                    if (!Array.isArray(tableColumn)) {\n                        tableColumn = Array.from(tableColumn);\n                    }\n                }\n                else if (tableColumn.length < rowCount) {\n                    tableColumn =\n                        new ArrayConstructor(rowCount);\n                    tableColumn.set(tableColumns[columnName]);\n                }\n                tableColumns[columnName] = tableColumn;\n                for (let i = (rowIndex || 0), iEnd = column.length; i < iEnd; ++i) {\n                    tableColumn[i] = column[i];\n                }\n                rowCount = Math.max(rowCount, column.length);\n            }\n            this.applyRowCount(rowCount);\n        }\n        if (tableModifier) {\n            tableModifier.modifyColumns(table, columns, rowIndex || 0);\n        }\n        table.emit({\n            type: 'afterSetColumns',\n            columns,\n            columnNames,\n            detail: eventDetail,\n            rowIndex\n        });\n    }\n    /**\n     * Sets or unsets the modifier for the table.\n     *\n     * @param {Highcharts.DataModifier} [modifier]\n     * Modifier to set, or `undefined` to unset.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<Highcharts.DataTable>}\n     * Resolves to this table if successful, or rejects on failure.\n     *\n     * @emits #setModifier\n     * @emits #afterSetModifier\n     */\n    setModifier(modifier, eventDetail) {\n        const table = this;\n        let promise;\n        table.emit({\n            type: 'setModifier',\n            detail: eventDetail,\n            modifier,\n            modified: table.modified\n        });\n        table.modified = table;\n        table.modifier = modifier;\n        if (modifier) {\n            promise = modifier.modify(table);\n        }\n        else {\n            promise = Promise.resolve(table);\n        }\n        return promise\n            .then((table) => {\n            table.emit({\n                type: 'afterSetModifier',\n                detail: eventDetail,\n                modifier,\n                modified: table.modified\n            });\n            return table;\n        })['catch']((error) => {\n            table.emit({\n                type: 'setModifierError',\n                error,\n                modifier,\n                modified: table.modified\n            });\n            throw error;\n        });\n    }\n    /**\n     * Sets the original row indexes for the table. It is used to keep the\n     * reference to the original rows when modifying the table.\n     *\n     * @param {Array<number|undefined>} originalRowIndexes\n     * Original row indexes array.\n     *\n     * @param {boolean} omitLocalRowIndexes\n     * Whether to omit the local row indexes calculation. Defaults to `false`.\n     */\n    setOriginalRowIndexes(originalRowIndexes, omitLocalRowIndexes = false) {\n        this.originalRowIndexes = originalRowIndexes;\n        if (omitLocalRowIndexes) {\n            return;\n        }\n        const modifiedIndexes = this.localRowIndexes = [];\n        for (let i = 0, iEnd = originalRowIndexes.length, originalIndex; i < iEnd; ++i) {\n            originalIndex = originalRowIndexes[i];\n            if (defined(originalIndex)) {\n                modifiedIndexes[originalIndex] = i;\n            }\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row, if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     *\n     * Note: This function is just a simplified wrap of\n     * {@link Highcharts.DataTable#setRows}.\n     *\n     * @function Highcharts.DataTable#setRow\n     *\n     * @param {Highcharts.DataTableRow|Highcharts.DataTableRowObject} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefind` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setRows\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex, insert, eventDetail) {\n        this.setRows([row], rowIndex, insert, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple rows. Will insert new rows, if no index was\n     * was provided, or if the index is higher than the total number of table\n     * rows.\n     *\n     * @function Highcharts.DataTable#setRows\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Row values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to set. Leave `undefined` to add as new rows.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setRows\n     * @emits #afterSetRows\n     */\n    setRows(rows, rowIndex = this.rowCount, insert, eventDetail) {\n        const table = this, columns = table.columns, columnNames = Object.keys(columns), modifier = table.modifier, rowCount = rows.length;\n        table.emit({\n            type: 'setRows',\n            detail: eventDetail,\n            rowCount,\n            rowIndex,\n            rows\n        });\n        for (let i = 0, i2 = rowIndex, row; i < rowCount; ++i, ++i2) {\n            row = rows[i];\n            if (row === DataTable.NULL) {\n                for (let j = 0, jEnd = columnNames.length; j < jEnd; ++j) {\n                    const column = columns[columnNames[j]];\n                    if (insert) {\n                        columns[columnNames[j]] = Data_ColumnUtils.splice(column, i2, 0, true, [null]).array;\n                    }\n                    else {\n                        column[i2] = null;\n                    }\n                }\n            }\n            else if (row instanceof Array) {\n                for (let j = 0, jEnd = columnNames.length; j < jEnd; ++j) {\n                    columns[columnNames[j]][i2] = row[j];\n                }\n            }\n            else {\n                super.setRow(row, i2, void 0, { silent: true });\n            }\n        }\n        const indexRowCount = insert ?\n            rowCount + rows.length :\n            rowIndex + rowCount;\n        if (indexRowCount > table.rowCount) {\n            table.rowCount = indexRowCount;\n            for (let i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                const columnName = columnNames[i];\n                columns[columnName] = Data_ColumnUtils.setLength(columns[columnName], indexRowCount);\n            }\n        }\n        if (modifier) {\n            modifier.modifyRows(table, rows, rowIndex);\n        }\n        table.emit({\n            type: 'afterSetRows',\n            detail: eventDetail,\n            rowCount,\n            rowIndex,\n            rows\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Null state for a row record. In some cases, a row in a table may not\n * contain any data or may be invalid. In these cases, a null state can be\n * used to indicate that the row record is empty or invalid.\n *\n * @name Highcharts.DataTable.NULL\n * @type {Highcharts.DataTableRowObject}\n *\n * @see {@link Highcharts.DataTable.isNull} for a null test.\n *\n * @example\n * table.setRows([DataTable.NULL, DataTable.NULL], 10);\n */\nDataTable.NULL = {};\n/**\n * Semantic version string of the DataTable class.\n * @internal\n */\nDataTable.version = '1.0.0';\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTable = (DataTable);\n\n;// ./code/es-modules/Data/Connectors/DataConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Wojciech Chmiel\n *  - Gøran Slettemark\n *\n * */\n\n\n\n\nconst { addEvent: DataConnector_addEvent, fireEvent: DataConnector_fireEvent, merge: DataConnector_merge, pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Abstract class providing an interface for managing a DataConnector.\n *\n * @private\n */\nclass DataConnector {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructor for the connector class.\n     *\n     * @param {DataConnector.UserOptions} [options]\n     * Options to use in the connector.\n     */\n    constructor(options = {}) {\n        this.table = new Data_DataTable(options.dataTable);\n        this.metadata = options.metadata || { columns: {} };\n    }\n    /**\n     * Poll timer ID, if active.\n     */\n    get polling() {\n        return !!this._polling;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Method for adding metadata for a single column.\n     *\n     * @param {string} name\n     * The name of the column to be described.\n     *\n     * @param {DataConnector.MetaColumn} columnMeta\n     * The metadata to apply to the column.\n     */\n    describeColumn(name, columnMeta) {\n        const connector = this, columns = connector.metadata.columns;\n        columns[name] = DataConnector_merge(columns[name] || {}, columnMeta);\n    }\n    /**\n     * Method for applying columns meta information to the whole DataConnector.\n     *\n     * @param {Highcharts.Dictionary<DataConnector.MetaColumn>} columns\n     * Pairs of column names and MetaColumn objects.\n     */\n    describeColumns(columns) {\n        const connector = this, columnNames = Object.keys(columns);\n        let columnName;\n        while (typeof (columnName = columnNames.pop()) === 'string') {\n            connector.describeColumn(columnName, columns[columnName]);\n        }\n    }\n    /**\n     * Emits an event on the connector to all registered callbacks of this\n     * event.\n     *\n     * @param {DataConnector.Event} [e]\n     * Event object containing additional event information.\n     */\n    emit(e) {\n        DataConnector_fireEvent(this, e.type, e);\n    }\n    /**\n     * Returns the order of columns.\n     *\n     * @param {boolean} [usePresentationState]\n     * Whether to use the column order of the presentation state of the table.\n     *\n     * @return {Array<string>|undefined}\n     * Order of columns.\n     */\n    getColumnOrder(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    usePresentationState) {\n        const connector = this, columns = connector.metadata.columns, names = Object.keys(columns || {});\n        if (names.length) {\n            return names.sort((a, b) => (pick(columns[a].index, 0) - pick(columns[b].index, 0)));\n        }\n    }\n    /**\n     * Retrieves the columns of the dataTable,\n     * applies column order from meta.\n     *\n     * @param {boolean} [usePresentationOrder]\n     * Whether to use the column order of the presentation state of the table.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * An object with the properties `columnNames` and `columnValues`\n     */\n    getSortedColumns(usePresentationOrder) {\n        return this.table.getColumns(this.getColumnOrder(usePresentationOrder));\n    }\n    /**\n     * The default load method, which fires the `afterLoad` event\n     *\n     * @return {Promise<DataConnector>}\n     * The loaded connector.\n     *\n     * @emits DataConnector#afterLoad\n     */\n    load() {\n        DataConnector_fireEvent(this, 'afterLoad', { table: this.table });\n        return Promise.resolve(this);\n    }\n    /**\n     * Registers a callback for a specific connector event.\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {DataEventEmitter.Callback} callback\n     * Function to register for the connector callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the connector event.\n     */\n    on(type, callback) {\n        return DataConnector_addEvent(this, type, callback);\n    }\n    /**\n     * The default save method, which fires the `afterSave` event.\n     *\n     * @return {Promise<DataConnector>}\n     * The saved connector.\n     *\n     * @emits DataConnector#afterSave\n     * @emits DataConnector#saveError\n     */\n    save() {\n        DataConnector_fireEvent(this, 'saveError', { table: this.table });\n        return Promise.reject(new Error('Not implemented'));\n    }\n    /**\n     * Sets the index and order of columns.\n     *\n     * @param {Array<string>} columnNames\n     * Order of columns.\n     */\n    setColumnOrder(columnNames) {\n        const connector = this;\n        for (let i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n            connector.describeColumn(columnNames[i], { index: i });\n        }\n    }\n    setModifierOptions(modifierOptions) {\n        const ModifierClass = (modifierOptions &&\n            Modifiers_DataModifier.types[modifierOptions.type]);\n        return this.table\n            .setModifier(ModifierClass ?\n            new ModifierClass(modifierOptions) :\n            void 0)\n            .then(() => this);\n    }\n    /**\n     * Starts polling new data after the specific time span in milliseconds.\n     *\n     * @param {number} refreshTime\n     * Refresh time in milliseconds between polls.\n     */\n    startPolling(refreshTime = 1000) {\n        const connector = this;\n        window.clearTimeout(connector._polling);\n        connector._polling = window.setTimeout(() => connector\n            .load()['catch']((error) => connector.emit({\n            type: 'loadError',\n            error,\n            table: connector.table\n        }))\n            .then(() => {\n            if (connector._polling) {\n                connector.startPolling(refreshTime);\n            }\n        }), refreshTime);\n    }\n    /**\n     * Stops polling data.\n     */\n    stopPolling() {\n        const connector = this;\n        window.clearTimeout(connector._polling);\n        delete connector._polling;\n    }\n    /**\n     * Retrieves metadata from a single column.\n     *\n     * @param {string} name\n     * The identifier for the column that should be described\n     *\n     * @return {DataConnector.MetaColumn|undefined}\n     * Returns a MetaColumn object if found.\n     */\n    whatIs(name) {\n        return this.metadata.columns[name];\n    }\n}\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (DataConnector) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Registry as a record object with connector names and their class.\n     */\n    DataConnector.types = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a connector class to the registry. The connector has to provide the\n     * `DataConnector.options` property and the `DataConnector.load` method to\n     * modify the table.\n     *\n     * @private\n     *\n     * @param {string} key\n     * Registry key of the connector class.\n     *\n     * @param {DataConnectorType} DataConnectorClass\n     * Connector class (aka class constructor) to register.\n     *\n     * @return {boolean}\n     * Returns true, if the registration was successful. False is returned, if\n     * their is already a connector registered with this key.\n     */\n    function registerType(key, DataConnectorClass) {\n        return (!!key &&\n            !DataConnector.types[key] &&\n            !!(DataConnector.types[key] = DataConnectorClass));\n    }\n    DataConnector.registerType = registerType;\n})(DataConnector || (DataConnector = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Connectors_DataConnector = (DataConnector);\n\n;// ./code/es-modules/Data/Converters/DataConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Sebastian Bochan\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *  - Wojciech Chmiel\n *  - Jomar Hønsi\n *\n * */\n\n\n\nconst { addEvent: DataConverter_addEvent, fireEvent: DataConverter_fireEvent, isNumber: DataConverter_isNumber, merge: DataConverter_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Base class providing an interface and basic methods for a DataConverter\n *\n * @private\n */\nclass DataConverter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the DataConverter.\n     *\n     * @param {DataConverter.UserOptions} [options]\n     * Options for the DataConverter.\n     */\n    constructor(options) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        /**\n         * A collection of available date formats.\n         */\n        this.dateFormats = {\n            'YYYY/mm/dd': {\n                regex: /^(\\d{4})([\\-\\.\\/])(\\d{1,2})\\2(\\d{1,2})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[1], match[3] - 1, +match[4]) :\n                        NaN);\n                }\n            },\n            'dd/mm/YYYY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{4})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[4], match[3] - 1, +match[1]) :\n                        NaN);\n                },\n                alternative: 'mm/dd/YYYY' // Different format with the same regex\n            },\n            'mm/dd/YYYY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{4})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[4], match[1] - 1, +match[3]) :\n                        NaN);\n                }\n            },\n            'dd/mm/YY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{2})$/,\n                parser: function (match) {\n                    const d = new Date();\n                    if (!match) {\n                        return NaN;\n                    }\n                    let year = +match[4];\n                    if (year > (d.getFullYear() - 2000)) {\n                        year += 1900;\n                    }\n                    else {\n                        year += 2000;\n                    }\n                    return Date.UTC(year, match[3] - 1, +match[1]);\n                },\n                alternative: 'mm/dd/YY' // Different format with the same regex\n            },\n            'mm/dd/YY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{2})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[4] + 2000, match[1] - 1, +match[3]) :\n                        NaN);\n                }\n            }\n        };\n        const mergedOptions = DataConverter_merge(DataConverter.defaultOptions, options);\n        let regExpPoint = mergedOptions.decimalPoint;\n        if (regExpPoint === '.' || regExpPoint === ',') {\n            regExpPoint = regExpPoint === '.' ? '\\\\.' : ',';\n            this.decimalRegExp =\n                new RegExp('^(-?[0-9]+)' + regExpPoint + '([0-9]+)$');\n        }\n        this.options = mergedOptions;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Converts a value to a boolean.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {boolean}\n     * Converted value as a boolean.\n     */\n    asBoolean(value) {\n        if (typeof value === 'boolean') {\n            return value;\n        }\n        if (typeof value === 'string') {\n            return value !== '' && value !== '0' && value !== 'false';\n        }\n        return !!this.asNumber(value);\n    }\n    /**\n     * Converts a value to a Date.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {globalThis.Date}\n     * Converted value as a Date.\n     */\n    asDate(value) {\n        let timestamp;\n        if (typeof value === 'string') {\n            timestamp = this.parseDate(value);\n        }\n        else if (typeof value === 'number') {\n            timestamp = value;\n        }\n        else if (value instanceof Date) {\n            return value;\n        }\n        else {\n            timestamp = this.parseDate(this.asString(value));\n        }\n        return new Date(timestamp);\n    }\n    /**\n     * Casts a string value to it's guessed type\n     *\n     * @param {*} value\n     * The value to examine.\n     *\n     * @return {number|string|Date}\n     * The converted value.\n     */\n    asGuessedType(value) {\n        const converter = this, typeMap = {\n            'number': converter.asNumber,\n            'Date': converter.asDate,\n            'string': converter.asString\n        };\n        return typeMap[converter.guessType(value)].call(converter, value);\n    }\n    /**\n     * Converts a value to a number.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {number}\n     * Converted value as a number.\n     */\n    asNumber(value) {\n        if (typeof value === 'number') {\n            return value;\n        }\n        if (typeof value === 'boolean') {\n            return value ? 1 : 0;\n        }\n        if (typeof value === 'string') {\n            const decimalRegex = this.decimalRegExp;\n            if (value.indexOf(' ') > -1) {\n                value = value.replace(/\\s+/g, '');\n            }\n            if (decimalRegex) {\n                if (!decimalRegex.test(value)) {\n                    return NaN;\n                }\n                value = value.replace(decimalRegex, '$1.$2');\n            }\n            return parseFloat(value);\n        }\n        if (value instanceof Date) {\n            return value.getDate();\n        }\n        if (value) {\n            return value.getRowCount();\n        }\n        return NaN;\n    }\n    /**\n     * Converts a value to a string.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {string}\n     * Converted value as a string.\n     */\n    asString(value) {\n        return '' + value;\n    }\n    /**\n     * Tries to guess the date format\n     *  - Check if either month candidate exceeds 12\n     *  - Check if year is missing (use current year)\n     *  - Check if a shortened year format is used (e.g. 1/1/99)\n     *  - If no guess can be made, the user must be prompted\n     * data is the data to deduce a format based on\n     * @private\n     *\n     * @param {Array<string>} data\n     * Data to check the format.\n     *\n     * @param {number} limit\n     * Max data to check the format.\n     *\n     * @param {boolean} save\n     * Whether to save the date format in the converter options.\n     */\n    deduceDateFormat(data, limit, save) {\n        const parser = this, stable = [], max = [];\n        let format = 'YYYY/mm/dd', thing, guessedFormat = [], i = 0, madeDeduction = false, \n        /// candidates = {},\n        elem, j;\n        if (!limit || limit > data.length) {\n            limit = data.length;\n        }\n        for (; i < limit; i++) {\n            if (typeof data[i] !== 'undefined' &&\n                data[i] && data[i].length) {\n                thing = data[i]\n                    .trim()\n                    .replace(/[\\-\\.\\/]/g, ' ')\n                    .split(' ');\n                guessedFormat = [\n                    '',\n                    '',\n                    ''\n                ];\n                for (j = 0; j < thing.length; j++) {\n                    if (j < guessedFormat.length) {\n                        elem = parseInt(thing[j], 10);\n                        if (elem) {\n                            max[j] = (!max[j] || max[j] < elem) ? elem : max[j];\n                            if (typeof stable[j] !== 'undefined') {\n                                if (stable[j] !== elem) {\n                                    stable[j] = false;\n                                }\n                            }\n                            else {\n                                stable[j] = elem;\n                            }\n                            if (elem > 31) {\n                                if (elem < 100) {\n                                    guessedFormat[j] = 'YY';\n                                }\n                                else {\n                                    guessedFormat[j] = 'YYYY';\n                                }\n                                /// madeDeduction = true;\n                            }\n                            else if (elem > 12 &&\n                                elem <= 31) {\n                                guessedFormat[j] = 'dd';\n                                madeDeduction = true;\n                            }\n                            else if (!guessedFormat[j].length) {\n                                guessedFormat[j] = 'mm';\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        if (madeDeduction) {\n            // This handles a few edge cases with hard to guess dates\n            for (j = 0; j < stable.length; j++) {\n                if (stable[j] !== false) {\n                    if (max[j] > 12 &&\n                        guessedFormat[j] !== 'YY' &&\n                        guessedFormat[j] !== 'YYYY') {\n                        guessedFormat[j] = 'YY';\n                    }\n                }\n                else if (max[j] > 12 && guessedFormat[j] === 'mm') {\n                    guessedFormat[j] = 'dd';\n                }\n            }\n            // If the middle one is dd, and the last one is dd,\n            // the last should likely be year.\n            if (guessedFormat.length === 3 &&\n                guessedFormat[1] === 'dd' &&\n                guessedFormat[2] === 'dd') {\n                guessedFormat[2] = 'YY';\n            }\n            format = guessedFormat.join('/');\n            // If the caculated format is not valid, we need to present an\n            // error.\n        }\n        // Save the deduced format in the converter options.\n        if (save) {\n            parser.options.dateFormat = format;\n        }\n        return format;\n    }\n    /**\n     * Emits an event on the DataConverter instance.\n     *\n     * @param {DataConverter.Event} [e]\n     * Event object containing additional event data\n     */\n    emit(e) {\n        DataConverter_fireEvent(this, e.type, e);\n    }\n    /**\n     * Initiates the data exporting. Should emit `exportError` on failure.\n     *\n     * @param {DataConnector} connector\n     * Connector to export from.\n     *\n     * @param {DataConverter.Options} [options]\n     * Options for the export.\n     */\n    export(\n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    connector, options\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        this.emit({\n            type: 'exportError',\n            columns: [],\n            headers: []\n        });\n        throw new Error('Not implemented');\n    }\n    /**\n     * Getter for the data table.\n     *\n     * @return {DataTable}\n     * Table of parsed data.\n     */\n    getTable() {\n        throw new Error('Not implemented');\n    }\n    /**\n     * Guesses the potential type of a string value for parsing CSV etc.\n     *\n     * @param {*} value\n     * The value to examine.\n     *\n     * @return {'number'|'string'|'Date'}\n     * Type string, either `string`, `Date`, or `number`.\n     */\n    guessType(value) {\n        const converter = this;\n        let result = 'string';\n        if (typeof value === 'string') {\n            const trimedValue = converter.trim(`${value}`), decimalRegExp = converter.decimalRegExp;\n            let innerTrimedValue = converter.trim(trimedValue, true);\n            if (decimalRegExp) {\n                innerTrimedValue = (decimalRegExp.test(innerTrimedValue) ?\n                    innerTrimedValue.replace(decimalRegExp, '$1.$2') :\n                    '');\n            }\n            const floatValue = parseFloat(innerTrimedValue);\n            if (+innerTrimedValue === floatValue) {\n                // String is numeric\n                value = floatValue;\n            }\n            else {\n                // Determine if a date string\n                const dateValue = converter.parseDate(value);\n                result = DataConverter_isNumber(dateValue) ? 'Date' : 'string';\n            }\n        }\n        if (typeof value === 'number') {\n            // Greater than milliseconds in a year assumed timestamp\n            result = value > 365 * 24 * 3600 * 1000 ? 'Date' : 'number';\n        }\n        return result;\n    }\n    /**\n     * Registers a callback for a specific event.\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {DataEventEmitter.Callback} callback\n     * Function to register for an modifier callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the modifier event.\n     */\n    on(type, callback) {\n        return DataConverter_addEvent(this, type, callback);\n    }\n    /**\n     * Initiates the data parsing. Should emit `parseError` on failure.\n     *\n     * @param {DataConverter.UserOptions} options\n     * Options of the DataConverter.\n     */\n    parse(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    options) {\n        this.emit({\n            type: 'parseError',\n            columns: [],\n            headers: []\n        });\n        throw new Error('Not implemented');\n    }\n    /**\n     * Parse a date and return it as a number.\n     *\n     * @param {string} value\n     * Value to parse.\n     *\n     * @param {string} dateFormatProp\n     * Which of the predefined date formats\n     * to use to parse date values.\n     */\n    parseDate(value, dateFormatProp) {\n        const converter = this, options = converter.options;\n        let dateFormat = dateFormatProp || options.dateFormat, result = NaN, key, format, match;\n        if (options.parseDate) {\n            result = options.parseDate(value);\n        }\n        else {\n            // Auto-detect the date format the first time\n            if (!dateFormat) {\n                for (key in converter.dateFormats) { // eslint-disable-line guard-for-in\n                    format = converter.dateFormats[key];\n                    match = value.match(format.regex);\n                    if (match) {\n                        // `converter.options.dateFormat` = dateFormat = key;\n                        dateFormat = key;\n                        // `converter.options.alternativeFormat` =\n                        // format.alternative || '';\n                        result = format.parser(match);\n                        break;\n                    }\n                }\n                // Next time, use the one previously found\n            }\n            else {\n                format = converter.dateFormats[dateFormat];\n                if (!format) {\n                    // The selected format is invalid\n                    format = converter.dateFormats['YYYY/mm/dd'];\n                }\n                match = value.match(format.regex);\n                if (match) {\n                    result = format.parser(match);\n                }\n            }\n            // Fall back to Date.parse\n            if (!match) {\n                match = Date.parse(value);\n                // External tools like Date.js and MooTools extend Date object\n                // and returns a date.\n                if (typeof match === 'object' &&\n                    match !== null &&\n                    match.getTime) {\n                    result = (match.getTime() -\n                        match.getTimezoneOffset() *\n                            60000);\n                    // Timestamp\n                }\n                else if (DataConverter_isNumber(match)) {\n                    result = match - (new Date(match)).getTimezoneOffset() * 60000;\n                    if ( // Reset dates without year in Chrome\n                    value.indexOf('2001') === -1 &&\n                        (new Date(result)).getFullYear() === 2001) {\n                        result = NaN;\n                    }\n                }\n            }\n        }\n        return result;\n    }\n    /**\n     * Trim a string from whitespaces.\n     *\n     * @param {string} str\n     * String to trim.\n     *\n     * @param {boolean} [inside=false]\n     * Remove all spaces between numbers.\n     *\n     * @return {string}\n     * Trimed string\n     */\n    trim(str, inside) {\n        if (typeof str === 'string') {\n            str = str.replace(/^\\s+|\\s+$/g, '');\n            // Clear white space insdie the string, like thousands separators\n            if (inside && /^[\\d\\s]+$/.test(str)) {\n                str = str.replace(/\\s/g, '');\n            }\n        }\n        return str;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options\n */\nDataConverter.defaultOptions = {\n    dateFormat: '',\n    alternativeFormat: '',\n    startColumn: 0,\n    endColumn: Number.MAX_VALUE,\n    startRow: 0,\n    endRow: Number.MAX_VALUE,\n    firstRowAsNames: true,\n    switchRowsAndColumns: false\n};\n/* *\n *\n *  Class Namespace\n *\n * */\n/**\n * Additionally provided types for events and conversion.\n */\n(function (DataConverter) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Registry as a record object with connector names and their class.\n     */\n    DataConverter.types = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a converter class to the registry.\n     *\n     * @private\n     *\n     * @param {string} key\n     * Registry key of the converter class.\n     *\n     * @param {DataConverterTypes} DataConverterClass\n     * Connector class (aka class constructor) to register.\n     *\n     * @return {boolean}\n     * Returns true, if the registration was successful. False is returned, if\n     * their is already a converter registered with this key.\n     */\n    function registerType(key, DataConverterClass) {\n        return (!!key &&\n            !DataConverter.types[key] &&\n            !!(DataConverter.types[key] = DataConverterClass));\n    }\n    DataConverter.registerType = registerType;\n    /**\n     * Converts an array of columns to a table instance. Second dimension of the\n     * array are the row cells.\n     *\n     * @param {Array<DataTable.Column>} [columns]\n     * Array to convert.\n     *\n     * @param {Array<string>} [headers]\n     * Column names to use.\n     *\n     * @return {DataTable}\n     * Table instance from the arrays.\n     */\n    function getTableFromColumns(columns = [], headers = []) {\n        const table = new Data_DataTable();\n        for (let i = 0, iEnd = Math.max(headers.length, columns.length); i < iEnd; ++i) {\n            table.setColumn(headers[i] || `${i}`, columns[i]);\n        }\n        return table;\n    }\n    DataConverter.getTableFromColumns = getTableFromColumns;\n})(DataConverter || (DataConverter = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Converters_DataConverter = (DataConverter);\n\n;// ./code/es-modules/Data/DataCursor.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * This class manages state cursors pointing on {@link Data.DataTable}. It\n * creates a relation between states of the user interface and the table cells,\n * columns, or rows.\n *\n * @class\n * @name Data.DataCursor\n */\nclass DataCursor {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(stateMap = {}) {\n        this.emittingRegister = [];\n        this.listenerMap = {};\n        this.stateMap = stateMap;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * This function registers a listener for a specific state and table.\n     *\n     * @example\n     * ```TypeScript\n     * dataCursor.addListener(myTable.id, 'hover', (e: DataCursor.Event) => {\n     *     if (e.cursor.type === 'position') {\n     *         console.log(`Hover over row #${e.cursor.row}.`);\n     *     }\n     * });\n     * ```\n     *\n     * @function #addListener\n     *\n     * @param {Data.DataCursor.TableId} tableId\n     * The ID of the table to listen to.\n     *\n     * @param {Data.DataCursor.State} state\n     * The state on the table to listen to.\n     *\n     * @param {Data.DataCursor.Listener} listener\n     * The listener to register.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    addListener(tableId, state, listener) {\n        const listenerMap = this.listenerMap[tableId] = (this.listenerMap[tableId] ||\n            {});\n        const listeners = listenerMap[state] = (listenerMap[state] ||\n            []);\n        listeners.push(listener);\n        return this;\n    }\n    /**\n     * @private\n     */\n    buildEmittingTag(e) {\n        return (e.cursor.type === 'position' ?\n            [\n                e.table.id,\n                e.cursor.column,\n                e.cursor.row,\n                e.cursor.state,\n                e.cursor.type\n            ] :\n            [\n                e.table.id,\n                e.cursor.columns,\n                e.cursor.firstRow,\n                e.cursor.lastRow,\n                e.cursor.state,\n                e.cursor.type\n            ]).join('\\0');\n    }\n    /**\n     * This function emits a state cursor related to a table. It will provide\n     * lasting state cursors of the table to listeners.\n     *\n     * @example\n     * ```ts\n     * dataCursor.emit(myTable, {\n     *     type: 'position',\n     *     column: 'city',\n     *     row: 4,\n     *     state: 'hover',\n     * });\n     * ```\n     *\n     * @param {Data.DataTable} table\n     * The related table of the cursor.\n     *\n     * @param {Data.DataCursor.Type} cursor\n     * The state cursor to emit.\n     *\n     * @param {Event} [event]\n     * Optional event information from a related source.\n     *\n     * @param {boolean} [lasting]\n     * Whether this state cursor should be kept until it is cleared with\n     * {@link DataCursor#remitCursor}.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    emitCursor(table, cursor, event, lasting) {\n        const tableId = table.id, state = cursor.state, listeners = (this.listenerMap[tableId] &&\n            this.listenerMap[tableId][state]);\n        if (listeners) {\n            const stateMap = this.stateMap[tableId] = (this.stateMap[tableId] ?? {});\n            const cursors = stateMap[cursor.state] || [];\n            if (lasting) {\n                if (!cursors.length) {\n                    stateMap[cursor.state] = cursors;\n                }\n                if (DataCursor.getIndex(cursor, cursors) === -1) {\n                    cursors.push(cursor);\n                }\n            }\n            const e = {\n                cursor,\n                cursors,\n                table\n            };\n            if (event) {\n                e.event = event;\n            }\n            const emittingRegister = this.emittingRegister, emittingTag = this.buildEmittingTag(e);\n            if (emittingRegister.indexOf(emittingTag) >= 0) {\n                // Break call stack loops\n                return this;\n            }\n            try {\n                this.emittingRegister.push(emittingTag);\n                for (let i = 0, iEnd = listeners.length; i < iEnd; ++i) {\n                    listeners[i].call(this, e);\n                }\n            }\n            finally {\n                const index = this.emittingRegister.indexOf(emittingTag);\n                if (index >= 0) {\n                    this.emittingRegister.splice(index, 1);\n                }\n            }\n        }\n        return this;\n    }\n    /**\n     * Removes a lasting state cursor.\n     *\n     * @function #remitCursor\n     *\n     * @param {string} tableId\n     * ID of the related cursor table.\n     *\n     * @param {Data.DataCursor.Type} cursor\n     * Copy or reference of the cursor.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    remitCursor(tableId, cursor) {\n        const cursors = (this.stateMap[tableId] &&\n            this.stateMap[tableId][cursor.state]);\n        if (cursors) {\n            const index = DataCursor.getIndex(cursor, cursors);\n            if (index >= 0) {\n                cursors.splice(index, 1);\n            }\n        }\n        return this;\n    }\n    /**\n     * This function removes a listener.\n     *\n     * @function #addListener\n     *\n     * @param {Data.DataCursor.TableId} tableId\n     * The ID of the table the listener is connected to.\n     *\n     * @param {Data.DataCursor.State} state\n     * The state on the table the listener is listening to.\n     *\n     * @param {Data.DataCursor.Listener} listener\n     * The listener to deregister.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    removeListener(tableId, state, listener) {\n        const listeners = (this.listenerMap[tableId] &&\n            this.listenerMap[tableId][state]);\n        if (listeners) {\n            const index = listeners.indexOf(listener);\n            if (index >= 0) {\n                listeners.splice(index, 1);\n            }\n        }\n        return this;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Semantic version string of the DataCursor class.\n * @internal\n */\nDataCursor.version = '1.0.0';\n/* *\n *\n *  Class Namespace\n *\n * */\n/**\n * @class Data.DataCursor\n */\n(function (DataCursor) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Finds the index of an cursor in an array.\n     * @private\n     */\n    function getIndex(needle, cursors) {\n        if (needle.type === 'position') {\n            for (let cursor, i = 0, iEnd = cursors.length; i < iEnd; ++i) {\n                cursor = cursors[i];\n                if (cursor.type === 'position' &&\n                    cursor.state === needle.state &&\n                    cursor.column === needle.column &&\n                    cursor.row === needle.row) {\n                    return i;\n                }\n            }\n        }\n        else {\n            const columnNeedle = JSON.stringify(needle.columns);\n            for (let cursor, i = 0, iEnd = cursors.length; i < iEnd; ++i) {\n                cursor = cursors[i];\n                if (cursor.type === 'range' &&\n                    cursor.state === needle.state &&\n                    cursor.firstRow === needle.firstRow &&\n                    cursor.lastRow === needle.lastRow &&\n                    JSON.stringify(cursor.columns) === columnNeedle) {\n                    return i;\n                }\n            }\n        }\n        return -1;\n    }\n    DataCursor.getIndex = getIndex;\n    /**\n     * Checks whether two cursor share the same properties.\n     * @private\n     */\n    function isEqual(cursorA, cursorB) {\n        if (cursorA.type === 'position' && cursorB.type === 'position') {\n            return (cursorA.column === cursorB.column &&\n                cursorA.row === cursorB.row &&\n                cursorA.state === cursorB.state);\n        }\n        if (cursorA.type === 'range' && cursorB.type === 'range') {\n            return (cursorA.firstRow === cursorB.firstRow &&\n                cursorA.lastRow === cursorB.lastRow &&\n                (JSON.stringify(cursorA.columns) ===\n                    JSON.stringify(cursorB.columns)));\n        }\n        return false;\n    }\n    DataCursor.isEqual = isEqual;\n    /**\n     * Checks whether a cursor is in a range.\n     * @private\n     */\n    function isInRange(needle, range) {\n        if (range.type === 'position') {\n            range = toRange(range);\n        }\n        if (needle.type === 'position') {\n            needle = toRange(needle, range);\n        }\n        const needleColumns = needle.columns;\n        const rangeColumns = range.columns;\n        return (needle.firstRow >= range.firstRow &&\n            needle.lastRow <= range.lastRow &&\n            (!needleColumns ||\n                !rangeColumns ||\n                needleColumns.every((column) => rangeColumns.indexOf(column) >= 0)));\n    }\n    DataCursor.isInRange = isInRange;\n    /**\n     * @private\n     */\n    function toPositions(cursor) {\n        if (cursor.type === 'position') {\n            return [cursor];\n        }\n        const columns = (cursor.columns || []);\n        const positions = [];\n        const state = cursor.state;\n        for (let row = cursor.firstRow, rowEnd = cursor.lastRow; row < rowEnd; ++row) {\n            if (!columns.length) {\n                positions.push({\n                    type: 'position',\n                    row,\n                    state\n                });\n                continue;\n            }\n            for (let column = 0, columnEnd = columns.length; column < columnEnd; ++column) {\n                positions.push({\n                    type: 'position',\n                    column: columns[column],\n                    row,\n                    state\n                });\n            }\n        }\n        return positions;\n    }\n    DataCursor.toPositions = toPositions;\n    /**\n     * @private\n     */\n    function toRange(cursor, defaultRange) {\n        if (cursor.type === 'range') {\n            return cursor;\n        }\n        const range = {\n            type: 'range',\n            firstRow: (cursor.row ??\n                (defaultRange && defaultRange.firstRow) ??\n                0),\n            lastRow: (cursor.row ??\n                (defaultRange && defaultRange.lastRow) ??\n                Number.MAX_VALUE),\n            state: cursor.state\n        };\n        if (typeof cursor.column !== 'undefined') {\n            range.columns = [cursor.column];\n        }\n        return range;\n    }\n    DataCursor.toRange = toRange;\n})(DataCursor || (DataCursor = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataCursor = (DataCursor);\n\n;// ./code/es-modules/Data/DataPoolDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\nconst DataPoolDefaults = {\n    connectors: []\n};\n/* *\n *\n *  Export Defaults\n *\n * */\n/* harmony default export */ const Data_DataPoolDefaults = (DataPoolDefaults);\n\n;// ./code/es-modules/Data/DataPool.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Data pool to load connectors on-demand.\n *\n * @class\n * @name Data.DataPool\n *\n * @param {Data.DataPoolOptions} options\n * Pool options with all connectors.\n */\nclass DataPool {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options = Data_DataPoolDefaults) {\n        options.connectors = (options.connectors || []);\n        this.connectors = {};\n        this.options = options;\n        this.waiting = {};\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Emits an event on this data pool to all registered callbacks of the given\n     * event.\n     * @private\n     *\n     * @param {DataTable.Event} e\n     * Event object with event information.\n     */\n    emit(e) {\n        external_highcharts_src_js_default_default().fireEvent(this, e.type, e);\n    }\n    /**\n     * Loads the connector.\n     *\n     * @function Data.DataPool#getConnector\n     *\n     * @param {string} connectorId\n     * ID of the connector.\n     *\n     * @return {Promise<Data.DataConnectorType>}\n     * Returns the connector.\n     */\n    getConnector(connectorId) {\n        const connector = this.connectors[connectorId];\n        // Already loaded\n        if (connector) {\n            return Promise.resolve(connector);\n        }\n        let waitingList = this.waiting[connectorId];\n        // Start loading\n        if (!waitingList) {\n            waitingList = this.waiting[connectorId] = [];\n            const connectorOptions = this.getConnectorOptions(connectorId);\n            if (!connectorOptions) {\n                throw new Error(`Connector '${connectorId}' not found.`);\n            }\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this\n                .loadConnector(connectorOptions)\n                .then((connector) => {\n                delete this.waiting[connectorId];\n                for (let i = 0, iEnd = waitingList.length; i < iEnd; ++i) {\n                    waitingList[i][0](connector);\n                }\n            })['catch']((error) => {\n                delete this.waiting[connectorId];\n                for (let i = 0, iEnd = waitingList.length; i < iEnd; ++i) {\n                    waitingList[i][1](error);\n                }\n            });\n        }\n        // Add request to waiting list\n        return new Promise((resolve, reject) => {\n            waitingList.push([resolve, reject]);\n        });\n    }\n    /**\n     * Returns the IDs of all connectors.\n     *\n     * @private\n     *\n     * @return {Array<string>}\n     * Names of all connectors.\n     */\n    getConnectorIds() {\n        const connectors = this.options.connectors, connectorIds = [];\n        for (let i = 0, iEnd = connectors.length; i < iEnd; ++i) {\n            connectorIds.push(connectors[i].id);\n        }\n        return connectorIds;\n    }\n    /**\n     * Loads the options of the connector.\n     *\n     * @private\n     *\n     * @param {string} connectorId\n     * ID of the connector.\n     *\n     * @return {DataPoolConnectorOptions|undefined}\n     * Returns the options of the connector, or `undefined` if not found.\n     */\n    getConnectorOptions(connectorId) {\n        const connectors = this.options.connectors;\n        for (let i = 0, iEnd = connectors.length; i < iEnd; ++i) {\n            if (connectors[i].id === connectorId) {\n                return connectors[i];\n            }\n        }\n    }\n    /**\n     * Loads the connector table.\n     *\n     * @function Data.DataPool#getConnectorTable\n     *\n     * @param {string} connectorId\n     * ID of the connector.\n     *\n     * @return {Promise<Data.DataTable>}\n     * Returns the connector table.\n     */\n    getConnectorTable(connectorId) {\n        return this\n            .getConnector(connectorId)\n            .then((connector) => connector.table);\n    }\n    /**\n     * Tests whether the connector has never been requested.\n     *\n     * @param {string} connectorId\n     * Name of the connector.\n     *\n     * @return {boolean}\n     * Returns `true`, if the connector has never been requested, otherwise\n     * `false`.\n     */\n    isNewConnector(connectorId) {\n        return !this.connectors[connectorId];\n    }\n    /**\n     * Creates and loads the connector.\n     *\n     * @private\n     *\n     * @param {Data.DataPoolConnectorOptions} options\n     * Options of connector.\n     *\n     * @return {Promise<Data.DataConnectorType>}\n     * Returns the connector.\n     */\n    loadConnector(options) {\n        return new Promise((resolve, reject) => {\n            this.emit({\n                type: 'load',\n                options\n            });\n            const ConnectorClass = Connectors_DataConnector.types[options.type];\n            if (!ConnectorClass) {\n                throw new Error(`Connector type not found. (${options.type})`);\n            }\n            const connector = new ConnectorClass(options.options);\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            connector\n                .load()\n                .then((connector) => {\n                this.connectors[options.id] = connector;\n                this.emit({\n                    type: 'afterLoad',\n                    options\n                });\n                resolve(connector);\n            })['catch'](reject);\n        });\n    }\n    /**\n     * Registers a callback for a specific event.\n     *\n     * @function Highcharts.DataPool#on\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {Highcharts.EventCallbackFunction<Highcharts.DataPool>} callback\n     * Function to register for an event callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the event.\n     */\n    on(type, callback) {\n        return external_highcharts_src_js_default_default().addEvent(this, type, callback);\n    }\n    /**\n     * Sets connector options under the specified `options.id`.\n     *\n     * @param {Data.DataPoolConnectorOptions} options\n     * Connector options to set.\n     */\n    setConnectorOptions(options) {\n        const connectors = this.options.connectors, instances = this.connectors;\n        this.emit({\n            type: 'setConnectorOptions',\n            options\n        });\n        for (let i = 0, iEnd = connectors.length; i < iEnd; ++i) {\n            if (connectors[i].id === options.id) {\n                connectors.splice(i, 1);\n                break;\n            }\n        }\n        if (instances[options.id]) {\n            instances[options.id].stopPolling();\n            delete instances[options.id];\n        }\n        connectors.push(options);\n        this.emit({\n            type: 'afterSetConnectorOptions',\n            options\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Semantic version string of the DataPool class.\n * @internal\n */\nDataPool.version = '1.0.0';\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataPool = (DataPool);\n\n;// ./code/es-modules/Data/Formula/FormulaParser.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @private\n */\nconst booleanRegExp = /^(?:FALSE|TRUE)/;\n/**\n * `.`-separated decimal.\n * @private\n */\nconst decimal1RegExp = /^[+\\-]?\\d+(?:\\.\\d+)?(?:e[+\\-]\\d+)?/;\n/**\n * `,`-separated decimal.\n * @private\n */\nconst decimal2RegExp = /^[+\\-]?\\d+(?:,\\d+)?(?:e[+\\-]\\d+)?/;\n/**\n * - Group 1: Function name\n * @private\n */\nconst functionRegExp = /^([A-Z][A-Z\\d\\.]*)\\(/;\n/**\n * @private\n */\nconst operatorRegExp = /^(?:[+\\-*\\/^<=>]|<=|=>)/;\n/**\n * - Group 1: Start column\n * - Group 2: Start row\n * - Group 3: End column\n * - Group 4: End row\n * @private\n */\nconst rangeA1RegExp = /^(\\$?[A-Z]+)(\\$?\\d+)\\:(\\$?[A-Z]+)(\\$?\\d+)/;\n/**\n * - Group 1: Start row\n * - Group 2: Start column\n * - Group 3: End row\n * - Group 4: End column\n * @private\n */\nconst rangeR1C1RegExp = /^R(\\d*|\\[\\d+\\])C(\\d*|\\[\\d+\\])\\:R(\\d*|\\[\\d+\\])C(\\d*|\\[\\d+\\])/;\n/**\n * - Group 1: Column\n * - Group 2: Row\n * @private\n */\nconst referenceA1RegExp = /^(\\$?[A-Z]+)(\\$?\\d+)(?![\\:C])/;\n/**\n * - Group 1: Row\n * - Group 2: Column\n * @private\n */\nconst referenceR1C1RegExp = /^R(\\d*|\\[\\d+\\])C(\\d*|\\[\\d+\\])(?!\\:)/;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Extracts the inner string of the most outer parantheses.\n *\n * @private\n *\n * @param {string} text\n * Text string to extract from.\n *\n * @return {string}\n * Extracted parantheses. If not found an exception will be thrown.\n */\nfunction extractParantheses(text) {\n    let parantheseLevel = 0;\n    for (let i = 0, iEnd = text.length, char, parantheseStart = 1; i < iEnd; ++i) {\n        char = text[i];\n        if (char === '(') {\n            if (!parantheseLevel) {\n                parantheseStart = i + 1;\n            }\n            ++parantheseLevel;\n            continue;\n        }\n        if (char === ')') {\n            --parantheseLevel;\n            if (!parantheseLevel) {\n                return text.substring(parantheseStart, i);\n            }\n        }\n    }\n    if (parantheseLevel > 0) {\n        const error = new Error('Incomplete parantheses.');\n        error.name = 'FormulaParseError';\n        throw error;\n    }\n    return '';\n}\n/**\n * Extracts the inner string value.\n *\n * @private\n *\n * @param {string} text\n * Text string to extract from.\n *\n * @return {string}\n * Extracted string. If not found an exception will be thrown.\n */\nfunction extractString(text) {\n    let start = -1;\n    for (let i = 0, iEnd = text.length, char, escaping = false; i < iEnd; ++i) {\n        char = text[i];\n        if (char === '\\\\') {\n            escaping = !escaping;\n            continue;\n        }\n        if (escaping) {\n            escaping = false;\n            continue;\n        }\n        if (char === '\"') {\n            if (start < 0) {\n                start = i;\n            }\n            else {\n                return text.substring(start + 1, i); // `ì` is excluding\n            }\n        }\n    }\n    const error = new Error('Incomplete string.');\n    error.name = 'FormulaParseError';\n    throw error;\n}\n/**\n * Parses an argument string. Formula arrays with a single term will be\n * simplified to the term.\n *\n * @private\n *\n * @param {string} text\n * Argument string to parse.\n *\n * @param {boolean} alternativeSeparators\n * Whether to expect `;` as argument separator and `,` as decimal separator.\n *\n * @return {Formula|Function|Range|Reference|Value}\n * The recognized term structure.\n */\nfunction parseArgument(text, alternativeSeparators) {\n    let match;\n    // Check for a R1C1:R1C1 range notation\n    match = text.match(rangeR1C1RegExp);\n    if (match) {\n        const beginColumnRelative = (match[2] === '' || match[2][0] === '[');\n        const beginRowRelative = (match[1] === '' || match[1][0] === '[');\n        const endColumnRelative = (match[4] === '' || match[4][0] === '[');\n        const endRowRelative = (match[3] === '' || match[3][0] === '[');\n        const range = {\n            type: 'range',\n            beginColumn: (beginColumnRelative ?\n                parseInt(match[2].substring(1, -1) || '0', 10) :\n                parseInt(match[2], 10) - 1),\n            beginRow: (beginRowRelative ?\n                parseInt(match[1].substring(1, -1) || '0', 10) :\n                parseInt(match[1], 10) - 1),\n            endColumn: (endColumnRelative ?\n                parseInt(match[4].substring(1, -1) || '0', 10) :\n                parseInt(match[4], 10) - 1),\n            endRow: (endRowRelative ?\n                parseInt(match[3].substring(1, -1) || '0', 10) :\n                parseInt(match[3], 10) - 1)\n        };\n        if (beginColumnRelative) {\n            range.beginColumnRelative = true;\n        }\n        if (beginRowRelative) {\n            range.beginRowRelative = true;\n        }\n        if (endColumnRelative) {\n            range.endColumnRelative = true;\n        }\n        if (endRowRelative) {\n            range.endRowRelative = true;\n        }\n        return range;\n    }\n    // Check for a A1:A1 range notation\n    match = text.match(rangeA1RegExp);\n    if (match) {\n        const beginColumnRelative = match[1][0] !== '$';\n        const beginRowRelative = match[2][0] !== '$';\n        const endColumnRelative = match[3][0] !== '$';\n        const endRowRelative = match[4][0] !== '$';\n        const range = {\n            type: 'range',\n            beginColumn: parseReferenceColumn(beginColumnRelative ?\n                match[1] :\n                match[1].substring(1)) - 1,\n            beginRow: parseInt(beginRowRelative ?\n                match[2] :\n                match[2].substring(1), 10) - 1,\n            endColumn: parseReferenceColumn(endColumnRelative ?\n                match[3] :\n                match[3].substring(1)) - 1,\n            endRow: parseInt(endRowRelative ?\n                match[4] :\n                match[4].substring(1), 10) - 1\n        };\n        if (beginColumnRelative) {\n            range.beginColumnRelative = true;\n        }\n        if (beginRowRelative) {\n            range.beginRowRelative = true;\n        }\n        if (endColumnRelative) {\n            range.endColumnRelative = true;\n        }\n        if (endRowRelative) {\n            range.endRowRelative = true;\n        }\n        return range;\n    }\n    // Fallback to formula processing for other pattern types\n    const formula = parseFormula(text, alternativeSeparators);\n    return (formula.length === 1 && typeof formula[0] !== 'string' ?\n        formula[0] :\n        formula);\n}\n/**\n * Parse arguments string inside function parantheses.\n *\n * @private\n *\n * @param {string} text\n * Parantheses string of the function.\n *\n * @param {boolean} alternativeSeparators\n * Whether to expect `;` as argument separator and `,` as decimal separator.\n *\n * @return {Highcharts.FormulaArguments}\n * Parsed arguments array.\n */\nfunction parseArguments(text, alternativeSeparators) {\n    const args = [], argumentsSeparator = (alternativeSeparators ? ';' : ',');\n    let parantheseLevel = 0, term = '';\n    for (let i = 0, iEnd = text.length, char; i < iEnd; ++i) {\n        char = text[i];\n        // Check for separator\n        if (char === argumentsSeparator &&\n            !parantheseLevel &&\n            term) {\n            args.push(parseArgument(term, alternativeSeparators));\n            term = '';\n            // Check for a quoted string before skip logic\n        }\n        else if (char === '\"' &&\n            !parantheseLevel &&\n            !term) {\n            const string = extractString(text.substring(i));\n            args.push(string);\n            i += string.length + 1; // Only +1 to cover ++i in for-loop\n            // Skip space and check paranthesis nesting\n        }\n        else if (char !== ' ') {\n            term += char;\n            if (char === '(') {\n                ++parantheseLevel;\n            }\n            else if (char === ')') {\n                --parantheseLevel;\n            }\n        }\n    }\n    // Look for left-overs from last argument\n    if (!parantheseLevel && term) {\n        args.push(parseArgument(term, alternativeSeparators));\n    }\n    return args;\n}\n/**\n * Converts a spreadsheet formula string into a formula array. Throws a\n * `FormulaParserError` when the string can not be parsed.\n *\n * @private\n * @function Formula.parseFormula\n *\n * @param {string} text\n * Spreadsheet formula string, without the leading `=`.\n *\n * @param {boolean} alternativeSeparators\n * * `false` to expect `,` between arguments and `.` in decimals.\n * * `true` to expect `;` between arguments and `,` in decimals.\n *\n * @return {Formula.Formula}\n * Formula array representing the string.\n */\nfunction parseFormula(text, alternativeSeparators) {\n    const decimalRegExp = (alternativeSeparators ?\n        decimal2RegExp :\n        decimal1RegExp), formula = [];\n    let match, next = (text[0] === '=' ? text.substring(1) : text).trim();\n    while (next) {\n        // Check for an R1C1 reference notation\n        match = next.match(referenceR1C1RegExp);\n        if (match) {\n            const columnRelative = (match[2] === '' || match[2][0] === '[');\n            const rowRelative = (match[1] === '' || match[1][0] === '[');\n            const reference = {\n                type: 'reference',\n                column: (columnRelative ?\n                    parseInt(match[2].substring(1, -1) || '0', 10) :\n                    parseInt(match[2], 10) - 1),\n                row: (rowRelative ?\n                    parseInt(match[1].substring(1, -1) || '0', 10) :\n                    parseInt(match[1], 10) - 1)\n            };\n            if (columnRelative) {\n                reference.columnRelative = true;\n            }\n            if (rowRelative) {\n                reference.rowRelative = true;\n            }\n            formula.push(reference);\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for an A1 reference notation\n        match = next.match(referenceA1RegExp);\n        if (match) {\n            const columnRelative = match[1][0] !== '$';\n            const rowRelative = match[2][0] !== '$';\n            const reference = {\n                type: 'reference',\n                column: parseReferenceColumn(columnRelative ?\n                    match[1] :\n                    match[1].substring(1)) - 1,\n                row: parseInt(rowRelative ?\n                    match[2] :\n                    match[2].substring(1), 10) - 1\n            };\n            if (columnRelative) {\n                reference.columnRelative = true;\n            }\n            if (rowRelative) {\n                reference.rowRelative = true;\n            }\n            formula.push(reference);\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a formula operator\n        match = next.match(operatorRegExp);\n        if (match) {\n            formula.push(match[0]);\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a boolean value\n        match = next.match(booleanRegExp);\n        if (match) {\n            formula.push(match[0] === 'TRUE');\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a number value\n        match = next.match(decimalRegExp);\n        if (match) {\n            formula.push(parseFloat(match[0]));\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a quoted string\n        if (next[0] === '\"') {\n            const string = extractString(next);\n            formula.push(string.substring(1, -1));\n            next = next.substring(string.length + 2).trim();\n            continue;\n        }\n        // Check for a function\n        match = next.match(functionRegExp);\n        if (match) {\n            next = next.substring(match[1].length).trim();\n            const parantheses = extractParantheses(next);\n            formula.push({\n                type: 'function',\n                name: match[1],\n                args: parseArguments(parantheses, alternativeSeparators)\n            });\n            next = next.substring(parantheses.length + 2).trim();\n            continue;\n        }\n        // Check for a formula in parantheses\n        if (next[0] === '(') {\n            const paranteses = extractParantheses(next);\n            if (paranteses) {\n                formula\n                    .push(parseFormula(paranteses, alternativeSeparators));\n                next = next.substring(paranteses.length + 2).trim();\n                continue;\n            }\n        }\n        // Something is not right\n        const position = text.length - next.length, error = new Error('Unexpected character `' +\n            text.substring(position, position + 1) +\n            '` at position ' + (position + 1) +\n            '. (`...' + text.substring(position - 5, position + 6) + '...`)');\n        error.name = 'FormulaParseError';\n        throw error;\n    }\n    return formula;\n}\n/**\n * Converts a reference column `A` of `A1` into a number. Supports endless sizes\n * `ZZZ...`, just limited by integer precision.\n *\n * @private\n *\n * @param {string} text\n * Column string to convert.\n *\n * @return {number}\n * Converted column index.\n */\nfunction parseReferenceColumn(text) {\n    let column = 0;\n    for (let i = 0, iEnd = text.length, code, factor = text.length - 1; i < iEnd; ++i) {\n        code = text.charCodeAt(i);\n        if (code >= 65 && code <= 90) {\n            column += (code - 64) * Math.pow(26, factor);\n        }\n        --factor;\n    }\n    return column;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst FormulaParser = {\n    parseFormula\n};\n/* harmony default export */ const Formula_FormulaParser = (FormulaParser);\n\n;// ./code/es-modules/Data/Formula/FormulaTypes.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Array of all possible operators.\n * @private\n */\nconst operators = ['+', '-', '*', '/', '^', '=', '<', '<=', '>', '>='];\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Tests an item for a Formula array.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a formula (or argument) array.\n */\nfunction isFormula(item) {\n    return item instanceof Array;\n}\n/**\n * Tests an item for a Function structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a formula function.\n */\nfunction isFunction(item) {\n    return (typeof item === 'object' &&\n        !(item instanceof Array) &&\n        item.type === 'function');\n}\n/**\n * Tests an item for an Operator string.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is an operator string.\n */\nfunction isOperator(item) {\n    return (typeof item === 'string' &&\n        operators.indexOf(item) >= 0);\n}\n/**\n * Tests an item for a Range structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a range.\n */\nfunction isRange(item) {\n    return (typeof item === 'object' &&\n        !(item instanceof Array) &&\n        item.type === 'range');\n}\n/**\n * Tests an item for a Reference structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a reference.\n */\nfunction isReference(item) {\n    return (typeof item === 'object' &&\n        !(item instanceof Array) &&\n        item.type === 'reference');\n}\n/**\n * Tests an item for a Value structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem|null|undefined} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a value.\n */\nfunction isValue(item) {\n    return (typeof item === 'boolean' ||\n        typeof item === 'number' ||\n        typeof item === 'string');\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst MathFormula = {\n    isFormula,\n    isFunction,\n    isOperator,\n    isRange,\n    isReference,\n    isValue\n};\n/* harmony default export */ const FormulaTypes = (MathFormula);\n\n;// ./code/es-modules/Data/Formula/FormulaProcessor.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { isFormula: FormulaProcessor_isFormula, isFunction: FormulaProcessor_isFunction, isOperator: FormulaProcessor_isOperator, isRange: FormulaProcessor_isRange, isReference: FormulaProcessor_isReference, isValue: FormulaProcessor_isValue } = FormulaTypes;\n/* *\n *\n *  Constants\n *\n * */\nconst asLogicalStringRegExp = / */;\nconst MAX_FALSE = Number.MAX_VALUE / 1.000000000001;\nconst MAX_STRING = Number.MAX_VALUE / 1.000000000002;\nconst MAX_TRUE = Number.MAX_VALUE;\nconst operatorPriority = {\n    '^': 3,\n    '*': 2,\n    '/': 2,\n    '+': 1,\n    '-': 1,\n    '=': 0,\n    '<': 0,\n    '<=': 0,\n    '>': 0,\n    '>=': 0\n};\nconst processorFunctions = {};\nconst processorFunctionNameRegExp = /^[A-Z][A-Z\\.]*$/;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Converts non-number types to logical numbers.\n *\n * @param {Highcharts.FormulaValue} value\n * Value to convert.\n *\n * @return {number}\n * Logical number value. `NaN` if not convertable.\n */\nfunction asLogicalNumber(value) {\n    switch (typeof value) {\n        case 'boolean':\n            return value ? MAX_TRUE : MAX_FALSE;\n        case 'string':\n            return MAX_STRING;\n        case 'number':\n            return value;\n        default:\n            return NaN;\n    }\n}\n/**\n * Converts strings to logical strings, while other types get passed through. In\n * logical strings the space character is the lowest value and letters are case\n * insensitive.\n *\n * @param {Highcharts.FormulaValue} value\n * Value to convert.\n *\n * @return {Highcharts.FormulaValue}\n * Logical string value or passed through value.\n */\nfunction asLogicalString(value) {\n    if (typeof value === 'string') {\n        return value.toLowerCase().replace(asLogicalStringRegExp, '\\0');\n    }\n    return value;\n}\n/**\n * Converts non-number types to a logic number.\n *\n * @param {Highcharts.FormulaValue} value\n * Value to convert.\n *\n * @return {number}\n * Number value. `NaN` if not convertable.\n */\nfunction asNumber(value) {\n    switch (typeof value) {\n        case 'boolean':\n            return value ? 1 : 0;\n        case 'string':\n            return parseFloat(value.replace(',', '.'));\n        case 'number':\n            return value;\n        default:\n            return NaN;\n    }\n}\n/**\n * Process a basic operation of two given values.\n *\n * @private\n *\n * @param {Highcharts.FormulaOperator} operator\n * Operator between values.\n *\n * @param {Highcharts.FormulaValue} x\n * First value for operation.\n *\n * @param {Highcharts.FormulaValue} y\n * Second value for operation.\n *\n * @return {Highcharts.FormulaValue}\n * Operation result. `NaN` if operation is not support.\n */\nfunction basicOperation(operator, x, y) {\n    switch (operator) {\n        case '=':\n            return asLogicalString(x) === asLogicalString(y);\n        case '<':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) < asLogicalString(y);\n            }\n            return asLogicalNumber(x) < asLogicalNumber(y);\n        case '<=':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) <= asLogicalString(y);\n            }\n            return asLogicalNumber(x) <= asLogicalNumber(y);\n        case '>':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) > asLogicalString(y);\n            }\n            return asLogicalNumber(x) > asLogicalNumber(y);\n        case '>=':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) >= asLogicalString(y);\n            }\n            return asLogicalNumber(x) >= asLogicalNumber(y);\n    }\n    x = asNumber(x);\n    y = asNumber(y);\n    let result;\n    switch (operator) {\n        case '+':\n            result = x + y;\n            break;\n        case '-':\n            result = x - y;\n            break;\n        case '*':\n            result = x * y;\n            break;\n        case '/':\n            result = x / y;\n            break;\n        case '^':\n            result = Math.pow(x, y);\n            break;\n        default:\n            return NaN;\n    }\n    // Limit decimal to 9 digits\n    return (result % 1 ?\n        Math.round(result * 1000000000) / 1000000000 :\n        result);\n}\n/**\n * Converts an argument to Value and in case of a range to an array of Values.\n *\n * @function Highcharts.Formula.getArgumentValue\n *\n * @param {Highcharts.FormulaRange|Highcharts.FormulaTerm} arg\n * Formula range or term to convert.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Highcharts.FormulaValue|Array<Highcharts.FormulaValue>}\n * Converted value.\n */\nfunction getArgumentValue(arg, table) {\n    // Add value\n    if (FormulaProcessor_isValue(arg)) {\n        return arg;\n    }\n    // Add values of a range\n    if (FormulaProcessor_isRange(arg)) {\n        return (table && getRangeValues(arg, table) || []);\n    }\n    // Add values of a function\n    if (FormulaProcessor_isFunction(arg)) {\n        return processFunction(arg, table);\n    }\n    // Process functions, operations, references with formula processor\n    return processFormula((FormulaProcessor_isFormula(arg) ? arg : [arg]), table);\n}\n/**\n * Converts all arguments to Values and in case of ranges to arrays of Values.\n *\n * @function Highcharts.Formula.getArgumentsValues\n *\n * @param {Highcharts.FormulaArguments} args\n * Formula arguments to convert.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Array<(Highcharts.FormulaValue|Array<Highcharts.FormulaValue>)>}\n * Converted values.\n */\nfunction getArgumentsValues(args, table) {\n    const values = [];\n    for (let i = 0, iEnd = args.length; i < iEnd; ++i) {\n        values.push(getArgumentValue(args[i], table));\n    }\n    return values;\n}\n/**\n * Extracts cell values from a table for a given range.\n *\n * @function Highcharts.Formula.getRangeValues\n *\n * @param {Highcharts.FormulaRange} range\n * Formula range to use.\n *\n * @param {Highcharts.DataTable} table\n * Table to extract from.\n *\n * @return {Array<Highcharts.FormulaValue>}\n * Extracted values.\n */\nfunction getRangeValues(range, table) {\n    const columnNames = table\n        .getColumnNames()\n        .slice(range.beginColumn, range.endColumn + 1), values = [];\n    for (let i = 0, iEnd = columnNames.length, cell; i < iEnd; ++i) {\n        const cells = table.getColumn(columnNames[i], true) || [];\n        for (let j = range.beginRow, jEnd = range.endRow + 1; j < jEnd; ++j) {\n            cell = cells[j];\n            if (typeof cell === 'string' &&\n                cell[0] === '=' &&\n                table !== table.modified) {\n                // Look in the modified table for formula result\n                cell = table.modified.getCell(columnNames[i], j);\n            }\n            values.push(FormulaProcessor_isValue(cell) ? cell : NaN);\n        }\n    }\n    return values;\n}\n/**\n * Extracts the cell value from a table for a given reference.\n *\n * @private\n *\n * @param {Highcharts.FormulaReference} reference\n * Formula reference to use.\n *\n * @param {Highcharts.DataTable} table\n * Table to extract from.\n *\n * @return {Highcharts.FormulaValue}\n * Extracted value. 'undefined' might also indicate that the cell was not found.\n */\nfunction getReferenceValue(reference, table) {\n    const columnName = table.getColumnNames()[reference.column];\n    if (columnName) {\n        const cell = table.getCell(columnName, reference.row);\n        if (typeof cell === 'string' &&\n            cell[0] === '=' &&\n            table !== table.modified) {\n            // Look in the modified table for formula result\n            const result = table.modified.getCell(columnName, reference.row);\n            return FormulaProcessor_isValue(result) ? result : NaN;\n        }\n        return FormulaProcessor_isValue(cell) ? cell : NaN;\n    }\n    return NaN;\n}\n/**\n * Processes a formula array on the given table. If the formula does not contain\n * references or ranges, then no table has to be provided.\n *\n * @private\n * @function Highcharts.processFormula\n *\n * @param {Highcharts.Formula} formula\n * Formula array to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Highcharts.FormulaValue}\n * Result value of the process. `NaN` indicates an error.\n */\nfunction processFormula(formula, table) {\n    let x;\n    for (let i = 0, iEnd = formula.length, item, operator, result, y; i < iEnd; ++i) {\n        item = formula[i];\n        // Remember operator for operation on next item\n        if (FormulaProcessor_isOperator(item)) {\n            operator = item;\n            continue;\n        }\n        // Next item is a value\n        if (FormulaProcessor_isValue(item)) {\n            y = item;\n            // Next item is a formula and needs to get processed first\n        }\n        else if (FormulaProcessor_isFormula(item)) {\n            y = processFormula(formula, table);\n            // Next item is a function call and needs to get processed first\n        }\n        else if (FormulaProcessor_isFunction(item)) {\n            result = processFunction(item, table);\n            y = (FormulaProcessor_isValue(result) ? result : NaN); // Arrays are not allowed here\n            // Next item is a reference and needs to get resolved\n        }\n        else if (FormulaProcessor_isReference(item)) {\n            y = (table && getReferenceValue(item, table));\n        }\n        // If we have a next value, lets do the operation\n        if (typeof y !== 'undefined') {\n            // Next value is our first value\n            if (typeof x === 'undefined') {\n                if (operator) {\n                    x = basicOperation(operator, 0, y);\n                }\n                else {\n                    x = y;\n                }\n                // Fail fast if no operator available\n            }\n            else if (!operator) {\n                return NaN;\n                // Regular next value\n            }\n            else {\n                const operator2 = formula[i + 1];\n                if (FormulaProcessor_isOperator(operator2) &&\n                    operatorPriority[operator2] > operatorPriority[operator]) {\n                    y = basicOperation(operator2, y, processFormula(formula.slice(i + 2)));\n                    i = iEnd;\n                }\n                x = basicOperation(operator, x, y);\n            }\n            operator = void 0;\n            y = void 0;\n        }\n    }\n    return FormulaProcessor_isValue(x) ? x : NaN;\n}\n/**\n * Process a function on the given table. If the arguments do not contain\n * references or ranges, then no table has to be provided.\n *\n * @private\n *\n * @param {Highcharts.FormulaFunction} formulaFunction\n * Formula function to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @param {Highcharts.FormulaReference} [reference]\n * Table cell reference to use for relative references and ranges.\n *\n * @return {Highcharts.FormulaValue|Array<Highcharts.FormulaValue>}\n * Result value (or values) of the process. `NaN` indicates an error.\n */\nfunction processFunction(formulaFunction, table, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nreference // @todo\n) {\n    const processor = processorFunctions[formulaFunction.name];\n    if (processor) {\n        try {\n            return processor(formulaFunction.args, table);\n        }\n        catch {\n            return NaN;\n        }\n    }\n    const error = new Error(`Function \"${formulaFunction.name}\" not found.`);\n    error.name = 'FormulaProcessError';\n    throw error;\n}\n/**\n * Registers a function for the FormulaProcessor.\n *\n * @param {string} name\n * Name of the function in spreadsheets notation with upper case.\n *\n * @param {Highcharts.FormulaFunction} processorFunction\n * ProcessorFunction for the FormulaProcessor. This is an object so that it\n * can take additional parameter for future validation routines.\n *\n * @return {boolean}\n * Return true, if the ProcessorFunction has been registered.\n */\nfunction registerProcessorFunction(name, processorFunction) {\n    return (processorFunctionNameRegExp.test(name) &&\n        !processorFunctions[name] &&\n        !!(processorFunctions[name] = processorFunction));\n}\n/**\n * Translates relative references and ranges in-place.\n *\n * @param {Highcharts.Formula} formula\n * Formula to translate references and ranges in.\n *\n * @param {number} [columnDelta=0]\n * Column delta to translate to. Negative translate back.\n *\n * @param {number} [rowDelta=0]\n * Row delta to translate to. Negative numbers translate back.\n *\n * @return {Highcharts.Formula}\n * Formula with translated reference and ranges. This formula is equal to the\n * first argument.\n */\nfunction translateReferences(formula, columnDelta = 0, rowDelta = 0) {\n    for (let i = 0, iEnd = formula.length, item; i < iEnd; ++i) {\n        item = formula[i];\n        if (item instanceof Array) {\n            translateReferences(item, columnDelta, rowDelta);\n        }\n        else if (FormulaProcessor_isFunction(item)) {\n            translateReferences(item.args, columnDelta, rowDelta);\n        }\n        else if (FormulaProcessor_isRange(item)) {\n            if (item.beginColumnRelative) {\n                item.beginColumn += columnDelta;\n            }\n            if (item.beginRowRelative) {\n                item.beginRow += rowDelta;\n            }\n            if (item.endColumnRelative) {\n                item.endColumn += columnDelta;\n            }\n            if (item.endRowRelative) {\n                item.endRow += rowDelta;\n            }\n        }\n        else if (FormulaProcessor_isReference(item)) {\n            if (item.columnRelative) {\n                item.column += columnDelta;\n            }\n            if (item.rowRelative) {\n                item.row += rowDelta;\n            }\n        }\n    }\n    return formula;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst FormulaProcessor = {\n    asNumber,\n    getArgumentValue,\n    getArgumentsValues,\n    getRangeValues,\n    getReferenceValue,\n    processFormula,\n    processorFunctions,\n    registerProcessorFunction,\n    translateReferences\n};\n/* harmony default export */ const Formula_FormulaProcessor = (FormulaProcessor);\n\n;// ./code/es-modules/Data/Formula/Functions/ABS.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: ABS_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `ABS(value)` implementation. Returns positive numbers.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Array<number>}\n * Result value of the process.\n */\nfunction ABS(args, table) {\n    const value = ABS_getArgumentValue(args[0], table);\n    switch (typeof value) {\n        case 'number':\n            return Math.abs(value);\n        case 'object': {\n            const values = [];\n            for (let i = 0, iEnd = value.length, value2; i < iEnd; ++i) {\n                value2 = value[i];\n                if (typeof value2 !== 'number') {\n                    return NaN;\n                }\n                values.push(Math.abs(value2));\n            }\n            return values;\n        }\n        default:\n            return NaN;\n    }\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('ABS', ABS);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_ABS = ((/* unused pure expression or super */ null && (ABS)));\n\n;// ./code/es-modules/Data/Formula/Functions/AND.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: AND_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `AND(...tests)` implementation. Returns `TRUE`, if all test\n * results are not `0` or `FALSE`.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean}\n * Result value of the process.\n */\nfunction AND(args, table) {\n    for (let i = 0, iEnd = args.length, value; i < iEnd; ++i) {\n        value = AND_getArgumentValue(args[i], table);\n        if (!value ||\n            (typeof value === 'object' &&\n                !AND(value, table))) {\n            return false;\n        }\n    }\n    return true;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('AND', AND);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_AND = ((/* unused pure expression or super */ null && (AND)));\n\n;// ./code/es-modules/Data/Formula/Functions/AVERAGE.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentsValues: AVERAGE_getArgumentsValues } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `AVERAGE(...values)` implementation. Calculates the average\n * of the given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.AVERAGE\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction AVERAGE(args, table) {\n    const values = AVERAGE_getArgumentsValues(args, table);\n    let count = 0, result = 0;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    ++count;\n                    result += value;\n                }\n                break;\n            case 'object':\n                for (let j = 0, jEnd = value.length, value2; j < jEnd; ++j) {\n                    value2 = value[j];\n                    if (typeof value2 === 'number' &&\n                        !isNaN(value2)) {\n                        ++count;\n                        result += value2;\n                    }\n                }\n                break;\n        }\n    }\n    return (count ? (result / count) : 0);\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('AVERAGE', AVERAGE);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_AVERAGE = ((/* unused pure expression or super */ null && (AVERAGE)));\n\n;// ./code/es-modules/Data/Formula/Functions/AVERAGEA.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: AVERAGEA_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `AVERAGEA(...values)` implementation. Calculates the\n * average of the given values. Strings and FALSE are calculated as 0.\n *\n * @private\n * @function Formula.processorFunctions.AVERAGEA\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction AVERAGEA(args, table) {\n    let count = 0, result = 0;\n    for (let i = 0, iEnd = args.length, value; i < iEnd; ++i) {\n        value = AVERAGEA_getArgumentValue(args[i], table);\n        switch (typeof value) {\n            case 'boolean':\n                ++count;\n                result += (value ? 1 : 0);\n                continue;\n            case 'number':\n                if (!isNaN(value)) {\n                    ++count;\n                    result += value;\n                }\n                continue;\n            case 'string':\n                ++count;\n                continue;\n            default:\n                for (let j = 0, jEnd = value.length, value2; j < jEnd; ++j) {\n                    value2 = value[j];\n                    switch (typeof value2) {\n                        case 'boolean':\n                            ++count;\n                            result += (value2 ? 1 : 0);\n                            continue;\n                        case 'number':\n                            if (!isNaN(value2)) {\n                                ++count;\n                                result += value2;\n                            }\n                            continue;\n                        case 'string':\n                            ++count;\n                            continue;\n                    }\n                }\n                continue;\n        }\n    }\n    return (count ? (result / count) : 0);\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('AVERAGEA', AVERAGEA);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_AVERAGEA = ((/* unused pure expression or super */ null && (AVERAGEA)));\n\n;// ./code/es-modules/Data/Formula/Functions/COUNT.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `COUNT(...values)` implementation. Returns the count of\n * given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.COUNT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction COUNT(args, table) {\n    const values = Formula_FormulaProcessor.getArgumentsValues(args, table);\n    let count = 0;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    ++count;\n                }\n                break;\n            case 'object':\n                count += COUNT(value, table);\n                break;\n        }\n    }\n    return count;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('COUNT', COUNT);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_COUNT = ((/* unused pure expression or super */ null && (COUNT)));\n\n;// ./code/es-modules/Data/Formula/Functions/COUNTA.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `COUNTA(...values)` implementation. Returns the count of\n * given values that are not empty.\n *\n * @private\n * @function Formula.processorFunctions.COUNT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction COUNTA(args, table) {\n    const values = Formula_FormulaProcessor.getArgumentsValues(args, table);\n    let count = 0;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (isNaN(value)) {\n                    continue;\n                }\n                break;\n            case 'object':\n                count += COUNTA(value, table);\n                continue;\n            case 'string':\n                if (!value) {\n                    continue;\n                }\n                break;\n        }\n        ++count;\n    }\n    return count;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('COUNTA', COUNTA);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_COUNTA = ((/* unused pure expression or super */ null && (COUNTA)));\n\n;// ./code/es-modules/Data/Formula/Functions/IF.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: IF_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `IF(test, value1, value2)` implementation. Returns one of\n * the values based on the test result. `value1` will be returned, if the test\n * result is not `0` or `FALSE`.\n *\n * @private\n * @function Formula.processorFunctions.IF\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Highcharts.FormulaValue|Array<Highcharts.FormulaValue>}\n * Result value of the process.\n */\nfunction IF(args, table) {\n    return (IF_getArgumentValue(args[0], table) ?\n        IF_getArgumentValue(args[1], table) :\n        IF_getArgumentValue(args[2], table));\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('IF', IF);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_IF = ((/* unused pure expression or super */ null && (IF)));\n\n;// ./code/es-modules/Data/Formula/Functions/ISNA.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: ISNA_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `ISNA(value)` implementation. Returns TRUE if value is not\n * a number.\n *\n * @private\n * @function Formula.processorFunctions.ISNA\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean}\n * Result value of the process.\n */\nfunction ISNA(args, table) {\n    const value = ISNA_getArgumentValue(args[0], table);\n    return (typeof value !== 'number' || isNaN(value));\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('ISNA', ISNA);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_ISNA = ((/* unused pure expression or super */ null && (ISNA)));\n\n;// ./code/es-modules/Data/Formula/Functions/MAX.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentsValues: MAX_getArgumentsValues } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MAX(...values)` implementation. Calculates the largest\n * of the given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.MAX\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MAX(args, table) {\n    const values = MAX_getArgumentsValues(args, table);\n    let result = Number.NEGATIVE_INFINITY;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (value > result) {\n                    result = value;\n                }\n                break;\n            case 'object':\n                value = MAX(value);\n                if (value > result) {\n                    result = value;\n                }\n                break;\n        }\n    }\n    return isFinite(result) ? result : 0;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MAX', MAX);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_MAX = ((/* unused pure expression or super */ null && (MAX)));\n\n;// ./code/es-modules/Data/Formula/Functions/MEDIAN.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MEDIAN(...values)` implementation. Calculates the median\n * average of the given values.\n *\n * @private\n * @function Formula.processorFunctions.MEDIAN\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MEDIAN(args, table) {\n    const median = [], values = Formula_FormulaProcessor.getArgumentsValues(args, table);\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    median.push(value);\n                }\n                break;\n            case 'object':\n                for (let j = 0, jEnd = value.length, value2; j < jEnd; ++j) {\n                    value2 = value[j];\n                    if (typeof value2 === 'number' &&\n                        !isNaN(value2)) {\n                        median.push(value2);\n                    }\n                }\n                break;\n        }\n    }\n    const count = median.length;\n    if (!count) {\n        return NaN;\n    }\n    const half = Math.floor(count / 2); // Floor because index starts at 0\n    return (count % 2 ?\n        median[half] : // Odd\n        (median[half - 1] + median[half]) / 2 // Even\n    );\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MEDIAN', MEDIAN);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_MEDIAN = ((/* unused pure expression or super */ null && (MEDIAN)));\n\n;// ./code/es-modules/Data/Formula/Functions/MIN.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentsValues: MIN_getArgumentsValues } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MIN(...values)` implementation. Calculates the lowest\n * of the given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.MIN\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MIN(args, table) {\n    const values = MIN_getArgumentsValues(args, table);\n    let result = Number.POSITIVE_INFINITY;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (value < result) {\n                    result = value;\n                }\n                break;\n            case 'object':\n                value = MIN(value);\n                if (value < result) {\n                    result = value;\n                }\n                break;\n        }\n    }\n    return isFinite(result) ? result : 0;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MIN', MIN);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_MIN = ((/* unused pure expression or super */ null && (MIN)));\n\n;// ./code/es-modules/Data/Formula/Functions/MOD.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: MOD_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MOD(value1, value2)` implementation. Calculates the rest\n * of the division with the given values.\n *\n * @private\n * @function Formula.processorFunctions.MOD\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MOD(args, table) {\n    let value1 = MOD_getArgumentValue(args[0], table), value2 = MOD_getArgumentValue(args[1], table);\n    if (typeof value1 === 'object') {\n        value1 = value1[0];\n    }\n    if (typeof value2 === 'object') {\n        value2 = value2[0];\n    }\n    if (typeof value1 !== 'number' ||\n        typeof value2 !== 'number' ||\n        value2 === 0) {\n        return NaN;\n    }\n    return value1 % value2;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MOD', MOD);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_MOD = ((/* unused pure expression or super */ null && (MOD)));\n\n;// ./code/es-modules/Data/Formula/Functions/MODE.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Creates the mode map of the given arguments.\n *\n * @private\n * @function Formula.processorFunctions.MULT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction getModeMap(args, table) {\n    const modeMap = {}, values = Formula_FormulaProcessor.getArgumentsValues(args, table);\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    modeMap[value] = (modeMap[value] || 0) + 1;\n                }\n                break;\n            case 'object':\n                for (let j = 0, jEnd = value.length, value2; j < jEnd; ++j) {\n                    value2 = value[j];\n                    if (typeof value2 === 'number' &&\n                        !isNaN(value2)) {\n                        modeMap[value2] = (modeMap[value2] || 0) + 1;\n                    }\n                }\n                break;\n        }\n    }\n    return modeMap;\n}\n/**\n * Processor for the `MODE.MULT(...values)` implementation. Calculates the most\n * frequent values of the give values.\n *\n * @private\n * @function Formula.processorFunctions.MULT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number|Array<number>}\n * Result value of the process.\n */\nfunction MULT(args, table) {\n    const modeMap = getModeMap(args, table), keys = Object.keys(modeMap);\n    if (!keys.length) {\n        return NaN;\n    }\n    let modeKeys = [parseFloat(keys[0])], modeCount = modeMap[keys[0]];\n    for (let i = 1, iEnd = keys.length, key, count; i < iEnd; ++i) {\n        key = keys[i];\n        count = modeMap[key];\n        if (modeCount < count) {\n            modeKeys = [parseFloat(key)];\n            modeCount = count;\n        }\n        else if (modeCount === count) {\n            modeKeys.push(parseFloat(key));\n        }\n    }\n    return modeCount > 1 ? modeKeys : NaN;\n}\n/**\n * Processor for the `MODE.SNGL(...values)` implementation. Calculates the\n * lowest most frequent value of the give values.\n *\n * @private\n * @function Formula.processorFunctions['MODE.SNGL']\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction SNGL(args, table) {\n    const modeMap = getModeMap(args, table), keys = Object.keys(modeMap);\n    if (!keys.length) {\n        return NaN;\n    }\n    let modeKey = parseFloat(keys[0]), modeCount = modeMap[keys[0]];\n    for (let i = 1, iEnd = keys.length, key, keyValue, count; i < iEnd; ++i) {\n        key = keys[i];\n        count = modeMap[key];\n        if (modeCount < count) {\n            modeKey = parseFloat(key);\n            modeCount = count;\n        }\n        else if (modeCount === count) {\n            keyValue = parseFloat(key);\n            if (modeKey > keyValue) {\n                modeKey = keyValue;\n                modeCount = count;\n            }\n        }\n    }\n    return modeCount > 1 ? modeKey : NaN;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MODE', SNGL);\nFormula_FormulaProcessor.registerProcessorFunction('MODE.MULT', MULT);\nFormula_FormulaProcessor.registerProcessorFunction('MODE.SNGL', SNGL);\n/* *\n *\n *  Default Export\n *\n * */\nconst MODE = {\n    MULT,\n    SNGL\n};\n/* harmony default export */ const Functions_MODE = ((/* unused pure expression or super */ null && (MODE)));\n\n;// ./code/es-modules/Data/Formula/Functions/NOT.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: NOT_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `NOT(value)` implementation. Returns the opposite test\n * result.\n *\n * @private\n * @function Formula.processorFunctions.NOT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean|number}\n * Result value of the process.\n */\nfunction NOT(args, table) {\n    let value = NOT_getArgumentValue(args[0], table);\n    if (typeof value === 'object') {\n        value = value[0];\n    }\n    switch (typeof value) {\n        case 'boolean':\n        case 'number':\n            return !value;\n    }\n    return NaN;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('NOT', NOT);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_NOT = ((/* unused pure expression or super */ null && (NOT)));\n\n;// ./code/es-modules/Data/Formula/Functions/OR.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: OR_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `OR(...tests)` implementation. Returns `TRUE`, if one test\n * result is not `0` or `FALSE`.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean}\n * Result value of the process.\n */\nfunction OR(args, table) {\n    for (let i = 0, iEnd = args.length, value; i < iEnd; ++i) {\n        value = OR_getArgumentValue(args[i], table);\n        if (typeof value === 'object') {\n            if (OR(value, table)) {\n                return true;\n            }\n        }\n        else if (value) {\n            return true;\n        }\n    }\n    return false;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('OR', OR);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_OR = ((/* unused pure expression or super */ null && (OR)));\n\n;// ./code/es-modules/Data/Formula/Functions/PRODUCT.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentsValues: PRODUCT_getArgumentsValues } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `PRODUCT(...values)` implementation. Calculates the product\n * of the given values.\n *\n * @private\n * @function Formula.processorFunctions.PRODUCT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction PRODUCT(args, table) {\n    const values = PRODUCT_getArgumentsValues(args, table);\n    let result = 1, calculated = false;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    calculated = true;\n                    result *= value;\n                }\n                break;\n            case 'object':\n                calculated = true;\n                result *= PRODUCT(value, table);\n                break;\n        }\n    }\n    return (calculated ? result : 0);\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('PRODUCT', PRODUCT);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_PRODUCT = ((/* unused pure expression or super */ null && (PRODUCT)));\n\n;// ./code/es-modules/Data/Formula/Functions/SUM.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `SUM(...values)` implementation. Calculates the sum of the\n * given values.\n *\n * @private\n * @function Formula.processorFunctions.SUM\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction SUM(args, table) {\n    const values = Formula_FormulaProcessor.getArgumentsValues(args, table);\n    let result = 0;\n    for (let i = 0, iEnd = values.length, value; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    result += value;\n                }\n                break;\n            case 'object':\n                result += SUM(value, table);\n                break;\n        }\n    }\n    return result;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('SUM', SUM); // 🐝\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_SUM = ((/* unused pure expression or super */ null && (SUM)));\n\n;// ./code/es-modules/Data/Formula/Functions/XOR.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nconst { getArgumentValue: XOR_getArgumentValue } = Formula_FormulaProcessor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `XOR(...tests)` implementation. Returns `TRUE`, if at least\n * one of the given tests differs in result of other tests.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean|number}\n * Result value of the process.\n */\nfunction XOR(args, table) {\n    for (let i = 0, iEnd = args.length, lastValue, value; i < iEnd; ++i) {\n        value = XOR_getArgumentValue(args[i], table);\n        switch (typeof value) {\n            case 'boolean':\n            case 'number':\n                if (typeof lastValue === 'undefined') {\n                    lastValue = !!value;\n                }\n                else if (!!value !== lastValue) {\n                    return true;\n                }\n                break;\n            case 'object':\n                for (let j = 0, jEnd = value.length, value2; j < jEnd; ++j) {\n                    value2 = value[j];\n                    switch (typeof value2) {\n                        case 'boolean':\n                        case 'number':\n                            if (typeof lastValue === 'undefined') {\n                                lastValue = !!value2;\n                            }\n                            else if (!!value2 !== lastValue) {\n                                return true;\n                            }\n                            break;\n                    }\n                }\n                break;\n        }\n    }\n    return false;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('XOR', XOR);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Functions_XOR = ((/* unused pure expression or super */ null && (XOR)));\n\n;// ./code/es-modules/Data/Formula/Formula.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* *\n *\n *  Default Export\n *\n * */\n/**\n * Formula engine to make use of spreadsheet formula strings.\n * @internal\n */\nconst Formula = {\n    ...Formula_FormulaParser,\n    ...Formula_FormulaProcessor,\n    ...FormulaTypes\n};\n/* harmony default export */ const Formula_Formula = (Formula);\n\n;// ./code/es-modules/Data/Converters/CSVConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Christer Vasseng\n *  - Gøran Slettemark\n *  - Sophie Bremer\n *\n * */\n\n\n\nconst { merge: CSVConverter_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transforming CSV to a table.\n *\n * @private\n */\nclass CSVConverter extends Converters_DataConverter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the CSV parser.\n     *\n     * @param {CSVConverter.UserOptions} [options]\n     * Options for the CSV parser.\n     */\n    constructor(options) {\n        const mergedOptions = CSVConverter_merge(CSVConverter.defaultOptions, options);\n        super(mergedOptions);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.columns = [];\n        this.headers = [];\n        this.dataTypes = [];\n        this.options = mergedOptions;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Creates a CSV string from the datatable on the connector instance.\n     *\n     * @param {DataConnector} connector\n     * Connector instance to export from.\n     *\n     * @param {CSVConverter.Options} [options]\n     * Options used for the export.\n     *\n     * @return {string}\n     * CSV string from the connector table.\n     */\n    export(connector, options = this.options) {\n        const { useLocalDecimalPoint, lineDelimiter } = options, exportNames = (this.options.firstRowAsNames !== false);\n        let { decimalPoint, itemDelimiter } = options;\n        if (!decimalPoint) {\n            decimalPoint = (itemDelimiter !== ',' && useLocalDecimalPoint ?\n                (1.1).toLocaleString()[1] :\n                '.');\n        }\n        if (!itemDelimiter) {\n            itemDelimiter = (decimalPoint === ',' ? ';' : ',');\n        }\n        const columns = connector.getSortedColumns(options.usePresentationOrder), columnNames = Object.keys(columns), csvRows = [], columnsCount = columnNames.length;\n        const rowArray = [];\n        // Add the names as the first row if they should be exported\n        if (exportNames) {\n            csvRows.push(columnNames.map((columnName) => `\"${columnName}\"`).join(itemDelimiter));\n        }\n        for (let columnIndex = 0; columnIndex < columnsCount; columnIndex++) {\n            const columnName = columnNames[columnIndex], column = columns[columnName], columnLength = column.length;\n            const columnMeta = connector.whatIs(columnName);\n            let columnDataType;\n            if (columnMeta) {\n                columnDataType = columnMeta.dataType;\n            }\n            for (let rowIndex = 0; rowIndex < columnLength; rowIndex++) {\n                let cellValue = column[rowIndex];\n                if (!rowArray[rowIndex]) {\n                    rowArray[rowIndex] = [];\n                }\n                // Prefer datatype from metadata\n                if (columnDataType === 'string') {\n                    cellValue = '\"' + cellValue + '\"';\n                }\n                else if (typeof cellValue === 'number') {\n                    cellValue = String(cellValue).replace('.', decimalPoint);\n                }\n                else if (typeof cellValue === 'string') {\n                    cellValue = `\"${cellValue}\"`;\n                }\n                rowArray[rowIndex][columnIndex] = cellValue;\n                // On the final column, push the row to the CSV\n                if (columnIndex === columnsCount - 1) {\n                    // Trim repeated undefined values starting at the end\n                    // Currently, we export the first \"comma\" even if the\n                    // second value is undefined\n                    let i = columnIndex;\n                    while (rowArray[rowIndex].length > 2) {\n                        const cellVal = rowArray[rowIndex][i];\n                        if (cellVal !== void 0) {\n                            break;\n                        }\n                        rowArray[rowIndex].pop();\n                        i--;\n                    }\n                    csvRows.push(rowArray[rowIndex].join(itemDelimiter));\n                }\n            }\n        }\n        return csvRows.join(lineDelimiter);\n    }\n    /**\n     * Initiates parsing of CSV\n     *\n     * @param {CSVConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits CSVDataParser#parse\n     * @emits CSVDataParser#afterParse\n     */\n    parse(options, eventDetail) {\n        const converter = this, dataTypes = converter.dataTypes, parserOptions = CSVConverter_merge(this.options, options), { beforeParse, lineDelimiter, firstRowAsNames, itemDelimiter } = parserOptions;\n        let lines, rowIt = 0, { csv, startRow, endRow } = parserOptions, column;\n        converter.columns = [];\n        converter.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n        if (csv && beforeParse) {\n            csv = beforeParse(csv);\n        }\n        if (csv) {\n            lines = csv\n                .replace(/\\r\\n|\\r/g, '\\n') // Windows | Mac\n                .split(lineDelimiter || '\\n');\n            if (!startRow || startRow < 0) {\n                startRow = 0;\n            }\n            if (!endRow || endRow >= lines.length) {\n                endRow = lines.length - 1;\n            }\n            if (!itemDelimiter) {\n                converter.guessedItemDelimiter =\n                    converter.guessDelimiter(lines);\n            }\n            // If the first row contain names, add them to the\n            // headers array and skip the row.\n            if (firstRowAsNames) {\n                const headers = lines[0].split(itemDelimiter || converter.guessedItemDelimiter || ',');\n                // Remove \"\"s from the headers\n                for (let i = 0; i < headers.length; i++) {\n                    headers[i] = headers[i].trim().replace(/^[\"']|[\"']$/g, '');\n                }\n                converter.headers = headers;\n                startRow++;\n            }\n            let offset = 0;\n            for (rowIt = startRow; rowIt <= endRow; rowIt++) {\n                if (lines[rowIt][0] === '#') {\n                    offset++;\n                }\n                else {\n                    converter\n                        .parseCSVRow(lines[rowIt], rowIt - startRow - offset);\n                }\n            }\n            if (dataTypes.length &&\n                dataTypes[0].length &&\n                dataTypes[0][1] === 'date' && // Format is a string date\n                !converter.options.dateFormat) {\n                converter.deduceDateFormat(converter.columns[0], null, true);\n            }\n            // Guess types.\n            for (let i = 0, iEnd = converter.columns.length; i < iEnd; ++i) {\n                column = converter.columns[i];\n                for (let j = 0, jEnd = column.length; j < jEnd; ++j) {\n                    if (column[j] && typeof column[j] === 'string') {\n                        let cellValue = converter.asGuessedType(column[j]);\n                        if (cellValue instanceof Date) {\n                            cellValue = cellValue.getTime();\n                        }\n                        converter.columns[i][j] = cellValue;\n                    }\n                }\n            }\n        }\n        converter.emit({\n            type: 'afterParse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n    }\n    /**\n     * Internal method that parses a single CSV row\n     */\n    parseCSVRow(columnStr, rowNumber) {\n        const converter = this, columns = converter.columns || [], dataTypes = converter.dataTypes, { startColumn, endColumn } = converter.options, itemDelimiter = (converter.options.itemDelimiter ||\n            converter.guessedItemDelimiter);\n        let { decimalPoint } = converter.options;\n        if (!decimalPoint || decimalPoint === itemDelimiter) {\n            decimalPoint = converter.guessedDecimalPoint || '.';\n        }\n        let i = 0, c = '', token = '', actualColumn = 0, column = 0;\n        const read = (j) => {\n            c = columnStr[j];\n        };\n        const pushType = (type) => {\n            if (dataTypes.length < column + 1) {\n                dataTypes.push([type]);\n            }\n            if (dataTypes[column][dataTypes[column].length - 1] !== type) {\n                dataTypes[column].push(type);\n            }\n        };\n        const push = () => {\n            if (startColumn > actualColumn || actualColumn > endColumn) {\n                // Skip this column, but increment the column count (#7272)\n                ++actualColumn;\n                token = '';\n                return;\n            }\n            // Save the type of the token.\n            if (typeof token === 'string') {\n                if (!isNaN(parseFloat(token)) && isFinite(token)) {\n                    token = parseFloat(token);\n                    pushType('number');\n                }\n                else if (!isNaN(Date.parse(token))) {\n                    token = token.replace(/\\//g, '-');\n                    pushType('date');\n                }\n                else {\n                    pushType('string');\n                }\n            }\n            else {\n                pushType('number');\n            }\n            if (columns.length < column + 1) {\n                columns.push([]);\n            }\n            // Try to apply the decimal point, and check if the token then is a\n            // number. If not, reapply the initial value\n            if (typeof token !== 'number' &&\n                converter.guessType(token) !== 'number' &&\n                decimalPoint) {\n                const initialValue = token;\n                token = token.replace(decimalPoint, '.');\n                if (converter.guessType(token) !== 'number') {\n                    token = initialValue;\n                }\n            }\n            columns[column][rowNumber] = token;\n            token = '';\n            ++column;\n            ++actualColumn;\n        };\n        if (!columnStr.trim().length) {\n            return;\n        }\n        if (columnStr.trim()[0] === '#') {\n            return;\n        }\n        for (; i < columnStr.length; i++) {\n            read(i);\n            if (c === '#') {\n                // If there are hexvalues remaining (#13283)\n                if (!/^#[A-F\\d]{3,3}|[A-F\\d]{6,6}/i.test(columnStr.substring(i))) {\n                    // The rest of the row is a comment\n                    push();\n                    return;\n                }\n            }\n            // Quoted string\n            if (c === '\"') {\n                read(++i);\n                while (i < columnStr.length) {\n                    if (c === '\"') {\n                        break;\n                    }\n                    token += c;\n                    read(++i);\n                }\n            }\n            else if (c === itemDelimiter) {\n                push();\n                // Actual column data\n            }\n            else {\n                token += c;\n            }\n        }\n        push();\n    }\n    /**\n     * Internal method that guesses the delimiter from the first\n     * 13 lines of the CSV\n     * @param {Array<string>} lines\n     * The CSV, split into lines\n     */\n    guessDelimiter(lines) {\n        let points = 0, commas = 0, guessed;\n        const potDelimiters = {\n            ',': 0,\n            ';': 0,\n            '\\t': 0\n        }, linesCount = lines.length;\n        for (let i = 0; i < linesCount; i++) {\n            let inStr = false, c, cn, cl, token = '';\n            // We should be able to detect dateformats within 13 rows\n            if (i > 13) {\n                break;\n            }\n            const columnStr = lines[i];\n            for (let j = 0; j < columnStr.length; j++) {\n                c = columnStr[j];\n                cn = columnStr[j + 1];\n                cl = columnStr[j - 1];\n                if (c === '#') {\n                    // Skip the rest of the line - it's a comment\n                    break;\n                }\n                if (c === '\"') {\n                    if (inStr) {\n                        if (cl !== '\"' && cn !== '\"') {\n                            while (cn === ' ' && j < columnStr.length) {\n                                cn = columnStr[++j];\n                            }\n                            // After parsing a string, the next non-blank\n                            // should be a delimiter if the CSV is properly\n                            // formed.\n                            if (typeof potDelimiters[cn] !== 'undefined') {\n                                potDelimiters[cn]++;\n                            }\n                            inStr = false;\n                        }\n                    }\n                    else {\n                        inStr = true;\n                    }\n                }\n                else if (typeof potDelimiters[c] !== 'undefined') {\n                    token = token.trim();\n                    if (!isNaN(Date.parse(token))) {\n                        potDelimiters[c]++;\n                    }\n                    else if (isNaN(Number(token)) ||\n                        !isFinite(Number(token))) {\n                        potDelimiters[c]++;\n                    }\n                    token = '';\n                }\n                else {\n                    token += c;\n                }\n                if (c === ',') {\n                    commas++;\n                }\n                if (c === '.') {\n                    points++;\n                }\n            }\n        }\n        // Count the potential delimiters.\n        // This could be improved by checking if the number of delimiters\n        // equals the number of columns - 1\n        if (potDelimiters[';'] > potDelimiters[',']) {\n            guessed = ';';\n        }\n        else if (potDelimiters[','] > potDelimiters[';']) {\n            guessed = ',';\n        }\n        else {\n            // No good guess could be made..\n            guessed = ',';\n        }\n        // Try to deduce the decimal point if it's not explicitly set.\n        // If both commas or points is > 0 there is likely an issue\n        if (points > commas) {\n            this.guessedDecimalPoint = '.';\n        }\n        else {\n            this.guessedDecimalPoint = ',';\n        }\n        return guessed;\n    }\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed CSV.\n     */\n    getTable() {\n        return Converters_DataConverter.getTableFromColumns(this.columns, this.headers);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options\n */\nCSVConverter.defaultOptions = {\n    ...Converters_DataConverter.defaultOptions,\n    lineDelimiter: '\\n'\n};\nConverters_DataConverter.registerType('CSV', CSVConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Converters_CSVConverter = (CSVConverter);\n\n;// ./code/es-modules/Data/Connectors/CSVConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Christer Vasseng\n *  - Gøran Slettemark\n *  - Sophie Bremer\n *\n * */\n\n\n\n\nconst { merge: CSVConnector_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class that handles creating a DataConnector from CSV\n *\n * @private\n */\nclass CSVConnector extends Connectors_DataConnector {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of CSVConnector.\n     *\n     * @param {CSVConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    constructor(options) {\n        const mergedOptions = CSVConnector_merge(CSVConnector.defaultOptions, options);\n        super(mergedOptions);\n        this.converter = new Converters_CSVConverter(mergedOptions);\n        this.options = mergedOptions;\n        if (mergedOptions.enablePolling) {\n            this.startPolling(Math.max(mergedOptions.dataRefreshRate || 0, 1) * 1000);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates the loading of the CSV source to the connector\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits CSVConnector#load\n     * @emits CSVConnector#afterLoad\n     */\n    load(eventDetail) {\n        const connector = this, converter = connector.converter, table = connector.table, { csv, csvURL, dataModifier } = connector.options;\n        connector.emit({\n            type: 'load',\n            csv,\n            detail: eventDetail,\n            table\n        });\n        return Promise\n            .resolve(csvURL ?\n            fetch(csvURL).then((response) => response.text()) :\n            csv || '')\n            .then((csv) => {\n            if (csv) {\n                // If already loaded, clear the current rows\n                table.deleteColumns();\n                converter.parse({ csv });\n                table.setColumns(converter.getTable().getColumns());\n            }\n            return connector\n                .setModifierOptions(dataModifier)\n                .then(() => csv);\n        })\n            .then((csv) => {\n            connector.emit({\n                type: 'afterLoad',\n                csv,\n                detail: eventDetail,\n                table\n            });\n            return connector;\n        })['catch']((error) => {\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error,\n                table\n            });\n            throw error;\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nCSVConnector.defaultOptions = {\n    csv: '',\n    csvURL: '',\n    enablePolling: false,\n    dataRefreshRate: 1,\n    firstRowAsNames: true\n};\nConnectors_DataConnector.registerType('CSV', CSVConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Connectors_CSVConnector = ((/* unused pure expression or super */ null && (CSVConnector)));\n\n;// ./code/es-modules/Data/Converters/JSONConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n\n\nconst { error, isArray, merge: JSONConverter_merge, objectEach: JSONConverter_objectEach } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transforming JSON to a table.\n *\n * @private\n */\nclass JSONConverter extends Converters_DataConverter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the JSON parser.\n     *\n     * @param {JSONConverter.UserOptions} [options]\n     * Options for the JSON parser.\n     */\n    constructor(options) {\n        const mergedOptions = JSONConverter_merge(JSONConverter.defaultOptions, options);\n        super(mergedOptions);\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.columns = [];\n        this.headers = [];\n        this.options = mergedOptions;\n        this.table = new Data_DataTable();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates parsing of JSON structure.\n     *\n     * @param {JSONConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits JSONConverter#parse\n     * @emits JSONConverter#afterParse\n     */\n    parse(options, eventDetail) {\n        const converter = this;\n        options = JSONConverter_merge(converter.options, options);\n        const { beforeParse, orientation, firstRowAsNames, columnNames } = options;\n        let data = options.data;\n        if (!data) {\n            return;\n        }\n        converter.columns = [];\n        converter.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n        if (beforeParse) {\n            data = beforeParse(data);\n        }\n        data = data.slice();\n        if (orientation === 'columns') {\n            for (let i = 0, iEnd = data.length; i < iEnd; i++) {\n                const item = data[i];\n                if (!(item instanceof Array)) {\n                    return;\n                }\n                if (converter.headers instanceof Array) {\n                    if (firstRowAsNames) {\n                        converter.headers.push(`${item.shift()}`);\n                    }\n                    else if (columnNames && columnNames instanceof Array) {\n                        converter.headers.push(columnNames[i]);\n                    }\n                    converter.table.setColumn(converter.headers[i] || i.toString(), item);\n                }\n                else {\n                    error('JSONConverter: Invalid `columnNames` option.', false);\n                }\n            }\n        }\n        else if (orientation === 'rows') {\n            if (firstRowAsNames) {\n                converter.headers = data.shift();\n            }\n            else if (columnNames) {\n                converter.headers = columnNames;\n            }\n            for (let rowIndex = 0, iEnd = data.length; rowIndex < iEnd; rowIndex++) {\n                let row = data[rowIndex];\n                if (isArray(row)) {\n                    for (let columnIndex = 0, jEnd = row.length; columnIndex < jEnd; columnIndex++) {\n                        if (converter.columns.length < columnIndex + 1) {\n                            converter.columns.push([]);\n                        }\n                        converter.columns[columnIndex].push(row[columnIndex]);\n                        if (converter.headers instanceof Array) {\n                            this.table.setColumn(converter.headers[columnIndex] ||\n                                columnIndex.toString(), converter.columns[columnIndex]);\n                        }\n                        else {\n                            error('JSONConverter: Invalid `columnNames` option.', false);\n                        }\n                    }\n                }\n                else {\n                    const columnNames = converter.headers;\n                    if (columnNames && !(columnNames instanceof Array)) {\n                        const newRow = {};\n                        JSONConverter_objectEach(columnNames, (arrayWithPath, name) => {\n                            newRow[name] = arrayWithPath.reduce((acc, key) => acc[key], row);\n                        });\n                        row = newRow;\n                    }\n                    this.table.setRows([row], rowIndex);\n                }\n            }\n        }\n        converter.emit({\n            type: 'afterParse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n    }\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed CSV.\n     */\n    getTable() {\n        return this.table;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options\n */\nJSONConverter.defaultOptions = {\n    ...Converters_DataConverter.defaultOptions,\n    data: [],\n    orientation: 'rows'\n};\nConverters_DataConverter.registerType('JSON', JSONConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Converters_JSONConverter = (JSONConverter);\n\n;// ./code/es-modules/Data/Connectors/JSONConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n\n\nconst { merge: JSONConnector_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class that handles creating a DataConnector from JSON structure\n *\n * @private\n */\nclass JSONConnector extends Connectors_DataConnector {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of JSONConnector.\n     *\n     * @param {JSONConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    constructor(options) {\n        const mergedOptions = JSONConnector_merge(JSONConnector.defaultOptions, options);\n        super(mergedOptions);\n        this.converter = new Converters_JSONConverter(mergedOptions);\n        this.options = mergedOptions;\n        if (mergedOptions.enablePolling) {\n            this.startPolling(Math.max(mergedOptions.dataRefreshRate || 0, 1) * 1000);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates the loading of the JSON source to the connector\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits JSONConnector#load\n     * @emits JSONConnector#afterLoad\n     */\n    load(eventDetail) {\n        const connector = this, converter = connector.converter, table = connector.table, { data, dataUrl, dataModifier } = connector.options;\n        connector.emit({\n            type: 'load',\n            data,\n            detail: eventDetail,\n            table\n        });\n        return Promise\n            .resolve(dataUrl ?\n            fetch(dataUrl).then((response) => response.json())['catch']((error) => {\n                connector.emit({\n                    type: 'loadError',\n                    detail: eventDetail,\n                    error,\n                    table\n                });\n                console.warn(`Unable to fetch data from ${dataUrl}.`); // eslint-disable-line no-console\n            }) :\n            data || [])\n            .then((data) => {\n            if (data) {\n                // If already loaded, clear the current rows\n                table.deleteColumns();\n                converter.parse({ data });\n                table.setColumns(converter.getTable().getColumns());\n            }\n            return connector.setModifierOptions(dataModifier).then(() => data);\n        })\n            .then((data) => {\n            connector.emit({\n                type: 'afterLoad',\n                data,\n                detail: eventDetail,\n                table\n            });\n            return connector;\n        })['catch']((error) => {\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error,\n                table\n            });\n            throw error;\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nJSONConnector.defaultOptions = {\n    data: [],\n    enablePolling: false,\n    dataRefreshRate: 0,\n    firstRowAsNames: true,\n    orientation: 'rows'\n};\nConnectors_DataConnector.registerType('JSON', JSONConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Connectors_JSONConnector = ((/* unused pure expression or super */ null && (JSONConnector)));\n\n;// ./code/es-modules/Data/Converters/GoogleSheetsConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *\n * */\n\n\n\nconst { merge: GoogleSheetsConverter_merge, uniqueKey: GoogleSheetsConverter_uniqueKey } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transformation of an Google Sheets to a table.\n *\n * @private\n */\nclass GoogleSheetsConverter extends Converters_DataConverter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the GoogleSheetsConverter.\n     *\n     * @param {GoogleSheetsConverter.UserOptions} [options]\n     * Options for the GoogleSheetsConverter.\n     */\n    constructor(options) {\n        const mergedOptions = GoogleSheetsConverter_merge(GoogleSheetsConverter.defaultOptions, options);\n        super(mergedOptions);\n        this.columns = [];\n        this.header = [];\n        this.options = mergedOptions;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates the parsing of the Google Sheet\n     *\n     * @param {GoogleSheetsConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits GoogleSheetsParser#parse\n     * @emits GoogleSheetsParser#afterParse\n     */\n    parse(options, eventDetail) {\n        const converter = this, parseOptions = GoogleSheetsConverter_merge(converter.options, options);\n        let columns = ((parseOptions.json?.values) || []).map((column) => column.slice());\n        if (columns.length === 0) {\n            return false;\n        }\n        converter.header = [];\n        converter.columns = [];\n        converter.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.header\n        });\n        // If beforeParse is defined, use it to modify the data\n        const { beforeParse, json } = parseOptions;\n        if (beforeParse && json) {\n            columns = beforeParse(json.values);\n        }\n        let column;\n        converter.columns = columns;\n        for (let i = 0, iEnd = columns.length; i < iEnd; i++) {\n            column = columns[i];\n            converter.header[i] = (parseOptions.firstRowAsNames ?\n                `${column.shift()}` :\n                GoogleSheetsConverter_uniqueKey());\n            for (let j = 0, jEnd = column.length; j < jEnd; ++j) {\n                if (column[j] && typeof column[j] === 'string') {\n                    let cellValue = converter.asGuessedType(column[j]);\n                    if (cellValue instanceof Date) {\n                        cellValue = cellValue.getTime();\n                    }\n                    converter.columns[i][j] = cellValue;\n                }\n            }\n        }\n        converter.emit({\n            type: 'afterParse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.header\n        });\n    }\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed Google Sheet\n     */\n    getTable() {\n        return Converters_DataConverter.getTableFromColumns(this.columns, this.header);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options\n */\nGoogleSheetsConverter.defaultOptions = {\n    ...Converters_DataConverter.defaultOptions\n};\nConverters_DataConverter.registerType('GoogleSheets', GoogleSheetsConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Converters_GoogleSheetsConverter = (GoogleSheetsConverter);\n\n;// ./code/es-modules/Data/Connectors/GoogleSheetsConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *  - Jomar Hønsi\n *\n * */\n\n\n\n\nconst { merge: GoogleSheetsConnector_merge, pick: GoogleSheetsConnector_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Tests Google's response for error.\n * @private\n */\nfunction isGoogleError(json) {\n    return (typeof json === 'object' && json &&\n        typeof json.error === 'object' && json.error &&\n        typeof json.error.code === 'number' &&\n        typeof json.error.message === 'string' &&\n        typeof json.error.status === 'string');\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @todo implement save, requires oauth2\n */\nclass GoogleSheetsConnector extends Connectors_DataConnector {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of GoogleSheetsConnector\n     *\n     * @param {GoogleSheetsConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    constructor(options) {\n        const mergedOptions = GoogleSheetsConnector_merge(GoogleSheetsConnector.defaultOptions, options);\n        super(mergedOptions);\n        this.converter = new Converters_GoogleSheetsConverter(mergedOptions);\n        this.options = mergedOptions;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Loads data from a Google Spreadsheet.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<this>}\n     * Same connector instance with modified table.\n     */\n    load(eventDetail) {\n        const connector = this, converter = connector.converter, table = connector.table, { dataModifier, dataRefreshRate, enablePolling, firstRowAsNames, googleAPIKey, googleSpreadsheetKey } = connector.options, url = GoogleSheetsConnector.buildFetchURL(googleAPIKey, googleSpreadsheetKey, connector.options);\n        connector.emit({\n            type: 'load',\n            detail: eventDetail,\n            table,\n            url\n        });\n        if (!URL.canParse(url)) {\n            throw new Error('Invalid URL: ' + url);\n        }\n        return fetch(url)\n            .then((response) => (response.json()))\n            .then((json) => {\n            if (isGoogleError(json)) {\n                throw new Error(json.error.message);\n            }\n            converter.parse({\n                firstRowAsNames,\n                json\n            });\n            // If already loaded, clear the current table\n            table.deleteColumns();\n            table.setColumns(converter.getTable().getColumns());\n            return connector.setModifierOptions(dataModifier);\n        })\n            .then(() => {\n            connector.emit({\n                type: 'afterLoad',\n                detail: eventDetail,\n                table,\n                url\n            });\n            // Polling\n            if (enablePolling) {\n                setTimeout(() => connector.load(), Math.max(dataRefreshRate || 0, 1) * 1000);\n            }\n            return connector;\n        })['catch']((error) => {\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error,\n                table\n            });\n            throw error;\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nGoogleSheetsConnector.defaultOptions = {\n    googleAPIKey: '',\n    googleSpreadsheetKey: '',\n    enablePolling: false,\n    dataRefreshRate: 2,\n    firstRowAsNames: true\n};\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (GoogleSheetsConnector) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Creates GoogleSheets API v4 URL.\n     * @private\n     */\n    function buildFetchURL(apiKey, sheetKey, options = {}) {\n        const url = new URL(`https://sheets.googleapis.com/v4/spreadsheets/${sheetKey}/values/`);\n        const range = options.onlyColumnNames ?\n            'A1:Z1' : buildQueryRange(options);\n        url.pathname += range;\n        const searchParams = url.searchParams;\n        searchParams.set('alt', 'json');\n        if (!options.onlyColumnNames) {\n            searchParams.set('dateTimeRenderOption', 'FORMATTED_STRING');\n            searchParams.set('majorDimension', 'COLUMNS');\n            searchParams.set('valueRenderOption', 'UNFORMATTED_VALUE');\n        }\n        searchParams.set('prettyPrint', 'false');\n        searchParams.set('key', apiKey);\n        return url.href;\n    }\n    GoogleSheetsConnector.buildFetchURL = buildFetchURL;\n    /**\n     * Creates sheets range.\n     * @private\n     */\n    function buildQueryRange(options = {}) {\n        const { endColumn, endRow, googleSpreadsheetRange, startColumn, startRow } = options;\n        return googleSpreadsheetRange || ((alphabet[startColumn || 0] || 'A') +\n            (Math.max((startRow || 0), 0) + 1) +\n            ':' +\n            (alphabet[GoogleSheetsConnector_pick(endColumn, 25)] || 'Z') +\n            (endRow ?\n                Math.max(endRow, 0) :\n                'Z'));\n    }\n    GoogleSheetsConnector.buildQueryRange = buildQueryRange;\n})(GoogleSheetsConnector || (GoogleSheetsConnector = {}));\nConnectors_DataConnector.registerType('GoogleSheets', GoogleSheetsConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Connectors_GoogleSheetsConnector = ((/* unused pure expression or super */ null && (GoogleSheetsConnector)));\n\n;// ./code/es-modules/Data/Converters/HTMLTableConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *\n * */\n\n\n\nconst { merge: HTMLTableConverter_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Row equal\n */\nfunction isRowEqual(row1, row2) {\n    let i = row1.length;\n    if (row2.length === i) {\n        while (--i) {\n            if (row1[i] !== row2[i]) {\n                return false;\n            }\n        }\n    }\n    else {\n        return false;\n    }\n    return true;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transformation of an HTML table to a table.\n *\n * @private\n */\nclass HTMLTableConverter extends Converters_DataConverter {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the HTMLTableConverter.\n     *\n     * @param {HTMLTableConverter.UserOptions} [options]\n     * Options for the HTMLTableConverter.\n     */\n    constructor(options) {\n        const mergedOptions = HTMLTableConverter_merge(HTMLTableConverter.defaultOptions, options);\n        super(mergedOptions);\n        this.columns = [];\n        this.headers = [];\n        this.options = mergedOptions;\n        if (mergedOptions.tableElement) {\n            this.tableElement = mergedOptions.tableElement;\n            this.tableElementID = mergedOptions.tableElement.id;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Exports the dataconnector as an HTML string, using the options\n     * provided on import unless other options are provided.\n     *\n     * @param {DataConnector} connector\n     * Connector instance to export from.\n     *\n     * @param {HTMLTableConnector.ExportOptions} [options]\n     * Options that override default or existing export options.\n     *\n     * @return {string}\n     * HTML from the current dataTable.\n     */\n    export(connector, options = this.options) {\n        const exportNames = (options.firstRowAsNames !== false), useMultiLevelHeaders = options.useMultiLevelHeaders;\n        const columns = connector.getSortedColumns(options.usePresentationOrder), columnNames = Object.keys(columns), htmlRows = [], columnsCount = columnNames.length;\n        const rowArray = [];\n        let tableHead = '';\n        // Add the names as the first row if they should be exported\n        if (exportNames) {\n            const subcategories = [];\n            // If using multilevel headers, the first value\n            // of each column is a subcategory\n            if (useMultiLevelHeaders) {\n                for (const name of columnNames) {\n                    let column = columns[name];\n                    if (!Array.isArray(column)) {\n                        // Convert to conventional array from typed array\n                        // if needed\n                        column = Array.from(column);\n                    }\n                    const subhead = (column.shift() || '').toString();\n                    columns[name] = column;\n                    subcategories.push(subhead);\n                }\n                tableHead = this.getTableHeaderHTML(columnNames, subcategories, options);\n            }\n            else {\n                tableHead = this.getTableHeaderHTML(void 0, columnNames, options);\n            }\n        }\n        for (let columnIndex = 0; columnIndex < columnsCount; columnIndex++) {\n            const columnName = columnNames[columnIndex], column = columns[columnName], columnLength = column.length;\n            for (let rowIndex = 0; rowIndex < columnLength; rowIndex++) {\n                let cellValue = column[rowIndex];\n                if (!rowArray[rowIndex]) {\n                    rowArray[rowIndex] = [];\n                }\n                // Alternative: Datatype from HTML attribute with\n                // connector.whatIs(columnName)\n                if (!(typeof cellValue === 'string' ||\n                    typeof cellValue === 'number' ||\n                    typeof cellValue === 'undefined')) {\n                    cellValue = (cellValue || '').toString();\n                }\n                rowArray[rowIndex][columnIndex] = this.getCellHTMLFromValue(columnIndex ? 'td' : 'th', null, columnIndex ? '' : 'scope=\"row\"', cellValue);\n                // On the final column, push the row to the array\n                if (columnIndex === columnsCount - 1) {\n                    htmlRows.push('<tr>' +\n                        rowArray[rowIndex].join('') +\n                        '</tr>');\n                }\n            }\n        }\n        let caption = '';\n        // Add table caption\n        // Current exportdata falls back to chart title\n        // but that should probably be handled elsewhere?\n        if (options.tableCaption) {\n            caption = '<caption class=\"highcharts-table-caption\">' +\n                options.tableCaption +\n                '</caption>';\n        }\n        return ('<table>' +\n            caption +\n            tableHead +\n            '<tbody>' +\n            htmlRows.join('') +\n            '</tbody>' +\n            '</table>');\n    }\n    /**\n     * Get table cell markup from row data.\n     */\n    getCellHTMLFromValue(tag, classes, attrs, value, decimalPoint) {\n        let val = value, className = 'text' + (classes ? ' ' + classes : '');\n        // Convert to string if number\n        if (typeof val === 'number') {\n            val = val.toString();\n            if (decimalPoint === ',') {\n                val = val.replace('.', decimalPoint);\n            }\n            className = 'number';\n        }\n        else if (!value) {\n            val = '';\n            className = 'empty';\n        }\n        return '<' + tag + (attrs ? ' ' + attrs : '') +\n            ' class=\"' + className + '\">' +\n            val + '</' + tag + '>';\n    }\n    /**\n     * Get table header markup from row data.\n     */\n    getTableHeaderHTML(topheaders = [], subheaders = [], options = this.options) {\n        const { useMultiLevelHeaders, useRowspanHeaders } = options;\n        let html = '<thead>', i = 0, len = subheaders && subheaders.length, next, cur, curColspan = 0, rowspan;\n        // Clean up multiple table headers. Chart.getDataRows() returns two\n        // levels of headers when using multilevel, not merged. We need to\n        // merge identical headers, remove redundant headers, and keep it\n        // all marked up nicely.\n        if (useMultiLevelHeaders &&\n            topheaders &&\n            subheaders &&\n            !isRowEqual(topheaders, subheaders)) {\n            html += '<tr>';\n            for (; i < len; ++i) {\n                cur = topheaders[i];\n                next = topheaders[i + 1];\n                if (cur === next) {\n                    ++curColspan;\n                }\n                else if (curColspan) {\n                    // Ended colspan\n                    // Add cur to HTML with colspan.\n                    html += this.getCellHTMLFromValue('th', 'highcharts-table-topheading', 'scope=\"col\" ' +\n                        'colspan=\"' + (curColspan + 1) + '\"', cur);\n                    curColspan = 0;\n                }\n                else {\n                    // Cur is standalone. If it is same as sublevel,\n                    // remove sublevel and add just toplevel.\n                    if (cur === subheaders[i]) {\n                        if (useRowspanHeaders) {\n                            rowspan = 2;\n                            delete subheaders[i];\n                        }\n                        else {\n                            rowspan = 1;\n                            subheaders[i] = '';\n                        }\n                    }\n                    else {\n                        rowspan = 1;\n                    }\n                    html += this.getCellHTMLFromValue('th', 'highcharts-table-topheading', 'scope=\"col\"' +\n                        (rowspan > 1 ?\n                            ' valign=\"top\" rowspan=\"' + rowspan + '\"' :\n                            ''), cur);\n                }\n            }\n            html += '</tr>';\n        }\n        // Add the subheaders (the only headers if not using multilevels)\n        if (subheaders) {\n            html += '<tr>';\n            for (i = 0, len = subheaders.length; i < len; ++i) {\n                if (typeof subheaders[i] !== 'undefined') {\n                    html += this.getCellHTMLFromValue('th', null, 'scope=\"col\"', subheaders[i]);\n                }\n            }\n            html += '</tr>';\n        }\n        html += '</thead>';\n        return html;\n    }\n    /**\n     * Initiates the parsing of the HTML table\n     *\n     * @param {HTMLTableConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits CSVDataParser#parse\n     * @emits CSVDataParser#afterParse\n     * @emits HTMLTableParser#parseError\n     */\n    parse(options, eventDetail) {\n        const converter = this, columns = [], headers = [], parseOptions = HTMLTableConverter_merge(converter.options, options), { endRow, startColumn, endColumn, firstRowAsNames } = parseOptions, tableHTML = parseOptions.tableElement || this.tableElement;\n        if (!(tableHTML instanceof HTMLElement)) {\n            converter.emit({\n                type: 'parseError',\n                columns,\n                detail: eventDetail,\n                headers,\n                error: 'Not a valid HTML Table'\n            });\n            return;\n        }\n        converter.tableElement = tableHTML;\n        converter.tableElementID = tableHTML.id;\n        this.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n        const rows = tableHTML.getElementsByTagName('tr'), rowsCount = rows.length;\n        let rowIndex = 0, item, { startRow } = parseOptions;\n        // Insert headers from the first row\n        if (firstRowAsNames && rowsCount) {\n            const items = rows[0].children, itemsLength = items.length;\n            for (let i = startColumn; i < itemsLength; i++) {\n                if (i > endColumn) {\n                    break;\n                }\n                item = items[i];\n                if (item.tagName === 'TD' ||\n                    item.tagName === 'TH') {\n                    headers.push(item.innerHTML);\n                }\n            }\n            startRow++;\n        }\n        while (rowIndex < rowsCount) {\n            if (rowIndex >= startRow && rowIndex <= endRow) {\n                const columnsInRow = rows[rowIndex].children, columnsInRowLength = columnsInRow.length;\n                let columnIndex = 0;\n                while (columnIndex < columnsInRowLength) {\n                    const relativeColumnIndex = columnIndex - startColumn, row = columns[relativeColumnIndex];\n                    item = columnsInRow[columnIndex];\n                    if ((item.tagName === 'TD' ||\n                        item.tagName === 'TH') &&\n                        (columnIndex >= startColumn &&\n                            columnIndex <= endColumn)) {\n                        if (!columns[relativeColumnIndex]) {\n                            columns[relativeColumnIndex] = [];\n                        }\n                        let cellValue = converter.asGuessedType(item.innerHTML);\n                        if (cellValue instanceof Date) {\n                            cellValue = cellValue.getTime();\n                        }\n                        columns[relativeColumnIndex][rowIndex - startRow] = cellValue;\n                        // Loop over all previous indices and make sure\n                        // they are nulls, not undefined.\n                        let i = 1;\n                        while (rowIndex - startRow >= i &&\n                            row[rowIndex - startRow - i] === void 0) {\n                            row[rowIndex - startRow - i] = null;\n                            i++;\n                        }\n                    }\n                    columnIndex++;\n                }\n            }\n            rowIndex++;\n        }\n        this.columns = columns;\n        this.headers = headers;\n        this.emit({\n            type: 'afterParse',\n            columns,\n            detail: eventDetail,\n            headers\n        });\n    }\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed HTML table\n     */\n    getTable() {\n        return Converters_DataConverter.getTableFromColumns(this.columns, this.headers);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options\n */\nHTMLTableConverter.defaultOptions = {\n    ...Converters_DataConverter.defaultOptions,\n    useRowspanHeaders: true,\n    useMultiLevelHeaders: true\n};\nConverters_DataConverter.registerType('HTMLTable', HTMLTableConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Converters_HTMLTableConverter = (HTMLTableConverter);\n\n;// ./code/es-modules/Data/Connectors/HTMLTableConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *\n * */\n\n\n\nconst { win } = (external_highcharts_src_js_default_default());\n\n\nconst { merge: HTMLTableConnector_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class that handles creating a data connector from an HTML table.\n *\n * @private\n */\nclass HTMLTableConnector extends Connectors_DataConnector {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of HTMLTableConnector.\n     *\n     * @param {HTMLTableConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    constructor(options) {\n        const mergedOptions = HTMLTableConnector_merge(HTMLTableConnector.defaultOptions, options);\n        super(mergedOptions);\n        this.converter = new Converters_HTMLTableConverter(mergedOptions);\n        this.options = mergedOptions;\n    }\n    /**\n     * Initiates creating the dataconnector from the HTML table\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits HTMLTableConnector#load\n     * @emits HTMLTableConnector#afterLoad\n     * @emits HTMLTableConnector#loadError\n     */\n    load(eventDetail) {\n        const connector = this, converter = connector.converter, table = connector.table, { dataModifier, table: tableHTML } = connector.options;\n        connector.emit({\n            type: 'load',\n            detail: eventDetail,\n            table,\n            tableElement: connector.tableElement\n        });\n        let tableElement;\n        if (typeof tableHTML === 'string') {\n            connector.tableID = tableHTML;\n            tableElement = win.document.getElementById(tableHTML);\n        }\n        else {\n            tableElement = tableHTML;\n            connector.tableID = tableElement.id;\n        }\n        connector.tableElement = tableElement || void 0;\n        if (!connector.tableElement) {\n            const error = 'HTML table not provided, or element with ID not found';\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error,\n                table\n            });\n            return Promise.reject(new Error(error));\n        }\n        converter.parse(HTMLTableConnector_merge({ tableElement: connector.tableElement }, connector.options), eventDetail);\n        // If already loaded, clear the current rows\n        table.deleteColumns();\n        table.setColumns(converter.getTable().getColumns());\n        return connector\n            .setModifierOptions(dataModifier)\n            .then(() => {\n            connector.emit({\n                type: 'afterLoad',\n                detail: eventDetail,\n                table,\n                tableElement: connector.tableElement\n            });\n            return connector;\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nHTMLTableConnector.defaultOptions = {\n    table: ''\n};\nConnectors_DataConnector.registerType('HTMLTable', HTMLTableConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Connectors_HTMLTableConnector = ((/* unused pure expression or super */ null && (HTMLTableConnector)));\n\n;// ./code/es-modules/Data/Modifiers/ChainModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Dawid Dragula\n *\n * */\n\n\n\nconst { merge: ChainModifier_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Modifies a table with the help of modifiers in an ordered chain.\n *\n */\nclass ChainModifier extends Modifiers_DataModifier {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the modifier chain.\n     *\n     * @param {Partial<ChainModifier.Options>} [options]\n     * Options to configure the modifier chain.\n     *\n     * @param {...DataModifier} [chain]\n     * Ordered chain of modifiers.\n     */\n    constructor(options, ...chain) {\n        super();\n        this.chain = chain;\n        this.options = ChainModifier_merge(ChainModifier.defaultOptions, options);\n        const optionsChain = this.options.chain || [];\n        for (let i = 0, iEnd = optionsChain.length, modifierOptions, ModifierClass; i < iEnd; ++i) {\n            modifierOptions = optionsChain[i];\n            if (!modifierOptions.type) {\n                continue;\n            }\n            ModifierClass = Modifiers_DataModifier.types[modifierOptions.type];\n            if (ModifierClass) {\n                chain.push(new ModifierClass(modifierOptions));\n            }\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a configured modifier to the end of the modifier chain. Please note,\n     * that the modifier can be added multiple times.\n     *\n     * @param {DataModifier} modifier\n     * Configured modifier to add.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     */\n    add(modifier, eventDetail) {\n        this.emit({\n            type: 'addModifier',\n            detail: eventDetail,\n            modifier\n        });\n        this.chain.push(modifier);\n        this.emit({\n            type: 'addModifier',\n            detail: eventDetail,\n            modifier\n        });\n    }\n    /**\n     * Clears all modifiers from the chain.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     */\n    clear(eventDetail) {\n        this.emit({\n            type: 'clearChain',\n            detail: eventDetail\n        });\n        this.chain.length = 0;\n        this.emit({\n            type: 'afterClearChain',\n            detail: eventDetail\n        });\n    }\n    /**\n     * Applies several modifications to the table and returns a modified copy of\n     * the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<Highcharts.DataTable>}\n     * Table with `modified` property as a reference.\n     */\n    async modify(table, eventDetail) {\n        const modifiers = (this.options.reverse ?\n            this.chain.slice().reverse() :\n            this.chain.slice());\n        if (table.modified === table) {\n            table.modified = table.clone(false, eventDetail);\n        }\n        let modified = table;\n        for (let i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n            try {\n                await modifiers[i].modify(modified, eventDetail);\n            }\n            catch (error) {\n                this.emit({\n                    type: 'error',\n                    detail: eventDetail,\n                    table\n                });\n                throw error;\n            }\n            modified = modified.modified;\n        }\n        table.modified = modified;\n        return table;\n    }\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyCell(table, columnName, rowIndex, cellValue, eventDetail) {\n        const modifiers = (this.options.reverse ?\n            this.chain.reverse() :\n            this.chain);\n        if (modifiers.length) {\n            let clone = table.clone();\n            for (let i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n                modifiers[i].modifyCell(clone, columnName, rowIndex, cellValue, eventDetail);\n                clone = clone.modified;\n            }\n            table.modified = clone;\n        }\n        return table;\n    }\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyColumns(table, columns, rowIndex, eventDetail) {\n        const modifiers = (this.options.reverse ?\n            this.chain.reverse() :\n            this.chain.slice());\n        if (modifiers.length) {\n            let clone = table.clone();\n            for (let i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n                modifiers[i].modifyColumns(clone, columns, rowIndex, eventDetail);\n                clone = clone.modified;\n            }\n            table.modified = clone;\n        }\n        return table;\n    }\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyRows(table, rows, rowIndex, eventDetail) {\n        const modifiers = (this.options.reverse ?\n            this.chain.reverse() :\n            this.chain.slice());\n        if (modifiers.length) {\n            let clone = table.clone();\n            for (let i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n                modifiers[i].modifyRows(clone, rows, rowIndex, eventDetail);\n                clone = clone.modified;\n            }\n            table.modified = clone;\n        }\n        return table;\n    }\n    /**\n     * Applies several modifications to the table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table as a reference.\n     *\n     * @emits ChainDataModifier#execute\n     * @emits ChainDataModifier#afterExecute\n     */\n    modifyTable(table, eventDetail) {\n        const chain = this;\n        chain.emit({\n            type: 'modify',\n            detail: eventDetail,\n            table\n        });\n        const modifiers = (chain.options.reverse ?\n            chain.chain.reverse() :\n            chain.chain.slice());\n        let modified = table.modified;\n        for (let i = 0, iEnd = modifiers.length, modifier; i < iEnd; ++i) {\n            modifier = modifiers[i];\n            modified = modifier.modifyTable(modified, eventDetail).modified;\n        }\n        table.modified = modified;\n        chain.emit({\n            type: 'afterModify',\n            detail: eventDetail,\n            table\n        });\n        return table;\n    }\n    /**\n     * Removes a configured modifier from all positions in the modifier chain.\n     *\n     * @param {DataModifier} modifier\n     * Configured modifier to remove.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     */\n    remove(modifier, eventDetail) {\n        const modifiers = this.chain;\n        this.emit({\n            type: 'removeModifier',\n            detail: eventDetail,\n            modifier\n        });\n        modifiers.splice(modifiers.indexOf(modifier), 1);\n        this.emit({\n            type: 'afterRemoveModifier',\n            detail: eventDetail,\n            modifier\n        });\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default option for the ordered modifier chain.\n */\nChainModifier.defaultOptions = {\n    type: 'Chain'\n};\nModifiers_DataModifier.registerType('Chain', ChainModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Modifiers_ChainModifier = ((/* unused pure expression or super */ null && (ChainModifier)));\n\n;// ./code/es-modules/Data/Modifiers/InvertModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Sophie Bremer\n *\n * */\n\n\n\nconst { merge: InvertModifier_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Inverts columns and rows in a table.\n *\n * @private\n */\nclass InvertModifier extends Modifiers_DataModifier {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the invert modifier.\n     *\n     * @param {Partial<InvertModifier.Options>} [options]\n     * Options to configure the invert modifier.\n     */\n    constructor(options) {\n        super();\n        this.options = InvertModifier_merge(InvertModifier.defaultOptions, options);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyCell(table, columnName, rowIndex, cellValue, eventDetail) {\n        const modified = table.modified, modifiedRowIndex = modified.getRowIndexBy('columnNames', columnName);\n        if (typeof modifiedRowIndex === 'undefined') {\n            modified.setColumns(this.modifyTable(table.clone()).getColumns(), void 0, eventDetail);\n        }\n        else {\n            modified.setCell(`${rowIndex}`, modifiedRowIndex, cellValue, eventDetail);\n        }\n        return table;\n    }\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyColumns(table, columns, rowIndex, eventDetail) {\n        const modified = table.modified, modifiedColumnNames = (modified.getColumn('columnNames') || []);\n        let columnNames = table.getColumnNames(), reset = (table.getRowCount() !== modifiedColumnNames.length);\n        if (!reset) {\n            for (let i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                if (columnNames[i] !== modifiedColumnNames[i]) {\n                    reset = true;\n                    break;\n                }\n            }\n        }\n        if (reset) {\n            return this.modifyTable(table, eventDetail);\n        }\n        columnNames = Object.keys(columns);\n        for (let i = 0, iEnd = columnNames.length, column, columnName, modifiedRowIndex; i < iEnd; ++i) {\n            columnName = columnNames[i];\n            column = columns[columnName];\n            modifiedRowIndex = (modified.getRowIndexBy('columnNames', columnName) ||\n                modified.getRowCount());\n            for (let j = 0, j2 = rowIndex, jEnd = column.length; j < jEnd; ++j, ++j2) {\n                modified.setCell(`${j2}`, modifiedRowIndex, column[j], eventDetail);\n            }\n        }\n        return table;\n    }\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyRows(table, rows, rowIndex, eventDetail) {\n        const columnNames = table.getColumnNames(), modified = table.modified, modifiedColumnNames = (modified.getColumn('columnNames') || []);\n        let reset = (table.getRowCount() !== modifiedColumnNames.length);\n        if (!reset) {\n            for (let i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                if (columnNames[i] !== modifiedColumnNames[i]) {\n                    reset = true;\n                    break;\n                }\n            }\n        }\n        if (reset) {\n            return this.modifyTable(table, eventDetail);\n        }\n        for (let i = 0, i2 = rowIndex, iEnd = rows.length, row; i < iEnd; ++i, ++i2) {\n            row = rows[i];\n            if (row instanceof Array) {\n                modified.setColumn(`${i2}`, row);\n            }\n            else {\n                for (let j = 0, jEnd = columnNames.length; j < jEnd; ++j) {\n                    modified.setCell(`${i2}`, j, row[columnNames[j]], eventDetail);\n                }\n            }\n        }\n        return table;\n    }\n    /**\n     * Inverts rows and columns in the table.\n     *\n     * @param {DataTable} table\n     * Table to invert.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table with inverted `modified` property as a reference.\n     */\n    modifyTable(table, eventDetail) {\n        const modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table });\n        const modified = table.modified;\n        if (table.hasColumns(['columnNames'])) { // Inverted table\n            const columnNamesColumn = ((table.deleteColumns(['columnNames']) || {})\n                .columnNames || []), columns = {}, columnNames = [];\n            for (let i = 0, iEnd = columnNamesColumn.length; i < iEnd; ++i) {\n                columnNames.push('' + columnNamesColumn[i]);\n            }\n            for (let i = 0, iEnd = table.getRowCount(), row; i < iEnd; ++i) {\n                row = table.getRow(i);\n                if (row) {\n                    columns[columnNames[i]] = row;\n                }\n            }\n            modified.deleteColumns();\n            modified.setColumns(columns);\n        }\n        else { // Regular table\n            const columns = {};\n            for (let i = 0, iEnd = table.getRowCount(), row; i < iEnd; ++i) {\n                row = table.getRow(i);\n                if (row) {\n                    columns[`${i}`] = row;\n                }\n            }\n            columns.columnNames = table.getColumnNames();\n            modified.deleteColumns();\n            modified.setColumns(columns);\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table });\n        return table;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options for the invert modifier.\n */\nInvertModifier.defaultOptions = {\n    type: 'Invert'\n};\nModifiers_DataModifier.registerType('Invert', InvertModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Modifiers_InvertModifier = ((/* unused pure expression or super */ null && (InvertModifier)));\n\n;// ./code/es-modules/Data/Modifiers/MathModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Replaces formula strings in a table with calculated values.\n *\n * @class\n * @name Highcharts.DataModifier.types.MathModifier\n * @augments Highcharts.DataModifier\n */\nclass MathModifier extends Modifiers_DataModifier {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options) {\n        super();\n        this.options = {\n            ...MathModifier.defaultOptions,\n            ...options\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    modifyTable(table, eventDetail) {\n        const modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table });\n        const alternativeSeparators = modifier.options.alternativeSeparators, formulaColumns = (modifier.options.formulaColumns ||\n            table.getColumnNames()), modified = table.modified;\n        for (let i = 0, iEnd = formulaColumns.length, columnName; i < iEnd; ++i) {\n            columnName = formulaColumns[i];\n            if (formulaColumns.indexOf(columnName) >= 0) {\n                modified.setColumn(columnName, modifier.processColumn(table, columnName));\n            }\n        }\n        const columnFormulas = (modifier.options.columnFormulas || []);\n        for (let i = 0, iEnd = columnFormulas.length, columnFormula, formula; i < iEnd; ++i) {\n            columnFormula = columnFormulas[i];\n            formula = Formula_FormulaParser.parseFormula(columnFormula.formula, alternativeSeparators);\n            modified.setColumn(columnFormula.column, modifier.processColumnFormula(formula, table, columnFormula.rowStart, columnFormula.rowEnd));\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table });\n        return table;\n    }\n    /**\n     * Process a column by replacing formula strings with calculated values.\n     *\n     * @private\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to extract column from and use as reference.\n     *\n     * @param {string} columnName\n     * Name of column to process.\n     *\n     * @param {number} rowIndex\n     * Row index to start the replacing process from.\n     *\n     * @return {Highcharts.DataTableColumn}\n     * Returns the processed table column.\n     */\n    processColumn(table, columnName, rowIndex = 0) {\n        const alternativeSeparators = this.options.alternativeSeparators, column = (table.getColumn(columnName, true) || [])\n            .slice(rowIndex > 0 ? rowIndex : 0);\n        for (let i = 0, iEnd = column.length, cacheFormula = [], cacheString = '', cell; i < iEnd; ++i) {\n            cell = column[i];\n            if (typeof cell === 'string' &&\n                cell[0] === '=') {\n                try {\n                    // Use cache while formula string is repetitive\n                    cacheFormula = (cacheString === cell ?\n                        cacheFormula :\n                        Formula_FormulaParser.parseFormula(cell.substring(1), alternativeSeparators));\n                    // Process parsed formula string\n                    column[i] =\n                        Formula_FormulaProcessor.processFormula(cacheFormula, table);\n                }\n                catch {\n                    column[i] = NaN;\n                }\n            }\n        }\n        return column;\n    }\n    /**\n     * Process a column by replacing cell values with calculated values from a\n     * given formula.\n     *\n     * @private\n     *\n     * @param {Highcharts.Formula} formula\n     * Formula to use for processing.\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to extract column from and use as reference.\n     *\n     * @param {number} rowStart\n     * Row index to start the replacing process from.\n     *\n     * @param {number} rowEnd\n     * Row index to end the replacing process.\n     *\n     * @return {Highcharts.DataTableColumn}\n     * Returns the processed table column.\n     */\n    processColumnFormula(formula, table, rowStart = 0, rowEnd = table.getRowCount()) {\n        rowStart = rowStart >= 0 ? rowStart : 0;\n        rowEnd = rowEnd >= 0 ? rowEnd : table.getRowCount() + rowEnd;\n        const column = [], modified = table.modified;\n        for (let i = 0, iEnd = (rowEnd - rowStart); i < iEnd; ++i) {\n            try {\n                column[i] = Formula_FormulaProcessor.processFormula(formula, modified);\n            }\n            catch {\n                column[i] = NaN;\n            }\n            finally {\n                formula = Formula_FormulaProcessor.translateReferences(formula, 0, 1);\n            }\n        }\n        return column;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options of MathModifier.\n * @private\n */\nMathModifier.defaultOptions = {\n    type: 'Math',\n    alternativeSeparators: false\n};\nModifiers_DataModifier.registerType('Math', MathModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Modifiers_MathModifier = ((/* unused pure expression or super */ null && (MathModifier)));\n\n;// ./code/es-modules/Data/Modifiers/RangeModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Dawid Dragula\n *\n * */\n\n\n\nconst { merge: RangeModifier_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Filters out table rows with a specific value range.\n *\n */\nclass RangeModifier extends Modifiers_DataModifier {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the range modifier.\n     *\n     * @param {Partial<RangeModifier.Options>} [options]\n     * Options to configure the range modifier.\n     */\n    constructor(options) {\n        super();\n        this.options = RangeModifier_merge(RangeModifier.defaultOptions, options);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Replaces table rows with filtered rows.\n     *\n     * @param {DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyTable(table, eventDetail) {\n        const modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table });\n        let indexes = [];\n        const { additive, ranges, strict } = modifier.options;\n        if (ranges.length) {\n            const modified = table.modified;\n            let columns = table.getColumns(), rows = [];\n            for (let i = 0, iEnd = ranges.length, range, rangeColumn; i < iEnd; ++i) {\n                range = ranges[i];\n                if (strict &&\n                    typeof range.minValue !== typeof range.maxValue) {\n                    continue;\n                }\n                if (i > 0 && !additive) {\n                    modified.deleteRows();\n                    modified.setRows(rows);\n                    modified.setOriginalRowIndexes(indexes, true);\n                    columns = modified.getColumns();\n                    rows = [];\n                    indexes = [];\n                }\n                rangeColumn = (columns[range.column] || []);\n                for (let j = 0, jEnd = rangeColumn.length, cell, row, originalRowIndex; j < jEnd; ++j) {\n                    cell = rangeColumn[j];\n                    switch (typeof cell) {\n                        default:\n                            continue;\n                        case 'boolean':\n                        case 'number':\n                        case 'string':\n                            break;\n                    }\n                    if (strict &&\n                        typeof cell !== typeof range.minValue) {\n                        continue;\n                    }\n                    if (cell >= range.minValue &&\n                        cell <= range.maxValue) {\n                        if (additive) {\n                            row = table.getRow(j);\n                            originalRowIndex = table.getOriginalRowIndex(j);\n                        }\n                        else {\n                            row = modified.getRow(j);\n                            originalRowIndex = modified.getOriginalRowIndex(j);\n                        }\n                        if (row) {\n                            rows.push(row);\n                            indexes.push(originalRowIndex);\n                        }\n                    }\n                }\n            }\n            modified.deleteRows();\n            modified.setRows(rows);\n            modified.setOriginalRowIndexes(indexes);\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table });\n        return table;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options for the range modifier.\n */\nRangeModifier.defaultOptions = {\n    type: 'Range',\n    ranges: []\n};\nModifiers_DataModifier.registerType('Range', RangeModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Modifiers_RangeModifier = ((/* unused pure expression or super */ null && (RangeModifier)));\n\n;// ./code/es-modules/Data/Modifiers/SortModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Dawid Dragula\n *\n * */\n\n\n\n\nconst { merge: SortModifier_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Sort table rows according to values of a column.\n *\n */\nclass SortModifier extends Modifiers_DataModifier {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static ascending(a, b) {\n        return ((a || 0) < (b || 0) ? -1 :\n            (a || 0) > (b || 0) ? 1 :\n                0);\n    }\n    static descending(a, b) {\n        return ((b || 0) < (a || 0) ? -1 :\n            (b || 0) > (a || 0) ? 1 :\n                0);\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the range modifier.\n     *\n     * @param {Partial<RangeDataModifier.Options>} [options]\n     * Options to configure the range modifier.\n     */\n    constructor(options) {\n        super();\n        this.options = SortModifier_merge(SortModifier.defaultOptions, options);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Returns index and row for sort reference.\n     *\n     * @private\n     *\n     * @param {Highcharts.DataTable} table\n     * Table with rows to reference.\n     *\n     * @return {Array<SortModifier.RowReference>}\n     * Array of row references.\n     */\n    getRowReferences(table) {\n        const rows = table.getRows(), rowReferences = [];\n        for (let i = 0, iEnd = rows.length; i < iEnd; ++i) {\n            rowReferences.push({\n                index: i,\n                row: rows[i]\n            });\n        }\n        return rowReferences;\n    }\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyCell(table, columnName, rowIndex, cellValue, eventDetail) {\n        const modifier = this, { orderByColumn, orderInColumn } = modifier.options;\n        if (columnName === orderByColumn) {\n            if (orderInColumn) {\n                table.modified.setCell(columnName, rowIndex, cellValue);\n                table.modified.setColumn(orderInColumn, modifier\n                    .modifyTable(new Data_DataTable({\n                    columns: table\n                        .getColumns([orderByColumn, orderInColumn])\n                }))\n                    .modified\n                    .getColumn(orderInColumn));\n            }\n            else {\n                modifier.modifyTable(table, eventDetail);\n            }\n        }\n        return table;\n    }\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyColumns(table, columns, rowIndex, eventDetail) {\n        const modifier = this, { orderByColumn, orderInColumn } = modifier.options, columnNames = Object.keys(columns);\n        if (columnNames.indexOf(orderByColumn) > -1) {\n            if (orderInColumn &&\n                columns[columnNames[0]].length) {\n                table.modified.setColumns(columns, rowIndex);\n                table.modified.setColumn(orderInColumn, modifier\n                    .modifyTable(new Data_DataTable({\n                    columns: table\n                        .getColumns([orderByColumn, orderInColumn])\n                }))\n                    .modified\n                    .getColumn(orderInColumn));\n            }\n            else {\n                modifier.modifyTable(table, eventDetail);\n            }\n        }\n        return table;\n    }\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyRows(table, rows, rowIndex, eventDetail) {\n        const modifier = this, { orderByColumn, orderInColumn } = modifier.options;\n        if (orderInColumn &&\n            rows.length) {\n            table.modified.setRows(rows, rowIndex);\n            table.modified.setColumn(orderInColumn, modifier\n                .modifyTable(new Data_DataTable({\n                columns: table\n                    .getColumns([orderByColumn, orderInColumn])\n            }))\n                .modified\n                .getColumn(orderInColumn));\n        }\n        else {\n            modifier.modifyTable(table, eventDetail);\n        }\n        return table;\n    }\n    /**\n     * Sorts rows in the table.\n     *\n     * @param {DataTable} table\n     * Table to sort in.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table with `modified` property as a reference.\n     */\n    modifyTable(table, eventDetail) {\n        const modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table });\n        const columnNames = table.getColumnNames(), rowCount = table.getRowCount(), rowReferences = this.getRowReferences(table), { direction, orderByColumn, orderInColumn } = modifier.options, compare = (direction === 'asc' ?\n            SortModifier.ascending :\n            SortModifier.descending), orderByColumnIndex = columnNames.indexOf(orderByColumn), modified = table.modified;\n        if (orderByColumnIndex !== -1) {\n            rowReferences.sort((a, b) => compare(a.row[orderByColumnIndex], b.row[orderByColumnIndex]));\n        }\n        if (orderInColumn) {\n            const column = [];\n            for (let i = 0; i < rowCount; ++i) {\n                column[rowReferences[i].index] = i;\n            }\n            modified.setColumns({ [orderInColumn]: column });\n        }\n        else {\n            const originalIndexes = [];\n            const rows = [];\n            let rowReference;\n            for (let i = 0; i < rowCount; ++i) {\n                rowReference = rowReferences[i];\n                originalIndexes.push(modified.getOriginalRowIndex(rowReference.index));\n                rows.push(rowReference.row);\n            }\n            modified.setRows(rows, 0);\n            modified.setOriginalRowIndexes(originalIndexes);\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table });\n        return table;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Default options to group table rows.\n */\nSortModifier.defaultOptions = {\n    type: 'Sort',\n    direction: 'desc',\n    orderByColumn: 'y'\n};\nModifiers_DataModifier.registerType('Sort', SortModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Modifiers_SortModifier = ((/* unused pure expression or super */ null && (SortModifier)));\n\n;// ./code/es-modules/masters/modules/data-tools.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nG.DataConnector = G.DataConnector || Connectors_DataConnector;\nG.DataConverter = G.DataConverter || Converters_DataConverter;\nG.DataCursor = G.DataCursor || Data_DataCursor;\nG.DataModifier = G.DataModifier || Modifiers_DataModifier;\nG.DataPool = G.DataPool || Data_DataPool;\nG.DataTable = G.DataTable || Data_DataTable;\nG.Formula = G.Formula || Formula_Formula;\n/* harmony default export */ const data_tools_src = ((external_highcharts_src_js_default_default()));\n\nexport { data_tools_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "DataModifier", "Column<PERSON><PERSON><PERSON>", "DataConnector", "DataConverter", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "addEvent", "fireEvent", "merge", "benchmark", "dataTable", "options", "results", "modifier", "execute", "modifyTable", "emit", "type", "iterations", "on", "length", "times", "startTime", "endTime", "window", "performance", "now", "push", "e", "modify", "table", "eventDetail", "Promise", "resolve", "reject", "modified", "clone", "detail", "modifyCell", "columnName", "rowIndex", "cellValue", "modifyColumns", "columns", "modifyRows", "rows", "callback", "types", "registerType", "DataModifierClass", "Modifiers_DataModifier", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "from", "removed", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "Data_ColumnUtils", "DataTableCore_fireEvent", "objectEach", "<PERSON><PERSON><PERSON>", "Data_DataTableCore", "autoId", "id", "rowCount", "versionTag", "slice", "Math", "max", "applyRowCount", "deleteRows", "getColumn", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "addColumns", "DataTable_addEvent", "defined", "extend", "DataTable_fireEvent", "isNumber", "DataTable_uniqueKey", "DataTable", "isNull", "NULL", "i", "iEnd", "skipColumns", "tableOptions", "tableClone", "originalRowIndexes", "localRowIndexes", "deleteColumns", "deletedColumns", "modifiedColumns", "deleteRowIndexReferences", "deletedRows", "modifiedRows", "deleted<PERSON>ells", "j", "jEnd", "includes", "getCell", "getCellAsBoolean", "getCellAsNumber", "useNaN", "isNaN", "parseFloat", "getCellAsString", "getColumnAsNumbers", "columnAsNumber", "columnLength", "getColumnNames", "asBasicColumns", "tableColumns", "getLocalRowIndex", "originalRowIndex", "getModifier", "getOriginalRowIndex", "getRows", "getRowCount", "getRowIndexBy", "rowIndexOffset", "indexOf", "getRowObject", "getRowObjects", "i2", "min", "getVersionTag", "hasColumns", "hasRowWith", "Number", "isFinite", "renameColumn", "newColumnName", "setCell", "typeAsOriginal", "tableModifier", "tableColumn", "ArrayConstructor", "setModifier", "promise", "then", "error", "setOriginalRowIndexes", "omitLocalRowIndexes", "modifiedIndexes", "originalIndex", "setRows", "version", "DataConnector_addEvent", "DataConnector_fireEvent", "DataConnector_merge", "pick", "metadata", "polling", "_polling", "describeColumn", "name", "columnMeta", "connector", "describeColumns", "pop", "getColumnOrder", "usePresentationState", "names", "sort", "b", "index", "getSortedColumns", "usePresentationOrder", "load", "save", "Error", "setColumnOrder", "setModifierOptions", "modifierOptions", "ModifierClass", "startPolling", "refreshTime", "clearTimeout", "setTimeout", "stopPolling", "whatIs", "DataConnectorClass", "Connectors_DataConnector", "DataConverter_addEvent", "DataConverter_fireEvent", "DataConverter_isNumber", "DataConverter_merge", "dateFormats", "regex", "parser", "match", "Date", "UTC", "NaN", "alternative", "year", "getFullYear", "mergedOptions", "defaultOptions", "regExpPoint", "decimalPoint", "decimalRegExp", "RegExp", "asBoolean", "value", "asNumber", "asDate", "timestamp", "parseDate", "asString", "asGuessedType", "typeMap", "converter", "guessType", "decimalRegex", "replace", "test", "getDate", "deduceDateFormat", "data", "limit", "stable", "format", "thing", "guessedFormat", "madeDeduction", "elem", "trim", "split", "parseInt", "join", "dateFormat", "export", "headers", "getTable", "trimedValue", "innerTrimedValue", "floatValue", "parse", "dateFormatProp", "getTime", "getTimezoneOffset", "str", "inside", "alternativeFormat", "startColumn", "endColumn", "MAX_VALUE", "startRow", "endRow", "firstRowAsNames", "switchRowsAndColumns", "DataConverterClass", "getTableFromColumns", "Converters_DataConverter", "DataCursor", "stateMap", "emittingRegister", "listenerMap", "addListener", "tableId", "state", "listener", "listeners", "buildEmittingTag", "cursor", "firstRow", "lastRow", "emitCursor", "event", "lasting", "cursors", "getIndex", "emittingTag", "remitCursor", "removeListener", "to<PERSON><PERSON><PERSON>", "defaultRange", "range", "needle", "columnNeedle", "JSON", "stringify", "isEqual", "cursorA", "cursorB", "isInRange", "needleColumns", "rangeColumns", "every", "toPositions", "positions", "rowEnd", "columnEnd", "Data_DataCursor", "Data_DataPoolDefaults", "connectors", "DataPool", "waiting", "getConnector", "connectorId", "waitingList", "connectorOptions", "getConnectorOptions", "loadConnector", "getConnectorIds", "connectorIds", "getConnectorTable", "isNewConnector", "ConnectorClass", "setConnectorOptions", "instances", "booleanRegExp", "decimal1RegExp", "decimal2RegExp", "functionRegExp", "operatorRegExp", "rangeA1RegExp", "rangeR1C1RegExp", "referenceA1RegExp", "referenceR1C1RegExp", "extractParantheses", "text", "parantheseLevel", "char", "parantheseStart", "substring", "extractString", "escaping", "parseArgument", "alternativeSeparators", "beginColumnRelative", "beginRowRelative", "endColumnRelative", "endRowRelative", "beginColumn", "beginRow", "parseReferenceColumn", "formula", "parseForm<PERSON>", "next", "columnRelative", "rowRelative", "reference", "string", "parantheses", "args", "parseArguments", "argumentsSeparator", "term", "parantes<PERSON>", "position", "code", "factor", "charCodeAt", "pow", "Formula_FormulaParser", "operators", "FormulaTypes", "isFormula", "item", "isFunction", "isOperator", "isRange", "isReference", "isValue", "FormulaProcessor_isFormula", "FormulaProcessor_isFunction", "FormulaProcessor_isOperator", "FormulaProcessor_isRange", "FormulaProcessor_isReference", "FormulaProcessor_isValue", "asLogicalStringRegExp", "MAX_FALSE", "MAX_STRING", "MAX_TRUE", "operatorPriority", "processorFunctions", "processorFunctionNameRegExp", "asLogicalNumber", "asLogicalString", "toLowerCase", "basicOperation", "operator", "x", "y", "round", "getArgumentValue", "arg", "getRangeValues", "processFunction", "processFormula", "values", "cell", "cells", "getReferenceValue", "operator2", "formulaFunction", "processor", "Formula_FormulaProcessor", "getArgumentsValues", "registerProcessorFunction", "processorFunction", "translateReferences", "columnDel<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ABS_getArgumentValue", "abs", "value2", "AND_getArgumentValue", "AND", "AVERAGE_getArgumentsValues", "count", "AVERAGEA_getArgumentValue", "COUNT", "COUNTA", "IF_getArgumentValue", "ISNA_getArgumentValue", "MAX_getArgumentsValues", "MAX", "NEGATIVE_INFINITY", "median", "half", "floor", "MIN_getArgumentsValues", "MIN", "POSITIVE_INFINITY", "MOD_getArgumentValue", "getModeMap", "modeMap", "SNGL", "mode<PERSON>ey", "modeCount", "keyValue", "value1", "modeKeys", "NOT_getArgumentValue", "OR_getArgumentValue", "OR", "PRODUCT_getArgumentsValues", "PRODUCT", "calculated", "SUM", "XOR_getArgumentValue", "lastValue", "Formula", "CSVConverter_merge", "CSVConverter", "dataTypes", "useLocalDecimalPoint", "lineDelimiter", "exportNames", "itemDelimiter", "toLocaleString", "csvRows", "columnsCount", "rowArray", "columnIndex", "columnDataType", "dataType", "String", "cellVal", "parserOptions", "beforeParse", "lines", "rowIt", "csv", "guessedItemDelimiter", "guess<PERSON><PERSON><PERSON><PERSON>", "offset", "parseCSVRow", "columnStr", "rowNumber", "guessedDecimalPoint", "c", "token", "actualColumn", "read", "pushType", "initialValue", "points", "commas", "guessed", "potDelimiters", "linesCount", "inStr", "cn", "cl", "CSVConnector_merge", "CSVConnector", "enablePolling", "dataRefreshRate", "csvURL", "dataModifier", "fetch", "response", "JSONConverter_merge", "JSONConverter_objectEach", "JSONConverter", "orientation", "shift", "toString", "newRow", "arrayWithPath", "acc", "JSONConnector_merge", "JSONConnector", "dataUrl", "json", "console", "warn", "GoogleSheetsConverter_merge", "GoogleSheetsConverter_uniqueKey", "GoogleSheetsConverter", "header", "parseOptions", "GoogleSheetsConnector_merge", "GoogleSheetsConnector_pick", "GoogleSheetsConnector", "googleAPIKey", "googleSpreadsheetKey", "url", "buildFetchURL", "URL", "canParse", "message", "status", "alphabet", "buildQueryRange", "googleSpreadsheetRange", "<PERSON><PERSON><PERSON><PERSON>", "sheetKey", "onlyColumnNames", "pathname", "searchParams", "href", "HTMLTableConverter_merge", "HTMLTableConverter", "tableElement", "tableElementID", "useMultiLevelHeaders", "htmlRows", "tableHead", "subcategories", "subhead", "getTableHeaderHTML", "getCellHTMLFromValue", "caption", "tableCaption", "tag", "classes", "attrs", "val", "className", "topheaders", "subheaders", "useRowspanHeaders", "html", "len", "cur", "cur<PERSON><PERSON><PERSON>", "rowspan", "isRowEqual", "row1", "row2", "tableHTML", "HTMLElement", "getElementsByTagName", "rowsCount", "children", "itemsLength", "tagName", "innerHTML", "columnsInRow", "columnsInRowLength", "relativeColumnIndex", "win", "HTMLTableConnector_merge", "HTMLTableConnector", "tableID", "document", "getElementById", "ChainModifier_merge", "ChainModifier", "chain", "optionsChain", "add", "clear", "modifiers", "reverse", "remove", "InvertModifier_merge", "InvertModifier", "modifiedRowIndex", "modifiedColumnNames", "reset", "j2", "columnNamesColumn", "MathModifier", "formulaColumns", "processColumn", "columnFormulas", "columnFormula", "processColumnFormula", "rowStart", "cacheFormula", "cacheString", "RangeModifier_merge", "RangeModifier", "indexes", "additive", "ranges", "strict", "rangeColumn", "minValue", "maxValue", "SortModifier_merge", "SortModifier", "ascending", "descending", "getRowReferences", "rowReferences", "orderByColumn", "orderInColumn", "direction", "compare", "orderByColumnIndex", "rowReference", "originalIndexes", "G", "data_tools_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,CAEvF,IA4QEC,EAsEAC,EA4tDAC,EAimBAC,EA9zEPF,EAjVSG,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDzB,EAAwD,OAAU,CAC7H,IAAI0B,EAA0DrB,EAAoBC,CAAC,CAACmB,GAiBpF,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,UAAAA,CAAS,CAAEC,MAAAA,CAAK,CAAE,CAAIH,GAUxC,OAAMzB,EAoBF6B,UAAUC,CAAS,CAAEC,CAAO,CAAE,CAC1B,IAAMC,EAAU,EAAE,CACZC,EAAW,IAAI,CACfC,EAAU,KACZD,EAASE,WAAW,CAACL,GACrBG,EAASG,IAAI,CAAC,CACVC,KAAM,yBACV,EACJ,EAIM,CAAEC,WAAAA,CAAU,CAAE,CAAGV,EAHA,CACnBU,WAAY,CAChB,EAC6CP,GAC7CE,EAASM,EAAE,CAAC,0BAA2B,KACnC,GAAIP,EAAQQ,MAAM,GAAKF,EAAY,CAC/BL,EAASG,IAAI,CAAC,CACVC,KAAM,iBACNL,QAAAA,CACJ,GACA,MACJ,CAEAE,GACJ,GACA,IAAMO,EAAQ,CACVC,UAAW,EACXC,QAAS,CACb,EAWA,OATAV,EAASM,EAAE,CAAC,SAAU,KAClBE,EAAMC,SAAS,CAAGE,OAAOC,WAAW,CAACC,GAAG,EAC5C,GACAb,EAASM,EAAE,CAAC,cAAe,KACvBE,EAAME,OAAO,CAAGC,OAAOC,WAAW,CAACC,GAAG,GACtCd,EAAQe,IAAI,CAACN,EAAME,OAAO,CAAGF,EAAMC,SAAS,CAChD,GAEAR,IACOF,CACX,CAOAI,KAAKY,CAAC,CAAE,CACJrB,EAAU,IAAI,CAAEqB,EAAEX,IAAI,CAAEW,EAC5B,CAaAC,OAAOC,CAAK,CAAEC,CAAW,CAAE,CACvB,IAAMlB,EAAW,IAAI,CACrB,OAAO,IAAImB,QAAQ,CAACC,EAASC,KACrBJ,EAAMK,QAAQ,GAAKL,GACnBA,CAAAA,EAAMK,QAAQ,CAAGL,EAAMM,KAAK,CAAC,CAAA,EAAOL,EAAW,EAEnD,GAAI,CACAE,EAAQpB,EAASE,WAAW,CAACe,EAAOC,GACxC,CACA,MAAOH,EAAG,CACNf,EAASG,IAAI,CAAC,CACVC,KAAM,QACNoB,OAAQN,EACRD,MAAAA,CACJ,GACAI,EAAON,EACX,CACJ,EACJ,CAuBAU,WAAWR,CAAK,CAEhBS,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,CAE1C,CACE,OAAO,IAAI,CAAChB,WAAW,CAACe,EAC5B,CAoBAY,cAAcZ,CAAK,CAEnBa,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAE5B,CACE,OAAO,IAAI,CAAChB,WAAW,CAACe,EAC5B,CAoBAc,WAAWd,CAAK,CAEhBe,CAAI,CAAEL,CAAQ,CAAET,CAAW,CAEzB,CACE,OAAO,IAAI,CAAChB,WAAW,CAACe,EAC5B,CAaAX,GAAGF,CAAI,CAAE6B,CAAQ,CAAE,CACf,OAAOxC,EAAS,IAAI,CAAEW,EAAM6B,EAChC,CACJ,CAwBIlE,CAfOA,EA4CRA,GAAiBA,CAAAA,EAAe,CAAC,CAAA,GA7BnBmE,KAAK,CAAG,CAAC,EA4BtBnE,EAAaoE,YAAY,CALzB,SAAsBvD,CAAG,CAAEwD,CAAiB,EACxC,MAAQ,CAAC,CAACxD,GACN,CAACb,EAAamE,KAAK,CAACtD,EAAI,EACxB,CAAC,CAAEb,CAAAA,EAAamE,KAAK,CAACtD,EAAI,CAAGwD,CAAgB,CACrD,EAQyB,IAAMC,EAA0BtE,CAyDzDC,EArCOA,EAyFRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GApDlBsE,SAAS,CAPrB,SAAmBC,CAAM,CAAEhC,CAAM,CAAEiC,CAAU,SACzC,AAAIC,MAAMC,OAAO,CAACH,IACdA,EAAOhC,MAAM,CAAGA,EACTgC,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAGjC,EACxD,EAoDAvC,EAAY2E,MAAM,CAvBlB,SAAgBJ,CAAM,CAAEK,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,EAAQ,EAAE,EACrE,GAAIN,MAAMC,OAAO,CAACH,GAId,OAHKE,MAAMC,OAAO,CAACK,IACfA,CAAAA,EAAQN,MAAMO,IAAI,CAACD,EAAK,EAErB,CACHE,QAASV,EAAOI,MAAM,CAACC,EAAOC,KAAgBE,GAC9CG,MAAOX,CACX,EAEJ,IAAMY,EAAcrE,OAAOsE,cAAc,CAACb,GACrCc,WAAW,CACVJ,EAAUV,CAAM,CAACO,EAAoB,WAAa,QAAQ,CAACF,EAAOA,EAAQC,GAE1ES,EAAS,IAAIH,EADDZ,EAAOhC,MAAM,CAAGsC,EAAcE,EAAMxC,MAAM,EAK5D,OAHA+C,EAAOC,GAAG,CAAChB,EAAOiB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAAChB,EAAOiB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMxC,MAAM,EAC9D,CACH0C,QAASA,EACTC,MAAOI,CACX,CACJ,EAQyB,IAAMG,EAAoBzF,EAmBjD,CAAEsE,UAAAA,CAAS,CAAEK,OAAAA,CAAM,CAAE,CAAGc,EAExB,CAAE/D,UAAWgE,CAAuB,CAAEC,WAAAA,CAAU,CAAEC,UAAAA,CAAS,CAAE,CAAIpE,IA+PpCqE,EA9OnC,MAiBIR,YAAYvD,EAAU,CAAC,CAAC,CAAE,CAOtB,IAAI,CAACgE,MAAM,CAAG,CAAChE,EAAQiE,EAAE,CACzB,IAAI,CAACjC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACiC,EAAE,CAAIjE,EAAQiE,EAAE,EAAIH,IACzB,IAAI,CAACtC,QAAQ,CAAG,IAAI,CACpB,IAAI,CAAC0C,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGL,IAClB,IAAII,EAAW,EACfL,EAAW7D,EAAQgC,OAAO,EAAI,CAAC,EAAG,CAACS,EAAQb,KACvC,IAAI,CAACI,OAAO,CAACJ,EAAW,CAAGa,EAAO2B,KAAK,GACvCF,EAAWG,KAAKC,GAAG,CAACJ,EAAUzB,EAAOhC,MAAM,CAC/C,GACA,IAAI,CAAC8D,aAAa,CAACL,EACvB,CAaAK,cAAcL,CAAQ,CAAE,CACpB,IAAI,CAACA,QAAQ,CAAGA,EAChBL,EAAW,IAAI,CAAC7B,OAAO,CAAE,CAACS,EAAQb,KAC1Ba,EAAOhC,MAAM,GAAKyD,GAClB,CAAA,IAAI,CAAClC,OAAO,CAACJ,EAAW,CAAGY,EAAUC,EAAQyB,EAAQ,CAE7D,EACJ,CAeAM,WAAW3C,CAAQ,CAAEqC,EAAW,CAAC,CAAE,CAC/B,GAAIA,EAAW,GAAKrC,EAAW,IAAI,CAACqC,QAAQ,CAAE,CAC1C,IAAIzD,EAAS,EACboD,EAAW,IAAI,CAAC7B,OAAO,CAAE,CAACS,EAAQb,KAC9B,IAAI,CAACI,OAAO,CAACJ,EAAW,CACpBiB,EAAOJ,EAAQZ,EAAUqC,GAAUd,KAAK,CAC5C3C,EAASgC,EAAOhC,MAAM,AAC1B,GACA,IAAI,CAACyD,QAAQ,CAAGzD,CACpB,CACAmD,EAAwB,IAAI,CAAE,kBAAmB,CAAE/B,SAAAA,EAAUqC,SAAAA,CAAS,GACtE,IAAI,CAACC,UAAU,CAAGL,GACtB,CAWAW,UAAU7C,CAAU,CAEpB8C,CAAW,CAAE,CACT,OAAO,IAAI,CAAC1C,OAAO,CAACJ,EAAW,AACnC,CAYA+C,WAAWC,CAAW,CAEtBF,CAAW,CAAE,CACT,MAAO,AAACE,CAAAA,GAAe5F,OAAO6F,IAAI,CAAC,IAAI,CAAC7C,OAAO,CAAA,EAAG8C,MAAM,CAAC,CAAC9C,EAASJ,KAC/DI,CAAO,CAACJ,EAAW,CAAG,IAAI,CAACI,OAAO,CAACJ,EAAW,CACvCI,GACR,CAAC,EACR,CAaA+C,OAAOlD,CAAQ,CAAE+C,CAAW,CAAE,CAC1B,MAAO,AAACA,CAAAA,GAAe5F,OAAO6F,IAAI,CAAC,IAAI,CAAC7C,OAAO,CAAA,EAAGgD,GAAG,CAAC,AAAClG,GAAQ,IAAI,CAACkD,OAAO,CAAClD,EAAI,EAAE,CAAC+C,EAAS,CAChG,CAmBAoD,UAAUrD,CAAU,CAAEa,EAAS,EAAE,CAAEZ,EAAW,CAAC,CAAET,CAAW,CAAE,CAC1D,IAAI,CAAC8D,UAAU,CAAC,CAAE,CAACtD,EAAW,CAAEa,CAAO,EAAGZ,EAAUT,EACxD,CAmBA8D,WAAWlD,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAAE,CACvC,IAAI8C,EAAW,IAAI,CAACA,QAAQ,CAC5BL,EAAW7B,EAAS,CAACS,EAAQb,KACzB,IAAI,CAACI,OAAO,CAACJ,EAAW,CAAGa,EAAO2B,KAAK,GACvCF,EAAWzB,EAAOhC,MAAM,AAC5B,GACA,IAAI,CAAC8D,aAAa,CAACL,GACd9C,GAAa+D,SACdvB,EAAwB,IAAI,CAAE,mBAC9B,IAAI,CAACO,UAAU,CAAGL,IAE1B,CAoBAsB,OAAOC,CAAG,CAAExD,EAAW,IAAI,CAACqC,QAAQ,CAAEoB,CAAM,CAAElE,CAAW,CAAE,CACvD,GAAM,CAAEY,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAEuD,EAAgBD,EAAS,IAAI,CAACpB,QAAQ,CAAG,EAAIrC,EAAW,EAClFgC,EAAWwB,EAAK,CAACvD,EAAWF,KACxB,IAAIa,EAAST,CAAO,CAACJ,EAAW,EAC5BR,GAAaoE,aAAe,CAAA,GAAS,AAAI7C,MAAM4C,GAC/C9C,IACI6C,EACA7C,EAASI,EAAOJ,EAAQZ,EAAU,EAAG,CAAA,EAAM,CAACC,EAAU,EAAEsB,KAAK,CAG7DX,CAAM,CAACZ,EAAS,CAAGC,EAEvBE,CAAO,CAACJ,EAAW,CAAGa,EAE9B,GACI8C,EAAgB,IAAI,CAACrB,QAAQ,EAC7B,IAAI,CAACK,aAAa,CAACgB,GAElBnE,GAAa+D,SACdvB,EAAwB,IAAI,CAAE,gBAC9B,IAAI,CAACO,UAAU,CAAGL,IAE1B,CACJ,EA6DM,CAAEnE,SAAU8F,CAAkB,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAE/F,UAAWgG,CAAmB,CAAEC,SAAAA,CAAQ,CAAE/B,UAAWgC,CAAmB,CAAE,CAAIpG,GAiBrI,OAAMqG,UAAkBhC,EAyBpB,OAAOiC,OAAOX,CAAG,CAAE,CACf,GAAIA,IAAQU,EAAUE,IAAI,CACtB,MAAO,CAAA,EAEX,GAAIZ,aAAe1C,MAAO,CACtB,GAAI,CAAC0C,EAAI5E,MAAM,CACX,MAAO,CAAA,EAEX,IAAK,IAAIyF,EAAI,EAAGC,EAAOd,EAAI5E,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAC3C,GAAIb,AAAW,OAAXA,CAAG,CAACa,EAAE,CACN,MAAO,CAAA,CAGnB,KACK,CACD,IAAMtB,EAAc5F,OAAO6F,IAAI,CAACQ,GAChC,GAAI,CAACT,EAAYnE,MAAM,CACnB,MAAO,CAAA,EAEX,IAAK,IAAIyF,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACnD,GAAIb,AAAwB,OAAxBA,CAAG,CAACT,CAAW,CAACsB,EAAE,CAAC,CACnB,MAAO,CAAA,CAGnB,CACA,MAAO,CAAA,CACX,CAMA3C,YAAYvD,EAAU,CAAC,CAAC,CAAE,CACtB,KAAK,CAACA,GACN,IAAI,CAACwB,QAAQ,CAAG,IAAI,AACxB,CAyBAC,MAAM2E,CAAW,CAAEhF,CAAW,CAAE,CAC5B,IAAoBiF,EAAe,CAAC,EACpClF,AADc,IAAI,CACZd,IAAI,CAAC,CAAEC,KAAM,aAAcoB,OAAQN,CAAY,GAChDgF,GACDC,CAAAA,EAAarE,OAAO,CAAGb,AAHb,IAAI,CAGea,OAAO,AAAD,EAElCb,AALS,IAAI,CAKP6C,MAAM,EACbqC,CAAAA,EAAapC,EAAE,CAAG9C,AANR,IAAI,CAMU8C,EAAE,AAAD,EAE7B,IAAMqC,EAAa,IAAIP,EAAUM,GAWjC,OAVKD,IACDE,EAAWnC,UAAU,CAAGhD,AAVd,IAAI,CAUgBgD,UAAU,CACxCmC,EAAWC,kBAAkB,CAAGpF,AAXtB,IAAI,CAWwBoF,kBAAkB,CACxDD,EAAWE,eAAe,CAAGrF,AAZnB,IAAI,CAYqBqF,eAAe,EAEtDrF,AAdc,IAAI,CAcZd,IAAI,CAAC,CACPC,KAAM,kBACNoB,OAAQN,EACRkF,WAAAA,CACJ,GACOA,CACX,CAmBAG,cAAc7B,CAAW,CAAExD,CAAW,CAAE,CACpC,IAAoBY,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,CAAE0E,EAAiB,CAAC,EAAGC,EAAkB,CAAC,EAAGzG,EAAWiB,AAArF,IAAI,CAAuFjB,QAAQ,CAAEgE,EAAW/C,AAAhH,IAAI,CAAkH+C,QAAQ,CAE5I,GAAIU,AADJA,CAAAA,EAAeA,GAAe5F,OAAO6F,IAAI,CAAC7C,EAAQ,EAClCvB,MAAM,CAAE,CACpBU,AAHU,IAAI,CAGRd,IAAI,CAAC,CACPC,KAAM,gBACNsE,YAAAA,EACAlD,OAAQN,CACZ,GACA,IAAK,IAAI8E,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEgC,EAAQb,EAAYsE,EAAIC,EAAM,EAAED,EAEvEzD,CAAAA,EAAST,CAAO,CADhBJ,EAAagD,CAAW,CAACsB,EAAE,CACC,AAAD,IAEvBQ,CAAc,CAAC9E,EAAW,CAAGa,EAC7BkE,CAAe,CAAC/E,EAAW,CAAG,AAAIe,MAAMuB,IAE5C,OAAOlC,CAAO,CAACJ,EAAW,CAe9B,OAbK5C,OAAO6F,IAAI,CAAC7C,GAASvB,MAAM,GAC5BU,AAlBM,IAAI,CAkBJ+C,QAAQ,CAAG,EACjB,IAAI,CAAC0C,wBAAwB,IAE7B1G,GACAA,EAAS6B,aAAa,CAtBhB,IAAI,CAsBoB4E,EAAiB,EAAGvF,GAEtDD,AAxBU,IAAI,CAwBRd,IAAI,CAAC,CACPC,KAAM,qBACN0B,QAAS0E,EACT9B,YAAAA,EACAlD,OAAQN,CACZ,GACOsF,CACX,CACJ,CAOAE,0BAA2B,CACvB,OAAO,IAAI,CAACL,kBAAkB,CAC9B,OAAO,IAAI,CAACC,eAAe,AAG/B,CAsBAhC,WAAW3C,CAAQ,CAAEqC,EAAW,CAAC,CAAE9C,CAAW,CAAE,CAC5C,IAAoByF,EAAc,EAAE,CAAEC,EAAe,EAAE,CAAE5G,EAAWiB,AAAtD,IAAI,CAAwDjB,QAAQ,CAWlF,GAVAiB,AADc,IAAI,CACZd,IAAI,CAAC,CACPC,KAAM,aACNoB,OAAQN,EACR8C,SAAAA,EACArC,SAAWA,GAAY,CAC3B,GACwB,KAAA,IAAbA,IACPA,EAAW,EACXqC,EAAW/C,AATD,IAAI,CASG+C,QAAQ,EAEzBA,EAAW,GAAKrC,EAAWV,AAXjB,IAAI,CAWmB+C,QAAQ,CAAE,CAC3C,IAAMlC,EAAUb,AAZN,IAAI,CAYQa,OAAO,CAAE4C,EAAc5F,OAAO6F,IAAI,CAAC7C,GACzD,IAAK,IAAIkE,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEgC,EAAQsE,EAAcnF,EAAYsE,EAAIC,EAAM,EAAED,EAAG,CAExFzD,EAAST,CAAO,CADhBJ,EAAagD,CAAW,CAACsB,EAAE,CACC,CAC5B,IAAM1C,EAASG,EAAiBd,MAAM,CAACJ,EAAQZ,EAAUqC,GACzD6C,EAAevD,EAAOL,OAAO,CAC7BnB,CAAO,CAACJ,EAAW,CAAGa,EAASe,EAAOJ,KAAK,CACtC8C,GACD/E,CAAAA,AApBE,IAAI,CAoBA+C,QAAQ,CAAGzB,EAAOhC,MAAM,AAAD,EAEjC,IAAK,IAAIuG,EAAI,EAAGC,EAAOF,EAAatG,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EACpDH,CAAW,CAACG,EAAE,CAAIH,CAAW,CAACG,EAAE,EAAI,EAAE,CACtCH,CAAW,CAACG,EAAE,CAACd,EAAE,CAAGa,CAAY,CAACC,EAAE,CAEvCF,EAAa9F,IAAI,CAAC,AAAI2B,MAAMwD,GAChC,CACJ,CAWA,OAVIjG,GACAA,EAAS+B,UAAU,CA9BT,IAAI,CA8Ba6E,EAAejF,GAAY,EAAIT,GAE9DD,AAhCc,IAAI,CAgCZd,IAAI,CAAC,CACPC,KAAM,kBACNoB,OAAQN,EACR8C,SAAAA,EACArC,SAAWA,GAAY,EACvBK,KAAM2E,CACV,GACOA,CACX,CASAxG,KAAKY,CAAC,CAAE,CACA,CACA,qBACA,kBACA,eACA,kBACA,eACH,CAACiG,QAAQ,CAACjG,EAAEX,IAAI,GACb,CAAA,IAAI,CAAC6D,UAAU,CAAG2B,GAAoB,EAE1CF,EAAoB,IAAI,CAAE3E,EAAEX,IAAI,CAAEW,EACtC,CAeAkG,QAAQvF,CAAU,CAAEC,CAAQ,CAAE,CAE1B,IAAMY,EAAStB,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACxC,GAAIa,EACA,OAAOA,CAAM,CAACZ,EAAS,AAE/B,CAeAuF,iBAAiBxF,CAAU,CAAEC,CAAQ,CAAE,CAEnC,IAAMY,EAAStB,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACxC,MAAO,CAAC,CAAEa,CAAAA,GAAUA,CAAM,CAACZ,EAAS,AAAD,CACvC,CAkBAwF,gBAAgBzF,CAAU,CAAEC,CAAQ,CAAEyF,CAAM,CAAE,CAE1C,IAAM7E,EAAStB,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACpCE,EAAaW,GAAUA,CAAM,CAACZ,EAAS,CAC3C,OAAQ,OAAOC,GACX,IAAK,UACD,MAAQA,GAAAA,CACZ,KAAK,SACD,OAAQyF,MAAMzF,IAAc,CAACwF,EAAS,KAAOxF,CACrD,CAEA,OAAQyF,MADRzF,EAAY0F,WAAW,CAAC,EAAE1F,GAAa,GAAG,CAAC,IACf,CAACwF,EAAS,KAAOxF,CACjD,CAeA2F,gBAAgB7F,CAAU,CAAEC,CAAQ,CAAE,CAElC,IAAMY,EAAStB,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CAExC,MAAO,CAAC,EAAGa,GAAUA,CAAM,CAACZ,EAAS,CAAE,CAAC,AAC5C,CAgBA4C,UAAU7C,CAAU,CAAE8C,CAAW,CAAE,CAC/B,OAAO,IAAI,CAACC,UAAU,CAAC,CAAC/C,EAAW,CAAE8C,EAAY,CAAC9C,EAAW,AACjE,CAqBA8F,mBAAmB9F,CAAU,CAAE0F,CAAM,CAAE,CAEnC,IAAM7E,EAAST,AADeb,AAAhB,IAAI,CAAkBa,OAAO,AACrB,CAACJ,EAAW,CAAE+F,EAAiB,EAAE,CACvD,GAAIlF,EAAQ,CACR,IAAMmF,EAAenF,EAAOhC,MAAM,CAClC,GAAI6G,EACA,IAAK,IAAIpB,EAAI,EAAGA,EAAI0B,EAAc,EAAE1B,EAChCyB,EAAe3G,IAAI,CAACG,AANlB,IAAI,CAMoBkG,eAAe,CAACzF,EAAYsE,EAAG,CAAA,QAG5D,CACD,IAAK,IAAIA,EAAI,EAAGpE,EAAWoE,EAAI0B,EAAc,EAAE1B,EAAG,CAE9C,GAAI,AAAqB,UAArB,MADJpE,CAAAA,EAAYW,CAAM,CAACyD,EAAE,AAAD,EAGhB,OAAOzD,EAAO2B,KAAK,GAEvB,GAAItC,MAAAA,EAEA,KAER,CACA,IAAK,IAAIoE,EAAI,EAAGA,EAAI0B,EAAc,EAAE1B,EAChCyB,EAAe3G,IAAI,CAACG,AAtBlB,IAAI,CAsBoBkG,eAAe,CAACzF,EAAYsE,GAE9D,CACJ,CACA,OAAOyB,CACX,CASAE,gBAAiB,CAEb,OADkC7I,OAAO6F,IAAI,CAAC1D,AAAhC,IAAI,CAAkCa,OAAO,CAE/D,CAmBA2C,WAAWC,CAAW,CAAEF,CAAW,CAAEoD,CAAc,CAAE,CACjD,IAAoBC,EAAe5G,AAArB,IAAI,CAAuBa,OAAO,CAAEA,EAAU,CAAC,EAC7D4C,EAAeA,GAAe5F,OAAO6F,IAAI,CAACkD,GAC1C,IAAK,IAAI7B,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEgC,EAAQb,EAAYsE,EAAIC,EAAM,EAAED,EAEvEzD,CAAAA,EAASsF,CAAY,CADrBnG,EAAagD,CAAW,CAACsB,EAAE,CACM,AAAD,IAExBxB,EACA1C,CAAO,CAACJ,EAAW,CAAGa,EAEjBqF,GAAkB,CAACnF,MAAMC,OAAO,CAACH,GACtCT,CAAO,CAACJ,EAAW,CAAGe,MAAMO,IAAI,CAACT,GAGjCT,CAAO,CAACJ,EAAW,CAAGa,EAAO2B,KAAK,IAI9C,OAAOpC,CACX,CAWAgG,iBAAiBC,CAAgB,CAAE,CAC/B,GAAM,CAAEzB,gBAAAA,CAAe,CAAE,CAAG,IAAI,QAChC,AAAIA,EACOA,CAAe,CAACyB,EAAiB,CAErCA,CACX,CAQAC,aAAc,CACV,OAAO,IAAI,CAAChI,QAAQ,AACxB,CAWAiI,oBAAoBtG,CAAQ,CAAE,CAC1B,GAAM,CAAE0E,mBAAAA,CAAkB,CAAE,CAAG,IAAI,QACnC,AAAIA,EACOA,CAAkB,CAAC1E,EAAS,CAEhCA,CACX,CAgBAkD,OAAOlD,CAAQ,CAAE+C,CAAW,CAAE,CAC1B,OAAO,IAAI,CAACwD,OAAO,CAACvG,EAAU,EAAG+C,EAAY,CAAC,EAAE,AACpD,CASAyD,aAAc,CAEV,OAAO,IAAI,CAACnE,QAAQ,AACxB,CAkBAoE,cAAc1G,CAAU,CAAEE,CAAS,CAAEyG,CAAc,CAAE,CAEjD,IAAM9F,EAAStB,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACxC,GAAIa,EAAQ,CACR,IAAIZ,EAAW,GASf,GARIc,MAAMC,OAAO,CAACH,GAEdZ,EAAWY,EAAO+F,OAAO,CAAC1G,EAAWyG,GAEhC1C,EAAS/D,IAEdD,CAAAA,EAAWY,EAAO+F,OAAO,CAAC1G,EAAWyG,EAAc,EAEnD1G,AAAa,KAAbA,EACA,OAAOA,CAEf,CACJ,CAgBA4G,aAAa5G,CAAQ,CAAE+C,CAAW,CAAE,CAChC,OAAO,IAAI,CAAC8D,aAAa,CAAC7G,EAAU,EAAG+C,EAAY,CAAC,EAAE,AAC1D,CAkBA8D,cAAc7G,EAAW,CAAC,CAAEqC,EAAY,IAAI,CAACA,QAAQ,CAAGrC,CAAS,CAAE+C,CAAW,CAAE,CAC5E,IAAoB5C,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,CAAEE,EAAO,AAAIS,MAAMuB,GAC9DU,EAAeA,GAAe5F,OAAO6F,IAAI,CAAC7C,GAC1C,IAAK,IAAIkE,EAAIrE,EAAU8G,EAAK,EAAGxC,EAAO9B,KAAKuE,GAAG,CAACzH,AAFjC,IAAI,CAEmC+C,QAAQ,CAAGrC,EAAWqC,GAAYzB,EAAQ4C,EAAKa,EAAIC,EAAM,EAAED,EAAG,EAAEyC,EAEjH,IAAK,IAAM/G,KADXyD,EAAMnD,CAAI,CAACyG,EAAG,CAAG,CAAC,EACO/D,GACrBnC,EAAST,CAAO,CAACJ,EAAW,CAC5ByD,CAAG,CAACzD,EAAW,CAAIa,EAASA,CAAM,CAACyD,EAAE,CAAG,KAAK,EAGrD,OAAOhE,CACX,CAkBAkG,QAAQvG,EAAW,CAAC,CAAEqC,EAAY,IAAI,CAACA,QAAQ,CAAGrC,CAAS,CAAE+C,CAAW,CAAE,CACtE,IAAoB5C,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,CAAEE,EAAO,AAAIS,MAAMuB,GAC9DU,EAAeA,GAAe5F,OAAO6F,IAAI,CAAC7C,GAC1C,IAAK,IAAIkE,EAAIrE,EAAU8G,EAAK,EAAGxC,EAAO9B,KAAKuE,GAAG,CAACzH,AAFjC,IAAI,CAEmC+C,QAAQ,CAAGrC,EAAWqC,GAAYzB,EAAQ4C,EAAKa,EAAIC,EAAM,EAAED,EAAG,EAAEyC,EAEjH,IAAK,IAAM/G,KADXyD,EAAMnD,CAAI,CAACyG,EAAG,CAAG,EAAE,CACM/D,GACrBnC,EAAST,CAAO,CAACJ,EAAW,CAC5ByD,EAAIrE,IAAI,CAACyB,EAASA,CAAM,CAACyD,EAAE,CAAG,KAAK,GAG3C,OAAOhE,CACX,CASA2G,eAAgB,CACZ,OAAO,IAAI,CAAC1E,UAAU,AAC1B,CAYA2E,WAAWlE,CAAW,CAAE,CACpB,IAAoB5C,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,CAC3C,IAAK,IAAIkE,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAcyF,EAAIC,EAAM,EAAED,EAE/D,GAAI,CAAClE,CAAO,CADC4C,CAAW,CAACsB,EAAE,CACH,CACpB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,CAeA6C,WAAWnH,CAAU,CAAEE,CAAS,CAAE,CAE9B,IAAMW,EAAStB,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,QAExC,AAAIe,MAAMC,OAAO,CAACH,GACNA,AAA8B,KAA9BA,EAAO+F,OAAO,CAAC1G,KAGvB4D,CAAAA,EAAQ5D,IAAckH,OAAOC,QAAQ,CAACnH,EAAS,GACvCW,AAA+B,KAA/BA,EAAO+F,OAAO,CAAC,CAAC1G,EAGhC,CAeAtB,GAAGF,CAAI,CAAE6B,CAAQ,CAAE,CACf,OAAOsD,EAAmB,IAAI,CAAEnF,EAAM6B,EAC1C,CAgBA+G,aAAatH,CAAU,CAAEuH,CAAa,CAAE,CACpC,IAAoBnH,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,OAC3C,EAAIA,CAAO,CAACJ,EAAW,GACfA,IAAeuH,IACfnH,CAAO,CAACmH,EAAc,CAAGnH,CAAO,CAACJ,EAAW,CAC5C,OAAOI,CAAO,CAACJ,EAAW,EAEvB,CAAA,EAGf,CAsBAwH,QAAQxH,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,CAAE,CAClD,IAAoBY,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,CAAE9B,EAAWiB,AAA1C,IAAI,CAA4CjB,QAAQ,CAClEuC,EAAST,CAAO,CAACJ,EAAW,CAC5Ba,CAAAA,CAAAA,GAAUA,CAAM,CAACZ,EAAS,GAAKC,CAAQ,IAG3CX,AALc,IAAI,CAKZd,IAAI,CAAC,CACPC,KAAM,UACNwB,UAAAA,EACAF,WAAYA,EACZF,OAAQN,EACRS,SAAAA,CACJ,GACKY,GACDA,CAAAA,EAAST,CAAO,CAACJ,EAAW,CAAG,AAAIe,MAAMxB,AAb/B,IAAI,CAaiC+C,QAAQ,CAAA,EAEvDrC,GAAYV,AAfF,IAAI,CAeI+C,QAAQ,EAC1B/C,CAAAA,AAhBU,IAAI,CAgBR+C,QAAQ,CAAIrC,EAAW,CAAC,EAElCY,CAAM,CAACZ,EAAS,CAAGC,EACf5B,GACAA,EAASyB,UAAU,CApBT,IAAI,CAoBaC,EAAYC,EAAUC,GAErDX,AAtBc,IAAI,CAsBZd,IAAI,CAAC,CACPC,KAAM,eACNwB,UAAAA,EACAF,WAAYA,EACZF,OAAQN,EACRS,SAAAA,CACJ,GACJ,CAyBAqD,WAAWlD,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAAEiI,CAAc,CAAE,CACvD,IAAoBtB,EAAe5G,AAArB,IAAI,CAAuBa,OAAO,CAAEsH,EAAgBnI,AAApD,IAAI,CAAsDjB,QAAQ,CAAE0E,EAAc5F,OAAO6F,IAAI,CAAC7C,GACxGkC,EAAW/C,AADD,IAAI,CACG+C,QAAQ,CAQ7B,GAPA/C,AAFc,IAAI,CAEZd,IAAI,CAAC,CACPC,KAAM,aACN0B,QAAAA,EACA4C,YAAAA,EACAlD,OAAQN,EACRS,SAAAA,CACJ,GACI,AAAC6D,EAAQ7D,IAAcwH,EAGtB,CACD,IAAK,IAAInD,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEgC,EAAQ8G,EAAa3H,EAAY4H,EAAkBtD,EAAIC,EAAM,EAAED,EAAG,CAEzGzD,EAAST,CAAO,CADhBJ,EAAagD,CAAW,CAACsB,EAAE,CACC,CAE5BsD,EAAmBxK,OAAOsE,cAAc,CAAC,AAACiG,AAD1CA,CAAAA,EAAcxB,CAAY,CAACnG,EAAW,AAAD,GACoByH,EAAkBE,EAAc9G,GAAQc,WAAW,CACvGgG,EAGIC,IAAqB7G,MACrBA,MAAMC,OAAO,CAAC2G,IACfA,CAAAA,EAAc5G,MAAMO,IAAI,CAACqG,EAAW,EAGnCA,EAAY9I,MAAM,CAAGyD,GAG1BqF,AAFAA,CAAAA,EACI,IAAIC,EAAiBtF,EAAQ,EACrBT,GAAG,CAACsE,CAAY,CAACnG,EAAW,EAVxC2H,EAAc,IAAIC,EAAiBtF,GAYvC6D,CAAY,CAACnG,EAAW,CAAG2H,EAC3B,IAAK,IAAIrD,EAAKrE,GAAY,EAAIsE,EAAO1D,EAAOhC,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAC5DqD,CAAW,CAACrD,EAAE,CAAGzD,CAAM,CAACyD,EAAE,CAE9BhC,EAAWG,KAAKC,GAAG,CAACJ,EAAUzB,EAAOhC,MAAM,CAC/C,CACA,IAAI,CAAC8D,aAAa,CAACL,EACvB,MA5BI,KAAK,CAACgB,WAAWlD,EAASH,EAAU8D,EAAOvE,EAAa,CAAE+D,OAAQ,CAAA,CAAK,IA6BvEmE,GACAA,EAAcvH,aAAa,CAxCjB,IAAI,CAwCqBC,EAASH,GAAY,GAE5DV,AA1Cc,IAAI,CA0CZd,IAAI,CAAC,CACPC,KAAM,kBACN0B,QAAAA,EACA4C,YAAAA,EACAlD,OAAQN,EACRS,SAAAA,CACJ,EACJ,CAgBA4H,YAAYvJ,CAAQ,CAAEkB,CAAW,CAAE,CAC/B,IACIsI,EADEvI,EAAQ,IAAI,CAgBlB,OAdAA,EAAMd,IAAI,CAAC,CACPC,KAAM,cACNoB,OAAQN,EACRlB,SAAAA,EACAsB,SAAUL,EAAMK,QAAQ,AAC5B,GACAL,EAAMK,QAAQ,CAAGL,EACjBA,EAAMjB,QAAQ,CAAGA,EAOVwJ,CANHxJ,EACUA,EAASgB,MAAM,CAACC,GAGhBE,QAAQC,OAAO,CAACH,IAGzBwI,IAAI,CAAC,AAACxI,IACPA,EAAMd,IAAI,CAAC,CACPC,KAAM,mBACNoB,OAAQN,EACRlB,SAAAA,EACAsB,SAAUL,EAAMK,QAAQ,AAC5B,GACOL,IACR,KAAQ,CAAC,AAACyI,IAOT,MANAzI,EAAMd,IAAI,CAAC,CACPC,KAAM,mBACNsJ,MAAAA,EACA1J,SAAAA,EACAsB,SAAUL,EAAMK,QAAQ,AAC5B,GACMoI,CACV,EACJ,CAWAC,sBAAsBtD,CAAkB,CAAEuD,EAAsB,CAAA,CAAK,CAAE,CAEnE,GADA,IAAI,CAACvD,kBAAkB,CAAGA,EACtBuD,EACA,OAEJ,IAAMC,EAAkB,IAAI,CAACvD,eAAe,CAAG,EAAE,CACjD,IAAK,IAAIN,EAAI,EAAGC,EAAOI,EAAmB9F,MAAM,CAAEuJ,EAAe9D,EAAIC,EAAM,EAAED,EAErER,EADJsE,EAAgBzD,CAAkB,CAACL,EAAE,GAEjC6D,CAAAA,CAAe,CAACC,EAAc,CAAG9D,CAAAA,CAG7C,CAyBAd,OAAOC,CAAG,CAAExD,CAAQ,CAAEyD,CAAM,CAAElE,CAAW,CAAE,CACvC,IAAI,CAAC6I,OAAO,CAAC,CAAC5E,EAAI,CAAExD,EAAUyD,EAAQlE,EAC1C,CAuBA6I,QAAQ/H,CAAI,CAAEL,EAAW,IAAI,CAACqC,QAAQ,CAAEoB,CAAM,CAAElE,CAAW,CAAE,CACzD,IAAoBY,EAAUb,AAAhB,IAAI,CAAkBa,OAAO,CAAE4C,EAAc5F,OAAO6F,IAAI,CAAC7C,GAAU9B,EAAWiB,AAA9E,IAAI,CAAgFjB,QAAQ,CAAEgE,EAAWhC,EAAKzB,MAAM,CAClIU,AADc,IAAI,CACZd,IAAI,CAAC,CACPC,KAAM,UACNoB,OAAQN,EACR8C,SAAAA,EACArC,SAAAA,EACAK,KAAAA,CACJ,GACA,IAAK,IAAIgE,EAAI,EAAGyC,EAAK9G,EAAUwD,EAAKa,EAAIhC,EAAU,EAAEgC,EAAG,EAAEyC,EAErD,GAAItD,AADJA,CAAAA,EAAMnD,CAAI,CAACgE,EAAE,AAAD,IACAH,EAAUE,IAAI,CACtB,IAAK,IAAIe,EAAI,EAAGC,EAAOrC,EAAYnE,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EAAG,CACtD,IAAMvE,EAAST,CAAO,CAAC4C,CAAW,CAACoC,EAAE,CAAC,CAClC1B,EACAtD,CAAO,CAAC4C,CAAW,CAACoC,EAAE,CAAC,CAAGrD,EAAiBd,MAAM,CAACJ,EAAQkG,EAAI,EAAG,CAAA,EAAM,CAAC,KAAK,EAAEvF,KAAK,CAGpFX,CAAM,CAACkG,EAAG,CAAG,IAErB,MAEC,GAAItD,aAAe1C,MACpB,IAAK,IAAIqE,EAAI,EAAGC,EAAOrC,EAAYnE,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EACnDhF,CAAO,CAAC4C,CAAW,CAACoC,EAAE,CAAC,CAAC2B,EAAG,CAAGtD,CAAG,CAAC2B,EAAE,MAIxC,KAAK,CAAC5B,OAAOC,EAAKsD,EAAI,KAAK,EAAG,CAAExD,OAAQ,CAAA,CAAK,GAGrD,IAAMI,EAAgBD,EAClBpB,EAAWhC,EAAKzB,MAAM,CACtBoB,EAAWqC,EACf,GAAIqB,EAAgBpE,AAjCN,IAAI,CAiCQ+C,QAAQ,CAAE,CAChC/C,AAlCU,IAAI,CAkCR+C,QAAQ,CAAGqB,EACjB,IAAK,IAAIW,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAAG,CACtD,IAAMtE,EAAagD,CAAW,CAACsB,EAAE,AACjClE,CAAAA,CAAO,CAACJ,EAAW,CAAG+B,EAAiBnB,SAAS,CAACR,CAAO,CAACJ,EAAW,CAAE2D,EAC1E,CACJ,CACIrF,GACAA,EAAS+B,UAAU,CAzCT,IAAI,CAyCaC,EAAML,GAErCV,AA3Cc,IAAI,CA2CZd,IAAI,CAAC,CACPC,KAAM,eACNoB,OAAQN,EACR8C,SAAAA,EACArC,SAAAA,EACAK,KAAAA,CACJ,EACJ,CACJ,CAmBA6D,EAAUE,IAAI,CAAG,CAAC,EAKlBF,EAAUmE,OAAO,CAAG,QA2BpB,GAAM,CAAEvK,SAAUwK,CAAsB,CAAEvK,UAAWwK,CAAuB,CAAEvK,MAAOwK,CAAmB,CAAEC,KAAAA,CAAI,CAAE,CAAI5K,GAWpH,OAAMvB,EAYFoF,YAAYvD,EAAU,CAAC,CAAC,CAAE,CACtB,IAAI,CAACmB,KAAK,CAAG,IA7CgC4E,EA6Cb/F,EAAQD,SAAS,EACjD,IAAI,CAACwK,QAAQ,CAAGvK,EAAQuK,QAAQ,EAAI,CAAEvI,QAAS,CAAC,CAAE,CACtD,CAIA,IAAIwI,SAAU,CACV,MAAO,CAAC,CAAC,IAAI,CAACC,QAAQ,AAC1B,CAeAC,eAAeC,CAAI,CAAEC,CAAU,CAAE,CAC7B,IAAwB5I,EAAU6I,AAAhB,IAAI,CAAsBN,QAAQ,CAACvI,OAAO,AAC5DA,CAAAA,CAAO,CAAC2I,EAAK,CAAGN,EAAoBrI,CAAO,CAAC2I,EAAK,EAAI,CAAC,EAAGC,EAC7D,CAOAE,gBAAgB9I,CAAO,CAAE,CACrB,IACIJ,EADoBgD,EAAc5F,OAAO6F,IAAI,CAAC7C,GAElD,KAAO,AAA4C,UAA5C,MAAQJ,CAAAA,EAAagD,EAAYmG,GAAG,EAAC,GACxCF,AAHc,IAAI,CAGRH,cAAc,CAAC9I,EAAYI,CAAO,CAACJ,EAAW,CAEhE,CAQAvB,KAAKY,CAAC,CAAE,CACJmJ,EAAwB,IAAI,CAAEnJ,EAAEX,IAAI,CAAEW,EAC1C,CAUA+J,eAEAC,CAAoB,CAAE,CAClB,IAAwBjJ,EAAU6I,AAAhB,IAAI,CAAsBN,QAAQ,CAACvI,OAAO,CAAEkJ,EAAQlM,OAAO6F,IAAI,CAAC7C,GAAW,CAAC,GAC9F,GAAIkJ,EAAMzK,MAAM,CACZ,OAAOyK,EAAMC,IAAI,CAAC,CAACxM,EAAGyM,IAAOd,EAAKtI,CAAO,CAACrD,EAAE,CAAC0M,KAAK,CAAE,GAAKf,EAAKtI,CAAO,CAACoJ,EAAE,CAACC,KAAK,CAAE,GAExF,CAWAC,iBAAiBC,CAAoB,CAAE,CACnC,OAAO,IAAI,CAACpK,KAAK,CAACwD,UAAU,CAAC,IAAI,CAACqG,cAAc,CAACO,GACrD,CASAC,MAAO,CAEH,OADApB,EAAwB,IAAI,CAAE,YAAa,CAAEjJ,MAAO,IAAI,CAACA,KAAK,AAAC,GACxDE,QAAQC,OAAO,CAAC,IAAI,CAC/B,CAaAd,GAAGF,CAAI,CAAE6B,CAAQ,CAAE,CACf,OAAOgI,EAAuB,IAAI,CAAE7J,EAAM6B,EAC9C,CAUAsJ,MAAO,CAEH,OADArB,EAAwB,IAAI,CAAE,YAAa,CAAEjJ,MAAO,IAAI,CAACA,KAAK,AAAC,GACxDE,QAAQE,MAAM,CAAC,AAAImK,MAAM,mBACpC,CAOAC,eAAe/G,CAAW,CAAE,CAExB,IAAK,IAAIsB,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACnD2E,AAFc,IAAI,CAERH,cAAc,CAAC9F,CAAW,CAACsB,EAAE,CAAE,CAAEmF,MAAOnF,CAAE,EAE5D,CACA0F,mBAAmBC,CAAe,CAAE,CAChC,IAAMC,EAAiBD,GACnBtJ,EAAuBH,KAAK,CAACyJ,EAAgBvL,IAAI,CAAC,CACtD,OAAO,IAAI,CAACa,KAAK,CACZsI,WAAW,CAACqC,EACb,IAAIA,EAAcD,GAClB,KAAK,GACJlC,IAAI,CAAC,IAAM,IAAI,CACxB,CAOAoC,aAAaC,EAAc,GAAI,CAAE,CAC7B,IAAMnB,EAAY,IAAI,CACtBhK,OAAOoL,YAAY,CAACpB,EAAUJ,QAAQ,EACtCI,EAAUJ,QAAQ,CAAG5J,OAAOqL,UAAU,CAAC,IAAMrB,EACxCW,IAAI,GAAG,KAAQ,CAAC,AAAC5B,GAAUiB,EAAUxK,IAAI,CAAC,CAC3CC,KAAM,YACNsJ,MAAAA,EACAzI,MAAO0J,EAAU1J,KAAK,AAC1B,IACKwI,IAAI,CAAC,KACFkB,EAAUJ,QAAQ,EAClBI,EAAUkB,YAAY,CAACC,EAE/B,GAAIA,EACR,CAIAG,aAAc,CAEVtL,OAAOoL,YAAY,CAACpB,AADF,IAAI,CACQJ,QAAQ,EACtC,OAAOI,AAFW,IAAI,CAELJ,QAAQ,AAC7B,CAUA2B,OAAOzB,CAAI,CAAE,CACT,OAAO,IAAI,CAACJ,QAAQ,CAACvI,OAAO,CAAC2I,EAAK,AACtC,CACJ,CAoBIxM,CAdOA,EA2CRA,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GA7BpBiE,KAAK,CAAG,CAAC,EA4BvBjE,EAAckE,YAAY,CAL1B,SAAsBvD,CAAG,CAAEuN,CAAkB,EACzC,MAAQ,CAAC,CAACvN,GACN,CAACX,EAAciE,KAAK,CAACtD,EAAI,EACzB,CAAC,CAAEX,CAAAA,EAAciE,KAAK,CAACtD,EAAI,CAAGuN,CAAiB,CACvD,EAQyB,IAAMC,EAA4BnO,EAuBzD,CAAEwB,SAAU4M,CAAsB,CAAE3M,UAAW4M,CAAuB,CAAE3G,SAAU4G,CAAsB,CAAE5M,MAAO6M,CAAmB,CAAE,CAAIhN,GAWhJ,OAAMtB,EAYFmF,YAAYvD,CAAO,CAAE,CASjB,IAAI,CAAC2M,WAAW,CAAG,CACf,aAAc,CACVC,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC3CG,GACR,CACJ,EACA,aAAc,CACVL,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC3CG,GACR,EACAC,YAAa,YACjB,EACA,aAAc,CACVN,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC3CG,GACR,CACJ,EACA,WAAY,CACRL,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,IAAMpO,EAAI,IAAIqO,KACd,GAAI,CAACD,EACD,OAAOG,IAEX,IAAIE,EAAO,CAACL,CAAK,CAAC,EAAE,CAOpB,OANIK,EAAQzO,EAAE0O,WAAW,GAAK,IAC1BD,GAAQ,KAGRA,GAAQ,IAELJ,KAAKC,GAAG,CAACG,EAAML,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,CACjD,EACAI,YAAa,UACjB,EACA,WAAY,CACRN,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAG,IAAMA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAClDG,GACR,CACJ,CACJ,EACA,IAAMI,EAAgBX,EAAoBtO,EAAckP,cAAc,CAAEtN,GACpEuN,EAAcF,EAAcG,YAAY,CACxCD,CAAAA,AAAgB,MAAhBA,GAAuBA,AAAgB,MAAhBA,CAAkB,IACzCA,EAAcA,AAAgB,MAAhBA,EAAsB,MAAQ,IAC5C,IAAI,CAACE,aAAa,CACd,AAAIC,OAAO,cAAgBH,EAAc,cAEjD,IAAI,CAACvN,OAAO,CAAGqN,CACnB,CAeAM,UAAUC,CAAK,CAAE,OACb,AAAI,AAAiB,WAAjB,OAAOA,EACAA,EAEP,AAAiB,UAAjB,OAAOA,EACAA,AAAU,KAAVA,GAAgBA,AAAU,MAAVA,GAAiBA,AAAU,UAAVA,EAErC,CAAC,CAAC,IAAI,CAACC,QAAQ,CAACD,EAC3B,CAUAE,OAAOF,CAAK,CAAE,CACV,IAAIG,EACJ,GAAI,AAAiB,UAAjB,OAAOH,EACPG,EAAY,IAAI,CAACC,SAAS,CAACJ,QAE1B,GAAI,AAAiB,UAAjB,OAAOA,EACZG,EAAYH,OAEX,GAAIA,aAAiBb,KACtB,OAAOa,EAGPG,EAAY,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,QAAQ,CAACL,IAE7C,OAAO,IAAIb,KAAKgB,EACpB,CAUAG,cAAcN,CAAK,CAAE,CAMjB,MAAOO,AAL2B,CAAA,CAC9B,OAAUC,AADI,IAAI,CACEP,QAAQ,CAC5B,KAAQO,AAFM,IAAI,CAEAN,MAAM,CACxB,OAAUM,AAHI,IAAI,CAGEH,QAAQ,AAChC,CAAA,CACc,CAACG,AALG,IAAI,CAKGC,SAAS,CAACT,GAAO,CAACpO,IAAI,CAL7B,IAAI,CAKqCoO,EAC/D,CAUAC,SAASD,CAAK,CAAE,CACZ,GAAI,AAAiB,UAAjB,OAAOA,EACP,OAAOA,EAEX,GAAI,AAAiB,WAAjB,OAAOA,EACP,MAAOA,GAAAA,EAEX,GAAI,AAAiB,UAAjB,OAAOA,EAAoB,CAC3B,IAAMU,EAAe,IAAI,CAACb,aAAa,CAIvC,GAHIG,EAAMpF,OAAO,CAAC,KAAO,IACrBoF,CAAAA,EAAQA,EAAMW,OAAO,CAAC,OAAQ,GAAE,EAEhCD,EAAc,CACd,GAAI,CAACA,EAAaE,IAAI,CAACZ,GACnB,OAAOX,IAEXW,EAAQA,EAAMW,OAAO,CAACD,EAAc,QACxC,CACA,OAAO9G,WAAWoG,EACtB,QACA,AAAIA,aAAiBb,KACVa,EAAMa,OAAO,GAEpBb,EACOA,EAAMvF,WAAW,GAErB4E,GACX,CAUAgB,SAASL,CAAK,CAAE,CACZ,MAAO,GAAKA,CAChB,CAmBAc,iBAAiBC,CAAI,CAAEC,CAAK,CAAEnD,CAAI,CAAE,CAChC,IAAqBoD,EAAS,EAAE,CAAEvK,EAAM,EAAE,CACtCwK,EAAS,aAAcC,EAAOC,EAAgB,EAAE,CAAE9I,EAAI,EAAG+I,EAAgB,CAAA,EAE7EC,EAAMlI,EAIN,IAHI,CAAA,CAAC4H,GAASA,EAAQD,EAAKlO,MAAM,AAAD,GAC5BmO,CAAAA,EAAQD,EAAKlO,MAAM,AAAD,EAEfyF,EAAI0I,EAAO1I,IACd,GAAI,AAAmB,KAAA,IAAZyI,CAAI,CAACzI,EAAE,EACdyI,CAAI,CAACzI,EAAE,EAAIyI,CAAI,CAACzI,EAAE,CAACzF,MAAM,CAUzB,IAAKuG,EAAI,EATT+H,EAAQJ,CAAI,CAACzI,EAAE,CACViJ,IAAI,GACJZ,OAAO,CAAC,YAAa,KACrBa,KAAK,CAAC,KACXJ,EAAgB,CACZ,GACA,GACA,GACH,CACWhI,EAAI+H,EAAMtO,MAAM,CAAEuG,IACtBA,EAAIgI,EAAcvO,MAAM,EACxByO,CAAAA,EAAOG,SAASN,CAAK,CAAC/H,EAAE,CAAE,GAAE,IAExB1C,CAAG,CAAC0C,EAAE,CAAG,AAAC,CAAC1C,CAAG,CAAC0C,EAAE,EAAI1C,CAAG,CAAC0C,EAAE,CAAGkI,EAAQA,EAAO5K,CAAG,CAAC0C,EAAE,CAC/C,AAAqB,KAAA,IAAd6H,CAAM,CAAC7H,EAAE,CACZ6H,CAAM,CAAC7H,EAAE,GAAKkI,GACdL,CAAAA,CAAM,CAAC7H,EAAE,CAAG,CAAA,CAAI,EAIpB6H,CAAM,CAAC7H,EAAE,CAAGkI,EAEZA,EAAO,GACHA,EAAO,IACPF,CAAa,CAAChI,EAAE,CAAG,KAGnBgI,CAAa,CAAChI,EAAE,CAAG,OAIlBkI,EAAO,IACZA,GAAQ,IACRF,CAAa,CAAChI,EAAE,CAAG,KACnBiI,EAAgB,CAAA,GAEVD,CAAa,CAAChI,EAAE,CAACvG,MAAM,EAC7BuO,CAAAA,CAAa,CAAChI,EAAE,CAAG,IAAG,GAO9C,GAAIiI,EAAe,CAEf,IAAKjI,EAAI,EAAGA,EAAI6H,EAAOpO,MAAM,CAAEuG,IACvB6H,AAAc,CAAA,IAAdA,CAAM,CAAC7H,EAAE,CACL1C,CAAG,CAAC0C,EAAE,CAAG,IACTgI,AAAqB,OAArBA,CAAa,CAAChI,EAAE,EAChBgI,AAAqB,SAArBA,CAAa,CAAChI,EAAE,EAChBgI,CAAAA,CAAa,CAAChI,EAAE,CAAG,IAAG,EAGrB1C,CAAG,CAAC0C,EAAE,CAAG,IAAMgI,AAAqB,OAArBA,CAAa,CAAChI,EAAE,EACpCgI,CAAAA,CAAa,CAAChI,EAAE,CAAG,IAAG,CAKD,CAAA,IAAzBgI,EAAcvO,MAAM,EACpBuO,AAAqB,OAArBA,CAAa,CAAC,EAAE,EAChBA,AAAqB,OAArBA,CAAa,CAAC,EAAE,EAChBA,CAAAA,CAAa,CAAC,EAAE,CAAG,IAAG,EAE1BF,EAASE,EAAcM,IAAI,CAAC,IAGhC,CAKA,OAHI7D,GACAoB,CAAAA,AAjFW,IAAI,CAiFR7M,OAAO,CAACuP,UAAU,CAAGT,CAAK,EAE9BA,CACX,CAOAzO,KAAKY,CAAC,CAAE,CACJuL,EAAwB,IAAI,CAAEvL,EAAEX,IAAI,CAAEW,EAC1C,CAUAuO,OAEA3E,CAAS,CAAE7K,CAAO,CAEhB,CAME,MALA,IAAI,CAACK,IAAI,CAAC,CACNC,KAAM,cACN0B,QAAS,EAAE,CACXyN,QAAS,EAAE,AACf,GACM,AAAI/D,MAAM,kBACpB,CAOAgE,UAAW,CACP,MAAM,AAAIhE,MAAM,kBACpB,CAUA2C,UAAUT,CAAK,CAAE,CAEb,IAAIpK,EAAS,SACb,GAAI,AAAiB,UAAjB,OAAOoK,EAAoB,CAC3B,IAAM+B,EAAcvB,AAHN,IAAI,CAGYe,IAAI,CAAC,CAAC,EAAEvB,EAAM,CAAC,EAAGH,EAAgBW,AAHlD,IAAI,CAGwDX,aAAa,CACnFmC,EAAmBxB,AAJT,IAAI,CAIee,IAAI,CAACQ,EAAa,CAAA,GAC/ClC,GACAmC,CAAAA,EAAoBnC,EAAce,IAAI,CAACoB,GACnCA,EAAiBrB,OAAO,CAACd,EAAe,SACxC,EAAE,EAEV,IAAMoC,EAAarI,WAAWoI,EAC1B,EAACA,IAAqBC,EAEtBjC,EAAQiC,EAKRrM,EAASiJ,EADS2B,AAjBR,IAAI,CAiBcJ,SAAS,CAACJ,IACO,OAAS,QAE9D,CAKA,MAJqB,UAAjB,OAAOA,GAEPpK,CAAAA,EAASoK,EAAQ,QAAyB,OAAS,QAAO,EAEvDpK,CACX,CAaAhD,GAAGF,CAAI,CAAE6B,CAAQ,CAAE,CACf,OAAOoK,EAAuB,IAAI,CAAEjM,EAAM6B,EAC9C,CAOA2N,MAEA9P,CAAO,CAAE,CAML,MALA,IAAI,CAACK,IAAI,CAAC,CACNC,KAAM,aACN0B,QAAS,EAAE,CACXyN,QAAS,EAAE,AACf,GACM,AAAI/D,MAAM,kBACpB,CAWAsC,UAAUJ,CAAK,CAAEmC,CAAc,CAAE,CAC7B,IAAwB/P,EAAUoO,AAAhB,IAAI,CAAsBpO,OAAO,CAC/CuP,EAAaQ,GAAkB/P,EAAQuP,UAAU,CAAE/L,EAASyJ,IAAKnO,EAAKgQ,EAAQhC,EAClF,GAAI9M,EAAQgO,SAAS,CACjBxK,EAASxD,EAAQgO,SAAS,CAACJ,OAE1B,CAED,GAAK2B,EAgBDT,CAAAA,EAASV,AAvBC,IAAI,CAuBKzB,WAAW,CAAC4C,EAAW,AAAD,GAGrCT,CAAAA,EAASV,AA1BH,IAAI,CA0BSzB,WAAW,CAAC,aAAa,AAAD,EAE/CG,CAAAA,EAAQc,EAAMd,KAAK,CAACgC,EAAOlC,KAAK,CAAA,GAE5BpJ,CAAAA,EAASsL,EAAOjC,MAAM,CAACC,EAAK,OAtBhC,IAAKhO,KAAOsP,AARF,IAAI,CAQQzB,WAAW,CAG7B,GAFAmC,EAASV,AATH,IAAI,CASSzB,WAAW,CAAC7N,EAAI,CACnCgO,EAAQc,EAAMd,KAAK,CAACgC,EAAOlC,KAAK,EACrB,CAEP2C,EAAazQ,EAGb0E,EAASsL,EAAOjC,MAAM,CAACC,GACvB,KACJ,CAgBJ,CAACA,IAIG,AAAiB,UAAjB,MAHJA,CAAAA,EAAQC,KAAK+C,KAAK,CAAClC,EAAK,GAIpBd,AAAU,OAAVA,GACAA,EAAMkD,OAAO,CACbxM,EAAUsJ,EAAMkD,OAAO,GACnBlD,AACI,IADJA,EAAMmD,iBAAiB,GAItBxD,EAAuBK,KAC5BtJ,EAASsJ,EAAQ,AAAwC,IAAxC,AAAC,IAAIC,KAAKD,GAAQmD,iBAAiB,GAE1B,KAA1BrC,EAAMpF,OAAO,CAAC,SACV,AAAqC,OAArC,AAAC,IAAIuE,KAAKvJ,GAAS4J,WAAW,IAC9B5J,CAAAA,EAASyJ,GAAE,GAI3B,CACA,OAAOzJ,CACX,CAaA2L,KAAKe,CAAG,CAAEC,CAAM,CAAE,CAQd,MAPmB,UAAf,OAAOD,IACPA,EAAMA,EAAI3B,OAAO,CAAC,aAAc,IAE5B4B,GAAU,YAAY3B,IAAI,CAAC0B,IAC3BA,CAAAA,EAAMA,EAAI3B,OAAO,CAAC,MAAO,GAAE,GAG5B2B,CACX,CACJ,CASA9R,EAAckP,cAAc,CAAG,CAC3BiC,WAAY,GACZa,kBAAmB,GACnBC,YAAa,EACbC,UAAWtH,OAAOuH,SAAS,CAC3BC,SAAU,EACVC,OAAQzH,OAAOuH,SAAS,CACxBG,gBAAiB,CAAA,EACjBC,qBAAsB,CAAA,CAC1B,EAuBIvS,CAdOA,EA8DRA,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAhDpBgE,KAAK,CAAG,CAAC,EA0BvBhE,EAAciE,YAAY,CAL1B,SAAsBvD,CAAG,CAAE8R,CAAkB,EACzC,MAAQ,CAAC,CAAC9R,GACN,CAACV,EAAcgE,KAAK,CAACtD,EAAI,EACzB,CAAC,CAAEV,CAAAA,EAAcgE,KAAK,CAACtD,EAAI,CAAG8R,CAAiB,CACvD,EAsBAxS,EAAcyS,mBAAmB,CAPjC,SAA6B7O,EAAU,EAAE,CAAEyN,EAAU,EAAE,EACnD,IAAMtO,EAAQ,IAj4B+B4E,EAk4B7C,IAAK,IAAIG,EAAI,EAAGC,EAAO9B,KAAKC,GAAG,CAACmL,EAAQhP,MAAM,CAAEuB,EAAQvB,MAAM,EAAGyF,EAAIC,EAAM,EAAED,EACzE/E,EAAM8D,SAAS,CAACwK,CAAO,CAACvJ,EAAE,EAAI,CAAC,EAAEA,EAAE,CAAC,CAAElE,CAAO,CAACkE,EAAE,EAEpD,OAAO/E,CACX,EAQyB,IAAM2P,EAA4B1S,CA6B/D,OAAM2S,EAMFxN,YAAYyN,EAAW,CAAC,CAAC,CAAE,CACvB,IAAI,CAACC,gBAAgB,CAAG,EAAE,CAC1B,IAAI,CAACC,WAAW,CAAG,CAAC,EACpB,IAAI,CAACF,QAAQ,CAAGA,CACpB,CAgCAG,YAAYC,CAAO,CAAEC,CAAK,CAAEC,CAAQ,CAAE,CAClC,IAAMJ,EAAc,IAAI,CAACA,WAAW,CAACE,EAAQ,CAAI,IAAI,CAACF,WAAW,CAACE,EAAQ,EACtE,CAAC,EAIL,MADAG,AAFkBL,CAAAA,CAAW,CAACG,EAAM,CAAIH,CAAW,CAACG,EAAM,EACtD,EAAE,EACIrQ,IAAI,CAACsQ,GACR,IAAI,AACf,CAIAE,iBAAiBvQ,CAAC,CAAE,CAChB,MAAO,AAACA,CAAAA,AAAkB,aAAlBA,EAAEwQ,MAAM,CAACnR,IAAI,CACjB,CACIW,EAAEE,KAAK,CAAC8C,EAAE,CACVhD,EAAEwQ,MAAM,CAAChP,MAAM,CACfxB,EAAEwQ,MAAM,CAACpM,GAAG,CACZpE,EAAEwQ,MAAM,CAACJ,KAAK,CACdpQ,EAAEwQ,MAAM,CAACnR,IAAI,CAChB,CACD,CACIW,EAAEE,KAAK,CAAC8C,EAAE,CACVhD,EAAEwQ,MAAM,CAACzP,OAAO,CAChBf,EAAEwQ,MAAM,CAACC,QAAQ,CACjBzQ,EAAEwQ,MAAM,CAACE,OAAO,CAChB1Q,EAAEwQ,MAAM,CAACJ,KAAK,CACdpQ,EAAEwQ,MAAM,CAACnR,IAAI,CAChB,AAAD,EAAGgP,IAAI,CAAC,KAChB,CA+BAsC,WAAWzQ,CAAK,CAAEsQ,CAAM,CAAEI,CAAK,CAAEC,CAAO,CAAE,CACtC,IAAMV,EAAUjQ,EAAM8C,EAAE,CAAEoN,EAAQI,EAAOJ,KAAK,CAAEE,EAAa,IAAI,CAACL,WAAW,CAACE,EAAQ,EAClF,IAAI,CAACF,WAAW,CAACE,EAAQ,CAACC,EAAM,CACpC,GAAIE,EAAW,CACX,IAAMP,EAAW,IAAI,CAACA,QAAQ,CAACI,EAAQ,CAAI,IAAI,CAACJ,QAAQ,CAACI,EAAQ,EAAI,CAAC,EAChEW,EAAUf,CAAQ,CAACS,EAAOJ,KAAK,CAAC,EAAI,EAAE,CACxCS,IACKC,EAAQtR,MAAM,EACfuQ,CAAAA,CAAQ,CAACS,EAAOJ,KAAK,CAAC,CAAGU,CAAM,EAEU,KAAzChB,EAAWiB,QAAQ,CAACP,EAAQM,IAC5BA,EAAQ/Q,IAAI,CAACyQ,IAGrB,IAAMxQ,EAAI,CACNwQ,OAAAA,EACAM,QAAAA,EACA5Q,MAAAA,CACJ,EACI0Q,GACA5Q,CAAAA,EAAE4Q,KAAK,CAAGA,CAAI,EAElB,IAAMZ,EAAmB,IAAI,CAACA,gBAAgB,CAAEgB,EAAc,IAAI,CAACT,gBAAgB,CAACvQ,GACpF,GAAIgQ,EAAiBzI,OAAO,CAACyJ,IAAgB,EAEzC,OAAO,IAAI,CAEf,GAAI,CACA,IAAI,CAAChB,gBAAgB,CAACjQ,IAAI,CAACiR,GAC3B,IAAK,IAAI/L,EAAI,EAAGC,EAAOoL,EAAU9Q,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACjDqL,CAAS,CAACrL,EAAE,CAAC1G,IAAI,CAAC,IAAI,CAAEyB,EAEhC,QACQ,CACJ,IAAMoK,EAAQ,IAAI,CAAC4F,gBAAgB,CAACzI,OAAO,CAACyJ,GACxC5G,GAAS,GACT,IAAI,CAAC4F,gBAAgB,CAACpO,MAAM,CAACwI,EAAO,EAE5C,CACJ,CACA,OAAO,IAAI,AACf,CAeA6G,YAAYd,CAAO,CAAEK,CAAM,CAAE,CACzB,IAAMM,EAAW,IAAI,CAACf,QAAQ,CAACI,EAAQ,EACnC,IAAI,CAACJ,QAAQ,CAACI,EAAQ,CAACK,EAAOJ,KAAK,CAAC,CACxC,GAAIU,EAAS,CACT,IAAM1G,EAAQ0F,EAAWiB,QAAQ,CAACP,EAAQM,GACtC1G,GAAS,GACT0G,EAAQlP,MAAM,CAACwI,EAAO,EAE9B,CACA,OAAO,IAAI,AACf,CAkBA8G,eAAef,CAAO,CAAEC,CAAK,CAAEC,CAAQ,CAAE,CACrC,IAAMC,EAAa,IAAI,CAACL,WAAW,CAACE,EAAQ,EACxC,IAAI,CAACF,WAAW,CAACE,EAAQ,CAACC,EAAM,CACpC,GAAIE,EAAW,CACX,IAAMlG,EAAQkG,EAAU/I,OAAO,CAAC8I,GAC5BjG,GAAS,GACTkG,EAAU1O,MAAM,CAACwI,EAAO,EAEhC,CACA,OAAO,IAAI,AACf,CACJ,CAUA0F,EAAW7G,OAAO,CAAG,QASrB,AAAC,SAAU6G,CAAU,EAoHjB,SAASqB,EAAQX,CAAM,CAAEY,CAAY,EACjC,GAAIZ,AAAgB,UAAhBA,EAAOnR,IAAI,CACX,OAAOmR,EAEX,IAAMa,EAAQ,CACVhS,KAAM,QACNoR,SAAWD,EAAOpM,GAAG,EAChBgN,CAAAA,GAAgBA,EAAaX,QAAQ,AAAD,GACrC,EACJC,QAAUF,EAAOpM,GAAG,EACfgN,CAAAA,GAAgBA,EAAaV,OAAO,AAAD,GACpC3I,OAAOuH,SAAS,CACpBc,MAAOI,EAAOJ,KAAK,AACvB,EAIA,OAH6B,KAAA,IAAlBI,EAAOhP,MAAM,EACpB6P,CAAAA,EAAMtQ,OAAO,CAAG,CAACyP,EAAOhP,MAAM,CAAC,AAAD,EAE3B6P,CACX,CA5FAvB,EAAWiB,QAAQ,CA3BnB,SAAkBO,CAAM,CAAER,CAAO,EAC7B,GAAIQ,AAAgB,aAAhBA,EAAOjS,IAAI,CACX,CAAA,IAAK,IAAImR,EAAQvL,EAAI,EAAGC,EAAO4L,EAAQtR,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAEvD,GAAIuL,AAAgB,aAAhBA,AADJA,CAAAA,EAASM,CAAO,CAAC7L,EAAE,AAAD,EACP5F,IAAI,EACXmR,EAAOJ,KAAK,GAAKkB,EAAOlB,KAAK,EAC7BI,EAAOhP,MAAM,GAAK8P,EAAO9P,MAAM,EAC/BgP,EAAOpM,GAAG,GAAKkN,EAAOlN,GAAG,CACzB,OAAOa,CAEf,KAEC,CACD,IAAMsM,EAAeC,KAAKC,SAAS,CAACH,EAAOvQ,OAAO,EAClD,IAAK,IAAIyP,EAAQvL,EAAI,EAAGC,EAAO4L,EAAQtR,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAEvD,GAAIuL,AAAgB,UAAhBA,AADJA,CAAAA,EAASM,CAAO,CAAC7L,EAAE,AAAD,EACP5F,IAAI,EACXmR,EAAOJ,KAAK,GAAKkB,EAAOlB,KAAK,EAC7BI,EAAOC,QAAQ,GAAKa,EAAOb,QAAQ,EACnCD,EAAOE,OAAO,GAAKY,EAAOZ,OAAO,EACjCc,KAAKC,SAAS,CAACjB,EAAOzP,OAAO,IAAMwQ,EACnC,OAAOtM,CAGnB,CACA,OAAO,EACX,EAoBA6K,EAAW4B,OAAO,CAdlB,SAAiBC,CAAO,CAAEC,CAAO,QAC7B,AAAID,AAAiB,aAAjBA,EAAQtS,IAAI,EAAmBuS,AAAiB,aAAjBA,EAAQvS,IAAI,CACnCsS,EAAQnQ,MAAM,GAAKoQ,EAAQpQ,MAAM,EACrCmQ,EAAQvN,GAAG,GAAKwN,EAAQxN,GAAG,EAC3BuN,EAAQvB,KAAK,GAAKwB,EAAQxB,KAAK,CAElB,UAAjBuB,EAAQtS,IAAI,EAAgBuS,AAAiB,UAAjBA,EAAQvS,IAAI,EAChCsS,EAAQlB,QAAQ,GAAKmB,EAAQnB,QAAQ,EACzCkB,EAAQjB,OAAO,GAAKkB,EAAQlB,OAAO,EAClCc,KAAKC,SAAS,CAACE,EAAQ5Q,OAAO,IAC3ByQ,KAAKC,SAAS,CAACG,EAAQ7Q,OAAO,CAG9C,EAqBA+O,EAAW+B,SAAS,CAfpB,SAAmBP,CAAM,CAAED,CAAK,EACT,aAAfA,EAAMhS,IAAI,EACVgS,CAAAA,EAAQF,EAAQE,EAAK,EAEL,aAAhBC,EAAOjS,IAAI,EACXiS,CAAAA,EAASH,EAAQG,EAAQD,EAAK,EAElC,IAAMS,EAAgBR,EAAOvQ,OAAO,CAC9BgR,EAAeV,EAAMtQ,OAAO,CAClC,OAAQuQ,EAAOb,QAAQ,EAAIY,EAAMZ,QAAQ,EACrCa,EAAOZ,OAAO,EAAIW,EAAMX,OAAO,EAC9B,CAAA,CAACoB,GACE,CAACC,GACDD,EAAcE,KAAK,CAAC,AAACxQ,GAAWuQ,EAAaxK,OAAO,CAAC/F,IAAW,EAAC,CAC7E,EAgCAsO,EAAWmC,WAAW,CA3BtB,SAAqBzB,CAAM,EACvB,GAAIA,AAAgB,aAAhBA,EAAOnR,IAAI,CACX,MAAO,CAACmR,EAAO,CAEnB,IAAMzP,EAAWyP,EAAOzP,OAAO,EAAI,EAAE,CAC/BmR,EAAY,EAAE,CACd9B,EAAQI,EAAOJ,KAAK,CAC1B,IAAK,IAAIhM,EAAMoM,EAAOC,QAAQ,CAAE0B,EAAS3B,EAAOE,OAAO,CAAEtM,EAAM+N,EAAQ,EAAE/N,EAAK,CAC1E,GAAI,CAACrD,EAAQvB,MAAM,CAAE,CACjB0S,EAAUnS,IAAI,CAAC,CACXV,KAAM,WACN+E,IAAAA,EACAgM,MAAAA,CACJ,GACA,QACJ,CACA,IAAK,IAAI5O,EAAS,EAAG4Q,EAAYrR,EAAQvB,MAAM,CAAEgC,EAAS4Q,EAAW,EAAE5Q,EACnE0Q,EAAUnS,IAAI,CAAC,CACXV,KAAM,WACNmC,OAAQT,CAAO,CAACS,EAAO,CACvB4C,IAAAA,EACAgM,MAAAA,CACJ,EAER,CACA,OAAO8B,CACX,EAwBApC,EAAWqB,OAAO,CAAGA,CACzB,EAAGrB,GAAeA,CAAAA,EAAa,CAAC,CAAA,GAMH,IAAMuC,EAAmBvC,EA6BnBwC,EARV,CACrBC,WAAY,EAAE,AAClB,CAuCA,OAAMC,EAMFlQ,YAAYvD,EAAUuT,CAAqB,CAAE,CACzCvT,EAAQwT,UAAU,CAAIxT,EAAQwT,UAAU,EAAI,EAAE,CAC9C,IAAI,CAACA,UAAU,CAAG,CAAC,EACnB,IAAI,CAACxT,OAAO,CAAGA,EACf,IAAI,CAAC0T,OAAO,CAAG,CAAC,CACpB,CAcArT,KAAKY,CAAC,CAAE,CACJvB,IAA6CE,SAAS,CAAC,IAAI,CAAEqB,EAAEX,IAAI,CAAEW,EACzE,CAYA0S,aAAaC,CAAW,CAAE,CACtB,IAAM/I,EAAY,IAAI,CAAC2I,UAAU,CAACI,EAAY,CAE9C,GAAI/I,EACA,OAAOxJ,QAAQC,OAAO,CAACuJ,GAE3B,IAAIgJ,EAAc,IAAI,CAACH,OAAO,CAACE,EAAY,CAE3C,GAAI,CAACC,EAAa,CACdA,EAAc,IAAI,CAACH,OAAO,CAACE,EAAY,CAAG,EAAE,CAC5C,IAAME,EAAmB,IAAI,CAACC,mBAAmB,CAACH,GAClD,GAAI,CAACE,EACD,MAAM,AAAIpI,MAAM,CAAC,WAAW,EAAEkI,EAAY,YAAY,CAAC,EAG3D,IAAI,CACCI,aAAa,CAACF,GACdnK,IAAI,CAAC,AAACkB,IACP,OAAO,IAAI,CAAC6I,OAAO,CAACE,EAAY,CAChC,IAAK,IAAI1N,EAAI,EAAGC,EAAO0N,EAAYpT,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACnD2N,CAAW,CAAC3N,EAAE,CAAC,EAAE,CAAC2E,EAE1B,GAAG,KAAQ,CAAC,AAACjB,IACT,OAAO,IAAI,CAAC8J,OAAO,CAACE,EAAY,CAChC,IAAK,IAAI1N,EAAI,EAAGC,EAAO0N,EAAYpT,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACnD2N,CAAW,CAAC3N,EAAE,CAAC,EAAE,CAAC0D,EAE1B,EACJ,CAEA,OAAO,IAAIvI,QAAQ,CAACC,EAASC,KACzBsS,EAAY7S,IAAI,CAAC,CAACM,EAASC,EAAO,CACtC,EACJ,CASA0S,iBAAkB,CACd,IAAMT,EAAa,IAAI,CAACxT,OAAO,CAACwT,UAAU,CAAEU,EAAe,EAAE,CAC7D,IAAK,IAAIhO,EAAI,EAAGC,EAAOqN,EAAW/S,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAClDgO,EAAalT,IAAI,CAACwS,CAAU,CAACtN,EAAE,CAACjC,EAAE,EAEtC,OAAOiQ,CACX,CAYAH,oBAAoBH,CAAW,CAAE,CAC7B,IAAMJ,EAAa,IAAI,CAACxT,OAAO,CAACwT,UAAU,CAC1C,IAAK,IAAItN,EAAI,EAAGC,EAAOqN,EAAW/S,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAClD,GAAIsN,CAAU,CAACtN,EAAE,CAACjC,EAAE,GAAK2P,EACrB,OAAOJ,CAAU,CAACtN,EAAE,AAGhC,CAYAiO,kBAAkBP,CAAW,CAAE,CAC3B,OAAO,IAAI,CACND,YAAY,CAACC,GACbjK,IAAI,CAAC,AAACkB,GAAcA,EAAU1J,KAAK,CAC5C,CAWAiT,eAAeR,CAAW,CAAE,CACxB,MAAO,CAAC,IAAI,CAACJ,UAAU,CAACI,EAAY,AACxC,CAYAI,cAAchU,CAAO,CAAE,CACnB,OAAO,IAAIqB,QAAQ,CAACC,EAASC,KACzB,IAAI,CAAClB,IAAI,CAAC,CACNC,KAAM,OACNN,QAAAA,CACJ,GACA,IAAMqU,EAAiB/H,EAAyBlK,KAAK,CAACpC,EAAQM,IAAI,CAAC,CACnE,GAAI,CAAC+T,EACD,MAAM,AAAI3I,MAAM,CAAC,2BAA2B,EAAE1L,EAAQM,IAAI,CAAC,CAAC,CAAC,EAIjEuK,AAFkB,IAAIwJ,EAAerU,EAAQA,OAAO,EAG/CwL,IAAI,GACJ7B,IAAI,CAAC,AAACkB,IACP,IAAI,CAAC2I,UAAU,CAACxT,EAAQiE,EAAE,CAAC,CAAG4G,EAC9B,IAAI,CAACxK,IAAI,CAAC,CACNC,KAAM,YACNN,QAAAA,CACJ,GACAsB,EAAQuJ,EACZ,GAAG,KAAQ,CAACtJ,EAChB,EACJ,CAeAf,GAAGF,CAAI,CAAE6B,CAAQ,CAAE,CACf,OAAOzC,IAA6CC,QAAQ,CAAC,IAAI,CAAEW,EAAM6B,EAC7E,CAOAmS,oBAAoBtU,CAAO,CAAE,CACzB,IAAMwT,EAAa,IAAI,CAACxT,OAAO,CAACwT,UAAU,CAAEe,EAAY,IAAI,CAACf,UAAU,CACvE,IAAI,CAACnT,IAAI,CAAC,CACNC,KAAM,sBACNN,QAAAA,CACJ,GACA,IAAK,IAAIkG,EAAI,EAAGC,EAAOqN,EAAW/S,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAClD,GAAIsN,CAAU,CAACtN,EAAE,CAACjC,EAAE,GAAKjE,EAAQiE,EAAE,CAAE,CACjCuP,EAAW3Q,MAAM,CAACqD,EAAG,GACrB,KACJ,CAEAqO,CAAS,CAACvU,EAAQiE,EAAE,CAAC,GACrBsQ,CAAS,CAACvU,EAAQiE,EAAE,CAAC,CAACkI,WAAW,GACjC,OAAOoI,CAAS,CAACvU,EAAQiE,EAAE,CAAC,EAEhCuP,EAAWxS,IAAI,CAAChB,GAChB,IAAI,CAACK,IAAI,CAAC,CACNC,KAAM,2BACNN,QAAAA,CACJ,EACJ,CACJ,CAUAyT,EAASvJ,OAAO,CAAG,QA8BnB,IAAMsK,EAAgB,kBAKhBC,EAAiB,qCAKjBC,EAAiB,oCAKjBC,EAAiB,uBAIjBC,EAAiB,0BAQjBC,EAAgB,4CAQhBC,EAAkB,8DAMlBC,EAAoB,gCAMpBC,EAAsB,sCAiB5B,SAASC,EAAmBC,CAAI,EAC5B,IAAIC,EAAkB,EACtB,IAAK,IAAIjP,EAAI,EAAGC,EAAO+O,EAAKzU,MAAM,CAAE2U,EAAMC,EAAkB,EAAGnP,EAAIC,EAAM,EAAED,EAAG,CAE1E,GAAIkP,AAAS,MADbA,CAAAA,EAAOF,CAAI,CAAChP,EAAE,AAAD,EACK,CACTiP,GACDE,CAAAA,EAAkBnP,EAAI,CAAA,EAE1B,EAAEiP,EACF,QACJ,CACA,GAAIC,AAAS,MAATA,GAEI,GAACD,EACD,OAAOD,EAAKI,SAAS,CAACD,EAAiBnP,EAGnD,CACA,GAAIiP,EAAkB,EAAG,CACrB,IAAMvL,EAAQ,AAAI8B,MAAM,0BAExB,OADA9B,EAAMe,IAAI,CAAG,oBACPf,CACV,CACA,MAAO,EACX,CAYA,SAAS2L,GAAcL,CAAI,EACvB,IAAIpS,EAAQ,GACZ,IAAK,IAAIoD,EAAI,EAAGC,EAAO+O,EAAKzU,MAAM,CAAE2U,EAAMI,EAAW,CAAA,EAAOtP,EAAIC,EAAM,EAAED,EAAG,CAEvE,GAAIkP,AAAS,OADbA,CAAAA,EAAOF,CAAI,CAAChP,EAAE,AAAD,EACM,CACfsP,EAAW,CAACA,EACZ,QACJ,CACA,GAAIA,EAAU,CACVA,EAAW,CAAA,EACX,QACJ,CACA,GAAIJ,AAAS,MAATA,EAAc,CACd,IAAItS,CAAAA,EAAQ,CAAA,EAIR,OAAOoS,EAAKI,SAAS,CAACxS,EAAQ,EAAGoD,GAHjCpD,EAAQoD,CAKhB,CACJ,CACA,IAAM0D,EAAQ,AAAI8B,MAAM,qBAExB,OADA9B,EAAMe,IAAI,CAAG,oBACPf,CACV,CAgBA,SAAS6L,GAAcP,CAAI,CAAEQ,CAAqB,MAC1C5I,EAGJ,GADAA,EAAQoI,EAAKpI,KAAK,CAACgI,GACR,CACP,IAAMa,EAAuB7I,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACrD8I,EAAoB9I,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAClD+I,EAAqB/I,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACnDgJ,EAAkBhJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAChDwF,EAAQ,CACVhS,KAAM,QACNyV,YAAcJ,EACVtG,SAASvC,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CjG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7BkJ,SAAWJ,EACPvG,SAASvC,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CjG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7BwD,UAAYuF,EACRxG,SAASvC,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CjG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7B2D,OAASqF,EACLzG,SAASvC,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CjG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,CACjC,EAaA,OAZI6I,GACArD,CAAAA,EAAMqD,mBAAmB,CAAG,CAAA,CAAG,EAE/BC,GACAtD,CAAAA,EAAMsD,gBAAgB,CAAG,CAAA,CAAG,EAE5BC,GACAvD,CAAAA,EAAMuD,iBAAiB,CAAG,CAAA,CAAG,EAE7BC,GACAxD,CAAAA,EAAMwD,cAAc,CAAG,CAAA,CAAG,EAEvBxD,CACX,CAGA,GADAxF,EAAQoI,EAAKpI,KAAK,CAAC+H,GACR,CACP,IAAMc,EAAsB7I,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACjC8I,EAAmB9I,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC9B+I,EAAoB/I,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC/BgJ,EAAiBhJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC5BwF,EAAQ,CACVhS,KAAM,QACNyV,YAAaE,GAAqBN,EAC9B7I,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,IAAM,EAC7BU,SAAU3G,SAASuG,EACf9I,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,GAAI,IAAM,EACjChF,UAAW2F,GAAqBJ,EAC5B/I,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,IAAM,EAC7B7E,OAAQpB,SAASyG,EACbhJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,GAAI,IAAM,CACrC,EAaA,OAZIK,GACArD,CAAAA,EAAMqD,mBAAmB,CAAG,CAAA,CAAG,EAE/BC,GACAtD,CAAAA,EAAMsD,gBAAgB,CAAG,CAAA,CAAG,EAE5BC,GACAvD,CAAAA,EAAMuD,iBAAiB,CAAG,CAAA,CAAG,EAE7BC,GACAxD,CAAAA,EAAMwD,cAAc,CAAG,CAAA,CAAG,EAEvBxD,CACX,CAEA,IAAM4D,EAAUC,GAAajB,EAAMQ,GACnC,OAAQQ,AAAmB,IAAnBA,EAAQzV,MAAM,EAAU,AAAsB,UAAtB,OAAOyV,CAAO,CAAC,EAAE,CAC7CA,CAAO,CAAC,EAAE,CACVA,CACR,CAqEA,SAASC,GAAajB,CAAI,CAAEQ,CAAqB,EAC7C,IAAMjI,EAAiBiI,EACnBhB,EACAD,EAAiByB,EAAU,EAAE,CAC7BpJ,EAAOsJ,EAAO,AAAClB,CAAAA,AAAY,MAAZA,CAAI,CAAC,EAAE,CAAWA,EAAKI,SAAS,CAAC,GAAKJ,CAAG,EAAG/F,IAAI,GACnE,KAAOiH,GAAM,CAGT,GADAtJ,EAAQsJ,EAAKtJ,KAAK,CAACkI,GACR,CACP,IAAMqB,EAAkBvJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAChDwJ,EAAexJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC7CyJ,EAAY,CACdjW,KAAM,YACNmC,OAAS4T,EACLhH,SAASvC,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CjG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7BzH,IAAMiR,EACFjH,SAASvC,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CjG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,CACjC,EACIuJ,GACAE,CAAAA,EAAUF,cAAc,CAAG,CAAA,CAAG,EAE9BC,GACAC,CAAAA,EAAUD,WAAW,CAAG,CAAA,CAAG,EAE/BJ,EAAQlV,IAAI,CAACuV,GACbH,EAAOA,EAAKd,SAAS,CAACxI,CAAK,CAAC,EAAE,CAACrM,MAAM,EAAE0O,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQsJ,EAAKtJ,KAAK,CAACiI,GACR,CACP,IAAMsB,EAAiBvJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC5BwJ,EAAcxJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACzByJ,EAAY,CACdjW,KAAM,YACNmC,OAAQwT,GAAqBI,EACzBvJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,IAAM,EAC7BjQ,IAAKgK,SAASiH,EACVxJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAACwI,SAAS,CAAC,GAAI,IAAM,CACrC,EACIe,GACAE,CAAAA,EAAUF,cAAc,CAAG,CAAA,CAAG,EAE9BC,GACAC,CAAAA,EAAUD,WAAW,CAAG,CAAA,CAAG,EAE/BJ,EAAQlV,IAAI,CAACuV,GACbH,EAAOA,EAAKd,SAAS,CAACxI,CAAK,CAAC,EAAE,CAACrM,MAAM,EAAE0O,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQsJ,EAAKtJ,KAAK,CAAC8H,GACR,CACPsB,EAAQlV,IAAI,CAAC8L,CAAK,CAAC,EAAE,EACrBsJ,EAAOA,EAAKd,SAAS,CAACxI,CAAK,CAAC,EAAE,CAACrM,MAAM,EAAE0O,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQsJ,EAAKtJ,KAAK,CAAC0H,GACR,CACP0B,EAAQlV,IAAI,CAAC8L,AAAa,SAAbA,CAAK,CAAC,EAAE,EACrBsJ,EAAOA,EAAKd,SAAS,CAACxI,CAAK,CAAC,EAAE,CAACrM,MAAM,EAAE0O,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQsJ,EAAKtJ,KAAK,CAACW,GACR,CACPyI,EAAQlV,IAAI,CAACwG,WAAWsF,CAAK,CAAC,EAAE,GAChCsJ,EAAOA,EAAKd,SAAS,CAACxI,CAAK,CAAC,EAAE,CAACrM,MAAM,EAAE0O,IAAI,GAC3C,QACJ,CAEA,GAAIiH,AAAY,MAAZA,CAAI,CAAC,EAAE,CAAU,CACjB,IAAMI,EAASjB,GAAca,GAC7BF,EAAQlV,IAAI,CAACwV,EAAOlB,SAAS,CAAC,EAAG,KACjCc,EAAOA,EAAKd,SAAS,CAACkB,EAAO/V,MAAM,CAAG,GAAG0O,IAAI,GAC7C,QACJ,CAGA,GADArC,EAAQsJ,EAAKtJ,KAAK,CAAC6H,GACR,CAEP,IAAM8B,EAAcxB,EADpBmB,EAAOA,EAAKd,SAAS,CAACxI,CAAK,CAAC,EAAE,CAACrM,MAAM,EAAE0O,IAAI,IAE3C+G,EAAQlV,IAAI,CAAC,CACTV,KAAM,WACNqK,KAAMmC,CAAK,CAAC,EAAE,CACd4J,KAAMC,AAhJtB,SAAwBzB,CAAI,CAAEQ,CAAqB,EAC/C,IAAMgB,EAAO,EAAE,CAAEE,EAAsBlB,EAAwB,IAAM,IACjEP,EAAkB,EAAG0B,EAAO,GAChC,IAAK,IAAI3Q,EAAI,EAAGC,EAAO+O,EAAKzU,MAAM,CAAE2U,EAAMlP,EAAIC,EAAM,EAAED,EAGlD,GAAIkP,AAFJA,CAAAA,EAAOF,CAAI,CAAChP,EAAE,AAAD,IAEA0Q,GACT,CAACzB,GACD0B,EACAH,EAAK1V,IAAI,CAACyU,GAAcoB,EAAMnB,IAC9BmB,EAAO,QAGN,GAAIzB,AAAS,MAATA,GACJD,GACA0B,EAMa,MAATzB,IACLyB,GAAQzB,EACJA,AAAS,MAATA,EACA,EAAED,EAEY,MAATC,GACL,EAAED,OAZC,CACP,IAAMqB,EAASjB,GAAcL,EAAKI,SAAS,CAACpP,IAC5CwQ,EAAK1V,IAAI,CAACwV,GACVtQ,GAAKsQ,EAAO/V,MAAM,CAAG,CAEzB,CAeJ,MAHI,CAAC0U,GAAmB0B,GACpBH,EAAK1V,IAAI,CAACyU,GAAcoB,EAAMnB,IAE3BgB,CACX,EA4GqCD,EAAaf,EACtC,GACAU,EAAOA,EAAKd,SAAS,CAACmB,EAAYhW,MAAM,CAAG,GAAG0O,IAAI,GAClD,QACJ,CAEA,GAAIiH,AAAY,MAAZA,CAAI,CAAC,EAAE,CAAU,CACjB,IAAMU,EAAa7B,EAAmBmB,GACtC,GAAIU,EAAY,CACZZ,EACKlV,IAAI,CAACmV,GAAaW,EAAYpB,IACnCU,EAAOA,EAAKd,SAAS,CAACwB,EAAWrW,MAAM,CAAG,GAAG0O,IAAI,GACjD,QACJ,CACJ,CAEA,IAAM4H,EAAW7B,EAAKzU,MAAM,CAAG2V,EAAK3V,MAAM,CAAEmJ,EAAQ,AAAI8B,MAAM,yBAC1DwJ,EAAKI,SAAS,CAACyB,EAAUA,EAAW,GACpC,iBAAoBA,CAAAA,EAAW,CAAA,EAC/B,UAAY7B,EAAKI,SAAS,CAACyB,EAAW,EAAGA,EAAW,GAAK,QAE7D,OADAnN,EAAMe,IAAI,CAAG,oBACPf,CACV,CACA,OAAOsM,CACX,CAaA,SAASD,GAAqBf,CAAI,EAC9B,IAAIzS,EAAS,EACb,IAAK,IAAIyD,EAAI,EAAGC,EAAO+O,EAAKzU,MAAM,CAAEuW,EAAMC,EAAS/B,EAAKzU,MAAM,CAAG,EAAGyF,EAAIC,EAAM,EAAED,EAC5E8Q,CAAAA,EAAO9B,EAAKgC,UAAU,CAAChR,EAAC,GACZ,IAAM8Q,GAAQ,IACtBvU,CAAAA,GAAU,AAACuU,CAAAA,EAAO,EAAC,EAAK3S,KAAK8S,GAAG,CAAC,GAAIF,EAAM,EAE/C,EAAEA,EAEN,OAAOxU,CACX,CAS6B,IAAM2U,GAHb,CAClBjB,aAAAA,EACJ,EA0BMkB,GAAY,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,IAAK,KAAK,CAgHnCC,GARf,CAChBC,UAxFJ,SAAmBC,CAAI,EACnB,OAAOA,aAAgB7U,KAC3B,EAuFI8U,WA3EJ,SAAoBD,CAAI,EACpB,MAAQ,AAAgB,UAAhB,OAAOA,GACX,CAAEA,CAAAA,aAAgB7U,KAAI,GACtB6U,AAAc,aAAdA,EAAKlX,IAAI,AACjB,EAwEIoX,WA5DJ,SAAoBF,CAAI,EACpB,MAAQ,AAAgB,UAAhB,OAAOA,GACXH,GAAU7O,OAAO,CAACgP,IAAS,CACnC,EA0DIG,QA9CJ,SAAiBH,CAAI,EACjB,MAAQ,AAAgB,UAAhB,OAAOA,GACX,CAAEA,CAAAA,aAAgB7U,KAAI,GACtB6U,AAAc,UAAdA,EAAKlX,IAAI,AACjB,EA2CIsX,YA/BJ,SAAqBJ,CAAI,EACrB,MAAQ,AAAgB,UAAhB,OAAOA,GACX,CAAEA,CAAAA,aAAgB7U,KAAI,GACtB6U,AAAc,cAAdA,EAAKlX,IAAI,AACjB,EA4BIuX,QAhBJ,SAAiBL,CAAI,EACjB,MAAQ,AAAgB,WAAhB,OAAOA,GACX,AAAgB,UAAhB,OAAOA,GACP,AAAgB,UAAhB,OAAOA,CACf,CAaA,EAkBM,CAAED,UAAWO,EAA0B,CAAEL,WAAYM,EAA2B,CAAEL,WAAYM,EAA2B,CAAEL,QAASM,EAAwB,CAAEL,YAAaM,EAA4B,CAAEL,QAASM,EAAwB,CAAE,CAAGb,GAM/Oc,GAAwB,KACxBC,GAAYrP,OAAOuH,SAAS,CAAG,eAC/B+H,GAAatP,OAAOuH,SAAS,CAAG,eAChCgI,GAAWvP,OAAOuH,SAAS,CAC3BiI,GAAmB,CACrB,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,KAAM,EACN,IAAK,EACL,KAAM,CACV,EACMC,GAAqB,CAAC,EACtBC,GAA8B,kBAepC,SAASC,GAAgB/K,CAAK,EAC1B,OAAQ,OAAOA,GACX,IAAK,UACD,OAAOA,EAAQ2K,GAAWF,EAC9B,KAAK,SACD,OAAOC,EACX,KAAK,SACD,OAAO1K,CACX,SACI,OAAOX,GACf,CACJ,CAYA,SAAS2L,GAAgBhL,CAAK,QAC1B,AAAI,AAAiB,UAAjB,OAAOA,EACAA,EAAMiL,WAAW,GAAGtK,OAAO,CAAC6J,GAAuB,MAEvDxK,CACX,CAUA,SAASC,GAASD,CAAK,EACnB,OAAQ,OAAOA,GACX,IAAK,UACD,MAAOA,GAAAA,CACX,KAAK,SACD,OAAOpG,WAAWoG,EAAMW,OAAO,CAAC,IAAK,KACzC,KAAK,SACD,OAAOX,CACX,SACI,OAAOX,GACf,CACJ,CAkBA,SAAS6L,GAAeC,CAAQ,CAAEC,CAAC,CAAEC,CAAC,MA2B9BzV,EA1BJ,OAAQuV,GACJ,IAAK,IACD,OAAOH,GAAgBI,KAAOJ,GAAgBK,EAClD,KAAK,IACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,GAAKJ,GAAgBK,GAEhD,OAAON,GAAgBK,GAAKL,GAAgBM,EAChD,KAAK,KACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,IAAMJ,GAAgBK,GAEjD,OAAON,GAAgBK,IAAML,GAAgBM,EACjD,KAAK,IACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,GAAKJ,GAAgBK,GAEhD,OAAON,GAAgBK,GAAKL,GAAgBM,EAChD,KAAK,KACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,IAAMJ,GAAgBK,GAEjD,OAAON,GAAgBK,IAAML,GAAgBM,EACrD,CAIA,OAHAD,EAAInL,GAASmL,GACbC,EAAIpL,GAASoL,GAELF,GACJ,IAAK,IACDvV,EAASwV,EAAIC,EACb,KACJ,KAAK,IACDzV,EAASwV,EAAIC,EACb,KACJ,KAAK,IACDzV,EAASwV,EAAIC,EACb,KACJ,KAAK,IACDzV,EAASwV,EAAIC,EACb,KACJ,KAAK,IACDzV,EAASa,KAAK8S,GAAG,CAAC6B,EAAGC,GACrB,KACJ,SACI,OAAOhM,GACf,CAEA,OAAQzJ,EAAS,EACba,KAAK6U,KAAK,CAAC1V,AAAS,IAATA,GAAuB,IAClCA,CACR,CAeA,SAAS2V,GAAiBC,CAAG,CAAEjY,CAAK,SAEhC,AAAIgX,GAAyBiB,GAClBA,EAGPnB,GAAyBmB,GACjBjY,GAASkY,GAAeD,EAAKjY,IAAU,EAAE,CAGjD4W,GAA4BqB,GACrBE,GAAgBF,EAAKjY,GAGzBoY,GAAgBzB,GAA2BsB,GAAOA,EAAM,CAACA,EAAI,CAAGjY,EAC3E,CAoCA,SAASkY,GAAe/G,CAAK,CAAEnR,CAAK,EAChC,IAAMyD,EAAczD,EACf0G,cAAc,GACdzD,KAAK,CAACkO,EAAMyD,WAAW,CAAEzD,EAAMhC,SAAS,CAAG,GAAIkJ,EAAS,EAAE,CAC/D,IAAK,IAAItT,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEgZ,EAAMvT,EAAIC,EAAM,EAAED,EAAG,CAC5D,IAAMwT,EAAQvY,EAAMsD,SAAS,CAACG,CAAW,CAACsB,EAAE,CAAE,CAAA,IAAS,EAAE,CACzD,IAAK,IAAIc,EAAIsL,EAAM0D,QAAQ,CAAE/O,EAAOqL,EAAM7B,MAAM,CAAG,EAAGzJ,EAAIC,EAAM,EAAED,EAE1C,UAAhB,MADJyS,CAAAA,EAAOC,CAAK,CAAC1S,EAAE,AAAD,GAEVyS,AAAY,MAAZA,CAAI,CAAC,EAAE,EACPtY,IAAUA,EAAMK,QAAQ,EAExBiY,CAAAA,EAAOtY,EAAMK,QAAQ,CAAC2F,OAAO,CAACvC,CAAW,CAACsB,EAAE,CAAEc,EAAC,EAEnDwS,EAAOxY,IAAI,CAACmX,GAAyBsB,GAAQA,EAAOxM,IAE5D,CACA,OAAOuM,CACX,CAeA,SAASG,GAAkBpD,CAAS,CAAEpV,CAAK,EACvC,IAAMS,EAAaT,EAAM0G,cAAc,EAAE,CAAC0O,EAAU9T,MAAM,CAAC,CAC3D,GAAIb,EAAY,CACZ,IAAM6X,EAAOtY,EAAMgG,OAAO,CAACvF,EAAY2U,EAAUlR,GAAG,EACpD,GAAI,AAAgB,UAAhB,OAAOoU,GACPA,AAAY,MAAZA,CAAI,CAAC,EAAE,EACPtY,IAAUA,EAAMK,QAAQ,CAAE,CAE1B,IAAMgC,EAASrC,EAAMK,QAAQ,CAAC2F,OAAO,CAACvF,EAAY2U,EAAUlR,GAAG,EAC/D,OAAO8S,GAAyB3U,GAAUA,EAASyJ,GACvD,CACA,OAAOkL,GAAyBsB,GAAQA,EAAOxM,GACnD,CACA,OAAOA,GACX,CAiBA,SAASsM,GAAerD,CAAO,CAAE/U,CAAK,EAClC,IAAI6X,EACJ,IAAK,IAAI9S,EAAI,EAAGC,EAAO+P,EAAQzV,MAAM,CAAE+W,EAAMuB,EAAUvV,EAAQyV,EAAG/S,EAAIC,EAAM,EAAED,EAAG,CAG7E,GAAI8R,GAFJR,EAAOtB,CAAO,CAAChQ,EAAE,EAEsB,CACnC6S,EAAWvB,EACX,QACJ,CAmBA,GAjBIW,GAAyBX,GACzByB,EAAIzB,EAGCM,GAA2BN,GAChCyB,EAAIM,GAAerD,EAAS/U,GAGvB4W,GAA4BP,GAEjCyB,EAAKd,GADL3U,EAAS8V,GAAgB9B,EAAMrW,IACSqC,EAASyJ,IAG5CiL,GAA6BV,IAClCyB,CAAAA,EAAK9X,GAASwY,GAAkBnC,EAAMrW,EAAM,EAG5C,AAAa,KAAA,IAAN8X,EAAmB,CAE1B,GAAI,AAAa,KAAA,IAAND,EAEHA,EADAD,EACID,GAAeC,EAAU,EAAGE,GAG5BA,MAQP,CAJA,GAAI,CAACF,EACN,OAAO9L,IAIP,IAAM2M,EAAY1D,CAAO,CAAChQ,EAAI,EAAE,CAC5B8R,GAA4B4B,IAC5BpB,EAAgB,CAACoB,EAAU,CAAGpB,EAAgB,CAACO,EAAS,GACxDE,EAAIH,GAAec,EAAWX,EAAGM,GAAerD,EAAQ9R,KAAK,CAAC8B,EAAI,KAClEA,EAAIC,GAER6S,EAAIF,GAAeC,EAAUC,EAAGC,EACpC,CACAF,EAAW,KAAK,EAChBE,EAAI,KAAK,CACb,CACJ,CACA,OAAOd,GAAyBa,GAAKA,EAAI/L,GAC7C,CAmBA,SAASqM,GAAgBO,CAAe,CAAE1Y,CAAK,CAE/CoV,CAAS,EAEL,IAAMuD,EAAYrB,EAAkB,CAACoB,EAAgBlP,IAAI,CAAC,CAC1D,GAAImP,EACA,GAAI,CACA,OAAOA,EAAUD,EAAgBnD,IAAI,CAAEvV,EAC3C,CACA,KAAM,CACF,OAAO8L,GACX,CAEJ,IAAMrD,EAAQ,AAAI8B,MAAM,CAAC,UAAU,EAAEmO,EAAgBlP,IAAI,CAAC,YAAY,CAAC,CAEvE,OADAf,EAAMe,IAAI,CAAG,sBACPf,CACV,CAqF6B,IAAMmQ,GAXV,CACrBlM,SAAAA,GACAsL,iBAAAA,GACAa,mBA7PJ,SAA4BtD,CAAI,CAAEvV,CAAK,EACnC,IAAMqY,EAAS,EAAE,CACjB,IAAK,IAAItT,EAAI,EAAGC,EAAOuQ,EAAKjW,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAC5CsT,EAAOxY,IAAI,CAACmY,GAAiBzC,CAAI,CAACxQ,EAAE,CAAE/E,IAE1C,OAAOqY,CACX,EAwPIH,eAAAA,GACAM,kBAAAA,GACAJ,eAAAA,GACAd,mBAAAA,GACAwB,0BApEJ,SAAmCtP,CAAI,CAAEuP,CAAiB,EACtD,OAAQxB,GAA4BlK,IAAI,CAAC7D,IACrC,CAAC8N,EAAkB,CAAC9N,EAAK,EACzB,CAAC,CAAE8N,CAAAA,EAAkB,CAAC9N,EAAK,CAAGuP,CAAgB,CACtD,EAiEIC,oBAhDJ,SAASA,EAAoBjE,CAAO,CAAEkE,EAAc,CAAC,CAAEC,EAAW,CAAC,EAC/D,IAAK,IAAInU,EAAI,EAAGC,EAAO+P,EAAQzV,MAAM,CAAE+W,EAAMtR,EAAIC,EAAM,EAAED,EAEjDsR,AADJA,CAAAA,EAAOtB,CAAO,CAAChQ,EAAE,AAAD,YACIvD,MAChBwX,EAAoB3C,EAAM4C,EAAaC,GAElCtC,GAA4BP,GACjC2C,EAAoB3C,EAAKd,IAAI,CAAE0D,EAAaC,GAEvCpC,GAAyBT,IAC1BA,EAAK7B,mBAAmB,EACxB6B,CAAAA,EAAKzB,WAAW,EAAIqE,CAAU,EAE9B5C,EAAK5B,gBAAgB,EACrB4B,CAAAA,EAAKxB,QAAQ,EAAIqE,CAAO,EAExB7C,EAAK3B,iBAAiB,EACtB2B,CAAAA,EAAKlH,SAAS,EAAI8J,CAAU,EAE5B5C,EAAK1B,cAAc,EACnB0B,CAAAA,EAAK/G,MAAM,EAAI4J,CAAO,GAGrBnC,GAA6BV,KAC9BA,EAAKnB,cAAc,EACnBmB,CAAAA,EAAK/U,MAAM,EAAI2X,CAAU,EAEzB5C,EAAKlB,WAAW,EAChBkB,CAAAA,EAAKnS,GAAG,EAAIgV,CAAO,GAI/B,OAAOnE,CACX,CAgBA,EAkBM,CAAEiD,iBAAkBmB,EAAoB,CAAE,CAAGP,GA8CnDA,GAAyBE,yBAAyB,CAAC,MAzBnD,SAAavD,CAAI,CAAEvV,CAAK,EACpB,IAAMyM,EAAQ0M,GAAqB5D,CAAI,CAAC,EAAE,CAAEvV,GAC5C,OAAQ,OAAOyM,GACX,IAAK,SACD,OAAOvJ,KAAKkW,GAAG,CAAC3M,EACpB,KAAK,SAAU,CACX,IAAM4L,EAAS,EAAE,CACjB,IAAK,IAAItT,EAAI,EAAGC,EAAOyH,EAAMnN,MAAM,CAAE+Z,EAAQtU,EAAIC,EAAM,EAAED,EAAG,CAExD,GAAI,AAAkB,UAAlB,MADJsU,CAAAA,EAAS5M,CAAK,CAAC1H,EAAE,AAAD,EAEZ,OAAO+G,IAEXuM,EAAOxY,IAAI,CAACqD,KAAKkW,GAAG,CAACC,GACzB,CACA,OAAOhB,CACX,CACA,QACI,OAAOvM,GACf,CACJ,GA6BA,GAAM,CAAEkM,iBAAkBsB,EAAoB,CAAE,CAAGV,GAsCnDA,GAAyBE,yBAAyB,CAAC,MAhBnD,SAASS,EAAIhE,CAAI,CAAEvV,CAAK,EACpB,IAAK,IAAI+E,EAAI,EAAGC,EAAOuQ,EAAKjW,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAEnD,GAAI,CADJ0H,CAAAA,EAAQ6M,GAAqB/D,CAAI,CAACxQ,EAAE,CAAE/E,EAAK,GAEtC,AAAiB,UAAjB,OAAOyM,GACJ,CAAC8M,EAAI9M,EAAOzM,GAChB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,GA6BA,GAAM,CAAE6Y,mBAAoBW,EAA0B,CAAE,CAAGZ,GAqD3DA,GAAyBE,yBAAyB,CAAC,UA/BnD,SAAiBvD,CAAI,CAAEvV,CAAK,EACxB,IAAMqY,EAASmB,GAA2BjE,EAAMvV,GAC5CyZ,EAAQ,EAAGpX,EAAS,EACxB,IAAK,IAAI0C,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACIqB,MAAMqG,KACP,EAAEgN,EACFpX,GAAUoK,GAEd,KACJ,KAAK,SACD,IAAK,IAAI5G,EAAI,EAAGC,EAAO2G,EAAMnN,MAAM,CAAE+Z,EAAQxT,EAAIC,EAAM,EAAED,EAE/B,UAAlB,MADJwT,CAAAA,EAAS5M,CAAK,CAAC5G,EAAE,AAAD,GAEXO,MAAMiT,KACP,EAAEI,EACFpX,GAAUgX,EAI1B,CAEJ,OAAQI,EAASpX,EAASoX,EAAS,CACvC,GA6BA,GAAM,CAAEzB,iBAAkB0B,EAAyB,CAAE,CAAGd,GAqExDA,GAAyBE,yBAAyB,CAAC,WA/CnD,SAAkBvD,CAAI,CAAEvV,CAAK,EACzB,IAAIyZ,EAAQ,EAAGpX,EAAS,EACxB,IAAK,IAAI0C,EAAI,EAAGC,EAAOuQ,EAAKjW,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAEnD,OAAQ,MADR0H,CAAAA,EAAQiN,GAA0BnE,CAAI,CAACxQ,EAAE,CAAE/E,EAAK,GAE5C,IAAK,UACD,EAAEyZ,EACFpX,GAAWoK,GAAAA,EACX,QACJ,KAAK,SACIrG,MAAMqG,KACP,EAAEgN,EACFpX,GAAUoK,GAEd,QACJ,KAAK,SACD,EAAEgN,EACF,QACJ,SACI,IAAK,IAAI5T,EAAI,EAAGC,EAAO2G,EAAMnN,MAAM,CAAE+Z,EAAQxT,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADRwT,CAAAA,EAAS5M,CAAK,CAAC5G,EAAE,AAAD,GAEZ,IAAK,UACD,EAAE4T,EACFpX,GAAWgX,GAAAA,EACX,QACJ,KAAK,SACIjT,MAAMiT,KACP,EAAEI,EACFpX,GAAUgX,GAEd,QACJ,KAAK,SACD,EAAEI,EACF,QACR,CAEJ,QACR,CAEJ,OAAQA,EAASpX,EAASoX,EAAS,CACvC,GAyEAb,GAAyBE,yBAAyB,CAAC,QAvBnD,SAASa,EAAMpE,CAAI,CAAEvV,CAAK,EACtB,IAAMqY,EAASO,GAAyBC,kBAAkB,CAACtD,EAAMvV,GAC7DyZ,EAAQ,EACZ,IAAK,IAAI1U,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACG,CAACqB,MAAMqG,IACP,EAAEgN,EAEN,KACJ,KAAK,SACDA,GAASE,EAAMlN,EAAOzM,EAE9B,CAEJ,OAAOyZ,CACX,GA+EAb,GAAyBE,yBAAyB,CAAC,SA7BnD,SAASc,EAAOrE,CAAI,CAAEvV,CAAK,EACvB,IAAMqY,EAASO,GAAyBC,kBAAkB,CAACtD,EAAMvV,GAC7DyZ,EAAQ,EACZ,IAAK,IAAI1U,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAAG,CAExD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACD,GAAIqB,MAAMqG,GACN,SAEJ,KACJ,KAAK,SACDgN,GAASG,EAAOnN,EAAOzM,GACvB,QACJ,KAAK,SACD,GAAI,CAACyM,EACD,QAGZ,CACA,EAAEgN,CACN,CACA,OAAOA,CACX,GA6BA,GAAM,CAAEzB,iBAAkB6B,EAAmB,CAAE,CAAGjB,GAiClDA,GAAyBE,yBAAyB,CAAC,KAVnD,SAAYvD,CAAI,CAAEvV,CAAK,EACnB,OAAQ6Z,GAAoBtE,CAAI,CAAC,EAAE,CAAEvV,GACjC6Z,GAAoBtE,CAAI,CAAC,EAAE,CAAEvV,GAC7B6Z,GAAoBtE,CAAI,CAAC,EAAE,CAAEvV,EACrC,GA6BA,GAAM,CAAEgY,iBAAkB8B,EAAqB,CAAE,CAAGlB,GA+BpDA,GAAyBE,yBAAyB,CAAC,OATnD,SAAcvD,CAAI,CAAEvV,CAAK,EACrB,IAAMyM,EAAQqN,GAAsBvE,CAAI,CAAC,EAAE,CAAEvV,GAC7C,MAAQ,AAAiB,UAAjB,OAAOyM,GAAsBrG,MAAMqG,EAC/C,GA6BA,GAAM,CAAEoM,mBAAoBkB,EAAsB,CAAE,CAAGnB,GAgDvDA,GAAyBE,yBAAyB,CAAC,MA1BnD,SAASkB,EAAIzE,CAAI,CAAEvV,CAAK,EACpB,IAAMqY,EAAS0B,GAAuBxE,EAAMvV,GACxCqC,EAASwF,OAAOoS,iBAAiB,CACrC,IAAK,IAAIlV,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACG0H,EAAQpK,GACRA,CAAAA,EAASoK,CAAI,EAEjB,KACJ,KAAK,SACDA,CAAAA,EAAQuN,EAAIvN,EAAK,EACLpK,GACRA,CAAAA,EAASoK,CAAI,CAGzB,CAEJ,OAAO3E,SAASzF,GAAUA,EAAS,CACvC,GAsFAuW,GAAyBE,yBAAyB,CAAC,SApCnD,SAAgBvD,CAAI,CAAEvV,CAAK,EACvB,IAAMka,EAAS,EAAE,CAAE7B,EAASO,GAAyBC,kBAAkB,CAACtD,EAAMvV,GAC9E,IAAK,IAAI+E,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACIqB,MAAMqG,IACPyN,EAAOra,IAAI,CAAC4M,GAEhB,KACJ,KAAK,SACD,IAAK,IAAI5G,EAAI,EAAGC,EAAO2G,EAAMnN,MAAM,CAAE+Z,EAAQxT,EAAIC,EAAM,EAAED,EAE/B,UAAlB,MADJwT,CAAAA,EAAS5M,CAAK,CAAC5G,EAAE,AAAD,GAEXO,MAAMiT,IACPa,EAAOra,IAAI,CAACwZ,EAI5B,CAEJ,IAAMI,EAAQS,EAAO5a,MAAM,CAC3B,GAAI,CAACma,EACD,OAAO3N,IAEX,IAAMqO,EAAOjX,KAAKkX,KAAK,CAACX,EAAQ,GAChC,OAAQA,EAAQ,EACZS,CAAM,CAACC,EAAK,CACZ,AAACD,CAAAA,CAAM,CAACC,EAAO,EAAE,CAAGD,CAAM,CAACC,EAAK,AAAD,EAAK,CAE5C,GA6BA,GAAM,CAAEtB,mBAAoBwB,EAAsB,CAAE,CAAGzB,GAgDvDA,GAAyBE,yBAAyB,CAAC,MA1BnD,SAASwB,EAAI/E,CAAI,CAAEvV,CAAK,EACpB,IAAMqY,EAASgC,GAAuB9E,EAAMvV,GACxCqC,EAASwF,OAAO0S,iBAAiB,CACrC,IAAK,IAAIxV,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACG0H,EAAQpK,GACRA,CAAAA,EAASoK,CAAI,EAEjB,KACJ,KAAK,SACDA,CAAAA,EAAQ6N,EAAI7N,EAAK,EACLpK,GACRA,CAAAA,EAASoK,CAAI,CAGzB,CAEJ,OAAO3E,SAASzF,GAAUA,EAAS,CACvC,GA6BA,GAAM,CAAE2V,iBAAkBwC,EAAoB,CAAE,CAAG5B,GAqFnD,SAAS6B,GAAWlF,CAAI,CAAEvV,CAAK,EAC3B,IAAM0a,EAAU,CAAC,EAAGrC,EAASO,GAAyBC,kBAAkB,CAACtD,EAAMvV,GAC/E,IAAK,IAAI+E,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACIqB,MAAMqG,IACPiO,CAAAA,CAAO,CAACjO,EAAM,CAAG,AAACiO,CAAAA,CAAO,CAACjO,EAAM,EAAI,CAAA,EAAK,CAAA,EAE7C,KACJ,KAAK,SACD,IAAK,IAAI5G,EAAI,EAAGC,EAAO2G,EAAMnN,MAAM,CAAE+Z,EAAQxT,EAAIC,EAAM,EAAED,EAE/B,UAAlB,MADJwT,CAAAA,EAAS5M,CAAK,CAAC5G,EAAE,AAAD,GAEXO,MAAMiT,IACPqB,CAAAA,CAAO,CAACrB,EAAO,CAAG,AAACqB,CAAAA,CAAO,CAACrB,EAAO,EAAI,CAAA,EAAK,CAAA,CAI3D,CAEJ,OAAOqB,CACX,CAoDA,SAASC,GAAKpF,CAAI,CAAEvV,CAAK,EACrB,IAAM0a,EAAUD,GAAWlF,EAAMvV,GAAQ0D,EAAO7F,OAAO6F,IAAI,CAACgX,GAC5D,GAAI,CAAChX,EAAKpE,MAAM,CACZ,OAAOwM,IAEX,IAAI8O,EAAUvU,WAAW3C,CAAI,CAAC,EAAE,EAAGmX,EAAYH,CAAO,CAAChX,CAAI,CAAC,EAAE,CAAC,CAC/D,IAAK,IAAIqB,EAAI,EAAGC,EAAOtB,EAAKpE,MAAM,CAAE3B,EAAKmd,EAAUrB,EAAO1U,EAAIC,EAAM,EAAED,EAG9D8V,EADJpB,CAAAA,EAAQiB,CAAO,CADf/c,EAAM+F,CAAI,CAACqB,EAAE,CACO,AAAD,GAEf6V,EAAUvU,WAAW1I,GACrBkd,EAAYpB,GAEPoB,IAAcpB,GAEfmB,EADJE,CAAAA,EAAWzU,WAAW1I,EAAG,IAErBid,EAAUE,EACVD,EAAYpB,GAIxB,OAAOoB,EAAY,EAAID,EAAU9O,GACrC,CA3IA8M,GAAyBE,yBAAyB,CAAC,MApBnD,SAAavD,CAAI,CAAEvV,CAAK,EACpB,IAAI+a,EAASP,GAAqBjF,CAAI,CAAC,EAAE,CAAEvV,GAAQqZ,EAASmB,GAAqBjF,CAAI,CAAC,EAAE,CAAEvV,SAO1F,CANsB,UAAlB,OAAO+a,GACPA,CAAAA,EAASA,CAAM,CAAC,EAAE,AAAD,EAEC,UAAlB,OAAO1B,GACPA,CAAAA,EAASA,CAAM,CAAC,EAAE,AAAD,EAEjB,AAAkB,UAAlB,OAAO0B,GACP,AAAkB,UAAlB,OAAO1B,GACPA,AAAW,IAAXA,GACOvN,IAEJiP,EAAS1B,CACpB,GAuJAT,GAAyBE,yBAAyB,CAAC,OAAQ6B,IAC3D/B,GAAyBE,yBAAyB,CAAC,YAhEnD,SAAcvD,CAAI,CAAEvV,CAAK,EACrB,IAAM0a,EAAUD,GAAWlF,EAAMvV,GAAQ0D,EAAO7F,OAAO6F,IAAI,CAACgX,GAC5D,GAAI,CAAChX,EAAKpE,MAAM,CACZ,OAAOwM,IAEX,IAAIkP,EAAW,CAAC3U,WAAW3C,CAAI,CAAC,EAAE,EAAE,CAAEmX,EAAYH,CAAO,CAAChX,CAAI,CAAC,EAAE,CAAC,CAClE,IAAK,IAAIqB,EAAI,EAAGC,EAAOtB,EAAKpE,MAAM,CAAE3B,EAAK8b,EAAO1U,EAAIC,EAAM,EAAED,EAGpD8V,EADJpB,CAAAA,EAAQiB,CAAO,CADf/c,EAAM+F,CAAI,CAACqB,EAAE,CACO,AAAD,GAEfiW,EAAW,CAAC3U,WAAW1I,GAAK,CAC5Bkd,EAAYpB,GAEPoB,IAAcpB,GACnBuB,EAASnb,IAAI,CAACwG,WAAW1I,IAGjC,OAAOkd,EAAY,EAAIG,EAAWlP,GACtC,GA+CA8M,GAAyBE,yBAAyB,CAAC,YAAa6B,IA2BhE,GAAM,CAAE3C,iBAAkBiD,EAAoB,CAAE,CAAGrC,GAuCnDA,GAAyBE,yBAAyB,CAAC,MAjBnD,SAAavD,CAAI,CAAEvV,CAAK,EACpB,IAAIyM,EAAQwO,GAAqB1F,CAAI,CAAC,EAAE,CAAEvV,GAI1C,OAHqB,UAAjB,OAAOyM,GACPA,CAAAA,EAAQA,CAAK,CAAC,EAAE,AAAD,EAEX,OAAOA,GACX,IAAK,UACL,IAAK,SACD,MAAO,CAACA,CAChB,CACA,OAAOX,GACX,GA6BA,GAAM,CAAEkM,iBAAkBkD,EAAmB,CAAE,CAAGtC,GAyClDA,GAAyBE,yBAAyB,CAAC,KAnBnD,SAASqC,EAAG5F,CAAI,CAAEvV,CAAK,EACnB,IAAK,IAAI+E,EAAI,EAAGC,EAAOuQ,EAAKjW,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAEnD,GAAI,AAAiB,UAAjB,MADJ0H,CAAAA,EAAQyO,GAAoB3F,CAAI,CAACxQ,EAAE,CAAE/E,EAAK,EAEtC,CAAA,GAAImb,EAAG1O,EAAOzM,GACV,MAAO,CAAA,CACX,MAEC,GAAIyM,EACL,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,GA6BA,GAAM,CAAEoM,mBAAoBuC,EAA0B,CAAE,CAAGxC,GA+C3DA,GAAyBE,yBAAyB,CAAC,UAzBnD,SAASuC,EAAQ9F,CAAI,CAAEvV,CAAK,EACxB,IAAMqY,EAAS+C,GAA2B7F,EAAMvV,GAC5CqC,EAAS,EAAGiZ,EAAa,CAAA,EAC7B,IAAK,IAAIvW,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACIqB,MAAMqG,KACP6O,EAAa,CAAA,EACbjZ,GAAUoK,GAEd,KACJ,KAAK,SACD6O,EAAa,CAAA,EACbjZ,GAAUgZ,EAAQ5O,EAAOzM,EAEjC,CAEJ,OAAQsb,EAAajZ,EAAS,CAClC,GAyEAuW,GAAyBE,yBAAyB,CAAC,MAvBnD,SAASyC,EAAIhG,CAAI,CAAEvV,CAAK,EACpB,IAAMqY,EAASO,GAAyBC,kBAAkB,CAACtD,EAAMvV,GAC7DqC,EAAS,EACb,IAAK,IAAI0C,EAAI,EAAGC,EAAOqT,EAAO/Y,MAAM,CAAEmN,EAAO1H,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADR0H,CAAAA,EAAQ4L,CAAM,CAACtT,EAAE,AAAD,GAEZ,IAAK,SACIqB,MAAMqG,IACPpK,CAAAA,GAAUoK,CAAI,EAElB,KACJ,KAAK,SACDpK,GAAUkZ,EAAI9O,EAAOzM,EAE7B,CAEJ,OAAOqC,CACX,GA6BA,GAAM,CAAE2V,iBAAkBwD,EAAoB,CAAE,CAAG5C,GA4DnDA,GAAyBE,yBAAyB,CAAC,MAtCnD,SAAavD,CAAI,CAAEvV,CAAK,EACpB,IAAK,IAAI+E,EAAI,EAAGC,EAAOuQ,EAAKjW,MAAM,CAAEmc,EAAWhP,EAAO1H,EAAIC,EAAM,EAAED,EAE9D,OAAQ,MADR0H,CAAAA,EAAQ+O,GAAqBjG,CAAI,CAACxQ,EAAE,CAAE/E,EAAK,GAEvC,IAAK,UACL,IAAK,SACD,GAAI,AAAqB,KAAA,IAAdyb,EACPA,EAAY,CAAC,CAAChP,OAEb,GAAI,CAAC,CAACA,IAAUgP,EACjB,MAAO,CAAA,EAEX,KACJ,KAAK,SACD,IAAK,IAAI5V,EAAI,EAAGC,EAAO2G,EAAMnN,MAAM,CAAE+Z,EAAQxT,EAAIC,EAAM,EAAED,EAErD,OAAQ,MADRwT,CAAAA,EAAS5M,CAAK,CAAC5G,EAAE,AAAD,GAEZ,IAAK,UACL,IAAK,SACD,GAAI,AAAqB,KAAA,IAAd4V,EACPA,EAAY,CAAC,CAACpC,OAEb,GAAI,CAAC,CAACA,IAAWoC,EAClB,MAAO,CAAA,CAGnB,CAGZ,CAEJ,MAAO,CAAA,CACX,GA+DA,IAAMC,GAAU,CACZ,GAAGzF,EAAqB,CACxB,GAAG2C,EAAwB,CAC3B,GAAGzC,EAAY,AACnB,EAsBM,CAAEzX,MAAOid,EAAkB,CAAE,CAAIpd,GAWvC,OAAMqd,WAAqBjM,EAYvBvN,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgByP,GAAmBC,GAAazP,cAAc,CAAEtN,GACtE,KAAK,CAACqN,GAMN,IAAI,CAACrL,OAAO,CAAG,EAAE,CACjB,IAAI,CAACyN,OAAO,CAAG,EAAE,CACjB,IAAI,CAACuN,SAAS,CAAG,EAAE,CACnB,IAAI,CAAChd,OAAO,CAAGqN,CACnB,CAkBAmC,OAAO3E,CAAS,CAAE7K,EAAU,IAAI,CAACA,OAAO,CAAE,CACtC,GAAM,CAAEid,qBAAAA,CAAoB,CAAEC,cAAAA,CAAa,CAAE,CAAGld,EAASmd,EAAe,AAAiC,CAAA,IAAjC,IAAI,CAACnd,OAAO,CAAC0Q,eAAe,CAChG,CAAElD,aAAAA,CAAY,CAAE4P,cAAAA,CAAa,CAAE,CAAGpd,EACjCwN,GACDA,CAAAA,EAAgB4P,AAAkB,MAAlBA,GAAyBH,EACrC,AAAC,IAAKI,cAAc,EAAE,CAAC,EAAE,CACzB,GAAG,EAEND,GACDA,CAAAA,EAAiB5P,AAAiB,MAAjBA,EAAuB,IAAM,GAAG,EAErD,IAAMxL,EAAU6I,EAAUS,gBAAgB,CAACtL,EAAQuL,oBAAoB,EAAG3G,EAAc5F,OAAO6F,IAAI,CAAC7C,GAAUsb,EAAU,EAAE,CAAEC,EAAe3Y,EAAYnE,MAAM,CACvJ+c,EAAW,EAAE,CAEfL,GACAG,EAAQtc,IAAI,CAAC4D,EAAYI,GAAG,CAAC,AAACpD,GAAe,CAAC,CAAC,EAAEA,EAAW,CAAC,CAAC,EAAE0N,IAAI,CAAC8N,IAEzE,IAAK,IAAIK,EAAc,EAAGA,EAAcF,EAAcE,IAAe,CACjE,IAEIC,EAFE9b,EAAagD,CAAW,CAAC6Y,EAAY,CAAEhb,EAAST,CAAO,CAACJ,EAAW,CAAEgG,EAAenF,EAAOhC,MAAM,CACjGmK,EAAaC,EAAUuB,MAAM,CAACxK,GAEhCgJ,GACA8S,CAAAA,EAAiB9S,EAAW+S,QAAQ,AAAD,EAEvC,IAAK,IAAI9b,EAAW,EAAGA,EAAW+F,EAAc/F,IAAY,CACxD,IAAIC,EAAYW,CAAM,CAACZ,EAAS,CAgBhC,GAfK2b,CAAQ,CAAC3b,EAAS,EACnB2b,CAAAA,CAAQ,CAAC3b,EAAS,CAAG,EAAE,AAAD,EAGtB6b,AAAmB,WAAnBA,EACA5b,EAAY,IAAMA,EAAY,IAEzB,AAAqB,UAArB,OAAOA,EACZA,EAAY8b,OAAO9b,GAAWyM,OAAO,CAAC,IAAKf,GAEjB,UAArB,OAAO1L,GACZA,CAAAA,EAAY,CAAC,CAAC,EAAEA,EAAU,CAAC,CAAC,AAAD,EAE/B0b,CAAQ,CAAC3b,EAAS,CAAC4b,EAAY,CAAG3b,EAE9B2b,IAAgBF,EAAe,EAAG,CAIlC,IAAIrX,EAAIuX,EACR,KAEI,AAFGD,CAAQ,CAAC3b,EAAS,CAACpB,MAAM,CAAG,GAE3Bod,AAAY,KAAK,IADLL,CAAQ,CAAC3b,EAAS,CAACqE,EAAE,EAIrCsX,CAAQ,CAAC3b,EAAS,CAACkJ,GAAG,GACtB7E,IAEJoX,EAAQtc,IAAI,CAACwc,CAAQ,CAAC3b,EAAS,CAACyN,IAAI,CAAC8N,GACzC,CACJ,CACJ,CACA,OAAOE,EAAQhO,IAAI,CAAC4N,EACxB,CAaApN,MAAM9P,CAAO,CAAEoB,CAAW,CAAE,CACxB,IAAwB4b,EAAY5O,AAAlB,IAAI,CAAwB4O,SAAS,CAAEc,EAAgBhB,GAAmB,IAAI,CAAC9c,OAAO,CAAEA,GAAU,CAAE+d,YAAAA,CAAW,CAAEb,cAAAA,CAAa,CAAExM,gBAAAA,CAAe,CAAE0M,cAAAA,CAAa,CAAE,CAAGU,EACjLE,EAAOC,EAAQ,EAAG,CAAEC,IAAAA,CAAG,CAAE1N,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAE,CAAGqN,EAAerb,EAWjE,GAVA2L,AAFkB,IAAI,CAEZpM,OAAO,CAAG,EAAE,CACtBoM,AAHkB,IAAI,CAGZ/N,IAAI,CAAC,CACXC,KAAM,QACN0B,QAASoM,AALK,IAAI,CAKCpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AAPK,IAAI,CAOCqB,OAAO,AAC9B,GACIyO,GAAOH,GACPG,CAAAA,EAAMH,EAAYG,EAAG,EAErBA,EAAK,CAgBL,GAfAF,EAAQE,EACH3P,OAAO,CAAC,WAAY,MACpBa,KAAK,CAAC8N,GAAiB,MACxB,CAAA,CAAC1M,GAAYA,EAAW,CAAA,GACxBA,CAAAA,EAAW,CAAA,EAEX,CAAA,CAACC,GAAUA,GAAUuN,EAAMvd,MAAM,AAAD,GAChCgQ,CAAAA,EAASuN,EAAMvd,MAAM,CAAG,CAAA,EAEvB2c,GACDhP,CAAAA,AAvBU,IAAI,CAuBJ+P,oBAAoB,CAC1B/P,AAxBM,IAAI,CAwBAgQ,cAAc,CAACJ,EAAK,EAIlCtN,EAAiB,CACjB,IAAMjB,EAAUuO,CAAK,CAAC,EAAE,CAAC5O,KAAK,CAACgO,GAAiBhP,AA7BtC,IAAI,CA6B4C+P,oBAAoB,EAAI,KAElF,IAAK,IAAIjY,EAAI,EAAGA,EAAIuJ,EAAQhP,MAAM,CAAEyF,IAChCuJ,CAAO,CAACvJ,EAAE,CAAGuJ,CAAO,CAACvJ,EAAE,CAACiJ,IAAI,GAAGZ,OAAO,CAAC,eAAgB,GAE3DH,CAlCU,IAAI,CAkCJqB,OAAO,CAAGA,EACpBe,GACJ,CACA,IAAI6N,EAAS,EACb,IAAKJ,EAAQzN,EAAUyN,GAASxN,EAAQwN,IAChCD,AAAoB,MAApBA,CAAK,CAACC,EAAM,CAAC,EAAE,CACfI,IAGAjQ,AA3CM,IAAI,CA4CLkQ,WAAW,CAACN,CAAK,CAACC,EAAM,CAAEA,EAAQzN,EAAW6N,EAGtDrB,CAAAA,EAAUvc,MAAM,EAChBuc,CAAS,CAAC,EAAE,CAACvc,MAAM,EACnBuc,AAAoB,SAApBA,CAAS,CAAC,EAAE,CAAC,EAAE,EACf,CAAC5O,AAlDS,IAAI,CAkDHpO,OAAO,CAACuP,UAAU,EAC7BnB,AAnDU,IAAI,CAmDJM,gBAAgB,CAACN,AAnDjB,IAAI,CAmDuBpM,OAAO,CAAC,EAAE,CAAE,KAAM,CAAA,GAG3D,IAAK,IAAIkE,EAAI,EAAGC,EAAOiI,AAtDT,IAAI,CAsDepM,OAAO,CAACvB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAAG,CAC5DzD,EAAS2L,AAvDC,IAAI,CAuDKpM,OAAO,CAACkE,EAAE,CAC7B,IAAK,IAAIc,EAAI,EAAGC,EAAOxE,EAAOhC,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EAC9C,GAAIvE,CAAM,CAACuE,EAAE,EAAI,AAAqB,UAArB,OAAOvE,CAAM,CAACuE,EAAE,CAAe,CAC5C,IAAIlF,EAAYsM,AA1Dd,IAAI,CA0DoBF,aAAa,CAACzL,CAAM,CAACuE,EAAE,EAC7ClF,aAAqBiL,MACrBjL,CAAAA,EAAYA,EAAUkO,OAAO,EAAC,EAElC5B,AA9DE,IAAI,CA8DIpM,OAAO,CAACkE,EAAE,CAACc,EAAE,CAAGlF,CAC9B,CAER,CACJ,CACAsM,AAnEkB,IAAI,CAmEZ/N,IAAI,CAAC,CACXC,KAAM,aACN0B,QAASoM,AArEK,IAAI,CAqECpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AAvEK,IAAI,CAuECqB,OAAO,AAC9B,EACJ,CAIA6O,YAAYC,CAAS,CAAEC,CAAS,CAAE,CAC9B,IAAMpQ,EAAY,IAAI,CAAEpM,EAAUoM,EAAUpM,OAAO,EAAI,EAAE,CAAEgb,EAAY5O,EAAU4O,SAAS,CAAE,CAAE3M,YAAAA,CAAW,CAAEC,UAAAA,CAAS,CAAE,CAAGlC,EAAUpO,OAAO,CAAEod,EAAiBhP,EAAUpO,OAAO,CAACod,aAAa,EACxLhP,EAAU+P,oBAAoB,CAC9B,CAAE3Q,aAAAA,CAAY,CAAE,CAAGY,EAAUpO,OAAO,CACnCwN,GAAgBA,IAAiB4P,GAClC5P,CAAAA,EAAeY,EAAUqQ,mBAAmB,EAAI,GAAE,EAEtD,IAAIvY,EAAI,EAAGwY,EAAI,GAAIC,EAAQ,GAAIC,EAAe,EAAGnc,EAAS,EACpDoc,EAAO,AAAC7X,IACV0X,EAAIH,CAAS,CAACvX,EAAE,AACpB,EACM8X,EAAW,AAACxe,IACV0c,EAAUvc,MAAM,CAAGgC,EAAS,GAC5Bua,EAAUhc,IAAI,CAAC,CAACV,EAAK,EAErB0c,CAAS,CAACva,EAAO,CAACua,CAAS,CAACva,EAAO,CAAChC,MAAM,CAAG,EAAE,GAAKH,GACpD0c,CAAS,CAACva,EAAO,CAACzB,IAAI,CAACV,EAE/B,EACMU,EAAO,KACT,GAAIqP,EAAcuO,GAAgBA,EAAetO,EAAW,CAExD,EAAEsO,EACFD,EAAQ,GACR,MACJ,CAuBA,GArBI,AAAiB,UAAjB,OAAOA,EACH,CAACpX,MAAMC,WAAWmX,KAAW1V,SAAS0V,IACtCA,EAAQnX,WAAWmX,GACnBG,EAAS,WAEHvX,MAAMwF,KAAK+C,KAAK,CAAC6O,IAKvBG,EAAS,WAJTH,EAAQA,EAAMpQ,OAAO,CAAC,MAAO,KAC7BuQ,EAAS,SAObA,EAAS,UAET9c,EAAQvB,MAAM,CAAGgC,EAAS,GAC1BT,EAAQhB,IAAI,CAAC,EAAE,EAIf,AAAiB,UAAjB,OAAO2d,GACPvQ,AAA+B,WAA/BA,EAAUC,SAAS,CAACsQ,IACpBnR,EAAc,CACd,IAAMuR,EAAeJ,EACrBA,EAAQA,EAAMpQ,OAAO,CAACf,EAAc,KACD,WAA/BY,EAAUC,SAAS,CAACsQ,IACpBA,CAAAA,EAAQI,CAAW,CAE3B,CACA/c,CAAO,CAACS,EAAO,CAAC+b,EAAU,CAAGG,EAC7BA,EAAQ,GACR,EAAElc,EACF,EAAEmc,CACN,EACA,GAAKL,EAAUpP,IAAI,GAAG1O,MAAM,EAGxB8d,AAAwB,MAAxBA,EAAUpP,IAAI,EAAE,CAAC,EAAE,EAGvB,KAAOjJ,EAAIqY,EAAU9d,MAAM,CAAEyF,IAAK,CAE9B,GADA2Y,EAAK3Y,GACDwY,AAAM,MAANA,GAEI,CAAC,+BAA+BlQ,IAAI,CAAC+P,EAAUjJ,SAAS,CAACpP,IAAK,CAE9DlF,IACA,MACJ,CAGJ,GAAI0d,AAAM,MAANA,EAEA,IADAG,EAAK,EAAE3Y,GAEH,AADGA,EAAIqY,EAAU9d,MAAM,EACnBie,AAAM,MAANA,GAGJC,GAASD,EACTG,EAAK,EAAE3Y,QAGNwY,IAAMtB,EACXpc,IAIA2d,GAASD,CAEjB,CACA1d,IACJ,CAOAod,eAAeJ,CAAK,CAAE,CAClB,IAAIgB,EAAS,EAAGC,EAAS,EAAGC,EACtBC,EAAgB,CAClB,IAAK,EACL,IAAK,EACL,IAAM,CACV,EAAGC,EAAapB,EAAMvd,MAAM,CAC5B,IAAK,IAAIyF,EAAI,EAAGA,EAAIkZ,EAAYlZ,IAAK,CACjC,IAAImZ,EAAQ,CAAA,EAAOX,EAAGY,EAAIC,EAAIZ,EAAQ,GAEtC,GAAIzY,EAAI,GACJ,MAEJ,IAAMqY,EAAYP,CAAK,CAAC9X,EAAE,CAC1B,IAAK,IAAIc,EAAI,EAIT,AAJYA,EAAIuX,EAAU9d,MAAM,GAChCie,EAAIH,CAAS,CAACvX,EAAE,CAChBsY,EAAKf,CAAS,CAACvX,EAAI,EAAE,CACrBuY,EAAKhB,CAAS,CAACvX,EAAI,EAAE,CACjB0X,AAAM,MAANA,GAJ8B1X,IAAK,CAQvC,GAAI0X,AAAM,MAANA,GACA,GAAIW,EACA,CAAA,GAAIE,AAAO,MAAPA,GAAcD,AAAO,MAAPA,EAAY,CAC1B,KAAOA,AAAO,MAAPA,GAActY,EAAIuX,EAAU9d,MAAM,EACrC6e,EAAKf,CAAS,CAAC,EAAEvX,EAAE,AAKU,MAAA,IAAtBmY,CAAa,CAACG,EAAG,EACxBH,CAAa,CAACG,EAAG,GAErBD,EAAQ,CAAA,CACZ,CAAA,MAGAA,EAAQ,CAAA,OAGP,AAA4B,KAAA,IAArBF,CAAa,CAACT,EAAE,EAEvBnX,MAAMwF,KAAK+C,KAAK,CADrB6O,EAAQA,EAAMxP,IAAI,KAIT5H,CAAAA,MAAMyB,OAAO2V,KAClB,CAAC1V,SAASD,OAAO2V,GAAM,GACvBQ,CAAa,CAACT,EAAE,GAJhBS,CAAa,CAACT,EAAE,GAMpBC,EAAQ,IAGRA,GAASD,CAEH,CAAA,MAANA,GACAO,IAEM,MAANP,GACAM,GAER,CACJ,CAsBA,OAlBIG,CAAa,CAAC,IAAI,CAAGA,CAAa,CAAC,IAAI,CACvCD,EAAU,KAELC,CAAa,CAAC,IAAI,CAAGA,CAAa,CAAC,IAAI,CAC5CD,EAAU,KAQVF,EAASC,EACT,IAAI,CAACR,mBAAmB,CAAG,IAG3B,IAAI,CAACA,mBAAmB,CAAG,IAExBS,CACX,CAOAxP,UAAW,CACP,OAAOoB,EAAyBD,mBAAmB,CAAC,IAAI,CAAC7O,OAAO,CAAE,IAAI,CAACyN,OAAO,CAClF,CACJ,CASAsN,GAAazP,cAAc,CAAG,CAC1B,GAAGwD,EAAyBxD,cAAc,CAC1C4P,cAAe,IACnB,EACApM,EAAyBzO,YAAY,CAAC,MAAO0a,IA4B7C,GAAM,CAAEld,MAAO2f,EAAkB,CAAE,CAAI9f,GAWvC,OAAM+f,WAAqBnT,EAYvB/I,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgBmS,GAAmBC,GAAanS,cAAc,CAAEtN,GACtE,KAAK,CAACqN,GACN,IAAI,CAACe,SAAS,CAAG,IAhDqC2O,GAgDT1P,GAC7C,IAAI,CAACrN,OAAO,CAAGqN,EACXA,EAAcqS,aAAa,EAC3B,IAAI,CAAC3T,YAAY,CAAC1H,AAAkD,IAAlDA,KAAKC,GAAG,CAAC+I,EAAcsS,eAAe,EAAI,EAAG,GAEvE,CAeAnU,KAAKpK,CAAW,CAAE,CACd,IAAMyJ,EAAY,IAAI,CAAEuD,EAAYvD,EAAUuD,SAAS,CAAEjN,EAAQ0J,EAAU1J,KAAK,CAAE,CAAE+c,IAAAA,CAAG,CAAE0B,OAAAA,CAAM,CAAEC,aAAAA,CAAY,CAAE,CAAGhV,EAAU7K,OAAO,CAOnI,OANA6K,EAAUxK,IAAI,CAAC,CACXC,KAAM,OACN4d,IAAAA,EACAxc,OAAQN,EACRD,MAAAA,CACJ,GACOE,QACFC,OAAO,CAACse,EACTE,MAAMF,GAAQjW,IAAI,CAAC,AAACoW,GAAaA,EAAS7K,IAAI,IAC9CgJ,GAAO,IACNvU,IAAI,CAAC,AAACuU,IACHA,IAEA/c,EAAMsF,aAAa,GACnB2H,EAAU0B,KAAK,CAAC,CAAEoO,IAAAA,CAAI,GACtB/c,EAAM+D,UAAU,CAACkJ,EAAUsB,QAAQ,GAAG/K,UAAU,KAE7CkG,EACFe,kBAAkB,CAACiU,GACnBlW,IAAI,CAAC,IAAMuU,KAEfvU,IAAI,CAAC,AAACuU,IACPrT,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACN4d,IAAAA,EACAxc,OAAQN,EACRD,MAAAA,CACJ,GACO0J,IACR,KAAQ,CAAC,AAACjB,IAOT,MANAiB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRwI,MAAAA,EACAzI,MAAAA,CACJ,GACMyI,CACV,EACJ,CACJ,CAMA6V,GAAanS,cAAc,CAAG,CAC1B4Q,IAAK,GACL0B,OAAQ,GACRF,cAAe,CAAA,EACfC,gBAAiB,EACjBjP,gBAAiB,CAAA,CACrB,EACApE,EAAyBjK,YAAY,CAAC,MAAOod,IAyB7C,GAAM,CAAE7V,MAAAA,EAAK,CAAEhH,QAAAA,EAAO,CAAE/C,MAAOmgB,EAAmB,CAAEnc,WAAYoc,EAAwB,CAAE,CAAIvgB,GAW9F,OAAMwgB,WAAsBpP,EAYxBvN,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgB2S,GAAoBE,GAAc5S,cAAc,CAAEtN,GACxE,KAAK,CAACqN,GAMN,IAAI,CAACrL,OAAO,CAAG,EAAE,CACjB,IAAI,CAACyN,OAAO,CAAG,EAAE,CACjB,IAAI,CAACzP,OAAO,CAAGqN,EACf,IAAI,CAAClM,KAAK,CAAG,IApkJgC4E,CAqkJjD,CAkBA+J,MAAM9P,CAAO,CAAEoB,CAAW,CAAE,CAGxB,GAAM,CAAE2c,YAAAA,CAAW,CAAEoC,YAAAA,CAAW,CAAEzP,gBAAAA,CAAe,CAAE9L,YAAAA,CAAW,CAAE,CADhE5E,EAAUggB,GAAoB5R,AADZ,IAAI,CACkBpO,OAAO,CAAEA,GAE7C2O,EAAO3O,EAAQ2O,IAAI,CACvB,GAAKA,GAcL,GAXAP,AAPkB,IAAI,CAOZpM,OAAO,CAAG,EAAE,CACtBoM,AARkB,IAAI,CAQZ/N,IAAI,CAAC,CACXC,KAAM,QACN0B,QAASoM,AAVK,IAAI,CAUCpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AAZK,IAAI,CAYCqB,OAAO,AAC9B,GACIsO,GACApP,CAAAA,EAAOoP,EAAYpP,EAAI,EAE3BA,EAAOA,EAAKvK,KAAK,GACb+b,AAAgB,YAAhBA,EACA,IAAK,IAAIja,EAAI,EAAGC,EAAOwI,EAAKlO,MAAM,CAAEyF,EAAIC,EAAMD,IAAK,CAC/C,IAAMsR,EAAO7I,CAAI,CAACzI,EAAE,CACpB,GAAI,CAAEsR,CAAAA,aAAgB7U,KAAI,EACtB,MAEAyL,CAxBM,IAAI,CAwBAqB,OAAO,YAAY9M,OACzB+N,EACAtC,AA1BE,IAAI,CA0BIqB,OAAO,CAACzO,IAAI,CAAC,CAAC,EAAEwW,EAAK4I,KAAK,GAAG,CAAC,EAEnCxb,GAAeA,aAAuBjC,OAC3CyL,AA7BE,IAAI,CA6BIqB,OAAO,CAACzO,IAAI,CAAC4D,CAAW,CAACsB,EAAE,EAEzCkI,AA/BM,IAAI,CA+BAjN,KAAK,CAAC8D,SAAS,CAACmJ,AA/BpB,IAAI,CA+B0BqB,OAAO,CAACvJ,EAAE,EAAIA,EAAEma,QAAQ,GAAI7I,IAGhE5N,GAAM,+CAAgD,CAAA,EAE9D,MAEC,GAAIuW,AAAgB,SAAhBA,EAAwB,CACzBzP,EACAtC,AAxCU,IAAI,CAwCJqB,OAAO,CAAGd,EAAKyR,KAAK,GAEzBxb,GACLwJ,CAAAA,AA3CU,IAAI,CA2CJqB,OAAO,CAAG7K,CAAU,EAElC,IAAK,IAAI/C,EAAW,EAAGsE,EAAOwI,EAAKlO,MAAM,CAAEoB,EAAWsE,EAAMtE,IAAY,CACpE,IAAIwD,EAAMsJ,CAAI,CAAC9M,EAAS,CACxB,GAAIe,GAAQyC,GACR,IAAK,IAAIoY,EAAc,EAAGxW,EAAO5B,EAAI5E,MAAM,CAAEgd,EAAcxW,EAAMwW,IACzDrP,AAjDF,IAAI,CAiDQpM,OAAO,CAACvB,MAAM,CAAGgd,EAAc,GACzCrP,AAlDF,IAAI,CAkDQpM,OAAO,CAAChB,IAAI,CAAC,EAAE,EAE7BoN,AApDE,IAAI,CAoDIpM,OAAO,CAACyb,EAAY,CAACzc,IAAI,CAACqE,CAAG,CAACoY,EAAY,EAChDrP,AArDF,IAAI,CAqDQqB,OAAO,YAAY9M,MAC7B,IAAI,CAACxB,KAAK,CAAC8D,SAAS,CAACmJ,AAtDvB,IAAI,CAsD6BqB,OAAO,CAACgO,EAAY,EAC/CA,EAAY4C,QAAQ,GAAIjS,AAvD9B,IAAI,CAuDoCpM,OAAO,CAACyb,EAAY,EAG1D7T,GAAM,+CAAgD,CAAA,OAI7D,CACD,IAAMhF,EAAcwJ,AA/Dd,IAAI,CA+DoBqB,OAAO,CACrC,GAAI7K,GAAe,CAAEA,CAAAA,aAAuBjC,KAAI,EAAI,CAChD,IAAM2d,EAAS,CAAC,EAChBL,GAAyBrb,EAAa,CAAC2b,EAAe5V,KAClD2V,CAAM,CAAC3V,EAAK,CAAG4V,EAAczb,MAAM,CAAC,CAAC0b,EAAK1hB,IAAQ0hB,CAAG,CAAC1hB,EAAI,CAAEuG,EAChE,GACAA,EAAMib,CACV,CACA,IAAI,CAACnf,KAAK,CAAC8I,OAAO,CAAC,CAAC5E,EAAI,CAAExD,EAC9B,CACJ,CACJ,CACAuM,AA3EkB,IAAI,CA2EZ/N,IAAI,CAAC,CACXC,KAAM,aACN0B,QAASoM,AA7EK,IAAI,CA6ECpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AA/EK,IAAI,CA+ECqB,OAAO,AAC9B,GACJ,CAOAC,UAAW,CACP,OAAO,IAAI,CAACvO,KAAK,AACrB,CACJ,CASA+e,GAAc5S,cAAc,CAAG,CAC3B,GAAGwD,EAAyBxD,cAAc,CAC1CqB,KAAM,EAAE,CACRwR,YAAa,MACjB,EACArP,EAAyBzO,YAAY,CAAC,OAAQ6d,IAyB9C,GAAM,CAAErgB,MAAO4gB,EAAmB,CAAE,CAAI/gB,GAWxC,OAAMghB,WAAsBpU,EAYxB/I,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgBoT,GAAoBC,GAAcpT,cAAc,CAAEtN,GACxE,KAAK,CAACqN,GACN,IAAI,CAACe,SAAS,CAAG,IA7CsC8R,GA6CT7S,GAC9C,IAAI,CAACrN,OAAO,CAAGqN,EACXA,EAAcqS,aAAa,EAC3B,IAAI,CAAC3T,YAAY,CAAC1H,AAAkD,IAAlDA,KAAKC,GAAG,CAAC+I,EAAcsS,eAAe,EAAI,EAAG,GAEvE,CAeAnU,KAAKpK,CAAW,CAAE,CACd,IAAMyJ,EAAY,IAAI,CAAEuD,EAAYvD,EAAUuD,SAAS,CAAEjN,EAAQ0J,EAAU1J,KAAK,CAAE,CAAEwN,KAAAA,CAAI,CAAEgS,QAAAA,CAAO,CAAEd,aAAAA,CAAY,CAAE,CAAGhV,EAAU7K,OAAO,CAOrI,OANA6K,EAAUxK,IAAI,CAAC,CACXC,KAAM,OACNqO,KAAAA,EACAjN,OAAQN,EACRD,MAAAA,CACJ,GACOE,QACFC,OAAO,CAACqf,EACTb,MAAMa,GAAShX,IAAI,CAAC,AAACoW,GAAaA,EAASa,IAAI,IAAI,KAAQ,CAAC,AAAChX,IACzDiB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRwI,MAAAA,EACAzI,MAAAA,CACJ,GACA0f,QAAQC,IAAI,CAAC,CAAC,0BAA0B,EAAEH,EAAQ,CAAC,CAAC,CACxD,GACAhS,GAAQ,EAAE,EACThF,IAAI,CAAC,AAACgF,IACHA,IAEAxN,EAAMsF,aAAa,GACnB2H,EAAU0B,KAAK,CAAC,CAAEnB,KAAAA,CAAK,GACvBxN,EAAM+D,UAAU,CAACkJ,EAAUsB,QAAQ,GAAG/K,UAAU,KAE7CkG,EAAUe,kBAAkB,CAACiU,GAAclW,IAAI,CAAC,IAAMgF,KAE5DhF,IAAI,CAAC,AAACgF,IACP9D,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNqO,KAAAA,EACAjN,OAAQN,EACRD,MAAAA,CACJ,GACO0J,IACR,KAAQ,CAAC,AAACjB,IAOT,MANAiB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRwI,MAAAA,EACAzI,MAAAA,CACJ,GACMyI,CACV,EACJ,CACJ,CAMA8W,GAAcpT,cAAc,CAAG,CAC3BqB,KAAM,EAAE,CACR+Q,cAAe,CAAA,EACfC,gBAAiB,EACjBjP,gBAAiB,CAAA,EACjByP,YAAa,MACjB,EACA7T,EAAyBjK,YAAY,CAAC,OAAQqe,IA2B9C,GAAM,CAAE7gB,MAAOkhB,EAA2B,CAAEjd,UAAWkd,EAA+B,CAAE,CAAIthB,GAW5F,OAAMuhB,WAA8BnQ,EAYhCvN,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgB0T,GAA4BE,GAAsB3T,cAAc,CAAEtN,GACxF,KAAK,CAACqN,GACN,IAAI,CAACrL,OAAO,CAAG,EAAE,CACjB,IAAI,CAACkf,MAAM,CAAG,EAAE,CAChB,IAAI,CAAClhB,OAAO,CAAGqN,CACnB,CAkBAyC,MAAM9P,CAAO,CAAEoB,CAAW,CAAE,CACxB,IAkBIqB,EAlBoB0e,EAAeJ,GAA4B3S,AAAjD,IAAI,CAAuDpO,OAAO,CAAEA,GAClFgC,EAAU,AAAC,CAAA,AAACmf,EAAaP,IAAI,EAAEpH,QAAW,EAAE,AAAD,EAAGxU,GAAG,CAAC,AAACvC,GAAWA,EAAO2B,KAAK,IAC9E,GAAIpC,AAAmB,IAAnBA,EAAQvB,MAAM,CACd,MAAO,CAAA,CAEX2N,CALkB,IAAI,CAKZ8S,MAAM,CAAG,EAAE,CACrB9S,AANkB,IAAI,CAMZpM,OAAO,CAAG,EAAE,CACtBoM,AAPkB,IAAI,CAOZ/N,IAAI,CAAC,CACXC,KAAM,QACN0B,QAASoM,AATK,IAAI,CASCpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AAXK,IAAI,CAWC8S,MAAM,AAC7B,GAEA,GAAM,CAAEnD,YAAAA,CAAW,CAAE6C,KAAAA,CAAI,CAAE,CAAGO,EAC1BpD,GAAe6C,GACf5e,CAAAA,EAAU+b,EAAY6C,EAAKpH,MAAM,CAAA,EAGrCpL,AAnBkB,IAAI,CAmBZpM,OAAO,CAAGA,EACpB,IAAK,IAAIkE,EAAI,EAAGC,EAAOnE,EAAQvB,MAAM,CAAEyF,EAAIC,EAAMD,IAAK,CAClDzD,EAAST,CAAO,CAACkE,EAAE,CACnBkI,AAtBc,IAAI,CAsBR8S,MAAM,CAAChb,EAAE,CAAIib,EAAazQ,eAAe,CAC/C,CAAC,EAAEjO,EAAO2d,KAAK,GAAG,CAAC,CACnBY,KACJ,IAAK,IAAIha,EAAI,EAAGC,EAAOxE,EAAOhC,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EAC9C,GAAIvE,CAAM,CAACuE,EAAE,EAAI,AAAqB,UAArB,OAAOvE,CAAM,CAACuE,EAAE,CAAe,CAC5C,IAAIlF,EAAYsM,AA3BV,IAAI,CA2BgBF,aAAa,CAACzL,CAAM,CAACuE,EAAE,EAC7ClF,aAAqBiL,MACrBjL,CAAAA,EAAYA,EAAUkO,OAAO,EAAC,EAElC5B,AA/BM,IAAI,CA+BApM,OAAO,CAACkE,EAAE,CAACc,EAAE,CAAGlF,CAC9B,CAER,CACAsM,AAnCkB,IAAI,CAmCZ/N,IAAI,CAAC,CACXC,KAAM,aACN0B,QAASoM,AArCK,IAAI,CAqCCpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AAvCK,IAAI,CAuCC8S,MAAM,AAC7B,EACJ,CAOAxR,UAAW,CACP,OAAOoB,EAAyBD,mBAAmB,CAAC,IAAI,CAAC7O,OAAO,CAAE,IAAI,CAACkf,MAAM,CACjF,CACJ,CASAD,GAAsB3T,cAAc,CAAG,CACnC,GAAGwD,EAAyBxD,cAAc,AAC9C,EACAwD,EAAyBzO,YAAY,CAAC,eAAgB4e,IA6BtD,GAAM,CAAEphB,MAAOuhB,EAA2B,CAAE9W,KAAM+W,EAA0B,CAAE,CAAI3hB,GA0BlF,OAAM4hB,WAA8BhV,EAYhC/I,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgB+T,GAA4BE,GAAsBhU,cAAc,CAAEtN,GACxF,KAAK,CAACqN,GACN,IAAI,CAACe,SAAS,CAAG,IAhE8C6S,GAgET5T,GACtD,IAAI,CAACrN,OAAO,CAAGqN,CACnB,CAeA7B,KAAKpK,CAAW,CAAE,CACd,IAAMyJ,EAAY,IAAI,CAAEuD,EAAYvD,EAAUuD,SAAS,CAAEjN,EAAQ0J,EAAU1J,KAAK,CAAE,CAAE0e,aAAAA,CAAY,CAAEF,gBAAAA,CAAe,CAAED,cAAAA,CAAa,CAAEhP,gBAAAA,CAAe,CAAE6Q,aAAAA,CAAY,CAAEC,qBAAAA,CAAoB,CAAE,CAAG3W,EAAU7K,OAAO,CAAEyhB,EAAMH,GAAsBI,aAAa,CAACH,EAAcC,EAAsB3W,EAAU7K,OAAO,EAO5S,GANA6K,EAAUxK,IAAI,CAAC,CACXC,KAAM,OACNoB,OAAQN,EACRD,MAAAA,EACAsgB,IAAAA,CACJ,GACI,CAACE,IAAIC,QAAQ,CAACH,GACd,MAAM,AAAI/V,MAAM,gBAAkB+V,GAEtC,OAAO3B,MAAM2B,GACR9X,IAAI,CAAC,AAACoW,GAAcA,EAASa,IAAI,IACjCjX,IAAI,CAAC,AAACiX,IACP,GA7DA,AAAgB,UAAhB,OA6DkBA,GAAAA,GA5DtB,AAAsB,UAAtB,OAAOA,AA4DeA,EA5DVhX,KAAK,EAAiBgX,AA4DZA,EA5DiBhX,KAAK,EAC5C,AAA2B,UAA3B,OAAOgX,AA2DeA,EA3DVhX,KAAK,CAACoN,IAAI,EACtB,AAA8B,UAA9B,OAAO4J,AA0DeA,EA1DVhX,KAAK,CAACiY,OAAO,EACzB,AAA6B,UAA7B,OAAOjB,AAyDeA,EAzDVhX,KAAK,CAACkY,MAAM,CA0DhB,MAAM,AAAIpW,MAAMkV,EAAKhX,KAAK,CAACiY,OAAO,EAStC,OAPAzT,EAAU0B,KAAK,CAAC,CACZY,gBAAAA,EACAkQ,KAAAA,CACJ,GAEAzf,EAAMsF,aAAa,GACnBtF,EAAM+D,UAAU,CAACkJ,EAAUsB,QAAQ,GAAG/K,UAAU,IACzCkG,EAAUe,kBAAkB,CAACiU,EACxC,GACKlW,IAAI,CAAC,KACNkB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRD,MAAAA,EACAsgB,IAAAA,CACJ,GAEI/B,GACAxT,WAAW,IAAMrB,EAAUW,IAAI,GAAInH,AAAoC,IAApCA,KAAKC,GAAG,CAACqb,GAAmB,EAAG,IAE/D9U,IACR,KAAQ,CAAC,AAACjB,IAOT,MANAiB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRwI,MAAAA,EACAzI,MAAAA,CACJ,GACMyI,CACV,EACJ,CACJ,CAMA0X,GAAsBhU,cAAc,CAAG,CACnCiU,aAAc,GACdC,qBAAsB,GACtB9B,cAAe,CAAA,EACfC,gBAAiB,EACjBjP,gBAAiB,CAAA,CACrB,EAMA,AAAC,SAAU4Q,CAAqB,EAW5B,IAAMS,EAAW,6BA+BjB,SAASC,EAAgBhiB,EAAU,CAAC,CAAC,EACjC,GAAM,CAAEsQ,UAAAA,CAAS,CAAEG,OAAAA,CAAM,CAAEwR,uBAAAA,CAAsB,CAAE5R,YAAAA,CAAW,CAAEG,SAAAA,CAAQ,CAAE,CAAGxQ,EAC7E,OAAOiiB,GAA2B,AAACF,CAAAA,CAAQ,CAAC1R,GAAe,EAAE,EAAI,GAAE,EAC9DhM,CAAAA,KAAKC,GAAG,CAAEkM,GAAY,EAAI,GAAK,CAAA,EAChC,IACCuR,CAAAA,CAAQ,CAACV,GAA2B/Q,EAAW,IAAI,EAAI,GAAE,EACzDG,CAAAA,EACGpM,KAAKC,GAAG,CAACmM,EAAQ,GACjB,GAAE,CACd,CAdA6Q,EAAsBI,aAAa,CAhBnC,SAAuBQ,CAAM,CAAEC,CAAQ,CAAEniB,EAAU,CAAC,CAAC,EACjD,IAAMyhB,EAAM,IAAIE,IAAI,CAAC,8CAA8C,EAAEQ,EAAS,QAAQ,CAAC,EACjF7P,EAAQtS,EAAQoiB,eAAe,CACjC,QAAUJ,EAAgBhiB,EAC9ByhB,CAAAA,EAAIY,QAAQ,EAAI/P,EAChB,IAAMgQ,EAAeb,EAAIa,YAAY,CASrC,OARAA,EAAa7e,GAAG,CAAC,MAAO,QACnBzD,EAAQoiB,eAAe,GACxBE,EAAa7e,GAAG,CAAC,uBAAwB,oBACzC6e,EAAa7e,GAAG,CAAC,iBAAkB,WACnC6e,EAAa7e,GAAG,CAAC,oBAAqB,sBAE1C6e,EAAa7e,GAAG,CAAC,cAAe,SAChC6e,EAAa7e,GAAG,CAAC,MAAOye,GACjBT,EAAIc,IAAI,AACnB,EAgBAjB,EAAsBU,eAAe,CAAGA,CAC5C,EAAGV,IAA0BA,CAAAA,GAAwB,CAAC,CAAA,GACtDhV,EAAyBjK,YAAY,CAAC,eAAgBif,IA2BtD,GAAM,CAAEzhB,MAAO2iB,EAAwB,CAAE,CAAI9iB,GAiC7C,OAAM+iB,WAA2B3R,EAY7BvN,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgBmV,GAAyBC,GAAmBnV,cAAc,CAAEtN,GAClF,KAAK,CAACqN,GACN,IAAI,CAACrL,OAAO,CAAG,EAAE,CACjB,IAAI,CAACyN,OAAO,CAAG,EAAE,CACjB,IAAI,CAACzP,OAAO,CAAGqN,EACXA,EAAcqV,YAAY,GAC1B,IAAI,CAACA,YAAY,CAAGrV,EAAcqV,YAAY,CAC9C,IAAI,CAACC,cAAc,CAAGtV,EAAcqV,YAAY,CAACze,EAAE,CAE3D,CAmBAuL,OAAO3E,CAAS,CAAE7K,EAAU,IAAI,CAACA,OAAO,CAAE,CACtC,IAAMmd,EAAend,AAA4B,CAAA,IAA5BA,EAAQ0Q,eAAe,CAAakS,EAAuB5iB,EAAQ4iB,oBAAoB,CACtG5gB,EAAU6I,EAAUS,gBAAgB,CAACtL,EAAQuL,oBAAoB,EAAG3G,EAAc5F,OAAO6F,IAAI,CAAC7C,GAAU6gB,EAAW,EAAE,CAAEtF,EAAe3Y,EAAYnE,MAAM,CACxJ+c,EAAW,EAAE,CACfsF,EAAY,GAEhB,GAAI3F,EAAa,CACb,IAAM4F,EAAgB,EAAE,CAGxB,GAAIH,EAAsB,CACtB,IAAK,IAAMjY,KAAQ/F,EAAa,CAC5B,IAAInC,EAAST,CAAO,CAAC2I,EAAK,CACrBhI,MAAMC,OAAO,CAACH,IAGfA,CAAAA,EAASE,MAAMO,IAAI,CAACT,EAAM,EAE9B,IAAMugB,EAAU,AAACvgB,CAAAA,EAAO2d,KAAK,IAAM,EAAC,EAAGC,QAAQ,EAC/Cre,CAAAA,CAAO,CAAC2I,EAAK,CAAGlI,EAChBsgB,EAAc/hB,IAAI,CAACgiB,EACvB,CACAF,EAAY,IAAI,CAACG,kBAAkB,CAACre,EAAame,EAAe/iB,EACpE,MAEI8iB,EAAY,IAAI,CAACG,kBAAkB,CAAC,KAAK,EAAGre,EAAa5E,EAEjE,CACA,IAAK,IAAIyd,EAAc,EAAGA,EAAcF,EAAcE,IAAe,CACjE,IAA6Chb,EAAST,CAAO,CAA1C4C,CAAW,CAAC6Y,EAAY,CAA8B,CAAE7V,EAAenF,EAAOhC,MAAM,CACvG,IAAK,IAAIoB,EAAW,EAAGA,EAAW+F,EAAc/F,IAAY,CACxD,IAAIC,EAAYW,CAAM,CAACZ,EAAS,AAC3B2b,CAAAA,CAAQ,CAAC3b,EAAS,EACnB2b,CAAAA,CAAQ,CAAC3b,EAAS,CAAG,EAAE,AAAD,EAIC,UAArB,OAAOC,GACT,AAAqB,UAArB,OAAOA,GACP,AAAqB,KAAA,IAAdA,GACPA,CAAAA,EAAY,AAACA,CAAAA,GAAa,EAAC,EAAGue,QAAQ,EAAC,EAE3C7C,CAAQ,CAAC3b,EAAS,CAAC4b,EAAY,CAAG,IAAI,CAACyF,oBAAoB,CAACzF,EAAc,KAAO,KAAM,KAAMA,EAAc,GAAK,cAAe3b,GAE3H2b,IAAgBF,EAAe,GAC/BsF,EAAS7hB,IAAI,CAAC,OACVwc,CAAQ,CAAC3b,EAAS,CAACyN,IAAI,CAAC,IACxB,QAEZ,CACJ,CACA,IAAI6T,EAAU,GASd,OALInjB,EAAQojB,YAAY,EACpBD,CAAAA,EAAU,6CACNnjB,EAAQojB,YAAY,CACpB,YAAW,EAEX,UACJD,EACAL,EACA,UACAD,EAASvT,IAAI,CAAC,IAJV,kBAOZ,CAIA4T,qBAAqBG,CAAG,CAAEC,CAAO,CAAEC,CAAK,CAAE3V,CAAK,CAAEJ,CAAY,CAAE,CAC3D,IAAIgW,EAAM5V,EAAO6V,EAAY,OAAUH,CAAAA,EAAU,IAAMA,EAAU,EAAC,EAalE,MAXI,AAAe,UAAf,OAAOE,GACPA,EAAMA,EAAInD,QAAQ,GACG,MAAjB7S,GACAgW,CAAAA,EAAMA,EAAIjV,OAAO,CAAC,IAAKf,EAAY,EAEvCiW,EAAY,UAEN7V,IACN4V,EAAM,GACNC,EAAY,SAET,IAAMJ,EAAOE,CAAAA,EAAQ,IAAMA,EAAQ,EAAC,EACvC,WAAaE,EAAY,KACzBD,EAAM,KAAOH,EAAM,GAC3B,CAIAJ,mBAAmBS,EAAa,EAAE,CAAEC,EAAa,EAAE,CAAE3jB,EAAU,IAAI,CAACA,OAAO,CAAE,CACzE,GAAM,CAAE4iB,qBAAAA,CAAoB,CAAEgB,kBAAAA,CAAiB,CAAE,CAAG5jB,EAChD6jB,EAAO,UAAW3d,EAAI,EAAG4d,EAAMH,GAAcA,EAAWljB,MAAM,CAAQsjB,EAAKC,EAAa,EAAGC,EAK/F,GAAIrB,GACAc,GACAC,GACA,CAACO,AAvKb,SAAoBC,CAAI,CAAEC,CAAI,EAC1B,IAAIle,EAAIie,EAAK1jB,MAAM,CACnB,GAAI2jB,EAAK3jB,MAAM,GAAKyF,EAQhB,MAAO,CAAA,EAPP,KAAO,EAAEA,GACL,GAAIie,CAAI,CAACje,EAAE,GAAKke,CAAI,CAACle,EAAE,CACnB,MAAO,CAAA,EAOnB,MAAO,CAAA,CACX,EA0JwBwd,EAAYC,GAAa,CAErC,IADAE,GAAQ,OACD3d,EAAI4d,EAAK,EAAE5d,EAGV6d,AAFJA,CAAAA,EAAML,CAAU,CAACxd,EAAE,AAAD,IACXwd,CAAU,CAACxd,EAAI,EAAE,CAEpB,EAAE8d,EAEGA,GAGLH,GAAQ,IAAI,CAACX,oBAAoB,CAAC,KAAM,8BAA+B,wBACpDc,CAAAA,EAAa,CAAA,EAAK,IAAKD,GAC1CC,EAAa,IAKTD,IAAQJ,CAAU,CAACzd,EAAE,CACjB0d,GACAK,EAAU,EACV,OAAON,CAAU,CAACzd,EAAE,GAGpB+d,EAAU,EACVN,CAAU,CAACzd,EAAE,CAAG,IAIpB+d,EAAU,EAEdJ,GAAQ,IAAI,CAACX,oBAAoB,CAAC,KAAM,8BAA+B,cAClEe,CAAAA,EAAU,EACP,0BAA4BA,EAAU,IACtC,EAAC,EAAIF,IAGrBF,GAAQ,OACZ,CAEA,GAAIF,EAAY,CAEZ,IAAKzd,AADL2d,GAAQ,OACH3d,EAAI,EAAG4d,EAAMH,EAAWljB,MAAM,CAAEyF,EAAI4d,EAAK,EAAE5d,EACf,KAAA,IAAlByd,CAAU,CAACzd,EAAE,EACpB2d,CAAAA,GAAQ,IAAI,CAACX,oBAAoB,CAAC,KAAM,KAAM,cAAeS,CAAU,CAACzd,EAAE,CAAA,EAGlF2d,GAAQ,OACZ,CAEA,OADAA,EAAQ,UAEZ,CAcA/T,MAAM9P,CAAO,CAAEoB,CAAW,CAAE,CACxB,IAAwBY,EAAU,EAAE,CAAEyN,EAAU,EAAE,CAAE0R,EAAeqB,GAAyBpU,AAA1E,IAAI,CAAgFpO,OAAO,CAAEA,GAAU,CAAEyQ,OAAAA,CAAM,CAAEJ,YAAAA,CAAW,CAAEC,UAAAA,CAAS,CAAEI,gBAAAA,CAAe,CAAE,CAAGyQ,EAAckD,EAAYlD,EAAauB,YAAY,EAAI,IAAI,CAACA,YAAY,CACvP,GAAI,CAAE2B,CAAAA,aAAqBC,WAAU,EAAI,CACrClW,AAFc,IAAI,CAER/N,IAAI,CAAC,CACXC,KAAM,aACN0B,QAAAA,EACAN,OAAQN,EACRqO,QAAAA,EACA7F,MAAO,wBACX,GACA,MACJ,CACAwE,AAXkB,IAAI,CAWZsU,YAAY,CAAG2B,EACzBjW,AAZkB,IAAI,CAYZuU,cAAc,CAAG0B,EAAUpgB,EAAE,CACvC,IAAI,CAAC5D,IAAI,CAAC,CACNC,KAAM,QACN0B,QAASoM,AAfK,IAAI,CAeCpM,OAAO,CAC1BN,OAAQN,EACRqO,QAASrB,AAjBK,IAAI,CAiBCqB,OAAO,AAC9B,GACA,IAAMvN,EAAOmiB,EAAUE,oBAAoB,CAAC,MAAOC,EAAYtiB,EAAKzB,MAAM,CACtEoB,EAAW,EAAG2V,EAAM,CAAEhH,SAAAA,CAAQ,CAAE,CAAG2Q,EAEvC,GAAIzQ,GAAmB8T,EAAW,CAC9B,IAAMvhB,EAAQf,CAAI,CAAC,EAAE,CAACuiB,QAAQ,CAAEC,EAAczhB,EAAMxC,MAAM,CAC1D,IAAK,IAAIyF,EAAImK,EACT,AADsBnK,EAAIwe,IACtBxe,CAAAA,EAAIoK,CAAQ,EADuBpK,IAKnCsR,CAAAA,AAAiB,OAAjBA,AADJA,CAAAA,EAAOvU,CAAK,CAACiD,EAAE,AAAD,EACLye,OAAO,EACZnN,AAAiB,OAAjBA,EAAKmN,OAAO,AAAQ,GACpBlV,EAAQzO,IAAI,CAACwW,EAAKoN,SAAS,CAGnCpU,CAAAA,GACJ,CACA,KAAO3O,EAAW2iB,GAAW,CACzB,GAAI3iB,GAAY2O,GAAY3O,GAAY4O,EAAQ,CAC5C,IAAMoU,EAAe3iB,CAAI,CAACL,EAAS,CAAC4iB,QAAQ,CAAEK,EAAqBD,EAAapkB,MAAM,CAClFgd,EAAc,EAClB,KAAOA,EAAcqH,GAAoB,CACrC,IAAMC,EAAsBtH,EAAcpN,EAAahL,EAAMrD,CAAO,CAAC+iB,EAAoB,CAEzF,GAAI,AAACvN,CAAAA,AAAiB,OAAjBA,AADLA,CAAAA,EAAOqN,CAAY,CAACpH,EAAY,AAAD,EACrBkH,OAAO,EACbnN,AAAiB,OAAjBA,EAAKmN,OAAO,AAAQ,GACnBlH,GAAepN,GACZoN,GAAenN,EAAY,CAC1BtO,CAAO,CAAC+iB,EAAoB,EAC7B/iB,CAAAA,CAAO,CAAC+iB,EAAoB,CAAG,EAAE,AAAD,EAEpC,IAAIjjB,EAAYsM,AAlDd,IAAI,CAkDoBF,aAAa,CAACsJ,EAAKoN,SAAS,EAClD9iB,aAAqBiL,MACrBjL,CAAAA,EAAYA,EAAUkO,OAAO,EAAC,EAElChO,CAAO,CAAC+iB,EAAoB,CAACljB,EAAW2O,EAAS,CAAG1O,EAGpD,IAAIoE,EAAI,EACR,KAAOrE,EAAW2O,GAAYtK,GAC1Bb,AAAiC,KAAK,IAAtCA,CAAG,CAACxD,EAAW2O,EAAWtK,EAAE,EAC5Bb,CAAG,CAACxD,EAAW2O,EAAWtK,EAAE,CAAG,KAC/BA,GAER,CACAuX,GACJ,CACJ,CACA5b,GACJ,CACA,IAAI,CAACG,OAAO,CAAGA,EACf,IAAI,CAACyN,OAAO,CAAGA,EACf,IAAI,CAACpP,IAAI,CAAC,CACNC,KAAM,aACN0B,QAAAA,EACAN,OAAQN,EACRqO,QAAAA,CACJ,EACJ,CAOAC,UAAW,CACP,OAAOoB,EAAyBD,mBAAmB,CAAC,IAAI,CAAC7O,OAAO,CAAE,IAAI,CAACyN,OAAO,CAClF,CACJ,CASAgT,GAAmBnV,cAAc,CAAG,CAChC,GAAGwD,EAAyBxD,cAAc,CAC1CsW,kBAAmB,CAAA,EACnBhB,qBAAsB,CAAA,CAC1B,EACA9R,EAAyBzO,YAAY,CAAC,YAAaogB,IA2BnD,GAAM,CAAEuC,IAAAA,EAAG,CAAE,CAAItlB,IAGX,CAAEG,MAAOolB,EAAwB,CAAE,CAAIvlB,GAW7C,OAAMwlB,WAA2B5Y,EAY7B/I,YAAYvD,CAAO,CAAE,CACjB,IAAMqN,EAAgB4X,GAAyBC,GAAmB5X,cAAc,CAAEtN,GAClF,KAAK,CAACqN,GACN,IAAI,CAACe,SAAS,CAAG,IAlD2CqU,GAkDTpV,GACnD,IAAI,CAACrN,OAAO,CAAGqN,CACnB,CAWA7B,KAAKpK,CAAW,CAAE,CACd,IAOIshB,EAPE7X,EAAY,IAAI,CAAEuD,EAAYvD,EAAUuD,SAAS,CAAEjN,EAAQ0J,EAAU1J,KAAK,CAAE,CAAE0e,aAAAA,CAAY,CAAE1e,MAAOkjB,CAAS,CAAE,CAAGxZ,EAAU7K,OAAO,CAiBxI,GAhBA6K,EAAUxK,IAAI,CAAC,CACXC,KAAM,OACNoB,OAAQN,EACRD,MAAAA,EACAuhB,aAAc7X,EAAU6X,YAAY,AACxC,GAEI,AAAqB,UAArB,OAAO2B,GACPxZ,EAAUsa,OAAO,CAAGd,EACpB3B,EAAesC,GAAII,QAAQ,CAACC,cAAc,CAAChB,IAI3CxZ,EAAUsa,OAAO,CAAGzC,AADpBA,CAAAA,EAAe2B,CAAQ,EACUpgB,EAAE,CAEvC4G,EAAU6X,YAAY,CAAGA,GAAgB,KAAK,EAC1C,CAAC7X,EAAU6X,YAAY,CAAE,CACzB,IAAM9Y,EAAQ,wDAOd,OANAiB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRwI,MAAAA,EACAzI,MAAAA,CACJ,GACOE,QAAQE,MAAM,CAAC,AAAImK,MAAM9B,GACpC,CAKA,OAJAwE,EAAU0B,KAAK,CAACmV,GAAyB,CAAEvC,aAAc7X,EAAU6X,YAAY,AAAC,EAAG7X,EAAU7K,OAAO,EAAGoB,GAEvGD,EAAMsF,aAAa,GACnBtF,EAAM+D,UAAU,CAACkJ,EAAUsB,QAAQ,GAAG/K,UAAU,IACzCkG,EACFe,kBAAkB,CAACiU,GACnBlW,IAAI,CAAC,KACNkB,EAAUxK,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRD,MAAAA,EACAuhB,aAAc7X,EAAU6X,YAAY,AACxC,GACO7X,GAEf,CACJ,CAMAqa,GAAmB5X,cAAc,CAAG,CAChCnM,MAAO,EACX,EACAmL,EAAyBjK,YAAY,CAAC,YAAa6iB,IAyBnD,GAAM,CAAErlB,MAAOylB,EAAmB,CAAE,CAAI5lB,GAUxC,OAAM6lB,WAAsBhjB,EAexBgB,YAAYvD,CAAO,CAAE,GAAGwlB,CAAK,CAAE,CAC3B,KAAK,GACL,IAAI,CAACA,KAAK,CAAGA,EACb,IAAI,CAACxlB,OAAO,CAAGslB,GAAoBC,GAAcjY,cAAc,CAAEtN,GACjE,IAAMylB,EAAe,IAAI,CAACzlB,OAAO,CAACwlB,KAAK,EAAI,EAAE,CAC7C,IAAK,IAAItf,EAAI,EAAGC,EAAOsf,EAAahlB,MAAM,CAAEoL,EAAiBC,EAAe5F,EAAIC,EAAM,EAAED,EAE/E2F,AADLA,CAAAA,EAAkB4Z,CAAY,CAACvf,EAAE,AAAD,EACX5F,IAAI,EAGzBwL,CAAAA,EAAgBvJ,EAAuBH,KAAK,CAACyJ,EAAgBvL,IAAI,CAAC,AAAD,GAE7DklB,EAAMxkB,IAAI,CAAC,IAAI8K,EAAcD,GAGzC,CAgBA6Z,IAAIxlB,CAAQ,CAAEkB,CAAW,CAAE,CACvB,IAAI,CAACf,IAAI,CAAC,CACNC,KAAM,cACNoB,OAAQN,EACRlB,SAAAA,CACJ,GACA,IAAI,CAACslB,KAAK,CAACxkB,IAAI,CAACd,GAChB,IAAI,CAACG,IAAI,CAAC,CACNC,KAAM,cACNoB,OAAQN,EACRlB,SAAAA,CACJ,EACJ,CAOAylB,MAAMvkB,CAAW,CAAE,CACf,IAAI,CAACf,IAAI,CAAC,CACNC,KAAM,aACNoB,OAAQN,CACZ,GACA,IAAI,CAACokB,KAAK,CAAC/kB,MAAM,CAAG,EACpB,IAAI,CAACJ,IAAI,CAAC,CACNC,KAAM,kBACNoB,OAAQN,CACZ,EACJ,CAcA,MAAMF,OAAOC,CAAK,CAAEC,CAAW,CAAE,CAC7B,IAAMwkB,EAAa,IAAI,CAAC5lB,OAAO,CAAC6lB,OAAO,CACnC,IAAI,CAACL,KAAK,CAACphB,KAAK,GAAGyhB,OAAO,GAC1B,IAAI,CAACL,KAAK,CAACphB,KAAK,EAChBjD,CAAAA,EAAMK,QAAQ,GAAKL,GACnBA,CAAAA,EAAMK,QAAQ,CAAGL,EAAMM,KAAK,CAAC,CAAA,EAAOL,EAAW,EAEnD,IAAII,EAAWL,EACf,IAAK,IAAI+E,EAAI,EAAGC,EAAOyf,EAAUnlB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAAG,CACpD,GAAI,CACA,MAAM0f,CAAS,CAAC1f,EAAE,CAAChF,MAAM,CAACM,EAAUJ,EACxC,CACA,MAAOwI,EAAO,CAMV,MALA,IAAI,CAACvJ,IAAI,CAAC,CACNC,KAAM,QACNoB,OAAQN,EACRD,MAAAA,CACJ,GACMyI,CACV,CACApI,EAAWA,EAASA,QAAQ,AAChC,CAEA,OADAL,EAAMK,QAAQ,CAAGA,EACVL,CACX,CAyBAQ,WAAWR,CAAK,CAAES,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,CAAE,CAC5D,IAAMwkB,EAAa,IAAI,CAAC5lB,OAAO,CAAC6lB,OAAO,CACnC,IAAI,CAACL,KAAK,CAACK,OAAO,GAClB,IAAI,CAACL,KAAK,CACd,GAAII,EAAUnlB,MAAM,CAAE,CAClB,IAAIgB,EAAQN,EAAMM,KAAK,GACvB,IAAK,IAAIyE,EAAI,EAAGC,EAAOyf,EAAUnlB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACjD0f,CAAS,CAAC1f,EAAE,CAACvE,UAAU,CAACF,EAAOG,EAAYC,EAAUC,EAAWV,GAChEK,EAAQA,EAAMD,QAAQ,AAE1BL,CAAAA,EAAMK,QAAQ,CAAGC,CACrB,CACA,OAAON,CACX,CAsBAY,cAAcZ,CAAK,CAAEa,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAAE,CACjD,IAAMwkB,EAAa,IAAI,CAAC5lB,OAAO,CAAC6lB,OAAO,CACnC,IAAI,CAACL,KAAK,CAACK,OAAO,GAClB,IAAI,CAACL,KAAK,CAACphB,KAAK,GACpB,GAAIwhB,EAAUnlB,MAAM,CAAE,CAClB,IAAIgB,EAAQN,EAAMM,KAAK,GACvB,IAAK,IAAIyE,EAAI,EAAGC,EAAOyf,EAAUnlB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACjD0f,CAAS,CAAC1f,EAAE,CAACnE,aAAa,CAACN,EAAOO,EAASH,EAAUT,GACrDK,EAAQA,EAAMD,QAAQ,AAE1BL,CAAAA,EAAMK,QAAQ,CAAGC,CACrB,CACA,OAAON,CACX,CAsBAc,WAAWd,CAAK,CAAEe,CAAI,CAAEL,CAAQ,CAAET,CAAW,CAAE,CAC3C,IAAMwkB,EAAa,IAAI,CAAC5lB,OAAO,CAAC6lB,OAAO,CACnC,IAAI,CAACL,KAAK,CAACK,OAAO,GAClB,IAAI,CAACL,KAAK,CAACphB,KAAK,GACpB,GAAIwhB,EAAUnlB,MAAM,CAAE,CAClB,IAAIgB,EAAQN,EAAMM,KAAK,GACvB,IAAK,IAAIyE,EAAI,EAAGC,EAAOyf,EAAUnlB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACjD0f,CAAS,CAAC1f,EAAE,CAACjE,UAAU,CAACR,EAAOS,EAAML,EAAUT,GAC/CK,EAAQA,EAAMD,QAAQ,AAE1BL,CAAAA,EAAMK,QAAQ,CAAGC,CACrB,CACA,OAAON,CACX,CAkBAf,YAAYe,CAAK,CAAEC,CAAW,CAAE,CAE5BokB,AADc,IAAI,CACZnlB,IAAI,CAAC,CACPC,KAAM,SACNoB,OAAQN,EACRD,MAAAA,CACJ,GACA,IAAMykB,EAAaJ,AANL,IAAI,CAMOxlB,OAAO,CAAC6lB,OAAO,CACpCL,AAPU,IAAI,CAORA,KAAK,CAACK,OAAO,GACnBL,AARU,IAAI,CAQRA,KAAK,CAACphB,KAAK,GACjB5C,EAAWL,EAAMK,QAAQ,CAC7B,IAAK,IAAI0E,EAAI,EAAGC,EAAOyf,EAAUnlB,MAAM,CAAYyF,EAAIC,EAAM,EAAED,EAE3D1E,EAAWtB,AADA0lB,CAAS,CAAC1f,EAAE,CACH9F,WAAW,CAACoB,EAAUJ,GAAaI,QAAQ,CAQnE,OANAL,EAAMK,QAAQ,CAAGA,EACjBgkB,AAfc,IAAI,CAeZnlB,IAAI,CAAC,CACPC,KAAM,cACNoB,OAAQN,EACRD,MAAAA,CACJ,GACOA,CACX,CAUA2kB,OAAO5lB,CAAQ,CAAEkB,CAAW,CAAE,CAC1B,IAAMwkB,EAAY,IAAI,CAACJ,KAAK,CAC5B,IAAI,CAACnlB,IAAI,CAAC,CACNC,KAAM,iBACNoB,OAAQN,EACRlB,SAAAA,CACJ,GACA0lB,EAAU/iB,MAAM,CAAC+iB,EAAUpd,OAAO,CAACtI,GAAW,GAC9C,IAAI,CAACG,IAAI,CAAC,CACNC,KAAM,sBACNoB,OAAQN,EACRlB,SAAAA,CACJ,EACJ,CACJ,CASAqlB,GAAcjY,cAAc,CAAG,CAC3BhN,KAAM,OACV,EACAiC,EAAuBF,YAAY,CAAC,QAASkjB,IAyB7C,GAAM,CAAE1lB,MAAOkmB,EAAoB,CAAE,CAAIrmB,GAWzC,OAAMsmB,WAAuBzjB,EAYzBgB,YAAYvD,CAAO,CAAE,CACjB,KAAK,GACL,IAAI,CAACA,OAAO,CAAG+lB,GAAqBC,GAAe1Y,cAAc,CAAEtN,EACvE,CA4BA2B,WAAWR,CAAK,CAAES,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,CAAE,CAC5D,IAAMI,EAAWL,EAAMK,QAAQ,CAAEykB,EAAmBzkB,EAAS8G,aAAa,CAAC,cAAe1G,GAO1F,OANI,AAA4B,KAAA,IAArBqkB,EACPzkB,EAAS0D,UAAU,CAAC,IAAI,CAAC9E,WAAW,CAACe,EAAMM,KAAK,IAAIkD,UAAU,GAAI,KAAK,EAAGvD,GAG1EI,EAAS4H,OAAO,CAAC,CAAC,EAAEvH,EAAS,CAAC,CAAEokB,EAAkBnkB,EAAWV,GAE1DD,CACX,CAoBAY,cAAcZ,CAAK,CAAEa,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAAE,CACjD,IAAMI,EAAWL,EAAMK,QAAQ,CAAE0kB,EAAuB1kB,EAASiD,SAAS,CAAC,gBAAkB,EAAE,CAC3FG,EAAczD,EAAM0G,cAAc,GAAIse,EAAShlB,EAAMkH,WAAW,KAAO6d,EAAoBzlB,MAAM,CACrG,GAAI,CAAC0lB,EACD,CAAA,IAAK,IAAIjgB,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACnD,GAAItB,CAAW,CAACsB,EAAE,GAAKggB,CAAmB,CAAChgB,EAAE,CAAE,CAC3CigB,EAAQ,CAAA,EACR,KACJ,CACJ,CAEJ,GAAIA,EACA,OAAO,IAAI,CAAC/lB,WAAW,CAACe,EAAOC,GAEnCwD,EAAc5F,OAAO6F,IAAI,CAAC7C,GAC1B,IAAK,IAAIkE,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEgC,EAAQb,EAAYqkB,EAAkB/f,EAAIC,EAAM,EAAED,EAAG,CAE5FzD,EAAST,CAAO,CADhBJ,EAAagD,CAAW,CAACsB,EAAE,CACC,CAC5B+f,EAAoBzkB,EAAS8G,aAAa,CAAC,cAAe1G,IACtDJ,EAAS6G,WAAW,GACxB,IAAK,IAAIrB,EAAI,EAAGof,EAAKvkB,EAAUoF,EAAOxE,EAAOhC,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EAAG,EAAEof,EAClE5kB,EAAS4H,OAAO,CAAC,CAAC,EAAEgd,EAAG,CAAC,CAAEH,EAAkBxjB,CAAM,CAACuE,EAAE,CAAE5F,EAE/D,CACA,OAAOD,CACX,CAoBAc,WAAWd,CAAK,CAAEe,CAAI,CAAEL,CAAQ,CAAET,CAAW,CAAE,CAC3C,IAAMwD,EAAczD,EAAM0G,cAAc,GAAIrG,EAAWL,EAAMK,QAAQ,CAAE0kB,EAAuB1kB,EAASiD,SAAS,CAAC,gBAAkB,EAAE,CACjI0hB,EAAShlB,EAAMkH,WAAW,KAAO6d,EAAoBzlB,MAAM,CAC/D,GAAI,CAAC0lB,EACD,CAAA,IAAK,IAAIjgB,EAAI,EAAGC,EAAOvB,EAAYnE,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACnD,GAAItB,CAAW,CAACsB,EAAE,GAAKggB,CAAmB,CAAChgB,EAAE,CAAE,CAC3CigB,EAAQ,CAAA,EACR,KACJ,CACJ,CAEJ,GAAIA,EACA,OAAO,IAAI,CAAC/lB,WAAW,CAACe,EAAOC,GAEnC,IAAK,IAAI8E,EAAI,EAAGyC,EAAK9G,EAAUsE,EAAOjE,EAAKzB,MAAM,CAAE4E,EAAKa,EAAIC,EAAM,EAAED,EAAG,EAAEyC,EAErE,GAAItD,AADJA,CAAAA,EAAMnD,CAAI,CAACgE,EAAE,AAAD,YACOvD,MACfnB,EAASyD,SAAS,CAAC,CAAC,EAAE0D,EAAG,CAAC,CAAEtD,QAG5B,IAAK,IAAI2B,EAAI,EAAGC,EAAOrC,EAAYnE,MAAM,CAAEuG,EAAIC,EAAM,EAAED,EACnDxF,EAAS4H,OAAO,CAAC,CAAC,EAAET,EAAG,CAAC,CAAE3B,EAAG3B,CAAG,CAACT,CAAW,CAACoC,EAAE,CAAC,CAAE5F,GAI9D,OAAOD,CACX,CAaAf,YAAYe,CAAK,CAAEC,CAAW,CAAE,CAE5BlB,AADiB,IAAI,CACZG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAAA,CAAM,GAC3D,IAAMK,EAAWL,EAAMK,QAAQ,CAC/B,GAAIL,EAAM2H,UAAU,CAAC,CAAC,cAAc,EAAG,CACnC,IAAMud,EAAqB,AAACllB,CAAAA,EAAMsF,aAAa,CAAC,CAAC,cAAc,GAAK,CAAC,CAAA,EAChE7B,WAAW,EAAI,EAAE,CAAG5C,EAAU,CAAC,EAAG4C,EAAc,EAAE,CACvD,IAAK,IAAIsB,EAAI,EAAGC,EAAOkgB,EAAkB5lB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EACzDtB,EAAY5D,IAAI,CAAC,GAAKqlB,CAAiB,CAACngB,EAAE,EAE9C,IAAK,IAAIA,EAAI,EAAGC,EAAOhF,EAAMkH,WAAW,GAAIhD,EAAKa,EAAIC,EAAM,EAAED,EACzDb,CAAAA,EAAMlE,EAAM4D,MAAM,CAACmB,EAAC,GAEhBlE,CAAAA,CAAO,CAAC4C,CAAW,CAACsB,EAAE,CAAC,CAAGb,CAAE,EAGpC7D,EAASiF,aAAa,GACtBjF,EAAS0D,UAAU,CAAClD,EACxB,KACK,CACD,IAAMA,EAAU,CAAC,EACjB,IAAK,IAAIkE,EAAI,EAAGC,EAAOhF,EAAMkH,WAAW,GAAIhD,EAAKa,EAAIC,EAAM,EAAED,EACzDb,CAAAA,EAAMlE,EAAM4D,MAAM,CAACmB,EAAC,GAEhBlE,CAAAA,CAAO,CAAC,CAAC,EAAEkE,EAAE,CAAC,CAAC,CAAGb,CAAE,CAG5BrD,CAAAA,EAAQ4C,WAAW,CAAGzD,EAAM0G,cAAc,GAC1CrG,EAASiF,aAAa,GACtBjF,EAAS0D,UAAU,CAAClD,EACxB,CAEA,OADA9B,AA9BiB,IAAI,CA8BZG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAAA,CAAM,GACzDA,CACX,CACJ,CASA6kB,GAAe1Y,cAAc,CAAG,CAC5BhN,KAAM,QACV,EACAiC,EAAuBF,YAAY,CAAC,SAAU2jB,GAqC9C,OAAMM,WAAqB/jB,EAMvBgB,YAAYvD,CAAO,CAAE,CACjB,KAAK,GACL,IAAI,CAACA,OAAO,CAAG,CACX,GAAGsmB,GAAahZ,cAAc,CAC9B,GAAGtN,CAAO,AACd,CACJ,CAMAI,YAAYe,CAAK,CAAEC,CAAW,CAAE,CAE5BlB,AADiB,IAAI,CACZG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAAA,CAAM,GAC3D,IAAMuU,EAAwBxV,AAFb,IAAI,CAEkBF,OAAO,CAAC0V,qBAAqB,CAAE6Q,EAAkBrmB,AAFvE,IAAI,CAE4EF,OAAO,CAACumB,cAAc,EACnHplB,EAAM0G,cAAc,GAAKrG,EAAWL,EAAMK,QAAQ,CACtD,IAAK,IAAI0E,EAAI,EAAGC,EAAOogB,EAAe9lB,MAAM,CAAEmB,EAAYsE,EAAIC,EAAM,EAAED,EAClEtE,EAAa2kB,CAAc,CAACrgB,EAAE,CAC1BqgB,EAAe/d,OAAO,CAAC5G,IAAe,GACtCJ,EAASyD,SAAS,CAACrD,EAAY1B,AAPtB,IAAI,CAO2BsmB,aAAa,CAACrlB,EAAOS,IAGrE,IAAM6kB,EAAkBvmB,AAVP,IAAI,CAUYF,OAAO,CAACymB,cAAc,EAAI,EAAE,CAC7D,IAAK,IAAIvgB,EAAI,EAAGC,EAAOsgB,EAAehmB,MAAM,CAAEimB,EAAexQ,EAAShQ,EAAIC,EAAM,EAAED,EAC9EwgB,EAAgBD,CAAc,CAACvgB,EAAE,CACjCgQ,EAAUkB,GAAsBjB,YAAY,CAACuQ,EAAcxQ,OAAO,CAAER,GACpElU,EAASyD,SAAS,CAACyhB,EAAcjkB,MAAM,CAAEvC,AAd5B,IAAI,CAciCymB,oBAAoB,CAACzQ,EAAS/U,EAAOulB,EAAcE,QAAQ,CAAEF,EAActT,MAAM,GAGvI,OADAlT,AAhBiB,IAAI,CAgBZG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAAA,CAAM,GACzDA,CACX,CAkBAqlB,cAAcrlB,CAAK,CAAES,CAAU,CAAEC,EAAW,CAAC,CAAE,CAC3C,IAAM6T,EAAwB,IAAI,CAAC1V,OAAO,CAAC0V,qBAAqB,CAAEjT,EAAS,AAACtB,CAAAA,EAAMsD,SAAS,CAAC7C,EAAY,CAAA,IAAS,EAAE,AAAD,EAC7GwC,KAAK,CAACvC,EAAW,EAAIA,EAAW,GACrC,IAAK,IAAIqE,EAAI,EAAGC,EAAO1D,EAAOhC,MAAM,CAAEomB,EAAe,EAAE,CAAoBpN,EAAMvT,EAAIC,EAAM,EAAED,EAEzF,GAAI,AAAgB,UAAhB,MADJuT,CAAAA,EAAOhX,CAAM,CAACyD,EAAE,AAAD,GAEXuT,AAAY,MAAZA,CAAI,CAAC,EAAE,CACP,GAAI,CAEAoN,EAAgBC,AAN2C,KAM3BrN,EAC5BoN,EACAzP,GAAsBjB,YAAY,CAACsD,EAAKnE,SAAS,CAAC,GAAII,GAE1DjT,CAAM,CAACyD,EAAE,CACL6T,GAAyBR,cAAc,CAACsN,EAAc1lB,EAC9D,CACA,KAAM,CACFsB,CAAM,CAACyD,EAAE,CAAG+G,GAChB,CAGR,OAAOxK,CACX,CAsBAkkB,qBAAqBzQ,CAAO,CAAE/U,CAAK,CAAEylB,EAAW,CAAC,CAAExT,EAASjS,EAAMkH,WAAW,EAAE,CAAE,CAC7Eue,EAAWA,GAAY,EAAIA,EAAW,EACtCxT,EAASA,GAAU,EAAIA,EAASjS,EAAMkH,WAAW,GAAK+K,EACtD,IAAM3Q,EAAS,EAAE,CAAEjB,EAAWL,EAAMK,QAAQ,CAC5C,IAAK,IAAI0E,EAAI,EAAGC,EAAQiN,EAASwT,EAAW1gB,EAAIC,EAAM,EAAED,EACpD,GAAI,CACAzD,CAAM,CAACyD,EAAE,CAAG6T,GAAyBR,cAAc,CAACrD,EAAS1U,EACjE,CACA,KAAM,CACFiB,CAAM,CAACyD,EAAE,CAAG+G,GAChB,QACQ,CACJiJ,EAAU6D,GAAyBI,mBAAmB,CAACjE,EAAS,EAAG,EACvE,CAEJ,OAAOzT,CACX,CACJ,CAUA6jB,GAAahZ,cAAc,CAAG,CAC1BhN,KAAM,OACNoV,sBAAuB,CAAA,CAC3B,EACAnT,EAAuBF,YAAY,CAAC,OAAQikB,IAyB5C,GAAM,CAAEzmB,MAAOknB,EAAmB,CAAE,CAAIrnB,GAUxC,OAAMsnB,WAAsBzkB,EAYxBgB,YAAYvD,CAAO,CAAE,CACjB,KAAK,GACL,IAAI,CAACA,OAAO,CAAG+mB,GAAoBC,GAAc1Z,cAAc,CAAEtN,EACrE,CAkBAI,YAAYe,CAAK,CAAEC,CAAW,CAAE,CAE5BlB,AADiB,IAAI,CACZG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAAA,CAAM,GAC3D,IAAI8lB,EAAU,EAAE,CACV,CAAEC,SAAAA,CAAQ,CAAEC,OAAAA,CAAM,CAAEC,OAAAA,CAAM,CAAE,CAAGlnB,AAHpB,IAAI,CAGyBF,OAAO,CACrD,GAAImnB,EAAO1mB,MAAM,CAAE,CACf,IAAMe,EAAWL,EAAMK,QAAQ,CAC3BQ,EAAUb,EAAMwD,UAAU,GAAIzC,EAAO,EAAE,CAC3C,IAAK,IAAIgE,EAAI,EAAGC,EAAOghB,EAAO1mB,MAAM,CAAE6R,EAAO+U,EAAanhB,EAAIC,EAAM,EAAED,EAElE,GADAoM,EAAQ6U,CAAM,CAACjhB,EAAE,CACbkhB,CAAAA,GACA,OAAO9U,EAAMgV,QAAQ,EAAK,OAAOhV,EAAMiV,QAAQ,EAG/CrhB,EAAI,GAAK,CAACghB,IACV1lB,EAASgD,UAAU,GACnBhD,EAASyI,OAAO,CAAC/H,GACjBV,EAASqI,qBAAqB,CAACod,EAAS,CAAA,GACxCjlB,EAAUR,EAASmD,UAAU,GAC7BzC,EAAO,EAAE,CACT+kB,EAAU,EAAE,EAEhBI,EAAerlB,CAAO,CAACsQ,EAAM7P,MAAM,CAAC,EAAI,EAAE,CAC1C,IAAK,IAAIuE,EAAI,EAAGC,EAAOogB,EAAY5mB,MAAM,CAAEgZ,EAAMpU,EAAK4C,EAAkBjB,EAAIC,EAAM,EAAED,EAAG,CAEnF,OAAQ,MADRyS,CAAAA,EAAO4N,CAAW,CAACrgB,EAAE,AAAD,GAEhB,QACI,QACJ,KAAK,UACL,IAAK,SACL,IAAK,SAET,CACIogB,CAAAA,CAAAA,GACA,OAAO3N,GAAS,OAAOnH,EAAMgV,QAAQ,AAAD,GAGpC7N,GAAQnH,EAAMgV,QAAQ,EACtB7N,GAAQnH,EAAMiV,QAAQ,GAClBL,GACA7hB,EAAMlE,EAAM4D,MAAM,CAACiC,GACnBiB,EAAmB9G,EAAMgH,mBAAmB,CAACnB,KAG7C3B,EAAM7D,EAASuD,MAAM,CAACiC,GACtBiB,EAAmBzG,EAAS2G,mBAAmB,CAACnB,IAEhD3B,IACAnD,EAAKlB,IAAI,CAACqE,GACV4hB,EAAQjmB,IAAI,CAACiH,IAGzB,EAEJzG,EAASgD,UAAU,GACnBhD,EAASyI,OAAO,CAAC/H,GACjBV,EAASqI,qBAAqB,CAACod,EACnC,CAEA,OADA/mB,AAzDiB,IAAI,CAyDZG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAAA,CAAM,GACzDA,CACX,CACJ,CASA6lB,GAAc1Z,cAAc,CAAG,CAC3BhN,KAAM,QACN6mB,OAAQ,EAAE,AACd,EACA5kB,EAAuBF,YAAY,CAAC,QAAS2kB,IA0B7C,GAAM,CAAEnnB,MAAO2nB,EAAkB,CAAE,CAAI9nB,GAUvC,OAAM+nB,WAAqBllB,EAMvB,OAAOmlB,UAAU/oB,CAAC,CAAEyM,CAAC,CAAE,CACnB,MAAQ,AAACzM,CAAAA,GAAK,CAAA,EAAMyM,CAAAA,GAAK,CAAA,EAAK,GAC1B,CAAA,CAAA,AAACzM,CAAAA,GAAK,CAAA,EAAMyM,CAAAA,GAAK,CAAA,CAAC,CAE1B,CACA,OAAOuc,WAAWhpB,CAAC,CAAEyM,CAAC,CAAE,CACpB,MAAQ,AAACA,CAAAA,GAAK,CAAA,EAAMzM,CAAAA,GAAK,CAAA,EAAK,GAC1B,CAAA,CAAA,AAACyM,CAAAA,GAAK,CAAA,EAAMzM,CAAAA,GAAK,CAAA,CAAC,CAE1B,CAYA4E,YAAYvD,CAAO,CAAE,CACjB,KAAK,GACL,IAAI,CAACA,OAAO,CAAGwnB,GAAmBC,GAAana,cAAc,CAAEtN,EACnE,CAiBA4nB,iBAAiBzmB,CAAK,CAAE,CACpB,IAAMe,EAAOf,EAAMiH,OAAO,GAAIyf,EAAgB,EAAE,CAChD,IAAK,IAAI3hB,EAAI,EAAGC,EAAOjE,EAAKzB,MAAM,CAAEyF,EAAIC,EAAM,EAAED,EAC5C2hB,EAAc7mB,IAAI,CAAC,CACfqK,MAAOnF,EACPb,IAAKnD,CAAI,CAACgE,EAAE,AAChB,GAEJ,OAAO2hB,CACX,CAuBAlmB,WAAWR,CAAK,CAAES,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,CAAE,CAC5D,GAAuB,CAAE0mB,cAAAA,CAAa,CAAEC,cAAAA,CAAa,CAAE,CAAG7nB,AAAzC,IAAI,CAA8CF,OAAO,CAgB1E,OAfI4B,IAAekmB,IACXC,GACA5mB,EAAMK,QAAQ,CAAC4H,OAAO,CAACxH,EAAYC,EAAUC,GAC7CX,EAAMK,QAAQ,CAACyD,SAAS,CAAC8iB,EAAe7nB,AAJ/B,IAAI,CAKRE,WAAW,CAAC,IAjnNoB2F,EAinND,CAChC/D,QAASb,EACJwD,UAAU,CAAC,CAACmjB,EAAeC,EAAc,CAClD,IACKvmB,QAAQ,CACRiD,SAAS,CAACsjB,KAGf7nB,AAbS,IAAI,CAaJE,WAAW,CAACe,EAAOC,IAG7BD,CACX,CAoBAY,cAAcZ,CAAK,CAAEa,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAAE,CACjD,GAAuB,CAAE0mB,cAAAA,CAAa,CAAEC,cAAAA,CAAa,CAAE,CAAG7nB,AAAzC,IAAI,CAA8CF,OAAO,CAAE4E,EAAc5F,OAAO6F,IAAI,CAAC7C,GAiBtG,OAhBI4C,EAAY4D,OAAO,CAACsf,GAAiB,KACjCC,GACA/lB,CAAO,CAAC4C,CAAW,CAAC,EAAE,CAAC,CAACnE,MAAM,EAC9BU,EAAMK,QAAQ,CAAC0D,UAAU,CAAClD,EAASH,GACnCV,EAAMK,QAAQ,CAACyD,SAAS,CAAC8iB,EAAe7nB,AAL/B,IAAI,CAMRE,WAAW,CAAC,IAxpNoB2F,EAwpND,CAChC/D,QAASb,EACJwD,UAAU,CAAC,CAACmjB,EAAeC,EAAc,CAClD,IACKvmB,QAAQ,CACRiD,SAAS,CAACsjB,KAGf7nB,AAdS,IAAI,CAcJE,WAAW,CAACe,EAAOC,IAG7BD,CACX,CAoBAc,WAAWd,CAAK,CAAEe,CAAI,CAAEL,CAAQ,CAAET,CAAW,CAAE,CAC3C,GAAuB,CAAE0mB,cAAAA,CAAa,CAAEC,cAAAA,CAAa,CAAE,CAAG7nB,AAAzC,IAAI,CAA8CF,OAAO,CAe1E,OAdI+nB,GACA7lB,EAAKzB,MAAM,EACXU,EAAMK,QAAQ,CAACyI,OAAO,CAAC/H,EAAML,GAC7BV,EAAMK,QAAQ,CAACyD,SAAS,CAAC8iB,EAAe7nB,AAJ3B,IAAI,CAKZE,WAAW,CAAC,IA9rNwB2F,EA8rNL,CAChC/D,QAASb,EACJwD,UAAU,CAAC,CAACmjB,EAAeC,EAAc,CAClD,IACKvmB,QAAQ,CACRiD,SAAS,CAACsjB,KAGf7nB,AAba,IAAI,CAaRE,WAAW,CAACe,EAAOC,GAEzBD,CACX,CAaAf,YAAYe,CAAK,CAAEC,CAAW,CAAE,CAE5BlB,AADiB,IAAI,CACZG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAAA,CAAM,GAC3D,IAAMyD,EAAczD,EAAM0G,cAAc,GAAI3D,EAAW/C,EAAMkH,WAAW,GAAIwf,EAAgB,IAAI,CAACD,gBAAgB,CAACzmB,GAAQ,CAAE6mB,UAAAA,CAAS,CAAEF,cAAAA,CAAa,CAAEC,cAAAA,CAAa,CAAE,CAAG7nB,AAFvJ,IAAI,CAE4JF,OAAO,CAAEioB,EAAWD,AAAc,QAAdA,EACjMP,GAAaC,SAAS,CACtBD,GAAaE,UAAU,CAAGO,EAAqBtjB,EAAY4D,OAAO,CAACsf,GAAgBtmB,EAAWL,EAAMK,QAAQ,CAIhH,GAH2B,KAAvB0mB,GACAL,EAAc1c,IAAI,CAAC,CAACxM,EAAGyM,IAAM6c,EAAQtpB,EAAE0G,GAAG,CAAC6iB,EAAmB,CAAE9c,EAAE/F,GAAG,CAAC6iB,EAAmB,GAEzFH,EAAe,CACf,IAAMtlB,EAAS,EAAE,CACjB,IAAK,IAAIyD,EAAI,EAAGA,EAAIhC,EAAU,EAAEgC,EAC5BzD,CAAM,CAAColB,CAAa,CAAC3hB,EAAE,CAACmF,KAAK,CAAC,CAAGnF,EAErC1E,EAAS0D,UAAU,CAAC,CAAE,CAAC6iB,EAAc,CAAEtlB,CAAO,EAClD,KACK,CACD,IAEI0lB,EAFEC,EAAkB,EAAE,CACpBlmB,EAAO,EAAE,CAEf,IAAK,IAAIgE,EAAI,EAAGA,EAAIhC,EAAU,EAAEgC,EAC5BiiB,EAAeN,CAAa,CAAC3hB,EAAE,CAC/BkiB,EAAgBpnB,IAAI,CAACQ,EAAS2G,mBAAmB,CAACggB,EAAa9c,KAAK,GACpEnJ,EAAKlB,IAAI,CAACmnB,EAAa9iB,GAAG,EAE9B7D,EAASyI,OAAO,CAAC/H,EAAM,GACvBV,EAASqI,qBAAqB,CAACue,EACnC,CAEA,OADAloB,AA3BiB,IAAI,CA2BZG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAAA,CAAM,GACzDA,CACX,CACJ,CASAsmB,GAAana,cAAc,CAAG,CAC1BhN,KAAM,OACN0nB,UAAW,OACXF,cAAe,GACnB,EACAvlB,EAAuBF,YAAY,CAAC,OAAQolB,IA4B5C,IAAMY,GAAK3oB,GACX2oB,CAAAA,GAAElqB,aAAa,CAAGkqB,GAAElqB,aAAa,EAAImO,EACrC+b,GAAEjqB,aAAa,CAAGiqB,GAAEjqB,aAAa,EAAI0S,EACrCuX,GAAEtX,UAAU,CAAGsX,GAAEtX,UAAU,EAAIuC,EAC/B+U,GAAEpqB,YAAY,CAAGoqB,GAAEpqB,YAAY,EAAIsE,EACnC8lB,GAAE5U,QAAQ,CAAG4U,GAAE5U,QAAQ,EA9uK6BA,EA+uKpD4U,GAAEtiB,SAAS,CAAGsiB,GAAEtiB,SAAS,EAryN4BA,EAsyNrDsiB,GAAExL,OAAO,CAAGwL,GAAExL,OAAO,EAl1FiCA,GAm1FzB,IAAMyL,GAAmB5oB,WAE7C4oB,MAAkBC,OAAO"}