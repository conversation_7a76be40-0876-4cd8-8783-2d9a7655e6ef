{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highmaps JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/map\n * @requires highcharts\n *\n * Highmaps as a plugin for Highcharts or Highcharts Stock.\n *\n * (c) 2011-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__coloraxis_src_js_cdd22a72__ from \"./coloraxis.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"./coloraxis.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const external_coloraxis_src_js_namespaceObject = x({  });\n;// ./code/es-modules/Maps/MapNavigationDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst lang = {\n    zoomIn: 'Zoom in',\n    zoomOut: 'Zoom out'\n};\n/**\n * The `mapNavigation` option handles buttons for navigation in addition to\n * `mousewheel` and `doubleclick` handlers for map zooming.\n *\n * @product      highmaps\n * @optionparent mapNavigation\n */\nconst mapNavigation = {\n    /**\n     * General options for the map navigation buttons. Individual options\n     * can be given from the [mapNavigation.buttons](#mapNavigation.buttons)\n     * option set.\n     *\n     * @sample {highmaps} maps/mapnavigation/button-theme/\n     *         Theming the navigation buttons\n     */\n    buttonOptions: {\n        /**\n         * What box to align the buttons to. Possible values are `plotBox`\n         * and `spacingBox`.\n         *\n         * @type {Highcharts.ButtonRelativeToValue}\n         */\n        alignTo: 'plotBox',\n        /**\n         * The alignment of the navigation buttons.\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'left',\n        /**\n         * The vertical alignment of the buttons. Individual alignment can\n         * be adjusted by each button's `y` offset.\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'top',\n        /**\n         * The X offset of the buttons relative to its `align` setting.\n         */\n        x: 0,\n        /**\n         * The width of the map navigation buttons.\n         */\n        width: 18,\n        /**\n         * The pixel height of the map navigation buttons.\n         */\n        height: 18,\n        /**\n         * Padding for the navigation buttons.\n         *\n         * @since 5.0.0\n         */\n        padding: 5,\n        /**\n         * Text styles for the map navigation buttons.\n         *\n         * @type    {Highcharts.CSSObject}\n         * @default {\"fontSize\": \"1em\", \"fontWeight\": \"bold\"}\n         */\n        style: {\n            /** @ignore */\n            color: \"#666666\" /* Palette.neutralColor60 */,\n            /** @ignore */\n            fontSize: '1em',\n            /** @ignore */\n            fontWeight: 'bold'\n        },\n        /**\n         * A configuration object for the button theme. The object accepts\n         * SVG properties like `stroke-width`, `stroke` and `fill`. Tri-state\n         * button styles are supported by the `states.hover` and `states.select`\n         * objects.\n         *\n         * @sample {highmaps} maps/mapnavigation/button-theme/\n         *         Themed navigation buttons\n         *\n         * @type    {Highcharts.SVGAttributes}\n         * @default {\"stroke-width\": 1, \"text-align\": \"center\"}\n         */\n        theme: {\n            /** @ignore */\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            /** @ignore */\n            stroke: \"#e6e6e6\" /* Palette.neutralColor10 */,\n            /** @ignore */\n            'stroke-width': 1,\n            /** @ignore */\n            'text-align': 'center'\n        }\n    },\n    /**\n     * The individual buttons for the map navigation. This usually includes\n     * the zoom in and zoom out buttons. Properties for each button is\n     * inherited from\n     * [mapNavigation.buttonOptions](#mapNavigation.buttonOptions), while\n     * individual options can be overridden. But default, the `onclick`, `text`\n     * and `y` options are individual.\n     */\n    buttons: {\n        /**\n         * Options for the zoom in button. Properties for the zoom in and zoom\n         * out buttons are inherited from\n         * [mapNavigation.buttonOptions](#mapNavigation.buttonOptions), while\n         * individual options can be overridden. By default, the `onclick`,\n         * `text` and `y` options are individual.\n         *\n         * @extends mapNavigation.buttonOptions\n         */\n        zoomIn: {\n            // eslint-disable-next-line valid-jsdoc\n            /**\n             * Click handler for the button.\n             *\n             * @type    {Function}\n             * @default function () { this.mapZoom(0.5); }\n             */\n            onclick: function () {\n                this.mapZoom(0.5);\n            },\n            /**\n             * The text for the button. The tooltip (title) is a language option\n             * given by [lang.zoomIn](#lang.zoomIn).\n             */\n            text: '+',\n            /**\n             * The position of the zoomIn button relative to the vertical\n             * alignment.\n             */\n            y: 0\n        },\n        /**\n         * Options for the zoom out button. Properties for the zoom in and\n         * zoom out buttons are inherited from\n         * [mapNavigation.buttonOptions](#mapNavigation.buttonOptions), while\n         * individual options can be overridden. By default, the `onclick`,\n         * `text` and `y` options are individual.\n         *\n         * @extends mapNavigation.buttonOptions\n         */\n        zoomOut: {\n            // eslint-disable-next-line valid-jsdoc\n            /**\n             * Click handler for the button.\n             *\n             * @type    {Function}\n             * @default function () { this.mapZoom(2); }\n             */\n            onclick: function () {\n                this.mapZoom(2);\n            },\n            /**\n             * The text for the button. The tooltip (title) is a language option\n             * given by [lang.zoomOut](#lang.zoomIn).\n             */\n            text: '-',\n            /**\n             * The position of the zoomOut button relative to the vertical\n             * alignment.\n             */\n            y: 28\n        }\n    },\n    /**\n     * Whether to enable navigation buttons. By default it inherits the\n     * [enabled](#mapNavigation.enabled) setting.\n     *\n     * @type      {boolean}\n     * @apioption mapNavigation.enableButtons\n     */\n    /**\n     * Whether to enable map navigation. The default is not to enable\n     * navigation, as many choropleth maps are simple and don't need it.\n     * Additionally, when touch zoom and mouse wheel zoom is enabled, it breaks\n     * the default behaviour of these interactions in the website, and the\n     * implementer should be aware of this.\n     *\n     * Individual interactions can be enabled separately, namely buttons,\n     * multitouch zoom, double click zoom, double click zoom to element and\n     * mouse wheel zoom.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @apioption mapNavigation.enabled\n     */\n    /**\n     * Enables zooming in on an area on double clicking in the map. By default\n     * it inherits the [enabled](#mapNavigation.enabled) setting.\n     *\n     * @type      {boolean}\n     * @apioption mapNavigation.enableDoubleClickZoom\n     */\n    /**\n     * Whether to zoom in on an area when that area is double clicked.\n     *\n     * @sample {highmaps} maps/mapnavigation/doubleclickzoomto/\n     *         Enable double click zoom to\n     *\n     * @type      {boolean}\n     * @default   false\n     * @apioption mapNavigation.enableDoubleClickZoomTo\n     */\n    /**\n     * Enables zooming by mouse wheel. By default it inherits the [enabled](\n     * #mapNavigation.enabled) setting.\n     *\n     * @type      {boolean}\n     * @apioption mapNavigation.enableMouseWheelZoom\n     */\n    /**\n     * Whether to enable multitouch zooming. Note that if the chart covers the\n     * viewport, this prevents the user from using multitouch and touchdrag on\n     * the web page, so you should make sure the user is not trapped inside the\n     * chart. By default it inherits the [enabled](#mapNavigation.enabled)\n     * setting.\n     *\n     * @type      {boolean}\n     * @apioption mapNavigation.enableTouchZoom\n     */\n    /**\n     * Sensitivity of mouse wheel or trackpad scrolling. 1 is no sensitivity,\n     * while with 2, one mouse wheel delta will zoom in 50%.\n     *\n     * @since 4.2.4\n     */\n    mouseWheelSensitivity: 1.1\n    // Enabled: false,\n    // enableButtons: null, // inherit from enabled\n    // enableTouchZoom: null, // inherit from enabled\n    // enableDoubleClickZoom: null, // inherit from enabled\n    // enableDoubleClickZoomTo: false\n    // enableMouseWheelZoom: null, // inherit from enabled\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst mapNavigationDefaults = {\n    lang,\n    mapNavigation\n};\n/* harmony default export */ const MapNavigationDefaults = (mapNavigationDefaults);\n\n;// ./code/es-modules/Maps/MapPointer.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined, extend, pick, wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar MapPointer;\n(function (MapPointer) {\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let totalWheelDelta = 0;\n    let totalWheelDeltaTimer;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extend the Pointer.\n     * @private\n     */\n    function compose(PointerClass) {\n        const pointerProto = PointerClass.prototype;\n        if (!pointerProto.onContainerDblClick) {\n            extend(pointerProto, {\n                onContainerDblClick,\n                onContainerMouseWheel\n            });\n            wrap(pointerProto, 'normalize', wrapNormalize);\n            wrap(pointerProto, 'zoomOption', wrapZoomOption);\n        }\n    }\n    MapPointer.compose = compose;\n    /**\n     * The event handler for the doubleclick event.\n     * @private\n     */\n    function onContainerDblClick(e) {\n        const chart = this.chart;\n        e = this.normalize(e);\n        if (chart.options.mapNavigation.enableDoubleClickZoomTo) {\n            if (chart.pointer.inClass(e.target, 'highcharts-tracker') &&\n                chart.hoverPoint) {\n                chart.hoverPoint.zoomTo();\n            }\n        }\n        else if (chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop)) {\n            chart.mapZoom(0.5, void 0, void 0, e.chartX, e.chartY);\n        }\n    }\n    /**\n     * The event handler for the mouse scroll event.\n     * @private\n     */\n    function onContainerMouseWheel(e) {\n        const chart = this.chart;\n        e = this.normalize(e);\n        // Firefox uses e.deltaY or e.detail, WebKit and IE uses wheelDelta\n        // try wheelDelta first #15656\n        const delta = (defined(e.wheelDelta) && -e.wheelDelta / 120) ||\n            e.deltaY || e.detail;\n        // Wheel zooming on trackpads have different behaviours in Firefox vs\n        // WebKit. In Firefox the delta increments in steps by 1, so it is not\n        // distinguishable from true mouse wheel. Therefore we use this timer\n        // to avoid trackpad zooming going too fast and out of control. In\n        // WebKit however, the delta is < 1, so we simply disable animation in\n        // the `chart.mapZoom` call below.\n        if (Math.abs(delta) >= 1) {\n            totalWheelDelta += Math.abs(delta);\n            if (totalWheelDeltaTimer) {\n                clearTimeout(totalWheelDeltaTimer);\n            }\n            totalWheelDeltaTimer = setTimeout(() => {\n                totalWheelDelta = 0;\n            }, 50);\n        }\n        if (totalWheelDelta < 10 && chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop) && chart.mapView) {\n            chart.mapView.zoomBy((chart.options.mapNavigation.mouseWheelSensitivity -\n                1) * -delta, void 0, [e.chartX, e.chartY], \n            // Delta less than 1 indicates stepless/trackpad zooming, avoid\n            // animation delaying the zoom\n            Math.abs(delta) < 1 ? false : void 0);\n        }\n    }\n    /**\n     * Add lon and lat information to pointer events\n     * @private\n     */\n    function wrapNormalize(proceed, e, chartPosition) {\n        const chart = this.chart;\n        e = proceed.call(this, e, chartPosition);\n        if (chart && chart.mapView) {\n            const lonLat = chart.mapView.pixelsToLonLat({\n                x: e.chartX - chart.plotLeft,\n                y: e.chartY - chart.plotTop\n            });\n            if (lonLat) {\n                extend(e, lonLat);\n            }\n        }\n        return e;\n    }\n    /**\n     * The pinchType is inferred from mapNavigation options.\n     * @private\n     */\n    function wrapZoomOption(proceed) {\n        const mapNavigation = this.chart.options.mapNavigation;\n        // Pinch status\n        if (mapNavigation &&\n            pick(mapNavigation.enableTouchZoom, mapNavigation.enabled)) {\n            this.chart.zooming.pinchType = 'xy';\n        }\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n})(MapPointer || (MapPointer = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Maps_MapPointer = (MapPointer);\n\n;// ./code/es-modules/Maps/MapSymbols.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Variables\n *\n * */\nlet symbols;\n/* *\n *\n *  Functions\n *\n * */\n/**\n *\n */\nfunction bottomButton(x, y, w, h, options) {\n    if (options) {\n        const r = options?.r || 0;\n        options.brBoxY = y - r;\n        options.brBoxHeight = h + r;\n    }\n    return symbols.roundedRect(x, y, w, h, options);\n}\n/**\n *\n */\nfunction compose(SVGRendererClass) {\n    symbols = SVGRendererClass.prototype.symbols;\n    symbols.bottombutton = bottomButton;\n    symbols.topbutton = topButton;\n}\n/**\n *\n */\nfunction topButton(x, y, w, h, options) {\n    if (options) {\n        const r = options?.r || 0;\n        options.brBoxHeight = h + r;\n    }\n    return symbols.roundedRect(x, y, w, h, options);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst MapSymbols = {\n    compose\n};\n/* harmony default export */ const Maps_MapSymbols = (MapSymbols);\n\n;// ./code/es-modules/Maps/MapNavigation.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { setOptions } = (external_highcharts_src_js_default_default());\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\n\n\n\nconst { addEvent, extend: MapNavigation_extend, merge, objectEach, pick: MapNavigation_pick, pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction stopEvent(e) {\n    if (e) {\n        e.preventDefault?.();\n        e.stopPropagation?.();\n        e.cancelBubble = true;\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The MapNavigation handles buttons for navigation in addition to mousewheel\n * and doubleclick handlers for chart zooming.\n *\n * @private\n * @class\n * @name MapNavigation\n *\n * @param {Highcharts.Chart} chart\n *        The Chart instance.\n */\nclass MapNavigation {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(MapChartClass, PointerClass, SVGRendererClass) {\n        Maps_MapPointer.compose(PointerClass);\n        Maps_MapSymbols.compose(SVGRendererClass);\n        if (pushUnique(composed, 'Map.Navigation')) {\n            // Extend the Chart.render method to add zooming and panning\n            addEvent(MapChartClass, 'beforeRender', function () {\n                // Render the plus and minus buttons. Doing this before the\n                // shapes makes getBBox much quicker, at least in Chrome.\n                this.mapNavigation = new MapNavigation(this);\n                this.mapNavigation.update();\n            });\n            setOptions(MapNavigationDefaults);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart) {\n        this.chart = chart;\n        this.navButtons = [];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Update the map navigation with new options. Calling this is the same as\n     * calling `chart.update({ mapNavigation: {} })`.\n     *\n     * @function MapNavigation#update\n     *\n     * @param {Partial<Highcharts.MapNavigationOptions>} [options]\n     *        New options for the map navigation.\n     */\n    update(options) {\n        const mapNav = this, chart = mapNav.chart, navButtons = mapNav.navButtons, outerHandler = function (e) {\n            this.handler.call(chart, e);\n            stopEvent(e); // Stop default click event (#4444)\n        };\n        let navOptions = chart.options.mapNavigation;\n        // Merge in new options in case of update, and register back to chart\n        // options.\n        if (options) {\n            navOptions = chart.options.mapNavigation =\n                merge(chart.options.mapNavigation, options);\n        }\n        // Destroy buttons in case of dynamic update\n        while (navButtons.length) {\n            navButtons.pop()?.destroy();\n        }\n        if (!chart.renderer.forExport &&\n            MapNavigation_pick(navOptions.enableButtons, navOptions.enabled)) {\n            if (!mapNav.navButtonsGroup) {\n                mapNav.navButtonsGroup = chart.renderer.g()\n                    .attr({\n                    zIndex: 7 // #4955, #8392, #20476\n                })\n                    .add();\n            }\n            objectEach(navOptions.buttons, (buttonOptions, n) => {\n                buttonOptions = merge(navOptions.buttonOptions, buttonOptions);\n                const attr = {\n                    padding: buttonOptions.padding\n                };\n                // Presentational\n                if (!chart.styledMode && buttonOptions.theme) {\n                    MapNavigation_extend(attr, buttonOptions.theme);\n                    attr.style = merge(buttonOptions.theme.style, buttonOptions.style // #3203\n                    );\n                }\n                const { text, width = 0, height = 0, padding = 0 } = buttonOptions;\n                const button = chart.renderer\n                    .button(\n                // Display the text from options only if it is not plus\n                // or minus\n                (text !== '+' && text !== '-' && text) || '', 0, 0, outerHandler, attr, void 0, void 0, void 0, n === 'zoomIn' ? 'topbutton' : 'bottombutton')\n                    .addClass('highcharts-map-navigation highcharts-' + {\n                    zoomIn: 'zoom-in',\n                    zoomOut: 'zoom-out'\n                }[n])\n                    .attr({\n                    width,\n                    height,\n                    title: chart.options.lang[n],\n                    zIndex: 5\n                })\n                    .add(mapNav.navButtonsGroup);\n                // Add SVG paths for the default symbols, because the text\n                // representation of + and - is not sharp and position is not\n                // easy to control.\n                if (text === '+' || text === '-') {\n                    // Mysterious +1 to achieve centering\n                    const w = width + 1, d = [\n                        ['M', padding + 3, padding + height / 2],\n                        ['L', padding + w - 3, padding + height / 2]\n                    ];\n                    if (text === '+') {\n                        d.push(['M', padding + w / 2, padding + 3], ['L', padding + w / 2, padding + height - 3]);\n                    }\n                    chart.renderer\n                        .path(d)\n                        .addClass('highcharts-button-symbol')\n                        .attr(chart.styledMode ? {} : {\n                        stroke: buttonOptions.style?.color,\n                        'stroke-width': 3,\n                        'stroke-linecap': 'round'\n                    })\n                        .add(button);\n                }\n                button.handler = buttonOptions.onclick;\n                // Stop double click event (#4444)\n                addEvent(button.element, 'dblclick', stopEvent);\n                navButtons.push(button);\n                MapNavigation_extend(buttonOptions, {\n                    width: button.width,\n                    height: 2 * (button.height || 0)\n                });\n                if (!chart.hasLoaded) {\n                    // Align it after the plotBox is known (#12776)\n                    const unbind = addEvent(chart, 'load', () => {\n                        // #15406: Make sure button hasnt been destroyed\n                        if (button.element) {\n                            button.align(buttonOptions, false, buttonOptions.alignTo);\n                        }\n                        unbind();\n                    });\n                }\n                else {\n                    button.align(buttonOptions, false, buttonOptions.alignTo);\n                }\n            });\n            // Borrowed from overlapping-datalabels. Consider a shared module.\n            const isIntersectRect = (box1, box2) => !(box2.x >= box1.x + box1.width ||\n                box2.x + box2.width <= box1.x ||\n                box2.y >= box1.y + box1.height ||\n                box2.y + box2.height <= box1.y);\n            // Check the mapNavigation buttons collision with exporting button\n            // and translate the mapNavigation button if they overlap.\n            const adjustMapNavBtn = function () {\n                const expBtnBBox = chart.exportingGroup?.getBBox();\n                if (expBtnBBox) {\n                    const navBtnsBBox = mapNav.navButtonsGroup.getBBox();\n                    // If buttons overlap\n                    if (isIntersectRect(expBtnBBox, navBtnsBBox)) {\n                        // Adjust the mapNav buttons' position by translating\n                        // them above or below the exporting button\n                        const aboveExpBtn = -navBtnsBBox.y -\n                            navBtnsBBox.height + expBtnBBox.y - 5, belowExpBtn = expBtnBBox.y + expBtnBBox.height -\n                            navBtnsBBox.y + 5, mapNavVerticalAlign = (navOptions.buttonOptions &&\n                            navOptions.buttonOptions.verticalAlign);\n                        // If bottom aligned and adjusting the mapNav button\n                        // would translate it out of the plotBox, translate it\n                        // up instead of down\n                        mapNav.navButtonsGroup.attr({\n                            translateY: mapNavVerticalAlign === 'bottom' ?\n                                aboveExpBtn :\n                                belowExpBtn\n                        });\n                    }\n                }\n            };\n            if (!chart.hasLoaded) {\n                // Align it after the plotBox is known (#12776) and after the\n                // hamburger button's position is known so they don't overlap\n                // (#15782)\n                addEvent(chart, 'render', adjustMapNavBtn);\n            }\n        }\n        this.updateEvents(navOptions);\n    }\n    /**\n     * Update events, called internally from the update function. Add new event\n     * handlers, or unbinds events if disabled.\n     *\n     * @function MapNavigation#updateEvents\n     *\n     * @param {Partial<Highcharts.MapNavigationOptions>} options\n     *        Options for map navigation.\n     */\n    updateEvents(options) {\n        const chart = this.chart;\n        // Add the double click event\n        if (MapNavigation_pick(options.enableDoubleClickZoom, options.enabled) ||\n            options.enableDoubleClickZoomTo) {\n            this.unbindDblClick = this.unbindDblClick || addEvent(chart.container, 'dblclick', function (e) {\n                chart.pointer.onContainerDblClick(e);\n            });\n        }\n        else if (this.unbindDblClick) {\n            // Unbind and set unbinder to undefined\n            this.unbindDblClick = this.unbindDblClick();\n        }\n        // Add the mousewheel event\n        if (MapNavigation_pick(options.enableMouseWheelZoom, options.enabled)) {\n            this.unbindMouseWheel = this.unbindMouseWheel || addEvent(chart.container, 'wheel', function (e) {\n                // Prevent scrolling when the pointer is over the element\n                // with that class, for example anotation popup #12100.\n                if (!chart.pointer.inClass(e.target, 'highcharts-no-mousewheel')) {\n                    const initialZoom = chart.mapView?.zoom;\n                    chart.pointer.onContainerMouseWheel(e);\n                    // If the zoom level changed, prevent the default action\n                    // which is to scroll the page\n                    if (initialZoom !== chart.mapView?.zoom) {\n                        stopEvent(e);\n                    }\n                }\n                return false;\n            });\n        }\n        else if (this.unbindMouseWheel) {\n            // Unbind and set unbinder to undefined\n            this.unbindMouseWheel = this.unbindMouseWheel();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Maps_MapNavigation = (MapNavigation);\n\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SVGElement\"]\nconst external_highcharts_src_js_default_SVGElement_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGElement;\nvar external_highcharts_src_js_default_SVGElement_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGElement_namespaceObject);\n;// ./code/es-modules/Series/ColorMapComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: columnProto } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent: ColorMapComposition_addEvent, defined: ColorMapComposition_defined } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorMapComposition;\n(function (ColorMapComposition) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    ColorMapComposition.pointMembers = {\n        dataLabelOnNull: true,\n        moveToTopOnHover: true,\n        isValid: pointIsValid\n    };\n    ColorMapComposition.seriesMembers = {\n        colorKey: 'value',\n        axisTypes: ['xAxis', 'yAxis', 'colorAxis'],\n        parallelArrays: ['x', 'y', 'value'],\n        pointArrayMap: ['value'],\n        trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n        colorAttribs: seriesColorAttribs,\n        pointAttribs: columnProto.pointAttribs\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        const PointClass = SeriesClass.prototype.pointClass;\n        ColorMapComposition_addEvent(PointClass, 'afterSetState', onPointAfterSetState);\n        return SeriesClass;\n    }\n    ColorMapComposition.compose = compose;\n    /**\n     * Move points to the top of the z-index order when hovered.\n     * @private\n     */\n    function onPointAfterSetState(e) {\n        const point = this, series = point.series, renderer = series.chart.renderer;\n        if (point.moveToTopOnHover && point.graphic) {\n            if (!series.stateMarkerGraphic) {\n                // Create a `use` element and add it to the end of the group,\n                // which would make it appear on top of the other elements. This\n                // deals with z-index without reordering DOM elements (#13049).\n                series.stateMarkerGraphic = new (external_highcharts_src_js_default_SVGElement_default())(renderer, 'use')\n                    .css({\n                    pointerEvents: 'none'\n                })\n                    .add(point.graphic.parentGroup);\n            }\n            if (e?.state === 'hover') {\n                // Give the graphic DOM element the same id as the Point\n                // instance\n                point.graphic.attr({\n                    id: this.id\n                });\n                series.stateMarkerGraphic.attr({\n                    href: `${renderer.url}#${this.id}`,\n                    visibility: 'visible'\n                });\n            }\n            else {\n                series.stateMarkerGraphic.attr({\n                    href: ''\n                });\n            }\n        }\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    function pointIsValid() {\n        return (this.value !== null &&\n            this.value !== Infinity &&\n            this.value !== -Infinity &&\n            // Undefined is allowed, but NaN is not (#17279)\n            (this.value === void 0 || !isNaN(this.value)));\n    }\n    /**\n     * Get the color attributes to apply on the graphic\n     * @private\n     * @function Highcharts.colorMapSeriesMixin.colorAttribs\n     * @param {Highcharts.Point} point\n     * @return {Highcharts.SVGAttributes}\n     *         The SVG attributes\n     */\n    function seriesColorAttribs(point) {\n        const ret = {};\n        if (ColorMapComposition_defined(point.color) &&\n            (!point.state || point.state === 'normal') // #15746\n        ) {\n            ret[this.colorProp || 'fill'] = point.color;\n        }\n        return ret;\n    }\n})(ColorMapComposition || (ColorMapComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_ColorMapComposition = (ColorMapComposition);\n\n;// external [\"../highcharts.js\",\"default\",\"Series\"]\nconst external_highcharts_src_js_default_Series_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Series;\nvar external_highcharts_src_js_default_Series_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Series_namespaceObject);\n;// ./code/es-modules/Series/CenteredUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { deg2rad } = (external_highcharts_src_js_default_default());\n\n\nconst { fireEvent, isNumber, pick: CenteredUtilities_pick, relativeLength } = (external_highcharts_src_js_default_default());\n/**\n * @private\n */\nvar CenteredUtilities;\n(function (CenteredUtilities) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Get the center of the pie based on the size and center options relative\n     * to the plot area. Borrowed by the polar and gauge series types.\n     *\n     * @private\n     * @function Highcharts.CenteredSeriesMixin.getCenter\n     */\n    function getCenter() {\n        const options = this.options, chart = this.chart, slicingRoom = 2 * (options.slicedOffset || 0), plotWidth = chart.plotWidth - 2 * slicingRoom, plotHeight = chart.plotHeight - 2 * slicingRoom, centerOption = options.center, smallestSize = Math.min(plotWidth, plotHeight), thickness = options.thickness;\n        let handleSlicingRoom, size = options.size, innerSize = options.innerSize || 0, i, value;\n        if (typeof size === 'string') {\n            size = parseFloat(size);\n        }\n        if (typeof innerSize === 'string') {\n            innerSize = parseFloat(innerSize);\n        }\n        const positions = [\n            CenteredUtilities_pick(centerOption?.[0], '50%'),\n            CenteredUtilities_pick(centerOption?.[1], '50%'),\n            // Prevent from negative values\n            CenteredUtilities_pick(size && size < 0 ? void 0 : options.size, '100%'),\n            CenteredUtilities_pick(innerSize && innerSize < 0 ? void 0 : options.innerSize || 0, '0%')\n        ];\n        // No need for inner size in angular (gauges) series but still required\n        // for pie series\n        if (chart.angular && !(this instanceof (external_highcharts_src_js_default_Series_default()))) {\n            positions[3] = 0;\n        }\n        for (i = 0; i < 4; ++i) {\n            value = positions[i];\n            handleSlicingRoom = i < 2 || (i === 2 && /%$/.test(value));\n            // I == 0: centerX, relative to width\n            // i == 1: centerY, relative to height\n            // i == 2: size, relative to smallestSize\n            // i == 3: innerSize, relative to size\n            positions[i] = relativeLength(value, [plotWidth, plotHeight, smallestSize, positions[2]][i]) + (handleSlicingRoom ? slicingRoom : 0);\n        }\n        // Inner size cannot be larger than size (#3632)\n        if (positions[3] > positions[2]) {\n            positions[3] = positions[2];\n        }\n        // Thickness overrides innerSize, need to be less than pie size (#6647)\n        if (isNumber(thickness) &&\n            thickness * 2 < positions[2] && thickness > 0) {\n            positions[3] = positions[2] - thickness * 2;\n        }\n        fireEvent(this, 'afterGetCenter', { positions });\n        return positions;\n    }\n    CenteredUtilities.getCenter = getCenter;\n    /**\n     * GetStartAndEndRadians - Calculates start and end angles in radians.\n     * Used in series types such as pie and sunburst.\n     *\n     * @private\n     * @function Highcharts.CenteredSeriesMixin.getStartAndEndRadians\n     *\n     * @param {number} [start]\n     *        Start angle in degrees.\n     *\n     * @param {number} [end]\n     *        Start angle in degrees.\n     *\n     * @return {Highcharts.RadianAngles}\n     *         Returns an object containing start and end angles as radians.\n     */\n    function getStartAndEndRadians(start, end) {\n        const startAngle = isNumber(start) ? start : 0, // Must be a number\n        endAngle = ((isNumber(end) && // Must be a number\n            end > startAngle && // Must be larger than the start angle\n            // difference must be less than 360 degrees\n            (end - startAngle) < 360) ?\n            end :\n            startAngle + 360), correction = -90;\n        return {\n            start: deg2rad * (startAngle + correction),\n            end: deg2rad * (endAngle + correction)\n        };\n    }\n    CenteredUtilities.getStartAndEndRadians = getStartAndEndRadians;\n})(CenteredUtilities || (CenteredUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_CenteredUtilities = (CenteredUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.RadianAngles\n */ /**\n* @name Highcharts.RadianAngles#end\n* @type {number}\n*/ /**\n* @name Highcharts.RadianAngles#start\n* @type {number}\n*/\n''; // Keeps doclets above in JS file\n\n;// external [\"../highcharts.js\",\"default\",\"Chart\"]\nconst external_highcharts_src_js_default_Chart_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Chart;\nvar external_highcharts_src_js_default_Chart_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Chart_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SVGRenderer\"]\nconst external_highcharts_src_js_default_SVGRenderer_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGRenderer;\nvar external_highcharts_src_js_default_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGRenderer_namespaceObject);\n;// ./code/es-modules/Core/Chart/MapChart.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { getOptions } = (external_highcharts_src_js_default_default());\n\n\nconst { isNumber: MapChart_isNumber, merge: MapChart_merge, pick: MapChart_pick } = (external_highcharts_src_js_default_default());\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Map-optimized chart. Use {@link Highcharts.Chart|Chart} for common charts.\n *\n * @requires modules/map\n *\n * @class\n * @name Highcharts.MapChart\n * @extends Highcharts.Chart\n */\nclass MapChart extends (external_highcharts_src_js_default_Chart_default()) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initializes the chart. The constructor's arguments are passed on\n     * directly.\n     *\n     * @function Highcharts.MapChart#init\n     *\n     * @param {Highcharts.Options} userOptions\n     *        Custom options.\n     *\n     * @param {Function} [callback]\n     *        Function to run when the chart has loaded and all external\n     *        images are loaded.\n     *\n     *\n     * @emits Highcharts.MapChart#event:init\n     * @emits Highcharts.MapChart#event:afterInit\n     */\n    init(userOptions, callback) {\n        const defaultCreditsOptions = getOptions().credits;\n        const options = MapChart_merge({\n            chart: {\n                panning: {\n                    enabled: true,\n                    type: 'xy'\n                },\n                type: 'map'\n            },\n            credits: {\n                mapText: MapChart_pick(defaultCreditsOptions.mapText, ' \\u00a9 <a href=\"{geojson.copyrightUrl}\">' +\n                    '{geojson.copyrightShort}</a>'),\n                mapTextFull: MapChart_pick(defaultCreditsOptions.mapTextFull, '{geojson.copyright}')\n            },\n            mapView: {}, // Required to enable Chart.mapView\n            tooltip: {\n                followTouchMove: false\n            }\n        }, userOptions // User's options\n        );\n        super.init(options, callback);\n    }\n    /**\n     * Highcharts Maps only. Zoom in or out of the map. See also\n     * {@link Point#zoomTo}. See {@link Chart#fromLatLonToPoint} for how to get\n     * the `centerX` and `centerY` parameters for a geographic location.\n     *\n     * Deprecated as of v9.3 in favor of [MapView.zoomBy](https://api.highcharts.com/class-reference/Highcharts.MapView#zoomBy).\n     *\n     * @deprecated\n     * @function Highcharts.Chart#mapZoom\n     *\n     * @param {number} [howMuch]\n     *        How much to zoom the map. Values less than 1 zooms in. 0.5 zooms\n     *        in to half the current view. 2 zooms to twice the current view. If\n     *        omitted, the zoom is reset.\n     *\n     * @param {number} [xProjected]\n     *        The projected x position to keep stationary when zooming, if\n     *        available space.\n     *\n     * @param {number} [yProjected]\n     *        The projected y position to keep stationary when zooming, if\n     *        available space.\n     *\n     * @param {number} [chartX]\n     *        Keep this chart position stationary if possible. This is used for\n     *        example in `mousewheel` events, where the area under the mouse\n     *        should be fixed as we zoom in.\n     *\n     * @param {number} [chartY]\n     *        Keep this chart position stationary if possible.\n     */\n    mapZoom(howMuch, xProjected, yProjected, chartX, chartY) {\n        if (this.mapView) {\n            if (MapChart_isNumber(howMuch)) {\n                // Compliance, mapView.zoomBy uses different values\n                howMuch = Math.log(howMuch) / Math.log(0.5);\n            }\n            this.mapView.zoomBy(howMuch, MapChart_isNumber(xProjected) && MapChart_isNumber(yProjected) ?\n                this.mapView.projection.inverse([xProjected, yProjected]) :\n                void 0, MapChart_isNumber(chartX) && MapChart_isNumber(chartY) ?\n                [chartX, chartY] :\n                void 0);\n        }\n    }\n    update(options) {\n        // Calculate and set the recommended map view if map option is set\n        if (options.chart && 'map' in options.chart) {\n            this.mapView?.recommendMapView(this, [\n                options.chart.map,\n                ...(this.options.series || []).map((s) => s.mapData)\n            ], true);\n        }\n        super.update.apply(this, arguments);\n    }\n}\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (MapChart) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Contains all loaded map data for Highmaps.\n     *\n     * @requires modules/map\n     *\n     * @name Highcharts.maps\n     * @type {Record<string,*>}\n     */\n    MapChart.maps = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * The factory function for creating new map charts. Creates a new {@link\n     * Highcharts.MapChart|MapChart} object with different default options than\n     * the basic Chart.\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.mapChart\n     *\n     * @param {string|Highcharts.HTMLDOMElement} [renderTo]\n     *        The DOM element to render to, or its id.\n     *\n     * @param {Highcharts.Options} options\n     *        The chart options structure as described in the\n     *        [options reference](https://api.highcharts.com/highstock).\n     *\n     * @param {Highcharts.ChartCallbackFunction} [callback]\n     *        A function to execute when the chart object is finished\n     *        rendering and all external image files (`chart.backgroundImage`,\n     *        `chart.plotBackgroundImage` etc) are loaded.  Defining a\n     *        [chart.events.load](https://api.highcharts.com/highstock/chart.events.load)\n     *        handler is equivalent.\n     *\n     * @return {Highcharts.MapChart}\n     * The chart object.\n     */\n    function mapChart(a, b, c) {\n        return new MapChart(a, b, c);\n    }\n    MapChart.mapChart = mapChart;\n    /**\n     * Utility for reading SVG paths directly.\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.splitPath\n     *\n     * @param {string|Array<(string|number)>} path\n     *        Path to split.\n     *\n     * @return {Highcharts.SVGPathArray}\n     * Splitted SVG path\n     */\n    function splitPath(path) {\n        let arr;\n        if (typeof path === 'string') {\n            path = path\n                // Move letters apart\n                .replace(/([A-Z])/gi, ' $1 ')\n                // Trim\n                .replace(/^\\s*/, '').replace(/\\s*$/, '');\n            // Split on spaces and commas. The semicolon is bogus, designed to\n            // circumvent string replacement in the pre-v7 assembler that built\n            // specific styled mode files.\n            const split = path.split(/[ ,;]+/);\n            arr = split.map((item) => {\n                if (!/[A-Z]/i.test(item)) {\n                    return parseFloat(item);\n                }\n                return item;\n            });\n        }\n        else {\n            arr = path;\n        }\n        return external_highcharts_src_js_default_SVGRenderer_default().prototype.pathToSegments(arr);\n    }\n    MapChart.splitPath = splitPath;\n})(MapChart || (MapChart = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Chart_MapChart = (MapChart);\n\n;// ./code/es-modules/Maps/MapUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n// Compute bounds from a path element\nconst boundsFromPath = function (path) {\n    let x2 = -Number.MAX_VALUE, x1 = Number.MAX_VALUE, y2 = -Number.MAX_VALUE, y1 = Number.MAX_VALUE, validBounds;\n    path.forEach((seg) => {\n        const x = seg[seg.length - 2], y = seg[seg.length - 1];\n        if (typeof x === 'number' &&\n            typeof y === 'number') {\n            x1 = Math.min(x1, x);\n            x2 = Math.max(x2, x);\n            y1 = Math.min(y1, y);\n            y2 = Math.max(y2, y);\n            validBounds = true;\n        }\n    });\n    if (validBounds) {\n        return { x1, y1, x2, y2 };\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst MapUtilities = {\n    boundsFromPath\n};\n/* harmony default export */ const Maps_MapUtilities = (MapUtilities);\n\n;// ./code/es-modules/Series/Map/MapPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { boundsFromPath: MapPoint_boundsFromPath } = Maps_MapUtilities;\n\nconst ScatterPoint = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes.scatter.prototype.pointClass;\n\nconst { extend: MapPoint_extend, isNumber: MapPoint_isNumber, pick: MapPoint_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass MapPoint extends ScatterPoint {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Get the projected path based on the geometry. May also be called on\n     * mapData options (not point instances), hence static.\n     * @private\n     */\n    static getProjectedPath(point, projection) {\n        if (!point.projectedPath) {\n            if (projection && point.geometry) {\n                // Always true when given GeoJSON coordinates\n                projection.hasCoordinates = true;\n                point.projectedPath = projection.path(point.geometry);\n                // SVG path given directly in point options\n            }\n            else {\n                point.projectedPath = point.path;\n            }\n        }\n        return point.projectedPath || [];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extend the Point object to split paths.\n     * @private\n     */\n    applyOptions(options, x) {\n        const series = this.series, point = super.applyOptions(options, x), joinBy = series.joinBy;\n        if (series.mapData && series.mapMap) {\n            const joinKey = joinBy[1], mapKey = super.getNestedProperty(joinKey), mapPoint = typeof mapKey !== 'undefined' &&\n                series.mapMap[mapKey];\n            if (mapPoint) {\n                // Copy over properties; #20231 prioritize point.name\n                MapPoint_extend(point, {\n                    ...mapPoint,\n                    name: point.name ?? mapPoint.name\n                });\n            }\n            else if (series.pointArrayMap.indexOf('value') !== -1) {\n                point.value = point.value || null;\n            }\n        }\n        return point;\n    }\n    /**\n     * Get the bounds in terms of projected units\n     * @private\n     */\n    getProjectedBounds(projection) {\n        const path = MapPoint.getProjectedPath(this, projection), bounds = MapPoint_boundsFromPath(path), properties = this.properties, mapView = this.series.chart.mapView;\n        if (bounds) {\n            // Cache point bounding box for use to position data labels, bubbles\n            // etc\n            const propMiddleLon = properties?.['hc-middle-lon'], propMiddleLat = properties?.['hc-middle-lat'];\n            if (mapView && MapPoint_isNumber(propMiddleLon) && MapPoint_isNumber(propMiddleLat)) {\n                const projectedPoint = projection.forward([propMiddleLon, propMiddleLat]);\n                bounds.midX = projectedPoint[0];\n                bounds.midY = projectedPoint[1];\n            }\n            else {\n                const propMiddleX = properties?.['hc-middle-x'], propMiddleY = properties?.['hc-middle-y'];\n                bounds.midX = (bounds.x1 + (bounds.x2 - bounds.x1) * MapPoint_pick(this.middleX, MapPoint_isNumber(propMiddleX) ? propMiddleX : 0.5));\n                let middleYFraction = MapPoint_pick(this.middleY, MapPoint_isNumber(propMiddleY) ? propMiddleY : 0.5);\n                // No geographic geometry, only path given => flip\n                if (!this.geometry) {\n                    middleYFraction = 1 - middleYFraction;\n                }\n                bounds.midY =\n                    bounds.y2 - (bounds.y2 - bounds.y1) * middleYFraction;\n            }\n            return bounds;\n        }\n    }\n    /**\n     * Stop the fade-out\n     * @private\n     */\n    onMouseOver(e) {\n        external_highcharts_src_js_default_default().clearTimeout(this.colorInterval);\n        if (\n        // Valid...\n        (!this.isNull && this.visible) ||\n            // ... or interact anyway\n            this.series.options.nullInteraction) {\n            super.onMouseOver.call(this, e);\n        }\n        else {\n            // #3401 Tooltip doesn't hide when hovering over null points\n            this.series.onMouseOut();\n        }\n    }\n    setVisible(vis) {\n        const method = vis ? 'show' : 'hide';\n        this.visible = this.options.visible = !!vis;\n        // Show and hide associated elements\n        if (this.dataLabel) {\n            this.dataLabel[method]();\n        }\n        // For invisible map points, render them as null points rather than\n        // fully removing them. Makes more sense for color axes with data\n        // classes.\n        if (this.graphic) {\n            this.graphic.attr(this.series.pointAttribs(this));\n        }\n    }\n    /**\n     * Highmaps only. Zoom in on the point using the global animation.\n     *\n     * @sample maps/members/point-zoomto/\n     *         Zoom to points from buttons\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.Point#zoomTo\n     */\n    zoomTo(animOptions) {\n        const point = this, chart = point.series.chart, mapView = chart.mapView;\n        let bounds = point.bounds;\n        if (mapView && bounds) {\n            const inset = MapPoint_isNumber(point.insetIndex) &&\n                mapView.insets[point.insetIndex];\n            if (inset) {\n                // If in an inset, translate the bounds to pixels ...\n                const px1 = inset.projectedUnitsToPixels({\n                    x: bounds.x1,\n                    y: bounds.y1\n                }), px2 = inset.projectedUnitsToPixels({\n                    x: bounds.x2,\n                    y: bounds.y2\n                }), \n                // ... then back to projected units in the main mapView\n                proj1 = mapView.pixelsToProjectedUnits({\n                    x: px1.x,\n                    y: px1.y\n                }), proj2 = mapView.pixelsToProjectedUnits({\n                    x: px2.x,\n                    y: px2.y\n                });\n                bounds = {\n                    x1: proj1.x,\n                    y1: proj1.y,\n                    x2: proj2.x,\n                    y2: proj2.y\n                };\n            }\n            mapView.fitToBounds(bounds, void 0, false);\n            point.series.isDirty = true;\n            chart.redraw(animOptions);\n        }\n    }\n}\nMapPoint_extend(MapPoint.prototype, {\n    dataLabelOnNull: Series_ColorMapComposition.pointMembers.dataLabelOnNull,\n    moveToTopOnHover: Series_ColorMapComposition.pointMembers.moveToTopOnHover,\n    isValid: Series_ColorMapComposition.pointMembers.isValid\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Map_MapPoint = (MapPoint);\n\n;// ./code/es-modules/Series/Map/MapSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isNumber: MapSeriesDefaults_isNumber } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The map series is used for basic choropleth maps, where each map area has\n * a color based on its value.\n *\n * @sample maps/demo/all-maps/\n *         Choropleth map\n *\n * @extends      plotOptions.scatter\n * @excluding    boostBlending, boostThreshold, dragDrop, cluster, marker\n * @product      highmaps\n * @optionparent plotOptions.map\n *\n * @private\n */\nconst MapSeriesDefaults = {\n    /**\n     * Whether the MapView takes this series into account when computing the\n     * default zoom and center of the map.\n     *\n     * @sample maps/series/affectsmapview/\n     *         US map with world map backdrop\n     *\n     * @since 10.0.0\n     *\n     * @private\n     */\n    affectsMapView: true,\n    animation: false, // Makes the complex shapes slow\n    dataLabels: {\n        crop: false,\n        formatter: function () {\n            const { numberFormatter } = this.series.chart;\n            const { value } = this.point;\n            return MapSeriesDefaults_isNumber(value) ?\n                numberFormatter(value, -1) :\n                (this.point.name || ''); // #20231\n        },\n        inside: true, // For the color\n        overflow: false,\n        padding: 0,\n        verticalAlign: 'middle'\n    },\n    /**\n     * The SVG value used for the `stroke-linecap` and `stroke-linejoin` of\n     * the map borders. Round means that borders are rounded in the ends and\n     * bends.\n     *\n     * @sample maps/demo/mappoint-mapmarker/\n     *         Backdrop coastline with round linecap\n     *\n     * @type   {Highcharts.SeriesLinecapValue}\n     * @since  10.3.3\n     */\n    linecap: 'round',\n    /**\n     * @ignore-option\n     *\n     * @private\n     */\n    marker: null,\n    /**\n     * The color to apply to null points.\n     *\n     * In styled mode, the null point fill is set in the\n     * `.highcharts-null-point` class.\n     *\n     * @sample maps/demo/all-areas-as-null/\n     *         Null color\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     *\n     * @private\n     */\n    nullColor: \"#f7f7f7\" /* Palette.neutralColor3 */,\n    /**\n     * Whether to allow pointer interaction like tooltips and mouse events\n     * on null points.\n     *\n     * @type      {boolean}\n     * @since     4.2.7\n     * @apioption plotOptions.map.nullInteraction\n     *\n     * @private\n     */\n    stickyTracking: false,\n    tooltip: {\n        followPointer: true,\n        pointFormat: '{point.name}: {point.value}<br/>'\n    },\n    /**\n     * @ignore-option\n     *\n     * @private\n     */\n    turboThreshold: 0,\n    /**\n     * Whether all areas of the map defined in `mapData` should be rendered.\n     * If `true`, areas which don't correspond to a data point, are rendered\n     * as `null` points. If `false`, those areas are skipped.\n     *\n     * @sample maps/plotoptions/series-allareas-false/\n     *         All areas set to false\n     *\n     * @type      {boolean}\n     * @default   true\n     * @product   highmaps\n     * @apioption plotOptions.series.allAreas\n     *\n     * @private\n     */\n    allAreas: true,\n    /**\n     * The border color of the map areas.\n     *\n     * In styled mode, the border stroke is given in the `.highcharts-point`\n     * class.\n     *\n     * @sample {highmaps} maps/plotoptions/series-border/\n     *         Borders demo\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @default   #cccccc\n     * @product   highmaps\n     * @apioption plotOptions.series.borderColor\n     *\n     * @private\n     */\n    borderColor: \"#e6e6e6\" /* Palette.neutralColor10 */,\n    /**\n     * The border width of each map area.\n     *\n     * In styled mode, the border stroke width is given in the\n     * `.highcharts-point` class.\n     *\n     * @sample maps/plotoptions/series-border/\n     *         Borders demo\n     *\n     * @type      {number}\n     * @default   1\n     * @product   highmaps\n     * @apioption plotOptions.series.borderWidth\n     *\n     * @private\n     */\n    borderWidth: 1,\n    /**\n     * @type      {string}\n     * @default   value\n     * @apioption plotOptions.map.colorKey\n     */\n    /**\n     * What property to join the `mapData` to the value data. For example,\n     * if joinBy is \"code\", the mapData items with a specific code is merged\n     * into the data with the same code. For maps loaded from GeoJSON, the\n     * keys may be held in each point's `properties` object.\n     *\n     * The joinBy option can also be an array of two values, where the first\n     * points to a key in the `mapData`, and the second points to another\n     * key in the `data`.\n     *\n     * When joinBy is `null`, the map items are joined by their position in\n     * the array, which performs much better in maps with many data points.\n     * This is the recommended option if you are printing more than a\n     * thousand data points and have a backend that can preprocess the data\n     * into a parallel array of the mapData.\n     *\n     * @sample maps/plotoptions/series-border/\n     *         Joined by \"code\"\n     * @sample maps/demo/geojson/\n     *         GeoJSON joined by an array\n     * @sample maps/series/joinby-null/\n     *         Simple data joined by null\n     *\n     * @type      {string|Array<string>}\n     * @default   hc-key\n     * @product   highmaps\n     * @apioption plotOptions.series.joinBy\n     *\n     * @private\n     */\n    joinBy: 'hc-key',\n    /**\n     * Define the z index of the series.\n     *\n     * @type      {number}\n     * @product   highmaps\n     * @apioption plotOptions.series.zIndex\n     */\n    /**\n     * @apioption plotOptions.series.states\n     *\n     * @private\n     */\n    states: {\n        /**\n         * @apioption plotOptions.series.states.hover\n         */\n        hover: {\n            /** @ignore-option */\n            halo: void 0,\n            /**\n             * The color of the shape in this state.\n             *\n             * @sample maps/plotoptions/series-states-hover/\n             *         Hover options\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @product   highmaps\n             * @apioption plotOptions.series.states.hover.color\n             */\n            /**\n             * The border color of the point in this state.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @product   highmaps\n             * @apioption plotOptions.series.states.hover.borderColor\n             */\n            borderColor: \"#666666\" /* Palette.neutralColor60 */,\n            /**\n             * The border width of the point in this state\n             *\n             * @type      {number}\n             * @product   highmaps\n             * @apioption plotOptions.series.states.hover.borderWidth\n             */\n            borderWidth: 2\n            /**\n             * The relative brightness of the point when hovered, relative\n             * to the normal point color.\n             *\n             * @type      {number}\n             * @product   highmaps\n             * @default   0\n             * @apioption plotOptions.series.states.hover.brightness\n             */\n        },\n        /**\n         * @apioption plotOptions.series.states.normal\n         */\n        normal: {\n            /**\n             * @productdesc {highmaps}\n             * The animation adds some latency in order to reduce the effect\n             * of flickering when hovering in and out of for example an\n             * uneven coastline.\n             *\n             * @sample {highmaps} maps/plotoptions/series-states-animation-false/\n             *         No animation of fill color\n             *\n             * @apioption plotOptions.series.states.normal.animation\n             */\n            animation: true\n        },\n        /**\n         * @apioption plotOptions.series.states.select\n         */\n        select: {\n            /**\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @default   ${palette.neutralColor20}\n             * @product   highmaps\n             * @apioption plotOptions.series.states.select.color\n             */\n            color: \"#cccccc\" /* Palette.neutralColor20 */\n        }\n    },\n    legendSymbol: 'rectangle'\n};\n/**\n * An array of objects containing a `geometry` or `path` definition and\n * optionally additional properties to join in the `data` as per the `joinBy`\n * option. GeoJSON and TopoJSON structures can also be passed directly into\n * `mapData`.\n *\n * @sample maps/demo/category-map/\n *         Map data and joinBy\n * @sample maps/series/mapdata-multiple/\n *         Multiple map sources\n *\n * @type      {Array<Highcharts.SeriesMapDataOptions>|Highcharts.GeoJSON|Highcharts.TopoJSON}\n * @product   highmaps\n * @apioption series.mapData\n */\n/**\n * A `map` series. If the [type](#series.map.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.map\n * @excluding dataParser, dataURL, dragDrop, marker\n * @product   highmaps\n * @apioption series.map\n */\n/**\n * An array of data points for the series. For the `map` series type, points can\n * be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `value` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of arrays with 2 values. In this case, the values correspond to\n *    `[hc-key, value]`. Example:\n *    ```js\n *        data: [\n *            ['us-ny', 0],\n *            ['us-mi', 5],\n *            ['us-tx', 3],\n *            ['us-ak', 5]\n *        ]\n *    ```\n *\n * 3. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.map.turboThreshold),\n *    this option is not available.\n *    ```js\n *        data: [{\n *            value: 6,\n *            name: \"Point2\",\n *            color: \"#00FF00\"\n *        }, {\n *            value: 6,\n *            name: \"Point1\",\n *            color: \"#FF00FF\"\n *        }]\n *    ```\n *\n * @type      {Array<number|Array<string,(number|null)>|null|*>}\n * @product   highmaps\n * @apioption series.map.data\n */\n/**\n * When using automatic point colors pulled from the global\n * [colors](colors) or series-specific\n * [plotOptions.map.colors](series.colors) collections, this option\n * determines whether the chart should receive one color per series or\n * one color per point.\n *\n * In styled mode, the `colors` or `series.colors` arrays are not\n * supported, and instead this option gives the points individual color\n * class names on the form `highcharts-color-{n}`.\n *\n * @see [series colors](#plotOptions.map.colors)\n *\n * @sample {highmaps} maps/plotoptions/mapline-colorbypoint-false/\n *         Mapline colorByPoint set to false by default\n * @sample {highmaps} maps/plotoptions/mapline-colorbypoint-true/\n *         Mapline colorByPoint set to true\n *\n * @type      {boolean}\n * @default   false\n * @since     2.0\n * @product   highmaps\n * @apioption plotOptions.map.colorByPoint\n */\n/**\n * A series specific or series type specific color set to apply instead\n * of the global [colors](#colors) when [colorByPoint](\n * #plotOptions.map.colorByPoint) is true.\n *\n * @type      {Array<Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject>}\n * @since     3.0\n * @product   highmaps\n * @apioption plotOptions.map.colors\n */\n/**\n * Individual color for the point. By default the color is either used\n * to denote the value, or pulled from the global `colors` array.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highmaps\n * @apioption series.map.data.color\n */\n/**\n * Individual data label for each point. The options are the same as\n * the ones for [plotOptions.series.dataLabels](\n * #plotOptions.series.dataLabels).\n *\n * @sample maps/series/data-datalabels/\n *         Disable data labels for individual areas\n *\n * @type      {Highcharts.DataLabelsOptions}\n * @product   highmaps\n * @apioption series.map.data.dataLabels\n */\n/**\n * The `id` of a series in the [drilldown.series](#drilldown.series)\n * array to use for a drilldown for this point.\n *\n * @sample maps/demo/map-drilldown/\n *         Basic drilldown\n *\n * @type      {string}\n * @product   highmaps\n * @apioption series.map.data.drilldown\n */\n/**\n * For map and mapline series types, the geometry of a point.\n *\n * To achieve a better separation between the structure and the data,\n * it is recommended to use `mapData` to define the geometry instead\n * of defining it on the data points themselves.\n *\n * The geometry object is compatible to that of a `feature` in GeoJSON, so\n * features of GeoJSON can be passed directly into the `data`, optionally\n * after first filtering and processing it.\n *\n * For pre-projected maps (like GeoJSON maps from our\n * [map collection](https://code.highcharts.com/mapdata/)), user has to specify\n * coordinates in `projectedUnits` for geometry type other than `Point`,\n * instead of `[longitude, latitude]`.\n *\n * @sample maps/series/mappoint-line-geometry/\n *         Map point and line geometry\n * @sample maps/series/geometry-types/\n *         Geometry types\n *\n * @type      {Object}\n * @since 9.3.0\n * @product   highmaps\n * @apioption series.map.data.geometry\n */\n/**\n * The geometry type. Can be one of `LineString`, `Polygon`, `MultiLineString`\n * or `MultiPolygon`.\n *\n * @sample maps/series/geometry-types/\n *         Geometry types\n *\n * @declare   Highcharts.MapGeometryTypeValue\n * @type      {string}\n * @since     9.3.0\n * @product   highmaps\n * @validvalue [\"LineString\", \"Polygon\", \"MultiLineString\", \"MultiPolygon\"]\n * @apioption series.map.data.geometry.type\n */\n/**\n * The geometry coordinates in terms of arrays of `[longitude, latitude]`, or\n * a two dimensional array of the same. The dimensionality must comply with the\n * `type`.\n *\n * @type      {Array<LonLatArray>|Array<Array<LonLatArray>>}\n * @since 9.3.0\n * @product   highmaps\n * @apioption series.map.data.geometry.coordinates\n */\n/**\n * An id for the point. This can be used after render time to get a\n * pointer to the point object through `chart.get()`.\n *\n * @sample maps/series/data-id/\n *         Highlight a point by id\n *\n * @type      {string}\n * @product   highmaps\n * @apioption series.map.data.id\n */\n/**\n * When data labels are laid out on a map, Highmaps runs a simplified\n * algorithm to detect collision. When two labels collide, the one with\n * the lowest rank is hidden. By default the rank is computed from the\n * area.\n *\n * @type      {number}\n * @product   highmaps\n * @apioption series.map.data.labelrank\n */\n/**\n * The relative mid point of an area, used to place the data label.\n * Ranges from 0 to 1\\. When `mapData` is used, middleX can be defined\n * there.\n *\n * @type      {number}\n * @default   0.5\n * @product   highmaps\n * @apioption series.map.data.middleX\n */\n/**\n * The relative mid point of an area, used to place the data label.\n * Ranges from 0 to 1\\. When `mapData` is used, middleY can be defined\n * there.\n *\n * @type      {number}\n * @default   0.5\n * @product   highmaps\n * @apioption series.map.data.middleY\n */\n/**\n * The name of the point as shown in the legend, tooltip, dataLabel\n * etc.\n *\n * @sample maps/series/data-datalabels/\n *         Point names\n *\n * @type      {string}\n * @product   highmaps\n * @apioption series.map.data.name\n */\n/**\n * For map and mapline series types, the SVG path for the shape. For\n * compatibility with old IE, not all SVG path definitions are supported,\n * but M, L and C operators are safe.\n *\n * To achieve a better separation between the structure and the data,\n * it is recommended to use `mapData` to define that paths instead\n * of defining them on the data points themselves.\n *\n * For providing true geographical shapes based on longitude and latitude, use\n * the `geometry` option instead.\n *\n * @sample maps/series/data-path/\n *         Paths defined in data\n *\n * @type      {string}\n * @product   highmaps\n * @apioption series.map.data.path\n */\n/**\n * The numeric value of the data point.\n *\n * @type      {number|null}\n * @product   highmaps\n * @apioption series.map.data.value\n */\n/**\n * Individual point events\n *\n * @extends   plotOptions.series.point.events\n * @product   highmaps\n * @apioption series.map.data.events\n */\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Map_MapSeriesDefaults = (MapSeriesDefaults);\n\n;// ./code/es-modules/Maps/MapViewDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The `mapView` options control the initial view of the chart, and how\n * projection is set up for raw geoJSON maps (beta as of v9.3).\n *\n * To set the view dynamically after chart generation, see\n * [mapView.setView](/class-reference/Highcharts.MapView#setView).\n *\n * @since        9.3.0\n * @product      highmaps\n * @optionparent mapView\n */\nconst MapViewDefaults = {\n    /**\n     * The center of the map in terms of longitude and latitude. For\n     * preprojected maps (like the GeoJSON files in Map Collection v1.x),\n     * the units are projected x and y units.\n     *\n     * @sample {highmaps} maps/mapview/center-zoom\n     *         Custom view of a world map\n     * @sample {highmaps} maps/mapview/get-view\n     *         Report the current view of a preprojected map\n     *\n     * @type    {Highcharts.LonLatArray}\n     * @default [0, 0]\n     */\n    center: [0, 0],\n    /**\n     * Fit the map to a geometry object consisting of individual points or\n     * polygons. This is practical for responsive maps where we want to\n     * focus on a specific area regardless of map size - unlike setting\n     * `center` and `zoom`, where the view doesn't scale with different map\n     * sizes.\n     *\n     * The geometry can be combined with the [padding](#mapView.padding)\n     * option to avoid touching the edges of the chart.\n     *\n     * @sample maps/mapview/fittogeometry\n     *         Fitting the view to geometries\n     *\n     * @type {object}\n     * @since 10.3.3\n     */\n    fitToGeometry: void 0,\n    /**\n     * Prevents the end user from zooming too far in on the map. See\n     * [zoom](#mapView.zoom).\n     *\n     * @sample {highmaps} maps/mapview/maxzoom\n     *         Prevent zooming in too far\n     *\n     * @type   {number|undefined}\n     */\n    maxZoom: void 0,\n    /**\n     * The padding inside the plot area when auto fitting to the map bounds.\n     * A number signifies pixels, and a percentage is relative to the plot\n     * area size.\n     *\n     * An array sets individual padding for the sides in the order [top,\n     * right, bottom, left].\n     *\n     * @sample {highmaps} maps/chart/plotbackgroundcolor-color\n     *         Visible plot area and percentage padding\n     * @sample {highmaps} maps/demo/mappoint-mapmarker\n     *         Padding for individual sides\n     *\n     * @type  {number|string|Array<number|string>}\n     */\n    padding: 0,\n    /**\n     * The projection options allow applying client side projection to a map\n     * given in geographic coordinates, typically from TopoJSON or GeoJSON.\n     *\n     * @sample maps/demo/projection-explorer\n     *         Projection explorer\n     * @sample maps/demo/topojson-projection\n     *         Orthographic projection\n     * @sample maps/mapview/projection-custom-proj4js\n     *         Custom UTM projection definition\n     * @sample maps/mapview/projection-custom-d3geo\n     *         Custom Robinson projection definition\n     *\n     * @type   {object}\n     */\n    projection: {\n        /**\n         * Projection name. Built-in projections are `EqualEarth`,\n         * `LambertConformalConic`, `Miller`, `Orthographic` and `WebMercator`.\n         *\n         * @sample maps/demo/projection-explorer\n         *         Projection explorer\n         * @sample maps/mapview/projection-custom-proj4js\n         *         Custom UTM projection definition\n         * @sample maps/mapview/projection-custom-d3geo\n         *         Custom Robinson projection definition\n         * @sample maps/demo/topojson-projection\n         *         Orthographic projection\n         *\n         * @type   {string}\n         */\n        name: void 0,\n        /**\n         * The two standard parallels that define the map layout in conic\n         * projections, like the LambertConformalConic projection. If only\n         * one number is given, the second parallel will be the same as the\n         * first.\n         *\n         * @sample maps/mapview/projection-parallels\n         *         LCC projection with parallels\n         * @sample maps/demo/projection-explorer\n         *         Projection explorer\n         *\n         * @type {Array<number>}\n         */\n        parallels: void 0,\n        /**\n         * Rotation of the projection in terms of degrees `[lambda, phi,\n         * gamma]`. When given, a three-axis spherical rotation is be applied\n         * to the globe prior to the projection.\n         *\n         * * `lambda` shifts the longitudes by the given value.\n         * * `phi` shifts the latitudes by the given value. Can be omitted.\n         * * `gamma` applies a _roll_. Can be omitted.\n         *\n         * @sample maps/demo/projection-explorer\n         *         Projection explorer\n         * @sample maps/mapview/projection-america-centric\n         *         America-centric world map\n         */\n        rotation: void 0\n    },\n    /**\n     * The zoom level of a map. Higher zoom levels means more zoomed in. An\n     * increase of 1 zooms in to a quarter of the viewed area (half the\n     * width and height). Defaults to fitting to the map bounds.\n     *\n     * In a `WebMercator` projection, a zoom level of 0 represents\n     * the world in a 256x256 pixel square. This is a common concept for WMS\n     * tiling software.\n     *\n     * @sample {highmaps} maps/mapview/center-zoom\n     *         Custom view of a world map\n     * @sample {highmaps} maps/mapview/get-view\n     *         Report the current view of a preprojected map\n     *\n     * @type   {number}\n     */\n    zoom: void 0,\n    /**\n     * Generic options for the placement and appearance of map insets like\n     * non-contiguous territories.\n     *\n     * @since        10.0.0\n     * @product      highmaps\n     * @optionparent mapView.insetOptions\n     */\n    insetOptions: {\n        /**\n         * The border color of the insets.\n         *\n         * @sample maps/mapview/insetoptions-border\n         *         Inset border options\n         *\n         * @type {Highcharts.ColorType}\n         */\n        borderColor: \"#cccccc\" /* Palette.neutralColor20 */,\n        /**\n         * The pixel border width of the insets.\n         *\n         * @sample maps/mapview/insetoptions-border\n         *         Inset border options\n         */\n        borderWidth: 1,\n        /**\n         * The padding of the insets. Can be either a number of pixels, a\n         * percentage string, or an array of either. If an array is given, it\n         * sets the top, right, bottom, left paddings respectively.\n         *\n         * @type {number|string|Array<number|string>}\n         */\n        padding: '10%',\n        /**\n         * What coordinate system the `field` and `borderPath` should relate to.\n         * If `plotBox`, they will be fixed to the plot box and responsively\n         * move in relation to the main map. If `mapBoundingBox`, they will be\n         * fixed to the map bounding box, which is constant and centered in\n         * different chart sizes and ratios.\n         *\n         * @validvalue [\"plotBox\", \"mapBoundingBox\"]\n         */\n        relativeTo: 'mapBoundingBox',\n        /**\n         * The individual MapView insets, typically used for non-contiguous\n         * areas of a country. Each item inherits from the generic\n         * `insetOptions`.\n         *\n         * Some of the TopoJSON files of the [Highcharts Map\n         * Collection](https://code.highcharts.com/mapdata/) include a property\n         * called `hc-recommended-mapview`, and some of these include insets. In\n         * order to override the recommended inset options, an inset option with\n         * a matching id can be applied, and it will be merged into the embedded\n         * settings.\n         *\n         * @sample      maps/mapview/insets-extended\n         *              Extending the embedded insets\n         * @sample      maps/mapview/insets-complete\n         *              Complete inset config from scratch\n         *\n         * @extends     mapView.insetOptions\n         * @type        Array<Object>\n         * @product     highmaps\n         * @apioption   mapView.insets\n         */\n        /**\n         * A geometry object of type `MultiLineString` defining the border path\n         * of the inset in terms of `units`. If undefined, a border is rendered\n         * around the `field` geometry. It is recommended that the `borderPath`\n         * partly follows the outline of the `field` in order to make pointer\n         * positioning consistent.\n         *\n         * @sample    maps/mapview/insets-complete\n         *            Complete inset config with `borderPath`\n         *\n         * @product   highmaps\n         * @type      {Object|undefined}\n         * @apioption mapView.insets.borderPath\n         */\n        /**\n         * A geometry object of type `Polygon` defining where in the chart the\n         * inset should be rendered, in terms of `units` and relative to the\n         * `relativeTo` setting. If a `borderPath` is omitted, a border is\n         * rendered around the field. If undefined, the inset is rendered in the\n         * full plot area.\n         *\n         * @sample    maps/mapview/insets-extended\n         *            Border path emitted, field is rendered\n         *\n         * @product   highmaps\n         * @type      {object|undefined}\n         * @apioption mapView.insets.field\n         */\n        /**\n         * A geometry object of type `Polygon` encircling the shapes that should\n         * be rendered in the inset, in terms of geographic coordinates.\n         * Geometries within this geometry are removed from the default map view\n         * and rendered in the inset.\n         *\n         * @sample    maps/mapview/insets-complete\n         *            Complete inset config with `geoBounds`\n         *\n         * @product   highmaps\n         * @type      {object}\n         * @apioption mapView.insets.geoBounds\n         */\n        /**\n         * The id of the inset, used for internal reference.\n         *\n         * @sample    maps/mapview/insets-extended\n         *            Extending recommended insets by id\n         *\n         * @product   highmaps\n         * @type      {string}\n         * @apioption mapView.insets.id\n         */\n        /**\n         * The projection options for the inset.\n         *\n         * @product   highmaps\n         * @type      {Object}\n         * @extends   mapView.projection\n         * @apioption mapView.insets.projection\n         */\n        /**\n         * What units to use for the `field` and `borderPath` geometries. If\n         * `percent` (default), they relate to the box given in `relativeTo`. If\n         * `pixels`, they are absolute values.\n         *\n         * @validvalue [\"percent\", \"pixels\"]\n         */\n        units: 'percent'\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Maps_MapViewDefaults = (MapViewDefaults);\n\n;// external [\"../highcharts.js\",\"default\",\"Templating\"]\nconst external_highcharts_src_js_default_Templating_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Templating;\nvar external_highcharts_src_js_default_Templating_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Templating_namespaceObject);\n;// ./code/es-modules/Maps/GeoJSONComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { win } = (external_highcharts_src_js_default_default());\n\nconst { format } = (external_highcharts_src_js_default_Templating_default());\n\nconst { error, extend: GeoJSONComposition_extend, merge: GeoJSONComposition_merge, wrap: GeoJSONComposition_wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar GeoJSONComposition;\n(function (GeoJSONComposition) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Deprecated. Use `MapView.lonLatToProjectedUnits` instead.\n     *\n     * @deprecated\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.Chart#fromLatLonToPoint\n     *\n     * @param {Highcharts.MapLonLatObject} lonLat\n     *        Coordinates.\n     *\n     * @return {Highcharts.ProjectedXY}\n     * X and Y coordinates in terms of projected values\n     */\n    function chartFromLatLonToPoint(lonLat) {\n        return this.mapView && this.mapView.lonLatToProjectedUnits(lonLat);\n    }\n    /**\n     * Deprecated. Use `MapView.projectedUnitsToLonLat` instead.\n     *\n     * @deprecated\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.Chart#fromPointToLatLon\n     *\n     * @param {Highcharts.Point|Highcharts.ProjectedXY} point\n     *        A `Point` instance or anything containing `x` and `y` properties\n     *        with numeric values.\n     *\n     * @return {Highcharts.MapLonLatObject|undefined}\n     * An object with `lat` and `lon` properties.\n     */\n    function chartFromPointToLatLon(point) {\n        return this.mapView && this.mapView.projectedUnitsToLonLat(point);\n    }\n    /**\n     * Highcharts Maps only. Get point from latitude and longitude using\n     * specified transform definition.\n     *\n     * @requires modules/map\n     *\n     * @sample maps/series/latlon-transform/\n     *         Use specific transformation for lat/lon\n     *\n     * @function Highcharts.Chart#transformFromLatLon\n     *\n     * @param {Highcharts.MapLonLatObject} latLon\n     *        A latitude/longitude object.\n     *\n     * @param {*} transform\n     *        The transform definition to use as explained in the\n     *        {@link https://www.highcharts.com/docs/maps/latlon|documentation}.\n     *\n     * @return {ProjectedXY}\n     * An object with `x` and `y` properties.\n     */\n    function chartTransformFromLatLon(latLon, transform) {\n        /**\n         * Allows to manually load the proj4 library from Highcharts options\n         * instead of the `window`.\n         * In case of loading the library from a `script` tag,\n         * this option is not needed, it will be loaded from there by default.\n         *\n         * @type      {Function}\n         * @product   highmaps\n         * @apioption chart.proj4\n         */\n        const proj4 = this.options.chart.proj4 || win.proj4;\n        if (!proj4) {\n            error(21, false, this);\n            return;\n        }\n        const { jsonmarginX = 0, jsonmarginY = 0, jsonres = 1, scale = 1, xoffset = 0, xpan = 0, yoffset = 0, ypan = 0 } = transform;\n        const projected = proj4(transform.crs, [latLon.lon, latLon.lat]), cosAngle = transform.cosAngle ||\n            (transform.rotation && Math.cos(transform.rotation)), sinAngle = transform.sinAngle ||\n            (transform.rotation && Math.sin(transform.rotation)), rotated = transform.rotation ? [\n            projected[0] * cosAngle + projected[1] * sinAngle,\n            -projected[0] * sinAngle + projected[1] * cosAngle\n        ] : projected;\n        return {\n            x: ((rotated[0] - xoffset) * scale + xpan) * jsonres + jsonmarginX,\n            y: -(((yoffset - rotated[1]) * scale + ypan) * jsonres - jsonmarginY)\n        };\n    }\n    /**\n     * Highcharts Maps only. Get latLon from point using specified transform\n     * definition. The method returns an object with the numeric properties\n     * `lat` and `lon`.\n     *\n     * @requires modules/map\n     *\n     * @sample maps/series/latlon-transform/\n     *         Use specific transformation for lat/lon\n     *\n     * @function Highcharts.Chart#transformToLatLon\n     *\n     * @param {Highcharts.Point|Highcharts.ProjectedXY} point\n     *        A `Point` instance, or any object containing the properties `x`\n     *        and `y` with numeric values.\n     *\n     * @param {*} transform\n     *        The transform definition to use as explained in the\n     *        {@link https://www.highcharts.com/docs/maps/latlon|documentation}.\n     *\n     * @return {Highcharts.MapLonLatObject|undefined}\n     * An object with `lat` and `lon` properties.\n     */\n    function chartTransformToLatLon(point, transform) {\n        const proj4 = this.options.chart.proj4 || win.proj4;\n        if (!proj4) {\n            error(21, false, this);\n            return;\n        }\n        if (point.y === null) {\n            return;\n        }\n        const { jsonmarginX = 0, jsonmarginY = 0, jsonres = 1, scale = 1, xoffset = 0, xpan = 0, yoffset = 0, ypan = 0 } = transform;\n        const normalized = {\n            x: ((point.x - jsonmarginX) / jsonres - xpan) / scale + xoffset,\n            y: ((point.y - jsonmarginY) / jsonres + ypan) / scale + yoffset\n        }, cosAngle = transform.cosAngle ||\n            (transform.rotation && Math.cos(transform.rotation)), sinAngle = transform.sinAngle ||\n            (transform.rotation && Math.sin(transform.rotation)), \n        // Note: Inverted sinAngle to reverse rotation direction\n        projected = proj4(transform.crs, 'WGS84', transform.rotation ? {\n            x: normalized.x * cosAngle + normalized.y * -sinAngle,\n            y: normalized.x * sinAngle + normalized.y * cosAngle\n        } : normalized);\n        return { lat: projected.y, lon: projected.x };\n    }\n    /** @private */\n    function compose(ChartClass) {\n        const chartProto = ChartClass.prototype;\n        if (!chartProto.transformFromLatLon) {\n            chartProto.fromLatLonToPoint = chartFromLatLonToPoint;\n            chartProto.fromPointToLatLon = chartFromPointToLatLon;\n            chartProto.transformFromLatLon = chartTransformFromLatLon;\n            chartProto.transformToLatLon = chartTransformToLatLon;\n            GeoJSONComposition_wrap(chartProto, 'addCredits', wrapChartAddCredit);\n        }\n    }\n    GeoJSONComposition.compose = compose;\n    /**\n     * Highcharts Maps only. Restructure a GeoJSON or TopoJSON object in\n     * preparation to be read directly by the\n     * {@link https://api.highcharts.com/highmaps/plotOptions.series.mapData|series.mapData}\n     * option. The object will be broken down to fit a specific Highcharts type,\n     * either `map`, `mapline` or `mappoint`. Meta data in GeoJSON's properties\n     * object will be copied directly over to {@link Point.properties} in\n     * Highcharts Maps.\n     *\n     * @requires modules/map\n     *\n     * @sample maps/demo/geojson/ Simple areas\n     * @sample maps/demo/mapline-mappoint/ Multiple types\n     * @sample maps/series/mapdata-multiple/ Multiple map sources\n     *\n     * @function Highcharts.geojson\n     *\n     * @param {Highcharts.GeoJSON|Highcharts.TopoJSON} json\n     *        The GeoJSON or TopoJSON structure to parse, represented as a\n     *        JavaScript object.\n     *\n     * @param {string} [hType=map]\n     *        The Highcharts Maps series type to prepare for. Setting \"map\" will\n     *        return GeoJSON polygons and multipolygons. Setting \"mapline\" will\n     *        return GeoJSON linestrings and multilinestrings. Setting\n     *        \"mappoint\" will return GeoJSON points and multipoints.\n     *\n     *\n     * @return {Array<*>} An object ready for the `mapData` option.\n     */\n    function geojson(json, hType = 'map', series) {\n        const mapData = [];\n        const geojson = json.type === 'Topology' ? topo2geo(json) : json, features = geojson.features;\n        for (let i = 0, iEnd = features.length; i < iEnd; ++i) {\n            const feature = features[i], geometry = feature.geometry || {}, type = geometry.type, coordinates = geometry.coordinates, properties = feature.properties;\n            let pointOptions;\n            if ((hType === 'map' || hType === 'mapbubble') &&\n                (type === 'Polygon' || type === 'MultiPolygon')) {\n                if (coordinates.length) {\n                    pointOptions = { geometry: { coordinates, type } };\n                }\n            }\n            else if (hType === 'mapline' &&\n                (type === 'LineString' ||\n                    type === 'MultiLineString')) {\n                if (coordinates.length) {\n                    pointOptions = { geometry: { coordinates, type } };\n                }\n            }\n            else if (hType === 'mappoint' && type === 'Point') {\n                if (coordinates.length) {\n                    pointOptions = { geometry: { coordinates, type } };\n                }\n            }\n            if (pointOptions) {\n                const name = properties && (properties.name || properties.NAME), lon = properties && properties.lon, lat = properties && properties.lat;\n                mapData.push(GeoJSONComposition_extend(pointOptions, {\n                    lat: typeof lat === 'number' ? lat : void 0,\n                    lon: typeof lon === 'number' ? lon : void 0,\n                    name: typeof name === 'string' ? name : void 0,\n                    /**\n                     * In Highcharts Maps, when data is loaded from GeoJSON, the\n                     * GeoJSON item's properies are copied over here.\n                     *\n                     * @requires modules/map\n                     * @name Highcharts.Point#properties\n                     * @type {*}\n                     */\n                    properties\n                }));\n            }\n        }\n        // Create a credits text that includes map source, to be picked up in\n        // Chart.addCredits\n        if (series && geojson.copyrightShort) {\n            series.chart.mapCredits = format(series.chart.options.credits?.mapText, { geojson: geojson });\n            series.chart.mapCreditsFull = format(series.chart.options.credits?.mapTextFull, { geojson: geojson });\n        }\n        return mapData;\n    }\n    GeoJSONComposition.geojson = geojson;\n    /**\n     * Convert a TopoJSON topology to GeoJSON. By default the first object is\n     * handled.\n     * Based on https://github.com/topojson/topojson-specification\n     */\n    function topo2geo(topology, objectName) {\n        // Decode first object/feature as default\n        if (!objectName) {\n            objectName = Object.keys(topology.objects)[0];\n        }\n        const obj = topology.objects[objectName];\n        // Already decoded with the same title => return cache\n        if (obj['hc-decoded-geojson'] &&\n            obj['hc-decoded-geojson'].title === topology.title) {\n            return obj['hc-decoded-geojson'];\n        }\n        // Do the initial transform\n        let arcsArray = topology.arcs;\n        if (topology.transform) {\n            const arcs = topology.arcs, { scale, translate } = topology.transform;\n            let positionArray, x, y;\n            arcsArray = [];\n            for (let i = 0, iEnd = arcs.length; i < iEnd; ++i) {\n                const positions = arcs[i];\n                arcsArray.push(positionArray = []);\n                x = 0;\n                y = 0;\n                for (let j = 0, jEnd = positions.length; j < jEnd; ++j) {\n                    positionArray.push([\n                        (x += positions[j][0]) * scale[0] + translate[0],\n                        (y += positions[j][1]) * scale[1] + translate[1]\n                    ]);\n                }\n            }\n        }\n        // Recurse down any depth of multi-dimensional arrays of arcs and insert\n        // the coordinates\n        const arcsToCoordinates = (arcs) => {\n            if (typeof arcs[0] === 'number') {\n                return arcs.reduce((coordinates, arcNo, i) => {\n                    let arc = arcNo < 0 ? arcsArray[~arcNo] : arcsArray[arcNo];\n                    // The first point of an arc is always identical to the last\n                    // point of the previes arc, so slice it off to save further\n                    // processing.\n                    if (arcNo < 0) {\n                        arc = arc.slice(0, i === 0 ? arc.length : arc.length - 1);\n                        arc.reverse();\n                    }\n                    else if (i) {\n                        arc = arc.slice(1);\n                    }\n                    return coordinates.concat(arc);\n                }, []);\n            }\n            return arcs.map(arcsToCoordinates);\n        };\n        const geometries = obj.geometries, features = [];\n        for (let i = 0, iEnd = geometries.length; i < iEnd; ++i) {\n            features.push({\n                type: 'Feature',\n                properties: geometries[i].properties,\n                geometry: {\n                    type: geometries[i].type,\n                    coordinates: geometries[i].coordinates ||\n                        arcsToCoordinates(geometries[i].arcs)\n                }\n            });\n        }\n        const geojson = {\n            type: 'FeatureCollection',\n            copyright: topology.copyright,\n            copyrightShort: topology.copyrightShort,\n            copyrightUrl: topology.copyrightUrl,\n            features,\n            'hc-recommended-mapview': obj['hc-recommended-mapview'],\n            bbox: topology.bbox,\n            title: topology.title\n        };\n        obj['hc-decoded-geojson'] = geojson;\n        return geojson;\n    }\n    GeoJSONComposition.topo2geo = topo2geo;\n    /**\n     * Override addCredits to include map source by default.\n     * @private\n     */\n    function wrapChartAddCredit(proceed, credits) {\n        credits = GeoJSONComposition_merge(true, this.options.credits, credits);\n        proceed.call(this, credits);\n        // Add full map credits to hover\n        if (this.credits && this.mapCreditsFull) {\n            this.credits.attr({\n                title: this.mapCreditsFull\n            });\n        }\n    }\n})(GeoJSONComposition || (GeoJSONComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Maps_GeoJSONComposition = (GeoJSONComposition);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Represents the loose structure of a geographic JSON file.\n *\n * @interface Highcharts.GeoJSON\n */ /**\n* Full copyright note of the geographic data.\n* @name Highcharts.GeoJSON#copyright\n* @type {string|undefined}\n*/ /**\n* Short copyright note of the geographic data suitable for watermarks.\n* @name Highcharts.GeoJSON#copyrightShort\n* @type {string|undefined}\n*/ /**\n* Additional meta information based on the coordinate reference system.\n* @name Highcharts.GeoJSON#crs\n* @type {Highcharts.Dictionary<any>|undefined}\n*/ /**\n* Data sets of geographic features.\n* @name Highcharts.GeoJSON#features\n* @type {Array<Highcharts.GeoJSONFeature>}\n*/ /**\n* Map projections and transformations to be used when calculating between\n* lat/lon and chart values. Required for lat/lon support on maps. Allows\n* resizing, rotating, and moving portions of a map within its projected\n* coordinate system while still retaining lat/lon support. If using lat/lon\n* on a portion of the map that does not match a `hitZone`, the definition with\n* the key `default` is used.\n* @name Highcharts.GeoJSON#hc-transform\n* @type {Highcharts.Dictionary<Highcharts.GeoJSONTranslation>|undefined}\n*/ /**\n* Title of the geographic data.\n* @name Highcharts.GeoJSON#title\n* @type {string|undefined}\n*/ /**\n* Type of the geographic data. Type of an optimized map collection is\n* `FeatureCollection`.\n* @name Highcharts.GeoJSON#type\n* @type {string|undefined}\n*/ /**\n* Version of the geographic data.\n* @name Highcharts.GeoJSON#version\n* @type {string|undefined}\n*/\n/**\n * Data set of a geographic feature.\n * @interface Highcharts.GeoJSONFeature\n * @extends Highcharts.Dictionary<*>\n */ /**\n* Data type of the geographic feature.\n* @name Highcharts.GeoJSONFeature#type\n* @type {string}\n*/\n/**\n * Describes the map projection and transformations applied to a portion of\n * a map.\n * @interface Highcharts.GeoJSONTranslation\n */ /**\n* The coordinate reference system used to generate this portion of the map.\n* @name Highcharts.GeoJSONTranslation#crs\n* @type {string}\n*/ /**\n* Define the portion of the map that this definition applies to. Defined as a\n* GeoJSON polygon feature object, with `type` and `coordinates` properties.\n* @name Highcharts.GeoJSONTranslation#hitZone\n* @type {Highcharts.Dictionary<*>|undefined}\n*/ /**\n* Property for internal use for maps generated by Highsoft.\n* @name Highcharts.GeoJSONTranslation#jsonmarginX\n* @type {number|undefined}\n*/ /**\n* Property for internal use for maps generated by Highsoft.\n* @name Highcharts.GeoJSONTranslation#jsonmarginY\n* @type {number|undefined}\n*/ /**\n* Property for internal use for maps generated by Highsoft.\n* @name Highcharts.GeoJSONTranslation#jsonres\n* @type {number|undefined}\n*/ /**\n* Specifies clockwise rotation of the coordinates after the projection, but\n* before scaling and panning. Defined in radians, relative to the coordinate\n* system origin.\n* @name Highcharts.GeoJSONTranslation#rotation\n* @type {number|undefined}\n*/ /**\n* The scaling factor applied to the projected coordinates.\n* @name Highcharts.GeoJSONTranslation#scale\n* @type {number|undefined}\n*/ /**\n* Property for internal use for maps generated by Highsoft.\n* @name Highcharts.GeoJSONTranslation#xoffset\n* @type {number|undefined}\n*/ /**\n* X offset of projected coordinates after scaling.\n* @name Highcharts.GeoJSONTranslation#xpan\n* @type {number|undefined}\n*/ /**\n* Property for internal use for maps generated by Highsoft.\n* @name Highcharts.GeoJSONTranslation#yoffset\n* @type {number|undefined}\n*/ /**\n* Y offset of projected coordinates after scaling.\n* @name Highcharts.GeoJSONTranslation#ypan\n* @type {number|undefined}\n*/\n/**\n * Result object of a map transformation.\n *\n * @interface Highcharts.ProjectedXY\n */ /**\n* X coordinate in projected units.\n* @name Highcharts.ProjectedXY#x\n* @type {number}\n*/ /**\n* Y coordinate in projected units\n* @name Highcharts.ProjectedXY#y\n* @type {number}\n*/\n/**\n * A latitude/longitude object.\n *\n * @interface Highcharts.MapLonLatObject\n */ /**\n* The latitude.\n* @name Highcharts.MapLonLatObject#lat\n* @type {number}\n*/ /**\n* The longitude.\n* @name Highcharts.MapLonLatObject#lon\n* @type {number}\n*/\n/**\n * An array of longitude, latitude.\n *\n * @typedef {Array<number>} Highcharts.LonLatArray\n */\n/**\n * An array of GeoJSON or TopoJSON objects or strings used as map data for\n * series.\n *\n * @typedef {Array<*>|GeoJSON|TopoJSON|string} Highcharts.MapDataType\n */\n/**\n * A TopoJSON object, see description on the\n * [project's GitHub page](https://github.com/topojson/topojson).\n *\n * @typedef {Object} Highcharts.TopoJSON\n */\n''; // Detach doclets above\n\n;// ./code/es-modules/Core/Geometry/GeometryUtilities.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Namespace\n *\n * */\nvar GeometryUtilities;\n(function (GeometryUtilities) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculates the center between a list of points.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.PositionObject>} points\n     * A list of points to calculate the center of.\n     *\n     * @return {Highcharts.PositionObject}\n     * Calculated center\n     */\n    function getCenterOfPoints(points) {\n        const sum = points.reduce((sum, point) => {\n            sum.x += point.x;\n            sum.y += point.y;\n            return sum;\n        }, { x: 0, y: 0 });\n        return {\n            x: sum.x / points.length,\n            y: sum.y / points.length\n        };\n    }\n    GeometryUtilities.getCenterOfPoints = getCenterOfPoints;\n    /**\n     * Calculates the distance between two points based on their x and y\n     * coordinates.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} p1\n     * The x and y coordinates of the first point.\n     *\n     * @param {Highcharts.PositionObject} p2\n     * The x and y coordinates of the second point.\n     *\n     * @return {number}\n     * Returns the distance between the points.\n     */\n    function getDistanceBetweenPoints(p1, p2) {\n        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n    }\n    GeometryUtilities.getDistanceBetweenPoints = getDistanceBetweenPoints;\n    /**\n     * Calculates the angle between two points.\n     * @todo add unit tests.\n     * @private\n     * @param {Highcharts.PositionObject} p1 The first point.\n     * @param {Highcharts.PositionObject} p2 The second point.\n     * @return {number} Returns the angle in radians.\n     */\n    function getAngleBetweenPoints(p1, p2) {\n        return Math.atan2(p2.x - p1.x, p2.y - p1.y);\n    }\n    GeometryUtilities.getAngleBetweenPoints = getAngleBetweenPoints;\n    /**\n     * Test for point in polygon. Polygon defined as array of [x,y] points.\n     * @private\n     * @param {PositionObject} point The point potentially within a polygon.\n     * @param {Array<Array<number>>} polygon The polygon potentially containing the point.\n     */\n    function pointInPolygon({ x, y }, polygon) {\n        const len = polygon.length;\n        let i, j, inside = false;\n        for (i = 0, j = len - 1; i < len; j = i++) {\n            const [x1, y1] = polygon[i], [x2, y2] = polygon[j];\n            if (y1 > y !== y2 > y &&\n                (x < (x2 - x1) *\n                    (y - y1) /\n                    (y2 - y1) +\n                    x1)) {\n                inside = !inside;\n            }\n        }\n        return inside;\n    }\n    GeometryUtilities.pointInPolygon = pointInPolygon;\n})(GeometryUtilities || (GeometryUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Geometry_GeometryUtilities = (GeometryUtilities);\n\n;// ./code/es-modules/Core/Geometry/PolygonClip.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Simple line string clipping. Clip to bounds and insert intersection points.\n * @private\n */\nfunction clipLineString(line, boundsPolygon) {\n    const ret = [], l = clipPolygon(line, boundsPolygon, false);\n    for (let i = 1; i < l.length; i++) {\n        // Insert gap where two intersections follow each other\n        if (l[i].isIntersection && l[i - 1].isIntersection) {\n            ret.push(l.splice(0, i));\n            i = 0;\n        }\n        // Push the rest\n        if (i === l.length - 1) {\n            ret.push(l);\n        }\n    }\n    return ret;\n}\n/**\n * Clip a polygon to another polygon using the Sutherland/Hodgman algorithm.\n * @private\n */\nfunction clipPolygon(subjectPolygon, boundsPolygon, closed = true) {\n    let clipEdge1 = boundsPolygon[boundsPolygon.length - 1], clipEdge2, prevPoint, currentPoint, outputList = subjectPolygon;\n    for (let j = 0; j < boundsPolygon.length; j++) {\n        const inputList = outputList;\n        clipEdge2 = boundsPolygon[j];\n        outputList = [];\n        prevPoint = closed ?\n            // Polygon, wrap around\n            inputList[inputList.length - 1] :\n            // Open line string, don't wrap\n            inputList[0];\n        for (let i = 0; i < inputList.length; i++) {\n            currentPoint = inputList[i];\n            if (isInside(clipEdge1, clipEdge2, currentPoint)) {\n                if (!isInside(clipEdge1, clipEdge2, prevPoint)) {\n                    outputList.push(intersection(clipEdge1, clipEdge2, prevPoint, currentPoint));\n                }\n                outputList.push(currentPoint);\n            }\n            else if (isInside(clipEdge1, clipEdge2, prevPoint)) {\n                outputList.push(intersection(clipEdge1, clipEdge2, prevPoint, currentPoint));\n            }\n            prevPoint = currentPoint;\n        }\n        clipEdge1 = clipEdge2;\n    }\n    return outputList;\n}\n/** @private */\nfunction isInside(clipEdge1, clipEdge2, p) {\n    return ((clipEdge2[0] - clipEdge1[0]) * (p[1] - clipEdge1[1]) >\n        (clipEdge2[1] - clipEdge1[1]) * (p[0] - clipEdge1[0]));\n}\n/** @private */\nfunction intersection(clipEdge1, clipEdge2, prevPoint, currentPoint) {\n    const dc = [\n        clipEdge1[0] - clipEdge2[0],\n        clipEdge1[1] - clipEdge2[1]\n    ], dp = [\n        prevPoint[0] - currentPoint[0],\n        prevPoint[1] - currentPoint[1]\n    ], n1 = clipEdge1[0] * clipEdge2[1] - clipEdge1[1] * clipEdge2[0], n2 = prevPoint[0] * currentPoint[1] - prevPoint[1] * currentPoint[0], n3 = 1 / (dc[0] * dp[1] - dc[1] * dp[0]), intersection = [\n        (n1 * dp[0] - n2 * dc[0]) * n3,\n        (n1 * dp[1] - n2 * dc[1]) * n3\n    ];\n    intersection.isIntersection = true;\n    return intersection;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PolygonClip = {\n    clipLineString,\n    clipPolygon\n};\n/* harmony default export */ const Geometry_PolygonClip = (PolygonClip);\n\n;// ./code/es-modules/Maps/Projections/LambertConformalConic.js\n/* *\n * Lambert Conformal Conic projection\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst sign = Math.sign ||\n    ((n) => (n === 0 ? 0 : n > 0 ? 1 : -1)), scale = 63.78137, LambertConformalConic_deg2rad = Math.PI / 180, halfPI = Math.PI / 2, eps10 = 1e-6, tany = (y) => Math.tan((halfPI + y) / 2);\n/* *\n *\n *  Class\n *\n * */\nclass LambertConformalConic {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options) {\n        const parallels = (options.parallels || [])\n            .map((n) => n * LambertConformalConic_deg2rad), lat1 = parallels[0] || 0, lat2 = parallels[1] ?? lat1, cosLat1 = Math.cos(lat1);\n        if (typeof options.projectedBounds === 'object') {\n            this.projectedBounds = options.projectedBounds;\n        }\n        // Apply the global variables\n        let n = lat1 === lat2 ?\n            Math.sin(lat1) :\n            Math.log(cosLat1 / Math.cos(lat2)) / Math.log(tany(lat2) / tany(lat1));\n        if (Math.abs(n) < 1e-10) {\n            n = (sign(n) || 1) * 1e-10;\n        }\n        this.n = n;\n        this.c = cosLat1 * Math.pow(tany(lat1), n) / n;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    forward(lonLat) {\n        const { c, n, projectedBounds } = this, lon = lonLat[0] * LambertConformalConic_deg2rad;\n        let lat = lonLat[1] * LambertConformalConic_deg2rad;\n        if (c > 0) {\n            if (lat < -halfPI + eps10) {\n                lat = -halfPI + eps10;\n            }\n        }\n        else {\n            if (lat > halfPI - eps10) {\n                lat = halfPI - eps10;\n            }\n        }\n        const r = c / Math.pow(tany(lat), n), x = r * Math.sin(n * lon) * scale, y = (c - r * Math.cos(n * lon)) * scale, xy = [x, y];\n        if (projectedBounds && (x < projectedBounds.x1 ||\n            x > projectedBounds.x2 ||\n            y < projectedBounds.y1 ||\n            y > projectedBounds.y2)) {\n            xy.outside = true;\n        }\n        return xy;\n    }\n    inverse(xy) {\n        const { c, n } = this, x = xy[0] / scale, y = xy[1] / scale, cy = c - y, rho = sign(n) * Math.sqrt(x * x + cy * cy);\n        let l = Math.atan2(x, Math.abs(cy)) * sign(cy);\n        if (cy * n < 0) {\n            l -= Math.PI * sign(x) * sign(cy);\n        }\n        return [\n            (l / n) / LambertConformalConic_deg2rad,\n            (2 * Math.atan(Math.pow(c / rho, 1 / n)) - halfPI) / LambertConformalConic_deg2rad\n        ];\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Projections_LambertConformalConic = (LambertConformalConic);\n\n;// ./code/es-modules/Maps/Projections/EqualEarth.js\n/* *\n *\n * Equal Earth projection, an equal-area projection designed to minimize\n * distortion and remain pleasing to the eye.\n *\n * Invented by Bojan Šavrič, Bernhard Jenny, and Tom Patterson in 2018. It is\n * inspired by the widely used Robinson projection.\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst A1 = 1.340264, A2 = -0.081106, A3 = 0.000893, A4 = 0.003796, M = Math.sqrt(3) / 2.0, EqualEarth_scale = 74.03120656864502;\n/* *\n *\n *  Class\n *\n * */\nclass EqualEarth {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.bounds = {\n            x1: -200.37508342789243,\n            x2: 200.37508342789243,\n            y1: -97.52595454902263,\n            y2: 97.52595454902263\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    forward(lonLat) {\n        const d = Math.PI / 180, paramLat = Math.asin(M * Math.sin(lonLat[1] * d)), paramLatSq = paramLat * paramLat, paramLatPow6 = paramLatSq * paramLatSq * paramLatSq;\n        const x = lonLat[0] * d * Math.cos(paramLat) * EqualEarth_scale /\n            (M * (A1 +\n                3 * A2 * paramLatSq +\n                paramLatPow6 * (7 * A3 + 9 * A4 * paramLatSq))), y = paramLat * EqualEarth_scale * (A1 + A2 * paramLatSq + paramLatPow6 * (A3 + A4 * paramLatSq));\n        return [x, y];\n    }\n    inverse(xy) {\n        const x = xy[0] / EqualEarth_scale, y = xy[1] / EqualEarth_scale, d = 180 / Math.PI, epsilon = 1e-9;\n        let paramLat = y, paramLatSq, paramLatPow6, fy, fpy, dlat;\n        for (let i = 0; i < 12; ++i) {\n            paramLatSq = paramLat * paramLat;\n            paramLatPow6 = paramLatSq * paramLatSq * paramLatSq;\n            fy = paramLat * (A1 + A2 * paramLatSq + paramLatPow6 * (A3 + A4 * paramLatSq)) - y;\n            fpy = A1 + 3 * A2 * paramLatSq + paramLatPow6 * (7 * A3 + 9 * A4 * paramLatSq);\n            paramLat -= dlat = fy / fpy;\n            if (Math.abs(dlat) < epsilon) {\n                break;\n            }\n        }\n        paramLatSq = paramLat * paramLat;\n        paramLatPow6 = paramLatSq * paramLatSq * paramLatSq;\n        const lon = d * M * x * (A1 + 3 * A2 * paramLatSq + paramLatPow6 *\n            (7 * A3 + 9 * A4 * paramLatSq)) / Math.cos(paramLat), lat = d * Math.asin(Math.sin(paramLat) / M);\n        // If lons are beyond the border of a map -> resolve via break\n        if (Math.abs(lon) > 180) {\n            return [NaN, NaN];\n        }\n        return [lon, lat];\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Projections_EqualEarth = (EqualEarth);\n\n;// ./code/es-modules/Maps/Projections/Miller.js\n/* *\n * Miller projection\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst quarterPI = Math.PI / 4, Miller_deg2rad = Math.PI / 180, Miller_scale = 63.78137;\n/* *\n *\n *  Class\n *\n * */\nclass Miller {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.bounds = {\n            x1: -200.37508342789243,\n            x2: 200.37508342789243,\n            y1: -146.91480769173063,\n            y2: 146.91480769173063\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    forward(lonLat) {\n        return [\n            lonLat[0] * Miller_deg2rad * Miller_scale,\n            1.25 * Miller_scale * Math.log(Math.tan(quarterPI + 0.4 * lonLat[1] * Miller_deg2rad))\n        ];\n    }\n    inverse(xy) {\n        return [\n            (xy[0] / Miller_scale) / Miller_deg2rad,\n            2.5 * (Math.atan(Math.exp(0.8 * (xy[1] / Miller_scale))) - quarterPI) / Miller_deg2rad\n        ];\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Projections_Miller = (Miller);\n\n;// ./code/es-modules/Maps/Projections/Orthographic.js\n/* *\n * Orthographic projection\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst Orthographic_deg2rad = Math.PI / 180, Orthographic_scale = 63.78460826781007;\n/* *\n *\n *  Class\n *\n * */\nclass Orthographic {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.antimeridianCutting = false;\n        this.bounds = {\n            x1: -Orthographic_scale,\n            x2: Orthographic_scale,\n            y1: -Orthographic_scale,\n            y2: Orthographic_scale\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    forward(lonLat) {\n        const lonDeg = lonLat[0], latDeg = lonLat[1], lat = latDeg * Orthographic_deg2rad, xy = [\n            Math.cos(lat) * Math.sin(lonDeg * Orthographic_deg2rad) * Orthographic_scale,\n            Math.sin(lat) * Orthographic_scale\n        ];\n        if (lonDeg < -90 || lonDeg > 90) {\n            xy.outside = true;\n        }\n        return xy;\n    }\n    inverse(xy) {\n        const x = xy[0] / Orthographic_scale, y = xy[1] / Orthographic_scale, z = Math.sqrt(x * x + y * y), c = Math.asin(z), cSin = Math.sin(c), cCos = Math.cos(c);\n        return [\n            Math.atan2(x * cSin, z * cCos) / Orthographic_deg2rad,\n            Math.asin(z && y * cSin / z) / Orthographic_deg2rad\n        ];\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Projections_Orthographic = (Orthographic);\n\n;// ./code/es-modules/Maps/Projections/WebMercator.js\n/* *\n * Web Mercator projection, used for most online map tile services\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst r = 63.78137, WebMercator_deg2rad = Math.PI / 180;\n/* *\n *\n *  Class\n *\n * */\nclass WebMercator {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.bounds = {\n            x1: -200.37508342789243,\n            x2: 200.37508342789243,\n            y1: -200.3750834278071,\n            y2: 200.3750834278071\n        };\n        this.maxLatitude = 85.0511287798; // The latitude that defines a square\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    forward(lonLat) {\n        const sinLat = Math.sin(lonLat[1] * WebMercator_deg2rad), xy = [\n            r * lonLat[0] * WebMercator_deg2rad,\n            r * Math.log((1 + sinLat) / (1 - sinLat)) / 2\n        ];\n        if (Math.abs(lonLat[1]) > this.maxLatitude) {\n            xy.outside = true;\n        }\n        return xy;\n    }\n    inverse(xy) {\n        return [\n            xy[0] / (r * WebMercator_deg2rad),\n            (2 * Math.atan(Math.exp(xy[1] / r)) - (Math.PI / 2)) / WebMercator_deg2rad\n        ];\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Projections_WebMercator = (WebMercator);\n\n;// ./code/es-modules/Maps/Projections/ProjectionRegistry.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\n\n/* *\n *\n *  Constants\n *\n * */\nconst projectionRegistry = {\n    EqualEarth: Projections_EqualEarth,\n    LambertConformalConic: Projections_LambertConformalConic,\n    Miller: Projections_Miller,\n    Orthographic: Projections_Orthographic,\n    WebMercator: Projections_WebMercator\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ProjectionRegistry = (projectionRegistry);\n\n;// ./code/es-modules/Maps/Projection.js\n/* *\n *\n *  (c) 2021 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { clipLineString: Projection_clipLineString, clipPolygon: Projection_clipPolygon } = Geometry_PolygonClip;\n\n\nconst { clamp, erase } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst Projection_deg2rad = Math.PI * 2 / 360, \n// Safe padding on either side of the antimeridian to avoid points being\n// projected to the wrong side of the plane\nfloatCorrection = 0.000001;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Keep longitude within -180 and 180. This is faster than using the modulo\n * operator, and preserves the distinction between -180 and 180.\n * @private\n */\nconst wrapLon = (lon) => {\n    // Replacing the if's with while would increase the range, but make it prone\n    // to crashes on bad data\n    if (lon < -180) {\n        lon += 360;\n    }\n    if (lon > 180) {\n        lon -= 360;\n    }\n    return lon;\n};\n/**\n * Calculate the haversine of an angle.\n * @private\n */\nconst hav = (radians) => (1 - Math.cos(radians)) / 2;\n/**\n* Calculate the haversine of an angle from two coordinates.\n* @private\n*/\nconst havFromCoords = (point1, point2) => {\n    const cos = Math.cos, lat1 = point1[1] * Projection_deg2rad, lon1 = point1[0] * Projection_deg2rad, lat2 = point2[1] * Projection_deg2rad, lon2 = point2[0] * Projection_deg2rad, deltaLat = lat2 - lat1, deltaLon = lon2 - lon1, havFromCoords = hav(deltaLat) + cos(lat1) * cos(lat2) * hav(deltaLon);\n    return havFromCoords;\n};\n/* *\n *\n *  Class\n *\n * */\nclass Projection {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Add a projection definition to the registry, accessible by its `name`.\n     * @private\n     */\n    static add(name, definition) {\n        Projection.registry[name] = definition;\n    }\n    /**\n     * Calculate the distance in meters between two given coordinates.\n     * @private\n     */\n    static distance(point1, point2) {\n        const { atan2, sqrt } = Math, hav = havFromCoords(point1, point2), angularDistance = 2 * atan2(sqrt(hav), sqrt(1 - hav)), distance = angularDistance * 6371e3;\n        return distance;\n    }\n    /**\n     * Calculate the geodesic line string between two given coordinates.\n     * @private\n     */\n    static geodesic(point1, point2, inclusive, stepDistance = 500000) {\n        const { atan2, cos, sin, sqrt } = Math, distance = Projection.distance, lat1 = point1[1] * Projection_deg2rad, lon1 = point1[0] * Projection_deg2rad, lat2 = point2[1] * Projection_deg2rad, lon2 = point2[0] * Projection_deg2rad, cosLat1CosLon1 = cos(lat1) * cos(lon1), cosLat2CosLon2 = cos(lat2) * cos(lon2), cosLat1SinLon1 = cos(lat1) * sin(lon1), cosLat2SinLon2 = cos(lat2) * sin(lon2), sinLat1 = sin(lat1), sinLat2 = sin(lat2), pointDistance = distance(point1, point2), angDistance = pointDistance / 6371e3, sinAng = sin(angDistance), jumps = Math.round(pointDistance / stepDistance), lineString = [];\n        if (inclusive) {\n            lineString.push(point1);\n        }\n        if (jumps > 1) {\n            const step = 1 / jumps;\n            for (let fraction = step; fraction < 0.999; // Account for float errors\n             fraction += step) {\n                // Add intermediate point to lineString\n                const A = sin((1 - fraction) * angDistance) / sinAng, B = sin(fraction * angDistance) / sinAng, x = A * cosLat1CosLon1 + B * cosLat2CosLon2, y = A * cosLat1SinLon1 + B * cosLat2SinLon2, z = A * sinLat1 + B * sinLat2, lat3 = atan2(z, sqrt(x * x + y * y)), lon3 = atan2(y, x);\n                lineString.push([lon3 / Projection_deg2rad, lat3 / Projection_deg2rad]);\n            }\n        }\n        if (inclusive) {\n            lineString.push(point2);\n        }\n        return lineString;\n    }\n    static insertGeodesics(poly) {\n        let i = poly.length - 1;\n        while (i--) {\n            // Distance in degrees, either in lon or lat. Avoid heavy\n            // calculation of true distance.\n            const roughDistance = Math.max(Math.abs(poly[i][0] - poly[i + 1][0]), Math.abs(poly[i][1] - poly[i + 1][1]));\n            if (roughDistance > 10) {\n                const geodesic = Projection.geodesic(poly[i], poly[i + 1]);\n                if (geodesic.length) {\n                    poly.splice(i + 1, 0, ...geodesic);\n                }\n            }\n        }\n    }\n    static toString(options) {\n        const { name, rotation } = options || {};\n        return [name, rotation && rotation.join(',')].join(';');\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options = {}) {\n        // Whether the chart has points, lines or polygons given as coordinates\n        // with positive up, as opposed to paths in the SVG plane with positive\n        // down.\n        this.hasCoordinates = false;\n        // Whether the chart has true projection as opposed to pre-projected geojson\n        // as in the legacy map collection.\n        this.hasGeoProjection = false;\n        this.maxLatitude = 90;\n        this.options = options;\n        const { name, projectedBounds, rotation } = options;\n        this.rotator = rotation ? this.getRotator(rotation) : void 0;\n        const ProjectionDefinition = name ? Projection.registry[name] : void 0;\n        if (ProjectionDefinition) {\n            this.def = new ProjectionDefinition(options);\n        }\n        const { def, rotator } = this;\n        if (def) {\n            this.maxLatitude = def.maxLatitude || 90;\n            this.hasGeoProjection = true;\n        }\n        if (rotator && def) {\n            this.forward = (lonLat) => def.forward(rotator.forward(lonLat));\n            this.inverse = (xy) => rotator.inverse(def.inverse(xy));\n        }\n        else if (def) {\n            this.forward = (lonLat) => def.forward(lonLat);\n            this.inverse = (xy) => def.inverse(xy);\n        }\n        else if (rotator) {\n            this.forward = rotator.forward;\n            this.inverse = rotator.inverse;\n        }\n        // Projected bounds/clipping\n        this.bounds = projectedBounds === 'world' ?\n            def && def.bounds :\n            projectedBounds;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    lineIntersectsBounds(line) {\n        const { x1, x2, y1, y2 } = this.bounds || {};\n        const getIntersect = (line, dim, val) => {\n            const [p1, p2] = line, otherDim = dim ? 0 : 1;\n            // Check if points are on either side of the line\n            if (typeof val === 'number' && p1[dim] >= val !== p2[dim] >= val) {\n                const fraction = ((val - p1[dim]) / (p2[dim] - p1[dim])), crossingVal = p1[otherDim] +\n                    fraction * (p2[otherDim] - p1[otherDim]);\n                return dim ? [crossingVal, val] : [val, crossingVal];\n            }\n        };\n        let intersection, ret = line[0];\n        if ((intersection = getIntersect(line, 0, x1))) {\n            ret = intersection;\n            // Assuming line[1] was originally outside, replace it with the\n            // intersection point so that the horizontal intersection will\n            // be correct.\n            line[1] = intersection;\n        }\n        else if ((intersection = getIntersect(line, 0, x2))) {\n            ret = intersection;\n            line[1] = intersection;\n        }\n        if ((intersection = getIntersect(line, 1, y1))) {\n            ret = intersection;\n        }\n        else if ((intersection = getIntersect(line, 1, y2))) {\n            ret = intersection;\n        }\n        return ret;\n    }\n    /**\n     * Take the rotation options and returns the appropriate projection\n     * functions.\n     * @private\n     */\n    getRotator(rotation) {\n        const deltaLambda = rotation[0] * Projection_deg2rad, deltaPhi = (rotation[1] || 0) * Projection_deg2rad, deltaGamma = (rotation[2] || 0) * Projection_deg2rad;\n        const cosDeltaPhi = Math.cos(deltaPhi), sinDeltaPhi = Math.sin(deltaPhi), cosDeltaGamma = Math.cos(deltaGamma), sinDeltaGamma = Math.sin(deltaGamma);\n        if (deltaLambda === 0 && deltaPhi === 0 && deltaGamma === 0) {\n            // Don't waste processing time\n            return;\n        }\n        return {\n            forward: (lonLat) => {\n                // Lambda (lon) rotation\n                const lon = lonLat[0] * Projection_deg2rad + deltaLambda;\n                // Phi (lat) and gamma rotation\n                const lat = lonLat[1] * Projection_deg2rad, cosLat = Math.cos(lat), x = Math.cos(lon) * cosLat, y = Math.sin(lon) * cosLat, sinLat = Math.sin(lat), k = sinLat * cosDeltaPhi + x * sinDeltaPhi;\n                return [\n                    Math.atan2(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - sinLat * sinDeltaPhi) / Projection_deg2rad,\n                    Math.asin(k * cosDeltaGamma + y * sinDeltaGamma) / Projection_deg2rad\n                ];\n            },\n            inverse: (rLonLat) => {\n                // Lambda (lon) unrotation\n                const lon = rLonLat[0] * Projection_deg2rad;\n                // Phi (lat) and gamma unrotation\n                const lat = rLonLat[1] * Projection_deg2rad, cosLat = Math.cos(lat), x = Math.cos(lon) * cosLat, y = Math.sin(lon) * cosLat, sinLat = Math.sin(lat), k = sinLat * cosDeltaGamma - y * sinDeltaGamma;\n                return [\n                    (Math.atan2(y * cosDeltaGamma + sinLat * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi) - deltaLambda) / Projection_deg2rad,\n                    Math.asin(k * cosDeltaPhi - x * sinDeltaPhi) / Projection_deg2rad\n                ];\n            }\n        };\n    }\n    /**\n     * Project a lonlat coordinate position to xy. Dynamically overridden when\n     * projection is set.\n     * @private\n     */\n    forward(lonLat) {\n        return lonLat;\n    }\n    /**\n     * Unproject an xy chart coordinate position to lonlat. Dynamically\n     * overridden when projection is set.\n     * @private\n     */\n    inverse(xy) {\n        return xy;\n    }\n    cutOnAntimeridian(poly, isPolygon) {\n        const antimeridian = 180, intersections = [];\n        const polygons = [poly];\n        for (let i = 0, iEnd = poly.length; i < iEnd; ++i) {\n            const lonLat = poly[i];\n            let previousLonLat = poly[i - 1];\n            if (!i) {\n                if (!isPolygon) {\n                    continue;\n                }\n                // Else, wrap to beginning\n                previousLonLat = poly[poly.length - 1];\n            }\n            const lon1 = previousLonLat[0], lon2 = lonLat[0];\n            if (\n            // Both points, after rotating for antimeridian, are on the far\n            // side of the Earth\n            (lon1 < -90 || lon1 > 90) &&\n                (lon2 < -90 || lon2 > 90) &&\n                // ... and on either side of the plane\n                (lon1 > 0) !== (lon2 > 0)) {\n                // Interpolate to the intersection latitude\n                const fraction = clamp((antimeridian - (lon1 + 360) % 360) /\n                    ((lon2 + 360) % 360 - (lon1 + 360) % 360), 0, 1), lat = (previousLonLat[1] +\n                    fraction * (lonLat[1] - previousLonLat[1]));\n                intersections.push({\n                    i,\n                    lat,\n                    direction: lon1 < 0 ? 1 : -1,\n                    previousLonLat,\n                    lonLat\n                });\n            }\n        }\n        let polarIntersection;\n        if (intersections.length) {\n            if (isPolygon) {\n                // Simplified use of the even-odd rule, if there is an odd\n                // amount of intersections between the polygon and the\n                // antimeridian, the pole is inside the polygon. Applies\n                // primarily to Antarctica.\n                if (intersections.length % 2 === 1) {\n                    polarIntersection = intersections.slice().sort((a, b) => Math.abs(b.lat) - Math.abs(a.lat))[0];\n                    erase(intersections, polarIntersection);\n                }\n                // Pull out slices of the polygon that is on the opposite side\n                // of the antimeridian compared to the starting point\n                let i = intersections.length - 2;\n                while (i >= 0) {\n                    const index = intersections[i].i;\n                    const lonPlus = wrapLon(antimeridian +\n                        intersections[i].direction * floatCorrection);\n                    const lonMinus = wrapLon(antimeridian -\n                        intersections[i].direction * floatCorrection);\n                    const slice = poly.splice(index, intersections[i + 1].i - index, \n                    // Add interpolated points close to the cut\n                    ...Projection.geodesic([lonPlus, intersections[i].lat], [lonPlus, intersections[i + 1].lat], true));\n                    // Add interpolated points close to the cut\n                    slice.push(...Projection.geodesic([lonMinus, intersections[i + 1].lat], [lonMinus, intersections[i].lat], true));\n                    polygons.push(slice);\n                    i -= 2;\n                }\n                // Insert dummy points close to the pole\n                if (polarIntersection) {\n                    for (let i = 0; i < polygons.length; i++) {\n                        const { direction, lat } = polarIntersection, poly = polygons[i], indexOf = poly.indexOf(polarIntersection.lonLat);\n                        if (indexOf > -1) {\n                            const polarLatitude = (lat < 0 ? -1 : 1) *\n                                this.maxLatitude;\n                            const lon1 = wrapLon(antimeridian +\n                                direction * floatCorrection);\n                            const lon2 = wrapLon(antimeridian -\n                                direction * floatCorrection);\n                            const polarSegment = Projection.geodesic([lon1, lat], [lon1, polarLatitude], true);\n                            // Circle around the pole point in order to make\n                            // polygon clipping right. Without this, Antarctica\n                            // would wrap the wrong way in an LLC projection\n                            // with parallels [30, 40].\n                            for (let lon = lon1 + 120 * direction; lon > -180 && lon < 180; lon += 120 * direction) {\n                                polarSegment.push([lon, polarLatitude]);\n                            }\n                            polarSegment.push(...Projection.geodesic([lon2, polarLatitude], [lon2, polarIntersection.lat], true));\n                            poly.splice(indexOf, 0, ...polarSegment);\n                            break;\n                        }\n                    }\n                }\n                // Map lines, not closed\n            }\n            else {\n                let i = intersections.length;\n                while (i--) {\n                    const index = intersections[i].i;\n                    const slice = poly.splice(index, poly.length, \n                    // Add interpolated point close to the cut\n                    [\n                        wrapLon(antimeridian +\n                            intersections[i].direction * floatCorrection),\n                        intersections[i].lat\n                    ]);\n                    // Add interpolated point close to the cut\n                    slice.unshift([\n                        wrapLon(antimeridian -\n                            intersections[i].direction * floatCorrection),\n                        intersections[i].lat\n                    ]);\n                    polygons.push(slice);\n                }\n            }\n        }\n        return polygons;\n    }\n    /**\n     * Take a GeoJSON geometry and return a translated SVGPath.\n     * @private\n     */\n    path(geometry) {\n        const { bounds, def, rotator } = this;\n        const antimeridian = 180;\n        const path = [];\n        const isPolygon = geometry.type === 'Polygon' ||\n            geometry.type === 'MultiPolygon';\n        // @todo: It doesn't really have to do with whether north is\n        // positive. It depends on whether the coordinates are\n        // pre-projected.\n        const hasGeoProjection = this.hasGeoProjection;\n        // Detect whether we need to do antimeridian cutting and clipping to\n        // bounds. The alternative (currently for Orthographic) is to apply a\n        // clip angle.\n        const projectingToPlane = !def || def.antimeridianCutting !== false;\n        // We need to rotate in a separate step before applying antimeridian\n        // cutting\n        const preclip = projectingToPlane ? rotator : void 0;\n        const postclip = projectingToPlane ? (def || this) : this;\n        let boundsPolygon;\n        if (bounds) {\n            boundsPolygon = [\n                [bounds.x1, bounds.y1],\n                [bounds.x2, bounds.y1],\n                [bounds.x2, bounds.y2],\n                [bounds.x1, bounds.y2]\n            ];\n        }\n        const addToPath = (polygon) => {\n            // Create a copy of the original coordinates. The copy applies a\n            // correction of points close to the antimeridian in order to\n            // prevent the points to be projected to the wrong side of the\n            // plane. Float errors in topojson or in the projection may cause\n            // that.\n            const poly = polygon.map((lonLat) => {\n                if (projectingToPlane) {\n                    if (preclip) {\n                        lonLat = preclip.forward(lonLat);\n                    }\n                    let lon = lonLat[0];\n                    if (Math.abs(lon - antimeridian) < floatCorrection) {\n                        if (lon < antimeridian) {\n                            lon = antimeridian - floatCorrection;\n                        }\n                        else {\n                            lon = antimeridian + floatCorrection;\n                        }\n                    }\n                    lonLat = [lon, lonLat[1]];\n                }\n                return lonLat;\n            });\n            let polygons = [poly];\n            if (hasGeoProjection) {\n                // Insert great circles into long straight lines\n                Projection.insertGeodesics(poly);\n                if (projectingToPlane) {\n                    polygons = this.cutOnAntimeridian(poly, isPolygon);\n                }\n            }\n            polygons.forEach((poly) => {\n                if (poly.length < 2) {\n                    return;\n                }\n                let movedTo = false;\n                let firstValidLonLat;\n                let lastValidLonLat;\n                let gap = false;\n                const pushToPath = (point) => {\n                    if (!movedTo) {\n                        path.push(['M', point[0], point[1]]);\n                        movedTo = true;\n                    }\n                    else {\n                        path.push(['L', point[0], point[1]]);\n                    }\n                };\n                let someOutside = false, someInside = false;\n                let points = poly.map((lonLat) => {\n                    const xy = postclip.forward(lonLat);\n                    if (xy.outside) {\n                        someOutside = true;\n                    }\n                    else {\n                        someInside = true;\n                    }\n                    // Mercator projects pole points to Infinity, and\n                    // clipPolygon is not able to handle it.\n                    if (xy[1] === Infinity) {\n                        xy[1] = 10e9;\n                    }\n                    else if (xy[1] === -Infinity) {\n                        xy[1] = -10e9;\n                    }\n                    return xy;\n                });\n                if (projectingToPlane) {\n                    // Wrap around in order for pointInPolygon to work\n                    if (isPolygon) {\n                        points.push(points[0]);\n                    }\n                    if (someOutside) {\n                        // All points are outside\n                        if (!someInside) {\n                            return;\n                        }\n                        // Some inside, some outside. Clip to the bounds.\n                        if (boundsPolygon) {\n                            // Polygons\n                            if (isPolygon) {\n                                points = Projection_clipPolygon(points, boundsPolygon);\n                                // Linestrings\n                            }\n                            else if (bounds) {\n                                Projection_clipLineString(points, boundsPolygon)\n                                    .forEach((points) => {\n                                    movedTo = false;\n                                    points.forEach(pushToPath);\n                                });\n                                return;\n                            }\n                        }\n                    }\n                    points.forEach(pushToPath);\n                    // For orthographic projection, or when a clipAngle applies\n                }\n                else {\n                    for (let i = 0; i < points.length; i++) {\n                        const lonLat = poly[i], point = points[i];\n                        if (!point.outside) {\n                            // In order to be able to interpolate if the first\n                            // or last point is invalid (on the far side of the\n                            // globe in an orthographic projection), we need to\n                            // push the first valid point to the end of the\n                            // polygon.\n                            if (isPolygon && !firstValidLonLat) {\n                                firstValidLonLat = lonLat;\n                                poly.push(lonLat);\n                                points.push(point);\n                            }\n                            // When entering the first valid point after a gap\n                            // of invalid points, typically on the far side of\n                            // the globe in an orthographic projection.\n                            if (gap && lastValidLonLat) {\n                                // For areas, in an orthographic projection, the\n                                // great circle between two visible points will\n                                // be close to the horizon. A possible exception\n                                // may be when the two points are on opposite\n                                // sides of the globe. It that poses a problem,\n                                // we may have to rewrite this to use the small\n                                // circle related to the current lon0 and lat0.\n                                if (isPolygon && hasGeoProjection) {\n                                    const geodesic = Projection.geodesic(lastValidLonLat, lonLat);\n                                    geodesic.forEach((lonLat) => pushToPath(postclip.forward(lonLat)));\n                                    // For lines, just jump over the gap\n                                }\n                                else {\n                                    movedTo = false;\n                                }\n                            }\n                            pushToPath(point);\n                            lastValidLonLat = lonLat;\n                            gap = false;\n                        }\n                        else {\n                            gap = true;\n                        }\n                    }\n                }\n            });\n        };\n        if (geometry.type === 'LineString') {\n            addToPath(geometry.coordinates);\n        }\n        else if (geometry.type === 'MultiLineString') {\n            geometry.coordinates.forEach((c) => addToPath(c));\n        }\n        else if (geometry.type === 'Polygon') {\n            geometry.coordinates.forEach((c) => addToPath(c));\n            if (path.length) {\n                path.push(['Z']);\n            }\n        }\n        else if (geometry.type === 'MultiPolygon') {\n            geometry.coordinates.forEach((polygons) => {\n                polygons.forEach((c) => addToPath(c));\n            });\n            if (path.length) {\n                path.push(['Z']);\n            }\n        }\n        return path;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nProjection.registry = ProjectionRegistry;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Maps_Projection = (Projection);\n\n;// ./code/es-modules/Maps/MapView.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { composed: MapView_composed } = (external_highcharts_src_js_default_default());\n\n\n\nconst { pointInPolygon } = Geometry_GeometryUtilities;\nconst { topo2geo } = Maps_GeoJSONComposition;\n\nconst { boundsFromPath: MapView_boundsFromPath } = Maps_MapUtilities;\n\n\nconst { addEvent: MapView_addEvent, clamp: MapView_clamp, crisp, fireEvent: MapView_fireEvent, isArray, isNumber: MapView_isNumber, isObject, isString, merge: MapView_merge, pick: MapView_pick, pushUnique: MapView_pushUnique, relativeLength: MapView_relativeLength } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst tileSize = 256;\n/**\n * The world size in terms of 10k meters in the Web Mercator projection, to\n * match a 256 square tile to zoom level 0.\n * @private\n */\nconst worldSize = 400.979322;\n/* *\n *\n *  Variables\n *\n * */\nlet maps = {};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Compute the zoom from given bounds and the size of the playing field. Used in\n * two places, hence the local function.\n * @private\n */\nfunction zoomFromBounds(b, playingField) {\n    const { width, height } = playingField, scaleToField = Math.max((b.x2 - b.x1) / (width / tileSize), (b.y2 - b.y1) / (height / tileSize));\n    return Math.log(worldSize / scaleToField) / Math.log(2);\n}\n/**\n * Calculate and set the recommended map view drilldown or drillup if mapData\n * is set for the series.\n * @private\n */\nfunction recommendedMapViewAfterDrill(e) {\n    if (e.seriesOptions.mapData) {\n        this.mapView?.recommendMapView(this, [\n            this.options.chart.map,\n            e.seriesOptions.mapData\n        ], this.options.drilldown?.mapZooming);\n    }\n}\n/*\nConst mergeCollections = <\n    T extends Array<AnyRecord|undefined>\n>(a: T, b: T): T => {\n    b.forEach((newer, i): void => {\n        // Only merge by id supported for now. We may consider later to support\n        // more complex rules like those of `Chart.update` with `oneToOne`, but\n        // it is probably not needed. Existing insets can be disabled by\n        // overwriting the `geoBounds` with empty data.\n        if (newer && isString(newer.id)) {\n            const older = U.find(\n                a,\n                (aItem): boolean => (aItem && aItem.id) === newer.id\n            );\n            if (older) {\n                const aIndex = a.indexOf(older);\n                a[aIndex] = merge(older, newer);\n            }\n        }\n    });\n    return a;\n};\n*/\n/* *\n *\n *  Classes\n *\n * */\n/**\n * The map view handles zooming and centering on the map, and various\n * client-side projection capabilities.\n *\n * On a chart instance of `MapChart`, the map view is available as `chart.mapView`.\n *\n * @class\n * @name Highcharts.MapView\n *\n * @param {Highcharts.MapChart} chart\n *        The MapChart instance\n * @param {Highcharts.MapViewOptions} options\n *        MapView options\n */\nclass MapView {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(MapChartClass) {\n        if (MapView_pushUnique(MapView_composed, 'MapView')) {\n            maps = MapChartClass.maps;\n            // Initialize MapView after initialization, but before firstRender\n            MapView_addEvent(MapChartClass, 'afterInit', function () {\n                /**\n                 * The map view handles zooming and centering on the map, and\n                 * various client-side projection capabilities.\n                 *\n                 * @name Highcharts.MapChart#mapView\n                 * @type {Highcharts.MapView|undefined}\n                 */\n                this.mapView = new MapView(this, this.options.mapView);\n            }, { order: 0 });\n            MapView_addEvent(MapChartClass, 'addSeriesAsDrilldown', recommendedMapViewAfterDrill);\n            MapView_addEvent(MapChartClass, 'afterDrillUp', recommendedMapViewAfterDrill);\n        }\n    }\n    /**\n     * Return the composite bounding box of a collection of bounding boxes\n     * @private\n     */\n    static compositeBounds(arrayOfBounds) {\n        if (arrayOfBounds.length) {\n            return arrayOfBounds\n                .slice(1)\n                .reduce((acc, cur) => {\n                acc.x1 = Math.min(acc.x1, cur.x1);\n                acc.y1 = Math.min(acc.y1, cur.y1);\n                acc.x2 = Math.max(acc.x2, cur.x2);\n                acc.y2 = Math.max(acc.y2, cur.y2);\n                return acc;\n            }, MapView_merge(arrayOfBounds[0]));\n        }\n        return;\n    }\n    /**\n     * Merge two collections of insets by the id.\n     * @private\n     */\n    static mergeInsets(a, b) {\n        const toObject = (insets) => {\n            const ob = {};\n            insets.forEach((inset, i) => {\n                ob[inset && inset.id || `i${i}`] = inset;\n            });\n            return ob;\n        };\n        const insetsObj = MapView_merge(toObject(a), toObject(b)), insets = Object\n            .keys(insetsObj)\n            .map((key) => insetsObj[key]);\n        return insets;\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, options) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.allowTransformAnimation = true;\n        this.eventsToUnbind = [];\n        this.insets = [];\n        this.padding = [0, 0, 0, 0];\n        this.recommendedMapView = {};\n        if (!(this instanceof MapViewInset)) {\n            this.recommendMapView(chart, [\n                chart.options.chart.map,\n                ...(chart.options.series || []).map((s) => s.mapData)\n            ]);\n        }\n        this.userOptions = options || {};\n        const o = MapView_merge(Maps_MapViewDefaults, this.recommendedMapView, options);\n        // Merge the inset collections by id, or index if id missing\n        const recInsets = this.recommendedMapView?.insets, optInsets = options && options.insets;\n        if (recInsets && optInsets) {\n            o.insets = MapView.mergeInsets(recInsets, optInsets);\n        }\n        this.chart = chart;\n        /**\n         * The current center of the view in terms of `[longitude, latitude]`.\n         * @name Highcharts.MapView#center\n         * @readonly\n         * @type {LonLatArray}\n         */\n        this.center = o.center;\n        this.options = o;\n        this.projection = new Maps_Projection(o.projection);\n        // Initialize with full plot box so we don't have to check for undefined\n        // every time we use it\n        this.playingField = chart.plotBox;\n        /**\n         * The current zoom level of the view.\n         * @name Highcharts.MapView#zoom\n         * @readonly\n         * @type {number}\n         */\n        this.zoom = o.zoom || 0;\n        this.minZoom = o.minZoom;\n        // Create the insets\n        this.createInsets();\n        // Initialize and respond to chart size changes\n        this.eventsToUnbind.push(MapView_addEvent(chart, 'afterSetChartSize', () => {\n            this.playingField = this.getField();\n            if (this.minZoom === void 0 || // When initializing the chart\n                this.minZoom === this.zoom // When resizing the chart\n            ) {\n                this.fitToBounds(void 0, void 0, false);\n                if (\n                // Set zoom only when initializing the chart\n                // (do not overwrite when zooming in/out, #17082)\n                !this.chart.hasRendered &&\n                    MapView_isNumber(this.userOptions.zoom)) {\n                    this.zoom = this.userOptions.zoom;\n                }\n                if (this.userOptions.center) {\n                    MapView_merge(true, this.center, this.userOptions.center);\n                }\n            }\n        }));\n        this.setUpEvents();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create MapViewInset instances from insets options\n     * @private\n     */\n    createInsets() {\n        const options = this.options, insets = options.insets;\n        if (insets) {\n            insets.forEach((item) => {\n                const inset = new MapViewInset(this, MapView_merge(options.insetOptions, item));\n                this.insets.push(inset);\n            });\n        }\n    }\n    /**\n     * Fit the view to given bounds\n     *\n     * @function Highcharts.MapView#fitToBounds\n     * @param {Object} bounds\n     *        Bounds in terms of projected units given as  `{ x1, y1, x2, y2 }`.\n     *        If not set, fit to the bounds of the current data set\n     * @param {number|string} [padding=0]\n     *        Padding inside the bounds. A number signifies pixels, while a\n     *        percentage string (like `5%`) can be used as a fraction of the\n     *        plot area size.\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart immediately\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n     *        What animation to use for redraw\n     */\n    fitToBounds(bounds, padding, redraw = true, animation) {\n        const b = bounds || this.getProjectedBounds();\n        if (b) {\n            const pad = MapView_pick(padding, bounds ? 0 : this.options.padding), fullField = this.getField(false), padArr = isArray(pad) ? pad : [pad, pad, pad, pad];\n            this.padding = [\n                MapView_relativeLength(padArr[0], fullField.height),\n                MapView_relativeLength(padArr[1], fullField.width),\n                MapView_relativeLength(padArr[2], fullField.height),\n                MapView_relativeLength(padArr[3], fullField.width)\n            ];\n            // Apply the playing field, corrected with padding\n            this.playingField = this.getField();\n            const zoom = zoomFromBounds(b, this.playingField);\n            // Reset minZoom when fitting to natural bounds\n            if (!bounds) {\n                this.minZoom = zoom;\n            }\n            const center = this.projection.inverse([\n                (b.x2 + b.x1) / 2,\n                (b.y2 + b.y1) / 2\n            ]);\n            this.setView(center, zoom, redraw, animation);\n        }\n    }\n    getField(padded = true) {\n        const padding = padded ? this.padding : [0, 0, 0, 0];\n        return {\n            x: padding[3],\n            y: padding[0],\n            width: this.chart.plotWidth - padding[1] - padding[3],\n            height: this.chart.plotHeight - padding[0] - padding[2]\n        };\n    }\n    getGeoMap(map) {\n        if (isString(map)) {\n            if (maps[map] && maps[map].type === 'Topology') {\n                return topo2geo(maps[map]);\n            }\n            return maps[map];\n        }\n        if (isObject(map, true)) {\n            if (map.type === 'FeatureCollection') {\n                return map;\n            }\n            if (map.type === 'Topology') {\n                return topo2geo(map);\n            }\n        }\n    }\n    getMapBBox() {\n        const bounds = this.getProjectedBounds(), scale = this.getScale();\n        if (bounds) {\n            const padding = this.padding, p1 = this.projectedUnitsToPixels({\n                x: bounds.x1,\n                y: bounds.y2\n            }), width = ((bounds.x2 - bounds.x1) * scale +\n                padding[1] + padding[3]), height = ((bounds.y2 - bounds.y1) * scale +\n                padding[0] + padding[2]);\n            return {\n                width,\n                height,\n                x: p1.x - padding[3],\n                y: p1.y - padding[0]\n            };\n        }\n    }\n    getProjectedBounds() {\n        const projection = this.projection;\n        const allBounds = this.chart.series.reduce((acc, s) => {\n            const bounds = s.getProjectedBounds && s.getProjectedBounds();\n            if (bounds &&\n                s.options.affectsMapView !== false) {\n                acc.push(bounds);\n            }\n            return acc;\n        }, []);\n        // The bounds option\n        const fitToGeometry = this.options.fitToGeometry;\n        if (fitToGeometry) {\n            if (!this.fitToGeometryCache) {\n                if (fitToGeometry.type === 'MultiPoint') {\n                    const positions = fitToGeometry.coordinates\n                        .map((lonLat) => projection.forward(lonLat)), xs = positions.map((pos) => pos[0]), ys = positions.map((pos) => pos[1]);\n                    this.fitToGeometryCache = {\n                        x1: Math.min.apply(0, xs),\n                        x2: Math.max.apply(0, xs),\n                        y1: Math.min.apply(0, ys),\n                        y2: Math.max.apply(0, ys)\n                    };\n                }\n                else {\n                    this.fitToGeometryCache = MapView_boundsFromPath(projection.path(fitToGeometry));\n                }\n            }\n            return this.fitToGeometryCache;\n        }\n        return this.projection.bounds || MapView.compositeBounds(allBounds);\n    }\n    getScale() {\n        // A zoom of 0 means the world (360x360 degrees) fits in a 256x256 px\n        // tile\n        return (tileSize / worldSize) * Math.pow(2, this.zoom);\n    }\n    // Calculate the SVG transform to be applied to series groups\n    getSVGTransform() {\n        const { x, y, width, height } = this.playingField, projectedCenter = this.projection.forward(this.center), flipFactor = this.projection.hasCoordinates ? -1 : 1, scaleX = this.getScale(), scaleY = scaleX * flipFactor, translateX = x + width / 2 - projectedCenter[0] * scaleX, translateY = y + height / 2 - projectedCenter[1] * scaleY;\n        return { scaleX, scaleY, translateX, translateY };\n    }\n    /**\n     * Convert map coordinates in longitude/latitude to pixels\n     *\n     * @function Highcharts.MapView#lonLatToPixels\n     * @since 10.0.0\n     * @param  {Highcharts.MapLonLatObject} lonLat\n     *         The map coordinates\n     * @return {Highcharts.PositionObject|undefined}\n     *         The pixel position\n     */\n    lonLatToPixels(lonLat) {\n        const pos = this.lonLatToProjectedUnits(lonLat);\n        if (pos) {\n            return this.projectedUnitsToPixels(pos);\n        }\n    }\n    /**\n     * Get projected units from longitude/latitude. Insets are accounted for.\n     * Returns an object with x and y values corresponding to positions on the\n     * projected plane.\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.MapView#lonLatToProjectedUnits\n     *\n     * @since 10.0.0\n     * @sample maps/series/latlon-to-point/ Find a point from lon/lat\n     *\n     * @param {Highcharts.MapLonLatObject} lonLat Coordinates.\n     *\n     * @return {Highcharts.ProjectedXY} X and Y coordinates in terms of\n     *      projected values\n     */\n    lonLatToProjectedUnits(lonLat) {\n        const chart = this.chart, mapTransforms = chart.mapTransforms;\n        // Legacy, built-in transforms\n        if (mapTransforms) {\n            for (const transform in mapTransforms) {\n                if (Object.hasOwnProperty.call(mapTransforms, transform) &&\n                    mapTransforms[transform].hitZone) {\n                    const coords = chart.transformFromLatLon(lonLat, mapTransforms[transform]);\n                    if (coords && pointInPolygon(coords, mapTransforms[transform].hitZone.coordinates[0])) {\n                        return coords;\n                    }\n                }\n            }\n            return chart.transformFromLatLon(lonLat, mapTransforms['default'] // eslint-disable-line dot-notation\n            );\n        }\n        // Handle insets\n        for (const inset of this.insets) {\n            if (inset.options.geoBounds &&\n                pointInPolygon({ x: lonLat.lon, y: lonLat.lat }, inset.options.geoBounds.coordinates[0])) {\n                const insetProjectedPoint = inset.projection.forward([lonLat.lon, lonLat.lat]), pxPoint = inset.projectedUnitsToPixels({ x: insetProjectedPoint[0], y: insetProjectedPoint[1] });\n                return this.pixelsToProjectedUnits(pxPoint);\n            }\n        }\n        const point = this.projection.forward([lonLat.lon, lonLat.lat]);\n        if (!point.outside) {\n            return { x: point[0], y: point[1] };\n        }\n    }\n    /**\n     * Calculate longitude/latitude values for a point or position. Returns an\n     * object with the numeric properties `lon` and `lat`.\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.MapView#projectedUnitsToLonLat\n     *\n     * @since 10.0.0\n     *\n     * @sample maps/demo/latlon-advanced/ Advanced lat/lon demo\n     *\n     * @param {Highcharts.Point|Highcharts.ProjectedXY} point\n     *        A `Point` instance or anything containing `x` and `y` properties\n     *        with numeric values.\n     *\n     * @return {Highcharts.MapLonLatObject|undefined} An object with `lat` and\n     *         `lon` properties.\n     */\n    projectedUnitsToLonLat(point) {\n        const chart = this.chart, mapTransforms = chart.mapTransforms;\n        // Legacy, built-in transforms\n        if (mapTransforms) {\n            for (const transform in mapTransforms) {\n                if (Object.hasOwnProperty.call(mapTransforms, transform) &&\n                    mapTransforms[transform].hitZone &&\n                    pointInPolygon(point, mapTransforms[transform].hitZone.coordinates[0])) {\n                    return chart.transformToLatLon(point, mapTransforms[transform]);\n                }\n            }\n            return chart.transformToLatLon(point, mapTransforms['default'] // eslint-disable-line dot-notation\n            );\n        }\n        const pxPoint = this.projectedUnitsToPixels(point);\n        for (const inset of this.insets) {\n            if (inset.hitZone &&\n                pointInPolygon(pxPoint, inset.hitZone.coordinates[0])) {\n                const insetProjectedPoint = inset\n                    .pixelsToProjectedUnits(pxPoint), coordinates = inset.projection.inverse([insetProjectedPoint.x, insetProjectedPoint.y]);\n                return { lon: coordinates[0], lat: coordinates[1] };\n            }\n        }\n        const coordinates = this.projection.inverse([point.x, point.y]);\n        return { lon: coordinates[0], lat: coordinates[1] };\n    }\n    /**\n     * Calculate and set the recommended map view based on provided map data\n     * from series.\n     *\n     * @requires modules/map\n     *\n     * @function Highcharts.MapView#recommendMapView\n     *\n     * @since 11.4.0\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart object\n     *\n     * @param {Array<MapDataType | undefined>} mapDataArray\n     *        Array of map data from all series.\n     *\n     * @param {boolean} [update=false]\n     *        Whether to update the chart with recommended map view.\n     *\n     * @return {Highcharts.MapViewOptions|undefined} Best suitable map view.\n     */\n    recommendMapView(chart, mapDataArray, update = false) {\n        // Reset recommended map view\n        this.recommendedMapView = {};\n        // Handle the global map and series-level mapData\n        const geoMaps = mapDataArray.map((mapData) => this.getGeoMap(mapData));\n        const allGeoBounds = [];\n        geoMaps.forEach((geoMap) => {\n            if (geoMap) {\n                // Use the first geo map as main\n                if (!Object.keys(this.recommendedMapView).length) {\n                    this.recommendedMapView =\n                        geoMap['hc-recommended-mapview'] || {};\n                }\n                // Combine the bounding boxes of all loaded maps\n                if (geoMap.bbox) {\n                    const [x1, y1, x2, y2] = geoMap.bbox;\n                    allGeoBounds.push({ x1, y1, x2, y2 });\n                }\n            }\n        });\n        // Get the composite bounds\n        const geoBounds = (allGeoBounds.length &&\n            MapView.compositeBounds(allGeoBounds));\n        // Provide a best-guess recommended projection if not set in\n        // the map or in user options\n        MapView_fireEvent(this, 'onRecommendMapView', {\n            geoBounds,\n            chart\n        }, function () {\n            if (geoBounds &&\n                this.recommendedMapView) {\n                if (!this.recommendedMapView.projection) {\n                    const { x1, y1, x2, y2 } = geoBounds;\n                    this.recommendedMapView.projection =\n                        (x2 - x1 > 180 && y2 - y1 > 90) ?\n                            // Wide angle, go for the world view\n                            {\n                                name: 'EqualEarth',\n                                parallels: [0, 0],\n                                rotation: [0]\n                            } :\n                            // Narrower angle, use a projection better\n                            // suited for local view\n                            {\n                                name: 'LambertConformalConic',\n                                parallels: [y1, y2],\n                                rotation: [-(x1 + x2) / 2]\n                            };\n                }\n                if (!this.recommendedMapView.insets) {\n                    this.recommendedMapView.insets = void 0; // Reset insets\n                }\n            }\n        });\n        // Register the main geo map (from options.chart.map) if set\n        this.geoMap = geoMaps[0];\n        if (update &&\n            chart.hasRendered &&\n            !chart.userOptions.mapView?.projection &&\n            this.recommendedMapView) {\n            this.update(this.recommendedMapView);\n        }\n    }\n    redraw(animation) {\n        this.chart.series.forEach((s) => {\n            if (s.useMapGeometry) {\n                s.isDirty = true;\n            }\n        });\n        this.chart.redraw(animation);\n    }\n    /**\n     * Set the view to given center and zoom values.\n     * @function Highcharts.MapView#setView\n     * @param {Highcharts.LonLatArray|undefined} center\n     *        The center point\n     * @param {number} zoom\n     *        The zoom level\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw immediately\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n     *        Animation options for the redraw\n     *\n     * @sample maps/mapview/setview\n     *        Set the view programmatically\n     */\n    setView(center, zoom, redraw = true, animation) {\n        if (center) {\n            this.center = center;\n        }\n        if (typeof zoom === 'number') {\n            if (typeof this.minZoom === 'number') {\n                zoom = Math.max(zoom, this.minZoom);\n            }\n            if (typeof this.options.maxZoom === 'number') {\n                zoom = Math.min(zoom, this.options.maxZoom);\n            }\n            // Use isNumber to prevent Infinity (#17205)\n            if (MapView_isNumber(zoom)) {\n                this.zoom = zoom;\n            }\n        }\n        const bounds = this.getProjectedBounds();\n        if (bounds) {\n            const projectedCenter = this.projection.forward(this.center), { x, y, width, height } = this.playingField, scale = this.getScale(), bottomLeft = this.projectedUnitsToPixels({\n                x: bounds.x1,\n                y: bounds.y1\n            }), topRight = this.projectedUnitsToPixels({\n                x: bounds.x2,\n                y: bounds.y2\n            }), boundsCenterProjected = [\n                (bounds.x1 + bounds.x2) / 2,\n                (bounds.y1 + bounds.y2) / 2\n            ], isDrilling = this.chart.series.some((series) => series.isDrilling);\n            if (!isDrilling) {\n                // Constrain to data bounds\n                // Pixel coordinate system is reversed vs projected\n                const x1 = bottomLeft.x, y1 = topRight.y, x2 = topRight.x, y2 = bottomLeft.y;\n                // Map smaller than plot area, center it\n                if (x2 - x1 < width) {\n                    projectedCenter[0] = boundsCenterProjected[0];\n                    // Off west\n                }\n                else if (x1 < x && x2 < x + width) {\n                    // Adjust eastwards\n                    projectedCenter[0] +=\n                        Math.max(x1 - x, x2 - width - x) / scale;\n                    // Off east\n                }\n                else if (x2 > x + width && x1 > x) {\n                    // Adjust westwards\n                    projectedCenter[0] +=\n                        Math.min(x2 - width - x, x1 - x) / scale;\n                }\n                // Map smaller than plot area, center it\n                if (y2 - y1 < height) {\n                    projectedCenter[1] = boundsCenterProjected[1];\n                    // Off north\n                }\n                else if (y1 < y && y2 < y + height) {\n                    // Adjust southwards\n                    projectedCenter[1] -=\n                        Math.max(y1 - y, y2 - height - y) / scale;\n                    // Off south\n                }\n                else if (y2 > y + height && y1 > y) {\n                    // Adjust northwards\n                    projectedCenter[1] -=\n                        Math.min(y2 - height - y, y1 - y) / scale;\n                }\n                this.center = this.projection.inverse(projectedCenter);\n            }\n            this.insets.forEach((inset) => {\n                if (inset.options.field) {\n                    inset.hitZone = inset.getHitZone();\n                    inset.playingField = inset.getField();\n                }\n            });\n            this.render();\n        }\n        MapView_fireEvent(this, 'afterSetView');\n        if (redraw) {\n            this.redraw(animation);\n        }\n    }\n    /**\n     * Convert projected units to pixel position\n     *\n     * @function Highcharts.MapView#projectedUnitsToPixels\n     * @param {Highcharts.PositionObject} pos\n     *        The position in projected units\n     * @return {Highcharts.PositionObject} The position in pixels\n     */\n    projectedUnitsToPixels(pos) {\n        const scale = this.getScale(), projectedCenter = this.projection.forward(this.center), field = this.playingField, centerPxX = field.x + field.width / 2, centerPxY = field.y + field.height / 2;\n        const x = centerPxX - scale * (projectedCenter[0] - pos.x);\n        const y = centerPxY + scale * (projectedCenter[1] - pos.y);\n        return { x, y };\n    }\n    /**\n     * Convert pixel position to longitude and latitude.\n     *\n     * @function Highcharts.MapView#pixelsToLonLat\n     * @since 10.0.0\n     * @param  {Highcharts.PositionObject} pos\n     *         The position in pixels\n     * @return {Highcharts.MapLonLatObject|undefined}\n     *         The map coordinates\n     */\n    pixelsToLonLat(pos) {\n        return this.projectedUnitsToLonLat(this.pixelsToProjectedUnits(pos));\n    }\n    /**\n     * Convert pixel position to projected units\n     *\n     * @function Highcharts.MapView#pixelsToProjectedUnits\n     * @param {Highcharts.PositionObject} pos\n     *        The position in pixels\n     * @return {Highcharts.PositionObject} The position in projected units\n     */\n    pixelsToProjectedUnits(pos) {\n        const { x, y } = pos, scale = this.getScale(), projectedCenter = this.projection.forward(this.center), field = this.playingField, centerPxX = field.x + field.width / 2, centerPxY = field.y + field.height / 2;\n        const projectedX = projectedCenter[0] + (x - centerPxX) / scale;\n        const projectedY = projectedCenter[1] - (y - centerPxY) / scale;\n        return { x: projectedX, y: projectedY };\n    }\n    setUpEvents() {\n        const { chart } = this;\n        // Set up panning and touch zoom for maps. In orthographic projections\n        // the globe will rotate, otherwise adjust the map center and zoom.\n        let mouseDownCenterProjected, mouseDownKey, mouseDownRotation;\n        const onPan = (e) => {\n            const { lastTouches, pinchDown } = chart.pointer, projection = this.projection, touches = e.touches;\n            let { mouseDownX, mouseDownY } = chart, howMuch = 0;\n            if (pinchDown?.length === 1) {\n                mouseDownX = pinchDown[0].chartX;\n                mouseDownY = pinchDown[0].chartY;\n            }\n            else if (pinchDown?.length === 2) {\n                mouseDownX = (pinchDown[0].chartX + pinchDown[1].chartX) / 2;\n                mouseDownY = (pinchDown[0].chartY + pinchDown[1].chartY) / 2;\n            }\n            // How much has the distance between the fingers changed?\n            if (touches?.length === 2 && lastTouches) {\n                const startDistance = Math.sqrt(Math.pow(lastTouches[0].chartX - lastTouches[1].chartX, 2) +\n                    Math.pow(lastTouches[0].chartY - lastTouches[1].chartY, 2)), endDistance = Math.sqrt(Math.pow(touches[0].chartX - touches[1].chartX, 2) +\n                    Math.pow(touches[0].chartY - touches[1].chartY, 2));\n                howMuch = Math.log(startDistance / endDistance) / Math.log(0.5);\n            }\n            if (MapView_isNumber(mouseDownX) && MapView_isNumber(mouseDownY)) {\n                const key = `${mouseDownX},${mouseDownY}`;\n                let { chartX, chartY } = e.originalEvent;\n                if (touches?.length === 2) {\n                    chartX = (touches[0].chartX + touches[1].chartX) / 2;\n                    chartY = (touches[0].chartY + touches[1].chartY) / 2;\n                }\n                // Reset starting position\n                if (key !== mouseDownKey) {\n                    mouseDownKey = key;\n                    mouseDownCenterProjected = this.projection\n                        .forward(this.center);\n                    mouseDownRotation = (this.projection.options.rotation || [0, 0]).slice();\n                }\n                // Get the natural zoom level of the projection itself when\n                // zoomed to view the full world\n                const worldBounds = projection.def && projection.def.bounds, worldZoom = (worldBounds &&\n                    zoomFromBounds(worldBounds, this.playingField)) || -Infinity;\n                // Panning rotates the globe\n                if (projection.options.name === 'Orthographic' &&\n                    (touches?.length || 0) < 2 &&\n                    // ... but don't rotate if we're loading only a part of the\n                    // world\n                    (this.minZoom || Infinity) < worldZoom * 1.3) {\n                    // Empirical ratio where the globe rotates roughly the same\n                    // speed as moving the pointer across the center of the\n                    // projection\n                    const ratio = 440 / (this.getScale() * Math.min(chart.plotWidth, chart.plotHeight));\n                    if (mouseDownRotation) {\n                        const lon = (mouseDownX - chartX) * ratio -\n                            mouseDownRotation[0], lat = MapView_clamp(-mouseDownRotation[1] -\n                            (mouseDownY - chartY) * ratio, -80, 80), zoom = this.zoom;\n                        this.update({\n                            projection: {\n                                rotation: [-lon, -lat]\n                            }\n                        }, false);\n                        this.fitToBounds(void 0, void 0, false);\n                        this.zoom = zoom;\n                        chart.redraw(false);\n                    }\n                    // #17925 Skip NaN values\n                }\n                else if (MapView_isNumber(chartX) && MapView_isNumber(chartY)) {\n                    // #17238\n                    const scale = this.getScale(), flipFactor = this.projection.hasCoordinates ? 1 : -1;\n                    const newCenter = this.projection.inverse([\n                        mouseDownCenterProjected[0] +\n                            (mouseDownX - chartX) / scale,\n                        mouseDownCenterProjected[1] -\n                            (mouseDownY - chartY) / scale * flipFactor\n                    ]);\n                    // #19190 Skip NaN coords\n                    if (!isNaN(newCenter[0] + newCenter[1])) {\n                        this.zoomBy(howMuch, newCenter, void 0, false);\n                    }\n                }\n                e.preventDefault();\n            }\n        };\n        MapView_addEvent(chart, 'pan', onPan);\n        MapView_addEvent(chart, 'touchpan', onPan);\n        // Perform the map zoom by selection\n        MapView_addEvent(chart, 'selection', (evt) => {\n            // Zoom in\n            if (!evt.resetSelection) {\n                const x = evt.x - chart.plotLeft;\n                const y = evt.y - chart.plotTop;\n                const { y: y1, x: x1 } = this.pixelsToProjectedUnits({ x, y });\n                const { y: y2, x: x2 } = this.pixelsToProjectedUnits({ x: x + evt.width, y: y + evt.height });\n                this.fitToBounds({ x1, y1, x2, y2 }, void 0, true, evt.originalEvent.touches ?\n                    // On touch zoom, don't animate, since we're already in\n                    // transformed zoom preview\n                    false :\n                    // On mouse zoom, obey the chart-level animation\n                    void 0);\n                // Only for mouse. Touch users can pinch out.\n                if (!/^touch/.test((evt.originalEvent.type))) {\n                    chart.showResetZoom();\n                }\n                evt.preventDefault();\n                // Reset zoom\n            }\n            else {\n                this.zoomBy();\n            }\n        });\n    }\n    render() {\n        // We need a group for the insets\n        if (!this.group) {\n            this.group = this.chart.renderer.g('map-view')\n                .attr({ zIndex: 4 })\n                .add();\n        }\n    }\n    /**\n     * Update the view with given options\n     *\n     * @function Highcharts.MapView#update\n     *\n     * @param {Partial<Highcharts.MapViewOptions>} options\n     *        The new map view options to apply\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw immediately\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n     *        The animation to apply to a the redraw\n     */\n    update(options, redraw = true, animation) {\n        const newProjection = options.projection, isDirtyProjection = newProjection && ((Maps_Projection.toString(newProjection) !==\n            Maps_Projection.toString(this.options.projection)));\n        let isDirtyInsets = false;\n        MapView_merge(true, this.userOptions, options);\n        MapView_merge(true, this.options, options);\n        // If anything changed with the insets, destroy them all and create\n        // again below\n        if ('insets' in options) {\n            this.insets.forEach((inset) => inset.destroy());\n            this.insets.length = 0;\n            isDirtyInsets = true;\n        }\n        if (isDirtyProjection || 'fitToGeometry' in options) {\n            delete this.fitToGeometryCache;\n        }\n        if (isDirtyProjection || isDirtyInsets) {\n            this.chart.series.forEach((series) => {\n                const groups = series.transformGroups;\n                if (series.clearBounds) {\n                    series.clearBounds();\n                }\n                series.isDirty = true;\n                series.isDirtyData = true;\n                // Destroy inset transform groups\n                if (isDirtyInsets && groups) {\n                    while (groups.length > 1) {\n                        const group = groups.pop();\n                        if (group) {\n                            group.destroy();\n                        }\n                    }\n                }\n            });\n            if (isDirtyProjection) {\n                this.projection = new Maps_Projection(this.options.projection);\n            }\n            // Create new insets\n            if (isDirtyInsets) {\n                this.createInsets();\n            }\n            // Fit to natural bounds if center/zoom are not explicitly given\n            if (!options.center &&\n                // Do not fire fitToBounds if user don't want to set zoom\n                Object.hasOwnProperty.call(options, 'zoom') &&\n                !MapView_isNumber(options.zoom)) {\n                this.fitToBounds(void 0, void 0, false);\n            }\n        }\n        if (options.center || MapView_isNumber(options.zoom)) {\n            this.setView(this.options.center, options.zoom, false);\n        }\n        else if ('fitToGeometry' in options) {\n            this.fitToBounds(void 0, void 0, false);\n        }\n        if (redraw) {\n            this.chart.redraw(animation);\n        }\n    }\n    /**\n     * Zoom the map view by a given number\n     *\n     * @function Highcharts.MapView#zoomBy\n     *\n     * @param {number|undefined} [howMuch]\n     *        The amount of zoom to apply. 1 zooms in on half the current view,\n     *        -1 zooms out. Pass `undefined` to zoom to the full bounds of the\n     *        map.\n     * @param {Highcharts.LonLatArray} [coords]\n     *        Optional map coordinates to keep fixed\n     * @param {Array<number>} [chartCoords]\n     *        Optional chart coordinates to keep fixed, in pixels\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n     *        The animation to apply to a the redraw\n     */\n    zoomBy(howMuch, coords, chartCoords, animation) {\n        const chart = this.chart, projectedCenter = this.projection.forward(this.center);\n        if (typeof howMuch === 'number') {\n            const zoom = this.zoom + howMuch;\n            let center, x, y;\n            // Keep chartX and chartY stationary - convert to lat and lng\n            if (chartCoords) {\n                const [chartX, chartY] = chartCoords;\n                const scale = this.getScale();\n                const offsetX = chartX - chart.plotLeft - chart.plotWidth / 2;\n                const offsetY = chartY - chart.plotTop - chart.plotHeight / 2;\n                x = projectedCenter[0] + offsetX / scale;\n                y = projectedCenter[1] + offsetY / scale;\n            }\n            // Keep lon and lat stationary by adjusting the center\n            if (typeof x === 'number' && typeof y === 'number') {\n                const scale = 1 - Math.pow(2, this.zoom) / Math.pow(2, zoom);\n                const offsetX = projectedCenter[0] - x;\n                const offsetY = projectedCenter[1] - y;\n                projectedCenter[0] -= offsetX * scale;\n                projectedCenter[1] += offsetY * scale;\n                center = this.projection.inverse(projectedCenter);\n            }\n            this.setView(coords || center, zoom, void 0, animation);\n            // Undefined howMuch => reset zoom\n        }\n        else {\n            this.fitToBounds(void 0, void 0, void 0, animation);\n        }\n    }\n}\n// Putting this in the same file due to circular dependency with MapView\nclass MapViewInset extends MapView {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(mapView, options) {\n        super(mapView.chart, options);\n        this.id = options.id;\n        this.mapView = mapView;\n        this.options = MapView_merge({ center: [0, 0] }, mapView.options.insetOptions, options);\n        this.allBounds = [];\n        if (this.options.geoBounds) {\n            // The path in projected units in the map view's main projection.\n            // This is used for hit testing where the points should render.\n            const path = mapView.projection.path(this.options.geoBounds);\n            this.geoBoundsProjectedBox = MapView_boundsFromPath(path);\n            this.geoBoundsProjectedPolygon = path.map((segment) => [\n                segment[1] || 0,\n                segment[2] || 0\n            ]);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get the playing field in pixels\n     * @private\n     */\n    getField(padded = true) {\n        const hitZone = this.hitZone;\n        if (hitZone) {\n            const padding = padded ? this.padding : [0, 0, 0, 0], polygon = hitZone.coordinates[0], xs = polygon.map((xy) => xy[0]), ys = polygon.map((xy) => xy[1]), x = Math.min.apply(0, xs) + padding[3], x2 = Math.max.apply(0, xs) - padding[1], y = Math.min.apply(0, ys) + padding[0], y2 = Math.max.apply(0, ys) - padding[2];\n            if (MapView_isNumber(x) && MapView_isNumber(y)) {\n                return {\n                    x,\n                    y,\n                    width: x2 - x,\n                    height: y2 - y\n                };\n            }\n        }\n        // Fall back to plot area\n        return super.getField.call(this, padded);\n    }\n    /**\n     * Get the hit zone in pixels.\n     * @private\n     */\n    getHitZone() {\n        const { chart, mapView, options } = this, { coordinates } = options.field || {};\n        if (coordinates) {\n            let polygon = coordinates[0];\n            if (options.units === 'percent') {\n                const relativeTo = options.relativeTo === 'mapBoundingBox' &&\n                    mapView.getMapBBox() ||\n                    MapView_merge(chart.plotBox, { x: 0, y: 0 });\n                polygon = polygon.map((xy) => [\n                    MapView_relativeLength(`${xy[0]}%`, relativeTo.width, relativeTo.x),\n                    MapView_relativeLength(`${xy[1]}%`, relativeTo.height, relativeTo.y)\n                ]);\n            }\n            return {\n                type: 'Polygon',\n                coordinates: [polygon]\n            };\n        }\n    }\n    getProjectedBounds() {\n        return MapView.compositeBounds(this.allBounds);\n    }\n    /**\n     * Determine whether a point on the main projected plane is inside the\n     * geoBounds of the inset.\n     * @private\n     */\n    isInside(point) {\n        const { geoBoundsProjectedBox, geoBoundsProjectedPolygon } = this;\n        return Boolean(\n        // First we do a pre-pass to check whether the test point is inside\n        // the rectangular bounding box of the polygon. This is less\n        // expensive and will rule out most cases.\n        geoBoundsProjectedBox &&\n            point.x >= geoBoundsProjectedBox.x1 &&\n            point.x <= geoBoundsProjectedBox.x2 &&\n            point.y >= geoBoundsProjectedBox.y1 &&\n            point.y <= geoBoundsProjectedBox.y2 &&\n            // Next, do the more expensive check whether the point is inside the\n            // polygon itself.\n            geoBoundsProjectedPolygon &&\n            pointInPolygon(point, geoBoundsProjectedPolygon));\n    }\n    /**\n     * Render the map view inset with the border path\n     * @private\n     */\n    render() {\n        const { chart, mapView, options } = this, borderPath = options.borderPath || options.field;\n        if (borderPath && mapView.group) {\n            let animate = true;\n            if (!this.border) {\n                this.border = chart.renderer\n                    .path()\n                    .addClass('highcharts-mapview-inset-border')\n                    .add(mapView.group);\n                animate = false;\n            }\n            if (!chart.styledMode) {\n                this.border.attr({\n                    stroke: options.borderColor,\n                    'stroke-width': options.borderWidth\n                });\n            }\n            const strokeWidth = this.border.strokeWidth(), field = (options.relativeTo === 'mapBoundingBox' &&\n                mapView.getMapBBox()) || mapView.playingField;\n            const d = (borderPath.coordinates || []).reduce((d, lineString) => lineString.reduce((d, point, i) => {\n                let [x, y] = point;\n                if (options.units === 'percent') {\n                    x = chart.plotLeft + MapView_relativeLength(`${x}%`, field.width, field.x);\n                    y = chart.plotTop + MapView_relativeLength(`${y}%`, field.height, field.y);\n                }\n                x = crisp(x, strokeWidth);\n                y = crisp(y, strokeWidth);\n                d.push(i === 0 ? ['M', x, y] : ['L', x, y]);\n                return d;\n            }, d), []);\n            // Apply the border path\n            this.border[animate ? 'animate' : 'attr']({ d });\n        }\n    }\n    destroy() {\n        if (this.border) {\n            this.border = this.border.destroy();\n        }\n        this.eventsToUnbind.forEach((f) => f());\n    }\n    /**\n     * No chart-level events for insets\n     * @private\n     */\n    setUpEvents() { }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Maps_MapView = (MapView);\n\n;// ./code/es-modules/Series/Map/MapSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject, stop } = (external_highcharts_src_js_default_default());\n\n\n\nconst { noop } = (external_highcharts_src_js_default_default());\n\nconst { splitPath } = Chart_MapChart;\n\n\n\n\nconst { \n// Indirect dependency to keep product size low\ncolumn: ColumnSeries, scatter: ScatterSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: MapSeries_extend, find, fireEvent: MapSeries_fireEvent, getNestedProperty, isArray: MapSeries_isArray, defined: MapSeries_defined, isNumber: MapSeries_isNumber, isObject: MapSeries_isObject, merge: MapSeries_merge, objectEach: MapSeries_objectEach, pick: MapSeries_pick, splat } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.map\n *\n * @augments Highcharts.Series\n */\nclass MapSeries extends ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.processedData = [];\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * The initial animation for the map series. By default, animation is\n     * disabled.\n     * @private\n     */\n    animate(init) {\n        const { chart, group } = this, animation = animObject(this.options.animation);\n        // Initialize the animation\n        if (init) {\n            // Scale down the group and place it in the center\n            group.attr({\n                translateX: chart.plotLeft + chart.plotWidth / 2,\n                translateY: chart.plotTop + chart.plotHeight / 2,\n                scaleX: 0.001, // #1499\n                scaleY: 0.001\n            });\n            // Run the animation\n        }\n        else {\n            group.animate({\n                translateX: chart.plotLeft,\n                translateY: chart.plotTop,\n                scaleX: 1,\n                scaleY: 1\n            }, animation);\n        }\n    }\n    clearBounds() {\n        this.points.forEach((point) => {\n            delete point.bounds;\n            delete point.insetIndex;\n            delete point.projectedPath;\n        });\n        delete this.bounds;\n    }\n    /**\n     * Allow a quick redraw by just translating the area group. Used for zooming\n     * and panning in capable browsers.\n     * @private\n     */\n    doFullTranslate() {\n        return Boolean(this.isDirtyData ||\n            this.chart.isResizing ||\n            !this.hasRendered);\n    }\n    /**\n     * Draw the data labels. Special for maps is the time that the data labels\n     * are drawn (after points), and the clipping of the dataLabelsGroup.\n     * @private\n     */\n    drawMapDataLabels() {\n        super.drawDataLabels();\n        if (this.dataLabelsGroup) {\n            this.dataLabelsGroup.clip(this.chart.clipRect);\n        }\n    }\n    /**\n     * Use the drawPoints method of column, that is able to handle simple\n     * shapeArgs. Extend it by assigning the tooltip position.\n     * @private\n     */\n    drawPoints() {\n        const series = this, { chart, group, transformGroups = [] } = this, { mapView, renderer } = chart;\n        if (!mapView) {\n            return;\n        }\n        // Set groups that handle transform during zooming and panning in order\n        // to preserve clipping on series.group\n        this.transformGroups = transformGroups;\n        if (!transformGroups[0]) {\n            transformGroups[0] = renderer.g().add(group);\n        }\n        for (let i = 0, iEnd = mapView.insets.length; i < iEnd; ++i) {\n            if (!transformGroups[i + 1]) {\n                transformGroups.push(renderer.g().add(group));\n            }\n        }\n        // Draw the shapes again\n        if (this.doFullTranslate()) {\n            // Individual point actions.\n            this.points.forEach((point) => {\n                const { graphic } = point;\n                // Points should be added in the corresponding transform group\n                point.group = transformGroups[typeof point.insetIndex === 'number' ?\n                    point.insetIndex + 1 :\n                    0];\n                // When the point has been moved between insets after\n                // MapView.update\n                if (graphic && graphic.parentGroup !== point.group) {\n                    graphic.add(point.group);\n                }\n            });\n            // Draw the points\n            ColumnSeries.prototype.drawPoints.apply(this);\n            // Add class names\n            this.points.forEach((point) => {\n                const graphic = point.graphic;\n                if (graphic) {\n                    const animate = graphic.animate;\n                    let className = '';\n                    if (point.name) {\n                        className +=\n                            'highcharts-name-' +\n                                point.name.replace(/ /g, '-').toLowerCase();\n                    }\n                    if (point.properties?.['hc-key']) {\n                        className +=\n                            ' highcharts-key-' +\n                                point.properties['hc-key'].toString().toLowerCase();\n                    }\n                    if (className) {\n                        graphic.addClass(className);\n                    }\n                    // In styled mode, apply point colors by CSS\n                    if (chart.styledMode) {\n                        graphic.css(this.pointAttribs(point, point.selected && 'select' || void 0));\n                    }\n                    // If the map point is not visible and is not null (e.g.\n                    // hidden by data classes), then the point should be\n                    // visible, but without value\n                    graphic.attr({\n                        visibility: (point.visible ||\n                            (!point.visible && !point.isNull)) ? 'inherit' : 'hidden'\n                    });\n                    graphic.animate = function (params, options, complete) {\n                        const animateIn = (MapSeries_isNumber(params['stroke-width']) &&\n                            !MapSeries_isNumber(graphic['stroke-width'])), animateOut = (MapSeries_isNumber(graphic['stroke-width']) &&\n                            !MapSeries_isNumber(params['stroke-width']));\n                        // When strokeWidth is animating\n                        if (animateIn || animateOut) {\n                            const strokeWidth = MapSeries_pick(series.getStrokeWidth(series.options), 1 // Styled mode\n                            ), inheritedStrokeWidth = (strokeWidth /\n                                (chart.mapView?.getScale() ||\n                                    1));\n                            // For animating from undefined, .attr() reads the\n                            // property as the starting point\n                            if (animateIn) {\n                                graphic['stroke-width'] = inheritedStrokeWidth;\n                            }\n                            // For animating to undefined\n                            if (animateOut) {\n                                params['stroke-width'] = inheritedStrokeWidth;\n                            }\n                        }\n                        const ret = animate.call(graphic, params, options, animateOut ? function () {\n                            // Remove the attribute after finished animation\n                            graphic.element.removeAttribute('stroke-width');\n                            delete graphic['stroke-width'];\n                            // Proceed\n                            if (complete) {\n                                complete.apply(this, arguments);\n                            }\n                        } : complete);\n                        return ret;\n                    };\n                }\n            });\n        }\n        // Apply the SVG transform\n        transformGroups.forEach((transformGroup, i) => {\n            const view = i === 0 ? mapView : mapView.insets[i - 1], svgTransform = view.getSVGTransform(), strokeWidth = MapSeries_pick(this.getStrokeWidth(this.options), 1 // Styled mode\n            );\n            /*\n            Animate or move to the new zoom level. In order to prevent\n            flickering as the different transform components are set out of sync\n            (#5991), we run a fake animator attribute and set scale and\n            translation synchronously in the same step.\n\n            A possible improvement to the API would be to handle this in the\n            renderer or animation engine itself, to ensure that when we are\n            animating multiple properties, we make sure that each step for each\n            property is performed in the same step. Also, for symbols and for\n            transform properties, it should induce a single updateTransform and\n            symbolAttr call.\n            */\n            const scale = svgTransform.scaleX, flipFactor = svgTransform.scaleY > 0 ? 1 : -1;\n            const animatePoints = (scale) => {\n                (series.points || []).forEach((point) => {\n                    const graphic = point.graphic;\n                    let strokeWidth;\n                    if (graphic?.['stroke-width'] &&\n                        (strokeWidth = this.getStrokeWidth(point.options))) {\n                        graphic.attr({\n                            'stroke-width': strokeWidth / scale\n                        });\n                    }\n                });\n            };\n            if (renderer.globalAnimation &&\n                chart.hasRendered &&\n                mapView.allowTransformAnimation) {\n                const startTranslateX = Number(transformGroup.attr('translateX'));\n                const startTranslateY = Number(transformGroup.attr('translateY'));\n                const startScale = Number(transformGroup.attr('scaleX'));\n                const step = (now, fx) => {\n                    const scaleStep = startScale +\n                        (scale - startScale) * fx.pos;\n                    transformGroup.attr({\n                        translateX: (startTranslateX + (svgTransform.translateX - startTranslateX) * fx.pos),\n                        translateY: (startTranslateY + (svgTransform.translateY - startTranslateY) * fx.pos),\n                        scaleX: scaleStep,\n                        scaleY: scaleStep * flipFactor,\n                        'stroke-width': strokeWidth / scaleStep\n                    });\n                    animatePoints(scaleStep); // #18166\n                };\n                const animOptions = MapSeries_merge(animObject(renderer.globalAnimation)), userStep = animOptions.step;\n                animOptions.step = function () {\n                    if (userStep) {\n                        userStep.apply(this, arguments);\n                    }\n                    step.apply(this, arguments);\n                };\n                transformGroup\n                    .attr({ animator: 0 })\n                    .animate({ animator: 1 }, animOptions, function () {\n                    if (typeof renderer.globalAnimation !== 'boolean' &&\n                        renderer.globalAnimation.complete) {\n                        // Fire complete only from this place\n                        renderer.globalAnimation.complete({\n                            applyDrilldown: true\n                        });\n                    }\n                    MapSeries_fireEvent(this, 'mapZoomComplete');\n                }.bind(this));\n                // When dragging or first rendering, animation is off\n            }\n            else {\n                stop(transformGroup);\n                transformGroup.attr(MapSeries_merge(svgTransform, { 'stroke-width': strokeWidth / scale }));\n                animatePoints(scale); // #18166\n            }\n        });\n        if (!this.isDrilling) {\n            this.drawMapDataLabels();\n        }\n    }\n    /**\n     * Get the bounding box of all paths in the map combined.\n     *\n     */\n    getProjectedBounds() {\n        if (!this.bounds && this.chart.mapView) {\n            const { insets, projection } = this.chart.mapView, allBounds = [];\n            // Find the bounding box of each point\n            (this.points || []).forEach((point) => {\n                if (point.path || point.geometry) {\n                    // @todo Try to put these two conversions in\n                    // MapPoint.applyOptions\n                    if (typeof point.path === 'string') {\n                        point.path = splitPath(point.path);\n                        // Legacy one-dimensional array\n                    }\n                    else if (MapSeries_isArray(point.path) &&\n                        point.path[0] === 'M') {\n                        point.path = this.chart.renderer\n                            .pathToSegments(point.path);\n                    }\n                    // The first time a map point is used, analyze its box\n                    if (!point.bounds) {\n                        let bounds = point.getProjectedBounds(projection);\n                        if (bounds) {\n                            point.labelrank = MapSeries_pick(point.labelrank, \n                            // Bigger shape, higher rank\n                            ((bounds.x2 - bounds.x1) *\n                                (bounds.y2 - bounds.y1)));\n                            const { midX, midY } = bounds;\n                            if (insets && MapSeries_isNumber(midX) && MapSeries_isNumber(midY)) {\n                                const inset = find(insets, (inset) => inset.isInside({\n                                    x: midX, y: midY\n                                }));\n                                if (inset) {\n                                    // Project again, but with the inset\n                                    // projection\n                                    delete point.projectedPath;\n                                    bounds = point.getProjectedBounds(inset.projection);\n                                    if (bounds) {\n                                        inset.allBounds.push(bounds);\n                                    }\n                                    point.insetIndex = insets.indexOf(inset);\n                                }\n                            }\n                            point.bounds = bounds;\n                        }\n                    }\n                    if (point.bounds && point.insetIndex === void 0) {\n                        allBounds.push(point.bounds);\n                    }\n                }\n            });\n            this.bounds = Maps_MapView.compositeBounds(allBounds);\n        }\n        return this.bounds;\n    }\n    /**\n     * Return the stroke-width either from a series options or point options\n     * object. This function is used by both the map series where the\n     * `borderWidth` sets the stroke-width, and the mapline series where the\n     * `lineWidth` sets the stroke-width.\n     * @private\n     */\n    getStrokeWidth(options) {\n        const pointAttrToOptions = this.pointAttrToOptions;\n        return options[pointAttrToOptions?.['stroke-width'] || 'borderWidth'];\n    }\n    /**\n     * Define hasData function for non-cartesian series. Returns true if the\n     * series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    /**\n     * Get presentational attributes. In the maps series this runs in both\n     * styled and non-styled mode, because colors hold data when a colorAxis is\n     * used.\n     * @private\n     */\n    pointAttribs(point, state) {\n        const { mapView, styledMode } = point.series.chart;\n        const attr = styledMode ?\n            this.colorAttribs(point) :\n            ColumnSeries.prototype.pointAttribs.call(this, point, state);\n        // Individual stroke width\n        let pointStrokeWidth = this.getStrokeWidth(point.options);\n        // Handle state specific border or line width\n        if (state) {\n            const stateOptions = MapSeries_merge(this.options.states &&\n                this.options.states[state], point.options.states &&\n                point.options.states[state] ||\n                {}), stateStrokeWidth = this.getStrokeWidth(stateOptions);\n            if (MapSeries_defined(stateStrokeWidth)) {\n                pointStrokeWidth = stateStrokeWidth;\n            }\n            attr.stroke = stateOptions.borderColor ?? point.color;\n        }\n        if (pointStrokeWidth && mapView) {\n            pointStrokeWidth /= mapView.getScale();\n        }\n        // In order for dash style to avoid being scaled, set the transformed\n        // stroke width on the item\n        const seriesStrokeWidth = this.getStrokeWidth(this.options);\n        if (attr.dashstyle &&\n            mapView &&\n            MapSeries_isNumber(seriesStrokeWidth)) {\n            pointStrokeWidth = seriesStrokeWidth / mapView.getScale();\n        }\n        // Invisible map points means that the data value is removed from the\n        // map, but not the map area shape itself. Instead it is rendered like a\n        // null point. To fully remove a map area, it should be removed from the\n        // mapData.\n        if (!point.visible) {\n            attr.fill = this.options.nullColor;\n        }\n        if (MapSeries_defined(pointStrokeWidth)) {\n            attr['stroke-width'] = pointStrokeWidth;\n        }\n        else {\n            delete attr['stroke-width'];\n        }\n        attr['stroke-linecap'] = attr['stroke-linejoin'] = this.options.linecap;\n        return attr;\n    }\n    updateData() {\n        // #16782\n        if (this.processedData) {\n            return false;\n        }\n        return super.updateData.apply(this, arguments);\n    }\n    /**\n     * Extend setData to call processData and generatePoints immediately.\n     * @private\n     */\n    setData(data, redraw = true, animation, updatePoints) {\n        delete this.bounds;\n        super.setData(data, false, void 0, updatePoints);\n        this.processData();\n        this.generatePoints();\n        if (redraw) {\n            this.chart.redraw(animation);\n        }\n    }\n    dataColumnKeys() {\n        // No x data for maps\n        return this.pointArrayMap;\n    }\n    /**\n     * Extend processData to join in mapData. If the allAreas option is true,\n     * all areas from the mapData are used, and those that don't correspond to a\n     * data value are given null values. The results are stored in\n     * `processedData` in order to avoid mutating `data`.\n     * @private\n     */\n    processData() {\n        const options = this.options, data = options.data, chart = this.chart, chartOptions = chart.options.chart, joinBy = this.joinBy, pointArrayMap = options.keys || this.pointArrayMap, dataUsed = [], mapMap = {}, mapView = this.chart.mapView, mapDataObject = mapView && (\n        // Get map either from series or global\n        MapSeries_isObject(options.mapData, true) ?\n            mapView.getGeoMap(options.mapData) : mapView.geoMap), \n        // Pick up transform definitions for chart\n        mapTransforms = chart.mapTransforms =\n            chartOptions.mapTransforms ||\n                mapDataObject?.['hc-transform'] ||\n                chart.mapTransforms;\n        let mapPoint, props;\n        // Cache cos/sin of transform rotation angle\n        if (mapTransforms) {\n            MapSeries_objectEach(mapTransforms, (transform) => {\n                if (transform.rotation) {\n                    transform.cosAngle = Math.cos(transform.rotation);\n                    transform.sinAngle = Math.sin(transform.rotation);\n                }\n            });\n        }\n        let mapData;\n        if (MapSeries_isArray(options.mapData)) {\n            mapData = options.mapData;\n        }\n        else if (mapDataObject && mapDataObject.type === 'FeatureCollection') {\n            this.mapTitle = mapDataObject.title;\n            mapData = external_highcharts_src_js_default_default().geojson(mapDataObject, this.type, this);\n        }\n        // Reset processedData\n        this.processedData = [];\n        const processedData = this.processedData;\n        // Pick up numeric values, add index. Convert Array point definitions to\n        // objects using pointArrayMap.\n        if (data) {\n            let val;\n            for (let i = 0, iEnd = data.length; i < iEnd; ++i) {\n                val = data[i];\n                if (MapSeries_isNumber(val)) {\n                    processedData[i] = {\n                        value: val\n                    };\n                }\n                else if (MapSeries_isArray(val)) {\n                    let ix = 0;\n                    processedData[i] = {};\n                    // Automatically copy first item to hc-key if there is\n                    // an extra leading string\n                    if (!options.keys &&\n                        val.length > pointArrayMap.length &&\n                        typeof val[0] === 'string') {\n                        processedData[i]['hc-key'] = val[0];\n                        ++ix;\n                    }\n                    // Run through pointArrayMap and what's left of the\n                    // point data array in parallel, copying over the values\n                    for (let j = 0; j < pointArrayMap.length; ++j, ++ix) {\n                        if (pointArrayMap[j] &&\n                            typeof val[ix] !== 'undefined') {\n                            if (pointArrayMap[j].indexOf('.') > 0) {\n                                Map_MapPoint.prototype.setNestedProperty(processedData[i], val[ix], pointArrayMap[j]);\n                            }\n                            else {\n                                processedData[i][pointArrayMap[j]] = val[ix];\n                            }\n                        }\n                    }\n                }\n                else {\n                    processedData[i] = data[i];\n                }\n                if (joinBy &&\n                    joinBy[0] === '_i') {\n                    processedData[i]._i = i;\n                }\n            }\n        }\n        if (mapData) {\n            this.mapData = mapData;\n            this.mapMap = {};\n            for (let i = 0; i < mapData.length; i++) {\n                mapPoint = mapData[i];\n                props = mapPoint.properties;\n                mapPoint._i = i;\n                // Copy the property over to root for faster access\n                if (joinBy[0] && props && props[joinBy[0]]) {\n                    mapPoint[joinBy[0]] = props[joinBy[0]];\n                }\n                mapMap[mapPoint[joinBy[0]]] = mapPoint;\n            }\n            this.mapMap = mapMap;\n            // Registered the point codes that actually hold data\n            if (joinBy[1]) {\n                const joinKey = joinBy[1];\n                processedData.forEach((pointOptions) => {\n                    const mapKey = getNestedProperty(joinKey, pointOptions);\n                    if (mapMap[mapKey]) {\n                        dataUsed.push(mapMap[mapKey]);\n                    }\n                });\n            }\n            if (options.allAreas) {\n                // Register the point codes that actually hold data\n                if (joinBy[1]) {\n                    const joinKey = joinBy[1];\n                    processedData.forEach((pointOptions) => {\n                        dataUsed.push(getNestedProperty(joinKey, pointOptions));\n                    });\n                }\n                // Add those map points that don't correspond to data, which\n                // will be drawn as null points. Searching a string is faster\n                // than Array.indexOf\n                const dataUsedString = ('|' +\n                    dataUsed\n                        .map(function (point) {\n                        return point && point[joinBy[0]];\n                    })\n                        .join('|') +\n                    '|');\n                mapData.forEach((mapPoint) => {\n                    if (!joinBy[0] ||\n                        dataUsedString.indexOf('|' +\n                            mapPoint[joinBy[0]] +\n                            '|') === -1) {\n                        processedData.push(MapSeries_merge(mapPoint, { value: null }));\n                    }\n                });\n            }\n        }\n        // The processedXData array is used by general chart logic for checking\n        // data length in various scanarios.\n        this.dataTable.rowCount = processedData.length;\n        return void 0;\n    }\n    /**\n     * Extend setOptions by picking up the joinBy option and applying it to a\n     * series property.\n     * @private\n     */\n    setOptions(itemOptions) {\n        const options = super.setOptions(itemOptions);\n        let joinBy = options.joinBy;\n        if (options.joinBy === null) {\n            joinBy = '_i';\n        }\n        if (joinBy) {\n            this.joinBy = splat(joinBy);\n            if (!this.joinBy[1]) {\n                this.joinBy[1] = this.joinBy[0];\n            }\n        }\n        return options;\n    }\n    /**\n     * Add the path option for data points. Find the max value for color\n     * calculation.\n     * @private\n     */\n    translate() {\n        const series = this, doFullTranslate = series.doFullTranslate(), mapView = this.chart.mapView, projection = mapView?.projection;\n        // Recalculate box on updated data\n        if (this.chart.hasRendered && (this.isDirtyData || !this.hasRendered)) {\n            this.processData();\n            this.generatePoints();\n            delete this.bounds;\n            if (mapView &&\n                !mapView.userOptions.center &&\n                !MapSeries_isNumber(mapView.userOptions.zoom) &&\n                mapView.zoom === mapView.minZoom // #18542 don't zoom out if\n            // map is zoomed\n            ) {\n                // Not only recalculate bounds but also fit view\n                mapView.fitToBounds(void 0, void 0, false); // #17012\n            }\n            else {\n                // If center and zoom is defined in user options, get bounds but\n                // don't change view\n                this.getProjectedBounds();\n            }\n        }\n        if (mapView) {\n            const mainSvgTransform = mapView.getSVGTransform();\n            series.points.forEach((point) => {\n                const svgTransform = (MapSeries_isNumber(point.insetIndex) &&\n                    mapView.insets[point.insetIndex].getSVGTransform()) || mainSvgTransform;\n                // Record the middle point (loosely based on centroid),\n                // determined by the middleX and middleY options.\n                if (svgTransform &&\n                    point.bounds &&\n                    MapSeries_isNumber(point.bounds.midX) &&\n                    MapSeries_isNumber(point.bounds.midY)) {\n                    point.plotX = point.bounds.midX * svgTransform.scaleX +\n                        svgTransform.translateX;\n                    point.plotY = point.bounds.midY * svgTransform.scaleY +\n                        svgTransform.translateY;\n                }\n                if (doFullTranslate) {\n                    point.shapeType = 'path';\n                    point.shapeArgs = {\n                        d: Map_MapPoint.getProjectedPath(point, projection)\n                    };\n                }\n                if (!point.hiddenInDataClass) { // #20441\n                    if (point.projectedPath && !point.projectedPath.length) {\n                        point.setVisible(false);\n                    }\n                    else if (!point.visible) {\n                        point.setVisible(true);\n                    }\n                }\n            });\n        }\n        MapSeries_fireEvent(series, 'afterTranslate');\n    }\n    update(options) {\n        // Calculate and set the recommended map view after every series update\n        // if new mapData is set\n        if (options.mapData) {\n            this.chart.mapView?.recommendMapView(this.chart, [\n                this.chart.options.chart.map,\n                ...(this.chart.options.series || []).map((s, i) => {\n                    if (i === this._i) {\n                        return options.mapData;\n                    }\n                    return s.mapData;\n                })\n            ], true);\n        }\n        super.update.apply(this, arguments);\n    }\n}\nMapSeries.defaultOptions = MapSeries_merge(ScatterSeries.defaultOptions, Map_MapSeriesDefaults);\nMapSeries_extend(MapSeries.prototype, {\n    type: 'map',\n    axisTypes: Series_ColorMapComposition.seriesMembers.axisTypes,\n    colorAttribs: Series_ColorMapComposition.seriesMembers.colorAttribs,\n    colorKey: Series_ColorMapComposition.seriesMembers.colorKey,\n    // When tooltip is not shared, this series (and derivatives) requires\n    // direct touch/hover. KD-tree does not apply.\n    directTouch: true,\n    // We need the points' bounding boxes in order to draw the data labels,\n    // so we skip it now and call it from drawPoints instead.\n    drawDataLabels: noop,\n    // No graph for the map series\n    drawGraph: noop,\n    forceDL: true,\n    getCenter: Series_CenteredUtilities.getCenter,\n    getExtremesFromAll: true,\n    getSymbol: noop,\n    isCartesian: false,\n    parallelArrays: Series_ColorMapComposition.seriesMembers.parallelArrays,\n    pointArrayMap: Series_ColorMapComposition.seriesMembers.pointArrayMap,\n    pointClass: Map_MapPoint,\n    // X axis and Y axis must have same translation slope\n    preserveAspectRatio: true,\n    searchPoint: noop,\n    trackerGroups: Series_ColorMapComposition.seriesMembers.trackerGroups,\n    // Get axis extremes from paths, not values\n    useMapGeometry: true\n});\nSeries_ColorMapComposition.compose(MapSeries);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('map', MapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Map_MapSeries = (MapSeries);\n\n;// ./code/es-modules/Series/MapLine/MapLineSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A mapline series is a special case of the map series where the value\n * colors are applied to the strokes rather than the fills. It can also be\n * used for freeform drawing, like dividers, in the map.\n *\n * @sample maps/demo/mapline-mappoint/\n *         Mapline and map-point chart\n * @sample maps/demo/animated-mapline/\n *         Mapline with CSS keyframe animation\n * @sample maps/demo/flight-routes\n *         Flight routes\n *\n * @extends      plotOptions.map\n * @excluding    dragDrop\n * @product      highmaps\n * @optionparent plotOptions.mapline\n */\nconst MapLineSeriesDefaults = {\n    /**\n     * Pixel width of the mapline line.\n     *\n     * @type      {number}\n     * @since     10.3.3\n     * @product   highmaps\n     * @default   1\n     * @apioption plotOptions.mapline.lineWidth\n     */\n    lineWidth: 1,\n    /**\n     * Fill color for the map line shapes\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    fillColor: 'none',\n    legendSymbol: 'lineMarker'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapLine_MapLineSeriesDefaults = (MapLineSeriesDefaults);\n/**\n * A `mapline` series. If the [type](#series.mapline.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.mapline\n * @excluding dataParser, dataURL, dragDrop, marker\n * @product   highmaps\n * @apioption series.mapline\n */\n/**\n * An array of data points for the series. For the `mapline` series type,\n * points can be given in the following ways:\n *\n * 1.  An array of numerical values. In this case, the numerical values\n * will be interpreted as `value` options. Example:\n *\n *  ```js\n *  data: [0, 5, 3, 5]\n *  ```\n *\n * 2.  An array of arrays with 2 values. In this case, the values correspond\n * to `[hc-key, value]`. Example:\n *\n *  ```js\n *     data: [\n *         ['us-ny', 0],\n *         ['us-mi', 5],\n *         ['us-tx', 3],\n *         ['us-ak', 5]\n *     ]\n *  ```\n *\n * 3.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.map.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         value: 6,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         value: 6,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @type      {Array<number|Array<string,(number|null)>|null|*>}\n * @extends   series.map.data\n * @excluding drilldown\n * @product   highmaps\n * @apioption series.mapline.data\n */\n/**\n * Pixel width of the mapline line.\n *\n * @type      {number}\n * @since 10.2.0\n * @product   highmaps\n * @apioption plotOptions.mapline.states.hover.lineWidth\n */\n/**\n * Pixel width of the mapline line.\n *\n * @type      {number|undefined}\n * @since 10.3.3\n * @product   highmaps\n * @apioption series.mapline.data.lineWidth\n */\n/**\n *\n * @type      {number}\n * @product   highmaps\n * @excluding borderWidth\n * @apioption plotOptions.mapline.states.hover\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Series/MapLine/MapLineSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { extend: MapLineSeries_extend, merge: MapLineSeries_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.mapline\n *\n * @augments Highcharts.Series\n */\nclass MapLineSeries extends Map_MapSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get presentational attributes\n     * @private\n     * @function Highcharts.seriesTypes.mapline#pointAttribs\n     */\n    pointAttribs(point, state) {\n        const attr = super.pointAttribs(point, state);\n        // The difference from a map series is that the stroke takes the\n        // point color\n        attr.fill = this.options.fillColor;\n        return attr;\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nMapLineSeries.defaultOptions = MapLineSeries_merge(Map_MapSeries.defaultOptions, MapLine_MapLineSeriesDefaults);\nMapLineSeries_extend(MapLineSeries.prototype, {\n    type: 'mapline',\n    colorProp: 'stroke',\n    pointAttrToOptions: {\n        'stroke': 'color',\n        'stroke-width': 'lineWidth'\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('mapline', MapLineSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapLine_MapLineSeries = ((/* unused pure expression or super */ null && (MapLineSeries)));\n\n;// ./code/es-modules/Series/MapPoint/MapPointPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { scatter: MapPointPoint_ScatterSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isNumber: MapPointPoint_isNumber } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass MapPointPoint extends MapPointPoint_ScatterSeries.prototype.pointClass {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    isValid() {\n        return Boolean(this.options.geometry ||\n            (MapPointPoint_isNumber(this.x) && MapPointPoint_isNumber(this.y)) ||\n            (MapPointPoint_isNumber(this.options.lon) && MapPointPoint_isNumber(this.options.lat)));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapPoint_MapPointPoint = (MapPointPoint);\n\n;// ./code/es-modules/Series/MapPoint/MapPointSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A mappoint series is a special form of scatter series where the points\n * can be laid out in map coordinates on top of a map.\n *\n * @sample maps/demo/mapline-mappoint/\n *         Map-line and map-point series.\n * @sample maps/demo/mappoint-mapmarker\n *         Using the mapmarker symbol for points\n * @sample maps/demo/mappoint-datalabels-mapmarker\n *         Using the mapmarker shape for data labels\n *\n * @extends      plotOptions.scatter\n * @product      highmaps\n * @optionparent plotOptions.mappoint\n */\nconst MapPointSeriesDefaults = {\n    dataLabels: {\n        crop: false,\n        defer: false,\n        enabled: true,\n        formatter: function () {\n            return this.point.name;\n        },\n        overflow: false,\n        style: {\n            /** @internal */\n            color: \"#000000\" /* Palette.neutralColor100 */\n        }\n    },\n    legendSymbol: 'lineMarker'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapPoint_MapPointSeriesDefaults = (MapPointSeriesDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `mappoint` series. If the [type](#series.mappoint.type) option\n * is not specified, it is inherited from [chart.type](#chart.type).\n *\n *\n * @extends   series,plotOptions.mappoint\n * @excluding dataParser, dataURL\n * @product   highmaps\n * @apioption series.mappoint\n */\n/**\n * An array of data points for the series. For the `mappoint` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. The `x` values will be automatically\n *    calculated, either starting at 0 and incremented by 1, or from\n *    `pointStart` and `pointInterval` given in the series options. If the axis\n *    has categories, these will be used. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of arrays with 2 values. In this case, the values correspond\n * to `[hc-key, value]`. Example:\n *\n *  ```js\n *     data: [\n *         ['us-ny', 0],\n *         ['us-mi', 5],\n *         ['us-tx', 3],\n *         ['us-ak', 5]\n *     ]\n *  ```\n *\n * 3. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.mappoint.turboThreshold),\n *    this option is not available.\n *    ```js\n *        data: [{\n *            x: 1,\n *            y: 7,\n *            name: \"Point2\",\n *            color: \"#00FF00\"\n *        }, {\n *            x: 1,\n *            y: 4,\n *            name: \"Point1\",\n *            color: \"#FF00FF\"\n *        }]\n *    ```\n *\n * @type      {Array<number|Array<number,(number|null)>|null|*>}\n * @extends   series.map.data\n * @excluding labelrank, middleX, middleY, path, value\n * @product   highmaps\n * @apioption series.mappoint.data\n */\n/**\n * The geometry of a point.\n *\n * To achieve a better separation between the structure and the data,\n * it is recommended to use `mapData` to define the geometry instead\n * of defining it on the data points themselves.\n *\n * The geometry object is compatible to that of a `feature` in geoJSON, so\n * features of geoJSON can be passed directly into the `data`, optionally\n * after first filtering and processing it.\n *\n * @sample maps/series/mappoint-line-geometry/\n *         Map point and line geometry\n *\n * @type      {Object}\n * @since 9.3.0\n * @product   highmaps\n * @apioption series.mappoint.data.geometry\n */\n/**\n * The geometry type, which in case of the `mappoint` series is always `Point`.\n *\n * @type      {string}\n * @since 9.3.0\n * @product   highmaps\n * @validvalue [\"Point\"]\n * @apioption series.mappoint.data.geometry.type\n */\n/**\n * The geometry coordinates in terms of `[longitude, latitude]`.\n *\n * @type      {Highcharts.LonLatArray}\n * @since 9.3.0\n * @product   highmaps\n * @apioption series.mappoint.data.geometry.coordinates\n */\n/**\n * The latitude of the point. Must be combined with the `lon` option\n * to work. Overrides `x` and `y` values.\n *\n * @sample {highmaps} maps/demo/mappoint-latlon/\n *         Point position by lat/lon\n *\n * @type      {number}\n * @since     1.1.0\n * @product   highmaps\n * @apioption series.mappoint.data.lat\n */\n/**\n * The longitude of the point. Must be combined with the `lon` option\n * to work. Overrides `x` and `y` values.\n *\n * @sample {highmaps} maps/demo/mappoint-latlon/\n *         Point position by lat/lon\n *\n * @type      {number}\n * @since     1.1.0\n * @product   highmaps\n * @apioption series.mappoint.data.lon\n */\n/**\n * The x coordinate of the point in terms of projected units.\n *\n * @sample {highmaps} maps/series/mapline-mappoint-path-xy/\n *         Map point demo\n *\n * @type      {number}\n * @product   highmaps\n * @apioption series.mappoint.data.x\n */\n/**\n * The x coordinate of the point in terms of projected units.\n *\n * @sample {highmaps} maps/series/mapline-mappoint-path-xy/\n *         Map point demo\n *\n * @type      {number|null}\n * @product   highmaps\n * @apioption series.mappoint.data.y\n */\n/**\n * @type      {number}\n * @product   highmaps\n * @excluding borderColor, borderWidth\n * @apioption plotOptions.mappoint\n */\n(''); // Keeps doclets above in JS file\n\n;// external [\"../highcharts.js\",\"default\",\"Series\",\"types\",\"scatter\"]\nvar external_highcharts_src_js_default_Series_types_scatter_x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var external_highcharts_src_js_default_Series_types_scatter_y = (x) => (() => (x))\n    const external_highcharts_src_js_default_Series_types_scatter_namespaceObject = external_highcharts_src_js_default_Series_types_scatter_x({  });\n;// ./code/es-modules/Series/MapPoint/MapPointSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { noop: MapPointSeries_noop } = (external_highcharts_src_js_default_default());\n\n\n\nconst { map: MapPointSeries_MapSeries, scatter: MapPointSeries_ScatterSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\n\nconst { extend: MapPointSeries_extend, fireEvent: MapPointSeries_fireEvent, isNumber: MapPointSeries_isNumber, merge: MapPointSeries_merge } = (external_highcharts_src_js_default_default());\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.mappoint\n *\n * @augments Highcharts.Series\n */\nclass MapPointSeries extends MapPointSeries_ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.clearBounds = MapPointSeries_MapSeries.prototype.clearBounds;\n        /* eslint-enable valid-jsdoc */\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    drawDataLabels() {\n        super.drawDataLabels();\n        if (this.dataLabelsGroup) {\n            this.dataLabelsGroup.clip(this.chart.clipRect);\n        }\n    }\n    /**\n     * Resolve `lon`, `lat` or `geometry` options and project the resulted\n     * coordinates.\n     *\n     * @private\n     */\n    projectPoint(pointOptions) {\n        const mapView = this.chart.mapView;\n        if (mapView) {\n            const { geometry, lon, lat } = pointOptions;\n            let coordinates = (geometry &&\n                geometry.type === 'Point' &&\n                geometry.coordinates);\n            if (MapPointSeries_isNumber(lon) && MapPointSeries_isNumber(lat)) {\n                coordinates = [lon, lat];\n            }\n            if (coordinates) {\n                return mapView.lonLatToProjectedUnits({\n                    lon: coordinates[0],\n                    lat: coordinates[1]\n                });\n            }\n        }\n    }\n    translate() {\n        const mapView = this.chart.mapView;\n        this.generatePoints();\n        if (this.getProjectedBounds && this.isDirtyData) {\n            delete this.bounds;\n            this.getProjectedBounds(); // Added point needs bounds(#16598)\n        }\n        // Create map based translation\n        if (mapView) {\n            const mainSvgTransform = mapView.getSVGTransform(), { hasCoordinates } = mapView.projection;\n            this.points.forEach((p) => {\n                let { x = void 0, y = void 0 } = p;\n                const svgTransform = (MapPointSeries_isNumber(p.insetIndex) &&\n                    mapView.insets[p.insetIndex].getSVGTransform()) || mainSvgTransform;\n                const xy = (this.projectPoint(p.options) ||\n                    (p.properties &&\n                        this.projectPoint(p.properties)));\n                let didBounds;\n                if (xy) {\n                    x = xy.x;\n                    y = xy.y;\n                    // Map bubbles getting geometry from shape\n                }\n                else if (p.bounds) {\n                    x = p.bounds.midX;\n                    y = p.bounds.midY;\n                    if (svgTransform && MapPointSeries_isNumber(x) && MapPointSeries_isNumber(y)) {\n                        p.plotX = x * svgTransform.scaleX +\n                            svgTransform.translateX;\n                        p.plotY = y * svgTransform.scaleY +\n                            svgTransform.translateY;\n                        didBounds = true;\n                    }\n                }\n                if (MapPointSeries_isNumber(x) && MapPointSeries_isNumber(y)) {\n                    // Establish plotX and plotY\n                    if (!didBounds) {\n                        const plotCoords = mapView.projectedUnitsToPixels({ x, y });\n                        p.plotX = plotCoords.x;\n                        p.plotY = hasCoordinates ?\n                            plotCoords.y :\n                            this.chart.plotHeight - plotCoords.y;\n                    }\n                }\n                else {\n                    p.y = p.plotX = p.plotY = void 0;\n                }\n                p.isInside = this.isPointInside(p);\n                // Find point zone\n                p.zone = this.zones.length ? p.getZone() : void 0;\n            });\n        }\n        MapPointSeries_fireEvent(this, 'afterTranslate');\n    }\n}\nMapPointSeries.defaultOptions = MapPointSeries_merge(MapPointSeries_ScatterSeries.defaultOptions, MapPoint_MapPointSeriesDefaults);\n/* *\n *\n * Extra\n *\n * */\n/* *\n * The mapmarker symbol\n */\nconst mapmarker = (x, y, w, h, options) => {\n    const isLegendSymbol = options && options.context === 'legend';\n    let anchorX, anchorY;\n    if (isLegendSymbol) {\n        anchorX = x + w / 2;\n        anchorY = y + h;\n        // Put the pin in the anchor position (dataLabel.shape)\n    }\n    else if (options &&\n        typeof options.anchorX === 'number' &&\n        typeof options.anchorY === 'number') {\n        anchorX = options.anchorX;\n        anchorY = options.anchorY;\n        // Put the pin in the center and shift upwards (point.marker.symbol)\n    }\n    else {\n        anchorX = x + w / 2;\n        anchorY = y + h / 2;\n        y -= h;\n    }\n    const r = isLegendSymbol ? h / 3 : h / 2;\n    return [\n        ['M', anchorX, anchorY],\n        ['C', anchorX, anchorY, anchorX - r, y + r * 1.5, anchorX - r, y + r],\n        // A rx ry x-axis-rotation large-arc-flag sweep-flag x y\n        ['A', r, r, 1, 1, 1, anchorX + r, y + r],\n        ['C', anchorX + r, y + r * 1.5, anchorX, anchorY, anchorX, anchorY],\n        ['Z']\n    ];\n};\n(external_highcharts_src_js_default_SVGRenderer_default()).prototype.symbols.mapmarker = mapmarker;\nMapPointSeries_extend(MapPointSeries.prototype, {\n    type: 'mappoint',\n    axisTypes: ['colorAxis'],\n    forceDL: true,\n    isCartesian: false,\n    pointClass: MapPoint_MapPointPoint,\n    searchPoint: MapPointSeries_noop,\n    useMapGeometry: true // #16534\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('mappoint', MapPointSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapPoint_MapPointSeries = ((/* unused pure expression or super */ null && (MapPointSeries)));\n/* *\n *\n *  API Options\n *\n * */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/Series/Bubble/BubbleLegendDefaults.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Paweł Potaczek\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * The bubble legend is an additional element in legend which\n * presents the scale of the bubble series. Individual bubble ranges\n * can be defined by user or calculated from series. In the case of\n * automatically calculated ranges, a 1px margin of error is\n * permitted.\n *\n * @since        7.0.0\n * @product      highcharts highstock highmaps\n * @requires     highcharts-more\n * @optionparent legend.bubbleLegend\n */\nconst BubbleLegendDefaults = {\n    /**\n     * The color of the ranges borders, can be also defined for an\n     * individual range.\n     *\n     * @sample highcharts/bubble-legend/similartoseries/\n     *         Similar look to the bubble series\n     * @sample highcharts/bubble-legend/bordercolor/\n     *         Individual bubble border color\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    borderColor: void 0,\n    /**\n     * The width of the ranges borders in pixels, can be also\n     * defined for an individual range.\n     */\n    borderWidth: 2,\n    /**\n     * An additional class name to apply to the bubble legend'\n     * circle graphical elements. This option does not replace\n     * default class names of the graphical element.\n     *\n     * @sample {highcharts} highcharts/css/bubble-legend/\n     *         Styling by CSS\n     *\n     * @type {string}\n     */\n    className: void 0,\n    /**\n     * The main color of the bubble legend. Applies to ranges, if\n     * individual color is not defined.\n     *\n     * @sample highcharts/bubble-legend/similartoseries/\n     *         Similar look to the bubble series\n     * @sample highcharts/bubble-legend/color/\n     *         Individual bubble color\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    color: void 0,\n    /**\n     * An additional class name to apply to the bubble legend's\n     * connector graphical elements. This option does not replace\n     * default class names of the graphical element.\n     *\n     * @sample {highcharts} highcharts/css/bubble-legend/\n     *         Styling by CSS\n     *\n     * @type {string}\n     */\n    connectorClassName: void 0,\n    /**\n     * The color of the connector, can be also defined\n     * for an individual range.\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    connectorColor: void 0,\n    /**\n     * The length of the connectors in pixels. If labels are\n     * centered, the distance is reduced to 0.\n     *\n     * @sample highcharts/bubble-legend/connectorandlabels/\n     *         Increased connector length\n     */\n    connectorDistance: 60,\n    /**\n     * The width of the connectors in pixels.\n     *\n     * @sample highcharts/bubble-legend/connectorandlabels/\n     *         Increased connector width\n     */\n    connectorWidth: 1,\n    /**\n     * Enable or disable the bubble legend.\n     */\n    enabled: false,\n    /**\n     * Options for the bubble legend labels.\n     */\n    labels: {\n        /**\n         * An additional class name to apply to the bubble legend\n         * label graphical elements. This option does not replace\n         * default class names of the graphical element.\n         *\n         * @sample {highcharts} highcharts/css/bubble-legend/\n         *         Styling by CSS\n         *\n         * @type {string}\n         */\n        className: void 0,\n        /**\n         * Whether to allow data labels to overlap.\n         */\n        allowOverlap: false,\n        /**\n         * A format string for the bubble legend labels. Available\n         * variables are the same as for `formatter`.\n         *\n         * @sample highcharts/bubble-legend/format/\n         *         Add a unit\n         *\n         * @type {string}\n         */\n        format: '',\n        /**\n         * Available `this` properties are:\n         *\n         * - `this.value`: The bubble value.\n         *\n         * - `this.radius`: The radius of the bubble range.\n         *\n         * - `this.center`: The center y position of the range.\n         *\n         * @type {Highcharts.FormatterCallbackFunction<Highcharts.BubbleLegendFormatterContextObject>}\n         */\n        formatter: void 0,\n        /**\n         * The alignment of the labels compared to the bubble\n         * legend. Can be one of `left`, `center` or `right`.\n         *\n         * @sample highcharts/bubble-legend/connectorandlabels/\n         *         Labels on left\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'right',\n        /**\n         * CSS styles for the labels.\n         *\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @ignore-option */\n            fontSize: '0.9em',\n            /** @ignore-option */\n            color: \"#000000\" /* Palette.neutralColor100 */\n        },\n        /**\n         * The x position offset of the label relative to the\n         * connector.\n         */\n        x: 0,\n        /**\n         * The y position offset of the label relative to the\n         * connector.\n         */\n        y: 0\n    },\n    /**\n     * Maximum bubble legend range size. If values for ranges are\n     * not specified, the `minSize` and the `maxSize` are calculated\n     * from bubble series.\n     */\n    maxSize: 60, // Number\n    /**\n     * Minimum bubble legend range size. If values for ranges are\n     * not specified, the `minSize` and the `maxSize` are calculated\n     * from bubble series.\n     */\n    minSize: 10, // Number\n    /**\n     * The position of the bubble legend in the legend.\n     * @sample highcharts/bubble-legend/connectorandlabels/\n     *         Bubble legend as last item in legend\n     */\n    legendIndex: 0, // Number\n    /**\n     * Options for specific range. One range consists of bubble,\n     * label and connector.\n     *\n     * @sample highcharts/bubble-legend/ranges/\n     *         Manually defined ranges\n     * @sample highcharts/bubble-legend/autoranges/\n     *         Auto calculated ranges\n     *\n     * @type {Array<*>}\n     */\n    ranges: {\n        /**\n         * Range size value, similar to bubble Z data.\n         * @type {number}\n         */\n        value: void 0,\n        /**\n         * The color of the border for individual range.\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        borderColor: void 0,\n        /**\n         * The color of the bubble for individual range.\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        color: void 0,\n        /**\n         * The color of the connector for individual range.\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        connectorColor: void 0\n    },\n    /**\n     * Whether the bubble legend range value should be represented\n     * by the area or the width of the bubble. The default, area,\n     * corresponds best to the human perception of the size of each\n     * bubble.\n     *\n     * @sample highcharts/bubble-legend/ranges/\n     *         Size by width\n     *\n     * @type {Highcharts.BubbleSizeByValue}\n     */\n    sizeBy: 'area',\n    /**\n     * When this is true, the absolute value of z determines the\n     * size of the bubble. This means that with the default\n     * zThreshold of 0, a bubble of value -1 will have the same size\n     * as a bubble of value 1, while a bubble of value 0 will have a\n     * smaller size according to minSize.\n     */\n    sizeByAbsoluteValue: false,\n    /**\n     * Define the visual z index of the bubble legend.\n     */\n    zIndex: 1,\n    /**\n     * Ranges with lower value than zThreshold are skipped.\n     */\n    zThreshold: 0\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bubble_BubbleLegendDefaults = (BubbleLegendDefaults);\n\n;// ./code/es-modules/Series/Bubble/BubbleLegendItem.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Paweł Potaczek\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { noop: BubbleLegendItem_noop } = (external_highcharts_src_js_default_default());\n\nconst { arrayMax, arrayMin, isNumber: BubbleLegendItem_isNumber, merge: BubbleLegendItem_merge, pick: BubbleLegendItem_pick, stableSort } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * BubbleLegend class.\n *\n * @private\n * @class\n * @name Highcharts.BubbleLegend\n * @param {Highcharts.LegendBubbleLegendOptions} options\n * Options of BubbleLegendItem.\n *\n * @param {Highcharts.Legend} legend\n * Legend of item.\n */\nclass BubbleLegendItem {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(options, legend) {\n        this.setState = BubbleLegendItem_noop;\n        this.init(options, legend);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create basic bubbleLegend properties similar to item in legend.\n     * @private\n     */\n    init(options, legend) {\n        this.options = options;\n        this.visible = true;\n        this.chart = legend.chart;\n        this.legend = legend;\n    }\n    /**\n     * Depending on the position option, add bubbleLegend to legend items.\n     *\n     * @private\n     *\n     * @param {Array<(Highcharts.Point|Highcharts.Series)>} items\n     *        All legend items\n     */\n    addToLegend(items) {\n        // Insert bubbleLegend into legend items\n        items.splice(this.options.legendIndex, 0, this);\n    }\n    /**\n     * Calculate ranges, sizes and call the next steps of bubbleLegend\n     * creation.\n     *\n     * @private\n     *\n     * @param {Highcharts.Legend} legend\n     *        Legend instance\n     */\n    drawLegendSymbol(legend) {\n        const itemDistance = BubbleLegendItem_pick(legend.options.itemDistance, 20), legendItem = this.legendItem || {}, options = this.options, ranges = options.ranges, connectorDistance = options.connectorDistance;\n        let connectorSpace;\n        // Do not create bubbleLegend now if ranges or ranges values are not\n        // specified or if are empty array.\n        if (!ranges || !ranges.length || !BubbleLegendItem_isNumber(ranges[0].value)) {\n            legend.options.bubbleLegend.autoRanges = true;\n            return;\n        }\n        // Sort ranges to right render order\n        stableSort(ranges, function (a, b) {\n            return b.value - a.value;\n        });\n        this.ranges = ranges;\n        this.setOptions();\n        this.render();\n        // Get max label size\n        const maxLabel = this.getMaxLabelSize(), radius = this.ranges[0].radius, size = radius * 2;\n        // Space for connectors and labels.\n        connectorSpace =\n            connectorDistance - radius + maxLabel.width;\n        connectorSpace = connectorSpace > 0 ? connectorSpace : 0;\n        this.maxLabel = maxLabel;\n        this.movementX = options.labels.align === 'left' ?\n            connectorSpace : 0;\n        legendItem.labelWidth = size + connectorSpace + itemDistance;\n        legendItem.labelHeight = size + maxLabel.height / 2;\n    }\n    /**\n     * Set style options for each bubbleLegend range.\n     * @private\n     */\n    setOptions() {\n        const ranges = this.ranges, options = this.options, series = this.chart.series[options.seriesIndex], baseline = this.legend.baseline, bubbleAttribs = {\n            zIndex: options.zIndex,\n            'stroke-width': options.borderWidth\n        }, connectorAttribs = {\n            zIndex: options.zIndex,\n            'stroke-width': options.connectorWidth\n        }, labelAttribs = {\n            align: (this.legend.options.rtl ||\n                options.labels.align === 'left') ? 'right' : 'left',\n            zIndex: options.zIndex\n        }, fillOpacity = series.options.marker.fillOpacity, styledMode = this.chart.styledMode;\n        // Allow to parts of styles be used individually for range\n        ranges.forEach(function (range, i) {\n            if (!styledMode) {\n                bubbleAttribs.stroke = BubbleLegendItem_pick(range.borderColor, options.borderColor, series.color);\n                bubbleAttribs.fill = range.color || options.color;\n                if (!bubbleAttribs.fill) {\n                    bubbleAttribs.fill = series.color;\n                    bubbleAttribs['fill-opacity'] = fillOpacity ?? 1;\n                }\n                connectorAttribs.stroke = BubbleLegendItem_pick(range.connectorColor, options.connectorColor, series.color);\n            }\n            // Set options needed for rendering each range\n            ranges[i].radius = this.getRangeRadius(range.value);\n            ranges[i] = BubbleLegendItem_merge(ranges[i], {\n                center: (ranges[0].radius - ranges[i].radius +\n                    baseline)\n            });\n            if (!styledMode) {\n                BubbleLegendItem_merge(true, ranges[i], {\n                    bubbleAttribs: BubbleLegendItem_merge(bubbleAttribs),\n                    connectorAttribs: BubbleLegendItem_merge(connectorAttribs),\n                    labelAttribs: labelAttribs\n                });\n            }\n        }, this);\n    }\n    /**\n     * Calculate radius for each bubble range,\n     * used code from BubbleSeries.js 'getRadius' method.\n     *\n     * @private\n     *\n     * @param {number} value\n     *        Range value\n     *\n     * @return {number|null}\n     *         Radius for one range\n     */\n    getRangeRadius(value) {\n        const options = this.options, seriesIndex = this.options.seriesIndex, bubbleSeries = this.chart.series[seriesIndex], zMax = options.ranges[0].value, zMin = options.ranges[options.ranges.length - 1].value, minSize = options.minSize, maxSize = options.maxSize;\n        return bubbleSeries.getRadius.call(this, zMin, zMax, minSize, maxSize, value);\n    }\n    /**\n     * Render the legendItem group.\n     * @private\n     */\n    render() {\n        const legendItem = this.legendItem || {}, renderer = this.chart.renderer, zThreshold = this.options.zThreshold;\n        if (!this.symbols) {\n            this.symbols = {\n                connectors: [],\n                bubbleItems: [],\n                labels: []\n            };\n        }\n        // Nesting SVG groups to enable handleOverflow\n        legendItem.symbol = renderer.g('bubble-legend');\n        legendItem.label = renderer.g('bubble-legend-item')\n            .css(this.legend.itemStyle || {});\n        // To enable default 'hideOverlappingLabels' method\n        legendItem.symbol.translateX = 0;\n        legendItem.symbol.translateY = 0;\n        // To use handleOverflow method\n        legendItem.symbol.add(legendItem.label);\n        legendItem.label.add(legendItem.group);\n        for (const range of this.ranges) {\n            if (range.value >= zThreshold) {\n                this.renderRange(range);\n            }\n        }\n        this.hideOverlappingLabels();\n    }\n    /**\n     * Render one range, consisting of bubble symbol, connector and label.\n     *\n     * @private\n     *\n     * @param {Highcharts.LegendBubbleLegendRangesOptions} range\n     *        Range options\n     */\n    renderRange(range) {\n        const mainRange = this.ranges[0], legend = this.legend, options = this.options, labelsOptions = options.labels, chart = this.chart, bubbleSeries = chart.series[options.seriesIndex], renderer = chart.renderer, symbols = this.symbols, labels = symbols.labels, elementCenter = range.center, absoluteRadius = Math.abs(range.radius), connectorDistance = options.connectorDistance || 0, labelsAlign = labelsOptions.align, rtl = legend.options.rtl, borderWidth = options.borderWidth, connectorWidth = options.connectorWidth, posX = mainRange.radius || 0, posY = elementCenter - absoluteRadius -\n            borderWidth / 2 + connectorWidth / 2, crispMovement = (posY % 1 ? 1 : 0.5) -\n            (connectorWidth % 2 ? 0 : 0.5), styledMode = renderer.styledMode;\n        let connectorLength = rtl || labelsAlign === 'left' ?\n            -connectorDistance : connectorDistance;\n        // Set options for centered labels\n        if (labelsAlign === 'center') {\n            connectorLength = 0; // Do not use connector\n            options.connectorDistance = 0;\n            range.labelAttribs.align = 'center';\n        }\n        // Render bubble symbol\n        symbols.bubbleItems.push(renderer\n            .circle(posX, elementCenter + crispMovement, absoluteRadius)\n            .attr(styledMode ? {} : range.bubbleAttribs)\n            .addClass((styledMode ?\n            'highcharts-color-' +\n                bubbleSeries.colorIndex + ' ' :\n            '') +\n            'highcharts-bubble-legend-symbol ' +\n            (options.className || '')).add(this.legendItem.symbol));\n        // Render connector\n        symbols.connectors.push(renderer\n            .path(renderer.crispLine([\n            ['M', posX, posY],\n            ['L', posX + connectorLength, posY]\n        ], options.connectorWidth))\n            .attr((styledMode ? {} : range.connectorAttribs))\n            .addClass((styledMode ?\n            'highcharts-color-' +\n                this.options.seriesIndex + ' ' : '') +\n            'highcharts-bubble-legend-connectors ' +\n            (options.connectorClassName || '')).add(this.legendItem.symbol));\n        // Render label\n        const label = renderer\n            .text(this.formatLabel(range))\n            .attr((styledMode ? {} : range.labelAttribs))\n            .css(styledMode ? {} : labelsOptions.style)\n            .addClass('highcharts-bubble-legend-labels ' +\n            (options.labels.className || '')).add(this.legendItem.symbol);\n        // Now that the label is added we can read the bounding box and\n        // vertically align\n        const position = {\n            x: posX + connectorLength + options.labels.x,\n            y: posY + options.labels.y + label.getBBox().height * 0.4\n        };\n        label.attr(position);\n        labels.push(label);\n        // To enable default 'hideOverlappingLabels' method\n        label.placed = true;\n        label.alignAttr = position;\n    }\n    /**\n     * Get the label which takes up the most space.\n     * @private\n     */\n    getMaxLabelSize() {\n        const labels = this.symbols.labels;\n        let maxLabel, labelSize;\n        labels.forEach(function (label) {\n            labelSize = label.getBBox(true);\n            if (maxLabel) {\n                maxLabel = labelSize.width > maxLabel.width ?\n                    labelSize : maxLabel;\n            }\n            else {\n                maxLabel = labelSize;\n            }\n        });\n        return maxLabel || {};\n    }\n    /**\n     * Get formatted label for range.\n     *\n     * @private\n     *\n     * @param {Highcharts.LegendBubbleLegendRangesOptions} range\n     *        Range options\n     *\n     * @return {string}\n     *         Range label text\n     */\n    formatLabel(range) {\n        const options = this.options, formatter = options.labels.formatter, format = options.labels.format;\n        const { numberFormatter } = this.chart;\n        return format ? external_highcharts_src_js_default_Templating_default().format(format, range, this.chart) :\n            formatter ? formatter.call(range) :\n                numberFormatter(range.value, 1);\n    }\n    /**\n     * By using default chart 'hideOverlappingLabels' method, hide or show\n     * labels and connectors.\n     * @private\n     */\n    hideOverlappingLabels() {\n        const chart = this.chart, allowOverlap = this.options.labels.allowOverlap, symbols = this.symbols;\n        if (!allowOverlap && symbols) {\n            chart.hideOverlappingLabels(symbols.labels);\n            // Hide or show connectors\n            symbols.labels.forEach(function (label, index) {\n                if (!label.newOpacity) {\n                    symbols.connectors[index].hide();\n                }\n                else if (label.newOpacity !== label.oldOpacity) {\n                    symbols.connectors[index].show();\n                }\n            });\n        }\n    }\n    /**\n     * Calculate ranges from created series.\n     *\n     * @private\n     *\n     * @return {Array<Highcharts.LegendBubbleLegendRangesOptions>}\n     *         Array of range objects\n     */\n    getRanges() {\n        const bubbleLegend = this.legend.bubbleLegend, series = bubbleLegend.chart.series, rangesOptions = bubbleLegend.options.ranges;\n        let ranges, zData, minZ = Number.MAX_VALUE, maxZ = -Number.MAX_VALUE;\n        series.forEach(function (s) {\n            // Find the min and max Z, like in bubble series\n            if (s.isBubble && !s.ignoreSeries) {\n                zData = s.getColumn('z').filter(BubbleLegendItem_isNumber);\n                if (zData.length) {\n                    minZ = BubbleLegendItem_pick(s.options.zMin, Math.min(minZ, Math.max(arrayMin(zData), s.options.displayNegative === false ?\n                        s.options.zThreshold :\n                        -Number.MAX_VALUE)));\n                    maxZ = BubbleLegendItem_pick(s.options.zMax, Math.max(maxZ, arrayMax(zData)));\n                }\n            }\n        });\n        // Set values for ranges\n        if (minZ === maxZ) {\n            // Only one range if min and max values are the same.\n            ranges = [{ value: maxZ }];\n        }\n        else {\n            ranges = [\n                { value: minZ },\n                { value: (minZ + maxZ) / 2 },\n                { value: maxZ, autoRanges: true }\n            ];\n        }\n        // Prevent reverse order of ranges after redraw\n        if (rangesOptions.length && rangesOptions[0].radius) {\n            ranges.reverse();\n        }\n        // Merge ranges values with user options\n        ranges.forEach(function (range, i) {\n            if (rangesOptions && rangesOptions[i]) {\n                ranges[i] = BubbleLegendItem_merge(rangesOptions[i], range);\n            }\n        });\n        return ranges;\n    }\n    /**\n     * Calculate bubble legend sizes from rendered series.\n     *\n     * @private\n     *\n     * @return {Array<number,number>}\n     *         Calculated min and max bubble sizes\n     */\n    predictBubbleSizes() {\n        const chart = this.chart, legendOptions = chart.legend.options, floating = legendOptions.floating, horizontal = legendOptions.layout === 'horizontal', lastLineHeight = horizontal ? chart.legend.lastLineHeight : 0, plotSizeX = chart.plotSizeX, plotSizeY = chart.plotSizeY, bubbleSeries = chart.series[this.options.seriesIndex], pxSizes = bubbleSeries.getPxExtremes(), minSize = Math.ceil(pxSizes.minPxSize), maxPxSize = Math.ceil(pxSizes.maxPxSize), plotSize = Math.min(plotSizeY, plotSizeX);\n        let calculatedSize, maxSize = bubbleSeries.options.maxSize;\n        // Calculate predicted max size of bubble\n        if (floating || !(/%$/.test(maxSize))) {\n            calculatedSize = maxPxSize;\n        }\n        else {\n            maxSize = parseFloat(maxSize);\n            calculatedSize = ((plotSize + lastLineHeight) * maxSize / 100) /\n                (maxSize / 100 + 1);\n            // Get maxPxSize from bubble series if calculated bubble legend\n            // size will not affect to bubbles series.\n            if ((horizontal && plotSizeY - calculatedSize >=\n                plotSizeX) || (!horizontal && plotSizeX -\n                calculatedSize >= plotSizeY)) {\n                calculatedSize = maxPxSize;\n            }\n        }\n        return [minSize, Math.ceil(calculatedSize)];\n    }\n    /**\n     * Correct ranges with calculated sizes.\n     * @private\n     */\n    updateRanges(min, max) {\n        const bubbleLegendOptions = this.legend.options.bubbleLegend;\n        bubbleLegendOptions.minSize = min;\n        bubbleLegendOptions.maxSize = max;\n        bubbleLegendOptions.ranges = this.getRanges();\n    }\n    /**\n     * Because of the possibility of creating another legend line, predicted\n     * bubble legend sizes may differ by a few pixels, so it is necessary to\n     * correct them.\n     * @private\n     */\n    correctSizes() {\n        const legend = this.legend, chart = this.chart, bubbleSeries = chart.series[this.options.seriesIndex], pxSizes = bubbleSeries.getPxExtremes(), bubbleSeriesSize = pxSizes.maxPxSize, bubbleLegendSize = this.options.maxSize;\n        if (Math.abs(Math.ceil(bubbleSeriesSize) - bubbleLegendSize) >\n            1) {\n            this.updateRanges(this.options.minSize, pxSizes.maxPxSize);\n            legend.render();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bubble_BubbleLegendItem = (BubbleLegendItem);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @interface Highcharts.BubbleLegendFormatterContextObject\n */ /**\n* The center y position of the range.\n* @name Highcharts.BubbleLegendFormatterContextObject#center\n* @type {number}\n*/ /**\n* The radius of the bubble range.\n* @name Highcharts.BubbleLegendFormatterContextObject#radius\n* @type {number}\n*/ /**\n* The bubble value.\n* @name Highcharts.BubbleLegendFormatterContextObject#value\n* @type {number}\n*/\n''; // Detach doclets above\n\n;// ./code/es-modules/Series/Bubble/BubbleLegendComposition.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Paweł Potaczek\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { setOptions: BubbleLegendComposition_setOptions } = (external_highcharts_src_js_default_default());\n\nconst { composed: BubbleLegendComposition_composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent: BubbleLegendComposition_addEvent, objectEach: BubbleLegendComposition_objectEach, pushUnique: BubbleLegendComposition_pushUnique, wrap: BubbleLegendComposition_wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * If ranges are not specified, determine ranges from rendered bubble series\n * and render legend again.\n */\nfunction chartDrawChartBox(proceed, options, callback) {\n    const chart = this, legend = chart.legend, bubbleSeries = getVisibleBubbleSeriesIndex(chart) >= 0;\n    let bubbleLegendOptions, bubbleSizes, legendItem;\n    if (legend && legend.options.enabled && legend.bubbleLegend &&\n        legend.options.bubbleLegend.autoRanges && bubbleSeries) {\n        bubbleLegendOptions = legend.bubbleLegend.options;\n        bubbleSizes = legend.bubbleLegend.predictBubbleSizes();\n        legend.bubbleLegend.updateRanges(bubbleSizes[0], bubbleSizes[1]);\n        // Disable animation on init\n        if (!bubbleLegendOptions.placed) {\n            legend.group.placed = false;\n            legend.allItems.forEach((item) => {\n                legendItem = item.legendItem || {};\n                if (legendItem.group) {\n                    legendItem.group.translateY = void 0;\n                }\n            });\n        }\n        // Create legend with bubbleLegend\n        legend.render();\n        // Calculate margins after first rendering the bubble legend\n        if (!bubbleLegendOptions.placed) {\n            chart.getMargins();\n            chart.axes.forEach((axis) => {\n                axis.setScale();\n                axis.updateNames();\n                // Disable axis animation on init\n                BubbleLegendComposition_objectEach(axis.ticks, function (tick) {\n                    tick.isNew = true;\n                    tick.isNewLabel = true;\n                });\n            });\n            chart.getMargins();\n        }\n        bubbleLegendOptions.placed = true;\n        // Call default 'drawChartBox' method.\n        proceed.call(chart, options, callback);\n        // Check bubble legend sizes and correct them if necessary.\n        legend.bubbleLegend.correctSizes();\n        // Correct items positions with different dimensions in legend.\n        retranslateItems(legend, getLinesHeights(legend));\n    }\n    else {\n        proceed.call(chart, options, callback);\n        // Allow color change on static bubble legend after click on legend\n        if (legend && legend.options.enabled && legend.bubbleLegend) {\n            legend.render();\n            retranslateItems(legend, getLinesHeights(legend));\n        }\n    }\n}\n/**\n * Compose classes for use with Bubble series.\n * @private\n *\n * @param {Highcharts.Chart} ChartClass\n * Core chart class to use with Bubble series.\n *\n * @param {Highcharts.Legend} LegendClass\n * Core legend class to use with Bubble series.\n */\nfunction BubbleLegendComposition_compose(ChartClass, LegendClass) {\n    if (BubbleLegendComposition_pushUnique(BubbleLegendComposition_composed, 'Series.BubbleLegend')) {\n        BubbleLegendComposition_setOptions({\n            // Set default bubble legend options\n            legend: {\n                bubbleLegend: Bubble_BubbleLegendDefaults\n            }\n        });\n        BubbleLegendComposition_wrap(ChartClass.prototype, 'drawChartBox', chartDrawChartBox);\n        BubbleLegendComposition_addEvent(LegendClass, 'afterGetAllItems', onLegendAfterGetAllItems);\n        BubbleLegendComposition_addEvent(LegendClass, 'itemClick', onLegendItemClick);\n    }\n}\n/**\n * Check if there is at least one visible bubble series.\n *\n * @private\n * @function getVisibleBubbleSeriesIndex\n * @param {Highcharts.Chart} chart\n * Chart to check.\n * @return {number}\n * First visible bubble series index\n */\nfunction getVisibleBubbleSeriesIndex(chart) {\n    const series = chart.series;\n    let i = 0;\n    while (i < series.length) {\n        if (series[i] &&\n            series[i].isBubble &&\n            series[i].visible &&\n            series[i].dataTable.rowCount) {\n            return i;\n        }\n        i++;\n    }\n    return -1;\n}\n/**\n * Calculate height for each row in legend.\n *\n * @private\n * @function getLinesHeights\n *\n * @param {Highcharts.Legend} legend\n * Legend to calculate from.\n *\n * @return {Array<Highcharts.Dictionary<number>>}\n * Informations about line height and items amount\n */\nfunction getLinesHeights(legend) {\n    const items = legend.allItems, lines = [], length = items.length;\n    let lastLine, legendItem, legendItem2, i = 0, j = 0;\n    for (i = 0; i < length; i++) {\n        legendItem = items[i].legendItem || {};\n        legendItem2 = (items[i + 1] || {}).legendItem || {};\n        if (legendItem.labelHeight) {\n            // For bubbleLegend\n            items[i].itemHeight = legendItem.labelHeight;\n        }\n        if ( // Line break\n        items[i] === items[length - 1] ||\n            legendItem.y !== legendItem2.y) {\n            lines.push({ height: 0 });\n            lastLine = lines[lines.length - 1];\n            // Find the highest item in line\n            for (j; j <= i; j++) {\n                if (items[j].itemHeight > lastLine.height) {\n                    lastLine.height = items[j].itemHeight;\n                }\n            }\n            lastLine.step = i;\n        }\n    }\n    return lines;\n}\n/**\n * Start the bubble legend creation process.\n */\nfunction onLegendAfterGetAllItems(e) {\n    const legend = this, bubbleLegend = legend.bubbleLegend, legendOptions = legend.options, options = legendOptions.bubbleLegend, bubbleSeriesIndex = getVisibleBubbleSeriesIndex(legend.chart);\n    // Remove unnecessary element\n    if (bubbleLegend && bubbleLegend.ranges && bubbleLegend.ranges.length) {\n        // Allow change the way of calculating ranges in update\n        if (options.ranges.length) {\n            options.autoRanges =\n                !!options.ranges[0].autoRanges;\n        }\n        // Update bubbleLegend dimensions in each redraw\n        legend.destroyItem(bubbleLegend);\n    }\n    // Create bubble legend\n    if (bubbleSeriesIndex >= 0 &&\n        legendOptions.enabled &&\n        options.enabled) {\n        options.seriesIndex = bubbleSeriesIndex;\n        legend.bubbleLegend = new Bubble_BubbleLegendItem(options, legend);\n        legend.bubbleLegend.addToLegend(e.allItems);\n    }\n}\n/**\n * Toggle bubble legend depending on the visible status of bubble series.\n */\nfunction onLegendItemClick(e) {\n    // #14080 don't fire this code if click function is prevented\n    if (e.defaultPrevented) {\n        return false;\n    }\n    const legend = this, series = e.legendItem, chart = legend.chart, visible = series.visible;\n    let status;\n    if (legend && legend.bubbleLegend) {\n        // Temporary correct 'visible' property\n        series.visible = !visible;\n        // Save future status for getRanges method\n        series.ignoreSeries = visible;\n        // Check if at lest one bubble series is visible\n        status = getVisibleBubbleSeriesIndex(chart) >= 0;\n        // Hide bubble legend if all bubble series are disabled\n        if (legend.bubbleLegend.visible !== status) {\n            // Show or hide bubble legend\n            legend.update({\n                bubbleLegend: { enabled: status }\n            });\n            legend.bubbleLegend.visible = status; // Restore default status\n        }\n        series.visible = visible;\n    }\n}\n/**\n * Correct legend items translation in case of different elements heights.\n *\n * @private\n * @function Highcharts.Legend#retranslateItems\n *\n * @param {Highcharts.Legend} legend\n * Legend to translate in.\n *\n * @param {Array<Highcharts.Dictionary<number>>} lines\n * Informations about line height and items amount\n */\nfunction retranslateItems(legend, lines) {\n    const items = legend.allItems, rtl = legend.options.rtl;\n    let orgTranslateX, orgTranslateY, movementX, legendItem, actualLine = 0;\n    items.forEach((item, index) => {\n        legendItem = item.legendItem || {};\n        if (!legendItem.group) {\n            return;\n        }\n        orgTranslateX = legendItem.group.translateX || 0;\n        orgTranslateY = legendItem.y || 0;\n        movementX = item.movementX;\n        if (movementX || (rtl && item.ranges)) {\n            movementX = rtl ?\n                orgTranslateX - item.options.maxSize / 2 :\n                orgTranslateX + movementX;\n            legendItem.group.attr({ translateX: movementX });\n        }\n        if (index > lines[actualLine].step) {\n            actualLine++;\n        }\n        legendItem.group.attr({\n            translateY: Math.round(orgTranslateY + lines[actualLine].height / 2)\n        });\n        legendItem.y = orgTranslateY + lines[actualLine].height / 2;\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BubbleLegendComposition = {\n    compose: BubbleLegendComposition_compose\n};\n/* harmony default export */ const Bubble_BubbleLegendComposition = (BubbleLegendComposition);\n\n;// external [\"../highcharts.js\",\"default\",\"Point\"]\nconst external_highcharts_src_js_default_Point_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Point;\nvar external_highcharts_src_js_default_Point_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Point_namespaceObject);\n;// ./code/es-modules/Series/Bubble/BubblePoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { seriesTypes: { scatter: { prototype: { pointClass: BubblePoint_ScatterPoint } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { extend: BubblePoint_extend } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass BubblePoint extends BubblePoint_ScatterPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    haloPath(size) {\n        const computedSize = (size && this.marker ?\n            this.marker.radius ||\n                0 :\n            0) + size;\n        if (this.series.chart.inverted) {\n            const pos = this.pos() || [0, 0], { xAxis, yAxis, chart } = this.series;\n            return chart.renderer.symbols.circle(xAxis.len - pos[1] - computedSize, yAxis.len - pos[0] - computedSize, computedSize * 2, computedSize * 2);\n        }\n        return external_highcharts_src_js_default_Point_default().prototype.haloPath.call(this, \n        // #6067\n        computedSize);\n    }\n}\n/* *\n *\n *  Class Prototype\n *\n * */\nBubblePoint_extend(BubblePoint.prototype, {\n    ttBelow: false\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bubble_BubblePoint = (BubblePoint);\n\n;// ./code/es-modules/Series/Bubble/BubbleSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { composed: BubbleSeries_composed, noop: BubbleSeries_noop } = (external_highcharts_src_js_default_default());\n\nconst { series: Series, seriesTypes: { column: { prototype: BubbleSeries_columnProto }, scatter: BubbleSeries_ScatterSeries } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { addEvent: BubbleSeries_addEvent, arrayMax: BubbleSeries_arrayMax, arrayMin: BubbleSeries_arrayMin, clamp: BubbleSeries_clamp, extend: BubbleSeries_extend, isNumber: BubbleSeries_isNumber, merge: BubbleSeries_merge, pick: BubbleSeries_pick, pushUnique: BubbleSeries_pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add logic to pad each axis with the amount of pixels necessary to avoid the\n * bubbles to overflow.\n */\nfunction onAxisFoundExtremes() {\n    const axisLength = this.len, { coll, isXAxis, min } = this, range = (this.max || 0) - (min || 0);\n    let pxMin = 0, pxMax = axisLength, transA = axisLength / range, hasActiveSeries;\n    if (coll !== 'xAxis' && coll !== 'yAxis') {\n        return;\n    }\n    // Handle padding on the second pass, or on redraw\n    this.series.forEach((series) => {\n        if (series.bubblePadding && series.reserveSpace()) {\n            // Correction for #1673\n            this.allowZoomOutside = true;\n            hasActiveSeries = true;\n            const data = series.getColumn(isXAxis ? 'x' : 'y');\n            if (isXAxis) {\n                (series.onPoint || series).getRadii(0, 0, series);\n                if (series.onPoint) {\n                    series.radii = series.onPoint.radii;\n                }\n            }\n            if (range > 0) {\n                let i = data.length;\n                while (i--) {\n                    if (BubbleSeries_isNumber(data[i]) &&\n                        this.dataMin <= data[i] &&\n                        data[i] <= this.max) {\n                        const radius = series.radii && series.radii[i] || 0;\n                        pxMin = Math.min(((data[i] - min) * transA) - radius, pxMin);\n                        pxMax = Math.max(((data[i] - min) * transA) + radius, pxMax);\n                    }\n                }\n            }\n        }\n    });\n    // Apply the padding to the min and max properties\n    if (hasActiveSeries && range > 0 && !this.logarithmic) {\n        pxMax -= axisLength;\n        transA *= (axisLength +\n            Math.max(0, pxMin) - // #8901\n            Math.min(pxMax, axisLength)) / axisLength;\n        [\n            ['min', 'userMin', pxMin],\n            ['max', 'userMax', pxMax]\n        ].forEach((keys) => {\n            if (typeof BubbleSeries_pick(this.options[keys[0]], this[keys[1]]) === 'undefined') {\n                this[keys[0]] += keys[2] / transA;\n            }\n        });\n    }\n}\n/**\n * If a user has defined categories, it is necessary to retroactively hide any\n * ticks added by the 'onAxisFoundExtremes' function above (#21672).\n *\n * Otherwise they can show up on the axis, alongside user-defined categories.\n */\nfunction onAxisAfterRender() {\n    const { ticks, tickPositions, dataMin = 0, dataMax = 0, categories } = this, type = this.options.type;\n    if ((categories?.length || type === 'category') &&\n        this.series.find((s) => s.bubblePadding)) {\n        let tickCount = tickPositions.length;\n        while (tickCount--) {\n            const tick = ticks[tickPositions[tickCount]], pos = tick.pos || 0;\n            if (pos > dataMax || pos < dataMin) {\n                tick.label?.hide();\n            }\n        }\n    }\n}\n/* *\n *\n *  Class\n *\n * */\nclass BubbleSeries extends BubbleSeries_ScatterSeries {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(AxisClass, ChartClass, LegendClass) {\n        Bubble_BubbleLegendComposition.compose(ChartClass, LegendClass);\n        if (BubbleSeries_pushUnique(BubbleSeries_composed, 'Series.Bubble')) {\n            BubbleSeries_addEvent(AxisClass, 'foundExtremes', onAxisFoundExtremes);\n            BubbleSeries_addEvent(AxisClass, 'afterRender', onAxisAfterRender);\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Perform animation on the bubbles\n     * @private\n     */\n    animate(init) {\n        if (!init &&\n            this.points.length < this.options.animationLimit // #8099\n        ) {\n            this.points.forEach(function (point) {\n                const { graphic, plotX = 0, plotY = 0 } = point;\n                if (graphic && graphic.width) { // URL symbols don't have width\n                    // Start values\n                    if (!this.hasRendered) {\n                        graphic.attr({\n                            x: plotX,\n                            y: plotY,\n                            width: 1,\n                            height: 1\n                        });\n                    }\n                    graphic.animate(this.markerAttribs(point), this.options.animation);\n                }\n            }, this);\n        }\n    }\n    /**\n     * Get the radius for each point based on the minSize, maxSize and each\n     * point's Z value. This must be done prior to Series.translate because\n     * the axis needs to add padding in accordance with the point sizes.\n     * @private\n     */\n    getRadii() {\n        const zData = this.getColumn('z'), yData = this.getColumn('y'), radii = [];\n        let len, i, value, zExtremes = this.chart.bubbleZExtremes;\n        const { minPxSize, maxPxSize } = this.getPxExtremes();\n        // Get the collective Z extremes of all bubblish series. The chart-level\n        // `bubbleZExtremes` are only computed once, and reset on `updatedData`\n        // in any member series.\n        if (!zExtremes) {\n            let zMin = Number.MAX_VALUE;\n            let zMax = -Number.MAX_VALUE;\n            let valid;\n            this.chart.series.forEach((otherSeries) => {\n                if (otherSeries.bubblePadding && otherSeries.reserveSpace()) {\n                    const zExtremes = (otherSeries.onPoint || otherSeries).getZExtremes();\n                    if (zExtremes) {\n                        // Changed '||' to 'pick' because min or max can be 0.\n                        // #17280\n                        zMin = Math.min(BubbleSeries_pick(zMin, zExtremes.zMin), zExtremes.zMin);\n                        zMax = Math.max(BubbleSeries_pick(zMax, zExtremes.zMax), zExtremes.zMax);\n                        valid = true;\n                    }\n                }\n            });\n            if (valid) {\n                zExtremes = { zMin, zMax };\n                this.chart.bubbleZExtremes = zExtremes;\n            }\n            else {\n                zExtremes = { zMin: 0, zMax: 0 };\n            }\n        }\n        // Set the shape type and arguments to be picked up in drawPoints\n        for (i = 0, len = zData.length; i < len; i++) {\n            value = zData[i];\n            // Separate method to get individual radius for bubbleLegend\n            radii.push(this.getRadius(zExtremes.zMin, zExtremes.zMax, minPxSize, maxPxSize, value, yData && yData[i]));\n        }\n        this.radii = radii;\n    }\n    /**\n     * Get the individual radius for one point.\n     * @private\n     */\n    getRadius(zMin, zMax, minSize, maxSize, value, yValue) {\n        const options = this.options, sizeByArea = options.sizeBy !== 'width', zThreshold = options.zThreshold;\n        let zRange = zMax - zMin, pos = 0.5;\n        // #8608 - bubble should be visible when z is undefined\n        if (yValue === null || value === null) {\n            return null;\n        }\n        if (BubbleSeries_isNumber(value)) {\n            // When sizing by threshold, the absolute value of z determines\n            // the size of the bubble.\n            if (options.sizeByAbsoluteValue) {\n                value = Math.abs(value - zThreshold);\n                zMax = zRange = Math.max(zMax - zThreshold, Math.abs(zMin - zThreshold));\n                zMin = 0;\n            }\n            // Issue #4419 - if value is less than zMin, push a radius that's\n            // always smaller than the minimum size\n            if (value < zMin) {\n                return minSize / 2 - 1;\n            }\n            // Relative size, a number between 0 and 1\n            if (zRange > 0) {\n                pos = (value - zMin) / zRange;\n            }\n        }\n        if (sizeByArea && pos >= 0) {\n            pos = Math.sqrt(pos);\n        }\n        return Math.ceil(minSize + pos * (maxSize - minSize)) / 2;\n    }\n    /**\n     * Define hasData function for non-cartesian series.\n     * Returns true if the series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    /**\n     * @private\n     */\n    markerAttribs(point, state) {\n        const attr = super.markerAttribs(point, state), { height = 0, width = 0 } = attr;\n        // Bubble needs a specific `markerAttribs` override because the markers\n        // are rendered into the potentially inverted `series.group`. Unlike\n        // regular markers, which are rendered into the `markerGroup` (#21125).\n        return this.chart.inverted ? BubbleSeries_extend(attr, {\n            x: (point.plotX || 0) - width / 2,\n            y: (point.plotY || 0) - height / 2\n        }) : attr;\n    }\n    /**\n     * @private\n     */\n    pointAttribs(point, state) {\n        const markerOptions = this.options.marker, fillOpacity = markerOptions?.fillOpacity, attr = Series.prototype.pointAttribs.call(this, point, state);\n        attr['fill-opacity'] = fillOpacity ?? 1;\n        return attr;\n    }\n    /**\n     * Extend the base translate method to handle bubble size\n     * @private\n     */\n    translate() {\n        // Run the parent method\n        super.translate.call(this);\n        this.getRadii();\n        this.translateBubble();\n    }\n    translateBubble() {\n        const { data, options, radii } = this, { minPxSize } = this.getPxExtremes();\n        // Set the shape type and arguments to be picked up in drawPoints\n        let i = data.length;\n        while (i--) {\n            const point = data[i], radius = radii ? radii[i] : 0; // #1737\n            // Negative points means negative z values (#9728)\n            if (this.zoneAxis === 'z') {\n                point.negative = (point.z || 0) < (options.zThreshold || 0);\n            }\n            if (BubbleSeries_isNumber(radius) && radius >= minPxSize / 2) {\n                // Shape arguments\n                point.marker = BubbleSeries_extend(point.marker, {\n                    radius,\n                    width: 2 * radius,\n                    height: 2 * radius\n                });\n                // Alignment box for the data label\n                point.dlBox = {\n                    x: point.plotX - radius,\n                    y: point.plotY - radius,\n                    width: 2 * radius,\n                    height: 2 * radius\n                };\n            }\n            else { // Below zThreshold\n                // #1691\n                point.shapeArgs = point.plotY = point.dlBox = void 0;\n                point.isInside = false; // #17281\n            }\n        }\n    }\n    getPxExtremes() {\n        const smallestSize = Math.min(this.chart.plotWidth, this.chart.plotHeight);\n        const getPxSize = (length) => {\n            let isPercent;\n            if (typeof length === 'string') {\n                isPercent = /%$/.test(length);\n                length = parseInt(length, 10);\n            }\n            return isPercent ? smallestSize * length / 100 : length;\n        };\n        const minPxSize = getPxSize(BubbleSeries_pick(this.options.minSize, 8));\n        // Prioritize min size if conflict to make sure bubbles are\n        // always visible. #5873\n        const maxPxSize = Math.max(getPxSize(BubbleSeries_pick(this.options.maxSize, '20%')), minPxSize);\n        return { minPxSize, maxPxSize };\n    }\n    getZExtremes() {\n        const options = this.options, zData = this.getColumn('z').filter(BubbleSeries_isNumber);\n        if (zData.length) {\n            const zMin = BubbleSeries_pick(options.zMin, BubbleSeries_clamp(BubbleSeries_arrayMin(zData), options.displayNegative === false ?\n                (options.zThreshold || 0) :\n                -Number.MAX_VALUE, Number.MAX_VALUE));\n            const zMax = BubbleSeries_pick(options.zMax, BubbleSeries_arrayMax(zData));\n            if (BubbleSeries_isNumber(zMin) && BubbleSeries_isNumber(zMax)) {\n                return { zMin, zMax };\n            }\n        }\n    }\n    /**\n     * @private\n     * @function Highcharts.Series#searchKDTree\n     */\n    searchKDTree(point, compareX, e, suppliedPointEvaluator = BubbleSeries_noop, suppliedBSideCheckEvaluator = BubbleSeries_noop) {\n        suppliedPointEvaluator = (p1, p2, comparisonProp) => {\n            const p1Dist = p1[comparisonProp] || 0;\n            const p2Dist = p2[comparisonProp] || 0;\n            let ret, flip = false;\n            if (p1Dist === p2Dist) {\n                ret = p1.index > p2.index ? p1 : p2;\n            }\n            else if (p1Dist < 0 && p2Dist < 0) {\n                ret = (p1Dist - (p1.marker?.radius || 0) >=\n                    p2Dist - (p2.marker?.radius || 0)) ?\n                    p1 :\n                    p2;\n                flip = true;\n            }\n            else {\n                ret = p1Dist < p2Dist ? p1 : p2;\n            }\n            return [ret, flip];\n        };\n        suppliedBSideCheckEvaluator = (a, b, flip) => !flip && (a > b) || (a < b);\n        return super.searchKDTree(point, compareX, e, suppliedPointEvaluator, suppliedBSideCheckEvaluator);\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * A bubble series is a three dimensional series type where each point\n * renders an X, Y and Z value. Each points is drawn as a bubble where the\n * position along the X and Y axes mark the X and Y values, and the size of\n * the bubble relates to the Z value.\n *\n * @sample {highcharts} highcharts/demo/bubble/\n *         Bubble chart\n *\n * @extends      plotOptions.scatter\n * @excluding    cluster\n * @product      highcharts highstock\n * @requires     highcharts-more\n * @optionparent plotOptions.bubble\n */\nBubbleSeries.defaultOptions = BubbleSeries_merge(BubbleSeries_ScatterSeries.defaultOptions, {\n    dataLabels: {\n        formatter: function () {\n            const { numberFormatter } = this.series.chart;\n            const { z } = this.point;\n            return BubbleSeries_isNumber(z) ? numberFormatter(z, -1) : '';\n        },\n        inside: true,\n        verticalAlign: 'middle'\n    },\n    /**\n     * If there are more points in the series than the `animationLimit`, the\n     * animation won't run. Animation affects overall performance and\n     * doesn't work well with heavy data series.\n     *\n     * @since 6.1.0\n     */\n    animationLimit: 250,\n    /**\n     * Whether to display negative sized bubbles. The threshold is given\n     * by the [zThreshold](#plotOptions.bubble.zThreshold) option, and negative\n     * bubbles can be visualized by setting\n     * [negativeColor](#plotOptions.bubble.negativeColor).\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-negative/\n     *         Negative bubbles\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     3.0\n     * @apioption plotOptions.bubble.displayNegative\n     */\n    /**\n     * @extends   plotOptions.series.marker\n     * @excluding enabled, enabledThreshold, height, radius, width\n     */\n    marker: {\n        lineColor: null, // Inherit from series.color\n        lineWidth: 1,\n        /**\n         * The fill opacity of the bubble markers.\n         */\n        fillOpacity: 0.5,\n        /**\n         * In bubble charts, the radius is overridden and determined based\n         * on the point's data value.\n         *\n         * @ignore-option\n         */\n        radius: null,\n        states: {\n            hover: {\n                radiusPlus: 0\n            }\n        },\n        /**\n         * A predefined shape or symbol for the marker. Possible values are\n         * \"circle\", \"square\", \"diamond\", \"triangle\" and \"triangle-down\".\n         *\n         * Additionally, the URL to a graphic can be given on the form\n         * `url(graphic.png)`. Note that for the image to be applied to\n         * exported charts, its URL needs to be accessible by the export\n         * server.\n         *\n         * Custom callbacks for symbol path generation can also be added to\n         * `Highcharts.SVGRenderer.prototype.symbols`. The callback is then\n         * used by its method name, as shown in the demo.\n         *\n         * @sample {highcharts} highcharts/plotoptions/bubble-symbol/\n         *         Bubble chart with various symbols\n         * @sample {highcharts} highcharts/plotoptions/series-marker-symbol/\n         *         General chart with predefined, graphic and custom markers\n         *\n         * @type  {Highcharts.SymbolKeyValue|string}\n         * @since 5.0.11\n         */\n        symbol: 'circle'\n    },\n    /**\n     * Minimum bubble size. Bubbles will automatically size between the\n     * `minSize` and `maxSize` to reflect the `z` value of each bubble.\n     * Can be either pixels (when no unit is given), or a percentage of\n     * the smallest one of the plot width and height.\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-size/\n     *         Bubble size\n     *\n     * @type    {number|string}\n     * @since   3.0\n     * @product highcharts highstock\n     */\n    minSize: 8,\n    /**\n     * Maximum bubble size. Bubbles will automatically size between the\n     * `minSize` and `maxSize` to reflect the `z` value of each bubble.\n     * Can be either pixels (when no unit is given), or a percentage of\n     * the smallest one of the plot width and height.\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-size/\n     *         Bubble size\n     *\n     * @type    {number|string}\n     * @since   3.0\n     * @product highcharts highstock\n     */\n    maxSize: '20%',\n    /**\n     * When a point's Z value is below the\n     * [zThreshold](#plotOptions.bubble.zThreshold)\n     * setting, this color is used.\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-negative/\n     *         Negative bubbles\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     3.0\n     * @product   highcharts\n     * @apioption plotOptions.bubble.negativeColor\n     */\n    /**\n     * Whether the bubble's value should be represented by the area or the\n     * width of the bubble. The default, `area`, corresponds best to the\n     * human perception of the size of each bubble.\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-sizeby/\n     *         Comparison of area and size\n     *\n     * @type       {Highcharts.BubbleSizeByValue}\n     * @default    area\n     * @since      3.0.7\n     * @apioption  plotOptions.bubble.sizeBy\n     */\n    /**\n     * When this is true, the absolute value of z determines the size of\n     * the bubble. This means that with the default `zThreshold` of 0, a\n     * bubble of value -1 will have the same size as a bubble of value 1,\n     * while a bubble of value 0 will have a smaller size according to\n     * `minSize`.\n     *\n     * @sample    {highcharts} highcharts/plotoptions/bubble-sizebyabsolutevalue/\n     *            Size by absolute value, various thresholds\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.9\n     * @product   highcharts\n     * @apioption plotOptions.bubble.sizeByAbsoluteValue\n     */\n    /**\n     * When this is true, the series will not cause the Y axis to cross\n     * the zero plane (or [threshold](#plotOptions.series.threshold) option)\n     * unless the data actually crosses the plane.\n     *\n     * For example, if `softThreshold` is `false`, a series of 0, 1, 2,\n     * 3 will make the Y axis show negative values according to the\n     * `minPadding` option. If `softThreshold` is `true`, the Y axis starts\n     * at 0.\n     *\n     * @since   4.1.9\n     * @product highcharts\n     */\n    softThreshold: false,\n    states: {\n        hover: {\n            halo: {\n                size: 5\n            }\n        }\n    },\n    tooltip: {\n        pointFormat: '({point.x}, {point.y}), Size: {point.z}'\n    },\n    turboThreshold: 0,\n    /**\n     * The minimum for the Z value range. Defaults to the highest Z value\n     * in the data.\n     *\n     * @see [zMin](#plotOptions.bubble.zMin)\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-zmin-zmax/\n     *         Z has a possible range of 0-100\n     *\n     * @type      {number}\n     * @since     4.0.3\n     * @product   highcharts\n     * @apioption plotOptions.bubble.zMax\n     */\n    /**\n     * @default   z\n     * @apioption plotOptions.bubble.colorKey\n     */\n    /**\n     * The minimum for the Z value range. Defaults to the lowest Z value\n     * in the data.\n     *\n     * @see [zMax](#plotOptions.bubble.zMax)\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-zmin-zmax/\n     *         Z has a possible range of 0-100\n     *\n     * @type      {number}\n     * @since     4.0.3\n     * @product   highcharts\n     * @apioption plotOptions.bubble.zMin\n     */\n    /**\n     * When [displayNegative](#plotOptions.bubble.displayNegative) is `false`,\n     * bubbles with lower Z values are skipped. When `displayNegative`\n     * is `true` and a [negativeColor](#plotOptions.bubble.negativeColor)\n     * is given, points with lower Z is colored.\n     *\n     * @sample {highcharts} highcharts/plotoptions/bubble-negative/\n     *         Negative bubbles\n     *\n     * @since   3.0\n     * @product highcharts\n     */\n    zThreshold: 0,\n    zoneAxis: 'z'\n});\nBubbleSeries_extend(BubbleSeries.prototype, {\n    alignDataLabel: BubbleSeries_columnProto.alignDataLabel,\n    applyZones: BubbleSeries_noop,\n    bubblePadding: true,\n    isBubble: true,\n    keysAffectYAxis: ['y'],\n    pointArrayMap: ['y', 'z'],\n    pointClass: Bubble_BubblePoint,\n    parallelArrays: ['x', 'y', 'z'],\n    trackerGroups: ['group', 'dataLabelsGroup'],\n    specialGroup: 'group', // To allow clipping (#6296)\n    zoneAxis: 'z'\n});\n// On updated data in any series, delete the chart-level Z extremes cache\nBubbleSeries_addEvent(BubbleSeries, 'updatedData', (e) => {\n    delete e.target.chart.bubbleZExtremes;\n});\n// After removing series, delete the chart-level Z extremes cache, #17502.\nBubbleSeries_addEvent(BubbleSeries, 'remove', (e) => {\n    delete e.target.chart.bubbleZExtremes;\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('bubble', BubbleSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Bubble_BubbleSeries = (BubbleSeries);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @typedef {\"area\"|\"width\"} Highcharts.BubbleSizeByValue\n */\n''; // Detach doclets above\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `bubble` series. If the [type](#series.bubble.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.bubble\n * @excluding dataParser, dataURL, legendSymbolColor, stack\n * @product   highcharts highstock\n * @requires  highcharts-more\n * @apioption series.bubble\n */\n/**\n * An array of data points for the series. For the `bubble` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,y,z`. If the first value is a string, it is applied as the name of\n *    the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2\\. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 1, 2],\n *        [1, 5, 5],\n *        [2, 0, 2]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.bubble.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        y: 1,\n *        z: 1,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 5,\n *        z: 4,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.line.data\n * @product   highcharts\n * @apioption series.bubble.data\n */\n/**\n * @extends     series.line.data.marker\n * @excluding   enabledThreshold, height, radius, width\n * @product     highcharts\n * @apioption   series.bubble.data.marker\n */\n/**\n * The size value for each bubble. The bubbles' diameters are computed\n * based on the `z`, and controlled by series options like `minSize`,\n * `maxSize`, `sizeBy`, `zMin` and `zMax`.\n *\n * @type      {number|null}\n * @product   highcharts\n * @apioption series.bubble.data.z\n */\n/**\n * @excluding enabled, enabledThreshold, height, radius, width\n * @apioption series.bubble.marker\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es-modules/Series/MapBubble/MapBubblePoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\nconst { seriesTypes: { map: { prototype: { pointClass: { prototype: mapPointProto } } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { extend: MapBubblePoint_extend } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass MapBubblePoint extends Bubble_BubblePoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    isValid() {\n        return typeof this.z === 'number';\n    }\n}\nMapBubblePoint_extend(MapBubblePoint.prototype, {\n    applyOptions: mapPointProto.applyOptions,\n    getProjectedBounds: mapPointProto.getProjectedBounds\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapBubble_MapBubblePoint = (MapBubblePoint);\n\n;// ./code/es-modules/Series/MapBubble/MapBubbleSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { seriesTypes: { map: { prototype: mapProto }, mappoint: { prototype: MapBubbleSeries_mapPointProto } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { extend: MapBubbleSeries_extend, merge: MapBubbleSeries_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.mapbubble\n *\n * @augments Highcharts.Series\n *\n * @requires BubbleSeries\n * @requires MapPointSeries\n */\nclass MapBubbleSeries extends Bubble_BubbleSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.clearBounds = mapProto.clearBounds;\n    }\n    searchPoint(e, compareX) {\n        return this.searchKDTree({\n            plotX: e.chartX - this.chart.plotLeft,\n            plotY: e.chartY - this.chart.plotTop\n        }, compareX, e);\n    }\n    translate() {\n        MapBubbleSeries_mapPointProto.translate.call(this);\n        this.getRadii();\n        this.translateBubble();\n    }\n}\n/**\n * A map bubble series is a bubble series laid out on top of a map\n * series, where each bubble is tied to a specific map area.\n *\n * @sample maps/demo/map-bubble/\n *         Map bubble chart\n *\n * @extends      plotOptions.bubble\n * @product      highmaps\n * @optionparent plotOptions.mapbubble\n */\nMapBubbleSeries.defaultOptions = MapBubbleSeries_merge(Bubble_BubbleSeries.defaultOptions, {\n    /**\n     * The main color of the series. This color affects both the fill\n     * and the stroke of the bubble. For enhanced control, use `marker`\n     * options.\n     *\n     * @sample {highmaps} maps/plotoptions/mapbubble-color/\n     *         Pink bubbles\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.mapbubble.color\n     */\n    /**\n     * Whether to display negative sized bubbles. The threshold is\n     * given by the [zThreshold](#plotOptions.mapbubble.zThreshold)\n     * option, and negative bubbles can be visualized by setting\n     * [negativeColor](#plotOptions.bubble.negativeColor).\n     *\n     * @type      {boolean}\n     * @default   true\n     * @apioption plotOptions.mapbubble.displayNegative\n     */\n    /**\n     * Color of the line connecting bubbles. The default value is the same\n     * as series' color.\n     *\n     * In styled mode, the color can be defined by the\n     * [colorIndex](#plotOptions.series.colorIndex) option. Also, the series\n     * color can be set with the `.highcharts-series`,\n     * `.highcharts-color-{n}`, `.highcharts-{type}-series` or\n     * `.highcharts-series-{n}` class, or individual classes given by the\n     * `className` option.\n     *\n     *\n     * @sample {highmaps} maps/demo/spider-map/\n     *         Spider map\n     * @sample {highmaps} maps/plotoptions/spider-map-line-color/\n     *         Different line color\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.mapbubble.lineColor\n     */\n    /**\n     * Pixel width of the line connecting bubbles.\n     *\n     * @sample {highmaps} maps/demo/spider-map/\n     *         Spider map\n     *\n     * @product   highmaps\n     * @apioption plotOptions.mapbubble.lineWidth\n     */\n    lineWidth: 0,\n    /**\n     * Maximum bubble size. Bubbles will automatically size between the\n     * `minSize` and `maxSize` to reflect the `z` value of each bubble.\n     * Can be either pixels (when no unit is given), or a percentage of\n     * the smallest one of the plot width and height.\n     *\n     * @sample {highmaps} highcharts/plotoptions/bubble-size/\n     *         Bubble size\n     * @sample {highmaps} maps/demo/spider-map/\n     *         Spider map\n     *\n     * @product   highmaps\n     * @apioption plotOptions.mapbubble.maxSize\n     */\n    /**\n     * Minimum bubble size. Bubbles will automatically size between the\n     * `minSize` and `maxSize` to reflect the `z` value of each bubble.\n     * Can be either pixels (when no unit is given), or a percentage of\n     * the smallest one of the plot width and height.\n     *\n     * @sample {highmaps} maps/demo/map-bubble/\n     *         Bubble size\n     * @sample {highmaps} maps/demo/spider-map/\n     *         Spider map\n     *\n     * @product   highmaps\n     * @apioption plotOptions.mapbubble.minSize\n     */\n    /**\n     * When a point's Z value is below the\n     * [zThreshold](#plotOptions.mapbubble.zThreshold) setting, this\n     * color is used.\n     *\n     * @sample {highmaps} maps/plotoptions/mapbubble-negativecolor/\n     *         Negative color below a threshold\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.mapbubble.negativeColor\n     */\n    /**\n     * Whether the bubble's value should be represented by the area or\n     * the width of the bubble. The default, `area`, corresponds best to\n     * the human perception of the size of each bubble.\n     *\n     * @type       {Highcharts.BubbleSizeByValue}\n     * @default    area\n     * @apioption  plotOptions.mapbubble.sizeBy\n     */\n    /**\n     * When this is true, the absolute value of z determines the size\n     * of the bubble. This means that with the default `zThreshold` of\n     * 0, a bubble of value -1 will have the same size as a bubble of\n     * value 1, while a bubble of value 0 will have a smaller size\n     * according to `minSize`.\n     *\n     * @sample {highmaps} highcharts/plotoptions/bubble-sizebyabsolutevalue/\n     *         Size by absolute value, various thresholds\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     1.1.9\n     * @apioption plotOptions.mapbubble.sizeByAbsoluteValue\n     */\n    /**\n     * The maximum for the Z value range. Defaults to the highest Z value in\n     * the data.\n     *\n     * @see [zMin](#plotOptions.mapbubble.zMin)\n     *\n     * @sample {highmaps} highcharts/plotoptions/bubble-zmin-zmax/\n     *         Z has a possible range of 0-100\n     *\n     * @type      {number}\n     * @since     1.0.3\n     * @apioption plotOptions.mapbubble.zMax\n     */\n    /**\n     * The minimum for the Z value range. Defaults to the lowest Z value\n     * in the data.\n     *\n     * @see [zMax](#plotOptions.mapbubble.zMax)\n     *\n     * @sample {highmaps} highcharts/plotoptions/bubble-zmin-zmax/\n     *         Z has a possible range of 0-100\n     *\n     * @type      {number}\n     * @since     1.0.3\n     * @apioption plotOptions.mapbubble.zMin\n     */\n    /**\n     * When [displayNegative](#plotOptions.mapbubble.displayNegative)\n     * is `false`, bubbles with lower Z values are skipped. When\n     * `displayNegative` is `true` and a\n     * [negativeColor](#plotOptions.mapbubble.negativeColor) is given,\n     * points with lower Z is colored.\n     *\n     * @sample {highmaps} maps/plotoptions/mapbubble-negativecolor/\n     *         Negative color below a threshold\n     *\n     * @type      {number}\n     * @default   0\n     * @apioption plotOptions.mapbubble.zThreshold\n     */\n    /**\n     * @default 500\n     */\n    animationLimit: 500,\n    /**\n     * @type {string|Array<string>}\n     */\n    joinBy: 'hc-key',\n    tooltip: {\n        pointFormat: '{point.name}: {point.z}'\n    }\n});\nMapBubbleSeries_extend(MapBubbleSeries.prototype, {\n    type: 'mapbubble',\n    axisTypes: ['colorAxis'],\n    getProjectedBounds: mapProto.getProjectedBounds,\n    isCartesian: false,\n    // If one single value is passed, it is interpreted as z\n    pointArrayMap: ['z'],\n    pointClass: MapBubble_MapBubblePoint,\n    processData: mapProto.processData,\n    projectPoint: MapBubbleSeries_mapPointProto.projectPoint,\n    kdAxisArray: ['plotX', 'plotY'],\n    setData: mapProto.setData,\n    setOptions: mapProto.setOptions,\n    updateData: mapProto.updateData,\n    useMapGeometry: true,\n    xyFromShape: true\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('mapbubble', MapBubbleSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const MapBubble_MapBubbleSeries = (MapBubbleSeries);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `mapbubble` series. If the [type](#series.mapbubble.type) option\n * is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.mapbubble\n * @excluding dataParser, dataURL\n * @product   highmaps\n * @apioption series.mapbubble\n */\n/**\n * An array of data points for the series. For the `mapbubble` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values\n *    will be interpreted as `z` options. Example:\n *\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.mapbubble.turboThreshold),\n *    this option is not available.\n *\n *    ```js\n *        data: [{\n *            z: 9,\n *            name: \"Point2\",\n *            color: \"#00FF00\"\n *        }, {\n *            z: 10,\n *            name: \"Point1\",\n *            color: \"#FF00FF\"\n *        }]\n *    ```\n *\n * @type      {Array<number|null|*>}\n * @extends   series.mappoint.data\n * @excluding labelrank, middleX, middleY, path, value, x, y, lat, lon\n * @product   highmaps\n * @apioption series.mapbubble.data\n */\n/**\n * While the `x` and `y` values of the bubble are determined by the\n * underlying map, the `z` indicates the actual value that gives the\n * size of the bubble.\n *\n * @sample {highmaps} maps/demo/map-bubble/\n *         Bubble\n *\n * @type      {number|null}\n * @product   highmaps\n * @apioption series.mapbubble.data.z\n */\n/**\n * @excluding enabled, enabledThreshold, height, radius, width\n * @sample {highmaps} maps/plotoptions/mapbubble-symbol\n *         Map bubble with mapmarker symbol\n * @apioption series.mapbubble.marker\n */\n''; // Adds doclets above to transpiled file\n\n;// external [\"../highcharts.js\",\"default\",\"Color\"]\nconst external_highcharts_src_js_default_Color_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Color;\nvar external_highcharts_src_js_default_Color_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Color_namespaceObject);\n;// ./code/es-modules/Series/Heatmap/HeatmapPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { scatter: { prototype: { pointClass: HeatmapPoint_ScatterPoint } } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { clamp: HeatmapPoint_clamp, defined: HeatmapPoint_defined, extend: HeatmapPoint_extend, pick: HeatmapPoint_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass HeatmapPoint extends HeatmapPoint_ScatterPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    applyOptions(options, x) {\n        // #17970, if point is null remove its color, because it may be updated\n        if (this.isNull || this.value === null) {\n            delete this.color;\n        }\n        super.applyOptions(options, x);\n        this.formatPrefix = this.isNull || this.value === null ?\n            'null' : 'point';\n        return this;\n    }\n    /** @private */\n    getCellAttributes() {\n        const point = this, series = point.series, seriesOptions = series.options, xPad = (seriesOptions.colsize || 1) / 2, yPad = (seriesOptions.rowsize || 1) / 2, xAxis = series.xAxis, yAxis = series.yAxis, markerOptions = point.options.marker || series.options.marker, pointPlacement = series.pointPlacementToXValue(), // #7860\n        pointPadding = HeatmapPoint_pick(point.pointPadding, seriesOptions.pointPadding, 0), cellAttr = {\n            x1: HeatmapPoint_clamp(Math.round(xAxis.len -\n                xAxis.translate(point.x - xPad, false, true, false, true, -pointPlacement)), -xAxis.len, 2 * xAxis.len),\n            x2: HeatmapPoint_clamp(Math.round(xAxis.len -\n                xAxis.translate(point.x + xPad, false, true, false, true, -pointPlacement)), -xAxis.len, 2 * xAxis.len),\n            y1: HeatmapPoint_clamp(Math.round(yAxis.translate(point.y - yPad, false, true, false, true)), -yAxis.len, 2 * yAxis.len),\n            y2: HeatmapPoint_clamp(Math.round(yAxis.translate(point.y + yPad, false, true, false, true)), -yAxis.len, 2 * yAxis.len)\n        };\n        const dimensions = [['width', 'x'], ['height', 'y']];\n        // Handle marker's fixed width, and height values including border\n        // and pointPadding while calculating cell attributes.\n        for (const dimension of dimensions) {\n            const prop = dimension[0], direction = dimension[1];\n            let start = direction + '1', end = direction + '2';\n            const side = Math.abs(cellAttr[start] - cellAttr[end]), borderWidth = markerOptions &&\n                markerOptions.lineWidth || 0, plotPos = Math.abs(cellAttr[start] + cellAttr[end]) / 2, widthOrHeight = markerOptions && markerOptions[prop];\n            if (HeatmapPoint_defined(widthOrHeight) && widthOrHeight < side) {\n                const halfCellSize = widthOrHeight / 2 + borderWidth / 2;\n                cellAttr[start] = plotPos - halfCellSize;\n                cellAttr[end] = plotPos + halfCellSize;\n            }\n            // Handle pointPadding\n            if (pointPadding) {\n                if ((direction === 'x' && xAxis.reversed) ||\n                    (direction === 'y' && !yAxis.reversed)) {\n                    start = end;\n                    end = direction + '1';\n                }\n                cellAttr[start] += pointPadding;\n                cellAttr[end] -= pointPadding;\n            }\n        }\n        return cellAttr;\n    }\n    /**\n     * @private\n     */\n    haloPath(size) {\n        if (!size) {\n            return [];\n        }\n        const { x = 0, y = 0, width = 0, height = 0 } = this.shapeArgs || {};\n        return [\n            ['M', x - size, y - size],\n            ['L', x - size, y + height + size],\n            ['L', x + width + size, y + height + size],\n            ['L', x + width + size, y - size],\n            ['Z']\n        ];\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    isValid() {\n        // Undefined is allowed\n        return (this.value !== Infinity &&\n            this.value !== -Infinity);\n    }\n}\nHeatmapPoint_extend(HeatmapPoint.prototype, {\n    dataLabelOnNull: true,\n    moveToTopOnHover: true,\n    ttBelow: false\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Heatmap_HeatmapPoint = (HeatmapPoint);\n\n;// ./code/es-modules/Series/Heatmap/HeatmapSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { isNumber: HeatmapSeriesDefaults_isNumber } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A heatmap is a graphical representation of data where the individual\n * values contained in a matrix are represented as colors.\n *\n * @productdesc {highcharts}\n * Requires `modules/heatmap`.\n *\n * @sample highcharts/demo/heatmap/\n *         Simple heatmap\n * @sample highcharts/demo/heatmap-canvas/\n *         Heavy heatmap\n *\n * @extends      plotOptions.scatter\n * @excluding    animationLimit, cluster, connectEnds, connectNulls,\n *               cropThreshold, dashStyle, dragDrop, findNearestPointBy,\n *               getExtremesFromAll, jitter, legendSymbolColor, linecap,\n *               lineWidth, pointInterval, pointIntervalUnit, pointRange,\n *               pointStart, shadow, softThreshold, stacking, step, threshold\n * @product      highcharts highmaps\n * @optionparent plotOptions.heatmap\n */\nconst HeatmapSeriesDefaults = {\n    /**\n     * Animation is disabled by default on the heatmap series.\n     */\n    animation: false,\n    /**\n     * The border radius for each heatmap item. The border's color and\n     * width can be set in marker options.\n     *\n     * @see [lineColor](#plotOptions.heatmap.marker.lineColor)\n     * @see [lineWidth](#plotOptions.heatmap.marker.lineWidth)\n     */\n    borderRadius: 0,\n    /**\n     * The border width for each heatmap item.\n     */\n    borderWidth: 0,\n    /**\n     * Padding between the points in the heatmap.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     6.0\n     * @apioption plotOptions.heatmap.pointPadding\n     */\n    /**\n     * @default   value\n     * @apioption plotOptions.heatmap.colorKey\n     */\n    /**\n     * The main color of the series. In heat maps this color is rarely used,\n     * as we mostly use the color to denote the value of each point. Unless\n     * options are set in the [colorAxis](#colorAxis), the default value\n     * is pulled from the [options.colors](#colors) array.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.0\n     * @product   highcharts\n     * @apioption plotOptions.heatmap.color\n     */\n    /**\n     * The column size - how many X axis units each column in the heatmap\n     * should span.\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         One day\n     * @sample {highmaps} maps/demo/heatmap/\n     *         One day\n     *\n     * @type      {number}\n     * @default   1\n     * @since     4.0\n     * @product   highcharts highmaps\n     * @apioption plotOptions.heatmap.colsize\n     */\n    /**\n     * The row size - how many Y axis units each heatmap row should span.\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         1 by default\n     * @sample {highmaps} maps/demo/heatmap/\n     *         1 by default\n     *\n     * @type      {number}\n     * @default   1\n     * @since     4.0\n     * @product   highcharts highmaps\n     * @apioption plotOptions.heatmap.rowsize\n     */\n    /**\n     * Make the heatmap render its data points as an interpolated image.\n     *\n     * @sample highcharts/demo/heatmap-interpolation\n     *   Interpolated heatmap image displaying user activity on a website\n     * @sample highcharts/series-heatmap/interpolation\n     *   Interpolated heatmap toggle\n     *\n     */\n    interpolation: false,\n    /**\n     * The color applied to null points. In styled mode, a general CSS class\n     * is applied instead.\n     *\n     * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     */\n    nullColor: \"#f7f7f7\" /* Palette.neutralColor3 */,\n    dataLabels: {\n        formatter: function () {\n            const { numberFormatter } = this.series.chart;\n            const { value } = this.point;\n            return HeatmapSeriesDefaults_isNumber(value) ? numberFormatter(value, -1) : '';\n        },\n        inside: true,\n        verticalAlign: 'middle',\n        crop: false,\n        /**\n         * @ignore-option\n         */\n        overflow: 'allow',\n        padding: 0 // #3837\n    },\n    /**\n     * @excluding radius, enabledThreshold\n     * @since     8.1\n     */\n    marker: {\n        /**\n         * A predefined shape or symbol for the marker. When undefined, the\n         * symbol is pulled from options.symbols. Other possible values are\n         * `'circle'`, `'square'`,`'diamond'`, `'triangle'`,\n         * `'triangle-down'`, `'rect'`, and `'ellipse'`.\n         *\n         * Additionally, the URL to a graphic can be given on this form:\n         * `'url(graphic.png)'`. Note that for the image to be applied to\n         * exported charts, its URL needs to be accessible by the export\n         * server.\n         *\n         * Custom callbacks for symbol path generation can also be added to\n         * `Highcharts.SVGRenderer.prototype.symbols`. The callback is then\n         * used by its method name, as shown in the demo.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-marker-symbol/\n         *         Predefined, graphic and custom markers\n         * @sample {highstock} highcharts/plotoptions/series-marker-symbol/\n         *         Predefined, graphic and custom markers\n         */\n        symbol: 'rect',\n        /** @ignore-option */\n        radius: 0,\n        lineColor: void 0,\n        states: {\n            /**\n             * @excluding radius, radiusPlus\n             */\n            hover: {\n                /**\n                 * Set the marker's fixed width on hover state.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n                 *         70px fixed marker's width and height on hover\n                 *\n                 * @type      {number|undefined}\n                 * @default   undefined\n                 * @product   highcharts highmaps\n                 * @apioption plotOptions.heatmap.marker.states.hover.width\n                 */\n                /**\n                 * Set the marker's fixed height on hover state.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n                 *         70px fixed marker's width and height on hover\n                 *\n                 * @type      {number|undefined}\n                 * @default   undefined\n                 * @product   highcharts highmaps\n                 * @apioption plotOptions.heatmap.marker.states.hover.height\n                 */\n                /**\n                 * The number of pixels to increase the width of the\n                 * selected point.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n                 *         20px greater width and height on hover\n                 *\n                 * @type      {number|undefined}\n                 * @default   undefined\n                 * @product   highcharts highmaps\n                 * @apioption plotOptions.heatmap.marker.states.hover.widthPlus\n                 */\n                /**\n                 * The number of pixels to increase the height of the\n                 * selected point.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n                *          20px greater width and height on hover\n                    *\n                    * @type      {number|undefined}\n                    * @default   undefined\n                    * @product   highcharts highmaps\n                    * @apioption plotOptions.heatmap.marker.states.hover.heightPlus\n                    */\n                /**\n                 * The additional line width for a hovered point.\n                 *\n                 * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n                 *         5 pixels wider lineWidth on hover\n                 * @sample {highmaps} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n                 *         5 pixels wider lineWidth on hover\n                 */\n                lineWidthPlus: 0\n            },\n            /**\n             * @excluding radius\n             */\n            select: {\n            /**\n             * Set the marker's fixed width on select state.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n             *         70px fixed marker's width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.width\n             */\n            /**\n             * Set the marker's fixed height on select state.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n             *         70px fixed marker's width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.height\n             */\n            /**\n             * The number of pixels to increase the width of the\n             * selected point.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n             *         20px greater width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.widthPlus\n             */\n            /**\n             * The number of pixels to increase the height of the\n             * selected point.\n             *\n             * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n             *         20px greater width and height on hover\n             *\n             * @type      {number|undefined}\n             * @default   undefined\n             * @product   highcharts highmaps\n             * @apioption plotOptions.heatmap.marker.states.select.heightPlus\n             */\n            }\n        }\n    },\n    clip: true,\n    /** @ignore-option */\n    pointRange: null, // Dynamically set to colsize by default\n    tooltip: {\n        pointFormat: '{point.x}, {point.y}: {point.value}<br/>'\n    },\n    states: {\n        hover: {\n            /** @ignore-option */\n            halo: false, // #3406, halo is disabled on heatmaps by default\n            /**\n             * How much to brighten the point on interaction. Requires the\n             * main color to be defined in hex or rgb(a) format.\n             *\n             * In styled mode, the hover brightening is by default replaced\n             * with a fill-opacity set in the `.highcharts-point:hover`\n             * rule.\n             */\n            brightness: 0.2\n        }\n    },\n    legendSymbol: 'rectangle'\n};\n/**\n * A `heatmap` series. If the [type](#series.heatmap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @productdesc {highcharts}\n * Requires `modules/heatmap`.\n *\n * @extends   series,plotOptions.heatmap\n * @excluding cropThreshold, dataParser, dataURL, dragDrop ,pointRange, stack,\n * @product   highcharts highmaps\n * @apioption series.heatmap\n */\n/**\n * An array of data points for the series. For the `heatmap` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with 3 or 2 values. In this case, the values\n * correspond to `x,y,value`. If the first value is a string, it is\n * applied as the name of the point, and the `x` value is inferred.\n * The `x` value can also be omitted, in which case the inner arrays\n * should be of length 2\\. Then the `x` value is automatically calculated,\n * either starting at 0 and incremented by 1, or from `pointStart`\n * and `pointInterval` given in the series options.\n *\n *  ```js\n *     data: [\n *         [0, 9, 7],\n *         [1, 10, 4],\n *         [2, 6, 3]\n *     ]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.heatmap.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         x: 1,\n *         y: 3,\n *         value: 10,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         x: 1,\n *         y: 7,\n *         value: 10,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<number>|*>}\n * @extends   series.line.data\n * @product   highcharts highmaps\n * @apioption series.heatmap.data\n */\n/**\n * The color of the point. In heat maps the point color is rarely set\n * explicitly, as we use the color to denote the `value`. Options for\n * this are set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.color\n */\n/**\n * The value of the point, resulting in a color controlled by options\n * as set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.value\n */\n/**\n * The x value of the point. For datetime axes,\n * the X value is the timestamp in milliseconds since 1970.\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.x\n */\n/**\n * The y value of the point.\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.y\n */\n/**\n * Point padding for a single point.\n *\n * @sample maps/plotoptions/tilemap-pointpadding\n *         Point padding on tiles\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.pointPadding\n */\n/**\n * @excluding radius, enabledThreshold\n * @product   highcharts highmaps\n * @since     8.1\n * @apioption series.heatmap.data.marker\n */\n/**\n * @excluding radius, enabledThreshold\n * @product   highcharts highmaps\n * @since     8.1\n * @apioption series.heatmap.marker\n */\n/**\n * @excluding radius, radiusPlus\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.hover\n */\n/**\n * @excluding radius\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.select\n */\n/**\n * @excluding radius, radiusPlus\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.hover\n */\n/**\n * @excluding radius\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.select\n */\n/**\n* Set the marker's fixed width on hover state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n*         5 pixels wider lineWidth on hover\n*\n* @type      {number|undefined}\n* @default   0\n* @product   highcharts highmaps\n* @apioption series.heatmap.marker.states.hover.lineWidthPlus\n*/\n/**\n* Set the marker's fixed width on hover state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n*         70px fixed marker's width and height on hover\n*\n* @type      {number|undefined}\n* @default   undefined\n* @product   highcharts highmaps\n* @apioption series.heatmap.marker.states.hover.width\n*/\n/**\n * Set the marker's fixed height on hover state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.hover.height\n */\n/**\n* The number of pixels to increase the width of the\n* hovered point.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n*         One day\n*\n* @type      {number|undefined}\n* @default   undefined\n* @product   highcharts highmaps\n* @apioption series.heatmap.marker.states.hover.widthPlus\n*/\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.hover.heightPlus\n */\n/**\n * The number of pixels to increase the width of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.select.widthPlus\n */\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.marker.states.select.heightPlus\n */\n/**\n* Set the marker's fixed width on hover state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-linewidthplus\n*         5 pixels wider lineWidth on hover\n*\n* @type      {number|undefined}\n* @default   0\n* @product   highcharts highmaps\n* @apioption series.heatmap.data.marker.states.hover.lineWidthPlus\n*/\n/**\n * Set the marker's fixed width on hover state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.hover.width\n */\n/**\n * Set the marker's fixed height on hover state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.hover.height\n */\n/**\n * The number of pixels to increase the width of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.hover.widthPlus\n */\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.hover.heightPlus\n */\n/**\n* Set the marker's fixed width on select state.\n*\n* @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n*         70px fixed marker's width and height on hover\n*\n* @type      {number|undefined}\n* @default   undefined\n* @product   highcharts highmaps\n* @apioption series.heatmap.data.marker.states.select.width\n*/\n/**\n * Set the marker's fixed height on select state.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-width\n *         70px fixed marker's width and height on hover\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highmaps\n * @apioption series.heatmap.data.marker.states.select.height\n */\n/**\n * The number of pixels to increase the width of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.select.widthPlus\n */\n/**\n * The number of pixels to increase the height of the\n * hovered point.\n *\n * @sample {highcharts} maps/plotoptions/heatmap-marker-states-hover-widthplus\n *         One day\n *\n * @type      {number|undefined}\n * @default   undefined\n * @product   highcharts highstock\n * @apioption series.heatmap.data.marker.states.select.heightPlus\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Heatmap_HeatmapSeriesDefaults = (HeatmapSeriesDefaults);\n\n;// ./code/es-modules/Series/InterpolationUtilities.js\n/* *\n *\n *  (c) 2010-2025 Hubert Kozik\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { doc } = (external_highcharts_src_js_default_default());\n\nconst { defined: InterpolationUtilities_defined, pick: InterpolationUtilities_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Find color of point based on color axis.\n *\n * @function Highcharts.colorFromPoint\n *\n * @param {number | null} value\n *        Value to find corresponding color on the color axis.\n *\n * @param {Highcharts.Point} point\n *        Point to find it's color from color axis.\n *\n * @return {number[]}\n *        Color in RGBa array.\n */\nfunction colorFromPoint(value, point) {\n    const colorAxis = point.series.colorAxis;\n    if (colorAxis) {\n        const rgba = (colorAxis.toColor(value || 0, point)\n            .split(')')[0]\n            .split('(')[1]\n            .split(',')\n            .map((s) => InterpolationUtilities_pick(parseFloat(s), parseInt(s, 10))));\n        rgba[3] = InterpolationUtilities_pick(rgba[3], 1.0) * 255;\n        if (!InterpolationUtilities_defined(value) || !point.visible) {\n            rgba[3] = 0;\n        }\n        return rgba;\n    }\n    return [0, 0, 0, 0];\n}\n/**\n * Method responsible for creating a canvas for interpolation image.\n * @private\n */\nfunction getContext(series) {\n    const { canvas, context } = series;\n    if (canvas && context) {\n        context.clearRect(0, 0, canvas.width, canvas.height);\n    }\n    else {\n        series.canvas = doc.createElement('canvas');\n        series.context = series.canvas.getContext('2d', {\n            willReadFrequently: true\n        }) || void 0;\n        return series.context;\n    }\n    return context;\n}\nconst InterpolationUtilities = {\n    colorFromPoint,\n    getContext\n};\n/* harmony default export */ const Series_InterpolationUtilities = (InterpolationUtilities);\n\n;// ./code/es-modules/Series/Heatmap/HeatmapSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nconst { series: HeatmapSeries_Series, seriesTypes: { column: HeatmapSeries_ColumnSeries, scatter: HeatmapSeries_ScatterSeries } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { prototype: { symbols: HeatmapSeries_symbols } } = (external_highcharts_src_js_default_SVGRenderer_default());\n\nconst { addEvent: HeatmapSeries_addEvent, extend: HeatmapSeries_extend, fireEvent: HeatmapSeries_fireEvent, isNumber: HeatmapSeries_isNumber, merge: HeatmapSeries_merge, pick: HeatmapSeries_pick } = (external_highcharts_src_js_default_default());\n\nconst { colorFromPoint: HeatmapSeries_colorFromPoint, getContext: HeatmapSeries_getContext } = Series_InterpolationUtilities;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.heatmap\n *\n * @augments Highcharts.Series\n */\nclass HeatmapSeries extends HeatmapSeries_ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.valueMax = NaN;\n        this.valueMin = NaN;\n        this.isDirtyCanvas = true;\n        /* eslint-enable valid-jsdoc */\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    drawPoints() {\n        const series = this, seriesOptions = series.options, interpolation = seriesOptions.interpolation, seriesMarkerOptions = seriesOptions.marker || {};\n        if (interpolation) {\n            const { image, chart, xAxis, yAxis } = series, { reversed: xRev = false, len: width } = xAxis, { reversed: yRev = false, len: height } = yAxis, dimensions = { width, height };\n            if (!image || series.isDirtyData || series.isDirtyCanvas) {\n                const ctx = HeatmapSeries_getContext(series), { canvas, options: { colsize = 1, rowsize = 1 }, points, points: { length } } = series, pointsLen = length - 1, colorAxis = (chart.colorAxis && chart.colorAxis[0]);\n                if (canvas && ctx && colorAxis) {\n                    const { min: xMin, max: xMax } = xAxis.getExtremes(), { min: yMin, max: yMax } = yAxis.getExtremes(), xDelta = xMax - xMin, yDelta = yMax - yMin, imgMultiple = 8.0, lastX = Math.round(imgMultiple * ((xDelta / colsize) / imgMultiple)), lastY = Math.round(imgMultiple * ((yDelta / rowsize) / imgMultiple)), [transformX, transformY] = [\n                        [lastX, lastX / xDelta, xRev, 'ceil'],\n                        [lastY, lastY / yDelta, !yRev, 'floor']\n                    ].map(([last, scale, rev, rounding]) => (rev ?\n                        (v) => (Math[rounding](last -\n                            (scale * (v)))) :\n                        (v) => (Math[rounding](scale * v)))), canvasWidth = canvas.width = lastX + 1, canvasHeight = canvas.height = lastY + 1, canvasArea = canvasWidth * canvasHeight, pixelToPointScale = pointsLen / canvasArea, pixelData = new Uint8ClampedArray(canvasArea * 4), pointInPixels = (x, y) => (Math.ceil((canvasWidth * transformY(y - yMin)) +\n                        transformX(x - xMin)) * 4);\n                    series.buildKDTree();\n                    for (let i = 0; i < canvasArea; i++) {\n                        const point = points[Math.ceil(pixelToPointScale * i)], { x, y } = point;\n                        pixelData.set(HeatmapSeries_colorFromPoint(point.value, point), pointInPixels(x, y));\n                    }\n                    ctx.putImageData(new ImageData(pixelData, canvasWidth), 0, 0);\n                    if (image) {\n                        image.attr({\n                            ...dimensions,\n                            href: canvas.toDataURL('image/png', 1)\n                        });\n                    }\n                    else {\n                        series.directTouch = false;\n                        series.image = chart.renderer.image(canvas.toDataURL('image/png', 1))\n                            .attr(dimensions)\n                            .add(series.group);\n                    }\n                }\n                series.isDirtyCanvas = false;\n            }\n            else if (image.width !== width || image.height !== height) {\n                image.attr(dimensions);\n            }\n        }\n        else if (seriesMarkerOptions.enabled || series._hasPointMarkers) {\n            HeatmapSeries_Series.prototype.drawPoints.call(series);\n            series.points.forEach((point) => {\n                if (point.graphic) {\n                    // In styled mode, use CSS, otherwise the fill used in\n                    // the style sheet will take precedence over\n                    // the fill attribute.\n                    point.graphic[series.chart.styledMode ? 'css' : 'animate'](series.colorAttribs(point));\n                    if (point.value === null) { // #15708\n                        point.graphic.addClass('highcharts-null-point');\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * @private\n     */\n    getExtremes() {\n        // Get the extremes from the value data\n        const { dataMin, dataMax } = HeatmapSeries_Series.prototype.getExtremes\n            .call(this, this.getColumn('value'));\n        if (HeatmapSeries_isNumber(dataMin)) {\n            this.valueMin = dataMin;\n        }\n        if (HeatmapSeries_isNumber(dataMax)) {\n            this.valueMax = dataMax;\n        }\n        // Get the extremes from the y data\n        return HeatmapSeries_Series.prototype.getExtremes.call(this);\n    }\n    /**\n     * Override to also allow null points, used when building the k-d-tree for\n     * tooltips in boost mode.\n     * @private\n     */\n    getValidPoints(points, insideOnly) {\n        return HeatmapSeries_Series.prototype.getValidPoints.call(this, points, insideOnly, true);\n    }\n    /**\n     * Define hasData function for non-cartesian series. Returns true if the\n     * series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    /**\n     * Override the init method to add point ranges on both axes.\n     * @private\n     */\n    init() {\n        super.init.apply(this, arguments);\n        const options = this.options;\n        // #3758, prevent resetting in setData\n        options.pointRange = HeatmapSeries_pick(options.pointRange, options.colsize || 1);\n        // General point range\n        this.yAxis.axisPointRange = options.rowsize || 1;\n        // Bind new symbol names\n        HeatmapSeries_symbols.ellipse = HeatmapSeries_symbols.circle;\n        // @todo\n        //\n        // Setting the border radius here is a workaround. It should be set in\n        // the shapeArgs or returned from `markerAttribs`. However,\n        // Series.drawPoints does not pick up markerAttribs to be passed over to\n        // `renderer.symbol`. Also, image symbols are not positioned by their\n        // top left corner like other symbols are. This should be refactored,\n        // then we could save ourselves some tests for .hasImage etc. And the\n        // evaluation of borderRadius would be moved to `markerAttribs`.\n        if (options.marker && HeatmapSeries_isNumber(options.borderRadius)) {\n            options.marker.r = options.borderRadius;\n        }\n    }\n    /**\n     * @private\n     */\n    markerAttribs(point, state) {\n        const shapeArgs = point.shapeArgs || {};\n        if (point.hasImage) {\n            return {\n                x: point.plotX,\n                y: point.plotY\n            };\n        }\n        // Setting width and height attributes on image does not affect on its\n        // dimensions.\n        if (state && state !== 'normal') {\n            const pointMarkerOptions = point.options.marker || {}, seriesMarkerOptions = this.options.marker || {}, seriesStateOptions = (seriesMarkerOptions.states?.[state]) || {}, pointStateOptions = (pointMarkerOptions.states?.[state]) || {};\n            // Set new width and height basing on state options.\n            const width = (pointStateOptions.width ||\n                seriesStateOptions.width ||\n                shapeArgs.width ||\n                0) + (pointStateOptions.widthPlus ||\n                seriesStateOptions.widthPlus ||\n                0);\n            const height = (pointStateOptions.height ||\n                seriesStateOptions.height ||\n                shapeArgs.height ||\n                0) + (pointStateOptions.heightPlus ||\n                seriesStateOptions.heightPlus ||\n                0);\n            // Align marker by the new size.\n            const x = (shapeArgs.x || 0) + ((shapeArgs.width || 0) - width) / 2, y = (shapeArgs.y || 0) + ((shapeArgs.height || 0) - height) / 2;\n            return { x, y, width, height };\n        }\n        return shapeArgs;\n    }\n    /**\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, attr = HeatmapSeries_Series.prototype.pointAttribs.call(series, point, state), seriesOptions = series.options || {}, plotOptions = series.chart.options.plotOptions || {}, seriesPlotOptions = plotOptions.series || {}, heatmapPlotOptions = plotOptions.heatmap || {}, \n        // Get old properties in order to keep backward compatibility\n        borderColor = point?.options.borderColor ||\n            seriesOptions.borderColor ||\n            heatmapPlotOptions.borderColor ||\n            seriesPlotOptions.borderColor, borderWidth = point?.options.borderWidth ||\n            seriesOptions.borderWidth ||\n            heatmapPlotOptions.borderWidth ||\n            seriesPlotOptions.borderWidth ||\n            attr['stroke-width'];\n        // Apply lineColor, or set it to default series color.\n        attr.stroke = (point?.marker?.lineColor ||\n            seriesOptions.marker?.lineColor ||\n            borderColor ||\n            this.color);\n        // Apply old borderWidth property if exists.\n        attr['stroke-width'] = borderWidth;\n        if (state && state !== 'normal') {\n            const stateOptions = HeatmapSeries_merge(seriesOptions.states?.[state], seriesOptions.marker?.states?.[state], point?.options.states?.[state] || {});\n            attr.fill =\n                stateOptions.color ||\n                    external_highcharts_src_js_default_Color_default().parse(attr.fill).brighten(stateOptions.brightness || 0).get();\n            attr.stroke = (stateOptions.lineColor || attr.stroke); // #17896\n        }\n        return attr;\n    }\n    /**\n     * @private\n     */\n    translate() {\n        const series = this, options = series.options, { borderRadius, marker } = options, symbol = marker?.symbol || 'rect', shape = HeatmapSeries_symbols[symbol] ? symbol : 'rect', hasRegularShape = ['circle', 'square'].indexOf(shape) !== -1;\n        series.generatePoints();\n        for (const point of series.points) {\n            const cellAttr = point.getCellAttributes();\n            let x = Math.min(cellAttr.x1, cellAttr.x2), y = Math.min(cellAttr.y1, cellAttr.y2), width = Math.max(Math.abs(cellAttr.x2 - cellAttr.x1), 0), height = Math.max(Math.abs(cellAttr.y2 - cellAttr.y1), 0);\n            point.hasImage = (point.marker?.symbol || symbol || '').indexOf('url') === 0;\n            // If marker shape is regular (square), find the shorter cell's\n            // side.\n            if (hasRegularShape) {\n                const sizeDiff = Math.abs(width - height);\n                x = Math.min(cellAttr.x1, cellAttr.x2) +\n                    (width < height ? 0 : sizeDiff / 2);\n                y = Math.min(cellAttr.y1, cellAttr.y2) +\n                    (width < height ? sizeDiff / 2 : 0);\n                width = height = Math.min(width, height);\n            }\n            if (point.hasImage) {\n                point.marker = { width, height };\n            }\n            point.plotX = point.clientX = (cellAttr.x1 + cellAttr.x2) / 2;\n            point.plotY = (cellAttr.y1 + cellAttr.y2) / 2;\n            point.shapeType = 'path';\n            point.shapeArgs = HeatmapSeries_merge(true, { x, y, width, height }, {\n                d: HeatmapSeries_symbols[shape](x, y, width, height, { r: HeatmapSeries_isNumber(borderRadius) ? borderRadius : 0 })\n            });\n        }\n        HeatmapSeries_fireEvent(series, 'afterTranslate');\n    }\n}\nHeatmapSeries.defaultOptions = HeatmapSeries_merge(HeatmapSeries_ScatterSeries.defaultOptions, Heatmap_HeatmapSeriesDefaults);\nHeatmapSeries_addEvent(HeatmapSeries, 'afterDataClassLegendClick', function () {\n    this.isDirtyCanvas = true;\n    this.drawPoints();\n});\nHeatmapSeries_extend(HeatmapSeries.prototype, {\n    axisTypes: Series_ColorMapComposition.seriesMembers.axisTypes,\n    colorKey: Series_ColorMapComposition.seriesMembers.colorKey,\n    directTouch: true,\n    getExtremesFromAll: true,\n    keysAffectYAxis: ['y'],\n    parallelArrays: Series_ColorMapComposition.seriesMembers.parallelArrays,\n    pointArrayMap: ['y', 'value'],\n    pointClass: Heatmap_HeatmapPoint,\n    specialGroup: 'group',\n    trackerGroups: Series_ColorMapComposition.seriesMembers.trackerGroups,\n    /**\n     * @private\n     */\n    alignDataLabel: HeatmapSeries_ColumnSeries.prototype.alignDataLabel,\n    colorAttribs: Series_ColorMapComposition.seriesMembers.colorAttribs,\n    getSymbol: HeatmapSeries_Series.prototype.getSymbol\n});\nSeries_ColorMapComposition.compose(HeatmapSeries);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('heatmap', HeatmapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Heatmap_HeatmapSeries = ((/* unused pure expression or super */ null && (HeatmapSeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Heatmap series only. Padding between the points in the heatmap.\n * @name Highcharts.Point#pointPadding\n * @type {number|undefined}\n */\n/**\n * Heatmap series only. The value of the point, resulting in a color\n * controlled by options as set in the colorAxis configuration.\n * @name Highcharts.Point#value\n * @type {number|null|undefined}\n */\n/* *\n * @interface Highcharts.PointOptionsObject in parts/Point.ts\n */ /**\n* Heatmap series only. Point padding for a single point.\n* @name Highcharts.PointOptionsObject#pointPadding\n* @type {number|undefined}\n*/ /**\n* Heatmap series only. The value of the point, resulting in a color controlled\n* by options as set in the colorAxis configuration.\n* @name Highcharts.PointOptionsObject#value\n* @type {number|null|undefined}\n*/\n''; // Detach doclets above\n\n;// ./code/es-modules/masters/modules/map.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\n// Classes\nG.ColorMapComposition = Series_ColorMapComposition;\nG.MapChart = G.MapChart || Chart_MapChart;\nG.MapNavigation = G.MapNavigation || Maps_MapNavigation;\nG.MapView = G.MapView || Maps_MapView;\nG.Projection = G.Projection || Maps_Projection;\n// Functions\nG.mapChart = G.Map = G.MapChart.mapChart;\nG.maps = G.MapChart.maps;\nG.geojson = Maps_GeoJSONComposition.geojson;\nG.topo2geo = Maps_GeoJSONComposition.topo2geo;\n// Compositions\nMaps_GeoJSONComposition.compose(G.Chart);\nMapBubble_MapBubbleSeries.compose(G.Axis, G.Chart, G.Legend);\nMaps_MapNavigation.compose(Chart_MapChart, G.Pointer, G.SVGRenderer);\nMaps_MapView.compose(Chart_MapChart);\n// Default Export\n/* harmony default export */ const map_src = ((external_highcharts_src_js_default_default()));\n\nexport { map_src as default };\n"], "names": ["symbols", "__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "CenteredUtilities", "MapChart", "GeometryUtilities", "MapPointer", "ColorMapComposition", "GeoJSONComposition", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "MapNavigationDefaults", "lang", "zoomIn", "zoomOut", "mapNavigation", "buttonOptions", "alignTo", "align", "verticalAlign", "x", "width", "height", "padding", "style", "color", "fontSize", "fontWeight", "theme", "fill", "stroke", "buttons", "onclick", "mapZoom", "text", "y", "mouseWheelSensitivity", "defined", "extend", "pick", "wrap", "totalWheelDeltaTimer", "totalWheelDelta", "onContainerDblClick", "e", "chart", "normalize", "options", "enableDoubleClickZoomTo", "pointer", "inClass", "target", "hoverPoint", "zoomTo", "isInsidePlot", "chartX", "plotLeft", "chartY", "plotTop", "onContainerMouseWheel", "delta", "wheelDelta", "deltaY", "detail", "Math", "abs", "clearTimeout", "setTimeout", "mapView", "zoomBy", "wrapNormalize", "proceed", "chartPosition", "lonLat", "pixelsToLonLat", "wrapZoomOption", "enableTouchZoom", "enabled", "zooming", "pinchType", "apply", "slice", "arguments", "compose", "PointerClass", "pointer<PERSON><PERSON><PERSON>", "Maps_MapPointer", "bottomButton", "w", "h", "r", "brBoxY", "brBoxHeight", "roundedRect", "topButton", "SVGRendererClass", "bottombutton", "topbutton", "setOptions", "composed", "addEvent", "MapNavigation_extend", "merge", "objectEach", "MapNavigation_pick", "pushUnique", "stopEvent", "preventDefault", "stopPropagation", "cancelBubble", "MapNavigation", "MapChartClass", "Maps_MapSymbols", "update", "constructor", "navButtons", "mapNav", "outerHandler", "handler", "navOptions", "length", "pop", "destroy", "renderer", "forExport", "enableButtons", "navButtonsGroup", "g", "attr", "zIndex", "add", "styledMode", "button", "addClass", "title", "push", "path", "element", "hasLoaded", "unbind", "isIntersectRect", "box1", "box2", "expBtnBBox", "exportingGroup", "getBBox", "navBtnsBBox", "aboveExpBtn", "belowExpBtn", "mapNavVerticalAlign", "translateY", "updateEvents", "enableDoubleClickZoom", "unbindDblClick", "container", "enableMouseWheelZoom", "unbindMouseWheel", "initialZoom", "zoom", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "external_highcharts_src_js_default_SVGElement_namespaceObject", "SVGElement", "external_highcharts_src_js_default_SVGElement_default", "column", "columnProto", "seriesTypes", "ColorMapComposition_addEvent", "ColorMapComposition_defined", "onPointAfterSetState", "series", "point", "moveToTopOnHover", "graphic", "stateMarkerGraphic", "css", "pointerEvents", "parentGroup", "state", "id", "href", "url", "visibility", "pointMembers", "dataLabelOnNull", "<PERSON><PERSON><PERSON><PERSON>", "value", "Infinity", "isNaN", "seriesMembers", "colorKey", "axisTypes", "parallelArrays", "pointArrayMap", "trackerGroups", "colorAttribs", "ret", "colorProp", "pointAttribs", "SeriesClass", "pointClass", "Series_ColorMapComposition", "external_highcharts_src_js_default_Series_namespaceObject", "Series", "external_highcharts_src_js_default_Series_default", "deg2rad", "fireEvent", "isNumber", "CenteredUtilities_pick", "<PERSON><PERSON><PERSON><PERSON>", "getCenter", "slicingRoom", "slicedOffset", "plot<PERSON>id<PERSON>", "plotHeight", "centerOption", "center", "smallestSize", "min", "thickness", "handleSlicingRoom", "size", "innerSize", "i", "parseFloat", "positions", "angular", "test", "getStartAndEndRadians", "start", "end", "startAngle", "endAngle", "Series_CenteredUtilities", "external_highcharts_src_js_default_Chart_namespaceObject", "Chart", "external_highcharts_src_js_default_Chart_default", "external_highcharts_src_js_default_SVGRenderer_namespaceObject", "<PERSON><PERSON><PERSON><PERSON>", "external_highcharts_src_js_default_SVGRenderer_default", "getOptions", "MapChart_isNumber", "MapChart_merge", "MapChart_pick", "init", "userOptions", "callback", "defaultCreditsOptions", "credits", "panning", "type", "mapText", "mapTextFull", "tooltip", "followTouchMove", "<PERSON><PERSON><PERSON>", "xProjected", "y<PERSON>rojected", "log", "projection", "inverse", "recommendMapView", "map", "s", "mapData", "maps", "mapChart", "b", "c", "splitPath", "arr", "split", "replace", "item", "pathToSegments", "Chart_MapChart", "Maps_MapUtilities", "boundsFromPath", "x2", "Number", "MAX_VALUE", "x1", "y2", "y1", "validBounds", "for<PERSON>ach", "seg", "max", "MapPoint_boundsFromPath", "ScatterPoint", "scatter", "MapPoint_extend", "MapPoint_isNumber", "MapPoint_pick", "MapPoint", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectedPath", "geometry", "hasCoordinates", "applyOptions", "joinBy", "mapMap", "join<PERSON>ey", "mapKey", "getNestedProperty", "mapPoint", "name", "indexOf", "getProjectedBounds", "bounds", "properties", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "propMiddleLat", "projectedPoint", "forward", "midX", "midY", "propMiddleX", "propMiddleY", "middleX", "middleYFraction", "middleY", "onMouseOver", "colorInterval", "isNull", "visible", "nullInteraction", "onMouseOut", "setVisible", "vis", "dataLabel", "animOptions", "inset", "insetIndex", "insets", "px1", "projectedUnitsToPixels", "px2", "proj1", "pixelsToProjectedUnits", "proj2", "fitToBounds", "isDirty", "redraw", "MapSeriesDefaults_isNumber", "Maps_MapViewDefaults", "fitToGeometry", "max<PERSON><PERSON>", "parallels", "rotation", "insetOptions", "borderColor", "borderWidth", "relativeTo", "units", "external_highcharts_src_js_default_Templating_namespaceObject", "Templating", "external_highcharts_src_js_default_Templating_default", "win", "format", "error", "GeoJSONComposition_extend", "GeoJSONComposition_merge", "GeoJSONComposition_wrap", "chartFromLatLonToPoint", "lonLatToProjectedUnits", "chartFromPointToLatLon", "projectedUnitsToLonLat", "chartTransformFromLatLon", "latLon", "transform", "proj4", "jsonmarginX", "jsonmarginY", "jsonres", "scale", "xoffset", "xpan", "yoffset", "ypan", "projected", "crs", "lon", "lat", "cosAngle", "cos", "sinAngle", "sin", "rotated", "chartTransformToLatLon", "normalized", "topo2geo", "topology", "objectName", "keys", "objects", "arcsArray", "arcs", "positionArray", "translate", "iEnd", "j", "jEnd", "arcsToCoordinates", "reduce", "coordinates", "arcNo", "arc", "reverse", "concat", "geometries", "features", "g<PERSON><PERSON><PERSON>", "copyright", "copyrightShort", "copyrightUrl", "bbox", "wrapChartAddCredit", "mapCreditsFull", "ChartClass", "chartProto", "transformFromLatLon", "fromLatLonToPoint", "fromPointToLatLon", "transformToLatLon", "json", "hType", "pointOptions", "feature", "NAME", "mapCredits", "Maps_GeoJSONComposition", "getCenterOfPoints", "points", "sum", "getDistanceBetweenPoints", "p1", "p2", "sqrt", "pow", "getAngleBetweenPoints", "atan2", "pointInPolygon", "polygon", "len", "inside", "Geometry_GeometryUtilities", "clipPolygon", "subjectPolygon", "boundsPolygon", "closed", "clipEdge1", "clipEdge2", "prevPoint", "currentPoint", "outputList", "inputList", "isInside", "intersection", "p", "dc", "dp", "n1", "n2", "n3", "isIntersection", "sign", "LambertConformalConic_deg2rad", "PI", "halfPI", "tany", "tan", "Projections_LambertConformalConic", "lat1", "lat2", "cosLat1", "projectedBounds", "xy", "outside", "cy", "rho", "l", "atan", "M", "Projections_EqualEarth", "paramLat", "asin", "paramLatSq", "paramLatPow6", "A1", "A2", "A3", "A4", "fy", "dlat", "NaN", "quarterPI", "Miller_deg2rad", "Projections_Miller", "exp", "Orthographic_deg2rad", "Projections_Orthographic", "antimeridianCutting", "lonDeg", "latDeg", "z", "cSin", "WebMercator_deg2rad", "Projections_WebMercator", "maxLatitude", "sinLat", "clipLineString", "Projection_clipLineString", "Projection_clipPolygon", "line", "splice", "clamp", "erase", "Projection_deg2rad", "wrapLon", "hav", "radians", "havFromCoords", "point1", "point2", "lon1", "lon2", "Projection", "registry", "distance", "angularDistance", "geodesic", "inclusive", "stepDistance", "cosLat1CosLon1", "cosLat2CosLon2", "cosLat1SinLon1", "cosLat2SinLon2", "sinLat1", "sinLat2", "pointDistance", "angDistance", "sinAng", "jumps", "round", "lineString", "step", "fraction", "A", "B", "lat3", "lon3", "insertGeodesics", "poly", "roughDistance", "toString", "join", "hasGeoProjection", "rotator", "getRotator", "ProjectionDefinition", "def", "lineIntersectsBounds", "getIntersect", "dim", "val", "otherDim", "crossingVal", "deltaLambda", "deltaPhi", "deltaGamma", "cosDeltaPhi", "sinDeltaPhi", "cos<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "cosLat", "k", "rLonLat", "cutOnAntimeridian", "isPolygon", "polarIntersection", "intersections", "polygons", "previousLonLat", "antimeridian", "direction", "sort", "index", "lonPlus", "lonMinus", "polarLatitude", "polarSegment", "unshift", "projectingToPlane", "preclip", "postclip", "addToPath", "firstValidLonLat", "lastValidLonLat", "movedTo", "gap", "pushToPath", "someOutside", "someInside", "EqualEarth", "LambertConformalConic", "<PERSON>", "Orthographic", "WebMercator", "MapView_composed", "MapView_boundsFromPath", "MapView_addEvent", "MapView_clamp", "crisp", "MapView_fireEvent", "isArray", "MapView_isNumber", "isObject", "isString", "MapView_merge", "MapView_pick", "MapView_pushUnique", "MapView_relativeLength", "zoomFromBounds", "playingField", "worldSize", "recommendedMapViewAfterDrill", "seriesOptions", "drilldown", "mapZooming", "MapView", "order", "compositeBounds", "arrayOfBounds", "acc", "cur", "mergeInsets", "toObject", "ob", "insetsObj", "allowTransformAnimation", "eventsToUnbind", "recommendedMapView", "MapViewInset", "recInsets", "optInsets", "plotBox", "minZoom", "createInsets", "getField", "hasRendered", "setUpEvents", "animation", "pad", "fullField", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "padded", "getGeoMap", "getMapBBox", "getScale", "allBounds", "affectsMapView", "fitToGeometryCache", "xs", "pos", "ys", "getSVGTransform", "projectedCenter", "flipFactor", "scaleX", "scaleY", "translateX", "lonLatToPixels", "mapTransforms", "hitZone", "coords", "geoBounds", "insetProjectedPoint", "pxPoint", "mapDataArray", "geoMaps", "allGeoBounds", "geoMap", "useMapGeometry", "bottomLeft", "topRight", "boundsCenterProjected", "some", "isDrilling", "field", "getHitZone", "render", "centerPxX", "centerPxY", "mouseDownCenterProjected", "mouseDownKey", "mouseDownRotation", "onPan", "lastTouches", "pinchDown", "touches", "mouseDownX", "mouseDownY", "startDistance", "originalEvent", "worldBounds", "worldZoom", "ratio", "newCenter", "evt", "resetSelection", "showResetZoom", "group", "newProjection", "isDirtyProjection", "Maps_Projection", "isDirtyInsets", "groups", "transformGroups", "clearBounds", "isDirtyData", "chartCoords", "offsetX", "offsetY", "geoBoundsProjectedBox", "geoBoundsProjectedPolygon", "segment", "Boolean", "borderPath", "animate", "border", "strokeWidth", "f", "animObject", "stop", "noop", "ColumnSeries", "ScatterSeries", "MapSeries_extend", "find", "MapSeries_fireEvent", "MapSeries_isArray", "MapSeries_defined", "MapSeries_isNumber", "MapSeries_isObject", "MapSeries_merge", "MapSeries_objectEach", "MapSeries_pick", "splat", "MapSeries", "processedData", "doFullTranslate", "isResizing", "drawMapDataLabels", "drawDataLabels", "dataLabelsGroup", "clip", "clipRect", "drawPoints", "className", "toLowerCase", "selected", "params", "complete", "animateIn", "animateOut", "inheritedStrokeWidth", "getStrokeWidth", "removeAttribute", "transformGroup", "svgTransform", "view", "animatePoints", "globalAnimation", "startTranslateX", "startTranslateY", "startScale", "now", "fx", "scaleStep", "userStep", "animator", "applyDrilldown", "bind", "labelrank", "Maps_MapView", "pointAttrToOptions", "hasData", "dataTable", "rowCount", "pointStrokeWidth", "stateOptions", "states", "stateStrokeWidth", "seriesStrokeWidth", "dashstyle", "nullColor", "linecap", "updateData", "setData", "data", "updatePoints", "processData", "generatePoints", "dataColumnKeys", "props", "chartOptions", "dataUsed", "mapDataObject", "mapTitle", "ix", "Map_MapPoint", "setNestedProperty", "_i", "allAreas", "dataUsedString", "itemOptions", "mainSvgTransform", "plotX", "plotY", "shapeType", "shapeArgs", "hiddenInDataClass", "defaultOptions", "dataLabels", "crop", "formatter", "numberF<PERSON>atter", "overflow", "marker", "stickyTracking", "followPointer", "pointFormat", "turboThreshold", "hover", "halo", "normal", "select", "legendSymbol", "directTouch", "drawGraph", "forceDL", "getExtremesFromAll", "getSymbol", "isCartesian", "preserveAspectRatio", "searchPoint", "registerSeriesType", "Map_MapSeries", "MapLineSeries_extend", "MapLineSeries_merge", "MapLineSeries", "fillColor", "lineWidth", "MapPointPoint_ScatterSeries", "MapPointPoint_isNumber", "MapPointPoint", "MapPointSeries_noop", "MapPointSeries_MapSeries", "MapPointSeries_ScatterSeries", "MapPointSeries_extend", "MapPointSeries_fireEvent", "MapPointSeries_isNumber", "MapPointSeries_merge", "MapPointSeries", "projectPoint", "didBounds", "plotCoords", "isPointInside", "zone", "zones", "getZone", "defer", "mapmarker", "anchorX", "anchorY", "isLegendSymbol", "context", "Bubble_BubbleLegendDefaults", "connectorClassName", "connectorColor", "connectorDistance", "connectorWidth", "labels", "allowOverlap", "maxSize", "minSize", "legendIndex", "ranges", "sizeBy", "sizeByAbsoluteValue", "zThreshold", "BubbleLegendItem_noop", "arrayMax", "arrayMin", "BubbleLegendItem_isNumber", "BubbleLegendItem_merge", "BubbleLegendItem_pick", "stableSort", "Bubble_BubbleLegendItem", "legend", "setState", "addToLegend", "items", "drawLegendSymbol", "connectorSpace", "itemDistance", "legendItem", "bubbleLegend", "autoRanges", "max<PERSON><PERSON><PERSON>", "getMaxLabelSize", "radius", "movementX", "labelWidth", "labelHeight", "seriesIndex", "baseline", "bubbleAttribs", "connectorAttribs", "labelAttribs", "rtl", "fillOpacity", "range", "getRangeRadius", "bubbleSeries", "zMax", "zMin", "getRadius", "connectors", "bubbleItems", "symbol", "label", "itemStyle", "renderRange", "hideOverlappingLabels", "mainRange", "labelsOptions", "elementCenter", "absoluteRadius", "labelsAlign", "posX", "posY", "crispMovement", "connectorLength", "circle", "colorIndex", "crispLine", "formatLabel", "position", "placed", "alignAttr", "labelSize", "newOpacity", "oldOpacity", "show", "hide", "getRanges", "rangesOptions", "zData", "minZ", "maxZ", "isBubble", "ignoreSeries", "getColumn", "filter", "displayNegative", "predictBubbleSizes", "legendOptions", "floating", "horizontal", "layout", "lastLineHeight", "plotSizeX", "plotSizeY", "pxSizes", "getPxExtremes", "ceil", "minPxSize", "maxPxSize", "plotSize", "calculatedSize", "updateRanges", "bubbleLegendOptions", "correctSizes", "BubbleLegendComposition_setOptions", "BubbleLegendComposition_composed", "BubbleLegendComposition_addEvent", "BubbleLegendComposition_objectEach", "BubbleLegendComposition_pushUnique", "BubbleLegendComposition_wrap", "chartDrawChartBox", "bubbleSizes", "getVisibleBubbleSeriesIndex", "allItems", "<PERSON><PERSON><PERSON><PERSON>", "axes", "axis", "setScale", "updateNames", "ticks", "tick", "isNew", "isNewLabel", "retranslateItems", "getLinesHeights", "lines", "lastLine", "legendItem2", "itemHeight", "onLegendAfterGetAllItems", "bubbleSeriesIndex", "destroyItem", "onLegendItemClick", "status", "defaultPrevented", "orgTranslateX", "orgTranslateY", "actualLine", "LegendClass", "external_highcharts_src_js_default_Point_namespaceObject", "Point", "external_highcharts_src_js_default_Point_default", "BubblePoint_ScatterPoint", "BubblePoint_extend", "BubblePoint", "haloPath", "computedSize", "inverted", "xAxis", "yAxis", "ttBelow", "Bubble_BubblePoint", "BubbleSeries_composed", "BubbleSeries_noop", "BubbleSeries_columnProto", "BubbleSeries_ScatterSeries", "BubbleSeries_addEvent", "BubbleSeries_arrayMax", "BubbleSeries_arrayMin", "BubbleSeries_clamp", "BubbleSeries_extend", "BubbleSeries_isNumber", "BubbleSeries_merge", "BubbleSeries_pick", "BubbleSeries_pushUnique", "onAxisFoundExtremes", "axisLength", "coll", "isXAxis", "pxMin", "pxMax", "transA", "hasActiveSeries", "bubblePadding", "reserveSpace", "allowZoomOutside", "onPoint", "getRadii", "radii", "dataMin", "logarithmic", "onAxisAfterRender", "tickPositions", "dataMax", "categories", "tickCount", "BubbleSeries", "AxisClass", "Bubble_BubbleLegendComposition", "animationLimit", "markerAttribs", "yData", "zExtremes", "bubbleZExtremes", "valid", "otherSeries", "getZExtremes", "yValue", "sizeByArea", "zRange", "markerOptions", "translateBubble", "zoneAxis", "negative", "dlBox", "getPxSize", "isPercent", "parseInt", "searchKDTree", "compareX", "suppliedPointEvaluator", "suppliedBSideCheckEvaluator", "comparisonProp", "p1Dist", "p2Dist", "flip", "lineColor", "radiusPlus", "softT<PERSON>eshold", "alignDataLabel", "applyZones", "keysAffectYAxis", "specialGroup", "Bubble_BubbleSeries", "mapPointProto", "MapBubblePoint_extend", "MapBubblePoint", "mapProto", "mappoint", "MapBubbleSeries_mapPointProto", "MapBubbleSeries_extend", "MapBubbleSeries_merge", "MapBubbleSeries", "kdAxisArray", "xyFromShape", "external_highcharts_src_js_default_Color_namespaceObject", "Color", "external_highcharts_src_js_default_Color_default", "HeatmapPoint_ScatterPoint", "HeatmapPoint_clamp", "HeatmapPoint_defined", "HeatmapPoint_extend", "HeatmapPoint_pick", "HeatmapPoint", "formatPrefix", "getCellAttributes", "xPad", "colsize", "yPad", "rowsize", "pointPlacement", "pointPlacementToXValue", "pointPadding", "cellAttr", "dimension", "side", "plotPos", "widthOrHeight", "halfCellSize", "reversed", "HeatmapSeriesDefaults_isNumber", "doc", "InterpolationUtilities_defined", "InterpolationUtilities_pick", "HeatmapSeries_Series", "HeatmapSeries_ColumnSeries", "HeatmapSeries_ScatterSeries", "HeatmapSeries_symbols", "HeatmapSeries_addEvent", "HeatmapSeries_extend", "HeatmapSeries_fireEvent", "HeatmapSeries_isNumber", "HeatmapSeries_merge", "HeatmapSeries_pick", "colorFromPoint", "HeatmapSeries_colorFromPoint", "getContext", "HeatmapSeries_getContext", "colorAxis", "rgba", "toColor", "canvas", "clearRect", "createElement", "willReadFrequently", "HeatmapSeries", "valueMax", "valueMin", "isDirtyCanvas", "interpolation", "seriesMarkerOptions", "image", "xRev", "yRev", "dimensions", "ctx", "xMin", "xMax", "getExtremes", "yMin", "yMax", "xDelta", "y<PERSON><PERSON><PERSON>", "lastX", "imgMultiple", "lastY", "transformX", "transformY", "last", "rev", "rounding", "v", "canvasWidth", "canvasArea", "pixelToPointScale", "pointsLen", "pixelData", "Uint8ClampedArray", "pointInPixels", "buildKDTree", "set", "putImageData", "ImageData", "toDataURL", "_hasPointMarkers", "getValidPoints", "insideOnly", "pointRange", "axisPointRange", "ellipse", "borderRadius", "hasImage", "pointMarkerOptions", "seriesStateOptions", "pointStateOptions", "widthPlus", "heightPlus", "plotOptions", "seriesPlotOptions", "heatmapPlotOptions", "heatmap", "parse", "brighten", "brightness", "shape", "hasRegularShape", "sizeDiff", "clientX", "lineWidthPlus", "G", "Map", "MapBubble_MapBubbleSeries", "Axis", "Legend", "Maps_MapNavigation", "Pointer", "map_src", "default"], "mappings": "IA6dIA,CAldJ,WAAYC,MAA6D,sBAAuB,AAChG,OAAwE,oBAAqB,CAEpF,IAm7BEC,EAoQAC,EA4rDAC,EA5iFPC,EA0eAC,EAiIAJ,EAu8CAK,EAyfAH,EAl3FSI,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqD3B,EAAwD,OAAU,CAC7H,IAAI4B,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAI8C,CAAG,GAwQ9B,IAAMiB,EAJL,CAC1BC,KApPS,CACTC,OAAQ,UACRC,QAAS,UACb,EAkPIC,cA1OkB,CASlBC,cAAe,CAOXC,QAAS,UAMTC,MAAO,OAOPC,cAAe,MAIfC,EAAG,EAIHC,MAAO,GAIPC,OAAQ,GAMRC,QAAS,EAOTC,MAAO,CAEHC,MAAO,UAEPC,SAAU,MAEVC,WAAY,MAChB,EAaAC,MAAO,CAEHC,KAAM,UAENC,OAAQ,UAER,eAAgB,EAEhB,aAAc,QAClB,CACJ,EASAC,QAAS,CAULlB,OAAQ,CAQJmB,QAAS,WACL,IAAI,CAACC,OAAO,CAAC,GACjB,EAKAC,KAAM,IAKNC,EAAG,CACP,EAUArB,QAAS,CAQLkB,QAAS,WACL,IAAI,CAACC,OAAO,CAAC,EACjB,EAKAC,KAAM,IAKNC,EAAG,EACP,CACJ,EA+DAC,sBAAuB,GAO3B,CASA,EAeM,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAI9B,KAOzC,AAAC,SAAUxB,CAAU,EAMjB,IACIuD,EADAC,EAAkB,EA2BtB,SAASC,EAAoBC,CAAC,EAC1B,IAAMC,EAAQ,IAAI,CAACA,KAAK,CACxBD,EAAI,IAAI,CAACE,SAAS,CAACF,GACfC,EAAME,OAAO,CAAChC,aAAa,CAACiC,uBAAuB,CAC/CH,EAAMI,OAAO,CAACC,OAAO,CAACN,EAAEO,MAAM,CAAE,uBAChCN,EAAMO,UAAU,EAChBP,EAAMO,UAAU,CAACC,MAAM,GAGtBR,EAAMS,YAAY,CAACV,EAAEW,MAAM,CAAGV,EAAMW,QAAQ,CAAEZ,EAAEa,MAAM,CAAGZ,EAAMa,OAAO,GAC3Eb,EAAMZ,OAAO,CAAC,GAAK,KAAK,EAAG,KAAK,EAAGW,EAAEW,MAAM,CAAEX,EAAEa,MAAM,CAE7D,CAKA,SAASE,EAAsBf,CAAC,EAC5B,IAAMC,EAAQ,IAAI,CAACA,KAAK,CAIlBe,EAAQ,AAACvB,EAAQO,AAHvBA,CAAAA,EAAI,IAAI,CAACE,SAAS,CAACF,EAAC,EAGKiB,UAAU,GAAK,CAACjB,EAAEiB,UAAU,CAAG,KACpDjB,EAAEkB,MAAM,EAAIlB,EAAEmB,MAAM,CAOpBC,KAAKC,GAAG,CAACL,IAAU,IACnBlB,GAAmBsB,KAAKC,GAAG,CAACL,GACxBnB,GACAyB,aAAazB,GAEjBA,EAAuB0B,WAAW,KAC9BzB,EAAkB,CACtB,EAAG,KAEHA,EAAkB,IAAMG,EAAMS,YAAY,CAACV,EAAEW,MAAM,CAAGV,EAAMW,QAAQ,CAAEZ,EAAEa,MAAM,CAAGZ,EAAMa,OAAO,GAAKb,EAAMuB,OAAO,EAChHvB,EAAMuB,OAAO,CAACC,MAAM,CAAC,CAAA,CAAA,AAACxB,CAAAA,EAAME,OAAO,CAAChC,aAAa,CAACqB,qBAAqB,CACnE,CAAA,EAAMwB,CAAI,EAAG,KAAK,EAAG,CAAChB,EAAEW,MAAM,CAAEX,EAAEa,MAAM,CAAC,CAG7CO,CAAAA,CAAAA,AAAkB,EAAlBA,KAAKC,GAAG,CAACL,EAAS,GAAY,KAAK,EAE3C,CAKA,SAASU,EAAcC,CAAO,CAAE3B,CAAC,CAAE4B,CAAa,EAC5C,IAAM3B,EAAQ,IAAI,CAACA,KAAK,CAExB,GADAD,EAAI2B,EAAQ/D,IAAI,CAAC,IAAI,CAAEoC,EAAG4B,GACtB3B,GAASA,EAAMuB,OAAO,CAAE,CACxB,IAAMK,EAAS5B,EAAMuB,OAAO,CAACM,cAAc,CAAC,CACxCtD,EAAGwB,EAAEW,MAAM,CAAGV,EAAMW,QAAQ,CAC5BrB,EAAGS,EAAEa,MAAM,CAAGZ,EAAMa,OAAO,AAC/B,GACIe,GACAnC,EAAOM,EAAG6B,EAElB,CACA,OAAO7B,CACX,CAKA,SAAS+B,EAAeJ,CAAO,EAC3B,IAAMxD,EAAgB,IAAI,CAAC8B,KAAK,CAACE,OAAO,CAAChC,aAAa,CAElDA,GACAwB,EAAKxB,EAAc6D,eAAe,CAAE7D,EAAc8D,OAAO,GACzD,CAAA,IAAI,CAAChC,KAAK,CAACiC,OAAO,CAACC,SAAS,CAAG,IAAG,EAEtCR,EAAQS,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAACzE,IAAI,CAAC0E,UAAW,GACjD,CAlFAhG,EAAWiG,OAAO,CAXlB,SAAiBC,CAAY,EACzB,IAAMC,EAAeD,EAAa9E,SAAS,AACtC+E,CAAAA,EAAa1C,mBAAmB,GACjCL,EAAO+C,EAAc,CACjB1C,oBAAAA,EACAgB,sBAAAA,CACJ,GACAnB,EAAK6C,EAAc,YAAaf,GAChC9B,EAAK6C,EAAc,aAAcV,GAEzC,CAoFJ,EAAGzF,GAAeA,CAAAA,EAAa,CAAC,CAAA,GAMH,IAAMoG,EAAmBpG,EA2BtD,SAASqG,EAAanE,CAAC,CAAEe,CAAC,CAAEqD,CAAC,CAAEC,CAAC,CAAE1C,CAAO,EACrC,GAAIA,EAAS,CACT,IAAM2C,EAAI3C,GAAS2C,GAAK,CACxB3C,CAAAA,EAAQ4C,MAAM,CAAGxD,EAAIuD,EACrB3C,EAAQ6C,WAAW,CAAGH,EAAIC,CAC9B,CACA,OAAO7G,EAAQgH,WAAW,CAACzE,EAAGe,EAAGqD,EAAGC,EAAG1C,EAC3C,CAYA,SAAS+C,EAAU1E,CAAC,CAAEe,CAAC,CAAEqD,CAAC,CAAEC,CAAC,CAAE1C,CAAO,EAClC,GAAIA,EAAS,CACT,IAAM2C,EAAI3C,GAAS2C,GAAK,CACxB3C,CAAAA,EAAQ6C,WAAW,CAAGH,EAAIC,CAC9B,CACA,OAAO7G,EAAQgH,WAAW,CAACzE,EAAGe,EAAGqD,EAAGC,EAAG1C,EAC3C,CAS6B,MAvB7B,SAAiBgD,CAAgB,EAE7BlH,AADAA,CAAAA,EAAUkH,EAAiBzF,SAAS,CAACzB,OAAO,AAAD,EACnCmH,YAAY,CAAGT,EACvB1G,EAAQoH,SAAS,CAAGH,CACxB,EAiCM,CAAEI,WAAAA,CAAU,CAAE,CAAIxF,IAElB,CAAEyF,SAAAA,CAAQ,CAAE,CAAIzF,IAKhB,CAAE0F,SAAAA,CAAQ,CAAE9D,OAAQ+D,CAAoB,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEhE,KAAMiE,CAAkB,CAAEC,WAAAA,CAAU,CAAE,CAAI/F,IAS7G,SAASgG,EAAU9D,CAAC,EACZA,IACAA,EAAE+D,cAAc,KAChB/D,EAAEgE,eAAe,KACjBhE,EAAEiE,YAAY,CAAG,CAAA,EAEzB,CAiBA,MAAMC,EAMF,OAAO3B,QAAQ4B,CAAa,CAAE3B,CAAY,CAAEW,CAAgB,CAAE,CAC1DT,EAAgBH,OAAO,CAACC,GACxB4B,EAAwBjB,GACpBU,EAAWN,EAAU,oBAErBC,EAASW,EAAe,eAAgB,WAGpC,IAAI,CAAChG,aAAa,CAAG,IAAI+F,EAAc,IAAI,EAC3C,IAAI,CAAC/F,aAAa,CAACkG,MAAM,EAC7B,GACAf,EAAWvF,GAEnB,CAMAuG,YAAYrE,CAAK,CAAE,CACf,IAAI,CAACA,KAAK,CAAGA,EACb,IAAI,CAACsE,UAAU,CAAG,EAAE,AACxB,CAeAF,OAAOlE,CAAO,CAAE,CACZ,IAAMqE,EAAS,IAAI,CAAEvE,EAAQuE,EAAOvE,KAAK,CAAEsE,EAAaC,EAAOD,UAAU,CAAEE,EAAe,SAAUzE,CAAC,EACjG,IAAI,CAAC0E,OAAO,CAAC9G,IAAI,CAACqC,EAAOD,GACzB8D,EAAU9D,EACd,EACI2E,EAAa1E,EAAME,OAAO,CAAChC,aAAa,CAQ5C,IALIgC,GACAwE,CAAAA,EAAa1E,EAAME,OAAO,CAAChC,aAAa,CACpCuF,EAAMzD,EAAME,OAAO,CAAChC,aAAa,CAAEgC,EAAO,EAG3CoE,EAAWK,MAAM,EACpBL,EAAWM,GAAG,IAAIC,UAEtB,GAAI,CAAC7E,EAAM8E,QAAQ,CAACC,SAAS,EACzBpB,EAAmBe,EAAWM,aAAa,CAAEN,EAAW1C,OAAO,EAAG,CAC7DuC,EAAOU,eAAe,EACvBV,CAAAA,EAAOU,eAAe,CAAGjF,EAAM8E,QAAQ,CAACI,CAAC,GACpCC,IAAI,CAAC,CACNC,OAAQ,CACZ,GACKC,GAAG,EAAC,EAEb3B,EAAWgB,EAAWxF,OAAO,CAAE,CAACf,EAAe1B,KAE3C,IAAM0I,EAAO,CACTzG,QAASP,AAFbA,CAAAA,EAAgBsF,EAAMiB,EAAWvG,aAAa,CAAEA,EAAa,EAElCO,OAAO,AAClC,CAEI,EAACsB,EAAMsF,UAAU,EAAInH,EAAcY,KAAK,GACxCyE,EAAqB2B,EAAMhH,EAAcY,KAAK,EAC9CoG,EAAKxG,KAAK,CAAG8E,EAAMtF,EAAcY,KAAK,CAACJ,KAAK,CAAER,EAAcQ,KAAK,GAGrE,GAAM,CAAEU,KAAAA,CAAI,CAAEb,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAE,CAAGP,EAC/CoH,EAASvF,EAAM8E,QAAQ,CACxBS,MAAM,CAGX,AAAU,MAATlG,GAAgBA,AAAS,MAATA,GAAgBA,GAAS,GAAI,EAAG,EAAGmF,EAAcW,EAAM,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG1I,AAAM,WAANA,EAAiB,YAAc,gBAC1H+I,QAAQ,CAAC,wCAA0C,CAAA,CACpDxH,OAAQ,UACRC,QAAS,UACb,CAAA,CAAC,CAACxB,EAAE,EACC0I,IAAI,CAAC,CACN3G,MAAAA,EACAC,OAAAA,EACAgH,MAAOzF,EAAME,OAAO,CAACnC,IAAI,CAACtB,EAAE,CAC5B2I,OAAQ,CACZ,GACKC,GAAG,CAACd,EAAOU,eAAe,EAI/B,GAAI5F,AAAS,MAATA,GAAgBA,AAAS,MAATA,EAAc,CAE9B,IAAMsD,EAAInE,EAAQ,EAAG3B,EAAI,CACrB,CAAC,IAAK6B,EAAU,EAAGA,EAAUD,EAAS,EAAE,CACxC,CAAC,IAAKC,EAAUiE,EAAI,EAAGjE,EAAUD,EAAS,EAAE,CAC/C,AACY,CAAA,MAATY,GACAxC,EAAE6I,IAAI,CAAC,CAAC,IAAKhH,EAAUiE,EAAI,EAAGjE,EAAU,EAAE,CAAE,CAAC,IAAKA,EAAUiE,EAAI,EAAGjE,EAAUD,EAAS,EAAE,EAE5FuB,EAAM8E,QAAQ,CACTa,IAAI,CAAC9I,GACL2I,QAAQ,CAAC,4BACTL,IAAI,CAACnF,EAAMsF,UAAU,CAAG,CAAC,EAAI,CAC9BrG,OAAQd,EAAcQ,KAAK,EAAEC,MAC7B,eAAgB,EAChB,iBAAkB,OACtB,GACKyG,GAAG,CAACE,EACb,CASA,GARAA,EAAOd,OAAO,CAAGtG,EAAcgB,OAAO,CAEtCoE,EAASgC,EAAOK,OAAO,CAAE,WAAY/B,GACrCS,EAAWoB,IAAI,CAACH,GAChB/B,EAAqBrF,EAAe,CAChCK,MAAO+G,EAAO/G,KAAK,CACnBC,OAAQ,EAAK8G,CAAAA,EAAO9G,MAAM,EAAI,CAAA,CAClC,GACKuB,EAAM6F,SAAS,CAWhBN,EAAOlH,KAAK,CAACF,EAAe,CAAA,EAAOA,EAAcC,OAAO,MAXtC,CAElB,IAAM0H,EAASvC,EAASvD,EAAO,OAAQ,KAE/BuF,EAAOK,OAAO,EACdL,EAAOlH,KAAK,CAACF,EAAe,CAAA,EAAOA,EAAcC,OAAO,EAE5D0H,GACJ,EACJ,CAIJ,GAEA,IAAMC,EAAkB,CAACC,EAAMC,IAAS,CAAEA,CAAAA,EAAK1H,CAAC,EAAIyH,EAAKzH,CAAC,CAAGyH,EAAKxH,KAAK,EACnEyH,EAAK1H,CAAC,CAAG0H,EAAKzH,KAAK,EAAIwH,EAAKzH,CAAC,EAC7B0H,EAAK3G,CAAC,EAAI0G,EAAK1G,CAAC,CAAG0G,EAAKvH,MAAM,EAC9BwH,EAAK3G,CAAC,CAAG2G,EAAKxH,MAAM,EAAIuH,EAAK1G,CAAC,AAADA,CA0B5BU,CAAAA,EAAM6F,SAAS,EAIhBtC,EAASvD,EAAO,SA3BI,WACpB,IAAMkG,EAAalG,EAAMmG,cAAc,EAAEC,UACzC,GAAIF,EAAY,CACZ,IAAMG,EAAc9B,EAAOU,eAAe,CAACmB,OAAO,GAElD,GAAIL,EAAgBG,EAAYG,GAAc,CAG1C,IAAMC,EAAc,CAACD,EAAY/G,CAAC,CAC9B+G,EAAY5H,MAAM,CAAGyH,EAAW5G,CAAC,CAAG,EAAGiH,EAAcL,EAAW5G,CAAC,CAAG4G,EAAWzH,MAAM,CACrF4H,EAAY/G,CAAC,CAAG,EAAGkH,EAAuB9B,EAAWvG,aAAa,EAClEuG,EAAWvG,aAAa,CAACG,aAAa,CAI1CiG,EAAOU,eAAe,CAACE,IAAI,CAAC,CACxBsB,WAAYD,AAAwB,WAAxBA,EACRF,EACAC,CACR,EACJ,CACJ,CACJ,EAOJ,CACA,IAAI,CAACG,YAAY,CAAChC,EACtB,CAUAgC,aAAaxG,CAAO,CAAE,CAClB,IAAMF,EAAQ,IAAI,CAACA,KAAK,AAEpB2D,CAAAA,EAAmBzD,EAAQyG,qBAAqB,CAAEzG,EAAQ8B,OAAO,GACjE9B,EAAQC,uBAAuB,CAC/B,IAAI,CAACyG,cAAc,CAAG,IAAI,CAACA,cAAc,EAAIrD,EAASvD,EAAM6G,SAAS,CAAE,WAAY,SAAU9G,CAAC,EAC1FC,EAAMI,OAAO,CAACN,mBAAmB,CAACC,EACtC,GAEK,IAAI,CAAC6G,cAAc,EAExB,CAAA,IAAI,CAACA,cAAc,CAAG,IAAI,CAACA,cAAc,EAAC,EAG1CjD,EAAmBzD,EAAQ4G,oBAAoB,CAAE5G,EAAQ8B,OAAO,EAChE,IAAI,CAAC+E,gBAAgB,CAAG,IAAI,CAACA,gBAAgB,EAAIxD,EAASvD,EAAM6G,SAAS,CAAE,QAAS,SAAU9G,CAAC,EAG3F,GAAI,CAACC,EAAMI,OAAO,CAACC,OAAO,CAACN,EAAEO,MAAM,CAAE,4BAA6B,CAC9D,IAAM0G,EAAchH,EAAMuB,OAAO,EAAE0F,KACnCjH,EAAMI,OAAO,CAACU,qBAAqB,CAACf,GAGhCiH,IAAgBhH,EAAMuB,OAAO,EAAE0F,MAC/BpD,EAAU9D,EAElB,CACA,MAAO,CAAA,CACX,GAEK,IAAI,CAACgH,gBAAgB,EAE1B,CAAA,IAAI,CAACA,gBAAgB,CAAG,IAAI,CAACA,gBAAgB,EAAC,CAEtD,CACJ,CASA,IAAMG,EAAoEjL,EAAwD,OAAU,CAACkL,cAAc,CAC3J,IAAIC,EAAyE5K,EAAoBC,CAAC,CAACyK,GAEnG,IAAMG,EAAgEpL,EAAwD,OAAU,CAACqL,UAAU,CACnJ,IAAIC,EAAqE/K,EAAoBC,CAAC,CAAC4K,GAa/F,GAAM,CAAEG,OAAQ,CAAE/J,UAAWgK,CAAW,CAAE,CAAE,CAAG,AAACL,IAA6DM,WAAW,CAGlH,CAAEnE,SAAUoE,CAA4B,CAAEnI,QAASoI,CAA2B,CAAE,CAAI/J,KAO1F,AAAC,SAAUvB,CAAmB,EAsC1B,SAASuL,EAAqB9H,CAAC,EAC3B,IAAoB+H,EAASC,AAAf,IAAI,CAAiBD,MAAM,CAAEhD,EAAWgD,EAAO9H,KAAK,CAAC8E,QAAQ,AACvEiD,CADU,IAAI,CACRC,gBAAgB,EAAID,AADhB,IAAI,CACkBE,OAAO,GAClCH,EAAOI,kBAAkB,EAI1BJ,CAAAA,EAAOI,kBAAkB,CAAG,GAAKX,CAAAA,GAAsD,EAAGzC,EAAU,OAC/FqD,GAAG,CAAC,CACLC,cAAe,MACnB,GACK/C,GAAG,CAAC0C,AAVH,IAAI,CAUKE,OAAO,CAACI,WAAW,CAAA,EAElCtI,GAAGuI,QAAU,SAGbP,AAfM,IAAI,CAeJE,OAAO,CAAC9C,IAAI,CAAC,CACfoD,GAAI,IAAI,CAACA,EAAE,AACf,GACAT,EAAOI,kBAAkB,CAAC/C,IAAI,CAAC,CAC3BqD,KAAM,CAAC,EAAE1D,EAAS2D,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,EAAE,CAAC,CAAC,CAClCG,WAAY,SAChB,IAGAZ,EAAOI,kBAAkB,CAAC/C,IAAI,CAAC,CAC3BqD,KAAM,EACV,GAGZ,CA9DAlM,EAAoBqM,YAAY,CAAG,CAC/BC,gBAAiB,CAAA,EACjBZ,iBAAkB,CAAA,EAClBa,QAiEJ,WACI,OAAQ,AAAe,OAAf,IAAI,CAACC,KAAK,EACd,IAAI,CAACA,KAAK,GAAKC,KACf,IAAI,CAACD,KAAK,GAAK,CAACC,KAEf,CAAA,AAAe,KAAK,IAApB,IAAI,CAACD,KAAK,EAAe,CAACE,MAAM,IAAI,CAACF,KAAK,CAAA,CACnD,CAtEA,EACAxM,EAAoB2M,aAAa,CAAG,CAChCC,SAAU,QACVC,UAAW,CAAC,QAAS,QAAS,YAAY,CAC1CC,eAAgB,CAAC,IAAK,IAAK,QAAQ,CACnCC,cAAe,CAAC,QAAQ,CACxBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DC,aAwEJ,SAA4BxB,CAAK,EAC7B,IAAMyB,EAAM,CAAC,EAMb,OALI5B,EAA4BG,EAAMnJ,KAAK,GACtC,CAAA,CAACmJ,EAAMO,KAAK,EAAIP,AAAgB,WAAhBA,EAAMO,KAAK,AAAY,GAExCkB,CAAAA,CAAG,CAAC,IAAI,CAACC,SAAS,EAAI,OAAO,CAAG1B,EAAMnJ,KAAK,AAAD,EAEvC4K,CACX,EA/EIE,aAAcjC,EAAYiC,YAAY,AAC1C,EAcApN,EAAoBgG,OAAO,CAL3B,SAAiBqH,CAAW,EAGxB,OADAhC,EADmBgC,EAAYlM,SAAS,CAACmM,UAAU,CACV,gBAAiB/B,GACnD8B,CACX,CAkEJ,EAAGrN,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAMuN,EAA8BvN,EAG3DwN,EAA4D7N,EAAwD,OAAU,CAAC8N,MAAM,CAC3I,IAAIC,EAAiExN,EAAoBC,CAAC,CAACqN,GAa3F,GAAM,CAAEG,QAAAA,CAAO,CAAE,CAAIpM,IAGf,CAAEqM,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEzK,KAAM0K,CAAsB,CAAEC,eAAAA,CAAc,CAAE,CAAIxM,GAkE3E3B,EA7DOA,EA4FRA,GAAsBA,CAAAA,EAAoB,CAAC,CAAA,GA/BxBoO,SAAS,CA1C3B,WACI,IAAMpK,EAAU,IAAI,CAACA,OAAO,CAAEF,EAAQ,IAAI,CAACA,KAAK,CAAEuK,EAAc,EAAKrK,CAAAA,EAAQsK,YAAY,EAAI,CAAA,EAAIC,EAAYzK,EAAMyK,SAAS,CAAG,EAAIF,EAAaG,EAAa1K,EAAM0K,UAAU,CAAG,EAAIH,EAAaI,EAAezK,EAAQ0K,MAAM,CAAEC,EAAe1J,KAAK2J,GAAG,CAACL,EAAWC,GAAaK,EAAY7K,EAAQ6K,SAAS,CACzSC,EAAmBC,EAAO/K,EAAQ+K,IAAI,CAAEC,EAAYhL,EAAQgL,SAAS,EAAI,EAAGC,EAAGrC,CAC/D,CAAA,UAAhB,OAAOmC,GACPA,CAAAA,EAAOG,WAAWH,EAAI,EAED,UAArB,OAAOC,GACPA,CAAAA,EAAYE,WAAWF,EAAS,EAEpC,IAAMG,EAAY,CACdjB,EAAuBO,GAAc,CAAC,EAAE,CAAE,OAC1CP,EAAuBO,GAAc,CAAC,EAAE,CAAE,OAE1CP,EAAuBa,GAAQA,EAAO,EAAI,KAAK,EAAI/K,EAAQ+K,IAAI,CAAE,QACjEb,EAAuBc,GAAaA,EAAY,EAAI,KAAK,EAAIhL,EAAQgL,SAAS,EAAI,EAAG,MACxF,CAMD,KAHIlL,EAAMsL,OAAO,EAAM,IAAI,YAAatB,KACpCqB,CAAAA,CAAS,CAAC,EAAE,CAAG,CAAA,EAEdF,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACjBrC,EAAQuC,CAAS,CAACF,EAAE,CACpBH,EAAoBG,EAAI,GAAMA,AAAM,IAANA,GAAW,KAAKI,IAAI,CAACzC,GAKnDuC,CAAS,CAACF,EAAE,CAAGd,EAAevB,EAAO,CAAC2B,EAAWC,EAAYG,EAAcQ,CAAS,CAAC,EAAE,CAAC,CAACF,EAAE,EAAKH,CAAAA,EAAoBT,EAAc,CAAA,EAYtI,OATIc,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,EAC3BA,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,AAAD,EAG1BlB,EAASY,IACTA,AAAY,EAAZA,EAAgBM,CAAS,CAAC,EAAE,EAAIN,EAAY,GAC5CM,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,CAAGN,AAAY,EAAZA,CAAY,EAE9Cb,EAAU,IAAI,CAAE,iBAAkB,CAAEmB,UAAAA,CAAU,GACvCA,CACX,EA+BAnP,EAAkBsP,qBAAqB,CAbvC,SAA+BC,CAAK,CAAEC,CAAG,EACrC,IAAMC,EAAaxB,EAASsB,GAASA,EAAQ,EAC7CG,EAAY,AAACzB,EAASuB,IAClBA,EAAMC,GAEN,AAACD,EAAMC,EAAc,IACrBD,EACAC,EAAa,IACjB,MAAO,CACHF,MAAOxB,EAAW0B,CAAAA,EAFc,GAEQ,EACxCD,IAAKzB,EAAW2B,CAAAA,EAHgB,GAGI,CACxC,CACJ,EAQyB,IAAMC,EAA4B3P,EAmBzD4P,EAA2D7P,EAAwD,OAAU,CAAC8P,KAAK,CACzI,IAAIC,EAAgExP,EAAoBC,CAAC,CAACqP,GAE1F,IAAMG,EAAiEhQ,EAAwD,OAAU,CAACiQ,WAAW,CACrJ,IAAIC,EAAsE3P,EAAoBC,CAAC,CAACwP,GAchG,GAAM,CAAEG,WAAAA,CAAU,CAAE,CAAIvO,IAGlB,CAAEsM,SAAUkC,CAAiB,CAAE5I,MAAO6I,EAAc,CAAE5M,KAAM6M,EAAa,CAAE,CAAI1O,GAgBrF,OAAM1B,WAAkB6P,IAuBpBQ,KAAKC,CAAW,CAAEC,CAAQ,CAAE,CACxB,IAAMC,EAAwBP,IAAaQ,OAAO,CAC5C1M,EAAUoM,GAAe,CAC3BtM,MAAO,CACH6M,QAAS,CACL7K,QAAS,CAAA,EACT8K,KAAM,IACV,EACAA,KAAM,KACV,EACAF,QAAS,CACLG,QAASR,GAAcI,EAAsBI,OAAO,CAAE,uEAEtDC,YAAaT,GAAcI,EAAsBK,WAAW,CAAE,sBAClE,EACAzL,QAAS,CAAC,EACV0L,QAAS,CACLC,gBAAiB,CAAA,CACrB,CACJ,EAAGT,GAEH,KAAK,CAACD,KAAKtM,EAASwM,EACxB,CAgCAtN,QAAQ+N,CAAO,CAAEC,CAAU,CAAEC,CAAU,CAAE3M,CAAM,CAAEE,CAAM,CAAE,CACjD,IAAI,CAACW,OAAO,GACR8K,EAAkBc,IAElBA,CAAAA,EAAUhM,KAAKmM,GAAG,CAACH,GAAWhM,KAAKmM,GAAG,CAAC,GAAG,EAE9C,IAAI,CAAC/L,OAAO,CAACC,MAAM,CAAC2L,EAASd,EAAkBe,IAAef,EAAkBgB,GAC5E,IAAI,CAAC9L,OAAO,CAACgM,UAAU,CAACC,OAAO,CAAC,CAACJ,EAAYC,EAAW,EACxD,KAAK,EAAGhB,EAAkB3L,IAAW2L,EAAkBzL,GACvD,CAACF,EAAQE,EAAO,CAChB,KAAK,GAEjB,CACAwD,OAAOlE,CAAO,CAAE,CAERA,EAAQF,KAAK,EAAI,QAASE,EAAQF,KAAK,EACvC,IAAI,CAACuB,OAAO,EAAEkM,iBAAiB,IAAI,CAAE,CACjCvN,EAAQF,KAAK,CAAC0N,GAAG,IACd,AAAC,CAAA,IAAI,CAACxN,OAAO,CAAC4H,MAAM,EAAI,EAAE,AAAD,EAAG4F,GAAG,CAAC,AAACC,GAAMA,EAAEC,OAAO,EACtD,CAAE,CAAA,GAEP,KAAK,CAACxJ,OAAOjC,KAAK,CAAC,IAAI,CAAEE,UAC7B,CACJ,CAoBIlG,CAdOA,EAwFRA,IAAaA,CAAAA,GAAW,CAAC,CAAA,GA1Ef0R,IAAI,CAAG,CAAC,EAmCjB1R,EAAS2R,QAAQ,CAHjB,SAAkBhR,CAAC,CAAEiR,CAAC,CAAEC,CAAC,EACrB,OAAO,IAAI7R,EAASW,EAAGiR,EAAGC,EAC9B,EAuCA7R,EAAS8R,SAAS,CAxBlB,SAAmBtI,CAAI,EACnB,IAAIuI,EAqBJ,OAVIA,EAVA,AAAgB,UAAhB,OAAOvI,EAUDwI,AADQxI,AARdA,CAAAA,EAAOA,EAEFyI,OAAO,CAAC,YAAa,QAErBA,OAAO,CAAC,OAAQ,IAAIA,OAAO,CAAC,OAAQ,GAAE,EAIxBD,KAAK,CAAC,UACbT,GAAG,CAAC,AAACW,GACb,AAAK,SAAS9C,IAAI,CAAC8C,GAGZA,EAFIjD,WAAWiD,IAMpB1I,EAEHwG,IAAyD1O,SAAS,CAAC6Q,cAAc,CAACJ,EAC7F,EAQyB,IAAMK,GAAkBpS,GAuClBqS,GAHd,CACjBC,eAvBmB,SAAU9I,CAAI,EACjC,IAAI+I,EAAK,CAACC,OAAOC,SAAS,CAAEC,EAAKF,OAAOC,SAAS,CAAEE,EAAK,CAACH,OAAOC,SAAS,CAAEG,EAAKJ,OAAOC,SAAS,CAAEI,EAYlG,GAXArJ,EAAKsJ,OAAO,CAAC,AAACC,IACV,IAAM3Q,EAAI2Q,CAAG,CAACA,EAAIvK,MAAM,CAAG,EAAE,CAAErF,EAAI4P,CAAG,CAACA,EAAIvK,MAAM,CAAG,EAAE,AACrC,CAAA,UAAb,OAAOpG,GACP,AAAa,UAAb,OAAOe,IACPuP,EAAK1N,KAAK2J,GAAG,CAAC+D,EAAItQ,GAClBmQ,EAAKvN,KAAKgO,GAAG,CAACT,EAAInQ,GAClBwQ,EAAK5N,KAAK2J,GAAG,CAACiE,EAAIzP,GAClBwP,EAAK3N,KAAKgO,GAAG,CAACL,EAAIxP,GAClB0P,EAAc,CAAA,EAEtB,GACIA,EACA,MAAO,CAAEH,GAAAA,EAAIE,GAAAA,EAAIL,GAAAA,EAAII,GAAAA,CAAG,CAEhC,CAQA,EAgBM,CAAEL,eAAgBW,EAAuB,CAAE,CAAGZ,GAE9Ca,GAAe,AAACjI,IAA6DM,WAAW,CAAC4H,OAAO,CAAC7R,SAAS,CAACmM,UAAU,CAErH,CAAEnK,OAAQ8P,EAAe,CAAEpF,SAAUqF,EAAiB,CAAE9P,KAAM+P,EAAa,CAAE,CAAI5R,GAMvF,OAAM6R,WAAiBL,GAWnB,OAAOM,iBAAiB5H,CAAK,CAAEwF,CAAU,CAAE,CAYvC,OAXKxF,EAAM6H,aAAa,GAChBrC,GAAcxF,EAAM8H,QAAQ,EAE5BtC,EAAWuC,cAAc,CAAG,CAAA,EAC5B/H,EAAM6H,aAAa,CAAGrC,EAAW5H,IAAI,CAACoC,EAAM8H,QAAQ,GAIpD9H,EAAM6H,aAAa,CAAG7H,EAAMpC,IAAI,EAGjCoC,EAAM6H,aAAa,EAAI,EAAE,AACpC,CAUAG,aAAa7P,CAAO,CAAE3B,CAAC,CAAE,CACrB,IAAMuJ,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQ,KAAK,CAACgI,aAAa7P,EAAS3B,GAAIyR,EAASlI,EAAOkI,MAAM,CAC1F,GAAIlI,EAAO8F,OAAO,EAAI9F,EAAOmI,MAAM,CAAE,CACjC,IAAMC,EAAUF,CAAM,CAAC,EAAE,CAAEG,EAAS,KAAK,CAACC,kBAAkBF,GAAUG,EAAW,AAAkB,KAAA,IAAXF,GACpFrI,EAAOmI,MAAM,CAACE,EAAO,CACrBE,EAEAd,GAAgBxH,EAAO,CACnB,GAAGsI,CAAQ,CACXC,KAAMvI,EAAMuI,IAAI,EAAID,EAASC,IAAI,AACrC,GAE+C,KAA1CxI,EAAOuB,aAAa,CAACkH,OAAO,CAAC,UAClCxI,CAAAA,EAAMe,KAAK,CAAGf,EAAMe,KAAK,EAAI,IAAG,CAExC,CACA,OAAOf,CACX,CAKAyI,mBAAmBjD,CAAU,CAAE,CAC3B,IAA0DkD,EAASrB,GAAtDM,GAASC,gBAAgB,CAAC,IAAI,CAAEpC,IAAqDmD,EAAa,IAAI,CAACA,UAAU,CAAEnP,EAAU,IAAI,CAACuG,MAAM,CAAC9H,KAAK,CAACuB,OAAO,CACnK,GAAIkP,EAAQ,CAGR,IAAME,EAAgBD,GAAY,CAAC,gBAAgB,CAAEE,EAAgBF,GAAY,CAAC,gBAAgB,CAClG,GAAInP,GAAWiO,GAAkBmB,IAAkBnB,GAAkBoB,GAAgB,CACjF,IAAMC,EAAiBtD,EAAWuD,OAAO,CAAC,CAACH,EAAeC,EAAc,CACxEH,CAAAA,EAAOM,IAAI,CAAGF,CAAc,CAAC,EAAE,CAC/BJ,EAAOO,IAAI,CAAGH,CAAc,CAAC,EAAE,AACnC,KACK,CACD,IAAMI,EAAcP,GAAY,CAAC,cAAc,CAAEQ,EAAcR,GAAY,CAAC,cAAc,AAC1FD,CAAAA,EAAOM,IAAI,CAAIN,EAAO5B,EAAE,CAAG,AAAC4B,CAAAA,EAAO/B,EAAE,CAAG+B,EAAO5B,EAAE,AAAD,EAAKY,GAAc,IAAI,CAAC0B,OAAO,CAAE3B,GAAkByB,GAAeA,EAAc,IAChI,IAAIG,EAAkB3B,GAAc,IAAI,CAAC4B,OAAO,CAAE7B,GAAkB0B,GAAeA,EAAc,GAE5F,CAAA,IAAI,CAACrB,QAAQ,EACduB,CAAAA,EAAkB,EAAIA,CAAc,EAExCX,EAAOO,IAAI,CACPP,EAAO3B,EAAE,CAAG,AAAC2B,CAAAA,EAAO3B,EAAE,CAAG2B,EAAO1B,EAAE,AAAD,EAAKqC,CAC9C,CACA,OAAOX,CACX,CACJ,CAKAa,YAAYvR,CAAC,CAAE,CACXlC,IAA6CwD,YAAY,CAAC,IAAI,CAACkQ,aAAa,EAG5E,AAAC,CAAC,IAAI,CAACC,MAAM,EAAI,IAAI,CAACC,OAAO,EAEzB,IAAI,CAAC3J,MAAM,CAAC5H,OAAO,CAACwR,eAAe,CACnC,KAAK,CAACJ,YAAY3T,IAAI,CAAC,IAAI,CAAEoC,GAI7B,IAAI,CAAC+H,MAAM,CAAC6J,UAAU,EAE9B,CACAC,WAAWC,CAAG,CAAE,CAEZ,IAAI,CAACJ,OAAO,CAAG,IAAI,CAACvR,OAAO,CAACuR,OAAO,CAAG,CAAC,CAACI,EAEpC,IAAI,CAACC,SAAS,EACd,IAAI,CAACA,SAAS,CAJHD,EAAM,OAAS,OAIJ,GAKtB,IAAI,CAAC5J,OAAO,EACZ,IAAI,CAACA,OAAO,CAAC9C,IAAI,CAAC,IAAI,CAAC2C,MAAM,CAAC4B,YAAY,CAAC,IAAI,EAEvD,CAWAlJ,OAAOuR,CAAW,CAAE,CAChB,IAAoB/R,EAAQ+H,AAAd,IAAI,CAAgBD,MAAM,CAAC9H,KAAK,CAAEuB,EAAUvB,EAAMuB,OAAO,CACnEkP,EAAS1I,AADC,IAAI,CACC0I,MAAM,CACzB,GAAIlP,GAAWkP,EAAQ,CACnB,IAAMuB,EAAQxC,GAAkBzH,AAHtB,IAAI,CAGwBkK,UAAU,GAC5C1Q,EAAQ2Q,MAAM,CAACnK,AAJT,IAAI,CAIWkK,UAAU,CAAC,CACpC,GAAID,EAAO,CAEP,IAAMG,EAAMH,EAAMI,sBAAsB,CAAC,CACrC7T,EAAGkS,EAAO5B,EAAE,CACZvP,EAAGmR,EAAO1B,EAAE,AAChB,GAAIsD,EAAML,EAAMI,sBAAsB,CAAC,CACnC7T,EAAGkS,EAAO/B,EAAE,CACZpP,EAAGmR,EAAO3B,EAAE,AAChB,GAEAwD,EAAQ/Q,EAAQgR,sBAAsB,CAAC,CACnChU,EAAG4T,EAAI5T,CAAC,CACRe,EAAG6S,EAAI7S,CAAC,AACZ,GAAIkT,EAAQjR,EAAQgR,sBAAsB,CAAC,CACvChU,EAAG8T,EAAI9T,CAAC,CACRe,EAAG+S,EAAI/S,CAAC,AACZ,GACAmR,EAAS,CACL5B,GAAIyD,EAAM/T,CAAC,CACXwQ,GAAIuD,EAAMhT,CAAC,CACXoP,GAAI8D,EAAMjU,CAAC,CACXuQ,GAAI0D,EAAMlT,CAAC,AACf,CACJ,CACAiC,EAAQkR,WAAW,CAAChC,EAAQ,KAAK,EAAG,CAAA,GACpC1I,AA9BU,IAAI,CA8BRD,MAAM,CAAC4K,OAAO,CAAG,CAAA,EACvB1S,EAAM2S,MAAM,CAACZ,EACjB,CACJ,CACJ,CACAxC,GAAgBG,GAASjS,SAAS,CAAE,CAChCmL,gBAAiBiB,EAA2BlB,YAAY,CAACC,eAAe,CACxEZ,iBAAkB6B,EAA2BlB,YAAY,CAACX,gBAAgB,CAC1Ea,QAASgB,EAA2BlB,YAAY,CAACE,OAAO,AAC5D,GAoBA,GAAM,CAAEsB,SAAUyI,EAA0B,CAAE,CAAI/U,IAk1BfgV,GApRX,CAcpBjI,OAAQ,CAAC,EAAG,EAAE,CAiBdkI,cAAe,KAAK,EAUpBC,QAAS,KAAK,EAgBdrU,QAAS,EAgBT6O,WAAY,CAgBR+C,KAAM,KAAK,EAcX0C,UAAW,KAAK,EAehBC,SAAU,KAAK,CACnB,EAiBAhM,KAAM,KAAK,EASXiM,aAAc,CASVC,YAAa,UAObC,YAAa,EAQb1U,QAAS,MAUT2U,WAAY,iBAyFZC,MAAO,SACX,CACJ,EASMC,GAAgEtX,EAAwD,OAAU,CAACuX,UAAU,CACnJ,IAAIC,GAAqEjX,EAAoBC,CAAC,CAAC8W,IAa/F,GAAM,CAAEG,IAAAA,EAAG,CAAE,CAAI7V,IAEX,CAAE8V,OAAAA,EAAM,CAAE,CAAIF,KAEd,CAAEG,MAAAA,EAAK,CAAEnU,OAAQoU,EAAyB,CAAEpQ,MAAOqQ,EAAwB,CAAEnU,KAAMoU,EAAuB,CAAE,CAAIlW,KAOtH,AAAC,SAAUtB,CAAkB,EAqBzB,SAASyX,EAAuBpS,CAAM,EAClC,OAAO,IAAI,CAACL,OAAO,EAAI,IAAI,CAACA,OAAO,CAAC0S,sBAAsB,CAACrS,EAC/D,CAiBA,SAASsS,EAAuBnM,CAAK,EACjC,OAAO,IAAI,CAACxG,OAAO,EAAI,IAAI,CAACA,OAAO,CAAC4S,sBAAsB,CAACpM,EAC/D,CAsBA,SAASqM,EAAyBC,CAAM,CAAEC,CAAS,EAW/C,IAAMC,EAAQ,IAAI,CAACrU,OAAO,CAACF,KAAK,CAACuU,KAAK,EAAIb,GAAIa,KAAK,CACnD,GAAI,CAACA,EAAO,CACRX,GAAM,GAAI,CAAA,EAAO,IAAI,EACrB,MACJ,CACA,GAAM,CAAEY,YAAAA,EAAc,CAAC,CAAEC,YAAAA,EAAc,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAEC,MAAAA,EAAQ,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAEC,KAAAA,EAAO,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAEC,KAAAA,EAAO,CAAC,CAAE,CAAGT,EAC7GU,EAAYT,EAAMD,EAAUW,GAAG,CAAE,CAACZ,EAAOa,GAAG,CAAEb,EAAOc,GAAG,CAAC,EAAGC,EAAWd,EAAUc,QAAQ,EAC1Fd,EAAUrB,QAAQ,EAAI9R,KAAKkU,GAAG,CAACf,EAAUrB,QAAQ,EAAIqC,EAAWhB,EAAUgB,QAAQ,EAClFhB,EAAUrB,QAAQ,EAAI9R,KAAKoU,GAAG,CAACjB,EAAUrB,QAAQ,EAAIuC,EAAUlB,EAAUrB,QAAQ,CAAG,CACrF+B,CAAS,CAAC,EAAE,CAAGI,EAAWJ,CAAS,CAAC,EAAE,CAAGM,EACzC,CAACN,CAAS,CAAC,EAAE,CAAGM,EAAWN,CAAS,CAAC,EAAE,CAAGI,EAC7C,CAAGJ,EACJ,MAAO,CACHzW,EAAG,AAAC,CAAA,AAACiX,CAAAA,CAAO,CAAC,EAAE,CAAGZ,CAAM,EAAKD,EAAQE,CAAG,EAAKH,EAAUF,EACvDlV,EAAG,CAAE,CAAA,AAAC,CAAA,AAACwV,CAAAA,EAAUU,CAAO,CAAC,EAAE,AAAD,EAAKb,EAAQI,CAAG,EAAKL,EAAUD,CAAU,CACvE,CACJ,CAwBA,SAASgB,EAAuB1N,CAAK,CAAEuM,CAAS,EAC5C,IAAMC,EAAQ,IAAI,CAACrU,OAAO,CAACF,KAAK,CAACuU,KAAK,EAAIb,GAAIa,KAAK,CACnD,GAAI,CAACA,EAAO,CACRX,GAAM,GAAI,CAAA,EAAO,IAAI,EACrB,MACJ,CACA,GAAI7L,AAAY,OAAZA,EAAMzI,CAAC,CACP,OAEJ,GAAM,CAAEkV,YAAAA,EAAc,CAAC,CAAEC,YAAAA,EAAc,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAEC,MAAAA,EAAQ,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAEC,KAAAA,EAAO,CAAC,CAAEC,QAAAA,EAAU,CAAC,CAAEC,KAAAA,EAAO,CAAC,CAAE,CAAGT,EAC7GoB,EAAa,CACfnX,EAAG,AAAC,CAAA,AAACwJ,CAAAA,EAAMxJ,CAAC,CAAGiW,CAAU,EAAKE,EAAUG,CAAG,EAAKF,EAAQC,EACxDtV,EAAG,AAAC,CAAA,AAACyI,CAAAA,EAAMzI,CAAC,CAAGmV,CAAU,EAAKC,EAAUK,CAAG,EAAKJ,EAAQG,CAC5D,EAAGM,EAAWd,EAAUc,QAAQ,EAC3Bd,EAAUrB,QAAQ,EAAI9R,KAAKkU,GAAG,CAACf,EAAUrB,QAAQ,EAAIqC,EAAWhB,EAAUgB,QAAQ,EAClFhB,EAAUrB,QAAQ,EAAI9R,KAAKoU,GAAG,CAACjB,EAAUrB,QAAQ,EAEtD+B,EAAYT,EAAMD,EAAUW,GAAG,CAAE,QAASX,EAAUrB,QAAQ,CAAG,CAC3D1U,EAAGmX,EAAWnX,CAAC,CAAG6W,EAAWM,CAAAA,CAAAA,EAAWpW,CAAC,CAAIgW,CAAO,EACpDhW,EAAGoW,EAAWnX,CAAC,CAAG+W,EAAWI,EAAWpW,CAAC,CAAG8V,CAChD,EAAIM,GACJ,MAAO,CAAEP,IAAKH,EAAU1V,CAAC,CAAE4V,IAAKF,EAAUzW,CAAC,AAAC,CAChD,CAmGA,SAASoX,EAASC,CAAQ,CAAEC,CAAU,EAE7BA,GACDA,CAAAA,EAAa1Y,OAAO2Y,IAAI,CAACF,EAASG,OAAO,CAAC,CAAC,EAAE,AAAD,EAEhD,IAAMxY,EAAMqY,EAASG,OAAO,CAACF,EAAW,CAExC,GAAItY,CAAG,CAAC,qBAAqB,EACzBA,CAAG,CAAC,qBAAqB,CAACkI,KAAK,GAAKmQ,EAASnQ,KAAK,CAClD,OAAOlI,CAAG,CAAC,qBAAqB,CAGpC,IAAIyY,EAAYJ,EAASK,IAAI,CAC7B,GAAIL,EAAStB,SAAS,CAAE,CACpB,IACI4B,EAAe3X,EAAGe,EADhB2W,EAAOL,EAASK,IAAI,CAAE,CAAEtB,MAAAA,CAAK,CAAEwB,UAAAA,CAAS,CAAE,CAAGP,EAAStB,SAAS,CAErE0B,EAAY,EAAE,CACd,IAAK,IAAI7K,EAAI,EAAGiL,EAAOH,EAAKtR,MAAM,CAAEwG,EAAIiL,EAAM,EAAEjL,EAAG,CAC/C,IAAME,EAAY4K,CAAI,CAAC9K,EAAE,CACzB6K,EAAUtQ,IAAI,CAACwQ,EAAgB,EAAE,EACjC3X,EAAI,EACJe,EAAI,EACJ,IAAK,IAAI+W,EAAI,EAAGC,EAAOjL,EAAU1G,MAAM,CAAE0R,EAAIC,EAAM,EAAED,EACjDH,EAAcxQ,IAAI,CAAC,CACf,AAACnH,CAAAA,GAAK8M,CAAS,CAACgL,EAAE,CAAC,EAAE,AAAD,EAAK1B,CAAK,CAAC,EAAE,CAAGwB,CAAS,CAAC,EAAE,CAChD,AAAC7W,CAAAA,GAAK+L,CAAS,CAACgL,EAAE,CAAC,EAAE,AAAD,EAAK1B,CAAK,CAAC,EAAE,CAAGwB,CAAS,CAAC,EAAE,CACnD,CAET,CACJ,CAGA,IAAMI,EAAoB,AAACN,GACvB,AAAI,AAAmB,UAAnB,OAAOA,CAAI,CAAC,EAAE,CACPA,EAAKO,MAAM,CAAC,CAACC,EAAaC,EAAOvL,KACpC,IAAIwL,EAAMD,EAAQ,EAAIV,CAAS,CAAC,CAACU,EAAM,CAAGV,CAAS,CAACU,EAAM,CAW1D,OAPIA,EAAQ,EAERC,AADAA,CAAAA,EAAMA,EAAIvU,KAAK,CAAC,EAAG+I,AAAM,IAANA,EAAUwL,EAAIhS,MAAM,CAAGgS,EAAIhS,MAAM,CAAG,EAAC,EACpDiS,OAAO,GAENzL,GACLwL,CAAAA,EAAMA,EAAIvU,KAAK,CAAC,EAAC,EAEdqU,EAAYI,MAAM,CAACF,EAC9B,EAAG,EAAE,EAEFV,EAAKvI,GAAG,CAAC6I,GAEdO,EAAavZ,EAAIuZ,UAAU,CAAEC,EAAW,EAAE,CAChD,IAAK,IAAI5L,EAAI,EAAGiL,EAAOU,EAAWnS,MAAM,CAAEwG,EAAIiL,EAAM,EAAEjL,EAClD4L,EAASrR,IAAI,CAAC,CACVoH,KAAM,UACN4D,WAAYoG,CAAU,CAAC3L,EAAE,CAACuF,UAAU,CACpCb,SAAU,CACN/C,KAAMgK,CAAU,CAAC3L,EAAE,CAAC2B,IAAI,CACxB2J,YAAaK,CAAU,CAAC3L,EAAE,CAACsL,WAAW,EAClCF,EAAkBO,CAAU,CAAC3L,EAAE,CAAC8K,IAAI,CAC5C,CACJ,GAEJ,IAAMe,EAAU,CACZlK,KAAM,oBACNmK,UAAWrB,EAASqB,SAAS,CAC7BC,eAAgBtB,EAASsB,cAAc,CACvCC,aAAcvB,EAASuB,YAAY,CACnCJ,SAAAA,EACA,yBAA0BxZ,CAAG,CAAC,yBAAyB,CACvD6Z,KAAMxB,EAASwB,IAAI,CACnB3R,MAAOmQ,EAASnQ,KAAK,AACzB,EAEA,OADAlI,CAAG,CAAC,qBAAqB,CAAGyZ,EACrBA,CACX,CAMA,SAASK,EAAmB3V,CAAO,CAAEkL,CAAO,EACxCA,EAAUkH,GAAyB,CAAA,EAAM,IAAI,CAAC5T,OAAO,CAAC0M,OAAO,CAAEA,GAC/DlL,EAAQ/D,IAAI,CAAC,IAAI,CAAEiP,GAEf,IAAI,CAACA,OAAO,EAAI,IAAI,CAAC0K,cAAc,EACnC,IAAI,CAAC1K,OAAO,CAACzH,IAAI,CAAC,CACdM,MAAO,IAAI,CAAC6R,cAAc,AAC9B,EAER,CAjLA/a,EAAmB+F,OAAO,CAV1B,SAAiBiV,CAAU,EACvB,IAAMC,EAAaD,EAAW9Z,SAAS,AAClC+Z,CAAAA,EAAWC,mBAAmB,GAC/BD,EAAWE,iBAAiB,CAAG1D,EAC/BwD,EAAWG,iBAAiB,CAAGzD,EAC/BsD,EAAWC,mBAAmB,CAAGrD,EACjCoD,EAAWI,iBAAiB,CAAGnC,EAC/B1B,GAAwByD,EAAY,aAAcH,GAE1D,EAkFA9a,EAAmBya,OAAO,CAlD1B,SAAiBa,CAAI,CAAEC,EAAQ,KAAK,CAAEhQ,CAAM,EACxC,IAAM8F,EAAU,EAAE,CACZoJ,EAAUa,AAAc,aAAdA,EAAK/K,IAAI,CAAkB6I,EAASkC,GAAQA,EAAMd,EAAWC,EAAQD,QAAQ,CAC7F,IAAK,IAAI5L,EAAI,EAAGiL,EAAOW,EAASpS,MAAM,CAAEwG,EAAIiL,EAAM,EAAEjL,EAAG,CACnD,IACI4M,EADEC,EAAUjB,CAAQ,CAAC5L,EAAE,CAAE0E,EAAWmI,EAAQnI,QAAQ,EAAI,CAAC,EAAG/C,EAAO+C,EAAS/C,IAAI,CAAE2J,EAAc5G,EAAS4G,WAAW,CAAE/F,EAAasH,EAAQtH,UAAU,CAoBzJ,GAlBI,AAACoH,CAAAA,AAAU,QAAVA,GAAmBA,AAAU,cAAVA,CAAoB,GACvChL,CAAAA,AAAS,YAATA,GAAsBA,AAAS,iBAATA,CAAsB,EACzC2J,EAAY9R,MAAM,EAClBoT,CAAAA,EAAe,CAAElI,SAAU,CAAE4G,YAAAA,EAAa3J,KAAAA,CAAK,CAAE,CAAA,EAGhDgL,AAAU,YAAVA,GACJhL,CAAAA,AAAS,eAATA,GACGA,AAAS,oBAATA,CAAyB,EACzB2J,EAAY9R,MAAM,EAClBoT,CAAAA,EAAe,CAAElI,SAAU,CAAE4G,YAAAA,EAAa3J,KAAAA,CAAK,CAAE,CAAA,EAGtC,aAAVgL,GAAwBhL,AAAS,UAATA,GACzB2J,EAAY9R,MAAM,EAClBoT,CAAAA,EAAe,CAAElI,SAAU,CAAE4G,YAAAA,EAAa3J,KAAAA,CAAK,CAAE,CAAA,EAGrDiL,EAAc,CACd,IAAMzH,EAAOI,GAAeA,CAAAA,EAAWJ,IAAI,EAAII,EAAWuH,IAAI,AAAD,EAAI/C,EAAMxE,GAAcA,EAAWwE,GAAG,CAAEC,EAAMzE,GAAcA,EAAWyE,GAAG,CACvIvH,EAAQlI,IAAI,CAACmO,GAA0BkE,EAAc,CACjD5C,IAAK,AAAe,UAAf,OAAOA,EAAmBA,EAAM,KAAK,EAC1CD,IAAK,AAAe,UAAf,OAAOA,EAAmBA,EAAM,KAAK,EAC1C5E,KAAM,AAAgB,UAAhB,OAAOA,EAAoBA,EAAO,KAAK,EAS7CI,WAAAA,CACJ,GACJ,CACJ,CAOA,OAJI5I,GAAUkP,EAAQE,cAAc,GAChCpP,EAAO9H,KAAK,CAACkY,UAAU,CAAGvE,GAAO7L,EAAO9H,KAAK,CAACE,OAAO,CAAC0M,OAAO,EAAEG,QAAS,CAAEiK,QAASA,CAAQ,GAC3FlP,EAAO9H,KAAK,CAACsX,cAAc,CAAG3D,GAAO7L,EAAO9H,KAAK,CAACE,OAAO,CAAC0M,OAAO,EAAEI,YAAa,CAAEgK,QAASA,CAAQ,IAEhGpJ,CACX,EAmFArR,EAAmBoZ,QAAQ,CAAGA,CAelC,EAAGpZ,GAAuBA,CAAAA,EAAqB,CAAC,CAAA,GAMnB,IAAM4b,GAA2B5b,CAwM1DH,EA5BOA,EAkFRA,GAAsBA,CAAAA,EAAoB,CAAC,CAAA,GAtDxBgc,iBAAiB,CAXnC,SAA2BC,CAAM,EAC7B,IAAMC,EAAMD,EAAO7B,MAAM,CAAC,CAAC8B,EAAKvQ,KAC5BuQ,EAAI/Z,CAAC,EAAIwJ,EAAMxJ,CAAC,CAChB+Z,EAAIhZ,CAAC,EAAIyI,EAAMzI,CAAC,CACTgZ,GACR,CAAE/Z,EAAG,EAAGe,EAAG,CAAE,GAChB,MAAO,CACHf,EAAG+Z,EAAI/Z,CAAC,CAAG8Z,EAAO1T,MAAM,CACxBrF,EAAGgZ,EAAIhZ,CAAC,CAAG+Y,EAAO1T,MAAM,AAC5B,CACJ,EAoBAvI,EAAkBmc,wBAAwB,CAH1C,SAAkCC,CAAE,CAAEC,CAAE,EACpC,OAAOtX,KAAKuX,IAAI,CAACvX,KAAKwX,GAAG,CAACF,EAAGla,CAAC,CAAGia,EAAGja,CAAC,CAAE,GAAK4C,KAAKwX,GAAG,CAACF,EAAGnZ,CAAC,CAAGkZ,EAAGlZ,CAAC,CAAE,GACtE,EAaAlD,EAAkBwc,qBAAqB,CAHvC,SAA+BJ,CAAE,CAAEC,CAAE,EACjC,OAAOtX,KAAK0X,KAAK,CAACJ,EAAGla,CAAC,CAAGia,EAAGja,CAAC,CAAEka,EAAGnZ,CAAC,CAAGkZ,EAAGlZ,CAAC,CAC9C,EAuBAlD,EAAkB0c,cAAc,CAfhC,SAAwB,CAAEva,EAAAA,CAAC,CAAEe,EAAAA,CAAC,CAAE,CAAEyZ,CAAO,EACrC,IAAMC,EAAMD,EAAQpU,MAAM,CACtBwG,EAAGkL,EAAG4C,EAAS,CAAA,EACnB,IAAK9N,EAAI,EAAGkL,EAAI2C,EAAM,EAAG7N,EAAI6N,EAAK3C,EAAIlL,IAAK,CACvC,GAAM,CAAC0D,EAAIE,EAAG,CAAGgK,CAAO,CAAC5N,EAAE,CAAE,CAACuD,EAAII,EAAG,CAAGiK,CAAO,CAAC1C,EAAE,CAC9CtH,EAAKzP,GAAMwP,EAAKxP,GACff,EAAI,AAACmQ,CAAAA,EAAKG,CAAC,EACPvP,CAAAA,EAAIyP,CAAC,EACLD,CAAAA,EAAKC,CAAC,EACPF,GACJoK,CAAAA,EAAS,CAACA,CAAK,CAEvB,CACA,OAAOA,CACX,EAQyB,IAAMC,GAA8B9c,EAyCjE,SAAS+c,GAAYC,CAAc,CAAEC,CAAa,CAAEC,EAAS,CAAA,CAAI,EAC7D,IAAIC,EAAYF,CAAa,CAACA,EAAc1U,MAAM,CAAG,EAAE,CAAE6U,EAAWC,EAAWC,EAAcC,EAAaP,EAC1G,IAAK,IAAI/C,EAAI,EAAGA,EAAIgD,EAAc1U,MAAM,CAAE0R,IAAK,CAC3C,IAAMuD,EAAYD,EAClBH,EAAYH,CAAa,CAAChD,EAAE,CAC5BsD,EAAa,EAAE,CACfF,EAAYH,EAERM,CAAS,CAACA,EAAUjV,MAAM,CAAG,EAAE,CAE/BiV,CAAS,CAAC,EAAE,CAChB,IAAK,IAAIzO,EAAI,EAAGA,EAAIyO,EAAUjV,MAAM,CAAEwG,IAE9B0O,GAASN,EAAWC,EADxBE,EAAeE,CAAS,CAACzO,EAAE,GAElB0O,GAASN,EAAWC,EAAWC,IAChCE,EAAWjU,IAAI,CAACoU,GAAaP,EAAWC,EAAWC,EAAWC,IAElEC,EAAWjU,IAAI,CAACgU,IAEXG,GAASN,EAAWC,EAAWC,IACpCE,EAAWjU,IAAI,CAACoU,GAAaP,EAAWC,EAAWC,EAAWC,IAElED,EAAYC,EAEhBH,EAAYC,CAChB,CACA,OAAOG,CACX,CAEA,SAASE,GAASN,CAAS,CAAEC,CAAS,CAAEO,CAAC,EACrC,MAAQ,AAACP,CAAAA,CAAS,CAAC,EAAE,CAAGD,CAAS,CAAC,EAAE,AAAD,EAAMQ,CAAAA,CAAC,CAAC,EAAE,CAAGR,CAAS,CAAC,EAAE,AAAD,EACvD,AAACC,CAAAA,CAAS,CAAC,EAAE,CAAGD,CAAS,CAAC,EAAE,AAAD,EAAMQ,CAAAA,CAAC,CAAC,EAAE,CAAGR,CAAS,CAAC,EAAE,AAAD,CAC3D,CAEA,SAASO,GAAaP,CAAS,CAAEC,CAAS,CAAEC,CAAS,CAAEC,CAAY,EAC/D,IAAMM,EAAK,CACPT,CAAS,CAAC,EAAE,CAAGC,CAAS,CAAC,EAAE,CAC3BD,CAAS,CAAC,EAAE,CAAGC,CAAS,CAAC,EAAE,CAC9B,CAAES,EAAK,CACJR,CAAS,CAAC,EAAE,CAAGC,CAAY,CAAC,EAAE,CAC9BD,CAAS,CAAC,EAAE,CAAGC,CAAY,CAAC,EAAE,CACjC,CAAEQ,EAAKX,CAAS,CAAC,EAAE,CAAGC,CAAS,CAAC,EAAE,CAAGD,CAAS,CAAC,EAAE,CAAGC,CAAS,CAAC,EAAE,CAAEW,EAAKV,CAAS,CAAC,EAAE,CAAGC,CAAY,CAAC,EAAE,CAAGD,CAAS,CAAC,EAAE,CAAGC,CAAY,CAAC,EAAE,CAAEU,EAAK,EAAKJ,CAAAA,CAAE,CAAC,EAAE,CAAGC,CAAE,CAAC,EAAE,CAAGD,CAAE,CAAC,EAAE,CAAGC,CAAE,CAAC,EAAE,AAAD,EAAIH,EAAe,CAC9L,AAACI,CAAAA,EAAKD,CAAE,CAAC,EAAE,CAAGE,EAAKH,CAAE,CAAC,EAAE,AAAD,EAAKI,EAC5B,AAACF,CAAAA,EAAKD,CAAE,CAAC,EAAE,CAAGE,EAAKH,CAAE,CAAC,EAAE,AAAD,EAAKI,EAC/B,CAED,OADAN,EAAaO,cAAc,CAAG,CAAA,EACvBP,CACX,CAsBA,IAAMQ,GAAOnZ,KAAKmZ,IAAI,EACjB,CAAA,AAAC7d,GAAOA,AAAM,IAANA,EAAU,EAAIA,EAAI,EAAI,EAAI,EAAE,EAAsB8d,GAAgCpZ,KAAKqZ,EAAE,CAAG,IAAKC,GAAStZ,KAAKqZ,EAAE,CAAG,EAAiBE,GAAO,AAACpb,GAAM6B,KAAKwZ,GAAG,CAAC,AAACF,CAAAA,GAASnb,CAAAA,EAAK,GAwErJsb,GAlEnC,MAMIvW,YAAYnE,CAAO,CAAE,CACjB,IAAM8S,EAAY,AAAC9S,CAAAA,EAAQ8S,SAAS,EAAI,EAAE,AAAD,EACpCtF,GAAG,CAAC,AAACjR,GAAMA,EAAI8d,IAAgCM,EAAO7H,CAAS,CAAC,EAAE,EAAI,EAAG8H,EAAO9H,CAAS,CAAC,EAAE,EAAI6H,EAAME,EAAU5Z,KAAKkU,GAAG,CAACwF,EACvF,CAAA,UAAnC,OAAO3a,EAAQ8a,eAAe,EAC9B,CAAA,IAAI,CAACA,eAAe,CAAG9a,EAAQ8a,eAAe,AAAD,EAGjD,IAAIve,EAAIoe,IAASC,EACb3Z,KAAKoU,GAAG,CAACsF,GACT1Z,KAAKmM,GAAG,CAACyN,EAAU5Z,KAAKkU,GAAG,CAACyF,IAAS3Z,KAAKmM,GAAG,CAACoN,GAAKI,GAAQJ,GAAKG,GAClD,CAAA,MAAd1Z,KAAKC,GAAG,CAAC3E,IACTA,CAAAA,EAAI,AAAiB,MAAhB6d,CAAAA,GAAK7d,IAAM,CAAA,CAAS,EAE7B,IAAI,CAACA,CAAC,CAAGA,EACT,IAAI,CAACuR,CAAC,CAAG+M,EAAU5Z,KAAKwX,GAAG,CAAC+B,GAAKG,GAAOpe,GAAKA,CACjD,CAMAqU,QAAQlP,CAAM,CAAE,CACZ,GAAM,CAAEoM,EAAAA,CAAC,CAAEvR,EAAAA,CAAC,CAAEue,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAAE9F,EAAMtT,CAAM,CAAC,EAAE,CAAG2Y,GACtDpF,EAAMvT,CAAM,CAAC,EAAE,CAAG2Y,EAClBvM,CAAAA,EAAI,EACAmH,EAAM,CAACsF,GArCqH,MAsC5HtF,CAAAA,EAAM,CAACsF,GAtCqH,IAsCxG,EAIpBtF,EAAMsF,GA1CsH,MA2C5HtF,CAAAA,EAAMsF,GA3CsH,IA2CzG,EAG3B,IAAM5X,EAAImL,EAAI7M,KAAKwX,GAAG,CAAC+B,GAAKvF,GAAM1Y,GAAI8B,EAAIsE,EAAI1B,KAAKoU,GAAG,CAAC9Y,EAAIyY,GA9Cd,SA8C4B5V,EAAI,AAAC0O,CAAAA,EAAInL,EAAI1B,KAAKkU,GAAG,CAAC5Y,EAAIyY,EAAG,EA9CzD,SA8CqE+F,EAAK,CAAC1c,EAAGe,EAAE,CAO7H,OANI0b,GAAoBzc,CAAAA,EAAIyc,EAAgBnM,EAAE,EAC1CtQ,EAAIyc,EAAgBtM,EAAE,EACtBpP,EAAI0b,EAAgBjM,EAAE,EACtBzP,EAAI0b,EAAgBlM,EAAE,AAAD,GACrBmM,CAAAA,EAAGC,OAAO,CAAG,CAAA,CAAG,EAEbD,CACX,CACAzN,QAAQyN,CAAE,CAAE,CACR,GAAM,CAAEjN,EAAAA,CAAC,CAAEvR,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAE8B,EAAI0c,CAAE,CAAC,EAAE,CAxDa,SAwDgBE,EAAKnN,EAApBiN,CAAE,CAAC,EAAE,CAxDN,SAwD4BG,EAAMd,GAAK7d,GAAK0E,KAAKuX,IAAI,CAACna,EAAIA,EAAI4c,EAAKA,GAC5GE,EAAIla,KAAK0X,KAAK,CAACta,EAAG4C,KAAKC,GAAG,CAAC+Z,IAAOb,GAAKa,GAI3C,OAHIA,EAAK1e,EAAI,GACT4e,CAAAA,GAAKla,KAAKqZ,EAAE,CAAGF,GAAK/b,GAAK+b,GAAKa,EAAE,EAE7B,CACH,AAACE,EAAI5e,EAAK8d,GACV,AAAC,CAAA,EAAIpZ,KAAKma,IAAI,CAACna,KAAKwX,GAAG,CAAC3K,EAAIoN,EAAK,EAAI3e,IAAMge,EAAK,EAAKF,GACxD,AACL,CACJ,EAwBmEgB,GAAIpa,KAAKuX,IAAI,CAAC,GAAK,EA8DnD8C,GAxDnC,MACInX,aAAc,CAMV,IAAI,CAACoM,MAAM,CAAG,CACV5B,GAAI,oBACJH,GAAI,mBACJK,GAAI,mBACJD,GAAI,iBACR,CACJ,CAMAgC,QAAQlP,CAAM,CAAE,CACZ,IAAM/E,EAAIsE,KAAKqZ,EAAE,CAAG,IAAKiB,EAAWta,KAAKua,IAAI,CAACH,GAAIpa,KAAKoU,GAAG,CAAC3T,CAAM,CAAC,EAAE,CAAG/E,IAAK8e,EAAaF,EAAWA,EAAUG,EAAeD,EAAaA,EAAaA,EAKvJ,MAAO,CAJG/Z,CAAM,CAAC,EAAE,CAAG/E,EAAIsE,KAAKkU,GAAG,CAACoG,GA3BmE,kBA4BjGF,CAAAA,GAAKM,CAAAA,AA5BP,SA6BK,oBAASF,EACTC,EAAgB,CAAA,qBAAS,QAASD,CAAS,CAAC,CAAC,EAAQF,AA9ByC,kBA8BzCA,EAA+BI,CAAAA,AA9BzF,SA8B8FC,AA9B/E,SA8BoFH,EAAaC,EAAgBG,CAAAA,AA9BjG,OA8BsGC,AA9BvF,QA8B4FL,CAAS,CAAC,EAC1I,AACjB,CACAnO,QAAQyN,CAAE,CAAE,CACR,IAAM1c,EAAI0c,CAAE,CAAC,EAAE,CAlCuF,kBAkClE3b,EAAI2b,CAAE,CAAC,EAAE,CAlCyD,kBAkCpCpe,EAAI,IAAMsE,KAAKqZ,EAAE,CAC/EiB,EAAWnc,EAAGqc,EAAYC,EAAcK,EAASC,EACrD,IAAK,IAAI/Q,EAAI,EAAGA,EAAI,KAEhByQ,EAAeD,AADfA,CAAAA,EAAaF,EAAWA,CAAO,EACHE,EAAaA,EACzCM,EAAKR,EAAYI,CAAAA,AAvClB,SAuCuBC,AAvCR,SAuCaH,EAAaC,EAAgBG,CAAAA,AAvC1B,OAuC+BC,AAvChB,QAuCqBL,CAAS,CAAC,EAAKrc,EAEjFmc,GAAYS,EAAOD,EADbJ,CAAAA,AAxCP,SAwCY,oBAASF,EAAaC,EAAgB,CAAA,qBAAS,QAASD,CAAS,CAAC,GAEzExa,CAAAA,AARuF,KAQvFA,KAAKC,GAAG,CAAC8a,EAAc,GANP,EAAE/Q,GAW1ByQ,EAAeD,AADfA,CAAAA,EAAaF,EAAWA,CAAO,EACHE,EAAaA,EACzC,IAAMzG,EAAMrY,EAAI0e,GAAIhd,EAAKsd,CAAAA,AAhDtB,SAgD2B,oBAASF,EAAaC,EAC/C,CAAA,qBAAS,QAASD,CAAS,CAAC,EAAKxa,KAAKkU,GAAG,CAACoG,GAAWtG,EAAMtY,EAAIsE,KAAKua,IAAI,CAACva,KAAKoU,GAAG,CAACkG,GAAYF,WAEnG,AAAIpa,KAAKC,GAAG,CAAC8T,GAAO,IACT,CAACiH,IAAKA,IAAI,CAEd,CAACjH,EAAKC,EAAI,AACrB,CACJ,EAkBMiH,GAAYjb,KAAKqZ,EAAE,CAAG,EAAG6B,GAAiBlb,KAAKqZ,EAAE,CAAG,IA2CvB8B,GArCnC,MACIjY,aAAc,CAMV,IAAI,CAACoM,MAAM,CAAG,CACV5B,GAAI,oBACJH,GAAI,mBACJK,GAAI,oBACJD,GAAI,kBACR,CACJ,CAMAgC,QAAQlP,CAAM,CAAE,CACZ,MAAO,CACHA,CAAM,CAAC,EAAE,CAAGya,GA3BsD,SA4BlE,WAAsBlb,KAAKmM,GAAG,CAACnM,KAAKwZ,GAAG,CAACyB,GAAY,GAAMxa,CAAM,CAAC,EAAE,CAAGya,KACzE,AACL,CACA7O,QAAQyN,CAAE,CAAE,CACR,MAAO,CACH,AAACA,CAAE,CAAC,EAAE,CAjC4D,SAiCzCoB,GACzB,IAAOlb,CAAAA,KAAKma,IAAI,CAACna,KAAKob,GAAG,CAAC,GAAOtB,CAAAA,CAAE,CAAC,EAAE,CAlC4B,QAkCd,IAAOmB,EAAQ,EAAKC,GAC3E,AACL,CACJ,EAkBMG,GAAuBrb,KAAKqZ,EAAE,CAAG,IAiDJiC,GA3CnC,MACIpY,aAAc,CAMV,IAAI,CAACqY,mBAAmB,CAAG,CAAA,EAC3B,IAAI,CAACjM,MAAM,CAAG,CACV5B,GAAI,mBACJH,GAhBqD,kBAiBrDK,GAAI,mBACJD,GAlBqD,iBAmBzD,CACJ,CAMAgC,QAAQlP,CAAM,CAAE,CACZ,IAAM+a,EAAS/a,CAAM,CAAC,EAAE,CAAsBuT,EAAMyH,AAAjBhb,CAAM,CAAC,EAAE,CAAiB4a,GAAsBvB,EAAK,CACpF9Z,KAAKkU,GAAG,CAACF,GAAOhU,KAAKoU,GAAG,CAACoH,EAASH,IA5BmB,kBA6BrDrb,AA7BqD,kBA6BrDA,KAAKoU,GAAG,CAACJ,GACZ,CAID,MAHIwH,CAAAA,EAAS,KAAOA,EAAS,EAAC,GAC1B1B,CAAAA,EAAGC,OAAO,CAAG,CAAA,CAAG,EAEbD,CACX,CACAzN,QAAQyN,CAAE,CAAE,CACR,IAAM1c,EAAI0c,CAAE,CAAC,EAAE,CArC0C,kBAqCnB3b,EAAI2b,CAAE,CAAC,EAAE,CArCU,kBAqCa4B,EAAI1b,KAAKuX,IAAI,CAACna,EAAIA,EAAIe,EAAIA,GAAI0O,EAAI7M,KAAKua,IAAI,CAACmB,GAAIC,EAAO3b,KAAKoU,GAAG,CAACvH,GACtI,MAAO,CACH7M,KAAK0X,KAAK,CAACta,EAAIue,EAAMD,EAFwH1b,KAAKkU,GAAG,CAACrH,IAErHwO,GACjCrb,KAAKua,IAAI,CAACmB,GAAKvd,EAAIwd,EAAOD,GAAKL,GAClC,AACL,CACJ,EAkBoBO,GAAsB5b,KAAKqZ,EAAE,CAAG,IAgDjBwC,GA1CnC,MACI3Y,aAAc,CAMV,IAAI,CAACoM,MAAM,CAAG,CACV5B,GAAI,oBACJH,GAAI,mBACJK,GAAI,mBACJD,GAAI,iBACR,EACA,IAAI,CAACmO,WAAW,CAAG,aACvB,CAMAnM,QAAQlP,CAAM,CAAE,CACZ,IAAMsb,EAAS/b,KAAKoU,GAAG,CAAC3T,CAAM,CAAC,EAAE,CAAGmb,IAAsB9B,EAAK,CAC3DpY,AA5BF,SA4BMjB,CAAM,CAAC,EAAE,CAAGmb,GAChBla,AA7BF,SA6BM1B,KAAKmM,GAAG,CAAC,AAAC,CAAA,EAAI4P,CAAK,EAAM,CAAA,EAAIA,CAAK,GAAM,EAC/C,CAID,OAHI/b,KAAKC,GAAG,CAACQ,CAAM,CAAC,EAAE,EAAI,IAAI,CAACqb,WAAW,EACtChC,CAAAA,EAAGC,OAAO,CAAG,CAAA,CAAG,EAEbD,CACX,CACAzN,QAAQyN,CAAE,CAAE,CACR,MAAO,CACHA,CAAE,CAAC,EAAE,CAAIpY,CAAAA,AAtCX,SAsCeka,EAAkB,EAC/B,AAAC,CAAA,EAAI5b,KAAKma,IAAI,CAACna,KAAKob,GAAG,CAACtB,CAAE,CAAC,EAAE,CAvC/B,WAuCyC9Z,KAAKqZ,EAAE,CAAG,CAAC,EAAKuC,GAC1D,AACL,CACJ,EAwDM,CAAEI,eAAgBC,EAAyB,CAAEjE,YAAakE,EAAsB,CAAE,CA3YpE,CAChBF,eAzEJ,SAAwBG,CAAI,CAAEjE,CAAa,EACvC,IAAM7P,EAAM,EAAE,CAAE6R,EAAIlC,GAAYmE,EAAMjE,EAAe,CAAA,GACrD,IAAK,IAAIlO,EAAI,EAAGA,EAAIkQ,EAAE1W,MAAM,CAAEwG,IAEtBkQ,CAAC,CAAClQ,EAAE,CAACkP,cAAc,EAAIgB,CAAC,CAAClQ,EAAI,EAAE,CAACkP,cAAc,GAC9C7Q,EAAI9D,IAAI,CAAC2V,EAAEkC,MAAM,CAAC,EAAGpS,IACrBA,EAAI,GAGJA,IAAMkQ,EAAE1W,MAAM,CAAG,GACjB6E,EAAI9D,IAAI,CAAC2V,GAGjB,OAAO7R,CACX,EA4DI2P,YAAAA,EACJ,EA2YM,CAAEqE,MAAAA,EAAK,CAAEC,MAAAA,EAAK,CAAE,CAAI5f,IAMpB6f,GAAqBvc,AAAU,EAAVA,KAAKqZ,EAAE,CAAO,IAcnCmD,GAAU,AAACzI,IAGTA,EAAM,MACNA,CAAAA,GAAO,GAAE,EAETA,EAAM,KACNA,CAAAA,GAAO,GAAE,EAENA,GAML0I,GAAM,AAACC,GAAY,AAAC,CAAA,EAAI1c,KAAKkU,GAAG,CAACwI,EAAO,EAAK,EAK7CC,GAAgB,CAACC,EAAQC,KAC3B,IAAM3I,EAAMlU,KAAKkU,GAAG,CAAEwF,EAAOkD,CAAM,CAAC,EAAE,CAAGL,GAAoBO,EAAOF,CAAM,CAAC,EAAE,CAAGL,GAAoB5C,EAAOkD,CAAM,CAAC,EAAE,CAAGN,GAAoBQ,EAAOF,CAAM,CAAC,EAAE,CAAGN,GAC9J,OADkPE,GAArD9C,EAAOD,GAA8DxF,EAAIwF,GAAQxF,EAAIyF,GAAQ8C,GAArEM,EAAOD,EAEhO,CAMA,OAAME,GAUF,OAAO9Y,IAAIiL,CAAI,CAAEtT,CAAU,CAAE,CACzBmhB,GAAWC,QAAQ,CAAC9N,EAAK,CAAGtT,CAChC,CAKA,OAAOqhB,SAASN,CAAM,CAAEC,CAAM,CAAE,CAC5B,GAAM,CAAEnF,MAAAA,CAAK,CAAEH,KAAAA,CAAI,CAAE,CAAGvX,KAAMyc,EAAME,GAAcC,EAAQC,GAC1D,OADqIM,AAAkB,OAAlE,CAAA,EAAIzF,EAAMH,EAAKkF,GAAMlF,EAAK,EAAIkF,GAAI,CAE3H,CAKA,OAAOW,SAASR,CAAM,CAAEC,CAAM,CAAEQ,CAAS,CAAEC,EAAe,GAAM,CAAE,CAC9D,GAAM,CAAE5F,MAAAA,CAAK,CAAExD,IAAAA,CAAG,CAAEE,IAAAA,CAAG,CAAEmD,KAAAA,CAAI,CAAE,CAAGvX,KAAMkd,EAAWF,GAAWE,QAAQ,CAAExD,EAAOkD,CAAM,CAAC,EAAE,CAAGL,GAAoBO,EAAOF,CAAM,CAAC,EAAE,CAAGL,GAAoB5C,EAAOkD,CAAM,CAAC,EAAE,CAAGN,GAAoBQ,EAAOF,CAAM,CAAC,EAAE,CAAGN,GAAoBgB,EAAiBrJ,EAAIwF,GAAQxF,EAAI4I,GAAOU,EAAiBtJ,EAAIyF,GAAQzF,EAAI6I,GAAOU,EAAiBvJ,EAAIwF,GAAQtF,EAAI0I,GAAOY,EAAiBxJ,EAAIyF,GAAQvF,EAAI2I,GAAOY,EAAUvJ,EAAIsF,GAAOkE,EAAUxJ,EAAIuF,GAAOkE,EAAgBX,EAASN,EAAQC,GAASiB,EAAcD,EAAgB,OAAQE,EAAS3J,EAAI0J,GAAcE,EAAQhe,KAAKie,KAAK,CAACJ,EAAgBP,GAAeY,EAAa,EAAE,CAI1lB,GAHIb,GACAa,EAAW3Z,IAAI,CAACqY,GAEhBoB,EAAQ,EAAG,CACX,IAAMG,EAAO,EAAIH,EACjB,IAAK,IAAII,EAAWD,EAAMC,EAAW,KACpCA,GAAYD,EAAM,CAEf,IAAME,EAAIjK,EAAI,AAAC,CAAA,EAAIgK,CAAO,EAAKN,GAAeC,EAAQO,EAAIlK,EAAIgK,EAAWN,GAAeC,EAAQ3gB,EAAIihB,EAAId,EAAiBe,EAAId,EAAgBrf,EAAIkgB,EAAIZ,EAAiBa,EAAIZ,EAA+Ca,EAAO7G,EAAlC2G,EAAIV,EAAUW,EAAIV,EAAyBrG,EAAKna,EAAIA,EAAIe,EAAIA,IAAKqgB,EAAO9G,EAAMvZ,EAAGf,GAC/Q8gB,EAAW3Z,IAAI,CAAC,CAACia,EAAOjC,GAAoBgC,EAAOhC,GAAmB,CAC1E,CACJ,CAIA,OAHIc,GACAa,EAAW3Z,IAAI,CAACsY,GAEbqB,CACX,CACA,OAAOO,gBAAgBC,CAAI,CAAE,CACzB,IAAI1U,EAAI0U,EAAKlb,MAAM,CAAG,EACtB,KAAOwG,KAIH,GAAI2U,AADkB3e,KAAKgO,GAAG,CAAChO,KAAKC,GAAG,CAACye,CAAI,CAAC1U,EAAE,CAAC,EAAE,CAAG0U,CAAI,CAAC1U,EAAI,EAAE,CAAC,EAAE,EAAGhK,KAAKC,GAAG,CAACye,CAAI,CAAC1U,EAAE,CAAC,EAAE,CAAG0U,CAAI,CAAC1U,EAAI,EAAE,CAAC,EAAE,GACtF,GAAI,CACpB,IAAMoT,EAAWJ,GAAWI,QAAQ,CAACsB,CAAI,CAAC1U,EAAE,CAAE0U,CAAI,CAAC1U,EAAI,EAAE,CACrDoT,CAAAA,EAAS5Z,MAAM,EACfkb,EAAKtC,MAAM,CAACpS,EAAI,EAAG,KAAMoT,EAEjC,CAER,CACA,OAAOwB,SAAS7f,CAAO,CAAE,CACrB,GAAM,CAAEoQ,KAAAA,CAAI,CAAE2C,SAAAA,CAAQ,CAAE,CAAG/S,GAAW,CAAC,EACvC,MAAO,CAACoQ,EAAM2C,GAAYA,EAAS+M,IAAI,CAAC,KAAK,CAACA,IAAI,CAAC,IACvD,CAMA3b,YAAYnE,EAAU,CAAC,CAAC,CAAE,CAItB,IAAI,CAAC4P,cAAc,CAAG,CAAA,EAGtB,IAAI,CAACmQ,gBAAgB,CAAG,CAAA,EACxB,IAAI,CAAChD,WAAW,CAAG,GACnB,IAAI,CAAC/c,OAAO,CAAGA,EACf,GAAM,CAAEoQ,KAAAA,CAAI,CAAE0K,gBAAAA,CAAe,CAAE/H,SAAAA,CAAQ,CAAE,CAAG/S,CAC5C,CAAA,IAAI,CAACggB,OAAO,CAAGjN,EAAW,IAAI,CAACkN,UAAU,CAAClN,GAAY,KAAK,EAC3D,IAAMmN,EAAuB9P,EAAO6N,GAAWC,QAAQ,CAAC9N,EAAK,CAAG,KAAK,EACjE8P,GACA,CAAA,IAAI,CAACC,GAAG,CAAG,IAAID,EAAqBlgB,EAAO,EAE/C,GAAM,CAAEmgB,IAAAA,CAAG,CAAEH,QAAAA,CAAO,CAAE,CAAG,IAAI,CACzBG,IACA,IAAI,CAACpD,WAAW,CAAGoD,EAAIpD,WAAW,EAAI,GACtC,IAAI,CAACgD,gBAAgB,CAAG,CAAA,GAExBC,GAAWG,GACX,IAAI,CAACvP,OAAO,CAAG,AAAClP,GAAWye,EAAIvP,OAAO,CAACoP,EAAQpP,OAAO,CAAClP,IACvD,IAAI,CAAC4L,OAAO,CAAG,AAACyN,GAAOiF,EAAQ1S,OAAO,CAAC6S,EAAI7S,OAAO,CAACyN,KAE9CoF,GACL,IAAI,CAACvP,OAAO,CAAG,AAAClP,GAAWye,EAAIvP,OAAO,CAAClP,GACvC,IAAI,CAAC4L,OAAO,CAAG,AAACyN,GAAOoF,EAAI7S,OAAO,CAACyN,IAE9BiF,IACL,IAAI,CAACpP,OAAO,CAAGoP,EAAQpP,OAAO,CAC9B,IAAI,CAACtD,OAAO,CAAG0S,EAAQ1S,OAAO,EAGlC,IAAI,CAACiD,MAAM,CAAGuK,AAAoB,UAApBA,EACVqF,GAAOA,EAAI5P,MAAM,CACjBuK,CACR,CAMAsF,qBAAqBhD,CAAI,CAAE,CACvB,GAAM,CAAEzO,GAAAA,CAAE,CAAEH,GAAAA,CAAE,CAAEK,GAAAA,CAAE,CAAED,GAAAA,CAAE,CAAE,CAAG,IAAI,CAAC2B,MAAM,EAAI,CAAC,EACrC8P,EAAe,CAACjD,EAAMkD,EAAKC,KAC7B,GAAM,CAACjI,EAAIC,EAAG,CAAG6E,EAAMoD,EAAWF,EAAAA,EAElC,GAAI,AAAe,UAAf,OAAOC,GAAoBjI,CAAE,CAACgI,EAAI,EAAIC,GAAQhI,CAAE,CAAC+H,EAAI,EAAIC,EAAK,CAC9D,IAAMlB,EAAY,AAACkB,CAAAA,EAAMjI,CAAE,CAACgI,EAAI,AAAD,EAAM/H,CAAAA,CAAE,CAAC+H,EAAI,CAAGhI,CAAE,CAACgI,EAAI,AAAD,EAAKG,EAAcnI,CAAE,CAACkI,EAAS,CAChFnB,EAAY9G,CAAAA,CAAE,CAACiI,EAAS,CAAGlI,CAAE,CAACkI,EAAS,AAAD,EAC1C,OAAOF,EAAM,CAACG,EAAaF,EAAI,CAAG,CAACA,EAAKE,EAAY,AACxD,CACJ,EACI7G,EAActQ,EAAM8T,CAAI,CAAC,EAAE,CAkB/B,MAjBKxD,CAAAA,EAAeyG,EAAajD,EAAM,EAAGzO,EAAE,GACxCrF,EAAMsQ,EAINwD,CAAI,CAAC,EAAE,CAAGxD,GAEJA,CAAAA,EAAeyG,EAAajD,EAAM,EAAG5O,EAAE,IAC7ClF,EAAMsQ,EACNwD,CAAI,CAAC,EAAE,CAAGxD,GAETA,CAAAA,EAAeyG,EAAajD,EAAM,EAAGvO,EAAE,EACxCvF,EAAMsQ,EAEAA,CAAAA,EAAeyG,EAAajD,EAAM,EAAGxO,EAAE,GAC7CtF,CAAAA,EAAMsQ,CAAW,EAEdtQ,CACX,CAMA2W,WAAWlN,CAAQ,CAAE,CACjB,IAAM2N,EAAc3N,CAAQ,CAAC,EAAE,CAAGyK,GAAoBmD,EAAW,AAAC5N,CAAAA,CAAQ,CAAC,EAAE,EAAI,CAAA,EAAKyK,GAAoBoD,EAAa,AAAC7N,CAAAA,CAAQ,CAAC,EAAE,EAAI,CAAA,EAAKyK,GACtIqD,EAAc5f,KAAKkU,GAAG,CAACwL,GAAWG,EAAc7f,KAAKoU,GAAG,CAACsL,GAAWI,EAAgB9f,KAAKkU,GAAG,CAACyL,GAAaI,EAAgB/f,KAAKoU,GAAG,CAACuL,GACzI,GAAIF,AAAgB,IAAhBA,GAAqBC,AAAa,IAAbA,GAAkBC,AAAe,IAAfA,EAI3C,MAAO,CACHhQ,QAAS,AAAClP,IAEN,IAAMsT,EAAMtT,CAAM,CAAC,EAAE,CAAG8b,GAAqBkD,EAEvCzL,EAAMvT,CAAM,CAAC,EAAE,CAAG8b,GAAoByD,EAAShgB,KAAKkU,GAAG,CAACF,GAAM5W,EAAI4C,KAAKkU,GAAG,CAACH,GAAOiM,EAAQ7hB,EAAI6B,KAAKoU,GAAG,CAACL,GAAOiM,EAAQjE,EAAS/b,KAAKoU,GAAG,CAACJ,GAAMiM,EAAIlE,EAAS6D,EAAcxiB,EAAIyiB,EACnL,MAAO,CACH7f,KAAK0X,KAAK,CAACvZ,EAAI2hB,EAAgBG,EAAIF,EAAe3iB,EAAIwiB,EAAc7D,EAAS8D,GAAetD,GAC5Fvc,KAAKua,IAAI,CAAC0F,EAAIH,EAAgB3hB,EAAI4hB,GAAiBxD,GACtD,AACL,EACAlQ,QAAS,AAAC6T,IAEN,IAAMnM,EAAMmM,CAAO,CAAC,EAAE,CAAG3D,GAEnBvI,EAAMkM,CAAO,CAAC,EAAE,CAAG3D,GAAoByD,EAAShgB,KAAKkU,GAAG,CAACF,GAAM5W,EAAI4C,KAAKkU,GAAG,CAACH,GAAOiM,EAAQ7hB,EAAI6B,KAAKoU,GAAG,CAACL,GAAOiM,EAAQjE,EAAS/b,KAAKoU,GAAG,CAACJ,GAAMiM,EAAIlE,EAAS+D,EAAgB3hB,EAAI4hB,EACtL,MAAO,CACH,AAAC/f,CAAAA,KAAK0X,KAAK,CAACvZ,EAAI2hB,EAAgB/D,EAASgE,EAAe3iB,EAAIwiB,EAAcK,EAAIJ,GAAeJ,CAAU,EAAKlD,GAC5Gvc,KAAKua,IAAI,CAAC0F,EAAIL,EAAcxiB,EAAIyiB,GAAetD,GAClD,AACL,CACJ,CACJ,CAMA5M,QAAQlP,CAAM,CAAE,CACZ,OAAOA,CACX,CAMA4L,QAAQyN,CAAE,CAAE,CACR,OAAOA,CACX,CACAqG,kBAAkBzB,CAAI,CAAE0B,CAAS,CAAE,CAC/B,IAiCIC,EAjCsBC,EAAgB,EAAE,CACtCC,EAAW,CAAC7B,EAAK,CACvB,IAAK,IAAI1U,EAAI,EAAGiL,EAAOyJ,EAAKlb,MAAM,CAAEwG,EAAIiL,EAAM,EAAEjL,EAAG,CAC/C,IAAMvJ,EAASie,CAAI,CAAC1U,EAAE,CAClBwW,EAAiB9B,CAAI,CAAC1U,EAAI,EAAE,CAChC,GAAI,CAACA,EAAG,CACJ,GAAI,CAACoW,EACD,SAGJI,EAAiB9B,CAAI,CAACA,EAAKlb,MAAM,CAAG,EAAE,AAC1C,CACA,IAAMsZ,EAAO0D,CAAc,CAAC,EAAE,CAAEzD,EAAOtc,CAAM,CAAC,EAAE,CAChD,GAGA,AAACqc,CAAAA,EAAO,KAAOA,EAAO,EAAC,GAClBC,CAAAA,EAAO,KAAOA,EAAO,EAAC,GAEvB,AAACD,EAAO,GAAQC,EAAO,EAAI,CAE3B,IAAMqB,EAAW/B,GAAM,AAACoE,CAAAA,AArBX,IAqB0B,AAAC3D,CAAAA,EAAO,GAAE,EAAK,GAAE,EACnD,CAAA,AAACC,CAAAA,EAAO,GAAE,EAAK,IAAM,AAACD,CAAAA,EAAO,GAAE,EAAK,GAAE,EAAI,EAAG,GAAI9I,EAAOwM,CAAc,CAAC,EAAE,CAC1EpC,EAAY3d,CAAAA,CAAM,CAAC,EAAE,CAAG+f,CAAc,CAAC,EAAE,AAAD,EAC5CF,EAAc/b,IAAI,CAAC,CACfyF,EACAgK,IAAAA,EACA0M,UAAW5D,EAAO,EAAI,EAAI,GAC1B0D,eAAAA,EACA/f,OAAAA,CACJ,EACJ,CACJ,CAEA,GAAI6f,EAAc9c,MAAM,EACpB,GAAI4c,EAAW,CAKPE,EAAc9c,MAAM,CAAG,GAAM,IAC7B6c,EAAoBC,EAAcrf,KAAK,GAAG0f,IAAI,CAAC,CAAChlB,EAAGiR,IAAM5M,KAAKC,GAAG,CAAC2M,EAAEoH,GAAG,EAAIhU,KAAKC,GAAG,CAACtE,EAAEqY,GAAG,EAAE,CAAC,EAAE,CAC9FsI,GAAMgE,EAAeD,IAIzB,IAAIrW,EAAIsW,EAAc9c,MAAM,CAAG,EAC/B,KAAOwG,GAAK,GAAG,CACX,IAAM4W,EAAQN,CAAa,CAACtW,EAAE,CAACA,CAAC,CAC1B6W,EAAUrE,GAAQiE,AAjDf,IAkDLH,AA3RN,KA2RMA,CAAa,CAACtW,EAAE,CAAC0W,SAAS,EACxBI,EAAWtE,GAAQiE,AAnDhB,IAoDLH,AA7RN,KA6RMA,CAAa,CAACtW,EAAE,CAAC0W,SAAS,EACxBzf,EAAQyd,EAAKtC,MAAM,CAACwE,EAAON,CAAa,CAACtW,EAAI,EAAE,CAACA,CAAC,CAAG4W,KAEvD5D,GAAWI,QAAQ,CAAC,CAACyD,EAASP,CAAa,CAACtW,EAAE,CAACgK,GAAG,CAAC,CAAE,CAAC6M,EAASP,CAAa,CAACtW,EAAI,EAAE,CAACgK,GAAG,CAAC,CAAE,CAAA,IAE7F/S,EAAMsD,IAAI,IAAIyY,GAAWI,QAAQ,CAAC,CAAC0D,EAAUR,CAAa,CAACtW,EAAI,EAAE,CAACgK,GAAG,CAAC,CAAE,CAAC8M,EAAUR,CAAa,CAACtW,EAAE,CAACgK,GAAG,CAAC,CAAE,CAAA,IAC1GuM,EAAShc,IAAI,CAACtD,GACd+I,GAAK,CACT,CAEA,GAAIqW,EACA,IAAK,IAAIrW,EAAI,EAAGA,EAAIuW,EAAS/c,MAAM,CAAEwG,IAAK,CACtC,GAAM,CAAE0W,UAAAA,CAAS,CAAE1M,IAAAA,CAAG,CAAE,CAAGqM,EAAmB3B,EAAO6B,CAAQ,CAACvW,EAAE,CAAEoF,EAAUsP,EAAKtP,OAAO,CAACiR,EAAkB5f,MAAM,EACjH,GAAI2O,EAAU,GAAI,CACd,IAAM2R,EAAgB,AAAC/M,CAAAA,EAAM,EAAI,GAAK,CAAA,EAClC,IAAI,CAAC8H,WAAW,CACdgB,EAAON,GAAQiE,AApEpB,IAqEGC,AA9Sd,KA8ScA,GACE3D,EAAOP,GAAQiE,AAtEpB,IAuEGC,AAhTd,KAgTcA,GACEM,EAAehE,GAAWI,QAAQ,CAAC,CAACN,EAAM9I,EAAI,CAAE,CAAC8I,EAAMiE,EAAc,CAAE,CAAA,GAK7E,IAAK,IAAIhN,EAAM+I,EAAO,IAAM4D,EAAW3M,EAAM,MAAQA,EAAM,IAAKA,GAAO,IAAM2M,EACzEM,EAAazc,IAAI,CAAC,CAACwP,EAAKgN,EAAc,EAE1CC,EAAazc,IAAI,IAAIyY,GAAWI,QAAQ,CAAC,CAACL,EAAMgE,EAAc,CAAE,CAAChE,EAAMsD,EAAkBrM,GAAG,CAAC,CAAE,CAAA,IAC/F0K,EAAKtC,MAAM,CAAChN,EAAS,KAAM4R,GAC3B,KACJ,CACJ,CAGR,KACK,CACD,IAAIhX,EAAIsW,EAAc9c,MAAM,CAC5B,KAAOwG,KAAK,CACR,IAAM4W,EAAQN,CAAa,CAACtW,EAAE,CAACA,CAAC,CAC1B/I,EAAQyd,EAAKtC,MAAM,CAACwE,EAAOlC,EAAKlb,MAAM,CAE5C,CACIgZ,GAAQiE,AA/FH,IAgGDH,AAzUV,KAyUUA,CAAa,CAACtW,EAAE,CAAC0W,SAAS,EAC9BJ,CAAa,CAACtW,EAAE,CAACgK,GAAG,CACvB,EAED/S,EAAMggB,OAAO,CAAC,CACVzE,GAAQiE,AArGH,IAsGDH,AA/UV,KA+UUA,CAAa,CAACtW,EAAE,CAAC0W,SAAS,EAC9BJ,CAAa,CAACtW,EAAE,CAACgK,GAAG,CACvB,EACDuM,EAAShc,IAAI,CAACtD,EAClB,CACJ,EAEJ,OAAOsf,CACX,CAKA/b,KAAKkK,CAAQ,CAAE,CACX,IAiBIwJ,EAjBE,CAAE5I,OAAAA,CAAM,CAAE4P,IAAAA,CAAG,CAAEH,QAAAA,CAAO,CAAE,CAAG,IAAI,CAE/Bva,EAAO,EAAE,CACT4b,EAAY1R,AAAkB,YAAlBA,EAAS/C,IAAI,EAC3B+C,AAAkB,iBAAlBA,EAAS/C,IAAI,CAIXmT,EAAmB,IAAI,CAACA,gBAAgB,CAIxCoC,EAAoB,CAAChC,GAAOA,AAA4B,CAAA,IAA5BA,EAAI3D,mBAAmB,CAGnD4F,EAAUD,EAAoBnC,EAAU,KAAK,EAC7CqC,EAAWF,GAAqBhC,GAAO,IAAI,CAE7C5P,GACA4I,CAAAA,EAAgB,CACZ,CAAC5I,EAAO5B,EAAE,CAAE4B,EAAO1B,EAAE,CAAC,CACtB,CAAC0B,EAAO/B,EAAE,CAAE+B,EAAO1B,EAAE,CAAC,CACtB,CAAC0B,EAAO/B,EAAE,CAAE+B,EAAO3B,EAAE,CAAC,CACtB,CAAC2B,EAAO5B,EAAE,CAAE4B,EAAO3B,EAAE,CAAC,CACzB,AAAD,EAEJ,IAAM0T,EAAY,AAACzJ,IAMf,IAAM8G,EAAO9G,EAAQrL,GAAG,CAAC,AAAC9L,IACtB,GAAIygB,EAAmB,CACfC,GACA1gB,CAAAA,EAAS0gB,EAAQxR,OAAO,CAAClP,EAAM,EAEnC,IAAIsT,EAAMtT,CAAM,CAAC,EAAE,AAlYrB,CAAA,KAmYMT,KAAKC,GAAG,CAAC8T,EArCJ,OAuCDA,EADAA,EAtCC,IAuCK0M,WAGAA,YAGdhgB,EAAS,CAACsT,EAAKtT,CAAM,CAAC,EAAE,CAAC,AAC7B,CACA,OAAOA,CACX,GACI8f,EAAW,CAAC7B,EAAK,CACjBI,IAEA9B,GAAWyB,eAAe,CAACC,GACvBwC,GACAX,CAAAA,EAAW,IAAI,CAACJ,iBAAiB,CAACzB,EAAM0B,EAAS,GAGzDG,EAASzS,OAAO,CAAC,AAAC4Q,QAKV4C,EACAC,EALJ,GAAI7C,EAAKlb,MAAM,CAAG,EACd,OAEJ,IAAIge,EAAU,CAAA,EAGVC,EAAM,CAAA,EACJC,EAAa,AAAC9a,IACX4a,EAKDhd,EAAKD,IAAI,CAAC,CAAC,IAAKqC,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,GAJnCpC,EAAKD,IAAI,CAAC,CAAC,IAAKqC,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,EACnC4a,EAAU,CAAA,EAKlB,EACIG,EAAc,CAAA,EAAOC,EAAa,CAAA,EAClC1K,EAASwH,EAAKnS,GAAG,CAAC,AAAC9L,IACnB,IAAMqZ,EAAKsH,EAASzR,OAAO,CAAClP,GAe5B,OAdIqZ,EAAGC,OAAO,CACV4H,EAAc,CAAA,EAGdC,EAAa,CAAA,EAIb9H,CAAE,CAAC,EAAE,GAAKlS,IACVkS,CAAE,CAAC,EAAE,CAAG,KAEHA,CAAE,CAAC,EAAE,GAAK,CAAClS,KAChBkS,CAAAA,CAAE,CAAC,EAAE,CAAG,KAAI,EAETA,CACX,GACA,GAAIoH,EAAmB,CAKnB,GAHId,GACAlJ,EAAO3S,IAAI,CAAC2S,CAAM,CAAC,EAAE,EAErByK,EAAa,CAEb,GAAI,CAACC,EACD,OAGJ,GAAI1J,GAEA,GAAIkI,EACAlJ,EAASgF,GAAuBhF,EAAQgB,QAGvC,GAAI5I,EAAQ,CACb2M,GAA0B/E,EAAQgB,GAC7BpK,OAAO,CAAC,AAACoJ,IACVsK,EAAU,CAAA,EACVtK,EAAOpJ,OAAO,CAAC4T,EACnB,GACA,MACJ,EAER,CACAxK,EAAOpJ,OAAO,CAAC4T,EAEnB,MAEI,IAAK,IAAI1X,EAAI,EAAGA,EAAIkN,EAAO1T,MAAM,CAAEwG,IAAK,CACpC,IAAMvJ,EAASie,CAAI,CAAC1U,EAAE,CAAEpD,EAAQsQ,CAAM,CAAClN,EAAE,AACpCpD,CAAAA,EAAMmT,OAAO,CAoCd0H,EAAM,CAAA,GA9BFrB,GAAa,CAACkB,IACdA,EAAmB7gB,EACnBie,EAAKna,IAAI,CAAC9D,GACVyW,EAAO3S,IAAI,CAACqC,IAKZ6a,GAAOF,IAQHnB,GAAatB,EAEb1B,AADiBJ,GAAWI,QAAQ,CAACmE,EAAiB9gB,GAC7CqN,OAAO,CAAC,AAACrN,GAAWihB,EAAWN,EAASzR,OAAO,CAAClP,KAIzD+gB,EAAU,CAAA,GAGlBE,EAAW9a,GACX2a,EAAkB9gB,EAClBghB,EAAM,CAAA,EAKd,CAER,EACJ,EAqBA,MApBI/S,AAAkB,eAAlBA,EAAS/C,IAAI,CACb0V,EAAU3S,EAAS4G,WAAW,EAEzB5G,AAAkB,oBAAlBA,EAAS/C,IAAI,CAClB+C,EAAS4G,WAAW,CAACxH,OAAO,CAAC,AAACjB,GAAMwU,EAAUxU,IAEzC6B,AAAkB,YAAlBA,EAAS/C,IAAI,EAClB+C,EAAS4G,WAAW,CAACxH,OAAO,CAAC,AAACjB,GAAMwU,EAAUxU,IAC1CrI,EAAKhB,MAAM,EACXgB,EAAKD,IAAI,CAAC,CAAC,IAAI,GAGI,iBAAlBmK,EAAS/C,IAAI,GAClB+C,EAAS4G,WAAW,CAACxH,OAAO,CAAC,AAACyS,IAC1BA,EAASzS,OAAO,CAAC,AAACjB,GAAMwU,EAAUxU,GACtC,GACIrI,EAAKhB,MAAM,EACXgB,EAAKD,IAAI,CAAC,CAAC,IAAI,GAGhBC,CACX,CACJ,CAMAwY,GAAWC,QAAQ,CAxkBQ,CACvB4E,WAAYxH,GACZyH,sBAAuBrI,GACvBsI,OAAQ5G,GACR6G,aAAc1G,GACd2G,YAAapG,EACjB,EAslBA,GAAM,CAAE1Z,SAAU+f,EAAgB,CAAE,CAAIxlB,IAIlC,CAAEib,eAAAA,EAAc,CAAE,CAAGI,GACrB,CAAEvD,SAAAA,EAAQ,CAAE,CAAGwC,GAEf,CAAE1J,eAAgB6U,EAAsB,CAAE,CAAG9U,GAG7C,CAAEjL,SAAUggB,EAAgB,CAAE/F,MAAOgG,EAAa,CAAEC,MAAAA,EAAK,CAAEvZ,UAAWwZ,EAAiB,CAAEC,QAAAA,EAAO,CAAExZ,SAAUyZ,EAAgB,CAAEC,SAAAA,EAAQ,CAAEC,SAAAA,EAAQ,CAAErgB,MAAOsgB,EAAa,CAAErkB,KAAMskB,EAAY,CAAEpgB,WAAYqgB,EAAkB,CAAE5Z,eAAgB6Z,EAAsB,CAAE,CAAIrmB,IAkB1QgQ,GAAO,CAAC,EAWZ,SAASsW,GAAepW,CAAC,CAAEqW,CAAY,EACnC,GAAM,CAAE5lB,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAG2lB,EAC1B,OAAOjjB,KAAKmM,GAAG,CAAC+W,AAnBF,WAkByCljB,KAAKgO,GAAG,CAAC,AAACpB,CAAAA,EAAEW,EAAE,CAAGX,EAAEc,EAAE,AAAD,EAAMrQ,CAAAA,EAxBpE,GAwBmF,EAAI,AAACuP,CAAAA,EAAEe,EAAE,CAAGf,EAAEgB,EAAE,AAAD,EAAMtQ,CAAAA,EAxBxG,GAwBwH,IACzF0C,KAAKmM,GAAG,CAAC,EACzD,CAMA,SAASgX,GAA6BvkB,CAAC,EAC/BA,EAAEwkB,aAAa,CAAC3W,OAAO,EACvB,IAAI,CAACrM,OAAO,EAAEkM,iBAAiB,IAAI,CAAE,CACjC,IAAI,CAACvN,OAAO,CAACF,KAAK,CAAC0N,GAAG,CACtB3N,EAAEwkB,aAAa,CAAC3W,OAAO,CAC1B,CAAE,IAAI,CAAC1N,OAAO,CAACskB,SAAS,EAAEC,WAEnC,CA2CA,MAAMC,GAMF,OAAOpiB,QAAQ4B,CAAa,CAAE,CACtB+f,GAAmBZ,GAAkB,aACrCxV,GAAO3J,EAAc2J,IAAI,CAEzB0V,GAAiBrf,EAAe,YAAa,WAQzC,IAAI,CAAC3C,OAAO,CAAG,IAAImjB,GAAQ,IAAI,CAAE,IAAI,CAACxkB,OAAO,CAACqB,OAAO,CACzD,EAAG,CAAEojB,MAAO,CAAE,GACdpB,GAAiBrf,EAAe,uBAAwBogB,IACxDf,GAAiBrf,EAAe,eAAgBogB,IAExD,CAKA,OAAOM,gBAAgBC,CAAa,CAAE,CAClC,GAAIA,EAAclgB,MAAM,CACpB,OAAOkgB,EACFziB,KAAK,CAAC,GACNoU,MAAM,CAAC,CAACsO,EAAKC,KACdD,EAAIjW,EAAE,CAAG1N,KAAK2J,GAAG,CAACga,EAAIjW,EAAE,CAAEkW,EAAIlW,EAAE,EAChCiW,EAAI/V,EAAE,CAAG5N,KAAK2J,GAAG,CAACga,EAAI/V,EAAE,CAAEgW,EAAIhW,EAAE,EAChC+V,EAAIpW,EAAE,CAAGvN,KAAKgO,GAAG,CAAC2V,EAAIpW,EAAE,CAAEqW,EAAIrW,EAAE,EAChCoW,EAAIhW,EAAE,CAAG3N,KAAKgO,GAAG,CAAC2V,EAAIhW,EAAE,CAAEiW,EAAIjW,EAAE,EACzBgW,GACRf,GAAcc,CAAa,CAAC,EAAE,EAGzC,CAKA,OAAOG,YAAYloB,CAAC,CAAEiR,CAAC,CAAE,CACrB,IAAMkX,EAAW,AAAC/S,IACd,IAAMgT,EAAK,CAAC,EAIZ,OAHAhT,EAAOjD,OAAO,CAAC,CAAC+C,EAAO7G,KACnB+Z,CAAE,CAAClT,GAASA,EAAMzJ,EAAE,EAAI,CAAC,CAAC,EAAE4C,EAAE,CAAC,CAAC,CAAG6G,CACvC,GACOkT,CACX,EACMC,EAAYpB,GAAckB,EAASnoB,GAAImoB,EAASlX,IAGtD,OAHoE5Q,OAC/D2Y,IAAI,CAACqP,GACLzX,GAAG,CAAC,AAACzQ,GAAQkoB,CAAS,CAACloB,EAAI,CAEpC,CAMAoH,YAAYrE,CAAK,CAAEE,CAAO,CAAE,CAMxB,IAAI,CAACklB,uBAAuB,CAAG,CAAA,EAC/B,IAAI,CAACC,cAAc,CAAG,EAAE,CACxB,IAAI,CAACnT,MAAM,CAAG,EAAE,CAChB,IAAI,CAACxT,OAAO,CAAG,CAAC,EAAG,EAAG,EAAG,EAAE,CAC3B,IAAI,CAAC4mB,kBAAkB,CAAG,CAAC,EACrB,IAAI,YAAYC,IAClB,IAAI,CAAC9X,gBAAgB,CAACzN,EAAO,CACzBA,EAAME,OAAO,CAACF,KAAK,CAAC0N,GAAG,IACpB,AAAC1N,CAAAA,EAAME,OAAO,CAAC4H,MAAM,EAAI,EAAE,AAAD,EAAG4F,GAAG,CAAC,AAACC,GAAMA,EAAEC,OAAO,EACvD,EAEL,IAAI,CAACnB,WAAW,CAAGvM,GAAW,CAAC,EAC/B,IAAMhD,EAAI6mB,GAAclR,GAAsB,IAAI,CAACyS,kBAAkB,CAAEplB,GAEjEslB,EAAY,IAAI,CAACF,kBAAkB,EAAEpT,OAAQuT,EAAYvlB,GAAWA,EAAQgS,MAAM,CACpFsT,GAAaC,GACbvoB,CAAAA,EAAEgV,MAAM,CAAGwS,GAAQM,WAAW,CAACQ,EAAWC,EAAS,EAEvD,IAAI,CAACzlB,KAAK,CAAGA,EAOb,IAAI,CAAC4K,MAAM,CAAG1N,EAAE0N,MAAM,CACtB,IAAI,CAAC1K,OAAO,CAAGhD,EACf,IAAI,CAACqQ,UAAU,CAAG,IAjN4B4Q,GAiNRjhB,EAAEqQ,UAAU,EAGlD,IAAI,CAAC6W,YAAY,CAAGpkB,EAAM0lB,OAAO,CAOjC,IAAI,CAACze,IAAI,CAAG/J,EAAE+J,IAAI,EAAI,EACtB,IAAI,CAAC0e,OAAO,CAAGzoB,EAAEyoB,OAAO,CAExB,IAAI,CAACC,YAAY,GAEjB,IAAI,CAACP,cAAc,CAAC3f,IAAI,CAAC6d,GAAiBvjB,EAAO,oBAAqB,KAClE,IAAI,CAACokB,YAAY,CAAG,IAAI,CAACyB,QAAQ,GAC7B,CAAA,AAAiB,KAAK,IAAtB,IAAI,CAACF,OAAO,EACZ,IAAI,CAACA,OAAO,GAAK,IAAI,CAAC1e,IAAI,AAAD,IAEzB,IAAI,CAACwL,WAAW,CAAC,KAAK,EAAG,KAAK,EAAG,CAAA,GAIjC,CAAC,IAAI,CAACzS,KAAK,CAAC8lB,WAAW,EACnBlC,GAAiB,IAAI,CAACnX,WAAW,CAACxF,IAAI,GACtC,CAAA,IAAI,CAACA,IAAI,CAAG,IAAI,CAACwF,WAAW,CAACxF,IAAI,AAAD,EAEhC,IAAI,CAACwF,WAAW,CAAC7B,MAAM,EACvBmZ,GAAc,CAAA,EAAM,IAAI,CAACnZ,MAAM,CAAE,IAAI,CAAC6B,WAAW,CAAC7B,MAAM,EAGpE,IACA,IAAI,CAACmb,WAAW,EACpB,CAUAH,cAAe,CACX,IAAM1lB,EAAU,IAAI,CAACA,OAAO,CAAEgS,EAAShS,EAAQgS,MAAM,CACjDA,GACAA,EAAOjD,OAAO,CAAC,AAACZ,IACZ,IAAM2D,EAAQ,IAAIuT,GAAa,IAAI,CAAExB,GAAc7jB,EAAQgT,YAAY,CAAE7E,IACzE,IAAI,CAAC6D,MAAM,CAACxM,IAAI,CAACsM,EACrB,EAER,CAiBAS,YAAYhC,CAAM,CAAE/R,CAAO,CAAEiU,EAAS,CAAA,CAAI,CAAEqT,CAAS,CAAE,CACnD,IAAMjY,EAAI0C,GAAU,IAAI,CAACD,kBAAkB,GAC3C,GAAIzC,EAAG,CACH,IAAMkY,EAAMjC,GAAatlB,EAAS+R,EAAS,EAAI,IAAI,CAACvQ,OAAO,CAACxB,OAAO,EAAGwnB,EAAY,IAAI,CAACL,QAAQ,CAAC,CAAA,GAAQM,EAASxC,GAAQsC,GAAOA,EAAM,CAACA,EAAKA,EAAKA,EAAKA,EAAI,AAC1J,CAAA,IAAI,CAACvnB,OAAO,CAAG,CACXwlB,GAAuBiC,CAAM,CAAC,EAAE,CAAED,EAAUznB,MAAM,EAClDylB,GAAuBiC,CAAM,CAAC,EAAE,CAAED,EAAU1nB,KAAK,EACjD0lB,GAAuBiC,CAAM,CAAC,EAAE,CAAED,EAAUznB,MAAM,EAClDylB,GAAuBiC,CAAM,CAAC,EAAE,CAAED,EAAU1nB,KAAK,EACpD,CAED,IAAI,CAAC4lB,YAAY,CAAG,IAAI,CAACyB,QAAQ,GACjC,IAAM5e,EAAOkd,GAAepW,EAAG,IAAI,CAACqW,YAAY,EAE3C3T,GACD,CAAA,IAAI,CAACkV,OAAO,CAAG1e,CAAG,EAEtB,IAAM2D,EAAS,IAAI,CAAC2C,UAAU,CAACC,OAAO,CAAC,CACnC,AAACO,CAAAA,EAAEW,EAAE,CAAGX,EAAEc,EAAE,AAAD,EAAK,EAChB,AAACd,CAAAA,EAAEe,EAAE,CAAGf,EAAEgB,EAAE,AAAD,EAAK,EACnB,EACD,IAAI,CAACqX,OAAO,CAACxb,EAAQ3D,EAAM0L,EAAQqT,EACvC,CACJ,CACAH,SAASQ,EAAS,CAAA,CAAI,CAAE,CACpB,IAAM3nB,EAAU2nB,EAAS,IAAI,CAAC3nB,OAAO,CAAG,CAAC,EAAG,EAAG,EAAG,EAAE,CACpD,MAAO,CACHH,EAAGG,CAAO,CAAC,EAAE,CACbY,EAAGZ,CAAO,CAAC,EAAE,CACbF,MAAO,IAAI,CAACwB,KAAK,CAACyK,SAAS,CAAG/L,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CACrDD,OAAQ,IAAI,CAACuB,KAAK,CAAC0K,UAAU,CAAGhM,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,AAC3D,CACJ,CACA4nB,UAAU5Y,CAAG,CAAE,CACX,GAAIoW,GAASpW,UACT,AAAIG,EAAI,CAACH,EAAI,EAAIG,AAAmB,aAAnBA,EAAI,CAACH,EAAI,CAACZ,IAAI,CACpB6I,GAAS9H,EAAI,CAACH,EAAI,EAEtBG,EAAI,CAACH,EAAI,CAEpB,GAAImW,GAASnW,EAAK,CAAA,GAAO,CACrB,GAAIA,AAAa,sBAAbA,EAAIZ,IAAI,CACR,OAAOY,EAEX,GAAIA,AAAa,aAAbA,EAAIZ,IAAI,CACR,OAAO6I,GAASjI,EAExB,CACJ,CACA6Y,YAAa,CACT,IAAM9V,EAAS,IAAI,CAACD,kBAAkB,GAAImE,EAAQ,IAAI,CAAC6R,QAAQ,GAC/D,GAAI/V,EAAQ,CACR,IAAM/R,EAAU,IAAI,CAACA,OAAO,CAAE8Z,EAAK,IAAI,CAACpG,sBAAsB,CAAC,CAC3D7T,EAAGkS,EAAO5B,EAAE,CACZvP,EAAGmR,EAAO3B,EAAE,AAChB,GAGA,MAAO,CACHtQ,MAJS,AAACiS,CAAAA,EAAO/B,EAAE,CAAG+B,EAAO5B,EAAE,AAAD,EAAK8F,EACnCjW,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAIvBD,OAJoC,AAACgS,CAAAA,EAAO3B,EAAE,CAAG2B,EAAO1B,EAAE,AAAD,EAAK4F,EAC9DjW,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAIvBH,EAAGia,EAAGja,CAAC,CAAGG,CAAO,CAAC,EAAE,CACpBY,EAAGkZ,EAAGlZ,CAAC,CAAGZ,CAAO,CAAC,EAAE,AACxB,CACJ,CACJ,CACA8R,oBAAqB,CACjB,IAAMjD,EAAa,IAAI,CAACA,UAAU,CAC5BkZ,EAAY,IAAI,CAACzmB,KAAK,CAAC8H,MAAM,CAAC0O,MAAM,CAAC,CAACsO,EAAKnX,KAC7C,IAAM8C,EAAS9C,EAAE6C,kBAAkB,EAAI7C,EAAE6C,kBAAkB,GAK3D,OAJIC,GACA9C,AAA6B,CAAA,IAA7BA,EAAEzN,OAAO,CAACwmB,cAAc,EACxB5B,EAAIpf,IAAI,CAAC+K,GAENqU,CACX,EAAG,EAAE,EAEChS,EAAgB,IAAI,CAAC5S,OAAO,CAAC4S,aAAa,CAChD,GAAIA,EAAe,CACf,GAAI,CAAC,IAAI,CAAC6T,kBAAkB,EACxB,GAAI7T,AAAuB,eAAvBA,EAAchG,IAAI,CAAmB,CACrC,IAAMzB,EAAYyH,EAAc2D,WAAW,CACtC/I,GAAG,CAAC,AAAC9L,GAAW2L,EAAWuD,OAAO,CAAClP,IAAUglB,EAAKvb,EAAUqC,GAAG,CAAC,AAACmZ,GAAQA,CAAG,CAAC,EAAE,EAAGC,EAAKzb,EAAUqC,GAAG,CAAC,AAACmZ,GAAQA,CAAG,CAAC,EAAE,CACzH,CAAA,IAAI,CAACF,kBAAkB,CAAG,CACtB9X,GAAI1N,KAAK2J,GAAG,CAAC3I,KAAK,CAAC,EAAGykB,GACtBlY,GAAIvN,KAAKgO,GAAG,CAAChN,KAAK,CAAC,EAAGykB,GACtB7X,GAAI5N,KAAK2J,GAAG,CAAC3I,KAAK,CAAC,EAAG2kB,GACtBhY,GAAI3N,KAAKgO,GAAG,CAAChN,KAAK,CAAC,EAAG2kB,EAC1B,CACJ,MAEI,IAAI,CAACH,kBAAkB,CAAGrD,GAAuB/V,EAAW5H,IAAI,CAACmN,IAGzE,OAAO,IAAI,CAAC6T,kBAAkB,AAClC,CACA,OAAO,IAAI,CAACpZ,UAAU,CAACkD,MAAM,EAAIiU,GAAQE,eAAe,CAAC6B,EAC7D,CACAD,UAAW,CAGP,OAAO,AA7VE,IAMC,WAuVsBrlB,KAAKwX,GAAG,CAAC,EAAG,IAAI,CAAC1R,IAAI,CACzD,CAEA8f,iBAAkB,CACd,GAAM,CAAExoB,EAAAA,CAAC,CAAEe,EAAAA,CAAC,CAAEd,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAG,IAAI,CAAC2lB,YAAY,CAAE4C,EAAkB,IAAI,CAACzZ,UAAU,CAACuD,OAAO,CAAC,IAAI,CAAClG,MAAM,EAAGqc,EAAa,IAAI,CAAC1Z,UAAU,CAACuC,cAAc,CAAG,GAAK,EAAGoX,EAAS,IAAI,CAACV,QAAQ,GAAIW,EAASD,EAASD,EAAYG,EAAa7oB,EAAIC,EAAQ,EAAIwoB,CAAe,CAAC,EAAE,CAAGE,EAAQzgB,EAAanH,EAAIb,EAAS,EAAIuoB,CAAe,CAAC,EAAE,CAAGG,EACtU,MAAO,CAAED,OAAAA,EAAQC,OAAAA,EAAQC,WAAAA,EAAY3gB,WAAAA,CAAW,CACpD,CAWA4gB,eAAezlB,CAAM,CAAE,CACnB,IAAMilB,EAAM,IAAI,CAAC5S,sBAAsB,CAACrS,GACxC,GAAIilB,EACA,OAAO,IAAI,CAACzU,sBAAsB,CAACyU,EAE3C,CAkBA5S,uBAAuBrS,CAAM,CAAE,CAC3B,IAAM5B,EAAQ,IAAI,CAACA,KAAK,CAAEsnB,EAAgBtnB,EAAMsnB,aAAa,CAE7D,GAAIA,EAAe,CACf,IAAK,IAAMhT,KAAagT,EACpB,GAAInqB,OAAOO,cAAc,CAACC,IAAI,CAAC2pB,EAAehT,IAC1CgT,CAAa,CAAChT,EAAU,CAACiT,OAAO,CAAE,CAClC,IAAMC,EAASxnB,EAAMyX,mBAAmB,CAAC7V,EAAQ0lB,CAAa,CAAChT,EAAU,EACzE,GAAIkT,GAAU1O,GAAe0O,EAAQF,CAAa,CAAChT,EAAU,CAACiT,OAAO,CAAC9Q,WAAW,CAAC,EAAE,EAChF,OAAO+Q,CAEf,CAEJ,OAAOxnB,EAAMyX,mBAAmB,CAAC7V,EAAQ0lB,EAAc,OAAU,CAErE,CAEA,IAAK,IAAMtV,KAAS,IAAI,CAACE,MAAM,CAC3B,GAAIF,EAAM9R,OAAO,CAACunB,SAAS,EACvB3O,GAAe,CAAEva,EAAGqD,EAAOsT,GAAG,CAAE5V,EAAGsC,EAAOuT,GAAG,AAAC,EAAGnD,EAAM9R,OAAO,CAACunB,SAAS,CAAChR,WAAW,CAAC,EAAE,EAAG,CAC1F,IAAMiR,EAAsB1V,EAAMzE,UAAU,CAACuD,OAAO,CAAC,CAAClP,EAAOsT,GAAG,CAAEtT,EAAOuT,GAAG,CAAC,EAAGwS,EAAU3V,EAAMI,sBAAsB,CAAC,CAAE7T,EAAGmpB,CAAmB,CAAC,EAAE,CAAEpoB,EAAGooB,CAAmB,CAAC,EAAE,AAAC,GAC9K,OAAO,IAAI,CAACnV,sBAAsB,CAACoV,EACvC,CAEJ,IAAM5f,EAAQ,IAAI,CAACwF,UAAU,CAACuD,OAAO,CAAC,CAAClP,EAAOsT,GAAG,CAAEtT,EAAOuT,GAAG,CAAC,EAC9D,GAAI,CAACpN,EAAMmT,OAAO,CACd,MAAO,CAAE3c,EAAGwJ,CAAK,CAAC,EAAE,CAAEzI,EAAGyI,CAAK,CAAC,EAAE,AAAC,CAE1C,CAoBAoM,uBAAuBpM,CAAK,CAAE,CAC1B,IAAM/H,EAAQ,IAAI,CAACA,KAAK,CAAEsnB,EAAgBtnB,EAAMsnB,aAAa,CAE7D,GAAIA,EAAe,CACf,IAAK,IAAMhT,KAAagT,EACpB,GAAInqB,OAAOO,cAAc,CAACC,IAAI,CAAC2pB,EAAehT,IAC1CgT,CAAa,CAAChT,EAAU,CAACiT,OAAO,EAChCzO,GAAe/Q,EAAOuf,CAAa,CAAChT,EAAU,CAACiT,OAAO,CAAC9Q,WAAW,CAAC,EAAE,EACrE,OAAOzW,EAAM4X,iBAAiB,CAAC7P,EAAOuf,CAAa,CAAChT,EAAU,EAGtE,OAAOtU,EAAM4X,iBAAiB,CAAC7P,EAAOuf,EAAc,OAAU,CAElE,CACA,IAAMK,EAAU,IAAI,CAACvV,sBAAsB,CAACrK,GAC5C,IAAK,IAAMiK,KAAS,IAAI,CAACE,MAAM,CAC3B,GAAIF,EAAMuV,OAAO,EACbzO,GAAe6O,EAAS3V,EAAMuV,OAAO,CAAC9Q,WAAW,CAAC,EAAE,EAAG,CACvD,IAAMiR,EAAsB1V,EACvBO,sBAAsB,CAACoV,GAAUlR,EAAczE,EAAMzE,UAAU,CAACC,OAAO,CAAC,CAACka,EAAoBnpB,CAAC,CAAEmpB,EAAoBpoB,CAAC,CAAC,EAC3H,MAAO,CAAE4V,IAAKuB,CAAW,CAAC,EAAE,CAAEtB,IAAKsB,CAAW,CAAC,EAAE,AAAC,CACtD,CAEJ,IAAMA,EAAc,IAAI,CAAClJ,UAAU,CAACC,OAAO,CAAC,CAACzF,EAAMxJ,CAAC,CAAEwJ,EAAMzI,CAAC,CAAC,EAC9D,MAAO,CAAE4V,IAAKuB,CAAW,CAAC,EAAE,CAAEtB,IAAKsB,CAAW,CAAC,EAAE,AAAC,CACtD,CAsBAhJ,iBAAiBzN,CAAK,CAAE4nB,CAAY,CAAExjB,EAAS,CAAA,CAAK,CAAE,CAElD,IAAI,CAACkhB,kBAAkB,CAAG,CAAC,EAE3B,IAAMuC,EAAUD,EAAala,GAAG,CAAC,AAACE,GAAY,IAAI,CAAC0Y,SAAS,CAAC1Y,IACvDka,EAAe,EAAE,CACvBD,EAAQ5Y,OAAO,CAAC,AAAC8Y,IACb,GAAIA,IAEK5qB,OAAO2Y,IAAI,CAAC,IAAI,CAACwP,kBAAkB,EAAE3gB,MAAM,EAC5C,CAAA,IAAI,CAAC2gB,kBAAkB,CACnByC,CAAM,CAAC,yBAAyB,EAAI,CAAC,CAAA,EAGzCA,EAAO3Q,IAAI,EAAE,CACb,GAAM,CAACvI,EAAIE,EAAIL,EAAII,EAAG,CAAGiZ,EAAO3Q,IAAI,CACpC0Q,EAAapiB,IAAI,CAAC,CAAEmJ,GAAAA,EAAIE,GAAAA,EAAIL,GAAAA,EAAII,GAAAA,CAAG,EACvC,CAER,GAEA,IAAM2Y,EAAaK,EAAanjB,MAAM,EAClC+f,GAAQE,eAAe,CAACkD,GAG5BpE,GAAkB,IAAI,CAAE,qBAAsB,CAC1C+D,UAAAA,EACAznB,MAAAA,CACJ,EAAG,WACC,GAAIynB,GACA,IAAI,CAACnC,kBAAkB,CAAE,CACzB,GAAI,CAAC,IAAI,CAACA,kBAAkB,CAAC/X,UAAU,CAAE,CACrC,GAAM,CAAEsB,GAAAA,CAAE,CAAEE,GAAAA,CAAE,CAAEL,GAAAA,CAAE,CAAEI,GAAAA,CAAE,CAAE,CAAG2Y,CAC3B,CAAA,IAAI,CAACnC,kBAAkB,CAAC/X,UAAU,CAC9B,AAACmB,EAAKG,EAAK,KAAOC,EAAKC,EAAK,GAExB,CACIuB,KAAM,aACN0C,UAAW,CAAC,EAAG,EAAE,CACjBC,SAAU,CAAC,EAAE,AACjB,EAGA,CACI3C,KAAM,wBACN0C,UAAW,CAACjE,EAAID,EAAG,CACnBmE,SAAU,CAAC,CAAEpE,CAAAA,EAAKH,CAAC,EAAK,EAAE,AAC9B,CACZ,CACK,IAAI,CAAC4W,kBAAkB,CAACpT,MAAM,EAC/B,CAAA,IAAI,CAACoT,kBAAkB,CAACpT,MAAM,CAAG,KAAK,CAAA,CAE9C,CACJ,GAEA,IAAI,CAAC6V,MAAM,CAAGF,CAAO,CAAC,EAAE,CACpBzjB,GACApE,EAAM8lB,WAAW,EACjB,CAAC9lB,EAAMyM,WAAW,CAAClL,OAAO,EAAEgM,YAC5B,IAAI,CAAC+X,kBAAkB,EACvB,IAAI,CAAClhB,MAAM,CAAC,IAAI,CAACkhB,kBAAkB,CAE3C,CACA3S,OAAOqT,CAAS,CAAE,CACd,IAAI,CAAChmB,KAAK,CAAC8H,MAAM,CAACmH,OAAO,CAAC,AAACtB,IACnBA,EAAEqa,cAAc,EAChBra,CAAAA,EAAE+E,OAAO,CAAG,CAAA,CAAG,CAEvB,GACA,IAAI,CAAC1S,KAAK,CAAC2S,MAAM,CAACqT,EACtB,CAgBAI,QAAQxb,CAAM,CAAE3D,CAAI,CAAE0L,EAAS,CAAA,CAAI,CAAEqT,CAAS,CAAE,CACxCpb,GACA,CAAA,IAAI,CAACA,MAAM,CAAGA,CAAK,EAEH,UAAhB,OAAO3D,IACqB,UAAxB,OAAO,IAAI,CAAC0e,OAAO,EACnB1e,CAAAA,EAAO9F,KAAKgO,GAAG,CAAClI,EAAM,IAAI,CAAC0e,OAAO,CAAA,EAEF,UAAhC,OAAO,IAAI,CAACzlB,OAAO,CAAC6S,OAAO,EAC3B9L,CAAAA,EAAO9F,KAAK2J,GAAG,CAAC7D,EAAM,IAAI,CAAC/G,OAAO,CAAC6S,OAAO,CAAA,EAG1C6Q,GAAiB3c,IACjB,CAAA,IAAI,CAACA,IAAI,CAAGA,CAAG,GAGvB,IAAMwJ,EAAS,IAAI,CAACD,kBAAkB,GACtC,GAAIC,EAAQ,CACR,IAAMuW,EAAkB,IAAI,CAACzZ,UAAU,CAACuD,OAAO,CAAC,IAAI,CAAClG,MAAM,EAAG,CAAErM,EAAAA,CAAC,CAAEe,EAAAA,CAAC,CAAEd,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAG,IAAI,CAAC2lB,YAAY,CAAEzP,EAAQ,IAAI,CAAC6R,QAAQ,GAAIyB,EAAa,IAAI,CAAC7V,sBAAsB,CAAC,CACzK7T,EAAGkS,EAAO5B,EAAE,CACZvP,EAAGmR,EAAO1B,EAAE,AAChB,GAAImZ,EAAW,IAAI,CAAC9V,sBAAsB,CAAC,CACvC7T,EAAGkS,EAAO/B,EAAE,CACZpP,EAAGmR,EAAO3B,EAAE,AAChB,GAAIqZ,EAAwB,CACxB,AAAC1X,CAAAA,EAAO5B,EAAE,CAAG4B,EAAO/B,EAAE,AAAD,EAAK,EAC1B,AAAC+B,CAAAA,EAAO1B,EAAE,CAAG0B,EAAO3B,EAAE,AAAD,EAAK,EAC7B,CACD,GAAI,CADY,IAAI,CAAC9O,KAAK,CAAC8H,MAAM,CAACsgB,IAAI,CAAC,AAACtgB,GAAWA,EAAOugB,UAAU,EACnD,CAGb,IAAMxZ,EAAKoZ,EAAW1pB,CAAC,CAAEwQ,EAAKmZ,EAAS5oB,CAAC,CAAEoP,EAAKwZ,EAAS3pB,CAAC,CAAEuQ,EAAKmZ,EAAW3oB,CAAC,AAExEoP,CAAAA,EAAKG,EAAKrQ,EACVwoB,CAAe,CAAC,EAAE,CAAGmB,CAAqB,CAAC,EAAE,CAGxCtZ,EAAKtQ,GAAKmQ,EAAKnQ,EAAIC,EAExBwoB,CAAe,CAAC,EAAE,EACd7lB,KAAKgO,GAAG,CAACN,EAAKtQ,EAAGmQ,EAAKlQ,EAAQD,GAAKoW,EAGlCjG,EAAKnQ,EAAIC,GAASqQ,EAAKtQ,GAE5ByoB,CAAAA,CAAe,CAAC,EAAE,EACd7lB,KAAK2J,GAAG,CAAC4D,EAAKlQ,EAAQD,EAAGsQ,EAAKtQ,GAAKoW,CAAI,EAG3C7F,EAAKC,EAAKtQ,EACVuoB,CAAe,CAAC,EAAE,CAAGmB,CAAqB,CAAC,EAAE,CAGxCpZ,EAAKzP,GAAKwP,EAAKxP,EAAIb,EAExBuoB,CAAe,CAAC,EAAE,EACd7lB,KAAKgO,GAAG,CAACJ,EAAKzP,EAAGwP,EAAKrQ,EAASa,GAAKqV,EAGnC7F,EAAKxP,EAAIb,GAAUsQ,EAAKzP,GAE7B0nB,CAAAA,CAAe,CAAC,EAAE,EACd7lB,KAAK2J,GAAG,CAACgE,EAAKrQ,EAASa,EAAGyP,EAAKzP,GAAKqV,CAAI,EAEhD,IAAI,CAAC/J,MAAM,CAAG,IAAI,CAAC2C,UAAU,CAACC,OAAO,CAACwZ,EAC1C,CACA,IAAI,CAAC9U,MAAM,CAACjD,OAAO,CAAC,AAAC+C,IACbA,EAAM9R,OAAO,CAACooB,KAAK,GACnBtW,EAAMuV,OAAO,CAAGvV,EAAMuW,UAAU,GAChCvW,EAAMoS,YAAY,CAAGpS,EAAM6T,QAAQ,GAE3C,GACA,IAAI,CAAC2C,MAAM,EACf,CACA9E,GAAkB,IAAI,CAAE,gBACpB/Q,GACA,IAAI,CAACA,MAAM,CAACqT,EAEpB,CASA5T,uBAAuByU,CAAG,CAAE,CACxB,IAAMlS,EAAQ,IAAI,CAAC6R,QAAQ,GAAIQ,EAAkB,IAAI,CAACzZ,UAAU,CAACuD,OAAO,CAAC,IAAI,CAAClG,MAAM,EAAG0d,EAAQ,IAAI,CAAClE,YAAY,CAAEqE,EAAYH,EAAM/pB,CAAC,CAAG+pB,EAAM9pB,KAAK,CAAG,EAAGkqB,EAAYJ,EAAMhpB,CAAC,CAAGgpB,EAAM7pB,MAAM,CAAG,EAG9L,MAAO,CAAEF,EAFCkqB,EAAY9T,EAASqS,CAAAA,CAAe,CAAC,EAAE,CAAGH,EAAItoB,CAAC,AAADA,EAE5Ce,EADFopB,EAAY/T,EAASqS,CAAAA,CAAe,CAAC,EAAE,CAAGH,EAAIvnB,CAAC,AAADA,CAC1C,CAClB,CAWAuC,eAAeglB,CAAG,CAAE,CAChB,OAAO,IAAI,CAAC1S,sBAAsB,CAAC,IAAI,CAAC5B,sBAAsB,CAACsU,GACnE,CASAtU,uBAAuBsU,CAAG,CAAE,CACxB,GAAM,CAAEtoB,EAAAA,CAAC,CAAEe,EAAAA,CAAC,CAAE,CAAGunB,EAAKlS,EAAQ,IAAI,CAAC6R,QAAQ,GAAIQ,EAAkB,IAAI,CAACzZ,UAAU,CAACuD,OAAO,CAAC,IAAI,CAAClG,MAAM,EAAG0d,EAAQ,IAAI,CAAClE,YAAY,CAAEqE,EAAYH,EAAM/pB,CAAC,CAAG+pB,EAAM9pB,KAAK,CAAG,EAAGkqB,EAAYJ,EAAMhpB,CAAC,CAAGgpB,EAAM7pB,MAAM,CAAG,EAG9M,MAAO,CAAEF,EAFUyoB,CAAe,CAAC,EAAE,CAAG,AAACzoB,CAAAA,EAAIkqB,CAAQ,EAAK9T,EAElCrV,EADL0nB,CAAe,CAAC,EAAE,CAAG,AAAC1nB,CAAAA,EAAIopB,CAAQ,EAAK/T,CACpB,CAC1C,CACAoR,aAAc,CACV,IAGI4C,EAA0BC,EAAcC,EAHtC,CAAE7oB,MAAAA,CAAK,CAAE,CAAG,IAAI,CAIhB8oB,EAAQ,AAAC/oB,IACX,GAAM,CAAEgpB,YAAAA,CAAW,CAAEC,UAAAA,CAAS,CAAE,CAAGhpB,EAAMI,OAAO,CAAEmN,EAAa,IAAI,CAACA,UAAU,CAAE0b,EAAUlpB,EAAEkpB,OAAO,CAC/F,CAAEC,WAAAA,CAAU,CAAEC,WAAAA,CAAU,CAAE,CAAGnpB,EAAOmN,EAAU,EAgBlD,GAfI6b,GAAWrkB,SAAW,GACtBukB,EAAaF,CAAS,CAAC,EAAE,CAACtoB,MAAM,CAChCyoB,EAAaH,CAAS,CAAC,EAAE,CAACpoB,MAAM,EAE3BooB,GAAWrkB,SAAW,IAC3BukB,EAAa,AAACF,CAAAA,CAAS,CAAC,EAAE,CAACtoB,MAAM,CAAGsoB,CAAS,CAAC,EAAE,CAACtoB,MAAM,AAAD,EAAK,EAC3DyoB,EAAa,AAACH,CAAAA,CAAS,CAAC,EAAE,CAACpoB,MAAM,CAAGooB,CAAS,CAAC,EAAE,CAACpoB,MAAM,AAAD,EAAK,GAG3DqoB,GAAStkB,SAAW,GAAKokB,GAIzB5b,CAAAA,EAAUhM,KAAKmM,GAAG,CAAC8b,AAHGjoB,KAAKuX,IAAI,CAACvX,KAAKwX,GAAG,CAACoQ,CAAW,CAAC,EAAE,CAACroB,MAAM,CAAGqoB,CAAW,CAAC,EAAE,CAACroB,MAAM,CAAE,GACpFS,KAAKwX,GAAG,CAACoQ,CAAW,CAAC,EAAE,CAACnoB,MAAM,CAAGmoB,CAAW,CAAC,EAAE,CAACnoB,MAAM,CAAE,IAAmBO,KAAKuX,IAAI,CAACvX,KAAKwX,GAAG,CAACsQ,CAAO,CAAC,EAAE,CAACvoB,MAAM,CAAGuoB,CAAO,CAAC,EAAE,CAACvoB,MAAM,CAAE,GACrIS,KAAKwX,GAAG,CAACsQ,CAAO,CAAC,EAAE,CAACroB,MAAM,CAAGqoB,CAAO,CAAC,EAAE,CAACroB,MAAM,CAAE,KACFO,KAAKmM,GAAG,CAAC,GAAG,EAE9DsW,GAAiBsF,IAAetF,GAAiBuF,GAAa,CAC9D,IAAMlsB,EAAM,CAAC,EAAEisB,EAAW,CAAC,EAAEC,EAAW,CAAC,CACrC,CAAEzoB,OAAAA,CAAM,CAAEE,OAAAA,CAAM,CAAE,CAAGb,EAAEspB,aAAa,CACpCJ,GAAStkB,SAAW,IACpBjE,EAAS,AAACuoB,CAAAA,CAAO,CAAC,EAAE,CAACvoB,MAAM,CAAGuoB,CAAO,CAAC,EAAE,CAACvoB,MAAM,AAAD,EAAK,EACnDE,EAAS,AAACqoB,CAAAA,CAAO,CAAC,EAAE,CAACroB,MAAM,CAAGqoB,CAAO,CAAC,EAAE,CAACroB,MAAM,AAAD,EAAK,GAGnD3D,IAAQ2rB,IACRA,EAAe3rB,EACf0rB,EAA2B,IAAI,CAACpb,UAAU,CACrCuD,OAAO,CAAC,IAAI,CAAClG,MAAM,EACxBie,EAAoB,AAAC,CAAA,IAAI,CAACtb,UAAU,CAACrN,OAAO,CAAC+S,QAAQ,EAAI,CAAC,EAAG,EAAE,AAAD,EAAG7Q,KAAK,IAI1E,IAAMknB,EAAc/b,EAAW8S,GAAG,EAAI9S,EAAW8S,GAAG,CAAC5P,MAAM,CAAE8Y,EAAY,AAACD,GACtEnF,GAAemF,EAAa,IAAI,CAAClF,YAAY,GAAM,CAACrb,IAExD,GAAIwE,AAA4B,iBAA5BA,EAAWrN,OAAO,CAACoQ,IAAI,EACvB,AAAyB,EAAxB2Y,CAAAA,GAAStkB,QAAU,CAAA,GAGpB,AAAC,CAAA,IAAI,CAACghB,OAAO,EAAI5c,GAAO,EAAKwgB,AAAY,IAAZA,EAAiB,CAI9C,IAAMC,EAAQ,IAAO,CAAA,IAAI,CAAChD,QAAQ,GAAKrlB,KAAK2J,GAAG,CAAC9K,EAAMyK,SAAS,CAAEzK,EAAM0K,UAAU,CAAA,EACjF,GAAIme,EAAmB,CACnB,IAAM3T,EAAM,AAACgU,CAAAA,EAAaxoB,CAAK,EAAK8oB,EAChCX,CAAiB,CAAC,EAAE,CAAE1T,EAAMqO,GAAc,CAACqF,CAAiB,CAAC,EAAE,CAC/D,AAACM,CAAAA,EAAavoB,CAAK,EAAK4oB,EAAO,IAAK,IAAKviB,EAAO,IAAI,CAACA,IAAI,CAC7D,IAAI,CAAC7C,MAAM,CAAC,CACRmJ,WAAY,CACR0F,SAAU,CAAC,CAACiC,EAAK,CAACC,EAAI,AAC1B,CACJ,EAAG,CAAA,GACH,IAAI,CAAC1C,WAAW,CAAC,KAAK,EAAG,KAAK,EAAG,CAAA,GACjC,IAAI,CAACxL,IAAI,CAAGA,EACZjH,EAAM2S,MAAM,CAAC,CAAA,EACjB,CAEJ,MACK,GAAIiR,GAAiBljB,IAAWkjB,GAAiBhjB,GAAS,CAE3D,IAAM+T,EAAQ,IAAI,CAAC6R,QAAQ,GAAIS,EAAa,IAAI,CAAC1Z,UAAU,CAACuC,cAAc,CAAG,EAAI,GAC3E2Z,EAAY,IAAI,CAAClc,UAAU,CAACC,OAAO,CAAC,CACtCmb,CAAwB,CAAC,EAAE,CACvB,AAACO,CAAAA,EAAaxoB,CAAK,EAAKiU,EAC5BgU,CAAwB,CAAC,EAAE,CACvB,AAACQ,CAAAA,EAAavoB,CAAK,EAAK+T,EAAQsS,EACvC,EAEIje,MAAMygB,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,GAClC,IAAI,CAACjoB,MAAM,CAAC2L,EAASsc,EAAW,KAAK,EAAG,CAAA,EAEhD,CACA1pB,EAAE+D,cAAc,EACpB,CACJ,EACAyf,GAAiBvjB,EAAO,MAAO8oB,GAC/BvF,GAAiBvjB,EAAO,WAAY8oB,GAEpCvF,GAAiBvjB,EAAO,YAAa,AAAC0pB,IAElC,GAAKA,EAAIC,cAAc,CAmBnB,IAAI,CAACnoB,MAAM,OAnBU,CACrB,IAAMjD,EAAImrB,EAAInrB,CAAC,CAAGyB,EAAMW,QAAQ,CAC1BrB,EAAIoqB,EAAIpqB,CAAC,CAAGU,EAAMa,OAAO,CACzB,CAAEvB,EAAGyP,CAAE,CAAExQ,EAAGsQ,CAAE,CAAE,CAAG,IAAI,CAAC0D,sBAAsB,CAAC,CAAEhU,EAAAA,EAAGe,EAAAA,CAAE,GACtD,CAAEA,EAAGwP,CAAE,CAAEvQ,EAAGmQ,CAAE,CAAE,CAAG,IAAI,CAAC6D,sBAAsB,CAAC,CAAEhU,EAAGA,EAAImrB,EAAIlrB,KAAK,CAAEc,EAAGA,EAAIoqB,EAAIjrB,MAAM,AAAC,GAC3F,IAAI,CAACgU,WAAW,CAAC,CAAE5D,GAAAA,EAAIE,GAAAA,EAAIL,GAAAA,EAAII,GAAAA,CAAG,EAAG,KAAK,EAAG,CAAA,EAAM4a,CAAAA,EAAIL,aAAa,CAACJ,OAAO,EAKxE,KAAK,GAEJ,SAAS1d,IAAI,CAAEme,EAAIL,aAAa,CAACvc,IAAI,GACtC9M,EAAM4pB,aAAa,GAEvBF,EAAI5lB,cAAc,EAEtB,CAIJ,EACJ,CACA0kB,QAAS,CAEA,IAAI,CAACqB,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,IAAI,CAAC7pB,KAAK,CAAC8E,QAAQ,CAACI,CAAC,CAAC,YAC9BC,IAAI,CAAC,CAAEC,OAAQ,CAAE,GACjBC,GAAG,EAAC,CAEjB,CAaAjB,OAAOlE,CAAO,CAAEyS,EAAS,CAAA,CAAI,CAAEqT,CAAS,CAAE,CACtC,IAAM8D,EAAgB5pB,EAAQqN,UAAU,CAAEwc,EAAoBD,GAAmBE,AAp1BnC7L,GAo1BmD4B,QAAQ,CAAC+J,KACtGE,AAr1B0C7L,GAq1B1B4B,QAAQ,CAAC,IAAI,CAAC7f,OAAO,CAACqN,UAAU,EAChD0c,EAAgB,CAAA,EACpBlG,GAAc,CAAA,EAAM,IAAI,CAACtX,WAAW,CAAEvM,GACtC6jB,GAAc,CAAA,EAAM,IAAI,CAAC7jB,OAAO,CAAEA,GAG9B,WAAYA,IACZ,IAAI,CAACgS,MAAM,CAACjD,OAAO,CAAC,AAAC+C,GAAUA,EAAMnN,OAAO,IAC5C,IAAI,CAACqN,MAAM,CAACvN,MAAM,CAAG,EACrBslB,EAAgB,CAAA,GAEhBF,CAAAA,GAAqB,kBAAmB7pB,CAAM,GAC9C,OAAO,IAAI,CAACymB,kBAAkB,CAE9BoD,CAAAA,GAAqBE,CAAY,IACjC,IAAI,CAACjqB,KAAK,CAAC8H,MAAM,CAACmH,OAAO,CAAC,AAACnH,IACvB,IAAMoiB,EAASpiB,EAAOqiB,eAAe,CAOrC,GANIriB,EAAOsiB,WAAW,EAClBtiB,EAAOsiB,WAAW,GAEtBtiB,EAAO4K,OAAO,CAAG,CAAA,EACjB5K,EAAOuiB,WAAW,CAAG,CAAA,EAEjBJ,GAAiBC,EACjB,KAAOA,EAAOvlB,MAAM,CAAG,GAAG,CACtB,IAAMklB,EAAQK,EAAOtlB,GAAG,GACpBilB,GACAA,EAAMhlB,OAAO,EAErB,CAER,GACIklB,GACA,CAAA,IAAI,CAACxc,UAAU,CAAG,IAt3BoB4Q,GAs3BA,IAAI,CAACje,OAAO,CAACqN,UAAU,CAAA,EAG7D0c,GACA,IAAI,CAACrE,YAAY,GAGjB,CAAC1lB,EAAQ0K,MAAM,EAEfzN,OAAOO,cAAc,CAACC,IAAI,CAACuC,EAAS,SACpC,CAAC0jB,GAAiB1jB,EAAQ+G,IAAI,GAC9B,IAAI,CAACwL,WAAW,CAAC,KAAK,EAAG,KAAK,EAAG,CAAA,IAGrCvS,EAAQ0K,MAAM,EAAIgZ,GAAiB1jB,EAAQ+G,IAAI,EAC/C,IAAI,CAACmf,OAAO,CAAC,IAAI,CAAClmB,OAAO,CAAC0K,MAAM,CAAE1K,EAAQ+G,IAAI,CAAE,CAAA,GAE3C,kBAAmB/G,GACxB,IAAI,CAACuS,WAAW,CAAC,KAAK,EAAG,KAAK,EAAG,CAAA,GAEjCE,GACA,IAAI,CAAC3S,KAAK,CAAC2S,MAAM,CAACqT,EAE1B,CAiBAxkB,OAAO2L,CAAO,CAAEqa,CAAM,CAAE8C,CAAW,CAAEtE,CAAS,CAAE,CAC5C,IAAMhmB,EAAQ,IAAI,CAACA,KAAK,CAAEgnB,EAAkB,IAAI,CAACzZ,UAAU,CAACuD,OAAO,CAAC,IAAI,CAAClG,MAAM,EAC/E,GAAI,AAAmB,UAAnB,OAAOuC,EAAsB,CAC7B,IACIvC,EAAQrM,EAAGe,EADT2H,EAAO,IAAI,CAACA,IAAI,CAAGkG,EAGzB,GAAImd,EAAa,CACb,GAAM,CAAC5pB,EAAQE,EAAO,CAAG0pB,EACnB3V,EAAQ,IAAI,CAAC6R,QAAQ,GACrB+D,EAAU7pB,EAASV,EAAMW,QAAQ,CAAGX,EAAMyK,SAAS,CAAG,EACtD+f,EAAU5pB,EAASZ,EAAMa,OAAO,CAAGb,EAAM0K,UAAU,CAAG,EAC5DnM,EAAIyoB,CAAe,CAAC,EAAE,CAAGuD,EAAU5V,EACnCrV,EAAI0nB,CAAe,CAAC,EAAE,CAAGwD,EAAU7V,CACvC,CAEA,GAAI,AAAa,UAAb,OAAOpW,GAAkB,AAAa,UAAb,OAAOe,EAAgB,CAChD,IAAMqV,EAAQ,EAAIxT,KAAKwX,GAAG,CAAC,EAAG,IAAI,CAAC1R,IAAI,EAAI9F,KAAKwX,GAAG,CAAC,EAAG1R,GACjDsjB,EAAUvD,CAAe,CAAC,EAAE,CAAGzoB,EAC/BisB,EAAUxD,CAAe,CAAC,EAAE,CAAG1nB,CACrC0nB,CAAAA,CAAe,CAAC,EAAE,EAAIuD,EAAU5V,EAChCqS,CAAe,CAAC,EAAE,EAAIwD,EAAU7V,EAChC/J,EAAS,IAAI,CAAC2C,UAAU,CAACC,OAAO,CAACwZ,EACrC,CACA,IAAI,CAACZ,OAAO,CAACoB,GAAU5c,EAAQ3D,EAAM,KAAK,EAAG+e,EAEjD,MAEI,IAAI,CAACvT,WAAW,CAAC,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGuT,EAEjD,CACJ,CAEA,MAAMT,WAAqBb,GAMvBrgB,YAAY9C,CAAO,CAAErB,CAAO,CAAE,CAM1B,GALA,KAAK,CAACqB,EAAQvB,KAAK,CAAEE,GACrB,IAAI,CAACqI,EAAE,CAAGrI,EAAQqI,EAAE,CACpB,IAAI,CAAChH,OAAO,CAAGA,EACf,IAAI,CAACrB,OAAO,CAAG6jB,GAAc,CAAEnZ,OAAQ,CAAC,EAAG,EAAE,AAAC,EAAGrJ,EAAQrB,OAAO,CAACgT,YAAY,CAAEhT,GAC/E,IAAI,CAACumB,SAAS,CAAG,EAAE,CACf,IAAI,CAACvmB,OAAO,CAACunB,SAAS,CAAE,CAGxB,IAAM9hB,EAAOpE,EAAQgM,UAAU,CAAC5H,IAAI,CAAC,IAAI,CAACzF,OAAO,CAACunB,SAAS,CAC3D,CAAA,IAAI,CAACgD,qBAAqB,CAAGnH,GAAuB3d,GACpD,IAAI,CAAC+kB,yBAAyB,CAAG/kB,EAAK+H,GAAG,CAAC,AAACid,GAAY,CACnDA,CAAO,CAAC,EAAE,EAAI,EACdA,CAAO,CAAC,EAAE,EAAI,EACjB,CACL,CACJ,CAUA9E,SAASQ,EAAS,CAAA,CAAI,CAAE,CACpB,IAAMkB,EAAU,IAAI,CAACA,OAAO,CAC5B,GAAIA,EAAS,CACT,IAAM7oB,EAAU2nB,EAAS,IAAI,CAAC3nB,OAAO,CAAG,CAAC,EAAG,EAAG,EAAG,EAAE,CAAEqa,EAAUwO,EAAQ9Q,WAAW,CAAC,EAAE,CAAEmQ,EAAK7N,EAAQrL,GAAG,CAAC,AAACuN,GAAOA,CAAE,CAAC,EAAE,EAAG6L,EAAK/N,EAAQrL,GAAG,CAAC,AAACuN,GAAOA,CAAE,CAAC,EAAE,EAAG1c,EAAI4C,KAAK2J,GAAG,CAAC3I,KAAK,CAAC,EAAGykB,GAAMloB,CAAO,CAAC,EAAE,CAAEgQ,EAAKvN,KAAKgO,GAAG,CAAChN,KAAK,CAAC,EAAGykB,GAAMloB,CAAO,CAAC,EAAE,CAAEY,EAAI6B,KAAK2J,GAAG,CAAC3I,KAAK,CAAC,EAAG2kB,GAAMpoB,CAAO,CAAC,EAAE,CAAEoQ,EAAK3N,KAAKgO,GAAG,CAAChN,KAAK,CAAC,EAAG2kB,GAAMpoB,CAAO,CAAC,EAAE,CAC1T,GAAIklB,GAAiBrlB,IAAMqlB,GAAiBtkB,GACxC,MAAO,CACHf,EAAAA,EACAe,EAAAA,EACAd,MAAOkQ,EAAKnQ,EACZE,OAAQqQ,EAAKxP,CACjB,CAER,CAEA,OAAO,KAAK,CAACumB,SAASloB,IAAI,CAAC,IAAI,CAAE0oB,EACrC,CAKAkC,YAAa,CACT,GAAM,CAAEvoB,MAAAA,CAAK,CAAEuB,QAAAA,CAAO,CAAErB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAE,CAAEuW,YAAAA,CAAW,CAAE,CAAGvW,EAAQooB,KAAK,EAAI,CAAC,EAC9E,GAAI7R,EAAa,CACb,IAAIsC,EAAUtC,CAAW,CAAC,EAAE,CAC5B,GAAIvW,AAAkB,YAAlBA,EAAQoT,KAAK,CAAgB,CAC7B,IAAMD,EAAanT,AAAuB,mBAAvBA,EAAQmT,UAAU,EACjC9R,EAAQglB,UAAU,IAClBxC,GAAc/jB,EAAM0lB,OAAO,CAAE,CAAEnnB,EAAG,EAAGe,EAAG,CAAE,GAC9CyZ,EAAUA,EAAQrL,GAAG,CAAC,AAACuN,GAAO,CAC1BiJ,GAAuB,CAAC,EAAEjJ,CAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE5H,EAAW7U,KAAK,CAAE6U,EAAW9U,CAAC,EAClE2lB,GAAuB,CAAC,EAAEjJ,CAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE5H,EAAW5U,MAAM,CAAE4U,EAAW/T,CAAC,EACtE,CACL,CACA,MAAO,CACHwN,KAAM,UACN2J,YAAa,CAACsC,EAAQ,AAC1B,CACJ,CACJ,CACAvI,oBAAqB,CACjB,OAAOkU,GAAQE,eAAe,CAAC,IAAI,CAAC6B,SAAS,CACjD,CAMA5M,SAAS9R,CAAK,CAAE,CACZ,GAAM,CAAE0iB,sBAAAA,CAAqB,CAAEC,0BAAAA,CAAyB,CAAE,CAAG,IAAI,CACjE,MAAOE,CAAAA,CAIPH,CAAAA,GACI1iB,EAAMxJ,CAAC,EAAIksB,EAAsB5b,EAAE,EACnC9G,EAAMxJ,CAAC,EAAIksB,EAAsB/b,EAAE,EACnC3G,EAAMzI,CAAC,EAAImrB,EAAsB1b,EAAE,EACnChH,EAAMzI,CAAC,EAAImrB,EAAsB3b,EAAE,EAGnC4b,GACA5R,GAAe/Q,EAAO2iB,EAAyB,CACvD,CAKAlC,QAAS,CACL,GAAM,CAAExoB,MAAAA,CAAK,CAAEuB,QAAAA,CAAO,CAAErB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAE2qB,EAAa3qB,EAAQ2qB,UAAU,EAAI3qB,EAAQooB,KAAK,CAC1F,GAAIuC,GAActpB,EAAQsoB,KAAK,CAAE,CAC7B,IAAIiB,EAAU,CAAA,CACT,CAAA,IAAI,CAACC,MAAM,GACZ,IAAI,CAACA,MAAM,CAAG/qB,EAAM8E,QAAQ,CACvBa,IAAI,GACJH,QAAQ,CAAC,mCACTH,GAAG,CAAC9D,EAAQsoB,KAAK,EACtBiB,EAAU,CAAA,GAET9qB,EAAMsF,UAAU,EACjB,IAAI,CAACylB,MAAM,CAAC5lB,IAAI,CAAC,CACblG,OAAQiB,EAAQiT,WAAW,CAC3B,eAAgBjT,EAAQkT,WAAW,AACvC,GAEJ,IAAM4X,EAAc,IAAI,CAACD,MAAM,CAACC,WAAW,GAAI1C,EAAQ,AAAwB,mBAAvBpoB,EAAQmT,UAAU,EACtE9R,EAAQglB,UAAU,IAAOhlB,EAAQ6iB,YAAY,CAC3CvnB,EAAI,AAACguB,CAAAA,EAAWpU,WAAW,EAAI,EAAE,AAAD,EAAGD,MAAM,CAAC,CAAC3Z,EAAGwiB,IAAeA,EAAW7I,MAAM,CAAC,CAAC3Z,EAAGkL,EAAOoD,KAC5F,GAAI,CAAC5M,EAAGe,EAAE,CAAGyI,EAQb,MAPsB,YAAlB7H,EAAQoT,KAAK,GACb/U,EAAIyB,EAAMW,QAAQ,CAAGujB,GAAuB,CAAC,EAAE3lB,EAAE,CAAC,CAAC,CAAE+pB,EAAM9pB,KAAK,CAAE8pB,EAAM/pB,CAAC,EACzEe,EAAIU,EAAMa,OAAO,CAAGqjB,GAAuB,CAAC,EAAE5kB,EAAE,CAAC,CAAC,CAAEgpB,EAAM7pB,MAAM,CAAE6pB,EAAMhpB,CAAC,GAE7Ef,EAAIklB,GAAMllB,EAAGysB,GACb1rB,EAAImkB,GAAMnkB,EAAG0rB,GACbnuB,EAAE6I,IAAI,CAACyF,AAAM,IAANA,EAAU,CAAC,IAAK5M,EAAGe,EAAE,CAAG,CAAC,IAAKf,EAAGe,EAAE,EACnCzC,CACX,EAAGA,GAAI,EAAE,EAET,IAAI,CAACkuB,MAAM,CAACD,EAAU,UAAY,OAAO,CAAC,CAAEjuB,EAAAA,CAAE,EAClD,CACJ,CACAgI,SAAU,CACF,IAAI,CAACkmB,MAAM,EACX,CAAA,IAAI,CAACA,MAAM,CAAG,IAAI,CAACA,MAAM,CAAClmB,OAAO,EAAC,EAEtC,IAAI,CAACwgB,cAAc,CAACpW,OAAO,CAAC,AAACgc,GAAMA,IACvC,CAKAlF,aAAc,CAAE,CACpB,CAoBA,GAAM,CAAEmF,WAAAA,EAAU,CAAEC,KAAAA,EAAI,CAAE,CAAIttB,IAIxB,CAAEutB,KAAAA,EAAI,CAAE,CAAIvtB,IAEZ,CAAEoQ,UAAAA,EAAS,CAAE,CAAGM,GAKhB,CAEN/G,OAAQ6jB,EAAY,CAAE/b,QAASgc,EAAa,CAAE,CAAG,AAAClkB,IAA6DM,WAAW,CAEpH,CAAEjI,OAAQ8rB,EAAgB,CAAEC,KAAAA,EAAI,CAAEthB,UAAWuhB,EAAmB,CAAErb,kBAAAA,EAAiB,CAAEuT,QAAS+H,EAAiB,CAAElsB,QAASmsB,EAAiB,CAAExhB,SAAUyhB,EAAkB,CAAE/H,SAAUgI,EAAkB,CAAEpoB,MAAOqoB,EAAe,CAAEpoB,WAAYqoB,EAAoB,CAAErsB,KAAMssB,EAAc,CAAEC,MAAAA,EAAK,CAAE,CAAIpuB,GAa1S,OAAMquB,WAAkBZ,GACpBjnB,aAAc,CAMV,KAAK,IAAIhC,WACT,IAAI,CAAC8pB,aAAa,CAAG,EAAE,AAC3B,CAWArB,QAAQte,CAAI,CAAE,CACV,GAAM,CAAExM,MAAAA,CAAK,CAAE6pB,MAAAA,CAAK,CAAE,CAAG,IAAI,CAAE7D,EAAYkF,GAAW,IAAI,CAAChrB,OAAO,CAAC8lB,SAAS,EAExExZ,EAEAqd,EAAM1kB,IAAI,CAAC,CACPiiB,WAAYpnB,EAAMW,QAAQ,CAAGX,EAAMyK,SAAS,CAAG,EAC/ChE,WAAYzG,EAAMa,OAAO,CAAGb,EAAM0K,UAAU,CAAG,EAC/Cwc,OAAQ,KACRC,OAAQ,IACZ,GAIA0C,EAAMiB,OAAO,CAAC,CACV1D,WAAYpnB,EAAMW,QAAQ,CAC1B8F,WAAYzG,EAAMa,OAAO,CACzBqmB,OAAQ,EACRC,OAAQ,CACZ,EAAGnB,EAEX,CACAoE,aAAc,CACV,IAAI,CAAC/R,MAAM,CAACpJ,OAAO,CAAC,AAAClH,IACjB,OAAOA,EAAM0I,MAAM,CACnB,OAAO1I,EAAMkK,UAAU,CACvB,OAAOlK,EAAM6H,aAAa,AAC9B,GACA,OAAO,IAAI,CAACa,MAAM,AACtB,CAMA2b,iBAAkB,CACd,MAAOxB,CAAAA,CAAQ,CAAA,IAAI,CAACP,WAAW,EAC3B,IAAI,CAACrqB,KAAK,CAACqsB,UAAU,EACrB,CAAC,IAAI,CAACvG,WAAW,AAAD,CACxB,CAMAwG,mBAAoB,CAChB,KAAK,CAACC,iBACF,IAAI,CAACC,eAAe,EACpB,IAAI,CAACA,eAAe,CAACC,IAAI,CAAC,IAAI,CAACzsB,KAAK,CAAC0sB,QAAQ,CAErD,CAMAC,YAAa,CACT,IAAM7kB,EAAS,IAAI,CAAE,CAAE9H,MAAAA,CAAK,CAAE6pB,MAAAA,CAAK,CAAEM,gBAAAA,EAAkB,EAAE,CAAE,CAAG,IAAI,CAAE,CAAE5oB,QAAAA,CAAO,CAAEuD,SAAAA,CAAQ,CAAE,CAAG9E,EAC5F,GAAKuB,GAKL,IAAI,CAAC4oB,eAAe,CAAGA,EAClBA,CAAe,CAAC,EAAE,EACnBA,CAAAA,CAAe,CAAC,EAAE,CAAGrlB,EAASI,CAAC,GAAGG,GAAG,CAACwkB,EAAK,EAE/C,IAAK,IAAI1e,EAAI,EAAGiL,EAAO7U,EAAQ2Q,MAAM,CAACvN,MAAM,CAAEwG,EAAIiL,EAAM,EAAEjL,EACjDgf,CAAe,CAAChf,EAAI,EAAE,EACvBgf,EAAgBzkB,IAAI,CAACZ,EAASI,CAAC,GAAGG,GAAG,CAACwkB,IAI1C,IAAI,CAACuC,eAAe,KAEpB,IAAI,CAAC/T,MAAM,CAACpJ,OAAO,CAAC,AAAClH,IACjB,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAGF,CAEpBA,CAAAA,EAAM8hB,KAAK,CAAGM,CAAe,CAAC,AAA4B,UAA5B,OAAOpiB,EAAMkK,UAAU,CACjDlK,EAAMkK,UAAU,CAAG,EACnB,EAAE,CAGFhK,GAAWA,EAAQI,WAAW,GAAKN,EAAM8hB,KAAK,EAC9C5hB,EAAQ5C,GAAG,CAAC0C,EAAM8hB,KAAK,CAE/B,GAEAwB,GAAa5tB,SAAS,CAACkvB,UAAU,CAACxqB,KAAK,CAAC,IAAI,EAE5C,IAAI,CAACkW,MAAM,CAACpJ,OAAO,CAAC,AAAClH,IACjB,IAAME,EAAUF,EAAME,OAAO,CAC7B,GAAIA,EAAS,CACT,IAAM6iB,EAAU7iB,EAAQ6iB,OAAO,CAC3B8B,EAAY,EACZ7kB,CAAAA,EAAMuI,IAAI,EACVsc,CAAAA,GACI,mBACI7kB,EAAMuI,IAAI,CAAClC,OAAO,CAAC,KAAM,KAAKye,WAAW,EAAC,EAElD9kB,EAAM2I,UAAU,EAAE,CAAC,SAAS,EAC5Bkc,CAAAA,GACI,mBACI7kB,EAAM2I,UAAU,CAAC,SAAS,CAACqP,QAAQ,GAAG8M,WAAW,EAAC,EAE1DD,GACA3kB,EAAQzC,QAAQ,CAAConB,GAGjB5sB,EAAMsF,UAAU,EAChB2C,EAAQE,GAAG,CAAC,IAAI,CAACuB,YAAY,CAAC3B,EAAOA,EAAM+kB,QAAQ,EAAI,UAAY,KAAK,IAK5E7kB,EAAQ9C,IAAI,CAAC,CACTuD,WAAY,AAACX,CAAAA,EAAM0J,OAAO,EACrB,CAAA,AAAC1J,EAAM0J,OAAO,EAAK1J,EAAMyJ,MAAM,AAAD,EAAkB,SAAZ,SAC7C,GACAvJ,EAAQ6iB,OAAO,CAAG,SAAUiC,CAAM,CAAE7sB,CAAO,CAAE8sB,CAAQ,EACjD,IAAMC,EAAarB,GAAmBmB,CAAM,CAAC,eAAe,GACxD,CAACnB,GAAmB3jB,CAAO,CAAC,eAAe,EAAIilB,EAActB,GAAmB3jB,CAAO,CAAC,eAAe,GACvG,CAAC2jB,GAAmBmB,CAAM,CAAC,eAAe,EAE9C,GAAIE,GAAaC,EAAY,CACzB,IACGC,EAAwBnC,AADPgB,GAAelkB,EAAOslB,cAAc,CAACtlB,EAAO5H,OAAO,EAAG,GAErEF,CAAAA,EAAMuB,OAAO,EAAEilB,YACZ,CAAA,EAGJyG,GACAhlB,CAAAA,CAAO,CAAC,eAAe,CAAGklB,CAAmB,EAG7CD,GACAH,CAAAA,CAAM,CAAC,eAAe,CAAGI,CAAmB,CAEpD,CAUA,OATYrC,EAAQntB,IAAI,CAACsK,EAAS8kB,EAAQ7sB,EAASgtB,EAAa,WAE5DjlB,EAAQrC,OAAO,CAACynB,eAAe,CAAC,gBAChC,OAAOplB,CAAO,CAAC,eAAe,CAE1B+kB,GACAA,EAAS7qB,KAAK,CAAC,IAAI,CAAEE,UAE7B,EAAI2qB,EAER,CACJ,CACJ,IAGJ7C,EAAgBlb,OAAO,CAAC,CAACqe,EAAgBniB,KACrC,IAAwDoiB,EAAeC,AAA1DriB,CAAAA,AAAM,IAANA,EAAU5J,EAAUA,EAAQ2Q,MAAM,CAAC/G,EAAI,EAAE,AAAD,EAAuB4b,eAAe,GAAIiE,EAAcgB,GAAe,IAAI,CAACoB,cAAc,CAAC,IAAI,CAACltB,OAAO,EAAG,GAezJyU,EAAQ4Y,EAAarG,MAAM,CAAED,EAAasG,EAAapG,MAAM,CAAG,EAAI,EAAI,GACxEsG,EAAgB,AAAC9Y,IACnB,AAAC7M,CAAAA,EAAOuQ,MAAM,EAAI,EAAE,AAAD,EAAGpJ,OAAO,CAAC,AAAClH,IAC3B,IACIijB,EADE/iB,EAAUF,EAAME,OAAO,CAEzBA,GAAS,CAAC,eAAe,EACxB+iB,CAAAA,EAAc,IAAI,CAACoC,cAAc,CAACrlB,EAAM7H,OAAO,CAAA,GAChD+H,EAAQ9C,IAAI,CAAC,CACT,eAAgB6lB,EAAcrW,CAClC,EAER,EACJ,EACA,GAAI7P,EAAS4oB,eAAe,EACxB1tB,EAAM8lB,WAAW,EACjBvkB,EAAQ6jB,uBAAuB,CAAE,CACjC,IAAMuI,EAAkBhf,OAAO2e,EAAenoB,IAAI,CAAC,eAC7CyoB,EAAkBjf,OAAO2e,EAAenoB,IAAI,CAAC,eAC7C0oB,EAAalf,OAAO2e,EAAenoB,IAAI,CAAC,WACxCma,EAAO,CAACwO,EAAKC,KACf,IAAMC,EAAYH,EACd,AAAClZ,CAAAA,EAAQkZ,CAAS,EAAKE,EAAGlH,GAAG,CACjCyG,EAAenoB,IAAI,CAAC,CAChBiiB,WAAauG,EAAkB,AAACJ,CAAAA,EAAanG,UAAU,CAAGuG,CAAc,EAAKI,EAAGlH,GAAG,CACnFpgB,WAAamnB,EAAkB,AAACL,CAAAA,EAAa9mB,UAAU,CAAGmnB,CAAc,EAAKG,EAAGlH,GAAG,CACnFK,OAAQ8G,EACR7G,OAAQ6G,EAAY/G,EACpB,eAAgB+D,EAAcgD,CAClC,GACAP,EAAcO,EAClB,EACMjc,EAAc+Z,GAAgBZ,GAAWpmB,EAAS4oB,eAAe,GAAIO,EAAWlc,EAAYuN,IAAI,AACtGvN,CAAAA,EAAYuN,IAAI,CAAG,WACX2O,GACAA,EAAS9rB,KAAK,CAAC,IAAI,CAAEE,WAEzBid,EAAKnd,KAAK,CAAC,IAAI,CAAEE,UACrB,EACAirB,EACKnoB,IAAI,CAAC,CAAE+oB,SAAU,CAAE,GACnBpD,OAAO,CAAC,CAAEoD,SAAU,CAAE,EAAGnc,EAAa,CAAA,WACC,WAApC,OAAOjN,EAAS4oB,eAAe,EAC/B5oB,EAAS4oB,eAAe,CAACV,QAAQ,EAEjCloB,EAAS4oB,eAAe,CAACV,QAAQ,CAAC,CAC9BmB,eAAgB,CAAA,CACpB,GAEJ1C,GAAoB,IAAI,CAAE,kBAC9B,CAAA,EAAE2C,IAAI,CAAC,IAAI,EAEf,MAEIjD,GAAKmC,GACLA,EAAenoB,IAAI,CAAC2mB,GAAgByB,EAAc,CAAE,eAAgBvC,EAAcrW,CAAM,IACxF8Y,EAAc9Y,EAEtB,GACK,IAAI,CAAC0T,UAAU,EAChB,IAAI,CAACiE,iBAAiB,GAE9B,CAKA9b,oBAAqB,CACjB,GAAI,CAAC,IAAI,CAACC,MAAM,EAAI,IAAI,CAACzQ,KAAK,CAACuB,OAAO,CAAE,CACpC,GAAM,CAAE2Q,OAAAA,CAAM,CAAE3E,WAAAA,CAAU,CAAE,CAAG,IAAI,CAACvN,KAAK,CAACuB,OAAO,CAAEklB,EAAY,EAAE,CAEjE,AAAC,CAAA,IAAI,CAACpO,MAAM,EAAI,EAAE,AAAD,EAAGpJ,OAAO,CAAC,AAAClH,IACzB,GAAIA,EAAMpC,IAAI,EAAIoC,EAAM8H,QAAQ,CAAE,CAa9B,GAVI,AAAsB,UAAtB,OAAO9H,EAAMpC,IAAI,CACjBoC,EAAMpC,IAAI,CAAGsI,GAAUlG,EAAMpC,IAAI,EAG5B+lB,GAAkB3jB,EAAMpC,IAAI,GACjCoC,AAAkB,MAAlBA,EAAMpC,IAAI,CAAC,EAAE,EACboC,CAAAA,EAAMpC,IAAI,CAAG,IAAI,CAAC3F,KAAK,CAAC8E,QAAQ,CAC3BwJ,cAAc,CAACvG,EAAMpC,IAAI,CAAA,EAG9B,CAACoC,EAAM0I,MAAM,CAAE,CACf,IAAIA,EAAS1I,EAAMyI,kBAAkB,CAACjD,GACtC,GAAIkD,EAAQ,CACR1I,EAAMsmB,SAAS,CAAGrC,GAAejkB,EAAMsmB,SAAS,CAE/C,AAAC5d,CAAAA,EAAO/B,EAAE,CAAG+B,EAAO5B,EAAE,AAAD,EACjB4B,CAAAA,EAAO3B,EAAE,CAAG2B,EAAO1B,EAAE,AAAD,GACzB,GAAM,CAAEgC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAGP,EACvB,GAAIyB,GAAU0Z,GAAmB7a,IAAS6a,GAAmB5a,GAAO,CAChE,IAAMgB,EAAQwZ,GAAKtZ,EAAQ,AAACF,GAAUA,EAAM6H,QAAQ,CAAC,CACjDtb,EAAGwS,EAAMzR,EAAG0R,CAChB,IACIgB,IAGA,OAAOjK,EAAM6H,aAAa,CAC1Ba,CAAAA,EAAS1I,EAAMyI,kBAAkB,CAACwB,EAAMzE,UAAU,CAAA,GAE9CyE,EAAMyU,SAAS,CAAC/gB,IAAI,CAAC+K,GAEzB1I,EAAMkK,UAAU,CAAGC,EAAO3B,OAAO,CAACyB,GAE1C,CACAjK,EAAM0I,MAAM,CAAGA,CACnB,CACJ,CACI1I,EAAM0I,MAAM,EAAI1I,AAAqB,KAAK,IAA1BA,EAAMkK,UAAU,EAChCwU,EAAU/gB,IAAI,CAACqC,EAAM0I,MAAM,CAEnC,CACJ,GACA,IAAI,CAACA,MAAM,CAAG6d,AA3VyB5J,GA2VZE,eAAe,CAAC6B,EAC/C,CACA,OAAO,IAAI,CAAChW,MAAM,AACtB,CAQA2c,eAAeltB,CAAO,CAAE,CACpB,IAAMquB,EAAqB,IAAI,CAACA,kBAAkB,CAClD,OAAOruB,CAAO,CAACquB,GAAoB,CAAC,eAAe,EAAI,cAAc,AACzE,CAMAC,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CAOAhlB,aAAa3B,CAAK,CAAEO,CAAK,CAAE,CACvB,GAAM,CAAE/G,QAAAA,CAAO,CAAE+D,WAAAA,CAAU,CAAE,CAAGyC,EAAMD,MAAM,CAAC9H,KAAK,CAC5CmF,EAAOG,EACT,IAAI,CAACiE,YAAY,CAACxB,GAClBsjB,GAAa5tB,SAAS,CAACiM,YAAY,CAAC/L,IAAI,CAAC,IAAI,CAAEoK,EAAOO,GAEtDqmB,EAAmB,IAAI,CAACvB,cAAc,CAACrlB,EAAM7H,OAAO,EAExD,GAAIoI,EAAO,CACP,IAAMsmB,EAAe9C,GAAgB,IAAI,CAAC5rB,OAAO,CAAC2uB,MAAM,EACpD,IAAI,CAAC3uB,OAAO,CAAC2uB,MAAM,CAACvmB,EAAM,CAAEP,EAAM7H,OAAO,CAAC2uB,MAAM,EAChD9mB,EAAM7H,OAAO,CAAC2uB,MAAM,CAACvmB,EAAM,EAC3B,CAAC,GAAIwmB,EAAmB,IAAI,CAAC1B,cAAc,CAACwB,GAC5CjD,GAAkBmD,IAClBH,CAAAA,EAAmBG,CAAe,EAEtC3pB,EAAKlG,MAAM,CAAG2vB,EAAazb,WAAW,EAAIpL,EAAMnJ,KAAK,AACzD,CACI+vB,GAAoBptB,GACpBotB,CAAAA,GAAoBptB,EAAQilB,QAAQ,EAAC,EAIzC,IAAMuI,EAAoB,IAAI,CAAC3B,cAAc,CAAC,IAAI,CAACltB,OAAO,EAoB1D,OAnBIiF,EAAK6pB,SAAS,EACdztB,GACAqqB,GAAmBmD,IACnBJ,CAAAA,EAAmBI,EAAoBxtB,EAAQilB,QAAQ,EAAC,EAMvDze,EAAM0J,OAAO,EACdtM,CAAAA,EAAKnG,IAAI,CAAG,IAAI,CAACkB,OAAO,CAAC+uB,SAAS,AAAD,EAEjCtD,GAAkBgD,GAClBxpB,CAAI,CAAC,eAAe,CAAGwpB,EAGvB,OAAOxpB,CAAI,CAAC,eAAe,CAE/BA,CAAI,CAAC,iBAAiB,CAAGA,CAAI,CAAC,kBAAkB,CAAG,IAAI,CAACjF,OAAO,CAACgvB,OAAO,CAChE/pB,CACX,CACAgqB,YAAa,OAET,CAAI,IAAI,CAAChD,aAAa,EAGf,KAAK,CAACgD,WAAWhtB,KAAK,CAAC,IAAI,CAAEE,UACxC,CAKA+sB,QAAQC,CAAI,CAAE1c,EAAS,CAAA,CAAI,CAAEqT,CAAS,CAAEsJ,CAAY,CAAE,CAClD,OAAO,IAAI,CAAC7e,MAAM,CAClB,KAAK,CAAC2e,QAAQC,EAAM,CAAA,EAAO,KAAK,EAAGC,GACnC,IAAI,CAACC,WAAW,GAChB,IAAI,CAACC,cAAc,GACf7c,GACA,IAAI,CAAC3S,KAAK,CAAC2S,MAAM,CAACqT,EAE1B,CACAyJ,gBAAiB,CAEb,OAAO,IAAI,CAACpmB,aAAa,AAC7B,CAQAkmB,aAAc,CACV,IASIlf,EAAUqf,EAUV9hB,EAnBE1N,EAAU,IAAI,CAACA,OAAO,CAAEmvB,EAAOnvB,EAAQmvB,IAAI,CAAErvB,EAAQ,IAAI,CAACA,KAAK,CAAE2vB,EAAe3vB,EAAME,OAAO,CAACF,KAAK,CAAEgQ,EAAS,IAAI,CAACA,MAAM,CAAE3G,EAAgBnJ,EAAQ4V,IAAI,EAAI,IAAI,CAACzM,aAAa,CAAEumB,EAAW,EAAE,CAAE3f,EAAS,CAAC,EAAG1O,EAAU,IAAI,CAACvB,KAAK,CAACuB,OAAO,CAAEsuB,EAAgBtuB,GAE/PsqB,CAAAA,GAAmB3rB,EAAQ0N,OAAO,CAAE,CAAA,GAChCrM,EAAQ+kB,SAAS,CAACpmB,EAAQ0N,OAAO,EAAIrM,EAAQwmB,MAAM,AAAD,EAEtDT,EAAgBtnB,EAAMsnB,aAAa,CAC/BqI,EAAarI,aAAa,EACtBuI,GAAe,CAAC,eAAe,EAC/B7vB,EAAMsnB,aAAa,CAGvBA,GACAyE,GAAqBzE,EAAe,AAAChT,IAC7BA,EAAUrB,QAAQ,GAClBqB,EAAUc,QAAQ,CAAGjU,KAAKkU,GAAG,CAACf,EAAUrB,QAAQ,EAChDqB,EAAUgB,QAAQ,CAAGnU,KAAKoU,GAAG,CAACjB,EAAUrB,QAAQ,EAExD,GAGAyY,GAAkBxrB,EAAQ0N,OAAO,EACjCA,EAAU1N,EAAQ0N,OAAO,CAEpBiiB,GAAiBA,AAAuB,sBAAvBA,EAAc/iB,IAAI,GACxC,IAAI,CAACgjB,QAAQ,CAAGD,EAAcpqB,KAAK,CACnCmI,EAAU/P,IAA6CmZ,OAAO,CAAC6Y,EAAe,IAAI,CAAC/iB,IAAI,CAAE,IAAI,GAGjG,IAAI,CAACqf,aAAa,CAAG,EAAE,CACvB,IAAMA,EAAgB,IAAI,CAACA,aAAa,CAGxC,GAAIkD,EAAM,CACN,IAAI5O,EACJ,IAAK,IAAItV,EAAI,EAAGiL,EAAOiZ,EAAK1qB,MAAM,CAAEwG,EAAIiL,EAAM,EAAEjL,EAAG,CAE/C,GAAIygB,GADJnL,EAAM4O,CAAI,CAAClkB,EAAE,EAETghB,CAAa,CAAChhB,EAAE,CAAG,CACfrC,MAAO2X,CACX,OAEC,GAAIiL,GAAkBjL,GAAM,CAC7B,IAAIsP,EAAK,CACT5D,CAAAA,CAAa,CAAChhB,EAAE,CAAG,CAAC,EAGhB,CAACjL,EAAQ4V,IAAI,EACb2K,EAAI9b,MAAM,CAAG0E,EAAc1E,MAAM,EACjC,AAAkB,UAAlB,OAAO8b,CAAG,CAAC,EAAE,GACb0L,CAAa,CAAChhB,EAAE,CAAC,SAAS,CAAGsV,CAAG,CAAC,EAAE,CACnC,EAAEsP,GAIN,IAAK,IAAI1Z,EAAI,EAAGA,EAAIhN,EAAc1E,MAAM,CAAE,EAAE0R,EAAG,EAAE0Z,EACzC1mB,CAAa,CAACgN,EAAE,EAChB,AAAmB,KAAA,IAAZoK,CAAG,CAACsP,EAAG,GACV1mB,CAAa,CAACgN,EAAE,CAAC9F,OAAO,CAAC,KAAO,EAChCyf,AA7jImBtgB,GA6jINjS,SAAS,CAACwyB,iBAAiB,CAAC9D,CAAa,CAAChhB,EAAE,CAAEsV,CAAG,CAACsP,EAAG,CAAE1mB,CAAa,CAACgN,EAAE,EAGpF8V,CAAa,CAAChhB,EAAE,CAAC9B,CAAa,CAACgN,EAAE,CAAC,CAAGoK,CAAG,CAACsP,EAAG,CAI5D,MAEI5D,CAAa,CAAChhB,EAAE,CAAGkkB,CAAI,CAAClkB,EAAE,CAE1B6E,GACAA,AAAc,OAAdA,CAAM,CAAC,EAAE,EACTmc,CAAAA,CAAa,CAAChhB,EAAE,CAAC+kB,EAAE,CAAG/kB,CAAAA,CAE9B,CACJ,CACA,GAAIyC,EAAS,CACT,IAAI,CAACA,OAAO,CAAGA,EACf,IAAI,CAACqC,MAAM,CAAG,CAAC,EACf,IAAK,IAAI9E,EAAI,EAAGA,EAAIyC,EAAQjJ,MAAM,CAAEwG,IAEhCukB,EAAQrf,AADRA,CAAAA,EAAWzC,CAAO,CAACzC,EAAE,AAAD,EACHuF,UAAU,CAC3BL,EAAS6f,EAAE,CAAG/kB,EAEV6E,CAAM,CAAC,EAAE,EAAI0f,GAASA,CAAK,CAAC1f,CAAM,CAAC,EAAE,CAAC,EACtCK,CAAAA,CAAQ,CAACL,CAAM,CAAC,EAAE,CAAC,CAAG0f,CAAK,CAAC1f,CAAM,CAAC,EAAE,CAAC,AAAD,EAEzCC,CAAM,CAACI,CAAQ,CAACL,CAAM,CAAC,EAAE,CAAC,CAAC,CAAGK,EAIlC,GAFA,IAAI,CAACJ,MAAM,CAAGA,EAEVD,CAAM,CAAC,EAAE,CAAE,CACX,IAAME,EAAUF,CAAM,CAAC,EAAE,CACzBmc,EAAcld,OAAO,CAAC,AAAC8I,IACnB,IAAM5H,EAASC,GAAkBF,EAAS6H,EACtC9H,CAAAA,CAAM,CAACE,EAAO,EACdyf,EAASlqB,IAAI,CAACuK,CAAM,CAACE,EAAO,CAEpC,EACJ,CACA,GAAIjQ,EAAQiwB,QAAQ,CAAE,CAElB,GAAIngB,CAAM,CAAC,EAAE,CAAE,CACX,IAAME,EAAUF,CAAM,CAAC,EAAE,CACzBmc,EAAcld,OAAO,CAAC,AAAC8I,IACnB6X,EAASlqB,IAAI,CAAC0K,GAAkBF,EAAS6H,GAC7C,EACJ,CAIA,IAAMqY,EAAkB,IACpBR,EACKliB,GAAG,CAAC,SAAU3F,CAAK,EACpB,OAAOA,GAASA,CAAK,CAACiI,CAAM,CAAC,EAAE,CAAC,AACpC,GACKgQ,IAAI,CAAC,KACV,IACJpS,EAAQqB,OAAO,CAAC,AAACoB,IACRL,CAAM,CAAC,EAAE,EACVogB,AAEa,KAFbA,EAAe7f,OAAO,CAAC,IACnBF,CAAQ,CAACL,CAAM,CAAC,EAAE,CAAC,CACnB,MACJmc,EAAczmB,IAAI,CAAComB,GAAgBzb,EAAU,CAAEvH,MAAO,IAAK,GAEnE,EACJ,CACJ,CAGA,IAAI,CAAC2lB,SAAS,CAACC,QAAQ,CAAGvC,EAAcxnB,MAAM,AAElD,CAMAtB,WAAWgtB,CAAW,CAAE,CACpB,IAAMnwB,EAAU,KAAK,CAACmD,WAAWgtB,GAC7BrgB,EAAS9P,EAAQ8P,MAAM,CAU3B,OATuB,OAAnB9P,EAAQ8P,MAAM,EACdA,CAAAA,EAAS,IAAG,EAEZA,IACA,IAAI,CAACA,MAAM,CAAGic,GAAMjc,GACf,IAAI,CAACA,MAAM,CAAC,EAAE,EACf,CAAA,IAAI,CAACA,MAAM,CAAC,EAAE,CAAG,IAAI,CAACA,MAAM,CAAC,EAAE,AAAD,GAG/B9P,CACX,CAMAiW,WAAY,CACR,IAAqBiW,EAAkBtkB,AAAxB,IAAI,CAA2BskB,eAAe,GAAI7qB,EAAU,IAAI,CAACvB,KAAK,CAACuB,OAAO,CAAEgM,EAAahM,GAASgM,WAqBrH,GAnBI,IAAI,CAACvN,KAAK,CAAC8lB,WAAW,EAAK,CAAA,IAAI,CAACuE,WAAW,EAAI,CAAC,IAAI,CAACvE,WAAW,AAAD,IAC/D,IAAI,CAACyJ,WAAW,GAChB,IAAI,CAACC,cAAc,GACnB,OAAO,IAAI,CAAC/e,MAAM,CACdlP,CAAAA,GACCA,EAAQkL,WAAW,CAAC7B,MAAM,EAC1BghB,GAAmBrqB,EAAQkL,WAAW,CAACxF,IAAI,GAC5C1F,EAAQ0F,IAAI,GAAK1F,EAAQokB,OAAO,CAShC,IAAI,CAACnV,kBAAkB,GALvBjP,EAAQkR,WAAW,CAAC,KAAK,EAAG,KAAK,EAAG,CAAA,IAQxClR,EAAS,CACT,IAAM+uB,EAAmB/uB,EAAQwlB,eAAe,GAChDjf,AAvBW,IAAI,CAuBRuQ,MAAM,CAACpJ,OAAO,CAAC,AAAClH,IACnB,IAAMwlB,EAAe,AAAC3B,GAAmB7jB,EAAMkK,UAAU,GACrD1Q,EAAQ2Q,MAAM,CAACnK,EAAMkK,UAAU,CAAC,CAAC8U,eAAe,IAAOuJ,EAGvD/C,GACAxlB,EAAM0I,MAAM,EACZmb,GAAmB7jB,EAAM0I,MAAM,CAACM,IAAI,GACpC6a,GAAmB7jB,EAAM0I,MAAM,CAACO,IAAI,IACpCjJ,EAAMwoB,KAAK,CAAGxoB,EAAM0I,MAAM,CAACM,IAAI,CAAGwc,EAAarG,MAAM,CACjDqG,EAAanG,UAAU,CAC3Brf,EAAMyoB,KAAK,CAAGzoB,EAAM0I,MAAM,CAACO,IAAI,CAAGuc,EAAapG,MAAM,CACjDoG,EAAa9mB,UAAU,EAE3B2lB,IACArkB,EAAM0oB,SAAS,CAAG,OAClB1oB,EAAM2oB,SAAS,CAAG,CACd7zB,EAAGmzB,AAxsIwBtgB,GAwsIXC,gBAAgB,CAAC5H,EAAOwF,EAC5C,GAECxF,EAAM4oB,iBAAiB,GACpB5oB,EAAM6H,aAAa,EAAI,CAAC7H,EAAM6H,aAAa,CAACjL,MAAM,CAClDoD,EAAM6J,UAAU,CAAC,CAAA,GAEX7J,EAAM0J,OAAO,EACnB1J,EAAM6J,UAAU,CAAC,CAAA,GAG7B,EACJ,CACA6Z,GArDe,IAAI,CAqDS,iBAChC,CACArnB,OAAOlE,CAAO,CAAE,CAGRA,EAAQ0N,OAAO,EACf,IAAI,CAAC5N,KAAK,CAACuB,OAAO,EAAEkM,iBAAiB,IAAI,CAACzN,KAAK,CAAE,CAC7C,IAAI,CAACA,KAAK,CAACE,OAAO,CAACF,KAAK,CAAC0N,GAAG,IACzB,AAAC,CAAA,IAAI,CAAC1N,KAAK,CAACE,OAAO,CAAC4H,MAAM,EAAI,EAAE,AAAD,EAAG4F,GAAG,CAAC,CAACC,EAAGxC,IACzC,AAAIA,IAAM,IAAI,CAAC+kB,EAAE,CACNhwB,EAAQ0N,OAAO,CAEnBD,EAAEC,OAAO,EAEvB,CAAE,CAAA,GAEP,KAAK,CAACxJ,OAAOjC,KAAK,CAAC,IAAI,CAAEE,UAC7B,CACJ,CACA6pB,GAAU0E,cAAc,CAAG9E,GAAgBR,GAAcsF,cAAc,CAtsI7C,CAYtBlK,eAAgB,CAAA,EAChBV,UAAW,CAAA,EACX6K,WAAY,CACRC,KAAM,CAAA,EACNC,UAAW,WACP,GAAM,CAAEC,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAAClpB,MAAM,CAAC9H,KAAK,CACvC,CAAE8I,MAAAA,CAAK,CAAE,CAAG,IAAI,CAACf,KAAK,CAC5B,OAAO6K,GAA2B9J,GAC9BkoB,EAAgBloB,EAAO,IACtB,IAAI,CAACf,KAAK,CAACuI,IAAI,EAAI,EAC5B,EACA2I,OAAQ,CAAA,EACRgY,SAAU,CAAA,EACVvyB,QAAS,EACTJ,cAAe,QACnB,EAYA4wB,QAAS,QAMTgC,OAAQ,KAcRjC,UAAW,UAWXkC,eAAgB,CAAA,EAChBlkB,QAAS,CACLmkB,cAAe,CAAA,EACfC,YAAa,kCACjB,EAMAC,eAAgB,EAgBhBnB,SAAU,CAAA,EAiBVhd,YAAa,UAiBbC,YAAa,EAoCbpD,OAAQ,SAaR6e,OAAQ,CAIJ0C,MAAO,CAEHC,KAAM,KAAK,EAkBXre,YAAa,UAQbC,YAAa,CAUjB,EAIAqe,OAAQ,CAYJzL,UAAW,CAAA,CACf,EAIA0L,OAAQ,CAOJ9yB,MAAO,SACX,CACJ,EACA+yB,aAAc,WAClB,GA08HApG,GAAiBW,GAAUzuB,SAAS,CAAE,CAClCqP,KAAM,MACN3D,UAAWU,EAA2BZ,aAAa,CAACE,SAAS,CAC7DI,aAAcM,EAA2BZ,aAAa,CAACM,YAAY,CACnEL,SAAUW,EAA2BZ,aAAa,CAACC,QAAQ,CAG3D0oB,YAAa,CAAA,EAGbrF,eAAgBnB,GAEhByG,UAAWzG,GACX0G,QAAS,CAAA,EACTxnB,UAAWuB,EAAyBvB,SAAS,CAC7CynB,mBAAoB,CAAA,EACpBC,UAAW5G,GACX6G,YAAa,CAAA,EACb7oB,eAAgBS,EAA2BZ,aAAa,CAACG,cAAc,CACvEC,cAAeQ,EAA2BZ,aAAa,CAACI,aAAa,CACrEO,WA7vI+C8F,GA+vI/CwiB,oBAAqB,CAAA,EACrBC,YAAa/G,GACb9hB,cAAeO,EAA2BZ,aAAa,CAACK,aAAa,CAErE0e,eAAgB,CAAA,CACpB,GACAne,EAA2BvH,OAAO,CAAC4pB,IACnC9kB,IAA4DgrB,kBAAkB,CAAC,MAAOlG,IAMzD,IAAMmG,GAAiBnG,GA2J9C,CAAEzsB,OAAQ6yB,EAAoB,CAAE7uB,MAAO8uB,EAAmB,CAAE,CAAI10B,GAatE,OAAM20B,WAAsBH,GAWxB3oB,aAAa3B,CAAK,CAAEO,CAAK,CAAE,CACvB,IAAMnD,EAAO,KAAK,CAACuE,aAAa3B,EAAOO,GAIvC,OADAnD,EAAKnG,IAAI,CAAG,IAAI,CAACkB,OAAO,CAACuyB,SAAS,CAC3BttB,CACX,CACJ,CAMAqtB,GAAc5B,cAAc,CAAG2B,GAAoBF,GAAczB,cAAc,CA7JjD,CAU1B8B,UAAW,EAMXD,UAAW,OACXd,aAAc,YAClB,GA4IAW,GAAqBE,GAAc/0B,SAAS,CAAE,CAC1CqP,KAAM,UACNrD,UAAW,SACX8kB,mBAAoB,CAChB,OAAU,QACV,eAAgB,WACpB,CACJ,GACAnnB,IAA4DgrB,kBAAkB,CAAC,UAAWI,IAoB1F,GAAM,CAAEljB,QAASqjB,EAA2B,CAAE,CAAG,AAACvrB,IAA6DM,WAAW,CAEpH,CAAEyC,SAAUyoB,EAAsB,CAAE,CAAI/0B,GAM9C,OAAMg1B,WAAsBF,GAA4Bl1B,SAAS,CAACmM,UAAU,CAMxEf,SAAU,CACN,MAAO+hB,CAAAA,CAAQ,CAAA,IAAI,CAAC1qB,OAAO,CAAC2P,QAAQ,EAC/B+iB,GAAuB,IAAI,CAACr0B,CAAC,GAAKq0B,GAAuB,IAAI,CAACtzB,CAAC,GAC/DszB,GAAuB,IAAI,CAAC1yB,OAAO,CAACgV,GAAG,GAAK0d,GAAuB,IAAI,CAAC1yB,OAAO,CAACiV,GAAG,CAAC,CAC7F,CACJ,CAwNa3Y,EAAoBK,CAAC,CAAzB,CAAC,EAIoI,CAAG,GAajJ,GAAM,CAAEuuB,KAAM0H,EAAmB,CAAE,CAAIj1B,IAIjC,CAAE6P,IAAKqlB,EAAwB,CAAEzjB,QAAS0jB,EAA4B,CAAE,CAAG,AAAC5rB,IAA6DM,WAAW,CAGpJ,CAAEjI,OAAQwzB,EAAqB,CAAE/oB,UAAWgpB,EAAwB,CAAE/oB,SAAUgpB,EAAuB,CAAE1vB,MAAO2vB,EAAoB,CAAE,CAAIv1B,GAehJ,OAAMw1B,WAAuBL,GACzB3uB,aAAc,CAMV,KAAK,IAAIhC,WACT,IAAI,CAAC+nB,WAAW,CAAG2I,GAAyBt1B,SAAS,CAAC2sB,WAAW,AAErE,CAOAmC,gBAAiB,CACb,KAAK,CAACA,iBACF,IAAI,CAACC,eAAe,EACpB,IAAI,CAACA,eAAe,CAACC,IAAI,CAAC,IAAI,CAACzsB,KAAK,CAAC0sB,QAAQ,CAErD,CAOA4G,aAAavb,CAAY,CAAE,CACvB,IAAMxW,EAAU,IAAI,CAACvB,KAAK,CAACuB,OAAO,CAClC,GAAIA,EAAS,CACT,GAAM,CAAEsO,SAAAA,CAAQ,CAAEqF,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAG4C,EAC3BtB,EAAe5G,GACfA,AAAkB,UAAlBA,EAAS/C,IAAI,EACb+C,EAAS4G,WAAW,CAIxB,GAHI0c,GAAwBje,IAAQie,GAAwBhe,IACxDsB,CAAAA,EAAc,CAACvB,EAAKC,EAAI,AAAD,EAEvBsB,EACA,OAAOlV,EAAQ0S,sBAAsB,CAAC,CAClCiB,IAAKuB,CAAW,CAAC,EAAE,CACnBtB,IAAKsB,CAAW,CAAC,EAAE,AACvB,EAER,CACJ,CACAN,WAAY,CACR,IAAM5U,EAAU,IAAI,CAACvB,KAAK,CAACuB,OAAO,CAOlC,GANA,IAAI,CAACiuB,cAAc,GACf,IAAI,CAAChf,kBAAkB,EAAI,IAAI,CAAC6Z,WAAW,GAC3C,OAAO,IAAI,CAAC5Z,MAAM,CAClB,IAAI,CAACD,kBAAkB,IAGvBjP,EAAS,CACT,IAAM+uB,EAAmB/uB,EAAQwlB,eAAe,GAAI,CAAEjX,eAAAA,CAAc,CAAE,CAAGvO,EAAQgM,UAAU,CAC3F,IAAI,CAAC8K,MAAM,CAACpJ,OAAO,CAAC,AAAC8K,IACjB,IAMIwZ,EANA,CAAEh1B,EAAAA,CAAC,CAAWe,EAAAA,CAAC,CAAW,CAAGya,EAC3BwT,EAAe,AAAC4F,GAAwBpZ,EAAE9H,UAAU,GACtD1Q,EAAQ2Q,MAAM,CAAC6H,EAAE9H,UAAU,CAAC,CAAC8U,eAAe,IAAOuJ,EACjDrV,EAAM,IAAI,CAACqY,YAAY,CAACvZ,EAAE7Z,OAAO,GAClC6Z,EAAErJ,UAAU,EACT,IAAI,CAAC4iB,YAAY,CAACvZ,EAAErJ,UAAU,EAkBtC,GAhBIuK,GACA1c,EAAI0c,EAAG1c,CAAC,CACRe,EAAI2b,EAAG3b,CAAC,EAGHya,EAAEtJ,MAAM,GACblS,EAAIwb,EAAEtJ,MAAM,CAACM,IAAI,CACjBzR,EAAIya,EAAEtJ,MAAM,CAACO,IAAI,CACbuc,GAAgB4F,GAAwB50B,IAAM40B,GAAwB7zB,KACtEya,EAAEwW,KAAK,CAAGhyB,EAAIgvB,EAAarG,MAAM,CAC7BqG,EAAanG,UAAU,CAC3BrN,EAAEyW,KAAK,CAAGlxB,EAAIiuB,EAAapG,MAAM,CAC7BoG,EAAa9mB,UAAU,CAC3B8sB,EAAY,CAAA,IAGhBJ,GAAwB50B,IAAM40B,GAAwB7zB,GAEtD,CAAA,GAAI,CAACi0B,EAAW,CACZ,IAAMC,EAAajyB,EAAQ6Q,sBAAsB,CAAC,CAAE7T,EAAAA,EAAGe,EAAAA,CAAE,EACzDya,CAAAA,EAAEwW,KAAK,CAAGiD,EAAWj1B,CAAC,CACtBwb,EAAEyW,KAAK,CAAG1gB,EACN0jB,EAAWl0B,CAAC,CACZ,IAAI,CAACU,KAAK,CAAC0K,UAAU,CAAG8oB,EAAWl0B,CAAC,AAC5C,CAAA,MAGAya,EAAEza,CAAC,CAAGya,EAAEwW,KAAK,CAAGxW,EAAEyW,KAAK,CAAG,KAAK,CAEnCzW,CAAAA,EAAEF,QAAQ,CAAG,IAAI,CAAC4Z,aAAa,CAAC1Z,GAEhCA,EAAE2Z,IAAI,CAAG,IAAI,CAACC,KAAK,CAAChvB,MAAM,CAAGoV,EAAE6Z,OAAO,GAAK,KAAK,CACpD,EACJ,CACAV,GAAyB,IAAI,CAAE,iBACnC,CACJ,CACAG,GAAezC,cAAc,CAAGwC,GAAqBJ,GAA6BpC,cAAc,CA9TjE,CAC3BC,WAAY,CACRC,KAAM,CAAA,EACN+C,MAAO,CAAA,EACP7xB,QAAS,CAAA,EACT+uB,UAAW,WACP,OAAO,IAAI,CAAChpB,KAAK,CAACuI,IAAI,AAC1B,EACA2gB,SAAU,CAAA,EACVtyB,MAAO,CAEHC,MAAO,SACX,CACJ,EACA+yB,aAAc,YAClB,GAsVA,AAACxlB,IAA0D1O,SAAS,CAACzB,OAAO,CAAC83B,SAAS,CA9BpE,CAACv1B,EAAGe,EAAGqD,EAAGC,EAAG1C,KAC3B,IACI6zB,EAASC,EADPC,EAAiB/zB,GAAWA,AAAoB,WAApBA,EAAQg0B,OAAO,CAE7CD,GACAF,EAAUx1B,EAAIoE,EAAI,EAClBqxB,EAAU10B,EAAIsD,GAGT1C,GACL,AAA2B,UAA3B,OAAOA,EAAQ6zB,OAAO,EACtB,AAA2B,UAA3B,OAAO7zB,EAAQ8zB,OAAO,EACtBD,EAAU7zB,EAAQ6zB,OAAO,CACzBC,EAAU9zB,EAAQ8zB,OAAO,GAIzBD,EAAUx1B,EAAIoE,EAAI,EAClBqxB,EAAU10B,EAAIsD,EAAI,EAClBtD,GAAKsD,GAET,IAAMC,EAAIoxB,EAAiBrxB,EAAI,EAAIA,EAAI,EACvC,MAAO,CACH,CAAC,IAAKmxB,EAASC,EAAQ,CACvB,CAAC,IAAKD,EAASC,EAASD,EAAUlxB,EAAGvD,EAAIuD,AAAI,IAAJA,EAASkxB,EAAUlxB,EAAGvD,EAAIuD,EAAE,CAErE,CAAC,IAAKA,EAAGA,EAAG,EAAG,EAAG,EAAGkxB,EAAUlxB,EAAGvD,EAAIuD,EAAE,CACxC,CAAC,IAAKkxB,EAAUlxB,EAAGvD,EAAIuD,AAAI,IAAJA,EAASkxB,EAASC,EAASD,EAASC,EAAQ,CACnE,CAAC,IAAI,CACR,AACL,EAEAf,GAAsBI,GAAe51B,SAAS,CAAE,CAC5CqP,KAAM,WACN3D,UAAW,CAAC,YAAY,CACxB2oB,QAAS,CAAA,EACTG,YAAa,CAAA,EACbroB,WA5YyDipB,GA6YzDV,YAAaW,GACb9K,eAAgB,CAAA,CACpB,GACA5gB,IAA4DgrB,kBAAkB,CAAC,WAAYiB,IAwR9D,IAAMc,GA5ON,CAYzBhhB,YAAa,KAAK,EAKlBC,YAAa,EAWbwZ,UAAW,KAAK,EAYhBhuB,MAAO,KAAK,EAWZw1B,mBAAoB,KAAK,EAOzBC,eAAgB,KAAK,EAQrBC,kBAAmB,GAOnBC,eAAgB,EAIhBvyB,QAAS,CAAA,EAITwyB,OAAQ,CAWJ5H,UAAW,KAAK,EAIhB6H,aAAc,CAAA,EAUd9gB,OAAQ,GAYRod,UAAW,KAAK,EAUhB1yB,MAAO,QAMPM,MAAO,CAEHE,SAAU,QAEVD,MAAO,SACX,EAKAL,EAAG,EAKHe,EAAG,CACP,EAMAo1B,QAAS,GAMTC,QAAS,GAMTC,YAAa,EAYbC,OAAQ,CAKJ/rB,MAAO,KAAK,EAKZqK,YAAa,KAAK,EAKlBvU,MAAO,KAAK,EAKZy1B,eAAgB,KAAK,CACzB,EAYAS,OAAQ,OAQRC,oBAAqB,CAAA,EAIrB3vB,OAAQ,EAIR4vB,WAAY,CAChB,EAuBM,CAAE5J,KAAM6J,EAAqB,CAAE,CAAIp3B,IAEnC,CAAEq3B,SAAAA,EAAQ,CAAEC,SAAAA,EAAQ,CAAEhrB,SAAUirB,EAAyB,CAAE3xB,MAAO4xB,EAAsB,CAAE31B,KAAM41B,EAAqB,CAAEC,WAAAA,EAAU,CAAE,CAAI13B,IAoZ1G23B,GAlYnC,MAMInxB,YAAYnE,CAAO,CAAEu1B,CAAM,CAAE,CACzB,IAAI,CAACC,QAAQ,CAAGT,GAChB,IAAI,CAACzoB,IAAI,CAACtM,EAASu1B,EACvB,CAUAjpB,KAAKtM,CAAO,CAAEu1B,CAAM,CAAE,CAClB,IAAI,CAACv1B,OAAO,CAAGA,EACf,IAAI,CAACuR,OAAO,CAAG,CAAA,EACf,IAAI,CAACzR,KAAK,CAAGy1B,EAAOz1B,KAAK,CACzB,IAAI,CAACy1B,MAAM,CAAGA,CAClB,CASAE,YAAYC,CAAK,CAAE,CAEfA,EAAMrY,MAAM,CAAC,IAAI,CAACrd,OAAO,CAAC00B,WAAW,CAAE,EAAG,IAAI,CAClD,CAUAiB,iBAAiBJ,CAAM,CAAE,CACrB,IACIK,EADEC,EAAeT,GAAsBG,EAAOv1B,OAAO,CAAC61B,YAAY,CAAE,IAAKC,EAAa,IAAI,CAACA,UAAU,EAAI,CAAC,EAAG91B,EAAU,IAAI,CAACA,OAAO,CAAE20B,EAAS30B,EAAQ20B,MAAM,CAAEP,EAAoBp0B,EAAQo0B,iBAAiB,CAI/M,GAAI,CAACO,GAAU,CAACA,EAAOlwB,MAAM,EAAI,CAACywB,GAA0BP,CAAM,CAAC,EAAE,CAAC/rB,KAAK,EAAG,CAC1E2sB,EAAOv1B,OAAO,CAAC+1B,YAAY,CAACC,UAAU,CAAG,CAAA,EACzC,MACJ,CAEAX,GAAWV,EAAQ,SAAU/3B,CAAC,CAAEiR,CAAC,EAC7B,OAAOA,EAAEjF,KAAK,CAAGhM,EAAEgM,KAAK,AAC5B,GACA,IAAI,CAAC+rB,MAAM,CAAGA,EACd,IAAI,CAACxxB,UAAU,GACf,IAAI,CAACmlB,MAAM,GAEX,IAAM2N,EAAW,IAAI,CAACC,eAAe,GAAIC,EAAS,IAAI,CAACxB,MAAM,CAAC,EAAE,CAACwB,MAAM,CAAEprB,EAAOorB,AAAS,EAATA,EAIhFP,EAAiBA,AAFjBA,CAAAA,EACIxB,EAAoB+B,EAASF,EAAS33B,KAAK,AAAD,EACZ,EAAIs3B,EAAiB,EACvD,IAAI,CAACK,QAAQ,CAAGA,EAChB,IAAI,CAACG,SAAS,CAAGp2B,AAAyB,SAAzBA,EAAQs0B,MAAM,CAACn2B,KAAK,CACjCy3B,EAAiB,EACrBE,EAAWO,UAAU,CAAGtrB,EAAO6qB,EAAiBC,EAChDC,EAAWQ,WAAW,CAAGvrB,EAAOkrB,EAAS13B,MAAM,CAAG,CACtD,CAKA4E,YAAa,CACT,IAAMwxB,EAAS,IAAI,CAACA,MAAM,CAAE30B,EAAU,IAAI,CAACA,OAAO,CAAE4H,EAAS,IAAI,CAAC9H,KAAK,CAAC8H,MAAM,CAAC5H,EAAQu2B,WAAW,CAAC,CAAEC,EAAW,IAAI,CAACjB,MAAM,CAACiB,QAAQ,CAAEC,EAAgB,CAClJvxB,OAAQlF,EAAQkF,MAAM,CACtB,eAAgBlF,EAAQkT,WAAW,AACvC,EAAGwjB,EAAmB,CAClBxxB,OAAQlF,EAAQkF,MAAM,CACtB,eAAgBlF,EAAQq0B,cAAc,AAC1C,EAAGsC,EAAe,CACdx4B,MAAO,AAAC,IAAI,CAACo3B,MAAM,CAACv1B,OAAO,CAAC42B,GAAG,EAC3B52B,AAAyB,SAAzBA,EAAQs0B,MAAM,CAACn2B,KAAK,CAAe,QAAU,OACjD+G,OAAQlF,EAAQkF,MAAM,AAC1B,EAAG2xB,EAAcjvB,EAAO5H,OAAO,CAACgxB,MAAM,CAAC6F,WAAW,CAAEzxB,EAAa,IAAI,CAACtF,KAAK,CAACsF,UAAU,CAEtFuvB,EAAO5lB,OAAO,CAAC,SAAU+nB,CAAK,CAAE7rB,CAAC,EACxB7F,IACDqxB,EAAc13B,MAAM,CAAGq2B,GAAsB0B,EAAM7jB,WAAW,CAAEjT,EAAQiT,WAAW,CAAErL,EAAOlJ,KAAK,EACjG+3B,EAAc33B,IAAI,CAAGg4B,EAAMp4B,KAAK,EAAIsB,EAAQtB,KAAK,CAC5C+3B,EAAc33B,IAAI,GACnB23B,EAAc33B,IAAI,CAAG8I,EAAOlJ,KAAK,CACjC+3B,CAAa,CAAC,eAAe,CAAGI,GAAe,GAEnDH,EAAiB33B,MAAM,CAAGq2B,GAAsB0B,EAAM3C,cAAc,CAAEn0B,EAAQm0B,cAAc,CAAEvsB,EAAOlJ,KAAK,GAG9Gi2B,CAAM,CAAC1pB,EAAE,CAACkrB,MAAM,CAAG,IAAI,CAACY,cAAc,CAACD,EAAMluB,KAAK,EAClD+rB,CAAM,CAAC1pB,EAAE,CAAGkqB,GAAuBR,CAAM,CAAC1pB,EAAE,CAAE,CAC1CP,OAASiqB,CAAM,CAAC,EAAE,CAACwB,MAAM,CAAGxB,CAAM,CAAC1pB,EAAE,CAACkrB,MAAM,CACxCK,CACR,GACKpxB,GACD+vB,GAAuB,CAAA,EAAMR,CAAM,CAAC1pB,EAAE,CAAE,CACpCwrB,cAAetB,GAAuBsB,GACtCC,iBAAkBvB,GAAuBuB,GACzCC,aAAcA,CAClB,EAER,EAAG,IAAI,CACX,CAaAI,eAAenuB,CAAK,CAAE,CAClB,IAAM5I,EAAU,IAAI,CAACA,OAAO,CAAEu2B,EAAc,IAAI,CAACv2B,OAAO,CAACu2B,WAAW,CAAES,EAAe,IAAI,CAACl3B,KAAK,CAAC8H,MAAM,CAAC2uB,EAAY,CAAEU,EAAOj3B,EAAQ20B,MAAM,CAAC,EAAE,CAAC/rB,KAAK,CAAEsuB,EAAOl3B,EAAQ20B,MAAM,CAAC30B,EAAQ20B,MAAM,CAAClwB,MAAM,CAAG,EAAE,CAACmE,KAAK,CAAE6rB,EAAUz0B,EAAQy0B,OAAO,CAAED,EAAUx0B,EAAQw0B,OAAO,CACjQ,OAAOwC,EAAaG,SAAS,CAAC15B,IAAI,CAAC,IAAI,CAAEy5B,EAAMD,EAAMxC,EAASD,EAAS5rB,EAC3E,CAKA0f,QAAS,CACL,IAAMwN,EAAa,IAAI,CAACA,UAAU,EAAI,CAAC,EAAGlxB,EAAW,IAAI,CAAC9E,KAAK,CAAC8E,QAAQ,CAAEkwB,EAAa,IAAI,CAAC90B,OAAO,CAAC80B,UAAU,CAkB9G,IAAK,IAAMgC,KAjBN,IAAI,CAACh7B,OAAO,EACb,CAAA,IAAI,CAACA,OAAO,CAAG,CACXs7B,WAAY,EAAE,CACdC,YAAa,EAAE,CACf/C,OAAQ,EAAE,AACd,CAAA,EAGJwB,EAAWwB,MAAM,CAAG1yB,EAASI,CAAC,CAAC,iBAC/B8wB,EAAWyB,KAAK,CAAG3yB,EAASI,CAAC,CAAC,sBACzBiD,GAAG,CAAC,IAAI,CAACstB,MAAM,CAACiC,SAAS,EAAI,CAAC,GAEnC1B,EAAWwB,MAAM,CAACpQ,UAAU,CAAG,EAC/B4O,EAAWwB,MAAM,CAAC/wB,UAAU,CAAG,EAE/BuvB,EAAWwB,MAAM,CAACnyB,GAAG,CAAC2wB,EAAWyB,KAAK,EACtCzB,EAAWyB,KAAK,CAACpyB,GAAG,CAAC2wB,EAAWnM,KAAK,EACjB,IAAI,CAACgL,MAAM,EACvBmC,EAAMluB,KAAK,EAAIksB,GACf,IAAI,CAAC2C,WAAW,CAACX,GAGzB,IAAI,CAACY,qBAAqB,EAC9B,CASAD,YAAYX,CAAK,CAAE,CACf,IAAMa,EAAY,IAAI,CAAChD,MAAM,CAAC,EAAE,CAAEY,EAAS,IAAI,CAACA,MAAM,CAAEv1B,EAAU,IAAI,CAACA,OAAO,CAAE43B,EAAgB53B,EAAQs0B,MAAM,CAAEx0B,EAAQ,IAAI,CAACA,KAAK,CAAEk3B,EAAel3B,EAAM8H,MAAM,CAAC5H,EAAQu2B,WAAW,CAAC,CAAE3xB,EAAW9E,EAAM8E,QAAQ,CAAE9I,EAAU,IAAI,CAACA,OAAO,CAAEw4B,EAASx4B,EAAQw4B,MAAM,CAAEuD,EAAgBf,EAAMpsB,MAAM,CAAEotB,EAAiB72B,KAAKC,GAAG,CAAC41B,EAAMX,MAAM,EAAG/B,EAAoBp0B,EAAQo0B,iBAAiB,EAAI,EAAG2D,EAAcH,EAAcz5B,KAAK,CAAEy4B,EAAMrB,EAAOv1B,OAAO,CAAC42B,GAAG,CAAE1jB,EAAclT,EAAQkT,WAAW,CAAEmhB,EAAiBr0B,EAAQq0B,cAAc,CAAE2D,EAAOL,EAAUxB,MAAM,EAAI,EAAG8B,EAAOJ,EAAgBC,EACvjB5kB,EAAc,EAAImhB,EAAiB,EAAG6D,EAAgB,AAACD,CAAAA,EAAO,EAAI,EAAI,EAAE,EACvE5D,CAAAA,EAAiB,EAAI,EAAI,EAAE,EAAIjvB,EAAaR,EAASQ,UAAU,CAChE+yB,EAAkBvB,GAAOmB,AAAgB,SAAhBA,EACzB,CAAC3D,EAAoBA,CAEL,CAAA,WAAhB2D,IACAI,EAAkB,EAClBn4B,EAAQo0B,iBAAiB,CAAG,EAC5B0C,EAAMH,YAAY,CAACx4B,KAAK,CAAG,UAG/BrC,EAAQu7B,WAAW,CAAC7xB,IAAI,CAACZ,EACpBwzB,MAAM,CAACJ,EAAMH,EAAgBK,EAAeJ,GAC5C7yB,IAAI,CAACG,EAAa,CAAC,EAAI0xB,EAAML,aAAa,EAC1CnxB,QAAQ,CAAC,AAACF,CAAAA,EACX,oBACI4xB,EAAaqB,UAAU,CAAG,IAC9B,EAAC,EACD,mCACCr4B,CAAAA,EAAQ0sB,SAAS,EAAI,EAAC,GAAIvnB,GAAG,CAAC,IAAI,CAAC2wB,UAAU,CAACwB,MAAM,GAEzDx7B,EAAQs7B,UAAU,CAAC5xB,IAAI,CAACZ,EACnBa,IAAI,CAACb,EAAS0zB,SAAS,CAAC,CACzB,CAAC,IAAKN,EAAMC,EAAK,CACjB,CAAC,IAAKD,EAAOG,EAAiBF,EAAK,CACtC,CAAEj4B,EAAQq0B,cAAc,GACpBpvB,IAAI,CAAEG,EAAa,CAAC,EAAI0xB,EAAMJ,gBAAgB,EAC9CpxB,QAAQ,CAAC,AAACF,CAAAA,EACX,oBACI,IAAI,CAACpF,OAAO,CAACu2B,WAAW,CAAG,IAAM,EAAC,EACtC,uCACCv2B,CAAAA,EAAQk0B,kBAAkB,EAAI,EAAC,GAAI/uB,GAAG,CAAC,IAAI,CAAC2wB,UAAU,CAACwB,MAAM,GAElE,IAAMC,EAAQ3yB,EACTzF,IAAI,CAAC,IAAI,CAACo5B,WAAW,CAACzB,IACtB7xB,IAAI,CAAEG,EAAa,CAAC,EAAI0xB,EAAMH,YAAY,EAC1C1uB,GAAG,CAAC7C,EAAa,CAAC,EAAIwyB,EAAcn5B,KAAK,EACzC6G,QAAQ,CAAC,mCACTtF,CAAAA,EAAQs0B,MAAM,CAAC5H,SAAS,EAAI,EAAC,GAAIvnB,GAAG,CAAC,IAAI,CAAC2wB,UAAU,CAACwB,MAAM,EAG1DkB,EAAW,CACbn6B,EAAG25B,EAAOG,EAAkBn4B,EAAQs0B,MAAM,CAACj2B,CAAC,CAC5Ce,EAAG64B,EAAOj4B,EAAQs0B,MAAM,CAACl1B,CAAC,CAAGm4B,AAAyB,GAAzBA,EAAMrxB,OAAO,GAAG3H,MAAM,AACvD,EACAg5B,EAAMtyB,IAAI,CAACuzB,GACXlE,EAAO9uB,IAAI,CAAC+xB,GAEZA,EAAMkB,MAAM,CAAG,CAAA,EACflB,EAAMmB,SAAS,CAAGF,CACtB,CAKAtC,iBAAkB,KAEVD,EAAU0C,EAWd,OAVArE,AAFe,IAAI,CAACx4B,OAAO,CAACw4B,MAAM,CAE3BvlB,OAAO,CAAC,SAAUwoB,CAAK,EAC1BoB,EAAYpB,EAAMrxB,OAAO,CAAC,CAAA,GAEtB+vB,EADAA,EACW0C,EAAUr6B,KAAK,CAAG23B,EAAS33B,KAAK,CACvCq6B,EAAY1C,EAGL0C,CAEnB,GACO1C,GAAY,CAAC,CACxB,CAYAsC,YAAYzB,CAAK,CAAE,CACf,IAAM92B,EAAU,IAAI,CAACA,OAAO,CAAE6wB,EAAY7wB,EAAQs0B,MAAM,CAACzD,SAAS,CAAEpd,EAASzT,EAAQs0B,MAAM,CAAC7gB,MAAM,CAC5F,CAAEqd,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAAChxB,KAAK,CACtC,OAAO2T,EAASF,KAAwDE,MAAM,CAACA,EAAQqjB,EAAO,IAAI,CAACh3B,KAAK,EACpG+wB,EAAYA,EAAUpzB,IAAI,CAACq5B,GACvBhG,EAAgBgG,EAAMluB,KAAK,CAAE,EACzC,CAMA8uB,uBAAwB,CACpB,IAAM53B,EAAQ,IAAI,CAACA,KAAK,CAAEy0B,EAAe,IAAI,CAACv0B,OAAO,CAACs0B,MAAM,CAACC,YAAY,CAAEz4B,EAAU,IAAI,CAACA,OAAO,AAC7F,EAACy4B,GAAgBz4B,IACjBgE,EAAM43B,qBAAqB,CAAC57B,EAAQw4B,MAAM,EAE1Cx4B,EAAQw4B,MAAM,CAACvlB,OAAO,CAAC,SAAUwoB,CAAK,CAAE1V,CAAK,EACpC0V,EAAMqB,UAAU,CAGZrB,EAAMqB,UAAU,GAAKrB,EAAMsB,UAAU,EAC1C/8B,EAAQs7B,UAAU,CAACvV,EAAM,CAACiX,IAAI,GAH9Bh9B,EAAQs7B,UAAU,CAACvV,EAAM,CAACkX,IAAI,EAKtC,GAER,CASAC,WAAY,CACR,IAAMjD,EAAe,IAAI,CAACR,MAAM,CAACQ,YAAY,CAAEnuB,EAASmuB,EAAaj2B,KAAK,CAAC8H,MAAM,CAAEqxB,EAAgBlD,EAAa/1B,OAAO,CAAC20B,MAAM,CAC1HA,EAAQuE,EAAOC,EAAO1qB,OAAOC,SAAS,CAAE0qB,EAAO,CAAC3qB,OAAOC,SAAS,CAmCpE,OAlCA9G,EAAOmH,OAAO,CAAC,SAAUtB,CAAC,EAElBA,EAAE4rB,QAAQ,EAAI,CAAC5rB,EAAE6rB,YAAY,EAEzBJ,AADJA,CAAAA,EAAQzrB,EAAE8rB,SAAS,CAAC,KAAKC,MAAM,CAACtE,GAAyB,EAC/CzwB,MAAM,GACZ00B,EAAO/D,GAAsB3nB,EAAEzN,OAAO,CAACk3B,IAAI,CAAEj2B,KAAK2J,GAAG,CAACuuB,EAAMl4B,KAAKgO,GAAG,CAACgmB,GAASiE,GAAQzrB,AAA8B,CAAA,IAA9BA,EAAEzN,OAAO,CAACy5B,eAAe,CAC3GhsB,EAAEzN,OAAO,CAAC80B,UAAU,CACpB,CAACrmB,OAAOC,SAAS,IACrB0qB,EAAOhE,GAAsB3nB,EAAEzN,OAAO,CAACi3B,IAAI,CAAEh2B,KAAKgO,GAAG,CAACmqB,EAAMpE,GAASkE,KAGjF,GAIIvE,EAFAwE,IAASC,EAEA,CAAC,CAAExwB,MAAOwwB,CAAK,EAAE,CAGjB,CACL,CAAExwB,MAAOuwB,CAAK,EACd,CAAEvwB,MAAO,AAACuwB,CAAAA,EAAOC,CAAG,EAAK,CAAE,EAC3B,CAAExwB,MAAOwwB,EAAMpD,WAAY,CAAA,CAAK,EACnC,CAGDiD,EAAcx0B,MAAM,EAAIw0B,CAAa,CAAC,EAAE,CAAC9C,MAAM,EAC/CxB,EAAOje,OAAO,GAGlBie,EAAO5lB,OAAO,CAAC,SAAU+nB,CAAK,CAAE7rB,CAAC,EACzBguB,GAAiBA,CAAa,CAAChuB,EAAE,EACjC0pB,CAAAA,CAAM,CAAC1pB,EAAE,CAAGkqB,GAAuB8D,CAAa,CAAChuB,EAAE,CAAE6rB,EAAK,CAElE,GACOnC,CACX,CASA+E,oBAAqB,CACjB,IAAM55B,EAAQ,IAAI,CAACA,KAAK,CAAE65B,EAAgB75B,EAAMy1B,MAAM,CAACv1B,OAAO,CAAE45B,EAAWD,EAAcC,QAAQ,CAAEC,EAAaF,AAAyB,eAAzBA,EAAcG,MAAM,CAAmBC,EAAiBF,EAAa/5B,EAAMy1B,MAAM,CAACwE,cAAc,CAAG,EAAGC,EAAYl6B,EAAMk6B,SAAS,CAAEC,EAAYn6B,EAAMm6B,SAAS,CAAEjD,EAAel3B,EAAM8H,MAAM,CAAC,IAAI,CAAC5H,OAAO,CAACu2B,WAAW,CAAC,CAAE2D,EAAUlD,EAAamD,aAAa,GAAI1F,EAAUxzB,KAAKm5B,IAAI,CAACF,EAAQG,SAAS,EAAGC,EAAYr5B,KAAKm5B,IAAI,CAACF,EAAQI,SAAS,EAAGC,EAAWt5B,KAAK2J,GAAG,CAACqvB,EAAWD,GAC5dQ,EAAgBhG,EAAUwC,EAAah3B,OAAO,CAACw0B,OAAO,CAiB1D,OAfIoF,GAAY,CAAE,KAAKvuB,IAAI,CAACmpB,GACxBgG,EAAiBF,GAIjBE,EAAiB,AAAED,CAAAA,EAAWR,CAAa,EAD3CvF,CAAAA,EAAUtpB,WAAWspB,EAAO,EAC8B,IACrDA,CAAAA,EAAU,IAAM,CAAA,EAGjB,CAAA,AAACqF,GAAcI,EAAYO,GAC3BR,GAAe,CAACH,GAAcG,EAC9BQ,GAAkBP,CAAS,GAC3BO,CAAAA,EAAiBF,CAAQ,GAG1B,CAAC7F,EAASxzB,KAAKm5B,IAAI,CAACI,GAAgB,AAC/C,CAKAC,aAAa7vB,CAAG,CAAEqE,CAAG,CAAE,CACnB,IAAMyrB,EAAsB,IAAI,CAACnF,MAAM,CAACv1B,OAAO,CAAC+1B,YAAY,AAC5D2E,CAAAA,EAAoBjG,OAAO,CAAG7pB,EAC9B8vB,EAAoBlG,OAAO,CAAGvlB,EAC9ByrB,EAAoB/F,MAAM,CAAG,IAAI,CAACqE,SAAS,EAC/C,CAOA2B,cAAe,CACX,IAAMpF,EAAS,IAAI,CAACA,MAAM,CAA6E2E,EAAUlD,AAAlDl3B,AAA3B,IAAI,CAACA,KAAK,CAAuB8H,MAAM,CAAC,IAAI,CAAC5H,OAAO,CAACu2B,WAAW,CAAC,CAAyB4D,aAAa,GACvIl5B,KAAKC,GAAG,CAACD,KAAKm5B,IAAI,CAD4IF,EAAQI,SAAS,EAAqB,IAAI,CAACt6B,OAAO,CAACw0B,OAAO,EAExN,IACA,IAAI,CAACiG,YAAY,CAAC,IAAI,CAACz6B,OAAO,CAACy0B,OAAO,CAAEyF,EAAQI,SAAS,EACzD/E,EAAOjN,MAAM,GAErB,CACJ,EA6CM,CAAEnlB,WAAYy3B,EAAkC,CAAE,CAAIj9B,IAEtD,CAAEyF,SAAUy3B,EAAgC,CAAE,CAAIl9B,IAElD,CAAE0F,SAAUy3B,EAAgC,CAAEt3B,WAAYu3B,EAAkC,CAAEr3B,WAAYs3B,EAAkC,CAAEv7B,KAAMw7B,EAA4B,CAAE,CAAIt9B,IAU5L,SAASu9B,GAAkB15B,CAAO,CAAExB,CAAO,CAAEwM,CAAQ,EACjD,IACIkuB,EAAqBS,EAAarF,EADlBP,EAASz1B,AAAf,IAAI,CAAiBy1B,MAAM,CAAEyB,EAAeoE,GAA5C,IAAI,GAA8E,CAE5F7F,CAAAA,GAAUA,EAAOv1B,OAAO,CAAC8B,OAAO,EAAIyzB,EAAOQ,YAAY,EACvDR,EAAOv1B,OAAO,CAAC+1B,YAAY,CAACC,UAAU,EAAIgB,GAC1C0D,EAAsBnF,EAAOQ,YAAY,CAAC/1B,OAAO,CACjDm7B,EAAc5F,EAAOQ,YAAY,CAAC2D,kBAAkB,GACpDnE,EAAOQ,YAAY,CAAC0E,YAAY,CAACU,CAAW,CAAC,EAAE,CAAEA,CAAW,CAAC,EAAE,EAE1DT,EAAoBjC,MAAM,GAC3BlD,EAAO5L,KAAK,CAAC8O,MAAM,CAAG,CAAA,EACtBlD,EAAO8F,QAAQ,CAACtsB,OAAO,CAAC,AAACZ,IAEjB2nB,AADJA,CAAAA,EAAa3nB,EAAK2nB,UAAU,EAAI,CAAC,CAAA,EAClBnM,KAAK,EAChBmM,CAAAA,EAAWnM,KAAK,CAACpjB,UAAU,CAAG,KAAK,CAAA,CAE3C,IAGJgvB,EAAOjN,MAAM,GAERoS,EAAoBjC,MAAM,GAC3B34B,AArBM,IAAI,CAqBJw7B,UAAU,GAChBx7B,AAtBM,IAAI,CAsBJy7B,IAAI,CAACxsB,OAAO,CAAC,AAACysB,IAChBA,EAAKC,QAAQ,GACbD,EAAKE,WAAW,GAEhBX,GAAmCS,EAAKG,KAAK,CAAE,SAAUC,CAAI,EACzDA,EAAKC,KAAK,CAAG,CAAA,EACbD,EAAKE,UAAU,CAAG,CAAA,CACtB,EACJ,GACAh8B,AA/BM,IAAI,CA+BJw7B,UAAU,IAEpBZ,EAAoBjC,MAAM,CAAG,CAAA,EAE7Bj3B,EAAQ/D,IAAI,CAnCF,IAAI,CAmCMuC,EAASwM,GAE7B+oB,EAAOQ,YAAY,CAAC4E,YAAY,GAEhCoB,GAAiBxG,EAAQyG,GAAgBzG,MAGzC/zB,EAAQ/D,IAAI,CA1CF,IAAI,CA0CMuC,EAASwM,GAEzB+oB,GAAUA,EAAOv1B,OAAO,CAAC8B,OAAO,EAAIyzB,EAAOQ,YAAY,GACvDR,EAAOjN,MAAM,GACbyT,GAAiBxG,EAAQyG,GAAgBzG,KAGrD,CAkCA,SAAS6F,GAA4Bt7B,CAAK,EACtC,IAAM8H,EAAS9H,EAAM8H,MAAM,CACvBqD,EAAI,EACR,KAAOA,EAAIrD,EAAOnD,MAAM,EAAE,CACtB,GAAImD,CAAM,CAACqD,EAAE,EACTrD,CAAM,CAACqD,EAAE,CAACouB,QAAQ,EAClBzxB,CAAM,CAACqD,EAAE,CAACsG,OAAO,EACjB3J,CAAM,CAACqD,EAAE,CAACsjB,SAAS,CAACC,QAAQ,CAC5B,OAAOvjB,CAEXA,CAAAA,GACJ,CACA,OAAO,EACX,CAaA,SAAS+wB,GAAgBzG,CAAM,EAC3B,IAAMG,EAAQH,EAAO8F,QAAQ,CAAEY,EAAQ,EAAE,CAAEx3B,EAASixB,EAAMjxB,MAAM,CAC5Dy3B,EAAUpG,EAAYqG,EAAalxB,EAAI,EAAGkL,EAAI,EAClD,IAAKlL,EAAI,EAAGA,EAAIxG,EAAQwG,IAOpB,GANA6qB,EAAaJ,CAAK,CAACzqB,EAAE,CAAC6qB,UAAU,EAAI,CAAC,EACrCqG,EAAc,AAACzG,CAAAA,CAAK,CAACzqB,EAAI,EAAE,EAAI,CAAC,CAAA,EAAG6qB,UAAU,EAAI,CAAC,EAC9CA,EAAWQ,WAAW,EAEtBZ,CAAAA,CAAK,CAACzqB,EAAE,CAACmxB,UAAU,CAAGtG,EAAWQ,WAAW,AAAD,EAG/CZ,CAAK,CAACzqB,EAAE,GAAKyqB,CAAK,CAACjxB,EAAS,EAAE,EAC1BqxB,EAAW12B,CAAC,GAAK+8B,EAAY/8B,CAAC,CAAE,CAIhC,IAHA68B,EAAMz2B,IAAI,CAAC,CAAEjH,OAAQ,CAAE,GACvB29B,EAAWD,CAAK,CAACA,EAAMx3B,MAAM,CAAG,EAAE,CAE1B0R,GAAKlL,EAAGkL,IACRuf,CAAK,CAACvf,EAAE,CAACimB,UAAU,CAAGF,EAAS39B,MAAM,EACrC29B,CAAAA,EAAS39B,MAAM,CAAGm3B,CAAK,CAACvf,EAAE,CAACimB,UAAU,AAAD,CAG5CF,CAAAA,EAAS9c,IAAI,CAAGnU,CACpB,CAEJ,OAAOgxB,CACX,CAIA,SAASI,GAAyBx8B,CAAC,EAC/B,IAAqBk2B,EAAeR,AAArB,IAAI,CAAwBQ,YAAY,CAAE4D,EAAgBpE,AAA1D,IAAI,CAA6Dv1B,OAAO,CAAEA,EAAU25B,EAAc5D,YAAY,CAAEuG,EAAoBlB,GAA4B7F,AAAhK,IAAI,CAAmKz1B,KAAK,EAEvLi2B,GAAgBA,EAAapB,MAAM,EAAIoB,EAAapB,MAAM,CAAClwB,MAAM,GAE7DzE,EAAQ20B,MAAM,CAAClwB,MAAM,EACrBzE,CAAAA,EAAQg2B,UAAU,CACd,CAAC,CAACh2B,EAAQ20B,MAAM,CAAC,EAAE,CAACqB,UAAU,AAAD,EAGrCT,AATW,IAAI,CASRgH,WAAW,CAACxG,IAGnBuG,GAAqB,GACrB3C,EAAc73B,OAAO,EACrB9B,EAAQ8B,OAAO,GACf9B,EAAQu2B,WAAW,CAAG+F,EACtB/G,AAhBW,IAAI,CAgBRQ,YAAY,CAAG,IAAIT,GAAwBt1B,EAhBvC,IAAI,EAiBfu1B,AAjBW,IAAI,CAiBRQ,YAAY,CAACN,WAAW,CAAC51B,EAAEw7B,QAAQ,EAElD,CAIA,SAASmB,GAAkB38B,CAAC,MAMpB48B,EAJJ,GAAI58B,EAAE68B,gBAAgB,CAClB,MAAO,CAAA,EAEX,IAAqB90B,EAAS/H,EAAEi2B,UAAU,CAAEh2B,EAAQy1B,AAArC,IAAI,CAAwCz1B,KAAK,CAAEyR,EAAU3J,EAAO2J,OAAO,CAA3E,IAAI,EAELgkB,AAFC,IAAI,CAEEQ,YAAY,GAE7BnuB,EAAO2J,OAAO,CAAG,CAACA,EAElB3J,EAAO0xB,YAAY,CAAG/nB,EAEtBkrB,EAASrB,GAA4Bt7B,IAAU,EAE3Cy1B,AAVO,IAAI,CAUJQ,YAAY,CAACxkB,OAAO,GAAKkrB,IAEhClH,AAZO,IAAI,CAYJrxB,MAAM,CAAC,CACV6xB,aAAc,CAAEj0B,QAAS26B,CAAO,CACpC,GACAlH,AAfO,IAAI,CAeJQ,YAAY,CAACxkB,OAAO,CAAGkrB,GAElC70B,EAAO2J,OAAO,CAAGA,EAEzB,CAaA,SAASwqB,GAAiBxG,CAAM,CAAE0G,CAAK,EACnC,IAAMvG,EAAQH,EAAO8F,QAAQ,CAAEzE,EAAMrB,EAAOv1B,OAAO,CAAC42B,GAAG,CACnD+F,EAAeC,EAAexG,EAAWN,EAAY+G,EAAa,EACtEnH,EAAM3mB,OAAO,CAAC,CAACZ,EAAM0T,KAEZiU,AADLA,CAAAA,EAAa3nB,EAAK2nB,UAAU,EAAI,CAAC,CAAA,EACjBnM,KAAK,GAGrBgT,EAAgB7G,EAAWnM,KAAK,CAACzC,UAAU,EAAI,EAC/C0V,EAAgB9G,EAAW12B,CAAC,EAAI,EAE5Bg3B,CAAAA,AADJA,CAAAA,EAAYjoB,EAAKioB,SAAS,AAAD,GACPQ,GAAOzoB,EAAKwmB,MAAM,IAChCyB,EAAYQ,EACR+F,EAAgBxuB,EAAKnO,OAAO,CAACw0B,OAAO,CAAG,EACvCmI,EAAgBvG,EACpBN,EAAWnM,KAAK,CAAC1kB,IAAI,CAAC,CAAEiiB,WAAYkP,CAAU,IAE9CvU,EAAQoa,CAAK,CAACY,EAAW,CAACzd,IAAI,EAC9Byd,IAEJ/G,EAAWnM,KAAK,CAAC1kB,IAAI,CAAC,CAClBsB,WAAYtF,KAAKie,KAAK,CAAC0d,EAAgBX,CAAK,CAACY,EAAW,CAACt+B,MAAM,CAAG,EACtE,GACAu3B,EAAW12B,CAAC,CAAGw9B,EAAgBX,CAAK,CAACY,EAAW,CAACt+B,MAAM,CAAG,EAC9D,EACJ,CAS6B,OA7K7B,SAAyC8Y,CAAU,CAAEylB,CAAW,EACxD9B,GAAmCH,GAAkC,yBACrED,GAAmC,CAE/BrF,OAAQ,CACJQ,aAAc9B,EAClB,CACJ,GACAgH,GAA6B5jB,EAAW9Z,SAAS,CAAE,eAAgB29B,IACnEJ,GAAiCgC,EAAa,mBAAoBT,IAClEvB,GAAiCgC,EAAa,YAAaN,IAEnE,EAoKMO,GAA2DhhC,EAAwD,OAAU,CAACihC,KAAK,CACzI,IAAIC,GAAgE3gC,EAAoBC,CAAC,CAACwgC,IAc1F,GAAM,CAAEv1B,YAAa,CAAE4H,QAAS,CAAE7R,UAAW,CAAEmM,WAAYwzB,EAAwB,CAAE,CAAE,CAAE,CAAE,CAAIh2B,IAEzF,CAAE3H,OAAQ49B,EAAkB,CAAE,CAAIx/B,GAMxC,OAAMy/B,WAAoBF,GAUtBG,SAAStyB,CAAI,CAAE,CACX,IAAMuyB,EAAe,AAACvyB,CAAAA,GAAQ,IAAI,CAACimB,MAAM,EACrC,IAAI,CAACA,MAAM,CAACmF,MAAM,EACd,CACJ,EAAKprB,EACT,GAAI,IAAI,CAACnD,MAAM,CAAC9H,KAAK,CAACy9B,QAAQ,CAAE,CAC5B,IAAM5W,EAAM,IAAI,CAACA,GAAG,IAAM,CAAC,EAAG,EAAE,CAAE,CAAE6W,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE39B,MAAAA,CAAK,CAAE,CAAG,IAAI,CAAC8H,MAAM,CACvE,OAAO9H,EAAM8E,QAAQ,CAAC9I,OAAO,CAACs8B,MAAM,CAACoF,EAAM1kB,GAAG,CAAG6N,CAAG,CAAC,EAAE,CAAG2W,EAAcG,EAAM3kB,GAAG,CAAG6N,CAAG,CAAC,EAAE,CAAG2W,EAAcA,AAAe,EAAfA,EAAkBA,AAAe,EAAfA,EACjI,CACA,OAAOL,KAAmD1/B,SAAS,CAAC8/B,QAAQ,CAAC5/B,IAAI,CAAC,IAAI,CAEtF6/B,EACJ,CACJ,CAMAH,GAAmBC,GAAY7/B,SAAS,CAAE,CACtCmgC,QAAS,CAAA,CACb,GAM6B,IAAMC,GAAsBP,GAgBnD,CAAEh6B,SAAUw6B,EAAqB,CAAE1S,KAAM2S,EAAiB,CAAE,CAAIlgC,IAEhE,CAAEiK,OAAQiC,EAAM,CAAErC,YAAa,CAAEF,OAAQ,CAAE/J,UAAWugC,EAAwB,CAAE,CAAE1uB,QAAS2uB,EAA0B,CAAE,CAAE,CAAI72B,IAE7H,CAAE7D,SAAU26B,EAAqB,CAAEhJ,SAAUiJ,EAAqB,CAAEhJ,SAAUiJ,EAAqB,CAAE5gB,MAAO6gB,EAAkB,CAAE5+B,OAAQ6+B,EAAmB,CAAEn0B,SAAUo0B,EAAqB,CAAE96B,MAAO+6B,EAAkB,CAAE9+B,KAAM++B,EAAiB,CAAE76B,WAAY86B,EAAuB,CAAE,CAAI7gC,IAUjS,SAAS8gC,KACL,IAAMC,EAAa,IAAI,CAAC5lB,GAAG,CAAE,CAAE6lB,KAAAA,CAAI,CAAEC,QAAAA,CAAO,CAAEh0B,IAAAA,CAAG,CAAE,CAAG,IAAI,CAAEksB,EAAQ,AAAC,CAAA,IAAI,CAAC7nB,GAAG,EAAI,CAAA,EAAMrE,CAAAA,GAAO,CAAA,EAC1Fi0B,EAAQ,EAAGC,EAAQJ,EAAYK,EAASL,EAAa5H,EAAOkI,EAC5DL,CAAAA,AAAS,UAATA,GAAoBA,AAAS,UAATA,CAAe,IAIvC,IAAI,CAAC/2B,MAAM,CAACmH,OAAO,CAAC,AAACnH,IACjB,GAAIA,EAAOq3B,aAAa,EAAIr3B,EAAOs3B,YAAY,GAAI,CAE/C,IAAI,CAACC,gBAAgB,CAAG,CAAA,EACxBH,EAAkB,CAAA,EAClB,IAAM7P,EAAOvnB,EAAO2xB,SAAS,CAACqF,EAAU,IAAM,KAO9C,GANIA,IACA,AAACh3B,CAAAA,EAAOw3B,OAAO,EAAIx3B,CAAK,EAAGy3B,QAAQ,CAAC,EAAG,EAAGz3B,GACtCA,EAAOw3B,OAAO,EACdx3B,CAAAA,EAAO03B,KAAK,CAAG13B,EAAOw3B,OAAO,CAACE,KAAK,AAAD,GAGtCxI,EAAQ,EAAG,CACX,IAAI7rB,EAAIkkB,EAAK1qB,MAAM,CACnB,KAAOwG,KACH,GAAIozB,GAAsBlP,CAAI,CAAClkB,EAAE,GAC7B,IAAI,CAACs0B,OAAO,EAAIpQ,CAAI,CAAClkB,EAAE,EACvBkkB,CAAI,CAAClkB,EAAE,EAAI,IAAI,CAACgE,GAAG,CAAE,CACrB,IAAMknB,EAASvuB,EAAO03B,KAAK,EAAI13B,EAAO03B,KAAK,CAACr0B,EAAE,EAAI,EAClD4zB,EAAQ59B,KAAK2J,GAAG,CAAC,AAAEukB,CAAAA,CAAI,CAAClkB,EAAE,CAAGL,CAAE,EAAKm0B,EAAU5I,EAAQ0I,GACtDC,EAAQ79B,KAAKgO,GAAG,CAAC,AAAEkgB,CAAAA,CAAI,CAAClkB,EAAE,CAAGL,CAAE,EAAKm0B,EAAU5I,EAAQ2I,EAC1D,CAER,CACJ,CACJ,GAEIE,GAAmBlI,EAAQ,GAAK,CAAC,IAAI,CAAC0I,WAAW,GACjDV,GAASJ,EACTK,GAAU,AAACL,CAAAA,EACPz9B,KAAKgO,GAAG,CAAC,EAAG4vB,GACZ59B,KAAK2J,GAAG,CAACk0B,EAAOJ,EAAU,EAAKA,EACnC,CACI,CAAC,MAAO,UAAWG,EAAM,CACzB,CAAC,MAAO,UAAWC,EAAM,CAC5B,CAAC/vB,OAAO,CAAC,AAAC6G,IACgE,KAAA,IAA5D2oB,GAAkB,IAAI,CAACv+B,OAAO,CAAC4V,CAAI,CAAC,EAAE,CAAC,CAAE,IAAI,CAACA,CAAI,CAAC,EAAE,CAAC,GAC7D,CAAA,IAAI,CAACA,CAAI,CAAC,EAAE,CAAC,EAAIA,CAAI,CAAC,EAAE,CAAGmpB,CAAK,CAExC,IAER,CAOA,SAASU,KACL,GAAM,CAAE9D,MAAAA,CAAK,CAAE+D,cAAAA,CAAa,CAAEH,QAAAA,EAAU,CAAC,CAAEI,QAAAA,EAAU,CAAC,CAAEC,WAAAA,CAAU,CAAE,CAAG,IAAI,CAAEhzB,EAAO,IAAI,CAAC5M,OAAO,CAAC4M,IAAI,CACrG,GAAI,AAACgzB,CAAAA,GAAYn7B,QAAUmI,AAAS,aAATA,CAAkB,GACzC,IAAI,CAAChF,MAAM,CAAC0jB,IAAI,CAAC,AAAC7d,GAAMA,EAAEwxB,aAAa,EAAG,CAC1C,IAAIY,EAAYH,EAAcj7B,MAAM,CACpC,KAAOo7B,KAAa,CAChB,IAAMjE,EAAOD,CAAK,CAAC+D,CAAa,CAACG,EAAU,CAAC,CAAElZ,EAAMiV,EAAKjV,GAAG,EAAI,EAC5DA,CAAAA,EAAMgZ,GAAWhZ,EAAM4Y,CAAM,GAC7B3D,EAAKrE,KAAK,EAAEwB,MAEpB,CACJ,CACJ,CAMA,MAAM+G,WAAqB/B,GAMvB,OAAO37B,QAAQ29B,CAAS,CAAE1oB,CAAU,CAAEylB,CAAW,CAAE,CAC/CkD,GAAuC3oB,EAAYylB,GAC/C0B,GAAwBZ,GAAuB,mBAC/CI,GAAsB+B,EAAW,gBAAiBtB,IAClDT,GAAsB+B,EAAW,cAAeN,IAExD,CAUA7U,QAAQte,CAAI,CAAE,CACN,CAACA,GACD,IAAI,CAAC6L,MAAM,CAAC1T,MAAM,CAAG,IAAI,CAACzE,OAAO,CAACigC,cAAc,EAEhD,IAAI,CAAC9nB,MAAM,CAACpJ,OAAO,CAAC,SAAUlH,CAAK,EAC/B,GAAM,CAAEE,QAAAA,CAAO,CAAEsoB,MAAAA,EAAQ,CAAC,CAAEC,MAAAA,EAAQ,CAAC,CAAE,CAAGzoB,EACtCE,GAAWA,EAAQzJ,KAAK,GAEnB,IAAI,CAACsnB,WAAW,EACjB7d,EAAQ9C,IAAI,CAAC,CACT5G,EAAGgyB,EACHjxB,EAAGkxB,EACHhyB,MAAO,EACPC,OAAQ,CACZ,GAEJwJ,EAAQ6iB,OAAO,CAAC,IAAI,CAACsV,aAAa,CAACr4B,GAAQ,IAAI,CAAC7H,OAAO,CAAC8lB,SAAS,EAEzE,EAAG,IAAI,CAEf,CAOAuZ,UAAW,CACP,IAAMnG,EAAQ,IAAI,CAACK,SAAS,CAAC,KAAM4G,EAAQ,IAAI,CAAC5G,SAAS,CAAC,KAAM+F,EAAQ,EAAE,CACtExmB,EAAK7N,EAAGrC,EAAOw3B,EAAY,IAAI,CAACtgC,KAAK,CAACugC,eAAe,CACnD,CAAEhG,UAAAA,CAAS,CAAEC,UAAAA,CAAS,CAAE,CAAG,IAAI,CAACH,aAAa,GAInD,GAAI,CAACiG,EAAW,CACZ,IAEIE,EAFApJ,EAAOzoB,OAAOC,SAAS,CACvBuoB,EAAO,CAACxoB,OAAOC,SAAS,CAE5B,IAAI,CAAC5O,KAAK,CAAC8H,MAAM,CAACmH,OAAO,CAAC,AAACwxB,IACvB,GAAIA,EAAYtB,aAAa,EAAIsB,EAAYrB,YAAY,GAAI,CACzD,IAAMkB,EAAY,AAACG,CAAAA,EAAYnB,OAAO,EAAImB,CAAU,EAAGC,YAAY,GAC/DJ,IAGAlJ,EAAOj2B,KAAK2J,GAAG,CAAC2zB,GAAkBrH,EAAMkJ,EAAUlJ,IAAI,EAAGkJ,EAAUlJ,IAAI,EACvED,EAAOh2B,KAAKgO,GAAG,CAACsvB,GAAkBtH,EAAMmJ,EAAUnJ,IAAI,EAAGmJ,EAAUnJ,IAAI,EACvEqJ,EAAQ,CAAA,EAEhB,CACJ,GACIA,GACAF,EAAY,CAAElJ,KAAAA,EAAMD,KAAAA,CAAK,EACzB,IAAI,CAACn3B,KAAK,CAACugC,eAAe,CAAGD,GAG7BA,EAAY,CAAElJ,KAAM,EAAGD,KAAM,CAAE,CAEvC,CAEA,IAAKhsB,EAAI,EAAG6N,EAAMogB,EAAMz0B,MAAM,CAAEwG,EAAI6N,EAAK7N,IACrCrC,EAAQswB,CAAK,CAACjuB,EAAE,CAEhBq0B,EAAM95B,IAAI,CAAC,IAAI,CAAC2xB,SAAS,CAACiJ,EAAUlJ,IAAI,CAAEkJ,EAAUnJ,IAAI,CAAEoD,EAAWC,EAAW1xB,EAAOu3B,GAASA,CAAK,CAACl1B,EAAE,EAE5G,CAAA,IAAI,CAACq0B,KAAK,CAAGA,CACjB,CAKAnI,UAAUD,CAAI,CAAED,CAAI,CAAExC,CAAO,CAAED,CAAO,CAAE5rB,CAAK,CAAE63B,CAAM,CAAE,CACnD,IAAMzgC,EAAU,IAAI,CAACA,OAAO,CAAE0gC,EAAa1gC,AAAmB,UAAnBA,EAAQ40B,MAAM,CAAcE,EAAa90B,EAAQ80B,UAAU,CAClG6L,EAAS1J,EAAOC,EAAMvQ,EAAM,GAEhC,GAAI8Z,AAAW,OAAXA,GAAmB73B,AAAU,OAAVA,EACnB,OAAO,KAEX,GAAIy1B,GAAsBz1B,GAAQ,CAU9B,GAPI5I,EAAQ60B,mBAAmB,GAC3BjsB,EAAQ3H,KAAKC,GAAG,CAAC0H,EAAQksB,GACzBmC,EAAO0J,EAAS1/B,KAAKgO,GAAG,CAACgoB,EAAOnC,EAAY7zB,KAAKC,GAAG,CAACg2B,EAAOpC,IAC5DoC,EAAO,GAIPtuB,EAAQsuB,EACR,OAAOzC,EAAU,EAAI,EAGrBkM,EAAS,GACTha,CAAAA,EAAM,AAAC/d,CAAAA,EAAQsuB,CAAG,EAAKyJ,CAAK,CAEpC,CAIA,OAHID,GAAc/Z,GAAO,GACrBA,CAAAA,EAAM1lB,KAAKuX,IAAI,CAACmO,EAAG,EAEhB1lB,KAAKm5B,IAAI,CAAC3F,EAAU9N,EAAO6N,CAAAA,EAAUC,CAAM,GAAM,CAC5D,CAMAnG,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CAIA0R,cAAcr4B,CAAK,CAAEO,CAAK,CAAE,CACxB,IAAMnD,EAAO,KAAK,CAACi7B,cAAcr4B,EAAOO,GAAQ,CAAE7J,OAAAA,EAAS,CAAC,CAAED,MAAAA,EAAQ,CAAC,CAAE,CAAG2G,EAI5E,OAAO,IAAI,CAACnF,KAAK,CAACy9B,QAAQ,CAAGa,GAAoBn5B,EAAM,CACnD5G,EAAG,AAACwJ,CAAAA,EAAMwoB,KAAK,EAAI,CAAA,EAAK/xB,EAAQ,EAChCc,EAAG,AAACyI,CAAAA,EAAMyoB,KAAK,EAAI,CAAA,EAAK/xB,EAAS,CACrC,GAAK0G,CACT,CAIAuE,aAAa3B,CAAK,CAAEO,CAAK,CAAE,CACvB,IAAMw4B,EAAgB,IAAI,CAAC5gC,OAAO,CAACgxB,MAAM,CAAE6F,EAAc+J,GAAe/J,YAAa5xB,EAAO4E,GAAOtM,SAAS,CAACiM,YAAY,CAAC/L,IAAI,CAAC,IAAI,CAAEoK,EAAOO,GAE5I,OADAnD,CAAI,CAAC,eAAe,CAAG4xB,GAAe,EAC/B5xB,CACX,CAKAgR,WAAY,CAER,KAAK,CAACA,UAAUxY,IAAI,CAAC,IAAI,EACzB,IAAI,CAAC4hC,QAAQ,GACb,IAAI,CAACwB,eAAe,EACxB,CACAA,iBAAkB,CACd,GAAM,CAAE1R,KAAAA,CAAI,CAAEnvB,QAAAA,CAAO,CAAEs/B,MAAAA,CAAK,CAAE,CAAG,IAAI,CAAE,CAAEjF,UAAAA,CAAS,CAAE,CAAG,IAAI,CAACF,aAAa,GAErElvB,EAAIkkB,EAAK1qB,MAAM,CACnB,KAAOwG,KAAK,CACR,IAAMpD,EAAQsnB,CAAI,CAAClkB,EAAE,CAAEkrB,EAASmJ,EAAQA,CAAK,CAACr0B,EAAE,CAAG,CAE7B,CAAA,MAAlB,IAAI,CAAC61B,QAAQ,EACbj5B,CAAAA,EAAMk5B,QAAQ,CAAG,AAACl5B,CAAAA,EAAM8U,CAAC,EAAI,CAAA,EAAM3c,CAAAA,EAAQ80B,UAAU,EAAI,CAAA,CAAC,EAE1DuJ,GAAsBlI,IAAWA,GAAUkE,EAAY,GAEvDxyB,EAAMmpB,MAAM,CAAGoN,GAAoBv2B,EAAMmpB,MAAM,CAAE,CAC7CmF,OAAAA,EACA73B,MAAO,EAAI63B,EACX53B,OAAQ,EAAI43B,CAChB,GAEAtuB,EAAMm5B,KAAK,CAAG,CACV3iC,EAAGwJ,EAAMwoB,KAAK,CAAG8F,EACjB/2B,EAAGyI,EAAMyoB,KAAK,CAAG6F,EACjB73B,MAAO,EAAI63B,EACX53B,OAAQ,EAAI43B,CAChB,IAIAtuB,EAAM2oB,SAAS,CAAG3oB,EAAMyoB,KAAK,CAAGzoB,EAAMm5B,KAAK,CAAG,KAAK,EACnDn5B,EAAM8R,QAAQ,CAAG,CAAA,EAEzB,CACJ,CACAwgB,eAAgB,CACZ,IAAMxvB,EAAe1J,KAAK2J,GAAG,CAAC,IAAI,CAAC9K,KAAK,CAACyK,SAAS,CAAE,IAAI,CAACzK,KAAK,CAAC0K,UAAU,EACnEy2B,EAAY,AAACx8B,IACf,IAAIy8B,EAKJ,MAJsB,UAAlB,OAAOz8B,IACPy8B,EAAY,KAAK71B,IAAI,CAAC5G,GACtBA,EAAS08B,SAAS18B,EAAQ,KAEvBy8B,EAAYv2B,EAAelG,EAAS,IAAMA,CACrD,EACM41B,EAAY4G,EAAU1C,GAAkB,IAAI,CAACv+B,OAAO,CAACy0B,OAAO,CAAE,IAG9D6F,EAAYr5B,KAAKgO,GAAG,CAACgyB,EAAU1C,GAAkB,IAAI,CAACv+B,OAAO,CAACw0B,OAAO,CAAE,QAAS6F,GACtF,MAAO,CAAEA,UAAAA,EAAWC,UAAAA,CAAU,CAClC,CACAkG,cAAe,CACX,IAAMxgC,EAAU,IAAI,CAACA,OAAO,CAAEk5B,EAAQ,IAAI,CAACK,SAAS,CAAC,KAAKC,MAAM,CAAC6E,IACjE,GAAInF,EAAMz0B,MAAM,CAAE,CACd,IAAMyyB,EAAOqH,GAAkBv+B,EAAQk3B,IAAI,CAAEiH,GAAmBD,GAAsBhF,GAAQl5B,AAA4B,CAAA,IAA5BA,EAAQy5B,eAAe,CAChHz5B,EAAQ80B,UAAU,EAAI,EACvB,CAACrmB,OAAOC,SAAS,CAAED,OAAOC,SAAS,GACjCuoB,EAAOsH,GAAkBv+B,EAAQi3B,IAAI,CAAEgH,GAAsB/E,IACnE,GAAImF,GAAsBnH,IAASmH,GAAsBpH,GACrD,MAAO,CAAEC,KAAAA,EAAMD,KAAAA,CAAK,CAE5B,CACJ,CAKAmK,aAAav5B,CAAK,CAAEw5B,CAAQ,CAAExhC,CAAC,CAAEyhC,EAAyBzD,EAAiB,CAAE0D,EAA8B1D,EAAiB,CAAE,CAqB1H,OApBAyD,EAAyB,CAAChpB,EAAIC,EAAIipB,KAC9B,IAAMC,EAASnpB,CAAE,CAACkpB,EAAe,EAAI,EAC/BE,EAASnpB,CAAE,CAACipB,EAAe,EAAI,EACjCl4B,EAAKq4B,EAAO,CAAA,EAchB,OAbIF,IAAWC,EACXp4B,EAAMgP,EAAGuJ,KAAK,CAAGtJ,EAAGsJ,KAAK,CAAGvJ,EAAKC,EAE5BkpB,EAAS,GAAKC,EAAS,GAC5Bp4B,EAAM,AAACm4B,EAAUnpB,CAAAA,EAAG0Y,MAAM,EAAEmF,QAAU,CAAA,GAClCuL,EAAUnpB,CAAAA,EAAGyY,MAAM,EAAEmF,QAAU,CAAA,EAC/B7d,EACAC,EACJopB,EAAO,CAAA,GAGPr4B,EAAMm4B,EAASC,EAASppB,EAAKC,EAE1B,CAACjP,EAAKq4B,EAAK,AACtB,EACAJ,EAA8B,CAAC3kC,EAAGiR,EAAG8zB,IAAS,CAACA,GAAS/kC,EAAIiR,GAAOjR,EAAIiR,EAChE,KAAK,CAACuzB,aAAav5B,EAAOw5B,EAAUxhC,EAAGyhC,EAAwBC,EAC1E,CACJ,CAqBAzB,GAAapP,cAAc,CAAG4N,GAAmBP,GAA2BrN,cAAc,CAAE,CACxFC,WAAY,CACRE,UAAW,WACP,GAAM,CAAEC,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAAClpB,MAAM,CAAC9H,KAAK,CACvC,CAAE6c,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAC9U,KAAK,CACxB,OAAOw2B,GAAsB1hB,GAAKmU,EAAgBnU,EAAG,IAAM,EAC/D,EACA5D,OAAQ,CAAA,EACR3a,cAAe,QACnB,EAQA6hC,eAAgB,IAmBhBjP,OAAQ,CACJ4Q,UAAW,KACXpP,UAAW,EAIXqE,YAAa,GAObV,OAAQ,KACRxH,OAAQ,CACJ0C,MAAO,CACHwQ,WAAY,CAChB,CACJ,EAsBAvK,OAAQ,QACZ,EAcA7C,QAAS,EAcTD,QAAS,MAwDTsN,cAAe,CAAA,EACfnT,OAAQ,CACJ0C,MAAO,CACHC,KAAM,CACFvmB,KAAM,CACV,CACJ,CACJ,EACAgC,QAAS,CACLokB,YAAa,yCACjB,EACAC,eAAgB,EA6ChB0D,WAAY,EACZgM,SAAU,GACd,GACA1C,GAAoB0B,GAAaviC,SAAS,CAAE,CACxCwkC,eAAgBjE,GAAyBiE,cAAc,CACvDC,WAAYnE,GACZoB,cAAe,CAAA,EACf5F,SAAU,CAAA,EACV4I,gBAAiB,CAAC,IAAI,CACtB94B,cAAe,CAAC,IAAK,IAAI,CACzBO,WAAYi0B,GACZz0B,eAAgB,CAAC,IAAK,IAAK,IAAI,CAC/BE,cAAe,CAAC,QAAS,kBAAkB,CAC3C84B,aAAc,QACdpB,SAAU,GACd,GAEA9C,GAAsB8B,GAAc,cAAe,AAACjgC,IAChD,OAAOA,EAAEO,MAAM,CAACN,KAAK,CAACugC,eAAe,AACzC,GAEArC,GAAsB8B,GAAc,SAAU,AAACjgC,IAC3C,OAAOA,EAAEO,MAAM,CAACN,KAAK,CAACugC,eAAe,AACzC,GACAn5B,IAA4DgrB,kBAAkB,CAAC,SAAU4N,IAM5D,IAAMqC,GAAuBrC,GAsHpD,CAAEt4B,YAAa,CAAEgG,IAAK,CAAEjQ,UAAW,CAAEmM,WAAY,CAAEnM,UAAW6kC,EAAa,CAAE,CAAE,CAAE,CAAE,CAAE,CAAIl7B,IAEzF,CAAE3H,OAAQ8iC,EAAqB,CAAE,CAAI1kC,GAM3C,OAAM2kC,WAAuB3E,GAMzBh1B,SAAU,CACN,MAAO,AAAkB,UAAlB,OAAO,IAAI,CAACgU,CAAC,AACxB,CACJ,CACA0lB,GAAsBC,GAAe/kC,SAAS,CAAE,CAC5CsS,aAAcuyB,GAAcvyB,YAAY,CACxCS,mBAAoB8xB,GAAc9xB,kBAAkB,AACxD,GAsBA,GAAM,CAAE9I,YAAa,CAAEgG,IAAK,CAAEjQ,UAAWglC,EAAQ,CAAE,CAAEC,SAAU,CAAEjlC,UAAWklC,EAA6B,CAAE,CAAE,CAAE,CAAIv7B,IAE7G,CAAE3H,OAAQmjC,EAAsB,CAAEn/B,MAAOo/B,EAAqB,CAAE,CAAIhlC,GAgB1E,OAAMilC,WAAwBT,GAC1Bh+B,aAAc,CAMV,KAAK,IAAIhC,WACT,IAAI,CAAC+nB,WAAW,CAAGqY,GAASrY,WAAW,AAC3C,CACA+H,YAAYpyB,CAAC,CAAEwhC,CAAQ,CAAE,CACrB,OAAO,IAAI,CAACD,YAAY,CAAC,CACrB/Q,MAAOxwB,EAAEW,MAAM,CAAG,IAAI,CAACV,KAAK,CAACW,QAAQ,CACrC6vB,MAAOzwB,EAAEa,MAAM,CAAG,IAAI,CAACZ,KAAK,CAACa,OAAO,AACxC,EAAG0gC,EAAUxhC,EACjB,CACAoW,WAAY,CACRwsB,GAA8BxsB,SAAS,CAACxY,IAAI,CAAC,IAAI,EACjD,IAAI,CAAC4hC,QAAQ,GACb,IAAI,CAACwB,eAAe,EACxB,CACJ,CAYA+B,GAAgBlS,cAAc,CAAGiS,GAAsBR,GAAoBzR,cAAc,CAAE,CAmDvF8B,UAAW,EA2GXyN,eAAgB,IAIhBnwB,OAAQ,SACR/C,QAAS,CACLokB,YAAa,yBACjB,CACJ,GACAuR,GAAuBE,GAAgBrlC,SAAS,CAAE,CAC9CqP,KAAM,YACN3D,UAAW,CAAC,YAAY,CACxBqH,mBAAoBiyB,GAASjyB,kBAAkB,CAC/CyhB,YAAa,CAAA,EAEb5oB,cAAe,CAAC,IAAI,CACpBO,WAjP2D44B,GAkP3DjT,YAAakT,GAASlT,WAAW,CACjC+D,aAAcqP,GAA8BrP,YAAY,CACxDyP,YAAa,CAAC,QAAS,QAAQ,CAC/B3T,QAASqT,GAASrT,OAAO,CACzB/rB,WAAYo/B,GAASp/B,UAAU,CAC/B8rB,WAAYsT,GAAStT,UAAU,CAC/BnH,eAAgB,CAAA,EAChBgb,YAAa,CAAA,CACjB,GACA57B,IAA4DgrB,kBAAkB,CAAC,YAAa0Q,IA6E5F,IAAMG,GAA2DhnC,EAAwD,OAAU,CAACinC,KAAK,CACzI,IAAIC,GAAgE3mC,EAAoBC,CAAC,CAACwmC,IAa1F,GAAM,CAAE3zB,QAAS,CAAE7R,UAAW,CAAEmM,WAAYw5B,EAAyB,CAAE,CAAE,CAAE,CAAG,AAACh8B,IAA6DM,WAAW,CAEjJ,CAAE8V,MAAO6lB,EAAkB,CAAE7jC,QAAS8jC,EAAoB,CAAE7jC,OAAQ8jC,EAAmB,CAAE7jC,KAAM8jC,EAAiB,CAAE,CAAI3lC,GAM5H,OAAM4lC,WAAqBL,GAOvBrzB,aAAa7P,CAAO,CAAE3B,CAAC,CAAE,CAQrB,MANI,CAAA,IAAI,CAACiT,MAAM,EAAI,AAAe,OAAf,IAAI,CAAC1I,KAAK,AAAQ,GACjC,OAAO,IAAI,CAAClK,KAAK,CAErB,KAAK,CAACmR,aAAa7P,EAAS3B,GAC5B,IAAI,CAACmlC,YAAY,CAAG,IAAI,CAAClyB,MAAM,EAAI,AAAe,OAAf,IAAI,CAAC1I,KAAK,CACzC,OAAS,QACN,IAAI,AACf,CAEA66B,mBAAoB,CAChB,IAAoB77B,EAASC,AAAf,IAAI,CAAiBD,MAAM,CAAEyc,EAAgBzc,EAAO5H,OAAO,CAAE0jC,EAAO,AAACrf,CAAAA,EAAcsf,OAAO,EAAI,CAAA,EAAK,EAAGC,EAAO,AAACvf,CAAAA,EAAcwf,OAAO,EAAI,CAAA,EAAK,EAAGrG,EAAQ51B,EAAO41B,KAAK,CAAEC,EAAQ71B,EAAO61B,KAAK,CAAEmD,EAAgB/4B,AAA3M,IAAI,CAA6M7H,OAAO,CAACgxB,MAAM,EAAIppB,EAAO5H,OAAO,CAACgxB,MAAM,CAAE8S,EAAiBl8B,EAAOm8B,sBAAsB,GACtTC,EAAeV,GAAkBz7B,AADnB,IAAI,CACqBm8B,YAAY,CAAE3f,EAAc2f,YAAY,CAAE,GAAIC,EAAW,CAC5Ft1B,GAAIw0B,GAAmBliC,KAAKie,KAAK,CAACse,EAAM1kB,GAAG,CACvC0kB,EAAMvnB,SAAS,CAACpO,AAHV,IAAI,CAGYxJ,CAAC,CAAGqlC,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,EAAM,CAACI,IAAkB,CAACtG,EAAM1kB,GAAG,CAAE,EAAI0kB,EAAM1kB,GAAG,EAC1GtK,GAAI20B,GAAmBliC,KAAKie,KAAK,CAACse,EAAM1kB,GAAG,CACvC0kB,EAAMvnB,SAAS,CAACpO,AALV,IAAI,CAKYxJ,CAAC,CAAGqlC,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,EAAM,CAACI,IAAkB,CAACtG,EAAM1kB,GAAG,CAAE,EAAI0kB,EAAM1kB,GAAG,EAC1GjK,GAAIs0B,GAAmBliC,KAAKie,KAAK,CAACue,EAAMxnB,SAAS,CAACpO,AANxC,IAAI,CAM0CzI,CAAC,CAAGwkC,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,IAAQ,CAACnG,EAAM3kB,GAAG,CAAE,EAAI2kB,EAAM3kB,GAAG,EACvHlK,GAAIu0B,GAAmBliC,KAAKie,KAAK,CAACue,EAAMxnB,SAAS,CAACpO,AAPxC,IAAI,CAO0CzI,CAAC,CAAGwkC,EAAM,CAAA,EAAO,CAAA,EAAM,CAAA,EAAO,CAAA,IAAQ,CAACnG,EAAM3kB,GAAG,CAAE,EAAI2kB,EAAM3kB,GAAG,CAC3H,EAIA,IAAK,IAAMorB,IAHQ,CAAC,CAAC,QAAS,IAAI,CAAE,CAAC,SAAU,IAAI,CAAC,CAGhB,CAChC,IAAM5mC,EAAO4mC,CAAS,CAAC,EAAE,CAAEviB,EAAYuiB,CAAS,CAAC,EAAE,CAC/C34B,EAAQoW,EAAY,IAAKnW,EAAMmW,EAAY,IACzCwiB,EAAOljC,KAAKC,GAAG,CAAC+iC,CAAQ,CAAC14B,EAAM,CAAG04B,CAAQ,CAACz4B,EAAI,EAAG0H,EAAc0tB,GAClEA,EAAcpO,SAAS,EAAI,EAAG4R,EAAUnjC,KAAKC,GAAG,CAAC+iC,CAAQ,CAAC14B,EAAM,CAAG04B,CAAQ,CAACz4B,EAAI,EAAI,EAAG64B,EAAgBzD,GAAiBA,CAAa,CAACtjC,EAAK,CAC/I,GAAI8lC,GAAqBiB,IAAkBA,EAAgBF,EAAM,CAC7D,IAAMG,EAAeD,EAAgB,EAAInxB,EAAc,CACvD+wB,CAAAA,CAAQ,CAAC14B,EAAM,CAAG64B,EAAUE,EAC5BL,CAAQ,CAACz4B,EAAI,CAAG44B,EAAUE,CAC9B,CAEIN,IACI,CAAA,AAAe,MAAdriB,GAAqB6b,EAAM+G,QAAQ,EACnC5iB,AAAc,MAAdA,GAAqB,CAAC8b,EAAM8G,QAAQ,IACrCh5B,EAAQC,EACRA,EAAMmW,EAAY,KAEtBsiB,CAAQ,CAAC14B,EAAM,EAAIy4B,EACnBC,CAAQ,CAACz4B,EAAI,EAAIw4B,EAEzB,CACA,OAAOC,CACX,CAIA5G,SAAStyB,CAAI,CAAE,CACX,GAAI,CAACA,EACD,MAAO,EAAE,CAEb,GAAM,CAAE1M,EAAAA,EAAI,CAAC,CAAEe,EAAAA,EAAI,CAAC,CAAEd,MAAAA,EAAQ,CAAC,CAAEC,OAAAA,EAAS,CAAC,CAAE,CAAG,IAAI,CAACiyB,SAAS,EAAI,CAAC,EACnE,MAAO,CACH,CAAC,IAAKnyB,EAAI0M,EAAM3L,EAAI2L,EAAK,CACzB,CAAC,IAAK1M,EAAI0M,EAAM3L,EAAIb,EAASwM,EAAK,CAClC,CAAC,IAAK1M,EAAIC,EAAQyM,EAAM3L,EAAIb,EAASwM,EAAK,CAC1C,CAAC,IAAK1M,EAAIC,EAAQyM,EAAM3L,EAAI2L,EAAK,CACjC,CAAC,IAAI,CACR,AACL,CAMApC,SAAU,CAEN,OAAQ,IAAI,CAACC,KAAK,GAAKC,KACnB,IAAI,CAACD,KAAK,GAAK,CAACC,GACxB,CACJ,CACAw6B,GAAoBE,GAAahmC,SAAS,CAAE,CACxCmL,gBAAiB,CAAA,EACjBZ,iBAAkB,CAAA,EAClB41B,QAAS,CAAA,CACb,GAoBA,GAAM,CAAEzzB,SAAUu6B,EAA8B,CAAE,CAAI7mC,IAioBhD,CAAE8mC,IAAAA,EAAG,CAAE,CAAI9mC,IAEX,CAAE2B,QAASolC,EAA8B,CAAEllC,KAAMmlC,EAA2B,CAAE,CAAIhnC,IA4ElF,CAAEiK,OAAQg9B,EAAoB,CAAEp9B,YAAa,CAAEF,OAAQu9B,EAA0B,CAAEz1B,QAAS01B,EAA2B,CAAE,CAAE,CAAI59B,IAE/H,CAAE3J,UAAW,CAAEzB,QAASipC,EAAqB,CAAE,CAAE,CAAI94B,IAErD,CAAE5I,SAAU2hC,EAAsB,CAAEzlC,OAAQ0lC,EAAoB,CAAEj7B,UAAWk7B,EAAuB,CAAEj7B,SAAUk7B,EAAsB,CAAE5hC,MAAO6hC,EAAmB,CAAE5lC,KAAM6lC,EAAkB,CAAE,CAAI1nC,IAElM,CAAE2nC,eAAgBC,EAA4B,CAAEC,WAAYC,EAAwB,CAAE,CA5B7D,CAC3BH,eAnCJ,SAAwB18B,CAAK,CAAEf,CAAK,EAChC,IAAM69B,EAAY79B,EAAMD,MAAM,CAAC89B,SAAS,CACxC,GAAIA,EAAW,CACX,IAAMC,EAAQD,EAAUE,OAAO,CAACh9B,GAAS,EAAGf,GACvCoG,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,KACNT,GAAG,CAAC,AAACC,GAAMk3B,GAA4Bz5B,WAAWuC,GAAI0zB,SAAS1zB,EAAG,MAKvE,OAJAk4B,CAAI,CAAC,EAAE,CAAGhB,AAA4C,IAA5CA,GAA4BgB,CAAI,CAAC,EAAE,CAAE,GAC1CjB,GAA+B97B,IAAWf,EAAM0J,OAAO,EACxDo0B,CAAAA,CAAI,CAAC,EAAE,CAAG,CAAA,EAEPA,CACX,CACA,MAAO,CAAC,EAAG,EAAG,EAAG,EAAE,AACvB,EAqBIH,WAhBJ,SAAoB59B,CAAM,EACtB,GAAM,CAAEi+B,OAAAA,CAAM,CAAE7R,QAAAA,CAAO,CAAE,CAAGpsB,SAC5B,AAAIi+B,GAAU7R,GACVA,EAAQ8R,SAAS,CAAC,EAAG,EAAGD,EAAOvnC,KAAK,CAAEunC,EAAOtnC,MAAM,EAShDy1B,IANHpsB,EAAOi+B,MAAM,CAAGpB,GAAIsB,aAAa,CAAC,UAClCn+B,EAAOosB,OAAO,CAAGpsB,EAAOi+B,MAAM,CAACL,UAAU,CAAC,KAAM,CAC5CQ,mBAAoB,CAAA,CACxB,IAAM,KAAK,EACJp+B,EAAOosB,OAAO,CAG7B,CAIA,CAsCA,OAAMiS,WAAsBnB,GACxB3gC,aAAc,CAMV,KAAK,IAAIhC,WACT,IAAI,CAAC+jC,QAAQ,CAAGjqB,IAChB,IAAI,CAACkqB,QAAQ,CAAGlqB,IAChB,IAAI,CAACmqB,aAAa,CAAG,CAAA,CAEzB,CASA3Z,YAAa,CACT,IAAM7kB,EAAS,IAAI,CAAEyc,EAAgBzc,EAAO5H,OAAO,CAAEqmC,EAAgBhiB,EAAcgiB,aAAa,CAAEC,EAAsBjiB,EAAc2M,MAAM,EAAI,CAAC,EACjJ,GAAIqV,EAAe,CACf,GAAM,CAAEE,MAAAA,CAAK,CAAEzmC,MAAAA,CAAK,CAAE09B,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG71B,EAAQ,CAAE28B,SAAUiC,EAAO,CAAA,CAAK,CAAE1tB,IAAKxa,CAAK,CAAE,CAAGk/B,EAAO,CAAE+G,SAAUkC,EAAO,CAAA,CAAK,CAAE3tB,IAAKva,CAAM,CAAE,CAAGk/B,EAAOiJ,EAAa,CAAEpoC,MAAAA,EAAOC,OAAAA,CAAO,EAC7K,GAAI,CAACgoC,GAAS3+B,EAAOuiB,WAAW,EAAIviB,EAAOw+B,aAAa,CAAE,CACtD,IAAMO,EAAMlB,GAAyB79B,GAAS,CAAEi+B,OAAAA,CAAM,CAAE7lC,QAAS,CAAE2jC,QAAAA,EAAU,CAAC,CAAEE,QAAAA,EAAU,CAAC,CAAE,CAAE1rB,OAAAA,CAAM,CAAEA,OAAQ,CAAE1T,OAAAA,CAAM,CAAE,CAAE,CAAGmD,EAAgC89B,EAAa5lC,EAAM4lC,SAAS,EAAI5lC,EAAM4lC,SAAS,CAAC,EAAE,CAChN,GAAIG,GAAUc,GAAOjB,EAAW,CAC5B,GAAM,CAAE96B,IAAKg8B,CAAI,CAAE33B,IAAK43B,CAAI,CAAE,CAAGrJ,EAAMsJ,WAAW,GAAI,CAAEl8B,IAAKm8B,CAAI,CAAE93B,IAAK+3B,CAAI,CAAE,CAAGvJ,EAAMqJ,WAAW,GAAIG,EAASJ,EAAOD,EAAMM,EAASF,EAAOD,EAAyBI,EAAQlmC,KAAKie,KAAK,CAACkoB,AAAgBH,EAAStD,EAAjD,EAAA,GAA2E0D,EAAQpmC,KAAKie,KAAK,CAACkoB,AAAgBF,EAASrD,EAAvH,EAAA,GAAiJ,CAACyD,EAAYC,EAAW,CAAG,CACxU,CAACJ,EAAOA,EAAQF,EAAQT,EAAM,OAAO,CACrC,CAACa,EAAOA,EAAQH,EAAQ,CAACT,EAAM,QAAQ,CAC1C,CAACj5B,GAAG,CAAC,CAAC,CAACg6B,EAAM/yB,EAAOgzB,EAAKC,EAAS,GAAMD,EACrC,AAACE,GAAO1mC,IAAI,CAACymC,EAAS,CAACF,EAClB/yB,EAASkzB,GACd,AAACA,GAAO1mC,IAAI,CAACymC,EAAS,CAACjzB,EAAQkzB,IAAOC,EAAc/B,EAAOvnC,KAAK,CAAG6oC,EAAQ,EAA6CU,EAAaD,EAAxC/B,CAAAA,EAAOtnC,MAAM,CAAG8oC,EAAQ,CAAA,EAA4CS,EAAoBC,AAR3CtjC,CAAAA,EAAS,CAAA,EAQ8CojC,EAAYG,EAAY,IAAIC,kBAAkBJ,AAAa,EAAbA,GAAiBK,EAAgB,CAAC7pC,EAAGe,IAAO6B,AACnQ,EADmQA,KAAKm5B,IAAI,CAAC,AAACwN,EAAcL,EAAWnoC,EAAI2nC,GACnUO,EAAWjpC,EAAIuoC,IACnBh/B,EAAOugC,WAAW,GAClB,IAAK,IAAIl9B,EAAI,EAAGA,EAAI48B,EAAY58B,IAAK,CACjC,IAAMpD,EAAQsQ,CAAM,CAAClX,KAAKm5B,IAAI,CAAC0N,EAAoB78B,GAAG,CAAE,CAAE5M,EAAAA,CAAC,CAAEe,EAAAA,CAAC,CAAE,CAAGyI,EACnEmgC,EAAUI,GAAG,CAAC7C,GAA6B19B,EAAMe,KAAK,CAAEf,GAAQqgC,EAAc7pC,EAAGe,GACrF,CACAunC,EAAI0B,YAAY,CAAC,IAAIC,UAAUN,EAAWJ,GAAc,EAAG,GACvDrB,EACAA,EAAMthC,IAAI,CAAC,CACP,GAAGyhC,CAAU,CACbp+B,KAAMu9B,EAAO0C,SAAS,CAAC,YAAa,EACxC,IAGA3gC,EAAO8pB,WAAW,CAAG,CAAA,EACrB9pB,EAAO2+B,KAAK,CAAGzmC,EAAM8E,QAAQ,CAAC2hC,KAAK,CAACV,EAAO0C,SAAS,CAAC,YAAa,IAC7DtjC,IAAI,CAACyhC,GACLvhC,GAAG,CAACyC,EAAO+hB,KAAK,EAE7B,CACA/hB,EAAOw+B,aAAa,CAAG,CAAA,CAC3B,KACSG,CAAAA,EAAMjoC,KAAK,GAAKA,GAASioC,EAAMhoC,MAAM,GAAKA,CAAK,GACpDgoC,EAAMthC,IAAI,CAACyhC,EAEnB,KACSJ,CAAAA,EAAoBxkC,OAAO,EAAI8F,EAAO4gC,gBAAgB,AAAD,IAC1D5D,GAAqBrnC,SAAS,CAACkvB,UAAU,CAAChvB,IAAI,CAACmK,GAC/CA,EAAOuQ,MAAM,CAACpJ,OAAO,CAAC,AAAClH,IACfA,EAAME,OAAO,GAIbF,EAAME,OAAO,CAACH,EAAO9H,KAAK,CAACsF,UAAU,CAAG,MAAQ,UAAU,CAACwC,EAAOyB,YAAY,CAACxB,IAC3D,OAAhBA,EAAMe,KAAK,EACXf,EAAME,OAAO,CAACzC,QAAQ,CAAC,yBAGnC,GAER,CAIAwhC,aAAc,CAEV,GAAM,CAAEvH,QAAAA,CAAO,CAAEI,QAAAA,CAAO,CAAE,CAAGiF,GAAqBrnC,SAAS,CAACupC,WAAW,CAClErpC,IAAI,CAAC,IAAI,CAAE,IAAI,CAAC87B,SAAS,CAAC,UAQ/B,OAPI4L,GAAuB5F,IACvB,CAAA,IAAI,CAAC4G,QAAQ,CAAG5G,CAAM,EAEtB4F,GAAuBxF,IACvB,CAAA,IAAI,CAACuG,QAAQ,CAAGvG,CAAM,EAGnBiF,GAAqBrnC,SAAS,CAACupC,WAAW,CAACrpC,IAAI,CAAC,IAAI,CAC/D,CAMAgrC,eAAetwB,CAAM,CAAEuwB,CAAU,CAAE,CAC/B,OAAO9D,GAAqBrnC,SAAS,CAACkrC,cAAc,CAAChrC,IAAI,CAAC,IAAI,CAAE0a,EAAQuwB,EAAY,CAAA,EACxF,CAMApa,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CAKAliB,MAAO,CACH,KAAK,CAACA,KAAKrK,KAAK,CAAC,IAAI,CAAEE,WACvB,IAAMnC,EAAU,IAAI,CAACA,OAAO,AAE5BA,CAAAA,EAAQ2oC,UAAU,CAAGtD,GAAmBrlC,EAAQ2oC,UAAU,CAAE3oC,EAAQ2jC,OAAO,EAAI,GAE/E,IAAI,CAAClG,KAAK,CAACmL,cAAc,CAAG5oC,EAAQ6jC,OAAO,EAAI,EAE/CkB,GAAsB8D,OAAO,CAAG9D,GAAsB3M,MAAM,CAUxDp4B,EAAQgxB,MAAM,EAAImU,GAAuBnlC,EAAQ8oC,YAAY,GAC7D9oC,CAAAA,EAAQgxB,MAAM,CAACruB,CAAC,CAAG3C,EAAQ8oC,YAAY,AAAD,CAE9C,CAIA5I,cAAcr4B,CAAK,CAAEO,CAAK,CAAE,CACxB,IAAMooB,EAAY3oB,EAAM2oB,SAAS,EAAI,CAAC,EACtC,GAAI3oB,EAAMkhC,QAAQ,CACd,MAAO,CACH1qC,EAAGwJ,EAAMwoB,KAAK,CACdjxB,EAAGyI,EAAMyoB,KAAK,AAClB,EAIJ,GAAIloB,GAASA,AAAU,WAAVA,EAAoB,CAC7B,IAAM4gC,EAAqBnhC,EAAM7H,OAAO,CAACgxB,MAAM,EAAI,CAAC,EAAGsV,EAAsB,IAAI,CAACtmC,OAAO,CAACgxB,MAAM,EAAI,CAAC,EAAGiY,EAAqB,AAAC3C,EAAoB3X,MAAM,EAAE,CAACvmB,EAAM,EAAK,CAAC,EAAG8gC,EAAoB,AAACF,EAAmBra,MAAM,EAAE,CAACvmB,EAAM,EAAK,CAAC,EAEjO9J,EAAQ,AAAC4qC,CAAAA,EAAkB5qC,KAAK,EAClC2qC,EAAmB3qC,KAAK,EACxBkyB,EAAUlyB,KAAK,EACf,CAAA,EAAM4qC,CAAAA,EAAkBC,SAAS,EACjCF,EAAmBE,SAAS,EAC5B,CAAA,EACE5qC,EAAS,AAAC2qC,CAAAA,EAAkB3qC,MAAM,EACpC0qC,EAAmB1qC,MAAM,EACzBiyB,EAAUjyB,MAAM,EAChB,CAAA,EAAM2qC,CAAAA,EAAkBE,UAAU,EAClCH,EAAmBG,UAAU,EAC7B,CAAA,EAGJ,MAAO,CAAE/qC,EADC,AAACmyB,CAAAA,EAAUnyB,CAAC,EAAI,CAAA,EAAK,AAAC,CAAA,AAACmyB,CAAAA,EAAUlyB,KAAK,EAAI,CAAA,EAAKA,CAAI,EAAK,EACtDc,EAD6D,AAACoxB,CAAAA,EAAUpxB,CAAC,EAAI,CAAA,EAAK,AAAC,CAAA,AAACoxB,CAAAA,EAAUjyB,MAAM,EAAI,CAAA,EAAKA,CAAK,EAAK,EACpHD,MAAAA,EAAOC,OAAAA,CAAO,CACjC,CACA,OAAOiyB,CACX,CAIAhnB,aAAa3B,CAAK,CAAEO,CAAK,CAAE,CACvB,IAAqBnD,EAAO2/B,GAAqBrnC,SAAS,CAACiM,YAAY,CAAC/L,IAAI,CAA7D,IAAI,CAAkEoK,EAAOO,GAAQic,EAAgBzc,AAArG,IAAI,CAAwG5H,OAAO,EAAI,CAAC,EAAGqpC,EAAczhC,AAAzI,IAAI,CAA4I9H,KAAK,CAACE,OAAO,CAACqpC,WAAW,EAAI,CAAC,EAAGC,EAAoBD,EAAYzhC,MAAM,EAAI,CAAC,EAAG2hC,EAAqBF,EAAYG,OAAO,EAAI,CAAC,EAE3Rv2B,EAAcpL,GAAO7H,QAAQiT,aACzBoR,EAAcpR,WAAW,EACzBs2B,EAAmBt2B,WAAW,EAC9Bq2B,EAAkBr2B,WAAW,CAAEC,EAAcrL,GAAO7H,QAAQkT,aAC5DmR,EAAcnR,WAAW,EACzBq2B,EAAmBr2B,WAAW,EAC9Bo2B,EAAkBp2B,WAAW,EAC7BjO,CAAI,CAAC,eAAe,CAQxB,GANAA,EAAKlG,MAAM,CAAI8I,GAAOmpB,QAAQ4Q,WAC1Bvd,EAAc2M,MAAM,EAAE4Q,WACtB3uB,GACA,IAAI,CAACvU,KAAK,CAEduG,CAAI,CAAC,eAAe,CAAGiO,EACnB9K,GAASA,AAAU,WAAVA,EAAoB,CAC7B,IAAMsmB,EAAe0W,GAAoB/gB,EAAcsK,MAAM,EAAE,CAACvmB,EAAM,CAAEic,EAAc2M,MAAM,EAAErC,QAAQ,CAACvmB,EAAM,CAAEP,GAAO7H,QAAQ2uB,QAAQ,CAACvmB,EAAM,EAAI,CAAC,EAClJnD,CAAAA,EAAKnG,IAAI,CACL4vB,EAAahwB,KAAK,EACdukC,KAAmDwG,KAAK,CAACxkC,EAAKnG,IAAI,EAAE4qC,QAAQ,CAAChb,EAAaib,UAAU,EAAI,GAAGvsC,GAAG,GACtH6H,EAAKlG,MAAM,CAAI2vB,EAAakT,SAAS,EAAI38B,EAAKlG,MAAM,AACxD,CACA,OAAOkG,CACX,CAIAgR,WAAY,CACR,GAA+C,CAAE6yB,aAAAA,CAAY,CAAE9X,OAAAA,CAAM,CAAE,CAAxCppB,AAAhB,IAAI,CAAmB5H,OAAO,CAAsCs3B,EAAStG,GAAQsG,QAAU,OAAQsS,EAAQ7E,EAAqB,CAACzN,EAAO,CAAGA,EAAS,OAAQuS,EAAkB,AAAwC,KAAxC,CAAC,SAAU,SAAS,CAACx5B,OAAO,CAACu5B,GAE9N,IAAK,IAAM/hC,KADXD,AADe,IAAI,CACZ0nB,cAAc,GACD1nB,AAFL,IAAI,CAEQuQ,MAAM,EAAE,CAC/B,IAAM8rB,EAAWp8B,EAAM47B,iBAAiB,GACpCplC,EAAI4C,KAAK2J,GAAG,CAACq5B,EAASt1B,EAAE,CAAEs1B,EAASz1B,EAAE,EAAGpP,EAAI6B,KAAK2J,GAAG,CAACq5B,EAASp1B,EAAE,CAAEo1B,EAASr1B,EAAE,EAAGtQ,EAAQ2C,KAAKgO,GAAG,CAAChO,KAAKC,GAAG,CAAC+iC,EAASz1B,EAAE,CAAGy1B,EAASt1B,EAAE,EAAG,GAAIpQ,EAAS0C,KAAKgO,GAAG,CAAChO,KAAKC,GAAG,CAAC+iC,EAASr1B,EAAE,CAAGq1B,EAASp1B,EAAE,EAAG,GAIrM,GAHAhH,EAAMkhC,QAAQ,CAAG,AAA0D,IAA1D,AAAClhC,CAAAA,EAAMmpB,MAAM,EAAEsG,QAAUA,GAAU,EAAC,EAAGjnB,OAAO,CAAC,OAG5Dw5B,EAAiB,CACjB,IAAMC,EAAW7oC,KAAKC,GAAG,CAAC5C,EAAQC,GAClCF,EAAI4C,KAAK2J,GAAG,CAACq5B,EAASt1B,EAAE,CAAEs1B,EAASz1B,EAAE,EAChClQ,CAAAA,EAAQC,EAAS,EAAIurC,EAAW,CAAA,EACrC1qC,EAAI6B,KAAK2J,GAAG,CAACq5B,EAASp1B,EAAE,CAAEo1B,EAASr1B,EAAE,EAChCtQ,CAAAA,EAAQC,EAASurC,EAAW,EAAI,CAAA,EACrCxrC,EAAQC,EAAS0C,KAAK2J,GAAG,CAACtM,EAAOC,EACrC,CACIsJ,EAAMkhC,QAAQ,EACdlhC,CAAAA,EAAMmpB,MAAM,CAAG,CAAE1yB,MAAAA,EAAOC,OAAAA,CAAO,CAAA,EAEnCsJ,EAAMwoB,KAAK,CAAGxoB,EAAMkiC,OAAO,CAAG,AAAC9F,CAAAA,EAASt1B,EAAE,CAAGs1B,EAASz1B,EAAE,AAAD,EAAK,EAC5D3G,EAAMyoB,KAAK,CAAG,AAAC2T,CAAAA,EAASp1B,EAAE,CAAGo1B,EAASr1B,EAAE,AAAD,EAAK,EAC5C/G,EAAM0oB,SAAS,CAAG,OAClB1oB,EAAM2oB,SAAS,CAAG4U,GAAoB,CAAA,EAAM,CAAE/mC,EAAAA,EAAGe,EAAAA,EAAGd,MAAAA,EAAOC,OAAAA,CAAO,EAAG,CACjE5B,EAAGooC,EAAqB,CAAC6E,EAAM,CAACvrC,EAAGe,EAAGd,EAAOC,EAAQ,CAAEoE,EAAGwiC,GAAuB2D,GAAgBA,EAAe,CAAE,EACtH,EACJ,CACA5D,GA1Be,IAAI,CA0Ba,iBACpC,CACJ,CACAe,GAAcvV,cAAc,CAAG0U,GAAoBN,GAA4BpU,cAAc,CA96B/D,CAI1B5K,UAAW,CAAA,EAQXgjB,aAAc,EAId51B,YAAa,EA8DbmzB,cAAe,CAAA,EAOftX,UAAW,UACX4B,WAAY,CACRE,UAAW,WACP,GAAM,CAAEC,gBAAAA,CAAe,CAAE,CAAG,IAAI,CAAClpB,MAAM,CAAC9H,KAAK,CACvC,CAAE8I,MAAAA,CAAK,CAAE,CAAG,IAAI,CAACf,KAAK,CAC5B,OAAO28B,GAA+B57B,GAASkoB,EAAgBloB,EAAO,IAAM,EAChF,EACAmQ,OAAQ,CAAA,EACR3a,cAAe,SACfwyB,KAAM,CAAA,EAING,SAAU,QACVvyB,QAAS,CACb,EAKAwyB,OAAQ,CAqBJsG,OAAQ,OAERnB,OAAQ,EACRyL,UAAW,KAAK,EAChBjT,OAAQ,CAIJ0C,MAAO,CAuDH2Y,cAAe,CACnB,EAIAxY,OAAQ,CA+CR,CACJ,CACJ,EACAjF,KAAM,CAAA,EAENoc,WAAY,KACZ57B,QAAS,CACLokB,YAAa,0CACjB,EACAxC,OAAQ,CACJ0C,MAAO,CAEHC,KAAM,CAAA,EASNqY,WAAY,EAChB,CACJ,EACAlY,aAAc,WAClB,GAqqBAuT,GAAuBiB,GAAe,4BAA6B,WAC/D,IAAI,CAACG,aAAa,CAAG,CAAA,EACrB,IAAI,CAAC3Z,UAAU,EACnB,GACAwY,GAAqBgB,GAAc1oC,SAAS,CAAE,CAC1C0L,UAAWU,EAA2BZ,aAAa,CAACE,SAAS,CAC7DD,SAAUW,EAA2BZ,aAAa,CAACC,QAAQ,CAC3D0oB,YAAa,CAAA,EACbG,mBAAoB,CAAA,EACpBoQ,gBAAiB,CAAC,IAAI,CACtB/4B,eAAgBS,EAA2BZ,aAAa,CAACG,cAAc,CACvEC,cAAe,CAAC,IAAK,QAAQ,CAC7BO,WAp+BuD65B,GAq+BvDrB,aAAc,QACd94B,cAAeO,EAA2BZ,aAAa,CAACK,aAAa,CAIrE24B,eAAgB8C,GAA2BtnC,SAAS,CAACwkC,cAAc,CACnE14B,aAAcM,EAA2BZ,aAAa,CAACM,YAAY,CACnEyoB,UAAW8S,GAAqBrnC,SAAS,CAACu0B,SAAS,AACvD,GACAnoB,EAA2BvH,OAAO,CAAC6jC,IACnC/+B,IAA4DgrB,kBAAkB,CAAC,UAAW+T,IAqD1F,IAAMgE,GAAKtsC,GAEXssC,CAAAA,GAAE7tC,mBAAmB,CAAGuN,EACxBsgC,GAAEhuC,QAAQ,CAAGguC,GAAEhuC,QAAQ,EAAIoS,GAC3B47B,GAAElmC,aAAa,CAAGkmC,GAAElmC,aAAa,EAt3RwBA,EAu3RzDkmC,GAAEzlB,OAAO,CAAGylB,GAAEzlB,OAAO,EA/kJ8BA,GAglJnDylB,GAAEhsB,UAAU,CAAGgsB,GAAEhsB,UAAU,EApqL2BA,GAsqLtDgsB,GAAEr8B,QAAQ,CAAGq8B,GAAEC,GAAG,CAAGD,GAAEhuC,QAAQ,CAAC2R,QAAQ,CACxCq8B,GAAEt8B,IAAI,CAAGs8B,GAAEhuC,QAAQ,CAAC0R,IAAI,CACxBs8B,GAAEnzB,OAAO,CAAGmB,GAAwBnB,OAAO,CAC3CmzB,GAAEx0B,QAAQ,CAAGwC,GAAwBxC,QAAQ,CAE7CwC,GAAwB7V,OAAO,CAAC6nC,GAAEp+B,KAAK,EACvCs+B,AA1uCgEvH,GA0uCtCxgC,OAAO,CAAC6nC,GAAEG,IAAI,CAAEH,GAAEp+B,KAAK,CAAEo+B,GAAEI,MAAM,EAC3DC,AAj4RyDvmC,EAi4RtC3B,OAAO,CAACiM,GAAgB47B,GAAEM,OAAO,CAAEN,GAAEj+B,WAAW,EACnEoiB,AA1lJmD5J,GA0lJtCpiB,OAAO,CAACiM,IAEQ,IAAMm8B,GAAY7sC,WAEtC6sC,MAAWC,OAAO"}