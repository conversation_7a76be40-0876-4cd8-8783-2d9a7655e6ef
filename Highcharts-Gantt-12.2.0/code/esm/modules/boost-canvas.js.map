{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/boost-canvas\n * @requires highcharts\n *\n * Boost module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Extensions/Boost/Boostables.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nconst Boostables = [\n    'area',\n    'areaspline',\n    'arearange',\n    'column',\n    'columnrange',\n    'bar',\n    'line',\n    'scatter',\n    'heatmap',\n    'bubble',\n    'treemap'\n];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_Boostables = (Boostables);\n\n;// ./code/es-modules/Extensions/Boost/BoostableMap.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nconst BoostableMap = {};\nBoost_Boostables.forEach((item) => {\n    BoostableMap[item] = true;\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_BoostableMap = (BoostableMap);\n\n;// ./code/es-modules/Extensions/Boost/BoostChart.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, pick, pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass, wglMode) {\n    if (wglMode && pushUnique(composed, 'Boost.Chart')) {\n        ChartClass.prototype.callbacks.push(onChartCallback);\n    }\n    return ChartClass;\n}\n/**\n * Get the clip rectangle for a target, either a series or the chart.\n * For the chart, we need to consider the maximum extent of its Y axes,\n * in case of Highcharts Stock panes and navigator.\n *\n * @private\n * @function Highcharts.Chart#getBoostClipRect\n */\nfunction getBoostClipRect(chart, target) {\n    const navigator = chart.navigator;\n    let clipBox = {\n        x: chart.plotLeft,\n        y: chart.plotTop,\n        width: chart.plotWidth,\n        height: chart.plotHeight\n    };\n    if (navigator && chart.inverted) { // #17820, #20936\n        clipBox.width += navigator.top + navigator.height;\n        if (!navigator.opposite) {\n            clipBox.x = navigator.left;\n        }\n    }\n    else if (navigator && !chart.inverted) {\n        clipBox.height = navigator.top + navigator.height - chart.plotTop;\n    }\n    // Clipping of individual series (#11906, #19039).\n    if (target.is) {\n        const { xAxis, yAxis } = target;\n        clipBox = chart.getClipBox(target);\n        if (chart.inverted) {\n            const lateral = clipBox.width;\n            clipBox.width = clipBox.height;\n            clipBox.height = lateral;\n            clipBox.x = yAxis.pos;\n            clipBox.y = xAxis.pos;\n        }\n        else {\n            clipBox.x = xAxis.pos;\n            clipBox.y = yAxis.pos;\n        }\n    }\n    if (target === chart) {\n        const verticalAxes = chart.inverted ? chart.xAxis : chart.yAxis; // #14444\n        if (verticalAxes.length <= 1) {\n            clipBox.y = Math.min(verticalAxes[0].pos, clipBox.y);\n            clipBox.height = (verticalAxes[0].pos -\n                chart.plotTop +\n                verticalAxes[0].len);\n        }\n    }\n    return clipBox;\n}\n/**\n * Returns true if the chart is in series boost mode.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart to check.\n * @return {boolean}\n * `true` if the chart is in series boost mode.\n */\nfunction isChartSeriesBoosting(chart) {\n    const allSeries = chart.series, boost = chart.boost = chart.boost || {}, boostOptions = chart.options.boost || {}, threshold = pick(boostOptions.seriesThreshold, 50);\n    if (allSeries.length >= threshold) {\n        return true;\n    }\n    if (allSeries.length === 1) {\n        return false;\n    }\n    let allowBoostForce = boostOptions.allowForce;\n    if (typeof allowBoostForce === 'undefined') {\n        allowBoostForce = true;\n        for (const axis of chart.xAxis) {\n            if (pick(axis.min, -Infinity) > pick(axis.dataMin, -Infinity) ||\n                pick(axis.max, Infinity) < pick(axis.dataMax, Infinity)) {\n                allowBoostForce = false;\n                break;\n            }\n        }\n    }\n    if (typeof boost.forceChartBoost !== 'undefined') {\n        if (allowBoostForce) {\n            return boost.forceChartBoost;\n        }\n        boost.forceChartBoost = void 0;\n    }\n    // If there are more than five series currently boosting,\n    // we should boost the whole chart to avoid running out of webgl contexts.\n    let canBoostCount = 0, needBoostCount = 0, seriesOptions;\n    for (const series of allSeries) {\n        seriesOptions = series.options;\n        // Don't count series with boostThreshold set to 0\n        // See #8950\n        // Also don't count if the series is hidden.\n        // See #9046\n        if (seriesOptions.boostThreshold === 0 ||\n            series.visible === false) {\n            continue;\n        }\n        // Don't count heatmap series as they are handled differently.\n        // In the future we should make the heatmap/treemap path compatible\n        // with forcing. See #9636.\n        if (series.type === 'heatmap') {\n            continue;\n        }\n        if (Boost_BoostableMap[series.type]) {\n            ++canBoostCount;\n        }\n        if (patientMax(series.getColumn('x', true), seriesOptions.data, \n        /// series.xData,\n        series.points) >= (seriesOptions.boostThreshold || Number.MAX_VALUE)) {\n            ++needBoostCount;\n        }\n    }\n    boost.forceChartBoost = allowBoostForce && ((\n    // Even when the series that need a boost are less than or equal\n    // to 5, force a chart boost when all series are to be boosted.\n    // See #18815\n    canBoostCount === allSeries.length &&\n        needBoostCount === canBoostCount) ||\n        needBoostCount > 5);\n    return boost.forceChartBoost;\n}\n/**\n * Take care of the canvas blitting\n * @private\n */\nfunction onChartCallback(chart) {\n    /**\n     * Convert chart-level canvas to image.\n     * @private\n     */\n    function canvasToSVG() {\n        if (chart.boost &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            chart.boost.wgl.render(chart);\n        }\n    }\n    /**\n     * Clear chart-level canvas.\n     * @private\n     */\n    function preRender() {\n        // Reset force state\n        chart.boost = chart.boost || {};\n        chart.boost.forceChartBoost = void 0;\n        chart.boosted = false;\n        // Clear the canvas\n        if (!chart.axes.some((axis) => axis.isPanning)) {\n            chart.boost.clear?.();\n        }\n        if (chart.boost.canvas &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            // Allocate\n            chart.boost.wgl.allocateBuffer(chart);\n        }\n        // See #6518 + #6739\n        if (chart.boost.markerGroup &&\n            chart.xAxis &&\n            chart.xAxis.length > 0 &&\n            chart.yAxis &&\n            chart.yAxis.length > 0) {\n            chart.boost.markerGroup.translate(chart.xAxis[0].pos, chart.yAxis[0].pos);\n        }\n    }\n    addEvent(chart, 'predraw', preRender);\n    // Use the load event rather than redraw, otherwise user load events will\n    // fire too early (#18755)\n    addEvent(chart, 'load', canvasToSVG, { order: -1 });\n    addEvent(chart, 'redraw', canvasToSVG);\n    let prevX = -1;\n    let prevY = -1;\n    addEvent(chart.pointer, 'afterGetHoverData', (e) => {\n        const series = e.hoverPoint?.series;\n        chart.boost = chart.boost || {};\n        if (chart.boost.markerGroup && series) {\n            const xAxis = chart.inverted ? series.yAxis : series.xAxis;\n            const yAxis = chart.inverted ? series.xAxis : series.yAxis;\n            if ((xAxis && xAxis.pos !== prevX) ||\n                (yAxis && yAxis.pos !== prevY)) {\n                // #21176: If the axis is changed, hide teh halo without\n                // animation  to prevent flickering of halos sharing the\n                // same marker group\n                chart.series.forEach((s) => {\n                    s.halo?.hide();\n                });\n                // #10464: Keep the marker group position in sync with the\n                // position of the hovered series axes since there is only\n                // one shared marker group when boosting.\n                chart.boost.markerGroup.translate(xAxis.pos, yAxis.pos);\n                prevX = xAxis.pos;\n                prevY = yAxis.pos;\n            }\n        }\n    });\n}\n/**\n * Tolerant max() function.\n *\n * @private\n * @param {...Array<Array<unknown>>} args\n * Max arguments\n * @return {number}\n * Max value\n */\nfunction patientMax(...args) {\n    let r = -Number.MAX_VALUE;\n    args.forEach((t) => {\n        if (typeof t !== 'undefined' &&\n            t !== null &&\n            typeof t.length !== 'undefined') {\n            if (t.length > 0) {\n                r = t.length;\n                return true;\n            }\n        }\n    });\n    return r;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BoostChart = {\n    compose,\n    getBoostClipRect,\n    isChartSeriesBoosting\n};\n/* harmony default export */ const Boost_BoostChart = (BoostChart);\n\n;// external [\"../highcharts.js\",\"default\",\"Color\"]\nconst external_highcharts_src_js_default_Color_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Color;\nvar external_highcharts_src_js_default_Color_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Color_namespaceObject);\n;// ./code/es-modules/Extensions/Boost/WGLDrawMode.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nconst WGLDrawMode = {\n    'area': 'LINES',\n    'arearange': 'LINES',\n    'areaspline': 'LINES',\n    'column': 'LINES',\n    'columnrange': 'LINES',\n    'bar': 'LINES',\n    'line': 'LINE_STRIP',\n    'scatter': 'POINTS',\n    'heatmap': 'TRIANGLES',\n    'treemap': 'TRIANGLES',\n    'bubble': 'POINTS'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLDrawMode = (WGLDrawMode);\n\n;// ./code/es-modules/Extensions/Boost/WGLShader.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { clamp, error, pick: WGLShader_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst fragmentShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    'precision highp float;',\n    'uniform vec4 fillColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform sampler2D uSampler;',\n    'uniform bool isCircle;',\n    'uniform bool hasColor;',\n    // 'vec4 toColor(float value, vec2 point) {',\n    //     'return vec4(0.0, 0.0, 0.0, 0.0);',\n    // '}',\n    'void main(void) {',\n    'vec4 col = fillColor;',\n    'vec4 tcol = texture2D(uSampler, gl_PointCoord.st);',\n    'if (hasColor) {',\n    'col = vColor;',\n    '}',\n    'if (isCircle) {',\n    'col *= tcol;',\n    'if (tcol.r < 0.0) {',\n    'discard;',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\nconst vertexShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    '#version 100',\n    '#define LN10 2.302585092994046',\n    'precision highp float;',\n    'attribute vec4 aVertexPosition;',\n    'attribute vec4 aColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform mat4 uPMatrix;',\n    'uniform float pSize;',\n    'uniform float translatedThreshold;',\n    'uniform bool hasThreshold;',\n    'uniform bool skipTranslation;',\n    'uniform float xAxisTrans;',\n    'uniform float xAxisMin;',\n    'uniform float xAxisMinPad;',\n    'uniform float xAxisPointRange;',\n    'uniform float xAxisLen;',\n    'uniform bool  xAxisPostTranslate;',\n    'uniform float xAxisOrdinalSlope;',\n    'uniform float xAxisOrdinalOffset;',\n    'uniform float xAxisPos;',\n    'uniform bool  xAxisCVSCoord;',\n    'uniform bool  xAxisIsLog;',\n    'uniform bool  xAxisReversed;',\n    'uniform float yAxisTrans;',\n    'uniform float yAxisMin;',\n    'uniform float yAxisMinPad;',\n    'uniform float yAxisPointRange;',\n    'uniform float yAxisLen;',\n    'uniform bool  yAxisPostTranslate;',\n    'uniform float yAxisOrdinalSlope;',\n    'uniform float yAxisOrdinalOffset;',\n    'uniform float yAxisPos;',\n    'uniform bool  yAxisCVSCoord;',\n    'uniform bool  yAxisIsLog;',\n    'uniform bool  yAxisReversed;',\n    'uniform bool  isBubble;',\n    'uniform bool  bubbleSizeByArea;',\n    'uniform float bubbleZMin;',\n    'uniform float bubbleZMax;',\n    'uniform float bubbleZThreshold;',\n    'uniform float bubbleMinSize;',\n    'uniform float bubbleMaxSize;',\n    'uniform bool  bubbleSizeAbs;',\n    'uniform bool  isInverted;',\n    'float bubbleRadius(){',\n    'float value = aVertexPosition.w;',\n    'float zMax = bubbleZMax;',\n    'float zMin = bubbleZMin;',\n    'float radius = 0.0;',\n    'float pos = 0.0;',\n    'float zRange = zMax - zMin;',\n    'if (bubbleSizeAbs){',\n    'value = value - bubbleZThreshold;',\n    'zMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);',\n    'zMin = 0.0;',\n    '}',\n    'if (value < zMin){',\n    'radius = bubbleZMin / 2.0 - 1.0;',\n    '} else {',\n    'pos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;',\n    'if (bubbleSizeByArea && pos > 0.0){',\n    'pos = sqrt(pos);',\n    '}',\n    'radius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;',\n    '}',\n    'return radius * 2.0;',\n    '}',\n    'float translate(float val,',\n    'float pointPlacement,',\n    'float localA,',\n    'float localMin,',\n    'float minPixelPadding,',\n    'float pointRange,',\n    'float len,',\n    'bool  cvsCoord,',\n    'bool  isLog,',\n    'bool  reversed',\n    '){',\n    'float sign = 1.0;',\n    'float cvsOffset = 0.0;',\n    'if (cvsCoord) {',\n    'sign *= -1.0;',\n    'cvsOffset = len;',\n    '}',\n    'if (isLog) {',\n    'val = log(val) / LN10;',\n    '}',\n    'if (reversed) {',\n    'sign *= -1.0;',\n    'cvsOffset -= sign * len;',\n    '}',\n    'return sign * (val - localMin) * localA + cvsOffset + ',\n    '(sign * minPixelPadding);', // ' + localA * pointPlacement * pointRange;',\n    '}',\n    'float xToPixels(float value) {',\n    'if (skipTranslation){',\n    'return value;// + xAxisPos;',\n    '}',\n    'return translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;',\n    '}',\n    'float yToPixels(float value, float checkTreshold) {',\n    'float v;',\n    'if (skipTranslation){',\n    'v = value;// + yAxisPos;',\n    '} else {',\n    'v = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;',\n    'if (v > yAxisLen) {',\n    'v = yAxisLen;',\n    '}',\n    '}',\n    'if (checkTreshold > 0.0 && hasThreshold) {',\n    'v = min(v, translatedThreshold);',\n    '}',\n    'return v;',\n    '}',\n    'void main(void) {',\n    'if (isBubble){',\n    'gl_PointSize = bubbleRadius();',\n    '} else {',\n    'gl_PointSize = pSize;',\n    '}',\n    // 'gl_PointSize = 10.0;',\n    'vColor = aColor;',\n    'if (skipTranslation && isInverted) {',\n    // If we get translated values from JS, just swap them (x, y)\n    'gl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);',\n    '} else if (isInverted) {',\n    // But when calculating pixel positions directly,\n    // swap axes and values (x, y)\n    'gl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);',\n    '} else {',\n    'gl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);',\n    '}',\n    // 'gl_Position = uPMatrix * vec4(aVertexPosition.x, aVertexPosition.y, 0.0, 1.0);',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * A static shader mimicing axis translation functions found in Core/Axis\n *\n * @private\n *\n * @param {WebGLContext} gl\n * the context in which the shader is active\n */\nclass WGLShader {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(gl) {\n        // Error stack\n        this.errors = [];\n        this.uLocations = {};\n        this.gl = gl;\n        if (gl && !this.createShader()) {\n            return void 0;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Bind the shader.\n     * This makes the shader the active one until another one is bound,\n     * or until 0 is bound.\n     * @private\n     */\n    bind() {\n        if (this.gl && this.shaderProgram) {\n            this.gl.useProgram(this.shaderProgram);\n        }\n    }\n    /**\n     * Create the shader.\n     * Loads the shader program statically defined above\n     * @private\n     */\n    createShader() {\n        const v = this.stringToProgram(vertexShader, 'vertex'), f = this.stringToProgram(fragmentShader, 'fragment'), uloc = (n) => (this.gl.getUniformLocation(this.shaderProgram, n));\n        if (!v || !f) {\n            this.shaderProgram = false;\n            this.handleErrors();\n            return false;\n        }\n        this.shaderProgram = this.gl.createProgram();\n        this.gl.attachShader(this.shaderProgram, v);\n        this.gl.attachShader(this.shaderProgram, f);\n        this.gl.linkProgram(this.shaderProgram);\n        if (!this.gl.getProgramParameter(this.shaderProgram, this.gl.LINK_STATUS)) {\n            this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram));\n            this.handleErrors();\n            this.shaderProgram = false;\n            return false;\n        }\n        this.gl.useProgram(this.shaderProgram);\n        this.gl.bindAttribLocation(this.shaderProgram, 0, 'aVertexPosition');\n        this.pUniform = uloc('uPMatrix');\n        this.psUniform = uloc('pSize');\n        this.fcUniform = uloc('fillColor');\n        this.isBubbleUniform = uloc('isBubble');\n        this.bubbleSizeAbsUniform = uloc('bubbleSizeAbs');\n        this.bubbleSizeAreaUniform = uloc('bubbleSizeByArea');\n        this.uSamplerUniform = uloc('uSampler');\n        this.skipTranslationUniform = uloc('skipTranslation');\n        this.isCircleUniform = uloc('isCircle');\n        this.isInverted = uloc('isInverted');\n        return true;\n    }\n    /**\n     * Handle errors accumulated in errors stack\n     * @private\n     */\n    handleErrors() {\n        if (this.errors.length) {\n            error('[highcharts boost] shader error - ' +\n                this.errors.join('\\n'));\n        }\n    }\n    /**\n     * String to shader program\n     * @private\n     * @param {string} str\n     * Program source\n     * @param {string} type\n     * Program type: either `vertex` or `fragment`\n     */\n    stringToProgram(str, type) {\n        const shader = this.gl.createShader(type === 'vertex' ? this.gl.VERTEX_SHADER : this.gl.FRAGMENT_SHADER);\n        this.gl.shaderSource(shader, str);\n        this.gl.compileShader(shader);\n        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {\n            this.errors.push('when compiling ' +\n                type +\n                ' shader:\\n' +\n                this.gl.getShaderInfoLog(shader));\n            return false;\n        }\n        return shader;\n    }\n    /**\n     * Destroy the shader\n     * @private\n     */\n    destroy() {\n        if (this.gl && this.shaderProgram) {\n            this.gl.deleteProgram(this.shaderProgram);\n            this.shaderProgram = false;\n        }\n    }\n    fillColorUniform() {\n        return this.fcUniform;\n    }\n    /**\n     * Get the shader program handle\n     * @private\n     * @return {WebGLProgram}\n     * The handle for the program\n     */\n    getProgram() {\n        return this.shaderProgram;\n    }\n    pointSizeUniform() {\n        return this.psUniform;\n    }\n    perspectiveUniform() {\n        return this.pUniform;\n    }\n    /**\n     * Flush\n     * @private\n     */\n    reset() {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isBubbleUniform, 0);\n            this.gl.uniform1i(this.isCircleUniform, 0);\n        }\n    }\n    /**\n     * Set bubble uniforms\n     * @private\n     * @param {Highcharts.Series} series\n     * Series to use\n     */\n    setBubbleUniforms(series, zCalcMin, zCalcMax, pixelRatio = 1) {\n        const seriesOptions = series.options;\n        let zMin = Number.MAX_VALUE, zMax = -Number.MAX_VALUE;\n        if (this.gl && this.shaderProgram && series.is('bubble')) {\n            const pxSizes = series.getPxExtremes();\n            zMin = WGLShader_pick(seriesOptions.zMin, clamp(zCalcMin, seriesOptions.displayNegative === false ?\n                seriesOptions.zThreshold : -Number.MAX_VALUE, zMin));\n            zMax = WGLShader_pick(seriesOptions.zMax, Math.max(zMax, zCalcMax));\n            this.gl.uniform1i(this.isBubbleUniform, 1);\n            this.gl.uniform1i(this.isCircleUniform, 1);\n            this.gl.uniform1i(this.bubbleSizeAreaUniform, (series.options.sizeBy !== 'width'));\n            this.gl.uniform1i(this.bubbleSizeAbsUniform, series.options\n                .sizeByAbsoluteValue);\n            this.setUniform('bubbleMinSize', pxSizes.minPxSize * pixelRatio);\n            this.setUniform('bubbleMaxSize', pxSizes.maxPxSize * pixelRatio);\n            this.setUniform('bubbleZMin', zMin);\n            this.setUniform('bubbleZMax', zMax);\n            this.setUniform('bubbleZThreshold', series.options.zThreshold);\n        }\n    }\n    /**\n     * Set the Color uniform.\n     * @private\n     * @param {Array<number>} color\n     * Array with RGBA values.\n     */\n    setColor(color) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform4f(this.fcUniform, color[0] / 255.0, color[1] / 255.0, color[2] / 255.0, color[3]);\n        }\n    }\n    /**\n     * Enable/disable circle drawing\n     * @private\n     */\n    setDrawAsCircle(flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isCircleUniform, flag ? 1 : 0);\n        }\n    }\n    /**\n     * Set if inversion state\n     * @private\n     * @param {number} flag\n     * Inversion flag\n     */\n    setInverted(flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isInverted, flag);\n        }\n    }\n    /**\n     * Set the perspective matrix\n     * @private\n     * @param {Float32List} m\n     * Matrix 4 x 4\n     */\n    setPMatrix(m) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniformMatrix4fv(this.pUniform, false, m);\n        }\n    }\n    /**\n     * Set the point size.\n     * @private\n     * @param {number} p\n     * Point size\n     */\n    setPointSize(p) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1f(this.psUniform, p);\n        }\n    }\n    /**\n     * Set skip translation\n     * @private\n     */\n    setSkipTranslation(flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.skipTranslationUniform, flag === true ? 1 : 0);\n        }\n    }\n    /**\n     * Set the active texture\n     * @private\n     * @param {number} texture\n     * Texture to activate\n     */\n    setTexture(texture) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.uSamplerUniform, texture);\n        }\n    }\n    /**\n     * Set a uniform value.\n     * This uses a hash map to cache uniform locations.\n     * @private\n     * @param {string} name\n     * Name of the uniform to set.\n     * @param {number} val\n     * Value to set\n     */\n    setUniform(name, val) {\n        if (this.gl && this.shaderProgram) {\n            const u = this.uLocations[name] = (this.uLocations[name] ||\n                this.gl.getUniformLocation(this.shaderProgram, name));\n            this.gl.uniform1f(u, val);\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLShader = (WGLShader);\n\n;// ./code/es-modules/Extensions/Boost/WGLVertexBuffer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Vertex Buffer abstraction.\n * A vertex buffer is a set of vertices which are passed to the GPU\n * in a single call.\n *\n * @private\n * @class\n * @name WGLVertexBuffer\n *\n * @param {WebGLContext} gl\n * Context in which to create the buffer.\n * @param {WGLShader} shader\n * Shader to use.\n */\nclass WGLVertexBuffer {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(gl, shader, dataComponents\n    /* , type */\n    ) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.buffer = false;\n        this.iterator = 0;\n        this.preAllocated = false;\n        this.vertAttribute = false;\n        this.components = dataComponents || 2;\n        this.dataComponents = dataComponents;\n        this.gl = gl;\n        this.shader = shader;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Note about pre-allocated buffers:\n     *     - This is slower for charts with many series\n     * @private\n     */\n    allocate(size) {\n        this.iterator = -1;\n        this.preAllocated = new Float32Array(size * 4);\n    }\n    /**\n     * Bind the buffer\n     * @private\n     */\n    bind() {\n        if (!this.buffer) {\n            return false;\n        }\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        // gl.enableVertexAttribArray(vertAttribute);\n        // gl.bindBuffer(gl.ARRAY_BUFFER, buffer);\n        this.gl.vertexAttribPointer(this.vertAttribute, this.components, this.gl.FLOAT, false, 0, 0);\n        /// gl.enableVertexAttribArray(vertAttribute);\n    }\n    /**\n     * Build the buffer\n     * @private\n     * @param {Array<number>} dataIn\n     * Zero padded array of indices\n     * @param {string} attrib\n     * Name of the Attribute to bind the buffer to\n     * @param {number} dataComponents\n     * Number of components per. indice\n     */\n    build(dataIn, attrib, dataComponents) {\n        let farray;\n        this.data = dataIn || [];\n        if ((!this.data || this.data.length === 0) && !this.preAllocated) {\n            /// console.error('trying to render empty vbuffer');\n            this.destroy();\n            return false;\n        }\n        this.components = dataComponents || this.components;\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n        }\n        if (!this.preAllocated) {\n            farray = new Float32Array(this.data);\n        }\n        this.buffer = this.gl.createBuffer();\n        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffer);\n        this.gl.bufferData(this.gl.ARRAY_BUFFER, this.preAllocated || farray, this.gl.STATIC_DRAW);\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        this.vertAttribute = this.gl\n            .getAttribLocation(this.shader.getProgram(), attrib);\n        this.gl.enableVertexAttribArray(this.vertAttribute);\n        // Trigger cleanup\n        farray = false;\n        return true;\n    }\n    /**\n     * @private\n     */\n    destroy() {\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n            this.buffer = false;\n            this.vertAttribute = false;\n        }\n        this.iterator = 0;\n        this.components = this.dataComponents || 2;\n        this.data = [];\n    }\n    /**\n     * Adds data to the pre-allocated buffer.\n     * @private\n     * @param {number} x\n     * X data\n     * @param {number} y\n     * Y data\n     * @param {number} a\n     * A data\n     * @param {number} b\n     * B data\n     */\n    push(x, y, a, b) {\n        if (this.preAllocated) { // && iterator <= preAllocated.length - 4) {\n            this.preAllocated[++this.iterator] = x;\n            this.preAllocated[++this.iterator] = y;\n            this.preAllocated[++this.iterator] = a;\n            this.preAllocated[++this.iterator] = b;\n        }\n    }\n    /**\n     * Render the buffer\n     *\n     * @private\n     * @param {number} from\n     * Start indice.\n     * @param {number} to\n     * End indice.\n     * @param {WGLDrawModeValue} drawMode\n     * Draw mode.\n     */\n    render(from, to, drawMode) {\n        const length = this.preAllocated ?\n            this.preAllocated.length : this.data.length;\n        if (!this.buffer) {\n            return false;\n        }\n        if (!length) {\n            return false;\n        }\n        if (!from || from > length || from < 0) {\n            from = 0;\n        }\n        if (!to || to > length) {\n            to = length;\n        }\n        if (from >= to) {\n            return false;\n        }\n        drawMode = drawMode || 'POINTS';\n        this.gl.drawArrays(this.gl[drawMode], from / this.components, (to - from) / this.components);\n        return true;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLVertexBuffer = (WGLVertexBuffer);\n\n;// ./code/es-modules/Extensions/Boost/WGLRenderer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { parse: color } = (external_highcharts_src_js_default_Color_default());\n\nconst { doc, win } = (external_highcharts_src_js_default_default());\n\nconst { isNumber, isObject, merge, objectEach, pick: WGLRenderer_pick } = (external_highcharts_src_js_default_default());\n\n\n\n/* *\n *\n *  Constants\n *\n * */\n// Things to draw as \"rectangles\" (i.e lines)\nconst asBar = {\n    'column': true,\n    'columnrange': true,\n    'bar': true,\n    'area': true,\n    'areaspline': true,\n    'arearange': true\n};\nconst asCircle = {\n    'scatter': true,\n    'bubble': true\n};\nconst contexts = [\n    'webgl',\n    'experimental-webgl',\n    'moz-webgl',\n    'webkit-3d'\n];\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * Main renderer. Used to render series.\n *\n * Notes to self:\n * - May be able to build a point map by rendering to a separate canvas and\n *   encoding values in the color data.\n * - Need to figure out a way to transform the data quicker\n *\n * @private\n *\n * @param {Function} postRenderCallback\n */\nclass WGLRenderer {\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns an orthographic perspective matrix\n     * @private\n     * @param {number} width\n     * the width of the viewport in pixels\n     * @param {number} height\n     * the height of the viewport in pixels\n     */\n    static orthoMatrix(width, height) {\n        const near = 0, far = 1;\n        return [\n            2 / width, 0, 0, 0,\n            0, -(2 / height), 0, 0,\n            0, 0, -2 / (far - near), 0,\n            -1, 1, -(far + near) / (far - near), 1\n        ];\n    }\n    /**\n     * @private\n     */\n    static seriesPointCount(series) {\n        let isStacked, xData, s;\n        if (series.boosted) {\n            isStacked = !!series.options.stacking;\n            xData = ((series.getColumn('x').length ?\n                series.getColumn('x') :\n                void 0) ||\n                series.options.xData ||\n                series.getColumn('x', true));\n            s = (isStacked ? series.data : (xData || series.options.data))\n                .length;\n            if (series.type === 'treemap') {\n                s *= 12;\n            }\n            else if (series.type === 'heatmap') {\n                s *= 6;\n            }\n            else if (asBar[series.type]) {\n                s *= 2;\n            }\n            return s;\n        }\n        return 0;\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(postRenderCallback) {\n        /**\n         * The data to render - array of coordinates.\n         * Repeating sequence of [x, y, checkThreshold, pointSize].\n         */\n        this.data = [];\n        // Height of our viewport in pixels\n        this.height = 0;\n        // Is it inited?\n        this.isInited = false;\n        // The marker data\n        this.markerData = [];\n        // The series stack\n        this.series = [];\n        // Texture handles\n        this.textureHandles = {};\n        // Width of our viewport in pixels\n        this.width = 0;\n        this.postRenderCallback = postRenderCallback;\n        this.settings = {\n            pointSize: 1,\n            lineWidth: 1,\n            fillColor: '#AA00AA',\n            useAlpha: true,\n            usePreallocated: false,\n            useGPUTranslations: false,\n            debug: {\n                timeRendering: false,\n                timeSeriesProcessing: false,\n                timeSetup: false,\n                timeBufferCopy: false,\n                timeKDTree: false,\n                showSkipSummary: false\n            }\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    getPixelRatio() {\n        return this.settings.pixelRatio || win.devicePixelRatio || 1;\n    }\n    /**\n     * @private\n     */\n    setOptions(options) {\n        // The pixelRatio defaults to 1. This is an antipattern, we should\n        // refactor the Boost options to include an object of default options as\n        // base for the merge, like other components.\n        if (!('pixelRatio' in options)) {\n            options.pixelRatio = 1;\n        }\n        merge(true, this.settings, options);\n    }\n    /**\n     * Allocate a float buffer to fit all series\n     * @private\n     */\n    allocateBuffer(chart) {\n        const vbuffer = this.vbuffer;\n        let s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        chart.series.forEach((series) => {\n            if (series.boosted) {\n                s += WGLRenderer.seriesPointCount(series);\n            }\n        });\n        vbuffer && vbuffer.allocate(s);\n    }\n    /**\n     * @private\n     */\n    allocateBufferForSingleSeries(series) {\n        const vbuffer = this.vbuffer;\n        let s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        if (series.boosted) {\n            s = WGLRenderer.seriesPointCount(series);\n        }\n        vbuffer && vbuffer.allocate(s);\n    }\n    /**\n     * Clear the depth and color buffer\n     * @private\n     */\n    clear() {\n        const gl = this.gl;\n        gl && gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);\n    }\n    /**\n     * Push data for a single series\n     * This calculates additional vertices and transforms the data to be\n     * aligned correctly in memory\n     * @private\n     */\n    pushSeriesData(series, inst) {\n        const data = this.data, settings = this.settings, vbuffer = this.vbuffer, isRange = (series.pointArrayMap &&\n            series.pointArrayMap.join(',') === 'low,high'), { chart, options, sorted, xAxis, yAxis } = series, isStacked = !!options.stacking, rawData = options.data, xExtremes = series.xAxis.getExtremes(), \n        // Taking into account the offset of the min point #19497\n        xMin = xExtremes.min - (series.xAxis.minPointOffset || 0), xMax = xExtremes.max + (series.xAxis.minPointOffset || 0), yExtremes = series.yAxis.getExtremes(), yMin = yExtremes.min - (series.yAxis.minPointOffset || 0), yMax = yExtremes.max + (series.yAxis.minPointOffset || 0), xData = (series.getColumn('x').length ? series.getColumn('x') : void 0) || options.xData || series.getColumn('x', true), yData = (series.getColumn('y').length ? series.getColumn('y') : void 0) || options.yData || series.getColumn('y', true), zData = (series.getColumn('z').length ? series.getColumn('z') : void 0) || options.zData || series.getColumn('z', true), useRaw = !xData || xData.length === 0, \n        /// threshold = options.threshold,\n        // yBottom = chart.yAxis[0].getThreshold(threshold),\n        // hasThreshold = isNumber(threshold),\n        // colorByPoint = series.options.colorByPoint,\n        // This is required for color by point, so make sure this is\n        // uncommented if enabling that\n        // colorIndex = 0,\n        // Required for color axis support\n        // caxis,\n        connectNulls = options.connectNulls, \n        // For some reason eslint/TypeScript don't pick up that this is\n        // actually used: --- bre1470: it is never read, just set\n        // maxVal: (number|undefined), // eslint-disable-line no-unused-vars\n        points = series.points || false, sdata = isStacked ? series.data : (xData || rawData), closestLeft = { x: Number.MAX_VALUE, y: 0 }, closestRight = { x: -Number.MAX_VALUE, y: 0 }, cullXThreshold = 1, cullYThreshold = 1, chartDestroyed = typeof chart.index === 'undefined', drawAsBar = asBar[series.type], zoneAxis = options.zoneAxis || 'y', zones = options.zones || false, threshold = options.threshold, pixelRatio = this.getPixelRatio();\n        let plotWidth = series.chart.plotWidth, lastX = false, lastY = false, minVal, scolor, \n        //\n        skipped = 0, hadPoints = false, \n        // The following are used in the builder while loop\n        x, y, d, z, i = -1, px = false, nx = false, low, nextInside = false, prevInside = false, pcolor = false, isXInside = false, isYInside = true, firstPoint = true, zoneColors, zoneDefColor = false, gapSize = false, vlen = 0;\n        if (options.boostData && options.boostData.length > 0) {\n            return;\n        }\n        if (options.gapSize) {\n            gapSize = options.gapUnit !== 'value' ?\n                options.gapSize * series.closestPointRange :\n                options.gapSize;\n        }\n        if (zones) {\n            zoneColors = [];\n            zones.forEach((zone, i) => {\n                if (zone.color) {\n                    const zoneColor = color(zone.color).rgba;\n                    zoneColor[0] /= 255.0;\n                    zoneColor[1] /= 255.0;\n                    zoneColor[2] /= 255.0;\n                    zoneColors[i] = zoneColor;\n                    if (!zoneDefColor && typeof zone.value === 'undefined') {\n                        zoneDefColor = zoneColor;\n                    }\n                }\n            });\n            if (!zoneDefColor) {\n                const seriesColor = ((series.pointAttribs && series.pointAttribs().fill) ||\n                    series.color);\n                zoneDefColor = color(seriesColor).rgba;\n                zoneDefColor[0] /= 255.0;\n                zoneDefColor[1] /= 255.0;\n                zoneDefColor[2] /= 255.0;\n            }\n        }\n        if (chart.inverted) {\n            plotWidth = series.chart.plotHeight;\n        }\n        series.closestPointRangePx = Number.MAX_VALUE;\n        /**\n         * Push color to color buffer - need to do this per vertex.\n         * @private\n         */\n        const pushColor = (color) => {\n            if (color) {\n                inst.colorData.push(color[0]);\n                inst.colorData.push(color[1]);\n                inst.colorData.push(color[2]);\n                inst.colorData.push(color[3]);\n            }\n        };\n        /**\n         * Push a vertice to the data buffer.\n         * @private\n         */\n        const vertice = (x, y, checkTreshold, pointSize = 1, color) => {\n            pushColor(color);\n            // Correct for pixel ratio\n            if (pixelRatio !== 1 && (!settings.useGPUTranslations ||\n                inst.skipTranslation)) {\n                x *= pixelRatio;\n                y *= pixelRatio;\n                pointSize *= pixelRatio;\n            }\n            if (settings.usePreallocated && vbuffer) {\n                vbuffer.push(x, y, checkTreshold ? 1 : 0, pointSize);\n                vlen += 4;\n            }\n            else {\n                data.push(x);\n                data.push(y);\n                data.push(checkTreshold ? pixelRatio : 0);\n                data.push(pointSize);\n            }\n        };\n        /**\n         * @private\n         */\n        const closeSegment = () => {\n            if (inst.segments.length) {\n                inst.segments[inst.segments.length - 1].to = data.length || vlen;\n            }\n        };\n        /**\n         * Create a new segment for the current set.\n         * @private\n         */\n        const beginSegment = () => {\n            // Insert a segment on the series.\n            // A segment is just a start indice.\n            // When adding a segment, if one exists from before, it should\n            // set the previous segment's end\n            if (inst.segments.length &&\n                inst.segments[inst.segments.length - 1].from === (data.length || vlen)) {\n                return;\n            }\n            closeSegment();\n            inst.segments.push({\n                from: data.length || vlen\n            });\n        };\n        /**\n         * Push a rectangle to the data buffer.\n         * @private\n         */\n        const pushRect = (x, y, w, h, color) => {\n            pushColor(color);\n            vertice(x + w, y);\n            pushColor(color);\n            vertice(x, y);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x + w, y + h);\n            pushColor(color);\n            vertice(x + w, y);\n        };\n        // Create the first segment\n        beginSegment();\n        // Special case for point shapes\n        if (points && points.length > 0) {\n            // If we're doing points, we assume that the points are already\n            // translated, so we skip the shader translation.\n            inst.skipTranslation = true;\n            // Force triangle draw mode\n            inst.drawMode = 'TRIANGLES';\n            // We don't have a z component in the shader, so we need to sort.\n            if (points[0].node && points[0].node.levelDynamic) {\n                points.sort((a, b) => {\n                    if (a.node) {\n                        if (a.node.levelDynamic >\n                            b.node.levelDynamic) {\n                            return 1;\n                        }\n                        if (a.node.levelDynamic <\n                            b.node.levelDynamic) {\n                            return -1;\n                        }\n                    }\n                    return 0;\n                });\n            }\n            points.forEach((point) => {\n                const plotY = point.plotY;\n                let swidth, pointAttr;\n                if (typeof plotY !== 'undefined' &&\n                    !isNaN(plotY) &&\n                    point.y !== null &&\n                    point.shapeArgs) {\n                    let { x = 0, y = 0, width = 0, height = 0 } = point.shapeArgs;\n                    pointAttr = chart.styledMode ?\n                        point.series\n                            .colorAttribs(point) :\n                        pointAttr = point.series.pointAttribs(point);\n                    swidth = pointAttr['stroke-width'] || 0;\n                    // Handle point colors\n                    pcolor = color(pointAttr.fill).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                    // So there are two ways of doing this. Either we can\n                    // create a rectangle of two triangles, or we can do a\n                    // point and use point size. Latter is faster, but\n                    // only supports squares. So we're doing triangles.\n                    // We could also use one color per. vertice to get\n                    // better color interpolation.\n                    // If there's stroking, we do an additional rect\n                    if (series.is('treemap')) {\n                        swidth = swidth || 1;\n                        scolor = color(pointAttr.stroke).rgba;\n                        scolor[0] /= 255.0;\n                        scolor[1] /= 255.0;\n                        scolor[2] /= 255.0;\n                        pushRect(x, y, width, height, scolor);\n                        swidth /= 2;\n                    }\n                    // } else {\n                    //     swidth = 0;\n                    // }\n                    // Fixes issues with inverted heatmaps (see #6981). The root\n                    // cause is that the coordinate system is flipped. In other\n                    // words, instead of [0,0] being top-left, it's\n                    // bottom-right. This causes a vertical and horizontal flip\n                    // in the resulting image, making it rotated 180 degrees.\n                    if (series.is('heatmap') && chart.inverted) {\n                        x = xAxis.len - x;\n                        y = yAxis.len - y;\n                        width = -width;\n                        height = -height;\n                    }\n                    pushRect(x + swidth, y + swidth, width - (swidth * 2), height - (swidth * 2), pcolor);\n                }\n            });\n            closeSegment();\n            return;\n        }\n        // Extract color axis\n        // (chart.axes || []).forEach((a): void => {\n        //     if (H.ColorAxis && a instanceof H.ColorAxis) {\n        //         caxis = a;\n        //     }\n        // });\n        while (i < sdata.length - 1) {\n            d = sdata[++i];\n            if (typeof d === 'undefined') {\n                continue;\n            }\n            /// px = x = y = z = nx = low = false;\n            // chartDestroyed = typeof chart.index === 'undefined';\n            // nextInside = prevInside = pcolor = isXInside = isYInside = false;\n            // drawAsBar = asBar[series.type];\n            if (chartDestroyed) {\n                break;\n            }\n            // Uncomment this to enable color by point.\n            // This currently left disabled as the charts look really ugly\n            // when enabled and there's a lot of points.\n            // Leaving in for the future (tm).\n            // if (colorByPoint) {\n            //     colorIndex = ++colorIndex %\n            //         series.chart.options.colors.length;\n            //     pcolor = toRGBAFast(series.chart.options.colors[colorIndex]);\n            //     pcolor[0] /= 255.0;\n            //     pcolor[1] /= 255.0;\n            //     pcolor[2] /= 255.0;\n            // }\n            // Handle the point.color option (#5999)\n            const pointOptions = rawData && rawData[i];\n            if (!useRaw && isObject(pointOptions, true)) {\n                if (pointOptions.color) {\n                    pcolor = color(pointOptions.color).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                }\n            }\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1][0];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1][0];\n                }\n                if (d.length >= 3) {\n                    z = d[2];\n                    if (d[2] > inst.zMax) {\n                        inst.zMax = d[2];\n                    }\n                    if (d[2] < inst.zMin) {\n                        inst.zMin = d[2];\n                    }\n                }\n            }\n            else {\n                x = d;\n                y = yData?.[i];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1];\n                }\n                if (zData && zData.length) {\n                    z = zData[i];\n                    if (zData[i] > inst.zMax) {\n                        inst.zMax = zData[i];\n                    }\n                    if (zData[i] < inst.zMin) {\n                        inst.zMin = zData[i];\n                    }\n                }\n            }\n            if (!connectNulls && (x === null || y === null)) {\n                beginSegment();\n                continue;\n            }\n            if (nx && nx >= xMin && nx <= xMax) {\n                nextInside = true;\n            }\n            if (px && px >= xMin && px <= xMax) {\n                prevInside = true;\n            }\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = series.getColumn('low', true)?.[i];\n                y = series.getColumn('high', true)?.[i] || 0;\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n            }\n            if (yMin !== null &&\n                typeof yMin !== 'undefined' &&\n                yMax !== null &&\n                typeof yMax !== 'undefined') {\n                isYInside = y >= yMin && y <= yMax;\n            }\n            // Do not render points outside the zoomed range (#19701)\n            if (!sorted && !isYInside) {\n                continue;\n            }\n            if (x > xMax && closestRight.x < xMax) {\n                closestRight.x = x;\n                closestRight.y = y;\n            }\n            if (x < xMin && closestLeft.x > xMin) {\n                closestLeft.x = x;\n                closestLeft.y = y;\n            }\n            if (y === null && connectNulls) {\n                continue;\n            }\n            // Cull points outside the extremes\n            // Continue if `sdata` has only one point as `nextInside` asserts\n            // whether the next point exists and will thus be false. (#22194)\n            if (y === null || (!isYInside && sdata.length > 1 &&\n                !nextInside && !prevInside)) {\n                beginSegment();\n                continue;\n            }\n            // The first point before and first after extremes should be\n            // rendered (#9962, 19701)\n            // Make sure series with a single point are rendered (#21897)\n            if (sorted && ((nx >= xMin || x >= xMin) &&\n                (px <= xMax || x <= xMax)) ||\n                !sorted && ((x >= xMin) && (x <= xMax))) {\n                isXInside = true;\n            }\n            if (!isXInside && !nextInside && !prevInside) {\n                continue;\n            }\n            if (gapSize && x - px > gapSize) {\n                beginSegment();\n            }\n            // Note: Boost requires that zones are sorted!\n            if (zones) {\n                let zoneColor;\n                zones.some((// eslint-disable-line no-loop-func\n                zone, i) => {\n                    const last = zones[i - 1];\n                    if (zoneAxis === 'x') {\n                        if (typeof zone.value !== 'undefined' &&\n                            x <= zone.value) {\n                            if (zoneColors[i] &&\n                                (!last || x >= last.value)) {\n                                zoneColor = zoneColors[i];\n                            }\n                            return true;\n                        }\n                        return false;\n                    }\n                    if (typeof zone.value !== 'undefined' && y <= zone.value) {\n                        if (zoneColors[i] &&\n                            (!last || y >= last.value)) {\n                            zoneColor = zoneColors[i];\n                        }\n                        return true;\n                    }\n                    return false;\n                });\n                pcolor = zoneColor || zoneDefColor || pcolor;\n            }\n            // Skip translations - temporary floating point fix\n            if (!settings.useGPUTranslations) {\n                inst.skipTranslation = true;\n                x = xAxis.toPixels(x, true);\n                y = yAxis.toPixels(y, true);\n                // Make sure we're not drawing outside of the chart area.\n                // See #6594. Update: this is no longer required as far as I\n                // can tell. Leaving in for git blame in case there are edge\n                // cases I've not found. Having this in breaks #10246.\n                // if (y > plotHeight) {\n                // y = plotHeight;\n                // }\n                if (x > plotWidth) {\n                    // If this is rendered as a point, just skip drawing it\n                    // entirely, as we're not dependant on lineTo'ing to it.\n                    // See #8197\n                    if (inst.drawMode === 'POINTS') {\n                        continue;\n                    }\n                    // Having this here will clamp markers and make the angle\n                    // of the last line wrong. See 9166.\n                    // x = plotWidth;\n                }\n            }\n            // No markers on out of bounds things.\n            // Out of bound things are shown if and only if the next\n            // or previous point is inside the rect.\n            if (inst.hasMarkers && isXInside) {\n                /// x = Highcharts.correctFloat(\n                //     Math.min(Math.max(-1e5, xAxis.translate(\n                //         x,\n                //         0,\n                //         0,\n                //         0,\n                //         1,\n                //         0.5,\n                //         false\n                //     )), 1e5)\n                // );\n                if (lastX !== false) {\n                    series.closestPointRangePx = Math.min(series.closestPointRangePx, Math.abs(x - lastX));\n                }\n            }\n            // If the last _drawn_ point is closer to this point than the\n            // threshold, skip it. Shaves off 20-100ms in processing.\n            if (!settings.useGPUTranslations &&\n                !settings.usePreallocated &&\n                (lastX && Math.abs(x - lastX) < cullXThreshold) &&\n                (lastY && Math.abs(y - lastY) < cullYThreshold)) {\n                if (settings.debug.showSkipSummary) {\n                    ++skipped;\n                }\n                continue;\n            }\n            if (drawAsBar) {\n                minVal = low || 0;\n                if (low === false || typeof low === 'undefined') {\n                    if (y < 0) {\n                        minVal = y;\n                    }\n                    else {\n                        minVal = 0;\n                    }\n                }\n                if ((!isRange && !isStacked) ||\n                    yAxis.logarithmic // #16850\n                ) {\n                    minVal = Math.max(threshold === null ? yMin : threshold, // #5268\n                    yMin); // #8731\n                }\n                if (!settings.useGPUTranslations) {\n                    minVal = yAxis.toPixels(minVal, true);\n                }\n                // Need to add an extra point here\n                vertice(x, minVal, 0, 0, pcolor);\n            }\n            // Do step line if enabled.\n            // Draws an additional point at the old Y at the new X.\n            // See #6976.\n            if (options.step && !firstPoint) {\n                vertice(x, lastY, 0, 2, pcolor);\n            }\n            vertice(x, y, 0, series.type === 'bubble' ? (z || 1) : 2, pcolor);\n            // Uncomment this to support color axis.\n            // if (caxis) {\n            //     pcolor = color(caxis.toColor(y)).rgba;\n            //     inst.colorData.push(color[0] / 255.0);\n            //     inst.colorData.push(color[1] / 255.0);\n            //     inst.colorData.push(color[2] / 255.0);\n            //     inst.colorData.push(color[3]);\n            // }\n            lastX = x;\n            lastY = y;\n            hadPoints = true;\n            firstPoint = false;\n        }\n        if (settings.debug.showSkipSummary) {\n            console.log('skipped points:', skipped); // eslint-disable-line no-console\n        }\n        const pushSupplementPoint = (point, atStart) => {\n            if (!settings.useGPUTranslations) {\n                inst.skipTranslation = true;\n                point.x = xAxis.toPixels(point.x, true);\n                point.y = yAxis.toPixels(point.y, true);\n            }\n            // We should only do this for lines, and we should ignore markers\n            // since there's no point here that would have a marker.\n            if (atStart) {\n                this.data = [point.x, point.y, 0, 2].concat(this.data);\n                return;\n            }\n            vertice(point.x, point.y, 0, 2);\n        };\n        if (!hadPoints &&\n            connectNulls !== false &&\n            series.drawMode === 'line_strip') {\n            if (closestLeft.x < Number.MAX_VALUE) {\n                // We actually need to push this *before* the complete buffer.\n                pushSupplementPoint(closestLeft, true);\n            }\n            if (closestRight.x > -Number.MAX_VALUE) {\n                pushSupplementPoint(closestRight);\n            }\n        }\n        closeSegment();\n    }\n    /**\n     * Push a series to the renderer\n     * If we render the series immediately, we don't have to loop later\n     * @private\n     * @param {Highchart.Series} s\n     * The series to push.\n     */\n    pushSeries(s) {\n        const markerData = this.markerData, series = this.series, settings = this.settings;\n        if (series.length > 0) {\n            if (series[series.length - 1].hasMarkers) {\n                series[series.length - 1].markerTo = markerData.length;\n            }\n        }\n        if (settings.debug.timeSeriesProcessing) {\n            console.time('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n        const obj = {\n            segments: [],\n            markerFrom: markerData.length,\n            // Push RGBA values to this array to use per. point coloring.\n            // It should be 0-padded, so each component should be pushed in\n            // succession.\n            colorData: [],\n            series: s,\n            zMin: Number.MAX_VALUE,\n            zMax: -Number.MAX_VALUE,\n            hasMarkers: s.options.marker ?\n                s.options.marker.enabled !== false :\n                false,\n            showMarkers: true,\n            drawMode: Boost_WGLDrawMode[s.type] || 'LINE_STRIP'\n        };\n        if (s.index >= series.length) {\n            series.push(obj);\n        }\n        else {\n            series[s.index] = obj;\n        }\n        // Add the series data to our buffer(s)\n        this.pushSeriesData(s, obj);\n        if (settings.debug.timeSeriesProcessing) {\n            console.timeEnd('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n    }\n    /**\n     * Flush the renderer.\n     * This removes pushed series and vertices.\n     * Should be called after clearing and before rendering\n     * @private\n     */\n    flush() {\n        const vbuffer = this.vbuffer;\n        this.data = [];\n        this.markerData = [];\n        this.series = [];\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n    }\n    /**\n     * Pass x-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The x-axis.\n     */\n    setXAxis(axis) {\n        const shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        const pixelRatio = this.getPixelRatio();\n        shader.setUniform('xAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('xAxisMin', axis.min);\n        shader.setUniform('xAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('xAxisPointRange', axis.pointRange);\n        shader.setUniform('xAxisLen', axis.len * pixelRatio);\n        shader.setUniform('xAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('xAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('xAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('xAxisReversed', (!!axis.reversed));\n    }\n    /**\n     * Pass y-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The y-axis.\n     */\n    setYAxis(axis) {\n        const shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        const pixelRatio = this.getPixelRatio();\n        shader.setUniform('yAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('yAxisMin', axis.min);\n        shader.setUniform('yAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('yAxisPointRange', axis.pointRange);\n        shader.setUniform('yAxisLen', axis.len * pixelRatio);\n        shader.setUniform('yAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('yAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('yAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('yAxisReversed', (!!axis.reversed));\n    }\n    /**\n     * Set the translation threshold\n     * @private\n     * @param {boolean} has\n     * Has threshold flag.\n     * @param {numbe} translation\n     * The threshold.\n     */\n    setThreshold(has, translation) {\n        const shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        shader.setUniform('hasThreshold', has);\n        shader.setUniform('translatedThreshold', translation);\n    }\n    /**\n     * Render the data\n     * This renders all pushed series.\n     * @private\n     */\n    renderChart(chart) {\n        const gl = this.gl, settings = this.settings, shader = this.shader, vbuffer = this.vbuffer;\n        const pixelRatio = this.getPixelRatio();\n        if (chart) {\n            this.width = chart.chartWidth * pixelRatio;\n            this.height = chart.chartHeight * pixelRatio;\n        }\n        else {\n            return false;\n        }\n        const height = this.height, width = this.width;\n        if (!gl || !shader || !width || !height) {\n            return false;\n        }\n        if (settings.debug.timeRendering) {\n            console.time('gl rendering'); // eslint-disable-line no-console\n        }\n        gl.canvas.width = width;\n        gl.canvas.height = height;\n        shader.bind();\n        gl.viewport(0, 0, width, height);\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n        if (settings.lineWidth > 1 && !(external_highcharts_src_js_default_default()).isMS) {\n            gl.lineWidth(settings.lineWidth);\n        }\n        if (vbuffer) {\n            vbuffer.build(this.data, 'aVertexPosition', 4);\n            vbuffer.bind();\n        }\n        shader.setInverted(chart.inverted);\n        // Render the series\n        this.series.forEach((s, si) => {\n            const options = s.series.options, shapeOptions = options.marker, lineWidth = (typeof options.lineWidth !== 'undefined' ?\n                options.lineWidth :\n                1), threshold = options.threshold, hasThreshold = isNumber(threshold), yBottom = s.series.yAxis.getThreshold(threshold), translatedThreshold = yBottom, showMarkers = WGLRenderer_pick(options.marker ? options.marker.enabled : null, s.series.xAxis.isRadial ? true : null, s.series.closestPointRangePx >\n                2 * ((options.marker ?\n                    options.marker.radius :\n                    10) || 10)), shapeTexture = this.textureHandles[(shapeOptions && shapeOptions.symbol) ||\n                s.series.symbol] || this.textureHandles.circle;\n            let sindex, cbuffer, fillColor, scolor = [];\n            if (s.segments.length === 0 ||\n                s.segments[0].from === s.segments[0].to) {\n                return;\n            }\n            if (shapeTexture.isReady) {\n                gl.bindTexture(gl.TEXTURE_2D, shapeTexture.handle);\n                shader.setTexture(shapeTexture.handle);\n            }\n            if (chart.styledMode) {\n                if (s.series.markerGroup === s.series.chart.boost?.markerGroup) {\n                    // Create a temporary markerGroup to get the fill color\n                    delete s.series.markerGroup;\n                    s.series.markerGroup = s.series.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n                    fillColor = s.series.markerGroup.getStyle('fill');\n                    s.series.markerGroup.destroy();\n                    s.series.markerGroup = s.series.chart.boost?.markerGroup;\n                }\n                else {\n                    fillColor = s.series.markerGroup?.getStyle('fill');\n                }\n            }\n            else {\n                fillColor =\n                    (s.drawMode === 'POINTS' && // #14260\n                        s.series.pointAttribs &&\n                        s.series.pointAttribs().fill) ||\n                        s.series.color;\n                if (options.colorByPoint) {\n                    fillColor = s.series.chart.options.colors[si];\n                }\n            }\n            if (s.series.fillOpacity && options.fillOpacity) {\n                fillColor = new (external_highcharts_src_js_default_Color_default())(fillColor).setOpacity(WGLRenderer_pick(options.fillOpacity, 1.0)).get();\n            }\n            scolor = color(fillColor).rgba;\n            if (!settings.useAlpha) {\n                scolor[3] = 1.0;\n            }\n            // Blending\n            if (options.boostBlending === 'add') {\n                gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n                gl.blendEquation(gl.FUNC_ADD);\n            }\n            else if (options.boostBlending === 'mult' ||\n                options.boostBlending === 'multiply') {\n                gl.blendFunc(gl.DST_COLOR, gl.ZERO);\n            }\n            else if (options.boostBlending === 'darken') {\n                gl.blendFunc(gl.ONE, gl.ONE);\n                gl.blendEquation(gl.FUNC_MIN);\n            }\n            else {\n                /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n                // gl.blendEquation(gl.FUNC_ADD);\n                gl.blendFuncSeparate(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA);\n            }\n            shader.reset();\n            // If there are entries in the colorData buffer, build and bind it.\n            if (s.colorData.length > 0) {\n                shader.setUniform('hasColor', 1);\n                cbuffer = new Boost_WGLVertexBuffer(gl, shader);\n                cbuffer.build(\n                // The color array attribute for vertex is assigned from 0,\n                // so it needs to be shifted to be applied to further\n                // segments. #18858\n                Array(s.segments[0].from).concat(s.colorData), 'aColor', 4);\n                cbuffer.bind();\n            }\n            else {\n                // Set the hasColor uniform to false (0) when the series\n                // contains no colorData buffer points. #18858\n                shader.setUniform('hasColor', 0);\n                // #15869, a buffer with fewer points might already be bound by\n                // a different series/chart causing out of range errors\n                gl.disableVertexAttribArray(gl.getAttribLocation(shader.getProgram(), 'aColor'));\n            }\n            // Set series specific uniforms\n            shader.setColor(scolor);\n            this.setXAxis(s.series.xAxis);\n            this.setYAxis(s.series.yAxis);\n            this.setThreshold(hasThreshold, translatedThreshold);\n            if (s.drawMode === 'POINTS') {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 0.5) * 2 * pixelRatio);\n            }\n            // If set to true, the toPixels translations in the shader\n            // is skipped, i.e it's assumed that the value is a pixel coord.\n            shader.setSkipTranslation(s.skipTranslation);\n            if (s.series.type === 'bubble') {\n                shader.setBubbleUniforms(s.series, s.zMin, s.zMax, pixelRatio);\n            }\n            shader.setDrawAsCircle(asCircle[s.series.type] || false);\n            if (!vbuffer) {\n                return;\n            }\n            // Do the actual rendering\n            // If the line width is < 0, skip rendering of the lines. See #7833.\n            if (lineWidth > 0 || s.drawMode !== 'LINE_STRIP') {\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, s.drawMode);\n                }\n            }\n            if (s.hasMarkers && showMarkers) {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 5) * 2 * pixelRatio);\n                shader.setDrawAsCircle(true);\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, 'POINTS');\n                }\n            }\n        });\n        if (settings.debug.timeRendering) {\n            console.timeEnd('gl rendering'); // eslint-disable-line no-console\n        }\n        if (this.postRenderCallback) {\n            this.postRenderCallback(this);\n        }\n        this.flush();\n    }\n    /**\n     * Render the data when ready\n     * @private\n     */\n    render(chart) {\n        this.clear();\n        if (chart.renderer.forExport) {\n            return this.renderChart(chart);\n        }\n        if (this.isInited) {\n            this.renderChart(chart);\n        }\n        else {\n            setTimeout(() => {\n                this.render(chart);\n            }, 1);\n        }\n    }\n    /**\n     * Set the viewport size in pixels\n     * Creates an orthographic perspective matrix and applies it.\n     * @private\n     */\n    setSize(width, height) {\n        const shader = this.shader;\n        // Skip if there's no change, or if we have no valid shader\n        if (!shader || (this.width === width && this.height === height)) {\n            return;\n        }\n        this.width = width;\n        this.height = height;\n        shader.bind();\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n    }\n    /**\n     * Init OpenGL\n     * @private\n     */\n    init(canvas, noFlush) {\n        const settings = this.settings;\n        this.isInited = false;\n        if (!canvas) {\n            return false;\n        }\n        if (settings.debug.timeSetup) {\n            console.time('gl setup'); // eslint-disable-line no-console\n        }\n        for (let i = 0; i < contexts.length; ++i) {\n            this.gl = canvas.getContext(contexts[i], {\n            //    /premultipliedAlpha: false\n            });\n            if (this.gl) {\n                break;\n            }\n        }\n        const gl = this.gl;\n        if (gl) {\n            if (!noFlush) {\n                this.flush();\n            }\n        }\n        else {\n            return false;\n        }\n        gl.enable(gl.BLEND);\n        /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n        gl.disable(gl.DEPTH_TEST);\n        /// gl.depthMask(gl.FALSE);\n        gl.depthFunc(gl.LESS);\n        const shader = this.shader = new Boost_WGLShader(gl);\n        if (!shader) {\n            // We need to abort, there's no shader context\n            return false;\n        }\n        this.vbuffer = new Boost_WGLVertexBuffer(gl, shader);\n        const createTexture = (name, fn) => {\n            const props = {\n                isReady: false,\n                texture: doc.createElement('canvas'),\n                handle: gl.createTexture()\n            }, ctx = props.texture.getContext('2d');\n            this.textureHandles[name] = props;\n            props.texture.width = 512;\n            props.texture.height = 512;\n            ctx.mozImageSmoothingEnabled = false;\n            ctx.webkitImageSmoothingEnabled = false;\n            ctx.msImageSmoothingEnabled = false;\n            ctx.imageSmoothingEnabled = false;\n            ctx.strokeStyle = 'rgba(255, 255, 255, 0)';\n            ctx.fillStyle = '#FFF';\n            fn(ctx);\n            try {\n                gl.activeTexture(gl.TEXTURE0);\n                gl.bindTexture(gl.TEXTURE_2D, props.handle);\n                /// gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, true);\n                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, props.texture);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);\n                /// gl.generateMipmap(gl.TEXTURE_2D);\n                gl.bindTexture(gl.TEXTURE_2D, null);\n                props.isReady = true;\n            }\n            catch (e) {\n                // Silent error\n            }\n        };\n        // Circle shape\n        createTexture('circle', (ctx) => {\n            ctx.beginPath();\n            ctx.arc(256, 256, 256, 0, 2 * Math.PI);\n            ctx.stroke();\n            ctx.fill();\n        });\n        // Square shape\n        createTexture('square', (ctx) => {\n            ctx.fillRect(0, 0, 512, 512);\n        });\n        // Diamond shape\n        createTexture('diamond', (ctx) => {\n            ctx.beginPath();\n            ctx.moveTo(256, 0);\n            ctx.lineTo(512, 256);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(0, 256);\n            ctx.lineTo(256, 0);\n            ctx.fill();\n        });\n        // Triangle shape\n        createTexture('triangle', (ctx) => {\n            ctx.beginPath();\n            ctx.moveTo(0, 512);\n            ctx.lineTo(256, 0);\n            ctx.lineTo(512, 512);\n            ctx.lineTo(0, 512);\n            ctx.fill();\n        });\n        // Triangle shape (rotated)\n        createTexture('triangle-down', (ctx) => {\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(512, 0);\n            ctx.lineTo(0, 0);\n            ctx.fill();\n        });\n        this.isInited = true;\n        if (settings.debug.timeSetup) {\n            console.timeEnd('gl setup'); // eslint-disable-line no-console\n        }\n        return true;\n    }\n    /**\n     * @private\n     * @todo use it\n     */\n    destroy() {\n        const gl = this.gl, shader = this.shader, vbuffer = this.vbuffer;\n        this.flush();\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n        if (shader) {\n            shader.destroy();\n        }\n        if (gl) {\n            objectEach(this.textureHandles, (texture) => {\n                if (texture.handle) {\n                    gl.deleteTexture(texture.handle);\n                }\n            });\n            gl.canvas.width = 1;\n            gl.canvas.height = 1;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Boost_WGLRenderer = (WGLRenderer);\n\n;// ./code/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items = []) {\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice(start, deleteCount, ...items),\n                array: column\n            };\n        }\n        const Constructor = Object.getPrototypeOf(column)\n            .constructor;\n        const removed = column[removedAsSubarray ? 'subarray' : 'slice'](start, start + deleteCount);\n        const newLength = column.length - deleteCount + items.length;\n        const result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nconst { setLength, splice } = Data_ColumnUtils;\n\nconst { fireEvent, objectEach: DataTableCore_objectEach, uniqueKey } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nclass DataTableCore {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    constructor(options = {}) {\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        let rowCount = 0;\n        DataTableCore_objectEach(options.columns || {}, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    applyRowCount(rowCount) {\n        this.rowCount = rowCount;\n        DataTableCore_objectEach(this.columns, (column, columnName) => {\n            if (column.length !== rowCount) {\n                this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    }\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    deleteRows(rowIndex, rowCount = 1) {\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            let length = 0;\n            DataTableCore_objectEach(this.columns, (column, columnName) => {\n                this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length = column.length;\n            });\n            this.rowCount = length;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex, rowCount });\n        this.versionTag = uniqueKey();\n    }\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    getColumn(columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    }\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    getColumns(columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return (columnNames || Object.keys(this.columns)).reduce((columns, columnName) => {\n            columns[columnName] = this.columns[columnName];\n            return columns;\n        }, {});\n    }\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    getRow(rowIndex, columnNames) {\n        return (columnNames || Object.keys(this.columns)).map((key) => this.columns[key]?.[rowIndex]);\n    }\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumn(columnName, column = [], rowIndex = 0, eventDetail) {\n        this.setColumns({ [columnName]: column }, rowIndex, eventDetail);\n    }\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    setColumns(columns, rowIndex, eventDetail) {\n        let rowCount = this.rowCount;\n        DataTableCore_objectEach(columns, (column, columnName) => {\n            this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    }\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    setRow(row, rowIndex = this.rowCount, insert, eventDetail) {\n        const { columns } = this, indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        DataTableCore_objectEach(row, (cellValue, columnName) => {\n            let column = columns[columnName] ||\n                eventDetail?.addColumns !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!eventDetail?.silent) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/Extensions/Boost/BoostSeries.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { getBoostClipRect: BoostSeries_getBoostClipRect, isChartSeriesBoosting: BoostSeries_isChartSeriesBoosting } = Boost_BoostChart;\n\nconst { getOptions } = (external_highcharts_src_js_default_default());\n\nconst { composed: BoostSeries_composed, doc: BoostSeries_doc, noop, win: BoostSeries_win } = (external_highcharts_src_js_default_default());\n\nconst { addEvent: BoostSeries_addEvent, destroyObjectProperties, error: BoostSeries_error, extend, fireEvent: BoostSeries_fireEvent, isArray, isNumber: BoostSeries_isNumber, pick: BoostSeries_pick, pushUnique: BoostSeries_pushUnique, wrap, defined } = (external_highcharts_src_js_default_default());\n\n\n/* *\n *\n *  Constants\n *\n * */\nconst CHUNK_SIZE = 3000;\n/* *\n *\n *  Variables\n *\n * */\nlet index, mainCanvas;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction allocateIfNotSeriesBoosting(renderer, series) {\n    const boost = series.boost;\n    if (renderer &&\n        boost &&\n        boost.target &&\n        boost.canvas &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        renderer.allocateBufferForSingleSeries(series);\n    }\n}\n/**\n * Return true if ths boost.enabled option is true\n *\n * @private\n * @param {Highcharts.Chart} chart\n * The chart\n * @return {boolean}\n * True, if boost is enabled.\n */\nfunction boostEnabled(chart) {\n    return BoostSeries_pick((chart &&\n        chart.options &&\n        chart.options.boost &&\n        chart.options.boost.enabled), true);\n}\n/**\n * @private\n */\nfunction BoostSeries_compose(SeriesClass, seriesTypes, PointClass, wglMode) {\n    if (BoostSeries_pushUnique(BoostSeries_composed, 'Boost.Series')) {\n        const plotOptions = getOptions().plotOptions, seriesProto = SeriesClass.prototype;\n        BoostSeries_addEvent(SeriesClass, 'destroy', onSeriesDestroy);\n        BoostSeries_addEvent(SeriesClass, 'hide', onSeriesHide);\n        if (wglMode) {\n            seriesProto.renderCanvas = seriesRenderCanvas;\n        }\n        wrap(seriesProto, 'getExtremes', wrapSeriesGetExtremes);\n        wrap(seriesProto, 'processData', wrapSeriesProcessData);\n        wrap(seriesProto, 'searchPoint', wrapSeriesSearchPoint);\n        [\n            'translate',\n            'generatePoints',\n            'drawTracker',\n            'drawPoints',\n            'render'\n        ].forEach((method) => wrapSeriesFunctions(seriesProto, seriesTypes, method));\n        wrap(PointClass.prototype, 'firePointEvent', function (proceed, type, e) {\n            if (type === 'click' && this.series.boosted) {\n                const point = e.point;\n                if ((point.dist || point.distX) >= (point.series.options.marker?.radius ?? 10)) {\n                    return;\n                }\n            }\n            return proceed.apply(this, [].slice.call(arguments, 1));\n        });\n        // Set default options\n        Boost_Boostables.forEach((type) => {\n            const typePlotOptions = plotOptions[type];\n            if (typePlotOptions) {\n                typePlotOptions.boostThreshold = 5000;\n                typePlotOptions.boostData = [];\n                seriesTypes[type].prototype.fillOpacity = true;\n            }\n        });\n        if (wglMode) {\n            const { area: AreaSeries, areaspline: AreaSplineSeries, bubble: BubbleSeries, column: ColumnSeries, heatmap: HeatmapSeries, scatter: ScatterSeries, treemap: TreemapSeries } = seriesTypes;\n            if (AreaSeries) {\n                extend(AreaSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (AreaSplineSeries) {\n                extend(AreaSplineSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (BubbleSeries) {\n                const bubbleProto = BubbleSeries.prototype;\n                // By default, the bubble series does not use the KD-tree, so\n                // force it to.\n                delete bubbleProto.buildKDTree;\n                // SeriesTypes.bubble.prototype.directTouch = false;\n                // Needed for markers to work correctly\n                wrap(bubbleProto, 'markerAttribs', function (proceed) {\n                    if (this.boosted) {\n                        return false;\n                    }\n                    return proceed.apply(this, [].slice.call(arguments, 1));\n                });\n            }\n            if (ColumnSeries) {\n                extend(ColumnSeries.prototype, {\n                    fill: true,\n                    sampling: true\n                });\n            }\n            if (ScatterSeries) {\n                ScatterSeries.prototype.fill = true;\n            }\n            // We need to handle heatmaps separately, since we can't perform the\n            // size/color calculations in the shader easily.\n            // @todo This likely needs future optimization.\n            [HeatmapSeries, TreemapSeries].forEach((SC) => {\n                if (SC) {\n                    wrap(SC.prototype, 'drawPoints', wrapSeriesDrawPoints);\n                }\n            });\n        }\n    }\n    return SeriesClass;\n}\n/**\n * Create a canvas + context and attach it to the target\n *\n * @private\n * @function createAndAttachRenderer\n *\n * @param {Highcharts.Chart} chart\n * the chart\n *\n * @param {Highcharts.Series} series\n * the series\n *\n * @return {Highcharts.BoostGLRenderer}\n * the canvas renderer\n */\nfunction createAndAttachRenderer(chart, series) {\n    const ChartClass = chart.constructor, targetGroup = chart.seriesGroup || series.group, alpha = 1;\n    let width = chart.chartWidth, height = chart.chartHeight, target = chart, foSupported = typeof SVGForeignObjectElement !== 'undefined', hasClickHandler = false;\n    if (BoostSeries_isChartSeriesBoosting(chart)) {\n        target = chart;\n    }\n    else {\n        target = series;\n        hasClickHandler = Boolean(series.options.events?.click ||\n            series.options.point?.events?.click);\n    }\n    const boost = target.boost =\n        target.boost ||\n            {};\n    // Support for foreignObject is flimsy as best.\n    // IE does not support it, and Chrome has a bug which messes up\n    // the canvas draw order.\n    // As such, we force the Image fallback for now, but leaving the\n    // actual Canvas path in-place in case this changes in the future.\n    foSupported = false;\n    if (!mainCanvas) {\n        mainCanvas = BoostSeries_doc.createElement('canvas');\n    }\n    if (!boost.target) {\n        boost.canvas = mainCanvas;\n        // Fall back to image tag if foreignObject isn't supported,\n        // or if we're exporting.\n        if (chart.renderer.forExport || !foSupported) {\n            target.renderTarget = boost.target = chart.renderer.image('', 0, 0, width, height)\n                .addClass('highcharts-boost-canvas')\n                .add(targetGroup);\n            boost.clear = function () {\n                boost.target.attr({\n                    // Insert a blank pixel (#17182)\n                    /* eslint-disable-next-line max-len*/\n                    href: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='\n                });\n            };\n            boost.copy = function () {\n                boost.resize();\n                boost.target.attr({\n                    href: boost.canvas.toDataURL('image/png')\n                });\n            };\n        }\n        else {\n            boost.targetFo = chart.renderer\n                .createElement('foreignObject')\n                .add(targetGroup);\n            target.renderTarget = boost.target =\n                BoostSeries_doc.createElement('canvas');\n            boost.targetCtx = boost.target.getContext('2d');\n            boost.targetFo.element.appendChild(boost.target);\n            boost.clear = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n            };\n            boost.copy = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n                boost.targetCtx.drawImage(boost.canvas, 0, 0);\n            };\n        }\n        boost.resize = function () {\n            width = chart.chartWidth;\n            height = chart.chartHeight;\n            (boost.targetFo || boost.target)\n                .attr({\n                x: 0,\n                y: 0,\n                width,\n                height\n            })\n                .css({\n                pointerEvents: hasClickHandler ? void 0 : 'none',\n                mixedBlendMode: 'normal',\n                opacity: alpha\n            })\n                .addClass(hasClickHandler ? 'highcharts-tracker' : '');\n            if (target instanceof ChartClass) {\n                target.boost?.markerGroup?.translate(chart.plotLeft, chart.plotTop);\n            }\n        };\n        boost.clipRect = chart.renderer.clipRect();\n        (boost.targetFo || boost.target)\n            .attr({\n            // Set the z index of the boost target to that of the last\n            // series using it. This logic is not perfect, as it will not\n            // handle interleaved series with boost enabled or disabled. But\n            // it will cover the most common use case of one or more\n            // successive boosted or non-boosted series (#9819).\n            zIndex: series.options.zIndex\n        });\n        if (target instanceof ChartClass) {\n            target.boost.markerGroup = target.renderer\n                .g()\n                .add(targetGroup)\n                .translate(series.xAxis.pos, series.yAxis.pos);\n        }\n    }\n    boost.canvas.width = width;\n    boost.canvas.height = height;\n    if (boost.clipRect) {\n        const box = BoostSeries_getBoostClipRect(chart, target), \n        // When using panes, the image itself must be clipped. When not\n        // using panes, it is better to clip the target group, because then\n        // we preserve clipping on touch- and mousewheel zoom preview.\n        clippedElement = (box.width === chart.clipBox.width &&\n            box.height === chart.clipBox.height) ? targetGroup :\n            (boost.targetFo || boost.target);\n        boost.clipRect.attr(box);\n        clippedElement?.clip(boost.clipRect);\n    }\n    boost.resize();\n    boost.clear();\n    if (!boost.wgl) {\n        boost.wgl = new Boost_WGLRenderer((wgl) => {\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.time('buffer copy'); // eslint-disable-line no-console\n            }\n            boost.copy();\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.timeEnd('buffer copy'); // eslint-disable-line no-console\n            }\n        });\n        if (!boost.wgl.init(boost.canvas)) {\n            // The OGL renderer couldn't be inited. This likely means a shader\n            // error as we wouldn't get to this point if there was no WebGL\n            // support.\n            BoostSeries_error('[highcharts boost] - unable to init WebGL renderer');\n        }\n        boost.wgl.setOptions(chart.options.boost || {});\n        if (target instanceof ChartClass) {\n            boost.wgl.allocateBuffer(chart);\n        }\n    }\n    boost.wgl.setSize(width, height);\n    return boost.wgl;\n}\n/**\n * If implemented in the core, parts of this can probably be\n * shared with other similar methods in Highcharts.\n * @private\n * @function Highcharts.Series#destroyGraphics\n */\nfunction destroyGraphics(series) {\n    const points = series.points;\n    if (points) {\n        let point, i;\n        for (i = 0; i < points.length; i = i + 1) {\n            point = points[i];\n            if (point && point.destroyElements) {\n                point.destroyElements(); // #7557\n            }\n        }\n    }\n    ['graph', 'area', 'tracker'].forEach((prop) => {\n        const seriesProp = series[prop];\n        if (seriesProp) {\n            series[prop] = seriesProp.destroy();\n        }\n    });\n    for (const zone of series.zones) {\n        destroyObjectProperties(zone, void 0, true);\n    }\n}\n/**\n * An \"async\" foreach loop. Uses a setTimeout to keep the loop from blocking the\n * UI thread.\n *\n * @private\n * @param {Array<unknown>} arr\n * The array to loop through.\n * @param {Function} fn\n * The callback to call for each item.\n * @param {Function} finalFunc\n * The callback to call when done.\n * @param {number} [chunkSize]\n * The number of iterations per timeout.\n * @param {number} [i]\n * The current index.\n * @param {boolean} [noTimeout]\n * Set to true to skip timeouts.\n */\nfunction eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout) {\n    i = i || 0;\n    chunkSize = chunkSize || CHUNK_SIZE;\n    const threshold = i + chunkSize;\n    let proceed = true;\n    while (proceed && i < threshold && i < arr.length) {\n        proceed = fn(arr[i], i);\n        ++i;\n    }\n    if (proceed) {\n        if (i < arr.length) {\n            if (noTimeout) {\n                eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout);\n            }\n            else if (BoostSeries_win.requestAnimationFrame) {\n                // If available, do requestAnimationFrame - shaves off a few ms\n                BoostSeries_win.requestAnimationFrame(function () {\n                    eachAsync(arr, fn, finalFunc, chunkSize, i);\n                });\n            }\n            else {\n                setTimeout(eachAsync, 0, arr, fn, finalFunc, chunkSize, i);\n            }\n        }\n        else if (finalFunc) {\n            finalFunc();\n        }\n    }\n}\n/**\n * Enter boost mode and apply boost-specific properties.\n * @private\n * @function Highcharts.Series#enterBoost\n */\nfunction enterBoost(series) {\n    series.boost = series.boost || {\n        // Faster than a series bind:\n        getPoint: ((bp) => getPoint(series, bp))\n    };\n    const alteredByBoost = series.boost.altered = [];\n    // Save the original values, including whether it was an own\n    // property or inherited from the prototype.\n    ['allowDG', 'directTouch', 'stickyTracking'].forEach((prop) => {\n        alteredByBoost.push({\n            prop: prop,\n            val: series[prop],\n            own: Object.hasOwnProperty.call(series, prop)\n        });\n    });\n    series.allowDG = false;\n    series.directTouch = false;\n    series.stickyTracking = true;\n    // Prevent animation when zooming in on boosted series(#13421).\n    series.finishedAnimating = true;\n    // Hide series label if any\n    if (series.labelBySeries) {\n        series.labelBySeries = series.labelBySeries.destroy();\n    }\n    // Destroy existing points after zoom out\n    if (series.is('scatter') &&\n        !series.is('treemap') &&\n        series.data.length) {\n        for (const point of series.data) {\n            point?.destroy?.();\n        }\n        series.data.length = 0;\n        series.points.length = 0;\n        delete series.processedData;\n    }\n}\n/**\n * Exit from boost mode and restore non-boost properties.\n * @private\n * @function Highcharts.Series#exitBoost\n */\nfunction exitBoost(series) {\n    const boost = series.boost, chart = series.chart, chartBoost = chart.boost;\n    if (chartBoost?.markerGroup) {\n        chartBoost.markerGroup.destroy();\n        chartBoost.markerGroup = void 0;\n        for (const s of chart.series) {\n            s.markerGroup = void 0;\n            s.markerGroup = s.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n        }\n    }\n    // Reset instance properties and/or delete instance properties and go back\n    // to prototype\n    if (boost) {\n        (boost.altered || []).forEach((setting) => {\n            if (setting.own) {\n                series[setting.prop] = setting.val;\n            }\n            else {\n                // Revert to prototype\n                delete series[setting.prop];\n            }\n        });\n        // Clear previous run\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n    // #21106, clean up boost clipping on the series groups.\n    (chart.seriesGroup || series.group)?.clip();\n}\n/**\n * @private\n * @function Highcharts.Series#hasExtremes\n */\nfunction hasExtremes(series, checkX) {\n    const options = series.options, dataLength = series.dataTable.modified.rowCount, xAxis = series.xAxis && series.xAxis.options, yAxis = series.yAxis && series.yAxis.options, colorAxis = series.colorAxis && series.colorAxis.options;\n    return dataLength > (options.boostThreshold || Number.MAX_VALUE) &&\n        // Defined yAxis extremes\n        BoostSeries_isNumber(yAxis.min) &&\n        BoostSeries_isNumber(yAxis.max) &&\n        // Defined (and required) xAxis extremes\n        (!checkX ||\n            (BoostSeries_isNumber(xAxis.min) && BoostSeries_isNumber(xAxis.max))) &&\n        // Defined (e.g. heatmap) colorAxis extremes\n        (!colorAxis ||\n            (BoostSeries_isNumber(colorAxis.min) && BoostSeries_isNumber(colorAxis.max)));\n}\n/**\n * Used multiple times. In processData first on this.options.data, the second\n * time it runs the check again after processedXData is built.\n * If the data is going to be grouped, the series shouldn't be boosted.\n * @private\n */\nconst getSeriesBoosting = (series, data) => {\n    // Check if will be grouped.\n    if (series.forceCrop) {\n        return false;\n    }\n    return (BoostSeries_isChartSeriesBoosting(series.chart) ||\n        ((data ? data.length : 0) >=\n            (series.options.boostThreshold || Number.MAX_VALUE)));\n};\n/**\n * Extend series.destroy to also remove the fake k-d-tree points (#5137).\n * Normally this is handled by Series.destroy that calls Point.destroy,\n * but the fake search points are not registered like that.\n * @private\n */\nfunction onSeriesDestroy() {\n    const series = this, chart = series.chart;\n    if (chart.boost &&\n        chart.boost.markerGroup === series.markerGroup) {\n        series.markerGroup = null;\n    }\n    if (chart.hoverPoints) {\n        chart.hoverPoints = chart.hoverPoints.filter(function (point) {\n            return point.series === series;\n        });\n    }\n    if (chart.hoverPoint && chart.hoverPoint.series === series) {\n        chart.hoverPoint = null;\n    }\n}\n/**\n * @private\n */\nfunction onSeriesHide() {\n    const boost = this.boost;\n    if (boost && boost.canvas && boost.target) {\n        if (boost.wgl) {\n            boost.wgl.clear();\n        }\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n}\n/**\n * Performs the actual render if the renderer is\n * attached to the series.\n * @private\n */\nfunction renderIfNotSeriesBoosting(series) {\n    const boost = series.boost;\n    if (boost &&\n        boost.canvas &&\n        boost.target &&\n        boost.wgl &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        boost.wgl.render(series.chart);\n    }\n}\n/**\n * Return a full Point object based on the index.\n * The boost module uses stripped point objects for performance reasons.\n * @private\n * @param {object|Highcharts.Point} boostPoint\n *        A stripped-down point object\n * @return {Highcharts.Point}\n *         A Point object as per https://api.highcharts.com/highcharts#Point\n */\nfunction getPoint(series, boostPoint) {\n    const seriesOptions = series.options, xAxis = series.xAxis, PointClass = series.pointClass;\n    if (boostPoint instanceof PointClass) {\n        return boostPoint;\n    }\n    const isScatter = series.is('scatter'), xData = ((isScatter && series.getColumn('x', true).length ?\n        series.getColumn('x', true) :\n        void 0) ||\n        (series.getColumn('x').length ? series.getColumn('x') : void 0) ||\n        seriesOptions.xData ||\n        series.getColumn('x', true) ||\n        false), yData = (series.getColumn('y', true) ||\n        seriesOptions.yData ||\n        false), point = new PointClass(series, (isScatter && xData && yData) ?\n        [xData[boostPoint.i], yData[boostPoint.i]] :\n        (isArray(series.options.data) ? series.options.data : [])[boostPoint.i], xData ? xData[boostPoint.i] : void 0);\n    point.category = BoostSeries_pick(xAxis.categories ?\n        xAxis.categories[point.x] :\n        point.x, // @todo simplify\n    point.x);\n    point.key = point.name ?? point.category;\n    point.dist = boostPoint.dist;\n    point.distX = boostPoint.distX;\n    point.plotX = boostPoint.plotX;\n    point.plotY = boostPoint.plotY;\n    point.index = boostPoint.i;\n    point.percentage = boostPoint.percentage;\n    point.isInside = series.isPointInside(point);\n    return point;\n}\n/**\n * @private\n */\nfunction scatterProcessData(force) {\n    const series = this, { options, xAxis, yAxis } = series;\n    // Process only on changes\n    if (!series.isDirty &&\n        !xAxis.isDirty &&\n        !yAxis.isDirty &&\n        !force) {\n        return false;\n    }\n    // Required to get tick-based zoom ranges that take options into account\n    // like `minPadding`, `maxPadding`, `startOnTick`, `endOnTick`.\n    series.yAxis.setTickInterval();\n    const boostThreshold = options.boostThreshold || 0, cropThreshold = options.cropThreshold, xData = series.getColumn('x'), xExtremes = xAxis.getExtremes(), xMax = xExtremes.max ?? Number.MAX_VALUE, xMin = xExtremes.min ?? -Number.MAX_VALUE, yData = series.getColumn('y'), yExtremes = yAxis.getExtremes(), yMax = yExtremes.max ?? Number.MAX_VALUE, yMin = yExtremes.min ?? -Number.MAX_VALUE;\n    // Skip processing in non-boost zoom\n    if (!series.boosted &&\n        xAxis.old &&\n        yAxis.old &&\n        xMin >= (xAxis.old.min ?? -Number.MAX_VALUE) &&\n        xMax <= (xAxis.old.max ?? Number.MAX_VALUE) &&\n        yMin >= (yAxis.old.min ?? -Number.MAX_VALUE) &&\n        yMax <= (yAxis.old.max ?? Number.MAX_VALUE)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Without thresholds just assign data\n    const dataLength = series.dataTable.rowCount;\n    if (!boostThreshold ||\n        dataLength < boostThreshold ||\n        (cropThreshold &&\n            !series.forceCrop &&\n            !series.getExtremesFromAll &&\n            !options.getExtremesFromAll &&\n            dataLength < cropThreshold)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Filter unsorted scatter data for ranges\n    const processedData = [], processedXData = [], processedYData = [], xRangeNeeded = !(BoostSeries_isNumber(xExtremes.max) || BoostSeries_isNumber(xExtremes.min)), yRangeNeeded = !(BoostSeries_isNumber(yExtremes.max) || BoostSeries_isNumber(yExtremes.min));\n    let cropped = false, x, xDataMax = xData[0], xDataMin = xData[0], y, yDataMax = yData?.[0], yDataMin = yData?.[0];\n    for (let i = 0, iEnd = xData.length; i < iEnd; ++i) {\n        x = xData[i];\n        y = yData?.[i];\n        if (x >= xMin && x <= xMax &&\n            y >= yMin && y <= yMax) {\n            processedData.push({ x, y });\n            processedXData.push(x);\n            processedYData.push(y);\n            if (xRangeNeeded) {\n                xDataMax = Math.max(xDataMax, x);\n                xDataMin = Math.min(xDataMin, x);\n            }\n            if (yRangeNeeded) {\n                yDataMax = Math.max(yDataMax, y);\n                yDataMin = Math.min(yDataMin, y);\n            }\n        }\n        else {\n            cropped = true;\n        }\n    }\n    if (xRangeNeeded) {\n        xAxis.dataMax = Math.max(xDataMax, xAxis.dataMax || 0);\n        xAxis.dataMin = Math.min(xDataMin, xAxis.dataMin || 0);\n    }\n    if (yRangeNeeded) {\n        yAxis.dataMax = Math.max(yDataMax, yAxis.dataMax || 0);\n        yAxis.dataMin = Math.min(yDataMin, yAxis.dataMin || 0);\n    }\n    // Set properties as base processData\n    series.cropped = cropped;\n    series.cropStart = 0;\n    // For boosted points rendering\n    if (cropped && series.dataTable.modified === series.dataTable) {\n        // Calling setColumns with cropped data must be done on a new instance\n        // to avoid modification of the original (complete) data\n        series.dataTable.modified = new Data_DataTableCore();\n    }\n    series.dataTable.modified.setColumns({\n        x: processedXData,\n        y: processedYData\n    });\n    if (!getSeriesBoosting(series, processedXData)) {\n        series.processedData = processedData; // For un-boosted points rendering\n    }\n    return true;\n}\n/**\n * @private\n * @function Highcharts.Series#renderCanvas\n */\nfunction seriesRenderCanvas() {\n    const options = this.options || {}, chart = this.chart, chartBoost = chart.boost, seriesBoost = this.boost, xAxis = this.xAxis, yAxis = this.yAxis, xData = options.xData || this.getColumn('x', true), yData = options.yData || this.getColumn('y', true), lowData = this.getColumn('low', true), highData = this.getColumn('high', true), rawData = this.processedData || options.data, xExtremes = xAxis.getExtremes(), \n    // Taking into account the offset of the min point #19497\n    xMin = xExtremes.min - (xAxis.minPointOffset || 0), xMax = xExtremes.max + (xAxis.minPointOffset || 0), yExtremes = yAxis.getExtremes(), yMin = yExtremes.min - (yAxis.minPointOffset || 0), yMax = yExtremes.max + (yAxis.minPointOffset || 0), pointTaken = {}, sampling = !!this.sampling, enableMouseTracking = options.enableMouseTracking, threshold = options.threshold, isRange = this.pointArrayMap &&\n        this.pointArrayMap.join(',') === 'low,high', isStacked = !!options.stacking, cropStart = this.cropStart || 0, requireSorting = this.requireSorting, useRaw = !xData, compareX = options.findNearestPointBy === 'x', xDataFull = ((this.getColumn('x').length ?\n        this.getColumn('x') :\n        void 0) ||\n        this.options.xData ||\n        this.getColumn('x', true)), lineWidth = BoostSeries_pick(options.lineWidth, 1), nullYSubstitute = options.nullInteraction && yMin;\n    let renderer = false, lastClientX, yBottom = yAxis.getThreshold(threshold), minVal, maxVal, minI, maxI;\n    // When touch-zooming or mouse-panning, re-rendering the canvas would not\n    // perform fast enough. Instead, let the axes redraw, but not the series.\n    // The series is scale-translated in an event handler for an approximate\n    // preview.\n    if (xAxis.isPanning || yAxis.isPanning) {\n        return;\n    }\n    // Get or create the renderer\n    renderer = createAndAttachRenderer(chart, this);\n    chart.boosted = true;\n    if (!this.visible) {\n        return;\n    }\n    // If we are zooming out from SVG mode, destroy the graphics\n    if (this.points || this.graph) {\n        destroyGraphics(this);\n    }\n    // If we're rendering per. series we should create the marker groups\n    // as usual.\n    if (!BoostSeries_isChartSeriesBoosting(chart)) {\n        // If all series were boosting, but are not anymore\n        // restore private markerGroup\n        if (this.markerGroup === chartBoost?.markerGroup) {\n            this.markerGroup = void 0;\n        }\n        this.markerGroup = this.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n    }\n    else {\n        // If series has a private markerGroup, remove that\n        // and use common markerGroup\n        if (this.markerGroup &&\n            this.markerGroup !== chartBoost?.markerGroup) {\n            this.markerGroup.destroy();\n        }\n        // Use a single group for the markers\n        this.markerGroup = chartBoost?.markerGroup;\n        // When switching from chart boosting mode, destroy redundant\n        // series boosting targets\n        if (seriesBoost && seriesBoost.target) {\n            this.renderTarget =\n                seriesBoost.target =\n                    seriesBoost.target.destroy();\n        }\n    }\n    const points = this.points = [], addKDPoint = (clientX, plotY, i, percentage) => {\n        const x = xDataFull ? xDataFull[cropStart + i] : false, pushPoint = (plotX) => {\n            if (chart.inverted) {\n                plotX = xAxis.len - plotX;\n                plotY = yAxis.len - plotY;\n            }\n            points.push({\n                destroy: noop,\n                x: x,\n                clientX: plotX,\n                plotX: plotX,\n                plotY: plotY,\n                i: cropStart + i,\n                percentage: percentage\n            });\n        };\n        // We need to do ceil on the clientX to make things\n        // snap to pixel values. The renderer will frequently\n        // draw stuff on \"sub-pixels\".\n        clientX = Math.ceil(clientX);\n        // Shaves off about 60ms compared to repeated concatenation\n        index = compareX ? clientX : clientX + ',' + plotY;\n        // The k-d tree requires series points.\n        // Reduce the amount of points, since the time to build the\n        // tree increases exponentially.\n        if (enableMouseTracking) {\n            if (!pointTaken[index]) {\n                pointTaken[index] = true;\n                pushPoint(clientX);\n            }\n            else if (x === xDataFull[xDataFull.length - 1]) {\n                // If the last point is on the same pixel as the last\n                // tracked point, swap them. (#18856)\n                points.length--;\n                pushPoint(clientX);\n            }\n        }\n    };\n    // Do not start building while drawing\n    this.buildKDTree = noop;\n    BoostSeries_fireEvent(this, 'renderCanvas');\n    if (this.is('line') &&\n        lineWidth > 1 &&\n        seriesBoost?.target &&\n        chartBoost &&\n        !chartBoost.lineWidthFilter) {\n        chartBoost.lineWidthFilter = chart.renderer.definition({\n            tagName: 'filter',\n            children: [\n                {\n                    tagName: 'feMorphology',\n                    attributes: {\n                        operator: 'dilate',\n                        radius: 0.25 * lineWidth\n                    }\n                }\n            ],\n            attributes: { id: 'linewidth' }\n        });\n        seriesBoost.target.attr({\n            filter: 'url(#linewidth)'\n        });\n    }\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n        // Perform the actual renderer if we're on series level\n        renderIfNotSeriesBoosting(this);\n    }\n    /**\n     * This builds the KD-tree\n     * @private\n     */\n    function processPoint(d, i) {\n        const chartDestroyed = typeof chart.index === 'undefined';\n        let x, y, clientX, plotY, percentage, low = false, isYInside = true;\n        if (!defined(d)) {\n            return true;\n        }\n        if (!chartDestroyed) {\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n            }\n            else {\n                x = d;\n                y = yData[i] ?? nullYSubstitute ?? null;\n            }\n            // Resolve low and high for range series\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = lowData[i];\n                y = highData[i];\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n                percentage = d.percentage;\n            }\n            // Optimize for scatter zooming\n            if (!requireSorting) {\n                isYInside = (y || 0) >= yMin && y <= yMax;\n            }\n            if (y !== null && x >= xMin && x <= xMax && isYInside) {\n                clientX = xAxis.toPixels(x, true);\n                if (sampling) {\n                    if (typeof minI === 'undefined' ||\n                        clientX === lastClientX) {\n                        if (!isRange) {\n                            low = y;\n                        }\n                        if (typeof maxI === 'undefined' ||\n                            y > maxVal) {\n                            maxVal = y;\n                            maxI = i;\n                        }\n                        if (typeof minI === 'undefined' ||\n                            low < minVal) {\n                            minVal = low;\n                            minI = i;\n                        }\n                    }\n                    // Add points and reset\n                    if (!compareX || clientX !== lastClientX) {\n                        // `maxI` is number too:\n                        if (typeof minI !== 'undefined') {\n                            plotY =\n                                yAxis.toPixels(maxVal, true);\n                            yBottom =\n                                yAxis.toPixels(minVal, true);\n                            addKDPoint(clientX, plotY, maxI, percentage);\n                            if (yBottom !== plotY) {\n                                addKDPoint(clientX, yBottom, minI, percentage);\n                            }\n                        }\n                        minI = maxI = void 0;\n                        lastClientX = clientX;\n                    }\n                }\n                else {\n                    plotY = Math.ceil(yAxis.toPixels(y, true));\n                    addKDPoint(clientX, plotY, i, percentage);\n                }\n            }\n        }\n        return !chartDestroyed;\n    }\n    /**\n     * @private\n     */\n    const boostOptions = renderer.settings, doneProcessing = () => {\n        BoostSeries_fireEvent(this, 'renderedCanvas');\n        // Go back to prototype, ready to build\n        delete this.buildKDTree;\n        // Check that options exist, as async processing\n        // could mean the series is removed at this point (#19895)\n        if (this.options) {\n            this.buildKDTree();\n        }\n        if (boostOptions.debug.timeKDTree) {\n            console.timeEnd('kd tree building'); // eslint-disable-line no-console\n        }\n    };\n    // Loop over the points to build the k-d tree - skip this if\n    // exporting\n    if (!chart.renderer.forExport) {\n        if (boostOptions.debug.timeKDTree) {\n            console.time('kd tree building'); // eslint-disable-line no-console\n        }\n        eachAsync(isStacked ?\n            this.data.slice(cropStart) :\n            (xData || rawData), processPoint, doneProcessing);\n    }\n}\n/**\n * Used for treemap|heatmap.drawPoints\n * @private\n */\nfunction wrapSeriesDrawPoints(proceed) {\n    let enabled = true;\n    if (this.chart.options && this.chart.options.boost) {\n        enabled = typeof this.chart.options.boost.enabled === 'undefined' ?\n            true :\n            this.chart.options.boost.enabled;\n    }\n    if (!enabled || !this.boosted) {\n        return proceed.call(this);\n    }\n    this.chart.boosted = true;\n    // Make sure we have a valid OGL context\n    const renderer = createAndAttachRenderer(this.chart, this);\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n    }\n    renderIfNotSeriesBoosting(this);\n}\n/**\n * Override a bunch of methods the same way. If the number of points is\n * below the threshold, run the original method. If not, check for a\n * canvas version or do nothing.\n *\n * Note that we're not overriding any of these for heatmaps.\n */\nfunction wrapSeriesFunctions(seriesProto, seriesTypes, method) {\n    /**\n     * @private\n     */\n    function branch(proceed) {\n        const letItPass = this.options.stacking &&\n            (method === 'translate' || method === 'generatePoints');\n        if (!this.boosted ||\n            letItPass ||\n            !boostEnabled(this.chart) ||\n            this.type === 'heatmap' ||\n            this.type === 'treemap' ||\n            !Boost_BoostableMap[this.type] ||\n            this.options.boostThreshold === 0) {\n            proceed.call(this);\n            // Run canvas version of method, like renderCanvas(), if it exists\n        }\n        else if (method === 'render' && this.renderCanvas) {\n            this.renderCanvas();\n        }\n    }\n    wrap(seriesProto, method, branch);\n    // Special case for some types, when translate method is already wrapped\n    if (method === 'translate') {\n        for (const type of [\n            'column',\n            'arearange',\n            'columnrange',\n            'heatmap',\n            'treemap'\n        ]) {\n            if (seriesTypes[type]) {\n                wrap(seriesTypes[type].prototype, method, branch);\n            }\n        }\n    }\n}\n/**\n * Do not compute extremes when min and max are set. If we use this in the\n * core, we can add the hook to hasExtremes to the methods directly.\n * @private\n */\nfunction wrapSeriesGetExtremes(proceed) {\n    if (this.boosted) {\n        if (hasExtremes(this)) {\n            return {};\n        }\n        if (this.xAxis.isPanning || this.yAxis.isPanning) {\n            // Do not re-compute the extremes during panning, because looping\n            // the data is expensive. The `this` contains the `dataMin` and\n            // `dataMax` to use.\n            return this;\n        }\n    }\n    return proceed.apply(this, [].slice.call(arguments, 1));\n}\n/**\n * If the series is a heatmap or treemap, or if the series is not boosting\n * do the default behaviour. Otherwise, process if the series has no\n * extremes.\n * @private\n */\nfunction wrapSeriesProcessData(proceed) {\n    let dataToMeasure = this.options.data;\n    if (boostEnabled(this.chart) && Boost_BoostableMap[this.type]) {\n        const series = this, \n        // Flag for code that should run for ScatterSeries and its\n        // subclasses, apart from the enlisted exceptions.\n        isScatter = series.is('scatter') &&\n            !series.is('bubble') &&\n            !series.is('treemap') &&\n            !series.is('heatmap');\n        // If there are no extremes given in the options, we also need to\n        // process the data to read the data extremes. If this is a heatmap,\n        // do default behaviour.\n        if (\n        // First pass with options.data:\n        !getSeriesBoosting(series, dataToMeasure) ||\n            isScatter ||\n            series.is('treemap') ||\n            // Use processedYData for the stack (#7481):\n            series.options.stacking ||\n            !hasExtremes(series, true)) {\n            // Do nothing until the panning stops\n            if (series.boosted && (series.xAxis?.isPanning || series.yAxis?.isPanning)) {\n                return;\n            }\n            // Extra check for zoomed scatter data\n            if (isScatter && series.yAxis.type !== 'treegrid') {\n                scatterProcessData.call(series, arguments[1]);\n            }\n            else {\n                proceed.apply(series, [].slice.call(arguments, 1));\n            }\n            dataToMeasure = series.getColumn('x', true);\n        }\n        // Set the isBoosting flag, second pass with processedXData to\n        // see if we have zoomed.\n        series.boosted = getSeriesBoosting(series, dataToMeasure);\n        // Enter or exit boost mode\n        if (series.boosted) {\n            // Force turbo-mode:\n            let firstPoint;\n            if (series.options.data?.length) {\n                firstPoint = series.getFirstValidPoint(series.options.data);\n                if (!BoostSeries_isNumber(firstPoint) &&\n                    !isArray(firstPoint) &&\n                    !series.is('treemap')) {\n                    BoostSeries_error(12, false, series.chart);\n                }\n            }\n            enterBoost(series);\n        }\n        else {\n            exitBoost(series);\n        }\n        // The series type is not boostable\n    }\n    else {\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Return a point instance from the k-d-tree\n * @private\n */\nfunction wrapSeriesSearchPoint(proceed) {\n    const result = proceed.apply(this, [].slice.call(arguments, 1));\n    if (this.boost && result) {\n        return this.boost.getPoint(result);\n    }\n    return result;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst BoostSeries = {\n    compose: BoostSeries_compose,\n    destroyGraphics,\n    eachAsync,\n    getPoint\n};\n/* harmony default export */ const Boost_BoostSeries = (BoostSeries);\n\n;// ./code/es-modules/Extensions/BoostCanvas.js\n/* *\n *\n *  License: www.highcharts.com/license\n *  Author: Torstein Honsi, Christer Vasseng\n *\n *  This module serves as a fallback for the Boost module in IE9 and IE10. Newer\n *  browsers support WebGL which is faster.\n *\n *  It is recommended to include this module in conditional comments targeting\n *  IE9 and IE10.\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getBoostClipRect: BoostCanvas_getBoostClipRect, isChartSeriesBoosting: BoostCanvas_isChartSeriesBoosting } = Boost_BoostChart;\n\nconst { destroyGraphics: BoostCanvas_destroyGraphics } = Boost_BoostSeries;\n\nconst { parse: BoostCanvas_color } = (external_highcharts_src_js_default_Color_default());\n\nconst { doc: BoostCanvas_doc, noop: BoostCanvas_noop } = (external_highcharts_src_js_default_default());\n\nconst { addEvent: BoostCanvas_addEvent, fireEvent: BoostCanvas_fireEvent, isNumber: BoostCanvas_isNumber, merge: BoostCanvas_merge, pick: BoostCanvas_pick, wrap: BoostCanvas_wrap } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Namespace\n *\n * */\nvar BoostCanvas;\n(function (BoostCanvas) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // Use a blank pixel for clearing canvas (#17182)\n    const b64BlankPixel = ('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAw' +\n        'CAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=');\n    const CHUNK_SIZE = 50000;\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    let ChartConstructor;\n    let destroyLoadingDiv;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function areaCvsDrawPoint(ctx, clientX, plotY, yBottom, lastPoint) {\n        if (lastPoint && clientX !== lastPoint.clientX) {\n            ctx.moveTo(lastPoint.clientX, lastPoint.yBottom);\n            ctx.lineTo(lastPoint.clientX, lastPoint.plotY);\n            ctx.lineTo(clientX, plotY);\n            ctx.lineTo(clientX, yBottom);\n        }\n    }\n    /**\n     * @private\n     */\n    function bubbleCvsMarkerCircle(ctx, clientX, plotY, r, i) {\n        ctx.moveTo(clientX, plotY);\n        ctx.arc(clientX, plotY, this.radii && this.radii[i], 0, 2 * Math.PI, false);\n    }\n    /**\n     * @private\n     */\n    function columnCvsDrawPoint(ctx, clientX, plotY, yBottom) {\n        ctx.rect(clientX - 1, plotY, 1, yBottom - plotY);\n    }\n    /**\n     * @private\n     */\n    function compose(ChartClass, SeriesClass, seriesTypes) {\n        const seriesProto = SeriesClass.prototype;\n        if (!seriesProto.renderCanvas) {\n            const { area: AreaSeries, bubble: BubbleSeries, column: ColumnSeries, heatmap: HeatmapSeries, scatter: ScatterSeries } = seriesTypes;\n            ChartConstructor = ChartClass;\n            ChartClass.prototype.callbacks.push((chart) => {\n                BoostCanvas_addEvent(chart, 'predraw', onChartClear);\n                BoostCanvas_addEvent(chart, 'render', onChartCanvasToSVG);\n            });\n            seriesProto.canvasToSVG = seriesCanvasToSVG;\n            seriesProto.cvsLineTo = seriesCvsLineTo;\n            seriesProto.getContext = seriesGetContext;\n            seriesProto.renderCanvas = seriesRenderCanvas;\n            if (AreaSeries) {\n                const areaProto = AreaSeries.prototype;\n                areaProto.cvsDrawPoint = areaCvsDrawPoint;\n                areaProto.fill = true;\n                areaProto.fillOpacity = true;\n                areaProto.sampling = true;\n            }\n            if (BubbleSeries) {\n                const bubbleProto = BubbleSeries.prototype;\n                bubbleProto.cvsMarkerCircle = bubbleCvsMarkerCircle;\n                bubbleProto.cvsStrokeBatch = 1;\n            }\n            if (ColumnSeries) {\n                const columnProto = ColumnSeries.prototype;\n                columnProto.cvsDrawPoint = columnCvsDrawPoint;\n                columnProto.fill = true;\n                columnProto.sampling = true;\n            }\n            if (HeatmapSeries) {\n                const heatmapProto = HeatmapSeries.prototype;\n                BoostCanvas_wrap(heatmapProto, 'drawPoints', wrapHeatmapDrawPoints);\n            }\n            if (ScatterSeries) {\n                const scatterProto = ScatterSeries.prototype;\n                scatterProto.cvsMarkerCircle = scatterCvsMarkerCircle;\n                scatterProto.cvsMarkerSquare = scatterCvsMarkerSquare;\n                scatterProto.fill = true;\n            }\n        }\n    }\n    BoostCanvas.compose = compose;\n    /**\n     * @private\n     */\n    function onChartCanvasToSVG() {\n        if (this.boost && this.boost.copy) {\n            this.boost.copy();\n        }\n    }\n    /**\n     * @private\n     */\n    function onChartClear() {\n        const boost = this.boost || {};\n        if (boost.target) {\n            boost.target.attr({ href: b64BlankPixel });\n        }\n        if (boost.canvas) {\n            boost.canvas.getContext('2d').clearRect(0, 0, boost.canvas.width, boost.canvas.height);\n        }\n    }\n    /**\n     * Draw the canvas image inside an SVG image\n     *\n     * @private\n     * @function Highcharts.Series#canvasToSVG\n     */\n    function seriesCanvasToSVG() {\n        if (!BoostCanvas_isChartSeriesBoosting(this.chart)) {\n            if (this.boost && this.boost.copy) {\n                this.boost.copy();\n            }\n            else if (this.chart.boost && this.chart.boost.copy) {\n                this.chart.boost.copy();\n            }\n        }\n        else if (this.boost && this.boost.clear) {\n            this.boost.clear();\n        }\n    }\n    /**\n     * @private\n     */\n    function seriesCvsLineTo(ctx, clientX, plotY) {\n        ctx.lineTo(clientX, plotY);\n    }\n    /**\n     * Create a hidden canvas to draw the graph on. The contents is later\n     * copied over to an SVG image element.\n     *\n     * @private\n     * @function Highcharts.Series#getContext\n     */\n    function seriesGetContext() {\n        const chart = this.chart, target = BoostCanvas_isChartSeriesBoosting(chart) ? chart : this, targetGroup = (target === chart ?\n            chart.seriesGroup :\n            chart.seriesGroup || this.group), width = chart.chartWidth, height = chart.chartHeight, swapXY = function (proceed, x, y, a, b, c, d) {\n            proceed.call(this, y, x, a, b, c, d);\n        };\n        let ctx;\n        const boost = target.boost =\n            target.boost ||\n                {};\n        ctx = boost.targetCtx;\n        if (!boost.canvas) {\n            boost.canvas = BoostCanvas_doc.createElement('canvas');\n            boost.target = chart.renderer\n                .image('', 0, 0, width, height)\n                .addClass('highcharts-boost-canvas')\n                .add(targetGroup);\n            ctx = boost.targetCtx =\n                boost.canvas.getContext('2d');\n            if (chart.inverted) {\n                ['moveTo', 'lineTo', 'rect', 'arc'].forEach((fn) => {\n                    BoostCanvas_wrap(ctx, fn, swapXY);\n                });\n            }\n            boost.copy = function () {\n                boost.target.attr({\n                    href: boost.canvas.toDataURL('image/png')\n                });\n            };\n            boost.clear = function () {\n                ctx.clearRect(0, 0, boost.canvas.width, boost.canvas.height);\n                if (target === boost.target) {\n                    boost.target.attr({\n                        href: b64BlankPixel\n                    });\n                }\n            };\n            boost.clipRect = chart.renderer.clipRect();\n            boost.target.clip(boost.clipRect);\n        }\n        else if (!(target instanceof ChartConstructor)) {\n            ///  ctx.clearRect(0, 0, width, height);\n        }\n        if (boost.canvas.width !== width) {\n            boost.canvas.width = width;\n        }\n        if (boost.canvas.height !== height) {\n            boost.canvas.height = height;\n        }\n        boost.target.attr({\n            x: 0,\n            y: 0,\n            width: width,\n            height: height,\n            style: 'pointer-events: none',\n            href: b64BlankPixel\n        });\n        if (boost.clipRect) {\n            boost.clipRect.attr(BoostCanvas_getBoostClipRect(chart, target));\n        }\n        return ctx;\n    }\n    /**\n     * @private\n     */\n    function seriesRenderCanvas() {\n        const series = this, options = series.options, chart = series.chart, xAxis = series.xAxis, yAxis = series.yAxis, activeBoostSettings = chart.options.boost || {}, boostSettings = {\n            timeRendering: activeBoostSettings.timeRendering || false,\n            timeSeriesProcessing: activeBoostSettings.timeSeriesProcessing || false,\n            timeSetup: activeBoostSettings.timeSetup || false\n        }, xData = series.getColumn('x', true), yData = series.getColumn('y', true), rawData = options.data, xExtremes = xAxis.getExtremes(), xMin = xExtremes.min, xMax = xExtremes.max, yExtremes = yAxis.getExtremes(), yMin = yExtremes.min, yMax = yExtremes.max, pointTaken = {}, sampling = !!series.sampling, r = options.marker && options.marker.radius, strokeBatch = series.cvsStrokeBatch || 1000, enableMouseTracking = options.enableMouseTracking, threshold = options.threshold, hasThreshold = BoostCanvas_isNumber(threshold), translatedThreshold = yAxis.getThreshold(threshold), doFill = series.fill, isRange = (series.pointArrayMap &&\n            series.pointArrayMap.join(',') === 'low,high'), isStacked = !!options.stacking, cropStart = series.cropStart || 0, loadingOptions = chart.options.loading, requireSorting = series.requireSorting, connectNulls = options.connectNulls, useRaw = !xData, sdata = (isStacked ?\n            series.data :\n            (xData || rawData)), fillColor = (series.fillOpacity ?\n            external_highcharts_src_js_default_Color_default().parse(series.color).setOpacity(BoostCanvas_pick(options.fillOpacity, 0.75)).get() :\n            series.color), compareX = options.findNearestPointBy === 'x', boost = this.boost || {}, cvsDrawPoint = series.cvsDrawPoint, cvsLineTo = options.lineWidth ? series.cvsLineTo : void 0, cvsMarker = (r && r <= 1 ?\n            series.cvsMarkerSquare :\n            series.cvsMarkerCircle);\n        if (boost.target) {\n            boost.target.attr({ href: b64BlankPixel });\n        }\n        // If we are zooming out from SVG mode, destroy the graphics\n        if (series.points || series.graph) {\n            BoostCanvas_destroyGraphics(series);\n        }\n        // The group\n        series.plotGroup('group', 'series', series.visible ? 'visible' : 'hidden', options.zIndex, chart.seriesGroup);\n        series.markerGroup = series.group;\n        BoostCanvas_addEvent(series, 'destroy', function () {\n            // Prevent destroy twice\n            series.markerGroup = null;\n        });\n        const points = this.points = [], ctx = this.getContext();\n        series.buildKDTree = BoostCanvas_noop; // Do not start building while drawing\n        if (boost.clear) {\n            boost.clear();\n        }\n        // If (series.canvas) {\n        //     ctx.clearRect(\n        //         0,\n        //         0,\n        //         series.canvas.width,\n        //         series.canvas.height\n        //     );\n        // }\n        if (!series.visible) {\n            return;\n        }\n        // Display a loading indicator\n        if (rawData.length > 99999) {\n            chart.options.loading = BoostCanvas_merge(loadingOptions, {\n                labelStyle: {\n                    backgroundColor: BoostCanvas_color(\"#ffffff\" /* Palette.backgroundColor */).setOpacity(0.75).get(),\n                    padding: '1em',\n                    borderRadius: '0.5em'\n                },\n                style: {\n                    backgroundColor: 'none',\n                    opacity: 1\n                }\n            });\n            external_highcharts_src_js_default_default().clearTimeout(destroyLoadingDiv);\n            chart.showLoading('Drawing...');\n            chart.options.loading = loadingOptions; // Reset\n        }\n        if (boostSettings.timeRendering) {\n            console.time('canvas rendering'); // eslint-disable-line no-console\n        }\n        // Loop variables\n        let c = 0, lastClientX, lastPoint, yBottom = translatedThreshold, wasNull, minVal, maxVal, minI, maxI, index;\n        // Loop helpers\n        const stroke = function () {\n            if (doFill) {\n                ctx.fillStyle = fillColor;\n                ctx.fill();\n            }\n            else {\n                ctx.strokeStyle = series.color;\n                ctx.lineWidth = options.lineWidth;\n                ctx.stroke();\n            }\n        }, \n        //\n        drawPoint = function (clientX, plotY, yBottom, i) {\n            if (c === 0) {\n                ctx.beginPath();\n                if (cvsLineTo) {\n                    ctx.lineJoin = 'round';\n                }\n            }\n            if (chart.scroller &&\n                series.options.className ===\n                    'highcharts-navigator-series') {\n                plotY += chart.scroller.top;\n                if (yBottom) {\n                    yBottom += chart.scroller.top;\n                }\n            }\n            else {\n                plotY += chart.plotTop;\n            }\n            clientX += chart.plotLeft;\n            if (wasNull) {\n                ctx.moveTo(clientX, plotY);\n            }\n            else {\n                if (cvsDrawPoint) {\n                    cvsDrawPoint(ctx, clientX, plotY, yBottom, lastPoint);\n                }\n                else if (cvsLineTo) {\n                    cvsLineTo(ctx, clientX, plotY);\n                }\n                else if (cvsMarker) {\n                    cvsMarker.call(series, ctx, clientX, plotY, r, i);\n                }\n            }\n            // We need to stroke the line for every 1000 pixels. It will\n            // crash the browser memory use if we stroke too\n            // infrequently.\n            c = c + 1;\n            if (c === strokeBatch) {\n                stroke();\n                c = 0;\n            }\n            // Area charts need to keep track of the last point\n            lastPoint = {\n                clientX: clientX,\n                plotY: plotY,\n                yBottom: yBottom\n            };\n        }, xDataFull = ((this.getColumn('x').length ? this.getColumn('x') : void 0) ||\n            this.options.xData ||\n            (this.getColumn('x', true).length ?\n                this.getColumn('x', true) :\n                false)), \n        //\n        addKDPoint = function (clientX, plotY, i) {\n            // Shaves off about 60ms compared to repeated concatenation\n            index = compareX ? clientX : clientX + ',' + plotY;\n            // The k-d tree requires series points.\n            // Reduce the amount of points, since the time to build the\n            // tree increases exponentially.\n            if (enableMouseTracking && !pointTaken[index]) {\n                pointTaken[index] = true;\n                if (chart.inverted) {\n                    clientX = xAxis.len - clientX;\n                    plotY = yAxis.len - plotY;\n                }\n                points.push({\n                    x: xDataFull ?\n                        xDataFull[cropStart + i] :\n                        false,\n                    clientX: clientX,\n                    plotX: clientX,\n                    plotY: plotY,\n                    i: cropStart + i\n                });\n            }\n        };\n        // Loop over the points\n        Boost_BoostSeries.eachAsync(sdata, (d, i) => {\n            const chartDestroyed = typeof chart.index === 'undefined';\n            let x, y, clientX, plotY, isNull, low, isNextInside = false, isPrevInside = false, nx = NaN, px = NaN, isYInside = true;\n            if (!chartDestroyed) {\n                if (useRaw) {\n                    x = d[0];\n                    y = d[1];\n                    if (sdata[i + 1]) {\n                        nx = sdata[i + 1][0];\n                    }\n                    if (sdata[i - 1]) {\n                        px = sdata[i - 1][0];\n                    }\n                }\n                else {\n                    x = d;\n                    y = yData[i];\n                    if (sdata[i + 1]) {\n                        nx = sdata[i + 1];\n                    }\n                    if (sdata[i - 1]) {\n                        px = sdata[i - 1];\n                    }\n                }\n                if (nx && nx >= xMin && nx <= xMax) {\n                    isNextInside = true;\n                }\n                if (px && px >= xMin && px <= xMax) {\n                    isPrevInside = true;\n                }\n                // Resolve low and high for range series\n                if (isRange) {\n                    if (useRaw) {\n                        y = d.slice(1, 3);\n                    }\n                    low = y[0];\n                    y = y[1];\n                }\n                else if (isStacked) {\n                    x = d.x;\n                    y = d.stackY;\n                    low = y - d.y;\n                }\n                isNull = y === null;\n                // Optimize for scatter zooming\n                if (!requireSorting) {\n                    isYInside = y >= yMin && y <= yMax;\n                }\n                if (!isNull &&\n                    ((x >= xMin && x <= xMax && isYInside) ||\n                        (isNextInside || isPrevInside))) {\n                    clientX = Math.round(xAxis.toPixels(x, true));\n                    if (sampling) {\n                        if (typeof minI === 'undefined' ||\n                            clientX === lastClientX) {\n                            if (!isRange) {\n                                low = y;\n                            }\n                            if (typeof maxI === 'undefined' || y > maxVal) {\n                                maxVal = y;\n                                maxI = i;\n                            }\n                            if (typeof minI === 'undefined' ||\n                                low < minVal) {\n                                minVal = low;\n                                minI = i;\n                            }\n                        }\n                        // Add points and reset\n                        if (clientX !== lastClientX) {\n                            // `maxI` also a number:\n                            if (typeof minI !== 'undefined') {\n                                plotY = yAxis.toPixels(maxVal, true);\n                                yBottom = yAxis.toPixels(minVal, true);\n                                drawPoint(clientX, hasThreshold ?\n                                    Math.min(plotY, translatedThreshold) : plotY, hasThreshold ?\n                                    Math.max(yBottom, translatedThreshold) : yBottom, i);\n                                addKDPoint(clientX, plotY, maxI);\n                                if (yBottom !== plotY) {\n                                    addKDPoint(clientX, yBottom, minI);\n                                }\n                            }\n                            minI = maxI = void 0;\n                            lastClientX = clientX;\n                        }\n                    }\n                    else {\n                        plotY = Math.round(yAxis.toPixels(y, true));\n                        drawPoint(clientX, plotY, yBottom, i);\n                        addKDPoint(clientX, plotY, i);\n                    }\n                }\n                wasNull = isNull && !connectNulls;\n                if (i % CHUNK_SIZE === 0) {\n                    if (series.boost &&\n                        series.boost.copy) {\n                        series.boost.copy();\n                    }\n                    else if (series.chart.boost &&\n                        series.chart.boost.copy) {\n                        series.chart.boost.copy();\n                    }\n                }\n            }\n            return !chartDestroyed;\n        }, function () {\n            const loadingDiv = chart.loadingDiv, loadingShown = chart.loadingShown;\n            stroke();\n            // If (series.boostCopy || series.chart.boostCopy) {\n            //     (series.boostCopy || series.chart.boostCopy)();\n            // }\n            series.canvasToSVG();\n            if (boostSettings.timeRendering) {\n                console.timeEnd('canvas rendering'); // eslint-disable-line no-console\n            }\n            BoostCanvas_fireEvent(series, 'renderedCanvas');\n            // Do not use chart.hideLoading, as it runs JS animation and\n            // will be blocked by buildKDTree. CSS animation looks good, but\n            // then it must be deleted in timeout. If we add the module to\n            // core, change hideLoading so we can skip this block.\n            if (loadingShown) {\n                loadingDiv.style.transition = 'opacity 250ms';\n                loadingDiv.opacity = 0;\n                chart.loadingShown = false;\n                destroyLoadingDiv = setTimeout(function () {\n                    if (loadingDiv.parentNode) { // In exporting it is falsy\n                        loadingDiv.parentNode.removeChild(loadingDiv);\n                    }\n                    chart.loadingDiv = chart.loadingSpan = null;\n                }, 250);\n            }\n            // Go back to prototype, ready to build\n            delete series.buildKDTree;\n            series.buildKDTree();\n            // Don't do async on export, the exportChart, getSVGForExport and\n            // getSVG methods are not chained for it.\n        }, chart.renderer.forExport ? Number.MAX_VALUE : void 0);\n    }\n    /**\n     * @private\n     */\n    function scatterCvsMarkerCircle(ctx, clientX, plotY, r) {\n        ctx.moveTo(clientX, plotY);\n        ctx.arc(clientX, plotY, r, 0, 2 * Math.PI, false);\n    }\n    /**\n     * Rect is twice as fast as arc, should be used for small markers.\n     * @private\n     */\n    function scatterCvsMarkerSquare(ctx, clientX, plotY, r) {\n        ctx.rect(clientX - r, plotY - r, r * 2, r * 2);\n    }\n    /**\n     * @private\n     */\n    function wrapHeatmapDrawPoints() {\n        const chart = this.chart, ctx = this.getContext(), inverted = this.chart.inverted, xAxis = this.xAxis, yAxis = this.yAxis;\n        if (ctx) {\n            // Draw the columns\n            this.points.forEach((point) => {\n                const plotY = point.plotY;\n                let pointAttr;\n                if (typeof plotY !== 'undefined' &&\n                    !isNaN(plotY) &&\n                    point.y !== null &&\n                    ctx) {\n                    const { x = 0, y = 0, width = 0, height = 0 } = point.shapeArgs || {};\n                    if (!chart.styledMode) {\n                        pointAttr = point.series.pointAttribs(point);\n                    }\n                    else {\n                        pointAttr = point.series.colorAttribs(point);\n                    }\n                    ctx.fillStyle = pointAttr.fill;\n                    if (inverted) {\n                        ctx.fillRect(yAxis.len - y + xAxis.left, xAxis.len - x + yAxis.top, -height, -width);\n                    }\n                    else {\n                        ctx.fillRect(x + xAxis.left, y + yAxis.top, width, height);\n                    }\n                }\n            });\n            this.canvasToSVG();\n        }\n        else {\n            this.chart.showLoading('Your browser doesn\\'t support HTML5 canvas, <br>' +\n                'please use a modern browser');\n        }\n    }\n})(BoostCanvas || (BoostCanvas = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Extensions_BoostCanvas = (BoostCanvas);\n\n;// ./code/es-modules/masters/modules/boost-canvas.js\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\n/**\n * Initialize the canvas boost.\n *\n * @function Highcharts.initCanvasBoost\n */\nG.initCanvasBoost = function () {\n    Extensions_BoostCanvas.compose(G.Chart, G.Series, G.seriesTypes);\n};\n/* harmony default export */ const boost_canvas_src = ((external_highcharts_src_js_default_default()));\n\nexport { boost_canvas_src as default };\n"], "names": ["index", "mainCanvas", "__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "Column<PERSON><PERSON><PERSON>", "BoostCanvas", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "Boost_Boostables", "BoostableMap", "for<PERSON>ach", "item", "composed", "addEvent", "pick", "pushUnique", "isChartSeriesBoosting", "chart", "allSeries", "series", "boost", "boostOptions", "options", "threshold", "seriesThreshold", "length", "allowBoostForce", "allowForce", "axis", "xAxis", "min", "Infinity", "dataMin", "max", "dataMax", "forceChartBoost", "canBoostCount", "needBoostCount", "seriesOptions", "boostThreshold", "visible", "type", "Boost_BoostableMap", "patientMax", "args", "r", "Number", "MAX_VALUE", "t", "getColumn", "data", "points", "onChartCallback", "canvasToSVG", "wgl", "render", "boosted", "axes", "some", "isPanning", "clear", "canvas", "allocate<PERSON><PERSON><PERSON>", "markerGroup", "yAxis", "translate", "pos", "order", "prevX", "prevY", "pointer", "e", "hoverPoint", "inverted", "s", "halo", "hide", "<PERSON><PERSON><PERSON>", "compose", "ChartClass", "wglMode", "callbacks", "push", "getBoostClipRect", "target", "navigator", "clipBox", "x", "plotLeft", "y", "plotTop", "width", "plot<PERSON>id<PERSON>", "height", "plotHeight", "top", "opposite", "left", "is", "getClipBox", "lateral", "verticalAxes", "Math", "len", "external_highcharts_src_js_default_Color_namespaceObject", "Color", "external_highcharts_src_js_default_Color_default", "Boost_WGLDrawMode", "clamp", "error", "WG<PERSON><PERSON><PERSON>_pick", "Boost_WGLShader", "constructor", "gl", "errors", "uLocations", "createShader", "bind", "shaderProgram", "useProgram", "v", "stringToProgram", "f", "uloc", "getUniformLocation", "createProgram", "<PERSON><PERSON><PERSON><PERSON>", "linkProgram", "getProgramParameter", "LINK_STATUS", "bindAttribLocation", "pUniform", "psUniform", "fcUniform", "isBubbleUniform", "bubbleSizeAbsUniform", "bubbleSizeAreaUniform", "uSamplerUniform", "skipTranslationUniform", "isCircleUniform", "isInverted", "getProgramInfoLog", "handleErrors", "join", "str", "shader", "VERTEX_SHADER", "FRAGMENT_SHADER", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS", "getShaderInfoLog", "destroy", "deleteProgram", "fillColorUniform", "getProgram", "pointSizeUniform", "perspectiveUniform", "reset", "uniform1i", "setBubbleUniforms", "zCalcMin", "zCalcMax", "pixelRatio", "zMin", "zMax", "pxSizes", "getPxExtremes", "displayNegative", "zThreshold", "sizeBy", "sizeByAbsoluteValue", "setUniform", "minPxSize", "maxPxSize", "setColor", "color", "uniform4f", "setDrawAsCircle", "flag", "setInverted", "setPMatrix", "m", "uniformMatrix4fv", "setPointSize", "p", "uniform1f", "setSkipTranslation", "setTexture", "texture", "name", "val", "u", "Boost_WGLVertexBuffer", "dataComponents", "buffer", "iterator", "preAllocated", "vertAttribute", "components", "allocate", "size", "Float32Array", "vertexAttribPointer", "FLOAT", "build", "dataIn", "attrib", "farray", "deleteBuffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "bufferData", "STATIC_DRAW", "getAttribLocation", "enableVertexAttribArray", "b", "from", "to", "drawMode", "drawArrays", "parse", "doc", "win", "isNumber", "isObject", "merge", "objectEach", "WG<PERSON>enderer_pick", "asBar", "asCircle", "contexts", "WG<PERSON><PERSON><PERSON>", "orthoMatrix", "seriesPointCount", "isStacked", "xData", "stacking", "postRender<PERSON>allback", "isInited", "markerData", "textureHandles", "settings", "pointSize", "lineWidth", "fillColor", "useAlpha", "usePreallocated", "useGPUTranslations", "debug", "timeRendering", "timeSeriesProcessing", "timeSetup", "timeBufferCopy", "timeKDTree", "showSkipSummary", "getPixelRatio", "devicePixelRatio", "setOptions", "vbuffer", "allocateBufferForSingleSeries", "COLOR_BUFFER_BIT", "DEPTH_BUFFER_BIT", "pushSeriesData", "inst", "isRange", "pointArrayMap", "sorted", "rawData", "xExtremes", "getExtremes", "xMin", "minPointOffset", "xMax", "yExtremes", "yMin", "yMax", "yData", "zData", "useRaw", "connectNulls", "sdata", "closestLeft", "closestRight", "chartDestroyed", "drawAsBar", "zoneAxis", "zones", "lastX", "lastY", "minVal", "scolor", "skipped", "hadPoints", "z", "i", "px", "nx", "low", "nextInside", "prevInside", "pcolor", "isXInside", "isYInside", "firstPoint", "zoneColors", "zoneDefColor", "gapSize", "vlen", "boostData", "gapUnit", "closestPointRange", "zone", "zoneColor", "rgba", "value", "pointAttribs", "fill", "closestPointRangePx", "pushColor", "colorData", "vertice", "checkTreshold", "skipTranslation", "closeSegment", "segments", "beginSegment", "pushRect", "w", "h", "node", "levelDynamic", "sort", "point", "swidth", "pointAttr", "plotY", "isNaN", "shapeArgs", "styledMode", "colorAttribs", "stroke", "pointOptions", "slice", "stackY", "last", "toPixels", "hasMark<PERSON>", "abs", "logarithmic", "step", "console", "log", "pushSupplementPoint", "atStart", "concat", "pushSeries", "markerTo", "time", "markerFrom", "marker", "enabled", "showMarkers", "timeEnd", "flush", "setXAxis", "transA", "minPixelPadding", "pointRange", "horiz", "reversed", "setYAxis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "translation", "<PERSON><PERSON><PERSON>", "chartWidth", "chartHeight", "viewport", "isMS", "si", "shapeOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yBottom", "get<PERSON><PERSON><PERSON>old", "isRadial", "radius", "shapeTexture", "symbol", "circle", "sindex", "cbuffer", "isReady", "bindTexture", "TEXTURE_2D", "handle", "plotGroup", "seriesGroup", "addClass", "getStyle", "colorByPoint", "colors", "fillOpacity", "setOpacity", "boostBlending", "blendFunc", "SRC_ALPHA", "ONE", "blendEquation", "FUNC_ADD", "DST_COLOR", "ZERO", "FUNC_MIN", "blendFuncSeparate", "ONE_MINUS_SRC_ALPHA", "Array", "disableVertexAttribArray", "renderer", "forExport", "setTimeout", "setSize", "init", "noFlush", "getContext", "enable", "BLEND", "disable", "DEPTH_TEST", "depthFunc", "LESS", "createTexture", "fn", "props", "createElement", "ctx", "mozImageSmoothingEnabled", "webkitImageSmoothingEnabled", "msImageSmoothingEnabled", "imageSmoothingEnabled", "strokeStyle", "fillStyle", "activeTexture", "TEXTURE0", "texImage2D", "RGBA", "UNSIGNED_BYTE", "texParameteri", "TEXTURE_WRAP_S", "CLAMP_TO_EDGE", "TEXTURE_WRAP_T", "TEXTURE_MAG_FILTER", "LINEAR", "TEXTURE_MIN_FILTER", "beginPath", "arc", "PI", "fillRect", "moveTo", "lineTo", "deleteTexture", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "removed", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "result", "set", "subarray", "fireEvent", "DataTableCore_objectEach", "<PERSON><PERSON><PERSON>", "Data_DataTableCore", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "applyRowCount", "deleteRows", "rowIndex", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "BoostSeries_getBoostClipRect", "BoostSeries_isChartSeriesBoosting", "getOptions", "BoostSeries_composed", "BoostSeries_doc", "noop", "BoostSeries_win", "BoostSeries_addEvent", "destroyObjectProperties", "BoostSeries_error", "extend", "BoostSeries_fireEvent", "BoostSeries_isNumber", "BoostSeries_pick", "BoostSeries_pushUnique", "wrap", "defined", "allocateIfNotSeriesBoosting", "boostEnabled", "createAndAttachRenderer", "targetGroup", "group", "foSupported", "SVGForeignObjectElement", "hasClickHandler", "Boolean", "events", "click", "renderTarget", "image", "add", "attr", "href", "copy", "resize", "toDataURL", "targetFo", "targetCtx", "element", "append<PERSON><PERSON><PERSON>", "drawImage", "css", "pointerEvents", "mixedBlendMode", "opacity", "clipRect", "zIndex", "g", "box", "clippedElement", "clip", "destroyGraphics", "destroyElements", "seriesProp", "eachAsync", "arr", "finalFunc", "chunkSize", "noTimeout", "proceed", "requestAnimationFrame", "hasExtremes", "checkX", "dataLength", "dataTable", "colorAxis", "getSeriesBoosting", "forceCrop", "onSeriesDestroy", "hoverPoints", "filter", "onSeriesHide", "renderIfNotSeriesBoosting", "getPoint", "boostPoint", "PointClass", "pointClass", "isScatter", "category", "categories", "dist", "distX", "plotX", "percentage", "isInside", "isPointInside", "scatterProcessData", "force", "isDirty", "setTickInterval", "cropThreshold", "old", "getExtremesFromAll", "processedData", "processedXData", "processedYData", "xRangeNeeded", "y<PERSON>ange<PERSON><PERSON>ed", "cropped", "xDataMax", "xDataMin", "yDataMax", "yDataMin", "iEnd", "cropStart", "seriesRenderCanvas", "chartBoost", "seriesBoost", "lowData", "highData", "pointTaken", "sampling", "enableMouseTracking", "requireSorting", "compareX", "findNearestPointBy", "xDataFull", "nullYSubstitute", "nullInteraction", "lastClientX", "maxVal", "minI", "maxI", "graph", "addKDPoint", "clientX", "pushPoint", "ceil", "buildKDTree", "lineWidthFilter", "tagName", "children", "attributes", "operator", "wrapSeriesDrawPoints", "wrapSeriesGetExtremes", "apply", "arguments", "wrapSeriesProcessData", "dataToMeasure", "getFirstValidPoint", "enterBoost", "bp", "alteredByBoost", "altered", "own", "allowDG", "directTouch", "stickyTracking", "finishedAnimating", "labelBySeries", "exitBoost", "setting", "wrapSeriesSearchPoint", "Boost_BoostSeries", "SeriesClass", "seriesTypes", "plotOptions", "seriesProto", "renderCanvas", "method", "wrapSeriesFunctions", "branch", "letItPass", "typePlotOptions", "area", "AreaSeries", "areaspline", "AreaSplineSeries", "bubble", "BubbleSeries", "ColumnSeries", "heatmap", "HeatmapSeries", "scatter", "ScatterSeries", "treemap", "TreemapSeries", "bubbleProto", "SC", "BoostCanvas_getBoostClipRect", "BoostCanvas_isChartSeriesBoosting", "BoostCanvas_destroyGraphics", "BoostCanvas_color", "BoostCanvas_doc", "BoostCanvas_noop", "BoostCanvas_addEvent", "BoostCanvas_fireEvent", "BoostCanvas_isNumber", "BoostCanvas_merge", "BoostCanvas_pick", "BoostCanvas_wrap", "ChartConstructor", "destroyLoadingDiv", "b64BlankPixel", "areaCvsDrawPoint", "lastPoint", "bubbleCvsMarkerCircle", "radii", "columnCvsDrawPoint", "rect", "onChartCanvasToSVG", "onChartClear", "clearRect", "seriesCanvasToSVG", "seriesCvsLineTo", "seriesGetContext", "swapXY", "c", "style", "activeBoostSettings", "boostSettings", "strokeBatch", "cvsStrokeBatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doFill", "loadingOptions", "loading", "cvsDrawPoint", "cvsLineTo", "cvs<PERSON><PERSON><PERSON>", "cvsMarkerSquare", "cvsMarkerCircle", "labelStyle", "backgroundColor", "padding", "borderRadius", "clearTimeout", "showLoading", "<PERSON><PERSON><PERSON>", "drawPoint", "lineJoin", "scroller", "className", "isNull", "isNextInside", "isPrevInside", "NaN", "round", "loadingDiv", "loadingShown", "transition", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "loadingSpan", "scatterCvsMarkerCircle", "scatterCvsMarkerSquare", "wrapHeatmapDrawPoints", "areaProto", "columnProto", "scatterProto", "Extensions_BoostCanvas", "G", "initCanvasBoost", "Chart", "Series", "boost_canvas_src", "default"], "mappings": "IAisFIA,EAAOC,CArrFX,WAAYC,MAA6D,sBAAuB,CAEvF,IAyvEEC,EADPA,EAy/CAC,EAjvHSC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDvB,EAAwD,OAAU,CAC7H,IAAIwB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAsCvD,IAAME,EAlBhB,CACf,OACA,aACA,YACA,SACA,cACA,MACA,OACA,UACA,UACA,SACA,UACH,CAiCKC,EAAe,CAAC,EACtBD,EAAiBE,OAAO,CAAC,AAACC,IACtBF,CAAY,CAACE,EAAK,CAAG,CAAA,CACzB,GAuBA,GAAM,CAAEC,SAAAA,CAAQ,CAAE,CAAIL,IAEhB,CAAEM,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAIR,IA2ExC,SAASS,EAAsBC,CAAK,EAChC,IAAMC,EAAYD,EAAME,MAAM,CAAEC,EAAQH,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAAGC,EAAeJ,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,EAAGG,EAAYT,EAAKO,EAAaG,eAAe,CAAE,IAClK,GAAIN,EAAUO,MAAM,EAAIF,EACpB,MAAO,CAAA,EAEX,GAAIL,AAAqB,IAArBA,EAAUO,MAAM,CAChB,MAAO,CAAA,EAEX,IAAIC,EAAkBL,EAAaM,UAAU,CAC7C,GAAI,AAA2B,KAAA,IAApBD,EAEP,CAAA,IAAK,IAAME,KADXF,EAAkB,CAAA,EACCT,EAAMY,KAAK,EAC1B,GAAIf,EAAKc,EAAKE,GAAG,CAAE,CAACC,KAAYjB,EAAKc,EAAKI,OAAO,CAAE,CAACD,MAChDjB,EAAKc,EAAKK,GAAG,CAAEF,KAAYjB,EAAKc,EAAKM,OAAO,CAAEH,KAAW,CACzDL,EAAkB,CAAA,EAClB,KACJ,CACJ,CAEJ,GAAI,AAAiC,KAAA,IAA1BN,EAAMe,eAAe,CAAkB,CAC9C,GAAIT,EACA,OAAON,EAAMe,eAAe,AAEhCf,CAAAA,EAAMe,eAAe,CAAG,KAAK,CACjC,CAGA,IAAIC,EAAgB,EAAGC,EAAiB,EAAGC,EAC3C,IAAK,IAAMnB,KAAUD,EAMoB,IAAjCoB,AALJA,CAAAA,EAAgBnB,EAAOG,OAAO,AAAD,EAKXiB,cAAc,EAC5BpB,AAAmB,CAAA,IAAnBA,EAAOqB,OAAO,EAMdrB,AAAgB,YAAhBA,EAAOsB,IAAI,GAGXC,AA1I6CjC,CA0I3B,CAACU,EAAOsB,IAAI,CAAC,EAC/B,EAAEL,EAEFO,AAmGZ,SAAoB,GAAGC,CAAI,EACvB,IAAIC,EAAI,CAACC,OAAOC,SAAS,CAWzB,OAVAH,EAAKlC,OAAO,CAAC,AAACsC,IACV,GAAI,MAAOA,GAEP,AAAoB,KAAA,IAAbA,EAAEvB,MAAM,EACXuB,EAAEvB,MAAM,CAAG,EAEX,OADAoB,EAAIG,EAAEvB,MAAM,CACL,CAAA,CAGnB,GACOoB,CACX,EAhHuB1B,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOX,EAAcY,IAAI,CAE9D/B,EAAOgC,MAAM,GAAMb,CAAAA,EAAcC,cAAc,EAAIO,OAAOC,SAAS,AAAD,GAC9D,EAAEV,GAUV,OAPAjB,EAAMe,eAAe,CAAGT,GAAoB,CAAA,AAI5CU,IAAkBlB,EAAUO,MAAM,EAC9BY,IAAmBD,GACnBC,EAAiB,CAAA,EACdjB,EAAMe,eAAe,AAChC,CAKA,SAASiB,EAAgBnC,CAAK,EAK1B,SAASoC,IACDpC,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAACkC,GAAG,EACftC,EAAsBC,IACtBA,EAAMG,KAAK,CAACkC,GAAG,CAACC,MAAM,CAACtC,EAE/B,CA6BAJ,EAASI,EAAO,UAxBhB,WAEIA,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAC9BH,EAAMG,KAAK,CAACe,eAAe,CAAG,KAAK,EACnClB,EAAMuC,OAAO,CAAG,CAAA,EAEXvC,EAAMwC,IAAI,CAACC,IAAI,CAAC,AAAC9B,GAASA,EAAK+B,SAAS,GACzC1C,EAAMG,KAAK,CAACwC,KAAK,KAEjB3C,EAAMG,KAAK,CAACyC,MAAM,EAClB5C,EAAMG,KAAK,CAACkC,GAAG,EACftC,EAAsBC,IAEtBA,EAAMG,KAAK,CAACkC,GAAG,CAACQ,cAAc,CAAC7C,GAG/BA,EAAMG,KAAK,CAAC2C,WAAW,EACvB9C,EAAMY,KAAK,EACXZ,EAAMY,KAAK,CAACJ,MAAM,CAAG,GACrBR,EAAM+C,KAAK,EACX/C,EAAM+C,KAAK,CAACvC,MAAM,CAAG,GACrBR,EAAMG,KAAK,CAAC2C,WAAW,CAACE,SAAS,CAAChD,EAAMY,KAAK,CAAC,EAAE,CAACqC,GAAG,CAAEjD,EAAM+C,KAAK,CAAC,EAAE,CAACE,GAAG,CAEhF,GAIArD,EAASI,EAAO,OAAQoC,EAAa,CAAEc,MAAO,EAAG,GACjDtD,EAASI,EAAO,SAAUoC,GAC1B,IAAIe,EAAQ,GACRC,EAAQ,GACZxD,EAASI,EAAMqD,OAAO,CAAE,oBAAqB,AAACC,IAC1C,IAAMpD,EAASoD,EAAEC,UAAU,EAAErD,OAE7B,GADAF,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAC1BH,EAAMG,KAAK,CAAC2C,WAAW,EAAI5C,EAAQ,CACnC,IAAMU,EAAQZ,EAAMwD,QAAQ,CAAGtD,EAAO6C,KAAK,CAAG7C,EAAOU,KAAK,CACpDmC,EAAQ/C,EAAMwD,QAAQ,CAAGtD,EAAOU,KAAK,CAAGV,EAAO6C,KAAK,CACtD,CAAA,AAACnC,GAASA,EAAMqC,GAAG,GAAKE,GACvBJ,GAASA,EAAME,GAAG,GAAKG,CAAK,IAI7BpD,EAAME,MAAM,CAACT,OAAO,CAAC,AAACgE,IAClBA,EAAEC,IAAI,EAAEC,MACZ,GAIA3D,EAAMG,KAAK,CAAC2C,WAAW,CAACE,SAAS,CAACpC,EAAMqC,GAAG,CAAEF,EAAME,GAAG,EACtDE,EAAQvC,EAAMqC,GAAG,CACjBG,EAAQL,EAAME,GAAG,CAEzB,CACJ,EACJ,CAkC6B,IAAMW,EALhB,CACfC,QAxOJ,SAAiBC,CAAU,CAAEC,CAAO,EAIhC,OAHIA,GAAWjE,EAAWH,EAAU,gBAChCmE,EAAW5E,SAAS,CAAC8E,SAAS,CAACC,IAAI,CAAC9B,GAEjC2B,CACX,EAoOII,iBA3NJ,SAA0BlE,CAAK,CAAEmE,CAAM,EACnC,IAAMC,EAAYpE,EAAMoE,SAAS,CAC7BC,EAAU,CACVC,EAAGtE,EAAMuE,QAAQ,CACjBC,EAAGxE,EAAMyE,OAAO,CAChBC,MAAO1E,EAAM2E,SAAS,CACtBC,OAAQ5E,EAAM6E,UAAU,AAC5B,EAWA,GAVIT,GAAapE,EAAMwD,QAAQ,EAC3Ba,EAAQK,KAAK,EAAIN,EAAUU,GAAG,CAAGV,EAAUQ,MAAM,CAC5CR,EAAUW,QAAQ,EACnBV,CAAAA,EAAQC,CAAC,CAAGF,EAAUY,IAAI,AAAD,GAGxBZ,GAAa,CAACpE,EAAMwD,QAAQ,EACjCa,CAAAA,EAAQO,MAAM,CAAGR,EAAUU,GAAG,CAAGV,EAAUQ,MAAM,CAAG5E,EAAMyE,OAAO,AAAD,EAGhEN,EAAOc,EAAE,CAAE,CACX,GAAM,CAAErE,MAAAA,CAAK,CAAEmC,MAAAA,CAAK,CAAE,CAAGoB,EAEzB,GADAE,EAAUrE,EAAMkF,UAAU,CAACf,GACvBnE,EAAMwD,QAAQ,CAAE,CAChB,IAAM2B,EAAUd,EAAQK,KAAK,AAC7BL,CAAAA,EAAQK,KAAK,CAAGL,EAAQO,MAAM,CAC9BP,EAAQO,MAAM,CAAGO,EACjBd,EAAQC,CAAC,CAAGvB,EAAME,GAAG,CACrBoB,EAAQG,CAAC,CAAG5D,EAAMqC,GAAG,AACzB,MAEIoB,EAAQC,CAAC,CAAG1D,EAAMqC,GAAG,CACrBoB,EAAQG,CAAC,CAAGzB,EAAME,GAAG,AAE7B,CACA,GAAIkB,IAAWnE,EAAO,CAClB,IAAMoF,EAAepF,EAAMwD,QAAQ,CAAGxD,EAAMY,KAAK,CAAGZ,EAAM+C,KAAK,AAC3DqC,CAAAA,EAAa5E,MAAM,EAAI,IACvB6D,EAAQG,CAAC,CAAGa,KAAKxE,GAAG,CAACuE,CAAY,CAAC,EAAE,CAACnC,GAAG,CAAEoB,EAAQG,CAAC,EACnDH,EAAQO,MAAM,CAAIQ,CAAY,CAAC,EAAE,CAACnC,GAAG,CACjCjD,EAAMyE,OAAO,CACbW,CAAY,CAAC,EAAE,CAACE,GAAG,CAE/B,CACA,OAAOjB,CACX,EAiLItE,sBAAAA,CACJ,EAIMwF,EAA2DzH,EAAwD,OAAU,CAAC0H,KAAK,CACzI,IAAIC,EAAgExH,EAAoBC,CAAC,CAACqH,GAqC7D,IAAMG,EAlBf,CAChB,KAAQ,QACR,UAAa,QACb,WAAc,QACd,OAAU,QACV,YAAe,QACf,IAAO,QACP,KAAQ,aACR,QAAW,SACX,QAAW,YACX,QAAW,YACX,OAAU,QACd,EAsBM,CAAEC,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE/F,KAAMgG,CAAc,CAAE,CAAIvG,IAgcbwG,EAjQnC,MAMIC,YAAYC,CAAE,CAAE,CAKZ,GAHA,IAAI,CAACC,MAAM,CAAG,EAAE,CAChB,IAAI,CAACC,UAAU,CAAG,CAAC,EACnB,IAAI,CAACF,EAAE,CAAGA,EACNA,GAAM,CAAC,IAAI,CAACG,YAAY,GACxB,MAER,CAYAC,MAAO,CACC,IAAI,CAACJ,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,CAE7C,CAMAF,cAAe,CACX,IAAMI,EAAI,IAAI,CAACC,eAAe,CA/LjB,ygHA+LgC,UAAWC,EAAI,IAAI,CAACD,eAAe,CA9NjE,+bA8NkF,YAAaE,EAAO,AAACxI,GAAO,IAAI,CAAC8H,EAAE,CAACW,kBAAkB,CAAC,IAAI,CAACN,aAAa,CAAEnI,UAC5K,AAAI,AAACqI,GAAME,GAKX,IAAI,CAACJ,aAAa,CAAG,IAAI,CAACL,EAAE,CAACY,aAAa,GAC1C,IAAI,CAACZ,EAAE,CAACa,YAAY,CAAC,IAAI,CAACR,aAAa,CAAEE,GACzC,IAAI,CAACP,EAAE,CAACa,YAAY,CAAC,IAAI,CAACR,aAAa,CAAEI,GACzC,IAAI,CAACT,EAAE,CAACc,WAAW,CAAC,IAAI,CAACT,aAAa,EACjC,IAAI,CAACL,EAAE,CAACe,mBAAmB,CAAC,IAAI,CAACV,aAAa,CAAE,IAAI,CAACL,EAAE,CAACgB,WAAW,IAMxE,IAAI,CAAChB,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,EACrC,IAAI,CAACL,EAAE,CAACiB,kBAAkB,CAAC,IAAI,CAACZ,aAAa,CAAE,EAAG,mBAClD,IAAI,CAACa,QAAQ,CAAGR,EAAK,YACrB,IAAI,CAACS,SAAS,CAAGT,EAAK,SACtB,IAAI,CAACU,SAAS,CAAGV,EAAK,aACtB,IAAI,CAACW,eAAe,CAAGX,EAAK,YAC5B,IAAI,CAACY,oBAAoB,CAAGZ,EAAK,iBACjC,IAAI,CAACa,qBAAqB,CAAGb,EAAK,oBAClC,IAAI,CAACc,eAAe,CAAGd,EAAK,YAC5B,IAAI,CAACe,sBAAsB,CAAGf,EAAK,mBACnC,IAAI,CAACgB,eAAe,CAAGhB,EAAK,YAC5B,IAAI,CAACiB,UAAU,CAAGjB,EAAK,cAChB,CAAA,IAjBH,IAAI,CAACT,MAAM,CAAChC,IAAI,CAAC,IAAI,CAAC+B,EAAE,CAAC4B,iBAAiB,CAAC,IAAI,CAACvB,aAAa,GAC7D,IAAI,CAACwB,YAAY,GACjB,IAAI,CAACxB,aAAa,CAAG,CAAA,EACd,CAAA,IAZP,IAAI,CAACA,aAAa,CAAG,CAAA,EACrB,IAAI,CAACwB,YAAY,GACV,CAAA,EAyBf,CAKAA,cAAe,CACP,IAAI,CAAC5B,MAAM,CAACzF,MAAM,EAClBoF,EAAM,qCACF,IAAI,CAACK,MAAM,CAAC6B,IAAI,CAAC,MAE7B,CASAtB,gBAAgBuB,CAAG,CAAEvG,CAAI,CAAE,CACvB,IAAMwG,EAAS,IAAI,CAAChC,EAAE,CAACG,YAAY,CAAC3E,AAAS,WAATA,EAAoB,IAAI,CAACwE,EAAE,CAACiC,aAAa,CAAG,IAAI,CAACjC,EAAE,CAACkC,eAAe,QAGvG,CAFA,IAAI,CAAClC,EAAE,CAACmC,YAAY,CAACH,EAAQD,GAC7B,IAAI,CAAC/B,EAAE,CAACoC,aAAa,CAACJ,GACjB,IAAI,CAAChC,EAAE,CAACqC,kBAAkB,CAACL,EAAQ,IAAI,CAAChC,EAAE,CAACsC,cAAc,GAOvDN,GANH,IAAI,CAAC/B,MAAM,CAAChC,IAAI,CAAC,kBACbzC,EACA,aACA,IAAI,CAACwE,EAAE,CAACuC,gBAAgB,CAACP,IACtB,CAAA,EAGf,CAKAQ,SAAU,CACF,IAAI,CAACxC,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAACyC,aAAa,CAAC,IAAI,CAACpC,aAAa,EACxC,IAAI,CAACA,aAAa,CAAG,CAAA,EAE7B,CACAqC,kBAAmB,CACf,OAAO,IAAI,CAACtB,SAAS,AACzB,CAOAuB,YAAa,CACT,OAAO,IAAI,CAACtC,aAAa,AAC7B,CACAuC,kBAAmB,CACf,OAAO,IAAI,CAACzB,SAAS,AACzB,CACA0B,oBAAqB,CACjB,OAAO,IAAI,CAAC3B,QAAQ,AACxB,CAKA4B,OAAQ,CACA,IAAI,CAAC9C,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAAC1B,eAAe,CAAE,GACxC,IAAI,CAACrB,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACrB,eAAe,CAAE,GAEhD,CAOAsB,kBAAkB9I,CAAM,CAAE+I,CAAQ,CAAEC,CAAQ,CAAEC,EAAa,CAAC,CAAE,CAC1D,IAAM9H,EAAgBnB,EAAOG,OAAO,CAChC+I,EAAOvH,OAAOC,SAAS,CAAEuH,EAAO,CAACxH,OAAOC,SAAS,CACrD,GAAI,IAAI,CAACkE,EAAE,EAAI,IAAI,CAACK,aAAa,EAAInG,EAAO+E,EAAE,CAAC,UAAW,CACtD,IAAMqE,EAAUpJ,EAAOqJ,aAAa,GACpCH,EAAOvD,EAAexE,EAAc+H,IAAI,CAAEzD,EAAMsD,EAAU5H,AAAkC,CAAA,IAAlCA,EAAcmI,eAAe,CACnFnI,EAAcoI,UAAU,CAAG,CAAC5H,OAAOC,SAAS,CAAEsH,IAClDC,EAAOxD,EAAexE,EAAcgI,IAAI,CAAEhE,KAAKrE,GAAG,CAACqI,EAAMH,IACzD,IAAI,CAAClD,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAAC1B,eAAe,CAAE,GACxC,IAAI,CAACrB,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACrB,eAAe,CAAE,GACxC,IAAI,CAAC1B,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACxB,qBAAqB,CAAGrH,AAA0B,UAA1BA,EAAOG,OAAO,CAACqJ,MAAM,EACpE,IAAI,CAAC1D,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACzB,oBAAoB,CAAEpH,EAAOG,OAAO,CACtDsJ,mBAAmB,EACxB,IAAI,CAACC,UAAU,CAAC,gBAAiBN,EAAQO,SAAS,CAAGV,GACrD,IAAI,CAACS,UAAU,CAAC,gBAAiBN,EAAQQ,SAAS,CAAGX,GACrD,IAAI,CAACS,UAAU,CAAC,aAAcR,GAC9B,IAAI,CAACQ,UAAU,CAAC,aAAcP,GAC9B,IAAI,CAACO,UAAU,CAAC,mBAAoB1J,EAAOG,OAAO,CAACoJ,UAAU,CACjE,CACJ,CAOAM,SAASC,CAAK,CAAE,CACR,IAAI,CAAChE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiE,SAAS,CAAC,IAAI,CAAC7C,SAAS,CAAE4C,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAExG,CAKAE,gBAAgBC,CAAI,CAAE,CACd,IAAI,CAACnE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACrB,eAAe,CAAEyC,GAAAA,EAEhD,CAOAC,YAAYD,CAAI,CAAE,CACV,IAAI,CAACnE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACpB,UAAU,CAAEwC,EAE3C,CAOAE,WAAWC,CAAC,CAAE,CACN,IAAI,CAACtE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACuE,gBAAgB,CAAC,IAAI,CAACrD,QAAQ,CAAE,CAAA,EAAOoD,EAEvD,CAOAE,aAAaC,CAAC,CAAE,CACR,IAAI,CAACzE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC0E,SAAS,CAAC,IAAI,CAACvD,SAAS,CAAEsD,EAE1C,CAKAE,mBAAmBR,CAAI,CAAE,CACjB,IAAI,CAACnE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACtB,sBAAsB,CAAE0C,CAAAA,CAAAA,AAAS,CAAA,IAATA,CAAY,EAEnE,CAOAS,WAAWC,CAAO,CAAE,CACZ,IAAI,CAAC7E,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC+C,SAAS,CAAC,IAAI,CAACvB,eAAe,CAAEqD,EAEhD,CAUAjB,WAAWkB,CAAI,CAAEC,CAAG,CAAE,CAClB,GAAI,IAAI,CAAC/E,EAAE,EAAI,IAAI,CAACK,aAAa,CAAE,CAC/B,IAAM2E,EAAI,IAAI,CAAC9E,UAAU,CAAC4E,EAAK,CAAI,IAAI,CAAC5E,UAAU,CAAC4E,EAAK,EACpD,IAAI,CAAC9E,EAAE,CAACW,kBAAkB,CAAC,IAAI,CAACN,aAAa,CAAEyE,GACnD,IAAI,CAAC9E,EAAE,CAAC0E,SAAS,CAACM,EAAGD,EACzB,CACJ,CACJ,EAuMmCE,EA/JnC,MAMIlF,YAAYC,CAAE,CAAEgC,CAAM,CAAEkD,CAAc,CAEpC,CAME,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,YAAY,CAAG,CAAA,EACpB,IAAI,CAACC,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,UAAU,CAAGL,GAAkB,EACpC,IAAI,CAACA,cAAc,CAAGA,EACtB,IAAI,CAAClF,EAAE,CAAGA,EACV,IAAI,CAACgC,MAAM,CAAGA,CAClB,CAWAwD,SAASC,CAAI,CAAE,CACX,IAAI,CAACL,QAAQ,CAAG,GAChB,IAAI,CAACC,YAAY,CAAG,IAAIK,aAAaD,AAAO,EAAPA,EACzC,CAKArF,MAAO,CACH,GAAI,CAAC,IAAI,CAAC+E,MAAM,CACZ,MAAO,CAAA,EAKX,IAAI,CAACnF,EAAE,CAAC2F,mBAAmB,CAAC,IAAI,CAACL,aAAa,CAAE,IAAI,CAACC,UAAU,CAAE,IAAI,CAACvF,EAAE,CAAC4F,KAAK,CAAE,CAAA,EAAO,EAAG,EAE9F,CAWAC,MAAMC,CAAM,CAAEC,CAAM,CAAEb,CAAc,CAAE,CAClC,IAAIc,QAEJ,CADA,IAAI,CAAC/J,IAAI,CAAG6J,GAAU,EAAE,CACpB,AAAE,IAAI,CAAC7J,IAAI,EAAI,AAAqB,IAArB,IAAI,CAACA,IAAI,CAACzB,MAAM,EAAY,IAAI,CAAC6K,YAAY,GAKhE,IAAI,CAACE,UAAU,CAAGL,GAAkB,IAAI,CAACK,UAAU,CAC/C,IAAI,CAACJ,MAAM,EACX,IAAI,CAACnF,EAAE,CAACiG,YAAY,CAAC,IAAI,CAACd,MAAM,EAE/B,IAAI,CAACE,YAAY,EAClBW,CAAAA,EAAS,IAAIN,aAAa,IAAI,CAACzJ,IAAI,CAAA,EAEvC,IAAI,CAACkJ,MAAM,CAAG,IAAI,CAACnF,EAAE,CAACkG,YAAY,GAClC,IAAI,CAAClG,EAAE,CAACmG,UAAU,CAAC,IAAI,CAACnG,EAAE,CAACoG,YAAY,CAAE,IAAI,CAACjB,MAAM,EACpD,IAAI,CAACnF,EAAE,CAACqG,UAAU,CAAC,IAAI,CAACrG,EAAE,CAACoG,YAAY,CAAE,IAAI,CAACf,YAAY,EAAIW,EAAQ,IAAI,CAAChG,EAAE,CAACsG,WAAW,EAEzF,IAAI,CAAChB,aAAa,CAAG,IAAI,CAACtF,EAAE,CACvBuG,iBAAiB,CAAC,IAAI,CAACvE,MAAM,CAACW,UAAU,GAAIoD,GACjD,IAAI,CAAC/F,EAAE,CAACwG,uBAAuB,CAAC,IAAI,CAAClB,aAAa,EAElDU,EAAS,CAAA,EACF,CAAA,IAnBH,IAAI,CAACxD,OAAO,GACL,CAAA,EAmBf,CAIAA,SAAU,CACF,IAAI,CAAC2C,MAAM,GACX,IAAI,CAACnF,EAAE,CAACiG,YAAY,CAAC,IAAI,CAACd,MAAM,EAChC,IAAI,CAACA,MAAM,CAAG,CAAA,EACd,IAAI,CAACG,aAAa,CAAG,CAAA,GAEzB,IAAI,CAACF,QAAQ,CAAG,EAChB,IAAI,CAACG,UAAU,CAAG,IAAI,CAACL,cAAc,EAAI,EACzC,IAAI,CAACjJ,IAAI,CAAG,EAAE,AAClB,CAaAgC,KAAKK,CAAC,CAAEE,CAAC,CAAEjG,CAAC,CAAEkO,CAAC,CAAE,CACT,IAAI,CAACpB,YAAY,GACjB,IAAI,CAACA,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG9G,EACrC,IAAI,CAAC+G,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG5G,EACrC,IAAI,CAAC6G,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG7M,EACrC,IAAI,CAAC8M,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAGqB,EAE7C,CAYAnK,OAAOoK,CAAI,CAAEC,CAAE,CAAEC,CAAQ,CAAE,CACvB,IAAMpM,EAAS,IAAI,CAAC6K,YAAY,CAC5B,IAAI,CAACA,YAAY,CAAC7K,MAAM,CAAG,IAAI,CAACyB,IAAI,CAACzB,MAAM,OAC/C,EAAK,IAAI,CAAC2K,MAAM,IAGX3K,IAGD,CAAA,CAACkM,GAAQA,EAAOlM,GAAUkM,EAAO,CAAA,GACjCA,CAAAA,EAAO,CAAA,EAEP,CAAA,CAACC,GAAMA,EAAKnM,CAAK,GACjBmM,CAAAA,EAAKnM,CAAK,GAEVkM,CAAAA,GAAQC,CAAC,IAGbC,EAAWA,GAAY,SACvB,IAAI,CAAC5G,EAAE,CAAC6G,UAAU,CAAC,IAAI,CAAC7G,EAAE,CAAC4G,EAAS,CAAEF,EAAO,IAAI,CAACnB,UAAU,CAAE,AAACoB,CAAAA,EAAKD,CAAG,EAAK,IAAI,CAACnB,UAAU,EACpF,CAAA,GACX,CACJ,EAsBM,CAAEuB,MAAO9C,CAAK,CAAE,CAAIvE,IAEpB,CAAEsH,IAAAA,CAAG,CAAEC,IAAAA,CAAG,CAAE,CAAI1N,IAEhB,CAAE2N,SAAAA,CAAQ,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEvN,KAAMwN,CAAgB,CAAE,CAAI/N,IAUrEgO,EAAQ,CACV,OAAU,CAAA,EACV,YAAe,CAAA,EACf,IAAO,CAAA,EACP,KAAQ,CAAA,EACR,WAAc,CAAA,EACd,UAAa,CAAA,CACjB,EACMC,EAAW,CACb,QAAW,CAAA,EACX,OAAU,CAAA,CACd,EACMC,EAAW,CACb,QACA,qBACA,YACA,YACH,AAmBD,OAAMC,EAcF,OAAOC,YAAYhJ,CAAK,CAAEE,CAAM,CAAE,CAE9B,MAAO,CACH,EAAIF,EAAO,EAAG,EAAG,EACjB,EAAG,CAAE,CAAA,EAAIE,CAAK,EAAI,EAAG,EACrB,EAAG,EAAG,GAAmB,EACzB,GAAI,EAAG,GAA8B,EACxC,AACL,CAIA,OAAO+I,iBAAiBzN,CAAM,CAAE,CAC5B,IAAI0N,EAAWC,EAAOpK,SACtB,AAAIvD,EAAOqC,OAAO,EACdqL,EAAY,CAAC,CAAC1N,EAAOG,OAAO,CAACyN,QAAQ,CACrCD,EAAS,AAAC3N,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAClCN,EAAO8B,SAAS,CAAC,KACjB,KAAK,CAAA,GACL9B,EAAOG,OAAO,CAACwN,KAAK,EACpB3N,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAC1ByB,EAAI,AAACmK,CAAAA,EAAY1N,EAAO+B,IAAI,CAAI4L,GAAS3N,EAAOG,OAAO,CAAC4B,IAAI,EACvDzB,MAAM,CACPN,AAAgB,YAAhBA,EAAOsB,IAAI,CACXiC,GAAK,GAEAvD,AAAgB,YAAhBA,EAAOsB,IAAI,CAChBiC,GAAK,EAEA6J,CAAK,CAACpN,EAAOsB,IAAI,CAAC,EACvBiC,CAAAA,GAAK,CAAA,EAEFA,GAEJ,CACX,CAMAsC,YAAYgI,CAAkB,CAAE,CAK5B,IAAI,CAAC9L,IAAI,CAAG,EAAE,CAEd,IAAI,CAAC2C,MAAM,CAAG,EAEd,IAAI,CAACoJ,QAAQ,CAAG,CAAA,EAEhB,IAAI,CAACC,UAAU,CAAG,EAAE,CAEpB,IAAI,CAAC/N,MAAM,CAAG,EAAE,CAEhB,IAAI,CAACgO,cAAc,CAAG,CAAC,EAEvB,IAAI,CAACxJ,KAAK,CAAG,EACb,IAAI,CAACqJ,kBAAkB,CAAGA,EAC1B,IAAI,CAACI,QAAQ,CAAG,CACZC,UAAW,EACXC,UAAW,EACXC,UAAW,UACXC,SAAU,CAAA,EACVC,gBAAiB,CAAA,EACjBC,mBAAoB,CAAA,EACpBC,MAAO,CACHC,cAAe,CAAA,EACfC,qBAAsB,CAAA,EACtBC,UAAW,CAAA,EACXC,eAAgB,CAAA,EAChBC,WAAY,CAAA,EACZC,gBAAiB,CAAA,CACrB,CACJ,CACJ,CASAC,eAAgB,CACZ,OAAO,IAAI,CAACd,QAAQ,CAAChF,UAAU,EAAI6D,EAAIkC,gBAAgB,EAAI,CAC/D,CAIAC,WAAW9O,CAAO,CAAE,CAIV,eAAgBA,GAClBA,CAAAA,EAAQ8I,UAAU,CAAG,CAAA,EAEzBgE,EAAM,CAAA,EAAM,IAAI,CAACgB,QAAQ,CAAE9N,EAC/B,CAKAwC,eAAe7C,CAAK,CAAE,CAClB,IAAMoP,EAAU,IAAI,CAACA,OAAO,CACxB3L,EAAI,CACH,CAAA,IAAI,CAAC0K,QAAQ,CAACK,eAAe,GAGlCxO,EAAME,MAAM,CAACT,OAAO,CAAC,AAACS,IACdA,EAAOqC,OAAO,EACdkB,CAAAA,GAAKgK,EAAYE,gBAAgB,CAACzN,EAAM,CAEhD,GACAkP,GAAWA,EAAQ5D,QAAQ,CAAC/H,GAChC,CAIA4L,8BAA8BnP,CAAM,CAAE,CAClC,IAAMkP,EAAU,IAAI,CAACA,OAAO,CACxB3L,EAAI,CACH,CAAA,IAAI,CAAC0K,QAAQ,CAACK,eAAe,GAG9BtO,EAAOqC,OAAO,EACdkB,CAAAA,EAAIgK,EAAYE,gBAAgB,CAACzN,EAAM,EAE3CkP,GAAWA,EAAQ5D,QAAQ,CAAC/H,GAChC,CAKAd,OAAQ,CACJ,IAAMqD,EAAK,IAAI,CAACA,EAAE,AAClBA,CAAAA,GAAMA,EAAGrD,KAAK,CAACqD,EAAGsJ,gBAAgB,CAAGtJ,EAAGuJ,gBAAgB,CAC5D,CAOAC,eAAetP,CAAM,CAAEuP,CAAI,CAAE,CACzB,IAAMxN,EAAO,IAAI,CAACA,IAAI,CAAEkM,EAAW,IAAI,CAACA,QAAQ,CAAEiB,EAAU,IAAI,CAACA,OAAO,CAAEM,EAAWxP,EAAOyP,aAAa,EACrGzP,AAAmC,aAAnCA,EAAOyP,aAAa,CAAC7H,IAAI,CAAC,KAAsB,CAAE9H,MAAAA,CAAK,CAAEK,QAAAA,CAAO,CAAEuP,OAAAA,CAAM,CAAEhP,MAAAA,CAAK,CAAEmC,MAAAA,CAAK,CAAE,CAAG7C,EAAQ0N,EAAY,CAAC,CAACvN,EAAQyN,QAAQ,CAAE+B,EAAUxP,EAAQ4B,IAAI,CAAE6N,EAAY5P,EAAOU,KAAK,CAACmP,WAAW,GAEnMC,EAAOF,EAAUjP,GAAG,CAAIX,CAAAA,EAAOU,KAAK,CAACqP,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU9O,GAAG,CAAId,CAAAA,EAAOU,KAAK,CAACqP,cAAc,EAAI,CAAA,EAAIE,EAAYjQ,EAAO6C,KAAK,CAACgN,WAAW,GAAIK,EAAOD,EAAUtP,GAAG,CAAIX,CAAAA,EAAO6C,KAAK,CAACkN,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUnP,GAAG,CAAId,CAAAA,EAAO6C,KAAK,CAACkN,cAAc,EAAI,CAAA,EAAIpC,EAAQ,AAAC3N,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAAM3B,EAAQwN,KAAK,EAAI3N,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOsO,EAAQ,AAACpQ,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAAM3B,EAAQiQ,KAAK,EAAIpQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOuO,EAAQ,AAACrQ,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAAM3B,EAAQkQ,KAAK,EAAIrQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOwO,EAAS,CAAC3C,GAASA,AAAiB,IAAjBA,EAAMrN,MAAM,CAU9pBiQ,EAAepQ,EAAQoQ,YAAY,CAInCvO,EAAShC,EAAOgC,MAAM,EAAI,CAAA,EAAOwO,EAAQ9C,EAAY1N,EAAO+B,IAAI,CAAI4L,GAASgC,EAAUc,EAAc,CAAErM,EAAGzC,OAAOC,SAAS,CAAE0C,EAAG,CAAE,EAAGoM,EAAe,CAAEtM,EAAG,CAACzC,OAAOC,SAAS,CAAE0C,EAAG,CAAE,EAA2CqM,EAAiB,AAAuB,KAAA,IAAhB7Q,EAAMpC,KAAK,CAAkBkT,EAAYxD,CAAK,CAACpN,EAAOsB,IAAI,CAAC,CAAEuP,EAAW1Q,EAAQ0Q,QAAQ,EAAI,IAAKC,EAAQ3Q,EAAQ2Q,KAAK,EAAI,CAAA,EAAO1Q,EAAYD,EAAQC,SAAS,CAAE6I,EAAa,IAAI,CAAC8F,aAAa,GAC9atK,EAAYzE,EAAOF,KAAK,CAAC2E,SAAS,CAAEsM,EAAQ,CAAA,EAAOC,EAAQ,CAAA,EAAOC,EAAQC,EAE9EC,EAAU,EAAGC,EAAY,CAAA,EAEzBhN,EAAGE,EAAGlG,EAAGiT,EAAGC,EAAI,GAAIC,EAAK,CAAA,EAAOC,EAAK,CAAA,EAAOC,EAAKC,EAAa,CAAA,EAAOC,EAAa,CAAA,EAAOC,GAAS,CAAA,EAAOC,GAAY,CAAA,EAAOC,GAAY,CAAA,EAAMC,GAAa,CAAA,EAAMC,GAAYC,GAAe,CAAA,EAAOC,GAAU,CAAA,EAAOC,GAAO,EAC3N,GAAIhS,EAAQiS,SAAS,EAAIjS,EAAQiS,SAAS,CAAC9R,MAAM,CAAG,EAChD,MAEAH,CAAAA,EAAQ+R,OAAO,EACfA,CAAAA,GAAU/R,AAAoB,UAApBA,EAAQkS,OAAO,CACrBlS,EAAQ+R,OAAO,CAAGlS,EAAOsS,iBAAiB,CAC1CnS,EAAQ+R,OAAO,AAAD,EAElBpB,IACAkB,GAAa,EAAE,CACflB,EAAMvR,OAAO,CAAC,CAACgT,EAAMjB,KACjB,GAAIiB,EAAKzI,KAAK,CAAE,CACZ,IAAM0I,EAAY1I,EAAMyI,EAAKzI,KAAK,EAAE2I,IAAI,AACxCD,CAAAA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChBR,EAAU,CAACV,EAAE,CAAGkB,EACXP,IAAgB,AAAsB,KAAA,IAAfM,EAAKG,KAAK,EAClCT,CAAAA,GAAeO,CAAQ,CAE/B,CACJ,GACKP,KAGDA,GAAenI,EAFM,AAAC9J,EAAO2S,YAAY,EAAI3S,EAAO2S,YAAY,GAAGC,IAAI,EACnE5S,EAAO8J,KAAK,EACkB2I,IAAI,CACtCR,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,MAGvBnS,EAAMwD,QAAQ,EACdmB,CAAAA,EAAYzE,EAAOF,KAAK,CAAC6E,UAAU,AAAD,EAEtC3E,EAAO6S,mBAAmB,CAAGlR,OAAOC,SAAS,CAK7C,IAAMkR,GAAY,AAAChJ,IACXA,IACAyF,EAAKwD,SAAS,CAAChP,IAAI,CAAC+F,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAAChP,IAAI,CAAC+F,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAAChP,IAAI,CAAC+F,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAAChP,IAAI,CAAC+F,CAAK,CAAC,EAAE,EAEpC,EAKMkJ,GAAU,CAAC5O,EAAGE,EAAG2O,EAAe/E,EAAY,CAAC,CAAEpE,KACjDgJ,GAAUhJ,GAES,IAAfb,GAAqB,CAAA,CAACgF,EAASM,kBAAkB,EACjDgB,EAAK2D,eAAe,AAAD,IACnB9O,GAAK6E,EACL3E,GAAK2E,EACLiF,GAAajF,GAEbgF,EAASK,eAAe,EAAIY,GAC5BA,EAAQnL,IAAI,CAACK,EAAGE,EAAG2O,GAAAA,EAAuB/E,GAC1CiE,IAAQ,IAGRpQ,EAAKgC,IAAI,CAACK,GACVrC,EAAKgC,IAAI,CAACO,GACVvC,EAAKgC,IAAI,CAACkP,EAAgBhK,EAAa,GACvClH,EAAKgC,IAAI,CAACmK,GAElB,EAIMiF,GAAe,KACb5D,EAAK6D,QAAQ,CAAC9S,MAAM,EACpBiP,CAAAA,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC9S,MAAM,CAAG,EAAE,CAACmM,EAAE,CAAG1K,EAAKzB,MAAM,EAAI6R,EAAG,CAEvE,EAKMkB,GAAe,KAKb9D,CAAAA,CAAAA,EAAK6D,QAAQ,CAAC9S,MAAM,EACpBiP,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC9S,MAAM,CAAG,EAAE,CAACkM,IAAI,GAAMzK,CAAAA,EAAKzB,MAAM,EAAI6R,EAAG,CAAC,IAGzEgB,KACA5D,EAAK6D,QAAQ,CAACrP,IAAI,CAAC,CACfyI,KAAMzK,EAAKzB,MAAM,EAAI6R,EACzB,GACJ,EAKMmB,GAAW,CAAClP,EAAGE,EAAGiP,EAAGC,EAAG1J,KAC1BgJ,GAAUhJ,GACVkJ,GAAQ5O,EAAImP,EAAGjP,GACfwO,GAAUhJ,GACVkJ,GAAQ5O,EAAGE,GACXwO,GAAUhJ,GACVkJ,GAAQ5O,EAAGE,EAAIkP,GACfV,GAAUhJ,GACVkJ,GAAQ5O,EAAGE,EAAIkP,GACfV,GAAUhJ,GACVkJ,GAAQ5O,EAAImP,EAAGjP,EAAIkP,GACnBV,GAAUhJ,GACVkJ,GAAQ5O,EAAImP,EAAGjP,EACnB,EAIA,GAFA+O,KAEIrR,GAAUA,EAAO1B,MAAM,CAAG,EAAG,CAG7BiP,EAAK2D,eAAe,CAAG,CAAA,EAEvB3D,EAAK7C,QAAQ,CAAG,YAEZ1K,CAAM,CAAC,EAAE,CAACyR,IAAI,EAAIzR,CAAM,CAAC,EAAE,CAACyR,IAAI,CAACC,YAAY,EAC7C1R,EAAO2R,IAAI,CAAC,CAACtV,EAAGkO,KACZ,GAAIlO,EAAEoV,IAAI,CAAE,CACR,GAAIpV,EAAEoV,IAAI,CAACC,YAAY,CACnBnH,EAAEkH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEX,GAAIrV,EAAEoV,IAAI,CAACC,YAAY,CACnBnH,EAAEkH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEf,CACA,OAAO,CACX,GAEJ1R,EAAOzC,OAAO,CAAC,AAACqU,IACZ,IACIC,EAAQC,EADNC,EAAQH,EAAMG,KAAK,CAEzB,GAAI,AAAiB,KAAA,IAAVA,GACP,CAACC,MAAMD,IACPH,AAAY,OAAZA,EAAMtP,CAAC,EACPsP,EAAMK,SAAS,CAAE,CACjB,GAAI,CAAE7P,EAAAA,EAAI,CAAC,CAAEE,EAAAA,EAAI,CAAC,CAAEE,MAAAA,EAAQ,CAAC,CAAEE,OAAAA,EAAS,CAAC,CAAE,CAAGkP,EAAMK,SAAS,CAK7DJ,EAASC,AAJTA,CAAAA,EAAYhU,EAAMoU,UAAU,CACxBN,EAAM5T,MAAM,CACPmU,YAAY,CAACP,GAClBE,EAAYF,EAAM5T,MAAM,CAAC2S,YAAY,CAACiB,EAAK,CAC7B,CAAC,eAAe,EAAI,EAEtChC,GAAS9H,EAAMgK,EAAUlB,IAAI,EAAEH,IAAI,CACnCb,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IAQT5R,EAAO+E,EAAE,CAAC,aACV8O,EAASA,GAAU,EACnB3C,EAASpH,EAAMgK,EAAUM,MAAM,EAAE3B,IAAI,CACrCvB,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACboC,GAASlP,EAAGE,EAAGE,EAAOE,EAAQwM,GAC9B2C,GAAU,GAUV7T,EAAO+E,EAAE,CAAC,YAAcjF,EAAMwD,QAAQ,GACtCc,EAAI1D,EAAM0E,GAAG,CAAGhB,EAChBE,EAAIzB,EAAMuC,GAAG,CAAGd,EAChBE,EAAQ,CAACA,EACTE,EAAS,CAACA,GAEd4O,GAASlP,EAAIyP,EAAQvP,EAAIuP,EAAQrP,EAASqP,AAAS,EAATA,EAAanP,EAAUmP,AAAS,EAATA,EAAajC,GAClF,CACJ,GACAuB,KACA,MACJ,CAOA,KAAO7B,EAAId,EAAMlQ,MAAM,CAAG,GAAG,CAEzB,GAAI,AAAa,KAAA,IADjBlC,CAAAA,EAAIoS,CAAK,CAAC,EAAEc,EAAE,AAAD,EAET,SAMJ,GAAIX,EACA,MAeJ,IAAM0D,EAAe1E,GAAWA,CAAO,CAAC2B,EAAE,CA+C1C,GA9CI,CAAChB,GAAUtD,EAASqH,EAAc,CAAA,IAC9BA,EAAavK,KAAK,GAClB8H,GAAS9H,EAAMuK,EAAavK,KAAK,EAAE2I,IAAI,CACvCb,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,KAGjBtB,GACAlM,EAAIhG,CAAC,CAAC,EAAE,CACRkG,EAAIlG,CAAC,CAAC,EAAE,CACJoS,CAAK,CAACc,EAAI,EAAE,EACZE,CAAAA,EAAKhB,CAAK,CAACc,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBd,CAAK,CAACc,EAAI,EAAE,EACZC,CAAAA,EAAKf,CAAK,CAACc,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBlT,EAAEkC,MAAM,EAAI,IACZ+Q,EAAIjT,CAAC,CAAC,EAAE,CACJA,CAAC,CAAC,EAAE,CAAGmR,EAAKpG,IAAI,EAChBoG,CAAAA,EAAKpG,IAAI,CAAG/K,CAAC,CAAC,EAAE,AAAD,EAEfA,CAAC,CAAC,EAAE,CAAGmR,EAAKrG,IAAI,EAChBqG,CAAAA,EAAKrG,IAAI,CAAG9K,CAAC,CAAC,EAAE,AAAD,KAKvBgG,EAAIhG,EACJkG,EAAI8L,GAAO,CAACkB,EAAE,CACVd,CAAK,CAACc,EAAI,EAAE,EACZE,CAAAA,EAAKhB,CAAK,CAACc,EAAI,EAAE,AAAD,EAEhBd,CAAK,CAACc,EAAI,EAAE,EACZC,CAAAA,EAAKf,CAAK,CAACc,EAAI,EAAE,AAAD,EAEhBjB,GAASA,EAAM/P,MAAM,GACrB+Q,EAAIhB,CAAK,CAACiB,EAAE,CACRjB,CAAK,CAACiB,EAAE,CAAG/B,EAAKpG,IAAI,EACpBoG,CAAAA,EAAKpG,IAAI,CAAGkH,CAAK,CAACiB,EAAE,AAAD,EAEnBjB,CAAK,CAACiB,EAAE,CAAG/B,EAAKrG,IAAI,EACpBqG,CAAAA,EAAKrG,IAAI,CAAGmH,CAAK,CAACiB,EAAE,AAAD,IAI3B,CAACf,GAAiBnM,CAAAA,AAAM,OAANA,GAAcE,AAAM,OAANA,CAAS,EAAI,CAC7C+O,KACA,QACJ,CA0BA,GAzBI7B,GAAMA,GAAM1B,GAAQ0B,GAAMxB,GAC1B0B,CAAAA,EAAa,CAAA,CAAG,EAEhBH,GAAMA,GAAMzB,GAAQyB,GAAMvB,GAC1B2B,CAAAA,EAAa,CAAA,CAAG,EAEhBnC,GACIc,GACAhM,CAAAA,EAAIlG,EAAEkW,KAAK,CAAC,EAAG,EAAC,EAEpB7C,EAAMzR,EAAO8B,SAAS,CAAC,MAAO,CAAA,IAAO,CAACwP,EAAE,CACxChN,EAAItE,EAAO8B,SAAS,CAAC,OAAQ,CAAA,IAAO,CAACwP,EAAE,EAAI,GAEtC5D,IACLtJ,EAAIhG,EAAEgG,CAAC,CAEPqN,EAAMnN,AADNA,CAAAA,EAAIlG,EAAEmW,MAAM,AAAD,EACDnW,EAAEkG,CAAC,QAEb4L,GAGA,MADAC,GAEA2B,CAAAA,GAAYxN,GAAK4L,GAAQ5L,GAAK6L,CAAG,EAGjC,CAACT,GAAU,CAACoC,KAGZ1N,EAAI4L,GAAQU,EAAatM,CAAC,CAAG4L,IAC7BU,EAAatM,CAAC,CAAGA,EACjBsM,EAAapM,CAAC,CAAGA,GAEjBF,EAAI0L,GAAQW,EAAYrM,CAAC,CAAG0L,IAC5BW,EAAYrM,CAAC,CAAGA,EAChBqM,EAAYnM,CAAC,CAAGA,GAEhBA,AAAM,OAANA,GAAciM,GAVd,SAgBJ,GAAIjM,AAAM,OAANA,GAAe,CAACwN,IAAatB,EAAMlQ,MAAM,CAAG,GAC5C,CAACoR,GAAc,CAACC,EAAa,CAC7B0B,KACA,QACJ,CASA,GALI3D,CAAAA,GAAY8B,CAAAA,GAAM1B,GAAQ1L,GAAK0L,CAAG,GACjCyB,CAAAA,GAAMvB,GAAQ5L,GAAK4L,CAAG,GACvB,CAACN,GAAYtL,GAAK0L,GAAU1L,GAAK4L,CAAK,GACtC6B,CAAAA,GAAY,CAAA,CAAG,EAEf,AAACA,IAAcH,GAAeC,GAOlC,GAJIO,IAAW9N,EAAImN,EAAKW,IACpBmB,KAGAvC,EAAO,CACP,IAAI0B,EACJ1B,EAAMvO,IAAI,CAAC,CACXgQ,EAAMjB,KACF,IAAMkD,EAAO1D,CAAK,CAACQ,EAAI,EAAE,OACzB,AAAIT,AAAa,MAAbA,EACA,AAA0B,KAAA,IAAf0B,EAAKG,KAAK,EACjBtO,GAAKmO,EAAKG,KAAK,GACXV,EAAU,CAACV,EAAE,EACZ,CAAA,CAACkD,GAAQpQ,GAAKoQ,EAAK9B,KAAK,AAAD,GACxBF,CAAAA,EAAYR,EAAU,CAACV,EAAE,AAAD,EAErB,CAAA,GAIf,AAA0B,KAAA,IAAfiB,EAAKG,KAAK,EAAoBpO,GAAKiO,EAAKG,KAAK,GAChDV,EAAU,CAACV,EAAE,EACZ,CAAA,CAACkD,GAAQlQ,GAAKkQ,EAAK9B,KAAK,AAAD,GACxBF,CAAAA,EAAYR,EAAU,CAACV,EAAE,AAAD,EAErB,CAAA,EAGf,GACAM,GAASY,GAAaP,IAAgBL,EAC1C,CAEA,GAAI,AAAC3D,EAASM,kBAAkB,GAC5BgB,EAAK2D,eAAe,CAAG,CAAA,EACvB9O,EAAI1D,EAAM+T,QAAQ,CAACrQ,EAAG,CAAA,GACtBE,EAAIzB,EAAM4R,QAAQ,CAACnQ,EAAG,CAAA,GAQlBF,CAAAA,CAAAA,EAAIK,CAAQ,GAIR8K,AAAkB,WAAlBA,EAAK7C,QAAQ,GA6BzB,GAlBI6C,EAAKmF,UAAU,EAAI7C,IAYfd,AAAU,CAAA,IAAVA,GACA/Q,CAAAA,EAAO6S,mBAAmB,CAAG1N,KAAKxE,GAAG,CAACX,EAAO6S,mBAAmB,CAAE1N,KAAKwP,GAAG,CAACvQ,EAAI2M,GAAM,EAKzF,CAAC9C,EAASM,kBAAkB,EAC5B,CAACN,EAASK,eAAe,EACxByC,GAAS5L,AAlakL,EAkalLA,KAAKwP,GAAG,CAACvQ,EAAI2M,IACtBC,GAAS7L,AAnasM,EAmatMA,KAAKwP,GAAG,CAACrQ,EAAI0M,GAA0B,CAC7C/C,EAASO,KAAK,CAACM,eAAe,EAC9B,EAAEqC,EAEN,QACJ,CACIP,IACAK,EAASQ,GAAO,EACZA,CAAAA,AAAQ,CAAA,IAARA,GAAiB,AAAe,KAAA,IAARA,CAAkB,IAEtCR,EADA3M,EAAI,EACKA,EAGA,GAGZ,CAAA,AAACkL,GAAY9B,CAAQ,IACtB7K,EAAM+R,WAAW,EAEjB3D,CAAAA,EAAS9L,KAAKrE,GAAG,CAACV,AAAc,OAAdA,EAAqB8P,EAAO9P,EAC9C8P,EAAI,EAEHjC,EAASM,kBAAkB,EAC5B0C,CAAAA,EAASpO,EAAM4R,QAAQ,CAACxD,EAAQ,CAAA,EAAI,EAGxC+B,GAAQ5O,EAAG6M,EAAQ,EAAG,EAAGW,KAKzBzR,EAAQ0U,IAAI,EAAI,CAAC9C,IACjBiB,GAAQ5O,EAAG4M,EAAO,EAAG,EAAGY,IAE5BoB,GAAQ5O,EAAGE,EAAG,EAAGtE,AAAgB,WAAhBA,EAAOsB,IAAI,CAAiB+P,GAAK,EAAK,EAAGO,IAS1Db,EAAQ3M,EACR4M,EAAQ1M,EACR8M,EAAY,CAAA,EACZW,GAAa,CAAA,GACjB,CACI9D,EAASO,KAAK,CAACM,eAAe,EAC9BgG,QAAQC,GAAG,CAAC,kBAAmB5D,GAEnC,IAAM6D,GAAsB,CAACpB,EAAOqB,KAQhC,GAPKhH,EAASM,kBAAkB,GAC5BgB,EAAK2D,eAAe,CAAG,CAAA,EACvBU,EAAMxP,CAAC,CAAG1D,EAAM+T,QAAQ,CAACb,EAAMxP,CAAC,CAAE,CAAA,GAClCwP,EAAMtP,CAAC,CAAGzB,EAAM4R,QAAQ,CAACb,EAAMtP,CAAC,CAAE,CAAA,IAIlC2Q,EAAS,CACT,IAAI,CAAClT,IAAI,CAAG,CAAC6R,EAAMxP,CAAC,CAAEwP,EAAMtP,CAAC,CAAE,EAAG,EAAE,CAAC4Q,MAAM,CAAC,IAAI,CAACnT,IAAI,EACrD,MACJ,CACAiR,GAAQY,EAAMxP,CAAC,CAAEwP,EAAMtP,CAAC,CAAE,EAAG,EACjC,CACI,EAAC8M,GACDb,AAAiB,CAAA,IAAjBA,GACAvQ,AAAoB,eAApBA,EAAO0M,QAAQ,GACX+D,EAAYrM,CAAC,CAAGzC,OAAOC,SAAS,EAEhCoT,GAAoBvE,EAAa,CAAA,GAEjCC,EAAatM,CAAC,CAAG,CAACzC,OAAOC,SAAS,EAClCoT,GAAoBtE,IAG5ByC,IACJ,CAQAgC,WAAW5R,CAAC,CAAE,CACV,IAAMwK,EAAa,IAAI,CAACA,UAAU,CAAE/N,EAAS,IAAI,CAACA,MAAM,CAAEiO,EAAW,IAAI,CAACA,QAAQ,AAC9EjO,CAAAA,EAAOM,MAAM,CAAG,GACZN,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAACoU,UAAU,EACpC1U,CAAAA,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAAC8U,QAAQ,CAAGrH,EAAWzN,MAAM,AAAD,EAGzD2N,EAASO,KAAK,CAACE,oBAAoB,EACnCoG,QAAQO,IAAI,CAAC,YAAc9R,EAAEjC,IAAI,CAAG,WAExC,IAAMxC,EAAM,CACRsU,SAAU,EAAE,CACZkC,WAAYvH,EAAWzN,MAAM,CAI7ByS,UAAW,EAAE,CACb/S,OAAQuD,EACR2F,KAAMvH,OAAOC,SAAS,CACtBuH,KAAM,CAACxH,OAAOC,SAAS,CACvB8S,WAAYnR,EAAAA,EAAEpD,OAAO,CAACoV,MAAM,EACxBhS,AAA6B,CAAA,IAA7BA,EAAEpD,OAAO,CAACoV,MAAM,CAACC,OAAO,CAE5BC,YAAa,CAAA,EACb/I,SAAUlH,CAAiB,CAACjC,EAAEjC,IAAI,CAAC,EAAI,YAC3C,CACIiC,CAAAA,EAAE7F,KAAK,EAAIsC,EAAOM,MAAM,CACxBN,EAAO+D,IAAI,CAACjF,GAGZkB,CAAM,CAACuD,EAAE7F,KAAK,CAAC,CAAGoB,EAGtB,IAAI,CAACwQ,cAAc,CAAC/L,EAAGzE,GACnBmP,EAASO,KAAK,CAACE,oBAAoB,EACnCoG,QAAQY,OAAO,CAAC,YAAcnS,EAAEjC,IAAI,CAAG,UAE/C,CAOAqU,OAAQ,CACJ,IAAMzG,EAAU,IAAI,CAACA,OAAO,AAC5B,CAAA,IAAI,CAACnN,IAAI,CAAG,EAAE,CACd,IAAI,CAACgM,UAAU,CAAG,EAAE,CACpB,IAAI,CAAC/N,MAAM,CAAG,EAAE,CACZkP,GACAA,EAAQ5G,OAAO,EAEvB,CAOAsN,SAASnV,CAAI,CAAE,CACX,IAAMqH,EAAS,IAAI,CAACA,MAAM,CAC1B,GAAI,CAACA,EACD,OAEJ,IAAMmB,EAAa,IAAI,CAAC8F,aAAa,GACrCjH,EAAO4B,UAAU,CAAC,aAAcjJ,EAAKoV,MAAM,CAAG5M,GAC9CnB,EAAO4B,UAAU,CAAC,WAAYjJ,EAAKE,GAAG,EACtCmH,EAAO4B,UAAU,CAAC,cAAejJ,EAAKqV,eAAe,CAAG7M,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmBjJ,EAAKsV,UAAU,EACpDjO,EAAO4B,UAAU,CAAC,WAAYjJ,EAAK2E,GAAG,CAAG6D,GACzCnB,EAAO4B,UAAU,CAAC,WAAYjJ,EAAKsC,GAAG,CAAGkG,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAACjJ,EAAKuV,KAAK,EAC/ClO,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAACjJ,EAAKmU,WAAW,EACnD9M,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAACjJ,EAAKwV,QAAQ,CACvD,CAOAC,SAASzV,CAAI,CAAE,CACX,IAAMqH,EAAS,IAAI,CAACA,MAAM,CAC1B,GAAI,CAACA,EACD,OAEJ,IAAMmB,EAAa,IAAI,CAAC8F,aAAa,GACrCjH,EAAO4B,UAAU,CAAC,aAAcjJ,EAAKoV,MAAM,CAAG5M,GAC9CnB,EAAO4B,UAAU,CAAC,WAAYjJ,EAAKE,GAAG,EACtCmH,EAAO4B,UAAU,CAAC,cAAejJ,EAAKqV,eAAe,CAAG7M,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmBjJ,EAAKsV,UAAU,EACpDjO,EAAO4B,UAAU,CAAC,WAAYjJ,EAAK2E,GAAG,CAAG6D,GACzCnB,EAAO4B,UAAU,CAAC,WAAYjJ,EAAKsC,GAAG,CAAGkG,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAACjJ,EAAKuV,KAAK,EAC/ClO,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAACjJ,EAAKmU,WAAW,EACnD9M,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAACjJ,EAAKwV,QAAQ,CACvD,CASAE,aAAaC,CAAG,CAAEC,CAAW,CAAE,CAC3B,IAAMvO,EAAS,IAAI,CAACA,MAAM,CACrBA,IAGLA,EAAO4B,UAAU,CAAC,eAAgB0M,GAClCtO,EAAO4B,UAAU,CAAC,sBAAuB2M,GAC7C,CAMAC,YAAYxW,CAAK,CAAE,CACf,IAAMgG,EAAK,IAAI,CAACA,EAAE,CAAEmI,EAAW,IAAI,CAACA,QAAQ,CAAEnG,EAAS,IAAI,CAACA,MAAM,CAAEoH,EAAU,IAAI,CAACA,OAAO,CACpFjG,EAAa,IAAI,CAAC8F,aAAa,GACrC,IAAIjP,EAKA,MAAO,CAAA,CAJP,CAAA,IAAI,CAAC0E,KAAK,CAAG1E,EAAMyW,UAAU,CAAGtN,EAChC,IAAI,CAACvE,MAAM,CAAG5E,EAAM0W,WAAW,CAAGvN,EAKtC,IAAMvE,EAAS,IAAI,CAACA,MAAM,CAAEF,EAAQ,IAAI,CAACA,KAAK,CAC9C,GAAI,CAACsB,GAAM,CAACgC,GAAU,CAACtD,GAAS,CAACE,EAC7B,MAAO,CAAA,CAEPuJ,CAAAA,EAASO,KAAK,CAACC,aAAa,EAC5BqG,QAAQO,IAAI,CAAC,gBAEjBvP,EAAGpD,MAAM,CAAC8B,KAAK,CAAGA,EAClBsB,EAAGpD,MAAM,CAACgC,MAAM,CAAGA,EACnBoD,EAAO5B,IAAI,GACXJ,EAAG2Q,QAAQ,CAAC,EAAG,EAAGjS,EAAOE,GACzBoD,EAAOqC,UAAU,CAACoD,EAAYC,WAAW,CAAChJ,EAAOE,IAC7CuJ,EAASE,SAAS,CAAG,GAAK,CAAC,AAAC/O,IAA8CsX,IAAI,EAC9E5Q,EAAGqI,SAAS,CAACF,EAASE,SAAS,EAE/Be,IACAA,EAAQvD,KAAK,CAAC,IAAI,CAAC5J,IAAI,CAAE,kBAAmB,GAC5CmN,EAAQhJ,IAAI,IAEhB4B,EAAOoC,WAAW,CAACpK,EAAMwD,QAAQ,EAEjC,IAAI,CAACtD,MAAM,CAACT,OAAO,CAAC,CAACgE,EAAGoT,KACpB,IAAMxW,EAAUoD,EAAEvD,MAAM,CAACG,OAAO,CAAEyW,EAAezW,EAAQoV,MAAM,CAAEpH,EAAa,AAA6B,KAAA,IAAtBhO,EAAQgO,SAAS,CAClGhO,EAAQgO,SAAS,CACjB,EAAI/N,EAAYD,EAAQC,SAAS,CAAEyW,EAAe9J,EAAS3M,GAAY0W,EAAUvT,EAAEvD,MAAM,CAAC6C,KAAK,CAACkU,YAAY,CAAC3W,GAA2CqV,EAActI,EAAiBhN,EAAQoV,MAAM,CAAGpV,EAAQoV,MAAM,CAACC,OAAO,CAAG,KAAMjS,EAAAA,EAAEvD,MAAM,CAACU,KAAK,CAACsW,QAAQ,EAAU,KAAMzT,EAAEvD,MAAM,CAAC6S,mBAAmB,CAC1S,EAAK,CAAA,AAAC1S,CAAAA,EAAQoV,MAAM,CAChBpV,EAAQoV,MAAM,CAAC0B,MAAM,CACrB,EAAC,GAAM,EAAC,GAAKC,EAAe,IAAI,CAAClJ,cAAc,CAAC,AAAC4I,GAAgBA,EAAaO,MAAM,EACxF5T,EAAEvD,MAAM,CAACmX,MAAM,CAAC,EAAI,IAAI,CAACnJ,cAAc,CAACoJ,MAAM,CAC9CC,EAAQC,EAASlJ,EAAW8C,EAAS,EAAE,CAC3C,GAAI3N,AAAsB,IAAtBA,EAAE6P,QAAQ,CAAC9S,MAAM,EACjBiD,EAAE6P,QAAQ,CAAC,EAAE,CAAC5G,IAAI,GAAKjJ,EAAE6P,QAAQ,CAAC,EAAE,CAAC3G,EAAE,CA0F3C,CAAA,GAvFIyK,EAAaK,OAAO,GACpBzR,EAAG0R,WAAW,CAAC1R,EAAG2R,UAAU,CAAEP,EAAaQ,MAAM,EACjD5P,EAAO4C,UAAU,CAACwM,EAAaQ,MAAM,GAErC5X,EAAMoU,UAAU,CACZ3Q,EAAEvD,MAAM,CAAC4C,WAAW,GAAKW,EAAEvD,MAAM,CAACF,KAAK,CAACG,KAAK,EAAE2C,aAE/C,OAAOW,EAAEvD,MAAM,CAAC4C,WAAW,CAC3BW,EAAEvD,MAAM,CAAC4C,WAAW,CAAGW,EAAEvD,MAAM,CAAC2X,SAAS,CAAC,cAAe,UAAW,UAAW,EAAG7X,EAAM8X,WAAW,EAAEC,QAAQ,CAAC,sBAC9GzJ,EAAY7K,EAAEvD,MAAM,CAAC4C,WAAW,CAACkV,QAAQ,CAAC,QAC1CvU,EAAEvD,MAAM,CAAC4C,WAAW,CAAC0F,OAAO,GAC5B/E,EAAEvD,MAAM,CAAC4C,WAAW,CAAGW,EAAEvD,MAAM,CAACF,KAAK,CAACG,KAAK,EAAE2C,aAG7CwL,EAAY7K,EAAEvD,MAAM,CAAC4C,WAAW,EAAEkV,SAAS,SAI/C1J,EACI,AAAgB,WAAf7K,EAAEmJ,QAAQ,EACPnJ,EAAEvD,MAAM,CAAC2S,YAAY,EACrBpP,EAAEvD,MAAM,CAAC2S,YAAY,GAAGC,IAAI,EAC5BrP,EAAEvD,MAAM,CAAC8J,KAAK,CAClB3J,EAAQ4X,YAAY,EACpB3J,CAAAA,EAAY7K,EAAEvD,MAAM,CAACF,KAAK,CAACK,OAAO,CAAC6X,MAAM,CAACrB,EAAG,AAAD,GAGhDpT,EAAEvD,MAAM,CAACiY,WAAW,EAAI9X,EAAQ8X,WAAW,EAC3C7J,CAAAA,EAAY,GAAK7I,CAAAA,GAAiD,EAAG6I,GAAW8J,UAAU,CAAC/K,EAAiBhN,EAAQ8X,WAAW,CAAE,IAAMpZ,GAAG,EAAC,EAE/IqS,EAASpH,EAAMsE,GAAWqE,IAAI,CACzBxE,EAASI,QAAQ,EAClB6C,CAAAA,CAAM,CAAC,EAAE,CAAG,CAAE,EAGd/Q,AAA0B,QAA1BA,EAAQgY,aAAa,EACrBrS,EAAGsS,SAAS,CAACtS,EAAGuS,SAAS,CAAEvS,EAAGwS,GAAG,EACjCxS,EAAGyS,aAAa,CAACzS,EAAG0S,QAAQ,GAEvBrY,AAA0B,SAA1BA,EAAQgY,aAAa,EAC1BhY,AAA0B,aAA1BA,EAAQgY,aAAa,CACrBrS,EAAGsS,SAAS,CAACtS,EAAG2S,SAAS,CAAE3S,EAAG4S,IAAI,EAE7BvY,AAA0B,WAA1BA,EAAQgY,aAAa,EAC1BrS,EAAGsS,SAAS,CAACtS,EAAGwS,GAAG,CAAExS,EAAGwS,GAAG,EAC3BxS,EAAGyS,aAAa,CAACzS,EAAG6S,QAAQ,GAK5B7S,EAAG8S,iBAAiB,CAAC9S,EAAGuS,SAAS,CAAEvS,EAAG+S,mBAAmB,CAAE/S,EAAGwS,GAAG,CAAExS,EAAG+S,mBAAmB,EAE7F/Q,EAAOc,KAAK,GAERrF,EAAEwP,SAAS,CAACzS,MAAM,CAAG,GACrBwH,EAAO4B,UAAU,CAAC,WAAY,GAE9B4N,AADAA,CAAAA,EAAU,IAAIvM,EAAsBjF,EAAIgC,EAAM,EACtC6D,KAAK,CAIbmN,MAAMvV,EAAE6P,QAAQ,CAAC,EAAE,CAAC5G,IAAI,EAAE0I,MAAM,CAAC3R,EAAEwP,SAAS,EAAG,SAAU,GACzDuE,EAAQpR,IAAI,KAKZ4B,EAAO4B,UAAU,CAAC,WAAY,GAG9B5D,EAAGiT,wBAAwB,CAACjT,EAAGuG,iBAAiB,CAACvE,EAAOW,UAAU,GAAI,YAG1EX,EAAO+B,QAAQ,CAACqH,GAChB,IAAI,CAAC0E,QAAQ,CAACrS,EAAEvD,MAAM,CAACU,KAAK,EAC5B,IAAI,CAACwV,QAAQ,CAAC3S,EAAEvD,MAAM,CAAC6C,KAAK,EAC5B,IAAI,CAACsT,YAAY,CAACU,EAtFiIC,GAuFhI,WAAfvT,EAAEmJ,QAAQ,EACV5E,EAAOwC,YAAY,CAAC6C,AAAiE,EAAjEA,EAAiBhN,EAAQoV,MAAM,EAAIpV,EAAQoV,MAAM,CAAC0B,MAAM,CAAE,IAAWhO,GAI7FnB,EAAO2C,kBAAkB,CAAClH,EAAE2P,eAAe,EACrB,WAAlB3P,EAAEvD,MAAM,CAACsB,IAAI,EACbwG,EAAOgB,iBAAiB,CAACvF,EAAEvD,MAAM,CAAEuD,EAAE2F,IAAI,CAAE3F,EAAE4F,IAAI,CAAEF,GAEvDnB,EAAOkC,eAAe,CAACqD,CAAQ,CAAC9J,EAAEvD,MAAM,CAACsB,IAAI,CAAC,EAAI,CAAA,GAC7C4N,GAKL,GAAIf,EAAY,GAAK5K,AAAe,eAAfA,EAAEmJ,QAAQ,CAC3B,IAAK2K,EAAS,EAAGA,EAAS9T,EAAE6P,QAAQ,CAAC9S,MAAM,CAAE+W,IACzCnI,EAAQ9M,MAAM,CAACmB,EAAE6P,QAAQ,CAACiE,EAAO,CAAC7K,IAAI,CAAEjJ,EAAE6P,QAAQ,CAACiE,EAAO,CAAC5K,EAAE,CAAElJ,EAAEmJ,QAAQ,EAGjF,GAAInJ,EAAEmR,UAAU,EAAIe,EAGhB,IAFA3N,EAAOwC,YAAY,CAAC6C,AAA+D,EAA/DA,EAAiBhN,EAAQoV,MAAM,EAAIpV,EAAQoV,MAAM,CAAC0B,MAAM,CAAE,GAAShO,GACvFnB,EAAOkC,eAAe,CAAC,CAAA,GAClBqN,EAAS,EAAGA,EAAS9T,EAAE6P,QAAQ,CAAC9S,MAAM,CAAE+W,IACzCnI,EAAQ9M,MAAM,CAACmB,EAAE6P,QAAQ,CAACiE,EAAO,CAAC7K,IAAI,CAAEjJ,EAAE6P,QAAQ,CAACiE,EAAO,CAAC5K,EAAE,CAAE,UAZvE,CAeJ,GACIwB,EAASO,KAAK,CAACC,aAAa,EAC5BqG,QAAQY,OAAO,CAAC,gBAEhB,IAAI,CAAC7H,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAC,IAAI,EAEhC,IAAI,CAAC8H,KAAK,EACd,CAKAvT,OAAOtC,CAAK,CAAE,CAEV,GADA,IAAI,CAAC2C,KAAK,GACN3C,EAAMkZ,QAAQ,CAACC,SAAS,CACxB,OAAO,IAAI,CAAC3C,WAAW,CAACxW,EAExB,CAAA,IAAI,CAACgO,QAAQ,CACb,IAAI,CAACwI,WAAW,CAACxW,GAGjBoZ,WAAW,KACP,IAAI,CAAC9W,MAAM,CAACtC,EAChB,EAAG,EAEX,CAMAqZ,QAAQ3U,CAAK,CAAEE,CAAM,CAAE,CACnB,IAAMoD,EAAS,IAAI,CAACA,MAAM,CAErBA,GAAW,CAAA,IAAI,CAACtD,KAAK,GAAKA,GAAS,IAAI,CAACE,MAAM,GAAKA,CAAK,IAG7D,IAAI,CAACF,KAAK,CAAGA,EACb,IAAI,CAACE,MAAM,CAAGA,EACdoD,EAAO5B,IAAI,GACX4B,EAAOqC,UAAU,CAACoD,EAAYC,WAAW,CAAChJ,EAAOE,IACrD,CAKA0U,KAAK1W,CAAM,CAAE2W,CAAO,CAAE,CAClB,IAAMpL,EAAW,IAAI,CAACA,QAAQ,CAE9B,GADA,IAAI,CAACH,QAAQ,CAAG,CAAA,EACZ,CAACpL,EACD,MAAO,CAAA,CAEPuL,CAAAA,EAASO,KAAK,CAACG,SAAS,EACxBmG,QAAQO,IAAI,CAAC,YAEjB,IAAK,IAAI/D,EAAI,EAAGA,EAAIhE,EAAShN,MAAM,GAC/B,IAAI,CAACwF,EAAE,CAAGpD,EAAO4W,UAAU,CAAChM,CAAQ,CAACgE,EAAE,CAAE,CAEzC,IACI,IAAI,CAACxL,EAAE,EAJsB,EAAEwL,GAQvC,IAAMxL,EAAK,IAAI,CAACA,EAAE,CAClB,IAAIA,EAMA,MAAO,CAAA,EALFuT,GACD,IAAI,CAAC1D,KAAK,GAMlB7P,EAAGyT,MAAM,CAACzT,EAAG0T,KAAK,EAElB1T,EAAGsS,SAAS,CAACtS,EAAGuS,SAAS,CAAEvS,EAAG+S,mBAAmB,EACjD/S,EAAG2T,OAAO,CAAC3T,EAAG4T,UAAU,EAExB5T,EAAG6T,SAAS,CAAC7T,EAAG8T,IAAI,EACpB,IAAM9R,EAAS,IAAI,CAACA,MAAM,CAAG,IAAIlC,EAAgBE,GACjD,GAAI,CAACgC,EAED,MAAO,CAAA,CAEX,CAAA,IAAI,CAACoH,OAAO,CAAG,IAAInE,EAAsBjF,EAAIgC,GAC7C,IAAM+R,EAAgB,CAACjP,EAAMkP,KACzB,IAAMC,EAAQ,CACVxC,QAAS,CAAA,EACT5M,QAASkC,EAAImN,aAAa,CAAC,UAC3BtC,OAAQ5R,EAAG+T,aAAa,EAC5B,EAAGI,EAAMF,EAAMpP,OAAO,CAAC2O,UAAU,CAAC,KAClC,CAAA,IAAI,CAACtL,cAAc,CAACpD,EAAK,CAAGmP,EAC5BA,EAAMpP,OAAO,CAACnG,KAAK,CAAG,IACtBuV,EAAMpP,OAAO,CAACjG,MAAM,CAAG,IACvBuV,EAAIC,wBAAwB,CAAG,CAAA,EAC/BD,EAAIE,2BAA2B,CAAG,CAAA,EAClCF,EAAIG,uBAAuB,CAAG,CAAA,EAC9BH,EAAII,qBAAqB,CAAG,CAAA,EAC5BJ,EAAIK,WAAW,CAAG,yBAClBL,EAAIM,SAAS,CAAG,OAChBT,EAAGG,GACH,GAAI,CACAnU,EAAG0U,aAAa,CAAC1U,EAAG2U,QAAQ,EAC5B3U,EAAG0R,WAAW,CAAC1R,EAAG2R,UAAU,CAAEsC,EAAMrC,MAAM,EAE1C5R,EAAG4U,UAAU,CAAC5U,EAAG2R,UAAU,CAAE,EAAG3R,EAAG6U,IAAI,CAAE7U,EAAG6U,IAAI,CAAE7U,EAAG8U,aAAa,CAAEb,EAAMpP,OAAO,EACjF7E,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGgV,cAAc,CAAEhV,EAAGiV,aAAa,EACnEjV,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGkV,cAAc,CAAElV,EAAGiV,aAAa,EACnEjV,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGmV,kBAAkB,CAAEnV,EAAGoV,MAAM,EAChEpV,EAAG+U,aAAa,CAAC/U,EAAG2R,UAAU,CAAE3R,EAAGqV,kBAAkB,CAAErV,EAAGoV,MAAM,EAEhEpV,EAAG0R,WAAW,CAAC1R,EAAG2R,UAAU,CAAE,MAC9BsC,EAAMxC,OAAO,CAAG,CAAA,CACpB,CACA,MAAOnU,EAAG,CAEV,CACJ,EA4CA,OA1CAyW,EAAc,SAAU,AAACI,IACrBA,EAAImB,SAAS,GACbnB,EAAIoB,GAAG,CAAC,IAAK,IAAK,IAAK,EAAG,EAAIlW,KAAKmW,EAAE,EACrCrB,EAAI7F,MAAM,GACV6F,EAAIrH,IAAI,EACZ,GAEAiH,EAAc,SAAU,AAACI,IACrBA,EAAIsB,QAAQ,CAAC,EAAG,EAAG,IAAK,IAC5B,GAEA1B,EAAc,UAAW,AAACI,IACtBA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,IAAK,GAChBvB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIrH,IAAI,EACZ,GAEAiH,EAAc,WAAY,AAACI,IACvBA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,EAAG,KACdvB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAIrH,IAAI,EACZ,GAEAiH,EAAc,gBAAiB,AAACI,IAC5BA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,EAAG,GACdvB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIwB,MAAM,CAAC,EAAG,GACdxB,EAAIrH,IAAI,EACZ,GACA,IAAI,CAAC9E,QAAQ,CAAG,CAAA,EACZG,EAASO,KAAK,CAACG,SAAS,EACxBmG,QAAQY,OAAO,CAAC,YAEb,CAAA,CACX,CAKApN,SAAU,CACN,IAAMxC,EAAK,IAAI,CAACA,EAAE,CAAEgC,EAAS,IAAI,CAACA,MAAM,CAAEoH,EAAU,IAAI,CAACA,OAAO,CAChE,IAAI,CAACyG,KAAK,GACNzG,GACAA,EAAQ5G,OAAO,GAEfR,GACAA,EAAOQ,OAAO,GAEdxC,IACAoH,EAAW,IAAI,CAACc,cAAc,CAAE,AAACrD,IACzBA,EAAQ+M,MAAM,EACd5R,EAAG4V,aAAa,CAAC/Q,EAAQ+M,MAAM,CAEvC,GACA5R,EAAGpD,MAAM,CAAC8B,KAAK,CAAG,EAClBsB,EAAGpD,MAAM,CAACgC,MAAM,CAAG,EAE3B,CACJ,CA+DI7G,CArCOA,EAyFRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GApDlB8d,SAAS,CAPrB,SAAmBC,CAAM,CAAEtb,CAAM,CAAEub,CAAU,SACzC,AAAI/C,MAAMgD,OAAO,CAACF,IACdA,EAAOtb,MAAM,CAAGA,EACTsb,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAGvb,EACxD,EAoDAzC,EAAYke,MAAM,CAvBlB,SAAgBH,CAAM,CAAEI,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,EAAQ,EAAE,EACrE,GAAIrD,MAAMgD,OAAO,CAACF,GAId,OAHK9C,MAAMgD,OAAO,CAACK,IACfA,CAAAA,EAAQrD,MAAMtM,IAAI,CAAC2P,EAAK,EAErB,CACHC,QAASR,EAAOG,MAAM,CAACC,EAAOC,KAAgBE,GAC9CE,MAAOT,CACX,EAEJ,IAAMU,EAAc5d,OAAO6d,cAAc,CAACX,GACrC/V,WAAW,CACVuW,EAAUR,CAAM,CAACM,EAAoB,WAAa,QAAQ,CAACF,EAAOA,EAAQC,GAE1EO,EAAS,IAAIF,EADDV,EAAOtb,MAAM,CAAG2b,EAAcE,EAAM7b,MAAM,EAK5D,OAHAkc,EAAOC,GAAG,CAACb,EAAOc,QAAQ,CAAC,EAAGV,GAAQ,GACtCQ,EAAOC,GAAG,CAACN,EAAOH,GAClBQ,EAAOC,GAAG,CAACb,EAAOc,QAAQ,CAACV,EAAQC,GAAcD,EAAQG,EAAM7b,MAAM,EAC9D,CACH8b,QAASA,EACTC,MAAOG,CACX,CACJ,EA2BJ,GAAM,CAAEb,UAAAA,CAAS,CAAEI,OAAAA,CAAM,CAAE,CAnB4Ble,EAqBjD,CAAE8e,UAAAA,CAAS,CAAEzP,WAAY0P,CAAwB,CAAEC,UAAAA,CAAS,CAAE,CAAIzd,IA+PrC0d,EA9OnC,MAiBIjX,YAAY1F,EAAU,CAAC,CAAC,CAAE,CAOtB,IAAI,CAAC4c,MAAM,CAAG,CAAC5c,EAAQ6c,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAI7c,EAAQ6c,EAAE,EAAIH,IACzB,IAAI,CAACK,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGP,IAClB,IAAIM,EAAW,EACfP,EAAyBzc,EAAQ8c,OAAO,EAAI,CAAC,EAAG,CAACrB,EAAQyB,KACrD,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAGzB,EAAOtH,KAAK,GACvC6I,EAAWhY,KAAKrE,GAAG,CAACqc,EAAUvB,EAAOtb,MAAM,CAC/C,GACA,IAAI,CAACgd,aAAa,CAACH,EACvB,CAaAG,cAAcH,CAAQ,CAAE,CACpB,IAAI,CAACA,QAAQ,CAAGA,EAChBP,EAAyB,IAAI,CAACK,OAAO,CAAE,CAACrB,EAAQyB,KACxCzB,EAAOtb,MAAM,GAAK6c,GAClB,CAAA,IAAI,CAACF,OAAO,CAACI,EAAW,CAAG1B,EAAUC,EAAQuB,EAAQ,CAE7D,EACJ,CAeAI,WAAWC,CAAQ,CAAEL,EAAW,CAAC,CAAE,CAC/B,GAAIA,EAAW,GAAKK,EAAW,IAAI,CAACL,QAAQ,CAAE,CAC1C,IAAI7c,EAAS,EACbsc,EAAyB,IAAI,CAACK,OAAO,CAAE,CAACrB,EAAQyB,KAC5C,IAAI,CAACJ,OAAO,CAACI,EAAW,CACpBtB,EAAOH,EAAQ4B,EAAUL,GAAUd,KAAK,CAC5C/b,EAASsb,EAAOtb,MAAM,AAC1B,GACA,IAAI,CAAC6c,QAAQ,CAAG7c,CACpB,CACAqc,EAAU,IAAI,CAAE,kBAAmB,CAAEa,SAAAA,EAAUL,SAAAA,CAAS,GACxD,IAAI,CAACC,UAAU,CAAGP,GACtB,CAWA/a,UAAUub,CAAU,CAEpBI,CAAW,CAAE,CACT,OAAO,IAAI,CAACR,OAAO,CAACI,EAAW,AACnC,CAYAK,WAAWC,CAAW,CAEtBF,CAAW,CAAE,CACT,MAAO,AAACE,CAAAA,GAAejf,OAAOkf,IAAI,CAAC,IAAI,CAACX,OAAO,CAAA,EAAGY,MAAM,CAAC,CAACZ,EAASI,KAC/DJ,CAAO,CAACI,EAAW,CAAG,IAAI,CAACJ,OAAO,CAACI,EAAW,CACvCJ,GACR,CAAC,EACR,CAaAa,OAAON,CAAQ,CAAEG,CAAW,CAAE,CAC1B,MAAO,AAACA,CAAAA,GAAejf,OAAOkf,IAAI,CAAC,IAAI,CAACX,OAAO,CAAA,EAAGc,GAAG,CAAC,AAACvf,GAAQ,IAAI,CAACye,OAAO,CAACze,EAAI,EAAE,CAACgf,EAAS,CAChG,CAmBAQ,UAAUX,CAAU,CAAEzB,EAAS,EAAE,CAAE4B,EAAW,CAAC,CAAES,CAAW,CAAE,CAC1D,IAAI,CAACC,UAAU,CAAC,CAAE,CAACb,EAAW,CAAEzB,CAAO,EAAG4B,EAAUS,EACxD,CAmBAC,WAAWjB,CAAO,CAAEO,CAAQ,CAAES,CAAW,CAAE,CACvC,IAAId,EAAW,IAAI,CAACA,QAAQ,CAC5BP,EAAyBK,EAAS,CAACrB,EAAQyB,KACvC,IAAI,CAACJ,OAAO,CAACI,EAAW,CAAGzB,EAAOtH,KAAK,GACvC6I,EAAWvB,EAAOtb,MAAM,AAC5B,GACA,IAAI,CAACgd,aAAa,CAACH,GACdc,GAAaE,SACdxB,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACS,UAAU,CAAGP,IAE1B,CAoBAuB,OAAOC,CAAG,CAAEb,EAAW,IAAI,CAACL,QAAQ,CAAEmB,CAAM,CAAEL,CAAW,CAAE,CACvD,GAAM,CAAEhB,QAAAA,CAAO,CAAE,CAAG,IAAI,CAAEsB,EAAgBD,EAAS,IAAI,CAACnB,QAAQ,CAAG,EAAIK,EAAW,EAClFZ,EAAyByB,EAAK,CAACG,EAAWnB,KACtC,IAAIzB,EAASqB,CAAO,CAACI,EAAW,EAC5BY,GAAaQ,aAAe,CAAA,GAAS,AAAI3F,MAAMyF,GAC/C3C,IACI0C,EACA1C,EAASG,EAAOH,EAAQ4B,EAAU,EAAG,CAAA,EAAM,CAACgB,EAAU,EAAEnC,KAAK,CAG7DT,CAAM,CAAC4B,EAAS,CAAGgB,EAEvBvB,CAAO,CAACI,EAAW,CAAGzB,EAE9B,GACI2C,EAAgB,IAAI,CAACpB,QAAQ,EAC7B,IAAI,CAACG,aAAa,CAACiB,GAElBN,GAAaE,SACdxB,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACS,UAAU,CAAGP,IAE1B,CACJ,EAyDM,CAAE7Y,iBAAkB0a,CAA4B,CAAE7e,sBAAuB8e,CAAiC,CAAE,CAAGjb,EAE/G,CAAEkb,WAAAA,CAAU,CAAE,CAAIxf,IAElB,CAAEK,SAAUof,CAAoB,CAAEhS,IAAKiS,CAAe,CAAEC,KAAAA,CAAI,CAAEjS,IAAKkS,CAAe,CAAE,CAAI5f,IAExF,CAAEM,SAAUuf,CAAoB,CAAEC,wBAAAA,CAAuB,CAAExZ,MAAOyZ,CAAiB,CAAEC,OAAAA,EAAM,CAAEzC,UAAW0C,EAAqB,CAAEvD,QAAAA,EAAO,CAAE/O,SAAUuS,EAAoB,CAAE3f,KAAM4f,EAAgB,CAAE3f,WAAY4f,EAAsB,CAAEC,KAAAA,EAAI,CAAEC,QAAAA,EAAO,CAAE,CAAItgB,IAuB7P,SAASugB,GAA4B3G,CAAQ,CAAEhZ,CAAM,EACjD,IAAMC,EAAQD,EAAOC,KAAK,CACtB+Y,GACA/Y,GACAA,EAAMgE,MAAM,EACZhE,EAAMyC,MAAM,EACZ,CAACic,EAAkC3e,EAAOF,KAAK,GAC/CkZ,EAAS7J,6BAA6B,CAACnP,EAE/C,CAUA,SAAS4f,GAAa9f,CAAK,EACvB,OAAOyf,GAAkBzf,GACrBA,EAAMK,OAAO,EACbL,EAAMK,OAAO,CAACF,KAAK,EACnBH,EAAMK,OAAO,CAACF,KAAK,CAACuV,OAAO,CAAG,CAAA,EACtC,CA0GA,SAASqK,GAAwB/f,CAAK,CAAEE,CAAM,EAC1C,IAAM4D,EAAa9D,EAAM+F,WAAW,CAAEia,EAAchgB,EAAM8X,WAAW,EAAI5X,EAAO+f,KAAK,CACjFvb,EAAQ1E,EAAMyW,UAAU,CAAE7R,EAAS5E,EAAM0W,WAAW,CAAEvS,EAASnE,EAAOkgB,EAAc,AAAmC,aAAnC,OAAOC,wBAAyCC,EAAkB,CAAA,EACtJvB,EAAkC7e,GAClCmE,EAASnE,GAGTmE,EAASjE,EACTkgB,EAAkBC,CAAAA,CAAQngB,CAAAA,EAAOG,OAAO,CAACigB,MAAM,EAAEC,OAC7CrgB,EAAOG,OAAO,CAACyT,KAAK,EAAEwM,QAAQC,KAAI,GAE1C,IAAMpgB,EAAQgE,EAAOhE,KAAK,CACtBgE,EAAOhE,KAAK,EACR,CAAC,EAyFT,GAnFA+f,EAAc,CAAA,EACTriB,GACDA,CAAAA,EAAamhB,EAAgB9E,aAAa,CAAC,SAAQ,EAEnD,CAAC/Z,EAAMgE,MAAM,GACbhE,EAAMyC,MAAM,CAAG/E,EAGXmC,EAAMkZ,QAAQ,CAACC,SAAS,EAAI,CAAC+G,GAC7B/b,EAAOqc,YAAY,CAAGrgB,EAAMgE,MAAM,CAAGnE,EAAMkZ,QAAQ,CAACuH,KAAK,CAAC,GAAI,EAAG,EAAG/b,EAAOE,GACtEmT,QAAQ,CAAC,2BACT2I,GAAG,CAACV,GACT7f,EAAMwC,KAAK,CAAG,WACVxC,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CAGdC,KAAM,oHACV,EACJ,EACAzgB,EAAM0gB,IAAI,CAAG,WACT1gB,EAAM2gB,MAAM,GACZ3gB,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CACdC,KAAMzgB,EAAMyC,MAAM,CAACme,SAAS,CAAC,YACjC,EACJ,IAGA5gB,EAAM6gB,QAAQ,CAAGhhB,EAAMkZ,QAAQ,CAC1BgB,aAAa,CAAC,iBACdwG,GAAG,CAACV,GACT7b,EAAOqc,YAAY,CAAGrgB,EAAMgE,MAAM,CAC9B6a,EAAgB9E,aAAa,CAAC,UAClC/Z,EAAM8gB,SAAS,CAAG9gB,EAAMgE,MAAM,CAACqV,UAAU,CAAC,MAC1CrZ,EAAM6gB,QAAQ,CAACE,OAAO,CAACC,WAAW,CAAChhB,EAAMgE,MAAM,EAC/ChE,EAAMwC,KAAK,CAAG,WACVxC,EAAMgE,MAAM,CAACO,KAAK,CAAGvE,EAAMyC,MAAM,CAAC8B,KAAK,CACvCvE,EAAMgE,MAAM,CAACS,MAAM,CAAGzE,EAAMyC,MAAM,CAACgC,MAAM,AAC7C,EACAzE,EAAM0gB,IAAI,CAAG,WACT1gB,EAAMgE,MAAM,CAACO,KAAK,CAAGvE,EAAMyC,MAAM,CAAC8B,KAAK,CACvCvE,EAAMgE,MAAM,CAACS,MAAM,CAAGzE,EAAMyC,MAAM,CAACgC,MAAM,CACzCzE,EAAM8gB,SAAS,CAACG,SAAS,CAACjhB,EAAMyC,MAAM,CAAE,EAAG,EAC/C,GAEJzC,EAAM2gB,MAAM,CAAG,WACXpc,EAAQ1E,EAAMyW,UAAU,CACxB7R,EAAS5E,EAAM0W,WAAW,CAC1B,AAACvW,CAAAA,EAAM6gB,QAAQ,EAAI7gB,EAAMgE,MAAM,AAAD,EACzBwc,IAAI,CAAC,CACNrc,EAAG,EACHE,EAAG,EACHE,MAAAA,EACAE,OAAAA,CACJ,GACKyc,GAAG,CAAC,CACLC,cAAelB,EAAkB,KAAK,EAAI,OAC1CmB,eAAgB,SAChBC,QA3EmF,CA4EvF,GACKzJ,QAAQ,CAACqI,EAAkB,qBAAuB,IACnDjc,aAAkBL,GAClBK,EAAOhE,KAAK,EAAE2C,aAAaE,UAAUhD,EAAMuE,QAAQ,CAAEvE,EAAMyE,OAAO,CAE1E,EACAtE,EAAMshB,QAAQ,CAAGzhB,EAAMkZ,QAAQ,CAACuI,QAAQ,GACxC,AAACthB,CAAAA,EAAM6gB,QAAQ,EAAI7gB,EAAMgE,MAAM,AAAD,EACzBwc,IAAI,CAAC,CAMNe,OAAQxhB,EAAOG,OAAO,CAACqhB,MAAM,AACjC,GACIvd,aAAkBL,GAClBK,CAAAA,EAAOhE,KAAK,CAAC2C,WAAW,CAAGqB,EAAO+U,QAAQ,CACrCyI,CAAC,GACDjB,GAAG,CAACV,GACJhd,SAAS,CAAC9C,EAAOU,KAAK,CAACqC,GAAG,CAAE/C,EAAO6C,KAAK,CAACE,GAAG,CAAA,GAGzD9C,EAAMyC,MAAM,CAAC8B,KAAK,CAAGA,EACrBvE,EAAMyC,MAAM,CAACgC,MAAM,CAAGA,EAClBzE,EAAMshB,QAAQ,CAAE,CAChB,IAAMG,EAAMhD,EAA6B5e,EAAOmE,GAIhD0d,EAAiB,AAACD,EAAIld,KAAK,GAAK1E,EAAMqE,OAAO,CAACK,KAAK,EAC/Ckd,EAAIhd,MAAM,GAAK5E,EAAMqE,OAAO,CAACO,MAAM,CAAIob,EACtC7f,EAAM6gB,QAAQ,EAAI7gB,EAAMgE,MAAM,CACnChE,EAAMshB,QAAQ,CAACd,IAAI,CAACiB,GACpBC,GAAgBC,KAAK3hB,EAAMshB,QAAQ,CACvC,CAyBA,OAxBAthB,EAAM2gB,MAAM,GACZ3gB,EAAMwC,KAAK,GACP,CAACxC,EAAMkC,GAAG,GACVlC,EAAMkC,GAAG,CAAG,IA7sBoCoL,EA6sBd,AAACpL,IAC3BA,EAAI8L,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjCkG,QAAQO,IAAI,CAAC,eAEjBpV,EAAM0gB,IAAI,GACNxe,EAAI8L,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjCkG,QAAQY,OAAO,CAAC,cAExB,GACKzV,EAAMkC,GAAG,CAACiX,IAAI,CAACnZ,EAAMyC,MAAM,GAI5Byc,EAAkB,sDAEtBlf,EAAMkC,GAAG,CAAC8M,UAAU,CAACnP,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,GACzCgE,aAAkBL,GAClB3D,EAAMkC,GAAG,CAACQ,cAAc,CAAC7C,IAGjCG,EAAMkC,GAAG,CAACgX,OAAO,CAAC3U,EAAOE,GAClBzE,EAAMkC,GAAG,AACpB,CAOA,SAAS0f,GAAgB7hB,CAAM,EAC3B,IAAMgC,EAAShC,EAAOgC,MAAM,CAC5B,GAAIA,EAAQ,CACR,IAAI4R,EAAOtC,EACX,IAAKA,EAAI,EAAGA,EAAItP,EAAO1B,MAAM,CAAEgR,GAAQ,EACnCsC,CAAAA,EAAQ5R,CAAM,CAACsP,EAAE,AAAD,GACHsC,EAAMkO,eAAe,EAC9BlO,EAAMkO,eAAe,EAGjC,CAOA,IAAK,IAAMvP,KANX,CAAC,QAAS,OAAQ,UAAU,CAAChT,OAAO,CAAC,AAACR,IAClC,IAAMgjB,EAAa/hB,CAAM,CAACjB,EAAK,CAC3BgjB,GACA/hB,CAAAA,CAAM,CAACjB,EAAK,CAAGgjB,EAAWzZ,OAAO,EAAC,CAE1C,GACmBtI,EAAO8Q,KAAK,EAC3BoO,EAAwB3M,EAAM,KAAK,EAAG,CAAA,EAE9C,CAmBA,SAASyP,GAAUC,CAAG,CAAEnI,CAAE,CAAEoI,CAAS,CAAEC,CAAS,CAAE7Q,CAAC,CAAE8Q,CAAS,EAG1D,IAAMhiB,EAAYkR,AAFlBA,CAAAA,EAAIA,GAAK,CAAA,EACT6Q,CAAAA,EAAYA,GA3UG,GA2UmB,EAE9BE,EAAU,CAAA,EACd,KAAOA,GAAW/Q,EAAIlR,GAAakR,EAAI2Q,EAAI3hB,MAAM,EAC7C+hB,EAAUvI,EAAGmI,CAAG,CAAC3Q,EAAE,CAAEA,GACrB,EAAEA,EAEF+Q,IACI/Q,EAAI2Q,EAAI3hB,MAAM,CACV8hB,EACAJ,GAAUC,EAAKnI,EAAIoI,EAAWC,EAAW7Q,EAAG8Q,GAEvCpD,EAAgBsD,qBAAqB,CAE1CtD,EAAgBsD,qBAAqB,CAAC,WAClCN,GAAUC,EAAKnI,EAAIoI,EAAWC,EAAW7Q,EAC7C,GAGA4H,WAAW8I,GAAW,EAAGC,EAAKnI,EAAIoI,EAAWC,EAAW7Q,GAGvD4Q,GACLA,IAGZ,CAiFA,SAASK,GAAYviB,CAAM,CAAEwiB,CAAM,EAC/B,IAAMriB,EAAUH,EAAOG,OAAO,CAAEsiB,EAAaziB,EAAO0iB,SAAS,CAACxF,QAAQ,CAACC,QAAQ,CAAEzc,EAAQV,EAAOU,KAAK,EAAIV,EAAOU,KAAK,CAACP,OAAO,CAAE0C,EAAQ7C,EAAO6C,KAAK,EAAI7C,EAAO6C,KAAK,CAAC1C,OAAO,CAAEwiB,EAAY3iB,EAAO2iB,SAAS,EAAI3iB,EAAO2iB,SAAS,CAACxiB,OAAO,CACrO,OAAOsiB,EAActiB,CAAAA,EAAQiB,cAAc,EAAIO,OAAOC,SAAS,AAAD,GAE1D0d,GAAqBzc,EAAMlC,GAAG,GAC9B2e,GAAqBzc,EAAM/B,GAAG,GAE7B,CAAA,CAAC0hB,GACGlD,GAAqB5e,EAAMC,GAAG,GAAK2e,GAAqB5e,EAAMI,GAAG,CAAC,GAEtE,CAAA,CAAC6hB,GACGrD,GAAqBqD,EAAUhiB,GAAG,GAAK2e,GAAqBqD,EAAU7hB,GAAG,CAAC,CACvF,CAOA,IAAM8hB,GAAoB,CAAC5iB,EAAQ+B,IAE/B,CAAI/B,EAAO6iB,SAAS,EAGZlE,CAAAA,EAAkC3e,EAAOF,KAAK,GACjD,AAACiC,CAAAA,EAAOA,EAAKzB,MAAM,CAAG,CAAA,GAClBN,CAAAA,EAAOG,OAAO,CAACiB,cAAc,EAAIO,OAAOC,SAAS,AAAD,CAAE,EAQ/D,SAASkhB,KACL,IAAM9iB,EAAS,IAAI,CAAEF,EAAQE,EAAOF,KAAK,AACrCA,CAAAA,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAAC2C,WAAW,GAAK5C,EAAO4C,WAAW,EAC9C5C,CAAAA,EAAO4C,WAAW,CAAG,IAAG,EAExB9C,EAAMijB,WAAW,EACjBjjB,CAAAA,EAAMijB,WAAW,CAAGjjB,EAAMijB,WAAW,CAACC,MAAM,CAAC,SAAUpP,CAAK,EACxD,OAAOA,EAAM5T,MAAM,GAAKA,CAC5B,EAAC,EAEDF,EAAMuD,UAAU,EAAIvD,EAAMuD,UAAU,CAACrD,MAAM,GAAKA,GAChDF,CAAAA,EAAMuD,UAAU,CAAG,IAAG,CAE9B,CAIA,SAAS4f,KACL,IAAMhjB,EAAQ,IAAI,CAACA,KAAK,CACpBA,GAASA,EAAMyC,MAAM,EAAIzC,EAAMgE,MAAM,GACjChE,EAAMkC,GAAG,EACTlC,EAAMkC,GAAG,CAACM,KAAK,GAEfxC,EAAMwC,KAAK,EACXxC,EAAMwC,KAAK,GAGvB,CAMA,SAASygB,GAA0BljB,CAAM,EACrC,IAAMC,EAAQD,EAAOC,KAAK,CACtBA,GACAA,EAAMyC,MAAM,EACZzC,EAAMgE,MAAM,EACZhE,EAAMkC,GAAG,EACT,CAACwc,EAAkC3e,EAAOF,KAAK,GAC/CG,EAAMkC,GAAG,CAACC,MAAM,CAACpC,EAAOF,KAAK,CAErC,CAUA,SAASqjB,GAASnjB,CAAM,CAAEojB,CAAU,EAChC,IAAMjiB,EAAgBnB,EAAOG,OAAO,CAAEO,EAAQV,EAAOU,KAAK,CAAE2iB,EAAarjB,EAAOsjB,UAAU,CAC1F,GAAIF,aAAsBC,EACtB,OAAOD,EAEX,IAAMG,EAAYvjB,EAAO+E,EAAE,CAAC,WAAY4I,EAAS,AAAC4V,CAAAA,GAAavjB,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAMxB,MAAM,CAC7FN,EAAO8B,SAAS,CAAC,IAAK,CAAA,GACtB,KAAK,CAAA,GACJ9B,CAAAA,EAAO8B,SAAS,CAAC,KAAKxB,MAAM,CAAGN,EAAO8B,SAAS,CAAC,KAAO,KAAK,CAAA,GAC7DX,EAAcwM,KAAK,EACnB3N,EAAO8B,SAAS,CAAC,IAAK,CAAA,IACtB,CAAA,EAAQsO,EAASpQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,IACvCX,EAAciP,KAAK,EACnB,CAAA,EAAQwD,EAAQ,IAAIyP,EAAWrjB,EAAQ,AAACujB,GAAa5V,GAASyC,EAC9D,CAACzC,CAAK,CAACyV,EAAW9R,CAAC,CAAC,CAAElB,CAAK,CAACgT,EAAW9R,CAAC,CAAC,CAAC,CAC1C,AAACwK,CAAAA,GAAQ9b,EAAOG,OAAO,CAAC4B,IAAI,EAAI/B,EAAOG,OAAO,CAAC4B,IAAI,CAAG,EAAE,AAAD,CAAE,CAACqhB,EAAW9R,CAAC,CAAC,CAAE3D,EAAQA,CAAK,CAACyV,EAAW9R,CAAC,CAAC,CAAG,KAAK,GAahH,OAZAsC,EAAM4P,QAAQ,CAAGjE,GAAiB7e,EAAM+iB,UAAU,CAC9C/iB,EAAM+iB,UAAU,CAAC7P,EAAMxP,CAAC,CAAC,CACzBwP,EAAMxP,CAAC,CACXwP,EAAMxP,CAAC,EACPwP,EAAMpV,GAAG,CAAGoV,EAAMhJ,IAAI,EAAIgJ,EAAM4P,QAAQ,CACxC5P,EAAM8P,IAAI,CAAGN,EAAWM,IAAI,CAC5B9P,EAAM+P,KAAK,CAAGP,EAAWO,KAAK,CAC9B/P,EAAMgQ,KAAK,CAAGR,EAAWQ,KAAK,CAC9BhQ,EAAMG,KAAK,CAAGqP,EAAWrP,KAAK,CAC9BH,EAAMlW,KAAK,CAAG0lB,EAAW9R,CAAC,CAC1BsC,EAAMiQ,UAAU,CAAGT,EAAWS,UAAU,CACxCjQ,EAAMkQ,QAAQ,CAAG9jB,EAAO+jB,aAAa,CAACnQ,GAC/BA,CACX,CAIA,SAASoQ,GAAmBC,CAAK,EAC7B,GAAqB,CAAE9jB,QAAAA,CAAO,CAAEO,MAAAA,CAAK,CAAEmC,MAAAA,CAAK,CAAE,CAA/B,IAAI,CAEnB,GAAI,CAAC7C,AAFU,IAAI,CAEPkkB,OAAO,EACf,CAACxjB,EAAMwjB,OAAO,EACd,CAACrhB,EAAMqhB,OAAO,EACd,CAACD,EACD,MAAO,CAAA,EAIXjkB,AAVe,IAAI,CAUZ6C,KAAK,CAACshB,eAAe,GAC5B,IAAM/iB,EAAiBjB,EAAQiB,cAAc,EAAI,EAAGgjB,EAAgBjkB,EAAQikB,aAAa,CAAEzW,EAAQ3N,AAXpF,IAAI,CAWuF8B,SAAS,CAAC,KAAM8N,EAAYlP,EAAMmP,WAAW,GAAIG,EAAOJ,EAAU9O,GAAG,EAAIa,OAAOC,SAAS,CAAEkO,EAAOF,EAAUjP,GAAG,EAAI,CAACgB,OAAOC,SAAS,CAAEwO,EAAQpQ,AAXzO,IAAI,CAW4O8B,SAAS,CAAC,KAAMmO,EAAYpN,EAAMgN,WAAW,GAAIM,EAAOF,EAAUnP,GAAG,EAAIa,OAAOC,SAAS,CAAEsO,EAAOD,EAAUtP,GAAG,EAAI,CAACgB,OAAOC,SAAS,CAEnY,GAAI,CAAC5B,AAbU,IAAI,CAaPqC,OAAO,EACf3B,EAAM2jB,GAAG,EACTxhB,EAAMwhB,GAAG,EACTvU,GAASpP,CAAAA,EAAM2jB,GAAG,CAAC1jB,GAAG,EAAI,CAACgB,OAAOC,SAAS,AAAD,GAC1CoO,GAAStP,CAAAA,EAAM2jB,GAAG,CAACvjB,GAAG,EAAIa,OAAOC,SAAS,AAAD,GACzCsO,GAASrN,CAAAA,EAAMwhB,GAAG,CAAC1jB,GAAG,EAAI,CAACgB,OAAOC,SAAS,AAAD,GAC1CuO,GAAStN,CAAAA,EAAMwhB,GAAG,CAACvjB,GAAG,EAAIa,OAAOC,SAAS,AAAD,EAKzC,OAJA5B,AApBW,IAAI,CAoBR0iB,SAAS,CAACxF,QAAQ,CAACgB,UAAU,CAAC,CACjC9Z,EAAGuJ,EACHrJ,EAAG8L,CACP,GACO,CAAA,EAGX,IAAMqS,EAAaziB,AA3BJ,IAAI,CA2BO0iB,SAAS,CAACvF,QAAQ,CAC5C,GAAI,CAAC/b,GACDqhB,EAAarhB,GACZgjB,GACG,CAACpkB,AA/BM,IAAI,CA+BH6iB,SAAS,EACjB,CAAC7iB,AAhCM,IAAI,CAgCHskB,kBAAkB,EAC1B,CAACnkB,EAAQmkB,kBAAkB,EAC3B7B,EAAa2B,EAKjB,OAJApkB,AAnCW,IAAI,CAmCR0iB,SAAS,CAACxF,QAAQ,CAACgB,UAAU,CAAC,CACjC9Z,EAAGuJ,EACHrJ,EAAG8L,CACP,GACO,CAAA,EAGX,IAAMmU,EAAgB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAe,CAAEpF,CAAAA,GAAqB1P,EAAU9O,GAAG,GAAKwe,GAAqB1P,EAAUjP,GAAG,CAAA,EAAIgkB,EAAe,CAAErF,CAAAA,GAAqBrP,EAAUnP,GAAG,GAAKwe,GAAqBrP,EAAUtP,GAAG,CAAA,EACxPikB,EAAU,CAAA,EAAOxgB,EAAGygB,EAAWlX,CAAK,CAAC,EAAE,CAAEmX,EAAWnX,CAAK,CAAC,EAAE,CAAErJ,EAAGygB,EAAW3U,GAAO,CAAC,EAAE,CAAE4U,EAAW5U,GAAO,CAAC,EAAE,CACjH,IAAK,IAAIkB,EAAI,EAAG2T,EAAOtX,EAAMrN,MAAM,CAAEgR,EAAI2T,EAAM,EAAE3T,EAC7ClN,EAAIuJ,CAAK,CAAC2D,EAAE,CACZhN,EAAI8L,GAAO,CAACkB,EAAE,CACVlN,GAAK0L,GAAQ1L,GAAK4L,GAClB1L,GAAK4L,GAAQ5L,GAAK6L,GAClBoU,EAAcxgB,IAAI,CAAC,CAAEK,EAAAA,EAAGE,EAAAA,CAAE,GAC1BkgB,EAAezgB,IAAI,CAACK,GACpBqgB,EAAe1gB,IAAI,CAACO,GAChBogB,IACAG,EAAW1f,KAAKrE,GAAG,CAAC+jB,EAAUzgB,GAC9B0gB,EAAW3f,KAAKxE,GAAG,CAACmkB,EAAU1gB,IAE9BugB,IACAI,EAAW5f,KAAKrE,GAAG,CAACikB,EAAUzgB,GAC9B0gB,EAAW7f,KAAKxE,GAAG,CAACqkB,EAAU1gB,KAIlCsgB,EAAU,CAAA,EA2BlB,OAxBIF,IACAhkB,EAAMK,OAAO,CAAGoE,KAAKrE,GAAG,CAAC+jB,EAAUnkB,EAAMK,OAAO,EAAI,GACpDL,EAAMG,OAAO,CAAGsE,KAAKxE,GAAG,CAACmkB,EAAUpkB,EAAMG,OAAO,EAAI,IAEpD8jB,IACA9hB,EAAM9B,OAAO,CAAGoE,KAAKrE,GAAG,CAACikB,EAAUliB,EAAM9B,OAAO,EAAI,GACpD8B,EAAMhC,OAAO,CAAGsE,KAAKxE,GAAG,CAACqkB,EAAUniB,EAAMhC,OAAO,EAAI,IAGxDb,AA1Ee,IAAI,CA0EZ4kB,OAAO,CAAGA,EACjB5kB,AA3Ee,IAAI,CA2EZklB,SAAS,CAAG,EAEfN,GAAW5kB,AA7EA,IAAI,CA6EG0iB,SAAS,CAACxF,QAAQ,GAAKld,AA7E9B,IAAI,CA6EiC0iB,SAAS,EAGzD1iB,CAAAA,AAhFW,IAAI,CAgFR0iB,SAAS,CAACxF,QAAQ,CAAG,IAAIJ,CAAmB,EAEvD9c,AAlFe,IAAI,CAkFZ0iB,SAAS,CAACxF,QAAQ,CAACgB,UAAU,CAAC,CACjC9Z,EAAGogB,EACHlgB,EAAGmgB,CACP,GACK7B,GAtFU,IAAI,CAsFY4B,IAC3BxkB,CAAAA,AAvFW,IAAI,CAuFRukB,aAAa,CAAGA,CAAY,EAEhC,CAAA,CACX,CAKA,SAASY,KACL,IAAMhlB,EAAU,IAAI,CAACA,OAAO,EAAI,CAAC,EAAGL,EAAQ,IAAI,CAACA,KAAK,CAAEslB,EAAatlB,EAAMG,KAAK,CAAEolB,EAAc,IAAI,CAACplB,KAAK,CAAES,EAAQ,IAAI,CAACA,KAAK,CAAEmC,EAAQ,IAAI,CAACA,KAAK,CAAE8K,EAAQxN,EAAQwN,KAAK,EAAI,IAAI,CAAC7L,SAAS,CAAC,IAAK,CAAA,GAAOsO,EAAQjQ,EAAQiQ,KAAK,EAAI,IAAI,CAACtO,SAAS,CAAC,IAAK,CAAA,GAAOwjB,EAAU,IAAI,CAACxjB,SAAS,CAAC,MAAO,CAAA,GAAOyjB,EAAW,IAAI,CAACzjB,SAAS,CAAC,OAAQ,CAAA,GAAO6N,EAAU,IAAI,CAAC4U,aAAa,EAAIpkB,EAAQ4B,IAAI,CAAE6N,EAAYlP,EAAMmP,WAAW,GAEvZC,EAAOF,EAAUjP,GAAG,CAAID,CAAAA,EAAMqP,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU9O,GAAG,CAAIJ,CAAAA,EAAMqP,cAAc,EAAI,CAAA,EAAIE,EAAYpN,EAAMgN,WAAW,GAAIK,EAAOD,EAAUtP,GAAG,CAAIkC,CAAAA,EAAMkN,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUnP,GAAG,CAAI+B,CAAAA,EAAMkN,cAAc,EAAI,CAAA,EAAIyV,EAAa,CAAC,EAAGC,EAAW,CAAC,CAAC,IAAI,CAACA,QAAQ,CAAEC,EAAsBvlB,EAAQulB,mBAAmB,CAAEtlB,EAAYD,EAAQC,SAAS,CAAEoP,EAAU,IAAI,CAACC,aAAa,EACxY,AAAiC,aAAjC,IAAI,CAACA,aAAa,CAAC7H,IAAI,CAAC,KAAqB8F,EAAY,CAAC,CAACvN,EAAQyN,QAAQ,CAAEsX,EAAY,IAAI,CAACA,SAAS,EAAI,EAAGS,EAAiB,IAAI,CAACA,cAAc,CAAErV,EAAS,CAAC3C,EAAOiY,EAAWzlB,AAA+B,MAA/BA,EAAQ0lB,kBAAkB,CAAUC,EAAa,AAAC,CAAA,IAAI,CAAChkB,SAAS,CAAC,KAAKxB,MAAM,CAC5P,IAAI,CAACwB,SAAS,CAAC,KACf,KAAK,CAAA,GACL,IAAI,CAAC3B,OAAO,CAACwN,KAAK,EAClB,IAAI,CAAC7L,SAAS,CAAC,IAAK,CAAA,GAAQqM,EAAYoR,GAAiBpf,EAAQgO,SAAS,CAAE,GAAI4X,EAAkB5lB,EAAQ6lB,eAAe,EAAI9V,EAC7H8I,EAAW,CAAA,EAAOiN,EAAanP,EAAUjU,EAAMkU,YAAY,CAAC3W,GAAY6Q,EAAQiV,EAAQC,EAAMC,EAKlG,GAAI1lB,EAAM8B,SAAS,EAAIK,EAAML,SAAS,GAItCwW,EAAW6G,GAAwB/f,EAAO,IAAI,EAC9CA,EAAMuC,OAAO,CAAG,CAAA,EACZ,CAAC,IAAI,CAAChB,OAAO,EALb,OASA,CAAA,IAAI,CAACW,MAAM,EAAI,IAAI,CAACqkB,KAAK,AAAD,GACxBxE,GAAgB,IAAI,EAInBlD,EAAkC7e,IAW/B,IAAI,CAAC8C,WAAW,EAChB,IAAI,CAACA,WAAW,GAAKwiB,GAAYxiB,aACjC,IAAI,CAACA,WAAW,CAAC0F,OAAO,GAG5B,IAAI,CAAC1F,WAAW,CAAGwiB,GAAYxiB,YAG3ByiB,GAAeA,EAAYphB,MAAM,EACjC,CAAA,IAAI,CAACqc,YAAY,CACb+E,EAAYphB,MAAM,CACdohB,EAAYphB,MAAM,CAACqE,OAAO,EAAC,IAnBnC,IAAI,CAAC1F,WAAW,GAAKwiB,GAAYxiB,aACjC,CAAA,IAAI,CAACA,WAAW,CAAG,KAAK,CAAA,EAE5B,IAAI,CAACA,WAAW,CAAG,IAAI,CAAC+U,SAAS,CAAC,cAAe,UAAW,UAAW,EAAG7X,EAAM8X,WAAW,EAAEC,QAAQ,CAAC,uBAmB1G,IAAM7V,EAAS,IAAI,CAACA,MAAM,CAAG,EAAE,CAAEskB,EAAa,CAACC,EAASxS,EAAOzC,EAAGuS,KAC9D,IAAMzf,EAAI0hB,EAAAA,GAAYA,CAAS,CAACZ,EAAY5T,EAAE,CAAUkV,EAAY,AAAC5C,IAC7D9jB,EAAMwD,QAAQ,GACdsgB,EAAQljB,EAAM0E,GAAG,CAAGwe,EACpB7P,EAAQlR,EAAMuC,GAAG,CAAG2O,GAExB/R,EAAO+B,IAAI,CAAC,CACRuE,QAASyW,EACT3a,EAAGA,EACHmiB,QAAS3C,EACTA,MAAOA,EACP7P,MAAOA,EACPzC,EAAG4T,EAAY5T,EACfuS,WAAYA,CAChB,EACJ,EAIA0C,EAAUphB,KAAKshB,IAAI,CAACF,GAEpB7oB,EAAQkoB,EAAWW,EAAUA,EAAU,IAAMxS,EAIzC2R,IACKF,CAAU,CAAC9nB,EAAM,CAIb0G,IAAM0hB,CAAS,CAACA,EAAUxlB,MAAM,CAAG,EAAE,GAG1C0B,EAAO1B,MAAM,GACbkmB,EAAUD,KAPVf,CAAU,CAAC9nB,EAAM,CAAG,CAAA,EACpB8oB,EAAUD,IAStB,CAEA,CAAA,IAAI,CAACG,WAAW,CAAG3H,EACnBM,GAAsB,IAAI,CAAE,gBACxB,IAAI,CAACta,EAAE,CAAC,SACRoJ,EAAY,GACZkX,GAAaphB,QACbmhB,GACA,CAACA,EAAWuB,eAAe,GAC3BvB,EAAWuB,eAAe,CAAG7mB,EAAMkZ,QAAQ,CAACza,UAAU,CAAC,CACnDqoB,QAAS,SACTC,SAAU,CACN,CACID,QAAS,eACTE,WAAY,CACRC,SAAU,SACV9P,OAAQ,IAAO9I,CACnB,CACJ,EACH,CACD2Y,WAAY,CAAE9J,GAAI,WAAY,CAClC,GACAqI,EAAYphB,MAAM,CAACwc,IAAI,CAAC,CACpBuC,OAAQ,iBACZ,IAEAhK,IACA2G,GAA4B3G,EAAU,IAAI,EAC1CA,EAAS7D,UAAU,CAAC,IAAI,EAExB+N,GAA0B,IAAI,GAsFlC,IAAMhjB,EAAe8Y,EAAS/K,QAAQ,AAejCnO,CAAAA,EAAMkZ,QAAQ,CAACC,SAAS,GACrB/Y,EAAasO,KAAK,CAACK,UAAU,EAC7BiG,QAAQO,IAAI,CAAC,oBAEjB2M,GAAUtU,EACN,IAAI,CAAC3L,IAAI,CAACuS,KAAK,CAAC4Q,GACfvX,GAASgC,EArGlB,SAAsBvR,CAAC,CAAEkT,CAAC,EACtB,IAAMX,EAAiB,AAAuB,KAAA,IAAhB7Q,EAAMpC,KAAK,CACrC0G,EAAGE,EAAGiiB,EAASxS,EAAO8P,EAAYpS,EAAM,CAAA,EAAOK,EAAY,CAAA,QAC/D,CAAK4N,GAAQthB,KAGT,CAACuS,IACGL,GACAlM,EAAIhG,CAAC,CAAC,EAAE,CACRkG,EAAIlG,CAAC,CAAC,EAAE,GAGRgG,EAAIhG,EACJkG,EAAI8L,CAAK,CAACkB,EAAE,EAAIyU,GAAmB,MAGnCvW,GACIc,GACAhM,CAAAA,EAAIlG,EAAEkW,KAAK,CAAC,EAAG,EAAC,EAEpB7C,EAAM6T,CAAO,CAAChU,EAAE,CAChBhN,EAAIihB,CAAQ,CAACjU,EAAE,EAEV5D,IACLtJ,EAAIhG,EAAEgG,CAAC,CAEPqN,EAAMnN,AADNA,CAAAA,EAAIlG,EAAEmW,MAAM,AAAD,EACDnW,EAAEkG,CAAC,CACbuf,EAAazlB,EAAEylB,UAAU,EAGxB8B,GACD7T,CAAAA,EAAY,AAACxN,CAAAA,GAAK,CAAA,GAAM4L,GAAQ5L,GAAK6L,CAAG,EAElC,OAAN7L,GAAcF,GAAK0L,GAAQ1L,GAAK4L,GAAQ8B,IACxCyU,EAAU7lB,EAAM+T,QAAQ,CAACrQ,EAAG,CAAA,GACxBqhB,GACI,CAAA,AAAgB,KAAA,IAATU,GACPI,IAAYN,CAAU,IACjBzW,GACDiC,CAAAA,EAAMnN,CAAAA,EAEN,CAAA,AAAgB,KAAA,IAAT8hB,GACP9hB,EAAI4hB,CAAK,IACTA,EAAS5hB,EACT8hB,EAAO9U,GAEP,CAAA,AAAgB,KAAA,IAAT6U,GACP1U,EAAMR,CAAK,IACXA,EAASQ,EACT0U,EAAO7U,IAIVsU,GAAYW,IAAYN,IAEL,KAAA,IAATE,IACPpS,EACIlR,EAAM4R,QAAQ,CAACyR,EAAQ,CAAA,GAC3BpP,EACIjU,EAAM4R,QAAQ,CAACxD,EAAQ,CAAA,GAC3BqV,EAAWC,EAASxS,EAAOqS,EAAMvC,GAC7B/M,IAAY/C,GACZuS,EAAWC,EAASzP,EAASqP,EAAMtC,IAG3CsC,EAAOC,EAAO,KAAK,EACnBH,EAAcM,IAKlBD,EAAWC,EADXxS,EAAQ5O,KAAKshB,IAAI,CAAC5jB,EAAM4R,QAAQ,CAACnQ,EAAG,CAAA,IACTgN,EAAGuS,KAInC,CAAClT,EACZ,EAIyD,KACrD0O,GAAsB,IAAI,CAAE,kBAE5B,OAAO,IAAI,CAACqH,WAAW,CAGnB,IAAI,CAACvmB,OAAO,EACZ,IAAI,CAACumB,WAAW,GAEhBxmB,EAAasO,KAAK,CAACK,UAAU,EAC7BiG,QAAQY,OAAO,CAAC,mBAExB,GAWJ,CAKA,SAASsR,GAAqB3E,CAAO,EACjC,IAAI7M,EAAU,CAAA,EAMd,GALI,IAAI,CAAC1V,KAAK,CAACK,OAAO,EAAI,IAAI,CAACL,KAAK,CAACK,OAAO,CAACF,KAAK,EAC9CuV,CAAAA,EAAU,AAA4C,KAAA,IAArC,IAAI,CAAC1V,KAAK,CAACK,OAAO,CAACF,KAAK,CAACuV,OAAO,EAE7C,IAAI,CAAC1V,KAAK,CAACK,OAAO,CAACF,KAAK,CAACuV,OAAO,AAAD,EAEnC,CAACA,GAAW,CAAC,IAAI,CAACnT,OAAO,CACzB,OAAOggB,EAAQnjB,IAAI,CAAC,IAAI,CAE5B,CAAA,IAAI,CAACY,KAAK,CAACuC,OAAO,CAAG,CAAA,EAErB,IAAM2W,EAAW6G,GAAwB,IAAI,CAAC/f,KAAK,CAAE,IAAI,EACrDkZ,IACA2G,GAA4B3G,EAAU,IAAI,EAC1CA,EAAS7D,UAAU,CAAC,IAAI,GAE5B+N,GAA0B,IAAI,CAClC,CAkDA,SAAS+D,GAAsB5E,CAAO,EAClC,GAAI,IAAI,CAAChgB,OAAO,CAAE,CACd,GAAIkgB,GAAY,IAAI,EAChB,MAAO,CAAC,EAEZ,GAAI,IAAI,CAAC7hB,KAAK,CAAC8B,SAAS,EAAI,IAAI,CAACK,KAAK,CAACL,SAAS,CAI5C,OAAO,IAAI,AAEnB,CACA,OAAO6f,EAAQ6E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC5S,KAAK,CAACpV,IAAI,CAACioB,UAAW,GACxD,CAOA,SAASC,GAAsB/E,CAAO,EAClC,IAAIgF,EAAgB,IAAI,CAAClnB,OAAO,CAAC4B,IAAI,CACrC,GAAI6d,GAAa,IAAI,CAAC9f,KAAK,GAAKyB,AAlhHqBjC,CAkhHH,CAAC,IAAI,CAACgC,IAAI,CAAC,CAAE,CAC3D,IAGAiiB,EAAYvjB,AAHG,IAAI,CAGA+E,EAAE,CAAC,YAClB,CAAC/E,AAJU,IAAI,CAIP+E,EAAE,CAAC,WACX,CAAC/E,AALU,IAAI,CAKP+E,EAAE,CAAC,YACX,CAAC/E,AANU,IAAI,CAMP+E,EAAE,CAAC,WAIf,GAEA,CAAC6d,GAZc,IAAI,CAYQyE,IACvB9D,GACAvjB,AAdW,IAAI,CAcR+E,EAAE,CAAC,YAEV/E,AAhBW,IAAI,CAgBRG,OAAO,CAACyN,QAAQ,EACvB,CAAC2U,GAjBU,IAAI,CAiBM,CAAA,GAAO,CAE5B,GAAIviB,AAnBO,IAAI,CAmBJqC,OAAO,EAAKrC,CAAAA,AAnBZ,IAAI,CAmBeU,KAAK,EAAE8B,WAAaxC,AAnBvC,IAAI,CAmB0C6C,KAAK,EAAEL,SAAQ,EACpE,MAGA+gB,CAAAA,GAAavjB,AAAsB,aAAtBA,AAvBN,IAAI,CAuBS6C,KAAK,CAACvB,IAAI,CAC9B0iB,GAAmB9kB,IAAI,CAxBhB,IAAI,CAwBqBioB,SAAS,CAAC,EAAE,EAG5C9E,EAAQ6E,KAAK,CA3BN,IAAI,CA2BW,EAAE,CAAC5S,KAAK,CAACpV,IAAI,CAACioB,UAAW,IAEnDE,EAAgBrnB,AA7BL,IAAI,CA6BQ8B,SAAS,CAAC,IAAK,CAAA,EAC1C,CAKA,GAFA9B,AAjCe,IAAI,CAiCZqC,OAAO,CAAGugB,GAjCF,IAAI,CAiCwByE,GAEvCrnB,AAnCW,IAAI,CAmCRqC,OAAO,CAAE,CAEhB,IAAI0P,GACA/R,AAtCO,IAAI,CAsCJG,OAAO,CAAC4B,IAAI,EAAEzB,QAEjB,AAACgf,GADLvN,EAAa/R,AAvCN,IAAI,CAuCSsnB,kBAAkB,CAACtnB,AAvChC,IAAI,CAuCmCG,OAAO,CAAC4B,IAAI,IAErD+Z,GAAQ/J,IACR/R,AA1CE,IAAI,CA0CC+E,EAAE,CAAC,YACXoa,EAAkB,GAAI,CAAA,EAAOnf,AA3C1B,IAAI,CA2C6BF,KAAK,EAGjDynB,AAvpBZ,SAAoBvnB,CAAM,EACtBA,EAAOC,KAAK,CAAGD,EAAOC,KAAK,EAAI,CAE3BkjB,SAAW,AAACqE,GAAOrE,GAASnjB,EAAQwnB,EACxC,EACA,IAAMC,EAAiBznB,EAAOC,KAAK,CAACynB,OAAO,CAAG,EAAE,CAoBhD,GAjBA,CAAC,UAAW,cAAe,iBAAiB,CAACnoB,OAAO,CAAC,AAACR,IAClD0oB,EAAe1jB,IAAI,CAAC,CAChBhF,KAAMA,EACN8L,IAAK7K,CAAM,CAACjB,EAAK,CACjB4oB,IAAKjpB,OAAOO,cAAc,CAACC,IAAI,CAACc,EAAQjB,EAC5C,EACJ,GACAiB,EAAO4nB,OAAO,CAAG,CAAA,EACjB5nB,EAAO6nB,WAAW,CAAG,CAAA,EACrB7nB,EAAO8nB,cAAc,CAAG,CAAA,EAExB9nB,EAAO+nB,iBAAiB,CAAG,CAAA,EAEvB/nB,EAAOgoB,aAAa,EACpBhoB,CAAAA,EAAOgoB,aAAa,CAAGhoB,EAAOgoB,aAAa,CAAC1f,OAAO,EAAC,EAGpDtI,EAAO+E,EAAE,CAAC,YACV,CAAC/E,EAAO+E,EAAE,CAAC,YACX/E,EAAO+B,IAAI,CAACzB,MAAM,CAAE,CACpB,IAAK,IAAMsT,KAAS5T,EAAO+B,IAAI,CAC3B6R,GAAOtL,WAEXtI,CAAAA,EAAO+B,IAAI,CAACzB,MAAM,CAAG,EACrBN,EAAOgC,MAAM,CAAC1B,MAAM,CAAG,EACvB,OAAON,EAAOukB,aAAa,AAC/B,CACJ,EAskBuB,IAAI,CA+CnB,MAEI0D,AAjnBZ,SAAmBjoB,CAAM,EACrB,IAAMC,EAAQD,EAAOC,KAAK,CAAEH,EAAQE,EAAOF,KAAK,CAAEslB,EAAatlB,EAAMG,KAAK,CAC1E,GAAImlB,GAAYxiB,YAGZ,IAAK,IAAMW,KAFX6hB,EAAWxiB,WAAW,CAAC0F,OAAO,GAC9B8c,EAAWxiB,WAAW,CAAG,KAAK,EACd9C,EAAME,MAAM,EACxBuD,EAAEX,WAAW,CAAG,KAAK,EACrBW,EAAEX,WAAW,CAAGW,EAAEoU,SAAS,CAAC,cAAe,UAAW,UAAW,EAAG7X,EAAM8X,WAAW,EAAEC,QAAQ,CAAC,sBAKpG5X,IACA,AAACA,CAAAA,EAAMynB,OAAO,EAAI,EAAE,AAAD,EAAGnoB,OAAO,CAAC,AAAC2oB,IACvBA,EAAQP,GAAG,CACX3nB,CAAM,CAACkoB,EAAQnpB,IAAI,CAAC,CAAGmpB,EAAQrd,GAAG,CAIlC,OAAO7K,CAAM,CAACkoB,EAAQnpB,IAAI,CAAC,AAEnC,GAEIkB,EAAMwC,KAAK,EACXxC,EAAMwC,KAAK,IAIlB3C,CAAAA,EAAM8X,WAAW,EAAI5X,EAAO+f,KAAK,AAAD,GAAI6B,MACzC,EAmiBuB,IAAI,CAoDvB,MAEIS,EAAQ6E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC5S,KAAK,CAACpV,IAAI,CAACioB,UAAW,GAErD,CAKA,SAASgB,GAAsB9F,CAAO,EAClC,IAAM7F,EAAS6F,EAAQ6E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC5S,KAAK,CAACpV,IAAI,CAACioB,UAAW,WAC5D,AAAI,IAAI,CAAClnB,KAAK,EAAIuc,EACP,IAAI,CAACvc,KAAK,CAACkjB,QAAQ,CAAC3G,GAExBA,CACX,CAY6B,IAAM4L,GANf,CAChBzkB,QAn/BJ,SAA6B0kB,CAAW,CAAEC,CAAW,CAAEjF,CAAU,CAAExf,CAAO,EACtE,GAAI2b,GAAuBX,EAAsB,gBAAiB,CAC9D,IAAM0J,EAAc3J,IAAa2J,WAAW,CAAEC,EAAcH,EAAYrpB,SAAS,CAkCjF,GAjCAigB,EAAqBoJ,EAAa,UAAWvF,IAC7C7D,EAAqBoJ,EAAa,OAAQpF,IACtCpf,GACA2kB,CAAAA,EAAYC,YAAY,CAAGtD,EAAiB,EAEhD1F,GAAK+I,EAAa,cAAevB,IACjCxH,GAAK+I,EAAa,cAAepB,IACjC3H,GAAK+I,EAAa,cAAeL,IACjC,CACI,YACA,iBACA,cACA,aACA,SACH,CAAC5oB,OAAO,CAAC,AAACmpB,GAAWC,AAu1B9B,CAAA,SAA6BH,CAAW,CAAEF,CAAW,CAAEI,CAAM,EAIzD,SAASE,EAAOvG,CAAO,EACnB,IAAMwG,EAAY,IAAI,CAAC1oB,OAAO,CAACyN,QAAQ,EAClC8a,CAAAA,AAAW,cAAXA,GAA0BA,AAAW,mBAAXA,CAA0B,CACrD,AAAC,CAAA,IAAI,CAACrmB,OAAO,GACbwmB,GACCjJ,GAAa,IAAI,CAAC9f,KAAK,GACxB,AAAc,YAAd,IAAI,CAACwB,IAAI,EACT,AAAc,YAAd,IAAI,CAACA,IAAI,EACRC,AA99G4CjC,CA89G1B,CAAC,IAAI,CAACgC,IAAI,CAAC,EAC9B,AAAgC,IAAhC,IAAI,CAACnB,OAAO,CAACiB,cAAc,CAIX,WAAXsnB,GAAuB,IAAI,CAACD,YAAY,EAC7C,IAAI,CAACA,YAAY,GAJjBpG,EAAQnjB,IAAI,CAAC,IAAI,CAMzB,CAGA,GAFAugB,GAAK+I,EAAaE,EAAQE,GAEtBF,AAAW,cAAXA,EACA,IAAK,IAAMpnB,IAAQ,CACf,SACA,YACA,cACA,UACA,UACH,CACOgnB,CAAW,CAAChnB,EAAK,EACjBme,GAAK6I,CAAW,CAAChnB,EAAK,CAACtC,SAAS,CAAE0pB,EAAQE,EAI1D,CAAA,EA33BkDJ,EAAaF,EAAaI,IACpEjJ,GAAK4D,EAAWrkB,SAAS,CAAE,iBAAkB,SAAUqjB,CAAO,CAAE/gB,CAAI,CAAE8B,CAAC,EACnE,GAAI9B,AAAS,UAATA,GAAoB,IAAI,CAACtB,MAAM,CAACqC,OAAO,CAAE,CACzC,IAAMuR,EAAQxQ,EAAEwQ,KAAK,CACrB,GAAI,AAACA,CAAAA,EAAM8P,IAAI,EAAI9P,EAAM+P,KAAK,AAAD,GAAO/P,CAAAA,EAAM5T,MAAM,CAACG,OAAO,CAACoV,MAAM,EAAE0B,QAAU,EAAC,EACxE,MAER,CACA,OAAOoL,EAAQ6E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC5S,KAAK,CAACpV,IAAI,CAACioB,UAAW,GACxD,GAEA9nB,EAAiBE,OAAO,CAAC,AAAC+B,IACtB,IAAMwnB,EAAkBP,CAAW,CAACjnB,EAAK,CACrCwnB,IACAA,EAAgB1nB,cAAc,CAAG,IACjC0nB,EAAgB1W,SAAS,CAAG,EAAE,CAC9BkW,CAAW,CAAChnB,EAAK,CAACtC,SAAS,CAACiZ,WAAW,CAAG,CAAA,EAElD,GACIpU,EAAS,CACT,GAAM,CAAEklB,KAAMC,CAAU,CAAEC,WAAYC,CAAgB,CAAEC,OAAQC,CAAY,CAAExN,OAAQyN,CAAY,CAAEC,QAASC,CAAa,CAAEC,QAASC,CAAa,CAAEC,QAASC,CAAa,CAAE,CAAGrB,EAe/K,GAdIU,GACA5J,GAAO4J,EAAWhqB,SAAS,CAAE,CACzB4T,KAAM,CAAA,EACNqF,YAAa,CAAA,EACbwN,SAAU,CAAA,CACd,GAEAyD,GACA9J,GAAO8J,EAAiBlqB,SAAS,CAAE,CAC/B4T,KAAM,CAAA,EACNqF,YAAa,CAAA,EACbwN,SAAU,CAAA,CACd,GAEA2D,EAAc,CACd,IAAMQ,EAAcR,EAAapqB,SAAS,AAG1C,QAAO4qB,EAAYlD,WAAW,CAG9BjH,GAAKmK,EAAa,gBAAiB,SAAUvH,CAAO,QAChD,CAAI,IAAI,CAAChgB,OAAO,EAGTggB,EAAQ6E,KAAK,CAAC,IAAI,CAAE,EAAE,CAAC5S,KAAK,CAACpV,IAAI,CAACioB,UAAW,GACxD,EACJ,CACIkC,GACAjK,GAAOiK,EAAarqB,SAAS,CAAE,CAC3B4T,KAAM,CAAA,EACN6S,SAAU,CAAA,CACd,GAEAgE,GACAA,CAAAA,EAAczqB,SAAS,CAAC4T,IAAI,CAAG,CAAA,CAAG,EAKtC,CAAC2W,EAAeI,EAAc,CAACpqB,OAAO,CAAC,AAACsqB,IAChCA,GACApK,GAAKoK,EAAG7qB,SAAS,CAAE,aAAcgoB,GAEzC,EACJ,CACJ,CACA,OAAOqB,CACX,EA85BIxG,gBAAAA,GACAG,UAAAA,GACAmB,SAAAA,EACJ,EAoBM,CAAEnf,iBAAkB8lB,EAA4B,CAAEjqB,sBAAuBkqB,EAAiC,CAAE,CAAGrmB,EAE/G,CAAEme,gBAAiBmI,EAA2B,CAAE,CAAG5B,GAEnD,CAAExb,MAAOqd,EAAiB,CAAE,CAAI1kB,IAEhC,CAAEsH,IAAKqd,EAAe,CAAEnL,KAAMoL,EAAgB,CAAE,CAAI/qB,IAEpD,CAAEM,SAAU0qB,EAAoB,CAAEzN,UAAW0N,EAAqB,CAAEtd,SAAUud,EAAoB,CAAErd,MAAOsd,EAAiB,CAAE5qB,KAAM6qB,EAAgB,CAAE/K,KAAMgL,EAAgB,CAAE,CAAIrrB,KAOxL,AAAC,SAAUtB,CAAW,EAOlB,IAQI4sB,EACAC,EATEC,EAAiB,qHAkBvB,SAASC,EAAiB5Q,CAAG,CAAEsM,CAAO,CAAExS,CAAK,CAAE+C,CAAO,CAAEgU,CAAS,EACzDA,GAAavE,IAAYuE,EAAUvE,OAAO,GAC1CtM,EAAIuB,MAAM,CAACsP,EAAUvE,OAAO,CAAEuE,EAAUhU,OAAO,EAC/CmD,EAAIwB,MAAM,CAACqP,EAAUvE,OAAO,CAAEuE,EAAU/W,KAAK,EAC7CkG,EAAIwB,MAAM,CAAC8K,EAASxS,GACpBkG,EAAIwB,MAAM,CAAC8K,EAASzP,GAE5B,CAIA,SAASiU,EAAsB9Q,CAAG,CAAEsM,CAAO,CAAExS,CAAK,CAAErS,CAAC,CAAE4P,CAAC,EACpD2I,EAAIuB,MAAM,CAAC+K,EAASxS,GACpBkG,EAAIoB,GAAG,CAACkL,EAASxS,EAAO,IAAI,CAACiX,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC1Z,EAAE,CAAE,EAAG,EAAInM,KAAKmW,EAAE,CAAE,CAAA,EACzE,CAIA,SAAS2P,EAAmBhR,CAAG,CAAEsM,CAAO,CAAExS,CAAK,CAAE+C,CAAO,EACpDmD,EAAIiR,IAAI,CAAC3E,EAAU,EAAGxS,EAAO,EAAG+C,EAAU/C,EAC9C,CAmDA,SAASoX,IACD,IAAI,CAAClrB,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC0gB,IAAI,EAC7B,IAAI,CAAC1gB,KAAK,CAAC0gB,IAAI,EAEvB,CAIA,SAASyK,IACL,IAAMnrB,EAAQ,IAAI,CAACA,KAAK,EAAI,CAAC,CACzBA,CAAAA,EAAMgE,MAAM,EACZhE,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CAAEC,KAAMkK,CAAc,GAExC3qB,EAAMyC,MAAM,EACZzC,EAAMyC,MAAM,CAAC4W,UAAU,CAAC,MAAM+R,SAAS,CAAC,EAAG,EAAGprB,EAAMyC,MAAM,CAAC8B,KAAK,CAAEvE,EAAMyC,MAAM,CAACgC,MAAM,CAE7F,CAOA,SAAS4mB,IACAvB,GAAkC,IAAI,CAACjqB,KAAK,EAQxC,IAAI,CAACG,KAAK,EAAI,IAAI,CAACA,KAAK,CAACwC,KAAK,EACnC,IAAI,CAACxC,KAAK,CAACwC,KAAK,GARZ,IAAI,CAACxC,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC0gB,IAAI,CAC7B,IAAI,CAAC1gB,KAAK,CAAC0gB,IAAI,GAEV,IAAI,CAAC7gB,KAAK,CAACG,KAAK,EAAI,IAAI,CAACH,KAAK,CAACG,KAAK,CAAC0gB,IAAI,EAC9C,IAAI,CAAC7gB,KAAK,CAACG,KAAK,CAAC0gB,IAAI,EAMjC,CAIA,SAAS4K,EAAgBtR,CAAG,CAAEsM,CAAO,CAAExS,CAAK,EACxCkG,EAAIwB,MAAM,CAAC8K,EAASxS,EACxB,CAQA,SAASyX,IACL,IAKIvR,EALEna,EAAQ,IAAI,CAACA,KAAK,CAAEmE,EAAS8lB,GAAkCjqB,GAASA,EAAQ,IAAI,CAAEggB,EAAe7b,IAAWnE,EAClHA,EAAM8X,WAAW,CACjB9X,EAAM8X,WAAW,EAAI,IAAI,CAACmI,KAAK,CAAGvb,EAAQ1E,EAAMyW,UAAU,CAAE7R,EAAS5E,EAAM0W,WAAW,CAAEiV,EAAS,SAAUpJ,CAAO,CAAEje,CAAC,CAAEE,CAAC,CAAEjG,CAAC,CAAEkO,CAAC,CAAEmf,CAAC,CAAEttB,CAAC,EACpIikB,EAAQnjB,IAAI,CAAC,IAAI,CAAEoF,EAAGF,EAAG/F,EAAGkO,EAAGmf,EAAGttB,EACtC,EAEM6B,EAAQgE,EAAOhE,KAAK,CACtBgE,EAAOhE,KAAK,EACR,CAAC,EAmDT,OAlDAga,EAAMha,EAAM8gB,SAAS,CAChB9gB,EAAMyC,MAAM,GACbzC,EAAMyC,MAAM,CAAGwnB,GAAgBlQ,aAAa,CAAC,UAC7C/Z,EAAMgE,MAAM,CAAGnE,EAAMkZ,QAAQ,CACxBuH,KAAK,CAAC,GAAI,EAAG,EAAG/b,EAAOE,GACvBmT,QAAQ,CAAC,2BACT2I,GAAG,CAACV,GACT7F,EAAMha,EAAM8gB,SAAS,CACjB9gB,EAAMyC,MAAM,CAAC4W,UAAU,CAAC,MACxBxZ,EAAMwD,QAAQ,EACd,CAAC,SAAU,SAAU,OAAQ,MAAM,CAAC/D,OAAO,CAAC,AAACua,IACzC2Q,GAAiBxQ,EAAKH,EAAI2R,EAC9B,GAEJxrB,EAAM0gB,IAAI,CAAG,WACT1gB,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CACdC,KAAMzgB,EAAMyC,MAAM,CAACme,SAAS,CAAC,YACjC,EACJ,EACA5gB,EAAMwC,KAAK,CAAG,WACVwX,EAAIoR,SAAS,CAAC,EAAG,EAAGprB,EAAMyC,MAAM,CAAC8B,KAAK,CAAEvE,EAAMyC,MAAM,CAACgC,MAAM,EACvDT,IAAWhE,EAAMgE,MAAM,EACvBhE,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CACdC,KAAMkK,CACV,EAER,EACA3qB,EAAMshB,QAAQ,CAAGzhB,EAAMkZ,QAAQ,CAACuI,QAAQ,GACxCthB,EAAMgE,MAAM,CAAC2d,IAAI,CAAC3hB,EAAMshB,QAAQ,GAKhCthB,EAAMyC,MAAM,CAAC8B,KAAK,GAAKA,GACvBvE,CAAAA,EAAMyC,MAAM,CAAC8B,KAAK,CAAGA,CAAI,EAEzBvE,EAAMyC,MAAM,CAACgC,MAAM,GAAKA,GACxBzE,CAAAA,EAAMyC,MAAM,CAACgC,MAAM,CAAGA,CAAK,EAE/BzE,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CACdrc,EAAG,EACHE,EAAG,EACHE,MAAOA,EACPE,OAAQA,EACRinB,MAAO,uBACPjL,KAAMkK,CACV,GACI3qB,EAAMshB,QAAQ,EACdthB,EAAMshB,QAAQ,CAACd,IAAI,CAACqJ,GAA6BhqB,EAAOmE,IAErDgW,CACX,CAIA,SAASkL,IACL,IAAMnlB,EAAS,IAAI,CAAEG,EAAUH,EAAOG,OAAO,CAAEL,EAAQE,EAAOF,KAAK,CAAEY,EAAQV,EAAOU,KAAK,CAAEmC,EAAQ7C,EAAO6C,KAAK,CAAE+oB,EAAsB9rB,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,EAAG4rB,EAAgB,CAC9Kpd,cAAemd,EAAoBnd,aAAa,EAAI,CAAA,EACpDC,qBAAsBkd,EAAoBld,oBAAoB,EAAI,CAAA,EAClEC,UAAWid,EAAoBjd,SAAS,EAAI,CAAA,CAChD,EAAGhB,EAAQ3N,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAOsO,EAAQpQ,EAAO8B,SAAS,CAAC,IAAK,CAAA,GAAO6N,EAAUxP,EAAQ4B,IAAI,CAAE6N,EAAYlP,EAAMmP,WAAW,GAAIC,EAAOF,EAAUjP,GAAG,CAAEqP,EAAOJ,EAAU9O,GAAG,CAAEmP,EAAYpN,EAAMgN,WAAW,GAAIK,EAAOD,EAAUtP,GAAG,CAAEwP,EAAOF,EAAUnP,GAAG,CAAE0kB,EAAa,CAAC,EAAGC,EAAW,CAAC,CAACzlB,EAAOylB,QAAQ,CAAE/jB,EAAIvB,EAAQoV,MAAM,EAAIpV,EAAQoV,MAAM,CAAC0B,MAAM,CAAE6U,EAAc9rB,EAAO+rB,cAAc,EAAI,IAAMrG,EAAsBvlB,EAAQulB,mBAAmB,CAAEtlB,EAAYD,EAAQC,SAAS,CAAEyW,EAAeyT,GAAqBlqB,GAAY4rB,EAAsBnpB,EAAMkU,YAAY,CAAC3W,GAAY6rB,EAASjsB,EAAO4S,IAAI,CAAEpD,EAAWxP,EAAOyP,aAAa,EAChnBzP,AAAmC,aAAnCA,EAAOyP,aAAa,CAAC7H,IAAI,CAAC,KAAsB8F,EAAY,CAAC,CAACvN,EAAQyN,QAAQ,CAAEsX,EAAYllB,EAAOklB,SAAS,EAAI,EAAGgH,EAAiBpsB,EAAMK,OAAO,CAACgsB,OAAO,CAAExG,EAAiB3lB,EAAO2lB,cAAc,CAAEpV,EAAepQ,EAAQoQ,YAAY,CAAED,EAAS,CAAC3C,EAAO6C,EAAS9C,EAClQ1N,EAAO+B,IAAI,CACV4L,GAASgC,EAAWvB,EAAapO,EAAOiY,WAAW,CACpD1S,IAAmDqH,KAAK,CAAC5M,EAAO8J,KAAK,EAAEoO,UAAU,CAACsS,GAAiBrqB,EAAQ8X,WAAW,CAAE,MAAOpZ,GAAG,GAClImB,EAAO8J,KAAK,CAAG8b,EAAWzlB,AAA+B,MAA/BA,EAAQ0lB,kBAAkB,CAAU5lB,EAAQ,IAAI,CAACA,KAAK,EAAI,CAAC,EAAGmsB,EAAepsB,EAAOosB,YAAY,CAAEC,EAAYlsB,EAAQgO,SAAS,CAAGnO,EAAOqsB,SAAS,CAAG,KAAK,EAAGC,EAAa5qB,GAAKA,GAAK,EAC9M1B,EAAOusB,eAAe,CACtBvsB,EAAOwsB,eAAe,AACtBvsB,CAAAA,EAAMgE,MAAM,EACZhE,EAAMgE,MAAM,CAACwc,IAAI,CAAC,CAAEC,KAAMkK,CAAc,GAGxC5qB,CAAAA,EAAOgC,MAAM,EAAIhC,EAAOqmB,KAAK,AAAD,GAC5B2D,GAA4BhqB,GAGhCA,EAAO2X,SAAS,CAAC,QAAS,SAAU3X,EAAOqB,OAAO,CAAG,UAAY,SAAUlB,EAAQqhB,MAAM,CAAE1hB,EAAM8X,WAAW,EAC5G5X,EAAO4C,WAAW,CAAG5C,EAAO+f,KAAK,CACjCqK,GAAqBpqB,EAAQ,UAAW,WAEpCA,EAAO4C,WAAW,CAAG,IACzB,GACA,IAAMZ,EAAS,IAAI,CAACA,MAAM,CAAG,EAAE,CAAEiY,EAAM,IAAI,CAACX,UAAU,GAatD,GAZAtZ,EAAO0mB,WAAW,CAAGyD,GACjBlqB,EAAMwC,KAAK,EACXxC,EAAMwC,KAAK,GAUX,CAACzC,EAAOqB,OAAO,CACf,MAGAsO,CAAAA,EAAQrP,MAAM,CAAG,QACjBR,EAAMK,OAAO,CAACgsB,OAAO,CAAG5B,GAAkB2B,EAAgB,CACtDO,WAAY,CACRC,gBAAiBzC,GAAkB,WAAyC/R,UAAU,CAAC,KAAMrZ,GAAG,GAChG8tB,QAAS,MACTC,aAAc,OAClB,EACAjB,MAAO,CACHe,gBAAiB,OACjBpL,QAAS,CACb,CACJ,GACAliB,IAA6CytB,YAAY,CAAClC,GAC1D7qB,EAAMgtB,WAAW,CAAC,cAClBhtB,EAAMK,OAAO,CAACgsB,OAAO,CAAGD,GAExBL,EAAcpd,aAAa,EAC3BqG,QAAQO,IAAI,CAAC,oBAGjB,IAAIqW,EAAI,EAAGzF,EAAa6E,EAAWhU,EAAUkV,EAAqBe,EAAS9b,EAAQiV,EAAQC,EAAMC,EAAM1oB,GAEjG0W,GAAS,WACP6X,GACAhS,EAAIM,SAAS,CAAGnM,EAChB6L,EAAIrH,IAAI,KAGRqH,EAAIK,WAAW,CAAGta,EAAO8J,KAAK,CAC9BmQ,EAAI9L,SAAS,CAAGhO,EAAQgO,SAAS,CACjC8L,EAAI7F,MAAM,GAElB,EAEA4Y,GAAY,SAAUzG,CAAO,CAAExS,CAAK,CAAE+C,CAAO,CAAExF,CAAC,EAClC,IAANoa,IACAzR,EAAImB,SAAS,GACTiR,GACApS,CAAAA,EAAIgT,QAAQ,CAAG,OAAM,GAGzBntB,EAAMotB,QAAQ,EACdltB,AACI,gCADJA,EAAOG,OAAO,CAACgtB,SAAS,EAExBpZ,GAASjU,EAAMotB,QAAQ,CAACtoB,GAAG,CACvBkS,GACAA,CAAAA,GAAWhX,EAAMotB,QAAQ,CAACtoB,GAAG,AAAD,GAIhCmP,GAASjU,EAAMyE,OAAO,CAE1BgiB,GAAWzmB,EAAMuE,QAAQ,CACrB0oB,EACA9S,EAAIuB,MAAM,CAAC+K,EAASxS,GAGhBqY,EACAA,EAAanS,EAAKsM,EAASxS,EAAO+C,EAASgU,GAEtCuB,EACLA,EAAUpS,EAAKsM,EAASxS,GAEnBuY,GACLA,EAAUptB,IAAI,CAACc,EAAQia,EAAKsM,EAASxS,EAAOrS,EAAG4P,GAMvDoa,CAAAA,GAAQ,CAAA,IACEI,IACN1X,KACAsX,EAAI,GAGRZ,EAAY,CACRvE,QAASA,EACTxS,MAAOA,EACP+C,QAASA,CACb,CACJ,EAAGgP,GAAa,AAAC,CAAA,IAAI,CAAChkB,SAAS,CAAC,KAAKxB,MAAM,CAAG,IAAI,CAACwB,SAAS,CAAC,KAAO,KAAK,CAAA,GACrE,IAAI,CAAC3B,OAAO,CAACwN,KAAK,EACjB,EAAA,IAAI,CAAC7L,SAAS,CAAC,IAAK,CAAA,GAAMxB,MAAM,EAC7B,IAAI,CAACwB,SAAS,CAAC,IAAK,CAAA,GAG5BwkB,GAAa,SAAUC,CAAO,CAAExS,CAAK,CAAEzC,CAAC,EAEpC5T,GAAQkoB,EAAWW,EAAUA,EAAU,IAAMxS,EAIzC2R,GAAuB,CAACF,CAAU,CAAC9nB,GAAM,GACzC8nB,CAAU,CAAC9nB,GAAM,CAAG,CAAA,EAChBoC,EAAMwD,QAAQ,GACdijB,EAAU7lB,EAAM0E,GAAG,CAAGmhB,EACtBxS,EAAQlR,EAAMuC,GAAG,CAAG2O,GAExB/R,EAAO+B,IAAI,CAAC,CACRK,EAAG0hB,EAAAA,IACCA,EAAS,CAACZ,EAAY5T,EAAE,CAE5BiV,QAASA,EACT3C,MAAO2C,EACPxS,MAAOA,EACPzC,EAAG4T,EAAY5T,CACnB,GAER,EAEA8W,GAAkBpG,SAAS,CAACxR,EAAO,CAACpS,EAAGkT,KACnC,IAAMX,EAAiB,AAAuB,KAAA,IAAhB7Q,EAAMpC,KAAK,CACrC0G,EAAGE,EAAGiiB,EAASxS,EAAOqZ,EAAQ3b,EAAK4b,EAAe,CAAA,EAAOC,EAAe,CAAA,EAAO9b,EAAK+b,IAAKhc,EAAKgc,IAAKzb,EAAY,CAAA,EAsGnH,MArGI,CAACnB,IACGL,GACAlM,EAAIhG,CAAC,CAAC,EAAE,CACRkG,EAAIlG,CAAC,CAAC,EAAE,CACJoS,CAAK,CAACc,EAAI,EAAE,EACZE,CAAAA,EAAKhB,CAAK,CAACc,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBd,CAAK,CAACc,EAAI,EAAE,EACZC,CAAAA,EAAKf,CAAK,CAACc,EAAI,EAAE,CAAC,EAAE,AAAD,IAIvBlN,EAAIhG,EACJkG,EAAI8L,CAAK,CAACkB,EAAE,CACRd,CAAK,CAACc,EAAI,EAAE,EACZE,CAAAA,EAAKhB,CAAK,CAACc,EAAI,EAAE,AAAD,EAEhBd,CAAK,CAACc,EAAI,EAAE,EACZC,CAAAA,EAAKf,CAAK,CAACc,EAAI,EAAE,AAAD,GAGpBE,GAAMA,GAAM1B,GAAQ0B,GAAMxB,GAC1Bqd,CAAAA,EAAe,CAAA,CAAG,EAElB9b,GAAMA,GAAMzB,GAAQyB,GAAMvB,GAC1Bsd,CAAAA,EAAe,CAAA,CAAG,EAGlB9d,GACIc,GACAhM,CAAAA,EAAIlG,EAAEkW,KAAK,CAAC,EAAG,EAAC,EAEpB7C,EAAMnN,CAAC,CAAC,EAAE,CACVA,EAAIA,CAAC,CAAC,EAAE,EAEHoJ,IACLtJ,EAAIhG,EAAEgG,CAAC,CAEPqN,EAAMnN,AADNA,CAAAA,EAAIlG,EAAEmW,MAAM,AAAD,EACDnW,EAAEkG,CAAC,EAEjB8oB,EAAS9oB,AAAM,OAANA,EAEJqhB,GACD7T,CAAAA,EAAYxN,GAAK4L,GAAQ5L,GAAK6L,CAAG,EAEjC,CAACid,GACA,CAAA,AAAChpB,GAAK0L,GAAQ1L,GAAK4L,GAAQ8B,GACvBub,GAAgBC,CAAY,IACjC/G,EAAUphB,KAAKqoB,KAAK,CAAC9sB,EAAM+T,QAAQ,CAACrQ,EAAG,CAAA,IACnCqhB,GACI,CAAA,AAAgB,KAAA,IAATU,GACPI,IAAYN,CAAU,IACjBzW,GACDiC,CAAAA,EAAMnN,CAAAA,EAEN,CAAA,AAAgB,KAAA,IAAT8hB,GAAwB9hB,EAAI4hB,CAAK,IACxCA,EAAS5hB,EACT8hB,EAAO9U,GAEP,CAAA,AAAgB,KAAA,IAAT6U,GACP1U,EAAMR,CAAK,IACXA,EAASQ,EACT0U,EAAO7U,IAIXiV,IAAYN,IAEQ,KAAA,IAATE,IACPpS,EAAQlR,EAAM4R,QAAQ,CAACyR,EAAQ,CAAA,GAC/BpP,EAAUjU,EAAM4R,QAAQ,CAACxD,EAAQ,CAAA,GACjC+b,GAAUzG,EAAS1P,EACf1R,KAAKxE,GAAG,CAACoT,EAAOiY,GAAuBjY,EAAO8C,EAC9C1R,KAAKrE,GAAG,CAACgW,EAASkV,GAAuBlV,EAASxF,GACtDgV,GAAWC,EAASxS,EAAOqS,GACvBtP,IAAY/C,GACZuS,GAAWC,EAASzP,EAASqP,IAGrCA,EAAOC,EAAO,KAAK,EACnBH,EAAcM,KAKlByG,GAAUzG,EADVxS,EAAQ5O,KAAKqoB,KAAK,CAAC3qB,EAAM4R,QAAQ,CAACnQ,EAAG,CAAA,IACXwS,EAASxF,GACnCgV,GAAWC,EAASxS,EAAOzC,KAGnCyb,EAAUK,GAAU,CAAC7c,EACjBe,EAjcG,KAicgB,IACftR,EAAOC,KAAK,EACZD,EAAOC,KAAK,CAAC0gB,IAAI,CACjB3gB,EAAOC,KAAK,CAAC0gB,IAAI,GAEZ3gB,EAAOF,KAAK,CAACG,KAAK,EACvBD,EAAOF,KAAK,CAACG,KAAK,CAAC0gB,IAAI,EACvB3gB,EAAOF,KAAK,CAACG,KAAK,CAAC0gB,IAAI,KAI5B,CAAChQ,CACZ,EAAG,WACC,IAAM8c,EAAa3tB,EAAM2tB,UAAU,CAAEC,EAAe5tB,EAAM4tB,YAAY,CACtEtZ,KAIApU,EAAOkC,WAAW,GACd2pB,EAAcpd,aAAa,EAC3BqG,QAAQY,OAAO,CAAC,oBAEpB2U,GAAsBrqB,EAAQ,kBAK1B0tB,IACAD,EAAW9B,KAAK,CAACgC,UAAU,CAAG,gBAC9BF,EAAWnM,OAAO,CAAG,EACrBxhB,EAAM4tB,YAAY,CAAG,CAAA,EACrB/C,EAAoBzR,WAAW,WACvBuU,EAAWG,UAAU,EACrBH,EAAWG,UAAU,CAACC,WAAW,CAACJ,GAEtC3tB,EAAM2tB,UAAU,CAAG3tB,EAAMguB,WAAW,CAAG,IAC3C,EAAG,MAGP,OAAO9tB,EAAO0mB,WAAW,CACzB1mB,EAAO0mB,WAAW,EAGtB,EAAG5mB,EAAMkZ,QAAQ,CAACC,SAAS,CAAGtX,OAAOC,SAAS,CAAG,KAAK,EAC1D,CAIA,SAASmsB,EAAuB9T,CAAG,CAAEsM,CAAO,CAAExS,CAAK,CAAErS,CAAC,EAClDuY,EAAIuB,MAAM,CAAC+K,EAASxS,GACpBkG,EAAIoB,GAAG,CAACkL,EAASxS,EAAOrS,EAAG,EAAG,EAAIyD,KAAKmW,EAAE,CAAE,CAAA,EAC/C,CAKA,SAAS0S,EAAuB/T,CAAG,CAAEsM,CAAO,CAAExS,CAAK,CAAErS,CAAC,EAClDuY,EAAIiR,IAAI,CAAC3E,EAAU7kB,EAAGqS,EAAQrS,EAAGA,AAAI,EAAJA,EAAOA,AAAI,EAAJA,EAC5C,CAIA,SAASusB,IACL,IAAMnuB,EAAQ,IAAI,CAACA,KAAK,CAAEma,EAAM,IAAI,CAACX,UAAU,GAAIhW,EAAW,IAAI,CAACxD,KAAK,CAACwD,QAAQ,CAAE5C,EAAQ,IAAI,CAACA,KAAK,CAAEmC,EAAQ,IAAI,CAACA,KAAK,CACrHoX,GAEA,IAAI,CAACjY,MAAM,CAACzC,OAAO,CAAC,AAACqU,IACjB,IACIE,EADEC,EAAQH,EAAMG,KAAK,CAEzB,GAAI,AAAiB,KAAA,IAAVA,GACP,CAACC,MAAMD,IACPH,AAAY,OAAZA,EAAMtP,CAAC,EACP2V,EAAK,CACL,GAAM,CAAE7V,EAAAA,EAAI,CAAC,CAAEE,EAAAA,EAAI,CAAC,CAAEE,MAAAA,EAAQ,CAAC,CAAEE,OAAAA,EAAS,CAAC,CAAE,CAAGkP,EAAMK,SAAS,EAAI,CAAC,CAOpEgG,CAAAA,EAAIM,SAAS,CAAGzG,CANXhU,EAAMoU,UAAU,CAILN,EAAM5T,MAAM,CAACmU,YAAY,CAACP,GAH1BA,EAAM5T,MAAM,CAAC2S,YAAY,CAACiB,IAKhBhB,IAAI,CAC1BtP,EACA2W,EAAIsB,QAAQ,CAAC1Y,EAAMuC,GAAG,CAAGd,EAAI5D,EAAMoE,IAAI,CAAEpE,EAAM0E,GAAG,CAAGhB,EAAIvB,EAAM+B,GAAG,CAAE,CAACF,EAAQ,CAACF,GAG9EyV,EAAIsB,QAAQ,CAACnX,EAAI1D,EAAMoE,IAAI,CAAER,EAAIzB,EAAM+B,GAAG,CAAEJ,EAAOE,EAE3D,CACJ,GACA,IAAI,CAACxC,WAAW,IAGhB,IAAI,CAACpC,KAAK,CAACgtB,WAAW,CAAC,6EAG/B,CA7cAhvB,EAAY6F,OAAO,CA3CnB,SAAiBC,CAAU,CAAEykB,CAAW,CAAEC,CAAW,EACjD,IAAME,EAAcH,EAAYrpB,SAAS,CACzC,GAAI,CAACwpB,EAAYC,YAAY,CAAE,CAC3B,GAAM,CAAEM,KAAMC,CAAU,CAAEG,OAAQC,CAAY,CAAExN,OAAQyN,CAAY,CAAEC,QAASC,CAAa,CAAEC,QAASC,CAAa,CAAE,CAAGnB,EAUzH,GARA1kB,EAAW5E,SAAS,CAAC8E,SAAS,CAACC,IAAI,CAAC,AAACjE,IACjCsqB,GAAqBtqB,EAAO,UAAWsrB,GACvChB,GAAqBtqB,EAAO,SAAUqrB,EAC1C,GACA3C,EAAYtmB,WAAW,CAAGopB,EAC1B9C,EAAY6D,SAAS,CAAGd,EACxB/C,EAAYlP,UAAU,CAAGkS,EACzBhD,EAAYC,YAAY,CAAGtD,EACvB6D,EAAY,CACZ,IAAMkF,EAAYlF,EAAWhqB,SAAS,AACtCkvB,CAAAA,EAAU9B,YAAY,CAAGvB,EACzBqD,EAAUtb,IAAI,CAAG,CAAA,EACjBsb,EAAUjW,WAAW,CAAG,CAAA,EACxBiW,EAAUzI,QAAQ,CAAG,CAAA,CACzB,CACA,GAAI2D,EAAc,CACd,IAAMQ,EAAcR,EAAapqB,SAAS,AAC1C4qB,CAAAA,EAAY4C,eAAe,CAAGzB,EAC9BnB,EAAYmC,cAAc,CAAG,CACjC,CACA,GAAI1C,EAAc,CACd,IAAM8E,EAAc9E,EAAarqB,SAAS,AAC1CmvB,CAAAA,EAAY/B,YAAY,CAAGnB,EAC3BkD,EAAYvb,IAAI,CAAG,CAAA,EACnBub,EAAY1I,QAAQ,CAAG,CAAA,CAC3B,CAKA,GAJI8D,GAEAkB,GADqBlB,EAAcvqB,SAAS,CACb,aAAcivB,GAE7CxE,EAAe,CACf,IAAM2E,EAAe3E,EAAczqB,SAAS,AAC5CovB,CAAAA,EAAa5B,eAAe,CAAGuB,EAC/BK,EAAa7B,eAAe,CAAGyB,EAC/BI,EAAaxb,IAAI,CAAG,CAAA,CACxB,CACJ,CACJ,CA+cJ,EAAG9U,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAML,IAAMuwB,GAA0BvwB,EAOvDwwB,GAAKlvB,GAMXkvB,CAAAA,GAAEC,eAAe,CAAG,WAChBF,GAAuB1qB,OAAO,CAAC2qB,GAAEE,KAAK,CAAEF,GAAEG,MAAM,CAAEH,GAAEhG,WAAW,CACnE,EAC6B,IAAMoG,GAAqBtvB,WAE/CsvB,MAAoBC,OAAO"}