let t,e;import*as i from"../highcharts.js";import"./broken-axis.js";import"./datagrouping.js";import"./mouse-wheel-zoom.js";var s,o,a,r,n,l,h,d={};d.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return d.d(e,{a:e}),e},d.d=(t,e)=>{for(var i in e)d.o(e,i)&&!d.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},d.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let p=i.default;var c=d.n(p);let u=i.default.Axis;var g=d.n(u);let m=i.default.Point;var x=d.n(m);let f=i.default.Series;var b=d.n(f);let{tooltipFormatter:v}=x().prototype,{addEvent:y,arrayMax:M,arrayMin:A,correctFloat:w,defined:k,isArray:S,isNumber:O,isString:E,pick:C}=c();!function(t){function e(t,e,i){!this.isXAxis&&(this.series.forEach(function(i){"compare"===t&&"boolean"!=typeof e?i.setCompare(e,!1):"cumulative"!==t||E(e)||i.setCumulative(e,!1)}),C(i,!0)&&this.chart.redraw())}function i(t){let e=this,{numberFormatter:i}=e.series.chart,s=function(s){t=t.replace("{point."+s+"}",(e[s]>0&&"change"===s?"+":"")+i(e[s],C(e.series.tooltipOptions.changeDecimals,2)))};return k(e.change)&&s("change"),k(e.cumulativeSum)&&s("cumulativeSum"),v.apply(this,[t])}function s(){let t,e=this.options.compare;("percent"===e||"value"===e||this.options.cumulative)&&(t=new d(this),"percent"===e||"value"===e?t.initCompare(e):t.initCumulative()),this.dataModify=t}function o(t){let e=t.dataExtremes,i=e.activeYData;if(this.dataModify&&e){let t;this.options.compare?t=[this.dataModify.modifyValue(e.dataMin),this.dataModify.modifyValue(e.dataMax)]:this.options.cumulative&&S(i)&&i.length>=2&&(t=d.getCumulativeExtremes(i)),t&&(e.dataMin=A(t),e.dataMax=M(t))}}function a(t,e){this.options.compare=this.userOptions.compare=t,this.update({},C(e,!0)),this.dataModify&&("value"===t||"percent"===t)?this.dataModify.initCompare(t):this.points.forEach(t=>{delete t.change})}function r(){let t=this.getColumn(this.pointArrayMap&&(this.options.pointValKey||this.pointValKey)||"y",!0);if(this.xAxis&&t.length&&this.dataModify){let e=this.getColumn("x",!0),i=this.dataTable.rowCount,s=+(!0!==this.options.compareStart);for(let o=0;o<i-s;o++){let i=t[o];if(O(i)&&0!==i&&e[o+s]>=(this.xAxis.min||0)){this.dataModify.compareValue=i;break}}}}function n(t,e){this.setModifier("compare",t,e)}function l(t,e){t=C(t,!1),this.options.cumulative=this.userOptions.cumulative=t,this.update({},C(e,!0)),this.dataModify?this.dataModify.initCumulative():this.points.forEach(t=>{delete t.cumulativeSum})}function h(t,e){this.setModifier("cumulative",t,e)}t.compose=function(t,d,p){let c=d.prototype,u=p.prototype,g=t.prototype;return g.setCompare||(g.setCompare=a,g.setCumulative=l,y(t,"afterInit",s),y(t,"afterGetExtremes",o),y(t,"afterProcessData",r)),c.setCompare||(c.setCompare=n,c.setModifier=e,c.setCumulative=h,u.tooltipFormatter=i),t};class d{constructor(t){this.series=t}modifyValue(){return 0}static getCumulativeExtremes(t){let e=1/0,i=-1/0;return t.reduce((t,s)=>{let o=t+s;return e=Math.min(e,o,t),i=Math.max(i,o,t),o}),[e,i]}initCompare(t){this.modifyValue=function(e,i){null===e&&(e=0);let s=this.compareValue;if(void 0!==e&&void 0!==s){if("value"===t?e-=s:e=e/s*100-100*(100!==this.series.options.compareBase),void 0!==i){let t=this.series.points[i];t&&(t.change=e)}return e}return 0}}initCumulative(){this.modifyValue=function(t,e){if(null===t&&(t=0),void 0!==t&&void 0!==e){let i=e>0?this.series.points[e-1]:null;i&&i.cumulativeSum&&(t=w(i.cumulativeSum+t));let s=this.series.points[e],o=s.series.options.cumulativeStart,a=s.x<=this.series.xAxis.max&&s.x>=this.series.xAxis.min;return s&&(!o||a?s.cumulativeSum=t:s.cumulativeSum=void 0),t}return 0}}}t.Additions=d}(o||(o={}));let T=o,{isTouchDevice:B}=c(),{addEvent:D,merge:P,pick:R}=c(),z=[];function I(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function L(){let t,e,i,s=this.legend,o=this.navigator;if(o){t=s&&s.options,e=o.xAxis,i=o.yAxis;let{scrollbarHeight:a,scrollButtonSize:r}=o;this.inverted?(o.left=o.opposite?this.chartWidth-a-o.height:this.spacing[3]+a,o.top=this.plotTop+r):(o.left=R(e.left,this.plotLeft+r),o.top=o.navigatorOptions.top||this.chartHeight-o.height-a-(this.scrollbar?.options.margin||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(t&&"bottom"===t.verticalAlign&&"proximate"!==t.layout&&t.enabled&&!t.floating?s.legendHeight+R(t.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),e&&i&&(this.inverted?e.options.left=i.options.left=o.left:e.options.top=i.options.top=o.top,e.setAxisSize(),i.setAxisSize())}}function W(e){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new t(this),R(e.redraw,!0)&&this.redraw(e.animation))}function X(){let e=this.options;(e.navigator.enabled||e.scrollbar.enabled)&&(this.scroller=this.navigator=new t(this))}function G(){let t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!B&&"x"===this.zooming.type||B&&"x"===this.zooming.pinchType))return!1}function Y(t){let e=t.navigator;if(e&&t.xAxis[0]){let i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function U(t){let e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(P(!0,this.options.navigator,e),P(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}let N=function(e,i){if(c().pushUnique(z,e)){let s=e.prototype;t=i,s.callbacks.push(Y),D(e,"afterAddSeries",I),D(e,"afterSetChartSize",L),D(e,"afterUpdate",W),D(e,"beforeRender",X),D(e,"beforeShowResetZoom",G),D(e,"update",U)}},{isTouchDevice:H}=c(),{addEvent:F,correctFloat:V,defined:_,isNumber:j,pick:Z}=c();function q(){this.navigatorAxis||(this.navigatorAxis=new $(this))}function K(t){let e,i=this.chart,s=i.options,o=s.navigator,a=this.navigatorAxis,r=i.zooming.pinchType,n=s.rangeSelector,l=i.zooming.type;if(this.isXAxis&&(o?.enabled||n?.enabled)){if("y"===l&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===l||H&&"xy"===r)&&this.options.range){let e=a.previousZoom;_(t.min)?a.previousZoom=[this.min,this.max]:e&&(t.min=e[0],t.max=e[1],a.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}class ${static compose(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),F(t,"init",q),F(t,"setExtremes",K))}constructor(t){this.axis=t}destroy(){this.axis=void 0}toFixedRange(t,e,i,s){let o=this.axis,a=(o.pointRange||0)/2,r=Z(i,o.translate(t,!0,!o.horiz)),n=Z(s,o.translate(e,!0,!o.horiz));return _(i)||(r=V(r+a)),_(s)||(n=V(n-a)),j(r)&&j(n)||(r=n=void 0),{min:r,max:n}}}let J=i.default.Color;var Q=d.n(J);let tt=i.default.SeriesRegistry;var te=d.n(tt);let{parse:ti}=Q(),{seriesTypes:ts}=te(),to={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:ti("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===ts.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},{defined:ta,isNumber:tr,pick:tn}=c(),{relativeLength:tl}=c(),th={"navigator-handle":function(t,e,i,s,o={}){var a,r,n,l,h;let d=o.width?o.width/2:i,p=tl(o.borderRadius||0,Math.min(2*d,s));return[["M",-1.5,(s=o.height||s)/2-3.5],["L",-1.5,s/2+4.5],["M",.5,s/2-3.5],["L",.5,s/2+4.5],...(a=-d-1,r=.5,n=2*d+1,l=s,h={r:p},h?.r?function(t,e,i,s,o){let a=o?.r||0;return[["M",t+a,e],["L",t+i-a,e],["A",a,a,0,0,1,t+i,e+a],["L",t+i,e+s-a],["A",a,a,0,0,1,t+i-a,e+s],["L",t+a,e+s],["A",a,a,0,0,1,t,e+s-a],["L",t,e+a],["A",a,a,0,0,1,t+a,e],["Z"]]}(a,.5,n,l,h):[["M",a,r],["L",a+n,r],["L",a+n,r+l],["L",a,r+l],["Z"]])]}},td=i.default.RendererRegistry;var tp=d.n(td);let{defined:tc}=c(),tu={setFixedRange:function(t){let e=this.xAxis[0];tc(e.dataMax)&&tc(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}},{setOptions:tg}=c(),{composed:tm}=c(),{getRendererType:tx}=tp(),{setFixedRange:tf}=tu,{addEvent:tb,extend:tv,pushUnique:ty}=c();function tM(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}let tA=function(t,e,i){$.compose(e),ty(tm,"Navigator")&&(t.prototype.setFixedRange=tf,tv(tx().prototype.symbols,th),tb(i,"afterUpdate",tM),tg({navigator:to}))},{composed:tw}=c(),{addEvent:tk,defined:tS,pick:tO,pushUnique:tE}=c();!function(t){let e;function i(t){let e=tO(t.options?.min,t.min),i=tO(t.options?.max,t.max);return{axisMin:e,axisMax:i,scrollMin:tS(t.dataMin)?Math.min(e,t.min,t.dataMin,tO(t.threshold,1/0)):e,scrollMax:tS(t.dataMax)?Math.max(i,t.max,t.dataMax,tO(t.threshold,-1/0)):i}}function s(){let t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function o(){let t=this;t.options?.scrollbar?.enabled&&(t.options.scrollbar.vertical=!t.horiz,t.options.startOnTick=t.options.endOnTick=!1,t.scrollbar=new e(t.chart.renderer,t.options.scrollbar,t.chart),tk(t.scrollbar,"changed",function(e){let s,o,{axisMin:a,axisMax:r,scrollMin:n,scrollMax:l}=i(t),h=l-n;if(tS(a)&&tS(r)){if(t.horiz&&!t.reversed||!t.horiz&&t.reversed?(s=n+h*this.to,o=n+h*this.from):(s=n+h*(1-this.from),o=n+h*(1-this.to)),this.shouldUpdateExtremes(e.DOMType)){let i="mousemove"!==e.DOMType&&"touchmove"!==e.DOMType&&void 0;t.setExtremes(o,s,!0,i,e)}else this.setRange(this.from,this.to)}}))}function a(){let t,e,s,{scrollMin:o,scrollMax:a}=i(this),r=this.scrollbar,n=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,h=this.options.margin||0;if(r&&l){if(this.horiz)this.opposite||(l[1]+=n),r.position(this.left,this.top+this.height+2+l[1]-(this.opposite?h:0),this.width,this.height),this.opposite||(l[1]+=h),t=1;else{let e;this.opposite&&(l[0]+=n),e=r.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:h):this.opposite?0:h,r.position(e,this.top,this.width,this.height),this.opposite&&(l[0]+=h),t=0}if(l[t]+=r.size+(r.options.margin||0),isNaN(o)||isNaN(a)||!tS(this.min)||!tS(this.max)||this.dataMin===this.dataMax)r.setRange(0,1);else if(this.min===this.max){let t=this.pointRange/(this.dataMax+1);e=t*this.min,s=t*(this.max+1),r.setRange(e,s)}else e=(this.min-o)/(a-o),s=(this.max-o)/(a-o),this.horiz&&!this.reversed||!this.horiz&&this.reversed?r.setRange(e,s):r.setRange(1-s,1-e)}}t.compose=function(t,i){tE(tw,"Axis.Scrollbar")&&(e=i,tk(t,"afterGetOffset",s),tk(t,"afterInit",o),tk(t,"afterRender",a))}}(a||(a={}));let tC=a,tT={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},{defaultOptions:tB}=c(),{addEvent:tD,correctFloat:tP,crisp:tR,defined:tz,destroyObjectProperties:tI,fireEvent:tL,merge:tW,pick:tX,removeEvent:tG}=c();class tY{static compose(t){tC.compose(t,tY)}static swapXY(t,e){return e&&t.forEach(t=>{let e,i=t.length;for(let s=0;s<i;s+=2)"number"==typeof(e=t[s+1])&&(t[s+1]=t[s+2],t[s+2]=e)}),t}constructor(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}addEvents(){let t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,s=this.track.element,o=this.mouseDownHandler.bind(this),a=this.mouseMoveHandler.bind(this),r=this.mouseUpHandler.bind(this),n=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[s,"click",this.trackClick.bind(this)],[i,"mousedown",o],[i.ownerDocument,"mousemove",a],[i.ownerDocument,"mouseup",r],[i,"touchstart",o],[i.ownerDocument,"touchmove",a],[i.ownerDocument,"touchend",r]];n.forEach(function(t){tD.apply(null,t)}),this._events=n}buttonToMaxClick(t){let e=(this.to-this.from)*tX(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),tL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}buttonToMinClick(t){let e=tP(this.to-this.from)*tX(this.options.step,.2);this.updatePosition(tP(this.from-e),tP(this.to-e)),tL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}cursorToScrollbarPosition(t){let e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}}destroy(){let t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,tI(e.scrollbarButtons))}drawScrollbarButton(t){let e=this.renderer,i=this.scrollbarButtons,s=this.options,o=this.size,a=e.g().add(this.group);if(i.push(a),s.buttonsEnabled){let r=e.rect().addClass("highcharts-scrollbar-button").add(a);this.chart.styledMode||r.attr({stroke:s.buttonBorderColor,"stroke-width":s.buttonBorderWidth,fill:s.buttonBackgroundColor}),r.attr(r.crisp({x:-.5,y:-.5,width:o,height:o,r:s.buttonBorderRadius},r.strokeWidth()));let n=e.path(tY.swapXY([["M",o/2+(t?-1:1),o/2-3],["L",o/2+(t?-1:1),o/2+3],["L",o/2+(t?2:-2),o/2]],s.vertical)).addClass("highcharts-scrollbar-arrow").add(i[t]);this.chart.styledMode||n.attr({fill:s.buttonArrowColor})}}init(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=tW(tT,tB.scrollbar,e),this.options.margin=tX(this.options.margin,10),this.chart=i,this.size=tX(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())}mouseDownHandler(t){let e=this.chart.pointer?.normalize(t)||t,i=this.cursorToScrollbarPosition(e);this.chartX=i.chartX,this.chartY=i.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0}mouseMoveHandler(t){let e,i=this.chart.pointer?.normalize(t)||t,s=this.options.vertical?"chartY":"chartX",o=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][s])&&(e=this.cursorToScrollbarPosition(i)[s]-this[s],this.hasDragged=!0,this.updatePosition(o[0]+e,o[1]+e),this.hasDragged&&tL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))}mouseUpHandler(t){this.hasDragged&&tL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null}position(t,e,i,s){let{buttonsEnabled:o,margin:a=0,vertical:r}=this.options,n=this.rendered?"animate":"attr",l=s,h=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=s,this.xOffset=l,this.yOffset=h,r?(this.width=this.yOffset=i=h=this.size,this.xOffset=l=0,this.yOffset=h=o?this.size:0,this.barWidth=s-(o?2*i:0),this.x=t+=a):(this.height=s=this.size,this.xOffset=l=o?this.size:0,this.barWidth=i-(o?2*s:0),this.y=this.y+a),this.group[n]({translateX:t,translateY:this.y}),this.track[n]({width:i,height:s}),this.scrollbarButtons[1][n]({translateX:r?0:i-l,translateY:r?s-h:0})}removeEvents(){this._events.forEach(function(t){tG.apply(null,t)}),this._events.length=0}render(){let t=this.renderer,e=this.options,i=this.size,s=this.chart.styledMode,o=t.g("scrollbar").attr({zIndex:e.zIndex}).hide().add();this.group=o,this.track=t.rect().addClass("highcharts-scrollbar-track").attr({r:e.trackBorderRadius||0,height:i,width:i}).add(o),s||this.track.attr({fill:e.trackBackgroundColor,stroke:e.trackBorderColor,"stroke-width":e.trackBorderWidth});let a=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-tR(0,a),y:-tR(0,a)}),this.scrollbarGroup=t.g().add(o),this.scrollbar=t.rect().addClass("highcharts-scrollbar-thumb").attr({height:i-a,width:i-a,r:e.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=t.path(tY.swapXY([["M",-3,i/4],["L",-3,2*i/3],["M",0,i/4],["L",0,2*i/3],["M",3,i/4],["L",3,2*i/3]],e.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),s||(this.scrollbar.attr({fill:e.barBackgroundColor,stroke:e.barBorderColor,"stroke-width":e.barBorderWidth}),this.scrollbarRifles.attr({stroke:e.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-tR(0,this.scrollbarStrokeWidth),-tR(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)}setRange(t,e){let i,s,o=this.options,a=o.vertical,r=o.minWidth,n=this.barWidth,l=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(!tz(n))return;let h=n*Math.min(e,1);i=Math.ceil(n*(t=Math.max(t,0))),this.calculatedWidth=s=tP(h-i),s<r&&(i=(n-r+s)*t,s=r);let d=Math.floor(i+this.xOffset+this.yOffset),p=s/2-.5;this.from=t,this.to=e,a?(this.scrollbarGroup[l]({translateY:d}),this.scrollbar[l]({height:s}),this.scrollbarRifles[l]({translateY:p}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[l]({translateX:d}),this.scrollbar[l]({width:s}),this.scrollbarRifles[l]({translateX:p}),this.scrollbarLeft=d,this.scrollbarTop=0),s<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===o.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}shouldUpdateExtremes(t){return tX(this.options.liveRedraw,c().svg&&!c().isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!tz(t)}trackClick(t){let e=this.chart.pointer?.normalize(t)||t,i=this.to-this.from,s=this.y+this.scrollbarTop,o=this.x+this.scrollbarLeft;this.options.vertical&&e.chartY>s||!this.options.vertical&&e.chartX>o?this.updatePosition(this.from+i,this.to+i):this.updatePosition(this.from-i,this.to-i),tL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}update(t){this.destroy(),this.init(this.chart.renderer,tW(!0,this.options,t),this.chart)}updatePosition(t,e){e>1&&(t=tP(1-tP(e-t)),e=1),t<0&&(e=tP(e-t),t=0),this.from=t,this.to=e}}tY.defaultOptions=tT,tB.scrollbar=tW(!0,tY.defaultOptions,tB.scrollbar);let tU=i.default.SVGRenderer;var tN=d.n(tU);let{defaultOptions:tH}=c(),{isTouchDevice:tF}=c(),{prototype:{symbols:tV}}=tN(),{addEvent:t_,clamp:tj,correctFloat:tZ,defined:tq,destroyObjectProperties:tK,erase:t$,extend:tJ,find:tQ,fireEvent:t0,isArray:t1,isNumber:t2,merge:t3,pick:t5,removeEvent:t6,splat:t9}=c();function t4(t,...e){let i=[].filter.call(e,t2);if(i.length)return Math[t].apply(0,i)}class t8{static compose(t,e,i){N(t,t8),tA(t,e,i)}constructor(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}drawHandle(t,e,i,s){let o=this.navigatorOptions.handles.height;this.handles[e][s](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-o)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-o/2-1)})}drawOutline(t,e,i,s){let o=this.navigatorOptions.maskInside,a=this.outline.strokeWidth(),r=a/2,n=a%2/2,l=this.scrollButtonSize,h=this.size,d=this.top,p=this.height,c=d-r,u=d+p,g=this.left,m,x;i?(m=d+e+n,e=d+t+n,x=[["M",g+p,d-l-n],["L",g+p,m],["L",g,m],["M",g,e],["L",g+p,e],["L",g+p,d+h+l]],o&&x.push(["M",g+p,m-r],["L",g+p,e+r])):(g-=l,t+=g+l-n,e+=g+l-n,x=[["M",g,c],["L",t,c],["L",t,u],["M",e,u],["L",e,c],["L",g+h+2*l,c]],o&&x.push(["M",t-r,c],["L",e+r,c])),this.outline[s]({d:x})}drawMasks(t,e,i,s){let o,a,r,n,l=this.left,h=this.top,d=this.height;i?(r=[l,l,l],n=[h,h+t,h+e],a=[d,d,d],o=[t,e-t,this.size-e]):(r=[l,l+t,l+e],n=[h,h,h],a=[t,e-t,this.size-e],o=[d,d,d]),this.shades.forEach((t,e)=>{t[s]({x:r[e],y:n[e],width:a[e],height:o[e]})})}renderElements(){let t=this,e=t.navigatorOptions,i=e.maskInside,s=t.chart,o=s.inverted,a=s.renderer,r={cursor:o?"ns-resize":"ew-resize"},n=t.navigatorGroup??(t.navigatorGroup=a.g("navigator").attr({zIndex:8,visibility:"hidden"}).add());if([!i,i,!i].forEach((i,o)=>{let l=t.shades[o]??(t.shades[o]=a.rect().addClass("highcharts-navigator-mask"+(1===o?"-inside":"-outside")).add(n));s.styledMode||(l.attr({fill:i?e.maskFill:"rgba(0,0,0,0)"}),1===o&&l.css(r))}),t.outline||(t.outline=a.path().addClass("highcharts-navigator-outline").add(n)),s.styledMode||t.outline.attr({"stroke-width":e.outlineWidth,stroke:e.outlineColor}),e.handles?.enabled){let i=e.handles,{height:o,width:l}=i;[0,1].forEach(e=>{let h=i.symbols[e];if(t.handles[e]&&t.handles[e].symbolUrl===h){if(!t.handles[e].isImg&&t.handles[e].symbolName!==h){let i=tV[h].call(tV,-l/2-1,0,l,o);t.handles[e].attr({d:i}),t.handles[e].symbolName=h}}else t.handles[e]?.destroy(),t.handles[e]=a.symbol(h,-l/2-1,0,l,o,i),t.handles[e].attr({zIndex:7-e}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][e]).add(n),t.addMouseEvents();s.inverted&&t.handles[e].attr({rotation:90,rotationOriginX:Math.floor(-l/2),rotationOriginY:(o+l)/2}),s.styledMode||t.handles[e].attr({fill:i.backgroundColor,stroke:i.borderColor,"stroke-width":i.lineWidth,width:i.width,height:i.height,x:-l/2-1,y:0}).css(r)})}}update(t,e=!1){let i=this.chart,s=i.options.chart.inverted!==i.scrollbar?.options.vertical;if(t3(!0,i.options.navigator,t),this.navigatorOptions=i.options.navigator||{},this.setOpposite(),tq(t.enabled)||s)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(i);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t6(t,"updatedData",this.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t.eventsToUnbind.push(t_(t,"updatedData",this.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=t.height??this.height;let e=this.getXAxisOffsets();this.xAxis.update({...t.xAxis,offsets:e,[i.inverted?"width":"height"]:this.height,[i.inverted?"height":"width"]:void 0},!1),this.yAxis.update({...t.yAxis,[i.inverted?"width":"height"]:this.height},!1)}e&&i.redraw()}render(t,e,i,s){let o=this.chart,a=this.xAxis,r=a.pointRange||0,n=a.navigatorAxis.fake?o.xAxis[0]:a,l=this.navigatorEnabled,h=this.rendered,d=o.inverted,p=o.xAxis[0].minRange,c=o.xAxis[0].options.maxRange,u=this.scrollButtonSize,g,m,x,f=this.scrollbarHeight,b,v;if(this.hasDragged&&!tq(i))return;if(this.isDirty&&this.renderElements(),t=tZ(t-r/2),e=tZ(e+r/2),!t2(t)||!t2(e)){if(!h)return;i=0,s=t5(a.width,n.width)}this.left=t5(a.left,o.plotLeft+u+(d?o.plotWidth:0));let y=this.size=b=t5(a.len,(d?o.plotHeight:o.plotWidth)-2*u);g=d?f:b+2*u,i=t5(i,a.toPixels(t,!0)),s=t5(s,a.toPixels(e,!0)),t2(i)&&Math.abs(i)!==1/0||(i=0,s=g);let M=a.toValue(i,!0),A=a.toValue(s,!0),w=Math.abs(tZ(A-M));w<p?this.grabbedLeft?i=a.toPixels(A-p-r,!0):this.grabbedRight&&(s=a.toPixels(M+p+r,!0)):tq(c)&&tZ(w-r)>c&&(this.grabbedLeft?i=a.toPixels(A-c-r,!0):this.grabbedRight&&(s=a.toPixels(M+c+r,!0))),this.zoomedMax=tj(Math.max(i,s),0,y),this.zoomedMin=tj(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,s),0,y),this.range=this.zoomedMax-this.zoomedMin,y=Math.round(this.zoomedMax);let k=Math.round(this.zoomedMin);l&&(this.navigatorGroup.attr({visibility:"inherit"}),v=h&&!this.hasDragged?"animate":"attr",this.drawMasks(k,y,d,v),this.drawOutline(k,y,d,v),this.navigatorOptions.handles.enabled&&(this.drawHandle(k,0,d,v),this.drawHandle(y,1,d,v))),this.scrollbar&&(d?(x=this.top-u,m=this.left-f+(l||!n.opposite?0:(n.titleOffset||0)+n.axisTitleMargin),f=b+2*u):(x=this.top+(l?this.height:-f),m=this.left-u),this.scrollbar.position(m,x,g,f),this.scrollbar.setRange(this.zoomedMin/(b||1),this.zoomedMax/(b||1))),this.rendered=!0,this.isDirty=!1,t0(this,"afterRender")}addMouseEvents(){let t=this,e=t.chart,i=e.container,s=[],o,a;t.mouseMoveHandler=o=function(e){t.onMouseMove(e)},t.mouseUpHandler=a=function(e){t.onMouseUp(e)},(s=t.getPartsEvents("mousedown")).push(t_(e.renderTo,"mousemove",o),t_(i.ownerDocument,"mouseup",a),t_(e.renderTo,"touchmove",o),t_(i.ownerDocument,"touchend",a)),s.concat(t.getPartsEvents("touchstart")),t.eventsToUnbind=s,t.series&&t.series[0]&&s.push(t_(t.series[0].xAxis,"foundExtremes",function(){e.navigator.modifyNavigatorAxisExtremes()}))}getPartsEvents(t){let e=this,i=[];return["shades","handles"].forEach(function(s){e[s].forEach(function(o,a){i.push(t_(o.element,t,function(t){e[s+"Mousedown"](t,a)}))})}),i}shadesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=this.xAxis,o=this.zoomedMin,a=this.size,r=this.range,n=this.left,l=t.chartX,h,d,p,c;i.inverted&&(l=t.chartY,n=this.top),1===e?(this.grabbedCenter=l,this.fixedWidth=r,this.dragOffset=l-o):(c=l-n-r/2,0===e?c=Math.max(0,c):2===e&&c+r>=a&&(c=a-r,this.reversedExtremes?(c-=r,d=this.getUnionExtremes().dataMin):h=this.getUnionExtremes().dataMax),c!==o&&(this.fixedWidth=r,tq((p=s.navigatorAxis.toFixedRange(c,c+r,d,h)).min)&&t0(this,"setRange",{min:Math.min(p.min,p.max),max:Math.max(p.min,p.max),redraw:!0,eventArguments:{trigger:"navigator"}})))}handlesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=i.xAxis[0],o=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=o?s.min:s.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=o?s.max:s.min),i.setFixedRange(void 0)}onMouseMove(t){let e=this,i=e.chart,s=e.navigatorSize,o=e.range,a=e.dragOffset,r=i.inverted,n=e.left,l;(!t.touches||0!==t.touches[0].pageX)&&(l=(t=i.pointer?.normalize(t)||t).chartX,r&&(n=e.top,l=t.chartY),e.grabbedLeft?(e.hasDragged=!0,e.render(0,0,l-n,e.otherHandlePos)):e.grabbedRight?(e.hasDragged=!0,e.render(0,0,e.otherHandlePos,l-n)):e.grabbedCenter&&(e.hasDragged=!0,l<a?l=a:l>s+a-o&&(l=s+a-o),e.render(0,0,l-a,l-a+o)),e.hasDragged&&e.scrollbar&&t5(e.scrollbar.options.liveRedraw,!tF&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){e.onMouseUp(t)},0)))}onMouseUp(t){let e,i,s,o,a,r,n=this.chart,l=this.xAxis,h=this.scrollbar,d=t.DOMEvent||t,p=n.inverted,c=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!h||!h.hasDragged)||"scrollbar"===t.trigger)&&(s=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?o=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(a=this.fixedExtreme),this.zoomedMax===this.size&&(a=this.reversedExtremes?s.dataMin:s.dataMax),0===this.zoomedMin&&(o=this.reversedExtremes?s.dataMax:s.dataMin),tq((r=l.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,o,a)).min)&&t0(this,"setRange",{min:Math.min(r.min,r.max),max:Math.max(r.min,r.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&t2(this.zoomedMin)&&t2(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,p,c),this.outline&&this.drawOutline(i,e,p,c),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,p,c),this.drawHandle(e,1,p,c)))}removeEvents(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()}removeBaseSeriesEvents(){let t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){t6(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&t6(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))}getXAxisOffsets(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]}init(t){let e=t.options,i=e.navigator||{},s=i.enabled,o=e.scrollbar||{},a=o.enabled,r=s&&i.height||0,n=a&&o.height||0,l=o.buttonsEnabled&&n||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=r,this.scrollbarHeight=n,this.scrollButtonSize=l,this.scrollbarEnabled=a,this.navigatorEnabled=s,this.navigatorOptions=i,this.scrollbarOptions=o,this.setOpposite();let h=this,d=h.baseSeries,p=t.xAxis.length,c=t.yAxis.length,u=d&&d[0]&&d[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,h.navigatorEnabled){let e=this.getXAxisOffsets();h.xAxis=new(g())(t,t3({breaks:u.options.breaks,ordinal:u.options.ordinal,overscroll:u.options.overscroll},i.xAxis,{type:"datetime",yAxis:i.yAxis?.id,index:p,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:u.options.ordinal?0:u.options.minPadding,maxPadding:u.options.ordinal?0:u.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:e,width:r}:{offsets:e,height:r}),"xAxis"),h.yAxis=new(g())(t,t3(i.yAxis,{alignTicks:!1,offset:0,index:c,isInternal:!0,reversed:t5(i.yAxis&&i.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:r}:{height:r}),"yAxis"),d||i.series.data?h.updateNavigatorSeries(!1):0===t.series.length&&(h.unbindRedraw=t_(t,"beforeRedraw",function(){t.series.length>0&&!h.series&&(h.setBaseSeries(),h.unbindRedraw())})),h.reversedExtremes=t.inverted&&!h.xAxis.reversed||!t.inverted&&h.xAxis.reversed,h.renderElements(),h.addMouseEvents()}else h.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){let s=t.xAxis[0],o=s.getExtremes(),a=s.len-2*l,r=t4("min",s.options.min,o.dataMin),n=t4("max",s.options.max,o.dataMax)-r;return i?e*n/a+r:a*(e-r)/n},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},h.xAxis.navigatorAxis.axis=h.xAxis,h.xAxis.navigatorAxis.toFixedRange=$.prototype.toFixedRange.bind(h.xAxis.navigatorAxis);if(t.options.scrollbar?.enabled){let e=t3(t.options.scrollbar,{vertical:t.inverted});t2(e.margin)||(e.margin=t.inverted?-3:3),t.scrollbar=h.scrollbar=new tY(t.renderer,e,t),t_(h.scrollbar,"changed",function(t){let e=h.size,i=e*this.to,s=e*this.from;h.hasDragged=h.scrollbar.hasDragged,h.render(0,0,s,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){h.onMouseUp(t)})})}h.addBaseSeriesEvents(),h.addChartEvents()}setOpposite(){let t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=t5(t.opposite,!!(!e&&i.inverted))}getUnionExtremes(t){let e,i=this.chart.xAxis[0],s=this.chart.time,o=this.xAxis,a=o.options,r=i.options;return t&&null===i.dataMin||(e={dataMin:t5(s.parse(a?.min),t4("min",s.parse(r.min),i.dataMin,o.dataMin,o.min)),dataMax:t5(s.parse(a?.max),t4("max",s.parse(r.max),i.dataMax,o.dataMax,o.max))}),e}setBaseSeries(t,e){let i=this.chart,s=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?tQ(i.series,t=>!t.options.isInternal).index:0),(i.series||[]).forEach((e,i)=>{!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&s.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)}updateNavigatorSeries(t,e){let i=this,s=i.chart,o=i.baseSeries,a={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:this.navigatorOptions.xAxis?.id,yAxis:this.navigatorOptions.yAxis?.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},r=i.series=(i.series||[]).filter(t=>{let e=t.baseSeries;return!(0>o.indexOf(e))||(e&&(t6(e,"updatedData",i.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),n,l,h=i.navigatorOptions.series,d;o&&o.length&&o.forEach(t=>{let p=t.navigatorSeries,c=tJ({color:t.color,visible:t.visible},t1(h)?tH.navigator.series:h);if(p&&!1===i.navigatorOptions.adaptToUpdatedData)return;a.name="Navigator "+o.length,d=(n=t.options||{}).navigatorOptions||{},c.dataLabels=t9(c.dataLabels),(l=t3(n,a,c,d)).pointRange=t5(c.pointRange,d.pointRange,tH.plotOptions[l.type||"line"].pointRange);let u=d.data||c.data;i.hasNavigatorData=i.hasNavigatorData||!!u,l.data=u||n.data?.slice(0),p&&p.options?p.update(l,e):(t.navigatorSeries=s.initSeries(l),s.setSortedData(),t.navigatorSeries.baseSeries=t,r.push(t.navigatorSeries))}),(h.data&&!(o&&o.length)||t1(h))&&(i.hasNavigatorData=!1,(h=t9(h)).forEach((t,e)=>{a.name="Navigator "+(r.length+1),(l=t3(tH.navigator.series,{color:s.series[e]&&!s.series[e].options.isInternal&&s.series[e].color||s.options.colors[e]||s.options.colors[0]},a,t)).data=t.data,l.data&&(i.hasNavigatorData=!0,r.push(s.initSeries(l)))})),t&&this.addBaseSeriesEvents()}addBaseSeriesEvents(){let t=this,e=t.baseSeries||[];e[0]&&e[0].xAxis&&e[0].eventsToUnbind.push(t_(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),e.forEach(i=>{i.eventsToUnbind.push(t_(i,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),i.eventsToUnbind.push(t_(i,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==this.navigatorOptions.adaptToUpdatedData&&i.xAxis&&i.eventsToUnbind.push(t_(i,"updatedData",this.updatedDataHandler)),i.eventsToUnbind.push(t_(i,"remove",function(){e&&t$(e,i),this.navigatorSeries&&t.series&&(t$(t.series,this.navigatorSeries),tq(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})}getBaseSeriesMin(t){return this.baseSeries.reduce(function(t,e){return Math.min(t,e.getColumn("x")[0]??t)},t)}modifyNavigatorAxisExtremes(){let t=this.xAxis;if(void 0!==t.getExtremes){let e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}}modifyBaseAxisExtremes(){let t,e,i=this.chart.navigator,s=this.getExtremes(),o=s.min,a=s.max,r=s.dataMin,n=s.dataMax,l=a-o,h=i.stickToMin,d=i.stickToMax,p=t5(this.ordinal?.convertOverscroll(this.options.overscroll),0),c=i.series&&i.series[0],u=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(h&&(t=(e=r)+l),d&&(t=n+p,h||(e=Math.max(r,t-l,i.getBaseSeriesMin(c&&c.xData?c.xData[0]:-Number.MAX_VALUE)))),u&&(h||d)&&t2(e)&&(this.min=this.userMin=e,this.max=this.userMax=t)),i.stickToMin=i.stickToMax=null}updatedDataHandler(){let t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=t5(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))}shouldStickToMin(t,e){let i=e.getBaseSeriesMin(t.getColumn("x")[0]),s=t.xAxis,o=s.max,a=s.min,r=s.options.range,n=!0;return!!(t2(o)&&t2(a))&&(r&&o-i>0?o-i<r:a<=i)}addChartEvents(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(t_(this.chart,"redraw",function(){let t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),t_(this.chart,"getMargins",function(){let t=this.navigator,e=t.opposite?"plotTop":"marginBottom";this.inverted&&(e=t.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(t.navigatorEnabled||!this.inverted?t.height+(this.scrollbar?.options.margin||0)+t.scrollbarHeight:0)+(t.navigatorOptions.margin||0)}),t_(t8,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))}destroy(){this.removeEvents(),this.xAxis&&(t$(this.chart.xAxis,this.xAxis),t$(this.chart.axes,this.xAxis)),this.yAxis&&(t$(this.chart.yAxis,this.yAxis),t$(this.chart.axes,this.yAxis)),(this.series||[]).forEach(t=>{t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(t=>{this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null}),[this.handles].forEach(t=>{tK(t)}),this.baseSeries.forEach(t=>{t.navigatorSeries=void 0}),this.navigatorEnabled=!1}}(s=r||(r={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},s.splice=function(t,e,i,s,o=[]){if(Array.isArray(t))return Array.isArray(o)||(o=Array.from(o)),{removed:t.splice(e,i,...o),array:t};let a=Object.getPrototypeOf(t).constructor,r=t[s?"subarray":"slice"](e,e+i),n=new a(t.length-i+o.length);return n.set(t.subarray(0,e),0),n.set(o,e),n.set(t.subarray(e+i),e+o.length),{removed:r,array:n}};let{setLength:t7,splice:et}=r,{fireEvent:ee,objectEach:ei,uniqueKey:es}=c(),eo=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||es(),this.modified=this,this.rowCount=0,this.versionTag=es();let e=0;ei(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,ei(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=t7(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;ei(this.columns,(s,o)=>{this.columns[o]=et(s,t,e).array,i=s.length}),this.rowCount=i}ee(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=es()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,s){this.setColumns({[t]:e},i,s)}setColumns(t,e,i){let s=this.rowCount;ei(t,(t,e)=>{this.columns[e]=t.slice(),s=t.length}),this.applyRowCount(s),i?.silent||(ee(this,"afterSetColumns"),this.versionTag=es())}setRow(t,e=this.rowCount,i,s){let{columns:o}=this,a=i?this.rowCount+1:e+1;ei(t,(t,r)=>{let n=o[r]||s?.addColumns!==!1&&Array(a);n&&(i?n=et(n,e,0,!0,[t]).array:n[e]=t,o[r]=n)}),a>this.rowCount&&this.applyRowCount(a),s?.silent||(ee(this,"afterSetRows"),this.versionTag=es())}},{addEvent:ea,correctFloat:er,css:en,defined:el,error:eh,isNumber:ed,pick:ep,timeUnits:ec,isString:eu}=c();!function(t){function e(t,i,s,o,a=[],r=0,n){let l={},h=this.options.tickPixelInterval,d=this.chart.time,p=[],c,u,g,m,x,f=0,b=[],v=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!a||a.length<3||void 0===i)return d.getTimeTicks.apply(d,arguments);let y=a.length;for(c=0;c<y;c++){if(x=c&&a[c-1]>s,a[c]<i&&(f=c),c===y-1||a[c+1]-a[c]>5*r||x){if(a[c]>v){for(u=d.getTimeTicks(t,a[f],a[c],o);u.length&&u[0]<=v;)u.shift();u.length&&(v=u[u.length-1]),p.push(b.length),b=b.concat(u)}f=c+1}if(x)break}if(u){if(m=u.info,n&&m.unitRange<=ec.hour){for(f=1,c=b.length-1;f<c;f++)d.dateFormat("%d",b[f])!==d.dateFormat("%d",b[f-1])&&(l[b[f]]="day",g=!0);g&&(l[b[0]]="day"),m.higherRanks=l}m.segmentStarts=p,b.info=m}else eh(12,!1,this.chart);if(n&&el(h)){let t=b.length,e=[],i=[],o,a,r,n,d,p=t;for(;p--;)a=this.translate(b[p]),r&&(i[p]=r-a),e[p]=r=a;for(i.sort((t,e)=>t-e),(n=i[Math.floor(i.length/2)])<.6*h&&(n=null),p=b[t-1]>s?t-1:t,r=void 0;p--;)d=Math.abs(r-(a=e[p])),r&&d<.8*h&&(null===n||d<.8*n)?(l[b[p]]&&!l[b[p+1]]?(o=p+1,r=a):o=p,b.splice(o,1)):r=a}return b}function i(t){let e=this.ordinal.positions;if(!e)return t;let i=e.length-1,s;return(t<0?t=e[0]:t>i?t=e[i]:(i=Math.floor(t),s=t-i),void 0!==s&&void 0!==e[i])?e[i]+(s?s*(e[i+1]-e[i]):0):t}function s(t){let e=this.ordinal,i=this.old?this.old.min:this.min,s=this.old?this.old.transA:this.transA,o=e.getExtendedPositions();if(o?.length){let a=er((t-i)*s+this.minPixelPadding),r=er(e.getIndexOfPoint(a,o)),n=er(r%1);if(r>=0&&r<=o.length-1){let t=o[Math.floor(r)],e=o[Math.ceil(r)];return o[Math.floor(r)]+n*(e-t)}}return t}function o(e,i){let s=t.Additions.findIndexOf(e,i,!0);if(e[s]===i)return s;let o=(i-e[s])/(e[s+1]-e[s]);return s+o}function a(){this.ordinal||(this.ordinal=new t.Additions(this))}function r(){let{eventArgs:t,options:e}=this;if(this.isXAxis&&el(e.overscroll)&&0!==e.overscroll&&ed(this.max)&&ed(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&(t?.trigger!=="pan"||this.isInternal)&&t?.trigger!=="navigator")){let i=this.ordinal.convertOverscroll(e.overscroll);this.max+=i,!this.isInternal&&el(this.userMin)&&t?.trigger!=="mousewheel"&&(this.min+=i)}}function n(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function l(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function h(t){let e=this.xAxis[0],i=e.ordinal.convertOverscroll(e.options.overscroll),s=t.originalEvent.chartX,o=this.options.chart.panning,a=!1;if(o&&"y"!==o.type&&e.options.ordinal&&e.series.length&&(!t.touches||t.touches.length<=1)){let t,o,r=this.mouseDownX,n=e.getExtremes(),l=n.dataMin,h=n.dataMax,d=n.min,p=n.max,c=this.hoverPoints,u=e.closestPointRange||e.ordinal?.overscrollPointsRange,g=Math.round((r-s)/(e.translationSlope*(e.ordinal.slope||u))),m=e.ordinal.getExtendedPositions(),x={ordinal:{positions:m,extendedOrdinalPositions:m}},f=e.index2val,b=e.val2lin;if(d<=l&&g<0||p+i>=h&&g>0)return;x.ordinal.positions?Math.abs(g)>1&&(c&&c.forEach(function(t){t.setState()}),h>(o=x.ordinal.positions)[o.length-1]&&o.push(h),this.setFixedRange(p-d),(t=e.navigatorAxis.toFixedRange(void 0,void 0,f.apply(x,[b.apply(x,[d,!0])+g]),f.apply(x,[b.apply(x,[p,!0])+g]))).min>=Math.min(o[0],d)&&t.max<=Math.max(o[o.length-1],p)+i&&e.setExtremes(t.min,t.max,!0,!1,{trigger:"pan"}),this.mouseDownX=s,en(this.container,{cursor:"move"})):a=!0}else a=!0;a||o&&/y/.test(o.type)?i&&(e.max=e.dataMax+i):t.preventDefault()}function d(){let t=this.xAxis;t?.options.ordinal&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function p(t,e){let i,s=this.ordinal,a=s.positions,r=s.slope,n;if(!a)return t;let l=a.length;if(a[0]<=t&&a[l-1]>=t)i=o(a,t);else{if(n=s.getExtendedPositions?.(),!n?.length)return t;let l=n.length;r||(r=(n[l-1]-n[0])/l);let h=o(n,a[0]);if(t>=n[0]&&t<=n[l-1])i=o(n,t)-h;else{if(!e)return t;i=t<n[0]?-h-(n[0]-t)/r:(t-n[l-1])/r+l-h}}return e?i:r*(i||0)+s.offset}t.compose=function(t,o,c){let u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=e,u.index2val=i,u.lin2val=s,u.val2lin=p,u.ordinal2lin=u.val2lin,ea(t,"afterInit",a),ea(t,"foundExtremes",r),ea(t,"afterSetScale",n),ea(t,"initialAxisTranslation",l),ea(c,"pan",h),ea(c,"touchpan",h),ea(o,"updatedData",d)),t},t.Additions=class{constructor(t){this.index={},this.axis=t}beforeSetTickPositions(){let t=this.axis,e=t.ordinal,i=t.getExtremes(),s=i.min,o=i.max,a=t.brokenAxis?.hasBreaks,r=t.options.ordinal,n,l,h,d,p,c,u,g=[],m=Number.MAX_VALUE,x=!1,f=!1,b=!1;if(r||a){let i=0;if(t.series.forEach(function(t,e){let s=t.getColumn("x",!0);if(l=[],e>0&&"highcharts-navigator-series"!==t.options.id&&s.length>1&&(f=i!==s[1]-s[0]),i=s[1]-s[0],t.boosted&&(b=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||a)&&(n=(g=g.concat(s)).length,g.sort(function(t,e){return t-e}),m=Math.min(m,ep(t.closestPointRange,m)),n)){for(e=0;e<n-1;)g[e]!==g[e+1]&&l.push(g[e+1]),e++;l[0]!==g[0]&&l.unshift(g[0]),g=l}}),t.ordinal.originalOrdinalRange||(t.ordinal.originalOrdinalRange=(g.length-1)*m),f&&b&&(g.pop(),g.shift()),(n=g.length)>2){for(h=g[1]-g[0],u=n-1;u--&&!x;)g[u+1]-g[u]!==h&&(x=!0);!t.options.keepOrdinalPadding&&(g[0]-s>h||o-g[g.length-1]>h)&&(x=!0)}else t.options.overscroll&&(2===n?m=g[1]-g[0]:1===n?(m=t.ordinal.convertOverscroll(t.options.overscroll),g=[g[0],g[0]+m]):m=e.overscrollPointsRange);x||t.forceOrdinal?(t.options.overscroll&&(e.overscrollPointsRange=m,g=g.concat(e.getOverscrollPositions())),e.positions=g,d=t.ordinal2lin(Math.max(s,g[0]),!0),p=Math.max(t.ordinal2lin(Math.min(o,g[g.length-1]),!0),1),e.slope=c=(o-s)/(p-d),e.offset=s-d*c):(e.overscrollPointsRange=ep(t.closestPointRange,e.overscrollPointsRange),e.positions=t.ordinal.slope=e.offset=void 0)}t.isOrdinal=r&&x,e.groupIntervalFactor=null}static findIndexOf(t,e,i){let s=0,o=t.length-1,a;for(;s<o;)t[a=Math.ceil((s+o)/2)]<=e?s=a:o=a-1;return t[s]===e?s:i?s:-1}getExtendedPositions(t=!0){let e=this,i=e.axis,s=i.constructor.prototype,o=i.chart,a=i.series.reduce((t,e)=>{let i=e.currentDataGrouping;return t+(i?i.count+i.unitName:"raw")},""),r=t?i.ordinal.convertOverscroll(i.options.overscroll):0,n=i.getExtremes(),l,h,d=e.index;return d||(d=e.index={}),!d[a]&&((l={series:[],chart:o,forceOrdinal:!1,getExtremes:function(){return{min:n.dataMin,max:n.dataMax+r}},applyGrouping:s.applyGrouping,getGroupPixelWidth:s.getGroupPixelWidth,getTimeTicks:s.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:s.ordinal2lin,getIndexOfPoint:s.getIndexOfPoint,val2lin:s.val2lin}).ordinal.axis=l,i.series.forEach(i=>{h={xAxis:l,chart:o,groupPixelWidth:i.groupPixelWidth,destroyGroupedData:c().noop,getColumn:i.getColumn,applyGrouping:i.applyGrouping,getProcessedData:i.getProcessedData,reserveSpace:i.reserveSpace,visible:i.visible};let s=i.getColumn("x").concat(t?e.getOverscrollPositions():[]);h.dataTable=new eo({columns:{x:s}}),h.options={...i.options,dataGrouping:i.currentDataGrouping?{firstAnchor:i.options.dataGrouping?.firstAnchor,anchor:i.options.dataGrouping?.anchor,lastAnchor:i.options.dataGrouping?.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[i.currentDataGrouping.unitName,[i.currentDataGrouping.count]]]}:{enabled:!1}},l.series.push(h),i.processData.apply(h)}),l.applyGrouping({hasExtremesChanged:!0}),h?.closestPointRange!==h?.basePointRange&&h.currentDataGrouping&&(l.forceOrdinal=!0),i.ordinal.beforeSetTickPositions.apply({axis:l}),!i.ordinal.originalOrdinalRange&&l.ordinal.originalOrdinalRange&&(i.ordinal.originalOrdinalRange=l.ordinal.originalOrdinalRange),l.ordinal.positions&&(d[a]=l.ordinal.positions)),d[a]}getGroupIntervalFactor(t,e,i){let s=i.getColumn("x",!0),o=s.length,a=[],r,n,l=this.groupIntervalFactor;if(!l){for(n=0;n<o-1;n++)a[n]=s[n+1]-s[n];a.sort(function(t,e){return t-e}),r=a[Math.floor(o/2)],t=Math.max(t,s[0]),e=Math.min(e,s[o-1]),this.groupIntervalFactor=l=o*r/(e-t)}return l}getIndexOfPoint(t,e){let i=this.axis,s=i.min,a=i.minPixelPadding;return o(e,s)+er((t-a)/(i.translationSlope*(this.slope||i.closestPointRange||this.overscrollPointsRange)))}getOverscrollPositions(){let t=this.axis,e=this.convertOverscroll(t.options.overscroll),i=this.overscrollPointsRange,s=[],o=t.dataMax;if(el(i))for(;o<t.dataMax+e;)s.push(o+=i);return s}postProcessTickInterval(t){let e,i=this.axis,s=this.slope,o=i.closestPointRange;return s&&o?i.options.breaks?o||t:t/(s/o):t}convertOverscroll(t=0){let e=this,i=e.axis,s=function(t){return ep(e.originalOrdinalRange,el(i.dataMax)&&el(i.dataMin)?i.dataMax-i.dataMin:0)*t};if(eu(t)){let e,o=parseInt(t,10);if(el(i.min)&&el(i.max)&&el(i.dataMin)&&el(i.dataMax)&&!(e=i.max-i.min==i.dataMax-i.dataMin)&&(this.originalOrdinalRange=i.max-i.min),/%$/.test(t))return s(o/100);if(/px/.test(t)){let t=Math.min(o,.9*i.len)/i.len;return s(t/(e?1-t:1))}return 0}return t}}}(n||(n={}));let eg=n,em={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},{defaultOptions:ex}=c(),{composed:ef}=c(),{addEvent:eb,defined:ev,extend:ey,isNumber:eM,merge:eA,pick:ew,pushUnique:ek}=c(),eS=[];function eO(){let t,e,i=this.range,s=i.type,o=this.max,a=this.chart.time,r=function(t,e){let i=a.toParts(t),o=i.slice();"year"===s?o[0]+=e:o[1]+=e;let r=a.makeTime.apply(a,o),n=a.toParts(r);return"month"===s&&i[1]===n[1]&&1===Math.abs(e)&&(o[0]=i[0],o[1]=i[1],o[2]=0),(r=a.makeTime.apply(a,o))-t};eM(i)?(t=o-i,e=i):i&&(t=o+r(o,-(i.count||1)),this.chart&&this.chart.setFixedRange(o-t));let n=ew(this.dataMin,Number.MIN_VALUE);return eM(t)||(t=n),t<=n&&(t=n,void 0===e&&(e=r(t,i.count)),this.newMax=Math.min(t+e,ew(this.dataMax,Number.MAX_VALUE))),eM(o)?!eM(i)&&i&&i._offsetMin&&(t+=i._offsetMin):t=void 0,t}function eE(){this.rangeSelector?.redrawElements()}function eC(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new e(this))}function eT(){let t=this.rangeSelector;if(t){eM(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);let e=t.options.verticalAlign;t.options.floating||("bottom"===e?this.extraBottomMargin=!0:"top"!==e||(this.extraTopMargin=!0))}}function eB(){let t,e=this.rangeSelector;if(!e)return;let i=this.xAxis[0].getExtremes(),s=this.legend,o=e&&e.options.verticalAlign;eM(i.min)&&e.render(i.min,i.max),s.display&&"top"===o&&o===s.options.verticalAlign&&(t=eA(this.spacingBox),"vertical"===s.options.layout?t.y=this.plotTop:t.y+=e.getHeight(),s.group.placed=!1,s.align(t))}function eD(){for(let t=0,e=eS.length;t<e;++t){let e=eS[t];if(e[0]===this){e[1].forEach(t=>t()),eS.splice(t,1);return}}}function eP(){let t=this.rangeSelector;if(t?.options?.enabled){let e=t.getHeight(),i=t.options.verticalAlign;t.options.floating||("bottom"===i?this.marginBottom+=e:"middle"===i||(this.plotTop+=e))}}function eR(t){let i=t.options.rangeSelector,s=this.extraBottomMargin,o=this.extraTopMargin,a=this.rangeSelector;if(i&&i.enabled&&!ev(a)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=a=new e(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,a){let t=i&&i.verticalAlign||a.options&&a.options.verticalAlign;a.options.floating||("bottom"===t?this.extraBottomMargin=!0:"middle"===t||(this.extraTopMargin=!0)),(this.extraBottomMargin!==s||this.extraTopMargin!==o)&&(this.isDirtyBox=!0)}}let ez=function(t,i,s){if(e=s,ek(ef,"RangeSelector")){let e=i.prototype;t.prototype.minFromRange=eO,eb(i,"afterGetContainer",eC),eb(i,"beforeRender",eT),eb(i,"destroy",eD),eb(i,"getMargins",eP),eb(i,"redraw",eB),eb(i,"update",eR),eb(i,"beforeRedraw",eE),e.callbacks.push(eB),ey(ex,{rangeSelector:em.rangeSelector}),ey(ex.lang,em.lang)}},eI=i.default.SVGElement;var eL=d.n(eI);let eW=i.default.Templating;var eX=d.n(eW);let{defaultOptions:eG}=c(),{format:eY}=eX(),{addEvent:eU,createElement:eN,css:eH,defined:eF,destroyObjectProperties:eV,diffObjects:e_,discardElement:ej,extend:eZ,fireEvent:eq,isNumber:eK,isString:e$,merge:eJ,objectEach:eQ,pick:e0,splat:e1}=c();function e2(t){let e=e=>RegExp(`%[[a-zA-Z]*${e}`).test(t);if(e$(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";let i=e$(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(e):t.dateStyle||t.day||t.month||t.year,s=e$(t)?["H","k","I","l","M","S"].some(e):t.timeStyle||t.hour||t.minute||t.second;return i&&s?"datetime-local":i?"date":s?"time":"text"}class e3{static compose(t,e){ez(t,e,e3)}constructor(t){this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=()=>{let t=0;return this.buttons.forEach(e=>{let i=e.getBBox();i.width>t&&(t=i.width)}),t},this.init(t)}clickButton(t,e){let i=this.chart,s=this.buttonOptions[t],o=i.xAxis[0],a=i.scroller&&i.scroller.getUnionExtremes()||o||{},r=s.type,n=s.dataGrouping,l=a.dataMin,h=a.dataMax,d,p=eK(o?.max)?Math.round(Math.min(o.max,h??o.max)):void 0,c,u=s._range,m,x,f,b=!0;if(null!==l&&null!==h){if(this.setSelected(t),n&&(this.forcedDataGrouping=!0,g().prototype.setDataGrouping.call(o||{chart:this.chart},n,!1),this.frozenStates=s.preserveDataGrouping),"month"===r||"year"===r)o?(x={range:s,max:p,chart:i,dataMin:l,dataMax:h},d=o.minFromRange.call(x),eK(x.newMax)&&(p=x.newMax),b=!1):u=s;else if(u)eK(p)&&(p=Math.min((d=Math.max(p-u,l))+u,h),b=!1);else if("ytd"===r){if(o)!o.hasData()||eK(h)&&eK(l)||(l=Number.MAX_VALUE,h=-Number.MAX_VALUE,i.series.forEach(t=>{let e=t.getColumn("x");e.length&&(l=Math.min(e[0],l),h=Math.max(e[e.length-1],h))}),e=!1),eK(h)&&eK(l)&&(d=m=(f=this.getYTDExtremes(h,l)).min,p=f.max);else{this.deferredYTDClick=t;return}}else"all"===r&&o&&(i.navigator&&i.navigator.baseSeries[0]&&(i.navigator.baseSeries[0].xAxis.options.range=void 0),d=l,p=h);if(b&&s._offsetMin&&eF(d)&&(d+=s._offsetMin),s._offsetMax&&eF(p)&&(p+=s._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),o)eK(d)&&eK(p)&&(o.setExtremes(d,p,e0(e,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:s}),i.setFixedRange(s._range));else{c=e1(i.options.xAxis||{})[0];let t=eU(i,"afterCreateAxes",function(){let t=i.xAxis[0];t.range=t.options.range=u,t.min=t.options.min=m});eU(i,"load",function(){let e=i.xAxis[0];i.setFixedRange(s._range),e.options.range=c.range,e.options.min=c.min,t()})}eq(this,"afterBtnClick")}}setSelected(t){this.selected=this.options.selected=t}init(t){let e=this,i=t.options.rangeSelector,s=t.options.lang,o=i.buttons,a=i.selected,r=function(){let t=e.minInput,i=e.maxInput;t&&t.blur&&eq(t,"blur"),i&&i.blur&&eq(i,"blur")};e.chart=t,e.options=i,e.buttons=[],e.buttonOptions=o.map(t=>(t.type&&s.rangeSelector&&(t.text??(t.text=s.rangeSelector[`${t.type}Text`]),t.title??(t.title=s.rangeSelector[`${t.type}Title`])),t.text=eY(t.text,{count:t.count||1}),t.title=eY(t.title,{count:t.count||1}),t)),this.eventsToUnbind=[],this.eventsToUnbind.push(eU(t.container,"mousedown",r)),this.eventsToUnbind.push(eU(t,"resize",r)),o.forEach(e.computeButtonRange),void 0!==a&&o[a]&&this.clickButton(a,!1),this.eventsToUnbind.push(eU(t,"load",function(){t.xAxis&&t.xAxis[0]&&eU(t.xAxis[0],"setExtremes",function(i){eK(this.max)&&eK(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()}updateButtonStates(){let t=this,e=this.chart,i=this.dropdown,s=this.dropdownLabel,o=e.xAxis[0],a=Math.round(o.max-o.min),r=!o.hasVisibleSeries,n=24*36e5,l=e.scroller&&e.scroller.getUnionExtremes()||o,h=l.dataMin,d=l.dataMax,p=t.getYTDExtremes(d,h),c=p.min,u=p.max,g=t.selected,m=t.options.allButtonsEnabled,x=Array(t.buttonOptions.length).fill(0),f=eK(g),b=t.buttons,v=!1,y=null;t.buttonOptions.forEach((e,i)=>{let s=e._range,l=e.type,p=e.count||1,b=e._offsetMax-e._offsetMin,M=i===g,A=s>d-h,w=s<o.minRange,k=!1,S=s===a;if(M&&A&&(v=!0),o.isOrdinal&&o.ordinal?.positions&&s&&a<s){let t=o.ordinal.positions,e=eg.Additions.findIndexOf(t,o.min,!0),i=Math.min(eg.Additions.findIndexOf(t,o.max,!0)+1,t.length-1);t[i]-t[e]>s&&(S=!0)}else("month"===l||"year"===l)&&a+36e5>=({month:28,year:365})[l]*n*p-b&&a-36e5<=({month:31,year:366})[l]*n*p+b?S=!0:"ytd"===l?(S=u-c+b===a,k=!M):"all"===l&&(S=o.max-o.min>=d-h);let O=!m&&!(v&&"all"===l)&&(A||w||r),E=v&&"all"===l||!k&&S||M&&t.frozenStates;O?x[i]=3:E&&(!f||i===g)&&(y=i)}),null!==y?(x[y]=2,t.setSelected(y),this.dropdown&&(this.dropdown.selectedIndex=y+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),s&&(s.setState(0),s.attr({text:(eG.lang.rangeSelectorZoom||"")+" ▾"})));for(let e=0;e<x.length;e++){let o=x[e],a=b[e];if(a.state!==o&&(a.setState(o),i)){i.options[e+1].disabled=3===o,2===o&&(s&&(s.setState(2),s.attr({text:t.buttonOptions[e].text+" ▾"})),i.selectedIndex=e+1);let a=s.getBBox();eH(i,{width:`${a.width}px`,height:`${a.height}px`})}}}computeButtonRange(t){let e=t.type,i=t.count||1,s={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};s[e]?t._range=s[e]*i:("month"===e||"year"===e)&&(t._range=24*({month:30,year:365})[e]*36e5*i),t._offsetMin=e0(t.offsetMin,0),t._offsetMax=e0(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin}getInputValue(t){let e="min"===t?this.minInput:this.maxInput,i=this.chart.options.rangeSelector,s=this.chart.time;return e?("text"===e.type&&i.inputDateParser||this.defaultInputDateParser)(e.value,"UTC"===s.timezone,s):0}setInputValue(t,e){let i=this.options,s=this.chart.time,o="min"===t?this.minInput:this.maxInput,a="min"===t?this.minDateBox:this.maxDateBox;if(o){o.setAttribute("type",e2(i.inputDateFormat||"%e %b %Y"));let t=o.getAttribute("data-hc-time"),r=eF(t)?Number(t):void 0;if(eF(e)){let t=r;eF(t)&&o.setAttribute("data-hc-time-previous",t),o.setAttribute("data-hc-time",e),r=e}o.value=s.dateFormat(this.inputTypeFormats[o.type]||i.inputEditDateFormat,r),a&&a.attr({text:s.dateFormat(i.inputDateFormat,r)})}}setInputExtremes(t,e,i){let s="min"===t?this.minInput:this.maxInput;if(s){let t=this.inputTypeFormats[s.type],o=this.chart.time;if(t){let a=o.dateFormat(t,e);s.min!==a&&(s.min=a);let r=o.dateFormat(t,i);s.max!==r&&(s.max=r)}}}showInput(t){let e="min"===t?this.minDateBox:this.maxDateBox,i="min"===t?this.minInput:this.maxInput;if(i&&e&&this.inputGroup){let t="text"===i.type,{translateX:s=0,translateY:o=0}=this.inputGroup,{x:a=0,width:r=0,height:n=0}=e,{inputBoxWidth:l}=this.options;eH(i,{width:t?r+(l?-2:20)+"px":"auto",height:n-2+"px",border:"2px solid silver"}),t&&l?eH(i,{left:s+a+"px",top:o+"px"}):eH(i,{left:Math.min(Math.round(a+s-(i.offsetWidth-r)/2),this.chart.chartWidth-i.offsetWidth)+"px",top:o-(i.offsetHeight-n)/2+"px"})}}hideInput(t){let e="min"===t?this.minInput:this.maxInput;e&&eH(e,{top:"-9999em",border:0,width:"1px",height:"1px"})}defaultInputDateParser(t,e,i){return i?.parse(t)||0}drawInput(t){let{chart:e,div:i,inputGroup:s}=this,o=this,a=e.renderer.style||{},r=e.renderer,n=e.options.rangeSelector,l=eG.lang,h="min"===t;function d(t){let{maxInput:i,minInput:s}=o,a=e.xAxis[0],r=e.scroller?.getUnionExtremes()||a,n=r.dataMin,l=r.dataMax,d=e.xAxis[0].getExtremes()[t],p=o.getInputValue(t);eK(p)&&p!==d&&(h&&i&&eK(n)?p>Number(i.getAttribute("data-hc-time"))?p=void 0:p<n&&(p=n):s&&eK(l)&&(p<Number(s.getAttribute("data-hc-time"))?p=void 0:p>l&&(p=l)),void 0!==p&&a.setExtremes(h?p:a.min,h?a.max:p,void 0,void 0,{trigger:"rangeSelectorInput"}))}let p=l[h?"rangeSelectorFrom":"rangeSelectorTo"]||"",u=r.label(p,0).addClass("highcharts-range-label").attr({padding:2*!!p,height:p?n.inputBoxHeight:0}).add(s),g=r.label("",0).addClass("highcharts-range-input").attr({padding:2,width:n.inputBoxWidth,height:n.inputBoxHeight,"text-align":"center"}).on("click",function(){o.showInput(t),o[t+"Input"].focus()});e.styledMode||g.attr({stroke:n.inputBoxBorderColor,"stroke-width":1}),g.add(s);let m=eN("input",{name:t,className:"highcharts-range-selector"},void 0,i);m.setAttribute("type",e2(n.inputDateFormat||"%e %b %Y")),e.styledMode||(u.css(eJ(a,n.labelStyle)),g.css(eJ({color:"#333333"},a,n.inputStyle)),eH(m,eZ({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:a.fontSize,fontFamily:a.fontFamily,top:"-9999em"},n.inputStyle))),m.onfocus=()=>{o.showInput(t)},m.onblur=()=>{m===c().doc.activeElement&&d(t),o.hideInput(t),o.setInputValue(t),m.blur()};let x=!1;return m.onchange=()=>{x||(d(t),o.hideInput(t),m.blur())},m.onkeypress=e=>{13===e.keyCode&&d(t)},m.onkeydown=e=>{x=!0,("ArrowUp"===e.key||"ArrowDown"===e.key||"Tab"===e.key)&&d(t)},m.onkeyup=()=>{x=!1},{dateBox:g,input:m,label:u}}getPosition(){let t=this.chart,e=t.options.rangeSelector,i="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:i+e.buttonPosition.y,inputTop:i+e.inputPosition.y-10}}getYTDExtremes(t,e){let i=this.chart.time,s=i.toParts(t)[0];return{max:t,min:Math.max(e,i.makeTime(s,0))}}createElements(){let t=this.chart,e=t.renderer,i=t.container,s=t.options,o=s.rangeSelector,a=o.inputEnabled,r=e0(s.chart.style?.zIndex,0)+1;!1!==o.enabled&&(this.group=e.g("range-selector-group").attr({zIndex:7}).add(),this.div=eN("div",void 0,{position:"relative",height:0,zIndex:r}),this.buttonOptions.length&&this.renderButtons(),i.parentNode&&i.parentNode.insertBefore(this.div,i),a&&this.createInputs())}createInputs(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);let t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;let e=this.drawInput("max");this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input}render(t,e){if(!1===this.options.enabled)return;let i=this.chart,s=i.options.rangeSelector;if(s.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",e),this.chart.styledMode||(this.maxLabel?.css(s.labelStyle),this.minLabel?.css(s.labelStyle));let o=i.scroller&&i.scroller.getUnionExtremes()||i.xAxis[0]||{};if(eF(o.dataMin)&&eF(o.dataMax)){let t=i.xAxis[0].minRange||0;this.setInputExtremes("min",o.dataMin,Math.min(o.dataMax,this.getInputValue("max"))-t),this.setInputExtremes("max",Math.max(o.dataMin,this.getInputValue("min"))+t,o.dataMax)}if(this.inputGroup){let t=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(e=>{if(e){let{width:i}=e.getBBox();i&&(e.attr({x:t}),t+=i+s.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(s.labelStyle),this.alignElements(),this.updateButtonStates()}renderButtons(){var t;let{chart:e,options:i}=this,s=eG.lang,o=e.renderer,a=eJ(i.buttonTheme),r=a&&a.states;delete a.width,delete a.states,this.buttonGroup=o.g("range-selector-buttons").add(this.group);let n=this.dropdown=eN("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),l=e.userOptions.rangeSelector?.buttonTheme;this.dropdownLabel=o.button("",0,0,()=>{},eJ(a,{"stroke-width":e0(a["stroke-width"],0),width:"auto",paddingLeft:e0(i.buttonTheme.paddingLeft,l?.padding,8),paddingRight:e0(i.buttonTheme.paddingRight,l?.padding,8)}),r&&r.hover,r&&r.select,r&&r.disabled).hide().add(this.group),eU(n,"touchstart",()=>{n.style.fontSize="16px"});let h=c().isMS?"mouseover":"mouseenter",d=c().isMS?"mouseout":"mouseleave";eU(n,h,()=>{eq(this.dropdownLabel.element,h)}),eU(n,d,()=>{eq(this.dropdownLabel.element,d)}),eU(n,"change",()=>{eq(this.buttons[n.selectedIndex-1].element,"click")}),this.zoomText=o.label(s.rangeSelectorZoom||"",0).attr({padding:i.buttonTheme.padding,height:i.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(i.labelStyle),(t=i.buttonTheme)["stroke-width"]??(t["stroke-width"]=0)),eN("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,n),this.createButtons()}createButtons(){let{options:t}=this,e=eJ(t.buttonTheme),i=e&&e.states,s=e.width||28;delete e.width,delete e.states,this.buttonOptions.forEach((t,e)=>{this.createButton(t,e,s,i)})}createButton(t,e,i,s){let{dropdown:o,buttons:a,chart:r,options:n}=this,l=r.renderer,h=eJ(n.buttonTheme);o?.add(eN("option",{textContent:t.title||t.text}),e+2),a[e]=l.button(t.text??"",0,0,i=>{let s,o=t.events&&t.events.click;o&&(s=o.call(t,i)),!1!==s&&this.clickButton(e),this.isActive=!0},h,s&&s.hover,s&&s.select,s&&s.disabled).attr({"text-align":"center",width:i}).add(this.buttonGroup),t.title&&a[e].attr("title",t.title)}alignElements(){let{buttonGroup:t,buttons:e,chart:i,group:s,inputGroup:o,options:a,zoomText:r}=this,n=i.options,l=n.exporting&&!1!==n.exporting.enabled&&n.navigation&&n.navigation.buttonOptions,{buttonPosition:h,inputPosition:d,verticalAlign:p}=a,c=(t,e,s)=>l&&this.titleCollision(i)&&"top"===p&&s&&e.y-t.getBBox().height-12<(l.y||0)+(l.height||0)+i.spacing[0]?-40:0,u=i.plotLeft;if(s&&h&&d){let n=h.x-i.spacing[3];if(t){if(this.positionButtons(),!this.initialButtonGroupWidth){let t=0;r&&(t+=r.getBBox().width+5),e.forEach((i,s)=>{t+=i.width||0,s!==e.length-1&&(t+=a.buttonSpacing)}),this.initialButtonGroupWidth=t}u-=i.spacing[3];let o=c(t,h,"right"===h.align||"right"===d.align);this.alignButtonGroup(o),this.buttonGroup?.translateY&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),s.placed=t.placed=i.hasLoaded}let l=0;a.inputEnabled&&o&&(l=c(o,d,"right"===h.align||"right"===d.align),"left"===d.align?n=u:"right"===d.align&&(n=-Math.max(i.axisOffset[1],-l)),o.align({y:d.y,width:o.getBBox().width,align:d.align,x:d.x+n-2},!0,i.spacingBox),o.placed=i.hasLoaded),this.handleCollision(l),s.align({verticalAlign:p},!0,i.spacingBox);let g=s.alignAttr.translateY,m=s.getBBox().height+20,x=0;if("bottom"===p){let t=i.legend&&i.legend.options;x=g-(m=m+(t&&"bottom"===t.verticalAlign&&t.enabled&&!t.floating?i.legend.legendHeight+e0(t.margin,10):0)-20)-(a.floating?0:a.y)-(i.titleOffset?i.titleOffset[2]:0)-10}"top"===p?(a.floating&&(x=0),i.titleOffset&&i.titleOffset[0]&&(x=i.titleOffset[0]),x+=i.margin[0]-i.spacing[0]||0):"middle"===p&&(d.y===h.y?x=g:(d.y||h.y)&&(d.y<0||h.y<0?x-=Math.min(d.y,h.y):x=g-m)),s.translate(a.x,a.y+Math.floor(x));let{minInput:f,maxInput:b,dropdown:v}=this;a.inputEnabled&&f&&b&&(f.style.marginTop=s.translateY+"px",b.style.marginTop=s.translateY+"px"),v&&(v.style.marginTop=s.translateY+"px")}}redrawElements(){let t=this.chart,{inputBoxHeight:e,inputBoxBorderColor:i}=this.options;if(this.maxDateBox?.attr({height:e}),this.minDateBox?.attr({height:e}),t.styledMode||(this.maxDateBox?.attr({stroke:i}),this.minDateBox?.attr({stroke:i})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;let t=this.options.buttons??[],e=Math.min(t.length,this.buttonOptions.length),{dropdown:i,options:s}=this,o=eJ(s.buttonTheme),a=o&&o.states,r=o.width||28;if(t.length<this.buttonOptions.length)for(let e=this.buttonOptions.length-1;e>=t.length;e--){let t=this.buttons.pop();t?.destroy(),this.dropdown?.options.remove(e+1)}for(let s=e-1;s>=0;s--)if(0!==Object.keys(e_(t[s],this.buttonOptions[s])).length){let e=t[s];this.buttons[s].destroy(),i?.options.remove(s+1),this.createButton(e,s,r,a),this.computeButtonRange(e)}if(t.length>this.buttonOptions.length)for(let e=this.buttonOptions.length;e<t.length;e++)this.createButton(t[e],e,r,a),this.computeButtonRange(t[e]);this.buttonOptions=this.options.buttons??[],eF(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}}alignButtonGroup(t,e){let{chart:i,options:s,buttonGroup:o,dropdown:a,dropdownLabel:r}=this,{buttonPosition:n}=s,l=i.plotLeft-i.spacing[3],h=n.x-i.spacing[3],d=i.plotLeft;"right"===n.align?(h+=t-l,this.hasVisibleDropdown&&(d=i.chartWidth+t-this.maxButtonWidth()-20)):"center"===n.align&&(h-=l/2,this.hasVisibleDropdown&&(d=i.chartWidth/2-this.maxButtonWidth())),a&&eH(a,{left:d+"px",top:o?.translateY+"px"}),r?.attr({x:d}),o&&o.align({y:n.y,width:e0(e,this.initialButtonGroupWidth),align:n.align,x:h},!0,i.spacingBox)}positionButtons(){let{buttons:t,chart:e,options:i,zoomText:s}=this,o=e.hasLoaded?"animate":"attr",{buttonPosition:a}=i,r=e.plotLeft,n=r;s&&"hidden"!==s.visibility&&(s[o]({x:e0(r+a.x,r)}),n+=a.x+s.getBBox().width+5);for(let e=0,s=this.buttonOptions.length;e<s;++e)"hidden"!==t[e].visibility?(t[e][o]({x:n}),n+=(t[e].width||0)+i.buttonSpacing):t[e][o]({x:r})}handleCollision(t){let{chart:e,buttonGroup:i,inputGroup:s,initialButtonGroupWidth:o}=this,{buttonPosition:a,dropdown:r,inputPosition:n}=this.options,l=()=>{s&&i&&s.attr({translateX:s.alignAttr.translateX+(e.axisOffset[1]>=-t?0:-t),translateY:s.alignAttr.translateY+i.getBBox().height+10})};s&&i?n.align===a.align?(l(),o>e.plotWidth+t-20?this.collapseButtons():this.expandButtons()):o-t+s.getBBox().width>e.plotWidth?"responsive"===r?this.collapseButtons():l():this.expandButtons():i&&"responsive"===r&&(o>e.plotWidth?this.collapseButtons():this.expandButtons()),i&&("always"===r&&this.collapseButtons(),"never"===r&&this.expandButtons()),this.alignButtonGroup(t)}collapseButtons(){let{buttons:t,zoomText:e}=this;!0!==this.isCollapsed&&(this.isCollapsed=!0,e.hide(),t.forEach(t=>void t.hide()),this.showDropdown())}expandButtons(){let{buttons:t,zoomText:e}=this;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),e.show(),t.forEach(t=>void t.show()),this.positionButtons())}showDropdown(){let{buttonGroup:t,dropdownLabel:e,dropdown:i}=this;t&&i&&(e.show(),eH(i,{visibility:"inherit"}),this.hasVisibleDropdown=!0)}hideDropdown(){let{dropdown:t}=this;t&&(this.dropdownLabel.hide(),eH(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)}getHeight(){let t=this.options,e=this.group,i=t.inputPosition,s=t.buttonPosition,o=t.y,a=s.y,r=i.y,n=0;if(t.height)return t.height;this.alignElements(),n=e?e.getBBox(!0).height+13+o:0;let l=Math.min(r,a);return(r<0&&a<0||r>0&&a>0)&&(n+=Math.abs(l)),n}titleCollision(t){return!(t.options.title.text||t.options.subtitle.text)}update(t,e=!0){let i=this.chart;if(eJ(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,i.options.rangeSelector.selected=void 0),eF(t.enabled))return this.destroy(),this.init(i);this.isDirty=!!t.buttons,e&&this.render()}destroy(){let t=this,e=t.minInput,i=t.maxInput;t.eventsToUnbind&&(t.eventsToUnbind.forEach(t=>t()),t.eventsToUnbind=void 0),eV(t.buttons),e&&(e.onfocus=e.onblur=e.onchange=null),i&&(i.onfocus=i.onblur=i.onchange=null),eQ(t,function(e,i){e&&"chart"!==i&&(e instanceof eL()?e.destroy():e instanceof window.HTMLElement&&ej(e),delete t[i]),e!==e3.prototype[i]&&(t[i]=null)},this),this.buttons=[]}}eZ(e3.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}});let e5=i.default.Chart;var e6=d.n(e5);let{format:e9}=eX(),{getOptions:e4}=c(),{setFixedRange:e8}=tu,{addEvent:e7,clamp:it,crisp:ie,defined:ii,extend:is,find:io,isNumber:ia,isString:ir,merge:il,pick:ih,splat:id}=c();function ip(t,e,i){return"xAxis"===t?{minPadding:0,maxPadding:0,overscroll:0,ordinal:!0}:"yAxis"===t?{labels:{y:-2},opposite:i.opposite??e.opposite??!0,showLastLabel:!!(e.categories||"category"===e.type),title:{text:void 0}}:{}}function ic(t,e){if("xAxis"===t){let t=ih(e.navigator?.enabled,to.enabled,!0),i={type:"datetime",categories:void 0};return t&&(i.startOnTick=!1,i.endOnTick=!1),i}return{}}class iu extends e6(){init(t,e){let i=e4(),s=t.xAxis,o=t.yAxis,a=ih(t.navigator?.enabled,to.enabled,!0);t.xAxis=t.yAxis=void 0;let r=il({chart:{panning:{enabled:!0,type:"x"},zooming:{pinchType:"x",mouseWheel:{type:"x"}}},navigator:{enabled:a},scrollbar:{enabled:ih(tT.enabled,!0)},rangeSelector:{enabled:ih(em.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:ih(i.tooltip?.split,!0),crosshairs:!0},legend:{enabled:!1}},t,{isStock:!0});t.xAxis=s,t.yAxis=o,r.xAxis=id(t.xAxis||{}).map(e=>il(ip("xAxis",e,i.xAxis),e,ic("xAxis",t))),r.yAxis=id(t.yAxis||{}).map(t=>il(ip("yAxis",t,i.yAxis),t)),super.init(r,e)}createAxis(t,e){return e.axis=il(ip(t,e.axis,e4()[t]),e.axis,ic(t,this.userOptions)),super.createAxis(t,e)}}e7(e6(),"update",function(t){let e=t.options;"scrollbar"in e&&this.navigator&&(il(!0,this.options.scrollbar,e.scrollbar),this.navigator.update({enabled:!!this.navigator.navigatorEnabled}),delete e.scrollbar)}),function(t){function e(t){if(!(this.crosshair?.label?.enabled&&this.cross&&ia(this.min)&&ia(this.max)))return;let e=this.chart,i=this.logarithmic,s=this.crosshair.label,o=this.horiz,a=this.opposite,r=this.left,n=this.top,l=this.width,h="inside"===this.options.tickPosition,d=!1!==this.crosshair.snap,p=t.e||this.cross?.e,c=t.point,u=this.crossLabel,g,m,x=s.format,f="",b,v=0,y=this.min,M=this.max;i&&(y=i.lin2log(this.min),M=i.lin2log(this.max));let A=o?"center":a?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center";u||(u=this.crossLabel=e.renderer.label("",0,void 0,s.shape||"callout").addClass("highcharts-crosshair-label highcharts-color-"+(c?.series?c.series.colorIndex:this.series[0]&&this.series[0].colorIndex)).attr({align:s.align||A,padding:ih(s.padding,8),r:ih(s.borderRadius,3),zIndex:2}).add(this.labelGroup),e.styledMode||u.attr({fill:s.backgroundColor||c?.series?.color||"#666666",stroke:s.borderColor||"","stroke-width":s.borderWidth||0}).css(is({color:"#ffffff",fontWeight:"normal",fontSize:"0.7em",textAlign:"center"},s.style||{}))),o?(g=d?(c.plotX||0)+r:p.chartX,m=n+(a?0:this.height)):(g=r+this.offset+(a?l:0),m=d?(c.plotY||0)+n:p.chartY),x||s.formatter||(this.dateTime&&(f="%b %d, %Y"),x="{value"+(f?":"+f:"")+"}");let w=d?this.isXAxis?c.x:c.y:this.toValue(o?p.chartX:p.chartY),k=c?.series?c.series.isPointInside(c):ia(w)&&w>y&&w<M,S="";x?S=e9(x,{value:w},e):s.formatter&&ia(w)&&(S=s.formatter.call(this,w)),u.attr({text:S,x:g,y:m,visibility:k?"inherit":"hidden"});let O=u.getBBox();!ia(u.x)||o||a||(g=u.x-O.width/2),ia(u.y)&&(o?(h&&!a||!h&&a)&&(m=u.y-O.height):m=u.y-O.height/2),b=o?{left:r,right:r+this.width}:{left:"left"===this.labelAlign?r:0,right:"right"===this.labelAlign?r+this.width:e.chartWidth};let E=u.translateX||0;E<b.left&&(v=b.left-E),E+O.width>=b.right&&(v=-(E+O.width-b.right)),u.attr({x:Math.max(0,g+v),y:Math.max(0,m),anchorX:o?g:this.opposite?0:e.chartWidth,anchorY:o?this.opposite?e.chartHeight:0:m+O.height/2})}function i(){this.crossLabel&&(this.crossLabel=this.crossLabel.hide())}function s(t){let e=this.chart,i=this.options,s=e._labelPanes=e._labelPanes||{},o=i.labels;if(e.options.isStock&&"yAxis"===this.coll){let e=i.top+","+i.height;!s[e]&&o.enabled&&(15===o.distance&&1===this.side&&(o.distance=0),void 0===o.align&&(o.align="right"),s[e]=this,t.align="right",t.preventDefault())}}function o(){let t=this.chart,e=this.options&&this.options.top+","+this.options.height;e&&t._labelPanes&&t._labelPanes[e]===this&&delete t._labelPanes[e]}function a(t){let e=this,i=e.isLinked&&!e.series&&e.linkedParent?e.linkedParent.series:e.series,s=e.chart,o=s.renderer,a=e.left,r=e.top,n=[],l=t.translatedValue,h=t.value,d=t.force,p,c,u,g,m=[],x,f;if(s.options.isStock&&!1!==t.acrossPanes&&"xAxis"===e.coll||"yAxis"===e.coll){for(let o of(t.preventDefault(),m=(t=>{let o="xAxis"===t?"yAxis":"xAxis",a=e.options[o];return ia(a)?[s[o][a]]:ir(a)?[s.get(a)]:i.map(t=>t[o])})(e.coll),e.isXAxis?s.yAxis:s.xAxis))if(!o.options.isInternal){let t=o.isXAxis?"yAxis":"xAxis";e===(ii(o.options[t])?s[t][o.options[t]]:s[t][0])&&m.push(o)}for(let t of(x=m.length?[]:[e.isXAxis?s.yAxis[0]:s.xAxis[0]],m))-1!==x.indexOf(t)||io(x,e=>e.pos===t.pos&&e.len===t.len)||x.push(t);if(ia(f=ih(l,e.translate(h||0,void 0,void 0,t.old)))){if(e.horiz)for(let t of x){let i;g=(c=t.pos)+t.len,p=u=Math.round(f+e.transB),"pass"!==d&&(p<a||p>a+e.width)&&(d?p=u=it(p,a,a+e.width):i=!0),i||n.push(["M",p,c],["L",u,g])}else for(let t of x){let i;u=(p=t.pos)+t.len,c=g=Math.round(r+e.height-f),"pass"!==d&&(c<r||c>r+e.height)&&(d?c=g=it(c,r,r+e.height):i=!0),i||n.push(["M",p,c],["L",u,g])}}t.path=n.length>0?o.crispPolyLine(n,t.lineWidth||1):void 0}}function r(t){if(this.chart.options.isStock){let e;this.is("column")||this.is("columnrange")?e={borderWidth:0,shadow:!1}:this.is("scatter")||this.is("sma")||(e={marker:{enabled:!1,radius:2}}),e&&(t.plotOptions[this.type]=il(t.plotOptions[this.type],e))}}function n(){let t=this.chart,e=this.options.dataGrouping;return!1!==this.allowDG&&e&&ih(e.enabled,t.options.isStock)}function l(t,e){for(let i=0;i<t.length;i+=2){let s=t[i],o=t[i+1];ii(s[1])&&s[1]===o[1]&&(s[1]=o[1]=ie(s[1],e)),ii(s[2])&&s[2]===o[2]&&(s[2]=o[2]=ie(s[2],e))}return t}t.compose=function(t,h,d,p){let c=d.prototype;c.forceCropping||(e7(h,"afterDrawCrosshair",e),e7(h,"afterHideCrosshair",i),e7(h,"autoLabelAlign",s),e7(h,"destroy",o),e7(h,"getPlotLinePath",a),t.prototype.setFixedRange=e8,c.forceCropping=n,e7(d,"setOptions",r),p.prototype.crispPolyLine=l)},t.stockChart=function(e,i,s){return new t(e,i,s)}}(iu||(iu={}));let ig=iu,{column:{prototype:{pointClass:im}}}=te().seriesTypes,{column:ix}=te().seriesTypes,{crisp:ib,extend:iv,merge:iy}=c(),{defaultOptions:iM}=c();class iA extends ix{extendStem(t,e,i){let s=t[0],o=t[1];"number"==typeof s[2]&&(s[2]=Math.max(i+e,s[2])),"number"==typeof o[2]&&(o[2]=Math.min(i-e,o[2]))}getPointPath(t,e){let i=e.strokeWidth(),s=t.series,o=ib(t.plotX||0,i),a=Math.round(t.shapeArgs.width/2),r=[["M",o,Math.round(t.yBottom)],["L",o,Math.round(t.plotHigh)]];if(null!==t.close){let e=ib(t.plotClose,i);r.push(["M",o,e],["L",o+a,e]),s.extendStem(r,i/2,e)}return r}drawSinglePoint(t){let e=t.series,i=e.chart,s,o=t.graphic;void 0!==t.plotY&&(o||(t.graphic=o=i.renderer.path().add(e.group)),i.styledMode||o.attr(e.pointAttribs(t,t.selected&&"select")),s=e.getPointPath(t,o),o[o?"animate":"attr"]({d:s}).addClass(t.getClassName(),!0))}drawPoints(){this.points.forEach(this.drawSinglePoint)}init(){super.init.apply(this,arguments),this.options.stacking=void 0}pointAttribs(t,e){let i=super.pointAttribs.call(this,t,e);return delete i.fill,i}toYData(t){return[t.high,t.low,t.close]}translate(){let t=this,e=t.yAxis,i=this.pointArrayMap&&this.pointArrayMap.slice()||[],s=i.map(t=>`plot${t.charAt(0).toUpperCase()+t.slice(1)}`);s.push("yBottom"),i.push("low"),super.translate.apply(t),t.points.forEach(function(o){i.forEach(function(i,a){let r=o[i];null!==r&&(t.dataModify&&(r=t.dataModify.modifyValue(r)),o[s[a]]=e.toPixels(r,!0))}),o.tooltipPos[1]=o.plotHigh+e.pos-t.chart.plotTop})}}iA.defaultOptions=iy(ix.defaultOptions,{lineWidth:1,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>{series.chart.options.lang.stockHigh}: {point.high}<br/>{series.chart.options.lang.stockLow}: {point.low}<br/>{series.chart.options.lang.stockClose}: {point.close}<br/>'},threshold:null,states:{hover:{lineWidth:3}},stickyTracking:!0}),iv(iA.prototype,{pointClass:class extends im{},animate:null,directTouch:!1,keysAffectYAxis:["low","high"],pointArrayMap:["high","low","close"],pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"},pointValKey:"close"}),iv(iM.lang,{stockOpen:"Open",stockHigh:"High",stockLow:"Low",stockClose:"Close"}),te().registerSeriesType("hlc",iA);let{seriesTypes:{hlc:iw}}=te();class ik extends iw.prototype.pointClass{getClassName(){return super.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")}resolveUpColor(){this.open<this.close&&!this.options.color&&this.series.options.upColor&&(this.color=this.series.options.upColor)}resolveColor(){super.resolveColor(),this.series.is("heikinashi")||this.resolveUpColor()}getZone(){let t=super.getZone();return this.resolveUpColor(),t}applyOptions(){return super.applyOptions.apply(this,arguments),this.resolveColor&&this.resolveColor(),this}}let{composed:iS}=c(),{hlc:iO}=te().seriesTypes,{addEvent:iE,crisp:iC,extend:iT,merge:iB,pushUnique:iD}=c();function iP(t){let e=t.options,i=e.dataGrouping;i&&e.useOhlcData&&"highcharts-navigator-series"!==e.id&&(i.approximation="ohlc")}function iR(t){let e=t.options;e.useOhlcData&&"highcharts-navigator-series"!==e.id&&iT(this,{pointValKey:iz.prototype.pointValKey,pointArrayMap:iz.prototype.pointArrayMap,toYData:iz.prototype.toYData})}class iz extends iO{static compose(t,...e){iD(iS,"OHLCSeries")&&(iE(t,"afterSetOptions",iP),iE(t,"init",iR))}getPointPath(t,e){let i=super.getPointPath(t,e),s=e.strokeWidth(),o=iC(t.plotX||0,s),a=Math.round(t.shapeArgs.width/2);if(null!==t.open){let e=iC(t.plotOpen,s);i.push(["M",o,e],["L",o-a,e]),super.extendStem(i,s/2,e)}return i}pointAttribs(t,e){let i=super.pointAttribs.call(this,t,e),s=this.options;return delete i.fill,!t.options.color&&s.upColor&&t.open<t.close&&(i.stroke=s.upColor),i}toYData(t){return[t.open,t.high,t.low,t.close]}}iz.defaultOptions=iB(iO.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>{series.chart.options.lang.stockOpen}: {point.open}<br/>{series.chart.options.lang.stockHigh}: {point.high}<br/>{series.chart.options.lang.stockLow}: {point.low}<br/>{series.chart.options.lang.stockClose}: {point.close}<br/>'}}),iT(iz.prototype,{pointClass:ik,pointArrayMap:["open","high","low","close"]}),te().registerSeriesType("ohlc",iz);let{column:iI,ohlc:iL}=te().seriesTypes,{crisp:iW,merge:iX}=c();class iG extends iL{pointAttribs(t,e){let i=iI.prototype.pointAttribs.call(this,t,e),s=this.options,o=t.open<t.close,a=s.lineColor||this.color,r=t.color||this.color;if(i["stroke-width"]=s.lineWidth,i.fill=t.options.color||o&&s.upColor||r,i.stroke=t.options.lineColor||o&&s.upLineColor||a,e){let t=s.states[e];i.fill=t.color||i.fill,i.stroke=t.lineColor||i.stroke,i["stroke-width"]=t.lineWidth||i["stroke-width"]}return i}drawPoints(){let t=this.points,e=this.chart,i=this.yAxis.reversed;for(let s of t){let t=s.graphic,o,a,r,n,l,h,d,p,c,u=!t;if(void 0!==s.plotY){t||(s.graphic=t=e.renderer.path().add(this.group)),this.chart.styledMode||t.attr(this.pointAttribs(s,s.selected&&"select")).shadow(this.options.shadow);let g=t.strokeWidth();d=iW(s.plotX||0,g),r=Math.min(o=s.plotOpen,a=s.plotClose),n=Math.max(o,a),c=Math.round(s.shapeArgs.width/2),l=i?n!==s.yBottom:Math.round(r)!==Math.round(s.plotHigh||0),h=i?Math.round(r)!==Math.round(s.plotHigh||0):n!==s.yBottom,r=iW(r,g),n=iW(n,g),(p=[]).push(["M",d-c,n],["L",d-c,r],["L",d+c,r],["L",d+c,n],["Z"],["M",d,r],["L",d,l?Math.round(i?s.yBottom:s.plotHigh):r],["M",d,n],["L",d,h?Math.round(i?s.plotHigh:s.yBottom):n]),t[u?"attr":"animate"]({d:p}).addClass(s.getClassName(),!0)}}}}iG.defaultOptions=iX(iL.defaultOptions,{tooltip:iL.defaultOptions.tooltip},{states:{hover:{lineWidth:2}},threshold:null,lineColor:"#000000",lineWidth:1,upColor:"#ffffff",stickyTracking:!0}),te().registerSeriesType("candlestick",iG);let{column:{prototype:{pointClass:iY}}}=te().seriesTypes,{isNumber:iU}=c(),iN=class extends iY{constructor(){super(...arguments),this.ttBelow=!1}isValid(){return iU(this.y)||void 0===this.y}hasNewShapeType(){let t=this.options.shape||this.series.options.shape;return this.graphic&&t&&t!==this.graphic.symbolKey}};!function(t){let e=[];function i(t,e,i,s,o){let a=o&&o.anchorX||t,r=o&&o.anchorY||e,n=this.circle(a-1,r-1,2,2);return n.push(["M",a,r],["L",t,e+s],["L",t,e],["L",t+i,e],["L",t+i,e+s],["L",t,e+s],["Z"]),n}function s(t,e){t[e+"pin"]=function(i,s,o,a,r){let n,l=r&&r.anchorX,h=r&&r.anchorY;if("circle"===e&&a>o&&(i-=Math.round((a-o)/2),o=a),n=t[e](i,s,o,a,r),l&&h){let r=l;if("circle"===e)r=i+o/2;else{let t=n[0],e=n[1];"M"===t[0]&&"L"===e[0]&&(r=(t[1]+e[1])/2)}let d=s>h?s:s+a;n.push(["M",r,d],["L",l,h]),n=n.concat(t.circle(l-1,h-1,2,2))}return n}}t.compose=function(t){if(-1===e.indexOf(t)){e.push(t);let o=t.prototype.symbols;o.flag=i,s(o,"circle"),s(o,"square")}let o=tp().getRendererType();e.indexOf(o)&&e.push(o)}}(l||(l={}));let iH=l,iF=i.default.Series.types.column;var iV=d.n(iF);let{composed:i_}=c(),{prototype:ij}=iV(),{prototype:iZ}=b(),{defined:iq,pushUnique:iK,stableSort:i$}=c();!function(t){function e(t){return iZ.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this,t)}function i(){ij.translate.apply(this);let t=this,e=t.options,i=t.chart,s=t.points,o=e.onSeries,a=o&&i.get(o),r=a&&a.options.step,n=a&&a.points,l=i.inverted,h=t.xAxis,d=t.yAxis,p=s.length-1,c,u,g=e.onKey||"y",m=n&&n.length,x=0,f,b,v,y,M;if(a&&a.visible&&m){for(x=(a.pointXOffset||0)+(a.barW||0)/2,y=a.currentDataGrouping,b=n[m-1].x+(y?y.totalRange:0),i$(s,(t,e)=>t.x-e.x),g="plot"+g[0].toUpperCase()+g.substr(1);m--&&s[p];)if(f=n[m],(c=s[p]).y=f.y,f.x<=c.x&&void 0!==f[g]){if(c.x<=b&&(c.plotY=f[g],f.x<c.x&&!r&&(v=n[m+1])&&void 0!==v[g])){if(iq(c.plotX)&&a.is("spline")){let t=[f.plotX||0,f.plotY||0],e=[v.plotX||0,v.plotY||0],i=f.controlPoints?.high||t,s=v.controlPoints?.low||e,o=(o,a)=>Math.pow(1-o,3)*t[a]+3*(1-o)*(1-o)*o*i[a]+3*(1-o)*o*o*s[a]+o*o*o*e[a],a=0,r=1,n;for(let t=0;t<100;t++){let t=(a+r)/2,e=o(t,0);if(null===e)break;if(.25>Math.abs(e-c.plotX)){n=t;break}e<c.plotX?a=t:r=t}iq(n)&&(c.plotY=o(n,1),c.y=d.toValue(c.plotY,!0))}else M=(c.x-f.x)/(v.x-f.x),c.plotY+=M*(v[g]-f[g]),c.y+=M*(v.y-f.y)}if(p--,m++,p<0)break}}s.forEach((e,i)=>{let o;e.plotX+=x,(void 0===e.plotY||l)&&(e.plotX>=0&&e.plotX<=h.len?l?(e.plotY=h.translate(e.x,0,1,0,1),e.plotX=iq(e.y)?d.translate(e.y,0,0,0,1):0):e.plotY=(h.opposite?0:t.yAxis.len)+h.offset:e.shapeArgs={}),(u=s[i-1])&&u.plotX===e.plotX&&(void 0===u.stackIndex&&(u.stackIndex=0),o=u.stackIndex+1),e.stackIndex=o}),this.onSeries=a}t.compose=function(t){if(iK(i_,"OnSeries")){let s=t.prototype;s.getPlotBox=e,s.translate=i}return t},t.getPlotBox=e,t.translate=i}(h||(h={}));let iJ=h,{noop:iQ}=c(),{distribute:i0}=c(),{series:i1,seriesTypes:{column:i2}}=te(),{addEvent:i3,defined:i5,extend:i6,isNumber:i9,merge:i4,objectEach:i8,wrap:i7}=c();class st extends i2{animate(t){t&&this.setClip()}drawPoints(){let t,e,i,s,o,a,r,n,l,h,d,p=this.points,c=this.chart,u=c.renderer,g=c.inverted,m=this.options,x=m.y,f=this.yAxis,b={},v=[],y=i9(m.borderRadius)?m.borderRadius:0;for(s=p.length;s--;)o=p[s],h=(g?o.plotY:o.plotX)>this.xAxis.len,t=o.plotX,r=o.stackIndex,i=o.options.shape||m.shape,void 0!==(e=o.plotY)&&(e=o.plotY+x-(void 0!==r&&r*m.stackDistance)),o.anchorX=r?void 0:o.plotX,n=r?void 0:o.plotY,d="flag"!==i,a=o.graphic,void 0!==e&&t>=0&&!h?(a&&o.hasNewShapeType()&&(a=a.destroy()),a||(a=o.graphic=u.label("",0,void 0,i,void 0,void 0,m.useHTML).addClass("highcharts-point").add(this.markerGroup),o.graphic.div&&(o.graphic.div.point=o),a.isNew=!0),a.attr({align:d?"center":"left",width:m.width,height:m.height,"text-align":m.textAlign,r:y}),c.styledMode||a.attr(this.pointAttribs(o)).css(i4(m.style,o.style)).shadow(m.shadow),t>0&&(t-=a.strokeWidth()%2),l={y:e,anchorY:n},m.allowOverlapX&&(l.x=t,l.anchorX=o.anchorX),a.attr({text:o.options.title??m.title??"A"})[a.isNew?"attr":"animate"](l),m.allowOverlapX||(b[o.plotX]?b[o.plotX].size=Math.max(b[o.plotX].size,a.width||0):b[o.plotX]={align:.5*!!d,size:a.width||0,target:t,anchorX:t}),o.tooltipPos=[t,e+f.pos-c.plotTop]):a&&(o.graphic=a.destroy());if(!m.allowOverlapX){let t=100;for(let e of(i8(b,function(e){e.plotX=e.anchorX,v.push(e),t=Math.max(e.size,t)}),i0(v,g?f.len:this.xAxis.len,t),p)){let t=e.plotX,i=e.graphic,s=i&&b[t];s&&i&&(i5(s.pos)?i[i.isNew?"attr":"animate"]({x:s.pos+(s.align||0)*s.size,anchorX:e.anchorX}).show().isNew=!1:i.hide().isNew=!0)}}m.useHTML&&this.markerGroup&&i7(this.markerGroup,"on",function(t){return eL().prototype.on.apply(t.apply(this,[].slice.call(arguments,1)),[].slice.call(arguments,1))})}drawTracker(){let t=this.points;for(let e of(super.drawTracker(),t)){let i=e.graphic;i&&(e.unbindMouseOver&&e.unbindMouseOver(),e.unbindMouseOver=i3(i.element,"mouseover",function(){for(let s of(e.stackIndex>0&&!e.raised&&(e._y=i.y,i.attr({y:e._y-8}),e.raised=!0),t))s!==e&&s.raised&&s.graphic&&(s.graphic.attr({y:s._y}),s.raised=!1)}))}}pointAttribs(t,e){let i=this.options,s=t&&t.color||this.color,o=i.lineColor,a=t&&t.lineWidth,r=t&&t.fillColor||i.fillColor;return e&&(r=i.states[e].fillColor,o=i.states[e].lineColor,a=i.states[e].lineWidth),{fill:r||s,stroke:o||s,"stroke-width":a||i.lineWidth||0}}setClip(){i1.prototype.setClip.apply(this,arguments),!1!==this.options.clip&&this.sharedClipKey&&this.markerGroup&&this.markerGroup.clip(this.chart.sharedClips[this.sharedClipKey])}}st.compose=iH.compose,st.defaultOptions=i4(i2.defaultOptions,{borderRadius:0,pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}"},threshold:null,y:-30,fillColor:"#ffffff",lineWidth:1,states:{hover:{lineColor:"#000000",fillColor:"#ccd3ff"}},style:{fontSize:"0.7em",fontWeight:"bold"}}),iJ.compose(st),i6(st.prototype,{allowDG:!1,forceCrop:!0,invertible:!1,noSharedTooltip:!0,pointClass:iN,sorted:!1,takeOrdinalPosition:!1,trackerGroups:["markerGroup"],buildKDTree:iQ,init:i1.prototype.init}),te().registerSeriesType("flags",st),d.d({},{}),d.d({},{}),d.d({},{});let se=c();se.Navigator=se.Navigator||t8,se.OrdinalAxis=se.OrdinalAxis||eg,se.RangeSelector=se.RangeSelector||e3,se.Scrollbar=se.Scrollbar||tY,se.stockChart=se.stockChart||ig.stockChart,se.StockChart=se.StockChart||se.stockChart,se.extend(se.StockChart,ig),T.compose(se.Series,se.Axis,se.Point),st.compose(se.Renderer),iz.compose(se.Series),se.Navigator.compose(se.Chart,se.Axis,se.Series),se.OrdinalAxis.compose(se.Axis,se.Series,se.Chart),se.RangeSelector.compose(se.Axis,se.Chart),se.Scrollbar.compose(se.Axis),se.StockChart.compose(se.Chart,se.Axis,se.Series,se.SVGRenderer);let si=c();export{si as default};