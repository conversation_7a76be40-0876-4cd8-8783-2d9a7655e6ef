!function(t,e){"object"==typeof exports&&"object"==typeof module?(t._Highcharts=e(),module.exports=t._Highcharts):"function"==typeof define&&define.amd?define("highcharts/highcharts",[],e):"object"==typeof exports?(t._Highcharts=e(),exports.highcharts=t._Highcharts):(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e())}("undefined"==typeof window?this:window,()=>(()=>{"use strict";let t,e,i;var s,r,o,a,n,h,l,d,c,p,g,u,f,m,x,y,b,v,k,M={};M.d=(t,e)=>{for(var i in e)M.o(e,i)&&!M.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},M.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var w={};M.d(w,{default:()=>dK}),function(t){t.SVG_NS="http://www.w3.org/2000/svg",t.product="Highcharts",t.version="12.2.0",t.win="undefined"!=typeof window?window:{},t.doc=t.win.document,t.svg=!!t.doc?.createElementNS?.(t.SVG_NS,"svg")?.createSVGRect,t.pageLang=t.doc?.documentElement?.closest("[lang]")?.lang,t.userAgent=t.win.navigator?.userAgent||"",t.isChrome=t.win.chrome,t.isFirefox=-1!==t.userAgent.indexOf("Firefox"),t.isMS=/(edge|msie|trident)/i.test(t.userAgent)&&!t.win.opera,t.isSafari=!t.isChrome&&-1!==t.userAgent.indexOf("Safari"),t.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(t.userAgent),t.isWebKit=-1!==t.userAgent.indexOf("AppleWebKit"),t.deg2rad=2*Math.PI/360,t.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],t.noop=function(){},t.supportsPassiveEvents=function(){let e=!1;if(!t.isMS){let i=Object.defineProperty({},"passive",{get:function(){e=!0}});t.win.addEventListener&&t.win.removeEventListener&&(t.win.addEventListener("testPassive",t.noop,i),t.win.removeEventListener("testPassive",t.noop,i))}return e}(),t.charts=[],t.composed=[],t.dateFormats={},t.seriesTypes={},t.symbolSizes={},t.chartCount=0}(s||(s={}));let S=s,{charts:A,doc:T,win:C}=S;function O(t,e,i,s){let r=e?"Highcharts error":"Highcharts warning";32===t&&(t=`${r}: Deprecated member`);let o=z(t),a=o?`${r} #${t}: www.highcharts.com/errors/${t}/`:t.toString();if(void 0!==s){let t="";o&&(a+="?"),U(s,function(e,i){t+=`
 - ${i}: ${e}`,o&&(a+=encodeURI(i)+"="+encodeURI(e))}),a+=t}$(S,"displayError",{chart:i,code:t,message:a,params:s},function(){if(e)throw Error(a);C.console&&-1===O.messages.indexOf(a)&&console.warn(a)}),O.messages.push(a)}function P(t,e){return parseInt(t,e||10)}function E(t){return"string"==typeof t}function L(t){let e=Object.prototype.toString.call(t);return"[object Array]"===e||"[object Array Iterator]"===e}function D(t,e){return!!t&&"object"==typeof t&&(!e||!L(t))}function I(t){return D(t)&&"number"==typeof t.nodeType}function B(t){let e=t?.constructor;return!!(D(t,!0)&&!I(t)&&e?.name&&"Object"!==e.name)}function z(t){return"number"==typeof t&&!isNaN(t)&&t<1/0&&t>-1/0}function N(t){return null!=t}function R(t,e,i){let s,r=E(e)&&!N(i),o=(e,i)=>{N(e)?t.setAttribute(i,e):r?(s=t.getAttribute(i))||"class"!==i||(s=t.getAttribute(i+"Name")):t.removeAttribute(i)};return E(e)?o(i,e):U(e,o),s}function W(t){return L(t)?t:[t]}function H(t,e){let i;for(i in t||(t={}),e)t[i]=e[i];return t}function X(){let t=arguments,e=t.length;for(let i=0;i<e;i++){let e=t[i];if(null!=e)return e}}function F(t,e){H(t.style,e)}function G(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))}function Y(t,e){return t>1e14?t:parseFloat(t.toPrecision(e||14))}(O||(O={})).messages=[],Math.easeInOutSine=function(t){return -.5*(Math.cos(Math.PI*t)-1)};let j=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){let i,s=t.length;for(i=0;i<s;i++)if(e(t[i],i))return t[i]};function U(t,e,i){for(let s in t)Object.hasOwnProperty.call(t,s)&&e.call(i||t[s],t[s],s,t)}function V(t,e,i){function s(e,i){let s=t.removeEventListener;s&&s.call(t,e,i,!1)}function r(i){let r,o;t.nodeName&&(e?(r={})[e]=!0:r=i,U(r,function(t,e){if(i[e])for(o=i[e].length;o--;)s(e,i[e][o].fn)}))}let o="function"==typeof t&&t.prototype||t;if(Object.hasOwnProperty.call(o,"hcEvents")){let t=o.hcEvents;if(e){let o=t[e]||[];i?(t[e]=o.filter(function(t){return i!==t.fn}),s(e,i)):(r(t),t[e]=[])}else r(t),delete o.hcEvents}}function $(t,e,i,s){if(i=i||{},T?.createEvent&&(t.dispatchEvent||t.fireEvent&&t!==S)){let s=T.createEvent("Events");s.initEvent(e,!0,!0),i=H(s,i),t.dispatchEvent?t.dispatchEvent(i):t.fireEvent(e,i)}else if(t.hcEvents){i.target||H(i,{preventDefault:function(){i.defaultPrevented=!0},target:t,type:e});let s=[],r=t,o=!1;for(;r.hcEvents;)Object.hasOwnProperty.call(r,"hcEvents")&&r.hcEvents[e]&&(s.length&&(o=!0),s.unshift.apply(s,r.hcEvents[e])),r=Object.getPrototypeOf(r);o&&s.sort((t,e)=>t.order-e.order),s.forEach(e=>{!1===e.fn.call(t,i)&&i.preventDefault()})}s&&!i.defaultPrevented&&s.call(t,i)}let Z=function(){let e=Math.random().toString(36).substring(2,9)+"-",i=0;return function(){return"highcharts-"+(t?"":e)+i++}}();C.jQuery&&(C.jQuery.fn.highcharts=function(){let t=[].slice.call(arguments);if(this[0])return t[0]?(new S[E(t[0])?t.shift():"Chart"](this[0],t[0],t[1]),this):A[R(this[0],"data-highcharts-chart")]});let _={addEvent:function(t,e,i,s={}){let r="function"==typeof t&&t.prototype||t;Object.hasOwnProperty.call(r,"hcEvents")||(r.hcEvents={});let o=r.hcEvents;S.Point&&t instanceof S.Point&&t.series&&t.series.chart&&(t.series.chart.runTrackerClick=!0);let a=t.addEventListener;a&&a.call(t,e,i,!!S.supportsPassiveEvents&&{passive:void 0===s.passive?-1!==e.indexOf("touch"):s.passive,capture:!1}),o[e]||(o[e]=[]);let n={fn:i,order:"number"==typeof s.order?s.order:1/0};return o[e].push(n),o[e].sort((t,e)=>t.order-e.order),function(){V(t,e,i)}},arrayMax:function(t){let e=t.length,i=t[0];for(;e--;)t[e]>i&&(i=t[e]);return i},arrayMin:function(t){let e=t.length,i=t[0];for(;e--;)t[e]<i&&(i=t[e]);return i},attr:R,clamp:function(t,e,i){return t>e?t<i?t:i:e},clearTimeout:function(t){N(t)&&clearTimeout(t)},correctFloat:Y,createElement:function(t,e,i,s,r){let o=T.createElement(t);return e&&H(o,e),r&&F(o,{padding:"0",border:"none",margin:"0"}),i&&F(o,i),s&&s.appendChild(o),o},crisp:function(t,e=0,i){let s=e%2/2,r=i?-1:1;return(Math.round(t*r-s)+s)*r},css:F,defined:N,destroyObjectProperties:function(t,e,i){U(t,function(s,r){s!==e&&s?.destroy&&s.destroy(),(s?.destroy||!i)&&delete t[r]})},diffObjects:function(t,e,i,s){let r={};return!function t(e,r,o,a){let n=i?r:e;U(e,function(i,h){if(!a&&s&&s.indexOf(h)>-1&&r[h]){i=W(i),o[h]=[];for(let e=0;e<Math.max(i.length,r[h].length);e++)r[h][e]&&(void 0===i[e]?o[h][e]=r[h][e]:(o[h][e]={},t(i[e],r[h][e],o[h][e],a+1)))}else D(i,!0)&&!i.nodeType?(o[h]=L(i)?[]:{},t(i,r[h]||{},o[h],a+1),0===Object.keys(o[h]).length&&("colorAxis"!==h||0!==a)&&delete o[h]):(e[h]!==r[h]||h in e&&!(h in r))&&"__proto__"!==h&&"constructor"!==h&&(o[h]=n[h])})}(t,e,r,0),r},discardElement:function(t){t?.parentElement?.removeChild(t)},erase:function(t,e){let i=t.length;for(;i--;)if(t[i]===e){t.splice(i,1);break}},error:O,extend:H,extendClass:function(t,e){let i=function(){};return i.prototype=new t,H(i.prototype,e),i},find:j,fireEvent:$,getAlignFactor:(t="")=>({center:.5,right:1,middle:.5,bottom:1})[t]||0,getClosestDistance:function(t,e){let i,s,r,o,a=!e;return t.forEach(t=>{if(t.length>1)for(o=s=t.length-1;o>0;o--)(r=t[o]-t[o-1])<0&&!a?(e?.(),e=void 0):r&&(void 0===i||r<i)&&(i=r)}),i},getMagnitude:G,getNestedProperty:function(t,e){let i=t.split(".");for(;i.length&&N(e);){let t=i.shift();if(void 0===t||"__proto__"===t)return;if("this"===t){let t;return D(e)&&(t=e["@this"]),t??e}let s=e[t.replace(/[\\'"]/g,"")];if(!N(s)||"function"==typeof s||"number"==typeof s.nodeType||s===C)return;e=s}return e},getStyle:function t(e,i,s){let r;if("width"===i){let i=Math.min(e.offsetWidth,e.scrollWidth),s=e.getBoundingClientRect?.().width;return s<i&&s>=i-1&&(i=Math.floor(s)),Math.max(0,i-(t(e,"padding-left",!0)||0)-(t(e,"padding-right",!0)||0))}if("height"===i)return Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-(t(e,"padding-top",!0)||0)-(t(e,"padding-bottom",!0)||0));let o=C.getComputedStyle(e,void 0);return o&&(r=o.getPropertyValue(i),X(s,"opacity"!==i)&&(r=P(r))),r},insertItem:function(t,e){let i,s=t.options.index,r=e.length;for(i=t.options.isInternal?r:0;i<r+1;i++)if(!e[i]||z(s)&&s<X(e[i].options.index,e[i]._i)||e[i].options.isInternal){e.splice(i,0,t);break}return i},isArray:L,isClass:B,isDOMElement:I,isFunction:function(t){return"function"==typeof t},isNumber:z,isObject:D,isString:E,merge:function(t,...e){let i,s=[t,...e],r={},o=function(t,e){return"object"!=typeof t&&(t={}),U(e,function(i,s){"__proto__"!==s&&"constructor"!==s&&(!D(i,!0)||B(i)||I(i)?t[s]=e[s]:t[s]=o(t[s]||{},i))}),t};!0===t&&(r=s[1],s=Array.prototype.slice.call(s,2));let a=s.length;for(i=0;i<a;i++)r=o(r,s[i]);return r},normalizeTickInterval:function(t,e,i,s,r){let o,a=t;i=X(i,G(t));let n=t/i;for(!e&&(e=r?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===s&&(1===i?e=e.filter(function(t){return t%1==0}):i<=.1&&(e=[1/i]))),o=0;o<e.length&&(a=e[o],(!r||!(a*i>=t))&&(r||!(n<=(e[o]+(e[o+1]||e[o]))/2)));o++);return Y(a*i,-Math.round(Math.log(.001)/Math.LN10))},objectEach:U,offset:function(t){let e=T.documentElement,i=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:i.top+(C.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(C.pageXOffset||e.scrollLeft)-(e.clientLeft||0),width:i.width,height:i.height}},pad:function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||"0")+t},pick:X,pInt:P,pushUnique:function(t,e){return 0>t.indexOf(e)&&!!t.push(e)},relativeLength:function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},removeEvent:V,replaceNested:function(t,...e){let i,s;do for(s of(i=t,e))t=t.replace(s[0],s[1]);while(t!==i);return t},splat:W,stableSort:function(t,e){let i,s,r=t.length;for(s=0;s<r;s++)t[s].safeI=s;for(t.sort(function(t,s){return 0===(i=e(t,s))?t.safeI-s.safeI:i}),s=0;s<r;s++)delete t[s].safeI},syncTimeout:function(t,e,i){return e>0?setTimeout(t,e,i):(t.call(0,i),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(t){return E(t)?t.substring(0,1).toUpperCase()+t.substring(1):String(t)},uniqueKey:Z,useSerialIds:function(e){return t=X(e,t)},wrap:function(t,e,i){let s=t[e];t[e]=function(){let t=arguments,e=this;return i.apply(this,[function(){return s.apply(e,arguments.length?arguments:t)}].concat([].slice.call(arguments)))}}},{pageLang:q,win:K}=S,{defined:J,error:Q,extend:tt,isNumber:te,isObject:ti,isString:ts,merge:tr,objectEach:to,pad:ta,splat:tn,timeUnits:th,ucfirst:tl}=_,td=S.isSafari&&K.Intl&&!K.Intl.DateTimeFormat.prototype.formatRange,tc=t=>void 0===t.main,tp=class{constructor(t,e){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=K.Date,this.update(t),this.lang=e}update(t={}){this.dTLCache={},this.options=t=tr(!0,this.options,t);let{timezoneOffset:e,useUTC:i}=t;this.Date=t.Date||K.Date||Date;let s=t.timezone;J(i)&&(s=i?"UTC":void 0),e&&e%60==0&&(s="Etc/GMT"+(e>0?"+":"")+e/60),this.variableTimezone="UTC"!==s&&s?.indexOf("Etc/GMT")!==0,this.timezone=s,["months","shortMonths","weekdays","shortWeekdays"].forEach(t=>{let e=/months/i.test(t),i=/short/.test(t),s={timeZone:"UTC"};s[e?"month":"weekday"]=i?"short":"long",this[t]=(e?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(t=>this.dateFormat(s,(e?31:1)*24*36e5*t))})}toParts(t){let[e,i,s,r,o,a,n]=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},t,"es").split(/(?:, | |\/|:)/g);return[r,+s-1,i,o,a,n,Math.floor(Number(t)||0)%1e3,"DLMXJVS".indexOf(e)].map(Number)}dateTimeFormat(t,e,i=this.options.locale||q){let s=JSON.stringify(t)+i;ts(t)&&(t=this.str2dtf(t));let r=this.dTLCache[s];if(!r){t.timeZone??(t.timeZone=this.timezone);try{r=new Intl.DateTimeFormat(i,t)}catch(e){/Invalid time zone/i.test(e.message)?(Q(34),t.timeZone="UTC",r=new Intl.DateTimeFormat(i,t)):Q(e.message,!1)}}return this.dTLCache[s]=r,r?.format(e)||""}str2dtf(t,e={}){let i={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(i).forEach(s=>{-1!==t.indexOf(s)&&tt(e,i[s])}),e}makeTime(t,e,i=1,s=0,r,o,a){let n=this.Date.UTC(t,e,i,s,r||0,o||0,a||0);if("UTC"!==this.timezone){let t=this.getTimezoneOffset(n);if(n+=t,-1!==[2,3,8,9,10,11].indexOf(e)&&(s<5||s>20)){let e=this.getTimezoneOffset(n);t!==e?n+=e-t:t-36e5!==this.getTimezoneOffset(n-36e5)||td||(n-=36e5)}}return n}parse(t){if(!ts(t))return t??void 0;let e=(t=t.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(t),i=/^[0-9]{4}-[0-9]{2}(-[0-9]{2}|)$/.test(t);e||i||(t+="Z");let s=Date.parse(t);if(te(s))return s+(!e||i?this.getTimezoneOffset(s):0)}getTimezoneOffset(t){if("UTC"!==this.timezone){let[e,i,s,r,o=0]=this.dateTimeFormat({timeZoneName:"shortOffset"},t,"en").split(/(GMT|:)/).map(Number),a=-(36e5*(s+o/60));if(te(a))return a}return 0}dateFormat(t,e,i){let s=this.lang;if(!J(e)||isNaN(e))return s?.invalidDate||"";if(ts(t=t??"%Y-%m-%d %H:%M:%S")){let i,r=/%\[([a-zA-Z]+)\]/g;for(;i=r.exec(t);)t=t.replace(i[0],this.dateTimeFormat(i[1],e,s?.locale))}if(ts(t)&&-1!==t.indexOf("%")){let i=this,[r,o,a,n,h,l,d,c]=this.toParts(e),p=s?.weekdays||this.weekdays,g=s?.shortWeekdays||this.shortWeekdays,u=s?.months||this.months,f=s?.shortMonths||this.shortMonths;to(tt({a:g?g[c]:p[c].substr(0,3),A:p[c],d:ta(a),e:ta(a,2," "),w:c,v:s?.weekFrom??"",b:f[o],B:u[o],m:ta(o+1),o:o+1,y:r.toString().substr(2,2),Y:r,H:ta(n),k:n,I:ta(n%12||12),l:n%12||12,M:ta(h),p:n<12?"AM":"PM",P:n<12?"am":"pm",S:ta(l),L:ta(d,3)},S.dateFormats),function(s,r){if(ts(t))for(;-1!==t.indexOf("%"+r);)t=t.replace("%"+r,"function"==typeof s?s.call(i,e):s)})}else if(ti(t)){let i=(this.getTimezoneOffset(e)||0)/36e5,s=this.timezone||"Etc/GMT"+(i>=0?"+":"")+i,{prefix:r="",suffix:o=""}=t;t=r+this.dateTimeFormat(tt({timeZone:s},t),e)+o}return i?tl(t):t}resolveDTLFormat(t){return ti(t,!0)?ti(t,!0)&&tc(t)?{main:t}:t:{main:(t=tn(t))[0],from:t[1],to:t[2]}}getDateFormat(t,e,i,s){let r=this.dateFormat("%m-%d %H:%M:%S.%L",e),o="01-01 00:00:00.000",a={millisecond:15,second:12,minute:9,hour:6,day:3},n="millisecond",h=n;for(n in th){if(t&&t===th.week&&+this.dateFormat("%w",e)===i&&r.substr(6)===o.substr(6)){n="week";break}if(t&&th[n]>t){n=h;break}if(a[n]&&r.substr(a[n])!==o.substr(a[n]))break;"week"!==n&&(h=n)}return this.resolveDTLFormat(s[n]).main}},{defined:tg,extend:tu,timeUnits:tf}=_,tm=class extends tp{getTimeTicks(t,e,i,s){let r=this,o=[],a={},{count:n=1,unitRange:h}=t,[l,d,c,p,g,u]=r.toParts(e),f=(e||0)%1e3,m;if(s??(s=1),tg(e)){if(f=h>=tf.second?0:n*Math.floor(f/n),h>=tf.second&&(u=h>=tf.minute?0:n*Math.floor(u/n)),h>=tf.minute&&(g=h>=tf.hour?0:n*Math.floor(g/n)),h>=tf.hour&&(p=h>=tf.day?0:n*Math.floor(p/n)),h>=tf.day&&(c=h>=tf.month?1:Math.max(1,n*Math.floor(c/n))),h>=tf.month&&(d=h>=tf.year?0:n*Math.floor(d/n)),h>=tf.year&&(l-=l%n),h===tf.week){n&&(e=r.makeTime(l,d,c,p,g,u,f));let t=this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},e,"es"),i="DLMXJVS".indexOf(t);c+=-i+s+(i<s?-7:0)}e=r.makeTime(l,d,c,p,g,u,f),r.variableTimezone&&tg(i)&&(m=i-e>4*tf.month||r.getTimezoneOffset(e)!==r.getTimezoneOffset(i));let t=e,x=1;for(;t<i;)o.push(t),h===tf.year?t=r.makeTime(l+x*n,0):h===tf.month?t=r.makeTime(l,d+x*n):m&&(h===tf.day||h===tf.week)?t=r.makeTime(l,d,c+x*n*(h===tf.day?1:7)):m&&h===tf.hour&&n>1?t=r.makeTime(l,d,c,p+x*n):t+=h*n,x++;o.push(t),h<=tf.hour&&o.length<1e4&&o.forEach(t=>{t%18e5==0&&"000000000"===r.dateFormat("%H%M%S%L",t)&&(a[t]="day")})}return o.info=tu(t,{higherRanks:a,totalRange:h*n}),o}},{isTouchDevice:tx}=S,{fireEvent:ty,merge:tb}=_,tv={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{weekFrom:"week from",chartTitle:"Chart title",locale:void 0,loading:"Loading...",months:void 0,seriesName:"Series {add index 1}",shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],pieSliceName:"Slice",resetZoom:"Reset zoom",yAxisTitle:"Values",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:t=>Math.sqrt(1-Math.pow(t-1,2))},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"%v %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,position:{x:0,y:3},shared:!1,snap:tx?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},tk=new tm(tv.time,tv.lang),tM={defaultOptions:tv,defaultTime:tk,getOptions:function(){return tv},setOptions:function(t){return ty(S,"setOptions",{options:t}),tb(!0,tv,t),t.time&&tk.update(tv.time),t.lang&&"locale"in t.lang&&tk.update({locale:t.lang.locale}),t.lang?.chartTitle&&(tv.title={...tv.title,text:t.lang.chartTitle}),tv}},{win:tw}=S,{isNumber:tS,isString:tA,merge:tT,pInt:tC,defined:tO}=_,tP=(t,e,i)=>`color-mix(in srgb,${t},${e} ${100*i}%)`,tE=t=>tA(t)&&!!t&&"none"!==t;class tL{static parse(t){return t?new tL(t):tL.None}constructor(t){let e,i,s,r;this.rgba=[NaN,NaN,NaN,NaN],this.input=t;let o=S.Color;if(o&&o!==tL)return new o(t);if("object"==typeof t&&void 0!==t.stops)this.stops=t.stops.map(t=>new tL(t[1]));else if("string"==typeof t)for(this.input=t=tL.names[t.toLowerCase()]||t,s=tL.parsers.length;s--&&!i;)(e=(r=tL.parsers[s]).regex.exec(t))&&(i=r.parse(e));i&&(this.rgba=i)}get(t){let e=this.input,i=this.rgba;if(this.output)return this.output;if("object"==typeof e&&void 0!==this.stops){let i=tT(e);return i.stops=[].slice.call(i.stops),this.stops.forEach((e,s)=>{i.stops[s]=[i.stops[s][0],e.get(t)]}),i}return i&&tS(i[0])?"rgb"!==t&&(t||1!==i[3])?"a"===t?`${i[3]}`:"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e}brighten(t){let e=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(tS(t)&&0!==t){if(tS(e[0]))for(let i=0;i<3;i++)e[i]+=tC(255*t),e[i]<0&&(e[i]=0),e[i]>255&&(e[i]=255);else tL.useColorMix&&tE(this.input)&&(this.output=tP(this.input,t>0?"white":"black",Math.abs(t)))}return this}setOpacity(t){return this.rgba[3]=t,this}tweenTo(t,e){let i=this.rgba,s=t.rgba;if(!tS(i[0])||!tS(s[0]))return tL.useColorMix&&tE(this.input)&&tE(t.input)&&e<.99?tP(this.input,t.input,e):t.input||"none";let r=1!==s[3]||1!==i[3],o=(t,s)=>t+(i[s]-t)*(1-e),a=s.slice(0,3).map(o).map(Math.round);return r&&a.push(o(s[3],3)),(r?"rgba(":"rgb(")+a.join(",")+")"}}tL.names={white:"#ffffff",black:"#000000"},tL.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(t){return[tC(t[1]),tC(t[2]),tC(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(t){return[tC(t[1]),tC(t[2]),tC(t[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(t){return[tC(t[1]+t[1],16),tC(t[2]+t[2],16),tC(t[3]+t[3],16),tO(t[4])?tC(t[4]+t[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(t){return[tC(t[1],16),tC(t[2],16),tC(t[3],16),tO(t[4])?tC(t[4],16)/255:1]}}],tL.useColorMix=tw.CSS?.supports("color","color-mix(in srgb,red,blue 9%)"),tL.None=new tL("");let{parse:tD}=tL,{win:tI}=S,{isNumber:tB,objectEach:tz}=_;class tN{constructor(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}dSetter(){let t=this.paths,e=t?.[0],i=t?.[1],s=this.now||0,r=[];if(1!==s&&e&&i){if(e.length===i.length&&s<1)for(let t=0;t<i.length;t++){let o=e[t],a=i[t],n=[];for(let t=0;t<a.length;t++){let e=o[t],i=a[t];tB(e)&&tB(i)&&("A"!==a[0]||4!==t&&5!==t)?n[t]=e+s*(i-e):n[t]=i}r.push(n)}else r=i}else r=this.toD||[];this.elem.attr("d",r,void 0,!0)}update(){let t=this.elem,e=this.prop,i=this.now,s=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,s&&s.call(t,i,this)}run(t,e,i){let s=this,r=s.options,o=function(t){return!o.stopped&&s.step(t)},a=tI.requestAnimationFrame||function(t){setTimeout(t,13)},n=function(){for(let t=0;t<tN.timers.length;t++)tN.timers[t]()||tN.timers.splice(t--,1);tN.timers.length&&a(n)};t!==e||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=t,this.end=e,this.unit=i,this.now=this.start,this.pos=0,o.elem=this.elem,o.prop=this.prop,o()&&1===tN.timers.push(o)&&a(n)):(delete r.curAnim[this.prop],r.complete&&0===Object.keys(r.curAnim).length&&r.complete.call(this.elem))}step(t){let e,i,s=+new Date,r=this.options,o=this.elem,a=r.complete,n=r.duration,h=r.curAnim;return o.attr&&!o.element?e=!1:t||s>=n+this.startTime?(this.now=this.end,this.pos=1,this.update(),h[this.prop]=!0,i=!0,tz(h,function(t){!0!==t&&(i=!1)}),i&&a&&a.call(o),e=!1):(this.pos=r.easing((s-this.startTime)/n),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e}initPath(t,e,i){let s=t.startX,r=t.endX,o=i.slice(),a=t.isArea,n=a?2:1,h=e&&i.length>e.length&&i.hasStackedCliffs,l,d,c,p,g=e?.slice();if(!g||h)return[o,o];function u(t,e){for(;t.length<d;){let i=t[0],s=e[d-t.length];if(s&&"M"===i[0]&&("C"===s[0]?t[0]=["C",i[1],i[2],i[1],i[2],i[1],i[2]]:t[0]=["L",i[1],i[2]]),t.unshift(i),a){let e=t.pop();t.push(t[t.length-1],e)}}}function f(t){for(;t.length<d;){let e=t[Math.floor(t.length/n)-1].slice();if("C"===e[0]&&(e[1]=e[5],e[2]=e[6]),a){let i=t[Math.floor(t.length/n)].slice();t.splice(t.length/2,0,e,i)}else t.push(e)}}if(s&&r&&r.length){for(c=0;c<s.length;c++){if(s[c]===r[0]){l=c;break}if(s[0]===r[r.length-s.length+c]){l=c,p=!0;break}if(s[s.length-1]===r[r.length-s.length+c]){l=s.length-c;break}}void 0===l&&(g=[])}return g.length&&tB(l)&&(d=o.length+l*n,p?(u(g,o),f(o)):(u(o,g),f(g))),[g,o]}fillSetter(){tN.prototype.strokeSetter.apply(this,arguments)}strokeSetter(){this.elem.attr(this.prop,tD(this.start).tweenTo(tD(this.end),this.pos),void 0,!0)}}tN.timers=[];let{defined:tR,getStyle:tW,isArray:tH,isNumber:tX,isObject:tF,merge:tG,objectEach:tY,pick:tj}=_;function tU(t){return tF(t)?tG({duration:500,defer:0},t):{duration:500*!!t,defer:0}}function tV(t,e){let i=tN.timers.length;for(;i--;)tN.timers[i].elem!==t||e&&e!==tN.timers[i].prop||(tN.timers[i].stopped=!0)}let t$={animate:function(t,e,i){let s,r="",o,a,n;tF(i)||(n=arguments,i={duration:n[2],easing:n[3],complete:n[4]}),tX(i.duration)||(i.duration=400),i.easing="function"==typeof i.easing?i.easing:Math[i.easing]||Math.easeInOutSine,i.curAnim=tG(e),tY(e,function(n,h){tV(t,h),a=new tN(t,i,h),o=void 0,"d"===h&&tH(e.d)?(a.paths=a.initPath(t,t.pathArray,e.d),a.toD=e.d,s=0,o=1):t.attr?s=t.attr(h):(s=parseFloat(tW(t,h))||0,"opacity"!==h&&(r="px")),o||(o=n),"string"==typeof o&&o.match("px")&&(o=o.replace(/px/g,"")),a.run(s,o,r)})},animObject:tU,getDeferredAnimation:function(t,e,i){let s=tU(e),r=i?[i]:t.series,o=0,a=0;return r.forEach(t=>{let i=tU(t.options.animation);o=tF(e)&&tR(e.defer)?s.defer:Math.max(o,i.duration+i.defer),a=Math.min(s.duration,i.duration)}),t.renderer.forExport&&(o=0),{defer:Math.max(0,o-a),duration:Math.min(o,a)}},setAnimation:function(t,e){e.renderer.globalAnimation=tj(t,e.options.chart.animation,!0)},stop:tV},{SVG_NS:tZ,win:t_}=S,{attr:tq,createElement:tK,css:tJ,error:tQ,isFunction:t0,isString:t1,objectEach:t2,splat:t3}=_,{trustedTypes:t5}=t_,t6=t5&&t0(t5.createPolicy)&&t5.createPolicy("highcharts",{createHTML:t=>t}),t9=t6?t6.createHTML(""):"";class t4{static filterUserAttributes(t){return t2(t,(e,i)=>{let s=!0;-1===t4.allowedAttributes.indexOf(i)&&(s=!1),-1!==["background","dynsrc","href","lowsrc","src"].indexOf(i)&&(s=t1(e)&&t4.allowedReferences.some(t=>0===e.indexOf(t))),s||(tQ(33,!1,void 0,{"Invalid attribute in config":`${i}`}),delete t[i]),t1(e)&&t[i]&&(t[i]=e.replace(/</g,"&lt;"))}),t}static parseStyle(t){return t.split(";").reduce((t,e)=>{let i=e.split(":").map(t=>t.trim()),s=i.shift();return s&&i.length&&(t[s.replace(/-([a-z])/g,t=>t[1].toUpperCase())]=i.join(":")),t},{})}static setElementHTML(t,e){t.innerHTML=t4.emptyHTML,e&&new t4(e).addToDOM(t)}constructor(t){this.nodes="string"==typeof t?this.parseMarkup(t):t}addToDOM(t){return function t(e,i){let s;return t3(e).forEach(function(e){let r,o=e.tagName,a=e.textContent?S.doc.createTextNode(e.textContent):void 0,n=t4.bypassHTMLFiltering;if(o){if("#text"===o)r=a;else if(-1!==t4.allowedTags.indexOf(o)||n){let s="svg"===o?tZ:i.namespaceURI||tZ,h=S.doc.createElementNS(s,o),l=e.attributes||{};t2(e,function(t,e){"tagName"!==e&&"attributes"!==e&&"children"!==e&&"style"!==e&&"textContent"!==e&&(l[e]=t)}),tq(h,n?l:t4.filterUserAttributes(l)),e.style&&tJ(h,e.style),a&&h.appendChild(a),t(e.children||[],h),r=h}else tQ(33,!1,void 0,{"Invalid tagName in config":o})}r&&i.appendChild(r),s=r}),s}(this.nodes,t)}parseMarkup(t){let e,i=[];t=t.trim().replace(/ style=(["'])/g," data-style=$1");try{e=new DOMParser().parseFromString(t6?t6.createHTML(t):t,"text/html")}catch(t){}if(!e){let i=tK("div");i.innerHTML=t,e={body:i}}let s=(t,e)=>{let i=t.nodeName.toLowerCase(),r={tagName:i};"#text"===i&&(r.textContent=t.textContent||"");let o=t.attributes;if(o){let t={};[].forEach.call(o,e=>{"data-style"===e.name?r.style=t4.parseStyle(e.value):t[e.name]=e.value}),r.attributes=t}if(t.childNodes.length){let e=[];[].forEach.call(t.childNodes,t=>{s(t,e)}),e.length&&(r.children=e)}e.push(r)};return[].forEach.call(e.body.childNodes,t=>s(t,i)),i}}t4.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],t4.allowedReferences=["https://","http://","mailto:","/","../","./","#"],t4.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],t4.emptyHTML=t9,t4.bypassHTMLFiltering=!1;let{defaultOptions:t8,defaultTime:t7}=tM,{pageLang:et}=S,{extend:ee,getNestedProperty:ei,isArray:es,isNumber:er,isObject:eo,isString:ea,pick:en,ucfirst:eh}=_,el={add:(t,e)=>t+e,divide:(t,e)=>0!==e?t/e:"",eq:(t,e)=>t==e,each:function(t){let e=arguments[arguments.length-1];return!!es(t)&&t.map((i,s)=>ep(e.body,ee(eo(i)?i:{"@this":i},{"@index":s,"@first":0===s,"@last":s===t.length-1}))).join("")},ge:(t,e)=>t>=e,gt:(t,e)=>t>e,if:t=>!!t,le:(t,e)=>t<=e,lt:(t,e)=>t<e,multiply:(t,e)=>t*e,ne:(t,e)=>t!=e,subtract:(t,e)=>t-e,ucfirst:eh,unless:t=>!t},ed={},ec=t=>/^["'].+["']$/.test(t);function ep(t="",e,i){let s=/\{([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/g,r=/\(([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/g,o=[],a=/f$/,n=/\.(\d)/,h=i?.options?.lang||t8.lang,l=i?.time||t7,d=i?.numberFormatter||eg,c=(t="")=>{let i;return"true"===t||"false"!==t&&((i=Number(t)).toString()===t?i:ec(t)?t.slice(1,-1):ei(t,e))},p,g,u=0,f;for(;null!==(p=s.exec(t));){let i=p,s=r.exec(p[1]);s&&(p=s,f=!0),g?.isBlock||(g={ctx:e,expression:p[1],find:p[0],isBlock:"#"===p[1].charAt(0),start:p.index,startInner:p.index+p[0].length,length:p[0].length});let a=(g.isBlock?i:p)[1].split(" ")[0].replace("#","");el[a]&&(g.isBlock&&a===g.fn&&u++,g.fn||(g.fn=a));let n="else"===p[1];if(g.isBlock&&g.fn&&(p[1]===`/${g.fn}`||n)){if(u)!n&&u--;else{let e=g.startInner,i=t.substr(e,p.index-e);void 0===g.body?(g.body=i,g.startInner=p.index+p[0].length):g.elseBody=i,g.find+=i+p[0],n||(o.push(g),g=void 0)}}else g.isBlock||o.push(g);if(s&&!g?.isBlock)break}return o.forEach(s=>{let o,p,{body:g,elseBody:u,expression:f,fn:m}=s;if(m){let t=[s],r=[],a=f.length,n=0,h;for(p=0;p<=a;p++){let t=f.charAt(p);h||'"'!==t&&"'"!==t?h===t&&(h=""):h=t,h||" "!==t&&p!==a||(r.push(f.substr(n,p-n)),n=p+1)}for(p=el[m].length;p--;)t.unshift(c(r[p+1]));o=el[m].apply(e,t),s.isBlock&&"boolean"==typeof o&&(o=ep(o?g:u,e,i))}else{let t=ec(f)?[f]:f.split(":");if(o=c(t.shift()||""),t.length&&"number"==typeof o){let e=t.join(":");if(a.test(e)){let t=parseInt((e.match(n)||["","-1"])[1],10);null!==o&&(o=d(o,t,h.decimalPoint,e.indexOf(",")>-1?h.thousandsSep:""))}else o=l.dateFormat(e,o)}r.lastIndex=0,r.test(s.find)&&ea(o)&&(o=`"${o}"`)}t=t.replace(s.find,en(o,""))}),f?ep(t,e,i):t}function eg(t,e,i,s){e*=1;let r,o,[a,n]=(t=+t||0).toString().split("e").map(Number),h=this?.options?.lang||t8.lang,l=(t.toString().split(".")[1]||"").split("e")[0].length,d=e,c={};i??(i=h.decimalPoint),s??(s=h.thousandsSep),-1===e?e=Math.min(l,20):er(e)?e&&n<0&&((o=e+n)>=0?(a=+a.toExponential(o).split("e")[0],e=o):(a=Math.floor(a),t=e<20?+(a*Math.pow(10,n)).toFixed(e):0,n=0)):e=2,n&&(e??(e=2),t=a),er(e)&&e>=0&&(c.minimumFractionDigits=e,c.maximumFractionDigits=e),""===s&&(c.useGrouping=!1);let p=s||i,g=p?"en":this?.locale||h.locale||et,u=JSON.stringify(c)+g;return r=(ed[u]??(ed[u]=new Intl.NumberFormat(g,c))).format(t),p&&(r=r.replace(/([,\.])/g,"_$1").replace(/_\,/g,s??",").replace("_.",i??".")),(e||0!=+r)&&(!(n<0)||d)||(r="0"),n&&0!=+r&&(r+="e"+(n<0?"":"+")+n),r}let eu={dateFormat:function(t,e,i){return t7.dateFormat(t,e,i)},format:ep,helpers:el,numberFormat:eg};!function(t){let e;t.rendererTypes={},t.getRendererType=function(i=e){return t.rendererTypes[i]||t.rendererTypes[e]},t.registerRendererType=function(i,s,r){t.rendererTypes[i]=s,(!e||r)&&(e=i,S.Renderer=s)}}(r||(r={}));let ef=r,{clamp:em,pick:ex,pushUnique:ey,stableSort:eb}=_;(o||(o={})).distribute=function t(e,i,s){let r=e,o=r.reducedLen||i,a=(t,e)=>t.target-e.target,n=[],h=e.length,l=[],d=n.push,c,p,g,u=!0,f,m,x=0,y;for(c=h;c--;)x+=e[c].size;if(x>o){for(eb(e,(t,e)=>(e.rank||0)-(t.rank||0)),g=(y=e[0].rank===e[e.length-1].rank)?h/2:-1,p=y?g:h-1;g&&x>o;)f=e[c=Math.floor(p)],ey(l,c)&&(x-=f.size),p+=g,y&&p>=e.length&&(g/=2,p=g);l.sort((t,e)=>e-t).forEach(t=>d.apply(n,e.splice(t,1)))}for(eb(e,a),e=e.map(t=>({size:t.size,targets:[t.target],align:ex(t.align,.5)}));u;){for(c=e.length;c--;)f=e[c],m=(Math.min.apply(0,f.targets)+Math.max.apply(0,f.targets))/2,f.pos=em(m-f.size*f.align,0,i-f.size);for(c=e.length,u=!1;c--;)c>0&&e[c-1].pos+e[c-1].size>e[c].pos&&(e[c-1].size+=e[c].size,e[c-1].targets=e[c-1].targets.concat(e[c].targets),e[c-1].align=.5,e[c-1].pos+e[c-1].size>i&&(e[c-1].pos=i-e[c-1].size),e.splice(c,1),u=!0)}return d.apply(r,n),c=0,e.some(e=>{let o=0;return(e.targets||[]).some(()=>(r[c].pos=e.pos+o,void 0!==s&&Math.abs(r[c].pos-r[c].target)>s)?(r.slice(0,c+1).forEach(t=>delete t.pos),r.reducedLen=(r.reducedLen||i)-.1*i,r.reducedLen>.1*i&&t(r,i,s),!0):(o+=r[c].size,c++,!1))}),eb(r,a),r};let ev=o,{animate:ek,animObject:eM,stop:ew}=t$,{deg2rad:eS,doc:eA,svg:eT,SVG_NS:eC,win:eO,isFirefox:eP}=S,{addEvent:eE,attr:eL,createElement:eD,crisp:eI,css:eB,defined:ez,erase:eN,extend:eR,fireEvent:eW,getAlignFactor:eH,isArray:eX,isFunction:eF,isNumber:eG,isObject:eY,isString:ej,merge:eU,objectEach:eV,pick:e$,pInt:eZ,pushUnique:e_,replaceNested:eq,syncTimeout:eK,uniqueKey:eJ}=_;class eQ{_defaultGetter(t){let e=e$(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e}_defaultSetter(t,e,i){i.setAttribute(e,t)}add(t){let e,i=this.renderer,s=this.element;return t&&(this.parentGroup=t),void 0!==this.textStr&&"text"===this.element.nodeName&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(s),this.onAdd&&this.onAdd(),this}addClass(t,e){let i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(t,e){return -1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this}afterSetters(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)}align(t,e,i,s=!0){let r=this.renderer,o=r.alignedObjects,a=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);let n=!i||ej(i)?i||"renderer":void 0;n&&(a&&e_(o,this),i=void 0);let h=e$(i,r[n],r),l=(h.x||0)+(t.x||0)+((h.width||0)-(t.width||0))*eH(t.align),d=(h.y||0)+(t.y||0)+((h.height||0)-(t.height||0))*eH(t.verticalAlign),c={"text-align":t?.align};return c[e?"translateX":"x"]=Math.round(l),c[e?"translateY":"y"]=Math.round(d),s&&(this[this.placed?"animate":"attr"](c),this.placed=!0),this.alignAttr=c,this}alignSetter(t){let e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))}animate(t,e,i){let s=eM(e$(e,this.renderer.globalAnimation,!0)),r=s.defer;return eA.hidden&&(s.duration=0),0!==s.duration?(i&&(s.complete=i),eK(()=>{this.element&&ek(this,t,s)},r)):(this.attr(t,void 0,i||s.complete),eV(t,function(t,e){s.step&&s.step.call(this,t,{prop:e,pos:1,elem:this})},this)),this}applyTextOutline(t){let e=this.element;-1!==t.indexOf("contrast")&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));let i=t.indexOf(" "),s=t.substring(i+1),r=t.substring(0,i);if(r&&"none"!==r&&S.svg){this.fakeTS=!0,r=r.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*Number(e)+i}),this.removeTextOutline();let t=eA.createElementNS(eC,"tspan");eL(t,{class:"highcharts-text-outline",fill:s,stroke:s,"stroke-width":r,"stroke-linejoin":"round"});let i=e.querySelector("textPath")||e;[].forEach.call(i.childNodes,e=>{let i=e.cloneNode(!0);i.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(t=>i.removeAttribute(t)),t.appendChild(i)});let o=0;[].forEach.call(i.querySelectorAll("text tspan"),t=>{o+=Number(t.getAttribute("dy"))});let a=eA.createElementNS(eC,"tspan");a.textContent="​",eL(a,{x:Number(e.getAttribute("x")),dy:-o}),t.appendChild(a),i.insertBefore(t,i.firstChild)}}attr(t,e,i,s){let{element:r}=this,o=eQ.symbolCustomAttribs,a,n,h=this,l;return"string"==typeof t&&void 0!==e&&(a=t,(t={})[a]=e),"string"==typeof t?h=(this[t+"Getter"]||this._defaultGetter).call(this,t,r):(eV(t,function(e,i){l=!1,s||ew(this,i),this.symbolName&&-1!==o.indexOf(i)&&(n||(this.symbolAttr(t),n=!0),l=!0),this.rotation&&("x"===i||"y"===i)&&(this.doTransform=!0),l||(this[i+"Setter"]||this._defaultSetter).call(this,e,i,r)},this),this.afterSetters()),i&&i.call(this),h}clip(t){if(t&&!t.clipPath){let e=eJ()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);eR(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?`url(${this.renderer.url}#${t.id})`:"none")}crisp(t,e){e=Math.round(e||t.strokeWidth||0);let i=t.x||this.x||0,s=t.y||this.y||0,r=(t.width||this.width||0)+i,o=(t.height||this.height||0)+s,a=eI(i,e),n=eI(s,e);return eR(t,{x:a,y:n,width:eI(r,e)-a,height:eI(o,e)-n}),ez(t.strokeWidth)&&(t.strokeWidth=e),t}complexColor(t,e,i){let s=this.renderer,r,o,a,n,h,l,d,c,p,g,u=[],f;eW(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?o="radialGradient":t.linearGradient&&(o="linearGradient"),o){if(a=t[o],h=s.gradients,l=t.stops,p=i.radialReference,eX(a)&&(t[o]=a={x1:a[0],y1:a[1],x2:a[2],y2:a[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===o&&p&&!ez(a.gradientUnits)&&(n=a,a=eU(a,s.getRadialAttr(p,n),{gradientUnits:"userSpaceOnUse"})),eV(a,function(t,e){"id"!==e&&u.push(e,t)}),eV(l,function(t){u.push(t)}),h[u=u.join(",")])g=h[u].attr("id");else{a.id=g=eJ();let t=h[u]=s.createElement(o).attr(a).add(s.defs);t.radAttr=n,t.stops=[],l.forEach(function(e){0===e[1].indexOf("rgba")?(d=(r=tL.parse(e[1])).get("rgb"),c=r.get("a")):(d=e[1],c=1);let i=s.createElement("stop").attr({offset:e[0],"stop-color":d,"stop-opacity":c}).add(t);t.stops.push(i)})}f="url("+s.url+"#"+g+")",i.setAttribute(e,f),i.gradient=u,t.toString=function(){return f}}})}css(t){let e=this.styles,i={},s=this.element,r,o=!e;if(e&&eV(t,function(t,s){e&&e[s]!==t&&(i[s]=t,o=!0)}),o){e&&(t=eR(e,i)),null===t.width||"auto"===t.width?delete this.textWidth:"text"===s.nodeName.toLowerCase()&&t.width&&(r=this.textWidth=eZ(t.width)),eR(this.styles,t),r&&!eT&&this.renderer.forExport&&delete t.width;let o=eP&&t.fontSize||null;o&&(eG(o)||/^\d+$/.test(o))&&(t.fontSize+="px");let a=eU(t);s.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(t=>a&&delete a[t]),a.color&&(a.fill=a.color,delete a.color)),eB(s,a)}return this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this}dashstyleSetter(t){let e,i=this["stroke-width"];if("inherit"===i&&(i=1),t){let s=(t=t.toLowerCase()).replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=s.length;e--;)s[e]=""+eZ(s[e])*e$(i,NaN);t=s.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}}destroy(){let t=this,e=t.element||{},i=t.renderer,s=e.ownerSVGElement,r="SPAN"===e.nodeName&&t.parentGroup||void 0,o,a;if(e.onclick=e.onmouseout=e.onmouseover=e.onmousemove=e.point=null,ew(t),t.clipPath&&s){let e=t.clipPath;[].forEach.call(s.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){t.getAttribute("clip-path").indexOf(e.element.id)>-1&&t.removeAttribute("clip-path")}),t.clipPath=e.destroy()}if(t.stops){for(a=0;a<t.stops.length;a++)t.stops[a].destroy();t.stops.length=0,t.stops=void 0}for(t.safeRemoveChild(e);r?.div&&0===r.div.childNodes.length;)o=r.parentGroup,t.safeRemoveChild(r.div),delete r.div,r=o;t.alignOptions&&eN(i.alignedObjects,t),eV(t,(e,i)=>{(t[i]?.parentGroup===t||-1!==["connector","foreignObject"].indexOf(i))&&t[i]?.destroy?.(),delete t[i]})}dSetter(t,e,i){eX(t)&&("string"==typeof t[0]&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce((t,e,i)=>e?.join?(i?t+" ":"")+e.join(" "):(e||"").toString(),"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)}fillSetter(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)}hrefSetter(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)}getBBox(t,e){let i,s,r,o,{alignValue:a,element:n,renderer:h,styles:l,textStr:d}=this,{cache:c,cacheKeys:p}=h,g=n.namespaceURI===this.SVG_NS,u=e$(e,this.rotation,0),f=h.styledMode?n&&eQ.prototype.getStyle.call(n,"font-size"):l.fontSize;if(ez(d)&&(-1===(o=d.toString()).indexOf("<")&&(o=o.replace(/\d/g,"0")),o+=["",h.rootFontSize,f,u,this.textWidth,a,l.lineClamp,l.textOverflow,l.fontWeight].join(",")),o&&!t&&(i=c[o]),!i||i.polygon){if(g||h.forExport){try{r=this.fakeTS&&function(t){let e=n.querySelector(".highcharts-text-outline");e&&eB(e,{display:t})},eF(r)&&r("none"),i=n.getBBox?eR({},n.getBBox()):{width:n.offsetWidth,height:n.offsetHeight,x:0,y:0},eF(r)&&r("")}catch(t){}(!i||i.width<0)&&(i={x:0,y:0,width:0,height:0})}else i=this.htmlGetBBox();s=i.height,g&&(i.height=s=({"11px,17":14,"13px,20":16})[`${f||""},${Math.round(s)}`]||s),u&&(i=this.getRotatedBox(i,u));let t={bBox:i};eW(this,"afterGetBBox",t),i=t.bBox}if(o&&(""===d||i.height>0)){for(;p.length>250;)delete c[p.shift()];c[o]||p.push(o),c[o]=i}return i}getRotatedBox(t,e){let{x:i,y:s,width:r,height:o}=t,{alignValue:a,translateY:n,rotationOriginX:h=0,rotationOriginY:l=0}=this,d=eH(a),c=Number(this.element.getAttribute("y")||0)-(n?0:s),p=e*eS,g=(e-90)*eS,u=Math.cos(p),f=Math.sin(p),m=r*u,x=r*f,y=Math.cos(g),b=Math.sin(g),[[v,k],[M,w]]=[h,l].map(t=>[t-t*u,t*f]),S=i+d*(r-m)+v+w+c*y,A=S+m,T=A-o*y,C=T-m,O=s+c-d*x-k+M+c*b,P=O+x,E=P-o*b,L=E-x,D=Math.min(S,A,T,C),I=Math.min(O,P,E,L),B=Math.max(S,A,T,C)-D,z=Math.max(O,P,E,L)-I;return{x:D,y:I,width:B,height:z,polygon:[[S,O],[A,P],[T,E],[C,L]]}}getStyle(t){return eO.getComputedStyle(this.element||this,"").getPropertyValue(t)}hasClass(t){return -1!==(""+this.attr("class")).split(" ").indexOf(t)}hide(){return this.attr({visibility:"hidden"})}htmlGetBBox(){return{height:0,width:0,x:0,y:0}}constructor(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=eC,this.element="span"===e||"body"===e?eD(e):eA.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},eW(this,"afterInit")}on(t,e){let{onEvents:i}=this;return i[t]&&i[t](),i[t]=eE(this.element,t,e),this}opacitySetter(t,e,i){let s=Number(Number(t).toFixed(3));this.opacity=s,i.setAttribute(e,s)}reAlign(){this.alignOptions?.width&&"left"!==this.alignOptions.align&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())}removeClass(t){return this.attr("class",(""+this.attr("class")).replace(ej(t)?RegExp(`(^| )${t}( |$)`):t," ").replace(/ +/g," ").trim())}removeTextOutline(){let t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)}safeRemoveChild(t){let e=t.parentNode;e&&e.removeChild(t)}setRadialReference(t){let e=this.element.gradient&&this.renderer.gradients[this.element.gradient]||void 0;return this.element.radialReference=t,e?.radAttr&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this}shadow(t){let{renderer:e}=this,i=eU(this.parentGroup?.rotation===90?{offsetX:-1,offsetY:-1}:{},eY(t)?t:{}),s=e.shadowDefinition(i);return this.attr({filter:t?`url(${e.url}#${s})`:"none"})}show(t=!0){return this.attr({visibility:t?"inherit":"visible"})}"stroke-widthSetter"(t,e,i){this[e]=t,i.setAttribute(e,t)}strokeWidth(){if(!this.renderer.styledMode)return this["stroke-width"]||0;let t=this.getStyle("stroke-width"),e=0,i;return/px$/.test(t)?e=eZ(t):""!==t&&(eL(i=eA.createElementNS(eC,"rect"),{width:t,"stroke-width":0}),this.element.parentNode.appendChild(i),e=i.getBBox().width,i.parentNode.removeChild(i)),e}symbolAttr(t){let e=this;eQ.symbolCustomAttribs.forEach(function(i){e[i]=e$(t[i],e[i])}),e.attr({d:e.renderer.symbols[e.symbolName](e.x,e.y,e.width,e.height,e)})}textSetter(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())}titleSetter(t){let e=this.element,i=e.getElementsByTagName("title")[0]||eA.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=eq(e$(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")}toFront(){let t=this.element;return t.parentNode.appendChild(t),this}translate(t,e){return this.attr({translateX:t,translateY:e})}updateTransform(t="transform"){let{element:e,foreignObject:i,matrix:s,padding:r,rotation:o=0,rotationOriginX:a,rotationOriginY:n,scaleX:h,scaleY:l,text:d,translateX:c=0,translateY:p=0}=this,g=["translate("+c+","+p+")"];ez(s)&&g.push("matrix("+s.join(",")+")"),o&&(g.push("rotate("+o+" "+(a??e.getAttribute("x")??this.x??0)+" "+(n??e.getAttribute("y")??this.y??0)+")"),d?.element.tagName!=="SPAN"||d?.foreignObject||d.attr({rotation:o,rotationOriginX:(a||0)-r,rotationOriginY:(n||0)-r})),(ez(h)||ez(l))&&g.push("scale("+e$(h,1)+" "+e$(l,1)+")"),g.length&&!(d||this).textPath&&(i?.element||e).setAttribute(t,g.join(" "))}visibilitySetter(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t}xGetter(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)}zIndexSetter(t,e){let i=this.renderer,s=this.parentGroup,r=(s||i).element||i.box,o=this.element,a=r===i.box,n,h,l,d=!1,c,p=this.added,g;if(ez(t)?(o.setAttribute("data-z-index",t),t*=1,this[e]===t&&(p=!1)):ez(this[e])&&o.removeAttribute("data-z-index"),this[e]=t,p){for((t=this.zIndex)&&s&&(s.handleZ=!0),g=(n=r.childNodes).length-1;g>=0&&!d;g--)c=!ez(l=(h=n[g]).getAttribute("data-z-index")),h!==o&&(t<0&&c&&!a&&!g?(r.insertBefore(o,n[g]),d=!0):(eZ(l)<=t||c&&(!ez(t)||t>=0))&&(r.insertBefore(o,n[g+1]),d=!0));d||(r.insertBefore(o,n[3*!!a]),d=!0)}return d}}eQ.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],eQ.prototype.strokeSetter=eQ.prototype.fillSetter,eQ.prototype.yGetter=eQ.prototype.xGetter,eQ.prototype.matrixSetter=eQ.prototype.rotationOriginXSetter=eQ.prototype.rotationOriginYSetter=eQ.prototype.rotationSetter=eQ.prototype.scaleXSetter=eQ.prototype.scaleYSetter=eQ.prototype.translateXSetter=eQ.prototype.translateYSetter=eQ.prototype.verticalAlignSetter=function(t,e){this[e]=t,this.doTransform=!0};let e0=eQ,{defined:e1,extend:e2,getAlignFactor:e3,isNumber:e5,merge:e6,pick:e9,removeEvent:e4}=_;class e8 extends e0{constructor(t,e,i,s,r,o,a,n,h,l){let d;super(t,"g"),this.paddingLeftSetter=this.paddingSetter,this.paddingRightSetter=this.paddingSetter,this.doUpdate=!1,this.textStr=e,this.x=i,this.y=s,this.anchorX=o,this.anchorY=a,this.baseline=h,this.className=l,this.addClass("button"===l?"highcharts-no-tooltip":"highcharts-label"),l&&this.addClass("highcharts-"+l),this.text=t.text(void 0,0,0,n).attr({zIndex:1}),"string"==typeof r&&((d=/^url\((.*?)\)$/.test(r))||this.renderer.symbols[r])&&(this.symbolKey=r),this.bBox=e8.emptyBBox,this.padding=3,this.baselineOffset=0,this.needsBox=t.styledMode||d,this.deferredAttr={},this.alignFactor=0}alignSetter(t){let e=e3(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&e5(this.xSetting)&&this.attr({x:this.xSetting}))}anchorXSetter(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)}anchorYSetter(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)}boxAttr(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e}css(t){if(t){let e={};t=e6(t),e8.textProps.forEach(i=>{void 0!==t[i]&&(e[i]=t[i],delete t[i])}),this.text.css(e),"fontSize"in e||"fontWeight"in e?this.updateTextPadding():("width"in e||"textOverflow"in e)&&this.updateBoxSize()}return e0.prototype.css.call(this,t)}destroy(){e4(this.element,"mouseenter"),e4(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),e0.prototype.destroy.call(this)}fillSetter(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)}getBBox(t,e){this.textStr&&0===this.bBox.width&&0===this.bBox.height&&this.updateBoxSize();let{padding:i,height:s=0,translateX:r=0,translateY:o=0,width:a=0}=this,n=e9(this.paddingLeft,i),h=e??(this.rotation||0),l={width:a,height:s,x:r+this.bBox.x-n,y:o+this.bBox.y-i+this.baselineOffset};return h&&(l=this.getRotatedBox(l,h)),l}getCrispAdjust(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2}heightSetter(t){this.heightSetting=t,this.doUpdate=!0}afterSetters(){super.afterSetters(),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)}onAdd(){this.text.add(this),this.attr({text:e9(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&e1(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})}paddingSetter(t,e){e5(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0}rSetter(t,e){this.boxAttr(e,t)}strokeSetter(t,e){this.stroke=t,this.boxAttr(e,t)}"stroke-widthSetter"(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)}"text-alignSetter"(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()}textSetter(t){void 0!==t&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()}updateBoxSize(){let t,e=this.text,i={},s=this.padding,r=this.bBox=(!e5(this.widthSetting)||!e5(this.heightSetting)||this.textAlign)&&e1(e.textStr)?e.getBBox(void 0,0):e8.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||r.height||0)+2*s;let o=this.renderer.fontMetrics(e);if(this.baselineOffset=s+Math.min((this.text.firstLineMetrics||o).b,r.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-o.h)/2),this.needsBox&&!e.textPath){if(!this.box){let t=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();t.addClass(("button"===this.className?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),t.add(this)}i.x=t=this.getCrispAdjust(),i.y=(this.baseline?-this.baselineOffset:0)+t,i.width=Math.round(this.width),i.height=Math.round(this.height),this.box.attr(e2(i,this.deferredAttr)),this.deferredAttr={}}}updateTextPadding(){let t=this.text,e=t.styles.textAlign||this.textAlign;if(!t.textPath){this.updateBoxSize();let i=this.baseline?0:this.baselineOffset,s=(this.paddingLeft??this.padding)+e3(e)*(this.widthSetting??this.bBox.width);(s!==t.x||i!==t.y)&&(t.attr({align:e,x:s}),void 0!==i&&t.attr("y",i)),t.x=s,t.y=i}}widthSetter(t){this.widthSetting=e5(t)?t:void 0,this.doUpdate=!0}getPaddedWidth(){let t=this.padding,e=e9(this.paddingLeft,t),i=e9(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i}xSetter(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)}ySetter(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)}}e8.emptyBBox={width:0,height:0,x:0,y:0},e8.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"];let{defined:e7,isNumber:it,pick:ie}=_;function ii(t,e,i,s,r){let o=[];if(r){let a=r.start||0,n=r.end||0,h=ie(r.r,i),l=ie(r.r,s||i),d=2e-4/(r.borderRadius?1:Math.max(h,1)),c=Math.abs(n-a-2*Math.PI)<d;c&&(a=Math.PI/2,n=2.5*Math.PI-d);let p=r.innerR,g=ie(r.open,c),u=Math.cos(a),f=Math.sin(a),m=Math.cos(n),x=Math.sin(n),y=ie(r.longArc,n-a-Math.PI<d?0:1),b=["A",h,l,0,y,ie(r.clockwise,1),t+h*m,e+l*x];b.params={start:a,end:n,cx:t,cy:e},o.push(["M",t+h*u,e+l*f],b),e7(p)&&((b=["A",p,p,0,y,e7(r.clockwise)?1-r.clockwise:0,t+p*u,e+p*f]).params={start:n,end:a,cx:t,cy:e},o.push(g?["M",t+p*m,e+p*x]:["L",t+p*m,e+p*x],b)),g||o.push(["Z"])}return o}function is(t,e,i,s,r){return r?.r?ir(t,e,i,s,r):[["M",t,e],["L",t+i,e],["L",t+i,e+s],["L",t,e+s],["Z"]]}function ir(t,e,i,s,r){let o=r?.r||0;return[["M",t+o,e],["L",t+i-o,e],["A",o,o,0,0,1,t+i,e+o],["L",t+i,e+s-o],["A",o,o,0,0,1,t+i-o,e+s],["L",t+o,e+s],["A",o,o,0,0,1,t,e+s-o],["L",t,e+o],["A",o,o,0,0,1,t+o,e],["Z"]]}let io={arc:ii,callout:function(t,e,i,s,r){let o=Math.min(r?.r||0,i,s),a=o+6,n=r?.anchorX,h=r?.anchorY||0,l=ir(t,e,i,s,{r:o});if(!it(n)||n<i&&n>0&&h<s&&h>0)return l;if(t+n>i-a){if(h>e+a&&h<e+s-a)l.splice(3,1,["L",t+i,h-6],["L",t+i+6,h],["L",t+i,h+6],["L",t+i,e+s-o]);else if(n<i){let r=h<e+a,d=r?e:e+s;l.splice(r?2:5,0,["L",n,h],["L",t+i-o,d])}else l.splice(3,1,["L",t+i,s/2],["L",n,h],["L",t+i,s/2],["L",t+i,e+s-o])}else if(t+n<a){if(h>e+a&&h<e+s-a)l.splice(7,1,["L",t,h+6],["L",t-6,h],["L",t,h-6],["L",t,e+o]);else if(n>0){let i=h<e+a,r=i?e:e+s;l.splice(i?1:6,0,["L",n,h],["L",t+o,r])}else l.splice(7,1,["L",t,s/2],["L",n,h],["L",t,s/2],["L",t,e+o])}else h>s&&n<i-a?l.splice(5,1,["L",n+6,e+s],["L",n,e+s+6],["L",n-6,e+s],["L",t+o,e+s]):h<0&&n>a&&l.splice(1,1,["L",n-6,e],["L",n,e-6],["L",n+6,e],["L",i-o,e]);return l},circle:function(t,e,i,s){return ii(t+i/2,e+s/2,i/2,s/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(t,e,i,s){return[["M",t+i/2,e],["L",t+i,e+s/2],["L",t+i/2,e+s],["L",t,e+s/2],["Z"]]},rect:is,roundedRect:ir,square:is,triangle:function(t,e,i,s){return[["M",t+i/2,e],["L",t+i,e+s],["L",t,e+s],["Z"]]},"triangle-down":function(t,e,i,s){return[["M",t,e],["L",t+i,e],["L",t+i/2,e+s],["Z"]]}},{doc:ia,SVG_NS:ih,win:il}=S,{attr:id,extend:ic,fireEvent:ip,isString:ig,objectEach:iu,pick:im}=_,ix=(t,e)=>t.substring(0,e)+"…",iy=class{constructor(t){let e=t.styles;this.renderer=t.renderer,this.svgElement=t,this.width=t.textWidth,this.textLineHeight=e?.lineHeight,this.textOutline=e?.textOutline,this.ellipsis=e?.textOverflow==="ellipsis",this.lineClamp=e?.lineClamp,this.noWrap=e?.whiteSpace==="nowrap"}buildSVG(){let t=this.svgElement,e=t.element,i=t.renderer,s=im(t.textStr,"").toString(),r=-1!==s.indexOf("<"),o=e.childNodes,a=!t.added&&i.box,n=[s,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,t.getStyle("font-size"),t.styles.lineClamp,this.width].join(",");if(n!==t.textCache){t.textCache=n,delete t.actualWidth;for(let t=o.length;t--;)e.removeChild(o[t]);if(r||this.ellipsis||this.width||t.textPath||-1!==s.indexOf(" ")&&(!this.noWrap||/<br.*?>/g.test(s))){if(""!==s){a&&a.appendChild(e);let i=new t4(s);this.modifyTree(i.nodes),i.addToDOM(e),this.modifyDOM(),this.ellipsis&&-1!==(e.textContent||"").indexOf("…")&&t.attr("title",this.unescapeEntities(t.textStr||"",["&lt;","&gt;"])),a&&a.removeChild(e)}}else e.appendChild(ia.createTextNode(this.unescapeEntities(s)));ig(this.textOutline)&&t.applyTextOutline&&t.applyTextOutline(this.textOutline)}}modifyDOM(){let t,e=this.svgElement,i=id(e.element,"x");for(e.firstLineMetrics=void 0;t=e.element.firstChild;)if(/^[\s\u200B]*$/.test(t.textContent||" "))e.element.removeChild(t);else break;[].forEach.call(e.element.querySelectorAll("tspan.highcharts-br"),(t,s)=>{t.nextSibling&&t.previousSibling&&(0===s&&1===t.previousSibling.nodeType&&(e.firstLineMetrics=e.renderer.fontMetrics(t.previousSibling)),id(t,{dy:this.getLineHeight(t.nextSibling),x:i}))});let s=this.width||0;if(!s)return;let r=(t,r)=>{let o=t.textContent||"",a=o.replace(/([^\^])-/g,"$1- ").split(" "),n=!this.noWrap&&(a.length>1||e.element.childNodes.length>1),h=this.getLineHeight(r),l=Math.max(0,s-.8*h),d=0,c=e.actualWidth;if(n){let o=[],n=[];for(;r.firstChild&&r.firstChild!==t;)n.push(r.firstChild),r.removeChild(r.firstChild);for(;a.length;)if(a.length&&!this.noWrap&&d>0&&(o.push(t.textContent||""),t.textContent=a.join(" ").replace(/- /g,"-")),this.truncate(t,void 0,a,0===d&&c||0,s,l,(t,e)=>a.slice(0,e).join(" ").replace(/- /g,"-")),c=e.actualWidth,d++,this.lineClamp&&d>=this.lineClamp){a.length&&(this.truncate(t,t.textContent||"",void 0,0,s,l,ix),t.textContent=t.textContent?.replace("…","")+"…");break}n.forEach(e=>{r.insertBefore(e,t)}),o.forEach(e=>{r.insertBefore(ia.createTextNode(e),t);let s=ia.createElementNS(ih,"tspan");s.textContent="​",id(s,{dy:h,x:i}),r.insertBefore(s,t)})}else this.ellipsis&&o&&this.truncate(t,o,void 0,0,s,l,ix)},o=t=>{[].slice.call(t.childNodes).forEach(i=>{i.nodeType===il.Node.TEXT_NODE?r(i,t):(-1!==i.className.baseVal.indexOf("highcharts-br")&&(e.actualWidth=0),o(i))})};o(e.element)}getLineHeight(t){let e=t.nodeType===il.Node.TEXT_NODE?t.parentElement:t;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(e||this.svgElement.element).h}modifyTree(t){let e=(i,s)=>{let{attributes:r={},children:o,style:a={},tagName:n}=i,h=this.renderer.styledMode;if("b"===n||"strong"===n?h?r.class="highcharts-strong":a.fontWeight="bold":("i"===n||"em"===n)&&(h?r.class="highcharts-emphasized":a.fontStyle="italic"),a?.color&&(a.fill=a.color),"br"===n){r.class="highcharts-br",i.textContent="​";let e=t[s+1];e?.textContent&&(e.textContent=e.textContent.replace(/^ +/gm,""))}else"a"===n&&o&&o.some(t=>"#text"===t.tagName)&&(i.children=[{children:o,tagName:"tspan"}]);"#text"!==n&&"a"!==n&&(i.tagName="tspan"),ic(i,{attributes:r,style:a}),o&&o.filter(t=>"#text"!==t.tagName).forEach(e)};t.forEach(e),ip(this.svgElement,"afterModifyTree",{nodes:t})}truncate(t,e,i,s,r,o,a){let n,h,l=this.svgElement,{rotation:d}=l,c=[],p=i&&!s?1:0,g=(e||i||"").length,u=g;i||(r=o);let f=function(e,r){let o=r||e,a=t.parentNode;if(a&&void 0===c[o]&&a.getSubStringLength)try{c[o]=s+a.getSubStringLength(0,i?o+1:o)}catch(t){}return c[o]};if(l.rotation=0,s+(h=f(t.textContent.length))>r){for(;p<=g;)u=Math.ceil((p+g)/2),i&&(n=a(i,u)),h=f(u,n&&n.length-1),p===g?p=g+1:h>r?g=u-1:p=u;0===g?t.textContent="":e&&g===e.length-1||(t.textContent=n||a(e||i,u)),this.ellipsis&&h>r&&this.truncate(t,t.textContent||"",void 0,0,r,o,ix)}i&&i.splice(0,u),l.actualWidth=h,l.rotation=d}unescapeEntities(t,e){return iu(this.renderer.escapes,function(i,s){e&&-1!==e.indexOf(i)||(t=t.toString().replace(RegExp(i,"g"),s))}),t}},{defaultOptions:ib}=tM,{charts:iv,deg2rad:ik,doc:iM,isFirefox:iw,isMS:iS,isWebKit:iA,noop:iT,SVG_NS:iC,symbolSizes:iO,win:iP}=S,{addEvent:iE,attr:iL,createElement:iD,crisp:iI,css:iB,defined:iz,destroyObjectProperties:iN,extend:iR,isArray:iW,isNumber:iH,isObject:iX,isString:iF,merge:iG,pick:iY,pInt:ij,replaceNested:iU,uniqueKey:iV}=_;class i${constructor(t,e,i,s,r,o,a){let n,h;this.x=0,this.y=0;let l=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),d=l.element;a||l.css(this.getStyle(s||{})),t.appendChild(d),iL(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&iL(d,"xmlns",this.SVG_NS),this.box=d,this.boxWrapper=l,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(iM.createTextNode("Created with Highcharts 12.2.0")),this.defs=this.createElement("defs").add(),this.allowHTML=o,this.forExport=r,this.styledMode=a,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=l.getStyle("font-size"),this.setSize(e,i,!1),iw&&t.getBoundingClientRect&&((n=function(){iB(t,{left:0,top:0}),h=t.getBoundingClientRect(),iB(t,{left:Math.ceil(h.left)-h.left+"px",top:Math.ceil(h.top)-h.top+"px"})})(),this.unSubPixelFix=iE(iP,"resize",n))}definition(t){return new t4([t]).addToDOM(this.defs.element)}getReferenceURL(){if((iw||iA)&&iM.getElementsByTagName("base").length){if(!iz(e)){let t=iV(),i=new t4([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":`url(#${t})`,fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(iM.body);iB(i,{position:"fixed",top:0,left:0,zIndex:9e5});let s=iM.elementFromPoint(6,6);e=s?.id==="hitme",iM.body.removeChild(i)}if(e)return iU(iP.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""}getStyle(t){return this.style=iR({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style}setStyle(t){this.boxWrapper.css(this.getStyle(t))}isHidden(){return!this.boxWrapper.getBBox().width}destroy(){let t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),iN(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null}createElement(t){return new this.Element(this,t)}getRadialAttr(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}}shadowDefinition(t){let e=[`highcharts-drop-shadow-${this.chartIndex}`,...Object.keys(t).map(e=>`${e}-${t[e]}`)].join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=iG({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector(`#${e}`)||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e}getShadowFilterContent(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]}buildText(t){new iy(t).buildSVG()}getContrast(t){let e=tL.parse(t).rgba,i=" clamp(0,calc(9e9*(0.5 - (0.2126*r + 0.7152*g + 0.0722*b))),1)";if(iH(e[0])||!tL.useColorMix){let t=e.map(t=>{let e=t/255;return e<=.04?e/12.92:Math.pow((e+.055)/1.055,2.4)}),i=.2126*t[0]+.7152*t[1]+.0722*t[2];return 1.05/(i+.05)>(i+.05)/.05?"#FFFFFF":"#000000"}return"color(from "+t+" srgb"+i+i+i+")"}button(t,e,i,s,r={},o,a,n,h,l){let d=this.label(t,e,i,h,void 0,void 0,l,void 0,"button"),c=this.styledMode,p=arguments,g=0;r=iG(ib.global.buttonTheme,r),c&&(delete r.fill,delete r.stroke,delete r["stroke-width"]);let u=r.states||{},f=r.style||{};delete r.states,delete r.style;let m=[t4.filterUserAttributes(r)],x=[f];return c||["hover","select","disabled"].forEach((t,e)=>{m.push(iG(m[0],t4.filterUserAttributes(p[e+5]||u[t]||{}))),x.push(m[e+1].style),delete m[e+1].style}),iE(d.element,iS?"mouseover":"mouseenter",function(){3!==g&&d.setState(1)}),iE(d.element,iS?"mouseout":"mouseleave",function(){3!==g&&d.setState(g)}),d.setState=(t=0)=>{if(1!==t&&(d.state=g=t),d.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t]),!c){d.attr(m[t]);let e=x[t];iX(e)&&d.css(e)}},d.attr(m[0]),!c&&(d.css(iR({cursor:"default"},f)),l&&d.text.css({pointerEvents:"none"})),d.on("touchstart",t=>t.stopPropagation()).on("click",function(t){3!==g&&s?.call(d,t)})}crispLine(t,e){let[i,s]=t;return iz(i[1])&&i[1]===s[1]&&(i[1]=s[1]=iI(i[1],e)),iz(i[2])&&i[2]===s[2]&&(i[2]=s[2]=iI(i[2],e)),t}path(t){let e=this.styledMode?{}:{fill:"none"};return iW(t)?e.d=t:iX(t)&&iR(e,t),this.createElement("path").attr(e)}circle(t,e,i){let s=iX(t)?t:void 0===t?{}:{x:t,y:e,r:i},r=this.createElement("circle");return r.xSetter=r.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},r.attr(s)}arc(t,e,i,s,r,o){let a;iX(t)?(e=(a=t).y,i=a.r,s=a.innerR,r=a.start,o=a.end,t=a.x):a={innerR:s,start:r,end:o};let n=this.symbol("arc",t,e,i,i,a);return n.r=i,n}rect(t,e,i,s,r,o){let a=iX(t)?t:void 0===t?{}:{x:t,y:e,r,width:Math.max(i||0,0),height:Math.max(s||0,0)},n=this.createElement("rect");return this.styledMode||(void 0!==o&&(a["stroke-width"]=o,iR(a,n.crisp(a))),a.fill="none"),n.rSetter=function(t,e,i){n.r=t,iL(i,{rx:t,ry:t})},n.rGetter=function(){return n.r||0},n.attr(a)}roundedRect(t){return this.symbol("roundedRect").attr(t)}setSize(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:iY(i,!0)?void 0:0}),this.alignElements()}g(t){let e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e}image(t,e,i,s,r,o){let a={preserveAspectRatio:"none"};iH(e)&&(a.x=e),iH(i)&&(a.y=i),iH(s)&&(a.width=s),iH(r)&&(a.height=r);let n=this.createElement("image").attr(a),h=function(e){n.attr({href:t}),o.call(n,e)};if(o){n.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});let e=new iP.Image;iE(e,"load",h),e.src=t,e.complete&&h({})}else n.attr({href:t});return n}symbol(t,e,i,s,r,o){let a,n,h,l,d=this,c=/^url\((.*?)\)$/,p=c.test(t),g=!p&&(this.symbols[t]?t:"circle"),u=g&&this.symbols[g];if(u)"number"==typeof e&&(n=u.call(this.symbols,e||0,i||0,s||0,r||0,o)),a=this.path(n),d.styledMode||a.attr("fill","none"),iR(a,{symbolName:g||void 0,x:e,y:i,width:s,height:r}),o&&iR(a,o);else if(p){h=t.match(c)[1];let s=a=this.image(h);s.imgwidth=iY(o?.width,iO[h]?.width),s.imgheight=iY(o?.height,iO[h]?.height),l=t=>t.attr({width:t.width,height:t.height}),["width","height"].forEach(t=>{s[`${t}Setter`]=function(t,e){this[e]=t;let{alignByTranslate:i,element:s,width:r,height:a,imgwidth:n,imgheight:h}=this,l="width"===e?n:h,d=1;o&&"within"===o.backgroundSize&&r&&a&&n&&h?(d=Math.min(r/n,a/h),iL(s,{width:Math.round(n*d),height:Math.round(h*d)})):s&&l&&s.setAttribute(e,l),!i&&n&&h&&this.translate(((r||0)-n*d)/2,((a||0)-h*d)/2)}}),iz(e)&&s.attr({x:e,y:i}),s.isImg=!0,s.symbolUrl=t,iz(s.imgwidth)&&iz(s.imgheight)?l(s):(s.attr({width:0,height:0}),iD("img",{onload:function(){let t=iv[d.chartIndex];0===this.width&&(iB(this,{position:"absolute",top:"-999em"}),iM.body.appendChild(this)),iO[h]={width:this.width,height:this.height},s.imgwidth=this.width,s.imgheight=this.height,s.element&&l(s),this.parentNode&&this.parentNode.removeChild(this),d.imgCount--,d.imgCount||!t||t.hasLoaded||t.onload()},src:h}),this.imgCount++)}return a}clipRect(t,e,i,s){return this.rect(t,e,i,s,0)}text(t,e,i,s){let r={};if(s&&(this.allowHTML||!this.forExport))return this.html(t,e,i);r.x=Math.round(e||0),i&&(r.y=Math.round(i)),iz(t)&&(r.text=t);let o=this.createElement("text").attr(r);return s&&(!this.forExport||this.allowHTML)||(o.xSetter=function(t,e,i){let s=i.getElementsByTagName("tspan"),r=i.getAttribute(e);for(let i=0,o;i<s.length;i++)(o=s[i]).getAttribute(e)===r&&o.setAttribute(e,t);i.setAttribute(e,t)}),o}fontMetrics(t){let e=ij(e0.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),s=Math.round(.8*i);return{h:i,b:s,f:e}}rotCorr(t,e,i){let s=t;return e&&i&&(s=Math.max(s*Math.cos(e*ik),4)),{x:-t/3*Math.sin(e*ik),y:s}}pathToSegments(t){let e=[],i=[],s={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2};for(let r=0;r<t.length;r++)iF(i[0])&&iH(t[r])&&i.length===s[i[0].toUpperCase()]&&t.splice(r,0,i[0].replace("M","L").replace("m","l")),"string"==typeof t[r]&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[r]);return e.push(i.slice(0)),e}label(t,e,i,s,r,o,a,n,h){return new e8(this,t,e,i,s,r,o,a,n,h)}alignElements(){this.alignedObjects.forEach(t=>t.align())}}iR(i$.prototype,{Element:e0,SVG_NS:iC,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:io,draw:iT}),ef.registerRendererType("svg",i$,!0);let{composed:iZ,isFirefox:i_}=S,{attr:iq,css:iK,createElement:iJ,defined:iQ,extend:i0,getAlignFactor:i1,isNumber:i2,pInt:i3,pushUnique:i5}=_;function i6(t,e,i){let s=this.div?.style||i.style;e0.prototype[`${e}Setter`].call(this,t,e,i),s&&(s[e]=t)}let i9=(t,e)=>{if(!t.div){let i=iq(t.element,"class"),s=t.css,r=iJ("div",i?{className:i}:void 0,{position:"absolute",left:`${t.translateX||0}px`,top:`${t.translateY||0}px`,...t.styles,display:t.display,opacity:t.opacity,visibility:t.visibility},t.parentGroup?.div||e);t.classSetter=(t,e,i)=>{i.setAttribute("class",t),r.className=t},t.translateXSetter=t.translateYSetter=(e,i)=>{t[i]=e,r.style["translateX"===i?"left":"top"]=`${e}px`,t.doTransform=!0},t.opacitySetter=t.visibilitySetter=i6,t.css=e=>(s.call(t,e),e.cursor&&(r.style.cursor=e.cursor),e.pointerEvents&&(r.style.pointerEvents=e.pointerEvents),t),t.on=function(){return e0.prototype.on.apply({element:r,onEvents:t.onEvents},arguments),t},t.div=r}return t.div};class i4 extends e0{static compose(t){i5(iZ,this.compose)&&(t.prototype.html=function(t,e,i){return new i4(this,"span").attr({text:t,x:Math.round(e),y:Math.round(i)})})}constructor(t,e){super(t,e),i4.useForeignObject?this.foreignObject=t.createElement("foreignObject").attr({zIndex:2}):this.css({position:"absolute",...t.styledMode?{}:{fontFamily:t.style.fontFamily,fontSize:t.style.fontSize}}),this.element.style.whiteSpace="nowrap"}getSpanCorrection(t,e,i){this.xCorr=-t*i,this.yCorr=-e}css(t){let e,{element:i}=this,s="SPAN"===i.tagName&&t&&"width"in t,r=s&&t.width;return s&&(delete t.width,this.textWidth=i3(r)||void 0,e=!0),t?.textOverflow==="ellipsis"&&(t.overflow="hidden",t.whiteSpace="nowrap"),t?.lineClamp&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),i2(Number(t?.fontSize))&&(t.fontSize+="px"),i0(this.styles,t),iK(i,t),e&&this.updateTransform(),this}htmlGetBBox(){let{element:t}=this;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}}updateTransform(){if(!this.added){this.alignOnAdd=!0;return}let{element:t,foreignObject:e,oldTextWidth:i,renderer:s,rotation:r,rotationOriginX:o,rotationOriginY:a,scaleX:n,scaleY:h,styles:{display:l="inline-block",whiteSpace:d},textAlign:c="left",textWidth:p,translateX:g=0,translateY:u=0,x:f=0,y:m=0}=this;if(e||iK(t,{marginLeft:`${g}px`,marginTop:`${u}px`}),"SPAN"===t.tagName){let g,u=[r,c,t.innerHTML,p,this.textAlign].join(","),x=-(this.parentGroup?.padding*1)||0;if(p!==i){let e=this.textPxLength?this.textPxLength:(iK(t,{width:"",whiteSpace:d||"nowrap"}),t.offsetWidth),s=p||0,o=""===t.style.textOverflow&&t.style.webkitLineClamp;(s>i||e>s||o)&&(/[\-\s\u00AD]/.test(t.textContent||t.innerText)||"ellipsis"===t.style.textOverflow)&&(iK(t,{width:(r||n||e>s||o)&&i2(p)?p+"px":"auto",display:l,whiteSpace:d||"normal"}),this.oldTextWidth=p)}e&&(iK(t,{display:"inline-block",verticalAlign:"top"}),e.attr({width:s.width,height:s.height})),u!==this.cTT&&(g=s.fontMetrics(t).b,iQ(r)&&!e&&(r!==(this.oldRotation||0)||c!==this.oldAlign)&&iK(t,{transform:`rotate(${r}deg)`,transformOrigin:`${x}% ${x}px`}),this.getSpanCorrection(!iQ(r)&&!this.textWidth&&this.textPxLength||t.offsetWidth,g,i1(c)));let{xCorr:y=0,yCorr:b=0}=this,v={left:`${f+y}px`,top:`${m+b}px`,textAlign:c,transformOrigin:`${(o??f)-y-f-x}px ${(a??m)-b-m-x}px`};(n||h)&&(v.transform=`scale(${n??1},${h??1})`),e?(super.updateTransform(),i2(f)&&i2(m)?(e.attr({x:f+y,y:m+b,width:t.offsetWidth+3,height:t.offsetHeight,"transform-origin":t.getAttribute("transform-origin")||"0 0"}),iK(t,{display:l,textAlign:c})):i_&&e.attr({width:0,height:0})):iK(t,v),this.cTT=u,this.oldRotation=r,this.oldAlign=c}}add(t){let{foreignObject:e,renderer:i}=this,s=i.box.parentNode,r=[];if(e)e.add(t),super.add(i.createElement("body").attr({xmlns:"http://www.w3.org/1999/xhtml"}).css({background:"transparent",margin:"0 3px 0 0"}).add(e));else{let e;if(this.parentGroup=t,t&&!(e=t.div)){let i=t;for(;i;)r.push(i),i=i.parentGroup;for(let t of r.reverse())e=i9(t,s)}(e||s).appendChild(this.element)}return this.added=!0,this.alignOnAdd&&this.updateTransform(),this}textSetter(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,t4.setElementHTML(this.element,t??""),this.textStr=t,this.doTransform=!0)}alignSetter(t){this.alignValue=this.textAlign=t,this.doTransform=!0}xSetter(t,e){this[e]=t,this.doTransform=!0}}let i8=i4.prototype;i8.visibilitySetter=i8.opacitySetter=i6,i8.ySetter=i8.rotationSetter=i8.rotationOriginXSetter=i8.rotationOriginYSetter=i8.xSetter,!function(t){t.xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},t.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){let{numberFormatter:t}=this.axis.chart;return t(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0}}(a||(a={}));let i7=a,{addEvent:st,isFunction:se,objectEach:si,removeEvent:ss}=_;(n||(n={})).registerEventOptions=function(t,e){t.eventOptions=t.eventOptions||{},si(e.events,function(e,i){t.eventOptions[i]!==e&&(t.eventOptions[i]&&(ss(t,i,t.eventOptions[i]),delete t.eventOptions[i]),se(e)&&(t.eventOptions[i]=e,st(t,i,e,{order:0})))})};let sr=n,{deg2rad:so}=S,{clamp:sa,correctFloat:sn,defined:sh,destroyObjectProperties:sl,extend:sd,fireEvent:sc,getAlignFactor:sp,isNumber:sg,merge:su,objectEach:sf,pick:sm}=_,sx=class{constructor(t,e,i,s,r){this.isNew=!0,this.isNewLabel=!0,this.axis=t,this.pos=e,this.type=i||"",this.parameters=r||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,sc(this,"init"),i||s||this.addLabel()}addLabel(){let t=this,e=t.axis,i=e.options,s=e.chart,r=e.categories,o=e.logarithmic,a=e.names,n=t.pos,h=sm(t.options?.labels,i.labels),l=e.tickPositions,d=n===l[0],c=n===l[l.length-1],p=(!h.step||1===h.step)&&1===e.tickInterval,g=l.info,u=t.label,f,m,x,y=this.parameters.category||(r?sm(r[n],a[n],n):n);o&&sg(y)&&(y=sn(o.lin2log(y))),e.dateTime&&(g?f=(m=s.time.resolveDTLFormat(i.dateTimeLabelFormats[!i.grid&&g.higherRanks[n]||g.unitName])).main:sg(y)&&(f=e.dateTime.getXDateFormat(y,i.dateTimeLabelFormats||{}))),t.isFirst=d,t.isLast=c;let b={axis:e,chart:s,dateTimeLabelFormat:f,isFirst:d,isLast:c,pos:n,tick:t,tickPositionInfo:g,value:y};sc(this,"labelFormat",b);let v=t=>h.formatter?h.formatter.call(t,t):h.format?(t.text=e.defaultLabelFormatter.call(t),eu.format(h.format,t,s)):e.defaultLabelFormatter.call(t),k=v.call(b,b),M=m?.list;M?t.shortenLabel=function(){for(x=0;x<M.length;x++)if(sd(b,{dateTimeLabelFormat:M[x]}),u.attr({text:v.call(b,b)}),u.getBBox().width<e.getSlotWidth(t)-2*(h.padding||0))return;u.attr({text:""})}:t.shortenLabel=void 0,p&&e._addedPlotLB&&t.moveLabel(k,h),sh(u)||t.movedLabel?u&&u.textStr!==k&&!p&&(!u.textWidth||h.style.width||u.styles.width||u.css({width:null}),u.attr({text:k}),u.textPxLength=u.getBBox().width):(t.label=u=t.createLabel(k,h),t.rotation=0)}createLabel(t,e,i){let s=this.axis,{renderer:r,styledMode:o}=s.chart,a=e.style.whiteSpace,n=sh(t)&&e.enabled?r.text(t,i?.x,i?.y,e.useHTML).add(s.labelGroup):void 0;return n&&(o||n.css(su(e.style)),n.textPxLength=n.getBBox().width,!o&&a&&n.css({whiteSpace:a})),n}destroy(){sl(this,this.axis)}getPosition(t,e,i,s){let r=this.axis,o=r.chart,a=s&&o.oldChartHeight||o.chartHeight,n={x:t?sn(r.translate(e+i,void 0,void 0,s)+r.transB):r.left+r.offset+(r.opposite?(s&&o.oldChartWidth||o.chartWidth)-r.right-r.left:0),y:t?a-r.bottom+r.offset-(r.opposite?r.height:0):sn(a-r.translate(e+i,void 0,void 0,s)-r.transB)};return n.y=sa(n.y,-1e9,1e9),sc(this,"afterGetPosition",{pos:n}),n}getLabelPosition(t,e,i,s,r,o,a,n){let h,l,d=this.axis,c=d.transA,p=d.isLinked&&d.linkedParent?d.linkedParent.reversed:d.reversed,g=d.staggerLines,u=d.tickRotCorr||{x:0,y:0},f=s||d.reserveSpaceDefault?0:-d.labelOffset*("center"===d.labelAlign?.5:1),m=r.distance,x={};return h=0===d.side?i.rotation?-m:-i.getBBox().height:2===d.side?u.y+m:Math.cos(i.rotation*so)*(u.y-i.getBBox(!1,0).height/2),sh(r.y)&&(h=0===d.side&&d.horiz?r.y+h:r.y),t=t+sm(r.x,[0,1,0,-1][d.side]*m)+f+u.x-(o&&s?o*c*(p?-1:1):0),e=e+h-(o&&!s?o*c*(p?1:-1):0),g&&(l=a/(n||1)%g,d.opposite&&(l=g-l-1),e+=l*(d.labelOffset/g)),x.x=t,x.y=Math.round(e),sc(this,"afterGetLabelPosition",{pos:x,tickmarkOffset:o,index:a}),x}getLabelSize(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0}getMarkPath(t,e,i,s,r=!1,o){return o.crispLine([["M",t,e],["L",t+(r?0:-i),e+(r?i:0)]],s)}handleOverflow(t){let e=this.axis,i=e.options.labels,s=t.x,r=e.chart.chartWidth,o=e.chart.spacing,a=sm(e.labelLeft,Math.min(e.pos,o[3])),n=sm(e.labelRight,Math.max(e.isRadial?0:e.pos+e.len,r-o[1])),h=this.label,l=this.rotation,d=sp(e.labelAlign||h.attr("align")),c=h.getBBox().width,p=e.getSlotWidth(this),g=p,u=1,f;l||"justify"!==i.overflow?l<0&&s-d*c<a?f=Math.round(s/Math.cos(l*so)-a):l>0&&s+d*c>n&&(f=Math.round((r-s)/Math.cos(l*so))):(s-d*c<a?g=t.x+g*(1-d)-a:s+(1-d)*c>n&&(g=n-t.x+g*d,u=-1),(g=Math.min(p,g))<p&&"center"===e.labelAlign&&(t.x+=u*(p-g-d*(p-Math.min(c,g)))),(c>g||e.autoRotation&&h?.styles?.width)&&(f=g)),f&&h&&(this.shortenLabel?this.shortenLabel():h.css(sd({},{width:Math.floor(f)+"px",lineClamp:+!e.isRadial})))}moveLabel(t,e){let i=this,s=i.label,r=i.axis,o=!1,a;s&&s.textStr===t?(i.movedLabel=s,o=!0,delete i.label):sf(r.ticks,function(e){o||e.isNew||e===i||!e.label||e.label.textStr!==t||(i.movedLabel=e.label,o=!0,e.labelPos=i.movedLabel.xy,delete e.label)}),!o&&(i.labelPos||s)&&(a=i.labelPos||s.xy,i.movedLabel=i.createLabel(t,e,a),i.movedLabel&&i.movedLabel.attr({opacity:0}))}render(t,e,i){let s=this.axis,r=s.horiz,o=this.pos,a=sm(this.tickmarkOffset,s.tickmarkOffset),n=this.getPosition(r,o,a,e),h=n.x,l=n.y,d=s.pos,c=d+s.len,p=r?h:l,g=sm(i,this.label?.newOpacity,1);!s.chart.polar&&(sn(p)<d||p>c)&&(i=0),i??(i=1),this.isActive=!0,this.renderGridLine(e,i),this.renderMark(n,i),this.renderLabel(n,e,g,t),this.isNew=!1,sc(this,"afterRender")}renderGridLine(t,e){let i=this.axis,s=i.options,r={},o=this.pos,a=this.type,n=sm(this.tickmarkOffset,i.tickmarkOffset),h=i.chart.renderer,l=this.gridLine,d,c=s.gridLineWidth,p=s.gridLineColor,g=s.gridLineDashStyle;"minor"===this.type&&(c=s.minorGridLineWidth,p=s.minorGridLineColor,g=s.minorGridLineDashStyle),l||(i.chart.styledMode||(r.stroke=p,r["stroke-width"]=c||0,r.dashstyle=g),a||(r.zIndex=1),t&&(e=0),this.gridLine=l=h.path().attr(r).addClass("highcharts-"+(a?a+"-":"")+"grid-line").add(i.gridGroup)),l&&(d=i.getPlotLinePath({value:o+n,lineWidth:l.strokeWidth(),force:"pass",old:t,acrossPanes:!1}))&&l[t||this.isNew?"attr":"animate"]({d:d,opacity:e})}renderMark(t,e){let i=this.axis,s=i.options,r=i.chart.renderer,o=this.type,a=i.tickSize(o?o+"Tick":"tick"),n=t.x,h=t.y,l=sm(s["minor"!==o?"tickWidth":"minorTickWidth"],!o&&i.isXAxis?1:0),d=s["minor"!==o?"tickColor":"minorTickColor"],c=this.mark,p=!c;a&&(i.opposite&&(a[0]=-a[0]),c||(this.mark=c=r.path().addClass("highcharts-"+(o?o+"-":"")+"tick").add(i.axisGroup),i.chart.styledMode||c.attr({stroke:d,"stroke-width":l})),c[p?"attr":"animate"]({d:this.getMarkPath(n,h,a[0],c.strokeWidth(),i.horiz,r),opacity:e}))}renderLabel(t,e,i,s){let r=this.axis,o=r.horiz,a=r.options,n=this.label,h=a.labels,l=h.step,d=sm(this.tickmarkOffset,r.tickmarkOffset),c=t.x,p=t.y,g=!0;n&&sg(c)&&(n.xy=t=this.getLabelPosition(c,p,n,o,h,d,s,l),(!this.isFirst||this.isLast||a.showFirstLabel)&&(!this.isLast||this.isFirst||a.showLastLabel)?!o||h.step||h.rotation||e||0===i||this.handleOverflow(t):g=!1,l&&s%l&&(g=!1),g&&sg(t.y)?(t.opacity=i,n[this.isNewLabel?"attr":"animate"](t).show(!0),this.isNewLabel=!1):(n.hide(),this.isNewLabel=!0))}replaceMovedLabel(){let t=this.label,e=this.axis;t&&!this.isNew&&(t.animate({opacity:0},void 0,t.destroy),delete this.label),e.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel}},{animObject:sy}=t$,{xAxis:sb,yAxis:sv}=i7,{defaultOptions:sk}=tM,{registerEventOptions:sM}=sr,{deg2rad:sw}=S,{arrayMax:sS,arrayMin:sA,clamp:sT,correctFloat:sC,defined:sO,destroyObjectProperties:sP,erase:sE,error:sL,extend:sD,fireEvent:sI,getClosestDistance:sB,insertItem:sz,isArray:sN,isNumber:sR,isString:sW,merge:sH,normalizeTickInterval:sX,objectEach:sF,pick:sG,relativeLength:sY,removeEvent:sj,splat:sU,syncTimeout:sV}=_,s$=(t,e)=>sX(e,void 0,void 0,sG(t.options.allowDecimals,e<.5||void 0!==t.tickAmount),!!t.tickAmount);sD(sk,{xAxis:sb,yAxis:sH(sb,sv)});class sZ{constructor(t,e,i){this.init(t,e,i)}init(t,e,i=this.coll){let s="xAxis"===i,r=this.isZAxis||(t.inverted?!s:s);this.chart=t,this.horiz=r,this.isXAxis=s,this.coll=i,sI(this,"init",{userOptions:e}),this.opposite=sG(e.opposite,this.opposite),this.side=sG(e.side,this.side,r?2*!this.opposite:this.opposite?1:3),this.setOptions(e);let o=this.options,a=o.labels;this.type??(this.type=o.type||"linear"),this.uniqueNames??(this.uniqueNames=o.uniqueNames??!0),sI(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=sG(o.reversed,this.reversed),this.visible=o.visible,this.zoomEnabled=o.zoomEnabled,this.hasNames="category"===this.type||!0===o.categories,this.categories=sN(o.categories)&&o.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=sO(o.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},this.len??(this.len=0),this.minRange=this.userMinRange=o.minRange||o.maxZoom,this.range=o.range,this.offset=o.offset||0,this.max=void 0,this.min=void 0;let n=sG(o.crosshair,sU(t.options.tooltip.crosshairs)[+!s]);this.crosshair=!0===n?{}:n,-1===t.axes.indexOf(this)&&(s?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),sz(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&s&&!sO(this.reversed)&&(this.reversed=!0),this.labelRotation=sR(a.rotation)?a.rotation:void 0,sM(this,o),sI(this,"afterInit")}setOptions(t){let e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=sH(e,"yAxis"===this.coll?{title:{text:this.chart.options.lang.yAxisTitle}}:{},sk[this.coll],t),sI(this,"afterSetOptions",{userOptions:t})}defaultLabelFormatter(){let t=this.axis,{numberFormatter:e}=this.chart,i=sR(this.value)?this.value:NaN,s=t.chart.time,r=t.categories,o=this.dateTimeLabelFormat,a=sk.lang,n=a.numericSymbols,h=a.numericSymbolMagnitude||1e3,l=t.logarithmic?Math.abs(i):t.tickInterval,d=n?.length,c,p;if(r)p=`${this.value}`;else if(o)p=s.dateFormat(o,i,!0);else if(d&&n&&l>=1e3)for(;d--&&void 0===p;)l>=(c=Math.pow(h,d+1))&&10*i%c==0&&null!==n[d]&&0!==i&&(p=e(i/c,-1)+n[d]);return void 0===p&&(p=Math.abs(i)>=1e4?e(i,-1):e(i,-1,void 0,"")),p}getSeriesExtremes(){let t,e=this;sI(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(i=>{if(i.reserveSpace()){let s=i.options,r,o=s.threshold,a,n;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(o||0)&&(o=void 0),e.isXAxis)(r=i.getColumn("x")).length&&(r=e.logarithmic?r.filter(t=>t>0):r,a=(t=i.getXExtremes(r)).min,n=t.max,sR(a)||a instanceof Date||(r=r.filter(sR),a=(t=i.getXExtremes(r)).min,n=t.max),r.length&&(e.dataMin=Math.min(sG(e.dataMin,a),a),e.dataMax=Math.max(sG(e.dataMax,n),n)));else{let t=i.applyExtremes();sR(t.dataMin)&&(a=t.dataMin,e.dataMin=Math.min(sG(e.dataMin,a),a)),sR(t.dataMax)&&(n=t.dataMax,e.dataMax=Math.max(sG(e.dataMax,n),n)),sO(o)&&(e.threshold=o),(!s.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),sI(this,"afterGetSeriesExtremes")}translate(t,e,i,s,r,o){let a=this.linkedParent||this,n=s&&a.old?a.old.min:a.min;if(!sR(n))return NaN;let h=a.minPixelPadding,l=(a.isOrdinal||a.brokenAxis?.hasBreaks||a.logarithmic&&r)&&a.lin2val,d=1,c=0,p=s&&a.old?a.old.transA:a.transA,g=0;return p||(p=a.transA),i&&(d*=-1,c=a.len),a.reversed&&(d*=-1,c-=d*(a.sector||a.len)),e?(g=(t=t*d+c-h)/p+n,l&&(g=a.lin2val(g))):(l&&(t=a.val2lin(t)),g=d*(t-n)*p+c+d*h+(sR(o)?p*o:0),a.isRadial||(g=sC(g))),g}toPixels(t,e){return this.translate(this.chart?.time.parse(t)??NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)}toValue(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)}getPlotLinePath(t){let e=this,i=e.chart,s=e.left,r=e.top,o=t.old,a=t.value,n=t.lineWidth,h=o&&i.oldChartHeight||i.chartHeight,l=o&&i.oldChartWidth||i.chartWidth,d=e.transB,c=t.translatedValue,p=t.force,g,u,f,m,x;function y(t,e,i){return"pass"!==p&&(t<e||t>i)&&(p?t=sT(t,e,i):x=!0),t}let b={value:a,lineWidth:n,old:o,force:p,acrossPanes:t.acrossPanes,translatedValue:c};return sI(this,"getPlotLinePath",b,function(t){g=f=(c=sT(c=sG(c,e.translate(a,void 0,void 0,o)),-1e9,1e9))+d,u=m=h-c-d,sR(c)?e.horiz?(u=r,m=h-e.bottom+(e.options.isInternal?0:i.scrollablePixelsY||0),g=f=y(g,s,s+e.width)):(g=s,f=l-e.right+(i.scrollablePixelsX||0),u=m=y(u,r,r+e.height)):(x=!0,p=!1),t.path=x&&!p?void 0:i.renderer.crispLine([["M",g,u],["L",f,m]],n||1)}),b.path}getLinearTickPositions(t,e,i){let s,r,o,a=sC(Math.floor(e/t)*t),n=sC(Math.ceil(i/t)*t),h=[];if(sC(a+t)===a&&(o=20),this.single)return[e];for(s=a;s<=n&&(h.push(s),(s=sC(s+t,o))!==r);)r=s;return h}getMinorTickInterval(){let{minorTicks:t,minorTickInterval:e}=this.options;return!0===t?sG(e,"auto"):!1!==t?e:void 0}getMinorTickPositions(){let t=this.options,e=this.tickPositions,i=this.minorTickInterval,s=this.pointRangePadding||0,r=(this.min||0)-s,o=(this.max||0)+s,a=this.brokenAxis?.hasBreaks?this.brokenAxis.unitLength:o-r,n=[],h;if(a&&a/i<this.len/3){let s=this.logarithmic;if(s)this.paddedTicks.forEach(function(t,e,r){e&&n.push.apply(n,s.getLogTickPositions(i,r[e-1],r[e],!0))});else if(this.dateTime&&"auto"===this.getMinorTickInterval())n=n.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(i),r,o,t.startOfWeek));else for(h=r+(e[0]-r)%i;h<=o&&h!==n[0];h+=i)n.push(h)}return 0!==n.length&&this.trimTicks(n),n}adjustForMinRange(){let t=this.options,e=this.logarithmic,i=this.chart.time,{max:s,min:r,minRange:o}=this,a,n,h,l;this.isXAxis&&void 0===o&&!e&&(o=sO(t.min)||sO(t.max)||sO(t.floor)||sO(t.ceiling)?null:Math.min(5*(sB(this.series.map(t=>{let e=t.getColumn("x");return t.xIncrement?e.slice(0,2):e}))||0),this.dataMax-this.dataMin)),sR(s)&&sR(r)&&sR(o)&&s-r<o&&(n=this.dataMax-this.dataMin>=o,a=(o-s+r)/2,h=[r-a,i.parse(t.min)??r-a],n&&(h[2]=e?e.log2lin(this.dataMin):this.dataMin),l=[(r=sS(h))+o,i.parse(t.max)??r+o],n&&(l[2]=e?e.log2lin(this.dataMax):this.dataMax),(s=sA(l))-r<o&&(h[0]=s-o,h[1]=i.parse(t.min)??s-o,r=sS(h))),this.minRange=o,this.min=r,this.max=s}getClosest(){let t,e;if(this.categories)e=1;else{let i=[];this.series.forEach(function(t){let s=t.closestPointRange,r=t.getColumn("x");1===r.length?i.push(r[0]):t.sorted&&sO(s)&&t.reserveSpace()&&(e=sO(e)?Math.min(e,s):s)}),i.length&&(i.sort((t,e)=>t-e),t=sB([i]))}return t&&e?Math.min(t,e):t||e}nameToX(t){let e=sN(this.options.categories),i=e?this.categories:this.names,s=t.options.x,r;return t.series.requireSorting=!1,sO(s)||(s=this.uniqueNames&&i?e?i.indexOf(t.name):sG(i.keys[t.name],-1):t.series.autoIncrement()),-1===s?!e&&i&&(r=i.length):sR(s)&&(r=s),void 0!==r?(this.names[r]=t.name,this.names.keys[t.name]=r):t.x&&(r=t.x),r}updateNames(){let t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(e=>{e.xIncrement=null,(!e.points||e.isDirtyData)&&(t.max=Math.max(t.max||0,e.dataTable.rowCount-1),e.processData(),e.generatePoints());let i=e.getColumn("x").slice();e.data.forEach((e,s)=>{let r=i[s];e?.options&&void 0!==e.name&&void 0!==(r=t.nameToX(e))&&r!==e.x&&(i[s]=e.x=r)}),e.dataTable.setColumn("x",i)}))}setAxisTranslation(){let t=this,e=t.max-t.min,i=t.linkedParent,s=!!t.categories,r=t.isXAxis,o=t.axisPointRange||0,a,n=0,h=0,l,d=t.transA;(r||s||o)&&(a=t.getClosest(),i?(n=i.minPointOffset,h=i.pointRangePadding):t.series.forEach(function(e){let i=s?1:r?sG(e.options.pointRange,a,0):t.axisPointRange||0,l=e.options.pointPlacement;if(o=Math.max(o,i),!t.single||s){let t=e.is("xrange")?!r:r;n=Math.max(n,t&&sW(l)?0:i/2),h=Math.max(h,t&&"on"===l?0:i)}}),l=t.ordinal?.slope&&a?t.ordinal.slope/a:1,t.minPointOffset=n*=l,t.pointRangePadding=h*=l,t.pointRange=Math.min(o,t.single&&s?1:e),r&&(t.closestPointRange=a)),t.translationSlope=t.transA=d=t.staticScale||t.len/(e+h||1),t.transB=t.horiz?t.left:t.bottom,t.minPixelPadding=d*n,sI(this,"afterSetAxisTranslation")}minFromRange(){let{max:t,min:e}=this;return sR(t)&&sR(e)&&t-e||void 0}setTickInterval(t){let{categories:e,chart:i,dataMax:s,dataMin:r,dateTime:o,isXAxis:a,logarithmic:n,options:h,softThreshold:l}=this,d=i.time,c=sR(this.threshold)?this.threshold:void 0,p=this.minRange||0,{ceiling:g,floor:u,linkedTo:f,softMax:m,softMin:x}=h,y=sR(f)&&i[this.coll]?.[f],b=h.tickPixelInterval,v=h.maxPadding,k=h.minPadding,M=0,w,S=sR(h.tickInterval)&&h.tickInterval>=0?h.tickInterval:void 0,A,T,C,O;if(o||e||y||this.getTickAmount(),C=sG(this.userMin,d.parse(h.min)),O=sG(this.userMax,d.parse(h.max)),y?(this.linkedParent=y,w=y.getExtremes(),this.min=sG(w.min,w.dataMin),this.max=sG(w.max,w.dataMax),this.type!==y.type&&sL(11,!0,i)):(l&&sO(c)&&sR(s)&&sR(r)&&(r>=c?(A=c,k=0):s<=c&&(T=c,v=0)),this.min=sG(C,A,r),this.max=sG(O,T,s)),sR(this.max)&&sR(this.min)&&(n&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,sG(r,this.min))&&sL(10,!0,i),this.min=sC(n.log2lin(this.min),16),this.max=sC(n.log2lin(this.max),16)),this.range&&sR(r)&&(this.userMin=this.min=C=Math.max(r,this.minFromRange()||0),this.userMax=O=this.max,this.range=void 0)),sI(this,"foundExtremes"),this.adjustForMinRange(),sR(this.min)&&sR(this.max)){if(!sR(this.userMin)&&sR(x)&&x<this.min&&(this.min=C=x),!sR(this.userMax)&&sR(m)&&m>this.max&&(this.max=O=m),e||this.axisPointRange||this.stacking?.usePercentage||y||!(M=this.max-this.min)||(!sO(C)&&k&&(this.min-=M*k),sO(O)||!v||(this.max+=M*v)),!sR(this.userMin)&&sR(u)&&(this.min=Math.max(this.min,u)),!sR(this.userMax)&&sR(g)&&(this.max=Math.min(this.max,g)),l&&sR(r)&&sR(s)){let t=c||0;!sO(C)&&this.min<t&&r>=t?this.min=h.minRange?Math.min(t,this.max-p):t:!sO(O)&&this.max>t&&s<=t&&(this.max=h.minRange?Math.max(t,this.min+p):t)}!i.polar&&this.min>this.max&&(sO(h.min)?this.max=this.min:sO(h.max)&&(this.min=this.max)),M=this.max-this.min}if(this.min!==this.max&&sR(this.min)&&sR(this.max)?y&&!S&&b===y.options.tickPixelInterval?this.tickInterval=S=y.tickInterval:this.tickInterval=sG(S,this.tickAmount?M/Math.max(this.tickAmount-1,1):void 0,e?1:M*b/Math.max(this.len,b)):this.tickInterval=1,a&&!t){let t=this.min!==this.old?.min||this.max!==this.old?.max;this.series.forEach(function(e){e.forceCrop=e.forceCropping?.(),e.processData(t)}),sI(this,"postProcessData",{hasExtremesChanged:t})}this.setAxisTranslation(),sI(this,"initialAxisTranslation"),this.pointRange&&!S&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));let P=sG(h.minTickInterval,o&&!this.series.some(t=>!t.sorted)?this.closestPointRange:0);!S&&P&&this.tickInterval<P&&(this.tickInterval=P),o||n||S||(this.tickInterval=s$(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()}setTickPositions(){let t=this.options,e=t.tickPositions,i=t.tickPositioner,s=this.getMinorTickInterval(),r=!this.isPanning,o=r&&t.startOnTick,a=r&&t.endOnTick,n=[],h;if(this.tickmarkOffset=this.categories&&"between"===t.tickmarkPlacement&&1===this.tickInterval?.5:0,this.single=this.min===this.max&&sO(this.min)&&!this.tickAmount&&(this.min%1==0||!1!==t.allowDecimals),e)n=e.slice();else if(sR(this.min)&&sR(this.max)){if(!this.ordinal?.positions&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))n=[this.min,this.max],sL(19,!1,this.chart);else if(this.dateTime)n=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,t.units),this.min,this.max,t.startOfWeek,this.ordinal?.positions,this.closestPointRange,!0);else if(this.logarithmic)n=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else{let t=this.tickInterval,e=t;for(;e<=2*t;)if(n=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&n.length>this.tickAmount)this.tickInterval=s$(this,e*=1.1);else break}n.length>this.len&&(n=[n[0],n[n.length-1]])[0]===n[1]&&(n.length=1),i&&(this.tickPositions=n,(h=i.apply(this,[this.min,this.max]))&&(n=h))}this.tickPositions=n,this.minorTickInterval="auto"===s&&this.tickInterval?this.tickInterval/t.minorTicksPerMajor:s,this.paddedTicks=n.slice(0),this.trimTicks(n,o,a),!this.isLinked&&sR(this.min)&&sR(this.max)&&(this.single&&n.length<2&&!this.categories&&!this.series.some(t=>t.is("heatmap")&&"between"===t.options.pointPlacement)&&(this.min-=.5,this.max+=.5),e||h||this.adjustTickAmount()),sI(this,"afterSetTickPositions")}trimTicks(t,e,i){let s=t[0],r=t[t.length-1],o=!this.isOrdinal&&this.minPointOffset||0;if(sI(this,"trimTicks"),!this.isLinked||!this.grid){if(e&&s!==-1/0)this.min=s;else for(;this.min-o>t[0];)t.shift();if(i)this.max=r;else for(;this.max+o<t[t.length-1];)t.pop();0===t.length&&sO(s)&&!this.options.tickPositions&&t.push((r+s)/2)}}alignToOthers(){let t,e=this,i=e.chart,s=[this],r=e.options,o=i.options.chart,a="yAxis"===this.coll&&o.alignThresholds,n=[];if(e.thresholdAlignment=void 0,(!1!==o.alignTicks&&r.alignTicks||a)&&!1!==r.startOnTick&&!1!==r.endOnTick&&!e.logarithmic){let r=t=>{let{horiz:e,options:i}=t;return[e?i.left:i.top,i.width,i.height,i.pane].join(",")},o=r(this);i[this.coll].forEach(function(i){let{series:a}=i;a.length&&a.some(t=>t.visible)&&i!==e&&r(i)===o&&(t=!0,s.push(i))})}if(t&&a){s.forEach(t=>{let i=t.getThresholdAlignment(e);sR(i)&&n.push(i)});let t=n.length>1?n.reduce((t,e)=>t+=e,0)/n.length:void 0;s.forEach(e=>{e.thresholdAlignment=t})}return t}getThresholdAlignment(t){if((!sR(this.dataMin)||this!==t&&this.series.some(t=>t.isDirty||t.isDirtyData))&&this.getSeriesExtremes(),sR(this.threshold)){let t=sT((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(t=1-t),t}}getTickAmount(){let t=this.options,e=t.tickPixelInterval,i=t.tickAmount;sO(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i}adjustTickAmount(){let t=this,{finalTickAmt:e,max:i,min:s,options:r,tickPositions:o,tickAmount:a,thresholdAlignment:n}=t,h=o?.length,l=sG(t.threshold,t.softThreshold?0:null),d,c,p=t.tickInterval,g,u=()=>o.push(sC(o[o.length-1]+p)),f=()=>o.unshift(sC(o[0]-p));if(sR(n)&&(g=n<.5?Math.ceil(n*(a-1)):Math.floor(n*(a-1)),r.reversed&&(g=a-1-g)),t.hasData()&&sR(s)&&sR(i)){let n=()=>{t.transA*=(h-1)/(a-1),t.min=r.startOnTick?o[0]:Math.min(s,o[0]),t.max=r.endOnTick?o[o.length-1]:Math.max(i,o[o.length-1])};if(sR(g)&&sR(t.threshold)){for(;o[g]!==l||o.length!==a||o[0]>s||o[o.length-1]<i;){for(o.length=0,o.push(t.threshold);o.length<a;)void 0===o[g]||o[g]>t.threshold?f():u();if(p>8*t.tickInterval)break;p*=2}n()}else if(h<a){for(;o.length<a;)o.length%2||s===l?u():f();n()}if(sO(e)){for(c=d=o.length;c--;)(3===e&&c%2==1||e<=2&&c>0&&c<d-1)&&o.splice(c,1);t.finalTickAmt=void 0}}}setScale(){let{coll:t,stacking:e}=this,i=!1,s=!1;this.series.forEach(t=>{i=i||t.isDirtyData||t.isDirty,s=s||t.xAxis?.isDirty||!1}),this.setAxisSize();let r=this.len!==this.old?.len;r||i||s||this.isLinked||this.forceRedraw||this.userMin!==this.old?.userMin||this.userMax!==this.old?.userMax||this.alignToOthers()?(e&&"yAxis"===t&&e.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),e&&"xAxis"===t&&e.buildStacks(),this.isDirty||(this.isDirty=r||this.min!==this.old?.min||this.max!==this.old?.max)):e&&e.cleanStacks(),i&&delete this.allExtremes,sI(this,"afterSetScale")}setExtremes(t,e,i=!0,s,r){let o=this.chart;this.series.forEach(t=>{delete t.kdTree}),t=o.time.parse(t),e=o.time.parse(e),sI(this,"setExtremes",r=sD(r,{min:t,max:e}),t=>{this.userMin=t.min,this.userMax=t.max,this.eventArgs=t,i&&o.redraw(s)})}setAxisSize(){let t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],s=this.horiz,r=this.width=Math.round(sY(sG(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),o=this.height=Math.round(sY(sG(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),a=this.top=Math.round(sY(sG(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),n=this.left=Math.round(sY(sG(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-o-a,this.right=t.chartWidth-r-n,this.len=Math.max(s?r:o,0),this.pos=s?n:a}getExtremes(){let t=this.logarithmic;return{min:t?sC(t.lin2log(this.min)):this.min,max:t?sC(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}}getThreshold(t){let e=this.logarithmic,i=e?e.lin2log(this.min):this.min,s=e?e.lin2log(this.max):this.max;return null===t||t===-1/0?t=i:t===1/0?t=s:i>t?t=i:s<t&&(t=s),this.translate(t,0,1,0,1)}autoLabelAlign(t){let e=(sG(t,0)-90*this.side+720)%360,i={align:"center"};return sI(this,"autoLabelAlign",i,function(t){e>15&&e<165?t.align="right":e>195&&e<345&&(t.align="left")}),i.align}tickSize(t){let e=this.options,i=sG(e["tick"===t?"tickWidth":"minorTickWidth"],"tick"===t&&this.isXAxis&&!this.categories?1:0),s=e["tick"===t?"tickLength":"minorTickLength"],r;i&&s&&("inside"===e[t+"Position"]&&(s=-s),r=[s,i]);let o={tickSize:r};return sI(this,"afterTickSize",o),o.tickSize}labelMetrics(){let t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)}unsquish(){let t=this.options.labels,e=t.padding||0,i=this.horiz,s=this.tickInterval,r=this.len/((+!!this.categories+this.max-this.min)/s),o=t.rotation,a=sC(.8*this.labelMetrics().h),n=Math.max(this.max-this.min,0),h=function(t){let i=(t+2*e)/(r||1);return(i=i>1?Math.ceil(i):1)*s>n&&t!==1/0&&r!==1/0&&n&&(i=Math.ceil(n/s)),sC(i*s)},l=s,d,c=Number.MAX_VALUE,p;if(i){if(!t.staggerLines&&(sR(o)?p=[o]:r<t.autoRotationLimit&&(p=t.autoRotation)),p){let t,e;for(let i of p)(i===o||i&&i>=-90&&i<=90)&&(e=(t=h(Math.abs(a/Math.sin(sw*i))))+Math.abs(i/360))<c&&(c=e,d=i,l=t)}}else l=h(.75*a);return this.autoRotation=p,this.labelRotation=sG(d,sR(o)?o:0),t.step?s:l}getSlotWidth(t){let e=this.chart,i=this.horiz,s=this.options.labels,r=Math.max(this.tickPositions.length-+!this.categories,1),o=e.margin[3];if(t&&sR(t.slotWidth))return t.slotWidth;if(i&&s.step<2&&!this.isRadial)return s.rotation?0:(this.staggerLines||1)*this.len/r;if(!i){let t=s.style.width;if(void 0!==t)return parseInt(String(t),10);if(o)return o-e.spacing[3]}return .33*e.chartWidth}renderUnsquish(){let t=this.chart,e=t.renderer,i=this.tickPositions,s=this.ticks,r=this.options.labels,o=r.style,a=this.horiz,n=this.getSlotWidth(),h=Math.max(1,Math.round(n-(a?2*(r.padding||0):r.distance||0))),l={},d=this.labelMetrics(),c=o.lineClamp,p,g=c??(Math.floor(this.len/(i.length*d.h))||1),u=0;sW(r.rotation)||(l.rotation=r.rotation||0),i.forEach(function(t){let e=s[t];e.movedLabel&&e.replaceMovedLabel();let i=e.label?.textPxLength||0;i>u&&(u=i)}),this.maxLabelLength=u,this.autoRotation?u>h&&u>d.h?l.rotation=this.labelRotation:this.labelRotation=0:n&&(p=h),l.rotation&&(p=u>.5*t.chartHeight?.33*t.chartHeight:u,c||(g=1)),this.labelAlign=r.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(l.align=this.labelAlign),i.forEach(function(t){let e=s[t],i=e?.label,r=o.width,a={};i&&(i.attr(l),e.shortenLabel?e.shortenLabel():p&&!r&&"nowrap"!==o.whiteSpace&&(p<(i.textPxLength||0)||"SPAN"===i.element.tagName)?i.css(sD(a,{width:`${p}px`,lineClamp:g})):!i.styles.width||a.width||r||i.css({width:"auto"}),e.rotation=l.rotation)},this),this.tickRotCorr=e.rotCorr(d.b,this.labelRotation||0,0!==this.side)}hasData(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&sO(this.min)&&sO(this.max)}addTitle(t){let e,i=this.chart.renderer,s=this.horiz,r=this.opposite,o=this.options.title,a=this.chart.styledMode;this.axisTitle||((e=o.textAlign)||(e=(s?{low:"left",middle:"center",high:"right"}:{low:r?"right":"left",middle:"center",high:r?"left":"right"})[o.align]),this.axisTitle=i.text(o.text||"",0,0,o.useHTML).attr({zIndex:7,rotation:o.rotation||0,align:e}).addClass("highcharts-axis-title"),a||this.axisTitle.css(sH(o.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),a||o.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)}generateTick(t){let e=this.ticks;e[t]?e[t].addLabel():e[t]=new sx(this,t)}createGroups(){let{axisParent:t,chart:e,coll:i,options:s}=this,r=e.renderer,o=(e,o,a)=>r.g(e).attr({zIndex:a}).addClass(`highcharts-${i.toLowerCase()}${o} `+(this.isRadial?`highcharts-radial-axis${o} `:"")+(s.className||"")).add(t);this.axisGroup||(this.gridGroup=o("grid","-grid",s.gridZIndex),this.axisGroup=o("axis","",s.zIndex),this.labelGroup=o("axis-labels","-labels",s.labels.zIndex))}getOffset(){let t=this,{chart:e,horiz:i,options:s,side:r,ticks:o,tickPositions:a,coll:n}=t,h=e.inverted&&!t.isZAxis?[1,0,3,2][r]:r,l=t.hasData(),d=s.title,c=s.labels,p=sR(s.crossing),g=e.axisOffset,u=e.clipOffset,f=[-1,1,1,-1][r],m,x=0,y,b=0,v=0,k,M;if(t.showAxis=m=l||s.showEmpty,t.staggerLines=t.horiz&&c.staggerLines||void 0,t.createGroups(),l||t.isLinked?(a.forEach(function(e){t.generateTick(e)}),t.renderUnsquish(),t.reserveSpaceDefault=0===r||2===r||({1:"left",3:"right"})[r]===t.labelAlign,sG(c.reserveSpace,!p&&null,"center"===t.labelAlign||null,t.reserveSpaceDefault)&&a.forEach(function(t){v=Math.max(o[t].getLabelSize(),v)}),t.staggerLines&&(v*=t.staggerLines),t.labelOffset=v*(t.opposite?-1:1)):sF(o,function(t,e){t.destroy(),delete o[e]}),d?.text&&!1!==d.enabled&&(t.addTitle(m),m&&!p&&!1!==d.reserveSpace&&(t.titleOffset=x=t.axisTitle.getBBox()[i?"height":"width"],b=sO(y=d.offset)?0:sG(d.margin,i?5:10))),t.renderLine(),t.offset=f*sG(s.offset,g[r]?g[r]+(s.margin||0):0),t.tickRotCorr=t.tickRotCorr||{x:0,y:0},M=0===r?-t.labelMetrics().h:2===r?t.tickRotCorr.y:0,k=Math.abs(v)+b,v&&(k-=M,k+=f*(i?sG(c.y,t.tickRotCorr.y+f*c.distance):sG(c.x,f*c.distance))),t.axisTitleMargin=sG(y,k),t.getMaxLabelDimensions&&(t.maxLabelDimensions=t.getMaxLabelDimensions(o,a)),"colorAxis"!==n&&u){let e=this.tickSize("tick");g[r]=Math.max(g[r],(t.axisTitleMargin||0)+x+f*t.offset,k,a?.length&&e?e[0]+f*t.offset:0);let i=!t.axisLine||s.offset?0:t.axisLine.strokeWidth()/2;u[h]=Math.max(u[h],i)}sI(this,"afterGetOffset")}getLinePath(t){let e=this.chart,i=this.opposite,s=this.offset,r=this.horiz,o=this.left+(i?this.width:0)+s,a=e.chartHeight-this.bottom-(i?this.height:0)+s;return i&&(t*=-1),e.renderer.crispLine([["M",r?this.left:o,r?a:this.top],["L",r?e.chartWidth-this.right:o,r?a:e.chartHeight-this.bottom]],t)}renderLine(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))}getTitlePosition(t){let e=this.horiz,i=this.left,s=this.top,r=this.len,o=this.options.title,a=e?i:s,n=this.opposite,h=this.offset,l=o.x,d=o.y,c=this.chart.renderer.fontMetrics(t),p=t?Math.max(t.getBBox(!1,0).height-c.h-1,0):0,g={low:a+(e?0:r),middle:a+r/2,high:a+(e?r:0)}[o.align],u=(e?s+this.height:i)+(e?1:-1)*(n?-1:1)*(this.axisTitleMargin||0)+[-p,p,c.f,-p][this.side],f={x:e?g+l:u+(n?this.width:0)+h+l,y:e?u+d-(n?this.height:0)+h:g+d};return sI(this,"afterGetTitlePosition",{titlePosition:f}),f}renderMinorTick(t,e){let i=this.minorTicks;i[t]||(i[t]=new sx(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)}renderTick(t,e,i){let s=this.isLinked,r=this.ticks;(!s||t>=this.min&&t<=this.max||this.grid?.isColumn)&&(r[t]||(r[t]=new sx(this,t)),i&&r[t].isNew&&r[t].render(e,!0,-1),r[t].render(e))}render(){let t,e,i=this,s=i.chart,r=i.logarithmic,o=s.renderer,a=i.options,n=i.isLinked,h=i.tickPositions,l=i.axisTitle,d=i.ticks,c=i.minorTicks,p=i.alternateBands,g=a.stackLabels,u=a.alternateGridColor,f=a.crossing,m=i.tickmarkOffset,x=i.axisLine,y=i.showAxis,b=sy(o.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[d,c,p].forEach(function(t){sF(t,function(t){t.isActive=!1})}),sR(f)){let t=this.isXAxis?s.yAxis[0]:s.xAxis[0],e=[1,-1,-1,1][this.side];if(t){let s=t.toPixels(f,!0);i.horiz&&(s=t.len-s),i.offset=e*s}}if(i.hasData()||n){let o=i.chart.hasRendered&&i.old&&sR(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(t){i.renderMinorTick(t,o)}),h.length&&(h.forEach(function(t,e){i.renderTick(t,e,o)}),m&&(0===i.min||i.single)&&(d[-1]||(d[-1]=new sx(i,-1,null,!0)),d[-1].render(-1))),u&&h.forEach(function(o,a){e=void 0!==h[a+1]?h[a+1]+m:i.max-m,a%2==0&&o<i.max&&e<=i.max+(s.polar?-m:m)&&(p[o]||(p[o]=new S.PlotLineOrBand(i,{})),t=o+m,p[o].options={from:r?r.lin2log(t):t,to:r?r.lin2log(e):e,color:u,className:"highcharts-alternate-grid"},p[o].render(),p[o].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(a.plotLines||[]).concat(a.plotBands||[]).forEach(function(t){i.addPlotBandOrLine(t)}))}[d,c,p].forEach(function(t){let e=[],i=b.duration;sF(t,function(t,i){t.isActive||(t.render(i,!1,0),t.isActive=!1,e.push(i))}),sV(function(){let i=e.length;for(;i--;)t[e[i]]&&!t[e[i]].isActive&&(t[e[i]].destroy(),delete t[e[i]])},t!==p&&s.hasRendered&&i?i:0)}),x&&(x[x.isPlaced?"animate":"attr"]({d:this.getLinePath(x.strokeWidth())}),x.isPlaced=!0,x[y?"show":"hide"](y)),l&&y&&(l[l.isNew?"attr":"animate"](i.getTitlePosition(l)),l.isNew=!1),g?.enabled&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,sI(this,"afterRender")}redraw(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})}getKeepProps(){return this.keepProps||sZ.keepProps}destroy(t){let e=this,i=e.plotLinesAndBands,s=this.eventOptions;if(sI(this,"destroy",{keepEvents:t}),t||sj(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(t){sP(t)}),i){let t=i.length;for(;t--;)i[t].destroy()}for(let t in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(t){e[t]&&(e[t]=e[t].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[t]=e.plotLinesAndBandsGroups[t].destroy();sF(e,function(t,i){-1===e.getKeepProps().indexOf(i)&&delete e[i]}),this.eventOptions=s}drawCrosshair(t,e){let i=this.crosshair,s=i?.snap??!0,r=this.chart,o,a,n,h=this.cross,l;if(sI(this,"drawCrosshair",{e:t,point:e}),t||(t=this.cross?.e),i&&!1!==(sO(e)||!s)){if(s?sO(e)&&(a=sG("colorAxis"!==this.coll?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):a=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),sO(a)&&(l={value:e&&(this.isXAxis?e.x:sG(e.stackY,e.y)),translatedValue:a},r.polar&&sD(l,{isCrosshair:!0,chartX:t?.chartX,chartY:t?.chartY,point:e}),o=this.getPlotLinePath(l)||null),!sO(o)){this.hideCrosshair();return}n=this.categories&&!this.isRadial,h||(this.cross=h=r.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(n?"category ":"thin ")+(i.className||"")).attr({zIndex:sG(i.zIndex,2)}).add(),!r.styledMode&&(h.attr({stroke:i.color||(n?tL.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":sG(i.width,1)}).css({"pointer-events":"none"}),i.dashStyle&&h.attr({dashstyle:i.dashStyle}))),h.show().attr({d:o}),n&&!i.width&&h.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();sI(this,"afterDrawCrosshair",{e:t,point:e})}hideCrosshair(){this.cross&&this.cross.hide(),sI(this,"afterHideCrosshair")}update(t,e){let i=this.chart;t=sH(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,sG(e,!0)&&i.redraw()}remove(t){let e=this.chart,i=this.coll,s=this.series,r=s.length;for(;r--;)s[r]&&s[r].remove(!1);sE(e.axes,this),sE(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,sG(t,!0)&&e.redraw()}setTitle(t,e){this.update({title:t},e)}setCategories(t,e){this.update({categories:t},e)}}sZ.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"];let{addEvent:s_,getMagnitude:sq,normalizeTickInterval:sK,timeUnits:sJ}=_;!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new s(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,s_(t,"afterSetType",i)),t};class s{constructor(t){this.axis=t}normalizeTimeTickInterval(t,e){let i=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],s=i[i.length-1],r=sJ[s[0]],o=s[1],a;for(a=0;a<i.length&&(r=sJ[(s=i[a])[0]],o=s[1],!i[a+1]||!(t<=(r*o[o.length-1]+sJ[i[a+1][0]])/2));a++);r===sJ.year&&t<5*r&&(o=[1,2,5]);let n=sK(t/r,o,"year"===s[0]?Math.max(sq(t/r),1):1);return{unitRange:r,count:n,unitName:s[0]}}getXDateFormat(t,e){let{axis:i}=this,s=i.chart.time;return i.closestPointRange?s.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||s.resolveDTLFormat(e.year).main:s.resolveDTLFormat(e.day).main}}t.Additions=s}(h||(h={}));let sQ=h,{addEvent:s0,normalizeTickInterval:s1,pick:s2}=_;!function(t){function e(){"logarithmic"!==this.type?this.logarithmic=void 0:this.logarithmic??(this.logarithmic=new s(this))}function i(){let t=this.logarithmic;t&&(this.lin2val=function(e){return t.lin2log(e)},this.val2lin=function(e){return t.log2lin(e)})}t.compose=function(t){return t.keepProps.includes("logarithmic")||(t.keepProps.push("logarithmic"),s0(t,"afterSetType",e),s0(t,"afterInit",i)),t};class s{constructor(t){this.axis=t}getLogTickPositions(t,e,i,s){let r=this.axis,o=r.len,a=r.options,n=[];if(s||(this.minorAutoInterval=void 0),t>=.5)t=Math.round(t),n=r.getLinearTickPositions(t,e,i);else if(t>=.08){let r,o,a,h,l,d,c,p=Math.floor(e);for(r=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],o=p;o<i+1&&!c;o++)for(a=0,h=r.length;a<h&&!c;a++)(l=this.log2lin(this.lin2log(o)*r[a]))>e&&(!s||d<=i)&&void 0!==d&&n.push(d),d>i&&(c=!0),d=l}else{let h=this.lin2log(e),l=this.lin2log(i),d=s?r.getMinorTickInterval():a.tickInterval,c=a.tickPixelInterval/(s?5:1),p=s?o/r.tickPositions.length:o;t=s1(t=s2("auto"===d?null:d,this.minorAutoInterval,(l-h)*c/(p||1))),n=r.getLinearTickPositions(t,h,l).map(this.log2lin),s||(this.minorAutoInterval=t/5)}return s||(r.tickInterval=t),n}lin2log(t){return Math.pow(10,t)}log2lin(t){return Math.log(t)/Math.LN10}}t.Additions=s}(l||(l={}));let s3=l,{erase:s5,extend:s6,isNumber:s9}=_;!function(t){let e;function i(t){return this.addPlotBandOrLine(t,"plotBands")}function s(t,i){let s=this.userOptions,r=new e(this,t);if(this.visible&&(r=r.render()),r){if(this._addedPlotLB||(this._addedPlotLB=!0,(s.plotLines||[]).concat(s.plotBands||[]).forEach(t=>{this.addPlotBandOrLine(t)})),i){let e=s[i]||[];e.push(t),s[i]=e}this.plotLinesAndBands.push(r)}return r}function r(t){return this.addPlotBandOrLine(t,"plotLines")}function o(t,e,i){i=i||this.options;let s=this.getPlotLinePath({value:e,force:!0,acrossPanes:i.acrossPanes}),r=[],o=this.horiz,a=!s9(this.min)||!s9(this.max)||t<this.min&&e<this.min||t>this.max&&e>this.max,n=this.getPlotLinePath({value:t,force:!0,acrossPanes:i.acrossPanes}),h,l=1,d;if(n&&s)for(a&&(d=n.toString()===s.toString(),l=0),h=0;h<n.length;h+=2){let t=n[h],e=n[h+1],i=s[h],a=s[h+1];("M"===t[0]||"L"===t[0])&&("M"===e[0]||"L"===e[0])&&("M"===i[0]||"L"===i[0])&&("M"===a[0]||"L"===a[0])&&(o&&i[1]===t[1]?(i[1]+=l,a[1]+=l):o||i[2]!==t[2]||(i[2]+=l,a[2]+=l),r.push(["M",t[1],t[2]],["L",e[1],e[2]],["L",a[1],a[2]],["L",i[1],i[2]],["Z"])),r.isFlat=d}return r}function a(t){this.removePlotBandOrLine(t)}function n(t){let e=this.plotLinesAndBands,i=this.options,s=this.userOptions;if(e){let r=e.length;for(;r--;)e[r].id===t&&e[r].destroy();[i.plotLines||[],s.plotLines||[],i.plotBands||[],s.plotBands||[]].forEach(function(e){for(r=e.length;r--;)e[r]?.id===t&&s5(e,e[r])})}}function h(t){this.removePlotBandOrLine(t)}t.compose=function(t,l){let d=l.prototype;return d.addPlotBand||(e=t,s6(d,{addPlotBand:i,addPlotLine:r,addPlotBandOrLine:s,getPlotBandPath:o,removePlotBand:a,removePlotLine:h,removePlotBandOrLine:n})),l}}(d||(d={}));let s4=d,{addEvent:s8,arrayMax:s7,arrayMin:rt,defined:re,destroyObjectProperties:ri,erase:rs,fireEvent:rr,merge:ro,objectEach:ra,pick:rn}=_;class rh{static compose(t,e){return s8(t,"afterInit",function(){this.labelCollectors.push(()=>{let t=[];for(let e of this.axes)for(let{label:i,options:s}of e.plotLinesAndBands)i&&!s?.label?.allowOverlap&&t.push(i);return t})}),s4.compose(rh,e)}constructor(t,e){this.axis=t,this.options=e,this.id=e.id}render(){rr(this,"render");let{axis:t,options:e}=this,{horiz:i,logarithmic:s}=t,{color:r,events:o,zIndex:a=0}=e,{renderer:n,time:h}=t.chart,l={},d=h.parse(e.to),c=h.parse(e.from),p=h.parse(e.value),g=e.borderWidth,u=e.label,{label:f,svgElem:m}=this,x=[],y,b=re(c)&&re(d),v=re(p),k=!m,M={class:"highcharts-plot-"+(b?"band ":"line ")+(e.className||"")},w=b?"bands":"lines";if(!t.chart.styledMode&&(v?(M.stroke=r||"#999999",M["stroke-width"]=rn(e.width,1),e.dashStyle&&(M.dashstyle=e.dashStyle)):b&&(M.fill=r||"#e6e9ff",g&&(M.stroke=e.borderColor,M["stroke-width"]=g))),l.zIndex=a,w+="-"+a,(y=t.plotLinesAndBandsGroups[w])||(t.plotLinesAndBandsGroups[w]=y=n.g("plot-"+w).attr(l).add()),m||(this.svgElem=m=n.path().attr(M).add(y)),re(p))x=t.getPlotLinePath({value:s?.log2lin(p)??p,lineWidth:m.strokeWidth(),acrossPanes:e.acrossPanes});else{if(!(re(c)&&re(d)))return;x=t.getPlotBandPath(s?.log2lin(c)??c,s?.log2lin(d)??d,e)}return!this.eventsAdded&&o&&(ra(o,(t,e)=>{m?.on(e,t=>{o[e].apply(this,[t])})}),this.eventsAdded=!0),(k||!m.d)&&x?.length?m.attr({d:x}):m&&(x?(m.show(),m.animate({d:x})):m.d&&(m.hide(),f&&(this.label=f=f.destroy()))),u&&(re(u.text)||re(u.formatter))&&x?.length&&t.width>0&&t.height>0&&!x.isFlat?(u=ro({align:i&&b?"center":void 0,x:i?!b&&4:10,verticalAlign:!i&&b?"middle":void 0,y:i?b?16:10:b?6:-4,rotation:i&&!b?90:0,...b?{inside:!0}:{}},u),this.renderLabel(u,x,b,a)):f&&f.hide(),this}renderLabel(t,e,i,s){let r=this.axis,o=r.chart.renderer,a=t.inside,n=this.label;n||(this.label=n=o.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:s}),r.chart.styledMode||n.css(ro({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),n.add());let h=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],l=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],d=rt(h),c=rt(l),p=s7(h)-d;n.align(t,!1,{x:d,y:c,width:p,height:s7(l)-c}),n.alignAttr.y-=o.fontMetrics(n).b,(!n.alignValue||"left"===n.alignValue||re(a))&&n.css({width:(t.style?.width||(i&&a?p:90===n.rotation?r.height-(n.alignAttr.y-r.top):(t.clip?r.width:r.chart.chartWidth)-(n.alignAttr.x-r.left)))+"px"}),n.show(!0)}getLabelText(t){return re(t.formatter)?t.formatter.call(this):t.text}destroy(){rs(this.axis.plotLinesAndBands,this),delete this.axis,ri(this)}}let{animObject:rl}=t$,{format:rd}=eu,{composed:rc,dateFormats:rp,doc:rg,isSafari:ru}=S,{distribute:rf}=ev,{addEvent:rm,clamp:rx,css:ry,discardElement:rb,extend:rv,fireEvent:rk,getAlignFactor:rM,isArray:rw,isNumber:rS,isObject:rA,isString:rT,merge:rC,pick:rO,pushUnique:rP,splat:rE,syncTimeout:rL}=_;class rD{constructor(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}bodyFormatter(t){return t.map(t=>{let e=t.series.tooltipOptions,i=t.formatPrefix||"point";return(e[i+"Formatter"]||t.tooltipFormatter).call(t,e[i+"Format"]||"")})}cleanSplit(t){this.chart.series.forEach(function(e){let i=e?.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})}defaultFormatter(t){let e,i=this.points||rE(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e}destroy(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),rb(this.container)),_.clearTimeout(this.hideTimer)}getAnchor(t,e){let i,{chart:s,pointer:r}=this,o=s.inverted,a=s.plotTop,n=s.plotLeft;if(t=rE(t),t[0].series?.yAxis&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)void 0===e.chartX&&(e=r.normalize(e)),i=[e.chartX-n,e.chartY-a];else if(t[0].tooltipPos)i=t[0].tooltipPos;else{let s=0,r=0;t.forEach(function(t){let e=t.pos(!0);e&&(s+=e[0],r+=e[1])}),s/=t.length,r/=t.length,this.shared&&t.length>1&&e&&(o?s=e.chartX:r=e.chartY),i=[s-n,r-a]}return i.map(Math.round)}getClassName(t,e,i){let s=this.options,r=t.series,o=r.options;return[s.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+rO(t.colorIndex,r.colorIndex),o?.className].filter(rT).join(" ")}getLabel({anchorX:t,anchorY:e}={anchorX:0,anchorY:0}){let i=this,s=this.chart.styledMode,r=this.options,o=this.split&&this.allowShared,a=this.container,n=this.chart.renderer;if(this.label){let t=!this.label.hasClass("highcharts-label");(!o&&t||o&&!t)&&this.destroy()}if(!this.label){if(this.outside){let t=this.chart,e=t.options.chart.style,i=ef.getRendererType();this.container=a=S.doc.createElement("div"),a.className="highcharts-tooltip-container "+(t.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),ry(a,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,(e?.zIndex||0)+3)}),this.renderer=n=new i(a,0,0,e,void 0,void 0,n.styledMode)}if(o?this.label=n.g("tooltip"):(this.label=n.label("",t,e,r.shape||"callout",void 0,void 0,r.useHTML,void 0,"tooltip").attr({padding:r.padding,r:r.borderRadius}),s||this.label.attr({fill:r.backgroundColor,"stroke-width":r.borderWidth||0}).css(r.style).css({pointerEvents:r.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),i.outside){let t=this.label;[t.xSetter,t.ySetter].forEach((e,s)=>{t[s?"ySetter":"xSetter"]=r=>{e.call(t,i.distance),t[s?"y":"x"]=r,a&&(a.style[s?"top":"left"]=`${r}px`)}})}this.label.attr({zIndex:8}).shadow(r.shadow??!r.fixed).add()}return a&&!a.parentElement&&S.doc.body.appendChild(a),this.label}getPlayingField(){let{body:t,documentElement:e}=rg,{chart:i,distance:s,outside:r}=this;return{width:r?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*s-2:i.chartWidth,height:r?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}}getPosition(t,e,i){let{distance:s,chart:r,outside:o,pointer:a}=this,{inverted:n,plotLeft:h,plotTop:l,polar:d}=r,{plotX:c=0,plotY:p=0}=i,g={},u=n&&i.h||0,{height:f,width:m}=this.getPlayingField(),x=a.getChartPosition(),y=t=>t*x.scaleX,b=t=>t*x.scaleY,v=i=>{let a="x"===i;return[i,a?m:f,a?t:e].concat(o?[a?y(t):b(e),a?x.left-s+y(c+h):x.top-s+b(p+l),0,a?m:f]:[a?t:e,a?c+h:p+l,a?h:l,a?h+r.plotWidth:l+r.plotHeight])},k=v("y"),M=v("x"),w,S=!!i.negative;!d&&r.hoverSeries?.yAxis?.reversed&&(S=!S);let A=!this.followPointer&&rO(i.ttBelow,!d&&!n===S),T=function(t,e,i,r,a,n,h){let l=o?"y"===t?b(s):y(s):s,d=(i-r)/2,c=r<a-s,p=a+s+r<e,f=a-l-i+d,m=a+l-d;if(A&&p)g[t]=m;else if(!A&&c)g[t]=f;else if(c)g[t]=Math.min(h-r,f-u<0?f:f-u);else{if(!p)return g[t]=0,!1;g[t]=Math.max(n,m+u+i>e?m:m+u)}},C=function(t,e,i,r,o){if(o<s||o>e-s)return!1;o<i/2?g[t]=1:o>e-r/2?g[t]=e-r-2:g[t]=o-i/2},O=function(t){[k,M]=[M,k],w=t},P=()=>{!1!==T.apply(0,k)?!1!==C.apply(0,M)||w||(O(!0),P()):w?g.x=g.y=0:(O(!0),P())};return(n&&!d||this.len>1)&&O(),P(),g}getFixedPosition(t,e,i){let s=i.series,{chart:r,options:o,split:a}=this,n=o.position,h=n.relativeTo,l=o.shared||s?.yAxis?.isRadial&&("pane"===h||!h)?"plotBox":h,d="chart"===l?r.renderer:r[l]||r.getClipBox(s,!0);return{x:d.x+(d.width-t)*rM(n.align)+n.x,y:d.y+(d.height-e)*rM(n.verticalAlign)+(!a&&n.y||0)}}hide(t){let e=this;_.clearTimeout(this.hideTimer),t=rO(t,this.options.hideDelay),this.isHidden||(this.hideTimer=rL(function(){let i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t?150:t,complete:()=>{i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))}init(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=rO(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))}shouldStickOnContact(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))}move(t,e,i,s){let{followPointer:r,options:o}=this,a=rl(!r&&!this.isHidden&&!o.fixed&&o.animation),n=r||(this.len||0)>1,h={x:t,y:e};n?h.anchorX=h.anchorY=NaN:(h.anchorX=i,h.anchorY=s),a.step=()=>this.drawTracker(),this.getLabel().animate(h,a)}refresh(t,e){let{chart:i,options:s,pointer:r,shared:o}=this,a=rE(t),n=a[0],h=s.format,l=s.formatter||this.defaultFormatter,d=i.styledMode,c=this.allowShared;if(!s.enabled||!n.series)return;_.clearTimeout(this.hideTimer),this.allowShared=!(!rw(t)&&t.series&&t.series.noSharedTooltip),c=c&&!this.allowShared,this.followPointer=!this.split&&n.series.tooltipOptions.followPointer;let p=this.getAnchor(t,e),g=p[0],u=p[1];o&&this.allowShared&&(r.applyInactiveState(a),a.forEach(t=>t.setState("hover")),n.points=a),this.len=a.length;let f=rT(h)?rd(h,n,i):l.call(n,this);n.points=void 0;let m=n.series;if(this.distance=rO(m.tooltipOptions.distance,16),!1===f)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(f,a);else{let t=g,o=u;if(e&&r.isDirectTouch&&(t=e.chartX-i.plotLeft,o=e.chartY-i.plotTop),i.polar||!1===m.options.clip||a.some(e=>r.isDirectTouch||e.series.shouldShowTooltip(t,o))){let t=this.getLabel(c&&this.tt||{});(!s.style.width||d)&&t.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),t.attr({class:this.getClassName(n),text:f&&f.join?f.join(""):f}),this.outside&&t.attr({x:rx(t.x||0,0,this.getPlayingField().width-(t.width||0)-1)}),d||t.attr({stroke:s.borderColor||n.color||m.color||"#666666"}),this.updatePosition({plotX:g,plotY:u,negative:n.negative,ttBelow:n.ttBelow,series:m,h:p[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}rk(this,"refresh")}renderSplit(t,e){let i=this,{chart:s,chart:{chartWidth:r,chartHeight:o,plotHeight:a,plotLeft:n,plotTop:h,scrollablePixelsY:l=0,scrollablePixelsX:d,styledMode:c},distance:p,options:g,options:{fixed:u,position:f,positioner:m},pointer:x}=i,{scrollLeft:y=0,scrollTop:b=0}=s.scrollablePlotArea?.scrollingContainer||{},v=i.outside&&"number"!=typeof d?rg.documentElement.getBoundingClientRect():{left:y,right:y+r,top:b,bottom:b+o},k=i.getLabel(),M=this.renderer||s.renderer,w=!!s.xAxis[0]?.opposite,{left:S,top:A}=x.getChartPosition(),T=m||u,C=h+b,O=0,P=a-l,E=function(t,e,s,r=[0,0],o=!0){let a,n;if(s.isHeader)n=w?0:P,a=rx(r[0]-t/2,v.left,v.right-t-(i.outside?S:0));else if(u&&s){let r=i.getFixedPosition(t,e,s);a=r.x,n=r.y-C}else n=r[1]-C,a=rx(a=o?r[0]-t-p:r[0]+p,o?a:v.left,v.right);return{x:a,y:n}};rT(t)&&(t=[!1,t]);let L=t.slice(0,e.length+1).reduce(function(t,s,r){if(!1!==s&&""!==s){let o=e[r-1]||{isHeader:!0,plotX:e[0].plotX,plotY:a,series:{}},l=o.isHeader,d=l?i:o.series,f=d.tt=function(t,e,s){let r=t,{isHeader:o,series:a}=e,n=a.tooltipOptions||g;if(!r){let t={padding:n.padding,r:n.borderRadius};c||(t.fill=n.backgroundColor,t["stroke-width"]=n.borderWidth??(u&&!o?0:1)),r=M.label("",0,0,n[o?"headerShape":"shape"]||(u&&!o?"rect":"callout"),void 0,void 0,n.useHTML).addClass(i.getClassName(e,!0,o)).attr(t).add(k)}return r.isActive=!0,r.attr({text:s}),c||r.css(n.style).attr({stroke:n.borderColor||e.color||a.color||"#333333"}),r}(d.tt,o,s.toString()),x=f.getBBox(),y=x.width+f.strokeWidth();l&&(O=x.height,P+=O,w&&(C-=O));let{anchorX:b,anchorY:S}=function(t){let e,i,{isHeader:s,plotX:r=0,plotY:o=0,series:l}=t;if(s)e=Math.max(n+r,n),i=h+a/2;else{let{xAxis:t,yAxis:s}=l;e=t.pos+rx(r,-p,t.len+p),l.shouldShowTooltip(0,s.pos-h+o,{ignoreX:!0})&&(i=s.pos+o)}return{anchorX:e=rx(e,v.left-p,v.right+p),anchorY:i}}(o);if("number"==typeof S){let e=x.height+1,s=(m||E).call(i,y,e,o,[b,S]);t.push({align:T?0:void 0,anchorX:b,anchorY:S,boxWidth:y,point:o,rank:rO(s.rank,+!!l),size:e,target:s.y,tt:f,x:s.x})}else f.isActive=!1}return t},[]);!T&&L.some(t=>{let{outside:e}=i,s=(e?S:0)+t.anchorX;return s<v.left&&s+t.boxWidth<v.right||s<S-v.left+t.boxWidth&&v.right-s>s})&&(L=L.map(t=>{let{x:e,y:i}=E.call(this,t.boxWidth,t.size,t.point,[t.anchorX,t.anchorY],!1);return rv(t,{target:i,x:e})})),i.cleanSplit(),rf(L,P);let D={left:S,right:S};L.forEach(function(t){let{x:e,boxWidth:s,isHeader:r}=t;!r&&(i.outside&&S+e<D.left&&(D.left=S+e),!r&&i.outside&&D.left+s>D.right&&(D.right=S+e))}),L.forEach(function(t){let{x:e,anchorX:s,anchorY:r,pos:o,point:{isHeader:a}}=t,n={visibility:void 0===o?"hidden":"inherit",x:e,y:(o||0)+C+(u&&f.y||0),anchorX:s,anchorY:r};if(i.outside&&e<s){let t=S-D.left;t>0&&(a||(n.x=e+t,n.anchorX=s+t),a&&(n.x=(D.right-D.left)/2,n.anchorX=s+t))}t.tt.attr(n)});let{container:I,outside:B,renderer:z}=i;if(B&&I&&z){let{width:t,height:e,x:i,y:s}=k.getBBox();z.setSize(t+i,e+s,!1),I.style.left=D.left+"px",I.style.top=A+"px"}ru&&k.attr({opacity:1===k.opacity?.999:1})}drawTracker(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}let t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(!e||!i)return;let s={x:0,y:0,width:0,height:0},r=this.getAnchor(i),o=e.getBBox();r[0]+=t.plotLeft-(e.translateX||0),r[1]+=t.plotTop-(e.translateY||0),s.x=Math.min(0,r[0]),s.y=Math.min(0,r[1]),s.width=r[0]<0?Math.max(Math.abs(r[0]),o.width-r[0]):Math.max(Math.abs(r[0]),o.width),s.height=r[1]<0?Math.max(Math.abs(r[1]),o.height-Math.abs(r[1])):Math.max(Math.abs(r[1]),o.height),this.tracker?this.tracker.attr(s):(this.tracker=e.renderer.rect(s).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}styledModeFormat(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')}headerFooterFormatter(t,e){let i=t.series,s=i.tooltipOptions,r=i.xAxis,o=r?.dateTime,a={isFooter:e,point:t},n=s.xDateFormat||"",h=s[e?"footerFormat":"headerFormat"];return rk(this,"headerFormatter",a,function(e){if(o&&!n&&rS(t.key)&&(n=o.getXDateFormat(t.key,s.dateTimeLabelFormats)),o&&n){if(rA(n)){let t=n;rp[0]=e=>i.chart.time.dateFormat(t,e),n="%0"}(t.tooltipDateKeys||["key"]).forEach(t=>{h=h.replace(RegExp("point\\."+t+"([ \\)}])"),`(point.${t}:${n})$1`)})}i.chart.styledMode&&(h=this.styledModeFormat(h)),e.text=rd(h,t,this.chart)}),a.text||""}update(t){this.destroy(),this.init(this.chart,rC(!0,this.options,t))}updatePosition(t){let{chart:e,container:i,distance:s,options:r,pointer:o,renderer:a}=this,{height:n=0,width:h=0}=this.getLabel(),{fixed:l,positioner:d}=r,{left:c,top:p,scaleX:g,scaleY:u}=o.getChartPosition(),f=(d||l&&this.getFixedPosition||this.getPosition).call(this,h,n,t),m=S.doc,x=(t.plotX||0)+e.plotLeft,y=(t.plotY||0)+e.plotTop,b;if(a&&i){if(d||l){let{scrollLeft:t=0,scrollTop:i=0}=e.scrollablePlotArea?.scrollingContainer||{};f.x+=t+c-s,f.y+=i+p-s}b=(r.borderWidth||0)+2*s+2,a.setSize(rx(h+b,0,m.documentElement.clientWidth)-1,n+b,!1),(1!==g||1!==u)&&(ry(i,{transform:`scale(${g}, ${u})`}),x*=g,y*=u),x+=c-f.x,y+=p-f.y}this.move(Math.round(f.x),Math.round(f.y||0),x,y)}}!function(t){t.compose=function(e){rP(rc,"Core.Tooltip")&&rm(e,"afterInit",function(){let e=this.chart;e.options.tooltip&&(e.tooltip=new t(e,e.options.tooltip,this))})}}(rD||(rD={}));let rI=rD,{animObject:rB}=t$,{defaultOptions:rz}=tM,{format:rN}=eu,{addEvent:rR,crisp:rW,erase:rH,extend:rX,fireEvent:rF,getNestedProperty:rG,isArray:rY,isFunction:rj,isNumber:rU,isObject:rV,merge:r$,pick:rZ,syncTimeout:r_,removeEvent:rq,uniqueKey:rK}=_;class rJ{animateBeforeDestroy(){let t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(i){t[i]=t[i].animate("dataLabel"===i?{x:t[i].startXPos,y:t[i].startYPos,opacity:0}:e)}),i.plural.forEach(function(e){t[e].forEach(function(e){e.element&&e.animate(rX({x:t.startXPos},e.startYPos?{x:e.startXPos,y:e.startYPos}:{}))})})}applyOptions(t,e){let i=this.series,s=i.options.pointValKey||i.pointValKey;return rX(this,t=rJ.prototype.optionsToObject.call(this,t)),this.options=this.options?rX(this.options,t):t,t.group&&delete this.group,t.dataLabels&&delete this.dataLabels,s&&(this.y=rJ.prototype.getNestedProperty.call(this,s)),this.selected&&(this.state="select"),"name"in this&&void 0===e&&i.xAxis&&i.xAxis.hasNames&&(this.x=i.xAxis.nameToX(this)),void 0===this.x&&i?this.x=e??i.autoIncrement():rU(t.x)&&i.options.relativeXValue?this.x=i.autoIncrement(t.x):"string"==typeof this.x&&(e??(e=i.chart.time.parse(this.x)),rU(e)&&(this.x=e)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this}destroy(){if(!this.destroyed){let t=this,e=t.series,i=e.chart,s=e.options.dataSorting,r=i.hoverPoints,o=rB(t.series.chart.renderer.globalAnimation),a=()=>{for(let e in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(rq(t),t.destroyElements()),t)delete t[e]};t.legendItem&&i.legend.destroyItem(t),r&&(t.setState(),rH(r,t),r.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),s?.enabled?(this.animateBeforeDestroy(),r_(a,o.duration)):a(),i.pointCount--}this.destroyed=!0}destroyElements(t){let e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(t){e[t]=e[t].destroy()}),i.plural.forEach(function(t){e[t].forEach(function(t){t?.element&&t.destroy()}),delete e[t]})}firePointEvent(t,e,i){let s=this,r=this.series.options;s.manageEvent(t),"click"===t&&r.allowPointSelect&&(i=function(t){!s.destroyed&&s.select&&s.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),rF(s,t,e,i)}getClassName(){return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+(this.zone?.className?" "+this.zone.className.replace("highcharts-negative",""):"")}getGraphicalProps(t){let e,i,s=this,r=[],o={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&r.push("graphic","connector"),t.dataLabel&&r.push("dataLabel","dataLabelPath","dataLabelUpper"),i=r.length;i--;)s[e=r[i]]&&o.singular.push(e);return["graphic","dataLabel"].forEach(function(e){let i=e+"s";t[e]&&s[i]&&o.plural.push(i)}),o}getNestedProperty(t){return t?0===t.indexOf("custom.")?rG(t,this.options):this[t]:void 0}getZone(){let t=this.series,e=t.zones,i=t.zoneAxis||"y",s,r=0;for(s=e[0];this[i]>=s.value;)s=e[++r];return this.nonZonedColor||(this.nonZonedColor=this.color),s?.color&&!this.options.color?this.color=s.color:this.color=this.nonZonedColor,s}hasNewShapeType(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType}constructor(t,e,i){this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),this.id??(this.id=rK()),this.resolveColor(),this.dataLabelOnNull??(this.dataLabelOnNull=t.options.nullInteraction),t.chart.pointCount++,rF(this,"afterInit")}isValid(){return(rU(this.x)||this.x instanceof Date)&&rU(this.y)}optionsToObject(t){let e=this.series,i=e.options.keys,s=i||e.pointArrayMap||["y"],r=s.length,o={},a,n=0,h=0;if(rU(t)||null===t)o[s[0]]=t;else if(rY(t))for(!i&&t.length>r&&("string"==(a=typeof t[0])?e.xAxis?.dateTime?o.x=e.chart.time.parse(t[0]):o.name=t[0]:"number"===a&&(o.x=t[0]),n++);h<r;)i&&void 0===t[n]||(s[h].indexOf(".")>0?rJ.prototype.setNestedProperty(o,t[n],s[h]):o[s[h]]=t[n]),n++,h++;else"object"==typeof t&&(o=t,t.dataLabels&&(e.hasDataLabels=()=>!0),t.marker&&(e._hasPointMarkers=!0));return o}pos(t,e=this.plotY){if(!this.destroyed){let{plotX:i,series:s}=this,{chart:r,xAxis:o,yAxis:a}=s,n=0,h=0;if(rU(i)&&rU(e))return t&&(n=o?o.pos:r.plotLeft,h=a?a.pos:r.plotTop),r.inverted&&o&&a?[a.len-e+h,o.len-i+n]:[i+n,e+h]}}resolveColor(){let t=this.series,e=t.chart.options.chart,i=t.chart.styledMode,s,r,o=e.colorCount,a;delete this.nonZonedColor,t.options.colorByPoint?(i||(s=(r=t.options.colors||t.chart.options.colors)[t.colorCounter],o=r.length),a=t.colorCounter,t.colorCounter++,t.colorCounter===o&&(t.colorCounter=0)):(i||(s=t.color),a=t.colorIndex),this.colorIndex=rZ(this.options.colorIndex,a),this.color=rZ(this.options.color,s)}setNestedProperty(t,e,i){return i.split(".").reduce(function(t,i,s,r){let o=r.length-1===s;return t[i]=o?e:rV(t[i],!0)?t[i]:{},t[i]},t),t}shouldDraw(){return!this.isNull}tooltipFormatter(t){let{chart:e,pointArrayMap:i=["y"],tooltipOptions:s}=this.series,{valueDecimals:r="",valuePrefix:o="",valueSuffix:a=""}=s;return e.styledMode&&(t=e.tooltip?.styledModeFormat(t)||t),i.forEach(e=>{e="{point."+e,(o||a)&&(t=t.replace(RegExp(e+"}","g"),o+e+"}"+a)),t=t.replace(RegExp(e+"}","g"),e+":,."+r+"f}")}),rN(t,this,e)}update(t,e,i,s){let r,o=this,a=o.series,n=o.graphic,h=a.chart,l=a.options;function d(){o.applyOptions(t);let s=n&&o.hasMockGraphic,d=null===o.y?!s:s;n&&d&&(o.graphic=n.destroy(),delete o.hasMockGraphic),rV(t,!0)&&(n?.element&&t&&t.marker&&void 0!==t.marker.symbol&&(o.graphic=n.destroy()),t?.dataLabels&&o.dataLabel&&(o.dataLabel=o.dataLabel.destroy())),r=o.index;let c={};for(let t of a.dataColumnKeys())c[t]=o[t];a.dataTable.setRow(c,r),l.data[r]=rV(l.data[r],!0)||rV(t,!0)?o.options:rZ(t,l.data[r]),a.isDirty=a.isDirtyData=!0,!a.fixedBox&&a.hasCartesianSeries&&(h.isDirtyBox=!0),"point"===l.legendType&&(h.isDirtyLegend=!0),e&&h.redraw(i)}e=rZ(e,!0),!1===s?d():o.firePointEvent("update",{options:t},d)}remove(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)}select(t,e){let i=this,s=i.series,r=s.chart;t=rZ(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,s.options.data[s.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||r.getSelectedPoints().forEach(function(t){let e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(r.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})}),delete this.selectedStaging}onMouseOver(t){let{inverted:e,pointer:i}=this.series.chart;i&&(t=t?i.normalize(t):i.getChartCoordinatesFromPoint(this,e),i.runPointActions(t,this))}onMouseOut(){let t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null}manageEvent(t){let e=r$(this.series.options.point,this.options),i=e.events?.[t];rj(i)&&(!this.hcEvents?.[t]||this.hcEvents?.[t]?.map(t=>t.fn).indexOf(i)===-1)?(this.importedUserEvent?.(),this.importedUserEvent=rR(this,t,i),this.hcEvents&&(this.hcEvents[t].userEvent=!0)):this.importedUserEvent&&!i&&this.hcEvents?.[t]&&this.hcEvents?.[t].userEvent&&(rq(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent)}setState(t,e){let i=this.series,s=this.state,r=i.options.states[t||"normal"]||{},o=rz.plotOptions[i.type].marker&&i.options.marker,a=o&&!1===o.enabled,n=o?.states?.[t||"normal"]||{},h=!1===n.enabled,l=this.marker||{},d=i.chart,c=o&&i.markerAttribs,p=i.halo,g,u,f,m=i.stateMarkerGraphic,x;if((t=t||"")===this.state&&!e||this.selected&&"select"!==t||!1===r.enabled||t&&(h||a&&!1===n.enabled)||t&&l.states&&l.states[t]&&!1===l.states[t].enabled)return;if(this.state=t,c&&(g=i.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(s&&this.graphic.removeClass("highcharts-point-"+s),t&&this.graphic.addClass("highcharts-point-"+t),!d.styledMode){u=i.pointAttribs(this,t),f=rZ(d.options.chart.animation,r.animation);let e=u.opacity;i.options.inactiveOtherPoints&&rU(e)&&(this.dataLabels||[]).forEach(function(t){t&&!t.hasClass("highcharts-data-label-hidden")&&(t.animate({opacity:e},f),t.connector&&t.connector.animate({opacity:e},f))}),this.graphic.animate(u,f)}g&&this.graphic.animate(g,rZ(d.options.chart.animation,n.animation,o.animation)),m&&m.hide()}else t&&n&&(x=l.symbol||i.symbol,m&&m.currentSymbol!==x&&(m=m.destroy()),g&&(m?m[e?"animate":"attr"]({x:g.x,y:g.y}):x&&(i.stateMarkerGraphic=m=d.renderer.symbol(x,g.x,g.y,g.width,g.height,r$(o,n)).add(i.markerGroup),m.currentSymbol=x)),!d.styledMode&&m&&"inactive"!==this.state&&m.attr(i.pointAttribs(this,t))),m&&(m[t&&this.isInside?"show":"hide"](),m.element.point=this,m.addClass(this.getClassName(),!0));let y=r.halo,b=this.graphic||m,v=b?.visibility||"inherit";y?.size&&b&&"hidden"!==v&&!this.isCluster?(p||(i.halo=p=d.renderer.path().add(b.parentGroup)),p.show()[e?"animate":"attr"]({d:this.haloPath(y.size)}),p.attr({class:"highcharts-halo highcharts-color-"+rZ(this.colorIndex,i.colorIndex)+(this.className?" "+this.className:""),visibility:v,zIndex:-1}),p.point=this,d.styledMode||p.attr(rX({fill:this.color||i.color,"fill-opacity":y.opacity},t4.filterUserAttributes(y.attributes||{})))):p?.point?.haloPath&&!p.point.destroyed&&p.animate({d:p.point.haloPath(0)},null,p.hide),rF(this,"afterSetState",{state:t})}haloPath(t){let e=this.pos();return e?this.series.chart.renderer.symbols.circle(rW(e[0],1)-t,e[1]-t,2*t,2*t):[]}}let rQ=rJ,{parse:r0}=tL,{charts:r1,composed:r2,isTouchDevice:r3}=S,{addEvent:r5,attr:r6,css:r9,extend:r4,find:r8,fireEvent:r7,isNumber:ot,isObject:oe,objectEach:oi,offset:os,pick:or,pushUnique:oo,splat:oa}=_;class on{applyInactiveState(t=[]){let e=[];t.forEach(t=>{let i=t.series;e.push(i),i.linkedParent&&e.push(i.linkedParent),i.linkedSeries&&e.push.apply(e,i.linkedSeries),i.navigatorSeries&&e.push(i.navigatorSeries),i.boosted&&i.markerGroup&&e.push.apply(e,this.chart.series.filter(t=>t.markerGroup===i.markerGroup))}),this.chart.series.forEach(t=>{-1===e.indexOf(t)?t.setState("inactive",!0):t.options.inactiveOtherPoints&&t.setAllPointsToState("inactive")})}destroy(){let t=this;this.eventsToUnbind.forEach(t=>t()),this.eventsToUnbind=[],!S.chartCount&&(on.unbindDocumentMouseUp.forEach(t=>t.unbind()),on.unbindDocumentMouseUp.length=0,on.unbindDocumentTouchEnd&&(on.unbindDocumentTouchEnd=on.unbindDocumentTouchEnd())),clearInterval(t.tooltipTimeout),oi(t,function(e,i){t[i]=void 0})}getSelectionMarkerAttrs(t,e){let i={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return r7(this,"getSelectionMarkerAttrs",i,i=>{let s,{chart:r,zoomHor:o,zoomVert:a}=this,{mouseDownX:n=0,mouseDownY:h=0}=r,l=i.attrs;l.x=r.plotLeft,l.y=r.plotTop,l.width=o?1:r.plotWidth,l.height=a?1:r.plotHeight,o&&(l.width=Math.max(1,Math.abs(s=t-n)),l.x=(s>0?0:s)+n),a&&(l.height=Math.max(1,Math.abs(s=e-h)),l.y=(s>0?0:s)+h)}),i}drag(t){let{chart:e}=this,{mouseDownX:i=0,mouseDownY:s=0}=e,{panning:r,panKey:o,selectionMarkerFill:a}=e.options.chart,n=e.plotLeft,h=e.plotTop,l=e.plotWidth,d=e.plotHeight,c=oe(r)?r.enabled:r,p=o&&t[`${o}Key`],g=t.chartX,u=t.chartY,f,m=this.selectionMarker;if((!m||!m.touch)&&(g<n?g=n:g>n+l&&(g=n+l),u<h?u=h:u>h+d&&(u=h+d),this.hasDragged=Math.sqrt(Math.pow(i-g,2)+Math.pow(s-u,2)),this.hasDragged>10)){f=e.isInsidePlot(i-n,s-h,{visiblePlotOnly:!0});let{shapeType:o,attrs:l}=this.getSelectionMarkerAttrs(g,u);(e.hasCartesianSeries||e.mapView)&&this.hasZoom&&f&&!p&&!m&&(this.selectionMarker=m=e.renderer[o](),m.attr({class:"highcharts-selection-marker",zIndex:7}).add(),e.styledMode||m.attr({fill:a||r0("#334eff").setOpacity(.25).get()})),m&&m.attr(l),f&&!m&&c&&e.pan(t,r)}}dragStart(t){let e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY}getSelectionBox(t){let e={args:{marker:t},result:t.getBBox()};return r7(this,"getSelectionBox",e),e.result}drop(t){let e,{chart:i,selectionMarker:s}=this;for(let t of i.axes)t.isPanning&&(t.isPanning=!1,(t.options.startOnTick||t.options.endOnTick||t.series.some(t=>t.boosted))&&(t.forceRedraw=!0,t.setExtremes(t.userMin,t.userMax,!1),e=!0));if(e&&i.redraw(),s&&t){if(this.hasDragged){let e=this.getSelectionBox(s);i.transform({axes:i.axes.filter(t=>t.zoomEnabled&&("xAxis"===t.coll&&this.zoomX||"yAxis"===t.coll&&this.zoomY)),selection:{originalEvent:t,xAxis:[],yAxis:[],...e},from:e})}ot(i.index)&&(this.selectionMarker=s.destroy())}i&&ot(i.index)&&(r9(i.container,{cursor:i._cursor}),i.cancelClick=this.hasDragged>10,i.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])}findNearestKDPoint(t,e,i){let s;return t.forEach(function(t){let r=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y"),o=t.searchPoint(i,r);oe(o,!0)&&o.series&&(!oe(s,!0)||function(t,i){let s,r=t.distX-i.distX,o=t.dist-i.dist,a=i.series.group?.zIndex-t.series.group?.zIndex;return 0!==r&&e?r:0!==o?o:0!==a?a:t.series.index>i.series.index?-1:1}(s,o)>0)&&(s=o)}),s}getChartCoordinatesFromPoint(t,e){let{xAxis:i,yAxis:s}=t.series,r=t.shapeArgs;if(i&&s){let o=t.clientX??t.plotX??0,a=t.plotY||0;return t.isNode&&r&&ot(r.x)&&ot(r.y)&&(o=r.x,a=r.y),e?{chartX:s.len+s.pos-a,chartY:i.len+i.pos-o}:{chartX:o+i.pos,chartY:a+s.pos}}if(r?.x&&r.y)return{chartX:r.x,chartY:r.y}}getChartPosition(){if(this.chartPosition)return this.chartPosition;let{container:t}=this.chart,e=os(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};let{offsetHeight:i,offsetWidth:s}=t;return s>2&&i>2&&(this.chartPosition.scaleX=e.width/s,this.chartPosition.scaleY=e.height/i),this.chartPosition}getCoordinates(t){let e={xAxis:[],yAxis:[]};for(let i of this.chart.axes)e[i.isXAxis?"xAxis":"yAxis"].push({axis:i,value:i.toValue(t[i.horiz?"chartX":"chartY"])});return e}getHoverData(t,e,i,s,r,o){let a=[],n=function(t){return t.visible&&!(!r&&t.directTouch)&&or(t.options.enableMouseTracking,!0)},h=e,l,d={chartX:o?o.chartX:void 0,chartY:o?o.chartY:void 0,shared:r};r7(this,"beforeGetHoverData",d),l=h&&!h.stickyTracking?[h]:i.filter(t=>t.stickyTracking&&(d.filter||n)(t));let c=s&&t||!o?t:this.findNearestKDPoint(l,r,o);return h=c?.series,c&&(r&&!h.noSharedTooltip?(l=i.filter(function(t){return d.filter?d.filter(t):n(t)&&!t.noSharedTooltip})).forEach(function(t){let e=t.options?.nullInteraction,i=r8(t.points,function(t){return t.x===c.x&&(!t.isNull||!!e)});oe(i)&&(t.boosted&&t.boost&&(i=t.boost.getPoint(i)),a.push(i))}):a.push(c)),r7(this,"afterGetHoverData",d={hoverPoint:c}),{hoverPoint:d.hoverPoint,hoverSeries:h,hoverPoints:a}}getPointFromEvent(t){let e=t.target,i;for(;e&&!i;)i=e.point,e=e.parentNode;return i}onTrackerMouseOut(t){let e=this.chart,i=t.relatedTarget,s=e.hoverSeries;this.isDirectTouch=!1,!s||!i||s.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+s.index)&&this.inClass(i,"highcharts-tracker")||s.onMouseOut()}inClass(t,e){let i=t,s;for(;i;){if(s=r6(i,"class")){if(-1!==s.indexOf(e))return!0;if(-1!==s.indexOf("highcharts-container"))return!1}i=i.parentElement}}constructor(t,e){this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!e.chart.events?.click,this.pinchDown=[],this.setDOMEvents(),r7(this,"afterInit")}normalize(t,e){let i=t.touches,s=i?i.length?i.item(0):or(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());let r=s.pageX-e.left,o=s.pageY-e.top;return r4(t,{chartX:Math.round(r/=e.scaleX),chartY:Math.round(o/=e.scaleY)})}onContainerClick(t){let e=this.chart,i=e.hoverPoint,s=this.normalize(t),r=e.plotLeft,o=e.plotTop;!e.cancelClick&&(i&&this.inClass(s.target,"highcharts-tracker")?(r7(i.series,"click",r4(s,{point:i})),e.hoverPoint&&i.firePointEvent("click",s)):(r4(s,this.getCoordinates(s)),e.isInsidePlot(s.chartX-r,s.chartY-o,{visiblePlotOnly:!0})&&r7(e,"click",s)))}onContainerMouseDown(t){let e=(1&(t.buttons||t.button))==1;t=this.normalize(t),S.isFirefox&&0!==t.button&&this.onContainerMouseMove(t),(void 0===t.button||e)&&(this.zoomOption(t),e&&t.preventDefault?.(),this.dragStart(t))}onContainerMouseLeave(t){let{pointer:e}=r1[or(on.hoverChartIndex,-1)]||{};t=this.normalize(t),this.onContainerMouseMove(t),e&&!this.inClass(t.relatedTarget,"highcharts-tooltip")&&(e.reset(),e.chartPosition=void 0)}onContainerMouseEnter(){delete this.chartPosition}onContainerMouseMove(t){let e=this.chart,i=e.tooltip,s=this.normalize(t);this.setHoverChartIndex(t),("mousedown"===e.mouseIsDown||this.touchSelect(s))&&this.drag(s),!e.openMenu&&(this.inClass(s.target,"highcharts-tracker")||e.isInsidePlot(s.chartX-e.plotLeft,s.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!i?.shouldStickOnContact(s)&&(this.inClass(s.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(s))}onDocumentTouchEnd(t){this.onDocumentMouseUp(t)}onContainerTouchMove(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)}onContainerTouchStart(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))}onDocumentMouseMove(t){let e=this.chart,i=e.tooltip,s=this.chartPosition,r=this.normalize(t,s);!s||e.isInsidePlot(r.chartX-e.plotLeft,r.chartY-e.plotTop,{visiblePlotOnly:!0})||i?.shouldStickOnContact(r)||r.target!==e.container.ownerDocument&&this.inClass(r.target,"highcharts-tracker")||this.reset()}onDocumentMouseUp(t){r1[or(on.hoverChartIndex,-1)]?.pointer?.drop(t)}pinch(t){let e=this,{chart:i,hasZoom:s,lastTouches:r}=e,o=[].map.call(t.touches||[],t=>e.normalize(t)),a=o.length,n=1===a&&(e.inClass(t.target,"highcharts-tracker")&&i.runTrackerClick||e.runChartClick),h=i.tooltip,l=1===a&&or(h?.options.followTouchMove,!0);a>1?e.initiated=!0:l&&(e.initiated=!1),s&&e.initiated&&!n&&!1!==t.cancelable&&t.preventDefault(),"touchstart"===t.type?(e.pinchDown=o,e.res=!0,i.mouseDownX=t.chartX):l?this.runPointActions(e.normalize(t)):r&&(r7(i,"touchpan",{originalEvent:t,touches:o},()=>{let e=t=>{let e=t[0],i=t[1]||e;return{x:e.chartX,y:e.chartY,width:i.chartX-e.chartX,height:i.chartY-e.chartY}};i.transform({axes:i.axes.filter(t=>t.zoomEnabled&&(this.zoomHor&&t.horiz||this.zoomVert&&!t.horiz)),to:e(o),from:e(r),trigger:t.type})}),e.res&&(e.res=!1,this.reset(!1,0))),e.lastTouches=o}reset(t,e){let i=this.chart,s=i.hoverSeries,r=i.hoverPoint,o=i.hoverPoints,a=i.tooltip,n=a?.shared?o:r;t&&n&&oa(n).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?a&&n&&oa(n).length&&(a.refresh(n),a.shared&&o?o.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):r&&(r.setState(r.state,!0),i.axes.forEach(function(t){t.crosshair&&r.series[t.coll]===t&&t.drawCrosshair(null,r)}))):(r&&r.onMouseOut(),o&&o.forEach(function(t){t.setState()}),s&&s.onMouseOut(),a&&a.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)}runPointActions(t,e,i){let s=this.chart,r=s.series,o=s.tooltip?.options.enabled?s.tooltip:void 0,a=!!o&&o.shared,n=e||s.hoverPoint,h=n?.series||s.hoverSeries,l=(!t||"touchmove"!==t.type)&&(!!e||h?.directTouch&&this.isDirectTouch),d=this.getHoverData(n,h,r,l,a,t);n=d.hoverPoint,h=d.hoverSeries;let c=d.hoverPoints,p=h?.tooltipOptions.followPointer&&!h.tooltipOptions.split,g=a&&h&&!h.noSharedTooltip;if(n&&(i||n!==s.hoverPoint||o?.isHidden)){if((s.hoverPoints||[]).forEach(function(t){-1===c.indexOf(t)&&t.setState()}),s.hoverSeries!==h&&h.onMouseOver(),this.applyInactiveState(c),(c||[]).forEach(function(t){t.setState("hover")}),s.hoverPoint&&s.hoverPoint.firePointEvent("mouseOut"),!n.series)return;s.hoverPoints=c,s.hoverPoint=n,n.firePointEvent("mouseOver",void 0,()=>{o&&n&&o.refresh(g?c:n,t)})}else if(p&&o&&!o.isHidden){let e=o.getAnchor([{}],t);s.isInsidePlot(e[0],e[1],{visiblePlotOnly:!0})&&o.updatePosition({plotX:e[0],plotY:e[1]})}this.unDocMouseMove||(this.unDocMouseMove=r5(s.container.ownerDocument,"mousemove",t=>r1[on.hoverChartIndex??-1]?.pointer?.onDocumentMouseMove(t)),this.eventsToUnbind.push(this.unDocMouseMove)),s.axes.forEach(function(e){let i,r=e.crosshair?.snap??!0;!r||(i=s.hoverPoint)&&i.series[e.coll]===e||(i=r8(c,t=>t.series?.[e.coll]===e)),i||!r?e.drawCrosshair(t,i):e.hideCrosshair()})}setDOMEvents(){let t=this.chart.container,e=t.ownerDocument;t.onmousedown=this.onContainerMouseDown.bind(this),t.onmousemove=this.onContainerMouseMove.bind(this),t.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(r5(t,"mouseenter",this.onContainerMouseEnter.bind(this)),r5(t,"mouseleave",this.onContainerMouseLeave.bind(this))),on.unbindDocumentMouseUp.some(t=>t.doc===e)||on.unbindDocumentMouseUp.push({doc:e,unbind:r5(e,"mouseup",this.onDocumentMouseUp.bind(this))});let i=this.chart.renderTo.parentElement;for(;i&&"BODY"!==i.tagName;)this.eventsToUnbind.push(r5(i,"scroll",()=>{delete this.chartPosition})),i=i.parentElement;this.eventsToUnbind.push(r5(t,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),r5(t,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),on.unbindDocumentTouchEnd||(on.unbindDocumentTouchEnd=r5(e,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),r5(this.chart,"redraw",this.setPointerCapture.bind(this))}setPointerCapture(){if(!r3)return;let t=this.pointerCaptureEventsToUnbind,e=this.chart,i=e.container,s=or(e.options.tooltip?.followTouchMove,!0)&&e.series.some(t=>t.options.findNearestPointBy.indexOf("y")>-1);!this.hasPointerCapture&&s?(t.push(r5(i,"pointerdown",t=>{t.target?.hasPointerCapture(t.pointerId)&&t.target?.releasePointerCapture(t.pointerId)}),r5(i,"pointermove",t=>{e.pointer?.getPointFromEvent(t)?.onMouseOver(t)})),e.styledMode||r9(i,{"touch-action":"none"}),i.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!s&&(t.forEach(t=>t()),t.length=0,e.styledMode||r9(i,{"touch-action":or(e.options.chart.style?.["touch-action"],"manipulation")}),i.className=i.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}setHoverChartIndex(t){let e=this.chart,i=S.charts[or(on.hoverChartIndex,-1)];if(i&&i!==e){let s={relatedTarget:e.container};t&&!t?.relatedTarget&&Object.assign({},t,s),i.pointer?.onContainerMouseLeave(t||s)}i?.mouseIsDown||(on.hoverChartIndex=e.index)}touch(t,e){let i,{chart:s,pinchDown:r=[]}=this;this.setHoverChartIndex(),1===(t=this.normalize(t)).touches.length?s.isInsidePlot(t.chartX-s.plotLeft,t.chartY-s.plotTop,{visiblePlotOnly:!0})&&!s.openMenu?(e&&this.runPointActions(t),"touchmove"===t.type&&(i=!!r[0]&&Math.pow(r[0].chartX-t.chartX,2)+Math.pow(r[0].chartY-t.chartY,2)>=16),or(i,!0)&&this.pinch(t)):e&&this.reset():2===t.touches.length&&this.pinch(t)}touchSelect(t){return!!(this.chart.zooming.singleTouch&&t.touches&&1===t.touches.length)}zoomOption(t){let e=this.chart,i=e.inverted,s=e.zooming.type||"",r,o;/touch/.test(t.type)&&(s=or(e.zooming.pinchType,s)),this.zoomX=r=/x/.test(s),this.zoomY=o=/y/.test(s),this.zoomHor=r&&!i||o&&i,this.zoomVert=o&&!i||r&&i,this.hasZoom=r||o}}on.unbindDocumentMouseUp=[],function(t){t.compose=function(e){oo(r2,"Core.Pointer")&&r5(e,"beforeRender",function(){this.pointer=new t(this,this.options)})}}(on||(on={}));let oh=on;!function(t){t.setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},t.splice=function(t,e,i,s,r=[]){if(Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice(e,i,...r),array:t};let o=Object.getPrototypeOf(t).constructor,a=t[s?"subarray":"slice"](e,e+i),n=new o(t.length-i+r.length);return n.set(t.subarray(0,e),0),n.set(r,e),n.set(t.subarray(e+i),e+r.length),{removed:a,array:n}}}(c||(c={}));let{setLength:ol,splice:od}=c,{fireEvent:oc,objectEach:op,uniqueKey:og}=_,ou=class{constructor(t={}){this.autoId=!t.id,this.columns={},this.id=t.id||og(),this.modified=this,this.rowCount=0,this.versionTag=og();let e=0;op(t.columns||{},(t,i)=>{this.columns[i]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}applyRowCount(t){this.rowCount=t,op(this.columns,(e,i)=>{e.length!==t&&(this.columns[i]=ol(e,t))})}deleteRows(t,e=1){if(e>0&&t<this.rowCount){let i=0;op(this.columns,(s,r)=>{this.columns[r]=od(s,t,e).array,i=s.length}),this.rowCount=i}oc(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=og()}getColumn(t,e){return this.columns[t]}getColumns(t,e){return(t||Object.keys(this.columns)).reduce((t,e)=>(t[e]=this.columns[e],t),{})}getRow(t,e){return(e||Object.keys(this.columns)).map(e=>this.columns[e]?.[t])}setColumn(t,e=[],i=0,s){this.setColumns({[t]:e},i,s)}setColumns(t,e,i){let s=this.rowCount;op(t,(t,e)=>{this.columns[e]=t.slice(),s=t.length}),this.applyRowCount(s),i?.silent||(oc(this,"afterSetColumns"),this.versionTag=og())}setRow(t,e=this.rowCount,i,s){let{columns:r}=this,o=i?this.rowCount+1:e+1;op(t,(t,a)=>{let n=r[a]||s?.addColumns!==!1&&Array(o);n&&(i?n=od(n,e,0,!0,[t]).array:n[e]=t,r[a]=n)}),o>this.rowCount&&this.applyRowCount(o),s?.silent||(oc(this,"afterSetRows"),this.versionTag=og())}},{extend:of,merge:om,pick:ox}=_;!function(t){function e(t,e,i){let s=this.legendItem=this.legendItem||{},{chart:r,options:o}=this,{baseline:a=0,symbolWidth:n,symbolHeight:h}=t,l=this.symbol||"circle",d=h/2,c=r.renderer,p=s.group,g=a-Math.round((t.fontMetrics?.b||h)*(i?.4:.3)),u={},f,m=o.marker,x=0;if(r.styledMode||(u["stroke-width"]=Math.min(o.lineWidth||0,24),o.dashStyle?u.dashstyle=o.dashStyle:"square"===o.linecap||(u["stroke-linecap"]="round")),s.line=c.path().addClass("highcharts-graph").attr(u).add(p),i&&(s.area=c.path().addClass("highcharts-area").add(p)),u["stroke-linecap"]&&(x=Math.min(s.line.strokeWidth(),n)/2),n){let t=[["M",x,g],["L",n-x,g]];s.line.attr({d:t}),s.area?.attr({d:[...t,["L",n-x,a],["L",x,a]]})}if(m&&!1!==m.enabled&&n){let t=Math.min(ox(m.radius,d),d);0===l.indexOf("url")&&(m=om(m,{width:h,height:h}),t=0),s.symbol=f=c.symbol(l,n/2-t,g-t,2*t,2*t,of({context:"legend"},m)).addClass("highcharts-point").add(p),f.isMarker=!0}}t.areaMarker=function(t,i){e.call(this,t,i,!0)},t.lineMarker=e,t.rectangle=function(t,e){let i=e.legendItem||{},s=t.options,r=t.symbolHeight,o=s.squareSymbol,a=o?r:t.symbolWidth;i.symbol=this.chart.renderer.rect(o?(t.symbolWidth-r)/2:0,t.baseline-r+1,a,r,ox(t.options.symbolRadius,r/2)).addClass("highcharts-point").attr({zIndex:3}).add(i.group)}}(p||(p={}));let oy=p,{defaultOptions:ob}=tM,{extend:ov,extendClass:ok,merge:oM}=_;!function(t){function e(e,i){let s=ob.plotOptions||{},r=i.defaultOptions,o=i.prototype;return o.type=e,o.pointClass||(o.pointClass=rQ),!t.seriesTypes[e]&&(r&&(s[e]=r),t.seriesTypes[e]=i,!0)}t.seriesTypes=S.seriesTypes,t.registerSeriesType=e,t.seriesType=function(i,s,r,o,a){let n=ob.plotOptions||{};if(s=s||"",n[i]=oM(n[s],r),delete t.seriesTypes[i],e(i,ok(t.seriesTypes[s]||function(){},o)),t.seriesTypes[i].prototype.type=i,a){class e extends rQ{}ov(e.prototype,a),t.seriesTypes[i].prototype.pointClass=e}return t.seriesTypes[i]}}(g||(g={}));let ow=g,{animObject:oS,setAnimation:oA}=t$,{defaultOptions:oT}=tM,{registerEventOptions:oC}=sr,{svg:oO,win:oP}=S,{seriesTypes:oE}=ow,{format:oL}=eu,{arrayMax:oD,arrayMin:oI,clamp:oB,correctFloat:oz,crisp:oN,defined:oR,destroyObjectProperties:oW,diffObjects:oH,erase:oX,error:oF,extend:oG,find:oY,fireEvent:oj,getClosestDistance:oU,getNestedProperty:oV,insertItem:o$,isArray:oZ,isNumber:o_,isString:oq,merge:oK,objectEach:oJ,pick:oQ,removeEvent:o0,syncTimeout:o1}=_;class o2{constructor(){this.zoneAxis="y"}init(t,e){let i;oj(this,"init",{options:e}),this.dataTable??(this.dataTable=new ou);let s=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);let r=this.options,o=!1!==r.visible;this.linkedSeries=[],this.bindAxes(),oG(this,{name:r.name,state:"",visible:o,selected:!0===r.selected}),oC(this,r);let a=r.events;(a?.click||r.point?.events?.click||r.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),s.length&&(i=s[s.length-1]),this._i=oQ(i?._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",o$(this,s)),r.dataSorting?.enabled?this.setDataSortingOptions():this.points||this.data||this.setData(r.data,!1),oj(this,"afterInit")}is(t){return oE[t]&&this instanceof oE[t]}bindAxes(){let t,e=this,i=e.options,s=e.chart;oj(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(r){(s[r]||[]).forEach(function(s){t=s.options,(oQ(i[r],0)===s.index||void 0!==i[r]&&i[r]===t.id)&&(o$(e,s.series),e[r]=s,s.isDirty=!0)}),e[r]||e.optionalAxis===r||oF(18,!0,s)})}),oj(this,"afterBindAxes")}hasData(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.dataTable.rowCount>0}hasMarkerChanged(t,e){let i=t.marker,s=e.marker||{};return i&&(s.enabled&&!i.enabled||s.symbol!==i.symbol||s.height!==i.height||s.width!==i.width)}autoIncrement(t){let e,i=this.options,{pointIntervalUnit:s,relativeXValue:r}=this.options,o=this.chart.time,a=this.xIncrement??o.parse(i.pointStart)??0;if(this.pointInterval=e=oQ(this.pointInterval,i.pointInterval,1),r&&o_(t)&&(e*=t),s){let t=o.toParts(a);"day"===s?t[2]+=e:"month"===s?t[1]+=e:"year"===s&&(t[0]+=e),e=o.makeTime.apply(o,t)-a}return r&&o_(t)?a+e:(this.xIncrement=a+e,a)}setDataSortingOptions(){let t=this.options;oG(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),oR(t.pointRange)||(t.pointRange=1)}setOptions(t){let e,i=this.chart,s=i.options.plotOptions,r=i.userOptions||{},o=oK(t),a=i.styledMode,n={plotOptions:s,userOptions:o};oj(this,"setOptions",n);let h=n.plotOptions[this.type],l=r.plotOptions||{},d=l.series||{},c=oT.plotOptions[this.type]||{},p=l[this.type]||{};h.dataLabels=this.mergeArrays(c.dataLabels,h.dataLabels),this.userOptions=n.userOptions;let g=oK(h,s.series,p,o);this.tooltipOptions=oK(oT.tooltip,oT.plotOptions.series?.tooltip,c?.tooltip,i.userOptions.tooltip,l.series?.tooltip,p.tooltip,o.tooltip),this.stickyTracking=oQ(o.stickyTracking,p.stickyTracking,d.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),null===h.marker&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";let u=this.zones=(g.zones||[]).map(t=>({...t}));return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(e={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},a||(e.color=g.negativeColor,e.fillColor=g.negativeFillColor),u.push(e)),u.length&&oR(u[u.length-1].value)&&u.push(a?{}:{color:this.color,fillColor:this.fillColor}),oj(this,"afterSetOptions",{options:g}),g}getName(){return this.options.name??oL(this.chart.options.lang.seriesName,this,this.chart)}getCyclic(t,e,i){let s,r,o=this.chart,a=`${t}Index`,n=`${t}Counter`,h=i?.length||o.options.chart.colorCount;!e&&(oR(r=oQ("color"===t?this.options.colorIndex:void 0,this[a]))?s=r:(o.series.length||(o[n]=0),s=o[n]%h,o[n]+=1),i&&(e=i[s])),void 0!==s&&(this[a]=s),this[t]=e}getColor(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||oT.plotOptions[this.type].color,this.chart.options.colors)}getPointsCollection(){return(this.hasGroupedData?this.points:this.data)||[]}getSymbol(){let t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)}getColumn(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]}findPointIndex(t,e){let i,s,r,{id:o,x:a}=t,n=this.points,h=this.options.dataSorting,l=this.cropStart||0;if(o){let t=this.chart.get(o);t instanceof rQ&&(i=t)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){let e=e=>!e.touched&&e.index===t.index;if(h?.matchByName?e=e=>!e.touched&&e.name===t.name:this.options.relativeXValue&&(e=e=>!e.touched&&e.options.x===t.x),!(i=oY(n,e)))return}return i&&void 0!==(r=i?.index)&&(s=!0),void 0===r&&o_(a)&&(r=this.getColumn("x").indexOf(a,e)),-1!==r&&void 0!==r&&this.cropped&&(r=r>=l?r-l:r),!s&&o_(r)&&n[r]?.touched&&(r=void 0),r}updateData(t,e){let{options:i,requireSorting:s}=this,r=i.dataSorting,o=this.points,a=[],n=t.length===o.length,h,l,d,c,p=!0;if(this.xIncrement=null,t.forEach((t,e)=>{let l,d=oR(t)&&this.pointClass.prototype.optionsToObject.call({series:this},t)||{},{id:p,x:g}=d;p||o_(g)?(-1===(l=this.findPointIndex(d,c))||void 0===l?a.push(t):o[l]&&t!==i.data?.[l]?(o[l].update(t,!1,void 0,!1),o[l].touched=!0,s&&(c=l+1)):o[l]&&(o[l].touched=!0),(!n||e!==l||r?.enabled||this.hasDerivedData)&&(h=!0)):a.push(t)},this),h)for(l=o.length;l--;)(d=o[l])&&!d.touched&&d.remove?.(!1,e);else n&&!r?.enabled?(t.forEach((t,e)=>{t===o[e].y||o[e].destroyed||o[e].update(t,!1,void 0,!1)}),a.length=0):p=!1;if(o.forEach(t=>{t&&(t.touched=!1)}),!p)return!1;a.forEach(t=>{this.addPoint(t,!1,void 0,void 0,!1)},this);let g=this.getColumn("x");return null===this.xIncrement&&g.length&&(this.xIncrement=oD(g),this.autoIncrement()),!0}dataColumnKeys(){return["x",...this.pointArrayMap||["y"]]}setData(t,e=!0,i,s){let r=this.points,o=r?.length||0,a=this.options,n=this.chart,h=a.dataSorting,l=this.xAxis,d=a.turboThreshold,c=this.dataTable,p=this.dataColumnKeys(),g=this.pointValKey||"y",u=(this.pointArrayMap||[]).length,f=a.keys,m,x,y=0,b=1,v;n.options.chart.allowMutatingData||(a.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,v=oK(!0,t));let k=(t=v||t||[]).length;if(h?.enabled&&(t=this.sortData(t)),n.options.chart.allowMutatingData&&!1!==s&&k&&o&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(x=this.updateData(t,i)),!x){this.xIncrement=null,this.colorCounter=0;let e=d&&k>d;if(e){let i=this.getFirstValidPoint(t),s=this.getFirstValidPoint(t,k-1,-1),r=t=>!!(oZ(t)&&(f||o_(t[0])));if(o_(i)&&o_(s)){let e=[],i=[];for(let s of t)e.push(this.autoIncrement()),i.push(s);c.setColumns({x:e,[g]:i})}else if(r(i)&&r(s)){if(u){let e=+(i.length===u),s=Array(p.length).fill(0).map(()=>[]);for(let i of t){e&&s[0].push(this.autoIncrement());for(let t=e;t<=u;t++)s[t]?.push(i[t-e])}c.setColumns(p.reduce((t,e,i)=>(t[e]=s[i],t),{}))}else{f&&(y=f.indexOf("x"),b=f.indexOf("y"),y=y>=0?y:0,b=b>=0?b:1),1===i.length&&(b=0);let e=[],s=[];if(y===b)for(let i of t)e.push(this.autoIncrement()),s.push(i[b]);else for(let i of t)e.push(i[y]),s.push(i[b]);c.setColumns({x:e,[g]:s})}}else e=!1}if(!e){let e=p.reduce((t,e)=>(t[e]=[],t),{});for(m=0;m<k;m++){let i=this.pointClass.prototype.applyOptions.apply({series:this},[t[m]]);for(let t of p)e[t][m]=i[t]}c.setColumns(e)}for(oq(this.getColumn("y")[0])&&oF(14,!0,n),this.data=[],this.options.data=this.userOptions.data=t,m=o;m--;)r[m]?.destroy();l&&(l.minRange=l.userMinRange),this.isDirty=n.isDirtyBox=!0,this.isDirtyData=!!r,i=!1}"point"===a.legendType&&(this.processData(),this.generatePoints()),e&&n.redraw(i)}sortData(t){let e=this,i=e.options.dataSorting.sortKey||"y",s=function(t,e){return oR(e)&&t.pointClass.prototype.optionsToObject.call({series:t},e)||{}};return t.forEach(function(i,r){t[r]=s(e,i),t[r].index=r},this),t.concat().sort((t,e)=>{let s=oV(i,t),r=oV(i,e);return r<s?-1:+(r>s)}).forEach(function(t,e){t.x=e},this),e.linkedSeries&&e.linkedSeries.forEach(function(e){let i=e.options,r=i.data;!i.dataSorting?.enabled&&r&&(r.forEach(function(i,o){r[o]=s(e,i),t[o]&&(r[o].x=t[o].x,r[o].index=o)}),e.setData(r,!1))}),t}getProcessedData(t){let e=this,{dataTable:i,isCartesian:s,options:r,xAxis:o}=e,a=r.cropThreshold,n=t||e.getExtremesFromAll,h=o?.logarithmic,l=i.rowCount,d,c,p=0,g,u,f,m=e.getColumn("x"),x=i,y=!1;return o&&(u=(g=o.getExtremes()).min,f=g.max,y=!!(o.categories&&!o.names.length),s&&e.sorted&&!n&&(!a||l>a||e.forceCrop)&&(m[l-1]<u||m[0]>f?x=new ou:e.getColumn(e.pointValKey||"y").length&&(m[0]<u||m[l-1]>f)&&(x=(d=this.cropData(i,u,f)).modified,p=d.start,c=!0))),m=x.getColumn("x")||[],{modified:x,cropped:c,cropStart:p,closestPointRange:oU([h?m.map(h.log2lin):m],()=>e.requireSorting&&!y&&oF(15,!1,e.chart))}}processData(t){let e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;let s=this.getProcessedData();i.modified=s.modified,this.cropped=s.cropped,this.cropStart=s.cropStart,this.closestPointRange=this.basePointRange=s.closestPointRange,oj(this,"afterProcessData")}cropData(t,e,i){let s=t.getColumn("x",!0)||[],r=s.length,o={},a,n,h=0,l=r;for(a=0;a<r;a++)if(s[a]>=e){h=Math.max(0,a-1);break}for(n=a;n<r;n++)if(s[n]>i){l=n+1;break}for(let e of this.dataColumnKeys()){let i=t.getColumn(e,!0);i&&(o[e]=i.slice(h,l))}return{modified:new ou({columns:o}),start:h,end:l}}generatePoints(){let t=this.options,e=this.processedData||t.data,i=this.dataTable.modified,s=this.getColumn("x",!0),r=this.pointClass,o=i.rowCount,a=this.cropStart||0,n=this.hasGroupedData,h=t.keys,l=[],d=t.dataGrouping?.groupAll?a:0,c=this.xAxis?.categories,p=this.pointArrayMap||["y"],g=this.dataColumnKeys(),u,f,m,x,y=this.data,b;if(!y&&!n){let t=[];t.length=e?.length||0,y=this.data=t}for(h&&n&&(this.options.keys=!1),x=0;x<o;x++)f=a+x,n?((m=new r(this,i.getRow(x,g)||[])).dataGroup=this.groupMap[d+x],m.dataGroup?.options&&(m.options=m.dataGroup.options,oG(m,m.dataGroup.options),delete m.dataLabels)):(m=y[f],b=e?e[f]:i.getRow(x,p),m||void 0===b||(y[f]=m=new r(this,b,s[x]))),m&&(m.index=n?d+x:f,l[x]=m,m.category=c?.[m.x]??m.x,m.key=m.name??m.category);if(this.options.keys=h,y&&(o!==(u=y.length)||n))for(x=0;x<u;x++)x!==a||n||(x+=o),y[x]&&(y[x].destroyElements(),y[x].plotX=void 0);this.data=y,this.points=l,oj(this,"afterGeneratePoints")}getXExtremes(t){return{min:oI(t),max:oD(t)}}getExtremes(t,e){let{xAxis:i,yAxis:s}=this,r=e||this.getExtremesFromAll||this.options.getExtremesFromAll,o=r&&this.cropped?this.dataTable:this.dataTable.modified,a=o.rowCount,n=t||this.stackedYData,h=n?[n]:(this.keysAffectYAxis||this.pointArrayMap||["y"])?.map(t=>o.getColumn(t,!0)||[])||[],l=this.getColumn("x",!0),d=[],c=this.requireSorting&&!this.is("column")?1:0,p=!!s&&s.positiveValuesOnly,g=r||this.cropped||!i,u,f,m,x=0,y=0;for(i&&(x=(u=i.getExtremes()).min,y=u.max),m=0;m<a;m++)if(f=l[m],g||(l[m+c]||f)>=x&&(l[m-c]||f)<=y)for(let t of h){let e=t[m];o_(e)&&(e>0||!p)&&d.push(e)}let b={activeYData:d,dataMin:oI(d),dataMax:oD(d)};return oj(this,"afterGetExtremes",{dataExtremes:b}),b}applyExtremes(){let t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t}getFirstValidPoint(t,e=0,i=1){let s=t.length,r=e;for(;r>=0&&r<s;){if(oR(t[r]))return t[r];r+=i}}translate(){this.generatePoints();let t=this.options,e=t.stacking,i=this.xAxis,s=this.enabledDataSorting,r=this.yAxis,o=this.points,a=o.length,n=this.pointPlacementToXValue(),h=!!n,l=t.threshold,d=t.startFromThreshold?l:0,c=t?.nullInteraction&&r.len,p,g,u,f,m=Number.MAX_VALUE;function x(t){return oB(t,-1e9,1e9)}for(p=0;p<a;p++){let t,a=o[p],y=a.x,b,v,k=a.y,M=a.low,w=e&&r.stacking?.stacks[(this.negStacks&&k<(d?0:l)?"-":"")+this.stackKey];a.plotX=o_(g=i.translate(y,!1,!1,!1,!0,n))?oz(x(g)):void 0,e&&this.visible&&w&&w[y]&&(f=this.getStackIndicator(f,y,this.index),!a.isNull&&f.key&&(v=(b=w[y]).points[f.key]),b&&oZ(v)&&(M=v[0],k=v[1],M===d&&f.key===w[y].base&&(M=oQ(o_(l)?l:r.min)),r.positiveValuesOnly&&oR(M)&&M<=0&&(M=void 0),a.total=a.stackTotal=oQ(b.total),a.percentage=oR(a.y)&&b.total?a.y/b.total*100:void 0,a.stackY=k,this.irregularWidths||b.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),a.yBottom=oR(M)?x(r.translate(M,!1,!0,!1,!0)):void 0,this.dataModify&&(k=this.dataModify.modifyValue(k,p)),o_(k)&&void 0!==a.plotX?t=o_(t=r.translate(k,!1,!0,!1,!0))?x(t):void 0:!o_(k)&&c&&(t=c),a.plotY=t,a.isInside=this.isPointInside(a),a.clientX=h?oz(i.translate(y,!1,!1,!1,!0,n)):g,a.negative=(a.y||0)<(l||0),a.isNull||!1===a.visible||(void 0!==u&&(m=Math.min(m,Math.abs(g-u))),u=g),a.zone=this.zones.length?a.getZone():void 0,!a.graphic&&this.group&&s&&(a.isNew=!0)}this.closestPointRangePx=m,oj(this,"afterTranslate")}getValidPoints(t,e,i){let s=this.chart;return(t||this.points||[]).filter(function(t){let{plotX:r,plotY:o}=t;return!!((i||!t.isNull&&o_(o))&&(!e||s.isInsidePlot(r,o,{inverted:s.inverted})))&&!1!==t.visible})}getSharedClipKey(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey}setClip(){let{chart:t,group:e,markerGroup:i}=this,s=t.sharedClips,r=t.renderer,o=t.getClipBox(this),a=this.getSharedClipKey(),n=s[a];n?n.animate(o):s[a]=n=r.clipRect(o),e&&e.clip(!1===this.options.clip?void 0:n),i&&i.clip()}animate(t){let{chart:e,group:i,markerGroup:s}=this,r=e.inverted,o=oS(this.options.animation),a=[this.getSharedClipKey(),o.duration,o.easing,o.defer].join(","),n=e.sharedClips[a],h=e.sharedClips[a+"m"];if(t&&i){let t=e.getClipBox(this);if(n)n.attr("height",t.height);else{t.width=0,r&&(t.x=e.plotHeight),n=e.renderer.clipRect(t),e.sharedClips[a]=n;let i={x:-99,y:-99,width:r?e.plotWidth+199:99,height:r?99:e.plotHeight+199};h=e.renderer.clipRect(i),e.sharedClips[a+"m"]=h}i.clip(n),s?.clip(h)}else if(n&&!n.hasClass("highcharts-animating")){let t=e.getClipBox(this),i=o.step;(s?.element.childNodes.length||e.series.length>1)&&(o.step=function(t,e){i&&i.apply(e,arguments),"width"===e.prop&&h?.element&&h.attr(r?"height":"width",t+99)}),n.addClass("highcharts-animating").animate(t,o)}}afterAnimate(){this.setClip(),oJ(this.chart.sharedClips,(t,e,i)=>{t&&!this.chart.container.querySelector(`[clip-path="url(#${t.id})"]`)&&(t.destroy(),delete i[e])}),this.finishedAnimating=!0,oj(this,"afterAnimate")}drawPoints(t=this.points){let e,i,s,r,o,a,n,h=this.chart,l=h.styledMode,{colorAxis:d,options:c}=this,p=c.marker,g=c.nullInteraction,u=this[this.specialGroup||"markerGroup"],f=this.xAxis,m=oQ(p.enabled,!f||!!f.isRadial||null,this.closestPointRangePx>=p.enabledThreshold*p.radius);if(!1!==p.enabled||this._hasPointMarkers)for(e=0;e<t.length;e++){r=(s=(i=t[e]).graphic)?"animate":"attr",o=i.marker||{},a=!!i.marker;let c=i.isNull;if((m&&!oR(o.enabled)||o.enabled)&&(!c||g)&&!1!==i.visible){let t=oQ(o.symbol,this.symbol,"rect");n=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=f.reversed?-(n.width||0):f.width);let e=!1!==i.isInside;if(!s&&e&&((n.width||0)>0||i.hasImage)&&(i.graphic=s=h.renderer.symbol(t,n.x,n.y,n.width,n.height,a?o:p).add(u),this.enabledDataSorting&&h.hasRendered&&(s.attr({x:i.startXPos}),r="animate")),s&&"animate"===r&&s[e?"show":"hide"](e).animate(n),s){let t=this.pointAttribs(i,l||!i.selected?void 0:"select");l?d&&s.css({fill:t.fill}):s[r](t)}s&&s.addClass(i.getClassName(),!0)}else s&&(i.graphic=s.destroy())}}markerAttribs(t,e){let i=this.options,s=i.marker,r=t.marker||{},o=r.symbol||s.symbol,a={},n,h,l=oQ(r.radius,s?.radius);e&&(n=s.states[e],h=r.states&&r.states[e],l=oQ(h?.radius,n?.radius,l&&l+(n?.radiusPlus||0))),t.hasImage=o&&0===o.indexOf("url"),t.hasImage&&(l=0);let d=t.pos();return o_(l)&&d&&(i.crisp&&(d[0]=oN(d[0],t.hasImage?0:"rect"===o?s?.lineWidth||0:1)),a.x=d[0]-l,a.y=d[1]-l),l&&(a.width=a.height=2*l),a}pointAttribs(t,e){let i=this.options,s=i.marker,r=t?.options,o=r?.marker||{},a=r?.color,n=t?.color,h=t?.zone?.color,l,d,c=this.color,p,g,u=oQ(o.lineWidth,s.lineWidth),f=t?.isNull&&i.nullInteraction?0:1;return c=a||h||n||c,p=o.fillColor||s.fillColor||c,g=o.lineColor||s.lineColor||c,e=e||"normal",l=s.states[e]||{},u=oQ((d=o.states&&o.states[e]||{}).lineWidth,l.lineWidth,u+oQ(d.lineWidthPlus,l.lineWidthPlus,0)),p=d.fillColor||l.fillColor||p,g=d.lineColor||l.lineColor||g,{stroke:g,"stroke-width":u,fill:p,opacity:f=oQ(d.opacity,l.opacity,f)}}destroy(t){let e,i,s=this,r=s.chart,o=/AppleWebKit\/533/.test(oP.navigator.userAgent),a=s.data||[];for(oj(s,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(s.axisTypes||[]).forEach(function(t){i=s[t],i?.series&&(oX(i.series,s),i.isDirty=i.forceRedraw=!0)}),s.legendItem&&s.chart.legend.destroyItem(s),e=a.length;e--;)a[e]?.destroy?.();for(let t of s.zones)oW(t,void 0,!0);_.clearTimeout(s.animationTimeout),oJ(s,function(t,e){t instanceof e0&&!t.survive&&t[o&&"group"===e?"hide":"destroy"]()}),r.hoverSeries===s&&(r.hoverSeries=void 0),oX(r.series,s),r.orderItems("series"),oJ(s,function(e,i){t&&"hcEvents"===i||delete s[i]})}applyZones(){let{area:t,chart:e,graph:i,zones:s,points:r,xAxis:o,yAxis:a,zoneAxis:n}=this,{inverted:h,renderer:l}=e,d=this[`${n}Axis`],{isXAxis:c,len:p=0,minPointOffset:g=0}=d||{},u=(i?.strokeWidth()||0)/2+1,f=(t,e=0,i=0)=>{h&&(i=p-i);let{translated:s=0,lineClip:r}=t,o=i-s;r?.push(["L",e,Math.abs(o)<u?i-u*(o<=0?-1:1):s])};if(s.length&&(i||t)&&d&&o_(d.min)){let e=d.getExtremes().max+g,u=t=>{t.forEach((e,i)=>{("M"===e[0]||"L"===e[0])&&(t[i]=[e[0],c?p-e[1]:e[1],c?e[2]:p-e[2]])})};if(s.forEach(t=>{t.lineClip=[],t.translated=oB(d.toPixels(oQ(t.value,e),!0)||0,0,p)}),i&&!this.showLine&&i.hide(),t&&t.hide(),"y"===n&&r.length<o.len)for(let t of r){let{plotX:e,plotY:i,zone:r}=t,o=r&&s[s.indexOf(r)-1];r&&f(r,e,i),o&&f(o,e,i)}let m=[],x=d.toPixels(d.getExtremes().min-g,!0);s.forEach(e=>{let s=e.lineClip||[],r=Math.round(e.translated||0);o.reversed&&s.reverse();let{clip:n,simpleClip:d}=e,p=0,g=0,f=o.len,y=a.len;c?(p=r,f=x):(g=r,y=x);let b=[["M",p,g],["L",f,g],["L",f,y],["L",p,y],["Z"]],v=[b[0],...s,b[1],b[2],...m,b[3],b[4]];m=s.reverse(),x=r,h&&(u(v),t&&u(b)),n?(n.animate({d:v}),d?.animate({d:b})):(n=e.clip=l.path(v),t&&(d=e.simpleClip=l.path(b))),i&&e.graph?.clip(n),t&&e.area?.clip(d)})}else this.visible&&(i&&i.show(),t&&t.show())}plotGroup(t,e,i,s,r){let o=this[t],a=!o,n={visibility:i,zIndex:s||.1};return oR(this.opacity)&&!this.chart.styledMode&&"inactive"!==this.state&&(n.opacity=this.opacity),o||(this[t]=o=this.chart.renderer.g().add(r)),o.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(oR(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(o.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),o.attr(n)[a?"attr":"animate"](this.getPlotBox(e)),o}getPlotBox(t){let e=this.xAxis,i=this.yAxis,s=this.chart,r=s.inverted&&!s.polar&&e&&this.invertible&&"series"===t;return s.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:s.plotLeft,translateY:i?i.top:s.plotTop,rotation:90*!!r,rotationOriginX:r?(e.len-i.len)/2:0,rotationOriginY:r?(e.len+i.len)/2:0,scaleX:r?-1:1,scaleY:1}}removeEvents(t){let{eventsToUnbind:e}=this;t||o0(this),e.length&&(e.forEach(t=>{t()}),e.length=0)}render(){let t=this,{chart:e,options:i,hasRendered:s}=t,r=oS(i.animation),o=t.visible?"inherit":"hidden",a=i.zIndex,n=e.seriesGroup,h=t.finishedAnimating?0:r.duration;oj(this,"render"),t.plotGroup("group","series",o,a,n),t.markerGroup=t.plotGroup("markerGroup","markers",o,a,n),!1!==i.clip&&t.setClip(),h&&t.animate?.(!0),t.drawGraph&&(t.drawGraph(),t.applyZones()),t.visible&&t.drawPoints(),t.drawDataLabels?.(),t.redrawPoints?.(),i.enableMouseTracking&&t.drawTracker?.(),h&&t.animate?.(),s||(h&&r.defer&&(h+=r.defer),t.animationTimeout=o1(()=>{t.afterAnimate()},h||0)),t.isDirty=!1,t.hasRendered=!0,oj(t,"afterRender")}redraw(){let t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree}reserveSpace(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries}searchPoint(t,e){let{xAxis:i,yAxis:s}=this,r=this.chart.inverted;return this.searchKDTree({clientX:r?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:r?s.len-t.chartX+s.pos:t.chartY-s.pos},e,t)}buildKDTree(t){this.buildingKdTree=!0;let e=this,i=e.options,s=i.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,o1(function(){e.kdTree=function t(i,s,r){let o,a,n=i?.length;if(n)return o=e.kdAxisArray[s%r],i.sort((t,e)=>(t[o]||0)-(e[o]||0)),{point:i[a=Math.floor(n/2)],left:t(i.slice(0,a),s+1,r),right:t(i.slice(a+1),s+1,r)}}(e.getValidPoints(void 0,!e.directTouch,i?.nullInteraction),s,s),e.buildingKdTree=!1},i.kdNow||t?.type==="touchstart"?0:1)}searchKDTree(t,e,i,s,r){let o=this,[a,n]=this.kdAxisArray,h=e?"distX":"dist",l=(o.options.findNearestPointBy||"").indexOf("y")>-1?2:1,d=!!o.isBubble,c=s||((t,e,i)=>{let s=t[i]||0,r=e[i]||0;return[s===r&&t.index>e.index||s<r?t:e,!1]}),p=r||((t,e)=>t<e);if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function t(e,i,s,r){let l=i.point,g=o.kdAxisArray[s%r],u=l,f=!1;!function(t,e){let i=t[a],s=e[a],r=oR(i)&&oR(s)?i-s:null,o=t[n],h=e[n],l=oR(o)&&oR(h)?o-h:0,c=d&&e.marker?.radius||0;e.dist=Math.sqrt((r&&r*r||0)+l*l)-c,e.distX=oR(r)?Math.abs(r)-c:Number.MAX_VALUE}(e,l);let m=(e[g]||0)-(l[g]||0)+(d&&l.marker?.radius||0),x=m<0?"left":"right",y=m<0?"right":"left";return i[x]&&([u,f]=c(l,t(e,i[x],s+1,r),h)),i[y]&&p(Math.sqrt(m*m),u[h],f)&&(u=c(u,t(e,i[y],s+1,r),h)[0]),u}(t,this.kdTree,l,l)}pointPlacementToXValue(){let{options:t,xAxis:e}=this,i=t.pointPlacement;return"between"===i&&(i=e.reversed?-.5:.5),o_(i)?i*(t.pointRange||e.pointRange):0}isPointInside(t){let{chart:e,xAxis:i,yAxis:s}=this,{plotX:r=-1,plotY:o=-1}=t;return o>=0&&o<=(s?s.len:e.plotHeight)&&r>=0&&r<=(i?i.len:e.plotWidth)}drawTracker(){let t=this,e=t.options,i=e.trackByArea,s=[].concat((i?t.areaPath:t.graphPath)||[]),r=t.chart,o=r.pointer,a=r.renderer,n=r.options.tooltip?.snap||0,h=()=>{e.enableMouseTracking&&r.hoverSeries!==t&&t.onMouseOver()},l="rgba(192,192,192,"+(oO?1e-4:.002)+")",d=t.tracker;d?d.attr({d:s}):t.graph&&(t.tracker=d=a.path(s).attr({visibility:t.visible?"inherit":"hidden",zIndex:2}).addClass(i?"highcharts-tracker-area":"highcharts-tracker-line").add(t.group),r.styledMode||d.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:l,fill:i?l:"none","stroke-width":t.graph.strokeWidth()+(i?0:2*n)}),[t.tracker,t.markerGroup,t.dataLabelsGroup].forEach(t=>{t&&(t.addClass("highcharts-tracker").on("mouseover",h).on("mouseout",t=>{o?.onTrackerMouseOut(t)}),e.cursor&&!r.styledMode&&t.css({cursor:e.cursor}),t.on("touchstart",h))})),oj(this,"afterDrawTracker")}addPoint(t,e,i,s,r){let o,a,n=this.options,{chart:h,data:l,dataTable:d,xAxis:c}=this,p=c?.hasNames&&c.names,g=n.data,u=this.getColumn("x");e=oQ(e,!0);let f={series:this};this.pointClass.prototype.applyOptions.apply(f,[t]);let m=f.x;if(a=u.length,this.requireSorting&&m<u[a-1])for(o=!0;a&&u[a-1]>m;)a--;d.setRow(f,a,!0,{addColumns:!1}),p&&f.name&&(p[m]=f.name),g?.splice(a,0,t),(o||this.processedData)&&(this.data.splice(a,0,null),this.processData()),"point"===n.legendType&&this.generatePoints(),i&&(l[0]&&l[0].remove?l[0].remove(!1):([l,g].filter(oR).forEach(t=>{t.shift()}),d.deleteRows(0))),!1!==r&&oj(this,"addPoint",{point:f}),this.isDirty=!0,this.isDirtyData=!0,e&&h.redraw(s)}removePoint(t,e,i){let s=this,{chart:r,data:o,points:a,dataTable:n}=s,h=o[t],l=function(){[a?.length===o.length?a:void 0,o,s.options.data].filter(oR).forEach(e=>{e.splice(t,1)}),n.deleteRows(t),h?.destroy(),s.isDirty=!0,s.isDirtyData=!0,e&&r.redraw()};oA(i,r),e=oQ(e,!0),h?h.firePointEvent("remove",null,l):l()}remove(t,e,i,s){let r=this,o=r.chart;function a(){r.destroy(s),o.isDirtyLegend=o.isDirtyBox=!0,o.linkSeries(s),oQ(t,!0)&&o.redraw(e)}!1!==i?oj(r,"remove",null,a):a()}update(t,e){oj(this,"update",{options:t=oH(t,this.userOptions)});let i=this,s=i.chart,r=i.userOptions,o=i.initialType||i.type,a=s.options.plotOptions,n=oE[o].prototype,h=i.finishedAnimating&&{animation:!1},l={},d,c,p=o2.keepProps.slice(),g=t.type||r.type||s.options.chart.type,u=!(this.hasDerivedData||g&&g!==this.type||void 0!==t.keys||void 0!==t.pointStart||void 0!==t.pointInterval||void 0!==t.relativeXValue||t.joinBy||t.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(t=>i.hasOptionChanged(t)));g=g||o,u?(p.push.apply(p,o2.keepPropsForPoints),!1!==t.visible&&p.push("area","graph"),i.parallelArrays.forEach(function(t){p.push(t+"Data")}),t.data&&(t.dataSorting&&oG(i.options.dataSorting,t.dataSorting),this.setData(t.data,!1))):this.dataTable.modified=this.dataTable,t=oK(r,{index:void 0===r.index?i.index:r.index,pointStart:a?.series?.pointStart??r.pointStart??i.getColumn("x")[0]},!u&&{data:i.options.data},t,h),u&&t.data&&(t.data=i.options.data),(p=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(p)).forEach(function(t){p[t]=i[t],delete i[t]});let f=!1;if(oE[g]){if(f=g!==i.type,i.remove(!1,!1,!1,!0),f){if(s.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(i,oE[g].prototype);else{let t=Object.hasOwnProperty.call(i,"hcEvents")&&i.hcEvents;for(c in n)i[c]=void 0;oG(i,oE[g].prototype),t?i.hcEvents=t:delete i.hcEvents}}}else oF(17,!0,s,{missingModuleFor:g});if(p.forEach(function(t){i[t]=p[t]}),i.init(s,t),u&&this.points)for(let t of(!1===(d=i.options).visible?(l.graphic=1,l.dataLabel=1):(this.hasMarkerChanged(d,r)&&(l.graphic=1),i.hasDataLabels?.()||(l.dataLabel=1)),this.points))t?.series&&(t.resolveColor(),Object.keys(l).length&&t.destroyElements(l),!1===d.showInLegend&&t.legendItem&&s.legend.destroyItem(t));i.initialType=o,s.linkSeries(),s.setSortedData(),f&&i.linkedSeries.length&&(i.isDirtyData=!0),oj(this,"afterUpdate"),oQ(e,!0)&&s.redraw(!!u&&void 0)}setName(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0}hasOptionChanged(t){let e=this.chart,i=this.options[t],s=e.options.plotOptions,r=this.userOptions[t],o=oQ(s?.[this.type]?.[t],s?.series?.[t]);return r&&!oR(o)?i!==r:i!==oQ(o,i)}onMouseOver(){let t=this.chart,e=t.hoverSeries,i=t.pointer;i?.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&oj(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this}onMouseOut(){let t=this.options,e=this.chart,i=e.tooltip,s=e.hoverPoint;e.hoverSeries=null,s&&s.onMouseOut(),this&&t.events.mouseOut&&oj(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(t){t.setState("",!0)})}setState(t,e){let i=this,s=i.options,r=i.graph,o=s.inactiveOtherPoints,a=s.states,n=oQ(a[t||"normal"]&&a[t||"normal"].animation,i.chart.options.chart.animation),h=s.lineWidth,l=s.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(a[t]&&!1===a[t].enabled)return;if(t&&(h=a[t].lineWidth||h+(a[t].lineWidthPlus||0),l=oQ(a[t].opacity,l)),r&&!r.dashstyle&&o_(h))for(let t of[r,...this.zones.map(t=>t.graph)])t?.animate({"stroke-width":h},n);o||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:l},n)})}e&&o&&i.points&&i.setAllPointsToState(t||void 0)}setAllPointsToState(t){this.points.forEach(function(e){e.setState&&e.setState(t)})}setVisible(t,e){let i=this,s=i.chart,r=s.options.chart.ignoreHiddenSeries,o=i.visible;i.visible=t=i.options.visible=i.userOptions.visible=void 0===t?!o:t;let a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(t=>{i[t]?.[a]()}),(s.hoverSeries===i||s.hoverPoint?.series===i)&&i.onMouseOut(),i.legendItem&&s.legend.colorizeItem(i,t),i.isDirty=!0,i.options.stacking&&s.series.forEach(t=>{t.options.stacking&&t.visible&&(t.isDirty=!0)}),i.linkedSeries.forEach(e=>{e.setVisible(t,!1)}),r&&(s.isDirtyBox=!0),oj(i,a),!1!==e&&s.redraw()}show(){this.setVisible(!0)}hide(){this.setVisible(!1)}select(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),oj(this,t?"select":"unselect")}shouldShowTooltip(t,e,i={}){return i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)}drawLegendSymbol(t,e){oy[this.options.legendSymbol||"rectangle"]?.call(this,t,e)}}o2.defaultOptions={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){let{numberFormatter:t}=this.series.chart;return"number"!=typeof this.y?"":t(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},o2.types=ow.seriesTypes,o2.registerType=ow.registerSeriesType,o2.keepProps=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],o2.keepPropsForPoints=["data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"],oG(o2.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:rQ,requireSorting:!0,sorted:!0}),ow.series=o2;let o3=o2,{animObject:o5,setAnimation:o6}=t$,{registerEventOptions:o9}=sr,{composed:o4,marginNames:o8}=S,{distribute:o7}=ev,{format:at}=eu,{addEvent:ae,createElement:ai,css:as,defined:ar,discardElement:ao,find:aa,fireEvent:an,isNumber:ah,merge:al,pick:ad,pushUnique:ac,relativeLength:ap,stableSort:ag,syncTimeout:au}=_;class af{constructor(t,e){this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),o9(this,e),ae(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),ae(this.chart,"render",()=>{this.options.enabled&&this.proximate&&(this.proximatePositions(),this.positionItems())})}setOptions(t){let e=ad(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=al(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=ad(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted,this.baseline=void 0}update(t,e){let i=this.chart;this.setOptions(al(!0,this.options,t)),"events"in this.options&&o9(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,ad(e,!0)&&i.redraw(),an(this,"afterUpdate",{redraw:e})}colorizeItem(t,e){let i=t.color,{area:s,group:r,label:o,line:a,symbol:n}=t.legendItem||{};if((t instanceof o3||t instanceof rQ)&&(t.color=t.options?.legendSymbolColor||i),r?.[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){let{itemHiddenStyle:i={}}=this,r=i.color,{fillColor:h,fillOpacity:l,lineColor:d,marker:c}=t.options,p=t=>(!e&&(t.fill&&(t.fill=r),t.stroke&&(t.stroke=r)),t);o?.css(al(e?this.itemStyle:i)),a?.attr(p({stroke:d||t.color})),n&&n.attr(p(c&&n.isMarker?t.pointAttribs():{fill:t.color})),s?.attr(p({fill:h||t.color,"fill-opacity":h?1:l??.75}))}t.color=i,an(this,"afterColorizeItem",{item:t,visible:e})}positionItems(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()}positionItem(t){let{group:e,x:i=0,y:s=0}=t.legendItem||{},r=this.options,o=r.symbolPadding,a=!r.rtl,n=t.checkbox;if(e?.element){let r={translateX:a?i:this.legendWidth-i-2*o-4,translateY:s};e[ar(e.translateY)?"animate":"attr"](r,void 0,()=>{an(this,"afterPositionItem",{item:t})})}n&&(n.x=i,n.y=s)}destroyItem(t){let e=t.checkbox,i=t.legendItem||{};for(let t of["group","label","line","symbol"])i[t]&&(i[t]=i[t].destroy());e&&ao(e),t.legendItem=void 0}destroy(){for(let t of this.getAllItems())this.destroyItem(t);for(let t of["clipRect","up","down","pager","nav","box","title","group"])this[t]&&(this[t]=this[t].destroy());this.display=null}positionCheckboxes(){let t,e=this.group?.alignAttr,i=this.clipHeight||this.legendHeight,s=this.titleHeight;e&&(t=e.translateY,this.allItems.forEach(function(r){let o,a=r.checkbox;a&&(o=t+s+a.y+(this.scrollOffset||0)+3,as(a,{left:e.translateX+r.checkboxOffset+a.x-20+"px",top:o+"px",display:this.proximate||o>t-6&&o<t+i-6?"":"none"}))},this))}renderTitle(){let t=this.options,e=this.padding,i=t.title,s,r=0;i.text&&(this.title||(this.title=this.chart.renderer.label(i.text,e-3,e-4,void 0,void 0,void 0,t.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(i.style),this.title.add(this.group)),i.width||this.title.css({width:this.maxLegendWidth+"px"}),r=(s=this.title.getBBox()).height,this.offsetWidth=s.width,this.contentGroup.attr({translateY:r})),this.titleHeight=r}setText(t){let e=this.options;t.legendItem.label.attr({text:e.labelFormat?at(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})}renderItem(t){let e=t.legendItem=t.legendItem||{},i=this.chart,s=i.renderer,r=this.options,o="horizontal"===r.layout,a=this.symbolWidth,n=r.symbolPadding||0,h=this.itemStyle,l=this.itemHiddenStyle,d=o?ad(r.itemDistance,20):0,c=!r.rtl,p=!t.series,g=!p&&t.series.drawLegendSymbol?t.series:t,u=g.options,f=!!this.createCheckboxForItem&&u&&u.showCheckbox,m=r.useHTML,x=t.options.className,y=e.label,b=a+n+d+20*!!f;!y&&(e.group=s.g("legend-item").addClass("highcharts-"+g.type+"-series highcharts-color-"+t.colorIndex+(x?" "+x:"")+(p?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),e.label=y=s.text("",c?a+n:-n,this.baseline||0,m),i.styledMode||y.css(al(t.visible?h:l)),y.attr({align:c?"left":"right",zIndex:2}).add(e.group),!this.baseline&&(this.fontMetrics=s.fontMetrics(y),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,y.attr("y",this.baseline),this.symbolHeight=ad(r.symbolHeight,this.fontMetrics.f),r.squareSymbol&&(this.symbolWidth=ad(r.symbolWidth,Math.max(this.symbolHeight,16)),b=this.symbolWidth+n+d+20*!!f,c&&y.attr("x",this.symbolWidth+n))),g.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,y,m)),f&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(i.styledMode||!h.width)&&y.css({width:(r.itemWidth||this.widthOption||i.spacingBox.width)-b+"px"}),this.setText(t);let v=y.getBBox(),k=this.fontMetrics?.h||0;t.itemWidth=t.checkboxOffset=r.itemWidth||e.labelWidth||v.width+b,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(e.labelHeight||(v.height>1.5*k?v.height:k))}layoutItem(t){let e=this.options,i=this.padding,s="horizontal"===e.layout,r=t.itemHeight,o=this.itemMarginBottom,a=this.itemMarginTop,n=s?ad(e.itemDistance,20):0,h=this.maxLegendWidth,l=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth,d=t.legendItem||{};s&&this.itemX-i+l>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=a+this.lastLineHeight+o),this.lastLineHeight=0),this.lastItemY=a+this.itemY+o,this.lastLineHeight=Math.max(r,this.lastLineHeight),d.x=this.itemX,d.y=this.itemY,s?this.itemX+=l:(this.itemY+=a+r+o,this.lastLineHeight=r),this.offsetWidth=this.widthOption||Math.max((s?this.itemX-i-(t.checkbox?0:n):l)+i,this.offsetWidth)}getAllItems(){let t=[];return this.chart.series.forEach(function(e){let i=e?.options;e&&ad(i.showInLegend,!ar(i.linkedTo)&&void 0,!0)&&(t=t.concat(e.legendItem?.labels||("point"===i.legendType?e.data:e)))}),an(this,"afterGetAllItems",{allItems:t}),t}getAlignment(){let t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)}adjustMargins(t,e){let i=this.chart,s=this.options,r=this.getAlignment();r&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(o,a){o.test(r)&&!ar(t[a])&&(i[o8[a]]=Math.max(i[o8[a]],i.legend[(a+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][a]*s[a%2?"x":"y"]+ad(s.margin,12)+e[a]+(i.titleOffset[a]||0)))})}proximatePositions(){let t,e=this.chart,i=[],s="left"===this.options.align;for(let r of(this.allItems.forEach(function(t){let r,o,a=s,n,h;t.yAxis&&(t.xAxis.options.reversed&&(a=!a),t.points&&(r=aa(a?t.points:t.points.slice(0).reverse(),function(t){return ah(t.plotY)})),o=this.itemMarginTop+t.legendItem.label.getBBox().height+this.itemMarginBottom,h=t.yAxis.top-e.plotTop,n=t.visible?(r?r.plotY:t.yAxis.height)+(h-.3*o):h+t.yAxis.height,i.push({target:n,size:o,item:t}))},this),o7(i,e.plotHeight)))t=r.item.legendItem||{},ah(r.pos)&&(t.y=e.plotTop-e.spacing[0]+r.pos)}render(){let t=this.chart,e=t.renderer,i=this.options,s=this.padding,r=this.getAllItems(),o,a,n,h=this.group,l,d=this.box;this.itemX=s,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=ap(i.width,t.spacingBox.width-s),l=t.spacingBox.width-2*s-i.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(l/=2),this.maxLegendWidth=this.widthOption||l,h||(this.group=h=e.g("legend").addClass(i.className||"").attr({zIndex:7}).add(),this.contentGroup=e.g().attr({zIndex:1}).add(h),this.scrollGroup=e.g().add(this.contentGroup)),this.renderTitle(),ag(r,(t,e)=>(t.options?.legendIndex||0)-(e.options?.legendIndex||0)),i.reversed&&r.reverse(),this.allItems=r,this.display=o=!!r.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,r.forEach(this.renderItem,this),r.forEach(this.layoutItem,this),a=(this.widthOption||this.offsetWidth)+s,n=this.lastItemY+this.lastLineHeight+this.titleHeight,n=this.handleOverflow(n)+s,d||(this.box=d=e.rect().addClass("highcharts-legend-box").attr({r:i.borderRadius}).add(h)),t.styledMode||d.attr({stroke:i.borderColor,"stroke-width":i.borderWidth||0,fill:i.backgroundColor||"none"}).shadow(i.shadow),a>0&&n>0&&d[d.placed?"animate":"attr"](d.crisp.call({},{x:0,y:0,width:a,height:n},d.strokeWidth())),h[o?"show":"hide"](),t.styledMode&&"none"===h.getStyle("display")&&(a=n=0),this.legendWidth=a,this.legendHeight=n,o&&this.align(),this.proximate||this.positionItems(),an(this,"afterRender")}align(t=this.chart.spacingBox){let e=this.chart,i=this.options,s=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?s+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(s-=e.titleOffset[2]),s!==t.y&&(t=al(t,{y:s})),e.hasRendered||(this.group.placed=!1),this.group.align(al(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)}handleOverflow(t){let e=this,i=this.chart,s=i.renderer,r=this.options,o=r.y,a="top"===r.verticalAlign,n=this.padding,h=r.maxHeight,l=r.navigation,d=ad(l.animation,!0),c=l.arrowSize||12,p=this.pages,g=this.allItems,u=function(t){"number"==typeof t?M.attr({height:t}):M&&(e.clipRect=M.destroy(),e.contentGroup.clip()),e.contentGroup.div&&(e.contentGroup.div.style.clip=t?"rect("+n+"px,9999px,"+(n+t)+"px,0)":"auto")},f=function(t){return e[t]=s.circle(0,0,1.3*c).translate(c/2,c/2).add(k),i.styledMode||e[t].attr("fill","rgba(0,0,0,0.0001)"),e[t]},m,x,y,b,v=i.spacingBox.height+(a?-o:o)-n,k=this.nav,M=this.clipRect;return"horizontal"!==r.layout||"middle"===r.verticalAlign||r.floating||(v/=2),h&&(v=Math.min(v,h)),p.length=0,t&&v>0&&t>v&&!1!==l.enabled?(this.clipHeight=m=Math.max(v-20-this.titleHeight-n,0),this.currentPage=ad(this.currentPage,1),this.fullHeight=t,g.forEach((t,e)=>{let i=(y=t.legendItem||{}).y||0,s=Math.round(y.label.getBBox().height),r=p.length;(!r||i-p[r-1]>m&&(x||i)!==p[r-1])&&(p.push(x||i),r++),y.pageIx=r-1,x&&b&&(b.pageIx=r-1),e===g.length-1&&i+s-p[r-1]>m&&i>p[r-1]&&(p.push(i),y.pageIx=r),i!==x&&(x=i),b=y}),M||(M=e.clipRect=s.clipRect(0,n-2,9999,0),e.contentGroup.clip(M)),u(m),k||(this.nav=k=s.g().attr({zIndex:1}).add(this.group),this.up=s.symbol("triangle",0,0,c,c).add(k),f("upTracker").on("click",function(){e.scroll(-1,d)}),this.pager=s.text("",15,10).addClass("highcharts-legend-navigation"),!i.styledMode&&l.style&&this.pager.css(l.style),this.pager.add(k),this.down=s.symbol("triangle-down",0,0,c,c).add(k),f("downTracker").on("click",function(){e.scroll(1,d)})),e.scroll(0),t=v):k&&(u(),this.nav=k.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t}scroll(t,e){let i=this.chart,s=this.pages,r=s.length,o=this.clipHeight,a=this.options.navigation,n=this.pager,h=this.padding,l=this.currentPage+t;l>r&&(l=r),l>0&&(void 0!==e&&o6(e,i),this.nav.attr({translateX:h,translateY:o+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(t){t.attr({class:1===l?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),n.attr({text:l+"/"+r}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,class:l===r?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),i.styledMode||(this.up.attr({fill:1===l?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:1===l?"default":"pointer"}),this.down.attr({fill:l===r?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:l===r?"default":"pointer"})),this.scrollOffset=-s[l-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=l,this.positionCheckboxes(),au(()=>{an(this,"afterScroll",{currentPage:l})},o5(ad(e,i.renderer.globalAnimation,!0)).duration))}setItemEvents(t,e,i){let s=this,r=t.legendItem||{},o=s.chart.renderer.boxWrapper,a=t instanceof rQ,n=t instanceof o3,h="highcharts-legend-"+(a?"point":"series")+"-active",l=s.chart.styledMode,d=i?[e,r.symbol]:[r.group],c=e=>{s.allItems.forEach(i=>{t!==i&&[i].concat(i.linkedSeries||[]).forEach(t=>{t.setState(e,!a)})})};for(let i of d)i&&i.on("mouseover",function(){t.visible&&c("inactive"),t.setState("hover"),t.visible&&o.addClass(h),l||e.css(s.options.itemHoverStyle)}).on("mouseout",function(){s.chart.styledMode||e.css(al(t.visible?s.itemStyle:s.itemHiddenStyle)),c(""),o.removeClass(h),t.setState()}).on("click",function(e){let i=function(){t.setVisible&&t.setVisible(),c(t.visible?"inactive":"")};o.removeClass(h),an(s,"itemClick",{browserEvent:e,legendItem:t},i),a?t.firePointEvent("legendItemClick",{browserEvent:e}):n&&an(t,"legendItemClick",{browserEvent:e})})}createCheckboxForItem(t){t.checkbox=ai("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),ae(t.checkbox,"click",function(e){let i=e.target;an(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})}}!function(t){t.compose=function(e){ac(o4,"Core.Legend")&&ae(e,"beforeMargins",function(){this.legend=new t(this,this.options.legend)})}}(af||(af={}));let am=af,{animate:ax,animObject:ay,setAnimation:ab}=t$,{defaultOptions:av}=tM,{numberFormat:ak}=eu,{registerEventOptions:aM}=sr,{charts:aw,doc:aS,marginNames:aA,svg:aT,win:aC}=S,{seriesTypes:aO}=ow,{addEvent:aP,attr:aE,createElement:aL,css:aD,defined:aI,diffObjects:aB,discardElement:az,erase:aN,error:aR,extend:aW,find:aH,fireEvent:aX,getAlignFactor:aF,getStyle:aG,isArray:aY,isNumber:aj,isObject:aU,isString:aV,merge:a$,objectEach:aZ,pick:a_,pInt:aq,relativeLength:aK,removeEvent:aJ,splat:aQ,syncTimeout:a0,uniqueKey:a1}=_;class a2{static chart(t,e,i){return new a2(t,e,i)}constructor(t,e,i){this.sharedClips={};let s=[...arguments];(aV(t)||t.nodeName)&&(this.renderTo=s.shift()),this.init(s[0],s[1])}setZoomOptions(){let t=this.options.chart,e=t.zooming;this.zooming={...e,type:a_(t.zoomType,e.type),key:a_(t.zoomKey,e.key),pinchType:a_(t.pinchType,e.pinchType),singleTouch:a_(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:a$(e.resetButton,t.resetZoomButton)}}init(t,e){aX(this,"init",{args:arguments},function(){let i=a$(av,t),s=i.chart,r=this.renderTo||s.renderTo;this.userOptions=aW({},t),(this.renderTo=aV(r)?aS.getElementById(r):r)||aR(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=i,this.axes=[],this.series=[],this.locale=i.lang.locale??this.renderTo.closest("[lang]")?.lang,this.time=new tm(aW(i.time||{},{locale:this.locale}),i.lang),i.time=this.time.options,this.numberFormatter=(s.numberFormatter||ak).bind(this),this.styledMode=s.styledMode,this.hasCartesianSeries=s.showAxes,this.index=aw.length,aw.push(this),S.chartCount++,aM(this,s),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),aX(this,"afterInit"),this.firstRender()})}initSeries(t){let e=this.options.chart,i=t.type||e.type,s=aO[i];s||aR(17,!0,this,{missingModuleFor:i});let r=new s;return"function"==typeof r.init&&r.init(this,t),r}setSortedData(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})}getSeriesOrderByLinks(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})}orderItems(t,e=0){let i=this[t],s=this.options[t]=aQ(this.options[t]).slice(),r=this.userOptions[t]=this.userOptions[t]?aQ(this.userOptions[t]).slice():[];if(this.hasRendered&&(s.splice(e),r.splice(e)),i)for(let t=e,o=i.length;t<o;++t){let e=i[t];e&&(e.index=t,e instanceof o3&&(e.name=e.getName()),e.options.isInternal||(s[t]=e.options,r[t]=e.userOptions))}}getClipBox(t,e){let i=this.inverted,{xAxis:s,yAxis:r}=t||{},{x:o,y:a,width:n,height:h}=a$(this.clipBox);return t&&(s&&s.len!==this.plotSizeX&&(n=s.len),r&&r.len!==this.plotSizeY&&(h=r.len),i&&!t.invertible&&([n,h]=[h,n])),e&&(o+=(i?r:s)?.pos??this.plotLeft,a+=(i?s:r)?.pos??this.plotTop),{x:o,y:a,width:n,height:h}}isInsidePlot(t,e,i={}){let{inverted:s,plotBox:r,plotLeft:o,plotTop:a,scrollablePlotBox:n}=this,{scrollLeft:h=0,scrollTop:l=0}=i.visiblePlotOnly&&this.scrollablePlotArea?.scrollingContainer||{},d=i.series,c=i.visiblePlotOnly&&n||r,p=i.inverted?e:t,g=i.inverted?t:e,u={x:p,y:g,isInsidePlot:!0,options:i};if(!i.ignoreX){let t=d&&(s&&!this.polar?d.yAxis:d.xAxis)||{pos:o,len:1/0},e=i.paneCoordinates?t.pos+p:o+p;e>=Math.max(h+o,t.pos)&&e<=Math.min(h+o+c.width,t.pos+t.len)||(u.isInsidePlot=!1)}if(!i.ignoreY&&u.isInsidePlot){let t=!s&&i.axis&&!i.axis.isXAxis&&i.axis||d&&(s?d.xAxis:d.yAxis)||{pos:a,len:1/0},e=i.paneCoordinates?t.pos+g:a+g;e>=Math.max(l+a,t.pos)&&e<=Math.min(l+a+c.height,t.pos+t.len)||(u.isInsidePlot=!1)}return aX(this,"afterIsInsidePlot",u),u.isInsidePlot}redraw(t){aX(this,"beforeRedraw");let e=this.hasCartesianSeries?this.axes:this.colorAxis||[],i=this.series,s=this.pointer,r=this.legend,o=this.userOptions.legend,a=this.renderer,n=a.isHidden(),h=[],l,d,c,p=this.isDirtyBox,g=this.isDirtyLegend,u;for(a.rootFontSize=a.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),ab(!!this.hasRendered&&t,this),n&&this.temporaryDisplay(),this.layOutTitles(!1),c=i.length;c--;)if(((u=i[c]).options.stacking||u.options.centerInCategory)&&(d=!0,u.isDirty)){l=!0;break}if(l)for(c=i.length;c--;)(u=i[c]).options.stacking&&(u.isDirty=!0);i.forEach(function(t){t.isDirty&&("point"===t.options.legendType?("function"==typeof t.updateTotals&&t.updateTotals(),g=!0):o&&(o.labelFormatter||o.labelFormat)&&(g=!0)),t.isDirtyData&&aX(t,"updatedData")}),g&&r&&r.options.enabled&&(r.render(),this.isDirtyLegend=!1),d&&this.getStacks(),e.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),e.forEach(function(t){t.isDirty&&(p=!0)}),e.forEach(function(t){let e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,h.push(function(){aX(t,"afterSetExtremes",aW(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(p||d)&&t.redraw()}),p&&this.drawChartBox(),aX(this,"predraw"),i.forEach(function(t){(p||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),s&&s.reset(!0),a.draw(),aX(this,"redraw"),aX(this,"render"),n&&this.temporaryDisplay(!0),h.forEach(function(t){t.call()})}get(t){let e=this.series;function i(e){return e.id===t||e.options&&e.options.id===t}let s=aH(this.axes,i)||aH(this.series,i);for(let t=0;!s&&t<e.length;t++)s=aH(e[t].points||[],i);return s}createAxes(){let t=this.userOptions;for(let e of(aX(this,"createAxes"),["xAxis","yAxis"]))for(let i of t[e]=aQ(t[e]||{}))new sZ(this,i,e);aX(this,"afterCreateAxes")}getSelectedPoints(){return this.series.reduce((t,e)=>(e.getPointsCollection().forEach(e=>{a_(e.selectedStaging,e.selected)&&t.push(e)}),t),[])}getSelectedSeries(){return this.series.filter(t=>t.selected)}setTitle(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)}applyDescription(t,e){let i=this,s=this.options[t]=a$(this.options[t],e),r=this[t];r&&e&&(this[t]=r=r.destroy()),s&&!r&&((r=this.renderer.text(s.text,0,0,s.useHTML).attr({align:s.align,class:"highcharts-"+t,zIndex:s.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(e,s){i.applyDescription(t,e),i.layOutTitles(s)},this.styledMode||r.css(aW("title"===t?{fontSize:this.options.isStock?"1em":"1.2em"}:{},s.style)),r.textPxLength=r.getBBox().width,r.css({whiteSpace:s.style?.whiteSpace}),this[t]=r)}layOutTitles(t=!0){let e=[0,0,0],{options:i,renderer:s,spacingBox:r}=this;["title","subtitle","caption"].forEach(t=>{let i=this[t],o=this.options[t],a=a$(r),n=i?.textPxLength||0;if(i&&o){aX(this,"layOutTitle",{alignTo:a,key:t,textPxLength:n});let r=s.fontMetrics(i),h=r.b,l=r.h,d=o.verticalAlign||"top",c="top"===d,p=c&&o.minScale||1,g="title"===t?c?-3:0:c?e[0]+2:0,u=Math.min(a.width/n,1),f=Math.max(p,u),m=a$({y:"bottom"===d?h:g+h},{align:"title"===t?u<p?"left":"center":this.title?.alignValue},o),x=(o.width||(u>p?this.chartWidth:a.width)/f)+"px";i.alignValue!==m.align&&(i.placed=!1);let y=Math.round(i.css({width:x}).getBBox(o.useHTML).height);if(m.height=y,i.align(m,!1,a).attr({align:m.align,scaleX:f,scaleY:f,"transform-origin":`${a.x+n*f*aF(m.align)} ${l}`}),!o.floating){let t=y*(y<1.2*l?1:f);"top"===d?e[0]=Math.ceil(e[0]+t):"bottom"===d&&(e[2]=Math.ceil(e[2]+t))}}},this),e[0]&&"top"===(i.title?.verticalAlign||"top")&&(e[0]+=i.title?.margin||0),e[2]&&i.caption?.verticalAlign==="bottom"&&(e[2]+=i.caption?.margin||0);let o=!this.titleOffset||this.titleOffset.join(",")!==e.join(",");this.titleOffset=e,aX(this,"afterLayOutTitles"),!this.isDirtyBox&&o&&(this.isDirtyBox=this.isDirtyLegend=o,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())}getContainerBox(){let t=[].map.call(this.renderTo.children,t=>{if(t!==this.container){let e=t.style.display;return t.style.display="none",[t,e]}}),e={width:aG(this.renderTo,"width",!0)||0,height:aG(this.renderTo,"height",!0)||0};return t.filter(Boolean).forEach(([t,e])=>{t.style.display=e}),e}getChartSize(){let t=this.options.chart,e=t.width,i=t.height,s=this.getContainerBox(),r=s.height<=1||!this.renderTo.parentElement?.style.height&&"100%"===this.renderTo.style.height;this.chartWidth=Math.max(0,e||s.width||600),this.chartHeight=Math.max(0,aK(i,this.chartWidth)||(r?400:s.height)),this.containerBox=s}temporaryDisplay(t){let e=this.renderTo,i;if(t)for(;e?.style;)e.hcOrigStyle&&(aD(e,e.hcOrigStyle),delete e.hcOrigStyle),e.hcOrigDetached&&(aS.body.removeChild(e),e.hcOrigDetached=!1),e=e.parentNode;else for(;e?.style&&(aS.body.contains(e)||e.parentNode||(e.hcOrigDetached=!0,aS.body.appendChild(e)),("none"===aG(e,"display",!1)||e.hcOricDetached)&&(e.hcOrigStyle={display:e.style.display,height:e.style.height,overflow:e.style.overflow},i={display:"block",overflow:"hidden"},e!==this.renderTo&&(i.height=0),aD(e,i),e.offsetWidth||e.style.setProperty("display","block","important")),(e=e.parentNode)!==aS.body););}setClassName(t){this.container.className="highcharts-container "+(t||"")}getContainer(){let t,e=this.options,i=e.chart,s="data-highcharts-chart",r=a1(),o=this.renderTo,a=aq(aE(o,s));aj(a)&&aw[a]&&aw[a].hasRendered&&aw[a].destroy(),aE(o,s,this.index),o.innerHTML=t4.emptyHTML,i.skipClone||o.offsetWidth||this.temporaryDisplay(),this.getChartSize();let n=this.chartHeight,h=this.chartWidth;aD(o,{overflow:"hidden"}),this.styledMode||(t=aW({position:"relative",overflow:"hidden",width:h+"px",height:n+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},i.style||{}));let l=aL("div",{id:r},t,o);this.container=l,this.getChartSize(),h===this.chartWidth||(h=this.chartWidth,this.styledMode||aD(l,{width:a_(i.style?.width,h+"px")})),this.containerBox=this.getContainerBox(),this._cursor=l.style.cursor;let d=i.renderer||!aT?ef.getRendererType(i.renderer):i$;if(this.renderer=new d(l,h,n,void 0,i.forExport,e.exporting?.allowHTML,this.styledMode),ab(void 0,this),this.setClassName(i.className),this.styledMode)for(let t in e.defs)this.renderer.definition(e.defs[t]);else this.renderer.setStyle(i.style);this.renderer.chartIndex=this.index,aX(this,"afterGetContainer")}getMargins(t){let{spacing:e,margin:i,titleOffset:s}=this;this.resetMargins(),s[0]&&!aI(i[0])&&(this.plotTop=Math.max(this.plotTop,s[0]+e[0])),s[2]&&!aI(i[2])&&(this.marginBottom=Math.max(this.marginBottom,s[2]+e[2])),this.legend?.display&&this.legend.adjustMargins(i,e),aX(this,"getMargins"),t||this.getAxisMargins()}getAxisMargins(){let t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,s=t.margin,r=function(t){t.forEach(function(t){t.visible&&t.getOffset()})};t.hasCartesianSeries?r(t.axes):i?.length&&r(i),aA.forEach(function(i,r){aI(s[r])||(t[i]+=e[r])}),t.setChartSize()}getOptions(){return aB(this.userOptions,av)}reflow(t){let e=this,i=e.containerBox,s=e.getContainerBox();delete e.pointer?.chartPosition,!e.isPrinting&&!e.isResizing&&i&&s.width&&((s.width!==i.width||s.height!==i.height)&&(_.clearTimeout(e.reflowTimeout),e.reflowTimeout=a0(function(){e.container&&e.setSize(void 0,void 0,!1)},100*!!t)),e.containerBox=s)}setReflow(){let t=this,e=e=>{t.options?.chart.reflow&&t.hasLoaded&&t.reflow(e)};if("function"==typeof ResizeObserver)new ResizeObserver(e).observe(t.renderTo);else{let t=aP(aC,"resize",e);aP(this,"destroy",t)}}setSize(t,e,i){let s=this,r=s.renderer;s.isResizing+=1,ab(i,s);let o=r.globalAnimation;s.oldChartHeight=s.chartHeight,s.oldChartWidth=s.chartWidth,void 0!==t&&(s.options.chart.width=t),void 0!==e&&(s.options.chart.height=e),s.getChartSize();let{chartWidth:a,chartHeight:n,scrollablePixelsX:h=0,scrollablePixelsY:l=0}=s;(s.isDirtyBox||a!==s.oldChartWidth||n!==s.oldChartHeight)&&(s.styledMode||(o?ax:aD)(s.container,{width:`${a+h}px`,height:`${n+l}px`},o),s.setChartSize(!0),r.setSize(a,n,o),s.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),s.isDirtyLegend=!0,s.isDirtyBox=!0,s.layOutTitles(),s.getMargins(),s.redraw(o),s.oldChartHeight=void 0,aX(s,"resize"),setTimeout(()=>{s&&aX(s,"endResize")},ay(o).duration)),s.isResizing-=1}setChartSize(t){let e,i,s,r,{chartHeight:o,chartWidth:a,inverted:n,spacing:h,renderer:l}=this,d=this.clipOffset,c=Math[n?"floor":"round"];this.plotLeft=e=Math.round(this.plotLeft),this.plotTop=i=Math.round(this.plotTop),this.plotWidth=s=Math.max(0,Math.round(a-e-(this.marginRight??0))),this.plotHeight=r=Math.max(0,Math.round(o-i-(this.marginBottom??0))),this.plotSizeX=n?r:s,this.plotSizeY=n?s:r,this.spacingBox=l.spacingBox={x:h[3],y:h[0],width:a-h[3]-h[1],height:o-h[0]-h[2]},this.plotBox=l.plotBox={x:e,y:i,width:s,height:r},d&&(this.clipBox={x:c(d[3]),y:c(d[0]),width:c(this.plotSizeX-d[1]-d[3]),height:c(this.plotSizeY-d[0]-d[2])}),t||(this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),l.alignElements()),aX(this,"afterSetChartSize",{skipAxes:t})}resetMargins(){aX(this,"resetMargins");let t=this,e=t.options.chart,i=e.plotBorderWidth||0,s=Math.round(i)/2;["margin","spacing"].forEach(function(i){let s=e[i],r=aU(s)?s:[s,s,s,s];["Top","Right","Bottom","Left"].forEach(function(s,o){t[i][o]=a_(e[i+s],r[o])})}),aA.forEach(function(e,i){t[e]=a_(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[s,s,s,s],t.plotBorderWidth=i}drawChartBox(){let t=this.options.chart,e=this.renderer,i=this.chartWidth,s=this.chartHeight,r=this.styledMode,o=this.plotBGImage,a=t.backgroundColor,n=t.plotBackgroundColor,h=t.plotBackgroundImage,l=this.plotLeft,d=this.plotTop,c=this.plotWidth,p=this.plotHeight,g=this.plotBox,u=this.clipRect,f=this.clipBox,m=this.chartBackground,x=this.plotBackground,y=this.plotBorder,b,v,k,M="animate";m||(this.chartBackground=m=e.rect().addClass("highcharts-background").add(),M="attr"),r?b=v=m.strokeWidth():(v=(b=t.borderWidth||0)+8*!!t.shadow,k={fill:a||"none"},(b||m["stroke-width"])&&(k.stroke=t.borderColor,k["stroke-width"]=b),m.attr(k).shadow(t.shadow)),m[M]({x:v/2,y:v/2,width:i-v-b%2,height:s-v-b%2,r:t.borderRadius}),M="animate",x||(M="attr",this.plotBackground=x=e.rect().addClass("highcharts-plot-background").add()),x[M](g),!r&&(x.attr({fill:n||"none"}).shadow(t.plotShadow),h&&(o?(h!==o.attr("href")&&o.attr("href",h),o.animate(g)):this.plotBGImage=e.image(h,l,d,c,p).add())),u?u.animate({width:f.width,height:f.height}):this.clipRect=e.clipRect(f),M="animate",y||(M="attr",this.plotBorder=y=e.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),r||y.attr({stroke:t.plotBorderColor,"stroke-width":t.plotBorderWidth||0,fill:"none"}),y[M](y.crisp(g,-y.strokeWidth())),this.isDirtyBox=!1,aX(this,"afterDrawChartBox")}propFromSeries(){let t,e,i,s=this,r=s.options.chart,o=s.options.series;["inverted","angular","polar"].forEach(function(a){for(e=aO[r.type],i=r[a]||e&&e.prototype[a],t=o?.length;!i&&t--;)(e=aO[o[t].type])&&e.prototype[a]&&(i=!0);s[a]=i})}linkSeries(t){let e=this,i=e.series;i.forEach(function(t){t.linkedSeries.length=0}),i.forEach(function(t){let{linkedTo:i}=t.options;if(aV(i)){let s;(s=":previous"===i?e.series[t.index-1]:e.get(i))&&s.linkedParent!==t&&(s.linkedSeries.push(t),t.linkedParent=s,s.enabledDataSorting&&t.setDataSortingOptions(),t.visible=a_(t.options.visible,s.options.visible,t.visible))}}),aX(this,"afterLinkSeries",{isUpdating:t})}renderSeries(){this.series.forEach(function(t){t.translate(),t.render()})}render(){let t=this.axes,e=this.colorAxis,i=this.renderer,s=this.options.chart.axisLayoutRuns||2,r=t=>{t.forEach(t=>{t.visible&&t.render()})},o=0,a=!0,n,h=0;for(let e of(this.setTitle(),aX(this,"beforeMargins"),this.getStacks?.(),this.getMargins(!0),this.setChartSize(),t)){let{options:t}=e,{labels:i}=t;if(this.hasCartesianSeries&&e.horiz&&e.visible&&i.enabled&&e.series.length&&"colorAxis"!==e.coll&&!this.polar){o=t.tickLength,e.createGroups();let s=new sx(e,0,"",!0),r=s.createLabel("x",i);if(s.destroy(),r&&a_(i.reserveSpace,!aj(t.crossing))&&(o=r.getBBox().height+i.distance+Math.max(t.offset||0,0)),o){r?.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-o,0);(a||n||s>1)&&h<s;){let e=this.plotWidth,i=this.plotHeight;for(let e of t)0===h?e.setScale():(e.horiz&&a||!e.horiz&&n)&&e.setTickInterval(!0);0===h?this.getAxisMargins():this.getMargins(),a=e/this.plotWidth>(h?1:1.1),n=i/this.plotHeight>(h?1:1.05),h++}this.drawChartBox(),this.hasCartesianSeries?r(t):e?.length&&r(e),this.seriesGroup||(this.seriesGroup=i.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0}addCredits(t){let e=this,i=a$(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(aC.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})}destroy(){let t,e=this,i=e.axes,s=e.series,r=e.container,o=r?.parentNode;for(aX(e,"destroy"),e.renderer.forExport?aN(aw,e):aw[e.index]=void 0,S.chartCount--,e.renderTo.removeAttribute("data-highcharts-chart"),aJ(e),t=i.length;t--;)i[t]=i[t].destroy();for(this.scroller?.destroy?.(),t=s.length;t--;)s[t]=s[t].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(t=>{e[t]=e[t]?.destroy?.()}),r&&(r.innerHTML=t4.emptyHTML,aJ(r),o&&az(r)),aZ(e,function(t,i){delete e[i]})}firstRender(){let t=this,e=t.options;t.getContainer(),t.resetMargins(),t.setChartSize(),t.propFromSeries(),t.createAxes();let i=aY(e.series)?e.series:[];e.series=[],i.forEach(function(e){t.initSeries(e)}),t.linkSeries(),t.setSortedData(),aX(t,"beforeRender"),t.render(),t.pointer?.getChartPosition(),t.renderer.imgCount||t.hasLoaded||t.onload(),t.temporaryDisplay(!0)}onload(){this.callbacks.concat([this.callback]).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),aX(this,"load"),aX(this,"render"),aI(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0}warnIfA11yModuleNotLoaded(){let{options:t,title:e}=this;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":(e?.element.textContent||"").replace(/</g,"&lt;")}),t.accessibility&&!1===t.accessibility.enabled||aR('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))}addSeries(t,e,i){let s,r=this;return t&&(e=a_(e,!0),aX(r,"addSeries",{options:t},function(){s=r.initSeries(t),r.isDirtyLegend=!0,r.linkSeries(),s.enabledDataSorting&&s.setData(t.data,!1),aX(r,"afterAddSeries",{series:s}),e&&r.redraw(i)})),s}addAxis(t,e,i,s){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:s})}addColorAxis(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})}createAxis(t,e){let i=new sZ(this,e.axis,t);return a_(e.redraw,!0)&&this.redraw(e.animation),i}showLoading(t){let e=this,i=e.options,s=i.loading,r=function(){o&&aD(o,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},o=e.loadingDiv,a=e.loadingSpan;o||(e.loadingDiv=o=aL("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),a||(e.loadingSpan=a=aL("span",{className:"highcharts-loading-inner"},null,o),aP(e,"redraw",r)),o.className="highcharts-loading",t4.setElementHTML(a,a_(t,i.lang.loading,"")),e.styledMode||(aD(o,aW(s.style,{zIndex:10})),aD(a,s.labelStyle),e.loadingShown||(aD(o,{opacity:0,display:""}),ax(o,{opacity:s.style.opacity||.5},{duration:s.showDuration||0}))),e.loadingShown=!0,r()}hideLoading(){let t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||ax(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){aD(e,{display:"none"})}})),this.loadingShown=!1}update(t,e,i,s){let r,o,a,n=this,h={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},l=t.isResponsiveOptions,d=[];aX(n,"update",{options:t}),l||n.setResponsive(!1,!0),t=aB(t,n.options),n.userOptions=a$(n.userOptions,t);let c=t.chart;c&&(a$(!0,n.options.chart,c),this.setZoomOptions(),"className"in c&&n.setClassName(c.className),("inverted"in c||"polar"in c||"type"in c)&&(n.propFromSeries(),r=!0),"alignTicks"in c&&(r=!0),"events"in c&&aM(this,c),aZ(c,function(t,e){-1!==n.propsRequireUpdateSeries.indexOf("chart."+e)&&(o=!0),-1!==n.propsRequireDirtyBox.indexOf(e)&&(n.isDirtyBox=!0),-1===n.propsRequireReflow.indexOf(e)||(n.isDirtyBox=!0,l||(a=!0))}),!n.styledMode&&c.style&&n.renderer.setStyle(n.options.chart.style||{})),!n.styledMode&&t.colors&&(this.options.colors=t.colors),aZ(t,function(e,i){n[i]&&"function"==typeof n[i].update?n[i].update(e,!1):"function"==typeof n[h[i]]?n[h[i]](e):"colors"!==i&&-1===n.collectionsWithUpdate.indexOf(i)&&a$(!0,n.options[i],t[i]),"chart"!==i&&-1!==n.propsRequireUpdateSeries.indexOf(i)&&(o=!0)}),this.collectionsWithUpdate.forEach(function(e){t[e]&&(aQ(t[e]).forEach(function(t,s){let r,o=aI(t.id);o&&(r=n.get(t.id)),!r&&n[e]&&(r=n[e][a_(t.index,s)])&&(o&&aI(r.options.id)||r.options.isInternal)&&(r=void 0),r&&r.coll===e&&(r.update(t,!1),i&&(r.touched=!0)),!r&&i&&n.collectionsWithInit[e]&&(n.collectionsWithInit[e][0].apply(n,[t].concat(n.collectionsWithInit[e][1]||[]).concat([!1])).touched=!0)}),i&&n[e].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:d.push(t)}))}),d.forEach(function(t){t.chart&&t.remove&&t.remove(!1)}),r&&n.axes.forEach(function(t){t.update({},!1)}),o&&n.getSeriesOrderByLinks().forEach(function(t){t.chart&&t.update({},!1)},this);let p=c?.width,g=c&&(aV(c.height)?aK(c.height,p||n.chartWidth):c.height);a||aj(p)&&p!==n.chartWidth||aj(g)&&g!==n.chartHeight?n.setSize(p,g,s):a_(e,!0)&&n.redraw(s),aX(n,"afterUpdate",{options:t,redraw:e,animation:s})}setSubtitle(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)}setCaption(t,e){this.applyDescription("caption",t),this.layOutTitles(e)}showResetZoom(){let t=this,e=av.lang,i=t.zooming.resetButton,s=i.theme,r="chart"===i.relativeTo||"spacingBox"===i.relativeTo?null:"plotBox";function o(){t.zoomOut()}aX(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,o,s).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,r)}),aX(this,"afterShowResetZoom")}zoomOut(){aX(this,"selection",{resetSelection:!0},()=>this.transform({reset:!0,trigger:"zoom"}))}pan(t,e){let i=this,s="object"==typeof e?e:{enabled:e,type:"x"},r=s.type,o=r&&i[({x:"xAxis",xy:"axes",y:"yAxis"})[r]].filter(t=>t.options.panningEnabled&&!t.options.isInternal),a=i.options.chart;a?.panning&&(a.panning=s),aX(this,"pan",{originalEvent:t},()=>{i.transform({axes:o,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),aD(i.container,{cursor:"move"})})}transform(t){let{axes:e=this.axes,event:i,from:s={},reset:r,selection:o,to:a={},trigger:n}=t,{inverted:h,time:l}=this,d=!1,c,p;for(let t of(this.hoverPoints?.forEach(t=>t.setState()),e)){let{horiz:e,len:g,minPointOffset:u=0,options:f,reversed:m}=t,x=e?"width":"height",y=e?"x":"y",b=a_(a[x],t.len),v=a_(s[x],t.len),k=10>Math.abs(b)?1:b/v,M=(s[y]||0)+v/2-t.pos,w=M-((a[y]??t.pos)+b/2-t.pos)/k,S=m&&!h||!m&&h?-1:1;if(!r&&(M<0||M>t.len))continue;let A=t.toValue(w,!0)+(o||t.isOrdinal?0:u*S),T=t.toValue(w+g/k,!0)-(o||t.isOrdinal?0:u*S||0),C=t.allExtremes;if(A>T&&([A,T]=[T,A]),1===k&&!r&&"yAxis"===t.coll&&!C){for(let e of t.series){let t=e.getExtremes(e.getProcessedData(!0).modified.getColumn("y")||[],!0);C??(C={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),aj(t.dataMin)&&aj(t.dataMax)&&(C.dataMin=Math.min(t.dataMin,C.dataMin),C.dataMax=Math.max(t.dataMax,C.dataMax))}t.allExtremes=C}let{dataMin:O,dataMax:P,min:E,max:L}=aW(t.getExtremes(),C||{}),D=l.parse(f.min),I=l.parse(f.max),B=O??D,z=P??I,N=T-A,R=t.categories?0:Math.min(N,z-B),W=B-R*(aI(D)?0:f.minPadding),H=z+R*(aI(I)?0:f.maxPadding),X=t.allowZoomOutside||1===k||"zoom"!==n&&k>1,F=Math.min(D??W,W,X?E:W),G=Math.max(I??H,H,X?L:H);(!t.isOrdinal||t.options.overscroll||1!==k||r)&&(A<F&&(A=F,k>=1&&(T=A+N)),T>G&&(T=G,k>=1&&(A=T-N)),(r||t.series.length&&(A!==E||T!==L)&&A>=F&&T<=G)&&(o?o[t.coll].push({axis:t,min:A,max:T}):(t.isPanning="zoom"!==n,t.isPanning&&(p=!0),t.setExtremes(r?void 0:A,r?void 0:T,!1,!1,{move:w,trigger:n,scale:k}),!r&&(A>F||T<G)&&"mousewheel"!==n&&(c=!0)),d=!0),i&&(this[e?"mouseDownX":"mouseDownY"]=i[e?"chartX":"chartY"]))}return d&&(o?aX(this,"selection",o,()=>{delete t.selection,t.trigger="zoom",this.transform(t)}):(!c||p||this.resetZoomButton?!c&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw("zoom"===n&&(this.options.chart.animation??this.pointCount<100)))),d}}aW(a2.prototype,{callbacks:[],collectionsWithInit:{xAxis:[a2.prototype.addAxis,[!0]],yAxis:[a2.prototype.addAxis,[!1]],series:[a2.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});let{stop:a3}=t$,{composed:a5}=S,{addEvent:a6,createElement:a9,css:a4,defined:a8,erase:a7,merge:nt,pushUnique:ne}=_;function ni(){let t=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!t&&(this.scrollablePlotArea=t=new nr(this)),t?.applyFixed()}function ns(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}class nr{static compose(t,e,i){ne(a5,this.compose)&&(a6(t,"afterInit",ns),a6(e,"afterSetChartSize",t=>this.afterSetSize(t.target,t)),a6(e,"render",ni),a6(i,"show",ns))}static afterSetSize(t,e){let i,s,r,{minWidth:o,minHeight:a}=t.options.chart.scrollablePlotArea||{},{clipBox:n,plotBox:h,inverted:l,renderer:d}=t;if(!d.forExport&&(o?(t.scrollablePixelsX=i=Math.max(0,o-t.chartWidth),i&&(t.scrollablePlotBox=nt(t.plotBox),h.width=t.plotWidth+=i,n[l?"height":"width"]+=i,r=!0)):a&&(t.scrollablePixelsY=s=Math.max(0,a-t.chartHeight),a8(s)&&(t.scrollablePlotBox=nt(t.plotBox),h.height=t.plotHeight+=s,n[l?"width":"height"]+=s,r=!1)),a8(r)&&!e.skipAxes))for(let e of t.axes)(e.horiz===r||t.hasParallelCoordinates&&"yAxis"===e.coll)&&(e.setAxisSize(),e.setAxisTranslation())}constructor(t){let e,i=t.options.chart,s=ef.getRendererType(),r=i.scrollablePlotArea||{},o=this.moveFixedElements.bind(this),a={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(a.overflowX="auto"),t.scrollablePixelsY&&(a.overflowY="auto"),this.chart=t;let n=this.parentDiv=a9("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),h=this.scrollingContainer=a9("div",{className:"highcharts-scrolling"},a,n),l=this.innerContainer=a9("div",{className:"highcharts-inner-container"},void 0,h),d=this.fixedDiv=a9("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:(i.style?.zIndex||0)+2,top:0},void 0,!0),c=this.fixedRenderer=new s(d,t.chartWidth,t.chartHeight,i.style);this.mask=c.path().attr({fill:i.backgroundColor||"#fff","fill-opacity":r.opacity??.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),h.parentNode.insertBefore(d,h),a4(t.renderTo,{overflow:"visible"}),a6(t,"afterShowResetZoom",o),a6(t,"afterApplyDrilldown",o),a6(t,"afterLayOutTitles",o),a6(h,"scroll",()=>{let{pointer:i,hoverPoint:s}=t;i&&(delete i.chartPosition,s&&(e=s),i.runPointActions(void 0,e,!0))}),l.appendChild(t.container)}applyFixed(){let{chart:t,fixedRenderer:e,isDirty:i,scrollingContainer:s}=this,{axisOffset:r,chartWidth:o,chartHeight:a,container:n,plotHeight:h,plotLeft:l,plotTop:d,plotWidth:c,scrollablePixelsX:p=0,scrollablePixelsY:g=0}=t,{scrollPositionX:u=0,scrollPositionY:f=0}=t.options.chart.scrollablePlotArea||{},m=o+p,x=a+g;e.setSize(o,a),(i??!0)&&(this.isDirty=!1,this.moveFixedElements()),a3(t.container),a4(n,{width:`${m}px`,height:`${x}px`}),t.renderer.boxWrapper.attr({width:m,height:x,viewBox:[0,0,m,x].join(" ")}),t.chartBackground?.attr({width:m,height:x}),a4(s,{width:`${o}px`,height:`${a}px`}),a8(i)||(s.scrollLeft=p*u,s.scrollTop=g*f);let y=d-r[0]-1,b=l-r[3]-1,v=d+h+r[2]+1,k=l+c+r[1]+1,M=l+c-p,w=d+h-g,S=[["M",0,0]];p?S=[["M",0,y],["L",l-1,y],["L",l-1,v],["L",0,v],["Z"],["M",M,y],["L",o,y],["L",o,v],["L",M,v],["Z"]]:g&&(S=[["M",b,0],["L",b,d-1],["L",k,d-1],["L",k,0],["Z"],["M",b,w],["L",b,a],["L",k,a],["L",k,w],["Z"]]),"adjustHeight"!==t.redrawTrigger&&this.mask.attr({d:S})}moveFixedElements(){let t,{container:e,inverted:i,scrollablePixelsX:s,scrollablePixelsY:r}=this.chart,o=this.fixedRenderer,a=nr.fixedSelectors;if(s&&!i?t=".highcharts-yaxis":s&&i?t=".highcharts-xaxis":r&&!i?t=".highcharts-xaxis":r&&i&&(t=".highcharts-yaxis"),t&&!(this.chart.hasParallelCoordinates&&".highcharts-yaxis"===t))for(let e of[`${t}:not(.highcharts-radial-axis)`,`${t}-labels:not(.highcharts-radial-axis-labels)`])ne(a,e);else for(let t of[".highcharts-xaxis",".highcharts-yaxis"])for(let e of[`${t}:not(.highcharts-radial-axis)`,`${t}-labels:not(.highcharts-radial-axis-labels)`])a7(a,e);for(let t of a)[].forEach.call(e.querySelectorAll(t),t=>{(t.namespaceURI===o.SVG_NS?o.box:o.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})}}nr.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"];let{format:no}=eu,{series:na}=ow,{destroyObjectProperties:nn,fireEvent:nh,getAlignFactor:nl,isNumber:nd,pick:nc}=_,np=class{constructor(t,e,i,s,r){let o=t.chart.inverted,a=t.reversed;this.axis=t;let n=this.isNegative=!!i!=!!a;this.options=e=e||{},this.x=s,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=r,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:e.align||(o?n?"left":"right":"center"),verticalAlign:e.verticalAlign||(o?"middle":n?"bottom":"top"),y:e.y,x:e.x},this.textAlign=e.textAlign||(o?n?"right":"left":"center")}destroy(){nn(this,this.axis)}render(t){let e=this.axis.chart,i=this.options,s=i.format,r=s?no(s,this,e):i.formatter.call(this);if(this.label)this.label.attr({text:r,visibility:"hidden"});else{this.label=e.renderer.label(r,null,void 0,i.shape,void 0,void 0,i.useHTML,!1,"stack-labels");let s={r:i.borderRadius||0,text:r,padding:nc(i.padding,5),visibility:"hidden"};e.styledMode||(s.fill=i.backgroundColor,s.stroke=i.borderColor,s["stroke-width"]=i.borderWidth,this.label.css(i.style||{})),this.label.attr(s),this.label.added||this.label.add(t)}this.label.labelrank=e.plotSizeY,nh(this,"afterRender")}setOffset(t,e,i,s,r,o){let{alignOptions:a,axis:n,label:h,options:l,textAlign:d}=this,c=n.chart,p=this.getStackBox({xOffset:t,width:e,boxBottom:i,boxTop:s,defaultX:r,xAxis:o}),{verticalAlign:g}=a;if(h&&p){let t=h.getBBox(void 0,0),e=h.padding,i="justify"===nc(l.overflow,"justify"),s;a.x=l.x||0,a.y=l.y||0;let{x:r,y:o}=this.adjustStackPosition({labelBox:t,verticalAlign:g,textAlign:d});p.x-=r,p.y-=o,h.align(a,!1,p),(s=c.isInsidePlot(h.alignAttr.x+a.x+r,h.alignAttr.y+a.y+o))||(i=!1),i&&na.prototype.justifyDataLabel.call(n,h,a,h.alignAttr,t,p),h.attr({x:h.alignAttr.x,y:h.alignAttr.y,rotation:l.rotation,rotationOriginX:t.width*nl(l.textAlign||"center"),rotationOriginY:t.height/2}),nc(!i&&l.crop,!0)&&(s=nd(h.x)&&nd(h.y)&&c.isInsidePlot(h.x-e+(h.width||0),h.y)&&c.isInsidePlot(h.x+e,h.y)),h[s?"show":"hide"]()}nh(this,"afterSetOffset",{xOffset:t,width:e})}adjustStackPosition({labelBox:t,verticalAlign:e,textAlign:i}){return{x:t.width/2+t.width/2*(2*nl(i)-1),y:t.height/2*2*(1-nl(e))}}getStackBox(t){let e=this.axis,i=e.chart,{boxTop:s,defaultX:r,xOffset:o,width:a,boxBottom:n}=t,h=e.stacking.usePercentage?100:nc(s,this.total,0),l=e.toPixels(h),d=t.xAxis||i.xAxis[0],c=nc(r,d.translate(this.x))+o,p=Math.abs(l-e.toPixels(n||nd(e.min)&&e.logarithmic&&e.logarithmic.lin2log(e.min)||0)),g=i.inverted,u=this.isNegative;return g?{x:(u?l:l-p)-i.plotLeft,y:d.height-c-a+d.top-i.plotTop,width:p,height:a}:{x:c+d.transB-i.plotLeft,y:(u?l-p:l)-i.plotTop,width:a,height:p}}},{getDeferredAnimation:ng}=t$,{series:{prototype:nu}}=ow,{addEvent:nf,correctFloat:nm,defined:nx,destroyObjectProperties:ny,fireEvent:nb,isNumber:nv,objectEach:nk,pick:nM}=_;function nw(){let t=this.inverted;this.axes.forEach(t=>{t.stacking?.stacks&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(e=>{let i=e.xAxis?.options||{};e.options.stacking&&e.reserveSpace()&&(e.stackKey=[e.type,nM(e.options.stack,""),t?i.top:i.left,t?i.height:i.width].join(","))})}function nS(){let t=this.stacking;if(t){let e=t.stacks;nk(e,(t,i)=>{ny(t),delete e[i]}),t.stackTotalGroup?.destroy()}}function nA(){this.stacking||(this.stacking=new nL(this))}function nT(t,e,i,s){return!nx(t)||t.x!==e||s&&t.stackKey!==s?t={x:e,index:0,key:s,stackKey:s}:t.index++,t.key=[i,e,t.index].join(","),t}function nC(){let t,e=this,i=e.yAxis,s=e.stackKey||"",r=i.stacking.stacks,o=e.getColumn("x",!0),a=e.options.stacking,n=e[a+"Stacker"];n&&[s,"-"+s].forEach(i=>{let s=o.length,a,h,l;for(;s--;)a=o[s],t=e.getStackIndicator(t,a,e.index,i),h=r[i]?.[a],(l=h?.points[t.key||""])&&n.call(e,l,h,s)})}function nO(t,e,i){let s=e.total?100/e.total:0;t[0]=nm(t[0]*s),t[1]=nm(t[1]*s),this.stackedYData[i]=t[1]}function nP(t){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?nu.setStackedPoints.call(this,t,"group"):t.stacking.resetStacks())}function nE(t,e){let i,s,r,o,a,n,h,l=e||this.options.stacking;if(!l||!this.reserveSpace()||(({group:"xAxis"})[l]||"yAxis")!==t.coll)return;let d=this.getColumn("x",!0),c=this.getColumn(this.pointValKey||"y",!0),p=[],g=c.length,u=this.options,f=u.threshold||0,m=u.startFromThreshold?f:0,x=u.stack,y=e?`${this.type},${l}`:this.stackKey||"",b="-"+y,v=this.negStacks,k=t.stacking,M=k.stacks,w=k.oldStacks;for(k.stacksTouched+=1,h=0;h<g;h++){let e=d[h]||0,g=c[h],u=nv(g)&&g||0;n=(i=this.getStackIndicator(i,e,this.index)).key||"",M[a=(s=v&&u<(m?0:f))?b:y]||(M[a]={}),M[a][e]||(w[a]?.[e]?(M[a][e]=w[a][e],M[a][e].total=null):M[a][e]=new np(t,t.options.stackLabels,!!s,e,x)),r=M[a][e],null!==g?(r.points[n]=r.points[this.index]=[nM(r.cumulative,m)],nx(r.cumulative)||(r.base=n),r.touched=k.stacksTouched,i.index>0&&!1===this.singleStacks&&(r.points[n][0]=r.points[this.index+","+e+",0"][0])):(delete r.points[n],delete r.points[this.index]);let S=r.total||0;"percent"===l?(o=s?y:b,S=v&&M[o]?.[e]?(o=M[o][e]).total=Math.max(o.total||0,S)+Math.abs(u):nm(S+Math.abs(u))):"group"===l?nv(g)&&S++:S=nm(S+u),"group"===l?r.cumulative=(S||1)-1:r.cumulative=nm(nM(r.cumulative,m)+u),r.total=S,null!==g&&(r.points[n].push(r.cumulative),p[h]=r.cumulative,r.hasValidPoints=!0)}"percent"===l&&(k.usePercentage=!0),"group"!==l&&(this.stackedYData=p),k.oldStacks={}}class nL{constructor(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}buildStacks(){let t,e,i=this.axis,s=i.series,r="xAxis"===i.coll,o=i.options.reversedStacks,a=s.length;for(this.resetStacks(),this.usePercentage=!1,e=a;e--;)t=s[o?e:a-e-1],r&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!r)for(e=0;e<a;e++)s[e].modifyStacks();nb(i,"afterBuildStacks")}cleanStacks(){this.oldStacks&&(this.stacks=this.oldStacks,nk(this.stacks,t=>{nk(t,t=>{t.cumulative=t.total})}))}resetStacks(){nk(this.stacks,t=>{nk(t,(e,i)=>{nv(e.touched)&&e.touched<this.stacksTouched?(e.destroy(),delete t[i]):(e.total=null,e.cumulative=null)})})}renderStackTotals(){let t=this.axis,e=t.chart,i=e.renderer,s=this.stacks,r=ng(e,t.options.stackLabels?.animation||!1),o=this.stackTotalGroup=this.stackTotalGroup||i.g("stack-labels").attr({zIndex:6,opacity:0}).add();o.translate(e.plotLeft,e.plotTop),nk(s,t=>{nk(t,t=>{t.render(o)})}),o.animate({opacity:1},r)}}(u||(u={})).compose=function(t,e,i){let s=e.prototype,r=i.prototype;s.getStacks||(nf(t,"init",nA),nf(t,"destroy",nS),s.getStacks=nw,r.getStackIndicator=nT,r.modifyStacks=nC,r.percentStacker=nO,r.setGroupedPoints=nP,r.setStackedPoints=nE)};let nD=u,{defined:nI,merge:nB,isObject:nz}=_;class nN extends o3{drawGraph(){let t=this.options,e=(this.gappedPath||this.getGraphPath).call(this),i=this.chart.styledMode;[this,...this.zones].forEach((s,r)=>{let o,a=s.graph,n=a?"animate":"attr",h=s.dashStyle||t.dashStyle;a?(a.endX=this.preventGraphAnimation?null:e.xMap,a.animate({d:e})):e.length&&(s.graph=a=this.chart.renderer.path(e).addClass("highcharts-graph"+(r?` highcharts-zone-graph-${r-1} `:" ")+(r&&s.className||"")).attr({zIndex:1}).add(this.group)),a&&!i&&(o={stroke:!r&&t.lineColor||s.color||this.color||"#cccccc","stroke-width":t.lineWidth||0,fill:this.fillGraph&&this.color||"none"},h?o.dashstyle=h:"square"!==t.linecap&&(o["stroke-linecap"]=o["stroke-linejoin"]="round"),a[n](o).shadow(t.shadow&&nB({filterUnits:"userSpaceOnUse"},nz(t.shadow)?t.shadow:{}))),a&&(a.startX=e.xMap,a.isArea=e.isArea)})}getGraphPath(t,e,i){let s=this,r=s.options,o=[],a=[],n,h=r.step,l=(t=t||s.points).reversed;return l&&t.reverse(),(h=({right:1,center:2})[h]||h&&3)&&l&&(h=4-h),(t=this.getValidPoints(t,!1,r.nullInteraction||!(r.connectNulls&&!e&&!i))).forEach(function(l,d){let c,p=l.plotX,g=l.plotY,u=t[d-1],f=l.isNull||"number"!=typeof g;(l.leftCliff||u?.rightCliff)&&!i&&(n=!0),f&&!nI(e)&&d>0?n=!r.connectNulls:f&&!e?n=!0:(0===d||n?c=[["M",l.plotX,l.plotY]]:s.getPointSpline?c=[s.getPointSpline(t,l,d)]:h?(c=1===h?[["L",u.plotX,g]]:2===h?[["L",(u.plotX+p)/2,u.plotY],["L",(u.plotX+p)/2,g]]:[["L",p,u.plotY]]).push(["L",p,g]):c=[["L",p,g]],a.push(l.x),h&&(a.push(l.x),2===h&&a.push(l.x)),o.push.apply(o,c),n=!1)}),o.xMap=a,s.graphPath=o,o}}nN.defaultOptions=nB(o3.defaultOptions,{legendSymbol:"lineMarker"}),ow.registerSeriesType("line",nN);let{seriesTypes:{line:nR}}=ow,{extend:nW,merge:nH,objectEach:nX,pick:nF}=_;class nG extends nR{drawGraph(){this.areaPath=[],super.drawGraph.apply(this);let{areaPath:t,options:e}=this;[this,...this.zones].forEach((i,s)=>{let r={},o=i.fillColor||e.fillColor,a=i.area,n=a?"animate":"attr";a?(a.endX=this.preventGraphAnimation?null:t.xMap,a.animate({d:t})):(r.zIndex=0,(a=i.area=this.chart.renderer.path(t).addClass("highcharts-area"+(s?` highcharts-zone-area-${s-1} `:" ")+(s&&i.className||"")).add(this.group)).isArea=!0),this.chart.styledMode||(r.fill=o||i.color||this.color,r["fill-opacity"]=o?1:e.fillOpacity??.75,a.css({pointerEvents:this.stickyTracking?"none":"auto"})),a[n](r),a.startX=t.xMap,a.shiftUnit=e.step?2:1})}getGraphPath(t){let e,i,s,r=nR.prototype.getGraphPath,o=this.options,a=o.stacking,n=this.yAxis,h=[],l=[],d=this.index,c=n.stacking.stacks[this.stackKey],p=o.threshold,g=Math.round(n.getThreshold(o.threshold)),u=nF(o.connectNulls,"percent"===a),f=function(i,s,r){let o=t[i],u=a&&c[o.x].points[d],f=o[r+"Null"]||0,m=o[r+"Cliff"]||0,x,y,b=!0;m||f?(x=(f?u[0]:u[1])+m,y=u[0]+m,b=!!f):!a&&t[s]&&t[s].isNull&&(x=y=p),void 0!==x&&(l.push({plotX:e,plotY:null===x?g:n.getThreshold(x),isNull:b,isCliff:!0}),h.push({plotX:e,plotY:null===y?g:n.getThreshold(y),doCurve:!1}))};t=t||this.points,a&&(t=this.getStackPoints(t));for(let r=0,o=t.length;r<o;++r)a||(t[r].leftCliff=t[r].rightCliff=t[r].leftNull=t[r].rightNull=void 0),i=t[r].isNull,e=nF(t[r].rectPlotX,t[r].plotX),s=a?nF(t[r].yBottom,g):g,i&&!u||(u||f(r,r-1,"left"),i&&!a&&u||(l.push(t[r]),h.push({x:r,plotX:e,plotY:s})),u||f(r,r+1,"right"));let m=r.call(this,l,!0,!0);h.reversed=!0;let x=r.call(this,h,!0,!0),y=x[0];y&&"M"===y[0]&&(x[0]=["L",y[1],y[2]]);let b=m.concat(x);b.length&&b.push(["Z"]);let v=r.call(this,l,!1,u);return this.chart.series.length>1&&a&&l.some(t=>t.isCliff)&&(b.hasStackedCliffs=v.hasStackedCliffs=!0),b.xMap=m.xMap,this.areaPath=b,v}getStackPoints(t){let e=this,i=[],s=[],r=this.xAxis,o=this.yAxis,a=o.stacking.stacks[this.stackKey],n={},h=o.series,l=h.length,d=o.options.reversedStacks?1:-1,c=h.indexOf(e);if(t=t||this.points,this.options.stacking){for(let e=0;e<t.length;e++)t[e].leftNull=t[e].rightNull=void 0,n[t[e].x]=t[e];nX(a,function(t,e){null!==t.total&&s.push(e)}),s.sort(function(t,e){return t-e});let p=h.map(t=>t.visible);s.forEach(function(t,g){let u=0,f,m;if(n[t]&&!n[t].isNull)i.push(n[t]),[-1,1].forEach(function(i){let r=1===i?"rightNull":"leftNull",o=a[s[g+i]],u=0;if(o){let i=c;for(;i>=0&&i<l;){let s=h[i].index;!(f=o.points[s])&&(s===e.index?n[t][r]=!0:p[i]&&(m=a[t].points[s])&&(u-=m[1]-m[0])),i+=d}}n[t][1===i?"rightCliff":"leftCliff"]=u});else{let e=c;for(;e>=0&&e<l;){let i=h[e].index;if(f=a[t].points[i]){u=f[1];break}e+=d}u=nF(u,0),u=o.translate(u,0,1,0,1),i.push({isNull:!0,plotX:r.translate(t,0,0,0,1),x:t,plotY:u,yBottom:u})}})}return i}}nG.defaultOptions=nH(nR.defaultOptions,{threshold:0,legendSymbol:"areaMarker"}),nW(nG.prototype,{singleStacks:!1}),ow.registerSeriesType("area",nG);let{line:nY}=ow.seriesTypes,{merge:nj,pick:nU}=_;class nV extends nY{getPointSpline(t,e,i){let s,r,o,a,n=e.plotX||0,h=e.plotY||0,l=t[i-1],d=t[i+1];function c(t){return t&&!t.isNull&&!1!==t.doCurve&&!e.isCliff}if(c(l)&&c(d)){let t=l.plotX||0,i=l.plotY||0,c=d.plotX||0,p=d.plotY||0,g=0;s=(1.5*n+t)/2.5,r=(1.5*h+i)/2.5,o=(1.5*n+c)/2.5,a=(1.5*h+p)/2.5,o!==s&&(g=(a-r)*(o-n)/(o-s)+h-a),r+=g,a+=g,r>i&&r>h?(r=Math.max(i,h),a=2*h-r):r<i&&r<h&&(r=Math.min(i,h),a=2*h-r),a>p&&a>h?(a=Math.max(p,h),r=2*h-a):a<p&&a<h&&(a=Math.min(p,h),r=2*h-a),e.rightContX=o,e.rightContY=a,e.controlPoints={low:[s,r],high:[o,a]}}let p=["C",nU(l.rightContX,l.plotX,0),nU(l.rightContY,l.plotY,0),nU(s,n,0),nU(r,h,0),n,h];return l.rightContX=l.rightContY=void 0,p}}nV.defaultOptions=nj(nY.defaultOptions),ow.registerSeriesType("spline",nV);let n$=nV,{area:nZ,area:{prototype:n_}}=ow.seriesTypes,{extend:nq,merge:nK}=_;class nJ extends n${}nJ.defaultOptions=nK(n$.defaultOptions,nZ.defaultOptions),nq(nJ.prototype,{getGraphPath:n_.getGraphPath,getStackPoints:n_.getStackPoints,drawGraph:n_.drawGraph}),ow.registerSeriesType("areaspline",nJ);let{animObject:nQ}=t$,{parse:n0}=tL,{noop:n1}=S,{clamp:n2,crisp:n3,defined:n5,extend:n6,fireEvent:n9,isArray:n4,isNumber:n8,merge:n7,pick:ht,objectEach:he}=_;class hi extends o3{animate(t){let e,i,s=this,r=this.yAxis,o=r.pos,a=r.reversed,n=s.options,{clipOffset:h,inverted:l}=this.chart,d={},c=l?"translateX":"translateY";t&&h?(d.scaleY=.001,i=n2(r.toPixels(n.threshold||0),o,o+r.len),l?d.translateX=(i+=a?-Math.floor(h[0]):Math.ceil(h[2]))-r.len:d.translateY=i+=a?Math.ceil(h[0]):-Math.floor(h[2]),s.clipBox&&s.setClip(),s.group.attr(d)):(e=Number(s.group.attr(c)),s.group.animate({scaleY:1},n6(nQ(s.options.animation),{step:function(t,i){s.group&&(d[c]=e+i.pos*(o-e),s.group.attr(d))}})))}init(t,e){super.init.apply(this,arguments);let i=this;(t=i.chart).hasRendered&&t.series.forEach(function(t){t.type===i.type&&(t.isDirty=!0)})}getColumnMetrics(){let t=this,e=t.options,i=t.xAxis,s=t.yAxis,r=i.options.reversedStacks,o=i.reversed&&!r||!i.reversed&&r,a={},n,h=0;!1===e.grouping?h=1:t.chart.series.forEach(function(e){let i,r=e.yAxis,o=e.options;e.type===t.type&&e.reserveSpace()&&s.len===r.len&&s.pos===r.pos&&(o.stacking&&"group"!==o.stacking?(void 0===a[n=e.stackKey]&&(a[n]=h++),i=a[n]):!1!==o.grouping&&(i=h++),e.columnIndex=i)});let l=Math.min(Math.abs(i.transA)*(!i.brokenAxis?.hasBreaks&&i.ordinal?.slope||e.pointRange||i.closestPointRange||i.tickInterval||1),i.len),d=l*e.groupPadding,c=(l-2*d)/(h||1),p=Math.min(e.maxPointWidth||i.len,ht(e.pointWidth,c*(1-2*e.pointPadding))),g=(t.columnIndex||0)+ +!!o;return t.columnMetrics={width:p,offset:(c-p)/2+(d+g*c-l/2)*(o?-1:1),paddedWidth:c,columnCount:h},t.columnMetrics}crispCol(t,e,i,s){let r=this.borderWidth,o=this.chart.inverted;return s=n3(e+s,r,o)-(e=n3(e,r,o)),this.options.crisp&&(i=n3(t+i,r)-(t=n3(t,r))),{x:t,y:e,width:i,height:s}}adjustForMissingColumns(t,e,i,s){if(!i.isNull&&s.columnCount>1){let r=this.xAxis.series.filter(t=>t.visible).map(t=>t.index),o=0,a=0;he(this.xAxis.stacking?.stacks,t=>{let e="number"==typeof i.x?t[i.x.toString()]?.points:void 0,s=e?.[this.index],n={};if(e&&n4(s)){let t=this.index,i=Object.keys(e).filter(t=>!t.match(",")&&e[t]&&e[t].length>1).map(parseFloat).filter(t=>-1!==r.indexOf(t)).filter(e=>{let i=this.chart.series[e].options,s=i.stacking&&i.stack;if(n5(s)){if(n8(n[s]))return t===e&&(t=n[s]),!1;n[s]=e}return!0}).sort((t,e)=>e-t);o=i.indexOf(t),a=i.length}}),o=this.xAxis.reversed?a-1-o:o;let n=(a-1)*s.paddedWidth+e;t=(i.plotX||0)+n/2-e-o*s.paddedWidth}return t}translate(){let t=this,e=t.chart,i=t.options,s=t.dense=t.closestPointRange*t.xAxis.transA<2,r=t.borderWidth=ht(i.borderWidth,+!s),o=t.xAxis,a=t.yAxis,n=i.threshold,h=ht(i.minPointLength,5),l=t.getColumnMetrics(),d=l.width,c=t.pointXOffset=l.offset,p=t.dataMin,g=t.dataMax,u=t.translatedThreshold=a.getThreshold(n),f=t.barW=Math.max(d,1+2*r);i.pointPadding&&i.crisp&&(f=Math.ceil(f)),o3.prototype.translate.apply(t),t.points.forEach(function(s){let r=ht(s.yBottom,u),m=999+Math.abs(r),x=s.plotX||0,y=n2(s.plotY,-m,a.len+m),b,v=Math.min(y,r),k=Math.max(y,r)-v,M=d,w=x+c,S=f;h&&Math.abs(k)<h&&(k=h,b=!a.reversed&&!s.negative||a.reversed&&s.negative,n8(n)&&n8(g)&&s.y===n&&g<=n&&(a.min||0)<n&&(p!==g||(a.max||0)<=n)&&(b=!b,s.negative=!s.negative),v=Math.abs(v-u)>h?r-h:u-(b?h:0)),n5(s.options.pointWidth)&&(w-=Math.round(((M=S=Math.ceil(s.options.pointWidth))-d)/2)),i.centerInCategory&&(w=t.adjustForMissingColumns(w,M,s,l)),s.barX=w,s.pointWidth=M,s.tooltipPos=e.inverted?[n2(a.len+a.pos-e.plotLeft-y,a.pos-e.plotLeft,a.len+a.pos-e.plotLeft),o.len+o.pos-e.plotTop-w-S/2,k]:[o.left-e.plotLeft+w+S/2,n2(y+a.pos-e.plotTop,a.pos-e.plotTop,a.len+a.pos-e.plotTop),k],s.shapeType=t.pointClass.prototype.shapeType||"roundedRect",s.shapeArgs=t.crispCol(w,v,S,s.isNull?0:k)}),n9(this,"afterColumnTranslate")}drawGraph(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")}pointAttribs(t,e){let i=this.options,s=this.pointAttrToOptions||{},r=s.stroke||"borderColor",o=s["stroke-width"]||"borderWidth",a,n,h,l=t&&t.color||this.color,d=t&&t[r]||i[r]||l,c=t&&t.options.dashStyle||i.dashStyle,p=t&&t[o]||i[o]||this[o]||0,g=t?.isNull&&i.nullInteraction?0:t?.opacity??i.opacity??1;t&&this.zones.length&&(n=t.getZone(),l=t.options.color||n&&(n.color||t.nonZonedColor)||this.color,n&&(d=n.borderColor||d,c=n.dashStyle||c,p=n.borderWidth||p)),e&&t&&(h=(a=n7(i.states[e],t.options.states&&t.options.states[e]||{})).brightness,l=a.color||void 0!==h&&n0(l).brighten(a.brightness).get()||l,d=a[r]||d,p=a[o]||p,c=a.dashStyle||c,g=ht(a.opacity,g));let u={fill:l,stroke:d,"stroke-width":p,opacity:g};return c&&(u.dashstyle=c),u}drawPoints(t=this.points){let e,i=this,s=this.chart,r=i.options,o=r.nullInteraction,a=s.renderer,n=r.animationLimit||250;t.forEach(function(t){let h=t.plotY,l=t.graphic,d=!!l,c=l&&s.pointCount<n?"animate":"attr";n8(h)&&(null!==t.y||o)?(e=t.shapeArgs,l&&t.hasNewShapeType()&&(l=l.destroy()),i.enabledDataSorting&&(t.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!l&&(t.graphic=l=a[t.shapeType](e).add(t.group||i.group),l&&i.enabledDataSorting&&s.hasRendered&&s.pointCount<n&&(l.attr({x:t.startXPos}),d=!0,c="animate")),l&&d&&l[c](n7(e)),s.styledMode||l[c](i.pointAttribs(t,t.selected&&"select")).shadow(!1!==t.allowShadow&&r.shadow),l&&(l.addClass(t.getClassName(),!0),l.attr({visibility:t.visible?"inherit":"hidden"}))):l&&(t.graphic=l.destroy())})}drawTracker(t=this.points){let e,i=this,s=i.chart,r=s.pointer,o=function(t){r?.normalize(t);let e=r?.getPointFromEvent(t);r&&e&&i.options.enableMouseTracking&&(s.isInsidePlot(t.chartX-s.plotLeft,t.chartY-s.plotTop,{visiblePlotOnly:!0})||r?.inClass(t.target,"highcharts-data-label"))&&(r.isDirectTouch=!0,e.onMouseOver(t))};t.forEach(function(t){e=n4(t.dataLabels)?t.dataLabels:t.dataLabel?[t.dataLabel]:[],t.graphic&&(t.graphic.element.point=t),e.forEach(function(e){(e.div||e.element).point=t})}),i._hasTracking||(i.trackerGroups.forEach(function(t){i[t]&&(i[t].addClass("highcharts-tracker").on("mouseover",o).on("mouseout",function(t){r?.onTrackerMouseOut(t)}).on("touchstart",o),!s.styledMode&&i.options.cursor&&i[t].css({cursor:i.options.cursor}))}),i._hasTracking=!0),n9(this,"afterDrawTracker")}remove(){let t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),o3.prototype.remove.apply(t,arguments)}}hi.defaultOptions=n7(o3.defaultOptions,{borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"}),n6(hi.prototype,{directTouch:!0,getSymbol:n1,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),ow.registerSeriesType("column",hi);let hs=hi,{getDeferredAnimation:hr}=t$,{format:ho}=eu,{defined:ha,extend:hn,fireEvent:hh,getAlignFactor:hl,isArray:hd,isString:hc,merge:hp,objectEach:hg,pick:hu,pInt:hf,splat:hm}=_;!function(t){function e(){return h(this).some(t=>t?.enabled)}function i(t,e,i,s,r){let{chart:o,enabledDataSorting:a}=this,n=this.isCartesian&&o.inverted,h=t.plotX,l=t.plotY,d=i.rotation||0,c=ha(h)&&ha(l)&&o.isInsidePlot(h,Math.round(l),{inverted:n,paneCoordinates:!0,series:this}),p=0===d&&"justify"===hu(i.overflow,a?"none":"justify"),g=this.visible&&!1!==t.visible&&ha(h)&&(t.series.forceDL||a&&!p||c||hu(i.inside,!!this.options.stacking)&&s&&o.isInsidePlot(h,n?s.x+1:s.y+s.height-1,{inverted:n,paneCoordinates:!0,series:this})),u=t.pos();if(g&&u){var f;let h=e.getBBox(),l=e.getBBox(void 0,0);if(s=hn({x:u[0],y:Math.round(u[1]),width:0,height:0},s||{}),"plotEdges"===i.alignTo&&this.isCartesian&&(s[n?"x":"y"]=0,s[n?"width":"height"]=this.yAxis?.len||0),hn(i,{width:h.width,height:h.height}),f=s,a&&this.xAxis&&!p&&this.setDataLabelStartPos(t,e,r,c,f),e.align(hp(i,{width:l.width,height:l.height}),!1,s,!1),e.alignAttr.x+=hl(i.align)*(l.width-h.width),e.alignAttr.y+=hl(i.verticalAlign)*(l.height-h.height),e[e.placed?"animate":"attr"]({"text-align":e.alignAttr["text-align"]||"center",x:e.alignAttr.x+(h.width-l.width)/2,y:e.alignAttr.y+(h.height-l.height)/2,rotationOriginX:(e.width||0)/2,rotationOriginY:(e.height||0)/2}),p&&s.height>=0)this.justifyDataLabel(e,i,e.alignAttr,h,s,r);else if(hu(i.crop,!0)){let{x:t,y:i}=e.alignAttr;g=o.isInsidePlot(t,i,{paneCoordinates:!0,series:this})&&o.isInsidePlot(t+h.width-1,i+h.height-1,{paneCoordinates:!0,series:this})}i.shape&&!d&&e[r?"attr":"animate"]({anchorX:u[0],anchorY:u[1]})}r&&a&&(e.placed=!1),g||a&&!p?(e.show(),e.placed=!0):(e.hide(),e.placed=!1)}function s(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function r(t){let e=this.hasRendered||0,i=this.initDataLabelsGroup().attr({opacity:+e});return!e&&i&&(this.visible&&i.show(),this.options.animation?i.animate({opacity:1},t):i.attr({opacity:1})),i}function o(t){let e;t=t||this.points;let i=this,s=i.chart,r=i.options,o=s.renderer,{backgroundColor:a,plotBackgroundColor:l}=s.options.chart,d=o.getContrast(hc(l)&&l||hc(a)&&a||"#000000"),c=h(i),{animation:p,defer:g}=c[0],u=g?hr(s,p,i):{defer:0,duration:0};hh(this,"drawDataLabels"),i.hasDataLabels?.()&&(e=this.initDataLabels(u),t.forEach(t=>{let a=t.dataLabels||[],h=t.color||i.color;hm(n(c,t.dlOptions||t.options?.dataLabels)).forEach((n,l)=>{let c=n.enabled&&(t.visible||t.dataLabelOnHidden)&&(!t.isNull||t.dataLabelOnNull)&&function(t,e){let i=e.filter;if(i){let e=i.operator,s=t[i.property],r=i.value;return">"===e&&s>r||"<"===e&&s<r||">="===e&&s>=r||"<="===e&&s<=r||"=="===e&&s==r||"==="===e&&s===r||"!="===e&&s!=r||"!=="===e&&s!==r||!1}return!0}(t,n),{backgroundColor:p,borderColor:g,distance:u,style:f={}}=n,m,x,y,b={},v=a[l],k=!v,M;c&&(x=ha(m=hu(n[t.formatPrefix+"Format"],n.format))?ho(m,t,s):(n[t.formatPrefix+"Formatter"]||n.formatter).call(t,n),y=n.rotation,!s.styledMode&&(f.color=hu(n.color,f.color,hc(i.color)?i.color:void 0,"#000000"),"contrast"===f.color?("none"!==p&&(M=p),t.contrastColor=o.getContrast("auto"!==M&&hc(M)&&M||(hc(h)?h:"")),f.color=M||!ha(u)&&n.inside||0>hf(u||0)||r.stacking?t.contrastColor:d):delete t.contrastColor,r.cursor&&(f.cursor=r.cursor)),b={r:n.borderRadius||0,rotation:y,padding:n.padding,zIndex:1},s.styledMode||(b.fill="auto"===p?t.color:p,b.stroke="auto"===g?t.color:g,b["stroke-width"]=n.borderWidth),hg(b,(t,e)=>{void 0===t&&delete b[e]})),!v||c&&ha(x)&&!!(v.div||v.text?.foreignObject)==!!n.useHTML&&(v.rotation&&n.rotation||v.rotation===n.rotation)||(v=void 0,k=!0),c&&ha(x)&&(v?b.text=x:(v=o.label(x,0,0,n.shape,void 0,void 0,n.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+t.colorIndex+" "+(n.className||"")+(n.useHTML?" highcharts-tracker":"")),v&&(v.options=n,v.attr(b),s.styledMode?f.width&&v.css({width:f.width,textOverflow:f.textOverflow,whiteSpace:f.whiteSpace}):v.css(f).shadow(n.shadow),hh(v,"beforeAddingDataLabel",{labelOptions:n,point:t}),v.added||v.add(e),i.alignDataLabel(t,v,n,void 0,k),v.isActive=!0,a[l]&&a[l]!==v&&a[l].destroy(),a[l]=v))});let l=a.length;for(;l--;)a[l]?.isActive?a[l].isActive=!1:(a[l]?.destroy(),a.splice(l,1));t.dataLabel=a[0],t.dataLabels=a})),hh(this,"afterDrawDataLabels")}function a(t,e,i,s,r,o){let a=this.chart,n=e.align,h=e.verticalAlign,l=t.box?0:t.padding||0,d=a.inverted?this.yAxis:this.xAxis,c=d?d.left-a.plotLeft:0,p=a.inverted?this.xAxis:this.yAxis,g=p?p.top-a.plotTop:0,{x:u=0,y:f=0}=e,m,x;return(m=(i.x||0)+l+c)<0&&("right"===n&&u>=0?(e.align="left",e.inside=!0):u-=m,x=!0),(m=(i.x||0)+s.width-l+c)>a.plotWidth&&("left"===n&&u<=0?(e.align="right",e.inside=!0):u+=a.plotWidth-m,x=!0),(m=i.y+l+g)<0&&("bottom"===h&&f>=0?(e.verticalAlign="top",e.inside=!0):f-=m,x=!0),(m=(i.y||0)+s.height-l+g)>a.plotHeight&&("top"===h&&f<=0?(e.verticalAlign="bottom",e.inside=!0):f+=a.plotHeight-m,x=!0),x&&(e.x=u,e.y=f,t.placed=!o,t.align(e,void 0,r)),x}function n(t,e){let i=[],s;if(hd(t)&&!hd(e))i=t.map(function(t){return hp(t,e)});else if(hd(e)&&!hd(t))i=e.map(function(e){return hp(t,e)});else if(hd(t)||hd(e)){if(hd(t)&&hd(e))for(s=Math.max(t.length,e.length);s--;)i[s]=hp(t[s],e[s])}else i=hp(t,e);return i}function h(t){let e=t.chart.options.plotOptions;return hm(n(n(e?.series?.dataLabels,e?.[t.type]?.dataLabels),t.options.dataLabels))}function l(t,e,i,s,r){let o=this.chart,a=o.inverted,n=this.xAxis,h=n.reversed,l=((a?e.height:e.width)||0)/2,d=t.pointWidth,c=d?d/2:0;e.startXPos=a?r.x:h?-l-c:n.width-l+c,e.startYPos=a?h?this.yAxis.height-l+c:-l-c:r.y,s?"hidden"===e.visibility&&(e.show(),e.attr({opacity:0}).animate({opacity:1})):e.attr({opacity:1}).animate({opacity:0},void 0,e.hide),o.hasRendered&&(i&&e.attr({x:e.startXPos,y:e.startYPos}),e.placed=!0)}t.compose=function(t){let h=t.prototype;h.initDataLabels||(h.initDataLabels=r,h.initDataLabelsGroup=s,h.alignDataLabel=i,h.drawDataLabels=o,h.justifyDataLabel=a,h.mergeArrays=n,h.setDataLabelStartPos=l,h.hasDataLabels=e)}}(f||(f={}));let hx=f,{composed:hy}=S,{series:hb}=ow,{merge:hv,pushUnique:hk}=_;!function(t){function e(t,e,i,s,r){let{chart:o,options:a}=this,n=o.inverted,h=this.xAxis?.len||o.plotSizeX||0,l=this.yAxis?.len||o.plotSizeY||0,d=t.dlBox||t.shapeArgs,c=t.below??(t.plotY||0)>(this.translatedThreshold??l),p=i.inside??!!a.stacking;if(d){if(s=hv(d),"allow"!==i.overflow||!1!==i.crop||!1!==a.clip){s.y<0&&(s.height+=s.y,s.y=0);let t=s.y+s.height-l;t>0&&t<s.height-1&&(s.height-=t)}n&&(s={x:l-s.y-s.height,y:h-s.x-s.width,width:s.height,height:s.width}),p||(n?(s.x+=c?0:s.width,s.width=0):(s.y+=c?s.height:0,s.height=0))}i.align??(i.align=!n||p?"center":c?"right":"left"),i.verticalAlign??(i.verticalAlign=n||p?"middle":c?"top":"bottom"),hb.prototype.alignDataLabel.call(this,t,e,i,s,r),i.inside&&t.contrastColor&&e.css({color:t.contrastColor})}t.compose=function(t){hx.compose(hb),hk(hy,"ColumnDataLabel")&&(t.prototype.alignDataLabel=e)}}(m||(m={}));let hM=m,{extend:hw,merge:hS}=_;class hA extends hs{}hA.defaultOptions=hS(hs.defaultOptions,{}),hw(hA.prototype,{inverted:!0}),ow.registerSeriesType("bar",hA);let{column:hT,line:hC}=ow.seriesTypes,{addEvent:hO,extend:hP,merge:hE}=_;class hL extends hC{applyJitter(){let t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(s,r){["x","y"].forEach(function(o,a){if(e[o]&&!s.isNull){let n=`plot${o.toUpperCase()}`,h=t[`${o}Axis`],l=e[o]*h.transA;if(h&&!h.logarithmic){let t=Math.max(0,(s[n]||0)-l),e=Math.min(h.len,(s[n]||0)+l);s[n]=t+(e-t)*function(t){let e=1e4*Math.sin(t);return e-Math.floor(e)}(r+a*i),"x"===o&&(s.clientX=s.plotX)}}})})}drawGraph(){this.options.lineWidth?super.drawGraph():this.graph&&(this.graph=this.graph.destroy())}}hL.defaultOptions=hE(hC.defaultOptions,{lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}}),hP(hL.prototype,{drawTracker:hT.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),hO(hL,"afterTranslate",function(){this.applyJitter()}),ow.registerSeriesType("scatter",hL);let{deg2rad:hD}=S,{fireEvent:hI,isNumber:hB,pick:hz,relativeLength:hN}=_;!function(t){t.getCenter=function(){let t=this.options,e=this.chart,i=2*(t.slicedOffset||0),s=e.plotWidth-2*i,r=e.plotHeight-2*i,o=t.center,a=Math.min(s,r),n=t.thickness,h,l=t.size,d=t.innerSize||0,c,p;"string"==typeof l&&(l=parseFloat(l)),"string"==typeof d&&(d=parseFloat(d));let g=[hz(o?.[0],"50%"),hz(o?.[1],"50%"),hz(l&&l<0?void 0:t.size,"100%"),hz(d&&d<0?void 0:t.innerSize||0,"0%")];for(!e.angular||this instanceof o3||(g[3]=0),c=0;c<4;++c)p=g[c],h=c<2||2===c&&/%$/.test(p),g[c]=hN(p,[s,r,a,g[2]][c])+(h?i:0);return g[3]>g[2]&&(g[3]=g[2]),hB(n)&&2*n<g[2]&&n>0&&(g[3]=g[2]-2*n),hI(this,"afterGetCenter",{positions:g}),g},t.getStartAndEndRadians=function(t,e){let i=hB(t)?t:0,s=hB(e)&&e>i&&e-i<360?e:i+360;return{start:hD*(i+-90),end:hD*(s+-90)}}}(x||(x={}));let hR=x,{setAnimation:hW}=t$,{addEvent:hH,defined:hX,extend:hF,isNumber:hG,pick:hY,relativeLength:hj}=_;class hU extends rQ{getConnectorPath(t){let e=t.dataLabelPosition,i=t.options||{},s=i.connectorShape,r=this.connectorShapes[s]||s;return e&&r.call(this,{...e.computed,alignment:e.alignment},e.connectorPosition,i)||[]}getTranslate(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}}haloPath(t){let e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})}constructor(t,e,i){super(t,e,i),this.half=0,this.name??(this.name=t.chart.options.lang.pieSliceName);let s=t=>{this.slice("select"===t.type)};hH(this,"select",s),hH(this,"unselect",s)}isValid(){return hG(this.y)&&this.y>=0}setVisible(t,e=!0){t!==this.visible&&this.update({visible:t??!this.visible},e,void 0,!1)}slice(t,e,i){let s=this.series;hW(i,s.chart),e=hY(e,!0),this.sliced=this.options.sliced=t=hX(t)?t:!this.sliced,s.options.data[s.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())}}hF(hU.prototype,{connectorShapes:{fixedOffset:function(t,e,i){let s=e.breakAt,r=e.touchingSliceAt,o=i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*s.x-r.x,2*s.y-r.y,s.x,s.y]:["L",s.x,s.y];return[["M",t.x,t.y],o,["L",r.x,r.y]]},straight:function(t,e){let i=e.touchingSliceAt;return[["M",t.x,t.y],["L",i.x,i.y]]},crookedLine:function(t,e,i){let{angle:s=this.angle||0,breakAt:r,touchingSliceAt:o}=e,{series:a}=this,[n,h,l]=a.center,d=l/2,{plotLeft:c,plotWidth:p}=a.chart,g="left"===t.alignment,{x:u,y:f}=t,m=r.x;if(i.crookDistance){let t=hj(i.crookDistance,1);m=g?n+d+(p+c-n-d)*(1-t):c+(n-d)*t}else m=n+(h-f)*Math.tan(s-Math.PI/2);let x=[["M",u,f]];return(g?m<=u&&m>=r.x:m>=u&&m<=r.x)&&x.push(["L",m,f]),x.push(["L",r.x,r.y],["L",o.x,o.y]),x}}});let{getStartAndEndRadians:hV}=hR,{noop:h$}=S,{clamp:hZ,extend:h_,fireEvent:hq,merge:hK,pick:hJ}=_;class hQ extends o3{animate(t){let e=this,i=e.points,s=e.startAngleRad;t||i.forEach(function(t){let i=t.graphic,r=t.shapeArgs;i&&r&&(i.attr({r:hJ(t.startR,e.center&&e.center[3]/2),start:s,end:s}),i.animate({r:r.r,start:r.start,end:r.end},e.options.animation))})}drawEmpty(){let t,e,i=this.startAngleRad,s=this.endAngleRad,r=this.options;0===this.total&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,s).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:io.arc(t,e,this.center[2]/2,0,{start:i,end:s,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":r.borderWidth,fill:r.fillColor||"none",stroke:r.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())}drawPoints(){let t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})}generatePoints(){super.generatePoints(),this.updateTotals()}getX(t,e,i,s){let r=this.center,o=this.radii?this.radii[i.index]||0:r[2]/2,a=s.dataLabelPosition,n=a?.distance||0,h=Math.asin(hZ((t-r[1])/(o+n),-1,1));return r[0]+Math.cos(h)*(o+n)*(e?-1:1)+(n>0?(e?-1:1)*(s.padding||0):0)}hasData(){return!!this.dataTable.rowCount}redrawPoints(){let t,e,i,s,r=this,o=r.chart;this.drawEmpty(),r.group&&!o.styledMode&&r.group.shadow(r.options.shadow),r.points.forEach(function(a){let n={};e=a.graphic,!a.isNull&&e?(s=a.shapeArgs,t=a.getTranslate(),o.styledMode||(i=r.pointAttribs(a,a.selected&&"select")),a.delayedRendering?(e.setRadialReference(r.center).attr(s).attr(t),o.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),a.delayedRendering=!1):(e.setRadialReference(r.center),o.styledMode||hK(!0,n,i),hK(!0,n,s,t),e.animate(n)),e.attr({visibility:a.visible?"inherit":"hidden"}),e.addClass(a.getClassName(),!0)):e&&(a.graphic=e.destroy())})}sortByAngle(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})}translate(t){hq(this,"translate"),this.generatePoints();let e=this.options,i=e.slicedOffset,s=hV(e.startAngle,e.endAngle),r=this.startAngleRad=s.start,o=(this.endAngleRad=s.end)-r,a=this.points,n=e.ignoreHiddenPoint,h=a.length,l,d,c,p,g,u,f,m=0;for(t||(this.center=t=this.getCenter()),u=0;u<h;u++){f=a[u],l=r+m*o,f.isValid()&&(!n||f.visible)&&(m+=f.percentage/100),d=r+m*o;let e={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*l)/1e3,end:Math.round(1e3*d)/1e3};f.shapeType="arc",f.shapeArgs=e,(c=(d+l)/2)>1.5*Math.PI?c-=2*Math.PI:c<-Math.PI/2&&(c+=2*Math.PI),f.slicedTranslation={translateX:Math.round(Math.cos(c)*i),translateY:Math.round(Math.sin(c)*i)},p=Math.cos(c)*t[2]/2,g=Math.sin(c)*t[2]/2,f.tooltipPos=[t[0]+.7*p,t[1]+.7*g],f.half=+(c<-Math.PI/2||c>Math.PI/2),f.angle=c}hq(this,"afterTranslate")}updateTotals(){let t=this.points,e=t.length,i=this.options.ignoreHiddenPoint,s,r,o=0;for(s=0;s<e;s++)(r=t[s]).isValid()&&(!i||r.visible)&&(o+=r.y);for(s=0,this.total=o;s<e;s++)(r=t[s]).percentage=o>0&&(r.visible||!i)?r.y/o*100:0,r.total=o}}hQ.defaultOptions=hK(o3.defaultOptions,{borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}}),h_(hQ.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:hs.prototype.drawTracker,getCenter:hR.getCenter,getSymbol:h$,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:hs.prototype.pointAttribs,pointClass:hU,requireSorting:!1,searchPoint:h$,trackerGroups:["group","dataLabelsGroup"]}),ow.registerSeriesType("pie",hQ);let{composed:h0,noop:h1}=S,{distribute:h2}=ev,{series:h3}=ow,{arrayMax:h5,clamp:h6,defined:h9,pick:h4,pushUnique:h8,relativeLength:h7}=_;!function(t){let e={radialDistributionY:function(t,e){return(e.dataLabelPosition?.top||0)+t.distributeBox.pos},radialDistributionX:function(t,e,i,s,r){let o=r.dataLabelPosition;return t.getX(i<(o?.top||0)+2||i>(o?.bottom||0)-2?s:i,e.half,e,r)},justify:function(t,e,i,s){return s[0]+(t.half?-1:1)*(i+(e.dataLabelPosition?.distance||0))},alignToPlotEdges:function(t,e,i,s){let r=t.getBBox().width;return e?r+s:i-r-s},alignToConnectors:function(t,e,i,s){let r=0,o;return t.forEach(function(t){(o=t.dataLabel.getBBox().width)>r&&(r=o)}),e?r+s:i-r-s}};function i(t,e){let i=Math.PI/2,{start:s=0,end:r=0}=t.shapeArgs||{},o=t.angle||0;e>0&&s<i&&r>i&&o>i/2&&o<1.5*i&&(o=o<=i?Math.max(i/2,(s+i)/2):Math.min(1.5*i,(i+r)/2));let{center:a,options:n}=this,h=a[2]/2,l=Math.cos(o),d=Math.sin(o),c=a[0]+l*h,p=a[1]+d*h,g=Math.min((n.slicedOffset||0)+(n.borderWidth||0),e/5);return{natural:{x:c+l*e,y:p+d*e},computed:{},alignment:e<0?"center":t.half?"right":"left",connectorPosition:{angle:o,breakAt:{x:c+l*g,y:p+d*g},touchingSliceAt:{x:c,y:p}},distance:e}}function s(){let t=this,e=t.points,i=t.chart,s=i.plotWidth,r=i.plotHeight,o=i.plotLeft,a=Math.round(i.chartWidth/3),n=t.center,h=n[2]/2,l=n[1],d=[[],[]],c=[0,0,0,0],p=t.dataLabelPositioners,g,u,f,m=0;t.visible&&t.hasDataLabels?.()&&(e.forEach(t=>{(t.dataLabels||[]).forEach(t=>{t.shortened&&(t.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.shortened=!1)})}),h3.prototype.drawDataLabels.apply(t),e.forEach(t=>{(t.dataLabels||[]).forEach((e,i)=>{let s=n[2]/2,r=e.options,o=h7(r?.distance||0,s);0===i&&d[t.half].push(t),!h9(r?.style?.width)&&e.getBBox().width>a&&(e.css({width:Math.round(.7*a)+"px"}),e.shortened=!0),e.dataLabelPosition=this.getDataLabelPosition(t,o),m=Math.max(m,o)})}),d.forEach((e,a)=>{let d=e.length,g=[],x,y,b=0,v;d&&(t.sortByAngle(e,a-.5),m>0&&(x=Math.max(0,l-h-m),y=Math.min(l+h+m,i.plotHeight),e.forEach(t=>{(t.dataLabels||[]).forEach(e=>{let s=e.dataLabelPosition;s&&s.distance>0&&(s.top=Math.max(0,l-h-s.distance),s.bottom=Math.min(l+h+s.distance,i.plotHeight),b=e.getBBox().height||21,e.lineHeight=i.renderer.fontMetrics(e.text||e).h+2*e.padding,t.distributeBox={target:(e.dataLabelPosition?.natural.y||0)-s.top+e.lineHeight/2,size:b,rank:t.y},g.push(t.distributeBox))})}),h2(g,v=y+b-x,v/5)),e.forEach(i=>{(i.dataLabels||[]).forEach(l=>{let d=l.options||{},m=i.distributeBox,x=l.dataLabelPosition,y=x?.natural.y||0,b=d.connectorPadding||0,v=l.lineHeight||21,k=(v-l.getBBox().height)/2,M=0,w=y,S="inherit";if(x){if(g&&h9(m)&&x.distance>0&&(void 0===m.pos?S="hidden":(f=m.size,w=p.radialDistributionY(i,l))),d.justify)M=p.justify(i,l,h,n);else switch(d.alignTo){case"connectors":M=p.alignToConnectors(e,a,s,o);break;case"plotEdges":M=p.alignToPlotEdges(l,a,s,o);break;default:M=p.radialDistributionX(t,i,w-k,y,l)}if(x.attribs={visibility:S,align:x.alignment},x.posAttribs={x:M+(d.x||0)+(({left:b,right:-b})[x.alignment]||0),y:w+(d.y||0)-v/2},x.computed.x=M,x.computed.y=w-k,h4(d.crop,!0)){let t;M-(u=l.getBBox().width)<b&&1===a?(t=Math.round(u-M+b),c[3]=Math.max(t,c[3])):M+u>s-b&&0===a&&(t=Math.round(M+u-s+b),c[1]=Math.max(t,c[1])),w-f/2<0?c[0]=Math.max(Math.round(-w+f/2),c[0]):w+f/2>r&&(c[2]=Math.max(Math.round(w+f/2-r),c[2])),x.sideOverflow=t}}})}))}),(0===h5(c)||this.verifyDataLabelOverflow(c))&&(this.placeDataLabels(),this.points.forEach(e=>{(e.dataLabels||[]).forEach(s=>{let{connectorColor:r,connectorWidth:o=1}=s.options||{},a=s.dataLabelPosition;if(o){let n;g=s.connector,a&&a.distance>0?(n=!g,g||(s.connector=g=i.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+e.colorIndex+(e.className?" "+e.className:"")).add(t.dataLabelsGroup)),i.styledMode||g.attr({"stroke-width":o,stroke:r||e.color||"#666666"}),g[n?"attr":"animate"]({d:e.getConnectorPath(s)}),g.attr({visibility:a.attribs?.visibility})):g&&(s.connector=g.destroy())}})})))}function r(){this.points.forEach(t=>{(t.dataLabels||[]).forEach(t=>{let e=t.dataLabelPosition;e?(e.sideOverflow&&(t.css({width:Math.max(t.getBBox().width-e.sideOverflow,0)+"px",textOverflow:t.options?.style?.textOverflow||"ellipsis"}),t.shortened=!0),t.attr(e.attribs),t[t.moved?"animate":"attr"](e.posAttribs),t.moved=!0):t&&t.attr({y:-9999})}),delete t.distributeBox},this)}function o(t){let e=this.center,i=this.options,s=i.center,r=i.minSize||80,o=r,a=null!==i.size;return!a&&(null!==s[0]?o=Math.max(e[2]-Math.max(t[1],t[3]),r):(o=Math.max(e[2]-t[1]-t[3],r),e[0]+=(t[3]-t[1])/2),null!==s[1]?o=h6(o,r,e[2]-Math.max(t[0],t[2])):(o=h6(o,r,e[2]-t[0]-t[2]),e[1]+=(t[0]-t[2])/2),o<e[2]?(e[2]=o,e[3]=Math.min(i.thickness?Math.max(0,o-2*i.thickness):Math.max(0,h7(i.innerSize||0,o)),o),this.translate(e),this.drawDataLabels&&this.drawDataLabels()):a=!0),a}t.compose=function(t){if(hx.compose(h3),h8(h0,"PieDataLabel")){let a=t.prototype;a.dataLabelPositioners=e,a.alignDataLabel=h1,a.drawDataLabels=s,a.getDataLabelPosition=i,a.placeDataLabels=r,a.verifyDataLabelOverflow=o}}}(y||(y={}));let lt=y;!function(t){t.getCenterOfPoints=function(t){let e=t.reduce((t,e)=>(t.x+=e.x,t.y+=e.y,t),{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},t.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},t.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},t.pointInPolygon=function({x:t,y:e},i){let s=i.length,r,o,a=!1;for(r=0,o=s-1;r<s;o=r++){let[s,n]=i[r],[h,l]=i[o];n>e!=l>e&&t<(h-s)*(e-n)/(l-n)+s&&(a=!a)}return a}}(b||(b={}));let{pointInPolygon:le}=b,{addEvent:li,getAlignFactor:ls,fireEvent:lr,objectEach:lo,pick:la}=_;function ln(t){let e=t.length,i=(t,e)=>!(e.x>=t.x+t.width||e.x+e.width<=t.x||e.y>=t.y+t.height||e.y+e.height<=t.y),s=(t,e)=>{for(let i of t)if(le({x:i[0],y:i[1]},e))return!0;return!1},r,o,a,n,h,l=!1;for(let i=0;i<e;i++)(r=t[i])&&(r.oldOpacity=r.opacity,r.newOpacity=1,r.absoluteBox=function(t){if(t&&(!t.alignAttr||t.placed)){let e=t.box?0:t.padding||0,i=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},{height:s,polygon:r,width:o}=t.getBBox(),a=ls(t.alignValue)*o;return t.width=o,t.height=s,{x:i.x+(t.parentGroup?.translateX||0)+e-a,y:i.y+(t.parentGroup?.translateY||0)+e,width:o-2*e,height:s-2*e,polygon:r}}}(r));t.sort((t,e)=>(e.labelrank||0)-(t.labelrank||0));for(let r=0;r<e;++r){n=(o=t[r])&&o.absoluteBox;let l=n?.polygon;for(let d=r+1;d<e;++d){h=(a=t[d])&&a.absoluteBox;let e=!1;if(n&&h&&o!==a&&0!==o.newOpacity&&0!==a.newOpacity&&"hidden"!==o.visibility&&"hidden"!==a.visibility){let t=h.polygon;if(l&&t&&l!==t?s(l,t)&&(e=!0):i(n,h)&&(e=!0),e){let t=o.labelrank<a.labelrank?o:a,e=t.text;t.newOpacity=0,e?.element.querySelector("textPath")&&e.hide()}}}}for(let e of t)lh(e,this)&&(l=!0);l&&lr(this,"afterHideAllOverlappingLabels")}function lh(t,e){let i,s=!1;return t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.hasClass("highcharts-data-label")?(t[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),s=!0,t[t.isOld?"animate":"attr"]({opacity:i},void 0,function(){e.styledMode||t.css({pointerEvents:i?"auto":"none"})}),lr(e,"afterHideOverlappingLabel")):t.attr({opacity:i})),t.isOld=!0),s}function ll(){let t=this,e=[];for(let i of t.labelCollectors||[])e=e.concat(i());for(let i of t.yAxis||[])i.stacking&&i.options.stackLabels&&!i.options.stackLabels.allowOverlap&&lo(i.stacking.stacks,t=>{lo(t,t=>{t.label&&e.push(t.label)})});for(let i of t.series||[])if(i.visible&&i.hasDataLabels?.()){let s=i=>{for(let s of i)s.visible&&(s.dataLabels||[]).forEach(i=>{let r=i.options||{};i.labelrank=la(r.labelrank,s.labelrank,s.shapeArgs?.height),r.allowOverlap??Number(r.distance)>0?(i.oldOpacity=i.opacity,i.newOpacity=1,lh(i,t)):e.push(i)})};s(i.nodes||[]),s(i.points)}this.hideOverlappingLabels(e)}let ld={compose:function(t){let e=t.prototype;e.hideOverlappingLabels||(e.hideOverlappingLabels=ln,li(t,"render",ll))}},{defaultOptions:lc}=tM,{noop:lp}=S,{addEvent:lg,extend:lu,isObject:lf,merge:lm,relativeLength:lx}=_,ly={radius:0,scope:"stack",where:void 0},lb=lp,lv=lp;function lk(t,e,i,s,r={}){let o=lb(t,e,i,s,r),{innerR:a=0,r:n=i,start:h=0,end:l=0}=r;if(r.open||!r.borderRadius)return o;let d=l-h,c=Math.sin(d/2),p=Math.max(Math.min(lx(r.borderRadius||0,n-a),(n-a)/2,n*c/(1+c)),0),g=Math.min(p,d/Math.PI*2*a),u=o.length-1;for(;u--;)!function(t,e,i){let s,r,o,a=t[e],n=t[e+1];if("Z"===n[0]&&(n=t[0]),("M"===a[0]||"L"===a[0])&&"A"===n[0]?(s=a,r=n,o=!0):"A"===a[0]&&("M"===n[0]||"L"===n[0])&&(s=n,r=a),s&&r&&r.params){let a=r[1],n=r[5],h=r.params,{start:l,end:d,cx:c,cy:p}=h,g=n?a-i:a+i,u=g?Math.asin(i/g):0,f=n?u:-u,m=Math.cos(u)*g;o?(h.start=l+f,s[1]=c+m*Math.cos(l),s[2]=p+m*Math.sin(l),t.splice(e+1,0,["A",i,i,0,0,1,c+a*Math.cos(h.start),p+a*Math.sin(h.start)])):(h.end=d-f,r[6]=c+a*Math.cos(h.end),r[7]=p+a*Math.sin(h.end),t.splice(e+1,0,["A",i,i,0,0,1,c+m*Math.cos(d),p+m*Math.sin(d)])),r[4]=Math.abs(h.end-h.start)<Math.PI?0:1}}(o,u,u>1?g:p);return o}function lM(){if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d())){let{options:t,yAxis:e}=this,i="percent"===t.stacking,s=lc.plotOptions?.[this.type]?.borderRadius,r=lw(t.borderRadius,lf(s)?s:{}),o=e.options.reversed;for(let s of this.points){let{shapeArgs:a}=s;if("roundedRect"===s.shapeType&&a){let{width:n=0,height:h=0,y:l=0}=a,d=l,c=h;if("stack"===r.scope&&s.stackTotal){let r=e.translate(i?100:s.stackTotal,!1,!0,!1,!0),o=e.translate(t.threshold||0,!1,!0,!1,!0),a=this.crispCol(0,Math.min(r,o),0,Math.abs(r-o));d=a.y,c=a.height}let p=(s.negative?-1:1)*(o?-1:1)==-1,g=r.where;!g&&this.is("waterfall")&&Math.abs((s.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(g="all"),g||(g="end");let u=Math.min(lx(r.radius,n),n/2,"all"===g?h/2:1/0)||0;"end"===g&&(p&&(d-=u),c+=u),lu(a,{brBoxHeight:c,brBoxY:d,r:u})}}}}function lw(t,e){return lf(t)||(t={radius:t||0}),lm(ly,e,t)}function lS(){let t=lw(this.options.borderRadius);for(let e of this.points){let i=e.shapeArgs;i&&(i.borderRadius=lx(t.radius,(i.r||0)-(i.innerR||0)))}}function lA(t,e,i,s,r={}){let o=lv(t,e,i,s,r),{r:a=0,brBoxHeight:n=s,brBoxY:h=e}=r,l=e-h,d=h+n-(e+s),c=l-a>-.1?0:a,p=d-a>-.1?0:a,g=Math.max(c&&l,0),u=Math.max(p&&d,0),f=[t+c,e],m=[t+i-c,e],x=[t+i,e+c],y=[t+i,e+s-p],b=[t+i-p,e+s],v=[t+p,e+s],k=[t,e+s-p],M=[t,e+c],w=(t,e)=>Math.sqrt(Math.pow(t,2)-Math.pow(e,2));if(g){let t=w(c,c-g);f[0]-=t,m[0]+=t,x[1]=M[1]=e+c-g}if(s<c-g){let r=w(c,c-g-s);x[0]=y[0]=t+i-c+r,b[0]=Math.min(x[0],b[0]),v[0]=Math.max(y[0],v[0]),k[0]=M[0]=t+c-r,x[1]=M[1]=e+s}if(u){let t=w(p,p-u);b[0]+=t,v[0]-=t,y[1]=k[1]=e+s-p+u}if(s<p-u){let r=w(p,p-u-s);x[0]=y[0]=t+i-p+r,m[0]=Math.min(x[0],m[0]),f[0]=Math.max(y[0],f[0]),k[0]=M[0]=t+p-r,y[1]=k[1]=e}return o.length=0,o.push(["M",...f],["L",...m],["A",c,c,0,0,1,...x],["L",...y],["A",p,p,0,0,1,...b],["L",...v],["A",p,p,0,0,1,...k],["L",...M],["A",c,c,0,0,1,...f],["Z"]),o}let{diffObjects:lT,extend:lC,find:lO,merge:lP,pick:lE,uniqueKey:lL}=_;!function(t){function e(t,e){let i=t.condition;(i.callback||function(){return this.chartWidth<=lE(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=lE(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=lE(i.minWidth,0)&&this.chartHeight>=lE(i.minHeight,0)}).call(this)&&e.push(t._id)}function i(t,e){let i=this.options.responsive,s=this.currentResponsive,r=[],o;!e&&i&&i.rules&&i.rules.forEach(t=>{void 0===t._id&&(t._id=lL()),this.matchResponsiveRule(t,r)},this);let a=lP(...r.map(t=>lO(i?.rules||[],e=>e._id===t)).map(t=>t?.chartOptions));a.isResponsiveOptions=!0,r=r.toString()||void 0;let n=s?.ruleIds;r===n||(s&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(s.undoOptions,t,!0),this.updatingResponsive=!1),r?((o=lT(a,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:r,mergedOptions:a,undoOptions:o},this.updatingResponsive||this.update(a,t,!0)):this.currentResponsive=void 0)}t.compose=function(t){let s=t.prototype;return s.matchResponsiveRule||lC(s,{matchResponsiveRule:e,setResponsive:i}),t}}(v||(v={}));let lD=v;S.AST=t4,S.Axis=sZ,S.Chart=a2,S.Color=tL,S.DataLabel=hx,S.DataTableCore=ou,S.Fx=tN,S.HTMLElement=i4,S.Legend=am,S.LegendSymbol=oy,S.OverlappingDataLabels=S.OverlappingDataLabels||ld,S.PlotLineOrBand=rh,S.Point=rQ,S.Pointer=oh,S.RendererRegistry=ef,S.Series=o3,S.SeriesRegistry=ow,S.StackItem=np,S.SVGElement=e0,S.SVGRenderer=i$,S.Templating=eu,S.Tick=sx,S.Time=tm,S.Tooltip=rI,S.animate=t$.animate,S.animObject=t$.animObject,S.chart=a2.chart,S.color=tL.parse,S.dateFormat=eu.dateFormat,S.defaultOptions=tM.defaultOptions,S.distribute=ev.distribute,S.format=eu.format,S.getDeferredAnimation=t$.getDeferredAnimation,S.getOptions=tM.getOptions,S.numberFormat=eu.numberFormat,S.seriesType=ow.seriesType,S.setAnimation=t$.setAnimation,S.setOptions=tM.setOptions,S.stop=t$.stop,S.time=tM.defaultTime,S.timers=tN.timers,({compose:function(t,e,i){let s=t.types.pie;if(!e.symbolCustomAttribs.includes("borderRadius")){let r=i.prototype.symbols;lg(t,"afterColumnTranslate",lM,{order:9}),lg(s,"afterTranslate",lS),e.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),lb=r.arc,lv=r.roundedRect,r.arc=lk,r.roundedRect=lA}},optionsToObject:lw}).compose(S.Series,S.SVGElement,S.SVGRenderer),hM.compose(S.Series.types.column),hx.compose(S.Series),sQ.compose(S.Axis),i4.compose(S.SVGRenderer),am.compose(S.Chart),s3.compose(S.Axis),ld.compose(S.Chart),lt.compose(S.Series.types.pie),rh.compose(S.Chart,S.Axis),oh.compose(S.Chart),lD.compose(S.Chart),nr.compose(S.Axis,S.Chart,S.Series),nD.compose(S.Axis,S.Chart,S.Series),rI.compose(S.Pointer),_.extend(S,_);let{isTouchDevice:lI}=S,{addEvent:lB,merge:lz,pick:lN}=_,lR=[];function lW(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function lH(){let t,e,i,s=this.legend,r=this.navigator;if(r){t=s&&s.options,e=r.xAxis,i=r.yAxis;let{scrollbarHeight:o,scrollButtonSize:a}=r;this.inverted?(r.left=r.opposite?this.chartWidth-o-r.height:this.spacing[3]+o,r.top=this.plotTop+a):(r.left=lN(e.left,this.plotLeft+a),r.top=r.navigatorOptions.top||this.chartHeight-r.height-o-(this.scrollbar?.options.margin||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(t&&"bottom"===t.verticalAlign&&"proximate"!==t.layout&&t.enabled&&!t.floating?s.legendHeight+lN(t.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),e&&i&&(this.inverted?e.options.left=i.options.left=r.left:e.options.top=i.options.top=r.top,e.setAxisSize(),i.setAxisSize())}}function lX(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new i(this),lN(t.redraw,!0)&&this.redraw(t.animation))}function lF(){let t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new i(this))}function lG(){let t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!lI&&"x"===this.zooming.type||lI&&"x"===this.zooming.pinchType))return!1}function lY(t){let e=t.navigator;if(e&&t.xAxis[0]){let i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function lj(t){let e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(lz(!0,this.options.navigator,e),lz(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}let lU={compose:function(t,e){if(_.pushUnique(lR,t)){let s=t.prototype;i=e,s.callbacks.push(lY),lB(t,"afterAddSeries",lW),lB(t,"afterSetChartSize",lH),lB(t,"afterUpdate",lX),lB(t,"beforeRender",lF),lB(t,"beforeShowResetZoom",lG),lB(t,"update",lj)}}},{isTouchDevice:lV}=S,{addEvent:l$,correctFloat:lZ,defined:l_,isNumber:lq,pick:lK}=_;function lJ(){this.navigatorAxis||(this.navigatorAxis=new l0(this))}function lQ(t){let e,i=this.chart,s=i.options,r=s.navigator,o=this.navigatorAxis,a=i.zooming.pinchType,n=s.rangeSelector,h=i.zooming.type;if(this.isXAxis&&(r?.enabled||n?.enabled)){if("y"===h&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===h||lV&&"xy"===a)&&this.options.range){let e=o.previousZoom;l_(t.min)?o.previousZoom=[this.min,this.max]:e&&(t.min=e[0],t.max=e[1],o.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}class l0{static compose(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),l$(t,"init",lJ),l$(t,"setExtremes",lQ))}constructor(t){this.axis=t}destroy(){this.axis=void 0}toFixedRange(t,e,i,s){let r=this.axis,o=(r.pointRange||0)/2,a=lK(i,r.translate(t,!0,!r.horiz)),n=lK(s,r.translate(e,!0,!r.horiz));return l_(i)||(a=lZ(a+o)),l_(s)||(n=lZ(n-o)),lq(a)&&lq(n)||(a=n=void 0),{min:a,max:n}}}let{parse:l1}=tL,{seriesTypes:l2}=ow,l3={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:l1("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===l2.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},{relativeLength:l5}=_,l6={"navigator-handle":function(t,e,i,s,r={}){let o=r.width?r.width/2:i,a=l5(r.borderRadius||0,Math.min(2*o,s));return[["M",-1.5,(s=r.height||s)/2-3.5],["L",-1.5,s/2+4.5],["M",.5,s/2-3.5],["L",.5,s/2+4.5],...io.rect(-o-1,.5,2*o+1,s,{r:a})]}},{defined:l9}=_,{setOptions:l4}=tM,{composed:l8}=S,{getRendererType:l7}=ef,{setFixedRange:dt}={setFixedRange:function(t){let e=this.xAxis[0];l9(e.dataMax)&&l9(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}},{addEvent:de,extend:di,pushUnique:ds}=_;function dr(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}let da={compose:function(t,e,i){l0.compose(e),ds(l8,"Navigator")&&(t.prototype.setFixedRange=dt,di(l7().prototype.symbols,l6),de(i,"afterUpdate",dr),l4({navigator:l3}))}},{composed:dn}=S,{addEvent:dh,defined:dl,pick:dd,pushUnique:dc}=_;!function(t){let e;function i(t){let e=dd(t.options?.min,t.min),i=dd(t.options?.max,t.max);return{axisMin:e,axisMax:i,scrollMin:dl(t.dataMin)?Math.min(e,t.min,t.dataMin,dd(t.threshold,1/0)):e,scrollMax:dl(t.dataMax)?Math.max(i,t.max,t.dataMax,dd(t.threshold,-1/0)):i}}function s(){let t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function r(){let t=this;t.options?.scrollbar?.enabled&&(t.options.scrollbar.vertical=!t.horiz,t.options.startOnTick=t.options.endOnTick=!1,t.scrollbar=new e(t.chart.renderer,t.options.scrollbar,t.chart),dh(t.scrollbar,"changed",function(e){let s,r,{axisMin:o,axisMax:a,scrollMin:n,scrollMax:h}=i(t),l=h-n;if(dl(o)&&dl(a)){if(t.horiz&&!t.reversed||!t.horiz&&t.reversed?(s=n+l*this.to,r=n+l*this.from):(s=n+l*(1-this.from),r=n+l*(1-this.to)),this.shouldUpdateExtremes(e.DOMType)){let i="mousemove"!==e.DOMType&&"touchmove"!==e.DOMType&&void 0;t.setExtremes(r,s,!0,i,e)}else this.setRange(this.from,this.to)}}))}function o(){let t,e,s,{scrollMin:r,scrollMax:o}=i(this),a=this.scrollbar,n=this.axisTitleMargin+(this.titleOffset||0),h=this.chart.scrollbarsOffsets,l=this.options.margin||0;if(a&&h){if(this.horiz)this.opposite||(h[1]+=n),a.position(this.left,this.top+this.height+2+h[1]-(this.opposite?l:0),this.width,this.height),this.opposite||(h[1]+=l),t=1;else{let e;this.opposite&&(h[0]+=n),e=a.options.opposite?this.left+this.width+2+h[0]-(this.opposite?0:l):this.opposite?0:l,a.position(e,this.top,this.width,this.height),this.opposite&&(h[0]+=l),t=0}if(h[t]+=a.size+(a.options.margin||0),isNaN(r)||isNaN(o)||!dl(this.min)||!dl(this.max)||this.dataMin===this.dataMax)a.setRange(0,1);else if(this.min===this.max){let t=this.pointRange/(this.dataMax+1);e=t*this.min,s=t*(this.max+1),a.setRange(e,s)}else e=(this.min-r)/(o-r),s=(this.max-r)/(o-r),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(e,s):a.setRange(1-s,1-e)}}t.compose=function(t,i){dc(dn,"Axis.Scrollbar")&&(e=i,dh(t,"afterGetOffset",s),dh(t,"afterInit",r),dh(t,"afterRender",o))}}(k||(k={}));let dp=k,dg={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},{defaultOptions:du}=tM,{addEvent:df,correctFloat:dm,crisp:dx,defined:dy,destroyObjectProperties:db,fireEvent:dv,merge:dk,pick:dM,removeEvent:dw}=_;class dS{static compose(t){dp.compose(t,dS)}static swapXY(t,e){return e&&t.forEach(t=>{let e,i=t.length;for(let s=0;s<i;s+=2)"number"==typeof(e=t[s+1])&&(t[s+1]=t[s+2],t[s+2]=e)}),t}constructor(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}addEvents(){let t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,s=this.track.element,r=this.mouseDownHandler.bind(this),o=this.mouseMoveHandler.bind(this),a=this.mouseUpHandler.bind(this),n=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[s,"click",this.trackClick.bind(this)],[i,"mousedown",r],[i.ownerDocument,"mousemove",o],[i.ownerDocument,"mouseup",a],[i,"touchstart",r],[i.ownerDocument,"touchmove",o],[i.ownerDocument,"touchend",a]];n.forEach(function(t){df.apply(null,t)}),this._events=n}buttonToMaxClick(t){let e=(this.to-this.from)*dM(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),dv(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}buttonToMinClick(t){let e=dm(this.to-this.from)*dM(this.options.step,.2);this.updatePosition(dm(this.from-e),dm(this.to-e)),dv(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}cursorToScrollbarPosition(t){let e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}}destroy(){let t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,db(e.scrollbarButtons))}drawScrollbarButton(t){let e=this.renderer,i=this.scrollbarButtons,s=this.options,r=this.size,o=e.g().add(this.group);if(i.push(o),s.buttonsEnabled){let a=e.rect().addClass("highcharts-scrollbar-button").add(o);this.chart.styledMode||a.attr({stroke:s.buttonBorderColor,"stroke-width":s.buttonBorderWidth,fill:s.buttonBackgroundColor}),a.attr(a.crisp({x:-.5,y:-.5,width:r,height:r,r:s.buttonBorderRadius},a.strokeWidth()));let n=e.path(dS.swapXY([["M",r/2+(t?-1:1),r/2-3],["L",r/2+(t?-1:1),r/2+3],["L",r/2+(t?2:-2),r/2]],s.vertical)).addClass("highcharts-scrollbar-arrow").add(i[t]);this.chart.styledMode||n.attr({fill:s.buttonArrowColor})}}init(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=dk(dg,du.scrollbar,e),this.options.margin=dM(this.options.margin,10),this.chart=i,this.size=dM(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())}mouseDownHandler(t){let e=this.chart.pointer?.normalize(t)||t,i=this.cursorToScrollbarPosition(e);this.chartX=i.chartX,this.chartY=i.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0}mouseMoveHandler(t){let e,i=this.chart.pointer?.normalize(t)||t,s=this.options.vertical?"chartY":"chartX",r=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][s])&&(e=this.cursorToScrollbarPosition(i)[s]-this[s],this.hasDragged=!0,this.updatePosition(r[0]+e,r[1]+e),this.hasDragged&&dv(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))}mouseUpHandler(t){this.hasDragged&&dv(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null}position(t,e,i,s){let{buttonsEnabled:r,margin:o=0,vertical:a}=this.options,n=this.rendered?"animate":"attr",h=s,l=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=s,this.xOffset=h,this.yOffset=l,a?(this.width=this.yOffset=i=l=this.size,this.xOffset=h=0,this.yOffset=l=r?this.size:0,this.barWidth=s-(r?2*i:0),this.x=t+=o):(this.height=s=this.size,this.xOffset=h=r?this.size:0,this.barWidth=i-(r?2*s:0),this.y=this.y+o),this.group[n]({translateX:t,translateY:this.y}),this.track[n]({width:i,height:s}),this.scrollbarButtons[1][n]({translateX:a?0:i-h,translateY:a?s-l:0})}removeEvents(){this._events.forEach(function(t){dw.apply(null,t)}),this._events.length=0}render(){let t=this.renderer,e=this.options,i=this.size,s=this.chart.styledMode,r=t.g("scrollbar").attr({zIndex:e.zIndex}).hide().add();this.group=r,this.track=t.rect().addClass("highcharts-scrollbar-track").attr({r:e.trackBorderRadius||0,height:i,width:i}).add(r),s||this.track.attr({fill:e.trackBackgroundColor,stroke:e.trackBorderColor,"stroke-width":e.trackBorderWidth});let o=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-dx(0,o),y:-dx(0,o)}),this.scrollbarGroup=t.g().add(r),this.scrollbar=t.rect().addClass("highcharts-scrollbar-thumb").attr({height:i-o,width:i-o,r:e.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=t.path(dS.swapXY([["M",-3,i/4],["L",-3,2*i/3],["M",0,i/4],["L",0,2*i/3],["M",3,i/4],["L",3,2*i/3]],e.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),s||(this.scrollbar.attr({fill:e.barBackgroundColor,stroke:e.barBorderColor,"stroke-width":e.barBorderWidth}),this.scrollbarRifles.attr({stroke:e.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-dx(0,this.scrollbarStrokeWidth),-dx(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)}setRange(t,e){let i,s,r=this.options,o=r.vertical,a=r.minWidth,n=this.barWidth,h=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(!dy(n))return;let l=n*Math.min(e,1);i=Math.ceil(n*(t=Math.max(t,0))),this.calculatedWidth=s=dm(l-i),s<a&&(i=(n-a+s)*t,s=a);let d=Math.floor(i+this.xOffset+this.yOffset),c=s/2-.5;this.from=t,this.to=e,o?(this.scrollbarGroup[h]({translateY:d}),this.scrollbar[h]({height:s}),this.scrollbarRifles[h]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:d}),this.scrollbar[h]({width:s}),this.scrollbarRifles[h]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),s<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===r.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}shouldUpdateExtremes(t){return dM(this.options.liveRedraw,S.svg&&!S.isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!dy(t)}trackClick(t){let e=this.chart.pointer?.normalize(t)||t,i=this.to-this.from,s=this.y+this.scrollbarTop,r=this.x+this.scrollbarLeft;this.options.vertical&&e.chartY>s||!this.options.vertical&&e.chartX>r?this.updatePosition(this.from+i,this.to+i):this.updatePosition(this.from-i,this.to-i),dv(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})}update(t){this.destroy(),this.init(this.chart.renderer,dk(!0,this.options,t),this.chart)}updatePosition(t,e){e>1&&(t=dm(1-dm(e-t)),e=1),t<0&&(e=dm(e-t),t=0),this.from=t,this.to=e}}dS.defaultOptions=dg,du.scrollbar=dk(!0,dS.defaultOptions,du.scrollbar);let{defaultOptions:dA}=tM,{isTouchDevice:dT}=S,{prototype:{symbols:dC}}=i$,{addEvent:dO,clamp:dP,correctFloat:dE,defined:dL,destroyObjectProperties:dD,erase:dI,extend:dB,find:dz,fireEvent:dN,isArray:dR,isNumber:dW,merge:dH,pick:dX,removeEvent:dF,splat:dG}=_;function dY(t,...e){let i=[].filter.call(e,dW);if(i.length)return Math[t].apply(0,i)}class dj{static compose(t,e,i){lU.compose(t,dj),da.compose(t,e,i)}constructor(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}drawHandle(t,e,i,s){let r=this.navigatorOptions.handles.height;this.handles[e][s](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-r)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-r/2-1)})}drawOutline(t,e,i,s){let r=this.navigatorOptions.maskInside,o=this.outline.strokeWidth(),a=o/2,n=o%2/2,h=this.scrollButtonSize,l=this.size,d=this.top,c=this.height,p=d-a,g=d+c,u=this.left,f,m;i?(f=d+e+n,e=d+t+n,m=[["M",u+c,d-h-n],["L",u+c,f],["L",u,f],["M",u,e],["L",u+c,e],["L",u+c,d+l+h]],r&&m.push(["M",u+c,f-a],["L",u+c,e+a])):(u-=h,t+=u+h-n,e+=u+h-n,m=[["M",u,p],["L",t,p],["L",t,g],["M",e,g],["L",e,p],["L",u+l+2*h,p]],r&&m.push(["M",t-a,p],["L",e+a,p])),this.outline[s]({d:m})}drawMasks(t,e,i,s){let r,o,a,n,h=this.left,l=this.top,d=this.height;i?(a=[h,h,h],n=[l,l+t,l+e],o=[d,d,d],r=[t,e-t,this.size-e]):(a=[h,h+t,h+e],n=[l,l,l],o=[t,e-t,this.size-e],r=[d,d,d]),this.shades.forEach((t,e)=>{t[s]({x:a[e],y:n[e],width:o[e],height:r[e]})})}renderElements(){let t=this,e=t.navigatorOptions,i=e.maskInside,s=t.chart,r=s.inverted,o=s.renderer,a={cursor:r?"ns-resize":"ew-resize"},n=t.navigatorGroup??(t.navigatorGroup=o.g("navigator").attr({zIndex:8,visibility:"hidden"}).add());if([!i,i,!i].forEach((i,r)=>{let h=t.shades[r]??(t.shades[r]=o.rect().addClass("highcharts-navigator-mask"+(1===r?"-inside":"-outside")).add(n));s.styledMode||(h.attr({fill:i?e.maskFill:"rgba(0,0,0,0)"}),1===r&&h.css(a))}),t.outline||(t.outline=o.path().addClass("highcharts-navigator-outline").add(n)),s.styledMode||t.outline.attr({"stroke-width":e.outlineWidth,stroke:e.outlineColor}),e.handles?.enabled){let i=e.handles,{height:r,width:h}=i;[0,1].forEach(e=>{let l=i.symbols[e];if(t.handles[e]&&t.handles[e].symbolUrl===l){if(!t.handles[e].isImg&&t.handles[e].symbolName!==l){let i=dC[l].call(dC,-h/2-1,0,h,r);t.handles[e].attr({d:i}),t.handles[e].symbolName=l}}else t.handles[e]?.destroy(),t.handles[e]=o.symbol(l,-h/2-1,0,h,r,i),t.handles[e].attr({zIndex:7-e}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][e]).add(n),t.addMouseEvents();s.inverted&&t.handles[e].attr({rotation:90,rotationOriginX:Math.floor(-h/2),rotationOriginY:(r+h)/2}),s.styledMode||t.handles[e].attr({fill:i.backgroundColor,stroke:i.borderColor,"stroke-width":i.lineWidth,width:i.width,height:i.height,x:-h/2-1,y:0}).css(a)})}}update(t,e=!1){let i=this.chart,s=i.options.chart.inverted!==i.scrollbar?.options.vertical;if(dH(!0,i.options.navigator,t),this.navigatorOptions=i.options.navigator||{},this.setOpposite(),dL(t.enabled)||s)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(i);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{dF(t,"updatedData",this.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(t=>{t.eventsToUnbind.push(dO(t,"updatedData",this.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=t.height??this.height;let e=this.getXAxisOffsets();this.xAxis.update({...t.xAxis,offsets:e,[i.inverted?"width":"height"]:this.height,[i.inverted?"height":"width"]:void 0},!1),this.yAxis.update({...t.yAxis,[i.inverted?"width":"height"]:this.height},!1)}e&&i.redraw()}render(t,e,i,s){let r=this.chart,o=this.xAxis,a=o.pointRange||0,n=o.navigatorAxis.fake?r.xAxis[0]:o,h=this.navigatorEnabled,l=this.rendered,d=r.inverted,c=r.xAxis[0].minRange,p=r.xAxis[0].options.maxRange,g=this.scrollButtonSize,u,f,m,x=this.scrollbarHeight,y,b;if(this.hasDragged&&!dL(i))return;if(this.isDirty&&this.renderElements(),t=dE(t-a/2),e=dE(e+a/2),!dW(t)||!dW(e)){if(!l)return;i=0,s=dX(o.width,n.width)}this.left=dX(o.left,r.plotLeft+g+(d?r.plotWidth:0));let v=this.size=y=dX(o.len,(d?r.plotHeight:r.plotWidth)-2*g);u=d?x:y+2*g,i=dX(i,o.toPixels(t,!0)),s=dX(s,o.toPixels(e,!0)),dW(i)&&Math.abs(i)!==1/0||(i=0,s=u);let k=o.toValue(i,!0),M=o.toValue(s,!0),w=Math.abs(dE(M-k));w<c?this.grabbedLeft?i=o.toPixels(M-c-a,!0):this.grabbedRight&&(s=o.toPixels(k+c+a,!0)):dL(p)&&dE(w-a)>p&&(this.grabbedLeft?i=o.toPixels(M-p-a,!0):this.grabbedRight&&(s=o.toPixels(k+p+a,!0))),this.zoomedMax=dP(Math.max(i,s),0,v),this.zoomedMin=dP(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,s),0,v),this.range=this.zoomedMax-this.zoomedMin,v=Math.round(this.zoomedMax);let S=Math.round(this.zoomedMin);h&&(this.navigatorGroup.attr({visibility:"inherit"}),b=l&&!this.hasDragged?"animate":"attr",this.drawMasks(S,v,d,b),this.drawOutline(S,v,d,b),this.navigatorOptions.handles.enabled&&(this.drawHandle(S,0,d,b),this.drawHandle(v,1,d,b))),this.scrollbar&&(d?(m=this.top-g,f=this.left-x+(h||!n.opposite?0:(n.titleOffset||0)+n.axisTitleMargin),x=y+2*g):(m=this.top+(h?this.height:-x),f=this.left-g),this.scrollbar.position(f,m,u,x),this.scrollbar.setRange(this.zoomedMin/(y||1),this.zoomedMax/(y||1))),this.rendered=!0,this.isDirty=!1,dN(this,"afterRender")}addMouseEvents(){let t=this,e=t.chart,i=e.container,s=[],r,o;t.mouseMoveHandler=r=function(e){t.onMouseMove(e)},t.mouseUpHandler=o=function(e){t.onMouseUp(e)},(s=t.getPartsEvents("mousedown")).push(dO(e.renderTo,"mousemove",r),dO(i.ownerDocument,"mouseup",o),dO(e.renderTo,"touchmove",r),dO(i.ownerDocument,"touchend",o)),s.concat(t.getPartsEvents("touchstart")),t.eventsToUnbind=s,t.series&&t.series[0]&&s.push(dO(t.series[0].xAxis,"foundExtremes",function(){e.navigator.modifyNavigatorAxisExtremes()}))}getPartsEvents(t){let e=this,i=[];return["shades","handles"].forEach(function(s){e[s].forEach(function(r,o){i.push(dO(r.element,t,function(t){e[s+"Mousedown"](t,o)}))})}),i}shadesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=this.xAxis,r=this.zoomedMin,o=this.size,a=this.range,n=this.left,h=t.chartX,l,d,c,p;i.inverted&&(h=t.chartY,n=this.top),1===e?(this.grabbedCenter=h,this.fixedWidth=a,this.dragOffset=h-r):(p=h-n-a/2,0===e?p=Math.max(0,p):2===e&&p+a>=o&&(p=o-a,this.reversedExtremes?(p-=a,d=this.getUnionExtremes().dataMin):l=this.getUnionExtremes().dataMax),p!==r&&(this.fixedWidth=a,dL((c=s.navigatorAxis.toFixedRange(p,p+a,d,l)).min)&&dN(this,"setRange",{min:Math.min(c.min,c.max),max:Math.max(c.min,c.max),redraw:!0,eventArguments:{trigger:"navigator"}})))}handlesMousedown(t,e){t=this.chart.pointer?.normalize(t)||t;let i=this.chart,s=i.xAxis[0],r=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=r?s.min:s.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=r?s.max:s.min),i.setFixedRange(void 0)}onMouseMove(t){let e=this,i=e.chart,s=e.navigatorSize,r=e.range,o=e.dragOffset,a=i.inverted,n=e.left,h;(!t.touches||0!==t.touches[0].pageX)&&(h=(t=i.pointer?.normalize(t)||t).chartX,a&&(n=e.top,h=t.chartY),e.grabbedLeft?(e.hasDragged=!0,e.render(0,0,h-n,e.otherHandlePos)):e.grabbedRight?(e.hasDragged=!0,e.render(0,0,e.otherHandlePos,h-n)):e.grabbedCenter&&(e.hasDragged=!0,h<o?h=o:h>s+o-r&&(h=s+o-r),e.render(0,0,h-o,h-o+r)),e.hasDragged&&e.scrollbar&&dX(e.scrollbar.options.liveRedraw,!dT&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){e.onMouseUp(t)},0)))}onMouseUp(t){let e,i,s,r,o,a,n=this.chart,h=this.xAxis,l=this.scrollbar,d=t.DOMEvent||t,c=n.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!l||!l.hasDragged)||"scrollbar"===t.trigger)&&(s=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?r=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(o=this.fixedExtreme),this.zoomedMax===this.size&&(o=this.reversedExtremes?s.dataMin:s.dataMax),0===this.zoomedMin&&(r=this.reversedExtremes?s.dataMax:s.dataMin),dL((a=h.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,r,o)).min)&&dN(this,"setRange",{min:Math.min(a.min,a.max),max:Math.max(a.min,a.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&dW(this.zoomedMin)&&dW(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,c,p),this.outline&&this.drawOutline(i,e,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,c,p),this.drawHandle(e,1,c,p)))}removeEvents(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()}removeBaseSeriesEvents(){let t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){dF(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&dF(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))}getXAxisOffsets(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]}init(t){let e=t.options,i=e.navigator||{},s=i.enabled,r=e.scrollbar||{},o=r.enabled,a=s&&i.height||0,n=o&&r.height||0,h=r.buttonsEnabled&&n||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=a,this.scrollbarHeight=n,this.scrollButtonSize=h,this.scrollbarEnabled=o,this.navigatorEnabled=s,this.navigatorOptions=i,this.scrollbarOptions=r,this.setOpposite();let l=this,d=l.baseSeries,c=t.xAxis.length,p=t.yAxis.length,g=d&&d[0]&&d[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,l.navigatorEnabled){let e=this.getXAxisOffsets();l.xAxis=new sZ(t,dH({breaks:g.options.breaks,ordinal:g.options.ordinal,overscroll:g.options.overscroll},i.xAxis,{type:"datetime",yAxis:i.yAxis?.id,index:c,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:g.options.ordinal?0:g.options.minPadding,maxPadding:g.options.ordinal?0:g.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:e,width:a}:{offsets:e,height:a}),"xAxis"),l.yAxis=new sZ(t,dH(i.yAxis,{alignTicks:!1,offset:0,index:p,isInternal:!0,reversed:dX(i.yAxis&&i.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:a}:{height:a}),"yAxis"),d||i.series.data?l.updateNavigatorSeries(!1):0===t.series.length&&(l.unbindRedraw=dO(t,"beforeRedraw",function(){t.series.length>0&&!l.series&&(l.setBaseSeries(),l.unbindRedraw())})),l.reversedExtremes=t.inverted&&!l.xAxis.reversed||!t.inverted&&l.xAxis.reversed,l.renderElements(),l.addMouseEvents()}else l.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){let s=t.xAxis[0],r=s.getExtremes(),o=s.len-2*h,a=dY("min",s.options.min,r.dataMin),n=dY("max",s.options.max,r.dataMax)-a;return i?e*n/o+a:o*(e-a)/n},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},l.xAxis.navigatorAxis.axis=l.xAxis,l.xAxis.navigatorAxis.toFixedRange=l0.prototype.toFixedRange.bind(l.xAxis.navigatorAxis);if(t.options.scrollbar?.enabled){let e=dH(t.options.scrollbar,{vertical:t.inverted});dW(e.margin)||(e.margin=t.inverted?-3:3),t.scrollbar=l.scrollbar=new dS(t.renderer,e,t),dO(l.scrollbar,"changed",function(t){let e=l.size,i=e*this.to,s=e*this.from;l.hasDragged=l.scrollbar.hasDragged,l.render(0,0,s,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){l.onMouseUp(t)})})}l.addBaseSeriesEvents(),l.addChartEvents()}setOpposite(){let t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=dX(t.opposite,!!(!e&&i.inverted))}getUnionExtremes(t){let e,i=this.chart.xAxis[0],s=this.chart.time,r=this.xAxis,o=r.options,a=i.options;return t&&null===i.dataMin||(e={dataMin:dX(s.parse(o?.min),dY("min",s.parse(a.min),i.dataMin,r.dataMin,r.min)),dataMax:dX(s.parse(o?.max),dY("max",s.parse(a.max),i.dataMax,r.dataMax,r.max))}),e}setBaseSeries(t,e){let i=this.chart,s=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?dz(i.series,t=>!t.options.isInternal).index:0),(i.series||[]).forEach((e,i)=>{!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&s.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)}updateNavigatorSeries(t,e){let i=this,s=i.chart,r=i.baseSeries,o={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:this.navigatorOptions.xAxis?.id,yAxis:this.navigatorOptions.yAxis?.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},a=i.series=(i.series||[]).filter(t=>{let e=t.baseSeries;return!(0>r.indexOf(e))||(e&&(dF(e,"updatedData",i.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),n,h,l=i.navigatorOptions.series,d;r&&r.length&&r.forEach(t=>{let c=t.navigatorSeries,p=dB({color:t.color,visible:t.visible},dR(l)?dA.navigator.series:l);if(c&&!1===i.navigatorOptions.adaptToUpdatedData)return;o.name="Navigator "+r.length,d=(n=t.options||{}).navigatorOptions||{},p.dataLabels=dG(p.dataLabels),(h=dH(n,o,p,d)).pointRange=dX(p.pointRange,d.pointRange,dA.plotOptions[h.type||"line"].pointRange);let g=d.data||p.data;i.hasNavigatorData=i.hasNavigatorData||!!g,h.data=g||n.data?.slice(0),c&&c.options?c.update(h,e):(t.navigatorSeries=s.initSeries(h),s.setSortedData(),t.navigatorSeries.baseSeries=t,a.push(t.navigatorSeries))}),(l.data&&!(r&&r.length)||dR(l))&&(i.hasNavigatorData=!1,(l=dG(l)).forEach((t,e)=>{o.name="Navigator "+(a.length+1),(h=dH(dA.navigator.series,{color:s.series[e]&&!s.series[e].options.isInternal&&s.series[e].color||s.options.colors[e]||s.options.colors[0]},o,t)).data=t.data,h.data&&(i.hasNavigatorData=!0,a.push(s.initSeries(h)))})),t&&this.addBaseSeriesEvents()}addBaseSeriesEvents(){let t=this,e=t.baseSeries||[];e[0]&&e[0].xAxis&&e[0].eventsToUnbind.push(dO(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),e.forEach(i=>{i.eventsToUnbind.push(dO(i,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),i.eventsToUnbind.push(dO(i,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==this.navigatorOptions.adaptToUpdatedData&&i.xAxis&&i.eventsToUnbind.push(dO(i,"updatedData",this.updatedDataHandler)),i.eventsToUnbind.push(dO(i,"remove",function(){e&&dI(e,i),this.navigatorSeries&&t.series&&(dI(t.series,this.navigatorSeries),dL(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})}getBaseSeriesMin(t){return this.baseSeries.reduce(function(t,e){return Math.min(t,e.getColumn("x")[0]??t)},t)}modifyNavigatorAxisExtremes(){let t=this.xAxis;if(void 0!==t.getExtremes){let e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}}modifyBaseAxisExtremes(){let t,e,i=this.chart.navigator,s=this.getExtremes(),r=s.min,o=s.max,a=s.dataMin,n=s.dataMax,h=o-r,l=i.stickToMin,d=i.stickToMax,c=dX(this.ordinal?.convertOverscroll(this.options.overscroll),0),p=i.series&&i.series[0],g=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(l&&(t=(e=a)+h),d&&(t=n+c,l||(e=Math.max(a,t-h,i.getBaseSeriesMin(p&&p.xData?p.xData[0]:-Number.MAX_VALUE)))),g&&(l||d)&&dW(e)&&(this.min=this.userMin=e,this.max=this.userMax=t)),i.stickToMin=i.stickToMax=null}updatedDataHandler(){let t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=dX(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))}shouldStickToMin(t,e){let i=e.getBaseSeriesMin(t.getColumn("x")[0]),s=t.xAxis,r=s.max,o=s.min,a=s.options.range,n=!0;return!!(dW(r)&&dW(o))&&(a&&r-i>0?r-i<a:o<=i)}addChartEvents(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(dO(this.chart,"redraw",function(){let t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),dO(this.chart,"getMargins",function(){let t=this.navigator,e=t.opposite?"plotTop":"marginBottom";this.inverted&&(e=t.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(t.navigatorEnabled||!this.inverted?t.height+(this.scrollbar?.options.margin||0)+t.scrollbarHeight:0)+(t.navigatorOptions.margin||0)}),dO(dj,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))}destroy(){this.removeEvents(),this.xAxis&&(dI(this.chart.xAxis,this.xAxis),dI(this.chart.axes,this.xAxis)),this.yAxis&&(dI(this.chart.yAxis,this.yAxis),dI(this.chart.axes,this.yAxis)),(this.series||[]).forEach(t=>{t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(t=>{this[t]&&this[t].destroy&&this[t].destroy(),this[t]=null}),[this.handles].forEach(t=>{dD(t)}),this.baseSeries.forEach(t=>{t.navigatorSeries=void 0}),this.navigatorEnabled=!1}}let dU={chart:{height:70,margin:[0,5,0,5]},exporting:{enabled:!1},legend:{enabled:!1},navigator:{enabled:!1},plotOptions:{series:{states:{hover:{enabled:!1}},marker:{enabled:!1}}},scrollbar:{enabled:!1},title:{text:""},tooltip:{enabled:!1},xAxis:{visible:!1},yAxis:{height:0,visible:!1}},{merge:dV,addEvent:d$,fireEvent:dZ,pick:d_}=_;class dq{static navigator(t,e){let i=new dq(t,e);return S.navigators?S.navigators.push(i):S.navigators=[i],i}constructor(t,e){this.boundAxes=[],this.userOptions=e,this.chartOptions=dV(S.getOptions(),dU,e.chart,{navigator:e}),this.chartOptions.chart&&e.height&&(this.chartOptions.chart.height=e.height);let i=new a2(t,this.chartOptions);i.options=dV(i.options,{navigator:{enabled:!0},scrollbar:{enabled:!0}}),this.chartOptions.navigator&&this.chartOptions.scrollbar&&(this.chartOptions.navigator.enabled=!0,this.chartOptions.scrollbar.enabled=!0),this.navigator=new dj(i),i.navigator=this.navigator,this.initNavigator()}bind(t,e=!0){let i=this,s=t instanceof a2?t.xAxis[0]:t;if(!(s instanceof sZ))return;let{min:r,max:o}=this.navigator.xAxis,a=[];if(e){let t=d$(s,"setExtremes",t=>{("pan"===t.trigger||"zoom"===t.trigger||"mousewheel"===t.trigger)&&i.setRange(t.min,t.max,!0,"pan"!==t.trigger&&"mousewheel"!==t.trigger,{trigger:s})});a.push(t)}let n=d$(this.navigator,"setRange",t=>{s.setExtremes(t.min,t.max,t.redraw,t.animation)});a.push(n);let h=this.boundAxes.filter(function(t){return t.axis===s})[0];h||(h={axis:s,callbacks:[]},this.boundAxes.push(h)),h.callbacks=a,s.series.forEach(t=>{t.options.showInNavigator&&i.addSeries(t.options)}),s.setExtremes(r,o),d$(s,"destroy",t=>{t.keepEvents||this.unbind(s)})}unbind(t){if(!t){this.boundAxes.forEach(({callbacks:t})=>{t.forEach(t=>t())}),this.boundAxes.length=0;return}let e=t instanceof sZ?t:t.xAxis[0];for(let t=this.boundAxes.length-1;t>=0;t--)this.boundAxes[t].axis===e&&(this.boundAxes[t].callbacks.forEach(t=>t()),this.boundAxes.splice(t,1))}destroy(){this.boundAxes.forEach(({callbacks:t})=>{t.forEach(t=>t())}),this.boundAxes.length=0,this.navigator.destroy(),this.navigator.chart.destroy()}update(t,e){this.chartOptions=dV(this.chartOptions,t.height&&{chart:{height:t.height}},t.chart,{navigator:t}),this.navigator.chart.update(this.chartOptions,e)}redraw(){this.navigator.chart.redraw()}addSeries(t){this.navigator.chart.addSeries(dV(t,{showInNavigator:d_(t.showInNavigator,!0)})),this.navigator.setBaseSeries()}initNavigator(){let t=this.navigator;t.top=1,t.xAxis.setScale(),t.yAxis.setScale(),t.xAxis.render(),t.yAxis.render(),t.series?.forEach(t=>{t.translate(),t.render(),t.redraw()});let{min:e,max:i}=this.getInitialExtremes();t.chart.xAxis[0].userMin=e,t.chart.xAxis[0].userMax=i,t.render(e,i)}getRange(){let{min:t,max:e}=this.navigator.chart.xAxis[0].getExtremes(),{userMin:i,userMax:s,min:r,max:o}=this.navigator.xAxis.getExtremes();return{min:d_(t,r),max:d_(e,o),dataMin:r,dataMax:o,userMin:i,userMax:s}}setRange(t,e,i,s,r){dZ(this.navigator,"setRange",{min:t,max:e,redraw:i,animation:s,eventArguments:dV(r,{trigger:"navigator"})})}getInitialExtremes(){let{min:t,max:e}=this.navigator.xAxis.getExtremes();return{min:t,max:e}}}S.StandaloneNavigator=S.StandaloneNavigator||dq,S.navigator=S.StandaloneNavigator.navigator,da.compose(S.Chart,S.Axis,S.Series);let dK=S;return w.default})());