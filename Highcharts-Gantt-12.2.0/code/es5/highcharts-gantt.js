!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("highcharts/highcharts",[],e):"object"==typeof exports?exports.highcharts=e():(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e())}(this,function(){return function(){"use strict";var t,e,i,o,r,n,s,a,h,l,d,c,p,u,f,g,v,m,y,x,b,M,k,w,S,A,T,P,O,C,E,L,B,I,D,z,R,N,W,G,X,F,H,j,Y,_,U,V,q,Z,K,$,J,Q,tt,te,ti,to,tr,tn,ts,ta,th,tl,td,tc={};tc.d=function(t,e){for(var i in e)tc.o(e,i)&&!tc.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},tc.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var tp={};tc.d(tp,{default:function(){return f6}}),(t=N||(N={})).SVG_NS="http://www.w3.org/2000/svg",t.product="Highcharts",t.version="12.2.0",t.win="undefined"!=typeof window?window:{},t.doc=t.win.document,t.svg=!!(null===(i=null===(e=null===t.doc||void 0===t.doc?void 0:t.doc.createElementNS)||void 0===e?void 0:e.call(t.doc,t.SVG_NS,"svg"))||void 0===i?void 0:i.createSVGRect),t.pageLang=null===(r=null===(o=null===t.doc||void 0===t.doc?void 0:t.doc.documentElement)||void 0===o?void 0:o.closest("[lang]"))||void 0===r?void 0:r.lang,t.userAgent=(null===(n=t.win.navigator)||void 0===n?void 0:n.userAgent)||"",t.isChrome=t.win.chrome,t.isFirefox=-1!==t.userAgent.indexOf("Firefox"),t.isMS=/(edge|msie|trident)/i.test(t.userAgent)&&!t.win.opera,t.isSafari=!t.isChrome&&-1!==t.userAgent.indexOf("Safari"),t.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(t.userAgent),t.isWebKit=-1!==t.userAgent.indexOf("AppleWebKit"),t.deg2rad=2*Math.PI/360,t.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],t.noop=function(){},t.supportsPassiveEvents=function(){var e=!1;if(!t.isMS){var i=Object.defineProperty({},"passive",{get:function(){e=!0}});t.win.addEventListener&&t.win.removeEventListener&&(t.win.addEventListener("testPassive",t.noop,i),t.win.removeEventListener("testPassive",t.noop,i))}return e}(),t.charts=[],t.composed=[],t.dateFormats={},t.seriesTypes={},t.symbolSizes={},t.chartCount=0;var tu=N,tf=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},tg=tu.charts,tv=tu.doc,tm=tu.win;function ty(t,e,i,o){var r=e?"Highcharts error":"Highcharts warning";32===t&&(t=""+r+": Deprecated member");var n=tA(t),s=n?""+r+" #"+t+": www.highcharts.com/errors/"+t+"/":t.toString();if(void 0!==o){var a="";n&&(s+="?"),tz(o,function(t,e){a+="\n - ".concat(e,": ").concat(t),n&&(s+=encodeURI(e)+"="+encodeURI(t))}),s+=a}tN(tu,"displayError",{chart:i,code:t,message:s,params:o},function(){if(e)throw Error(s);tm.console&&-1===ty.messages.indexOf(s)&&console.warn(s)}),ty.messages.push(s)}function tx(t,e){return parseInt(t,e||10)}function tb(t){return"string"==typeof t}function tM(t){var e=Object.prototype.toString.call(t);return"[object Array]"===e||"[object Array Iterator]"===e}function tk(t,e){return!!t&&"object"==typeof t&&(!e||!tM(t))}function tw(t){return tk(t)&&"number"==typeof t.nodeType}function tS(t){var e=null==t?void 0:t.constructor;return!!(tk(t,!0)&&!tw(t)&&(null==e?void 0:e.name)&&"Object"!==e.name)}function tA(t){return"number"==typeof t&&!isNaN(t)&&t<1/0&&t>-1/0}function tT(t){return null!=t}function tP(t,e,i){var o,r=tb(e)&&!tT(i),n=function(e,i){tT(e)?t.setAttribute(i,e):r?(o=t.getAttribute(i))||"class"!==i||(o=t.getAttribute(i+"Name")):t.removeAttribute(i)};return tb(e)?n(i,e):tz(e,n),o}function tO(t){return tM(t)?t:[t]}function tC(t,e){var i;for(i in t||(t={}),e)t[i]=e[i];return t}function tE(){for(var t=arguments,e=t.length,i=0;i<e;i++){var o=t[i];if(null!=o)return o}}function tL(t,e){tC(t.style,e)}function tB(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))}function tI(t,e){return t>1e14?t:parseFloat(t.toPrecision(e||14))}(ty||(ty={})).messages=[],Math.easeInOutSine=function(t){return -.5*(Math.cos(Math.PI*t)-1)};var tD=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){var i,o=t.length;for(i=0;i<o;i++)if(e(t[i],i))return t[i]};function tz(t,e,i){for(var o in t)Object.hasOwnProperty.call(t,o)&&e.call(i||t[o],t[o],o,t)}function tR(t,e,i){function o(e,i){var o=t.removeEventListener;o&&o.call(t,e,i,!1)}function r(i){var r,n;t.nodeName&&(e?(r={})[e]=!0:r=i,tz(r,function(t,e){if(i[e])for(n=i[e].length;n--;)o(e,i[e][n].fn)}))}var n="function"==typeof t&&t.prototype||t;if(Object.hasOwnProperty.call(n,"hcEvents")){var s=n.hcEvents;if(e){var a=s[e]||[];i?(s[e]=a.filter(function(t){return i!==t.fn}),o(e,i)):(r(s),s[e]=[])}else r(s),delete n.hcEvents}}function tN(t,e,i,o){if(i=i||{},(null==tv?void 0:tv.createEvent)&&(t.dispatchEvent||t.fireEvent&&t!==tu)){var r=tv.createEvent("Events");r.initEvent(e,!0,!0),i=tC(r,i),t.dispatchEvent?t.dispatchEvent(i):t.fireEvent(e,i)}else if(t.hcEvents){i.target||tC(i,{preventDefault:function(){i.defaultPrevented=!0},target:t,type:e});for(var n=[],s=t,a=!1;s.hcEvents;)Object.hasOwnProperty.call(s,"hcEvents")&&s.hcEvents[e]&&(n.length&&(a=!0),n.unshift.apply(n,s.hcEvents[e])),s=Object.getPrototypeOf(s);a&&n.sort(function(t,e){return t.order-e.order}),n.forEach(function(e){!1===e.fn.call(t,i)&&i.preventDefault()})}o&&!i.defaultPrevented&&o.call(t,i)}var tW=(s=Math.random().toString(36).substring(2,9)+"-",a=0,function(){return"highcharts-"+(W?"":s)+a++});tm.jQuery&&(tm.jQuery.fn.highcharts=function(){var t=[].slice.call(arguments);if(this[0])return t[0]?(new tu[tb(t[0])?t.shift():"Chart"](this[0],t[0],t[1]),this):tg[tP(this[0],"data-highcharts-chart")]});var tG={addEvent:function(t,e,i,o){void 0===o&&(o={});var r="function"==typeof t&&t.prototype||t;Object.hasOwnProperty.call(r,"hcEvents")||(r.hcEvents={});var n=r.hcEvents;tu.Point&&t instanceof tu.Point&&t.series&&t.series.chart&&(t.series.chart.runTrackerClick=!0);var s=t.addEventListener;s&&s.call(t,e,i,!!tu.supportsPassiveEvents&&{passive:void 0===o.passive?-1!==e.indexOf("touch"):o.passive,capture:!1}),n[e]||(n[e]=[]);var a={fn:i,order:"number"==typeof o.order?o.order:1/0};return n[e].push(a),n[e].sort(function(t,e){return t.order-e.order}),function(){tR(t,e,i)}},arrayMax:function(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i},arrayMin:function(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i},attr:tP,clamp:function(t,e,i){return t>e?t<i?t:i:e},clearTimeout:function(t){tT(t)&&clearTimeout(t)},correctFloat:tI,createElement:function(t,e,i,o,r){var n=tv.createElement(t);return e&&tC(n,e),r&&tL(n,{padding:"0",border:"none",margin:"0"}),i&&tL(n,i),o&&o.appendChild(n),n},crisp:function(t,e,i){void 0===e&&(e=0);var o=e%2/2,r=i?-1:1;return(Math.round(t*r-o)+o)*r},css:tL,defined:tT,destroyObjectProperties:function(t,e,i){tz(t,function(o,r){o!==e&&(null==o?void 0:o.destroy)&&o.destroy(),((null==o?void 0:o.destroy)||!i)&&delete t[r]})},diffObjects:function(t,e,i,o){var r={};return!function t(e,r,n,s){var a=i?r:e;tz(e,function(i,h){if(!s&&o&&o.indexOf(h)>-1&&r[h]){i=tO(i),n[h]=[];for(var l=0;l<Math.max(i.length,r[h].length);l++)r[h][l]&&(void 0===i[l]?n[h][l]=r[h][l]:(n[h][l]={},t(i[l],r[h][l],n[h][l],s+1)))}else tk(i,!0)&&!i.nodeType?(n[h]=tM(i)?[]:{},t(i,r[h]||{},n[h],s+1),0===Object.keys(n[h]).length&&("colorAxis"!==h||0!==s)&&delete n[h]):(e[h]!==r[h]||h in e&&!(h in r))&&"__proto__"!==h&&"constructor"!==h&&(n[h]=a[h])})}(t,e,r,0),r},discardElement:function(t){var e;null===(e=null==t?void 0:t.parentElement)||void 0===e||e.removeChild(t)},erase:function(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}},error:ty,extend:tC,extendClass:function(t,e){var i=function(){};return i.prototype=new t,tC(i.prototype,e),i},find:tD,fireEvent:tN,getAlignFactor:function(t){return void 0===t&&(t=""),({center:.5,right:1,middle:.5,bottom:1})[t]||0},getClosestDistance:function(t,e){var i,o,r,n,s=!e;return t.forEach(function(t){if(t.length>1)for(n=o=t.length-1;n>0;n--)(r=t[n]-t[n-1])<0&&!s?(null==e||e(),e=void 0):r&&(void 0===i||r<i)&&(i=r)}),i},getMagnitude:tB,getNestedProperty:function(t,e){for(var i=t.split(".");i.length&&tT(e);){var o=i.shift();if(void 0===o||"__proto__"===o)return;if("this"===o){var r=void 0;return tk(e)&&(r=e["@this"]),null!=r?r:e}var n=e[o.replace(/[\\'"]/g,"")];if(!tT(n)||"function"==typeof n||"number"==typeof n.nodeType||n===tm)return;e=n}return e},getStyle:function t(e,i,o){if("width"===i){var r,n,s=Math.min(e.offsetWidth,e.scrollWidth),a=null===(r=e.getBoundingClientRect)||void 0===r?void 0:r.call(e).width;return a<s&&a>=s-1&&(s=Math.floor(a)),Math.max(0,s-(t(e,"padding-left",!0)||0)-(t(e,"padding-right",!0)||0))}if("height"===i)return Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-(t(e,"padding-top",!0)||0)-(t(e,"padding-bottom",!0)||0));var h=tm.getComputedStyle(e,void 0);return h&&(n=h.getPropertyValue(i),tE(o,"opacity"!==i)&&(n=tx(n))),n},insertItem:function(t,e){var i,o=t.options.index,r=e.length;for(i=t.options.isInternal?r:0;i<r+1;i++)if(!e[i]||tA(o)&&o<tE(e[i].options.index,e[i]._i)||e[i].options.isInternal){e.splice(i,0,t);break}return i},isArray:tM,isClass:tS,isDOMElement:tw,isFunction:function(t){return"function"==typeof t},isNumber:tA,isObject:tk,isString:tb,merge:function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o,r=tf([t],e,!0),n={},s=function(t,e){return"object"!=typeof t&&(t={}),tz(e,function(i,o){"__proto__"!==o&&"constructor"!==o&&(!tk(i,!0)||tS(i)||tw(i)?t[o]=e[o]:t[o]=s(t[o]||{},i))}),t};!0===t&&(n=r[1],r=Array.prototype.slice.call(r,2));var a=r.length;for(o=0;o<a;o++)n=s(n,r[o]);return n},normalizeTickInterval:function(t,e,i,o,r){var n,s=t;i=tE(i,tB(t));var a=t/i;for(!e&&(e=r?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===o&&(1===i?e=e.filter(function(t){return t%1==0}):i<=.1&&(e=[1/i]))),n=0;n<e.length&&(s=e[n],(!r||!(s*i>=t))&&(r||!(a<=(e[n]+(e[n+1]||e[n]))/2)));n++);return tI(s*i,-Math.round(Math.log(.001)/Math.LN10))},objectEach:tz,offset:function(t){var e=tv.documentElement,i=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:i.top+(tm.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(tm.pageXOffset||e.scrollLeft)-(e.clientLeft||0),width:i.width,height:i.height}},pad:function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||"0")+t},pick:tE,pInt:tx,pushUnique:function(t,e){return 0>t.indexOf(e)&&!!t.push(e)},relativeLength:function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},removeEvent:tR,replaceNested:function(t){for(var e,i,o=[],r=1;r<arguments.length;r++)o[r-1]=arguments[r];do{e=t;for(var n=0;n<o.length;n++)i=o[n],t=t.replace(i[0],i[1])}while(t!==e);return t},splat:tO,stableSort:function(t,e){var i,o,r=t.length;for(o=0;o<r;o++)t[o].safeI=o;for(t.sort(function(t,o){return 0===(i=e(t,o))?t.safeI-o.safeI:i}),o=0;o<r;o++)delete t[o].safeI},syncTimeout:function(t,e,i){return e>0?setTimeout(t,e,i):(t.call(0,i),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(t){return tb(t)?t.substring(0,1).toUpperCase()+t.substring(1):String(t)},uniqueKey:tW,useSerialIds:function(t){return W=tE(t,W)},wrap:function(t,e,i){var o=t[e];t[e]=function(){var t=arguments,e=this;return i.apply(this,[function(){return o.apply(e,arguments.length?arguments:t)}].concat([].slice.call(arguments)))}}},tX=tu.pageLang,tF=tu.win,tH=tG.defined,tj=tG.error,tY=tG.extend,t_=tG.isNumber,tU=tG.isObject,tV=tG.isString,tq=tG.merge,tZ=tG.objectEach,tK=tG.pad,t$=tG.splat,tJ=tG.timeUnits,tQ=tG.ucfirst,t0=tu.isSafari&&tF.Intl&&!tF.Intl.DateTimeFormat.prototype.formatRange,t1=function(){function t(t,e){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=tF.Date,this.update(t),this.lang=e}return t.prototype.update=function(t){var e=this;void 0===t&&(t={}),this.dTLCache={},this.options=t=tq(!0,this.options,t);var i=t.timezoneOffset,o=t.useUTC;this.Date=t.Date||tF.Date||Date;var r=t.timezone;tH(o)&&(r=o?"UTC":void 0),i&&i%60==0&&(r="Etc/GMT"+(i>0?"+":"")+i/60),this.variableTimezone="UTC"!==r&&(null==r?void 0:r.indexOf("Etc/GMT"))!==0,this.timezone=r,["months","shortMonths","weekdays","shortWeekdays"].forEach(function(t){var i=/months/i.test(t),o=/short/.test(t),r={timeZone:"UTC"};r[i?"month":"weekday"]=o?"short":"long",e[t]=(i?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(function(t){return e.dateFormat(r,(i?31:1)*24*36e5*t)})})},t.prototype.toParts=function(t){var e=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},t,"es").split(/(?:, | |\/|:)/g),i=e[0],o=e[1],r=e[2];return[e[3],+r-1,o,e[4],e[5],e[6],Math.floor(Number(t)||0)%1e3,"DLMXJVS".indexOf(i)].map(Number)},t.prototype.dateTimeFormat=function(t,e,i){void 0===i&&(i=this.options.locale||tX);var o,r=JSON.stringify(t)+i;tV(t)&&(t=this.str2dtf(t));var n=this.dTLCache[r];if(!n){null!==(o=t.timeZone)&&void 0!==o||(t.timeZone=this.timezone);try{n=new Intl.DateTimeFormat(i,t)}catch(e){/Invalid time zone/i.test(e.message)?(tj(34),t.timeZone="UTC",n=new Intl.DateTimeFormat(i,t)):tj(e.message,!1)}}return this.dTLCache[r]=n,(null==n?void 0:n.format(e))||""},t.prototype.str2dtf=function(t,e){void 0===e&&(e={});var i={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(i).forEach(function(o){-1!==t.indexOf(o)&&tY(e,i[o])}),e},t.prototype.makeTime=function(t,e,i,o,r,n,s){void 0===i&&(i=1),void 0===o&&(o=0);var a=this.Date.UTC(t,e,i,o,r||0,n||0,s||0);if("UTC"!==this.timezone){var h=this.getTimezoneOffset(a);if(a+=h,-1!==[2,3,8,9,10,11].indexOf(e)&&(o<5||o>20)){var l=this.getTimezoneOffset(a);h!==l?a+=l-h:h-36e5!==this.getTimezoneOffset(a-36e5)||t0||(a-=36e5)}}return a},t.prototype.parse=function(t){if(!tV(t))return null!=t?t:void 0;var e=(t=t.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(t),i=/^[0-9]{4}-[0-9]{2}(-[0-9]{2}|)$/.test(t);e||i||(t+="Z");var o=Date.parse(t);if(t_(o))return o+(!e||i?this.getTimezoneOffset(o):0)},t.prototype.getTimezoneOffset=function(t){if("UTC"!==this.timezone){var e=this.dateTimeFormat({timeZoneName:"shortOffset"},t,"en").split(/(GMT|:)/).map(Number),i=(e[0],e[1],e[2]),o=(e[3],e[4]),r=-(36e5*(i+(void 0===o?0:o)/60));if(t_(r))return r}return 0},t.prototype.dateFormat=function(t,e,i){var o,r=this.lang;if(!tH(e)||isNaN(e))return(null==r?void 0:r.invalidDate)||"";if(tV(t=null!=t?t:"%Y-%m-%d %H:%M:%S"))for(var n=/%\[([a-zA-Z]+)\]/g,s=void 0;s=n.exec(t);)t=t.replace(s[0],this.dateTimeFormat(s[1],e,null==r?void 0:r.locale));if(tV(t)&&-1!==t.indexOf("%")){var a=this,h=this.toParts(e),l=h[0],d=h[1],c=h[2],p=h[3],u=h[4],f=h[5],g=h[6],v=h[7],m=(null==r?void 0:r.weekdays)||this.weekdays,y=(null==r?void 0:r.shortWeekdays)||this.shortWeekdays,x=(null==r?void 0:r.months)||this.months,b=(null==r?void 0:r.shortMonths)||this.shortMonths;tZ(tY({a:y?y[v]:m[v].substr(0,3),A:m[v],d:tK(c),e:tK(c,2," "),w:v,v:null!==(o=null==r?void 0:r.weekFrom)&&void 0!==o?o:"",b:b[d],B:x[d],m:tK(d+1),o:d+1,y:l.toString().substr(2,2),Y:l,H:tK(p),k:p,I:tK(p%12||12),l:p%12||12,M:tK(u),p:p<12?"AM":"PM",P:p<12?"am":"pm",S:tK(f),L:tK(g,3)},tu.dateFormats),function(i,o){if(tV(t))for(;-1!==t.indexOf("%"+o);)t=t.replace("%"+o,"function"==typeof i?i.call(a,e):i)})}else if(tU(t)){var M=(this.getTimezoneOffset(e)||0)/36e5,k=this.timezone||"Etc/GMT"+(M>=0?"+":"")+M,w=t.prefix,S=t.suffix;t=(void 0===w?"":w)+this.dateTimeFormat(tY({timeZone:k},t),e)+(void 0===S?"":S)}return i?tQ(t):t},t.prototype.resolveDTLFormat=function(t){return tU(t,!0)?tU(t,!0)&&void 0===t.main?{main:t}:t:{main:(t=t$(t))[0],from:t[1],to:t[2]}},t.prototype.getDateFormat=function(t,e,i,o){var r=this.dateFormat("%m-%d %H:%M:%S.%L",e),n="01-01 00:00:00.000",s={millisecond:15,second:12,minute:9,hour:6,day:3},a="millisecond",h=a;for(a in tJ){if(t&&t===tJ.week&&+this.dateFormat("%w",e)===i&&r.substr(6)===n.substr(6)){a="week";break}if(t&&tJ[a]>t){a=h;break}if(s[a]&&r.substr(s[a])!==n.substr(s[a]))break;"week"!==a&&(h=a)}return this.resolveDTLFormat(o[a]).main},t}(),t2=(h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),t3=tG.defined,t5=tG.extend,t6=tG.timeUnits,t9=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return t2(e,t),e.prototype.getTimeTicks=function(t,e,i,o){var r,n=this,s=[],a={},h=t.count,l=void 0===h?1:h,d=t.unitRange,c=n.toParts(e),p=c[0],u=c[1],f=c[2],g=c[3],v=c[4],m=c[5],y=(e||0)%1e3;if(null!=o||(o=1),t3(e)){if(y=d>=t6.second?0:l*Math.floor(y/l),d>=t6.second&&(m=d>=t6.minute?0:l*Math.floor(m/l)),d>=t6.minute&&(v=d>=t6.hour?0:l*Math.floor(v/l)),d>=t6.hour&&(g=d>=t6.day?0:l*Math.floor(g/l)),d>=t6.day&&(f=d>=t6.month?1:Math.max(1,l*Math.floor(f/l))),d>=t6.month&&(u=d>=t6.year?0:l*Math.floor(u/l)),d>=t6.year&&(p-=p%l),d===t6.week){l&&(e=n.makeTime(p,u,f,g,v,m,y));var x=this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},e,"es"),b="DLMXJVS".indexOf(x);f+=-b+o+(b<o?-7:0)}e=n.makeTime(p,u,f,g,v,m,y),n.variableTimezone&&t3(i)&&(r=i-e>4*t6.month||n.getTimezoneOffset(e)!==n.getTimezoneOffset(i));for(var M=e,k=1;M<i;)s.push(M),d===t6.year?M=n.makeTime(p+k*l,0):d===t6.month?M=n.makeTime(p,u+k*l):r&&(d===t6.day||d===t6.week)?M=n.makeTime(p,u,f+k*l*(d===t6.day?1:7)):r&&d===t6.hour&&l>1?M=n.makeTime(p,u,f,g+k*l):M+=d*l,k++;s.push(M),d<=t6.hour&&s.length<1e4&&s.forEach(function(t){t%18e5==0&&"000000000"===n.dateFormat("%H%M%S%L",t)&&(a[t]="day")})}return s.info=t5(t,{higherRanks:a,totalRange:d*l}),s},e}(t1),t4=function(){return(t4=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},t8=tu.isTouchDevice,t7=tG.fireEvent,et=tG.merge,ee={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{weekFrom:"week from",chartTitle:"Chart title",locale:void 0,loading:"Loading...",months:void 0,seriesName:"Series {add index 1}",shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],pieSliceName:"Slice",resetZoom:"Reset zoom",yAxisTitle:"Values",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:function(t){return Math.sqrt(1-Math.pow(t-1,2))}},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"%v %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,position:{x:0,y:3},shared:!1,snap:t8?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},ei=new t9(ee.time,ee.lang),eo=function(t){var e;return t7(tu,"setOptions",{options:t}),et(!0,ee,t),t.time&&ei.update(ee.time),t.lang&&"locale"in t.lang&&ei.update({locale:t.lang.locale}),(null===(e=t.lang)||void 0===e?void 0:e.chartTitle)&&(ee.title=t4(t4({},ee.title),{text:t.lang.chartTitle})),ee},er=tu.win,en=tG.isNumber,es=tG.isString,ea=tG.merge,eh=tG.pInt,el=tG.defined,ed=function(t,e,i){return"color-mix(in srgb,".concat(t,",").concat(e," ").concat(100*i,"%)")},ec=function(t){return es(t)&&!!t&&"none"!==t},ep=function(){var t;function e(t){this.rgba=[NaN,NaN,NaN,NaN],this.input=t;var i,o,r,n,s=tu.Color;if(s&&s!==e)return new s(t);if("object"==typeof t&&void 0!==t.stops)this.stops=t.stops.map(function(t){return new e(t[1])});else if("string"==typeof t)for(this.input=t=e.names[t.toLowerCase()]||t,r=e.parsers.length;r--&&!o;)(i=(n=e.parsers[r]).regex.exec(t))&&(o=n.parse(i));o&&(this.rgba=o)}return e.parse=function(t){return t?new e(t):e.None},e.prototype.get=function(t){var e=this.input,i=this.rgba;if(this.output)return this.output;if("object"==typeof e&&void 0!==this.stops){var o=ea(e);return o.stops=[].slice.call(o.stops),this.stops.forEach(function(e,i){o.stops[i]=[o.stops[i][0],e.get(t)]}),o}return i&&en(i[0])?"rgb"!==t&&(t||1!==i[3])?"a"===t?"".concat(i[3]):"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e},e.prototype.brighten=function(t){var i=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(en(t)&&0!==t){if(en(i[0]))for(var o=0;o<3;o++)i[o]+=eh(255*t),i[o]<0&&(i[o]=0),i[o]>255&&(i[o]=255);else e.useColorMix&&ec(this.input)&&(this.output=ed(this.input,t>0?"white":"black",Math.abs(t)))}return this},e.prototype.setOpacity=function(t){return this.rgba[3]=t,this},e.prototype.tweenTo=function(t,i){var o=this.rgba,r=t.rgba;if(!en(o[0])||!en(r[0]))return e.useColorMix&&ec(this.input)&&ec(t.input)&&i<.99?ed(this.input,t.input,i):t.input||"none";var n=1!==r[3]||1!==o[3],s=function(t,e){return t+(o[e]-t)*(1-i)},a=r.slice(0,3).map(s).map(Math.round);return n&&a.push(s(r[3],3)),(n?"rgba(":"rgb(")+a.join(",")+")"},e.names={white:"#ffffff",black:"#000000"},e.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(t){return[eh(t[1]),eh(t[2]),eh(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(t){return[eh(t[1]),eh(t[2]),eh(t[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(t){return[eh(t[1]+t[1],16),eh(t[2]+t[2],16),eh(t[3]+t[3],16),el(t[4])?eh(t[4]+t[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(t){return[eh(t[1],16),eh(t[2],16),eh(t[3],16),el(t[4])?eh(t[4],16)/255:1]}}],e.useColorMix=null===(t=er.CSS)||void 0===t?void 0:t.supports("color","color-mix(in srgb,red,blue 9%)"),e.None=new e(""),e}(),eu=ep.parse,ef=tu.win,eg=tG.isNumber,ev=tG.objectEach,em=function(){function t(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}return t.prototype.dSetter=function(){var t=this.paths,e=null==t?void 0:t[0],i=null==t?void 0:t[1],o=this.now||0,r=[];if(1!==o&&e&&i){if(e.length===i.length&&o<1)for(var n=0;n<i.length;n++){for(var s=e[n],a=i[n],h=[],l=0;l<a.length;l++){var d=s[l],c=a[l];eg(d)&&eg(c)&&("A"!==a[0]||4!==l&&5!==l)?h[l]=d+o*(c-d):h[l]=c}r.push(h)}else r=i}else r=this.toD||[];this.elem.attr("d",r,void 0,!0)},t.prototype.update=function(){var t=this.elem,e=this.prop,i=this.now,o=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,o&&o.call(t,i,this)},t.prototype.run=function(e,i,o){var r=this,n=r.options,s=function(t){return!s.stopped&&r.step(t)},a=ef.requestAnimationFrame||function(t){setTimeout(t,13)},h=function(){for(var e=0;e<t.timers.length;e++)t.timers[e]()||t.timers.splice(e--,1);t.timers.length&&a(h)};e!==i||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=e,this.end=i,this.unit=o,this.now=this.start,this.pos=0,s.elem=this.elem,s.prop=this.prop,s()&&1===t.timers.push(s)&&a(h)):(delete n.curAnim[this.prop],n.complete&&0===Object.keys(n.curAnim).length&&n.complete.call(this.elem))},t.prototype.step=function(t){var e,i,o=+new Date,r=this.options,n=this.elem,s=r.complete,a=r.duration,h=r.curAnim;return n.attr&&!n.element?e=!1:t||o>=a+this.startTime?(this.now=this.end,this.pos=1,this.update(),h[this.prop]=!0,i=!0,ev(h,function(t){!0!==t&&(i=!1)}),i&&s&&s.call(n),e=!1):(this.pos=r.easing((o-this.startTime)/a),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e},t.prototype.initPath=function(t,e,i){var o,r,n,s,a=t.startX,h=t.endX,l=i.slice(),d=t.isArea,c=d?2:1,p=e&&i.length>e.length&&i.hasStackedCliffs,u=null==e?void 0:e.slice();if(!u||p)return[l,l];function f(t,e){for(;t.length<r;){var i=t[0],o=e[r-t.length];if(o&&"M"===i[0]&&("C"===o[0]?t[0]=["C",i[1],i[2],i[1],i[2],i[1],i[2]]:t[0]=["L",i[1],i[2]]),t.unshift(i),d){var n=t.pop();t.push(t[t.length-1],n)}}}function g(t){for(;t.length<r;){var e=t[Math.floor(t.length/c)-1].slice();if("C"===e[0]&&(e[1]=e[5],e[2]=e[6]),d){var i=t[Math.floor(t.length/c)].slice();t.splice(t.length/2,0,e,i)}else t.push(e)}}if(a&&h&&h.length){for(n=0;n<a.length;n++){if(a[n]===h[0]){o=n;break}if(a[0]===h[h.length-a.length+n]){o=n,s=!0;break}if(a[a.length-1]===h[h.length-a.length+n]){o=a.length-n;break}}void 0===o&&(u=[])}return u.length&&eg(o)&&(r=l.length+o*c,s?(f(u,l),g(l)):(f(l,u),g(u))),[u,l]},t.prototype.fillSetter=function(){t.prototype.strokeSetter.apply(this,arguments)},t.prototype.strokeSetter=function(){this.elem.attr(this.prop,eu(this.start).tweenTo(eu(this.end),this.pos),void 0,!0)},t.timers=[],t}(),ey=tG.defined,ex=tG.getStyle,eb=tG.isArray,eM=tG.isNumber,ek=tG.isObject,ew=tG.merge,eS=tG.objectEach,eA=tG.pick;function eT(t){return ek(t)?ew({duration:500,defer:0},t):{duration:500*!!t,defer:0}}function eP(t,e){for(var i=em.timers.length;i--;)em.timers[i].elem!==t||e&&e!==em.timers[i].prop||(em.timers[i].stopped=!0)}var eO=function(t,e,i){var o,r,n,s,a="";ek(i)||(s=arguments,i={duration:s[2],easing:s[3],complete:s[4]}),eM(i.duration)||(i.duration=400),i.easing="function"==typeof i.easing?i.easing:Math[i.easing]||Math.easeInOutSine,i.curAnim=ew(e),eS(e,function(s,h){eP(t,h),n=new em(t,i,h),r=void 0,"d"===h&&eb(e.d)?(n.paths=n.initPath(t,t.pathArray,e.d),n.toD=e.d,o=0,r=1):t.attr?o=t.attr(h):(o=parseFloat(ex(t,h))||0,"opacity"!==h&&(a="px")),r||(r=s),"string"==typeof r&&r.match("px")&&(r=r.replace(/px/g,"")),n.run(o,r,a)})},eC=function(t,e,i){var o=eT(e),r=i?[i]:t.series,n=0,s=0;return r.forEach(function(t){var i=eT(t.options.animation);n=ek(e)&&ey(e.defer)?o.defer:Math.max(n,i.duration+i.defer),s=Math.min(o.duration,i.duration)}),t.renderer.forExport&&(n=0),{defer:Math.max(0,n-s),duration:Math.min(n,s)}},eE=function(t,e){e.renderer.globalAnimation=eA(t,e.options.chart.animation,!0)},eL=tu.SVG_NS,eB=tu.win,eI=tG.attr,eD=tG.createElement,ez=tG.css,eR=tG.error,eN=tG.isFunction,eW=tG.isString,eG=tG.objectEach,eX=tG.splat,eF=eB.trustedTypes,eH=eF&&eN(eF.createPolicy)&&eF.createPolicy("highcharts",{createHTML:function(t){return t}}),ej=eH?eH.createHTML(""):"",eY=function(){function t(t){this.nodes="string"==typeof t?this.parseMarkup(t):t}return t.filterUserAttributes=function(e){return eG(e,function(i,o){var r=!0;-1===t.allowedAttributes.indexOf(o)&&(r=!1),-1!==["background","dynsrc","href","lowsrc","src"].indexOf(o)&&(r=eW(i)&&t.allowedReferences.some(function(t){return 0===i.indexOf(t)})),r||(eR(33,!1,void 0,{"Invalid attribute in config":"".concat(o)}),delete e[o]),eW(i)&&e[o]&&(e[o]=i.replace(/</g,"&lt;"))}),e},t.parseStyle=function(t){return t.split(";").reduce(function(t,e){var i=e.split(":").map(function(t){return t.trim()}),o=i.shift();return o&&i.length&&(t[o.replace(/-([a-z])/g,function(t){return t[1].toUpperCase()})]=i.join(":")),t},{})},t.setElementHTML=function(e,i){e.innerHTML=t.emptyHTML,i&&new t(i).addToDOM(e)},t.prototype.addToDOM=function(e){return function e(i,o){var r;return eX(i).forEach(function(i){var n,s=i.tagName,a=i.textContent?tu.doc.createTextNode(i.textContent):void 0,h=t.bypassHTMLFiltering;if(s){if("#text"===s)n=a;else if(-1!==t.allowedTags.indexOf(s)||h){var l="svg"===s?eL:o.namespaceURI||eL,d=tu.doc.createElementNS(l,s),c=i.attributes||{};eG(i,function(t,e){"tagName"!==e&&"attributes"!==e&&"children"!==e&&"style"!==e&&"textContent"!==e&&(c[e]=t)}),eI(d,h?c:t.filterUserAttributes(c)),i.style&&ez(d,i.style),a&&d.appendChild(a),e(i.children||[],d),n=d}else eR(33,!1,void 0,{"Invalid tagName in config":s})}n&&o.appendChild(n),r=n}),r}(this.nodes,e)},t.prototype.parseMarkup=function(e){var i,o=[];e=e.trim().replace(/ style=(["'])/g," data-style=$1");try{i=new DOMParser().parseFromString(eH?eH.createHTML(e):e,"text/html")}catch(t){}if(!i){var r=eD("div");r.innerHTML=e,i={body:r}}var n=function(e,i){var o=e.nodeName.toLowerCase(),r={tagName:o};"#text"===o&&(r.textContent=e.textContent||"");var s=e.attributes;if(s){var a={};[].forEach.call(s,function(e){"data-style"===e.name?r.style=t.parseStyle(e.value):a[e.name]=e.value}),r.attributes=a}if(e.childNodes.length){var h=[];[].forEach.call(e.childNodes,function(t){n(t,h)}),h.length&&(r.children=h)}i.push(r)};return[].forEach.call(i.body.childNodes,function(t){return n(t,o)}),o},t.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],t.allowedReferences=["https://","http://","mailto:","/","../","./","#"],t.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],t.emptyHTML=ej,t.bypassHTMLFiltering=!1,t}(),e_=tu.pageLang,eU=tG.extend,eV=tG.getNestedProperty,eq=tG.isArray,eZ=tG.isNumber,eK=tG.isObject,e$=tG.isString,eJ=tG.pick,eQ={add:function(t,e){return t+e},divide:function(t,e){return 0!==e?t/e:""},eq:function(t,e){return t==e},each:function(t){var e=arguments[arguments.length-1];return!!eq(t)&&t.map(function(i,o){return e2(e.body,eU(eK(i)?i:{"@this":i},{"@index":o,"@first":0===o,"@last":o===t.length-1}))}).join("")},ge:function(t,e){return t>=e},gt:function(t,e){return t>e},if:function(t){return!!t},le:function(t,e){return t<=e},lt:function(t,e){return t<e},multiply:function(t,e){return t*e},ne:function(t,e){return t!=e},subtract:function(t,e){return t-e},ucfirst:tG.ucfirst,unless:function(t){return!t}},e0={},e1=function(t){return/^["'].+["']$/.test(t)};function e2(t,e,i){void 0===t&&(t="");for(var o,r,n,s,a=/\{([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/g,h=/\(([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/g,l=[],d=/f$/,c=/\.(\d)/,p=(null===(o=null==i?void 0:i.options)||void 0===o?void 0:o.lang)||ee.lang,u=(null==i?void 0:i.time)||ei,f=(null==i?void 0:i.numberFormatter)||e3,g=function(t){var i;return void 0===t&&(t=""),"true"===t||"false"!==t&&((i=Number(t)).toString()===t?i:e1(t)?t.slice(1,-1):eV(t,e))},v=0;null!==(r=a.exec(t));){var m=r,y=h.exec(r[1]);y&&(r=y,s=!0),(null==n?void 0:n.isBlock)||(n={ctx:e,expression:r[1],find:r[0],isBlock:"#"===r[1].charAt(0),start:r.index,startInner:r.index+r[0].length,length:r[0].length});var x=(n.isBlock?m:r)[1].split(" ")[0].replace("#","");eQ[x]&&(n.isBlock&&x===n.fn&&v++,n.fn||(n.fn=x));var b="else"===r[1];if(n.isBlock&&n.fn&&(r[1]==="/".concat(n.fn)||b)){if(v)!b&&v--;else{var M=n.startInner,k=t.substr(M,r.index-M);void 0===n.body?(n.body=k,n.startInner=r.index+r[0].length):n.elseBody=k,n.find+=k+r[0],b||(l.push(n),n=void 0)}}else n.isBlock||l.push(n);if(y&&!(null==n?void 0:n.isBlock))break}return l.forEach(function(o){var r,n,s=o.body,a=o.elseBody,l=o.expression,v=o.fn;if(v){var m=[o],y=[],x=l.length,b=0,M=void 0;for(n=0;n<=x;n++){var k=l.charAt(n);M||'"'!==k&&"'"!==k?M===k&&(M=""):M=k,M||" "!==k&&n!==x||(y.push(l.substr(b,n-b)),b=n+1)}for(n=eQ[v].length;n--;)m.unshift(g(y[n+1]));r=eQ[v].apply(e,m),o.isBlock&&"boolean"==typeof r&&(r=e2(r?s:a,e,i))}else{var w=e1(l)?[l]:l.split(":");if(r=g(w.shift()||""),w.length&&"number"==typeof r){var S=w.join(":");if(d.test(S)){var A=parseInt((S.match(c)||["","-1"])[1],10);null!==r&&(r=f(r,A,p.decimalPoint,S.indexOf(",")>-1?p.thousandsSep:""))}else r=u.dateFormat(S,r)}h.lastIndex=0,h.test(o.find)&&e$(r)&&(r='"'.concat(r,'"'))}t=t.replace(o.find,eJ(r,""))}),s?e2(t,e,i):t}function e3(t,e,i,o){e*=1;var r,n,s,a,h=(t=+t||0).toString().split("e").map(Number),l=h[0],d=h[1],c=(null===(r=this===null||void 0===this?void 0:this.options)||void 0===r?void 0:r.lang)||ee.lang,p=(t.toString().split(".")[1]||"").split("e")[0].length,u=e,f={};null!=i||(i=c.decimalPoint),null!=o||(o=c.thousandsSep),-1===e?e=Math.min(p,20):eZ(e)?e&&d<0&&((a=e+d)>=0?(l=+l.toExponential(a).split("e")[0],e=a):(l=Math.floor(l),t=e<20?+(l*Math.pow(10,d)).toFixed(e):0,d=0)):e=2,d&&(null!=e||(e=2),t=l),eZ(e)&&e>=0&&(f.minimumFractionDigits=e,f.maximumFractionDigits=e),""===o&&(f.useGrouping=!1);var g=o||i,v=g?"en":(this===null||void 0===this?void 0:this.locale)||c.locale||e_,m=JSON.stringify(f)+v;return s=(null!==(n=e0[m])&&void 0!==n?n:e0[m]=new Intl.NumberFormat(v,f)).format(t),g&&(s=s.replace(/([,\.])/g,"_$1").replace(/_\,/g,null!=o?o:",").replace("_.",null!=i?i:".")),(e||0!=+s)&&(!(d<0)||u)||(s="0"),d&&0!=+s&&(s+="e"+(d<0?"":"+")+d),s}var e5={dateFormat:function(t,e,i){return ei.dateFormat(t,e,i)},format:e2,helpers:eQ,numberFormat:e3};(l=G||(G={})).rendererTypes={},l.getRendererType=function(t){return void 0===t&&(t=d),l.rendererTypes[t]||l.rendererTypes[d]},l.registerRendererType=function(t,e,i){l.rendererTypes[t]=e,(!d||i)&&(d=t,tu.Renderer=e)};var e6=G,e9=tG.clamp,e4=tG.pick,e8=tG.pushUnique,e7=tG.stableSort;(X||(X={})).distribute=function t(e,i,o){var r,n,s,a,h,l,d=e,c=d.reducedLen||i,p=function(t,e){return t.target-e.target},u=[],f=e.length,g=[],v=u.push,m=!0,y=0;for(r=f;r--;)y+=e[r].size;if(y>c){for(e7(e,function(t,e){return(e.rank||0)-(t.rank||0)}),s=(l=e[0].rank===e[e.length-1].rank)?f/2:-1,n=l?s:f-1;s&&y>c;)a=e[r=Math.floor(n)],e8(g,r)&&(y-=a.size),n+=s,l&&n>=e.length&&(s/=2,n=s);g.sort(function(t,e){return e-t}).forEach(function(t){return v.apply(u,e.splice(t,1))})}for(e7(e,p),e=e.map(function(t){return{size:t.size,targets:[t.target],align:e4(t.align,.5)}});m;){for(r=e.length;r--;)a=e[r],h=(Math.min.apply(0,a.targets)+Math.max.apply(0,a.targets))/2,a.pos=e9(h-a.size*a.align,0,i-a.size);for(r=e.length,m=!1;r--;)r>0&&e[r-1].pos+e[r-1].size>e[r].pos&&(e[r-1].size+=e[r].size,e[r-1].targets=e[r-1].targets.concat(e[r].targets),e[r-1].align=.5,e[r-1].pos+e[r-1].size>i&&(e[r-1].pos=i-e[r-1].size),e.splice(r,1),m=!0)}return v.apply(d,u),r=0,e.some(function(e){var n=0;return(e.targets||[]).some(function(){return(d[r].pos=e.pos+n,void 0!==o&&Math.abs(d[r].pos-d[r].target)>o)?(d.slice(0,r+1).forEach(function(t){return delete t.pos}),d.reducedLen=(d.reducedLen||i)-.1*i,d.reducedLen>.1*i&&t(d,i,o),!0):(n+=d[r].size,r++,!1)})}),e7(d,p),d};var it=X,ie=tu.deg2rad,ii=tu.doc,io=tu.svg,ir=tu.SVG_NS,is=tu.win,ia=tu.isFirefox,ih=tG.addEvent,il=tG.attr,id=tG.createElement,ic=tG.crisp,ip=tG.css,iu=tG.defined,ig=tG.erase,iv=tG.extend,im=tG.fireEvent,iy=tG.getAlignFactor,ix=tG.isArray,ib=tG.isFunction,iM=tG.isNumber,ik=tG.isObject,iw=tG.isString,iS=tG.merge,iA=tG.objectEach,iT=tG.pick,iP=tG.pInt,iO=tG.pushUnique,iC=tG.replaceNested,iE=tG.syncTimeout,iL=tG.uniqueKey,iB=function(){function t(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=ir,this.element="span"===e||"body"===e?id(e):ii.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},im(this,"afterInit")}return t.prototype._defaultGetter=function(t){var e=iT(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e},t.prototype._defaultSetter=function(t,e,i){i.setAttribute(e,t)},t.prototype.add=function(t){var e,i=this.renderer,o=this.element;return t&&(this.parentGroup=t),void 0!==this.textStr&&"text"===this.element.nodeName&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(o),this.onAdd&&this.onAdd(),this},t.prototype.addClass=function(t,e){var i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(t,e){return -1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this},t.prototype.afterSetters=function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},t.prototype.align=function(t,e,i,o){void 0===o&&(o=!0);var r=this.renderer,n=r.alignedObjects,s=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);var a=!i||iw(i)?i||"renderer":void 0;a&&(s&&iO(n,this),i=void 0);var h=iT(i,r[a],r),l=(h.x||0)+(t.x||0)+((h.width||0)-(t.width||0))*iy(t.align),d=(h.y||0)+(t.y||0)+((h.height||0)-(t.height||0))*iy(t.verticalAlign),c={"text-align":null==t?void 0:t.align};return c[e?"translateX":"x"]=Math.round(l),c[e?"translateY":"y"]=Math.round(d),o&&(this[this.placed?"animate":"attr"](c),this.placed=!0),this.alignAttr=c,this},t.prototype.alignSetter=function(t){var e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))},t.prototype.animate=function(t,e,i){var o=this,r=eT(iT(e,this.renderer.globalAnimation,!0)),n=r.defer;return ii.hidden&&(r.duration=0),0!==r.duration?(i&&(r.complete=i),iE(function(){o.element&&eO(o,t,r)},n)):(this.attr(t,void 0,i||r.complete),iA(t,function(t,e){r.step&&r.step.call(this,t,{prop:e,pos:1,elem:this})},this)),this},t.prototype.applyTextOutline=function(t){var e=this.element;-1!==t.indexOf("contrast")&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));var i=t.indexOf(" "),o=t.substring(i+1),r=t.substring(0,i);if(r&&"none"!==r&&tu.svg){this.fakeTS=!0,r=r.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*Number(e)+i}),this.removeTextOutline();var n=ii.createElementNS(ir,"tspan");il(n,{class:"highcharts-text-outline",fill:o,stroke:o,"stroke-width":r,"stroke-linejoin":"round"});var s=e.querySelector("textPath")||e;[].forEach.call(s.childNodes,function(t){var e=t.cloneNode(!0);e.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(function(t){return e.removeAttribute(t)}),n.appendChild(e)});var a=0;[].forEach.call(s.querySelectorAll("text tspan"),function(t){a+=Number(t.getAttribute("dy"))});var h=ii.createElementNS(ir,"tspan");h.textContent="​",il(h,{x:Number(e.getAttribute("x")),dy:-a}),n.appendChild(h),s.insertBefore(n,s.firstChild)}},t.prototype.attr=function(e,i,o,r){var n,s,a,h=this.element,l=t.symbolCustomAttribs,d=this;return"string"==typeof e&&void 0!==i&&(n=e,(e={})[n]=i),"string"==typeof e?d=(this[e+"Getter"]||this._defaultGetter).call(this,e,h):(iA(e,function(t,i){a=!1,r||eP(this,i),this.symbolName&&-1!==l.indexOf(i)&&(s||(this.symbolAttr(e),s=!0),a=!0),this.rotation&&("x"===i||"y"===i)&&(this.doTransform=!0),a||(this[i+"Setter"]||this._defaultSetter).call(this,t,i,h)},this),this.afterSetters()),o&&o.call(this),d},t.prototype.clip=function(t){if(t&&!t.clipPath){var e=iL()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);iv(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?"url(".concat(this.renderer.url,"#").concat(t.id,")"):"none")},t.prototype.crisp=function(t,e){e=Math.round(e||t.strokeWidth||0);var i=t.x||this.x||0,o=t.y||this.y||0,r=(t.width||this.width||0)+i,n=(t.height||this.height||0)+o,s=ic(i,e),a=ic(o,e);return iv(t,{x:s,y:a,width:ic(r,e)-s,height:ic(n,e)-a}),iu(t.strokeWidth)&&(t.strokeWidth=e),t},t.prototype.complexColor=function(t,e,i){var o,r,n,s,a,h,l,d,c,p,u,f=this.renderer,g=[];im(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?r="radialGradient":t.linearGradient&&(r="linearGradient"),r){if(n=t[r],a=f.gradients,h=t.stops,c=i.radialReference,ix(n)&&(t[r]=n={x1:n[0],y1:n[1],x2:n[2],y2:n[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===r&&c&&!iu(n.gradientUnits)&&(s=n,n=iS(n,f.getRadialAttr(c,s),{gradientUnits:"userSpaceOnUse"})),iA(n,function(t,e){"id"!==e&&g.push(e,t)}),iA(h,function(t){g.push(t)}),a[g=g.join(",")])p=a[g].attr("id");else{n.id=p=iL();var v=a[g]=f.createElement(r).attr(n).add(f.defs);v.radAttr=s,v.stops=[],h.forEach(function(t){0===t[1].indexOf("rgba")?(l=(o=ep.parse(t[1])).get("rgb"),d=o.get("a")):(l=t[1],d=1);var e=f.createElement("stop").attr({offset:t[0],"stop-color":l,"stop-opacity":d}).add(v);v.stops.push(e)})}u="url("+f.url+"#"+p+")",i.setAttribute(e,u),i.gradient=g,t.toString=function(){return u}}})},t.prototype.css=function(t){var e,i=this.styles,o={},r=this.element,n=!i;if(i&&iA(t,function(t,e){i&&i[e]!==t&&(o[e]=t,n=!0)}),n){i&&(t=iv(i,o)),null===t.width||"auto"===t.width?delete this.textWidth:"text"===r.nodeName.toLowerCase()&&t.width&&(e=this.textWidth=iP(t.width)),iv(this.styles,t),e&&!io&&this.renderer.forExport&&delete t.width;var s=ia&&t.fontSize||null;s&&(iM(s)||/^\d+$/.test(s))&&(t.fontSize+="px");var a=iS(t);r.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(function(t){return a&&delete a[t]}),a.color&&(a.fill=a.color,delete a.color)),ip(r,a)}return this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this},t.prototype.dashstyleSetter=function(t){var e,i=this["stroke-width"];if("inherit"===i&&(i=1),t){var o=(t=t.toLowerCase()).replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=o.length;e--;)o[e]=""+iP(o[e])*iT(i,NaN);t=o.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}},t.prototype.destroy=function(){var t,e,i=this,o=i.element||{},r=i.renderer,n=o.ownerSVGElement,s="SPAN"===o.nodeName&&i.parentGroup||void 0;if(o.onclick=o.onmouseout=o.onmouseover=o.onmousemove=o.point=null,eP(i),i.clipPath&&n){var a=i.clipPath;[].forEach.call(n.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){t.getAttribute("clip-path").indexOf(a.element.id)>-1&&t.removeAttribute("clip-path")}),i.clipPath=a.destroy()}if(i.stops){for(e=0;e<i.stops.length;e++)i.stops[e].destroy();i.stops.length=0,i.stops=void 0}for(i.safeRemoveChild(o);(null==s?void 0:s.div)&&0===s.div.childNodes.length;)t=s.parentGroup,i.safeRemoveChild(s.div),delete s.div,s=t;i.alignOptions&&ig(r.alignedObjects,i),iA(i,function(t,e){var o,r,n;((null===(o=i[e])||void 0===o?void 0:o.parentGroup)===i||-1!==["connector","foreignObject"].indexOf(e))&&(null===(n=null===(r=i[e])||void 0===r?void 0:r.destroy)||void 0===n||n.call(r)),delete i[e]})},t.prototype.dSetter=function(t,e,i){ix(t)&&("string"==typeof t[0]&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce(function(t,e,i){return(null==e?void 0:e.join)?(i?t+" ":"")+e.join(" "):(e||"").toString()},"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)},t.prototype.fillSetter=function(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)},t.prototype.hrefSetter=function(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)},t.prototype.getBBox=function(e,i){var o,r,n,s,a=this.alignValue,h=this.element,l=this.renderer,d=this.styles,c=this.textStr,p=l.cache,u=l.cacheKeys,f=h.namespaceURI===this.SVG_NS,g=iT(i,this.rotation,0),v=l.styledMode?h&&t.prototype.getStyle.call(h,"font-size"):d.fontSize;if(iu(c)&&(-1===(s=c.toString()).indexOf("<")&&(s=s.replace(/\d/g,"0")),s+=["",l.rootFontSize,v,g,this.textWidth,a,d.lineClamp,d.textOverflow,d.fontWeight].join(",")),s&&!e&&(o=p[s]),!o||o.polygon){if(f||l.forExport){try{n=this.fakeTS&&function(t){var e=h.querySelector(".highcharts-text-outline");e&&ip(e,{display:t})},ib(n)&&n("none"),o=h.getBBox?iv({},h.getBBox()):{width:h.offsetWidth,height:h.offsetHeight,x:0,y:0},ib(n)&&n("")}catch(t){}(!o||o.width<0)&&(o={x:0,y:0,width:0,height:0})}else o=this.htmlGetBBox();r=o.height,f&&(o.height=r=({"11px,17":14,"13px,20":16})[""+(v||"")+",".concat(Math.round(r))]||r),g&&(o=this.getRotatedBox(o,g));var m={bBox:o};im(this,"afterGetBBox",m),o=m.bBox}if(s&&(""===c||o.height>0)){for(;u.length>250;)delete p[u.shift()];p[s]||u.push(s),p[s]=o}return o},t.prototype.getRotatedBox=function(t,e){var i=t.x,o=t.y,r=t.width,n=t.height,s=this.alignValue,a=this.translateY,h=this.rotationOriginX,l=this.rotationOriginY,d=iy(s),c=Number(this.element.getAttribute("y")||0)-(a?0:o),p=e*ie,u=(e-90)*ie,f=Math.cos(p),g=Math.sin(p),v=r*f,m=r*g,y=Math.cos(u),x=Math.sin(u),b=[void 0===h?0:h,void 0===l?0:l].map(function(t){return[t-t*f,t*g]}),M=b[0],k=M[0],w=M[1],S=b[1],A=S[0],T=i+d*(r-v)+k+S[1]+c*y,P=T+v,O=P-n*y,C=O-v,E=o+c-d*m-w+A+c*x,L=E+m,B=L-n*x,I=B-m,D=Math.min(T,P,O,C),z=Math.min(E,L,B,I),R=Math.max(T,P,O,C)-D,N=Math.max(E,L,B,I)-z;return{x:D,y:z,width:R,height:N,polygon:[[T,E],[P,L],[O,B],[C,I]]}},t.prototype.getStyle=function(t){return is.getComputedStyle(this.element||this,"").getPropertyValue(t)},t.prototype.hasClass=function(t){return -1!==(""+this.attr("class")).split(" ").indexOf(t)},t.prototype.hide=function(){return this.attr({visibility:"hidden"})},t.prototype.htmlGetBBox=function(){return{height:0,width:0,x:0,y:0}},t.prototype.on=function(t,e){var i=this.onEvents;return i[t]&&i[t](),i[t]=ih(this.element,t,e),this},t.prototype.opacitySetter=function(t,e,i){var o=Number(Number(t).toFixed(3));this.opacity=o,i.setAttribute(e,o)},t.prototype.reAlign=function(){var t;(null===(t=this.alignOptions)||void 0===t?void 0:t.width)&&"left"!==this.alignOptions.align&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())},t.prototype.removeClass=function(t){return this.attr("class",(""+this.attr("class")).replace(iw(t)?new RegExp("(^| )".concat(t,"( |$)")):t," ").replace(/ +/g," ").trim())},t.prototype.removeTextOutline=function(){var t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)},t.prototype.safeRemoveChild=function(t){var e=t.parentNode;e&&e.removeChild(t)},t.prototype.setRadialReference=function(t){var e=this.element.gradient&&this.renderer.gradients[this.element.gradient]||void 0;return this.element.radialReference=t,(null==e?void 0:e.radAttr)&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this},t.prototype.shadow=function(t){var e,i=this.renderer,o=iS((null===(e=this.parentGroup)||void 0===e?void 0:e.rotation)===90?{offsetX:-1,offsetY:-1}:{},ik(t)?t:{}),r=i.shadowDefinition(o);return this.attr({filter:t?"url(".concat(i.url,"#").concat(r,")"):"none"})},t.prototype.show=function(t){return void 0===t&&(t=!0),this.attr({visibility:t?"inherit":"visible"})},t.prototype["stroke-widthSetter"]=function(t,e,i){this[e]=t,i.setAttribute(e,t)},t.prototype.strokeWidth=function(){if(!this.renderer.styledMode)return this["stroke-width"]||0;var t,e=this.getStyle("stroke-width"),i=0;return/px$/.test(e)?i=iP(e):""!==e&&(il(t=ii.createElementNS(ir,"rect"),{width:e,"stroke-width":0}),this.element.parentNode.appendChild(t),i=t.getBBox().width,t.parentNode.removeChild(t)),i},t.prototype.symbolAttr=function(e){var i=this;t.symbolCustomAttribs.forEach(function(t){i[t]=iT(e[t],i[t])}),i.attr({d:i.renderer.symbols[i.symbolName](i.x,i.y,i.width,i.height,i)})},t.prototype.textSetter=function(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())},t.prototype.titleSetter=function(t){var e=this.element,i=e.getElementsByTagName("title")[0]||ii.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=iC(iT(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")},t.prototype.toFront=function(){var t=this.element;return t.parentNode.appendChild(t),this},t.prototype.translate=function(t,e){return this.attr({translateX:t,translateY:e})},t.prototype.updateTransform=function(t){void 0===t&&(t="transform");var e,i,o,r,n=this.element,s=this.foreignObject,a=this.matrix,h=this.padding,l=this.rotation,d=void 0===l?0:l,c=this.rotationOriginX,p=this.rotationOriginY,u=this.scaleX,f=this.scaleY,g=this.text,v=this.translateX,m=this.translateY,y=["translate("+(void 0===v?0:v)+","+(void 0===m?0:m)+")"];iu(a)&&y.push("matrix("+a.join(",")+")"),!d||(y.push("rotate("+d+" "+(null!==(i=null!==(e=null!=c?c:n.getAttribute("x"))&&void 0!==e?e:this.x)&&void 0!==i?i:0)+" "+(null!==(r=null!==(o=null!=p?p:n.getAttribute("y"))&&void 0!==o?o:this.y)&&void 0!==r?r:0)+")"),(null==g?void 0:g.element.tagName)!=="SPAN"||(null==g?void 0:g.foreignObject)||g.attr({rotation:d,rotationOriginX:(c||0)-h,rotationOriginY:(p||0)-h})),(iu(u)||iu(f))&&y.push("scale("+iT(u,1)+" "+iT(f,1)+")"),y.length&&!(g||this).textPath&&((null==s?void 0:s.element)||n).setAttribute(t,y.join(" "))},t.prototype.visibilitySetter=function(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t},t.prototype.xGetter=function(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)},t.prototype.zIndexSetter=function(t,e){var i,o,r,n,s,a=this.renderer,h=this.parentGroup,l=(h||a).element||a.box,d=this.element,c=l===a.box,p=!1,u=this.added;if(iu(t)?(d.setAttribute("data-z-index",t),t*=1,this[e]===t&&(u=!1)):iu(this[e])&&d.removeAttribute("data-z-index"),this[e]=t,u){for((t=this.zIndex)&&h&&(h.handleZ=!0),s=(i=l.childNodes).length-1;s>=0&&!p;s--)n=!iu(r=(o=i[s]).getAttribute("data-z-index")),o!==d&&(t<0&&n&&!c&&!s?(l.insertBefore(d,i[s]),p=!0):(iP(r)<=t||n&&(!iu(t)||t>=0))&&(l.insertBefore(d,i[s+1]),p=!0));p||(l.insertBefore(d,i[3*!!c]),p=!0)}return p},t.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],t}();iB.prototype.strokeSetter=iB.prototype.fillSetter,iB.prototype.yGetter=iB.prototype.xGetter,iB.prototype.matrixSetter=iB.prototype.rotationOriginXSetter=iB.prototype.rotationOriginYSetter=iB.prototype.rotationSetter=iB.prototype.scaleXSetter=iB.prototype.scaleYSetter=iB.prototype.translateXSetter=iB.prototype.translateYSetter=iB.prototype.verticalAlignSetter=function(t,e){this[e]=t,this.doTransform=!0};var iI=(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),iD=tG.defined,iz=tG.extend,iR=tG.getAlignFactor,iN=tG.isNumber,iW=tG.merge,iG=tG.pick,iX=tG.removeEvent,iF=function(t){function e(i,o,r,n,s,a,h,l,d,c){var p,u=t.call(this,i,"g")||this;return u.paddingLeftSetter=u.paddingSetter,u.paddingRightSetter=u.paddingSetter,u.doUpdate=!1,u.textStr=o,u.x=r,u.y=n,u.anchorX=a,u.anchorY=h,u.baseline=d,u.className=c,u.addClass("button"===c?"highcharts-no-tooltip":"highcharts-label"),c&&u.addClass("highcharts-"+c),u.text=i.text(void 0,0,0,l).attr({zIndex:1}),"string"==typeof s&&((p=/^url\((.*?)\)$/.test(s))||u.renderer.symbols[s])&&(u.symbolKey=s),u.bBox=e.emptyBBox,u.padding=3,u.baselineOffset=0,u.needsBox=i.styledMode||p,u.deferredAttr={},u.alignFactor=0,u}return iI(e,t),e.prototype.alignSetter=function(t){var e=iR(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&iN(this.xSetting)&&this.attr({x:this.xSetting}))},e.prototype.anchorXSetter=function(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)},e.prototype.anchorYSetter=function(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)},e.prototype.boxAttr=function(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e},e.prototype.css=function(t){if(t){var i={};t=iW(t),e.textProps.forEach(function(e){void 0!==t[e]&&(i[e]=t[e],delete t[e])}),this.text.css(i),"fontSize"in i||"fontWeight"in i?this.updateTextPadding():("width"in i||"textOverflow"in i)&&this.updateBoxSize()}return iB.prototype.css.call(this,t)},e.prototype.destroy=function(){iX(this.element,"mouseenter"),iX(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),iB.prototype.destroy.call(this)},e.prototype.fillSetter=function(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)},e.prototype.getBBox=function(t,e){this.textStr&&0===this.bBox.width&&0===this.bBox.height&&this.updateBoxSize();var i=this.padding,o=this.height,r=this.translateX,n=this.translateY,s=this.width,a=iG(this.paddingLeft,i),h=null!=e?e:this.rotation||0,l={width:void 0===s?0:s,height:void 0===o?0:o,x:(void 0===r?0:r)+this.bBox.x-a,y:(void 0===n?0:n)+this.bBox.y-i+this.baselineOffset};return h&&(l=this.getRotatedBox(l,h)),l},e.prototype.getCrispAdjust=function(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2},e.prototype.heightSetter=function(t){this.heightSetting=t,this.doUpdate=!0},e.prototype.afterSetters=function(){t.prototype.afterSetters.call(this),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)},e.prototype.onAdd=function(){this.text.add(this),this.attr({text:iG(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&iD(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})},e.prototype.paddingSetter=function(t,e){iN(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0},e.prototype.rSetter=function(t,e){this.boxAttr(e,t)},e.prototype.strokeSetter=function(t,e){this.stroke=t,this.boxAttr(e,t)},e.prototype["stroke-widthSetter"]=function(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)},e.prototype["text-alignSetter"]=function(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()},e.prototype.textSetter=function(t){void 0!==t&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()},e.prototype.updateBoxSize=function(){var t,i=this.text,o={},r=this.padding,n=this.bBox=(!iN(this.widthSetting)||!iN(this.heightSetting)||this.textAlign)&&iD(i.textStr)?i.getBBox(void 0,0):e.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||n.height||0)+2*r;var s=this.renderer.fontMetrics(i);if(this.baselineOffset=r+Math.min((this.text.firstLineMetrics||s).b,n.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-s.h)/2),this.needsBox&&!i.textPath){if(!this.box){var a=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();a.addClass(("button"===this.className?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),a.add(this)}o.x=t=this.getCrispAdjust(),o.y=(this.baseline?-this.baselineOffset:0)+t,o.width=Math.round(this.width),o.height=Math.round(this.height),this.box.attr(iz(o,this.deferredAttr)),this.deferredAttr={}}},e.prototype.updateTextPadding=function(){var t,e,i=this.text,o=i.styles.textAlign||this.textAlign;if(!i.textPath){this.updateBoxSize();var r=this.baseline?0:this.baselineOffset,n=(null!==(t=this.paddingLeft)&&void 0!==t?t:this.padding)+iR(o)*(null!==(e=this.widthSetting)&&void 0!==e?e:this.bBox.width);(n!==i.x||r!==i.y)&&(i.attr({align:o,x:n}),void 0!==r&&i.attr("y",r)),i.x=n,i.y=r}},e.prototype.widthSetter=function(t){this.widthSetting=iN(t)?t:void 0,this.doUpdate=!0},e.prototype.getPaddedWidth=function(){var t=this.padding,e=iG(this.paddingLeft,t),i=iG(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i},e.prototype.xSetter=function(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)},e.prototype.ySetter=function(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)},e.emptyBBox={width:0,height:0,x:0,y:0},e.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"],e}(iB),iH=tG.defined,ij=tG.isNumber,iY=tG.pick;function i_(t,e,i,o,r){var n=[];if(r){var s=r.start||0,a=r.end||0,h=iY(r.r,i),l=iY(r.r,o||i),d=2e-4/(r.borderRadius?1:Math.max(h,1)),c=Math.abs(a-s-2*Math.PI)<d;c&&(s=Math.PI/2,a=2.5*Math.PI-d);var p=r.innerR,u=iY(r.open,c),f=Math.cos(s),g=Math.sin(s),v=Math.cos(a),m=Math.sin(a),y=iY(r.longArc,a-s-Math.PI<d?0:1),x=["A",h,l,0,y,iY(r.clockwise,1),t+h*v,e+l*m];x.params={start:s,end:a,cx:t,cy:e},n.push(["M",t+h*f,e+l*g],x),iH(p)&&((x=["A",p,p,0,y,iH(r.clockwise)?1-r.clockwise:0,t+p*f,e+p*g]).params={start:a,end:s,cx:t,cy:e},n.push(u?["M",t+p*v,e+p*m]:["L",t+p*v,e+p*m],x)),u||n.push(["Z"])}return n}function iU(t,e,i,o,r){return(null==r?void 0:r.r)?iV(t,e,i,o,r):[["M",t,e],["L",t+i,e],["L",t+i,e+o],["L",t,e+o],["Z"]]}function iV(t,e,i,o,r){var n=(null==r?void 0:r.r)||0;return[["M",t+n,e],["L",t+i-n,e],["A",n,n,0,0,1,t+i,e+n],["L",t+i,e+o-n],["A",n,n,0,0,1,t+i-n,e+o],["L",t+n,e+o],["A",n,n,0,0,1,t,e+o-n],["L",t,e+n],["A",n,n,0,0,1,t+n,e],["Z"]]}var iq={arc:i_,callout:function(t,e,i,o,r){var n=Math.min((null==r?void 0:r.r)||0,i,o),s=n+6,a=null==r?void 0:r.anchorX,h=(null==r?void 0:r.anchorY)||0,l=iV(t,e,i,o,{r:n});if(!ij(a)||a<i&&a>0&&h<o&&h>0)return l;if(t+a>i-s){if(h>e+s&&h<e+o-s)l.splice(3,1,["L",t+i,h-6],["L",t+i+6,h],["L",t+i,h+6],["L",t+i,e+o-n]);else if(a<i){var d=h<e+s,c=d?e:e+o,p=d?2:5;l.splice(p,0,["L",a,h],["L",t+i-n,c])}else l.splice(3,1,["L",t+i,o/2],["L",a,h],["L",t+i,o/2],["L",t+i,e+o-n])}else if(t+a<s){if(h>e+s&&h<e+o-s)l.splice(7,1,["L",t,h+6],["L",t-6,h],["L",t,h-6],["L",t,e+n]);else if(a>0){var d=h<e+s,c=d?e:e+o,p=d?1:6;l.splice(p,0,["L",a,h],["L",t+n,c])}else l.splice(7,1,["L",t,o/2],["L",a,h],["L",t,o/2],["L",t,e+n])}else h>o&&a<i-s?l.splice(5,1,["L",a+6,e+o],["L",a,e+o+6],["L",a-6,e+o],["L",t+n,e+o]):h<0&&a>s&&l.splice(1,1,["L",a-6,e],["L",a,e-6],["L",a+6,e],["L",i-n,e]);return l},circle:function(t,e,i,o){return i_(t+i/2,e+o/2,i/2,o/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o/2],["L",t+i/2,e+o],["L",t,e+o/2],["Z"]]},rect:iU,roundedRect:iV,square:iU,triangle:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o],["L",t,e+o],["Z"]]},"triangle-down":function(t,e,i,o){return[["M",t,e],["L",t+i,e],["L",t+i/2,e+o],["Z"]]}},iZ=tu.doc,iK=tu.SVG_NS,i$=tu.win,iJ=tG.attr,iQ=tG.extend,i0=tG.fireEvent,i1=tG.isString,i2=tG.objectEach,i3=tG.pick,i5=function(t,e){return t.substring(0,e)+"…"},i6=function(){function t(t){var e=t.styles;this.renderer=t.renderer,this.svgElement=t,this.width=t.textWidth,this.textLineHeight=null==e?void 0:e.lineHeight,this.textOutline=null==e?void 0:e.textOutline,this.ellipsis=(null==e?void 0:e.textOverflow)==="ellipsis",this.lineClamp=null==e?void 0:e.lineClamp,this.noWrap=(null==e?void 0:e.whiteSpace)==="nowrap"}return t.prototype.buildSVG=function(){var t=this.svgElement,e=t.element,i=t.renderer,o=i3(t.textStr,"").toString(),r=-1!==o.indexOf("<"),n=e.childNodes,s=!t.added&&i.box,a=[o,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,t.getStyle("font-size"),t.styles.lineClamp,this.width].join(",");if(a!==t.textCache){t.textCache=a,delete t.actualWidth;for(var h=n.length;h--;)e.removeChild(n[h]);if(r||this.ellipsis||this.width||t.textPath||-1!==o.indexOf(" ")&&(!this.noWrap||/<br.*?>/g.test(o))){if(""!==o){s&&s.appendChild(e);var l=new eY(o);this.modifyTree(l.nodes),l.addToDOM(e),this.modifyDOM(),this.ellipsis&&-1!==(e.textContent||"").indexOf("…")&&t.attr("title",this.unescapeEntities(t.textStr||"",["&lt;","&gt;"])),s&&s.removeChild(e)}}else e.appendChild(iZ.createTextNode(this.unescapeEntities(o)));i1(this.textOutline)&&t.applyTextOutline&&t.applyTextOutline(this.textOutline)}},t.prototype.modifyDOM=function(){var t,e=this,i=this.svgElement,o=iJ(i.element,"x");for(i.firstLineMetrics=void 0;t=i.element.firstChild;)if(/^[\s\u200B]*$/.test(t.textContent||" "))i.element.removeChild(t);else break;[].forEach.call(i.element.querySelectorAll("tspan.highcharts-br"),function(t,r){t.nextSibling&&t.previousSibling&&(0===r&&1===t.previousSibling.nodeType&&(i.firstLineMetrics=i.renderer.fontMetrics(t.previousSibling)),iJ(t,{dy:e.getLineHeight(t.nextSibling),x:o}))});var r=this.width||0;if(r){var n=function(t,n){var s,a=t.textContent||"",h=a.replace(/([^\^])-/g,"$1- ").split(" "),l=!e.noWrap&&(h.length>1||i.element.childNodes.length>1),d=e.getLineHeight(n),c=Math.max(0,r-.8*d),p=0,u=i.actualWidth;if(l){for(var f=[],g=[];n.firstChild&&n.firstChild!==t;)g.push(n.firstChild),n.removeChild(n.firstChild);for(;h.length;)if(h.length&&!e.noWrap&&p>0&&(f.push(t.textContent||""),t.textContent=h.join(" ").replace(/- /g,"-")),e.truncate(t,void 0,h,0===p&&u||0,r,c,function(t,e){return h.slice(0,e).join(" ").replace(/- /g,"-")}),u=i.actualWidth,p++,e.lineClamp&&p>=e.lineClamp){h.length&&(e.truncate(t,t.textContent||"",void 0,0,r,c,i5),t.textContent=(null===(s=t.textContent)||void 0===s?void 0:s.replace("…",""))+"…");break}g.forEach(function(e){n.insertBefore(e,t)}),f.forEach(function(e){n.insertBefore(iZ.createTextNode(e),t);var i=iZ.createElementNS(iK,"tspan");i.textContent="​",iJ(i,{dy:d,x:o}),n.insertBefore(i,t)})}else e.ellipsis&&a&&e.truncate(t,a,void 0,0,r,c,i5)},s=function(t){[].slice.call(t.childNodes).forEach(function(e){e.nodeType===i$.Node.TEXT_NODE?n(e,t):(-1!==e.className.baseVal.indexOf("highcharts-br")&&(i.actualWidth=0),s(e))})};s(i.element)}},t.prototype.getLineHeight=function(t){var e=t.nodeType===i$.Node.TEXT_NODE?t.parentElement:t;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(e||this.svgElement.element).h},t.prototype.modifyTree=function(t){var e=this,i=function(o,r){var n=o.attributes,s=void 0===n?{}:n,a=o.children,h=o.style,l=void 0===h?{}:h,d=o.tagName,c=e.renderer.styledMode;if("b"===d||"strong"===d?c?s.class="highcharts-strong":l.fontWeight="bold":("i"===d||"em"===d)&&(c?s.class="highcharts-emphasized":l.fontStyle="italic"),(null==l?void 0:l.color)&&(l.fill=l.color),"br"===d){s.class="highcharts-br",o.textContent="​";var p=t[r+1];(null==p?void 0:p.textContent)&&(p.textContent=p.textContent.replace(/^ +/gm,""))}else"a"===d&&a&&a.some(function(t){return"#text"===t.tagName})&&(o.children=[{children:a,tagName:"tspan"}]);"#text"!==d&&"a"!==d&&(o.tagName="tspan"),iQ(o,{attributes:s,style:l}),a&&a.filter(function(t){return"#text"!==t.tagName}).forEach(i)};t.forEach(i),i0(this.svgElement,"afterModifyTree",{nodes:t})},t.prototype.truncate=function(t,e,i,o,r,n,s){var a,h,l=this.svgElement,d=l.rotation,c=[],p=i&&!o?1:0,u=(e||i||"").length,f=u;i||(r=n);var g=function(e,r){var n=r||e,s=t.parentNode;if(s&&void 0===c[n]&&s.getSubStringLength)try{c[n]=o+s.getSubStringLength(0,i?n+1:n)}catch(t){}return c[n]};if(l.rotation=0,o+(h=g(t.textContent.length))>r){for(;p<=u;)f=Math.ceil((p+u)/2),i&&(a=s(i,f)),h=g(f,a&&a.length-1),p===u?p=u+1:h>r?u=f-1:p=f;0===u?t.textContent="":e&&u===e.length-1||(t.textContent=a||s(e||i,f)),this.ellipsis&&h>r&&this.truncate(t,t.textContent||"",void 0,0,r,n,i5)}i&&i.splice(0,f),l.actualWidth=h,l.rotation=d},t.prototype.unescapeEntities=function(t,e){return i2(this.renderer.escapes,function(i,o){e&&-1!==e.indexOf(i)||(t=t.toString().replace(RegExp(i,"g"),o))}),t},t}(),i9=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},i4=tu.charts,i8=tu.deg2rad,i7=tu.doc,ot=tu.isFirefox,oe=tu.isMS,oi=tu.isWebKit,oo=tu.noop,or=tu.SVG_NS,on=tu.symbolSizes,os=tu.win,oa=tG.addEvent,oh=tG.attr,ol=tG.createElement,od=tG.crisp,oc=tG.css,op=tG.defined,ou=tG.destroyObjectProperties,of=tG.extend,og=tG.isArray,ov=tG.isNumber,om=tG.isObject,oy=tG.isString,ox=tG.merge,ob=tG.pick,oM=tG.pInt,ok=tG.replaceNested,ow=tG.uniqueKey,oS=function(){function t(t,e,i,o,r,n,s){this.x=0,this.y=0;var a,h,l=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),d=l.element;s||l.css(this.getStyle(o||{})),t.appendChild(d),oh(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&oh(d,"xmlns",this.SVG_NS),this.box=d,this.boxWrapper=l,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(i7.createTextNode("Created with Highcharts 12.2.0")),this.defs=this.createElement("defs").add(),this.allowHTML=n,this.forExport=r,this.styledMode=s,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=l.getStyle("font-size"),this.setSize(e,i,!1),ot&&t.getBoundingClientRect&&((a=function(){oc(t,{left:0,top:0}),h=t.getBoundingClientRect(),oc(t,{left:Math.ceil(h.left)-h.left+"px",top:Math.ceil(h.top)-h.top+"px"})})(),this.unSubPixelFix=oa(os,"resize",a))}return t.prototype.definition=function(t){return new eY([t]).addToDOM(this.defs.element)},t.prototype.getReferenceURL=function(){if((ot||oi)&&i7.getElementsByTagName("base").length){if(!op(F)){var t=ow(),e=new eY([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":"url(#".concat(t,")"),fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(i7.body);oc(e,{position:"fixed",top:0,left:0,zIndex:9e5});var i=i7.elementFromPoint(6,6);F=(null==i?void 0:i.id)==="hitme",i7.body.removeChild(e)}if(F)return ok(os.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""},t.prototype.getStyle=function(t){return this.style=of({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style},t.prototype.setStyle=function(t){this.boxWrapper.css(this.getStyle(t))},t.prototype.isHidden=function(){return!this.boxWrapper.getBBox().width},t.prototype.destroy=function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),ou(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null},t.prototype.createElement=function(t){return new this.Element(this,t)},t.prototype.getRadialAttr=function(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}},t.prototype.shadowDefinition=function(t){var e=i9(["highcharts-drop-shadow-".concat(this.chartIndex)],Object.keys(t).map(function(e){return""+e+"-".concat(t[e])}),!0).join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=ox({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector("#".concat(e))||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e},t.prototype.getShadowFilterContent=function(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]},t.prototype.buildText=function(t){new i6(t).buildSVG()},t.prototype.getContrast=function(t){var e=ep.parse(t).rgba,i=" clamp(0,calc(9e9*(0.5 - (0.2126*r + 0.7152*g + 0.0722*b))),1)";if(ov(e[0])||!ep.useColorMix){var o=e.map(function(t){var e=t/255;return e<=.04?e/12.92:Math.pow((e+.055)/1.055,2.4)}),r=.2126*o[0]+.7152*o[1]+.0722*o[2];return 1.05/(r+.05)>(r+.05)/.05?"#FFFFFF":"#000000"}return"color(from "+t+" srgb"+i+i+i+")"},t.prototype.button=function(t,e,i,o,r,n,s,a,h,l){void 0===r&&(r={});var d=this.label(t,e,i,h,void 0,void 0,l,void 0,"button"),c=this.styledMode,p=arguments,u=0;r=ox(ee.global.buttonTheme,r),c&&(delete r.fill,delete r.stroke,delete r["stroke-width"]);var f=r.states||{},g=r.style||{};delete r.states,delete r.style;var v=[eY.filterUserAttributes(r)],m=[g];return c||["hover","select","disabled"].forEach(function(t,e){v.push(ox(v[0],eY.filterUserAttributes(p[e+5]||f[t]||{}))),m.push(v[e+1].style),delete v[e+1].style}),oa(d.element,oe?"mouseover":"mouseenter",function(){3!==u&&d.setState(1)}),oa(d.element,oe?"mouseout":"mouseleave",function(){3!==u&&d.setState(u)}),d.setState=function(t){if(void 0===t&&(t=0),1!==t&&(d.state=u=t),d.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t]),!c){d.attr(v[t]);var e=m[t];om(e)&&d.css(e)}},d.attr(v[0]),!c&&(d.css(of({cursor:"default"},g)),l&&d.text.css({pointerEvents:"none"})),d.on("touchstart",function(t){return t.stopPropagation()}).on("click",function(t){3!==u&&(null==o||o.call(d,t))})},t.prototype.crispLine=function(t,e){var i=t[0],o=t[1];return op(i[1])&&i[1]===o[1]&&(i[1]=o[1]=od(i[1],e)),op(i[2])&&i[2]===o[2]&&(i[2]=o[2]=od(i[2],e)),t},t.prototype.path=function(t){var e=this.styledMode?{}:{fill:"none"};return og(t)?e.d=t:om(t)&&of(e,t),this.createElement("path").attr(e)},t.prototype.circle=function(t,e,i){var o=om(t)?t:void 0===t?{}:{x:t,y:e,r:i},r=this.createElement("circle");return r.xSetter=r.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},r.attr(o)},t.prototype.arc=function(t,e,i,o,r,n){om(t)?(e=(s=t).y,i=s.r,o=s.innerR,r=s.start,n=s.end,t=s.x):s={innerR:o,start:r,end:n};var s,a=this.symbol("arc",t,e,i,i,s);return a.r=i,a},t.prototype.rect=function(t,e,i,o,r,n){var s=om(t)?t:void 0===t?{}:{x:t,y:e,r:r,width:Math.max(i||0,0),height:Math.max(o||0,0)},a=this.createElement("rect");return this.styledMode||(void 0!==n&&(s["stroke-width"]=n,of(s,a.crisp(s))),s.fill="none"),a.rSetter=function(t,e,i){a.r=t,oh(i,{rx:t,ry:t})},a.rGetter=function(){return a.r||0},a.attr(s)},t.prototype.roundedRect=function(t){return this.symbol("roundedRect").attr(t)},t.prototype.setSize=function(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:ob(i,!0)?void 0:0}),this.alignElements()},t.prototype.g=function(t){var e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e},t.prototype.image=function(t,e,i,o,r,n){var s={preserveAspectRatio:"none"};ov(e)&&(s.x=e),ov(i)&&(s.y=i),ov(o)&&(s.width=o),ov(r)&&(s.height=r);var a=this.createElement("image").attr(s),h=function(e){a.attr({href:t}),n.call(a,e)};if(n){a.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});var l=new os.Image;oa(l,"load",h),l.src=t,l.complete&&h({})}else a.attr({href:t});return a},t.prototype.symbol=function(t,e,i,o,r,n){var s,a,h,l,d,c,p=this,u=/^url\((.*?)\)$/,f=u.test(t),g=!f&&(this.symbols[t]?t:"circle"),v=g&&this.symbols[g];if(v)"number"==typeof e&&(l=v.call(this.symbols,e||0,i||0,o||0,r||0,n)),h=this.path(l),p.styledMode||h.attr("fill","none"),of(h,{symbolName:g||void 0,x:e,y:i,width:o,height:r}),n&&of(h,n);else if(f){d=t.match(u)[1];var m=h=this.image(d);m.imgwidth=ob(null==n?void 0:n.width,null===(s=on[d])||void 0===s?void 0:s.width),m.imgheight=ob(null==n?void 0:n.height,null===(a=on[d])||void 0===a?void 0:a.height),c=function(t){return t.attr({width:t.width,height:t.height})},["width","height"].forEach(function(t){m[""+t+"Setter"]=function(t,e){this[e]=t;var i=this.alignByTranslate,o=this.element,r=this.width,s=this.height,a=this.imgwidth,h=this.imgheight,l="width"===e?a:h,d=1;n&&"within"===n.backgroundSize&&r&&s&&a&&h?(d=Math.min(r/a,s/h),oh(o,{width:Math.round(a*d),height:Math.round(h*d)})):o&&l&&o.setAttribute(e,l),!i&&a&&h&&this.translate(((r||0)-a*d)/2,((s||0)-h*d)/2)}}),op(e)&&m.attr({x:e,y:i}),m.isImg=!0,m.symbolUrl=t,op(m.imgwidth)&&op(m.imgheight)?c(m):(m.attr({width:0,height:0}),ol("img",{onload:function(){var t=i4[p.chartIndex];0===this.width&&(oc(this,{position:"absolute",top:"-999em"}),i7.body.appendChild(this)),on[d]={width:this.width,height:this.height},m.imgwidth=this.width,m.imgheight=this.height,m.element&&c(m),this.parentNode&&this.parentNode.removeChild(this),p.imgCount--,p.imgCount||!t||t.hasLoaded||t.onload()},src:d}),this.imgCount++)}return h},t.prototype.clipRect=function(t,e,i,o){return this.rect(t,e,i,o,0)},t.prototype.text=function(t,e,i,o){var r={};if(o&&(this.allowHTML||!this.forExport))return this.html(t,e,i);r.x=Math.round(e||0),i&&(r.y=Math.round(i)),op(t)&&(r.text=t);var n=this.createElement("text").attr(r);return o&&(!this.forExport||this.allowHTML)||(n.xSetter=function(t,e,i){for(var o=i.getElementsByTagName("tspan"),r=i.getAttribute(e),n=0,s=void 0;n<o.length;n++)(s=o[n]).getAttribute(e)===r&&s.setAttribute(e,t);i.setAttribute(e,t)}),n},t.prototype.fontMetrics=function(t){var e=oM(iB.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),o=Math.round(.8*i);return{h:i,b:o,f:e}},t.prototype.rotCorr=function(t,e,i){var o=t;return e&&i&&(o=Math.max(o*Math.cos(e*i8),4)),{x:-t/3*Math.sin(e*i8),y:o}},t.prototype.pathToSegments=function(t){for(var e=[],i=[],o={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2},r=0;r<t.length;r++)oy(i[0])&&ov(t[r])&&i.length===o[i[0].toUpperCase()]&&t.splice(r,0,i[0].replace("M","L").replace("m","l")),"string"==typeof t[r]&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[r]);return e.push(i.slice(0)),e},t.prototype.label=function(t,e,i,o,r,n,s,a,h){return new iF(this,t,e,i,o,r,n,s,a,h)},t.prototype.alignElements=function(){this.alignedObjects.forEach(function(t){return t.align()})},t}();of(oS.prototype,{Element:iB,SVG_NS:or,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:iq,draw:oo}),e6.registerRendererType("svg",oS,!0);var oA=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),oT=function(){return(oT=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},oP=tu.composed,oO=tu.isFirefox,oC=tG.attr,oE=tG.css,oL=tG.createElement,oB=tG.defined,oI=tG.extend,oD=tG.getAlignFactor,oz=tG.isNumber,oR=tG.pInt,oN=tG.pushUnique;function oW(t,e,i){var o,r=(null===(o=this.div)||void 0===o?void 0:o.style)||i.style;iB.prototype[""+e+"Setter"].call(this,t,e,i),r&&(r[e]=t)}var oG=function(t,e){var i;if(!t.div){var o=oC(t.element,"class"),r=t.css,n=oL("div",o?{className:o}:void 0,oT(oT({position:"absolute",left:""+(t.translateX||0)+"px",top:""+(t.translateY||0)+"px"},t.styles),{display:t.display,opacity:t.opacity,visibility:t.visibility}),(null===(i=t.parentGroup)||void 0===i?void 0:i.div)||e);t.classSetter=function(t,e,i){i.setAttribute("class",t),n.className=t},t.translateXSetter=t.translateYSetter=function(e,i){t[i]=e,n.style["translateX"===i?"left":"top"]=""+e+"px",t.doTransform=!0},t.opacitySetter=t.visibilitySetter=oW,t.css=function(e){return r.call(t,e),e.cursor&&(n.style.cursor=e.cursor),e.pointerEvents&&(n.style.pointerEvents=e.pointerEvents),t},t.on=function(){return iB.prototype.on.apply({element:n,onEvents:t.onEvents},arguments),t},t.div=n}return t.div},oX=function(t){function e(i,o){var r=t.call(this,i,o)||this;return e.useForeignObject?r.foreignObject=i.createElement("foreignObject").attr({zIndex:2}):r.css(oT({position:"absolute"},i.styledMode?{}:{fontFamily:i.style.fontFamily,fontSize:i.style.fontSize})),r.element.style.whiteSpace="nowrap",r}return oA(e,t),e.compose=function(t){oN(oP,this.compose)&&(t.prototype.html=function(t,i,o){return new e(this,"span").attr({text:t,x:Math.round(i),y:Math.round(o)})})},e.prototype.getSpanCorrection=function(t,e,i){this.xCorr=-t*i,this.yCorr=-e},e.prototype.css=function(t){var e,i=this.element,o="SPAN"===i.tagName&&t&&"width"in t,r=o&&t.width;return o&&(delete t.width,this.textWidth=oR(r)||void 0,e=!0),(null==t?void 0:t.textOverflow)==="ellipsis"&&(t.overflow="hidden",t.whiteSpace="nowrap"),(null==t?void 0:t.lineClamp)&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),oz(Number(null==t?void 0:t.fontSize))&&(t.fontSize+="px"),oI(this.styles,t),oE(i,t),e&&this.updateTransform(),this},e.prototype.htmlGetBBox=function(){var t=this.element;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}},e.prototype.updateTransform=function(){if(!this.added){this.alignOnAdd=!0;return}var e,i=this.element,o=this.foreignObject,r=this.oldTextWidth,n=this.renderer,s=this.rotation,a=this.rotationOriginX,h=this.rotationOriginY,l=this.scaleX,d=this.scaleY,c=this.styles,p=c.display,u=void 0===p?"inline-block":p,f=c.whiteSpace,g=this.textAlign,v=void 0===g?"left":g,m=this.textWidth,y=this.translateX,x=this.translateY,b=this.x,M=void 0===b?0:b,k=this.y,w=void 0===k?0:k;if(o||oE(i,{marginLeft:""+(void 0===y?0:y)+"px",marginTop:""+(void 0===x?0:x)+"px"}),"SPAN"===i.tagName){var S=[s,v,i.innerHTML,m,this.textAlign].join(","),A=-((null===(e=this.parentGroup)||void 0===e?void 0:e.padding)*1)||0,T=void 0;if(m!==r){var P=this.textPxLength?this.textPxLength:(oE(i,{width:"",whiteSpace:f||"nowrap"}),i.offsetWidth),O=m||0,C=""===i.style.textOverflow&&i.style.webkitLineClamp;(O>r||P>O||C)&&(/[\-\s\u00AD]/.test(i.textContent||i.innerText)||"ellipsis"===i.style.textOverflow)&&(oE(i,{width:(s||l||P>O||C)&&oz(m)?m+"px":"auto",display:u,whiteSpace:f||"normal"}),this.oldTextWidth=m)}o&&(oE(i,{display:"inline-block",verticalAlign:"top"}),o.attr({width:n.width,height:n.height})),S!==this.cTT&&(T=n.fontMetrics(i).b,oB(s)&&!o&&(s!==(this.oldRotation||0)||v!==this.oldAlign)&&oE(i,{transform:"rotate(".concat(s,"deg)"),transformOrigin:""+A+"% "+A+"px"}),this.getSpanCorrection(!oB(s)&&!this.textWidth&&this.textPxLength||i.offsetWidth,T,oD(v)));var E=this.xCorr,L=void 0===E?0:E,B=this.yCorr,I=void 0===B?0:B,D=(null!=a?a:M)-L-M-A,z=(null!=h?h:w)-I-w-A,R={left:""+(M+L)+"px",top:""+(w+I)+"px",textAlign:v,transformOrigin:""+D+"px "+z+"px"};(l||d)&&(R.transform="scale(".concat(null!=l?l:1,",").concat(null!=d?d:1,")")),o?(t.prototype.updateTransform.call(this),oz(M)&&oz(w)?(o.attr({x:M+L,y:w+I,width:i.offsetWidth+3,height:i.offsetHeight,"transform-origin":i.getAttribute("transform-origin")||"0 0"}),oE(i,{display:u,textAlign:v})):oO&&o.attr({width:0,height:0})):oE(i,R),this.cTT=S,this.oldRotation=s,this.oldAlign=v}},e.prototype.add=function(e){var i=this.foreignObject,o=this.renderer,r=o.box.parentNode,n=[];if(i)i.add(e),t.prototype.add.call(this,o.createElement("body").attr({xmlns:"http://www.w3.org/1999/xhtml"}).css({background:"transparent",margin:"0 3px 0 0"}).add(i));else{var s=void 0;if(this.parentGroup=e,e&&!(s=e.div)){for(var a=e;a;)n.push(a),a=a.parentGroup;for(var h=0,l=n.reverse();h<l.length;h++)s=oG(l[h],r)}(s||r).appendChild(this.element)}return this.added=!0,this.alignOnAdd&&this.updateTransform(),this},e.prototype.textSetter=function(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,eY.setElementHTML(this.element,null!=t?t:""),this.textStr=t,this.doTransform=!0)},e.prototype.alignSetter=function(t){this.alignValue=this.textAlign=t,this.doTransform=!0},e.prototype.xSetter=function(t,e){this[e]=t,this.doTransform=!0},e}(iB),oF=oX.prototype;oF.visibilitySetter=oF.opacitySetter=oW,oF.ySetter=oF.rotationSetter=oF.rotationOriginXSetter=oF.rotationOriginYSetter=oF.xSetter,(u=H||(H={})).xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},u.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){return(0,this.axis.chart.numberFormatter)(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0};var oH=H,oj=tG.addEvent,oY=tG.isFunction,o_=tG.objectEach,oU=tG.removeEvent;(j||(j={})).registerEventOptions=function(t,e){t.eventOptions=t.eventOptions||{},o_(e.events,function(e,i){t.eventOptions[i]!==e&&(t.eventOptions[i]&&(oU(t,i,t.eventOptions[i]),delete t.eventOptions[i]),oY(e)&&(t.eventOptions[i]=e,oj(t,i,e,{order:0})))})};var oV=j,oq=tu.deg2rad,oZ=tG.clamp,oK=tG.correctFloat,o$=tG.defined,oJ=tG.destroyObjectProperties,oQ=tG.extend,o0=tG.fireEvent,o1=tG.getAlignFactor,o2=tG.isNumber,o3=tG.merge,o5=tG.objectEach,o6=tG.pick,o9=function(){function t(t,e,i,o,r){this.isNew=!0,this.isNewLabel=!0,this.axis=t,this.pos=e,this.type=i||"",this.parameters=r||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,o0(this,"init"),i||o||this.addLabel()}return t.prototype.addLabel=function(){var t,e,i,o,r=this,n=r.axis,s=n.options,a=n.chart,h=n.categories,l=n.logarithmic,d=n.names,c=r.pos,p=o6(null===(t=r.options)||void 0===t?void 0:t.labels,s.labels),u=n.tickPositions,f=c===u[0],g=c===u[u.length-1],v=(!p.step||1===p.step)&&1===n.tickInterval,m=u.info,y=r.label,x=this.parameters.category||(h?o6(h[c],d[c],c):c);l&&o2(x)&&(x=oK(l.lin2log(x))),n.dateTime&&(m?e=(i=a.time.resolveDTLFormat(s.dateTimeLabelFormats[!s.grid&&m.higherRanks[c]||m.unitName])).main:o2(x)&&(e=n.dateTime.getXDateFormat(x,s.dateTimeLabelFormats||{}))),r.isFirst=f,r.isLast=g;var b={axis:n,chart:a,dateTimeLabelFormat:e,isFirst:f,isLast:g,pos:c,tick:r,tickPositionInfo:m,value:x};o0(this,"labelFormat",b);var M=function(t){return p.formatter?p.formatter.call(t,t):p.format?(t.text=n.defaultLabelFormatter.call(t),e5.format(p.format,t,a)):n.defaultLabelFormatter.call(t)},k=M.call(b,b),w=null==i?void 0:i.list;w?r.shortenLabel=function(){for(o=0;o<w.length;o++)if(oQ(b,{dateTimeLabelFormat:w[o]}),y.attr({text:M.call(b,b)}),y.getBBox().width<n.getSlotWidth(r)-2*(p.padding||0))return;y.attr({text:""})}:r.shortenLabel=void 0,v&&n._addedPlotLB&&r.moveLabel(k,p),o$(y)||r.movedLabel?y&&y.textStr!==k&&!v&&(!y.textWidth||p.style.width||y.styles.width||y.css({width:null}),y.attr({text:k}),y.textPxLength=y.getBBox().width):(r.label=y=r.createLabel(k,p),r.rotation=0)},t.prototype.createLabel=function(t,e,i){var o=this.axis,r=o.chart,n=r.renderer,s=r.styledMode,a=e.style.whiteSpace,h=o$(t)&&e.enabled?n.text(t,null==i?void 0:i.x,null==i?void 0:i.y,e.useHTML).add(o.labelGroup):void 0;return h&&(s||h.css(o3(e.style)),h.textPxLength=h.getBBox().width,!s&&a&&h.css({whiteSpace:a})),h},t.prototype.destroy=function(){oJ(this,this.axis)},t.prototype.getPosition=function(t,e,i,o){var r=this.axis,n=r.chart,s=o&&n.oldChartHeight||n.chartHeight,a={x:t?oK(r.translate(e+i,void 0,void 0,o)+r.transB):r.left+r.offset+(r.opposite?(o&&n.oldChartWidth||n.chartWidth)-r.right-r.left:0),y:t?s-r.bottom+r.offset-(r.opposite?r.height:0):oK(s-r.translate(e+i,void 0,void 0,o)-r.transB)};return a.y=oZ(a.y,-1e9,1e9),o0(this,"afterGetPosition",{pos:a}),a},t.prototype.getLabelPosition=function(t,e,i,o,r,n,s,a){var h,l,d=this.axis,c=d.transA,p=d.isLinked&&d.linkedParent?d.linkedParent.reversed:d.reversed,u=d.staggerLines,f=d.tickRotCorr||{x:0,y:0},g=o||d.reserveSpaceDefault?0:-d.labelOffset*("center"===d.labelAlign?.5:1),v=r.distance,m={};return h=0===d.side?i.rotation?-v:-i.getBBox().height:2===d.side?f.y+v:Math.cos(i.rotation*oq)*(f.y-i.getBBox(!1,0).height/2),o$(r.y)&&(h=0===d.side&&d.horiz?r.y+h:r.y),t=t+o6(r.x,[0,1,0,-1][d.side]*v)+g+f.x-(n&&o?n*c*(p?-1:1):0),e=e+h-(n&&!o?n*c*(p?1:-1):0),u&&(l=s/(a||1)%u,d.opposite&&(l=u-l-1),e+=l*(d.labelOffset/u)),m.x=t,m.y=Math.round(e),o0(this,"afterGetLabelPosition",{pos:m,tickmarkOffset:n,index:s}),m},t.prototype.getLabelSize=function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},t.prototype.getMarkPath=function(t,e,i,o,r,n){return void 0===r&&(r=!1),n.crispLine([["M",t,e],["L",t+(r?0:-i),e+(r?i:0)]],o)},t.prototype.handleOverflow=function(t){var e,i,o=this.axis,r=o.options.labels,n=t.x,s=o.chart.chartWidth,a=o.chart.spacing,h=o6(o.labelLeft,Math.min(o.pos,a[3])),l=o6(o.labelRight,Math.max(o.isRadial?0:o.pos+o.len,s-a[1])),d=this.label,c=this.rotation,p=o1(o.labelAlign||d.attr("align")),u=d.getBBox().width,f=o.getSlotWidth(this),g=f,v=1;c||"justify"!==r.overflow?c<0&&n-p*u<h?i=Math.round(n/Math.cos(c*oq)-h):c>0&&n+p*u>l&&(i=Math.round((s-n)/Math.cos(c*oq))):(n-p*u<h?g=t.x+g*(1-p)-h:n+(1-p)*u>l&&(g=l-t.x+g*p,v=-1),(g=Math.min(f,g))<f&&"center"===o.labelAlign&&(t.x+=v*(f-g-p*(f-Math.min(u,g)))),(u>g||o.autoRotation&&(null===(e=null==d?void 0:d.styles)||void 0===e?void 0:e.width))&&(i=g)),i&&d&&(this.shortenLabel?this.shortenLabel():d.css(oQ({},{width:Math.floor(i)+"px",lineClamp:+!o.isRadial})))},t.prototype.moveLabel=function(t,e){var i,o=this,r=o.label,n=o.axis,s=!1;r&&r.textStr===t?(o.movedLabel=r,s=!0,delete o.label):o5(n.ticks,function(e){s||e.isNew||e===o||!e.label||e.label.textStr!==t||(o.movedLabel=e.label,s=!0,e.labelPos=o.movedLabel.xy,delete e.label)}),!s&&(o.labelPos||r)&&(i=o.labelPos||r.xy,o.movedLabel=o.createLabel(t,e,i),o.movedLabel&&o.movedLabel.attr({opacity:0}))},t.prototype.render=function(t,e,i){var o,r=this.axis,n=r.horiz,s=this.pos,a=o6(this.tickmarkOffset,r.tickmarkOffset),h=this.getPosition(n,s,a,e),l=h.x,d=h.y,c=r.pos,p=c+r.len,u=n?l:d,f=o6(i,null===(o=this.label)||void 0===o?void 0:o.newOpacity,1);!r.chart.polar&&(oK(u)<c||u>p)&&(i=0),null!=i||(i=1),this.isActive=!0,this.renderGridLine(e,i),this.renderMark(h,i),this.renderLabel(h,e,f,t),this.isNew=!1,o0(this,"afterRender")},t.prototype.renderGridLine=function(t,e){var i,o=this.axis,r=o.options,n={},s=this.pos,a=this.type,h=o6(this.tickmarkOffset,o.tickmarkOffset),l=o.chart.renderer,d=this.gridLine,c=r.gridLineWidth,p=r.gridLineColor,u=r.gridLineDashStyle;"minor"===this.type&&(c=r.minorGridLineWidth,p=r.minorGridLineColor,u=r.minorGridLineDashStyle),d||(o.chart.styledMode||(n.stroke=p,n["stroke-width"]=c||0,n.dashstyle=u),a||(n.zIndex=1),t&&(e=0),this.gridLine=d=l.path().attr(n).addClass("highcharts-"+(a?a+"-":"")+"grid-line").add(o.gridGroup)),d&&(i=o.getPlotLinePath({value:s+h,lineWidth:d.strokeWidth(),force:"pass",old:t,acrossPanes:!1}))&&d[t||this.isNew?"attr":"animate"]({d:i,opacity:e})},t.prototype.renderMark=function(t,e){var i=this.axis,o=i.options,r=i.chart.renderer,n=this.type,s=i.tickSize(n?n+"Tick":"tick"),a=t.x,h=t.y,l=o6(o["minor"!==n?"tickWidth":"minorTickWidth"],!n&&i.isXAxis?1:0),d=o["minor"!==n?"tickColor":"minorTickColor"],c=this.mark,p=!c;s&&(i.opposite&&(s[0]=-s[0]),c||(this.mark=c=r.path().addClass("highcharts-"+(n?n+"-":"")+"tick").add(i.axisGroup),i.chart.styledMode||c.attr({stroke:d,"stroke-width":l})),c[p?"attr":"animate"]({d:this.getMarkPath(a,h,s[0],c.strokeWidth(),i.horiz,r),opacity:e}))},t.prototype.renderLabel=function(t,e,i,o){var r=this.axis,n=r.horiz,s=r.options,a=this.label,h=s.labels,l=h.step,d=o6(this.tickmarkOffset,r.tickmarkOffset),c=t.x,p=t.y,u=!0;a&&o2(c)&&(a.xy=t=this.getLabelPosition(c,p,a,n,h,d,o,l),(!this.isFirst||this.isLast||s.showFirstLabel)&&(!this.isLast||this.isFirst||s.showLastLabel)?!n||h.step||h.rotation||e||0===i||this.handleOverflow(t):u=!1,l&&o%l&&(u=!1),u&&o2(t.y)?(t.opacity=i,a[this.isNewLabel?"attr":"animate"](t).show(!0),this.isNewLabel=!1):(a.hide(),this.isNewLabel=!0))},t.prototype.replaceMovedLabel=function(){var t=this.label,e=this.axis;t&&!this.isNew&&(t.animate({opacity:0},void 0,t.destroy),delete this.label),e.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel},t}(),o4=oH.xAxis,o8=oH.yAxis,o7=oV.registerEventOptions,rt=tu.deg2rad,re=tG.arrayMax,ri=tG.arrayMin,ro=tG.clamp,rr=tG.correctFloat,rn=tG.defined,rs=tG.destroyObjectProperties,ra=tG.erase,rh=tG.error,rl=tG.extend,rd=tG.fireEvent,rc=tG.getClosestDistance,rp=tG.insertItem,ru=tG.isArray,rf=tG.isNumber,rg=tG.isString,rv=tG.merge,rm=tG.normalizeTickInterval,ry=tG.objectEach,rx=tG.pick,rb=tG.relativeLength,rM=tG.removeEvent,rk=tG.splat,rw=tG.syncTimeout,rS=function(t,e){return rm(e,void 0,void 0,rx(t.options.allowDecimals,e<.5||void 0!==t.tickAmount),!!t.tickAmount)};rl(ee,{xAxis:o4,yAxis:rv(o4,o8)});var rA=function(){function t(t,e,i){this.init(t,e,i)}return t.prototype.init=function(t,e,i){void 0===i&&(i=this.coll);var o,r,n,s,a="xAxis"===i,h=this.isZAxis||(t.inverted?!a:a);this.chart=t,this.horiz=h,this.isXAxis=a,this.coll=i,rd(this,"init",{userOptions:e}),this.opposite=rx(e.opposite,this.opposite),this.side=rx(e.side,this.side,h?2*!this.opposite:this.opposite?1:3),this.setOptions(e);var l=this.options,d=l.labels;null!==(o=this.type)&&void 0!==o||(this.type=l.type||"linear"),null!==(r=this.uniqueNames)&&void 0!==r||(this.uniqueNames=null===(n=l.uniqueNames)||void 0===n||n),rd(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=rx(l.reversed,this.reversed),this.visible=l.visible,this.zoomEnabled=l.zoomEnabled,this.hasNames="category"===this.type||!0===l.categories,this.categories=ru(l.categories)&&l.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=rn(l.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},null!==(s=this.len)&&void 0!==s||(this.len=0),this.minRange=this.userMinRange=l.minRange||l.maxZoom,this.range=l.range,this.offset=l.offset||0,this.max=void 0,this.min=void 0;var c=rx(l.crosshair,rk(t.options.tooltip.crosshairs)[+!a]);this.crosshair=!0===c?{}:c,-1===t.axes.indexOf(this)&&(a?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),rp(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&a&&!rn(this.reversed)&&(this.reversed=!0),this.labelRotation=rf(d.rotation)?d.rotation:void 0,o7(this,l),rd(this,"afterInit")},t.prototype.setOptions=function(t){var e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=rv(e,"yAxis"===this.coll?{title:{text:this.chart.options.lang.yAxisTitle}}:{},ee[this.coll],t),rd(this,"afterSetOptions",{userOptions:t})},t.prototype.defaultLabelFormatter=function(){var t,e,i=this.axis,o=this.chart.numberFormatter,r=rf(this.value)?this.value:NaN,n=i.chart.time,s=i.categories,a=this.dateTimeLabelFormat,h=ee.lang,l=h.numericSymbols,d=h.numericSymbolMagnitude||1e3,c=i.logarithmic?Math.abs(r):i.tickInterval,p=null==l?void 0:l.length;if(s)e="".concat(this.value);else if(a)e=n.dateFormat(a,r,!0);else if(p&&l&&c>=1e3)for(;p--&&void 0===e;)c>=(t=Math.pow(d,p+1))&&10*r%t==0&&null!==l[p]&&0!==r&&(e=o(r/t,-1)+l[p]);return void 0===e&&(e=Math.abs(r)>=1e4?o(r,-1):o(r,-1,void 0,"")),e},t.prototype.getSeriesExtremes=function(){var t,e=this;rd(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(function(i){if(i.reserveSpace()){var o=i.options,r=void 0,n=o.threshold,s=void 0,a=void 0;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(n||0)&&(n=void 0),e.isXAxis)(r=i.getColumn("x")).length&&(r=e.logarithmic?r.filter(function(t){return t>0}):r,s=(t=i.getXExtremes(r)).min,a=t.max,rf(s)||s instanceof Date||(r=r.filter(rf),s=(t=i.getXExtremes(r)).min,a=t.max),r.length&&(e.dataMin=Math.min(rx(e.dataMin,s),s),e.dataMax=Math.max(rx(e.dataMax,a),a)));else{var h=i.applyExtremes();rf(h.dataMin)&&(s=h.dataMin,e.dataMin=Math.min(rx(e.dataMin,s),s)),rf(h.dataMax)&&(a=h.dataMax,e.dataMax=Math.max(rx(e.dataMax,a),a)),rn(n)&&(e.threshold=n),(!o.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),rd(this,"afterGetSeriesExtremes")},t.prototype.translate=function(t,e,i,o,r,n){var s,a=this.linkedParent||this,h=o&&a.old?a.old.min:a.min;if(!rf(h))return NaN;var l=a.minPixelPadding,d=(a.isOrdinal||(null===(s=a.brokenAxis)||void 0===s?void 0:s.hasBreaks)||a.logarithmic&&r)&&a.lin2val,c=1,p=0,u=o&&a.old?a.old.transA:a.transA,f=0;return u||(u=a.transA),i&&(c*=-1,p=a.len),a.reversed&&(c*=-1,p-=c*(a.sector||a.len)),e?(f=(t=t*c+p-l)/u+h,d&&(f=a.lin2val(f))):(d&&(t=a.val2lin(t)),f=c*(t-h)*u+p+c*l+(rf(n)?u*n:0),a.isRadial||(f=rr(f))),f},t.prototype.toPixels=function(t,e){var i,o;return this.translate(null!==(o=null===(i=this.chart)||void 0===i?void 0:i.time.parse(t))&&void 0!==o?o:NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)},t.prototype.toValue=function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)},t.prototype.getPlotLinePath=function(t){var e,i,o,r,n,s=this,a=s.chart,h=s.left,l=s.top,d=t.old,c=t.value,p=t.lineWidth,u=d&&a.oldChartHeight||a.chartHeight,f=d&&a.oldChartWidth||a.chartWidth,g=s.transB,v=t.translatedValue,m=t.force;function y(t,e,i){return"pass"!==m&&(t<e||t>i)&&(m?t=ro(t,e,i):n=!0),t}var x={value:c,lineWidth:p,old:d,force:m,acrossPanes:t.acrossPanes,translatedValue:v};return rd(this,"getPlotLinePath",x,function(t){e=o=(v=ro(v=rx(v,s.translate(c,void 0,void 0,d)),-1e9,1e9))+g,i=r=u-v-g,rf(v)?s.horiz?(i=l,r=u-s.bottom+(s.options.isInternal?0:a.scrollablePixelsY||0),e=o=y(e,h,h+s.width)):(e=h,o=f-s.right+(a.scrollablePixelsX||0),i=r=y(i,l,l+s.height)):(n=!0,m=!1),t.path=n&&!m?void 0:a.renderer.crispLine([["M",e,i],["L",o,r]],p||1)}),x.path},t.prototype.getLinearTickPositions=function(t,e,i){var o,r,n,s=rr(Math.floor(e/t)*t),a=rr(Math.ceil(i/t)*t),h=[];if(rr(s+t)===s&&(n=20),this.single)return[e];for(o=s;o<=a&&(h.push(o),(o=rr(o+t,n))!==r);)r=o;return h},t.prototype.getMinorTickInterval=function(){var t=this.options,e=t.minorTicks,i=t.minorTickInterval;return!0===e?rx(i,"auto"):!1!==e?i:void 0},t.prototype.getMinorTickPositions=function(){var t,e,i=this.options,o=this.tickPositions,r=this.minorTickInterval,n=this.pointRangePadding||0,s=(this.min||0)-n,a=(this.max||0)+n,h=(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)?this.brokenAxis.unitLength:a-s,l=[];if(h&&h/r<this.len/3){var d=this.logarithmic;if(d)this.paddedTicks.forEach(function(t,e,i){e&&l.push.apply(l,d.getLogTickPositions(r,i[e-1],i[e],!0))});else if(this.dateTime&&"auto"===this.getMinorTickInterval())l=l.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(r),s,a,i.startOfWeek));else for(e=s+(o[0]-s)%r;e<=a&&e!==l[0];e+=r)l.push(e)}return 0!==l.length&&this.trimTicks(l),l},t.prototype.adjustForMinRange=function(){var t,e,i,o,r,n,s,a=this.options,h=this.logarithmic,l=this.chart.time,d=this.max,c=this.min,p=this.minRange;this.isXAxis&&void 0===p&&!h&&(p=rn(a.min)||rn(a.max)||rn(a.floor)||rn(a.ceiling)?null:Math.min(5*(rc(this.series.map(function(t){var e=t.getColumn("x");return t.xIncrement?e.slice(0,2):e}))||0),this.dataMax-this.dataMin)),rf(d)&&rf(c)&&rf(p)&&d-c<p&&(r=this.dataMax-this.dataMin>=p,o=(p-d+c)/2,n=[c-o,null!==(t=l.parse(a.min))&&void 0!==t?t:c-o],r&&(n[2]=h?h.log2lin(this.dataMin):this.dataMin),s=[(c=re(n))+p,null!==(e=l.parse(a.max))&&void 0!==e?e:c+p],r&&(s[2]=h?h.log2lin(this.dataMax):this.dataMax),(d=ri(s))-c<p&&(n[0]=d-p,n[1]=null!==(i=l.parse(a.min))&&void 0!==i?i:d-p,c=re(n))),this.minRange=p,this.min=c,this.max=d},t.prototype.getClosest=function(){var t,e;if(this.categories)e=1;else{var i=[];this.series.forEach(function(t){var o=t.closestPointRange,r=t.getColumn("x");1===r.length?i.push(r[0]):t.sorted&&rn(o)&&t.reserveSpace()&&(e=rn(e)?Math.min(e,o):o)}),i.length&&(i.sort(function(t,e){return t-e}),t=rc([i]))}return t&&e?Math.min(t,e):t||e},t.prototype.nameToX=function(t){var e,i=ru(this.options.categories),o=i?this.categories:this.names,r=t.options.x;return t.series.requireSorting=!1,rn(r)||(r=this.uniqueNames&&o?i?o.indexOf(t.name):rx(o.keys[t.name],-1):t.series.autoIncrement()),-1===r?!i&&o&&(e=o.length):rf(r)&&(e=r),void 0!==e?(this.names[e]=t.name,this.names.keys[t.name]=e):t.x&&(e=t.x),e},t.prototype.updateNames=function(){var t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(function(e){e.xIncrement=null,(!e.points||e.isDirtyData)&&(t.max=Math.max(t.max||0,e.dataTable.rowCount-1),e.processData(),e.generatePoints());var i=e.getColumn("x").slice();e.data.forEach(function(e,o){var r=i[o];(null==e?void 0:e.options)&&void 0!==e.name&&void 0!==(r=t.nameToX(e))&&r!==e.x&&(i[o]=e.x=r)}),e.dataTable.setColumn("x",i)}))},t.prototype.setAxisTranslation=function(){var t,e,i,o=this,r=o.max-o.min,n=o.linkedParent,s=!!o.categories,a=o.isXAxis,h=o.axisPointRange||0,l=0,d=0,c=o.transA;(a||s||h)&&(e=o.getClosest(),n?(l=n.minPointOffset,d=n.pointRangePadding):o.series.forEach(function(t){var i=s?1:a?rx(t.options.pointRange,e,0):o.axisPointRange||0,r=t.options.pointPlacement;if(h=Math.max(h,i),!o.single||s){var n=t.is("xrange")?!a:a;l=Math.max(l,n&&rg(r)?0:i/2),d=Math.max(d,n&&"on"===r?0:i)}}),i=(null===(t=o.ordinal)||void 0===t?void 0:t.slope)&&e?o.ordinal.slope/e:1,o.minPointOffset=l*=i,o.pointRangePadding=d*=i,o.pointRange=Math.min(h,o.single&&s?1:r),a&&(o.closestPointRange=e)),o.translationSlope=o.transA=c=o.staticScale||o.len/(r+d||1),o.transB=o.horiz?o.left:o.bottom,o.minPixelPadding=c*l,rd(this,"afterSetAxisTranslation")},t.prototype.minFromRange=function(){var t=this.max,e=this.min;return rf(t)&&rf(e)&&t-e||void 0},t.prototype.setTickInterval=function(t){var e,i,o,r,n,s,a,h,l,d=this.categories,c=this.chart,p=this.dataMax,u=this.dataMin,f=this.dateTime,g=this.isXAxis,v=this.logarithmic,m=this.options,y=this.softThreshold,x=c.time,b=rf(this.threshold)?this.threshold:void 0,M=this.minRange||0,k=m.ceiling,w=m.floor,S=m.linkedTo,A=m.softMax,T=m.softMin,P=rf(S)&&(null===(e=c[this.coll])||void 0===e?void 0:e[S]),O=m.tickPixelInterval,C=m.maxPadding,E=m.minPadding,L=0,B=rf(m.tickInterval)&&m.tickInterval>=0?m.tickInterval:void 0;if(f||d||P||this.getTickAmount(),h=rx(this.userMin,x.parse(m.min)),l=rx(this.userMax,x.parse(m.max)),P?(this.linkedParent=P,n=P.getExtremes(),this.min=rx(n.min,n.dataMin),this.max=rx(n.max,n.dataMax),this.type!==P.type&&rh(11,!0,c)):(y&&rn(b)&&rf(p)&&rf(u)&&(u>=b?(s=b,E=0):p<=b&&(a=b,C=0)),this.min=rx(h,s,u),this.max=rx(l,a,p)),rf(this.max)&&rf(this.min)&&(v&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,rx(u,this.min))&&rh(10,!0,c),this.min=rr(v.log2lin(this.min),16),this.max=rr(v.log2lin(this.max),16)),this.range&&rf(u)&&(this.userMin=this.min=h=Math.max(u,this.minFromRange()||0),this.userMax=l=this.max,this.range=void 0)),rd(this,"foundExtremes"),this.adjustForMinRange(),rf(this.min)&&rf(this.max)){if(!rf(this.userMin)&&rf(T)&&T<this.min&&(this.min=h=T),!rf(this.userMax)&&rf(A)&&A>this.max&&(this.max=l=A),d||this.axisPointRange||(null===(i=this.stacking)||void 0===i?void 0:i.usePercentage)||P||!(L=this.max-this.min)||(!rn(h)&&E&&(this.min-=L*E),rn(l)||!C||(this.max+=L*C)),!rf(this.userMin)&&rf(w)&&(this.min=Math.max(this.min,w)),!rf(this.userMax)&&rf(k)&&(this.max=Math.min(this.max,k)),y&&rf(u)&&rf(p)){var I=b||0;!rn(h)&&this.min<I&&u>=I?this.min=m.minRange?Math.min(I,this.max-M):I:!rn(l)&&this.max>I&&p<=I&&(this.max=m.minRange?Math.max(I,this.min+M):I)}!c.polar&&this.min>this.max&&(rn(m.min)?this.max=this.min:rn(m.max)&&(this.min=this.max)),L=this.max-this.min}if(this.min!==this.max&&rf(this.min)&&rf(this.max)?P&&!B&&O===P.options.tickPixelInterval?this.tickInterval=B=P.tickInterval:this.tickInterval=rx(B,this.tickAmount?L/Math.max(this.tickAmount-1,1):void 0,d?1:L*O/Math.max(this.len,O)):this.tickInterval=1,g&&!t){var D=this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max);this.series.forEach(function(t){var e;t.forceCrop=null===(e=t.forceCropping)||void 0===e?void 0:e.call(t),t.processData(D)}),rd(this,"postProcessData",{hasExtremesChanged:D})}this.setAxisTranslation(),rd(this,"initialAxisTranslation"),this.pointRange&&!B&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));var z=rx(m.minTickInterval,f&&!this.series.some(function(t){return!t.sorted})?this.closestPointRange:0);!B&&z&&this.tickInterval<z&&(this.tickInterval=z),f||v||B||(this.tickInterval=rS(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()},t.prototype.setTickPositions=function(){var t,e,i,o=this.options,r=o.tickPositions,n=o.tickPositioner,s=this.getMinorTickInterval(),a=!this.isPanning,h=a&&o.startOnTick,l=a&&o.endOnTick,d=[];if(this.tickmarkOffset=this.categories&&"between"===o.tickmarkPlacement&&1===this.tickInterval?.5:0,this.single=this.min===this.max&&rn(this.min)&&!this.tickAmount&&(this.min%1==0||!1!==o.allowDecimals),r)d=r.slice();else if(rf(this.min)&&rf(this.max)){if(!(null===(t=this.ordinal)||void 0===t?void 0:t.positions)&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))d=[this.min,this.max],rh(19,!1,this.chart);else if(this.dateTime)d=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,o.units),this.min,this.max,o.startOfWeek,null===(e=this.ordinal)||void 0===e?void 0:e.positions,this.closestPointRange,!0);else if(this.logarithmic)d=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else for(var c=this.tickInterval,p=c;p<=2*c;)if(d=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&d.length>this.tickAmount)this.tickInterval=rS(this,p*=1.1);else break;d.length>this.len&&(d=[d[0],d[d.length-1]])[0]===d[1]&&(d.length=1),n&&(this.tickPositions=d,(i=n.apply(this,[this.min,this.max]))&&(d=i))}this.tickPositions=d,this.minorTickInterval="auto"===s&&this.tickInterval?this.tickInterval/o.minorTicksPerMajor:s,this.paddedTicks=d.slice(0),this.trimTicks(d,h,l),!this.isLinked&&rf(this.min)&&rf(this.max)&&(this.single&&d.length<2&&!this.categories&&!this.series.some(function(t){return t.is("heatmap")&&"between"===t.options.pointPlacement})&&(this.min-=.5,this.max+=.5),r||i||this.adjustTickAmount()),rd(this,"afterSetTickPositions")},t.prototype.trimTicks=function(t,e,i){var o=t[0],r=t[t.length-1],n=!this.isOrdinal&&this.minPointOffset||0;if(rd(this,"trimTicks"),!this.isLinked||!this.grid){if(e&&o!==-1/0)this.min=o;else for(;this.min-n>t[0];)t.shift();if(i)this.max=r;else for(;this.max+n<t[t.length-1];)t.pop();0===t.length&&rn(o)&&!this.options.tickPositions&&t.push((r+o)/2)}},t.prototype.alignToOthers=function(){var t,e=this,i=e.chart,o=[this],r=e.options,n=i.options.chart,s="yAxis"===this.coll&&n.alignThresholds,a=[];if(e.thresholdAlignment=void 0,(!1!==n.alignTicks&&r.alignTicks||s)&&!1!==r.startOnTick&&!1!==r.endOnTick&&!e.logarithmic){var h=function(t){var e=t.horiz,i=t.options;return[e?i.left:i.top,i.width,i.height,i.pane].join(",")},l=h(this);i[this.coll].forEach(function(i){var r=i.series;r.length&&r.some(function(t){return t.visible})&&i!==e&&h(i)===l&&(t=!0,o.push(i))})}if(t&&s){o.forEach(function(t){var i=t.getThresholdAlignment(e);rf(i)&&a.push(i)});var d=a.length>1?a.reduce(function(t,e){return t+e},0)/a.length:void 0;o.forEach(function(t){t.thresholdAlignment=d})}return t},t.prototype.getThresholdAlignment=function(t){if((!rf(this.dataMin)||this!==t&&this.series.some(function(t){return t.isDirty||t.isDirtyData}))&&this.getSeriesExtremes(),rf(this.threshold)){var e=ro((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(e=1-e),e}},t.prototype.getTickAmount=function(){var t=this.options,e=t.tickPixelInterval,i=t.tickAmount;rn(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i},t.prototype.adjustTickAmount=function(){var t,e,i,o=this,r=o.finalTickAmt,n=o.max,s=o.min,a=o.options,h=o.tickPositions,l=o.tickAmount,d=o.thresholdAlignment,c=null==h?void 0:h.length,p=rx(o.threshold,o.softThreshold?0:null),u=o.tickInterval,f=function(){return h.push(rr(h[h.length-1]+u))},g=function(){return h.unshift(rr(h[0]-u))};if(rf(d)&&(i=d<.5?Math.ceil(d*(l-1)):Math.floor(d*(l-1)),a.reversed&&(i=l-1-i)),o.hasData()&&rf(s)&&rf(n)){var v=function(){o.transA*=(c-1)/(l-1),o.min=a.startOnTick?h[0]:Math.min(s,h[0]),o.max=a.endOnTick?h[h.length-1]:Math.max(n,h[h.length-1])};if(rf(i)&&rf(o.threshold)){for(;h[i]!==p||h.length!==l||h[0]>s||h[h.length-1]<n;){for(h.length=0,h.push(o.threshold);h.length<l;)void 0===h[i]||h[i]>o.threshold?g():f();if(u>8*o.tickInterval)break;u*=2}v()}else if(c<l){for(;h.length<l;)h.length%2||s===p?f():g();v()}if(rn(r)){for(e=t=h.length;e--;)(3===r&&e%2==1||r<=2&&e>0&&e<t-1)&&h.splice(e,1);o.finalTickAmt=void 0}}},t.prototype.setScale=function(){var t,e,i,o,r,n=this.coll,s=this.stacking,a=!1,h=!1;this.series.forEach(function(t){var e;a=a||t.isDirtyData||t.isDirty,h=h||(null===(e=t.xAxis)||void 0===e?void 0:e.isDirty)||!1}),this.setAxisSize();var l=this.len!==(null===(t=this.old)||void 0===t?void 0:t.len);l||a||h||this.isLinked||this.forceRedraw||this.userMin!==(null===(e=this.old)||void 0===e?void 0:e.userMin)||this.userMax!==(null===(i=this.old)||void 0===i?void 0:i.userMax)||this.alignToOthers()?(s&&"yAxis"===n&&s.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),s&&"xAxis"===n&&s.buildStacks(),this.isDirty||(this.isDirty=l||this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(r=this.old)||void 0===r?void 0:r.max))):s&&s.cleanStacks(),a&&delete this.allExtremes,rd(this,"afterSetScale")},t.prototype.setExtremes=function(t,e,i,o,r){var n=this;void 0===i&&(i=!0);var s=this.chart;this.series.forEach(function(t){delete t.kdTree}),rd(this,"setExtremes",r=rl(r,{min:t=s.time.parse(t),max:e=s.time.parse(e)}),function(t){n.userMin=t.min,n.userMax=t.max,n.eventArgs=t,i&&s.redraw(o)})},t.prototype.setAxisSize=function(){var t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],o=this.horiz,r=this.width=Math.round(rb(rx(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),n=this.height=Math.round(rb(rx(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),s=this.top=Math.round(rb(rx(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),a=this.left=Math.round(rb(rx(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-n-s,this.right=t.chartWidth-r-a,this.len=Math.max(o?r:n,0),this.pos=o?a:s},t.prototype.getExtremes=function(){var t=this.logarithmic;return{min:t?rr(t.lin2log(this.min)):this.min,max:t?rr(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},t.prototype.getThreshold=function(t){var e=this.logarithmic,i=e?e.lin2log(this.min):this.min,o=e?e.lin2log(this.max):this.max;return null===t||t===-1/0?t=i:t===1/0?t=o:i>t?t=i:o<t&&(t=o),this.translate(t,0,1,0,1)},t.prototype.autoLabelAlign=function(t){var e=(rx(t,0)-90*this.side+720)%360,i={align:"center"};return rd(this,"autoLabelAlign",i,function(t){e>15&&e<165?t.align="right":e>195&&e<345&&(t.align="left")}),i.align},t.prototype.tickSize=function(t){var e,i=this.options,o=rx(i["tick"===t?"tickWidth":"minorTickWidth"],"tick"===t&&this.isXAxis&&!this.categories?1:0),r=i["tick"===t?"tickLength":"minorTickLength"];o&&r&&("inside"===i[t+"Position"]&&(r=-r),e=[r,o]);var n={tickSize:e};return rd(this,"afterTickSize",n),n.tickSize},t.prototype.labelMetrics=function(){var t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)},t.prototype.unsquish=function(){var t,e,i=this.options.labels,o=i.padding||0,r=this.horiz,n=this.tickInterval,s=this.len/((+!!this.categories+this.max-this.min)/n),a=i.rotation,h=rr(.8*this.labelMetrics().h),l=Math.max(this.max-this.min,0),d=function(t){var e=(t+2*o)/(s||1);return(e=e>1?Math.ceil(e):1)*n>l&&t!==1/0&&s!==1/0&&l&&(e=Math.ceil(l/n)),rr(e*n)},c=n,p=Number.MAX_VALUE;if(r){if(!i.staggerLines&&(rf(a)?e=[a]:s<i.autoRotationLimit&&(e=i.autoRotation)),e)for(var u=void 0,f=void 0,g=0,v=e;g<v.length;g++){var m=v[g];(m===a||m&&m>=-90&&m<=90)&&(f=(u=d(Math.abs(h/Math.sin(rt*m))))+Math.abs(m/360))<p&&(p=f,t=m,c=u)}}else c=d(.75*h);return this.autoRotation=e,this.labelRotation=rx(t,rf(a)?a:0),i.step?n:c},t.prototype.getSlotWidth=function(t){var e=this.chart,i=this.horiz,o=this.options.labels,r=Math.max(this.tickPositions.length-+!this.categories,1),n=e.margin[3];if(t&&rf(t.slotWidth))return t.slotWidth;if(i&&o.step<2&&!this.isRadial)return o.rotation?0:(this.staggerLines||1)*this.len/r;if(!i){var s=o.style.width;if(void 0!==s)return parseInt(String(s),10);if(n)return n-e.spacing[3]}return .33*e.chartWidth},t.prototype.renderUnsquish=function(){var t,e=this.chart,i=e.renderer,o=this.tickPositions,r=this.ticks,n=this.options.labels,s=n.style,a=this.horiz,h=this.getSlotWidth(),l=Math.max(1,Math.round(h-(a?2*(n.padding||0):n.distance||0))),d={},c=this.labelMetrics(),p=s.lineClamp,u=null!=p?p:Math.floor(this.len/(o.length*c.h))||1,f=0;rg(n.rotation)||(d.rotation=n.rotation||0),o.forEach(function(t){var e,i=r[t];i.movedLabel&&i.replaceMovedLabel();var o=(null===(e=i.label)||void 0===e?void 0:e.textPxLength)||0;o>f&&(f=o)}),this.maxLabelLength=f,this.autoRotation?f>l&&f>c.h?d.rotation=this.labelRotation:this.labelRotation=0:h&&(t=l),d.rotation&&(t=f>.5*e.chartHeight?.33*e.chartHeight:f,p||(u=1)),this.labelAlign=n.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(d.align=this.labelAlign),o.forEach(function(e){var i=r[e],o=null==i?void 0:i.label,n=s.width,a={};o&&(o.attr(d),i.shortenLabel?i.shortenLabel():t&&!n&&"nowrap"!==s.whiteSpace&&(t<(o.textPxLength||0)||"SPAN"===o.element.tagName)?o.css(rl(a,{width:""+t+"px",lineClamp:u})):!o.styles.width||a.width||n||o.css({width:"auto"}),i.rotation=d.rotation)},this),this.tickRotCorr=i.rotCorr(c.b,this.labelRotation||0,0!==this.side)},t.prototype.hasData=function(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&rn(this.min)&&rn(this.max)},t.prototype.addTitle=function(t){var e,i=this.chart.renderer,o=this.horiz,r=this.opposite,n=this.options.title,s=this.chart.styledMode;this.axisTitle||((e=n.textAlign)||(e=(o?{low:"left",middle:"center",high:"right"}:{low:r?"right":"left",middle:"center",high:r?"left":"right"})[n.align]),this.axisTitle=i.text(n.text||"",0,0,n.useHTML).attr({zIndex:7,rotation:n.rotation||0,align:e}).addClass("highcharts-axis-title"),s||this.axisTitle.css(rv(n.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),s||n.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)},t.prototype.generateTick=function(t){var e=this.ticks;e[t]?e[t].addLabel():e[t]=new o9(this,t)},t.prototype.createGroups=function(){var t=this,e=this.axisParent,i=this.chart,o=this.coll,r=this.options,n=i.renderer,s=function(i,s,a){return n.g(i).attr({zIndex:a}).addClass("highcharts-".concat(o.toLowerCase()).concat(s," ")+(t.isRadial?"highcharts-radial-axis".concat(s," "):"")+(r.className||"")).add(e)};this.axisGroup||(this.gridGroup=s("grid","-grid",r.gridZIndex),this.axisGroup=s("axis","",r.zIndex),this.labelGroup=s("axis-labels","-labels",r.labels.zIndex))},t.prototype.getOffset=function(){var t,e,i,o,r=this,n=r.chart,s=r.horiz,a=r.options,h=r.side,l=r.ticks,d=r.tickPositions,c=r.coll,p=n.inverted&&!r.isZAxis?[1,0,3,2][h]:h,u=r.hasData(),f=a.title,g=a.labels,v=rf(a.crossing),m=n.axisOffset,y=n.clipOffset,x=[-1,1,1,-1][h],b=0,M=0,k=0;if(r.showAxis=t=u||a.showEmpty,r.staggerLines=r.horiz&&g.staggerLines||void 0,r.createGroups(),u||r.isLinked?(d.forEach(function(t){r.generateTick(t)}),r.renderUnsquish(),r.reserveSpaceDefault=0===h||2===h||({1:"left",3:"right"})[h]===r.labelAlign,rx(g.reserveSpace,!v&&null,"center"===r.labelAlign||null,r.reserveSpaceDefault)&&d.forEach(function(t){k=Math.max(l[t].getLabelSize(),k)}),r.staggerLines&&(k*=r.staggerLines),r.labelOffset=k*(r.opposite?-1:1)):ry(l,function(t,e){t.destroy(),delete l[e]}),(null==f?void 0:f.text)&&!1!==f.enabled&&(r.addTitle(t),t&&!v&&!1!==f.reserveSpace&&(r.titleOffset=b=r.axisTitle.getBBox()[s?"height":"width"],M=rn(e=f.offset)?0:rx(f.margin,s?5:10))),r.renderLine(),r.offset=x*rx(a.offset,m[h]?m[h]+(a.margin||0):0),r.tickRotCorr=r.tickRotCorr||{x:0,y:0},o=0===h?-r.labelMetrics().h:2===h?r.tickRotCorr.y:0,i=Math.abs(k)+M,k&&(i-=o,i+=x*(s?rx(g.y,r.tickRotCorr.y+x*g.distance):rx(g.x,x*g.distance))),r.axisTitleMargin=rx(e,i),r.getMaxLabelDimensions&&(r.maxLabelDimensions=r.getMaxLabelDimensions(l,d)),"colorAxis"!==c&&y){var w=this.tickSize("tick");m[h]=Math.max(m[h],(r.axisTitleMargin||0)+b+x*r.offset,i,(null==d?void 0:d.length)&&w?w[0]+x*r.offset:0);var S=!r.axisLine||a.offset?0:r.axisLine.strokeWidth()/2;y[p]=Math.max(y[p],S)}rd(this,"afterGetOffset")},t.prototype.getLinePath=function(t){var e=this.chart,i=this.opposite,o=this.offset,r=this.horiz,n=this.left+(i?this.width:0)+o,s=e.chartHeight-this.bottom-(i?this.height:0)+o;return i&&(t*=-1),e.renderer.crispLine([["M",r?this.left:n,r?s:this.top],["L",r?e.chartWidth-this.right:n,r?s:e.chartHeight-this.bottom]],t)},t.prototype.renderLine=function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))},t.prototype.getTitlePosition=function(t){var e=this.horiz,i=this.left,o=this.top,r=this.len,n=this.options.title,s=e?i:o,a=this.opposite,h=this.offset,l=n.x,d=n.y,c=this.chart.renderer.fontMetrics(t),p=t?Math.max(t.getBBox(!1,0).height-c.h-1,0):0,u={low:s+(e?0:r),middle:s+r/2,high:s+(e?r:0)}[n.align],f=(e?o+this.height:i)+(e?1:-1)*(a?-1:1)*(this.axisTitleMargin||0)+[-p,p,c.f,-p][this.side],g={x:e?u+l:f+(a?this.width:0)+h+l,y:e?f+d-(a?this.height:0)+h:u+d};return rd(this,"afterGetTitlePosition",{titlePosition:g}),g},t.prototype.renderMinorTick=function(t,e){var i=this.minorTicks;i[t]||(i[t]=new o9(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)},t.prototype.renderTick=function(t,e,i){var o,r=this.isLinked,n=this.ticks;(!r||t>=this.min&&t<=this.max||(null===(o=this.grid)||void 0===o?void 0:o.isColumn))&&(n[t]||(n[t]=new o9(this,t)),i&&n[t].isNew&&n[t].render(e,!0,-1),n[t].render(e))},t.prototype.render=function(){var t,e,i=this,o=i.chart,r=i.logarithmic,n=o.renderer,s=i.options,a=i.isLinked,h=i.tickPositions,l=i.axisTitle,d=i.ticks,c=i.minorTicks,p=i.alternateBands,u=s.stackLabels,f=s.alternateGridColor,g=s.crossing,v=i.tickmarkOffset,m=i.axisLine,y=i.showAxis,x=eT(n.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[d,c,p].forEach(function(t){ry(t,function(t){t.isActive=!1})}),rf(g)){var b=this.isXAxis?o.yAxis[0]:o.xAxis[0],M=[1,-1,-1,1][this.side];if(b){var k=b.toPixels(g,!0);i.horiz&&(k=b.len-k),i.offset=M*k}}if(i.hasData()||a){var w=i.chart.hasRendered&&i.old&&rf(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(t){i.renderMinorTick(t,w)}),h.length&&(h.forEach(function(t,e){i.renderTick(t,e,w)}),v&&(0===i.min||i.single)&&(d[-1]||(d[-1]=new o9(i,-1,null,!0)),d[-1].render(-1))),f&&h.forEach(function(n,s){e=void 0!==h[s+1]?h[s+1]+v:i.max-v,s%2==0&&n<i.max&&e<=i.max+(o.polar?-v:v)&&(p[n]||(p[n]=new tu.PlotLineOrBand(i,{})),t=n+v,p[n].options={from:r?r.lin2log(t):t,to:r?r.lin2log(e):e,color:f,className:"highcharts-alternate-grid"},p[n].render(),p[n].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(s.plotLines||[]).concat(s.plotBands||[]).forEach(function(t){i.addPlotBandOrLine(t)}))}[d,c,p].forEach(function(t){var e=[],i=x.duration;ry(t,function(t,i){t.isActive||(t.render(i,!1,0),t.isActive=!1,e.push(i))}),rw(function(){for(var i=e.length;i--;)t[e[i]]&&!t[e[i]].isActive&&(t[e[i]].destroy(),delete t[e[i]])},t!==p&&o.hasRendered&&i?i:0)}),m&&(m[m.isPlaced?"animate":"attr"]({d:this.getLinePath(m.strokeWidth())}),m.isPlaced=!0,m[y?"show":"hide"](y)),l&&y&&(l[l.isNew?"attr":"animate"](i.getTitlePosition(l)),l.isNew=!1),(null==u?void 0:u.enabled)&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,rd(this,"afterRender")},t.prototype.redraw=function(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})},t.prototype.getKeepProps=function(){return this.keepProps||t.keepProps},t.prototype.destroy=function(t){var e=this,i=e.plotLinesAndBands,o=this.eventOptions;if(rd(this,"destroy",{keepEvents:t}),t||rM(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(t){rs(t)}),i)for(var r=i.length;r--;)i[r].destroy();for(var n in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(t){e[t]&&(e[t]=e[t].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[n]=e.plotLinesAndBandsGroups[n].destroy();ry(e,function(t,i){-1===e.getKeepProps().indexOf(i)&&delete e[i]}),this.eventOptions=o},t.prototype.drawCrosshair=function(t,e){var i,o,r,n,s,a,h=this.crosshair,l=null===(i=null==h?void 0:h.snap)||void 0===i||i,d=this.chart,c=this.cross;if(rd(this,"drawCrosshair",{e:t,point:e}),t||(t=null===(o=this.cross)||void 0===o?void 0:o.e),h&&!1!==(rn(e)||!l)){if(l?rn(e)&&(n=rx("colorAxis"!==this.coll?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):n=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),rn(n)&&(a={value:e&&(this.isXAxis?e.x:rx(e.stackY,e.y)),translatedValue:n},d.polar&&rl(a,{isCrosshair:!0,chartX:null==t?void 0:t.chartX,chartY:null==t?void 0:t.chartY,point:e}),r=this.getPlotLinePath(a)||null),!rn(r)){this.hideCrosshair();return}s=this.categories&&!this.isRadial,c||(this.cross=c=d.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(s?"category ":"thin ")+(h.className||"")).attr({zIndex:rx(h.zIndex,2)}).add(),!d.styledMode&&(c.attr({stroke:h.color||(s?ep.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":rx(h.width,1)}).css({"pointer-events":"none"}),h.dashStyle&&c.attr({dashstyle:h.dashStyle}))),c.show().attr({d:r}),s&&!h.width&&c.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();rd(this,"afterDrawCrosshair",{e:t,point:e})},t.prototype.hideCrosshair=function(){this.cross&&this.cross.hide(),rd(this,"afterHideCrosshair")},t.prototype.update=function(t,e){var i=this.chart;t=rv(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,rx(e,!0)&&i.redraw()},t.prototype.remove=function(t){for(var e=this.chart,i=this.coll,o=this.series,r=o.length;r--;)o[r]&&o[r].remove(!1);ra(e.axes,this),ra(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,rx(t,!0)&&e.redraw()},t.prototype.setTitle=function(t,e){this.update({title:t},e)},t.prototype.setCategories=function(t,e){this.update({categories:t},e)},t.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"],t}(),rT=tG.addEvent,rP=tG.getMagnitude,rO=tG.normalizeTickInterval,rC=tG.timeUnits;!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,rT(t,"afterSetType",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.normalizeTimeTickInterval=function(t,e){var i,o=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],r=o[o.length-1],n=rC[r[0]],s=r[1];for(i=0;i<o.length&&(n=rC[(r=o[i])[0]],s=r[1],!o[i+1]||!(t<=(n*s[s.length-1]+rC[o[i+1][0]])/2));i++);n===rC.year&&t<5*n&&(s=[1,2,5]);var a=rO(t/n,s,"year"===r[0]?Math.max(rP(t/n),1):1);return{unitRange:n,count:a,unitName:r[0]}},t.prototype.getXDateFormat=function(t,e){var i=this.axis,o=i.chart.time;return i.closestPointRange?o.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||o.resolveDTLFormat(e.year).main:o.resolveDTLFormat(e.day).main},t}();t.Additions=o}(Y||(Y={}));var rE=Y,rL=tG.addEvent,rB=tG.normalizeTickInterval,rI=tG.pick;!function(t){function e(){var t;"logarithmic"!==this.type?this.logarithmic=void 0:null!==(t=this.logarithmic)&&void 0!==t||(this.logarithmic=new o(this))}function i(){var t=this.logarithmic;t&&(this.lin2val=function(e){return t.lin2log(e)},this.val2lin=function(e){return t.log2lin(e)})}t.compose=function(t){return t.keepProps.includes("logarithmic")||(t.keepProps.push("logarithmic"),rL(t,"afterSetType",e),rL(t,"afterInit",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.getLogTickPositions=function(t,e,i,o){var r=this.axis,n=r.len,s=r.options,a=[];if(o||(this.minorAutoInterval=void 0),t>=.5)t=Math.round(t),a=r.getLinearTickPositions(t,e,i);else if(t>=.08){var h=Math.floor(e),l=void 0,d=void 0,c=void 0,p=void 0,u=void 0,f=void 0,g=void 0;for(l=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],d=h;d<i+1&&!g;d++)for(c=0,p=l.length;c<p&&!g;c++)(u=this.log2lin(this.lin2log(d)*l[c]))>e&&(!o||f<=i)&&void 0!==f&&a.push(f),f>i&&(g=!0),f=u}else{var v=this.lin2log(e),m=this.lin2log(i),y=o?r.getMinorTickInterval():s.tickInterval,x=s.tickPixelInterval/(o?5:1),b=o?n/r.tickPositions.length:n;t=rB(t=rI("auto"===y?null:y,this.minorAutoInterval,(m-v)*x/(b||1))),a=r.getLinearTickPositions(t,v,m).map(this.log2lin),o||(this.minorAutoInterval=t/5)}return o||(r.tickInterval=t),a},t.prototype.lin2log=function(t){return Math.pow(10,t)},t.prototype.log2lin=function(t){return Math.log(t)/Math.LN10},t}();t.Additions=o}(_||(_={}));var rD=_,rz=tG.erase,rR=tG.extend,rN=tG.isNumber;!function(t){var e;function i(t){return this.addPlotBandOrLine(t,"plotBands")}function o(t,i){var o=this,r=this.userOptions,n=new e(this,t);if(this.visible&&(n=n.render()),n){if(this._addedPlotLB||(this._addedPlotLB=!0,(r.plotLines||[]).concat(r.plotBands||[]).forEach(function(t){o.addPlotBandOrLine(t)})),i){var s=r[i]||[];s.push(t),r[i]=s}this.plotLinesAndBands.push(n)}return n}function r(t){return this.addPlotBandOrLine(t,"plotLines")}function n(t,e,i){i=i||this.options;var o,r,n=this.getPlotLinePath({value:e,force:!0,acrossPanes:i.acrossPanes}),s=[],a=this.horiz,h=!rN(this.min)||!rN(this.max)||t<this.min&&e<this.min||t>this.max&&e>this.max,l=this.getPlotLinePath({value:t,force:!0,acrossPanes:i.acrossPanes}),d=1;if(l&&n)for(h&&(r=l.toString()===n.toString(),d=0),o=0;o<l.length;o+=2){var c=l[o],p=l[o+1],u=n[o],f=n[o+1];("M"===c[0]||"L"===c[0])&&("M"===p[0]||"L"===p[0])&&("M"===u[0]||"L"===u[0])&&("M"===f[0]||"L"===f[0])&&(a&&u[1]===c[1]?(u[1]+=d,f[1]+=d):a||u[2]!==c[2]||(u[2]+=d,f[2]+=d),s.push(["M",c[1],c[2]],["L",p[1],p[2]],["L",f[1],f[2]],["L",u[1],u[2]],["Z"])),s.isFlat=r}return s}function s(t){this.removePlotBandOrLine(t)}function a(t){var e=this.plotLinesAndBands,i=this.options,o=this.userOptions;if(e){for(var r=e.length;r--;)e[r].id===t&&e[r].destroy();[i.plotLines||[],o.plotLines||[],i.plotBands||[],o.plotBands||[]].forEach(function(e){var i;for(r=e.length;r--;)(null===(i=e[r])||void 0===i?void 0:i.id)===t&&rz(e,e[r])})}}function h(t){this.removePlotBandOrLine(t)}t.compose=function(t,l){var d=l.prototype;return d.addPlotBand||(e=t,rR(d,{addPlotBand:i,addPlotLine:r,addPlotBandOrLine:o,getPlotBandPath:n,removePlotBand:s,removePlotLine:h,removePlotBandOrLine:a})),l}}(U||(U={}));var rW=U,rG=function(){return(rG=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},rX=tG.addEvent,rF=tG.arrayMax,rH=tG.arrayMin,rj=tG.defined,rY=tG.destroyObjectProperties,r_=tG.erase,rU=tG.fireEvent,rV=tG.merge,rq=tG.objectEach,rZ=tG.pick,rK=function(){function t(t,e){this.axis=t,this.options=e,this.id=e.id}return t.compose=function(e,i){return rX(e,"afterInit",function(){var t=this;this.labelCollectors.push(function(){for(var e,i=[],o=0,r=t.axes;o<r.length;o++)for(var n=r[o],s=0,a=n.plotLinesAndBands;s<a.length;s++){var h=a[s],l=h.label,d=h.options;!l||(null===(e=null==d?void 0:d.label)||void 0===e?void 0:e.allowOverlap)||i.push(l)}return i})}),rW.compose(t,i)},t.prototype.render=function(){var t=this;rU(this,"render");var e,i,o,r,n=this.axis,s=this.options,a=n.horiz,h=n.logarithmic,l=s.color,d=s.events,c=s.zIndex,p=void 0===c?0:c,u=n.chart,f=u.renderer,g=u.time,v={},m=g.parse(s.to),y=g.parse(s.from),x=g.parse(s.value),b=s.borderWidth,M=s.label,k=this.label,w=this.svgElem,S=[],A=rj(y)&&rj(m),T=rj(x),P=!w,O={class:"highcharts-plot-"+(A?"band ":"line ")+(s.className||"")},C=A?"bands":"lines";if(!n.chart.styledMode&&(T?(O.stroke=l||"#999999",O["stroke-width"]=rZ(s.width,1),s.dashStyle&&(O.dashstyle=s.dashStyle)):A&&(O.fill=l||"#e6e9ff",b&&(O.stroke=s.borderColor,O["stroke-width"]=b))),v.zIndex=p,C+="-"+p,(r=n.plotLinesAndBandsGroups[C])||(n.plotLinesAndBandsGroups[C]=r=f.g("plot-"+C).attr(v).add()),w||(this.svgElem=w=f.path().attr(O).add(r)),rj(x))S=n.getPlotLinePath({value:null!==(e=null==h?void 0:h.log2lin(x))&&void 0!==e?e:x,lineWidth:w.strokeWidth(),acrossPanes:s.acrossPanes});else{if(!(rj(y)&&rj(m)))return;S=n.getPlotBandPath(null!==(i=null==h?void 0:h.log2lin(y))&&void 0!==i?i:y,null!==(o=null==h?void 0:h.log2lin(m))&&void 0!==o?o:m,s)}return!this.eventsAdded&&d&&(rq(d,function(e,i){null==w||w.on(i,function(e){d[i].apply(t,[e])})}),this.eventsAdded=!0),(P||!w.d)&&(null==S?void 0:S.length)?w.attr({d:S}):w&&(S?(w.show(),w.animate({d:S})):w.d&&(w.hide(),k&&(this.label=k=k.destroy()))),M&&(rj(M.text)||rj(M.formatter))&&(null==S?void 0:S.length)&&n.width>0&&n.height>0&&!S.isFlat?(M=rV(rG({align:a&&A?"center":void 0,x:a?!A&&4:10,verticalAlign:!a&&A?"middle":void 0,y:a?A?16:10:A?6:-4,rotation:a&&!A?90:0},A?{inside:!0}:{}),M),this.renderLabel(M,S,A,p)):k&&k.hide(),this},t.prototype.renderLabel=function(t,e,i,o){var r,n=this.axis,s=n.chart.renderer,a=t.inside,h=this.label;h||(this.label=h=s.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:o}),n.chart.styledMode||h.css(rV({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),h.add());var l=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],d=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],c=rH(l),p=rH(d),u=rF(l)-c;h.align(t,!1,{x:c,y:p,width:u,height:rF(d)-p}),h.alignAttr.y-=s.fontMetrics(h).b,(!h.alignValue||"left"===h.alignValue||rj(a))&&h.css({width:((null===(r=t.style)||void 0===r?void 0:r.width)||(i&&a?u:90===h.rotation?n.height-(h.alignAttr.y-n.top):(t.clip?n.width:n.chart.chartWidth)-(h.alignAttr.x-n.left)))+"px"}),h.show(!0)},t.prototype.getLabelText=function(t){return rj(t.formatter)?t.formatter.call(this):t.text},t.prototype.destroy=function(){r_(this.axis.plotLinesAndBands,this),delete this.axis,rY(this)},t}(),r$=e5.format,rJ=tu.composed,rQ=tu.dateFormats,r0=tu.doc,r1=tu.isSafari,r2=it.distribute,r3=tG.addEvent,r5=tG.clamp,r6=tG.css,r9=tG.discardElement,r4=tG.extend,r8=tG.fireEvent,r7=tG.getAlignFactor,nt=tG.isArray,ne=tG.isNumber,ni=tG.isObject,no=tG.isString,nr=tG.merge,nn=tG.pick,ns=tG.pushUnique,na=tG.splat,nh=tG.syncTimeout,nl=function(){function t(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}return t.prototype.bodyFormatter=function(t){return t.map(function(t){var e=t.series.tooltipOptions,i=t.formatPrefix||"point";return(e[i+"Formatter"]||t.tooltipFormatter).call(t,e[i+"Format"]||"")})},t.prototype.cleanSplit=function(t){this.chart.series.forEach(function(e){var i=null==e?void 0:e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})},t.prototype.defaultFormatter=function(t){var e,i=this.points||na(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e},t.prototype.destroy=function(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),r9(this.container)),tG.clearTimeout(this.hideTimer)},t.prototype.getAnchor=function(t,e){var i,o,r=this.chart,n=this.pointer,s=r.inverted,a=r.plotTop,h=r.plotLeft;if((null===(i=(t=na(t))[0].series)||void 0===i?void 0:i.yAxis)&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)void 0===e.chartX&&(e=n.normalize(e)),o=[e.chartX-h,e.chartY-a];else if(t[0].tooltipPos)o=t[0].tooltipPos;else{var l=0,d=0;t.forEach(function(t){var e=t.pos(!0);e&&(l+=e[0],d+=e[1])}),l/=t.length,d/=t.length,this.shared&&t.length>1&&e&&(s?l=e.chartX:d=e.chartY),o=[l-h,d-a]}return o.map(Math.round)},t.prototype.getClassName=function(t,e,i){var o=this.options,r=t.series,n=r.options;return[o.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+nn(t.colorIndex,r.colorIndex),null==n?void 0:n.className].filter(no).join(" ")},t.prototype.getLabel=function(t){var e,i=void 0===t?{anchorX:0,anchorY:0}:t,o=i.anchorX,r=i.anchorY,n=this,s=this.chart.styledMode,a=this.options,h=this.split&&this.allowShared,l=this.container,d=this.chart.renderer;if(this.label){var c=!this.label.hasClass("highcharts-label");(!h&&c||h&&!c)&&this.destroy()}if(!this.label){if(this.outside){var p=this.chart,u=p.options.chart.style,f=e6.getRendererType();this.container=l=tu.doc.createElement("div"),l.className="highcharts-tooltip-container "+(p.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),r6(l,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,((null==u?void 0:u.zIndex)||0)+3)}),this.renderer=d=new f(l,0,0,u,void 0,void 0,d.styledMode)}if(h?this.label=d.g("tooltip"):(this.label=d.label("",o,r,a.shape||"callout",void 0,void 0,a.useHTML,void 0,"tooltip").attr({padding:a.padding,r:a.borderRadius}),s||this.label.attr({fill:a.backgroundColor,"stroke-width":a.borderWidth||0}).css(a.style).css({pointerEvents:a.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),n.outside){var g=this.label;[g.xSetter,g.ySetter].forEach(function(t,e){g[e?"ySetter":"xSetter"]=function(i){t.call(g,n.distance),g[e?"y":"x"]=i,l&&(l.style[e?"top":"left"]=""+i+"px")}})}this.label.attr({zIndex:8}).shadow(null!==(e=a.shadow)&&void 0!==e?e:!a.fixed).add()}return l&&!l.parentElement&&tu.doc.body.appendChild(l),this.label},t.prototype.getPlayingField=function(){var t=r0.body,e=r0.documentElement,i=this.chart,o=this.distance,r=this.outside;return{width:r?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*o-2:i.chartWidth,height:r?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}},t.prototype.getPosition=function(t,e,i){var o,r,n,s=this.distance,a=this.chart,h=this.outside,l=this.pointer,d=a.inverted,c=a.plotLeft,p=a.plotTop,u=a.polar,f=i.plotX,g=void 0===f?0:f,v=i.plotY,m=void 0===v?0:v,y={},x=d&&i.h||0,b=this.getPlayingField(),M=b.height,k=b.width,w=l.getChartPosition(),S=function(t){return t*w.scaleX},A=function(t){return t*w.scaleY},T=function(i){var o="x"===i;return[i,o?k:M,o?t:e].concat(h?[o?S(t):A(e),o?w.left-s+S(g+c):w.top-s+A(m+p),0,o?k:M]:[o?t:e,o?g+c:m+p,o?c:p,o?c+a.plotWidth:p+a.plotHeight])},P=T("y"),O=T("x"),C=!!i.negative;!u&&(null===(r=null===(o=a.hoverSeries)||void 0===o?void 0:o.yAxis)||void 0===r?void 0:r.reversed)&&(C=!C);var E=!this.followPointer&&nn(i.ttBelow,!u&&!d===C),L=function(t,e,i,o,r,n,a){var l=h?"y"===t?A(s):S(s):s,d=(i-o)/2,c=o<r-s,p=r+s+o<e,u=r-l-i+d,f=r+l-d;if(E&&p)y[t]=f;else if(!E&&c)y[t]=u;else if(c)y[t]=Math.min(a-o,u-x<0?u:u-x);else{if(!p)return y[t]=0,!1;y[t]=Math.max(n,f+x+i>e?f:f+x)}},B=function(t,e,i,o,r){if(r<s||r>e-s)return!1;r<i/2?y[t]=1:r>e-o/2?y[t]=e-o-2:y[t]=r-i/2},I=function(t){var e;P=(e=[O,P])[0],O=e[1],n=t},D=function(){!1!==L.apply(0,P)?!1!==B.apply(0,O)||n||(I(!0),D()):n?y.x=y.y=0:(I(!0),D())};return(d&&!u||this.len>1)&&I(),D(),y},t.prototype.getFixedPosition=function(t,e,i){var o,r=i.series,n=this.chart,s=this.options,a=this.split,h=s.position,l=h.relativeTo,d=s.shared||(null===(o=null==r?void 0:r.yAxis)||void 0===o?void 0:o.isRadial)&&("pane"===l||!l)?"plotBox":l,c="chart"===d?n.renderer:n[d]||n.getClipBox(r,!0);return{x:c.x+(c.width-t)*r7(h.align)+h.x,y:c.y+(c.height-e)*r7(h.verticalAlign)+(!a&&h.y||0)}},t.prototype.hide=function(t){var e=this;tG.clearTimeout(this.hideTimer),t=nn(t,this.options.hideDelay),this.isHidden||(this.hideTimer=nh(function(){var i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t?150:t,complete:function(){i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))},t.prototype.init=function(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=nn(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))},t.prototype.shouldStickOnContact=function(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))},t.prototype.move=function(t,e,i,o){var r=this,n=this.followPointer,s=this.options,a=eT(!n&&!this.isHidden&&!s.fixed&&s.animation),h=n||(this.len||0)>1,l={x:t,y:e};h?l.anchorX=l.anchorY=NaN:(l.anchorX=i,l.anchorY=o),a.step=function(){return r.drawTracker()},this.getLabel().animate(l,a)},t.prototype.refresh=function(t,e){var i=this.chart,o=this.options,r=this.pointer,n=this.shared,s=na(t),a=s[0],h=o.format,l=o.formatter||this.defaultFormatter,d=i.styledMode,c=this.allowShared;if(o.enabled&&a.series){tG.clearTimeout(this.hideTimer),this.allowShared=!(!nt(t)&&t.series&&t.series.noSharedTooltip),c=c&&!this.allowShared,this.followPointer=!this.split&&a.series.tooltipOptions.followPointer;var p=this.getAnchor(t,e),u=p[0],f=p[1];n&&this.allowShared&&(r.applyInactiveState(s),s.forEach(function(t){return t.setState("hover")}),a.points=s),this.len=s.length;var g=no(h)?r$(h,a,i):l.call(a,this);a.points=void 0;var v=a.series;if(this.distance=nn(v.tooltipOptions.distance,16),!1===g)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(g,s);else{var m=u,y=f;if(e&&r.isDirectTouch&&(m=e.chartX-i.plotLeft,y=e.chartY-i.plotTop),i.polar||!1===v.options.clip||s.some(function(t){return r.isDirectTouch||t.series.shouldShowTooltip(m,y)})){var x=this.getLabel(c&&this.tt||{});(!o.style.width||d)&&x.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),x.attr({class:this.getClassName(a),text:g&&g.join?g.join(""):g}),this.outside&&x.attr({x:r5(x.x||0,0,this.getPlayingField().width-(x.width||0)-1)}),d||x.attr({stroke:o.borderColor||a.color||v.color||"#666666"}),this.updatePosition({plotX:u,plotY:f,negative:a.negative,ttBelow:a.ttBelow,series:v,h:p[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}r8(this,"refresh")}},t.prototype.renderSplit=function(t,e){var i,o,r=this,n=this,s=n.chart,a=n.chart,h=a.chartWidth,l=a.chartHeight,d=a.plotHeight,c=a.plotLeft,p=a.plotTop,u=a.scrollablePixelsY,f=a.scrollablePixelsX,g=a.styledMode,v=n.distance,m=n.options,y=n.options,x=y.fixed,b=y.position,M=y.positioner,k=n.pointer,w=(null===(i=s.scrollablePlotArea)||void 0===i?void 0:i.scrollingContainer)||{},S=w.scrollLeft,A=void 0===S?0:S,T=w.scrollTop,P=void 0===T?0:T,O=n.outside&&"number"!=typeof f?r0.documentElement.getBoundingClientRect():{left:A,right:A+h,top:P,bottom:P+l},C=n.getLabel(),E=this.renderer||s.renderer,L=!!(null===(o=s.xAxis[0])||void 0===o?void 0:o.opposite),B=k.getChartPosition(),I=B.left,D=B.top,z=M||x,R=p+P,N=0,W=d-(void 0===u?0:u),G=function(t,e,i,o,r){if(void 0===o&&(o=[0,0]),void 0===r&&(r=!0),i.isHeader)a=L?0:W,s=r5(o[0]-t/2,O.left,O.right-t-(n.outside?I:0));else if(x&&i){var s,a,h=n.getFixedPosition(t,e,i);s=h.x,a=h.y-R}else a=o[1]-R,s=r5(s=r?o[0]-t-v:o[0]+v,r?s:O.left,O.right);return{x:s,y:a}};no(t)&&(t=[!1,t]);var X=t.slice(0,e.length+1).reduce(function(t,i,o){if(!1!==i&&""!==i){var r=e[o-1]||{isHeader:!0,plotX:e[0].plotX,plotY:d,series:{}},s=r.isHeader,a=s?n:r.series,h=a.tt=function(t,e,i){var o,r=t,s=e.isHeader,a=e.series,h=a.tooltipOptions||m;if(!r){var l={padding:h.padding,r:h.borderRadius};g||(l.fill=h.backgroundColor,l["stroke-width"]=null!==(o=h.borderWidth)&&void 0!==o?o:x&&!s?0:1),r=E.label("",0,0,h[s?"headerShape":"shape"]||(x&&!s?"rect":"callout"),void 0,void 0,h.useHTML).addClass(n.getClassName(e,!0,s)).attr(l).add(C)}return r.isActive=!0,r.attr({text:i}),g||r.css(h.style).attr({stroke:h.borderColor||e.color||a.color||"#333333"}),r}(a.tt,r,i.toString()),l=h.getBBox(),u=l.width+h.strokeWidth();s&&(N=l.height,W+=N,L&&(R-=N));var f=function(t){var e,i,o=t.isHeader,r=t.plotX,n=void 0===r?0:r,s=t.plotY,a=void 0===s?0:s,h=t.series;if(o)e=Math.max(c+n,c),i=p+d/2;else{var l=h.xAxis,u=h.yAxis;e=l.pos+r5(n,-v,l.len+v),h.shouldShowTooltip(0,u.pos-p+a,{ignoreX:!0})&&(i=u.pos+a)}return{anchorX:e=r5(e,O.left-v,O.right+v),anchorY:i}}(r),y=f.anchorX,b=f.anchorY;if("number"==typeof b){var k=l.height+1,w=(M||G).call(n,u,k,r,[y,b]);t.push({align:z?0:void 0,anchorX:y,anchorY:b,boxWidth:u,point:r,rank:nn(w.rank,+!!s),size:k,target:w.y,tt:h,x:w.x})}else h.isActive=!1}return t},[]);!z&&X.some(function(t){var e=(n.outside?I:0)+t.anchorX;return e<O.left&&e+t.boxWidth<O.right||e<I-O.left+t.boxWidth&&O.right-e>e})&&(X=X.map(function(t){var e=G.call(r,t.boxWidth,t.size,t.point,[t.anchorX,t.anchorY],!1),i=e.x;return r4(t,{target:e.y,x:i})})),n.cleanSplit(),r2(X,W);var F={left:I,right:I};X.forEach(function(t){var e=t.x,i=t.boxWidth,o=t.isHeader;!o&&(n.outside&&I+e<F.left&&(F.left=I+e),!o&&n.outside&&F.left+i>F.right&&(F.right=I+e))}),X.forEach(function(t){var e=t.x,i=t.anchorX,o=t.anchorY,r=t.pos,s=t.point.isHeader,a={visibility:void 0===r?"hidden":"inherit",x:e,y:(r||0)+R+(x&&b.y||0),anchorX:i,anchorY:o};if(n.outside&&e<i){var h=I-F.left;h>0&&(s||(a.x=e+h,a.anchorX=i+h),s&&(a.x=(F.right-F.left)/2,a.anchorX=i+h))}t.tt.attr(a)});var H=n.container,j=n.outside,Y=n.renderer;if(j&&H&&Y){var _=C.getBBox(),U=_.width,V=_.height,q=_.x,Z=_.y;Y.setSize(U+q,V+Z,!1),H.style.left=F.left+"px",H.style.top=D+"px"}r1&&C.attr({opacity:1===C.opacity?.999:1})},t.prototype.drawTracker=function(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}var t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(e&&i){var o={x:0,y:0,width:0,height:0},r=this.getAnchor(i),n=e.getBBox();r[0]+=t.plotLeft-(e.translateX||0),r[1]+=t.plotTop-(e.translateY||0),o.x=Math.min(0,r[0]),o.y=Math.min(0,r[1]),o.width=r[0]<0?Math.max(Math.abs(r[0]),n.width-r[0]):Math.max(Math.abs(r[0]),n.width),o.height=r[1]<0?Math.max(Math.abs(r[1]),n.height-Math.abs(r[1])):Math.max(Math.abs(r[1]),n.height),this.tracker?this.tracker.attr(o):(this.tracker=e.renderer.rect(o).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}},t.prototype.styledModeFormat=function(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')},t.prototype.headerFooterFormatter=function(t,e){var i=t.series,o=i.tooltipOptions,r=i.xAxis,n=null==r?void 0:r.dateTime,s={isFooter:e,point:t},a=o.xDateFormat||"",h=o[e?"footerFormat":"headerFormat"];return r8(this,"headerFormatter",s,function(e){if(n&&!a&&ne(t.key)&&(a=n.getXDateFormat(t.key,o.dateTimeLabelFormats)),n&&a){if(ni(a)){var r=a;rQ[0]=function(t){return i.chart.time.dateFormat(r,t)},a="%0"}(t.tooltipDateKeys||["key"]).forEach(function(t){h=h.replace(RegExp("point\\."+t+"([ \\)}])"),"(point.".concat(t,":").concat(a,")$1"))})}i.chart.styledMode&&(h=this.styledModeFormat(h)),e.text=r$(h,t,this.chart)}),s.text||""},t.prototype.update=function(t){this.destroy(),this.init(this.chart,nr(!0,this.options,t))},t.prototype.updatePosition=function(t){var e,i,o=this.chart,r=this.container,n=this.distance,s=this.options,a=this.pointer,h=this.renderer,l=this.getLabel(),d=l.height,c=void 0===d?0:d,p=l.width,u=void 0===p?0:p,f=s.fixed,g=s.positioner,v=a.getChartPosition(),m=v.left,y=v.top,x=v.scaleX,b=v.scaleY,M=(g||f&&this.getFixedPosition||this.getPosition).call(this,u,c,t),k=tu.doc,w=(t.plotX||0)+o.plotLeft,S=(t.plotY||0)+o.plotTop;if(h&&r){if(g||f){var A=(null===(e=o.scrollablePlotArea)||void 0===e?void 0:e.scrollingContainer)||{},T=A.scrollLeft,P=A.scrollTop;M.x+=(void 0===T?0:T)+m-n,M.y+=(void 0===P?0:P)+y-n}i=(s.borderWidth||0)+2*n+2,h.setSize(r5(u+i,0,k.documentElement.clientWidth)-1,c+i,!1),(1!==x||1!==b)&&(r6(r,{transform:"scale(".concat(x,", ").concat(b,")")}),w*=x,S*=b),w+=m-M.x,S+=y-M.y}this.move(Math.round(M.x),Math.round(M.y||0),w,S)},t}();(f=nl||(nl={})).compose=function(t){ns(rJ,"Core.Tooltip")&&r3(t,"afterInit",function(){var t=this.chart;t.options.tooltip&&(t.tooltip=new f(t,t.options.tooltip,this))})};var nd=nl,nc=e5.format,np=tG.addEvent,nu=tG.crisp,nf=tG.erase,ng=tG.extend,nv=tG.fireEvent,nm=tG.getNestedProperty,ny=tG.isArray,nx=tG.isFunction,nb=tG.isNumber,nM=tG.isObject,nk=tG.merge,nw=tG.pick,nS=tG.syncTimeout,nA=tG.removeEvent,nT=tG.uniqueKey,nP=function(){function t(t,e,i){var o,r;this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),null!==(o=this.id)&&void 0!==o||(this.id=nT()),this.resolveColor(),null!==(r=this.dataLabelOnNull)&&void 0!==r||(this.dataLabelOnNull=t.options.nullInteraction),t.chart.pointCount++,nv(this,"afterInit")}return t.prototype.animateBeforeDestroy=function(){var t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(i){t[i]=t[i].animate("dataLabel"===i?{x:t[i].startXPos,y:t[i].startYPos,opacity:0}:e)}),i.plural.forEach(function(e){t[e].forEach(function(e){e.element&&e.animate(ng({x:t.startXPos},e.startYPos?{x:e.startXPos,y:e.startYPos}:{}))})})},t.prototype.applyOptions=function(e,i){var o=this.series,r=o.options.pointValKey||o.pointValKey;return ng(this,e=t.prototype.optionsToObject.call(this,e)),this.options=this.options?ng(this.options,e):e,e.group&&delete this.group,e.dataLabels&&delete this.dataLabels,r&&(this.y=t.prototype.getNestedProperty.call(this,r)),this.selected&&(this.state="select"),"name"in this&&void 0===i&&o.xAxis&&o.xAxis.hasNames&&(this.x=o.xAxis.nameToX(this)),void 0===this.x&&o?this.x=null!=i?i:o.autoIncrement():nb(e.x)&&o.options.relativeXValue?this.x=o.autoIncrement(e.x):"string"==typeof this.x&&(null!=i||(i=o.chart.time.parse(this.x)),nb(i)&&(this.x=i)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this},t.prototype.destroy=function(){if(!this.destroyed){var t=this,e=t.series,i=e.chart,o=e.options.dataSorting,r=i.hoverPoints,n=eT(t.series.chart.renderer.globalAnimation),s=function(){for(var e in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(nA(t),t.destroyElements()),t)delete t[e]};t.legendItem&&i.legend.destroyItem(t),r&&(t.setState(),nf(r,t),r.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),(null==o?void 0:o.enabled)?(this.animateBeforeDestroy(),nS(s,n.duration)):s(),i.pointCount--}this.destroyed=!0},t.prototype.destroyElements=function(t){var e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(t){e[t]=e[t].destroy()}),i.plural.forEach(function(t){e[t].forEach(function(t){(null==t?void 0:t.element)&&t.destroy()}),delete e[t]})},t.prototype.firePointEvent=function(t,e,i){var o=this,r=this.series.options;o.manageEvent(t),"click"===t&&r.allowPointSelect&&(i=function(t){!o.destroyed&&o.select&&o.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),nv(o,t,e,i)},t.prototype.getClassName=function(){var t;return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+((null===(t=this.zone)||void 0===t?void 0:t.className)?" "+this.zone.className.replace("highcharts-negative",""):"")},t.prototype.getGraphicalProps=function(t){var e,i,o=this,r=[],n={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&r.push("graphic","connector"),t.dataLabel&&r.push("dataLabel","dataLabelPath","dataLabelUpper"),i=r.length;i--;)o[e=r[i]]&&n.singular.push(e);return["graphic","dataLabel"].forEach(function(e){var i=e+"s";t[e]&&o[i]&&n.plural.push(i)}),n},t.prototype.getNestedProperty=function(t){return t?0===t.indexOf("custom.")?nm(t,this.options):this[t]:void 0},t.prototype.getZone=function(){var t,e=this.series,i=e.zones,o=e.zoneAxis||"y",r=0;for(t=i[0];this[o]>=t.value;)t=i[++r];return this.nonZonedColor||(this.nonZonedColor=this.color),(null==t?void 0:t.color)&&!this.options.color?this.color=t.color:this.color=this.nonZonedColor,t},t.prototype.hasNewShapeType=function(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType},t.prototype.isValid=function(){return(nb(this.x)||this.x instanceof Date)&&nb(this.y)},t.prototype.optionsToObject=function(e){var i,o,r=this.series,n=r.options.keys,s=n||r.pointArrayMap||["y"],a=s.length,h={},l=0,d=0;if(nb(e)||null===e)h[s[0]]=e;else if(ny(e))for(!n&&e.length>a&&("string"==(o=typeof e[0])?(null===(i=r.xAxis)||void 0===i?void 0:i.dateTime)?h.x=r.chart.time.parse(e[0]):h.name=e[0]:"number"===o&&(h.x=e[0]),l++);d<a;)n&&void 0===e[l]||(s[d].indexOf(".")>0?t.prototype.setNestedProperty(h,e[l],s[d]):h[s[d]]=e[l]),l++,d++;else"object"==typeof e&&(h=e,e.dataLabels&&(r.hasDataLabels=function(){return!0}),e.marker&&(r._hasPointMarkers=!0));return h},t.prototype.pos=function(t,e){if(void 0===e&&(e=this.plotY),!this.destroyed){var i=this.plotX,o=this.series,r=o.chart,n=o.xAxis,s=o.yAxis,a=0,h=0;if(nb(i)&&nb(e))return t&&(a=n?n.pos:r.plotLeft,h=s?s.pos:r.plotTop),r.inverted&&n&&s?[s.len-e+h,n.len-i+a]:[i+a,e+h]}},t.prototype.resolveColor=function(){var t,e,i,o=this.series,r=o.chart.options.chart,n=o.chart.styledMode,s=r.colorCount;delete this.nonZonedColor,o.options.colorByPoint?(n||(t=(e=o.options.colors||o.chart.options.colors)[o.colorCounter],s=e.length),i=o.colorCounter,o.colorCounter++,o.colorCounter===s&&(o.colorCounter=0)):(n||(t=o.color),i=o.colorIndex),this.colorIndex=nw(this.options.colorIndex,i),this.color=nw(this.options.color,t)},t.prototype.setNestedProperty=function(t,e,i){return i.split(".").reduce(function(t,i,o,r){var n=r.length-1===o;return t[i]=n?e:nM(t[i],!0)?t[i]:{},t[i]},t),t},t.prototype.shouldDraw=function(){return!this.isNull},t.prototype.tooltipFormatter=function(t){var e,i=this.series,o=i.chart,r=i.pointArrayMap,n=i.tooltipOptions,s=n.valueDecimals,a=void 0===s?"":s,h=n.valuePrefix,l=void 0===h?"":h,d=n.valueSuffix,c=void 0===d?"":d;return o.styledMode&&(t=(null===(e=o.tooltip)||void 0===e?void 0:e.styledModeFormat(t))||t),(void 0===r?["y"]:r).forEach(function(e){e="{point."+e,(l||c)&&(t=t.replace(RegExp(e+"}","g"),l+e+"}"+c)),t=t.replace(RegExp(e+"}","g"),e+":,."+a+"f}")}),nc(t,this,o)},t.prototype.update=function(t,e,i,o){var r,n=this,s=n.series,a=n.graphic,h=s.chart,l=s.options;function d(){n.applyOptions(t);var o=a&&n.hasMockGraphic,d=null===n.y?!o:o;a&&d&&(n.graphic=a.destroy(),delete n.hasMockGraphic),nM(t,!0)&&((null==a?void 0:a.element)&&t&&t.marker&&void 0!==t.marker.symbol&&(n.graphic=a.destroy()),(null==t?void 0:t.dataLabels)&&n.dataLabel&&(n.dataLabel=n.dataLabel.destroy())),r=n.index;for(var c={},p=0,u=s.dataColumnKeys();p<u.length;p++){var f=u[p];c[f]=n[f]}s.dataTable.setRow(c,r),l.data[r]=nM(l.data[r],!0)||nM(t,!0)?n.options:nw(t,l.data[r]),s.isDirty=s.isDirtyData=!0,!s.fixedBox&&s.hasCartesianSeries&&(h.isDirtyBox=!0),"point"===l.legendType&&(h.isDirtyLegend=!0),e&&h.redraw(i)}e=nw(e,!0),!1===o?d():n.firePointEvent("update",{options:t},d)},t.prototype.remove=function(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)},t.prototype.select=function(t,e){var i=this,o=i.series,r=o.chart;t=nw(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,o.options.data[o.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||r.getSelectedPoints().forEach(function(t){var e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(r.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})}),delete this.selectedStaging},t.prototype.onMouseOver=function(t){var e=this.series.chart,i=e.inverted,o=e.pointer;o&&(t=t?o.normalize(t):o.getChartCoordinatesFromPoint(this,i),o.runPointActions(t,this))},t.prototype.onMouseOut=function(){var t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null},t.prototype.manageEvent=function(t){var e,i,o,r,n,s,a,h=null===(e=nk(this.series.options.point,this.options).events)||void 0===e?void 0:e[t];!nx(h)||(null===(i=this.hcEvents)||void 0===i?void 0:i[t])&&(null===(r=null===(o=this.hcEvents)||void 0===o?void 0:o[t])||void 0===r?void 0:r.map(function(t){return t.fn}).indexOf(h))!==-1?this.importedUserEvent&&!h&&(null===(s=this.hcEvents)||void 0===s?void 0:s[t])&&(null===(a=this.hcEvents)||void 0===a?void 0:a[t].userEvent)&&(nA(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent):(null===(n=this.importedUserEvent)||void 0===n||n.call(this),this.importedUserEvent=np(this,t,h),this.hcEvents&&(this.hcEvents[t].userEvent=!0))},t.prototype.setState=function(t,e){var i,o,r,n,s,a,h=this.series,l=this.state,d=h.options.states[t||"normal"]||{},c=ee.plotOptions[h.type].marker&&h.options.marker,p=c&&!1===c.enabled,u=(null===(i=null==c?void 0:c.states)||void 0===i?void 0:i[t||"normal"])||{},f=!1===u.enabled,g=this.marker||{},v=h.chart,m=c&&h.markerAttribs,y=h.halo,x=h.stateMarkerGraphic;if(((t=t||"")!==this.state||e)&&(!this.selected||"select"===t)&&!1!==d.enabled&&(!t||!f&&(!p||!1!==u.enabled))&&(!t||!g.states||!g.states[t]||!1!==g.states[t].enabled)){if(this.state=t,m&&(r=h.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(l&&this.graphic.removeClass("highcharts-point-"+l),t&&this.graphic.addClass("highcharts-point-"+t),!v.styledMode){n=h.pointAttribs(this,t),s=nw(v.options.chart.animation,d.animation);var b=n.opacity;h.options.inactiveOtherPoints&&nb(b)&&(this.dataLabels||[]).forEach(function(t){t&&!t.hasClass("highcharts-data-label-hidden")&&(t.animate({opacity:b},s),t.connector&&t.connector.animate({opacity:b},s))}),this.graphic.animate(n,s)}r&&this.graphic.animate(r,nw(v.options.chart.animation,u.animation,c.animation)),x&&x.hide()}else t&&u&&(a=g.symbol||h.symbol,x&&x.currentSymbol!==a&&(x=x.destroy()),r&&(x?x[e?"animate":"attr"]({x:r.x,y:r.y}):a&&(h.stateMarkerGraphic=x=v.renderer.symbol(a,r.x,r.y,r.width,r.height,nk(c,u)).add(h.markerGroup),x.currentSymbol=a)),!v.styledMode&&x&&"inactive"!==this.state&&x.attr(h.pointAttribs(this,t))),x&&(x[t&&this.isInside?"show":"hide"](),x.element.point=this,x.addClass(this.getClassName(),!0));var M=d.halo,k=this.graphic||x,w=(null==k?void 0:k.visibility)||"inherit";(null==M?void 0:M.size)&&k&&"hidden"!==w&&!this.isCluster?(y||(h.halo=y=v.renderer.path().add(k.parentGroup)),y.show()[e?"animate":"attr"]({d:this.haloPath(M.size)}),y.attr({class:"highcharts-halo highcharts-color-"+nw(this.colorIndex,h.colorIndex)+(this.className?" "+this.className:""),visibility:w,zIndex:-1}),y.point=this,v.styledMode||y.attr(ng({fill:this.color||h.color,"fill-opacity":M.opacity},eY.filterUserAttributes(M.attributes||{})))):(null===(o=null==y?void 0:y.point)||void 0===o?void 0:o.haloPath)&&!y.point.destroyed&&y.animate({d:y.point.haloPath(0)},null,y.hide),nv(this,"afterSetState",{state:t})}},t.prototype.haloPath=function(t){var e=this.pos();return e?this.series.chart.renderer.symbols.circle(nu(e[0],1)-t,e[1]-t,2*t,2*t):[]},t}(),nO=function(){return(nO=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},nC=ep.parse,nE=tu.charts,nL=tu.composed,nB=tu.isTouchDevice,nI=tG.addEvent,nD=tG.attr,nz=tG.css,nR=tG.extend,nN=tG.find,nW=tG.fireEvent,nG=tG.isNumber,nX=tG.isObject,nF=tG.objectEach,nH=tG.offset,nj=tG.pick,nY=tG.pushUnique,n_=tG.splat,nU=function(){function t(t,e){var i;this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!(null===(i=e.chart.events)||void 0===i?void 0:i.click),this.pinchDown=[],this.setDOMEvents(),nW(this,"afterInit")}return t.prototype.applyInactiveState=function(t){var e=this;void 0===t&&(t=[]);var i=[];t.forEach(function(t){var o=t.series;i.push(o),o.linkedParent&&i.push(o.linkedParent),o.linkedSeries&&i.push.apply(i,o.linkedSeries),o.navigatorSeries&&i.push(o.navigatorSeries),o.boosted&&o.markerGroup&&i.push.apply(i,e.chart.series.filter(function(t){return t.markerGroup===o.markerGroup}))}),this.chart.series.forEach(function(t){-1===i.indexOf(t)?t.setState("inactive",!0):t.options.inactiveOtherPoints&&t.setAllPointsToState("inactive")})},t.prototype.destroy=function(){var e=this;this.eventsToUnbind.forEach(function(t){return t()}),this.eventsToUnbind=[],!tu.chartCount&&(t.unbindDocumentMouseUp.forEach(function(t){return t.unbind()}),t.unbindDocumentMouseUp.length=0,t.unbindDocumentTouchEnd&&(t.unbindDocumentTouchEnd=t.unbindDocumentTouchEnd())),clearInterval(e.tooltipTimeout),nF(e,function(t,i){e[i]=void 0})},t.prototype.getSelectionMarkerAttrs=function(t,e){var i=this,o={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return nW(this,"getSelectionMarkerAttrs",o,function(o){var r,n=i.chart,s=i.zoomHor,a=i.zoomVert,h=n.mouseDownX,l=void 0===h?0:h,d=n.mouseDownY,c=void 0===d?0:d,p=o.attrs;p.x=n.plotLeft,p.y=n.plotTop,p.width=s?1:n.plotWidth,p.height=a?1:n.plotHeight,s&&(p.width=Math.max(1,Math.abs(r=t-l)),p.x=(r>0?0:r)+l),a&&(p.height=Math.max(1,Math.abs(r=e-c)),p.y=(r>0?0:r)+c)}),o},t.prototype.drag=function(t){var e,i=this.chart,o=i.mouseDownX,r=void 0===o?0:o,n=i.mouseDownY,s=void 0===n?0:n,a=i.options.chart,h=a.panning,l=a.panKey,d=a.selectionMarkerFill,c=i.plotLeft,p=i.plotTop,u=i.plotWidth,f=i.plotHeight,g=nX(h)?h.enabled:h,v=l&&t[""+l+"Key"],m=t.chartX,y=t.chartY,x=this.selectionMarker;if((!x||!x.touch)&&(m<c?m=c:m>c+u&&(m=c+u),y<p?y=p:y>p+f&&(y=p+f),this.hasDragged=Math.sqrt(Math.pow(r-m,2)+Math.pow(s-y,2)),this.hasDragged>10)){e=i.isInsidePlot(r-c,s-p,{visiblePlotOnly:!0});var b=this.getSelectionMarkerAttrs(m,y),M=b.shapeType,k=b.attrs;(i.hasCartesianSeries||i.mapView)&&this.hasZoom&&e&&!v&&!x&&(this.selectionMarker=x=i.renderer[M](),x.attr({class:"highcharts-selection-marker",zIndex:7}).add(),i.styledMode||x.attr({fill:d||nC("#334eff").setOpacity(.25).get()})),x&&x.attr(k),e&&!x&&g&&i.pan(t,h)}},t.prototype.dragStart=function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY},t.prototype.getSelectionBox=function(t){var e={args:{marker:t},result:t.getBBox()};return nW(this,"getSelectionBox",e),e.result},t.prototype.drop=function(t){for(var e,i=this,o=this.chart,r=this.selectionMarker,n=0,s=o.axes;n<s.length;n++){var a=s[n];a.isPanning&&(a.isPanning=!1,(a.options.startOnTick||a.options.endOnTick||a.series.some(function(t){return t.boosted}))&&(a.forceRedraw=!0,a.setExtremes(a.userMin,a.userMax,!1),e=!0))}if(e&&o.redraw(),r&&t){if(this.hasDragged){var h=this.getSelectionBox(r);o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&("xAxis"===t.coll&&i.zoomX||"yAxis"===t.coll&&i.zoomY)}),selection:nO({originalEvent:t,xAxis:[],yAxis:[]},h),from:h})}nG(o.index)&&(this.selectionMarker=r.destroy())}o&&nG(o.index)&&(nz(o.container,{cursor:o._cursor}),o.cancelClick=this.hasDragged>10,o.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])},t.prototype.findNearestKDPoint=function(t,e,i){var o;return t.forEach(function(t){var r,n,s,a,h,l,d,c=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y"),p=t.searchPoint(i,c);nX(p,!0)&&p.series&&(!nX(o,!0)||(h=(r=o).distX-p.distX,l=r.dist-p.dist,d=(null===(n=p.series.group)||void 0===n?void 0:n.zIndex)-(null===(s=r.series.group)||void 0===s?void 0:s.zIndex),(0!==h&&e?h:0!==l?l:0!==d?d:r.series.index>p.series.index?-1:1)>0))&&(o=p)}),o},t.prototype.getChartCoordinatesFromPoint=function(t,e){var i,o,r=t.series,n=r.xAxis,s=r.yAxis,a=t.shapeArgs;if(n&&s){var h=null!==(o=null!==(i=t.clientX)&&void 0!==i?i:t.plotX)&&void 0!==o?o:0,l=t.plotY||0;return t.isNode&&a&&nG(a.x)&&nG(a.y)&&(h=a.x,l=a.y),e?{chartX:s.len+s.pos-l,chartY:n.len+n.pos-h}:{chartX:h+n.pos,chartY:l+s.pos}}if((null==a?void 0:a.x)&&a.y)return{chartX:a.x,chartY:a.y}},t.prototype.getChartPosition=function(){if(this.chartPosition)return this.chartPosition;var t=this.chart.container,e=nH(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};var i=t.offsetHeight,o=t.offsetWidth;return o>2&&i>2&&(this.chartPosition.scaleX=e.width/o,this.chartPosition.scaleY=e.height/i),this.chartPosition},t.prototype.getCoordinates=function(t){for(var e={xAxis:[],yAxis:[]},i=0,o=this.chart.axes;i<o.length;i++){var r=o[i];e[r.isXAxis?"xAxis":"yAxis"].push({axis:r,value:r.toValue(t[r.horiz?"chartX":"chartY"])})}return e},t.prototype.getHoverData=function(t,e,i,o,r,n){var s,a=[],h=function(t){return t.visible&&!(!r&&t.directTouch)&&nj(t.options.enableMouseTracking,!0)},l=e,d={chartX:n?n.chartX:void 0,chartY:n?n.chartY:void 0,shared:r};nW(this,"beforeGetHoverData",d),s=l&&!l.stickyTracking?[l]:i.filter(function(t){return t.stickyTracking&&(d.filter||h)(t)});var c=o&&t||!n?t:this.findNearestKDPoint(s,r,n);return l=null==c?void 0:c.series,c&&(r&&!l.noSharedTooltip?(s=i.filter(function(t){return d.filter?d.filter(t):h(t)&&!t.noSharedTooltip})).forEach(function(t){var e,i=null===(e=t.options)||void 0===e?void 0:e.nullInteraction,o=nN(t.points,function(t){return t.x===c.x&&(!t.isNull||!!i)});nX(o)&&(t.boosted&&t.boost&&(o=t.boost.getPoint(o)),a.push(o))}):a.push(c)),nW(this,"afterGetHoverData",d={hoverPoint:c}),{hoverPoint:d.hoverPoint,hoverSeries:l,hoverPoints:a}},t.prototype.getPointFromEvent=function(t){for(var e,i=t.target;i&&!e;)e=i.point,i=i.parentNode;return e},t.prototype.onTrackerMouseOut=function(t){var e=this.chart,i=t.relatedTarget,o=e.hoverSeries;this.isDirectTouch=!1,!o||!i||o.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+o.index)&&this.inClass(i,"highcharts-tracker")||o.onMouseOut()},t.prototype.inClass=function(t,e){for(var i,o=t;o;){if(i=nD(o,"class")){if(-1!==i.indexOf(e))return!0;if(-1!==i.indexOf("highcharts-container"))return!1}o=o.parentElement}},t.prototype.normalize=function(t,e){var i=t.touches,o=i?i.length?i.item(0):nj(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());var r=o.pageX-e.left,n=o.pageY-e.top;return nR(t,{chartX:Math.round(r/=e.scaleX),chartY:Math.round(n/=e.scaleY)})},t.prototype.onContainerClick=function(t){var e=this.chart,i=e.hoverPoint,o=this.normalize(t),r=e.plotLeft,n=e.plotTop;!e.cancelClick&&(i&&this.inClass(o.target,"highcharts-tracker")?(nW(i.series,"click",nR(o,{point:i})),e.hoverPoint&&i.firePointEvent("click",o)):(nR(o,this.getCoordinates(o)),e.isInsidePlot(o.chartX-r,o.chartY-n,{visiblePlotOnly:!0})&&nW(e,"click",o)))},t.prototype.onContainerMouseDown=function(t){var e,i=(1&(t.buttons||t.button))==1;t=this.normalize(t),tu.isFirefox&&0!==t.button&&this.onContainerMouseMove(t),(void 0===t.button||i)&&(this.zoomOption(t),i&&(null===(e=t.preventDefault)||void 0===e||e.call(t)),this.dragStart(t))},t.prototype.onContainerMouseLeave=function(e){var i=(nE[nj(t.hoverChartIndex,-1)]||{}).pointer;e=this.normalize(e),this.onContainerMouseMove(e),i&&!this.inClass(e.relatedTarget,"highcharts-tooltip")&&(i.reset(),i.chartPosition=void 0)},t.prototype.onContainerMouseEnter=function(){delete this.chartPosition},t.prototype.onContainerMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.normalize(t);this.setHoverChartIndex(t),("mousedown"===e.mouseIsDown||this.touchSelect(o))&&this.drag(o),!e.openMenu&&(this.inClass(o.target,"highcharts-tracker")||e.isInsidePlot(o.chartX-e.plotLeft,o.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!(null==i?void 0:i.shouldStickOnContact(o))&&(this.inClass(o.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(o))},t.prototype.onDocumentTouchEnd=function(t){this.onDocumentMouseUp(t)},t.prototype.onContainerTouchMove=function(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)},t.prototype.onContainerTouchStart=function(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))},t.prototype.onDocumentMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.chartPosition,r=this.normalize(t,o);!o||e.isInsidePlot(r.chartX-e.plotLeft,r.chartY-e.plotTop,{visiblePlotOnly:!0})||(null==i?void 0:i.shouldStickOnContact(r))||r.target!==e.container.ownerDocument&&this.inClass(r.target,"highcharts-tracker")||this.reset()},t.prototype.onDocumentMouseUp=function(e){var i,o;null===(o=null===(i=nE[nj(t.hoverChartIndex,-1)])||void 0===i?void 0:i.pointer)||void 0===o||o.drop(e)},t.prototype.pinch=function(t){var e=this,i=this,o=i.chart,r=i.hasZoom,n=i.lastTouches,s=[].map.call(t.touches||[],function(t){return i.normalize(t)}),a=s.length,h=1===a&&(i.inClass(t.target,"highcharts-tracker")&&o.runTrackerClick||i.runChartClick),l=o.tooltip,d=1===a&&nj(null==l?void 0:l.options.followTouchMove,!0);a>1?i.initiated=!0:d&&(i.initiated=!1),r&&i.initiated&&!h&&!1!==t.cancelable&&t.preventDefault(),"touchstart"===t.type?(i.pinchDown=s,i.res=!0,o.mouseDownX=t.chartX):d?this.runPointActions(i.normalize(t)):n&&(nW(o,"touchpan",{originalEvent:t,touches:s},function(){var i=function(t){var e=t[0],i=t[1]||e;return{x:e.chartX,y:e.chartY,width:i.chartX-e.chartX,height:i.chartY-e.chartY}};o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&(e.zoomHor&&t.horiz||e.zoomVert&&!t.horiz)}),to:i(s),from:i(n),trigger:t.type})}),i.res&&(i.res=!1,this.reset(!1,0))),i.lastTouches=s},t.prototype.reset=function(t,e){var i=this.chart,o=i.hoverSeries,r=i.hoverPoint,n=i.hoverPoints,s=i.tooltip,a=(null==s?void 0:s.shared)?n:r;t&&a&&n_(a).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?s&&a&&n_(a).length&&(s.refresh(a),s.shared&&n?n.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):r&&(r.setState(r.state,!0),i.axes.forEach(function(t){t.crosshair&&r.series[t.coll]===t&&t.drawCrosshair(null,r)}))):(r&&r.onMouseOut(),n&&n.forEach(function(t){t.setState()}),o&&o.onMouseOut(),s&&s.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)},t.prototype.runPointActions=function(e,i,o){var r,n=this.chart,s=n.series,a=(null===(r=n.tooltip)||void 0===r?void 0:r.options.enabled)?n.tooltip:void 0,h=!!a&&a.shared,l=i||n.hoverPoint,d=(null==l?void 0:l.series)||n.hoverSeries,c=(!e||"touchmove"!==e.type)&&(!!i||(null==d?void 0:d.directTouch)&&this.isDirectTouch),p=this.getHoverData(l,d,s,c,h,e);l=p.hoverPoint,d=p.hoverSeries;var u=p.hoverPoints,f=(null==d?void 0:d.tooltipOptions.followPointer)&&!d.tooltipOptions.split,g=h&&d&&!d.noSharedTooltip;if(l&&(o||l!==n.hoverPoint||(null==a?void 0:a.isHidden))){if((n.hoverPoints||[]).forEach(function(t){-1===u.indexOf(t)&&t.setState()}),n.hoverSeries!==d&&d.onMouseOver(),this.applyInactiveState(u),(u||[]).forEach(function(t){t.setState("hover")}),n.hoverPoint&&n.hoverPoint.firePointEvent("mouseOut"),!l.series)return;n.hoverPoints=u,n.hoverPoint=l,l.firePointEvent("mouseOver",void 0,function(){a&&l&&a.refresh(g?u:l,e)})}else if(f&&a&&!a.isHidden){var v=a.getAnchor([{}],e);n.isInsidePlot(v[0],v[1],{visiblePlotOnly:!0})&&a.updatePosition({plotX:v[0],plotY:v[1]})}this.unDocMouseMove||(this.unDocMouseMove=nI(n.container.ownerDocument,"mousemove",function(e){var i,o,r;return null===(r=null===(o=nE[null!==(i=t.hoverChartIndex)&&void 0!==i?i:-1])||void 0===o?void 0:o.pointer)||void 0===r?void 0:r.onDocumentMouseMove(e)}),this.eventsToUnbind.push(this.unDocMouseMove)),n.axes.forEach(function(t){var i,o,r,s=null===(o=null===(i=t.crosshair)||void 0===i?void 0:i.snap)||void 0===o||o;!s||(r=n.hoverPoint)&&r.series[t.coll]===t||(r=nN(u,function(e){var i;return(null===(i=e.series)||void 0===i?void 0:i[t.coll])===t})),r||!s?t.drawCrosshair(e,r):t.hideCrosshair()})},t.prototype.setDOMEvents=function(){var e=this,i=this.chart.container,o=i.ownerDocument;i.onmousedown=this.onContainerMouseDown.bind(this),i.onmousemove=this.onContainerMouseMove.bind(this),i.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(nI(i,"mouseenter",this.onContainerMouseEnter.bind(this)),nI(i,"mouseleave",this.onContainerMouseLeave.bind(this))),t.unbindDocumentMouseUp.some(function(t){return t.doc===o})||t.unbindDocumentMouseUp.push({doc:o,unbind:nI(o,"mouseup",this.onDocumentMouseUp.bind(this))});for(var r=this.chart.renderTo.parentElement;r&&"BODY"!==r.tagName;)this.eventsToUnbind.push(nI(r,"scroll",function(){delete e.chartPosition})),r=r.parentElement;this.eventsToUnbind.push(nI(i,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),nI(i,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),t.unbindDocumentTouchEnd||(t.unbindDocumentTouchEnd=nI(o,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),nI(this.chart,"redraw",this.setPointerCapture.bind(this))},t.prototype.setPointerCapture=function(){if(nB){var t,e,i=this.pointerCaptureEventsToUnbind,o=this.chart,r=o.container,n=nj(null===(t=o.options.tooltip)||void 0===t?void 0:t.followTouchMove,!0)&&o.series.some(function(t){return t.options.findNearestPointBy.indexOf("y")>-1});!this.hasPointerCapture&&n?(i.push(nI(r,"pointerdown",function(t){var e,i;(null===(e=t.target)||void 0===e?void 0:e.hasPointerCapture(t.pointerId))&&(null===(i=t.target)||void 0===i||i.releasePointerCapture(t.pointerId))}),nI(r,"pointermove",function(t){var e,i;null===(i=null===(e=o.pointer)||void 0===e?void 0:e.getPointFromEvent(t))||void 0===i||i.onMouseOver(t)})),o.styledMode||nz(r,{"touch-action":"none"}),r.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!n&&(i.forEach(function(t){return t()}),i.length=0,o.styledMode||nz(r,{"touch-action":nj(null===(e=o.options.chart.style)||void 0===e?void 0:e["touch-action"],"manipulation")}),r.className=r.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}},t.prototype.setHoverChartIndex=function(e){var i,o=this.chart,r=tu.charts[nj(t.hoverChartIndex,-1)];if(r&&r!==o){var n={relatedTarget:o.container};!e||(null==e?void 0:e.relatedTarget)||Object.assign({},e,n),null===(i=r.pointer)||void 0===i||i.onContainerMouseLeave(e||n)}(null==r?void 0:r.mouseIsDown)||(t.hoverChartIndex=o.index)},t.prototype.touch=function(t,e){var i,o=this.chart,r=this.pinchDown,n=void 0===r?[]:r;this.setHoverChartIndex(),1===(t=this.normalize(t)).touches.length?o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})&&!o.openMenu?(e&&this.runPointActions(t),"touchmove"===t.type&&(i=!!n[0]&&Math.pow(n[0].chartX-t.chartX,2)+Math.pow(n[0].chartY-t.chartY,2)>=16),nj(i,!0)&&this.pinch(t)):e&&this.reset():2===t.touches.length&&this.pinch(t)},t.prototype.touchSelect=function(t){return!!(this.chart.zooming.singleTouch&&t.touches&&1===t.touches.length)},t.prototype.zoomOption=function(t){var e,i,o=this.chart,r=o.inverted,n=o.zooming.type||"";/touch/.test(t.type)&&(n=nj(o.zooming.pinchType,n)),this.zoomX=e=/x/.test(n),this.zoomY=i=/y/.test(n),this.zoomHor=e&&!r||i&&r,this.zoomVert=i&&!r||e&&r,this.hasZoom=e||i},t.unbindDocumentMouseUp=[],t}();(g=nU||(nU={})).compose=function(t){nY(nL,"Core.Pointer")&&nI(t,"beforeRender",function(){this.pointer=new g(this,this.options)})};var nV=nU,nq=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};(v=V||(V={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},v.splice=function(t,e,i,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,nq([e,i],r,!1)),array:t};var n=Object.getPrototypeOf(t).constructor,s=t[o?"subarray":"slice"](e,e+i),a=new n(t.length-i+r.length);return a.set(t.subarray(0,e),0),a.set(r,e),a.set(t.subarray(e+i),e+r.length),{removed:s,array:a}};var nZ=V,nK=nZ.setLength,n$=nZ.splice,nJ=tG.fireEvent,nQ=tG.objectEach,n0=tG.uniqueKey,n1=function(){function t(t){void 0===t&&(t={});var e=this;this.autoId=!t.id,this.columns={},this.id=t.id||n0(),this.modified=this,this.rowCount=0,this.versionTag=n0();var i=0;nQ(t.columns||{},function(t,o){e.columns[o]=t.slice(),i=Math.max(i,t.length)}),this.applyRowCount(i)}return t.prototype.applyRowCount=function(t){var e=this;this.rowCount=t,nQ(this.columns,function(i,o){i.length!==t&&(e.columns[o]=nK(i,t))})},t.prototype.deleteRows=function(t,e){var i=this;if(void 0===e&&(e=1),e>0&&t<this.rowCount){var o=0;nQ(this.columns,function(r,n){i.columns[n]=n$(r,t,e).array,o=r.length}),this.rowCount=o}nJ(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=n0()},t.prototype.getColumn=function(t,e){return this.columns[t]},t.prototype.getColumns=function(t,e){var i=this;return(t||Object.keys(this.columns)).reduce(function(t,e){return t[e]=i.columns[e],t},{})},t.prototype.getRow=function(t,e){var i=this;return(e||Object.keys(this.columns)).map(function(e){var o;return null===(o=i.columns[e])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,e,i,o){var r;void 0===e&&(e=[]),void 0===i&&(i=0),this.setColumns(((r={})[t]=e,r),i,o)},t.prototype.setColumns=function(t,e,i){var o=this,r=this.rowCount;nQ(t,function(t,e){o.columns[e]=t.slice(),r=t.length}),this.applyRowCount(r),(null==i?void 0:i.silent)||(nJ(this,"afterSetColumns"),this.versionTag=n0())},t.prototype.setRow=function(t,e,i,o){void 0===e&&(e=this.rowCount);var r=this.columns,n=i?this.rowCount+1:e+1;nQ(t,function(t,s){var a=r[s]||(null==o?void 0:o.addColumns)!==!1&&Array(n);a&&(i?a=n$(a,e,0,!0,[t]).array:a[e]=t,r[s]=a)}),n>this.rowCount&&this.applyRowCount(n),(null==o?void 0:o.silent)||(nJ(this,"afterSetRows"),this.versionTag=n0())},t}(),n2=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},n3=tG.extend,n5=tG.merge,n6=tG.pick;!function(t){function e(t,e,i){var o,r,n,s=this.legendItem=this.legendItem||{},a=this.chart,h=this.options,l=t.baseline,d=void 0===l?0:l,c=t.symbolWidth,p=t.symbolHeight,u=this.symbol||"circle",f=p/2,g=a.renderer,v=s.group,m=d-Math.round(((null===(o=t.fontMetrics)||void 0===o?void 0:o.b)||p)*(i?.4:.3)),y={},x=h.marker,b=0;if(a.styledMode||(y["stroke-width"]=Math.min(h.lineWidth||0,24),h.dashStyle?y.dashstyle=h.dashStyle:"square"===h.linecap||(y["stroke-linecap"]="round")),s.line=g.path().addClass("highcharts-graph").attr(y).add(v),i&&(s.area=g.path().addClass("highcharts-area").add(v)),y["stroke-linecap"]&&(b=Math.min(s.line.strokeWidth(),c)/2),c){var M=[["M",b,m],["L",c-b,m]];s.line.attr({d:M}),null===(r=s.area)||void 0===r||r.attr({d:n2(n2([],M,!0),[["L",c-b,d],["L",b,d]],!1)})}if(x&&!1!==x.enabled&&c){var k=Math.min(n6(x.radius,f),f);0===u.indexOf("url")&&(x=n5(x,{width:p,height:p}),k=0),s.symbol=n=g.symbol(u,c/2-k,m-k,2*k,2*k,n3({context:"legend"},x)).addClass("highcharts-point").add(v),n.isMarker=!0}}t.areaMarker=function(t,i){e.call(this,t,i,!0)},t.lineMarker=e,t.rectangle=function(t,e){var i=e.legendItem||{},o=t.options,r=t.symbolHeight,n=o.squareSymbol,s=n?r:t.symbolWidth;i.symbol=this.chart.renderer.rect(n?(t.symbolWidth-r)/2:0,t.baseline-r+1,s,r,n6(t.options.symbolRadius,r/2)).addClass("highcharts-point").attr({zIndex:3}).add(i.group)}}(q||(q={}));var n9=q,n4={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){var t=this.series.chart.numberFormatter;return"number"!=typeof this.y?"":t(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},n8=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),n7=tG.extend,st=tG.extendClass,se=tG.merge;!function(t){function e(e,i){var o=ee.plotOptions||{},r=i.defaultOptions,n=i.prototype;return n.type=e,n.pointClass||(n.pointClass=nP),!t.seriesTypes[e]&&(r&&(o[e]=r),t.seriesTypes[e]=i,!0)}t.seriesTypes=tu.seriesTypes,t.registerSeriesType=e,t.seriesType=function(i,o,r,n,s){var a=ee.plotOptions||{};if(o=o||"",a[i]=se(a[o],r),delete t.seriesTypes[i],e(i,st(t.seriesTypes[o]||function(){},n)),t.seriesTypes[i].prototype.type=i,s){var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n8(e,t),e}(nP);n7(h.prototype,s),t.seriesTypes[i].prototype.pointClass=h}return t.seriesTypes[i]}}(Z||(Z={}));var si=Z,so=function(){return(so=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},sr=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},sn=oV.registerEventOptions,ss=tu.svg,sa=tu.win,sh=si.seriesTypes,sl=e5.format,sd=tG.arrayMax,sc=tG.arrayMin,sp=tG.clamp,su=tG.correctFloat,sf=tG.crisp,sg=tG.defined,sv=tG.destroyObjectProperties,sm=tG.diffObjects,sy=tG.erase,sx=tG.error,sb=tG.extend,sM=tG.find,sk=tG.fireEvent,sw=tG.getClosestDistance,sS=tG.getNestedProperty,sA=tG.insertItem,sT=tG.isArray,sP=tG.isNumber,sO=tG.isString,sC=tG.merge,sE=tG.objectEach,sL=tG.pick,sB=tG.removeEvent,sI=tG.syncTimeout,sD=function(){function t(){this.zoneAxis="y"}return t.prototype.init=function(t,e){sk(this,"init",{options:e}),null!==(i=this.dataTable)&&void 0!==i||(this.dataTable=new n1);var i,o,r,n,s,a=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);var h=this.options,l=!1!==h.visible;this.linkedSeries=[],this.bindAxes(),sb(this,{name:h.name,state:"",visible:l,selected:!0===h.selected}),sn(this,h);var d=h.events;((null==d?void 0:d.click)||(null===(r=null===(o=h.point)||void 0===o?void 0:o.events)||void 0===r?void 0:r.click)||h.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),a.length&&(s=a[a.length-1]),this._i=sL(null==s?void 0:s._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",sA(this,a)),(null===(n=h.dataSorting)||void 0===n?void 0:n.enabled)?this.setDataSortingOptions():this.points||this.data||this.setData(h.data,!1),sk(this,"afterInit")},t.prototype.is=function(t){return sh[t]&&this instanceof sh[t]},t.prototype.bindAxes=function(){var t,e=this,i=e.options,o=e.chart;sk(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(r){(o[r]||[]).forEach(function(o){t=o.options,(sL(i[r],0)===o.index||void 0!==i[r]&&i[r]===t.id)&&(sA(e,o.series),e[r]=o,o.isDirty=!0)}),e[r]||e.optionalAxis===r||sx(18,!0,o)})}),sk(this,"afterBindAxes")},t.prototype.hasData=function(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.dataTable.rowCount>0},t.prototype.hasMarkerChanged=function(t,e){var i=t.marker,o=e.marker||{};return i&&(o.enabled&&!i.enabled||o.symbol!==i.symbol||o.height!==i.height||o.width!==i.width)},t.prototype.autoIncrement=function(t){var e,i,o,r=this.options,n=this.options,s=n.pointIntervalUnit,a=n.relativeXValue,h=this.chart.time,l=null!==(i=null!==(e=this.xIncrement)&&void 0!==e?e:h.parse(r.pointStart))&&void 0!==i?i:0;if(this.pointInterval=o=sL(this.pointInterval,r.pointInterval,1),a&&sP(t)&&(o*=t),s){var d=h.toParts(l);"day"===s?d[2]+=o:"month"===s?d[1]+=o:"year"===s&&(d[0]+=o),o=h.makeTime.apply(h,d)-l}return a&&sP(t)?l+o:(this.xIncrement=l+o,l)},t.prototype.setDataSortingOptions=function(){var t=this.options;sb(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),sg(t.pointRange)||(t.pointRange=1)},t.prototype.setOptions=function(t){var e,i,o,r=this.chart,n=r.options.plotOptions,s=r.userOptions||{},a=sC(t),h=r.styledMode,l={plotOptions:n,userOptions:a};sk(this,"setOptions",l);var d=l.plotOptions[this.type],c=s.plotOptions||{},p=c.series||{},u=ee.plotOptions[this.type]||{},f=c[this.type]||{};d.dataLabels=this.mergeArrays(u.dataLabels,d.dataLabels),this.userOptions=l.userOptions;var g=sC(d,n.series,f,a);this.tooltipOptions=sC(ee.tooltip,null===(e=ee.plotOptions.series)||void 0===e?void 0:e.tooltip,null==u?void 0:u.tooltip,r.userOptions.tooltip,null===(i=c.series)||void 0===i?void 0:i.tooltip,f.tooltip,a.tooltip),this.stickyTracking=sL(a.stickyTracking,f.stickyTracking,p.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),null===d.marker&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";var v=this.zones=(g.zones||[]).map(function(t){return so({},t)});return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(o={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},h||(o.color=g.negativeColor,o.fillColor=g.negativeFillColor),v.push(o)),v.length&&sg(v[v.length-1].value)&&v.push(h?{}:{color:this.color,fillColor:this.fillColor}),sk(this,"afterSetOptions",{options:g}),g},t.prototype.getName=function(){var t;return null!==(t=this.options.name)&&void 0!==t?t:sl(this.chart.options.lang.seriesName,this,this.chart)},t.prototype.getCyclic=function(t,e,i){var o,r,n=this.chart,s=""+t+"Index",a=""+t+"Counter",h=(null==i?void 0:i.length)||n.options.chart.colorCount;!e&&(sg(r=sL("color"===t?this.options.colorIndex:void 0,this[s]))?o=r:(n.series.length||(n[a]=0),o=n[a]%h,n[a]+=1),i&&(e=i[o])),void 0!==o&&(this[s]=o),this[t]=e},t.prototype.getColor=function(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||ee.plotOptions[this.type].color,this.chart.options.colors)},t.prototype.getPointsCollection=function(){return(this.hasGroupedData?this.points:this.data)||[]},t.prototype.getSymbol=function(){var t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)},t.prototype.getColumn=function(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]},t.prototype.findPointIndex=function(t,e){var i,o,r,n,s=t.id,a=t.x,h=this.points,l=this.options.dataSorting,d=this.cropStart||0;if(s){var c=this.chart.get(s);c instanceof nP&&(o=c)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){var p=function(e){return!e.touched&&e.index===t.index};if((null==l?void 0:l.matchByName)?p=function(e){return!e.touched&&e.name===t.name}:this.options.relativeXValue&&(p=function(e){return!e.touched&&e.options.x===t.x}),!(o=sM(h,p)))return}return o&&void 0!==(n=null==o?void 0:o.index)&&(r=!0),void 0===n&&sP(a)&&(n=this.getColumn("x").indexOf(a,e)),-1!==n&&void 0!==n&&this.cropped&&(n=n>=d?n-d:n),!r&&sP(n)&&(null===(i=h[n])||void 0===i?void 0:i.touched)&&(n=void 0),n},t.prototype.updateData=function(t,e){var i,o,r,n,s,a=this,h=this.options,l=this.requireSorting,d=h.dataSorting,c=this.points,p=[],u=t.length===c.length,f=!0;if(this.xIncrement=null,t.forEach(function(t,e){var i,r,n=sg(t)&&a.pointClass.prototype.optionsToObject.call({series:a},t)||{},f=n.id,g=n.x;f||sP(g)?(-1===(r=a.findPointIndex(n,s))||void 0===r?p.push(t):c[r]&&t!==(null===(i=h.data)||void 0===i?void 0:i[r])?(c[r].update(t,!1,void 0,!1),c[r].touched=!0,l&&(s=r+1)):c[r]&&(c[r].touched=!0),(!u||e!==r||(null==d?void 0:d.enabled)||a.hasDerivedData)&&(o=!0)):p.push(t)},this),o)for(r=c.length;r--;)(n=c[r])&&!n.touched&&(null===(i=n.remove)||void 0===i||i.call(n,!1,e));else!u||(null==d?void 0:d.enabled)?f=!1:(t.forEach(function(t,e){t===c[e].y||c[e].destroyed||c[e].update(t,!1,void 0,!1)}),p.length=0);if(c.forEach(function(t){t&&(t.touched=!1)}),!f)return!1;p.forEach(function(t){a.addPoint(t,!1,void 0,void 0,!1)},this);var g=this.getColumn("x");return null===this.xIncrement&&g.length&&(this.xIncrement=sd(g),this.autoIncrement()),!0},t.prototype.dataColumnKeys=function(){return sr(["x"],this.pointArrayMap||["y"],!0)},t.prototype.setData=function(t,e,i,o){void 0===e&&(e=!0);var r,n,s,a,h,l,d,c=this.points,p=(null==c?void 0:c.length)||0,u=this.options,f=this.chart,g=u.dataSorting,v=this.xAxis,m=u.turboThreshold,y=this.dataTable,x=this.dataColumnKeys(),b=this.pointValKey||"y",M=(this.pointArrayMap||[]).length,k=u.keys,w=0,S=1;f.options.chart.allowMutatingData||(u.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,d=sC(!0,t));var A=(t=d||t||[]).length;if((null==g?void 0:g.enabled)&&(t=this.sortData(t)),f.options.chart.allowMutatingData&&!1!==o&&A&&p&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(l=this.updateData(t,i)),!l){this.xIncrement=null,this.colorCounter=0;var T=m&&A>m;if(T){var P=this.getFirstValidPoint(t),O=this.getFirstValidPoint(t,A-1,-1),C=function(t){return!!(sT(t)&&(k||sP(t[0])))};if(sP(P)&&sP(O)){for(var E=[],L=[],B=0,I=t;B<I.length;B++){var D=I[B];E.push(this.autoIncrement()),L.push(D)}y.setColumns(((r={x:E})[b]=L,r))}else if(C(P)&&C(O)){if(M){for(var z=+(P.length===M),R=Array(x.length).fill(0).map(function(){return[]}),N=0,W=t;N<W.length;N++){var G=W[N];z&&R[0].push(this.autoIncrement());for(var X=z;X<=M;X++)null===(s=R[X])||void 0===s||s.push(G[X-z])}y.setColumns(x.reduce(function(t,e,i){return t[e]=R[i],t},{}))}else{k&&(w=k.indexOf("x"),S=k.indexOf("y"),w=w>=0?w:0,S=S>=0?S:1),1===P.length&&(S=0);var F=[],L=[];if(w===S)for(var H=0,j=t;H<j.length;H++){var G=j[H];F.push(this.autoIncrement()),L.push(G[S])}else for(var Y=0,_=t;Y<_.length;Y++){var G=_[Y];F.push(G[w]),L.push(G[S])}y.setColumns(((n={x:F})[b]=L,n))}}else T=!1}if(!T){var U=x.reduce(function(t,e){return t[e]=[],t},{});for(h=0;h<A;h++)for(var G=this.pointClass.prototype.applyOptions.apply({series:this},[t[h]]),V=0;V<x.length;V++){var q=x[V];U[q][h]=G[q]}y.setColumns(U)}for(sO(this.getColumn("y")[0])&&sx(14,!0,f),this.data=[],this.options.data=this.userOptions.data=t,h=p;h--;)null===(a=c[h])||void 0===a||a.destroy();v&&(v.minRange=v.userMinRange),this.isDirty=f.isDirtyBox=!0,this.isDirtyData=!!c,i=!1}"point"===u.legendType&&(this.processData(),this.generatePoints()),e&&f.redraw(i)},t.prototype.sortData=function(t){var e=this,i=e.options.dataSorting.sortKey||"y",o=function(t,e){return sg(e)&&t.pointClass.prototype.optionsToObject.call({series:t},e)||{}};return t.forEach(function(i,r){t[r]=o(e,i),t[r].index=r},this),t.concat().sort(function(t,e){var o=sS(i,t),r=sS(i,e);return r<o?-1:+(r>o)}).forEach(function(t,e){t.x=e},this),e.linkedSeries&&e.linkedSeries.forEach(function(e){var i,r=e.options,n=r.data;(null===(i=r.dataSorting)||void 0===i?void 0:i.enabled)||!n||(n.forEach(function(i,r){n[r]=o(e,i),t[r]&&(n[r].x=t[r].x,n[r].index=r)}),e.setData(n,!1))}),t},t.prototype.getProcessedData=function(t){var e,i,o,r,n,s=this,a=s.dataTable,h=s.isCartesian,l=s.options,d=s.xAxis,c=l.cropThreshold,p=t||s.getExtremesFromAll,u=null==d?void 0:d.logarithmic,f=a.rowCount,g=0,v=s.getColumn("x"),m=a,y=!1;return d&&(r=(o=d.getExtremes()).min,n=o.max,y=!!(d.categories&&!d.names.length),h&&s.sorted&&!p&&(!c||f>c||s.forceCrop)&&(v[f-1]<r||v[0]>n?m=new n1:s.getColumn(s.pointValKey||"y").length&&(v[0]<r||v[f-1]>n)&&(m=(e=this.cropData(a,r,n)).modified,g=e.start,i=!0))),v=m.getColumn("x")||[],{modified:m,cropped:i,cropStart:g,closestPointRange:sw([u?v.map(u.log2lin):v],function(){return s.requireSorting&&!y&&sx(15,!1,s.chart)})}},t.prototype.processData=function(t){var e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;var o=this.getProcessedData();i.modified=o.modified,this.cropped=o.cropped,this.cropStart=o.cropStart,this.closestPointRange=this.basePointRange=o.closestPointRange,sk(this,"afterProcessData")},t.prototype.cropData=function(t,e,i){var o,r,n=t.getColumn("x",!0)||[],s=n.length,a={},h=0,l=s;for(o=0;o<s;o++)if(n[o]>=e){h=Math.max(0,o-1);break}for(r=o;r<s;r++)if(n[r]>i){l=r+1;break}for(var d=0,c=this.dataColumnKeys();d<c.length;d++){var p=c[d],u=t.getColumn(p,!0);u&&(a[p]=u.slice(h,l))}return{modified:new n1({columns:a}),start:h,end:l}},t.prototype.generatePoints=function(){var t,e,i,o,r,n,s,a,h,l,d=this.options,c=this.processedData||d.data,p=this.dataTable.modified,u=this.getColumn("x",!0),f=this.pointClass,g=p.rowCount,v=this.cropStart||0,m=this.hasGroupedData,y=d.keys,x=[],b=(null===(t=d.dataGrouping)||void 0===t?void 0:t.groupAll)?v:0,M=null===(e=this.xAxis)||void 0===e?void 0:e.categories,k=this.pointArrayMap||["y"],w=this.dataColumnKeys(),S=this.data;if(!S&&!m){var A=[];A.length=(null==c?void 0:c.length)||0,S=this.data=A}for(y&&m&&(this.options.keys=!1),h=0;h<g;h++)s=v+h,m?((a=new f(this,p.getRow(h,w)||[])).dataGroup=this.groupMap[b+h],(null===(i=a.dataGroup)||void 0===i?void 0:i.options)&&(a.options=a.dataGroup.options,sb(a,a.dataGroup.options),delete a.dataLabels)):(a=S[s],l=c?c[s]:p.getRow(h,k),a||void 0===l||(S[s]=a=new f(this,l,u[h]))),a&&(a.index=m?b+h:s,x[h]=a,a.category=null!==(o=null==M?void 0:M[a.x])&&void 0!==o?o:a.x,a.key=null!==(r=a.name)&&void 0!==r?r:a.category);if(this.options.keys=y,S&&(g!==(n=S.length)||m))for(h=0;h<n;h++)h!==v||m||(h+=g),S[h]&&(S[h].destroyElements(),S[h].plotX=void 0);this.data=S,this.points=x,sk(this,"afterGeneratePoints")},t.prototype.getXExtremes=function(t){return{min:sc(t),max:sd(t)}},t.prototype.getExtremes=function(t,e){var i,o,r,n,s=this.xAxis,a=this.yAxis,h=e||this.getExtremesFromAll||this.options.getExtremesFromAll,l=h&&this.cropped?this.dataTable:this.dataTable.modified,d=l.rowCount,c=t||this.stackedYData,p=c?[c]:(null===(i=this.keysAffectYAxis||this.pointArrayMap||["y"])||void 0===i?void 0:i.map(function(t){return l.getColumn(t,!0)||[]}))||[],u=this.getColumn("x",!0),f=[],g=this.requireSorting&&!this.is("column")?1:0,v=!!a&&a.positiveValuesOnly,m=h||this.cropped||!s,y=0,x=0;for(s&&(y=(o=s.getExtremes()).min,x=o.max),n=0;n<d;n++)if(r=u[n],m||(u[n+g]||r)>=y&&(u[n-g]||r)<=x)for(var b=0;b<p.length;b++){var M=p[b][n];sP(M)&&(M>0||!v)&&f.push(M)}var k={activeYData:f,dataMin:sc(f),dataMax:sd(f)};return sk(this,"afterGetExtremes",{dataExtremes:k}),k},t.prototype.applyExtremes=function(){var t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t},t.prototype.getFirstValidPoint=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=1);for(var o=t.length,r=e;r>=0&&r<o;){if(sg(t[r]))return t[r];r+=i}},t.prototype.translate=function(){this.generatePoints();var t,e,i,o,r,n=this.options,s=n.stacking,a=this.xAxis,h=this.enabledDataSorting,l=this.yAxis,d=this.points,c=d.length,p=this.pointPlacementToXValue(),u=!!p,f=n.threshold,g=n.startFromThreshold?f:0,v=(null==n?void 0:n.nullInteraction)&&l.len,m=Number.MAX_VALUE;function y(t){return sp(t,-1e9,1e9)}for(e=0;e<c;e++){var x=d[e],b=x.x,M=void 0,k=void 0,w=x.y,S=x.low,A=s&&(null===(t=l.stacking)||void 0===t?void 0:t.stacks[(this.negStacks&&w<(g?0:f)?"-":"")+this.stackKey]);x.plotX=sP(i=a.translate(b,!1,!1,!1,!0,p))?su(y(i)):void 0,s&&this.visible&&A&&A[b]&&(r=this.getStackIndicator(r,b,this.index),!x.isNull&&r.key&&(k=(M=A[b]).points[r.key]),M&&sT(k)&&(S=k[0],w=k[1],S===g&&r.key===A[b].base&&(S=sL(sP(f)?f:l.min)),l.positiveValuesOnly&&sg(S)&&S<=0&&(S=void 0),x.total=x.stackTotal=sL(M.total),x.percentage=sg(x.y)&&M.total?x.y/M.total*100:void 0,x.stackY=w,this.irregularWidths||M.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),x.yBottom=sg(S)?y(l.translate(S,!1,!0,!1,!0)):void 0,this.dataModify&&(w=this.dataModify.modifyValue(w,e));var T=void 0;sP(w)&&void 0!==x.plotX?T=sP(T=l.translate(w,!1,!0,!1,!0))?y(T):void 0:!sP(w)&&v&&(T=v),x.plotY=T,x.isInside=this.isPointInside(x),x.clientX=u?su(a.translate(b,!1,!1,!1,!0,p)):i,x.negative=(x.y||0)<(f||0),x.isNull||!1===x.visible||(void 0!==o&&(m=Math.min(m,Math.abs(i-o))),o=i),x.zone=this.zones.length?x.getZone():void 0,!x.graphic&&this.group&&h&&(x.isNew=!0)}this.closestPointRangePx=m,sk(this,"afterTranslate")},t.prototype.getValidPoints=function(t,e,i){var o=this.chart;return(t||this.points||[]).filter(function(t){var r=t.plotX,n=t.plotY;return!!((i||!t.isNull&&sP(n))&&(!e||o.isInsidePlot(r,n,{inverted:o.inverted})))&&!1!==t.visible})},t.prototype.getSharedClipKey=function(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey},t.prototype.setClip=function(){var t=this.chart,e=this.group,i=this.markerGroup,o=t.sharedClips,r=t.renderer,n=t.getClipBox(this),s=this.getSharedClipKey(),a=o[s];a?a.animate(n):o[s]=a=r.clipRect(n),e&&e.clip(!1===this.options.clip?void 0:a),i&&i.clip()},t.prototype.animate=function(t){var e=this.chart,i=this.group,o=this.markerGroup,r=e.inverted,n=eT(this.options.animation),s=[this.getSharedClipKey(),n.duration,n.easing,n.defer].join(","),a=e.sharedClips[s],h=e.sharedClips[s+"m"];if(t&&i){var l=e.getClipBox(this);if(a)a.attr("height",l.height);else{l.width=0,r&&(l.x=e.plotHeight),a=e.renderer.clipRect(l),e.sharedClips[s]=a;var d={x:-99,y:-99,width:r?e.plotWidth+199:99,height:r?99:e.plotHeight+199};h=e.renderer.clipRect(d),e.sharedClips[s+"m"]=h}i.clip(a),null==o||o.clip(h)}else if(a&&!a.hasClass("highcharts-animating")){var c=e.getClipBox(this),p=n.step;((null==o?void 0:o.element.childNodes.length)||e.series.length>1)&&(n.step=function(t,e){p&&p.apply(e,arguments),"width"===e.prop&&(null==h?void 0:h.element)&&h.attr(r?"height":"width",t+99)}),a.addClass("highcharts-animating").animate(c,n)}},t.prototype.afterAnimate=function(){var t=this;this.setClip(),sE(this.chart.sharedClips,function(e,i,o){e&&!t.chart.container.querySelector('[clip-path="url(#'.concat(e.id,')"]'))&&(e.destroy(),delete o[i])}),this.finishedAnimating=!0,sk(this,"afterAnimate")},t.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i,o,r,n,s,a,h=this.chart,l=h.styledMode,d=this.colorAxis,c=this.options,p=c.marker,u=c.nullInteraction,f=this[this.specialGroup||"markerGroup"],g=this.xAxis,v=sL(p.enabled,!g||!!g.isRadial||null,this.closestPointRangePx>=p.enabledThreshold*p.radius);if(!1!==p.enabled||this._hasPointMarkers)for(e=0;e<t.length;e++){r=(o=(i=t[e]).graphic)?"animate":"attr",n=i.marker||{},s=!!i.marker;var m=i.isNull;if((v&&!sg(n.enabled)||n.enabled)&&(!m||u)&&!1!==i.visible){var y=sL(n.symbol,this.symbol,"rect");a=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=g.reversed?-(a.width||0):g.width);var x=!1!==i.isInside;if(!o&&x&&((a.width||0)>0||i.hasImage)&&(i.graphic=o=h.renderer.symbol(y,a.x,a.y,a.width,a.height,s?n:p).add(f),this.enabledDataSorting&&h.hasRendered&&(o.attr({x:i.startXPos}),r="animate")),o&&"animate"===r&&o[x?"show":"hide"](x).animate(a),o){var b=this.pointAttribs(i,l||!i.selected?void 0:"select");l?d&&o.css({fill:b.fill}):o[r](b)}o&&o.addClass(i.getClassName(),!0)}else o&&(i.graphic=o.destroy())}},t.prototype.markerAttribs=function(t,e){var i,o,r=this.options,n=r.marker,s=t.marker||{},a=s.symbol||n.symbol,h={},l=sL(s.radius,null==n?void 0:n.radius);e&&(i=n.states[e],l=sL(null==(o=s.states&&s.states[e])?void 0:o.radius,null==i?void 0:i.radius,l&&l+((null==i?void 0:i.radiusPlus)||0))),t.hasImage=a&&0===a.indexOf("url"),t.hasImage&&(l=0);var d=t.pos();return sP(l)&&d&&(r.crisp&&(d[0]=sf(d[0],t.hasImage?0:"rect"===a?(null==n?void 0:n.lineWidth)||0:1)),h.x=d[0]-l,h.y=d[1]-l),l&&(h.width=h.height=2*l),h},t.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a=this.options,h=a.marker,l=null==t?void 0:t.options,d=(null==l?void 0:l.marker)||{},c=null==l?void 0:l.color,p=null==t?void 0:t.color,u=null===(i=null==t?void 0:t.zone)||void 0===i?void 0:i.color,f=this.color,g=sL(d.lineWidth,h.lineWidth),v=(null==t?void 0:t.isNull)&&a.nullInteraction?0:1;return f=c||u||p||f,n=d.fillColor||h.fillColor||f,s=d.lineColor||h.lineColor||f,e=e||"normal",o=h.states[e]||{},g=sL((r=d.states&&d.states[e]||{}).lineWidth,o.lineWidth,g+sL(r.lineWidthPlus,o.lineWidthPlus,0)),n=r.fillColor||o.fillColor||n,s=r.lineColor||o.lineColor||s,{stroke:s,"stroke-width":g,fill:n,opacity:v=sL(r.opacity,o.opacity,v)}},t.prototype.destroy=function(t){var e,i,o,r,n=this,s=n.chart,a=/AppleWebKit\/533/.test(sa.navigator.userAgent),h=n.data||[];for(sk(n,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(n.axisTypes||[]).forEach(function(t){(null==(r=n[t])?void 0:r.series)&&(sy(r.series,n),r.isDirty=r.forceRedraw=!0)}),n.legendItem&&n.chart.legend.destroyItem(n),o=h.length;o--;)null===(i=null===(e=h[o])||void 0===e?void 0:e.destroy)||void 0===i||i.call(e);for(var l=0,d=n.zones;l<d.length;l++)sv(d[l],void 0,!0);tG.clearTimeout(n.animationTimeout),sE(n,function(t,e){t instanceof iB&&!t.survive&&t[a&&"group"===e?"hide":"destroy"]()}),s.hoverSeries===n&&(s.hoverSeries=void 0),sy(s.series,n),s.orderItems("series"),sE(n,function(e,i){t&&"hcEvents"===i||delete n[i]})},t.prototype.applyZones=function(){var t=this.area,e=this.chart,i=this.graph,o=this.zones,r=this.points,n=this.xAxis,s=this.yAxis,a=this.zoneAxis,h=e.inverted,l=e.renderer,d=this[""+a+"Axis"],c=d||{},p=c.isXAxis,u=c.len,f=void 0===u?0:u,g=c.minPointOffset,v=void 0===g?0:g,m=((null==i?void 0:i.strokeWidth())||0)/2+1,y=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0),h&&(i=f-i);var o=t.translated,r=void 0===o?0:o,n=t.lineClip,s=i-r;null==n||n.push(["L",e,Math.abs(s)<m?i-m*(s<=0?-1:1):r])};if(o.length&&(i||t)&&d&&sP(d.min)){var x=d.getExtremes().max+v,b=function(t){t.forEach(function(e,i){("M"===e[0]||"L"===e[0])&&(t[i]=[e[0],p?f-e[1]:e[1],p?e[2]:f-e[2]])})};if(o.forEach(function(t){t.lineClip=[],t.translated=sp(d.toPixels(sL(t.value,x),!0)||0,0,f)}),i&&!this.showLine&&i.hide(),t&&t.hide(),"y"===a&&r.length<n.len)for(var M=0;M<r.length;M++){var k=r[M],w=k.plotX,S=k.plotY,A=k.zone,T=A&&o[o.indexOf(A)-1];A&&y(A,w,S),T&&y(T,w,S)}var P=[],O=d.toPixels(d.getExtremes().min-v,!0);o.forEach(function(e){var o,r,a=e.lineClip||[],d=Math.round(e.translated||0);n.reversed&&a.reverse();var c=e.clip,u=e.simpleClip,f=0,g=0,v=n.len,m=s.len;p?(f=d,v=O):(g=d,m=O);var y=[["M",f,g],["L",v,g],["L",v,m],["L",f,m],["Z"]],x=sr(sr(sr(sr([y[0]],a,!0),[y[1],y[2]],!1),P,!0),[y[3],y[4]],!1);P=a.reverse(),O=d,h&&(b(x),t&&b(y)),c?(c.animate({d:x}),null==u||u.animate({d:y})):(c=e.clip=l.path(x),t&&(u=e.simpleClip=l.path(y))),i&&(null===(o=e.graph)||void 0===o||o.clip(c)),t&&(null===(r=e.area)||void 0===r||r.clip(u))})}else this.visible&&(i&&i.show(),t&&t.show())},t.prototype.plotGroup=function(t,e,i,o,r){var n=this[t],s=!n,a={visibility:i,zIndex:o||.1};return sg(this.opacity)&&!this.chart.styledMode&&"inactive"!==this.state&&(a.opacity=this.opacity),n||(this[t]=n=this.chart.renderer.g().add(r)),n.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(sg(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(n.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),n.attr(a)[s?"attr":"animate"](this.getPlotBox(e)),n},t.prototype.getPlotBox=function(t){var e=this.xAxis,i=this.yAxis,o=this.chart,r=o.inverted&&!o.polar&&e&&this.invertible&&"series"===t;return o.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:o.plotLeft,translateY:i?i.top:o.plotTop,rotation:90*!!r,rotationOriginX:r?(e.len-i.len)/2:0,rotationOriginY:r?(e.len+i.len)/2:0,scaleX:r?-1:1,scaleY:1}},t.prototype.removeEvents=function(t){var e=this.eventsToUnbind;t||sB(this),e.length&&(e.forEach(function(t){t()}),e.length=0)},t.prototype.render=function(){var t,e,i,o,r,n=this,s=n.chart,a=n.options,h=n.hasRendered,l=eT(a.animation),d=n.visible?"inherit":"hidden",c=a.zIndex,p=s.seriesGroup,u=n.finishedAnimating?0:l.duration;sk(this,"render"),n.plotGroup("group","series",d,c,p),n.markerGroup=n.plotGroup("markerGroup","markers",d,c,p),!1!==a.clip&&n.setClip(),u&&(null===(t=n.animate)||void 0===t||t.call(n,!0)),n.drawGraph&&(n.drawGraph(),n.applyZones()),n.visible&&n.drawPoints(),null===(e=n.drawDataLabels)||void 0===e||e.call(n),null===(i=n.redrawPoints)||void 0===i||i.call(n),a.enableMouseTracking&&(null===(o=n.drawTracker)||void 0===o||o.call(n)),u&&(null===(r=n.animate)||void 0===r||r.call(n)),h||(u&&l.defer&&(u+=l.defer),n.animationTimeout=sI(function(){n.afterAnimate()},u||0)),n.isDirty=!1,n.hasRendered=!0,sk(n,"afterRender")},t.prototype.redraw=function(){var t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree},t.prototype.reserveSpace=function(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries},t.prototype.searchPoint=function(t,e){var i=this.xAxis,o=this.yAxis,r=this.chart.inverted;return this.searchKDTree({clientX:r?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:r?o.len-t.chartX+o.pos:t.chartY-o.pos},e,t)},t.prototype.buildKDTree=function(t){this.buildingKdTree=!0;var e=this,i=e.options,o=i.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,sI(function(){e.kdTree=function t(i,o,r){var n,s,a=null==i?void 0:i.length;if(a)return n=e.kdAxisArray[o%r],i.sort(function(t,e){return(t[n]||0)-(e[n]||0)}),{point:i[s=Math.floor(a/2)],left:t(i.slice(0,s),o+1,r),right:t(i.slice(s+1),o+1,r)}}(e.getValidPoints(void 0,!e.directTouch,null==i?void 0:i.nullInteraction),o,o),e.buildingKdTree=!1},i.kdNow||(null==t?void 0:t.type)==="touchstart"?0:1)},t.prototype.searchKDTree=function(t,e,i,o,r){var n=this,s=this.kdAxisArray,a=s[0],h=s[1],l=e?"distX":"dist",d=(n.options.findNearestPointBy||"").indexOf("y")>-1?2:1,c=!!n.isBubble,p=o||function(t,e,i){var o=t[i]||0,r=e[i]||0;return[o===r&&t.index>e.index||o<r?t:e,!1]},u=r||function(t,e){return t<e};if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function t(e,i,o,r){var s,d,f,g,v,m,y,x,b,M,k=i.point,w=n.kdAxisArray[o%r],S=k,A=!1;d=e[a],f=k[a],g=sg(d)&&sg(f)?d-f:null,v=e[h],m=k[h],y=sg(v)&&sg(m)?v-m:0,x=c&&(null===(s=k.marker)||void 0===s?void 0:s.radius)||0,k.dist=Math.sqrt((g&&g*g||0)+y*y)-x,k.distX=sg(g)?Math.abs(g)-x:Number.MAX_VALUE;var T=(e[w]||0)-(k[w]||0)+(c&&(null===(M=k.marker)||void 0===M?void 0:M.radius)||0),P=T<0?"left":"right",O=T<0?"right":"left";return i[P]&&(S=(b=p(k,t(e,i[P],o+1,r),l))[0],A=b[1]),i[O]&&u(Math.sqrt(T*T),S[l],A)&&(S=p(S,t(e,i[O],o+1,r),l)[0]),S}(t,this.kdTree,d,d)},t.prototype.pointPlacementToXValue=function(){var t=this.options,e=this.xAxis,i=t.pointPlacement;return"between"===i&&(i=e.reversed?-.5:.5),sP(i)?i*(t.pointRange||e.pointRange):0},t.prototype.isPointInside=function(t){var e=this.chart,i=this.xAxis,o=this.yAxis,r=t.plotX,n=void 0===r?-1:r,s=t.plotY,a=void 0===s?-1:s;return a>=0&&a<=(o?o.len:e.plotHeight)&&n>=0&&n<=(i?i.len:e.plotWidth)},t.prototype.drawTracker=function(){var t,e=this,i=e.options,o=i.trackByArea,r=[].concat((o?e.areaPath:e.graphPath)||[]),n=e.chart,s=n.pointer,a=n.renderer,h=(null===(t=n.options.tooltip)||void 0===t?void 0:t.snap)||0,l=function(){i.enableMouseTracking&&n.hoverSeries!==e&&e.onMouseOver()},d="rgba(192,192,192,"+(ss?1e-4:.002)+")",c=e.tracker;c?c.attr({d:r}):e.graph&&(e.tracker=c=a.path(r).attr({visibility:e.visible?"inherit":"hidden",zIndex:2}).addClass(o?"highcharts-tracker-area":"highcharts-tracker-line").add(e.group),n.styledMode||c.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:d,fill:o?d:"none","stroke-width":e.graph.strokeWidth()+(o?0:2*h)}),[e.tracker,e.markerGroup,e.dataLabelsGroup].forEach(function(t){t&&(t.addClass("highcharts-tracker").on("mouseover",l).on("mouseout",function(t){null==s||s.onTrackerMouseOut(t)}),i.cursor&&!n.styledMode&&t.css({cursor:i.cursor}),t.on("touchstart",l))})),sk(this,"afterDrawTracker")},t.prototype.addPoint=function(t,e,i,o,r){var n,s,a=this.options,h=this.chart,l=this.data,d=this.dataTable,c=this.xAxis,p=(null==c?void 0:c.hasNames)&&c.names,u=a.data,f=this.getColumn("x");e=sL(e,!0);var g={series:this};this.pointClass.prototype.applyOptions.apply(g,[t]);var v=g.x;if(s=f.length,this.requireSorting&&v<f[s-1])for(n=!0;s&&f[s-1]>v;)s--;d.setRow(g,s,!0,{addColumns:!1}),p&&g.name&&(p[v]=g.name),null==u||u.splice(s,0,t),(n||this.processedData)&&(this.data.splice(s,0,null),this.processData()),"point"===a.legendType&&this.generatePoints(),i&&(l[0]&&l[0].remove?l[0].remove(!1):([l,u].filter(sg).forEach(function(t){t.shift()}),d.deleteRows(0))),!1!==r&&sk(this,"addPoint",{point:g}),this.isDirty=!0,this.isDirtyData=!0,e&&h.redraw(o)},t.prototype.removePoint=function(t,e,i){var o=this,r=o.chart,n=o.data,s=o.points,a=o.dataTable,h=n[t],l=function(){[(null==s?void 0:s.length)===n.length?s:void 0,n,o.options.data].filter(sg).forEach(function(e){e.splice(t,1)}),a.deleteRows(t),null==h||h.destroy(),o.isDirty=!0,o.isDirtyData=!0,e&&r.redraw()};eE(i,r),e=sL(e,!0),h?h.firePointEvent("remove",null,l):l()},t.prototype.remove=function(t,e,i,o){var r=this,n=r.chart;function s(){r.destroy(o),n.isDirtyLegend=n.isDirtyBox=!0,n.linkSeries(o),sL(t,!0)&&n.redraw(e)}!1!==i?sk(r,"remove",null,s):s()},t.prototype.update=function(e,i){sk(this,"update",{options:e=sm(e,this.userOptions)});var o,r,n,s,a,h,l=this,d=l.chart,c=l.userOptions,p=l.initialType||l.type,u=d.options.plotOptions,f=sh[p].prototype,g=l.finishedAnimating&&{animation:!1},v={},m=t.keepProps.slice(),y=e.type||c.type||d.options.chart.type,x=!(this.hasDerivedData||y&&y!==this.type||void 0!==e.keys||void 0!==e.pointStart||void 0!==e.pointInterval||void 0!==e.relativeXValue||e.joinBy||e.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(function(t){return l.hasOptionChanged(t)}));y=y||p,x?(m.push.apply(m,t.keepPropsForPoints),!1!==e.visible&&m.push("area","graph"),l.parallelArrays.forEach(function(t){m.push(t+"Data")}),e.data&&(e.dataSorting&&sb(l.options.dataSorting,e.dataSorting),this.setData(e.data,!1))):this.dataTable.modified=this.dataTable,e=sC(c,{index:void 0===c.index?l.index:c.index,pointStart:null!==(n=null!==(r=null===(o=null==u?void 0:u.series)||void 0===o?void 0:o.pointStart)&&void 0!==r?r:c.pointStart)&&void 0!==n?n:l.getColumn("x")[0]},!x&&{data:l.options.data},e,g),x&&e.data&&(e.data=l.options.data),(m=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(m)).forEach(function(t){m[t]=l[t],delete l[t]});var b=!1;if(sh[y]){if(b=y!==l.type,l.remove(!1,!1,!1,!0),b){if(d.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(l,sh[y].prototype);else{var M=Object.hasOwnProperty.call(l,"hcEvents")&&l.hcEvents;for(h in f)l[h]=void 0;sb(l,sh[y].prototype),M?l.hcEvents=M:delete l.hcEvents}}}else sx(17,!0,d,{missingModuleFor:y});if(m.forEach(function(t){l[t]=m[t]}),l.init(d,e),x&&this.points){!1===(a=l.options).visible?(v.graphic=1,v.dataLabel=1):(this.hasMarkerChanged(a,c)&&(v.graphic=1),(null===(s=l.hasDataLabels)||void 0===s?void 0:s.call(l))||(v.dataLabel=1));for(var k=0,w=this.points;k<w.length;k++){var S=w[k];(null==S?void 0:S.series)&&(S.resolveColor(),Object.keys(v).length&&S.destroyElements(v),!1===a.showInLegend&&S.legendItem&&d.legend.destroyItem(S))}}l.initialType=p,d.linkSeries(),d.setSortedData(),b&&l.linkedSeries.length&&(l.isDirtyData=!0),sk(this,"afterUpdate"),sL(i,!0)&&d.redraw(!!x&&void 0)},t.prototype.setName=function(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0},t.prototype.hasOptionChanged=function(t){var e,i,o=this.chart,r=this.options[t],n=o.options.plotOptions,s=this.userOptions[t],a=sL(null===(e=null==n?void 0:n[this.type])||void 0===e?void 0:e[t],null===(i=null==n?void 0:n.series)||void 0===i?void 0:i[t]);return s&&!sg(a)?r!==s:r!==sL(a,r)},t.prototype.onMouseOver=function(){var t=this.chart,e=t.hoverSeries,i=t.pointer;null==i||i.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&sk(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},t.prototype.onMouseOut=function(){var t=this.options,e=this.chart,i=e.tooltip,o=e.hoverPoint;e.hoverSeries=null,o&&o.onMouseOut(),this&&t.events.mouseOut&&sk(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(t){t.setState("",!0)})},t.prototype.setState=function(t,e){var i=this,o=i.options,r=i.graph,n=o.inactiveOtherPoints,s=o.states,a=sL(s[t||"normal"]&&s[t||"normal"].animation,i.chart.options.chart.animation),h=o.lineWidth,l=o.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(s[t]&&!1===s[t].enabled)return;if(t&&(h=s[t].lineWidth||h+(s[t].lineWidthPlus||0),l=sL(s[t].opacity,l)),r&&!r.dashstyle&&sP(h))for(var d=0,c=sr([r],this.zones.map(function(t){return t.graph}),!0);d<c.length;d++){var p=c[d];null==p||p.animate({"stroke-width":h},a)}n||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:l},a)})}e&&n&&i.points&&i.setAllPointsToState(t||void 0)},t.prototype.setAllPointsToState=function(t){this.points.forEach(function(e){e.setState&&e.setState(t)})},t.prototype.setVisible=function(t,e){var i,o=this,r=o.chart,n=r.options.chart.ignoreHiddenSeries,s=o.visible;o.visible=t=o.options.visible=o.userOptions.visible=void 0===t?!s:t;var a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(function(t){var e;null===(e=o[t])||void 0===e||e[a]()}),(r.hoverSeries===o||(null===(i=r.hoverPoint)||void 0===i?void 0:i.series)===o)&&o.onMouseOut(),o.legendItem&&r.legend.colorizeItem(o,t),o.isDirty=!0,o.options.stacking&&r.series.forEach(function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),o.linkedSeries.forEach(function(e){e.setVisible(t,!1)}),n&&(r.isDirtyBox=!0),sk(o,a),!1!==e&&r.redraw()},t.prototype.show=function(){this.setVisible(!0)},t.prototype.hide=function(){this.setVisible(!1)},t.prototype.select=function(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),sk(this,t?"select":"unselect")},t.prototype.shouldShowTooltip=function(t,e,i){return void 0===i&&(i={}),i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)},t.prototype.drawLegendSymbol=function(t,e){var i;null===(i=n9[this.options.legendSymbol||"rectangle"])||void 0===i||i.call(this,t,e)},t.defaultOptions=n4,t.types=si.seriesTypes,t.registerType=si.registerSeriesType,t.keepProps=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],t.keepPropsForPoints=["data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"],t}();sb(sD.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:nP,requireSorting:!0,sorted:!0}),si.series=sD;var sz=oV.registerEventOptions,sR=tu.composed,sN=tu.marginNames,sW=it.distribute,sG=e5.format,sX=tG.addEvent,sF=tG.createElement,sH=tG.css,sj=tG.defined,sY=tG.discardElement,s_=tG.find,sU=tG.fireEvent,sV=tG.isNumber,sq=tG.merge,sZ=tG.pick,sK=tG.pushUnique,s$=tG.relativeLength,sJ=tG.stableSort,sQ=tG.syncTimeout,s0=function(){function t(t,e){var i=this;this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),sz(this,e),sX(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),sX(this.chart,"render",function(){i.options.enabled&&i.proximate&&(i.proximatePositions(),i.positionItems())})}return t.prototype.setOptions=function(t){var e=sZ(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=sq(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=sZ(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted,this.baseline=void 0},t.prototype.update=function(t,e){var i=this.chart;this.setOptions(sq(!0,this.options,t)),"events"in this.options&&sz(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,sZ(e,!0)&&i.redraw(),sU(this,"afterUpdate",{redraw:e})},t.prototype.colorizeItem=function(t,e){var i,o=t.color,r=t.legendItem||{},n=r.area,s=r.group,a=r.label,h=r.line,l=r.symbol;if((t instanceof sD||t instanceof nP)&&(t.color=(null===(i=t.options)||void 0===i?void 0:i.legendSymbolColor)||o),null==s||s[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){var d=this.itemHiddenStyle,c=void 0===d?{}:d,p=c.color,u=t.options,f=u.fillColor,g=u.fillOpacity,v=u.lineColor,m=u.marker,y=function(t){return!e&&(t.fill&&(t.fill=p),t.stroke&&(t.stroke=p)),t};null==a||a.css(sq(e?this.itemStyle:c)),null==h||h.attr(y({stroke:v||t.color})),l&&l.attr(y(m&&l.isMarker?t.pointAttribs():{fill:t.color})),null==n||n.attr(y({fill:f||t.color,"fill-opacity":f?1:null!=g?g:.75}))}t.color=o,sU(this,"afterColorizeItem",{item:t,visible:e})},t.prototype.positionItems=function(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()},t.prototype.positionItem=function(t){var e=this,i=t.legendItem||{},o=i.group,r=i.x,n=void 0===r?0:r,s=i.y,a=void 0===s?0:s,h=this.options,l=h.symbolPadding,d=!h.rtl,c=t.checkbox;if(null==o?void 0:o.element){var p={translateX:d?n:this.legendWidth-n-2*l-4,translateY:a};o[sj(o.translateY)?"animate":"attr"](p,void 0,function(){sU(e,"afterPositionItem",{item:t})})}c&&(c.x=n,c.y=a)},t.prototype.destroyItem=function(t){for(var e=t.checkbox,i=t.legendItem||{},o=0,r=["group","label","line","symbol"];o<r.length;o++){var n=r[o];i[n]&&(i[n]=i[n].destroy())}e&&sY(e),t.legendItem=void 0},t.prototype.destroy=function(){for(var t=0,e=this.getAllItems();t<e.length;t++){var i=e[t];this.destroyItem(i)}for(var o=0,r=["clipRect","up","down","pager","nav","box","title","group"];o<r.length;o++){var n=r[o];this[n]&&(this[n]=this[n].destroy())}this.display=null},t.prototype.positionCheckboxes=function(){var t,e,i=null===(t=this.group)||void 0===t?void 0:t.alignAttr,o=this.clipHeight||this.legendHeight,r=this.titleHeight;i&&(e=i.translateY,this.allItems.forEach(function(t){var n,s=t.checkbox;s&&(n=e+r+s.y+(this.scrollOffset||0)+3,sH(s,{left:i.translateX+t.checkboxOffset+s.x-20+"px",top:n+"px",display:this.proximate||n>e-6&&n<e+o-6?"":"none"}))},this))},t.prototype.renderTitle=function(){var t,e=this.options,i=this.padding,o=e.title,r=0;o.text&&(this.title||(this.title=this.chart.renderer.label(o.text,i-3,i-4,void 0,void 0,void 0,e.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(o.style),this.title.add(this.group)),o.width||this.title.css({width:this.maxLegendWidth+"px"}),r=(t=this.title.getBBox()).height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:r})),this.titleHeight=r},t.prototype.setText=function(t){var e=this.options;t.legendItem.label.attr({text:e.labelFormat?sG(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})},t.prototype.renderItem=function(t){var e,i=t.legendItem=t.legendItem||{},o=this.chart,r=o.renderer,n=this.options,s="horizontal"===n.layout,a=this.symbolWidth,h=n.symbolPadding||0,l=this.itemStyle,d=this.itemHiddenStyle,c=s?sZ(n.itemDistance,20):0,p=!n.rtl,u=!t.series,f=!u&&t.series.drawLegendSymbol?t.series:t,g=f.options,v=!!this.createCheckboxForItem&&g&&g.showCheckbox,m=n.useHTML,y=t.options.className,x=i.label,b=a+h+c+20*!!v;!x&&(i.group=r.g("legend-item").addClass("highcharts-"+f.type+"-series highcharts-color-"+t.colorIndex+(y?" "+y:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),i.label=x=r.text("",p?a+h:-h,this.baseline||0,m),o.styledMode||x.css(sq(t.visible?l:d)),x.attr({align:p?"left":"right",zIndex:2}).add(i.group),!this.baseline&&(this.fontMetrics=r.fontMetrics(x),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,x.attr("y",this.baseline),this.symbolHeight=sZ(n.symbolHeight,this.fontMetrics.f),n.squareSymbol&&(this.symbolWidth=sZ(n.symbolWidth,Math.max(this.symbolHeight,16)),b=this.symbolWidth+h+c+20*!!v,p&&x.attr("x",this.symbolWidth+h))),f.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,x,m)),v&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(o.styledMode||!l.width)&&x.css({width:(n.itemWidth||this.widthOption||o.spacingBox.width)-b+"px"}),this.setText(t);var M=x.getBBox(),k=(null===(e=this.fontMetrics)||void 0===e?void 0:e.h)||0;t.itemWidth=t.checkboxOffset=n.itemWidth||i.labelWidth||M.width+b,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(i.labelHeight||(M.height>1.5*k?M.height:k))},t.prototype.layoutItem=function(t){var e=this.options,i=this.padding,o="horizontal"===e.layout,r=t.itemHeight,n=this.itemMarginBottom,s=this.itemMarginTop,a=o?sZ(e.itemDistance,20):0,h=this.maxLegendWidth,l=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth,d=t.legendItem||{};o&&this.itemX-i+l>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=s+this.lastLineHeight+n),this.lastLineHeight=0),this.lastItemY=s+this.itemY+n,this.lastLineHeight=Math.max(r,this.lastLineHeight),d.x=this.itemX,d.y=this.itemY,o?this.itemX+=l:(this.itemY+=s+r+n,this.lastLineHeight=r),this.offsetWidth=this.widthOption||Math.max((o?this.itemX-i-(t.checkbox?0:a):l)+i,this.offsetWidth)},t.prototype.getAllItems=function(){var t=[];return this.chart.series.forEach(function(e){var i,o=null==e?void 0:e.options;e&&sZ(o.showInLegend,!sj(o.linkedTo)&&void 0,!0)&&(t=t.concat((null===(i=e.legendItem)||void 0===i?void 0:i.labels)||("point"===o.legendType?e.data:e)))}),sU(this,"afterGetAllItems",{allItems:t}),t},t.prototype.getAlignment=function(){var t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)},t.prototype.adjustMargins=function(t,e){var i=this.chart,o=this.options,r=this.getAlignment();r&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(n,s){n.test(r)&&!sj(t[s])&&(i[sN[s]]=Math.max(i[sN[s]],i.legend[(s+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][s]*o[s%2?"x":"y"]+sZ(o.margin,12)+e[s]+(i.titleOffset[s]||0)))})},t.prototype.proximatePositions=function(){var t,e=this.chart,i=[],o="left"===this.options.align;this.allItems.forEach(function(t){var r,n,s,a,h=o;t.yAxis&&(t.xAxis.options.reversed&&(h=!h),t.points&&(r=s_(h?t.points:t.points.slice(0).reverse(),function(t){return sV(t.plotY)})),n=this.itemMarginTop+t.legendItem.label.getBBox().height+this.itemMarginBottom,a=t.yAxis.top-e.plotTop,s=t.visible?(r?r.plotY:t.yAxis.height)+(a-.3*n):a+t.yAxis.height,i.push({target:s,size:n,item:t}))},this);for(var r=0,n=sW(i,e.plotHeight);r<n.length;r++){var s=n[r];t=s.item.legendItem||{},sV(s.pos)&&(t.y=e.plotTop-e.spacing[0]+s.pos)}},t.prototype.render=function(){var t,e,i,o,r=this.chart,n=r.renderer,s=this.options,a=this.padding,h=this.getAllItems(),l=this.group,d=this.box;this.itemX=a,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=s$(s.width,r.spacingBox.width-a),o=r.spacingBox.width-2*a-s.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(o/=2),this.maxLegendWidth=this.widthOption||o,l||(this.group=l=n.g("legend").addClass(s.className||"").attr({zIndex:7}).add(),this.contentGroup=n.g().attr({zIndex:1}).add(l),this.scrollGroup=n.g().add(this.contentGroup)),this.renderTitle(),sJ(h,function(t,e){var i,o;return((null===(i=t.options)||void 0===i?void 0:i.legendIndex)||0)-((null===(o=e.options)||void 0===o?void 0:o.legendIndex)||0)}),s.reversed&&h.reverse(),this.allItems=h,this.display=t=!!h.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,h.forEach(this.renderItem,this),h.forEach(this.layoutItem,this),e=(this.widthOption||this.offsetWidth)+a,i=this.lastItemY+this.lastLineHeight+this.titleHeight,i=this.handleOverflow(i)+a,d||(this.box=d=n.rect().addClass("highcharts-legend-box").attr({r:s.borderRadius}).add(l)),r.styledMode||d.attr({stroke:s.borderColor,"stroke-width":s.borderWidth||0,fill:s.backgroundColor||"none"}).shadow(s.shadow),e>0&&i>0&&d[d.placed?"animate":"attr"](d.crisp.call({},{x:0,y:0,width:e,height:i},d.strokeWidth())),l[t?"show":"hide"](),r.styledMode&&"none"===l.getStyle("display")&&(e=i=0),this.legendWidth=e,this.legendHeight=i,t&&this.align(),this.proximate||this.positionItems(),sU(this,"afterRender")},t.prototype.align=function(t){void 0===t&&(t=this.chart.spacingBox);var e=this.chart,i=this.options,o=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?o+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(o-=e.titleOffset[2]),o!==t.y&&(t=sq(t,{y:o})),e.hasRendered||(this.group.placed=!1),this.group.align(sq(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)},t.prototype.handleOverflow=function(t){var e,i,o,r,n=this,s=this.chart,a=s.renderer,h=this.options,l=h.y,d="top"===h.verticalAlign,c=this.padding,p=h.maxHeight,u=h.navigation,f=sZ(u.animation,!0),g=u.arrowSize||12,v=this.pages,m=this.allItems,y=function(t){"number"==typeof t?k.attr({height:t}):k&&(n.clipRect=k.destroy(),n.contentGroup.clip()),n.contentGroup.div&&(n.contentGroup.div.style.clip=t?"rect("+c+"px,9999px,"+(c+t)+"px,0)":"auto")},x=function(t){return n[t]=a.circle(0,0,1.3*g).translate(g/2,g/2).add(M),s.styledMode||n[t].attr("fill","rgba(0,0,0,0.0001)"),n[t]},b=s.spacingBox.height+(d?-l:l)-c,M=this.nav,k=this.clipRect;return"horizontal"!==h.layout||"middle"===h.verticalAlign||h.floating||(b/=2),p&&(b=Math.min(b,p)),v.length=0,t&&b>0&&t>b&&!1!==u.enabled?(this.clipHeight=e=Math.max(b-20-this.titleHeight-c,0),this.currentPage=sZ(this.currentPage,1),this.fullHeight=t,m.forEach(function(t,n){var s=(o=t.legendItem||{}).y||0,a=Math.round(o.label.getBBox().height),h=v.length;(!h||s-v[h-1]>e&&(i||s)!==v[h-1])&&(v.push(i||s),h++),o.pageIx=h-1,i&&r&&(r.pageIx=h-1),n===m.length-1&&s+a-v[h-1]>e&&s>v[h-1]&&(v.push(s),o.pageIx=h),s!==i&&(i=s),r=o}),k||(k=n.clipRect=a.clipRect(0,c-2,9999,0),n.contentGroup.clip(k)),y(e),M||(this.nav=M=a.g().attr({zIndex:1}).add(this.group),this.up=a.symbol("triangle",0,0,g,g).add(M),x("upTracker").on("click",function(){n.scroll(-1,f)}),this.pager=a.text("",15,10).addClass("highcharts-legend-navigation"),!s.styledMode&&u.style&&this.pager.css(u.style),this.pager.add(M),this.down=a.symbol("triangle-down",0,0,g,g).add(M),x("downTracker").on("click",function(){n.scroll(1,f)})),n.scroll(0),t=b):M&&(y(),this.nav=M.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},t.prototype.scroll=function(t,e){var i=this,o=this.chart,r=this.pages,n=r.length,s=this.clipHeight,a=this.options.navigation,h=this.pager,l=this.padding,d=this.currentPage+t;d>n&&(d=n),d>0&&(void 0!==e&&eE(e,o),this.nav.attr({translateX:l,translateY:s+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(t){t.attr({class:1===d?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),h.attr({text:d+"/"+n}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,class:d===n?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),o.styledMode||(this.up.attr({fill:1===d?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:1===d?"default":"pointer"}),this.down.attr({fill:d===n?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:d===n?"default":"pointer"})),this.scrollOffset=-r[d-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=d,this.positionCheckboxes(),sQ(function(){sU(i,"afterScroll",{currentPage:d})},eT(sZ(e,o.renderer.globalAnimation,!0)).duration))},t.prototype.setItemEvents=function(t,e,i){for(var o=this,r=t.legendItem||{},n=o.chart.renderer.boxWrapper,s=t instanceof nP,a=t instanceof sD,h="highcharts-legend-"+(s?"point":"series")+"-active",l=o.chart.styledMode,d=i?[e,r.symbol]:[r.group],c=function(e){o.allItems.forEach(function(i){t!==i&&[i].concat(i.linkedSeries||[]).forEach(function(t){t.setState(e,!s)})})},p=0;p<d.length;p++){var u=d[p];u&&u.on("mouseover",function(){t.visible&&c("inactive"),t.setState("hover"),t.visible&&n.addClass(h),l||e.css(o.options.itemHoverStyle)}).on("mouseout",function(){o.chart.styledMode||e.css(sq(t.visible?o.itemStyle:o.itemHiddenStyle)),c(""),n.removeClass(h),t.setState()}).on("click",function(e){var i=function(){t.setVisible&&t.setVisible(),c(t.visible?"inactive":"")};n.removeClass(h),sU(o,"itemClick",{browserEvent:e,legendItem:t},i),s?t.firePointEvent("legendItemClick",{browserEvent:e}):a&&sU(t,"legendItemClick",{browserEvent:e})})}},t.prototype.createCheckboxForItem=function(t){t.checkbox=sF("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),sX(t.checkbox,"click",function(e){var i=e.target;sU(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})},t}();(y=s0||(s0={})).compose=function(t){sK(sR,"Core.Legend")&&sX(t,"beforeMargins",function(){this.legend=new y(this,this.options.legend)})};var s1=s0,s2=function(){return(s2=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},s3=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},s5=e5.numberFormat,s6=oV.registerEventOptions,s9=tu.charts,s4=tu.doc,s8=tu.marginNames,s7=tu.svg,at=tu.win,ae=si.seriesTypes,ai=tG.addEvent,ao=tG.attr,ar=tG.createElement,an=tG.css,as=tG.defined,aa=tG.diffObjects,ah=tG.discardElement,al=tG.erase,ad=tG.error,ac=tG.extend,ap=tG.find,au=tG.fireEvent,af=tG.getAlignFactor,ag=tG.getStyle,av=tG.isArray,am=tG.isNumber,ay=tG.isObject,ax=tG.isString,ab=tG.merge,aM=tG.objectEach,ak=tG.pick,aw=tG.pInt,aS=tG.relativeLength,aA=tG.removeEvent,aT=tG.splat,aP=tG.syncTimeout,aO=tG.uniqueKey,aC=function(){function t(t,e,i){this.sharedClips={};var o=s3([],arguments,!0);(ax(t)||t.nodeName)&&(this.renderTo=o.shift()),this.init(o[0],o[1])}return t.chart=function(e,i,o){return new t(e,i,o)},t.prototype.setZoomOptions=function(){var t=this.options.chart,e=t.zooming;this.zooming=s2(s2({},e),{type:ak(t.zoomType,e.type),key:ak(t.zoomKey,e.key),pinchType:ak(t.pinchType,e.pinchType),singleTouch:ak(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:ab(e.resetButton,t.resetZoomButton)})},t.prototype.init=function(t,e){au(this,"init",{args:arguments},function(){var i,o,r=ab(ee,t),n=r.chart,s=this.renderTo||n.renderTo;this.userOptions=ac({},t),(this.renderTo=ax(s)?s4.getElementById(s):s)||ad(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=r,this.axes=[],this.series=[],this.locale=null!==(i=r.lang.locale)&&void 0!==i?i:null===(o=this.renderTo.closest("[lang]"))||void 0===o?void 0:o.lang,this.time=new t9(ac(r.time||{},{locale:this.locale}),r.lang),r.time=this.time.options,this.numberFormatter=(n.numberFormatter||s5).bind(this),this.styledMode=n.styledMode,this.hasCartesianSeries=n.showAxes,this.index=s9.length,s9.push(this),tu.chartCount++,s6(this,n),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),au(this,"afterInit"),this.firstRender()})},t.prototype.initSeries=function(t){var e=this.options.chart,i=t.type||e.type,o=ae[i];o||ad(17,!0,this,{missingModuleFor:i});var r=new o;return"function"==typeof r.init&&r.init(this,t),r},t.prototype.setSortedData=function(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})},t.prototype.getSeriesOrderByLinks=function(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})},t.prototype.orderItems=function(t,e){void 0===e&&(e=0);var i=this[t],o=this.options[t]=aT(this.options[t]).slice(),r=this.userOptions[t]=this.userOptions[t]?aT(this.userOptions[t]).slice():[];if(this.hasRendered&&(o.splice(e),r.splice(e)),i)for(var n=e,s=i.length;n<s;++n){var a=i[n];a&&(a.index=n,a instanceof sD&&(a.name=a.getName()),a.options.isInternal||(o[n]=a.options,r[n]=a.userOptions))}},t.prototype.getClipBox=function(t,e){var i,o,r,n,s,a=this.inverted,h=t||{},l=h.xAxis,d=h.yAxis,c=ab(this.clipBox),p=c.x,u=c.y,f=c.width,g=c.height;return t&&(l&&l.len!==this.plotSizeX&&(f=l.len),d&&d.len!==this.plotSizeY&&(g=d.len),a&&!t.invertible&&(f=(i=[g,f])[0],g=i[1])),e&&(p+=null!==(r=null===(o=a?d:l)||void 0===o?void 0:o.pos)&&void 0!==r?r:this.plotLeft,u+=null!==(s=null===(n=a?l:d)||void 0===n?void 0:n.pos)&&void 0!==s?s:this.plotTop),{x:p,y:u,width:f,height:g}},t.prototype.isInsidePlot=function(t,e,i){void 0===i&&(i={});var o,r=this.inverted,n=this.plotBox,s=this.plotLeft,a=this.plotTop,h=this.scrollablePlotBox,l=i.visiblePlotOnly&&(null===(o=this.scrollablePlotArea)||void 0===o?void 0:o.scrollingContainer)||{},d=l.scrollLeft,c=void 0===d?0:d,p=l.scrollTop,u=void 0===p?0:p,f=i.series,g=i.visiblePlotOnly&&h||n,v=i.inverted?e:t,m=i.inverted?t:e,y={x:v,y:m,isInsidePlot:!0,options:i};if(!i.ignoreX){var x=f&&(r&&!this.polar?f.yAxis:f.xAxis)||{pos:s,len:1/0},b=i.paneCoordinates?x.pos+v:s+v;b>=Math.max(c+s,x.pos)&&b<=Math.min(c+s+g.width,x.pos+x.len)||(y.isInsidePlot=!1)}if(!i.ignoreY&&y.isInsidePlot){var M=!r&&i.axis&&!i.axis.isXAxis&&i.axis||f&&(r?f.xAxis:f.yAxis)||{pos:a,len:1/0},k=i.paneCoordinates?M.pos+m:a+m;k>=Math.max(u+a,M.pos)&&k<=Math.min(u+a+g.height,M.pos+M.len)||(y.isInsidePlot=!1)}return au(this,"afterIsInsidePlot",y),y.isInsidePlot},t.prototype.redraw=function(t){au(this,"beforeRedraw");var e,i,o,r,n=this.hasCartesianSeries?this.axes:this.colorAxis||[],s=this.series,a=this.pointer,h=this.legend,l=this.userOptions.legend,d=this.renderer,c=d.isHidden(),p=[],u=this.isDirtyBox,f=this.isDirtyLegend;for(d.rootFontSize=d.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),eE(!!this.hasRendered&&t,this),c&&this.temporaryDisplay(),this.layOutTitles(!1),o=s.length;o--;)if(((r=s[o]).options.stacking||r.options.centerInCategory)&&(i=!0,r.isDirty)){e=!0;break}if(e)for(o=s.length;o--;)(r=s[o]).options.stacking&&(r.isDirty=!0);s.forEach(function(t){t.isDirty&&("point"===t.options.legendType?("function"==typeof t.updateTotals&&t.updateTotals(),f=!0):l&&(l.labelFormatter||l.labelFormat)&&(f=!0)),t.isDirtyData&&au(t,"updatedData")}),f&&h&&h.options.enabled&&(h.render(),this.isDirtyLegend=!1),i&&this.getStacks(),n.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),n.forEach(function(t){t.isDirty&&(u=!0)}),n.forEach(function(t){var e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,p.push(function(){au(t,"afterSetExtremes",ac(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(u||i)&&t.redraw()}),u&&this.drawChartBox(),au(this,"predraw"),s.forEach(function(t){(u||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),a&&a.reset(!0),d.draw(),au(this,"redraw"),au(this,"render"),c&&this.temporaryDisplay(!0),p.forEach(function(t){t.call()})},t.prototype.get=function(t){var e=this.series;function i(e){return e.id===t||e.options&&e.options.id===t}for(var o=ap(this.axes,i)||ap(this.series,i),r=0;!o&&r<e.length;r++)o=ap(e[r].points||[],i);return o},t.prototype.createAxes=function(){var t=this.userOptions;au(this,"createAxes");for(var e=0,i=["xAxis","yAxis"];e<i.length;e++)for(var o=i[e],r=t[o]=aT(t[o]||{}),n=0;n<r.length;n++)new rA(this,r[n],o);au(this,"afterCreateAxes")},t.prototype.getSelectedPoints=function(){return this.series.reduce(function(t,e){return e.getPointsCollection().forEach(function(e){ak(e.selectedStaging,e.selected)&&t.push(e)}),t},[])},t.prototype.getSelectedSeries=function(){return this.series.filter(function(t){return t.selected})},t.prototype.setTitle=function(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)},t.prototype.applyDescription=function(t,e){var i,o=this,r=this.options[t]=ab(this.options[t],e),n=this[t];n&&e&&(this[t]=n=n.destroy()),r&&!n&&((n=this.renderer.text(r.text,0,0,r.useHTML).attr({align:r.align,class:"highcharts-"+t,zIndex:r.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(e,i){o.applyDescription(t,e),o.layOutTitles(i)},this.styledMode||n.css(ac("title"===t?{fontSize:this.options.isStock?"1em":"1.2em"}:{},r.style)),n.textPxLength=n.getBBox().width,n.css({whiteSpace:null===(i=r.style)||void 0===i?void 0:i.whiteSpace}),this[t]=n)},t.prototype.layOutTitles=function(t){var e,i,o,r,n=this;void 0===t&&(t=!0);var s=[0,0,0],a=this.options,h=this.renderer,l=this.spacingBox;["title","subtitle","caption"].forEach(function(t){var e,i=n[t],o=n.options[t],r=ab(l),a=(null==i?void 0:i.textPxLength)||0;if(i&&o){au(n,"layOutTitle",{alignTo:r,key:t,textPxLength:a});var d=h.fontMetrics(i),c=d.b,p=d.h,u=o.verticalAlign||"top",f="top"===u,g=f&&o.minScale||1,v="title"===t?f?-3:0:f?s[0]+2:0,m=Math.min(r.width/a,1),y=Math.max(g,m),x=ab({y:"bottom"===u?c:v+c},{align:"title"===t?m<g?"left":"center":null===(e=n.title)||void 0===e?void 0:e.alignValue},o),b=(o.width||(m>g?n.chartWidth:r.width)/y)+"px";i.alignValue!==x.align&&(i.placed=!1);var M=Math.round(i.css({width:b}).getBBox(o.useHTML).height);if(x.height=M,i.align(x,!1,r).attr({align:x.align,scaleX:y,scaleY:y,"transform-origin":""+(r.x+a*y*af(x.align))+" ".concat(p)}),!o.floating){var k=M*(M<1.2*p?1:y);"top"===u?s[0]=Math.ceil(s[0]+k):"bottom"===u&&(s[2]=Math.ceil(s[2]+k))}}},this),s[0]&&"top"===((null===(e=a.title)||void 0===e?void 0:e.verticalAlign)||"top")&&(s[0]+=(null===(i=a.title)||void 0===i?void 0:i.margin)||0),s[2]&&(null===(o=a.caption)||void 0===o?void 0:o.verticalAlign)==="bottom"&&(s[2]+=(null===(r=a.caption)||void 0===r?void 0:r.margin)||0);var d=!this.titleOffset||this.titleOffset.join(",")!==s.join(",");this.titleOffset=s,au(this,"afterLayOutTitles"),!this.isDirtyBox&&d&&(this.isDirtyBox=this.isDirtyLegend=d,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())},t.prototype.getContainerBox=function(){var t=this,e=[].map.call(this.renderTo.children,function(e){if(e!==t.container){var i=e.style.display;return e.style.display="none",[e,i]}}),i={width:ag(this.renderTo,"width",!0)||0,height:ag(this.renderTo,"height",!0)||0};return e.filter(Boolean).forEach(function(t){var e=t[0],i=t[1];e.style.display=i}),i},t.prototype.getChartSize=function(){var t,e=this.options.chart,i=e.width,o=e.height,r=this.getContainerBox(),n=r.height<=1||!(null===(t=this.renderTo.parentElement)||void 0===t?void 0:t.style.height)&&"100%"===this.renderTo.style.height;this.chartWidth=Math.max(0,i||r.width||600),this.chartHeight=Math.max(0,aS(o,this.chartWidth)||(n?400:r.height)),this.containerBox=r},t.prototype.temporaryDisplay=function(t){var e,i=this.renderTo;if(t)for(;null==i?void 0:i.style;)i.hcOrigStyle&&(an(i,i.hcOrigStyle),delete i.hcOrigStyle),i.hcOrigDetached&&(s4.body.removeChild(i),i.hcOrigDetached=!1),i=i.parentNode;else for(;(null==i?void 0:i.style)&&(s4.body.contains(i)||i.parentNode||(i.hcOrigDetached=!0,s4.body.appendChild(i)),("none"===ag(i,"display",!1)||i.hcOricDetached)&&(i.hcOrigStyle={display:i.style.display,height:i.style.height,overflow:i.style.overflow},e={display:"block",overflow:"hidden"},i!==this.renderTo&&(e.height=0),an(i,e),i.offsetWidth||i.style.setProperty("display","block","important")),(i=i.parentNode)!==s4.body););},t.prototype.setClassName=function(t){this.container.className="highcharts-container "+(t||"")},t.prototype.getContainer=function(){var t,e,i,o=this.options,r=o.chart,n="data-highcharts-chart",s=aO(),a=this.renderTo,h=aw(ao(a,n));am(h)&&s9[h]&&s9[h].hasRendered&&s9[h].destroy(),ao(a,n,this.index),a.innerHTML=eY.emptyHTML,r.skipClone||a.offsetWidth||this.temporaryDisplay(),this.getChartSize();var l=this.chartHeight,d=this.chartWidth;an(a,{overflow:"hidden"}),this.styledMode||(i=ac({position:"relative",overflow:"hidden",width:d+"px",height:l+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},r.style||{}));var c=ar("div",{id:s},i,a);this.container=c,this.getChartSize(),d===this.chartWidth||(d=this.chartWidth,this.styledMode||an(c,{width:ak(null===(t=r.style)||void 0===t?void 0:t.width,d+"px")})),this.containerBox=this.getContainerBox(),this._cursor=c.style.cursor;var p=r.renderer||!s7?e6.getRendererType(r.renderer):oS;if(this.renderer=new p(c,d,l,void 0,r.forExport,null===(e=o.exporting)||void 0===e?void 0:e.allowHTML,this.styledMode),eE(void 0,this),this.setClassName(r.className),this.styledMode)for(var u in o.defs)this.renderer.definition(o.defs[u]);else this.renderer.setStyle(r.style);this.renderer.chartIndex=this.index,au(this,"afterGetContainer")},t.prototype.getMargins=function(t){var e,i=this.spacing,o=this.margin,r=this.titleOffset;this.resetMargins(),r[0]&&!as(o[0])&&(this.plotTop=Math.max(this.plotTop,r[0]+i[0])),r[2]&&!as(o[2])&&(this.marginBottom=Math.max(this.marginBottom,r[2]+i[2])),(null===(e=this.legend)||void 0===e?void 0:e.display)&&this.legend.adjustMargins(o,i),au(this,"getMargins"),t||this.getAxisMargins()},t.prototype.getAxisMargins=function(){var t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,o=t.margin,r=function(t){t.forEach(function(t){t.visible&&t.getOffset()})};t.hasCartesianSeries?r(t.axes):(null==i?void 0:i.length)&&r(i),s8.forEach(function(i,r){as(o[r])||(t[i]+=e[r])}),t.setChartSize()},t.prototype.getOptions=function(){return aa(this.userOptions,ee)},t.prototype.reflow=function(t){var e,i=this,o=i.containerBox,r=i.getContainerBox();null===(e=i.pointer)||void 0===e||delete e.chartPosition,!i.isPrinting&&!i.isResizing&&o&&r.width&&((r.width!==o.width||r.height!==o.height)&&(tG.clearTimeout(i.reflowTimeout),i.reflowTimeout=aP(function(){i.container&&i.setSize(void 0,void 0,!1)},100*!!t)),i.containerBox=r)},t.prototype.setReflow=function(){var t=this,e=function(e){var i;(null===(i=t.options)||void 0===i?void 0:i.chart.reflow)&&t.hasLoaded&&t.reflow(e)};if("function"==typeof ResizeObserver)new ResizeObserver(e).observe(t.renderTo);else{var i=ai(at,"resize",e);ai(this,"destroy",i)}},t.prototype.setSize=function(t,e,i){var o=this,r=o.renderer;o.isResizing+=1,eE(i,o);var n=r.globalAnimation;o.oldChartHeight=o.chartHeight,o.oldChartWidth=o.chartWidth,void 0!==t&&(o.options.chart.width=t),void 0!==e&&(o.options.chart.height=e),o.getChartSize();var s=o.chartWidth,a=o.chartHeight,h=o.scrollablePixelsX,l=o.scrollablePixelsY;(o.isDirtyBox||s!==o.oldChartWidth||a!==o.oldChartHeight)&&(o.styledMode||(n?eO:an)(o.container,{width:""+(s+(void 0===h?0:h))+"px",height:""+(a+(void 0===l?0:l))+"px"},n),o.setChartSize(!0),r.setSize(s,a,n),o.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),o.isDirtyLegend=!0,o.isDirtyBox=!0,o.layOutTitles(),o.getMargins(),o.redraw(n),o.oldChartHeight=void 0,au(o,"resize"),setTimeout(function(){o&&au(o,"endResize")},eT(n).duration)),o.isResizing-=1},t.prototype.setChartSize=function(t){var e,i,o,r,n,s,a=this.chartHeight,h=this.chartWidth,l=this.inverted,d=this.spacing,c=this.renderer,p=this.clipOffset,u=Math[l?"floor":"round"];this.plotLeft=o=Math.round(this.plotLeft),this.plotTop=r=Math.round(this.plotTop),this.plotWidth=n=Math.max(0,Math.round(h-o-(null!==(e=this.marginRight)&&void 0!==e?e:0))),this.plotHeight=s=Math.max(0,Math.round(a-r-(null!==(i=this.marginBottom)&&void 0!==i?i:0))),this.plotSizeX=l?s:n,this.plotSizeY=l?n:s,this.spacingBox=c.spacingBox={x:d[3],y:d[0],width:h-d[3]-d[1],height:a-d[0]-d[2]},this.plotBox=c.plotBox={x:o,y:r,width:n,height:s},p&&(this.clipBox={x:u(p[3]),y:u(p[0]),width:u(this.plotSizeX-p[1]-p[3]),height:u(this.plotSizeY-p[0]-p[2])}),t||(this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),c.alignElements()),au(this,"afterSetChartSize",{skipAxes:t})},t.prototype.resetMargins=function(){au(this,"resetMargins");var t=this,e=t.options.chart,i=e.plotBorderWidth||0,o=Math.round(i)/2;["margin","spacing"].forEach(function(i){var o=e[i],r=ay(o)?o:[o,o,o,o];["Top","Right","Bottom","Left"].forEach(function(o,n){t[i][n]=ak(e[i+o],r[n])})}),s8.forEach(function(e,i){t[e]=ak(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[o,o,o,o],t.plotBorderWidth=i},t.prototype.drawChartBox=function(){var t,e,i,o=this.options.chart,r=this.renderer,n=this.chartWidth,s=this.chartHeight,a=this.styledMode,h=this.plotBGImage,l=o.backgroundColor,d=o.plotBackgroundColor,c=o.plotBackgroundImage,p=this.plotLeft,u=this.plotTop,f=this.plotWidth,g=this.plotHeight,v=this.plotBox,m=this.clipRect,y=this.clipBox,x=this.chartBackground,b=this.plotBackground,M=this.plotBorder,k="animate";x||(this.chartBackground=x=r.rect().addClass("highcharts-background").add(),k="attr"),a?t=e=x.strokeWidth():(e=(t=o.borderWidth||0)+8*!!o.shadow,i={fill:l||"none"},(t||x["stroke-width"])&&(i.stroke=o.borderColor,i["stroke-width"]=t),x.attr(i).shadow(o.shadow)),x[k]({x:e/2,y:e/2,width:n-e-t%2,height:s-e-t%2,r:o.borderRadius}),k="animate",b||(k="attr",this.plotBackground=b=r.rect().addClass("highcharts-plot-background").add()),b[k](v),!a&&(b.attr({fill:d||"none"}).shadow(o.plotShadow),c&&(h?(c!==h.attr("href")&&h.attr("href",c),h.animate(v)):this.plotBGImage=r.image(c,p,u,f,g).add())),m?m.animate({width:y.width,height:y.height}):this.clipRect=r.clipRect(y),k="animate",M||(k="attr",this.plotBorder=M=r.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),a||M.attr({stroke:o.plotBorderColor,"stroke-width":o.plotBorderWidth||0,fill:"none"}),M[k](M.crisp(v,-M.strokeWidth())),this.isDirtyBox=!1,au(this,"afterDrawChartBox")},t.prototype.propFromSeries=function(){var t,e,i,o=this,r=o.options.chart,n=o.options.series;["inverted","angular","polar"].forEach(function(s){for(e=ae[r.type],i=r[s]||e&&e.prototype[s],t=null==n?void 0:n.length;!i&&t--;)(e=ae[n[t].type])&&e.prototype[s]&&(i=!0);o[s]=i})},t.prototype.linkSeries=function(t){var e=this,i=e.series;i.forEach(function(t){t.linkedSeries.length=0}),i.forEach(function(t){var i=t.options.linkedTo;if(ax(i)){var o=void 0;(o=":previous"===i?e.series[t.index-1]:e.get(i))&&o.linkedParent!==t&&(o.linkedSeries.push(t),t.linkedParent=o,o.enabledDataSorting&&t.setDataSortingOptions(),t.visible=ak(t.options.visible,o.options.visible,t.visible))}}),au(this,"afterLinkSeries",{isUpdating:t})},t.prototype.renderSeries=function(){this.series.forEach(function(t){t.translate(),t.render()})},t.prototype.render=function(){var t,e,i=this.axes,o=this.colorAxis,r=this.renderer,n=this.options.chart.axisLayoutRuns||2,s=function(t){t.forEach(function(t){t.visible&&t.render()})},a=0,h=!0,l=0;this.setTitle(),au(this,"beforeMargins"),null===(t=this.getStacks)||void 0===t||t.call(this),this.getMargins(!0),this.setChartSize();for(var d=0;d<i.length;d++){var c=i[d],p=c.options,u=p.labels;if(this.hasCartesianSeries&&c.horiz&&c.visible&&u.enabled&&c.series.length&&"colorAxis"!==c.coll&&!this.polar){a=p.tickLength,c.createGroups();var f=new o9(c,0,"",!0),g=f.createLabel("x",u);if(f.destroy(),g&&ak(u.reserveSpace,!am(p.crossing))&&(a=g.getBBox().height+u.distance+Math.max(p.offset||0,0)),a){null==g||g.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-a,0);(h||e||n>1)&&l<n;){for(var v=this.plotWidth,m=this.plotHeight,y=0;y<i.length;y++){var c=i[y];0===l?c.setScale():(c.horiz&&h||!c.horiz&&e)&&c.setTickInterval(!0)}0===l?this.getAxisMargins():this.getMargins(),h=v/this.plotWidth>(l?1:1.1),e=m/this.plotHeight>(l?1:1.05),l++}this.drawChartBox(),this.hasCartesianSeries?s(i):(null==o?void 0:o.length)&&s(o),this.seriesGroup||(this.seriesGroup=r.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0},t.prototype.addCredits=function(t){var e=this,i=ab(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(at.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})},t.prototype.destroy=function(){var t,e,i,o=this,r=o.axes,n=o.series,s=o.container,a=null==s?void 0:s.parentNode;for(au(o,"destroy"),o.renderer.forExport?al(s9,o):s9[o.index]=void 0,tu.chartCount--,o.renderTo.removeAttribute("data-highcharts-chart"),aA(o),i=r.length;i--;)r[i]=r[i].destroy();for(null===(e=null===(t=this.scroller)||void 0===t?void 0:t.destroy)||void 0===e||e.call(t),i=n.length;i--;)n[i]=n[i].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(function(t){var e,i;o[t]=null===(i=null===(e=o[t])||void 0===e?void 0:e.destroy)||void 0===i?void 0:i.call(e)}),s&&(s.innerHTML=eY.emptyHTML,aA(s),a&&ah(s)),aM(o,function(t,e){delete o[e]})},t.prototype.firstRender=function(){var t,e=this,i=e.options;e.getContainer(),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.createAxes();var o=av(i.series)?i.series:[];i.series=[],o.forEach(function(t){e.initSeries(t)}),e.linkSeries(),e.setSortedData(),au(e,"beforeRender"),e.render(),null===(t=e.pointer)||void 0===t||t.getChartPosition(),e.renderer.imgCount||e.hasLoaded||e.onload(),e.temporaryDisplay(!0)},t.prototype.onload=function(){this.callbacks.concat([this.callback]).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),au(this,"load"),au(this,"render"),as(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0},t.prototype.warnIfA11yModuleNotLoaded=function(){var t=this.options,e=this.title;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":((null==e?void 0:e.element.textContent)||"").replace(/</g,"&lt;")}),t.accessibility&&!1===t.accessibility.enabled||ad('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))},t.prototype.addSeries=function(t,e,i){var o,r=this;return t&&(e=ak(e,!0),au(r,"addSeries",{options:t},function(){o=r.initSeries(t),r.isDirtyLegend=!0,r.linkSeries(),o.enabledDataSorting&&o.setData(t.data,!1),au(r,"afterAddSeries",{series:o}),e&&r.redraw(i)})),o},t.prototype.addAxis=function(t,e,i,o){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:o})},t.prototype.addColorAxis=function(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})},t.prototype.createAxis=function(t,e){var i=new rA(this,e.axis,t);return ak(e.redraw,!0)&&this.redraw(e.animation),i},t.prototype.showLoading=function(t){var e=this,i=e.options,o=i.loading,r=function(){n&&an(n,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},n=e.loadingDiv,s=e.loadingSpan;n||(e.loadingDiv=n=ar("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),s||(e.loadingSpan=s=ar("span",{className:"highcharts-loading-inner"},null,n),ai(e,"redraw",r)),n.className="highcharts-loading",eY.setElementHTML(s,ak(t,i.lang.loading,"")),e.styledMode||(an(n,ac(o.style,{zIndex:10})),an(s,o.labelStyle),e.loadingShown||(an(n,{opacity:0,display:""}),eO(n,{opacity:o.style.opacity||.5},{duration:o.showDuration||0}))),e.loadingShown=!0,r()},t.prototype.hideLoading=function(){var t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||eO(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){an(e,{display:"none"})}})),this.loadingShown=!1},t.prototype.update=function(t,e,i,o){var r,n,s,a=this,h={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},l=t.isResponsiveOptions,d=[];au(a,"update",{options:t}),l||a.setResponsive(!1,!0),t=aa(t,a.options),a.userOptions=ab(a.userOptions,t);var c=t.chart;c&&(ab(!0,a.options.chart,c),this.setZoomOptions(),"className"in c&&a.setClassName(c.className),("inverted"in c||"polar"in c||"type"in c)&&(a.propFromSeries(),r=!0),"alignTicks"in c&&(r=!0),"events"in c&&s6(this,c),aM(c,function(t,e){-1!==a.propsRequireUpdateSeries.indexOf("chart."+e)&&(n=!0),-1!==a.propsRequireDirtyBox.indexOf(e)&&(a.isDirtyBox=!0),-1===a.propsRequireReflow.indexOf(e)||(a.isDirtyBox=!0,l||(s=!0))}),!a.styledMode&&c.style&&a.renderer.setStyle(a.options.chart.style||{})),!a.styledMode&&t.colors&&(this.options.colors=t.colors),aM(t,function(e,i){a[i]&&"function"==typeof a[i].update?a[i].update(e,!1):"function"==typeof a[h[i]]?a[h[i]](e):"colors"!==i&&-1===a.collectionsWithUpdate.indexOf(i)&&ab(!0,a.options[i],t[i]),"chart"!==i&&-1!==a.propsRequireUpdateSeries.indexOf(i)&&(n=!0)}),this.collectionsWithUpdate.forEach(function(e){t[e]&&(aT(t[e]).forEach(function(t,o){var r,n=as(t.id);n&&(r=a.get(t.id)),!r&&a[e]&&(r=a[e][ak(t.index,o)])&&(n&&as(r.options.id)||r.options.isInternal)&&(r=void 0),r&&r.coll===e&&(r.update(t,!1),i&&(r.touched=!0)),!r&&i&&a.collectionsWithInit[e]&&(a.collectionsWithInit[e][0].apply(a,[t].concat(a.collectionsWithInit[e][1]||[]).concat([!1])).touched=!0)}),i&&a[e].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:d.push(t)}))}),d.forEach(function(t){t.chart&&t.remove&&t.remove(!1)}),r&&a.axes.forEach(function(t){t.update({},!1)}),n&&a.getSeriesOrderByLinks().forEach(function(t){t.chart&&t.update({},!1)},this);var p=null==c?void 0:c.width,u=c&&(ax(c.height)?aS(c.height,p||a.chartWidth):c.height);s||am(p)&&p!==a.chartWidth||am(u)&&u!==a.chartHeight?a.setSize(p,u,o):ak(e,!0)&&a.redraw(o),au(a,"afterUpdate",{options:t,redraw:e,animation:o})},t.prototype.setSubtitle=function(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)},t.prototype.setCaption=function(t,e){this.applyDescription("caption",t),this.layOutTitles(e)},t.prototype.showResetZoom=function(){var t=this,e=ee.lang,i=t.zooming.resetButton,o=i.theme,r="chart"===i.relativeTo||"spacingBox"===i.relativeTo?null:"plotBox";function n(){t.zoomOut()}au(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,n,o).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,r)}),au(this,"afterShowResetZoom")},t.prototype.zoomOut=function(){var t=this;au(this,"selection",{resetSelection:!0},function(){return t.transform({reset:!0,trigger:"zoom"})})},t.prototype.pan=function(t,e){var i=this,o="object"==typeof e?e:{enabled:e,type:"x"},r=o.type,n=r&&i[({x:"xAxis",xy:"axes",y:"yAxis"})[r]].filter(function(t){return t.options.panningEnabled&&!t.options.isInternal}),s=i.options.chart;(null==s?void 0:s.panning)&&(s.panning=o),au(this,"pan",{originalEvent:t},function(){i.transform({axes:n,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),an(i.container,{cursor:"move"})})},t.prototype.transform=function(t){var e,i,o,r,n,s,a=this,h=t.axes,l=void 0===h?this.axes:h,d=t.event,c=t.from,p=void 0===c?{}:c,u=t.reset,f=t.selection,g=t.to,v=void 0===g?{}:g,m=t.trigger,y=this.inverted,x=this.time,b=!1;null===(i=this.hoverPoints)||void 0===i||i.forEach(function(t){return t.setState()});for(var M=0;M<l.length;M++){var k=l[M],w=k.horiz,S=k.len,A=k.minPointOffset,T=void 0===A?0:A,P=k.options,O=k.reversed,C=w?"width":"height",E=w?"x":"y",L=ak(v[C],k.len),B=ak(p[C],k.len),I=10>Math.abs(L)?1:L/B,D=(p[E]||0)+B/2-k.pos,z=D-((null!==(o=v[E])&&void 0!==o?o:k.pos)+L/2-k.pos)/I,R=O&&!y||!O&&y?-1:1;if(u||!(D<0)&&!(D>k.len)){var N=k.toValue(z,!0)+(f||k.isOrdinal?0:T*R),W=k.toValue(z+S/I,!0)-(f||k.isOrdinal?0:T*R||0),G=k.allExtremes;if(N>W&&(N=(e=[W,N])[0],W=e[1]),1===I&&!u&&"yAxis"===k.coll&&!G){for(var X=0,F=k.series;X<F.length;X++){var H=F[X],j=H.getExtremes(H.getProcessedData(!0).modified.getColumn("y")||[],!0);null!=G||(G={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),am(j.dataMin)&&am(j.dataMax)&&(G.dataMin=Math.min(j.dataMin,G.dataMin),G.dataMax=Math.max(j.dataMax,G.dataMax))}k.allExtremes=G}var Y=ac(k.getExtremes(),G||{}),_=Y.dataMin,U=Y.dataMax,V=Y.min,q=Y.max,Z=x.parse(P.min),K=x.parse(P.max),$=null!=_?_:Z,J=null!=U?U:K,Q=W-N,tt=k.categories?0:Math.min(Q,J-$),te=$-tt*(as(Z)?0:P.minPadding),ti=J+tt*(as(K)?0:P.maxPadding),to=k.allowZoomOutside||1===I||"zoom"!==m&&I>1,tr=Math.min(null!=Z?Z:te,te,to?V:te),tn=Math.max(null!=K?K:ti,ti,to?q:ti);(!k.isOrdinal||k.options.overscroll||1!==I||u)&&(N<tr&&(N=tr,I>=1&&(W=N+Q)),W>tn&&(W=tn,I>=1&&(N=W-Q)),(u||k.series.length&&(N!==V||W!==q)&&N>=tr&&W<=tn)&&(f?f[k.coll].push({axis:k,min:N,max:W}):(k.isPanning="zoom"!==m,k.isPanning&&(s=!0),k.setExtremes(u?void 0:N,u?void 0:W,!1,!1,{move:z,trigger:m,scale:I}),!u&&(N>tr||W<tn)&&"mousewheel"!==m&&(n=!0)),b=!0),d&&(this[w?"mouseDownX":"mouseDownY"]=d[w?"chartX":"chartY"]))}}return b&&(f?au(this,"selection",f,function(){delete t.selection,t.trigger="zoom",a.transform(t)}):(!n||s||this.resetZoomButton?!n&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw("zoom"===m&&(null!==(r=this.options.chart.animation)&&void 0!==r?r:this.pointCount<100)))),b},t}();ac(aC.prototype,{callbacks:[],collectionsWithInit:{xAxis:[aC.prototype.addAxis,[!0]],yAxis:[aC.prototype.addAxis,[!1]],series:[aC.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});var aE=tu.composed,aL=tG.addEvent,aB=tG.createElement,aI=tG.css,aD=tG.defined,az=tG.erase,aR=tG.merge,aN=tG.pushUnique;function aW(){var t=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!t&&(this.scrollablePlotArea=t=new aX(this)),null==t||t.applyFixed()}function aG(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}var aX=function(){function t(t){var e,i,o,r=t.options.chart,n=e6.getRendererType(),s=r.scrollablePlotArea||{},a=this.moveFixedElements.bind(this),h={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(h.overflowX="auto"),t.scrollablePixelsY&&(h.overflowY="auto"),this.chart=t;var l=this.parentDiv=aB("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),d=this.scrollingContainer=aB("div",{className:"highcharts-scrolling"},h,l),c=this.innerContainer=aB("div",{className:"highcharts-inner-container"},void 0,d),p=this.fixedDiv=aB("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:((null===(e=r.style)||void 0===e?void 0:e.zIndex)||0)+2,top:0},void 0,!0),u=this.fixedRenderer=new n(p,t.chartWidth,t.chartHeight,r.style);this.mask=u.path().attr({fill:r.backgroundColor||"#fff","fill-opacity":null!==(i=s.opacity)&&void 0!==i?i:.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),d.parentNode.insertBefore(p,d),aI(t.renderTo,{overflow:"visible"}),aL(t,"afterShowResetZoom",a),aL(t,"afterApplyDrilldown",a),aL(t,"afterLayOutTitles",a),aL(d,"scroll",function(){var e=t.pointer,i=t.hoverPoint;e&&(delete e.chartPosition,i&&(o=i),e.runPointActions(void 0,o,!0))}),c.appendChild(t.container)}return t.compose=function(t,e,i){var o=this;aN(aE,this.compose)&&(aL(t,"afterInit",aG),aL(e,"afterSetChartSize",function(t){return o.afterSetSize(t.target,t)}),aL(e,"render",aW),aL(i,"show",aG))},t.afterSetSize=function(t,e){var i,o,r,n=t.options.chart.scrollablePlotArea||{},s=n.minWidth,a=n.minHeight,h=t.clipBox,l=t.plotBox,d=t.inverted;if(!t.renderer.forExport&&(s?(t.scrollablePixelsX=i=Math.max(0,s-t.chartWidth),i&&(t.scrollablePlotBox=aR(t.plotBox),l.width=t.plotWidth+=i,h[d?"height":"width"]+=i,r=!0)):a&&(t.scrollablePixelsY=o=Math.max(0,a-t.chartHeight),aD(o)&&(t.scrollablePlotBox=aR(t.plotBox),l.height=t.plotHeight+=o,h[d?"width":"height"]+=o,r=!1)),aD(r)&&!e.skipAxes))for(var c=0,p=t.axes;c<p.length;c++){var u=p[c];(u.horiz===r||t.hasParallelCoordinates&&"yAxis"===u.coll)&&(u.setAxisSize(),u.setAxisTranslation())}},t.prototype.applyFixed=function(){var t,e=this.chart,i=this.fixedRenderer,o=this.isDirty,r=this.scrollingContainer,n=e.axisOffset,s=e.chartWidth,a=e.chartHeight,h=e.container,l=e.plotHeight,d=e.plotLeft,c=e.plotTop,p=e.plotWidth,u=e.scrollablePixelsX,f=void 0===u?0:u,g=e.scrollablePixelsY,v=void 0===g?0:g,m=e.options.chart.scrollablePlotArea||{},y=m.scrollPositionX,x=m.scrollPositionY,b=s+f,M=a+v;i.setSize(s,a),(null==o||o)&&(this.isDirty=!1,this.moveFixedElements()),eP(e.container),aI(h,{width:""+b+"px",height:""+M+"px"}),e.renderer.boxWrapper.attr({width:b,height:M,viewBox:[0,0,b,M].join(" ")}),null===(t=e.chartBackground)||void 0===t||t.attr({width:b,height:M}),aI(r,{width:""+s+"px",height:""+a+"px"}),aD(o)||(r.scrollLeft=f*(void 0===y?0:y),r.scrollTop=v*(void 0===x?0:x));var k=c-n[0]-1,w=d-n[3]-1,S=c+l+n[2]+1,A=d+p+n[1]+1,T=d+p-f,P=c+l-v,O=[["M",0,0]];f?O=[["M",0,k],["L",d-1,k],["L",d-1,S],["L",0,S],["Z"],["M",T,k],["L",s,k],["L",s,S],["L",T,S],["Z"]]:v&&(O=[["M",w,0],["L",w,c-1],["L",A,c-1],["L",A,0],["Z"],["M",w,P],["L",w,a],["L",A,a],["L",A,P],["Z"]]),"adjustHeight"!==e.redrawTrigger&&this.mask.attr({d:O})},t.prototype.moveFixedElements=function(){var e,i=this.chart,o=i.container,r=i.inverted,n=i.scrollablePixelsX,s=i.scrollablePixelsY,a=this.fixedRenderer,h=t.fixedSelectors;if(n&&!r?e=".highcharts-yaxis":n&&r?e=".highcharts-xaxis":s&&!r?e=".highcharts-xaxis":s&&r&&(e=".highcharts-yaxis"),e&&!(this.chart.hasParallelCoordinates&&".highcharts-yaxis"===e))for(var l=0,d=[""+e+":not(.highcharts-radial-axis)",""+e+"-labels:not(.highcharts-radial-axis-labels)"];l<d.length;l++){var c=d[l];aN(h,c)}else for(var p=0,u=[".highcharts-xaxis",".highcharts-yaxis"];p<u.length;p++)for(var f=u[p],g=0,v=[""+f+":not(.highcharts-radial-axis)",""+f+"-labels:not(.highcharts-radial-axis-labels)"];g<v.length;g++){var c=v[g];az(h,c)}for(var m=0;m<h.length;m++){var c=h[m];[].forEach.call(o.querySelectorAll(c),function(t){(t.namespaceURI===a.SVG_NS?a.box:a.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})}},t.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"],t}(),aF=e5.format,aH=si.series,aj=tG.destroyObjectProperties,aY=tG.fireEvent,a_=tG.getAlignFactor,aU=tG.isNumber,aV=tG.pick,aq=function(){function t(t,e,i,o,r){var n=t.chart.inverted,s=t.reversed;this.axis=t;var a=this.isNegative=!!i!=!!s;this.options=e=e||{},this.x=o,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=r,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:e.align||(n?a?"left":"right":"center"),verticalAlign:e.verticalAlign||(n?"middle":a?"bottom":"top"),y:e.y,x:e.x},this.textAlign=e.textAlign||(n?a?"right":"left":"center")}return t.prototype.destroy=function(){aj(this,this.axis)},t.prototype.render=function(t){var e=this.axis.chart,i=this.options,o=i.format,r=o?aF(o,this,e):i.formatter.call(this);if(this.label)this.label.attr({text:r,visibility:"hidden"});else{this.label=e.renderer.label(r,null,void 0,i.shape,void 0,void 0,i.useHTML,!1,"stack-labels");var n={r:i.borderRadius||0,text:r,padding:aV(i.padding,5),visibility:"hidden"};e.styledMode||(n.fill=i.backgroundColor,n.stroke=i.borderColor,n["stroke-width"]=i.borderWidth,this.label.css(i.style||{})),this.label.attr(n),this.label.added||this.label.add(t)}this.label.labelrank=e.plotSizeY,aY(this,"afterRender")},t.prototype.setOffset=function(t,e,i,o,r,n){var s=this.alignOptions,a=this.axis,h=this.label,l=this.options,d=this.textAlign,c=a.chart,p=this.getStackBox({xOffset:t,width:e,boxBottom:i,boxTop:o,defaultX:r,xAxis:n}),u=s.verticalAlign;if(h&&p){var f=h.getBBox(void 0,0),g=h.padding,v="justify"===aV(l.overflow,"justify"),m=void 0;s.x=l.x||0,s.y=l.y||0;var y=this.adjustStackPosition({labelBox:f,verticalAlign:u,textAlign:d}),x=y.x,b=y.y;p.x-=x,p.y-=b,h.align(s,!1,p),(m=c.isInsidePlot(h.alignAttr.x+s.x+x,h.alignAttr.y+s.y+b))||(v=!1),v&&aH.prototype.justifyDataLabel.call(a,h,s,h.alignAttr,f,p),h.attr({x:h.alignAttr.x,y:h.alignAttr.y,rotation:l.rotation,rotationOriginX:f.width*a_(l.textAlign||"center"),rotationOriginY:f.height/2}),aV(!v&&l.crop,!0)&&(m=aU(h.x)&&aU(h.y)&&c.isInsidePlot(h.x-g+(h.width||0),h.y)&&c.isInsidePlot(h.x+g,h.y)),h[m?"show":"hide"]()}aY(this,"afterSetOffset",{xOffset:t,width:e})},t.prototype.adjustStackPosition=function(t){var e=t.labelBox,i=t.verticalAlign,o=t.textAlign;return{x:e.width/2+e.width/2*(2*a_(o)-1),y:e.height/2*2*(1-a_(i))}},t.prototype.getStackBox=function(t){var e=this.axis,i=e.chart,o=t.boxTop,r=t.defaultX,n=t.xOffset,s=t.width,a=t.boxBottom,h=e.stacking.usePercentage?100:aV(o,this.total,0),l=e.toPixels(h),d=t.xAxis||i.xAxis[0],c=aV(r,d.translate(this.x))+n,p=Math.abs(l-e.toPixels(a||aU(e.min)&&e.logarithmic&&e.logarithmic.lin2log(e.min)||0)),u=i.inverted,f=this.isNegative;return u?{x:(f?l:l-p)-i.plotLeft,y:d.height-c-s+d.top-i.plotTop,width:p,height:s}:{x:c+d.transB-i.plotLeft,y:(f?l-p:l)-i.plotTop,width:s,height:p}},t}(),aZ=si.series.prototype,aK=tG.addEvent,a$=tG.correctFloat,aJ=tG.defined,aQ=tG.destroyObjectProperties,a0=tG.fireEvent,a1=tG.isNumber,a2=tG.objectEach,a3=tG.pick;function a5(){var t=this.inverted;this.axes.forEach(function(t){var e;(null===(e=t.stacking)||void 0===e?void 0:e.stacks)&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(function(e){var i,o=(null===(i=e.xAxis)||void 0===i?void 0:i.options)||{};e.options.stacking&&e.reserveSpace()&&(e.stackKey=[e.type,a3(e.options.stack,""),t?o.top:o.left,t?o.height:o.width].join(","))})}function a6(){var t,e=this.stacking;if(e){var i=e.stacks;a2(i,function(t,e){aQ(t),delete i[e]}),null===(t=e.stackTotalGroup)||void 0===t||t.destroy()}}function a9(){this.stacking||(this.stacking=new hi(this))}function a4(t,e,i,o){return!aJ(t)||t.x!==e||o&&t.stackKey!==o?t={x:e,index:0,key:o,stackKey:o}:t.index++,t.key=[i,e,t.index].join(","),t}function a8(){var t,e=this,i=e.yAxis,o=e.stackKey||"",r=i.stacking.stacks,n=e.getColumn("x",!0),s=e.options.stacking,a=e[s+"Stacker"];a&&[o,"-"+o].forEach(function(i){for(var o,s,h,l,d=n.length;d--;)s=n[d],t=e.getStackIndicator(t,s,e.index,i),(l=null==(h=null===(o=r[i])||void 0===o?void 0:o[s])?void 0:h.points[t.key||""])&&a.call(e,l,h,d)})}function a7(t,e,i){var o=e.total?100/e.total:0;t[0]=a$(t[0]*o),t[1]=a$(t[1]*o),this.stackedYData[i]=t[1]}function ht(t){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?aZ.setStackedPoints.call(this,t,"group"):t.stacking.resetStacks())}function he(t,e){var i,o,r,n,s,a,h,l,d,c=e||this.options.stacking;if(c&&this.reserveSpace()&&(({group:"xAxis"})[c]||"yAxis")===t.coll){var p=this.getColumn("x",!0),u=this.getColumn(this.pointValKey||"y",!0),f=[],g=u.length,v=this.options,m=v.threshold||0,y=v.startFromThreshold?m:0,x=v.stack,b=e?""+this.type+",".concat(c):this.stackKey||"",M="-"+b,k=this.negStacks,w=t.stacking,S=w.stacks,A=w.oldStacks;for(w.stacksTouched+=1,d=0;d<g;d++){var T=p[d]||0,P=u[d],O=a1(P)&&P||0;l=(r=this.getStackIndicator(r,T,this.index)).key||"",S[h=(n=k&&O<(y?0:m))?M:b]||(S[h]={}),S[h][T]||((null===(i=A[h])||void 0===i?void 0:i[T])?(S[h][T]=A[h][T],S[h][T].total=null):S[h][T]=new aq(t,t.options.stackLabels,!!n,T,x)),s=S[h][T],null!==P?(s.points[l]=s.points[this.index]=[a3(s.cumulative,y)],aJ(s.cumulative)||(s.base=l),s.touched=w.stacksTouched,r.index>0&&!1===this.singleStacks&&(s.points[l][0]=s.points[this.index+","+T+",0"][0])):(delete s.points[l],delete s.points[this.index]);var C=s.total||0;"percent"===c?(a=n?b:M,C=k&&(null===(o=S[a])||void 0===o?void 0:o[T])?(a=S[a][T]).total=Math.max(a.total||0,C)+Math.abs(O):a$(C+Math.abs(O))):"group"===c?a1(P)&&C++:C=a$(C+O),"group"===c?s.cumulative=(C||1)-1:s.cumulative=a$(a3(s.cumulative,y)+O),s.total=C,null!==P&&(s.points[l].push(s.cumulative),f[d]=s.cumulative,s.hasValidPoints=!0)}"percent"===c&&(w.usePercentage=!0),"group"!==c&&(this.stackedYData=f),w.oldStacks={}}}var hi=function(){function t(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}return t.prototype.buildStacks=function(){var t,e,i=this.axis,o=i.series,r="xAxis"===i.coll,n=i.options.reversedStacks,s=o.length;for(this.resetStacks(),this.usePercentage=!1,e=s;e--;)t=o[n?e:s-e-1],r&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!r)for(e=0;e<s;e++)o[e].modifyStacks();a0(i,"afterBuildStacks")},t.prototype.cleanStacks=function(){this.oldStacks&&(this.stacks=this.oldStacks,a2(this.stacks,function(t){a2(t,function(t){t.cumulative=t.total})}))},t.prototype.resetStacks=function(){var t=this;a2(this.stacks,function(e){a2(e,function(i,o){a1(i.touched)&&i.touched<t.stacksTouched?(i.destroy(),delete e[o]):(i.total=null,i.cumulative=null)})})},t.prototype.renderStackTotals=function(){var t,e=this.axis,i=e.chart,o=i.renderer,r=this.stacks,n=eC(i,(null===(t=e.options.stackLabels)||void 0===t?void 0:t.animation)||!1),s=this.stackTotalGroup=this.stackTotalGroup||o.g("stack-labels").attr({zIndex:6,opacity:0}).add();s.translate(i.plotLeft,i.plotTop),a2(r,function(t){a2(t,function(t){t.render(s)})}),s.animate({opacity:1},n)},t}();(K||(K={})).compose=function(t,e,i){var o=e.prototype,r=i.prototype;o.getStacks||(aK(t,"init",a9),aK(t,"destroy",a6),o.getStacks=a5,r.getStackIndicator=a4,r.modifyStacks=a8,r.percentStacker=a7,r.setGroupedPoints=ht,r.setStackedPoints=he)};var ho=K,hr=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hn=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},hs=tG.defined,ha=tG.merge,hh=tG.isObject,hl=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hr(e,t),e.prototype.drawGraph=function(){var t=this,e=this.options,i=(this.gappedPath||this.getGraphPath).call(this),o=this.chart.styledMode;hn([this],this.zones,!0).forEach(function(r,n){var s,a=r.graph,h=a?"animate":"attr",l=r.dashStyle||e.dashStyle;a?(a.endX=t.preventGraphAnimation?null:i.xMap,a.animate({d:i})):i.length&&(r.graph=a=t.chart.renderer.path(i).addClass("highcharts-graph"+(n?" highcharts-zone-graph-".concat(n-1," "):" ")+(n&&r.className||"")).attr({zIndex:1}).add(t.group)),a&&!o&&(s={stroke:!n&&e.lineColor||r.color||t.color||"#cccccc","stroke-width":e.lineWidth||0,fill:t.fillGraph&&t.color||"none"},l?s.dashstyle=l:"square"!==e.linecap&&(s["stroke-linecap"]=s["stroke-linejoin"]="round"),a[h](s).shadow(e.shadow&&ha({filterUnits:"userSpaceOnUse"},hh(e.shadow)?e.shadow:{}))),a&&(a.startX=i.xMap,a.isArea=i.isArea)})},e.prototype.getGraphPath=function(t,e,i){var o,r=this,n=r.options,s=[],a=[],h=n.step,l=(t=t||r.points).reversed;return l&&t.reverse(),(h=({right:1,center:2})[h]||h&&3)&&l&&(h=4-h),(t=this.getValidPoints(t,!1,n.nullInteraction||!(n.connectNulls&&!e&&!i))).forEach(function(l,d){var c,p=l.plotX,u=l.plotY,f=t[d-1],g=l.isNull||"number"!=typeof u;(l.leftCliff||(null==f?void 0:f.rightCliff))&&!i&&(o=!0),g&&!hs(e)&&d>0?o=!n.connectNulls:g&&!e?o=!0:(0===d||o?c=[["M",l.plotX,l.plotY]]:r.getPointSpline?c=[r.getPointSpline(t,l,d)]:h?(c=1===h?[["L",f.plotX,u]]:2===h?[["L",(f.plotX+p)/2,f.plotY],["L",(f.plotX+p)/2,u]]:[["L",p,f.plotY]]).push(["L",p,u]):c=[["L",p,u]],a.push(l.x),h&&(a.push(l.x),2===h&&a.push(l.x)),s.push.apply(s,c),o=!1)}),s.xMap=a,r.graphPath=s,s},e.defaultOptions=ha(sD.defaultOptions,{legendSymbol:"lineMarker"}),e}(sD);si.registerSeriesType("line",hl);var hd={threshold:0,legendSymbol:"areaMarker"},hc=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hp=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},hu=si.seriesTypes.line,hf=tG.extend,hg=tG.merge,hv=tG.objectEach,hm=tG.pick,hy=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hc(e,t),e.prototype.drawGraph=function(){var e=this;this.areaPath=[],t.prototype.drawGraph.apply(this);var i=this.areaPath,o=this.options;hp([this],this.zones,!0).forEach(function(t,r){var n,s={},a=t.fillColor||o.fillColor,h=t.area,l=h?"animate":"attr";h?(h.endX=e.preventGraphAnimation?null:i.xMap,h.animate({d:i})):(s.zIndex=0,(h=t.area=e.chart.renderer.path(i).addClass("highcharts-area"+(r?" highcharts-zone-area-".concat(r-1," "):" ")+(r&&t.className||"")).add(e.group)).isArea=!0),e.chart.styledMode||(s.fill=a||t.color||e.color,s["fill-opacity"]=a?1:null!==(n=o.fillOpacity)&&void 0!==n?n:.75,h.css({pointerEvents:e.stickyTracking?"none":"auto"})),h[l](s),h.startX=i.xMap,h.shiftUnit=o.step?2:1})},e.prototype.getGraphPath=function(t){var e,i,o,r=hu.prototype.getGraphPath,n=this.options,s=n.stacking,a=this.yAxis,h=[],l=[],d=this.index,c=a.stacking.stacks[this.stackKey],p=n.threshold,u=Math.round(a.getThreshold(n.threshold)),f=hm(n.connectNulls,"percent"===s),g=function(i,o,r){var n,f,g=t[i],v=s&&c[g.x].points[d],m=g[r+"Null"]||0,y=g[r+"Cliff"]||0,x=!0;y||m?(n=(m?v[0]:v[1])+y,f=v[0]+y,x=!!m):!s&&t[o]&&t[o].isNull&&(n=f=p),void 0!==n&&(l.push({plotX:e,plotY:null===n?u:a.getThreshold(n),isNull:x,isCliff:!0}),h.push({plotX:e,plotY:null===f?u:a.getThreshold(f),doCurve:!1}))};t=t||this.points,s&&(t=this.getStackPoints(t));for(var v=0,m=t.length;v<m;++v)s||(t[v].leftCliff=t[v].rightCliff=t[v].leftNull=t[v].rightNull=void 0),i=t[v].isNull,e=hm(t[v].rectPlotX,t[v].plotX),o=s?hm(t[v].yBottom,u):u,i&&!f||(f||g(v,v-1,"left"),i&&!s&&f||(l.push(t[v]),h.push({x:v,plotX:e,plotY:o})),f||g(v,v+1,"right"));var y=r.call(this,l,!0,!0);h.reversed=!0;var x=r.call(this,h,!0,!0),b=x[0];b&&"M"===b[0]&&(x[0]=["L",b[1],b[2]]);var M=y.concat(x);M.length&&M.push(["Z"]);var k=r.call(this,l,!1,f);return this.chart.series.length>1&&s&&l.some(function(t){return t.isCliff})&&(M.hasStackedCliffs=k.hasStackedCliffs=!0),M.xMap=y.xMap,this.areaPath=M,k},e.prototype.getStackPoints=function(t){var e=this,i=[],o=[],r=this.xAxis,n=this.yAxis,s=n.stacking.stacks[this.stackKey],a={},h=n.series,l=h.length,d=n.options.reversedStacks?1:-1,c=h.indexOf(e);if(t=t||this.points,this.options.stacking){for(var p=0;p<t.length;p++)t[p].leftNull=t[p].rightNull=void 0,a[t[p].x]=t[p];hv(s,function(t,e){null!==t.total&&o.push(e)}),o.sort(function(t,e){return t-e});var u=h.map(function(t){return t.visible});o.forEach(function(t,p){var f,g,v=0;if(a[t]&&!a[t].isNull)i.push(a[t]),[-1,1].forEach(function(i){var r=1===i?"rightNull":"leftNull",n=s[o[p+i]],v=0;if(n)for(var m=c;m>=0&&m<l;){var y=h[m].index;!(f=n.points[y])&&(y===e.index?a[t][r]=!0:u[m]&&(g=s[t].points[y])&&(v-=g[1]-g[0])),m+=d}a[t][1===i?"rightCliff":"leftCliff"]=v});else{for(var m=c;m>=0&&m<l;){var y=h[m].index;if(f=s[t].points[y]){v=f[1];break}m+=d}v=hm(v,0),v=n.translate(v,0,1,0,1),i.push({isNull:!0,plotX:r.translate(t,0,0,0,1),x:t,plotY:v,yBottom:v})}})}return i},e.defaultOptions=hg(hu.defaultOptions,hd),e}(hu);hf(hy.prototype,{singleStacks:!1}),si.registerSeriesType("area",hy);var hx=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hb=si.seriesTypes.line,hM=tG.merge,hk=tG.pick,hw=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hx(e,t),e.prototype.getPointSpline=function(t,e,i){var o,r,n,s,a=e.plotX||0,h=e.plotY||0,l=t[i-1],d=t[i+1];function c(t){return t&&!t.isNull&&!1!==t.doCurve&&!e.isCliff}if(c(l)&&c(d)){var p=l.plotX||0,u=l.plotY||0,f=d.plotX||0,g=d.plotY||0,v=0;o=(1.5*a+p)/2.5,r=(1.5*h+u)/2.5,n=(1.5*a+f)/2.5,s=(1.5*h+g)/2.5,n!==o&&(v=(s-r)*(n-a)/(n-o)+h-s),r+=v,s+=v,r>u&&r>h?(r=Math.max(u,h),s=2*h-r):r<u&&r<h&&(r=Math.min(u,h),s=2*h-r),s>g&&s>h?(s=Math.max(g,h),r=2*h-s):s<g&&s<h&&(s=Math.min(g,h),r=2*h-s),e.rightContX=n,e.rightContY=s,e.controlPoints={low:[o,r],high:[n,s]}}var m=["C",hk(l.rightContX,l.plotX,0),hk(l.rightContY,l.plotY,0),hk(o,a,0),hk(r,h,0),a,h];return l.rightContX=l.rightContY=void 0,m},e.defaultOptions=hM(hb.defaultOptions),e}(hb);si.registerSeriesType("spline",hw);var hS=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hA=si.seriesTypes,hT=hA.area,hP=hA.area.prototype,hO=tG.extend,hC=tG.merge,hE=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hS(e,t),e.defaultOptions=hC(hw.defaultOptions,hT.defaultOptions),e}(hw);hO(hE.prototype,{getGraphPath:hP.getGraphPath,getStackPoints:hP.getStackPoints,drawGraph:hP.drawGraph}),si.registerSeriesType("areaspline",hE);var hL={borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"},hB=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hI=ep.parse,hD=tu.noop,hz=tG.clamp,hR=tG.crisp,hN=tG.defined,hW=tG.extend,hG=tG.fireEvent,hX=tG.isArray,hF=tG.isNumber,hH=tG.merge,hj=tG.pick,hY=tG.objectEach,h_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hB(e,t),e.prototype.animate=function(t){var e,i,o=this,r=this.yAxis,n=r.pos,s=r.reversed,a=o.options,h=this.chart,l=h.clipOffset,d=h.inverted,c={},p=d?"translateX":"translateY";t&&l?(c.scaleY=.001,i=hz(r.toPixels(a.threshold||0),n,n+r.len),d?c.translateX=(i+=s?-Math.floor(l[0]):Math.ceil(l[2]))-r.len:c.translateY=i+=s?Math.ceil(l[0]):-Math.floor(l[2]),o.clipBox&&o.setClip(),o.group.attr(c)):(e=Number(o.group.attr(p)),o.group.animate({scaleY:1},hW(eT(o.options.animation),{step:function(t,i){o.group&&(c[p]=e+i.pos*(n-e),o.group.attr(c))}})))},e.prototype.init=function(e,i){t.prototype.init.apply(this,arguments);var o=this;(e=o.chart).hasRendered&&e.series.forEach(function(t){t.type===o.type&&(t.isDirty=!0)})},e.prototype.getColumnMetrics=function(){var t,e,i,o=this,r=o.options,n=o.xAxis,s=o.yAxis,a=n.options.reversedStacks,h=n.reversed&&!a||!n.reversed&&a,l={},d=0;!1===r.grouping?d=1:o.chart.series.forEach(function(t){var e,r=t.yAxis,n=t.options;t.type===o.type&&t.reserveSpace()&&s.len===r.len&&s.pos===r.pos&&(n.stacking&&"group"!==n.stacking?(void 0===l[i=t.stackKey]&&(l[i]=d++),e=l[i]):!1!==n.grouping&&(e=d++),t.columnIndex=e)});var c=Math.min(Math.abs(n.transA)*(!(null===(t=n.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(null===(e=n.ordinal)||void 0===e?void 0:e.slope)||r.pointRange||n.closestPointRange||n.tickInterval||1),n.len),p=c*r.groupPadding,u=(c-2*p)/(d||1),f=Math.min(r.maxPointWidth||n.len,hj(r.pointWidth,u*(1-2*r.pointPadding))),g=(o.columnIndex||0)+ +!!h;return o.columnMetrics={width:f,offset:(u-f)/2+(p+g*u-c/2)*(h?-1:1),paddedWidth:u,columnCount:d},o.columnMetrics},e.prototype.crispCol=function(t,e,i,o){var r=this.borderWidth,n=this.chart.inverted;return o=hR(e+o,r,n)-(e=hR(e,r,n)),this.options.crisp&&(i=hR(t+i,r)-(t=hR(t,r))),{x:t,y:e,width:i,height:o}},e.prototype.adjustForMissingColumns=function(t,e,i,o){var r,n=this;if(!i.isNull&&o.columnCount>1){var s=this.xAxis.series.filter(function(t){return t.visible}).map(function(t){return t.index}),a=0,h=0;hY(null===(r=this.xAxis.stacking)||void 0===r?void 0:r.stacks,function(t){var e,o="number"==typeof i.x?null===(e=t[i.x.toString()])||void 0===e?void 0:e.points:void 0,r=null==o?void 0:o[n.index],l={};if(o&&hX(r)){var d=n.index,c=Object.keys(o).filter(function(t){return!t.match(",")&&o[t]&&o[t].length>1}).map(parseFloat).filter(function(t){return -1!==s.indexOf(t)}).filter(function(t){var e=n.chart.series[t].options,i=e.stacking&&e.stack;if(hN(i)){if(hF(l[i]))return d===t&&(d=l[i]),!1;l[i]=t}return!0}).sort(function(t,e){return e-t});a=c.indexOf(d),h=c.length}}),a=this.xAxis.reversed?h-1-a:a;var l=(h-1)*o.paddedWidth+e;t=(i.plotX||0)+l/2-e-a*o.paddedWidth}return t},e.prototype.translate=function(){var t=this,e=t.chart,i=t.options,o=t.dense=t.closestPointRange*t.xAxis.transA<2,r=t.borderWidth=hj(i.borderWidth,+!o),n=t.xAxis,s=t.yAxis,a=i.threshold,h=hj(i.minPointLength,5),l=t.getColumnMetrics(),d=l.width,c=t.pointXOffset=l.offset,p=t.dataMin,u=t.dataMax,f=t.translatedThreshold=s.getThreshold(a),g=t.barW=Math.max(d,1+2*r);i.pointPadding&&i.crisp&&(g=Math.ceil(g)),sD.prototype.translate.apply(t),t.points.forEach(function(o){var r,v=hj(o.yBottom,f),m=999+Math.abs(v),y=o.plotX||0,x=hz(o.plotY,-m,s.len+m),b=Math.min(x,v),M=Math.max(x,v)-b,k=d,w=y+c,S=g;h&&Math.abs(M)<h&&(M=h,r=!s.reversed&&!o.negative||s.reversed&&o.negative,hF(a)&&hF(u)&&o.y===a&&u<=a&&(s.min||0)<a&&(p!==u||(s.max||0)<=a)&&(r=!r,o.negative=!o.negative),b=Math.abs(b-f)>h?v-h:f-(r?h:0)),hN(o.options.pointWidth)&&(w-=Math.round(((k=S=Math.ceil(o.options.pointWidth))-d)/2)),i.centerInCategory&&(w=t.adjustForMissingColumns(w,k,o,l)),o.barX=w,o.pointWidth=k,o.tooltipPos=e.inverted?[hz(s.len+s.pos-e.plotLeft-x,s.pos-e.plotLeft,s.len+s.pos-e.plotLeft),n.len+n.pos-e.plotTop-w-S/2,M]:[n.left-e.plotLeft+w+S/2,hz(x+s.pos-e.plotTop,s.pos-e.plotTop,s.len+s.pos-e.plotTop),M],o.shapeType=t.pointClass.prototype.shapeType||"roundedRect",o.shapeArgs=t.crispCol(w,b,S,o.isNull?0:M)}),hG(this,"afterColumnTranslate")},e.prototype.drawGraph=function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},e.prototype.pointAttribs=function(t,e){var i,o,r,n,s,a=this.options,h=this.pointAttrToOptions||{},l=h.stroke||"borderColor",d=h["stroke-width"]||"borderWidth",c=t&&t.color||this.color,p=t&&t[l]||a[l]||c,u=t&&t.options.dashStyle||a.dashStyle,f=t&&t[d]||a[d]||this[d]||0,g=(null==t?void 0:t.isNull)&&a.nullInteraction?0:null!==(o=null!==(i=null==t?void 0:t.opacity)&&void 0!==i?i:a.opacity)&&void 0!==o?o:1;t&&this.zones.length&&(n=t.getZone(),c=t.options.color||n&&(n.color||t.nonZonedColor)||this.color,n&&(p=n.borderColor||p,u=n.dashStyle||u,f=n.borderWidth||f)),e&&t&&(s=(r=hH(a.states[e],t.options.states&&t.options.states[e]||{})).brightness,c=r.color||void 0!==s&&hI(c).brighten(r.brightness).get()||c,p=r[l]||p,f=r[d]||f,u=r.dashStyle||u,g=hj(r.opacity,g));var v={fill:c,stroke:p,"stroke-width":f,opacity:g};return u&&(v.dashstyle=u),v},e.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i=this,o=this.chart,r=i.options,n=r.nullInteraction,s=o.renderer,a=r.animationLimit||250;t.forEach(function(t){var h=t.plotY,l=t.graphic,d=!!l,c=l&&o.pointCount<a?"animate":"attr";hF(h)&&(null!==t.y||n)?(e=t.shapeArgs,l&&t.hasNewShapeType()&&(l=l.destroy()),i.enabledDataSorting&&(t.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!l&&(t.graphic=l=s[t.shapeType](e).add(t.group||i.group),l&&i.enabledDataSorting&&o.hasRendered&&o.pointCount<a&&(l.attr({x:t.startXPos}),d=!0,c="animate")),l&&d&&l[c](hH(e)),o.styledMode||l[c](i.pointAttribs(t,t.selected&&"select")).shadow(!1!==t.allowShadow&&r.shadow),l&&(l.addClass(t.getClassName(),!0),l.attr({visibility:t.visible?"inherit":"hidden"}))):l&&(t.graphic=l.destroy())})},e.prototype.drawTracker=function(t){void 0===t&&(t=this.points);var e,i=this,o=i.chart,r=o.pointer,n=function(t){null==r||r.normalize(t);var e=null==r?void 0:r.getPointFromEvent(t);r&&e&&i.options.enableMouseTracking&&(o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})||(null==r?void 0:r.inClass(t.target,"highcharts-data-label")))&&(r.isDirectTouch=!0,e.onMouseOver(t))};t.forEach(function(t){e=hX(t.dataLabels)?t.dataLabels:t.dataLabel?[t.dataLabel]:[],t.graphic&&(t.graphic.element.point=t),e.forEach(function(e){(e.div||e.element).point=t})}),i._hasTracking||(i.trackerGroups.forEach(function(t){i[t]&&(i[t].addClass("highcharts-tracker").on("mouseover",n).on("mouseout",function(t){null==r||r.onTrackerMouseOut(t)}).on("touchstart",n),!o.styledMode&&i.options.cursor&&i[t].css({cursor:i.options.cursor}))}),i._hasTracking=!0),hG(this,"afterDrawTracker")},e.prototype.remove=function(){var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),sD.prototype.remove.apply(t,arguments)},e.defaultOptions=hH(sD.defaultOptions,hL),e}(sD);hW(h_.prototype,{directTouch:!0,getSymbol:hD,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),si.registerSeriesType("column",h_);var hU=e5.format,hV=tG.defined,hq=tG.extend,hZ=tG.fireEvent,hK=tG.getAlignFactor,h$=tG.isArray,hJ=tG.isString,hQ=tG.merge,h0=tG.objectEach,h1=tG.pick,h2=tG.pInt,h3=tG.splat;!function(t){function e(){return h(this).some(function(t){return null==t?void 0:t.enabled})}function i(t,e,i,o,r){var n,s=this.chart,a=this.enabledDataSorting,h=this.isCartesian&&s.inverted,l=t.plotX,d=t.plotY,c=i.rotation||0,p=hV(l)&&hV(d)&&s.isInsidePlot(l,Math.round(d),{inverted:h,paneCoordinates:!0,series:this}),u=0===c&&"justify"===h1(i.overflow,a?"none":"justify"),f=this.visible&&!1!==t.visible&&hV(l)&&(t.series.forceDL||a&&!u||p||h1(i.inside,!!this.options.stacking)&&o&&s.isInsidePlot(l,h?o.x+1:o.y+o.height-1,{inverted:h,paneCoordinates:!0,series:this})),g=t.pos();if(f&&g){var v,m=e.getBBox(),y=e.getBBox(void 0,0);if(o=hq({x:g[0],y:Math.round(g[1]),width:0,height:0},o||{}),"plotEdges"===i.alignTo&&this.isCartesian&&(o[h?"x":"y"]=0,o[h?"width":"height"]=(null===(n=this.yAxis)||void 0===n?void 0:n.len)||0),hq(i,{width:m.width,height:m.height}),v=o,a&&this.xAxis&&!u&&this.setDataLabelStartPos(t,e,r,p,v),e.align(hQ(i,{width:y.width,height:y.height}),!1,o,!1),e.alignAttr.x+=hK(i.align)*(y.width-m.width),e.alignAttr.y+=hK(i.verticalAlign)*(y.height-m.height),e[e.placed?"animate":"attr"]({"text-align":e.alignAttr["text-align"]||"center",x:e.alignAttr.x+(m.width-y.width)/2,y:e.alignAttr.y+(m.height-y.height)/2,rotationOriginX:(e.width||0)/2,rotationOriginY:(e.height||0)/2}),u&&o.height>=0)this.justifyDataLabel(e,i,e.alignAttr,m,o,r);else if(h1(i.crop,!0)){var x=e.alignAttr,b=x.x,M=x.y;f=s.isInsidePlot(b,M,{paneCoordinates:!0,series:this})&&s.isInsidePlot(b+m.width-1,M+m.height-1,{paneCoordinates:!0,series:this})}i.shape&&!c&&e[r?"attr":"animate"]({anchorX:g[0],anchorY:g[1]})}r&&a&&(e.placed=!1),f||a&&!u?(e.show(),e.placed=!0):(e.hide(),e.placed=!1)}function o(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function r(t){var e=this.hasRendered||0,i=this.initDataLabelsGroup().attr({opacity:+e});return!e&&i&&(this.visible&&i.show(),this.options.animation?i.animate({opacity:1},t):i.attr({opacity:1})),i}function n(t){t=t||this.points;var e,i,o=this,r=o.chart,n=o.options,s=r.renderer,l=r.options.chart,d=l.backgroundColor,c=l.plotBackgroundColor,p=s.getContrast(hJ(c)&&c||hJ(d)&&d||"#000000"),u=h(o),f=u[0],g=f.animation,v=f.defer?eC(r,g,o):{defer:0,duration:0};hZ(this,"drawDataLabels"),(null===(e=o.hasDataLabels)||void 0===e?void 0:e.call(o))&&(i=this.initDataLabels(v),t.forEach(function(t){var e,h,l,d=t.dataLabels||[],c=t.color||o.color;h3(a(u,t.dlOptions||(null===(e=t.options)||void 0===e?void 0:e.dataLabels))).forEach(function(e,a){var h,l,u,f,g,v=e.enabled&&(t.visible||t.dataLabelOnHidden)&&(!t.isNull||t.dataLabelOnNull)&&function(t,e){var i=e.filter;if(i){var o=i.operator,r=t[i.property],n=i.value;return">"===o&&r>n||"<"===o&&r<n||">="===o&&r>=n||"<="===o&&r<=n||"=="===o&&r==n||"==="===o&&r===n||"!="===o&&r!=n||"!=="===o&&r!==n||!1}return!0}(t,e),m=e.backgroundColor,y=e.borderColor,x=e.distance,b=e.style,M=void 0===b?{}:b,k={},w=d[a],S=!w;v&&(u=hV(l=h1(e[t.formatPrefix+"Format"],e.format))?hU(l,t,r):(e[t.formatPrefix+"Formatter"]||e.formatter).call(t,e),f=e.rotation,!r.styledMode&&(M.color=h1(e.color,M.color,hJ(o.color)?o.color:void 0,"#000000"),"contrast"===M.color?("none"!==m&&(g=m),t.contrastColor=s.getContrast("auto"!==g&&hJ(g)&&g||(hJ(c)?c:"")),M.color=g||!hV(x)&&e.inside||0>h2(x||0)||n.stacking?t.contrastColor:p):delete t.contrastColor,n.cursor&&(M.cursor=n.cursor)),k={r:e.borderRadius||0,rotation:f,padding:e.padding,zIndex:1},r.styledMode||(k.fill="auto"===m?t.color:m,k.stroke="auto"===y?t.color:y,k["stroke-width"]=e.borderWidth),h0(k,function(t,e){void 0===t&&delete k[e]})),!w||v&&hV(u)&&!!(w.div||(null===(h=w.text)||void 0===h?void 0:h.foreignObject))==!!e.useHTML&&(w.rotation&&e.rotation||w.rotation===e.rotation)||(w=void 0,S=!0),v&&hV(u)&&(w?k.text=u:(w=s.label(u,0,0,e.shape,void 0,void 0,e.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+t.colorIndex+" "+(e.className||"")+(e.useHTML?" highcharts-tracker":"")),w&&(w.options=e,w.attr(k),r.styledMode?M.width&&w.css({width:M.width,textOverflow:M.textOverflow,whiteSpace:M.whiteSpace}):w.css(M).shadow(e.shadow),hZ(w,"beforeAddingDataLabel",{labelOptions:e,point:t}),w.added||w.add(i),o.alignDataLabel(t,w,e,void 0,S),w.isActive=!0,d[a]&&d[a]!==w&&d[a].destroy(),d[a]=w))});for(var f=d.length;f--;)(null===(h=d[f])||void 0===h?void 0:h.isActive)?d[f].isActive=!1:(null===(l=d[f])||void 0===l||l.destroy(),d.splice(f,1));t.dataLabel=d[0],t.dataLabels=d})),hZ(this,"afterDrawDataLabels")}function s(t,e,i,o,r,n){var s,a,h=this.chart,l=e.align,d=e.verticalAlign,c=t.box?0:t.padding||0,p=h.inverted?this.yAxis:this.xAxis,u=p?p.left-h.plotLeft:0,f=h.inverted?this.xAxis:this.yAxis,g=f?f.top-h.plotTop:0,v=e.x,m=void 0===v?0:v,y=e.y,x=void 0===y?0:y;return(s=(i.x||0)+c+u)<0&&("right"===l&&m>=0?(e.align="left",e.inside=!0):m-=s,a=!0),(s=(i.x||0)+o.width-c+u)>h.plotWidth&&("left"===l&&m<=0?(e.align="right",e.inside=!0):m+=h.plotWidth-s,a=!0),(s=i.y+c+g)<0&&("bottom"===d&&x>=0?(e.verticalAlign="top",e.inside=!0):x-=s,a=!0),(s=(i.y||0)+o.height-c+g)>h.plotHeight&&("top"===d&&x<=0?(e.verticalAlign="bottom",e.inside=!0):x+=h.plotHeight-s,a=!0),a&&(e.x=m,e.y=x,t.placed=!n,t.align(e,void 0,r)),a}function a(t,e){var i,o=[];if(h$(t)&&!h$(e))o=t.map(function(t){return hQ(t,e)});else if(h$(e)&&!h$(t))o=e.map(function(e){return hQ(t,e)});else if(h$(t)||h$(e)){if(h$(t)&&h$(e))for(i=Math.max(t.length,e.length);i--;)o[i]=hQ(t[i],e[i])}else o=hQ(t,e);return o}function h(t){var e,i,o=t.chart.options.plotOptions;return h3(a(a(null===(e=null==o?void 0:o.series)||void 0===e?void 0:e.dataLabels,null===(i=null==o?void 0:o[t.type])||void 0===i?void 0:i.dataLabels),t.options.dataLabels))}function l(t,e,i,o,r){var n=this.chart,s=n.inverted,a=this.xAxis,h=a.reversed,l=((s?e.height:e.width)||0)/2,d=t.pointWidth,c=d?d/2:0;e.startXPos=s?r.x:h?-l-c:a.width-l+c,e.startYPos=s?h?this.yAxis.height-l+c:-l-c:r.y,o?"hidden"===e.visibility&&(e.show(),e.attr({opacity:0}).animate({opacity:1})):e.attr({opacity:1}).animate({opacity:0},void 0,e.hide),n.hasRendered&&(i&&e.attr({x:e.startXPos,y:e.startYPos}),e.placed=!0)}t.compose=function(t){var h=t.prototype;h.initDataLabels||(h.initDataLabels=r,h.initDataLabelsGroup=o,h.alignDataLabel=i,h.drawDataLabels=n,h.justifyDataLabel=s,h.mergeArrays=a,h.setDataLabelStartPos=l,h.hasDataLabels=e)}}($||($={}));var h5=$,h6=tu.composed,h9=si.series,h4=tG.merge,h8=tG.pushUnique;!function(t){function e(t,e,i,o,r){var n,s,a,h,l,d,c,p=this.chart,u=this.options,f=p.inverted,g=(null===(n=this.xAxis)||void 0===n?void 0:n.len)||p.plotSizeX||0,v=(null===(s=this.yAxis)||void 0===s?void 0:s.len)||p.plotSizeY||0,m=t.dlBox||t.shapeArgs,y=null!==(a=t.below)&&void 0!==a?a:(t.plotY||0)>(null!==(h=this.translatedThreshold)&&void 0!==h?h:v),x=null!==(l=i.inside)&&void 0!==l?l:!!u.stacking;if(m){if(o=h4(m),"allow"!==i.overflow||!1!==i.crop||!1!==u.clip){o.y<0&&(o.height+=o.y,o.y=0);var b=o.y+o.height-v;b>0&&b<o.height-1&&(o.height-=b)}f&&(o={x:v-o.y-o.height,y:g-o.x-o.width,width:o.height,height:o.width}),x||(f?(o.x+=y?0:o.width,o.width=0):(o.y+=y?o.height:0,o.height=0))}null!==(d=i.align)&&void 0!==d||(i.align=!f||x?"center":y?"right":"left"),null!==(c=i.verticalAlign)&&void 0!==c||(i.verticalAlign=f||x?"middle":y?"top":"bottom"),h9.prototype.alignDataLabel.call(this,t,e,i,o,r),i.inside&&t.contrastColor&&e.css({color:t.contrastColor})}t.compose=function(t){h5.compose(h9),h8(h6,"ColumnDataLabel")&&(t.prototype.alignDataLabel=e)}}(J||(J={}));var h7=J,lt=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),le=tG.extend,li=tG.merge,lo=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lt(e,t),e.defaultOptions=li(h_.defaultOptions,{}),e}(h_);le(lo.prototype,{inverted:!0}),si.registerSeriesType("bar",lo);var lr={lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}},ln=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ls=si.seriesTypes,la=ls.column,lh=ls.line,ll=tG.addEvent,ld=tG.extend,lc=tG.merge,lp=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ln(e,t),e.prototype.applyJitter=function(){var t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(o,r){["x","y"].forEach(function(n,s){if(e[n]&&!o.isNull){var a="plot".concat(n.toUpperCase()),h=t[""+n+"Axis"],l=e[n]*h.transA;if(h&&!h.logarithmic){var d,c=Math.max(0,(o[a]||0)-l),p=Math.min(h.len,(o[a]||0)+l);o[a]=c+(p-c)*((d=1e4*Math.sin(r+s*i))-Math.floor(d)),"x"===n&&(o.clientX=o.plotX)}}})})},e.prototype.drawGraph=function(){this.options.lineWidth?t.prototype.drawGraph.call(this):this.graph&&(this.graph=this.graph.destroy())},e.defaultOptions=lc(lh.defaultOptions,lr),e}(lh);ld(lp.prototype,{drawTracker:la.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),ll(lp,"afterTranslate",function(){this.applyJitter()}),si.registerSeriesType("scatter",lp);var lu=tu.deg2rad,lf=tG.fireEvent,lg=tG.isNumber,lv=tG.pick,lm=tG.relativeLength;(T=Q||(Q={})).getCenter=function(){var t,e,i,o=this.options,r=this.chart,n=2*(o.slicedOffset||0),s=r.plotWidth-2*n,a=r.plotHeight-2*n,h=o.center,l=Math.min(s,a),d=o.thickness,c=o.size,p=o.innerSize||0;"string"==typeof c&&(c=parseFloat(c)),"string"==typeof p&&(p=parseFloat(p));var u=[lv(null==h?void 0:h[0],"50%"),lv(null==h?void 0:h[1],"50%"),lv(c&&c<0?void 0:o.size,"100%"),lv(p&&p<0?void 0:o.innerSize||0,"0%")];for(!r.angular||this instanceof sD||(u[3]=0),e=0;e<4;++e)i=u[e],t=e<2||2===e&&/%$/.test(i),u[e]=lm(i,[s,a,l,u[2]][e])+(t?n:0);return u[3]>u[2]&&(u[3]=u[2]),lg(d)&&2*d<u[2]&&d>0&&(u[3]=u[2]-2*d),lf(this,"afterGetCenter",{positions:u}),u},T.getStartAndEndRadians=function(t,e){var i=lg(t)?t:0,o=lg(e)&&e>i&&e-i<360?e:i+360;return{start:lu*(i+-90),end:lu*(o+-90)}};var ly=Q,lx=(P=function(t,e){return(P=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}P(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lb=function(){return(lb=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},lM=tG.addEvent,lk=tG.defined,lw=tG.extend,lS=tG.isNumber,lA=tG.pick,lT=tG.relativeLength,lP=function(t){function e(e,i,o){var r,n=t.call(this,e,i,o)||this;n.half=0,null!==(r=n.name)&&void 0!==r||(n.name=e.chart.options.lang.pieSliceName);var s=function(t){n.slice("select"===t.type)};return lM(n,"select",s),lM(n,"unselect",s),n}return lx(e,t),e.prototype.getConnectorPath=function(t){var e=t.dataLabelPosition,i=t.options||{},o=i.connectorShape,r=this.connectorShapes[o]||o;return e&&r.call(this,lb(lb({},e.computed),{alignment:e.alignment}),e.connectorPosition,i)||[]},e.prototype.getTranslate=function(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}},e.prototype.haloPath=function(t){var e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})},e.prototype.isValid=function(){return lS(this.y)&&this.y>=0},e.prototype.setVisible=function(t,e){void 0===e&&(e=!0),t!==this.visible&&this.update({visible:null!=t?t:!this.visible},e,void 0,!1)},e.prototype.slice=function(t,e,i){var o=this.series;eE(i,o.chart),e=lA(e,!0),this.sliced=this.options.sliced=t=lk(t)?t:!this.sliced,o.options.data[o.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())},e}(nP);lw(lP.prototype,{connectorShapes:{fixedOffset:function(t,e,i){var o=e.breakAt,r=e.touchingSliceAt,n=i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*o.x-r.x,2*o.y-r.y,o.x,o.y]:["L",o.x,o.y];return[["M",t.x,t.y],n,["L",r.x,r.y]]},straight:function(t,e){var i=e.touchingSliceAt;return[["M",t.x,t.y],["L",i.x,i.y]]},crookedLine:function(t,e,i){var o=e.angle,r=void 0===o?this.angle||0:o,n=e.breakAt,s=e.touchingSliceAt,a=this.series,h=a.center,l=h[0],d=h[1],c=h[2]/2,p=a.chart,u=p.plotLeft,f=p.plotWidth,g="left"===t.alignment,v=t.x,m=t.y,y=n.x;if(i.crookDistance){var x=lT(i.crookDistance,1);y=g?l+c+(f+u-l-c)*(1-x):u+(l-c)*x}else y=l+(d-m)*Math.tan(r-Math.PI/2);var b=[["M",v,m]];return(g?y<=v&&y>=n.x:y>=v&&y<=n.x)&&b.push(["L",y,m]),b.push(["L",n.x,n.y],["L",s.x,s.y]),b}}});var lO={borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}},lC=(O=function(t,e){return(O=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}O(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lE=ly.getStartAndEndRadians,lL=tu.noop,lB=tG.clamp,lI=tG.extend,lD=tG.fireEvent,lz=tG.merge,lR=tG.pick,lN=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lC(e,t),e.prototype.animate=function(t){var e=this,i=e.points,o=e.startAngleRad;t||i.forEach(function(t){var i=t.graphic,r=t.shapeArgs;i&&r&&(i.attr({r:lR(t.startR,e.center&&e.center[3]/2),start:o,end:o}),i.animate({r:r.r,start:r.start,end:r.end},e.options.animation))})},e.prototype.drawEmpty=function(){var t,e,i=this.startAngleRad,o=this.endAngleRad,r=this.options;0===this.total&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,o).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:iq.arc(t,e,this.center[2]/2,0,{start:i,end:o,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":r.borderWidth,fill:r.fillColor||"none",stroke:r.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())},e.prototype.drawPoints=function(){var t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})},e.prototype.generatePoints=function(){t.prototype.generatePoints.call(this),this.updateTotals()},e.prototype.getX=function(t,e,i,o){var r=this.center,n=this.radii?this.radii[i.index]||0:r[2]/2,s=o.dataLabelPosition,a=(null==s?void 0:s.distance)||0,h=Math.asin(lB((t-r[1])/(n+a),-1,1));return r[0]+Math.cos(h)*(n+a)*(e?-1:1)+(a>0?(e?-1:1)*(o.padding||0):0)},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.redrawPoints=function(){var t,e,i,o,r=this,n=r.chart;this.drawEmpty(),r.group&&!n.styledMode&&r.group.shadow(r.options.shadow),r.points.forEach(function(s){var a={};e=s.graphic,!s.isNull&&e?(o=s.shapeArgs,t=s.getTranslate(),n.styledMode||(i=r.pointAttribs(s,s.selected&&"select")),s.delayedRendering?(e.setRadialReference(r.center).attr(o).attr(t),n.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),s.delayedRendering=!1):(e.setRadialReference(r.center),n.styledMode||lz(!0,a,i),lz(!0,a,o,t),e.animate(a)),e.attr({visibility:s.visible?"inherit":"hidden"}),e.addClass(s.getClassName(),!0)):e&&(s.graphic=e.destroy())})},e.prototype.sortByAngle=function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},e.prototype.translate=function(t){lD(this,"translate"),this.generatePoints();var e,i,o,r,n,s,a,h=this.options,l=h.slicedOffset,d=lE(h.startAngle,h.endAngle),c=this.startAngleRad=d.start,p=(this.endAngleRad=d.end)-c,u=this.points,f=h.ignoreHiddenPoint,g=u.length,v=0;for(t||(this.center=t=this.getCenter()),s=0;s<g;s++){a=u[s],e=c+v*p,a.isValid()&&(!f||a.visible)&&(v+=a.percentage/100),i=c+v*p;var m={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*e)/1e3,end:Math.round(1e3*i)/1e3};a.shapeType="arc",a.shapeArgs=m,(o=(i+e)/2)>1.5*Math.PI?o-=2*Math.PI:o<-Math.PI/2&&(o+=2*Math.PI),a.slicedTranslation={translateX:Math.round(Math.cos(o)*l),translateY:Math.round(Math.sin(o)*l)},r=Math.cos(o)*t[2]/2,n=Math.sin(o)*t[2]/2,a.tooltipPos=[t[0]+.7*r,t[1]+.7*n],a.half=+(o<-Math.PI/2||o>Math.PI/2),a.angle=o}lD(this,"afterTranslate")},e.prototype.updateTotals=function(){var t,e,i=this.points,o=i.length,r=this.options.ignoreHiddenPoint,n=0;for(t=0;t<o;t++)(e=i[t]).isValid()&&(!r||e.visible)&&(n+=e.y);for(t=0,this.total=n;t<o;t++)(e=i[t]).percentage=n>0&&(e.visible||!r)?e.y/n*100:0,e.total=n},e.defaultOptions=lz(sD.defaultOptions,lO),e}(sD);lI(lN.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:h_.prototype.drawTracker,getCenter:ly.getCenter,getSymbol:lL,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:h_.prototype.pointAttribs,pointClass:lP,requireSorting:!1,searchPoint:lL,trackerGroups:["group","dataLabelsGroup"]}),si.registerSeriesType("pie",lN);var lW=tu.composed,lG=tu.noop,lX=it.distribute,lF=si.series,lH=tG.arrayMax,lj=tG.clamp,lY=tG.defined,l_=tG.pick,lU=tG.pushUnique,lV=tG.relativeLength;!function(t){var e={radialDistributionY:function(t,e){var i;return((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.top)||0)+t.distributeBox.pos},radialDistributionX:function(t,e,i,o,r){var n=r.dataLabelPosition;return t.getX(i<((null==n?void 0:n.top)||0)+2||i>((null==n?void 0:n.bottom)||0)-2?o:i,e.half,e,r)},justify:function(t,e,i,o){var r;return o[0]+(t.half?-1:1)*(i+((null===(r=e.dataLabelPosition)||void 0===r?void 0:r.distance)||0))},alignToPlotEdges:function(t,e,i,o){var r=t.getBBox().width;return e?r+o:i-r-o},alignToConnectors:function(t,e,i,o){var r,n=0;return t.forEach(function(t){(r=t.dataLabel.getBBox().width)>n&&(n=r)}),e?n+o:i-n-o}};function i(t,e){var i=Math.PI/2,o=t.shapeArgs||{},r=o.start,n=void 0===r?0:r,s=o.end,a=void 0===s?0:s,h=t.angle||0;e>0&&n<i&&a>i&&h>i/2&&h<1.5*i&&(h=h<=i?Math.max(i/2,(n+i)/2):Math.min(1.5*i,(i+a)/2));var l=this.center,d=this.options,c=l[2]/2,p=Math.cos(h),u=Math.sin(h),f=l[0]+p*c,g=l[1]+u*c,v=Math.min((d.slicedOffset||0)+(d.borderWidth||0),e/5);return{natural:{x:f+p*e,y:g+u*e},computed:{},alignment:e<0?"center":t.half?"right":"left",connectorPosition:{angle:h,breakAt:{x:f+p*v,y:g+u*v},touchingSliceAt:{x:f,y:g}},distance:e}}function o(){var t,e,i,o,r=this,n=this,s=n.points,a=n.chart,h=a.plotWidth,l=a.plotHeight,d=a.plotLeft,c=Math.round(a.chartWidth/3),p=n.center,u=p[2]/2,f=p[1],g=[[],[]],v=[0,0,0,0],m=n.dataLabelPositioners,y=0;n.visible&&(null===(t=n.hasDataLabels)||void 0===t?void 0:t.call(n))&&(s.forEach(function(t){(t.dataLabels||[]).forEach(function(t){t.shortened&&(t.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.shortened=!1)})}),lF.prototype.drawDataLabels.apply(n),s.forEach(function(t){(t.dataLabels||[]).forEach(function(e,i){var o,n=p[2]/2,s=e.options,a=lV((null==s?void 0:s.distance)||0,n);0===i&&g[t.half].push(t),!lY(null===(o=null==s?void 0:s.style)||void 0===o?void 0:o.width)&&e.getBBox().width>c&&(e.css({width:Math.round(.7*c)+"px"}),e.shortened=!0),e.dataLabelPosition=r.getDataLabelPosition(t,a),y=Math.max(y,a)})}),g.forEach(function(t,e){var r,s,c,g=t.length,x=[],b=0;g&&(n.sortByAngle(t,e-.5),y>0&&(r=Math.max(0,f-u-y),s=Math.min(f+u+y,a.plotHeight),t.forEach(function(t){(t.dataLabels||[]).forEach(function(e){var i,o=e.dataLabelPosition;o&&o.distance>0&&(o.top=Math.max(0,f-u-o.distance),o.bottom=Math.min(f+u+o.distance,a.plotHeight),b=e.getBBox().height||21,e.lineHeight=a.renderer.fontMetrics(e.text||e).h+2*e.padding,t.distributeBox={target:((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.natural.y)||0)-o.top+e.lineHeight/2,size:b,rank:t.y},x.push(t.distributeBox))})}),lX(x,c=s+b-r,c/5)),t.forEach(function(r){(r.dataLabels||[]).forEach(function(s){var a=s.options||{},c=r.distributeBox,f=s.dataLabelPosition,g=(null==f?void 0:f.natural.y)||0,y=a.connectorPadding||0,b=s.lineHeight||21,M=(b-s.getBBox().height)/2,k=0,w=g,S="inherit";if(f){if(x&&lY(c)&&f.distance>0&&(void 0===c.pos?S="hidden":(o=c.size,w=m.radialDistributionY(r,s))),a.justify)k=m.justify(r,s,u,p);else switch(a.alignTo){case"connectors":k=m.alignToConnectors(t,e,h,d);break;case"plotEdges":k=m.alignToPlotEdges(s,e,h,d);break;default:k=m.radialDistributionX(n,r,w-M,g,s)}if(f.attribs={visibility:S,align:f.alignment},f.posAttribs={x:k+(a.x||0)+(({left:y,right:-y})[f.alignment]||0),y:w+(a.y||0)-b/2},f.computed.x=k,f.computed.y=w-M,l_(a.crop,!0)){i=s.getBBox().width;var A=void 0;k-i<y&&1===e?(A=Math.round(i-k+y),v[3]=Math.max(A,v[3])):k+i>h-y&&0===e&&(A=Math.round(k+i-h+y),v[1]=Math.max(A,v[1])),w-o/2<0?v[0]=Math.max(Math.round(-w+o/2),v[0]):w+o/2>l&&(v[2]=Math.max(Math.round(w+o/2-l),v[2])),f.sideOverflow=A}}})}))}),(0===lH(v)||this.verifyDataLabelOverflow(v))&&(this.placeDataLabels(),this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(i){var o,r=i.options||{},s=r.connectorColor,h=r.connectorWidth,l=void 0===h?1:h,d=i.dataLabelPosition;if(l){var c=void 0;e=i.connector,d&&d.distance>0?(c=!e,e||(i.connector=e=a.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+t.colorIndex+(t.className?" "+t.className:"")).add(n.dataLabelsGroup)),a.styledMode||e.attr({"stroke-width":l,stroke:s||t.color||"#666666"}),e[c?"attr":"animate"]({d:t.getConnectorPath(i)}),e.attr({visibility:null===(o=d.attribs)||void 0===o?void 0:o.visibility})):e&&(i.connector=e.destroy())}})})))}function r(){this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(t){var e,i,o=t.dataLabelPosition;o?(o.sideOverflow&&(t.css({width:Math.max(t.getBBox().width-o.sideOverflow,0)+"px",textOverflow:(null===(i=null===(e=t.options)||void 0===e?void 0:e.style)||void 0===i?void 0:i.textOverflow)||"ellipsis"}),t.shortened=!0),t.attr(o.attribs),t[t.moved?"animate":"attr"](o.posAttribs),t.moved=!0):t&&t.attr({y:-9999})}),delete t.distributeBox},this)}function n(t){var e=this.center,i=this.options,o=i.center,r=i.minSize||80,n=r,s=null!==i.size;return!s&&(null!==o[0]?n=Math.max(e[2]-Math.max(t[1],t[3]),r):(n=Math.max(e[2]-t[1]-t[3],r),e[0]+=(t[3]-t[1])/2),null!==o[1]?n=lj(n,r,e[2]-Math.max(t[0],t[2])):(n=lj(n,r,e[2]-t[0]-t[2]),e[1]+=(t[0]-t[2])/2),n<e[2]?(e[2]=n,e[3]=Math.min(i.thickness?Math.max(0,n-2*i.thickness):Math.max(0,lV(i.innerSize||0,n)),n),this.translate(e),this.drawDataLabels&&this.drawDataLabels()):s=!0),s}t.compose=function(t){if(h5.compose(lF),lU(lW,"PieDataLabel")){var s=t.prototype;s.dataLabelPositioners=e,s.alignDataLabel=lG,s.drawDataLabels=o,s.getDataLabelPosition=i,s.placeDataLabels=r,s.verifyDataLabelOverflow=n}}}(tt||(tt={}));var lq=tt;(C=te||(te={})).getCenterOfPoints=function(t){var e=t.reduce(function(t,e){return t.x+=e.x,t.y+=e.y,t},{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},C.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},C.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},C.pointInPolygon=function(t,e){var i,o,r=t.x,n=t.y,s=e.length,a=!1;for(i=0,o=s-1;i<s;o=i++){var h=e[i],l=h[0],d=h[1],c=e[o],p=c[0],u=c[1];d>n!=u>n&&r<(p-l)*(n-d)/(u-d)+l&&(a=!a)}return a};var lZ=te.pointInPolygon,lK=tG.addEvent,l$=tG.getAlignFactor,lJ=tG.fireEvent,lQ=tG.objectEach,l0=tG.pick;function l1(t){for(var e,i,o,r,n,s=t.length,a=!1,h=0;h<s;h++)(e=t[h])&&(e.oldOpacity=e.opacity,e.newOpacity=1,e.absoluteBox=function(t){var e,i;if(t&&(!t.alignAttr||t.placed)){var o=t.box?0:t.padding||0,r=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},n=t.getBBox(),s=n.height,a=n.polygon,h=n.width,l=l$(t.alignValue)*h;return t.width=h,t.height=s,{x:r.x+((null===(e=t.parentGroup)||void 0===e?void 0:e.translateX)||0)+o-l,y:r.y+((null===(i=t.parentGroup)||void 0===i?void 0:i.translateY)||0)+o,width:h-2*o,height:s-2*o,polygon:a}}}(e));t.sort(function(t,e){return(e.labelrank||0)-(t.labelrank||0)});for(var h=0;h<s;++h)for(var l=null==(r=(i=t[h])&&i.absoluteBox)?void 0:r.polygon,d=h+1;d<s;++d){n=(o=t[d])&&o.absoluteBox;var c=!1;if(r&&n&&i!==o&&0!==i.newOpacity&&0!==o.newOpacity&&"hidden"!==i.visibility&&"hidden"!==o.visibility){var p=n.polygon;if(l&&p&&l!==p?function(t,e){for(var i=0;i<t.length;i++){var o=t[i];if(lZ({x:o[0],y:o[1]},e))return!0}return!1}(l,p)&&(c=!0):!(n.x>=r.x+r.width||n.x+n.width<=r.x||n.y>=r.y+r.height||n.y+n.height<=r.y)&&(c=!0),c){var u=i.labelrank<o.labelrank?i:o,f=u.text;u.newOpacity=0,(null==f?void 0:f.element.querySelector("textPath"))&&f.hide()}}}for(var g=0;g<t.length;g++)l2(t[g],this)&&(a=!0);a&&lJ(this,"afterHideAllOverlappingLabels")}function l2(t,e){var i,o=!1;return t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.hasClass("highcharts-data-label")?(t[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),o=!0,t[t.isOld?"animate":"attr"]({opacity:i},void 0,function(){e.styledMode||t.css({pointerEvents:i?"auto":"none"})}),lJ(e,"afterHideOverlappingLabel")):t.attr({opacity:i})),t.isOld=!0),o}function l3(){for(var t,e=this,i=[],o=0,r=e.labelCollectors||[];o<r.length;o++){var n=r[o];i=i.concat(n())}for(var s=0,a=e.yAxis||[];s<a.length;s++){var h=a[s];h.stacking&&h.options.stackLabels&&!h.options.stackLabels.allowOverlap&&lQ(h.stacking.stacks,function(t){lQ(t,function(t){t.label&&i.push(t.label)})})}for(var l=0,d=e.series||[];l<d.length;l++){var c=d[l];if(c.visible&&(null===(t=c.hasDataLabels)||void 0===t?void 0:t.call(c))){var p=function(t){for(var o=function(t){t.visible&&(t.dataLabels||[]).forEach(function(o){var r,n,s=o.options||{};o.labelrank=l0(s.labelrank,t.labelrank,null===(r=t.shapeArgs)||void 0===r?void 0:r.height),(null!==(n=s.allowOverlap)&&void 0!==n?n:Number(s.distance)>0)?(o.oldOpacity=o.opacity,o.newOpacity=1,l2(o,e)):i.push(o)})},r=0;r<t.length;r++)o(t[r])};p(c.nodes||[]),p(c.points)}}this.hideOverlappingLabels(i)}var l5={compose:function(t){var e=t.prototype;e.hideOverlappingLabels||(e.hideOverlappingLabels=l1,lK(t,"render",l3))}},l6=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},l9=tu.noop,l4=tG.addEvent,l8=tG.extend,l7=tG.isObject,dt=tG.merge,de=tG.relativeLength,di={radius:0,scope:"stack",where:void 0},dr=l9,dn=l9;function ds(t,e,i,o,r){void 0===r&&(r={});var n=dr(t,e,i,o,r),s=r.innerR,a=void 0===s?0:s,h=r.r,l=void 0===h?i:h,d=r.start,c=r.end;if(r.open||!r.borderRadius)return n;for(var p=(void 0===c?0:c)-(void 0===d?0:d),u=Math.sin(p/2),f=Math.max(Math.min(de(r.borderRadius||0,l-a),(l-a)/2,l*u/(1+u)),0),g=Math.min(f,p/Math.PI*2*a),v=n.length-1;v--;)!function(t,e,i){var o,r,n,s=t[e],a=t[e+1];if("Z"===a[0]&&(a=t[0]),("M"===s[0]||"L"===s[0])&&"A"===a[0]?(o=s,r=a,n=!0):"A"===s[0]&&("M"===a[0]||"L"===a[0])&&(o=a,r=s),o&&r&&r.params){var h=r[1],l=r[5],d=r.params,c=d.start,p=d.end,u=d.cx,f=d.cy,g=l?h-i:h+i,v=g?Math.asin(i/g):0,m=l?v:-v,y=Math.cos(v)*g;n?(d.start=c+m,o[1]=u+y*Math.cos(c),o[2]=f+y*Math.sin(c),t.splice(e+1,0,["A",i,i,0,0,1,u+h*Math.cos(d.start),f+h*Math.sin(d.start)])):(d.end=p-m,r[6]=u+h*Math.cos(d.end),r[7]=f+h*Math.sin(d.end),t.splice(e+1,0,["A",i,i,0,0,1,u+y*Math.cos(p),f+y*Math.sin(p)])),r[4]=Math.abs(d.end-d.start)<Math.PI?0:1}}(n,v,v>1?g:f);return n}function da(){var t,e;if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d()))for(var i=this.options,o=this.yAxis,r="percent"===i.stacking,n=null===(e=null===(t=ee.plotOptions)||void 0===t?void 0:t[this.type])||void 0===e?void 0:e.borderRadius,s=dh(i.borderRadius,l7(n)?n:{}),a=o.options.reversed,h=0,l=this.points;h<l.length;h++){var d=l[h],c=d.shapeArgs;if("roundedRect"===d.shapeType&&c){var p=c.width,u=void 0===p?0:p,f=c.height,g=void 0===f?0:f,v=c.y,m=void 0===v?0:v,y=g;if("stack"===s.scope&&d.stackTotal){var x=o.translate(r?100:d.stackTotal,!1,!0,!1,!0),b=o.translate(i.threshold||0,!1,!0,!1,!0),M=this.crispCol(0,Math.min(x,b),0,Math.abs(x-b));m=M.y,y=M.height}var k=(d.negative?-1:1)*(a?-1:1)==-1,w=s.where;!w&&this.is("waterfall")&&Math.abs((d.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(w="all"),w||(w="end");var S=Math.min(de(s.radius,u),u/2,"all"===w?g/2:1/0)||0;"end"===w&&(k&&(m-=S),y+=S),l8(c,{brBoxHeight:y,brBoxY:m,r:S})}}}function dh(t,e){return l7(t)||(t={radius:t||0}),dt(di,e,t)}function dl(){for(var t=dh(this.options.borderRadius),e=0,i=this.points;e<i.length;e++){var o=i[e].shapeArgs;o&&(o.borderRadius=de(t.radius,(o.r||0)-(o.innerR||0)))}}function dd(t,e,i,o,r){void 0===r&&(r={});var n=dn(t,e,i,o,r),s=r.r,a=void 0===s?0:s,h=r.brBoxHeight,l=void 0===h?o:h,d=r.brBoxY,c=void 0===d?e:d,p=e-c,u=c+l-(e+o),f=p-a>-.1?0:a,g=u-a>-.1?0:a,v=Math.max(f&&p,0),m=Math.max(g&&u,0),y=[t+f,e],x=[t+i-f,e],b=[t+i,e+f],M=[t+i,e+o-g],k=[t+i-g,e+o],w=[t+g,e+o],S=[t,e+o-g],A=[t,e+f],T=function(t,e){return Math.sqrt(Math.pow(t,2)-Math.pow(e,2))};if(v){var P=T(f,f-v);y[0]-=P,x[0]+=P,b[1]=A[1]=e+f-v}if(o<f-v){var P=T(f,f-v-o);b[0]=M[0]=t+i-f+P,k[0]=Math.min(b[0],k[0]),w[0]=Math.max(M[0],w[0]),S[0]=A[0]=t+f-P,b[1]=A[1]=e+o}if(m){var P=T(g,g-m);k[0]+=P,w[0]-=P,M[1]=S[1]=e+o-g+m}if(o<g-m){var P=T(g,g-m-o);b[0]=M[0]=t+i-g+P,x[0]=Math.min(b[0],x[0]),y[0]=Math.max(M[0],y[0]),S[0]=A[0]=t+g-P,M[1]=S[1]=e}return n.length=0,n.push(l6(["M"],y,!0),l6(["L"],x,!0),l6(["A",f,f,0,0,1],b,!0),l6(["L"],M,!0),l6(["A",g,g,0,0,1],k,!0),l6(["L"],w,!0),l6(["A",g,g,0,0,1],S,!0),l6(["L"],A,!0),l6(["A",f,f,0,0,1],y,!0),["Z"]),n}var dc=tG.diffObjects,dp=tG.extend,du=tG.find,df=tG.merge,dg=tG.pick,dv=tG.uniqueKey;!function(t){function e(t,e){var i=t.condition;(i.callback||function(){return this.chartWidth<=dg(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=dg(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=dg(i.minWidth,0)&&this.chartHeight>=dg(i.minHeight,0)}).call(this)&&e.push(t._id)}function i(t,e){var i,o=this,r=this.options.responsive,n=this.currentResponsive,s=[];!e&&r&&r.rules&&r.rules.forEach(function(t){void 0===t._id&&(t._id=dv()),o.matchResponsiveRule(t,s)},this);var a=df.apply(void 0,s.map(function(t){return du((null==r?void 0:r.rules)||[],function(e){return e._id===t})}).map(function(t){return null==t?void 0:t.chartOptions}));a.isResponsiveOptions=!0,s=s.toString()||void 0;var h=null==n?void 0:n.ruleIds;s===h||(n&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(n.undoOptions,t,!0),this.updatingResponsive=!1),s?((i=dc(a,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:s,mergedOptions:a,undoOptions:i},this.updatingResponsive||this.update(a,t,!0)):this.currentResponsive=void 0)}t.compose=function(t){var o=t.prototype;return o.matchResponsiveRule||dp(o,{matchResponsiveRule:e,setResponsive:i}),t}}(ti||(ti={}));var dm=ti;function dy(t,e,i,o){return[["M",t,e+o/2],["L",t+i,e],["L",t,e+o/2],["L",t+i,e+o]]}function dx(t,e,i,o){return dy(t,e,i/2,o)}function db(t,e,i,o){return[["M",t+i,e],["L",t,e+o/2],["L",t+i,e+o],["Z"]]}function dM(t,e,i,o){return db(t,e,i/2,o)}tu.AST=eY,tu.Axis=rA,tu.Chart=aC,tu.Color=ep,tu.DataLabel=h5,tu.DataTableCore=n1,tu.Fx=em,tu.HTMLElement=oX,tu.Legend=s1,tu.LegendSymbol=n9,tu.OverlappingDataLabels=tu.OverlappingDataLabels||l5,tu.PlotLineOrBand=rK,tu.Point=nP,tu.Pointer=nV,tu.RendererRegistry=e6,tu.Series=sD,tu.SeriesRegistry=si,tu.StackItem=aq,tu.SVGElement=iB,tu.SVGRenderer=oS,tu.Templating=e5,tu.Tick=o9,tu.Time=t9,tu.Tooltip=nd,tu.animate=eO,tu.animObject=eT,tu.chart=aC.chart,tu.color=ep.parse,tu.dateFormat=e5.dateFormat,tu.defaultOptions=ee,tu.distribute=it.distribute,tu.format=e5.format,tu.getDeferredAnimation=eC,tu.getOptions=function(){return ee},tu.numberFormat=e5.numberFormat,tu.seriesType=si.seriesType,tu.setAnimation=eE,tu.setOptions=eo,tu.stop=eP,tu.time=ei,tu.timers=em.timers,({compose:function(t,e,i){var o=t.types.pie;if(!e.symbolCustomAttribs.includes("borderRadius")){var r=i.prototype.symbols;l4(t,"afterColumnTranslate",da,{order:9}),l4(o,"afterTranslate",dl),e.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),dr=r.arc,dn=r.roundedRect,r.arc=ds,r.roundedRect=dd}},optionsToObject:dh}).compose(tu.Series,tu.SVGElement,tu.SVGRenderer),h7.compose(tu.Series.types.column),h5.compose(tu.Series),rE.compose(tu.Axis),oX.compose(tu.SVGRenderer),s1.compose(tu.Chart),rD.compose(tu.Axis),l5.compose(tu.Chart),lq.compose(tu.Series.types.pie),rK.compose(tu.Chart,tu.Axis),nV.compose(tu.Chart),dm.compose(tu.Chart),aX.compose(tu.Axis,tu.Chart,tu.Series),ho.compose(tu.Axis,tu.Chart,tu.Series),nd.compose(tu.Pointer),tG.extend(tu,tG);var dk=function(t){var e=t.prototype.symbols;e.arrow=dy,e["arrow-filled"]=db,e["arrow-filled-half"]=dM,e["arrow-half"]=dx,e["triangle-left"]=db,e["triangle-left-half"]=dM},dw=tG.defined,dS=tG.error,dA=tG.merge,dT=tG.objectEach,dP=tu.deg2rad,dO=Math.max,dC=Math.min,dE=function(){function t(t,e,i){this.init(t,e,i)}return t.prototype.init=function(t,e,i){this.fromPoint=t,this.toPoint=e,this.options=i,this.chart=t.series.chart,this.pathfinder=this.chart.pathfinder},t.prototype.renderPath=function(t,e){var i=this.chart,o=i.styledMode,r=this.pathfinder,n={},s=this.graphics&&this.graphics.path;r.group||(r.group=i.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(i.seriesGroup)),r.group.translate(i.plotLeft,i.plotTop),s&&s.renderer||(s=i.renderer.path().add(r.group),o||s.attr({opacity:0})),s.attr(e),n.d=t,o||(n.opacity=1),s.animate(n),this.graphics=this.graphics||{},this.graphics.path=s},t.prototype.addMarker=function(t,e,i){var o,r,n,s,a,h,l,d=this.fromPoint.series.chart,c=d.pathfinder,p=d.renderer,u="start"===t?this.fromPoint:this.toPoint,f=u.getPathfinderAnchorPoint(e);e.enabled&&((l="start"===t?i[1]:i[i.length-2])&&"M"===l[0]||"L"===l[0])&&(h={x:l[1],y:l[2]},r=u.getRadiansToVector(h,f),o=u.getMarkerVector(r,e.radius,f),e.width&&e.height?(s=e.width,a=e.height):s=a=2*e.radius,this.graphics=this.graphics||{},n={x:o.x-s/2,y:o.y-a/2,width:s,height:a,rotation:-r/dP,rotationOriginX:o.x,rotationOriginY:o.y},this.graphics[t]?this.graphics[t].animate(n):(this.graphics[t]=p.symbol(e.symbol).addClass("highcharts-point-connecting-path-"+t+"-marker highcharts-color-"+this.fromPoint.colorIndex).attr(n).add(c.group),p.styledMode||this.graphics[t].attr({fill:e.color||this.fromPoint.color,stroke:e.lineColor,"stroke-width":e.lineWidth,opacity:0}).animate({opacity:1},u.series.options.animation)))},t.prototype.getPath=function(t){var e=this.pathfinder,i=this.chart,o=e.algorithms[t.type],r=e.chartObstacles;return"function"!=typeof o?(dS('"'+t.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]}):(o.requiresObstacles&&!r&&(r=e.chartObstacles=e.getChartObstacles(t),i.options.connectors.algorithmMargin=t.algorithmMargin,e.chartObstacleMetrics=e.getObstacleMetrics(r)),o(this.fromPoint.getPathfinderAnchorPoint(t.startMarker),this.toPoint.getPathfinderAnchorPoint(t.endMarker),dA({chartObstacles:r,lineObstacles:e.lineObstacles||[],obstacleMetrics:e.chartObstacleMetrics,hardBounds:{xMin:0,xMax:i.plotWidth,yMin:0,yMax:i.plotHeight},obstacleOptions:{margin:t.algorithmMargin},startDirectionX:e.getAlgorithmStartDirection(t.startMarker)},t)))},t.prototype.render=function(){var t=this.fromPoint,e=t.series,i=e.chart,o=i.pathfinder,r={},n=dA(i.options.connectors,e.options.connectors,t.options.connectors,this.options);!i.styledMode&&(r.stroke=n.lineColor||t.color,r["stroke-width"]=n.lineWidth,n.dashStyle&&(r.dashstyle=n.dashStyle)),r.class="highcharts-point-connecting-path highcharts-color-"+t.colorIndex,dw((n=dA(r,n)).marker.radius)||(n.marker.radius=dC(dO(Math.ceil((n.algorithmMargin||8)/2)-1,1),5));var s=this.getPath(n),a=s.path;s.obstacles&&(o.lineObstacles=o.lineObstacles||[],o.lineObstacles=o.lineObstacles.concat(s.obstacles)),this.renderPath(a,r),this.addMarker("start",dA(n.marker,n.startMarker),a),this.addMarker("end",dA(n.marker,n.endMarker),a)},t.prototype.destroy=function(){this.graphics&&(dT(this.graphics,function(t){t.destroy()}),delete this.graphics)},t}(),dL=tu.composed,dB=tG.addEvent,dI=tG.merge,dD=tG.pushUnique,dz=tG.wrap,dR={color:"#ccd3ff",width:2,label:{format:"%[abdYHM]",formatter:function(t,e){return this.axis.chart.time.dateFormat(e||"",t,!0)},rotation:0,style:{fontSize:"0.7em"}}};function dN(){var t=this.options,e=t.currentDateIndicator;if(e){var i="object"==typeof e?dI(dR,e):dI(dR);i.value=Date.now(),i.className="highcharts-current-date-indicator",t.plotLines||(t.plotLines=[]),t.plotLines.push(i)}}function dW(){this.label&&this.label.attr({text:this.getLabelText(this.options.label)})}function dG(t,e){var i=this.options;return i&&i.className&&-1!==i.className.indexOf("highcharts-current-date-indicator")&&i.label&&"function"==typeof i.label.formatter?(i.value=Date.now(),i.label.formatter.call(this,i.value,i.label.format)):t.call(this,e)}var dX=(E=function(t,e){return(E=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}E(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),dF=tG.isArray,dH=tG.merge,dj=tG.splat,dY=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return dX(e,t),e.prototype.init=function(e,i){var o,r=e.xAxis,n=e.yAxis;e.xAxis=e.yAxis=void 0;var s=dH(!0,{chart:{type:"gantt"},title:{text:""},legend:{enabled:!1},navigator:{series:{type:"gantt"},yAxis:{type:"category"}}},e,{isGantt:!0});e.xAxis=r,e.yAxis=n,s.xAxis=(dF(e.xAxis)?e.xAxis:[e.xAxis||{},{}]).map(function(t,e){var i,r,n;return 1===e&&(o=0),dH({grid:{borderColor:"#cccccc",enabled:!0},opposite:null===(n=null!==(r=null===(i=ee.xAxis)||void 0===i?void 0:i.opposite)&&void 0!==r?r:t.opposite)||void 0===n||n,linkedTo:o},t,{type:"datetime"})}),s.yAxis=dj(e.yAxis||{}).map(function(t){return dH({grid:{borderColor:"#cccccc",enabled:!0},staticScale:50,reversed:!0,type:t.categories?t.type:"treegrid"},t)}),t.prototype.init.call(this,s,i)},e}(aC);(L=dY||(dY={})).ganttChart=function(t,e,i){return new L(t,e,i)};var d_=dY,dU=tu.isTouchDevice,dV=tG.addEvent,dq=tG.merge,dZ=tG.pick,dK=[];function d$(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function dJ(){var t,e,i,o,r=this.legend,n=this.navigator;if(n){e=r&&r.options,i=n.xAxis,o=n.yAxis;var s=n.scrollbarHeight,a=n.scrollButtonSize;this.inverted?(n.left=n.opposite?this.chartWidth-s-n.height:this.spacing[3]+s,n.top=this.plotTop+a):(n.left=dZ(i.left,this.plotLeft+a),n.top=n.navigatorOptions.top||this.chartHeight-n.height-s-((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(e&&"bottom"===e.verticalAlign&&"proximate"!==e.layout&&e.enabled&&!e.floating?r.legendHeight+dZ(e.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),i&&o&&(this.inverted?i.options.left=o.options.left=n.left:i.options.top=o.options.top=n.top,i.setAxisSize(),o.setAxisSize())}}function dQ(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new to(this),dZ(t.redraw,!0)&&this.redraw(t.animation))}function d0(){var t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new to(this))}function d1(){var t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!dU&&"x"===this.zooming.type||dU&&"x"===this.zooming.pinchType))return!1}function d2(t){var e=t.navigator;if(e&&t.xAxis[0]){var i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function d3(t){var e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(dq(!0,this.options.navigator,e),dq(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}var d5=function(t,e){if(tG.pushUnique(dK,t)){var i=t.prototype;to=e,i.callbacks.push(d2),dV(t,"afterAddSeries",d$),dV(t,"afterSetChartSize",dJ),dV(t,"afterUpdate",dQ),dV(t,"beforeRender",d0),dV(t,"beforeShowResetZoom",d1),dV(t,"update",d3)}},d6=tu.isTouchDevice,d9=tG.addEvent,d4=tG.correctFloat,d8=tG.defined,d7=tG.isNumber,ct=tG.pick;function ce(){this.navigatorAxis||(this.navigatorAxis=new co(this))}function ci(t){var e,i=this.chart,o=i.options,r=o.navigator,n=this.navigatorAxis,s=i.zooming.pinchType,a=o.rangeSelector,h=i.zooming.type;if(this.isXAxis&&((null==r?void 0:r.enabled)||(null==a?void 0:a.enabled))){if("y"===h&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===h||d6&&"xy"===s)&&this.options.range){var l=n.previousZoom;d8(t.min)?n.previousZoom=[this.min,this.max]:l&&(t.min=l[0],t.max=l[1],n.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}var co=function(){function t(t){this.axis=t}return t.compose=function(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),d9(t,"init",ce),d9(t,"setExtremes",ci))},t.prototype.destroy=function(){this.axis=void 0},t.prototype.toFixedRange=function(t,e,i,o){var r=this.axis,n=(r.pointRange||0)/2,s=ct(i,r.translate(t,!0,!r.horiz)),a=ct(o,r.translate(e,!0,!r.horiz));return d8(i)||(s=d4(s+n)),d8(o)||(a=d4(a-n)),d7(s)&&d7(a)||(s=a=void 0),{min:s,max:a}},t}(),cr=ep.parse,cn=si.seriesTypes,cs={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:cr("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===cn.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},ca=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},ch=tG.relativeLength,cl={"navigator-handle":function(t,e,i,o,r){void 0===r&&(r={});var n=r.width?r.width/2:i,s=ch(r.borderRadius||0,Math.min(2*n,o));return ca([["M",-1.5,(o=r.height||o)/2-3.5],["L",-1.5,o/2+4.5],["M",.5,o/2-3.5],["L",.5,o/2+4.5]],iq.rect(-n-1,.5,2*n+1,o,{r:s}),!0)}},cd=tG.defined,cc=tu.composed,cp=e6.getRendererType,cu={setFixedRange:function(t){var e=this.xAxis[0];cd(e.dataMax)&&cd(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}}.setFixedRange,cf=tG.addEvent,cg=tG.extend,cv=tG.pushUnique;function cm(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}var cy=function(t,e,i){co.compose(e),cv(cc,"Navigator")&&(t.prototype.setFixedRange=cu,cg(cp().prototype.symbols,cl),cf(i,"afterUpdate",cm),eo({navigator:cs}))},cx=tu.composed,cb=tG.addEvent,cM=tG.defined,ck=tG.pick,cw=tG.pushUnique;!function(t){var e;function i(t){var e,i,o=ck(null===(e=t.options)||void 0===e?void 0:e.min,t.min),r=ck(null===(i=t.options)||void 0===i?void 0:i.max,t.max);return{axisMin:o,axisMax:r,scrollMin:cM(t.dataMin)?Math.min(o,t.min,t.dataMin,ck(t.threshold,1/0)):o,scrollMax:cM(t.dataMax)?Math.max(r,t.max,t.dataMax,ck(t.threshold,-1/0)):r}}function o(){var t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function r(){var t,o,r=this;(null===(o=null===(t=r.options)||void 0===t?void 0:t.scrollbar)||void 0===o?void 0:o.enabled)&&(r.options.scrollbar.vertical=!r.horiz,r.options.startOnTick=r.options.endOnTick=!1,r.scrollbar=new e(r.chart.renderer,r.options.scrollbar,r.chart),cb(r.scrollbar,"changed",function(t){var e,o,n=i(r),s=n.axisMin,a=n.axisMax,h=n.scrollMin,l=n.scrollMax-h;if(cM(s)&&cM(a)){if(r.horiz&&!r.reversed||!r.horiz&&r.reversed?(e=h+l*this.to,o=h+l*this.from):(e=h+l*(1-this.from),o=h+l*(1-this.to)),this.shouldUpdateExtremes(t.DOMType)){var d="mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&void 0;r.setExtremes(o,e,!0,d,t)}else this.setRange(this.from,this.to)}}))}function n(){var t,e,o,r=i(this),n=r.scrollMin,s=r.scrollMax,a=this.scrollbar,h=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,d=this.options.margin||0;if(a&&l){if(this.horiz)this.opposite||(l[1]+=h),a.position(this.left,this.top+this.height+2+l[1]-(this.opposite?d:0),this.width,this.height),this.opposite||(l[1]+=d),t=1;else{this.opposite&&(l[0]+=h);var c=void 0;c=a.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:d):this.opposite?0:d,a.position(c,this.top,this.width,this.height),this.opposite&&(l[0]+=d),t=0}if(l[t]+=a.size+(a.options.margin||0),isNaN(n)||isNaN(s)||!cM(this.min)||!cM(this.max)||this.dataMin===this.dataMax)a.setRange(0,1);else if(this.min===this.max){var p=this.pointRange/(this.dataMax+1);e=p*this.min,o=p*(this.max+1),a.setRange(e,o)}else e=(this.min-n)/(s-n),o=(this.max-n)/(s-n),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(e,o):a.setRange(1-o,1-e)}}t.compose=function(t,i){cw(cx,"Axis.Scrollbar")&&(e=i,cb(t,"afterGetOffset",o),cb(t,"afterInit",r),cb(t,"afterRender",n))}}(tr||(tr={}));var cS=tr,cA={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},cT=tG.addEvent,cP=tG.correctFloat,cO=tG.crisp,cC=tG.defined,cE=tG.destroyObjectProperties,cL=tG.fireEvent,cB=tG.merge,cI=tG.pick,cD=tG.removeEvent,cz=function(){function t(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}return t.compose=function(e){cS.compose(e,t)},t.swapXY=function(t,e){return e&&t.forEach(function(t){for(var e,i=t.length,o=0;o<i;o+=2)"number"==typeof(e=t[o+1])&&(t[o+1]=t[o+2],t[o+2]=e)}),t},t.prototype.addEvents=function(){var t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,o=this.track.element,r=this.mouseDownHandler.bind(this),n=this.mouseMoveHandler.bind(this),s=this.mouseUpHandler.bind(this),a=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[o,"click",this.trackClick.bind(this)],[i,"mousedown",r],[i.ownerDocument,"mousemove",n],[i.ownerDocument,"mouseup",s],[i,"touchstart",r],[i.ownerDocument,"touchmove",n],[i.ownerDocument,"touchend",s]];a.forEach(function(t){cT.apply(null,t)}),this._events=a},t.prototype.buttonToMaxClick=function(t){var e=(this.to-this.from)*cI(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),cL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.buttonToMinClick=function(t){var e=cP(this.to-this.from)*cI(this.options.step,.2);this.updatePosition(cP(this.from-e),cP(this.to-e)),cL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.cursorToScrollbarPosition=function(t){var e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}},t.prototype.destroy=function(){var t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,cE(e.scrollbarButtons))},t.prototype.drawScrollbarButton=function(e){var i=this.renderer,o=this.scrollbarButtons,r=this.options,n=this.size,s=i.g().add(this.group);if(o.push(s),r.buttonsEnabled){var a=i.rect().addClass("highcharts-scrollbar-button").add(s);this.chart.styledMode||a.attr({stroke:r.buttonBorderColor,"stroke-width":r.buttonBorderWidth,fill:r.buttonBackgroundColor}),a.attr(a.crisp({x:-.5,y:-.5,width:n,height:n,r:r.buttonBorderRadius},a.strokeWidth()));var h=i.path(t.swapXY([["M",n/2+(e?-1:1),n/2-3],["L",n/2+(e?-1:1),n/2+3],["L",n/2+(e?2:-2),n/2]],r.vertical)).addClass("highcharts-scrollbar-arrow").add(o[e]);this.chart.styledMode||h.attr({fill:r.buttonArrowColor})}},t.prototype.init=function(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=cB(cA,ee.scrollbar,e),this.options.margin=cI(this.options.margin,10),this.chart=i,this.size=cI(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())},t.prototype.mouseDownHandler=function(t){var e,i=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,o=this.cursorToScrollbarPosition(i);this.chartX=o.chartX,this.chartY=o.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0},t.prototype.mouseMoveHandler=function(t){var e,i,o=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,r=this.options.vertical?"chartY":"chartX",n=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][r])&&(i=this.cursorToScrollbarPosition(o)[r]-this[r],this.hasDragged=!0,this.updatePosition(n[0]+i,n[1]+i),this.hasDragged&&cL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))},t.prototype.mouseUpHandler=function(t){this.hasDragged&&cL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null},t.prototype.position=function(t,e,i,o){var r=this.options,n=r.buttonsEnabled,s=r.margin,a=void 0===s?0:s,h=r.vertical,l=this.rendered?"animate":"attr",d=o,c=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=o,this.xOffset=d,this.yOffset=c,h?(this.width=this.yOffset=i=c=this.size,this.xOffset=d=0,this.yOffset=c=n?this.size:0,this.barWidth=o-(n?2*i:0),this.x=t+=a):(this.height=o=this.size,this.xOffset=d=n?this.size:0,this.barWidth=i-(n?2*o:0),this.y=this.y+a),this.group[l]({translateX:t,translateY:this.y}),this.track[l]({width:i,height:o}),this.scrollbarButtons[1][l]({translateX:h?0:i-d,translateY:h?o-c:0})},t.prototype.removeEvents=function(){this._events.forEach(function(t){cD.apply(null,t)}),this._events.length=0},t.prototype.render=function(){var e=this.renderer,i=this.options,o=this.size,r=this.chart.styledMode,n=e.g("scrollbar").attr({zIndex:i.zIndex}).hide().add();this.group=n,this.track=e.rect().addClass("highcharts-scrollbar-track").attr({r:i.trackBorderRadius||0,height:o,width:o}).add(n),r||this.track.attr({fill:i.trackBackgroundColor,stroke:i.trackBorderColor,"stroke-width":i.trackBorderWidth});var s=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-cO(0,s),y:-cO(0,s)}),this.scrollbarGroup=e.g().add(n),this.scrollbar=e.rect().addClass("highcharts-scrollbar-thumb").attr({height:o-s,width:o-s,r:i.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=e.path(t.swapXY([["M",-3,o/4],["L",-3,2*o/3],["M",0,o/4],["L",0,2*o/3],["M",3,o/4],["L",3,2*o/3]],i.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),r||(this.scrollbar.attr({fill:i.barBackgroundColor,stroke:i.barBorderColor,"stroke-width":i.barBorderWidth}),this.scrollbarRifles.attr({stroke:i.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-cO(0,this.scrollbarStrokeWidth),-cO(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)},t.prototype.setRange=function(t,e){var i,o,r=this.options,n=r.vertical,s=r.minWidth,a=this.barWidth,h=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(cC(a)){var l=a*Math.min(e,1);i=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=o=cP(l-i),o<s&&(i=(a-s+o)*t,o=s);var d=Math.floor(i+this.xOffset+this.yOffset),c=o/2-.5;this.from=t,this.to=e,n?(this.scrollbarGroup[h]({translateY:d}),this.scrollbar[h]({height:o}),this.scrollbarRifles[h]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:d}),this.scrollbar[h]({width:o}),this.scrollbarRifles[h]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),o<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===r.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}},t.prototype.shouldUpdateExtremes=function(t){return cI(this.options.liveRedraw,tu.svg&&!tu.isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!cC(t)},t.prototype.trackClick=function(t){var e,i=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,o=this.to-this.from,r=this.y+this.scrollbarTop,n=this.x+this.scrollbarLeft;this.options.vertical&&i.chartY>r||!this.options.vertical&&i.chartX>n?this.updatePosition(this.from+o,this.to+o):this.updatePosition(this.from-o,this.to-o),cL(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.update=function(t){this.destroy(),this.init(this.chart.renderer,cB(!0,this.options,t),this.chart)},t.prototype.updatePosition=function(t,e){e>1&&(t=cP(1-cP(e-t)),e=1),t<0&&(e=cP(e-t),t=0),this.from=t,this.to=e},t.defaultOptions=cA,t}();ee.scrollbar=cB(!0,cz.defaultOptions,ee.scrollbar);var cR=function(){return(cR=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},cN=tu.isTouchDevice,cW=oS.prototype.symbols,cG=tG.addEvent,cX=tG.clamp,cF=tG.correctFloat,cH=tG.defined,cj=tG.destroyObjectProperties,cY=tG.erase,c_=tG.extend,cU=tG.find,cV=tG.fireEvent,cq=tG.isArray,cZ=tG.isNumber,cK=tG.merge,c$=tG.pick,cJ=tG.removeEvent,cQ=tG.splat;function c0(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o=[].filter.call(e,cZ);if(o.length)return Math[t].apply(0,o)}var c1=function(){function t(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}return t.compose=function(e,i,o){d5(e,t),cy(e,i,o)},t.prototype.drawHandle=function(t,e,i,o){var r=this.navigatorOptions.handles.height;this.handles[e][o](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-r)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-r/2-1)})},t.prototype.drawOutline=function(t,e,i,o){var r,n,s=this.navigatorOptions.maskInside,a=this.outline.strokeWidth(),h=a/2,l=a%2/2,d=this.scrollButtonSize,c=this.size,p=this.top,u=this.height,f=p-h,g=p+u,v=this.left;i?(r=p+e+l,e=p+t+l,n=[["M",v+u,p-d-l],["L",v+u,r],["L",v,r],["M",v,e],["L",v+u,e],["L",v+u,p+c+d]],s&&n.push(["M",v+u,r-h],["L",v+u,e+h])):(v-=d,t+=v+d-l,e+=v+d-l,n=[["M",v,f],["L",t,f],["L",t,g],["M",e,g],["L",e,f],["L",v+c+2*d,f]],s&&n.push(["M",t-h,f],["L",e+h,f])),this.outline[o]({d:n})},t.prototype.drawMasks=function(t,e,i,o){var r,n,s,a,h=this.left,l=this.top,d=this.height;i?(s=[h,h,h],a=[l,l+t,l+e],n=[d,d,d],r=[t,e-t,this.size-e]):(s=[h,h+t,h+e],a=[l,l,l],n=[t,e-t,this.size-e],r=[d,d,d]),this.shades.forEach(function(t,e){t[o]({x:s[e],y:a[e],width:n[e],height:r[e]})})},t.prototype.renderElements=function(){var t,e,i=this,o=i.navigatorOptions,r=o.maskInside,n=i.chart,s=n.inverted,a=n.renderer,h={cursor:s?"ns-resize":"ew-resize"},l=null!==(t=i.navigatorGroup)&&void 0!==t?t:i.navigatorGroup=a.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();if([!r,r,!r].forEach(function(t,e){var r,s=null!==(r=i.shades[e])&&void 0!==r?r:i.shades[e]=a.rect().addClass("highcharts-navigator-mask"+(1===e?"-inside":"-outside")).add(l);n.styledMode||(s.attr({fill:t?o.maskFill:"rgba(0,0,0,0)"}),1===e&&s.css(h))}),i.outline||(i.outline=a.path().addClass("highcharts-navigator-outline").add(l)),n.styledMode||i.outline.attr({"stroke-width":o.outlineWidth,stroke:o.outlineColor}),null===(e=o.handles)||void 0===e?void 0:e.enabled){var d=o.handles,c=d.height,p=d.width;[0,1].forEach(function(t){var e,o=d.symbols[t];if(i.handles[t]&&i.handles[t].symbolUrl===o){if(!i.handles[t].isImg&&i.handles[t].symbolName!==o){var r=cW[o].call(cW,-p/2-1,0,p,c);i.handles[t].attr({d:r}),i.handles[t].symbolName=o}}else null===(e=i.handles[t])||void 0===e||e.destroy(),i.handles[t]=a.symbol(o,-p/2-1,0,p,c,d),i.handles[t].attr({zIndex:7-t}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][t]).add(l),i.addMouseEvents();n.inverted&&i.handles[t].attr({rotation:90,rotationOriginX:Math.floor(-p/2),rotationOriginY:(c+p)/2}),n.styledMode||i.handles[t].attr({fill:d.backgroundColor,stroke:d.borderColor,"stroke-width":d.lineWidth,width:d.width,height:d.height,x:-p/2-1,y:0}).css(h)})}},t.prototype.update=function(t,e){var i,o,r,n,s=this;void 0===e&&(e=!1);var a=this.chart,h=a.options.chart.inverted!==(null===(r=a.scrollbar)||void 0===r?void 0:r.options.vertical);if(cK(!0,a.options.navigator,t),this.navigatorOptions=a.options.navigator||{},this.setOpposite(),cH(t.enabled)||h)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(a);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){cJ(t,"updatedData",s.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){t.eventsToUnbind.push(cG(t,"updatedData",s.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=null!==(n=t.height)&&void 0!==n?n:this.height;var l=this.getXAxisOffsets();this.xAxis.update(cR(cR({},t.xAxis),((i={offsets:l})[a.inverted?"width":"height"]=this.height,i[a.inverted?"height":"width"]=void 0,i)),!1),this.yAxis.update(cR(cR({},t.yAxis),((o={})[a.inverted?"width":"height"]=this.height,o)),!1)}e&&a.redraw()},t.prototype.render=function(t,e,i,o){var r,n,s,a,h,l=this.chart,d=this.xAxis,c=d.pointRange||0,p=d.navigatorAxis.fake?l.xAxis[0]:d,u=this.navigatorEnabled,f=this.rendered,g=l.inverted,v=l.xAxis[0].minRange,m=l.xAxis[0].options.maxRange,y=this.scrollButtonSize,x=this.scrollbarHeight;if(!this.hasDragged||cH(i)){if(this.isDirty&&this.renderElements(),t=cF(t-c/2),e=cF(e+c/2),!cZ(t)||!cZ(e)){if(!f)return;i=0,o=c$(d.width,p.width)}this.left=c$(d.left,l.plotLeft+y+(g?l.plotWidth:0));var b=this.size=a=c$(d.len,(g?l.plotHeight:l.plotWidth)-2*y);r=g?x:a+2*y,i=c$(i,d.toPixels(t,!0)),o=c$(o,d.toPixels(e,!0)),cZ(i)&&Math.abs(i)!==1/0||(i=0,o=r);var M=d.toValue(i,!0),k=d.toValue(o,!0),w=Math.abs(cF(k-M));w<v?this.grabbedLeft?i=d.toPixels(k-v-c,!0):this.grabbedRight&&(o=d.toPixels(M+v+c,!0)):cH(m)&&cF(w-c)>m&&(this.grabbedLeft?i=d.toPixels(k-m-c,!0):this.grabbedRight&&(o=d.toPixels(M+m+c,!0))),this.zoomedMax=cX(Math.max(i,o),0,b),this.zoomedMin=cX(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,o),0,b),this.range=this.zoomedMax-this.zoomedMin,b=Math.round(this.zoomedMax);var S=Math.round(this.zoomedMin);u&&(this.navigatorGroup.attr({visibility:"inherit"}),h=f&&!this.hasDragged?"animate":"attr",this.drawMasks(S,b,g,h),this.drawOutline(S,b,g,h),this.navigatorOptions.handles.enabled&&(this.drawHandle(S,0,g,h),this.drawHandle(b,1,g,h))),this.scrollbar&&(g?(s=this.top-y,n=this.left-x+(u||!p.opposite?0:(p.titleOffset||0)+p.axisTitleMargin),x=a+2*y):(s=this.top+(u?this.height:-x),n=this.left-y),this.scrollbar.position(n,s,r,x),this.scrollbar.setRange(this.zoomedMin/(a||1),this.zoomedMax/(a||1))),this.rendered=!0,this.isDirty=!1,cV(this,"afterRender")}},t.prototype.addMouseEvents=function(){var t,e,i=this,o=i.chart,r=o.container,n=[];i.mouseMoveHandler=t=function(t){i.onMouseMove(t)},i.mouseUpHandler=e=function(t){i.onMouseUp(t)},(n=i.getPartsEvents("mousedown")).push(cG(o.renderTo,"mousemove",t),cG(r.ownerDocument,"mouseup",e),cG(o.renderTo,"touchmove",t),cG(r.ownerDocument,"touchend",e)),n.concat(i.getPartsEvents("touchstart")),i.eventsToUnbind=n,i.series&&i.series[0]&&n.push(cG(i.series[0].xAxis,"foundExtremes",function(){o.navigator.modifyNavigatorAxisExtremes()}))},t.prototype.getPartsEvents=function(t){var e=this,i=[];return["shades","handles"].forEach(function(o){e[o].forEach(function(r,n){i.push(cG(r.element,t,function(t){e[o+"Mousedown"](t,n)}))})}),i},t.prototype.shadesMousedown=function(t,e){t=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t;var i,o,r,n,s,a=this.chart,h=this.xAxis,l=this.zoomedMin,d=this.size,c=this.range,p=this.left,u=t.chartX;a.inverted&&(u=t.chartY,p=this.top),1===e?(this.grabbedCenter=u,this.fixedWidth=c,this.dragOffset=u-l):(s=u-p-c/2,0===e?s=Math.max(0,s):2===e&&s+c>=d&&(s=d-c,this.reversedExtremes?(s-=c,r=this.getUnionExtremes().dataMin):o=this.getUnionExtremes().dataMax),s!==l&&(this.fixedWidth=c,cH((n=h.navigatorAxis.toFixedRange(s,s+c,r,o)).min)&&cV(this,"setRange",{min:Math.min(n.min,n.max),max:Math.max(n.min,n.max),redraw:!0,eventArguments:{trigger:"navigator"}})))},t.prototype.handlesMousedown=function(t,e){t=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t;var i,o=this.chart,r=o.xAxis[0],n=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=n?r.min:r.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=n?r.max:r.min),o.setFixedRange(void 0)},t.prototype.onMouseMove=function(t){var e,i,o=this,r=o.chart,n=o.navigatorSize,s=o.range,a=o.dragOffset,h=r.inverted,l=o.left;(!t.touches||0!==t.touches[0].pageX)&&(i=(t=(null===(e=r.pointer)||void 0===e?void 0:e.normalize(t))||t).chartX,h&&(l=o.top,i=t.chartY),o.grabbedLeft?(o.hasDragged=!0,o.render(0,0,i-l,o.otherHandlePos)):o.grabbedRight?(o.hasDragged=!0,o.render(0,0,o.otherHandlePos,i-l)):o.grabbedCenter&&(o.hasDragged=!0,i<a?i=a:i>n+a-s&&(i=n+a-s),o.render(0,0,i-a,i-a+s)),o.hasDragged&&o.scrollbar&&c$(o.scrollbar.options.liveRedraw,!cN&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){o.onMouseUp(t)},0)))},t.prototype.onMouseUp=function(t){var e,i,o,r,n,s,a=this.chart,h=this.xAxis,l=this.scrollbar,d=t.DOMEvent||t,c=a.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!l||!l.hasDragged)||"scrollbar"===t.trigger)&&(o=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?r=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(n=this.fixedExtreme),this.zoomedMax===this.size&&(n=this.reversedExtremes?o.dataMin:o.dataMax),0===this.zoomedMin&&(r=this.reversedExtremes?o.dataMax:o.dataMin),cH((s=h.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,r,n)).min)&&cV(this,"setRange",{min:Math.min(s.min,s.max),max:Math.max(s.min,s.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&cZ(this.zoomedMin)&&cZ(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,c,p),this.outline&&this.drawOutline(i,e,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,c,p),this.drawHandle(e,1,c,p)))},t.prototype.removeEvents=function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()},t.prototype.removeBaseSeriesEvents=function(){var t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){cJ(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&cJ(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},t.prototype.getXAxisOffsets=function(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]},t.prototype.init=function(t){var e,i,o=t.options,r=o.navigator||{},n=r.enabled,s=o.scrollbar||{},a=s.enabled,h=n&&r.height||0,l=a&&s.height||0,d=s.buttonsEnabled&&l||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=h,this.scrollbarHeight=l,this.scrollButtonSize=d,this.scrollbarEnabled=a,this.navigatorEnabled=n,this.navigatorOptions=r,this.scrollbarOptions=s,this.setOpposite();var c=this,p=c.baseSeries,u=t.xAxis.length,f=t.yAxis.length,g=p&&p[0]&&p[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,c.navigatorEnabled){var v=this.getXAxisOffsets();c.xAxis=new rA(t,cK({breaks:g.options.breaks,ordinal:g.options.ordinal,overscroll:g.options.overscroll},r.xAxis,{type:"datetime",yAxis:null===(e=r.yAxis)||void 0===e?void 0:e.id,index:u,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:g.options.ordinal?0:g.options.minPadding,maxPadding:g.options.ordinal?0:g.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:v,width:h}:{offsets:v,height:h}),"xAxis"),c.yAxis=new rA(t,cK(r.yAxis,{alignTicks:!1,offset:0,index:f,isInternal:!0,reversed:c$(r.yAxis&&r.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:h}:{height:h}),"yAxis"),p||r.series.data?c.updateNavigatorSeries(!1):0===t.series.length&&(c.unbindRedraw=cG(t,"beforeRedraw",function(){t.series.length>0&&!c.series&&(c.setBaseSeries(),c.unbindRedraw())})),c.reversedExtremes=t.inverted&&!c.xAxis.reversed||!t.inverted&&c.xAxis.reversed,c.renderElements(),c.addMouseEvents()}else c.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){var o=t.xAxis[0],r=o.getExtremes(),n=o.len-2*d,s=c0("min",o.options.min,r.dataMin),a=c0("max",o.options.max,r.dataMax)-s;return i?e*a/n+s:n*(e-s)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},c.xAxis.navigatorAxis.axis=c.xAxis,c.xAxis.navigatorAxis.toFixedRange=co.prototype.toFixedRange.bind(c.xAxis.navigatorAxis);if(null===(i=t.options.scrollbar)||void 0===i?void 0:i.enabled){var m=cK(t.options.scrollbar,{vertical:t.inverted});cZ(m.margin)||(m.margin=t.inverted?-3:3),t.scrollbar=c.scrollbar=new cz(t.renderer,m,t),cG(c.scrollbar,"changed",function(t){var e=c.size,i=e*this.to,o=e*this.from;c.hasDragged=c.scrollbar.hasDragged,c.render(0,0,o,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){c.onMouseUp(t)})})}c.addBaseSeriesEvents(),c.addChartEvents()},t.prototype.setOpposite=function(){var t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=c$(t.opposite,!!(!e&&i.inverted))},t.prototype.getUnionExtremes=function(t){var e,i=this.chart.xAxis[0],o=this.chart.time,r=this.xAxis,n=r.options,s=i.options;return t&&null===i.dataMin||(e={dataMin:c$(o.parse(null==n?void 0:n.min),c0("min",o.parse(s.min),i.dataMin,r.dataMin,r.min)),dataMax:c$(o.parse(null==n?void 0:n.max),c0("max",o.parse(s.max),i.dataMax,r.dataMax,r.max))}),e},t.prototype.setBaseSeries=function(t,e){var i=this.chart,o=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?cU(i.series,function(t){return!t.options.isInternal}).index:0),(i.series||[]).forEach(function(e,i){!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&o.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)},t.prototype.updateNavigatorSeries=function(t,e){var i,o,r,n,s,a=this,h=a.chart,l=a.baseSeries,d={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:null===(i=this.navigatorOptions.xAxis)||void 0===i?void 0:i.id,yAxis:null===(o=this.navigatorOptions.yAxis)||void 0===o?void 0:o.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},c=a.series=(a.series||[]).filter(function(t){var e=t.baseSeries;return!(0>l.indexOf(e))||(e&&(cJ(e,"updatedData",a.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),p=a.navigatorOptions.series;l&&l.length&&l.forEach(function(t){var i,o=t.navigatorSeries,u=c_({color:t.color,visible:t.visible},cq(p)?ee.navigator.series:p);if(!o||!1!==a.navigatorOptions.adaptToUpdatedData){d.name="Navigator "+l.length,s=(r=t.options||{}).navigatorOptions||{},u.dataLabels=cQ(u.dataLabels),(n=cK(r,d,u,s)).pointRange=c$(u.pointRange,s.pointRange,ee.plotOptions[n.type||"line"].pointRange);var f=s.data||u.data;a.hasNavigatorData=a.hasNavigatorData||!!f,n.data=f||(null===(i=r.data)||void 0===i?void 0:i.slice(0)),o&&o.options?o.update(n,e):(t.navigatorSeries=h.initSeries(n),h.setSortedData(),t.navigatorSeries.baseSeries=t,c.push(t.navigatorSeries))}}),(p.data&&!(l&&l.length)||cq(p))&&(a.hasNavigatorData=!1,(p=cQ(p)).forEach(function(t,e){d.name="Navigator "+(c.length+1),(n=cK(ee.navigator.series,{color:h.series[e]&&!h.series[e].options.isInternal&&h.series[e].color||h.options.colors[e]||h.options.colors[0]},d,t)).data=t.data,n.data&&(a.hasNavigatorData=!0,c.push(h.initSeries(n)))})),t&&this.addBaseSeriesEvents()},t.prototype.addBaseSeriesEvents=function(){var t=this,e=this,i=e.baseSeries||[];i[0]&&i[0].xAxis&&i[0].eventsToUnbind.push(cG(i[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),i.forEach(function(o){o.eventsToUnbind.push(cG(o,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),o.eventsToUnbind.push(cG(o,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==t.navigatorOptions.adaptToUpdatedData&&o.xAxis&&o.eventsToUnbind.push(cG(o,"updatedData",t.updatedDataHandler)),o.eventsToUnbind.push(cG(o,"remove",function(){i&&cY(i,o),this.navigatorSeries&&e.series&&(cY(e.series,this.navigatorSeries),cH(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})},t.prototype.getBaseSeriesMin=function(t){return this.baseSeries.reduce(function(t,e){var i;return Math.min(t,null!==(i=e.getColumn("x")[0])&&void 0!==i?i:t)},t)},t.prototype.modifyNavigatorAxisExtremes=function(){var t=this.xAxis;if(void 0!==t.getExtremes){var e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}},t.prototype.modifyBaseAxisExtremes=function(){var t,e,i,o=this.chart.navigator,r=this.getExtremes(),n=r.min,s=r.max,a=r.dataMin,h=r.dataMax,l=s-n,d=o.stickToMin,c=o.stickToMax,p=c$(null===(t=this.ordinal)||void 0===t?void 0:t.convertOverscroll(this.options.overscroll),0),u=o.series&&o.series[0],f=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(d&&(e=(i=a)+l),c&&(e=h+p,d||(i=Math.max(a,e-l,o.getBaseSeriesMin(u&&u.xData?u.xData[0]:-Number.MAX_VALUE)))),f&&(d||c)&&cZ(i)&&(this.min=this.userMin=i,this.max=this.userMax=e)),o.stickToMin=o.stickToMax=null},t.prototype.updatedDataHandler=function(){var t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=c$(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))},t.prototype.shouldStickToMin=function(t,e){var i=e.getBaseSeriesMin(t.getColumn("x")[0]),o=t.xAxis,r=o.max,n=o.min,s=o.options.range,a=!0;return!!(cZ(r)&&cZ(n))&&(s&&r-i>0?r-i<s:n<=i)},t.prototype.addChartEvents=function(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(cG(this.chart,"redraw",function(){var t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),cG(this.chart,"getMargins",function(){var t,e=this.navigator,i=e.opposite?"plotTop":"marginBottom";this.inverted&&(i=e.opposite?"marginRight":"plotLeft"),this[i]=(this[i]||0)+(e.navigatorEnabled||!this.inverted?e.height+((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)+e.scrollbarHeight:0)+(e.navigatorOptions.margin||0)}),cG(t,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))},t.prototype.destroy=function(){var t=this;this.removeEvents(),this.xAxis&&(cY(this.chart.xAxis,this.xAxis),cY(this.chart.axes,this.xAxis)),this.yAxis&&(cY(this.chart.yAxis,this.yAxis),cY(this.chart.axes,this.yAxis)),(this.series||[]).forEach(function(t){t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(function(e){t[e]&&t[e].destroy&&t[e].destroy(),t[e]=null}),[this.handles].forEach(function(t){cj(t)}),this.baseSeries.forEach(function(t){t.navigatorSeries=void 0}),this.navigatorEnabled=!1},t}(),c2={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},c3=tu.composed,c5=tG.addEvent,c6=tG.defined,c9=tG.extend,c4=tG.isNumber,c8=tG.merge,c7=tG.pick,pt=tG.pushUnique,pe=[];function pi(){var t,e,i=this.range,o=i.type,r=this.max,n=this.chart.time,s=function(t,e){var i=n.toParts(t),r=i.slice();"year"===o?r[0]+=e:r[1]+=e;var s=n.makeTime.apply(n,r),a=n.toParts(s);return"month"===o&&i[1]===a[1]&&1===Math.abs(e)&&(r[0]=i[0],r[1]=i[1],r[2]=0),(s=n.makeTime.apply(n,r))-t};c4(i)?(t=r-i,e=i):i&&(t=r+s(r,-(i.count||1)),this.chart&&this.chart.setFixedRange(r-t));var a=c7(this.dataMin,Number.MIN_VALUE);return c4(t)||(t=a),t<=a&&(t=a,void 0===e&&(e=s(t,i.count)),this.newMax=Math.min(t+e,c7(this.dataMax,Number.MAX_VALUE))),c4(r)?!c4(i)&&i&&i._offsetMin&&(t+=i._offsetMin):t=void 0,t}function po(){var t;null===(t=this.rangeSelector)||void 0===t||t.redrawElements()}function pr(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new tn(this))}function pn(){var t=this.rangeSelector;if(t){c4(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);var e=t.options.verticalAlign;t.options.floating||("bottom"===e?this.extraBottomMargin=!0:"top"!==e||(this.extraTopMargin=!0))}}function ps(){var t,e=this.rangeSelector;if(e){var i=this.xAxis[0].getExtremes(),o=this.legend,r=e&&e.options.verticalAlign;c4(i.min)&&e.render(i.min,i.max),o.display&&"top"===r&&r===o.options.verticalAlign&&(t=c8(this.spacingBox),"vertical"===o.options.layout?t.y=this.plotTop:t.y+=e.getHeight(),o.group.placed=!1,o.align(t))}}function pa(){for(var t=0,e=pe.length;t<e;++t){var i=pe[t];if(i[0]===this){i[1].forEach(function(t){return t()}),pe.splice(t,1);return}}}function ph(){var t,e=this.rangeSelector;if(null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.enabled){var i=e.getHeight(),o=e.options.verticalAlign;e.options.floating||("bottom"===o?this.marginBottom+=i:"middle"===o||(this.plotTop+=i))}}function pl(t){var e=t.options.rangeSelector,i=this.extraBottomMargin,o=this.extraTopMargin,r=this.rangeSelector;if(e&&e.enabled&&!c6(r)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=r=new tn(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,r){var n=e&&e.verticalAlign||r.options&&r.options.verticalAlign;r.options.floating||("bottom"===n?this.extraBottomMargin=!0:"middle"===n||(this.extraTopMargin=!0)),(this.extraBottomMargin!==i||this.extraTopMargin!==o)&&(this.isDirtyBox=!0)}}var pd=function(t,e,i){if(tn=i,pt(c3,"RangeSelector")){var o=e.prototype;t.prototype.minFromRange=pi,c5(e,"afterGetContainer",pr),c5(e,"beforeRender",pn),c5(e,"destroy",pa),c5(e,"getMargins",ph),c5(e,"redraw",ps),c5(e,"update",pl),c5(e,"beforeRedraw",po),o.callbacks.push(ps),c9(ee,{rangeSelector:c2.rangeSelector}),c9(ee.lang,c2.lang)}},pc=function(){return(pc=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},pp=tG.addEvent,pu=tG.correctFloat,pf=tG.css,pg=tG.defined,pv=tG.error,pm=tG.isNumber,py=tG.pick,px=tG.timeUnits,pb=tG.isString;!function(t){function e(t,e,i,o,r,n,s){void 0===r&&(r=[]),void 0===n&&(n=0);var a,h,l,d,c,p={},u=this.options.tickPixelInterval,f=this.chart.time,g=[],v=0,m=[],y=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!r||r.length<3||void 0===e)return f.getTimeTicks.apply(f,arguments);var x=r.length;for(a=0;a<x;a++){if(c=a&&r[a-1]>i,r[a]<e&&(v=a),a===x-1||r[a+1]-r[a]>5*n||c){if(r[a]>y){for(h=f.getTimeTicks(t,r[v],r[a],o);h.length&&h[0]<=y;)h.shift();h.length&&(y=h[h.length-1]),g.push(m.length),m=m.concat(h)}v=a+1}if(c)break}if(h){if(d=h.info,s&&d.unitRange<=px.hour){for(v=1,a=m.length-1;v<a;v++)f.dateFormat("%d",m[v])!==f.dateFormat("%d",m[v-1])&&(p[m[v]]="day",l=!0);l&&(p[m[0]]="day"),d.higherRanks=p}d.segmentStarts=g,m.info=d}else pv(12,!1,this.chart);if(s&&pg(u)){for(var b=m.length,M=[],k=[],w=void 0,S=void 0,A=void 0,T=void 0,P=void 0,O=b;O--;)S=this.translate(m[O]),A&&(k[O]=A-S),M[O]=A=S;for(k.sort(function(t,e){return t-e}),(T=k[Math.floor(k.length/2)])<.6*u&&(T=null),O=m[b-1]>i?b-1:b,A=void 0;O--;)P=Math.abs(A-(S=M[O])),A&&P<.8*u&&(null===T||P<.8*T)?(p[m[O]]&&!p[m[O+1]]?(w=O+1,A=S):w=O,m.splice(w,1)):A=S}return m}function i(t){var e=this.ordinal.positions;if(!e)return t;var i,o=e.length-1;return(t<0?t=e[0]:t>o?t=e[o]:(o=Math.floor(t),i=t-o),void 0!==i&&void 0!==e[o])?e[o]+(i?i*(e[o+1]-e[o]):0):t}function o(t){var e=this.ordinal,i=this.old?this.old.min:this.min,o=this.old?this.old.transA:this.transA,r=e.getExtendedPositions();if(null==r?void 0:r.length){var n=pu((t-i)*o+this.minPixelPadding),s=pu(e.getIndexOfPoint(n,r)),a=pu(s%1);if(s>=0&&s<=r.length-1){var h=r[Math.floor(s)],l=r[Math.ceil(s)];return r[Math.floor(s)]+a*(l-h)}}return t}function r(e,i){var o=t.Additions.findIndexOf(e,i,!0);if(e[o]===i)return o;var r=(i-e[o])/(e[o+1]-e[o]);return o+r}function n(){this.ordinal||(this.ordinal=new t.Additions(this))}function s(){var t=this.eventArgs,e=this.options;if(this.isXAxis&&pg(e.overscroll)&&0!==e.overscroll&&pm(this.max)&&pm(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&((null==t?void 0:t.trigger)!=="pan"||this.isInternal)&&(null==t?void 0:t.trigger)!=="navigator")){var i=this.ordinal.convertOverscroll(e.overscroll);this.max+=i,!this.isInternal&&pg(this.userMin)&&(null==t?void 0:t.trigger)!=="mousewheel"&&(this.min+=i)}}function a(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function h(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function l(t){var e,i=this.xAxis[0],o=i.ordinal.convertOverscroll(i.options.overscroll),r=t.originalEvent.chartX,n=this.options.chart.panning,s=!1;if(n&&"y"!==n.type&&i.options.ordinal&&i.series.length&&(!t.touches||t.touches.length<=1)){var a=this.mouseDownX,h=i.getExtremes(),l=h.dataMin,d=h.dataMax,c=h.min,p=h.max,u=this.hoverPoints,f=i.closestPointRange||(null===(e=i.ordinal)||void 0===e?void 0:e.overscrollPointsRange),g=Math.round((a-r)/(i.translationSlope*(i.ordinal.slope||f))),v=i.ordinal.getExtendedPositions(),m={ordinal:{positions:v,extendedOrdinalPositions:v}},y=i.index2val,x=i.val2lin,b=void 0,M=void 0;if(c<=l&&g<0||p+o>=d&&g>0)return;m.ordinal.positions?Math.abs(g)>1&&(u&&u.forEach(function(t){t.setState()}),d>(M=m.ordinal.positions)[M.length-1]&&M.push(d),this.setFixedRange(p-c),(b=i.navigatorAxis.toFixedRange(void 0,void 0,y.apply(m,[x.apply(m,[c,!0])+g]),y.apply(m,[x.apply(m,[p,!0])+g]))).min>=Math.min(M[0],c)&&b.max<=Math.max(M[M.length-1],p)+o&&i.setExtremes(b.min,b.max,!0,!1,{trigger:"pan"}),this.mouseDownX=r,pf(this.container,{cursor:"move"})):s=!0}else s=!0;s||n&&/y/.test(n.type)?o&&(i.max=i.dataMax+o):t.preventDefault()}function d(){var t=this.xAxis;(null==t?void 0:t.options.ordinal)&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function c(t,e){var i,o,n,s=this.ordinal,a=s.positions,h=s.slope;if(!a)return t;var l=a.length;if(a[0]<=t&&a[l-1]>=t)i=r(a,t);else{if(!(null==(n=null===(o=s.getExtendedPositions)||void 0===o?void 0:o.call(s))?void 0:n.length))return t;var d=n.length;h||(h=(n[d-1]-n[0])/d);var c=r(n,a[0]);if(t>=n[0]&&t<=n[d-1])i=r(n,t)-c;else{if(!e)return t;if(t<n[0]){var p=n[0]-t,u=p/h;i=-c-u}else{var p=t-n[d-1],u=p/h;i=u+d-c}}}return e?i:h*(i||0)+s.offset}t.compose=function(t,r,p){var u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=e,u.index2val=i,u.lin2val=o,u.val2lin=c,u.ordinal2lin=u.val2lin,pp(t,"afterInit",n),pp(t,"foundExtremes",s),pp(t,"afterSetScale",a),pp(t,"initialAxisTranslation",h),pp(p,"pan",l),pp(p,"touchpan",l),pp(r,"updatedData",d)),t},t.Additions=function(){function t(t){this.index={},this.axis=t}return t.prototype.beforeSetTickPositions=function(){var t,e,i,o,r,n,s,a,h=this.axis,l=h.ordinal,d=h.getExtremes(),c=d.min,p=d.max,u=null===(t=h.brokenAxis)||void 0===t?void 0:t.hasBreaks,f=h.options.ordinal,g=[],v=Number.MAX_VALUE,m=!1,y=!1,x=!1;if(f||u){var b=0;if(h.series.forEach(function(t,o){var r=t.getColumn("x",!0);if(i=[],o>0&&"highcharts-navigator-series"!==t.options.id&&r.length>1&&(y=b!==r[1]-r[0]),b=r[1]-r[0],t.boosted&&(x=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||u)&&(e=(g=g.concat(r)).length,g.sort(function(t,e){return t-e}),v=Math.min(v,py(t.closestPointRange,v)),e)){for(o=0;o<e-1;)g[o]!==g[o+1]&&i.push(g[o+1]),o++;i[0]!==g[0]&&i.unshift(g[0]),g=i}}),h.ordinal.originalOrdinalRange||(h.ordinal.originalOrdinalRange=(g.length-1)*v),y&&x&&(g.pop(),g.shift()),(e=g.length)>2){for(o=g[1]-g[0],a=e-1;a--&&!m;)g[a+1]-g[a]!==o&&(m=!0);!h.options.keepOrdinalPadding&&(g[0]-c>o||p-g[g.length-1]>o)&&(m=!0)}else h.options.overscroll&&(2===e?v=g[1]-g[0]:1===e?(v=h.ordinal.convertOverscroll(h.options.overscroll),g=[g[0],g[0]+v]):v=l.overscrollPointsRange);m||h.forceOrdinal?(h.options.overscroll&&(l.overscrollPointsRange=v,g=g.concat(l.getOverscrollPositions())),l.positions=g,r=h.ordinal2lin(Math.max(c,g[0]),!0),n=Math.max(h.ordinal2lin(Math.min(p,g[g.length-1]),!0),1),l.slope=s=(p-c)/(n-r),l.offset=c-r*s):(l.overscrollPointsRange=py(h.closestPointRange,l.overscrollPointsRange),l.positions=h.ordinal.slope=l.offset=void 0)}h.isOrdinal=f&&m,l.groupIntervalFactor=null},t.findIndexOf=function(t,e,i){for(var o,r=0,n=t.length-1;r<n;)t[o=Math.ceil((r+n)/2)]<=e?r=o:n=o-1;return t[r]===e?r:i?r:-1},t.prototype.getExtendedPositions=function(t){void 0===t&&(t=!0);var e,i=this,o=i.axis,r=o.constructor.prototype,n=o.chart,s=o.series.reduce(function(t,e){var i=e.currentDataGrouping;return t+(i?i.count+i.unitName:"raw")},""),a=t?o.ordinal.convertOverscroll(o.options.overscroll):0,h=o.getExtremes(),l=void 0,d=i.index;return d||(d=i.index={}),!d[s]&&((e={series:[],chart:n,forceOrdinal:!1,getExtremes:function(){return{min:h.dataMin,max:h.dataMax+a}},applyGrouping:r.applyGrouping,getGroupPixelWidth:r.getGroupPixelWidth,getTimeTicks:r.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:r.ordinal2lin,getIndexOfPoint:r.getIndexOfPoint,val2lin:r.val2lin}).ordinal.axis=e,o.series.forEach(function(o){l={xAxis:e,chart:n,groupPixelWidth:o.groupPixelWidth,destroyGroupedData:tu.noop,getColumn:o.getColumn,applyGrouping:o.applyGrouping,getProcessedData:o.getProcessedData,reserveSpace:o.reserveSpace,visible:o.visible};var r,s,a,h=o.getColumn("x").concat(t?i.getOverscrollPositions():[]);l.dataTable=new n1({columns:{x:h}}),l.options=pc(pc({},o.options),{dataGrouping:o.currentDataGrouping?{firstAnchor:null===(r=o.options.dataGrouping)||void 0===r?void 0:r.firstAnchor,anchor:null===(s=o.options.dataGrouping)||void 0===s?void 0:s.anchor,lastAnchor:null===(a=o.options.dataGrouping)||void 0===a?void 0:a.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[o.currentDataGrouping.unitName,[o.currentDataGrouping.count]]]}:{enabled:!1}}),e.series.push(l),o.processData.apply(l)}),e.applyGrouping({hasExtremesChanged:!0}),(null==l?void 0:l.closestPointRange)!==(null==l?void 0:l.basePointRange)&&l.currentDataGrouping&&(e.forceOrdinal=!0),o.ordinal.beforeSetTickPositions.apply({axis:e}),!o.ordinal.originalOrdinalRange&&e.ordinal.originalOrdinalRange&&(o.ordinal.originalOrdinalRange=e.ordinal.originalOrdinalRange),e.ordinal.positions&&(d[s]=e.ordinal.positions)),d[s]},t.prototype.getGroupIntervalFactor=function(t,e,i){var o,r,n=i.getColumn("x",!0),s=n.length,a=[],h=this.groupIntervalFactor;if(!h){for(r=0;r<s-1;r++)a[r]=n[r+1]-n[r];a.sort(function(t,e){return t-e}),o=a[Math.floor(s/2)],t=Math.max(t,n[0]),e=Math.min(e,n[s-1]),this.groupIntervalFactor=h=s*o/(e-t)}return h},t.prototype.getIndexOfPoint=function(t,e){var i=this.axis,o=i.min,n=i.minPixelPadding;return r(e,o)+pu((t-n)/(i.translationSlope*(this.slope||i.closestPointRange||this.overscrollPointsRange)))},t.prototype.getOverscrollPositions=function(){var t=this.axis,e=this.convertOverscroll(t.options.overscroll),i=this.overscrollPointsRange,o=[],r=t.dataMax;if(pg(i))for(;r<t.dataMax+e;)o.push(r+=i);return o},t.prototype.postProcessTickInterval=function(t){var e,i=this.axis,o=this.slope,r=i.closestPointRange;return o&&r?i.options.breaks?r||t:t/(o/r):t},t.prototype.convertOverscroll=function(t){void 0===t&&(t=0);var e=this,i=e.axis,o=function(t){return py(e.originalOrdinalRange,pg(i.dataMax)&&pg(i.dataMin)?i.dataMax-i.dataMin:0)*t};if(pb(t)){var r=parseInt(t,10),n=void 0;if(pg(i.min)&&pg(i.max)&&pg(i.dataMin)&&pg(i.dataMax)&&!(n=i.max-i.min==i.dataMax-i.dataMin)&&(this.originalOrdinalRange=i.max-i.min),/%$/.test(t))return o(r/100);if(/px/.test(t)){var s=Math.min(r,.9*i.len)/i.len;return o(s/(n?1-s:1))}return 0}return t},t}()}(ts||(ts={}));var pM=ts,pk=e5.format,pw=tG.addEvent,pS=tG.createElement,pA=tG.css,pT=tG.defined,pP=tG.destroyObjectProperties,pO=tG.diffObjects,pC=tG.discardElement,pE=tG.extend,pL=tG.fireEvent,pB=tG.isNumber,pI=tG.isString,pD=tG.merge,pz=tG.objectEach,pR=tG.pick,pN=tG.splat;function pW(t){var e=function(e){return new RegExp("%[[a-zA-Z]*".concat(e)).test(t)};if(pI(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";var i=pI(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(e):t.dateStyle||t.day||t.month||t.year,o=pI(t)?["H","k","I","l","M","S"].some(e):t.timeStyle||t.hour||t.minute||t.second;return i&&o?"datetime-local":i?"date":o?"time":"text"}var pG=function(){function t(t){var e=this;this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=function(){var t=0;return e.buttons.forEach(function(e){var i=e.getBBox();i.width>t&&(t=i.width)}),t},this.init(t)}return t.compose=function(e,i){pd(e,i,t)},t.prototype.clickButton=function(t,e){var i,o,r,n,s,a=this.chart,h=this.buttonOptions[t],l=a.xAxis[0],d=a.scroller&&a.scroller.getUnionExtremes()||l||{},c=h.type,p=h.dataGrouping,u=d.dataMin,f=d.dataMax,g=pB(null==l?void 0:l.max)?Math.round(Math.min(l.max,null!=f?f:l.max)):void 0,v=h._range,m=!0;if(null!==u&&null!==f){if(this.setSelected(t),p&&(this.forcedDataGrouping=!0,rA.prototype.setDataGrouping.call(l||{chart:this.chart},p,!1),this.frozenStates=h.preserveDataGrouping),"month"===c||"year"===c)l?(n={range:h,max:g,chart:a,dataMin:u,dataMax:f},i=l.minFromRange.call(n),pB(n.newMax)&&(g=n.newMax),m=!1):v=h;else if(v)pB(g)&&(g=Math.min((i=Math.max(g-v,u))+v,f),m=!1);else if("ytd"===c){if(l)!l.hasData()||pB(f)&&pB(u)||(u=Number.MAX_VALUE,f=-Number.MAX_VALUE,a.series.forEach(function(t){var e=t.getColumn("x");e.length&&(u=Math.min(e[0],u),f=Math.max(e[e.length-1],f))}),e=!1),pB(f)&&pB(u)&&(i=r=(s=this.getYTDExtremes(f,u)).min,g=s.max);else{this.deferredYTDClick=t;return}}else"all"===c&&l&&(a.navigator&&a.navigator.baseSeries[0]&&(a.navigator.baseSeries[0].xAxis.options.range=void 0),i=u,g=f);if(m&&h._offsetMin&&pT(i)&&(i+=h._offsetMin),h._offsetMax&&pT(g)&&(g+=h._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),l)pB(i)&&pB(g)&&(l.setExtremes(i,g,pR(e,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:h}),a.setFixedRange(h._range));else{o=pN(a.options.xAxis||{})[0];var y=pw(a,"afterCreateAxes",function(){var t=a.xAxis[0];t.range=t.options.range=v,t.min=t.options.min=r});pw(a,"load",function(){var t=a.xAxis[0];a.setFixedRange(h._range),t.options.range=o.range,t.options.min=o.min,y()})}pL(this,"afterBtnClick")}},t.prototype.setSelected=function(t){this.selected=this.options.selected=t},t.prototype.init=function(t){var e=this,i=t.options.rangeSelector,o=t.options.lang,r=i.buttons,n=i.selected,s=function(){var t=e.minInput,i=e.maxInput;t&&t.blur&&pL(t,"blur"),i&&i.blur&&pL(i,"blur")};e.chart=t,e.options=i,e.buttons=[],e.buttonOptions=r.map(function(t){var e,i;return t.type&&o.rangeSelector&&(null!==(e=t.text)&&void 0!==e||(t.text=o.rangeSelector[""+t.type+"Text"]),null!==(i=t.title)&&void 0!==i||(t.title=o.rangeSelector[""+t.type+"Title"])),t.text=pk(t.text,{count:t.count||1}),t.title=pk(t.title,{count:t.count||1}),t}),this.eventsToUnbind=[],this.eventsToUnbind.push(pw(t.container,"mousedown",s)),this.eventsToUnbind.push(pw(t,"resize",s)),r.forEach(e.computeButtonRange),void 0!==n&&r[n]&&this.clickButton(n,!1),this.eventsToUnbind.push(pw(t,"load",function(){t.xAxis&&t.xAxis[0]&&pw(t.xAxis[0],"setExtremes",function(i){pB(this.max)&&pB(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()},t.prototype.updateButtonStates=function(){var t=this,e=this.chart,i=this.dropdown,o=this.dropdownLabel,r=e.xAxis[0],n=Math.round(r.max-r.min),s=!r.hasVisibleSeries,a=24*36e5,h=e.scroller&&e.scroller.getUnionExtremes()||r,l=h.dataMin,d=h.dataMax,c=t.getYTDExtremes(d,l),p=c.min,u=c.max,f=t.selected,g=t.options.allButtonsEnabled,v=Array(t.buttonOptions.length).fill(0),m=pB(f),y=t.buttons,x=!1,b=null;t.buttonOptions.forEach(function(e,i){var o,h=e._range,c=e.type,y=e.count||1,M=e._offsetMax-e._offsetMin,k=i===f,w=h>d-l,S=h<r.minRange,A=!1,T=h===n;if(k&&w&&(x=!0),r.isOrdinal&&(null===(o=r.ordinal)||void 0===o?void 0:o.positions)&&h&&n<h){var P=r.ordinal.positions,O=pM.Additions.findIndexOf(P,r.min,!0),C=Math.min(pM.Additions.findIndexOf(P,r.max,!0)+1,P.length-1);P[C]-P[O]>h&&(T=!0)}else("month"===c||"year"===c)&&n+36e5>=({month:28,year:365})[c]*a*y-M&&n-36e5<=({month:31,year:366})[c]*a*y+M?T=!0:"ytd"===c?(T=u-p+M===n,A=!k):"all"===c&&(T=r.max-r.min>=d-l);var E=!g&&!(x&&"all"===c)&&(w||S||s),L=x&&"all"===c||!A&&T||k&&t.frozenStates;E?v[i]=3:L&&(!m||i===f)&&(b=i)}),null!==b?(v[b]=2,t.setSelected(b),this.dropdown&&(this.dropdown.selectedIndex=b+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),o&&(o.setState(0),o.attr({text:(ee.lang.rangeSelectorZoom||"")+" ▾"})));for(var M=0;M<v.length;M++){var k=v[M],w=y[M];if(w.state!==k&&(w.setState(k),i)){i.options[M+1].disabled=3===k,2===k&&(o&&(o.setState(2),o.attr({text:t.buttonOptions[M].text+" ▾"})),i.selectedIndex=M+1);var S=o.getBBox();pA(i,{width:""+S.width+"px",height:""+S.height+"px"})}}},t.prototype.computeButtonRange=function(t){var e=t.type,i=t.count||1,o={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};o[e]?t._range=o[e]*i:("month"===e||"year"===e)&&(t._range=24*({month:30,year:365})[e]*36e5*i),t._offsetMin=pR(t.offsetMin,0),t._offsetMax=pR(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin},t.prototype.getInputValue=function(t){var e="min"===t?this.minInput:this.maxInput,i=this.chart.options.rangeSelector,o=this.chart.time;return e?("text"===e.type&&i.inputDateParser||this.defaultInputDateParser)(e.value,"UTC"===o.timezone,o):0},t.prototype.setInputValue=function(t,e){var i=this.options,o=this.chart.time,r="min"===t?this.minInput:this.maxInput,n="min"===t?this.minDateBox:this.maxDateBox;if(r){r.setAttribute("type",pW(i.inputDateFormat||"%e %b %Y"));var s=r.getAttribute("data-hc-time"),a=pT(s)?Number(s):void 0;if(pT(e)){var h=a;pT(h)&&r.setAttribute("data-hc-time-previous",h),r.setAttribute("data-hc-time",e),a=e}r.value=o.dateFormat(this.inputTypeFormats[r.type]||i.inputEditDateFormat,a),n&&n.attr({text:o.dateFormat(i.inputDateFormat,a)})}},t.prototype.setInputExtremes=function(t,e,i){var o="min"===t?this.minInput:this.maxInput;if(o){var r=this.inputTypeFormats[o.type],n=this.chart.time;if(r){var s=n.dateFormat(r,e);o.min!==s&&(o.min=s);var a=n.dateFormat(r,i);o.max!==a&&(o.max=a)}}},t.prototype.showInput=function(t){var e="min"===t?this.minDateBox:this.maxDateBox,i="min"===t?this.minInput:this.maxInput;if(i&&e&&this.inputGroup){var o="text"===i.type,r=this.inputGroup,n=r.translateX,s=void 0===n?0:n,a=r.translateY,h=void 0===a?0:a,l=e.x,d=void 0===l?0:l,c=e.width,p=void 0===c?0:c,u=e.height,f=void 0===u?0:u,g=this.options.inputBoxWidth;pA(i,{width:o?p+(g?-2:20)+"px":"auto",height:f-2+"px",border:"2px solid silver"}),o&&g?pA(i,{left:s+d+"px",top:h+"px"}):pA(i,{left:Math.min(Math.round(d+s-(i.offsetWidth-p)/2),this.chart.chartWidth-i.offsetWidth)+"px",top:h-(i.offsetHeight-f)/2+"px"})}},t.prototype.hideInput=function(t){var e="min"===t?this.minInput:this.maxInput;e&&pA(e,{top:"-9999em",border:0,width:"1px",height:"1px"})},t.prototype.defaultInputDateParser=function(t,e,i){return(null==i?void 0:i.parse(t))||0},t.prototype.drawInput=function(t){var e=this.chart,i=this.div,o=this.inputGroup,r=this,n=e.renderer.style||{},s=e.renderer,a=e.options.rangeSelector,h=ee.lang,l="min"===t;function d(t){var i,o=r.maxInput,n=r.minInput,s=e.xAxis[0],a=(null===(i=e.scroller)||void 0===i?void 0:i.getUnionExtremes())||s,h=a.dataMin,d=a.dataMax,c=e.xAxis[0].getExtremes()[t],p=r.getInputValue(t);pB(p)&&p!==c&&(l&&o&&pB(h)?p>Number(o.getAttribute("data-hc-time"))?p=void 0:p<h&&(p=h):n&&pB(d)&&(p<Number(n.getAttribute("data-hc-time"))?p=void 0:p>d&&(p=d)),void 0!==p&&s.setExtremes(l?p:s.min,l?s.max:p,void 0,void 0,{trigger:"rangeSelectorInput"}))}var c=h[l?"rangeSelectorFrom":"rangeSelectorTo"]||"",p=s.label(c,0).addClass("highcharts-range-label").attr({padding:2*!!c,height:c?a.inputBoxHeight:0}).add(o),u=s.label("",0).addClass("highcharts-range-input").attr({padding:2,width:a.inputBoxWidth,height:a.inputBoxHeight,"text-align":"center"}).on("click",function(){r.showInput(t),r[t+"Input"].focus()});e.styledMode||u.attr({stroke:a.inputBoxBorderColor,"stroke-width":1}),u.add(o);var f=pS("input",{name:t,className:"highcharts-range-selector"},void 0,i);f.setAttribute("type",pW(a.inputDateFormat||"%e %b %Y")),e.styledMode||(p.css(pD(n,a.labelStyle)),u.css(pD({color:"#333333"},n,a.inputStyle)),pA(f,pE({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:n.fontSize,fontFamily:n.fontFamily,top:"-9999em"},a.inputStyle))),f.onfocus=function(){r.showInput(t)},f.onblur=function(){f===tu.doc.activeElement&&d(t),r.hideInput(t),r.setInputValue(t),f.blur()};var g=!1;return f.onchange=function(){g||(d(t),r.hideInput(t),f.blur())},f.onkeypress=function(e){13===e.keyCode&&d(t)},f.onkeydown=function(e){g=!0,("ArrowUp"===e.key||"ArrowDown"===e.key||"Tab"===e.key)&&d(t)},f.onkeyup=function(){g=!1},{dateBox:u,input:f,label:p}},t.prototype.getPosition=function(){var t=this.chart,e=t.options.rangeSelector,i="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:i+e.buttonPosition.y,inputTop:i+e.inputPosition.y-10}},t.prototype.getYTDExtremes=function(t,e){var i=this.chart.time,o=i.toParts(t)[0];return{max:t,min:Math.max(e,i.makeTime(o,0))}},t.prototype.createElements=function(){var t,e=this.chart,i=e.renderer,o=e.container,r=e.options,n=r.rangeSelector,s=n.inputEnabled,a=pR(null===(t=r.chart.style)||void 0===t?void 0:t.zIndex,0)+1;!1!==n.enabled&&(this.group=i.g("range-selector-group").attr({zIndex:7}).add(),this.div=pS("div",void 0,{position:"relative",height:0,zIndex:a}),this.buttonOptions.length&&this.renderButtons(),o.parentNode&&o.parentNode.insertBefore(this.div,o),s&&this.createInputs())},t.prototype.createInputs=function(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);var t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;var e=this.drawInput("max");this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input},t.prototype.render=function(t,e){if(!1!==this.options.enabled){var i,o,r=this.chart,n=r.options.rangeSelector;if(n.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",e),this.chart.styledMode||(null===(i=this.maxLabel)||void 0===i||i.css(n.labelStyle),null===(o=this.minLabel)||void 0===o||o.css(n.labelStyle));var s=r.scroller&&r.scroller.getUnionExtremes()||r.xAxis[0]||{};if(pT(s.dataMin)&&pT(s.dataMax)){var a=r.xAxis[0].minRange||0;this.setInputExtremes("min",s.dataMin,Math.min(s.dataMax,this.getInputValue("max"))-a),this.setInputExtremes("max",Math.max(s.dataMin,this.getInputValue("min"))+a,s.dataMax)}if(this.inputGroup){var h=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(function(t){if(t){var e=t.getBBox().width;e&&(t.attr({x:h}),h+=e+n.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(n.labelStyle),this.alignElements(),this.updateButtonStates()}},t.prototype.renderButtons=function(){var t,e,i,o=this,r=this.chart,n=this.options,s=ee.lang,a=r.renderer,h=pD(n.buttonTheme),l=h&&h.states;delete h.width,delete h.states,this.buttonGroup=a.g("range-selector-buttons").add(this.group);var d=this.dropdown=pS("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),c=null===(t=r.userOptions.rangeSelector)||void 0===t?void 0:t.buttonTheme;this.dropdownLabel=a.button("",0,0,function(){},pD(h,{"stroke-width":pR(h["stroke-width"],0),width:"auto",paddingLeft:pR(n.buttonTheme.paddingLeft,null==c?void 0:c.padding,8),paddingRight:pR(n.buttonTheme.paddingRight,null==c?void 0:c.padding,8)}),l&&l.hover,l&&l.select,l&&l.disabled).hide().add(this.group),pw(d,"touchstart",function(){d.style.fontSize="16px"});var p=tu.isMS?"mouseover":"mouseenter",u=tu.isMS?"mouseout":"mouseleave";pw(d,p,function(){pL(o.dropdownLabel.element,p)}),pw(d,u,function(){pL(o.dropdownLabel.element,u)}),pw(d,"change",function(){pL(o.buttons[d.selectedIndex-1].element,"click")}),this.zoomText=a.label(s.rangeSelectorZoom||"",0).attr({padding:n.buttonTheme.padding,height:n.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(n.labelStyle),(null===(e=(i=n.buttonTheme)["stroke-width"])||void 0===e)&&(i["stroke-width"]=0)),pS("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,d),this.createButtons()},t.prototype.createButtons=function(){var t=this,e=pD(this.options.buttonTheme),i=e&&e.states,o=e.width||28;delete e.width,delete e.states,this.buttonOptions.forEach(function(e,r){t.createButton(e,r,o,i)})},t.prototype.createButton=function(t,e,i,o){var r,n=this,s=this.dropdown,a=this.buttons,h=this.chart,l=this.options,d=h.renderer,c=pD(l.buttonTheme);null==s||s.add(pS("option",{textContent:t.title||t.text}),e+2),a[e]=d.button(null!==(r=t.text)&&void 0!==r?r:"",0,0,function(i){var o,r=t.events&&t.events.click;r&&(o=r.call(t,i)),!1!==o&&n.clickButton(e),n.isActive=!0},c,o&&o.hover,o&&o.select,o&&o.disabled).attr({"text-align":"center",width:i}).add(this.buttonGroup),t.title&&a[e].attr("title",t.title)},t.prototype.alignElements=function(){var t,e=this,i=this.buttonGroup,o=this.buttons,r=this.chart,n=this.group,s=this.inputGroup,a=this.options,h=this.zoomText,l=r.options,d=l.exporting&&!1!==l.exporting.enabled&&l.navigation&&l.navigation.buttonOptions,c=a.buttonPosition,p=a.inputPosition,u=a.verticalAlign,f=function(t,i,o){return d&&e.titleCollision(r)&&"top"===u&&o&&i.y-t.getBBox().height-12<(d.y||0)+(d.height||0)+r.spacing[0]?-40:0},g=r.plotLeft;if(n&&c&&p){var v=c.x-r.spacing[3];if(i){if(this.positionButtons(),!this.initialButtonGroupWidth){var m=0;h&&(m+=h.getBBox().width+5),o.forEach(function(t,e){m+=t.width||0,e!==o.length-1&&(m+=a.buttonSpacing)}),this.initialButtonGroupWidth=m}g-=r.spacing[3];var y=f(i,c,"right"===c.align||"right"===p.align);this.alignButtonGroup(y),(null===(t=this.buttonGroup)||void 0===t?void 0:t.translateY)&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),n.placed=i.placed=r.hasLoaded}var x=0;a.inputEnabled&&s&&(x=f(s,p,"right"===c.align||"right"===p.align),"left"===p.align?v=g:"right"===p.align&&(v=-Math.max(r.axisOffset[1],-x)),s.align({y:p.y,width:s.getBBox().width,align:p.align,x:p.x+v-2},!0,r.spacingBox),s.placed=r.hasLoaded),this.handleCollision(x),n.align({verticalAlign:u},!0,r.spacingBox);var b=n.alignAttr.translateY,M=n.getBBox().height+20,k=0;if("bottom"===u){var w=r.legend&&r.legend.options;k=b-(M=M+(w&&"bottom"===w.verticalAlign&&w.enabled&&!w.floating?r.legend.legendHeight+pR(w.margin,10):0)-20)-(a.floating?0:a.y)-(r.titleOffset?r.titleOffset[2]:0)-10}"top"===u?(a.floating&&(k=0),r.titleOffset&&r.titleOffset[0]&&(k=r.titleOffset[0]),k+=r.margin[0]-r.spacing[0]||0):"middle"===u&&(p.y===c.y?k=b:(p.y||c.y)&&(p.y<0||c.y<0?k-=Math.min(p.y,c.y):k=b-M)),n.translate(a.x,a.y+Math.floor(k));var S=this.minInput,A=this.maxInput,T=this.dropdown;a.inputEnabled&&S&&A&&(S.style.marginTop=n.translateY+"px",A.style.marginTop=n.translateY+"px"),T&&(T.style.marginTop=n.translateY+"px")}},t.prototype.redrawElements=function(){var t,e,i,o,r,n,s,a=this.chart,h=this.options,l=h.inputBoxHeight,d=h.inputBoxBorderColor;if(null===(t=this.maxDateBox)||void 0===t||t.attr({height:l}),null===(e=this.minDateBox)||void 0===e||e.attr({height:l}),a.styledMode||(null===(i=this.maxDateBox)||void 0===i||i.attr({stroke:d}),null===(o=this.minDateBox)||void 0===o||o.attr({stroke:d})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;var c=null!==(r=this.options.buttons)&&void 0!==r?r:[],p=Math.min(c.length,this.buttonOptions.length),u=this.dropdown,f=pD(this.options.buttonTheme),g=f&&f.states,v=f.width||28;if(c.length<this.buttonOptions.length)for(var m=this.buttonOptions.length-1;m>=c.length;m--){var y=this.buttons.pop();null==y||y.destroy(),null===(n=this.dropdown)||void 0===n||n.options.remove(m+1)}for(var m=p-1;m>=0;m--)if(0!==Object.keys(pO(c[m],this.buttonOptions[m])).length){var x=c[m];this.buttons[m].destroy(),null==u||u.options.remove(m+1),this.createButton(x,m,v,g),this.computeButtonRange(x)}if(c.length>this.buttonOptions.length)for(var m=this.buttonOptions.length;m<c.length;m++)this.createButton(c[m],m,v,g),this.computeButtonRange(c[m]);this.buttonOptions=null!==(s=this.options.buttons)&&void 0!==s?s:[],pT(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}},t.prototype.alignButtonGroup=function(t,e){var i=this.chart,o=this.options,r=this.buttonGroup,n=this.dropdown,s=this.dropdownLabel,a=o.buttonPosition,h=i.plotLeft-i.spacing[3],l=a.x-i.spacing[3],d=i.plotLeft;"right"===a.align?(l+=t-h,this.hasVisibleDropdown&&(d=i.chartWidth+t-this.maxButtonWidth()-20)):"center"===a.align&&(l-=h/2,this.hasVisibleDropdown&&(d=i.chartWidth/2-this.maxButtonWidth())),n&&pA(n,{left:d+"px",top:(null==r?void 0:r.translateY)+"px"}),null==s||s.attr({x:d}),r&&r.align({y:a.y,width:pR(e,this.initialButtonGroupWidth),align:a.align,x:l},!0,i.spacingBox)},t.prototype.positionButtons=function(){var t=this.buttons,e=this.chart,i=this.options,o=this.zoomText,r=e.hasLoaded?"animate":"attr",n=i.buttonPosition,s=e.plotLeft,a=s;o&&"hidden"!==o.visibility&&(o[r]({x:pR(s+n.x,s)}),a+=n.x+o.getBBox().width+5);for(var h=0,l=this.buttonOptions.length;h<l;++h)"hidden"!==t[h].visibility?(t[h][r]({x:a}),a+=(t[h].width||0)+i.buttonSpacing):t[h][r]({x:s})},t.prototype.handleCollision=function(t){var e=this.chart,i=this.buttonGroup,o=this.inputGroup,r=this.initialButtonGroupWidth,n=this.options,s=n.buttonPosition,a=n.dropdown,h=n.inputPosition,l=function(){o&&i&&o.attr({translateX:o.alignAttr.translateX+(e.axisOffset[1]>=-t?0:-t),translateY:o.alignAttr.translateY+i.getBBox().height+10})};o&&i?h.align===s.align?(l(),r>e.plotWidth+t-20?this.collapseButtons():this.expandButtons()):r-t+o.getBBox().width>e.plotWidth?"responsive"===a?this.collapseButtons():l():this.expandButtons():i&&"responsive"===a&&(r>e.plotWidth?this.collapseButtons():this.expandButtons()),i&&("always"===a&&this.collapseButtons(),"never"===a&&this.expandButtons()),this.alignButtonGroup(t)},t.prototype.collapseButtons=function(){var t=this.buttons,e=this.zoomText;!0!==this.isCollapsed&&(this.isCollapsed=!0,e.hide(),t.forEach(function(t){t.hide()}),this.showDropdown())},t.prototype.expandButtons=function(){var t=this.buttons,e=this.zoomText;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),e.show(),t.forEach(function(t){t.show()}),this.positionButtons())},t.prototype.showDropdown=function(){var t=this.buttonGroup,e=this.dropdownLabel,i=this.dropdown;t&&i&&(e.show(),pA(i,{visibility:"inherit"}),this.hasVisibleDropdown=!0)},t.prototype.hideDropdown=function(){var t=this.dropdown;t&&(this.dropdownLabel.hide(),pA(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)},t.prototype.getHeight=function(){var t=this.options,e=this.group,i=t.inputPosition,o=t.buttonPosition,r=t.y,n=o.y,s=i.y,a=0;if(t.height)return t.height;this.alignElements(),a=e?e.getBBox(!0).height+13+r:0;var h=Math.min(s,n);return(s<0&&n<0||s>0&&n>0)&&(a+=Math.abs(h)),a},t.prototype.titleCollision=function(t){return!(t.options.title.text||t.options.subtitle.text)},t.prototype.update=function(t,e){void 0===e&&(e=!0);var i=this.chart;if(pD(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,i.options.rangeSelector.selected=void 0),pT(t.enabled))return this.destroy(),this.init(i);this.isDirty=!!t.buttons,e&&this.render()},t.prototype.destroy=function(){var e=this,i=e.minInput,o=e.maxInput;e.eventsToUnbind&&(e.eventsToUnbind.forEach(function(t){return t()}),e.eventsToUnbind=void 0),pP(e.buttons),i&&(i.onfocus=i.onblur=i.onchange=null),o&&(o.onfocus=o.onblur=o.onchange=null),pz(e,function(i,o){i&&"chart"!==o&&(i instanceof iB?i.destroy():i instanceof window.HTMLElement&&pC(i),delete e[o]),i!==t.prototype[o]&&(e[o]=null)},this),this.buttons=[]},t}();pE(pG.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}});var pX=function(t,e){for(var i=[],o=0;o<t.length;o++){var r=t[o][1],n=t[o][2];if("number"==typeof r&&"number"==typeof n){if(0===o)i.push(["M",r,n]);else if(o===t.length-1)i.push(["L",r,n]);else if(e){var s=t[o-1],a=t[o+1];if(s&&a){var h=s[1],l=s[2],d=a[1],c=a[2];if("number"==typeof h&&"number"==typeof d&&"number"==typeof l&&"number"==typeof c&&h!==d&&l!==c){var p=h<d?1:-1,u=l<c?1:-1;i.push(["L",r-p*Math.min(Math.abs(r-h),e),n-u*Math.min(Math.abs(n-l),e)],["C",r,n,r,n,r+p*Math.min(Math.abs(r-d),e),n+u*Math.min(Math.abs(n-c),e)])}}}else i.push(["L",r,n])}}return i},pF=tG.pick,pH=Math.min,pj=Math.max,pY=Math.abs;function p_(t,e,i){for(var o,r,n=e-1e-7,s=i||0,a=t.length-1;s<=a;)if((r=n-t[o=a+s>>1].xMin)>0)s=o+1;else{if(!(r<0))return o;a=o-1}return s>0?s-1:0}function pU(t,e){for(var i,o=p_(t,e.x+1)+1;o--;)if(t[o].xMax>=e.x&&(i=t[o],e.x<=i.xMax&&e.x>=i.xMin&&e.y<=i.yMax&&e.y>=i.yMin))return o;return -1}function pV(t){var e=[];if(t.length){e.push(["M",t[0].start.x,t[0].start.y]);for(var i=0;i<t.length;++i)e.push(["L",t[i].end.x,t[i].end.y])}return e}function pq(t,e){t.yMin=pj(t.yMin,e.yMin),t.yMax=pH(t.yMax,e.yMax),t.xMin=pj(t.xMin,e.xMin),t.xMax=pH(t.xMax,e.xMax)}var pZ=function(t,e,i){var o,r,n,s,a,h=[],l=i.chartObstacles,d=pU(l,t),c=pU(l,e),p=pF(i.startDirectionX,pY(e.x-t.x)>pY(e.y-t.y))?"x":"y";function u(t,e,i,o,r){var n={x:t.x,y:t.y};return n[e]=i[o||e]+(r||0),n}function f(t,e,i){var o=pY(e[i]-t[i+"Min"])>pY(e[i]-t[i+"Max"]);return u(e,i,t,i+(o?"Max":"Min"),o?1:-1)}c>-1?(o={start:n=f(l[c],e,p),end:e},a=n):a=e,d>-1&&(n=f(r=l[d],t,p),h.push({start:t,end:n}),n[p]>=t[p]==n[p]>=a[p]&&(s=t[p="y"===p?"x":"y"]<e[p],h.push({start:n,end:u(n,p,r,p+(s?"Max":"Min"),s?1:-1)}),p="y"===p?"x":"y"));var g=h.length?h[h.length-1].end:t;n=u(g,p,a),h.push({start:g,end:n});var v=u(n,p="y"===p?"x":"y",a);return h.push({start:n,end:v}),h.push(o),{path:pX(pV(h),i.radius),obstacles:h}};function pK(t,e,i){var o,r,n,s,a,h,l,d=pF(i.startDirectionX,pY(e.x-t.x)>pY(e.y-t.y)),c=d?"x":"y",p=[],u=i.obstacleMetrics,f=pH(t.x,e.x)-u.maxWidth-10,g=pj(t.x,e.x)+u.maxWidth+10,v=pH(t.y,e.y)-u.maxHeight-10,m=pj(t.y,e.y)+u.maxHeight+10,y=!1,x=i.chartObstacles,b=p_(x,g),M=p_(x,f);function k(t,e,i){var o,r,n,s,a=t.x<e.x?1:-1;t.x<e.x?(o=t,r=e):(o=e,r=t),t.y<e.y?(s=t,n=e):(s=e,n=t);for(var h=a<0?pH(p_(x,r.x),x.length-1):0;x[h]&&(a>0&&x[h].xMin<=r.x||a<0&&x[h].xMax>=o.x);){if(x[h].xMin<=r.x&&x[h].xMax>=o.x&&x[h].yMin<=n.y&&x[h].yMax>=s.y){if(i)return{y:t.y,x:t.x<e.x?x[h].xMin-1:x[h].xMax+1,obstacle:x[h]};return{x:t.x,y:t.y<e.y?x[h].yMin-1:x[h].yMax+1,obstacle:x[h]}}h+=a}return e}function w(t,e,i,o,r){var n=r.soft,s=r.hard,a=o?"x":"y",h={x:e.x,y:e.y},l={x:e.x,y:e.y},d=t[a+"Max"]>=n[a+"Max"],c=t[a+"Min"]<=n[a+"Min"],p=t[a+"Max"]>=s[a+"Max"],u=t[a+"Min"]<=s[a+"Min"],f=pY(t[a+"Min"]-e[a]),g=pY(t[a+"Max"]-e[a]),v=10>pY(f-g)?e[a]<i[a]:g<f;l[a]=t[a+"Min"],h[a]=t[a+"Max"];var m=k(e,l,o)[a]!==l[a],y=k(e,h,o)[a]!==h[a];return v=m?!y||v:!y&&v,v=c?!d||v:!d&&v,v=u?!p||v:!p&&v}for((b=pU(x=x.slice(M,b+1),e))>-1&&(o=x[b],r=e,n=pH(o.xMax-r.x,r.x-o.xMin)<pH(o.yMax-r.y,r.y-o.yMin),s=w(o,r,t,n,{soft:i.hardBounds,hard:i.hardBounds}),l=n?{y:r.y,x:o[s?"xMax":"xMin"]+(s?1:-1)}:{x:r.x,y:o[s?"yMax":"yMin"]+(s?1:-1)},p.push({end:e,start:l}),e=l);(b=pU(x,e))>-1;)h=e[c]-t[c]<0,(l={x:e.x,y:e.y})[c]=x[b][h?c+"Max":c+"Min"]+(h?1:-1),p.push({end:e,start:l}),e=l;return{path:pV(a=(a=function t(e,o,r){if(e.x===o.x&&e.y===o.y)return[];var n,s,a,h,l,d,c,p=r?"x":"y",u=i.obstacleOptions.margin,b={soft:{xMin:f,xMax:g,yMin:v,yMax:m},hard:i.hardBounds};return(l=pU(x,e))>-1?(h=w(l=x[l],e,o,r,b),pq(l,i.hardBounds),c=r?{y:e.y,x:l[h?"xMax":"xMin"]+(h?1:-1)}:{x:e.x,y:l[h?"yMax":"yMin"]+(h?1:-1)},(d=pU(x,c))>-1&&(pq(d=x[d],i.hardBounds),c[p]=h?pj(l[p+"Max"]-u+1,(d[p+"Min"]+l[p+"Max"])/2):pH(l[p+"Min"]+u-1,(d[p+"Max"]+l[p+"Min"])/2),e.x===c.x&&e.y===c.y?(y&&(c[p]=h?pj(l[p+"Max"],d[p+"Max"])+1:pH(l[p+"Min"],d[p+"Min"])-1),y=!y):y=!1),s=[{start:e,end:c}]):(n=k(e,{x:r?o.x:e.x,y:r?e.y:o.y},r),s=[{start:e,end:{x:n.x,y:n.y}}],n[r?"x":"y"]!==o[r?"x":"y"]&&(h=w(n.obstacle,n,o,!r,b),pq(n.obstacle,i.hardBounds),a={x:r?n.x:n.obstacle[h?"xMax":"xMin"]+(h?1:-1),y:r?n.obstacle[h?"yMax":"yMin"]+(h?1:-1):n.y},r=!r,s=s.concat(t({x:n.x,y:n.y},a,r)))),s=s.concat(t(s[s.length-1].end,o,!r))}(t,e,d)).concat(p.reverse())),obstacles:a}}pZ.requiresObstacles=!0,pK.requiresObstacles=!0;var p$={connectors:{type:"straight",radius:0,lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}},pJ=tG.defined,pQ=tG.error,p0=tG.merge;function p1(t){var e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};var i=t.graphic&&t.graphic.getBBox();return i?{xMin:t.plotX-i.width/2,xMax:t.plotX+i.width/2,yMin:t.plotY-i.height/2,yMax:t.plotY+i.height/2}:null}!function(t){function e(t){var e,i,o=p1(this);switch(t.align){case"right":e="xMax";break;case"left":e="xMin"}switch(t.verticalAlign){case"top":i="yMin";break;case"bottom":i="yMax"}return{x:e?o[e]:(o.xMin+o.xMax)/2,y:i?o[i]:(o.yMin+o.yMax)/2}}function i(t,e){var i;return!pJ(e)&&(i=p1(this))&&(e={x:(i.xMin+i.xMax)/2,y:(i.yMin+i.yMax)/2}),Math.atan2(e.y-t.y,t.x-e.x)}function o(t,e,i){for(var o=2*Math.PI,r=p1(this),n=r.xMax-r.xMin,s=r.yMax-r.yMin,a=Math.atan2(s,n),h=n/2,l=s/2,d=r.xMin+h,c=r.yMin+l,p={x:d,y:c},u=t,f=1,g=!1,v=1,m=1;u<-Math.PI;)u+=o;for(;u>Math.PI;)u-=o;return f=Math.tan(u),u>-a&&u<=a?(m=-1,g=!0):u>a&&u<=Math.PI-a?m=-1:u>Math.PI-a||u<=-(Math.PI-a)?(v=-1,g=!0):v=-1,g?(p.x+=v*h,p.y+=m*h*f):(p.x+=s/(2*f)*v,p.y+=m*l),i.x!==d&&(p.x=i.x),i.y!==c&&(p.y=i.y),{x:p.x+e*Math.cos(u),y:p.y-e*Math.sin(u)}}t.compose=function(t,r,n){var s=n.prototype;s.getPathfinderAnchorPoint||(t.prototype.callbacks.push(function(t){if(!1!==t.options.connectors.enabled)(t.options.pathfinder||t.series.reduce(function(t,e){return e.options&&p0(!0,e.options.connectors=e.options.connectors||{},e.options.pathfinder),t||e.options&&e.options.pathfinder},!1))&&(p0(!0,t.options.connectors=t.options.connectors||{},t.options.pathfinder),pQ('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')),this.pathfinder=new r(this),this.pathfinder.update(!0)}),s.getMarkerVector=o,s.getPathfinderAnchorPoint=e,s.getRadiansToVector=i,eo(p$))}}(ta||(ta={}));var p2=ta,p3=tG.addEvent,p5=tG.defined,p6=tG.pick,p9=tG.splat,p4=Math.max,p8=Math.min,p7=function(){function t(t){this.init(t)}return t.compose=function(e,i){p2.compose(e,t,i)},t.prototype.init=function(t){this.chart=t,this.connections=[],p3(t,"redraw",function(){this.pathfinder.update()})},t.prototype.update=function(t){var e=this.chart,i=this,o=i.connections;i.connections=[],e.series.forEach(function(t){t.visible&&!t.options.isInternal&&t.points.forEach(function(t){var o,r,n=t.options;n&&n.dependency&&(n.connect=n.dependency);var s=(null===(o=t.options)||void 0===o?void 0:o.connect)?p9(t.options.connect):[];t.visible&&!1!==t.isInside&&s.forEach(function(o){var n="string"==typeof o?o:o.to;n&&(r=e.get(n)),r instanceof nP&&r.series.visible&&r.visible&&!1!==r.isInside&&i.connections.push(new dE(t,r,"string"==typeof o?{}:o))})})});for(var r=0,n=void 0,s=void 0,a=o.length,h=i.connections.length;r<a;++r){s=!1;var l=o[r];for(n=0;n<h;++n){var d=i.connections[n];if((l.options&&l.options.type)===(d.options&&d.options.type)&&l.fromPoint===d.fromPoint&&l.toPoint===d.toPoint){d.graphics=l.graphics,s=!0;break}}s||l.destroy()}delete this.chartObstacles,delete this.lineObstacles,i.renderConnections(t)},t.prototype.renderConnections=function(t){t?this.chart.series.forEach(function(t){var e=function(){var e=t.chart.pathfinder;(e&&e.connections||[]).forEach(function(e){e.fromPoint&&e.fromPoint.series===t&&e.render()}),t.pathfinderRemoveRenderEvent&&(t.pathfinderRemoveRenderEvent(),delete t.pathfinderRemoveRenderEvent)};!1===t.options.animation?e():t.pathfinderRemoveRenderEvent=p3(t,"afterAnimate",e)}):this.connections.forEach(function(t){t.render()})},t.prototype.getChartObstacles=function(t){for(var e,i=this.chart.series,o=p6(t.algorithmMargin,0),r=[],n=0,s=i.length;n<s;++n)if(i[n].visible&&!i[n].options.isInternal)for(var a=0,h=i[n].points.length,l=void 0,d=void 0;a<h;++a)(d=i[n].points[a]).visible&&(l=function(t){var e=t.shapeArgs;if(e)return{xMin:e.x||0,xMax:(e.x||0)+(e.width||0),yMin:e.y||0,yMax:(e.y||0)+(e.height||0)};var i=t.graphic&&t.graphic.getBBox();return i?{xMin:t.plotX-i.width/2,xMax:t.plotX+i.width/2,yMin:t.plotY-i.height/2,yMax:t.plotY+i.height/2}:null}(d))&&r.push({xMin:l.xMin-o,xMax:l.xMax+o,yMin:l.yMin-o,yMax:l.yMax+o});return r=r.sort(function(t,e){return t.xMin-e.xMin}),p5(t.algorithmMargin)||(e=t.algorithmMargin=function(t){for(var e,i=t.length,o=[],r=0;r<i;++r)for(var n=r+1;n<i;++n)(e=function t(e,i,o){var r=p6(o,10),n=e.yMax+r>i.yMin-r&&e.yMin-r<i.yMax+r,s=e.xMax+r>i.xMin-r&&e.xMin-r<i.xMax+r,a=n?e.xMin>i.xMax?e.xMin-i.xMax:i.xMin-e.xMax:1/0,h=s?e.yMin>i.yMax?e.yMin-i.yMax:i.yMin-e.yMax:1/0;return s&&n?r?t(e,i,Math.floor(r/2)):1/0:p8(a,h)}(t[r],t[n]))<80&&o.push(e);return o.push(80),p4(Math.floor(o.sort(function(t,e){return t-e})[Math.floor(o.length/10)]/2-1),1)}(r),r.forEach(function(t){t.xMin-=e,t.xMax+=e,t.yMin-=e,t.yMax+=e})),r},t.prototype.getObstacleMetrics=function(t){for(var e,i,o=0,r=0,n=t.length;n--;)e=t[n].xMax-t[n].xMin,i=t[n].yMax-t[n].yMin,o<e&&(o=e),r<i&&(r=i);return{maxHeight:r,maxWidth:o}},t.prototype.getAlgorithmStartDirection=function(t){var e="left"!==t.align&&"right"!==t.align,i="top"!==t.verticalAlign&&"bottom"!==t.verticalAlign;return e?!!i&&void 0:!!i||void 0},t}();p7.prototype.algorithms={fastAvoid:pK,straight:function(t,e){return{path:[["M",t.x,t.y],["L",e.x,e.y]],obstacles:[{start:t,end:e}]}},simpleConnect:pZ},tu.Pathfinder=tu.Pathfinder||p7,dk(tu.SVGRenderer),tu.Pathfinder.compose(tu.Chart,tu.Point);var ut=tG.addEvent,ue=tG.defined,ui=tG.isNumber,uo=tG.pick;function ur(){var t=this.chart.options.chart;!this.horiz&&ui(this.options.staticScale)&&(!t.height||t.scrollablePlotArea&&t.scrollablePlotArea.minHeight)&&(this.staticScale=this.options.staticScale)}function un(){if("adjustHeight"!==this.redrawTrigger){for(var t=0,e=this.axes||[];t<e.length;t++)!function(t){var e=t.chart,i=!!e.initiatedScale&&e.options.animation,o=t.options.staticScale;if(t.staticScale&&ue(t.min)){var r=uo(t.brokenAxis&&t.brokenAxis.unitLength,t.max+t.tickInterval-t.min)*o,n=(r=Math.max(r,o))-e.plotHeight;!e.scrollablePixelsY&&Math.abs(n)>=1&&(e.plotHeight=r,e.redrawTrigger="adjustHeight",e.setSize(void 0,e.chartHeight+n,i)),t.series.forEach(function(t){var i=t.sharedClipKey&&e.sharedClips[t.sharedClipKey];i&&i.attr(e.inverted?{width:e.plotHeight}:{height:e.plotHeight})})}}(e[t]);this.initiatedScale=!0}this.redrawTrigger=null}var us=function(t,e){var i=e.prototype;i.adjustHeight||(ut(t,"afterSetOptions",ur),i.adjustHeight=un,ut(e,"render",i.adjustHeight))};us(tu.Axis,tu.Chart);var ua=tG.correctFloat,uh=tG.isNumber,ul=tG.isObject,ud={colorByPoint:!0,dataLabels:{formatter:function(){var t=this.partialFill;if(ul(t)&&(t=t.amount),uh(t)&&t>0)return ua(100*t)+"%"},inside:!0,verticalAlign:"middle",style:{whiteSpace:"nowrap"}},tooltip:{headerFormat:'<span style="font-size: 0.8em">{ucfirst point.x} - {point.x2}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.yCategory}</b><br/>'},borderRadius:3,pointRange:0},uc=(B=function(t,e){return(B=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}B(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),up=si.seriesTypes.column.prototype.pointClass,uu=tG.extend,uf=function(t){function e(e,i){var o=t.call(this,e,i)||this;return o.y||(o.y=0),o}return uc(e,t),e.getColorByCategory=function(t,e){var i=t.options.colors||t.chart.options.colors,o=i?i.length:t.chart.options.chart.colorCount,r=e.y%o,n=null==i?void 0:i[r];return{colorIndex:r,color:n}},e.prototype.resolveColor=function(){var t=this.series;if(t.options.colorByPoint&&!this.options.color){var i=e.getColorByCategory(t,this);t.chart.styledMode||(this.color=i.color),this.options.colorIndex||(this.colorIndex=i.colorIndex)}else this.color=this.options.color||t.color},e.prototype.applyOptions=function(e,i){var o;return t.prototype.applyOptions.call(this,e,i),this.x2=this.series.chart.time.parse(this.x2),this.isNull=!(null===(o=this.isValid)||void 0===o?void 0:o.call(this)),this},e.prototype.setState=function(){t.prototype.setState.apply(this,arguments),this.series.drawPoint(this,this.series.getAnimationVerb())},e.prototype.isValid=function(){return"number"==typeof this.x&&"number"==typeof this.x2},e}(up);uu(uf.prototype,{ttBelow:!1,tooltipDateKeys:["x","x2"]});var ug=(I=function(t,e){return(I=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}I(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uv=tu.composed,um=tu.noop,uy=ep.parse,ux=si.seriesTypes.column,ub=tG.addEvent,uM=tG.clamp,uk=tG.crisp,uw=tG.defined,uS=tG.extend,uA=tG.find,uT=tG.isNumber,uP=tG.isObject,uO=tG.merge,uC=tG.pick,uE=tG.pushUnique,uL=tG.relativeLength;function uB(){var t,e;if(this.isXAxis){t=uC(this.dataMax,-Number.MAX_VALUE);for(var i=0,o=this.series;i<o.length;i++){var r=o[i],n=r.dataTable.getColumn("x2",!0)||r.dataTable.getColumn("end",!0);if(n)for(var s=0;s<n.length;s++){var a=n[s];uT(a)&&a>t&&(t=a,e=!0)}}e&&(this.dataMax=t)}}var uI=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ug(e,t),e.compose=function(t){uE(uv,"Series.XRange")&&ub(t,"afterGetSeriesExtremes",uB)},e.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options.stacking=void 0},e.prototype.getColumnMetrics=function(){var e=this,i=function(){for(var t=0,i=e.chart.series;t<i.length;t++){var o=i[t],r=o.xAxis;o.xAxis=o.yAxis,o.yAxis=r}};i();var o=t.prototype.getColumnMetrics.call(this);return i(),o},e.prototype.cropData=function(e,i,o){var r=e.getColumn("x")||[],n=e.getColumn("x2");e.setColumn("x",n,void 0,{silent:!0});var s=t.prototype.cropData.call(this,e,i,o);return e.setColumn("x",r.slice(s.start,s.end),void 0,{silent:!0}),s},e.prototype.findPointIndex=function(t){var e,i=this.cropStart,o=this.points,r=t.id;if(r){var n=uA(o,function(t){return t.id===r});e=n?n.index:void 0}if(void 0===e){var n=uA(o,function(e){return e.x===t.x&&e.x2===t.x2&&!e.touched});e=n?n.index:void 0}return this.cropped&&uT(e)&&uT(i)&&e>=i&&(e-=i),e},e.prototype.alignDataLabel=function(e){var i,o,r=e.plotX;e.plotX=uC(null===(i=e.dlBox)||void 0===i?void 0:i.centerX,e.plotX),e.dataLabel&&(null===(o=e.shapeArgs)||void 0===o?void 0:o.width)&&e.dataLabel.css({width:""+e.shapeArgs.width+"px"}),t.prototype.alignDataLabel.apply(this,arguments),e.plotX=r},e.prototype.translatePoint=function(t){var e,i,o,r,n,s,a,h,l,d=this.xAxis,c=this.yAxis,p=this.columnMetrics,u=this.options,f=u.minPointLength||0,g=((null===(e=t.shapeArgs)||void 0===e?void 0:e.width)||0)/2,v=this.pointXOffset=p.offset,m=uC(t.x2,t.x+(t.len||0)),y=u.borderRadius,x=this.chart.plotTop,b=this.chart.plotLeft,M=t.plotX,k=d.translate(m,0,0,0,1),w=Math.abs(k-M),S=this.chart.inverted,A=uC(u.borderWidth,1),T=p.offset,P=Math.round(p.width);f&&((r=f-w)<0&&(r=0),M-=r/2,k+=r/2),M=Math.max(M,-10),k=uM(k,-10,d.len+10),uw(t.options.pointWidth)&&(T-=(Math.ceil(t.options.pointWidth)-P)/2,P=Math.ceil(t.options.pointWidth)),u.pointPlacement&&uT(t.plotY)&&c.categories&&(t.plotY=c.translate(t.y,0,1,0,1,u.pointPlacement));var O=uk(Math.min(M,k),A),C=uk(Math.max(M,k),A)-O,E=Math.min(uL("object"==typeof y?y.radius:y||0,P),Math.min(C,P)/2),L={x:O,y:uk((t.plotY||0)+T,A),width:C,height:P,r:E};t.shapeArgs=L,S?t.tooltipPos[1]+=v+g:t.tooltipPos[0]-=g+v-L.width/2,a=(s=L.x)+L.width,s<0||a>d.len?(s=uM(s,0,d.len),h=(a=uM(a,0,d.len))-s,t.dlBox=uO(L,{x:s,width:a-s,centerX:h?h/2:null})):t.dlBox=null;var B=t.tooltipPos,I=+!!S,D=+!S,z=this.columnMetrics?this.columnMetrics.offset:-p.width/2;S?B[I]+=L.width/2:B[I]=uM(B[I]+(d.reversed?-1:0)*L.width,d.left-b,d.left+d.len-b-1),B[D]=uM(B[D]+(S?-1:1)*z,c.top-x,c.top+c.len-x-1),(n=t.partialFill)&&(uP(n)&&(n=n.amount),uT(n)||(n=0),t.partShapeArgs=uO(L),l=Math.max(Math.round(w*n+t.plotX-M),0),t.clipRectArgs={x:d.reversed?L.x+w-l:L.x,y:L.y,width:l,height:L.height}),t.key=t.category||t.name,t.yCategory=null===(i=c.categories)||void 0===i?void 0:i[null!==(o=t.y)&&void 0!==o?o:-1]},e.prototype.translate=function(){t.prototype.translate.apply(this,arguments);for(var e=0,i=this.points;e<i.length;e++){var o=i[e];this.translatePoint(o)}},e.prototype.drawPoint=function(t,e){var i=this.options,o=this.chart.renderer,r=t.shapeType,n=t.shapeArgs,s=t.partShapeArgs,a=t.clipRectArgs,h=t.state,l=i.states[h||"normal"]||{},d=void 0===h?"attr":e,c=this.pointAttribs(t,h),p=uC(this.chart.options.chart.animation,l.animation),u=t.graphic,f=t.partialFill;if(t.isNull||!1===t.visible)u&&(t.graphic=u.destroy());else if(u?u.rect[e](n):(t.graphic=u=o.g("point").addClass(t.getClassName()).add(t.group||this.group),u.rect=o[r](uO(n)).addClass(t.getClassName()).addClass("highcharts-partfill-original").add(u)),s&&(u.partRect?(u.partRect[e](uO(s)),u.partialClipRect[e](uO(a))):(u.partialClipRect=o.clipRect(a.x,a.y,a.width,a.height),u.partRect=o[r](s).addClass("highcharts-partfill-overlay").add(u).clip(u.partialClipRect))),!this.chart.styledMode&&(u.rect[e](c,p).shadow(i.shadow),s)){uP(f)||(f={}),uP(i.partialFill)&&(f=uO(i.partialFill,f));var g=f.fill||uy(c.fill).brighten(-.3).get()||uy(t.color||this.color).brighten(-.3).get();c.fill=g,u.partRect[d](c,p).shadow(i.shadow)}},e.prototype.drawPoints=function(){for(var t=this.getAnimationVerb(),e=0,i=this.points;e<i.length;e++){var o=i[e];this.drawPoint(o,t)}},e.prototype.getAnimationVerb=function(){return this.chart.pointCount<(this.options.animationLimit||250)?"animate":"attr"},e.prototype.isPointInside=function(e){var i=e.shapeArgs,o=e.plotX,r=e.plotY;return i?void 0!==o&&void 0!==r&&r>=0&&r<=this.yAxis.len&&(i.x||0)+(i.width||0)>=0&&o<=this.xAxis.len:t.prototype.isPointInside.apply(this,arguments)},e.defaultOptions=uO(ux.defaultOptions,ud),e}(ux);uS(uI.prototype,{pointClass:uf,pointArrayMap:["x2","y"],getExtremesFromAll:!0,keysAffectYAxis:["y"],parallelArrays:["x","x2","y"],requireSorting:!1,type:"xrange",animate:si.series.prototype.animate,autoIncrement:um,buildKDTree:um}),si.registerSeriesType("xrange",uI),uI.compose(tu.Axis);var uD=(D=function(t,e){return(D=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}D(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uz=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return uD(e,t),e.setGanttPointAliases=function(t,e){var i,o,r;t.x=t.start=e.time.parse(null!==(i=t.start)&&void 0!==i?i:t.x),t.x2=t.end=e.time.parse(null!==(o=t.end)&&void 0!==o?o:t.x2),t.partialFill=t.completed=null!==(r=t.completed)&&void 0!==r?r:t.partialFill},e.prototype.applyOptions=function(i,o){var r,n=t.prototype.applyOptions.call(this,i,o);return e.setGanttPointAliases(n,n.series.chart),this.isNull=!(null===(r=this.isValid)||void 0===r?void 0:r.call(this)),n},e.prototype.isValid=function(){return("number"==typeof this.start||"number"==typeof this.x)&&("number"==typeof this.end||"number"==typeof this.x2||this.milestone)},e}(si.seriesTypes.xrange.prototype.pointClass),uR=tG.isNumber,uN={grouping:!1,dataLabels:{enabled:!0},tooltip:{headerFormat:'<span style="font-size: 0.8em">{series.name}</span><br/>',pointFormat:null,pointFormatter:function(){var t=this.series,e=t.xAxis,i=t.tooltipOptions.dateTimeLabelFormats,o=e.options.startOfWeek,r=t.tooltipOptions,n=this.options.milestone,s=r.xDateFormat,a="<b>"+(this.name||this.yCategory)+"</b>";if(r.pointFormat)return this.tooltipFormatter(r.pointFormat);!s&&uR(this.start)&&(s=t.chart.time.getDateFormat(e.closestPointRange,this.start,o,i||{}));var h=t.chart.time.dateFormat(s,this.start),l=t.chart.time.dateFormat(s,this.end);return a+="<br/>",n?a+=h+"<br/>":a+="Start: "+h+"<br/>"+("End: "+l)+"<br/>",a}},connectors:{type:"simpleConnect",animation:{reversed:!0},radius:0,startMarker:{enabled:!0,symbol:"arrow-filled",radius:4,fill:"#fa0",align:"left"},endMarker:{enabled:!1,align:"right"}}},uW=tG.addEvent,uG=tG.find,uX=tG.fireEvent,uF=tG.isArray,uH=tG.isNumber,uj=tG.pick;!function(t){function e(){void 0!==this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)}function i(){var t;(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(this.options.ordinal=!1)}function o(){var t=this.brokenAxis;if(null==t?void 0:t.hasBreaks){for(var e=this.tickPositions,i=this.tickPositions.info,o=[],r=0;r<e.length;r++)t.isInAnyBreak(e[r])||o.push(e[r]);this.tickPositions=o,this.tickPositions.info=i}}function r(){this.brokenAxis||(this.brokenAxis=new l(this))}function n(){var t,e,i=this.isDirty,o=this.options.connectNulls,r=this.points,n=this.xAxis,s=this.yAxis;if(i)for(var a=r.length;a--;){var h=r[a],l=(null!==h.y||!1!==o)&&((null===(t=null==n?void 0:n.brokenAxis)||void 0===t?void 0:t.isInAnyBreak(h.x,!0))||(null===(e=null==s?void 0:s.brokenAxis)||void 0===e?void 0:e.isInAnyBreak(h.y,!0)));h.visible=!l&&!1!==h.options.visible}}function s(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,uj(this.pointArrayMap,["y"]))}function a(t,e){var i,o,r,n,s=this,a=s.points;if(null===(i=null==t?void 0:t.brokenAxis)||void 0===i?void 0:i.hasBreaks){var h=t.brokenAxis;e.forEach(function(e){o=(null==h?void 0:h.breakArray)||[],r=t.isXAxis?t.min:uj(s.options.threshold,t.min);var i,l,d=null===(l=null===(i=null==t?void 0:t.options)||void 0===i?void 0:i.breaks)||void 0===l?void 0:l.filter(function(t){for(var e=!0,i=0;i<o.length;i++){var r=o[i];if(r.from===t.from&&r.to===t.to){e=!1;break}}return e});a.forEach(function(i){n=uj(i["stack"+e.toUpperCase()],i[e]),o.forEach(function(e){if(uH(r)&&uH(n)){var o="";r<e.from&&n>e.to||r>e.from&&n<e.from?o="pointBreak":(r<e.from&&n>e.from&&n<e.to||r>e.from&&n>e.to&&n<e.from)&&(o="pointInBreak"),o&&uX(t,o,{point:i,brk:e})}}),null==d||d.forEach(function(e){uX(t,"pointOutsideOfBreak",{point:i,brk:e})})})})}}function h(){var t=this.currentDataGrouping,e=null==t?void 0:t.gapSize,i=this.points.slice(),o=this.yAxis,r=this.options.gapSize,n=i.length-1;if(r&&n>0){"value"!==this.options.gapUnit&&(r*=this.basePointRange),e&&e>r&&e>=this.basePointRange&&(r=e);for(var s=void 0,a=void 0;n--;)if(a&&!1!==a.visible||(a=i[n+1]),s=i[n],!1!==a.visible&&!1!==s.visible){if(a.x-s.x>r){var h=(s.x+a.x)/2;i.splice(n+1,0,{isNull:!0,x:h}),o.stacking&&this.options.stacking&&((o.stacking.stacks[this.stackKey][h]=new aq(o,o.options.stackLabels,!1,h,this.stack)).total=0)}a=s}}return this.getGraphPath(i)}t.compose=function(t,l){if(!t.keepProps.includes("brokenAxis")){t.keepProps.push("brokenAxis"),uW(t,"init",r),uW(t,"afterInit",e),uW(t,"afterSetTickPositions",o),uW(t,"afterSetOptions",i);var d=l.prototype;d.drawBreaks=a,d.gappedPath=h,uW(l,"afterGeneratePoints",n),uW(l,"afterRender",s)}return t};var l=function(){function t(t){this.hasBreaks=!1,this.axis=t}return t.isInBreak=function(t,e){var i,o=t.repeat||1/0,r=t.from,n=t.to-t.from,s=e>=r?(e-r)%o:o-(r-e)%o;return t.inclusive?s<=n:s<n&&0!==s},t.lin2Val=function(e){var i=this.brokenAxis,o=null==i?void 0:i.breakArray;if(!o||!uH(e))return e;var r,n,s=e;for(n=0;n<o.length&&!((r=o[n]).from>=s);n++)r.to<s?s+=r.len:t.isInBreak(r,s)&&(s+=r.len);return s},t.val2Lin=function(e){var i=this.brokenAxis,o=null==i?void 0:i.breakArray;if(!o||!uH(e))return e;var r,n,s=e;for(n=0;n<o.length;n++)if((r=o[n]).to<=e)s-=r.len;else if(r.from>=e)break;else if(t.isInBreak(r,e)){s-=e-r.from;break}return s},t.prototype.findBreakAt=function(t,e){return uG(e,function(e){return e.from<t&&t<e.to})},t.prototype.isInAnyBreak=function(e,i){var o,r,n,s=this.axis,a=s.options.breaks||[],h=a.length;if(h&&uH(e)){for(;h--;)t.isInBreak(a[h],e)&&(o=!0,r||(r=uj(a[h].showPoints,!s.isXAxis)));n=o&&i?o&&!r:o}return n},t.prototype.setBreaks=function(e,i){var o=this,r=o.axis,n=r.chart.time,s=uF(e)&&!!e.length&&!!Object.keys(e[0]).length;r.isDirty=o.hasBreaks!==s,o.hasBreaks=s,null==e||e.forEach(function(t){t.from=n.parse(t.from)||0,t.to=n.parse(t.to)||0}),e!==r.options.breaks&&(r.options.breaks=r.userOptions.breaks=e),r.forceRedraw=!0,r.series.forEach(function(t){t.isDirty=!0}),s||r.val2lin!==t.val2Lin||(delete r.val2lin,delete r.lin2val),s&&(r.userOptions.ordinal=!1,r.lin2val=t.lin2Val,r.val2lin=t.val2Lin,r.setExtremes=function(t,e,i,n,s){if(o.hasBreaks){for(var a=this.options.breaks||[],h=void 0;h=o.findBreakAt(t,a);)t=h.to;for(;h=o.findBreakAt(e,a);)e=h.from;e<t&&(e=t)}r.constructor.prototype.setExtremes.call(this,t,e,i,n,s)},r.setAxisTranslation=function(){if(r.constructor.prototype.setAxisTranslation.call(this),o.unitLength=void 0,o.hasBreaks){var e,i,n,s,a=r.options.breaks||[],h=[],l=[],d=uj(r.pointRangePadding,0),c=0,p=r.userMin||r.min,u=r.userMax||r.max;a.forEach(function(e){i=e.repeat||1/0,uH(p)&&uH(u)&&(t.isInBreak(e,p)&&(p+=e.to%i-p%i),t.isInBreak(e,u)&&(u-=u%i-e.from%i))}),a.forEach(function(t){if(n=t.from,i=t.repeat||1/0,uH(p)&&uH(u)){for(;n-i>p;)n-=i;for(;n<p;)n+=i;for(s=n;s<u;s+=i)h.push({value:s,move:"in"}),h.push({value:s+t.to-t.from,move:"out",size:t.breakSize})}}),h.sort(function(t,e){return t.value===e.value?+("in"!==t.move)-+("in"!==e.move):t.value-e.value}),e=0,n=p,h.forEach(function(t){1===(e+="in"===t.move?1:-1)&&"in"===t.move&&(n=t.value),0===e&&uH(n)&&(l.push({from:n,to:t.value,len:t.value-n-(t.size||0)}),c+=t.value-n-(t.size||0))}),o.breakArray=l,uH(p)&&uH(u)&&uH(r.min)&&(o.unitLength=u-p-c+d,uX(r,"afterBreaks"),r.staticScale?r.transA=r.staticScale:o.unitLength&&(r.transA*=(u-r.min+d)/o.unitLength),d&&(r.minPixelPadding=r.transA*(r.minPointOffset||0)),r.min=p,r.max=u)}}),uj(i,!0)&&r.chart.redraw()},t}();t.Additions=l}(th||(th={}));var uY=th,u_=tu.dateFormats,uU=tG.addEvent,uV=tG.defined,uq=tG.erase,uZ=tG.find,uK=tG.isArray,u$=tG.isNumber,uJ=tG.merge,uQ=tG.pick,u0=tG.timeUnits,u1=tG.wrap;function u2(t){return tG.isObject(t,!0)}function u3(t,e){var i={width:0,height:0};if(e.forEach(function(e){var o,r=t[e],n=0,s=0;u2(r)&&(n=(o=u2(r.label)?r.label:{}).getBBox?o.getBBox().height:0,o.textStr&&!u$(o.textPxLength)&&(o.textPxLength=o.getBBox().width),s=u$(o.textPxLength)?Math.round(o.textPxLength):0,o.textStr&&(s=Math.round(o.getBBox().width)),i.height=Math.max(n,i.height),i.width=Math.max(s,i.width))}),"treegrid"===this.type&&this.treeGrid&&this.treeGrid.mapOfPosToGridNode){var o=this.treeGrid.mapOfPosToGridNode[-1].height||0;i.width+=this.options.labels.indentation*(o-1)}return i}function u5(t){var e=this.grid,i=3===this.side;if(i||t.apply(this),!(null==e?void 0:e.isColumn)){var o=(null==e?void 0:e.columns)||[];i&&(o=o.slice().reverse()),o.forEach(function(t){t.getOffset()})}i&&t.apply(this)}function u6(t){if(!0===(this.options.grid||{}).enabled){var e=this.axisTitle,i=this.height,o=this.horiz,r=this.left,n=this.offset,s=this.opposite,a=this.options,h=this.top,l=this.width,d=this.tickSize(),c=null==e?void 0:e.getBBox().width,p=a.title.x,u=a.title.y,f=uQ(a.title.margin,o?5:10),g=e?this.chart.renderer.fontMetrics(e).f:0,v=(o?h+i:r)+(o?1:-1)*(s?-1:1)*(d?d[0]/2:0)+(this.side===tl.bottom?g:0);t.titlePosition.x=o?r-(c||0)/2-f+p:v+(s?l:0)+n+p,t.titlePosition.y=o?v-(s?i:0)+(s?g:-g)/2+n+u:h-f+u}}function u9(){var t,e=this.chart,i=this.options.grid,o=void 0===i?{}:i,r=this.userOptions;if(o.enabled&&((t=this.options).labels.align=uQ(t.labels.align,"center"),this.categories||(t.showLastLabel=!1),this.labelRotation=0,t.labels.rotation=0,t.minTickInterval=1),o.columns)for(var n=this.grid.columns=[],s=this.grid.columnIndex=0;++s<o.columns.length;){var a=uJ(r,o.columns[s],{isInternal:!0,linkedTo:0,scrollbar:{enabled:!1}},{grid:{columns:void 0}}),h=new rA(this.chart,a,"yAxis");h.grid.isColumn=!0,h.grid.columnIndex=s,uq(e.axes,h),uq(e[this.coll]||[],h),n.push(h)}}function u4(){var t,e,i=this.axisTitle,o=this.grid,r=this.options;if(!0===(r.grid||{}).enabled){var n=this.min||0,s=this.max||0,a=this.ticks[this.tickPositions[0]];if(i&&!this.chart.styledMode&&(null==a?void 0:a.slotWidth)&&!this.options.title.style.width&&i.css({width:""+a.slotWidth+"px"}),this.maxLabelDimensions=this.getMaxLabelDimensions(this.ticks,this.tickPositions),this.rightWall&&this.rightWall.destroy(),(null===(t=this.grid)||void 0===t?void 0:t.isOuterAxis())&&this.axisLine){var h=r.lineWidth;if(h){var l=this.getLinePath(h),d=l[0],c=l[1],p=(this.tickSize("tick")||[1])[0]*(this.side===tl.top||this.side===tl.left?-1:1);if("M"===d[0]&&"L"===c[0]&&(this.horiz?(d[2]+=p,c[2]+=p):(d[1]+=p,c[1]+=p)),!this.horiz&&this.chart.marginRight){var u=["L",this.left,d[2]||0],f=[d,u],g=["L",this.chart.chartWidth-this.chart.marginRight,this.toPixels(s+this.tickmarkOffset)],v=[["M",c[1]||0,this.toPixels(s+this.tickmarkOffset)],g];this.grid.upperBorder||n%1==0||(this.grid.upperBorder=this.grid.renderBorder(f)),this.grid.upperBorder&&(this.grid.upperBorder.attr({stroke:r.lineColor,"stroke-width":r.lineWidth}),this.grid.upperBorder.animate({d:f})),this.grid.lowerBorder||s%1==0||(this.grid.lowerBorder=this.grid.renderBorder(v)),this.grid.lowerBorder&&(this.grid.lowerBorder.attr({stroke:r.lineColor,"stroke-width":r.lineWidth}),this.grid.lowerBorder.animate({d:v}))}this.grid.axisLineExtra?(this.grid.axisLineExtra.attr({stroke:r.lineColor,"stroke-width":r.lineWidth}),this.grid.axisLineExtra.animate({d:l})):this.grid.axisLineExtra=this.grid.renderBorder(l),this.axisLine[this.showAxis?"show":"hide"]()}}if(((null==o?void 0:o.columns)||[]).forEach(function(t){return t.render()}),!this.horiz&&this.chart.hasRendered&&(this.scrollbar||(null===(e=this.linkedParent)||void 0===e?void 0:e.scrollbar))&&this.tickPositions.length){for(var m=this.tickmarkOffset,y=this.tickPositions[this.tickPositions.length-1],x=this.tickPositions[0],b=void 0,M=void 0;(b=this.hiddenLabels.pop())&&b.element;)b.show();for(;(M=this.hiddenMarks.pop())&&M.element;)M.show();(b=this.ticks[x].label)&&(n-x>m?this.hiddenLabels.push(b.hide()):b.show()),(b=this.ticks[y].label)&&(y-s>m?this.hiddenLabels.push(b.hide()):b.show());var k=this.ticks[y].mark;k&&y-s<m&&y-s>0&&this.ticks[y].isLast&&this.hiddenMarks.push(k.hide())}}}function u8(){var t,e=null===(t=this.tickPositions)||void 0===t?void 0:t.info,i=this.options,o=i.grid||{},r=this.userOptions.labels||{};o.enabled&&(this.horiz?(this.series.forEach(function(t){t.options.pointRange=0}),e&&i.dateTimeLabelFormats&&i.labels&&!uV(r.align)&&(!1===i.dateTimeLabelFormats[e.unitName].range||e.count>1)&&(i.labels.align="left",uV(r.x)||(i.labels.x=3))):"treegrid"!==this.type&&this.grid&&this.grid.columns&&(this.minPointOffset=this.tickInterval))}function u7(t){var e,i=this.options,o=t.userOptions,r=i&&u2(i.grid)?i.grid:{};!0===r.enabled&&(e=uJ(!0,{className:"highcharts-grid-axis "+(o.className||""),dateTimeLabelFormats:{hour:{list:["%[HM]","%[H]"]},day:{list:["%[AeB]","%[aeb]","%[E]"]},week:{list:["Week %W","W%W"]},month:{list:["%[B]","%[b]","%o"]}},grid:{borderWidth:1},labels:{padding:2,style:{fontSize:"0.9em"}},margin:0,title:{text:null,reserveSpace:!1,rotation:0,style:{textOverflow:"ellipsis"}},units:[["millisecond",[1,10,100]],["second",[1,10]],["minute",[1,5,15]],["hour",[1,6]],["day",[1]],["week",[1]],["month",[1]],["year",null]]},o),"xAxis"!==this.coll||(uV(o.linkedTo)&&!uV(o.tickPixelInterval)&&(e.tickPixelInterval=350),!(!uV(o.tickPixelInterval)&&uV(o.linkedTo))||uV(o.tickPositioner)||uV(o.tickInterval)||uV(o.units)||(e.tickPositioner=function(t,i){var o,r,n=null===(r=null===(o=this.linkedParent)||void 0===o?void 0:o.tickPositions)||void 0===r?void 0:r.info;if(n){for(var s=e.units||[],a=void 0,h=1,l="year",d=0;d<s.length;d++){var c=s[d];if(c&&c[0]===n.unitName){a=d;break}}var p=u$(a)&&s[a+1];if(p){l=p[0]||"year";var u=p[1];h=(null==u?void 0:u[0])||1}else"year"===n.unitName&&(h=10*n.count);var f=u0[l];return this.tickInterval=f*h,this.chart.time.getTimeTicks({unitRange:f,count:h,unitName:l},t,i,this.options.startOfWeek)}})),uJ(!0,this.options,e),this.horiz&&(i.minPadding=uQ(o.minPadding,0),i.maxPadding=uQ(o.maxPadding,0)),u$(i.grid.borderWidth)&&(i.tickWidth=i.lineWidth=r.borderWidth))}function ft(t){var e=t.userOptions,i=(null==e?void 0:e.grid)||{},o=i.columns;i.enabled&&o&&uJ(!0,this.options,o[0])}function fe(){(this.grid.columns||[]).forEach(function(t){return t.setScale()})}function fi(t){var e=this.horiz,i=this.maxLabelDimensions,o=this.options.grid,r=void 0===o?{}:o;if(r.enabled&&i){var n=2*this.options.labels.distance,s=e?r.cellHeight||n+i.height:n+i.width;uK(t.tickSize)?t.tickSize[0]=s:t.tickSize=[s,0]}}function fo(){this.axes.forEach(function(t){var e;((null===(e=t.grid)||void 0===e?void 0:e.columns)||[]).forEach(function(t){t.setAxisSize(),t.setAxisTranslation()})})}function fr(t){var e=this.grid;(e.columns||[]).forEach(function(e){return e.destroy(t.keepEvents)}),e.columns=void 0}function fn(t){var e=t.userOptions||{},i=e.grid||{};i.enabled&&uV(i.borderColor)&&(e.tickColor=e.lineColor=i.borderColor),this.grid||(this.grid=new fd(this)),this.hiddenLabels=[],this.hiddenMarks=[]}function fs(t){var e=this.label,i=this.axis,o=i.reversed,r=i.chart,n=i.options.grid||{},s=i.options.labels,a=s.align,h=tl[i.side],l=t.tickmarkOffset,d=i.tickPositions,c=this.pos-l,p=u$(d[t.index+1])?d[t.index+1]-l:(i.max||0)+l,u=i.tickSize("tick"),f=u?u[0]:0,g=u?u[1]/2:0;if(!0===n.enabled){var v,m=void 0,y=void 0,x=void 0;if("top"===h?v=(m=i.top+i.offset)-f:"bottom"===h?m=(v=r.chartHeight-i.bottom+i.offset)+f:(m=i.top+i.len-(i.translate(o?p:c)||0),v=i.top+i.len-(i.translate(o?c:p)||0)),"right"===h?x=(y=r.chartWidth-i.right+i.offset)+f:"left"===h?y=(x=i.left+i.offset)-f:(y=Math.round(i.left+(i.translate(o?p:c)||0))-g,x=Math.min(Math.round(i.left+(i.translate(o?c:p)||0))-g,i.left+i.len)),this.slotWidth=x-y,t.pos.x="left"===a?y:"right"===a?x:y+(x-y)/2,t.pos.y=v+(m-v)/2,e){var b=r.renderer.fontMetrics(e),M=e.getBBox().height;if(s.useHTML)t.pos.y+=b.b+-(M/2);else{var k=Math.round(M/b.h);t.pos.y+=(b.b-(b.h-b.f))/2+-((k-1)*b.h/2)}}t.pos.x+=i.horiz&&s.x||0}}function fa(t){var e,i=t.axis,o=t.value;if(null===(e=i.options.grid)||void 0===e?void 0:e.enabled){var r=i.tickPositions,n=(i.linkedParent||i).series[0],s=o===r[0],a=o===r[r.length-1],h=n&&uZ(n.options.data,function(t){return t[i.isXAxis?"x":"y"]===o}),l=void 0;h&&n.is("gantt")&&(l=uJ(h),tu.seriesTypes.gantt.prototype.pointClass.setGanttPointAliases(l,i.chart)),t.isFirst=s,t.isLast=a,t.point=l}}function fh(){var t,e,i=this.options,o=i.grid||{},r=this.categories,n=this.tickPositions,s=n[0],a=n[1],h=n[n.length-1],l=n[n.length-2],d=null===(t=this.linkedParent)||void 0===t?void 0:t.min,c=null===(e=this.linkedParent)||void 0===e?void 0:e.max,p=d||this.min,u=c||this.max,f=this.tickInterval,g=u$(p)&&p>=s+f&&p<a,v=u$(p)&&s<p&&s+f>p,m=u$(u)&&h>u&&h-f<u,y=u$(u)&&u<=h-f&&u>l;!0===o.enabled&&!r&&(this.isXAxis||this.isLinked)&&((v||g)&&!i.startOnTick&&(n[0]=p),(m||y)&&!i.endOnTick&&(n[n.length-1]=u))}function fl(t){var e,i=this.options.grid;return!0===(void 0===i?{}:i).enabled&&this.categories?this.tickInterval:t.apply(this,(e=arguments,Array.prototype.slice.call(e,1)))}(z=tl||(tl={}))[z.top=0]="top",z[z.right=1]="right",z[z.bottom=2]="bottom",z[z.left=3]="left";var fd=function(){function t(t){this.axis=t}return t.prototype.isOuterAxis=function(){var t,e=this.axis,i=e.chart,o=e.grid.columnIndex,r=(null===(t=e.linkedParent)||void 0===t?void 0:t.grid.columns)||e.grid.columns||[],n=o?e.linkedParent:e,s=-1,a=0;return 3===e.side&&!i.inverted&&r.length?!e.linkedParent:((i[e.coll]||[]).forEach(function(t,i){t.side!==e.side||t.options.isInternal||(a=i,t!==n||(s=i))}),a===s&&(!u$(o)||r.length===o))},t.prototype.renderBorder=function(t){var e=this.axis,i=e.chart.renderer,o=e.options,r=i.path(t).addClass("highcharts-axis-line").add(e.axisGroup);return i.styledMode||r.attr({stroke:o.lineColor,"stroke-width":o.lineWidth,zIndex:7}),r},t}();u_.E=function(t){return this.dateFormat("%a",t,!0).charAt(0)},u_.W=function(t){var e=this.toParts(t),i=(e[7]+6)%7,o=e.slice(0);o[2]=e[2]-i+3;var r=this.toParts(this.makeTime(o[0],0,1));return 4!==r[7]&&(e[1]=0,e[2]=1+(11-r[7])%7),(1+Math.floor((this.makeTime(o[0],o[1],o[2])-this.makeTime(r[0],r[1],r[2]))/6048e5)).toString()};var fc=function(t,e){var i={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(i[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>e.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(i[o[r]]=t[o[r]]);return i},fp=tG.extend,fu=tG.isNumber,ff=tG.pick;function fg(t,e,i,o,r,n){var s,a,h=n&&n.after,l=n&&n.before,d={data:o,depth:i-1,id:t,level:i,parent:e||""},c=0,p=0;"function"==typeof l&&l(d,n);var u=(r[t]||[]).map(function(e){var o=fg(e.id,t,i+1,e,r,n),h=e.start||NaN,l=!0===e.milestone?h:e.end||NaN;return s=!fu(s)||h<s?h:s,a=!fu(a)||l>a?l:a,c=c+1+o.descendants,p=Math.max(o.height+1,p),o});return o&&(o.start=ff(o.start,s),o.end=ff(o.end,a)),fp(d,{children:u,descendants:c,height:p}),"function"==typeof h&&h(d,n),d}var fv={getNode:fg,getTree:function(t,e){var i,o;return fg("",null,1,null,(i=[],Object.keys(o=t.reduce(function(t,e){var o=e.parent,r=void 0===o?"":o,n=e.id;return void 0===t[r]&&(t[r]=[]),t[r].push(e),n&&i.push(n),t},{})).forEach(function(t){var e;if(""!==t&&-1===i.indexOf(t)){var r=o[t].map(function(t){return fc(t,[])});(e=o[""]).push.apply(e,r),delete o[t]}}),o),e)}},fm=tG.addEvent,fy=tG.removeEvent,fx=tG.isObject,fb=tG.isNumber,fM=tG.pick,fk=tG.wrap;function fw(){this.treeGrid||(this.treeGrid=new fT(this))}function fS(t,e,i,o,r,n,s,a,h){var l,d,c,p,u=fM(null===(l=this.options)||void 0===l?void 0:l.labels,n),f=this.pos,g=this.axis,v="treegrid"===g.type,m=t.apply(this,[e,i,o,r,u,s,a,h]);if(v){var y=u&&fx(u.symbol,!0)?u.symbol:{},x=y.width,b=y.padding,M=void 0===b?5*!g.linkedParent:b,k=u&&fb(u.indentation)?u.indentation:0;p=(null==(c=null==(d=g.treeGrid.mapOfPosToGridNode)?void 0:d[f])?void 0:c.depth)||1,m.x+=(void 0===x?0:x)+2*M+(p-1)*k}return m}function fA(t){var e,i,o,r,n,s,a,h,l,d,c,p,u,f,g,v=this.pos,m=this.axis,y=this.label,x=this.treeGrid,b=this.options,M=null==x?void 0:x.labelIcon,k=null==y?void 0:y.element,w=m.treeGrid,S=m.options,A=m.chart,T=m.tickPositions,P=w.mapOfPosToGridNode,O=fM(null==b?void 0:b.labels,null==S?void 0:S.labels),C=O&&fx(O.symbol,!0)?O.symbol:{},E=null==P?void 0:P[v],L=E||{},B=L.descendants,I=L.depth,D=E&&B&&B>0,z="treegrid"===m.type&&k,R=T.indexOf(v)>-1,N="highcharts-treegrid-node-",W=N+"level-",G=A.styledMode;(z&&E&&y.removeClass(RegExp(W+".*")).addClass(W+I),t.apply(this,Array.prototype.slice.call(arguments,1)),z&&D)?(g=w.isCollapsed(E),e={color:!G&&y.styles.color||"",collapsed:g,group:y.parentGroup,options:C,renderer:y.renderer,show:R,xy:y.xy},r=!(o=this.treeGrid).labelIcon,n=e.renderer,s=e.xy,h=(a=e.options).width||0,l=a.height||0,d=5*(null!==(i=a.padding)&&void 0!==i?!i:!this.axis.linkedParent),c={x:s.x-h/2-d,y:s.y-l/2},p=e.collapsed?90:180,u=e.show&&fb(c.y),(f=o.labelIcon)||(o.labelIcon=f=n.path(n.symbols[a.type](a.x||0,a.y||0,h,l)).addClass("highcharts-label-icon").add(e.group)),f[u?"show":"hide"](),n.styledMode||f.attr({cursor:"pointer",fill:fM(e.color,"#666666"),"stroke-width":1,stroke:a.lineColor,strokeWidth:a.lineWidth||0}),f[r?"attr":"animate"]({translateX:c.x,translateY:c.y,rotation:p}),y.addClass(N+(g?"collapsed":"expanded")).removeClass(N+(g?"expanded":"collapsed")),G||y.css({cursor:"pointer"}),[y,M].forEach(function(t){t&&!t.attachedTreeGridEvents&&(fm(t.element,"mouseover",function(){y.addClass("highcharts-treegrid-node-active"),y.renderer.styledMode||y.css({textDecoration:"underline"})}),fm(t.element,"mouseout",function(){var t;t=fx(O.style)?O.style:{},y.removeClass("highcharts-treegrid-node-active"),y.renderer.styledMode||y.css({textDecoration:t.textDecoration||"none"})}),fm(t.element,"click",function(){x.toggleCollapse()}),t.attachedTreeGridEvents=!0)})):M&&(fy(k),null==y||y.css({cursor:"default"}),M.destroy())}var fT=function(){function t(t){this.tick=t}return t.compose=function(t){var e=t.prototype;e.toggleCollapse||(fm(t,"init",fw),fk(e,"getLabelPosition",fS),fk(e,"renderLabel",fA),e.collapse=function(t){this.treeGrid.collapse(t)},e.expand=function(t){this.treeGrid.expand(t)},e.toggleCollapse=function(t){this.treeGrid.toggleCollapse(t)})},t.prototype.collapse=function(t){var e=this.tick,i=e.axis,o=i.brokenAxis;if(o&&i.treeGrid.mapOfPosToGridNode){var r=e.pos,n=i.treeGrid.mapOfPosToGridNode[r],s=i.treeGrid.collapse(n);o.setBreaks(s,fM(t,!0))}},t.prototype.destroy=function(){this.labelIcon&&this.labelIcon.destroy()},t.prototype.expand=function(t){var e=this.tick,i=e.pos,o=e.axis,r=o.treeGrid,n=o.brokenAxis,s=r.mapOfPosToGridNode;if(n&&s){var a=s[i],h=r.expand(a);n.setBreaks(h,fM(t,!0))}},t.prototype.toggleCollapse=function(t){var e=this.tick,i=e.axis,o=i.brokenAxis;if(o&&i.treeGrid.mapOfPosToGridNode){var r=e.pos,n=i.treeGrid.mapOfPosToGridNode[r],s=i.treeGrid.toggleCollapse(n);o.setBreaks(s,fM(t,!0))}},t}(),fP=(tG.extend,tG.isArray),fO=tG.isNumber,fC=tG.isObject,fE=tG.merge,fL=tG.pick,fB=(tG.relativeLength,function(t){var e,i,o,r,n,s,a={};if(fC(t))for(r=fO(t.from)?t.from:1,s=t.levels,i={},e=fC(t.defaults)?t.defaults:{},fP(s)&&(i=s.reduce(function(t,i){var o,n,s;return fC(i)&&fO(i.level)&&(n=fL((s=fE({},i)).levelIsConstant,e.levelIsConstant),delete s.levelIsConstant,delete s.level,fC(t[o=i.level+(n?0:r-1)])?fE(!0,t[o],s):t[o]=s),t},{})),n=fO(t.to)?t.to:1,o=0;o<=n;o++)a[o]=fE({},e,fC(i[o])?i[o]:{});return a}),fI=tG.addEvent,fD=tG.isArray,fz=tG.splat,fR=tG.find,fN=tG.fireEvent,fW=tG.isObject,fG=tG.isString,fX=tG.merge,fF=tG.pick,fH=tG.removeEvent,fj=tG.wrap;function fY(t,e){var i=t.collapseEnd||0,o=t.collapseStart||0;return i>=e&&(o-=.5),{from:o,to:i,showPoints:!1}}function f_(t,e,i){var o,r,n=[],s=[],a={},h=e||!1,l={},d=-1,c=fv.getTree(t,{after:function(t){var e=l[t.pos],i=0,o=0;e.children.forEach(function(t){o+=(t.descendants||0)+1,i=Math.max((t.height||0)+1,i)}),e.descendants=o,e.height=i,e.collapsed&&s.push(e)},before:function(t){var e,i,o=fW(t.data,!0)?t.data:{},r=fG(o.name)?o.name:"",s=a[t.parent],c=fW(s,!0)?l[s.pos]:null;h&&fW(c,!0)&&(e=fR(c.children,function(t){return t.name===r}))?(i=e.pos,e.nodes.push(t)):i=d++,!l[i]&&(l[i]=e={depth:c?c.depth+1:0,name:r,id:o.id,nodes:[t],children:[],pos:i},-1!==i&&n.push(r),fW(c,!0)&&c.children.push(e)),fG(t.id)&&(a[t.id]=t),e&&!0===o.collapsed&&(e.collapsed=!0),t.pos=i}});return o=l,{categories:n,mapOfIdToNode:a,mapOfPosToGridNode:l=(r=function(t,e,o){var n=t.nodes,s=e+(-1===e?0:i-1),a=(s-e)/2,h=e+a;return n.forEach(function(t){var i=t.data;fW(i,!0)&&(i.y=e+(i.seriesIndex||0),delete i.seriesIndex),t.pos=h}),o[h]=t,t.pos=h,t.tickmarkOffset=a+.5,t.collapseStart=s+.5,t.children.forEach(function(t){r(t,s+1,o),s=(t.collapseEnd||0)-.5}),t.collapseEnd=s+.5,o})(o["-1"],-1,{}),collapsedNodes:s,tree:c}}function fU(t){var e=t.target;e.axes.filter(function(t){return"treegrid"===t.type}).forEach(function(i){var o,r,n,s=i.options||{},a=s.labels,h=i.uniqueNames,l=e.time.parse(s.max),d=!i.treeGrid.mapOfPosToGridNode||i.series.some(function(t){return!t.hasRendered||t.isDirtyData||t.isDirty}),c=0;if(d){var p=[];if(r=i.series.reduce(function(t,i){var o=i.options.data||[],r=o[0],n=Array.isArray(r)&&!r.find(function(t){return"object"==typeof t});return p.push(n),i.visible&&(o.forEach(function(o){var r;(n||(null===(r=i.options.keys)||void 0===r?void 0:r.length))&&(o=i.pointClass.prototype.optionsToObject.call({series:i},o),i.pointClass.setGanttPointAliases(o,e)),fW(o,!0)&&(o.seriesIndex=c,t.push(o))}),!0===h&&c++),t},[]),l&&r.length<l)for(var u=r.length;u<=l;u++)r.push({name:u+"​"});i.categories=(n=f_(r,h||!1,!0===h?c:1)).categories,i.treeGrid.mapOfPosToGridNode=n.mapOfPosToGridNode,i.hasNames=!0,i.treeGrid.tree=n.tree,i.series.forEach(function(t,e){var i=(t.options.data||[]).map(function(i){return(p[e]||fD(i)&&t.options.keys&&t.options.keys.length)&&r.forEach(function(t){var e=fz(i);e.indexOf(t.x||0)>=0&&e.indexOf(t.x2||0)>=0&&(i=t)}),fW(i,!0)?fX(i):i});t.visible&&t.setData(i,!1)}),i.treeGrid.mapOptionsToLevel=fB({defaults:a,from:1,levels:null==a?void 0:a.levels,to:null===(o=i.treeGrid.tree)||void 0===o?void 0:o.height}),"beforeRender"===t.type&&(i.treeGrid.collapsedNodes=n.collapsedNodes)}})}function fV(t,e){var i,o,r,n=this.treeGrid.mapOptionsToLevel||{},s="treegrid"===this.type,a=this.ticks,h=a[e];s&&this.treeGrid.mapOfPosToGridNode?((i=n[(r=this.treeGrid.mapOfPosToGridNode[e]).depth])&&(o={labels:i}),!h&&td?a[e]=h=new td(this,e,void 0,void 0,{category:r.name,tickmarkOffset:r.tickmarkOffset,options:o}):(h.parameters.category=r.name,h.options=o,h.addLabel())):t.apply(this,Array.prototype.slice.call(arguments,1))}function fq(t,e,i,o){var r=this,n="treegrid"===i.type;r.treeGrid||(r.treeGrid=new f$(r)),n&&(fI(e,"beforeRender",fU),fI(e,"beforeRedraw",fU),fI(e,"addSeries",function(t){if(t.options.data){var e=f_(t.options.data,i.uniqueNames||!1,1);r.treeGrid.collapsedNodes=(r.treeGrid.collapsedNodes||[]).concat(e.collapsedNodes)}}),fI(r,"foundExtremes",function(){r.treeGrid.collapsedNodes&&r.treeGrid.collapsedNodes.forEach(function(t){var e=r.treeGrid.collapse(t);r.brokenAxis&&(r.brokenAxis.setBreaks(e,!1),r.treeGrid.collapsedNodes&&(r.treeGrid.collapsedNodes=r.treeGrid.collapsedNodes.filter(function(e){return t.collapseStart!==e.collapseStart||t.collapseEnd!==e.collapseEnd})))})}),fI(r,"afterBreaks",function(){"yAxis"===r.coll&&!r.staticScale&&r.chart.options.chart.height&&(r.isDirty=!0)}),i=fX({grid:{enabled:!0},labels:{align:"left",levels:[{level:void 0},{level:1,style:{fontWeight:"bold"}}],symbol:{type:"triangle",x:-5,y:-5,height:10,width:10}},uniqueNames:!1},i,{reversed:!0})),t.apply(r,[e,i,o]),n&&(r.hasNames=!0,r.options.showLastLabel=!0)}function fZ(t){var e,i,o,r,n,s=this.options,a=this.chart.time,h="number"==typeof s.linkedTo?null===(e=this.chart[this.coll])||void 0===e?void 0:e[s.linkedTo]:void 0;if("treegrid"===this.type){if(this.min=null!==(o=null!==(i=this.userMin)&&void 0!==i?i:a.parse(s.min))&&void 0!==o?o:this.dataMin,this.max=null!==(n=null!==(r=this.userMax)&&void 0!==r?r:a.parse(s.max))&&void 0!==n?n:this.dataMax,fN(this,"foundExtremes"),this.setAxisTranslation(),this.tickInterval=1,this.tickmarkOffset=.5,this.tickPositions=this.treeGrid.mapOfPosToGridNode?this.treeGrid.getTickPositions():[],h){var l=h.getExtremes();this.min=fF(l.min,l.dataMin),this.max=fF(l.max,l.dataMax),this.tickPositions=h.tickPositions}this.linkedParent=h}else t.apply(this,Array.prototype.slice.call(arguments,1))}function fK(t){var e=this;"treegrid"===this.type&&e.visible&&e.tickPositions.forEach(function(t){var i,o=e.ticks[t];(null===(i=o.label)||void 0===i?void 0:i.attachedTreeGridEvents)&&(fH(o.label.element),o.label.attachedTreeGridEvents=!1)}),t.apply(e,Array.prototype.slice.call(arguments,1))}var f$=function(){function t(t){this.axis=t}return t.compose=function(t,e,i,o){if(!t.keepProps.includes("treeGrid")){var r,n,s,a=t.prototype;t.keepProps.push("treeGrid"),fj(a,"generateTick",fV),fj(a,"init",fq),fj(a,"setTickInterval",fZ),fj(a,"redraw",fK),a.utils={getNode:fv.getNode},td||(td=o)}return r=t,n=e,s=o,r.keepProps.includes("grid")||(r.keepProps.push("grid"),r.prototype.getMaxLabelDimensions=u3,u1(r.prototype,"unsquish",fl),u1(r.prototype,"getOffset",u5),uU(r,"init",fn),uU(r,"afterGetTitlePosition",u6),uU(r,"afterInit",u9),uU(r,"afterRender",u4),uU(r,"afterSetAxisTranslation",u8),uU(r,"afterSetOptions",u7),uU(r,"afterSetOptions",ft),uU(r,"afterSetScale",fe),uU(r,"afterTickSize",fi),uU(r,"trimTicks",fh),uU(r,"destroy",fr),uU(n,"afterSetChartSize",fo),uU(s,"afterGetLabelPosition",fs),uU(s,"labelFormat",fa)),uY.compose(t,i),fT.compose(o),t},t.prototype.setCollapsedStatus=function(t){var e=this.axis,i=e.chart;e.series.forEach(function(e){var o=e.options.data;if(t.id&&o){var r=i.get(t.id),n=o[e.data.indexOf(r)];r&&n&&(r.collapsed=t.collapsed,n.collapsed=t.collapsed)}})},t.prototype.collapse=function(t){var e=this.axis,i=e.options.breaks||[],o=fY(t,e.max);return i.push(o),t.collapsed=!0,e.treeGrid.setCollapsedStatus(t),i},t.prototype.expand=function(t){var e=this.axis,i=e.options.breaks||[],o=fY(t,e.max);return t.collapsed=!1,e.treeGrid.setCollapsedStatus(t),i.reduce(function(t,e){return(e.to!==o.to||e.from!==o.from)&&t.push(e),t},[])},t.prototype.getTickPositions=function(){var t=this.axis,e=Math.floor(t.min/t.tickInterval)*t.tickInterval,i=Math.ceil(t.max/t.tickInterval)*t.tickInterval;return Object.keys(t.treeGrid.mapOfPosToGridNode||{}).reduce(function(o,r){var n,s=+r;return s>=e&&s<=i&&!(null===(n=t.brokenAxis)||void 0===n?void 0:n.isInAnyBreak(s))&&o.push(s),o},[])},t.prototype.isCollapsed=function(t){var e=this.axis,i=e.options.breaks||[],o=fY(t,e.max);return i.some(function(t){return t.from===o.from&&t.to===o.to})},t.prototype.toggleCollapse=function(t){return this.isCollapsed(t)?this.expand(t):this.collapse(t)},t}(),fJ=(R=function(t,e){return(R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}R(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),fQ=si.series,f0=si.seriesTypes.xrange,f1=tG.extend,f2=tG.isNumber,f3=tG.merge,f5=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return fJ(e,t),e.compose=function(t,e,i,o){if(f0.compose(t),e&&(us(t,e),i))p7.compose(e,i.prototype.pointClass),o&&f$.compose(t,e,i,o)},e.prototype.drawPoint=function(e,i){var o,r=this.options,n=this.chart.renderer,s=e.shapeArgs,a=e.plotY,h=e.selected&&"select",l=r.stacking&&!r.borderRadius,d=e.graphic;e.options.milestone?f2(a)&&null!==e.y&&!1!==e.visible?(o=n.symbols.diamond(s.x||0,s.y||0,s.width||0,s.height||0),d?d[i]({d:o}):e.graphic=d=n.path(o).addClass(e.getClassName(),!0).add(e.group||this.group),this.chart.styledMode||e.graphic.attr(this.pointAttribs(e,h)).shadow(r.shadow,null,l)):d&&(e.graphic=d.destroy()):t.prototype.drawPoint.call(this,e,i)},e.prototype.translatePoint=function(e){var i,o;t.prototype.translatePoint.call(this,e),e.options.milestone&&(o=(i=e.shapeArgs).height||0,e.shapeArgs={x:(i.x||0)-o/2,y:i.y,width:o,height:o})},e.defaultOptions=f3(f0.defaultOptions,uN),e}(f0);f1(f5.prototype,{pointArrayMap:["start","end","y"],pointClass:uz,setData:fQ.prototype.setData}),si.registerSeriesType("gantt",f5),tu.Connection=tu.Connection||dE,tu.GanttChart=tu.GanttChart||d_,tu.Navigator=tu.Navigator||c1,tu.RangeSelector=tu.RangeSelector||pG,tu.Scrollbar=tu.Scrollbar||cz,tu.ganttChart=tu.GanttChart.ganttChart,dk(tu.SVGRenderer),({compose:function(t,e){dD(dL,"CurrentDateIndication")&&(dB(t,"afterSetOptions",dN),dB(e,"render",dW),dz(e.prototype,"getLabelText",dG))}}).compose(tu.Axis,tu.PlotLineOrBand),f5.compose(tu.Axis,tu.Chart,tu.Series,tu.Tick),tu.Navigator.compose(tu.Chart,tu.Axis,tu.Series),tu.RangeSelector.compose(tu.Axis,tu.Chart),tu.Scrollbar.compose(tu.Axis),tu.product="Highcharts Gantt";var f6=tu;return tp.default}()});