{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/themes/high-contrast-dark\n * @requires highcharts\n *\n * (c) 2009-2025 Highsoft AS\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/high-contrast-dark\", [[\"highcharts/highcharts\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/high-contrast-dark\"] = factory(require(\"highcharts\"));\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ high_contrast_dark_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/Themes/HighContrastDark.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  Accessible high-contrast dark theme for Highcharts. Specifically tailored\n *  towards 3:1 contrast against black/off-black backgrounds. Neighboring\n *  colors are tested for color blindness.\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n/* *\n *\n *  Theme\n *\n * */\nvar HighContrastDarkTheme;\n(function (HighContrastDarkTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    var textBright = '#F0F0F3';\n    HighContrastDarkTheme.options = {\n        colors: [\n            '#67B9EE',\n            '#CEEDA5',\n            '#9F6AE1',\n            '#FEA26E',\n            '#6BA48F',\n            '#EA3535',\n            '#8D96B7',\n            '#ECCA15',\n            '#20AA09',\n            '#E0C3E4'\n        ],\n        chart: {\n            backgroundColor: '#1f1f20',\n            plotBorderColor: '#606063'\n        },\n        title: {\n            style: {\n                color: textBright\n            }\n        },\n        subtitle: {\n            style: {\n                color: textBright\n            }\n        },\n        xAxis: {\n            gridLineColor: '#707073',\n            labels: {\n                style: {\n                    color: textBright\n                }\n            },\n            lineColor: '#707073',\n            minorGridLineColor: '#505053',\n            tickColor: '#707073',\n            title: {\n                style: {\n                    color: textBright\n                }\n            }\n        },\n        yAxis: {\n            gridLineColor: '#707073',\n            labels: {\n                style: {\n                    color: textBright\n                }\n            },\n            lineColor: '#707073',\n            minorGridLineColor: '#505053',\n            tickColor: '#707073',\n            title: {\n                style: {\n                    color: textBright\n                }\n            }\n        },\n        tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            style: {\n                color: textBright\n            }\n        },\n        plotOptions: {\n            series: {\n                dataLabels: {\n                    color: textBright\n                },\n                marker: {\n                    lineColor: '#333'\n                }\n            },\n            boxplot: {\n                fillColor: '#505053'\n            },\n            candlestick: {\n                lineColor: 'white'\n            },\n            errorbar: {\n                color: 'white'\n            },\n            map: {\n                nullColor: '#353535'\n            }\n        },\n        legend: {\n            backgroundColor: 'transparent',\n            itemStyle: {\n                color: textBright\n            },\n            itemHoverStyle: {\n                color: '#FFF'\n            },\n            itemHiddenStyle: {\n                color: '#606063'\n            },\n            title: {\n                style: {\n                    color: '#D0D0D0'\n                }\n            }\n        },\n        credits: {\n            style: {\n                color: textBright\n            }\n        },\n        drilldown: {\n            activeAxisLabelStyle: {\n                color: textBright\n            },\n            activeDataLabelStyle: {\n                color: textBright\n            }\n        },\n        navigation: {\n            buttonOptions: {\n                symbolStroke: '#DDDDDD',\n                theme: {\n                    fill: '#505053'\n                }\n            }\n        },\n        rangeSelector: {\n            buttonTheme: {\n                fill: '#505053',\n                stroke: '#000000',\n                style: {\n                    color: '#eee'\n                },\n                states: {\n                    hover: {\n                        fill: '#707073',\n                        stroke: '#000000',\n                        style: {\n                            color: textBright\n                        }\n                    },\n                    select: {\n                        fill: '#303030',\n                        stroke: '#101010',\n                        style: {\n                            color: textBright\n                        }\n                    }\n                }\n            },\n            inputBoxBorderColor: '#505053',\n            inputStyle: {\n                backgroundColor: '#333',\n                color: textBright\n            },\n            labelStyle: {\n                color: textBright\n            }\n        },\n        navigator: {\n            handles: {\n                backgroundColor: '#666',\n                borderColor: '#AAA'\n            },\n            outlineColor: '#CCC',\n            maskFill: 'rgba(180,180,255,0.2)',\n            series: {\n                color: '#7798BF',\n                lineColor: '#A6C7ED'\n            },\n            xAxis: {\n                gridLineColor: '#505053'\n            }\n        },\n        scrollbar: {\n            barBackgroundColor: '#808083',\n            barBorderColor: '#808083',\n            buttonArrowColor: '#CCC',\n            buttonBackgroundColor: '#606063',\n            buttonBorderColor: '#606063',\n            rifleColor: '#FFF',\n            trackBackgroundColor: '#404043',\n            trackBorderColor: '#404043'\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        setOptions(HighContrastDarkTheme.options);\n    }\n    HighContrastDarkTheme.apply = apply;\n})(HighContrastDarkTheme || (HighContrastDarkTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var HighContrastDark = (HighContrastDarkTheme);\n\n;// ./code/es5/es-modules/masters/themes/high-contrast-dark.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = HighContrastDark.options;\nHighContrastDark.apply();\n/* harmony default export */ var high_contrast_dark_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "HighContrastDarkTheme", "textBright", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "high_contrast_dark_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "options", "colors", "chart", "backgroundColor", "plotBorderColor", "title", "style", "color", "subtitle", "xAxis", "gridLineColor", "labels", "lineColor", "minorGridLineColor", "tickColor", "yAxis", "tooltip", "plotOptions", "series", "dataLabels", "marker", "boxplot", "fillColor", "candlestick", "errorbar", "map", "nullColor", "legend", "itemStyle", "itemHoverStyle", "itemHiddenStyle", "credits", "drilldown", "activeAxisLabelStyle", "activeDataLabelStyle", "navigation", "buttonOptions", "symbolStroke", "theme", "fill", "rangeSelector", "buttonTheme", "stroke", "states", "hover", "select", "inputBoxBorderColor", "inputStyle", "labelStyle", "navigator", "handles", "borderColor", "outlineColor", "maskFill", "scrollbar", "barBackgroundColor", "barBorderColor", "buttonArrowColor", "buttonBackgroundColor", "buttonBorderColor", "rifleColor", "trackBackgroundColor", "trackBorderColor", "apply", "HighContrastDark"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,eAC1B,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,uCAAwC,CAAC,CAAC,wBAAwB,CAAC,CAAEJ,GACrE,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,uCAAuC,CAAGD,EAAQG,QAAQ,eAElEJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,IAAI,CAAE,SAASO,CAAgC,EAClD,OAAgB,AAAC,WACP,aACA,IAqGCC,EAMHC,EAPJD,EApGUE,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAwB,CACvE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAmBjHE,EAAa,AAACD,IAA+EC,UAAU,CAanG5B,EAAa,UACjBD,CAPOA,EA2MRA,GAA0BA,CAAAA,EAAwB,CAAC,CAAA,GApM5B8B,OAAO,CAAG,CAC5BC,OAAQ,CACJ,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACH,CACDC,MAAO,CACHC,gBAAiB,UACjBC,gBAAiB,SACrB,EACAC,MAAO,CACHC,MAAO,CACHC,MAAOpC,CACX,CACJ,EACAqC,SAAU,CACNF,MAAO,CACHC,MAAOpC,CACX,CACJ,EACAsC,MAAO,CACHC,cAAe,UACfC,OAAQ,CACJL,MAAO,CACHC,MAAOpC,CACX,CACJ,EACAyC,UAAW,UACXC,mBAAoB,UACpBC,UAAW,UACXT,MAAO,CACHC,MAAO,CACHC,MAAOpC,CACX,CACJ,CACJ,EACA4C,MAAO,CACHL,cAAe,UACfC,OAAQ,CACJL,MAAO,CACHC,MAAOpC,CACX,CACJ,EACAyC,UAAW,UACXC,mBAAoB,UACpBC,UAAW,UACXT,MAAO,CACHC,MAAO,CACHC,MAAOpC,CACX,CACJ,CACJ,EACA6C,QAAS,CACLb,gBAAiB,sBACjBG,MAAO,CACHC,MAAOpC,CACX,CACJ,EACA8C,YAAa,CACTC,OAAQ,CACJC,WAAY,CACRZ,MAAOpC,CACX,EACAiD,OAAQ,CACJR,UAAW,MACf,CACJ,EACAS,QAAS,CACLC,UAAW,SACf,EACAC,YAAa,CACTX,UAAW,OACf,EACAY,SAAU,CACNjB,MAAO,OACX,EACAkB,IAAK,CACDC,UAAW,SACf,CACJ,EACAC,OAAQ,CACJxB,gBAAiB,cACjByB,UAAW,CACPrB,MAAOpC,CACX,EACA0D,eAAgB,CACZtB,MAAO,MACX,EACAuB,gBAAiB,CACbvB,MAAO,SACX,EACAF,MAAO,CACHC,MAAO,CACHC,MAAO,SACX,CACJ,CACJ,EACAwB,QAAS,CACLzB,MAAO,CACHC,MAAOpC,CACX,CACJ,EACA6D,UAAW,CACPC,qBAAsB,CAClB1B,MAAOpC,CACX,EACA+D,qBAAsB,CAClB3B,MAAOpC,CACX,CACJ,EACAgE,WAAY,CACRC,cAAe,CACXC,aAAc,UACdC,MAAO,CACHC,KAAM,SACV,CACJ,CACJ,EACAC,cAAe,CACXC,YAAa,CACTF,KAAM,UACNG,OAAQ,UACRpC,MAAO,CACHC,MAAO,MACX,EACAoC,OAAQ,CACJC,MAAO,CACHL,KAAM,UACNG,OAAQ,UACRpC,MAAO,CACHC,MAAOpC,CACX,CACJ,EACA0E,OAAQ,CACJN,KAAM,UACNG,OAAQ,UACRpC,MAAO,CACHC,MAAOpC,CACX,CACJ,CACJ,CACJ,EACA2E,oBAAqB,UACrBC,WAAY,CACR5C,gBAAiB,OACjBI,MAAOpC,CACX,EACA6E,WAAY,CACRzC,MAAOpC,CACX,CACJ,EACA8E,UAAW,CACPC,QAAS,CACL/C,gBAAiB,OACjBgD,YAAa,MACjB,EACAC,aAAc,OACdC,SAAU,wBACVnC,OAAQ,CACJX,MAAO,UACPK,UAAW,SACf,EACAH,MAAO,CACHC,cAAe,SACnB,CACJ,EACA4C,UAAW,CACPC,mBAAoB,UACpBC,eAAgB,UAChBC,iBAAkB,OAClBC,sBAAuB,UACvBC,kBAAmB,UACnBC,WAAY,OACZC,qBAAsB,UACtBC,iBAAkB,SACtB,CACJ,EAYA5F,EAAsB6F,KAAK,CAH3B,WACIhE,EAAW7B,EAAsB8B,OAAO,CAC5C,EAQyB,IAAIgE,EAAoB9F,CAOrD,CAAC4B,IAA+EwC,KAAK,CAAG0B,EAAiBhE,OAAO,CAChHgE,EAAiBD,KAAK,GACO,IAAInE,EAA2BE,IAGlD,OADYH,EAAoB,OAAU,AAE3C,GAET"}