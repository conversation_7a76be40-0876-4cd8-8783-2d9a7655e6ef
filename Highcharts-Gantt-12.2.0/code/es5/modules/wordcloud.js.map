{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/wordcloud\n * @requires highcharts\n *\n * (c) 2016-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/wordcloud\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/wordcloud\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ wordcloud_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    var animatableAttribs = params.animatableAttribs,\n        onComplete = params.onComplete,\n        css = params.css,\n        renderer = params.renderer;\n    var animation = (point.series && point.series.chart.hasRendered) ?\n            // Chart-level animation on updates\n            void 0 :\n            // Series-level animation on new points\n            (point.series &&\n                point.series.options.animation);\n    var graphic = point.graphic;\n    params.attribs = __assign(__assign({}, params.attribs), { 'class': point.getClassName() }) || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        var destroy_1 = function () {\n                point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, function () { return destroy_1(); });\n        }\n        else {\n            destroy_1();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DrawPointUtilities = {\n    draw: draw\n};\n/* harmony default export */ var Series_DrawPointUtilities = (DrawPointUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Wordcloud/WordcloudPoint.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/* *\n *\n *  Class\n *\n * */\nvar WordcloudPoint = /** @class */ (function (_super) {\n    __extends(WordcloudPoint, _super);\n    function WordcloudPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    WordcloudPoint.prototype.isValid = function () {\n        return true;\n    };\n    return WordcloudPoint;\n}(ColumnPoint));\nextend(WordcloudPoint.prototype, {\n    weight: 1\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Wordcloud_WordcloudPoint = (WordcloudPoint);\n\n;// ./code/es5/es-modules/Series/Wordcloud/WordcloudSeriesDefaults.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A word cloud is a visualization of a set of words, where the size and\n * placement of a word is determined by how it is weighted.\n *\n * @sample highcharts/demo/wordcloud Word Cloud chart\n *\n * @extends      plotOptions.column\n * @excluding    allAreas, boostThreshold, clip, colorAxis, compare,\n *               compareBase, crisp, cropThreshold, dataGrouping,\n *               dataLabels, depth, dragDrop, edgeColor, findNearestPointBy,\n *               getExtremesFromAll, grouping, groupPadding, groupZPadding,\n *               joinBy, maxPointWidth, minPointLength, navigatorOptions,\n *               negativeColor, pointInterval, pointIntervalUnit,\n *               pointPadding, pointPlacement, pointRange, pointStart,\n *               pointWidth, pointStart, pointWidth, shadow, showCheckbox,\n *               showInNavigator, softThreshold, stacking, threshold,\n *               zoneAxis, zones, dataSorting, boostBlending\n * @product      highcharts\n * @since        6.0.0\n * @requires     modules/wordcloud\n * @optionparent plotOptions.wordcloud\n */\nvar WordcloudSeriesDefaults = {\n    /**\n     * If there is no space for a word on the playing field, then this\n     * option will allow the playing field to be extended to fit the word.\n     * If false then the word will be dropped from the visualization.\n     *\n     * NB! This option is currently not decided to be published in the API,\n     * and is therefore marked as private.\n     *\n     * @ignore-option\n     */\n    allowExtendPlayingField: true,\n    animation: {\n        /** @internal */\n        duration: 500\n    },\n    borderWidth: 0,\n    /**\n     * @ignore-option\n     */\n    clip: false, // Something goes wrong with clip. // @todo fix this\n    colorByPoint: true,\n    cropThreshold: Infinity,\n    /**\n     * A threshold determining the minimum font size that can be applied to\n     * a word.\n     */\n    minFontSize: 1,\n    /**\n     * The word with the largest weight will have a font size equal to this\n     * value. The font size of a word is the ratio between its weight and\n     * the largest occuring weight, multiplied with the value of\n     * maxFontSize.\n     */\n    maxFontSize: 25,\n    /**\n     * This option decides which algorithm is used for placement, and\n     * rotation of a word. The choice of algorith is therefore a crucial\n     * part of the resulting layout of the wordcloud. It is possible for\n     * users to add their own custom placement strategies for use in word\n     * cloud. Read more about it in our\n     * [documentation](https://www.highcharts.com/docs/chart-and-series-types/word-cloud-series#custom-placement-strategies)\n     *\n     * @validvalue [\"center\", \"random\"]\n     */\n    placementStrategy: 'center',\n    /**\n     * Rotation options for the words in the wordcloud.\n     *\n     * @sample highcharts/plotoptions/wordcloud-rotation\n     *         Word cloud with rotation\n     */\n    rotation: {\n        /**\n         * The smallest degree of rotation for a word.\n         */\n        from: 0,\n        /**\n         * The number of possible orientations for a word, within the range\n         * of `rotation.from` and `rotation.to`. Must be a number larger\n         * than 0.\n         */\n        orientations: 2,\n        /**\n         * The largest degree of rotation for a word.\n         */\n        to: 90\n    },\n    showInLegend: false,\n    /**\n     * Spiral used for placing a word after the initial position\n     * experienced a collision with either another word or the borders.\n     * It is possible for users to add their own custom spiralling\n     * algorithms for use in word cloud. Read more about it in our\n     * [documentation](https://www.highcharts.com/docs/chart-and-series-types/word-cloud-series#custom-spiralling-algorithm)\n     *\n     * @validvalue [\"archimedean\", \"rectangular\", \"square\"]\n     */\n    spiral: 'rectangular',\n    /**\n     * CSS styles for the words.\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"fontFamily\":\"sans-serif\", \"fontWeight\": \"900\"}\n     */\n    style: {\n        /** @ignore-option */\n        fontFamily: 'sans-serif',\n        /** @ignore-option */\n        fontWeight: '900',\n        /** @ignore-option */\n        whiteSpace: 'nowrap'\n    },\n    tooltip: {\n        followPointer: true,\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}: <b>{point.weight}</b><br/>'\n    }\n};\n/**\n * A `wordcloud` series. If the [type](#series.wordcloud.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.wordcloud\n * @exclude   dataSorting, boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/wordcloud\n * @apioption series.wordcloud\n */\n/**\n * An array of data points for the series. For the `wordcloud` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 2 values. In this case, the values correspond to\n *    `name,weight`.\n *    ```js\n *    data: [\n *        ['Lorem', 4],\n *        ['Ipsum', 1]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.arearange.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        name: \"Lorem\",\n *        weight: 4\n *    }, {\n *        name: \"Ipsum\",\n *        weight: 1\n *    }]\n *    ```\n *\n * @type      {Array<Array<string,number>|*>}\n * @extends   series.line.data\n * @excluding drilldown, marker, x, y\n * @product   highcharts\n * @apioption series.wordcloud.data\n */\n/**\n * The name decides the text for a word.\n *\n * @type      {string}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.wordcloud.data.name\n */\n/**\n * The weighting of a word. The weight decides the relative size of a word\n * compared to the rest of the collection.\n *\n * @type      {number}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.wordcloud.data.weight\n */\n''; // Detach doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Wordcloud_WordcloudSeriesDefaults = (WordcloudSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Wordcloud/WordcloudUtils.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\n\nvar deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\nvar WordcloudUtils_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n * Functions\n *\n * */\n/**\n * Detects if there is a collision between two rectangles.\n *\n * @private\n * @function isRectanglesIntersecting\n *\n * @param {Highcharts.PolygonBoxObject} r1\n * First rectangle.\n *\n * @param {Highcharts.PolygonBoxObject} r2\n * Second rectangle.\n *\n * @return {boolean}\n * Returns true if the rectangles overlap.\n */\nfunction isRectanglesIntersecting(r1, r2) {\n    return !(r2.left > r1.right ||\n        r2.right < r1.left ||\n        r2.top > r1.bottom ||\n        r2.bottom < r1.top);\n}\n/**\n * Calculates the normals to a line between two points.\n *\n * @private\n * @function getNormals\n * @param {Highcharts.PolygonPointObject} p1\n *        Start point for the line. Array of x and y value.\n * @param {Highcharts.PolygonPointObject} p2\n *        End point for the line. Array of x and y value.\n * @return {Highcharts.PolygonObject}\n *         Returns the two normals in an array.\n */\nfunction getNormals(p1, p2) {\n    var dx = p2[0] - p1[0], // X2 - x1\n        dy = p2[1] - p1[1]; // Y2 - y1\n        return [\n            [-dy,\n        dx],\n            [dy, -dx]\n        ];\n}\n/**\n * @private\n */\nfunction getAxesFromPolygon(polygon) {\n    var points,\n        axes = polygon.axes || [];\n    if (!axes.length) {\n        axes = [];\n        points = points = polygon.concat([polygon[0]]);\n        points.reduce(function (p1, p2) {\n            var normals = getNormals(p1,\n                p2),\n                axis = normals[0]; // Use the left normal as axis.\n                // Check that the axis is unique.\n                if (!find(axes,\n                function (existing) {\n                    return existing[0] === axis[0] &&\n                        existing[1] === axis[1];\n            })) {\n                axes.push(axis);\n            }\n            // Return p2 to be used as p1 in next iteration.\n            return p2;\n        });\n        polygon.axes = axes;\n    }\n    return axes;\n}\n/**\n * Projects a polygon onto a coordinate.\n *\n * @private\n * @function project\n * @param {Highcharts.PolygonObject} polygon\n * Array of points in a polygon.\n * @param {Highcharts.PolygonPointObject} target\n * The coordinate of pr\n */\nfunction project(polygon, target) {\n    var products = polygon.map(function (point) {\n            var ax = point[0],\n        ay = point[1],\n        bx = target[0],\n        by = target[1];\n        return ax * bx + ay * by;\n    });\n    return {\n        min: Math.min.apply(this, products),\n        max: Math.max.apply(this, products)\n    };\n}\n/**\n * @private\n */\nfunction isPolygonsOverlappingOnAxis(axis, polygon1, polygon2) {\n    var projection1 = project(polygon1,\n        axis),\n        projection2 = project(polygon2,\n        axis),\n        isOverlapping = !(projection2.min > projection1.max ||\n            projection2.max < projection1.min);\n    return !isOverlapping;\n}\n/**\n * Checks whether two convex polygons are colliding by using the Separating\n * Axis Theorem.\n *\n * @private\n * @function isPolygonsColliding\n * @param {Highcharts.PolygonObject} polygon1\n *        First polygon.\n *\n * @param {Highcharts.PolygonObject} polygon2\n *        Second polygon.\n *\n * @return {boolean}\n *         Returns true if they are colliding, otherwise false.\n */\nfunction isPolygonsColliding(polygon1, polygon2) {\n    // Get the axis from both polygons.\n    var axes1 = getAxesFromPolygon(polygon1),\n        axes2 = getAxesFromPolygon(polygon2),\n        axes = axes1.concat(axes2),\n        overlappingOnAllAxes = !find(axes,\n        function (axis) { return isPolygonsOverlappingOnAxis(axis,\n        polygon1,\n        polygon2); });\n    return overlappingOnAllAxes;\n}\n/**\n * Detects if a word collides with any previously placed words.\n *\n * @private\n * @function intersectsAnyWord\n *\n * @param {Highcharts.Point} point\n * Point which the word is connected to.\n *\n * @param {Array<Highcharts.Point>} points\n * Previously placed points to check against.\n *\n * @return {boolean}\n * Returns true if there is collision.\n */\nfunction intersectsAnyWord(point, points) {\n    var rect = point.rect,\n        polygon = point.polygon,\n        lastCollidedWith = point.lastCollidedWith,\n        isIntersecting = function (p) {\n            var result = isRectanglesIntersecting(rect,\n        p.rect);\n        if (result &&\n            (point.rotation % 90 || p.rotation % 90)) {\n            result = isPolygonsColliding(polygon, p.polygon);\n        }\n        return result;\n    };\n    var intersects = false;\n    // If the point has already intersected a different point, chances are\n    // they are still intersecting. So as an enhancement we check this\n    // first.\n    if (lastCollidedWith) {\n        intersects = isIntersecting(lastCollidedWith);\n        // If they no longer intersects, remove the cache from the point.\n        if (!intersects) {\n            delete point.lastCollidedWith;\n        }\n    }\n    // If not already found, then check if we can find a point that is\n    // intersecting.\n    if (!intersects) {\n        intersects = !!find(points, function (p) {\n            var result = isIntersecting(p);\n            if (result) {\n                point.lastCollidedWith = p;\n            }\n            return result;\n        });\n    }\n    return intersects;\n}\n/**\n * Gives a set of cordinates for an Archimedian Spiral.\n *\n * @private\n * @function archimedeanSpiral\n *\n * @param {number} attempt\n * How far along the spiral we have traversed.\n *\n * @param {Highcharts.WordcloudSpiralParamsObject} [params]\n * Additional parameters.\n *\n * @return {boolean|Highcharts.PositionObject}\n * Resulting coordinates, x and y. False if the word should be dropped from\n * the visualization.\n */\nfunction archimedeanSpiral(attempt, params) {\n    var field = params.field,\n        maxDelta = (field.width * field.width) + (field.height * field.height),\n        t = attempt * 0.8; // 0.2 * 4 = 0.8. Enlarging the spiral.\n        var result = false;\n    // Emergency brake. TODO make spiralling logic more foolproof.\n    if (attempt <= 10000) {\n        result = {\n            x: t * Math.cos(t),\n            y: t * Math.sin(t)\n        };\n        if (!(Math.min(Math.abs(result.x), Math.abs(result.y)) < maxDelta)) {\n            result = false;\n        }\n    }\n    return result;\n}\n/**\n * Gives a set of coordinates for an rectangular spiral.\n *\n * @private\n * @function squareSpiral\n *\n * @param {number} attempt\n * How far along the spiral we have traversed.\n *\n * @param {Highcharts.WordcloudSpiralParamsObject} [params]\n * Additional parameters.\n *\n * @return {boolean|Highcharts.PositionObject}\n * Resulting coordinates, x and y. False if the word should be dropped from\n * the visualization.\n */\nfunction squareSpiral(attempt, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nparams) {\n    var a = attempt * 4,\n        k = Math.ceil((Math.sqrt(a) - 1) / 2),\n        isBoolean = function (x) { return (typeof x === 'boolean'); };\n    var t = 2 * k + 1,\n        m = Math.pow(t, 2),\n        result = false;\n    t -= 1;\n    if (attempt <= 10000) {\n        if (isBoolean(result) && a >= m - t) {\n            result = {\n                x: k - (m - a),\n                y: -k\n            };\n        }\n        m -= t;\n        if (isBoolean(result) && a >= m - t) {\n            result = {\n                x: -k,\n                y: -k + (m - a)\n            };\n        }\n        m -= t;\n        if (isBoolean(result)) {\n            if (a >= m - t) {\n                result = {\n                    x: -k + (m - a),\n                    y: k\n                };\n            }\n            else {\n                result = {\n                    x: k,\n                    y: k - (m - a - t)\n                };\n            }\n        }\n        result.x *= 5;\n        result.y *= 5;\n    }\n    return result;\n}\n/**\n * Gives a set of coordinates for an rectangular spiral.\n *\n * @private\n * @function rectangularSpiral\n *\n * @param {number} attempt\n * How far along the spiral we have traversed.\n *\n * @param {Highcharts.WordcloudSpiralParamsObject} [params]\n * Additional parameters.\n *\n * @return {boolean|Higcharts.PositionObject}\n * Resulting coordinates, x and y. False if the word should be dropped from\n * the visualization.\n */\nfunction rectangularSpiral(attempt, params) {\n    var result = squareSpiral(attempt,\n        params),\n        field = params.field;\n    if (result) {\n        result.x *= field.ratioX;\n        result.y *= field.ratioY;\n    }\n    return result;\n}\n/**\n * @private\n * @function getRandomPosition\n *\n * @param {number} size\n * Random factor.\n *\n * @return {number}\n * Random position.\n */\nfunction getRandomPosition(size) {\n    return Math.round((size * (Math.random() + 0.5)) / 2);\n}\n/**\n * Calculates the proper scale to fit the cloud inside the plotting area.\n *\n * @private\n * @function getScale\n *\n * @param {number} targetWidth\n * Width of target area.\n *\n * @param {number} targetHeight\n * Height of target area.\n *\n * @param {Object} field\n * The playing field.\n *\n * @return {number}\n * Returns the value to scale the playing field up to the size of the target\n * area.\n */\nfunction getScale(targetWidth, targetHeight, field) {\n    var height = Math.max(Math.abs(field.top), Math.abs(field.bottom)) * 2, width = Math.max(Math.abs(field.left), Math.abs(field.right)) * 2, scaleX = width > 0 ? 1 / width * targetWidth : 1, scaleY = height > 0 ? 1 / height * targetHeight : 1;\n    return Math.min(scaleX, scaleY);\n}\n/**\n * Calculates what is called the playing field. The field is the area which\n * all the words are allowed to be positioned within. The area is\n * proportioned to match the target aspect ratio.\n *\n * @private\n * @function getPlayingField\n *\n * @param {number} targetWidth\n * Width of the target area.\n *\n * @param {number} targetHeight\n * Height of the target area.\n *\n * @param {Array<Highcharts.Point>} data\n * Array of points.\n *\n * @param {Object} data.dimensions\n * The height and width of the word.\n *\n * @return {Object}\n * The width and height of the playing field.\n */\nfunction getPlayingField(targetWidth, targetHeight, data) {\n    var info = data.reduce(function (obj,\n        point) {\n            var dimensions = point.dimensions,\n        x = Math.max(dimensions.width,\n        dimensions.height);\n        // Find largest height.\n        obj.maxHeight = Math.max(obj.maxHeight, dimensions.height);\n        // Find largest width.\n        obj.maxWidth = Math.max(obj.maxWidth, dimensions.width);\n        // Sum up the total maximum area of all the words.\n        obj.area += x * x;\n        return obj;\n    }, {\n        maxHeight: 0,\n        maxWidth: 0,\n        area: 0\n    }), \n    /**\n     * Use largest width, largest height, or root of total area to give\n     * size to the playing field.\n     */\n    x = Math.max(info.maxHeight, // Have enough space for the tallest word\n    info.maxWidth, // Have enough space for the broadest word\n    // Adjust 15% to account for close packing of words\n    Math.sqrt(info.area) * 0.85), ratioX = targetWidth > targetHeight ? targetWidth / targetHeight : 1, ratioY = targetHeight > targetWidth ? targetHeight / targetWidth : 1;\n    return {\n        width: x * ratioX,\n        height: x * ratioY,\n        ratioX: ratioX,\n        ratioY: ratioY\n    };\n}\n/**\n * Calculates a number of degrees to rotate, based upon a number of\n * orientations within a range from-to.\n *\n * @private\n * @function getRotation\n *\n * @param {number} [orientations]\n * Number of orientations.\n *\n * @param {number} [index]\n * Index of point, used to decide orientation.\n *\n * @param {number} [from]\n * The smallest degree of rotation.\n *\n * @param {number} [to]\n * The largest degree of rotation.\n *\n * @return {boolean|number}\n * Returns the resulting rotation for the word. Returns false if invalid\n * input parameters.\n */\nfunction getRotation(orientations, index, from, to) {\n    var result = false, // Default to false\n        range,\n        intervals,\n        orientation;\n    // Check if we have valid input parameters.\n    if (isNumber(orientations) &&\n        isNumber(index) &&\n        isNumber(from) &&\n        isNumber(to) &&\n        orientations > 0 &&\n        index > -1 &&\n        to > from) {\n        range = to - from;\n        intervals = range / (orientations - 1 || 1);\n        orientation = index % orientations;\n        result = from + (orientation * intervals);\n    }\n    return result;\n}\n/**\n * Calculates the spiral positions and store them in scope for quick access.\n *\n * @private\n * @function getSpiral\n *\n * @param {Function} fn\n * The spiral function.\n *\n * @param {Object} params\n * Additional parameters for the spiral.\n *\n * @return {Function}\n * Function with access to spiral positions.\n */\nfunction getSpiral(fn, params) {\n    var length = 10000,\n        arr = [];\n    for (var i = 1; i < length; i++) {\n        // @todo unnecessary amount of precalculation\n        arr.push(fn(i, params));\n    }\n    return function (attempt) { return (attempt <= length ? arr[attempt - 1] : false); };\n}\n/**\n * Detects if a word is placed outside the playing field.\n *\n * @private\n * @function outsidePlayingField\n *\n * @param {Highcharts.PolygonBoxObject} rect\n * The word box.\n *\n * @param {Highcharts.WordcloudFieldObject} field\n * The width and height of the playing field.\n *\n * @return {boolean}\n * Returns true if the word is placed outside the field.\n */\nfunction outsidePlayingField(rect, field) {\n    var playingField = {\n            left: -(field.width / 2),\n            right: field.width / 2,\n            top: -(field.height / 2),\n            bottom: field.height / 2\n        };\n    return !(playingField.left < rect.left &&\n        playingField.right > rect.right &&\n        playingField.top < rect.top &&\n        playingField.bottom > rect.bottom);\n}\n/**\n * @private\n */\nfunction movePolygon(deltaX, deltaY, polygon) {\n    return polygon.map(function (point) {\n        return [\n            point[0] + deltaX,\n            point[1] + deltaY\n        ];\n    });\n}\n/**\n * Check if a point intersects with previously placed words, or if it goes\n * outside the field boundaries. If a collision, then try to adjusts the\n * position.\n *\n * @private\n * @function intersectionTesting\n *\n * @param {Highcharts.Point} point\n * Point to test for intersections.\n *\n * @param {Highcharts.WordcloudTestOptionsObject} options\n * Options object.\n *\n * @return {boolean|Highcharts.PositionObject}\n * Returns an object with how much to correct the positions. Returns false\n * if the word should not be placed at all.\n */\nfunction intersectionTesting(point, options) {\n    var placed = options.placed,\n        field = options.field,\n        rectangle = options.rectangle,\n        polygon = options.polygon,\n        spiral = options.spiral, \n        // Make a copy to update values during intersection testing.\n        rect = point.rect = WordcloudUtils_extend({},\n        rectangle);\n    var attempt = 1,\n        delta = {\n            x: 0,\n            y: 0\n        };\n    point.polygon = polygon;\n    point.rotation = options.rotation;\n    /* While w intersects any previously placed words:\n        do {\n        move w a little bit along a spiral path\n        } while any part of w is outside the playing field and\n                the spiral radius is still smallish */\n    while (delta !== false &&\n        (intersectsAnyWord(point, placed) ||\n            outsidePlayingField(rect, field))) {\n        delta = spiral(attempt);\n        if (isObject(delta)) {\n            // Update the DOMRect with new positions.\n            rect.left = rectangle.left + delta.x;\n            rect.right = rectangle.right + delta.x;\n            rect.top = rectangle.top + delta.y;\n            rect.bottom = rectangle.bottom + delta.y;\n            point.polygon = movePolygon(delta.x, delta.y, polygon);\n        }\n        attempt++;\n    }\n    return delta;\n}\n/**\n * Extends the playing field to have enough space to fit a given word.\n *\n * @private\n * @function extendPlayingField\n *\n * @param {Highcharts.WordcloudFieldObject} field\n * The width, height and ratios of a playing field.\n *\n * @param {Highcharts.PolygonBoxObject} rectangle\n * The bounding box of the word to add space for.\n *\n * @return {Highcharts.WordcloudFieldObject}\n * Returns the extended playing field with updated height and width.\n */\nfunction extendPlayingField(field, rectangle) {\n    var height,\n        width,\n        ratioX,\n        ratioY,\n        x,\n        extendWidth,\n        extendHeight,\n        result;\n    if (isObject(field) && isObject(rectangle)) {\n        height = (rectangle.bottom - rectangle.top);\n        width = (rectangle.right - rectangle.left);\n        ratioX = field.ratioX;\n        ratioY = field.ratioY;\n        // Use the same variable to extend both the height and width.\n        x = ((width * ratioX) > (height * ratioY)) ? width : height;\n        // Multiply variable with ratios to preserve aspect ratio.\n        extendWidth = x * ratioX;\n        extendHeight = x * ratioY;\n        // Calculate the size of the new field after adding\n        // space for the word.\n        result = merge(field, {\n            // Add space on the left and right.\n            width: field.width + (extendWidth * 2),\n            // Add space on the top and bottom.\n            height: field.height + (extendHeight * 2)\n        });\n    }\n    else {\n        result = field;\n    }\n    // Return the new extended field.\n    return result;\n}\n/**\n * If a rectangle is outside a give field, then the boundaries of the field\n * is adjusted accordingly. Modifies the field object which is passed as the\n * first parameter.\n *\n * @private\n * @function updateFieldBoundaries\n *\n * @param {Highcharts.WordcloudFieldObject} field\n * The bounding box of a playing field.\n *\n * @param {Highcharts.PolygonBoxObject} rectangle\n * The bounding box for a placed point.\n *\n * @return {Highcharts.WordcloudFieldObject}\n * Returns a modified field object.\n */\nfunction updateFieldBoundaries(field, rectangle) {\n    // @todo improve type checking.\n    if (!isNumber(field.left) || field.left > rectangle.left) {\n        field.left = rectangle.left;\n    }\n    if (!isNumber(field.right) || field.right < rectangle.right) {\n        field.right = rectangle.right;\n    }\n    if (!isNumber(field.top) || field.top > rectangle.top) {\n        field.top = rectangle.top;\n    }\n    if (!isNumber(field.bottom) || field.bottom < rectangle.bottom) {\n        field.bottom = rectangle.bottom;\n    }\n    return field;\n}\n/**\n * Alternative solution to correctFloat.\n * E.g Highcharts.correctFloat(123, 2) returns 120, when it should be 123.\n *\n * @private\n * @function correctFloat\n */\nfunction correctFloat(number, precision) {\n    var p = isNumber(precision) ? precision : 14,\n        magnitude = Math.pow(10,\n        p);\n    return Math.round(number * magnitude) / magnitude;\n}\n/**\n * @private\n */\nfunction getBoundingBoxFromPolygon(points) {\n    return points.reduce(function (obj, point) {\n        var x = point[0],\n            y = point[1];\n        obj.left = Math.min(x, obj.left);\n        obj.right = Math.max(x, obj.right);\n        obj.bottom = Math.max(y, obj.bottom);\n        obj.top = Math.min(y, obj.top);\n        return obj;\n    }, {\n        left: Number.MAX_VALUE,\n        right: -Number.MAX_VALUE,\n        bottom: -Number.MAX_VALUE,\n        top: Number.MAX_VALUE\n    });\n}\n/**\n * @private\n */\nfunction getPolygon(x, y, width, height, rotation) {\n    var origin = [x, y], left = x - (width / 2), right = x + (width / 2), top = y - (height / 2), bottom = y + (height / 2), polygon = [\n            [left, top],\n            [right, top],\n            [right, bottom],\n            [left, bottom]\n        ];\n    return polygon.map(function (point) {\n        return rotate2DToPoint(point, origin, -rotation);\n    });\n}\n/**\n * Rotates a point clockwise around the origin.\n *\n * @private\n * @function rotate2DToOrigin\n * @param {Highcharts.PolygonPointObject} point\n *        The x and y coordinates for the point.\n * @param {number} angle\n *        The angle of rotation.\n * @return {Highcharts.PolygonPointObject}\n *         The x and y coordinate for the rotated point.\n */\nfunction rotate2DToOrigin(point, angle) {\n    var x = point[0],\n        y = point[1],\n        rad = deg2rad * -angle,\n        cosAngle = Math.cos(rad),\n        sinAngle = Math.sin(rad);\n    return [\n        correctFloat(x * cosAngle - y * sinAngle),\n        correctFloat(x * sinAngle + y * cosAngle)\n    ];\n}\n/**\n * Rotate a point clockwise around another point.\n *\n * @private\n * @function rotate2DToPoint\n * @param {Highcharts.PolygonPointObject} point\n *        The x and y coordinates for the point.\n * @param {Highcharts.PolygonPointObject} origin\n *        The point to rotate around.\n * @param {number} angle\n *        The angle of rotation.\n * @return {Highcharts.PolygonPointObject}\n *         The x and y coordinate for the rotated point.\n */\nfunction rotate2DToPoint(point, origin, angle) {\n    var x = point[0] - origin[0],\n        y = point[1] - origin[1],\n        rotated = rotate2DToOrigin([x,\n        y],\n        angle);\n    return [\n        rotated[0] + origin[0],\n        rotated[1] + origin[1]\n    ];\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar WordcloudUtils = {\n    archimedeanSpiral: archimedeanSpiral,\n    extendPlayingField: extendPlayingField,\n    getBoundingBoxFromPolygon: getBoundingBoxFromPolygon,\n    getPlayingField: getPlayingField,\n    getPolygon: getPolygon,\n    getRandomPosition: getRandomPosition,\n    getRotation: getRotation,\n    getScale: getScale,\n    getSpiral: getSpiral,\n    intersectionTesting: intersectionTesting,\n    isPolygonsColliding: isPolygonsColliding,\n    isRectanglesIntersecting: isRectanglesIntersecting,\n    rectangularSpiral: rectangularSpiral,\n    rotate2DToOrigin: rotate2DToOrigin,\n    rotate2DToPoint: rotate2DToPoint,\n    squareSpiral: squareSpiral,\n    updateFieldBoundaries: updateFieldBoundaries\n};\n/* harmony default export */ var Wordcloud_WordcloudUtils = (WordcloudUtils);\n\n;// ./code/es5/es-modules/Series/Wordcloud/WordcloudSeries.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a word cloud.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\nvar WordcloudSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\n\nvar WordcloudSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, WordcloudSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, WordcloudSeries_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, WordcloudSeries_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n\n\n\nvar WordcloudSeries_archimedeanSpiral = Wordcloud_WordcloudUtils.archimedeanSpiral, WordcloudSeries_extendPlayingField = Wordcloud_WordcloudUtils.extendPlayingField, WordcloudSeries_getBoundingBoxFromPolygon = Wordcloud_WordcloudUtils.getBoundingBoxFromPolygon, WordcloudSeries_getPlayingField = Wordcloud_WordcloudUtils.getPlayingField, WordcloudSeries_getPolygon = Wordcloud_WordcloudUtils.getPolygon, WordcloudSeries_getRandomPosition = Wordcloud_WordcloudUtils.getRandomPosition, WordcloudSeries_getRotation = Wordcloud_WordcloudUtils.getRotation, WordcloudSeries_getScale = Wordcloud_WordcloudUtils.getScale, WordcloudSeries_getSpiral = Wordcloud_WordcloudUtils.getSpiral, WordcloudSeries_intersectionTesting = Wordcloud_WordcloudUtils.intersectionTesting, WordcloudSeries_isPolygonsColliding = Wordcloud_WordcloudUtils.isPolygonsColliding, WordcloudSeries_rectangularSpiral = Wordcloud_WordcloudUtils.rectangularSpiral, WordcloudSeries_rotate2DToOrigin = Wordcloud_WordcloudUtils.rotate2DToOrigin, WordcloudSeries_rotate2DToPoint = Wordcloud_WordcloudUtils.rotate2DToPoint, WordcloudSeries_squareSpiral = Wordcloud_WordcloudUtils.squareSpiral, WordcloudSeries_updateFieldBoundaries = Wordcloud_WordcloudUtils.updateFieldBoundaries;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.wordcloud\n *\n * @augments Highcharts.Series\n */\nvar WordcloudSeries = /** @class */ (function (_super) {\n    WordcloudSeries_extends(WordcloudSeries, _super);\n    function WordcloudSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     *\n     * Functions\n     *\n     */\n    WordcloudSeries.prototype.pointAttribs = function (point, state) {\n        var attribs = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().seriesTypes.column.prototype\n                .pointAttribs.call(this,\n            point,\n            state);\n        delete attribs.stroke;\n        delete attribs['stroke-width'];\n        return attribs;\n    };\n    /**\n     * Calculates the fontSize of a word based on its weight.\n     *\n     * @private\n     * @function Highcharts.Series#deriveFontSize\n     *\n     * @param {number} [relativeWeight=0]\n     * The weight of the word, on a scale 0-1.\n     *\n     * @param {number} [maxFontSize=1]\n     * The maximum font size of a word.\n     *\n     * @param {number} [minFontSize=1]\n     * The minimum font size of a word.\n     *\n     * @return {number}\n     * Returns the resulting fontSize of a word. If minFontSize is larger then\n     * maxFontSize the result will equal minFontSize.\n     */\n    WordcloudSeries.prototype.deriveFontSize = function (relativeWeight, maxFontSize, minFontSize) {\n        var weight = WordcloudSeries_isNumber(relativeWeight) ? relativeWeight : 0,\n            max = WordcloudSeries_isNumber(maxFontSize) ? maxFontSize : 1,\n            min = WordcloudSeries_isNumber(minFontSize) ? minFontSize : 1;\n        return Math.floor(Math.max(min, weight * max));\n    };\n    WordcloudSeries.prototype.drawPoints = function () {\n        var series = this,\n            hasRendered = series.hasRendered,\n            xAxis = series.xAxis,\n            yAxis = series.yAxis,\n            chart = series.chart,\n            group = series.group,\n            options = series.options,\n            animation = options.animation,\n            allowExtendPlayingField = options.allowExtendPlayingField,\n            renderer = chart.renderer,\n            placed = [],\n            placementStrategy = series.placementStrategy[options.placementStrategy],\n            rotation = options.rotation,\n            weights = series.points.map(function (p) {\n                return p.weight;\n        }), maxWeight = Math.max.apply(null, weights), \n        // `concat()` prevents from sorting the original array.\n        points = series.points.concat().sort(function (a, b) { return (b.weight - a.weight // Sort descending\n        ); });\n        var testElement = renderer.text().add(group),\n            field;\n        // Reset the scale before finding the dimensions (#11993).\n        // SVGGRaphicsElement.getBBox() (used in SVGElement.getBBox(boolean))\n        // returns slightly different values for the same element depending on\n        // whether it is rendered in a group which has already defined scale\n        // (e.g. 6) or in the group without a scale (scale = 1).\n        series.group.attr({\n            scaleX: 1,\n            scaleY: 1\n        });\n        // Get the dimensions for each word.\n        // Used in calculating the playing field.\n        for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n            var point = points_1[_i];\n            var relativeWeight = 1 / maxWeight * point.weight,\n                fontSize = series.deriveFontSize(relativeWeight,\n                options.maxFontSize,\n                options.minFontSize),\n                css = WordcloudSeries_extend({\n                    fontSize: fontSize + 'px'\n                },\n                options.style);\n            testElement.css(css).attr({\n                x: 0,\n                y: 0,\n                text: point.name\n            });\n            var bBox = testElement.getBBox(true);\n            point.dimensions = {\n                height: bBox.height,\n                width: bBox.width\n            };\n        }\n        // Calculate the playing field.\n        field = WordcloudSeries_getPlayingField(xAxis.len, yAxis.len, points);\n        var spiral = WordcloudSeries_getSpiral(series.spirals[options.spiral], {\n                field: field\n            });\n        // Draw all the points.\n        for (var _a = 0, points_2 = points; _a < points_2.length; _a++) {\n            var point = points_2[_a];\n            var relativeWeight = 1 / maxWeight * point.weight,\n                fontSize = series.deriveFontSize(relativeWeight,\n                options.maxFontSize,\n                options.minFontSize),\n                css = WordcloudSeries_extend({\n                    fontSize: fontSize + 'px'\n                },\n                options.style),\n                placement = placementStrategy(point, {\n                    data: points,\n                    field: field,\n                    placed: placed,\n                    rotation: rotation\n                }),\n                attr = WordcloudSeries_extend(series.pointAttribs(point, (point.selected && 'select')), {\n                    align: 'center',\n                    'alignment-baseline': 'middle',\n                    'dominant-baseline': 'middle', // #15973: Firefox\n                    x: placement.x,\n                    y: placement.y,\n                    text: point.name,\n                    rotation: WordcloudSeries_isNumber(placement.rotation) ?\n                        placement.rotation :\n                        void 0\n                }),\n                polygon = WordcloudSeries_getPolygon(placement.x,\n                placement.y,\n                point.dimensions.width,\n                point.dimensions.height,\n                placement.rotation),\n                rectangle = WordcloudSeries_getBoundingBoxFromPolygon(polygon);\n            var delta = WordcloudSeries_intersectionTesting(point, {\n                    rectangle: rectangle,\n                    polygon: polygon,\n                    field: field,\n                    placed: placed,\n                    spiral: spiral,\n                    rotation: placement.rotation\n                }),\n                animate = void 0;\n            // If there is no space for the word, extend the playing field.\n            if (!delta && allowExtendPlayingField) {\n                // Extend the playing field to fit the word.\n                field = WordcloudSeries_extendPlayingField(field, rectangle);\n                // Run intersection testing one more time to place the word.\n                delta = WordcloudSeries_intersectionTesting(point, {\n                    rectangle: rectangle,\n                    polygon: polygon,\n                    field: field,\n                    placed: placed,\n                    spiral: spiral,\n                    rotation: placement.rotation\n                });\n            }\n            // Check if point was placed, if so delete it, otherwise place it\n            // on the correct positions.\n            if (WordcloudSeries_isObject(delta)) {\n                attr.x = (attr.x || 0) + delta.x;\n                attr.y = (attr.y || 0) + delta.y;\n                rectangle.left += delta.x;\n                rectangle.right += delta.x;\n                rectangle.top += delta.y;\n                rectangle.bottom += delta.y;\n                field = WordcloudSeries_updateFieldBoundaries(field, rectangle);\n                placed.push(point);\n                point.isNull = false;\n                point.isInside = true; // #15447\n            }\n            else {\n                point.isNull = true;\n            }\n            if (animation) {\n                // Animate to new positions\n                animate = {\n                    x: attr.x,\n                    y: attr.y\n                };\n                // Animate from center of chart\n                if (!hasRendered) {\n                    attr.x = 0;\n                    attr.y = 0;\n                    // Or animate from previous position\n                }\n                else {\n                    delete attr.x;\n                    delete attr.y;\n                }\n            }\n            Series_DrawPointUtilities.draw(point, {\n                animatableAttribs: animate,\n                attribs: attr,\n                css: css,\n                group: group,\n                renderer: renderer,\n                shapeArgs: void 0,\n                shapeType: 'text'\n            });\n        }\n        // Destroy the element after use.\n        testElement = testElement.destroy();\n        // Scale the series group to fit within the plotArea.\n        var scale = WordcloudSeries_getScale(xAxis.len,\n            yAxis.len,\n            field);\n        series.group.attr({\n            scaleX: scale,\n            scaleY: scale\n        });\n    };\n    WordcloudSeries.prototype.hasData = function () {\n        var series = this;\n        return (WordcloudSeries_isObject(series) &&\n            series.visible === true &&\n            isArray(series.points) &&\n            series.points.length > 0);\n    };\n    WordcloudSeries.prototype.getPlotBox = function () {\n        var series = this, chart = series.chart, inverted = chart.inverted, \n            // Swap axes for inverted (#2339)\n            xAxis = series[(inverted ? 'yAxis' : 'xAxis')], yAxis = series[(inverted ? 'xAxis' : 'yAxis')], width = xAxis ? xAxis.len : chart.plotWidth, height = yAxis ? yAxis.len : chart.plotHeight, x = xAxis ? xAxis.left : chart.plotLeft, y = yAxis ? yAxis.top : chart.plotTop;\n        return {\n            translateX: x + (width / 2),\n            translateY: y + (height / 2),\n            scaleX: 1, // #1623\n            scaleY: 1\n        };\n    };\n    /* *\n     *\n     *  Static properties\n     *\n     * */\n    WordcloudSeries.defaultOptions = WordcloudSeries_merge(ColumnSeries.defaultOptions, Wordcloud_WordcloudSeriesDefaults);\n    return WordcloudSeries;\n}(ColumnSeries));\nWordcloudSeries_extend(WordcloudSeries.prototype, {\n    animate: noop,\n    animateDrilldown: noop,\n    animateDrillupFrom: noop,\n    isCartesian: false,\n    pointClass: Wordcloud_WordcloudPoint,\n    setClip: noop,\n    // Strategies used for deciding rotation and initial position of a word. To\n    // implement a custom strategy, have a look at the function random for\n    // example.\n    placementStrategy: {\n        random: function (point, options) {\n            var field = options.field,\n                r = options.rotation;\n            return {\n                x: WordcloudSeries_getRandomPosition(field.width) - (field.width / 2),\n                y: WordcloudSeries_getRandomPosition(field.height) - (field.height / 2),\n                rotation: WordcloudSeries_getRotation(r.orientations, point.index, r.from, r.to)\n            };\n        },\n        center: function (point, options) {\n            var r = options.rotation;\n            return {\n                x: 0,\n                y: 0,\n                rotation: WordcloudSeries_getRotation(r.orientations, point.index, r.from, r.to)\n            };\n        }\n    },\n    pointArrayMap: ['weight'],\n    // Spirals used for placing a word after the initial position experienced a\n    // collision with either another word or the borders. To implement a custom\n    // spiral, look at the function archimedeanSpiral for example.\n    spirals: {\n        'archimedean': WordcloudSeries_archimedeanSpiral,\n        'rectangular': WordcloudSeries_rectangularSpiral,\n        'square': WordcloudSeries_squareSpiral\n    },\n    utils: {\n        extendPlayingField: WordcloudSeries_extendPlayingField,\n        getRotation: WordcloudSeries_getRotation,\n        isPolygonsColliding: WordcloudSeries_isPolygonsColliding,\n        rotate2DToOrigin: WordcloudSeries_rotate2DToOrigin,\n        rotate2DToPoint: WordcloudSeries_rotate2DToPoint\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('wordcloud', WordcloudSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Wordcloud_WordcloudSeries = ((/* unused pure expression or super */ null && (WordcloudSeries)));\n\n;// ./code/es5/es-modules/masters/modules/wordcloud.js\n\n\n\n\n/* harmony default export */ var wordcloud_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "wordcloud_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "__assign", "assign", "t", "s", "i", "arguments", "length", "p", "apply", "Series_DrawPointUtilities", "draw", "point", "params", "animatableAttribs", "onComplete", "css", "renderer", "animation", "series", "chart", "hasRendered", "options", "graphic", "attribs", "getClassName", "shouldDraw", "shapeType", "text", "image", "imageUrl", "attr", "shapeArgs", "add", "group", "animate", "isNew", "destroy_1", "destroy", "keys", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "__", "constructor", "create", "ColumnPoint", "seriesTypes", "column", "pointClass", "extend", "WordcloudPoint", "_super", "<PERSON><PERSON><PERSON><PERSON>", "weight", "Wordcloud_WordcloudSeriesDefaults", "allowExtendPlayingField", "duration", "borderWidth", "clip", "colorByPoint", "cropThreshold", "Infinity", "minFontSize", "maxFontSize", "placementStrategy", "rotation", "from", "orientations", "to", "showInLegend", "spiral", "style", "fontFamily", "fontWeight", "whiteSpace", "tooltip", "followPointer", "pointFormat", "deg2rad", "WordcloudUtils_extend", "find", "isNumber", "isObject", "merge", "getAxesFromPolygon", "polygon", "points", "axes", "concat", "reduce", "p1", "p2", "dx", "dy", "axis", "normals", "existing", "push", "project", "target", "products", "map", "ax", "ay", "min", "Math", "max", "isPolygonsColliding", "polygon1", "polygon2", "axes1", "axes2", "projection1", "projection2", "squareSpiral", "attempt", "k", "ceil", "sqrt", "isBoolean", "x", "m", "pow", "result", "y", "correctFloat", "number", "precision", "magnitude", "round", "rotate2DToOrigin", "angle", "rad", "cosAngle", "cos", "sinAngle", "sin", "rotate2DToPoint", "origin", "rotated", "targetWidth", "targetHeight", "data", "info", "dimensions", "width", "height", "maxHeight", "max<PERSON><PERSON><PERSON>", "area", "ratioX", "ratioY", "field", "abs", "top", "bottom", "left", "right", "fn", "arr", "WordcloudSeries_extends", "TypeError", "String", "noop", "ColumnSeries", "WordcloudSeries_extend", "isArray", "WordcloudSeries_isNumber", "WordcloudSeries_isObject", "WordcloudSeries_merge", "WordcloudSeries_extendPlayingField", "rectangle", "extendWidth", "extendHeight", "WordcloudSeries_getPolygon", "WordcloudSeries_getRandomPosition", "size", "random", "WordcloudSeries_getRotation", "index", "intervals", "range", "orientation", "WordcloudSeries_intersectionTesting", "rect", "lastCollidedWith", "isIntersecting", "intersects", "playingField", "placed", "delta", "intersectsAnyWord", "r1", "r2", "movePolygon", "deltaX", "deltaY", "WordcloudSeries", "pointAttribs", "state", "stroke", "deriveFontSize", "relativeWeight", "floor", "drawPoints", "xAxis", "yAxis", "weights", "maxWeight", "sort", "testElement", "scaleX", "scaleY", "_i", "points_1", "fontSize", "name", "bBox", "getBBox", "WordcloudSeries_getPlayingField", "len", "WordcloudSeries_getSpiral", "spirals", "_a", "points_2", "placement", "selected", "align", "Number", "MAX_VALUE", "isNull", "isInside", "scale", "WordcloudSeries_getScale", "hasData", "visible", "getPlotBox", "inverted", "plot<PERSON>id<PERSON>", "plotHeight", "translateX", "plotLeft", "translateY", "plotTop", "defaultOptions", "animateDrilldown", "animateDrillupFrom", "isCartesian", "setClip", "r", "center", "pointArrayMap", "max<PERSON><PERSON><PERSON>", "utils", "extendPlayingField", "getRotation", "registerSeriesType"], "mappings": "CAUA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACzG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAEhHJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAkMFC,EAwhCAA,EA1tCMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAe,CAC9D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAQjHE,EAAgD,WAShD,MAAOA,AARPA,CAAAA,EAAWb,OAAOc,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAGC,EAAI,EAAGzB,EAAI0B,UAAUC,MAAM,CAAEF,EAAIzB,EAAGyB,IAE5C,IAAK,IAAIG,KADTJ,EAAIE,SAAS,CAACD,EAAE,CACKjB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACQ,EAAGI,IACzDL,CAAAA,CAAC,CAACK,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,EAElB,OAAOL,CACX,CAAA,EACgBM,KAAK,CAAC,IAAI,CAAEH,UAChC,EA2EiCI,EAHR,CACrBC,KAzDJ,SAAcC,CAAK,CAAEC,CAAM,EACvB,IAAIC,EAAoBD,EAAOC,iBAAiB,CAC5CC,EAAaF,EAAOE,UAAU,CAC9BC,EAAMH,EAAOG,GAAG,CAChBC,EAAWJ,EAAOI,QAAQ,CAC1BC,EAAY,AAACN,EAAMO,MAAM,EAAIP,EAAMO,MAAM,CAACC,KAAK,CAACC,WAAW,CAEvD,KAAK,EAEJT,EAAMO,MAAM,EACTP,EAAMO,MAAM,CAACG,OAAO,CAACJ,SAAS,CACtCK,EAAUX,EAAMW,OAAO,CAE3B,GADAV,EAAOW,OAAO,CAAGvB,EAASA,EAAS,CAAC,EAAGY,EAAOW,OAAO,EAAG,CAAE,MAASZ,EAAMa,YAAY,EAAG,IAAM,CAAC,EAC1Fb,EAAMc,UAAU,GACZH,IAWDX,EAAMW,OAAO,CATTA,EADAV,AAAqB,SAArBA,EAAOc,SAAS,CACNV,EAASW,IAAI,GAElBf,AAAqB,UAArBA,EAAOc,SAAS,CACXV,EAASY,KAAK,CAAChB,EAAOiB,QAAQ,EAAI,IACvCC,IAAI,CAAClB,EAAOmB,SAAS,EAAI,CAAC,GAGrBf,CAAQ,CAACJ,EAAOc,SAAS,CAAC,CAACd,EAAOmB,SAAS,EAAI,CAAC,GAG9DT,EAAQU,GAAG,CAACpB,EAAOqB,KAAK,GAExBlB,GACAO,EAAQP,GAAG,CAACA,GAEhBO,EACKQ,IAAI,CAAClB,EAAOW,OAAO,EACnBW,OAAO,CAACrB,EAAmBD,CAAAA,EAAOuB,KAAK,EAAWlB,EAAWH,QAEjE,GAAIQ,EAAS,CACd,IAAIc,EAAY,WACRzB,EAAMW,OAAO,CAAGA,EAAWA,GAAWA,EAAQe,OAAO,GAC/B,YAAtB,OAAOvB,GACPA,GAER,CAEI3B,CAAAA,OAAOmD,IAAI,CAACzB,GAAmBP,MAAM,CACrCgB,EAAQY,OAAO,CAACrB,EAAmB,KAAK,EAAG,WAAc,OAAOuB,GAAa,GAG7EA,GAER,CACJ,CAQA,EAIIG,EAAmIhE,EAAoB,KACvJiE,EAAuJjE,EAAoBI,CAAC,CAAC4D,GAc7KE,GACIrE,EAAgB,SAAUU,CAAC,CAC3B4D,CAAC,EAMD,MAAOtE,AALHA,CAAAA,EAAgBe,OAAOwD,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU/D,CAAC,CAC1D4D,CAAC,EAAI5D,EAAE8D,SAAS,CAAGF,CAAG,GACd,SAAU5D,CAAC,CACnB4D,CAAC,EAAI,IAAK,IAAInC,KAAKmC,EAAOA,EAAEhD,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGmC,CAAC,CAACnC,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAG4D,EAC5B,EACO,SAAU5D,CAAC,CAAE4D,CAAC,EAEjB,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGjE,CAAG,CADtCV,EAAcU,EAAG4D,GAEjB5D,EAAEW,SAAS,CAAGiD,AAAM,OAANA,EAAavD,OAAO6D,MAAM,CAACN,GAAMI,CAAAA,EAAGrD,SAAS,CAAGiD,EAAEjD,SAAS,CAAE,IAAIqD,CAAG,CACtF,GAGAG,EAAc,AAACT,IAA2IU,WAAW,CAACC,MAAM,CAAC1D,SAAS,CAAC2D,UAAU,CAEjMC,EAAS,AAACtD,IAA+EsD,MAAM,CAM/FC,EAAgC,SAAUC,CAAM,EAEhD,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAO/C,KAAK,CAAC,IAAI,CAAEH,YAAc,IAAI,AACnE,CASA,OAZAoC,EAAUa,EAAgBC,GAS1BD,EAAe7D,SAAS,CAAC+D,OAAO,CAAG,WAC/B,MAAO,CAAA,CACX,EACOF,CACX,EAAEL,GACFI,EAAOC,EAAe7D,SAAS,CAAE,CAC7BgE,OAAQ,CACZ,GAqN6B,IAAIC,EArKH,CAW1BC,wBAAyB,CAAA,EACzB1C,UAAW,CAEP2C,SAAU,GACd,EACAC,YAAa,EAIbC,KAAM,CAAA,EACNC,aAAc,CAAA,EACdC,cAAeC,IAKfC,YAAa,EAObC,YAAa,GAWbC,kBAAmB,SAOnBC,SAAU,CAINC,KAAM,EAMNC,aAAc,EAIdC,GAAI,EACR,EACAC,aAAc,CAAA,EAUdC,OAAQ,cAORC,MAAO,CAEHC,WAAY,aAEZC,WAAY,MAEZC,WAAY,QAChB,EACAC,QAAS,CACLC,cAAe,CAAA,EACfC,YAAa,sFACjB,CACJ,EAoFIC,EAAU,AAACnF,IAA+EmF,OAAO,CAEjGC,EAAwB,AAACpF,IAA+EsD,MAAM,CAAE+B,EAAO,AAACrF,IAA+EqF,IAAI,CAAEC,EAAW,AAACtF,IAA+EsF,QAAQ,CAAEC,EAAW,AAACvF,IAA+EuF,QAAQ,CAAEC,EAAQ,AAACxF,IAA+EwF,KAAK,CAmDxf,SAASC,EAAmBC,CAAO,EAC/B,IAAIC,EACAC,EAAOF,EAAQE,IAAI,EAAI,EAAE,CAqB7B,OApBKA,EAAKrF,MAAM,GACZqF,EAAO,EAAE,CAETD,AADAA,CAAAA,EAAkBD,EAAQG,MAAM,CAAC,CAACH,CAAO,CAAC,EAAE,CAAC,CAAA,EACtCI,MAAM,CAAC,SAAUC,CAAE,CAAEC,CAAE,EAC1B,IAlBJC,EACAC,EAmBQC,EAAOC,CApBfH,EAAKD,AAmBGA,CAnBD,CAAC,EAAE,CAAGD,AAkBgBA,CAlBd,CAAC,EAAE,CAEX,CACH,CAAC,EAFLG,EAAKF,AAkBGA,CAlBD,CAAC,EAAE,CAAGD,AAiBgBA,CAjBd,CAAC,EAAE,EAGlBE,EAAG,CACC,CAACC,EAAI,CAACD,EAAG,CACZ,CAcqB,CAAC,EAAE,CAUrB,OARSZ,EAAKO,EACV,SAAUS,CAAQ,EACd,OAAOA,CAAQ,CAAC,EAAE,GAAKF,CAAI,CAAC,EAAE,EAC1BE,CAAQ,CAAC,EAAE,GAAKF,CAAI,CAAC,EAAE,AACnC,IACIP,EAAKU,IAAI,CAACH,GAGPH,CACX,GACAN,EAAQE,IAAI,CAAGA,GAEZA,CACX,CAWA,SAASW,EAAQb,CAAO,CAAEc,CAAM,EAC5B,IAAIC,EAAWf,EAAQgB,GAAG,CAAC,SAAU9F,CAAK,EAClC,IAAI+F,EAAK/F,CAAK,CAAC,EAAE,CACrBgG,EAAKhG,CAAK,CAAC,EAAE,CAGb,OAAO+F,EAFFH,CAAM,CAAC,EAAE,CAEGI,EADZJ,CAAM,CAAC,EAAE,AAElB,GACA,MAAO,CACHK,IAAKC,KAAKD,GAAG,CAACpG,KAAK,CAAC,IAAI,CAAEgG,GAC1BM,IAAKD,KAAKC,GAAG,CAACtG,KAAK,CAAC,IAAI,CAAEgG,EAC9B,CACJ,CA4BA,SAASO,EAAoBC,CAAQ,CAAEC,CAAQ,EAE3C,IAAIC,EAAQ1B,EAAmBwB,GAC3BG,EAAQ3B,EAAmByB,GAM/B,MAJ2B,CAAC7B,EADjB8B,EAAMtB,MAAM,CAACuB,GAEpB,SAAUjB,CAAI,MA7BdkB,EAEAC,EA2BkB,OA7BlBD,EAAcd,EA8BdU,EADqDd,GAvBlD,CAFa,CAAEmB,CAAAA,CAFlBA,EAAcf,EA6BdW,EAFqDf,IAzBvBU,GAAG,CAAGQ,EAAYN,GAAG,EAC/CO,EAAYP,GAAG,CAAGM,EAAYR,GAAG,AAAD,CA0BzB,EAEnB,CAsGA,SAASU,EAAaC,CAAO,CAE7B3G,CAAM,EACF,IAAI7B,EAAIwI,AAAU,EAAVA,EACJC,EAAIX,KAAKY,IAAI,CAAC,AAACZ,CAAAA,KAAKa,IAAI,CAAC3I,GAAK,CAAA,EAAK,GACnC4I,EAAY,SAAUC,CAAC,EAAI,MAAQ,AAAa,WAAb,OAAOA,CAAkB,EAC5D1H,EAAI,EAAIsH,EAAI,EACZK,EAAIhB,KAAKiB,GAAG,CAAC5H,EAAG,GAChB6H,EAAS,CAAA,EAkCb,OAjCA7H,GAAK,EACDqH,GAAW,MACPI,EAAUI,IAAWhJ,GAAK8I,EAAI3H,GAC9B6H,CAAAA,EAAS,CACLH,EAAGJ,EAAKK,CAAAA,EAAI9I,CAAAA,EACZiJ,EAAG,CAACR,CACR,CAAA,EAEJK,GAAK3H,EACDyH,EAAUI,IAAWhJ,GAAK8I,EAAI3H,GAC9B6H,CAAAA,EAAS,CACLH,EAAG,CAACJ,EACJQ,EAAG,CAACR,EAAKK,CAAAA,EAAI9I,CAAAA,CACjB,CAAA,EAEJ8I,GAAK3H,EACDyH,EAAUI,KAENA,EADAhJ,GAAK8I,EAAI3H,EACA,CACL0H,EAAG,CAACJ,EAAKK,CAAAA,EAAI9I,CAAAA,EACbiJ,EAAGR,CACP,EAGS,CACLI,EAAGJ,EACHQ,EAAGR,EAAKK,CAAAA,EAAI9I,EAAImB,CAAAA,CACpB,GAGR6H,EAAOH,CAAC,EAAI,EACZG,EAAOC,CAAC,EAAI,GAETD,CACX,CAgXA,SAASE,EAAaC,CAAM,CAAEC,CAAS,EACnC,IACIC,EAAYvB,KAAKiB,GAAG,CAAC,GADjBzC,EAAS8C,GAAaA,EAAY,IAG1C,OAAOtB,KAAKwB,KAAK,CAACH,EAASE,GAAaA,CAC5C,CA8CA,SAASE,EAAiB3H,CAAK,CAAE4H,CAAK,EAClC,IAAIX,EAAIjH,CAAK,CAAC,EAAE,CACZqH,EAAIrH,CAAK,CAAC,EAAE,CACZ6H,EAAMtD,CAAAA,CAAAA,EAAWqD,CAAI,EACrBE,EAAW5B,KAAK6B,GAAG,CAACF,GACpBG,EAAW9B,KAAK+B,GAAG,CAACJ,GACxB,MAAO,CACHP,EAAaL,EAAIa,EAAWT,EAAIW,GAChCV,EAAaL,EAAIe,EAAWX,EAAIS,GACnC,AACL,CAeA,SAASI,EAAgBlI,CAAK,CAAEmI,CAAM,CAAEP,CAAK,EACzC,IAEIQ,EAAUT,EAAiB,CAFvB3H,CAAK,CAAC,EAAE,CAAGmI,CAAM,CAAC,EAAE,CACpBnI,CAAK,CAAC,EAAE,CAAGmI,CAAM,CAAC,EAAE,CAEtB,CACFP,GACJ,MAAO,CACHQ,CAAO,CAAC,EAAE,CAAGD,CAAM,CAAC,EAAE,CACtBC,CAAO,CAAC,EAAE,CAAGD,CAAM,CAAC,EAAE,CACzB,AACL,CAyB6B,MAzY7B,SAAyBE,CAAW,CAAEC,CAAY,CAAEC,CAAI,EACpD,IAAIC,EAAOD,EAAKrD,MAAM,CAAC,SAAUtG,CAAG,CAChCoB,CAAK,EACD,IAAIyI,EAAazI,EAAMyI,UAAU,CACrCxB,EAAIf,KAAKC,GAAG,CAACsC,EAAWC,KAAK,CAC7BD,EAAWE,MAAM,EAOjB,OALA/J,EAAIgK,SAAS,CAAG1C,KAAKC,GAAG,CAACvH,EAAIgK,SAAS,CAAEH,EAAWE,MAAM,EAEzD/J,EAAIiK,QAAQ,CAAG3C,KAAKC,GAAG,CAACvH,EAAIiK,QAAQ,CAAEJ,EAAWC,KAAK,EAEtD9J,EAAIkK,IAAI,EAAI7B,EAAIA,EACTrI,CACX,EAAG,CACCgK,UAAW,EACXC,SAAU,EACVC,KAAM,CACV,GAKA7B,EAAIf,KAAKC,GAAG,CAACqC,EAAKI,SAAS,CAC3BJ,EAAKK,QAAQ,CAEb3C,AAAuB,IAAvBA,KAAKa,IAAI,CAACyB,EAAKM,IAAI,GAAWC,EAASV,EAAcC,EAAeD,EAAcC,EAAe,EAAGU,EAASV,EAAeD,EAAcC,EAAeD,EAAc,EACvK,MAAO,CACHK,MAAOzB,EAAI8B,EACXJ,OAAQ1B,EAAI+B,EACZD,OAAQA,EACRC,OAAQA,CACZ,CACJ,IA3DA,SAAkBX,CAAW,CAAEC,CAAY,CAAEW,CAAK,EAC9C,IAAIN,EAASzC,AAAwD,EAAxDA,KAAKC,GAAG,CAACD,KAAKgD,GAAG,CAACD,EAAME,GAAG,EAAGjD,KAAKgD,GAAG,CAACD,EAAMG,MAAM,GAAQV,EAAQxC,AAAwD,EAAxDA,KAAKC,GAAG,CAACD,KAAKgD,GAAG,CAACD,EAAMI,IAAI,EAAGnD,KAAKgD,GAAG,CAACD,EAAMK,KAAK,GACnI,OAAOpD,KAAKD,GAAG,CADqIyC,EAAQ,EAAI,EAAIA,EAAQL,EAAc,EAAYM,EAAS,EAAI,EAAIA,EAASL,EAAe,EAEnP,IAmHA,SAAmBiB,CAAE,CAAEtJ,CAAM,EAGzB,IAAK,IADDuJ,EAAM,EAAE,CACH/J,EAAI,EAAGA,EAFH,IAEeA,IAExB+J,EAAI9D,IAAI,CAAC6D,EAAG9J,EAAGQ,IAEnB,OAAO,SAAU2G,CAAO,EAAI,OAAQA,GANvB,KAM2C4C,CAAG,CAAC5C,EAAU,EAAE,AAAW,CACvF,EAqTI6C,GACIhM,EAAgB,SAAUU,CAAC,CAC3B4D,CAAC,EAOD,MAAOtE,AANHA,CAAAA,EAAgBe,OAAOwD,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU/D,CAAC,CAC1D4D,CAAC,EAAI5D,EAAE8D,SAAS,CAAGF,CAAG,GACd,SAAU5D,CAAC,CACnB4D,CAAC,EAAI,IAAK,IAAInC,KAAKmC,EAAOvD,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC+C,EAC/DnC,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGmC,CAAC,CAACnC,EAAE,AAAD,CAAG,CAAA,EACIzB,EAAG4D,EAC5B,EACO,SAAU5D,CAAC,CAAE4D,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAI2H,UAAU,uBAAyBC,OAAO5H,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGjE,CAAG,CADtCV,EAAcU,EAAG4D,GAEjB5D,EAAEW,SAAS,CAAGiD,AAAM,OAANA,EAAavD,OAAO6D,MAAM,CAACN,GAAMI,CAAAA,EAAGrD,SAAS,CAAGiD,EAAEjD,SAAS,CAAE,IAAIqD,CAAG,CACtF,GAIAyH,EAAO,AAACxK,IAA+EwK,IAAI,CAE3FC,EAAe,AAAChI,IAA2IU,WAAW,CAACC,MAAM,CAE7KsH,EAAyB,AAAC1K,IAA+EsD,MAAM,CAAEqH,EAAU,AAAC3K,IAA+E2K,OAAO,CAAEC,EAA2B,AAAC5K,IAA+EsF,QAAQ,CAAEuF,EAA2B,AAAC7K,IAA+EuF,QAAQ,CAAEuF,EAAwB,AAAC9K,IAA+EwF,KAAK,CAI3duF,EArOpF,SAA4BlB,CAAK,CAAEmB,CAAS,EACxC,IAAIzB,EACAD,EACAK,EACAC,EACA/B,EAGAG,EAwBJ,OAvBIzC,EAASsE,IAAUtE,EAASyF,IAC5BzB,EAAUyB,EAAUhB,MAAM,CAAGgB,EAAUjB,GAAG,CAC1CT,EAAS0B,EAAUd,KAAK,CAAGc,EAAUf,IAAI,CAIzCpC,EAAI,AAAC,AAACyB,EAHNK,CAAAA,EAASE,EAAMF,MAAM,AAAD,EAGKJ,EAFzBK,CAAAA,EAASC,EAAMD,MAAM,AAAD,EAEyBN,EAAQC,EAMrDvB,EAASxC,EAAMqE,EAAO,CAElBP,MAAOO,EAAMP,KAAK,CAAI2B,AANZpD,EAAI8B,EAMsB,EAEpCJ,OAAQM,EAAMN,MAAM,CAAI2B,AAPbrD,EAAI+B,EAOwB,CAC3C,IAGA5B,EAAS6B,EAGN7B,CACX,EAoMkVmD,EA/HlV,SAAoBtD,CAAC,CAAEI,CAAC,CAAEqB,CAAK,CAAEC,CAAM,CAAEjF,CAAQ,EAC7C,IAAIyE,EAAS,CAAClB,EAAGI,EAAE,CAAEgC,EAAOpC,EAAKyB,EAAQ,EAAIY,EAAQrC,EAAKyB,EAAQ,EAAIS,EAAM9B,EAAKsB,EAAS,EAAIS,EAAS/B,EAAKsB,EAAS,EAMrH,MAAO7D,AAN4H,CAC3H,CAACuE,EAAMF,EAAI,CACX,CAACG,EAAOH,EAAI,CACZ,CAACG,EAAOF,EAAO,CACf,CAACC,EAAMD,EAAO,CACjB,CACUtD,GAAG,CAAC,SAAU9F,CAAK,EAC9B,OAAOkI,EAAgBlI,EAAOmI,EAAQ,CAACzE,EAC3C,EACJ,EAqHoZ8G,EAtepZ,SAA2BC,CAAI,EAC3B,OAAOvE,KAAKwB,KAAK,CAAC,AAAC+C,EAAQvE,CAAAA,KAAKwE,MAAM,GAAK,EAAE,EAAM,EACvD,EAoeoeC,EA7Xpe,SAAqB/G,CAAY,CAAEgH,CAAK,CAAEjH,CAAI,CAAEE,CAAE,EAC9C,IAEIgH,EAFAzD,EAAS,CAAA,EAiBb,OAZI1C,EAASd,IACTc,EAASkG,IACTlG,EAASf,IACTe,EAASb,IACTD,EAAe,GACfgH,EAAQ,IACR/G,EAAKF,IAELkH,EAAYC,AADJjH,CAAAA,EAAKF,CAAG,EACKC,CAAAA,EAAe,GAAK,CAAA,EAEzCwD,EAASzD,EAAQoH,AADHH,EAAQhH,EACSiH,GAE5BzD,CACX,EA0WsqB4D,EAzRtqB,SAA6BhL,CAAK,CAAEU,CAAO,EACvC,IAnXIuK,EACAnG,EACAoG,EACAC,EASAC,EA8TAC,EAyCAC,EAAS5K,EAAQ4K,MAAM,CACvBrC,EAAQvI,EAAQuI,KAAK,CACrBmB,EAAY1J,EAAQ0J,SAAS,CAC7BtF,EAAUpE,EAAQoE,OAAO,CACzBf,EAASrD,EAAQqD,MAAM,CAEvBkH,EAAOjL,EAAMiL,IAAI,CAAGzG,EAAsB,CAAC,EAC3C4F,GACAxD,EAAU,EACV2E,EAAQ,CACJtE,EAAG,EACHI,EAAG,CACP,EAQJ,IAPArH,EAAM8E,OAAO,CAAGA,EAChB9E,EAAM0D,QAAQ,CAAGhD,EAAQgD,QAAQ,CAM1B6H,AAAU,CAAA,IAAVA,GACFC,CAAAA,AAxYDP,EAAOjL,AAwYYA,EAxYNiL,IAAI,CACjBnG,EAAU9E,AAuYSA,EAvYH8E,OAAO,CACvBoG,EAAmBlL,AAsYAA,EAtYMkL,gBAAgB,CACzCC,EAAiB,SAAUvL,CAAC,EACxB,IAxIsB6L,EAAIC,EAwItBtE,GAxIkBqE,EAwIgBR,EAvIvC,CAAES,CAAAA,CADyBA,EAyI9B9L,EAAEqL,IAAI,EAxIE5B,IAAI,CAAGoC,EAAGnC,KAAK,EACvBoC,EAAGpC,KAAK,CAAGmC,EAAGpC,IAAI,EAClBqC,EAAGvC,GAAG,CAAGsC,EAAGrC,MAAM,EAClBsC,EAAGtC,MAAM,CAAGqC,EAAGtC,GAAG,AAAD,GA0IjB,OAJI/B,GACCpH,CAAAA,AAiYcA,EAjYR0D,QAAQ,CAAG,IAAM9D,EAAE8D,QAAQ,CAAG,EAAC,GACtC0D,CAAAA,EAAShB,EAAoBtB,EAASlF,EAAEkF,OAAO,CAAA,EAE5CsC,CACX,EACIgE,EAAa,CAAA,GAIbF,GACAE,CAAAA,EAAaD,EAAeD,EAAgB,GAGxC,OAAOlL,AAoXQA,EApXFkL,gBAAgB,CAKhCE,GACDA,CAAAA,EAAa,CAAC,CAAC3G,EA8WW6G,EA9WE,SAAU1L,CAAC,EACnC,IAAIwH,EAAS+D,EAAevL,GAI5B,OAHIwH,GACApH,CAAAA,AA2WWA,EA3WLkL,gBAAgB,CAAGtL,CAAAA,EAEtBwH,CACX,EAAC,EAEEgE,IA8SEC,CAAAA,CANLA,EAAe,CACXhC,KAAM,CAAEJ,CAAAA,AA8DkBA,EA9DZP,KAAK,CAAG,CAAA,EACtBY,MAAOL,AA6DmBA,EA7DbP,KAAK,CAAG,EACrBS,IAAK,CAAEF,CAAAA,AA4DmBA,EA5DbN,MAAM,CAAG,CAAA,EACtBS,OAAQH,AA2DkBA,EA3DZN,MAAM,CAAG,CAC3B,GACkBU,IAAI,CAAG4B,AAyDDA,EAzDM5B,IAAI,EAClCgC,EAAa/B,KAAK,CAAG2B,AAwDGA,EAxDE3B,KAAK,EAC/B+B,EAAalC,GAAG,CAAG8B,AAuDKA,EAvDA9B,GAAG,EAC3BkC,EAAajC,MAAM,CAAG6B,AAsDEA,EAtDG7B,MAAM,AAAD,CAsDG,GAE/BzE,EADJ4G,EAAQxH,EAAO6C,MAGXqE,EAAK5B,IAAI,CAAGe,EAAUf,IAAI,CAAGkC,EAAMtE,CAAC,CACpCgE,EAAK3B,KAAK,CAAGc,EAAUd,KAAK,CAAGiC,EAAMtE,CAAC,CACtCgE,EAAK9B,GAAG,CAAGiB,EAAUjB,GAAG,CAAGoC,EAAMlE,CAAC,CAClC4D,EAAK7B,MAAM,CAAGgB,EAAUhB,MAAM,CAAGmC,EAAMlE,CAAC,CACxCrH,EAAM8E,OAAO,CAAG6G,AAzD5B,SAAqBC,CAAM,CAAEC,CAAM,CAAE/G,CAAO,EACxC,OAAOA,EAAQgB,GAAG,CAAC,SAAU9F,CAAK,EAC9B,MAAO,CACHA,CAAK,CAAC,EAAE,CAAG4L,EACX5L,CAAK,CAAC,EAAE,CAAG6L,EACd,AACL,EACJ,EAkDwCN,EAAMtE,CAAC,CAAEsE,EAAMlE,CAAC,CAAEvC,IAElD8B,IAEJ,OAAO2E,CACX,EAkQIO,EAAiC,SAAUlJ,CAAM,EAEjD,SAASkJ,IACL,OAAOlJ,AAAW,OAAXA,GAAmBA,EAAO/C,KAAK,CAAC,IAAI,CAAEH,YAAc,IAAI,AACnE,CA2OA,OA9OA+J,EAAwBqC,EAAiBlJ,GASzCkJ,EAAgBhN,SAAS,CAACiN,YAAY,CAAG,SAAU/L,CAAK,CAAEgM,CAAK,EAC3D,IAAIpL,EAAUxB,IAA8EmD,WAAW,CAACC,MAAM,CAAC1D,SAAS,CAC/GiN,YAAY,CAAC/M,IAAI,CAAC,IAAI,CAC3BgB,EACAgM,GAGJ,OAFA,OAAOpL,EAAQqL,MAAM,CACrB,OAAOrL,CAAO,CAAC,eAAe,CACvBA,CACX,EAoBAkL,EAAgBhN,SAAS,CAACoN,cAAc,CAAG,SAAUC,CAAc,CAAE3I,CAAW,CAAED,CAAW,EACzF,IAAIT,EAASkH,EAAyBmC,GAAkBA,EAAiB,EACrEhG,EAAM6D,EAAyBxG,GAAeA,EAAc,EAEhE,OAAO0C,KAAKkG,KAAK,CAAClG,KAAKC,GAAG,CADhB6D,EAAyBzG,GAAeA,EAAc,EAChCT,EAASqD,GAC7C,EACA2F,EAAgBhN,SAAS,CAACuN,UAAU,CAAG,WACnC,IAoBIpD,EAnBAxI,EAAcF,AADL,IAAI,CACQE,WAAW,CAChC6L,EAAQ/L,AAFC,IAAI,CAEE+L,KAAK,CACpBC,EAAQhM,AAHC,IAAI,CAGEgM,KAAK,CACpB/L,EAAQD,AAJC,IAAI,CAIEC,KAAK,CACpBc,EAAQf,AALC,IAAI,CAKEe,KAAK,CACpBZ,EAAUH,AAND,IAAI,CAMIG,OAAO,CACxBJ,EAAYI,EAAQJ,SAAS,CAC7B0C,EAA0BtC,EAAQsC,uBAAuB,CACzD3C,EAAWG,EAAMH,QAAQ,CACzBiL,EAAS,EAAE,CACX7H,EAAoBlD,AAXX,IAAI,CAWckD,iBAAiB,CAAC/C,EAAQ+C,iBAAiB,CAAC,CACvEC,EAAWhD,EAAQgD,QAAQ,CAC3B8I,EAAUjM,AAbD,IAAI,CAaIwE,MAAM,CAACe,GAAG,CAAC,SAAUlG,CAAC,EACnC,OAAOA,EAAEkD,MAAM,AACvB,GAAI2J,EAAYvG,KAAKC,GAAG,CAACtG,KAAK,CAAC,KAAM2M,GAErCzH,EAASxE,AAjBI,IAAI,CAiBDwE,MAAM,CAACE,MAAM,GAAGyH,IAAI,CAAC,SAAUtO,CAAC,CAAE2D,CAAC,EAAI,OAAQA,EAAEe,MAAM,CAAG1E,EAAE0E,MAAM,AAC/E,GACC6J,EAActM,EAASW,IAAI,GAAGK,GAAG,CAACC,GAOtCf,AA1Ba,IAAI,CA0BVe,KAAK,CAACH,IAAI,CAAC,CACdyL,OAAQ,EACRC,OAAQ,CACZ,GAGA,IAAK,IAAIC,EAAK,EAAsBA,EAAKC,AAAbhI,EAAsBpF,MAAM,CAAEmN,IAAM,CAC5D,IAAI9M,EAAQ+M,AADYhI,CACJ,CAAC+H,EAAG,CACpBX,EAAiB,EAAIM,EAAYzM,EAAM8C,MAAM,CAC7CkK,EAAWzM,AAnCN,IAAI,CAmCS2L,cAAc,CAACC,EACjCzL,EAAQ8C,WAAW,CACnB9C,EAAQ6C,WAAW,EACnBnD,EAAM0J,EAAuB,CACzBkD,SAAUA,EAAW,IACzB,EACAtM,EAAQsD,KAAK,EACjB2I,EAAYvM,GAAG,CAACA,GAAKe,IAAI,CAAC,CACtB8F,EAAG,EACHI,EAAG,EACHrG,KAAMhB,EAAMiN,IAAI,AACpB,GACA,IAAIC,EAAOP,EAAYQ,OAAO,CAAC,CAAA,EAC/BnN,CAAAA,EAAMyI,UAAU,CAAG,CACfE,OAAQuE,EAAKvE,MAAM,CACnBD,MAAOwE,EAAKxE,KAAK,AACrB,CACJ,CAEAO,EAAQmE,EAAgCd,EAAMe,GAAG,CAAEd,EAAMc,GAAG,CAAEtI,GAK9D,IAAK,IAJDhB,EAASuJ,EAA0B/M,AAvD1B,IAAI,CAuD6BgN,OAAO,CAAC7M,EAAQqD,MAAM,CAAC,CAAE,CAC/DkF,MAAOA,CACX,GAEKuE,EAAK,EAAsBA,EAAKC,AAAb1I,EAAsBpF,MAAM,CAAE6N,IAAM,CAC5D,IAxSmBvE,EAAOmB,EAwStBpK,EAAQyN,AADY1I,CACJ,CAACyI,EAAG,CACpBrB,EAAiB,EAAIM,EAAYzM,EAAM8C,MAAM,CAC7CkK,EAAWzM,AA9DN,IAAI,CA8DS2L,cAAc,CAACC,EACjCzL,EAAQ8C,WAAW,CACnB9C,EAAQ6C,WAAW,EACnBnD,EAAM0J,EAAuB,CACzBkD,SAAUA,EAAW,IACzB,EACAtM,EAAQsD,KAAK,EACb0J,EAAYjK,EAAkBzD,EAAO,CACjCuI,KAAMxD,EACNkE,MAAOA,EACPqC,OAAQA,EACR5H,SAAUA,CACd,GACAvC,EAAO2I,EAAuBvJ,AA3EzB,IAAI,CA2E4BwL,YAAY,CAAC/L,EAAQA,EAAM2N,QAAQ,EAAI,UAAY,CACpFC,MAAO,SACP,qBAAsB,SACtB,oBAAqB,SACrB3G,EAAGyG,EAAUzG,CAAC,CACdI,EAAGqG,EAAUrG,CAAC,CACdrG,KAAMhB,EAAMiN,IAAI,CAChBvJ,SAAUsG,EAAyB0D,EAAUhK,QAAQ,EACjDgK,EAAUhK,QAAQ,CAClB,KAAK,CACb,GACAoB,EAAUyF,EAA2BmD,EAAUzG,CAAC,CAChDyG,EAAUrG,CAAC,CACXrH,EAAMyI,UAAU,CAACC,KAAK,CACtB1I,EAAMyI,UAAU,CAACE,MAAM,CACvB+E,EAAUhK,QAAQ,EAClB0G,EAtSLrF,AAsS2DD,EAtSpDI,MAAM,CAAC,SAAUtG,CAAG,CAAEoB,CAAK,EACrC,IAAIiH,EAAIjH,CAAK,CAAC,EAAE,CACZqH,EAAIrH,CAAK,CAAC,EAAE,CAKhB,OAJApB,EAAIyK,IAAI,CAAGnD,KAAKD,GAAG,CAACgB,EAAGrI,EAAIyK,IAAI,EAC/BzK,EAAI0K,KAAK,CAAGpD,KAAKC,GAAG,CAACc,EAAGrI,EAAI0K,KAAK,EACjC1K,EAAIwK,MAAM,CAAGlD,KAAKC,GAAG,CAACkB,EAAGzI,EAAIwK,MAAM,EACnCxK,EAAIuK,GAAG,CAAGjD,KAAKD,GAAG,CAACoB,EAAGzI,EAAIuK,GAAG,EACtBvK,CACX,EAAG,CACCyK,KAAMwE,OAAOC,SAAS,CACtBxE,MAAO,CAACuE,OAAOC,SAAS,CACxB1E,OAAQ,CAACyE,OAAOC,SAAS,CACzB3E,IAAK0E,OAAOC,SAAS,AACzB,GA0RYvC,EAAQP,EAAoChL,EAAO,CAC/CoK,UAAWA,EACXtF,QAASA,EACTmE,MAAOA,EACPqC,OAAQA,EACRvH,OAAQA,EACRL,SAAUgK,EAAUhK,QAAQ,AAChC,GACAnC,EAAU,KAAK,CAEf,EAACgK,GAASvI,IAEViG,EAAQkB,EAAmClB,EAAOmB,GAElDmB,EAAQP,EAAoChL,EAAO,CAC/CoK,UAAWA,EACXtF,QAASA,EACTmE,MAAOA,EACPqC,OAAQA,EACRvH,OAAQA,EACRL,SAAUgK,EAAUhK,QAAQ,AAChC,IAIAuG,EAAyBsB,IACzBpK,EAAK8F,CAAC,CAAG,AAAC9F,CAAAA,EAAK8F,CAAC,EAAI,CAAA,EAAKsE,EAAMtE,CAAC,CAChC9F,EAAKkG,CAAC,CAAG,AAAClG,CAAAA,EAAKkG,CAAC,EAAI,CAAA,EAAKkE,EAAMlE,CAAC,CAChC+C,EAAUf,IAAI,EAAIkC,EAAMtE,CAAC,CACzBmD,EAAUd,KAAK,EAAIiC,EAAMtE,CAAC,CAC1BmD,EAAUjB,GAAG,EAAIoC,EAAMlE,CAAC,CACxB+C,EAAUhB,MAAM,EAAImC,EAAMlE,CAAC,CAvWZ4B,EAwW+BA,EAxWxBmB,EAwW+BA,EAtW7D,CAAA,CAAC1F,EAASuE,EAAMI,IAAI,GAAKJ,EAAMI,IAAI,CAAGe,EAAUf,IAAI,AAAD,GACnDJ,CAAAA,EAAMI,IAAI,CAAGe,EAAUf,IAAI,AAAD,EAE1B,CAAA,CAAC3E,EAASuE,EAAMK,KAAK,GAAKL,EAAMK,KAAK,CAAGc,EAAUd,KAAK,AAAD,GACtDL,CAAAA,EAAMK,KAAK,CAAGc,EAAUd,KAAK,AAAD,EAE5B,CAAA,CAAC5E,EAASuE,EAAME,GAAG,GAAKF,EAAME,GAAG,CAAGiB,EAAUjB,GAAG,AAAD,GAChDF,CAAAA,EAAME,GAAG,CAAGiB,EAAUjB,GAAG,AAAD,EAExB,CAAA,CAACzE,EAASuE,EAAMG,MAAM,GAAKH,EAAMG,MAAM,CAAGgB,EAAUhB,MAAM,AAAD,GACzDH,CAAAA,EAAMG,MAAM,CAAGgB,EAAUhB,MAAM,AAAD,EA4VtBH,EA1VLA,EA2VKqC,EAAO5F,IAAI,CAAC1F,GACZA,EAAM+N,MAAM,CAAG,CAAA,EACf/N,EAAMgO,QAAQ,CAAG,CAAA,GAGjBhO,EAAM+N,MAAM,CAAG,CAAA,EAEfzN,IAEAiB,EAAU,CACN0F,EAAG9F,EAAK8F,CAAC,CACTI,EAAGlG,EAAKkG,CAAC,AACb,EAEK5G,GAMD,OAAOU,EAAK8F,CAAC,CACb,OAAO9F,EAAKkG,CAAC,GANblG,EAAK8F,CAAC,CAAG,EACT9F,EAAKkG,CAAC,CAAG,IAQjBvH,EAA0BC,IAAI,CAACC,EAAO,CAClCE,kBAAmBqB,EACnBX,QAASO,EACTf,IAAKA,EACLkB,MAAOA,EACPjB,SAAUA,EACVe,UAAW,KAAK,EAChBL,UAAW,MACf,EACJ,CAEA4L,EAAcA,EAAYjL,OAAO,GAEjC,IAAIuM,EAAQC,EAAyB5B,EAAMe,GAAG,CAC1Cd,EAAMc,GAAG,CACTpE,GACJ1I,AArKa,IAAI,CAqKVe,KAAK,CAACH,IAAI,CAAC,CACdyL,OAAQqB,EACRpB,OAAQoB,CACZ,EACJ,EACAnC,EAAgBhN,SAAS,CAACqP,OAAO,CAAG,WAEhC,OAAQlE,EADK,IAAI,GAEb1J,AAAmB,CAAA,IAAnBA,AAFS,IAAI,CAEN6N,OAAO,EACdrE,EAAQxJ,AAHC,IAAI,CAGEwE,MAAM,GACrBxE,AAJS,IAAI,CAINwE,MAAM,CAACpF,MAAM,CAAG,CAC/B,EACAmM,EAAgBhN,SAAS,CAACuP,UAAU,CAAG,WACnC,IAAmB7N,EAAQD,AAAd,IAAI,CAAiBC,KAAK,CAAE8N,EAAW9N,EAAM8N,QAAQ,CAE9DhC,EAAQ/L,AAFC,IAAI,AAEC,CAAE+N,EAAW,QAAU,QAAS,CAAE/B,EAAQhM,AAF/C,IAAI,AAEiD,CAAE+N,EAAW,QAAU,QAAS,CAAE5F,EAAQ4D,EAAQA,EAAMe,GAAG,CAAG7M,EAAM+N,SAAS,CAAE5F,EAAS4D,EAAQA,EAAMc,GAAG,CAAG7M,EAAMgO,UAAU,CAC9L,MAAO,CACHC,WAAYxH,AAFoLqF,CAAAA,EAAQA,EAAMjD,IAAI,CAAG7I,EAAMkO,QAAQ,AAAD,EAEjNhG,EAAQ,EACzBiG,WAAYtH,AAH6NkF,CAAAA,EAAQA,EAAMpD,GAAG,CAAG3I,EAAMoO,OAAO,AAAD,EAGxPjG,EAAS,EAC1BiE,OAAQ,EACRC,OAAQ,CACZ,CACJ,EAMAf,EAAgB+C,cAAc,CAAG3E,EAAsBL,EAAagF,cAAc,CAAE9L,GAC7E+I,CACX,EAAEjC,GACFC,EAAuBgC,EAAgBhN,SAAS,CAAE,CAC9CyC,QAASqI,EACTkF,iBAAkBlF,EAClBmF,mBAAoBnF,EACpBoF,YAAa,CAAA,EACbvM,WAxwCyDE,EAywCzDsM,QAASrF,EAITnG,kBAAmB,CACfiH,OAAQ,SAAU1K,CAAK,CAAEU,CAAO,EAC5B,IAAIuI,EAAQvI,EAAQuI,KAAK,CACrBiG,EAAIxO,EAAQgD,QAAQ,CACxB,MAAO,CACHuD,EAAGuD,EAAkCvB,EAAMP,KAAK,EAAKO,EAAMP,KAAK,CAAG,EACnErB,EAAGmD,EAAkCvB,EAAMN,MAAM,EAAKM,EAAMN,MAAM,CAAG,EACrEjF,SAAUiH,EAA4BuE,EAAEtL,YAAY,CAAE5D,EAAM4K,KAAK,CAAEsE,EAAEvL,IAAI,CAAEuL,EAAErL,EAAE,CACnF,CACJ,EACAsL,OAAQ,SAAUnP,CAAK,CAAEU,CAAO,EAC5B,IAAIwO,EAAIxO,EAAQgD,QAAQ,CACxB,MAAO,CACHuD,EAAG,EACHI,EAAG,EACH3D,SAAUiH,EAA4BuE,EAAEtL,YAAY,CAAE5D,EAAM4K,KAAK,CAAEsE,EAAEvL,IAAI,CAAEuL,EAAErL,EAAE,CACnF,CACJ,CACJ,EACAuL,cAAe,CAAC,SAAS,CAIzB7B,QAAS,CACL,YAv3BR,SAA2B3G,CAAO,CAAE3G,CAAM,EACtC,IAAIgJ,EAAQhJ,EAAOgJ,KAAK,CACpBoG,EAAW,AAACpG,EAAMP,KAAK,CAAGO,EAAMP,KAAK,CAAKO,EAAMN,MAAM,CAAGM,EAAMN,MAAM,CACrEpJ,EAAIqH,AAAU,GAAVA,EACAQ,EAAS,CAAA,EAWjB,OATIR,GAAW,KAKP,CAAEV,CAAAA,KAAKD,GAAG,CAACC,KAAKgD,GAAG,CAAC9B,AAJxBA,CAAAA,EAAS,CACLH,EAAG1H,EAAI2G,KAAK6B,GAAG,CAACxI,GAChB8H,EAAG9H,EAAI2G,KAAK+B,GAAG,CAAC1I,EACpB,CAAA,EAC+B0H,CAAC,EAAGf,KAAKgD,GAAG,CAAC9B,EAAOC,CAAC,GAAKgI,CAAO,GAC5DjI,CAAAA,EAAS,CAAA,CAAI,EAGdA,CACX,EAw2BQ,YA3xBR,SAA2BR,CAAO,CAAE3G,CAAM,EACtC,IAAImH,EAAST,EAAaC,EACtB3G,GACAgJ,EAAQhJ,EAAOgJ,KAAK,CAKxB,OAJI7B,IACAA,EAAOH,CAAC,EAAIgC,EAAMF,MAAM,CACxB3B,EAAOC,CAAC,EAAI4B,EAAMD,MAAM,EAErB5B,CACX,EAmxBQ,OAjVUT,CAkVd,EACA2I,MAAO,CACHC,mBAAoBpF,EACpBqF,YAAa7E,EACbvE,oBA3ViBA,EA4VjBuB,iBAzVcA,EA0VdO,gBAzVaA,CA0VjB,CACJ,GACArG,IAA0I4N,kBAAkB,CAAC,YAAa3D,GAa7I,IAAI5M,EAAkBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,GAET"}