{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/variable-pie\n * @requires highcharts\n *\n * Variable Pie module for Highcharts\n *\n * (c) 2010-2025 <PERSON><PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/variable-pie\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/variable-pie\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ variable_pie_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/VariablePie/VariablePieSeriesDefaults.js\n/* *\n *\n *  Variable Pie module for Highcharts\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A variable pie series is a two dimensional series type, where each point\n * renders an Y and Z value.  Each point is drawn as a pie slice where the\n * size (arc) of the slice relates to the Y value and the radius of pie\n * slice relates to the Z value.\n *\n * @sample {highcharts} highcharts/demo/variable-radius-pie/\n *         Variable-radius pie chart\n *\n * @extends      plotOptions.pie\n * @excluding    dragDrop\n * @since        6.0.0\n * @product      highcharts\n * @requires     modules/variable-pie\n * @optionparent plotOptions.variablepie\n */\nvar VariablePieSeriesDefaults = {\n    /**\n     * The minimum size of the points' radius related to chart's `plotArea`.\n     * If a number is set, it applies in pixels.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/min-max-point-size/\n     *         Example of minPointSize and maxPointSize\n     * @sample {highcharts} highcharts/variable-radius-pie/min-point-size-100/\n     *         minPointSize set to 100\n     *\n     * @type  {number|string}\n     * @since 6.0.0\n     */\n    minPointSize: '10%',\n    /**\n     * The maximum size of the points' radius related to chart's `plotArea`.\n     * If a number is set, it applies in pixels.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/min-max-point-size/\n     *         Example of minPointSize and maxPointSize\n     *\n     * @type  {number|string}\n     * @since 6.0.0\n     */\n    maxPointSize: '100%',\n    /**\n     * The minimum possible z value for the point's radius calculation. If\n     * the point's Z value is smaller than zMin, the slice will be drawn\n     * according to the zMin value.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/zmin-5/\n     *         zMin set to 5, smaller z values are treated as 5\n     * @sample {highcharts} highcharts/variable-radius-pie/zmin-zmax/\n     *         Series limited by both zMin and zMax\n     *\n     * @type  {number}\n     * @since 6.0.0\n     */\n    zMin: void 0,\n    /**\n     * The maximum possible z value for the point's radius calculation. If\n     * the point's Z value is bigger than zMax, the slice will be drawn\n     * according to the zMax value\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/zmin-zmax/\n     *         Series limited by both zMin and zMax\n     *\n     * @type  {number}\n     * @since 6.0.0\n     */\n    zMax: void 0,\n    /**\n     * Whether the pie slice's value should be represented by the area or\n     * the radius of the slice. Can be either `area` or `radius`. The\n     * default, `area`, corresponds best to the human perception of the size\n     * of each pie slice.\n     *\n     * @sample {highcharts} highcharts/variable-radius-pie/sizeby/\n     *         Difference between area and radius sizeBy\n     *\n     * @type  {Highcharts.VariablePieSizeByValue}\n     * @since 6.0.0\n     */\n    sizeBy: 'area',\n    tooltip: {\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}<br/>Value: {point.y}<br/>Size: {point.z}<br/>'\n    }\n};\n/**\n * A `variablepie` series. If the [type](#series.variablepie.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.variablepie\n * @excluding dataParser, dataURL, stack, xAxis, yAxis, dataSorting,\n *            boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/variable-pie\n * @apioption series.variablepie\n */\n/**\n * An array of data points for the series. For the `variablepie` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 2 values. In this case, the numerical values will\n *    be interpreted as `y, z` options. Example:\n *    ```js\n *    data: [\n *        [40, 75],\n *        [50, 50],\n *        [60, 40]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.variablepie.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        y: 1,\n *        z: 4,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        y: 7,\n *        z: 10,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|*>}\n * @extends   series.pie.data\n * @excluding marker, x\n * @product   highcharts\n * @apioption series.variablepie.data\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var VariablePie_VariablePieSeriesDefaults = (VariablePieSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/VariablePie/VariablePieSeries.js\n/* *\n *\n *  Variable Pie module for Highcharts\n *\n *  (c) 2010-2025 Grzegorz Blachliński\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar PieSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.pie;\n\nvar arrayMax = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).arrayMax, arrayMin = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).arrayMin, clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The variablepie series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.variablepie\n *\n * @augments Highcharts.Series\n */\nvar VariablePieSeries = /** @class */ (function (_super) {\n    __extends(VariablePieSeries, _super);\n    function VariablePieSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Before standard translate method for pie chart it is needed to calculate\n     * min/max radius of each pie slice based on its Z value.\n     * @private\n     */\n    VariablePieSeries.prototype.calculateExtremes = function () {\n        var series = this,\n            chart = series.chart,\n            plotWidth = chart.plotWidth,\n            plotHeight = chart.plotHeight,\n            seriesOptions = series.options,\n            slicingRoom = 2 * (seriesOptions.slicedOffset || 0),\n            zData = series.getColumn('z'),\n            smallestSize = Math.min(plotWidth,\n            plotHeight) - slicingRoom, \n            // Min and max size of pie slice:\n            extremes = {}, \n            // In pie charts size of a pie is changed to make space for\n            // dataLabels, then series.center is changing.\n            positions = series.center || series.getCenter();\n        var zMin,\n            zMax;\n        for (var _i = 0, _a = ['minPointSize', 'maxPointSize']; _i < _a.length; _i++) {\n            var prop = _a[_i];\n            var length_1 = seriesOptions[prop];\n            var isPercent = /%$/.test(length_1);\n            length_1 = parseInt(length_1, 10);\n            extremes[prop] = isPercent ?\n                smallestSize * length_1 / 100 :\n                length_1 * 2; // Because it should be radius, not diameter.\n        }\n        series.minPxSize = positions[3] + extremes.minPointSize;\n        series.maxPxSize = clamp(positions[2], positions[3] + extremes.minPointSize, extremes.maxPointSize);\n        if (zData.length) {\n            zMin = pick(seriesOptions.zMin, arrayMin(zData.filter(series.zValEval)));\n            zMax = pick(seriesOptions.zMax, arrayMax(zData.filter(series.zValEval)));\n            this.getRadii(zMin, zMax, series.minPxSize, series.maxPxSize);\n        }\n    };\n    /**\n     * Finding radius of series points based on their Z value and min/max Z\n     * value for all series.\n     *\n     * @private\n     * @function Highcharts.Series#getRadii\n     *\n     * @param {number} zMin\n     * Min threshold for Z value. If point's Z value is smaller that zMin, point\n     * will have the smallest possible radius.\n     *\n     * @param {number} zMax\n     * Max threshold for Z value. If point's Z value is bigger that zMax, point\n     * will have the biggest possible radius.\n     *\n     * @param {number} minSize\n     * Minimal pixel size possible for radius.\n     *\n     * @param {numbner} maxSize\n     * Minimal pixel size possible for radius.\n     */\n    VariablePieSeries.prototype.getRadii = function (zMin, zMax, minSize, maxSize) {\n        var zData = this.getColumn('z'), radii = [], options = this.options, sizeByArea = options.sizeBy !== 'radius', zRange = zMax - zMin;\n        var pos,\n            value,\n            radius;\n        // Calculate radius for all pie slice's based on their Z values\n        for (var i = 0; i < zData.length; i++) {\n            // If zData[i] is null/undefined/string we need to take zMin for\n            // smallest radius.\n            value = this.zValEval(zData[i]) ? zData[i] : zMin;\n            if (value <= zMin) {\n                radius = minSize / 2;\n            }\n            else if (value >= zMax) {\n                radius = maxSize / 2;\n            }\n            else {\n                // Relative size, a number between 0 and 1\n                pos = zRange > 0 ? (value - zMin) / zRange : 0.5;\n                if (sizeByArea) {\n                    pos = Math.sqrt(pos);\n                }\n                radius = Math.ceil(minSize + pos * (maxSize - minSize)) / 2;\n            }\n            radii.push(radius);\n        }\n        this.radii = radii;\n    };\n    /**\n     * It is needed to null series.center on chart redraw. Probably good idea\n     * will be to add this option in directly in pie series.\n     * @private\n     */\n    VariablePieSeries.prototype.redraw = function () {\n        this.center = null;\n        _super.prototype.redraw.call(this);\n    };\n    /** @private */\n    VariablePieSeries.prototype.getDataLabelPosition = function (point, distance) {\n        var _a = this, center = _a.center, options = _a.options, angle = point.angle || 0, r = this.radii[point.index], x = center[0] + Math.cos(angle) * r, y = center[1] + Math.sin(angle) * r, connectorOffset = (options.slicedOffset || 0) +\n                (options.borderWidth || 0), \n            // Set the anchor point for data labels. Use point.labelDistance\n            // instead of labelDistance // #1174\n            // finalConnectorOffset - not override connectorOffset value.\n            finalConnectorOffset = Math.min(connectorOffset, distance / 5); // #1678\n            return {\n                distance: distance,\n                natural: {\n                    // Initial position of the data label - it's utilized for\n                    // finding the final position for the label\n                    x: x + Math.cos(angle) * distance,\n                    y: y + Math.sin(angle) * distance\n                },\n                computed: {\n                // Used for generating connector path - initialized later in\n                // drawDataLabels function x: undefined, y: undefined\n                },\n                // Left - pie on the left side of the data label\n                // Right - pie on the right side of the data label\n                alignment: point.half ? 'right' : 'left',\n                connectorPosition: {\n                    breakAt: {\n                        x: x + Math.cos(angle) * finalConnectorOffset,\n                        y: y + Math.sin(angle) * finalConnectorOffset\n                    },\n                    touchingSliceAt: {\n                        x: x,\n                        y: y\n                    }\n                }\n            };\n    };\n    /**\n     * Extend translate by updating radius for each pie slice instead of using\n     * one global radius.\n     * @private\n     */\n    VariablePieSeries.prototype.translate = function (positions) {\n        this.generatePoints();\n        var series = this, precision = 1000, // Issue #172\n            options = series.options, slicedOffset = options.slicedOffset, startAngle = options.startAngle || 0, startAngleRad = Math.PI / 180 * (startAngle - 90), endAngleRad = Math.PI / 180 * (pick(options.endAngle, startAngle + 360) - 90), circ = endAngleRad - startAngleRad, // 2 * Math.PI,\n            points = series.points, ignoreHiddenPoint = options.ignoreHiddenPoint;\n        var cumulative = 0,\n            start,\n            end,\n            angle, \n            // The x component of the radius vector for a given point\n            radiusX,\n            radiusY,\n            point,\n            pointRadii;\n        series.startAngleRad = startAngleRad;\n        series.endAngleRad = endAngleRad;\n        // Use calculateExtremes to get series.radii array.\n        series.calculateExtremes();\n        // Get positions - either an integer or a percentage string must be\n        // given. If positions are passed as a parameter, we're in a\n        // recursive loop for adjusting space for data labels.\n        if (!positions) {\n            series.center = positions = series.getCenter();\n        }\n        // Calculate the geometry for each point\n        for (var i = 0; i < points.length; i++) {\n            point = points[i];\n            pointRadii = series.radii[i];\n            // Set start and end angle\n            start = startAngleRad + (cumulative * circ);\n            if (!ignoreHiddenPoint || point.visible) {\n                cumulative += point.percentage / 100;\n            }\n            end = startAngleRad + (cumulative * circ);\n            // Set the shape\n            point.shapeType = 'arc';\n            point.shapeArgs = {\n                x: positions[0],\n                y: positions[1],\n                r: pointRadii,\n                innerR: positions[3] / 2,\n                start: Math.round(start * precision) / precision,\n                end: Math.round(end * precision) / precision\n            };\n            // The angle must stay within -90 and 270 (#2645)\n            angle = (end + start) / 2;\n            if (angle > 1.5 * Math.PI) {\n                angle -= 2 * Math.PI;\n            }\n            else if (angle < -Math.PI / 2) {\n                angle += 2 * Math.PI;\n            }\n            // Center for the sliced out slice\n            point.slicedTranslation = {\n                translateX: Math.round(Math.cos(angle) * slicedOffset),\n                translateY: Math.round(Math.sin(angle) * slicedOffset)\n            };\n            // Set the anchor point for tooltips\n            radiusX = Math.cos(angle) * positions[2] / 2;\n            radiusY = Math.sin(angle) * positions[2] / 2;\n            point.tooltipPos = [\n                positions[0] + radiusX * 0.7,\n                positions[1] + radiusY * 0.7\n            ];\n            point.half = angle < -Math.PI / 2 || angle > Math.PI / 2 ?\n                1 :\n                0;\n            point.angle = angle;\n        }\n        fireEvent(series, 'afterTranslate');\n    };\n    /**\n     * For arrayMin and arrayMax calculations array shouldn't have\n     * null/undefined/string values. In this case it is needed to check if\n     * points Z value is a Number.\n     * @private\n     */\n    VariablePieSeries.prototype.zValEval = function (zVal) {\n        if (typeof zVal === 'number' && !isNaN(zVal)) {\n            return true;\n        }\n        return null;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    VariablePieSeries.defaultOptions = merge(PieSeries.defaultOptions, VariablePie_VariablePieSeriesDefaults);\n    return VariablePieSeries;\n}(PieSeries));\nextend(VariablePieSeries.prototype, {\n    pointArrayMap: ['y', 'z'],\n    parallelArrays: ['x', 'y', 'z']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('variablepie', VariablePieSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var VariablePie_VariablePieSeries = ((/* unused pure expression or super */ null && (VariablePieSeries)));\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @typedef {\"area\"|\"radius\"} Highcharts.VariablePieSizeByValue\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es5/es-modules/masters/modules/variable-pie.js\n\n\n\n\n/* harmony default export */ var variable_pie_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "variable_pie_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "VariablePie_VariablePieSeriesDefaults", "minPointSize", "maxPointSize", "zMin", "zMax", "sizeBy", "tooltip", "pointFormat", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "PieSeries", "seriesTypes", "pie", "arrayMax", "arrayMin", "clamp", "extend", "fireEvent", "merge", "pick", "VariablePieSeries", "_super", "apply", "arguments", "calculateExtremes", "chart", "series", "plot<PERSON>id<PERSON>", "plotHeight", "seriesOptions", "options", "slicingRoom", "slicedOffset", "zData", "getColumn", "smallestSize", "Math", "min", "extremes", "positions", "center", "getCenter", "_i", "_a", "length", "length_1", "isPercent", "test", "parseInt", "minPxSize", "maxPxSize", "filter", "zValEval", "getRadii", "minSize", "maxSize", "pos", "value", "radius", "radii", "sizeByArea", "zRange", "i", "sqrt", "ceil", "push", "redraw", "getDataLabelPosition", "point", "distance", "angle", "r", "index", "x", "cos", "y", "sin", "finalConnectorOffset", "borderWidth", "natural", "computed", "alignment", "half", "connectorPosition", "breakAt", "touchingSliceAt", "translate", "generatePoints", "start", "end", "radiusX", "radiusY", "pointRadii", "startAngle", "startAngleRad", "PI", "endAngleRad", "endAngle", "circ", "points", "ignoreHiddenPoint", "cumulative", "visible", "percentage", "shapeType", "shapeArgs", "innerR", "round", "slicedTranslation", "translateX", "translateY", "tooltipPos", "zVal", "isNaN", "defaultOptions", "pointArrayMap", "parallelArrays", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GAC5G,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAEnHJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IA6QFC,EA7QMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAkB,CACjE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAwKhJE,EArID,CAa5BC,aAAc,MAWdC,aAAc,OAcdC,KAAM,KAAK,EAYXC,KAAM,KAAK,EAaXC,OAAQ,OACRC,QAAS,CACLC,YAAa,wGACjB,CACJ,EAiFIC,GACItC,EAAgB,SAAUU,CAAC,CAC3B6B,CAAC,EAMD,MAAOvC,AALHA,CAAAA,EAAgBe,OAAOyB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUhC,CAAC,CAC1D6B,CAAC,EAAI7B,EAAE+B,SAAS,CAAGF,CAAG,GACd,SAAU7B,CAAC,CACnB6B,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAEjB,cAAc,CAACqB,IAAIjC,CAAAA,CAAC,CAACiC,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCjC,EAAG6B,EAC5B,EACO,SAAU7B,CAAC,CAAE6B,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAGnC,CAAG,CADtCV,EAAcU,EAAG6B,GAEjB7B,EAAEW,SAAS,CAAGkB,AAAM,OAANA,EAAaxB,OAAO+B,MAAM,CAACP,GAAMK,CAAAA,EAAGvB,SAAS,CAAGkB,EAAElB,SAAS,CAAE,IAAIuB,CAAG,CACtF,GAGAG,EAAY,AAAClB,IAA2ImB,WAAW,CAACC,GAAG,CAEvKC,EAAW,AAACvB,IAA+EuB,QAAQ,CAAEC,EAAW,AAACxB,IAA+EwB,QAAQ,CAAEC,EAAQ,AAACzB,IAA+EyB,KAAK,CAAEC,EAAS,AAAC1B,IAA+E0B,MAAM,CAAEC,EAAY,AAAC3B,IAA+E2B,SAAS,CAAEC,EAAQ,AAAC5B,IAA+E4B,KAAK,CAAEC,EAAO,AAAC7B,IAA+E6B,IAAI,CAgB3qBC,EAAmC,SAAUC,CAAM,EAEnD,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAwOA,OA3OAtB,EAAUmB,EAAmBC,GAc7BD,EAAkBpC,SAAS,CAACwC,iBAAiB,CAAG,WAiB5C,IAAK,IAFD5B,EACAC,EAdA4B,EAAQC,AADC,IAAI,CACED,KAAK,CACpBE,EAAYF,EAAME,SAAS,CAC3BC,EAAaH,EAAMG,UAAU,CAC7BC,EAAgBH,AAJP,IAAI,CAIUI,OAAO,CAC9BC,EAAc,EAAKF,CAAAA,EAAcG,YAAY,EAAI,CAAA,EACjDC,EAAQP,AANC,IAAI,CAMEQ,SAAS,CAAC,KACzBC,EAAeC,KAAKC,GAAG,CAACV,EACxBC,GAAcG,EAEdO,EAAW,CAAC,EAGZC,EAAYb,AAbH,IAAI,CAaMc,MAAM,EAAId,AAbpB,IAAI,CAauBe,SAAS,GAGxCC,EAAK,EAAGC,EAAK,CAAC,eAAgB,eAAe,CAAED,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CAC1E,IAAI3D,EAAO4D,CAAE,CAACD,EAAG,CACbG,EAAWhB,CAAa,CAAC9C,EAAK,CAC9B+D,EAAY,KAAKC,IAAI,CAACF,GAC1BA,EAAWG,SAASH,EAAU,IAC9BP,CAAQ,CAACvD,EAAK,CAAG+D,EACbX,EAAeU,EAAW,IAC1BA,AAAW,EAAXA,CACR,CACAnB,AAzBa,IAAI,CAyBVuB,SAAS,CAAGV,CAAS,CAAC,EAAE,CAAGD,EAAS5C,YAAY,CACvDgC,AA1Ba,IAAI,CA0BVwB,SAAS,CAAGnC,EAAMwB,CAAS,CAAC,EAAE,CAAEA,CAAS,CAAC,EAAE,CAAGD,EAAS5C,YAAY,CAAE4C,EAAS3C,YAAY,EAC9FsC,EAAMW,MAAM,GACZhD,EAAOuB,EAAKU,EAAcjC,IAAI,CAAEkB,EAASmB,EAAMkB,MAAM,CAACzB,AA5B7C,IAAI,CA4BgD0B,QAAQ,IACrEvD,EAAOsB,EAAKU,EAAchC,IAAI,CAAEgB,EAASoB,EAAMkB,MAAM,CAACzB,AA7B7C,IAAI,CA6BgD0B,QAAQ,IACrE,IAAI,CAACC,QAAQ,CAACzD,EAAMC,EAAM6B,AA9BjB,IAAI,CA8BoBuB,SAAS,CAAEvB,AA9BnC,IAAI,CA8BsCwB,SAAS,EAEpE,EAsBA9B,EAAkBpC,SAAS,CAACqE,QAAQ,CAAG,SAAUzD,CAAI,CAAEC,CAAI,CAAEyD,CAAO,CAAEC,CAAO,EAMzE,IAAK,IAJDC,EACAC,EACAC,EAHAzB,EAAQ,IAAI,CAACC,SAAS,CAAC,KAAMyB,EAAQ,EAAE,CAA0BC,EAAa9B,AAAmB,WAAnBA,AAA3B,IAAI,CAACA,OAAO,CAAuBhC,MAAM,CAAe+D,EAAShE,EAAOD,EAKtHkE,EAAI,EAAGA,EAAI7B,EAAMW,MAAM,CAAEkB,IAI1BL,AADJA,CAAAA,EAAQ,IAAI,CAACL,QAAQ,CAACnB,CAAK,CAAC6B,EAAE,EAAI7B,CAAK,CAAC6B,EAAE,CAAGlE,CAAG,GACnCA,EACT8D,EAASJ,EAAU,EAEdG,GAAS5D,EACd6D,EAASH,EAAU,GAInBC,EAAMK,EAAS,EAAI,AAACJ,CAAAA,EAAQ7D,CAAG,EAAKiE,EAAS,GACzCD,GACAJ,CAAAA,EAAMpB,KAAK2B,IAAI,CAACP,EAAG,EAEvBE,EAAStB,KAAK4B,IAAI,CAACV,EAAUE,EAAOD,CAAAA,EAAUD,CAAM,GAAM,GAE9DK,EAAMM,IAAI,CAACP,EAEf,CAAA,IAAI,CAACC,KAAK,CAAGA,CACjB,EAMAvC,EAAkBpC,SAAS,CAACkF,MAAM,CAAG,WACjC,IAAI,CAAC1B,MAAM,CAAG,KACdnB,EAAOrC,SAAS,CAACkF,MAAM,CAAChF,IAAI,CAAC,IAAI,CACrC,EAEAkC,EAAkBpC,SAAS,CAACmF,oBAAoB,CAAG,SAAUC,CAAK,CAAEC,CAAQ,EACxE,IAAe7B,EAASG,AAAf,IAAI,CAAcH,MAAM,CAAEV,EAAUa,AAApC,IAAI,CAAmCb,OAAO,CAAEwC,EAAQF,EAAME,KAAK,EAAI,EAAGC,EAAI,IAAI,CAACZ,KAAK,CAACS,EAAMI,KAAK,CAAC,CAAEC,EAAIjC,CAAM,CAAC,EAAE,CAAGJ,KAAKsC,GAAG,CAACJ,GAASC,EAAGI,EAAInC,CAAM,CAAC,EAAE,CAAGJ,KAAKwC,GAAG,CAACN,GAASC,EAKnLM,EAAuBzC,KAAKC,GAAG,CALyK,AAACP,CAAAA,EAAQE,YAAY,EAAI,CAAA,EAC5NF,CAAAA,EAAQgD,WAAW,EAAI,CAAA,EAIqBT,EAAW,GAC5D,MAAO,CACHA,SAAUA,EACVU,QAAS,CAGLN,EAAGA,EAAIrC,KAAKsC,GAAG,CAACJ,GAASD,EACzBM,EAAGA,EAAIvC,KAAKwC,GAAG,CAACN,GAASD,CAC7B,EACAW,SAAU,CAGV,EAGAC,UAAWb,EAAMc,IAAI,CAAG,QAAU,OAClCC,kBAAmB,CACfC,QAAS,CACLX,EAAGA,EAAIrC,KAAKsC,GAAG,CAACJ,GAASO,EACzBF,EAAGA,EAAIvC,KAAKwC,GAAG,CAACN,GAASO,CAC7B,EACAQ,gBAAiB,CACbZ,EAAGA,EACHE,EAAGA,CACP,CACJ,CACJ,CACR,EAMAvD,EAAkBpC,SAAS,CAACsG,SAAS,CAAG,SAAU/C,CAAS,EACvD,IAAI,CAACgD,cAAc,GACnB,IAIIC,EACAC,EACAnB,EAEAoB,EACAC,EACAvB,EACAwB,EAVA9D,EAAUJ,AADD,IAAI,CACII,OAAO,CAAEE,EAAeF,EAAQE,YAAY,CAAE6D,EAAa/D,EAAQ+D,UAAU,EAAI,EAAGC,EAAgB1D,KAAK2D,EAAE,CAAG,IAAOF,CAAAA,EAAa,EAAC,EAAIG,EAAc5D,KAAK2D,EAAE,CAAG,IAAO5E,CAAAA,EAAKW,EAAQmE,QAAQ,CAAEJ,EAAa,KAAO,EAAC,EAAIK,EAAOF,EAAcF,EAC5PK,EAASzE,AAFA,IAAI,CAEGyE,MAAM,CAAEC,EAAoBtE,EAAQsE,iBAAiB,CACrEC,EAAa,CASjB3E,CAZa,IAAI,CAYVoE,aAAa,CAAGA,EACvBpE,AAba,IAAI,CAaVsE,WAAW,CAAGA,EAErBtE,AAfa,IAAI,CAeVF,iBAAiB,GAInBe,GACDb,CAAAA,AApBS,IAAI,CAoBNc,MAAM,CAAGD,EAAYb,AApBnB,IAAI,CAoBsBe,SAAS,EAAC,EAGjD,IAAK,IAAIqB,EAAI,EAAGA,EAAIqC,EAAOvD,MAAM,CAAEkB,IAC/BM,EAAQ+B,CAAM,CAACrC,EAAE,CACjB8B,EAAalE,AAzBJ,IAAI,CAyBOiC,KAAK,CAACG,EAAE,CAE5B0B,EAAQM,EAAiBO,EAAaH,EAClC,CAAA,CAACE,GAAqBhC,EAAMkC,OAAO,AAAD,GAClCD,CAAAA,GAAcjC,EAAMmC,UAAU,CAAG,GAAE,EAEvCd,EAAMK,EAAiBO,EAAaH,EAEpC9B,EAAMoC,SAAS,CAAG,MAClBpC,EAAMqC,SAAS,CAAG,CACdhC,EAAGlC,CAAS,CAAC,EAAE,CACfoC,EAAGpC,CAAS,CAAC,EAAE,CACfgC,EAAGqB,EACHc,OAAQnE,CAAS,CAAC,EAAE,CAAG,EACvBiD,MAAOpD,KAAKuE,KAAK,CAACnB,AAvCK,IAuCLA,GAvCK,IAwCvBC,IAAKrD,KAAKuE,KAAK,CAAClB,AAxCO,IAwCPA,GAxCO,GAyC3B,EAGInB,AADJA,CAAAA,EAAQ,AAACmB,CAAAA,EAAMD,CAAI,EAAK,CAAA,EACZ,IAAMpD,KAAK2D,EAAE,CACrBzB,GAAS,EAAIlC,KAAK2D,EAAE,CAEfzB,EAAQ,CAAClC,KAAK2D,EAAE,CAAG,GACxBzB,CAAAA,GAAS,EAAIlC,KAAK2D,EAAE,AAAD,EAGvB3B,EAAMwC,iBAAiB,CAAG,CACtBC,WAAYzE,KAAKuE,KAAK,CAACvE,KAAKsC,GAAG,CAACJ,GAAStC,GACzC8E,WAAY1E,KAAKuE,KAAK,CAACvE,KAAKwC,GAAG,CAACN,GAAStC,EAC7C,EAEA0D,EAAUtD,KAAKsC,GAAG,CAACJ,GAAS/B,CAAS,CAAC,EAAE,CAAG,EAC3CoD,EAAUvD,KAAKwC,GAAG,CAACN,GAAS/B,CAAS,CAAC,EAAE,CAAG,EAC3C6B,EAAM2C,UAAU,CAAG,CACfxE,CAAS,CAAC,EAAE,CAAGmD,AAAU,GAAVA,EACfnD,CAAS,CAAC,EAAE,CAAGoD,AAAU,GAAVA,EAClB,CACDvB,EAAMc,IAAI,CAAGZ,CAAAA,CAAAA,EAAQ,CAAClC,KAAK2D,EAAE,CAAG,GAAKzB,EAAQlC,KAAK2D,EAAE,CAAG,CAAA,EAGvD3B,EAAME,KAAK,CAAGA,EAElBrD,EAnEa,IAAI,CAmEC,iBACtB,EAOAG,EAAkBpC,SAAS,CAACoE,QAAQ,CAAG,SAAU4D,CAAI,QACjD,CAAI,CAAA,AAAgB,UAAhB,OAAOA,GAAsBC,MAAMD,EAAI,GAGpC,IACX,EAMA5F,EAAkB8F,cAAc,CAAGhG,EAAMR,EAAUwG,cAAc,CAAEzH,GAC5D2B,CACX,EAAEV,GACFM,EAAOI,EAAkBpC,SAAS,CAAE,CAChCmI,cAAe,CAAC,IAAK,IAAI,CACzBC,eAAgB,CAAC,IAAK,IAAK,IAAI,AACnC,GACA5H,IAA0I6H,kBAAkB,CAAC,cAAejG,GAsB/I,IAAIhC,EAAqBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,GAET"}