{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/pictorial\n * @requires highcharts\n *\n * Pictorial graph series type for Highcharts\n *\n * (c) 2010-2025 <PERSON>stein Honsi, Magdalena Gut\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Series\"][\"types\"][\"column\"], require(\"highcharts\")[\"Chart\"], require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"StackItem\"], require(\"highcharts\")[\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/pictorial\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Series\",\"types\",\"column\"], [\"highcharts/highcharts\",\"Chart\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"Series\"], [\"highcharts/highcharts\",\"StackItem\"], [\"highcharts/highcharts\",\"SVGRenderer\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/pictorial\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Series\"][\"types\"][\"column\"], require(\"highcharts\")[\"Chart\"], require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"StackItem\"], require(\"highcharts\")[\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Series\"][\"types\"][\"column\"], root[\"Highcharts\"][\"Chart\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"StackItem\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__448__, __WEBPACK_EXTERNAL_MODULE__960__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__184__, __WEBPACK_EXTERNAL_MODULE__540__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 184:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__184__;\n\n/***/ }),\n\n/***/ 448:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__448__;\n\n/***/ }),\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 820:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ pictorial_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\",\"types\",\"column\"],\"commonjs\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"commonjs2\":[\"highcharts\",\"Series\",\"types\",\"column\"],\"root\":[\"Highcharts\",\"Series\",\"types\",\"column\"]}\nvar highcharts_Series_types_column_commonjs_highcharts_Series_types_column_commonjs2_highcharts_Series_types_column_root_Highcharts_Series_types_column_ = __webpack_require__(448);\n;// ./code/es5/es-modules/Extensions/PatternFill.js\n/* *\n *\n *  Module for using patterns or images as point fills.\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Torstein Hønsi, Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\n\nvar getOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getOptions;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, erase = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).erase, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, removeEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).removeEvent, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Constants\n *\n * */\nvar patterns = createPatterns();\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction compose(ChartClass, SeriesClass, SVGRendererClass) {\n    var PointClass = SeriesClass.prototype.pointClass,\n        pointProto = PointClass.prototype;\n    if (!pointProto.calculatePatternDimensions) {\n        addEvent(ChartClass, 'endResize', onChartEndResize);\n        addEvent(ChartClass, 'redraw', onChartRedraw);\n        extend(pointProto, {\n            calculatePatternDimensions: pointCalculatePatternDimensions\n        });\n        addEvent(PointClass, 'afterInit', onPointAfterInit);\n        addEvent(SeriesClass, 'render', onSeriesRender);\n        wrap(SeriesClass.prototype, 'getColor', wrapSeriesGetColor);\n        // Pattern scale corrections\n        addEvent(SeriesClass, 'afterRender', onPatternScaleCorrection);\n        addEvent(SeriesClass, 'mapZoomComplete', onPatternScaleCorrection);\n        extend(SVGRendererClass.prototype, {\n            addPattern: rendererAddPattern\n        });\n        addEvent(SVGRendererClass, 'complexColor', onRendererComplexColor);\n    }\n}\n/**\n * Add the predefined patterns.\n * @private\n */\nfunction createPatterns() {\n    var patterns = [],\n        colors = getOptions().colors;\n    // Start with subtle patterns\n    var i = 0;\n    for (var _i = 0, _a = [\n        'M 0 0 L 5 5 M 4.5 -0.5 L 5.5 0.5 M -0.5 4.5 L 0.5 5.5',\n        'M 0 5 L 5 0 M -0.5 0.5 L 0.5 -0.5 M 4.5 5.5 L 5.5 4.5',\n        'M 2 0 L 2 5 M 4 0 L 4 5',\n        'M 0 2 L 5 2 M 0 4 L 5 4',\n        'M 0 1.5 L 2.5 1.5 L 2.5 0 M 2.5 5 L 2.5 3.5 L 5 3.5'\n    ]; _i < _a.length; _i++) {\n        var pattern = _a[_i];\n        patterns.push({\n            path: pattern,\n            color: colors[i++],\n            width: 5,\n            height: 5,\n            patternTransform: 'scale(1.4 1.4)'\n        });\n    }\n    // Then add the more drastic ones\n    i = 5;\n    for (var _b = 0, _c = [\n        'M 0 0 L 5 10 L 10 0',\n        'M 3 3 L 8 3 L 8 8 L 3 8 Z',\n        'M 5 5 m -4 0 a 4 4 0 1 1 8 0 a 4 4 0 1 1 -8 0',\n        'M 0 0 L 10 10 M 9 -1 L 11 1 M -1 9 L 1 11',\n        'M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9'\n    ]; _b < _c.length; _b++) {\n        var pattern = _c[_b];\n        patterns.push({\n            path: pattern,\n            color: colors[i],\n            width: 10,\n            height: 10\n        });\n        i = i + 5;\n    }\n    return patterns;\n}\n/**\n * Utility function to compute a hash value from an object. Modified Java\n * String.hashCode implementation in JS. Use the preSeed parameter to add an\n * additional seeding step.\n *\n * @private\n * @function hashFromObject\n *\n * @param {Object} obj\n *        The javascript object to compute the hash from.\n *\n * @param {boolean} [preSeed=false]\n *        Add an optional preSeed stage.\n *\n * @return {string}\n *         The computed hash.\n */\nfunction hashFromObject(obj, preSeed) {\n    var str = JSON.stringify(obj),\n        strLen = str.length || 0;\n    var hash = 0,\n        i = 0,\n        char,\n        seedStep;\n    if (preSeed) {\n        seedStep = Math.max(Math.floor(strLen / 500), 1);\n        for (var a = 0; a < strLen; a += seedStep) {\n            hash += str.charCodeAt(a);\n        }\n        hash = hash & hash;\n    }\n    for (; i < strLen; ++i) {\n        char = str.charCodeAt(i);\n        hash = ((hash << 5) - hash) + char;\n        hash = hash & hash;\n    }\n    return hash.toString(16).replace('-', '1');\n}\n/**\n * When animation is used, we have to recalculate pattern dimensions after\n * resize, as the bounding boxes are not available until then.\n * @private\n */\nfunction onChartEndResize() {\n    if (this.renderer &&\n        (this.renderer.defIds || []).filter(function (id) { return (id &&\n            id.indexOf &&\n            id.indexOf('highcharts-pattern-') === 0); }).length) {\n        // We have non-default patterns to fix. Find them by looping through\n        // all points.\n        for (var _i = 0, _a = this.series; _i < _a.length; _i++) {\n            var series = _a[_i];\n            if (series.visible) {\n                for (var _b = 0, _c = series.points; _b < _c.length; _b++) {\n                    var point = _c[_b];\n                    var colorOptions = point.options && point.options.color;\n                    if (colorOptions &&\n                        colorOptions.pattern) {\n                        colorOptions.pattern\n                            ._width = 'defer';\n                        colorOptions.pattern\n                            ._height = 'defer';\n                    }\n                }\n            }\n        }\n        // Redraw without animation\n        this.redraw(false);\n    }\n}\n/**\n * Add a garbage collector to delete old patterns with autogenerated hashes that\n * are no longer being referenced.\n * @private\n */\nfunction onChartRedraw() {\n    var usedIds = {},\n        renderer = this.renderer, \n        // Get the autocomputed patterns - these are the ones we might delete\n        patterns = (renderer.defIds || []).filter(function (pattern) { return (pattern.indexOf &&\n            pattern.indexOf('highcharts-pattern-') === 0); });\n    if (patterns.length) {\n        // Look through the DOM for usage of the patterns. This can be points,\n        // series, tooltips etc.\n        [].forEach.call(this.renderTo.querySelectorAll('[color^=\"url(\"], [fill^=\"url(\"], [stroke^=\"url(\"]'), function (node) {\n            var id = node.getAttribute('fill') ||\n                    node.getAttribute('color') ||\n                    node.getAttribute('stroke');\n            if (id) {\n                var sanitizedId = id\n                        .replace(renderer.url, '')\n                        .replace('url(#', '')\n                        .replace(')', '');\n                usedIds[sanitizedId] = true;\n            }\n        });\n        // Loop through the patterns that exist and see if they are used\n        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {\n            var id = patterns_1[_i];\n            if (!usedIds[id]) {\n                // Remove id from used id list\n                erase(renderer.defIds, id);\n                // Remove pattern element\n                if (renderer.patternElements[id]) {\n                    renderer.patternElements[id].destroy();\n                    delete renderer.patternElements[id];\n                }\n            }\n        }\n    }\n}\n/**\n * Merge series color options to points.\n * @private\n */\nfunction onPointAfterInit() {\n    var point = this,\n        colorOptions = point.options.color;\n    // Only do this if we have defined a specific color on this point. Otherwise\n    // we will end up trying to re-add the series color for each point.\n    if (colorOptions && colorOptions.pattern) {\n        // Move path definition to object, allows for merge with series path\n        // definition\n        if (typeof colorOptions.pattern.path === 'string') {\n            colorOptions.pattern.path = {\n                d: colorOptions.pattern.path\n            };\n        }\n        // Merge with series options\n        point.color = point.options.color = merge(point.series.options.color, colorOptions);\n    }\n}\n/**\n * Add functionality to SVG renderer to handle patterns as complex colors.\n * @private\n */\nfunction onRendererComplexColor(args) {\n    var color = args.args[0],\n        prop = args.args[1],\n        element = args.args[2],\n        chartIndex = (this.chartIndex || 0);\n    var pattern = color.pattern, value = \"#333333\" /* Palette.neutralColor80 */;\n    // Handle patternIndex\n    if (typeof color.patternIndex !== 'undefined' && patterns) {\n        pattern = patterns[color.patternIndex];\n    }\n    // Skip and call default if there is no pattern\n    if (!pattern) {\n        return true;\n    }\n    // We have a pattern.\n    if (pattern.image ||\n        typeof pattern.path === 'string' ||\n        pattern.path && pattern.path.d) {\n        // Real pattern. Add it and set the color value to be a reference.\n        // Force Hash-based IDs for legend items, as they are drawn before\n        // point render, meaning they are drawn before autocalculated image\n        // width/heights. We don't want them to highjack the width/height for\n        // this ID if it is defined by users.\n        var forceHashId = element.parentNode &&\n                element.parentNode.getAttribute('class');\n        forceHashId = forceHashId &&\n            forceHashId.indexOf('highcharts-legend') > -1;\n        // If we don't have a width/height yet, handle it. Try faking a point\n        // and running the algorithm again.\n        if (pattern._width === 'defer' || pattern._height === 'defer') {\n            pointCalculatePatternDimensions.call({ graphic: { element: element } }, pattern);\n        }\n        // If we don't have an explicit ID, compute a hash from the\n        // definition and use that as the ID. This ensures that points with\n        // the same pattern definition reuse existing pattern elements by\n        // default. We combine two hashes, the second with an additional\n        // preSeed algorithm, to minimize collision probability.\n        if (forceHashId || !pattern.id) {\n            // Make a copy so we don't accidentally edit options when setting ID\n            pattern = merge({}, pattern);\n            pattern.id = 'highcharts-pattern-' + chartIndex + '-' +\n                hashFromObject(pattern) + hashFromObject(pattern, true);\n        }\n        // Add it. This function does nothing if an element with this ID\n        // already exists.\n        this.addPattern(pattern, !this.forExport && pick(pattern.animation, this.globalAnimation, { duration: 100 }));\n        value = \"url(\".concat(this.url, \"#\").concat(pattern.id + (this.forExport ? '-export' : ''), \")\");\n    }\n    else {\n        // Not a full pattern definition, just add color\n        value = pattern.color || value;\n    }\n    // Set the fill/stroke prop on the element\n    element.setAttribute(prop, value);\n    // Allow the color to be concatenated into tooltips formatters etc.\n    color.toString = function () {\n        return value;\n    };\n    // Skip default handler\n    return false;\n}\n/**\n * Calculate pattern dimensions on points that have their own pattern.\n * @private\n */\nfunction onSeriesRender() {\n    var isResizing = this.chart.isResizing;\n    if (this.isDirtyData || isResizing || !this.chart.hasRendered) {\n        for (var _i = 0, _a = this.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            var colorOptions = point.options && point.options.color;\n            if (colorOptions &&\n                colorOptions.pattern) {\n                // For most points we want to recalculate the dimensions on\n                // render, where we have the shape args and bbox. But if we\n                // are resizing and don't have the shape args, defer it, since\n                // the bounding box is still not resized.\n                if (isResizing &&\n                    !(point.shapeArgs &&\n                        point.shapeArgs.width &&\n                        point.shapeArgs.height)) {\n                    colorOptions\n                        .pattern._width = 'defer';\n                    colorOptions\n                        .pattern._height = 'defer';\n                }\n                else {\n                    point.calculatePatternDimensions(colorOptions.pattern);\n                }\n            }\n        }\n    }\n}\n/**\n * Set dimensions on pattern from point. This function will set internal\n * pattern._width/_height properties if width and height are not both already\n * set. We only do this on image patterns. The _width/_height properties are set\n * to the size of the bounding box of the point, optionally taking aspect ratio\n * into account. If only one of width or height are supplied as options, the\n * undefined option is calculated as above.\n *\n * @private\n * @function Highcharts.Point#calculatePatternDimensions\n *\n * @param {Highcharts.PatternOptionsObject} pattern\n *        The pattern to set dimensions on.\n *\n * @return {void}\n *\n * @requires modules/pattern-fill\n */\nfunction pointCalculatePatternDimensions(pattern) {\n    if (pattern.width && pattern.height) {\n        return;\n    }\n    var bBox = this.graphic && (this.graphic.getBBox &&\n            this.graphic.getBBox(true) ||\n            this.graphic.element &&\n                this.graphic.element.getBBox()) || {},\n        shapeArgs = this.shapeArgs;\n    // Prefer using shapeArgs, as it is animation agnostic\n    if (shapeArgs) {\n        bBox.width = shapeArgs.width || bBox.width;\n        bBox.height = shapeArgs.height || bBox.height;\n        bBox.x = shapeArgs.x || bBox.x;\n        bBox.y = shapeArgs.y || bBox.y;\n    }\n    // For images we stretch to bounding box\n    if (pattern.image) {\n        // If we do not have a bounding box at this point, simply add a defer\n        // key and pick this up in the fillSetter handler, where the bounding\n        // box should exist.\n        if (!bBox.width || !bBox.height) {\n            pattern._width = 'defer';\n            pattern._height = 'defer';\n            // Mark the pattern to be flipped later if upside down (#16810)\n            var scaleY = this.series.chart.mapView &&\n                    this.series.chart.mapView.getSVGTransform().scaleY;\n            if (defined(scaleY) && scaleY < 0) {\n                pattern._inverted = true;\n            }\n            return;\n        }\n        // Handle aspect ratio filling\n        if (pattern.aspectRatio) {\n            bBox.aspectRatio = bBox.width / bBox.height;\n            if (pattern.aspectRatio > bBox.aspectRatio) {\n                // Height of bBox will determine width\n                bBox.aspectWidth = bBox.height * pattern.aspectRatio;\n            }\n            else {\n                // Width of bBox will determine height\n                bBox.aspectHeight = bBox.width / pattern.aspectRatio;\n            }\n        }\n        // We set the width/height on internal properties to differentiate\n        // between the options set by a user and by this function.\n        pattern._width = pattern.width ||\n            Math.ceil(bBox.aspectWidth || bBox.width);\n        pattern._height = pattern.height ||\n            Math.ceil(bBox.aspectHeight || bBox.height);\n    }\n    // Set x/y accordingly, centering if using aspect ratio, otherwise adjusting\n    // so bounding box corner is 0,0 of pattern.\n    if (!pattern.width) {\n        pattern._x = pattern.x || 0;\n        pattern._x += bBox.x - Math.round(bBox.aspectWidth ?\n            Math.abs(bBox.aspectWidth - bBox.width) / 2 :\n            0);\n    }\n    if (!pattern.height) {\n        pattern._y = pattern.y || 0;\n        pattern._y += bBox.y - Math.round(bBox.aspectHeight ?\n            Math.abs(bBox.aspectHeight - bBox.height) / 2 :\n            0);\n    }\n}\n/**\n * Add a pattern to the renderer.\n *\n * @private\n * @function Highcharts.SVGRenderer#addPattern\n *\n * @param {Highcharts.PatternObject} options\n * The pattern options.\n *\n * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation]\n * The animation options.\n *\n * @return {Highcharts.SVGElement|undefined}\n * The added pattern. Undefined if the pattern already exists.\n *\n * @requires modules/pattern-fill\n */\nfunction rendererAddPattern(options, animation) {\n    var _this = this;\n    var animate = pick(animation, true), animationOptions = animObject(animate), color = options.color || \"#333333\" /* Palette.neutralColor80 */, defaultSize = 32, height = options.height ||\n            (typeof options._height === 'number' ? options._height : 0) ||\n            defaultSize, rect = function (fill) { return _this\n            .rect(0, 0, width, height)\n            .attr({ fill: fill })\n            .add(pattern); }, width = options.width ||\n            (typeof options._width === 'number' ? options._width : 0) ||\n            defaultSize;\n    var attribs,\n        id = options.id,\n        path;\n    if (!id) {\n        this.idCounter = this.idCounter || 0;\n        id = ('highcharts-pattern-' +\n            this.idCounter +\n            '-' +\n            (this.chartIndex || 0));\n        ++this.idCounter;\n    }\n    if (this.forExport) {\n        id += '-export';\n    }\n    // Do nothing if ID already exists\n    this.defIds = this.defIds || [];\n    if (this.defIds.indexOf(id) > -1) {\n        return;\n    }\n    // Store ID in list to avoid duplicates\n    this.defIds.push(id);\n    // Calculate pattern element attributes\n    var attrs = {\n            id: id,\n            patternUnits: 'userSpaceOnUse',\n            patternContentUnits: options.patternContentUnits || 'userSpaceOnUse',\n            width: width,\n            height: height,\n            x: options._x || options.x || 0,\n            y: options._y || options.y || 0\n        };\n    if (options._inverted) {\n        attrs.patternTransform = 'scale(1, -1)'; // (#16810)\n        if (options.patternTransform) {\n            options.patternTransform += ' scale(1, -1)';\n        }\n    }\n    if (options.patternTransform) {\n        attrs.patternTransform = options.patternTransform;\n    }\n    var pattern = this.createElement('pattern').attr(attrs).add(this.defs);\n    // Set id on the SVGRenderer object\n    pattern.id = id;\n    // Use an SVG path for the pattern\n    if (options.path) {\n        path = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(options.path) ?\n            options.path :\n            { d: options.path };\n        // The background\n        if (options.backgroundColor) {\n            rect(options.backgroundColor);\n        }\n        // The pattern\n        attribs = {\n            'd': path.d\n        };\n        if (!this.styledMode) {\n            attribs.stroke = path.stroke || color;\n            attribs['stroke-width'] = pick(path.strokeWidth, 2);\n            attribs.fill = path.fill || 'none';\n        }\n        if (path.transform) {\n            attribs.transform = path.transform;\n        }\n        this.createElement('path').attr(attribs).add(pattern);\n        pattern.color = color;\n        // Image pattern\n    }\n    else if (options.image) {\n        if (animate) {\n            this.image(options.image, 0, 0, width, height, function () {\n                // Onload\n                this.animate({\n                    opacity: pick(options.opacity, 1)\n                }, animationOptions);\n                removeEvent(this.element, 'load');\n            }).attr({ opacity: 0 }).add(pattern);\n        }\n        else {\n            this.image(options.image, 0, 0, width, height).add(pattern);\n        }\n    }\n    // For non-animated patterns, set opacity now\n    if (!(options.image && animate) && typeof options.opacity !== 'undefined') {\n        [].forEach.call(pattern.element.childNodes, function (child) {\n            child.setAttribute('opacity', options.opacity);\n        });\n    }\n    // Store for future reference\n    this.patternElements = this.patternElements || {};\n    this.patternElements[id] = pattern;\n    return pattern;\n}\n/**\n * Make sure we have a series color.\n * @private\n */\nfunction wrapSeriesGetColor(proceed) {\n    var oldColor = this.options.color;\n    // Temporarily remove color options to get defaults\n    if (oldColor &&\n        oldColor.pattern &&\n        !oldColor.pattern.color) {\n        delete this.options.color;\n        // Get default\n        proceed.apply(this, [].slice.call(arguments, 1));\n        // Replace with old, but add default color\n        oldColor.pattern.color =\n            this.color;\n        this.color = this.options.color = oldColor;\n    }\n    else {\n        // We have a color, no need to do anything special\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Scale patterns inversely to the series it's used in.\n * Maintains a visual (1,1) scale regardless of size.\n * @private\n */\nfunction onPatternScaleCorrection() {\n    var _a,\n        _b;\n    var series = this;\n    // If not a series used in a map chart, skip it.\n    if (!((_a = series.chart) === null || _a === void 0 ? void 0 : _a.mapView)) {\n        return;\n    }\n    var chart = series.chart,\n        renderer = chart.renderer,\n        patterns = renderer.patternElements;\n    // Only scale if we have patterns to scale.\n    if (((_b = renderer.defIds) === null || _b === void 0 ? void 0 : _b.length) && patterns) {\n        // Filter for points which have patterns that don't use images assigned\n        // and has a group scale available.\n        series.points.filter(function (p) {\n            var _a,\n                _b,\n                _c,\n                _d;\n            var point = p;\n            // No graphic we can fetch id from, filter out this point.\n            if (!point.graphic) {\n                return false;\n            }\n            return (point.graphic.element.hasAttribute('fill') ||\n                point.graphic.element.hasAttribute('color') ||\n                point.graphic.element.hasAttribute('stroke')) &&\n                !((_b = (_a = point.options.color) === null || _a === void 0 ? void 0 : _a.pattern) === null || _b === void 0 ? void 0 : _b.image) &&\n                !!((_c = point.group) === null || _c === void 0 ? void 0 : _c.scaleX) &&\n                !!((_d = point.group) === null || _d === void 0 ? void 0 : _d.scaleY);\n        })\n            // Map up pattern id's and their scales.\n            .map(function (p) {\n            var _a,\n                _b,\n                _c,\n                _d,\n                _e;\n            var point = p;\n            // Parse the id from the graphic element of the point.\n            var id = (((_a = point.graphic) === null || _a === void 0 ? void 0 : _a.element.getAttribute('fill')) ||\n                    ((_b = point.graphic) === null || _b === void 0 ? void 0 : _b.element.getAttribute('color')) ||\n                    ((_c = point.graphic) === null || _c === void 0 ? void 0 : _c.element.getAttribute('stroke')) || '')\n                    .replace(renderer.url, '')\n                    .replace('url(#', '')\n                    .replace(')', '');\n            return {\n                id: id,\n                x: ((_d = point.group) === null || _d === void 0 ? void 0 : _d.scaleX) || 1,\n                y: ((_e = point.group) === null || _e === void 0 ? void 0 : _e.scaleY) || 1\n            };\n        })\n            // Filter out colors and other non-patterns, as well as duplicates.\n            .filter(function (pointInfo, index, arr) {\n            return pointInfo.id !== '' &&\n                pointInfo.id.indexOf('highcharts-pattern-') !== -1 &&\n                !arr.some(function (otherInfo, otherIndex) {\n                    return otherInfo.id === pointInfo.id && otherIndex < index;\n                });\n        })\n            .forEach(function (pointInfo) {\n            var id = pointInfo.id;\n            patterns[id].scaleX = 1 / pointInfo.x;\n            patterns[id].scaleY = 1 / pointInfo.y;\n            patterns[id].updateTransform('patternTransform');\n        });\n    }\n}\n/* *\n *\n *  Export\n *\n * */\nvar PatternFill = {\n    compose: compose,\n    patterns: patterns\n};\n/* harmony default export */ var Extensions_PatternFill = (PatternFill);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Pattern options\n *\n * @interface Highcharts.PatternOptionsObject\n */ /**\n* Background color for the pattern if a `path` is set (not images).\n* @name Highcharts.PatternOptionsObject#backgroundColor\n* @type {Highcharts.ColorString|undefined}\n*/ /**\n* URL to an image to use as the pattern.\n* @name Highcharts.PatternOptionsObject#image\n* @type {string|undefined}\n*/ /**\n* Width of the pattern. For images this is automatically set to the width of\n* the element bounding box if not supplied. For non-image patterns the default\n* is 32px. Note that automatic resizing of image patterns to fill a bounding\n* box dynamically is only supported for patterns with an automatically\n* calculated ID.\n* @name Highcharts.PatternOptionsObject#width\n* @type {number|undefined}\n*/ /**\n* Analogous to pattern.width.\n* @name Highcharts.PatternOptionsObject#height\n* @type {number|undefined}\n*/ /**\n* For automatically calculated width and height on images, it is possible to\n* set an aspect ratio. The image will be zoomed to fill the bounding box,\n* maintaining the aspect ratio defined.\n* @name Highcharts.PatternOptionsObject#aspectRatio\n* @type {number|undefined}\n*/ /**\n* Horizontal offset of the pattern. Defaults to 0.\n* @name Highcharts.PatternOptionsObject#x\n* @type {number|undefined}\n*/ /**\n* Vertical offset of the pattern. Defaults to 0.\n* @name Highcharts.PatternOptionsObject#y\n* @type {number|undefined}\n*/ /**\n* Either an SVG path as string, or an object. As an object, supply the path\n* string in the `path.d` property. Other supported properties are standard SVG\n* attributes like `path.stroke` and `path.fill`. If a path is supplied for the\n* pattern, the `image` property is ignored.\n* @name Highcharts.PatternOptionsObject#path\n* @type {string|Highcharts.SVGAttributes|undefined}\n*/ /**\n* SVG `patternTransform` to apply to the entire pattern.\n* @name Highcharts.PatternOptionsObject#patternTransform\n* @type {string|undefined}\n* @see [patternTransform demo](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/series/pattern-fill-transform)\n*/ /**\n* Pattern color, used as default path stroke.\n* @name Highcharts.PatternOptionsObject#color\n* @type {Highcharts.ColorString|undefined}\n*/ /**\n* Opacity of the pattern as a float value from 0 to 1.\n* @name Highcharts.PatternOptionsObject#opacity\n* @type {number|undefined}\n*/ /**\n* ID to assign to the pattern. This is automatically computed if not added, and\n* identical patterns are reused. To refer to an existing pattern for a\n* Highcharts color, use `color: \"url(#pattern-id)\"`.\n* @name Highcharts.PatternOptionsObject#id\n* @type {string|undefined}\n*/\n/**\n * Holds a pattern definition.\n *\n * @sample highcharts/series/pattern-fill-area/\n *         Define a custom path pattern\n * @sample highcharts/series/pattern-fill-pie/\n *         Default patterns and a custom image pattern\n * @sample maps/demo/pattern-fill-map/\n *         Custom images on map\n *\n * @example\n * // Pattern used as a color option\n * color: {\n *     pattern: {\n *            path: {\n *                 d: 'M 3 3 L 8 3 L 8 8 Z',\n *                fill: '#102045'\n *            },\n *            width: 12,\n *            height: 12,\n *            color: '#907000',\n *            opacity: 0.5\n *     }\n * }\n *\n * @interface Highcharts.PatternObject\n */ /**\n* Pattern options\n* @name Highcharts.PatternObject#pattern\n* @type {Highcharts.PatternOptionsObject}\n*/ /**\n* Animation options for the image pattern loading.\n* @name Highcharts.PatternObject#animation\n* @type {boolean|Partial<Highcharts.AnimationOptionsObject>|undefined}\n*/ /**\n* Optionally an index referencing which pattern to use. Highcharts adds\n* 10 default patterns to the `Highcharts.patterns` array. Additional\n* pattern definitions can be pushed to this array if desired. This option\n* is an index into this array.\n* @name Highcharts.PatternObject#patternIndex\n* @type {number|undefined}\n*/\n''; // Keeps doclets above in transpiled file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Pictorial/PictorialUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi, Magdalena Gut\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar PictorialUtilities_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined;\n/**\n *\n */\nfunction rescalePatternFill(element, stackHeight, width, height, borderWidth) {\n    if (borderWidth === void 0) { borderWidth = 1; }\n    var fill = element && element.attr('fill'), match = fill && fill.match(/url\\(([^)]+)\\)/);\n    if (match) {\n        var patternPath = document.querySelector(\"\" + match[1] + \" path\");\n        if (patternPath) {\n            var bBox = patternPath.getBBox();\n            // Firefox (v108/Mac) is unable to detect the bounding box within\n            // defs. Without this block, the pictorial is not rendered.\n            if (bBox.width === 0) {\n                var parent_1 = patternPath.parentElement;\n                // Temporarily append it to the root\n                element.renderer.box.appendChild(patternPath);\n                bBox = patternPath.getBBox();\n                parent_1.appendChild(patternPath);\n            }\n            var scaleX = 1 / (bBox.width + borderWidth);\n            var scaleY = stackHeight / height / bBox.height, aspectRatio = bBox.width / bBox.height, pointAspectRatio = width / stackHeight, x = -bBox.width / 2;\n            if (aspectRatio < pointAspectRatio) {\n                scaleX = scaleX * aspectRatio / pointAspectRatio;\n            }\n            patternPath.setAttribute('stroke-width', borderWidth / (width * scaleX));\n            patternPath.setAttribute('transform', 'translate(0.5, 0)' +\n                \"scale(\".concat(scaleX, \" \").concat(scaleY, \") \") +\n                \"translate(\".concat(x + borderWidth * scaleX / 2, \", \").concat(-bBox.y, \")\"));\n        }\n    }\n}\n/**\n *\n */\nfunction getStackMetrics(yAxis, shape) {\n    var height = yAxis.len,\n        y = 0;\n    if (shape && PictorialUtilities_defined(shape.max)) {\n        y = yAxis.toPixels(shape.max, true);\n        height = yAxis.len - y;\n    }\n    return {\n        height: height,\n        y: y\n    };\n}\n/**\n *\n */\nfunction invertShadowGroup(shadowGroup, yAxis) {\n    var inverted = yAxis.chart.inverted;\n    if (inverted) {\n        shadowGroup.attr({\n            rotation: inverted ? 90 : 0,\n            scaleX: inverted ? -1 : 1\n        });\n    }\n}\n/* harmony default export */ var PictorialUtilities = ({ rescalePatternFill: rescalePatternFill, invertShadowGroup: invertShadowGroup, getStackMetrics: getStackMetrics });\n\n;// ./code/es5/es-modules/Series/Pictorial/PictorialPoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi, Magdalena Gut\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\nvar PictorialPoint_rescalePatternFill = PictorialUtilities.rescalePatternFill, PictorialPoint_getStackMetrics = PictorialUtilities.getStackMetrics;\n/* *\n *\n *  Class\n *\n * */\nvar PictorialPoint = /** @class */ (function (_super) {\n    __extends(PictorialPoint, _super);\n    function PictorialPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    PictorialPoint.prototype.setState = function () {\n        var point = this;\n        _super.prototype.setState.apply(point, arguments);\n        var series = point.series,\n            paths = series.options.paths;\n        if (point.graphic && point.shapeArgs && paths) {\n            var shape = paths[point.index %\n                    paths.length];\n            PictorialPoint_rescalePatternFill(point.graphic, PictorialPoint_getStackMetrics(series.yAxis, shape).height, point.shapeArgs.width || 0, point.shapeArgs.height || Infinity, point.series.options.borderWidth || 0);\n        }\n    };\n    return PictorialPoint;\n}(ColumnPoint));\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ var Pictorial_PictorialPoint = (PictorialPoint);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"StackItem\"],\"commonjs\":[\"highcharts\",\"StackItem\"],\"commonjs2\":[\"highcharts\",\"StackItem\"],\"root\":[\"Highcharts\",\"StackItem\"]}\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_ = __webpack_require__(184);\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default = /*#__PURE__*/__webpack_require__.n(highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es5/es-modules/Series/Pictorial/PictorialSeries.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi, Magdalena Gut\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar PictorialSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\n\n\n\n\n\n\n\nvar ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\nExtensions_PatternFill.compose((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()), (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default()));\nvar PictorialSeries_animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\nvar PictorialSeries_getStackMetrics = PictorialUtilities.getStackMetrics, PictorialSeries_invertShadowGroup = PictorialUtilities.invertShadowGroup, PictorialSeries_rescalePatternFill = PictorialUtilities.rescalePatternFill;\nvar PictorialSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, PictorialSeries_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, PictorialSeries_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, PictorialSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The pictorial series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pictorial\n *\n * @augments Highcharts.Series\n */\nvar PictorialSeries = /** @class */ (function (_super) {\n    PictorialSeries_extends(PictorialSeries, _super);\n    function PictorialSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     * Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Animate in the series. Called internally twice. First with the `init`\n     * parameter set to true, which sets up the initial state of the\n     * animation. Then when ready, it is called with the `init` parameter\n     * undefined, in order to perform the actual animation.\n     *\n     * @function Highcharts.Series#animate\n     *\n     * @param {boolean} [init]\n     * Initialize the animation.\n     */\n    PictorialSeries.prototype.animate = function (init) {\n        var _a = this, chart = _a.chart, group = _a.group, animation = PictorialSeries_animObject(this.options.animation), \n            // The key for temporary animation clips\n            animationClipKey = [\n                this.getSharedClipKey(),\n                animation.duration,\n                animation.easing,\n                animation.defer\n            ].join(',');\n        var animationClipRect = chart.sharedClips[animationClipKey];\n        // Initialize the animation. Set up the clipping rectangle.\n        if (init && group) {\n            var clipBox = chart.getClipBox(this);\n            // Create temporary animation clips\n            if (!animationClipRect) {\n                clipBox.y = clipBox.height;\n                clipBox.height = 0;\n                animationClipRect = chart.renderer.clipRect(clipBox);\n                chart.sharedClips[animationClipKey] = animationClipRect;\n            }\n            group.clip(animationClipRect);\n            // Run the animation\n        }\n        else if (animationClipRect &&\n            // Only first series in this pane\n            !animationClipRect.hasClass('highcharts-animating')) {\n            var finalBox = chart.getClipBox(this);\n            animationClipRect\n                .addClass('highcharts-animating')\n                .animate(finalBox, animation);\n        }\n    };\n    PictorialSeries.prototype.animateDrilldown = function () { };\n    PictorialSeries.prototype.animateDrillupFrom = function () { };\n    PictorialSeries.prototype.pointAttribs = function (point) {\n        var pointAttribs = _super.prototype.pointAttribs.apply(this,\n            arguments),\n            seriesOptions = this.options,\n            series = this,\n            paths = seriesOptions.paths;\n        if (point && point.shapeArgs && paths) {\n            var shape = paths[point.index % paths.length],\n                _a = PictorialSeries_getStackMetrics(series.yAxis,\n                shape),\n                y = _a.y,\n                height = _a.height,\n                pathDef = shape.definition;\n            // New pattern, replace\n            if (pathDef !== point.pathDef) {\n                point.pathDef = pathDef;\n                pointAttribs.fill = {\n                    pattern: {\n                        path: {\n                            d: pathDef,\n                            fill: pointAttribs.fill,\n                            strokeWidth: pointAttribs['stroke-width'],\n                            stroke: pointAttribs.stroke\n                        },\n                        x: point.shapeArgs.x,\n                        y: y,\n                        width: point.shapeArgs.width || 0,\n                        height: height,\n                        patternContentUnits: 'objectBoundingBox',\n                        backgroundColor: 'none',\n                        color: '#ff0000'\n                    }\n                };\n            }\n            else if (point.pathDef && point.graphic) {\n                delete pointAttribs.fill;\n            }\n        }\n        delete pointAttribs.stroke;\n        delete pointAttribs.strokeWidth;\n        return pointAttribs;\n    };\n    /**\n     * Make sure that path.max is also considered when calculating dataMax.\n     */\n    PictorialSeries.prototype.getExtremes = function () {\n        var extremes = _super.prototype.getExtremes.apply(this,\n            arguments),\n            series = this,\n            paths = series.options.paths;\n        if (paths) {\n            paths.forEach(function (path) {\n                if (PictorialSeries_defined(path.max) &&\n                    PictorialSeries_defined(extremes.dataMax) &&\n                    path.max > extremes.dataMax) {\n                    extremes.dataMax = path.max;\n                }\n            });\n        }\n        return extremes;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    PictorialSeries.defaultOptions = PictorialSeries_merge(ColumnSeries.defaultOptions, \n    /**\n     * A pictorial chart uses vector images to represents the data.\n     * The shape of the data point is taken from the path parameter.\n     *\n     * @sample       {highcharts} highcharts/demo/pictorial/\n     *               Pictorial chart\n     *\n     * @extends      plotOptions.column\n     * @since 11.0.0\n     * @product      highcharts\n     * @excluding    allAreas, borderRadius,\n     *               centerInCategory, colorAxis, colorKey, connectEnds,\n     *               connectNulls, crisp, compare, compareBase, dataSorting,\n     *               dashStyle, dataAsColumns, linecap, lineWidth, shadow,\n     *               onPoint\n     * @requires     modules/pictorial\n     * @optionparent plotOptions.pictorial\n     */\n    {\n        borderWidth: 0\n    });\n    return PictorialSeries;\n}(ColumnSeries));\n/* *\n *\n *  Events\n *\n * */\nPictorialSeries_addEvent(PictorialSeries, 'afterRender', function () {\n    var series = this, paths = series.options.paths, fillUrlMatcher = /url\\(([^)]+)\\)/;\n    series.points.forEach(function (point) {\n        if (point.graphic && point.shapeArgs && paths) {\n            var shape = paths[point.index % paths.length],\n                fill = point.graphic.attr('fill'),\n                match = fill && fill.match(fillUrlMatcher),\n                _a = PictorialSeries_getStackMetrics(series.yAxis,\n                shape),\n                y = _a.y,\n                height = _a.height;\n            if (match && series.chart.renderer.patternElements) {\n                var currentPattern = series.chart.renderer.patternElements[match[1].slice(1)];\n                if (currentPattern) {\n                    currentPattern.animate({\n                        x: point.shapeArgs.x,\n                        y: y,\n                        width: point.shapeArgs.width || 0,\n                        height: height\n                    });\n                }\n            }\n            PictorialSeries_rescalePatternFill(point.graphic, PictorialSeries_getStackMetrics(series.yAxis, shape).height, point.shapeArgs.width || 0, point.shapeArgs.height || Infinity, series.options.borderWidth || 0);\n        }\n    });\n});\n/**\n *\n */\nfunction renderStackShadow(stack) {\n    // Get first pictorial series\n    var stackKeys = Object\n            .keys(stack.points)\n            .filter(function (p) { return p.split(',').length > 1; }), allSeries = stack.axis.chart.series, seriesIndexes = stackKeys.map(function (key) {\n            return parseFloat(key.split(',')[0]);\n    });\n    var seriesIndex = -1;\n    seriesIndexes.forEach(function (index) {\n        if (allSeries[index] && allSeries[index].visible) {\n            seriesIndex = index;\n        }\n    });\n    var series = stack.axis.chart.series[seriesIndex];\n    if (series &&\n        series.is('pictorial') &&\n        stack.axis.hasData() &&\n        series.xAxis.hasData()) {\n        var xAxis = series.xAxis,\n            options = stack.axis.options,\n            chart = stack.axis.chart,\n            stackShadow = stack.shadow,\n            xCenter = xAxis.toPixels(stack.x,\n            true),\n            x = chart.inverted ? xAxis.len - xCenter : xCenter,\n            paths = series.options.paths || [],\n            index = stack.x % paths.length,\n            shape = paths[index],\n            width = series.getColumnMetrics &&\n                series.getColumnMetrics().width,\n            _a = PictorialSeries_getStackMetrics(series.yAxis,\n            shape),\n            height = _a.height,\n            y = _a.y,\n            shadowOptions = options.stackShadow,\n            strokeWidth = PictorialSeries_pick(shadowOptions && shadowOptions.borderWidth,\n            series.options.borderWidth, 1);\n        if (!stackShadow &&\n            shadowOptions &&\n            shadowOptions.enabled &&\n            shape) {\n            if (!stack.shadowGroup) {\n                stack.shadowGroup = chart.renderer.g('shadow-group')\n                    .add();\n            }\n            stack.shadowGroup.attr({\n                translateX: chart.inverted ?\n                    stack.axis.pos : xAxis.pos,\n                translateY: chart.inverted ?\n                    xAxis.pos : stack.axis.pos\n            });\n            stack.shadow = chart.renderer.rect(x, y, width, height)\n                .attr({\n                fill: {\n                    pattern: {\n                        path: {\n                            d: shape.definition,\n                            fill: shadowOptions.color ||\n                                '#dedede',\n                            strokeWidth: strokeWidth,\n                            stroke: shadowOptions.borderColor ||\n                                'transparent'\n                        },\n                        x: x,\n                        y: y,\n                        width: width,\n                        height: height,\n                        patternContentUnits: 'objectBoundingBox',\n                        backgroundColor: 'none',\n                        color: '#dedede'\n                    }\n                }\n            })\n                .add(stack.shadowGroup);\n            PictorialSeries_invertShadowGroup(stack.shadowGroup, stack.axis);\n            PictorialSeries_rescalePatternFill(stack.shadow, height, width, height, strokeWidth);\n            stack.setOffset(series.pointXOffset || 0, series.barW || 0);\n        }\n        else if (stackShadow && stack.shadowGroup) {\n            stackShadow.animate({\n                x: x,\n                y: y,\n                width: width,\n                height: height\n            });\n            var fillUrlMatcher = /url\\(([^)]+)\\)/, fill = stackShadow.attr('fill'), match = fill && fill.match(fillUrlMatcher);\n            if (match && chart.renderer.patternElements) {\n                chart.renderer.patternElements[match[1].slice(1)]\n                    .animate({\n                    x: x,\n                    y: y,\n                    width: width,\n                    height: height\n                });\n            }\n            stack.shadowGroup.animate({\n                translateX: chart.inverted ?\n                    stack.axis.pos : xAxis.pos,\n                translateY: chart.inverted ?\n                    xAxis.pos : stack.axis.pos\n            });\n            PictorialSeries_invertShadowGroup(stack.shadowGroup, stack.axis);\n            PictorialSeries_rescalePatternFill(stackShadow, height, width, height, strokeWidth);\n            stack.setOffset(series.pointXOffset || 0, series.barW || 0);\n        }\n    }\n    else if (stack.shadow && stack.shadowGroup) {\n        stack.shadow.destroy();\n        stack.shadow = void 0;\n        stack.shadowGroup.destroy();\n        stack.shadowGroup = void 0;\n    }\n}\n/**\n *\n */\nfunction forEachStack(chart, callback) {\n    if (chart.axes) {\n        chart.axes.forEach(function (axis) {\n            if (!axis.stacking) {\n                return;\n            }\n            var stacks = axis.stacking.stacks;\n            // Render each stack total\n            objectEach(stacks, function (type) {\n                objectEach(type, function (stack) {\n                    callback(stack);\n                });\n            });\n        });\n    }\n}\nPictorialSeries_addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'render', function () {\n    forEachStack(this, renderStackShadow);\n});\nPictorialSeries_addEvent((highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default()), 'afterSetOffset', function (e) {\n    if (this.shadow) {\n        var _a = this.axis,\n            chart = _a.chart,\n            len = _a.len,\n            xOffset = e.xOffset,\n            width = e.width,\n            translateX = chart.inverted ? xOffset - chart.xAxis[0].len : xOffset,\n            translateY = chart.inverted ? -len : 0;\n        this.shadow.attr({\n            translateX: translateX,\n            translateY: translateY\n        });\n        this.shadow.animate({ width: width });\n    }\n});\n/**\n *\n */\nfunction destroyAllStackShadows(chart) {\n    forEachStack(chart, function (stack) {\n        if (stack.shadow && stack.shadowGroup) {\n            stack.shadow.destroy();\n            stack.shadowGroup.destroy();\n            delete stack.shadow;\n            delete stack.shadowGroup;\n        }\n    });\n}\n// This is a workaround due to no implementation of the animation drilldown.\nPictorialSeries_addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'afterDrilldown', function () {\n    destroyAllStackShadows(this);\n});\nPictorialSeries_addEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'afterDrillUp', function () {\n    destroyAllStackShadows(this);\n});\nPictorialSeries.prototype.pointClass = Pictorial_PictorialPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('pictorial', PictorialSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Pictorial_PictorialSeries = ((/* unused pure expression or super */ null && (PictorialSeries)));\n/* *\n *\n * API Options\n *\n * */\n/**\n * A `pictorial` series. If the [type](#series.pictorial.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.pictorial\n * @since 11.0.0\n * @product   highcharts\n * @excluding dataParser, borderRadius, boostBlending, boostThreshold,\n *            borderColor, borderWidth, centerInCategory, connectEnds,\n *            connectNulls, crisp, colorKey, dataURL, dataAsColumns, depth,\n *            dragDrop, edgeColor, edgeWidth, linecap, lineWidth,  marker,\n *            dataSorting, dashStyle, onPoint, relativeXValue, shadow, zoneAxis,\n *            zones\n * @requires  modules/pictorial\n * @apioption series.pictorial\n */\n/**\n * An array of data points for the series. For the `pictorial` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 2 values. In this case, the values correspond\n *    to `x,y`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 40],\n *        [1, 50],\n *        [2, 60]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.pictorial.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 0,\n *        y: 40,\n *        name: \"Point1\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 60,\n *        name: \"Point2\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.column.data\n *\n * @sample {highcharts} highcharts/demo/pictorial/\n *         Pictorial chart\n * @sample {highcharts} highcharts/demo/pictorial-stackshadow/\n *         Pictorial stackShadow option\n * @sample {highcharts} highcharts/series-pictorial/paths-max/\n *         Pictorial max option\n *\n * @excluding borderColor, borderWidth, dashStyle, dragDrop\n * @since 11.0.0\n * @product   highcharts\n * @apioption series.pictorial.data\n */\n/**\n * The paths include options describing the series image. For further details on\n * preparing the SVG image, please refer to the [pictorial\n * documentation](https://www.highcharts.com/docs/chart-and-series-types/pictorial).\n *\n * @declare   Highcharts.SeriesPictorialPathsOptionsObject\n * @type      {Array<*>}\n *\n * @sample    {highcharts} highcharts/demo/pictorial/\n *            Pictorial chart\n *\n * @since     11.0.0\n * @product   highcharts\n * @apioption series.pictorial.paths\n */\n/**\n * The definition defines a path to be drawn. It corresponds `d` SVG attribute.\n *\n * @type      {string}\n *\n * @sample    {highcharts} highcharts/demo/pictorial/\n *            Pictorial chart\n *\n * @product   highcharts\n * @apioption series.pictorial.paths.definition\n */\n/**\n * The max option determines height of the image. It is the ratio of\n * `yAxis.max` to the `paths.max`.\n *\n * @sample {highcharts} highcharts/series-pictorial/paths-max\n *         Pictorial max option\n *\n * @type      {number}\n * @default   yAxis.max\n * @product   highcharts\n * @apioption series.pictorial.paths.max\n */\n/**\n * Relevant only for pictorial series. The `stackShadow` forms the background of\n * stacked points. Requires `series.stacking` to be defined.\n *\n * @sample {highcharts} highcharts/demo/pictorial-stackshadow/ Pictorial\n *         stackShadow option\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {*}\n * @since 11.0.0\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow\n */\n/**\n * The color of the `stackShadow` border.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @default   transparent\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.borderColor\n */\n/**\n * The width of the `stackShadow` border.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {number}\n * @default   0\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.borderWidth\n */\n/**\n * The color of the `stackShadow`.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @default   #dedede\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.color\n */\n/**\n * Enable or disable `stackShadow`.\n *\n * @declare   Highcharts.YAxisOptions\n * @type      {boolean}\n * @default   undefined\n * @product   highcharts\n * @requires  modules/pictorial\n * @apioption yAxis.stackShadow.enabled\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es5/es-modules/masters/modules/pictorial.js\n\n\n\n\n/* harmony default export */ var pictorial_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__448__", "__WEBPACK_EXTERNAL_MODULE__960__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__184__", "__WEBPACK_EXTERNAL_MODULE__540__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pictorial_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "animObject", "getOptions", "addEvent", "defined", "erase", "extend", "merge", "pick", "removeEvent", "wrap", "patterns", "createPatterns", "colors", "i", "_i", "_a", "length", "pattern", "push", "path", "color", "width", "height", "patternTransform", "_b", "_c", "hashFromObject", "preSeed", "seedStep", "str", "JSON", "stringify", "strLen", "hash", "Math", "max", "floor", "charCodeAt", "toString", "replace", "onChartEndResize", "renderer", "defIds", "filter", "id", "indexOf", "series", "visible", "points", "point", "colorOptions", "options", "_width", "_height", "redraw", "onChartRedraw", "usedIds", "for<PERSON>ach", "renderTo", "querySelectorAll", "node", "getAttribute", "url", "patterns_1", "patternElements", "destroy", "onPointAfterInit", "onRendererComplexColor", "args", "element", "chartIndex", "value", "patternIndex", "image", "forceHashId", "parentNode", "pointCalculatePatternDimensions", "graphic", "addPattern", "forExport", "animation", "globalAnimation", "duration", "concat", "setAttribute", "onSeriesRender", "isResizing", "chart", "isDirtyData", "hasRendered", "shapeArgs", "calculatePatternDimensions", "bBox", "getBBox", "x", "y", "scaleY", "mapView", "getSVGTransform", "_inverted", "aspectRatio", "aspectWidth", "aspectHeight", "ceil", "_x", "round", "abs", "_y", "rendererAddPattern", "fill", "attribs", "animate", "animationOptions", "idCounter", "attrs", "patternUnits", "patternContentUnits", "createElement", "attr", "add", "defs", "isObject", "backgroundColor", "_this", "rect", "styledMode", "stroke", "strokeWidth", "transform", "opacity", "childNodes", "child", "wrapSeriesGetColor", "proceed", "oldColor", "apply", "slice", "arguments", "onPatternScaleCorrection", "p", "_d", "hasAttribute", "group", "scaleX", "map", "_e", "pointInfo", "index", "arr", "some", "otherInfo", "otherIndex", "updateTransform", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "PictorialUtilities_defined", "stackHeight", "borderWidth", "match", "patternPath", "document", "querySelector", "parent_1", "parentElement", "box", "append<PERSON><PERSON><PERSON>", "pointAspectRatio", "yAxis", "shape", "len", "toPixels", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "__", "constructor", "create", "PictorialPoint", "_super", "setState", "paths", "PictorialPoint_rescalePatternFill", "PictorialPoint_getStackMetrics", "Infinity", "seriesTypes", "column", "pointClass", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "PictorialSeries_extends", "TypeError", "String", "ColumnSeries", "Extensions_PatternFill", "compose", "ChartClass", "SeriesClass", "SVGRendererClass", "PointClass", "pointProto", "PictorialSeries_animObject", "PictorialSeries_invertShadowGroup", "shadowGroup", "inverted", "rotation", "PictorialSeries_addEvent", "PictorialSeries_defined", "PictorialSeries_merge", "objectEach", "PictorialSeries_pick", "PictorialSeries", "init", "animationClipKey", "getSharedClipKey", "easing", "defer", "join", "animationClipRect", "sharedClips", "clipBox", "getClipBox", "clipRect", "clip", "hasClass", "finalBox", "addClass", "animateDrilldown", "animateDrillupFrom", "pointAttribs", "seriesOptions", "PictorialSeries_getStackMetrics", "pathDef", "getExtremes", "extremes", "dataMax", "defaultOptions", "renderStackShadow", "stack", "stackKeys", "keys", "split", "allSeries", "axis", "seriesIndexes", "parseFloat", "seriesIndex", "is", "hasData", "xAxis", "stackShadow", "shadow", "xCenter", "getColumnMetrics", "shadowOptions", "enabled", "g", "translateX", "pos", "translateY", "borderColor", "PictorialSeries_rescalePatternFill", "setOffset", "pointXOffset", "barW", "forEachStack", "callback", "axes", "stacking", "stacks", "type", "destroyAllStackShadows", "fill<PERSON>rl<PERSON><PERSON><PERSON>", "currentPattern", "e", "xOffset", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,SAAY,CAAEA,QAAQ,cAAc,WAAc,EAC/Q,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,SAAS,QAAQ,SAAS,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,SAAS,CAAE,CAAC,wBAAwB,YAAY,CAAE,CAAC,wBAAwB,cAAc,CAAC,CAAEJ,GACrT,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,SAAY,CAAEA,QAAQ,cAAc,WAAc,EAE/SJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAC,KAAQ,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,SAAY,CAAEA,EAAK,UAAa,CAAC,WAAc,CACvQ,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EAC9P,OAAgB,AAAC,WACP,aACA,IAi8BFC,EA2EAA,EA5gCMC,EAAuB,CAE/B,IACC,SAASZ,CAAM,EAEtBA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGW,CAEX,EAEA,IACC,SAASV,CAAM,EAEtBA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,SAASR,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,SAASlB,CAAM,EACtC,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,WAAa,OAAOpB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAAStB,CAAO,CAAEwB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAe,CAC9D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEsCvB,EAAoB,KAgB/K,IAAIyB,EAAa,AAACD,IAA+EC,UAAU,CAEvGC,EAAa,AAACF,IAA+EE,UAAU,CAEvGC,EAAW,AAACH,IAA+EG,QAAQ,CAAEC,EAAU,AAACJ,IAA+EI,OAAO,CAAEC,EAAQ,AAACL,IAA+EK,KAAK,CAAEC,EAAS,AAACN,IAA+EM,MAAM,CAAEC,EAAQ,AAACP,IAA+EO,KAAK,CAAEC,EAAO,AAACR,IAA+EQ,IAAI,CAAEC,EAAc,AAACT,IAA+ES,WAAW,CAAEC,EAAO,AAACV,IAA+EU,IAAI,CAM1wBC,EAAWC,AAgCf,WAKI,IAAK,IAJDD,EAAW,EAAE,CACbE,EAASX,IAAaW,MAAM,CAE5BC,EAAI,EACCC,EAAK,EAAGC,EAAK,CAClB,wDACA,wDACA,0BACA,0BACA,sDACH,CAAED,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACrB,IAAIG,EAAUF,CAAE,CAACD,EAAG,CACpBJ,EAASQ,IAAI,CAAC,CACVC,KAAMF,EACNG,MAAOR,CAAM,CAACC,IAAI,CAClBQ,MAAO,EACPC,OAAQ,EACRC,iBAAkB,gBACtB,EACJ,CAEAV,EAAI,EACJ,IAAK,IAAIW,EAAK,EAAGC,EAAK,CAClB,sBACA,4BACA,gDACA,4CACA,4CACH,CAAED,EAAKC,EAAGT,MAAM,CAAEQ,IAAM,CACrB,IAAIP,EAAUQ,CAAE,CAACD,EAAG,CACpBd,EAASQ,IAAI,CAAC,CACVC,KAAMF,EACNG,MAAOR,CAAM,CAACC,EAAE,CAChBQ,MAAO,GACPC,OAAQ,EACZ,GACAT,GAAQ,CACZ,CACA,OAAOH,CACX,IAkBA,SAASgB,EAAenC,CAAG,CAAEoC,CAAO,EAChC,IAKIC,EALAC,EAAMC,KAAKC,SAAS,CAACxC,GACrByC,EAASH,EAAIb,MAAM,EAAI,EACvBiB,EAAO,EACPpB,EAAI,EAGR,GAAIc,EAAS,CACTC,EAAWM,KAAKC,GAAG,CAACD,KAAKE,KAAK,CAACJ,EAAS,KAAM,GAC9C,IAAK,IAAIjD,EAAI,EAAGA,EAAIiD,EAAQjD,GAAK6C,EAC7BK,GAAQJ,EAAIQ,UAAU,CAACtD,GAE3BkD,GAAcA,CAClB,CACA,KAAOpB,EAAImB,EAAQ,EAAEnB,EAEjBoB,EAAO,AAAEA,CAAAA,GAAQ,CAAA,EAAKA,EADfJ,EAAIQ,UAAU,CAACxB,GAEtBoB,GAAcA,EAElB,OAAOA,EAAKK,QAAQ,CAAC,IAAIC,OAAO,CAAC,IAAK,IAC1C,CAMA,SAASC,IACL,GAAI,IAAI,CAACC,QAAQ,EACb,AAAC,CAAA,IAAI,CAACA,QAAQ,CAACC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,SAAUC,CAAE,EAAI,OAAQA,GACxDA,EAAGC,OAAO,EACVD,AAAsC,IAAtCA,EAAGC,OAAO,CAAC,sBAA+B,GAAG7B,MAAM,CAAE,CAGzD,IAAK,IAAIF,EAAK,EAAGC,EAAK,IAAI,CAAC+B,MAAM,CAAEhC,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACrD,IAAIgC,EAAS/B,CAAE,CAACD,EAAG,CACnB,GAAIgC,EAAOC,OAAO,CACd,IAAK,IAAIvB,EAAK,EAAGC,EAAKqB,EAAOE,MAAM,CAAExB,EAAKC,EAAGT,MAAM,CAAEQ,IAAM,CACvD,IAAIyB,EAAQxB,CAAE,CAACD,EAAG,CACd0B,EAAeD,EAAME,OAAO,EAAIF,EAAME,OAAO,CAAC/B,KAAK,CACnD8B,GACAA,EAAajC,OAAO,GACpBiC,EAAajC,OAAO,CACfmC,MAAM,CAAG,QACdF,EAAajC,OAAO,CACfoC,OAAO,CAAG,QAEvB,CAER,CAEA,IAAI,CAACC,MAAM,CAAC,CAAA,EAChB,CACJ,CAMA,SAASC,IACL,IAAIC,EAAU,CAAC,EACXf,EAAW,IAAI,CAACA,QAAQ,CAExB/B,EAAW,AAAC+B,CAAAA,EAASC,MAAM,EAAI,EAAE,AAAD,EAAGC,MAAM,CAAC,SAAU1B,CAAO,EAAI,OAAQA,EAAQ4B,OAAO,EAClF5B,AAA2C,IAA3CA,EAAQ4B,OAAO,CAAC,sBAA+B,GACvD,GAAInC,EAASM,MAAM,CAAE,CAGjB,EAAE,CAACyC,OAAO,CAAC9D,IAAI,CAAC,IAAI,CAAC+D,QAAQ,CAACC,gBAAgB,CAAC,qDAAsD,SAAUC,CAAI,EAC/G,IAAIhB,EAAKgB,EAAKC,YAAY,CAAC,SACnBD,EAAKC,YAAY,CAAC,UAClBD,EAAKC,YAAY,CAAC,UACtBjB,GAKAY,CAAAA,CAAO,CAJWZ,EACTL,OAAO,CAACE,EAASqB,GAAG,CAAE,IACtBvB,OAAO,CAAC,QAAS,IACjBA,OAAO,CAAC,IAAK,IACF,CAAG,CAAA,CAAG,CAElC,GAEA,IAAK,IAAIzB,EAAK,EAA0BA,EAAKiD,AAAfrD,EAA0BM,MAAM,CAAEF,IAAM,CAClE,IAAI8B,EAAKmB,AADiBrD,CACP,CAACI,EAAG,AACnB,EAAC0C,CAAO,CAACZ,EAAG,GAEZxC,EAAMqC,EAASC,MAAM,CAAEE,GAEnBH,EAASuB,eAAe,CAACpB,EAAG,GAC5BH,EAASuB,eAAe,CAACpB,EAAG,CAACqB,OAAO,GACpC,OAAOxB,EAASuB,eAAe,CAACpB,EAAG,EAG/C,CACJ,CACJ,CAKA,SAASsB,IACL,IACIhB,EAAeD,AADP,IAAI,CACSE,OAAO,CAAC/B,KAAK,CAGlC8B,GAAgBA,EAAajC,OAAO,GAGK,UAArC,OAAOiC,EAAajC,OAAO,CAACE,IAAI,EAChC+B,CAAAA,EAAajC,OAAO,CAACE,IAAI,CAAG,CACxBrC,EAAGoE,EAAajC,OAAO,CAACE,IAAI,AAChC,CAAA,EAGJ8B,AAbQ,IAAI,CAaN7B,KAAK,CAAG6B,AAbN,IAAI,CAaQE,OAAO,CAAC/B,KAAK,CAAGd,EAAM2C,AAblC,IAAI,CAaoCH,MAAM,CAACK,OAAO,CAAC/B,KAAK,CAAE8B,GAE9E,CAKA,SAASiB,EAAuBC,CAAI,EAChC,IAAIhD,EAAQgD,EAAKA,IAAI,CAAC,EAAE,CACpB5E,EAAO4E,EAAKA,IAAI,CAAC,EAAE,CACnBC,EAAUD,EAAKA,IAAI,CAAC,EAAE,CACtBE,EAAc,IAAI,CAACA,UAAU,EAAI,EACjCrD,EAAUG,EAAMH,OAAO,CAAEsD,EAAQ,UAMrC,GAJkC,KAAA,IAAvBnD,EAAMoD,YAAY,EAAoB9D,GAC7CO,CAAAA,EAAUP,CAAQ,CAACU,EAAMoD,YAAY,CAAC,AAAD,EAGrC,CAACvD,EACD,MAAO,CAAA,EAGX,GAAIA,EAAQwD,KAAK,EACb,AAAwB,UAAxB,OAAOxD,EAAQE,IAAI,EACnBF,EAAQE,IAAI,EAAIF,EAAQE,IAAI,CAACrC,CAAC,CAAE,CAMhC,IAAI4F,EAAcL,EAAQM,UAAU,EAC5BN,EAAQM,UAAU,CAACd,YAAY,CAAC,SACxCa,EAAcA,GACVA,EAAY7B,OAAO,CAAC,qBAAuB,GAG3C5B,CAAAA,AAAmB,UAAnBA,EAAQmC,MAAM,EAAgBnC,AAAoB,UAApBA,EAAQoC,OAAO,AAAW,GACxDuB,EAAgCjF,IAAI,CAAC,CAAEkF,QAAS,CAAER,QAASA,CAAQ,CAAE,EAAGpD,GAOxEyD,CAAAA,GAAe,CAACzD,EAAQ2B,EAAE,AAAD,GAGzB3B,CAAAA,AADAA,CAAAA,EAAUX,EAAM,CAAC,EAAGW,EAAO,EACnB2B,EAAE,CAAG,sBAAwB0B,EAAa,IAC9C5C,EAAeT,GAAWS,EAAeT,EAAS,CAAA,EAAI,EAI9D,IAAI,CAAC6D,UAAU,CAAC7D,EAAS,CAAC,IAAI,CAAC8D,SAAS,EAAIxE,EAAKU,EAAQ+D,SAAS,CAAE,IAAI,CAACC,eAAe,CAAE,CAAEC,SAAU,GAAI,IAC1GX,EAAQ,OAAOY,MAAM,CAAC,IAAI,CAACrB,GAAG,CAAE,KAAKqB,MAAM,CAAClE,EAAQ2B,EAAE,CAAI,CAAA,IAAI,CAACmC,SAAS,CAAG,UAAY,EAAC,EAAI,IAChG,MAGIR,EAAQtD,EAAQG,KAAK,EAAImD,EAS7B,OANAF,EAAQe,YAAY,CAAC5F,EAAM+E,GAE3BnD,EAAMkB,QAAQ,CAAG,WACb,OAAOiC,CACX,EAEO,CAAA,CACX,CAKA,SAASc,IACL,IAAIC,EAAa,IAAI,CAACC,KAAK,CAACD,UAAU,CACtC,GAAI,IAAI,CAACE,WAAW,EAAIF,GAAc,CAAC,IAAI,CAACC,KAAK,CAACE,WAAW,CACzD,IAAK,IAAI3E,EAAK,EAAGC,EAAK,IAAI,CAACiC,MAAM,CAAElC,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACrD,IAAImC,EAAQlC,CAAE,CAACD,EAAG,CACdoC,EAAeD,EAAME,OAAO,EAAIF,EAAME,OAAO,CAAC/B,KAAK,CACnD8B,GACAA,EAAajC,OAAO,GAKhBqE,GACA,CAAErC,CAAAA,EAAMyC,SAAS,EACbzC,EAAMyC,SAAS,CAACrE,KAAK,EACrB4B,EAAMyC,SAAS,CAACpE,MAAM,AAAD,GACzB4B,EACKjC,OAAO,CAACmC,MAAM,CAAG,QACtBF,EACKjC,OAAO,CAACoC,OAAO,CAAG,SAGvBJ,EAAM0C,0BAA0B,CAACzC,EAAajC,OAAO,EAGjE,CAER,CAmBA,SAAS2D,EAAgC3D,CAAO,EAC5C,GAAIA,CAAAA,EAAQI,KAAK,GAAIJ,EAAQK,MAAM,EAGnC,IAAIsE,EAAO,IAAI,CAACf,OAAO,EAAK,CAAA,IAAI,CAACA,OAAO,CAACgB,OAAO,EACxC,IAAI,CAAChB,OAAO,CAACgB,OAAO,CAAC,CAAA,IACrB,IAAI,CAAChB,OAAO,CAACR,OAAO,EAChB,IAAI,CAACQ,OAAO,CAACR,OAAO,CAACwB,OAAO,EAAC,GAAM,CAAC,EAC5CH,EAAY,IAAI,CAACA,SAAS,CAS9B,GAPIA,IACAE,EAAKvE,KAAK,CAAGqE,EAAUrE,KAAK,EAAIuE,EAAKvE,KAAK,CAC1CuE,EAAKtE,MAAM,CAAGoE,EAAUpE,MAAM,EAAIsE,EAAKtE,MAAM,CAC7CsE,EAAKE,CAAC,CAAGJ,EAAUI,CAAC,EAAIF,EAAKE,CAAC,CAC9BF,EAAKG,CAAC,CAAGL,EAAUK,CAAC,EAAIH,EAAKG,CAAC,EAG9B9E,EAAQwD,KAAK,CAAE,CAIf,GAAI,CAACmB,EAAKvE,KAAK,EAAI,CAACuE,EAAKtE,MAAM,CAAE,CAC7BL,EAAQmC,MAAM,CAAG,QACjBnC,EAAQoC,OAAO,CAAG,QAElB,IAAI2C,EAAS,IAAI,CAAClD,MAAM,CAACyC,KAAK,CAACU,OAAO,EAC9B,IAAI,CAACnD,MAAM,CAACyC,KAAK,CAACU,OAAO,CAACC,eAAe,GAAGF,MAAM,CACtD7F,EAAQ6F,IAAWA,EAAS,GAC5B/E,CAAAA,EAAQkF,SAAS,CAAG,CAAA,CAAG,EAE3B,MACJ,CAEIlF,EAAQmF,WAAW,GACnBR,EAAKQ,WAAW,CAAGR,EAAKvE,KAAK,CAAGuE,EAAKtE,MAAM,CACvCL,EAAQmF,WAAW,CAAGR,EAAKQ,WAAW,CAEtCR,EAAKS,WAAW,CAAGT,EAAKtE,MAAM,CAAGL,EAAQmF,WAAW,CAIpDR,EAAKU,YAAY,CAAGV,EAAKvE,KAAK,CAAGJ,EAAQmF,WAAW,EAK5DnF,EAAQmC,MAAM,CAAGnC,EAAQI,KAAK,EAC1Ba,KAAKqE,IAAI,CAACX,EAAKS,WAAW,EAAIT,EAAKvE,KAAK,EAC5CJ,EAAQoC,OAAO,CAAGpC,EAAQK,MAAM,EAC5BY,KAAKqE,IAAI,CAACX,EAAKU,YAAY,EAAIV,EAAKtE,MAAM,CAClD,CAGKL,EAAQI,KAAK,GACdJ,EAAQuF,EAAE,CAAGvF,EAAQ6E,CAAC,EAAI,EAC1B7E,EAAQuF,EAAE,EAAIZ,EAAKE,CAAC,CAAG5D,KAAKuE,KAAK,CAACb,EAAKS,WAAW,CAC9CnE,KAAKwE,GAAG,CAACd,EAAKS,WAAW,CAAGT,EAAKvE,KAAK,EAAI,EAC1C,IAEHJ,EAAQK,MAAM,GACfL,EAAQ0F,EAAE,CAAG1F,EAAQ8E,CAAC,EAAI,EAC1B9E,EAAQ0F,EAAE,EAAIf,EAAKG,CAAC,CAAG7D,KAAKuE,KAAK,CAACb,EAAKU,YAAY,CAC/CpE,KAAKwE,GAAG,CAACd,EAAKU,YAAY,CAAGV,EAAKtE,MAAM,EAAI,EAC5C,IAEZ,CAkBA,SAASsF,EAAmBzD,CAAO,CAAE6B,CAAS,EAC1C,IAGsC6B,EAMlCC,EAEA3F,EAVA4F,EAAUxG,EAAKyE,EAAW,CAAA,GAAOgC,EAAmBhH,EAAW+G,GAAU3F,EAAQ+B,EAAQ/B,KAAK,EAAI,UAA0DE,EAAS6B,EAAQ7B,MAAM,EAC9K,CAAA,AAA2B,UAA3B,OAAO6B,EAAQE,OAAO,CAAgBF,EAAQE,OAAO,CAAG,CAAA,GAD2F,GAKlIhC,EAAQ8B,EAAQ9B,KAAK,EACtC,CAAA,AAA0B,UAA1B,OAAO8B,EAAQC,MAAM,CAAgBD,EAAQC,MAAM,CAAG,CAAA,GAN6F,GASxJR,EAAKO,EAAQP,EAAE,CAenB,GAbI,CAACA,IACD,IAAI,CAACqE,SAAS,CAAG,IAAI,CAACA,SAAS,EAAI,EACnCrE,EAAM,sBACF,IAAI,CAACqE,SAAS,CACd,IACC,CAAA,IAAI,CAAC3C,UAAU,EAAI,CAAA,EACxB,EAAE,IAAI,CAAC2C,SAAS,EAEhB,IAAI,CAAClC,SAAS,EACdnC,CAAAA,GAAM,SAAQ,EAGlB,IAAI,CAACF,MAAM,CAAG,IAAI,CAACA,MAAM,EAAI,EAAE,EAC3B,CAAA,IAAI,CAACA,MAAM,CAACG,OAAO,CAACD,GAAM,EAAC,GAI/B,IAAI,CAACF,MAAM,CAACxB,IAAI,CAAC0B,GAEjB,IAAIsE,EAAQ,CACJtE,GAAIA,EACJuE,aAAc,iBACdC,oBAAqBjE,EAAQiE,mBAAmB,EAAI,iBACpD/F,MAAOA,EACPC,OAAQA,EACRwE,EAAG3C,EAAQqD,EAAE,EAAIrD,EAAQ2C,CAAC,EAAI,EAC9BC,EAAG5C,EAAQwD,EAAE,EAAIxD,EAAQ4C,CAAC,EAAI,CAClC,CACA5C,CAAAA,EAAQgD,SAAS,GACjBe,EAAM3F,gBAAgB,CAAG,eACrB4B,EAAQ5B,gBAAgB,EACxB4B,CAAAA,EAAQ5B,gBAAgB,EAAI,eAAc,GAG9C4B,EAAQ5B,gBAAgB,EACxB2F,CAAAA,EAAM3F,gBAAgB,CAAG4B,EAAQ5B,gBAAgB,AAAD,EAEpD,IAAIN,EAAU,IAAI,CAACoG,aAAa,CAAC,WAAWC,IAAI,CAACJ,GAAOK,GAAG,CAAC,IAAI,CAACC,IAAI,EAmDrE,OAjDAvG,EAAQ2B,EAAE,CAAGA,EAETO,EAAQhC,IAAI,GACZA,EAAOpB,IAA8E0H,QAAQ,CAACtE,EAAQhC,IAAI,EACtGgC,EAAQhC,IAAI,CACZ,CAAErC,EAAGqE,EAAQhC,IAAI,AAAC,EAElBgC,EAAQuE,eAAe,GAvDOb,EAwDzB1D,EAAQuE,eAAe,CAxDiBC,AAHzC,IAAI,CAIPC,IAAI,CAAC,EAAG,EAAGvG,EAAOC,GAClBgG,IAAI,CAAC,CAAET,KAAMA,CAAK,GAClBU,GAAG,CAACtG,IAwDT6F,EAAU,CACN,EAAK3F,EAAKrC,CAAC,AACf,EACK,IAAI,CAAC+I,UAAU,GAChBf,EAAQgB,MAAM,CAAG3G,EAAK2G,MAAM,EAAI1G,EAChC0F,CAAO,CAAC,eAAe,CAAGvG,EAAKY,EAAK4G,WAAW,CAAE,GACjDjB,EAAQD,IAAI,CAAG1F,EAAK0F,IAAI,EAAI,QAE5B1F,EAAK6G,SAAS,EACdlB,CAAAA,EAAQkB,SAAS,CAAG7G,EAAK6G,SAAS,AAAD,EAErC,IAAI,CAACX,aAAa,CAAC,QAAQC,IAAI,CAACR,GAASS,GAAG,CAACtG,GAC7CA,EAAQG,KAAK,CAAGA,GAGX+B,EAAQsB,KAAK,GACdsC,EACA,IAAI,CAACtC,KAAK,CAACtB,EAAQsB,KAAK,CAAE,EAAG,EAAGpD,EAAOC,EAAQ,WAE3C,IAAI,CAACyF,OAAO,CAAC,CACTkB,QAAS1H,EAAK4C,EAAQ8E,OAAO,CAAE,EACnC,EAAGjB,GACHxG,EAAY,IAAI,CAAC6D,OAAO,CAAE,OAC9B,GAAGiD,IAAI,CAAC,CAAEW,QAAS,CAAE,GAAGV,GAAG,CAACtG,GAG5B,IAAI,CAACwD,KAAK,CAACtB,EAAQsB,KAAK,CAAE,EAAG,EAAGpD,EAAOC,GAAQiG,GAAG,CAACtG,IAIrDkC,EAAQsB,KAAK,EAAIsC,GAAY,AAA2B,KAAA,IAApB5D,EAAQ8E,OAAO,EACrD,EAAE,CAACxE,OAAO,CAAC9D,IAAI,CAACsB,EAAQoD,OAAO,CAAC6D,UAAU,CAAE,SAAUC,CAAK,EACvDA,EAAM/C,YAAY,CAAC,UAAWjC,EAAQ8E,OAAO,CACjD,GAGJ,IAAI,CAACjE,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,CAAC,EAChD,IAAI,CAACA,eAAe,CAACpB,EAAG,CAAG3B,EACpBA,EACX,CAKA,SAASmH,EAAmBC,CAAO,EAC/B,IAAIC,EAAW,IAAI,CAACnF,OAAO,CAAC/B,KAAK,AAE7BkH,CAAAA,GACAA,EAASrH,OAAO,EAChB,CAACqH,EAASrH,OAAO,CAACG,KAAK,EACvB,OAAO,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAEzBiH,EAAQE,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,IAE7CH,EAASrH,OAAO,CAACG,KAAK,CAClB,IAAI,CAACA,KAAK,CACd,IAAI,CAACA,KAAK,CAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAGkH,GAIlCD,EAAQE,KAAK,CAAC,IAAI,CAAE,EAAE,CAACC,KAAK,CAAC7I,IAAI,CAAC8I,UAAW,GAErD,CAMA,SAASC,IAKL,GAAM,AAAwB,OAAvB3H,CAAAA,EAAK+B,AAFC,IAAI,CAEEyC,KAAK,AAAD,GAAexE,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGkF,OAAO,EAGzE,IAPIlF,EACAS,EAOAiB,EAAW8C,AADHzC,AALC,IAAI,CAKEyC,KAAK,CACH9C,QAAQ,CACzB/B,EAAW+B,EAASuB,eAAe,CAElC,CAAA,AAA2B,OAA1BxC,CAAAA,EAAKiB,EAASC,MAAM,AAAD,GAAelB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGR,MAAM,AAAD,GAAMN,GAG3EoC,AAZS,IAAI,CAYNE,MAAM,CAACL,MAAM,CAAC,SAAUgG,CAAC,EAK5B,IAJI5H,EACAS,EACAC,EACAmH,QAGJ,EAAK3F,AAFO0F,EAED9D,OAAO,EAGX,AAAC5B,CAAAA,AALI0F,EAKE9D,OAAO,CAACR,OAAO,CAACwE,YAAY,CAAC,SACvC5F,AANQ0F,EAMF9D,OAAO,CAACR,OAAO,CAACwE,YAAY,CAAC,UACnC5F,AAPQ0F,EAOF9D,OAAO,CAACR,OAAO,CAACwE,YAAY,CAAC,SAAQ,GAC3C,CAAE,CAAA,AAAsF,OAArFrH,CAAAA,EAAK,AAA+B,OAA9BT,CAAAA,EAAKkC,AARN0F,EAQYxF,OAAO,CAAC/B,KAAK,AAAD,GAAeL,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,OAAO,AAAD,GAAeO,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGiD,KAAK,AAAD,GAChI,CAAC,CAAE,CAAA,AAAuB,OAAtBhD,CAAAA,EAAKwB,AATD0F,EASOG,KAAK,AAAD,GAAerH,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsH,MAAM,AAAD,GACnE,CAAC,CAAE,CAAA,AAAuB,OAAtBH,CAAAA,EAAK3F,AAVD0F,EAUOG,KAAK,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG5C,MAAM,AAAD,CAC3E,GAEKgD,GAAG,CAAC,SAAUL,CAAC,EAMhB,IALI5H,EACAS,EACAC,EACAmH,EACAK,EASJ,MAAO,CACHrG,GAPK,AAAC,CAAA,AAAC,CAAA,AAAyB,OAAxB7B,CAAAA,EAAKkC,AAFL0F,EAEW9D,OAAO,AAAD,GAAe9D,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsD,OAAO,CAACR,YAAY,CAAC,OAAM,GAC1F,CAAA,AAAyB,OAAxBrC,CAAAA,EAAKyB,AAHH0F,EAGS9D,OAAO,AAAD,GAAerD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6C,OAAO,CAACR,YAAY,CAAC,QAAO,GACzF,CAAA,AAAyB,OAAxBpC,CAAAA,EAAKwB,AAJH0F,EAIS9D,OAAO,AAAD,GAAepD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG4C,OAAO,CAACR,YAAY,CAAC,SAAQ,GAAM,EAAC,EACjGtB,OAAO,CAACE,EAASqB,GAAG,CAAE,IACtBvB,OAAO,CAAC,QAAS,IACjBA,OAAO,CAAC,IAAK,IAGlBuD,EAAG,AAAC,CAAA,AAAuB,OAAtB8C,CAAAA,EAAK3F,AAVF0F,EAUQG,KAAK,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGG,MAAM,AAAD,GAAM,EAC1EhD,EAAG,AAAC,CAAA,AAAuB,OAAtBkD,CAAAA,EAAKhG,AAXF0F,EAWQG,KAAK,AAAD,GAAeG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGjD,MAAM,AAAD,GAAM,CAC9E,CACJ,GAEKrD,MAAM,CAAC,SAAUuG,CAAS,CAAEC,CAAK,CAAEC,CAAG,EACvC,MAAOF,AAAiB,KAAjBA,EAAUtG,EAAE,EACfsG,AAAgD,KAAhDA,EAAUtG,EAAE,CAACC,OAAO,CAAC,wBACrB,CAACuG,EAAIC,IAAI,CAAC,SAAUC,CAAS,CAAEC,CAAU,EACrC,OAAOD,EAAU1G,EAAE,GAAKsG,EAAUtG,EAAE,EAAI2G,EAAaJ,CACzD,EACR,GACK1F,OAAO,CAAC,SAAUyF,CAAS,EAC5B,IAAItG,EAAKsG,EAAUtG,EAAE,AACrBlC,CAAAA,CAAQ,CAACkC,EAAG,CAACmG,MAAM,CAAG,EAAIG,EAAUpD,CAAC,CACrCpF,CAAQ,CAACkC,EAAG,CAACoD,MAAM,CAAG,EAAIkD,EAAUnD,CAAC,CACrCrF,CAAQ,CAACkC,EAAG,CAAC4G,eAAe,CAAC,mBACjC,GAER,CA8HA,IAAIC,EAA+FlL,EAAoB,KACnHmL,EAAmHnL,EAAoBI,CAAC,CAAC8K,GAEzIE,EAAmIpL,EAAoB,KACvJqL,EAAuJrL,EAAoBI,CAAC,CAACgL,GAY7KE,EAA6B,AAAC9J,IAA+EI,OAAO,GAIxH,SAA4BkE,CAAO,CAAEyF,CAAW,CAAEzI,CAAK,CAAEC,CAAM,CAAEyI,CAAW,EACpD,KAAK,IAArBA,GAA0BA,CAAAA,EAAc,CAAA,EAC5C,IAAIlD,EAAOxC,GAAWA,EAAQiD,IAAI,CAAC,QAAS0C,EAAQnD,GAAQA,EAAKmD,KAAK,CAAC,kBACvE,GAAIA,EAAO,CACP,IAAIC,EAAcC,SAASC,aAAa,CAAC,GAAKH,CAAK,CAAC,EAAE,CAAG,SACzD,GAAIC,EAAa,CACb,IAAIrE,EAAOqE,EAAYpE,OAAO,GAG9B,GAAID,AAAe,IAAfA,EAAKvE,KAAK,CAAQ,CAClB,IAAI+I,EAAWH,EAAYI,aAAa,CAExChG,EAAQ5B,QAAQ,CAAC6H,GAAG,CAACC,WAAW,CAACN,GACjCrE,EAAOqE,EAAYpE,OAAO,GAC1BuE,EAASG,WAAW,CAACN,EACzB,CACA,IAAIlB,EAAS,EAAKnD,CAAAA,EAAKvE,KAAK,CAAG0I,CAAU,EACrC/D,EAAS8D,EAAcxI,EAASsE,EAAKtE,MAAM,CAAE8E,EAAcR,EAAKvE,KAAK,CAAGuE,EAAKtE,MAAM,CAAEkJ,EAAmBnJ,EAAQyI,EAAahE,EAAI,CAACF,EAAKvE,KAAK,CAAG,EAC/I+E,EAAcoE,GACdzB,CAAAA,EAASA,EAAS3C,EAAcoE,CAAe,EAEnDP,EAAY7E,YAAY,CAAC,eAAgB2E,EAAe1I,CAAAA,EAAQ0H,CAAK,GACrEkB,EAAY7E,YAAY,CAAC,YAAa,oBAClC,SAASD,MAAM,CAAC4D,EAAQ,KAAK5D,MAAM,CAACa,EAAQ,MAC5C,aAAab,MAAM,CAACW,EAAIiE,EAAchB,EAAS,EAAG,MAAM5D,MAAM,CAAC,CAACS,EAAKG,CAAC,CAAE,KAChF,CACJ,CACJ,IAIA,SAAyB0E,CAAK,CAAEC,CAAK,EACjC,IAAIpJ,EAASmJ,EAAME,GAAG,CAClB5E,EAAI,EAKR,OAJI2E,GAASb,EAA2Ba,EAAMvI,GAAG,IAC7C4D,EAAI0E,EAAMG,QAAQ,CAACF,EAAMvI,GAAG,CAAE,CAAA,GAC9Bb,EAASmJ,EAAME,GAAG,CAAG5E,GAElB,CACHzE,OAAQA,EACRyE,EAAGA,CACP,CACJ,EAyBI8E,GACIzM,EAAgB,SAAUU,CAAC,CAC3BgM,CAAC,EAMD,MAAO1M,AALHA,CAAAA,EAAgBe,OAAO4L,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUnM,CAAC,CAC1DgM,CAAC,EAAIhM,EAAEkM,SAAS,CAAGF,CAAG,GACd,SAAUhM,CAAC,CACnBgM,CAAC,EAAI,IAAK,IAAInC,KAAKmC,EAAOA,EAAEpL,cAAc,CAACiJ,IAAI7J,CAAAA,CAAC,CAAC6J,EAAE,CAAGmC,CAAC,CAACnC,EAAE,AAAD,CAAG,CAAA,EACvC7J,EAAGgM,EAC5B,EACO,SAAUhM,CAAC,CAAEgM,CAAC,EAEjB,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGrM,CAAG,CADtCV,EAAcU,EAAGgM,GAEjBhM,EAAEW,SAAS,CAAGqL,AAAM,OAANA,EAAa3L,OAAOiM,MAAM,CAACN,GAAMI,CAAAA,EAAGzL,SAAS,CAAGqL,EAAErL,SAAS,CAAE,IAAIyL,CAAG,CACtF,GAWAG,EAAgC,SAAUC,CAAM,EAEhD,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAO/C,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CAiBA,OApBAoC,EAAUQ,EAAgBC,GAS1BD,EAAe5L,SAAS,CAAC8L,QAAQ,CAAG,WAEhCD,EAAO7L,SAAS,CAAC8L,QAAQ,CAAChD,KAAK,CADnB,IAAI,CACuBE,WACvC,IAAI3F,EAASG,AAFD,IAAI,CAEGH,MAAM,CACrB0I,EAAQ1I,EAAOK,OAAO,CAACqI,KAAK,CAChC,GAAIvI,AAJQ,IAAI,CAIN4B,OAAO,EAAI5B,AAJT,IAAI,CAIWyC,SAAS,EAAI8F,EAAO,CAC3C,IAAId,EAAQc,CAAK,CAACvI,AALV,IAAI,CAKYkG,KAAK,CACrBqC,EAAMxK,MAAM,CAAC,CACrByK,EAAkCxI,AAP1B,IAAI,CAO4B4B,OAAO,CAAE6G,EAA+B5I,EAAO2H,KAAK,CAAEC,GAAOpJ,MAAM,CAAE2B,AAPrG,IAAI,CAOuGyC,SAAS,CAACrE,KAAK,EAAI,EAAG4B,AAPjI,IAAI,CAOmIyC,SAAS,CAACpE,MAAM,EAAIqK,IAAU1I,AAPrK,IAAI,CAOuKH,MAAM,CAACK,OAAO,CAAC4G,WAAW,EAAI,EACrN,CACJ,EACOsB,CACX,EA7BkB,AAACzB,IAA2IgC,WAAW,CAACC,MAAM,CAACpM,SAAS,CAACqM,UAAU,EAsCjMC,EAAmGxN,EAAoB,KACvHyN,EAAuHzN,EAAoBI,CAAC,CAACoN,GAE7IE,EAA+G1N,EAAoB,KACnI2N,EAAmI3N,EAAoBI,CAAC,CAACsN,GAEzJE,EAAuH5N,EAAoB,KAC3I6N,EAA2I7N,EAAoBI,CAAC,CAACwN,GAYjKE,GACIjO,EAAgB,SAAUU,CAAC,CAC3BgM,CAAC,EAOD,MAAO1M,AANHA,CAAAA,EAAgBe,OAAO4L,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUnM,CAAC,CAC1DgM,CAAC,EAAIhM,EAAEkM,SAAS,CAAGF,CAAG,GACd,SAAUhM,CAAC,CACnBgM,CAAC,EAAI,IAAK,IAAInC,KAAKmC,EAAO3L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACmL,EAC/DnC,IAAI7J,CAAAA,CAAC,CAAC6J,EAAE,CAAGmC,CAAC,CAACnC,EAAE,AAAD,CAAG,CAAA,EACI7J,EAAGgM,EAC5B,EACO,SAAUhM,CAAC,CAAEgM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIwB,UAAU,uBAAyBC,OAAOzB,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGrM,CAAG,CADtCV,EAAcU,EAAGgM,GAEjBhM,EAAEW,SAAS,CAAGqL,AAAM,OAANA,EAAa3L,OAAOiM,MAAM,CAACN,GAAMI,CAAAA,EAAGzL,SAAS,CAAGqL,EAAErL,SAAS,CAAE,IAAIyL,CAAG,CACtF,GAkBAsB,EAAe,AAAC5C,IAA2IgC,WAAW,CAACC,MAAM,CACjLY,AA9TkB,CAAA,CACdC,QA1lBJ,SAAiBC,CAAU,CAAEC,CAAW,CAAEC,CAAgB,EACtD,IAAIC,EAAaF,EAAYnN,SAAS,CAACqM,UAAU,CAC7CiB,EAAaD,EAAWrN,SAAS,AAChCsN,CAAAA,EAAWpH,0BAA0B,GACtCzF,EAASyM,EAAY,YAAanK,GAClCtC,EAASyM,EAAY,SAAUpJ,GAC/BlD,EAAO0M,EAAY,CACfpH,2BAA4Bf,CAChC,GACA1E,EAAS4M,EAAY,YAAa5I,GAClChE,EAAS0M,EAAa,SAAUvH,GAChC5E,EAAKmM,EAAYnN,SAAS,CAAE,WAAY2I,GAExClI,EAAS0M,EAAa,cAAelE,GACrCxI,EAAS0M,EAAa,kBAAmBlE,GACzCrI,EAAOwM,EAAiBpN,SAAS,CAAE,CAC/BqF,WAAY8B,CAChB,GACA1G,EAAS2M,EAAkB,eAAgB1I,GAEnD,EAukBIzD,SAAUA,CACd,CAAA,EA2TuBgM,OAAO,CAAEhD,IAAyGsC,IAA6GI,KACtP,IAAIY,EAA6B,AAACjN,IAA+EC,UAAU,CACjDiN,GAtI1E,SAA2BC,CAAW,CAAEzC,CAAK,EACzC,IAAI0C,EAAW1C,EAAMlF,KAAK,CAAC4H,QAAQ,CAC/BA,GACAD,EAAY5F,IAAI,CAAC,CACb8F,SAAUD,AAAW,KAAXA,EACVpE,OAAQoE,EAAW,GAAK,CAC5B,EAER,EA+HIE,GAA2B,AAACtN,IAA+EG,QAAQ,CAAEoN,GAA0B,AAACvN,IAA+EI,OAAO,CAAEoN,GAAwB,AAACxN,IAA+EO,KAAK,CAAEkN,GAAa,AAACzN,IAA+EyN,UAAU,CAAEC,GAAuB,AAAC1N,IAA+EQ,IAAI,CAe3iBmN,GAAiC,SAAUpC,CAAM,EAEjD,SAASoC,IACL,OAAOpC,AAAW,OAAXA,GAAmBA,EAAO/C,KAAK,CAAC,IAAI,CAAEE,YAAc,IAAI,AACnE,CA4IA,OA/IA4D,EAAwBqB,EAAiBpC,GAqBzCoC,EAAgBjO,SAAS,CAACsH,OAAO,CAAG,SAAU4G,CAAI,EAC9C,IAAepI,EAAQxE,AAAd,IAAI,CAAawE,KAAK,CAAEuD,EAAQ/H,AAAhC,IAAI,CAA+B+H,KAAK,CAAE9D,EAAYgI,EAA2B,IAAI,CAAC7J,OAAO,CAAC6B,SAAS,EAE5G4I,EAAmB,CACf,IAAI,CAACC,gBAAgB,GACrB7I,EAAUE,QAAQ,CAClBF,EAAU8I,MAAM,CAChB9I,EAAU+I,KAAK,CAClB,CAACC,IAAI,CAAC,KACPC,EAAoB1I,EAAM2I,WAAW,CAACN,EAAiB,CAE3D,GAAID,GAAQ7E,EAAO,CACf,IAAIqF,EAAU5I,EAAM6I,UAAU,CAAC,IAAI,EAE9BH,IACDE,EAAQpI,CAAC,CAAGoI,EAAQ7M,MAAM,CAC1B6M,EAAQ7M,MAAM,CAAG,EACjB2M,EAAoB1I,EAAM9C,QAAQ,CAAC4L,QAAQ,CAACF,GAC5C5I,EAAM2I,WAAW,CAACN,EAAiB,CAAGK,GAE1CnF,EAAMwF,IAAI,CAACL,EAEf,MACK,GAAIA,GAEL,CAACA,EAAkBM,QAAQ,CAAC,wBAAyB,CACrD,IAAIC,EAAWjJ,EAAM6I,UAAU,CAAC,IAAI,EACpCH,EACKQ,QAAQ,CAAC,wBACT1H,OAAO,CAACyH,EAAUxJ,EAC3B,CACJ,EACA0I,EAAgBjO,SAAS,CAACiP,gBAAgB,CAAG,WAAc,EAC3DhB,EAAgBjO,SAAS,CAACkP,kBAAkB,CAAG,WAAc,EAC7DjB,EAAgBjO,SAAS,CAACmP,YAAY,CAAG,SAAU3L,CAAK,EACpD,IAAI2L,EAAetD,EAAO7L,SAAS,CAACmP,YAAY,CAACrG,KAAK,CAAC,IAAI,CACvDE,WAGA+C,EAAQqD,AAFQ,IAAI,CAAC1L,OAAO,CAENqI,KAAK,CAC/B,GAAIvI,GAASA,EAAMyC,SAAS,EAAI8F,EAAO,CACnC,IAAId,EAAQc,CAAK,CAACvI,EAAMkG,KAAK,CAAGqC,EAAMxK,MAAM,CAAC,CACzCD,EAAK+N,EAAgChM,AAJhC,IAAI,CAImC2H,KAAK,CACjDC,GACA3E,EAAIhF,EAAGgF,CAAC,CACRzE,EAASP,EAAGO,MAAM,CAClByN,EAAUrE,EAAM1L,UAAU,AAE1B+P,CAAAA,IAAY9L,EAAM8L,OAAO,EACzB9L,EAAM8L,OAAO,CAAGA,EAChBH,EAAa/H,IAAI,CAAG,CAChB5F,QAAS,CACLE,KAAM,CACFrC,EAAGiQ,EACHlI,KAAM+H,EAAa/H,IAAI,CACvBkB,YAAa6G,CAAY,CAAC,eAAe,CACzC9G,OAAQ8G,EAAa9G,MAAM,AAC/B,EACAhC,EAAG7C,EAAMyC,SAAS,CAACI,CAAC,CACpBC,EAAGA,EACH1E,MAAO4B,EAAMyC,SAAS,CAACrE,KAAK,EAAI,EAChCC,OAAQA,EACR8F,oBAAqB,oBACrBM,gBAAiB,OACjBtG,MAAO,SACX,CACJ,GAEK6B,EAAM8L,OAAO,EAAI9L,EAAM4B,OAAO,EACnC,OAAO+J,EAAa/H,IAAI,AAEhC,CAGA,OAFA,OAAO+H,EAAa9G,MAAM,CAC1B,OAAO8G,EAAa7G,WAAW,CACxB6G,CACX,EAIAlB,EAAgBjO,SAAS,CAACuP,WAAW,CAAG,WACpC,IAAIC,EAAW3D,EAAO7L,SAAS,CAACuP,WAAW,CAACzG,KAAK,CAAC,IAAI,CAClDE,WAEA+C,EAAQ1I,AADC,IAAI,CACEK,OAAO,CAACqI,KAAK,CAUhC,OATIA,GACAA,EAAM/H,OAAO,CAAC,SAAUtC,CAAI,EACpBmM,GAAwBnM,EAAKgB,GAAG,GAChCmL,GAAwB2B,EAASC,OAAO,GACxC/N,EAAKgB,GAAG,CAAG8M,EAASC,OAAO,EAC3BD,CAAAA,EAASC,OAAO,CAAG/N,EAAKgB,GAAG,AAAD,CAElC,GAEG8M,CACX,EAMAvB,EAAgByB,cAAc,CAAG5B,GAAsBf,EAAa2C,cAAc,CAmBlF,CACIpF,YAAa,CACjB,GACO2D,CACX,EAAElB,GAmCF,SAAS4C,GAAkBC,CAAK,EAE5B,IAAIC,EAAYnQ,OACPoQ,IAAI,CAACF,EAAMrM,MAAM,EACjBL,MAAM,CAAC,SAAUgG,CAAC,EAAI,OAAOA,EAAE6G,KAAK,CAAC,KAAKxO,MAAM,CAAG,CAAG,GAAIyO,EAAYJ,EAAMK,IAAI,CAACnK,KAAK,CAACzC,MAAM,CAAE6M,EAAgBL,EAAUtG,GAAG,CAAC,SAAU/J,CAAG,EAC3I,OAAO2Q,WAAW3Q,EAAIuQ,KAAK,CAAC,IAAI,CAAC,EAAE,CAC3C,GACIK,EAAc,GAClBF,EAAclM,OAAO,CAAC,SAAU0F,CAAK,EAC7BsG,CAAS,CAACtG,EAAM,EAAIsG,CAAS,CAACtG,EAAM,CAACpG,OAAO,EAC5C8M,CAAAA,EAAc1G,CAAI,CAE1B,GACA,IAAIrG,EAASuM,EAAMK,IAAI,CAACnK,KAAK,CAACzC,MAAM,CAAC+M,EAAY,CACjD,GAAI/M,GACAA,EAAOgN,EAAE,CAAC,cACVT,EAAMK,IAAI,CAACK,OAAO,IAClBjN,EAAOkN,KAAK,CAACD,OAAO,GAAI,CACxB,IAAIC,EAAQlN,EAAOkN,KAAK,CACpB7M,EAAUkM,EAAMK,IAAI,CAACvM,OAAO,CAC5BoC,EAAQ8J,EAAMK,IAAI,CAACnK,KAAK,CACxB0K,EAAcZ,EAAMa,MAAM,CAC1BC,EAAUH,EAAMpF,QAAQ,CAACyE,EAAMvJ,CAAC,CAChC,CAAA,GACAA,EAAIP,EAAM4H,QAAQ,CAAG6C,EAAMrF,GAAG,CAAGwF,EAAUA,EAC3C3E,EAAQ1I,EAAOK,OAAO,CAACqI,KAAK,EAAI,EAAE,CAClCrC,EAAQkG,EAAMvJ,CAAC,CAAG0F,EAAMxK,MAAM,CAC9B0J,EAAQc,CAAK,CAACrC,EAAM,CACpB9H,EAAQyB,EAAOsN,gBAAgB,EAC3BtN,EAAOsN,gBAAgB,GAAG/O,KAAK,CACnCN,EAAK+N,EAAgChM,EAAO2H,KAAK,CACjDC,GACApJ,EAASP,EAAGO,MAAM,CAClByE,EAAIhF,EAAGgF,CAAC,CACRsK,EAAgBlN,EAAQ8M,WAAW,CACnClI,EAAc0F,GAAqB4C,GAAiBA,EAActG,WAAW,CAC7EjH,EAAOK,OAAO,CAAC4G,WAAW,CAAE,GAChC,GAAI,CAACkG,GACDI,GACAA,EAAcC,OAAO,EACrB5F,EACK2E,EAAMnC,WAAW,EAClBmC,CAAAA,EAAMnC,WAAW,CAAG3H,EAAM9C,QAAQ,CAAC8N,CAAC,CAAC,gBAChChJ,GAAG,EAAC,EAEb8H,EAAMnC,WAAW,CAAC5F,IAAI,CAAC,CACnBkJ,WAAYjL,EAAM4H,QAAQ,CACtBkC,EAAMK,IAAI,CAACe,GAAG,CAAGT,EAAMS,GAAG,CAC9BC,WAAYnL,EAAM4H,QAAQ,CACtB6C,EAAMS,GAAG,CAAGpB,EAAMK,IAAI,CAACe,GAAG,AAClC,GACApB,EAAMa,MAAM,CAAG3K,EAAM9C,QAAQ,CAACmF,IAAI,CAAC9B,EAAGC,EAAG1E,EAAOC,GAC3CgG,IAAI,CAAC,CACNT,KAAM,CACF5F,QAAS,CACLE,KAAM,CACFrC,EAAG4L,EAAM1L,UAAU,CACnB6H,KAAMwJ,EAAcjP,KAAK,EACrB,UACJ2G,YAAaA,EACbD,OAAQuI,EAAcM,WAAW,EAC7B,aACR,EACA7K,EAAGA,EACHC,EAAGA,EACH1E,MAAOA,EACPC,OAAQA,EACR8F,oBAAqB,oBACrBM,gBAAiB,OACjBtG,MAAO,SACX,CACJ,CACJ,GACKmG,GAAG,CAAC8H,EAAMnC,WAAW,EAC1BD,GAAkCoC,EAAMnC,WAAW,CAAEmC,EAAMK,IAAI,EAC/DkB,EAAmCvB,EAAMa,MAAM,CAAE5O,EAAQD,EAAOC,EAAQyG,GACxEsH,EAAMwB,SAAS,CAAC/N,EAAOgO,YAAY,EAAI,EAAGhO,EAAOiO,IAAI,EAAI,QAExD,GAAId,GAAeZ,EAAMnC,WAAW,CAAE,CACvC+C,EAAYlJ,OAAO,CAAC,CAChBjB,EAAGA,EACHC,EAAGA,EACH1E,MAAOA,EACPC,OAAQA,CACZ,GACA,IAAuCuF,EAAOoJ,EAAY3I,IAAI,CAAC,QAAS0C,EAAQnD,GAAQA,EAAKmD,KAAK,CAA7E,kBACjBA,GAASzE,EAAM9C,QAAQ,CAACuB,eAAe,EACvCuB,EAAM9C,QAAQ,CAACuB,eAAe,CAACgG,CAAK,CAAC,EAAE,CAACxB,KAAK,CAAC,GAAG,CAC5CzB,OAAO,CAAC,CACTjB,EAAGA,EACHC,EAAGA,EACH1E,MAAOA,EACPC,OAAQA,CACZ,GAEJ+N,EAAMnC,WAAW,CAACnG,OAAO,CAAC,CACtByJ,WAAYjL,EAAM4H,QAAQ,CACtBkC,EAAMK,IAAI,CAACe,GAAG,CAAGT,EAAMS,GAAG,CAC9BC,WAAYnL,EAAM4H,QAAQ,CACtB6C,EAAMS,GAAG,CAAGpB,EAAMK,IAAI,CAACe,GAAG,AAClC,GACAxD,GAAkCoC,EAAMnC,WAAW,CAAEmC,EAAMK,IAAI,EAC/DkB,EAAmCX,EAAa3O,EAAQD,EAAOC,EAAQyG,GACvEsH,EAAMwB,SAAS,CAAC/N,EAAOgO,YAAY,EAAI,EAAGhO,EAAOiO,IAAI,EAAI,EAC7D,CACJ,MACS1B,EAAMa,MAAM,EAAIb,EAAMnC,WAAW,GACtCmC,EAAMa,MAAM,CAACjM,OAAO,GACpBoL,EAAMa,MAAM,CAAG,KAAK,EACpBb,EAAMnC,WAAW,CAACjJ,OAAO,GACzBoL,EAAMnC,WAAW,CAAG,KAAK,EAEjC,CAIA,SAAS8D,GAAazL,CAAK,CAAE0L,CAAQ,EAC7B1L,EAAM2L,IAAI,EACV3L,EAAM2L,IAAI,CAACzN,OAAO,CAAC,SAAUiM,CAAI,EACxBA,EAAKyB,QAAQ,EAKlB3D,GAFakC,EAAKyB,QAAQ,CAACC,MAAM,CAEd,SAAUC,CAAI,EAC7B7D,GAAW6D,EAAM,SAAUhC,CAAK,EAC5B4B,EAAS5B,EACb,EACJ,EACJ,EAER,CAuBA,SAASiC,GAAuB/L,CAAK,EACjCyL,GAAazL,EAAO,SAAU8J,CAAK,EAC3BA,EAAMa,MAAM,EAAIb,EAAMnC,WAAW,GACjCmC,EAAMa,MAAM,CAACjM,OAAO,GACpBoL,EAAMnC,WAAW,CAACjJ,OAAO,GACzB,OAAOoL,EAAMa,MAAM,CACnB,OAAOb,EAAMnC,WAAW,CAEhC,EACJ,CAhMAG,GAAyBK,GAAiB,cAAe,WACrD,IAAI5K,EAAS,IAAI,CAAE0I,EAAQ1I,EAAOK,OAAO,CAACqI,KAAK,CAAE+F,EAAiB,iBAClEzO,EAAOE,MAAM,CAACS,OAAO,CAAC,SAAUR,CAAK,EACjC,GAAIA,EAAM4B,OAAO,EAAI5B,EAAMyC,SAAS,EAAI8F,EAAO,CAC3C,IAAId,EAAQc,CAAK,CAACvI,EAAMkG,KAAK,CAAGqC,EAAMxK,MAAM,CAAC,CACzC6F,EAAO5D,EAAM4B,OAAO,CAACyC,IAAI,CAAC,QAC1B0C,EAAQnD,GAAQA,EAAKmD,KAAK,CAACuH,GAC3BxQ,EAAK+N,EAAgChM,EAAO2H,KAAK,CACjDC,GACA3E,EAAIhF,EAAGgF,CAAC,CACRzE,EAASP,EAAGO,MAAM,CACtB,GAAI0I,GAASlH,EAAOyC,KAAK,CAAC9C,QAAQ,CAACuB,eAAe,CAAE,CAChD,IAAIwN,EAAiB1O,EAAOyC,KAAK,CAAC9C,QAAQ,CAACuB,eAAe,CAACgG,CAAK,CAAC,EAAE,CAACxB,KAAK,CAAC,GAAG,CACzEgJ,GACAA,EAAezK,OAAO,CAAC,CACnBjB,EAAG7C,EAAMyC,SAAS,CAACI,CAAC,CACpBC,EAAGA,EACH1E,MAAO4B,EAAMyC,SAAS,CAACrE,KAAK,EAAI,EAChCC,OAAQA,CACZ,EAER,CACAsP,EAAmC3N,EAAM4B,OAAO,CAAEiK,EAAgChM,EAAO2H,KAAK,CAAEC,GAAOpJ,MAAM,CAAE2B,EAAMyC,SAAS,CAACrE,KAAK,EAAI,EAAG4B,EAAMyC,SAAS,CAACpE,MAAM,EAAIqK,IAAU7I,EAAOK,OAAO,CAAC4G,WAAW,EAAI,EACjN,CACJ,EACJ,GAwIAsD,GAA0B3D,IAAwG,SAAU,WACxIsH,GAAa,IAAI,CAAE5B,GACvB,GACA/B,GAA0BnB,IAAwH,iBAAkB,SAAUuF,CAAC,EAC3K,GAAI,IAAI,CAACvB,MAAM,CAAE,CACb,IAAInP,EAAK,IAAI,CAAC2O,IAAI,CACdnK,EAAQxE,EAAGwE,KAAK,CAChBoF,EAAM5J,EAAG4J,GAAG,CACZ+G,EAAUD,EAAEC,OAAO,CACnBrQ,EAAQoQ,EAAEpQ,KAAK,CACfmP,EAAajL,EAAM4H,QAAQ,CAAGuE,EAAUnM,EAAMyK,KAAK,CAAC,EAAE,CAACrF,GAAG,CAAG+G,EAC7DhB,EAAanL,EAAM4H,QAAQ,CAAG,CAACxC,EAAM,EACzC,IAAI,CAACuF,MAAM,CAAC5I,IAAI,CAAC,CACbkJ,WAAYA,EACZE,WAAYA,CAChB,GACA,IAAI,CAACR,MAAM,CAACnJ,OAAO,CAAC,CAAE1F,MAAOA,CAAM,EACvC,CACJ,GAeAgM,GAA0B3D,IAAwG,iBAAkB,WAChJ4H,GAAuB,IAAI,CAC/B,GACAjE,GAA0B3D,IAAwG,eAAgB,WAC9I4H,GAAuB,IAAI,CAC/B,GACA5D,GAAgBjO,SAAS,CAACqM,UAAU,CA3ayBT,EA4a7DzB,IAA0I+H,kBAAkB,CAAC,YAAajE,IAoL7I,IAAI7N,GAAkBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,GAET"}