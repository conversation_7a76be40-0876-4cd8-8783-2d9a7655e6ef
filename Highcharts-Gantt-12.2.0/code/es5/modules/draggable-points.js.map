{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/draggable-points\n * @requires highcharts\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/draggable-points\", [[\"highcharts/highcharts\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/draggable-points\"] = factory(require(\"highcharts\"));\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ draggable_points_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/DraggablePoints/DragDropUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add multiple event listeners with the same handler to the same element.\n *\n * @private\n * @function addEvents\n * @param {T} el\n *        The element or object to add listeners to.\n * @param {Array<string>} types\n *        Array with the event types this handler should apply to.\n * @param {Function|Highcharts.EventCallbackFunction<T>} fn\n *        The function callback to execute when the events are fired.\n * @param {Highcharts.EventOptionsObject} [options]\n *        Event options:\n *        - `order`: The order the event handler should be called. This opens\n *          for having one handler be called before another, independent of in\n *          which order they were added.\n * @return {Function}\n *         A callback function to remove the added events.\n * @template T\n */\nfunction addEvents(el, types, fn, options) {\n    var removeFuncs = types.map(function (type) { return addEvent(el,\n        type,\n        fn,\n        options); });\n    return function () {\n        for (var _i = 0, removeFuncs_1 = removeFuncs; _i < removeFuncs_1.length; _i++) {\n            var fn_1 = removeFuncs_1[_i];\n            fn_1();\n        }\n    };\n}\n/**\n * Utility function to count the number of props in an object.\n *\n * @private\n * @function countProps\n *\n * @param {Object} obj\n *        The object to count.\n *\n * @return {number}\n *         Number of own properties on the object.\n */\nfunction countProps(obj) {\n    return Object.keys(obj).length;\n}\n/**\n * Utility function to get the value of the first prop of an object. (Note that\n * the order of keys in an object is usually not guaranteed.)\n *\n * @private\n * @function getFirstProp\n * @param {Highcharts.Dictionary<T>} obj\n *        The object to count.\n * @return {T}\n *         Value of the first prop in the object.\n * @template T\n */\nfunction getFirstProp(obj) {\n    for (var p in obj) {\n        if (Object.hasOwnProperty.call(obj, p)) {\n            return obj[p];\n        }\n    }\n}\n/**\n * Take a mouse/touch event and return the event object with chartX/chartY.\n *\n * @private\n * @function getNormalizedEvent\n * @param {global.PointerEvent} e\n *        The event to normalize.\n * @param {Highcharts.Chart} chart\n *        The related chart.\n * @return {Highcharts.PointerEventLObject}\n *         The normalized event.\n */\nfunction getNormalizedEvent(e, chart) {\n    var _a;\n    return (typeof e.chartX === 'undefined' ||\n        typeof e.chartY === 'undefined' ?\n        ((_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.normalize(e)) || e :\n        e);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DragDropUtilities = {\n    addEvents: addEvents,\n    countProps: countProps,\n    getFirstProp: getFirstProp,\n    getNormalizedEvent: getNormalizedEvent\n};\n/* harmony default export */ var DraggablePoints_DragDropUtilities = (DragDropUtilities);\n\n;// ./code/es5/es-modules/Extensions/DraggablePoints/DragDropDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The draggable-points module allows points to be moved around or modified in\n * the chart. In addition to the options mentioned under the `dragDrop` API\n * structure, the module fires three events,\n * [point.dragStart](plotOptions.series.point.events.dragStart),\n * [point.drag](plotOptions.series.point.events.drag) and\n * [point.drop](plotOptions.series.point.events.drop).\n *\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/resize-column\n *         Draggable column and line series\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/bar-series\n *         Draggable bar\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/drag-bubble\n *         Draggable bubbles\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/drag-xrange\n *         Draggable X range series\n * @sample {highcharts|highstock}\n *         highcharts/dragdrop/undraggable-points\n *         Dragging disabled for specific points\n * @sample {highmaps}\n *         maps/series/draggable-mappoint\n *         Draggable Map Point series\n *\n * @declare      Highcharts.SeriesDragDropOptionsObject\n * @since        6.2.0\n * @requires     modules/draggable-points\n * @optionparent plotOptions.series.dragDrop\n */\nvar DragDropDefaults = {\n    /**\n     * Set the minimum X value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number|string}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMinX\n     */\n    /**\n     * Set the maximum X value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number|string}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMaxX\n     */\n    /**\n     * Set the minimum Y value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMinY\n     */\n    /**\n     * Set the maximum Y value the points can be moved to.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Limit dragging\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Limit dragging\n     *\n     * @type      {number}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragMaxY\n     */\n    /**\n     * The X precision value to drag to for this series. Set to 0 to disable. By\n     * default this is disabled, except for category axes, where the default is\n     * `1`.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragPrecisionX\n     */\n    /**\n     * The Y precision value to drag to for this series. Set to 0 to disable. By\n     * default this is disabled, except for category axes, where the default is\n     * `1`.\n     *\n     * @type      {number}\n     * @default   0\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.dragPrecisionY\n     */\n    /**\n     * Enable dragging in the X dimension.\n     *\n     * @type      {boolean}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.draggableX\n     */\n    /**\n     * Enable dragging in the Y dimension. Note that this is not supported for\n     * TreeGrid axes (the default axis type in Gantt charts).\n     *\n     * @type      {boolean}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.draggableY\n     */\n    /**\n     * Group the points by a property. Points with the same property value will\n     * be grouped together when moving.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         Drag grouped points\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag grouped points\n     *\n     * @type      {string}\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.groupBy\n     */\n    /**\n     * Update points as they are dragged. If false, a guide box is drawn to\n     * illustrate the new point size.\n     *\n     * @sample {gantt} gantt/dragdrop/drag-gantt\n     *         liveRedraw disabled\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         liveRedraw disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     6.2.0\n     * @apioption plotOptions.series.dragDrop.liveRedraw\n     */\n    /**\n     * Set a key to hold when dragging to zoom the chart. This is useful to\n     * avoid zooming while moving points. Should be set different than\n     * [chart.panKey](#chart.panKey).\n     *\n     * @type       {string}\n     * @since      6.2.0\n     * @validvalue [\"alt\", \"ctrl\", \"meta\", \"shift\"]\n     * @deprecated\n     * @requires  modules/draggable-points\n     * @apioption  chart.zoomKey\n     */\n    /**\n     * Callback that fires when starting to drag a point. The mouse event object\n     * is passed in as an argument. If a drag handle is used, `e.updateProp` is\n     * set to the data property being dragged. The `this` context is the point.\n     * See [drag and drop options](plotOptions.series.dragDrop).\n     *\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag events\n     *\n     * @type      {Highcharts.PointDragStartCallbackFunction}\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.series.point.events.dragStart\n     */\n    /**\n     * Callback that fires while dragging a point. The mouse event is passed in\n     * as parameter. The original data can be accessed from `e.origin`, and the\n     * new point values can be accessed from `e.newPoints`. If there is only a\n     * single point being updated, it can be accessed from `e.newPoint` for\n     * simplicity, and its ID can be accessed from `e.newPointId`. The `this`\n     * context is the point being dragged. To stop the default drag action,\n     * return false. See [drag and drop options](plotOptions.series.dragDrop).\n     *\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag events\n     * @sample {highcharts|highstock} highcharts/dragdrop/undraggable-points\n     *         Dragging disabled for specific points\n     *\n     * @type      {Highcharts.PointDragCallbackFunction}\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.series.point.events.drag\n     */\n    /**\n     * Callback that fires when the point is dropped. The parameters passed are\n     * the same as for [drag](#plotOptions.series.point.events.drag). To stop\n     * the default drop action, return false. See\n     * [drag and drop options](plotOptions.series.dragDrop).\n     *\n     * @sample {highcharts} highcharts/dragdrop/drag-xrange\n     *         Drag events\n     * @sample {highcharts|highstock} highcharts/dragdrop/undraggable-points\n     *         Dragging disabled for specific points\n     *\n     * @type      {Highcharts.PointDropCallbackFunction}\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.series.point.events.drop\n     */\n    /**\n     * Point specific options for the draggable-points module. Overrides options\n     * on `series.dragDrop`.\n     *\n     * @declare   Highcharts.SeriesLineDataDragDropOptions\n     * @extends   plotOptions.series.dragDrop\n     * @since     6.2.0\n     * @requires  modules/draggable-points\n     * @apioption series.line.data.dragDrop\n     */\n    /**\n     * The amount of pixels to drag the pointer before it counts as a drag\n     * operation. This prevents drag/drop to fire when just clicking or\n     * selecting points.\n     *\n     * @type      {number}\n     * @default   2\n     * @since     6.2.0\n     */\n    dragSensitivity: 2,\n    /**\n     * Options for the drag handles available in column series.\n     *\n     * @declare      Highcharts.DragDropHandleOptionsObject\n     * @since        6.2.0\n     * @optionparent plotOptions.column.dragDrop.dragHandle\n     */\n    dragHandle: {\n        /**\n         * Function to define the SVG path to use for the drag handles. Takes\n         * the point as argument. Should return an SVG path in array format. The\n         * SVG path is automatically positioned on the point.\n         *\n         * @type      {Function}\n         * @since     6.2.0\n         * @apioption plotOptions.column.dragDrop.dragHandle.pathFormatter\n         */\n        // pathFormatter: null,\n        /**\n         * The mouse cursor to use for the drag handles. By default this is\n         * intelligently switching between `ew-resize` and `ns-resize` depending on\n         * the direction the point is being dragged.\n         *\n         * @type      {string}\n         * @since     6.2.0\n         * @apioption plotOptions.column.dragDrop.dragHandle.cursor\n         */\n        // cursor: null,\n        /**\n         * The class name of the drag handles. Defaults to `highcharts-drag-handle`.\n         *\n         * @since 6.2.0\n         */\n        className: 'highcharts-drag-handle',\n        /**\n         * The fill color of the drag handles.\n         *\n         * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since 6.2.0\n         */\n        color: '#fff',\n        /**\n         * The line color of the drag handles.\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 6.2.0\n         */\n        lineColor: 'rgba(0, 0, 0, 0.6)',\n        /**\n         * The line width for the drag handles.\n         *\n         * @since 6.2.0\n         */\n        lineWidth: 1,\n        /**\n         * The z index for the drag handles.\n         *\n         * @since 6.2.0\n         */\n        zIndex: 901\n    },\n    /**\n     * Style options for the guide box. The guide box has one state by default,\n     * the `default` state.\n     *\n     * @declare Highcharts.PlotOptionsSeriesDragDropGuideBoxOptions\n     * @since 6.2.0\n     * @type  {Highcharts.Dictionary<Highcharts.DragDropGuideBoxOptionsObject>}\n     */\n    guideBox: {\n        /**\n         * Style options for the guide box default state.\n         *\n         * @declare Highcharts.DragDropGuideBoxOptionsObject\n         * @since   6.2.0\n         */\n        'default': {\n            /**\n             * CSS class name of the guide box in this state. Defaults to\n             * `highcharts-drag-box-default`.\n             *\n             * @since 6.2.0\n             */\n            className: 'highcharts-drag-box-default',\n            /**\n             * Width of the line around the guide box.\n             *\n             * @since 6.2.0\n             */\n            lineWidth: 1,\n            /**\n             * Color of the border around the guide box.\n             *\n             * @type  {Highcharts.ColorString}\n             * @since 6.2.0\n             */\n            lineColor: '#888',\n            /**\n             * Guide box fill color.\n             *\n             * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @since 6.2.0\n             */\n            color: 'rgba(0, 0, 0, 0.1)',\n            /**\n             * Guide box cursor.\n             *\n             * @since 6.2.0\n             */\n            cursor: 'move',\n            /**\n             * Guide box zIndex.\n             *\n             * @since 6.2.0\n             */\n            zIndex: 900\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var DraggablePoints_DragDropDefaults = (DragDropDefaults);\n\n;// ./code/es5/es-modules/Extensions/DraggablePoints/DraggableChart.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\n\nvar DraggableChart_addEvents = DraggablePoints_DragDropUtilities.addEvents, DraggableChart_countProps = DraggablePoints_DragDropUtilities.countProps, DraggableChart_getFirstProp = DraggablePoints_DragDropUtilities.getFirstProp, DraggableChart_getNormalizedEvent = DraggablePoints_DragDropUtilities.getNormalizedEvent;\n\n\nvar doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc;\n\nvar DraggableChart_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add events to document and chart if the chart is draggable.\n *\n * @private\n * @function addDragDropEvents\n * @param {Highcharts.Chart} chart\n *        The chart to add events to.\n */\nfunction addDragDropEvents(chart) {\n    var container = chart.container;\n    // Only enable if we have a draggable chart\n    if (isChartDraggable(chart)) {\n        DraggableChart_addEvents(container, ['mousedown', 'touchstart'], function (e) {\n            mouseDown(DraggableChart_getNormalizedEvent(e, chart), chart);\n        });\n        DraggableChart_addEvents(container, ['mousemove', 'touchmove'], function (e) {\n            mouseMove(DraggableChart_getNormalizedEvent(e, chart), chart);\n        }, {\n            passive: false\n        });\n        DraggableChart_addEvent(container, 'mouseleave', function (e) {\n            mouseUp(DraggableChart_getNormalizedEvent(e, chart), chart);\n        });\n        chart.unbindDragDropMouseUp = DraggableChart_addEvents(doc, ['mouseup', 'touchend'], function (e) {\n            mouseUp(DraggableChart_getNormalizedEvent(e, chart), chart);\n        }, {\n            passive: false\n        });\n        // Add flag to avoid doing this again\n        chart.hasAddedDragDropEvents = true;\n        // Add cleanup to make sure we don't pollute document\n        DraggableChart_addEvent(chart, 'destroy', function () {\n            if (chart.unbindDragDropMouseUp) {\n                chart.unbindDragDropMouseUp();\n            }\n        });\n    }\n}\n/**\n * Remove the chart's drag handles if they exist.\n *\n * @private\n * @function Highcharts.Chart#hideDragHandles\n */\nfunction chartHideDragHandles() {\n    var chart = this,\n        dragHandles = (chart.dragHandles || {});\n    if (dragHandles) {\n        for (var _i = 0, _a = Object.keys(dragHandles); _i < _a.length; _i++) {\n            var key = _a[_i];\n            if (dragHandles[key].destroy) {\n                dragHandles[key].destroy();\n            }\n        }\n        delete chart.dragHandles;\n    }\n}\n/**\n * Set the state of the guide box.\n *\n * @private\n * @function Highcharts.Chart#setGuideBoxState\n * @param {string} state\n *        The state to set the guide box to.\n * @param {Highcharts.Dictionary<Highcharts.DragDropGuideBoxOptionsObject>} [options]\n *        Additional overall guideBox options to consider.\n * @return {Highcharts.SVGElement}\n *         The modified guide box.\n */\nfunction chartSetGuideBoxState(state, options) {\n    var guideBox = this.dragGuideBox,\n        guideBoxOptions = merge(DraggablePoints_DragDropDefaults.guideBox,\n        options),\n        stateOptions = merge(guideBoxOptions['default'], // eslint-disable-line dot-notation\n        guideBoxOptions[state]);\n    return guideBox\n        .attr({\n        'class': stateOptions.className,\n        stroke: stateOptions.lineColor,\n        strokeWidth: stateOptions.lineWidth,\n        fill: stateOptions.color,\n        cursor: stateOptions.cursor,\n        zIndex: stateOptions.zIndex\n    })\n        // Use pointerEvents 'none' to avoid capturing the click event\n        .css({ pointerEvents: 'none' });\n}\n/**\n * Check whether the zoomKey or panKey is pressed.\n *\n * @private\n * @function zoomOrPanKeyPressed\n * @param {global.Event} e\n *        A mouse event.\n * @return {boolean}\n *         True if the zoom or pan keys are pressed. False otherwise.\n */\nfunction chartZoomOrPanKeyPressed(e) {\n    // Check whether the panKey and zoomKey are set in chart.userOptions\n    var chart = this, chartOptions = chart.options.chart || {}, panKey = chartOptions.panKey && chartOptions.panKey + 'Key', zoomKey = chart.zooming.key && chart.zooming.key + 'Key';\n    return (e[zoomKey] || e[panKey]);\n}\n/**\n * Composes the chart class with essential functions to support draggable\n * points.\n *\n * @private\n * @function compose\n *\n * @param {Highcharts.Chart} ChartClass\n *        Class constructor of chart.\n */\nfunction compose(ChartClass) {\n    var chartProto = ChartClass.prototype;\n    if (!chartProto.hideDragHandles) {\n        chartProto.hideDragHandles = chartHideDragHandles;\n        chartProto.setGuideBoxState = chartSetGuideBoxState;\n        chartProto.zoomOrPanKeyPressed = chartZoomOrPanKeyPressed;\n        DraggableChart_addEvent(ChartClass, 'render', onChartRender);\n    }\n}\n/**\n * Default mouse move handler while dragging. Handles updating points or guide\n * box.\n *\n * @private\n * @function dragMove\n * @param {Highcharts.PointerEventObject} e\n *        The mouse move event.\n * @param {Highcharts.Point} point\n *        The point that is dragged.\n */\nfunction dragMove(e, point) {\n    var series = point.series,\n        chart = series.chart,\n        data = chart.dragDropData,\n        options = merge(series.options.dragDrop,\n        point.options.dragDrop),\n        draggableX = options.draggableX,\n        draggableY = options.draggableY,\n        origin = data.origin,\n        updateProp = data.updateProp;\n    var dX = e.chartX - origin.chartX,\n        dY = e.chartY - origin.chartY;\n    var oldDx = dX;\n    // Handle inverted\n    if (chart.inverted) {\n        dX = -dY;\n        dY = -oldDx;\n    }\n    // If we have liveRedraw enabled, update the points immediately. Otherwise\n    // update the guideBox.\n    if (pick(options.liveRedraw, true)) {\n        updatePoints(chart, false);\n        // Update drag handles\n        point.showDragHandles();\n    }\n    else {\n        // No live redraw, update guide box\n        if (updateProp) {\n            // We are resizing, so resize the guide box\n            resizeGuideBox(point, dX, dY);\n        }\n        else {\n            // We are moving, so move the guide box\n            chart.dragGuideBox.translate(draggableX ? dX : 0, draggableY ? dY : 0);\n        }\n    }\n    // Update stored previous dX/Y\n    origin.prevdX = dX;\n    origin.prevdY = dY;\n}\n/**\n * Flip a side property, used with resizeRect. If input side is \"left\", return\n * \"right\" etc.\n *\n * @private\n * @function flipResizeSide\n *\n * @param {string} side\n *        Side prop to flip. Can be `left`, `right`, `top` or `bottom`.\n *\n * @return {\"bottom\"|\"left\"|\"right\"|\"top\"|undefined}\n *         The flipped side.\n */\nfunction flipResizeSide(side) {\n    return {\n        left: 'right',\n        right: 'left',\n        top: 'bottom',\n        bottom: 'top'\n    }[side];\n}\n/**\n * Get a list of points that are grouped with this point. If only one point is\n * in the group, that point is returned by itself in an array.\n *\n * @private\n * @function getGroupedPoints\n * @param {Highcharts.Point} point\n *        Point to find group from.\n * @return {Array<Highcharts.Point>}\n *         Array of points in this group.\n */\nfunction getGroupedPoints(point) {\n    var series = point.series,\n        data = series.options.data || [],\n        groupKey = series.options.dragDrop.groupBy;\n    var points = [];\n    if (series.boosted && isArray(data)) { // #11156\n        for (var i = 0, iEnd = data.length; i < iEnd; ++i) {\n            points.push(new series.pointClass(// eslint-disable-line new-cap\n            series, data[i]));\n            points[points.length - 1].index = i;\n        }\n    }\n    else {\n        points = series.points;\n    }\n    return point.options[groupKey] ?\n        // If we have a grouping option, filter the points by that\n        points.filter(function (comparePoint) { return (comparePoint.options[groupKey] ===\n            point.options[groupKey]); }) :\n        // Otherwise return the point by itself only\n        [point];\n}\n/**\n * Calculate new point options from points being dragged.\n *\n * @private\n * @function getNewPoints\n *\n * @param {Object} dragDropData\n *        A chart's dragDropData with drag/drop origin information, and info on\n *        which points are being dragged.\n *\n * @param {Highcharts.PointerEventObject} newPos\n *        Event with the new position of the mouse (chartX/Y properties).\n *\n * @return {Highchats.Dictionary<object>}\n *         Hashmap with point.id mapped to an object with the original point\n *         reference, as well as the new data values.\n */\nfunction getNewPoints(dragDropData, newPos) {\n    var point = dragDropData.point,\n        series = point.series,\n        chart = series.chart,\n        options = merge(series.options.dragDrop,\n        point.options.dragDrop),\n        updateProps = {},\n        resizeProp = dragDropData.updateProp,\n        hashmap = {},\n        dragDropProps = point.series.dragDropProps;\n    // Go through the data props that can be updated on this series and find out\n    // which ones we want to update.\n    // eslint-disable-next-line guard-for-in\n    for (var key in dragDropProps) {\n        var val = dragDropProps[key];\n        // If we are resizing, skip if this key is not the correct one or it\n        // is not resizable.\n        if (resizeProp && (resizeProp !== key ||\n            !val.resize ||\n            val.optionName && options[val.optionName] === false)) {\n            continue;\n        }\n        // If we are resizing, we now know it is good. If we are moving, check\n        // that moving along this axis is enabled, and the prop is movable.\n        // If this prop is enabled, add it to be updated.\n        if (resizeProp || (val.move &&\n            (val.axis === 'x' && options.draggableX ||\n                val.axis === 'y' && options.draggableY))) {\n            if (chart.mapView) {\n                updateProps[key === 'x' ? 'lon' : 'lat'] = val;\n            }\n            else {\n                updateProps[key] = val;\n            }\n        }\n    }\n    // Go through the points to be updated and get new options for each of them\n    for (\n    // If resizing).forEach(only update the point we are resizing\n    var _i = 0,\n        _a = resizeProp ?\n            [point] :\n            dragDropData.groupedPoints; \n        // If resizing).forEach(only update the point we are resizing\n        _i < _a.length; \n        // If resizing).forEach(only update the point we are resizing\n        _i++) {\n            var p = _a[_i];\n        hashmap[p.id] = {\n            point: p,\n            newValues: p.getDropValues(dragDropData.origin, newPos, updateProps)\n        };\n    }\n    return hashmap;\n}\n/**\n * Get a snapshot of points, mouse position, and guide box dimensions\n *\n * @private\n * @function getPositionSnapshot\n *\n * @param {Highcharts.PointerEventObject} e\n *        Mouse event with mouse position to snapshot.\n *\n * @param {Array<Highcharts.Point>} points\n *        Points to take snapshot of. We store the value of the data properties\n *        defined in each series' dragDropProps.\n *\n * @param {Highcharts.SVGElement} [guideBox]\n *        The guide box to take snapshot of.\n *\n * @return {Object}\n *         Snapshot object. Point properties are placed in a hashmap with IDs as\n *         keys.\n */\nfunction getPositionSnapshot(e, points, guideBox) {\n    var res = {\n            chartX: e.chartX,\n            chartY: e.chartY,\n            guideBox: guideBox && {\n                x: guideBox.attr('x'),\n                y: guideBox.attr('y'),\n                width: guideBox.attr('width'),\n                height: guideBox.attr('height')\n            },\n            points: {}\n        };\n    // Loop over the points and add their props\n    for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n        var point = points_1[_i];\n        var dragDropProps = point.series.dragDropProps || {},\n            pointProps = {};\n        // Add all of the props defined in the series' dragDropProps to the\n        // snapshot\n        for (var _a = 0, _b = Object.keys(dragDropProps); _a < _b.length; _a++) {\n            var key = _b[_a];\n            var val = dragDropProps[key],\n                axis = point.series[val.axis + 'Axis'];\n            pointProps[key] = point[key];\n            // Record how far cursor was from the point when drag started.\n            // This later will be used to calculate new value according to the\n            // current position of the cursor.\n            // e.g. `high` value is translated to `highOffset`\n            if (point.series.chart.mapView && point.plotX && point.plotY) {\n                pointProps[key + 'Offset'] = key === 'x' ?\n                    point.plotX : point.plotY;\n            }\n            else {\n                pointProps[key + 'Offset'] =\n                    // E.g. yAxis.toPixels(point.high), xAxis.toPixels\n                    // (point.end)\n                    axis.toPixels(point[key]) -\n                        (axis.horiz ? e.chartX : e.chartY);\n            }\n        }\n        pointProps.point = point; // Store reference to point\n        res.points[point.id] = pointProps;\n    }\n    return res;\n}\n/**\n * In mousemove events, check that we have dragged mouse further than the\n * dragSensitivity before we call mouseMove handler.\n *\n * @private\n * @function hasDraggedPastSensitivity\n *\n * @param {Highcharts.PointerEventObject} e\n *        Mouse move event to test.\n *\n * @param {Highcharts.Chart} chart\n *        Chart that has started dragging.\n *\n * @param {number} sensitivity\n *        Pixel sensitivity to test against.\n *\n * @return {boolean}\n *         True if the event is moved past sensitivity relative to the chart's\n *         drag origin.\n */\nfunction hasDraggedPastSensitivity(e, chart, sensitivity) {\n    var orig = chart.dragDropData.origin,\n        oldX = orig.chartX,\n        oldY = orig.chartY,\n        newX = e.chartX,\n        newY = e.chartY,\n        distance = Math.sqrt((newX - oldX) * (newX - oldX) +\n            (newY - oldY) * (newY - oldY));\n    return distance > sensitivity;\n}\n/**\n * Prepare chart.dragDropData with origin info, and show the guide box.\n *\n * @private\n * @function initDragDrop\n * @param {Highcharts.PointerEventObject} e\n *        Mouse event with original mouse position.\n * @param {Highcharts.Point} point\n *        The point the dragging started on.\n * @return {void}\n */\nfunction initDragDrop(e, point) {\n    var groupedPoints = getGroupedPoints(point),\n        series = point.series,\n        chart = series.chart;\n    var guideBox;\n    // If liveRedraw is disabled, show the guide box with the default state\n    if (!pick(series.options.dragDrop && series.options.dragDrop.liveRedraw, true)) {\n        chart.dragGuideBox = guideBox = series.getGuideBox(groupedPoints);\n        chart\n            .setGuideBoxState('default', series.options.dragDrop.guideBox)\n            .add(series.group);\n    }\n    // Store some data on the chart to pick up later\n    chart.dragDropData = {\n        origin: getPositionSnapshot(e, groupedPoints, guideBox),\n        point: point,\n        groupedPoints: groupedPoints,\n        isDragging: true\n    };\n}\n/**\n * Utility function to test if a chart should have drag/drop enabled, looking at\n * its options.\n *\n * @private\n * @function isChartDraggable\n * @param {Highcharts.Chart} chart\n *        The chart to test.\n * @return {boolean}\n *         True if the chart is drag/droppable.\n */\nfunction isChartDraggable(chart) {\n    var i = chart.series ? chart.series.length : 0;\n    if ((chart.hasCartesianSeries && !chart.polar) ||\n        chart.mapView) {\n        while (i--) {\n            if (chart.series[i].options.dragDrop &&\n                isSeriesDraggable(chart.series[i])) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Utility function to test if a point is movable (any of its props can be\n * dragged by a move, not just individually).\n *\n * @private\n * @function isPointMovable\n * @param {Highcharts.Point} point\n *        The point to test.\n * @return {boolean}\n *         True if the point is movable.\n */\nfunction isPointMovable(point) {\n    var series = point.series,\n        chart = series.chart,\n        seriesDragDropOptions = series.options.dragDrop || {},\n        pointDragDropOptions = point.options && point.options.dragDrop,\n        updateProps = series.dragDropProps;\n    var p,\n        hasMovableX,\n        hasMovableY;\n    // eslint-disable-next-line guard-for-in\n    for (var key in updateProps) {\n        p = updateProps[key];\n        if (p.axis === 'x' && p.move) {\n            hasMovableX = true;\n        }\n        else if (p.axis === 'y' && p.move) {\n            hasMovableY = true;\n        }\n    }\n    // We can only move the point if draggableX/Y is set, even if all the\n    // individual prop options are set.\n    return ((seriesDragDropOptions.draggableX && hasMovableX ||\n        seriesDragDropOptions.draggableY && hasMovableY) &&\n        !(pointDragDropOptions &&\n            pointDragDropOptions.draggableX === false &&\n            pointDragDropOptions.draggableY === false) &&\n        (!!(series.yAxis && series.xAxis) ||\n            chart.mapView));\n}\n/**\n * Utility function to test if a series is using drag/drop, looking at its\n * options.\n *\n * @private\n * @function isSeriesDraggable\n * @param {Highcharts.Series} series\n *        The series to test.\n * @return {boolean}\n *         True if the series is using drag/drop.\n */\nfunction isSeriesDraggable(series) {\n    var props = ['draggableX', 'draggableY'], dragDropProps = series.dragDropProps || {};\n    var val;\n    // Add optionNames from dragDropProps to the array of props to check for\n    for (var _i = 0, _a = Object.keys(dragDropProps); _i < _a.length; _i++) {\n        var key = _a[_i];\n        val = dragDropProps[key];\n        if (val.optionName) {\n            props.push(val.optionName);\n        }\n    }\n    // Loop over all options we have that could enable dragDrop for this\n    // series. If any of them are truthy, this series is draggable.\n    var i = props.length;\n    while (i--) {\n        if (series.options.dragDrop[props[i]]) {\n            return true;\n        }\n    }\n}\n/**\n * On container mouse down. Init dragdrop if conditions are right.\n *\n * @private\n * @function mouseDown\n * @param {Highcharts.PointerEventObject} e\n *        The mouse down event.\n * @param {Highcharts.Chart} chart\n *        The chart we are clicking.\n */\nfunction mouseDown(e, chart) {\n    var dragPoint = chart.hoverPoint,\n        dragDropOptions = merge(dragPoint && dragPoint.series.options.dragDrop,\n        dragPoint && dragPoint.options.dragDrop),\n        draggableX = dragDropOptions.draggableX || false,\n        draggableY = dragDropOptions.draggableY || false;\n    // Reset cancel click\n    chart.cancelClick = false;\n    // Ignore if:\n    if (\n    // Option is disabled for the point\n    !(draggableX || draggableY) ||\n        // Zoom/pan key is pressed\n        chart.zoomOrPanKeyPressed(e) ||\n        // Dragging an annotation\n        chart.hasDraggedAnnotation) {\n        return;\n    }\n    // If we somehow get a mousedown event while we are dragging, cancel\n    if (chart.dragDropData && chart.dragDropData.isDragging) {\n        mouseUp(e, chart);\n        return;\n    }\n    // If this point is movable, start dragging it\n    if (dragPoint && isPointMovable(dragPoint)) {\n        chart.mouseIsDown = false; // Prevent zooming\n        initDragDrop(e, dragPoint);\n        dragPoint.firePointEvent('dragStart', e);\n    }\n}\n/**\n * On container mouse move. Handle drag sensitivity and fire drag event.\n *\n * @private\n * @function mouseMove\n * @param {Highcharts.PointerEventObject} e\n *        The mouse move event.\n * @param {Highcharts.Chart} chart\n *        The chart we are moving across.\n */\nfunction mouseMove(e, chart) {\n    // Ignore if zoom/pan key is pressed\n    if (chart.zoomOrPanKeyPressed(e)) {\n        return;\n    }\n    var dragDropData = chart.dragDropData;\n    var point,\n        seriesDragDropOpts,\n        newPoints,\n        numNewPoints = 0,\n        newPoint;\n    if (dragDropData && dragDropData.isDragging && dragDropData.point.series) {\n        point = dragDropData.point;\n        seriesDragDropOpts = point.series.options.dragDrop;\n        // No tooltip for dragging\n        e.preventDefault();\n        // Update sensitivity test if not passed yet\n        if (!dragDropData.draggedPastSensitivity) {\n            dragDropData.draggedPastSensitivity = hasDraggedPastSensitivity(e, chart, pick(point.options.dragDrop &&\n                point.options.dragDrop.dragSensitivity, seriesDragDropOpts &&\n                seriesDragDropOpts.dragSensitivity, DraggablePoints_DragDropDefaults.dragSensitivity));\n        }\n        // If we have dragged past dragSensitivity, run the mousemove handler\n        // for dragging\n        if (dragDropData.draggedPastSensitivity) {\n            // Find the new point values from the moving\n            dragDropData.newPoints = getNewPoints(dragDropData, e);\n            // If we are only dragging one point, add it to the event\n            newPoints = dragDropData.newPoints;\n            numNewPoints = DraggableChart_countProps(newPoints);\n            newPoint = numNewPoints === 1 ?\n                DraggableChart_getFirstProp(newPoints) :\n                null;\n            // Run the handler\n            point.firePointEvent('drag', {\n                origin: dragDropData.origin,\n                newPoints: dragDropData.newPoints,\n                newPoint: newPoint && newPoint.newValues,\n                newPointId: newPoint && newPoint.point.id,\n                numNewPoints: numNewPoints,\n                chartX: e.chartX,\n                chartY: e.chartY\n            }, function () {\n                dragMove(e, point);\n            });\n        }\n    }\n}\n/**\n * On container mouse up. Fire drop event and reset state.\n *\n * @private\n * @function mouseUp\n * @param {Highcharts.PointerEventObject} e\n *        The mouse up event.\n * @param {Highcharts.Chart} chart\n *        The chart we were dragging in.\n */\nfunction mouseUp(e, chart) {\n    var dragDropData = chart.dragDropData;\n    if (dragDropData &&\n        dragDropData.isDragging &&\n        dragDropData.draggedPastSensitivity &&\n        dragDropData.point.series) {\n        var point = dragDropData.point,\n            newPoints = dragDropData.newPoints,\n            numNewPoints = DraggableChart_countProps(newPoints),\n            newPoint = numNewPoints === 1 ?\n                DraggableChart_getFirstProp(newPoints) :\n                null;\n        // Hide the drag handles\n        if (chart.dragHandles) {\n            chart.hideDragHandles();\n        }\n        // Prevent default action\n        e.preventDefault();\n        chart.cancelClick = true;\n        // Fire the event, with a default handler that updates the points\n        point.firePointEvent('drop', {\n            origin: dragDropData.origin,\n            chartX: e.chartX,\n            chartY: e.chartY,\n            newPoints: newPoints,\n            numNewPoints: numNewPoints,\n            newPoint: newPoint && newPoint.newValues,\n            newPointId: newPoint && newPoint.point.id\n        }, function () {\n            updatePoints(chart);\n        });\n    }\n    // Reset\n    delete chart.dragDropData;\n    // Clean up the drag guide box if it exists. This is always added on\n    // drag start, even if user is overriding events.\n    if (chart.dragGuideBox) {\n        chart.dragGuideBox.destroy();\n        delete chart.dragGuideBox;\n    }\n}\n/**\n * Add event listener to Chart.render that checks whether or not we should add\n * dragdrop.\n * @private\n */\nfunction onChartRender() {\n    // If we don't have dragDrop events, see if we should add them\n    if (!this.hasAddedDragDropEvents) {\n        addDragDropEvents(this);\n    }\n}\n/**\n * Resize the guide box according to point options and a difference in mouse\n * positions. Handles reversed axes.\n *\n * @private\n * @function resizeGuideBox\n * @param {Highcharts.Point} point\n *        The point that is being resized.\n * @param {number} dX\n *        Difference in X position.\n * @param {number} dY\n *        Difference in Y position.\n */\nfunction resizeGuideBox(point, dX, dY) {\n    var series = point.series,\n        chart = series.chart,\n        dragDropData = chart.dragDropData,\n        resizeProp = series.dragDropProps[dragDropData.updateProp], \n        // `dragDropProp.resizeSide` holds info on which side to resize.\n        newPoint = dragDropData.newPoints[point.id].newValues,\n        resizeSide = typeof resizeProp.resizeSide === 'function' ?\n            resizeProp.resizeSide(newPoint,\n        point) : resizeProp.resizeSide;\n    // Call resize hook if it is defined\n    if (resizeProp.beforeResize) {\n        resizeProp.beforeResize(chart.dragGuideBox, newPoint, point);\n    }\n    // Do the resize\n    resizeRect(chart.dragGuideBox, resizeProp.axis === 'x' && series.xAxis.reversed ||\n        resizeProp.axis === 'y' && series.yAxis.reversed ?\n        flipResizeSide(resizeSide) : resizeSide, {\n        x: resizeProp.axis === 'x' ?\n            dX - (dragDropData.origin.prevdX || 0) : 0,\n        y: resizeProp.axis === 'y' ?\n            dY - (dragDropData.origin.prevdY || 0) : 0\n    });\n}\n/**\n * Resize a rect element on one side. The element is modified.\n *\n * @private\n * @function resizeRect\n * @param {Highcharts.SVGElement} rect\n *        Rect element to resize.\n * @param {string} updateSide\n *        Which side of the rect to update. Can be `left`, `right`, `top` or\n *        `bottom`.\n * @param {Highcharts.PositionObject} update\n *        Object with x and y properties, detailing how much to resize each\n *        dimension.\n * @return {void}\n */\nfunction resizeRect(rect, updateSide, update) {\n    var resizeAttrs;\n    switch (updateSide) {\n        case 'left':\n            resizeAttrs = {\n                x: rect.attr('x') + update.x,\n                width: Math.max(1, rect.attr('width') - update.x)\n            };\n            break;\n        case 'right':\n            resizeAttrs = {\n                width: Math.max(1, rect.attr('width') + update.x)\n            };\n            break;\n        case 'top':\n            resizeAttrs = {\n                y: rect.attr('y') + update.y,\n                height: Math.max(1, rect.attr('height') - update.y)\n            };\n            break;\n        case 'bottom':\n            resizeAttrs = {\n                height: Math.max(1, rect.attr('height') + update.y)\n            };\n            break;\n        default:\n    }\n    rect.attr(resizeAttrs);\n}\n/**\n * Update the points in a chart from dragDropData.newPoints.\n *\n * @private\n * @function updatePoints\n * @param {Highcharts.Chart} chart\n *        A chart with dragDropData.newPoints.\n * @param {boolean} [animation=true]\n *        Animate updating points?\n */\nfunction updatePoints(chart, animation) {\n    var newPoints = chart.dragDropData.newPoints,\n        animOptions = animObject(animation);\n    chart.isDragDropAnimating = true;\n    var newPoint;\n    // Update the points\n    for (var _i = 0, _a = Object.keys(newPoints); _i < _a.length; _i++) {\n        var key = _a[_i];\n        newPoint = newPoints[key];\n        newPoint.point.update(newPoint.newValues, false);\n    }\n    chart.redraw(animOptions);\n    // Clear the isAnimating flag after animation duration is complete.\n    // The complete handler for animation seems to have bugs at this time, so\n    // we have to use a timeout instead.\n    setTimeout(function () {\n        delete chart.isDragDropAnimating;\n        if (chart.hoverPoint && !chart.dragHandles) {\n            chart.hoverPoint.showDragHandles();\n        }\n    }, animOptions.duration);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DraggableChart = {\n    compose: compose,\n    flipResizeSide: flipResizeSide,\n    initDragDrop: initDragDrop\n};\n/* harmony default export */ var DraggablePoints_DraggableChart = (DraggableChart);\n\n;// ./code/es5/es-modules/Extensions/DraggablePoints/DragDropProps.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\nvar DragDropProps_flipResizeSide = DraggablePoints_DraggableChart.flipResizeSide;\n\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, DragDropProps_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, DragDropProps_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\n// Line series - only draggableX/Y, no drag handles\nvar line = {\n    x: {\n        axis: 'x',\n        move: true\n    },\n    y: {\n        axis: 'y',\n        move: true\n    }\n};\n// Flag series - same as line/scatter\nvar flags = line;\n// Column series - x can be moved, y can only be resized. Note extra\n// functionality for handling upside down columns (below threshold).\nvar column = {\n    x: {\n        axis: 'x',\n        move: true\n    },\n    y: {\n        axis: 'y',\n        move: false,\n        resize: true,\n        // Force guideBox start coordinates\n        beforeResize: function (guideBox, pointVals, point) {\n            // We need to ensure that guideBox always starts at threshold.\n            // We flip whether or not we update the top or bottom of the guide\n            // box at threshold, but if we drag the mouse fast, the top has not\n            // reached threshold before we cross over and update the bottom.\n            var plotThreshold = DragDropProps_pick(point.yBottom, // Added support for stacked series. (#18741)\n                point.series.translatedThreshold),\n                plotY = guideBox.attr('y'),\n                threshold = isNumber(point.stackY) ? (point.stackY - (point.y || 0)) : point.series.options.threshold || 0,\n                y = threshold + pointVals.y;\n            var height,\n                diff;\n            if (point.series.yAxis.reversed ? y < threshold : y >= threshold) {\n                // Above threshold - always set height to hit the threshold\n                height = guideBox.attr('height');\n                diff = plotThreshold ? plotThreshold - plotY - height : 0;\n                guideBox.attr({\n                    height: Math.max(0, Math.round(height + diff))\n                });\n            }\n            else {\n                // Below - always set y to start at threshold\n                guideBox.attr({\n                    y: Math.round(plotY + (plotThreshold ? plotThreshold - plotY : 0))\n                });\n            }\n        },\n        // Flip the side of the resize handle if column is below threshold.\n        // Make sure we remove the handle on the other side.\n        resizeSide: function (pointVals, point) {\n            var chart = point.series.chart,\n                dragHandles = chart.dragHandles,\n                side = pointVals.y >= (point.series.options.threshold || 0) ?\n                    'top' : 'bottom',\n                flipSide = DragDropProps_flipResizeSide(side);\n            // Force remove handle on other side\n            if (dragHandles && dragHandles[flipSide]) {\n                dragHandles[flipSide].destroy();\n                delete dragHandles[flipSide];\n            }\n            return side;\n        },\n        // Position handle at bottom if column is below threshold\n        handlePositioner: function (point) {\n            var bBox = (point.shapeArgs ||\n                    (point.graphic && point.graphic.getBBox()) ||\n                    {}),\n                reversed = point.series.yAxis.reversed,\n                threshold = point.series.options.threshold || 0,\n                y = point.y || 0,\n                bottom = (!reversed && y >= threshold) ||\n                    (reversed && y < threshold);\n            return {\n                x: bBox.x || 0,\n                y: bottom ? (bBox.y || 0) : (bBox.y || 0) + (bBox.height || 0)\n            };\n        },\n        // Horizontal handle\n        handleFormatter: function (point) {\n            var shapeArgs = point.shapeArgs || {},\n                radius = shapeArgs.r || 0, // Rounding of bar corners\n                width = shapeArgs.width || 0,\n                centerX = width / 2;\n            return [\n                // Left wick\n                ['M', radius, 0],\n                ['L', centerX - 5, 0],\n                // Circle\n                ['A', 1, 1, 0, 0, 0, centerX + 5, 0],\n                ['A', 1, 1, 0, 0, 0, centerX - 5, 0],\n                // Right wick\n                ['M', centerX + 5, 0],\n                ['L', width - radius, 0]\n            ];\n        }\n    }\n};\n// Boxplot series - move x, resize or move low/q1/q3/high\nvar boxplot = {\n    x: column.x,\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x || 0,\n            y: point.lowPlot\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.q1); }\n    },\n    /**\n     * Allow Q1 value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableQ1\n     */\n    q1: {\n        optionName: 'draggableQ1',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x || 0,\n            y: point.q1Plot\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.median && val >= point.low); }\n    },\n    median: {\n        // Median cannot be dragged individually, just move the whole\n        // point for this.\n        axis: 'y',\n        move: true\n    },\n    /**\n     * Allow Q3 value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableQ3\n     */\n    q3: {\n        optionName: 'draggableQ3',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x || 0,\n            y: point.q3Plot\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.high && val >= point.median); }\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.boxplot.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x || 0,\n            y: point.highPlot\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val >= point.q3); }\n    }\n};\n// Errorbar series - move x, resize or move low/high\nvar errorbar = {\n    x: column.x,\n    low: __assign(__assign({}, boxplot.low), { propValidate: function (val, point) { return (val <= point.high); } }),\n    high: __assign(__assign({}, boxplot.high), { propValidate: function (val, point) { return (val >= point.low); } })\n};\n/**\n * @exclude      draggableQ1, draggableQ3\n * @optionparent plotOptions.errorbar.dragDrop\n */\n// Bullet graph, x/y same as column, but also allow target to be dragged.\nvar bullet = {\n    x: column.x,\n    y: column.y,\n    /**\n     * Allow target value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.bullet.dragDrop.draggableTarget\n     */\n    target: {\n        optionName: 'draggableTarget',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: function (point) {\n            var bBox = point.targetGraphic.getBBox();\n            return {\n                x: point.barX,\n                y: bBox.y + bBox.height / 2\n            };\n        },\n        handleFormatter: column.y.handleFormatter\n    }\n};\n// OHLC series - move x, resize or move open/high/low/close\nvar ohlc = {\n    x: column.x,\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x,\n            y: point.plotLow\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.open && val <= point.close); }\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x,\n            y: point.plotHigh\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val >= point.open && val >= point.close); }\n    },\n    /**\n     * Allow open value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableOpen\n     */\n    open: {\n        optionName: 'draggableOpen',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: function (point) { return (point.open >= point.close ? 'top' : 'bottom'); },\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x,\n            y: point.plotOpen\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.high && val >= point.low); }\n    },\n    /**\n     * Allow close value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.ohlc.dragDrop.draggableClose\n     */\n    close: {\n        optionName: 'draggableClose',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: function (point) { return (point.open >= point.close ? 'bottom' : 'top'); },\n        handlePositioner: function (point) { return ({\n            x: point.shapeArgs.x,\n            y: point.plotClose\n        }); },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.high && val >= point.low); }\n    }\n};\n// Waterfall - mostly as column, but don't show drag handles for sum points\nvar waterfall = {\n    x: column.x,\n    y: DragDropProps_merge(column.y, {\n        handleFormatter: function (point) {\n            var _a,\n                _b;\n            return (point.isSum || point.isIntermediateSum ?\n                null :\n                ((_b = (_a = column === null || column === void 0 ? void 0 : column.y) === null || _a === void 0 ? void 0 : _a.handleFormatter) === null || _b === void 0 ? void 0 : _b.call(_a, point)) || null);\n        }\n    })\n};\n// Columnrange series - move x, resize or move low/high\nvar columnrange = {\n    x: {\n        axis: 'x',\n        move: true\n    },\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.columnrange.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: function (point) {\n            var bBox = (point.shapeArgs || point.graphic.getBBox());\n            return {\n                x: bBox.x || 0,\n                y: (bBox.y || 0) + (bBox.height || 0)\n            };\n        },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val <= point.high); }\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.columnrange.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: function (point) {\n            var bBox = (point.shapeArgs || point.graphic.getBBox());\n            return {\n                x: bBox.x || 0,\n                y: bBox.y || 0\n            };\n        },\n        handleFormatter: column.y.handleFormatter,\n        propValidate: function (val, point) { return (val >= point.low); }\n    }\n};\n// Arearange series - move x, resize or move low/high\nvar arearange = {\n    x: columnrange.x,\n    /**\n     * Allow low value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.arearange.dragDrop.draggableLow\n     */\n    low: {\n        optionName: 'draggableLow',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'bottom',\n        handlePositioner: function (point) {\n            var bBox = (point.graphics &&\n                    point.graphics[0] &&\n                    point.graphics[0].getBBox());\n            return bBox ? {\n                x: bBox.x + bBox.width / 2,\n                y: bBox.y + bBox.height / 2\n            } : { x: -999, y: -999 };\n        },\n        handleFormatter: arearangeHandleFormatter,\n        propValidate: columnrange.low.propValidate\n    },\n    /**\n     * Allow high value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.arearange.dragDrop.draggableHigh\n     */\n    high: {\n        optionName: 'draggableHigh',\n        axis: 'y',\n        move: true,\n        resize: true,\n        resizeSide: 'top',\n        handlePositioner: function (point) {\n            var bBox = (point.graphics &&\n                    point.graphics[1] &&\n                    point.graphics[1].getBBox());\n            return bBox ? {\n                x: bBox.x + bBox.width / 2,\n                y: bBox.y + bBox.height / 2\n            } : { x: -999, y: -999 };\n        },\n        handleFormatter: arearangeHandleFormatter,\n        propValidate: columnrange.high.propValidate\n    }\n};\n// Xrange - resize/move x/x2, and move y\nvar xrange = {\n    y: {\n        axis: 'y',\n        move: true\n    },\n    /**\n     * Allow x value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.xrange.dragDrop.draggableX1\n     */\n    x: {\n        optionName: 'draggableX1',\n        axis: 'x',\n        move: true,\n        resize: true,\n        resizeSide: 'left',\n        handlePositioner: function (point) { return (xrangeHandlePositioner(point, 'x')); },\n        handleFormatter: horizHandleFormatter,\n        propValidate: function (val, point) { return (val <= point.x2); }\n    },\n    /**\n     * Allow x2 value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.xrange.dragDrop.draggableX2\n     */\n    x2: {\n        optionName: 'draggableX2',\n        axis: 'x',\n        move: true,\n        resize: true,\n        resizeSide: 'right',\n        handlePositioner: function (point) { return (xrangeHandlePositioner(point, 'x2')); },\n        handleFormatter: horizHandleFormatter,\n        propValidate: function (val, point) { return (val >= point.x); }\n    }\n};\n// Gantt - same as xrange, but with aliases\nvar gantt = {\n    y: xrange.y,\n    /**\n     * Allow start value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.gantt.dragDrop.draggableStart\n     */\n    start: DragDropProps_merge(xrange.x, {\n        optionName: 'draggableStart',\n        // Do not allow individual drag handles for milestones\n        validateIndividualDrag: function (point) { return (!point.milestone); }\n    }),\n    /**\n     * Allow end value to be dragged individually.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @requires  modules/draggable-points\n     * @apioption plotOptions.gantt.dragDrop.draggableEnd\n     */\n    end: DragDropProps_merge(xrange.x2, {\n        optionName: 'draggableEnd',\n        // Do not allow individual drag handles for milestones\n        validateIndividualDrag: function (point) { return (!point.milestone); }\n    })\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Use a circle covering the marker as drag handle.\n * @private\n */\nfunction arearangeHandleFormatter(point) {\n    var radius = point.graphic ?\n            point.graphic.getBBox().width / 2 + 1 :\n            4;\n    return [\n        ['M', 0 - radius, 0],\n        ['a', radius, radius, 0, 1, 0, radius * 2, 0],\n        ['a', radius, radius, 0, 1, 0, radius * -2, 0]\n    ];\n}\n/**\n * 90deg rotated column handle path, used in multiple series types.\n * @private\n */\nfunction horizHandleFormatter(point) {\n    var shapeArgs = point.shapeArgs || point.graphic.getBBox(),\n        top = shapeArgs.r || 0, // Rounding of bar corners\n        bottom = shapeArgs.height - top,\n        centerY = shapeArgs.height / 2;\n    return [\n        // Top wick\n        ['M', 0, top],\n        ['L', 0, centerY - 5],\n        // Circle\n        ['A', 1, 1, 0, 0, 0, 0, centerY + 5],\n        ['A', 1, 1, 0, 0, 0, 0, centerY - 5],\n        // Bottom wick\n        ['M', 0, centerY + 5],\n        ['L', 0, bottom]\n    ];\n}\n/**\n * Handle positioner logic is the same for x and x2 apart from the x value.\n * shapeArgs does not take yAxis reversed etc into account, so we use\n * axis.toPixels to handle positioning.\n * @private\n */\nfunction xrangeHandlePositioner(point, xProp) {\n    var series = point.series,\n        xAxis = series.xAxis,\n        yAxis = series.yAxis,\n        inverted = series.chart.inverted,\n        offsetY = series.columnMetrics ? series.columnMetrics.offset :\n            -point.shapeArgs.height / 2;\n    // Using toPixels handles axis.reversed, but doesn't take\n    // chart.inverted into account.\n    var newX = xAxis.toPixels(point[xProp],\n        true),\n        newY = yAxis.toPixels(point.y,\n        true);\n    // Handle chart inverted\n    if (inverted) {\n        newX = xAxis.len - newX;\n        newY = yAxis.len - newY;\n    }\n    newY += offsetY; // (#12872)\n    return {\n        x: Math.round(newX),\n        y: Math.round(newY)\n    };\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DragDropProps = {\n    arearange: arearange,\n    boxplot: boxplot,\n    bullet: bullet,\n    column: column,\n    columnrange: columnrange,\n    errorbar: errorbar,\n    flags: flags,\n    gantt: gantt,\n    line: line,\n    ohlc: ohlc,\n    waterfall: waterfall,\n    xrange: xrange\n};\n/* harmony default export */ var DraggablePoints_DragDropProps = (DragDropProps);\n\n;// ./code/es5/es-modules/Extensions/DraggablePoints/DraggablePoints.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar DraggablePoints_addEvents = DraggablePoints_DragDropUtilities.addEvents, DraggablePoints_getNormalizedEvent = DraggablePoints_DragDropUtilities.getNormalizedEvent;\n\nvar DraggablePoints_initDragDrop = DraggablePoints_DraggableChart.initDragDrop;\n\n\n\nvar DraggablePoints_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, DraggablePoints_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, DraggablePoints_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Functions\n *\n * */\n/* @todo\nAdd drag/drop support to specific data props for different series types.\n\nThe dragDrop.draggableX/Y user options on series enable/disable all of these per\ndirection unless they are specifically set in options using\ndragDrop.{optionName}. If the prop does not specify an optionName here, it can\nonly be enabled/disabled by the user with draggableX/Y.\n\nSupported options for each prop:\n    optionName: User option in series.dragDrop that enables/disables\n        dragging this prop.\n    axis: Can be 'x' or 'y'. Whether this prop is linked to x or y axis.\n    move: Whether or not this prop should be updated when moving points.\n    resize: Whether or not to draw a drag handle and allow user to drag and\n        update this prop by itself.\n    beforeResize: Hook to perform tasks before a resize is made. Gets\n        the guide box, the new points values, and the point as args.\n    resizeSide: Which side of the guide box to resize when dragging the\n        handle. Can be \"left\", \"right\", \"top\", \"bottom\". Chart.inverted is\n        handled automatically. Can also be a function, taking the new point\n        values as parameter, as well as the point, and returning a string\n        with the side.\n    propValidate: Function that takes the prop value and the point as\n        arguments, and returns true if the prop value is valid, false if\n        not. It is used to prevent e.g. resizing \"low\" above \"high\".\n    handlePositioner: For resizeable props, return 0,0 in SVG plot coords of\n        where to place the dragHandle. Gets point as argument. Should return\n        object with x and y properties.\n    handleFormatter: For resizeable props, return the path of the drag\n        handle as an SVG path array. Gets the point as argument. The handle\n        is translated according to handlePositioner.\n    handleOptions: Options to merge with the default handle options.\n\n    TODO:\n    - It makes sense to have support for resizing the size of bubbles and\n        e.g variwide columns. This requires us to support dragging along a\n        z-axis, somehow computing a relative value from old to new pixel\n        size.\n    - Moving maps could be useful, although we would have to compute new\n        point.path values in order to do it properly (using SVG translate\n        is easier, but won't update the data).\n*/\n/** @private */\nfunction DraggablePoints_compose(ChartClass, SeriesClass) {\n    DraggablePoints_DraggableChart.compose(ChartClass);\n    var seriesProto = SeriesClass.prototype;\n    if (!seriesProto.dragDropProps) {\n        var PointClass = SeriesClass.prototype.pointClass,\n            seriesTypes = SeriesClass.types,\n            pointProto = PointClass.prototype;\n        pointProto.getDropValues = pointGetDropValues;\n        pointProto.showDragHandles = pointShowDragHandles;\n        DraggablePoints_addEvent(PointClass, 'mouseOut', onPointMouseOut);\n        DraggablePoints_addEvent(PointClass, 'mouseOver', onPointMouseOver);\n        DraggablePoints_addEvent(PointClass, 'remove', onPointRemove);\n        seriesProto.dragDropProps = DraggablePoints_DragDropProps.line;\n        seriesProto.getGuideBox = seriesGetGuideBox;\n        // Custom props for certain series types\n        var seriesWithDragDropProps = [\n                'arearange',\n                'boxplot',\n                'bullet',\n                'column',\n                'columnrange',\n                'errorbar',\n                'flags',\n                'gantt',\n                'ohlc',\n                'waterfall',\n                'xrange'\n            ];\n        for (var _i = 0, seriesWithDragDropProps_1 = seriesWithDragDropProps; _i < seriesWithDragDropProps_1.length; _i++) {\n            var seriesType = seriesWithDragDropProps_1[_i];\n            if (seriesTypes[seriesType]) {\n                seriesTypes[seriesType].prototype.dragDropProps =\n                    DraggablePoints_DragDropProps[seriesType];\n            }\n        }\n        // Don't support certain series types\n        var seriesWithoutDragDropProps = [\n                'bellcurve',\n                'gauge',\n                'histogram',\n                'map',\n                'mapline',\n                'pareto',\n                'pie',\n                'sankey',\n                'sma',\n                'sunburst',\n                'treemap',\n                'vector',\n                'windbarb',\n                'wordcloud'\n            ];\n        for (var _a = 0, seriesWithoutDragDropProps_1 = seriesWithoutDragDropProps; _a < seriesWithoutDragDropProps_1.length; _a++) {\n            var seriesType = seriesWithoutDragDropProps_1[_a];\n            if (seriesTypes[seriesType]) {\n                seriesTypes[seriesType].prototype.dragDropProps = null;\n            }\n        }\n    }\n}\n/**\n * On point mouse out. Hide drag handles, depending on state.\n *\n * @private\n * @function mouseOut\n * @param {Highcharts.Point} point\n *        The point mousing out of.\n */\nfunction mouseOut(point) {\n    var chart = point.series && point.series.chart,\n        dragDropData = chart && chart.dragDropData;\n    if (chart &&\n        chart.dragHandles &&\n        !(dragDropData &&\n            (dragDropData.isDragging &&\n                dragDropData.draggedPastSensitivity ||\n                dragDropData.isHoveringHandle === point.id))) {\n        chart.hideDragHandles();\n    }\n}\n/**\n * Mouseover on a point. Show drag handles if the conditions are right.\n *\n * @private\n * @function mouseOver\n * @param {Highcharts.Point} point\n *        The point mousing over.\n */\nfunction mouseOver(point) {\n    var series = point.series,\n        chart = series && series.chart,\n        dragDropData = chart && chart.dragDropData,\n        is3d = chart && chart.is3d && chart.is3d();\n    if (chart &&\n        !(dragDropData &&\n            dragDropData.isDragging && // Ignore if dragging a point\n            dragDropData.draggedPastSensitivity) &&\n        !chart.isDragDropAnimating && // Ignore if animating\n        series.options.dragDrop && // No need to compute handles without this\n        !is3d // No 3D support\n    ) {\n        // Hide the handles if they exist on another point already\n        if (chart.dragHandles) {\n            chart.hideDragHandles();\n        }\n        point.showDragHandles();\n    }\n}\n/**\n * Point mouseleave event. See above function for explanation of the timeout.\n * @private\n */\nfunction onPointMouseOut() {\n    var point = this;\n    setTimeout(function () {\n        if (point.series) {\n            mouseOut(point);\n        }\n    }, 10);\n}\n/**\n * Point hover event. We use a short timeout due to issues with coordinating\n * point mouseover/out events on dragHandles and points.\n *\n * Particularly arearange series are finicky since the markers are not\n * individual points. This logic should preferably be improved in the future.\n *\n * Notice that the mouseOut event below must have a shorter timeout to ensure\n * event order.\n */\nfunction onPointMouseOver() {\n    var point = this;\n    setTimeout(function () { return mouseOver(point); }, 12);\n}\n/**\n * Hide drag handles on a point if it is removed.\n * @private\n */\nfunction onPointRemove() {\n    var chart = this.series.chart,\n        dragHandles = chart.dragHandles;\n    if (dragHandles && dragHandles.point === this.id) {\n        chart.hideDragHandles();\n    }\n}\n/**\n * Mouseout on resize handle. Handle states, and possibly run mouseOut on point.\n *\n * @private\n * @function onResizeHandleMouseOut\n * @param {Highcharts.Point} point\n *        The point mousing out of.\n */\nfunction onResizeHandleMouseOut(point) {\n    var chart = point.series.chart;\n    if (chart.dragDropData &&\n        point.id === chart.dragDropData.isHoveringHandle) {\n        delete chart.dragDropData.isHoveringHandle;\n    }\n    if (!chart.hoverPoint) {\n        mouseOut(point);\n    }\n}\n/**\n * Mousedown on resize handle. Init a drag if the conditions are right.\n *\n * @private\n * @function onResizeHandleMouseDown\n * @param {Highcharts.PointerEventObject} e\n *        The mousedown event.\n * @param {Highcharts.Point} point\n *        The point mousing down on.\n * @param {string} updateProp\n *        The data property this resize handle is attached to for this point.\n */\nfunction onResizeHandleMouseDown(e, point, updateProp) {\n    var chart = point.series.chart;\n    // Ignore if zoom/pan key is pressed\n    if (chart.zoomOrPanKeyPressed(e)) {\n        return;\n    }\n    // Prevent zooming\n    chart.mouseIsDown = false;\n    // We started a drag\n    DraggablePoints_initDragDrop(e, point);\n    chart.dragDropData.updateProp =\n        e.updateProp = updateProp;\n    point.firePointEvent('dragStart', e);\n    // Prevent default to avoid point click for dragging too\n    e.stopPropagation();\n    e.preventDefault();\n}\n/**\n * Get updated point values when dragging a point.\n *\n * @private\n * @function Highcharts.Point#getDropValues\n *\n * @param {Object} origin\n *        Mouse position (chartX/Y) and point props at current data values.\n *        Point props should be organized per point.id in a hashmap.\n *\n * @param {Highcharts.PointerEventObject} newPos\n *        New mouse position (chartX/Y).\n *\n * @param {Highcharts.Dictionary<Highcharts.Dictionary<Highcharts.Dictionary<string>>>} updateProps\n *        Point props to modify. Map of prop objects where each key refers to\n *        the prop, and the value is an object with an axis property. Example:\n *        {\n *            x: {\n *                axis: 'x'\n *            },\n *            x2: {\n *                axis: 'x'\n *            }\n *        }\n *\n * @return {Highcharts.Dictionary<number>}\n *         An object with updated data values.\n */\nfunction pointGetDropValues(origin, newPos, updateProps) {\n    var point = this,\n        series = point.series,\n        chart = series.chart,\n        mapView = chart.mapView,\n        options = DraggablePoints_merge(series.options.dragDrop,\n        point.options.dragDrop),\n        result = {},\n        pointOrigin = origin.points[point.id],\n        updateSingleProp = Object.keys(updateProps).length === 1;\n    /**\n     * Utility function to apply precision and limit a value within the\n     * draggable range.\n     * @private\n     * @param {number} val\n     *        Value to limit\n     * @param {string} direction\n     *        Axis direction\n     * @return {number}\n     *         Limited value\n     */\n    var limitToRange = function (val,\n        dir) {\n            var _a,\n        _b,\n        _c;\n        var direction = dir.toUpperCase(), time = series.chart.time, defaultPrecision = series[\"\" + dir + \"Axis\"].categories ? 1 : 0, precision = (_a = options[\"dragPrecision\".concat(direction)]) !== null && _a !== void 0 ? _a : defaultPrecision, min = (_b = time.parse(options[\"dragMin\".concat(direction)])) !== null && _b !== void 0 ? _b : -Infinity, max = (_c = time.parse(options[\"dragMax\".concat(direction)])) !== null && _c !== void 0 ? _c : Infinity;\n        var res = val;\n        if (precision) {\n            res = Math.round(res / precision) * precision;\n        }\n        return clamp(res, min, max);\n    };\n    /**\n     * Utility function to apply precision and limit a value within the\n     * draggable range used only for Highcharts Maps.\n     * @private\n     * @param {PointerEvent} newPos\n     *        PointerEvent, which is used to get the value\n     * @param {string} direction\n     *        Axis direction\n     * @param {string} key\n     *        Key for choosing between longitude and latitude\n     * @return {number | undefined}\n     *         Limited value\n     */\n    var limitToMapRange = function (newPos,\n        dir,\n        key) {\n            var _a,\n        _b,\n        _c,\n        _d,\n        _e;\n        if (mapView) {\n            var direction = dir.toUpperCase(),\n                precision = (_a = options[\"dragPrecision\".concat(direction)]) !== null && _a !== void 0 ? _a : 0,\n                lonLatMin = mapView.pixelsToLonLat({\n                    x: 0,\n                    y: 0\n                }),\n                lonLatMax = mapView.pixelsToLonLat({\n                    x: chart.plotBox.width,\n                    y: chart.plotBox.height\n                });\n            var min = (_c = (_b = options[\"dragMin\".concat(direction)]) !== null && _b !== void 0 ? _b : lonLatMin === null || lonLatMin === void 0 ? void 0 : lonLatMin[key]) !== null && _c !== void 0 ? _c : -Infinity, max = (_e = (_d = options[\"dragMax\".concat(direction)]) !== null && _d !== void 0 ? _d : lonLatMax === null || lonLatMax === void 0 ? void 0 : lonLatMax[key]) !== null && _e !== void 0 ? _e : Infinity, res = newPos[key];\n            if (mapView.projection.options.name === 'Orthographic') {\n                return res;\n            }\n            if (key === 'lat') {\n                // If map is bigger than possible projection range\n                if (isNaN(min) || min > mapView.projection.maxLatitude) {\n                    min = mapView.projection.maxLatitude;\n                }\n                if (isNaN(max) || max < -1 * mapView.projection.maxLatitude) {\n                    max = -1 * mapView.projection.maxLatitude;\n                }\n                // Swap for latitude\n                var temp = max;\n                max = min;\n                min = temp;\n            }\n            if (!mapView.projection.hasCoordinates) {\n                // Establish y value\n                var lonLatRes = mapView.pixelsToLonLat({\n                        x: newPos.chartX - chart.plotLeft,\n                        y: chart.plotHeight - newPos.chartY + chart.plotTop\n                    });\n                if (lonLatRes) {\n                    res = lonLatRes[key];\n                }\n            }\n            if (precision) {\n                res = Math.round(res / precision) * precision;\n            }\n            return clamp(res, min, max);\n        }\n    };\n    // Assign new value to property. Adds dX/YValue to the old value, limiting\n    // it within min/max ranges.\n    for (var _i = 0, _a = Object.keys(updateProps); _i < _a.length; _i++) {\n        var key = _a[_i];\n        var val = updateProps[key],\n            oldVal = pointOrigin.point[key],\n            axis = series[val.axis + 'Axis'],\n            newVal = mapView ?\n                limitToMapRange(newPos,\n            val.axis,\n            key) :\n                limitToRange(axis.toValue((axis.horiz ? newPos.chartX : newPos.chartY) +\n                    pointOrigin[key + 'Offset']),\n            val.axis);\n        // If we are updating a single prop, and it has a validation function\n        // for the prop, run it. If it fails, don't update the value.\n        if (DraggablePoints_isNumber(newVal) &&\n            !(updateSingleProp &&\n                val.propValidate &&\n                !val.propValidate(newVal, point)) &&\n            typeof oldVal !== 'undefined') {\n            result[key] = newVal;\n        }\n    }\n    return result;\n}\n/**\n * Render drag handles on a point - depending on which handles are enabled - and\n * attach events to them.\n *\n * @private\n * @function Highcharts.Point#showDragHandles\n */\nfunction pointShowDragHandles() {\n    var point = this,\n        series = point.series,\n        chart = series.chart,\n        inverted = chart.inverted,\n        renderer = chart.renderer,\n        options = DraggablePoints_merge(series.options.dragDrop,\n        point.options.dragDrop),\n        dragDropProps = series.dragDropProps || {};\n    var dragHandles = chart.dragHandles;\n    var _loop_1 = function (key) {\n            var val = dragDropProps[key],\n        handleOptions = DraggablePoints_merge(DraggablePoints_DragDropDefaults.dragHandle,\n        val.handleOptions,\n        options.dragHandle),\n        handleAttrs = {\n                'class': handleOptions.className,\n                'stroke-width': handleOptions.lineWidth,\n                fill: handleOptions.color,\n                stroke: handleOptions.lineColor\n            },\n        pathFormatter = handleOptions.pathFormatter || val.handleFormatter,\n        handlePositioner = val.handlePositioner, \n            // Run validation function on whether or not we allow individual\n            // updating of this prop.\n            validate = val.validateIndividualDrag ?\n                val.validateIndividualDrag(point) : true;\n        var pos = void 0,\n            handle = void 0,\n            path = void 0;\n        if (val.resize &&\n            validate &&\n            val.resizeSide &&\n            pathFormatter &&\n            (options['draggable' + val.axis.toUpperCase()] ||\n                options[val.optionName]) &&\n            options[val.optionName] !== false) {\n            // Create handle if it doesn't exist\n            if (!dragHandles) {\n                dragHandles = chart.dragHandles = {\n                    group: renderer\n                        .g('drag-drop-handles')\n                        .add(series.markerGroup || series.group),\n                    point: point.id\n                };\n                // Store which point this is\n            }\n            else {\n                dragHandles.point = point.id;\n            }\n            // Find position and path of handle\n            pos = handlePositioner(point);\n            handleAttrs.d = path = pathFormatter(point);\n            // Correct left edge value depending on the xAxis' type, #16596\n            var minEdge = point.series.xAxis.categories ? -0.5 : 0;\n            if (!path || pos.x < minEdge || pos.y < 0) {\n                return { value: void 0 };\n            }\n            // If cursor is not set explicitly, use axis direction\n            handleAttrs.cursor = handleOptions.cursor ||\n                ((val.axis === 'x') !== !!inverted ?\n                    'ew-resize' : 'ns-resize');\n            // Create and add the handle element if it doesn't exist\n            handle = dragHandles[val.optionName];\n            if (!handle) {\n                handle = dragHandles[val.optionName] = renderer\n                    .path()\n                    .add(dragHandles.group);\n            }\n            // Move and update handle\n            handleAttrs.translateX = inverted ?\n                series.yAxis.len - pos.y :\n                pos.x;\n            handleAttrs.translateY = inverted ?\n                series.xAxis.len - pos.x :\n                pos.y;\n            if (inverted) {\n                handleAttrs.rotation = -90;\n            }\n            handle.attr(handleAttrs);\n            // Add events\n            DraggablePoints_addEvents(handle.element, ['touchstart', 'mousedown'], function (e) {\n                onResizeHandleMouseDown(DraggablePoints_getNormalizedEvent(e, chart), point, key);\n            }, {\n                passive: false\n            });\n            DraggablePoints_addEvent(dragHandles.group.element, 'mouseover', function () {\n                chart.dragDropData = chart.dragDropData || {};\n                chart.dragDropData.isHoveringHandle = point.id;\n            });\n            DraggablePoints_addEvents(dragHandles.group.element, ['touchend', 'mouseout'], function () {\n                onResizeHandleMouseOut(point);\n            });\n        }\n    };\n    // Go through each updateProp and see if we are supposed to create a handle\n    // for it.\n    for (var _i = 0, _a = Object.keys(dragDropProps); _i < _a.length; _i++) {\n        var key = _a[_i];\n        var state_1 = _loop_1(key);\n        if (typeof state_1 === \"object\")\n            return state_1.value;\n    }\n}\n/**\n * Returns an SVGElement to use as the guide box for a set of points.\n *\n * @private\n * @function Highcharts.Series#getGuideBox\n *\n * @param {Array<Highcharts.Point>} points\n *        The state to set the guide box to.\n *\n * @return {Highcharts.SVGElement}\n *         An SVG element for the guide box, not added to DOM.\n */\nfunction seriesGetGuideBox(points) {\n    var chart = this.chart;\n    var minX = Infinity,\n        maxX = -Infinity,\n        minY = Infinity,\n        maxY = -Infinity,\n        changed;\n    // Find bounding box of all points\n    for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n        var point = points_1[_i];\n        var bBox = (point.graphic && point.graphic.getBBox() || point.shapeArgs);\n        if (bBox) {\n            var plotX2 = void 0;\n            var x2 = point.x2;\n            if (DraggablePoints_isNumber(x2)) {\n                plotX2 = point.series.xAxis.translate(x2, false, false, false, true);\n            }\n            // Avoid a 0 min when some of the points being dragged are\n            // completely outside the plot\n            var skipBBox = !(bBox.width || bBox.height || bBox.x || bBox.y);\n            changed = true;\n            minX = Math.min(point.plotX || 0, plotX2 || 0, skipBBox ? Infinity : bBox.x || 0, minX);\n            maxX = Math.max(point.plotX || 0, plotX2 || 0, (bBox.x || 0) + (bBox.width || 0), maxX);\n            minY = Math.min(point.plotY || 0, skipBBox ? Infinity : bBox.y || 0, minY);\n            maxY = Math.max((bBox.y || 0) + (bBox.height || 0), maxY);\n        }\n    }\n    return changed ? chart.renderer.rect(minX, minY, maxX - minX, maxY - minY) : chart.renderer.g();\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DraggablePoints = {\n    compose: DraggablePoints_compose\n};\n/* harmony default export */ var DraggablePoints_DraggablePoints = (DraggablePoints);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Current drag and drop position.\n *\n * @interface Highcharts.DragDropPositionObject\n */ /**\n* Chart x position\n* @name Highcharts.DragDropPositionObject#chartX\n* @type {number}\n*/ /**\n* Chart y position\n* @name Highcharts.DragDropPositionObject#chartY\n* @type {number}\n*/ /**\n* Drag and drop guide box.\n* @name Highcharts.DragDropPositionObject#guideBox\n* @type {Highcharts.BBoxObject|undefined}\n*/ /**\n* Updated point data.\n* @name Highcharts.DragDropPositionObject#points\n* @type {Highcharts.Dictionary<Highcharts.Dictionary<number>>}\n*/ /**\n* Delta of previous x position.\n* @name Highcharts.DragDropPositionObject#prevdX\n* @type {number|undefined}\n*/ /**\n* Delta of previous y position.\n* @name Highcharts.DragDropPositionObject#prevdY\n* @type {number|undefined}\n*/\n/**\n * Function callback to execute while series points are dragged. Return false to\n * stop the default drag action.\n *\n * @callback Highcharts.PointDragCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Point where the event occurred.\n *\n * @param {Highcharts.PointDragEventObject} event\n *        Event arguments.\n */\n/**\n * Contains information about a points new values.\n *\n * @interface Highcharts.PointDragDropObject\n */ /**\n* New values.\n* @name Highcharts.PointDragDropObject#newValues\n* @type {Highcharts.Dictionary<number>}\n*/ /**\n* Updated point.\n* @name Highcharts.PointDragDropObject#point\n* @type {Highcharts.Point}\n*/\n/**\n * Contains common information for a drag event on series points.\n *\n * @interface Highcharts.PointDragEventObject\n */ /**\n* New point after drag if only a single one.\n* @name Highcharts.PointDropEventObject#newPoint\n* @type {Highcharts.PointDragDropObject|undefined}\n*/ /**\n* New point id after drag if only a single one.\n* @name Highcharts.PointDropEventObject#newPointId\n* @type {string|undefined}\n*/ /**\n* New points during drag.\n* @name Highcharts.PointDragEventObject#newPoints\n* @type {Highcharts.Dictionary<Highcharts.PointDragDropObject>}\n*/ /**\n* Original data.\n* @name Highcharts.PointDragEventObject#origin\n* @type {Highcharts.DragDropPositionObject}\n*/ /**\n* Prevent default drag action.\n* @name Highcharts.PointDragEventObject#preventDefault\n* @type {Function}\n*/ /**\n* Target point that caused the event.\n* @name Highcharts.PointDragEventObject#target\n* @type {Highcharts.Point}\n*/ /**\n* Event type.\n* @name Highcharts.PointDragEventObject#type\n* @type {\"drag\"}\n*/\n/**\n * Function callback to execute when a series point is dragged.\n *\n * @callback Highcharts.PointDragStartCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Point where the event occurred.\n *\n * @param {Highcharts.PointDragStartEventObject} event\n *        Event arguments.\n */\n/**\n * Contains common information for a drag event on series point.\n *\n * @interface Highcharts.PointDragStartEventObject\n * @extends global.MouseEvent\n */ /**\n* Data property being dragged.\n* @name Highcharts.PointDragStartEventObject#updateProp\n* @type {string|undefined}\n*/\n/**\n * Function callback to execute when series points are dropped.\n *\n * @callback Highcharts.PointDropCallbackFunction\n *\n * @param {Highcharts.Point} this\n *        Point where the event occurred.\n *\n * @param {Highcharts.PointDropEventObject} event\n *        Event arguments.\n */\n/**\n * Contains common information for a drop event on series points.\n *\n * @interface Highcharts.PointDropEventObject\n */ /**\n* New point after drop if only a single one.\n* @name Highcharts.PointDropEventObject#newPoint\n* @type {Highcharts.PointDragDropObject|undefined}\n*/ /**\n* New point id after drop if only a single one.\n* @name Highcharts.PointDropEventObject#newPointId\n* @type {string|undefined}\n*/ /**\n* New points after drop.\n* @name Highcharts.PointDropEventObject#newPoints\n* @type {Highcharts.Dictionary<Highcharts.PointDragDropObject>}\n*/ /**\n* Number of new points.\n* @name Highcharts.PointDropEventObject#numNewPoints\n* @type {number}\n*/ /**\n* Original data.\n* @name Highcharts.PointDropEventObject#origin\n* @type {Highcharts.DragDropPositionObject}\n*/ /**\n* Prevent default drop action.\n* @name Highcharts.PointDropEventObject#preventDefault\n* @type {Function}\n*/ /**\n* Target point that caused the event.\n* @name Highcharts.PointDropEventObject#target\n* @type {Highcharts.Point}\n*/ /**\n* Event type.\n* @name Highcharts.PointDropEventObject#type\n* @type {\"drop\"}\n*/\n''; // Detaches doclets above\n\n;// ./code/es5/es-modules/masters/modules/draggable-points.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nDraggablePoints_DraggablePoints.compose(G.Chart, G.Series);\n/* harmony default export */ var draggable_points_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "draggable_points_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "el", "types", "fn", "options", "removeFuncs", "map", "type", "_i", "removeFuncs_1", "length", "fn_1", "e", "chart", "_a", "chartX", "chartY", "pointer", "normalize", "DraggablePoints_DragDropDefaults", "dragSensitivity", "dragHandle", "className", "color", "lineColor", "lineWidth", "zIndex", "guideBox", "cursor", "animObject", "DraggableChart_countProps", "keys", "DraggableChart_getFirstProp", "p", "doc", "DraggableChart_addEvent", "isArray", "merge", "pick", "chartHideDragHandles", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "chartSetGuideBoxState", "state", "dragGuideBox", "guideBoxOptions", "stateOptions", "attr", "stroke", "strokeWidth", "fill", "css", "pointerEvents", "chartZoomOrPanKeyPressed", "chartOptions", "panKey", "zooming", "flipResizeSide", "side", "left", "right", "top", "bottom", "initDragDrop", "point", "groupedPoints", "getGroupedPoints", "series", "data", "groupKey", "dragDrop", "groupBy", "points", "boosted", "i", "iEnd", "push", "pointClass", "index", "filter", "comparePoint", "liveRedraw", "getGuideBox", "setGuideBoxState", "add", "group", "dragDropData", "origin", "getPositionSnapshot", "res", "x", "y", "width", "height", "points_1", "dragDropProps", "pointProps", "_b", "val", "axis", "mapView", "plotX", "plotY", "toPixels", "horiz", "id", "isDragging", "mouseUp", "draggedPastSensitivity", "newPoints", "numNewPoints", "newPoint", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preventDefault", "cancelClick", "firePointEvent", "newValues", "newPointId", "updatePoints", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasAddedDragDropEvents", "container", "isChartDraggable", "hasCartesianSeries", "polar", "isSeriesDraggable", "props", "optionName", "DraggableChart_addEvents", "mouseDown", "dragPoint", "hoverPoint", "dragDropOptions", "draggableX", "draggableY", "zoomOrPanKeyPressed", "hasDraggedAnnotation", "isPointMovable", "hasMovableX", "hasMovableY", "seriesDragDropOptions", "pointDragDropOptions", "updateProps", "move", "yAxis", "xAxis", "mouseIsDown", "DraggableChart_getNormalizedEvent", "mouseMove", "sensitivity", "orig", "oldX", "oldY", "newX", "newY", "seriesDragDropOpts", "distance", "Math", "sqrt", "getNewPoints", "newPos", "resizeProp", "updateProp", "<PERSON><PERSON><PERSON>", "resize", "getDropValues", "dX", "dY", "oldDx", "resizeSide", "inverted", "showDragHandles", "beforeResize", "resizeRect", "rect", "updateSide", "update", "resizeAttrs", "max", "reversed", "prevdX", "prevdY", "translate", "passive", "unbindDragDropMouseUp", "animation", "animOptions", "isDragDropAnimating", "redraw", "setTimeout", "duration", "ChartClass", "chartProto", "__assign", "assign", "t", "s", "arguments", "apply", "isNumber", "DragDropProps_merge", "DragDropProps_pick", "line", "column", "pointVals", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yBottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "stackY", "round", "flipSide", "DragDropProps_flipResizeSide", "handlePositioner", "bBox", "shapeArgs", "graphic", "getBBox", "handleFormatter", "radius", "r", "centerX", "boxplot", "low", "lowPlot", "propValidate", "q1", "q1Plot", "median", "q3", "q3Plot", "high", "highPlot", "errorbar", "bullet", "target", "targetGraphic", "barX", "ohlc", "plotLow", "open", "close", "plotHigh", "plotOpen", "plotClose", "waterfall", "isSum", "isIntermediateSum", "columnrange", "arearange", "graphics", "arearangeHandleFormatter", "xrange", "xrangeHandlePositioner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "x2", "centerY", "xProp", "offsetY", "columnMetrics", "offset", "len", "DraggablePoints_DragDropProps", "flags", "gantt", "start", "validateIndividualDrag", "milestone", "end", "DraggablePoints_addEvent", "clamp", "DraggablePoints_isNumber", "DraggablePoints_merge", "mouseOut", "isHoveringHandle", "onPointMouseOut", "onPointMouseOver", "is3d", "onPointRemove", "pointGetDropValues", "result", "<PERSON><PERSON><PERSON><PERSON>", "updateSingleProp", "oldVal", "newVal", "limitToMapRange", "dir", "_c", "_d", "_e", "direction", "toUpperCase", "precision", "concat", "lonLatMin", "pixelsToLonLat", "lonLatMax", "plotBox", "min", "Infinity", "projection", "name", "isNaN", "maxLatitude", "temp", "hasCoordinates", "lonLatRes", "plotLeft", "plotHeight", "plotTop", "limitToRange", "time", "defaultPrecision", "categories", "parse", "toValue", "pointShowDragHandles", "renderer", "_loop_1", "handleOptions", "handleAttrs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validate", "pos", "handle", "path", "g", "markerGroup", "minEdge", "value", "translateX", "translateY", "rotation", "DraggablePoints_addEvents", "element", "DraggablePoints_getNormalizedEvent", "DraggablePoints_initDragDrop", "stopPropagation", "state_1", "seriesGetGuideBox", "changed", "minX", "maxX", "minY", "maxY", "plotX2", "skip<PERSON><PERSON>", "G", "DraggablePoints_DraggablePoints", "compose", "SeriesClass", "DraggablePoints_DraggableChart", "seriesProto", "PointClass", "seriesTypes", "pointProto", "seriesWithDragDropProps_1", "seriesType", "seriesWithoutDragDropProps_1", "Chart", "Series"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,eAC1B,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,sCAAuC,CAAC,CAAC,wBAAwB,CAAC,CAAEJ,GACpE,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,sCAAsC,CAAGD,EAAQG,QAAQ,eAEjEJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,IAAI,CAAE,SAASO,CAAgC,EAClD,OAAgB,AAAC,WACP,aACA,IAAIC,EAAuB,CAE/B,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaV,OAAO,CAG5B,IAAIC,EAASM,CAAwB,CAACE,EAAS,CAAG,CAGjDT,QAAS,CAAC,CACX,EAMA,OAHAM,CAAmB,CAACG,EAAS,CAACR,EAAQA,EAAOD,OAAO,CAAEQ,GAG/CP,EAAOD,OAAO,AACtB,CAMCQ,EAAoBI,CAAC,CAAG,SAASX,CAAM,EACtC,IAAIY,EAASZ,GAAUA,EAAOa,UAAU,CACvC,WAAa,OAAOb,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAO,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASf,CAAO,CAAEiB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACnB,EAASkB,IAC5EE,OAAOC,cAAc,CAACrB,EAASkB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAsB,CACrE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAejHE,EAAW,AAACD,IAA+EC,QAAQ,GA0BvG,SAAmBC,CAAE,CAAEC,CAAK,CAAEC,CAAE,CAAEC,CAAO,EACrC,IAAIC,EAAcH,EAAMI,GAAG,CAAC,SAAUC,CAAI,EAAI,OAAOP,EAASC,EAC1DM,EACAJ,EACAC,EAAU,GACd,OAAO,WACH,IAAK,IAAII,EAAK,EAAgCA,EAAKC,AAAlBJ,EAAgCK,MAAM,CAAEF,IAErEG,AADWF,CAAAA,EAAAA,AADkBJ,CACL,CAACG,EAAG,AAAD,GAGnC,CACJ,IA+CA,SAA4BI,CAAC,CAAEC,CAAK,EAChC,IAAIC,EACJ,MAAQ,CAAA,AAAoB,KAAA,IAAbF,EAAEG,MAAM,EACnB,AAAoB,KAAA,IAAbH,EAAEI,MAAM,AAAe,GAC7B,CAAA,AAAyB,OAAxBF,CAAAA,EAAKD,EAAMI,OAAO,AAAD,GAAeH,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGI,SAAS,CAACN,EAAC,GAAMA,CAEvF,EA4XiCO,EA5TV,CA+LnBC,gBAAiB,EAQjBC,WAAY,CA0BRC,UAAW,yBAOXC,MAAO,OAOPC,UAAW,qBAMXC,UAAW,EAMXC,OAAQ,GACZ,EASAC,SAAU,CAON,QAAW,CAOPL,UAAW,8BAMXG,UAAW,EAOXD,UAAW,OAOXD,MAAO,qBAMPK,OAAQ,OAMRF,OAAQ,GACZ,CACJ,CACJ,EAsBIG,EAAa,AAAC9B,IAA+E8B,UAAU,CAE/BC,EAtb5E,SAAoBvC,CAAG,EACnB,OAAOJ,OAAO4C,IAAI,CAACxC,GAAKmB,MAAM,AAClC,EAobsJsB,EAvatJ,SAAsBzC,CAAG,EACrB,IAAK,IAAI0C,KAAK1C,EACV,GAAIJ,OAAOO,cAAc,CAACC,IAAI,CAACJ,EAAK0C,GAChC,OAAO1C,CAAG,CAAC0C,EAAE,AAGzB,EAoaIC,EAAM,AAACnC,IAA+EmC,GAAG,CAEzFC,EAA0B,AAACpC,IAA+EC,QAAQ,CAAEoC,EAAU,AAACrC,IAA+EqC,OAAO,CAAEC,EAAQ,AAACtC,IAA+EsC,KAAK,CAAEC,EAAO,AAACvC,IAA+EuC,IAAI,CAkDrZ,SAASC,IACL,IACIC,EAAe3B,AADP,IAAI,CACS2B,WAAW,EAAI,CAAC,EACzC,GAAIA,EAAa,CACb,IAAK,IAAIhC,EAAK,EAAGM,EAAK3B,OAAO4C,IAAI,CAACS,GAAchC,EAAKM,EAAGJ,MAAM,CAAEF,IAAM,CAClE,IAAIvB,EAAM6B,CAAE,CAACN,EAAG,AACZgC,CAAAA,CAAW,CAACvD,EAAI,CAACwD,OAAO,EACxBD,CAAW,CAACvD,EAAI,CAACwD,OAAO,EAEhC,CACA,OAAO5B,AATC,IAAI,CASC2B,WAAW,AAC5B,CACJ,CAaA,SAASE,EAAsBC,CAAK,CAAEvC,CAAO,EACzC,IAAIuB,EAAW,IAAI,CAACiB,YAAY,CAC5BC,EAAkBR,EAAMlB,EAAiCQ,QAAQ,CACjEvB,GACA0C,EAAeT,EAAMQ,EAAgB,OAAU,CAC/CA,CAAe,CAACF,EAAM,EAC1B,OAAOhB,EACFoB,IAAI,CAAC,CACN,MAASD,EAAaxB,SAAS,CAC/B0B,OAAQF,EAAatB,SAAS,CAC9ByB,YAAaH,EAAarB,SAAS,CACnCyB,KAAMJ,EAAavB,KAAK,CACxBK,OAAQkB,EAAalB,MAAM,CAC3BF,OAAQoB,EAAapB,MAAM,AAC/B,GAEKyB,GAAG,CAAC,CAAEC,cAAe,MAAO,EACrC,CAWA,SAASC,EAAyBzC,CAAC,EAE/B,IAAkB0C,EAAezC,AAArB,IAAI,CAAuBT,OAAO,CAACS,KAAK,EAAI,CAAC,EAAG0C,EAASD,EAAaC,MAAM,EAAID,EAAaC,MAAM,CAAG,MAClH,OAAQ3C,CAAC,CAD0HC,AAAvH,IAAI,CAAyH2C,OAAO,CAACvE,GAAG,EAAI4B,AAA5I,IAAI,CAA8I2C,OAAO,CAACvE,GAAG,CAAG,MAC1J,EAAI2B,CAAC,CAAC2C,EAAO,AACnC,CAoFA,SAASE,EAAeC,CAAI,EACxB,MAAO,CAAA,CACHC,KAAM,QACNC,MAAO,OACPC,IAAK,SACLC,OAAQ,KACZ,CAAA,CAAC,CAACJ,EAAK,AACX,CAoNA,SAASK,EAAanD,CAAC,CAAEoD,CAAK,EAC1B,IAGIrC,EAHAsC,EAAgBC,AAzMxB,SAA0BF,CAAK,EAC3B,IAAIG,EAASH,EAAMG,MAAM,CACrBC,EAAOD,EAAO/D,OAAO,CAACgE,IAAI,EAAI,EAAE,CAChCC,EAAWF,EAAO/D,OAAO,CAACkE,QAAQ,CAACC,OAAO,CAC1CC,EAAS,EAAE,CACf,GAAIL,EAAOM,OAAO,EAAIrC,EAAQgC,GAC1B,IAAK,IAAIM,EAAI,EAAGC,EAAOP,EAAK1D,MAAM,CAAEgE,EAAIC,EAAM,EAAED,EAC5CF,EAAOI,IAAI,CAAC,IAAIT,EAAOU,UAAU,CACjCV,EAAQC,CAAI,CAACM,EAAE,GACfF,CAAM,CAACA,EAAO9D,MAAM,CAAG,EAAE,CAACoE,KAAK,CAAGJ,OAItCF,EAASL,EAAOK,MAAM,CAE1B,OAAOR,EAAM5D,OAAO,CAACiE,EAAS,CAE1BG,EAAOO,MAAM,CAAC,SAAUC,CAAY,EAAI,OAAQA,EAAa5E,OAAO,CAACiE,EAAS,GAC1EL,EAAM5D,OAAO,CAACiE,EAAS,AAAG,GAE9B,CAACL,EAAM,AACf,EAoLyCA,GACjCG,EAASH,EAAMG,MAAM,CACrBtD,EAAQsD,EAAOtD,KAAK,CAGnByB,EAAK6B,EAAO/D,OAAO,CAACkE,QAAQ,EAAIH,EAAO/D,OAAO,CAACkE,QAAQ,CAACW,UAAU,CAAE,CAAA,KACrEpE,EAAM+B,YAAY,CAAGjB,EAAWwC,EAAOe,WAAW,CAACjB,GACnDpD,EACKsE,gBAAgB,CAAC,UAAWhB,EAAO/D,OAAO,CAACkE,QAAQ,CAAC3C,QAAQ,EAC5DyD,GAAG,CAACjB,EAAOkB,KAAK,GAGzBxE,EAAMyE,YAAY,CAAG,CACjBC,OAAQC,AApGhB,SAA6B5E,CAAC,CAAE4D,CAAM,CAAE7C,CAAQ,EAa5C,IAAK,IAZD8D,EAAM,CACF1E,OAAQH,EAAEG,MAAM,CAChBC,OAAQJ,EAAEI,MAAM,CAChBW,SAAUA,GAAY,CAClB+D,EAAG/D,EAASoB,IAAI,CAAC,KACjB4C,EAAGhE,EAASoB,IAAI,CAAC,KACjB6C,MAAOjE,EAASoB,IAAI,CAAC,SACrB8C,OAAQlE,EAASoB,IAAI,CAAC,SAC1B,EACAyB,OAAQ,CAAC,CACb,EAEKhE,EAAK,EAAsBA,EAAKsF,AAAbtB,EAAsB9D,MAAM,CAAEF,IAAM,CAM5D,IAAK,IALDwD,EAAQ8B,AADYtB,CACJ,CAAChE,EAAG,CACpBuF,EAAgB/B,EAAMG,MAAM,CAAC4B,aAAa,EAAI,CAAC,EAC/CC,EAAa,CAAC,EAGTlF,EAAK,EAAGmF,EAAK9G,OAAO4C,IAAI,CAACgE,GAAgBjF,EAAKmF,EAAGvF,MAAM,CAAEI,IAAM,CACpE,IAAI7B,EAAMgH,CAAE,CAACnF,EAAG,CACZoF,EAAMH,CAAa,CAAC9G,EAAI,CACxBkH,EAAOnC,EAAMG,MAAM,CAAC+B,EAAIC,IAAI,CAAG,OAAO,AAC1CH,CAAAA,CAAU,CAAC/G,EAAI,CAAG+E,CAAK,CAAC/E,EAAI,CAKxB+E,EAAMG,MAAM,CAACtD,KAAK,CAACuF,OAAO,EAAIpC,EAAMqC,KAAK,EAAIrC,EAAMsC,KAAK,CACxDN,CAAU,CAAC/G,EAAM,SAAS,CAAGA,AAAQ,MAARA,EACzB+E,EAAMqC,KAAK,CAAGrC,EAAMsC,KAAK,CAG7BN,CAAU,CAAC/G,EAAM,SAAS,CAGtBkH,EAAKI,QAAQ,CAACvC,CAAK,CAAC/E,EAAI,EACnBkH,CAAAA,EAAKK,KAAK,CAAG5F,EAAEG,MAAM,CAAGH,EAAEI,MAAM,AAAD,CAEhD,CACAgF,EAAWhC,KAAK,CAAGA,EACnByB,EAAIjB,MAAM,CAACR,EAAMyC,EAAE,CAAC,CAAGT,CAC3B,CACA,OAAOP,CACX,EAwDoC7E,EAAGqD,EAAetC,GAC9CqC,MAAOA,EACPC,cAAeA,EACfyC,WAAY,CAAA,CAChB,CACJ,CA4MA,SAASC,EAAQ/F,CAAC,CAAEC,CAAK,EACrB,IAAIyE,EAAezE,EAAMyE,YAAY,CACrC,GAAIA,GACAA,EAAaoB,UAAU,EACvBpB,EAAasB,sBAAsB,EACnCtB,EAAatB,KAAK,CAACG,MAAM,CAAE,CAC3B,IAAIH,EAAQsB,EAAatB,KAAK,CAC1B6C,EAAYvB,EAAauB,SAAS,CAClCC,EAAehF,EAA0B+E,GACzCE,EAAWD,AAAiB,IAAjBA,EACP9E,EAA4B6E,GAC5B,IAEJhG,CAAAA,EAAM2B,WAAW,EACjB3B,EAAMmG,eAAe,GAGzBpG,EAAEqG,cAAc,GAChBpG,EAAMqG,WAAW,CAAG,CAAA,EAEpBlD,EAAMmD,cAAc,CAAC,OAAQ,CACzB5B,OAAQD,EAAaC,MAAM,CAC3BxE,OAAQH,EAAEG,MAAM,CAChBC,OAAQJ,EAAEI,MAAM,CAChB6F,UAAWA,EACXC,aAAcA,EACdC,SAAUA,GAAYA,EAASK,SAAS,CACxCC,WAAYN,GAAYA,EAAS/C,KAAK,CAACyC,EAAE,AAC7C,EAAG,WACCa,EAAazG,EACjB,EACJ,CAEA,OAAOA,EAAMyE,YAAY,CAGrBzE,EAAM+B,YAAY,GAClB/B,EAAM+B,YAAY,CAACH,OAAO,GAC1B,OAAO5B,EAAM+B,YAAY,CAEjC,CAMA,SAAS2E,IAEL,GAAI,CAAC,IAAI,CAACC,sBAAsB,CAAE,KA3pBX3G,EACnB4G,EADmB5G,EA4pBD,IAAI,CA3pBtB4G,EAAY5G,EAAM4G,SAAS,CAE3BC,AAwaR,SAA0B7G,CAAK,EAC3B,IAAI6D,EAAI7D,EAAMsD,MAAM,CAAGtD,EAAMsD,MAAM,CAACzD,MAAM,CAAG,EAC7C,GAAI,AAACG,EAAM8G,kBAAkB,EAAI,CAAC9G,EAAM+G,KAAK,EACzC/G,EAAMuF,OAAO,CACb,CAAA,KAAO1B,KACH,GAAI7D,EAAMsD,MAAM,CAACO,EAAE,CAACtE,OAAO,CAACkE,QAAQ,EAChCuD,AA0DhB,SAA2B1D,CAAM,EAI7B,IAAK,IAFD+B,EADA4B,EAAQ,CAAC,aAAc,aAAa,CAAE/B,EAAgB5B,EAAO4B,aAAa,EAAI,CAAC,EAG1EvF,EAAK,EAAGM,EAAK3B,OAAO4C,IAAI,CAACgE,GAAgBvF,EAAKM,EAAGJ,MAAM,CAAEF,IAG1D0F,AADJA,CAAAA,EAAMH,CAAa,CADTjF,CAAE,CAACN,EAAG,CACQ,AAAD,EACfuH,UAAU,EACdD,EAAMlD,IAAI,CAACsB,EAAI6B,UAAU,EAMjC,IAbA,IAYIrD,EAAIoD,EAAMpH,MAAM,CACbgE,KACH,GAAIP,EAAO/D,OAAO,CAACkE,QAAQ,CAACwD,CAAK,CAACpD,EAAE,CAAC,CACjC,MAAO,CAAA,CAGnB,EA7EkC7D,EAAMsD,MAAM,CAACO,EAAE,EACjC,MAAO,CAAA,CAEf,CAEJ,MAAO,CAAA,CACX,EApbyB7D,KACjBmH,EAAyBP,EAAW,CAAC,YAAa,aAAa,CAAE,SAAU7G,CAAC,EACxEqH,AAogBZ,CAAA,SAAmBrH,CAAC,CAAEC,CAAK,EACvB,IAAIqH,EAAYrH,EAAMsH,UAAU,CAC5BC,EAAkB/F,EAAM6F,GAAaA,EAAU/D,MAAM,CAAC/D,OAAO,CAACkE,QAAQ,CACtE4D,GAAaA,EAAU9H,OAAO,CAACkE,QAAQ,EACvC+D,EAAaD,EAAgBC,UAAU,EAAI,CAAA,EAC3CC,EAAaF,EAAgBE,UAAU,EAAI,CAAA,EAI/C,GAFAzH,EAAMqG,WAAW,CAAG,CAAA,EAIpB,CAAA,CAAA,CAAEmB,CAAAA,GAAcC,CAAS,GAErBzH,EAAM0H,mBAAmB,CAAC3H,EAAC,IAE3BC,EAAM2H,oBAAoB,EAI9B,GAAI3H,EAAMyE,YAAY,EAAIzE,EAAMyE,YAAY,CAACoB,UAAU,CAAE,CACrDC,EAAQ/F,EAAGC,GACX,MACJ,CAEIqH,GAAaO,AA9FrB,SAAwBzE,CAAK,EACzB,IAKI/B,EACAyG,EACAC,EAPAxE,EAASH,EAAMG,MAAM,CACrBtD,EAAQsD,EAAOtD,KAAK,CACpB+H,EAAwBzE,EAAO/D,OAAO,CAACkE,QAAQ,EAAI,CAAC,EACpDuE,EAAuB7E,EAAM5D,OAAO,EAAI4D,EAAM5D,OAAO,CAACkE,QAAQ,CAC9DwE,EAAc3E,EAAO4B,aAAa,CAKtC,IAAK,IAAI9G,KAAO6J,EAER7G,AAAW,MAAXA,AADJA,CAAAA,EAAI6G,CAAW,CAAC7J,EAAI,AAAD,EACbkH,IAAI,EAAYlE,EAAE8G,IAAI,CACxBL,EAAc,CAAA,EAEE,MAAXzG,EAAEkE,IAAI,EAAYlE,EAAE8G,IAAI,EAC7BJ,CAAAA,EAAc,CAAA,CAAG,EAKzB,MAAQ,AAACC,CAAAA,EAAsBP,UAAU,EAAIK,GACzCE,EAAsBN,UAAU,EAAIK,CAAU,GAC9C,CAAEE,CAAAA,GACEA,AAAoC,CAAA,IAApCA,EAAqBR,UAAU,EAC/BQ,AAAoC,CAAA,IAApCA,EAAqBP,UAAU,AAAS,GAC3C,CAAA,CAAC,CAAEnE,CAAAA,EAAO6E,KAAK,EAAI7E,EAAO8E,KAAK,AAAD,GAC3BpI,EAAMuF,OAAO,AAAD,CACxB,EAkEoC8B,KAC5BrH,EAAMqI,WAAW,CAAG,CAAA,EACpBnF,EAAanD,EAAGsH,GAChBA,EAAUf,cAAc,CAAC,YAAavG,IAE9C,CAAA,EAjiBsBuI,EAAkCvI,EAAGC,GAAQA,EAC3D,GACAmH,EAAyBP,EAAW,CAAC,YAAa,YAAY,CAAE,SAAU7G,CAAC,EACvEwI,AAyiBZ,CAAA,SAAmBxI,CAAC,CAAEC,CAAK,EAEvB,IAAIA,EAAM0H,mBAAmB,CAAC3H,IAG9B,IA/LyCyI,EACrCC,EACAC,EACAC,EACAC,EACAC,EA2LA1F,EACA2F,EACA9C,EAEAE,EALAzB,EAAezE,EAAMyE,YAAY,CAIjCwB,EAAe,EAEfxB,GAAgBA,EAAaoB,UAAU,EAAIpB,EAAatB,KAAK,CAACG,MAAM,GAEpEwF,EAAqB3F,AADrBA,CAAAA,EAAQsB,EAAatB,KAAK,AAAD,EACEG,MAAM,CAAC/D,OAAO,CAACkE,QAAQ,CAElD1D,EAAEqG,cAAc,GAEZ,CAAC3B,EAAasB,sBAAsB,GA3MHyC,EA4MyC/G,EAAK0B,EAAM5D,OAAO,CAACkE,QAAQ,EACjGN,EAAM5D,OAAO,CAACkE,QAAQ,CAAClD,eAAe,CAAEuI,GACxCA,EAAmBvI,eAAe,CAAED,EAAiCC,eAAe,EA5M5FmI,EAAOD,CADPA,EAAOzI,AA2MgEA,EA3M1DyE,YAAY,CAACC,MAAM,EACpBxE,MAAM,CAClByI,EAAOF,EAAKtI,MAAM,CAyMdsE,EAAasB,sBAAsB,CApMpCgD,AAFQC,KAAKC,IAAI,CAAC,AAACL,CAAAA,CAFtBA,EAAO7I,AAwM6DA,EAxM3DG,MAAM,EAEcwI,CAAG,EAAME,CAAAA,EAAOF,CAAG,EAC5C,AAACG,CAAAA,CAFLA,EAAO9I,AAuM6DA,EAvM3DI,MAAM,EAEHwI,CAAG,EAAME,CAAAA,EAAOF,CAAG,GACjBH,GA0MV/D,EAAasB,sBAAsB,GAEnCtB,EAAauB,SAAS,CAAGkD,AAhWrC,SAAsBzE,CAAY,CAAE0E,CAAM,EACtC,IAAIhG,EAAQsB,EAAatB,KAAK,CAC1BG,EAASH,EAAMG,MAAM,CACrBtD,EAAQsD,EAAOtD,KAAK,CACpBT,EAAUiC,EAAM8B,EAAO/D,OAAO,CAACkE,QAAQ,CACvCN,EAAM5D,OAAO,CAACkE,QAAQ,EACtBwE,EAAc,CAAC,EACfmB,EAAa3E,EAAa4E,UAAU,CACpCC,EAAU,CAAC,EACXpE,EAAgB/B,EAAMG,MAAM,CAAC4B,aAAa,CAI9C,IAAK,IAAI9G,KAAO8G,EAAe,CAC3B,IAAIG,EAAMH,CAAa,CAAC9G,EAAI,CAGxBgL,CAAAA,CAAAA,GAAeA,IAAehL,GAC7BiH,EAAIkE,MAAM,EACXlE,CAAAA,CAAAA,EAAI6B,UAAU,EAAI3H,AAA4B,CAAA,IAA5BA,CAAO,CAAC8F,EAAI6B,UAAU,CAAC,AAAS,CAAC,GAMnDkC,CAAAA,GAAe/D,EAAI6C,IAAI,EACtB7C,CAAAA,AAAa,MAAbA,EAAIC,IAAI,EAAY/F,EAAQiI,UAAU,EACnCnC,AAAa,MAAbA,EAAIC,IAAI,EAAY/F,EAAQkI,UAAU,AAAD,CAAE,IACvCzH,EAAMuF,OAAO,CACb0C,CAAW,CAAC7J,AAAQ,MAARA,EAAc,MAAQ,MAAM,CAAGiH,EAG3C4C,CAAW,CAAC7J,EAAI,CAAGiH,EAG/B,CAEA,IAEA,IAAI1F,EAAK,EACLM,EAAKmJ,EACD,CAACjG,EAAM,CACPsB,EAAarB,aAAa,CAE9BzD,EAAKM,EAAGJ,MAAM,CAEdF,IAAM,CACF,IAAIyB,EAAInB,CAAE,CAACN,EAAG,AAClB2J,CAAAA,CAAO,CAAClI,EAAEwE,EAAE,CAAC,CAAG,CACZzC,MAAO/B,EACPmF,UAAWnF,EAAEoI,aAAa,CAAC/E,EAAaC,MAAM,CAAEyE,EAAQlB,EAC5D,CACJ,CACA,OAAOqB,CACX,EA0SkD7E,EAAc1E,GAIpDmG,EAAWD,AAAiB,IAD5BA,CAAAA,EAAehF,EADf+E,EAAYvB,EAAauB,SAAS,CACgB,EAE9C7E,EAA4B6E,GAC5B,KAEJ7C,EAAMmD,cAAc,CAAC,OAAQ,CACzB5B,OAAQD,EAAaC,MAAM,CAC3BsB,UAAWvB,EAAauB,SAAS,CACjCE,SAAUA,GAAYA,EAASK,SAAS,CACxCC,WAAYN,GAAYA,EAAS/C,KAAK,CAACyC,EAAE,CACzCK,aAAcA,EACd/F,OAAQH,EAAEG,MAAM,CAChBC,OAAQJ,EAAEI,MAAM,AACpB,EAAG,eA9dPmD,EACAtD,EACAuD,EACAhE,EAEAiI,EACAC,EACA/C,EACA2E,EACAI,EACAC,EACAC,EAoiBgBxG,EAAOsG,EAAIC,EAC3BpG,EACAtD,EACAyE,EACA2E,EAEAlD,EACA0D,EApjBArG,EAAOvD,CADPA,EAAQsD,CADRA,EAASH,AA+dWA,EA/dLG,MAAM,EACNtD,KAAK,EACPyE,YAAY,CAGzB+C,EAAajI,CAFbA,EAAUiC,EAAM8B,EAAO/D,OAAO,CAACkE,QAAQ,CACvCN,AA2doBA,EA3dd5D,OAAO,CAACkE,QAAQ,GACD+D,UAAU,CAC/BC,EAAalI,EAAQkI,UAAU,CAC/B/C,EAASnB,EAAKmB,MAAM,CACpB2E,EAAa9F,EAAK8F,UAAU,CAC5BI,EAAK1J,AAsdYA,EAtdVG,MAAM,CAAGwE,EAAOxE,MAAM,CAC7BwJ,EAAK3J,AAqdYA,EArdVI,MAAM,CAAGuE,EAAOvE,MAAM,CAC7BwJ,EAAQF,EAERzJ,EAAM6J,QAAQ,GACdJ,EAAK,CAACC,EACNA,EAAK,CAACC,GAINlI,EAAKlC,EAAQ6E,UAAU,CAAE,CAAA,IACzBqC,EAAazG,EAAO,CAAA,GAEpBmD,AAycoBA,EAzcd2G,eAAe,IAIjBT,GAqhBYlG,EAhFIA,EAgFGsG,EAnhBGA,EAmhBCC,EAnhBGA,EAshB9BjF,EAAezE,CADfA,EAAQsD,CADRA,EAASH,EAAMG,MAAM,EACNtD,KAAK,EACCyE,YAAY,CACjC2E,EAAa9F,EAAO4B,aAAa,CAACT,EAAa4E,UAAU,CAAC,CAE1DnD,EAAWzB,EAAauB,SAAS,CAAC7C,EAAMyC,EAAE,CAAC,CAACW,SAAS,CACrDqD,EAAa,AAAiC,YAAjC,OAAOR,EAAWQ,UAAU,CACrCR,EAAWQ,UAAU,CAAC1D,EAC1B/C,GAASiG,EAAWQ,UAAU,CAE9BR,EAAWW,YAAY,EACvBX,EAAWW,YAAY,CAAC/J,EAAM+B,YAAY,CAAEmE,EAAU/C,GAG1D6G,AAwBJ,SAAoBC,CAAI,CAAEC,CAAU,CAAEC,CAAM,EACxC,IAAIC,EACJ,OAAQF,GACJ,IAAK,OACDE,EAAc,CACVvF,EAAGoF,EAAK/H,IAAI,CAAC,KAAOiI,EAAOtF,CAAC,CAC5BE,MAAOiE,KAAKqB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,SAAWiI,EAAOtF,CAAC,CACpD,EACA,KACJ,KAAK,QACDuF,EAAc,CACVrF,MAAOiE,KAAKqB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,SAAWiI,EAAOtF,CAAC,CACpD,EACA,KACJ,KAAK,MACDuF,EAAc,CACVtF,EAAGmF,EAAK/H,IAAI,CAAC,KAAOiI,EAAOrF,CAAC,CAC5BE,OAAQgE,KAAKqB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,UAAYiI,EAAOrF,CAAC,CACtD,EACA,KACJ,KAAK,SACDsF,EAAc,CACVpF,OAAQgE,KAAKqB,GAAG,CAAC,EAAGJ,EAAK/H,IAAI,CAAC,UAAYiI,EAAOrF,CAAC,CACtD,CAGR,CACAmF,EAAK/H,IAAI,CAACkI,EACd,EApDepK,EAAM+B,YAAY,CAAEqH,AAAoB,MAApBA,EAAW9D,IAAI,EAAYhC,EAAO8E,KAAK,CAACkC,QAAQ,EAC3ElB,AAAoB,MAApBA,EAAW9D,IAAI,EAAYhC,EAAO6E,KAAK,CAACmC,QAAQ,CAChD1H,EAAegH,GAAcA,EAAY,CACzC/E,EAAGuE,AAAoB,MAApBA,EAAW9D,IAAI,CACdmE,EAAMhF,CAAAA,EAAaC,MAAM,CAAC6F,MAAM,EAAI,CAAA,EAAK,EAC7CzF,EAAGsE,AAAoB,MAApBA,EAAW9D,IAAI,CACdoE,EAAMjF,CAAAA,EAAaC,MAAM,CAAC8F,MAAM,EAAI,CAAA,EAAK,CACjD,IAriBQxK,EAAM+B,YAAY,CAAC0I,SAAS,CAACjD,EAAaiC,EAAK,EAAGhC,EAAaiC,EAAK,GAI5EhF,EAAO6F,MAAM,CAAGd,EAChB/E,EAAO8F,MAAM,CAAGd,CA2bR,KAGZ,CAAA,EAxlBsBpB,EAAkCvI,EAAGC,GAAQA,EAC3D,EAAG,CACC0K,QAAS,CAAA,CACb,GACApJ,EAAwBsF,EAAW,aAAc,SAAU7G,CAAC,EACxD+F,EAAQwC,EAAkCvI,EAAGC,GAAQA,EACzD,GACAA,EAAM2K,qBAAqB,CAAGxD,EAAyB9F,EAAK,CAAC,UAAW,WAAW,CAAE,SAAUtB,CAAC,EAC5F+F,EAAQwC,EAAkCvI,EAAGC,GAAQA,EACzD,EAAG,CACC0K,QAAS,CAAA,CACb,GAEA1K,EAAM2G,sBAAsB,CAAG,CAAA,EAE/BrF,EAAwBtB,EAAO,UAAW,WAClCA,EAAM2K,qBAAqB,EAC3B3K,EAAM2K,qBAAqB,EAEnC,GAkoBJ,CACJ,CA4FA,SAASlE,EAAazG,CAAK,CAAE4K,CAAS,EAClC,IAGI1E,EAHAF,EAAYhG,EAAMyE,YAAY,CAACuB,SAAS,CACxC6E,EAAc7J,EAAW4J,EAC7B5K,CAAAA,EAAM8K,mBAAmB,CAAG,CAAA,EAG5B,IAAK,IAAInL,EAAK,EAAGM,EAAK3B,OAAO4C,IAAI,CAAC8E,GAAYrG,EAAKM,EAAGJ,MAAM,CAAEF,IAG1DuG,AADAA,CAAAA,EAAWF,CAAS,CADV/F,CAAE,CAACN,EAAG,CACS,AAAD,EACfwD,KAAK,CAACgH,MAAM,CAACjE,EAASK,SAAS,CAAE,CAAA,GAE9CvG,EAAM+K,MAAM,CAACF,GAIbG,WAAW,WACP,OAAOhL,EAAM8K,mBAAmB,CAC5B9K,EAAMsH,UAAU,EAAI,CAACtH,EAAM2B,WAAW,EACtC3B,EAAMsH,UAAU,CAACwC,eAAe,EAExC,EAAGe,EAAYI,QAAQ,CAC3B,CAW6B,MAlrB7B,SAAiBC,CAAU,EACvB,IAAIC,EAAaD,EAAWtM,SAAS,AAChCuM,CAAAA,EAAWhF,eAAe,GAC3BgF,EAAWhF,eAAe,CAAGzE,EAC7ByJ,EAAW7G,gBAAgB,CAAGzC,EAC9BsJ,EAAWzD,mBAAmB,CAAGlF,EACjClB,EAAwB4J,EAAY,SAAUxE,GAEtD,EAyrBI0E,EAAgD,WAShD,MAAOA,AARPA,CAAAA,EAAW9M,OAAO+M,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAG1H,EAAI,EAAG/F,EAAI0N,UAAU3L,MAAM,CAAEgE,EAAI/F,EAAG+F,IAE5C,IAAK,IAAIzC,KADTmK,EAAIC,SAAS,CAAC3H,EAAE,CACKvF,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACyM,EAAGnK,IACzDkK,CAAAA,CAAC,CAAClK,EAAE,CAAGmK,CAAC,CAACnK,EAAE,AAAD,EAElB,OAAOkK,CACX,CAAA,EACgBG,KAAK,CAAC,IAAI,CAAED,UAChC,EAIIE,EAAW,AAACxM,IAA+EwM,QAAQ,CAAEC,EAAsB,AAACzM,IAA+EsC,KAAK,CAAEoK,EAAqB,AAAC1M,IAA+EuC,IAAI,CAO3ToK,EAAO,CACPhH,EAAG,CACCS,KAAM,IACN4C,KAAM,CAAA,CACV,EACApD,EAAG,CACCQ,KAAM,IACN4C,KAAM,CAAA,CACV,CACJ,EAKI4D,EAAS,CACTjH,EAAG,CACCS,KAAM,IACN4C,KAAM,CAAA,CACV,EACApD,EAAG,CACCQ,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EAERQ,aAAc,SAAUjJ,CAAQ,CAAEiL,CAAS,CAAE5I,CAAK,EAK9C,IAKI6B,EALAgH,EAAgBJ,EAAmBzI,EAAM8I,OAAO,CAChD9I,EAAMG,MAAM,CAAC4I,mBAAmB,EAChCzG,EAAQ3E,EAASoB,IAAI,CAAC,KACtBiK,EAAYT,EAASvI,EAAMiJ,MAAM,EAAKjJ,EAAMiJ,MAAM,CAAIjJ,CAAAA,EAAM2B,CAAC,EAAI,CAAA,EAAM3B,EAAMG,MAAM,CAAC/D,OAAO,CAAC4M,SAAS,EAAI,EACzGrH,EAAIqH,EAAYJ,EAAUjH,CAAC,CAG3B3B,CAAAA,EAAMG,MAAM,CAAC6E,KAAK,CAACmC,QAAQ,CAAGxF,EAAIqH,EAAYrH,GAAKqH,CAAQ,GAE3DnH,EAASlE,EAASoB,IAAI,CAAC,UAEvBpB,EAASoB,IAAI,CAAC,CACV8C,OAAQgE,KAAKqB,GAAG,CAAC,EAAGrB,KAAKqD,KAAK,CAACrH,EAF5BgH,CAAAA,EAAgBA,EAAgBvG,EAAQT,EAAS,CAAA,GAGxD,IAIAlE,EAASoB,IAAI,CAAC,CACV4C,EAAGkE,KAAKqD,KAAK,CAAC5G,EAASuG,CAAAA,EAAgBA,EAAgBvG,EAAQ,CAAA,EACnE,EAER,EAGAmE,WAAY,SAAUmC,CAAS,CAAE5I,CAAK,EAClC,IACIxB,EAAc3B,AADNmD,EAAMG,MAAM,CAACtD,KAAK,CACN2B,WAAW,CAC/BkB,EAAOkJ,EAAUjH,CAAC,EAAK3B,CAAAA,EAAMG,MAAM,CAAC/D,OAAO,CAAC4M,SAAS,EAAI,CAAA,EACrD,MAAQ,SACZG,EAAWC,AAjGP3J,EAiGoCC,GAM5C,OAJIlB,GAAeA,CAAW,CAAC2K,EAAS,GACpC3K,CAAW,CAAC2K,EAAS,CAAC1K,OAAO,GAC7B,OAAOD,CAAW,CAAC2K,EAAS,EAEzBzJ,CACX,EAEA2J,iBAAkB,SAAUrJ,CAAK,EAC7B,IAAIsJ,EAAQtJ,EAAMuJ,SAAS,EAClBvJ,EAAMwJ,OAAO,EAAIxJ,EAAMwJ,OAAO,CAACC,OAAO,IACvC,CAAC,EACLtC,EAAWnH,EAAMG,MAAM,CAAC6E,KAAK,CAACmC,QAAQ,CACtC6B,EAAYhJ,EAAMG,MAAM,CAAC/D,OAAO,CAAC4M,SAAS,EAAI,EAC9CrH,EAAI3B,EAAM2B,CAAC,EAAI,EAGnB,MAAO,CACHD,EAAG4H,EAAK5H,CAAC,EAAI,EACbC,EAAG7B,AAJM,AAAC,CAACqH,GAAYxF,GAAKqH,GACvB7B,GAAYxF,EAAIqH,EAGRM,EAAK3H,CAAC,EAAI,EAAK,AAAC2H,CAAAA,EAAK3H,CAAC,EAAI,CAAA,EAAM2H,CAAAA,EAAKzH,MAAM,EAAI,CAAA,CAChE,CACJ,EAEA6H,gBAAiB,SAAU1J,CAAK,EAC5B,IAAIuJ,EAAYvJ,EAAMuJ,SAAS,EAAI,CAAC,EAChCI,EAASJ,EAAUK,CAAC,EAAI,EACxBhI,EAAQ2H,EAAU3H,KAAK,EAAI,EAC3BiI,EAAUjI,EAAQ,EACtB,MAAO,CAEH,CAAC,IAAK+H,EAAQ,EAAE,CAChB,CAAC,IAAKE,EAAU,EAAG,EAAE,CAErB,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAG,EAAE,CACpC,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAG,EAAE,CAEpC,CAAC,IAAKA,EAAU,EAAG,EAAE,CACrB,CAAC,IAAKjI,EAAQ+H,EAAQ,EAAE,CAC3B,AACL,CACJ,CACJ,EAEIG,EAAU,CACVpI,EAAGiH,EAAOjH,CAAC,CASXqI,IAAK,CACDhG,WAAY,eACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,EAAI,EACxBC,EAAG3B,EAAMgK,OAAO,AACpB,CAAI,EACJN,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMkK,EAAE,AAAG,CACpE,EASAA,GAAI,CACAnG,WAAY,cACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,EAAI,EACxBC,EAAG3B,EAAMmK,MAAM,AACnB,CAAI,EACJT,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMoK,MAAM,EAAIlI,GAAOlC,EAAM+J,GAAG,AAAG,CAC5F,EACAK,OAAQ,CAGJjI,KAAM,IACN4C,KAAM,CAAA,CACV,EASAsF,GAAI,CACAtG,WAAY,cACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,MACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,EAAI,EACxBC,EAAG3B,EAAMsK,MAAM,AACnB,CAAI,EACJZ,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMuK,IAAI,EAAIrI,GAAOlC,EAAMoK,MAAM,AAAG,CAC7F,EASAG,KAAM,CACFxG,WAAY,gBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,MACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,EAAI,EACxBC,EAAG3B,EAAMwK,QAAQ,AACrB,CAAI,EACJd,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMqK,EAAE,AAAG,CACpE,CACJ,EAEII,EAAW,CACX/I,EAAGiH,EAAOjH,CAAC,CACXqI,IAAK9B,EAASA,EAAS,CAAC,EAAG6B,EAAQC,GAAG,EAAG,CAAEE,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMuK,IAAI,AAAG,CAAE,GAC/GA,KAAMtC,EAASA,EAAS,CAAC,EAAG6B,EAAQS,IAAI,EAAG,CAAEN,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAM+J,GAAG,AAAG,CAAE,EACpH,EAMIW,EAAS,CACThJ,EAAGiH,EAAOjH,CAAC,CACXC,EAAGgH,EAAOhH,CAAC,CASXgJ,OAAQ,CACJ5G,WAAY,kBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,MACZ4C,iBAAkB,SAAUrJ,CAAK,EAC7B,IAAIsJ,EAAOtJ,EAAM4K,aAAa,CAACnB,OAAO,GACtC,MAAO,CACH/H,EAAG1B,EAAM6K,IAAI,CACblJ,EAAG2H,EAAK3H,CAAC,CAAG2H,EAAKzH,MAAM,CAAG,CAC9B,CACJ,EACA6H,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,AAC7C,CACJ,EAEIoB,EAAO,CACPpJ,EAAGiH,EAAOjH,CAAC,CASXqI,IAAK,CACDhG,WAAY,eACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,CACpBC,EAAG3B,EAAM+K,OAAO,AACpB,CAAI,EACJrB,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMgL,IAAI,EAAI9I,GAAOlC,EAAMiL,KAAK,AAAG,CAC5F,EASAV,KAAM,CACFxG,WAAY,gBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,MACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,CACpBC,EAAG3B,EAAMkL,QAAQ,AACrB,CAAI,EACJxB,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMgL,IAAI,EAAI9I,GAAOlC,EAAMiL,KAAK,AAAG,CAC5F,EASAD,KAAM,CACFjH,WAAY,gBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SAAUzG,CAAK,EAAI,OAAQA,EAAMgL,IAAI,EAAIhL,EAAMiL,KAAK,CAAG,MAAQ,QAAW,EACtF5B,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,CACpBC,EAAG3B,EAAMmL,QAAQ,AACrB,CAAI,EACJzB,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMuK,IAAI,EAAIrI,GAAOlC,EAAM+J,GAAG,AAAG,CAC1F,EASAkB,MAAO,CACHlH,WAAY,iBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SAAUzG,CAAK,EAAI,OAAQA,EAAMgL,IAAI,EAAIhL,EAAMiL,KAAK,CAAG,SAAW,KAAQ,EACtF5B,iBAAkB,SAAUrJ,CAAK,EAAI,MAAQ,CACzC0B,EAAG1B,EAAMuJ,SAAS,CAAC7H,CAAC,CACpBC,EAAG3B,EAAMoL,SAAS,AACtB,CAAI,EACJ1B,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMuK,IAAI,EAAIrI,GAAOlC,EAAM+J,GAAG,AAAG,CAC1F,CACJ,EAEIsB,EAAY,CACZ3J,EAAGiH,EAAOjH,CAAC,CACXC,EAAG6G,EAAoBG,EAAOhH,CAAC,CAAE,CAC7B+H,gBAAiB,SAAU1J,CAAK,EAC5B,IAAIlD,EACAmF,EACJ,OAAQjC,EAAMsL,KAAK,EAAItL,EAAMuL,iBAAiB,CAC1C,KACA,AAAC,CAAA,AAAmI,OAAlItJ,CAAAA,EAAK,AAAoE,OAAnEnF,CAAAA,EAAK6L,MAAAA,EAAuC,KAAK,EAAIA,EAAOhH,CAAC,AAADA,GAAe7E,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG4M,eAAe,AAAD,GAAezH,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGtG,IAAI,CAACmB,EAAIkD,EAAK,GAAM,IACpM,CACJ,EACJ,EAEIwL,EAAc,CACd9J,EAAG,CACCS,KAAM,IACN4C,KAAM,CAAA,CACV,EASAgF,IAAK,CACDhG,WAAY,eACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SACZ4C,iBAAkB,SAAUrJ,CAAK,EAC7B,IAAIsJ,EAAQtJ,EAAMuJ,SAAS,EAAIvJ,EAAMwJ,OAAO,CAACC,OAAO,GACpD,MAAO,CACH/H,EAAG4H,EAAK5H,CAAC,EAAI,EACbC,EAAG,AAAC2H,CAAAA,EAAK3H,CAAC,EAAI,CAAA,EAAM2H,CAAAA,EAAKzH,MAAM,EAAI,CAAA,CACvC,CACJ,EACA6H,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAMuK,IAAI,AAAG,CACtE,EASAA,KAAM,CACFxG,WAAY,gBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,MACZ4C,iBAAkB,SAAUrJ,CAAK,EAC7B,IAAIsJ,EAAQtJ,EAAMuJ,SAAS,EAAIvJ,EAAMwJ,OAAO,CAACC,OAAO,GACpD,MAAO,CACH/H,EAAG4H,EAAK5H,CAAC,EAAI,EACbC,EAAG2H,EAAK3H,CAAC,EAAI,CACjB,CACJ,EACA+H,gBAAiBf,EAAOhH,CAAC,CAAC+H,eAAe,CACzCO,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAM+J,GAAG,AAAG,CACrE,CACJ,EAEI0B,EAAY,CACZ/J,EAAG8J,EAAY9J,CAAC,CAShBqI,IAAK,CACDhG,WAAY,eACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,SACZ4C,iBAAkB,SAAUrJ,CAAK,EAC7B,IAAIsJ,EAAQtJ,EAAM0L,QAAQ,EAClB1L,EAAM0L,QAAQ,CAAC,EAAE,EACjB1L,EAAM0L,QAAQ,CAAC,EAAE,CAACjC,OAAO,GACjC,OAAOH,EAAO,CACV5H,EAAG4H,EAAK5H,CAAC,CAAG4H,EAAK1H,KAAK,CAAG,EACzBD,EAAG2H,EAAK3H,CAAC,CAAG2H,EAAKzH,MAAM,CAAG,CAC9B,EAAI,CAAEH,EAAG,KAAMC,EAAG,IAAK,CAC3B,EACA+H,gBAAiBiC,EACjB1B,aAAcuB,EAAYzB,GAAG,CAACE,YAAY,AAC9C,EASAM,KAAM,CACFxG,WAAY,gBACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,MACZ4C,iBAAkB,SAAUrJ,CAAK,EAC7B,IAAIsJ,EAAQtJ,EAAM0L,QAAQ,EAClB1L,EAAM0L,QAAQ,CAAC,EAAE,EACjB1L,EAAM0L,QAAQ,CAAC,EAAE,CAACjC,OAAO,GACjC,OAAOH,EAAO,CACV5H,EAAG4H,EAAK5H,CAAC,CAAG4H,EAAK1H,KAAK,CAAG,EACzBD,EAAG2H,EAAK3H,CAAC,CAAG2H,EAAKzH,MAAM,CAAG,CAC9B,EAAI,CAAEH,EAAG,KAAMC,EAAG,IAAK,CAC3B,EACA+H,gBAAiBiC,EACjB1B,aAAcuB,EAAYjB,IAAI,CAACN,YAAY,AAC/C,CACJ,EAEI2B,EAAS,CACTjK,EAAG,CACCQ,KAAM,IACN4C,KAAM,CAAA,CACV,EASArD,EAAG,CACCqC,WAAY,cACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,OACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,OAAQ6L,EAAuB7L,EAAO,IAAO,EAClF0J,gBAAiBoC,EACjB7B,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAM+L,EAAE,AAAG,CACpE,EASAA,GAAI,CACAhI,WAAY,cACZ5B,KAAM,IACN4C,KAAM,CAAA,EACNqB,OAAQ,CAAA,EACRK,WAAY,QACZ4C,iBAAkB,SAAUrJ,CAAK,EAAI,OAAQ6L,EAAuB7L,EAAO,KAAQ,EACnF0J,gBAAiBoC,EACjB7B,aAAc,SAAU/H,CAAG,CAAElC,CAAK,EAAI,OAAQkC,GAAOlC,EAAM0B,CAAC,AAAG,CACnE,CACJ,EAwCA,SAASiK,EAAyB3L,CAAK,EACnC,IAAI2J,EAAS3J,EAAMwJ,OAAO,CAClBxJ,EAAMwJ,OAAO,CAACC,OAAO,GAAG7H,KAAK,CAAG,EAAI,EACpC,EACR,MAAO,CACH,CAAC,IAAK,EAAI+H,EAAQ,EAAE,CACpB,CAAC,IAAKA,EAAQA,EAAQ,EAAG,EAAG,EAAGA,AAAS,EAATA,EAAY,EAAE,CAC7C,CAAC,IAAKA,EAAQA,EAAQ,EAAG,EAAG,EAAGA,AAAS,GAATA,EAAa,EAAE,CACjD,AACL,CAKA,SAASmC,EAAqB9L,CAAK,EAC/B,IAAIuJ,EAAYvJ,EAAMuJ,SAAS,EAAIvJ,EAAMwJ,OAAO,CAACC,OAAO,GACpD5J,EAAM0J,EAAUK,CAAC,EAAI,EACrB9J,EAASyJ,EAAU1H,MAAM,CAAGhC,EAC5BmM,EAAUzC,EAAU1H,MAAM,CAAG,EACjC,MAAO,CAEH,CAAC,IAAK,EAAGhC,EAAI,CACb,CAAC,IAAK,EAAGmM,EAAU,EAAE,CAErB,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAE,CACpC,CAAC,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGA,EAAU,EAAE,CAEpC,CAAC,IAAK,EAAGA,EAAU,EAAE,CACrB,CAAC,IAAK,EAAGlM,EAAO,CACnB,AACL,CAOA,SAAS+L,EAAuB7L,CAAK,CAAEiM,CAAK,EACxC,IAAI9L,EAASH,EAAMG,MAAM,CACrB8E,EAAQ9E,EAAO8E,KAAK,CACpBD,EAAQ7E,EAAO6E,KAAK,CACpB0B,EAAWvG,EAAOtD,KAAK,CAAC6J,QAAQ,CAChCwF,EAAU/L,EAAOgM,aAAa,CAAGhM,EAAOgM,aAAa,CAACC,MAAM,CACxD,CAACpM,EAAMuJ,SAAS,CAAC1H,MAAM,CAAG,EAG9B4D,EAAOR,EAAM1C,QAAQ,CAACvC,CAAK,CAACiM,EAAM,CAClC,CAAA,GACAvG,EAAOV,EAAMzC,QAAQ,CAACvC,EAAM2B,CAAC,CAC7B,CAAA,GAOJ,OALI+E,IACAjB,EAAOR,EAAMoH,GAAG,CAAG5G,EACnBC,EAAOV,EAAMqH,GAAG,CAAG3G,GAGhB,CACHhE,EAAGmE,KAAKqD,KAAK,CAACzD,GACd9D,EAAGkE,KAAKqD,KAAK,CAHjBxD,GAAQwG,EAIR,CACJ,CAoB6B,IAAII,EAdb,CAChBb,UAAWA,EACX3B,QAASA,EACTY,OAAQA,EACR/B,OAAQA,EACR6C,YAAaA,EACbf,SAAUA,EACV8B,MA7kBQ7D,EA8kBR8D,MAhHQ,CACR7K,EAAGiK,EAAOjK,CAAC,CASX8K,MAAOjE,EAAoBoD,EAAOlK,CAAC,CAAE,CACjCqC,WAAY,iBAEZ2I,uBAAwB,SAAU1M,CAAK,EAAI,MAAQ,CAACA,EAAM2M,SAAS,AAAG,CAC1E,GASAC,IAAKpE,EAAoBoD,EAAOG,EAAE,CAAE,CAChChI,WAAY,eAEZ2I,uBAAwB,SAAU1M,CAAK,EAAI,MAAQ,CAACA,EAAM2M,SAAS,AAAG,CAC1E,EACJ,EAqFIjE,KAAMA,EACNoC,KAAMA,EACNO,UAAWA,EACXO,OAAQA,CACZ,EAuBIiB,EAA2B,AAAC9Q,IAA+EC,QAAQ,CAAE8Q,EAAQ,AAAC/Q,IAA+E+Q,KAAK,CAAEC,EAA2B,AAAChR,IAA+EwM,QAAQ,CAAEyE,EAAwB,AAACjR,IAA+EsC,KAAK,CAqH1b,SAAS4O,EAASjN,CAAK,EACnB,IAAInD,EAAQmD,EAAMG,MAAM,EAAIH,EAAMG,MAAM,CAACtD,KAAK,CAC1CyE,EAAezE,GAASA,EAAMyE,YAAY,CAC1CzE,GACAA,EAAM2B,WAAW,EACjB,CAAE8C,CAAAA,GACGA,CAAAA,EAAaoB,UAAU,EACpBpB,EAAasB,sBAAsB,EACnCtB,EAAa4L,gBAAgB,GAAKlN,EAAMyC,EAAE,AAAD,CAAC,GAClD5F,EAAMmG,eAAe,EAE7B,CAiCA,SAASmK,IACL,IAAInN,EAAQ,IAAI,CAChB6H,WAAW,WACH7H,EAAMG,MAAM,EACZ8M,EAASjN,EAEjB,EAAG,GACP,CAWA,SAASoN,IACL,IAAIpN,EAAQ,IAAI,CAChB6H,WAAW,eA3CP1H,EACAtD,EACAyE,EACA+L,EAwCqB,OAzCrB/L,EAAezE,CADfA,EAAQsD,CADRA,EAASH,AA2C6BA,EA3CvBG,MAAM,GACHA,EAAOtD,KAAK,GACNA,EAAMyE,YAAY,CAC1C+L,EAAOxQ,GAASA,EAAMwQ,IAAI,EAAIxQ,EAAMwQ,IAAI,SACxCxQ,GACEyE,GACEA,EAAaoB,UAAU,EACvBpB,EAAasB,sBAAsB,EACtC/F,EAAM8K,mBAAmB,GAC1BxH,EAAO/D,OAAO,CAACkE,QAAQ,EACtB+M,IAGGxQ,EAAM2B,WAAW,EACjB3B,EAAMmG,eAAe,GAEzBhD,AA2BsCA,EA3BhC2G,eAAe,IA2ByB,EAAG,GACzD,CAKA,SAAS2G,IACL,IAAIzQ,EAAQ,IAAI,CAACsD,MAAM,CAACtD,KAAK,CACzB2B,EAAc3B,EAAM2B,WAAW,CAC/BA,GAAeA,EAAYwB,KAAK,GAAK,IAAI,CAACyC,EAAE,EAC5C5F,EAAMmG,eAAe,EAE7B,CA4EA,SAASuK,GAAmBhM,CAAM,CAAEyE,CAAM,CAAElB,CAAW,EAoGnD,IAAK,IAlGD3E,EAASH,AADD,IAAI,CACGG,MAAM,CACrBtD,EAAQsD,EAAOtD,KAAK,CACpBuF,EAAUvF,EAAMuF,OAAO,CACvBhG,EAAU4Q,EAAsB7M,EAAO/D,OAAO,CAACkE,QAAQ,CACvDN,AALQ,IAAI,CAKN5D,OAAO,CAACkE,QAAQ,EACtBkN,EAAS,CAAC,EACVC,EAAclM,EAAOf,MAAM,CAACR,AAPpB,IAAI,CAOsByC,EAAE,CAAC,CACrCiL,EAAmBvS,AAAoC,IAApCA,OAAO4C,IAAI,CAAC+G,GAAapI,MAAM,CA2F7CF,EAAK,EAAGM,EAAK3B,OAAO4C,IAAI,CAAC+G,GAActI,EAAKM,EAAGJ,MAAM,CAAEF,IAAM,CAClE,IAAIvB,EAAM6B,CAAE,CAACN,EAAG,CACZ0F,EAAM4C,CAAW,CAAC7J,EAAI,CACtB0S,EAASF,EAAYzN,KAAK,CAAC/E,EAAI,CAC/BkH,EAAOhC,CAAM,CAAC+B,EAAIC,IAAI,CAAG,OAAO,CAChCyL,EAASxL,EACLyL,AA5DU,SAAU7H,CAAM,CAClC8H,CAAG,CACH7S,CAAG,EACC,IAAI6B,EACRmF,EACA8L,EACAC,EACAC,EACA,GAAI7L,EAAS,CACT,IAAI8L,EAAYJ,EAAIK,WAAW,GAC3BC,EAAY,AAAsD,OAArDtR,CAAAA,EAAKV,CAAO,CAAC,gBAAgBiS,MAAM,CAACH,GAAW,AAAD,GAAepR,AAAO,KAAK,IAAZA,EAAgBA,EAAK,EAC/FwR,EAAYlM,EAAQmM,cAAc,CAAC,CAC/B7M,EAAG,EACHC,EAAG,CACP,GACA6M,EAAYpM,EAAQmM,cAAc,CAAC,CAC/B7M,EAAG7E,EAAM4R,OAAO,CAAC7M,KAAK,CACtBD,EAAG9E,EAAM4R,OAAO,CAAC5M,MAAM,AAC3B,GACA6M,EAAM,AAA6J,OAA5JX,CAAAA,EAAK,AAAgD,OAA/C9L,CAAAA,EAAK7F,CAAO,CAAC,UAAUiS,MAAM,CAACH,GAAW,AAAD,GAAejM,AAAO,KAAK,IAAZA,EAAgBA,EAAKqM,MAAAA,EAA6C,KAAK,EAAIA,CAAS,CAACrT,EAAI,AAAD,GAAe8S,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACY,IAAUzH,EAAM,AAA6J,OAA5J+G,CAAAA,EAAK,AAAgD,OAA/CD,CAAAA,EAAK5R,CAAO,CAAC,UAAUiS,MAAM,CAACH,GAAW,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgBA,EAAKQ,MAAAA,EAA6C,KAAK,EAAIA,CAAS,CAACvT,EAAI,AAAD,GAAegT,AAAO,KAAK,IAAZA,EAAgBA,EAAKU,IAAUlN,EAAMuE,CAAM,CAAC/K,EAAI,CAC1a,GAAImH,AAAoC,iBAApCA,EAAQwM,UAAU,CAACxS,OAAO,CAACyS,IAAI,CAC/B,OAAOpN,EAEX,GAAIxG,AAAQ,QAARA,EAAe,CAEX6T,CAAAA,MAAMJ,IAAQA,EAAMtM,EAAQwM,UAAU,CAACG,WAAW,AAAD,GACjDL,CAAAA,EAAMtM,EAAQwM,UAAU,CAACG,WAAW,AAAD,EAEnCD,CAAAA,MAAM5H,IAAQA,EAAM,GAAK9E,EAAQwM,UAAU,CAACG,WAAW,AAAD,GACtD7H,CAAAA,EAAM,GAAK9E,EAAQwM,UAAU,CAACG,WAAW,AAAD,EAG5C,IAAIC,EAAO9H,EACXA,EAAMwH,EACNA,EAAMM,CACV,CACA,GAAI,CAAC5M,EAAQwM,UAAU,CAACK,cAAc,CAAE,CAEpC,IAAIC,EAAY9M,EAAQmM,cAAc,CAAC,CAC/B7M,EAAGsE,EAAOjJ,MAAM,CAAGF,EAAMsS,QAAQ,CACjCxN,EAAG9E,EAAMuS,UAAU,CAAGpJ,EAAOhJ,MAAM,CAAGH,EAAMwS,OAAO,AACvD,GACAH,GACAzN,CAAAA,EAAMyN,CAAS,CAACjU,EAAI,AAAD,CAE3B,CAIA,OAHImT,GACA3M,CAAAA,EAAMoE,KAAKqD,KAAK,CAACzH,EAAM2M,GAAaA,CAAQ,EAEzCtB,EAAMrL,EAAKiN,EAAKxH,EAC3B,CACJ,EAS4BlB,EACpB9D,EAAIC,IAAI,CACRlH,GACIqU,AAxFO,SAAUpN,CAAG,CAC5B4L,CAAG,EAIH,IAHQhR,EACRmF,EACA8L,EACIG,EAAYJ,EAAIK,WAAW,GAAIoB,EAAOpP,EAAOtD,KAAK,CAAC0S,IAAI,CAAEC,EAAmBrP,GAAAA,CAAM,CAAC,GAAK2N,EAAM,OAAO,CAAC2B,UAAU,CAAUrB,EAAY,AAAsD,OAArDtR,CAAAA,EAAKV,CAAO,CAAC,gBAAgBiS,MAAM,CAACH,GAAW,AAAD,GAAepR,AAAO,KAAK,IAAZA,EAAgBA,EAAK0S,EAAkBd,EAAM,AAA4D,OAA3DzM,CAAAA,EAAKsN,EAAKG,KAAK,CAACtT,CAAO,CAAC,UAAUiS,MAAM,CAACH,GAAW,CAAA,GAAejM,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAAC0M,IAAUzH,EAAM,AAA4D,OAA3D6G,CAAAA,EAAKwB,EAAKG,KAAK,CAACtT,CAAO,CAAC,UAAUiS,MAAM,CAACH,GAAW,CAAA,GAAeH,AAAO,KAAK,IAAZA,EAAgBA,EAAKY,IACpblN,EAAMS,EAIV,OAHIkM,GACA3M,CAAAA,EAAMoE,KAAKqD,KAAK,CAACzH,EAAM2M,GAAaA,CAAQ,EAEzCtB,EAAMrL,EAAKiN,EAAKxH,EAC3B,EA6EyB/E,EAAKwN,OAAO,CAAC,AAACxN,CAAAA,EAAKK,KAAK,CAAGwD,EAAOjJ,MAAM,CAAGiJ,EAAOhJ,MAAM,AAAD,EAChEyQ,CAAW,CAACxS,EAAM,SAAS,EACnCiH,EAAIC,IAAI,EAGR4K,EAAyBa,IACzB,CAAEF,CAAAA,GACExL,EAAI+H,YAAY,EAChB,CAAC/H,EAAI+H,YAAY,CAAC2D,EApHlB,IAAI,CAoH2B,GACnC,AAAkB,KAAA,IAAXD,GACPH,CAAAA,CAAM,CAACvS,EAAI,CAAG2S,CAAK,CAE3B,CACA,OAAOJ,CACX,CAQA,SAASoC,KAiGL,IAAK,IAhGD5P,EAAQ,IAAI,CACZG,EAASH,EAAMG,MAAM,CACrBtD,EAAQsD,EAAOtD,KAAK,CACpB6J,EAAW7J,EAAM6J,QAAQ,CACzBmJ,EAAWhT,EAAMgT,QAAQ,CACzBzT,EAAU4Q,EAAsB7M,EAAO/D,OAAO,CAACkE,QAAQ,CACvDN,EAAM5D,OAAO,CAACkE,QAAQ,EACtByB,EAAgB5B,EAAO4B,aAAa,EAAI,CAAC,EACzCvD,EAAc3B,EAAM2B,WAAW,CAC/BsR,EAAU,SAAU7U,CAAG,EACnB,IAAIiH,EAAMH,CAAa,CAAC9G,EAAI,CAChC8U,EAAgB/C,EAAsB7P,EAAiCE,UAAU,CACjF6E,EAAI6N,aAAa,CACjB3T,EAAQiB,UAAU,EAClB2S,EAAc,CACN,MAASD,EAAczS,SAAS,CAChC,eAAgByS,EAActS,SAAS,CACvCyB,KAAM6Q,EAAcxS,KAAK,CACzByB,OAAQ+Q,EAAcvS,SAAS,AACnC,EACJyS,EAAgBF,EAAcE,aAAa,EAAI/N,EAAIwH,eAAe,CAClEL,EAAmBnH,EAAImH,gBAAgB,CAGnC6G,EAAWhO,CAAAA,EAAIwK,sBAAsB,EACjCxK,EAAIwK,sBAAsB,CAAC1M,GAC/BmQ,EAAM,KAAK,EACXC,EAAS,KAAK,EACdC,EAAO,KAAK,EAChB,GAAInO,EAAIkE,MAAM,EACV8J,GACAhO,EAAIuE,UAAU,EACdwJ,GACC7T,CAAAA,CAAO,CAAC,YAAc8F,EAAIC,IAAI,CAACgM,WAAW,GAAG,EAC1C/R,CAAO,CAAC8F,EAAI6B,UAAU,CAAC,AAAD,GAC1B3H,AAA4B,CAAA,IAA5BA,CAAO,CAAC8F,EAAI6B,UAAU,CAAC,CAAY,CAE9BvF,EAUDA,EAAYwB,KAAK,CAAGA,EAAMyC,EAAE,CAT5BjE,EAAc3B,EAAM2B,WAAW,CAAG,CAC9B6C,MAAOwO,EACFS,CAAC,CAAC,qBACFlP,GAAG,CAACjB,EAAOoQ,WAAW,EAAIpQ,EAAOkB,KAAK,EAC3CrB,MAAOA,EAAMyC,EAAE,AACnB,EAOJ0N,EAAM9G,EAAiBrJ,GACvBgQ,EAAYlV,CAAC,CAAGuV,EAAOJ,EAAcjQ,GAErC,IAAIwQ,EAAUxQ,EAAMG,MAAM,CAAC8E,KAAK,CAACwK,UAAU,CAAG,IAAO,EACrD,GAAI,CAACY,GAAQF,EAAIzO,CAAC,CAAG8O,GAAWL,EAAIxO,CAAC,CAAG,EACpC,MAAO,CAAE8O,MAAO,KAAK,CAAE,CAG3BT,CAAAA,EAAYpS,MAAM,CAAGmS,EAAcnS,MAAM,EACpC,CAAA,AAAc,MAAbsE,EAAIC,IAAI,EAAc,CAAC,CAACuE,EACtB,YAAc,WAAU,EAEhC0J,CAAAA,EAAS5R,CAAW,CAAC0D,EAAI6B,UAAU,CAAC,AAAD,GAE/BqM,CAAAA,EAAS5R,CAAW,CAAC0D,EAAI6B,UAAU,CAAC,CAAG8L,EAClCQ,IAAI,GACJjP,GAAG,CAAC5C,EAAY6C,KAAK,CAAA,EAG9B2O,EAAYU,UAAU,CAAGhK,EACrBvG,EAAO6E,KAAK,CAACqH,GAAG,CAAG8D,EAAIxO,CAAC,CACxBwO,EAAIzO,CAAC,CACTsO,EAAYW,UAAU,CAAGjK,EACrBvG,EAAO8E,KAAK,CAACoH,GAAG,CAAG8D,EAAIzO,CAAC,CACxByO,EAAIxO,CAAC,CACL+E,GACAsJ,CAAAA,EAAYY,QAAQ,CAAG,GAAE,EAE7BR,EAAOrR,IAAI,CAACiR,GAEZa,EAA0BT,EAAOU,OAAO,CAAE,CAAC,aAAc,YAAY,CAAE,SAAUlU,CAAC,MAjQ7DA,EAAGoD,EAAOkG,EACnCrJ,EADyBD,EAkQOmU,EAAmCnU,EAAGC,GAlQ1CmD,EAkQkDA,EAlQ3CkG,EAkQkDjL,EA/PrF4B,CAFAA,EAAQmD,EAAMG,MAAM,CAACtD,KAAK,EAEpB0H,mBAAmB,CAAC3H,KAI9BC,EAAMqI,WAAW,CAAG,CAAA,EAEpB8L,AAp4BcjR,EAo4BenD,EAAGoD,GAChCnD,EAAMyE,YAAY,CAAC4E,UAAU,CACzBtJ,EAAEsJ,UAAU,CAAGA,EACnBlG,EAAMmD,cAAc,CAAC,YAAavG,GAElCA,EAAEqU,eAAe,GACjBrU,EAAEqG,cAAc,GAoPR,EAAG,CACCsE,QAAS,CAAA,CACb,GACAsF,EAAyBrO,EAAY6C,KAAK,CAACyP,OAAO,CAAE,YAAa,WAC7DjU,EAAMyE,YAAY,CAAGzE,EAAMyE,YAAY,EAAI,CAAC,EAC5CzE,EAAMyE,YAAY,CAAC4L,gBAAgB,CAAGlN,EAAMyC,EAAE,AAClD,GACAoO,EAA0BrS,EAAY6C,KAAK,CAACyP,OAAO,CAAE,CAAC,WAAY,WAAW,CAAE,eA/RnFjU,CACAA,EADAA,EAAQmD,AAgSuBA,EAhSjBG,MAAM,CAACtD,KAAK,EACpByE,YAAY,EAClBtB,AA8R+BA,EA9RzByC,EAAE,GAAK5F,EAAMyE,YAAY,CAAC4L,gBAAgB,EAChD,OAAOrQ,EAAMyE,YAAY,CAAC4L,gBAAgB,CAEzCrQ,EAAMsH,UAAU,EACjB8I,EA0R+BjN,EAC3B,EACJ,CACJ,EAGSxD,EAAK,EAAGM,EAAK3B,OAAO4C,IAAI,CAACgE,GAAgBvF,EAAKM,EAAGJ,MAAM,CAAEF,IAAM,CAEpE,IAAI0U,EAAUpB,EADJhT,CAAE,CAACN,EAAG,EAEhB,GAAI,AAAmB,UAAnB,OAAO0U,EACP,OAAOA,EAAQT,KAAK,AAC5B,CACJ,CAaA,SAASU,GAAkB3Q,CAAM,EAQ7B,IAAK,IAFD4Q,EALAvU,EAAQ,IAAI,CAACA,KAAK,CAClBwU,EAAO1C,IACP2C,EAAO,CAAC3C,IACR4C,EAAO5C,IACP6C,EAAO,CAAC7C,IAGHnS,EAAK,EAAsBA,EAAKsF,AAAbtB,EAAsB9D,MAAM,CAAEF,IAAM,CAC5D,IAAIwD,EAAQ8B,AADYtB,CACJ,CAAChE,EAAG,CACpB8M,EAAQtJ,EAAMwJ,OAAO,EAAIxJ,EAAMwJ,OAAO,CAACC,OAAO,IAAMzJ,EAAMuJ,SAAS,CACvE,GAAID,EAAM,CACN,IAAImI,EAAS,KAAK,EACd1F,EAAK/L,EAAM+L,EAAE,CACbgB,EAAyBhB,IACzB0F,CAAAA,EAASzR,EAAMG,MAAM,CAAC8E,KAAK,CAACqC,SAAS,CAACyE,EAAI,CAAA,EAAO,CAAA,EAAO,CAAA,EAAO,CAAA,EAAI,EAIvE,IAAI2F,EAAW,CAAEpI,CAAAA,EAAK1H,KAAK,EAAI0H,EAAKzH,MAAM,EAAIyH,EAAK5H,CAAC,EAAI4H,EAAK3H,CAAC,AAADA,EAC7DyP,EAAU,CAAA,EACVC,EAAOxL,KAAK6I,GAAG,CAAC1O,EAAMqC,KAAK,EAAI,EAAGoP,GAAU,EAAGC,EAAW/C,IAAWrF,EAAK5H,CAAC,EAAI,EAAG2P,GAClFC,EAAOzL,KAAKqB,GAAG,CAAClH,EAAMqC,KAAK,EAAI,EAAGoP,GAAU,EAAG,AAACnI,CAAAA,EAAK5H,CAAC,EAAI,CAAA,EAAM4H,CAAAA,EAAK1H,KAAK,EAAI,CAAA,EAAI0P,GAClFC,EAAO1L,KAAK6I,GAAG,CAAC1O,EAAMsC,KAAK,EAAI,EAAGoP,EAAW/C,IAAWrF,EAAK3H,CAAC,EAAI,EAAG4P,GACrEC,EAAO3L,KAAKqB,GAAG,CAAC,AAACoC,CAAAA,EAAK3H,CAAC,EAAI,CAAA,EAAM2H,CAAAA,EAAKzH,MAAM,EAAI,CAAA,EAAI2P,EACxD,CACJ,CACA,OAAOJ,EAAUvU,EAAMgT,QAAQ,CAAC/I,IAAI,CAACuK,EAAME,EAAMD,EAAOD,EAAMG,EAAOD,GAAQ1U,EAAMgT,QAAQ,CAACS,CAAC,EACjG,CAkLA,IAAIqB,GAAK5V,IACT6V,AA7KsB,CAAA,CAClBC,QAtfJ,SAAiC9J,CAAU,CAAE+J,CAAW,EACpDC,EAAuChK,GACvC,IAAIiK,EAAcF,EAAYrW,SAAS,CACvC,GAAI,CAACuW,EAAYjQ,aAAa,CAAE,CAC5B,IAAIkQ,EAAaH,EAAYrW,SAAS,CAACoF,UAAU,CAC7CqR,EAAcJ,EAAY5V,KAAK,CAC/BiW,EAAaF,EAAWxW,SAAS,AACrC0W,CAAAA,EAAW9L,aAAa,CAAGkH,GAC3B4E,EAAWxL,eAAe,CAAGiJ,GAC7B/C,EAAyBoF,EAAY,WAAY9E,GACjDN,EAAyBoF,EAAY,YAAa7E,GAClDP,EAAyBoF,EAAY,SAAU3E,GAC/C0E,EAAYjQ,aAAa,CAAGuK,EAA8B5D,IAAI,CAC9DsJ,EAAY9Q,WAAW,CAAGiQ,GAe1B,IAAK,IAAI3U,EAAK,EAAG4V,EAba,CACtB,YACA,UACA,SACA,SACA,cACA,WACA,QACA,QACA,OACA,YACA,SACH,CACiE5V,EAAK4V,EAA0B1V,MAAM,CAAEF,IAAM,CAC/G,IAAI6V,EAAaD,CAAyB,CAAC5V,EAAG,AAC1C0V,CAAAA,CAAW,CAACG,EAAW,EACvBH,CAAAA,CAAW,CAACG,EAAW,CAAC5W,SAAS,CAACsG,aAAa,CAC3CuK,CAA6B,CAAC+F,EAAW,AAAD,CAEpD,CAkBA,IAAK,IAAIvV,EAAK,EAAGwV,EAhBgB,CACzB,YACA,QACA,YACA,MACA,UACA,SACA,MACA,SACA,MACA,WACA,UACA,SACA,WACA,YACH,CACuExV,EAAKwV,EAA6B5V,MAAM,CAAEI,IAAM,CACxH,IAAIuV,EAAaC,CAA4B,CAACxV,EAAG,AAC7CoV,CAAAA,CAAW,CAACG,EAAW,EACvBH,CAAAA,CAAW,CAACG,EAAW,CAAC5W,SAAS,CAACsG,aAAa,CAAG,IAAG,CAE7D,CACJ,CACJ,CA4bA,CAAA,EA2KgC8P,OAAO,CAACF,GAAEY,KAAK,CAAEZ,GAAEa,MAAM,EAC5B,IAAI3W,GAAyBE,IAGhD,OADYH,EAAoB,OAAU,AAE3C,GAET"}