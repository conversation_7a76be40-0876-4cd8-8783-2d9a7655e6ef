!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("highcharts"),require("highcharts").Templating):"function"==typeof define&&define.amd?define("highcharts/modules/sonification",[["highcharts/highcharts"],["highcharts/highcharts","Templating"]],t):"object"==typeof exports?exports["highcharts/modules/sonification"]=t(require("highcharts"),require("highcharts").Templating):e.Highcharts=t(e.Highcharts,e.Highcharts.Templating)}(this,function(e,t){return function(){"use strict";var i={944:function(t){t.exports=e},984:function(e){e.exports=t}},n={};function o(e){var t=n[e];if(void 0!==t)return t.exports;var s=n[e]={exports:{}};return i[e](s,s.exports,o),s.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var i in t)o.o(t,i)&&!o.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var s={};o.d(s,{default:function(){return eq}});var a=o(944),r=o.n(a),l=function(){return(l=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var o in t=arguments[i])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},c=r().clamp,u=r().defined,h=r().pick;function p(e,t){return(.2414*e-.2414)*Math.log(t)+(3.5-1.7*e)/1.8}function m(e,t,i){e.gain.cancelScheduledValues(t),e.gain.setTargetAtTime(i,t,v.stopRampTime/4),e.gain.setValueAtTime(i,t+v.stopRampTime)}function f(e,t,i,n,o){void 0===o&&(o=1);var s="attack"===t,a=n.gain;if(a.cancelScheduledValues(i),!e.length){m(n,i,s?o:0);return}e[0].t>1&&e.unshift({t:0,vol:+!s}),e.forEach(function(t,n){var s=e[n-1],r=s?(t.t-s.t)/1e3:0,l=i+(s?s.t/1e3+v.stopRampTime:0);a.setTargetAtTime(t.vol*o,l,Math.max(r,v.stopRampTime)/2)})}var d=function(){function e(e,t){this.pulseWidth=Math.min(Math.max(0,t.pulseWidth||.5));var i=function(){return new OscillatorNode(e,{type:"sawtooth",detune:t.detune,frequency:Math.max(1,t.frequency||350)})};this.sawOscA=i(),this.sawOscB=i(),this.phaseInverter=new GainNode(e,{gain:-1}),this.masterGain=new GainNode(e),this.delayNode=new DelayNode(e,{delayTime:this.pulseWidth/this.sawOscA.frequency.value}),this.sawOscA.connect(this.masterGain),this.sawOscB.connect(this.phaseInverter),this.phaseInverter.connect(this.delayNode),this.delayNode.connect(this.masterGain)}return e.prototype.connect=function(e){this.masterGain.connect(e)},e.prototype.getFrequencyFacade=function(){var e=this;return{cancelScheduledValues:function(t){return e.sawOscA.frequency.cancelScheduledValues(t),e.sawOscB.frequency.cancelScheduledValues(t),e.delayNode.delayTime.cancelScheduledValues(t),e.sawOscA.frequency},setValueAtTime:function(t,i){return this.cancelScheduledValues(i),e.sawOscA.frequency.setValueAtTime(t,i),e.sawOscB.frequency.setValueAtTime(t,i),e.delayNode.delayTime.setValueAtTime(Math.round(1e4*e.pulseWidth/t)/1e4,i),e.sawOscA.frequency},setTargetAtTime:function(t,i,n){return this.cancelScheduledValues(i),e.sawOscA.frequency.setTargetAtTime(t,i,n),e.sawOscB.frequency.setTargetAtTime(t,i,n),e.delayNode.delayTime.setTargetAtTime(Math.round(1e4*e.pulseWidth/t)/1e4,i,n),e.sawOscA.frequency}}},e.prototype.getPWMTarget=function(){return this.delayNode.delayTime},e.prototype.start=function(){this.sawOscA.start(),this.sawOscB.start()},e.prototype.stop=function(e){this.sawOscA.stop(e),this.sawOscB.stop(e)},e}(),y=function(){function e(e,t,i){this.audioContext=e,this.options=t,this.fmOscillatorIx=t.fmOscillator,this.vmOscillatorIx=t.vmOscillator,this.createSoundSource(),this.createGain(),this.createFilters(),this.createVolTracking(),i&&this.connect(i)}return e.prototype.connect=function(e){[this.lowpassNode,this.highpassNode,this.volTrackingNode,this.vmNode,this.gainNode,this.whiteNoise,this.pulseNode,this.oscNode].reduce(function(e,t){return t?(t.connect(e),t):e},e)},e.prototype.start=function(){this.oscNode&&this.oscNode.start(),this.whiteNoise&&this.whiteNoise.start(),this.pulseNode&&this.pulseNode.start()},e.prototype.stopAtTime=function(e){this.oscNode&&this.oscNode.stop(e),this.whiteNoise&&this.whiteNoise.stop(e),this.pulseNode&&this.pulseNode.stop(e)},e.prototype.setFreqAtTime=function(e,t,i){void 0===i&&(i=0);var n=this.options,o=c(h(n.fixedFrequency,t)*(n.freqMultiplier||1),0,21e3),s=this.getOscTarget(),a=i/5e3;s&&(s.cancelScheduledValues(e),i&&e-(this.lastUpdateTime||-1)>.01?(s.setTargetAtTime(o,e,a),s.setValueAtTime(o,e+a)):s.setValueAtTime(o,e)),this.scheduleVolTrackingChange(o,e,i),this.scheduleFilterTrackingChange(o,e,i),this.lastUpdateTime=e},e.prototype.getFMTarget=function(){return this.oscNode&&this.oscNode.detune||this.whiteNoise&&this.whiteNoise.detune||this.pulseNode&&this.pulseNode.getPWMTarget()},e.prototype.getVMTarget=function(){return this.vmNode&&this.vmNode.gain},e.prototype.runEnvelopeAtTime=function(e,t){this.gainNode&&f(("attack"===e?this.options.attackEnvelope:this.options.releaseEnvelope)||[],e,t,this.gainNode,this.options.volume)},e.prototype.cancelScheduled=function(){this.gainNode&&this.gainNode.gain.cancelScheduledValues(this.audioContext.currentTime);var e=this.getOscTarget();e&&e.cancelScheduledValues(0),this.lowpassNode&&this.lowpassNode.frequency.cancelScheduledValues(0),this.highpassNode&&this.highpassNode.frequency.cancelScheduledValues(0),this.volTrackingNode&&this.volTrackingNode.gain.cancelScheduledValues(0)},e.prototype.scheduleVolTrackingChange=function(e,t,i){if(this.volTrackingNode){var n=p(this.options.volumePitchTrackingMultiplier||1,e),o=i?i/1e3:v.stopRampTime;this.volTrackingNode.gain.cancelScheduledValues(t),this.volTrackingNode.gain.setTargetAtTime(n,t,o/5),this.volTrackingNode.gain.setValueAtTime(n,t+o)}},e.prototype.scheduleFilterTrackingChange=function(e,t,i){var n=this.options,o=i?i/1e3:v.stopRampTime,s=function(i,n){var s=p(n.frequencyPitchTrackingMultiplier||1,e),a=c((n.frequency||1e3)*s,0,21e3);i.frequency.cancelScheduledValues(t),i.frequency.setTargetAtTime(a,t,o/5),i.frequency.setValueAtTime(a,t+o)};this.lowpassNode&&n.lowpass&&s(this.lowpassNode,n.lowpass),this.highpassNode&&n.highpass&&s(this.highpassNode,n.highpass)},e.prototype.createGain=function(){var e=this.options;(u(e.volume)||e.attackEnvelope&&e.attackEnvelope.length||e.releaseEnvelope&&e.releaseEnvelope.length)&&(this.gainNode=new GainNode(this.audioContext,{gain:h(e.volume,1)})),this.vmNode=new GainNode(this.audioContext)},e.prototype.createSoundSource=function(){var e=this.options,t=this.audioContext,i=(e.fixedFrequency||0)*(e.freqMultiplier||1);if("whitenoise"===e.type){for(var n=2*t.sampleRate,o=t.createBuffer(1,n,t.sampleRate),s=o.getChannelData(0),a=0;a<n;++a)s[a]=1.2*Math.random()-.6;var r=this.whiteNoise=t.createBufferSource();r.buffer=o,r.loop=!0}else"pulse"===e.type?this.pulseNode=new d(t,{detune:e.detune,pulseWidth:e.pulseWidth,frequency:i}):this.oscNode=new OscillatorNode(t,{type:e.type||"sine",detune:e.detune,frequency:i})},e.prototype.createFilters=function(){var e=this.options;e.lowpass&&e.lowpass.frequency&&(this.lowpassNode=new BiquadFilterNode(this.audioContext,{type:"lowpass",Q:e.lowpass.Q||1,frequency:e.lowpass.frequency})),e.highpass&&e.highpass.frequency&&(this.highpassNode=new BiquadFilterNode(this.audioContext,{type:"highpass",Q:e.highpass.Q||1,frequency:e.highpass.frequency}))},e.prototype.createVolTracking=function(){var e=this.options;e.volumePitchTrackingMultiplier&&1!==e.volumePitchTrackingMultiplier&&(this.volTrackingNode=new GainNode(this.audioContext,{gain:1}))},e.prototype.getOscTarget=function(){return this.oscNode?this.oscNode.frequency:this.pulseNode&&this.pulseNode.getFrequencyFacade()},e}(),v=function(){function e(e,t){var i=this;this.audioContext=e,this.options=t,this.eqNodes=[],this.midiInstrument=t.midiInstrument||1,this.outputNode=new GainNode(e,{gain:0}),this.createEqChain(this.outputNode);var n=this.eqNodes.length?this.eqNodes[0]:this.outputNode;this.oscillators=(this.options.oscillators||[]).map(function(t){return new y(e,t,u(t.fmOscillator)||u(t.vmOscillator)?void 0:n)}),this.oscillators.forEach(function(e){var t=function(t,i){if(i){var n=i[t]();n&&e.connect(n)}};u(e.fmOscillatorIx)&&t("getFMTarget",i.oscillators[e.fmOscillatorIx]),u(e.vmOscillatorIx)&&t("getVMTarget",i.oscillators[e.vmOscillatorIx])})}return e.prototype.startSilently=function(){this.outputNode.gain.value=0,this.oscillators.forEach(function(e){return e.start()})},e.prototype.stop=function(){var t=this.audioContext.currentTime,i=t+e.stopRampTime;m(this.outputNode,t,0),this.oscillators.forEach(function(e){return e.stopAtTime(i)}),this.outputNode.disconnect()},e.prototype.silenceAtTime=function(e){if(!e&&this.outputNode.gain.value<.01){this.outputNode.gain.value=0;return}this.releaseAtTime((e||0)+this.audioContext.currentTime)},e.prototype.mute=function(){this.cancelScheduled(),m(this.outputNode,this.audioContext.currentTime,0)},e.prototype.playFreqAtTime=function(e,t,i){var n=(e||0)+this.audioContext.currentTime,o=this.options;this.oscillators.forEach(function(e){e.setFreqAtTime(n,t,o.noteGlideDuration),e.runEnvelopeAtTime("attack",n)}),f(o.masterAttackEnvelope||[],"attack",n,this.outputNode,o.masterVolume),i&&this.releaseAtTime(n+i/1e3)},e.prototype.cancelScheduled=function(){this.outputNode.gain.cancelScheduledValues(this.audioContext.currentTime),this.oscillators.forEach(function(e){return e.cancelScheduled()})},e.prototype.connect=function(e){return this.outputNode.connect(e)},e.prototype.createEqChain=function(e){var t=this;this.eqNodes=(this.options.eq||[]).map(function(e){return new BiquadFilterNode(t.audioContext,l({type:"peaking"},e))}),this.eqNodes.reduceRight(function(e,t){return t.connect(e),t},e)},e.prototype.releaseAtTime=function(e){var t=0;this.oscillators.forEach(function(i){var n=i.options.releaseEnvelope;n&&n.length&&(t=Math.max(t,n[n.length-1].t),i.runEnvelopeAtTime("release",e))});var i=this.options.masterReleaseEnvelope||[];i.length&&(f(i,"release",e,this.outputNode,this.options.masterVolume),t=Math.max(t,i[i.length-1].t)),m(this.outputNode,e+t/1e3,0)},e.stopRampTime=.012,e}(),g={piano:{masterVolume:.45,masterAttackEnvelope:[{t:1,vol:.71},{t:40,vol:.79},{t:82,vol:.64},{t:147,vol:.29},{t:260,vol:.15},{t:417,vol:.05},{t:589,vol:0}],eq:[{frequency:200,Q:.7,gain:6},{frequency:450,gain:6},{frequency:1300,gain:2},{frequency:2600,Q:.8,gain:8},{frequency:3500,Q:.8,gain:6},{frequency:6200,Q:.8,gain:10},{frequency:8e3,gain:-23},{frequency:1e4,Q:.4,gain:-12}],oscillators:[{type:"pulse",volume:.5,pulseWidth:.55,volumePitchTrackingMultiplier:.1,lowpass:{frequency:4.5,frequencyPitchTrackingMultiplier:900,Q:-2},highpass:{frequency:270},attackEnvelope:[{t:1,vol:1}],releaseEnvelope:[{t:1,vol:1},{t:282,vol:.64},{t:597,vol:0}]},{type:"whitenoise",volume:.8,lowpass:{frequency:400},highpass:{frequency:300},attackEnvelope:[{t:1,vol:1},{t:19,vol:0}]}]},plucked:{masterVolume:.5,midiInstrument:25,masterAttackEnvelope:[{t:1,vol:.71},{t:4,vol:.71},{t:31,vol:.4},{t:109,vol:.12},{t:234,vol:.04},{t:442,vol:0}],eq:[{frequency:800,gain:-8},{frequency:1400,Q:4,gain:4},{frequency:1600,gain:-14},{frequency:2200,gain:-8},{frequency:3600,gain:-2},{frequency:6400,Q:2,gain:-6}],oscillators:[{type:"sawtooth",volume:.9,volumePitchTrackingMultiplier:.6,highpass:{frequency:100},lowpass:{frequency:8e3},releaseEnvelope:[{t:1,vol:1},{t:315,vol:.56},{t:550,vol:0}]}]},flute:{masterVolume:1.1,midiInstrument:74,noteGlideDuration:30,masterAttackEnvelope:[{t:0,vol:0},{t:29,vol:1},{t:76,vol:.48},{t:600,vol:.36}],masterReleaseEnvelope:[{t:1,vol:.36},{t:24,vol:.15},{t:119,vol:0}],eq:[{frequency:150,Q:.6,gain:-10},{frequency:500,gain:4},{frequency:1100,gain:-4},{frequency:2200,gain:-14},{frequency:5e3,gain:8},{frequency:6400,gain:10},{frequency:8e3,gain:12},{frequency:10800,gain:8}],oscillators:[{type:"triangle",volume:1,volumePitchTrackingMultiplier:.4,lowpass:{frequency:12,frequencyPitchTrackingMultiplier:100},highpass:{frequency:200}},{type:"sine",fixedFrequency:5,volume:.2,vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:48,vol:0},{t:225,vol:.05},{t:600,vol:.77}]},{type:"whitenoise",volume:.13,lowpass:{frequency:9e3,Q:3},highpass:{frequency:6e3,Q:3},vmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:26,vol:1},{t:93,vol:.8}]}]},lead:{masterVolume:1,midiInstrument:20,masterAttackEnvelope:[{t:1,vol:.81},{t:98,vol:.5},{t:201,vol:.18},{t:377,vol:.04},{t:586,vol:0},{t:586,vol:0}],eq:[{frequency:200,gain:-6},{frequency:400,gain:-8},{frequency:800,Q:.5,gain:-10},{frequency:1200,gain:4},{frequency:3600,gain:-4},{frequency:4200,gain:-12},{frequency:7400,gain:-14},{frequency:1e4,gain:2}],oscillators:[{type:"triangle",volume:1.1,volumePitchTrackingMultiplier:.6,lowpass:{frequency:5e3},highpass:{frequency:100}},{type:"sawtooth",volume:.4,lowpass:{frequency:7e3},highpass:{frequency:800,Q:6},releaseEnvelope:[{t:0,vol:.99},{t:200,vol:.83},{t:495,vol:0}]}]},vibraphone:{masterVolume:1,midiInstrument:12,masterAttackEnvelope:[{t:1,vol:0},{t:10,vol:.63},{t:82,vol:.64},{t:149,vol:.26},{t:600,vol:0}],eq:[{frequency:200,Q:.8,gain:-12},{frequency:400,gain:-4},{frequency:1600,Q:.5,gain:6},{frequency:2200,Q:.5,gain:6},{frequency:6400,gain:4},{frequency:12800,gain:4}],oscillators:[{type:"sine",volume:1.5,volumePitchTrackingMultiplier:1e-7,attackEnvelope:[{t:1,vol:1}],releaseEnvelope:[{t:1,vol:1},{t:146,vol:.39},{t:597,vol:0}]},{type:"whitenoise",volume:.03,volumePitchTrackingMultiplier:1e-4,lowpass:{frequency:900},highpass:{frequency:800},attackEnvelope:[{t:1,vol:1},{t:9,vol:0}]},{type:"sine",freqMultiplier:4,volume:.15,volumePitchTrackingMultiplier:1e-4},{type:"sine",fixedFrequency:3,volume:6,fmOscillator:0,releaseEnvelope:[{t:1,vol:1},{t:190,vol:.41},{t:600,vol:0}]},{type:"sine",fixedFrequency:6,volume:3,fmOscillator:2},{type:"sine",freqMultiplier:9,volume:5e-4,volumePitchTrackingMultiplier:1e-4,releaseEnvelope:[{t:1,vol:.97},{t:530,vol:0}]}]},saxophone:{masterVolume:1,midiInstrument:67,noteGlideDuration:10,masterAttackEnvelope:[{t:1,vol:.57},{t:35,vol:1},{t:87,vol:.84},{t:111,vol:.6},{t:296,vol:.49},{t:600,vol:.58}],masterReleaseEnvelope:[{t:1,vol:.58},{t:47,vol:.16},{t:119,vol:0}],eq:[{frequency:200,gain:-2},{frequency:600,gain:2},{frequency:800,gain:-10},{frequency:1100,gain:-2},{frequency:2200,gain:-2},{frequency:3500,gain:10},{frequency:12800,gain:4}],oscillators:[{type:"sawtooth",volume:.45,volumePitchTrackingMultiplier:.06,lowpass:{frequency:18,frequencyPitchTrackingMultiplier:200},highpass:{frequency:300}},{type:"whitenoise",fixedFrequency:1,volume:.4,highpass:{frequency:7e3},vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:51,vol:1},{t:86,vol:.84},{t:500,vol:.78}]},{type:"sine",fixedFrequency:4,volume:2,fmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:15,vol:.94},{t:79,vol:1},{t:172,vol:.47},{t:500,vol:.26}]},{type:"sine",fixedFrequency:7,volume:6,fmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:25,vol:.99},{t:85,vol:0},{t:85,vol:0},{t:387,vol:.02},{t:511,vol:.43},{t:600,vol:0}]}]},trumpet:{masterVolume:.3,midiInstrument:57,noteGlideDuration:40,masterAttackEnvelope:[{t:1,vol:0},{t:17,vol:1},{t:42,vol:.85},{t:76,vol:1},{t:202,vol:.65},{t:226,vol:.86},{t:282,vol:.63}],masterReleaseEnvelope:[{t:1,vol:.62},{t:34,vol:.14},{t:63,vol:.21},{t:96,vol:0}],eq:[{frequency:200,Q:.6,gain:10},{frequency:600,Q:.5,gain:6},{frequency:1500,Q:.7,gain:14},{frequency:3200,Q:2,gain:8},{frequency:3800,Q:.8,gain:10},{frequency:6200,gain:12},{frequency:8400,gain:-20},{frequency:12800,Q:.5,gain:-18}],oscillators:[{type:"sawtooth",volume:.15,pulseWidth:.5,volumePitchTrackingMultiplier:.5,lowpass:{frequency:1900,Q:3}},{type:"sine",fixedFrequency:6,volume:.2,vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:102,vol:.13},{t:556,vol:.24}]},{type:"whitenoise",volume:.45,highpass:{frequency:7e3,Q:9},vmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:89,vol:.51},{t:577,vol:.29}]},{type:"sine",fixedFrequency:5.7,volume:20,fmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:89,vol:1},{t:137,vol:.46},{t:283,vol:.15},{t:600,vol:.28}]}]},sawsynth:{masterVolume:.3,midiInstrument:51,noteGlideDuration:40,masterAttackEnvelope:[{t:0,vol:.6},{t:9,vol:1},{t:102,vol:.48}],eq:[{frequency:200,gain:-6}],oscillators:[{type:"sawtooth",volume:.4,volumePitchTrackingMultiplier:.3},{type:"sawtooth",volume:.4,detune:11,volumePitchTrackingMultiplier:.3},{type:"sawtooth",volume:.4,detune:-11,volumePitchTrackingMultiplier:.3}]},basic1:{masterVolume:1,noteGlideDuration:0,masterReleaseEnvelope:[{t:1,vol:.36},{t:24,vol:.15},{t:119,vol:0}],eq:[{frequency:150,Q:.6,gain:-12},{frequency:1100,gain:-2},{frequency:2200,gain:-16},{frequency:5e3,gain:8},{frequency:6400,gain:10},{frequency:8e3,gain:12},{frequency:10800,gain:8}],oscillators:[{type:"triangle",volume:1,volumePitchTrackingMultiplier:.05,lowpass:{frequency:17,frequencyPitchTrackingMultiplier:100},highpass:{frequency:200}},{type:"whitenoise",volume:.04,lowpass:{frequency:9e3,Q:3},highpass:{frequency:6e3,Q:3},vmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:26,vol:1},{t:71,vol:.73}]}]},basic2:{masterVolume:.3,eq:[{frequency:200,Q:.7,gain:6},{frequency:450,gain:2},{frequency:1300,gain:-2},{frequency:2600,Q:.8,gain:6},{frequency:3500,Q:.8,gain:6},{frequency:6200,Q:.8,gain:10},{frequency:8e3,gain:-18},{frequency:1e4,Q:.4,gain:-12}],oscillators:[{type:"pulse",volume:.4,pulseWidth:.55,volumePitchTrackingMultiplier:.1,lowpass:{frequency:4.5,frequencyPitchTrackingMultiplier:900,Q:-2},highpass:{frequency:270}}]},chord:{masterVolume:1,masterAttackEnvelope:[{t:1,vol:.79},{t:27,vol:.86},{t:62,vol:.81},{t:150,vol:.35},{t:408,vol:.04},{t:600,vol:0}],eq:[{frequency:200,gain:-8},{frequency:600,Q:2,gain:4},{frequency:800,gain:-10},{frequency:1600,gain:-2},{frequency:2200,gain:-6},{frequency:3600,Q:.7,gain:-2},{frequency:6400,gain:6},{frequency:12800,gain:6}],oscillators:[{type:"triangle",volume:1.1,volumePitchTrackingMultiplier:.05,lowpass:{frequency:8e3},highpass:{frequency:100},releaseEnvelope:[{t:1,vol:1},{t:315,vol:.56},{t:540,vol:0}]},{type:"triangle",freqMultiplier:1.17,volume:.4,volumePitchTrackingMultiplier:.07,lowpass:{frequency:5e3},highpass:{frequency:100},releaseEnvelope:[{t:0,vol:1},{t:476,vol:0}]},{type:"triangle",freqMultiplier:1.58333,volume:.7,volumePitchTrackingMultiplier:.02,highpass:{frequency:200},releaseEnvelope:[{t:0,vol:1},{t:422,vol:.56},{t:577,vol:0}]},{type:"sine",fixedFrequency:10,volume:4,fmOscillator:0,attackEnvelope:[{t:1,vol:1},{t:157,vol:.65}]},{type:"sine",fixedFrequency:5,volume:.3,vmOscillator:2,attackEnvelope:[{t:1,vol:1},{t:155,vol:.91},{t:289,vol:.78}]}]},wobble:{masterVolume:.9,masterReleaseEnvelope:[{t:1,vol:.36},{t:24,vol:.15},{t:119,vol:0}],eq:[{frequency:150,Q:.6,gain:-12},{frequency:1100,gain:-2},{frequency:2200,gain:-16},{frequency:5e3,gain:8},{frequency:6400,gain:10},{frequency:8e3,gain:12},{frequency:10800,gain:8}],oscillators:[{type:"triangle",volume:.9,volumePitchTrackingMultiplier:.1,lowpass:{frequency:17,frequencyPitchTrackingMultiplier:100},highpass:{frequency:200}},{type:"whitenoise",volume:.04,lowpass:{frequency:9e3,Q:3},highpass:{frequency:6e3,Q:3},vmOscillator:0,attackEnvelope:[{t:0,vol:0},{t:26,vol:1},{t:71,vol:.73}]},{type:"sine",freqMultiplier:.011,volume:30,fmOscillator:0}]},sine:{masterVolume:1,oscillators:[{type:"sine",volumePitchTrackingMultiplier:.07}]},sineGlide:{masterVolume:1,noteGlideDuration:100,oscillators:[{type:"sine",volumePitchTrackingMultiplier:.07}]},triangle:{masterVolume:.5,oscillators:[{type:"triangle",volume:1,volumePitchTrackingMultiplier:.07}]},sawtooth:{masterVolume:.25,midiInstrument:82,oscillators:[{type:"sawtooth",volume:.3,volumePitchTrackingMultiplier:.07}]},square:{masterVolume:.3,midiInstrument:81,oscillators:[{type:"square",volume:.2,volumePitchTrackingMultiplier:.07}]},chop:{masterVolume:1,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:44,vol:0}],oscillators:[{type:"whitenoise",volume:1,lowpass:{frequency:600},highpass:{frequency:200}}]},shaker:{masterVolume:.4,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:44,vol:0}],oscillators:[{type:"whitenoise",volume:1,lowpass:{frequency:6500},highpass:{frequency:5e3}}]},step:{masterVolume:1,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:44,vol:0}],eq:[{frequency:200,gain:-1},{frequency:400,gain:-14},{frequency:800,gain:8},{frequency:1e3,Q:5,gain:-24},{frequency:1600,gain:8},{frequency:2200,gain:-10},{frequency:5400,gain:4},{frequency:12800,gain:-36}],oscillators:[{type:"whitenoise",volume:1.5,lowpass:{frequency:300},highpass:{frequency:100,Q:6}}]},kick:{masterVolume:.55,masterAttackEnvelope:[{t:1,vol:.8},{t:15,vol:1},{t:45,vol:.35},{t:121,vol:.11},{t:242,vol:0}],eq:[{frequency:50,gain:6},{frequency:400,gain:-18},{frequency:1600,gain:18}],oscillators:[{type:"triangle",fixedFrequency:90,volume:1,lowpass:{frequency:300},attackEnvelope:[{t:1,vol:1},{t:6,vol:1},{t:45,vol:.01}]},{type:"whitenoise",volume:.4,lowpass:{frequency:200},attackEnvelope:[{t:1,vol:1},{t:30,vol:0}]},{type:"triangle",freqMultiplier:.1,volume:1,lowpass:{frequency:200}}]},shortnote:{masterVolume:.8,midiInstrument:116,masterAttackEnvelope:[{t:1,vol:1},{t:15,vol:0}],eq:[{frequency:400,gain:-4},{frequency:800,gain:-12},{frequency:2400,gain:4},{frequency:7200,gain:-20},{frequency:1e3,Q:5,gain:-12},{frequency:5400,gain:-32},{frequency:12800,gain:-14}],oscillators:[{type:"sawtooth",volume:.6,lowpass:{frequency:1e3}},{type:"whitenoise",volume:.2,lowpass:{frequency:1e4},highpass:{frequency:7e3},attackEnvelope:[{t:1,vol:1},{t:10,vol:0}]},{type:"whitenoise",volume:1.3,lowpass:{frequency:700,Q:4},highpass:{frequency:250}}]},noise:{masterVolume:.3,midiInstrument:122,oscillators:[{type:"whitenoise"}]},filteredNoise:{masterVolume:.3,midiInstrument:122,eq:[{frequency:1600,gain:-8},{frequency:2200,gain:-4}],oscillators:[{type:"whitenoise",lowpass:{frequency:5,frequencyPitchTrackingMultiplier:1300,Q:6},highpass:{frequency:5,frequencyPitchTrackingMultiplier:300,Q:6}}]},wind:{masterVolume:.75,midiInstrument:122,noteGlideDuration:150,masterReleaseEnvelope:[{t:0,vol:1},{t:124,vol:.24},{t:281,vol:0}],oscillators:[{type:"whitenoise",volume:1,lowpass:{frequency:100,frequencyPitchTrackingMultiplier:6,Q:23},highpass:{frequency:170,frequencyPitchTrackingMultiplier:6}},{type:"sine",freqMultiplier:.016,volume:1e3,fmOscillator:0}]}},q=r().defined,T=r().extend,w=function(){function e(e,t,i){this.audioContext=e,this.curParams={},this.midiTrackName=i.midiTrackName,this.masterVolNode=new GainNode(e),this.masterVolNode.connect(t),this.volumeNode=new GainNode(e),this.createNodesFromCapabilities(T({pan:!0},i.capabilities||{})),this.connectCapabilityNodes(this.volumeNode,this.masterVolNode),this.synthPatch=new v(e,"string"==typeof i.synthPatch?g[i.synthPatch]:i.synthPatch),this.midiInstrument=this.synthPatch.midiInstrument||1,this.synthPatch.startSilently(),this.synthPatch.connect(this.volumeNode)}return e.prototype.setMasterVolume=function(t){this.masterVolNode.gain.setTargetAtTime(t,0,e.rampTime)},e.prototype.scheduleEventAtTime=function(t,i){var n=T(this.curParams,i),o=q(i.frequency)?i.frequency:q(i.note)?e.musicalNoteToFrequency(i.note):220;q(o)&&this.synthPatch.playFreqAtTime(t,o,n.noteDuration),(q(n.tremoloDepth)||q(n.tremoloSpeed))&&this.setTremoloAtTime(t,n.tremoloDepth,n.tremoloSpeed),q(n.pan)&&this.setPanAtTime(t,n.pan),q(n.volume)&&this.setVolumeAtTime(t,n.volume),(q(n.lowpassFreq)||q(n.lowpassResonance))&&this.setFilterAtTime("lowpass",t,n.lowpassFreq,n.lowpassResonance),(q(n.highpassFreq)||q(n.highpassResonance))&&this.setFilterAtTime("highpass",t,n.highpassFreq,n.highpassResonance)},e.prototype.silenceAtTime=function(e){this.synthPatch.silenceAtTime(e)},e.prototype.cancel=function(){this.synthPatch.mute(),[this.tremoloDepth&&this.tremoloDepth.gain,this.tremoloOsc&&this.tremoloOsc.frequency,this.lowpassNode&&this.lowpassNode.frequency,this.lowpassNode&&this.lowpassNode.Q,this.highpassNode&&this.highpassNode.frequency,this.highpassNode&&this.highpassNode.Q,this.panNode&&this.panNode.pan,this.volumeNode.gain].forEach(function(e){return e&&e.cancelScheduledValues(0)})},e.prototype.destroy=function(){this.cancel(),this.synthPatch.stop(),this.tremoloOsc&&this.tremoloOsc.stop(),[this.tremoloDepth,this.tremoloOsc,this.lowpassNode,this.highpassNode,this.panNode,this.volumeNode,this.masterVolNode].forEach(function(e){return e&&e.disconnect()})},e.prototype.setPanAtTime=function(t,i){this.panNode&&this.panNode.pan.setTargetAtTime(i,t+this.audioContext.currentTime,e.rampTime)},e.prototype.setFilterAtTime=function(t,i,n,o){var s=this[t+"Node"],a=this.audioContext.currentTime+i;s&&(q(o)&&s.Q.setTargetAtTime(o,a,e.rampTime),q(n)&&s.frequency.setTargetAtTime(n,a,e.rampTime))},e.prototype.setVolumeAtTime=function(t,i){this.volumeNode&&this.volumeNode.gain.setTargetAtTime(i,t+this.audioContext.currentTime,e.rampTime)},e.prototype.setTremoloAtTime=function(t,i,n){var o=this.audioContext.currentTime+t;this.tremoloDepth&&q(i)&&this.tremoloDepth.gain.setTargetAtTime(i,o,e.rampTime),this.tremoloOsc&&q(n)&&this.tremoloOsc.frequency.setTargetAtTime(15*n,o,e.rampTime)},e.prototype.createNodesFromCapabilities=function(e){var t=this.audioContext;e.pan&&(this.panNode=new StereoPannerNode(t)),e.tremolo&&(this.tremoloOsc=new OscillatorNode(t,{type:"sine",frequency:3}),this.tremoloDepth=new GainNode(t),this.tremoloOsc.connect(this.tremoloDepth),this.tremoloDepth.connect(this.masterVolNode.gain),this.tremoloOsc.start()),e.filters&&(this.lowpassNode=new BiquadFilterNode(t,{type:"lowpass",frequency:2e4}),this.highpassNode=new BiquadFilterNode(t,{type:"highpass",frequency:0}))},e.prototype.connectCapabilityNodes=function(e,t){[this.panNode,this.lowpassNode,this.highpassNode,e].reduce(function(e,t){return t?(t.connect(e),t):e},t)},e.noteStringToC0Distance=function(e){var t=e.match(/^([a-g][#b]?)([0-8])$/i),i=t?t[1]:"a",n=i[0].toLowerCase(),o=i[1];return(({c:0,d:2,e:4,f:5,g:7,a:9,b:11})[n]||0)+("#"===o?1:"b"===o?-1:0)+12*(t?parseInt(t[2],10):4)},e.musicalNoteToFrequency=function(e){return 16.3516*Math.pow(2,Math.min("string"==typeof e?this.noteStringToC0Distance(e):e,107)/12)},e.rampTime=v.stopRampTime/4,e}(),x=r().pick,N=function(){function e(e){this.options=e,this.masterVolume=1,this.synthesis=window.speechSynthesis,void 0!==speechSynthesis.onvoiceschanged&&(speechSynthesis.onvoiceschanged=this.setVoice.bind(this)),this.setVoice(),this.scheduled=[]}return e.prototype.say=function(e,t){if(this.synthesis){this.synthesis.cancel();var i=new SpeechSynthesisUtterance(e);this.voice&&(i.voice=this.voice),i.rate=t&&t.rate||this.options.rate||1,i.pitch=t&&t.pitch||this.options.pitch||1,i.volume=x(t&&t.volume,this.options.volume,1)*this.masterVolume,this.synthesis.speak(i)}},e.prototype.sayAtTime=function(e,t,i){this.scheduled.push(setTimeout(this.say.bind(this,t,i),e))},e.prototype.cancel=function(){this.scheduled.forEach(clearTimeout),this.scheduled=[],this.synthesis.cancel()},e.prototype.destroy=function(){this.cancel()},e.prototype.setMasterVolume=function(e){this.masterVolume=e},e.prototype.setVoice=function(){if(this.synthesis){for(var e=this.options.name,t=this.options.language||"en-US",i=this.synthesis.getVoices(),n=i.length,o=void 0,s=0;s<n;++s){if(e&&i[s].name===e){this.voice=i[s];return}if(!o&&i[s].lang===t&&(o=i[s],!e))break}this.voice=o}},e}(),k=function(){function e(e,t,i,n,o){void 0===i&&(i=!1),this.type=e,this.engine=t,this.showPlayMarker=i,this.muted=o,this.events=n||[]}return e.prototype.addEvent=function(e){var t=this.events[this.events.length-1];if(t&&e.time<t.time){for(var i=this.events.length;i--&&this.events[i].time>e.time;);this.events.splice(i+1,0,e)}else this.events.push(e);return e},e.prototype.mute=function(){this.muted=!0},e.prototype.unmute=function(){this.muted=!1},e.prototype.cancel=function(){this.engine.cancel()},e.prototype.destroy=function(){this.engine.destroy()},e}(),P=r().pick,E=function(e,t){return t>>>8*e&255},M=[0,255,81,3,7,161,32],b=function(e){for(var t=127&e,i=[];e>>=7;)t<<=8,t|=127&e|128;for(;;)if(i.push(255&t),128&t)t>>=8;else break;return i},A=function(e){var t,i,n=[],o=function(e){for(var t=n.length;t--&&n[t].timeMS>e.timeMS;);n.splice(t+1,0,e)};return e.forEach(function(e){var n=e.instrumentEventOptions||{},s=e.time,a=i=P(n.noteDuration,i),r=a&&e.time+a,l=[{valMap:function(e){return 64+63*e&127},data:{10:n.pan,92:n.tremoloDepth,93:n.tremoloSpeed}},{valMap:function(e){return 127*e/2e4&127},data:{74:n.lowpassFreq,75:n.highpassFreq}},{valMap:function(e){return 63*Math.min(18,Math.max(-18,e))/18+63&127},data:{71:n.lowpassResonance,76:n.highpassResonance}}],c=t=void 0===n.volume?P(t,127):127*n.volume&127,u=n.frequency,h=n.note||0,p=12+(u?Math.round(12*Math.log(u)/Math.LN2-48.37632):"string"==typeof h?w.noteStringToC0Distance(h):h)&127;l.forEach(function(e){return Object.keys(e.data).forEach(function(t){var i=e.data[t];void 0!==i&&o({timeMS:s,type:"CTRL_CHG",data:[176,parseInt(t,10),e.valMap(i)]})})}),r&&(o({timeMS:s,type:"NON",data:[144,p,c]}),o({timeMS:r,type:"NOF",data:[128,p,c]}))}),n},O=function(e,t){var i=[];if(t&&i.push(0,192,127&t),e){for(var n=[],o=0;o<e.length;++o){var s=e.charCodeAt(o);s<128&&n.push(s)}return i.concat([0,255,3],b(n.length),n)}return i},C=function(e,t,i,n){var o=0,s=O(i,n),a=A(e).reduce(function(e,t){var i=b(t.timeMS-o);return o=t.timeMS,e.concat(i,t.data)},[]),r=[0,255,47,0],l=(t?M.length:0)+s.length+a.length+r.length;return[77,84,114,107,E(3,l),E(2,l),E(1,l),E(0,l)].concat(t?M:[],s,a,r)},S=function(e){var t,i=e.filter(function(e){return!!e.events.length}),n=i.length,o=n>1;return new Uint8Array([77,84,104,100,0,0,0,6,0,+((t=o?n+1:n)>1),E(1,t),E(0,t),1,244].concat(o?C([],!0):[],i.reduce(function(e,t){var i=t.engine;return e.concat(C(t.events,!o,i.midiTrackName,i.midiInstrument))},[])))},V=r().isSafari,I=r().win,F=r().win.document,D=I.URL||I.webkitURL||I,Q=function(e,t){var i=I.navigator,n=F.createElement("a");if("string"!=typeof e&&!(e instanceof String)&&i.msSaveOrOpenBlob){i.msSaveOrOpenBlob(e,t);return}if(e=""+e,i.userAgent.length>1e3)throw Error("Input too long");var o=/Edge\/\d+/.test(i.userAgent);if((V&&"string"==typeof e&&0===e.indexOf("data:application/pdf")||o||e.length>2e6)&&!(e=function(e){var t=e.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(t&&t.length>3&&I.atob&&I.ArrayBuffer&&I.Uint8Array&&I.Blob&&D.createObjectURL){for(var i=I.atob(t[3]),n=new I.ArrayBuffer(i.length),o=new I.Uint8Array(n),s=0;s<o.length;++s)o[s]=i.charCodeAt(s);return D.createObjectURL(new I.Blob([o],{type:t[1]}))}}(e)||""))throw Error("Failed to convert to blob");if(void 0!==n.download)n.href=e,n.download=t,F.body.appendChild(n),n.click(),F.body.removeChild(n);else try{if(!I.open(e,"chart"))throw Error("Failed to open window")}catch(t){I.location.href=e}},R=r().defined,j=r().find,B=r().merge,G=function(){function e(e,t){this.chart=t,this.isPaused=!1,this.isPlaying=!1,this.channels=[],this.scheduledCallbacks=[],this.playTimestamp=0,this.resumeFromTime=0,this.options=e||{}}return e.prototype.addChannel=function(e,t,i,n){if(void 0===i&&(i=!1),"instrument"===e&&!t.scheduleEventAtTime||"speech"===e&&!t.sayAtTime)throw Error("Highcharts Sonification: Invalid channel engine.");var o=new k(e,t,i,n);return this.channels.push(o),o},e.prototype.play=function(e,t,i,n){var o,s,a=this;void 0===t&&(t=!0),void 0===i&&(i=!0),this.isPlaying?this.cancel():this.clearScheduledCallbacks(),this.onEndArgument=n,this.playTimestamp=Date.now(),this.resumeFromTime=0,this.isPaused=!1,this.isPlaying=!0;var r=this.options.skipThreshold||2,l=this.options.onPlay,c=this.options.showTooltip,u=this.options.showCrosshair,h=e?(s=(o=(this.playingChannels||this.channels).map(function(t){return t.cancel(),{channel:t,filteredEvents:t.muted?[]:t.events.filter(e)}})).reduce(function(e,t){return Math.min(e,t.filteredEvents.length?t.filteredEvents[0].time:1/0)},1/0),o.map(function(e){return new k(e.channel.type,e.channel.engine,e.channel.showPlayMarker,e.filteredEvents.map(function(e){return B(e,{time:e.time-s})}),e.channel.muted)})):this.channels,p=[];t&&(this.playingChannels=h),l&&l({chart:this.chart,timeline:this});var m=0;h.forEach(function(e){if(!e.muted){var t=e.events.length,i=-1/0,n=-1/0,o="";m=Math.max(e.events[t-1]&&e.events[t-1].time||0,m);for(var s=0;s<t;++s)!function(s){var l=e.events[s],h=Object.keys(l.speechOptions||{}).concat(Object.keys(l.instrumentEventOptions||{})).join();if(h!==o||!(l.time-n<r)){o=h,n=l.time,"instrument"===e.type?e.engine.scheduleEventAtTime(l.time/1e3,l.instrumentEventOptions||{}):e.engine.sayAtTime(l.time,l.message||"",l.speechOptions||{});var m=l.relatedPoint,f=m&&m.series&&m.series.chart,d=l.callback||m&&(c||u)&&!1!==e.showPlayMarker&&(l.time-i>50||s===t-1);m&&p.push(m),d&&(a.scheduledCallbacks.push(setTimeout(function(){if(l.callback&&l.callback(),m){if(u){var e=m.series;e&&e.xAxis&&e.xAxis.crosshair&&e.xAxis.drawCrosshair(void 0,m),e&&e.yAxis&&e.yAxis.crosshair&&e.yAxis.drawCrosshair(void 0,m)}c&&!(f&&f.hoverPoints&&f.hoverPoints.length>1&&j(f.hoverPoints,function(e){return e===m})&&m.onMouseOver)&&m.onMouseOver()}},l.time)),i=l.time)}}(s)}});var f=this.options.onEnd,d=this.options.onStop;this.scheduledCallbacks.push(setTimeout(function(){var e=a.chart,t={chart:e,timeline:a,pointsPlayed:p};a.isPlaying=!1,i&&a.resetPlayState(),d&&d(t),f&&f(t),n&&n(t),e&&(e.tooltip&&e.tooltip.hide(0),e.hoverSeries&&e.hoverSeries.onMouseOut(),e.axes.forEach(function(e){return e.hideCrosshair()}))},m+250)),this.resumeFromTime=t?m:this.getLength()},e.prototype.pause=function(){return this.isPaused=!0,this.cancel(),this.resumeFromTime=Date.now()-this.playTimestamp-10,this.resumeFromTime},e.prototype.getCurrentTime=function(){return this.isPlaying?Date.now()-this.playTimestamp:this.resumeFromTime},e.prototype.getLength=function(){return this.channels.reduce(function(e,t){var i=t.events[t.events.length-1];return i?Math.max(i.time,e):e},0)},e.prototype.resume=function(){if(this.playingChannels){var e=this.resumeFromTime-50;this.play(function(t){return t.time>e},!1,!1,this.onEndArgument),this.playTimestamp-=e}else this.play(void 0,!1,!1,this.onEndArgument)},e.prototype.anchorPlayMoment=function(e,t){this.isPlaying&&this.pause();var i=0;this.play(function(t,n,o){var s=e(t,n,o);return s&&t.time>i&&(i=t.time),s},!1,!1,t),this.playingChannels=this.playingChannels||this.channels,this.isPaused=!0,this.isPlaying=!1,this.resumeFromTime=i},e.prototype.playAdjacent=function(e,t,i,n){this.isPlaying&&this.pause();var o=this.resumeFromTime,s=this.channels.reduce(function(t,i){for(var s=n?i.events.filter(n):i.events,a=0,r=s.length,l=t;a<r;){var c=a+r>>1,u=s[c].time,h=u-o;h>0?(e&&u<l&&(l=u),r=c):h<0?(!e&&u>l&&(l=u),a=c+1):e?a=c+1:r=c}return l},e?1/0:-1/0);if(s===1/0||s===-1/0){i&&i({chart:this.chart,timeline:this,attemptedNext:e});return}this.anchorPlayMoment(function(t,i,a){var r=e?t.time>o&&t.time<=s+.02:t.time<o&&t.time>=s-.02;return n?r&&n(t,i,a):r},t)},e.prototype.playClosestToPropValue=function(e,t,i,n,o){var s=1/0,a=null;(this.playingChannels||this.channels).forEach(function(i){for(var n=i.events,r=n.length;r--;)if(l=n[r],c=r,o?o(l,c,n)&&l.relatedPoint:l.relatedPoint){var l,c,u=n[r].relatedPoint[e],h=R(u)&&Math.abs(t-u);!1!==h&&h<s&&(s=h,a=n[r])}}),a?(this.play(function(e){return!!(a&&e.time<a.time+1&&e.time>a.time-1&&e.relatedPoint===a.relatedPoint)},!1,!1,i),this.playingChannels=this.playingChannels||this.channels,this.isPaused=!0,this.isPlaying=!1,this.resumeFromTime=a.time):n&&n({chart:this.chart,timeline:this})},e.prototype.getEventsForPoint=function(e){return this.channels.reduce(function(t,i){var n=i.events.filter(function(t){return t.relatedPoint===e});return t.concat(n)},[])},e.prototype.playSegment=function(e,t){var i={first:1/0,last:-1/0};if(this.channels.forEach(function(e){e.events.length&&(i.first=Math.min(e.events[0].time,i.first),i.last=Math.max(e.events[e.events.length-1].time,i.last))}),i.first<1/0){var n=(i.last-i.first)/100,o=i.first+e*n,s=o+n;if(!this.channels.some(function(e){for(var t=e.events,i=0,n=t.length;i<n;){var a=i+n>>1,r=t[a].time;if(r<o)i=a+1;else{if(!(r>s))return!0;n=a}}return!1}))return;this.play(function(e){return e.time>=o&&e.time<=s},!1,!1,t),this.playingChannels=this.playingChannels||this.channels,this.isPaused=!0,this.isPlaying=!1,this.resumeFromTime=s}},e.prototype.getLastPlayedPoint=function(e){var t=this.getCurrentTime(),i=this.playingChannels||this.channels,n=1/0,o=null;return i.forEach(function(i){var s=i.events.filter(function(i,n,o){return!!(i.relatedPoint&&i.time<=t&&(!e||e(i,n,o)))}),a=s[s.length-1];if(a){var r=Math.abs(a.time-t);r<n&&(n=r,o=a.relatedPoint)}}),o},e.prototype.reset=function(){this.isPlaying&&this.cancel(),this.resetPlayState()},e.prototype.cancel=function(){var e=this.options.onStop;e&&e({chart:this.chart,timeline:this}),this.isPlaying=!1,this.channels.forEach(function(e){return e.cancel()}),this.playingChannels&&this.playingChannels!==this.channels&&this.playingChannels.forEach(function(e){return e.cancel()}),this.clearScheduledCallbacks(),this.resumeFromTime=0},e.prototype.destroy=function(){this.cancel(),this.playingChannels&&this.playingChannels!==this.channels&&this.playingChannels.forEach(function(e){return e.destroy()}),this.channels.forEach(function(e){return e.destroy()})},e.prototype.setMasterVolume=function(e){this.channels.forEach(function(t){return t.engine.setMasterVolume(e)})},e.prototype.getMIDIData=function(){return S(this.channels.filter(function(e){return"instrument"===e.type}))},e.prototype.downloadMIDI=function(e){var t=this.getMIDIData(),i=(e||this.chart&&this.chart.options.title&&this.chart.options.title.text||"chart")+".mid",n=new Blob([t],{type:"application/octet-stream"}),o=window.URL.createObjectURL(n);Q(o,i),window.URL.revokeObjectURL(o)},e.prototype.resetPlayState=function(){delete this.playingChannels,delete this.onEndArgument,this.playTimestamp=this.resumeFromTime=0,this.isPaused=!1},e.prototype.clearScheduledCallbacks=function(){this.scheduledCallbacks.forEach(clearTimeout),this.scheduledCallbacks=[]},e}(),U=o(984),L=o.n(U),W=function(){return(W=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var o in t=arguments[i])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},H=r().clamp,K=r().defined,_=r().extend,$=r().getNestedProperty,Z=r().merge,z=r().pick,J=L().format,X=function(e){return/^([a-g][#b]?)[0-8]$/i.test(e)};function Y(e,t){var i;if(t){if("number"==typeof(i=e[t]))return i;i=$(t,e)}return"number"==typeof i?i:void 0}function ee(e,t,i,n,o){var s=t.max-t.min;if(s<=0)return i.min;var a=i.max-i.min,r=a*(e-t.min)/s;if(o){var l=t.min>0?function(e){return Math.log(e)/Math.LOG10E}:function(e){var t=Math.abs(e);t<10&&(t+=(10-t)/10);var i=Math.log(t)/Math.LN10;return e<0?-i:i},c=l(t.min);r=a*(l(e)-c)/(l(t.max)-c)}return H(n?i.max-r:i.min+r,i.min,i.max)}function et(e,t,i,n,o,s,a){return z(function(e,t,i,n,o,s){if("number"==typeof o)return o;if("function"==typeof o)return o(_({time:0},e));var a,r=o,l=n.mapFunction,c=n.min,u=n.max,h=n.within;if("object"==typeof o&&(r=o.mapTo,l=o.mapFunction||l,c=z(o.min,c),u=z(o.max,u),h=o.within||n.within,a=o.scale),!r)return null;var p="-"===r.charAt(0);p&&(r=r.slice(1));var m=e.value,f="value"===r&&void 0!==m&&s;if(!f){var d=o.value;if(void 0!==d)m=d;else{if(!e.point)return null;m=e.point[r]}void 0===m&&(m=$(r,e.point))}if("number"!=typeof m||null===m)return null;var y=null;if(e.point){if("xAxis"===h||"yAxis"===h){var v=e.point.series[h];v&&K(v.dataMin)&&K(v.dataMax)&&(y={min:v.dataMin,max:v.dataMax})}else("series"===h||i)&&e.point.series&&(y=t.seriesExtremes[e.point.series.index][f?s:r])}if(y||(y=t.globalExtremes[f?s:r]),a){for(var g=[],q=Math.floor(c/12),T=Math.ceil(u/12)+1,w=a.length,x=q;x<T;++x)for(var N=0;N<w;++N){var k=12*x+a[N];k>=c&&k<=u&&g.push(k)}var P=ee(m,y,{min:0,max:g.length-1},p,"logarithmic"===l);return g[Math.round(P)]}return ee(m,y,{min:c,max:u},p,"logarithmic"===l)}(e,t,i,_({min:0,max:1,mapTo:"y",mapFunction:"linear",within:"chart"},s||{}),n,a),o)}function ei(e,t,i,n){var o=n.mapping||{},s="speech"===n.type?new N({language:n.language,name:n.preferredVoice}):new w(t,i,{capabilities:{pan:!!o.pan,tremolo:!!o.tremolo,filters:!!(o.highpass||o.lowpass)},synthPatch:n.instrument,midiTrackName:n.midiName});return e.addChannel(n.type||"instrument",s,z(n.showPlayMarker,!0))}function en(e,t,i,n,o,s){var a=function(t,o,a,r){return et(e,n,!1,(r||i)[t],o,a,s)},r=[],l={noteDuration:a("noteDuration",200,{min:40,max:1e3}),pan:a("pan",0,{min:-1,max:1}),volume:a("volume",1,{min:.1,max:1})};i.frequency&&(l.frequency=a("frequency",440,{min:50,max:6e3})),i.lowpass&&(l.lowpassFreq=a("frequency",2e4,{min:0,max:2e4},i.lowpass),l.lowpassResonance=a("resonance",0,{min:-6,max:12},i.lowpass)),i.highpass&&(l.highpassFreq=a("frequency",2e4,{min:0,max:2e4},i.highpass),l.highpassResonance=a("resonance",0,{min:-6,max:12},i.highpass)),i.tremolo&&(l.tremoloDepth=a("depth",0,{min:0,max:.8},i.tremolo),l.tremoloSpeed=a("speed",0,{min:0,max:.8},i.tremolo));var c=a("gapBetweenNotes",150,{min:50,max:1e3}),u=a("playDelay",0,{max:200}),h=function(i,a){void 0===a&&(a=0);var h=i;i.mapTo?("string"==typeof i.min&&(h.min=w.noteStringToC0Distance(i.min)),"string"==typeof i.max&&(h.max=w.noteStringToC0Distance(i.max))):"string"==typeof i&&X(i)&&(h=w.noteStringToC0Distance(i)),l.note=et(e,n,!1,h,-1,{min:0,max:107},s),l.note>-1&&(o&&(l.note=Math.round(l.note)),r.push(t.addEvent({time:e.time+u+c*a,relatedPoint:e.point,instrumentEventOptions:void 0!==a?_({},l):l})))};return i.pitch&&i.pitch.constructor===Array?i.pitch.forEach(h):i.pitch?h(i.pitch):i.frequency&&r.push(t.addEvent({time:e.time+u,relatedPoint:e.point,instrumentEventOptions:l})),r}function eo(e,t,i,n,o){var s,a=function(t,s,a){return et(e,n,!1,i[t],s,a,o)},r=a("playDelay",0,{max:200}),l=a("pitch",1,{min:.3,max:2}),c=a("rate",1,{min:.4,max:4}),u=a("volume",1,{min:.1}),h=J("function"==typeof(s=i.text)?s(e):s,e,e.point&&e.point.series.chart);if(h)return t.addEvent({time:e.time+r,relatedPoint:e.point,speechOptions:{pitch:l,rate:c,volume:u},message:h})}function es(e,t,i){if("function"==typeof t)return t(e);if("object"==typeof t){var n=t.prop,o=z(e.value,e.point&&Y(e.point,n));if("number"!=typeof o)return!1;var s=!0,a=t.crossingUp,r=t.crossingDown,l="number"==typeof i;s=a&&r?l&&(i<a&&o>=a||i>r&&o<=r):(void 0===a||l&&i<a&&o>=a)&&(void 0===r||l&&i>r&&o<=r);var c=z(t.max,1/0),u=z(t.min,-1/0);return o<=c&&o>=u&&s}return!0}var ea=function(e,t,i){var n,o,s,a,r,l,c,u,h,p,m,f,d=i.options.sonification||{},y=d.defaultInstrumentOptions,v=d.defaultSpeechOptions,g=Z({enabled:!0,groupTimespan:15,algorithm:"minmax",prop:"y"},d.pointGrouping),q=d.globalTracks||[],T=d.globalContextTracks||[],w="sequential"===d.order,x=Math.max(50,d.duration-300),N=d.afterSeriesWait,k=d.events||{},P=(o=((n=i.options.sonification||{}).defaultInstrumentOptions||{}).mapping||{time:"x",pitch:"y"},s=n.defaultSpeechOptions&&n.defaultSpeechOptions.mapping||{},a=[],r={},l=function(e,t){null!==t?(a[t]=a[t]||{},a[t][e]=!0):r[e]=!0},c={},u={},h=function(e,t,i){var n=function(e){return"-"===e.charAt(0)?e.slice(1):e};if("string"==typeof t&&"text"!==e){if("pitch"===e&&X(t))return;"time"===e&&(u[t]=!0,l(t,i)),c[n(t)]=!0;return}if(t&&t.mapTo&&"string"==typeof t.mapTo){var o=n(t.mapTo);"time"===e&&l(o,i),("time"===e||"series"===t.within)&&(u[o]=!0),c[o]=!0;return}["tremolo","lowpass","highpass"].indexOf(e)>-1&&"object"==typeof t&&Object.keys(t).forEach(function(e){return h(e,t[e],i)})},p=function(e,t){Object.keys(e).forEach(function(i){return h(i,e[i],t)})},m=function(e){return e.forEach(function(e){c[e.valueProp||"x"]=u[e.valueProp||"x"]=!0})},p(o,null),p(s,null),m(n.globalContextTracks||[]),f=Object.keys(r).length,i.series.forEach(function(e){var t=e.options.sonification;if(e.visible&&!(t&&!1===t.enabled)&&(f&&(a[e.index]=Z(r)),t)){var i=(t.defaultInstrumentOptions||{}).mapping,n=(t.defaultSpeechOptions||{}).mapping;i&&p(i,e.index),n&&p(n,e.index),m(t.contextTracks||[]),(t.tracks||[]).concat(t.contextTracks||[]).forEach(function(t){t.mapping&&p(t.mapping,e.index)})}}),W({seriesTimeProps:a},function(e,t,i){for(var n=e.series,o=t.length,s=i.length,a=function(e){return e.reduce(function(e,t){return e[t]={min:1/0,max:-1/0},e},{})},r=function(e,t,i){var n=t[i];void 0===n&&(n=$(i,t)),"number"==typeof n&&(e[i].min=Math.min(e[i].min,n),e[i].max=Math.max(e[i].max,n))},l=a(t),c=n.length,u=Array(c);c--;){var h=a(i),p=n[c].options;if(n[c].visible&&(!p||!p.sonification||!1!==p.sonification.enabled)){for(var m=n[c].points||[],f=m.length;f--;){for(var d=o;d--;)r(l,m[f],t[d]);for(d=s;d--;)r(h,m[f],i[d])}u[c]=h}}return{globalExtremes:l,seriesExtremes:u}}(i,Object.keys(c),Object.keys(u)))),E=new G({onPlay:k.onPlay,onEnd:k.onEnd,onStop:k.onStop,showCrosshair:d.showCrosshair,showTooltip:d.showTooltip},i);i.sonification&&(i.sonification.propMetrics=P);var M=0;return i.series.forEach(function(i,n){var o=i.options.sonification||{};if(i.visible&&!1!==o.enabled){var s,a=w?function(e,t,i,n){var o,s,a=t-(e.chart.series.length-1)*n;if(i.seriesTimeProps.every(function(e){var t=Object.keys(e);return!(t.length>1)&&(o||(o=t[0]),o===t[0])})){var r=i.seriesExtremes[e.index][o];s=Math.round((r.max-r.min)/i.seriesExtremes.reduce(function(e,t){return t[o]?e+t[o].max-t[o].min:e},0)*a)}else{var l=e.chart.series.reduce(function(e,t){return e+t.points.length},0);s=Math.round((e.points||[]).length/l*a)}return Math.max(50,s)}(i,x,P,N):x,r=Z(y,o.defaultInstrumentOptions),l=Z(v,o.defaultSpeechOptions),c=Z(g,o.pointGrouping),u=(o.tracks||[r]).concat(q),h=E.channels.length&&!w?o.contextTracks||[]:(o.contextTracks||[]).concat(T),p=[];u.forEach(function(n){var o=Z({pointGrouping:c,midiName:n.midiName||i.name},"speech"===n.type?l:r,n),u=o.pointGrouping,h=o.activeWhen,m=function(e){"object"==typeof h&&h.prop&&(s=Y(e,h.prop))},f=ei(E,e,t,o),d=function(e){return p.push.apply(p,function(e,t,i,n){var o=[];if("speech"===i.type&&i.mapping){var s=eo(e,t,i.mapping,n);s&&(o=[s])}else i.mapping&&(o=en(e,t,i.mapping,n,z(i.roundToMusicalNotes,!0)));return o}(e,f,o,P))},y=[],v=0,g=function(e){if(1===y.length)d({point:y[0].point,time:v+e/2});else{var t=function(e,t){var i=e.algorithm||"minmax",n=function(e){return t[e]?[t[e].point]:[]};if("first"===i)return n(0);if("last"===i)return n(t.length-1);if("middle"===i)return n(t.length>>1);if("firstlast"===i)return n(0).concat(n(t.length-1));if("minmax"===i){var o,s,a,r,l=e.prop||"y";if(t.forEach(function(e){var t=Y(e.point,l);void 0!==t&&((!o||t<a)&&(o=e,a=t),(!s||t>r)&&(s=e,r=t))}),o&&s)return o.point===s.point?[o.point]:o.time>s.time?[s.point,o.point]:[o.point,s.point]}return[]}(u,y),i=e/t.length;t.forEach(function(e,t){return d({point:e,time:v+i/2+i*t})})}y=[]};(i.points||[]).forEach(function(e,t){var n,r=t===i.points.length-1,l=(n=M,et({point:e,time:0},P,w,o.mapping&&o.mapping.time||0,0,{min:0,max:a,mapTo:"x"})+n),c={point:e,time:l};if(!o.mapping||!es(c,h,s)){m(e),r&&y.length&&g(y[y.length-1].time-y[0].time);return}if(m(e),u.enabled){var p=l-v,f=u.groupTimespan,q=r&&p<=f?p:f;r||p>f?(p<=f&&y.push(c),g(q),v=Math.floor(l/f)*f,r&&p>f?d({point:c.point,time:v+q/2}):y=[c]):y.push(c)}else d(c)})});var m=p.reduce(function(e,t){return t.time<e.time?t:e},{time:1/0}),f=p.reduce(function(e,t){return t.time>e.time?t:e},{time:-1/0});m.callback=k.onSeriesStart?k.onSeriesStart.bind(null,{series:i,timeline:E}):void 0,f.callback=k.onSeriesEnd?k.onSeriesEnd.bind(null,{series:i,timeline:E}):void 0,h.forEach(function(i){var o="speech"===i.type?Z(v,i):Z(y,{mapping:{pitch:{mapTo:"value"}}},i),r=ei(E,e,t,o);s=void 0;var l=o.timeInterval,c=o.valueInterval,u=o.valueProp||"x",h=o.activeWhen,p=P.seriesExtremes[n][u],m=function(e,t){if(!o.mapping||!es({time:e,value:t},"object"==typeof h?_({prop:u},h):h,s)){s=t;return}s=t,"speech"===o.type?eo({time:e,value:t},r,o.mapping,P,u):en({time:e,value:t},r,o.mapping,P,z(o.roundToMusicalNotes,!0),u)};if(l)for(var f=0;f<=a;){var d=ee(f,{min:0,max:a},p);m(f+M,d),f+=l}if(c)for(var d=p.min;d<=p.max;){var f=ee(d,p,{min:0,max:a},!1,"logarithmic"===o.valueMapFunction);m(f+M,d),d+=c}}),w&&(M+=a+N)}}),E},er=r().defaultOptions,el=r().getOptions,ec=r().addEvent,eu=r().extend,eh=r().fireEvent,ep=r().merge,em=r().pick,ef=r().doc,ed=r().win,ey=function(){function e(e){this.chart=e,this.retryContextCounter=0,this.lastUpdate=0,this.unbindKeydown=ec(ef,"keydown",function(t){e&&e.sonification&&("Esc"===t.key||"Escape"===t.key)&&e.sonification.cancel()});try{this.audioContext=new ed.AudioContext,this.audioContext.suspend(),this.audioDestination=this.audioContext.destination}catch(e){}}return e.prototype.setAudioDestination=function(e){this.audioDestination=e,this.update()},e.prototype.isPlaying=function(){return!!this.timeline&&this.timeline.isPlaying},e.prototype.playSegment=function(e,t){this.ready(this.playSegment.bind(this,e,t))&&this.timeline&&this.timeline.playSegment(e,t)},e.prototype.playAdjacent=function(e,t,i){var n=this;if(this.ready(this.playAdjacent.bind(this,e,t,i))&&this.timeline){var o=this.chart.options.sonification,s=o&&o.events&&o.events.onBoundaryHit;s||this.initBoundaryInstrument(),this.timeline.playAdjacent(e,t,s||function(){n.defaultBoundaryHit()},i)}},e.prototype.playAdjacentSeries=function(e,t,i){void 0===t&&(t="x");var n=this.getLastPlayedPoint();if(n){var o=n.series.index+(e?1:-1);return this.playClosestToProp(t,n[t],function(e){return!!e.relatedPoint&&e.relatedPoint.series.index===o},i),this.chart.series[o]||null}return null},e.prototype.playClosestToProp=function(e,t,i,n){var o=this;if(this.ready(this.playClosestToProp.bind(this,e,t,i,n))&&this.timeline){var s=this.chart.options.sonification,a=s&&s.events&&s.events.onBoundaryHit;a||this.initBoundaryInstrument(),this.timeline.playClosestToPropValue(e,t,n,a||function(){return o.defaultBoundaryHit()},i)}},e.prototype.getLastPlayedPoint=function(){return this.timeline?this.timeline.getLastPlayedPoint():null},e.prototype.playNote=function(e,t,i){if(void 0===i&&(i=0),this.ready(this.playNote.bind(this,e,t))){var n=t.noteDuration=t.noteDuration||500,o=new w(this.audioContext,this.audioDestination,{synthPatch:e,capabilities:{filters:!0,tremolo:!0,pan:!0}});o.scheduleEventAtTime(i/1e3,t),setTimeout(function(){return o&&o.destroy()},i+n+500)}},e.prototype.speak=function(e,t,i){void 0===i&&(i=0),new N(ep({language:"en-US",rate:1.5,volume:.4},t||{})).sayAtTime(i,e)},e.prototype.cancel=function(){this.timeline&&this.timeline.cancel(),eh(this,"cancel")},e.prototype.downloadMIDI=function(){this.ready(this.downloadMIDI.bind(this))&&this.timeline&&(this.timeline.reset(),this.timeline.downloadMIDI())},e.prototype.sonifyChart=function(e,t){this.ready(this.sonifyChart.bind(this,e,t))&&this.timeline&&(this.timeline.reset(),this.beforePlay(),this.timeline.play(void 0,void 0,e,t))},e.prototype.sonifySeries=function(e,t,i){this.ready(this.sonifySeries.bind(this,e,t,i))&&this.timeline&&(this.timeline.reset(),this.beforePlay(),this.timeline.play(function(t){return!!t.relatedPoint&&t.relatedPoint.series===e},void 0,t,i))},e.prototype.sonifyPoint=function(e,t){this.ready(this.sonifyPoint.bind(this,e,t))&&this.timeline&&(this.timeline.reset(),this.beforePlay(),this.timeline.anchorPlayMoment(function(t){return t.relatedPoint===e},t))},e.prototype.setMasterVolume=function(e){this.timeline&&this.timeline.setMasterVolume(e)},e.prototype.destroy=function(){this.unbindKeydown(),this.timeline&&(this.timeline.destroy(),delete this.timeline),this.boundaryInstrument&&this.boundaryInstrument.stop(),this.audioContext&&(this.audioContext.close(),delete this.audioContext)},e.prototype.update=function(){var e=this.chart.options&&this.chart.options.sonification;if(this.ready(this.update.bind(this))&&e){var t=Date.now(),i=e.updateInterval;if(t-this.lastUpdate<i&&!this.forceReady){clearTimeout(this.scheduledUpdate),this.scheduledUpdate=setTimeout(this.update.bind(this),i/2);return}var n=e.events||{};if(n.beforeUpdate&&n.beforeUpdate({chart:this.chart,timeline:this.timeline}),this.lastUpdate=t,this.timeline&&this.timeline.destroy(),this.audioContext&&this.audioDestination){this.timeline=ea(this.audioContext,this.audioDestination,this.chart);var o=this.chart.options.sonification;this.timeline.setMasterVolume(em(o&&o.masterVolume,1))}n.afterUpdate&&n.afterUpdate({chart:this.chart,timeline:this.timeline})}},e.prototype.ready=function(e){var t=this;return!!this.audioContext&&!!this.audioDestination&&!!this.chart.options&&(!this.chart.options.sonification||!1!==this.chart.options.sonification.enabled)&&("suspended"!==this.audioContext.state||this.forceReady?(this.retryContextCounter=0,!0):(this.retryContextCounter++<20&&setTimeout(function(){t.audioContext&&"suspended"===t.audioContext.state?t.audioContext.resume().then(e):e()},5),!1))},e.prototype.beforePlay=function(){var e=this.chart.options.sonification,t=e&&e.events&&e.events.beforePlay;t&&t({chart:this.chart,timeline:this.timeline})},e.prototype.initBoundaryInstrument=function(){this.boundaryInstrument||(this.boundaryInstrument=new v(this.audioContext,ep(g.chop,{masterVolume:.3})),this.boundaryInstrument.startSilently(),this.boundaryInstrument.connect(this.audioDestination))},e.prototype.defaultBoundaryHit=function(){this.boundaryInstrument&&(this.boundaryInstrument.playFreqAtTime(.1,1,200),this.boundaryInstrument.playFreqAtTime(.2,1,200))},e}();!function(e){var t=[];function i(){var t=this.sonification,i=this.options&&this.options.sonification;i&&i.enabled?t?t.update():(this.sonification=new e(this),this.sonification.update()):t&&(t.destroy(),delete this.sonification)}function n(){this&&this.sonification&&this.sonification.destroy()}function o(){this.updateSonificationEnabled&&this.updateSonificationEnabled()}function s(e){var t=e.options.sonification;t&&(ep(!0,this.options.sonification,t),o.call(this))}e.compose=function(e,a,r){-1===t.indexOf(e)&&(t.push(e),eu(e.prototype,{updateSonificationEnabled:i,sonify:function(e){this.sonification&&this.sonification.sonifyChart(!1,e)},toggleSonify:function(e,t){if(void 0===e&&(e=!0),this.sonification){var i=this.sonification.timeline;ed.speechSynthesis&&ed.speechSynthesis.cancel(),i&&this.sonification.isPlaying()?e?this.sonification.cancel():i.pause():i&&i.isPaused?i.resume():this.sonification.sonifyChart(e,t)}}}),ec(e,"destroy",n),ec(e,"render",o),ec(e,"update",s)),-1===t.indexOf(a)&&(t.push(a),a.prototype.sonify=function(e){this.chart.sonification&&this.chart.sonification.sonifySeries(this,!1,e)}),-1===t.indexOf(r)&&(t.push(r),r.prototype.sonify=function(e){this.series.chart.sonification&&this.series.chart.sonification.sonifyPoint(this,e)});var l=el().exporting;l&&l.buttons&&l.buttons.contextButton.menuItems&&l.buttons.contextButton.menuItems.push("separator","downloadMIDI","playAsSound")}}(ey||(ey={})),ep(!0,er,{sonification:{enabled:!0,duration:6e3,afterSeriesWait:700,updateInterval:200,masterVolume:.7,order:"sequential",showTooltip:!0,showCrosshair:!0,pointGrouping:{enabled:!0,groupTimespan:15,algorithm:"minmax",prop:"y"},defaultInstrumentOptions:{roundToMusicalNotes:!0,instrument:"piano",mapping:{time:"x",pan:"x",noteDuration:200,pitch:{mapTo:"y",min:"c2",max:"c6",within:"yAxis"},gapBetweenNotes:100}},defaultSpeechOptions:{language:"en-US",mapping:{time:"x",rate:1.3,volume:.4},pointGrouping:{algorithm:"last"}}},exporting:{menuItemDefinitions:{downloadMIDI:{textKey:"downloadMIDI",onclick:function(){this.sonification&&this.sonification.downloadMIDI()}},playAsSound:{textKey:"playAsSound",onclick:function(){var e=this.sonification;e&&e.isPlaying()?e.cancel():this.sonify()}}}},lang:{downloadMIDI:"Download MIDI",playAsSound:"Play as sound"}});var ev=ey,eg=r();eg.sonification={InstrumentPresets:g,Scales:{minor:[0,2,3,5,7,8,10],dorian:[0,2,3,5,7,9,10],harmonicMinor:[0,2,3,5,7,8,11],phrygian:[0,1,3,5,7,8,11],major:[0,2,4,5,7,9,11],lydian:[0,2,4,6,7,9,11],mixolydian:[0,2,4,5,7,9,10],majorPentatonic:[0,2,4,7,9],minorPentatonic:[0,3,5,7,10]},SynthPatch:v,SonificationInstrument:w,SonificationSpeaker:N,SonificationTimeline:G,Sonification:ev},ev.compose(eg.Chart,eg.Series,eg.Point);var eq=r();return s.default}()});