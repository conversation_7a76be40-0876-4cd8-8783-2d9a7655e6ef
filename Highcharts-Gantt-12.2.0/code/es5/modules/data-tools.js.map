{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/data-tools\n * @requires highcharts\n *\n * Highcharts\n *\n * (c) 2010-2025 Highsoft AS\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/data-tools\", [[\"highcharts/highcharts\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/data-tools\"] = factory(require(\"highcharts\"));\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ data_tools_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Data/Modifiers/DataModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *\n * */\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Abstract class to provide an interface for modifying a table.\n *\n */\nvar DataModifier = /** @class */ (function () {\n    function DataModifier() {\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Runs a timed execution of the modifier on the given datatable.\n     * Can be configured to run multiple times.\n     *\n     * @param {DataTable} dataTable\n     * The datatable to execute\n     *\n     * @param {DataModifier.BenchmarkOptions} options\n     * Options. Currently supports `iterations` for number of iterations.\n     *\n     * @return {Array<number>}\n     * An array of times in milliseconds\n     *\n     */\n    DataModifier.prototype.benchmark = function (dataTable, options) {\n        var results = [];\n        var modifier = this;\n        var execute = function () {\n                modifier.modifyTable(dataTable);\n            modifier.emit({\n                type: 'afterBenchmarkIteration'\n            });\n        };\n        var defaultOptions = {\n                iterations: 1\n            };\n        var iterations = merge(defaultOptions,\n            options).iterations;\n        modifier.on('afterBenchmarkIteration', function () {\n            if (results.length === iterations) {\n                modifier.emit({\n                    type: 'afterBenchmark',\n                    results: results\n                });\n                return;\n            }\n            // Run again\n            execute();\n        });\n        var times = {\n                startTime: 0,\n                endTime: 0\n            };\n        // Add timers\n        modifier.on('modify', function () {\n            times.startTime = window.performance.now();\n        });\n        modifier.on('afterModify', function () {\n            times.endTime = window.performance.now();\n            results.push(times.endTime - times.startTime);\n        });\n        // Initial run\n        execute();\n        return results;\n    };\n    /**\n     * Emits an event on the modifier to all registered callbacks of this event.\n     *\n     * @param {DataModifier.Event} [e]\n     * Event object containing additonal event information.\n     */\n    DataModifier.prototype.emit = function (e) {\n        fireEvent(this, e.type, e);\n    };\n    /**\n     * Returns a modified copy of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<Highcharts.DataTable>}\n     * Table with `modified` property as a reference.\n     */\n    DataModifier.prototype.modify = function (table, eventDetail) {\n        var modifier = this;\n        return new Promise(function (resolve, reject) {\n            if (table.modified === table) {\n                table.modified = table.clone(false, eventDetail);\n            }\n            try {\n                resolve(modifier.modifyTable(table, eventDetail));\n            }\n            catch (e) {\n                modifier.emit({\n                    type: 'error',\n                    detail: eventDetail,\n                    table: table\n                });\n                reject(e);\n            }\n        });\n    };\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    DataModifier.prototype.modifyCell = function (table, \n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    columnName, rowIndex, cellValue, eventDetail\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        return this.modifyTable(table);\n    };\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    DataModifier.prototype.modifyColumns = function (table, \n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    columns, rowIndex, eventDetail\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        return this.modifyTable(table);\n    };\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    DataModifier.prototype.modifyRows = function (table, \n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    rows, rowIndex, eventDetail\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        return this.modifyTable(table);\n    };\n    /**\n     * Registers a callback for a specific modifier event.\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {DataEventEmitter.Callback} callback\n     * Function to register for an modifier callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the modifier event.\n     */\n    DataModifier.prototype.on = function (type, callback) {\n        return addEvent(this, type, callback);\n    };\n    return DataModifier;\n}());\n/* *\n *\n *  Class Namespace\n *\n * */\n/**\n * Additionally provided types for modifier events and options.\n */\n(function (DataModifier) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Registry as a record object with modifier names and their class\n     * constructor.\n     */\n    DataModifier.types = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a modifier class to the registry. The modifier class has to provide\n     * the `DataModifier.options` property and the `DataModifier.modifyTable`\n     * method to modify the table.\n     *\n     * @private\n     *\n     * @param {string} key\n     * Registry key of the modifier class.\n     *\n     * @param {DataModifierType} DataModifierClass\n     * Modifier class (aka class constructor) to register.\n     *\n     * @return {boolean}\n     * Returns true, if the registration was successful. False is returned, if\n     * their is already a modifier registered with this key.\n     */\n    function registerType(key, DataModifierClass) {\n        return (!!key &&\n            !DataModifier.types[key] &&\n            !!(DataModifier.types[key] = DataModifierClass));\n    }\n    DataModifier.registerType = registerType;\n})(DataModifier || (DataModifier = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Modifiers_DataModifier = (DataModifier);\n\n;// ./code/es5/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items) {\n        if (items === void 0) { items = []; }\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice.apply(column, __spreadArray([start, deleteCount], items, false)),\n                array: column\n            };\n        }\n        var Constructor = Object.getPrototypeOf(column)\n                .constructor;\n        var removed = column[removedAsSubarray ? 'subarray' : 'slice'](start,\n            start + deleteCount);\n        var newLength = column.length - deleteCount + items.length;\n        var result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es5/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nvar setLength = Data_ColumnUtils.setLength, splice = Data_ColumnUtils.splice;\n\nvar DataTableCore_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nvar DataTableCore = /** @class */ (function () {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    function DataTableCore(options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        var rowCount = 0;\n        objectEach(options.columns || {}, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    DataTableCore.prototype.applyRowCount = function (rowCount) {\n        var _this = this;\n        this.rowCount = rowCount;\n        objectEach(this.columns, function (column, columnName) {\n            if (column.length !== rowCount) {\n                _this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    };\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    DataTableCore.prototype.deleteRows = function (rowIndex, rowCount) {\n        var _this = this;\n        if (rowCount === void 0) { rowCount = 1; }\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            var length_1 = 0;\n            objectEach(this.columns, function (column, columnName) {\n                _this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length_1 = column.length;\n            });\n            this.rowCount = length_1;\n        }\n        DataTableCore_fireEvent(this, 'afterDeleteRows', { rowIndex: rowIndex, rowCount: rowCount });\n        this.versionTag = uniqueKey();\n    };\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getColumn = function (columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    };\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    DataTableCore.prototype.getColumns = function (columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).reduce(function (columns, columnName) {\n            columns[columnName] = _this.columns[columnName];\n            return columns;\n        }, {});\n    };\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getRow = function (rowIndex, columnNames) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).map(function (key) { var _a; return (_a = _this.columns[key]) === null || _a === void 0 ? void 0 : _a[rowIndex]; });\n    };\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumn = function (columnName, column, rowIndex, eventDetail) {\n        var _a;\n        if (column === void 0) { column = []; }\n        if (rowIndex === void 0) { rowIndex = 0; }\n        this.setColumns((_a = {}, _a[columnName] = column, _a), rowIndex, eventDetail);\n    };\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumns = function (columns, rowIndex, eventDetail) {\n        var _this = this;\n        var rowCount = this.rowCount;\n        objectEach(columns, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            DataTableCore_fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    };\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    DataTableCore.prototype.setRow = function (row, rowIndex, insert, eventDetail) {\n        if (rowIndex === void 0) { rowIndex = this.rowCount; }\n        var columns = this.columns,\n            indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        objectEach(row, function (cellValue, columnName) {\n            var column = columns[columnName] ||\n                    (eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.addColumns) !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            DataTableCore_fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    };\n    return DataTableCore;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Data/DataTable.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Jomar Hønsi\n *  - Dawid Dragula\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar DataTable_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, DataTable_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, DataTable_uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nvar DataTable = /** @class */ (function (_super) {\n    __extends(DataTable, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function DataTable(options) {\n        if (options === void 0) { options = {}; }\n        var _this = _super.call(this,\n            options) || this;\n        _this.modified = _this;\n        return _this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Tests whether a row contains only `null` values or is equal to\n     * DataTable.NULL. If all columns have `null` values, the function returns\n     * `true`. Otherwise, it returns `false` to indicate that the row contains\n     * at least one non-null value.\n     *\n     * @function Highcharts.DataTable.isNull\n     *\n     * @param {Highcharts.DataTableRow|Highcharts.DataTableRowObject} row\n     * Row to test.\n     *\n     * @return {boolean}\n     * Returns `true`, if the row contains only null, otherwise `false`.\n     *\n     * @example\n     * if (DataTable.isNull(row)) {\n     *   // handle null row\n     * }\n     */\n    DataTable.isNull = function (row) {\n        if (row === DataTable.NULL) {\n            return true;\n        }\n        if (row instanceof Array) {\n            if (!row.length) {\n                return false;\n            }\n            for (var i = 0, iEnd = row.length; i < iEnd; ++i) {\n                if (row[i] !== null) {\n                    return false;\n                }\n            }\n        }\n        else {\n            var columnNames = Object.keys(row);\n            if (!columnNames.length) {\n                return false;\n            }\n            for (var i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                if (row[columnNames[i]] !== null) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Returns a clone of this table. The cloned table is completely independent\n     * of the original, and any changes made to the clone will not affect\n     * the original table.\n     *\n     * @function Highcharts.DataTable#clone\n     *\n     * @param {boolean} [skipColumns]\n     * Whether to clone columns or not.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Clone of this data table.\n     *\n     * @emits #cloneTable\n     * @emits #afterCloneTable\n     */\n    DataTable.prototype.clone = function (skipColumns, eventDetail) {\n        var table = this,\n            tableOptions = {};\n        table.emit({ type: 'cloneTable', detail: eventDetail });\n        if (!skipColumns) {\n            tableOptions.columns = table.columns;\n        }\n        if (!table.autoId) {\n            tableOptions.id = table.id;\n        }\n        var tableClone = new DataTable(tableOptions);\n        if (!skipColumns) {\n            tableClone.versionTag = table.versionTag;\n            tableClone.originalRowIndexes = table.originalRowIndexes;\n            tableClone.localRowIndexes = table.localRowIndexes;\n        }\n        table.emit({\n            type: 'afterCloneTable',\n            detail: eventDetail,\n            tableClone: tableClone\n        });\n        return tableClone;\n    };\n    /**\n     * Deletes columns from the table.\n     *\n     * @function Highcharts.DataTable#deleteColumns\n     *\n     * @param {Array<string>} [columnNames]\n     * Names of columns to delete. If no array is provided, all\n     * columns will be deleted.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTableColumnCollection|undefined}\n     * Returns the deleted columns, if found.\n     *\n     * @emits #deleteColumns\n     * @emits #afterDeleteColumns\n     */\n    DataTable.prototype.deleteColumns = function (columnNames, eventDetail) {\n        var table = this,\n            columns = table.columns,\n            deletedColumns = {},\n            modifiedColumns = {},\n            modifier = table.modifier,\n            rowCount = table.rowCount;\n        columnNames = (columnNames || Object.keys(columns));\n        if (columnNames.length) {\n            table.emit({\n                type: 'deleteColumns',\n                columnNames: columnNames,\n                detail: eventDetail\n            });\n            for (var i = 0, iEnd = columnNames.length, column = void 0, columnName = void 0; i < iEnd; ++i) {\n                columnName = columnNames[i];\n                column = columns[columnName];\n                if (column) {\n                    deletedColumns[columnName] = column;\n                    modifiedColumns[columnName] = new Array(rowCount);\n                }\n                delete columns[columnName];\n            }\n            if (!Object.keys(columns).length) {\n                table.rowCount = 0;\n                this.deleteRowIndexReferences();\n            }\n            if (modifier) {\n                modifier.modifyColumns(table, modifiedColumns, 0, eventDetail);\n            }\n            table.emit({\n                type: 'afterDeleteColumns',\n                columns: deletedColumns,\n                columnNames: columnNames,\n                detail: eventDetail\n            });\n            return deletedColumns;\n        }\n    };\n    /**\n     * Deletes the row index references. This is useful when the original table\n     * is deleted, and the references are no longer needed. This table is\n     * then considered an original table or a table that has the same row's\n     * order as the original table.\n     */\n    DataTable.prototype.deleteRowIndexReferences = function () {\n        delete this.originalRowIndexes;\n        delete this.localRowIndexes;\n        // Here, in case of future need, can be implemented updating of the\n        // modified tables' row indexes references.\n    };\n    /**\n     * Deletes rows in this table.\n     *\n     * @function Highcharts.DataTable#deleteRows\n     *\n     * @param {number} [rowIndex]\n     * Index to start delete of rows. If not specified, all rows will be\n     * deleted.\n     *\n     * @param {number} [rowCount=1]\n     * Number of rows to delete.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Array<Highcharts.DataTableRow>}\n     * Returns the deleted rows, if found.\n     *\n     * @emits #deleteRows\n     * @emits #afterDeleteRows\n     */\n    DataTable.prototype.deleteRows = function (rowIndex, rowCount, eventDetail) {\n        if (rowCount === void 0) { rowCount = 1; }\n        var table = this,\n            deletedRows = [],\n            modifiedRows = [],\n            modifier = table.modifier;\n        table.emit({\n            type: 'deleteRows',\n            detail: eventDetail,\n            rowCount: rowCount,\n            rowIndex: (rowIndex || 0)\n        });\n        if (typeof rowIndex === 'undefined') {\n            rowIndex = 0;\n            rowCount = table.rowCount;\n        }\n        if (rowCount > 0 && rowIndex < table.rowCount) {\n            var columns = table.columns,\n                columnNames = Object.keys(columns);\n            for (var i = 0, iEnd = columnNames.length, column = void 0, deletedCells = void 0, columnName = void 0; i < iEnd; ++i) {\n                columnName = columnNames[i];\n                column = columns[columnName];\n                var result = Data_ColumnUtils.splice(column,\n                    rowIndex,\n                    rowCount);\n                deletedCells = result.removed;\n                columns[columnName] = column = result.array;\n                if (!i) {\n                    table.rowCount = column.length;\n                }\n                for (var j = 0, jEnd = deletedCells.length; j < jEnd; ++j) {\n                    deletedRows[j] = (deletedRows[j] || []);\n                    deletedRows[j][i] = deletedCells[j];\n                }\n                modifiedRows.push(new Array(iEnd));\n            }\n        }\n        if (modifier) {\n            modifier.modifyRows(table, modifiedRows, (rowIndex || 0), eventDetail);\n        }\n        table.emit({\n            type: 'afterDeleteRows',\n            detail: eventDetail,\n            rowCount: rowCount,\n            rowIndex: (rowIndex || 0),\n            rows: deletedRows\n        });\n        return deletedRows;\n    };\n    /**\n     * Emits an event on this table to all registered callbacks of the given\n     * event.\n     * @private\n     *\n     * @param {DataTable.Event} e\n     * Event object with event information.\n     */\n    DataTable.prototype.emit = function (e) {\n        if ([\n            'afterDeleteColumns',\n            'afterDeleteRows',\n            'afterSetCell',\n            'afterSetColumns',\n            'afterSetRows'\n        ].includes(e.type)) {\n            this.versionTag = DataTable_uniqueKey();\n        }\n        DataTable_fireEvent(this, e.type, e);\n    };\n    /**\n     * Fetches a single cell value.\n     *\n     * @function Highcharts.DataTable#getCell\n     *\n     * @param {string} columnName\n     * Column name of the cell to retrieve.\n     *\n     * @param {number} rowIndex\n     * Row index of the cell to retrieve.\n     *\n     * @return {Highcharts.DataTableCellType|undefined}\n     * Returns the cell value or `undefined`.\n     */\n    DataTable.prototype.getCell = function (columnName, rowIndex) {\n        var table = this;\n        var column = table.columns[columnName];\n        if (column) {\n            return column[rowIndex];\n        }\n    };\n    /**\n     * Fetches a cell value for the given row as a boolean.\n     *\n     * @function Highcharts.DataTable#getCellAsBoolean\n     *\n     * @param {string} columnName\n     * Column name to fetch.\n     *\n     * @param {number} rowIndex\n     * Row index to fetch.\n     *\n     * @return {boolean}\n     * Returns the cell value of the row as a boolean.\n     */\n    DataTable.prototype.getCellAsBoolean = function (columnName, rowIndex) {\n        var table = this;\n        var column = table.columns[columnName];\n        return !!(column && column[rowIndex]);\n    };\n    /**\n     * Fetches a cell value for the given row as a number.\n     *\n     * @function Highcharts.DataTable#getCellAsNumber\n     *\n     * @param {string} columnName\n     * Column name or to fetch.\n     *\n     * @param {number} rowIndex\n     * Row index to fetch.\n     *\n     * @param {boolean} [useNaN]\n     * Whether to return NaN instead of `null` and `undefined`.\n     *\n     * @return {number|null}\n     * Returns the cell value of the row as a number.\n     */\n    DataTable.prototype.getCellAsNumber = function (columnName, rowIndex, useNaN) {\n        var table = this;\n        var column = table.columns[columnName];\n        var cellValue = (column && column[rowIndex]);\n        switch (typeof cellValue) {\n            case 'boolean':\n                return (cellValue ? 1 : 0);\n            case 'number':\n                return (isNaN(cellValue) && !useNaN ? null : cellValue);\n        }\n        cellValue = parseFloat(\"\".concat(cellValue !== null && cellValue !== void 0 ? cellValue : ''));\n        return (isNaN(cellValue) && !useNaN ? null : cellValue);\n    };\n    /**\n     * Fetches a cell value for the given row as a string.\n     *\n     * @function Highcharts.DataTable#getCellAsString\n     *\n     * @param {string} columnName\n     * Column name to fetch.\n     *\n     * @param {number} rowIndex\n     * Row index to fetch.\n     *\n     * @return {string}\n     * Returns the cell value of the row as a string.\n     */\n    DataTable.prototype.getCellAsString = function (columnName, rowIndex) {\n        var table = this;\n        var column = table.columns[columnName];\n        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n        return \"\".concat((column && column[rowIndex]));\n    };\n    /**\n     * Fetches the given column by the canonical column name.\n     * This function is a simplified wrap of {@link getColumns}.\n     *\n     * @function Highcharts.DataTable#getColumn\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @param {boolean} [asReference]\n     * Whether to return the column as a readonly reference.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    DataTable.prototype.getColumn = function (columnName, asReference) {\n        return this.getColumns([columnName], asReference)[columnName];\n    };\n    /**\n     * Fetches the given column by the canonical column name, and\n     * validates the type of the first few cells. If the first defined cell is\n     * of type number, it assumes for performance reasons, that all cells are of\n     * type number or `null`. Otherwise it will convert all cells to number\n     * type, except `null`.\n     *\n     * @deprecated\n     *\n     * @function Highcharts.DataTable#getColumnAsNumbers\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @param {boolean} [useNaN]\n     * Whether to use NaN instead of `null` and `undefined`.\n     *\n     * @return {Array<(number|null)>}\n     * A copy of the column, or an empty array if not found.\n     */\n    DataTable.prototype.getColumnAsNumbers = function (columnName, useNaN) {\n        var table = this,\n            columns = table.columns;\n        var column = columns[columnName],\n            columnAsNumber = [];\n        if (column) {\n            var columnLength = column.length;\n            if (useNaN) {\n                for (var i = 0; i < columnLength; ++i) {\n                    columnAsNumber.push(table.getCellAsNumber(columnName, i, true));\n                }\n            }\n            else {\n                for (var i = 0, cellValue = void 0; i < columnLength; ++i) {\n                    cellValue = column[i];\n                    if (typeof cellValue === 'number') {\n                        // Assume unmixed data for performance reasons\n                        return column.slice();\n                    }\n                    if (cellValue !== null &&\n                        typeof cellValue !== 'undefined') {\n                        break;\n                    }\n                }\n                for (var i = 0; i < columnLength; ++i) {\n                    columnAsNumber.push(table.getCellAsNumber(columnName, i));\n                }\n            }\n        }\n        return columnAsNumber;\n    };\n    /**\n     * Fetches all column names.\n     *\n     * @function Highcharts.DataTable#getColumnNames\n     *\n     * @return {Array<string>}\n     * Returns all column names.\n     */\n    DataTable.prototype.getColumnNames = function () {\n        var table = this,\n            columnNames = Object.keys(table.columns);\n        return columnNames;\n    };\n    /**\n     * Retrieves all or the given columns.\n     *\n     * @function Highcharts.DataTable#getColumns\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @param {boolean} [asReference]\n     * Whether to return columns as a readonly reference.\n     *\n     * @param {boolean} [asBasicColumns]\n     * Whether to transform all typed array columns to normal arrays.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    DataTable.prototype.getColumns = function (columnNames, asReference, asBasicColumns) {\n        var table = this,\n            tableColumns = table.columns,\n            columns = {};\n        columnNames = (columnNames || Object.keys(tableColumns));\n        for (var i = 0, iEnd = columnNames.length, column = void 0, columnName = void 0; i < iEnd; ++i) {\n            columnName = columnNames[i];\n            column = tableColumns[columnName];\n            if (column) {\n                if (asReference) {\n                    columns[columnName] = column;\n                }\n                else if (asBasicColumns && !Array.isArray(column)) {\n                    columns[columnName] = Array.from(column);\n                }\n                else {\n                    columns[columnName] = column.slice();\n                }\n            }\n        }\n        return columns;\n    };\n    /**\n     * Takes the original row index and returns the local row index in the\n     * modified table for which this function is called.\n     *\n     * @param {number} originalRowIndex\n     * Original row index to get the local row index for.\n     *\n     * @return {number|undefined}\n     * Returns the local row index or `undefined` if not found.\n     */\n    DataTable.prototype.getLocalRowIndex = function (originalRowIndex) {\n        var localRowIndexes = this.localRowIndexes;\n        if (localRowIndexes) {\n            return localRowIndexes[originalRowIndex];\n        }\n        return originalRowIndex;\n    };\n    /**\n     * Retrieves the modifier for the table.\n     * @private\n     *\n     * @return {Highcharts.DataModifier|undefined}\n     * Returns the modifier or `undefined`.\n     */\n    DataTable.prototype.getModifier = function () {\n        return this.modifier;\n    };\n    /**\n     * Takes the local row index and returns the index of the corresponding row\n     * in the original table.\n     *\n     * @param {number} rowIndex\n     * Local row index to get the original row index for.\n     *\n     * @return {number|undefined}\n     * Returns the original row index or `undefined` if not found.\n     */\n    DataTable.prototype.getOriginalRowIndex = function (rowIndex) {\n        var originalRowIndexes = this.originalRowIndexes;\n        if (originalRowIndexes) {\n            return originalRowIndexes[rowIndex];\n        }\n        return rowIndex;\n    };\n    /**\n     * Retrieves the row at a given index. This function is a simplified wrap of\n     * {@link getRows}.\n     *\n     * @function Highcharts.DataTable#getRow\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names in order to retrieve.\n     *\n     * @return {Highcharts.DataTableRow}\n     * Returns the row values, or `undefined` if not found.\n     */\n    DataTable.prototype.getRow = function (rowIndex, columnNames) {\n        return this.getRows(rowIndex, 1, columnNames)[0];\n    };\n    /**\n     * Returns the number of rows in this table.\n     *\n     * @function Highcharts.DataTable#getRowCount\n     *\n     * @return {number}\n     * Number of rows in this table.\n     */\n    DataTable.prototype.getRowCount = function () {\n        // @todo Implement via property getter `.length` browsers supported\n        return this.rowCount;\n    };\n    /**\n     * Retrieves the index of the first row matching a specific cell value.\n     *\n     * @function Highcharts.DataTable#getRowIndexBy\n     *\n     * @param {string} columnName\n     * Column to search in.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Cell value to search for. `NaN` and `undefined` are not supported.\n     *\n     * @param {number} [rowIndexOffset]\n     * Index offset to start searching.\n     *\n     * @return {number|undefined}\n     * Index of the first row matching the cell value.\n     */\n    DataTable.prototype.getRowIndexBy = function (columnName, cellValue, rowIndexOffset) {\n        var table = this;\n        var column = table.columns[columnName];\n        if (column) {\n            var rowIndex = -1;\n            if (Array.isArray(column)) {\n                // Normal array\n                rowIndex = column.indexOf(cellValue, rowIndexOffset);\n            }\n            else if (isNumber(cellValue)) {\n                // Typed array\n                rowIndex = column.indexOf(cellValue, rowIndexOffset);\n            }\n            if (rowIndex !== -1) {\n                return rowIndex;\n            }\n        }\n    };\n    /**\n     * Retrieves the row at a given index. This function is a simplified wrap of\n     * {@link getRowObjects}.\n     *\n     * @function Highcharts.DataTable#getRowObject\n     *\n     * @param {number} rowIndex\n     * Row index.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names and their order to retrieve.\n     *\n     * @return {Highcharts.DataTableRowObject}\n     * Returns the row values, or `undefined` if not found.\n     */\n    DataTable.prototype.getRowObject = function (rowIndex, columnNames) {\n        return this.getRowObjects(rowIndex, 1, columnNames)[0];\n    };\n    /**\n     * Fetches all or a number of rows.\n     *\n     * @function Highcharts.DataTable#getRowObjects\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to fetch. Defaults to first row at index `0`.\n     *\n     * @param {number} [rowCount]\n     * Number of rows to fetch. Defaults to maximal number of rows.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names and their order to retrieve.\n     *\n     * @return {Highcharts.DataTableRowObject}\n     * Returns retrieved rows.\n     */\n    DataTable.prototype.getRowObjects = function (rowIndex, rowCount, columnNames) {\n        if (rowIndex === void 0) { rowIndex = 0; }\n        if (rowCount === void 0) { rowCount = (this.rowCount - rowIndex); }\n        var table = this,\n            columns = table.columns,\n            rows = new Array(rowCount);\n        columnNames = (columnNames || Object.keys(columns));\n        for (var i = rowIndex, i2 = 0, iEnd = Math.min(table.rowCount, (rowIndex + rowCount)), column = void 0, row = void 0; i < iEnd; ++i, ++i2) {\n            row = rows[i2] = {};\n            for (var _i = 0, columnNames_1 = columnNames; _i < columnNames_1.length; _i++) {\n                var columnName = columnNames_1[_i];\n                column = columns[columnName];\n                row[columnName] = (column ? column[i] : void 0);\n            }\n        }\n        return rows;\n    };\n    /**\n     * Fetches all or a number of rows.\n     *\n     * @function Highcharts.DataTable#getRows\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to fetch. Defaults to first row at index `0`.\n     *\n     * @param {number} [rowCount]\n     * Number of rows to fetch. Defaults to maximal number of rows.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names and their order to retrieve.\n     *\n     * @return {Highcharts.DataTableRow}\n     * Returns retrieved rows.\n     */\n    DataTable.prototype.getRows = function (rowIndex, rowCount, columnNames) {\n        if (rowIndex === void 0) { rowIndex = 0; }\n        if (rowCount === void 0) { rowCount = (this.rowCount - rowIndex); }\n        var table = this,\n            columns = table.columns,\n            rows = new Array(rowCount);\n        columnNames = (columnNames || Object.keys(columns));\n        for (var i = rowIndex, i2 = 0, iEnd = Math.min(table.rowCount, (rowIndex + rowCount)), column = void 0, row = void 0; i < iEnd; ++i, ++i2) {\n            row = rows[i2] = [];\n            for (var _i = 0, columnNames_2 = columnNames; _i < columnNames_2.length; _i++) {\n                var columnName = columnNames_2[_i];\n                column = columns[columnName];\n                row.push(column ? column[i] : void 0);\n            }\n        }\n        return rows;\n    };\n    /**\n     * Returns the unique version tag of the current state of the table.\n     *\n     * @function Highcharts.DataTable#getVersionTag\n     *\n     * @return {string}\n     * Unique version tag.\n     */\n    DataTable.prototype.getVersionTag = function () {\n        return this.versionTag;\n    };\n    /**\n     * Checks for given column names.\n     *\n     * @function Highcharts.DataTable#hasColumns\n     *\n     * @param {Array<string>} columnNames\n     * Column names to check.\n     *\n     * @return {boolean}\n     * Returns `true` if all columns have been found, otherwise `false`.\n     */\n    DataTable.prototype.hasColumns = function (columnNames) {\n        var table = this,\n            columns = table.columns;\n        for (var i = 0, iEnd = columnNames.length, columnName = void 0; i < iEnd; ++i) {\n            columnName = columnNames[i];\n            if (!columns[columnName]) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * Searches for a specific cell value.\n     *\n     * @function Highcharts.DataTable#hasRowWith\n     *\n     * @param {string} columnName\n     * Column to search in.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Cell value to search for. `NaN` and `undefined` are not supported.\n     *\n     * @return {boolean}\n     * True, if a row has been found, otherwise false.\n     */\n    DataTable.prototype.hasRowWith = function (columnName, cellValue) {\n        var table = this;\n        var column = table.columns[columnName];\n        // Normal array\n        if (Array.isArray(column)) {\n            return (column.indexOf(cellValue) !== -1);\n        }\n        // Typed array\n        if (defined(cellValue) && Number.isFinite(cellValue)) {\n            return (column.indexOf(+cellValue) !== -1);\n        }\n        return false;\n    };\n    /**\n     * Registers a callback for a specific event.\n     *\n     * @function Highcharts.DataTable#on\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {Highcharts.EventCallbackFunction<Highcharts.DataTable>} callback\n     * Function to register for an event callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the event.\n     */\n    DataTable.prototype.on = function (type, callback) {\n        return DataTable_addEvent(this, type, callback);\n    };\n    /**\n     * Renames a column of cell values.\n     *\n     * @function Highcharts.DataTable#renameColumn\n     *\n     * @param {string} columnName\n     * Name of the column to be renamed.\n     *\n     * @param {string} newColumnName\n     * New name of the column. An existing column with the same name will be\n     * replaced.\n     *\n     * @return {boolean}\n     * Returns `true` if successful, `false` if the column was not found.\n     */\n    DataTable.prototype.renameColumn = function (columnName, newColumnName) {\n        var table = this,\n            columns = table.columns;\n        if (columns[columnName]) {\n            if (columnName !== newColumnName) {\n                columns[newColumnName] = columns[columnName];\n                delete columns[columnName];\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Sets a cell value based on the row index and column.  Will\n     * insert a new column, if not found.\n     *\n     * @function Highcharts.DataTable#setCell\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index to set.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Cell value to set.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setCell\n     * @emits #afterSetCell\n     */\n    DataTable.prototype.setCell = function (columnName, rowIndex, cellValue, eventDetail) {\n        var table = this,\n            columns = table.columns,\n            modifier = table.modifier;\n        var column = columns[columnName];\n        if (column && column[rowIndex] === cellValue) {\n            return;\n        }\n        table.emit({\n            type: 'setCell',\n            cellValue: cellValue,\n            columnName: columnName,\n            detail: eventDetail,\n            rowIndex: rowIndex\n        });\n        if (!column) {\n            column = columns[columnName] = new Array(table.rowCount);\n        }\n        if (rowIndex >= table.rowCount) {\n            table.rowCount = (rowIndex + 1);\n        }\n        column[rowIndex] = cellValue;\n        if (modifier) {\n            modifier.modifyCell(table, columnName, rowIndex, cellValue);\n        }\n        table.emit({\n            type: 'afterSetCell',\n            cellValue: cellValue,\n            columnName: columnName,\n            detail: eventDetail,\n            rowIndex: rowIndex\n        });\n    };\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found.\n     *\n     * @function Highcharts.DataTable#setColumns\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Keep undefined to reset.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @param {boolean} [typeAsOriginal=false]\n     * Determines whether the original column retains its type when data\n     * replaced. If `true`, the original column keeps its type. If not\n     * (default), the original column will adopt the type of the replacement\n     * column.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTable.prototype.setColumns = function (columns, rowIndex, eventDetail, typeAsOriginal) {\n        var table = this,\n            tableColumns = table.columns,\n            tableModifier = table.modifier,\n            columnNames = Object.keys(columns);\n        var rowCount = table.rowCount;\n        table.emit({\n            type: 'setColumns',\n            columns: columns,\n            columnNames: columnNames,\n            detail: eventDetail,\n            rowIndex: rowIndex\n        });\n        if (!defined(rowIndex) && !typeAsOriginal) {\n            _super.prototype.setColumns.call(this, columns, rowIndex, extend(eventDetail, { silent: true }));\n        }\n        else {\n            for (var i = 0, iEnd = columnNames.length, column = void 0, tableColumn = void 0, columnName = void 0, ArrayConstructor = void 0; i < iEnd; ++i) {\n                columnName = columnNames[i];\n                column = columns[columnName];\n                tableColumn = tableColumns[columnName];\n                ArrayConstructor = Object.getPrototypeOf((tableColumn && typeAsOriginal) ? tableColumn : column).constructor;\n                if (!tableColumn) {\n                    tableColumn = new ArrayConstructor(rowCount);\n                }\n                else if (ArrayConstructor === Array) {\n                    if (!Array.isArray(tableColumn)) {\n                        tableColumn = Array.from(tableColumn);\n                    }\n                }\n                else if (tableColumn.length < rowCount) {\n                    tableColumn =\n                        new ArrayConstructor(rowCount);\n                    tableColumn.set(tableColumns[columnName]);\n                }\n                tableColumns[columnName] = tableColumn;\n                for (var i_1 = (rowIndex || 0), iEnd_1 = column.length; i_1 < iEnd_1; ++i_1) {\n                    tableColumn[i_1] = column[i_1];\n                }\n                rowCount = Math.max(rowCount, column.length);\n            }\n            this.applyRowCount(rowCount);\n        }\n        if (tableModifier) {\n            tableModifier.modifyColumns(table, columns, rowIndex || 0);\n        }\n        table.emit({\n            type: 'afterSetColumns',\n            columns: columns,\n            columnNames: columnNames,\n            detail: eventDetail,\n            rowIndex: rowIndex\n        });\n    };\n    /**\n     * Sets or unsets the modifier for the table.\n     *\n     * @param {Highcharts.DataModifier} [modifier]\n     * Modifier to set, or `undefined` to unset.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<Highcharts.DataTable>}\n     * Resolves to this table if successful, or rejects on failure.\n     *\n     * @emits #setModifier\n     * @emits #afterSetModifier\n     */\n    DataTable.prototype.setModifier = function (modifier, eventDetail) {\n        var table = this;\n        var promise;\n        table.emit({\n            type: 'setModifier',\n            detail: eventDetail,\n            modifier: modifier,\n            modified: table.modified\n        });\n        table.modified = table;\n        table.modifier = modifier;\n        if (modifier) {\n            promise = modifier.modify(table);\n        }\n        else {\n            promise = Promise.resolve(table);\n        }\n        return promise\n            .then(function (table) {\n            table.emit({\n                type: 'afterSetModifier',\n                detail: eventDetail,\n                modifier: modifier,\n                modified: table.modified\n            });\n            return table;\n        })['catch'](function (error) {\n            table.emit({\n                type: 'setModifierError',\n                error: error,\n                modifier: modifier,\n                modified: table.modified\n            });\n            throw error;\n        });\n    };\n    /**\n     * Sets the original row indexes for the table. It is used to keep the\n     * reference to the original rows when modifying the table.\n     *\n     * @param {Array<number|undefined>} originalRowIndexes\n     * Original row indexes array.\n     *\n     * @param {boolean} omitLocalRowIndexes\n     * Whether to omit the local row indexes calculation. Defaults to `false`.\n     */\n    DataTable.prototype.setOriginalRowIndexes = function (originalRowIndexes, omitLocalRowIndexes) {\n        if (omitLocalRowIndexes === void 0) { omitLocalRowIndexes = false; }\n        this.originalRowIndexes = originalRowIndexes;\n        if (omitLocalRowIndexes) {\n            return;\n        }\n        var modifiedIndexes = this.localRowIndexes = [];\n        for (var i = 0, iEnd = originalRowIndexes.length, originalIndex = void 0; i < iEnd; ++i) {\n            originalIndex = originalRowIndexes[i];\n            if (defined(originalIndex)) {\n                modifiedIndexes[originalIndex] = i;\n            }\n        }\n    };\n    /**\n     * Sets cell values of a row. Will insert a new row, if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     *\n     * Note: This function is just a simplified wrap of\n     * {@link Highcharts.DataTable#setRows}.\n     *\n     * @function Highcharts.DataTable#setRow\n     *\n     * @param {Highcharts.DataTableRow|Highcharts.DataTableRowObject} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefind` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setRows\n     * @emits #afterSetRows\n     */\n    DataTable.prototype.setRow = function (row, rowIndex, insert, eventDetail) {\n        this.setRows([row], rowIndex, insert, eventDetail);\n    };\n    /**\n     * Sets cell values for multiple rows. Will insert new rows, if no index was\n     * was provided, or if the index is higher than the total number of table\n     * rows.\n     *\n     * @function Highcharts.DataTable#setRows\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Row values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to set. Leave `undefined` to add as new rows.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setRows\n     * @emits #afterSetRows\n     */\n    DataTable.prototype.setRows = function (rows, rowIndex, insert, eventDetail) {\n        if (rowIndex === void 0) { rowIndex = this.rowCount; }\n        var table = this,\n            columns = table.columns,\n            columnNames = Object.keys(columns),\n            modifier = table.modifier,\n            rowCount = rows.length;\n        table.emit({\n            type: 'setRows',\n            detail: eventDetail,\n            rowCount: rowCount,\n            rowIndex: rowIndex,\n            rows: rows\n        });\n        for (var i = 0, i2 = rowIndex, row = void 0; i < rowCount; ++i, ++i2) {\n            row = rows[i];\n            if (row === DataTable.NULL) {\n                for (var j = 0, jEnd = columnNames.length; j < jEnd; ++j) {\n                    var column = columns[columnNames[j]];\n                    if (insert) {\n                        columns[columnNames[j]] = Data_ColumnUtils.splice(column, i2, 0, true, [null]).array;\n                    }\n                    else {\n                        column[i2] = null;\n                    }\n                }\n            }\n            else if (row instanceof Array) {\n                for (var j = 0, jEnd = columnNames.length; j < jEnd; ++j) {\n                    columns[columnNames[j]][i2] = row[j];\n                }\n            }\n            else {\n                _super.prototype.setRow.call(this, row, i2, void 0, { silent: true });\n            }\n        }\n        var indexRowCount = insert ?\n                rowCount + rows.length :\n                rowIndex + rowCount;\n        if (indexRowCount > table.rowCount) {\n            table.rowCount = indexRowCount;\n            for (var i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                var columnName = columnNames[i];\n                columns[columnName] = Data_ColumnUtils.setLength(columns[columnName], indexRowCount);\n            }\n        }\n        if (modifier) {\n            modifier.modifyRows(table, rows, rowIndex);\n        }\n        table.emit({\n            type: 'afterSetRows',\n            detail: eventDetail,\n            rowCount: rowCount,\n            rowIndex: rowIndex,\n            rows: rows\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Null state for a row record. In some cases, a row in a table may not\n     * contain any data or may be invalid. In these cases, a null state can be\n     * used to indicate that the row record is empty or invalid.\n     *\n     * @name Highcharts.DataTable.NULL\n     * @type {Highcharts.DataTableRowObject}\n     *\n     * @see {@link Highcharts.DataTable.isNull} for a null test.\n     *\n     * @example\n     * table.setRows([DataTable.NULL, DataTable.NULL], 10);\n     */\n    DataTable.NULL = {};\n    /**\n     * Semantic version string of the DataTable class.\n     * @internal\n     */\n    DataTable.version = '1.0.0';\n    return DataTable;\n}(Data_DataTableCore));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataTable = (DataTable);\n\n;// ./code/es5/es-modules/Data/Connectors/DataConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Wojciech Chmiel\n *  - Gøran Slettemark\n *\n * */\n\n\n\n\nvar DataConnector_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, DataConnector_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, DataConnector_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Abstract class providing an interface for managing a DataConnector.\n *\n * @private\n */\nvar DataConnector = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructor for the connector class.\n     *\n     * @param {DataConnector.UserOptions} [options]\n     * Options to use in the connector.\n     */\n    function DataConnector(options) {\n        if (options === void 0) { options = {}; }\n        this.table = new Data_DataTable(options.dataTable);\n        this.metadata = options.metadata || { columns: {} };\n    }\n    Object.defineProperty(DataConnector.prototype, \"polling\", {\n        /**\n         * Poll timer ID, if active.\n         */\n        get: function () {\n            return !!this._polling;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Method for adding metadata for a single column.\n     *\n     * @param {string} name\n     * The name of the column to be described.\n     *\n     * @param {DataConnector.MetaColumn} columnMeta\n     * The metadata to apply to the column.\n     */\n    DataConnector.prototype.describeColumn = function (name, columnMeta) {\n        var connector = this,\n            columns = connector.metadata.columns;\n        columns[name] = DataConnector_merge(columns[name] || {}, columnMeta);\n    };\n    /**\n     * Method for applying columns meta information to the whole DataConnector.\n     *\n     * @param {Highcharts.Dictionary<DataConnector.MetaColumn>} columns\n     * Pairs of column names and MetaColumn objects.\n     */\n    DataConnector.prototype.describeColumns = function (columns) {\n        var connector = this,\n            columnNames = Object.keys(columns);\n        var columnName;\n        while (typeof (columnName = columnNames.pop()) === 'string') {\n            connector.describeColumn(columnName, columns[columnName]);\n        }\n    };\n    /**\n     * Emits an event on the connector to all registered callbacks of this\n     * event.\n     *\n     * @param {DataConnector.Event} [e]\n     * Event object containing additional event information.\n     */\n    DataConnector.prototype.emit = function (e) {\n        DataConnector_fireEvent(this, e.type, e);\n    };\n    /**\n     * Returns the order of columns.\n     *\n     * @param {boolean} [usePresentationState]\n     * Whether to use the column order of the presentation state of the table.\n     *\n     * @return {Array<string>|undefined}\n     * Order of columns.\n     */\n    DataConnector.prototype.getColumnOrder = function (\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    usePresentationState) {\n        var connector = this,\n            columns = connector.metadata.columns,\n            names = Object.keys(columns || {});\n        if (names.length) {\n            return names.sort(function (a, b) { return (pick(columns[a].index, 0) - pick(columns[b].index, 0)); });\n        }\n    };\n    /**\n     * Retrieves the columns of the dataTable,\n     * applies column order from meta.\n     *\n     * @param {boolean} [usePresentationOrder]\n     * Whether to use the column order of the presentation state of the table.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * An object with the properties `columnNames` and `columnValues`\n     */\n    DataConnector.prototype.getSortedColumns = function (usePresentationOrder) {\n        return this.table.getColumns(this.getColumnOrder(usePresentationOrder));\n    };\n    /**\n     * The default load method, which fires the `afterLoad` event\n     *\n     * @return {Promise<DataConnector>}\n     * The loaded connector.\n     *\n     * @emits DataConnector#afterLoad\n     */\n    DataConnector.prototype.load = function () {\n        DataConnector_fireEvent(this, 'afterLoad', { table: this.table });\n        return Promise.resolve(this);\n    };\n    /**\n     * Registers a callback for a specific connector event.\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {DataEventEmitter.Callback} callback\n     * Function to register for the connector callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the connector event.\n     */\n    DataConnector.prototype.on = function (type, callback) {\n        return DataConnector_addEvent(this, type, callback);\n    };\n    /**\n     * The default save method, which fires the `afterSave` event.\n     *\n     * @return {Promise<DataConnector>}\n     * The saved connector.\n     *\n     * @emits DataConnector#afterSave\n     * @emits DataConnector#saveError\n     */\n    DataConnector.prototype.save = function () {\n        DataConnector_fireEvent(this, 'saveError', { table: this.table });\n        return Promise.reject(new Error('Not implemented'));\n    };\n    /**\n     * Sets the index and order of columns.\n     *\n     * @param {Array<string>} columnNames\n     * Order of columns.\n     */\n    DataConnector.prototype.setColumnOrder = function (columnNames) {\n        var connector = this;\n        for (var i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n            connector.describeColumn(columnNames[i], { index: i });\n        }\n    };\n    DataConnector.prototype.setModifierOptions = function (modifierOptions) {\n        var _this = this;\n        var ModifierClass = (modifierOptions &&\n                Modifiers_DataModifier.types[modifierOptions.type]);\n        return this.table\n            .setModifier(ModifierClass ?\n            new ModifierClass(modifierOptions) :\n            void 0)\n            .then(function () { return _this; });\n    };\n    /**\n     * Starts polling new data after the specific time span in milliseconds.\n     *\n     * @param {number} refreshTime\n     * Refresh time in milliseconds between polls.\n     */\n    DataConnector.prototype.startPolling = function (refreshTime) {\n        if (refreshTime === void 0) { refreshTime = 1000; }\n        var connector = this;\n        window.clearTimeout(connector._polling);\n        connector._polling = window.setTimeout(function () { return connector\n            .load()['catch'](function (error) { return connector.emit({\n            type: 'loadError',\n            error: error,\n            table: connector.table\n        }); })\n            .then(function () {\n            if (connector._polling) {\n                connector.startPolling(refreshTime);\n            }\n        }); }, refreshTime);\n    };\n    /**\n     * Stops polling data.\n     */\n    DataConnector.prototype.stopPolling = function () {\n        var connector = this;\n        window.clearTimeout(connector._polling);\n        delete connector._polling;\n    };\n    /**\n     * Retrieves metadata from a single column.\n     *\n     * @param {string} name\n     * The identifier for the column that should be described\n     *\n     * @return {DataConnector.MetaColumn|undefined}\n     * Returns a MetaColumn object if found.\n     */\n    DataConnector.prototype.whatIs = function (name) {\n        return this.metadata.columns[name];\n    };\n    return DataConnector;\n}());\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (DataConnector) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Registry as a record object with connector names and their class.\n     */\n    DataConnector.types = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a connector class to the registry. The connector has to provide the\n     * `DataConnector.options` property and the `DataConnector.load` method to\n     * modify the table.\n     *\n     * @private\n     *\n     * @param {string} key\n     * Registry key of the connector class.\n     *\n     * @param {DataConnectorType} DataConnectorClass\n     * Connector class (aka class constructor) to register.\n     *\n     * @return {boolean}\n     * Returns true, if the registration was successful. False is returned, if\n     * their is already a connector registered with this key.\n     */\n    function registerType(key, DataConnectorClass) {\n        return (!!key &&\n            !DataConnector.types[key] &&\n            !!(DataConnector.types[key] = DataConnectorClass));\n    }\n    DataConnector.registerType = registerType;\n})(DataConnector || (DataConnector = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Connectors_DataConnector = (DataConnector);\n\n;// ./code/es5/es-modules/Data/Converters/DataConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Sebastian Bochan\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *  - Wojciech Chmiel\n *  - Jomar Hønsi\n *\n * */\n\n\n\nvar DataConverter_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, DataConverter_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, DataConverter_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, DataConverter_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Base class providing an interface and basic methods for a DataConverter\n *\n * @private\n */\nvar DataConverter = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the DataConverter.\n     *\n     * @param {DataConverter.UserOptions} [options]\n     * Options for the DataConverter.\n     */\n    function DataConverter(options) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        /**\n         * A collection of available date formats.\n         */\n        this.dateFormats = {\n            'YYYY/mm/dd': {\n                regex: /^(\\d{4})([\\-\\.\\/])(\\d{1,2})\\2(\\d{1,2})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[1], match[3] - 1, +match[4]) :\n                        NaN);\n                }\n            },\n            'dd/mm/YYYY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{4})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[4], match[3] - 1, +match[1]) :\n                        NaN);\n                },\n                alternative: 'mm/dd/YYYY' // Different format with the same regex\n            },\n            'mm/dd/YYYY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{4})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[4], match[1] - 1, +match[3]) :\n                        NaN);\n                }\n            },\n            'dd/mm/YY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{2})$/,\n                parser: function (match) {\n                    var d = new Date();\n                    if (!match) {\n                        return NaN;\n                    }\n                    var year = +match[4];\n                    if (year > (d.getFullYear() - 2000)) {\n                        year += 1900;\n                    }\n                    else {\n                        year += 2000;\n                    }\n                    return Date.UTC(year, match[3] - 1, +match[1]);\n                },\n                alternative: 'mm/dd/YY' // Different format with the same regex\n            },\n            'mm/dd/YY': {\n                regex: /^(\\d{1,2})([\\-\\.\\/])(\\d{1,2})\\2(\\d{2})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[4] + 2000, match[1] - 1, +match[3]) :\n                        NaN);\n                }\n            }\n        };\n        var mergedOptions = DataConverter_merge(DataConverter.defaultOptions,\n            options);\n        var regExpPoint = mergedOptions.decimalPoint;\n        if (regExpPoint === '.' || regExpPoint === ',') {\n            regExpPoint = regExpPoint === '.' ? '\\\\.' : ',';\n            this.decimalRegExp =\n                new RegExp('^(-?[0-9]+)' + regExpPoint + '([0-9]+)$');\n        }\n        this.options = mergedOptions;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Converts a value to a boolean.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {boolean}\n     * Converted value as a boolean.\n     */\n    DataConverter.prototype.asBoolean = function (value) {\n        if (typeof value === 'boolean') {\n            return value;\n        }\n        if (typeof value === 'string') {\n            return value !== '' && value !== '0' && value !== 'false';\n        }\n        return !!this.asNumber(value);\n    };\n    /**\n     * Converts a value to a Date.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {globalThis.Date}\n     * Converted value as a Date.\n     */\n    DataConverter.prototype.asDate = function (value) {\n        var timestamp;\n        if (typeof value === 'string') {\n            timestamp = this.parseDate(value);\n        }\n        else if (typeof value === 'number') {\n            timestamp = value;\n        }\n        else if (value instanceof Date) {\n            return value;\n        }\n        else {\n            timestamp = this.parseDate(this.asString(value));\n        }\n        return new Date(timestamp);\n    };\n    /**\n     * Casts a string value to it's guessed type\n     *\n     * @param {*} value\n     * The value to examine.\n     *\n     * @return {number|string|Date}\n     * The converted value.\n     */\n    DataConverter.prototype.asGuessedType = function (value) {\n        var converter = this,\n            typeMap = {\n                'number': converter.asNumber,\n                'Date': converter.asDate,\n                'string': converter.asString\n            };\n        return typeMap[converter.guessType(value)].call(converter, value);\n    };\n    /**\n     * Converts a value to a number.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {number}\n     * Converted value as a number.\n     */\n    DataConverter.prototype.asNumber = function (value) {\n        if (typeof value === 'number') {\n            return value;\n        }\n        if (typeof value === 'boolean') {\n            return value ? 1 : 0;\n        }\n        if (typeof value === 'string') {\n            var decimalRegex = this.decimalRegExp;\n            if (value.indexOf(' ') > -1) {\n                value = value.replace(/\\s+/g, '');\n            }\n            if (decimalRegex) {\n                if (!decimalRegex.test(value)) {\n                    return NaN;\n                }\n                value = value.replace(decimalRegex, '$1.$2');\n            }\n            return parseFloat(value);\n        }\n        if (value instanceof Date) {\n            return value.getDate();\n        }\n        if (value) {\n            return value.getRowCount();\n        }\n        return NaN;\n    };\n    /**\n     * Converts a value to a string.\n     *\n     * @param {DataConverter.Type} value\n     * Value to convert.\n     *\n     * @return {string}\n     * Converted value as a string.\n     */\n    DataConverter.prototype.asString = function (value) {\n        return '' + value;\n    };\n    /**\n     * Tries to guess the date format\n     *  - Check if either month candidate exceeds 12\n     *  - Check if year is missing (use current year)\n     *  - Check if a shortened year format is used (e.g. 1/1/99)\n     *  - If no guess can be made, the user must be prompted\n     * data is the data to deduce a format based on\n     * @private\n     *\n     * @param {Array<string>} data\n     * Data to check the format.\n     *\n     * @param {number} limit\n     * Max data to check the format.\n     *\n     * @param {boolean} save\n     * Whether to save the date format in the converter options.\n     */\n    DataConverter.prototype.deduceDateFormat = function (data, limit, save) {\n        var parser = this,\n            stable = [],\n            max = [];\n        var format = 'YYYY/mm/dd', thing, guessedFormat = [], i = 0, madeDeduction = false, \n            /// candidates = {},\n            elem, j;\n        if (!limit || limit > data.length) {\n            limit = data.length;\n        }\n        for (; i < limit; i++) {\n            if (typeof data[i] !== 'undefined' &&\n                data[i] && data[i].length) {\n                thing = data[i]\n                    .trim()\n                    .replace(/[\\-\\.\\/]/g, ' ')\n                    .split(' ');\n                guessedFormat = [\n                    '',\n                    '',\n                    ''\n                ];\n                for (j = 0; j < thing.length; j++) {\n                    if (j < guessedFormat.length) {\n                        elem = parseInt(thing[j], 10);\n                        if (elem) {\n                            max[j] = (!max[j] || max[j] < elem) ? elem : max[j];\n                            if (typeof stable[j] !== 'undefined') {\n                                if (stable[j] !== elem) {\n                                    stable[j] = false;\n                                }\n                            }\n                            else {\n                                stable[j] = elem;\n                            }\n                            if (elem > 31) {\n                                if (elem < 100) {\n                                    guessedFormat[j] = 'YY';\n                                }\n                                else {\n                                    guessedFormat[j] = 'YYYY';\n                                }\n                                /// madeDeduction = true;\n                            }\n                            else if (elem > 12 &&\n                                elem <= 31) {\n                                guessedFormat[j] = 'dd';\n                                madeDeduction = true;\n                            }\n                            else if (!guessedFormat[j].length) {\n                                guessedFormat[j] = 'mm';\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        if (madeDeduction) {\n            // This handles a few edge cases with hard to guess dates\n            for (j = 0; j < stable.length; j++) {\n                if (stable[j] !== false) {\n                    if (max[j] > 12 &&\n                        guessedFormat[j] !== 'YY' &&\n                        guessedFormat[j] !== 'YYYY') {\n                        guessedFormat[j] = 'YY';\n                    }\n                }\n                else if (max[j] > 12 && guessedFormat[j] === 'mm') {\n                    guessedFormat[j] = 'dd';\n                }\n            }\n            // If the middle one is dd, and the last one is dd,\n            // the last should likely be year.\n            if (guessedFormat.length === 3 &&\n                guessedFormat[1] === 'dd' &&\n                guessedFormat[2] === 'dd') {\n                guessedFormat[2] = 'YY';\n            }\n            format = guessedFormat.join('/');\n            // If the caculated format is not valid, we need to present an\n            // error.\n        }\n        // Save the deduced format in the converter options.\n        if (save) {\n            parser.options.dateFormat = format;\n        }\n        return format;\n    };\n    /**\n     * Emits an event on the DataConverter instance.\n     *\n     * @param {DataConverter.Event} [e]\n     * Event object containing additional event data\n     */\n    DataConverter.prototype.emit = function (e) {\n        DataConverter_fireEvent(this, e.type, e);\n    };\n    /**\n     * Initiates the data exporting. Should emit `exportError` on failure.\n     *\n     * @param {DataConnector} connector\n     * Connector to export from.\n     *\n     * @param {DataConverter.Options} [options]\n     * Options for the export.\n     */\n    DataConverter.prototype.export = function (\n    /* eslint-disable @typescript-eslint/no-unused-vars */\n    connector, options\n    /* eslint-enable @typescript-eslint/no-unused-vars */\n    ) {\n        this.emit({\n            type: 'exportError',\n            columns: [],\n            headers: []\n        });\n        throw new Error('Not implemented');\n    };\n    /**\n     * Getter for the data table.\n     *\n     * @return {DataTable}\n     * Table of parsed data.\n     */\n    DataConverter.prototype.getTable = function () {\n        throw new Error('Not implemented');\n    };\n    /**\n     * Guesses the potential type of a string value for parsing CSV etc.\n     *\n     * @param {*} value\n     * The value to examine.\n     *\n     * @return {'number'|'string'|'Date'}\n     * Type string, either `string`, `Date`, or `number`.\n     */\n    DataConverter.prototype.guessType = function (value) {\n        var converter = this;\n        var result = 'string';\n        if (typeof value === 'string') {\n            var trimedValue = converter.trim(\"\".concat(value)),\n                decimalRegExp = converter.decimalRegExp;\n            var innerTrimedValue = converter.trim(trimedValue,\n                true);\n            if (decimalRegExp) {\n                innerTrimedValue = (decimalRegExp.test(innerTrimedValue) ?\n                    innerTrimedValue.replace(decimalRegExp, '$1.$2') :\n                    '');\n            }\n            var floatValue = parseFloat(innerTrimedValue);\n            if (+innerTrimedValue === floatValue) {\n                // String is numeric\n                value = floatValue;\n            }\n            else {\n                // Determine if a date string\n                var dateValue = converter.parseDate(value);\n                result = DataConverter_isNumber(dateValue) ? 'Date' : 'string';\n            }\n        }\n        if (typeof value === 'number') {\n            // Greater than milliseconds in a year assumed timestamp\n            result = value > 365 * 24 * 3600 * 1000 ? 'Date' : 'number';\n        }\n        return result;\n    };\n    /**\n     * Registers a callback for a specific event.\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {DataEventEmitter.Callback} callback\n     * Function to register for an modifier callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the modifier event.\n     */\n    DataConverter.prototype.on = function (type, callback) {\n        return DataConverter_addEvent(this, type, callback);\n    };\n    /**\n     * Initiates the data parsing. Should emit `parseError` on failure.\n     *\n     * @param {DataConverter.UserOptions} options\n     * Options of the DataConverter.\n     */\n    DataConverter.prototype.parse = function (\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    options) {\n        this.emit({\n            type: 'parseError',\n            columns: [],\n            headers: []\n        });\n        throw new Error('Not implemented');\n    };\n    /**\n     * Parse a date and return it as a number.\n     *\n     * @param {string} value\n     * Value to parse.\n     *\n     * @param {string} dateFormatProp\n     * Which of the predefined date formats\n     * to use to parse date values.\n     */\n    DataConverter.prototype.parseDate = function (value, dateFormatProp) {\n        var converter = this,\n            options = converter.options;\n        var dateFormat = dateFormatProp || options.dateFormat,\n            result = NaN,\n            key,\n            format,\n            match;\n        if (options.parseDate) {\n            result = options.parseDate(value);\n        }\n        else {\n            // Auto-detect the date format the first time\n            if (!dateFormat) {\n                for (key in converter.dateFormats) { // eslint-disable-line guard-for-in\n                    format = converter.dateFormats[key];\n                    match = value.match(format.regex);\n                    if (match) {\n                        // `converter.options.dateFormat` = dateFormat = key;\n                        dateFormat = key;\n                        // `converter.options.alternativeFormat` =\n                        // format.alternative || '';\n                        result = format.parser(match);\n                        break;\n                    }\n                }\n                // Next time, use the one previously found\n            }\n            else {\n                format = converter.dateFormats[dateFormat];\n                if (!format) {\n                    // The selected format is invalid\n                    format = converter.dateFormats['YYYY/mm/dd'];\n                }\n                match = value.match(format.regex);\n                if (match) {\n                    result = format.parser(match);\n                }\n            }\n            // Fall back to Date.parse\n            if (!match) {\n                match = Date.parse(value);\n                // External tools like Date.js and MooTools extend Date object\n                // and returns a date.\n                if (typeof match === 'object' &&\n                    match !== null &&\n                    match.getTime) {\n                    result = (match.getTime() -\n                        match.getTimezoneOffset() *\n                            60000);\n                    // Timestamp\n                }\n                else if (DataConverter_isNumber(match)) {\n                    result = match - (new Date(match)).getTimezoneOffset() * 60000;\n                    if ( // Reset dates without year in Chrome\n                    value.indexOf('2001') === -1 &&\n                        (new Date(result)).getFullYear() === 2001) {\n                        result = NaN;\n                    }\n                }\n            }\n        }\n        return result;\n    };\n    /**\n     * Trim a string from whitespaces.\n     *\n     * @param {string} str\n     * String to trim.\n     *\n     * @param {boolean} [inside=false]\n     * Remove all spaces between numbers.\n     *\n     * @return {string}\n     * Trimed string\n     */\n    DataConverter.prototype.trim = function (str, inside) {\n        if (typeof str === 'string') {\n            str = str.replace(/^\\s+|\\s+$/g, '');\n            // Clear white space insdie the string, like thousands separators\n            if (inside && /^[\\d\\s]+$/.test(str)) {\n                str = str.replace(/\\s/g, '');\n            }\n        }\n        return str;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options\n     */\n    DataConverter.defaultOptions = {\n        dateFormat: '',\n        alternativeFormat: '',\n        startColumn: 0,\n        endColumn: Number.MAX_VALUE,\n        startRow: 0,\n        endRow: Number.MAX_VALUE,\n        firstRowAsNames: true,\n        switchRowsAndColumns: false\n    };\n    return DataConverter;\n}());\n/* *\n *\n *  Class Namespace\n *\n * */\n/**\n * Additionally provided types for events and conversion.\n */\n(function (DataConverter) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    /**\n     * Registry as a record object with connector names and their class.\n     */\n    DataConverter.types = {};\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a converter class to the registry.\n     *\n     * @private\n     *\n     * @param {string} key\n     * Registry key of the converter class.\n     *\n     * @param {DataConverterTypes} DataConverterClass\n     * Connector class (aka class constructor) to register.\n     *\n     * @return {boolean}\n     * Returns true, if the registration was successful. False is returned, if\n     * their is already a converter registered with this key.\n     */\n    function registerType(key, DataConverterClass) {\n        return (!!key &&\n            !DataConverter.types[key] &&\n            !!(DataConverter.types[key] = DataConverterClass));\n    }\n    DataConverter.registerType = registerType;\n    /**\n     * Converts an array of columns to a table instance. Second dimension of the\n     * array are the row cells.\n     *\n     * @param {Array<DataTable.Column>} [columns]\n     * Array to convert.\n     *\n     * @param {Array<string>} [headers]\n     * Column names to use.\n     *\n     * @return {DataTable}\n     * Table instance from the arrays.\n     */\n    function getTableFromColumns(columns, headers) {\n        if (columns === void 0) { columns = []; }\n        if (headers === void 0) { headers = []; }\n        var table = new Data_DataTable();\n        for (var i = 0, iEnd = Math.max(headers.length, columns.length); i < iEnd; ++i) {\n            table.setColumn(headers[i] || \"\".concat(i), columns[i]);\n        }\n        return table;\n    }\n    DataConverter.getTableFromColumns = getTableFromColumns;\n})(DataConverter || (DataConverter = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Converters_DataConverter = (DataConverter);\n\n;// ./code/es5/es-modules/Data/DataCursor.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * This class manages state cursors pointing on {@link Data.DataTable}. It\n * creates a relation between states of the user interface and the table cells,\n * columns, or rows.\n *\n * @class\n * @name Data.DataCursor\n */\nvar DataCursor = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function DataCursor(stateMap) {\n        if (stateMap === void 0) { stateMap = {}; }\n        this.emittingRegister = [];\n        this.listenerMap = {};\n        this.stateMap = stateMap;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * This function registers a listener for a specific state and table.\n     *\n     * @example\n     * ```TypeScript\n     * dataCursor.addListener(myTable.id, 'hover', (e: DataCursor.Event) => {\n     *     if (e.cursor.type === 'position') {\n     *         console.log(`Hover over row #${e.cursor.row}.`);\n     *     }\n     * });\n     * ```\n     *\n     * @function #addListener\n     *\n     * @param {Data.DataCursor.TableId} tableId\n     * The ID of the table to listen to.\n     *\n     * @param {Data.DataCursor.State} state\n     * The state on the table to listen to.\n     *\n     * @param {Data.DataCursor.Listener} listener\n     * The listener to register.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    DataCursor.prototype.addListener = function (tableId, state, listener) {\n        var listenerMap = this.listenerMap[tableId] = (this.listenerMap[tableId] ||\n                {});\n        var listeners = listenerMap[state] = (listenerMap[state] ||\n                []);\n        listeners.push(listener);\n        return this;\n    };\n    /**\n     * @private\n     */\n    DataCursor.prototype.buildEmittingTag = function (e) {\n        return (e.cursor.type === 'position' ?\n            [\n                e.table.id,\n                e.cursor.column,\n                e.cursor.row,\n                e.cursor.state,\n                e.cursor.type\n            ] :\n            [\n                e.table.id,\n                e.cursor.columns,\n                e.cursor.firstRow,\n                e.cursor.lastRow,\n                e.cursor.state,\n                e.cursor.type\n            ]).join('\\0');\n    };\n    /**\n     * This function emits a state cursor related to a table. It will provide\n     * lasting state cursors of the table to listeners.\n     *\n     * @example\n     * ```ts\n     * dataCursor.emit(myTable, {\n     *     type: 'position',\n     *     column: 'city',\n     *     row: 4,\n     *     state: 'hover',\n     * });\n     * ```\n     *\n     * @param {Data.DataTable} table\n     * The related table of the cursor.\n     *\n     * @param {Data.DataCursor.Type} cursor\n     * The state cursor to emit.\n     *\n     * @param {Event} [event]\n     * Optional event information from a related source.\n     *\n     * @param {boolean} [lasting]\n     * Whether this state cursor should be kept until it is cleared with\n     * {@link DataCursor#remitCursor}.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    DataCursor.prototype.emitCursor = function (table, cursor, event, lasting) {\n        var _a;\n        var tableId = table.id,\n            state = cursor.state,\n            listeners = (this.listenerMap[tableId] &&\n                this.listenerMap[tableId][state]);\n        if (listeners) {\n            var stateMap = this.stateMap[tableId] = ((_a = this.stateMap[tableId]) !== null && _a !== void 0 ? _a : {});\n            var cursors = stateMap[cursor.state] || [];\n            if (lasting) {\n                if (!cursors.length) {\n                    stateMap[cursor.state] = cursors;\n                }\n                if (DataCursor.getIndex(cursor, cursors) === -1) {\n                    cursors.push(cursor);\n                }\n            }\n            var e = {\n                    cursor: cursor,\n                    cursors: cursors,\n                    table: table\n                };\n            if (event) {\n                e.event = event;\n            }\n            var emittingRegister = this.emittingRegister,\n                emittingTag = this.buildEmittingTag(e);\n            if (emittingRegister.indexOf(emittingTag) >= 0) {\n                // Break call stack loops\n                return this;\n            }\n            try {\n                this.emittingRegister.push(emittingTag);\n                for (var i = 0, iEnd = listeners.length; i < iEnd; ++i) {\n                    listeners[i].call(this, e);\n                }\n            }\n            finally {\n                var index = this.emittingRegister.indexOf(emittingTag);\n                if (index >= 0) {\n                    this.emittingRegister.splice(index, 1);\n                }\n            }\n        }\n        return this;\n    };\n    /**\n     * Removes a lasting state cursor.\n     *\n     * @function #remitCursor\n     *\n     * @param {string} tableId\n     * ID of the related cursor table.\n     *\n     * @param {Data.DataCursor.Type} cursor\n     * Copy or reference of the cursor.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    DataCursor.prototype.remitCursor = function (tableId, cursor) {\n        var cursors = (this.stateMap[tableId] &&\n                this.stateMap[tableId][cursor.state]);\n        if (cursors) {\n            var index = DataCursor.getIndex(cursor,\n                cursors);\n            if (index >= 0) {\n                cursors.splice(index, 1);\n            }\n        }\n        return this;\n    };\n    /**\n     * This function removes a listener.\n     *\n     * @function #addListener\n     *\n     * @param {Data.DataCursor.TableId} tableId\n     * The ID of the table the listener is connected to.\n     *\n     * @param {Data.DataCursor.State} state\n     * The state on the table the listener is listening to.\n     *\n     * @param {Data.DataCursor.Listener} listener\n     * The listener to deregister.\n     *\n     * @return {Data.DataCursor}\n     * Returns the DataCursor instance for a call chain.\n     */\n    DataCursor.prototype.removeListener = function (tableId, state, listener) {\n        var listeners = (this.listenerMap[tableId] &&\n                this.listenerMap[tableId][state]);\n        if (listeners) {\n            var index = listeners.indexOf(listener);\n            if (index >= 0) {\n                listeners.splice(index, 1);\n            }\n        }\n        return this;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Semantic version string of the DataCursor class.\n     * @internal\n     */\n    DataCursor.version = '1.0.0';\n    return DataCursor;\n}());\n/* *\n *\n *  Class Namespace\n *\n * */\n/**\n * @class Data.DataCursor\n */\n(function (DataCursor) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Finds the index of an cursor in an array.\n     * @private\n     */\n    function getIndex(needle, cursors) {\n        if (needle.type === 'position') {\n            for (var cursor = void 0, i = 0, iEnd = cursors.length; i < iEnd; ++i) {\n                cursor = cursors[i];\n                if (cursor.type === 'position' &&\n                    cursor.state === needle.state &&\n                    cursor.column === needle.column &&\n                    cursor.row === needle.row) {\n                    return i;\n                }\n            }\n        }\n        else {\n            var columnNeedle = JSON.stringify(needle.columns);\n            for (var cursor = void 0, i = 0, iEnd = cursors.length; i < iEnd; ++i) {\n                cursor = cursors[i];\n                if (cursor.type === 'range' &&\n                    cursor.state === needle.state &&\n                    cursor.firstRow === needle.firstRow &&\n                    cursor.lastRow === needle.lastRow &&\n                    JSON.stringify(cursor.columns) === columnNeedle) {\n                    return i;\n                }\n            }\n        }\n        return -1;\n    }\n    DataCursor.getIndex = getIndex;\n    /**\n     * Checks whether two cursor share the same properties.\n     * @private\n     */\n    function isEqual(cursorA, cursorB) {\n        if (cursorA.type === 'position' && cursorB.type === 'position') {\n            return (cursorA.column === cursorB.column &&\n                cursorA.row === cursorB.row &&\n                cursorA.state === cursorB.state);\n        }\n        if (cursorA.type === 'range' && cursorB.type === 'range') {\n            return (cursorA.firstRow === cursorB.firstRow &&\n                cursorA.lastRow === cursorB.lastRow &&\n                (JSON.stringify(cursorA.columns) ===\n                    JSON.stringify(cursorB.columns)));\n        }\n        return false;\n    }\n    DataCursor.isEqual = isEqual;\n    /**\n     * Checks whether a cursor is in a range.\n     * @private\n     */\n    function isInRange(needle, range) {\n        if (range.type === 'position') {\n            range = toRange(range);\n        }\n        if (needle.type === 'position') {\n            needle = toRange(needle, range);\n        }\n        var needleColumns = needle.columns;\n        var rangeColumns = range.columns;\n        return (needle.firstRow >= range.firstRow &&\n            needle.lastRow <= range.lastRow &&\n            (!needleColumns ||\n                !rangeColumns ||\n                needleColumns.every(function (column) { return rangeColumns.indexOf(column) >= 0; })));\n    }\n    DataCursor.isInRange = isInRange;\n    /**\n     * @private\n     */\n    function toPositions(cursor) {\n        if (cursor.type === 'position') {\n            return [cursor];\n        }\n        var columns = (cursor.columns || []);\n        var positions = [];\n        var state = cursor.state;\n        for (var row = cursor.firstRow, rowEnd = cursor.lastRow; row < rowEnd; ++row) {\n            if (!columns.length) {\n                positions.push({\n                    type: 'position',\n                    row: row,\n                    state: state\n                });\n                continue;\n            }\n            for (var column = 0, columnEnd = columns.length; column < columnEnd; ++column) {\n                positions.push({\n                    type: 'position',\n                    column: columns[column],\n                    row: row,\n                    state: state\n                });\n            }\n        }\n        return positions;\n    }\n    DataCursor.toPositions = toPositions;\n    /**\n     * @private\n     */\n    function toRange(cursor, defaultRange) {\n        var _a,\n            _b,\n            _c,\n            _d;\n        if (cursor.type === 'range') {\n            return cursor;\n        }\n        var range = {\n                type: 'range',\n                firstRow: ((_b = (_a = cursor.row) !== null && _a !== void 0 ? _a : (defaultRange && defaultRange.firstRow)) !== null && _b !== void 0 ? _b : 0),\n                lastRow: ((_d = (_c = cursor.row) !== null && _c !== void 0 ? _c : (defaultRange && defaultRange.lastRow)) !== null && _d !== void 0 ? _d : Number.MAX_VALUE),\n                state: cursor.state\n            };\n        if (typeof cursor.column !== 'undefined') {\n            range.columns = [cursor.column];\n        }\n        return range;\n    }\n    DataCursor.toRange = toRange;\n})(DataCursor || (DataCursor = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataCursor = (DataCursor);\n\n;// ./code/es5/es-modules/Data/DataPoolDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\nvar DataPoolDefaults = {\n    connectors: []\n};\n/* *\n *\n *  Export Defaults\n *\n * */\n/* harmony default export */ var Data_DataPoolDefaults = (DataPoolDefaults);\n\n;// ./code/es5/es-modules/Data/DataPool.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Data pool to load connectors on-demand.\n *\n * @class\n * @name Data.DataPool\n *\n * @param {Data.DataPoolOptions} options\n * Pool options with all connectors.\n */\nvar DataPool = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function DataPool(options) {\n        if (options === void 0) { options = Data_DataPoolDefaults; }\n        options.connectors = (options.connectors || []);\n        this.connectors = {};\n        this.options = options;\n        this.waiting = {};\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Emits an event on this data pool to all registered callbacks of the given\n     * event.\n     * @private\n     *\n     * @param {DataTable.Event} e\n     * Event object with event information.\n     */\n    DataPool.prototype.emit = function (e) {\n        highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().fireEvent(this, e.type, e);\n    };\n    /**\n     * Loads the connector.\n     *\n     * @function Data.DataPool#getConnector\n     *\n     * @param {string} connectorId\n     * ID of the connector.\n     *\n     * @return {Promise<Data.DataConnectorType>}\n     * Returns the connector.\n     */\n    DataPool.prototype.getConnector = function (connectorId) {\n        var _this = this;\n        var connector = this.connectors[connectorId];\n        // Already loaded\n        if (connector) {\n            return Promise.resolve(connector);\n        }\n        var waitingList = this.waiting[connectorId];\n        // Start loading\n        if (!waitingList) {\n            waitingList = this.waiting[connectorId] = [];\n            var connectorOptions = this.getConnectorOptions(connectorId);\n            if (!connectorOptions) {\n                throw new Error(\"Connector '\".concat(connectorId, \"' not found.\"));\n            }\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this\n                .loadConnector(connectorOptions)\n                .then(function (connector) {\n                delete _this.waiting[connectorId];\n                for (var i = 0, iEnd = waitingList.length; i < iEnd; ++i) {\n                    waitingList[i][0](connector);\n                }\n            })['catch'](function (error) {\n                delete _this.waiting[connectorId];\n                for (var i = 0, iEnd = waitingList.length; i < iEnd; ++i) {\n                    waitingList[i][1](error);\n                }\n            });\n        }\n        // Add request to waiting list\n        return new Promise(function (resolve, reject) {\n            waitingList.push([resolve, reject]);\n        });\n    };\n    /**\n     * Returns the IDs of all connectors.\n     *\n     * @private\n     *\n     * @return {Array<string>}\n     * Names of all connectors.\n     */\n    DataPool.prototype.getConnectorIds = function () {\n        var connectors = this.options.connectors,\n            connectorIds = [];\n        for (var i = 0, iEnd = connectors.length; i < iEnd; ++i) {\n            connectorIds.push(connectors[i].id);\n        }\n        return connectorIds;\n    };\n    /**\n     * Loads the options of the connector.\n     *\n     * @private\n     *\n     * @param {string} connectorId\n     * ID of the connector.\n     *\n     * @return {DataPoolConnectorOptions|undefined}\n     * Returns the options of the connector, or `undefined` if not found.\n     */\n    DataPool.prototype.getConnectorOptions = function (connectorId) {\n        var connectors = this.options.connectors;\n        for (var i = 0, iEnd = connectors.length; i < iEnd; ++i) {\n            if (connectors[i].id === connectorId) {\n                return connectors[i];\n            }\n        }\n    };\n    /**\n     * Loads the connector table.\n     *\n     * @function Data.DataPool#getConnectorTable\n     *\n     * @param {string} connectorId\n     * ID of the connector.\n     *\n     * @return {Promise<Data.DataTable>}\n     * Returns the connector table.\n     */\n    DataPool.prototype.getConnectorTable = function (connectorId) {\n        return this\n            .getConnector(connectorId)\n            .then(function (connector) { return connector.table; });\n    };\n    /**\n     * Tests whether the connector has never been requested.\n     *\n     * @param {string} connectorId\n     * Name of the connector.\n     *\n     * @return {boolean}\n     * Returns `true`, if the connector has never been requested, otherwise\n     * `false`.\n     */\n    DataPool.prototype.isNewConnector = function (connectorId) {\n        return !this.connectors[connectorId];\n    };\n    /**\n     * Creates and loads the connector.\n     *\n     * @private\n     *\n     * @param {Data.DataPoolConnectorOptions} options\n     * Options of connector.\n     *\n     * @return {Promise<Data.DataConnectorType>}\n     * Returns the connector.\n     */\n    DataPool.prototype.loadConnector = function (options) {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            _this.emit({\n                type: 'load',\n                options: options\n            });\n            var ConnectorClass = Connectors_DataConnector.types[options.type];\n            if (!ConnectorClass) {\n                throw new Error(\"Connector type not found. (\".concat(options.type, \")\"));\n            }\n            var connector = new ConnectorClass(options.options);\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            connector\n                .load()\n                .then(function (connector) {\n                _this.connectors[options.id] = connector;\n                _this.emit({\n                    type: 'afterLoad',\n                    options: options\n                });\n                resolve(connector);\n            })['catch'](reject);\n        });\n    };\n    /**\n     * Registers a callback for a specific event.\n     *\n     * @function Highcharts.DataPool#on\n     *\n     * @param {string} type\n     * Event type as a string.\n     *\n     * @param {Highcharts.EventCallbackFunction<Highcharts.DataPool>} callback\n     * Function to register for an event callback.\n     *\n     * @return {Function}\n     * Function to unregister callback from the event.\n     */\n    DataPool.prototype.on = function (type, callback) {\n        return highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().addEvent(this, type, callback);\n    };\n    /**\n     * Sets connector options under the specified `options.id`.\n     *\n     * @param {Data.DataPoolConnectorOptions} options\n     * Connector options to set.\n     */\n    DataPool.prototype.setConnectorOptions = function (options) {\n        var connectors = this.options.connectors,\n            instances = this.connectors;\n        this.emit({\n            type: 'setConnectorOptions',\n            options: options\n        });\n        for (var i = 0, iEnd = connectors.length; i < iEnd; ++i) {\n            if (connectors[i].id === options.id) {\n                connectors.splice(i, 1);\n                break;\n            }\n        }\n        if (instances[options.id]) {\n            instances[options.id].stopPolling();\n            delete instances[options.id];\n        }\n        connectors.push(options);\n        this.emit({\n            type: 'afterSetConnectorOptions',\n            options: options\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Semantic version string of the DataPool class.\n     * @internal\n     */\n    DataPool.version = '1.0.0';\n    return DataPool;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataPool = (DataPool);\n\n;// ./code/es5/es-modules/Data/Formula/FormulaParser.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @private\n */\nvar booleanRegExp = /^(?:FALSE|TRUE)/;\n/**\n * `.`-separated decimal.\n * @private\n */\nvar decimal1RegExp = /^[+\\-]?\\d+(?:\\.\\d+)?(?:e[+\\-]\\d+)?/;\n/**\n * `,`-separated decimal.\n * @private\n */\nvar decimal2RegExp = /^[+\\-]?\\d+(?:,\\d+)?(?:e[+\\-]\\d+)?/;\n/**\n * - Group 1: Function name\n * @private\n */\nvar functionRegExp = /^([A-Z][A-Z\\d\\.]*)\\(/;\n/**\n * @private\n */\nvar operatorRegExp = /^(?:[+\\-*\\/^<=>]|<=|=>)/;\n/**\n * - Group 1: Start column\n * - Group 2: Start row\n * - Group 3: End column\n * - Group 4: End row\n * @private\n */\nvar rangeA1RegExp = /^(\\$?[A-Z]+)(\\$?\\d+)\\:(\\$?[A-Z]+)(\\$?\\d+)/;\n/**\n * - Group 1: Start row\n * - Group 2: Start column\n * - Group 3: End row\n * - Group 4: End column\n * @private\n */\nvar rangeR1C1RegExp = /^R(\\d*|\\[\\d+\\])C(\\d*|\\[\\d+\\])\\:R(\\d*|\\[\\d+\\])C(\\d*|\\[\\d+\\])/;\n/**\n * - Group 1: Column\n * - Group 2: Row\n * @private\n */\nvar referenceA1RegExp = /^(\\$?[A-Z]+)(\\$?\\d+)(?![\\:C])/;\n/**\n * - Group 1: Row\n * - Group 2: Column\n * @private\n */\nvar referenceR1C1RegExp = /^R(\\d*|\\[\\d+\\])C(\\d*|\\[\\d+\\])(?!\\:)/;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Extracts the inner string of the most outer parantheses.\n *\n * @private\n *\n * @param {string} text\n * Text string to extract from.\n *\n * @return {string}\n * Extracted parantheses. If not found an exception will be thrown.\n */\nfunction extractParantheses(text) {\n    var parantheseLevel = 0;\n    for (var i = 0, iEnd = text.length, char = void 0, parantheseStart = 1; i < iEnd; ++i) {\n        char = text[i];\n        if (char === '(') {\n            if (!parantheseLevel) {\n                parantheseStart = i + 1;\n            }\n            ++parantheseLevel;\n            continue;\n        }\n        if (char === ')') {\n            --parantheseLevel;\n            if (!parantheseLevel) {\n                return text.substring(parantheseStart, i);\n            }\n        }\n    }\n    if (parantheseLevel > 0) {\n        var error = new Error('Incomplete parantheses.');\n        error.name = 'FormulaParseError';\n        throw error;\n    }\n    return '';\n}\n/**\n * Extracts the inner string value.\n *\n * @private\n *\n * @param {string} text\n * Text string to extract from.\n *\n * @return {string}\n * Extracted string. If not found an exception will be thrown.\n */\nfunction extractString(text) {\n    var start = -1;\n    for (var i = 0, iEnd = text.length, char = void 0, escaping = false; i < iEnd; ++i) {\n        char = text[i];\n        if (char === '\\\\') {\n            escaping = !escaping;\n            continue;\n        }\n        if (escaping) {\n            escaping = false;\n            continue;\n        }\n        if (char === '\"') {\n            if (start < 0) {\n                start = i;\n            }\n            else {\n                return text.substring(start + 1, i); // `ì` is excluding\n            }\n        }\n    }\n    var error = new Error('Incomplete string.');\n    error.name = 'FormulaParseError';\n    throw error;\n}\n/**\n * Parses an argument string. Formula arrays with a single term will be\n * simplified to the term.\n *\n * @private\n *\n * @param {string} text\n * Argument string to parse.\n *\n * @param {boolean} alternativeSeparators\n * Whether to expect `;` as argument separator and `,` as decimal separator.\n *\n * @return {Formula|Function|Range|Reference|Value}\n * The recognized term structure.\n */\nfunction parseArgument(text, alternativeSeparators) {\n    var match;\n    // Check for a R1C1:R1C1 range notation\n    match = text.match(rangeR1C1RegExp);\n    if (match) {\n        var beginColumnRelative = (match[2] === '' || match[2][0] === '[');\n        var beginRowRelative = (match[1] === '' || match[1][0] === '[');\n        var endColumnRelative = (match[4] === '' || match[4][0] === '[');\n        var endRowRelative = (match[3] === '' || match[3][0] === '[');\n        var range = {\n                type: 'range',\n                beginColumn: (beginColumnRelative ?\n                    parseInt(match[2].substring(1, -1) || '0', 10) :\n                    parseInt(match[2], 10) - 1),\n                beginRow: (beginRowRelative ?\n                    parseInt(match[1].substring(1, -1) || '0', 10) :\n                    parseInt(match[1], 10) - 1),\n                endColumn: (endColumnRelative ?\n                    parseInt(match[4].substring(1, -1) || '0', 10) :\n                    parseInt(match[4], 10) - 1),\n                endRow: (endRowRelative ?\n                    parseInt(match[3].substring(1, -1) || '0', 10) :\n                    parseInt(match[3], 10) - 1)\n            };\n        if (beginColumnRelative) {\n            range.beginColumnRelative = true;\n        }\n        if (beginRowRelative) {\n            range.beginRowRelative = true;\n        }\n        if (endColumnRelative) {\n            range.endColumnRelative = true;\n        }\n        if (endRowRelative) {\n            range.endRowRelative = true;\n        }\n        return range;\n    }\n    // Check for a A1:A1 range notation\n    match = text.match(rangeA1RegExp);\n    if (match) {\n        var beginColumnRelative = match[1][0] !== '$';\n        var beginRowRelative = match[2][0] !== '$';\n        var endColumnRelative = match[3][0] !== '$';\n        var endRowRelative = match[4][0] !== '$';\n        var range = {\n                type: 'range',\n                beginColumn: parseReferenceColumn(beginColumnRelative ?\n                    match[1] :\n                    match[1].substring(1)) - 1,\n                beginRow: parseInt(beginRowRelative ?\n                    match[2] :\n                    match[2].substring(1), 10) - 1,\n                endColumn: parseReferenceColumn(endColumnRelative ?\n                    match[3] :\n                    match[3].substring(1)) - 1,\n                endRow: parseInt(endRowRelative ?\n                    match[4] :\n                    match[4].substring(1), 10) - 1\n            };\n        if (beginColumnRelative) {\n            range.beginColumnRelative = true;\n        }\n        if (beginRowRelative) {\n            range.beginRowRelative = true;\n        }\n        if (endColumnRelative) {\n            range.endColumnRelative = true;\n        }\n        if (endRowRelative) {\n            range.endRowRelative = true;\n        }\n        return range;\n    }\n    // Fallback to formula processing for other pattern types\n    var formula = parseFormula(text,\n        alternativeSeparators);\n    return (formula.length === 1 && typeof formula[0] !== 'string' ?\n        formula[0] :\n        formula);\n}\n/**\n * Parse arguments string inside function parantheses.\n *\n * @private\n *\n * @param {string} text\n * Parantheses string of the function.\n *\n * @param {boolean} alternativeSeparators\n * Whether to expect `;` as argument separator and `,` as decimal separator.\n *\n * @return {Highcharts.FormulaArguments}\n * Parsed arguments array.\n */\nfunction parseArguments(text, alternativeSeparators) {\n    var args = [], argumentsSeparator = (alternativeSeparators ? ';' : ',');\n    var parantheseLevel = 0,\n        term = '';\n    for (var i = 0, iEnd = text.length, char = void 0; i < iEnd; ++i) {\n        char = text[i];\n        // Check for separator\n        if (char === argumentsSeparator &&\n            !parantheseLevel &&\n            term) {\n            args.push(parseArgument(term, alternativeSeparators));\n            term = '';\n            // Check for a quoted string before skip logic\n        }\n        else if (char === '\"' &&\n            !parantheseLevel &&\n            !term) {\n            var string = extractString(text.substring(i));\n            args.push(string);\n            i += string.length + 1; // Only +1 to cover ++i in for-loop\n            // Skip space and check paranthesis nesting\n        }\n        else if (char !== ' ') {\n            term += char;\n            if (char === '(') {\n                ++parantheseLevel;\n            }\n            else if (char === ')') {\n                --parantheseLevel;\n            }\n        }\n    }\n    // Look for left-overs from last argument\n    if (!parantheseLevel && term) {\n        args.push(parseArgument(term, alternativeSeparators));\n    }\n    return args;\n}\n/**\n * Converts a spreadsheet formula string into a formula array. Throws a\n * `FormulaParserError` when the string can not be parsed.\n *\n * @private\n * @function Formula.parseFormula\n *\n * @param {string} text\n * Spreadsheet formula string, without the leading `=`.\n *\n * @param {boolean} alternativeSeparators\n * * `false` to expect `,` between arguments and `.` in decimals.\n * * `true` to expect `;` between arguments and `,` in decimals.\n *\n * @return {Formula.Formula}\n * Formula array representing the string.\n */\nfunction parseFormula(text, alternativeSeparators) {\n    var decimalRegExp = (alternativeSeparators ?\n            decimal2RegExp :\n            decimal1RegExp),\n        formula = [];\n    var match,\n        next = (text[0] === '=' ? text.substring(1) : text).trim();\n    while (next) {\n        // Check for an R1C1 reference notation\n        match = next.match(referenceR1C1RegExp);\n        if (match) {\n            var columnRelative = (match[2] === '' || match[2][0] === '[');\n            var rowRelative = (match[1] === '' || match[1][0] === '[');\n            var reference = {\n                    type: 'reference',\n                    column: (columnRelative ?\n                        parseInt(match[2].substring(1, -1) || '0', 10) :\n                        parseInt(match[2], 10) - 1),\n                    row: (rowRelative ?\n                        parseInt(match[1].substring(1, -1) || '0', 10) :\n                        parseInt(match[1], 10) - 1)\n                };\n            if (columnRelative) {\n                reference.columnRelative = true;\n            }\n            if (rowRelative) {\n                reference.rowRelative = true;\n            }\n            formula.push(reference);\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for an A1 reference notation\n        match = next.match(referenceA1RegExp);\n        if (match) {\n            var columnRelative = match[1][0] !== '$';\n            var rowRelative = match[2][0] !== '$';\n            var reference = {\n                    type: 'reference',\n                    column: parseReferenceColumn(columnRelative ?\n                        match[1] :\n                        match[1].substring(1)) - 1,\n                    row: parseInt(rowRelative ?\n                        match[2] :\n                        match[2].substring(1), 10) - 1\n                };\n            if (columnRelative) {\n                reference.columnRelative = true;\n            }\n            if (rowRelative) {\n                reference.rowRelative = true;\n            }\n            formula.push(reference);\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a formula operator\n        match = next.match(operatorRegExp);\n        if (match) {\n            formula.push(match[0]);\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a boolean value\n        match = next.match(booleanRegExp);\n        if (match) {\n            formula.push(match[0] === 'TRUE');\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a number value\n        match = next.match(decimalRegExp);\n        if (match) {\n            formula.push(parseFloat(match[0]));\n            next = next.substring(match[0].length).trim();\n            continue;\n        }\n        // Check for a quoted string\n        if (next[0] === '\"') {\n            var string = extractString(next);\n            formula.push(string.substring(1, -1));\n            next = next.substring(string.length + 2).trim();\n            continue;\n        }\n        // Check for a function\n        match = next.match(functionRegExp);\n        if (match) {\n            next = next.substring(match[1].length).trim();\n            var parantheses = extractParantheses(next);\n            formula.push({\n                type: 'function',\n                name: match[1],\n                args: parseArguments(parantheses, alternativeSeparators)\n            });\n            next = next.substring(parantheses.length + 2).trim();\n            continue;\n        }\n        // Check for a formula in parantheses\n        if (next[0] === '(') {\n            var paranteses = extractParantheses(next);\n            if (paranteses) {\n                formula\n                    .push(parseFormula(paranteses, alternativeSeparators));\n                next = next.substring(paranteses.length + 2).trim();\n                continue;\n            }\n        }\n        // Something is not right\n        var position = text.length - next.length, error = new Error('Unexpected character `' +\n                text.substring(position, position + 1) +\n                '` at position ' + (position + 1) +\n                '. (`...' + text.substring(position - 5, position + 6) + '...`)');\n        error.name = 'FormulaParseError';\n        throw error;\n    }\n    return formula;\n}\n/**\n * Converts a reference column `A` of `A1` into a number. Supports endless sizes\n * `ZZZ...`, just limited by integer precision.\n *\n * @private\n *\n * @param {string} text\n * Column string to convert.\n *\n * @return {number}\n * Converted column index.\n */\nfunction parseReferenceColumn(text) {\n    var column = 0;\n    for (var i = 0, iEnd = text.length, code = void 0, factor = text.length - 1; i < iEnd; ++i) {\n        code = text.charCodeAt(i);\n        if (code >= 65 && code <= 90) {\n            column += (code - 64) * Math.pow(26, factor);\n        }\n        --factor;\n    }\n    return column;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar FormulaParser = {\n    parseFormula: parseFormula\n};\n/* harmony default export */ var Formula_FormulaParser = (FormulaParser);\n\n;// ./code/es5/es-modules/Data/Formula/FormulaTypes.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Array of all possible operators.\n * @private\n */\nvar operators = ['+', '-', '*', '/', '^', '=', '<', '<=', '>', '>='];\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Tests an item for a Formula array.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a formula (or argument) array.\n */\nfunction isFormula(item) {\n    return item instanceof Array;\n}\n/**\n * Tests an item for a Function structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a formula function.\n */\nfunction isFunction(item) {\n    return (typeof item === 'object' &&\n        !(item instanceof Array) &&\n        item.type === 'function');\n}\n/**\n * Tests an item for an Operator string.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is an operator string.\n */\nfunction isOperator(item) {\n    return (typeof item === 'string' &&\n        operators.indexOf(item) >= 0);\n}\n/**\n * Tests an item for a Range structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a range.\n */\nfunction isRange(item) {\n    return (typeof item === 'object' &&\n        !(item instanceof Array) &&\n        item.type === 'range');\n}\n/**\n * Tests an item for a Reference structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a reference.\n */\nfunction isReference(item) {\n    return (typeof item === 'object' &&\n        !(item instanceof Array) &&\n        item.type === 'reference');\n}\n/**\n * Tests an item for a Value structure.\n *\n * @private\n *\n * @param {Highcharts.FormulaItem|null|undefined} item\n * Item to test.\n *\n * @return {boolean}\n * `true`, if the item is a value.\n */\nfunction isValue(item) {\n    return (typeof item === 'boolean' ||\n        typeof item === 'number' ||\n        typeof item === 'string');\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar MathFormula = {\n    isFormula: isFormula,\n    isFunction: isFunction,\n    isOperator: isOperator,\n    isRange: isRange,\n    isReference: isReference,\n    isValue: isValue\n};\n/* harmony default export */ var FormulaTypes = (MathFormula);\n\n;// ./code/es5/es-modules/Data/Formula/FormulaProcessor.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar FormulaProcessor_isFormula = FormulaTypes.isFormula, FormulaProcessor_isFunction = FormulaTypes.isFunction, FormulaProcessor_isOperator = FormulaTypes.isOperator, FormulaProcessor_isRange = FormulaTypes.isRange, FormulaProcessor_isReference = FormulaTypes.isReference, FormulaProcessor_isValue = FormulaTypes.isValue;\n/* *\n *\n *  Constants\n *\n * */\nvar asLogicalStringRegExp = / */;\nvar MAX_FALSE = Number.MAX_VALUE / 1.000000000001;\nvar MAX_STRING = Number.MAX_VALUE / 1.000000000002;\nvar MAX_TRUE = Number.MAX_VALUE;\nvar operatorPriority = {\n    '^': 3,\n    '*': 2,\n    '/': 2,\n    '+': 1,\n    '-': 1,\n    '=': 0,\n    '<': 0,\n    '<=': 0,\n    '>': 0,\n    '>=': 0\n};\nvar processorFunctions = {};\nvar processorFunctionNameRegExp = /^[A-Z][A-Z\\.]*$/;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Converts non-number types to logical numbers.\n *\n * @param {Highcharts.FormulaValue} value\n * Value to convert.\n *\n * @return {number}\n * Logical number value. `NaN` if not convertable.\n */\nfunction asLogicalNumber(value) {\n    switch (typeof value) {\n        case 'boolean':\n            return value ? MAX_TRUE : MAX_FALSE;\n        case 'string':\n            return MAX_STRING;\n        case 'number':\n            return value;\n        default:\n            return NaN;\n    }\n}\n/**\n * Converts strings to logical strings, while other types get passed through. In\n * logical strings the space character is the lowest value and letters are case\n * insensitive.\n *\n * @param {Highcharts.FormulaValue} value\n * Value to convert.\n *\n * @return {Highcharts.FormulaValue}\n * Logical string value or passed through value.\n */\nfunction asLogicalString(value) {\n    if (typeof value === 'string') {\n        return value.toLowerCase().replace(asLogicalStringRegExp, '\\0');\n    }\n    return value;\n}\n/**\n * Converts non-number types to a logic number.\n *\n * @param {Highcharts.FormulaValue} value\n * Value to convert.\n *\n * @return {number}\n * Number value. `NaN` if not convertable.\n */\nfunction asNumber(value) {\n    switch (typeof value) {\n        case 'boolean':\n            return value ? 1 : 0;\n        case 'string':\n            return parseFloat(value.replace(',', '.'));\n        case 'number':\n            return value;\n        default:\n            return NaN;\n    }\n}\n/**\n * Process a basic operation of two given values.\n *\n * @private\n *\n * @param {Highcharts.FormulaOperator} operator\n * Operator between values.\n *\n * @param {Highcharts.FormulaValue} x\n * First value for operation.\n *\n * @param {Highcharts.FormulaValue} y\n * Second value for operation.\n *\n * @return {Highcharts.FormulaValue}\n * Operation result. `NaN` if operation is not support.\n */\nfunction basicOperation(operator, x, y) {\n    switch (operator) {\n        case '=':\n            return asLogicalString(x) === asLogicalString(y);\n        case '<':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) < asLogicalString(y);\n            }\n            return asLogicalNumber(x) < asLogicalNumber(y);\n        case '<=':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) <= asLogicalString(y);\n            }\n            return asLogicalNumber(x) <= asLogicalNumber(y);\n        case '>':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) > asLogicalString(y);\n            }\n            return asLogicalNumber(x) > asLogicalNumber(y);\n        case '>=':\n            if (typeof x === typeof y) {\n                return asLogicalString(x) >= asLogicalString(y);\n            }\n            return asLogicalNumber(x) >= asLogicalNumber(y);\n    }\n    x = asNumber(x);\n    y = asNumber(y);\n    var result;\n    switch (operator) {\n        case '+':\n            result = x + y;\n            break;\n        case '-':\n            result = x - y;\n            break;\n        case '*':\n            result = x * y;\n            break;\n        case '/':\n            result = x / y;\n            break;\n        case '^':\n            result = Math.pow(x, y);\n            break;\n        default:\n            return NaN;\n    }\n    // Limit decimal to 9 digits\n    return (result % 1 ?\n        Math.round(result * 1000000000) / 1000000000 :\n        result);\n}\n/**\n * Converts an argument to Value and in case of a range to an array of Values.\n *\n * @function Highcharts.Formula.getArgumentValue\n *\n * @param {Highcharts.FormulaRange|Highcharts.FormulaTerm} arg\n * Formula range or term to convert.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Highcharts.FormulaValue|Array<Highcharts.FormulaValue>}\n * Converted value.\n */\nfunction getArgumentValue(arg, table) {\n    // Add value\n    if (FormulaProcessor_isValue(arg)) {\n        return arg;\n    }\n    // Add values of a range\n    if (FormulaProcessor_isRange(arg)) {\n        return (table && getRangeValues(arg, table) || []);\n    }\n    // Add values of a function\n    if (FormulaProcessor_isFunction(arg)) {\n        return processFunction(arg, table);\n    }\n    // Process functions, operations, references with formula processor\n    return processFormula((FormulaProcessor_isFormula(arg) ? arg : [arg]), table);\n}\n/**\n * Converts all arguments to Values and in case of ranges to arrays of Values.\n *\n * @function Highcharts.Formula.getArgumentsValues\n *\n * @param {Highcharts.FormulaArguments} args\n * Formula arguments to convert.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Array<(Highcharts.FormulaValue|Array<Highcharts.FormulaValue>)>}\n * Converted values.\n */\nfunction getArgumentsValues(args, table) {\n    var values = [];\n    for (var i = 0, iEnd = args.length; i < iEnd; ++i) {\n        values.push(getArgumentValue(args[i], table));\n    }\n    return values;\n}\n/**\n * Extracts cell values from a table for a given range.\n *\n * @function Highcharts.Formula.getRangeValues\n *\n * @param {Highcharts.FormulaRange} range\n * Formula range to use.\n *\n * @param {Highcharts.DataTable} table\n * Table to extract from.\n *\n * @return {Array<Highcharts.FormulaValue>}\n * Extracted values.\n */\nfunction getRangeValues(range, table) {\n    var columnNames = table\n            .getColumnNames()\n            .slice(range.beginColumn,\n        range.endColumn + 1),\n        values = [];\n    for (var i = 0, iEnd = columnNames.length, cell = void 0; i < iEnd; ++i) {\n        var cells = table.getColumn(columnNames[i],\n            true) || [];\n        for (var j = range.beginRow, jEnd = range.endRow + 1; j < jEnd; ++j) {\n            cell = cells[j];\n            if (typeof cell === 'string' &&\n                cell[0] === '=' &&\n                table !== table.modified) {\n                // Look in the modified table for formula result\n                cell = table.modified.getCell(columnNames[i], j);\n            }\n            values.push(FormulaProcessor_isValue(cell) ? cell : NaN);\n        }\n    }\n    return values;\n}\n/**\n * Extracts the cell value from a table for a given reference.\n *\n * @private\n *\n * @param {Highcharts.FormulaReference} reference\n * Formula reference to use.\n *\n * @param {Highcharts.DataTable} table\n * Table to extract from.\n *\n * @return {Highcharts.FormulaValue}\n * Extracted value. 'undefined' might also indicate that the cell was not found.\n */\nfunction getReferenceValue(reference, table) {\n    var columnName = table.getColumnNames()[reference.column];\n    if (columnName) {\n        var cell = table.getCell(columnName,\n            reference.row);\n        if (typeof cell === 'string' &&\n            cell[0] === '=' &&\n            table !== table.modified) {\n            // Look in the modified table for formula result\n            var result = table.modified.getCell(columnName,\n                reference.row);\n            return FormulaProcessor_isValue(result) ? result : NaN;\n        }\n        return FormulaProcessor_isValue(cell) ? cell : NaN;\n    }\n    return NaN;\n}\n/**\n * Processes a formula array on the given table. If the formula does not contain\n * references or ranges, then no table has to be provided.\n *\n * @private\n * @function Highcharts.processFormula\n *\n * @param {Highcharts.Formula} formula\n * Formula array to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Highcharts.FormulaValue}\n * Result value of the process. `NaN` indicates an error.\n */\nfunction processFormula(formula, table) {\n    var x;\n    for (var i = 0, iEnd = formula.length, item = void 0, operator = void 0, result = void 0, y = void 0; i < iEnd; ++i) {\n        item = formula[i];\n        // Remember operator for operation on next item\n        if (FormulaProcessor_isOperator(item)) {\n            operator = item;\n            continue;\n        }\n        // Next item is a value\n        if (FormulaProcessor_isValue(item)) {\n            y = item;\n            // Next item is a formula and needs to get processed first\n        }\n        else if (FormulaProcessor_isFormula(item)) {\n            y = processFormula(formula, table);\n            // Next item is a function call and needs to get processed first\n        }\n        else if (FormulaProcessor_isFunction(item)) {\n            result = processFunction(item, table);\n            y = (FormulaProcessor_isValue(result) ? result : NaN); // Arrays are not allowed here\n            // Next item is a reference and needs to get resolved\n        }\n        else if (FormulaProcessor_isReference(item)) {\n            y = (table && getReferenceValue(item, table));\n        }\n        // If we have a next value, lets do the operation\n        if (typeof y !== 'undefined') {\n            // Next value is our first value\n            if (typeof x === 'undefined') {\n                if (operator) {\n                    x = basicOperation(operator, 0, y);\n                }\n                else {\n                    x = y;\n                }\n                // Fail fast if no operator available\n            }\n            else if (!operator) {\n                return NaN;\n                // Regular next value\n            }\n            else {\n                var operator2 = formula[i + 1];\n                if (FormulaProcessor_isOperator(operator2) &&\n                    operatorPriority[operator2] > operatorPriority[operator]) {\n                    y = basicOperation(operator2, y, processFormula(formula.slice(i + 2)));\n                    i = iEnd;\n                }\n                x = basicOperation(operator, x, y);\n            }\n            operator = void 0;\n            y = void 0;\n        }\n    }\n    return FormulaProcessor_isValue(x) ? x : NaN;\n}\n/**\n * Process a function on the given table. If the arguments do not contain\n * references or ranges, then no table has to be provided.\n *\n * @private\n *\n * @param {Highcharts.FormulaFunction} formulaFunction\n * Formula function to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @param {Highcharts.FormulaReference} [reference]\n * Table cell reference to use for relative references and ranges.\n *\n * @return {Highcharts.FormulaValue|Array<Highcharts.FormulaValue>}\n * Result value (or values) of the process. `NaN` indicates an error.\n */\nfunction processFunction(formulaFunction, table, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nreference // @todo\n) {\n    var processor = processorFunctions[formulaFunction.name];\n    if (processor) {\n        try {\n            return processor(formulaFunction.args, table);\n        }\n        catch (_a) {\n            return NaN;\n        }\n    }\n    var error = new Error(\"Function \\\"\".concat(formulaFunction.name, \"\\\" not found.\"));\n    error.name = 'FormulaProcessError';\n    throw error;\n}\n/**\n * Registers a function for the FormulaProcessor.\n *\n * @param {string} name\n * Name of the function in spreadsheets notation with upper case.\n *\n * @param {Highcharts.FormulaFunction} processorFunction\n * ProcessorFunction for the FormulaProcessor. This is an object so that it\n * can take additional parameter for future validation routines.\n *\n * @return {boolean}\n * Return true, if the ProcessorFunction has been registered.\n */\nfunction registerProcessorFunction(name, processorFunction) {\n    return (processorFunctionNameRegExp.test(name) &&\n        !processorFunctions[name] &&\n        !!(processorFunctions[name] = processorFunction));\n}\n/**\n * Translates relative references and ranges in-place.\n *\n * @param {Highcharts.Formula} formula\n * Formula to translate references and ranges in.\n *\n * @param {number} [columnDelta=0]\n * Column delta to translate to. Negative translate back.\n *\n * @param {number} [rowDelta=0]\n * Row delta to translate to. Negative numbers translate back.\n *\n * @return {Highcharts.Formula}\n * Formula with translated reference and ranges. This formula is equal to the\n * first argument.\n */\nfunction translateReferences(formula, columnDelta, rowDelta) {\n    if (columnDelta === void 0) { columnDelta = 0; }\n    if (rowDelta === void 0) { rowDelta = 0; }\n    for (var i = 0, iEnd = formula.length, item = void 0; i < iEnd; ++i) {\n        item = formula[i];\n        if (item instanceof Array) {\n            translateReferences(item, columnDelta, rowDelta);\n        }\n        else if (FormulaProcessor_isFunction(item)) {\n            translateReferences(item.args, columnDelta, rowDelta);\n        }\n        else if (FormulaProcessor_isRange(item)) {\n            if (item.beginColumnRelative) {\n                item.beginColumn += columnDelta;\n            }\n            if (item.beginRowRelative) {\n                item.beginRow += rowDelta;\n            }\n            if (item.endColumnRelative) {\n                item.endColumn += columnDelta;\n            }\n            if (item.endRowRelative) {\n                item.endRow += rowDelta;\n            }\n        }\n        else if (FormulaProcessor_isReference(item)) {\n            if (item.columnRelative) {\n                item.column += columnDelta;\n            }\n            if (item.rowRelative) {\n                item.row += rowDelta;\n            }\n        }\n    }\n    return formula;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar FormulaProcessor = {\n    asNumber: asNumber,\n    getArgumentValue: getArgumentValue,\n    getArgumentsValues: getArgumentsValues,\n    getRangeValues: getRangeValues,\n    getReferenceValue: getReferenceValue,\n    processFormula: processFormula,\n    processorFunctions: processorFunctions,\n    registerProcessorFunction: registerProcessorFunction,\n    translateReferences: translateReferences\n};\n/* harmony default export */ var Formula_FormulaProcessor = (FormulaProcessor);\n\n;// ./code/es5/es-modules/Data/Formula/Functions/ABS.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar ABS_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `ABS(value)` implementation. Returns positive numbers.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Array<number>}\n * Result value of the process.\n */\nfunction ABS(args, table) {\n    var value = ABS_getArgumentValue(args[0],\n        table);\n    switch (typeof value) {\n        case 'number':\n            return Math.abs(value);\n        case 'object': {\n            var values = [];\n            for (var i = 0, iEnd = value.length, value2 = void 0; i < iEnd; ++i) {\n                value2 = value[i];\n                if (typeof value2 !== 'number') {\n                    return NaN;\n                }\n                values.push(Math.abs(value2));\n            }\n            return values;\n        }\n        default:\n            return NaN;\n    }\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('ABS', ABS);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_ABS = ((/* unused pure expression or super */ null && (ABS)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/AND.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar AND_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `AND(...tests)` implementation. Returns `TRUE`, if all test\n * results are not `0` or `FALSE`.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean}\n * Result value of the process.\n */\nfunction AND(args, table) {\n    for (var i = 0, iEnd = args.length, value = void 0; i < iEnd; ++i) {\n        value = AND_getArgumentValue(args[i], table);\n        if (!value ||\n            (typeof value === 'object' &&\n                !AND(value, table))) {\n            return false;\n        }\n    }\n    return true;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('AND', AND);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_AND = ((/* unused pure expression or super */ null && (AND)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/AVERAGE.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar AVERAGE_getArgumentsValues = Formula_FormulaProcessor.getArgumentsValues;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `AVERAGE(...values)` implementation. Calculates the average\n * of the given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.AVERAGE\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction AVERAGE(args, table) {\n    var values = AVERAGE_getArgumentsValues(args,\n        table);\n    var count = 0,\n        result = 0;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    ++count;\n                    result += value;\n                }\n                break;\n            case 'object':\n                for (var j = 0, jEnd = value.length, value2 = void 0; j < jEnd; ++j) {\n                    value2 = value[j];\n                    if (typeof value2 === 'number' &&\n                        !isNaN(value2)) {\n                        ++count;\n                        result += value2;\n                    }\n                }\n                break;\n        }\n    }\n    return (count ? (result / count) : 0);\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('AVERAGE', AVERAGE);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_AVERAGE = ((/* unused pure expression or super */ null && (AVERAGE)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/AVERAGEA.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar AVERAGEA_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `AVERAGEA(...values)` implementation. Calculates the\n * average of the given values. Strings and FALSE are calculated as 0.\n *\n * @private\n * @function Formula.processorFunctions.AVERAGEA\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction AVERAGEA(args, table) {\n    var count = 0,\n        result = 0;\n    for (var i = 0, iEnd = args.length, value = void 0; i < iEnd; ++i) {\n        value = AVERAGEA_getArgumentValue(args[i], table);\n        switch (typeof value) {\n            case 'boolean':\n                ++count;\n                result += (value ? 1 : 0);\n                continue;\n            case 'number':\n                if (!isNaN(value)) {\n                    ++count;\n                    result += value;\n                }\n                continue;\n            case 'string':\n                ++count;\n                continue;\n            default:\n                for (var j = 0, jEnd = value.length, value2 = void 0; j < jEnd; ++j) {\n                    value2 = value[j];\n                    switch (typeof value2) {\n                        case 'boolean':\n                            ++count;\n                            result += (value2 ? 1 : 0);\n                            continue;\n                        case 'number':\n                            if (!isNaN(value2)) {\n                                ++count;\n                                result += value2;\n                            }\n                            continue;\n                        case 'string':\n                            ++count;\n                            continue;\n                    }\n                }\n                continue;\n        }\n    }\n    return (count ? (result / count) : 0);\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('AVERAGEA', AVERAGEA);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_AVERAGEA = ((/* unused pure expression or super */ null && (AVERAGEA)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/COUNT.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `COUNT(...values)` implementation. Returns the count of\n * given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.COUNT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction COUNT(args, table) {\n    var values = Formula_FormulaProcessor.getArgumentsValues(args,\n        table);\n    var count = 0;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    ++count;\n                }\n                break;\n            case 'object':\n                count += COUNT(value, table);\n                break;\n        }\n    }\n    return count;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('COUNT', COUNT);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_COUNT = ((/* unused pure expression or super */ null && (COUNT)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/COUNTA.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `COUNTA(...values)` implementation. Returns the count of\n * given values that are not empty.\n *\n * @private\n * @function Formula.processorFunctions.COUNT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction COUNTA(args, table) {\n    var values = Formula_FormulaProcessor.getArgumentsValues(args,\n        table);\n    var count = 0;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (isNaN(value)) {\n                    continue;\n                }\n                break;\n            case 'object':\n                count += COUNTA(value, table);\n                continue;\n            case 'string':\n                if (!value) {\n                    continue;\n                }\n                break;\n        }\n        ++count;\n    }\n    return count;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('COUNTA', COUNTA);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_COUNTA = ((/* unused pure expression or super */ null && (COUNTA)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/IF.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar IF_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `IF(test, value1, value2)` implementation. Returns one of\n * the values based on the test result. `value1` will be returned, if the test\n * result is not `0` or `FALSE`.\n *\n * @private\n * @function Formula.processorFunctions.IF\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {Highcharts.FormulaValue|Array<Highcharts.FormulaValue>}\n * Result value of the process.\n */\nfunction IF(args, table) {\n    return (IF_getArgumentValue(args[0], table) ?\n        IF_getArgumentValue(args[1], table) :\n        IF_getArgumentValue(args[2], table));\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('IF', IF);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_IF = ((/* unused pure expression or super */ null && (IF)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/ISNA.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar ISNA_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `ISNA(value)` implementation. Returns TRUE if value is not\n * a number.\n *\n * @private\n * @function Formula.processorFunctions.ISNA\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean}\n * Result value of the process.\n */\nfunction ISNA(args, table) {\n    var value = ISNA_getArgumentValue(args[0],\n        table);\n    return (typeof value !== 'number' || isNaN(value));\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('ISNA', ISNA);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_ISNA = ((/* unused pure expression or super */ null && (ISNA)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/MAX.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar MAX_getArgumentsValues = Formula_FormulaProcessor.getArgumentsValues;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MAX(...values)` implementation. Calculates the largest\n * of the given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.MAX\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MAX(args, table) {\n    var values = MAX_getArgumentsValues(args,\n        table);\n    var result = Number.NEGATIVE_INFINITY;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (value > result) {\n                    result = value;\n                }\n                break;\n            case 'object':\n                value = MAX(value);\n                if (value > result) {\n                    result = value;\n                }\n                break;\n        }\n    }\n    return isFinite(result) ? result : 0;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MAX', MAX);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_MAX = ((/* unused pure expression or super */ null && (MAX)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/MEDIAN.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MEDIAN(...values)` implementation. Calculates the median\n * average of the given values.\n *\n * @private\n * @function Formula.processorFunctions.MEDIAN\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MEDIAN(args, table) {\n    var median = [],\n        values = Formula_FormulaProcessor.getArgumentsValues(args,\n        table);\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    median.push(value);\n                }\n                break;\n            case 'object':\n                for (var j = 0, jEnd = value.length, value2 = void 0; j < jEnd; ++j) {\n                    value2 = value[j];\n                    if (typeof value2 === 'number' &&\n                        !isNaN(value2)) {\n                        median.push(value2);\n                    }\n                }\n                break;\n        }\n    }\n    var count = median.length;\n    if (!count) {\n        return NaN;\n    }\n    var half = Math.floor(count / 2); // Floor because index starts at 0\n        return (count % 2 ?\n            median[half] : // Odd\n            (median[half - 1] + median[half]) / 2 // Even\n        );\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MEDIAN', MEDIAN);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_MEDIAN = ((/* unused pure expression or super */ null && (MEDIAN)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/MIN.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar MIN_getArgumentsValues = Formula_FormulaProcessor.getArgumentsValues;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MIN(...values)` implementation. Calculates the lowest\n * of the given values that are numbers.\n *\n * @private\n * @function Formula.processorFunctions.MIN\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MIN(args, table) {\n    var values = MIN_getArgumentsValues(args,\n        table);\n    var result = Number.POSITIVE_INFINITY;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (value < result) {\n                    result = value;\n                }\n                break;\n            case 'object':\n                value = MIN(value);\n                if (value < result) {\n                    result = value;\n                }\n                break;\n        }\n    }\n    return isFinite(result) ? result : 0;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MIN', MIN);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_MIN = ((/* unused pure expression or super */ null && (MIN)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/MOD.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar MOD_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `MOD(value1, value2)` implementation. Calculates the rest\n * of the division with the given values.\n *\n * @private\n * @function Formula.processorFunctions.MOD\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction MOD(args, table) {\n    var value1 = MOD_getArgumentValue(args[0],\n        table),\n        value2 = MOD_getArgumentValue(args[1],\n        table);\n    if (typeof value1 === 'object') {\n        value1 = value1[0];\n    }\n    if (typeof value2 === 'object') {\n        value2 = value2[0];\n    }\n    if (typeof value1 !== 'number' ||\n        typeof value2 !== 'number' ||\n        value2 === 0) {\n        return NaN;\n    }\n    return value1 % value2;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MOD', MOD);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_MOD = ((/* unused pure expression or super */ null && (MOD)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/MODE.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Creates the mode map of the given arguments.\n *\n * @private\n * @function Formula.processorFunctions.MULT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction getModeMap(args, table) {\n    var modeMap = {},\n        values = Formula_FormulaProcessor.getArgumentsValues(args,\n        table);\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    modeMap[value] = (modeMap[value] || 0) + 1;\n                }\n                break;\n            case 'object':\n                for (var j = 0, jEnd = value.length, value2 = void 0; j < jEnd; ++j) {\n                    value2 = value[j];\n                    if (typeof value2 === 'number' &&\n                        !isNaN(value2)) {\n                        modeMap[value2] = (modeMap[value2] || 0) + 1;\n                    }\n                }\n                break;\n        }\n    }\n    return modeMap;\n}\n/**\n * Processor for the `MODE.MULT(...values)` implementation. Calculates the most\n * frequent values of the give values.\n *\n * @private\n * @function Formula.processorFunctions.MULT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number|Array<number>}\n * Result value of the process.\n */\nfunction MULT(args, table) {\n    var modeMap = getModeMap(args,\n        table),\n        keys = Object.keys(modeMap);\n    if (!keys.length) {\n        return NaN;\n    }\n    var modeKeys = [parseFloat(keys[0])],\n        modeCount = modeMap[keys[0]];\n    for (var i = 1, iEnd = keys.length, key = void 0, count = void 0; i < iEnd; ++i) {\n        key = keys[i];\n        count = modeMap[key];\n        if (modeCount < count) {\n            modeKeys = [parseFloat(key)];\n            modeCount = count;\n        }\n        else if (modeCount === count) {\n            modeKeys.push(parseFloat(key));\n        }\n    }\n    return modeCount > 1 ? modeKeys : NaN;\n}\n/**\n * Processor for the `MODE.SNGL(...values)` implementation. Calculates the\n * lowest most frequent value of the give values.\n *\n * @private\n * @function Formula.processorFunctions['MODE.SNGL']\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction SNGL(args, table) {\n    var modeMap = getModeMap(args,\n        table),\n        keys = Object.keys(modeMap);\n    if (!keys.length) {\n        return NaN;\n    }\n    var modeKey = parseFloat(keys[0]),\n        modeCount = modeMap[keys[0]];\n    for (var i = 1, iEnd = keys.length, key = void 0, keyValue = void 0, count = void 0; i < iEnd; ++i) {\n        key = keys[i];\n        count = modeMap[key];\n        if (modeCount < count) {\n            modeKey = parseFloat(key);\n            modeCount = count;\n        }\n        else if (modeCount === count) {\n            keyValue = parseFloat(key);\n            if (modeKey > keyValue) {\n                modeKey = keyValue;\n                modeCount = count;\n            }\n        }\n    }\n    return modeCount > 1 ? modeKey : NaN;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('MODE', SNGL);\nFormula_FormulaProcessor.registerProcessorFunction('MODE.MULT', MULT);\nFormula_FormulaProcessor.registerProcessorFunction('MODE.SNGL', SNGL);\n/* *\n *\n *  Default Export\n *\n * */\nvar MODE = {\n    MULT: MULT,\n    SNGL: SNGL\n};\n/* harmony default export */ var Functions_MODE = ((/* unused pure expression or super */ null && (MODE)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/NOT.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar NOT_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `NOT(value)` implementation. Returns the opposite test\n * result.\n *\n * @private\n * @function Formula.processorFunctions.NOT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean|number}\n * Result value of the process.\n */\nfunction NOT(args, table) {\n    var value = NOT_getArgumentValue(args[0],\n        table);\n    if (typeof value === 'object') {\n        value = value[0];\n    }\n    switch (typeof value) {\n        case 'boolean':\n        case 'number':\n            return !value;\n    }\n    return NaN;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('NOT', NOT);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_NOT = ((/* unused pure expression or super */ null && (NOT)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/OR.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar OR_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `OR(...tests)` implementation. Returns `TRUE`, if one test\n * result is not `0` or `FALSE`.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean}\n * Result value of the process.\n */\nfunction OR(args, table) {\n    for (var i = 0, iEnd = args.length, value = void 0; i < iEnd; ++i) {\n        value = OR_getArgumentValue(args[i], table);\n        if (typeof value === 'object') {\n            if (OR(value, table)) {\n                return true;\n            }\n        }\n        else if (value) {\n            return true;\n        }\n    }\n    return false;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('OR', OR);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_OR = ((/* unused pure expression or super */ null && (OR)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/PRODUCT.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar PRODUCT_getArgumentsValues = Formula_FormulaProcessor.getArgumentsValues;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `PRODUCT(...values)` implementation. Calculates the product\n * of the given values.\n *\n * @private\n * @function Formula.processorFunctions.PRODUCT\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction PRODUCT(args, table) {\n    var values = PRODUCT_getArgumentsValues(args,\n        table);\n    var result = 1,\n        calculated = false;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    calculated = true;\n                    result *= value;\n                }\n                break;\n            case 'object':\n                calculated = true;\n                result *= PRODUCT(value, table);\n                break;\n        }\n    }\n    return (calculated ? result : 0);\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('PRODUCT', PRODUCT);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_PRODUCT = ((/* unused pure expression or super */ null && (PRODUCT)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/SUM.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `SUM(...values)` implementation. Calculates the sum of the\n * given values.\n *\n * @private\n * @function Formula.processorFunctions.SUM\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to process.\n *\n * @return {number}\n * Result value of the process.\n */\nfunction SUM(args, table) {\n    var values = Formula_FormulaProcessor.getArgumentsValues(args,\n        table);\n    var result = 0;\n    for (var i = 0, iEnd = values.length, value = void 0; i < iEnd; ++i) {\n        value = values[i];\n        switch (typeof value) {\n            case 'number':\n                if (!isNaN(value)) {\n                    result += value;\n                }\n                break;\n            case 'object':\n                result += SUM(value, table);\n                break;\n        }\n    }\n    return result;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('SUM', SUM); // 🐝\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_SUM = ((/* unused pure expression or super */ null && (SUM)));\n\n;// ./code/es5/es-modules/Data/Formula/Functions/XOR.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\n\nvar XOR_getArgumentValue = Formula_FormulaProcessor.getArgumentValue;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Processor for the `XOR(...tests)` implementation. Returns `TRUE`, if at least\n * one of the given tests differs in result of other tests.\n *\n * @private\n * @function Formula.processorFunctions.AND\n *\n * @param {Highcharts.FormulaArguments} args\n * Arguments to process.\n *\n * @param {Highcharts.DataTable} [table]\n * Table to use for references and ranges.\n *\n * @return {boolean|number}\n * Result value of the process.\n */\nfunction XOR(args, table) {\n    for (var i = 0, iEnd = args.length, lastValue = void 0, value = void 0; i < iEnd; ++i) {\n        value = XOR_getArgumentValue(args[i], table);\n        switch (typeof value) {\n            case 'boolean':\n            case 'number':\n                if (typeof lastValue === 'undefined') {\n                    lastValue = !!value;\n                }\n                else if (!!value !== lastValue) {\n                    return true;\n                }\n                break;\n            case 'object':\n                for (var j = 0, jEnd = value.length, value2 = void 0; j < jEnd; ++j) {\n                    value2 = value[j];\n                    switch (typeof value2) {\n                        case 'boolean':\n                        case 'number':\n                            if (typeof lastValue === 'undefined') {\n                                lastValue = !!value2;\n                            }\n                            else if (!!value2 !== lastValue) {\n                                return true;\n                            }\n                            break;\n                    }\n                }\n                break;\n        }\n    }\n    return false;\n}\n/* *\n *\n *  Registry\n *\n * */\nFormula_FormulaProcessor.registerProcessorFunction('XOR', XOR);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Functions_XOR = ((/* unused pure expression or super */ null && (XOR)));\n\n;// ./code/es5/es-modules/Data/Formula/Formula.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n/* *\n *\n *  Imports\n *\n * */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* *\n *\n *  Default Export\n *\n * */\n/**\n * Formula engine to make use of spreadsheet formula strings.\n * @internal\n */\nvar Formula = __assign(__assign(__assign({}, Formula_FormulaParser), Formula_FormulaProcessor), FormulaTypes);\n/* harmony default export */ var Formula_Formula = (Formula);\n\n;// ./code/es5/es-modules/Data/Converters/CSVConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Christer Vasseng\n *  - Gøran Slettemark\n *  - Sophie Bremer\n *\n * */\n\nvar CSVConverter_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar CSVConverter_assign = (undefined && undefined.__assign) || function () {\n    CSVConverter_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return CSVConverter_assign.apply(this, arguments);\n};\n\n\nvar CSVConverter_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transforming CSV to a table.\n *\n * @private\n */\nvar CSVConverter = /** @class */ (function (_super) {\n    CSVConverter_extends(CSVConverter, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the CSV parser.\n     *\n     * @param {CSVConverter.UserOptions} [options]\n     * Options for the CSV parser.\n     */\n    function CSVConverter(options) {\n        var _this = this;\n        var mergedOptions = CSVConverter_merge(CSVConverter.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.columns = [];\n        _this.headers = [];\n        _this.dataTypes = [];\n        _this.options = mergedOptions;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Creates a CSV string from the datatable on the connector instance.\n     *\n     * @param {DataConnector} connector\n     * Connector instance to export from.\n     *\n     * @param {CSVConverter.Options} [options]\n     * Options used for the export.\n     *\n     * @return {string}\n     * CSV string from the connector table.\n     */\n    CSVConverter.prototype.export = function (connector, options) {\n        if (options === void 0) { options = this.options; }\n        var useLocalDecimalPoint = options.useLocalDecimalPoint,\n            lineDelimiter = options.lineDelimiter,\n            exportNames = (this.options.firstRowAsNames !== false);\n        var decimalPoint = options.decimalPoint,\n            itemDelimiter = options.itemDelimiter;\n        if (!decimalPoint) {\n            decimalPoint = (itemDelimiter !== ',' && useLocalDecimalPoint ?\n                (1.1).toLocaleString()[1] :\n                '.');\n        }\n        if (!itemDelimiter) {\n            itemDelimiter = (decimalPoint === ',' ? ';' : ',');\n        }\n        var columns = connector.getSortedColumns(options.usePresentationOrder),\n            columnNames = Object.keys(columns),\n            csvRows = [],\n            columnsCount = columnNames.length;\n        var rowArray = [];\n        // Add the names as the first row if they should be exported\n        if (exportNames) {\n            csvRows.push(columnNames.map(function (columnName) { return \"\\\"\".concat(columnName, \"\\\"\"); }).join(itemDelimiter));\n        }\n        for (var columnIndex = 0; columnIndex < columnsCount; columnIndex++) {\n            var columnName = columnNames[columnIndex],\n                column = columns[columnName],\n                columnLength = column.length;\n            var columnMeta = connector.whatIs(columnName);\n            var columnDataType = void 0;\n            if (columnMeta) {\n                columnDataType = columnMeta.dataType;\n            }\n            for (var rowIndex = 0; rowIndex < columnLength; rowIndex++) {\n                var cellValue = column[rowIndex];\n                if (!rowArray[rowIndex]) {\n                    rowArray[rowIndex] = [];\n                }\n                // Prefer datatype from metadata\n                if (columnDataType === 'string') {\n                    cellValue = '\"' + cellValue + '\"';\n                }\n                else if (typeof cellValue === 'number') {\n                    cellValue = String(cellValue).replace('.', decimalPoint);\n                }\n                else if (typeof cellValue === 'string') {\n                    cellValue = \"\\\"\".concat(cellValue, \"\\\"\");\n                }\n                rowArray[rowIndex][columnIndex] = cellValue;\n                // On the final column, push the row to the CSV\n                if (columnIndex === columnsCount - 1) {\n                    // Trim repeated undefined values starting at the end\n                    // Currently, we export the first \"comma\" even if the\n                    // second value is undefined\n                    var i = columnIndex;\n                    while (rowArray[rowIndex].length > 2) {\n                        var cellVal = rowArray[rowIndex][i];\n                        if (cellVal !== void 0) {\n                            break;\n                        }\n                        rowArray[rowIndex].pop();\n                        i--;\n                    }\n                    csvRows.push(rowArray[rowIndex].join(itemDelimiter));\n                }\n            }\n        }\n        return csvRows.join(lineDelimiter);\n    };\n    /**\n     * Initiates parsing of CSV\n     *\n     * @param {CSVConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits CSVDataParser#parse\n     * @emits CSVDataParser#afterParse\n     */\n    CSVConverter.prototype.parse = function (options, eventDetail) {\n        var converter = this,\n            dataTypes = converter.dataTypes,\n            parserOptions = CSVConverter_merge(this.options,\n            options),\n            beforeParse = parserOptions.beforeParse,\n            lineDelimiter = parserOptions.lineDelimiter,\n            firstRowAsNames = parserOptions.firstRowAsNames,\n            itemDelimiter = parserOptions.itemDelimiter;\n        var lines,\n            rowIt = 0,\n            csv = parserOptions.csv,\n            startRow = parserOptions.startRow,\n            endRow = parserOptions.endRow,\n            column;\n        converter.columns = [];\n        converter.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n        if (csv && beforeParse) {\n            csv = beforeParse(csv);\n        }\n        if (csv) {\n            lines = csv\n                .replace(/\\r\\n|\\r/g, '\\n') // Windows | Mac\n                .split(lineDelimiter || '\\n');\n            if (!startRow || startRow < 0) {\n                startRow = 0;\n            }\n            if (!endRow || endRow >= lines.length) {\n                endRow = lines.length - 1;\n            }\n            if (!itemDelimiter) {\n                converter.guessedItemDelimiter =\n                    converter.guessDelimiter(lines);\n            }\n            // If the first row contain names, add them to the\n            // headers array and skip the row.\n            if (firstRowAsNames) {\n                var headers = lines[0].split(itemDelimiter || converter.guessedItemDelimiter || ',');\n                // Remove \"\"s from the headers\n                for (var i = 0; i < headers.length; i++) {\n                    headers[i] = headers[i].trim().replace(/^[\"']|[\"']$/g, '');\n                }\n                converter.headers = headers;\n                startRow++;\n            }\n            var offset = 0;\n            for (rowIt = startRow; rowIt <= endRow; rowIt++) {\n                if (lines[rowIt][0] === '#') {\n                    offset++;\n                }\n                else {\n                    converter\n                        .parseCSVRow(lines[rowIt], rowIt - startRow - offset);\n                }\n            }\n            if (dataTypes.length &&\n                dataTypes[0].length &&\n                dataTypes[0][1] === 'date' && // Format is a string date\n                !converter.options.dateFormat) {\n                converter.deduceDateFormat(converter.columns[0], null, true);\n            }\n            // Guess types.\n            for (var i = 0, iEnd = converter.columns.length; i < iEnd; ++i) {\n                column = converter.columns[i];\n                for (var j = 0, jEnd = column.length; j < jEnd; ++j) {\n                    if (column[j] && typeof column[j] === 'string') {\n                        var cellValue = converter.asGuessedType(column[j]);\n                        if (cellValue instanceof Date) {\n                            cellValue = cellValue.getTime();\n                        }\n                        converter.columns[i][j] = cellValue;\n                    }\n                }\n            }\n        }\n        converter.emit({\n            type: 'afterParse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n    };\n    /**\n     * Internal method that parses a single CSV row\n     */\n    CSVConverter.prototype.parseCSVRow = function (columnStr, rowNumber) {\n        var converter = this,\n            columns = converter.columns || [],\n            dataTypes = converter.dataTypes,\n            _a = converter.options,\n            startColumn = _a.startColumn,\n            endColumn = _a.endColumn,\n            itemDelimiter = (converter.options.itemDelimiter ||\n                converter.guessedItemDelimiter);\n        var decimalPoint = converter.options.decimalPoint;\n        if (!decimalPoint || decimalPoint === itemDelimiter) {\n            decimalPoint = converter.guessedDecimalPoint || '.';\n        }\n        var i = 0, c = '', token = '', actualColumn = 0, column = 0;\n        var read = function (j) {\n                c = columnStr[j];\n        };\n        var pushType = function (type) {\n                if (dataTypes.length < column + 1) {\n                    dataTypes.push([type]);\n            }\n            if (dataTypes[column][dataTypes[column].length - 1] !== type) {\n                dataTypes[column].push(type);\n            }\n        };\n        var push = function () {\n                if (startColumn > actualColumn || actualColumn > endColumn) {\n                    // Skip this column, but increment the column count (#7272)\n                    ++actualColumn;\n                token = '';\n                return;\n            }\n            // Save the type of the token.\n            if (typeof token === 'string') {\n                if (!isNaN(parseFloat(token)) && isFinite(token)) {\n                    token = parseFloat(token);\n                    pushType('number');\n                }\n                else if (!isNaN(Date.parse(token))) {\n                    token = token.replace(/\\//g, '-');\n                    pushType('date');\n                }\n                else {\n                    pushType('string');\n                }\n            }\n            else {\n                pushType('number');\n            }\n            if (columns.length < column + 1) {\n                columns.push([]);\n            }\n            // Try to apply the decimal point, and check if the token then is a\n            // number. If not, reapply the initial value\n            if (typeof token !== 'number' &&\n                converter.guessType(token) !== 'number' &&\n                decimalPoint) {\n                var initialValue = token;\n                token = token.replace(decimalPoint, '.');\n                if (converter.guessType(token) !== 'number') {\n                    token = initialValue;\n                }\n            }\n            columns[column][rowNumber] = token;\n            token = '';\n            ++column;\n            ++actualColumn;\n        };\n        if (!columnStr.trim().length) {\n            return;\n        }\n        if (columnStr.trim()[0] === '#') {\n            return;\n        }\n        for (; i < columnStr.length; i++) {\n            read(i);\n            if (c === '#') {\n                // If there are hexvalues remaining (#13283)\n                if (!/^#[A-F\\d]{3,3}|[A-F\\d]{6,6}/i.test(columnStr.substring(i))) {\n                    // The rest of the row is a comment\n                    push();\n                    return;\n                }\n            }\n            // Quoted string\n            if (c === '\"') {\n                read(++i);\n                while (i < columnStr.length) {\n                    if (c === '\"') {\n                        break;\n                    }\n                    token += c;\n                    read(++i);\n                }\n            }\n            else if (c === itemDelimiter) {\n                push();\n                // Actual column data\n            }\n            else {\n                token += c;\n            }\n        }\n        push();\n    };\n    /**\n     * Internal method that guesses the delimiter from the first\n     * 13 lines of the CSV\n     * @param {Array<string>} lines\n     * The CSV, split into lines\n     */\n    CSVConverter.prototype.guessDelimiter = function (lines) {\n        var points = 0,\n            commas = 0,\n            guessed;\n        var potDelimiters = {\n                ',': 0,\n                ';': 0,\n                '\\t': 0\n            }, linesCount = lines.length;\n        for (var i = 0; i < linesCount; i++) {\n            var inStr = false,\n                c = void 0,\n                cn = void 0,\n                cl = void 0,\n                token = '';\n            // We should be able to detect dateformats within 13 rows\n            if (i > 13) {\n                break;\n            }\n            var columnStr = lines[i];\n            for (var j = 0; j < columnStr.length; j++) {\n                c = columnStr[j];\n                cn = columnStr[j + 1];\n                cl = columnStr[j - 1];\n                if (c === '#') {\n                    // Skip the rest of the line - it's a comment\n                    break;\n                }\n                if (c === '\"') {\n                    if (inStr) {\n                        if (cl !== '\"' && cn !== '\"') {\n                            while (cn === ' ' && j < columnStr.length) {\n                                cn = columnStr[++j];\n                            }\n                            // After parsing a string, the next non-blank\n                            // should be a delimiter if the CSV is properly\n                            // formed.\n                            if (typeof potDelimiters[cn] !== 'undefined') {\n                                potDelimiters[cn]++;\n                            }\n                            inStr = false;\n                        }\n                    }\n                    else {\n                        inStr = true;\n                    }\n                }\n                else if (typeof potDelimiters[c] !== 'undefined') {\n                    token = token.trim();\n                    if (!isNaN(Date.parse(token))) {\n                        potDelimiters[c]++;\n                    }\n                    else if (isNaN(Number(token)) ||\n                        !isFinite(Number(token))) {\n                        potDelimiters[c]++;\n                    }\n                    token = '';\n                }\n                else {\n                    token += c;\n                }\n                if (c === ',') {\n                    commas++;\n                }\n                if (c === '.') {\n                    points++;\n                }\n            }\n        }\n        // Count the potential delimiters.\n        // This could be improved by checking if the number of delimiters\n        // equals the number of columns - 1\n        if (potDelimiters[';'] > potDelimiters[',']) {\n            guessed = ';';\n        }\n        else if (potDelimiters[','] > potDelimiters[';']) {\n            guessed = ',';\n        }\n        else {\n            // No good guess could be made..\n            guessed = ',';\n        }\n        // Try to deduce the decimal point if it's not explicitly set.\n        // If both commas or points is > 0 there is likely an issue\n        if (points > commas) {\n            this.guessedDecimalPoint = '.';\n        }\n        else {\n            this.guessedDecimalPoint = ',';\n        }\n        return guessed;\n    };\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed CSV.\n     */\n    CSVConverter.prototype.getTable = function () {\n        return Converters_DataConverter.getTableFromColumns(this.columns, this.headers);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options\n     */\n    CSVConverter.defaultOptions = CSVConverter_assign(CSVConverter_assign({}, Converters_DataConverter.defaultOptions), { lineDelimiter: '\\n' });\n    return CSVConverter;\n}(Converters_DataConverter));\nConverters_DataConverter.registerType('CSV', CSVConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Converters_CSVConverter = (CSVConverter);\n\n;// ./code/es5/es-modules/Data/Connectors/CSVConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Christer Vasseng\n *  - Gøran Slettemark\n *  - Sophie Bremer\n *\n * */\n\nvar CSVConnector_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar CSVConnector_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class that handles creating a DataConnector from CSV\n *\n * @private\n */\nvar CSVConnector = /** @class */ (function (_super) {\n    CSVConnector_extends(CSVConnector, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of CSVConnector.\n     *\n     * @param {CSVConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    function CSVConnector(options) {\n        var _this = this;\n        var mergedOptions = CSVConnector_merge(CSVConnector.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        _this.converter = new Converters_CSVConverter(mergedOptions);\n        _this.options = mergedOptions;\n        if (mergedOptions.enablePolling) {\n            _this.startPolling(Math.max(mergedOptions.dataRefreshRate || 0, 1) * 1000);\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates the loading of the CSV source to the connector\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits CSVConnector#load\n     * @emits CSVConnector#afterLoad\n     */\n    CSVConnector.prototype.load = function (eventDetail) {\n        var connector = this,\n            converter = connector.converter,\n            table = connector.table,\n            _a = connector.options,\n            csv = _a.csv,\n            csvURL = _a.csvURL,\n            dataModifier = _a.dataModifier;\n        connector.emit({\n            type: 'load',\n            csv: csv,\n            detail: eventDetail,\n            table: table\n        });\n        return Promise\n            .resolve(csvURL ?\n            fetch(csvURL).then(function (response) { return response.text(); }) :\n            csv || '')\n            .then(function (csv) {\n            if (csv) {\n                // If already loaded, clear the current rows\n                table.deleteColumns();\n                converter.parse({ csv: csv });\n                table.setColumns(converter.getTable().getColumns());\n            }\n            return connector\n                .setModifierOptions(dataModifier)\n                .then(function () { return csv; });\n        })\n            .then(function (csv) {\n            connector.emit({\n                type: 'afterLoad',\n                csv: csv,\n                detail: eventDetail,\n                table: table\n            });\n            return connector;\n        })['catch'](function (error) {\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error: error,\n                table: table\n            });\n            throw error;\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    CSVConnector.defaultOptions = {\n        csv: '',\n        csvURL: '',\n        enablePolling: false,\n        dataRefreshRate: 1,\n        firstRowAsNames: true\n    };\n    return CSVConnector;\n}(Connectors_DataConnector));\nConnectors_DataConnector.registerType('CSV', CSVConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Connectors_CSVConnector = ((/* unused pure expression or super */ null && (CSVConnector)));\n\n;// ./code/es5/es-modules/Data/Converters/JSONConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\nvar JSONConverter_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar JSONConverter_assign = (undefined && undefined.__assign) || function () {\n    JSONConverter_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return JSONConverter_assign.apply(this, arguments);\n};\n\n\n\nvar error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, JSONConverter_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, JSONConverter_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transforming JSON to a table.\n *\n * @private\n */\nvar JSONConverter = /** @class */ (function (_super) {\n    JSONConverter_extends(JSONConverter, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the JSON parser.\n     *\n     * @param {JSONConverter.UserOptions} [options]\n     * Options for the JSON parser.\n     */\n    function JSONConverter(options) {\n        var _this = this;\n        var mergedOptions = JSONConverter_merge(JSONConverter.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.columns = [];\n        _this.headers = [];\n        _this.options = mergedOptions;\n        _this.table = new Data_DataTable();\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates parsing of JSON structure.\n     *\n     * @param {JSONConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits JSONConverter#parse\n     * @emits JSONConverter#afterParse\n     */\n    JSONConverter.prototype.parse = function (options, eventDetail) {\n        var converter = this;\n        options = JSONConverter_merge(converter.options, options);\n        var beforeParse = options.beforeParse,\n            orientation = options.orientation,\n            firstRowAsNames = options.firstRowAsNames,\n            columnNames = options.columnNames;\n        var data = options.data;\n        if (!data) {\n            return;\n        }\n        converter.columns = [];\n        converter.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n        if (beforeParse) {\n            data = beforeParse(data);\n        }\n        data = data.slice();\n        if (orientation === 'columns') {\n            for (var i = 0, iEnd = data.length; i < iEnd; i++) {\n                var item = data[i];\n                if (!(item instanceof Array)) {\n                    return;\n                }\n                if (converter.headers instanceof Array) {\n                    if (firstRowAsNames) {\n                        converter.headers.push(\"\".concat(item.shift()));\n                    }\n                    else if (columnNames && columnNames instanceof Array) {\n                        converter.headers.push(columnNames[i]);\n                    }\n                    converter.table.setColumn(converter.headers[i] || i.toString(), item);\n                }\n                else {\n                    error('JSONConverter: Invalid `columnNames` option.', false);\n                }\n            }\n        }\n        else if (orientation === 'rows') {\n            if (firstRowAsNames) {\n                converter.headers = data.shift();\n            }\n            else if (columnNames) {\n                converter.headers = columnNames;\n            }\n            var _loop_1 = function (rowIndex,\n                iEnd) {\n                    var row = data[rowIndex];\n                if (isArray(row)) {\n                    for (var columnIndex = 0, jEnd = row.length; columnIndex < jEnd; columnIndex++) {\n                        if (converter.columns.length < columnIndex + 1) {\n                            converter.columns.push([]);\n                        }\n                        converter.columns[columnIndex].push(row[columnIndex]);\n                        if (converter.headers instanceof Array) {\n                            this_1.table.setColumn(converter.headers[columnIndex] ||\n                                columnIndex.toString(), converter.columns[columnIndex]);\n                        }\n                        else {\n                            error('JSONConverter: Invalid `columnNames` option.', false);\n                        }\n                    }\n                }\n                else {\n                    var columnNames_1 = converter.headers;\n                    if (columnNames_1 && !(columnNames_1 instanceof Array)) {\n                        var newRow_1 = {};\n                        JSONConverter_objectEach(columnNames_1, function (arrayWithPath, name) {\n                            newRow_1[name] = arrayWithPath.reduce(function (acc, key) {\n                                return acc[key];\n                            }, row);\n                        });\n                        row = newRow_1;\n                    }\n                    this_1.table.setRows([row], rowIndex);\n                }\n            };\n            var this_1 = this;\n            for (var rowIndex = 0, iEnd = data.length; rowIndex < iEnd; rowIndex++) {\n                _loop_1(rowIndex, iEnd);\n            }\n        }\n        converter.emit({\n            type: 'afterParse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n    };\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed CSV.\n     */\n    JSONConverter.prototype.getTable = function () {\n        return this.table;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options\n     */\n    JSONConverter.defaultOptions = JSONConverter_assign(JSONConverter_assign({}, Converters_DataConverter.defaultOptions), { data: [], orientation: 'rows' });\n    return JSONConverter;\n}(Converters_DataConverter));\nConverters_DataConverter.registerType('JSON', JSONConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Converters_JSONConverter = (JSONConverter);\n\n;// ./code/es5/es-modules/Data/Connectors/JSONConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\nvar JSONConnector_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar JSONConnector_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class that handles creating a DataConnector from JSON structure\n *\n * @private\n */\nvar JSONConnector = /** @class */ (function (_super) {\n    JSONConnector_extends(JSONConnector, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of JSONConnector.\n     *\n     * @param {JSONConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    function JSONConnector(options) {\n        var _this = this;\n        var mergedOptions = JSONConnector_merge(JSONConnector.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        _this.converter = new Converters_JSONConverter(mergedOptions);\n        _this.options = mergedOptions;\n        if (mergedOptions.enablePolling) {\n            _this.startPolling(Math.max(mergedOptions.dataRefreshRate || 0, 1) * 1000);\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates the loading of the JSON source to the connector\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits JSONConnector#load\n     * @emits JSONConnector#afterLoad\n     */\n    JSONConnector.prototype.load = function (eventDetail) {\n        var connector = this,\n            converter = connector.converter,\n            table = connector.table,\n            _a = connector.options,\n            data = _a.data,\n            dataUrl = _a.dataUrl,\n            dataModifier = _a.dataModifier;\n        connector.emit({\n            type: 'load',\n            data: data,\n            detail: eventDetail,\n            table: table\n        });\n        return Promise\n            .resolve(dataUrl ?\n            fetch(dataUrl).then(function (response) { return response.json(); })['catch'](function (error) {\n                connector.emit({\n                    type: 'loadError',\n                    detail: eventDetail,\n                    error: error,\n                    table: table\n                });\n                console.warn(\"Unable to fetch data from \".concat(dataUrl, \".\")); // eslint-disable-line no-console\n            }) :\n            data || [])\n            .then(function (data) {\n            if (data) {\n                // If already loaded, clear the current rows\n                table.deleteColumns();\n                converter.parse({ data: data });\n                table.setColumns(converter.getTable().getColumns());\n            }\n            return connector.setModifierOptions(dataModifier).then(function () { return data; });\n        })\n            .then(function (data) {\n            connector.emit({\n                type: 'afterLoad',\n                data: data,\n                detail: eventDetail,\n                table: table\n            });\n            return connector;\n        })['catch'](function (error) {\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error: error,\n                table: table\n            });\n            throw error;\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    JSONConnector.defaultOptions = {\n        data: [],\n        enablePolling: false,\n        dataRefreshRate: 0,\n        firstRowAsNames: true,\n        orientation: 'rows'\n    };\n    return JSONConnector;\n}(Connectors_DataConnector));\nConnectors_DataConnector.registerType('JSON', JSONConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Connectors_JSONConnector = ((/* unused pure expression or super */ null && (JSONConnector)));\n\n;// ./code/es5/es-modules/Data/Converters/GoogleSheetsConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *\n * */\n\nvar GoogleSheetsConverter_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar GoogleSheetsConverter_assign = (undefined && undefined.__assign) || function () {\n    GoogleSheetsConverter_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return GoogleSheetsConverter_assign.apply(this, arguments);\n};\n\n\nvar GoogleSheetsConverter_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, GoogleSheetsConverter_uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transformation of an Google Sheets to a table.\n *\n * @private\n */\nvar GoogleSheetsConverter = /** @class */ (function (_super) {\n    GoogleSheetsConverter_extends(GoogleSheetsConverter, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the GoogleSheetsConverter.\n     *\n     * @param {GoogleSheetsConverter.UserOptions} [options]\n     * Options for the GoogleSheetsConverter.\n     */\n    function GoogleSheetsConverter(options) {\n        var _this = this;\n        var mergedOptions = GoogleSheetsConverter_merge(GoogleSheetsConverter.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        _this.columns = [];\n        _this.header = [];\n        _this.options = mergedOptions;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initiates the parsing of the Google Sheet\n     *\n     * @param {GoogleSheetsConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits GoogleSheetsParser#parse\n     * @emits GoogleSheetsParser#afterParse\n     */\n    GoogleSheetsConverter.prototype.parse = function (options, eventDetail) {\n        var _a;\n        var converter = this,\n            parseOptions = GoogleSheetsConverter_merge(converter.options,\n            options);\n        var columns = (((_a = parseOptions.json) === null || _a === void 0 ? void 0 : _a.values) || []).map(function (column) { return column.slice(); });\n        if (columns.length === 0) {\n            return false;\n        }\n        converter.header = [];\n        converter.columns = [];\n        converter.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.header\n        });\n        // If beforeParse is defined, use it to modify the data\n        var beforeParse = parseOptions.beforeParse,\n            json = parseOptions.json;\n        if (beforeParse && json) {\n            columns = beforeParse(json.values);\n        }\n        var column;\n        converter.columns = columns;\n        for (var i = 0, iEnd = columns.length; i < iEnd; i++) {\n            column = columns[i];\n            converter.header[i] = (parseOptions.firstRowAsNames ?\n                \"\".concat(column.shift()) :\n                GoogleSheetsConverter_uniqueKey());\n            for (var j = 0, jEnd = column.length; j < jEnd; ++j) {\n                if (column[j] && typeof column[j] === 'string') {\n                    var cellValue = converter.asGuessedType(column[j]);\n                    if (cellValue instanceof Date) {\n                        cellValue = cellValue.getTime();\n                    }\n                    converter.columns[i][j] = cellValue;\n                }\n            }\n        }\n        converter.emit({\n            type: 'afterParse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.header\n        });\n    };\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed Google Sheet\n     */\n    GoogleSheetsConverter.prototype.getTable = function () {\n        return Converters_DataConverter.getTableFromColumns(this.columns, this.header);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options\n     */\n    GoogleSheetsConverter.defaultOptions = GoogleSheetsConverter_assign({}, Converters_DataConverter.defaultOptions);\n    return GoogleSheetsConverter;\n}(Converters_DataConverter));\nConverters_DataConverter.registerType('GoogleSheets', GoogleSheetsConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Converters_GoogleSheetsConverter = (GoogleSheetsConverter);\n\n;// ./code/es5/es-modules/Data/Connectors/GoogleSheetsConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *  - Jomar Hønsi\n *\n * */\n\nvar GoogleSheetsConnector_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar GoogleSheetsConnector_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, GoogleSheetsConnector_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Tests Google's response for error.\n * @private\n */\nfunction isGoogleError(json) {\n    return (typeof json === 'object' && json &&\n        typeof json.error === 'object' && json.error &&\n        typeof json.error.code === 'number' &&\n        typeof json.error.message === 'string' &&\n        typeof json.error.status === 'string');\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @todo implement save, requires oauth2\n */\nvar GoogleSheetsConnector = /** @class */ (function (_super) {\n    GoogleSheetsConnector_extends(GoogleSheetsConnector, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of GoogleSheetsConnector\n     *\n     * @param {GoogleSheetsConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    function GoogleSheetsConnector(options) {\n        var _this = this;\n        var mergedOptions = GoogleSheetsConnector_merge(GoogleSheetsConnector.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        _this.converter = new Converters_GoogleSheetsConverter(mergedOptions);\n        _this.options = mergedOptions;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Loads data from a Google Spreadsheet.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<this>}\n     * Same connector instance with modified table.\n     */\n    GoogleSheetsConnector.prototype.load = function (eventDetail) {\n        var connector = this,\n            converter = connector.converter,\n            table = connector.table,\n            _a = connector.options,\n            dataModifier = _a.dataModifier,\n            dataRefreshRate = _a.dataRefreshRate,\n            enablePolling = _a.enablePolling,\n            firstRowAsNames = _a.firstRowAsNames,\n            googleAPIKey = _a.googleAPIKey,\n            googleSpreadsheetKey = _a.googleSpreadsheetKey,\n            url = GoogleSheetsConnector.buildFetchURL(googleAPIKey,\n            googleSpreadsheetKey,\n            connector.options);\n        connector.emit({\n            type: 'load',\n            detail: eventDetail,\n            table: table,\n            url: url\n        });\n        if (!URL.canParse(url)) {\n            throw new Error('Invalid URL: ' + url);\n        }\n        return fetch(url)\n            .then(function (response) { return (response.json()); })\n            .then(function (json) {\n            if (isGoogleError(json)) {\n                throw new Error(json.error.message);\n            }\n            converter.parse({\n                firstRowAsNames: firstRowAsNames,\n                json: json\n            });\n            // If already loaded, clear the current table\n            table.deleteColumns();\n            table.setColumns(converter.getTable().getColumns());\n            return connector.setModifierOptions(dataModifier);\n        })\n            .then(function () {\n            connector.emit({\n                type: 'afterLoad',\n                detail: eventDetail,\n                table: table,\n                url: url\n            });\n            // Polling\n            if (enablePolling) {\n                setTimeout(function () { return connector.load(); }, Math.max(dataRefreshRate || 0, 1) * 1000);\n            }\n            return connector;\n        })['catch'](function (error) {\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error: error,\n                table: table\n            });\n            throw error;\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    GoogleSheetsConnector.defaultOptions = {\n        googleAPIKey: '',\n        googleSpreadsheetKey: '',\n        enablePolling: false,\n        dataRefreshRate: 2,\n        firstRowAsNames: true\n    };\n    return GoogleSheetsConnector;\n}(Connectors_DataConnector));\n/* *\n *\n *  Class Namespace\n *\n * */\n(function (GoogleSheetsConnector) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    var alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Creates GoogleSheets API v4 URL.\n     * @private\n     */\n    function buildFetchURL(apiKey, sheetKey, options) {\n        if (options === void 0) { options = {}; }\n        var url = new URL(\"https://sheets.googleapis.com/v4/spreadsheets/\".concat(sheetKey, \"/values/\"));\n        var range = options.onlyColumnNames ?\n                'A1:Z1' : buildQueryRange(options);\n        url.pathname += range;\n        var searchParams = url.searchParams;\n        searchParams.set('alt', 'json');\n        if (!options.onlyColumnNames) {\n            searchParams.set('dateTimeRenderOption', 'FORMATTED_STRING');\n            searchParams.set('majorDimension', 'COLUMNS');\n            searchParams.set('valueRenderOption', 'UNFORMATTED_VALUE');\n        }\n        searchParams.set('prettyPrint', 'false');\n        searchParams.set('key', apiKey);\n        return url.href;\n    }\n    GoogleSheetsConnector.buildFetchURL = buildFetchURL;\n    /**\n     * Creates sheets range.\n     * @private\n     */\n    function buildQueryRange(options) {\n        if (options === void 0) { options = {}; }\n        var endColumn = options.endColumn,\n            endRow = options.endRow,\n            googleSpreadsheetRange = options.googleSpreadsheetRange,\n            startColumn = options.startColumn,\n            startRow = options.startRow;\n        return googleSpreadsheetRange || ((alphabet[startColumn || 0] || 'A') +\n            (Math.max((startRow || 0), 0) + 1) +\n            ':' +\n            (alphabet[GoogleSheetsConnector_pick(endColumn, 25)] || 'Z') +\n            (endRow ?\n                Math.max(endRow, 0) :\n                'Z'));\n    }\n    GoogleSheetsConnector.buildQueryRange = buildQueryRange;\n})(GoogleSheetsConnector || (GoogleSheetsConnector = {}));\nConnectors_DataConnector.registerType('GoogleSheets', GoogleSheetsConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Connectors_GoogleSheetsConnector = ((/* unused pure expression or super */ null && (GoogleSheetsConnector)));\n\n;// ./code/es5/es-modules/Data/Converters/HTMLTableConverter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *\n * */\n\nvar HTMLTableConverter_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar HTMLTableConverter_assign = (undefined && undefined.__assign) || function () {\n    HTMLTableConverter_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return HTMLTableConverter_assign.apply(this, arguments);\n};\n\n\nvar HTMLTableConverter_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Row equal\n */\nfunction isRowEqual(row1, row2) {\n    var i = row1.length;\n    if (row2.length === i) {\n        while (--i) {\n            if (row1[i] !== row2[i]) {\n                return false;\n            }\n        }\n    }\n    else {\n        return false;\n    }\n    return true;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles parsing and transformation of an HTML table to a table.\n *\n * @private\n */\nvar HTMLTableConverter = /** @class */ (function (_super) {\n    HTMLTableConverter_extends(HTMLTableConverter, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the HTMLTableConverter.\n     *\n     * @param {HTMLTableConverter.UserOptions} [options]\n     * Options for the HTMLTableConverter.\n     */\n    function HTMLTableConverter(options) {\n        var _this = this;\n        var mergedOptions = HTMLTableConverter_merge(HTMLTableConverter.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        _this.columns = [];\n        _this.headers = [];\n        _this.options = mergedOptions;\n        if (mergedOptions.tableElement) {\n            _this.tableElement = mergedOptions.tableElement;\n            _this.tableElementID = mergedOptions.tableElement.id;\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Exports the dataconnector as an HTML string, using the options\n     * provided on import unless other options are provided.\n     *\n     * @param {DataConnector} connector\n     * Connector instance to export from.\n     *\n     * @param {HTMLTableConnector.ExportOptions} [options]\n     * Options that override default or existing export options.\n     *\n     * @return {string}\n     * HTML from the current dataTable.\n     */\n    HTMLTableConverter.prototype.export = function (connector, options) {\n        if (options === void 0) { options = this.options; }\n        var exportNames = (options.firstRowAsNames !== false),\n            useMultiLevelHeaders = options.useMultiLevelHeaders;\n        var columns = connector.getSortedColumns(options.usePresentationOrder),\n            columnNames = Object.keys(columns),\n            htmlRows = [],\n            columnsCount = columnNames.length;\n        var rowArray = [];\n        var tableHead = '';\n        // Add the names as the first row if they should be exported\n        if (exportNames) {\n            var subcategories = [];\n            // If using multilevel headers, the first value\n            // of each column is a subcategory\n            if (useMultiLevelHeaders) {\n                for (var _i = 0, columnNames_1 = columnNames; _i < columnNames_1.length; _i++) {\n                    var name_1 = columnNames_1[_i];\n                    var column = columns[name_1];\n                    if (!Array.isArray(column)) {\n                        // Convert to conventional array from typed array\n                        // if needed\n                        column = Array.from(column);\n                    }\n                    var subhead = (column.shift() || '').toString();\n                    columns[name_1] = column;\n                    subcategories.push(subhead);\n                }\n                tableHead = this.getTableHeaderHTML(columnNames, subcategories, options);\n            }\n            else {\n                tableHead = this.getTableHeaderHTML(void 0, columnNames, options);\n            }\n        }\n        for (var columnIndex = 0; columnIndex < columnsCount; columnIndex++) {\n            var columnName = columnNames[columnIndex],\n                column = columns[columnName],\n                columnLength = column.length;\n            for (var rowIndex = 0; rowIndex < columnLength; rowIndex++) {\n                var cellValue = column[rowIndex];\n                if (!rowArray[rowIndex]) {\n                    rowArray[rowIndex] = [];\n                }\n                // Alternative: Datatype from HTML attribute with\n                // connector.whatIs(columnName)\n                if (!(typeof cellValue === 'string' ||\n                    typeof cellValue === 'number' ||\n                    typeof cellValue === 'undefined')) {\n                    cellValue = (cellValue || '').toString();\n                }\n                rowArray[rowIndex][columnIndex] = this.getCellHTMLFromValue(columnIndex ? 'td' : 'th', null, columnIndex ? '' : 'scope=\"row\"', cellValue);\n                // On the final column, push the row to the array\n                if (columnIndex === columnsCount - 1) {\n                    htmlRows.push('<tr>' +\n                        rowArray[rowIndex].join('') +\n                        '</tr>');\n                }\n            }\n        }\n        var caption = '';\n        // Add table caption\n        // Current exportdata falls back to chart title\n        // but that should probably be handled elsewhere?\n        if (options.tableCaption) {\n            caption = '<caption class=\"highcharts-table-caption\">' +\n                options.tableCaption +\n                '</caption>';\n        }\n        return ('<table>' +\n            caption +\n            tableHead +\n            '<tbody>' +\n            htmlRows.join('') +\n            '</tbody>' +\n            '</table>');\n    };\n    /**\n     * Get table cell markup from row data.\n     */\n    HTMLTableConverter.prototype.getCellHTMLFromValue = function (tag, classes, attrs, value, decimalPoint) {\n        var val = value,\n            className = 'text' + (classes ? ' ' + classes : '');\n        // Convert to string if number\n        if (typeof val === 'number') {\n            val = val.toString();\n            if (decimalPoint === ',') {\n                val = val.replace('.', decimalPoint);\n            }\n            className = 'number';\n        }\n        else if (!value) {\n            val = '';\n            className = 'empty';\n        }\n        return '<' + tag + (attrs ? ' ' + attrs : '') +\n            ' class=\"' + className + '\">' +\n            val + '</' + tag + '>';\n    };\n    /**\n     * Get table header markup from row data.\n     */\n    HTMLTableConverter.prototype.getTableHeaderHTML = function (topheaders, subheaders, options) {\n        if (topheaders === void 0) { topheaders = []; }\n        if (subheaders === void 0) { subheaders = []; }\n        if (options === void 0) { options = this.options; }\n        var useMultiLevelHeaders = options.useMultiLevelHeaders,\n            useRowspanHeaders = options.useRowspanHeaders;\n        var html = '<thead>',\n            i = 0,\n            len = subheaders && subheaders.length,\n            next,\n            cur,\n            curColspan = 0,\n            rowspan;\n        // Clean up multiple table headers. Chart.getDataRows() returns two\n        // levels of headers when using multilevel, not merged. We need to\n        // merge identical headers, remove redundant headers, and keep it\n        // all marked up nicely.\n        if (useMultiLevelHeaders &&\n            topheaders &&\n            subheaders &&\n            !isRowEqual(topheaders, subheaders)) {\n            html += '<tr>';\n            for (; i < len; ++i) {\n                cur = topheaders[i];\n                next = topheaders[i + 1];\n                if (cur === next) {\n                    ++curColspan;\n                }\n                else if (curColspan) {\n                    // Ended colspan\n                    // Add cur to HTML with colspan.\n                    html += this.getCellHTMLFromValue('th', 'highcharts-table-topheading', 'scope=\"col\" ' +\n                        'colspan=\"' + (curColspan + 1) + '\"', cur);\n                    curColspan = 0;\n                }\n                else {\n                    // Cur is standalone. If it is same as sublevel,\n                    // remove sublevel and add just toplevel.\n                    if (cur === subheaders[i]) {\n                        if (useRowspanHeaders) {\n                            rowspan = 2;\n                            delete subheaders[i];\n                        }\n                        else {\n                            rowspan = 1;\n                            subheaders[i] = '';\n                        }\n                    }\n                    else {\n                        rowspan = 1;\n                    }\n                    html += this.getCellHTMLFromValue('th', 'highcharts-table-topheading', 'scope=\"col\"' +\n                        (rowspan > 1 ?\n                            ' valign=\"top\" rowspan=\"' + rowspan + '\"' :\n                            ''), cur);\n                }\n            }\n            html += '</tr>';\n        }\n        // Add the subheaders (the only headers if not using multilevels)\n        if (subheaders) {\n            html += '<tr>';\n            for (i = 0, len = subheaders.length; i < len; ++i) {\n                if (typeof subheaders[i] !== 'undefined') {\n                    html += this.getCellHTMLFromValue('th', null, 'scope=\"col\"', subheaders[i]);\n                }\n            }\n            html += '</tr>';\n        }\n        html += '</thead>';\n        return html;\n    };\n    /**\n     * Initiates the parsing of the HTML table\n     *\n     * @param {HTMLTableConverter.UserOptions}[options]\n     * Options for the parser\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits CSVDataParser#parse\n     * @emits CSVDataParser#afterParse\n     * @emits HTMLTableParser#parseError\n     */\n    HTMLTableConverter.prototype.parse = function (options, eventDetail) {\n        var converter = this,\n            columns = [],\n            headers = [],\n            parseOptions = HTMLTableConverter_merge(converter.options,\n            options),\n            endRow = parseOptions.endRow,\n            startColumn = parseOptions.startColumn,\n            endColumn = parseOptions.endColumn,\n            firstRowAsNames = parseOptions.firstRowAsNames,\n            tableHTML = parseOptions.tableElement || this.tableElement;\n        if (!(tableHTML instanceof HTMLElement)) {\n            converter.emit({\n                type: 'parseError',\n                columns: columns,\n                detail: eventDetail,\n                headers: headers,\n                error: 'Not a valid HTML Table'\n            });\n            return;\n        }\n        converter.tableElement = tableHTML;\n        converter.tableElementID = tableHTML.id;\n        this.emit({\n            type: 'parse',\n            columns: converter.columns,\n            detail: eventDetail,\n            headers: converter.headers\n        });\n        var rows = tableHTML.getElementsByTagName('tr'),\n            rowsCount = rows.length;\n        var rowIndex = 0,\n            item,\n            startRow = parseOptions.startRow;\n        // Insert headers from the first row\n        if (firstRowAsNames && rowsCount) {\n            var items = rows[0].children,\n                itemsLength = items.length;\n            for (var i = startColumn; i < itemsLength; i++) {\n                if (i > endColumn) {\n                    break;\n                }\n                item = items[i];\n                if (item.tagName === 'TD' ||\n                    item.tagName === 'TH') {\n                    headers.push(item.innerHTML);\n                }\n            }\n            startRow++;\n        }\n        while (rowIndex < rowsCount) {\n            if (rowIndex >= startRow && rowIndex <= endRow) {\n                var columnsInRow = rows[rowIndex].children,\n                    columnsInRowLength = columnsInRow.length;\n                var columnIndex = 0;\n                while (columnIndex < columnsInRowLength) {\n                    var relativeColumnIndex = columnIndex - startColumn,\n                        row = columns[relativeColumnIndex];\n                    item = columnsInRow[columnIndex];\n                    if ((item.tagName === 'TD' ||\n                        item.tagName === 'TH') &&\n                        (columnIndex >= startColumn &&\n                            columnIndex <= endColumn)) {\n                        if (!columns[relativeColumnIndex]) {\n                            columns[relativeColumnIndex] = [];\n                        }\n                        var cellValue = converter.asGuessedType(item.innerHTML);\n                        if (cellValue instanceof Date) {\n                            cellValue = cellValue.getTime();\n                        }\n                        columns[relativeColumnIndex][rowIndex - startRow] = cellValue;\n                        // Loop over all previous indices and make sure\n                        // they are nulls, not undefined.\n                        var i = 1;\n                        while (rowIndex - startRow >= i &&\n                            row[rowIndex - startRow - i] === void 0) {\n                            row[rowIndex - startRow - i] = null;\n                            i++;\n                        }\n                    }\n                    columnIndex++;\n                }\n            }\n            rowIndex++;\n        }\n        this.columns = columns;\n        this.headers = headers;\n        this.emit({\n            type: 'afterParse',\n            columns: columns,\n            detail: eventDetail,\n            headers: headers\n        });\n    };\n    /**\n     * Handles converting the parsed data to a table.\n     *\n     * @return {DataTable}\n     * Table from the parsed HTML table\n     */\n    HTMLTableConverter.prototype.getTable = function () {\n        return Converters_DataConverter.getTableFromColumns(this.columns, this.headers);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options\n     */\n    HTMLTableConverter.defaultOptions = HTMLTableConverter_assign(HTMLTableConverter_assign({}, Converters_DataConverter.defaultOptions), { useRowspanHeaders: true, useMultiLevelHeaders: true });\n    return HTMLTableConverter;\n}(Converters_DataConverter));\nConverters_DataConverter.registerType('HTMLTable', HTMLTableConverter);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Converters_HTMLTableConverter = (HTMLTableConverter);\n\n;// ./code/es5/es-modules/Data/Connectors/HTMLTableConnector.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Wojciech Chmiel\n *  - Sophie Bremer\n *\n * */\n\nvar HTMLTableConnector_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\nvar HTMLTableConnector_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class that handles creating a data connector from an HTML table.\n *\n * @private\n */\nvar HTMLTableConnector = /** @class */ (function (_super) {\n    HTMLTableConnector_extends(HTMLTableConnector, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of HTMLTableConnector.\n     *\n     * @param {HTMLTableConnector.UserOptions} [options]\n     * Options for the connector and converter.\n     */\n    function HTMLTableConnector(options) {\n        var _this = this;\n        var mergedOptions = HTMLTableConnector_merge(HTMLTableConnector.defaultOptions,\n            options);\n        _this = _super.call(this, mergedOptions) || this;\n        _this.converter = new Converters_HTMLTableConverter(mergedOptions);\n        _this.options = mergedOptions;\n        return _this;\n    }\n    /**\n     * Initiates creating the dataconnector from the HTML table\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits HTMLTableConnector#load\n     * @emits HTMLTableConnector#afterLoad\n     * @emits HTMLTableConnector#loadError\n     */\n    HTMLTableConnector.prototype.load = function (eventDetail) {\n        var connector = this,\n            converter = connector.converter,\n            table = connector.table,\n            _a = connector.options,\n            dataModifier = _a.dataModifier,\n            tableHTML = _a.table;\n        connector.emit({\n            type: 'load',\n            detail: eventDetail,\n            table: table,\n            tableElement: connector.tableElement\n        });\n        var tableElement;\n        if (typeof tableHTML === 'string') {\n            connector.tableID = tableHTML;\n            tableElement = win.document.getElementById(tableHTML);\n        }\n        else {\n            tableElement = tableHTML;\n            connector.tableID = tableElement.id;\n        }\n        connector.tableElement = tableElement || void 0;\n        if (!connector.tableElement) {\n            var error = 'HTML table not provided, or element with ID not found';\n            connector.emit({\n                type: 'loadError',\n                detail: eventDetail,\n                error: error,\n                table: table\n            });\n            return Promise.reject(new Error(error));\n        }\n        converter.parse(HTMLTableConnector_merge({ tableElement: connector.tableElement }, connector.options), eventDetail);\n        // If already loaded, clear the current rows\n        table.deleteColumns();\n        table.setColumns(converter.getTable().getColumns());\n        return connector\n            .setModifierOptions(dataModifier)\n            .then(function () {\n            connector.emit({\n                type: 'afterLoad',\n                detail: eventDetail,\n                table: table,\n                tableElement: connector.tableElement\n            });\n            return connector;\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    HTMLTableConnector.defaultOptions = {\n        table: ''\n    };\n    return HTMLTableConnector;\n}(Connectors_DataConnector));\nConnectors_DataConnector.registerType('HTMLTable', HTMLTableConnector);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Connectors_HTMLTableConnector = ((/* unused pure expression or super */ null && (HTMLTableConnector)));\n\n;// ./code/es5/es-modules/Data/Modifiers/ChainModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Dawid Dragula\n *\n * */\n\nvar ChainModifier_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0,\n        sent: function() { if (t[0] & 1) throw t[1]; return t[1]; },\n        trys: [],\n        ops: [] },\n        f,\n        y,\n        t,\n        g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\n\n\nvar ChainModifier_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Modifies a table with the help of modifiers in an ordered chain.\n *\n */\nvar ChainModifier = /** @class */ (function (_super) {\n    ChainModifier_extends(ChainModifier, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the modifier chain.\n     *\n     * @param {Partial<ChainModifier.Options>} [options]\n     * Options to configure the modifier chain.\n     *\n     * @param {...DataModifier} [chain]\n     * Ordered chain of modifiers.\n     */\n    function ChainModifier(options) {\n        var chain = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            chain[_i - 1] = arguments[_i];\n        }\n        var _this = _super.call(this) || this;\n        _this.chain = chain;\n        _this.options = ChainModifier_merge(ChainModifier.defaultOptions, options);\n        var optionsChain = _this.options.chain || [];\n        for (var i = 0, iEnd = optionsChain.length, modifierOptions = void 0, ModifierClass = void 0; i < iEnd; ++i) {\n            modifierOptions = optionsChain[i];\n            if (!modifierOptions.type) {\n                continue;\n            }\n            ModifierClass = Modifiers_DataModifier.types[modifierOptions.type];\n            if (ModifierClass) {\n                chain.push(new ModifierClass(modifierOptions));\n            }\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds a configured modifier to the end of the modifier chain. Please note,\n     * that the modifier can be added multiple times.\n     *\n     * @param {DataModifier} modifier\n     * Configured modifier to add.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     */\n    ChainModifier.prototype.add = function (modifier, eventDetail) {\n        this.emit({\n            type: 'addModifier',\n            detail: eventDetail,\n            modifier: modifier\n        });\n        this.chain.push(modifier);\n        this.emit({\n            type: 'addModifier',\n            detail: eventDetail,\n            modifier: modifier\n        });\n    };\n    /**\n     * Clears all modifiers from the chain.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     */\n    ChainModifier.prototype.clear = function (eventDetail) {\n        this.emit({\n            type: 'clearChain',\n            detail: eventDetail\n        });\n        this.chain.length = 0;\n        this.emit({\n            type: 'afterClearChain',\n            detail: eventDetail\n        });\n    };\n    /**\n     * Applies several modifications to the table and returns a modified copy of\n     * the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Promise<Highcharts.DataTable>}\n     * Table with `modified` property as a reference.\n     */\n    ChainModifier.prototype.modify = function (table, eventDetail) {\n        return __awaiter(this, void 0, void 0, function () {\n            var modifiers,\n                modified,\n                i,\n                iEnd,\n                error_1;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        modifiers = (this.options.reverse ?\n                            this.chain.slice().reverse() :\n                            this.chain.slice());\n                        if (table.modified === table) {\n                            table.modified = table.clone(false, eventDetail);\n                        }\n                        modified = table;\n                        i = 0, iEnd = modifiers.length;\n                        _a.label = 1;\n                    case 1:\n                        if (!(i < iEnd)) return [3 /*break*/, 7];\n                        _a.label = 2;\n                    case 2:\n                        _a.trys.push([2, 4, , 5]);\n                        return [4 /*yield*/, modifiers[i].modify(modified, eventDetail)];\n                    case 3:\n                        _a.sent();\n                        return [3 /*break*/, 5];\n                    case 4:\n                        error_1 = _a.sent();\n                        this.emit({\n                            type: 'error',\n                            detail: eventDetail,\n                            table: table\n                        });\n                        throw error_1;\n                    case 5:\n                        modified = modified.modified;\n                        _a.label = 6;\n                    case 6:\n                        ++i;\n                        return [3 /*break*/, 1];\n                    case 7:\n                        table.modified = modified;\n                        return [2 /*return*/, table];\n                }\n            });\n        });\n    };\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    ChainModifier.prototype.modifyCell = function (table, columnName, rowIndex, cellValue, eventDetail) {\n        var modifiers = (this.options.reverse ?\n                this.chain.reverse() :\n                this.chain);\n        if (modifiers.length) {\n            var clone = table.clone();\n            for (var i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n                modifiers[i].modifyCell(clone, columnName, rowIndex, cellValue, eventDetail);\n                clone = clone.modified;\n            }\n            table.modified = clone;\n        }\n        return table;\n    };\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    ChainModifier.prototype.modifyColumns = function (table, columns, rowIndex, eventDetail) {\n        var modifiers = (this.options.reverse ?\n                this.chain.reverse() :\n                this.chain.slice());\n        if (modifiers.length) {\n            var clone = table.clone();\n            for (var i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n                modifiers[i].modifyColumns(clone, columns, rowIndex, eventDetail);\n                clone = clone.modified;\n            }\n            table.modified = clone;\n        }\n        return table;\n    };\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    ChainModifier.prototype.modifyRows = function (table, rows, rowIndex, eventDetail) {\n        var modifiers = (this.options.reverse ?\n                this.chain.reverse() :\n                this.chain.slice());\n        if (modifiers.length) {\n            var clone = table.clone();\n            for (var i = 0, iEnd = modifiers.length; i < iEnd; ++i) {\n                modifiers[i].modifyRows(clone, rows, rowIndex, eventDetail);\n                clone = clone.modified;\n            }\n            table.modified = clone;\n        }\n        return table;\n    };\n    /**\n     * Applies several modifications to the table.\n     *\n     * *Note:* The `modified` property of the table gets replaced.\n     *\n     * @param {DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table as a reference.\n     *\n     * @emits ChainDataModifier#execute\n     * @emits ChainDataModifier#afterExecute\n     */\n    ChainModifier.prototype.modifyTable = function (table, eventDetail) {\n        var chain = this;\n        chain.emit({\n            type: 'modify',\n            detail: eventDetail,\n            table: table\n        });\n        var modifiers = (chain.options.reverse ?\n                chain.chain.reverse() :\n                chain.chain.slice());\n        var modified = table.modified;\n        for (var i = 0, iEnd = modifiers.length, modifier = void 0; i < iEnd; ++i) {\n            modifier = modifiers[i];\n            modified = modifier.modifyTable(modified, eventDetail).modified;\n        }\n        table.modified = modified;\n        chain.emit({\n            type: 'afterModify',\n            detail: eventDetail,\n            table: table\n        });\n        return table;\n    };\n    /**\n     * Removes a configured modifier from all positions in the modifier chain.\n     *\n     * @param {DataModifier} modifier\n     * Configured modifier to remove.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     */\n    ChainModifier.prototype.remove = function (modifier, eventDetail) {\n        var modifiers = this.chain;\n        this.emit({\n            type: 'removeModifier',\n            detail: eventDetail,\n            modifier: modifier\n        });\n        modifiers.splice(modifiers.indexOf(modifier), 1);\n        this.emit({\n            type: 'afterRemoveModifier',\n            detail: eventDetail,\n            modifier: modifier\n        });\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default option for the ordered modifier chain.\n     */\n    ChainModifier.defaultOptions = {\n        type: 'Chain'\n    };\n    return ChainModifier;\n}(Modifiers_DataModifier));\nModifiers_DataModifier.registerType('Chain', ChainModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Modifiers_ChainModifier = ((/* unused pure expression or super */ null && (ChainModifier)));\n\n;// ./code/es5/es-modules/Data/Modifiers/InvertModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Sophie Bremer\n *\n * */\n\nvar InvertModifier_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar InvertModifier_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Inverts columns and rows in a table.\n *\n * @private\n */\nvar InvertModifier = /** @class */ (function (_super) {\n    InvertModifier_extends(InvertModifier, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the invert modifier.\n     *\n     * @param {Partial<InvertModifier.Options>} [options]\n     * Options to configure the invert modifier.\n     */\n    function InvertModifier(options) {\n        var _this = _super.call(this) || this;\n        _this.options = InvertModifier_merge(InvertModifier.defaultOptions, options);\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    InvertModifier.prototype.modifyCell = function (table, columnName, rowIndex, cellValue, eventDetail) {\n        var modified = table.modified,\n            modifiedRowIndex = modified.getRowIndexBy('columnNames',\n            columnName);\n        if (typeof modifiedRowIndex === 'undefined') {\n            modified.setColumns(this.modifyTable(table.clone()).getColumns(), void 0, eventDetail);\n        }\n        else {\n            modified.setCell(\"\".concat(rowIndex), modifiedRowIndex, cellValue, eventDetail);\n        }\n        return table;\n    };\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    InvertModifier.prototype.modifyColumns = function (table, columns, rowIndex, eventDetail) {\n        var modified = table.modified,\n            modifiedColumnNames = (modified.getColumn('columnNames') || []);\n        var columnNames = table.getColumnNames(),\n            reset = (table.getRowCount() !== modifiedColumnNames.length);\n        if (!reset) {\n            for (var i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                if (columnNames[i] !== modifiedColumnNames[i]) {\n                    reset = true;\n                    break;\n                }\n            }\n        }\n        if (reset) {\n            return this.modifyTable(table, eventDetail);\n        }\n        columnNames = Object.keys(columns);\n        for (var i = 0, iEnd = columnNames.length, column = void 0, columnName = void 0, modifiedRowIndex = void 0; i < iEnd; ++i) {\n            columnName = columnNames[i];\n            column = columns[columnName];\n            modifiedRowIndex = (modified.getRowIndexBy('columnNames', columnName) ||\n                modified.getRowCount());\n            for (var j = 0, j2 = rowIndex, jEnd = column.length; j < jEnd; ++j, ++j2) {\n                modified.setCell(\"\".concat(j2), modifiedRowIndex, column[j], eventDetail);\n            }\n        }\n        return table;\n    };\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    InvertModifier.prototype.modifyRows = function (table, rows, rowIndex, eventDetail) {\n        var columnNames = table.getColumnNames(),\n            modified = table.modified,\n            modifiedColumnNames = (modified.getColumn('columnNames') || []);\n        var reset = (table.getRowCount() !== modifiedColumnNames.length);\n        if (!reset) {\n            for (var i = 0, iEnd = columnNames.length; i < iEnd; ++i) {\n                if (columnNames[i] !== modifiedColumnNames[i]) {\n                    reset = true;\n                    break;\n                }\n            }\n        }\n        if (reset) {\n            return this.modifyTable(table, eventDetail);\n        }\n        for (var i = 0, i2 = rowIndex, iEnd = rows.length, row = void 0; i < iEnd; ++i, ++i2) {\n            row = rows[i];\n            if (row instanceof Array) {\n                modified.setColumn(\"\".concat(i2), row);\n            }\n            else {\n                for (var j = 0, jEnd = columnNames.length; j < jEnd; ++j) {\n                    modified.setCell(\"\".concat(i2), j, row[columnNames[j]], eventDetail);\n                }\n            }\n        }\n        return table;\n    };\n    /**\n     * Inverts rows and columns in the table.\n     *\n     * @param {DataTable} table\n     * Table to invert.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table with inverted `modified` property as a reference.\n     */\n    InvertModifier.prototype.modifyTable = function (table, eventDetail) {\n        var modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table: table });\n        var modified = table.modified;\n        if (table.hasColumns(['columnNames'])) { // Inverted table\n            var columnNamesColumn = ((table.deleteColumns(['columnNames']) || {})\n                    .columnNames || []),\n                columns = {},\n                columnNames = [];\n            for (var i = 0, iEnd = columnNamesColumn.length; i < iEnd; ++i) {\n                columnNames.push('' + columnNamesColumn[i]);\n            }\n            for (var i = 0, iEnd = table.getRowCount(), row = void 0; i < iEnd; ++i) {\n                row = table.getRow(i);\n                if (row) {\n                    columns[columnNames[i]] = row;\n                }\n            }\n            modified.deleteColumns();\n            modified.setColumns(columns);\n        }\n        else { // Regular table\n            var columns = {};\n            for (var i = 0, iEnd = table.getRowCount(), row = void 0; i < iEnd; ++i) {\n                row = table.getRow(i);\n                if (row) {\n                    columns[\"\".concat(i)] = row;\n                }\n            }\n            columns.columnNames = table.getColumnNames();\n            modified.deleteColumns();\n            modified.setColumns(columns);\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table: table });\n        return table;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options for the invert modifier.\n     */\n    InvertModifier.defaultOptions = {\n        type: 'Invert'\n    };\n    return InvertModifier;\n}(Modifiers_DataModifier));\nModifiers_DataModifier.registerType('Invert', InvertModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Modifiers_InvertModifier = ((/* unused pure expression or super */ null && (InvertModifier)));\n\n;// ./code/es5/es-modules/Data/Modifiers/MathModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\n\nvar MathModifier_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar MathModifier_assign = (undefined && undefined.__assign) || function () {\n    MathModifier_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return MathModifier_assign.apply(this, arguments);\n};\n\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Replaces formula strings in a table with calculated values.\n *\n * @class\n * @name Highcharts.DataModifier.types.MathModifier\n * @augments Highcharts.DataModifier\n */\nvar MathModifier = /** @class */ (function (_super) {\n    MathModifier_extends(MathModifier, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function MathModifier(options) {\n        var _this = _super.call(this) || this;\n        _this.options = MathModifier_assign(MathModifier_assign({}, MathModifier.defaultOptions), options);\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    MathModifier.prototype.modifyTable = function (table, eventDetail) {\n        var modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table: table });\n        var alternativeSeparators = modifier.options.alternativeSeparators,\n            formulaColumns = (modifier.options.formulaColumns ||\n                table.getColumnNames()),\n            modified = table.modified;\n        for (var i = 0, iEnd = formulaColumns.length, columnName = void 0; i < iEnd; ++i) {\n            columnName = formulaColumns[i];\n            if (formulaColumns.indexOf(columnName) >= 0) {\n                modified.setColumn(columnName, modifier.processColumn(table, columnName));\n            }\n        }\n        var columnFormulas = (modifier.options.columnFormulas || []);\n        for (var i = 0, iEnd = columnFormulas.length, columnFormula = void 0, formula = void 0; i < iEnd; ++i) {\n            columnFormula = columnFormulas[i];\n            formula = Formula_FormulaParser.parseFormula(columnFormula.formula, alternativeSeparators);\n            modified.setColumn(columnFormula.column, modifier.processColumnFormula(formula, table, columnFormula.rowStart, columnFormula.rowEnd));\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table: table });\n        return table;\n    };\n    /**\n     * Process a column by replacing formula strings with calculated values.\n     *\n     * @private\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to extract column from and use as reference.\n     *\n     * @param {string} columnName\n     * Name of column to process.\n     *\n     * @param {number} rowIndex\n     * Row index to start the replacing process from.\n     *\n     * @return {Highcharts.DataTableColumn}\n     * Returns the processed table column.\n     */\n    MathModifier.prototype.processColumn = function (table, columnName, rowIndex) {\n        if (rowIndex === void 0) { rowIndex = 0; }\n        var alternativeSeparators = this.options.alternativeSeparators,\n            column = (table.getColumn(columnName,\n            true) || [])\n                .slice(rowIndex > 0 ? rowIndex : 0);\n        for (var i = 0, iEnd = column.length, cacheFormula = [], cacheString = '', cell = void 0; i < iEnd; ++i) {\n            cell = column[i];\n            if (typeof cell === 'string' &&\n                cell[0] === '=') {\n                try {\n                    // Use cache while formula string is repetitive\n                    cacheFormula = (cacheString === cell ?\n                        cacheFormula :\n                        Formula_FormulaParser.parseFormula(cell.substring(1), alternativeSeparators));\n                    // Process parsed formula string\n                    column[i] =\n                        Formula_FormulaProcessor.processFormula(cacheFormula, table);\n                }\n                catch (_a) {\n                    column[i] = NaN;\n                }\n            }\n        }\n        return column;\n    };\n    /**\n     * Process a column by replacing cell values with calculated values from a\n     * given formula.\n     *\n     * @private\n     *\n     * @param {Highcharts.Formula} formula\n     * Formula to use for processing.\n     *\n     * @param {Highcharts.DataTable} table\n     * Table to extract column from and use as reference.\n     *\n     * @param {number} rowStart\n     * Row index to start the replacing process from.\n     *\n     * @param {number} rowEnd\n     * Row index to end the replacing process.\n     *\n     * @return {Highcharts.DataTableColumn}\n     * Returns the processed table column.\n     */\n    MathModifier.prototype.processColumnFormula = function (formula, table, rowStart, rowEnd) {\n        if (rowStart === void 0) { rowStart = 0; }\n        if (rowEnd === void 0) { rowEnd = table.getRowCount(); }\n        rowStart = rowStart >= 0 ? rowStart : 0;\n        rowEnd = rowEnd >= 0 ? rowEnd : table.getRowCount() + rowEnd;\n        var column = [],\n            modified = table.modified;\n        for (var i = 0, iEnd = (rowEnd - rowStart); i < iEnd; ++i) {\n            try {\n                column[i] = Formula_FormulaProcessor.processFormula(formula, modified);\n            }\n            catch (_a) {\n                column[i] = NaN;\n            }\n            finally {\n                formula = Formula_FormulaProcessor.translateReferences(formula, 0, 1);\n            }\n        }\n        return column;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options of MathModifier.\n     * @private\n     */\n    MathModifier.defaultOptions = {\n        type: 'Math',\n        alternativeSeparators: false\n    };\n    return MathModifier;\n}(Modifiers_DataModifier));\nModifiers_DataModifier.registerType('Math', MathModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Modifiers_MathModifier = ((/* unused pure expression or super */ null && (MathModifier)));\n\n;// ./code/es5/es-modules/Data/Modifiers/RangeModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Dawid Dragula\n *\n * */\n\nvar RangeModifier_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar RangeModifier_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Filters out table rows with a specific value range.\n *\n */\nvar RangeModifier = /** @class */ (function (_super) {\n    RangeModifier_extends(RangeModifier, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the range modifier.\n     *\n     * @param {Partial<RangeModifier.Options>} [options]\n     * Options to configure the range modifier.\n     */\n    function RangeModifier(options) {\n        var _this = _super.call(this) || this;\n        _this.options = RangeModifier_merge(RangeModifier.defaultOptions, options);\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Replaces table rows with filtered rows.\n     *\n     * @param {DataTable} table\n     * Table to modify.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table with `modified` property as a reference.\n     */\n    RangeModifier.prototype.modifyTable = function (table, eventDetail) {\n        var modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table: table });\n        var indexes = [];\n        var _a = modifier.options,\n            additive = _a.additive,\n            ranges = _a.ranges,\n            strict = _a.strict;\n        if (ranges.length) {\n            var modified = table.modified;\n            var columns = table.getColumns(),\n                rows = [];\n            for (var i = 0, iEnd = ranges.length, range = void 0, rangeColumn = void 0; i < iEnd; ++i) {\n                range = ranges[i];\n                if (strict &&\n                    typeof range.minValue !== typeof range.maxValue) {\n                    continue;\n                }\n                if (i > 0 && !additive) {\n                    modified.deleteRows();\n                    modified.setRows(rows);\n                    modified.setOriginalRowIndexes(indexes, true);\n                    columns = modified.getColumns();\n                    rows = [];\n                    indexes = [];\n                }\n                rangeColumn = (columns[range.column] || []);\n                for (var j = 0, jEnd = rangeColumn.length, cell = void 0, row = void 0, originalRowIndex = void 0; j < jEnd; ++j) {\n                    cell = rangeColumn[j];\n                    switch (typeof cell) {\n                        default:\n                            continue;\n                        case 'boolean':\n                        case 'number':\n                        case 'string':\n                            break;\n                    }\n                    if (strict &&\n                        typeof cell !== typeof range.minValue) {\n                        continue;\n                    }\n                    if (cell >= range.minValue &&\n                        cell <= range.maxValue) {\n                        if (additive) {\n                            row = table.getRow(j);\n                            originalRowIndex = table.getOriginalRowIndex(j);\n                        }\n                        else {\n                            row = modified.getRow(j);\n                            originalRowIndex = modified.getOriginalRowIndex(j);\n                        }\n                        if (row) {\n                            rows.push(row);\n                            indexes.push(originalRowIndex);\n                        }\n                    }\n                }\n            }\n            modified.deleteRows();\n            modified.setRows(rows);\n            modified.setOriginalRowIndexes(indexes);\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table: table });\n        return table;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options for the range modifier.\n     */\n    RangeModifier.defaultOptions = {\n        type: 'Range',\n        ranges: []\n    };\n    return RangeModifier;\n}(Modifiers_DataModifier));\nModifiers_DataModifier.registerType('Range', RangeModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Modifiers_RangeModifier = ((/* unused pure expression or super */ null && (RangeModifier)));\n\n;// ./code/es5/es-modules/Data/Modifiers/SortModifier.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Dawid Dragula\n *\n * */\n\nvar SortModifier_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar SortModifier_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Sort table rows according to values of a column.\n *\n */\nvar SortModifier = /** @class */ (function (_super) {\n    SortModifier_extends(SortModifier, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    /**\n     * Constructs an instance of the range modifier.\n     *\n     * @param {Partial<RangeDataModifier.Options>} [options]\n     * Options to configure the range modifier.\n     */\n    function SortModifier(options) {\n        var _this = _super.call(this) || this;\n        _this.options = SortModifier_merge(SortModifier.defaultOptions, options);\n        return _this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    SortModifier.ascending = function (a, b) {\n        return ((a || 0) < (b || 0) ? -1 :\n            (a || 0) > (b || 0) ? 1 :\n                0);\n    };\n    SortModifier.descending = function (a, b) {\n        return ((b || 0) < (a || 0) ? -1 :\n            (b || 0) > (a || 0) ? 1 :\n                0);\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Returns index and row for sort reference.\n     *\n     * @private\n     *\n     * @param {Highcharts.DataTable} table\n     * Table with rows to reference.\n     *\n     * @return {Array<SortModifier.RowReference>}\n     * Array of row references.\n     */\n    SortModifier.prototype.getRowReferences = function (table) {\n        var rows = table.getRows(),\n            rowReferences = [];\n        for (var i = 0, iEnd = rows.length; i < iEnd; ++i) {\n            rowReferences.push({\n                index: i,\n                row: rows[i]\n            });\n        }\n        return rowReferences;\n    };\n    /**\n     * Applies partial modifications of a cell change to the property `modified`\n     * of the given modified table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {string} columnName\n     * Column name of changed cell.\n     *\n     * @param {number|undefined} rowIndex\n     * Row index of changed cell.\n     *\n     * @param {Highcharts.DataTableCellType} cellValue\n     * Changed cell value.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    SortModifier.prototype.modifyCell = function (table, columnName, rowIndex, cellValue, eventDetail) {\n        var modifier = this,\n            _a = modifier.options,\n            orderByColumn = _a.orderByColumn,\n            orderInColumn = _a.orderInColumn;\n        if (columnName === orderByColumn) {\n            if (orderInColumn) {\n                table.modified.setCell(columnName, rowIndex, cellValue);\n                table.modified.setColumn(orderInColumn, modifier\n                    .modifyTable(new Data_DataTable({\n                    columns: table\n                        .getColumns([orderByColumn, orderInColumn])\n                }))\n                    .modified\n                    .getColumn(orderInColumn));\n            }\n            else {\n                modifier.modifyTable(table, eventDetail);\n            }\n        }\n        return table;\n    };\n    /**\n     * Applies partial modifications of column changes to the property\n     * `modified` of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Changed columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex=0]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    SortModifier.prototype.modifyColumns = function (table, columns, rowIndex, eventDetail) {\n        var modifier = this,\n            _a = modifier.options,\n            orderByColumn = _a.orderByColumn,\n            orderInColumn = _a.orderInColumn,\n            columnNames = Object.keys(columns);\n        if (columnNames.indexOf(orderByColumn) > -1) {\n            if (orderInColumn &&\n                columns[columnNames[0]].length) {\n                table.modified.setColumns(columns, rowIndex);\n                table.modified.setColumn(orderInColumn, modifier\n                    .modifyTable(new Data_DataTable({\n                    columns: table\n                        .getColumns([orderByColumn, orderInColumn])\n                }))\n                    .modified\n                    .getColumn(orderInColumn));\n            }\n            else {\n                modifier.modifyTable(table, eventDetail);\n            }\n        }\n        return table;\n    };\n    /**\n     * Applies partial modifications of row changes to the property `modified`\n     * of the given table.\n     *\n     * @param {Highcharts.DataTable} table\n     * Modified table.\n     *\n     * @param {Array<(Highcharts.DataTableRow|Highcharts.DataTableRowObject)>} rows\n     * Changed rows.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first changed row.\n     *\n     * @param {Highcharts.DataTableEventDetail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {Highcharts.DataTable}\n     * Table with `modified` property as a reference.\n     */\n    SortModifier.prototype.modifyRows = function (table, rows, rowIndex, eventDetail) {\n        var modifier = this,\n            _a = modifier.options,\n            orderByColumn = _a.orderByColumn,\n            orderInColumn = _a.orderInColumn;\n        if (orderInColumn &&\n            rows.length) {\n            table.modified.setRows(rows, rowIndex);\n            table.modified.setColumn(orderInColumn, modifier\n                .modifyTable(new Data_DataTable({\n                columns: table\n                    .getColumns([orderByColumn, orderInColumn])\n            }))\n                .modified\n                .getColumn(orderInColumn));\n        }\n        else {\n            modifier.modifyTable(table, eventDetail);\n        }\n        return table;\n    };\n    /**\n     * Sorts rows in the table.\n     *\n     * @param {DataTable} table\n     * Table to sort in.\n     *\n     * @param {DataEvent.Detail} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @return {DataTable}\n     * Table with `modified` property as a reference.\n     */\n    SortModifier.prototype.modifyTable = function (table, eventDetail) {\n        var _a;\n        var modifier = this;\n        modifier.emit({ type: 'modify', detail: eventDetail, table: table });\n        var columnNames = table.getColumnNames(),\n            rowCount = table.getRowCount(),\n            rowReferences = this.getRowReferences(table),\n            _b = modifier.options,\n            direction = _b.direction,\n            orderByColumn = _b.orderByColumn,\n            orderInColumn = _b.orderInColumn,\n            compare = (direction === 'asc' ?\n                SortModifier.ascending :\n                SortModifier.descending),\n            orderByColumnIndex = columnNames.indexOf(orderByColumn),\n            modified = table.modified;\n        if (orderByColumnIndex !== -1) {\n            rowReferences.sort(function (a, b) { return compare(a.row[orderByColumnIndex], b.row[orderByColumnIndex]); });\n        }\n        if (orderInColumn) {\n            var column = [];\n            for (var i = 0; i < rowCount; ++i) {\n                column[rowReferences[i].index] = i;\n            }\n            modified.setColumns((_a = {}, _a[orderInColumn] = column, _a));\n        }\n        else {\n            var originalIndexes = [];\n            var rows = [];\n            var rowReference = void 0;\n            for (var i = 0; i < rowCount; ++i) {\n                rowReference = rowReferences[i];\n                originalIndexes.push(modified.getOriginalRowIndex(rowReference.index));\n                rows.push(rowReference.row);\n            }\n            modified.setRows(rows, 0);\n            modified.setOriginalRowIndexes(originalIndexes);\n        }\n        modifier.emit({ type: 'afterModify', detail: eventDetail, table: table });\n        return table;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Default options to group table rows.\n     */\n    SortModifier.defaultOptions = {\n        type: 'Sort',\n        direction: 'desc',\n        orderByColumn: 'y'\n    };\n    return SortModifier;\n}(Modifiers_DataModifier));\nModifiers_DataModifier.registerType('Sort', SortModifier);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Modifiers_SortModifier = ((/* unused pure expression or super */ null && (SortModifier)));\n\n;// ./code/es5/es-modules/masters/modules/data-tools.js\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.DataConnector = G.DataConnector || Connectors_DataConnector;\nG.DataConverter = G.DataConverter || Converters_DataConverter;\nG.DataCursor = G.DataCursor || Data_DataCursor;\nG.DataModifier = G.DataModifier || Modifiers_DataModifier;\nG.DataPool = G.DataPool || Data_DataPool;\nG.DataTable = G.DataTable || Data_DataTable;\nG.Formula = G.Formula || Formula_Formula;\n/* harmony default export */ var data_tools_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "DataModifier", "Column<PERSON><PERSON><PERSON>", "extendStatics", "DataConnector", "DataConverter", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "data_tools_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "fireEvent", "merge", "benchmark", "dataTable", "options", "results", "modifier", "execute", "modifyTable", "emit", "type", "iterations", "on", "length", "times", "startTime", "endTime", "window", "performance", "now", "push", "e", "modify", "table", "eventDetail", "Promise", "resolve", "reject", "modified", "clone", "detail", "modifyCell", "columnName", "rowIndex", "cellValue", "modifyColumns", "columns", "modifyRows", "rows", "callback", "types", "registerType", "DataModifierClass", "Modifiers_DataModifier", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "ar", "i", "l", "Array", "slice", "concat", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "removed", "apply", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "Data_ColumnUtils", "DataTableCore_fireEvent", "objectEach", "<PERSON><PERSON><PERSON>", "DataTableCore", "_this", "autoId", "id", "rowCount", "versionTag", "Math", "max", "applyRowCount", "deleteRows", "length_1", "getColumn", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "_a", "setColumn", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "addColumns", "__extends", "b", "setPrototypeOf", "__proto__", "p", "__", "create", "DataTable_addEvent", "defined", "extend", "DataTable_fireEvent", "isNumber", "DataTable_uniqueKey", "DataTable", "_super", "isNull", "NULL", "iEnd", "skipColumns", "tableOptions", "tableClone", "originalRowIndexes", "localRowIndexes", "deleteColumns", "deletedColumns", "modifiedColumns", "deleteRowIndexReferences", "deletedRows", "modifiedRows", "deleted<PERSON>ells", "j", "jEnd", "includes", "getCell", "getCellAsBoolean", "getCellAsNumber", "useNaN", "isNaN", "parseFloat", "getCellAsString", "getColumnAsNumbers", "columnAsNumber", "columnLength", "getColumnNames", "asBasicColumns", "tableColumns", "getLocalRowIndex", "originalRowIndex", "getModifier", "getOriginalRowIndex", "getRows", "getRowCount", "getRowIndexBy", "rowIndexOffset", "indexOf", "getRowObject", "getRowObjects", "i2", "min", "_i", "columnNames_1", "columnNames_2", "getVersionTag", "hasColumns", "hasRowWith", "Number", "isFinite", "renameColumn", "newColumnName", "setCell", "typeAsOriginal", "tableModifier", "tableColumn", "ArrayConstructor", "i_1", "iEnd_1", "setModifier", "promise", "then", "error", "setOriginalRowIndexes", "omitLocalRowIndexes", "modifiedIndexes", "originalIndex", "setRows", "version", "DataConnector_addEvent", "DataConnector_fireEvent", "DataConnector_merge", "pick", "metadata", "_polling", "configurable", "describeColumn", "name", "columnMeta", "connector", "describeColumns", "pop", "getColumnOrder", "usePresentationState", "names", "sort", "index", "getSortedColumns", "usePresentationOrder", "load", "save", "Error", "setColumnOrder", "setModifierOptions", "modifierOptions", "ModifierClass", "startPolling", "refreshTime", "clearTimeout", "setTimeout", "stopPolling", "whatIs", "DataConnectorClass", "Connectors_DataConnector", "DataConverter_addEvent", "DataConverter_fireEvent", "DataConverter_isNumber", "DataConverter_merge", "dateFormats", "regex", "parser", "match", "Date", "UTC", "NaN", "alternative", "year", "getFullYear", "mergedOptions", "defaultOptions", "regExpPoint", "decimalPoint", "decimalRegExp", "RegExp", "asBoolean", "value", "asNumber", "asDate", "timestamp", "parseDate", "asString", "asGuessedType", "typeMap", "converter", "guessType", "decimalRegex", "replace", "test", "getDate", "deduceDateFormat", "data", "limit", "thing", "elem", "stable", "format", "guessedFormat", "madeDeduction", "trim", "split", "parseInt", "join", "dateFormat", "export", "headers", "getTable", "trimedValue", "innerTrimedValue", "floatValue", "parse", "dateFormatProp", "getTime", "getTimezoneOffset", "str", "inside", "alternativeFormat", "startColumn", "endColumn", "MAX_VALUE", "startRow", "endRow", "firstRowAsNames", "switchRowsAndColumns", "DataConverterClass", "getTableFromColumns", "Converters_DataConverter", "DataCursor", "stateMap", "emittingRegister", "listenerMap", "addListener", "tableId", "state", "listener", "listeners", "buildEmittingTag", "cursor", "firstRow", "lastRow", "emitCursor", "event", "lasting", "cursors", "getIndex", "emittingTag", "remitCursor", "removeListener", "to<PERSON><PERSON><PERSON>", "defaultRange", "_b", "_c", "_d", "range", "needle", "columnNeedle", "JSON", "stringify", "isEqual", "cursorA", "cursorB", "isInRange", "needleColumns", "rangeColumns", "every", "toPositions", "positions", "rowEnd", "columnEnd", "Data_DataCursor", "Data_DataPoolDefaults", "connectors", "DataPool", "waiting", "getConnector", "connectorId", "waitingList", "connectorOptions", "getConnectorOptions", "loadConnector", "getConnectorIds", "connectorIds", "getConnectorTable", "isNewConnector", "ConnectorClass", "setConnectorOptions", "instances", "booleanRegExp", "decimal1RegExp", "decimal2RegExp", "functionRegExp", "operatorRegExp", "rangeA1RegExp", "rangeR1C1RegExp", "referenceA1RegExp", "referenceR1C1RegExp", "extractParantheses", "text", "parantheseLevel", "char", "parantheseStart", "substring", "extractString", "escaping", "parseArgument", "alternativeSeparators", "beginColumnRelative", "beginRowRelative", "endColumnRelative", "endRowRelative", "beginColumn", "beginRow", "parseReferenceColumn", "formula", "parseForm<PERSON>", "next", "columnRelative", "rowRelative", "reference", "string", "parantheses", "args", "parseArguments", "argumentsSeparator", "term", "parantes<PERSON>", "position", "code", "factor", "charCodeAt", "pow", "Formula_FormulaParser", "operators", "FormulaTypes", "isFormula", "item", "isFunction", "isOperator", "isRange", "isReference", "isValue", "FormulaProcessor_isFormula", "FormulaProcessor_isFunction", "FormulaProcessor_isOperator", "FormulaProcessor_isRange", "FormulaProcessor_isReference", "FormulaProcessor_isValue", "asLogicalStringRegExp", "MAX_FALSE", "MAX_STRING", "MAX_TRUE", "operatorPriority", "processorFunctions", "processorFunctionNameRegExp", "asLogicalNumber", "asLogicalString", "toLowerCase", "basicOperation", "operator", "x", "y", "round", "getArgumentValue", "arg", "getRangeValues", "processFunction", "processFormula", "values", "cell", "cells", "getReferenceValue", "operator2", "formulaFunction", "processor", "Formula_FormulaProcessor", "getArgumentsValues", "registerProcessorFunction", "processorFunction", "translateReferences", "columnDel<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ABS_getArgumentValue", "abs", "value2", "AND_getArgumentValue", "AND", "AVERAGE_getArgumentsValues", "count", "AVERAGEA_getArgumentValue", "COUNT", "COUNTA", "IF_getArgumentValue", "ISNA_getArgumentValue", "MAX_getArgumentsValues", "MAX", "NEGATIVE_INFINITY", "median", "half", "floor", "MIN_getArgumentsValues", "MIN", "POSITIVE_INFINITY", "MOD_getArgumentValue", "getModeMap", "modeMap", "SNGL", "mode<PERSON>ey", "modeCount", "keyValue", "value1", "modeKeys", "NOT_getArgumentValue", "OR_getArgumentValue", "OR", "PRODUCT_getArgumentsValues", "PRODUCT", "calculated", "SUM", "XOR_getArgumentValue", "lastValue", "__assign", "assign", "t", "s", "Formula", "CSVConverter_extends", "TypeError", "String", "CSVConverter_assign", "CSVConverter_merge", "CSVConverter", "dataTypes", "useLocalDecimalPoint", "lineDelimiter", "exportNames", "itemDelimiter", "toLocaleString", "csvRows", "columnsCount", "rowArray", "columnIndex", "columnDataType", "dataType", "cellVal", "lines", "parserOptions", "beforeParse", "rowIt", "csv", "guessedItemDelimiter", "guess<PERSON><PERSON><PERSON><PERSON>", "offset", "parseCSVRow", "columnStr", "rowNumber", "guessedDecimalPoint", "c", "token", "actualColumn", "read", "pushType", "initialValue", "guessed", "points", "commas", "potDelimiters", "linesCount", "inStr", "cn", "cl", "CSVConnector_extends", "CSVConnector_merge", "CSVConnector", "enablePolling", "dataRefreshRate", "csvURL", "dataModifier", "fetch", "response", "JSONConverter_extends", "JSONConverter_assign", "JSONConverter_merge", "JSONConverter_objectEach", "JSONConverter", "orientation", "shift", "toString", "this_1", "_loop_1", "newRow_1", "arrayWithPath", "acc", "JSONConnector_extends", "JSONConnector_merge", "JSONConnector", "dataUrl", "json", "console", "warn", "GoogleSheetsConverter_extends", "GoogleSheetsConverter_assign", "GoogleSheetsConverter_merge", "GoogleSheetsConverter_uniqueKey", "GoogleSheetsConverter", "header", "parseOptions", "GoogleSheetsConnector_extends", "GoogleSheetsConnector_merge", "GoogleSheetsConnector_pick", "GoogleSheetsConnector", "googleAPIKey", "googleSpreadsheetKey", "url", "buildFetchURL", "URL", "canParse", "message", "status", "alphabet", "buildQueryRange", "googleSpreadsheetRange", "<PERSON><PERSON><PERSON><PERSON>", "sheetKey", "onlyColumnNames", "pathname", "searchParams", "href", "HTMLTableConverter_extends", "HTMLTableConverter_assign", "HTMLTableConverter_merge", "HTMLTableConverter", "tableElement", "tableElementID", "useMultiLevelHeaders", "htmlRows", "tableHead", "subcategories", "name_1", "subhead", "getTableHeaderHTML", "getCellHTMLFromValue", "caption", "tableCaption", "tag", "classes", "attrs", "val", "className", "topheaders", "subheaders", "cur", "rowspan", "useRowspanHeaders", "html", "len", "cur<PERSON><PERSON><PERSON>", "isRowEqual", "row1", "row2", "tableHTML", "HTMLElement", "getElementsByTagName", "rowsCount", "children", "itemsLength", "tagName", "innerHTML", "columnsInRow", "columnsInRowLength", "relativeColumnIndex", "HTMLTableConnector_extends", "win", "HTMLTableConnector_merge", "HTMLTableConnector", "tableID", "document", "getElementById", "ChainModifier_extends", "__generator", "thisArg", "body", "f", "g", "_", "label", "sent", "trys", "ops", "verb", "Symbol", "iterator", "v", "step", "op", "done", "ChainModifier_merge", "ChainModifier", "chain", "optionsChain", "add", "clear", "_arguments", "P", "generator", "modifiers", "error_1", "reverse", "fulfilled", "rejected", "adopt", "remove", "InvertModifier_extends", "InvertModifier_merge", "InvertModifier", "modifiedRowIndex", "modifiedColumnNames", "reset", "j2", "columnNamesColumn", "MathModifier_extends", "MathModifier_assign", "MathModifier", "formulaColumns", "processColumn", "columnFormulas", "columnFormula", "processColumnFormula", "rowStart", "cacheFormula", "cacheString", "RangeModifier_extends", "RangeModifier_merge", "RangeModifier", "indexes", "additive", "ranges", "strict", "rangeColumn", "minValue", "maxValue", "SortModifier_extends", "SortModifier_merge", "SortModifier", "ascending", "descending", "getRowReferences", "rowReferences", "orderByColumn", "orderInColumn", "direction", "compare", "orderByColumnIndex", "originalIndexes", "rowReference", "G"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,eAC1B,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,CAAC,wBAAwB,CAAC,CAAEJ,GAC9D,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQG,QAAQ,eAE3DJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,IAAI,CAAE,SAASO,CAAgC,EAClD,OAAgB,AAAC,WACP,aACA,IAuTCC,EA+EAC,EAwbHC,EA83CGC,EA6mBAC,EAytGHF,EA4fAA,EA2JAA,EAiOAA,EAoKAA,EAgLAA,EAwPAA,EAmbAA,EAqJAA,EAyaAA,EA2QAA,EA8MAA,EAwKAA,EAnrRJD,EArYUI,EAAuB,CAE/B,IACC,SAASV,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,SAAShB,CAAM,EACtC,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,WAAa,OAAOlB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASpB,CAAO,CAAEsB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAgB,CAC/D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAiBjHE,EAAW,AAACD,IAA+EC,QAAQ,CAAEC,EAAY,AAACF,IAA+EE,SAAS,CAAEC,EAAQ,AAACH,IAA+EG,KAAK,CAUzSlC,EAA8B,WAC9B,SAASA,IACT,CAqMA,OAjLAA,EAAayB,SAAS,CAACU,SAAS,CAAG,SAAUC,CAAS,CAAEC,CAAO,EAC3D,IAAIC,EAAU,EAAE,CACZC,EAAW,IAAI,CACfC,EAAU,WACND,EAASE,WAAW,CAACL,GACzBG,EAASG,IAAI,CAAC,CACVC,KAAM,yBACV,EACJ,EAIIC,EAAaV,EAHI,CACbU,WAAY,CAChB,EAEAP,GAASO,UAAU,CACvBL,EAASM,EAAE,CAAC,0BAA2B,WACnC,GAAIP,EAAQQ,MAAM,GAAKF,EAAY,CAC/BL,EAASG,IAAI,CAAC,CACVC,KAAM,iBACNL,QAASA,CACb,GACA,MACJ,CAEAE,GACJ,GACA,IAAIO,EAAQ,CACJC,UAAW,EACXC,QAAS,CACb,EAWJ,OATAV,EAASM,EAAE,CAAC,SAAU,WAClBE,EAAMC,SAAS,CAAGE,OAAOC,WAAW,CAACC,GAAG,EAC5C,GACAb,EAASM,EAAE,CAAC,cAAe,WACvBE,EAAME,OAAO,CAAGC,OAAOC,WAAW,CAACC,GAAG,GACtCd,EAAQe,IAAI,CAACN,EAAME,OAAO,CAAGF,EAAMC,SAAS,CAChD,GAEAR,IACOF,CACX,EAOAtC,EAAayB,SAAS,CAACiB,IAAI,CAAG,SAAUY,CAAC,EACrCrB,EAAU,IAAI,CAAEqB,EAAEX,IAAI,CAAEW,EAC5B,EAaAtD,EAAayB,SAAS,CAAC8B,MAAM,CAAG,SAAUC,CAAK,CAAEC,CAAW,EACxD,IAAIlB,EAAW,IAAI,CACnB,OAAO,IAAImB,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACpCJ,EAAMK,QAAQ,GAAKL,GACnBA,CAAAA,EAAMK,QAAQ,CAAGL,EAAMM,KAAK,CAAC,CAAA,EAAOL,EAAW,EAEnD,GAAI,CACAE,EAAQpB,EAASE,WAAW,CAACe,EAAOC,GACxC,CACA,MAAOH,EAAG,CACNf,EAASG,IAAI,CAAC,CACVC,KAAM,QACNoB,OAAQN,EACRD,MAAOA,CACX,GACAI,EAAON,EACX,CACJ,EACJ,EAuBAtD,EAAayB,SAAS,CAACuC,UAAU,CAAG,SAAUR,CAAK,CAEnDS,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,EAGxC,OAAO,IAAI,CAAChB,WAAW,CAACe,EAC5B,EAoBAxD,EAAayB,SAAS,CAAC2C,aAAa,CAAG,SAAUZ,CAAK,CAEtDa,CAAO,CAAEH,CAAQ,CAAET,CAAW,EAG1B,OAAO,IAAI,CAAChB,WAAW,CAACe,EAC5B,EAoBAxD,EAAayB,SAAS,CAAC6C,UAAU,CAAG,SAAUd,CAAK,CAEnDe,CAAI,CAAEL,CAAQ,CAAET,CAAW,EAGvB,OAAO,IAAI,CAAChB,WAAW,CAACe,EAC5B,EAaAxD,EAAayB,SAAS,CAACoB,EAAE,CAAG,SAAUF,CAAI,CAAE6B,CAAQ,EAChD,OAAOxC,EAAS,IAAI,CAAEW,EAAM6B,EAChC,EACOxE,CACX,GAwBIA,EAfOA,EA4CRA,GAAiBA,CAAAA,EAAe,CAAC,CAAA,GA7BnByE,KAAK,CAAG,CAAC,EA4BtBzE,EAAa0E,YAAY,CALzB,SAAsBzD,CAAG,CAAE0D,CAAiB,EACxC,MAAQ,CAAC,CAAC1D,GACN,CAACjB,EAAayE,KAAK,CAACxD,EAAI,EACxB,CAAC,CAAEjB,CAAAA,EAAayE,KAAK,CAACxD,EAAI,CAAG0D,CAAgB,CACrD,EAQyB,IAAIC,EAA0B5E,EAevD6E,EAA0D,SAAUC,CAAE,CAAEC,CAAI,CAAEC,CAAI,EAClF,GAAIA,GAAQC,AAAqB,GAArBA,UAAUnC,MAAM,CAAQ,IAAK,IAA4BoC,EAAxBC,EAAI,EAAGC,EAAIL,EAAKjC,MAAM,CAAMqC,EAAIC,EAAGD,KACxED,GAAQC,KAAKJ,IACRG,GAAIA,CAAAA,EAAKG,MAAM5D,SAAS,CAAC6D,KAAK,CAAC3D,IAAI,CAACoD,EAAM,EAAGI,EAAC,EACnDD,CAAE,CAACC,EAAE,CAAGJ,CAAI,CAACI,EAAE,EAGvB,OAAOL,EAAGS,MAAM,CAACL,GAAMG,MAAM5D,SAAS,CAAC6D,KAAK,CAAC3D,IAAI,CAACoD,GACtD,CA2CI9E,EArCOA,EA2FRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAtDlBuF,SAAS,CAPrB,SAAmBC,CAAM,CAAE3C,CAAM,CAAE4C,CAAU,SACzC,AAAIL,MAAMM,OAAO,CAACF,IACdA,EAAO3C,MAAM,CAAGA,EACT2C,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAG5C,EACxD,EAsDA7C,EAAY2F,MAAM,CAzBlB,SAAgBH,CAAM,CAAEI,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,CAAK,EAEhE,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQ,EAAE,AAAD,EAC7BX,MAAMM,OAAO,CAACF,GAId,OAHKJ,MAAMM,OAAO,CAACK,IACfA,CAAAA,EAAQX,MAAMN,IAAI,CAACiB,EAAK,EAErB,CACHC,QAASR,EAAOG,MAAM,CAACM,KAAK,CAACT,EAAQZ,EAAc,CAACgB,EAAOC,EAAY,CAAEE,EAAO,CAAA,IAChFG,MAAOV,CACX,EAEJ,IAAIW,EAAcjF,OAAOkF,cAAc,CAACZ,GAC/Ba,WAAW,CAChBL,EAAUR,CAAM,CAACM,EAAoB,WAAa,QAAQ,CAACF,EAC3DA,EAAQC,GAERS,EAAS,IAAIH,EADDX,EAAO3C,MAAM,CAAGgD,EAAcE,EAAMlD,MAAM,EAK1D,OAHAyD,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMlD,MAAM,EAC9D,CACHmD,QAASA,EACTE,MAAOI,CACX,CACJ,EAQyB,IAAIG,EAAoBzG,EAmBjDuF,EAAYkB,EAAiBlB,SAAS,CAAEI,EAASc,EAAiBd,MAAM,CAExEe,EAA0B,AAAC5E,IAA+EE,SAAS,CAAE2E,EAAa,AAAC7E,IAA+E6E,UAAU,CAAEC,EAAY,AAAC9E,IAA+E8E,SAAS,CAiBnUC,EAA+B,WAiB/B,SAASA,EAAczE,CAAO,EACV,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAI0E,EAAQ,IAAI,AAOhB,CAAA,IAAI,CAACC,MAAM,CAAG,CAAC3E,EAAQ4E,EAAE,CACzB,IAAI,CAAC5C,OAAO,CAAG,CAAC,EAOhB,IAAI,CAAC4C,EAAE,CAAI5E,EAAQ4E,EAAE,EAAIJ,IACzB,IAAI,CAAChD,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACqD,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGN,IAClB,IAAIK,EAAW,EACfN,EAAWvE,EAAQgC,OAAO,EAAI,CAAC,EAAG,SAAUoB,CAAM,CAAExB,CAAU,EAC1D8C,EAAM1C,OAAO,CAACJ,EAAW,CAAGwB,EAAOH,KAAK,GACxC4B,EAAWE,KAAKC,GAAG,CAACH,EAAUzB,EAAO3C,MAAM,CAC/C,GACA,IAAI,CAACwE,aAAa,CAACJ,EACvB,CAyMA,OA5LAJ,EAAcrF,SAAS,CAAC6F,aAAa,CAAG,SAAUJ,CAAQ,EACtD,IAAIH,EAAQ,IAAI,AAChB,CAAA,IAAI,CAACG,QAAQ,CAAGA,EAChBN,EAAW,IAAI,CAACvC,OAAO,CAAE,SAAUoB,CAAM,CAAExB,CAAU,EAC7CwB,EAAO3C,MAAM,GAAKoE,GAClBH,CAAAA,EAAM1C,OAAO,CAACJ,EAAW,CAAGuB,EAAUC,EAAQyB,EAAQ,CAE9D,EACJ,EAeAJ,EAAcrF,SAAS,CAAC8F,UAAU,CAAG,SAAUrD,CAAQ,CAAEgD,CAAQ,EAC7D,IAAIH,EAAQ,IAAI,CAEhB,GADiB,KAAK,IAAlBG,GAAuBA,CAAAA,EAAW,CAAA,EAClCA,EAAW,GAAKhD,EAAW,IAAI,CAACgD,QAAQ,CAAE,CAC1C,IAAIM,EAAW,EACfZ,EAAW,IAAI,CAACvC,OAAO,CAAE,SAAUoB,CAAM,CAAExB,CAAU,EACjD8C,EAAM1C,OAAO,CAACJ,EAAW,CACrB2B,EAAOH,EAAQvB,EAAUgD,GAAUf,KAAK,CAC5CqB,EAAW/B,EAAO3C,MAAM,AAC5B,GACA,IAAI,CAACoE,QAAQ,CAAGM,CACpB,CACAb,EAAwB,IAAI,CAAE,kBAAmB,CAAEzC,SAAUA,EAAUgD,SAAUA,CAAS,GAC1F,IAAI,CAACC,UAAU,CAAGN,GACtB,EAWAC,EAAcrF,SAAS,CAACgG,SAAS,CAAG,SAAUxD,CAAU,CAExDyD,CAAW,EACP,OAAO,IAAI,CAACrD,OAAO,CAACJ,EAAW,AACnC,EAYA6C,EAAcrF,SAAS,CAACkG,UAAU,CAAG,SAAUC,CAAW,CAE1DF,CAAW,EACP,IAAIX,EAAQ,IAAI,CAChB,MAAO,AAACa,CAAAA,GAAezG,OAAO0G,IAAI,CAAC,IAAI,CAACxD,OAAO,CAAA,EAAGyD,MAAM,CAAC,SAAUzD,CAAO,CAAEJ,CAAU,EAElF,OADAI,CAAO,CAACJ,EAAW,CAAG8C,EAAM1C,OAAO,CAACJ,EAAW,CACxCI,CACX,EAAG,CAAC,EACR,EAaAyC,EAAcrF,SAAS,CAACsG,MAAM,CAAG,SAAU7D,CAAQ,CAAE0D,CAAW,EAC5D,IAAIb,EAAQ,IAAI,CAChB,MAAO,AAACa,CAAAA,GAAezG,OAAO0G,IAAI,CAAC,IAAI,CAACxD,OAAO,CAAA,EAAG2D,GAAG,CAAC,SAAU/G,CAAG,EAAI,IAAIgH,EAAI,OAAO,AAA8B,OAA7BA,CAAAA,EAAKlB,EAAM1C,OAAO,CAACpD,EAAI,AAAD,GAAegH,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC/D,EAAS,AAAE,EACvK,EAmBA4C,EAAcrF,SAAS,CAACyG,SAAS,CAAG,SAAUjE,CAAU,CAAEwB,CAAM,CAAEvB,CAAQ,CAAET,CAAW,EACnF,IAAIwE,CACW,MAAK,IAAhBxC,GAAqBA,CAAAA,EAAS,EAAE,AAAD,EAClB,KAAK,IAAlBvB,GAAuBA,CAAAA,EAAW,CAAA,EACtC,IAAI,CAACiE,UAAU,CAAEF,CAAAA,AAASA,CAATA,EAAK,CAAC,CAAA,CAAK,CAAChE,EAAW,CAAGwB,EAAQwC,CAAC,EAAI/D,EAAUT,EACtE,EAmBAqD,EAAcrF,SAAS,CAAC0G,UAAU,CAAG,SAAU9D,CAAO,CAAEH,CAAQ,CAAET,CAAW,EACzE,IAAIsD,EAAQ,IAAI,CACZG,EAAW,IAAI,CAACA,QAAQ,CAC5BN,EAAWvC,EAAS,SAAUoB,CAAM,CAAExB,CAAU,EAC5C8C,EAAM1C,OAAO,CAACJ,EAAW,CAAGwB,EAAOH,KAAK,GACxC4B,EAAWzB,EAAO3C,MAAM,AAC5B,GACA,IAAI,CAACwE,aAAa,CAACJ,GACbzD,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAY2E,MAAM,AAAD,IAC7EzB,EAAwB,IAAI,CAAE,mBAC9B,IAAI,CAACQ,UAAU,CAAGN,IAE1B,EAoBAC,EAAcrF,SAAS,CAAC4G,MAAM,CAAG,SAAUC,CAAG,CAAEpE,CAAQ,CAAEqE,CAAM,CAAE9E,CAAW,EACxD,KAAK,IAAlBS,GAAuBA,CAAAA,EAAW,IAAI,CAACgD,QAAQ,AAAD,EAClD,IAAI7C,EAAU,IAAI,CAACA,OAAO,CACtBmE,EAAgBD,EAAS,IAAI,CAACrB,QAAQ,CAAG,EAAIhD,EAAW,EAC5D0C,EAAW0B,EAAK,SAAUnE,CAAS,CAAEF,CAAU,EAC3C,IAAIwB,EAASpB,CAAO,CAACJ,EAAW,EACxB,AAACR,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYgF,UAAU,AAAD,IAAO,CAAA,GAAS,AAAIpD,MAAMmD,GAC9G/C,IACI8C,EACA9C,EAASG,EAAOH,EAAQvB,EAAU,EAAG,CAAA,EAAM,CAACC,EAAU,EAAEgC,KAAK,CAG7DV,CAAM,CAACvB,EAAS,CAAGC,EAEvBE,CAAO,CAACJ,EAAW,CAAGwB,EAE9B,GACI+C,EAAgB,IAAI,CAACtB,QAAQ,EAC7B,IAAI,CAACI,aAAa,CAACkB,GAEjB/E,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAY2E,MAAM,AAAD,IAC7EzB,EAAwB,IAAI,CAAE,gBAC9B,IAAI,CAACQ,UAAU,CAAGN,IAE1B,EACOC,CACX,IA0DI4B,GACIxI,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAMD,MAAOzI,AALHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOA,EAAEjH,cAAc,CAACoH,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACvChI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EAEjB,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAKAE,EAAqB,AAAClH,IAA+EC,QAAQ,CAAEkH,EAAU,AAACnH,IAA+EmH,OAAO,CAAEC,EAAS,AAACpH,IAA+EoH,MAAM,CAAEC,EAAsB,AAACrH,IAA+EE,SAAS,CAAEoH,EAAW,AAACtH,IAA+EsH,QAAQ,CAAEC,EAAsB,AAACvH,IAA+E8E,SAAS,CAiBxnB0C,EAA2B,SAAUC,CAAM,EAO3C,SAASD,EAAUlH,CAAO,EACN,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAI0E,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CACxBU,IAAY,IAAI,CAEpB,OADA0E,EAAMlD,QAAQ,CAAGkD,EACVA,CACX,CAklCA,OA9lCA2B,EAAUa,EAAWC,GAqCrBD,EAAUE,MAAM,CAAG,SAAUnB,CAAG,EAC5B,GAAIA,IAAQiB,EAAUG,IAAI,CACtB,MAAO,CAAA,EAEX,GAAIpB,aAAejD,MAAO,CACtB,GAAI,CAACiD,EAAIxF,MAAM,CACX,MAAO,CAAA,EAEX,IAAK,IAAIqC,EAAI,EAAGwE,EAAOrB,EAAIxF,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAC3C,GAAImD,AAAW,OAAXA,CAAG,CAACnD,EAAE,CACN,MAAO,CAAA,CAGnB,KACK,CACD,IAAIyC,EAAczG,OAAO0G,IAAI,CAACS,GAC9B,GAAI,CAACV,EAAY9E,MAAM,CACnB,MAAO,CAAA,EAEX,IAAK,IAAIqC,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACnD,GAAImD,AAAwB,OAAxBA,CAAG,CAACV,CAAW,CAACzC,EAAE,CAAC,CACnB,MAAO,CAAA,CAGnB,CACA,MAAO,CAAA,CACX,EAyBAoE,EAAU9H,SAAS,CAACqC,KAAK,CAAG,SAAU8F,CAAW,CAAEnG,CAAW,EAC1D,IACIoG,EAAe,CAAC,EACpBrG,AAFY,IAAI,CAEVd,IAAI,CAAC,CAAEC,KAAM,aAAcoB,OAAQN,CAAY,GAChDmG,GACDC,CAAAA,EAAaxF,OAAO,CAAGb,AAJf,IAAI,CAIiBa,OAAO,AAAD,EAElCb,AANO,IAAI,CAMLwD,MAAM,EACb6C,CAAAA,EAAa5C,EAAE,CAAGzD,AAPV,IAAI,CAOYyD,EAAE,AAAD,EAE7B,IAAI6C,EAAa,IAAIP,EAAUM,GAW/B,OAVKD,IACDE,EAAW3C,UAAU,CAAG3D,AAXhB,IAAI,CAWkB2D,UAAU,CACxC2C,EAAWC,kBAAkB,CAAGvG,AAZxB,IAAI,CAY0BuG,kBAAkB,CACxDD,EAAWE,eAAe,CAAGxG,AAbrB,IAAI,CAauBwG,eAAe,EAEtDxG,AAfY,IAAI,CAeVd,IAAI,CAAC,CACPC,KAAM,kBACNoB,OAAQN,EACRqG,WAAYA,CAChB,GACOA,CACX,EAmBAP,EAAU9H,SAAS,CAACwI,aAAa,CAAG,SAAUrC,CAAW,CAAEnE,CAAW,EAClE,IACIY,EAAUb,AADF,IAAI,CACIa,OAAO,CACvB6F,EAAiB,CAAC,EAClBC,EAAkB,CAAC,EACnB5H,EAAWiB,AAJH,IAAI,CAIKjB,QAAQ,CACzB2E,EAAW1D,AALH,IAAI,CAKK0D,QAAQ,CAE7B,GAAIU,AADJA,CAAAA,EAAeA,GAAezG,OAAO0G,IAAI,CAACxD,EAAQ,EAClCvB,MAAM,CAAE,CACpBU,AARQ,IAAI,CAQNd,IAAI,CAAC,CACPC,KAAM,gBACNiF,YAAaA,EACb7D,OAAQN,CACZ,GACA,IAAK,IAAI0B,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAE2C,EAAS,KAAK,EAAGxB,EAAa,KAAK,EAAGkB,EAAIwE,EAAM,EAAExE,EAEzFM,CAAAA,EAASpB,CAAO,CADhBJ,EAAa2D,CAAW,CAACzC,EAAE,CACC,AAAD,IAEvB+E,CAAc,CAACjG,EAAW,CAAGwB,EAC7B0E,CAAe,CAAClG,EAAW,CAAG,AAAIoB,MAAM6B,IAE5C,OAAO7C,CAAO,CAACJ,EAAW,CAe9B,OAbK9C,OAAO0G,IAAI,CAACxD,GAASvB,MAAM,GAC5BU,AAvBI,IAAI,CAuBF0D,QAAQ,CAAG,EACjB,IAAI,CAACkD,wBAAwB,IAE7B7H,GACAA,EAAS6B,aAAa,CA3BlB,IAAI,CA2BsB+F,EAAiB,EAAG1G,GAEtDD,AA7BQ,IAAI,CA6BNd,IAAI,CAAC,CACPC,KAAM,qBACN0B,QAAS6F,EACTtC,YAAaA,EACb7D,OAAQN,CACZ,GACOyG,CACX,CACJ,EAOAX,EAAU9H,SAAS,CAAC2I,wBAAwB,CAAG,WAC3C,OAAO,IAAI,CAACL,kBAAkB,CAC9B,OAAO,IAAI,CAACC,eAAe,AAG/B,EAsBAT,EAAU9H,SAAS,CAAC8F,UAAU,CAAG,SAAUrD,CAAQ,CAAEgD,CAAQ,CAAEzD,CAAW,EACrD,KAAK,IAAlByD,GAAuBA,CAAAA,EAAW,CAAA,EACtC,IACImD,EAAc,EAAE,CAChBC,EAAe,EAAE,CACjB/H,EAAWiB,AAHH,IAAI,CAGKjB,QAAQ,CAW7B,GAVAiB,AAJY,IAAI,CAIVd,IAAI,CAAC,CACPC,KAAM,aACNoB,OAAQN,EACRyD,SAAUA,EACVhD,SAAWA,GAAY,CAC3B,GACwB,KAAA,IAAbA,IACPA,EAAW,EACXgD,EAAW1D,AAZH,IAAI,CAYK0D,QAAQ,EAEzBA,EAAW,GAAKhD,EAAWV,AAdnB,IAAI,CAcqB0D,QAAQ,CAGzC,IAAK,IAFD7C,EAAUb,AAfN,IAAI,CAeQa,OAAO,CACvBuD,EAAczG,OAAO0G,IAAI,CAACxD,GACrBc,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAE2C,EAAS,KAAK,EAAG8E,EAAe,KAAK,EAAGtG,EAAa,KAAK,EAAGkB,EAAIwE,EAAM,EAAExE,EAAG,CAEnHM,EAASpB,CAAO,CADhBJ,EAAa2D,CAAW,CAACzC,EAAE,CACC,CAC5B,IAAIoB,EAASG,EAAiBd,MAAM,CAACH,EACjCvB,EACAgD,GACJqD,EAAehE,EAAON,OAAO,CAC7B5B,CAAO,CAACJ,EAAW,CAAGwB,EAASc,EAAOJ,KAAK,CACtChB,GACD3B,CAAAA,AA1BA,IAAI,CA0BE0D,QAAQ,CAAGzB,EAAO3C,MAAM,AAAD,EAEjC,IAAK,IAAI0H,EAAI,EAAGC,EAAOF,EAAazH,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EACpDH,CAAW,CAACG,EAAE,CAAIH,CAAW,CAACG,EAAE,EAAI,EAAE,CACtCH,CAAW,CAACG,EAAE,CAACrF,EAAE,CAAGoF,CAAY,CAACC,EAAE,CAEvCF,EAAajH,IAAI,CAAC,AAAIgC,MAAMsE,GAChC,CAYJ,OAVIpH,GACAA,EAAS+B,UAAU,CApCX,IAAI,CAoCegG,EAAepG,GAAY,EAAIT,GAE9DD,AAtCY,IAAI,CAsCVd,IAAI,CAAC,CACPC,KAAM,kBACNoB,OAAQN,EACRyD,SAAUA,EACVhD,SAAWA,GAAY,EACvBK,KAAM8F,CACV,GACOA,CACX,EASAd,EAAU9H,SAAS,CAACiB,IAAI,CAAG,SAAUY,CAAC,EAC9B,CACA,qBACA,kBACA,eACA,kBACA,eACH,CAACoH,QAAQ,CAACpH,EAAEX,IAAI,GACb,CAAA,IAAI,CAACwE,UAAU,CAAGmC,GAAoB,EAE1CF,EAAoB,IAAI,CAAE9F,EAAEX,IAAI,CAAEW,EACtC,EAeAiG,EAAU9H,SAAS,CAACkJ,OAAO,CAAG,SAAU1G,CAAU,CAAEC,CAAQ,EAExD,IAAIuB,EAASjC,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACtC,GAAIwB,EACA,OAAOA,CAAM,CAACvB,EAAS,AAE/B,EAeAqF,EAAU9H,SAAS,CAACmJ,gBAAgB,CAAG,SAAU3G,CAAU,CAAEC,CAAQ,EAEjE,IAAIuB,EAASjC,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACtC,MAAO,CAAC,CAAEwB,CAAAA,GAAUA,CAAM,CAACvB,EAAS,AAAD,CACvC,EAkBAqF,EAAU9H,SAAS,CAACoJ,eAAe,CAAG,SAAU5G,CAAU,CAAEC,CAAQ,CAAE4G,CAAM,EAExE,IAAIrF,EAASjC,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CAClCE,EAAasB,GAAUA,CAAM,CAACvB,EAAS,CAC3C,OAAQ,OAAOC,GACX,IAAK,UACD,MAAQA,GAAAA,CACZ,KAAK,SACD,OAAQ4G,MAAM5G,IAAc,CAAC2G,EAAS,KAAO3G,CACrD,CAEA,OAAQ4G,MADR5G,EAAY6G,WAAW,GAAGzF,MAAM,CAACpB,MAAAA,EAA6CA,EAAY,OAC9D,CAAC2G,EAAS,KAAO3G,CACjD,EAeAoF,EAAU9H,SAAS,CAACwJ,eAAe,CAAG,SAAUhH,CAAU,CAAEC,CAAQ,EAEhE,IAAIuB,EAASjC,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CAEtC,MAAO,GAAGsB,MAAM,CAAEE,GAAUA,CAAM,CAACvB,EAAS,CAChD,EAgBAqF,EAAU9H,SAAS,CAACgG,SAAS,CAAG,SAAUxD,CAAU,CAAEyD,CAAW,EAC7D,OAAO,IAAI,CAACC,UAAU,CAAC,CAAC1D,EAAW,CAAEyD,EAAY,CAACzD,EAAW,AACjE,EAqBAsF,EAAU9H,SAAS,CAACyJ,kBAAkB,CAAG,SAAUjH,CAAU,CAAE6G,CAAM,EAGjE,IAAIrF,EAASpB,AADCb,AADF,IAAI,CACIa,OAAO,AACP,CAACJ,EAAW,CAC5BkH,EAAiB,EAAE,CACvB,GAAI1F,EAAQ,CACR,IAAI2F,EAAe3F,EAAO3C,MAAM,CAChC,GAAIgI,EACA,IAAK,IAAI3F,EAAI,EAAGA,EAAIiG,EAAc,EAAEjG,EAChCgG,EAAe9H,IAAI,CAACG,AARpB,IAAI,CAQsBqH,eAAe,CAAC5G,EAAYkB,EAAG,CAAA,QAG5D,CACD,IAAK,IAAIA,EAAI,EAAGhB,EAAY,KAAK,EAAGgB,EAAIiG,EAAc,EAAEjG,EAAG,CAEvD,GAAI,AAAqB,UAArB,MADJhB,CAAAA,EAAYsB,CAAM,CAACN,EAAE,AAAD,EAGhB,OAAOM,EAAOH,KAAK,GAEvB,GAAInB,MAAAA,EAEA,KAER,CACA,IAAK,IAAIgB,EAAI,EAAGA,EAAIiG,EAAc,EAAEjG,EAChCgG,EAAe9H,IAAI,CAACG,AAxBpB,IAAI,CAwBsBqH,eAAe,CAAC5G,EAAYkB,GAE9D,CACJ,CACA,OAAOgG,CACX,EASA5B,EAAU9H,SAAS,CAAC4J,cAAc,CAAG,WAGjC,OADkBlK,OAAO0G,IAAI,CAACrE,AADlB,IAAI,CACoBa,OAAO,CAE/C,EAmBAkF,EAAU9H,SAAS,CAACkG,UAAU,CAAG,SAAUC,CAAW,CAAEF,CAAW,CAAE4D,CAAc,EAC/E,IACIC,EAAe/H,AADP,IAAI,CACSa,OAAO,CAC5BA,EAAU,CAAC,EACfuD,EAAeA,GAAezG,OAAO0G,IAAI,CAAC0D,GAC1C,IAAK,IAAIpG,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAE2C,EAAS,KAAK,EAAGxB,EAAa,KAAK,EAAGkB,EAAIwE,EAAM,EAAExE,EAEzFM,CAAAA,EAAS8F,CAAY,CADrBtH,EAAa2D,CAAW,CAACzC,EAAE,CACM,AAAD,IAExBuC,EACArD,CAAO,CAACJ,EAAW,CAAGwB,EAEjB6F,GAAkB,CAACjG,MAAMM,OAAO,CAACF,GACtCpB,CAAO,CAACJ,EAAW,CAAGoB,MAAMN,IAAI,CAACU,GAGjCpB,CAAO,CAACJ,EAAW,CAAGwB,EAAOH,KAAK,IAI9C,OAAOjB,CACX,EAWAkF,EAAU9H,SAAS,CAAC+J,gBAAgB,CAAG,SAAUC,CAAgB,EAC7D,IAAIzB,EAAkB,IAAI,CAACA,eAAe,QAC1C,AAAIA,EACOA,CAAe,CAACyB,EAAiB,CAErCA,CACX,EAQAlC,EAAU9H,SAAS,CAACiK,WAAW,CAAG,WAC9B,OAAO,IAAI,CAACnJ,QAAQ,AACxB,EAWAgH,EAAU9H,SAAS,CAACkK,mBAAmB,CAAG,SAAUzH,CAAQ,EACxD,IAAI6F,EAAqB,IAAI,CAACA,kBAAkB,QAChD,AAAIA,EACOA,CAAkB,CAAC7F,EAAS,CAEhCA,CACX,EAgBAqF,EAAU9H,SAAS,CAACsG,MAAM,CAAG,SAAU7D,CAAQ,CAAE0D,CAAW,EACxD,OAAO,IAAI,CAACgE,OAAO,CAAC1H,EAAU,EAAG0D,EAAY,CAAC,EAAE,AACpD,EASA2B,EAAU9H,SAAS,CAACoK,WAAW,CAAG,WAE9B,OAAO,IAAI,CAAC3E,QAAQ,AACxB,EAkBAqC,EAAU9H,SAAS,CAACqK,aAAa,CAAG,SAAU7H,CAAU,CAAEE,CAAS,CAAE4H,CAAc,EAE/E,IAAItG,EAASjC,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,CACtC,GAAIwB,EAAQ,CACR,IAAIvB,EAAW,GASf,GARImB,MAAMM,OAAO,CAACF,GAEdvB,EAAWuB,EAAOuG,OAAO,CAAC7H,EAAW4H,GAEhC1C,EAASlF,IAEdD,CAAAA,EAAWuB,EAAOuG,OAAO,CAAC7H,EAAW4H,EAAc,EAEnD7H,AAAa,KAAbA,EACA,OAAOA,CAEf,CACJ,EAgBAqF,EAAU9H,SAAS,CAACwK,YAAY,CAAG,SAAU/H,CAAQ,CAAE0D,CAAW,EAC9D,OAAO,IAAI,CAACsE,aAAa,CAAChI,EAAU,EAAG0D,EAAY,CAAC,EAAE,AAC1D,EAkBA2B,EAAU9H,SAAS,CAACyK,aAAa,CAAG,SAAUhI,CAAQ,CAAEgD,CAAQ,CAAEU,CAAW,EACxD,KAAK,IAAlB1D,GAAuBA,CAAAA,EAAW,CAAA,EACrB,KAAK,IAAlBgD,GAAuBA,CAAAA,EAAY,IAAI,CAACA,QAAQ,CAAGhD,CAAQ,EAC/D,IACIG,EAAUb,AADF,IAAI,CACIa,OAAO,CACvBE,EAAO,AAAIc,MAAM6B,GACrBU,EAAeA,GAAezG,OAAO0G,IAAI,CAACxD,GAC1C,IAAK,IAAIc,EAAIjB,EAAUiI,EAAK,EAAGxC,EAAOvC,KAAKgF,GAAG,CAAC5I,AAJnC,IAAI,CAIqC0D,QAAQ,CAAGhD,EAAWgD,GAAYzB,EAAS,KAAK,EAAG6C,EAAM,KAAK,EAAGnD,EAAIwE,EAAM,EAAExE,EAAG,EAAEgH,EAAI,CACvI7D,EAAM/D,CAAI,CAAC4H,EAAG,CAAG,CAAC,EAClB,IAAK,IAAIE,EAAK,EAAGC,EAAgB1E,EAAayE,EAAKC,EAAcxJ,MAAM,CAAEuJ,IAAM,CAC3E,IAAIpI,EAAaqI,CAAa,CAACD,EAAG,CAClC5G,EAASpB,CAAO,CAACJ,EAAW,CAC5BqE,CAAG,CAACrE,EAAW,CAAIwB,EAASA,CAAM,CAACN,EAAE,CAAG,KAAK,CACjD,CACJ,CACA,OAAOZ,CACX,EAkBAgF,EAAU9H,SAAS,CAACmK,OAAO,CAAG,SAAU1H,CAAQ,CAAEgD,CAAQ,CAAEU,CAAW,EAClD,KAAK,IAAlB1D,GAAuBA,CAAAA,EAAW,CAAA,EACrB,KAAK,IAAlBgD,GAAuBA,CAAAA,EAAY,IAAI,CAACA,QAAQ,CAAGhD,CAAQ,EAC/D,IACIG,EAAUb,AADF,IAAI,CACIa,OAAO,CACvBE,EAAO,AAAIc,MAAM6B,GACrBU,EAAeA,GAAezG,OAAO0G,IAAI,CAACxD,GAC1C,IAAK,IAAIc,EAAIjB,EAAUiI,EAAK,EAAGxC,EAAOvC,KAAKgF,GAAG,CAAC5I,AAJnC,IAAI,CAIqC0D,QAAQ,CAAGhD,EAAWgD,GAAYzB,EAAS,KAAK,EAAG6C,EAAM,KAAK,EAAGnD,EAAIwE,EAAM,EAAExE,EAAG,EAAEgH,EAAI,CACvI7D,EAAM/D,CAAI,CAAC4H,EAAG,CAAG,EAAE,CACnB,IAAK,IAAIE,EAAK,EAAGE,EAAgB3E,EAAayE,EAAKE,EAAczJ,MAAM,CAAEuJ,IAErE5G,EAASpB,CAAO,CADCkI,CAAa,CAACF,EAAG,CACN,CAC5B/D,EAAIjF,IAAI,CAACoC,EAASA,CAAM,CAACN,EAAE,CAAG,KAAK,EAE3C,CACA,OAAOZ,CACX,EASAgF,EAAU9H,SAAS,CAAC+K,aAAa,CAAG,WAChC,OAAO,IAAI,CAACrF,UAAU,AAC1B,EAYAoC,EAAU9H,SAAS,CAACgL,UAAU,CAAG,SAAU7E,CAAW,EAGlD,IAAK,IADDvD,EAAUb,AADF,IAAI,CACIa,OAAO,CAClBc,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAEmB,EAAa,KAAK,EAAGkB,EAAIwE,EAAM,EAAExE,EAExE,GAAI,CAACd,CAAO,CADCuD,CAAW,CAACzC,EAAE,CACH,CACpB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,EAeAoE,EAAU9H,SAAS,CAACiL,UAAU,CAAG,SAAUzI,CAAU,CAAEE,CAAS,EAE5D,IAAIsB,EAASjC,AADD,IAAI,CACGa,OAAO,CAACJ,EAAW,QAEtC,AAAIoB,MAAMM,OAAO,CAACF,GACNA,AAA8B,KAA9BA,EAAOuG,OAAO,CAAC7H,KAGvB+E,CAAAA,EAAQ/E,IAAcwI,OAAOC,QAAQ,CAACzI,EAAS,GACvCsB,AAA+B,KAA/BA,EAAOuG,OAAO,CAAC,CAAC7H,EAGhC,EAeAoF,EAAU9H,SAAS,CAACoB,EAAE,CAAG,SAAUF,CAAI,CAAE6B,CAAQ,EAC7C,OAAOyE,EAAmB,IAAI,CAAEtG,EAAM6B,EAC1C,EAgBA+E,EAAU9H,SAAS,CAACoL,YAAY,CAAG,SAAU5I,CAAU,CAAE6I,CAAa,EAClE,IACIzI,EAAUb,AADF,IAAI,CACIa,OAAO,OAC3B,EAAIA,CAAO,CAACJ,EAAW,GACfA,IAAe6I,IACfzI,CAAO,CAACyI,EAAc,CAAGzI,CAAO,CAACJ,EAAW,CAC5C,OAAOI,CAAO,CAACJ,EAAW,EAEvB,CAAA,EAGf,EAsBAsF,EAAU9H,SAAS,CAACsL,OAAO,CAAG,SAAU9I,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,EAChF,IACIY,EAAUb,AADF,IAAI,CACIa,OAAO,CACvB9B,EAAWiB,AAFH,IAAI,CAEKjB,QAAQ,CACzBkD,EAASpB,CAAO,CAACJ,EAAW,CAC5BwB,CAAAA,CAAAA,GAAUA,CAAM,CAACvB,EAAS,GAAKC,CAAQ,IAG3CX,AAPY,IAAI,CAOVd,IAAI,CAAC,CACPC,KAAM,UACNwB,UAAWA,EACXF,WAAYA,EACZF,OAAQN,EACRS,SAAUA,CACd,GACKuB,GACDA,CAAAA,EAASpB,CAAO,CAACJ,EAAW,CAAG,AAAIoB,MAAM7B,AAfjC,IAAI,CAemC0D,QAAQ,CAAA,EAEvDhD,GAAYV,AAjBJ,IAAI,CAiBM0D,QAAQ,EAC1B1D,CAAAA,AAlBQ,IAAI,CAkBN0D,QAAQ,CAAIhD,EAAW,CAAC,EAElCuB,CAAM,CAACvB,EAAS,CAAGC,EACf5B,GACAA,EAASyB,UAAU,CAtBX,IAAI,CAsBeC,EAAYC,EAAUC,GAErDX,AAxBY,IAAI,CAwBVd,IAAI,CAAC,CACPC,KAAM,eACNwB,UAAWA,EACXF,WAAYA,EACZF,OAAQN,EACRS,SAAUA,CACd,GACJ,EAyBAqF,EAAU9H,SAAS,CAAC0G,UAAU,CAAG,SAAU9D,CAAO,CAAEH,CAAQ,CAAET,CAAW,CAAEuJ,CAAc,EACrF,IACIzB,EAAe/H,AADP,IAAI,CACSa,OAAO,CAC5B4I,EAAgBzJ,AAFR,IAAI,CAEUjB,QAAQ,CAC9BqF,EAAczG,OAAO0G,IAAI,CAACxD,GAC1B6C,EAAW1D,AAJH,IAAI,CAIK0D,QAAQ,CAQ7B,GAPA1D,AALY,IAAI,CAKVd,IAAI,CAAC,CACPC,KAAM,aACN0B,QAASA,EACTuD,YAAaA,EACb7D,OAAQN,EACRS,SAAUA,CACd,GACI,AAACgF,EAAQhF,IAAc8I,EAGtB,CACD,IAAK,IAAI7H,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAE2C,EAAS,KAAK,EAAGyH,EAAc,KAAK,EAAGjJ,EAAa,KAAK,EAAGkJ,EAAmB,KAAK,EAAGhI,EAAIwE,EAAM,EAAExE,EAAG,CAE7IM,EAASpB,CAAO,CADhBJ,EAAa2D,CAAW,CAACzC,EAAE,CACC,CAE5BgI,EAAmBhM,OAAOkF,cAAc,CAAC,AAAC6G,AAD1CA,CAAAA,EAAc3B,CAAY,CAACtH,EAAW,AAAD,GACoB+I,EAAkBE,EAAczH,GAAQa,WAAW,CACvG4G,EAGIC,IAAqB9H,MACrBA,MAAMM,OAAO,CAACuH,IACfA,CAAAA,EAAc7H,MAAMN,IAAI,CAACmI,EAAW,EAGnCA,EAAYpK,MAAM,CAAGoE,GAG1BgG,AAFAA,CAAAA,EACI,IAAIC,EAAiBjG,EAAQ,EACrBV,GAAG,CAAC+E,CAAY,CAACtH,EAAW,EAVxCiJ,EAAc,IAAIC,EAAiBjG,GAYvCqE,CAAY,CAACtH,EAAW,CAAGiJ,EAC3B,IAAK,IAAIE,EAAOlJ,GAAY,EAAImJ,EAAS5H,EAAO3C,MAAM,CAAEsK,EAAMC,EAAQ,EAAED,EACpEF,CAAW,CAACE,EAAI,CAAG3H,CAAM,CAAC2H,EAAI,CAElClG,EAAWE,KAAKC,GAAG,CAACH,EAAUzB,EAAO3C,MAAM,CAC/C,CACA,IAAI,CAACwE,aAAa,CAACJ,EACvB,MA5BIsC,EAAO/H,SAAS,CAAC0G,UAAU,CAACxG,IAAI,CAAC,IAAI,CAAE0C,EAASH,EAAUiF,EAAO1F,EAAa,CAAE2E,OAAQ,CAAA,CAAK,IA6B7F6E,GACAA,EAAc7I,aAAa,CA3CnB,IAAI,CA2CuBC,EAASH,GAAY,GAE5DV,AA7CY,IAAI,CA6CVd,IAAI,CAAC,CACPC,KAAM,kBACN0B,QAASA,EACTuD,YAAaA,EACb7D,OAAQN,EACRS,SAAUA,CACd,EACJ,EAgBAqF,EAAU9H,SAAS,CAAC6L,WAAW,CAAG,SAAU/K,CAAQ,CAAEkB,CAAW,EAC7D,IACI8J,EADA/J,EAAQ,IAAI,CAgBhB,OAdAA,EAAMd,IAAI,CAAC,CACPC,KAAM,cACNoB,OAAQN,EACRlB,SAAUA,EACVsB,SAAUL,EAAMK,QAAQ,AAC5B,GACAL,EAAMK,QAAQ,CAAGL,EACjBA,EAAMjB,QAAQ,CAAGA,EAOVgL,CANHhL,EACUA,EAASgB,MAAM,CAACC,GAGhBE,QAAQC,OAAO,CAACH,IAGzBgK,IAAI,CAAC,SAAUhK,CAAK,EAOrB,OANAA,EAAMd,IAAI,CAAC,CACPC,KAAM,mBACNoB,OAAQN,EACRlB,SAAUA,EACVsB,SAAUL,EAAMK,QAAQ,AAC5B,GACOL,CACX,GAAG,KAAQ,CAAC,SAAUiK,CAAK,EAOvB,MANAjK,EAAMd,IAAI,CAAC,CACPC,KAAM,mBACN8K,MAAOA,EACPlL,SAAUA,EACVsB,SAAUL,EAAMK,QAAQ,AAC5B,GACM4J,CACV,EACJ,EAWAlE,EAAU9H,SAAS,CAACiM,qBAAqB,CAAG,SAAU3D,CAAkB,CAAE4D,CAAmB,EAGzF,GAF4B,KAAK,IAA7BA,GAAkCA,CAAAA,EAAsB,CAAA,CAAI,EAChE,IAAI,CAAC5D,kBAAkB,CAAGA,GACtB4D,EAIJ,IAAK,IADDC,EAAkB,IAAI,CAAC5D,eAAe,CAAG,EAAE,CACtC7E,EAAI,EAAGwE,EAAOI,EAAmBjH,MAAM,CAAE+K,EAAgB,KAAK,EAAG1I,EAAIwE,EAAM,EAAExE,EAE9E+D,EADJ2E,EAAgB9D,CAAkB,CAAC5E,EAAE,GAEjCyI,CAAAA,CAAe,CAACC,EAAc,CAAG1I,CAAAA,CAG7C,EAyBAoE,EAAU9H,SAAS,CAAC4G,MAAM,CAAG,SAAUC,CAAG,CAAEpE,CAAQ,CAAEqE,CAAM,CAAE9E,CAAW,EACrE,IAAI,CAACqK,OAAO,CAAC,CAACxF,EAAI,CAAEpE,EAAUqE,EAAQ9E,EAC1C,EAuBA8F,EAAU9H,SAAS,CAACqM,OAAO,CAAG,SAAUvJ,CAAI,CAAEL,CAAQ,CAAEqE,CAAM,CAAE9E,CAAW,EACtD,KAAK,IAAlBS,GAAuBA,CAAAA,EAAW,IAAI,CAACgD,QAAQ,AAAD,EAClD,IACI7C,EAAUb,AADF,IAAI,CACIa,OAAO,CACvBuD,EAAczG,OAAO0G,IAAI,CAACxD,GAC1B9B,EAAWiB,AAHH,IAAI,CAGKjB,QAAQ,CACzB2E,EAAW3C,EAAKzB,MAAM,CAC1BU,AALY,IAAI,CAKVd,IAAI,CAAC,CACPC,KAAM,UACNoB,OAAQN,EACRyD,SAAUA,EACVhD,SAAUA,EACVK,KAAMA,CACV,GACA,IAAK,IAAIY,EAAI,EAAGgH,EAAKjI,EAAUoE,EAAM,KAAK,EAAGnD,EAAI+B,EAAU,EAAE/B,EAAG,EAAEgH,EAE9D,GAAI7D,AADJA,CAAAA,EAAM/D,CAAI,CAACY,EAAE,AAAD,IACAoE,EAAUG,IAAI,CACtB,IAAK,IAAIc,EAAI,EAAGC,EAAO7C,EAAY9E,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EAAG,CACtD,IAAI/E,EAASpB,CAAO,CAACuD,CAAW,CAAC4C,EAAE,CAAC,CAChCjC,EACAlE,CAAO,CAACuD,CAAW,CAAC4C,EAAE,CAAC,CAAG9D,EAAiBd,MAAM,CAACH,EAAQ0G,EAAI,EAAG,CAAA,EAAM,CAAC,KAAK,EAAEhG,KAAK,CAGpFV,CAAM,CAAC0G,EAAG,CAAG,IAErB,MAEC,GAAI7D,aAAejD,MACpB,IAAK,IAAImF,EAAI,EAAGC,EAAO7C,EAAY9E,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EACnDnG,CAAO,CAACuD,CAAW,CAAC4C,EAAE,CAAC,CAAC2B,EAAG,CAAG7D,CAAG,CAACkC,EAAE,MAIxChB,EAAO/H,SAAS,CAAC4G,MAAM,CAAC1G,IAAI,CAAC,IAAI,CAAE2G,EAAK6D,EAAI,KAAK,EAAG,CAAE/D,OAAQ,CAAA,CAAK,GAG3E,IAAII,EAAgBD,EACZrB,EAAW3C,EAAKzB,MAAM,CACtBoB,EAAWgD,EACnB,GAAIsB,EAAgBhF,AArCR,IAAI,CAqCU0D,QAAQ,CAAE,CAChC1D,AAtCQ,IAAI,CAsCN0D,QAAQ,CAAGsB,EACjB,IAAK,IAAIrD,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAAG,CACtD,IAAIlB,EAAa2D,CAAW,CAACzC,EAAE,AAC/Bd,CAAAA,CAAO,CAACJ,EAAW,CAAGyC,EAAiBlB,SAAS,CAACnB,CAAO,CAACJ,EAAW,CAAEuE,EAC1E,CACJ,CACIjG,GACAA,EAAS+B,UAAU,CA7CX,IAAI,CA6CeC,EAAML,GAErCV,AA/CY,IAAI,CA+CVd,IAAI,CAAC,CACPC,KAAM,eACNoB,OAAQN,EACRyD,SAAUA,EACVhD,SAAUA,EACVK,KAAMA,CACV,EACJ,EAmBAgF,EAAUG,IAAI,CAAG,CAAC,EAKlBH,EAAUwE,OAAO,CAAG,QACbxE,CACX,EAxrCuDzC,GAmtCnDkH,EAAyB,AAACjM,IAA+EC,QAAQ,CAAEiM,EAA0B,AAAClM,IAA+EE,SAAS,CAAEiM,EAAsB,AAACnM,IAA+EG,KAAK,CAAEiM,EAAO,AAACpM,IAA+EoM,IAAI,CAWhbhO,EAA+B,WAY/B,SAASA,EAAckC,CAAO,EACV,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAI,CAACmB,KAAK,CAAG,IA9C8B+F,EA8CXlH,EAAQD,SAAS,EACjD,IAAI,CAACgM,QAAQ,CAAG/L,EAAQ+L,QAAQ,EAAI,CAAE/J,QAAS,CAAC,CAAE,CACtD,CA8LA,OA7LAlD,OAAOC,cAAc,CAACjB,EAAcsB,SAAS,CAAE,UAAW,CAItDH,IAAK,WACD,MAAO,CAAC,CAAC,IAAI,CAAC+M,QAAQ,AAC1B,EACAhN,WAAY,CAAA,EACZiN,aAAc,CAAA,CAClB,GAeAnO,EAAcsB,SAAS,CAAC8M,cAAc,CAAG,SAAUC,CAAI,CAAEC,CAAU,EAC/D,IACIpK,EAAUqK,AADE,IAAI,CACIN,QAAQ,CAAC/J,OAAO,AACxCA,CAAAA,CAAO,CAACmK,EAAK,CAAGN,EAAoB7J,CAAO,CAACmK,EAAK,EAAI,CAAC,EAAGC,EAC7D,EAOAtO,EAAcsB,SAAS,CAACkN,eAAe,CAAG,SAAUtK,CAAO,EAIvD,IAHA,IAEIJ,EADA2D,EAAczG,OAAO0G,IAAI,CAACxD,GAEvB,AAA4C,UAA5C,MAAQJ,CAAAA,EAAa2D,EAAYgH,GAAG,EAAC,GACxCF,AAJY,IAAI,CAINH,cAAc,CAACtK,EAAYI,CAAO,CAACJ,EAAW,CAEhE,EAQA9D,EAAcsB,SAAS,CAACiB,IAAI,CAAG,SAAUY,CAAC,EACtC2K,EAAwB,IAAI,CAAE3K,EAAEX,IAAI,CAAEW,EAC1C,EAUAnD,EAAcsB,SAAS,CAACoN,cAAc,CAAG,SAEzCC,CAAoB,EAChB,IACIzK,EAAUqK,AADE,IAAI,CACIN,QAAQ,CAAC/J,OAAO,CACpC0K,EAAQ5N,OAAO0G,IAAI,CAACxD,GAAW,CAAC,GACpC,GAAI0K,EAAMjM,MAAM,CACZ,OAAOiM,EAAMC,IAAI,CAAC,SAAUjO,CAAC,CAAE4H,CAAC,EAAI,OAAQwF,EAAK9J,CAAO,CAACtD,EAAE,CAACkO,KAAK,CAAE,GAAKd,EAAK9J,CAAO,CAACsE,EAAE,CAACsG,KAAK,CAAE,EAAK,EAE5G,EAWA9O,EAAcsB,SAAS,CAACyN,gBAAgB,CAAG,SAAUC,CAAoB,EACrE,OAAO,IAAI,CAAC3L,KAAK,CAACmE,UAAU,CAAC,IAAI,CAACkH,cAAc,CAACM,GACrD,EASAhP,EAAcsB,SAAS,CAAC2N,IAAI,CAAG,WAE3B,OADAnB,EAAwB,IAAI,CAAE,YAAa,CAAEzK,MAAO,IAAI,CAACA,KAAK,AAAC,GACxDE,QAAQC,OAAO,CAAC,IAAI,CAC/B,EAaAxD,EAAcsB,SAAS,CAACoB,EAAE,CAAG,SAAUF,CAAI,CAAE6B,CAAQ,EACjD,OAAOwJ,EAAuB,IAAI,CAAErL,EAAM6B,EAC9C,EAUArE,EAAcsB,SAAS,CAAC4N,IAAI,CAAG,WAE3B,OADApB,EAAwB,IAAI,CAAE,YAAa,CAAEzK,MAAO,IAAI,CAACA,KAAK,AAAC,GACxDE,QAAQE,MAAM,CAAC,AAAI0L,MAAM,mBACpC,EAOAnP,EAAcsB,SAAS,CAAC8N,cAAc,CAAG,SAAU3H,CAAW,EAE1D,IAAK,IAAIzC,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACnDuJ,AAFY,IAAI,CAENH,cAAc,CAAC3G,CAAW,CAACzC,EAAE,CAAE,CAAE8J,MAAO9J,CAAE,EAE5D,EACAhF,EAAcsB,SAAS,CAAC+N,kBAAkB,CAAG,SAAUC,CAAe,EAClE,IAAI1I,EAAQ,IAAI,CACZ2I,EAAiBD,GACb7K,EAAuBH,KAAK,CAACgL,EAAgB9M,IAAI,CAAC,CAC1D,OAAO,IAAI,CAACa,KAAK,CACZ8J,WAAW,CAACoC,EACb,IAAIA,EAAcD,GAClB,KAAK,GACJjC,IAAI,CAAC,WAAc,OAAOzG,CAAO,EAC1C,EAOA5G,EAAcsB,SAAS,CAACkO,YAAY,CAAG,SAAUC,CAAW,EACpC,KAAK,IAArBA,GAA0BA,CAAAA,EAAc,GAAG,EAC/C,IAAIlB,EAAY,IAAI,CACpBxL,OAAO2M,YAAY,CAACnB,EAAUL,QAAQ,EACtCK,EAAUL,QAAQ,CAAGnL,OAAO4M,UAAU,CAAC,WAAc,OAAOpB,EACvDU,IAAI,GAAG,KAAQ,CAAC,SAAU3B,CAAK,EAAI,OAAOiB,EAAUhM,IAAI,CAAC,CAC1DC,KAAM,YACN8K,MAAOA,EACPjK,MAAOkL,EAAUlL,KAAK,AAC1B,EAAI,GACCgK,IAAI,CAAC,WACFkB,EAAUL,QAAQ,EAClBK,EAAUiB,YAAY,CAACC,EAE/B,EAAI,EAAGA,EACX,EAIAzP,EAAcsB,SAAS,CAACsO,WAAW,CAAG,WAElC7M,OAAO2M,YAAY,CAACnB,AADJ,IAAI,CACUL,QAAQ,EACtC,OAAOK,AAFS,IAAI,CAEHL,QAAQ,AAC7B,EAUAlO,EAAcsB,SAAS,CAACuO,MAAM,CAAG,SAAUxB,CAAI,EAC3C,OAAO,IAAI,CAACJ,QAAQ,CAAC/J,OAAO,CAACmK,EAAK,AACtC,EACOrO,CACX,GAoBIA,EAdOA,EA2CRA,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GA7BpBsE,KAAK,CAAG,CAAC,EA4BvBtE,EAAcuE,YAAY,CAL1B,SAAsBzD,CAAG,CAAEgP,CAAkB,EACzC,MAAQ,CAAC,CAAChP,GACN,CAACd,EAAcsE,KAAK,CAACxD,EAAI,EACzB,CAAC,CAAEd,CAAAA,EAAcsE,KAAK,CAACxD,EAAI,CAAGgP,CAAiB,CACvD,EAQyB,IAAIC,EAA4B/P,EAuBzDgQ,EAAyB,AAACpO,IAA+EC,QAAQ,CAAEoO,GAA0B,AAACrO,IAA+EE,SAAS,CAAEoO,GAAyB,AAACtO,IAA+EsH,QAAQ,CAAEiH,GAAsB,AAACvO,IAA+EG,KAAK,CAWtc9B,GAA+B,WAY/B,SAASA,EAAciC,CAAO,EAS1B,IAAI,CAACkO,WAAW,CAAG,CACf,aAAc,CACVC,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC3CG,GACR,CACJ,EACA,aAAc,CACVL,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC3CG,GACR,EACAC,YAAa,YACjB,EACA,aAAc,CACVN,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC3CG,GACR,CACJ,EACA,WAAY,CACRL,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,IAAI5P,EAAI,IAAI6P,KACZ,GAAI,CAACD,EACD,OAAOG,IAEX,IAAIE,EAAO,CAACL,CAAK,CAAC,EAAE,CAOpB,OANIK,EAAQjQ,EAAEkQ,WAAW,GAAK,IAC1BD,GAAQ,KAGRA,GAAQ,IAELJ,KAAKC,GAAG,CAACG,EAAML,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,CACjD,EACAI,YAAa,UACjB,EACA,WAAY,CACRN,MAAO,0CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAG,IAAMA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAClDG,GACR,CACJ,CACJ,EACA,IAAII,EAAgBX,GAAoBlQ,EAAc8Q,cAAc,CAChE7O,GACA8O,EAAcF,EAAcG,YAAY,CACxCD,CAAAA,AAAgB,MAAhBA,GAAuBA,AAAgB,MAAhBA,CAAkB,IACzCA,EAAcA,AAAgB,MAAhBA,EAAsB,MAAQ,IAC5C,IAAI,CAACE,aAAa,CACd,AAAIC,OAAO,cAAgBH,EAAc,cAEjD,IAAI,CAAC9O,OAAO,CAAG4O,CACnB,CA6bA,OA9aA7Q,EAAcqB,SAAS,CAAC8P,SAAS,CAAG,SAAUC,CAAK,QAC/C,AAAI,AAAiB,WAAjB,OAAOA,EACAA,EAEP,AAAiB,UAAjB,OAAOA,EACAA,AAAU,KAAVA,GAAgBA,AAAU,MAAVA,GAAiBA,AAAU,UAAVA,EAErC,CAAC,CAAC,IAAI,CAACC,QAAQ,CAACD,EAC3B,EAUApR,EAAcqB,SAAS,CAACiQ,MAAM,CAAG,SAAUF,CAAK,EAC5C,IAAIG,EACJ,GAAI,AAAiB,UAAjB,OAAOH,EACPG,EAAY,IAAI,CAACC,SAAS,CAACJ,QAE1B,GAAI,AAAiB,UAAjB,OAAOA,EACZG,EAAYH,OAEX,GAAIA,aAAiBb,KACtB,OAAOa,EAGPG,EAAY,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,QAAQ,CAACL,IAE7C,OAAO,IAAIb,KAAKgB,EACpB,EAUAvR,EAAcqB,SAAS,CAACqQ,aAAa,CAAG,SAAUN,CAAK,EAOnD,MAAOO,AALO,CAAA,CACN,OAAUC,AAFF,IAAI,CAEQP,QAAQ,CAC5B,KAAQO,AAHA,IAAI,CAGMN,MAAM,CACxB,OAAUM,AAJF,IAAI,CAIQH,QAAQ,AAChC,CAAA,CACU,CAACG,AANC,IAAI,CAMKC,SAAS,CAACT,GAAO,CAAC7P,IAAI,CAN/B,IAAI,CAMuC6P,EAC/D,EAUApR,EAAcqB,SAAS,CAACgQ,QAAQ,CAAG,SAAUD,CAAK,EAC9C,GAAI,AAAiB,UAAjB,OAAOA,EACP,OAAOA,EAEX,GAAI,AAAiB,WAAjB,OAAOA,EACP,MAAOA,GAAAA,EAEX,GAAI,AAAiB,UAAjB,OAAOA,EAAoB,CAC3B,IAAIU,EAAe,IAAI,CAACb,aAAa,CAIrC,GAHIG,EAAMxF,OAAO,CAAC,KAAO,IACrBwF,CAAAA,EAAQA,EAAMW,OAAO,CAAC,OAAQ,GAAE,EAEhCD,EAAc,CACd,GAAI,CAACA,EAAaE,IAAI,CAACZ,GACnB,OAAOX,IAEXW,EAAQA,EAAMW,OAAO,CAACD,EAAc,QACxC,CACA,OAAOlH,WAAWwG,EACtB,QACA,AAAIA,aAAiBb,KACVa,EAAMa,OAAO,GAEpBb,EACOA,EAAM3F,WAAW,GAErBgF,GACX,EAUAzQ,EAAcqB,SAAS,CAACoQ,QAAQ,CAAG,SAAUL,CAAK,EAC9C,MAAO,GAAKA,CAChB,EAmBApR,EAAcqB,SAAS,CAAC6Q,gBAAgB,CAAG,SAAUC,CAAI,CAAEC,CAAK,CAAEnD,CAAI,EAClE,IAG2BoD,EAEvBC,EAAMlI,EAJNmI,EAAS,EAAE,CACXtL,EAAM,EAAE,CACRuL,EAAS,aAAqBC,EAAgB,EAAE,CAAE1N,EAAI,EAAG2N,EAAgB,CAAA,EAM7E,IAHI,CAAA,CAACN,GAASA,EAAQD,EAAKzP,MAAM,AAAD,GAC5B0P,CAAAA,EAAQD,EAAKzP,MAAM,AAAD,EAEfqC,EAAIqN,EAAOrN,IACd,GAAI,AAAmB,KAAA,IAAZoN,CAAI,CAACpN,EAAE,EACdoN,CAAI,CAACpN,EAAE,EAAIoN,CAAI,CAACpN,EAAE,CAACrC,MAAM,CAUzB,IAAK0H,EAAI,EATTiI,EAAQF,CAAI,CAACpN,EAAE,CACV4N,IAAI,GACJZ,OAAO,CAAC,YAAa,KACrBa,KAAK,CAAC,KACXH,EAAgB,CACZ,GACA,GACA,GACH,CACWrI,EAAIiI,EAAM3P,MAAM,CAAE0H,IACtBA,EAAIqI,EAAc/P,MAAM,EACxB4P,CAAAA,EAAOO,SAASR,CAAK,CAACjI,EAAE,CAAE,GAAE,IAExBnD,CAAG,CAACmD,EAAE,CAAG,AAAC,CAACnD,CAAG,CAACmD,EAAE,EAAInD,CAAG,CAACmD,EAAE,CAAGkI,EAAQA,EAAOrL,CAAG,CAACmD,EAAE,CAC/C,AAAqB,KAAA,IAAdmI,CAAM,CAACnI,EAAE,CACZmI,CAAM,CAACnI,EAAE,GAAKkI,GACdC,CAAAA,CAAM,CAACnI,EAAE,CAAG,CAAA,CAAI,EAIpBmI,CAAM,CAACnI,EAAE,CAAGkI,EAEZA,EAAO,GACHA,EAAO,IACPG,CAAa,CAACrI,EAAE,CAAG,KAGnBqI,CAAa,CAACrI,EAAE,CAAG,OAIlBkI,EAAO,IACZA,GAAQ,IACRG,CAAa,CAACrI,EAAE,CAAG,KACnBsI,EAAgB,CAAA,GAEVD,CAAa,CAACrI,EAAE,CAAC1H,MAAM,EAC7B+P,CAAAA,CAAa,CAACrI,EAAE,CAAG,IAAG,GAO9C,GAAIsI,EAAe,CAEf,IAAKtI,EAAI,EAAGA,EAAImI,EAAO7P,MAAM,CAAE0H,IACvBmI,AAAc,CAAA,IAAdA,CAAM,CAACnI,EAAE,CACLnD,CAAG,CAACmD,EAAE,CAAG,IACTqI,AAAqB,OAArBA,CAAa,CAACrI,EAAE,EAChBqI,AAAqB,SAArBA,CAAa,CAACrI,EAAE,EAChBqI,CAAAA,CAAa,CAACrI,EAAE,CAAG,IAAG,EAGrBnD,CAAG,CAACmD,EAAE,CAAG,IAAMqI,AAAqB,OAArBA,CAAa,CAACrI,EAAE,EACpCqI,CAAAA,CAAa,CAACrI,EAAE,CAAG,IAAG,CAKD,CAAA,IAAzBqI,EAAc/P,MAAM,EACpB+P,AAAqB,OAArBA,CAAa,CAAC,EAAE,EAChBA,AAAqB,OAArBA,CAAa,CAAC,EAAE,EAChBA,CAAAA,CAAa,CAAC,EAAE,CAAG,IAAG,EAE1BD,EAASC,EAAcK,IAAI,CAAC,IAGhC,CAKA,OAHI7D,GACAoB,CAAAA,AAnFS,IAAI,CAmFNpO,OAAO,CAAC8Q,UAAU,CAAGP,CAAK,EAE9BA,CACX,EAOAxS,EAAcqB,SAAS,CAACiB,IAAI,CAAG,SAAUY,CAAC,EACtC8M,GAAwB,IAAI,CAAE9M,EAAEX,IAAI,CAAEW,EAC1C,EAUAlD,EAAcqB,SAAS,CAAC2R,MAAM,CAAG,SAEjC1E,CAAS,CAAErM,CAAO,EAQd,MALA,IAAI,CAACK,IAAI,CAAC,CACNC,KAAM,cACN0B,QAAS,EAAE,CACXgP,QAAS,EAAE,AACf,GACM,AAAI/D,MAAM,kBACpB,EAOAlP,EAAcqB,SAAS,CAAC6R,QAAQ,CAAG,WAC/B,MAAM,AAAIhE,MAAM,kBACpB,EAUAlP,EAAcqB,SAAS,CAACwQ,SAAS,CAAG,SAAUT,CAAK,EAE/C,IAAIjL,EAAS,SACb,GAAI,AAAiB,UAAjB,OAAOiL,EAAoB,CAC3B,IAAI+B,EAAcvB,AAHN,IAAI,CAGYe,IAAI,CAAC,GAAGxN,MAAM,CAACiM,IACvCH,EAAgBW,AAJR,IAAI,CAIcX,aAAa,CACvCmC,EAAmBxB,AALX,IAAI,CAKiBe,IAAI,CAACQ,EAClC,CAAA,GACAlC,GACAmC,CAAAA,EAAoBnC,EAAce,IAAI,CAACoB,GACnCA,EAAiBrB,OAAO,CAACd,EAAe,SACxC,EAAE,EAEV,IAAIoC,EAAazI,WAAWwI,EACxB,EAACA,IAAqBC,EAEtBjC,EAAQiC,EAKRlN,EAAS8J,GADO2B,AAnBR,IAAI,CAmBcJ,SAAS,CAACJ,IACS,OAAS,QAE9D,CAKA,MAJqB,UAAjB,OAAOA,GAEPjL,CAAAA,EAASiL,EAAQ,QAAyB,OAAS,QAAO,EAEvDjL,CACX,EAaAnG,EAAcqB,SAAS,CAACoB,EAAE,CAAG,SAAUF,CAAI,CAAE6B,CAAQ,EACjD,OAAO2L,EAAuB,IAAI,CAAExN,EAAM6B,EAC9C,EAOApE,EAAcqB,SAAS,CAACiS,KAAK,CAAG,SAEhCrR,CAAO,EAMH,MALA,IAAI,CAACK,IAAI,CAAC,CACNC,KAAM,aACN0B,QAAS,EAAE,CACXgP,QAAS,EAAE,AACf,GACM,AAAI/D,MAAM,kBACpB,EAWAlP,EAAcqB,SAAS,CAACmQ,SAAS,CAAG,SAAUJ,CAAK,CAAEmC,CAAc,EAC/D,IAII1S,EACA2R,EACAlC,EALArO,EAAU2P,AADE,IAAI,CACI3P,OAAO,CAC3B8Q,EAAaQ,GAAkBtR,EAAQ8Q,UAAU,CACjD5M,EAASsK,IAIb,GAAIxO,EAAQuP,SAAS,CACjBrL,EAASlE,EAAQuP,SAAS,CAACJ,OAE1B,CAED,GAAK2B,EAgBDP,CAAAA,EAASZ,AA5BD,IAAI,CA4BOzB,WAAW,CAAC4C,EAAW,AAAD,GAGrCP,CAAAA,EAASZ,AA/BL,IAAI,CA+BWzB,WAAW,CAAC,aAAa,AAAD,EAE/CG,CAAAA,EAAQc,EAAMd,KAAK,CAACkC,EAAOpC,KAAK,CAAA,GAE5BjK,CAAAA,EAASqM,EAAOnC,MAAM,CAACC,EAAK,OAtBhC,IAAKzP,KAAO+Q,AAbJ,IAAI,CAaUzB,WAAW,CAG7B,GAFAqC,EAASZ,AAdL,IAAI,CAcWzB,WAAW,CAACtP,EAAI,CACnCyP,EAAQc,EAAMd,KAAK,CAACkC,EAAOpC,KAAK,EACrB,CAEP2C,EAAalS,EAGbsF,EAASqM,EAAOnC,MAAM,CAACC,GACvB,KACJ,CAgBJ,CAACA,IAIG,AAAiB,UAAjB,MAHJA,CAAAA,EAAQC,KAAK+C,KAAK,CAAClC,EAAK,GAIpBd,AAAU,OAAVA,GACAA,EAAMkD,OAAO,CACbrN,EAAUmK,EAAMkD,OAAO,GACnBlD,AACI,IADJA,EAAMmD,iBAAiB,GAItBxD,GAAuBK,KAC5BnK,EAASmK,EAAQ,AAAwC,IAAxC,AAAC,IAAIC,KAAKD,GAAQmD,iBAAiB,GAE1B,KAA1BrC,EAAMxF,OAAO,CAAC,SACV,AAAqC,OAArC,AAAC,IAAI2E,KAAKpK,GAASyK,WAAW,IAC9BzK,CAAAA,EAASsK,GAAE,GAI3B,CACA,OAAOtK,CACX,EAaAnG,EAAcqB,SAAS,CAACsR,IAAI,CAAG,SAAUe,CAAG,CAAEC,CAAM,EAQhD,MAPmB,UAAf,OAAOD,IACPA,EAAMA,EAAI3B,OAAO,CAAC,aAAc,IAE5B4B,GAAU,YAAY3B,IAAI,CAAC0B,IAC3BA,CAAAA,EAAMA,EAAI3B,OAAO,CAAC,MAAO,GAAE,GAG5B2B,CACX,EASA1T,EAAc8Q,cAAc,CAAG,CAC3BiC,WAAY,GACZa,kBAAmB,GACnBC,YAAa,EACbC,UAAWvH,OAAOwH,SAAS,CAC3BC,SAAU,EACVC,OAAQ1H,OAAOwH,SAAS,CACxBG,gBAAiB,CAAA,EACjBC,qBAAsB,CAAA,CAC1B,EACOnU,CACX,GAuBIA,EAdOA,EAgERA,IAAkBA,CAAAA,GAAgB,CAAC,CAAA,GAlDpBqE,KAAK,CAAG,CAAC,EA0BvBrE,EAAcsE,YAAY,CAL1B,SAAsBzD,CAAG,CAAEuT,CAAkB,EACzC,MAAQ,CAAC,CAACvT,GACN,CAACb,EAAcqE,KAAK,CAACxD,EAAI,EACzB,CAAC,CAAEb,CAAAA,EAAcqE,KAAK,CAACxD,EAAI,CAAGuT,CAAiB,CACvD,EAwBApU,EAAcqU,mBAAmB,CATjC,SAA6BpQ,CAAO,CAAEgP,CAAO,EACzB,KAAK,IAAjBhP,GAAsBA,CAAAA,EAAU,EAAE,AAAD,EACrB,KAAK,IAAjBgP,GAAsBA,CAAAA,EAAU,EAAE,AAAD,EAErC,IAAK,IADD7P,EAAQ,IA35B+B+F,EA45BlCpE,EAAI,EAAGwE,EAAOvC,KAAKC,GAAG,CAACgM,EAAQvQ,MAAM,CAAEuB,EAAQvB,MAAM,EAAGqC,EAAIwE,EAAM,EAAExE,EACzE3B,EAAM0E,SAAS,CAACmL,CAAO,CAAClO,EAAE,EAAI,GAAGI,MAAM,CAACJ,GAAId,CAAO,CAACc,EAAE,EAE1D,OAAO3B,CACX,EAQyB,IAAIkR,GAA4BtU,GA6BzDuU,GAA4B,WAM5B,SAASA,EAAWC,CAAQ,EACP,KAAK,IAAlBA,GAAuBA,CAAAA,EAAW,CAAC,CAAA,EACvC,IAAI,CAACC,gBAAgB,CAAG,EAAE,CAC1B,IAAI,CAACC,WAAW,CAAG,CAAC,EACpB,IAAI,CAACF,QAAQ,CAAGA,CACpB,CAyMA,OAzKAD,EAAWlT,SAAS,CAACsT,WAAW,CAAG,SAAUC,CAAO,CAAEC,CAAK,CAAEC,CAAQ,EACjE,IAAIJ,EAAc,IAAI,CAACA,WAAW,CAACE,EAAQ,CAAI,IAAI,CAACF,WAAW,CAACE,EAAQ,EAChE,CAAC,EAIT,MADAG,AAFgBL,CAAAA,CAAW,CAACG,EAAM,CAAIH,CAAW,CAACG,EAAM,EAChD,EAAE,EACA5R,IAAI,CAAC6R,GACR,IAAI,AACf,EAIAP,EAAWlT,SAAS,CAAC2T,gBAAgB,CAAG,SAAU9R,CAAC,EAC/C,MAAO,AAACA,CAAAA,AAAkB,aAAlBA,EAAE+R,MAAM,CAAC1S,IAAI,CACjB,CACIW,EAAEE,KAAK,CAACyD,EAAE,CACV3D,EAAE+R,MAAM,CAAC5P,MAAM,CACfnC,EAAE+R,MAAM,CAAC/M,GAAG,CACZhF,EAAE+R,MAAM,CAACJ,KAAK,CACd3R,EAAE+R,MAAM,CAAC1S,IAAI,CAChB,CACD,CACIW,EAAEE,KAAK,CAACyD,EAAE,CACV3D,EAAE+R,MAAM,CAAChR,OAAO,CAChBf,EAAE+R,MAAM,CAACC,QAAQ,CACjBhS,EAAE+R,MAAM,CAACE,OAAO,CAChBjS,EAAE+R,MAAM,CAACJ,KAAK,CACd3R,EAAE+R,MAAM,CAAC1S,IAAI,CAChB,AAAD,EAAGuQ,IAAI,CAAC,KAChB,EA+BAyB,EAAWlT,SAAS,CAAC+T,UAAU,CAAG,SAAUhS,CAAK,CAAE6R,CAAM,CAAEI,CAAK,CAAEC,CAAO,EAErE,IADIzN,EACA+M,EAAUxR,EAAMyD,EAAE,CAClBgO,EAAQI,EAAOJ,KAAK,CACpBE,EAAa,IAAI,CAACL,WAAW,CAACE,EAAQ,EAClC,IAAI,CAACF,WAAW,CAACE,EAAQ,CAACC,EAAM,CACxC,GAAIE,EAAW,CACX,IAAIP,EAAW,IAAI,CAACA,QAAQ,CAACI,EAAQ,CAAI,AAAkC,OAAjC/M,CAAAA,EAAK,IAAI,CAAC2M,QAAQ,CAACI,EAAQ,AAAD,GAAe/M,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAAC,EACrG0N,EAAUf,CAAQ,CAACS,EAAOJ,KAAK,CAAC,EAAI,EAAE,CACtCS,IACKC,EAAQ7S,MAAM,EACf8R,CAAAA,CAAQ,CAACS,EAAOJ,KAAK,CAAC,CAAGU,CAAM,EAEU,KAAzChB,EAAWiB,QAAQ,CAACP,EAAQM,IAC5BA,EAAQtS,IAAI,CAACgS,IAGrB,IAAI/R,EAAI,CACA+R,OAAQA,EACRM,QAASA,EACTnS,MAAOA,CACX,EACAiS,GACAnS,CAAAA,EAAEmS,KAAK,CAAGA,CAAI,EAElB,IAAIZ,EAAmB,IAAI,CAACA,gBAAgB,CACxCgB,EAAc,IAAI,CAACT,gBAAgB,CAAC9R,GACxC,GAAIuR,EAAiB7I,OAAO,CAAC6J,IAAgB,EAEzC,OAAO,IAAI,CAEf,GAAI,CACA,IAAI,CAAChB,gBAAgB,CAACxR,IAAI,CAACwS,GAC3B,IAAK,IAAI1Q,EAAI,EAAGwE,EAAOwL,EAAUrS,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACjDgQ,CAAS,CAAChQ,EAAE,CAACxD,IAAI,CAAC,IAAI,CAAE2B,EAEhC,QACQ,CACJ,IAAI2L,EAAQ,IAAI,CAAC4F,gBAAgB,CAAC7I,OAAO,CAAC6J,GACtC5G,GAAS,GACT,IAAI,CAAC4F,gBAAgB,CAACjP,MAAM,CAACqJ,EAAO,EAE5C,CACJ,CACA,OAAO,IAAI,AACf,EAeA0F,EAAWlT,SAAS,CAACqU,WAAW,CAAG,SAAUd,CAAO,CAAEK,CAAM,EACxD,IAAIM,EAAW,IAAI,CAACf,QAAQ,CAACI,EAAQ,EAC7B,IAAI,CAACJ,QAAQ,CAACI,EAAQ,CAACK,EAAOJ,KAAK,CAAC,CAC5C,GAAIU,EAAS,CACT,IAAI1G,EAAQ0F,EAAWiB,QAAQ,CAACP,EAC5BM,GACA1G,GAAS,GACT0G,EAAQ/P,MAAM,CAACqJ,EAAO,EAE9B,CACA,OAAO,IAAI,AACf,EAkBA0F,EAAWlT,SAAS,CAACsU,cAAc,CAAG,SAAUf,CAAO,CAAEC,CAAK,CAAEC,CAAQ,EACpE,IAAIC,EAAa,IAAI,CAACL,WAAW,CAACE,EAAQ,EAClC,IAAI,CAACF,WAAW,CAACE,EAAQ,CAACC,EAAM,CACxC,GAAIE,EAAW,CACX,IAAIlG,EAAQkG,EAAUnJ,OAAO,CAACkJ,GAC1BjG,GAAS,GACTkG,EAAUvP,MAAM,CAACqJ,EAAO,EAEhC,CACA,OAAO,IAAI,AACf,EAUA0F,EAAW5G,OAAO,CAAG,QACd4G,CACX,KASA,AAAC,SAAUA,CAAU,EAoHjB,SAASqB,EAAQX,CAAM,CAAEY,CAAY,EAKjC,GAAIZ,AAAgB,UAAhBA,EAAO1S,IAAI,CACX,OAAO0S,EAEX,IAPIpN,EACAiO,EACAC,EACAC,EAIAC,EAAQ,CACJ1T,KAAM,QACN2S,SAAW,AAAsG,OAArGY,CAAAA,EAAK,AAAsB,OAArBjO,CAAAA,EAAKoN,EAAO/M,GAAG,AAAD,GAAeL,AAAO,KAAK,IAAZA,EAAgBA,EAAMgO,GAAgBA,EAAaX,QAAQ,GAAeY,AAAO,KAAK,IAAZA,EAAgBA,EAAK,EAC9IX,QAAU,AAAqG,OAApGa,CAAAA,EAAK,AAAsB,OAArBD,CAAAA,EAAKd,EAAO/M,GAAG,AAAD,GAAe6N,AAAO,KAAK,IAAZA,EAAgBA,EAAMF,GAAgBA,EAAaV,OAAO,GAAea,AAAO,KAAK,IAAZA,EAAgBA,EAAKzJ,OAAOwH,SAAS,CAC5Jc,MAAOI,EAAOJ,KAAK,AACvB,EAIJ,OAH6B,KAAA,IAAlBI,EAAO5P,MAAM,EACpB4Q,CAAAA,EAAMhS,OAAO,CAAG,CAACgR,EAAO5P,MAAM,CAAC,AAAD,EAE3B4Q,CACX,CA5FA1B,EAAWiB,QAAQ,CA3BnB,SAAkBU,CAAM,CAAEX,CAAO,EAC7B,GAAIW,AAAgB,aAAhBA,EAAO3T,IAAI,CACX,CAAA,IAAK,IAAI0S,EAAS,KAAK,EAAGlQ,EAAI,EAAGwE,EAAOgM,EAAQ7S,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAEhE,GAAIkQ,AAAgB,aAAhBA,AADJA,CAAAA,EAASM,CAAO,CAACxQ,EAAE,AAAD,EACPxC,IAAI,EACX0S,EAAOJ,KAAK,GAAKqB,EAAOrB,KAAK,EAC7BI,EAAO5P,MAAM,GAAK6Q,EAAO7Q,MAAM,EAC/B4P,EAAO/M,GAAG,GAAKgO,EAAOhO,GAAG,CACzB,OAAOnD,CAEf,MAIA,IAAK,IADDoR,EAAeC,KAAKC,SAAS,CAACH,EAAOjS,OAAO,EACvCgR,EAAS,KAAK,EAAGlQ,EAAI,EAAGwE,EAAOgM,EAAQ7S,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAEhE,GAAIkQ,AAAgB,UAAhBA,AADJA,CAAAA,EAASM,CAAO,CAACxQ,EAAE,AAAD,EACPxC,IAAI,EACX0S,EAAOJ,KAAK,GAAKqB,EAAOrB,KAAK,EAC7BI,EAAOC,QAAQ,GAAKgB,EAAOhB,QAAQ,EACnCD,EAAOE,OAAO,GAAKe,EAAOf,OAAO,EACjCiB,KAAKC,SAAS,CAACpB,EAAOhR,OAAO,IAAMkS,EACnC,OAAOpR,EAInB,OAAO,EACX,EAoBAwP,EAAW+B,OAAO,CAdlB,SAAiBC,CAAO,CAAEC,CAAO,QAC7B,AAAID,AAAiB,aAAjBA,EAAQhU,IAAI,EAAmBiU,AAAiB,aAAjBA,EAAQjU,IAAI,CACnCgU,EAAQlR,MAAM,GAAKmR,EAAQnR,MAAM,EACrCkR,EAAQrO,GAAG,GAAKsO,EAAQtO,GAAG,EAC3BqO,EAAQ1B,KAAK,GAAK2B,EAAQ3B,KAAK,CAElB,UAAjB0B,EAAQhU,IAAI,EAAgBiU,AAAiB,UAAjBA,EAAQjU,IAAI,EAChCgU,EAAQrB,QAAQ,GAAKsB,EAAQtB,QAAQ,EACzCqB,EAAQpB,OAAO,GAAKqB,EAAQrB,OAAO,EAClCiB,KAAKC,SAAS,CAACE,EAAQtS,OAAO,IAC3BmS,KAAKC,SAAS,CAACG,EAAQvS,OAAO,CAG9C,EAqBAsQ,EAAWkC,SAAS,CAfpB,SAAmBP,CAAM,CAAED,CAAK,EACT,aAAfA,EAAM1T,IAAI,EACV0T,CAAAA,EAAQL,EAAQK,EAAK,EAEL,aAAhBC,EAAO3T,IAAI,EACX2T,CAAAA,EAASN,EAAQM,EAAQD,EAAK,EAElC,IAAIS,EAAgBR,EAAOjS,OAAO,CAC9B0S,EAAeV,EAAMhS,OAAO,CAChC,OAAQiS,EAAOhB,QAAQ,EAAIe,EAAMf,QAAQ,EACrCgB,EAAOf,OAAO,EAAIc,EAAMd,OAAO,EAC9B,CAAA,CAACuB,GACE,CAACC,GACDD,EAAcE,KAAK,CAAC,SAAUvR,CAAM,EAAI,OAAOsR,EAAa/K,OAAO,CAACvG,IAAW,CAAG,EAAC,CAC/F,EAgCAkP,EAAWsC,WAAW,CA3BtB,SAAqB5B,CAAM,EACvB,GAAIA,AAAgB,aAAhBA,EAAO1S,IAAI,CACX,MAAO,CAAC0S,EAAO,CAKnB,IAAK,IAHDhR,EAAWgR,EAAOhR,OAAO,EAAI,EAAE,CAC/B6S,EAAY,EAAE,CACdjC,EAAQI,EAAOJ,KAAK,CACf3M,EAAM+M,EAAOC,QAAQ,CAAE6B,EAAS9B,EAAOE,OAAO,CAAEjN,EAAM6O,EAAQ,EAAE7O,EAAK,CAC1E,GAAI,CAACjE,EAAQvB,MAAM,CAAE,CACjBoU,EAAU7T,IAAI,CAAC,CACXV,KAAM,WACN2F,IAAKA,EACL2M,MAAOA,CACX,GACA,QACJ,CACA,IAAK,IAAIxP,EAAS,EAAG2R,EAAY/S,EAAQvB,MAAM,CAAE2C,EAAS2R,EAAW,EAAE3R,EACnEyR,EAAU7T,IAAI,CAAC,CACXV,KAAM,WACN8C,OAAQpB,CAAO,CAACoB,EAAO,CACvB6C,IAAKA,EACL2M,MAAOA,CACX,EAER,CACA,OAAOiC,CACX,EAwBAvC,EAAWqB,OAAO,CAAGA,CACzB,EAAGrB,IAAeA,CAAAA,GAAa,CAAC,CAAA,GAMH,IAAI0C,GAAmB1C,GA6BnB2C,GARV,CACnBC,WAAY,EAAE,AAClB,EAuCIC,GAA0B,WAM1B,SAASA,EAASnV,CAAO,EACL,KAAK,IAAjBA,GAAsBA,CAAAA,EAAUiV,EAAoB,EACxDjV,EAAQkV,UAAU,CAAIlV,EAAQkV,UAAU,EAAI,EAAE,CAC9C,IAAI,CAACA,UAAU,CAAG,CAAC,EACnB,IAAI,CAAClV,OAAO,CAAGA,EACf,IAAI,CAACoV,OAAO,CAAG,CAAC,CACpB,CA2NA,OA7MAD,EAAS/V,SAAS,CAACiB,IAAI,CAAG,SAAUY,CAAC,EACjCvB,IAA8EE,SAAS,CAAC,IAAI,CAAEqB,EAAEX,IAAI,CAAEW,EAC1G,EAYAkU,EAAS/V,SAAS,CAACiW,YAAY,CAAG,SAAUC,CAAW,EACnD,IAAI5Q,EAAQ,IAAI,CACZ2H,EAAY,IAAI,CAAC6I,UAAU,CAACI,EAAY,CAE5C,GAAIjJ,EACA,OAAOhL,QAAQC,OAAO,CAAC+K,GAE3B,IAAIkJ,EAAc,IAAI,CAACH,OAAO,CAACE,EAAY,CAE3C,GAAI,CAACC,EAAa,CACdA,EAAc,IAAI,CAACH,OAAO,CAACE,EAAY,CAAG,EAAE,CAC5C,IAAIE,EAAmB,IAAI,CAACC,mBAAmB,CAACH,GAChD,GAAI,CAACE,EACD,MAAM,AAAIvI,MAAM,cAAc/J,MAAM,CAACoS,EAAa,iBAGtD,IAAI,CACCI,aAAa,CAACF,GACdrK,IAAI,CAAC,SAAUkB,CAAS,EACzB,OAAO3H,EAAM0Q,OAAO,CAACE,EAAY,CACjC,IAAK,IAAIxS,EAAI,EAAGwE,EAAOiO,EAAY9U,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACnDyS,CAAW,CAACzS,EAAE,CAAC,EAAE,CAACuJ,EAE1B,GAAG,KAAQ,CAAC,SAAUjB,CAAK,EACvB,OAAO1G,EAAM0Q,OAAO,CAACE,EAAY,CACjC,IAAK,IAAIxS,EAAI,EAAGwE,EAAOiO,EAAY9U,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACnDyS,CAAW,CAACzS,EAAE,CAAC,EAAE,CAACsI,EAE1B,EACJ,CAEA,OAAO,IAAI/J,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACxCgU,EAAYvU,IAAI,CAAC,CAACM,EAASC,EAAO,CACtC,EACJ,EASA4T,EAAS/V,SAAS,CAACuW,eAAe,CAAG,WAGjC,IAAK,IAFDT,EAAa,IAAI,CAAClV,OAAO,CAACkV,UAAU,CACpCU,EAAe,EAAE,CACZ9S,EAAI,EAAGwE,EAAO4N,EAAWzU,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAClD8S,EAAa5U,IAAI,CAACkU,CAAU,CAACpS,EAAE,CAAC8B,EAAE,EAEtC,OAAOgR,CACX,EAYAT,EAAS/V,SAAS,CAACqW,mBAAmB,CAAG,SAAUH,CAAW,EAE1D,IAAK,IADDJ,EAAa,IAAI,CAAClV,OAAO,CAACkV,UAAU,CAC/BpS,EAAI,EAAGwE,EAAO4N,EAAWzU,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAClD,GAAIoS,CAAU,CAACpS,EAAE,CAAC8B,EAAE,GAAK0Q,EACrB,OAAOJ,CAAU,CAACpS,EAAE,AAGhC,EAYAqS,EAAS/V,SAAS,CAACyW,iBAAiB,CAAG,SAAUP,CAAW,EACxD,OAAO,IAAI,CACND,YAAY,CAACC,GACbnK,IAAI,CAAC,SAAUkB,CAAS,EAAI,OAAOA,EAAUlL,KAAK,AAAE,EAC7D,EAWAgU,EAAS/V,SAAS,CAAC0W,cAAc,CAAG,SAAUR,CAAW,EACrD,MAAO,CAAC,IAAI,CAACJ,UAAU,CAACI,EAAY,AACxC,EAYAH,EAAS/V,SAAS,CAACsW,aAAa,CAAG,SAAU1V,CAAO,EAChD,IAAI0E,EAAQ,IAAI,CAChB,OAAO,IAAIrD,QAAQ,SAAUC,CAAO,CAAEC,CAAM,EACxCmD,EAAMrE,IAAI,CAAC,CACPC,KAAM,OACNN,QAASA,CACb,GACA,IAAI+V,EAAiBlI,EAAyBzL,KAAK,CAACpC,EAAQM,IAAI,CAAC,CACjE,GAAI,CAACyV,EACD,MAAM,AAAI9I,MAAM,8BAA8B/J,MAAM,CAAClD,EAAQM,IAAI,CAAE,MAIvE+L,AAFgB,IAAI0J,EAAe/V,EAAQA,OAAO,EAG7C+M,IAAI,GACJ5B,IAAI,CAAC,SAAUkB,CAAS,EACzB3H,EAAMwQ,UAAU,CAAClV,EAAQ4E,EAAE,CAAC,CAAGyH,EAC/B3H,EAAMrE,IAAI,CAAC,CACPC,KAAM,YACNN,QAASA,CACb,GACAsB,EAAQ+K,EACZ,GAAG,KAAQ,CAAC9K,EAChB,EACJ,EAeA4T,EAAS/V,SAAS,CAACoB,EAAE,CAAG,SAAUF,CAAI,CAAE6B,CAAQ,EAC5C,OAAOzC,IAA8EC,QAAQ,CAAC,IAAI,CAAEW,EAAM6B,EAC9G,EAOAgT,EAAS/V,SAAS,CAAC4W,mBAAmB,CAAG,SAAUhW,CAAO,EACtD,IAAIkV,EAAa,IAAI,CAAClV,OAAO,CAACkV,UAAU,CACpCe,EAAY,IAAI,CAACf,UAAU,CAC/B,IAAI,CAAC7U,IAAI,CAAC,CACNC,KAAM,sBACNN,QAASA,CACb,GACA,IAAK,IAAI8C,EAAI,EAAGwE,EAAO4N,EAAWzU,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAClD,GAAIoS,CAAU,CAACpS,EAAE,CAAC8B,EAAE,GAAK5E,EAAQ4E,EAAE,CAAE,CACjCsQ,EAAW3R,MAAM,CAACT,EAAG,GACrB,KACJ,CAEAmT,CAAS,CAACjW,EAAQ4E,EAAE,CAAC,GACrBqR,CAAS,CAACjW,EAAQ4E,EAAE,CAAC,CAAC8I,WAAW,GACjC,OAAOuI,CAAS,CAACjW,EAAQ4E,EAAE,CAAC,EAEhCsQ,EAAWlU,IAAI,CAAChB,GAChB,IAAI,CAACK,IAAI,CAAC,CACNC,KAAM,2BACNN,QAASA,CACb,EACJ,EAUAmV,EAASzJ,OAAO,CAAG,QACZyJ,CACX,IA8BIe,GAAgB,kBAKhBC,GAAiB,qCAKjBC,GAAiB,oCAKjBC,GAAiB,uBAIjBC,GAAiB,0BAQjBC,GAAgB,4CAQhBC,GAAkB,8DAMlBC,GAAoB,gCAMpBC,GAAsB,sCAiB1B,SAASC,GAAmBC,CAAI,EAE5B,IAAK,IADDC,EAAkB,EACb/T,EAAI,EAAGwE,EAAOsP,EAAKnW,MAAM,CAAEqW,EAAO,KAAK,EAAGC,EAAkB,EAAGjU,EAAIwE,EAAM,EAAExE,EAAG,CAEnF,GAAIgU,AAAS,MADbA,CAAAA,EAAOF,CAAI,CAAC9T,EAAE,AAAD,EACK,CACT+T,GACDE,CAAAA,EAAkBjU,EAAI,CAAA,EAE1B,EAAE+T,EACF,QACJ,CACA,GAAIC,AAAS,MAATA,GAEI,GAACD,EACD,OAAOD,EAAKI,SAAS,CAACD,EAAiBjU,EAGnD,CACA,GAAI+T,EAAkB,EAAG,CACrB,IAAIzL,EAAQ,AAAI6B,MAAM,0BAEtB,OADA7B,EAAMe,IAAI,CAAG,oBACPf,CACV,CACA,MAAO,EACX,CAYA,SAAS6L,GAAcL,CAAI,EAEvB,IAAK,IADDpT,EAAQ,GACHV,EAAI,EAAGwE,EAAOsP,EAAKnW,MAAM,CAAEqW,EAAO,KAAK,EAAGI,EAAW,CAAA,EAAOpU,EAAIwE,EAAM,EAAExE,EAAG,CAEhF,GAAIgU,AAAS,OADbA,CAAAA,EAAOF,CAAI,CAAC9T,EAAE,AAAD,EACM,CACfoU,EAAW,CAACA,EACZ,QACJ,CACA,GAAIA,EAAU,CACVA,EAAW,CAAA,EACX,QACJ,CACA,GAAIJ,AAAS,MAATA,EAAc,CACd,IAAItT,CAAAA,EAAQ,CAAA,EAIR,OAAOoT,EAAKI,SAAS,CAACxT,EAAQ,EAAGV,GAHjCU,EAAQV,CAKhB,CACJ,CAnBA,IAoBIsI,EAAQ,AAAI6B,MAAM,qBAEtB,OADA7B,EAAMe,IAAI,CAAG,oBACPf,CACV,CAgBA,SAAS+L,GAAcP,CAAI,CAAEQ,CAAqB,EAI9C,GADA/I,EAAQuI,EAAKvI,KAAK,CAACmI,IACR,CACP,IAJAnI,EAIIgJ,EAAuBhJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACrDiJ,EAAoBjJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAClDkJ,EAAqBlJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACnDmJ,EAAkBnJ,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAChD2F,EAAQ,CACJ1T,KAAM,QACNmX,YAAcJ,EACVzG,SAASvC,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CpG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7BqJ,SAAWJ,EACP1G,SAASvC,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CpG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7BwD,UAAY0F,EACR3G,SAASvC,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CpG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7B2D,OAASwF,EACL5G,SAASvC,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CpG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,CACjC,EAaJ,OAZIgJ,GACArD,CAAAA,EAAMqD,mBAAmB,CAAG,CAAA,CAAG,EAE/BC,GACAtD,CAAAA,EAAMsD,gBAAgB,CAAG,CAAA,CAAG,EAE5BC,GACAvD,CAAAA,EAAMuD,iBAAiB,CAAG,CAAA,CAAG,EAE7BC,GACAxD,CAAAA,EAAMwD,cAAc,CAAG,CAAA,CAAG,EAEvBxD,CACX,CAGA,GADA3F,EAAQuI,EAAKvI,KAAK,CAACkI,IACR,CACP,IAAIc,EAAsBhJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACjCiJ,EAAmBjJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC9BkJ,EAAoBlJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC/BmJ,EAAiBnJ,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC5B2F,EAAQ,CACJ1T,KAAM,QACNmX,YAAaE,GAAqBN,EAC9BhJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,IAAM,EAC7BU,SAAU9G,SAAS0G,EACfjJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,GAAI,IAAM,EACjCnF,UAAW8F,GAAqBJ,EAC5BlJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,IAAM,EAC7BhF,OAAQpB,SAAS4G,EACbnJ,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,GAAI,IAAM,CACrC,EAaJ,OAZIK,GACArD,CAAAA,EAAMqD,mBAAmB,CAAG,CAAA,CAAG,EAE/BC,GACAtD,CAAAA,EAAMsD,gBAAgB,CAAG,CAAA,CAAG,EAE5BC,GACAvD,CAAAA,EAAMuD,iBAAiB,CAAG,CAAA,CAAG,EAE7BC,GACAxD,CAAAA,EAAMwD,cAAc,CAAG,CAAA,CAAG,EAEvBxD,CACX,CAEA,IAAI4D,EAAUC,GAAajB,EACvBQ,GACJ,OAAQQ,AAAmB,IAAnBA,EAAQnX,MAAM,EAAU,AAAsB,UAAtB,OAAOmX,CAAO,CAAC,EAAE,CAC7CA,CAAO,CAAC,EAAE,CACVA,CACR,CAsEA,SAASC,GAAajB,CAAI,CAAEQ,CAAqB,EAO7C,IANA,IAII/I,EAJAW,EAAiBoI,EACbhB,GACAD,GACJyB,EAAU,EAAE,CAEZE,EAAO,AAAClB,CAAAA,AAAY,MAAZA,CAAI,CAAC,EAAE,CAAWA,EAAKI,SAAS,CAAC,GAAKJ,CAAG,EAAGlG,IAAI,GACrDoH,GAAM,CAGT,GADAzJ,EAAQyJ,EAAKzJ,KAAK,CAACqI,IACR,CACP,IAAIqB,EAAkB1J,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAChD2J,EAAe3J,AAAa,KAAbA,CAAK,CAAC,EAAE,EAAWA,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC7C4J,EAAY,CACR3X,KAAM,YACN8C,OAAS2U,EACLnH,SAASvC,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CpG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,EAC7BpI,IAAM+R,EACFpH,SAASvC,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,EAAG,KAAO,IAAK,IAC3CpG,SAASvC,CAAK,CAAC,EAAE,CAAE,IAAM,CACjC,EACA0J,GACAE,CAAAA,EAAUF,cAAc,CAAG,CAAA,CAAG,EAE9BC,GACAC,CAAAA,EAAUD,WAAW,CAAG,CAAA,CAAG,EAE/BJ,EAAQ5W,IAAI,CAACiX,GACbH,EAAOA,EAAKd,SAAS,CAAC3I,CAAK,CAAC,EAAE,CAAC5N,MAAM,EAAEiQ,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQyJ,EAAKzJ,KAAK,CAACoI,IACR,CACP,IAAIsB,EAAiB1J,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CAC5B2J,EAAc3J,AAAgB,MAAhBA,CAAK,CAAC,EAAE,CAAC,EAAE,CACzB4J,EAAY,CACR3X,KAAM,YACN8C,OAAQuU,GAAqBI,EACzB1J,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,IAAM,EAC7B/Q,IAAK2K,SAASoH,EACV3J,CAAK,CAAC,EAAE,CACRA,CAAK,CAAC,EAAE,CAAC2I,SAAS,CAAC,GAAI,IAAM,CACrC,EACAe,GACAE,CAAAA,EAAUF,cAAc,CAAG,CAAA,CAAG,EAE9BC,GACAC,CAAAA,EAAUD,WAAW,CAAG,CAAA,CAAG,EAE/BJ,EAAQ5W,IAAI,CAACiX,GACbH,EAAOA,EAAKd,SAAS,CAAC3I,CAAK,CAAC,EAAE,CAAC5N,MAAM,EAAEiQ,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQyJ,EAAKzJ,KAAK,CAACiI,IACR,CACPsB,EAAQ5W,IAAI,CAACqN,CAAK,CAAC,EAAE,EACrByJ,EAAOA,EAAKd,SAAS,CAAC3I,CAAK,CAAC,EAAE,CAAC5N,MAAM,EAAEiQ,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQyJ,EAAKzJ,KAAK,CAAC6H,IACR,CACP0B,EAAQ5W,IAAI,CAACqN,AAAa,SAAbA,CAAK,CAAC,EAAE,EACrByJ,EAAOA,EAAKd,SAAS,CAAC3I,CAAK,CAAC,EAAE,CAAC5N,MAAM,EAAEiQ,IAAI,GAC3C,QACJ,CAGA,GADArC,EAAQyJ,EAAKzJ,KAAK,CAACW,GACR,CACP4I,EAAQ5W,IAAI,CAAC2H,WAAW0F,CAAK,CAAC,EAAE,GAChCyJ,EAAOA,EAAKd,SAAS,CAAC3I,CAAK,CAAC,EAAE,CAAC5N,MAAM,EAAEiQ,IAAI,GAC3C,QACJ,CAEA,GAAIoH,AAAY,MAAZA,CAAI,CAAC,EAAE,CAAU,CACjB,IAAII,EAASjB,GAAca,GAC3BF,EAAQ5W,IAAI,CAACkX,EAAOlB,SAAS,CAAC,EAAG,KACjCc,EAAOA,EAAKd,SAAS,CAACkB,EAAOzX,MAAM,CAAG,GAAGiQ,IAAI,GAC7C,QACJ,CAGA,GADArC,EAAQyJ,EAAKzJ,KAAK,CAACgI,IACR,CAEP,IAAI8B,EAAcxB,GADlBmB,EAAOA,EAAKd,SAAS,CAAC3I,CAAK,CAAC,EAAE,CAAC5N,MAAM,EAAEiQ,IAAI,IAE3CkH,EAAQ5W,IAAI,CAAC,CACTV,KAAM,WACN6L,KAAMkC,CAAK,CAAC,EAAE,CACd+J,KAAMC,AAnJtB,SAAwBzB,CAAI,CAAEQ,CAAqB,EAI/C,IAAK,IAHDgB,EAAO,EAAE,CAAEE,EAAsBlB,EAAwB,IAAM,IAC/DP,EAAkB,EAClB0B,EAAO,GACFzV,EAAI,EAAGwE,EAAOsP,EAAKnW,MAAM,CAAEqW,EAAO,KAAK,EAAGhU,EAAIwE,EAAM,EAAExE,EAG3D,GAAIgU,AAFJA,CAAAA,EAAOF,CAAI,CAAC9T,EAAE,AAAD,IAEAwV,GACT,CAACzB,GACD0B,EACAH,EAAKpX,IAAI,CAACmW,GAAcoB,EAAMnB,IAC9BmB,EAAO,QAGN,GAAIzB,AAAS,MAATA,GACJD,GACA0B,EAMa,MAATzB,IACLyB,GAAQzB,EACJA,AAAS,MAATA,EACA,EAAED,EAEY,MAATC,GACL,EAAED,OAZC,CACP,IAAIqB,EAASjB,GAAcL,EAAKI,SAAS,CAAClU,IAC1CsV,EAAKpX,IAAI,CAACkX,GACVpV,GAAKoV,EAAOzX,MAAM,CAAG,CAEzB,CAeJ,MAHI,CAACoW,GAAmB0B,GACpBH,EAAKpX,IAAI,CAACmW,GAAcoB,EAAMnB,IAE3BgB,CACX,EA8GqCD,EAAaf,EACtC,GACAU,EAAOA,EAAKd,SAAS,CAACmB,EAAY1X,MAAM,CAAG,GAAGiQ,IAAI,GAClD,QACJ,CAEA,GAAIoH,AAAY,MAAZA,CAAI,CAAC,EAAE,CAAU,CACjB,IAAIU,EAAa7B,GAAmBmB,GACpC,GAAIU,EAAY,CACZZ,EACK5W,IAAI,CAAC6W,GAAaW,EAAYpB,IACnCU,EAAOA,EAAKd,SAAS,CAACwB,EAAW/X,MAAM,CAAG,GAAGiQ,IAAI,GACjD,QACJ,CACJ,CAEA,IAAI+H,EAAW7B,EAAKnW,MAAM,CAAGqX,EAAKrX,MAAM,CAAE2K,EAAQ,AAAI6B,MAAM,yBACpD2J,EAAKI,SAAS,CAACyB,EAAUA,EAAW,GACpC,iBAAoBA,CAAAA,EAAW,CAAA,EAC/B,UAAY7B,EAAKI,SAAS,CAACyB,EAAW,EAAGA,EAAW,GAAK,QAEjE,OADArN,EAAMe,IAAI,CAAG,oBACPf,CACV,CACA,OAAOwM,CACX,CAaA,SAASD,GAAqBf,CAAI,EAE9B,IAAK,IADDxT,EAAS,EACJN,EAAI,EAAGwE,EAAOsP,EAAKnW,MAAM,CAAEiY,EAAO,KAAK,EAAGC,EAAS/B,EAAKnW,MAAM,CAAG,EAAGqC,EAAIwE,EAAM,EAAExE,EACrF4V,CAAAA,EAAO9B,EAAKgC,UAAU,CAAC9V,EAAC,GACZ,IAAM4V,GAAQ,IACtBtV,CAAAA,GAAU,AAACsV,CAAAA,EAAO,EAAC,EAAK3T,KAAK8T,GAAG,CAAC,GAAIF,EAAM,EAE/C,EAAEA,EAEN,OAAOvV,CACX,CAS6B,IAAI0V,GAHb,CAChBjB,aAAcA,EAClB,EA0BIkB,GAAY,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,IAAK,KAAK,CAgHnCC,GARf,CACdC,UAxFJ,SAAmBC,CAAI,EACnB,OAAOA,aAAgBlW,KAC3B,EAuFImW,WA3EJ,SAAoBD,CAAI,EACpB,MAAQ,AAAgB,UAAhB,OAAOA,GACX,CAAEA,CAAAA,aAAgBlW,KAAI,GACtBkW,AAAc,aAAdA,EAAK5Y,IAAI,AACjB,EAwEI8Y,WA5DJ,SAAoBF,CAAI,EACpB,MAAQ,AAAgB,UAAhB,OAAOA,GACXH,GAAUpP,OAAO,CAACuP,IAAS,CACnC,EA0DIG,QA9CJ,SAAiBH,CAAI,EACjB,MAAQ,AAAgB,UAAhB,OAAOA,GACX,CAAEA,CAAAA,aAAgBlW,KAAI,GACtBkW,AAAc,UAAdA,EAAK5Y,IAAI,AACjB,EA2CIgZ,YA/BJ,SAAqBJ,CAAI,EACrB,MAAQ,AAAgB,UAAhB,OAAOA,GACX,CAAEA,CAAAA,aAAgBlW,KAAI,GACtBkW,AAAc,cAAdA,EAAK5Y,IAAI,AACjB,EA4BIiZ,QAhBJ,SAAiBL,CAAI,EACjB,MAAQ,AAAgB,WAAhB,OAAOA,GACX,AAAgB,UAAhB,OAAOA,GACP,AAAgB,UAAhB,OAAOA,CACf,CAaA,EAkBIM,GAA6BR,GAAaC,SAAS,CAAEQ,GAA8BT,GAAaG,UAAU,CAAEO,GAA8BV,GAAaI,UAAU,CAAEO,GAA2BX,GAAaK,OAAO,CAAEO,GAA+BZ,GAAaM,WAAW,CAAEO,GAA2Bb,GAAaO,OAAO,CAM5TO,GAAwB,KACxBC,GAAYzP,OAAOwH,SAAS,CAAG,eAC/BkI,GAAa1P,OAAOwH,SAAS,CAAG,eAChCmI,GAAW3P,OAAOwH,SAAS,CAC3BoI,GAAmB,CACnB,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,IAAK,EACL,KAAM,EACN,IAAK,EACL,KAAM,CACV,EACIC,GAAqB,CAAC,EACtBC,GAA8B,kBAelC,SAASC,GAAgBlL,CAAK,EAC1B,OAAQ,OAAOA,GACX,IAAK,UACD,OAAOA,EAAQ8K,GAAWF,EAC9B,KAAK,SACD,OAAOC,EACX,KAAK,SACD,OAAO7K,CACX,SACI,OAAOX,GACf,CACJ,CAYA,SAAS8L,GAAgBnL,CAAK,QAC1B,AAAI,AAAiB,UAAjB,OAAOA,EACAA,EAAMoL,WAAW,GAAGzK,OAAO,CAACgK,GAAuB,MAEvD3K,CACX,CAUA,SAASC,GAASD,CAAK,EACnB,OAAQ,OAAOA,GACX,IAAK,UACD,MAAOA,GAAAA,CACX,KAAK,SACD,OAAOxG,WAAWwG,EAAMW,OAAO,CAAC,IAAK,KACzC,KAAK,SACD,OAAOX,CACX,SACI,OAAOX,GACf,CACJ,CAkBA,SAASgM,GAAeC,CAAQ,CAAEC,CAAC,CAAEC,CAAC,MA2B9BzW,EA1BJ,OAAQuW,GACJ,IAAK,IACD,OAAOH,GAAgBI,KAAOJ,GAAgBK,EAClD,KAAK,IACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,GAAKJ,GAAgBK,GAEhD,OAAON,GAAgBK,GAAKL,GAAgBM,EAChD,KAAK,KACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,IAAMJ,GAAgBK,GAEjD,OAAON,GAAgBK,IAAML,GAAgBM,EACjD,KAAK,IACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,GAAKJ,GAAgBK,GAEhD,OAAON,GAAgBK,GAAKL,GAAgBM,EAChD,KAAK,KACD,GAAI,OAAOD,GAAM,OAAOC,EACpB,OAAOL,GAAgBI,IAAMJ,GAAgBK,GAEjD,OAAON,GAAgBK,IAAML,GAAgBM,EACrD,CAIA,OAHAD,EAAItL,GAASsL,GACbC,EAAIvL,GAASuL,GAELF,GACJ,IAAK,IACDvW,EAASwW,EAAIC,EACb,KACJ,KAAK,IACDzW,EAASwW,EAAIC,EACb,KACJ,KAAK,IACDzW,EAASwW,EAAIC,EACb,KACJ,KAAK,IACDzW,EAASwW,EAAIC,EACb,KACJ,KAAK,IACDzW,EAASa,KAAK8T,GAAG,CAAC6B,EAAGC,GACrB,KACJ,SACI,OAAOnM,GACf,CAEA,OAAQtK,EAAS,EACba,KAAK6V,KAAK,CAAC1W,AAAS,IAATA,GAAuB,IAClCA,CACR,CAeA,SAAS2W,GAAiBC,CAAG,CAAE3Z,CAAK,SAEhC,AAAI0Y,GAAyBiB,GAClBA,EAGPnB,GAAyBmB,GACjB3Z,GAAS4Z,GAAeD,EAAK3Z,IAAU,EAAE,CAGjDsY,GAA4BqB,GACrBE,GAAgBF,EAAK3Z,GAGzB8Z,GAAgBzB,GAA2BsB,GAAOA,EAAM,CAACA,EAAI,CAAG3Z,EAC3E,CAoCA,SAAS4Z,GAAe/G,CAAK,CAAE7S,CAAK,EAMhC,IAAK,IALDoE,EAAcpE,EACT6H,cAAc,GACd/F,KAAK,CAAC+Q,EAAMyD,WAAW,CAC5BzD,EAAMnC,SAAS,CAAG,GAClBqJ,EAAS,EAAE,CACNpY,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAE0a,EAAO,KAAK,EAAGrY,EAAIwE,EAAM,EAAExE,EAGlE,IAAK,IAFDsY,EAAQja,EAAMiE,SAAS,CAACG,CAAW,CAACzC,EAAE,CACtC,CAAA,IAAS,EAAE,CACNqF,EAAI6L,EAAM0D,QAAQ,CAAEtP,EAAO4L,EAAMhC,MAAM,CAAG,EAAG7J,EAAIC,EAAM,EAAED,EAE1C,UAAhB,MADJgT,CAAAA,EAAOC,CAAK,CAACjT,EAAE,AAAD,GAEVgT,AAAY,MAAZA,CAAI,CAAC,EAAE,EACPha,IAAUA,EAAMK,QAAQ,EAExB2Z,CAAAA,EAAOha,EAAMK,QAAQ,CAAC8G,OAAO,CAAC/C,CAAW,CAACzC,EAAE,CAAEqF,EAAC,EAEnD+S,EAAOla,IAAI,CAAC6Y,GAAyBsB,GAAQA,EAAO3M,KAG5D,OAAO0M,CACX,CAeA,SAASG,GAAkBpD,CAAS,CAAE9W,CAAK,EACvC,IAAIS,EAAaT,EAAM6H,cAAc,EAAE,CAACiP,EAAU7U,MAAM,CAAC,CACzD,GAAIxB,EAAY,CACZ,IAAIuZ,EAAOha,EAAMmH,OAAO,CAAC1G,EACrBqW,EAAUhS,GAAG,EACjB,GAAI,AAAgB,UAAhB,OAAOkV,GACPA,AAAY,MAAZA,CAAI,CAAC,EAAE,EACPha,IAAUA,EAAMK,QAAQ,CAAE,CAE1B,IAAI0C,EAAS/C,EAAMK,QAAQ,CAAC8G,OAAO,CAAC1G,EAChCqW,EAAUhS,GAAG,EACjB,OAAO4T,GAAyB3V,GAAUA,EAASsK,GACvD,CACA,OAAOqL,GAAyBsB,GAAQA,EAAO3M,GACnD,CACA,OAAOA,GACX,CAiBA,SAASyM,GAAerD,CAAO,CAAEzW,CAAK,EAElC,IAAK,IADDuZ,EACK5X,EAAI,EAAGwE,EAAOsQ,EAAQnX,MAAM,CAAEyY,EAAO,KAAK,EAAGuB,EAAW,KAAK,EAAGvW,EAAS,KAAK,EAAGyW,EAAI,KAAK,EAAG7X,EAAIwE,EAAM,EAAExE,EAAG,CAGjH,GAAI4W,GAFJR,EAAOtB,CAAO,CAAC9U,EAAE,EAEsB,CACnC2X,EAAWvB,EACX,QACJ,CAmBA,GAjBIW,GAAyBX,GACzByB,EAAIzB,EAGCM,GAA2BN,GAChCyB,EAAIM,GAAerD,EAASzW,GAGvBsY,GAA4BP,GAEjCyB,EAAKd,GADL3V,EAAS8W,GAAgB9B,EAAM/X,IACS+C,EAASsK,IAG5CoL,GAA6BV,IAClCyB,CAAAA,EAAKxZ,GAASka,GAAkBnC,EAAM/X,EAAM,EAG5C,AAAa,KAAA,IAANwZ,EAAmB,CAE1B,GAAI,AAAa,KAAA,IAAND,EAEHA,EADAD,EACID,GAAeC,EAAU,EAAGE,GAG5BA,MAQP,CAJA,GAAI,CAACF,EACN,OAAOjM,IAIP,IAAI8M,EAAY1D,CAAO,CAAC9U,EAAI,EAAE,CAC1B4W,GAA4B4B,IAC5BpB,EAAgB,CAACoB,EAAU,CAAGpB,EAAgB,CAACO,EAAS,GACxDE,EAAIH,GAAec,EAAWX,EAAGM,GAAerD,EAAQ3U,KAAK,CAACH,EAAI,KAClEA,EAAIwE,GAERoT,EAAIF,GAAeC,EAAUC,EAAGC,EACpC,CACAF,EAAW,KAAK,EAChBE,EAAI,KAAK,CACb,CACJ,CACA,OAAOd,GAAyBa,GAAKA,EAAIlM,GAC7C,CAmBA,SAASwM,GAAgBO,CAAe,CAAEpa,CAAK,CAE/C8W,CAAS,EAEL,IAAIuD,EAAYrB,EAAkB,CAACoB,EAAgBpP,IAAI,CAAC,CACxD,GAAIqP,EACA,GAAI,CACA,OAAOA,EAAUD,EAAgBnD,IAAI,CAAEjX,EAC3C,CACA,MAAOyE,EAAI,CACP,OAAO4I,GACX,CAEJ,IAAIpD,EAAQ,AAAI6B,MAAM,aAAc/J,MAAM,CAACqY,EAAgBpP,IAAI,CAAE,gBAEjE,OADAf,EAAMe,IAAI,CAAG,sBACPf,CACV,CAuF6B,IAAIqQ,GAXV,CACnBrM,SAAUA,GACVyL,iBAAkBA,GAClBa,mBApQJ,SAA4BtD,CAAI,CAAEjX,CAAK,EAEnC,IAAK,IADD+Z,EAAS,EAAE,CACNpY,EAAI,EAAGwE,EAAO8Q,EAAK3X,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAC5CoY,EAAOla,IAAI,CAAC6Z,GAAiBzC,CAAI,CAACtV,EAAE,CAAE3B,IAE1C,OAAO+Z,CACX,EA+PIH,eAAgBA,GAChBM,kBAAmBA,GACnBJ,eAAgBA,GAChBd,mBAAoBA,GACpBwB,0BAtEJ,SAAmCxP,CAAI,CAAEyP,CAAiB,EACtD,OAAQxB,GAA4BrK,IAAI,CAAC5D,IACrC,CAACgO,EAAkB,CAAChO,EAAK,EACzB,CAAC,CAAEgO,CAAAA,EAAkB,CAAChO,EAAK,CAAGyP,CAAgB,CACtD,EAmEIC,oBAlDJ,SAASA,EAAoBjE,CAAO,CAAEkE,CAAW,CAAEC,CAAQ,EACnC,KAAK,IAArBD,GAA0BA,CAAAA,EAAc,CAAA,EAC3B,KAAK,IAAlBC,GAAuBA,CAAAA,EAAW,CAAA,EACtC,IAAK,IAAIjZ,EAAI,EAAGwE,EAAOsQ,EAAQnX,MAAM,CAAEyY,EAAO,KAAK,EAAGpW,EAAIwE,EAAM,EAAExE,EAE1DoW,AADJA,CAAAA,EAAOtB,CAAO,CAAC9U,EAAE,AAAD,YACIE,MAChB6Y,EAAoB3C,EAAM4C,EAAaC,GAElCtC,GAA4BP,GACjC2C,EAAoB3C,EAAKd,IAAI,CAAE0D,EAAaC,GAEvCpC,GAAyBT,IAC1BA,EAAK7B,mBAAmB,EACxB6B,CAAAA,EAAKzB,WAAW,EAAIqE,CAAU,EAE9B5C,EAAK5B,gBAAgB,EACrB4B,CAAAA,EAAKxB,QAAQ,EAAIqE,CAAO,EAExB7C,EAAK3B,iBAAiB,EACtB2B,CAAAA,EAAKrH,SAAS,EAAIiK,CAAU,EAE5B5C,EAAK1B,cAAc,EACnB0B,CAAAA,EAAKlH,MAAM,EAAI+J,CAAO,GAGrBnC,GAA6BV,KAC9BA,EAAKnB,cAAc,EACnBmB,CAAAA,EAAK9V,MAAM,EAAI0Y,CAAU,EAEzB5C,EAAKlB,WAAW,EAChBkB,CAAAA,EAAKjT,GAAG,EAAI8V,CAAO,GAI/B,OAAOnE,CACX,CAgBA,EAkBIoE,GAAuBP,GAAyBZ,gBAAgB,CA+CpEY,GAAyBE,yBAAyB,CAAC,MA1BnD,SAAavD,CAAI,CAAEjX,CAAK,EACpB,IAAIgO,EAAQ6M,GAAqB5D,CAAI,CAAC,EAAE,CACpCjX,GACJ,OAAQ,OAAOgO,GACX,IAAK,SACD,OAAOpK,KAAKkX,GAAG,CAAC9M,EACpB,KAAK,SAED,IAAK,IADD+L,EAAS,EAAE,CACNpY,EAAI,EAAGwE,EAAO6H,EAAM1O,MAAM,CAAEyb,EAAS,KAAK,EAAGpZ,EAAIwE,EAAM,EAAExE,EAAG,CAEjE,GAAI,AAAkB,UAAlB,MADJoZ,CAAAA,EAAS/M,CAAK,CAACrM,EAAE,AAAD,EAEZ,OAAO0L,IAEX0M,EAAOla,IAAI,CAAC+D,KAAKkX,GAAG,CAACC,GACzB,CACA,OAAOhB,CAEX,SACI,OAAO1M,GACf,CACJ,GA6BA,IAAI2N,GAAuBV,GAAyBZ,gBAAgB,CAsCpEY,GAAyBE,yBAAyB,CAAC,MAhBnD,SAASS,EAAIhE,CAAI,CAAEjX,CAAK,EACpB,IAAK,IAAI2B,EAAI,EAAGwE,EAAO8Q,EAAK3X,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE5D,GAAI,CADJqM,CAAAA,EAAQgN,GAAqB/D,CAAI,CAACtV,EAAE,CAAE3B,EAAK,GAEtC,AAAiB,UAAjB,OAAOgO,GACJ,CAACiN,EAAIjN,EAAOhO,GAChB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,GA6BA,IAAIkb,GAA6BZ,GAAyBC,kBAAkB,CAuD5ED,GAAyBE,yBAAyB,CAAC,UAjCnD,SAAiBvD,CAAI,CAAEjX,CAAK,EAKxB,IAAK,IAJD+Z,EAASmB,GAA2BjE,EACpCjX,GACAmb,EAAQ,EACRpY,EAAS,EACJpB,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACI4F,MAAMyG,KACP,EAAEmN,EACFpY,GAAUiL,GAEd,KACJ,KAAK,SACD,IAAK,IAAIhH,EAAI,EAAGC,EAAO+G,EAAM1O,MAAM,CAAEyb,EAAS,KAAK,EAAG/T,EAAIC,EAAM,EAAED,EAExC,UAAlB,MADJ+T,CAAAA,EAAS/M,CAAK,CAAChH,EAAE,AAAD,GAEXO,MAAMwT,KACP,EAAEI,EACFpY,GAAUgY,EAI1B,CAEJ,OAAQI,EAASpY,EAASoY,EAAS,CACvC,GA6BA,IAAIC,GAA4Bd,GAAyBZ,gBAAgB,CAsEzEY,GAAyBE,yBAAyB,CAAC,WAhDnD,SAAkBvD,CAAI,CAAEjX,CAAK,EAGzB,IAAK,IAFDmb,EAAQ,EACRpY,EAAS,EACJpB,EAAI,EAAGwE,EAAO8Q,EAAK3X,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE5D,OAAQ,MADRqM,CAAAA,EAAQoN,GAA0BnE,CAAI,CAACtV,EAAE,CAAE3B,EAAK,GAE5C,IAAK,UACD,EAAEmb,EACFpY,GAAWiL,GAAAA,EACX,QACJ,KAAK,SACIzG,MAAMyG,KACP,EAAEmN,EACFpY,GAAUiL,GAEd,QACJ,KAAK,SACD,EAAEmN,EACF,QACJ,SACI,IAAK,IAAInU,EAAI,EAAGC,EAAO+G,EAAM1O,MAAM,CAAEyb,EAAS,KAAK,EAAG/T,EAAIC,EAAM,EAAED,EAE9D,OAAQ,MADR+T,CAAAA,EAAS/M,CAAK,CAAChH,EAAE,AAAD,GAEZ,IAAK,UACD,EAAEmU,EACFpY,GAAWgY,GAAAA,EACX,QACJ,KAAK,SACIxT,MAAMwT,KACP,EAAEI,EACFpY,GAAUgY,GAEd,QACJ,KAAK,SACD,EAAEI,EACF,QACR,CAEJ,QACR,CAEJ,OAAQA,EAASpY,EAASoY,EAAS,CACvC,GA0EAb,GAAyBE,yBAAyB,CAAC,QAxBnD,SAASa,EAAMpE,CAAI,CAAEjX,CAAK,EAItB,IAAK,IAHD+Z,EAASO,GAAyBC,kBAAkB,CAACtD,EACrDjX,GACAmb,EAAQ,EACHxZ,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACG,CAAC4F,MAAMyG,IACP,EAAEmN,EAEN,KACJ,KAAK,SACDA,GAASE,EAAMrN,EAAOhO,EAE9B,CAEJ,OAAOmb,CACX,GAgFAb,GAAyBE,yBAAyB,CAAC,SA9BnD,SAASc,EAAOrE,CAAI,CAAEjX,CAAK,EAIvB,IAAK,IAHD+Z,EAASO,GAAyBC,kBAAkB,CAACtD,EACrDjX,GACAmb,EAAQ,EACHxZ,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAAG,CAEjE,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACD,GAAI4F,MAAMyG,GACN,SAEJ,KACJ,KAAK,SACDmN,GAASG,EAAOtN,EAAOhO,GACvB,QACJ,KAAK,SACD,GAAI,CAACgO,EACD,QAGZ,CACA,EAAEmN,CACN,CACA,OAAOA,CACX,GA6BA,IAAII,GAAsBjB,GAAyBZ,gBAAgB,CAiCnEY,GAAyBE,yBAAyB,CAAC,KAVnD,SAAYvD,CAAI,CAAEjX,CAAK,EACnB,OAAQub,GAAoBtE,CAAI,CAAC,EAAE,CAAEjX,GACjCub,GAAoBtE,CAAI,CAAC,EAAE,CAAEjX,GAC7Bub,GAAoBtE,CAAI,CAAC,EAAE,CAAEjX,EACrC,GA6BA,IAAIwb,GAAwBlB,GAAyBZ,gBAAgB,CAgCrEY,GAAyBE,yBAAyB,CAAC,OAVnD,SAAcvD,CAAI,CAAEjX,CAAK,EACrB,IAAIgO,EAAQwN,GAAsBvE,CAAI,CAAC,EAAE,CACrCjX,GACJ,MAAQ,AAAiB,UAAjB,OAAOgO,GAAsBzG,MAAMyG,EAC/C,GA6BA,IAAIyN,GAAyBnB,GAAyBC,kBAAkB,CAiDxED,GAAyBE,yBAAyB,CAAC,MA3BnD,SAASkB,EAAIzE,CAAI,CAAEjX,CAAK,EAIpB,IAAK,IAHD+Z,EAAS0B,GAAuBxE,EAChCjX,GACA+C,EAASoG,OAAOwS,iBAAiB,CAC5Bha,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACGqM,EAAQjL,GACRA,CAAAA,EAASiL,CAAI,EAEjB,KACJ,KAAK,SACDA,CAAAA,EAAQ0N,EAAI1N,EAAK,EACLjL,GACRA,CAAAA,EAASiL,CAAI,CAGzB,CAEJ,OAAO5E,SAASrG,GAAUA,EAAS,CACvC,GAwFAuX,GAAyBE,yBAAyB,CAAC,SAtCnD,SAAgBvD,CAAI,CAAEjX,CAAK,EAIvB,IAAK,IAHD4b,EAAS,EAAE,CACX7B,EAASO,GAAyBC,kBAAkB,CAACtD,EACrDjX,GACK2B,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACI4F,MAAMyG,IACP4N,EAAO/b,IAAI,CAACmO,GAEhB,KACJ,KAAK,SACD,IAAK,IAAIhH,EAAI,EAAGC,EAAO+G,EAAM1O,MAAM,CAAEyb,EAAS,KAAK,EAAG/T,EAAIC,EAAM,EAAED,EAExC,UAAlB,MADJ+T,CAAAA,EAAS/M,CAAK,CAAChH,EAAE,AAAD,GAEXO,MAAMwT,IACPa,EAAO/b,IAAI,CAACkb,EAI5B,CApBJ,IAsBII,EAAQS,EAAOtc,MAAM,CACzB,GAAI,CAAC6b,EACD,OAAO9N,IAEX,IAAIwO,EAAOjY,KAAKkY,KAAK,CAACX,EAAQ,GAC1B,OAAQA,EAAQ,EACZS,CAAM,CAACC,EAAK,CACZ,AAACD,CAAAA,CAAM,CAACC,EAAO,EAAE,CAAGD,CAAM,CAACC,EAAK,AAAD,EAAK,CAEhD,GA6BA,IAAIE,GAAyBzB,GAAyBC,kBAAkB,CAiDxED,GAAyBE,yBAAyB,CAAC,MA3BnD,SAASwB,EAAI/E,CAAI,CAAEjX,CAAK,EAIpB,IAAK,IAHD+Z,EAASgC,GAAuB9E,EAChCjX,GACA+C,EAASoG,OAAO8S,iBAAiB,CAC5Bta,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACGqM,EAAQjL,GACRA,CAAAA,EAASiL,CAAI,EAEjB,KACJ,KAAK,SACDA,CAAAA,EAAQgO,EAAIhO,EAAK,EACLjL,GACRA,CAAAA,EAASiL,CAAI,CAGzB,CAEJ,OAAO5E,SAASrG,GAAUA,EAAS,CACvC,GA6BA,IAAImZ,GAAuB5B,GAAyBZ,gBAAgB,CAwFpE,SAASyC,GAAWlF,CAAI,CAAEjX,CAAK,EAI3B,IAAK,IAHDoc,EAAU,CAAC,EACXrC,EAASO,GAAyBC,kBAAkB,CAACtD,EACrDjX,GACK2B,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACI4F,MAAMyG,IACPoO,CAAAA,CAAO,CAACpO,EAAM,CAAG,AAACoO,CAAAA,CAAO,CAACpO,EAAM,EAAI,CAAA,EAAK,CAAA,EAE7C,KACJ,KAAK,SACD,IAAK,IAAIhH,EAAI,EAAGC,EAAO+G,EAAM1O,MAAM,CAAEyb,EAAS,KAAK,EAAG/T,EAAIC,EAAM,EAAED,EAExC,UAAlB,MADJ+T,CAAAA,EAAS/M,CAAK,CAAChH,EAAE,AAAD,GAEXO,MAAMwT,IACPqB,CAAAA,CAAO,CAACrB,EAAO,CAAG,AAACqB,CAAAA,CAAO,CAACrB,EAAO,EAAI,CAAA,EAAK,CAAA,CAI3D,CAEJ,OAAOqB,CACX,CAuDA,SAASC,GAAKpF,CAAI,CAAEjX,CAAK,EACrB,IAAIoc,EAAUD,GAAWlF,EACrBjX,GACAqE,EAAO1G,OAAO0G,IAAI,CAAC+X,GACvB,GAAI,CAAC/X,EAAK/E,MAAM,CACZ,OAAO+N,IAIX,IAAK,IAFDiP,EAAU9U,WAAWnD,CAAI,CAAC,EAAE,EAC5BkY,EAAYH,CAAO,CAAC/X,CAAI,CAAC,EAAE,CAAC,CACvB1C,EAAI,EAAGwE,EAAO9B,EAAK/E,MAAM,CAAE7B,EAAM,KAAK,EAAG+e,EAAW,KAAK,EAAGrB,EAAQ,KAAK,EAAGxZ,EAAIwE,EAAM,EAAExE,EAGzF4a,EADJpB,CAAAA,EAAQiB,CAAO,CADf3e,EAAM4G,CAAI,CAAC1C,EAAE,CACO,AAAD,GAEf2a,EAAU9U,WAAW/J,GACrB8e,EAAYpB,GAEPoB,IAAcpB,GAEfmB,EADJE,CAAAA,EAAWhV,WAAW/J,EAAG,IAErB6e,EAAUE,EACVD,EAAYpB,GAIxB,OAAOoB,EAAY,EAAID,EAAUjP,GACrC,CAnJAiN,GAAyBE,yBAAyB,CAAC,MAvBnD,SAAavD,CAAI,CAAEjX,CAAK,EACpB,IAAIyc,EAASP,GAAqBjF,CAAI,CAAC,EAAE,CACrCjX,GACA+a,EAASmB,GAAqBjF,CAAI,CAAC,EAAE,CACrCjX,SAOJ,CANsB,UAAlB,OAAOyc,GACPA,CAAAA,EAASA,CAAM,CAAC,EAAE,AAAD,EAEC,UAAlB,OAAO1B,GACPA,CAAAA,EAASA,CAAM,CAAC,EAAE,AAAD,EAEjB,AAAkB,UAAlB,OAAO0B,GACP,AAAkB,UAAlB,OAAO1B,GACPA,AAAW,IAAXA,GACO1N,IAEJoP,EAAS1B,CACpB,GA+JAT,GAAyBE,yBAAyB,CAAC,OAAQ6B,IAC3D/B,GAAyBE,yBAAyB,CAAC,YAtEnD,SAAcvD,CAAI,CAAEjX,CAAK,EACrB,IAAIoc,EAAUD,GAAWlF,EACrBjX,GACAqE,EAAO1G,OAAO0G,IAAI,CAAC+X,GACvB,GAAI,CAAC/X,EAAK/E,MAAM,CACZ,OAAO+N,IAIX,IAAK,IAFDqP,EAAW,CAAClV,WAAWnD,CAAI,CAAC,EAAE,EAAE,CAChCkY,EAAYH,CAAO,CAAC/X,CAAI,CAAC,EAAE,CAAC,CACvB1C,EAAI,EAAGwE,EAAO9B,EAAK/E,MAAM,CAAE7B,EAAM,KAAK,EAAG0d,EAAQ,KAAK,EAAGxZ,EAAIwE,EAAM,EAAExE,EAGtE4a,EADJpB,CAAAA,EAAQiB,CAAO,CADf3e,EAAM4G,CAAI,CAAC1C,EAAE,CACO,AAAD,GAEf+a,EAAW,CAAClV,WAAW/J,GAAK,CAC5B8e,EAAYpB,GAEPoB,IAAcpB,GACnBuB,EAAS7c,IAAI,CAAC2H,WAAW/J,IAGjC,OAAO8e,EAAY,EAAIG,EAAWrP,GACtC,GAkDAiN,GAAyBE,yBAAyB,CAAC,YAAa6B,IA2BhE,IAAIM,GAAuBrC,GAAyBZ,gBAAgB,CAwCpEY,GAAyBE,yBAAyB,CAAC,MAlBnD,SAAavD,CAAI,CAAEjX,CAAK,EACpB,IAAIgO,EAAQ2O,GAAqB1F,CAAI,CAAC,EAAE,CACpCjX,GAIJ,OAHqB,UAAjB,OAAOgO,GACPA,CAAAA,EAAQA,CAAK,CAAC,EAAE,AAAD,EAEX,OAAOA,GACX,IAAK,UACL,IAAK,SACD,MAAO,CAACA,CAChB,CACA,OAAOX,GACX,GA6BA,IAAIuP,GAAsBtC,GAAyBZ,gBAAgB,CAyCnEY,GAAyBE,yBAAyB,CAAC,KAnBnD,SAASqC,EAAG5F,CAAI,CAAEjX,CAAK,EACnB,IAAK,IAAI2B,EAAI,EAAGwE,EAAO8Q,EAAK3X,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE5D,GAAI,AAAiB,UAAjB,MADJqM,CAAAA,EAAQ4O,GAAoB3F,CAAI,CAACtV,EAAE,CAAE3B,EAAK,EAEtC,CAAA,GAAI6c,EAAG7O,EAAOhO,GACV,MAAO,CAAA,CACX,MAEC,GAAIgO,EACL,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,GA6BA,IAAI8O,GAA6BxC,GAAyBC,kBAAkB,CAiD5ED,GAAyBE,yBAAyB,CAAC,UA3BnD,SAASuC,EAAQ9F,CAAI,CAAEjX,CAAK,EAKxB,IAAK,IAJD+Z,EAAS+C,GAA2B7F,EACpCjX,GACA+C,EAAS,EACTia,EAAa,CAAA,EACRrb,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACI4F,MAAMyG,KACPgP,EAAa,CAAA,EACbja,GAAUiL,GAEd,KACJ,KAAK,SACDgP,EAAa,CAAA,EACbja,GAAUga,EAAQ/O,EAAOhO,EAEjC,CAEJ,OAAQgd,EAAaja,EAAS,CAClC,GA0EAuX,GAAyBE,yBAAyB,CAAC,MAxBnD,SAASyC,EAAIhG,CAAI,CAAEjX,CAAK,EAIpB,IAAK,IAHD+Z,EAASO,GAAyBC,kBAAkB,CAACtD,EACrDjX,GACA+C,EAAS,EACJpB,EAAI,EAAGwE,EAAO4T,EAAOza,MAAM,CAAE0O,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAE9D,OAAQ,MADRqM,CAAAA,EAAQ+L,CAAM,CAACpY,EAAE,AAAD,GAEZ,IAAK,SACI4F,MAAMyG,IACPjL,CAAAA,GAAUiL,CAAI,EAElB,KACJ,KAAK,SACDjL,GAAUka,EAAIjP,EAAOhO,EAE7B,CAEJ,OAAO+C,CACX,GA6BA,IAAIma,GAAuB5C,GAAyBZ,gBAAgB,CA4DpEY,GAAyBE,yBAAyB,CAAC,MAtCnD,SAAavD,CAAI,CAAEjX,CAAK,EACpB,IAAK,IAAI2B,EAAI,EAAGwE,EAAO8Q,EAAK3X,MAAM,CAAE6d,EAAY,KAAK,EAAGnP,EAAQ,KAAK,EAAGrM,EAAIwE,EAAM,EAAExE,EAEhF,OAAQ,MADRqM,CAAAA,EAAQkP,GAAqBjG,CAAI,CAACtV,EAAE,CAAE3B,EAAK,GAEvC,IAAK,UACL,IAAK,SACD,GAAI,AAAqB,KAAA,IAAdmd,EACPA,EAAY,CAAC,CAACnP,OAEb,GAAI,CAAC,CAACA,IAAUmP,EACjB,MAAO,CAAA,EAEX,KACJ,KAAK,SACD,IAAK,IAAInW,EAAI,EAAGC,EAAO+G,EAAM1O,MAAM,CAAEyb,EAAS,KAAK,EAAG/T,EAAIC,EAAM,EAAED,EAE9D,OAAQ,MADR+T,CAAAA,EAAS/M,CAAK,CAAChH,EAAE,AAAD,GAEZ,IAAK,UACL,IAAK,SACD,GAAI,AAAqB,KAAA,IAAdmW,EACPA,EAAY,CAAC,CAACpC,OAEb,GAAI,CAAC,CAACA,IAAWoC,EAClB,MAAO,CAAA,CAGnB,CAGZ,CAEJ,MAAO,CAAA,CACX,GA4BA,IAAIC,GAAgD,WAShD,MAAOA,AARPA,CAAAA,GAAWzf,OAAO0f,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAG5b,EAAI,EAAGxE,EAAIsE,UAAUnC,MAAM,CAAEqC,EAAIxE,EAAGwE,IAE5C,IAAK,IAAI2D,KADTiY,EAAI9b,SAAS,CAACE,EAAE,CACKhE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACof,EAAGjY,IACzDgY,CAAAA,CAAC,CAAChY,EAAE,CAAGiY,CAAC,CAACjY,EAAE,AAAD,EAElB,OAAOgY,CACX,CAAA,EACgB5a,KAAK,CAAC,IAAI,CAAEjB,UAChC,EAoCI+b,GAAUJ,GAASA,GAASA,GAAS,CAAC,EAAGzF,IAAwB2C,IAA2BzC,IAoB5F4F,IACI/gB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAEAqY,GAA2D,WAS3D,MAAOA,AARPA,CAAAA,GAAsBjgB,OAAO0f,MAAM,EAAI,SAASC,CAAC,EAC7C,IAAK,IAAIC,EAAG5b,EAAI,EAAGxE,EAAIsE,UAAUnC,MAAM,CAAEqC,EAAIxE,EAAGwE,IAE5C,IAAK,IAAI2D,KADTiY,EAAI9b,SAAS,CAACE,EAAE,CACKhE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACof,EAAGjY,IACzDgY,CAAAA,CAAC,CAAChY,EAAE,CAAGiY,CAAC,CAACjY,EAAE,AAAD,EAElB,OAAOgY,CACX,CAAA,EAC2B5a,KAAK,CAAC,IAAI,CAAEjB,UAC3C,EAGIoc,GAAqB,AAACtf,IAA+EG,KAAK,CAW1Gof,GAA8B,SAAU9X,CAAM,EAa9C,SAAS8X,EAAajf,CAAO,EACzB,IAAI0E,EAAQ,IAAI,CACZkK,EAAgBoQ,GAAmBC,EAAapQ,cAAc,CAC9D7O,GAWJ,MAJA0E,AANAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EAMzC5M,OAAO,CAAG,EAAE,CAClB0C,EAAMsM,OAAO,CAAG,EAAE,CAClBtM,EAAMwa,SAAS,CAAG,EAAE,CACpBxa,EAAM1E,OAAO,CAAG4O,EACTlK,CACX,CA0ZA,OArbAka,GAAqBK,EAAc9X,GA6CnC8X,EAAa7f,SAAS,CAAC2R,MAAM,CAAG,SAAU1E,CAAS,CAAErM,CAAO,EACxC,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,IAAI,CAACA,OAAO,AAAD,EAC/C,IAAImf,EAAuBnf,EAAQmf,oBAAoB,CACnDC,EAAgBpf,EAAQof,aAAa,CACrCC,EAAe,AAAiC,CAAA,IAAjC,IAAI,CAACrf,OAAO,CAACiS,eAAe,CAC3ClD,EAAe/O,EAAQ+O,YAAY,CACnCuQ,EAAgBtf,EAAQsf,aAAa,CACpCvQ,GACDA,CAAAA,EAAgBuQ,AAAkB,MAAlBA,GAAyBH,EACrC,AAAC,IAAKI,cAAc,EAAE,CAAC,EAAE,CACzB,GAAG,EAEND,GACDA,CAAAA,EAAiBvQ,AAAiB,MAAjBA,EAAuB,IAAM,GAAG,EAErD,IAAI/M,EAAUqK,EAAUQ,gBAAgB,CAAC7M,EAAQ8M,oBAAoB,EACjEvH,EAAczG,OAAO0G,IAAI,CAACxD,GAC1Bwd,EAAU,EAAE,CACZC,EAAela,EAAY9E,MAAM,CACjCif,EAAW,EAAE,CAEbL,GACAG,EAAQxe,IAAI,CAACuE,EAAYI,GAAG,CAAC,SAAU/D,CAAU,EAAI,MAAO,IAAKsB,MAAM,CAACtB,EAAY,IAAO,GAAGiP,IAAI,CAACyO,IAEvG,IAAK,IAAIK,EAAc,EAAGA,EAAcF,EAAcE,IAAe,CACjE,IAAI/d,EAAa2D,CAAW,CAACoa,EAAY,CACrCvc,EAASpB,CAAO,CAACJ,EAAW,CAC5BmH,EAAe3F,EAAO3C,MAAM,CAC5B2L,EAAaC,EAAUsB,MAAM,CAAC/L,GAC9Bge,EAAiB,KAAK,EACtBxT,GACAwT,CAAAA,EAAiBxT,EAAWyT,QAAQ,AAAD,EAEvC,IAAK,IAAIhe,EAAW,EAAGA,EAAWkH,EAAclH,IAAY,CACxD,IAAIC,EAAYsB,CAAM,CAACvB,EAAS,CAgBhC,GAfK6d,CAAQ,CAAC7d,EAAS,EACnB6d,CAAAA,CAAQ,CAAC7d,EAAS,CAAG,EAAE,AAAD,EAGtB+d,AAAmB,WAAnBA,EACA9d,EAAY,IAAMA,EAAY,IAEzB,AAAqB,UAArB,OAAOA,EACZA,EAAYgd,OAAOhd,GAAWgO,OAAO,CAAC,IAAKf,GAEjB,UAArB,OAAOjN,GACZA,CAAAA,EAAY,IAAKoB,MAAM,CAACpB,EAAW,IAAI,EAE3C4d,CAAQ,CAAC7d,EAAS,CAAC8d,EAAY,CAAG7d,EAE9B6d,IAAgBF,EAAe,EAAG,CAKlC,IADA,IAAI3c,EAAI6c,EAGJ,AAFGD,CAAQ,CAAC7d,EAAS,CAACpB,MAAM,CAAG,GAE3Bqf,AAAY,KAAK,IADPJ,CAAQ,CAAC7d,EAAS,CAACiB,EAAE,EAInC4c,CAAQ,CAAC7d,EAAS,CAAC0K,GAAG,GACtBzJ,IAEJ0c,EAAQxe,IAAI,CAAC0e,CAAQ,CAAC7d,EAAS,CAACgP,IAAI,CAACyO,GACzC,CACJ,CACJ,CACA,OAAOE,EAAQ3O,IAAI,CAACuO,EACxB,EAaAH,EAAa7f,SAAS,CAACiS,KAAK,CAAG,SAAUrR,CAAO,CAAEoB,CAAW,EACzD,IAQI2e,EAKA3c,EAZA8b,EAAYvP,AADA,IAAI,CACMuP,SAAS,CAC/Bc,EAAgBhB,GAAmB,IAAI,CAAChf,OAAO,CAC/CA,GACAigB,EAAcD,EAAcC,WAAW,CACvCb,EAAgBY,EAAcZ,aAAa,CAC3CnN,EAAkB+N,EAAc/N,eAAe,CAC/CqN,EAAgBU,EAAcV,aAAa,CAE3CY,EAAQ,EACRC,EAAMH,EAAcG,GAAG,CACvBpO,EAAWiO,EAAcjO,QAAQ,CACjCC,EAASgO,EAAchO,MAAM,CAYjC,GAVArC,AAdgB,IAAI,CAcV3N,OAAO,CAAG,EAAE,CACtB2N,AAfgB,IAAI,CAeVtP,IAAI,CAAC,CACXC,KAAM,QACN0B,QAAS2N,AAjBG,IAAI,CAiBG3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,AAnBG,IAAI,CAmBGqB,OAAO,AAC9B,GACImP,GAAOF,GACPE,CAAAA,EAAMF,EAAYE,EAAG,EAErBA,EAAK,CAgBL,GAfAJ,EAAQI,EACHrQ,OAAO,CAAC,WAAY,MACpBa,KAAK,CAACyO,GAAiB,MACxB,CAAA,CAACrN,GAAYA,EAAW,CAAA,GACxBA,CAAAA,EAAW,CAAA,EAEX,CAAA,CAACC,GAAUA,GAAU+N,EAAMtf,MAAM,AAAD,GAChCuR,CAAAA,EAAS+N,EAAMtf,MAAM,CAAG,CAAA,EAEvB6e,GACD3P,CAAAA,AAnCQ,IAAI,CAmCFyQ,oBAAoB,CAC1BzQ,AApCI,IAAI,CAoCE0Q,cAAc,CAACN,EAAK,EAIlC9N,EAAiB,CAGjB,IAAK,IAFDjB,EAAU+O,CAAK,CAAC,EAAE,CAACpP,KAAK,CAAC2O,GAAiB3P,AAzCtC,IAAI,CAyC4CyQ,oBAAoB,EAAI,KAEvEtd,EAAI,EAAGA,EAAIkO,EAAQvQ,MAAM,CAAEqC,IAChCkO,CAAO,CAAClO,EAAE,CAAGkO,CAAO,CAAClO,EAAE,CAAC4N,IAAI,GAAGZ,OAAO,CAAC,eAAgB,GAE3DH,CA9CQ,IAAI,CA8CFqB,OAAO,CAAGA,EACpBe,GACJ,CACA,IAAIuO,EAAS,EACb,IAAKJ,EAAQnO,EAAUmO,GAASlO,EAAQkO,IAChCH,AAAoB,MAApBA,CAAK,CAACG,EAAM,CAAC,EAAE,CACfI,IAGA3Q,AAvDI,IAAI,CAwDH4Q,WAAW,CAACR,CAAK,CAACG,EAAM,CAAEA,EAAQnO,EAAWuO,EAGtDpB,CAAAA,EAAUze,MAAM,EAChBye,CAAS,CAAC,EAAE,CAACze,MAAM,EACnBye,AAAoB,SAApBA,CAAS,CAAC,EAAE,CAAC,EAAE,EACf,CAACvP,AA9DO,IAAI,CA8DD3P,OAAO,CAAC8Q,UAAU,EAC7BnB,AA/DQ,IAAI,CA+DFM,gBAAgB,CAACN,AA/DnB,IAAI,CA+DyB3N,OAAO,CAAC,EAAE,CAAE,KAAM,CAAA,GAG3D,IAAK,IAAIc,EAAI,EAAGwE,EAAOqI,AAlEX,IAAI,CAkEiB3N,OAAO,CAACvB,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAAG,CAC5DM,EAASuM,AAnED,IAAI,CAmEO3N,OAAO,CAACc,EAAE,CAC7B,IAAK,IAAIqF,EAAI,EAAGC,EAAOhF,EAAO3C,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EAC9C,GAAI/E,CAAM,CAAC+E,EAAE,EAAI,AAAqB,UAArB,OAAO/E,CAAM,CAAC+E,EAAE,CAAe,CAC5C,IAAIrG,EAAY6N,AAtEhB,IAAI,CAsEsBF,aAAa,CAACrM,CAAM,CAAC+E,EAAE,EAC7CrG,aAAqBwM,MACrBxM,CAAAA,EAAYA,EAAUyP,OAAO,EAAC,EAElC5B,AA1EA,IAAI,CA0EM3N,OAAO,CAACc,EAAE,CAACqF,EAAE,CAAGrG,CAC9B,CAER,CACJ,CACA6N,AA/EgB,IAAI,CA+EVtP,IAAI,CAAC,CACXC,KAAM,aACN0B,QAAS2N,AAjFG,IAAI,CAiFG3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,AAnFG,IAAI,CAmFGqB,OAAO,AAC9B,EACJ,EAIAiO,EAAa7f,SAAS,CAACmhB,WAAW,CAAG,SAAUC,CAAS,CAAEC,CAAS,EAC/D,IAAI9Q,EAAY,IAAI,CAChB3N,EAAU2N,EAAU3N,OAAO,EAAI,EAAE,CACjCkd,EAAYvP,EAAUuP,SAAS,CAC/BtZ,EAAK+J,EAAU3P,OAAO,CACtB4R,EAAchM,EAAGgM,WAAW,CAC5BC,EAAYjM,EAAGiM,SAAS,CACxByN,EAAiB3P,EAAU3P,OAAO,CAACsf,aAAa,EAC5C3P,EAAUyQ,oBAAoB,CAClCrR,EAAeY,EAAU3P,OAAO,CAAC+O,YAAY,CAC5CA,GAAgBA,IAAiBuQ,GAClCvQ,CAAAA,EAAeY,EAAU+Q,mBAAmB,EAAI,GAAE,EAEtD,IAAI5d,EAAI,EAAG6d,EAAI,GAAIC,EAAQ,GAAIC,EAAe,EAAGzd,EAAS,EACtD0d,EAAO,SAAU3Y,CAAC,EACdwY,EAAIH,CAAS,CAACrY,EAAE,AACxB,EACI4Y,EAAW,SAAUzgB,CAAI,EACjB4e,EAAUze,MAAM,CAAG2C,EAAS,GAC5B8b,EAAUle,IAAI,CAAC,CAACV,EAAK,EAEzB4e,CAAS,CAAC9b,EAAO,CAAC8b,CAAS,CAAC9b,EAAO,CAAC3C,MAAM,CAAG,EAAE,GAAKH,GACpD4e,CAAS,CAAC9b,EAAO,CAACpC,IAAI,CAACV,EAE/B,EACIU,EAAO,WACH,GAAI4Q,EAAciP,GAAgBA,EAAehP,EAAW,CAExD,EAAEgP,EACND,EAAQ,GACR,MACJ,CAuBA,GArBI,AAAiB,UAAjB,OAAOA,EACH,CAAClY,MAAMC,WAAWiY,KAAWrW,SAASqW,IACtCA,EAAQjY,WAAWiY,GACnBG,EAAS,WAEHrY,MAAM4F,KAAK+C,KAAK,CAACuP,IAKvBG,EAAS,WAJTH,EAAQA,EAAM9Q,OAAO,CAAC,MAAO,KAC7BiR,EAAS,SAObA,EAAS,UAET/e,EAAQvB,MAAM,CAAG2C,EAAS,GAC1BpB,EAAQhB,IAAI,CAAC,EAAE,EAIf,AAAiB,UAAjB,OAAO4f,GACPjR,AAA+B,WAA/BA,EAAUC,SAAS,CAACgR,IACpB7R,EAAc,CACd,IAAIiS,EAAeJ,EACnBA,EAAQA,EAAM9Q,OAAO,CAACf,EAAc,KACD,WAA/BY,EAAUC,SAAS,CAACgR,IACpBA,CAAAA,EAAQI,CAAW,CAE3B,CACAhf,CAAO,CAACoB,EAAO,CAACqd,EAAU,CAAGG,EAC7BA,EAAQ,GACR,EAAExd,EACF,EAAEyd,CACN,EACA,GAAKL,EAAU9P,IAAI,GAAGjQ,MAAM,EAGxB+f,AAAwB,MAAxBA,EAAU9P,IAAI,EAAE,CAAC,EAAE,EAGvB,KAAO5N,EAAI0d,EAAU/f,MAAM,CAAEqC,IAAK,CAE9B,GADAge,EAAKhe,GACD6d,AAAM,MAANA,GAEI,CAAC,+BAA+B5Q,IAAI,CAACyQ,EAAUxJ,SAAS,CAAClU,IAAK,CAE9D9B,IACA,MACJ,CAGJ,GAAI2f,AAAM,MAANA,EAEA,IADAG,EAAK,EAAEhe,GAEH,AADGA,EAAI0d,EAAU/f,MAAM,EACnBkgB,AAAM,MAANA,GAGJC,GAASD,EACTG,EAAK,EAAEhe,QAGN6d,IAAMrB,EACXte,IAIA4f,GAASD,CAEjB,CACA3f,IACJ,EAOAie,EAAa7f,SAAS,CAACihB,cAAc,CAAG,SAAUN,CAAK,EASnD,IAAK,IANDkB,EAFAC,EAAS,EACTC,EAAS,EAETC,EAAgB,CACZ,IAAK,EACL,IAAK,EACL,IAAM,CACV,EAAGC,EAAatB,EAAMtf,MAAM,CACvBqC,EAAI,EAAGA,EAAIue,EAAYve,IAAK,CACjC,IAAIwe,EAAQ,CAAA,EACRX,EAAI,KAAK,EACTY,EAAK,KAAK,EACVC,EAAK,KAAK,EACVZ,EAAQ,GAEZ,GAAI9d,EAAI,GACJ,MAGJ,IAAK,IADD0d,EAAYT,CAAK,CAACjd,EAAE,CACfqF,EAAI,EAIT,AAJYA,EAAIqY,EAAU/f,MAAM,GAChCkgB,EAAIH,CAAS,CAACrY,EAAE,CAChBoZ,EAAKf,CAAS,CAACrY,EAAI,EAAE,CACrBqZ,EAAKhB,CAAS,CAACrY,EAAI,EAAE,CACjBwY,AAAM,MAANA,GAJ8BxY,IAAK,CAQvC,GAAIwY,AAAM,MAANA,GACA,GAAIW,EACA,CAAA,GAAIE,AAAO,MAAPA,GAAcD,AAAO,MAAPA,EAAY,CAC1B,KAAOA,AAAO,MAAPA,GAAcpZ,EAAIqY,EAAU/f,MAAM,EACrC8gB,EAAKf,CAAS,CAAC,EAAErY,EAAE,AAKU,MAAA,IAAtBiZ,CAAa,CAACG,EAAG,EACxBH,CAAa,CAACG,EAAG,GAErBD,EAAQ,CAAA,CACZ,CAAA,MAGAA,EAAQ,CAAA,OAGP,AAA4B,KAAA,IAArBF,CAAa,CAACT,EAAE,EAEvBjY,MAAM4F,KAAK+C,KAAK,CADrBuP,EAAQA,EAAMlQ,IAAI,KAIThI,CAAAA,MAAM4B,OAAOsW,KAClB,CAACrW,SAASD,OAAOsW,GAAM,GACvBQ,CAAa,CAACT,EAAE,GAJhBS,CAAa,CAACT,EAAE,GAMpBC,EAAQ,IAGRA,GAASD,CAEH,CAAA,MAANA,GACAQ,IAEM,MAANR,GACAO,GAER,CACJ,CAsBA,OAlBIE,CAAa,CAAC,IAAI,CAAGA,CAAa,CAAC,IAAI,CACvCH,EAAU,KAELG,CAAa,CAAC,IAAI,CAAGA,CAAa,CAAC,IAAI,CAC5CH,EAAU,KAQVC,EAASC,EACT,IAAI,CAACT,mBAAmB,CAAG,IAG3B,IAAI,CAACA,mBAAmB,CAAG,IAExBO,CACX,EAOAhC,EAAa7f,SAAS,CAAC6R,QAAQ,CAAG,WAC9B,OAAOoB,GAAyBD,mBAAmB,CAAC,IAAI,CAACpQ,OAAO,CAAE,IAAI,CAACgP,OAAO,CAClF,EASAiO,EAAapQ,cAAc,CAAGkQ,GAAoBA,GAAoB,CAAC,EAAG1M,GAAyBxD,cAAc,EAAG,CAAEuQ,cAAe,IAAK,GACnIH,CACX,EAAE5M,IACFA,GAAyBhQ,YAAY,CAAC,MAAO4c,IAyB7C,IAAIwC,IACI5jB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAKAgb,GAAqB,AAAChiB,IAA+EG,KAAK,CAW1G8hB,GAA8B,SAAUxa,CAAM,EAa9C,SAASwa,EAAa3hB,CAAO,EACzB,IAAI0E,EAAQ,IAAI,CACZkK,EAAgB8S,GAAmBC,EAAa9S,cAAc,CAC9D7O,GAOJ,MALA0E,AADAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EACzCe,SAAS,CAAG,IAtEkCsP,GAsENrQ,GAC9ClK,EAAM1E,OAAO,CAAG4O,EACZA,EAAcgT,aAAa,EAC3Bld,EAAM4I,YAAY,CAACvI,AAAkD,IAAlDA,KAAKC,GAAG,CAAC4J,EAAciT,eAAe,EAAI,EAAG,IAE7Dnd,CACX,CA0EA,OAjGA+c,GAAqBE,EAAcxa,GAsCnCwa,EAAaviB,SAAS,CAAC2N,IAAI,CAAG,SAAU3L,CAAW,EAC/C,IAAIiL,EAAY,IAAI,CAChBsD,EAAYtD,EAAUsD,SAAS,CAC/BxO,EAAQkL,EAAUlL,KAAK,CACvByE,EAAKyG,EAAUrM,OAAO,CACtBmgB,EAAMva,EAAGua,GAAG,CACZ2B,EAASlc,EAAGkc,MAAM,CAClBC,EAAenc,EAAGmc,YAAY,CAOlC,OANA1V,EAAUhM,IAAI,CAAC,CACXC,KAAM,OACN6f,IAAKA,EACLze,OAAQN,EACRD,MAAOA,CACX,GACOE,QACFC,OAAO,CAACwgB,EACTE,MAAMF,GAAQ3W,IAAI,CAAC,SAAU8W,CAAQ,EAAI,OAAOA,EAASrL,IAAI,EAAI,GACjEuJ,GAAO,IACNhV,IAAI,CAAC,SAAUgV,CAAG,EAOnB,OANIA,IAEAhf,EAAMyG,aAAa,GACnB+H,EAAU0B,KAAK,CAAC,CAAE8O,IAAKA,CAAI,GAC3Bhf,EAAM2E,UAAU,CAAC6J,EAAUsB,QAAQ,GAAG3L,UAAU,KAE7C+G,EACFc,kBAAkB,CAAC4U,GACnB5W,IAAI,CAAC,WAAc,OAAOgV,CAAK,EACxC,GACKhV,IAAI,CAAC,SAAUgV,CAAG,EAOnB,OANA9T,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACN6f,IAAKA,EACLze,OAAQN,EACRD,MAAOA,CACX,GACOkL,CACX,GAAG,KAAQ,CAAC,SAAUjB,CAAK,EAOvB,MANAiB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRgK,MAAOA,EACPjK,MAAOA,CACX,GACMiK,CACV,EACJ,EAMAuW,EAAa9S,cAAc,CAAG,CAC1BsR,IAAK,GACL2B,OAAQ,GACRF,cAAe,CAAA,EACfC,gBAAiB,EACjB5P,gBAAiB,CAAA,CACrB,EACO0P,CACX,EAAE9T,GACFA,EAAyBxL,YAAY,CAAC,MAAOsf,IAsB7C,IAAIO,IACIrkB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAEAyb,GAA4D,WAS5D,MAAOA,AARPA,CAAAA,GAAuBrjB,OAAO0f,MAAM,EAAI,SAASC,CAAC,EAC9C,IAAK,IAAIC,EAAG5b,EAAI,EAAGxE,EAAIsE,UAAUnC,MAAM,CAAEqC,EAAIxE,EAAGwE,IAE5C,IAAK,IAAI2D,KADTiY,EAAI9b,SAAS,CAACE,EAAE,CACKhE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACof,EAAGjY,IACzDgY,CAAAA,CAAC,CAAChY,EAAE,CAAGiY,CAAC,CAACjY,EAAE,AAAD,EAElB,OAAOgY,CACX,CAAA,EAC4B5a,KAAK,CAAC,IAAI,CAAEjB,UAC5C,EAIIwI,GAAQ,AAAC1L,IAA+E0L,KAAK,CAAE9H,GAAU,AAAC5D,IAA+E4D,OAAO,CAAE8e,GAAsB,AAAC1iB,IAA+EG,KAAK,CAAEwiB,GAA2B,AAAC3iB,IAA+E6E,UAAU,CAWpa+d,GAA+B,SAAUnb,CAAM,EAa/C,SAASmb,EAActiB,CAAO,EAC1B,IAAI0E,EAAQ,IAAI,CACZkK,EAAgBwT,GAAoBE,EAAczT,cAAc,CAChE7O,GAWJ,MAJA0E,AANAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EAMzC5M,OAAO,CAAG,EAAE,CAClB0C,EAAMsM,OAAO,CAAG,EAAE,CAClBtM,EAAM1E,OAAO,CAAG4O,EAChBlK,EAAMvD,KAAK,CAAG,IAvxJ6B+F,EAwxJpCxC,CACX,CAiIA,OA5JAwd,GAAsBI,EAAenb,GA6CrCmb,EAAcljB,SAAS,CAACiS,KAAK,CAAG,SAAUrR,CAAO,CAAEoB,CAAW,EAC1D,IAAIuO,EAAY,IAAI,CAEhBsQ,EAAcjgB,AADlBA,CAAAA,EAAUoiB,GAAoBzS,EAAU3P,OAAO,CAAEA,EAAO,EAC9BigB,WAAW,CACjCsC,EAAcviB,EAAQuiB,WAAW,CACjCtQ,EAAkBjS,EAAQiS,eAAe,CACzC1M,EAAcvF,EAAQuF,WAAW,CACjC2K,EAAOlQ,EAAQkQ,IAAI,CACvB,GAAKA,GAcL,GAXAP,EAAU3N,OAAO,CAAG,EAAE,CACtB2N,EAAUtP,IAAI,CAAC,CACXC,KAAM,QACN0B,QAAS2N,EAAU3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,EAAUqB,OAAO,AAC9B,GACIiP,GACA/P,CAAAA,EAAO+P,EAAY/P,EAAI,EAE3BA,EAAOA,EAAKjN,KAAK,GACbsf,AAAgB,YAAhBA,EACA,IAAK,IAAIzf,EAAI,EAAGwE,EAAO4I,EAAKzP,MAAM,CAAEqC,EAAIwE,EAAMxE,IAAK,CAC/C,IAAIoW,EAAOhJ,CAAI,CAACpN,EAAE,CAClB,GAAI,CAAEoW,CAAAA,aAAgBlW,KAAI,EACtB,MAEA2M,CAAAA,EAAUqB,OAAO,YAAYhO,OACzBiP,EACAtC,EAAUqB,OAAO,CAAChQ,IAAI,CAAC,GAAGkC,MAAM,CAACgW,EAAKsJ,KAAK,KAEtCjd,GAAeA,aAAuBvC,OAC3C2M,EAAUqB,OAAO,CAAChQ,IAAI,CAACuE,CAAW,CAACzC,EAAE,EAEzC6M,EAAUxO,KAAK,CAAC0E,SAAS,CAAC8J,EAAUqB,OAAO,CAAClO,EAAE,EAAIA,EAAE2f,QAAQ,GAAIvJ,IAGhE9N,GAAM,+CAAgD,CAAA,EAE9D,MAEC,GAAImX,AAAgB,SAAhBA,EAAwB,CACzBtQ,EACAtC,EAAUqB,OAAO,CAAGd,EAAKsS,KAAK,GAEzBjd,GACLoK,CAAAA,EAAUqB,OAAO,CAAGzL,CAAU,EAmClC,IAAK,IADDmd,EAAS,IAAI,CACR7gB,EAAW,EAAGyF,EAAO4I,EAAKzP,MAAM,CAAEoB,EAAWyF,EAAMzF,KACxD8gB,AAlCU,SAAU9gB,CAAQ,CAC5ByF,CAAI,EACA,IAAIrB,EAAMiK,CAAI,CAACrO,EAAS,CAC5B,GAAIyB,GAAQ2C,GACR,IAAK,IAAI0Z,EAAc,EAAGvX,EAAOnC,EAAIxF,MAAM,CAAEkf,EAAcvX,EAAMuX,IACzDhQ,EAAU3N,OAAO,CAACvB,MAAM,CAAGkf,EAAc,GACzChQ,EAAU3N,OAAO,CAAChB,IAAI,CAAC,EAAE,EAE7B2O,EAAU3N,OAAO,CAAC2d,EAAY,CAAC3e,IAAI,CAACiF,CAAG,CAAC0Z,EAAY,EAChDhQ,EAAUqB,OAAO,YAAYhO,MAC7B0f,EAAOvhB,KAAK,CAAC0E,SAAS,CAAC8J,EAAUqB,OAAO,CAAC2O,EAAY,EACjDA,EAAY8C,QAAQ,GAAI9S,EAAU3N,OAAO,CAAC2d,EAAY,EAG1DvU,GAAM,+CAAgD,CAAA,OAI7D,CACD,IAAInB,EAAgB0F,EAAUqB,OAAO,CACrC,GAAI/G,GAAiB,CAAEA,CAAAA,aAAyBjH,KAAI,EAAI,CACpD,IAAI4f,EAAW,CAAC,EAChBP,GAAyBpY,EAAe,SAAU4Y,CAAa,CAAE1W,CAAI,EACjEyW,CAAQ,CAACzW,EAAK,CAAG0W,EAAcpd,MAAM,CAAC,SAAUqd,CAAG,CAAElkB,CAAG,EACpD,OAAOkkB,CAAG,CAAClkB,EAAI,AACnB,EAAGqH,EACP,GACAA,EAAM2c,CACV,CACAF,EAAOvhB,KAAK,CAACsK,OAAO,CAAC,CAACxF,EAAI,CAAEpE,EAChC,CACJ,EAGYA,EAAUyF,EAE1B,CACAqI,EAAUtP,IAAI,CAAC,CACXC,KAAM,aACN0B,QAAS2N,EAAU3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,EAAUqB,OAAO,AAC9B,GACJ,EAOAsR,EAAcljB,SAAS,CAAC6R,QAAQ,CAAG,WAC/B,OAAO,IAAI,CAAC9P,KAAK,AACrB,EASAmhB,EAAczT,cAAc,CAAGsT,GAAqBA,GAAqB,CAAC,EAAG9P,GAAyBxD,cAAc,EAAG,CAAEqB,KAAM,EAAE,CAAEqS,YAAa,MAAO,GAChJD,CACX,EAAEjQ,IACFA,GAAyBhQ,YAAY,CAAC,OAAQigB,IAsB9C,IAAIS,IACIllB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAKAsc,GAAsB,AAACtjB,IAA+EG,KAAK,CAW3GojB,GAA+B,SAAU9b,CAAM,EAa/C,SAAS8b,EAAcjjB,CAAO,EAC1B,IAAI0E,EAAQ,IAAI,CACZkK,EAAgBoU,GAAoBC,EAAcpU,cAAc,CAChE7O,GAOJ,MALA0E,AADAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EACzCe,SAAS,CAAG,IAnEmC2S,GAmEN1T,GAC/ClK,EAAM1E,OAAO,CAAG4O,EACZA,EAAcgT,aAAa,EAC3Bld,EAAM4I,YAAY,CAACvI,AAAkD,IAAlDA,KAAKC,GAAG,CAAC4J,EAAciT,eAAe,EAAI,EAAG,IAE7Dnd,CACX,CAgFA,OAvGAqe,GAAsBE,EAAe9b,GAsCrC8b,EAAc7jB,SAAS,CAAC2N,IAAI,CAAG,SAAU3L,CAAW,EAChD,IAAIiL,EAAY,IAAI,CAChBsD,EAAYtD,EAAUsD,SAAS,CAC/BxO,EAAQkL,EAAUlL,KAAK,CACvByE,EAAKyG,EAAUrM,OAAO,CACtBkQ,EAAOtK,EAAGsK,IAAI,CACdgT,EAAUtd,EAAGsd,OAAO,CACpBnB,EAAenc,EAAGmc,YAAY,CAOlC,OANA1V,EAAUhM,IAAI,CAAC,CACXC,KAAM,OACN4P,KAAMA,EACNxO,OAAQN,EACRD,MAAOA,CACX,GACOE,QACFC,OAAO,CAAC4hB,EACTlB,MAAMkB,GAAS/X,IAAI,CAAC,SAAU8W,CAAQ,EAAI,OAAOA,EAASkB,IAAI,EAAI,GAAG,KAAQ,CAAC,SAAU/X,CAAK,EACzFiB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRgK,MAAOA,EACPjK,MAAOA,CACX,GACAiiB,QAAQC,IAAI,CAAC,6BAA6BngB,MAAM,CAACggB,EAAS,KAC9D,GACAhT,GAAQ,EAAE,EACT/E,IAAI,CAAC,SAAU+E,CAAI,EAOpB,OANIA,IAEA/O,EAAMyG,aAAa,GACnB+H,EAAU0B,KAAK,CAAC,CAAEnB,KAAMA,CAAK,GAC7B/O,EAAM2E,UAAU,CAAC6J,EAAUsB,QAAQ,GAAG3L,UAAU,KAE7C+G,EAAUc,kBAAkB,CAAC4U,GAAc5W,IAAI,CAAC,WAAc,OAAO+E,CAAM,EACtF,GACK/E,IAAI,CAAC,SAAU+E,CAAI,EAOpB,OANA7D,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACN4P,KAAMA,EACNxO,OAAQN,EACRD,MAAOA,CACX,GACOkL,CACX,GAAG,KAAQ,CAAC,SAAUjB,CAAK,EAOvB,MANAiB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRgK,MAAOA,EACPjK,MAAOA,CACX,GACMiK,CACV,EACJ,EAMA6X,EAAcpU,cAAc,CAAG,CAC3BqB,KAAM,EAAE,CACR0R,cAAe,CAAA,EACfC,gBAAiB,EACjB5P,gBAAiB,CAAA,EACjBsQ,YAAa,MACjB,EACOU,CACX,EAAEpV,GACFA,EAAyBxL,YAAY,CAAC,OAAQ4gB,IAyB9C,IAAIK,IACIzlB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAEA6c,GAAoE,WASpE,MAAOA,AARPA,CAAAA,GAA+BzkB,OAAO0f,MAAM,EAAI,SAASC,CAAC,EACtD,IAAK,IAAIC,EAAG5b,EAAI,EAAGxE,EAAIsE,UAAUnC,MAAM,CAAEqC,EAAIxE,EAAGwE,IAE5C,IAAK,IAAI2D,KADTiY,EAAI9b,SAAS,CAACE,EAAE,CACKhE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACof,EAAGjY,IACzDgY,CAAAA,CAAC,CAAChY,EAAE,CAAGiY,CAAC,CAACjY,EAAE,AAAD,EAElB,OAAOgY,CACX,CAAA,EACoC5a,KAAK,CAAC,IAAI,CAAEjB,UACpD,EAGI4gB,GAA8B,AAAC9jB,IAA+EG,KAAK,CAAE4jB,GAAkC,AAAC/jB,IAA+E8E,SAAS,CAWhPkf,GAAuC,SAAUvc,CAAM,EAavD,SAASuc,EAAsB1jB,CAAO,EAClC,IAAI0E,EAAQ,IAAI,CACZkK,EAAgB4U,GAA4BE,EAAsB7U,cAAc,CAChF7O,GAKJ,MAHA0E,AADAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EACzC5M,OAAO,CAAG,EAAE,CAClB0C,EAAMif,MAAM,CAAG,EAAE,CACjBjf,EAAM1E,OAAO,CAAG4O,EACTlK,CACX,CAmFA,OAxGA4e,GAA8BI,EAAuBvc,GAuCrDuc,EAAsBtkB,SAAS,CAACiS,KAAK,CAAG,SAAUrR,CAAO,CAAEoB,CAAW,EAElE,IADIwE,EAsBAxC,EApBAwgB,EAAeJ,GAA4B7T,AAD/B,IAAI,CACqC3P,OAAO,CAC5DA,GACAgC,EAAU,AAAC,CAAA,AAAC,CAAA,AAA6B,OAA5B4D,CAAAA,EAAKge,EAAaT,IAAI,AAAD,GAAevd,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsV,MAAM,AAAD,GAAM,EAAE,AAAD,EAAGvV,GAAG,CAAC,SAAUvC,CAAM,EAAI,OAAOA,EAAOH,KAAK,EAAI,GAC/I,GAAIjB,AAAmB,IAAnBA,EAAQvB,MAAM,CACd,MAAO,CAAA,CAEXkP,CAPgB,IAAI,CAOVgU,MAAM,CAAG,EAAE,CACrBhU,AARgB,IAAI,CAQV3N,OAAO,CAAG,EAAE,CACtB2N,AATgB,IAAI,CASVtP,IAAI,CAAC,CACXC,KAAM,QACN0B,QAAS2N,AAXG,IAAI,CAWG3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,AAbG,IAAI,CAaGgU,MAAM,AAC7B,GAEA,IAAI1D,EAAc2D,EAAa3D,WAAW,CACtCkD,EAAOS,EAAaT,IAAI,CACxBlD,GAAekD,GACfnhB,CAAAA,EAAUie,EAAYkD,EAAKjI,MAAM,CAAA,EAGrCvL,AAtBgB,IAAI,CAsBV3N,OAAO,CAAGA,EACpB,IAAK,IAAIc,EAAI,EAAGwE,EAAOtF,EAAQvB,MAAM,CAAEqC,EAAIwE,EAAMxE,IAAK,CAClDM,EAASpB,CAAO,CAACc,EAAE,CACnB6M,AAzBY,IAAI,CAyBNgU,MAAM,CAAC7gB,EAAE,CAAI8gB,EAAa3R,eAAe,CAC/C,GAAG/O,MAAM,CAACE,EAAOof,KAAK,IACtBiB,KACJ,IAAK,IAAItb,EAAI,EAAGC,EAAOhF,EAAO3C,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EAC9C,GAAI/E,CAAM,CAAC+E,EAAE,EAAI,AAAqB,UAArB,OAAO/E,CAAM,CAAC+E,EAAE,CAAe,CAC5C,IAAIrG,EAAY6N,AA9BZ,IAAI,CA8BkBF,aAAa,CAACrM,CAAM,CAAC+E,EAAE,EAC7CrG,aAAqBwM,MACrBxM,CAAAA,EAAYA,EAAUyP,OAAO,EAAC,EAElC5B,AAlCI,IAAI,CAkCE3N,OAAO,CAACc,EAAE,CAACqF,EAAE,CAAGrG,CAC9B,CAER,CACA6N,AAtCgB,IAAI,CAsCVtP,IAAI,CAAC,CACXC,KAAM,aACN0B,QAAS2N,AAxCG,IAAI,CAwCG3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,AA1CG,IAAI,CA0CGgU,MAAM,AAC7B,EACJ,EAOAD,EAAsBtkB,SAAS,CAAC6R,QAAQ,CAAG,WACvC,OAAOoB,GAAyBD,mBAAmB,CAAC,IAAI,CAACpQ,OAAO,CAAE,IAAI,CAAC2hB,MAAM,CACjF,EASAD,EAAsB7U,cAAc,CAAG0U,GAA6B,CAAC,EAAGlR,GAAyBxD,cAAc,EACxG6U,CACX,EAAErR,IACFA,GAAyBhQ,YAAY,CAAC,eAAgBqhB,IA0BtD,IAAIG,IACIhmB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAKAod,GAA8B,AAACpkB,IAA+EG,KAAK,CAAEkkB,GAA6B,AAACrkB,IAA+EoM,IAAI,CA0BtOkY,GAAuC,SAAU7c,CAAM,EAavD,SAAS6c,EAAsBhkB,CAAO,EAClC,IAAI0E,EAAQ,IAAI,CACZkK,EAAgBkV,GAA4BE,EAAsBnV,cAAc,CAChF7O,GAIJ,MAFA0E,AADAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EACzCe,SAAS,CAAG,IAtF2C+T,GAsFN9U,GACvDlK,EAAM1E,OAAO,CAAG4O,EACTlK,CACX,CAuFA,OA3GAmf,GAA8BG,EAAuB7c,GAmCrD6c,EAAsB5kB,SAAS,CAAC2N,IAAI,CAAG,SAAU3L,CAAW,EACxD,IAAIiL,EAAY,IAAI,CAChBsD,EAAYtD,EAAUsD,SAAS,CAC/BxO,EAAQkL,EAAUlL,KAAK,CACvByE,EAAKyG,EAAUrM,OAAO,CACtB+hB,EAAenc,EAAGmc,YAAY,CAC9BF,EAAkBjc,EAAGic,eAAe,CACpCD,EAAgBhc,EAAGgc,aAAa,CAChC3P,EAAkBrM,EAAGqM,eAAe,CACpCgS,EAAere,EAAGqe,YAAY,CAC9BC,EAAuBte,EAAGse,oBAAoB,CAC9CC,EAAMH,EAAsBI,aAAa,CAACH,EAC1CC,EACA7X,EAAUrM,OAAO,EAOrB,GANAqM,EAAUhM,IAAI,CAAC,CACXC,KAAM,OACNoB,OAAQN,EACRD,MAAOA,EACPgjB,IAAKA,CACT,GACI,CAACE,IAAIC,QAAQ,CAACH,GACd,MAAM,AAAIlX,MAAM,gBAAkBkX,GAEtC,OAAOnC,MAAMmC,GACRhZ,IAAI,CAAC,SAAU8W,CAAQ,EAAI,OAAQA,EAASkB,IAAI,EAAK,GACrDhY,IAAI,CAAC,SAAUgY,CAAI,EACpB,GA7EA,AAAgB,UAAhB,OA6EkBA,GAAAA,GA5EtB,AAAsB,UAAtB,OAAOA,AA4EeA,EA5EV/X,KAAK,EAAiB+X,AA4EZA,EA5EiB/X,KAAK,EAC5C,AAA2B,UAA3B,OAAO+X,AA2EeA,EA3EV/X,KAAK,CAACsN,IAAI,EACtB,AAA8B,UAA9B,OAAOyK,AA0EeA,EA1EV/X,KAAK,CAACmZ,OAAO,EACzB,AAA6B,UAA7B,OAAOpB,AAyEeA,EAzEV/X,KAAK,CAACoZ,MAAM,CA0EhB,MAAM,AAAIvX,MAAMkW,EAAK/X,KAAK,CAACmZ,OAAO,EAStC,OAPA5U,EAAU0B,KAAK,CAAC,CACZY,gBAAiBA,EACjBkR,KAAMA,CACV,GAEAhiB,EAAMyG,aAAa,GACnBzG,EAAM2E,UAAU,CAAC6J,EAAUsB,QAAQ,GAAG3L,UAAU,IACzC+G,EAAUc,kBAAkB,CAAC4U,EACxC,GACK5W,IAAI,CAAC,WAWN,OAVAkB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRD,MAAOA,EACPgjB,IAAKA,CACT,GAEIvC,GACAnU,WAAW,WAAc,OAAOpB,EAAUU,IAAI,EAAI,EAAGhI,AAAoC,IAApCA,KAAKC,GAAG,CAAC6c,GAAmB,EAAG,IAEjFxV,CACX,GAAG,KAAQ,CAAC,SAAUjB,CAAK,EAOvB,MANAiB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRgK,MAAOA,EACPjK,MAAOA,CACX,GACMiK,CACV,EACJ,EAMA4Y,EAAsBnV,cAAc,CAAG,CACnCoV,aAAc,GACdC,qBAAsB,GACtBtC,cAAe,CAAA,EACfC,gBAAiB,EACjB5P,gBAAiB,CAAA,CACrB,EACO+R,CACX,EAAEnW,IAMF,AAAC,SAAUmW,CAAqB,EAW5B,IAAIS,EAAW,6BAgCf,SAASC,EAAgB1kB,CAAO,EACZ,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAI6R,EAAY7R,EAAQ6R,SAAS,CAC7BG,EAAShS,EAAQgS,MAAM,CACvB2S,EAAyB3kB,EAAQ2kB,sBAAsB,CACvD/S,EAAc5R,EAAQ4R,WAAW,CACjCG,EAAW/R,EAAQ+R,QAAQ,CAC/B,OAAO4S,GAA2B,AAACF,CAAAA,CAAQ,CAAC7S,GAAe,EAAE,EAAI,GAAE,EAC9D7M,CAAAA,KAAKC,GAAG,CAAE+M,GAAY,EAAI,GAAK,CAAA,EAChC,IACC0S,CAAAA,CAAQ,CAACV,GAA2BlS,EAAW,IAAI,EAAI,GAAE,EACzDG,CAAAA,EACGjN,KAAKC,GAAG,CAACgN,EAAQ,GACjB,GAAE,CACd,CAnBAgS,EAAsBI,aAAa,CAjBnC,SAAuBQ,CAAM,CAAEC,CAAQ,CAAE7kB,CAAO,EAC5B,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAImkB,EAAM,IAAIE,IAAI,iDAAiDnhB,MAAM,CAAC2hB,EAAU,aAChF7Q,EAAQhU,EAAQ8kB,eAAe,CAC3B,QAAUJ,EAAgB1kB,EAClCmkB,CAAAA,EAAIY,QAAQ,EAAI/Q,EAChB,IAAIgR,EAAeb,EAAIa,YAAY,CASnC,OARAA,EAAa7gB,GAAG,CAAC,MAAO,QACnBnE,EAAQ8kB,eAAe,GACxBE,EAAa7gB,GAAG,CAAC,uBAAwB,oBACzC6gB,EAAa7gB,GAAG,CAAC,iBAAkB,WACnC6gB,EAAa7gB,GAAG,CAAC,oBAAqB,sBAE1C6gB,EAAa7gB,GAAG,CAAC,cAAe,SAChC6gB,EAAa7gB,GAAG,CAAC,MAAOygB,GACjBT,EAAIc,IAAI,AACnB,EAqBAjB,EAAsBU,eAAe,CAAGA,CAC5C,EAAGV,IAA0BA,CAAAA,GAAwB,CAAC,CAAA,GACtDnW,EAAyBxL,YAAY,CAAC,eAAgB2hB,IAyBtD,IAAIkB,IACIrnB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAEAye,GAAiE,WASjE,MAAOA,AARPA,CAAAA,GAA4BrmB,OAAO0f,MAAM,EAAI,SAASC,CAAC,EACnD,IAAK,IAAIC,EAAG5b,EAAI,EAAGxE,EAAIsE,UAAUnC,MAAM,CAAEqC,EAAIxE,EAAGwE,IAE5C,IAAK,IAAI2D,KADTiY,EAAI9b,SAAS,CAACE,EAAE,CACKhE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACof,EAAGjY,IACzDgY,CAAAA,CAAC,CAAChY,EAAE,CAAGiY,CAAC,CAACjY,EAAE,AAAD,EAElB,OAAOgY,CACX,CAAA,EACiC5a,KAAK,CAAC,IAAI,CAAEjB,UACjD,EAGIwiB,GAA2B,AAAC1lB,IAA+EG,KAAK,CAiChHwlB,GAAoC,SAAUle,CAAM,EAapD,SAASke,EAAmBrlB,CAAO,EAC/B,IAAI0E,EAAQ,IAAI,CACZkK,EAAgBwW,GAAyBC,EAAmBxW,cAAc,CAC1E7O,GASJ,MAPA0E,AADAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EACzC5M,OAAO,CAAG,EAAE,CAClB0C,EAAMsM,OAAO,CAAG,EAAE,CAClBtM,EAAM1E,OAAO,CAAG4O,EACZA,EAAc0W,YAAY,GAC1B5gB,EAAM4gB,YAAY,CAAG1W,EAAc0W,YAAY,CAC/C5gB,EAAM6gB,cAAc,CAAG3W,EAAc0W,YAAY,CAAC1gB,EAAE,EAEjDF,CACX,CA6TA,OAtVAwgB,GAA2BG,EAAoBle,GA4C/Cke,EAAmBjmB,SAAS,CAAC2R,MAAM,CAAG,SAAU1E,CAAS,CAAErM,CAAO,EAC9C,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,IAAI,CAACA,OAAO,AAAD,EAC/C,IAAIqf,EAAerf,AAA4B,CAAA,IAA5BA,EAAQiS,eAAe,CACtCuT,EAAuBxlB,EAAQwlB,oBAAoB,CACnDxjB,EAAUqK,EAAUQ,gBAAgB,CAAC7M,EAAQ8M,oBAAoB,EACjEvH,EAAczG,OAAO0G,IAAI,CAACxD,GAC1ByjB,EAAW,EAAE,CACbhG,EAAela,EAAY9E,MAAM,CACjCif,EAAW,EAAE,CACbgG,EAAY,GAEhB,GAAIrG,EAAa,CACb,IAAIsG,EAAgB,EAAE,CAGtB,GAAIH,EAAsB,CACtB,IAAK,IAAIxb,EAAK,EAAgCA,EAAKC,AAAlB1E,EAAgC9E,MAAM,CAAEuJ,IAAM,CAC3E,IAAI4b,EAAS3b,AADgB1E,CACH,CAACyE,EAAG,CAC1B5G,EAASpB,CAAO,CAAC4jB,EAAO,CACvB5iB,MAAMM,OAAO,CAACF,IAGfA,CAAAA,EAASJ,MAAMN,IAAI,CAACU,EAAM,EAE9B,IAAIyiB,EAAU,AAACziB,CAAAA,EAAOof,KAAK,IAAM,EAAC,EAAGC,QAAQ,EAC7CzgB,CAAAA,CAAO,CAAC4jB,EAAO,CAAGxiB,EAClBuiB,EAAc3kB,IAAI,CAAC6kB,EACvB,CACAH,EAAY,IAAI,CAACI,kBAAkB,CAACvgB,EAAaogB,EAAe3lB,EACpE,MAEI0lB,EAAY,IAAI,CAACI,kBAAkB,CAAC,KAAK,EAAGvgB,EAAavF,EAEjE,CACA,IAAK,IAAI2f,EAAc,EAAGA,EAAcF,EAAcE,IAIlD,IAAK,IAFDvc,EAASpB,CAAO,CADHuD,CAAW,CAACoa,EAAY,CACT,CAC5B5W,EAAe3F,EAAO3C,MAAM,CACvBoB,EAAW,EAAGA,EAAWkH,EAAclH,IAAY,CACxD,IAAIC,EAAYsB,CAAM,CAACvB,EAAS,AAC3B6d,CAAAA,CAAQ,CAAC7d,EAAS,EACnB6d,CAAAA,CAAQ,CAAC7d,EAAS,CAAG,EAAE,AAAD,EAIC,UAArB,OAAOC,GACT,AAAqB,UAArB,OAAOA,GACP,AAAqB,KAAA,IAAdA,GACPA,CAAAA,EAAY,AAACA,CAAAA,GAAa,EAAC,EAAG2gB,QAAQ,EAAC,EAE3C/C,CAAQ,CAAC7d,EAAS,CAAC8d,EAAY,CAAG,IAAI,CAACoG,oBAAoB,CAACpG,EAAc,KAAO,KAAM,KAAMA,EAAc,GAAK,cAAe7d,GAE3H6d,IAAgBF,EAAe,GAC/BgG,EAASzkB,IAAI,CAAC,OACV0e,CAAQ,CAAC7d,EAAS,CAACgP,IAAI,CAAC,IACxB,QAEZ,CAEJ,IAAImV,EAAU,GASd,OALIhmB,EAAQimB,YAAY,EACpBD,CAAAA,EAAU,6CACNhmB,EAAQimB,YAAY,CACpB,YAAW,EAEX,UACJD,EACAN,EACA,UACAD,EAAS5U,IAAI,CAAC,IAJV,kBAOZ,EAIAwU,EAAmBjmB,SAAS,CAAC2mB,oBAAoB,CAAG,SAAUG,CAAG,CAAEC,CAAO,CAAEC,CAAK,CAAEjX,CAAK,CAAEJ,CAAY,EAClG,IAAIsX,EAAMlX,EACNmX,EAAY,OAAUH,CAAAA,EAAU,IAAMA,EAAU,EAAC,EAarD,MAXI,AAAe,UAAf,OAAOE,GACPA,EAAMA,EAAI5D,QAAQ,GACG,MAAjB1T,GACAsX,CAAAA,EAAMA,EAAIvW,OAAO,CAAC,IAAKf,EAAY,EAEvCuX,EAAY,UAENnX,IACNkX,EAAM,GACNC,EAAY,SAET,IAAMJ,EAAOE,CAAAA,EAAQ,IAAMA,EAAQ,EAAC,EACvC,WAAaE,EAAY,KACzBD,EAAM,KAAOH,EAAM,GAC3B,EAIAb,EAAmBjmB,SAAS,CAAC0mB,kBAAkB,CAAG,SAAUS,CAAU,CAAEC,CAAU,CAAExmB,CAAO,EACpE,KAAK,IAApBumB,GAAyBA,CAAAA,EAAa,EAAE,AAAD,EACxB,KAAK,IAApBC,GAAyBA,CAAAA,EAAa,EAAE,AAAD,EAC3B,KAAK,IAAjBxmB,GAAsBA,CAAAA,EAAU,IAAI,CAACA,OAAO,AAAD,EAC/C,IAMIymB,EAEAC,EARAlB,EAAuBxlB,EAAQwlB,oBAAoB,CACnDmB,EAAoB3mB,EAAQ2mB,iBAAiB,CAC7CC,EAAO,UACP9jB,EAAI,EACJ+jB,EAAML,GAAcA,EAAW/lB,MAAM,CAGrCqmB,EAAa,EAMjB,GAAItB,GACAe,GACAC,GACA,CAACO,AA9Lb,SAAoBC,CAAI,CAAEC,CAAI,EAC1B,IAAInkB,EAAIkkB,EAAKvmB,MAAM,CACnB,GAAIwmB,EAAKxmB,MAAM,GAAKqC,EAQhB,MAAO,CAAA,EAPP,KAAO,EAAEA,GACL,GAAIkkB,CAAI,CAAClkB,EAAE,GAAKmkB,CAAI,CAACnkB,EAAE,CACnB,MAAO,CAAA,EAOnB,MAAO,CAAA,CACX,EAiLwByjB,EAAYC,GAAa,CAErC,IADAI,GAAQ,OACD9jB,EAAI+jB,EAAK,EAAE/jB,EAGV2jB,AAFJA,CAAAA,EAAMF,CAAU,CAACzjB,EAAE,AAAD,IACXyjB,CAAU,CAACzjB,EAAI,EAAE,CAEpB,EAAEgkB,EAEGA,GAGLF,GAAQ,IAAI,CAACb,oBAAoB,CAAC,KAAM,8BAA+B,wBACpDe,CAAAA,EAAa,CAAA,EAAK,IAAKL,GAC1CK,EAAa,IAKTL,IAAQD,CAAU,CAAC1jB,EAAE,CACjB6jB,GACAD,EAAU,EACV,OAAOF,CAAU,CAAC1jB,EAAE,GAGpB4jB,EAAU,EACVF,CAAU,CAAC1jB,EAAE,CAAG,IAIpB4jB,EAAU,EAEdE,GAAQ,IAAI,CAACb,oBAAoB,CAAC,KAAM,8BAA+B,cAClEW,CAAAA,EAAU,EACP,0BAA4BA,EAAU,IACtC,EAAC,EAAID,IAGrBG,GAAQ,OACZ,CAEA,GAAIJ,EAAY,CAEZ,IAAK1jB,AADL8jB,GAAQ,OACH9jB,EAAI,EAAG+jB,EAAML,EAAW/lB,MAAM,CAAEqC,EAAI+jB,EAAK,EAAE/jB,EACf,KAAA,IAAlB0jB,CAAU,CAAC1jB,EAAE,EACpB8jB,CAAAA,GAAQ,IAAI,CAACb,oBAAoB,CAAC,KAAM,KAAM,cAAeS,CAAU,CAAC1jB,EAAE,CAAA,EAGlF8jB,GAAQ,OACZ,CAEA,OADAA,EAAQ,UAEZ,EAcAvB,EAAmBjmB,SAAS,CAACiS,KAAK,CAAG,SAAUrR,CAAO,CAAEoB,CAAW,EAC/D,IACIY,EAAU,EAAE,CACZgP,EAAU,EAAE,CACZ4S,EAAewB,GAAyBzV,AAH5B,IAAI,CAGkC3P,OAAO,CACzDA,GACAgS,EAAS4R,EAAa5R,MAAM,CAC5BJ,EAAcgS,EAAahS,WAAW,CACtCC,EAAY+R,EAAa/R,SAAS,CAClCI,EAAkB2R,EAAa3R,eAAe,CAC9CiV,EAAYtD,EAAa0B,YAAY,EAAI,IAAI,CAACA,YAAY,CAC9D,GAAI,CAAE4B,CAAAA,aAAqBC,WAAU,EAAI,CACrCxX,AAXY,IAAI,CAWNtP,IAAI,CAAC,CACXC,KAAM,aACN0B,QAASA,EACTN,OAAQN,EACR4P,QAASA,EACT5F,MAAO,wBACX,GACA,MACJ,CACAuE,AApBgB,IAAI,CAoBV2V,YAAY,CAAG4B,EACzBvX,AArBgB,IAAI,CAqBV4V,cAAc,CAAG2B,EAAUtiB,EAAE,CACvC,IAAI,CAACvE,IAAI,CAAC,CACNC,KAAM,QACN0B,QAAS2N,AAxBG,IAAI,CAwBG3N,OAAO,CAC1BN,OAAQN,EACR4P,QAASrB,AA1BG,IAAI,CA0BGqB,OAAO,AAC9B,GACA,IAGIkI,EAHAhX,EAAOglB,EAAUE,oBAAoB,CAAC,MACtCC,EAAYnlB,EAAKzB,MAAM,CACvBoB,EAAW,EAEXkQ,EAAW6R,EAAa7R,QAAQ,CAEpC,GAAIE,GAAmBoV,EAAW,CAG9B,IAAK,IAFD1jB,EAAQzB,CAAI,CAAC,EAAE,CAAColB,QAAQ,CACxBC,EAAc5jB,EAAMlD,MAAM,CACrBqC,EAAI8O,EACT,AADsB9O,EAAIykB,IACtBzkB,CAAAA,EAAI+O,CAAQ,EADuB/O,IAKnCoW,CAAAA,AAAiB,OAAjBA,AADJA,CAAAA,EAAOvV,CAAK,CAACb,EAAE,AAAD,EACL0kB,OAAO,EACZtO,AAAiB,OAAjBA,EAAKsO,OAAO,AAAQ,GACpBxW,EAAQhQ,IAAI,CAACkY,EAAKuO,SAAS,CAGnC1V,CAAAA,GACJ,CACA,KAAOlQ,EAAWwlB,GAAW,CACzB,GAAIxlB,GAAYkQ,GAAYlQ,GAAYmQ,EAIpC,IAHA,IAAI0V,EAAexlB,CAAI,CAACL,EAAS,CAACylB,QAAQ,CACtCK,EAAqBD,EAAajnB,MAAM,CACxCkf,EAAc,EACXA,EAAcgI,GAAoB,CACrC,IAAIC,EAAsBjI,EAAc/N,EACpC3L,EAAMjE,CAAO,CAAC4lB,EAAoB,CAEtC,GAAI,AAAC1O,CAAAA,AAAiB,OAAjBA,AADLA,CAAAA,EAAOwO,CAAY,CAAC/H,EAAY,AAAD,EACrB6H,OAAO,EACbtO,AAAiB,OAAjBA,EAAKsO,OAAO,AAAQ,GACnB7H,GAAe/N,GACZ+N,GAAe9N,EAAY,CAC1B7P,CAAO,CAAC4lB,EAAoB,EAC7B5lB,CAAAA,CAAO,CAAC4lB,EAAoB,CAAG,EAAE,AAAD,EAEpC,IAAI9lB,EAAY6N,AAjEhB,IAAI,CAiEsBF,aAAa,CAACyJ,EAAKuO,SAAS,EAClD3lB,aAAqBwM,MACrBxM,CAAAA,EAAYA,EAAUyP,OAAO,EAAC,EAElCvP,CAAO,CAAC4lB,EAAoB,CAAC/lB,EAAWkQ,EAAS,CAAGjQ,EAIpD,IADA,IAAIgB,EAAI,EACDjB,EAAWkQ,GAAYjP,GAC1BmD,AAAiC,KAAK,IAAtCA,CAAG,CAACpE,EAAWkQ,EAAWjP,EAAE,EAC5BmD,CAAG,CAACpE,EAAWkQ,EAAWjP,EAAE,CAAG,KAC/BA,GAER,CACA6c,GACJ,CAEJ9d,GACJ,CACA,IAAI,CAACG,OAAO,CAAGA,EACf,IAAI,CAACgP,OAAO,CAAGA,EACf,IAAI,CAAC3Q,IAAI,CAAC,CACNC,KAAM,aACN0B,QAASA,EACTN,OAAQN,EACR4P,QAASA,CACb,EACJ,EAOAqU,EAAmBjmB,SAAS,CAAC6R,QAAQ,CAAG,WACpC,OAAOoB,GAAyBD,mBAAmB,CAAC,IAAI,CAACpQ,OAAO,CAAE,IAAI,CAACgP,OAAO,CAClF,EASAqU,EAAmBxW,cAAc,CAAGsW,GAA0BA,GAA0B,CAAC,EAAG9S,GAAyBxD,cAAc,EAAG,CAAE8X,kBAAmB,CAAA,EAAMnB,qBAAsB,CAAA,CAAK,GACrLH,CACX,EAAEhT,IACFA,GAAyBhQ,YAAY,CAAC,YAAagjB,IAyBnD,IAAIwC,IACIhqB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAIAohB,GAAM,AAACpoB,IAA+EooB,GAAG,CAGzFC,GAA2B,AAACroB,IAA+EG,KAAK,CAWhHmoB,GAAoC,SAAU7gB,CAAM,EAapD,SAAS6gB,EAAmBhoB,CAAO,EAC/B,IAAI0E,EAAQ,IAAI,CACZkK,EAAgBmZ,GAAyBC,EAAmBnZ,cAAc,CAC1E7O,GAIJ,MAFA0E,AADAA,CAAAA,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,CAAEsP,IAAkB,IAAI,AAAD,EACzCe,SAAS,CAAG,IAxEwC0V,GAwENzW,GACpDlK,EAAM1E,OAAO,CAAG4O,EACTlK,CACX,CAoEA,OAxFAmjB,GAA2BG,EAAoB7gB,GA+B/C6gB,EAAmB5oB,SAAS,CAAC2N,IAAI,CAAG,SAAU3L,CAAW,EACrD,IAYIkkB,EAZAjZ,EAAY,IAAI,CAChBsD,EAAYtD,EAAUsD,SAAS,CAC/BxO,EAAQkL,EAAUlL,KAAK,CACvByE,EAAKyG,EAAUrM,OAAO,CACtB+hB,EAAenc,EAAGmc,YAAY,CAC9BmF,EAAYthB,EAAGzE,KAAK,CAiBxB,GAhBAkL,EAAUhM,IAAI,CAAC,CACXC,KAAM,OACNoB,OAAQN,EACRD,MAAOA,EACPmkB,aAAcjZ,EAAUiZ,YAAY,AACxC,GAEI,AAAqB,UAArB,OAAO4B,GACP7a,EAAU4b,OAAO,CAAGf,EACpB5B,EAAewC,GAAII,QAAQ,CAACC,cAAc,CAACjB,IAI3C7a,EAAU4b,OAAO,CAAG3C,AADpBA,CAAAA,EAAe4B,CAAQ,EACUtiB,EAAE,CAEvCyH,EAAUiZ,YAAY,CAAGA,GAAgB,KAAK,EAC1C,CAACjZ,EAAUiZ,YAAY,CAAE,CACzB,IAAIla,EAAQ,wDAOZ,OANAiB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRgK,MAAOA,EACPjK,MAAOA,CACX,GACOE,QAAQE,MAAM,CAAC,AAAI0L,MAAM7B,GACpC,CAKA,OAJAuE,EAAU0B,KAAK,CAAC0W,GAAyB,CAAEzC,aAAcjZ,EAAUiZ,YAAY,AAAC,EAAGjZ,EAAUrM,OAAO,EAAGoB,GAEvGD,EAAMyG,aAAa,GACnBzG,EAAM2E,UAAU,CAAC6J,EAAUsB,QAAQ,GAAG3L,UAAU,IACzC+G,EACFc,kBAAkB,CAAC4U,GACnB5W,IAAI,CAAC,WAON,OANAkB,EAAUhM,IAAI,CAAC,CACXC,KAAM,YACNoB,OAAQN,EACRD,MAAOA,EACPmkB,aAAcjZ,EAAUiZ,YAAY,AACxC,GACOjZ,CACX,EACJ,EAMA2b,EAAmBnZ,cAAc,CAAG,CAChC1N,MAAO,EACX,EACO6mB,CACX,EAAEna,GACFA,EAAyBxL,YAAY,CAAC,YAAa2lB,IAuBnD,IAAII,IACIvqB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAWA2hB,GAAsD,SAAUC,CAAO,CAAEC,CAAI,EAC7E,IAIIC,EACA7N,EACA8D,EACAgK,EAPAC,EAAI,CAAEC,MAAO,EACbC,KAAM,WAAa,GAAInK,AAAO,EAAPA,CAAC,CAAC,EAAE,CAAM,MAAMA,CAAC,CAAC,EAAE,CAAE,OAAOA,CAAC,CAAC,EAAE,AAAE,EAC1DoK,KAAM,EAAE,CACRC,IAAK,EAAE,AAAC,EAKZ,OAAOL,EAAI,CAAE3Q,KAAMiR,EAAK,GAAI,MAASA,EAAK,GAAI,OAAUA,EAAK,EAAG,EAAG,AAAkB,YAAlB,OAAOC,QAA0BP,CAAAA,CAAC,CAACO,OAAOC,QAAQ,CAAC,CAAG,WAAa,OAAO,IAAI,AAAE,CAAA,EAAIR,EACvJ,SAASM,EAAKzqB,CAAC,EAAI,OAAO,SAAU4qB,CAAC,EAAI,OAAOC,AAChD,SAAcC,CAAE,EACZ,GAAIZ,EAAG,MAAM,AAAI3J,UAAU,mCAC3B,KAAO4J,GAAMA,CAAAA,EAAI,EAAGW,CAAE,CAAC,EAAE,EAAKV,CAAAA,EAAI,CAAA,CAAC,EAAIA,GAAG,GAAI,CAC1C,GAAIF,EAAI,EAAG7N,GAAM8D,CAAAA,EAAI2K,AAAQ,EAARA,CAAE,CAAC,EAAE,CAAOzO,EAAE,MAAS,CAAGyO,CAAE,CAAC,EAAE,CAAGzO,EAAE,KAAQ,EAAK,CAAA,AAAC8D,CAAAA,EAAI9D,EAAE,MAAS,AAAD,GAAM8D,EAAEnf,IAAI,CAACqb,GAAI,CAAA,EAAKA,EAAE7C,IAAI,AAAD,GAAM,CAAC,AAAC2G,CAAAA,EAAIA,EAAEnf,IAAI,CAACqb,EAAGyO,CAAE,CAAC,EAAE,CAAA,EAAGC,IAAI,CAAE,OAAO5K,EAE3J,OADI9D,EAAI,EAAJA,AAAO8D,GAAG2K,CAAAA,EAAK,CAACA,AAAQ,EAARA,CAAE,CAAC,EAAE,CAAM3K,EAAEtP,KAAK,CAAC,AAAD,EAC9Bia,CAAE,CAAC,EAAE,EACT,KAAK,EAAG,KAAK,EAAG3K,EAAI2K,EAAI,KACxB,MAAK,EAAc,OAAXV,EAAEC,KAAK,GAAW,CAAExZ,MAAOia,CAAE,CAAC,EAAE,CAAEC,KAAM,CAAA,CAAM,CACtD,MAAK,EAAGX,EAAEC,KAAK,GAAIhO,EAAIyO,CAAE,CAAC,EAAE,CAAEA,EAAK,CAAC,EAAE,CAAE,QACxC,MAAK,EAAGA,EAAKV,EAAEI,GAAG,CAACvc,GAAG,GAAImc,EAAEG,IAAI,CAACtc,GAAG,GAAI,QACxC,SACI,GAAI,CAAckS,CAAAA,EAAIA,AAAhBA,CAAAA,EAAIiK,EAAEG,IAAI,AAAD,EAASpoB,MAAM,CAAG,GAAKge,CAAC,CAACA,EAAEhe,MAAM,CAAG,EAAE,AAAD,GAAO2oB,CAAAA,AAAU,IAAVA,CAAE,CAAC,EAAE,EAAUA,AAAU,IAAVA,CAAE,CAAC,EAAE,AAAK,EAAI,CAAEV,EAAI,EAAG,QAAU,CAC3G,GAAIU,AAAU,IAAVA,CAAE,CAAC,EAAE,EAAW,CAAA,CAAC3K,GAAM2K,CAAE,CAAC,EAAE,CAAG3K,CAAC,CAAC,EAAE,EAAI2K,CAAE,CAAC,EAAE,CAAG3K,CAAC,CAAC,EAAE,EAAI,CAAEiK,EAAEC,KAAK,CAAGS,CAAE,CAAC,EAAE,CAAE,KAAO,CACrF,GAAIA,AAAU,IAAVA,CAAE,CAAC,EAAE,EAAUV,EAAEC,KAAK,CAAGlK,CAAC,CAAC,EAAE,CAAE,CAAEiK,EAAEC,KAAK,CAAGlK,CAAC,CAAC,EAAE,CAAEA,EAAI2K,EAAI,KAAO,CACpE,GAAI3K,GAAKiK,EAAEC,KAAK,CAAGlK,CAAC,CAAC,EAAE,CAAE,CAAEiK,EAAEC,KAAK,CAAGlK,CAAC,CAAC,EAAE,CAAEiK,EAAEI,GAAG,CAAC9nB,IAAI,CAACooB,GAAK,KAAO,CAC9D3K,CAAC,CAAC,EAAE,EAAEiK,EAAEI,GAAG,CAACvc,GAAG,GACnBmc,EAAEG,IAAI,CAACtc,GAAG,GAAI,QACtB,CACA6c,EAAKb,EAAKjpB,IAAI,CAACgpB,EAASI,EAC5B,CAAE,MAAOznB,EAAG,CAAEmoB,EAAK,CAAC,EAAGnoB,EAAE,CAAE0Z,EAAI,CAAG,QAAU,CAAE6N,EAAI/J,EAAI,CAAG,CACzD,GAAI2K,AAAQ,EAARA,CAAE,CAAC,EAAE,CAAM,MAAMA,CAAE,CAAC,EAAE,CAAE,MAAO,CAAEja,MAAOia,CAAE,CAAC,EAAE,CAAGA,CAAE,CAAC,EAAE,CAAG,KAAK,EAAGC,KAAM,CAAA,CAAK,CACnF,EAtBqD,CAAC/qB,EAAG4qB,EAAE,CAAG,CAAG,CAuBrE,EAGII,GAAsB,AAAC5pB,IAA+EG,KAAK,CAU3G0pB,GAA+B,SAAUpiB,CAAM,EAgB/C,SAASoiB,EAAcvpB,CAAO,EAE1B,IAAK,IADDwpB,EAAQ,EAAE,CACLxf,EAAK,EAAGA,EAAKpH,UAAUnC,MAAM,CAAEuJ,IACpCwf,CAAK,CAACxf,EAAK,EAAE,CAAGpH,SAAS,CAACoH,EAAG,CAFjC,IAIItF,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,GAAK,IAAI,AACrCoF,CAAAA,EAAM8kB,KAAK,CAAGA,EACd9kB,EAAM1E,OAAO,CAAGspB,GAAoBC,EAAc1a,cAAc,CAAE7O,GAElE,IAAK,IADDypB,EAAe/kB,EAAM1E,OAAO,CAACwpB,KAAK,EAAI,EAAE,CACnC1mB,EAAI,EAAGwE,EAAOmiB,EAAahpB,MAAM,CAAE2M,EAAkB,KAAK,EAAGC,EAAgB,KAAK,EAAGvK,EAAIwE,EAAM,EAAExE,EAEjGsK,AADLA,CAAAA,EAAkBqc,CAAY,CAAC3mB,EAAE,AAAD,EACXxC,IAAI,EAGzB+M,CAAAA,EAAgB9K,EAAuBH,KAAK,CAACgL,EAAgB9M,IAAI,CAAC,AAAD,GAE7DkpB,EAAMxoB,IAAI,CAAC,IAAIqM,EAAcD,IAGrC,OAAO1I,CACX,CAkSA,OArUA0jB,GAAsBmB,EAAepiB,GAmDrCoiB,EAAcnqB,SAAS,CAACsqB,GAAG,CAAG,SAAUxpB,CAAQ,CAAEkB,CAAW,EACzD,IAAI,CAACf,IAAI,CAAC,CACNC,KAAM,cACNoB,OAAQN,EACRlB,SAAUA,CACd,GACA,IAAI,CAACspB,KAAK,CAACxoB,IAAI,CAACd,GAChB,IAAI,CAACG,IAAI,CAAC,CACNC,KAAM,cACNoB,OAAQN,EACRlB,SAAUA,CACd,EACJ,EAOAqpB,EAAcnqB,SAAS,CAACuqB,KAAK,CAAG,SAAUvoB,CAAW,EACjD,IAAI,CAACf,IAAI,CAAC,CACNC,KAAM,aACNoB,OAAQN,CACZ,GACA,IAAI,CAACooB,KAAK,CAAC/oB,MAAM,CAAG,EACpB,IAAI,CAACJ,IAAI,CAAC,CACNC,KAAM,kBACNoB,OAAQN,CACZ,EACJ,EAcAmoB,EAAcnqB,SAAS,CAAC8B,MAAM,CAAG,SAAUC,CAAK,CAAEC,CAAW,MAtJDknB,EAASsB,EAAYC,EAAGC,EAuJhF,OAvJwDxB,EAuJvC,IAAI,CAvJ4CsB,EAuJ1C,KAAK,EAvJiDC,EAuJ9C,KAAK,EAvJ4CC,EAuJzC,WACnC,IAAIC,EACAvoB,EACAsB,EACAwE,EACA0iB,EACJ,OAAO3B,GAAY,IAAI,CAAE,SAAUziB,CAAE,EACjC,OAAQA,EAAG+iB,KAAK,EACZ,KAAK,EACDoB,EAAa,IAAI,CAAC/pB,OAAO,CAACiqB,OAAO,CAC7B,IAAI,CAACT,KAAK,CAACvmB,KAAK,GAAGgnB,OAAO,GAC1B,IAAI,CAACT,KAAK,CAACvmB,KAAK,GAChB9B,EAAMK,QAAQ,GAAKL,GACnBA,CAAAA,EAAMK,QAAQ,CAAGL,EAAMM,KAAK,CAAC,CAAA,EAAOL,EAAW,EAEnDI,EAAWL,EACX2B,EAAI,EAAGwE,EAAOyiB,EAAUtpB,MAAM,CAC9BmF,EAAG+iB,KAAK,CAAG,CACf,MAAK,EACD,GAAI,CAAE7lB,CAAAA,EAAIwE,CAAG,EAAI,MAAO,CAAC,EAAa,EAAE,AACxC1B,CAAAA,EAAG+iB,KAAK,CAAG,CACf,MAAK,EAED,OADA/iB,EAAGijB,IAAI,CAAC7nB,IAAI,CAAC,CAAC,EAAG,GAAK,EAAE,EACjB,CAAC,EAAa+oB,CAAS,CAACjnB,EAAE,CAAC5B,MAAM,CAACM,EAAUJ,GAAa,AACpE,MAAK,EAED,OADAwE,EAAGgjB,IAAI,GACA,CAAC,EAAa,EAAE,AAC3B,MAAK,EAOD,MANAoB,EAAUpkB,EAAGgjB,IAAI,GACjB,IAAI,CAACvoB,IAAI,CAAC,CACNC,KAAM,QACNoB,OAAQN,EACRD,MAAOA,CACX,GACM6oB,CACV,MAAK,EACDxoB,EAAWA,EAASA,QAAQ,CAC5BoE,EAAG+iB,KAAK,CAAG,CACf,MAAK,EAED,MADA,EAAE7lB,EACK,CAAC,EAAa,EAAE,AAC3B,MAAK,EAED,OADA3B,EAAMK,QAAQ,CAAGA,EACV,CAAC,EAAcL,EAAM,AACpC,CACJ,EACJ,EAnMG,GAAK0oB,CAAAA,GAAMA,CAAAA,EAAIxoB,OAAM,CAAC,EAAG,SAAUC,CAAO,CAAEC,CAAM,EACrD,SAAS2oB,EAAU/a,CAAK,EAAI,GAAI,CAAEga,EAAKW,EAAUhS,IAAI,CAAC3I,GAAS,CAAE,MAAOlO,EAAG,CAAEM,EAAON,EAAI,CAAE,CAC1F,SAASkpB,EAAShb,CAAK,EAAI,GAAI,CAAEga,EAAKW,EAAU,KAAQ,CAAC3a,GAAS,CAAE,MAAOlO,EAAG,CAAEM,EAAON,EAAI,CAAE,CAC7F,SAASkoB,EAAKjlB,CAAM,MAJTiL,CAIajL,CAAAA,EAAOmlB,IAAI,CAAG/nB,EAAQ4C,EAAOiL,KAAK,EAAIib,AAJnCjb,CAAAA,CAAhBA,EAIyDjL,EAAOiL,KAAK,YAJpC0a,EAAI1a,EAAQ,IAAI0a,EAAE,SAAUvoB,CAAO,EAAIA,EAAQ6N,EAAQ,EAAC,EAIlBhE,IAAI,CAAC+e,EAAWC,EAAW,CAC7GhB,EAAK,AAACW,CAAAA,EAAYA,EAAUjmB,KAAK,CAACykB,EAASsB,GAAc,EAAE,CAAA,EAAG9R,IAAI,GACtE,EA+LA,EAyBAyR,EAAcnqB,SAAS,CAACuC,UAAU,CAAG,SAAUR,CAAK,CAAES,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,EAC9F,IAAI2oB,EAAa,IAAI,CAAC/pB,OAAO,CAACiqB,OAAO,CAC7B,IAAI,CAACT,KAAK,CAACS,OAAO,GAClB,IAAI,CAACT,KAAK,CAClB,GAAIO,EAAUtpB,MAAM,CAAE,CAElB,IAAK,IADDgB,EAAQN,EAAMM,KAAK,GACdqB,EAAI,EAAGwE,EAAOyiB,EAAUtpB,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACjDinB,CAAS,CAACjnB,EAAE,CAACnB,UAAU,CAACF,EAAOG,EAAYC,EAAUC,EAAWV,GAChEK,EAAQA,EAAMD,QAAQ,AAE1BL,CAAAA,EAAMK,QAAQ,CAAGC,CACrB,CACA,OAAON,CACX,EAsBAooB,EAAcnqB,SAAS,CAAC2C,aAAa,CAAG,SAAUZ,CAAK,CAAEa,CAAO,CAAEH,CAAQ,CAAET,CAAW,EACnF,IAAI2oB,EAAa,IAAI,CAAC/pB,OAAO,CAACiqB,OAAO,CAC7B,IAAI,CAACT,KAAK,CAACS,OAAO,GAClB,IAAI,CAACT,KAAK,CAACvmB,KAAK,GACxB,GAAI8mB,EAAUtpB,MAAM,CAAE,CAElB,IAAK,IADDgB,EAAQN,EAAMM,KAAK,GACdqB,EAAI,EAAGwE,EAAOyiB,EAAUtpB,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACjDinB,CAAS,CAACjnB,EAAE,CAACf,aAAa,CAACN,EAAOO,EAASH,EAAUT,GACrDK,EAAQA,EAAMD,QAAQ,AAE1BL,CAAAA,EAAMK,QAAQ,CAAGC,CACrB,CACA,OAAON,CACX,EAsBAooB,EAAcnqB,SAAS,CAAC6C,UAAU,CAAG,SAAUd,CAAK,CAAEe,CAAI,CAAEL,CAAQ,CAAET,CAAW,EAC7E,IAAI2oB,EAAa,IAAI,CAAC/pB,OAAO,CAACiqB,OAAO,CAC7B,IAAI,CAACT,KAAK,CAACS,OAAO,GAClB,IAAI,CAACT,KAAK,CAACvmB,KAAK,GACxB,GAAI8mB,EAAUtpB,MAAM,CAAE,CAElB,IAAK,IADDgB,EAAQN,EAAMM,KAAK,GACdqB,EAAI,EAAGwE,EAAOyiB,EAAUtpB,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACjDinB,CAAS,CAACjnB,EAAE,CAACb,UAAU,CAACR,EAAOS,EAAML,EAAUT,GAC/CK,EAAQA,EAAMD,QAAQ,AAE1BL,CAAAA,EAAMK,QAAQ,CAAGC,CACrB,CACA,OAAON,CACX,EAkBAooB,EAAcnqB,SAAS,CAACgB,WAAW,CAAG,SAAUe,CAAK,CAAEC,CAAW,EAE9DooB,AADY,IAAI,CACVnpB,IAAI,CAAC,CACPC,KAAM,SACNoB,OAAQN,EACRD,MAAOA,CACX,GAKA,IAAK,IAJD4oB,EAAaP,AANL,IAAI,CAMOxpB,OAAO,CAACiqB,OAAO,CAC9BT,AAPI,IAAI,CAOFA,KAAK,CAACS,OAAO,GACnBT,AARI,IAAI,CAQFA,KAAK,CAACvmB,KAAK,GACrBzB,EAAWL,EAAMK,QAAQ,CACpBsB,EAAI,EAAGwE,EAAOyiB,EAAUtpB,MAAM,CAAEP,EAAW,KAAK,EAAG4C,EAAIwE,EAAM,EAAExE,EAEpEtB,EAAWtB,GADA6pB,CAAS,CAACjnB,EAAE,EACH1C,WAAW,CAACoB,EAAUJ,GAAaI,QAAQ,CAQnE,OANAL,EAAMK,QAAQ,CAAGA,EACjBgoB,AAfY,IAAI,CAeVnpB,IAAI,CAAC,CACPC,KAAM,cACNoB,OAAQN,EACRD,MAAOA,CACX,GACOA,CACX,EAUAooB,EAAcnqB,SAAS,CAACirB,MAAM,CAAG,SAAUnqB,CAAQ,CAAEkB,CAAW,EAC5D,IAAI2oB,EAAY,IAAI,CAACP,KAAK,CAC1B,IAAI,CAACnpB,IAAI,CAAC,CACNC,KAAM,iBACNoB,OAAQN,EACRlB,SAAUA,CACd,GACA6pB,EAAUxmB,MAAM,CAACwmB,EAAUpgB,OAAO,CAACzJ,GAAW,GAC9C,IAAI,CAACG,IAAI,CAAC,CACNC,KAAM,sBACNoB,OAAQN,EACRlB,SAAUA,CACd,EACJ,EASAqpB,EAAc1a,cAAc,CAAG,CAC3BvO,KAAM,OACV,EACOipB,CACX,EAAEhnB,GACFA,EAAuBF,YAAY,CAAC,QAASknB,IAuB7C,IAAIe,IACIzsB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAIA6jB,GAAuB,AAAC7qB,IAA+EG,KAAK,CAW5G2qB,GAAgC,SAAUrjB,CAAM,EAahD,SAASqjB,EAAexqB,CAAO,EAC3B,IAAI0E,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,GAAK,IAAI,CAErC,OADAoF,EAAM1E,OAAO,CAAGuqB,GAAqBC,EAAe3b,cAAc,CAAE7O,GAC7D0E,CACX,CAkMA,OAlNA4lB,GAAuBE,EAAgBrjB,GA4CvCqjB,EAAeprB,SAAS,CAACuC,UAAU,CAAG,SAAUR,CAAK,CAAES,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,EAC/F,IAAII,EAAWL,EAAMK,QAAQ,CACzBipB,EAAmBjpB,EAASiI,aAAa,CAAC,cAC1C7H,GAOJ,OANI,AAA4B,KAAA,IAArB6oB,EACPjpB,EAASsE,UAAU,CAAC,IAAI,CAAC1F,WAAW,CAACe,EAAMM,KAAK,IAAI6D,UAAU,GAAI,KAAK,EAAGlE,GAG1EI,EAASkJ,OAAO,CAAC,GAAGxH,MAAM,CAACrB,GAAW4oB,EAAkB3oB,EAAWV,GAEhED,CACX,EAoBAqpB,EAAeprB,SAAS,CAAC2C,aAAa,CAAG,SAAUZ,CAAK,CAAEa,CAAO,CAAEH,CAAQ,CAAET,CAAW,EACpF,IAAII,EAAWL,EAAMK,QAAQ,CACzBkpB,EAAuBlpB,EAAS4D,SAAS,CAAC,gBAAkB,EAAE,CAC9DG,EAAcpE,EAAM6H,cAAc,GAClC2hB,EAASxpB,EAAMqI,WAAW,KAAOkhB,EAAoBjqB,MAAM,CAC/D,GAAI,CAACkqB,EACD,CAAA,IAAK,IAAI7nB,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACnD,GAAIyC,CAAW,CAACzC,EAAE,GAAK4nB,CAAmB,CAAC5nB,EAAE,CAAE,CAC3C6nB,EAAQ,CAAA,EACR,KACJ,CACJ,CAEJ,GAAIA,EACA,OAAO,IAAI,CAACvqB,WAAW,CAACe,EAAOC,GAEnCmE,EAAczG,OAAO0G,IAAI,CAACxD,GAC1B,IAAK,IAAIc,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAE2C,EAAS,KAAK,EAAGxB,EAAa,KAAK,EAAG6oB,EAAmB,KAAK,EAAG3nB,EAAIwE,EAAM,EAAExE,EAAG,CAEvHM,EAASpB,CAAO,CADhBJ,EAAa2D,CAAW,CAACzC,EAAE,CACC,CAC5B2nB,EAAoBjpB,EAASiI,aAAa,CAAC,cAAe7H,IACtDJ,EAASgI,WAAW,GACxB,IAAK,IAAIrB,EAAI,EAAGyiB,EAAK/oB,EAAUuG,EAAOhF,EAAO3C,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EAAG,EAAEyiB,EAClEppB,EAASkJ,OAAO,CAAC,GAAGxH,MAAM,CAAC0nB,GAAKH,EAAkBrnB,CAAM,CAAC+E,EAAE,CAAE/G,EAErE,CACA,OAAOD,CACX,EAoBAqpB,EAAeprB,SAAS,CAAC6C,UAAU,CAAG,SAAUd,CAAK,CAAEe,CAAI,CAAEL,CAAQ,CAAET,CAAW,EAC9E,IAAImE,EAAcpE,EAAM6H,cAAc,GAClCxH,EAAWL,EAAMK,QAAQ,CACzBkpB,EAAuBlpB,EAAS4D,SAAS,CAAC,gBAAkB,EAAE,CAC9DulB,EAASxpB,EAAMqI,WAAW,KAAOkhB,EAAoBjqB,MAAM,CAC/D,GAAI,CAACkqB,EACD,CAAA,IAAK,IAAI7nB,EAAI,EAAGwE,EAAO/B,EAAY9E,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACnD,GAAIyC,CAAW,CAACzC,EAAE,GAAK4nB,CAAmB,CAAC5nB,EAAE,CAAE,CAC3C6nB,EAAQ,CAAA,EACR,KACJ,CACJ,CAEJ,GAAIA,EACA,OAAO,IAAI,CAACvqB,WAAW,CAACe,EAAOC,GAEnC,IAAK,IAAI0B,EAAI,EAAGgH,EAAKjI,EAAUyF,EAAOpF,EAAKzB,MAAM,CAAEwF,EAAM,KAAK,EAAGnD,EAAIwE,EAAM,EAAExE,EAAG,EAAEgH,EAE9E,GAAI7D,AADJA,CAAAA,EAAM/D,CAAI,CAACY,EAAE,AAAD,YACOE,MACfxB,EAASqE,SAAS,CAAC,GAAG3C,MAAM,CAAC4G,GAAK7D,QAGlC,IAAK,IAAIkC,EAAI,EAAGC,EAAO7C,EAAY9E,MAAM,CAAE0H,EAAIC,EAAM,EAAED,EACnD3G,EAASkJ,OAAO,CAAC,GAAGxH,MAAM,CAAC4G,GAAK3B,EAAGlC,CAAG,CAACV,CAAW,CAAC4C,EAAE,CAAC,CAAE/G,GAIpE,OAAOD,CACX,EAaAqpB,EAAeprB,SAAS,CAACgB,WAAW,CAAG,SAAUe,CAAK,CAAEC,CAAW,EAE/DlB,AADe,IAAI,CACVG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAOA,CAAM,GAClE,IAAIK,EAAWL,EAAMK,QAAQ,CAC7B,GAAIL,EAAMiJ,UAAU,CAAC,CAAC,cAAc,EAAG,CAKnC,IAAK,IAJDygB,EAAqB,AAAC1pB,CAAAA,EAAMyG,aAAa,CAAC,CAAC,cAAc,GAAK,CAAC,CAAA,EAC1DrC,WAAW,EAAI,EAAE,CACtBvD,EAAU,CAAC,EACXuD,EAAc,EAAE,CACXzC,EAAI,EAAGwE,EAAOujB,EAAkBpqB,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EACzDyC,EAAYvE,IAAI,CAAC,GAAK6pB,CAAiB,CAAC/nB,EAAE,EAE9C,IAAK,IAAIA,EAAI,EAAGwE,EAAOnG,EAAMqI,WAAW,GAAIvD,EAAM,KAAK,EAAGnD,EAAIwE,EAAM,EAAExE,EAClEmD,CAAAA,EAAM9E,EAAMuE,MAAM,CAAC5C,EAAC,GAEhBd,CAAAA,CAAO,CAACuD,CAAW,CAACzC,EAAE,CAAC,CAAGmD,CAAE,EAGpCzE,EAASoG,aAAa,GACtBpG,EAASsE,UAAU,CAAC9D,EACxB,KACK,CAED,IAAK,IADDA,EAAU,CAAC,EACNc,EAAI,EAAGwE,EAAOnG,EAAMqI,WAAW,GAAIvD,EAAM,KAAK,EAAGnD,EAAIwE,EAAM,EAAExE,EAClEmD,CAAAA,EAAM9E,EAAMuE,MAAM,CAAC5C,EAAC,GAEhBd,CAAAA,CAAO,CAAC,GAAGkB,MAAM,CAACJ,GAAG,CAAGmD,CAAE,CAGlCjE,CAAAA,EAAQuD,WAAW,CAAGpE,EAAM6H,cAAc,GAC1CxH,EAASoG,aAAa,GACtBpG,EAASsE,UAAU,CAAC9D,EACxB,CAEA,OADA9B,AAhCe,IAAI,CAgCVG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAOA,CAAM,GAChEA,CACX,EASAqpB,EAAe3b,cAAc,CAAG,CAC5BvO,KAAM,QACV,EACOkqB,CACX,EAAEjoB,GACFA,EAAuBF,YAAY,CAAC,SAAUmoB,IAsB9C,IAAIM,IACIjtB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAEAqkB,GAA2D,WAS3D,MAAOA,AARPA,CAAAA,GAAsBjsB,OAAO0f,MAAM,EAAI,SAASC,CAAC,EAC7C,IAAK,IAAIC,EAAG5b,EAAI,EAAGxE,EAAIsE,UAAUnC,MAAM,CAAEqC,EAAIxE,EAAGwE,IAE5C,IAAK,IAAI2D,KADTiY,EAAI9b,SAAS,CAACE,EAAE,CACKhE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACof,EAAGjY,IACzDgY,CAAAA,CAAC,CAAChY,EAAE,CAAGiY,CAAC,CAACjY,EAAE,AAAD,EAElB,OAAOgY,CACX,CAAA,EAC2B5a,KAAK,CAAC,IAAI,CAAEjB,UAC3C,EAgBIooB,GAA8B,SAAU7jB,CAAM,EAO9C,SAAS6jB,EAAahrB,CAAO,EACzB,IAAI0E,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,GAAK,IAAI,CAErC,OADAoF,EAAM1E,OAAO,CAAG+qB,GAAoBA,GAAoB,CAAC,EAAGC,EAAanc,cAAc,EAAG7O,GACnF0E,CACX,CA6HA,OAvIAomB,GAAqBE,EAAc7jB,GAgBnC6jB,EAAa5rB,SAAS,CAACgB,WAAW,CAAG,SAAUe,CAAK,CAAEC,CAAW,EAE7DlB,AADe,IAAI,CACVG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAOA,CAAM,GAKlE,IAAK,IAJDiW,EAAwBlX,AAFb,IAAI,CAEkBF,OAAO,CAACoX,qBAAqB,CAC9D6T,EAAkB/qB,AAHP,IAAI,CAGYF,OAAO,CAACirB,cAAc,EAC7C9pB,EAAM6H,cAAc,GACxBxH,EAAWL,EAAMK,QAAQ,CACpBsB,EAAI,EAAGwE,EAAO2jB,EAAexqB,MAAM,CAAEmB,EAAa,KAAK,EAAGkB,EAAIwE,EAAM,EAAExE,EAC3ElB,EAAaqpB,CAAc,CAACnoB,EAAE,CAC1BmoB,EAAethB,OAAO,CAAC/H,IAAe,GACtCJ,EAASqE,SAAS,CAACjE,EAAY1B,AATxB,IAAI,CAS6BgrB,aAAa,CAAC/pB,EAAOS,IAIrE,IAAK,IADDupB,EAAkBjrB,AAZP,IAAI,CAYYF,OAAO,CAACmrB,cAAc,EAAI,EAAE,CAClDroB,EAAI,EAAGwE,EAAO6jB,EAAe1qB,MAAM,CAAE2qB,EAAgB,KAAK,EAAGxT,EAAU,KAAK,EAAG9U,EAAIwE,EAAM,EAAExE,EAChGsoB,EAAgBD,CAAc,CAACroB,EAAE,CACjC8U,EAAUkB,GAAsBjB,YAAY,CAACuT,EAAcxT,OAAO,CAAER,GACpE5V,EAASqE,SAAS,CAACulB,EAAchoB,MAAM,CAAElD,AAhB9B,IAAI,CAgBmCmrB,oBAAoB,CAACzT,EAASzW,EAAOiqB,EAAcE,QAAQ,CAAEF,EAActW,MAAM,GAGvI,OADA5U,AAlBe,IAAI,CAkBVG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAOA,CAAM,GAChEA,CACX,EAkBA6pB,EAAa5rB,SAAS,CAAC8rB,aAAa,CAAG,SAAU/pB,CAAK,CAAES,CAAU,CAAEC,CAAQ,EACvD,KAAK,IAAlBA,GAAuBA,CAAAA,EAAW,CAAA,EAKtC,IAAK,IAJDuV,EAAwB,IAAI,CAACpX,OAAO,CAACoX,qBAAqB,CAC1DhU,EAAS,AAACjC,CAAAA,EAAMiE,SAAS,CAACxD,EAC1B,CAAA,IAAS,EAAE,AAAD,EACLqB,KAAK,CAACpB,EAAW,EAAIA,EAAW,GAChCiB,EAAI,EAAGwE,EAAOlE,EAAO3C,MAAM,CAAE8qB,EAAe,EAAE,CAAoBpQ,EAAO,KAAK,EAAGrY,EAAIwE,EAAM,EAAExE,EAElG,GAAI,AAAgB,UAAhB,MADJqY,CAAAA,EAAO/X,CAAM,CAACN,EAAE,AAAD,GAEXqY,AAAY,MAAZA,CAAI,CAAC,EAAE,CACP,GAAI,CAEAoQ,EAAgBC,AAN2C,KAM3BrQ,EAC5BoQ,EACAzS,GAAsBjB,YAAY,CAACsD,EAAKnE,SAAS,CAAC,GAAII,GAE1DhU,CAAM,CAACN,EAAE,CACL2Y,GAAyBR,cAAc,CAACsQ,EAAcpqB,EAC9D,CACA,MAAOyE,EAAI,CACPxC,CAAM,CAACN,EAAE,CAAG0L,GAChB,CAGR,OAAOpL,CACX,EAsBA4nB,EAAa5rB,SAAS,CAACisB,oBAAoB,CAAG,SAAUzT,CAAO,CAAEzW,CAAK,CAAEmqB,CAAQ,CAAExW,CAAM,EACnE,KAAK,IAAlBwW,GAAuBA,CAAAA,EAAW,CAAA,EACvB,KAAK,IAAhBxW,GAAqBA,CAAAA,EAAS3T,EAAMqI,WAAW,EAAC,EACpD8hB,EAAWA,GAAY,EAAIA,EAAW,EACtCxW,EAASA,GAAU,EAAIA,EAAS3T,EAAMqI,WAAW,GAAKsL,EAGtD,IAAK,IAFD1R,EAAS,EAAE,CACX5B,EAAWL,EAAMK,QAAQ,CACpBsB,EAAI,EAAGwE,EAAQwN,EAASwW,EAAWxoB,EAAIwE,EAAM,EAAExE,EACpD,GAAI,CACAM,CAAM,CAACN,EAAE,CAAG2Y,GAAyBR,cAAc,CAACrD,EAASpW,EACjE,CACA,MAAOoE,EAAI,CACPxC,CAAM,CAACN,EAAE,CAAG0L,GAChB,QACQ,CACJoJ,EAAU6D,GAAyBI,mBAAmB,CAACjE,EAAS,EAAG,EACvE,CAEJ,OAAOxU,CACX,EAUA4nB,EAAanc,cAAc,CAAG,CAC1BvO,KAAM,OACN8W,sBAAuB,CAAA,CAC3B,EACO4T,CACX,EAAEzoB,GACFA,EAAuBF,YAAY,CAAC,OAAQ2oB,IAuB5C,IAAIS,IACI5tB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAIAglB,GAAsB,AAAChsB,IAA+EG,KAAK,CAU3G8rB,GAA+B,SAAUxkB,CAAM,EAa/C,SAASwkB,EAAc3rB,CAAO,EAC1B,IAAI0E,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,GAAK,IAAI,CAErC,OADAoF,EAAM1E,OAAO,CAAG0rB,GAAoBC,EAAc9c,cAAc,CAAE7O,GAC3D0E,CACX,CA+FA,OA/GA+mB,GAAsBE,EAAexkB,GAkCrCwkB,EAAcvsB,SAAS,CAACgB,WAAW,CAAG,SAAUe,CAAK,CAAEC,CAAW,EAE9DlB,AADe,IAAI,CACVG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAOA,CAAM,GAClE,IAAIyqB,EAAU,EAAE,CACZhmB,EAAK1F,AAHM,IAAI,CAGDF,OAAO,CACrB6rB,EAAWjmB,EAAGimB,QAAQ,CACtBC,EAASlmB,EAAGkmB,MAAM,CAClBC,EAASnmB,EAAGmmB,MAAM,CACtB,GAAID,EAAOrrB,MAAM,CAAE,CAIf,IAAK,IAHDe,EAAWL,EAAMK,QAAQ,CACzBQ,EAAUb,EAAMmE,UAAU,GAC1BpD,EAAO,EAAE,CACJY,EAAI,EAAGwE,EAAOwkB,EAAOrrB,MAAM,CAAEuT,EAAQ,KAAK,EAAGgY,EAAc,KAAK,EAAGlpB,EAAIwE,EAAM,EAAExE,EAEpF,GADAkR,EAAQ8X,CAAM,CAAChpB,EAAE,CACbipB,CAAAA,GACA,OAAO/X,EAAMiY,QAAQ,EAAK,OAAOjY,EAAMkY,QAAQ,EAG/CppB,EAAI,GAAK,CAAC+oB,IACVrqB,EAAS0D,UAAU,GACnB1D,EAASiK,OAAO,CAACvJ,GACjBV,EAAS6J,qBAAqB,CAACugB,EAAS,CAAA,GACxC5pB,EAAUR,EAAS8D,UAAU,GAC7BpD,EAAO,EAAE,CACT0pB,EAAU,EAAE,EAEhBI,EAAehqB,CAAO,CAACgS,EAAM5Q,MAAM,CAAC,EAAI,EAAE,CAC1C,IAAK,IAAI+E,EAAI,EAAGC,EAAO4jB,EAAYvrB,MAAM,CAAE0a,EAAO,KAAK,EAAGlV,EAAM,KAAK,EAAGmD,EAAmB,KAAK,EAAGjB,EAAIC,EAAM,EAAED,EAAG,CAE9G,OAAQ,MADRgT,CAAAA,EAAO6Q,CAAW,CAAC7jB,EAAE,AAAD,GAEhB,QACI,QACJ,KAAK,UACL,IAAK,SACL,IAAK,SAET,CACI4jB,CAAAA,CAAAA,GACA,OAAO5Q,GAAS,OAAOnH,EAAMiY,QAAQ,AAAD,GAGpC9Q,GAAQnH,EAAMiY,QAAQ,EACtB9Q,GAAQnH,EAAMkY,QAAQ,GAClBL,GACA5lB,EAAM9E,EAAMuE,MAAM,CAACyC,GACnBiB,EAAmBjI,EAAMmI,mBAAmB,CAACnB,KAG7ClC,EAAMzE,EAASkE,MAAM,CAACyC,GACtBiB,EAAmB5H,EAAS8H,mBAAmB,CAACnB,IAEhDlC,IACA/D,EAAKlB,IAAI,CAACiF,GACV2lB,EAAQ5qB,IAAI,CAACoI,IAGzB,EAEJ5H,EAAS0D,UAAU,GACnB1D,EAASiK,OAAO,CAACvJ,GACjBV,EAAS6J,qBAAqB,CAACugB,EACnC,CAEA,OADA1rB,AA7De,IAAI,CA6DVG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAOA,CAAM,GAChEA,CACX,EASAwqB,EAAc9c,cAAc,CAAG,CAC3BvO,KAAM,QACNwrB,OAAQ,EAAE,AACd,EACOH,CACX,EAAEppB,GACFA,EAAuBF,YAAY,CAAC,QAASspB,IAuB7C,IAAIQ,IACItuB,EAAgB,SAAUY,CAAC,CAC3B6H,CAAC,EAOD,MAAOzI,AANHA,CAAAA,EAAgBiB,OAAOyH,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaxD,OAAS,SAAUvE,CAAC,CAC1D6H,CAAC,EAAI7H,EAAE+H,SAAS,CAAGF,CAAG,GACd,SAAU7H,CAAC,CACnB6H,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOxH,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgH,EAC/DG,IAAIhI,CAAAA,CAAC,CAACgI,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIhI,EAAG6H,EAC5B,EACO,SAAU7H,CAAC,CAAE6H,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIuY,UAAU,uBAAyBC,OAAOxY,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACzC,WAAW,CAAGxF,CAAG,CADtCZ,EAAcY,EAAG6H,GAEjB7H,EAAEW,SAAS,CAAGkH,AAAM,OAANA,EAAaxH,OAAO6H,MAAM,CAACL,GAAMI,CAAAA,EAAGtH,SAAS,CAAGkH,EAAElH,SAAS,CAAE,IAAIsH,CAAG,CACtF,GAKA0lB,GAAqB,AAAC1sB,IAA+EG,KAAK,CAU1GwsB,GAA8B,SAAUllB,CAAM,EAa9C,SAASklB,EAAarsB,CAAO,EACzB,IAAI0E,EAAQyC,EAAO7H,IAAI,CAAC,IAAI,GAAK,IAAI,CAErC,OADAoF,EAAM1E,OAAO,CAAGosB,GAAmBC,EAAaxd,cAAc,CAAE7O,GACzD0E,CACX,CA4OA,OA5PAynB,GAAqBE,EAAcllB,GAsBnCklB,EAAaC,SAAS,CAAG,SAAU5tB,CAAC,CAAE4H,CAAC,EACnC,MAAQ,AAAC5H,CAAAA,GAAK,CAAA,EAAM4H,CAAAA,GAAK,CAAA,EAAK,GAC1B,CAAA,CAAA,AAAC5H,CAAAA,GAAK,CAAA,EAAM4H,CAAAA,GAAK,CAAA,CAAC,CAE1B,EACA+lB,EAAaE,UAAU,CAAG,SAAU7tB,CAAC,CAAE4H,CAAC,EACpC,MAAQ,AAACA,CAAAA,GAAK,CAAA,EAAM5H,CAAAA,GAAK,CAAA,EAAK,GAC1B,CAAA,CAAA,AAAC4H,CAAAA,GAAK,CAAA,EAAM5H,CAAAA,GAAK,CAAA,CAAC,CAE1B,EAiBA2tB,EAAajtB,SAAS,CAACotB,gBAAgB,CAAG,SAAUrrB,CAAK,EAGrD,IAAK,IAFDe,EAAOf,EAAMoI,OAAO,GACpBkjB,EAAgB,EAAE,CACb3pB,EAAI,EAAGwE,EAAOpF,EAAKzB,MAAM,CAAEqC,EAAIwE,EAAM,EAAExE,EAC5C2pB,EAAczrB,IAAI,CAAC,CACf4L,MAAO9J,EACPmD,IAAK/D,CAAI,CAACY,EAAE,AAChB,GAEJ,OAAO2pB,CACX,EAuBAJ,EAAajtB,SAAS,CAACuC,UAAU,CAAG,SAAUR,CAAK,CAAES,CAAU,CAAEC,CAAQ,CAAEC,CAAS,CAAEV,CAAW,EAC7F,IACIwE,EAAK1F,AADM,IAAI,CACDF,OAAO,CACrB0sB,EAAgB9mB,EAAG8mB,aAAa,CAChCC,EAAgB/mB,EAAG+mB,aAAa,CAgBpC,OAfI/qB,IAAe8qB,IACXC,GACAxrB,EAAMK,QAAQ,CAACkJ,OAAO,CAAC9I,EAAYC,EAAUC,GAC7CX,EAAMK,QAAQ,CAACqE,SAAS,CAAC8mB,EAAezsB,AAPjC,IAAI,CAQNE,WAAW,CAAC,IA3uOkB8G,EA2uOC,CAChClF,QAASb,EACJmE,UAAU,CAAC,CAAConB,EAAeC,EAAc,CAClD,IACKnrB,QAAQ,CACR4D,SAAS,CAACunB,KAGfzsB,AAhBO,IAAI,CAgBFE,WAAW,CAACe,EAAOC,IAG7BD,CACX,EAoBAkrB,EAAajtB,SAAS,CAAC2C,aAAa,CAAG,SAAUZ,CAAK,CAAEa,CAAO,CAAEH,CAAQ,CAAET,CAAW,EAClF,IACIwE,EAAK1F,AADM,IAAI,CACDF,OAAO,CACrB0sB,EAAgB9mB,EAAG8mB,aAAa,CAChCC,EAAgB/mB,EAAG+mB,aAAa,CAChCpnB,EAAczG,OAAO0G,IAAI,CAACxD,GAiB9B,OAhBIuD,EAAYoE,OAAO,CAAC+iB,GAAiB,KACjCC,GACA3qB,CAAO,CAACuD,CAAW,CAAC,EAAE,CAAC,CAAC9E,MAAM,EAC9BU,EAAMK,QAAQ,CAACsE,UAAU,CAAC9D,EAASH,GACnCV,EAAMK,QAAQ,CAACqE,SAAS,CAAC8mB,EAAezsB,AATjC,IAAI,CAUNE,WAAW,CAAC,IAtxOkB8G,EAsxOC,CAChClF,QAASb,EACJmE,UAAU,CAAC,CAAConB,EAAeC,EAAc,CAClD,IACKnrB,QAAQ,CACR4D,SAAS,CAACunB,KAGfzsB,AAlBO,IAAI,CAkBFE,WAAW,CAACe,EAAOC,IAG7BD,CACX,EAoBAkrB,EAAajtB,SAAS,CAAC6C,UAAU,CAAG,SAAUd,CAAK,CAAEe,CAAI,CAAEL,CAAQ,CAAET,CAAW,EAC5E,IACIwE,EAAK1F,AADM,IAAI,CACDF,OAAO,CACrB0sB,EAAgB9mB,EAAG8mB,aAAa,CAChCC,EAAgB/mB,EAAG+mB,aAAa,CAepC,OAdIA,GACAzqB,EAAKzB,MAAM,EACXU,EAAMK,QAAQ,CAACiK,OAAO,CAACvJ,EAAML,GAC7BV,EAAMK,QAAQ,CAACqE,SAAS,CAAC8mB,EAAezsB,AAP7B,IAAI,CAQVE,WAAW,CAAC,IA/zOsB8G,EA+zOH,CAChClF,QAASb,EACJmE,UAAU,CAAC,CAAConB,EAAeC,EAAc,CAClD,IACKnrB,QAAQ,CACR4D,SAAS,CAACunB,KAGfzsB,AAhBW,IAAI,CAgBNE,WAAW,CAACe,EAAOC,GAEzBD,CACX,EAaAkrB,EAAajtB,SAAS,CAACgB,WAAW,CAAG,SAAUe,CAAK,CAAEC,CAAW,EAG7DlB,AADe,IAAI,CACVG,IAAI,CAAC,CAAEC,KAAM,SAAUoB,OAAQN,EAAaD,MAAOA,CAAM,GAClE,IAHIyE,EAGAL,EAAcpE,EAAM6H,cAAc,GAClCnE,EAAW1D,EAAMqI,WAAW,GAC5BijB,EAAgB,IAAI,CAACD,gBAAgB,CAACrrB,GACtC0S,EAAK3T,AALM,IAAI,CAKDF,OAAO,CACrB4sB,EAAY/Y,EAAG+Y,SAAS,CACxBF,EAAgB7Y,EAAG6Y,aAAa,CAChCC,EAAgB9Y,EAAG8Y,aAAa,CAChCE,EAAWD,AAAc,QAAdA,EACPP,EAAaC,SAAS,CACtBD,EAAaE,UAAU,CAC3BO,EAAqBvnB,EAAYoE,OAAO,CAAC+iB,GACzClrB,EAAWL,EAAMK,QAAQ,CAI7B,GAH2B,KAAvBsrB,GACAL,EAAc9f,IAAI,CAAC,SAAUjO,CAAC,CAAE4H,CAAC,EAAI,OAAOumB,EAAQnuB,EAAEuH,GAAG,CAAC6mB,EAAmB,CAAExmB,EAAEL,GAAG,CAAC6mB,EAAmB,CAAG,GAE3GH,EAAe,CAEf,IAAK,IADDvpB,EAAS,EAAE,CACNN,EAAI,EAAGA,EAAI+B,EAAU,EAAE/B,EAC5BM,CAAM,CAACqpB,CAAa,CAAC3pB,EAAE,CAAC8J,KAAK,CAAC,CAAG9J,EAErCtB,EAASsE,UAAU,CAAEF,CAAAA,AAASA,CAATA,EAAK,CAAC,CAAA,CAAK,CAAC+mB,EAAc,CAAGvpB,EAAQwC,CAAC,EAC/D,KACK,CAID,IAAK,IAHDmnB,EAAkB,EAAE,CACpB7qB,EAAO,EAAE,CACT8qB,EAAe,KAAK,EACflqB,EAAI,EAAGA,EAAI+B,EAAU,EAAE/B,EAC5BkqB,EAAeP,CAAa,CAAC3pB,EAAE,CAC/BiqB,EAAgB/rB,IAAI,CAACQ,EAAS8H,mBAAmB,CAAC0jB,EAAapgB,KAAK,GACpE1K,EAAKlB,IAAI,CAACgsB,EAAa/mB,GAAG,EAE9BzE,EAASiK,OAAO,CAACvJ,EAAM,GACvBV,EAAS6J,qBAAqB,CAAC0hB,EACnC,CAEA,OADA7sB,AApCe,IAAI,CAoCVG,IAAI,CAAC,CAAEC,KAAM,cAAeoB,OAAQN,EAAaD,MAAOA,CAAM,GAChEA,CACX,EASAkrB,EAAaxd,cAAc,CAAG,CAC1BvO,KAAM,OACNssB,UAAW,OACXF,cAAe,GACnB,EACOL,CACX,EAAE9pB,GACFA,EAAuBF,YAAY,CAAC,OAAQgqB,IA4B5C,IAAIY,GAAKvtB,GACTutB,CAAAA,GAAEnvB,aAAa,CAAGmvB,GAAEnvB,aAAa,EAAI+P,EACrCof,GAAElvB,aAAa,CAAGkvB,GAAElvB,aAAa,EAAIsU,GACrC4a,GAAE3a,UAAU,CAAG2a,GAAE3a,UAAU,EAAI0C,GAC/BiY,GAAEtvB,YAAY,CAAGsvB,GAAEtvB,YAAY,EAAI4E,EACnC0qB,GAAE9X,QAAQ,CAAG8X,GAAE9X,QAAQ,EAn1L2BA,GAo1LlD8X,GAAE/lB,SAAS,CAAG+lB,GAAE/lB,SAAS,EAj7O0BA,EAk7OnD+lB,GAAEtO,OAAO,CAAGsO,GAAEtO,OAAO,EA34G+BA,GA44GvB,IAAInf,GAAmBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,GAET"}