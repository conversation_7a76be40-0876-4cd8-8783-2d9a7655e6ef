{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/pathfinder\n * @requires highcharts\n *\n * Pathfinder\n *\n * (c) 2016-2025 Øystein Moseng\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Point\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/pathfinder\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Point\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/pathfinder\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Point\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Point\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__260__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 260:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ pathfinder_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Gantt/Connection.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach;\n/* *\n *\n *  Constants\n *\n * */\nvar deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad, max = Math.max, min = Math.min;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Connection class. Used internally to represent a connection between two\n * points.\n *\n * @private\n * @class\n * @name Highcharts.Connection\n *\n * @param {Highcharts.Point} from\n *        Connection runs from this Point.\n *\n * @param {Highcharts.Point} to\n *        Connection runs to this Point.\n *\n * @param {Highcharts.ConnectorsOptions} [options]\n *        Connection options.\n */\nvar Connection = /** @class */ (function () {\n    function Connection(from, to, options) {\n        this.init(from, to, options);\n    }\n    /**\n     * Initialize the Connection object. Used as constructor only.\n     *\n     * @function Highcharts.Connection#init\n     *\n     * @param {Highcharts.Point} from\n     *        Connection runs from this Point.\n     *\n     * @param {Highcharts.Point} to\n     *        Connection runs to this Point.\n     *\n     * @param {Highcharts.ConnectorsOptions} [options]\n     *        Connection options.\n     */\n    Connection.prototype.init = function (from, to, options) {\n        this.fromPoint = from;\n        this.toPoint = to;\n        this.options = options;\n        this.chart = from.series.chart;\n        this.pathfinder = this.chart.pathfinder;\n    };\n    /**\n     * Add (or update) this connection's path on chart. Stores reference to the\n     * created element on this.graphics.path.\n     *\n     * @function Highcharts.Connection#renderPath\n     *\n     * @param {Highcharts.SVGPathArray} path\n     *        Path to render, in array format. E.g. ['M', 0, 0, 'L', 10, 10]\n     *\n     * @param {Highcharts.SVGAttributes} [attribs]\n     *        SVG attributes for the path.\n     *\n     * @param {Partial<Highcharts.AnimationOptionsObject>} [animation]\n     *        Animation options for the rendering.\n     */\n    Connection.prototype.renderPath = function (path, attribs) {\n        var connection = this,\n            chart = this.chart,\n            styledMode = chart.styledMode,\n            pathfinder = this.pathfinder,\n            anim = {};\n        var pathGraphic = connection.graphics && connection.graphics.path;\n        // Add the SVG element of the pathfinder group if it doesn't exist\n        if (!pathfinder.group) {\n            pathfinder.group = chart.renderer.g()\n                .addClass('highcharts-pathfinder-group')\n                .attr({ zIndex: -1 })\n                .add(chart.seriesGroup);\n        }\n        // Shift the group to compensate for plot area.\n        // Note: Do this always (even when redrawing a path) to avoid issues\n        // when updating chart in a way that changes plot metrics.\n        pathfinder.group.translate(chart.plotLeft, chart.plotTop);\n        // Create path if does not exist\n        if (!(pathGraphic && pathGraphic.renderer)) {\n            pathGraphic = chart.renderer.path()\n                .add(pathfinder.group);\n            if (!styledMode) {\n                pathGraphic.attr({\n                    opacity: 0\n                });\n            }\n        }\n        // Set path attribs and animate to the new path\n        pathGraphic.attr(attribs);\n        anim.d = path;\n        if (!styledMode) {\n            anim.opacity = 1;\n        }\n        pathGraphic.animate(anim);\n        // Store reference on connection\n        this.graphics = this.graphics || {};\n        this.graphics.path = pathGraphic;\n    };\n    /**\n     * Calculate and add marker graphics for connection to the chart. The\n     * created/updated elements are stored on this.graphics.start and\n     * this.graphics.end.\n     *\n     * @function Highcharts.Connection#addMarker\n     *\n     * @param {string} type\n     *        Marker type, either 'start' or 'end'.\n     *\n     * @param {Highcharts.ConnectorsMarkerOptions} options\n     *        All options for this marker. Not calculated or merged with other\n     *        options.\n     *\n     * @param {Highcharts.SVGPathArray} path\n     *        Connection path in array format. This is used to calculate the\n     *        rotation angle of the markers.\n     */\n    Connection.prototype.addMarker = function (type, options, path) {\n        var connection = this,\n            chart = connection.fromPoint.series.chart,\n            pathfinder = chart.pathfinder,\n            renderer = chart.renderer,\n            point = (type === 'start' ?\n                connection.fromPoint :\n                connection.toPoint),\n            anchor = point.getPathfinderAnchorPoint(options);\n        var markerVector,\n            radians,\n            rotation,\n            box,\n            width,\n            height,\n            pathVector,\n            segment;\n        if (!options.enabled) {\n            return;\n        }\n        // Last vector before start/end of path, used to get angle\n        if (type === 'start') {\n            segment = path[1];\n        }\n        else { // 'end'\n            segment = path[path.length - 2];\n        }\n        if (segment && segment[0] === 'M' || segment[0] === 'L') {\n            pathVector = {\n                x: segment[1],\n                y: segment[2]\n            };\n            // Get angle between pathVector and anchor point and use it to\n            // create marker position.\n            radians = point.getRadiansToVector(pathVector, anchor);\n            markerVector = point.getMarkerVector(radians, options.radius, anchor);\n            // Rotation of marker is calculated from angle between pathVector\n            // and markerVector.\n            // (Note:\n            //  Used to recalculate radians between markerVector and pathVector,\n            //  but this should be the same as between pathVector and anchor.)\n            rotation = -radians / deg2rad;\n            if (options.width && options.height) {\n                width = options.width;\n                height = options.height;\n            }\n            else {\n                width = height = options.radius * 2;\n            }\n            // Add graphics object if it does not exist\n            connection.graphics = connection.graphics || {};\n            box = {\n                x: markerVector.x - (width / 2),\n                y: markerVector.y - (height / 2),\n                width: width,\n                height: height,\n                rotation: rotation,\n                rotationOriginX: markerVector.x,\n                rotationOriginY: markerVector.y\n            };\n            if (!connection.graphics[type]) {\n                // Create new marker element\n                connection.graphics[type] = renderer\n                    .symbol(options.symbol)\n                    .addClass('highcharts-point-connecting-path-' + type + '-marker' +\n                    ' highcharts-color-' + this.fromPoint.colorIndex)\n                    .attr(box)\n                    .add(pathfinder.group);\n                if (!renderer.styledMode) {\n                    connection.graphics[type].attr({\n                        fill: options.color || connection.fromPoint.color,\n                        stroke: options.lineColor,\n                        'stroke-width': options.lineWidth,\n                        opacity: 0\n                    })\n                        .animate({\n                        opacity: 1\n                    }, point.series.options.animation);\n                }\n            }\n            else {\n                connection.graphics[type].animate(box);\n            }\n        }\n    };\n    /**\n     * Calculate and return connection path.\n     * Note: Recalculates chart obstacles on demand if they aren't calculated.\n     *\n     * @function Highcharts.Connection#getPath\n     *\n     * @param {Highcharts.ConnectorsOptions} options\n     *        Connector options. Not calculated or merged with other options.\n     *\n     * @return {object|undefined}\n     *         Calculated SVG path data in array format.\n     */\n    Connection.prototype.getPath = function (options) {\n        var pathfinder = this.pathfinder,\n            chart = this.chart,\n            algorithm = pathfinder.algorithms[options.type];\n        var chartObstacles = pathfinder.chartObstacles;\n        if (typeof algorithm !== 'function') {\n            error('\"' + options.type + '\" is not a Pathfinder algorithm.');\n            return {\n                path: [],\n                obstacles: []\n            };\n        }\n        // This function calculates obstacles on demand if they don't exist\n        if (algorithm.requiresObstacles && !chartObstacles) {\n            chartObstacles =\n                pathfinder.chartObstacles =\n                    pathfinder.getChartObstacles(options);\n            // If the algorithmMargin was computed, store the result in default\n            // options.\n            chart.options.connectors.algorithmMargin =\n                options.algorithmMargin;\n            // Cache some metrics too\n            pathfinder.chartObstacleMetrics =\n                pathfinder.getObstacleMetrics(chartObstacles);\n        }\n        // Get the SVG path\n        return algorithm(\n        // From\n        this.fromPoint.getPathfinderAnchorPoint(options.startMarker), \n        // To\n        this.toPoint.getPathfinderAnchorPoint(options.endMarker), merge({\n            chartObstacles: chartObstacles,\n            lineObstacles: pathfinder.lineObstacles || [],\n            obstacleMetrics: pathfinder.chartObstacleMetrics,\n            hardBounds: {\n                xMin: 0,\n                xMax: chart.plotWidth,\n                yMin: 0,\n                yMax: chart.plotHeight\n            },\n            obstacleOptions: {\n                margin: options.algorithmMargin\n            },\n            startDirectionX: pathfinder.getAlgorithmStartDirection(options.startMarker)\n        }, options));\n    };\n    /**\n     * (re)Calculate and (re)draw the connection.\n     *\n     * @function Highcharts.Connection#render\n     */\n    Connection.prototype.render = function () {\n        var connection = this,\n            fromPoint = connection.fromPoint,\n            series = fromPoint.series,\n            chart = series.chart,\n            pathfinder = chart.pathfinder,\n            attribs = {};\n        var options = merge(chart.options.connectors,\n            series.options.connectors,\n            fromPoint.options.connectors,\n            connection.options);\n        // Set path attribs\n        if (!chart.styledMode) {\n            attribs.stroke = options.lineColor || fromPoint.color;\n            attribs['stroke-width'] = options.lineWidth;\n            if (options.dashStyle) {\n                attribs.dashstyle = options.dashStyle;\n            }\n        }\n        attribs['class'] = // eslint-disable-line dot-notation\n            'highcharts-point-connecting-path ' +\n                'highcharts-color-' + fromPoint.colorIndex;\n        options = merge(attribs, options);\n        // Set common marker options\n        if (!defined(options.marker.radius)) {\n            options.marker.radius = min(max(Math.ceil((options.algorithmMargin || 8) / 2) - 1, 1), 5);\n        }\n        // Get the path\n        var pathResult = connection.getPath(options),\n            path = pathResult.path;\n        // Always update obstacle storage with obstacles from this path.\n        // We don't know if future calls will need this for their algorithm.\n        if (pathResult.obstacles) {\n            pathfinder.lineObstacles =\n                pathfinder.lineObstacles || [];\n            pathfinder.lineObstacles =\n                pathfinder.lineObstacles.concat(pathResult.obstacles);\n        }\n        // Add the calculated path to the pathfinder group\n        connection.renderPath(path, attribs);\n        // Render the markers\n        connection.addMarker('start', merge(options.marker, options.startMarker), path);\n        connection.addMarker('end', merge(options.marker, options.endMarker), path);\n    };\n    /**\n     * Destroy connection by destroying the added graphics elements.\n     *\n     * @function Highcharts.Connection#destroy\n     */\n    Connection.prototype.destroy = function () {\n        if (this.graphics) {\n            objectEach(this.graphics, function (val) {\n                val.destroy();\n            });\n            delete this.graphics;\n        }\n    };\n    return Connection;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Gantt_Connection = (Connection);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * The default pathfinder algorithm to use for a chart. It is possible to define\n * your own algorithms by adding them to the\n * `Highcharts.Pathfinder.prototype.algorithms`\n * object before the chart has been created.\n *\n * The default algorithms are as follows:\n *\n * `straight`:      Draws a straight line between the connecting\n *                  points. Does not avoid other points when drawing.\n *\n * `simpleConnect`: Finds a path between the points using right angles\n *                  only. Takes only starting/ending points into\n *                  account, and will not avoid other points.\n *\n * `fastAvoid`:     Finds a path between the points using right angles\n *                  only. Will attempt to avoid other points, but its\n *                  focus is performance over accuracy. Works well with\n *                  less dense datasets.\n *\n * @typedef {\"fastAvoid\"|\"simpleConnect\"|\"straight\"|string} Highcharts.PathfinderTypeValue\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Series/PathUtilities.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar getLinkPath = {\n    'default': getDefaultPath,\n    straight: getStraightPath,\n    curved: getCurvedPath\n};\n/**\n *\n */\nfunction getDefaultPath(pathParams) {\n    var x1 = pathParams.x1,\n        y1 = pathParams.y1,\n        x2 = pathParams.x2,\n        y2 = pathParams.y2,\n        _a = pathParams.width,\n        width = _a === void 0 ? 0 : _a,\n        _b = pathParams.inverted,\n        inverted = _b === void 0 ? false : _b,\n        radius = pathParams.radius,\n        parentVisible = pathParams.parentVisible;\n    var path = [\n            ['M',\n        x1,\n        y1],\n            ['L',\n        x1,\n        y1],\n            ['C',\n        x1,\n        y1,\n        x1,\n        y2,\n        x1,\n        y2],\n            ['L',\n        x1,\n        y2],\n            ['C',\n        x1,\n        y1,\n        x1,\n        y2,\n        x1,\n        y2],\n            ['L',\n        x1,\n        y2]\n        ];\n    return parentVisible ?\n        applyRadius([\n            ['M', x1, y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y2],\n            ['L', x2, y2]\n        ], radius) :\n        path;\n}\n/**\n *\n */\nfunction getStraightPath(pathParams) {\n    var x1 = pathParams.x1,\n        y1 = pathParams.y1,\n        x2 = pathParams.x2,\n        y2 = pathParams.y2,\n        _a = pathParams.width,\n        width = _a === void 0 ? 0 : _a,\n        _b = pathParams.inverted,\n        inverted = _b === void 0 ? false : _b,\n        parentVisible = pathParams.parentVisible;\n    return parentVisible ? [\n        ['M', x1, y1],\n        ['L', x1 + width * (inverted ? -1 : 1), y2],\n        ['L', x2, y2]\n    ] : [\n        ['M', x1, y1],\n        ['L', x1, y2],\n        ['L', x1, y2]\n    ];\n}\n/**\n *\n */\nfunction getCurvedPath(pathParams) {\n    var x1 = pathParams.x1,\n        y1 = pathParams.y1,\n        x2 = pathParams.x2,\n        y2 = pathParams.y2,\n        _a = pathParams.offset,\n        offset = _a === void 0 ? 0 : _a,\n        _b = pathParams.width,\n        width = _b === void 0 ? 0 : _b,\n        _c = pathParams.inverted,\n        inverted = _c === void 0 ? false : _c,\n        parentVisible = pathParams.parentVisible;\n    return parentVisible ?\n        [\n            ['M', x1, y1],\n            [\n                'C',\n                x1 + offset,\n                y1,\n                x1 - offset + width * (inverted ? -1 : 1),\n                y2,\n                x1 + width * (inverted ? -1 : 1),\n                y2\n            ],\n            ['L', x2, y2]\n        ] :\n        [\n            ['M', x1, y1],\n            ['C', x1, y1, x1, y2, x1, y2],\n            ['L', x2, y2]\n        ];\n}\n/**\n * General function to apply corner radius to a path\n * @private\n */\nfunction applyRadius(path, r) {\n    var d = [];\n    for (var i = 0; i < path.length; i++) {\n        var x = path[i][1];\n        var y = path[i][2];\n        if (typeof x === 'number' && typeof y === 'number') {\n            // MoveTo\n            if (i === 0) {\n                d.push(['M', x, y]);\n            }\n            else if (i === path.length - 1) {\n                d.push(['L', x, y]);\n                // CurveTo\n            }\n            else if (r) {\n                var prevSeg = path[i - 1];\n                var nextSeg = path[i + 1];\n                if (prevSeg && nextSeg) {\n                    var x1 = prevSeg[1],\n                        y1 = prevSeg[2],\n                        x2 = nextSeg[1],\n                        y2 = nextSeg[2];\n                    // Only apply to breaks\n                    if (typeof x1 === 'number' &&\n                        typeof x2 === 'number' &&\n                        typeof y1 === 'number' &&\n                        typeof y2 === 'number' &&\n                        x1 !== x2 &&\n                        y1 !== y2) {\n                        var directionX = x1 < x2 ? 1 : -1,\n                            directionY = y1 < y2 ? 1 : -1;\n                        d.push([\n                            'L',\n                            x - directionX * Math.min(Math.abs(x - x1), r),\n                            y - directionY * Math.min(Math.abs(y - y1), r)\n                        ], [\n                            'C',\n                            x,\n                            y,\n                            x,\n                            y,\n                            x + directionX * Math.min(Math.abs(x - x2), r),\n                            y + directionY * Math.min(Math.abs(y - y2), r)\n                        ]);\n                    }\n                }\n                // LineTo\n            }\n            else {\n                d.push(['L', x, y]);\n            }\n        }\n    }\n    return d;\n}\nvar PathUtilities = {\n    applyRadius: applyRadius,\n    getLinkPath: getLinkPath\n};\n/* harmony default export */ var Series_PathUtilities = (PathUtilities);\n\n;// ./code/es5/es-modules/Gantt/PathfinderAlgorithms.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\nvar PathfinderAlgorithms_min = Math.min, PathfinderAlgorithms_max = Math.max, abs = Math.abs;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get index of last obstacle before xMin. Employs a type of binary search, and\n * thus requires that obstacles are sorted by xMin value.\n *\n * @private\n * @function findLastObstacleBefore\n *\n * @param {Array<object>} obstacles\n *        Array of obstacles to search in.\n *\n * @param {number} xMin\n *        The xMin threshold.\n *\n * @param {number} [startIx]\n *        Starting index to search from. Must be within array range.\n *\n * @return {number}\n *         The index of the last obstacle element before xMin.\n */\nfunction findLastObstacleBefore(obstacles, xMin, startIx) {\n    var min = xMin - 0.0000001; // Make sure we include all obstacles at xMin\n        var left = startIx || 0, // Left limit\n        right = obstacles.length - 1, // Right limit\n        cursor,\n        cmp;\n    while (left <= right) {\n        cursor = (right + left) >> 1;\n        cmp = min - obstacles[cursor].xMin;\n        if (cmp > 0) {\n            left = cursor + 1;\n        }\n        else if (cmp < 0) {\n            right = cursor - 1;\n        }\n        else {\n            return cursor;\n        }\n    }\n    return left > 0 ? left - 1 : 0;\n}\n/**\n * Test if a point lays within an obstacle.\n *\n * @private\n * @function pointWithinObstacle\n *\n * @param {Object} obstacle\n *        Obstacle to test.\n *\n * @param {Highcharts.Point} point\n *        Point with x/y props.\n *\n * @return {boolean}\n *         Whether point is within the obstacle or not.\n */\nfunction pointWithinObstacle(obstacle, point) {\n    return (point.x <= obstacle.xMax &&\n        point.x >= obstacle.xMin &&\n        point.y <= obstacle.yMax &&\n        point.y >= obstacle.yMin);\n}\n/**\n * Find the index of an obstacle that wraps around a point.\n * Returns -1 if not found.\n *\n * @private\n * @function findObstacleFromPoint\n *\n * @param {Array<object>} obstacles\n *        Obstacles to test.\n *\n * @param {Highcharts.Point} point\n *        Point with x/y props.\n *\n * @return {number}\n *         Ix of the obstacle in the array, or -1 if not found.\n */\nfunction findObstacleFromPoint(obstacles, point) {\n    var i = findLastObstacleBefore(obstacles,\n        point.x + 1) + 1;\n    while (i--) {\n        if (obstacles[i].xMax >= point.x &&\n            // Optimization using lazy evaluation\n            pointWithinObstacle(obstacles[i], point)) {\n            return i;\n        }\n    }\n    return -1;\n}\n/**\n * Get SVG path array from array of line segments.\n *\n * @private\n * @function pathFromSegments\n *\n * @param {Array<object>} segments\n *        The segments to build the path from.\n *\n * @return {Highcharts.SVGPathArray}\n *         SVG path array as accepted by the SVG Renderer.\n */\nfunction pathFromSegments(segments) {\n    var path = [];\n    if (segments.length) {\n        path.push(['M', segments[0].start.x, segments[0].start.y]);\n        for (var i = 0; i < segments.length; ++i) {\n            path.push(['L', segments[i].end.x, segments[i].end.y]);\n        }\n    }\n    return path;\n}\n/**\n * Limits obstacle max/mins in all directions to bounds. Modifies input\n * obstacle.\n *\n * @private\n * @function limitObstacleToBounds\n *\n * @param {Object} obstacle\n *        Obstacle to limit.\n *\n * @param {Object} bounds\n *        Bounds to use as limit.\n *\n * @return {void}\n */\nfunction limitObstacleToBounds(obstacle, bounds) {\n    obstacle.yMin = PathfinderAlgorithms_max(obstacle.yMin, bounds.yMin);\n    obstacle.yMax = PathfinderAlgorithms_min(obstacle.yMax, bounds.yMax);\n    obstacle.xMin = PathfinderAlgorithms_max(obstacle.xMin, bounds.xMin);\n    obstacle.xMax = PathfinderAlgorithms_min(obstacle.xMax, bounds.xMax);\n}\n/**\n * Get an SVG path from a starting coordinate to an ending coordinate.\n * Draws a straight line.\n *\n * @function Highcharts.Pathfinder.algorithms.straight\n *\n * @param {Highcharts.PositionObject} start\n *        Starting coordinate, object with x/y props.\n *\n * @param {Highcharts.PositionObject} end\n *        Ending coordinate, object with x/y props.\n *\n * @return {Object}\n *         An object with the SVG path in Array form as accepted by the SVG\n *         renderer, as well as an array of new obstacles making up this\n *         path.\n */\nfunction straight(start, end) {\n    return {\n        path: [\n            ['M', start.x, start.y],\n            ['L', end.x, end.y]\n        ],\n        obstacles: [{ start: start, end: end }]\n    };\n}\n/**\n * Find a path from a starting coordinate to an ending coordinate, using\n * right angles only, and taking only starting/ending obstacle into\n * consideration.\n *\n * @function Highcharts.Pathfinder.algorithms.simpleConnect\n *\n * @param {Highcharts.PositionObject} start\n *        Starting coordinate, object with x/y props.\n *\n * @param {Highcharts.PositionObject} end\n *        Ending coordinate, object with x/y props.\n *\n * @param {Object} options\n *        Options for the algorithm:\n *        - chartObstacles: Array of chart obstacles to avoid\n *        - startDirectionX: Optional. True if starting in the X direction.\n *          If not provided, the algorithm starts in the direction that is\n *          the furthest between start/end.\n *\n * @return {Object}\n *         An object with the SVG path in Array form as accepted by the SVG\n *         renderer, as well as an array of new obstacles making up this\n *         path.\n */\nvar simpleConnect = function (start, end, options) {\n    var segments = [],\n        chartObstacles = options.chartObstacles,\n        startObstacleIx = findObstacleFromPoint(chartObstacles,\n        start),\n        endObstacleIx = findObstacleFromPoint(chartObstacles,\n        end);\n    var endSegment,\n        dir = pick(options.startDirectionX,\n        abs(end.x - start.x) > abs(end.y - start.y)) ? 'x' : 'y',\n        startObstacle,\n        endObstacle,\n        waypoint,\n        useMax,\n        endPoint;\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Return a clone of a point with a property set from a target object,\n     * optionally with an offset\n     * @private\n     */\n    function copyFromPoint(from, fromKey, to, toKey, offset) {\n        var point = {\n                x: from.x,\n                y: from.y\n            };\n        point[fromKey] = to[toKey || fromKey] + (offset || 0);\n        return point;\n    }\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Return waypoint outside obstacle.\n     * @private\n     */\n    function getMeOut(obstacle, point, direction) {\n        var useMax = abs(point[direction] - obstacle[direction + 'Min']) >\n                abs(point[direction] - obstacle[direction + 'Max']);\n        return copyFromPoint(point, direction, obstacle, direction + (useMax ? 'Max' : 'Min'), useMax ? 1 : -1);\n    }\n    // Pull out end point\n    if (endObstacleIx > -1) {\n        endObstacle = chartObstacles[endObstacleIx];\n        waypoint = getMeOut(endObstacle, end, dir);\n        endSegment = {\n            start: waypoint,\n            end: end\n        };\n        endPoint = waypoint;\n    }\n    else {\n        endPoint = end;\n    }\n    // If an obstacle envelops the start point, add a segment to get out,\n    // and around it.\n    if (startObstacleIx > -1) {\n        startObstacle = chartObstacles[startObstacleIx];\n        waypoint = getMeOut(startObstacle, start, dir);\n        segments.push({\n            start: start,\n            end: waypoint\n        });\n        // If we are going back again, switch direction to get around start\n        // obstacle.\n        if (\n        // Going towards max from start:\n        waypoint[dir] >= start[dir] ===\n            // Going towards min to end:\n            waypoint[dir] >= endPoint[dir]) {\n            dir = dir === 'y' ? 'x' : 'y';\n            useMax = start[dir] < end[dir];\n            segments.push({\n                start: waypoint,\n                end: copyFromPoint(waypoint, dir, startObstacle, dir + (useMax ? 'Max' : 'Min'), useMax ? 1 : -1)\n            });\n            // Switch direction again\n            dir = dir === 'y' ? 'x' : 'y';\n        }\n    }\n    // We are around the start obstacle. Go towards the end in one\n    // direction.\n    var prevWaypoint = segments.length ?\n            segments[segments.length - 1].end :\n            start;\n    waypoint = copyFromPoint(prevWaypoint, dir, endPoint);\n    segments.push({\n        start: prevWaypoint,\n        end: waypoint\n    });\n    // Final run to end point in the other direction\n    dir = dir === 'y' ? 'x' : 'y';\n    var waypoint2 = copyFromPoint(waypoint,\n        dir,\n        endPoint);\n    segments.push({\n        start: waypoint,\n        end: waypoint2\n    });\n    // Finally add the endSegment\n    segments.push(endSegment);\n    var path = Series_PathUtilities.applyRadius(pathFromSegments(segments),\n        options.radius);\n    return {\n        path: path,\n        obstacles: segments\n    };\n};\nsimpleConnect.requiresObstacles = true;\n/**\n * Find a path from a starting coordinate to an ending coordinate, taking\n * obstacles into consideration. Might not always find the optimal path,\n * but is fast, and usually good enough.\n *\n * @function Highcharts.Pathfinder.algorithms.fastAvoid\n *\n * @param {Highcharts.PositionObject} start\n *        Starting coordinate, object with x/y props.\n *\n * @param {Highcharts.PositionObject} end\n *        Ending coordinate, object with x/y props.\n *\n * @param {Object} options\n *        Options for the algorithm.\n *        - chartObstacles:  Array of chart obstacles to avoid\n *        - lineObstacles:   Array of line obstacles to jump over\n *        - obstacleMetrics: Object with metrics of chartObstacles cached\n *        - hardBounds:      Hard boundaries to not cross\n *        - obstacleOptions: Options for the obstacles, including margin\n *        - startDirectionX: Optional. True if starting in the X direction.\n *                           If not provided, the algorithm starts in the\n *                           direction that is the furthest between\n *                           start/end.\n *\n * @return {Object}\n *         An object with the SVG path in Array form as accepted by the SVG\n *         renderer, as well as an array of new obstacles making up this\n *         path.\n */\nfunction fastAvoid(start, end, options) {\n    /*\n        Algorithm rules/description\n        - Find initial direction\n        - Determine soft/hard max for each direction.\n        - Move along initial direction until obstacle.\n        - Change direction.\n        - If hitting obstacle, first try to change length of previous line\n            before changing direction again.\n\n        Soft min/max x = start/destination x +/- widest obstacle + margin\n        Soft min/max y = start/destination y +/- tallest obstacle + margin\n\n        @todo:\n            - Make retrospective, try changing prev segment to reduce\n                corners\n            - Fix logic for breaking out of end-points - not always picking\n                the best direction currently\n            - When going around the end obstacle we should not always go the\n                shortest route, rather pick the one closer to the end point\n    */\n    var dirIsX = pick(options.startDirectionX,\n        abs(end.x - start.x) > abs(end.y - start.y)),\n        dir = dirIsX ? 'x' : 'y',\n        endSegments = [], \n        // Boundaries to stay within. If beyond soft boundary, prefer to\n        // change direction ASAP. If at hard max, always change immediately.\n        metrics = options.obstacleMetrics,\n        softMinX = PathfinderAlgorithms_min(start.x,\n        end.x) - metrics.maxWidth - 10,\n        softMaxX = PathfinderAlgorithms_max(start.x,\n        end.x) + metrics.maxWidth + 10,\n        softMinY = PathfinderAlgorithms_min(start.y,\n        end.y) - metrics.maxHeight - 10,\n        softMaxY = PathfinderAlgorithms_max(start.y,\n        end.y) + metrics.maxHeight + 10;\n    var segments,\n        useMax,\n        extractedEndPoint,\n        forceObstacleBreak = false, // Used in clearPathTo to keep track of\n        // when to force break through an obstacle.\n        // Obstacles\n        chartObstacles = options.chartObstacles,\n        endObstacleIx = findLastObstacleBefore(chartObstacles,\n        softMaxX);\n    var startObstacleIx = findLastObstacleBefore(chartObstacles,\n        softMinX);\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * How far can you go between two points before hitting an obstacle?\n     * Does not work for diagonal lines (because it doesn't have to).\n     * @private\n     */\n    function pivotPoint(fromPoint, toPoint, directionIsX) {\n        var searchDirection = fromPoint.x < toPoint.x ? 1 : -1;\n        var firstPoint,\n            lastPoint,\n            highestPoint,\n            lowestPoint;\n        if (fromPoint.x < toPoint.x) {\n            firstPoint = fromPoint;\n            lastPoint = toPoint;\n        }\n        else {\n            firstPoint = toPoint;\n            lastPoint = fromPoint;\n        }\n        if (fromPoint.y < toPoint.y) {\n            lowestPoint = fromPoint;\n            highestPoint = toPoint;\n        }\n        else {\n            lowestPoint = toPoint;\n            highestPoint = fromPoint;\n        }\n        // Go through obstacle range in reverse if toPoint is before\n        // fromPoint in the X-dimension.\n        var i = searchDirection < 0 ?\n                // Searching backwards, start at last obstacle before last point\n                PathfinderAlgorithms_min(findLastObstacleBefore(chartObstacles,\n            lastPoint.x),\n            chartObstacles.length - 1) :\n                // Forwards. Since we're not sorted by xMax, we have to look\n                // at all obstacles.\n                0;\n        // Go through obstacles in this X range\n        while (chartObstacles[i] && (searchDirection > 0 && chartObstacles[i].xMin <= lastPoint.x ||\n            searchDirection < 0 && chartObstacles[i].xMax >= firstPoint.x)) {\n            // If this obstacle is between from and to points in a straight\n            // line, pivot at the intersection.\n            if (chartObstacles[i].xMin <= lastPoint.x &&\n                chartObstacles[i].xMax >= firstPoint.x &&\n                chartObstacles[i].yMin <= highestPoint.y &&\n                chartObstacles[i].yMax >= lowestPoint.y) {\n                if (directionIsX) {\n                    return {\n                        y: fromPoint.y,\n                        x: fromPoint.x < toPoint.x ?\n                            chartObstacles[i].xMin - 1 :\n                            chartObstacles[i].xMax + 1,\n                        obstacle: chartObstacles[i]\n                    };\n                }\n                // Else ...\n                return {\n                    x: fromPoint.x,\n                    y: fromPoint.y < toPoint.y ?\n                        chartObstacles[i].yMin - 1 :\n                        chartObstacles[i].yMax + 1,\n                    obstacle: chartObstacles[i]\n                };\n            }\n            i += searchDirection;\n        }\n        return toPoint;\n    }\n    /**\n     * Decide in which direction to dodge or get out of an obstacle.\n     * Considers desired direction, which way is shortest, soft and hard\n     * bounds.\n     *\n     * (? Returns a string, either xMin, xMax, yMin or yMax.)\n     *\n     * @private\n     * @function\n     *\n     * @param {Object} obstacle\n     *        Obstacle to dodge/escape.\n     *\n     * @param {Object} fromPoint\n     *        Point with x/y props that's dodging/escaping.\n     *\n     * @param {Object} toPoint\n     *        Goal point.\n     *\n     * @param {boolean} dirIsX\n     *        Dodge in X dimension.\n     *\n     * @param {Object} bounds\n     *        Hard and soft boundaries.\n     *\n     * @return {boolean}\n     *         Use max or not.\n     */\n    function getDodgeDirection(obstacle, fromPoint, toPoint, dirIsX, bounds) {\n        var softBounds = bounds.soft, hardBounds = bounds.hard, dir = dirIsX ? 'x' : 'y', toPointMax = { x: fromPoint.x, y: fromPoint.y }, toPointMin = { x: fromPoint.x, y: fromPoint.y }, maxOutOfSoftBounds = obstacle[dir + 'Max'] >=\n                softBounds[dir + 'Max'], minOutOfSoftBounds = obstacle[dir + 'Min'] <=\n                softBounds[dir + 'Min'], maxOutOfHardBounds = obstacle[dir + 'Max'] >=\n                hardBounds[dir + 'Max'], minOutOfHardBounds = obstacle[dir + 'Min'] <=\n                hardBounds[dir + 'Min'], \n            // Find out if we should prefer one direction over the other if\n            // we can choose freely\n            minDistance = abs(obstacle[dir + 'Min'] - fromPoint[dir]), maxDistance = abs(obstacle[dir + 'Max'] - fromPoint[dir]);\n        var // If it's a small difference, pick the one leading towards dest\n            // point. Otherwise pick the shortest distance\n            useMax = abs(minDistance - maxDistance) < 10 ?\n                fromPoint[dir] < toPoint[dir] :\n                maxDistance < minDistance;\n        // Check if we hit any obstacles trying to go around in either\n        // direction.\n        toPointMin[dir] = obstacle[dir + 'Min'];\n        toPointMax[dir] = obstacle[dir + 'Max'];\n        var minPivot = pivotPoint(fromPoint,\n            toPointMin,\n            dirIsX)[dir] !==\n                toPointMin[dir],\n            maxPivot = pivotPoint(fromPoint,\n            toPointMax,\n            dirIsX)[dir] !==\n                toPointMax[dir];\n        useMax = minPivot ?\n            (maxPivot ? useMax : true) :\n            (maxPivot ? false : useMax);\n        // `useMax` now contains our preferred choice, bounds not taken into\n        // account. If both or neither direction is out of bounds we want to\n        // use this.\n        // Deal with soft bounds\n        useMax = minOutOfSoftBounds ?\n            (maxOutOfSoftBounds ? useMax : true) : // Out on min\n            (maxOutOfSoftBounds ? false : useMax); // Not out on min\n        // Deal with hard bounds\n        useMax = minOutOfHardBounds ?\n            (maxOutOfHardBounds ? useMax : true) : // Out on min\n            (maxOutOfHardBounds ? false : useMax); // Not out on min\n        return useMax;\n    }\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Find a clear path between point.\n     * @private\n     */\n    function clearPathTo(fromPoint, toPoint, dirIsX) {\n        // Don't waste time if we've hit goal\n        if (fromPoint.x === toPoint.x && fromPoint.y === toPoint.y) {\n            return [];\n        }\n        var dir = dirIsX ? 'x' : 'y',\n            obstacleMargin = options.obstacleOptions.margin,\n            bounds = {\n                soft: {\n                    xMin: softMinX,\n                    xMax: softMaxX,\n                    yMin: softMinY,\n                    yMax: softMaxY\n                },\n                hard: options.hardBounds\n            };\n        var pivot,\n            segments,\n            waypoint,\n            waypointUseMax,\n            envelopingObstacle,\n            secondEnvelopingObstacle,\n            envelopWaypoint;\n        // If fromPoint is inside an obstacle we have a problem. Break out\n        // by just going to the outside of this obstacle. We prefer to go to\n        // the nearest edge in the chosen direction.\n        envelopingObstacle =\n            findObstacleFromPoint(chartObstacles, fromPoint);\n        if (envelopingObstacle > -1) {\n            envelopingObstacle = chartObstacles[envelopingObstacle];\n            waypointUseMax = getDodgeDirection(envelopingObstacle, fromPoint, toPoint, dirIsX, bounds);\n            // Cut obstacle to hard bounds to make sure we stay within\n            limitObstacleToBounds(envelopingObstacle, options.hardBounds);\n            envelopWaypoint = dirIsX ? {\n                y: fromPoint.y,\n                x: envelopingObstacle[waypointUseMax ? 'xMax' : 'xMin'] +\n                    (waypointUseMax ? 1 : -1)\n            } : {\n                x: fromPoint.x,\n                y: envelopingObstacle[waypointUseMax ? 'yMax' : 'yMin'] +\n                    (waypointUseMax ? 1 : -1)\n            };\n            // If we crashed into another obstacle doing this, we put the\n            // waypoint between them instead\n            secondEnvelopingObstacle = findObstacleFromPoint(chartObstacles, envelopWaypoint);\n            if (secondEnvelopingObstacle > -1) {\n                secondEnvelopingObstacle = chartObstacles[secondEnvelopingObstacle];\n                // Cut obstacle to hard bounds\n                limitObstacleToBounds(secondEnvelopingObstacle, options.hardBounds);\n                // Modify waypoint to lay between obstacles\n                envelopWaypoint[dir] = waypointUseMax ? PathfinderAlgorithms_max(envelopingObstacle[dir + 'Max'] - obstacleMargin + 1, (secondEnvelopingObstacle[dir + 'Min'] +\n                    envelopingObstacle[dir + 'Max']) / 2) :\n                    PathfinderAlgorithms_min((envelopingObstacle[dir + 'Min'] + obstacleMargin - 1), ((secondEnvelopingObstacle[dir + 'Max'] +\n                        envelopingObstacle[dir + 'Min']) / 2));\n                // We are not going anywhere. If this happens for the first\n                // time, do nothing. Otherwise, try to go to the extreme of\n                // the obstacle pair in the current direction.\n                if (fromPoint.x === envelopWaypoint.x &&\n                    fromPoint.y === envelopWaypoint.y) {\n                    if (forceObstacleBreak) {\n                        envelopWaypoint[dir] = waypointUseMax ?\n                            PathfinderAlgorithms_max(envelopingObstacle[dir + 'Max'], secondEnvelopingObstacle[dir + 'Max']) + 1 :\n                            PathfinderAlgorithms_min(envelopingObstacle[dir + 'Min'], secondEnvelopingObstacle[dir + 'Min']) - 1;\n                    }\n                    // Toggle on if off, and the opposite\n                    forceObstacleBreak = !forceObstacleBreak;\n                }\n                else {\n                    // This point is not identical to previous.\n                    // Clear break trigger.\n                    forceObstacleBreak = false;\n                }\n            }\n            segments = [{\n                    start: fromPoint,\n                    end: envelopWaypoint\n                }];\n        }\n        else { // If not enveloping, use standard pivot calculation\n            pivot = pivotPoint(fromPoint, {\n                x: dirIsX ? toPoint.x : fromPoint.x,\n                y: dirIsX ? fromPoint.y : toPoint.y\n            }, dirIsX);\n            segments = [{\n                    start: fromPoint,\n                    end: {\n                        x: pivot.x,\n                        y: pivot.y\n                    }\n                }];\n            // Pivot before goal, use a waypoint to dodge obstacle\n            if (pivot[dirIsX ? 'x' : 'y'] !== toPoint[dirIsX ? 'x' : 'y']) {\n                // Find direction of waypoint\n                waypointUseMax = getDodgeDirection(pivot.obstacle, pivot, toPoint, !dirIsX, bounds);\n                // Cut waypoint to hard bounds\n                limitObstacleToBounds(pivot.obstacle, options.hardBounds);\n                waypoint = {\n                    x: dirIsX ?\n                        pivot.x :\n                        pivot.obstacle[waypointUseMax ? 'xMax' : 'xMin'] +\n                            (waypointUseMax ? 1 : -1),\n                    y: dirIsX ?\n                        pivot.obstacle[waypointUseMax ? 'yMax' : 'yMin'] +\n                            (waypointUseMax ? 1 : -1) :\n                        pivot.y\n                };\n                // We're changing direction here, store that to make sure we\n                // also change direction when adding the last segment array\n                // after handling waypoint.\n                dirIsX = !dirIsX;\n                segments = segments.concat(clearPathTo({\n                    x: pivot.x,\n                    y: pivot.y\n                }, waypoint, dirIsX));\n            }\n        }\n        // Get segments for the other direction too\n        // Recursion is our friend\n        segments = segments.concat(clearPathTo(segments[segments.length - 1].end, toPoint, !dirIsX));\n        return segments;\n    }\n    // eslint-disable-next-line valid-jsdoc\n    /**\n     * Extract point to outside of obstacle in whichever direction is\n     * closest. Returns new point outside obstacle.\n     * @private\n     */\n    function extractFromObstacle(obstacle, point, goalPoint) {\n        var dirIsX = PathfinderAlgorithms_min(obstacle.xMax - point.x,\n            point.x - obstacle.xMin) <\n                PathfinderAlgorithms_min(obstacle.yMax - point.y,\n            point.y - obstacle.yMin),\n            bounds = {\n                soft: options.hardBounds,\n                hard: options.hardBounds\n            },\n            useMax = getDodgeDirection(obstacle,\n            point,\n            goalPoint,\n            dirIsX,\n            bounds);\n        return dirIsX ? {\n            y: point.y,\n            x: obstacle[useMax ? 'xMax' : 'xMin'] + (useMax ? 1 : -1)\n        } : {\n            x: point.x,\n            y: obstacle[useMax ? 'yMax' : 'yMin'] + (useMax ? 1 : -1)\n        };\n    }\n    // Cut the obstacle array to soft bounds for optimization in large\n    // datasets.\n    chartObstacles =\n        chartObstacles.slice(startObstacleIx, endObstacleIx + 1);\n    // If an obstacle envelops the end point, move it out of there and add\n    // a little segment to where it was.\n    if ((endObstacleIx = findObstacleFromPoint(chartObstacles, end)) > -1) {\n        extractedEndPoint = extractFromObstacle(chartObstacles[endObstacleIx], end, start);\n        endSegments.push({\n            end: end,\n            start: extractedEndPoint\n        });\n        end = extractedEndPoint;\n    }\n    // If it's still inside one or more obstacles, get out of there by\n    // force-moving towards the start point.\n    while ((endObstacleIx = findObstacleFromPoint(chartObstacles, end)) > -1) {\n        useMax = end[dir] - start[dir] < 0;\n        extractedEndPoint = {\n            x: end.x,\n            y: end.y\n        };\n        extractedEndPoint[dir] = chartObstacles[endObstacleIx][useMax ? dir + 'Max' : dir + 'Min'] + (useMax ? 1 : -1);\n        endSegments.push({\n            end: end,\n            start: extractedEndPoint\n        });\n        end = extractedEndPoint;\n    }\n    // Find the path\n    segments = clearPathTo(start, end, dirIsX);\n    // Add the end-point segments\n    segments = segments.concat(endSegments.reverse());\n    return {\n        path: pathFromSegments(segments),\n        obstacles: segments\n    };\n}\nfastAvoid.requiresObstacles = true;\n/* *\n *\n *  Default Export\n *\n * */\n// Define the available pathfinding algorithms.\n// Algorithms take up to 3 arguments: starting point, ending point, and an\n// options object.\nvar algorithms = {\n    fastAvoid: fastAvoid,\n    straight: straight,\n    simpleConnect: simpleConnect\n};\n/* harmony default export */ var PathfinderAlgorithms = (algorithms);\n\n;// ./code/es5/es-modules/Gantt/ConnectorsDefaults.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Defaults\n *\n * */\n/*\n @todo:\n     - Document how to write your own algorithms\n     - Consider adding a Point.pathTo method that wraps creating a connection\n       and rendering it\n*/\nvar connectorsDefaults = {\n    /**\n     * The Pathfinder module allows you to define connections between any two\n     * points, represented as lines - optionally with markers for the start\n     * and/or end points. Multiple algorithms are available for calculating how\n     * the connecting lines are drawn.\n     *\n     * Connector functionality requires Highcharts Gantt to be loaded. In Gantt\n     * charts, the connectors are used to draw dependencies between tasks.\n     *\n     * @see [dependency](series.gantt.data.dependency)\n     *\n     * @sample gantt/pathfinder/demo\n     *         Pathfinder connections\n     *\n     * @declare      Highcharts.ConnectorsOptions\n     * @product      gantt\n     * @optionparent connectors\n     */\n    connectors: {\n        /**\n         * Enable connectors for this chart. Requires Highcharts Gantt.\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     6.2.0\n         * @apioption connectors.enabled\n         */\n        /**\n         * Set the default dash style for this chart's connecting lines.\n         *\n         * @type      {string}\n         * @default   solid\n         * @since     6.2.0\n         * @apioption connectors.dashStyle\n         */\n        /**\n         * Set the default color for this chart's Pathfinder connecting lines.\n         * Defaults to the color of the point being connected.\n         *\n         * @type      {Highcharts.ColorString}\n         * @since     6.2.0\n         * @apioption connectors.lineColor\n         */\n        /**\n         * Set the default pathfinder margin to use, in pixels. Some Pathfinder\n         * algorithms attempt to avoid obstacles, such as other points in the\n         * chart. These algorithms use this margin to determine how close lines\n         * can be to an obstacle. The default is to compute this automatically\n         * from the size of the obstacles in the chart.\n         *\n         * To draw connecting lines close to existing points, set this to a low\n         * number. For more space around existing points, set this number\n         * higher.\n         *\n         * @sample gantt/pathfinder/algorithm-margin\n         *         Small algorithmMargin\n         *\n         * @type      {number}\n         * @since     6.2.0\n         * @apioption connectors.algorithmMargin\n         */\n        /**\n         * Set the default pathfinder algorithm to use for this chart. It is\n         * possible to define your own algorithms by adding them to the\n         * Highcharts.Pathfinder.prototype.algorithms object before the chart\n         * has been created.\n         *\n         * The default algorithms are as follows:\n         *\n         * `straight`:      Draws a straight line between the connecting\n         *                  points. Does not avoid other points when drawing.\n         *\n         * `simpleConnect`: Finds a path between the points using right angles\n         *                  only. Takes only starting/ending points into\n         *                  account, and will not avoid other points.\n         *\n         * `fastAvoid`:     Finds a path between the points using right angles\n         *                  only. Will attempt to avoid other points, but its\n         *                  focus is performance over accuracy. Works well with\n         *                  less dense datasets.\n         *\n         * Default value: `straight` is used as default for most series types,\n         * while `simpleConnect` is used as default for Gantt series, to show\n         * dependencies between points.\n         *\n         * @sample gantt/pathfinder/demo\n         *         Different types used\n         *\n         * @type    {Highcharts.PathfinderTypeValue}\n         * @default undefined\n         * @since   6.2.0\n         */\n        type: 'straight',\n        /**\n         * The corner radius for the connector line.\n         *\n         * @since 11.2.0\n         */\n        radius: 0,\n        /**\n         * Set the default pixel width for this chart's Pathfinder connecting\n         * lines.\n         *\n         * @since 6.2.0\n         */\n        lineWidth: 1,\n        /**\n         * Marker options for this chart's Pathfinder connectors. Note that\n         * this option is overridden by the `startMarker` and `endMarker`\n         * options.\n         *\n         * @declare Highcharts.ConnectorsMarkerOptions\n         * @since   6.2.0\n         */\n        marker: {\n            /**\n             * Set the radius of the connector markers. The default is\n             * automatically computed based on the algorithmMargin setting.\n             *\n             * Setting marker.width and marker.height will override this\n             * setting.\n             *\n             * @type      {number}\n             * @since     6.2.0\n             * @apioption connectors.marker.radius\n             */\n            /**\n             * Set the width of the connector markers. If not supplied, this\n             * is inferred from the marker radius.\n             *\n             * @type      {number}\n             * @since     6.2.0\n             * @apioption connectors.marker.width\n             */\n            /**\n             * Set the height of the connector markers. If not supplied, this\n             * is inferred from the marker radius.\n             *\n             * @type      {number}\n             * @since     6.2.0\n             * @apioption connectors.marker.height\n             */\n            /**\n             * Set the color of the connector markers. By default this is the\n             * same as the connector color.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             * @since     6.2.0\n             * @apioption connectors.marker.color\n             */\n            /**\n             * Set the line/border color of the connector markers. By default\n             * this is the same as the marker color.\n             *\n             * @type      {Highcharts.ColorString}\n             * @since     6.2.0\n             * @apioption connectors.marker.lineColor\n             */\n            /**\n             * Enable markers for the connectors.\n             */\n            enabled: false,\n            /**\n             * Horizontal alignment of the markers relative to the points.\n             *\n             * @type {Highcharts.AlignValue}\n             */\n            align: 'center',\n            /**\n             * Vertical alignment of the markers relative to the points.\n             *\n             * @type {Highcharts.VerticalAlignValue}\n             */\n            verticalAlign: 'middle',\n            /**\n             * Whether or not to draw the markers inside the points.\n             */\n            inside: false,\n            /**\n             * Set the line/border width of the pathfinder markers.\n             */\n            lineWidth: 1\n        },\n        /**\n         * Marker options specific to the start markers for this chart's\n         * Pathfinder connectors. Overrides the generic marker options.\n         *\n         * @declare Highcharts.ConnectorsStartMarkerOptions\n         * @extends connectors.marker\n         * @since   6.2.0\n         */\n        startMarker: {\n            /**\n             * Set the symbol of the connector start markers.\n             */\n            symbol: 'diamond'\n        },\n        /**\n         * Marker options specific to the end markers for this chart's\n         * Pathfinder connectors. Overrides the generic marker options.\n         *\n         * @declare Highcharts.ConnectorsEndMarkerOptions\n         * @extends connectors.marker\n         * @since   6.2.0\n         */\n        endMarker: {\n            /**\n             * Set the symbol of the connector end markers.\n             */\n            symbol: 'arrow-filled'\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var ConnectorsDefaults = (connectorsDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Override Pathfinder connector options for a series. Requires Highcharts Gantt\n * to be loaded.\n *\n * @declare   Highcharts.SeriesConnectorsOptionsObject\n * @extends   connectors\n * @since     6.2.0\n * @excluding enabled, algorithmMargin\n * @product   gantt\n * @apioption plotOptions.series.connectors\n */\n/**\n * Connect to a point. This option can be either a string, referring to the ID\n * of another point, or an object, or an array of either. If the option is an\n * array, each element defines a connection.\n *\n * @sample gantt/pathfinder/demo\n *         Different connection types\n *\n * @declare   Highcharts.XrangePointConnectorsOptionsObject\n * @type      {string|Array<string|*>|*}\n * @extends   plotOptions.series.connectors\n * @since     6.2.0\n * @excluding enabled\n * @product   gantt\n * @requires  highcharts-gantt\n * @apioption series.xrange.data.connect\n */\n/**\n * The ID of the point to connect to.\n *\n * @type      {string}\n * @since     6.2.0\n * @product   gantt\n * @apioption series.xrange.data.connect.to\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Gantt/PathfinderComposition.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n\nvar PathfinderComposition_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, PathfinderComposition_error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, PathfinderComposition_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get point bounding box using plotX/plotY and shapeArgs. If using\n * graphic.getBBox() directly, the bbox will be affected by animation.\n *\n * @private\n * @function\n *\n * @param {Highcharts.Point} point\n *        The point to get BB of.\n *\n * @return {Highcharts.Dictionary<number>|null}\n *         Result xMax, xMin, yMax, yMin.\n */\nfunction getPointBB(point) {\n    var shapeArgs = point.shapeArgs;\n    // Prefer using shapeArgs (columns)\n    if (shapeArgs) {\n        return {\n            xMin: shapeArgs.x || 0,\n            xMax: (shapeArgs.x || 0) + (shapeArgs.width || 0),\n            yMin: shapeArgs.y || 0,\n            yMax: (shapeArgs.y || 0) + (shapeArgs.height || 0)\n        };\n    }\n    // Otherwise use plotX/plotY and bb\n    var bb = point.graphic && point.graphic.getBBox();\n    return bb ? {\n        xMin: point.plotX - bb.width / 2,\n        xMax: point.plotX + bb.width / 2,\n        yMin: point.plotY - bb.height / 2,\n        yMax: point.plotY + bb.height / 2\n    } : null;\n}\n/**\n * Warn if using legacy options. Copy the options over. Note that this will\n * still break if using the legacy options in chart.update, addSeries etc.\n * @private\n */\nfunction warnLegacy(chart) {\n    if (chart.options.pathfinder ||\n        chart.series.reduce(function (acc, series) {\n            if (series.options) {\n                PathfinderComposition_merge(true, (series.options.connectors = series.options.connectors ||\n                    {}), series.options.pathfinder);\n            }\n            return acc || series.options && series.options.pathfinder;\n        }, false)) {\n        PathfinderComposition_merge(true, (chart.options.connectors = chart.options.connectors || {}), chart.options.pathfinder);\n        PathfinderComposition_error('WARNING: Pathfinder options have been renamed. ' +\n            'Use \"chart.connectors\" or \"series.connectors\" instead.');\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\nvar ConnectionComposition;\n(function (ConnectionComposition) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(ChartClass, PathfinderClass, PointClass) {\n        var pointProto = PointClass.prototype;\n        if (!pointProto.getPathfinderAnchorPoint) {\n            // Initialize Pathfinder for charts\n            ChartClass.prototype.callbacks.push(function (chart) {\n                var options = chart.options;\n                if (options.connectors.enabled !== false) {\n                    warnLegacy(chart);\n                    this.pathfinder = new PathfinderClass(this);\n                    this.pathfinder.update(true); // First draw, defer render\n                }\n            });\n            pointProto.getMarkerVector = pointGetMarkerVector;\n            pointProto.getPathfinderAnchorPoint = pointGetPathfinderAnchorPoint;\n            pointProto.getRadiansToVector = pointGetRadiansToVector;\n            // Set default Pathfinder options\n            setOptions(ConnectorsDefaults);\n        }\n    }\n    ConnectionComposition.compose = compose;\n    /**\n     * Get coordinates of anchor point for pathfinder connection.\n     *\n     * @private\n     * @function Highcharts.Point#getPathfinderAnchorPoint\n     *\n     * @param {Highcharts.ConnectorsMarkerOptions} markerOptions\n     *        Connection options for position on point.\n     *\n     * @return {Highcharts.PositionObject}\n     *         An object with x/y properties for the position. Coordinates are\n     *         in plot values, not relative to point.\n     */\n    function pointGetPathfinderAnchorPoint(markerOptions) {\n        var bb = getPointBB(this);\n        var x,\n            y;\n        switch (markerOptions.align) { // eslint-disable-line default-case\n            case 'right':\n                x = 'xMax';\n                break;\n            case 'left':\n                x = 'xMin';\n        }\n        switch (markerOptions.verticalAlign) { // eslint-disable-line default-case\n            case 'top':\n                y = 'yMin';\n                break;\n            case 'bottom':\n                y = 'yMax';\n        }\n        return {\n            x: x ? bb[x] : (bb.xMin + bb.xMax) / 2,\n            y: y ? bb[y] : (bb.yMin + bb.yMax) / 2\n        };\n    }\n    /**\n     * Utility to get the angle from one point to another.\n     *\n     * @private\n     * @function Highcharts.Point#getRadiansToVector\n     *\n     * @param {Highcharts.PositionObject} v1\n     *        The first vector, as an object with x/y properties.\n     *\n     * @param {Highcharts.PositionObject} v2\n     *        The second vector, as an object with x/y properties.\n     *\n     * @return {number}\n     *         The angle in degrees\n     */\n    function pointGetRadiansToVector(v1, v2) {\n        var box;\n        if (!PathfinderComposition_defined(v2)) {\n            box = getPointBB(this);\n            if (box) {\n                v2 = {\n                    x: (box.xMin + box.xMax) / 2,\n                    y: (box.yMin + box.yMax) / 2\n                };\n            }\n        }\n        return Math.atan2(v2.y - v1.y, v1.x - v2.x);\n    }\n    /**\n     * Utility to get the position of the marker, based on the path angle and\n     * the marker's radius.\n     *\n     * @private\n     * @function Highcharts.Point#getMarkerVector\n     *\n     * @param {number} radians\n     *        The angle in radians from the point center to another vector.\n     *\n     * @param {number} markerRadius\n     *        The radius of the marker, to calculate the additional distance to\n     *        the center of the marker.\n     *\n     * @param {Object} anchor\n     *        The anchor point of the path and marker as an object with x/y\n     *        properties.\n     *\n     * @return {Object}\n     *         The marker vector as an object with x/y properties.\n     */\n    function pointGetMarkerVector(radians, markerRadius, anchor) {\n        var twoPI = Math.PI * 2.0, bb = getPointBB(this), rectWidth = bb.xMax - bb.xMin, rectHeight = bb.yMax - bb.yMin, rAtan = Math.atan2(rectHeight, rectWidth), rectHalfWidth = rectWidth / 2.0, rectHalfHeight = rectHeight / 2.0, rectHorizontalCenter = bb.xMin + rectHalfWidth, rectVerticalCenter = bb.yMin + rectHalfHeight, edgePoint = {\n                x: rectHorizontalCenter,\n                y: rectVerticalCenter\n            };\n        var theta = radians,\n            tanTheta = 1,\n            leftOrRightRegion = false,\n            xFactor = 1,\n            yFactor = 1;\n        while (theta < -Math.PI) {\n            theta += twoPI;\n        }\n        while (theta > Math.PI) {\n            theta -= twoPI;\n        }\n        tanTheta = Math.tan(theta);\n        if ((theta > -rAtan) && (theta <= rAtan)) {\n            // Right side\n            yFactor = -1;\n            leftOrRightRegion = true;\n        }\n        else if (theta > rAtan && theta <= (Math.PI - rAtan)) {\n            // Top side\n            yFactor = -1;\n        }\n        else if (theta > (Math.PI - rAtan) || theta <= -(Math.PI - rAtan)) {\n            // Left side\n            xFactor = -1;\n            leftOrRightRegion = true;\n        }\n        else {\n            // Bottom side\n            xFactor = -1;\n        }\n        // Correct the edgePoint according to the placement of the marker\n        if (leftOrRightRegion) {\n            edgePoint.x += xFactor * (rectHalfWidth);\n            edgePoint.y += yFactor * (rectHalfWidth) * tanTheta;\n        }\n        else {\n            edgePoint.x += xFactor * (rectHeight / (2.0 * tanTheta));\n            edgePoint.y += yFactor * (rectHalfHeight);\n        }\n        if (anchor.x !== rectHorizontalCenter) {\n            edgePoint.x = anchor.x;\n        }\n        if (anchor.y !== rectVerticalCenter) {\n            edgePoint.y = anchor.y;\n        }\n        return {\n            x: edgePoint.x + (markerRadius * Math.cos(theta)),\n            y: edgePoint.y - (markerRadius * Math.sin(theta))\n        };\n    }\n})(ConnectionComposition || (ConnectionComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var PathfinderComposition = (ConnectionComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n;// ./code/es5/es-modules/Gantt/Pathfinder.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Pathfinder_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, Pathfinder_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat;\n/* *\n *\n *  Constants\n *\n * */\nvar Pathfinder_max = Math.max, Pathfinder_min = Math.min;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get point bounding box using plotX/plotY and shapeArgs. If using\n * graphic.getBBox() directly, the bbox will be affected by animation.\n *\n * @private\n * @function\n *\n * @param {Highcharts.Point} point\n *        The point to get BB of.\n *\n * @return {Highcharts.Dictionary<number>|null}\n *         Result xMax, xMin, yMax, yMin.\n */\nfunction Pathfinder_getPointBB(point) {\n    var shapeArgs = point.shapeArgs;\n    // Prefer using shapeArgs (columns)\n    if (shapeArgs) {\n        return {\n            xMin: shapeArgs.x || 0,\n            xMax: (shapeArgs.x || 0) + (shapeArgs.width || 0),\n            yMin: shapeArgs.y || 0,\n            yMax: (shapeArgs.y || 0) + (shapeArgs.height || 0)\n        };\n    }\n    // Otherwise use plotX/plotY and bb\n    var bb = point.graphic && point.graphic.getBBox();\n    return bb ? {\n        xMin: point.plotX - bb.width / 2,\n        xMax: point.plotX + bb.width / 2,\n        yMin: point.plotY - bb.height / 2,\n        yMax: point.plotY + bb.height / 2\n    } : null;\n}\n/**\n * Compute smallest distance between two rectangles.\n * @private\n */\nfunction calculateObstacleDistance(a, b, bbMargin) {\n    // Count the distance even if we are slightly off\n    var margin = Pathfinder_pick(bbMargin, 10),\n        yOverlap = a.yMax + margin > b.yMin - margin &&\n            a.yMin - margin < b.yMax + margin,\n        xOverlap = a.xMax + margin > b.xMin - margin &&\n            a.xMin - margin < b.xMax + margin,\n        xDistance = yOverlap ? (a.xMin > b.xMax ? a.xMin - b.xMax : b.xMin - a.xMax) : Infinity,\n        yDistance = xOverlap ? (a.yMin > b.yMax ? a.yMin - b.yMax : b.yMin - a.yMax) : Infinity;\n    // If the rectangles collide, try recomputing with smaller margin.\n    // If they collide anyway, discard the obstacle.\n    if (xOverlap && yOverlap) {\n        return (margin ?\n            calculateObstacleDistance(a, b, Math.floor(margin / 2)) :\n            Infinity);\n    }\n    return Pathfinder_min(xDistance, yDistance);\n}\n/**\n * Calculate margin to place around obstacles for the pathfinder in pixels.\n * Returns a minimum of 1 pixel margin.\n *\n * @private\n * @function\n *\n * @param {Array<object>} obstacles\n *        Obstacles to calculate margin from.\n *\n * @return {number}\n *         The calculated margin in pixels. At least 1.\n */\nfunction calculateObstacleMargin(obstacles) {\n    var len = obstacles.length,\n        distances = [];\n    var onstacleDistance;\n    // Go over all obstacles and compare them to the others.\n    for (var i = 0; i < len; ++i) {\n        // Compare to all obstacles ahead. We will already have compared this\n        // obstacle to the ones before.\n        for (var j = i + 1; j < len; ++j) {\n            onstacleDistance =\n                calculateObstacleDistance(obstacles[i], obstacles[j]);\n            // TODO: Magic number 80\n            if (onstacleDistance < 80) { // Ignore large distances\n                distances.push(onstacleDistance);\n            }\n        }\n    }\n    // Ensure we always have at least one value, even in very spacious charts\n    distances.push(80);\n    return Pathfinder_max(Math.floor(distances.sort(function (a, b) {\n        return (a - b);\n    })[\n    // Discard first 10% of the relevant distances, and then grab\n    // the smallest one.\n    Math.floor(distances.length / 10)] / 2 - 1 // Divide the distance by 2 and subtract 1.\n    ), 1 // 1 is the minimum margin\n    );\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Pathfinder class.\n *\n * @private\n * @class\n * @name Highcharts.Pathfinder\n *\n * @param {Highcharts.Chart} chart\n *        The chart to operate on.\n */\nvar Pathfinder = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function Pathfinder(chart) {\n        this.init(chart);\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    Pathfinder.compose = function (ChartClass, PointClass) {\n        PathfinderComposition.compose(ChartClass, Pathfinder, PointClass);\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize the Pathfinder object.\n     *\n     * @function Highcharts.Pathfinder#init\n     *\n     * @param {Highcharts.Chart} chart\n     *        The chart context.\n     */\n    Pathfinder.prototype.init = function (chart) {\n        // Initialize pathfinder with chart context\n        this.chart = chart;\n        // Init connection reference list\n        this.connections = [];\n        // Recalculate paths/obstacles on chart redraw\n        addEvent(chart, 'redraw', function () {\n            this.pathfinder.update();\n        });\n    };\n    /**\n     * Update Pathfinder connections from scratch.\n     *\n     * @function Highcharts.Pathfinder#update\n     *\n     * @param {boolean} [deferRender]\n     *        Whether or not to defer rendering of connections until\n     *        series.afterAnimate event has fired. Used on first render.\n     */\n    Pathfinder.prototype.update = function (deferRender) {\n        var chart = this.chart,\n            pathfinder = this,\n            oldConnections = pathfinder.connections;\n        // Rebuild pathfinder connections from options\n        pathfinder.connections = [];\n        chart.series.forEach(function (series) {\n            if (series.visible && !series.options.isInternal) {\n                series.points.forEach(function (point) {\n                    var _a;\n                    var ganttPointOptions = point.options;\n                    // For Gantt series the connect could be\n                    // defined as a dependency\n                    if (ganttPointOptions && ganttPointOptions.dependency) {\n                        ganttPointOptions.connect = ganttPointOptions\n                            .dependency;\n                    }\n                    var connects = ((_a = point.options) === null || _a === void 0 ? void 0 : _a.connect) ?\n                            splat(point.options.connect) :\n                            [];\n                    var to;\n                    if (point.visible && point.isInside !== false) {\n                        connects.forEach(function (connect) {\n                            var toId = typeof connect === 'string' ?\n                                    connect :\n                                    connect.to;\n                            if (toId) {\n                                to = chart.get(toId);\n                            }\n                            if (to instanceof (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default()) &&\n                                to.series.visible &&\n                                to.visible &&\n                                to.isInside !== false) {\n                                // Add new connection\n                                pathfinder.connections.push(new Gantt_Connection(point, // From\n                                to, typeof connect === 'string' ?\n                                    {} :\n                                    connect));\n                            }\n                        });\n                    }\n                });\n            }\n        });\n        // Clear connections that should not be updated, and move old info over\n        // to new connections.\n        for (var j = 0, k = void 0, found = void 0, lenOld = oldConnections.length, lenNew = pathfinder.connections.length; j < lenOld; ++j) {\n            found = false;\n            var oldCon = oldConnections[j];\n            for (k = 0; k < lenNew; ++k) {\n                var newCon = pathfinder.connections[k];\n                if ((oldCon.options && oldCon.options.type) ===\n                    (newCon.options && newCon.options.type) &&\n                    oldCon.fromPoint === newCon.fromPoint &&\n                    oldCon.toPoint === newCon.toPoint) {\n                    newCon.graphics = oldCon.graphics;\n                    found = true;\n                    break;\n                }\n            }\n            if (!found) {\n                oldCon.destroy();\n            }\n        }\n        // Clear obstacles to force recalculation. This must be done on every\n        // redraw in case positions have changed. Recalculation is handled in\n        // Connection.getPath on demand.\n        delete this.chartObstacles;\n        delete this.lineObstacles;\n        // Draw the pending connections\n        pathfinder.renderConnections(deferRender);\n    };\n    /**\n     * Draw the chart's connecting paths.\n     *\n     * @function Highcharts.Pathfinder#renderConnections\n     *\n     * @param {boolean} [deferRender]\n     *        Whether or not to defer render until series animation is finished.\n     *        Used on first render.\n     */\n    Pathfinder.prototype.renderConnections = function (deferRender) {\n        if (deferRender) {\n            // Render after series are done animating\n            this.chart.series.forEach(function (series) {\n                var render = function () {\n                        // Find pathfinder connections belonging to this series\n                        // that haven't rendered, and render them now.\n                        var pathfinder = series.chart.pathfinder,\n                    conns = pathfinder && pathfinder.connections || [];\n                    conns.forEach(function (connection) {\n                        if (connection.fromPoint &&\n                            connection.fromPoint.series === series) {\n                            connection.render();\n                        }\n                    });\n                    if (series.pathfinderRemoveRenderEvent) {\n                        series.pathfinderRemoveRenderEvent();\n                        delete series.pathfinderRemoveRenderEvent;\n                    }\n                };\n                if (series.options.animation === false) {\n                    render();\n                }\n                else {\n                    series.pathfinderRemoveRenderEvent = addEvent(series, 'afterAnimate', render);\n                }\n            });\n        }\n        else {\n            // Go through connections and render them\n            this.connections.forEach(function (connection) {\n                connection.render();\n            });\n        }\n    };\n    /**\n     * Get obstacles for the points in the chart. Does not include connecting\n     * lines from Pathfinder. Applies algorithmMargin to the obstacles.\n     *\n     * @function Highcharts.Pathfinder#getChartObstacles\n     *\n     * @param {Object} options\n     *        Options for the calculation. Currently only\n     *        `options.algorithmMargin`.\n     *\n     * @param {number} options.algorithmMargin\n     *        The algorithm margin to use for the obstacles.\n\n    * @return {Array<object>}\n     *         An array of calculated obstacles. Each obstacle is defined as an\n     *         object with xMin, xMax, yMin and yMax properties.\n     */\n    Pathfinder.prototype.getChartObstacles = function (options) {\n        var series = this.chart.series,\n            margin = Pathfinder_pick(options.algorithmMargin, 0);\n        var obstacles = [],\n            calculatedMargin;\n        for (var i = 0, sLen = series.length; i < sLen; ++i) {\n            if (series[i].visible && !series[i].options.isInternal) {\n                for (var j = 0, pLen = series[i].points.length, bb = void 0, point = void 0; j < pLen; ++j) {\n                    point = series[i].points[j];\n                    if (point.visible) {\n                        bb = Pathfinder_getPointBB(point);\n                        if (bb) {\n                            obstacles.push({\n                                xMin: bb.xMin - margin,\n                                xMax: bb.xMax + margin,\n                                yMin: bb.yMin - margin,\n                                yMax: bb.yMax + margin\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Sort obstacles by xMin for optimization\n        obstacles = obstacles.sort(function (a, b) {\n            return a.xMin - b.xMin;\n        });\n        // Add auto-calculated margin if the option is not defined\n        if (!Pathfinder_defined(options.algorithmMargin)) {\n            calculatedMargin =\n                options.algorithmMargin =\n                    calculateObstacleMargin(obstacles);\n            obstacles.forEach(function (obstacle) {\n                obstacle.xMin -= calculatedMargin;\n                obstacle.xMax += calculatedMargin;\n                obstacle.yMin -= calculatedMargin;\n                obstacle.yMax += calculatedMargin;\n            });\n        }\n        return obstacles;\n    };\n    /**\n     * Utility function to get metrics for obstacles:\n     * - Widest obstacle width\n     * - Tallest obstacle height\n     *\n     * @function Highcharts.Pathfinder#getObstacleMetrics\n     *\n     * @param {Array<object>} obstacles\n     *        An array of obstacles to inspect.\n     *\n     * @return {Object}\n     *         The calculated metrics, as an object with maxHeight and maxWidth\n     *         properties.\n     */\n    Pathfinder.prototype.getObstacleMetrics = function (obstacles) {\n        var maxWidth = 0,\n            maxHeight = 0,\n            width,\n            height,\n            i = obstacles.length;\n        while (i--) {\n            width = obstacles[i].xMax - obstacles[i].xMin;\n            height = obstacles[i].yMax - obstacles[i].yMin;\n            if (maxWidth < width) {\n                maxWidth = width;\n            }\n            if (maxHeight < height) {\n                maxHeight = height;\n            }\n        }\n        return {\n            maxHeight: maxHeight,\n            maxWidth: maxWidth\n        };\n    };\n    /**\n     * Utility to get which direction to start the pathfinding algorithm\n     * (X vs Y), calculated from a set of marker options.\n     *\n     * @function Highcharts.Pathfinder#getAlgorithmStartDirection\n     *\n     * @param {Highcharts.ConnectorsMarkerOptions} markerOptions\n     *        Marker options to calculate from.\n     *\n     * @return {boolean}\n     *         Returns true for X, false for Y, and undefined for autocalculate.\n     */\n    Pathfinder.prototype.getAlgorithmStartDirection = function (markerOptions) {\n        var xCenter = markerOptions.align !== 'left' &&\n                markerOptions.align !== 'right', yCenter = markerOptions.verticalAlign !== 'top' &&\n                markerOptions.verticalAlign !== 'bottom';\n        return xCenter ?\n            (yCenter ? void 0 : false) : // When x is centered\n            (yCenter ? true : void 0); // When x is off-center\n    };\n    return Pathfinder;\n}());\n/**\n * @name Highcharts.Pathfinder#algorithms\n * @type {Highcharts.Dictionary<Function>}\n */\nPathfinder.prototype.algorithms = PathfinderAlgorithms;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Gantt_Pathfinder = (Pathfinder);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The default pathfinder algorithm to use for a chart. It is possible to define\n * your own algorithms by adding them to the\n * `Highcharts.Pathfinder.prototype.algorithms`\n * object before the chart has been created.\n *\n * The default algorithms are as follows:\n *\n * `straight`:      Draws a straight line between the connecting\n *                  points. Does not avoid other points when drawing.\n *\n * `simpleConnect`: Finds a path between the points using right angles\n *                  only. Takes only starting/ending points into\n *                  account, and will not avoid other points.\n *\n * `fastAvoid`:     Finds a path between the points using right angles\n *                  only. Will attempt to avoid other points, but its\n *                  focus is performance over accuracy. Works well with\n *                  less dense datasets.\n *\n * @typedef {\"fastAvoid\"|\"simpleConnect\"|\"straight\"|string} Highcharts.PathfinderTypeValue\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Extensions/ArrowSymbols.js\n/* *\n *\n *  (c) 2017 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Creates an arrow symbol. Like a triangle, except not filled.\n * ```\n *                   o\n *             o\n *       o\n * o\n *       o\n *             o\n *                   o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the arrow\n *\n * @param {number} y\n *        y position of the arrow\n *\n * @param {number} w\n *        width of the arrow\n *\n * @param {number} h\n *        height of the arrow\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction arrow(x, y, w, h) {\n    return [\n        ['M', x, y + h / 2],\n        ['L', x + w, y],\n        ['L', x, y + h / 2],\n        ['L', x + w, y + h]\n    ];\n}\n/**\n * Creates a half-width arrow symbol. Like a triangle, except not filled.\n * ```\n *       o\n *    o\n * o\n *    o\n *       o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the arrow\n *\n * @param {number} y\n *        y position of the arrow\n *\n * @param {number} w\n *        width of the arrow\n *\n * @param {number} h\n *        height of the arrow\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction arrowHalf(x, y, w, h) {\n    return arrow(x, y, w / 2, h);\n}\n/**\n * @private\n */\nfunction compose(SVGRendererClass) {\n    var symbols = SVGRendererClass.prototype.symbols;\n    symbols.arrow = arrow;\n    symbols['arrow-filled'] = triangleLeft;\n    symbols['arrow-filled-half'] = triangleLeftHalf;\n    symbols['arrow-half'] = arrowHalf;\n    symbols['triangle-left'] = triangleLeft;\n    symbols['triangle-left-half'] = triangleLeftHalf;\n}\n/**\n * Creates a left-oriented triangle.\n * ```\n *             o\n *       ooooooo\n * ooooooooooooo\n *       ooooooo\n *             o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the triangle\n *\n * @param {number} y\n *        y position of the triangle\n *\n * @param {number} w\n *        width of the triangle\n *\n * @param {number} h\n *        height of the triangle\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction triangleLeft(x, y, w, h) {\n    return [\n        ['M', x + w, y],\n        ['L', x, y + h / 2],\n        ['L', x + w, y + h],\n        ['Z']\n    ];\n}\n/**\n * Creates a half-width, left-oriented triangle.\n * ```\n *       o\n *    oooo\n * ooooooo\n *    oooo\n *       o\n * ```\n *\n * @private\n * @function\n *\n * @param {number} x\n *        x position of the triangle\n *\n * @param {number} y\n *        y position of the triangle\n *\n * @param {number} w\n *        width of the triangle\n *\n * @param {number} h\n *        height of the triangle\n *\n * @return {Highcharts.SVGPathArray}\n *         Path array\n */\nfunction triangleLeftHalf(x, y, w, h) {\n    return triangleLeft(x, y, w / 2, h);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar ArrowSymbols = {\n    compose: compose\n};\n/* harmony default export */ var Extensions_ArrowSymbols = (ArrowSymbols);\n\n;// ./code/es5/es-modules/masters/modules/pathfinder.js\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Pathfinder = G.Pathfinder || Gantt_Pathfinder;\nExtensions_ArrowSymbols.compose(G.SVGRenderer);\nG.Pathfinder.compose(G.Chart, G.Point);\n/* harmony default export */ var pathfinder_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__260__", "ConnectionComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pathfinder_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "defined", "error", "merge", "objectEach", "deg2rad", "max", "Math", "min", "Connection", "from", "to", "options", "init", "fromPoint", "toPoint", "chart", "series", "pathfinder", "<PERSON><PERSON><PERSON>", "path", "attribs", "styledMode", "anim", "pathGraphic", "connection", "graphics", "group", "renderer", "g", "addClass", "attr", "zIndex", "add", "seriesGroup", "translate", "plotLeft", "plotTop", "opacity", "animate", "add<PERSON><PERSON><PERSON>", "type", "markerVector", "radians", "box", "width", "height", "pathVector", "segment", "point", "anchor", "getPathfinderAnchorPoint", "enabled", "length", "x", "y", "getRadiansToVector", "getMarkerVector", "radius", "rotation", "rotationOriginX", "rotationOriginY", "symbol", "colorIndex", "fill", "color", "stroke", "lineColor", "lineWidth", "animation", "<PERSON><PERSON><PERSON>", "algorithm", "algorithms", "chartObstacles", "obstacles", "requiresObstacles", "getChartObstacles", "connectors", "<PERSON><PERSON><PERSON>gin", "chartObstacleMetrics", "getObstacleMetrics", "startMarker", "<PERSON><PERSON><PERSON><PERSON>", "lineObstacles", "obstacleMetrics", "hardBounds", "xMin", "xMax", "plot<PERSON>id<PERSON>", "yMin", "yMax", "plotHeight", "obstacleOptions", "margin", "startDirectionX", "getAlgorithmStartDirection", "render", "dashStyle", "dashstyle", "marker", "ceil", "pathResult", "concat", "destroy", "val", "r", "i", "push", "prevSeg", "nextSeg", "x1", "y1", "x2", "y2", "directionX", "directionY", "abs", "pick", "PathfinderAlgorithms_min", "PathfinderAlgorithms_max", "findLastObstacleBefore", "startIx", "cursor", "cmp", "left", "right", "findObstacleFromPoint", "obstacle", "pathFromSegments", "segments", "start", "end", "limitObstacleToBounds", "bounds", "simpleConnect", "endSegment", "startObstacle", "waypoint", "useMax", "endPoint", "startObstacleIx", "endObstacleIx", "dir", "copyFromPoint", "fromKey", "to<PERSON><PERSON>", "offset", "getMeOut", "direction", "prevWaypoint", "waypoint2", "Series_PathUtilities", "fastAvoid", "dirIsX", "extractedEndPoint", "endSegments", "metrics", "softMinX", "max<PERSON><PERSON><PERSON>", "softMaxX", "softMinY", "maxHeight", "softMaxY", "forceObstacleBreak", "pivotPoint", "directionIsX", "firstPoint", "lastPoint", "highestPoint", "lowestPoint", "searchDirection", "getDodgeDirection", "softBounds", "soft", "hard", "toPointMax", "toPointMin", "maxOutOfSoftBounds", "minOutOfSoftBounds", "maxOutOfHardBounds", "minOutOfHardBounds", "minDistance", "maxDistance", "minPivot", "maxPivot", "slice", "clearPathTo", "pivot", "waypointUseMax", "envelopingObstacle", "secondEnvelopingObstacle", "envelopWaypoint", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "ConnectorsDefaults", "align", "verticalAlign", "inside", "setOptions", "PathfinderComposition_defined", "PathfinderComposition_error", "PathfinderComposition_merge", "getPointBB", "shapeArgs", "bb", "graphic", "getBBox", "plotX", "plotY", "pointGetPathfinderAnchorPoint", "markerOptions", "pointGetRadiansToVector", "v1", "v2", "atan2", "pointGetMarkerVector", "markerRadius", "twoPI", "PI", "rectWidth", "rectHeight", "rAtan", "rect<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rectHalfHeight", "rectHorizontalCenter", "rectVerticalCenter", "edgePoint", "theta", "tan<PERSON><PERSON><PERSON>", "leftOrRightRegion", "xFactor", "yFactor", "tan", "cos", "sin", "compose", "ChartClass", "PathfinderClass", "PointClass", "pointProto", "callbacks", "reduce", "acc", "update", "PathfinderComposition", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "addEvent", "Pathfinder_defined", "Pathfinder_pick", "splat", "Pathfinder_max", "Pathfinder_min", "Pathfinder", "connections", "deferRender", "oldConnections", "for<PERSON>ach", "visible", "isInternal", "points", "_a", "ganttPointOptions", "dependency", "connect", "connects", "isInside", "toId", "j", "k", "found", "lenOld", "len<PERSON><PERSON>", "oldCon", "newCon", "renderConnections", "conns", "pathfinderRemoveRenderEvent", "<PERSON><PERSON><PERSON><PERSON>", "sLen", "pLen", "Pathfinder_getPointBB", "sort", "b", "calculateObstacleMargin", "onstacleDistance", "len", "distances", "calculateObstacleDistance", "bb<PERSON><PERSON><PERSON>", "yOverlap", "xOverlap", "xDistance", "Infinity", "yDistance", "floor", "xCenter", "yCenter", "arrow", "w", "h", "arrow<PERSON>alf", "triangleLeft", "triangleLeftHalf", "straight", "G", "Extensions_ArrowSymbols", "SVGRendererClass", "symbols", "<PERSON><PERSON><PERSON><PERSON>", "Chart", "Point"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,EACvE,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAC,CAAEJ,GACjG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,EAExGJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAC9E,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAguDNC,EAhuDUC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAgB,CAC/D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAejHE,EAAU,AAACD,IAA+EC,OAAO,CAAEC,EAAQ,AAACF,IAA+EE,KAAK,CAAEC,EAAQ,AAACH,IAA+EG,KAAK,CAAEC,EAAa,AAACJ,IAA+EI,UAAU,CAMxYC,EAAU,AAACL,IAA+EK,OAAO,CAAEC,EAAMC,KAAKD,GAAG,CAAEE,EAAMD,KAAKC,GAAG,CAuBjIC,EAA4B,WAC5B,SAASA,EAAWC,CAAI,CAAEC,CAAE,CAAEC,CAAO,EACjC,IAAI,CAACC,IAAI,CAACH,EAAMC,EAAIC,EACxB,CA2SA,OA5RAH,EAAWf,SAAS,CAACmB,IAAI,CAAG,SAAUH,CAAI,CAAEC,CAAE,CAAEC,CAAO,EACnD,IAAI,CAACE,SAAS,CAAGJ,EACjB,IAAI,CAACK,OAAO,CAAGJ,EACf,IAAI,CAACC,OAAO,CAAGA,EACf,IAAI,CAACI,KAAK,CAAGN,EAAKO,MAAM,CAACD,KAAK,CAC9B,IAAI,CAACE,UAAU,CAAG,IAAI,CAACF,KAAK,CAACE,UAAU,AAC3C,EAgBAT,EAAWf,SAAS,CAACyB,UAAU,CAAG,SAAUC,CAAI,CAAEC,CAAO,EACrD,IACIL,EAAQ,IAAI,CAACA,KAAK,CAClBM,EAAaN,EAAMM,UAAU,CAC7BJ,EAAa,IAAI,CAACA,UAAU,CAC5BK,EAAO,CAAC,EACRC,EAAcC,AALD,IAAI,CAKQC,QAAQ,EAAID,AALxB,IAAI,CAK+BC,QAAQ,CAACN,IAAI,AAE5DF,CAAAA,EAAWS,KAAK,EACjBT,CAAAA,EAAWS,KAAK,CAAGX,EAAMY,QAAQ,CAACC,CAAC,GAC9BC,QAAQ,CAAC,+BACTC,IAAI,CAAC,CAAEC,OAAQ,EAAG,GAClBC,GAAG,CAACjB,EAAMkB,WAAW,CAAA,EAK9BhB,EAAWS,KAAK,CAACQ,SAAS,CAACnB,EAAMoB,QAAQ,CAAEpB,EAAMqB,OAAO,EAElDb,GAAeA,EAAYI,QAAQ,GACrCJ,EAAcR,EAAMY,QAAQ,CAACR,IAAI,GAC5Ba,GAAG,CAACf,EAAWS,KAAK,EACpBL,GACDE,EAAYO,IAAI,CAAC,CACbO,QAAS,CACb,IAIRd,EAAYO,IAAI,CAACV,GACjBE,EAAKxC,CAAC,CAAGqC,EACJE,GACDC,CAAAA,EAAKe,OAAO,CAAG,CAAA,EAEnBd,EAAYe,OAAO,CAAChB,GAEpB,IAAI,CAACG,QAAQ,CAAG,IAAI,CAACA,QAAQ,EAAI,CAAC,EAClC,IAAI,CAACA,QAAQ,CAACN,IAAI,CAAGI,CACzB,EAmBAf,EAAWf,SAAS,CAAC8C,SAAS,CAAG,SAAUC,CAAI,CAAE7B,CAAO,CAAEQ,CAAI,EAC1D,IAQIsB,EACAC,EAEAC,EACAC,EACAC,EACAC,EACAC,EAdAhC,EAAQS,AADK,IAAI,CACEX,SAAS,CAACG,MAAM,CAACD,KAAK,CACzCE,EAAaF,EAAME,UAAU,CAC7BU,EAAWZ,EAAMY,QAAQ,CACzBqB,EAASR,AAAS,UAATA,EACLhB,AALS,IAAI,CAKFX,SAAS,CACpBW,AANS,IAAI,CAMFV,OAAO,CACtBmC,EAASD,EAAME,wBAAwB,CAACvC,EASvCA,CAAAA,EAAQwC,OAAO,EAUhBJ,CAAAA,CALAA,EADAP,AAAS,UAATA,EACUrB,CAAI,CAAC,EAAE,CAGPA,CAAI,CAACA,EAAKiC,MAAM,CAAG,EAAE,GAEpBL,AAAe,MAAfA,CAAO,CAAC,EAAE,EAAYA,AAAe,MAAfA,CAAO,CAAC,EAAE,AAAO,IAClDD,EAAa,CACTO,EAAGN,CAAO,CAAC,EAAE,CACbO,EAAGP,CAAO,CAAC,EAAE,AACjB,EAGAL,EAAUM,EAAMO,kBAAkB,CAACT,EAAYG,GAC/CR,EAAeO,EAAMQ,eAAe,CAACd,EAAS/B,EAAQ8C,MAAM,CAAER,GAO1DtC,EAAQiC,KAAK,EAAIjC,EAAQkC,MAAM,EAC/BD,EAAQjC,EAAQiC,KAAK,CACrBC,EAASlC,EAAQkC,MAAM,EAGvBD,EAAQC,EAASlC,AAAiB,EAAjBA,EAAQ8C,MAAM,CAGnCjC,AAjDa,IAAI,CAiDNC,QAAQ,CAAGD,AAjDT,IAAI,CAiDgBC,QAAQ,EAAI,CAAC,EAC9CkB,EAAM,CACFU,EAAGZ,EAAaY,CAAC,CAAIT,EAAQ,EAC7BU,EAAGb,EAAaa,CAAC,CAAIT,EAAS,EAC9BD,MAAOA,EACPC,OAAQA,EACRa,SAfO,CAAChB,EAAUtC,EAgBlBuD,gBAAiBlB,EAAaY,CAAC,CAC/BO,gBAAiBnB,EAAaa,CAAC,AACnC,EACK9B,AA3DQ,IAAI,CA2DDC,QAAQ,CAACe,EAAK,CAqB1BhB,AAhFS,IAAI,CAgFFC,QAAQ,CAACe,EAAK,CAACF,OAAO,CAACK,IAnBlCnB,AA7DS,IAAI,CA6DFC,QAAQ,CAACe,EAAK,CAAGb,EACvBkC,MAAM,CAAClD,EAAQkD,MAAM,EACrBhC,QAAQ,CAAC,oCAAsCW,EAAtC,4BACa,IAAI,CAAC3B,SAAS,CAACiD,UAAU,EAC/ChC,IAAI,CAACa,GACLX,GAAG,CAACf,EAAWS,KAAK,EACpBC,EAASN,UAAU,EACpBG,AApEK,IAAI,CAoEEC,QAAQ,CAACe,EAAK,CAACV,IAAI,CAAC,CAC3BiC,KAAMpD,EAAQqD,KAAK,EAAIxC,AArEtB,IAAI,CAqE6BX,SAAS,CAACmD,KAAK,CACjDC,OAAQtD,EAAQuD,SAAS,CACzB,eAAgBvD,EAAQwD,SAAS,CACjC9B,QAAS,CACb,GACKC,OAAO,CAAC,CACTD,QAAS,CACb,EAAGW,EAAMhC,MAAM,CAACL,OAAO,CAACyD,SAAS,GAOjD,EAaA5D,EAAWf,SAAS,CAAC4E,OAAO,CAAG,SAAU1D,CAAO,EAC5C,IAAIM,EAAa,IAAI,CAACA,UAAU,CAC5BF,EAAQ,IAAI,CAACA,KAAK,CAClBuD,EAAYrD,EAAWsD,UAAU,CAAC5D,EAAQ6B,IAAI,CAAC,CAC/CgC,EAAiBvD,EAAWuD,cAAc,OAC9C,AAAI,AAAqB,YAArB,OAAOF,GACPrE,EAAM,IAAMU,EAAQ6B,IAAI,CAAG,oCACpB,CACHrB,KAAM,EAAE,CACRsD,UAAW,EAAE,AACjB,IAGAH,EAAUI,iBAAiB,EAAI,CAACF,IAChCA,EACIvD,EAAWuD,cAAc,CACrBvD,EAAW0D,iBAAiB,CAAChE,GAGrCI,EAAMJ,OAAO,CAACiE,UAAU,CAACC,eAAe,CACpClE,EAAQkE,eAAe,CAE3B5D,EAAW6D,oBAAoB,CAC3B7D,EAAW8D,kBAAkB,CAACP,IAG/BF,EAEP,IAAI,CAACzD,SAAS,CAACqC,wBAAwB,CAACvC,EAAQqE,WAAW,EAE3D,IAAI,CAAClE,OAAO,CAACoC,wBAAwB,CAACvC,EAAQsE,SAAS,EAAG/E,EAAM,CAC5DsE,eAAgBA,EAChBU,cAAejE,EAAWiE,aAAa,EAAI,EAAE,CAC7CC,gBAAiBlE,EAAW6D,oBAAoB,CAChDM,WAAY,CACRC,KAAM,EACNC,KAAMvE,EAAMwE,SAAS,CACrBC,KAAM,EACNC,KAAM1E,EAAM2E,UAAU,AAC1B,EACAC,gBAAiB,CACbC,OAAQjF,EAAQkE,eAAe,AACnC,EACAgB,gBAAiB5E,EAAW6E,0BAA0B,CAACnF,EAAQqE,WAAW,CAC9E,EAAGrE,IACP,EAMAH,EAAWf,SAAS,CAACsG,MAAM,CAAG,WAC1B,IACIlF,EAAYW,AADC,IAAI,CACMX,SAAS,CAChCG,EAASH,EAAUG,MAAM,CACzBD,EAAQC,EAAOD,KAAK,CACpBE,EAAaF,EAAME,UAAU,CAC7BG,EAAU,CAAC,EACXT,EAAUT,EAAMa,EAAMJ,OAAO,CAACiE,UAAU,CACxC5D,EAAOL,OAAO,CAACiE,UAAU,CACzB/D,EAAUF,OAAO,CAACiE,UAAU,CAC5BpD,AATa,IAAI,CASNb,OAAO,CAElB,EAACI,EAAMM,UAAU,GACjBD,EAAQ6C,MAAM,CAAGtD,EAAQuD,SAAS,EAAIrD,EAAUmD,KAAK,CACrD5C,CAAO,CAAC,eAAe,CAAGT,EAAQwD,SAAS,CACvCxD,EAAQqF,SAAS,EACjB5E,CAAAA,EAAQ6E,SAAS,CAAGtF,EAAQqF,SAAS,AAAD,GAG5C5E,EAAQ,KAAQ,CACZ,qDAC0BP,EAAUiD,UAAU,CAG7C9D,EAAQW,AAFbA,CAAAA,EAAUT,EAAMkB,EAAST,EAAO,EAEXuF,MAAM,CAACzC,MAAM,GAC9B9C,CAAAA,EAAQuF,MAAM,CAACzC,MAAM,CAAGlD,EAAIF,EAAIC,KAAK6F,IAAI,CAAC,AAACxF,CAAAA,EAAQkE,eAAe,EAAI,CAAA,EAAK,GAAK,EAAG,GAAI,EAAC,EAG5F,IAAIuB,EAAa5E,AA3BA,IAAI,CA2BO6C,OAAO,CAAC1D,GAChCQ,EAAOiF,EAAWjF,IAAI,AAGtBiF,CAAAA,EAAW3B,SAAS,GACpBxD,EAAWiE,aAAa,CACpBjE,EAAWiE,aAAa,EAAI,EAAE,CAClCjE,EAAWiE,aAAa,CACpBjE,EAAWiE,aAAa,CAACmB,MAAM,CAACD,EAAW3B,SAAS,GAG5DjD,AAtCiB,IAAI,CAsCVN,UAAU,CAACC,EAAMC,GAE5BI,AAxCiB,IAAI,CAwCVe,SAAS,CAAC,QAASrC,EAAMS,EAAQuF,MAAM,CAAEvF,EAAQqE,WAAW,EAAG7D,GAC1EK,AAzCiB,IAAI,CAyCVe,SAAS,CAAC,MAAOrC,EAAMS,EAAQuF,MAAM,CAAEvF,EAAQsE,SAAS,EAAG9D,EAC1E,EAMAX,EAAWf,SAAS,CAAC6G,OAAO,CAAG,WACvB,IAAI,CAAC7E,QAAQ,GACbtB,EAAW,IAAI,CAACsB,QAAQ,CAAE,SAAU8E,CAAG,EACnCA,EAAID,OAAO,EACf,GACA,OAAO,IAAI,CAAC7E,QAAQ,CAE5B,EACOjB,CACX,MAqKA,SAAqBW,CAAI,CAAEqF,CAAC,EAExB,IAAK,IADD1H,EAAI,EAAE,CACD2H,EAAI,EAAGA,EAAItF,EAAKiC,MAAM,CAAEqD,IAAK,CAClC,IAAIpD,EAAIlC,CAAI,CAACsF,EAAE,CAAC,EAAE,CACdnD,EAAInC,CAAI,CAACsF,EAAE,CAAC,EAAE,CAClB,GAAI,AAAa,UAAb,OAAOpD,GAAkB,AAAa,UAAb,OAAOC,GAEhC,GAAImD,AAAM,IAANA,EACA3H,EAAE4H,IAAI,CAAC,CAAC,IAAKrD,EAAGC,EAAE,OAEjB,GAAImD,IAAMtF,EAAKiC,MAAM,CAAG,EACzBtE,EAAE4H,IAAI,CAAC,CAAC,IAAKrD,EAAGC,EAAE,OAGjB,GAAIkD,EAAG,CACR,IAAIG,EAAUxF,CAAI,CAACsF,EAAI,EAAE,CACrBG,EAAUzF,CAAI,CAACsF,EAAI,EAAE,CACzB,GAAIE,GAAWC,EAAS,CACpB,IAAIC,EAAKF,CAAO,CAAC,EAAE,CACfG,EAAKH,CAAO,CAAC,EAAE,CACfI,EAAKH,CAAO,CAAC,EAAE,CACfI,EAAKJ,CAAO,CAAC,EAAE,CAEnB,GAAI,AAAc,UAAd,OAAOC,GACP,AAAc,UAAd,OAAOE,GACP,AAAc,UAAd,OAAOD,GACP,AAAc,UAAd,OAAOE,GACPH,IAAOE,GACPD,IAAOE,EAAI,CACX,IAAIC,EAAaJ,EAAKE,EAAK,EAAI,GAC3BG,EAAaJ,EAAKE,EAAK,EAAI,GAC/BlI,EAAE4H,IAAI,CAAC,CACH,IACArD,EAAI4D,EAAa3G,KAAKC,GAAG,CAACD,KAAK6G,GAAG,CAAC9D,EAAIwD,GAAKL,GAC5ClD,EAAI4D,EAAa5G,KAAKC,GAAG,CAACD,KAAK6G,GAAG,CAAC7D,EAAIwD,GAAKN,GAC/C,CAAE,CACC,IACAnD,EACAC,EACAD,EACAC,EACAD,EAAI4D,EAAa3G,KAAKC,GAAG,CAACD,KAAK6G,GAAG,CAAC9D,EAAI0D,GAAKP,GAC5ClD,EAAI4D,EAAa5G,KAAKC,GAAG,CAACD,KAAK6G,GAAG,CAAC7D,EAAI0D,GAAKR,GAC/C,CACL,CACJ,CAEJ,MAEI1H,EAAE4H,IAAI,CAAC,CAAC,IAAKrD,EAAGC,EAAE,EAG9B,CACA,OAAOxE,CACX,EAqBIsI,EAAO,AAACrH,IAA+EqH,IAAI,CAM3FC,EAA2B/G,KAAKC,GAAG,CAAE+G,EAA2BhH,KAAKD,GAAG,CAAE8G,EAAM7G,KAAK6G,GAAG,CAyB5F,SAASI,EAAuB9C,CAAS,CAAEY,CAAI,CAAEmC,CAAO,EAMpD,IALA,IAGIC,EACAC,EAJAnH,EAAM8E,EAAO,KACTsC,EAAOH,GAAW,EACtBI,EAAQnD,EAAUrB,MAAM,CAAG,EAGxBuE,GAAQC,GAGX,GAAIF,AADJA,CAAAA,EAAMnH,EAAMkE,CAAS,CADrBgD,EAAS,AAACG,EAAQD,GAAS,EACE,CAACtC,IAAI,AAAD,EACvB,EACNsC,EAAOF,EAAS,OAEf,IAAIC,CAAAA,EAAM,CAAA,EAIX,OAAOD,EAHPG,EAAQH,EAAS,EAMzB,OAAOE,EAAO,EAAIA,EAAO,EAAI,CACjC,CAsCA,SAASE,EAAsBpD,CAAS,CAAEzB,CAAK,EAG3C,IAFA,IAvByB8E,EAuBrBrB,EAAIc,EAAuB9C,EAC3BzB,EAAMK,CAAC,CAAG,GAAK,EACZoD,KACH,GAAIhC,CAAS,CAACgC,EAAE,CAACnB,IAAI,EAAItC,EAAMK,CAAC,GA1BXyE,EA4BGrD,CAAS,CAACgC,EAAE,CA3BhCzD,AA2BkCA,EA3B5BK,CAAC,EAAIyE,EAASxC,IAAI,EAC5BtC,AA0BsCA,EA1BhCK,CAAC,EAAIyE,EAASzC,IAAI,EACxBrC,AAyBsCA,EAzBhCM,CAAC,EAAIwE,EAASrC,IAAI,EACxBzC,AAwBsCA,EAxBhCM,CAAC,EAAIwE,EAAStC,IAAI,EAyBpB,OAAOiB,EAGf,OAAO,EACX,CAaA,SAASsB,EAAiBC,CAAQ,EAC9B,IAAI7G,EAAO,EAAE,CACb,GAAI6G,EAAS5E,MAAM,CAAE,CACjBjC,EAAKuF,IAAI,CAAC,CAAC,IAAKsB,CAAQ,CAAC,EAAE,CAACC,KAAK,CAAC5E,CAAC,CAAE2E,CAAQ,CAAC,EAAE,CAACC,KAAK,CAAC3E,CAAC,CAAC,EACzD,IAAK,IAAImD,EAAI,EAAGA,EAAIuB,EAAS5E,MAAM,CAAE,EAAEqD,EACnCtF,EAAKuF,IAAI,CAAC,CAAC,IAAKsB,CAAQ,CAACvB,EAAE,CAACyB,GAAG,CAAC7E,CAAC,CAAE2E,CAAQ,CAACvB,EAAE,CAACyB,GAAG,CAAC5E,CAAC,CAAC,CAE7D,CACA,OAAOnC,CACX,CAgBA,SAASgH,EAAsBL,CAAQ,CAAEM,CAAM,EAC3CN,EAAStC,IAAI,CAAG8B,EAAyBQ,EAAStC,IAAI,CAAE4C,EAAO5C,IAAI,EACnEsC,EAASrC,IAAI,CAAG4B,EAAyBS,EAASrC,IAAI,CAAE2C,EAAO3C,IAAI,EACnEqC,EAASzC,IAAI,CAAGiC,EAAyBQ,EAASzC,IAAI,CAAE+C,EAAO/C,IAAI,EACnEyC,EAASxC,IAAI,CAAG+B,EAAyBS,EAASxC,IAAI,CAAE8C,EAAO9C,IAAI,CACvE,CAoDA,IAAI+C,EAAgB,SAAUJ,CAAK,CAAEC,CAAG,CAAEvH,CAAO,EAC7C,IAMI2H,EAGAC,EAEAC,EACAC,EACAC,EAbAV,EAAW,EAAE,CACbxD,EAAiB7D,EAAQ6D,cAAc,CACvCmE,EAAkBd,EAAsBrD,EACxCyD,GACAW,EAAgBf,EAAsBrD,EACtC0D,GAEAW,EAAMzB,EAAKzG,EAAQkF,eAAe,CAClCsB,EAAIe,EAAI7E,CAAC,CAAG4E,EAAM5E,CAAC,EAAI8D,EAAIe,EAAI5E,CAAC,CAAG2E,EAAM3E,CAAC,GAAK,IAAM,IAYzD,SAASwF,EAAcrI,CAAI,CAAEsI,CAAO,CAAErI,CAAE,CAAEsI,CAAK,CAAEC,CAAM,EACnD,IAAIjG,EAAQ,CACJK,EAAG5C,EAAK4C,CAAC,CACTC,EAAG7C,EAAK6C,CAAC,AACb,EAEJ,OADAN,CAAK,CAAC+F,EAAQ,CAAGrI,CAAE,CAACsI,GAASD,EAAQ,CAAIE,CAAAA,GAAU,CAAA,EAC5CjG,CACX,CAMA,SAASkG,EAASpB,CAAQ,CAAE9E,CAAK,CAAEmG,CAAS,EACxC,IAAIV,EAAStB,EAAInE,CAAK,CAACmG,EAAU,CAAGrB,CAAQ,CAACqB,EAAY,MAAM,EACvDhC,EAAInE,CAAK,CAACmG,EAAU,CAAGrB,CAAQ,CAACqB,EAAY,MAAM,EAC1D,OAAOL,EAAc9F,EAAOmG,EAAWrB,EAAUqB,EAAaV,CAAAA,EAAS,MAAQ,KAAI,EAAIA,EAAS,EAAI,GACxG,CAEIG,EAAgB,IAGhBN,EAAa,CACTL,MAFJO,EAAWU,EADG1E,CAAc,CAACoE,EAAc,CACVV,EAAKW,GAGlCX,IAAKA,CACT,EACAQ,EAAWF,GAGXE,EAAWR,EAIXS,EAAkB,KAElBH,EAAWU,EADXX,EAAgB/D,CAAc,CAACmE,EAAgB,CACZV,EAAOY,GAC1Cb,EAAStB,IAAI,CAAC,CACVuB,MAAOA,EACPC,IAAKM,CACT,GAKAA,CAAQ,CAACK,EAAI,EAAIZ,CAAK,CAACY,EAAI,EAEvBL,CAAQ,CAACK,EAAI,EAAIH,CAAQ,CAACG,EAAI,GAE9BJ,EAASR,CAAK,CADdY,EAAMA,AAAQ,MAARA,EAAc,IAAM,IACP,CAAGX,CAAG,CAACW,EAAI,CAC9Bb,EAAStB,IAAI,CAAC,CACVuB,MAAOO,EACPN,IAAKY,EAAcN,EAAUK,EAAKN,EAAeM,EAAOJ,CAAAA,EAAS,MAAQ,KAAI,EAAIA,EAAS,EAAI,GAClG,GAEAI,EAAMA,AAAQ,MAARA,EAAc,IAAM,MAKlC,IAAIO,EAAepB,EAAS5E,MAAM,CAC1B4E,CAAQ,CAACA,EAAS5E,MAAM,CAAG,EAAE,CAAC8E,GAAG,CACjCD,EACRO,EAAWM,EAAcM,EAAcP,EAAKH,GAC5CV,EAAStB,IAAI,CAAC,CACVuB,MAAOmB,EACPlB,IAAKM,CACT,GAGA,IAAIa,EAAYP,EAAcN,EAD9BK,EAAMA,AAAQ,MAARA,EAAc,IAAM,IAGtBH,GASJ,OARAV,EAAStB,IAAI,CAAC,CACVuB,MAAOO,EACPN,IAAKmB,CACT,GAEArB,EAAStB,IAAI,CAAC4B,GAGP,CACHnH,KAHOmI,EAAiCvB,EAAiBC,GACzDrH,EAAQ8C,MAAM,EAGdgB,UAAWuD,CACf,CACJ,EAgCA,SAASuB,EAAUtB,CAAK,CAAEC,CAAG,CAAEvH,CAAO,EAqBlC,IAyS6BmH,EAAU9E,EAC/BwG,EAQAf,EAnSJT,EACAS,EACAgB,EAjBAD,EAASpC,EAAKzG,EAAQkF,eAAe,CACrCsB,EAAIe,EAAI7E,CAAC,CAAG4E,EAAM5E,CAAC,EAAI8D,EAAIe,EAAI5E,CAAC,CAAG2E,EAAM3E,CAAC,GAC1CuF,EAAMW,EAAS,IAAM,IACrBE,EAAc,EAAE,CAGhBC,EAAUhJ,EAAQwE,eAAe,CACjCyE,EAAWvC,EAAyBY,EAAM5E,CAAC,CAC3C6E,EAAI7E,CAAC,EAAIsG,EAAQE,QAAQ,CAAG,GAC5BC,EAAWxC,EAAyBW,EAAM5E,CAAC,CAC3C6E,EAAI7E,CAAC,EAAIsG,EAAQE,QAAQ,CAAG,GAC5BE,EAAW1C,EAAyBY,EAAM3E,CAAC,CAC3C4E,EAAI5E,CAAC,EAAIqG,EAAQK,SAAS,CAAG,GAC7BC,EAAW3C,EAAyBW,EAAM3E,CAAC,CAC3C4E,EAAI5E,CAAC,EAAIqG,EAAQK,SAAS,CAAG,GAI7BE,EAAqB,CAAA,EAGrB1F,EAAiB7D,EAAQ6D,cAAc,CACvCoE,EAAgBrB,EAAuB/C,EACvCsF,GACAnB,EAAkBpB,EAAuB/C,EACzCoF,GAOJ,SAASO,EAAWtJ,CAAS,CAAEC,CAAO,CAAEsJ,CAAY,EAChD,IACIC,EACAC,EACAC,EACAC,EAJAC,EAAkB5J,EAAUwC,CAAC,CAAGvC,EAAQuC,CAAC,CAAG,EAAI,EAKhDxC,CAAAA,EAAUwC,CAAC,CAAGvC,EAAQuC,CAAC,EACvBgH,EAAaxJ,EACbyJ,EAAYxJ,IAGZuJ,EAAavJ,EACbwJ,EAAYzJ,GAEZA,EAAUyC,CAAC,CAAGxC,EAAQwC,CAAC,EACvBkH,EAAc3J,EACd0J,EAAezJ,IAGf0J,EAAc1J,EACdyJ,EAAe1J,GAanB,IATA,IAAI4F,EAAIgE,EAAkB,EAElBpD,EAAyBE,EAAuB/C,EACpD8F,EAAUjH,CAAC,EACXmB,EAAepB,MAAM,CAAG,GAGpB,EAEDoB,CAAc,CAACiC,EAAE,EAAKgE,CAAAA,EAAkB,GAAKjG,CAAc,CAACiC,EAAE,CAACpB,IAAI,EAAIiF,EAAUjH,CAAC,EACrFoH,EAAkB,GAAKjG,CAAc,CAACiC,EAAE,CAACnB,IAAI,EAAI+E,EAAWhH,CAAC,AAADA,GAAI,CAGhE,GAAImB,CAAc,CAACiC,EAAE,CAACpB,IAAI,EAAIiF,EAAUjH,CAAC,EACrCmB,CAAc,CAACiC,EAAE,CAACnB,IAAI,EAAI+E,EAAWhH,CAAC,EACtCmB,CAAc,CAACiC,EAAE,CAACjB,IAAI,EAAI+E,EAAajH,CAAC,EACxCkB,CAAc,CAACiC,EAAE,CAAChB,IAAI,EAAI+E,EAAYlH,CAAC,CAAE,CACzC,GAAI8G,EACA,MAAO,CACH9G,EAAGzC,EAAUyC,CAAC,CACdD,EAAGxC,EAAUwC,CAAC,CAAGvC,EAAQuC,CAAC,CACtBmB,CAAc,CAACiC,EAAE,CAACpB,IAAI,CAAG,EACzBb,CAAc,CAACiC,EAAE,CAACnB,IAAI,CAAG,EAC7BwC,SAAUtD,CAAc,CAACiC,EAAE,AAC/B,EAGJ,MAAO,CACHpD,EAAGxC,EAAUwC,CAAC,CACdC,EAAGzC,EAAUyC,CAAC,CAAGxC,EAAQwC,CAAC,CACtBkB,CAAc,CAACiC,EAAE,CAACjB,IAAI,CAAG,EACzBhB,CAAc,CAACiC,EAAE,CAAChB,IAAI,CAAG,EAC7BqC,SAAUtD,CAAc,CAACiC,EAAE,AAC/B,CACJ,CACAA,GAAKgE,CACT,CACA,OAAO3J,CACX,CA6BA,SAAS4J,EAAkB5C,CAAQ,CAAEjH,CAAS,CAAEC,CAAO,CAAE0I,CAAM,CAAEpB,CAAM,EACnE,IAAIuC,EAAavC,EAAOwC,IAAI,CAAExF,EAAagD,EAAOyC,IAAI,CAAEhC,EAAMW,EAAS,IAAM,IAAKsB,EAAa,CAAEzH,EAAGxC,EAAUwC,CAAC,CAAEC,EAAGzC,EAAUyC,CAAC,AAAC,EAAGyH,EAAa,CAAE1H,EAAGxC,EAAUwC,CAAC,CAAEC,EAAGzC,EAAUyC,CAAC,AAAC,EAAG0H,EAAqBlD,CAAQ,CAACe,EAAM,MAAM,EACtN8B,CAAU,CAAC9B,EAAM,MAAM,CAAEoC,EAAqBnD,CAAQ,CAACe,EAAM,MAAM,EACnE8B,CAAU,CAAC9B,EAAM,MAAM,CAAEqC,EAAqBpD,CAAQ,CAACe,EAAM,MAAM,EACnEzD,CAAU,CAACyD,EAAM,MAAM,CAAEsC,EAAqBrD,CAAQ,CAACe,EAAM,MAAM,EACnEzD,CAAU,CAACyD,EAAM,MAAM,CAG3BuC,EAAcjE,EAAIW,CAAQ,CAACe,EAAM,MAAM,CAAGhI,CAAS,CAACgI,EAAI,EAAGwC,EAAclE,EAAIW,CAAQ,CAACe,EAAM,MAAM,CAAGhI,CAAS,CAACgI,EAAI,EAGnHJ,EAAStB,AAAiC,GAAjCA,EAAIiE,EAAcC,GACvBxK,CAAS,CAACgI,EAAI,CAAG/H,CAAO,CAAC+H,EAAI,CAC7BwC,EAAcD,CAGtBL,CAAAA,CAAU,CAAClC,EAAI,CAAGf,CAAQ,CAACe,EAAM,MAAM,CACvCiC,CAAU,CAACjC,EAAI,CAAGf,CAAQ,CAACe,EAAM,MAAM,CACvC,IAAIyC,EAAWnB,EAAWtJ,EACtBkK,EACAvB,EAAO,CAACX,EAAI,GACRkC,CAAU,CAAClC,EAAI,CACnB0C,EAAWpB,EAAWtJ,EACtBiK,EACAtB,EAAO,CAACX,EAAI,GACRiC,CAAU,CAACjC,EAAI,CAevB,OAdAJ,EAAS6C,EACJC,CAAAA,GAAW9C,EACX8C,CAAAA,GAAmB9C,EAKxBA,EAASwC,EACJD,CAAAA,GAAqBvC,EACrBuC,CAAAA,GAA6BvC,EAElCA,EAAS0C,EACJD,CAAAA,GAAqBzC,EACrByC,CAAAA,GAA6BzC,CAEtC,CA2KA,IAVKG,CAAAA,EAAgBf,EAJrBrD,EACIA,EAAegH,KAAK,CAAC7C,EAAiBC,EAAgB,GAGCV,EAAG,EAAK,KA5BtCJ,EA6BetD,CAAc,CAACoE,EAAc,CA7BlC5F,EA6BoCkF,EA5BnEsB,EAASnC,EAAyBS,EAASxC,IAAI,CAAGtC,EAAMK,CAAC,CACzDL,EAAMK,CAAC,CAAGyE,EAASzC,IAAI,EACnBgC,EAAyBS,EAASrC,IAAI,CAAGzC,EAAMM,CAAC,CACpDN,EAAMM,CAAC,CAAGwE,EAAStC,IAAI,EAKvBiD,EAASiC,EAAkB5C,EAC3B9E,EAmBwEiF,EAjBxEuB,EAPS,CACLoB,KAAMjK,EAAQyE,UAAU,CACxByF,KAAMlK,EAAQyE,UAAU,AAC5B,GAqBJqE,EAfOD,EAAS,CACZlG,EAAGN,EAAMM,CAAC,CACVD,EAAGyE,CAAQ,CAACW,EAAS,OAAS,OAAO,CAAIA,CAAAA,EAAS,EAAI,EAAC,CAC3D,EAAI,CACApF,EAAGL,EAAMK,CAAC,CACVC,EAAGwE,CAAQ,CAACW,EAAS,OAAS,OAAO,CAAIA,CAAAA,EAAS,EAAI,EAAC,CAC3D,EAUAiB,EAAYhD,IAAI,CAAC,CACbwB,IAAKA,EACLD,MAAOwB,CACX,GACAvB,EAAMuB,GAIH,AAACb,CAAAA,EAAgBf,EAAsBrD,EAAgB0D,EAAG,EAAK,IAClEO,EAASP,CAAG,CAACW,EAAI,CAAGZ,CAAK,CAACY,EAAI,CAAG,EAKjCY,AAJAA,CAAAA,EAAoB,CAChBpG,EAAG6E,EAAI7E,CAAC,CACRC,EAAG4E,EAAI5E,CAAC,AACZ,CAAA,CACiB,CAACuF,EAAI,CAAGrE,CAAc,CAACoE,EAAc,CAACH,EAASI,EAAM,MAAQA,EAAM,MAAM,CAAIJ,CAAAA,EAAS,EAAI,EAAC,EAC5GiB,EAAYhD,IAAI,CAAC,CACbwB,IAAKA,EACLD,MAAOwB,CACX,GACAvB,EAAMuB,EAMV,MAAO,CACHtI,KAAM4G,EAFVC,EAAWA,AAFXA,CAAAA,EAAWyD,AAnLX,SAASA,EAAY5K,CAAS,CAAEC,CAAO,CAAE0I,CAAM,EAE3C,GAAI3I,EAAUwC,CAAC,GAAKvC,EAAQuC,CAAC,EAAIxC,EAAUyC,CAAC,GAAKxC,EAAQwC,CAAC,CACtD,MAAO,EAAE,CAEb,IAWIoI,EACA1D,EACAQ,EACAmD,EACAC,EACAC,EACAC,EAjBAjD,EAAMW,EAAS,IAAM,IACrBuC,EAAiBpL,EAAQgF,eAAe,CAACC,MAAM,CAC/CwC,EAAS,CACLwC,KAAM,CACFvF,KAAMuE,EACNtE,KAAMwE,EACNtE,KAAMuE,EACNtE,KAAMwE,CACV,EACAY,KAAMlK,EAAQyE,UAAU,AAC5B,EAwGJ,MA3FIwG,AAFJA,CAAAA,EACI/D,EAAsBrD,EAAgB3D,EAAS,EAC1B,IAErB8K,EAAiBjB,EADjBkB,EAAqBpH,CAAc,CAACoH,EAAmB,CACA/K,EAAWC,EAAS0I,EAAQpB,GAEnFD,EAAsByD,EAAoBjL,EAAQyE,UAAU,EAC5D0G,EAAkBtC,EAAS,CACvBlG,EAAGzC,EAAUyC,CAAC,CACdD,EAAGuI,CAAkB,CAACD,EAAiB,OAAS,OAAO,CAClDA,CAAAA,EAAiB,EAAI,EAAC,CAC/B,EAAI,CACAtI,EAAGxC,EAAUwC,CAAC,CACdC,EAAGsI,CAAkB,CAACD,EAAiB,OAAS,OAAO,CAClDA,CAAAA,EAAiB,EAAI,EAAC,CAC/B,EAGAE,CAAAA,EAA2BhE,EAAsBrD,EAAgBsH,EAAe,EACjD,KAG3B3D,EAFA0D,EAA2BrH,CAAc,CAACqH,EAAyB,CAEnBlL,EAAQyE,UAAU,EAElE0G,CAAe,CAACjD,EAAI,CAAG8C,EAAiBrE,EAAyBsE,CAAkB,CAAC/C,EAAM,MAAM,CAAGkD,EAAiB,EAAG,AAACF,CAAAA,CAAwB,CAAChD,EAAM,MAAM,CACzJ+C,CAAkB,CAAC/C,EAAM,MAAM,AAAD,EAAK,GACnCxB,EAA0BuE,CAAkB,CAAC/C,EAAM,MAAM,CAAGkD,EAAiB,EAAK,AAACF,CAAAA,CAAwB,CAAChD,EAAM,MAAM,CACpH+C,CAAkB,CAAC/C,EAAM,MAAM,AAAD,EAAK,GAIvChI,EAAUwC,CAAC,GAAKyI,EAAgBzI,CAAC,EACjCxC,EAAUyC,CAAC,GAAKwI,EAAgBxI,CAAC,EAC7B4G,GACA4B,CAAAA,CAAe,CAACjD,EAAI,CAAG8C,EACnBrE,EAAyBsE,CAAkB,CAAC/C,EAAM,MAAM,CAAEgD,CAAwB,CAAChD,EAAM,MAAM,EAAI,EACnGxB,EAAyBuE,CAAkB,CAAC/C,EAAM,MAAM,CAAEgD,CAAwB,CAAChD,EAAM,MAAM,EAAI,CAAA,EAG3GqB,EAAqB,CAACA,GAKtBA,EAAqB,CAAA,GAG7BlC,EAAW,CAAC,CACJC,MAAOpH,EACPqH,IAAK4D,CACT,EAAE,GAGNJ,EAAQvB,EAAWtJ,EAAW,CAC1BwC,EAAGmG,EAAS1I,EAAQuC,CAAC,CAAGxC,EAAUwC,CAAC,CACnCC,EAAGkG,EAAS3I,EAAUyC,CAAC,CAAGxC,EAAQwC,CAAC,AACvC,EAAGkG,GACHxB,EAAW,CAAC,CACJC,MAAOpH,EACPqH,IAAK,CACD7E,EAAGqI,EAAMrI,CAAC,CACVC,EAAGoI,EAAMpI,CAAC,AACd,CACJ,EAAE,CAEFoI,CAAK,CAAClC,EAAS,IAAM,IAAI,GAAK1I,CAAO,CAAC0I,EAAS,IAAM,IAAI,GAEzDmC,EAAiBjB,EAAkBgB,EAAM5D,QAAQ,CAAE4D,EAAO5K,EAAS,CAAC0I,EAAQpB,GAE5ED,EAAsBuD,EAAM5D,QAAQ,CAAEnH,EAAQyE,UAAU,EACxDoD,EAAW,CACPnF,EAAGmG,EACCkC,EAAMrI,CAAC,CACPqI,EAAM5D,QAAQ,CAAC6D,EAAiB,OAAS,OAAO,CAC3CA,CAAAA,EAAiB,EAAI,EAAC,EAC/BrI,EAAGkG,EACCkC,EAAM5D,QAAQ,CAAC6D,EAAiB,OAAS,OAAO,CAC3CA,CAAAA,EAAiB,EAAI,EAAC,EAC3BD,EAAMpI,CAAC,AACf,EAIAkG,EAAS,CAACA,EACVxB,EAAWA,EAAS3B,MAAM,CAACoF,EAAY,CACnCpI,EAAGqI,EAAMrI,CAAC,CACVC,EAAGoI,EAAMpI,CAAC,AACd,EAAGkF,EAAUgB,MAKrBxB,EAAWA,EAAS3B,MAAM,CAACoF,EAAYzD,CAAQ,CAACA,EAAS5E,MAAM,CAAG,EAAE,CAAC8E,GAAG,CAAEpH,EAAS,CAAC0I,GAExF,EA2DuBvB,EAAOC,EAAKsB,EAAM,EAErBnD,MAAM,CAACqD,EAAYsC,OAAO,KAG1CvH,UAAWuD,CACf,CACJ,CAxZAK,EAAc3D,iBAAiB,CAAG,CAAA,EAyZlC6E,EAAU7E,iBAAiB,CAAG,CAAA,EAkQD,IAAIuH,EA3NR,CAmBrBrH,WAAY,CA0ERpC,KAAM,WAMNiB,OAAQ,EAORU,UAAW,EASX+B,OAAQ,CA+CJ/C,QAAS,CAAA,EAMT+I,MAAO,SAMPC,cAAe,SAIfC,OAAQ,CAAA,EAIRjI,UAAW,CACf,EASAa,YAAa,CAITnB,OAAQ,SACZ,EASAoB,UAAW,CAIPpB,OAAQ,cACZ,CACJ,CACJ,EAgEIwI,EAAa,AAACtM,IAA+EsM,UAAU,CAEvGC,EAAgC,AAACvM,IAA+EC,OAAO,CAAEuM,EAA8B,AAACxM,IAA+EE,KAAK,CAAEuM,EAA8B,AAACzM,IAA+EG,KAAK,CAmBrW,SAASuM,EAAWzJ,CAAK,EACrB,IAAI0J,EAAY1J,EAAM0J,SAAS,CAE/B,GAAIA,EACA,MAAO,CACHrH,KAAMqH,EAAUrJ,CAAC,EAAI,EACrBiC,KAAM,AAACoH,CAAAA,EAAUrJ,CAAC,EAAI,CAAA,EAAMqJ,CAAAA,EAAU9J,KAAK,EAAI,CAAA,EAC/C4C,KAAMkH,EAAUpJ,CAAC,EAAI,EACrBmC,KAAM,AAACiH,CAAAA,EAAUpJ,CAAC,EAAI,CAAA,EAAMoJ,CAAAA,EAAU7J,MAAM,EAAI,CAAA,CACpD,EAGJ,IAAI8J,EAAK3J,EAAM4J,OAAO,EAAI5J,EAAM4J,OAAO,CAACC,OAAO,GAC/C,OAAOF,EAAK,CACRtH,KAAMrC,EAAM8J,KAAK,CAAGH,EAAG/J,KAAK,CAAG,EAC/B0C,KAAMtC,EAAM8J,KAAK,CAAGH,EAAG/J,KAAK,CAAG,EAC/B4C,KAAMxC,EAAM+J,KAAK,CAAGJ,EAAG9J,MAAM,CAAG,EAChC4C,KAAMzC,EAAM+J,KAAK,CAAGJ,EAAG9J,MAAM,CAAG,CACpC,EAAI,IACR,EA0BA,AAAC,SAAUzE,CAAqB,EAwC5B,SAAS4O,EAA8BC,CAAa,EAChD,IACI5J,EACAC,EAFAqJ,EAAKF,EAAW,IAAI,EAGxB,OAAQQ,EAAcf,KAAK,EACvB,IAAK,QACD7I,EAAI,OACJ,KACJ,KAAK,OACDA,EAAI,MACZ,CACA,OAAQ4J,EAAcd,aAAa,EAC/B,IAAK,MACD7I,EAAI,OACJ,KACJ,KAAK,SACDA,EAAI,MACZ,CACA,MAAO,CACHD,EAAGA,EAAIsJ,CAAE,CAACtJ,EAAE,CAAG,AAACsJ,CAAAA,EAAGtH,IAAI,CAAGsH,EAAGrH,IAAI,AAAD,EAAK,EACrChC,EAAGA,EAAIqJ,CAAE,CAACrJ,EAAE,CAAG,AAACqJ,CAAAA,EAAGnH,IAAI,CAAGmH,EAAGlH,IAAI,AAAD,EAAK,CACzC,CACJ,CAgBA,SAASyH,EAAwBC,CAAE,CAAEC,CAAE,EACnC,IAAIzK,EAUJ,MATI,CAAC2J,EAA8Bc,IAC/BzK,CAAAA,EAAM8J,EAAW,IAAI,CAAA,GAEjBW,CAAAA,EAAK,CACD/J,EAAG,AAACV,CAAAA,EAAI0C,IAAI,CAAG1C,EAAI2C,IAAI,AAAD,EAAK,EAC3BhC,EAAG,AAACX,CAAAA,EAAI6C,IAAI,CAAG7C,EAAI8C,IAAI,AAAD,EAAK,CAC/B,CAAA,EAGDnF,KAAK+M,KAAK,CAACD,EAAG9J,CAAC,CAAG6J,EAAG7J,CAAC,CAAE6J,EAAG9J,CAAC,CAAG+J,EAAG/J,CAAC,CAC9C,CAsBA,SAASiK,EAAqB5K,CAAO,CAAE6K,CAAY,CAAEtK,CAAM,EAUvD,IATA,IAAIuK,EAAQlN,AAAU,EAAVA,KAAKmN,EAAE,CAAQd,EAAKF,EAAW,IAAI,EAAGiB,EAAYf,EAAGrH,IAAI,CAAGqH,EAAGtH,IAAI,CAAEsI,EAAahB,EAAGlH,IAAI,CAAGkH,EAAGnH,IAAI,CAAEoI,EAAQtN,KAAK+M,KAAK,CAACM,EAAYD,GAAYG,EAAgBH,EAAY,EAAKI,EAAiBH,EAAa,EAAKI,EAAuBpB,EAAGtH,IAAI,CAAGwI,EAAeG,EAAqBrB,EAAGnH,IAAI,CAAGsI,EAAgBG,EAAY,CACnU5K,EAAG0K,EACHzK,EAAG0K,CACP,EACAE,EAAQxL,EACRyL,EAAW,EACXC,EAAoB,CAAA,EACpBC,EAAU,EACVC,EAAU,EACPJ,EAAQ,CAAC5N,KAAKmN,EAAE,EACnBS,GAASV,EAEb,KAAOU,EAAQ5N,KAAKmN,EAAE,EAClBS,GAASV,EAoCb,OAlCAW,EAAW7N,KAAKiO,GAAG,CAACL,GAChB,AAACA,EAAQ,CAACN,GAAWM,GAASN,GAE9BU,EAAU,GACVF,EAAoB,CAAA,GAEfF,EAAQN,GAASM,GAAU5N,KAAKmN,EAAE,CAAGG,EAE1CU,EAAU,GAELJ,EAAS5N,KAAKmN,EAAE,CAAGG,GAAUM,GAAS,CAAE5N,CAAAA,KAAKmN,EAAE,CAAGG,CAAI,GAE3DS,EAAU,GACVD,EAAoB,CAAA,GAIpBC,EAAU,GAGVD,GACAH,EAAU5K,CAAC,EAAIgL,EAAWR,EAC1BI,EAAU3K,CAAC,EAAIgL,EAAWT,EAAiBM,IAG3CF,EAAU5K,CAAC,EAAIgL,AAAWV,EAAc,CAAA,EAAMQ,CAAO,EAAtCE,EACfJ,EAAU3K,CAAC,EAAIgL,EAAWR,GAE1B7K,EAAOI,CAAC,GAAK0K,GACbE,CAAAA,EAAU5K,CAAC,CAAGJ,EAAOI,CAAC,AAADA,EAErBJ,EAAOK,CAAC,GAAK0K,GACbC,CAAAA,EAAU3K,CAAC,CAAGL,EAAOK,CAAC,AAADA,EAElB,CACHD,EAAG4K,EAAU5K,CAAC,CAAIkK,EAAejN,KAAKkO,GAAG,CAACN,GAC1C5K,EAAG2K,EAAU3K,CAAC,CAAIiK,EAAejN,KAAKmO,GAAG,CAACP,EAC9C,CACJ,CA5IA9P,EAAsBsQ,OAAO,CAnB7B,SAAiBC,CAAU,CAAEC,CAAe,CAAEC,CAAU,EACpD,IAAIC,EAAaD,EAAWpP,SAAS,AAChCqP,CAAAA,EAAW5L,wBAAwB,GAEpCyL,EAAWlP,SAAS,CAACsP,SAAS,CAACrI,IAAI,CAAC,SAAU3F,CAAK,EAE/C,GAAIJ,AAA+B,CAAA,IAA/BA,AADUI,EAAMJ,OAAO,CACfiE,UAAU,CAACzB,OAAO,CAhCtCpC,CAAAA,AAiCuBA,EAjCjBJ,OAAO,CAACM,UAAU,EACxBF,AAgCuBA,EAhCjBC,MAAM,CAACgO,MAAM,CAAC,SAAUC,CAAG,CAAEjO,CAAM,EAKrC,OAJIA,EAAOL,OAAO,EACd6L,EAA4B,CAAA,EAAOxL,EAAOL,OAAO,CAACiE,UAAU,CAAG5D,EAAOL,OAAO,CAACiE,UAAU,EACpF,CAAC,EAAI5D,EAAOL,OAAO,CAACM,UAAU,EAE/BgO,GAAOjO,EAAOL,OAAO,EAAIK,EAAOL,OAAO,CAACM,UAAU,AAC7D,EAAG,CAAA,EAAK,IACRuL,EAA4B,CAAA,EAAOzL,AAyBZA,EAzBkBJ,OAAO,CAACiE,UAAU,CAAG7D,AAyBvCA,EAzB6CJ,OAAO,CAACiE,UAAU,EAAI,CAAC,EAAI7D,AAyBxEA,EAzB8EJ,OAAO,CAACM,UAAU,EACvHsL,EAA4B,0GAyBhB,IAAI,CAACtL,UAAU,CAAG,IAAI2N,EAAgB,IAAI,EAC1C,IAAI,CAAC3N,UAAU,CAACiO,MAAM,CAAC,CAAA,EAE/B,GACAJ,EAAWtL,eAAe,CAAG8J,EAC7BwB,EAAW5L,wBAAwB,CAAG8J,EACtC8B,EAAWvL,kBAAkB,CAAG2J,EAEhCb,EAAWJ,GAEnB,CA8IJ,EAAG7N,GAA0BA,CAAAA,EAAwB,CAAC,CAAA,GAMzB,IAAI+Q,EAAyB/Q,EAGtDgR,EAA+F7Q,EAAoB,KACnH8Q,EAAmH9Q,EAAoBI,CAAC,CAACyQ,GAkBzIE,EAAW,AAACvP,IAA+EuP,QAAQ,CAAEC,EAAqB,AAACxP,IAA+EC,OAAO,CAAEwP,EAAkB,AAACzP,IAA+EqH,IAAI,CAAEqI,EAAQ,AAAC1P,IAA+E0P,KAAK,CAMxZC,EAAiBpP,KAAKD,GAAG,CAAEsP,EAAiBrP,KAAKC,GAAG,CAqHpDqP,EAA4B,WAM5B,SAASA,EAAW7O,CAAK,EACrB,IAAI,CAACH,IAAI,CAACG,EACd,CA8QA,OAxQA6O,EAAWlB,OAAO,CAAG,SAAUC,CAAU,CAAEE,CAAU,EACjDM,EAAsBT,OAAO,CAACC,EAAYiB,EAAYf,EAC1D,EAcAe,EAAWnQ,SAAS,CAACmB,IAAI,CAAG,SAAUG,CAAK,EAEvC,IAAI,CAACA,KAAK,CAAGA,EAEb,IAAI,CAAC8O,WAAW,CAAG,EAAE,CAErBP,EAASvO,EAAO,SAAU,WACtB,IAAI,CAACE,UAAU,CAACiO,MAAM,EAC1B,EACJ,EAUAU,EAAWnQ,SAAS,CAACyP,MAAM,CAAG,SAAUY,CAAW,EAC/C,IAAI/O,EAAQ,IAAI,CAACA,KAAK,CAClBE,EAAa,IAAI,CACjB8O,EAAiB9O,EAAW4O,WAAW,AAE3C5O,CAAAA,EAAW4O,WAAW,CAAG,EAAE,CAC3B9O,EAAMC,MAAM,CAACgP,OAAO,CAAC,SAAUhP,CAAM,EAC7BA,EAAOiP,OAAO,EAAI,CAACjP,EAAOL,OAAO,CAACuP,UAAU,EAC5ClP,EAAOmP,MAAM,CAACH,OAAO,CAAC,SAAUhN,CAAK,EAEjC,IADIoN,EAWA1P,EAVA2P,EAAoBrN,EAAMrC,OAAO,CAGjC0P,GAAqBA,EAAkBC,UAAU,EACjDD,CAAAA,EAAkBE,OAAO,CAAGF,EACvBC,UAAU,AAAD,EAElB,IAAIE,EAAW,AAAC,CAAA,AAAyB,OAAxBJ,CAAAA,EAAKpN,EAAMrC,OAAO,AAAD,GAAeyP,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGG,OAAO,AAAD,EAC3Ed,EAAMzM,EAAMrC,OAAO,CAAC4P,OAAO,EAC3B,EAAE,AAENvN,CAAAA,EAAMiN,OAAO,EAAIjN,AAAmB,CAAA,IAAnBA,EAAMyN,QAAQ,EAC/BD,EAASR,OAAO,CAAC,SAAUO,CAAO,EAC9B,IAAIG,EAAO,AAAmB,UAAnB,OAAOH,EACVA,EACAA,EAAQ7P,EAAE,CACdgQ,GACAhQ,CAAAA,EAAKK,EAAMzB,GAAG,CAACoR,EAAI,EAEnBhQ,aAAe2O,KACf3O,EAAGM,MAAM,CAACiP,OAAO,EACjBvP,EAAGuP,OAAO,EACVvP,AAAgB,CAAA,IAAhBA,EAAG+P,QAAQ,EAEXxP,EAAW4O,WAAW,CAACnJ,IAAI,CAAC,IA/rDPlG,EA+rD4BwC,EACjDtC,EAAI,AAAmB,UAAnB,OAAO6P,EACP,CAAC,EACDA,GAEZ,EAER,EAER,GAGA,IAAK,IAAII,EAAI,EAAGC,EAAI,KAAK,EAAGC,EAAQ,KAAK,EAAGC,EAASf,EAAe3M,MAAM,CAAE2N,EAAS9P,EAAW4O,WAAW,CAACzM,MAAM,CAAEuN,EAAIG,EAAQ,EAAEH,EAAG,CACjIE,EAAQ,CAAA,EACR,IAAIG,EAASjB,CAAc,CAACY,EAAE,CAC9B,IAAKC,EAAI,EAAGA,EAAIG,EAAQ,EAAEH,EAAG,CACzB,IAAIK,EAAShQ,EAAW4O,WAAW,CAACe,EAAE,CACtC,GAAI,AAACI,CAAAA,EAAOrQ,OAAO,EAAIqQ,EAAOrQ,OAAO,CAAC6B,IAAI,AAAD,IACpCyO,CAAAA,EAAOtQ,OAAO,EAAIsQ,EAAOtQ,OAAO,CAAC6B,IAAI,AAAD,GACrCwO,EAAOnQ,SAAS,GAAKoQ,EAAOpQ,SAAS,EACrCmQ,EAAOlQ,OAAO,GAAKmQ,EAAOnQ,OAAO,CAAE,CACnCmQ,EAAOxP,QAAQ,CAAGuP,EAAOvP,QAAQ,CACjCoP,EAAQ,CAAA,EACR,KACJ,CACJ,CACKA,GACDG,EAAO1K,OAAO,EAEtB,CAIA,OAAO,IAAI,CAAC9B,cAAc,CAC1B,OAAO,IAAI,CAACU,aAAa,CAEzBjE,EAAWiQ,iBAAiB,CAACpB,EACjC,EAUAF,EAAWnQ,SAAS,CAACyR,iBAAiB,CAAG,SAAUpB,CAAW,EACtDA,EAEA,IAAI,CAAC/O,KAAK,CAACC,MAAM,CAACgP,OAAO,CAAC,SAAUhP,CAAM,EACtC,IAAI+E,EAAS,WAGL,IAAI9E,EAAaD,EAAOD,KAAK,CAACE,UAAU,CAE5CkQ,AADQlQ,CAAAA,GAAcA,EAAW4O,WAAW,EAAI,EAAE,AAAD,EAC3CG,OAAO,CAAC,SAAUxO,CAAU,EAC1BA,EAAWX,SAAS,EACpBW,EAAWX,SAAS,CAACG,MAAM,GAAKA,GAChCQ,EAAWuE,MAAM,EAEzB,GACI/E,EAAOoQ,2BAA2B,GAClCpQ,EAAOoQ,2BAA2B,GAClC,OAAOpQ,EAAOoQ,2BAA2B,CAEjD,CACIpQ,AAA6B,EAAA,IAA7BA,EAAOL,OAAO,CAACyD,SAAS,CACxB2B,IAGA/E,EAAOoQ,2BAA2B,CAAG9B,EAAStO,EAAQ,eAAgB+E,EAE9E,GAIA,IAAI,CAAC8J,WAAW,CAACG,OAAO,CAAC,SAAUxO,CAAU,EACzCA,EAAWuE,MAAM,EACrB,EAER,EAkBA6J,EAAWnQ,SAAS,CAACkF,iBAAiB,CAAG,SAAUhE,CAAO,EAKtD,IAAK,IADD0Q,EAHArQ,EAAS,IAAI,CAACD,KAAK,CAACC,MAAM,CAC1B4E,EAAS4J,EAAgB7O,EAAQkE,eAAe,CAAE,GAClDJ,EAAY,EAAE,CAETgC,EAAI,EAAG6K,EAAOtQ,EAAOoC,MAAM,CAAEqD,EAAI6K,EAAM,EAAE7K,EAC9C,GAAIzF,CAAM,CAACyF,EAAE,CAACwJ,OAAO,EAAI,CAACjP,CAAM,CAACyF,EAAE,CAAC9F,OAAO,CAACuP,UAAU,CAClD,IAAK,IAAIS,EAAI,EAAGY,EAAOvQ,CAAM,CAACyF,EAAE,CAAC0J,MAAM,CAAC/M,MAAM,CAAEuJ,EAAK,KAAK,EAAG3J,EAAQ,KAAK,EAAG2N,EAAIY,EAAM,EAAEZ,EAEjF3N,AADJA,CAAAA,EAAQhC,CAAM,CAACyF,EAAE,CAAC0J,MAAM,CAACQ,EAAE,AAAD,EAChBV,OAAO,EACbtD,CAAAA,EAAK6E,AAlS7B,SAA+BxO,CAAK,EAChC,IAAI0J,EAAY1J,EAAM0J,SAAS,CAE/B,GAAIA,EACA,MAAO,CACHrH,KAAMqH,EAAUrJ,CAAC,EAAI,EACrBiC,KAAM,AAACoH,CAAAA,EAAUrJ,CAAC,EAAI,CAAA,EAAMqJ,CAAAA,EAAU9J,KAAK,EAAI,CAAA,EAC/C4C,KAAMkH,EAAUpJ,CAAC,EAAI,EACrBmC,KAAM,AAACiH,CAAAA,EAAUpJ,CAAC,EAAI,CAAA,EAAMoJ,CAAAA,EAAU7J,MAAM,EAAI,CAAA,CACpD,EAGJ,IAAI8J,EAAK3J,EAAM4J,OAAO,EAAI5J,EAAM4J,OAAO,CAACC,OAAO,GAC/C,OAAOF,EAAK,CACRtH,KAAMrC,EAAM8J,KAAK,CAAGH,EAAG/J,KAAK,CAAG,EAC/B0C,KAAMtC,EAAM8J,KAAK,CAAGH,EAAG/J,KAAK,CAAG,EAC/B4C,KAAMxC,EAAM+J,KAAK,CAAGJ,EAAG9J,MAAM,CAAG,EAChC4C,KAAMzC,EAAM+J,KAAK,CAAGJ,EAAG9J,MAAM,CAAG,CACpC,EAAI,IACR,EA+QmDG,EAAK,GAE5ByB,EAAUiC,IAAI,CAAC,CACXrB,KAAMsH,EAAGtH,IAAI,CAAGO,EAChBN,KAAMqH,EAAGrH,IAAI,CAAGM,EAChBJ,KAAMmH,EAAGnH,IAAI,CAAGI,EAChBH,KAAMkH,EAAGlH,IAAI,CAAGG,CACpB,GAsBpB,OAfAnB,EAAYA,EAAUgN,IAAI,CAAC,SAAU1S,CAAC,CAAE2S,CAAC,EACrC,OAAO3S,EAAEsG,IAAI,CAAGqM,EAAErM,IAAI,AAC1B,GAEKkK,EAAmB5O,EAAQkE,eAAe,IAC3CwM,EACI1Q,EAAQkE,eAAe,CACnB8M,AAhQpB,SAAiClN,CAAS,EAKtC,IAAK,IAFDmN,EAFAC,EAAMpN,EAAUrB,MAAM,CACtB0O,EAAY,EAAE,CAGTrL,EAAI,EAAGA,EAAIoL,EAAK,EAAEpL,EAGvB,IAAK,IAAIkK,EAAIlK,EAAI,EAAGkK,EAAIkB,EAAK,EAAElB,EAC3BiB,CAAAA,EACIG,AAzChB,SAASA,EAA0BhT,CAAC,CAAE2S,CAAC,CAAEM,CAAQ,EAE7C,IAAIpM,EAAS4J,EAAgBwC,EAAU,IACnCC,EAAWlT,EAAE0G,IAAI,CAAGG,EAAS8L,EAAElM,IAAI,CAAGI,GAClC7G,EAAEyG,IAAI,CAAGI,EAAS8L,EAAEjM,IAAI,CAAGG,EAC/BsM,EAAWnT,EAAEuG,IAAI,CAAGM,EAAS8L,EAAErM,IAAI,CAAGO,GAClC7G,EAAEsG,IAAI,CAAGO,EAAS8L,EAAEpM,IAAI,CAAGM,EAC/BuM,EAAYF,EAAYlT,EAAEsG,IAAI,CAAGqM,EAAEpM,IAAI,CAAGvG,EAAEsG,IAAI,CAAGqM,EAAEpM,IAAI,CAAGoM,EAAErM,IAAI,CAAGtG,EAAEuG,IAAI,CAAI8M,IAC/EC,EAAYH,EAAYnT,EAAEyG,IAAI,CAAGkM,EAAEjM,IAAI,CAAG1G,EAAEyG,IAAI,CAAGkM,EAAEjM,IAAI,CAAGiM,EAAElM,IAAI,CAAGzG,EAAE0G,IAAI,CAAI2M,WAGnF,AAAIF,GAAYD,EACJrM,EACJmM,EAA0BhT,EAAG2S,EAAGpR,KAAKgS,KAAK,CAAC1M,EAAS,IACpDwM,IAEDzC,EAAewC,EAAWE,EACrC,EAwB0C5N,CAAS,CAACgC,EAAE,CAAEhC,CAAS,CAACkM,EAAE,CAAA,EAEjC,IACnBmB,EAAUpL,IAAI,CAACkL,GAM3B,OADAE,EAAUpL,IAAI,CAAC,IACRgJ,EAAepP,KAAKgS,KAAK,CAACR,EAAUL,IAAI,CAAC,SAAU1S,CAAC,CAAE2S,CAAC,EAC1D,OAAQ3S,EAAI2S,CAChB,EAAE,CAGFpR,KAAKgS,KAAK,CAACR,EAAU1O,MAAM,CAAG,IAAI,CAAG,EAAI,GACtC,EAEP,EAqO4CqB,GAChCA,EAAUuL,OAAO,CAAC,SAAUlI,CAAQ,EAChCA,EAASzC,IAAI,EAAIgM,EACjBvJ,EAASxC,IAAI,EAAI+L,EACjBvJ,EAAStC,IAAI,EAAI6L,EACjBvJ,EAASrC,IAAI,EAAI4L,CACrB,IAEG5M,CACX,EAeAmL,EAAWnQ,SAAS,CAACsF,kBAAkB,CAAG,SAAUN,CAAS,EAMzD,IALA,IAEI7B,EACAC,EAHAgH,EAAW,EACXG,EAAY,EAGZvD,EAAIhC,EAAUrB,MAAM,CACjBqD,KACH7D,EAAQ6B,CAAS,CAACgC,EAAE,CAACnB,IAAI,CAAGb,CAAS,CAACgC,EAAE,CAACpB,IAAI,CAC7CxC,EAAS4B,CAAS,CAACgC,EAAE,CAAChB,IAAI,CAAGhB,CAAS,CAACgC,EAAE,CAACjB,IAAI,CAC1CqE,EAAWjH,GACXiH,CAAAA,EAAWjH,CAAI,EAEfoH,EAAYnH,GACZmH,CAAAA,EAAYnH,CAAK,EAGzB,MAAO,CACHmH,UAAWA,EACXH,SAAUA,CACd,CACJ,EAaA+F,EAAWnQ,SAAS,CAACqG,0BAA0B,CAAG,SAAUmH,CAAa,EACrE,IAAIsF,EAAUtF,AAAwB,SAAxBA,EAAcf,KAAK,EACzBe,AAAwB,UAAxBA,EAAcf,KAAK,CAAcsG,EAAUvF,AAAgC,QAAhCA,EAAcd,aAAa,EACtEc,AAAgC,WAAhCA,EAAcd,aAAa,CACnC,OAAOoG,EACFC,EAAAA,GAAU,KAAK,EACfA,EAAAA,GAAiB,KAAK,CAC/B,EACO5C,CACX,IAwFA,SAAS6C,EAAMpP,CAAC,CAAEC,CAAC,CAAEoP,CAAC,CAAEC,CAAC,EACrB,MAAO,CACH,CAAC,IAAKtP,EAAGC,EAAIqP,EAAI,EAAE,CACnB,CAAC,IAAKtP,EAAIqP,EAAGpP,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIqP,EAAI,EAAE,CACnB,CAAC,IAAKtP,EAAIqP,EAAGpP,EAAIqP,EAAE,CACtB,AACL,CA6BA,SAASC,EAAUvP,CAAC,CAAEC,CAAC,CAAEoP,CAAC,CAAEC,CAAC,EACzB,OAAOF,EAAMpP,EAAGC,EAAGoP,EAAI,EAAGC,EAC9B,CAyCA,SAASE,EAAaxP,CAAC,CAAEC,CAAC,CAAEoP,CAAC,CAAEC,CAAC,EAC5B,MAAO,CACH,CAAC,IAAKtP,EAAIqP,EAAGpP,EAAE,CACf,CAAC,IAAKD,EAAGC,EAAIqP,EAAI,EAAE,CACnB,CAAC,IAAKtP,EAAIqP,EAAGpP,EAAIqP,EAAE,CACnB,CAAC,IAAI,CACR,AACL,CA6BA,SAASG,EAAiBzP,CAAC,CAAEC,CAAC,CAAEoP,CAAC,CAAEC,CAAC,EAChC,OAAOE,EAAaxP,EAAGC,EAAGoP,EAAI,EAAGC,EACrC,CAxMA/C,EAAWnQ,SAAS,CAAC8E,UAAU,CA/8Bd,CACbgF,UAAWA,EACXwJ,SAhjBJ,SAAkB9K,CAAK,CAAEC,CAAG,EACxB,MAAO,CACH/G,KAAM,CACF,CAAC,IAAK8G,EAAM5E,CAAC,CAAE4E,EAAM3E,CAAC,CAAC,CACvB,CAAC,IAAK4E,EAAI7E,CAAC,CAAE6E,EAAI5E,CAAC,CAAC,CACtB,CACDmB,UAAW,CAAC,CAAEwD,MAAOA,EAAOC,IAAKA,CAAI,EAAE,AAC3C,CACJ,EAyiBIG,cAAeA,CACnB,EAoqCA,IAAI2K,EAAKjT,GACTiT,CAAAA,EAAEpD,UAAU,CAAGoD,EAAEpD,UAAU,EApN0BA,EAqNrDqD,AAbmB,CAAA,CACfvE,QAlFJ,SAAiBwE,CAAgB,EAC7B,IAAIC,EAAUD,EAAiBzT,SAAS,CAAC0T,OAAO,AAChDA,CAAAA,EAAQV,KAAK,CAAGA,EAChBU,CAAO,CAAC,eAAe,CAAGN,EAC1BM,CAAO,CAAC,oBAAoB,CAAGL,EAC/BK,CAAO,CAAC,aAAa,CAAGP,EACxBO,CAAO,CAAC,gBAAgB,CAAGN,EAC3BM,CAAO,CAAC,qBAAqB,CAAGL,CACpC,CA2EA,CAAA,EAWwBpE,OAAO,CAACsE,EAAEI,WAAW,EAC7CJ,EAAEpD,UAAU,CAAClB,OAAO,CAACsE,EAAEK,KAAK,CAAEL,EAAEM,KAAK,EACR,IAAIzT,EAAmBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,GAET"}