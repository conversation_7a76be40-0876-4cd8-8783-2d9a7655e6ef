{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/offline-exporting\n * @requires highcharts\n * @requires highcharts/modules/exporting\n *\n * Client side exporting module\n *\n * (c) 2015-2025 Torstein Honsi / Oystein Moseng\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"AST\"], require(\"highcharts\")[\"Chart\"], require(\"highcharts\")[\"HttpUtilities\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/offline-exporting\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"AST\"], [\"highcharts/highcharts\",\"Chart\"], [\"highcharts/highcharts\",\"HttpUtilities\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/offline-exporting\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"AST\"], require(\"highcharts\")[\"Chart\"], require(\"highcharts\")[\"HttpUtilities\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"], root[\"Highcharts\"][\"Chart\"], root[\"Highcharts\"][\"HttpUtilities\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__, __WEBPACK_EXTERNAL_MODULE__960__, __WEBPACK_EXTERNAL_MODULE__156__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 156:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__156__;\n\n/***/ }),\n\n/***/ 660:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ offline_exporting_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nvar isSafari = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari, win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win, doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win.document;\n/* *\n *\n *  Constants\n *\n * */\nvar domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n * @private\n * @function Highcharts.dataURLtoBlob\n * @param {string} dataURL\n *        URL to convert\n * @return {string|undefined}\n *         Blob\n */\nfunction dataURLtoBlob(dataURL) {\n    var parts = dataURL\n            .replace(/filename=.*;/, '')\n            .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        var binStr = win.atob(parts[3]),\n            buf = new win.ArrayBuffer(binStr.length),\n            binary = new win.Uint8Array(buf);\n        for (var i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n * @param {string|global.URL} dataURL\n *        The dataURL/Blob to download\n * @param {string} filename\n *        The name of the resulting file (w/extension)\n * @return {void}\n */\nfunction downloadURL(dataURL, filename) {\n    var nav = win.navigator,\n        a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    var // Some browsers have limitations for data URL lengths. Try to convert\n        // to Blob or fall back. Edge always needs that blob.\n        isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n        // Safari on iOS needs Blob in order to download PDF\n        safariBlob = (isSafari &&\n            typeof dataURL === 'string' &&\n            dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch (_a) {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DownloadURL = {\n    dataURLtoBlob: dataURLtoBlob,\n    downloadURL: downloadURL\n};\n/* harmony default export */ var Extensions_DownloadURL = (DownloadURL);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\n;// ./code/es5/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructor\n             *\n             * */\n            function Additions(chart) {\n                this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        Additions.prototype.addUpdate = function (updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        };\n        /**\n         * @private\n         */\n        Additions.prototype.update = function (options, redraw) {\n            var _this = this;\n            this.updates.forEach(function (updateFn) {\n                updateFn.call(_this.chart, options, redraw);\n            });\n        };\n        return Additions;\n    }());\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es5/es-modules/Extensions/Exporting/ExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar isTouchDevice = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice;\n/* *\n *\n *  API Options\n *\n * */\n// Add the export related options\n/**\n * Options for the exporting module. For an overview on the matter, see\n * [the docs](https://www.highcharts.com/docs/export-module/export-module-overview) and\n * read our [Fair Usage Policy](https://www.highcharts.com/docs/export-module/privacy-disclaimer-export).\n *\n * @requires     modules/exporting\n * @optionparent exporting\n */\nvar exporting = {\n    /**\n     * Experimental setting to allow HTML inside the chart (added through\n     * the `useHTML` options), directly in the exported image. This allows\n     * you to preserve complicated HTML structures like tables or bi-directional\n     * text in exported charts.\n     *\n     * Disclaimer: The HTML is rendered in a `foreignObject` tag in the\n     * generated SVG. The official export server is based on PhantomJS,\n     * which supports this, but other SVG clients, like Batik, does not\n     * support it. This also applies to downloaded SVG that you want to\n     * open in a desktop client.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.8\n     * @apioption exporting.allowHTML\n     */\n    /**\n     * Allows the end user to sort the data table by clicking on column headers.\n     *\n     * @since 10.3.3\n     * @apioption exporting.allowTableSorting\n     */\n    allowTableSorting: true,\n    /**\n     * Allow exporting a chart retaining any user-applied CSS.\n     *\n     * Note that this is is default behavior in [styledMode](#chart.styledMode).\n     *\n     * @see [styledMode](#chart.styledMode)\n     *\n     * @sample {highcharts} highcharts/exporting/apply-stylesheets/\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since 12.0.0\n     * @apioption exporting.applyStyleSheets\n     */\n    /**\n     * Additional chart options to be merged into the chart before exporting to\n     * an image format. This does not apply to printing the chart via the export\n     * menu.\n     *\n     * For example, a common use case is to add data labels to improve\n     * readability of the exported chart, or to add a printer-friendly color\n     * scheme to exported PDFs.\n     *\n     * @sample {highcharts} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     * @sample {highstock} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     *\n     * @type      {Highcharts.Options}\n     * @apioption exporting.chartOptions\n     */\n    /**\n     * Whether to enable the exporting module. Disabling the module will\n     * hide the context button, but API methods will still be available.\n     *\n     * @sample {highcharts} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     * @sample {highstock} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     2.0\n     * @apioption exporting.enabled\n     */\n    /**\n     * Function to call if the offline-exporting module fails to export\n     * a chart on the client side, and [fallbackToExportServer](\n     * #exporting.fallbackToExportServer) is disabled. If left undefined, an\n     * exception is thrown instead. Receives two parameters, the exporting\n     * options, and the error from the module.\n     *\n     * @see [fallbackToExportServer](#exporting.fallbackToExportServer)\n     *\n     * @type      {Highcharts.ExportingErrorCallbackFunction}\n     * @since     5.0.0\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.error\n     */\n    /**\n     * Whether or not to fall back to the export server if the offline-exporting\n     * module is unable to export the chart on the client side. This happens for\n     * certain browsers, and certain features (e.g.\n     * [allowHTML](#exporting.allowHTML)), depending on the image type exporting\n     * to. For very complex charts, it is possible that export can fail in\n     * browsers that don't support Blob objects, due to data URL length limits.\n     * It is recommended to define the [exporting.error](#exporting.error)\n     * handler if disabling fallback, in order to notify users in case export\n     * fails.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     4.1.8\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.fallbackToExportServer\n     */\n    /**\n     * The filename, without extension, to use for the exported chart.\n     *\n     * @sample {highcharts} highcharts/exporting/filename/\n     *         Custom file name\n     * @sample {highstock} highcharts/exporting/filename/\n     *         Custom file name\n     *\n     * @type      {string}\n     * @default   chart\n     * @since     2.0\n     * @apioption exporting.filename\n     */\n    /**\n     * Highcharts v11.2.0 and older. An object containing additional key value\n     * data for the POST form that sends the SVG to the export server. For\n     * example, a `target` can be set to make sure the generated image is\n     * received in another frame, or a custom `enctype` or `encoding` can be\n     * set.\n     *\n     * With Highcharts v11.3.0, the `fetch` API replaced the old HTML form. To\n     * modify the request, now use [fetchOptions](#exporting.fetchOptions)\n     * instead.\n     *\n     * @deprecated\n     * @type      {Highcharts.HTMLAttributes}\n     * @since     3.0.8\n     * @apioption exporting.formAttributes\n     */\n    /**\n     * Options for the fetch request used when sending the SVG to the export\n     * server.\n     *\n     * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/fetch)\n     * for more information\n     *\n     * @type {Object}\n     * @since 11.3.0\n     * @apioption exporting.fetchOptions\n     */\n    /**\n     * Path where Highcharts will look for export module dependencies to\n     * load on demand if they don't already exist on `window`. Should currently\n     * point to location of [CanVG](https://github.com/canvg/canvg) library,\n     * [jsPDF](https://github.com/parallax/jsPDF) and\n     * [svg2pdf.js](https://github.com/yWorks/svg2pdf.js), required for client\n     * side export in certain browsers.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/{version}/lib\n     * @since     5.0.0\n     * @apioption exporting.libURL\n     */\n    /**\n     * Analogous to [sourceWidth](#exporting.sourceWidth).\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceHeight\n     */\n    /**\n     * The width of the original chart when exported, unless an explicit\n     * [chart.width](#chart.width) is set, or a pixel width is set on the\n     * container. The width exported raster image is then multiplied by\n     * [scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highstock} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highmaps} maps/exporting/sourcewidth/\n     *         Source size demo\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceWidth\n     */\n    /**\n     * The pixel width of charts exported to PNG or JPG. As of Highcharts\n     * 3.0, the default pixel width is a function of the [chart.width](\n     * #chart.width) or [exporting.sourceWidth](#exporting.sourceWidth) and the\n     * [exporting.scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/width/\n     *         Export to 200px wide images\n     * @sample {highstock} highcharts/exporting/width/\n     *         Export to 200px wide images\n     *\n     * @type      {number}\n     * @since     2.0\n     * @apioption exporting.width\n     */\n    /**\n     * Default MIME type for exporting if `chart.exportChart()` is called\n     * without specifying a `type` option. Possible values are `image/png`,\n     *  `image/jpeg`, `application/pdf` and `image/svg+xml`.\n     *\n     * @type  {Highcharts.ExportingMimeTypeValue}\n     * @since 2.0\n     */\n    type: 'image/png',\n    /**\n     * The URL for the server module converting the SVG string to an image\n     * format. By default this points to Highchart's free web service.\n     *\n     * @since 2.0\n     */\n    url: \"https://export-svg.highcharts.com?v=\".concat((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).version),\n    /**\n     * Settings for a custom font for the exported PDF, when using the\n     * `offline-exporting` module. This is used for languages containing\n     * non-ASCII characters, like Chinese, Russian, Japanese etc.\n     *\n     * As described in the [jsPDF\n     * docs](https://github.com/parallax/jsPDF#use-of-unicode-characters--utf-8),\n     * the 14 standard fonts in PDF are limited to the ASCII-codepage.\n     * Therefore, in order to support other text in the exported PDF, one or\n     * more TTF font files have to be passed on to the exporting module.\n     *\n     * See more in [the\n     * docs](https://www.highcharts.com/docs/export-module/client-side-export).\n     *\n     * @sample {highcharts} highcharts/exporting/offline-download-pdffont/\n     *         Download PDF in a language containing non-Latin characters.\n     *\n     * @since 10.0.0\n     * @requires modules/offline-exporting\n     */\n    pdfFont: {\n        /**\n         * The TTF font file for normal `font-style`. If font variations like\n         * `bold` or `italic` are not defined, the `normal` font will be used\n         * for those too.\n         *\n         * @type string|undefined\n         */\n        normal: void 0,\n        /**\n         * The TTF font file for bold text.\n         *\n         * @type string|undefined\n         */\n        bold: void 0,\n        /**\n         * The TTF font file for bold and italic text.\n         *\n         * @type string|undefined\n         */\n        bolditalic: void 0,\n        /**\n         * The TTF font file for italic text.\n         *\n         * @type string|undefined\n         */\n        italic: void 0\n    },\n    /**\n     * When printing the chart from the menu item in the burger menu, if\n     * the on-screen chart exceeds this width, it is resized. After printing\n     * or cancelled, it is restored. The default width makes the chart\n     * fit into typical paper format. Note that this does not affect the\n     * chart when printing the web page as a whole.\n     *\n     * @since 4.2.5\n     */\n    printMaxWidth: 780,\n    /**\n     * Defines the scale or zoom factor for the exported image compared\n     * to the on-screen display. While for instance a 600px wide chart\n     * may look good on a website, it will look bad in print. The default\n     * scale of 2 makes this chart export to a 1200px PNG or JPG.\n     *\n     * @see [chart.width](#chart.width)\n     * @see [exporting.sourceWidth](#exporting.sourceWidth)\n     *\n     * @sample {highcharts} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highstock} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highmaps} maps/exporting/scale/\n     *         Scale demonstrated\n     *\n     * @since 3.0\n     */\n    scale: 2,\n    /**\n     * Options for the export related buttons, print and export. In addition\n     * to the default buttons listed here, custom buttons can be added.\n     * See [navigation.buttonOptions](#navigation.buttonOptions) for general\n     * options.\n     *\n     * @type     {Highcharts.Dictionary<*>}\n     * @requires modules/exporting\n     */\n    buttons: {\n        /**\n         * Options for the export button.\n         *\n         * In styled mode, export button styles can be applied with the\n         * `.highcharts-contextbutton` class.\n         *\n         * @declare  Highcharts.ExportingButtonsOptionsObject\n         * @extends  navigation.buttonOptions\n         * @requires modules/exporting\n         */\n        contextButton: {\n            /**\n             * A click handler callback to use on the button directly instead of\n             * the popup menu.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-onclick/\n             *         Skip the menu and export the chart directly\n             *\n             * @type      {Function}\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.onclick\n             */\n            /**\n             * See [navigation.buttonOptions.symbolFill](\n             * #navigation.buttonOptions.symbolFill).\n             *\n             * @type      {Highcharts.ColorString}\n             * @default   #666666\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.symbolFill\n             */\n            /**\n             * The horizontal position of the button relative to the `align`\n             * option.\n             *\n             * @type      {number}\n             * @default   -10\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.x\n             */\n            /**\n             * The class name of the context button.\n             */\n            className: 'highcharts-contextbutton',\n            /**\n             * The class name of the menu appearing from the button.\n             */\n            menuClassName: 'highcharts-contextmenu',\n            /**\n             * The symbol for the button. Points to a definition function in\n             * the `Highcharts.Renderer.symbols` collection. The default\n             * `menu` function is part of the exporting module. Possible\n             * values are \"circle\", \"square\", \"diamond\", \"triangle\",\n             * \"triangle-down\", \"menu\", \"menuball\" or custom shape.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-symbol/\n             *         Use a circle for symbol\n             * @sample highcharts/exporting/buttons-contextbutton-symbol-custom/\n             *         Custom shape as symbol\n             *\n             * @type  {Highcharts.SymbolKeyValue|\"menu\"|\"menuball\"|string}\n             * @since 2.0\n             */\n            symbol: 'menu',\n            /**\n             * The key to a [lang](#lang) option setting that is used for the\n             * button's title tooltip. When the key is `contextButtonTitle`, it\n             * refers to [lang.contextButtonTitle](#lang.contextButtonTitle)\n             * that defaults to \"Chart context menu\".\n             *\n             * @since 6.1.4\n             */\n            titleKey: 'contextButtonTitle',\n            /**\n             * A collection of strings pointing to config options for the menu\n             * items. The config options are defined in the\n             * `menuItemDefinitions` option.\n             *\n             * By default, there is the \"View in full screen\" and \"Print\" menu\n             * items, plus one menu item for each of the available export types.\n             *\n             * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             *\n             * @type    {Array<string>}\n             * @default [\"viewFullscreen\", \"printChart\", \"separator\", \"downloadPNG\", \"downloadJPEG\", \"downloadSVG\"]\n             * @since   2.0\n             */\n            menuItems: [\n                'viewFullscreen',\n                'printChart',\n                'separator',\n                'downloadPNG',\n                'downloadJPEG',\n                'downloadSVG'\n            ]\n        }\n    },\n    /**\n     * An object consisting of definitions for the menu items in the context\n     * menu. Each key value pair has a `key` that is referenced in the\n     * [menuItems](#exporting.buttons.contextButton.menuItems) setting,\n     * and a `value`, which is an object with the following properties:\n     *\n     * - **onclick:** The click handler for the menu item\n     *\n     * - **text:** The text for the menu item\n     *\n     * - **textKey:** If internationalization is required, the key to a language\n     *   string\n     *\n     * Custom text for the \"exitFullScreen\" can be set only in lang options\n     * (it is not a separate button).\n     *\n     * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     *\n     *\n     * @type    {Highcharts.Dictionary<Highcharts.ExportingMenuObject>}\n     * @default {\"viewFullscreen\": {}, \"printChart\": {}, \"separator\": {}, \"downloadPNG\": {}, \"downloadJPEG\": {}, \"downloadPDF\": {}, \"downloadSVG\": {}}\n     * @since   5.0.13\n     */\n    menuItemDefinitions: {\n        /**\n         * @ignore\n         */\n        viewFullscreen: {\n            textKey: 'viewFullscreen',\n            onclick: function () {\n                if (this.fullscreen) {\n                    this.fullscreen.toggle();\n                }\n            }\n        },\n        /**\n         * @ignore\n         */\n        printChart: {\n            textKey: 'printChart',\n            onclick: function () {\n                this.print();\n            }\n        },\n        /**\n         * @ignore\n         */\n        separator: {\n            separator: true\n        },\n        /**\n         * @ignore\n         */\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: function () {\n                this.exportChart();\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: function () {\n                this.exportChart({\n                    type: 'application/pdf'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/svg+xml'\n                });\n            }\n        }\n    }\n};\n// Add language\n/**\n * @optionparent lang\n */\nvar lang = {\n    /**\n     * Exporting module only. The text for the menu item to view the chart\n     * in full screen.\n     *\n     * @since 8.0.1\n     */\n    viewFullscreen: 'View in full screen',\n    /**\n     * Exporting module only. The text for the menu item to exit the chart\n     * from full screen.\n     *\n     * @since 8.0.1\n     */\n    exitFullscreen: 'Exit from full screen',\n    /**\n     * Exporting module only. The text for the menu item to print the chart.\n     *\n     * @since    3.0.1\n     * @requires modules/exporting\n     */\n    printChart: 'Print chart',\n    /**\n     * Exporting module only. The text for the PNG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPNG: 'Download PNG image',\n    /**\n     * Exporting module only. The text for the JPEG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadJPEG: 'Download JPEG image',\n    /**\n     * Exporting module only. The text for the PDF download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPDF: 'Download PDF document',\n    /**\n     * Exporting module only. The text for the SVG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadSVG: 'Download SVG vector image',\n    /**\n     * Exporting module menu. The tooltip title for the context menu holding\n     * print and export menu items.\n     *\n     * @since    3.0\n     * @requires modules/exporting\n     */\n    contextButtonTitle: 'Chart context menu'\n};\n/**\n * A collection of options for buttons and menus appearing in the exporting\n * module or in Stock Tools.\n *\n * @requires     modules/exporting\n * @optionparent navigation\n */\nvar navigation = {\n    /**\n     * A collection of options for buttons appearing in the exporting\n     * module.\n     *\n     * In styled mode, the buttons are styled with the\n     * `.highcharts-contextbutton` and `.highcharts-button-symbol` classes.\n     *\n     * @requires modules/exporting\n     */\n    buttonOptions: {\n        /**\n         * Whether to enable buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-enabled/\n         *         Exporting module loaded but buttons disabled\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     2.0\n         * @apioption navigation.buttonOptions.enabled\n         */\n        /**\n         * The pixel size of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolSize: 14,\n        /**\n         * The x position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolX: 14.5,\n        /**\n         * The y position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolY: 13.5,\n        /**\n         * Alignment for the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-align/\n         *         Center aligned\n         *\n         * @type  {Highcharts.AlignValue}\n         * @since 2.0\n         */\n        align: 'right',\n        /**\n         * The pixel spacing between buttons, and between the context button and\n         * the title.\n         *\n         * @sample highcharts/title/widthadjust\n         *         Adjust the spacing when using text button\n         * @since 2.0\n         */\n        buttonSpacing: 5,\n        /**\n         * Pixel height of the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        height: 28,\n        /**\n         * A text string to add to the individual button.\n         *\n         * @sample highcharts/exporting/buttons-text/\n         *         Full text button\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         * @sample highcharts/exporting/buttons-text-symbol/\n         *         Combined symbol and text\n         *\n         * @type      {string}\n         * @default   null\n         * @since     3.0\n         * @apioption navigation.buttonOptions.text\n         */\n        /**\n         * Whether to use HTML for rendering the button. HTML allows for things\n         * like inline CSS or image-based icons.\n         *\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         *\n         * @type      boolean\n         * @default   false\n         * @since 10.3.0\n         * @apioption navigation.buttonOptions.useHTML\n         */\n        /**\n         * The vertical offset of the button's position relative to its\n         * `verticalAlign`. By default adjusted for the chart title alignment.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @since     2.0\n         * @apioption navigation.buttonOptions.y\n         */\n        y: -5,\n        /**\n         * The vertical alignment of the buttons. Can be one of `\"top\"`,\n         * `\"middle\"` or `\"bottom\"`.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @type  {Highcharts.VerticalAlignValue}\n         * @since 2.0\n         */\n        verticalAlign: 'top',\n        /**\n         * The pixel width of the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        width: 28,\n        /**\n         * Fill color for the symbol within the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolfill/\n         *         Blue symbol stroke for one of the buttons\n         *\n         * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since 2.0\n         */\n        symbolFill: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The color of the symbol's stroke or line.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolstroke/\n         *         Blue symbol stroke\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 2.0\n         */\n        symbolStroke: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The pixel stroke width of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolStrokeWidth: 3,\n        /**\n         * A configuration object for the button theme. The object accepts\n         * SVG properties like `stroke-width`, `stroke` and `fill`.\n         * Tri-state button styles are supported by the `states.hover` and\n         * `states.select` objects.\n         *\n         * @sample highcharts/navigation/buttonoptions-theme/\n         *         Theming the buttons\n         *\n         * @requires modules/exporting\n         *\n         * @since 3.0\n         */\n        theme: {\n            /**\n             * The default fill exists only to capture hover events.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            /**\n             * Padding for the button.\n             */\n            padding: 5,\n            /**\n             * Default stroke for the buttons.\n             *\n             * @type      {Highcharts.ColorString}\n             */\n            stroke: 'none',\n            /**\n             * Default stroke linecap for the buttons.\n             */\n            'stroke-linecap': 'round'\n        }\n    },\n    /**\n     * CSS styles for the popup menu appearing by default when the export\n     * icon is clicked. This menu is rendered in HTML.\n     *\n     * @see In styled mode, the menu is styled with the `.highcharts-menu`\n     *      class.\n     *\n     * @sample highcharts/navigation/menustyle/\n     *         Light gray menu background\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#ffffff\", \"borderRadius\": \"3px\", \"padding\": \"0.5em\"}\n     * @since   2.0\n     */\n    menuStyle: {\n        /** @ignore-option */\n        border: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        background: \"#ffffff\" /* Palette.backgroundColor */,\n        /** @ignore-option */\n        padding: '0.5em'\n    },\n    /**\n     * CSS styles for the individual items within the popup menu appearing\n     * by default when the export icon is clicked. The menu items are\n     * rendered in HTML. Font size defaults to `11px` on desktop and `14px`\n     * on touch devices.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample {highcharts} highcharts/navigation/menuitemstyle/\n     *         Add a grey stripe to the left\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"padding\": \"0.5em\", \"color\": \"#333333\", \"background\": \"none\", \"borderRadius\": \"3px\", \"fontSize\": \"0.8em\", \"transition\": \"background 250ms, color 250ms\"}\n     * @since   2.0\n     */\n    menuItemStyle: {\n        /** @ignore-option */\n        background: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        color: \"#333333\" /* Palette.neutralColor80 */,\n        /** @ignore-option */\n        padding: '0.5em',\n        /** @ignore-option */\n        fontSize: isTouchDevice ? '0.9em' : '0.8em',\n        /** @ignore-option */\n        transition: 'background 250ms, color 250ms'\n    },\n    /**\n     * CSS styles for the hover state of the individual items within the\n     * popup menu appearing by default when the export icon is clicked. The\n     * menu items are rendered in HTML.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample highcharts/navigation/menuitemhoverstyle/\n     *         Bold text on hover\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#f2f2f2\" }\n     * @since   2.0\n     */\n    menuItemHoverStyle: {\n        /** @ignore-option */\n        background: \"#f2f2f2\" /* Palette.neutralColor5 */\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar ExportingDefaults = {\n    exporting: exporting,\n    lang: lang,\n    navigation: navigation\n};\n/* harmony default export */ var Exporting_ExportingDefaults = (ExportingDefaults);\n\n;// ./code/es5/es-modules/Extensions/Exporting/ExportingSymbols.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ExportingSymbols;\n(function (ExportingSymbols) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    var modifiedClasses = [];\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SVGRendererClass) {\n        if (modifiedClasses.indexOf(SVGRendererClass) === -1) {\n            modifiedClasses.push(SVGRendererClass);\n            var symbols = SVGRendererClass.prototype.symbols;\n            symbols.menu = menu;\n            symbols.menuball = menuball.bind(symbols);\n        }\n    }\n    ExportingSymbols.compose = compose;\n    /**\n     * @private\n     */\n    function menu(x, y, width, height) {\n        var arr = [\n                ['M',\n            x,\n            y + 2.5],\n                ['L',\n            x + width,\n            y + 2.5],\n                ['M',\n            x,\n            y + height / 2 + 0.5],\n                ['L',\n            x + width,\n            y + height / 2 + 0.5],\n                ['M',\n            x,\n            y + height - 1.5],\n                ['L',\n            x + width,\n            y + height - 1.5]\n            ];\n        return arr;\n    }\n    /**\n     * @private\n     */\n    function menuball(x, y, width, height) {\n        var h = (height / 3) - 2;\n        var path = [];\n        path = path.concat(this.circle(width - h, y, h, h), this.circle(width - h, y + h + 4, h, h), this.circle(width - h, y + 2 * (h + 4), h, h));\n        return path;\n    }\n})(ExportingSymbols || (ExportingSymbols = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Exporting_ExportingSymbols = (ExportingSymbols);\n\n;// ./code/es5/es-modules/Extensions/Exporting/Fullscreen.js\n/* *\n *\n *  (c) 2009-2025 Rafal Sebestjanski\n *\n *  Full screen for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/**\n * The module allows user to enable display chart in full screen mode.\n * Used in StockTools too.\n * Based on default solutions in browsers.\n */\n\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onChartBeforeRender() {\n    /**\n     * @name Highcharts.Chart#fullscreen\n     * @type {Highcharts.Fullscreen}\n     * @requires modules/full-screen\n     */\n    this.fullscreen = new Fullscreen(this);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles displaying chart's container in the fullscreen mode.\n *\n * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n *\n * @class\n * @name Highcharts.Fullscreen\n *\n * @requires modules/exporting\n */\nvar Fullscreen = /** @class */ (function () {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function Fullscreen(chart) {\n        /**\n         * Chart managed by the fullscreen controller.\n         * @name Highcharts.Fullscreen#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The flag is set to `true` when the chart is displayed in\n         * the fullscreen mode.\n         *\n         * @name Highcharts.Fullscreen#isOpen\n         * @type {boolean|undefined}\n         * @since 8.0.1\n         */\n        this.isOpen = false;\n        var container = chart.renderTo;\n        // Hold event and methods available only for a current browser.\n        if (!this.browserProps) {\n            if (typeof container.requestFullscreen === 'function') {\n                this.browserProps = {\n                    fullscreenChange: 'fullscreenchange',\n                    requestFullscreen: 'requestFullscreen',\n                    exitFullscreen: 'exitFullscreen'\n                };\n            }\n            else if (container.mozRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'mozfullscreenchange',\n                    requestFullscreen: 'mozRequestFullScreen',\n                    exitFullscreen: 'mozCancelFullScreen'\n                };\n            }\n            else if (container.webkitRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'webkitfullscreenchange',\n                    requestFullscreen: 'webkitRequestFullScreen',\n                    exitFullscreen: 'webkitExitFullscreen'\n                };\n            }\n            else if (container.msRequestFullscreen) {\n                this.browserProps = {\n                    fullscreenChange: 'MSFullscreenChange',\n                    requestFullscreen: 'msRequestFullscreen',\n                    exitFullscreen: 'msExitFullscreen'\n                };\n            }\n        }\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Prepares the chart class to support fullscreen.\n     *\n     * @param {typeof_Highcharts.Chart} ChartClass\n     * The chart class to decorate with fullscreen support.\n     */\n    Fullscreen.compose = function (ChartClass) {\n        if (pushUnique(composed, 'Fullscreen')) {\n            // Initialize fullscreen\n            addEvent(ChartClass, 'beforeRender', onChartBeforeRender);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Stops displaying the chart in fullscreen mode.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function    Highcharts.Fullscreen#close\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    Fullscreen.prototype.close = function () {\n        var fullscreen = this,\n            chart = fullscreen.chart,\n            optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenClose', null, function () {\n            // Don't fire exitFullscreen() when user exited\n            // using 'Escape' button.\n            if (fullscreen.isOpen &&\n                fullscreen.browserProps &&\n                chart.container.ownerDocument instanceof Document) {\n                chart.container.ownerDocument[fullscreen.browserProps.exitFullscreen]();\n            }\n            // Unbind event as it's necessary only before exiting\n            // from fullscreen.\n            if (fullscreen.unbindFullscreenEvent) {\n                fullscreen.unbindFullscreenEvent = fullscreen\n                    .unbindFullscreenEvent();\n            }\n            chart.setSize(fullscreen.origWidth, fullscreen.origHeight, false);\n            fullscreen.origWidth = void 0;\n            fullscreen.origHeight = void 0;\n            optionsChart.width = fullscreen.origWidthOption;\n            optionsChart.height = fullscreen.origHeightOption;\n            fullscreen.origWidthOption = void 0;\n            fullscreen.origHeightOption = void 0;\n            fullscreen.isOpen = false;\n            fullscreen.setButtonText();\n        });\n    };\n    /**\n     * Displays the chart in fullscreen mode.\n     * When fired customly by user before exporting context button is created,\n     * button's text will not be replaced - it's on the user side.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function Highcharts.Fullscreen#open\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    Fullscreen.prototype.open = function () {\n        var fullscreen = this,\n            chart = fullscreen.chart,\n            optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenOpen', null, function () {\n            if (optionsChart) {\n                fullscreen.origWidthOption = optionsChart.width;\n                fullscreen.origHeightOption = optionsChart.height;\n            }\n            fullscreen.origWidth = chart.chartWidth;\n            fullscreen.origHeight = chart.chartHeight;\n            // Handle exitFullscreen() method when user clicks 'Escape' button.\n            if (fullscreen.browserProps) {\n                var unbindChange_1 = addEvent(chart.container.ownerDocument, // Chart's document\n                    fullscreen.browserProps.fullscreenChange,\n                    function () {\n                        // Handle lack of async of browser's\n                        // fullScreenChange event.\n                        if (fullscreen.isOpen) {\n                            fullscreen.isOpen = false;\n                        fullscreen.close();\n                    }\n                    else {\n                        chart.setSize(null, null, false);\n                        fullscreen.isOpen = true;\n                        fullscreen.setButtonText();\n                    }\n                });\n                var unbindDestroy_1 = addEvent(chart, 'destroy',\n                    unbindChange_1);\n                fullscreen.unbindFullscreenEvent = function () {\n                    unbindChange_1();\n                    unbindDestroy_1();\n                };\n                var promise = chart.renderTo[fullscreen.browserProps.requestFullscreen]();\n                if (promise) {\n                    promise['catch'](function () {\n                        alert(// eslint-disable-line no-alert\n                        'Full screen is not supported inside a frame.');\n                    });\n                }\n            }\n        });\n    };\n    /**\n     * Replaces the exporting context button's text when toogling the\n     * fullscreen mode.\n     *\n     * @private\n     *\n     * @since 8.0.1\n     *\n     * @requires modules/full-screen\n     */\n    Fullscreen.prototype.setButtonText = function () {\n        var chart = this.chart,\n            exportDivElements = chart.exportDivElements,\n            exportingOptions = chart.options.exporting,\n            menuItems = (exportingOptions &&\n                exportingOptions.buttons &&\n                exportingOptions.buttons.contextButton.menuItems),\n            lang = chart.options.lang;\n        if (exportingOptions &&\n            exportingOptions.menuItemDefinitions &&\n            lang &&\n            lang.exitFullscreen &&\n            lang.viewFullscreen &&\n            menuItems &&\n            exportDivElements) {\n            var exportDivElement = exportDivElements[menuItems.indexOf('viewFullscreen')];\n            if (exportDivElement) {\n                highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(exportDivElement, !this.isOpen ?\n                    (exportingOptions.menuItemDefinitions.viewFullscreen\n                        .text ||\n                        lang.viewFullscreen) : lang.exitFullscreen);\n            }\n        }\n    };\n    /**\n     * Toggles displaying the chart in fullscreen mode.\n     * By default, when the exporting module is enabled, a context button with\n     * a drop down menu in the upper right corner accesses this function.\n     * Exporting module required.\n     *\n     * @since 8.0.1\n     *\n     * @sample      highcharts/members/chart-togglefullscreen/\n     *              Toggle fullscreen mode from a HTML button\n     *\n     * @function Highcharts.Fullscreen#toggle\n     * @requires    modules/full-screen\n     */\n    Fullscreen.prototype.toggle = function () {\n        var fullscreen = this;\n        if (!fullscreen.isOpen) {\n            fullscreen.open();\n        }\n        else {\n            fullscreen.close();\n        }\n    };\n    return Fullscreen;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Exporting_Fullscreen = (Fullscreen);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired when closing the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenCloseCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired when opening the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenOpenCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n(''); // Keeps doclets above separated from following code\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires when a fullscreen is closed through the context menu item,\n * or a fullscreen is closed on the `Escape` button click,\n * or the `Chart.fullscreen.close` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenCloseCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenClose\n */\n/**\n * Fires when a fullscreen is opened through the context menu item,\n * or the `Chart.fullscreen.open` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenOpenCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenOpen\n */\n(''); // Keeps doclets above in transpiled file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"HttpUtilities\"],\"commonjs\":[\"highcharts\",\"HttpUtilities\"],\"commonjs2\":[\"highcharts\",\"HttpUtilities\"],\"root\":[\"Highcharts\",\"HttpUtilities\"]}\nvar highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_ = __webpack_require__(156);\nvar highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_default = /*#__PURE__*/__webpack_require__.n(highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_);\n;// ./code/es5/es-modules/Extensions/Exporting/Exporting.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\n\n\n\nvar defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions;\n\n\n\n\nvar Exporting_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, SVG_NS = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).SVG_NS, Exporting_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\nvar Exporting_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, css = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).css, createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement, discardElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).discardElement, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, Exporting_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, removeEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).removeEvent, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Composition\n *\n * */\nvar Exporting;\n(function (Exporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // These CSS properties are not inlined. Remember camelCase.\n    var inlineDenylist = [\n            /-/, // In Firefox, both hyphened and camelCased names are listed\n            /^(clipPath|cssText|d|height|width)$/, // Full words\n            /^font$/, // More specific props are set\n            /[lL]ogical(Width|Height)$/,\n            /^parentRule$/,\n            /^(cssRules|ownerRules)$/, // #19516 read-only properties\n            /perspective/,\n            /TapHighlightColor/,\n            /^transition/,\n            /^length$/, // #7700\n            /^\\d+$/ // #17538\n        ];\n    // These ones are translated to attributes rather than styles\n    var inlineToAttributes = [\n            'fill',\n            'stroke',\n            'strokeLinecap',\n            'strokeLinejoin',\n            'strokeWidth',\n            'textAnchor',\n            'x',\n            'y'\n        ];\n    Exporting.inlineAllowlist = [];\n    var unstyledElements = [\n            'clipPath',\n            'defs',\n            'desc'\n        ];\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    var printingChart;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add the export button to the chart, with options.\n     *\n     * @private\n     * @function Highcharts.Chart#addButton\n     * @param {Highcharts.NavigationButtonOptions} options\n     * @requires modules/exporting\n     */\n    function addButton(options) {\n        var chart = this,\n            renderer = chart.renderer,\n            btnOptions = merge(chart.options.navigation.buttonOptions,\n            options),\n            onclick = btnOptions.onclick,\n            menuItems = btnOptions.menuItems,\n            symbolSize = btnOptions.symbolSize || 12;\n        var symbol;\n        if (!chart.btnCount) {\n            chart.btnCount = 0;\n        }\n        // Keeps references to the button elements\n        if (!chart.exportDivElements) {\n            chart.exportDivElements = [];\n            chart.exportSVGElements = [];\n        }\n        if (btnOptions.enabled === false || !btnOptions.theme) {\n            return;\n        }\n        var theme = chart.styledMode ? {} : btnOptions.theme;\n        var callback;\n        if (onclick) {\n            callback = function (e) {\n                if (e) {\n                    e.stopPropagation();\n                }\n                onclick.call(chart, e);\n            };\n        }\n        else if (menuItems) {\n            callback = function (e) {\n                // Consistent with onclick call (#3495)\n                if (e) {\n                    e.stopPropagation();\n                }\n                chart.contextMenu(button.menuClassName, menuItems, button.translateX || 0, button.translateY || 0, button.width || 0, button.height || 0, button);\n                button.setState(2);\n            };\n        }\n        if (btnOptions.text && btnOptions.symbol) {\n            theme.paddingLeft = pick(theme.paddingLeft, 30);\n        }\n        else if (!btnOptions.text) {\n            extend(theme, {\n                width: btnOptions.width,\n                height: btnOptions.height,\n                padding: 0\n            });\n        }\n        var button = renderer\n                .button(btnOptions.text, 0, 0,\n            callback,\n            theme,\n            void 0,\n            void 0,\n            void 0,\n            void 0,\n            btnOptions.useHTML)\n                .addClass(options.className)\n                .attr({\n                title: pick(chart.options.lang[btnOptions._titleKey || btnOptions.titleKey], '')\n            });\n        button.menuClassName = (options.menuClassName ||\n            'highcharts-menu-' + chart.btnCount++);\n        if (btnOptions.symbol) {\n            symbol = renderer\n                .symbol(btnOptions.symbol, Math.round((btnOptions.symbolX || 0) - (symbolSize / 2)), Math.round((btnOptions.symbolY || 0) - (symbolSize / 2)), symbolSize, symbolSize\n            // If symbol is an image, scale it (#7957)\n            , {\n                width: symbolSize,\n                height: symbolSize\n            })\n                .addClass('highcharts-button-symbol')\n                .attr({\n                zIndex: 1\n            })\n                .add(button);\n            if (!chart.styledMode) {\n                symbol.attr({\n                    stroke: btnOptions.symbolStroke,\n                    fill: btnOptions.symbolFill,\n                    'stroke-width': btnOptions.symbolStrokeWidth || 1\n                });\n            }\n        }\n        button\n            .add(chart.exportingGroup)\n            .align(extend(btnOptions, {\n            width: button.width,\n            x: pick(btnOptions.x, chart.buttonOffset) // #1654\n        }), true, 'spacingBox');\n        chart.buttonOffset += (((button.width || 0) + btnOptions.buttonSpacing) *\n            (btnOptions.align === 'right' ? -1 : 1));\n        chart.exportSVGElements.push(button, symbol);\n    }\n    /**\n     * Clean up after printing a chart.\n     *\n     * @function Highcharts#afterPrint\n     *\n     * @private\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that was (or suppose to be) printed\n     *\n     * @emits Highcharts.Chart#event:afterPrint\n     */\n    function afterPrint() {\n        var chart = this;\n        if (!chart.printReverseInfo) {\n            return void 0;\n        }\n        var _a = chart.printReverseInfo,\n            childNodes = _a.childNodes,\n            origDisplay = _a.origDisplay,\n            resetParams = _a.resetParams;\n        // Put the chart back in\n        chart.moveContainers(chart.renderTo);\n        // Restore all body content\n        [].forEach.call(childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                node.style.display = (origDisplay[i] || '');\n            }\n        });\n        chart.isPrinting = false;\n        // Reset printMaxWidth\n        if (resetParams) {\n            chart.setSize.apply(chart, resetParams);\n        }\n        delete chart.printReverseInfo;\n        printingChart = void 0;\n        Exporting_fireEvent(chart, 'afterPrint');\n    }\n    /**\n     * Prepare chart and document before printing a chart.\n     *\n     * @function Highcharts#beforePrint\n     *\n     * @private\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     */\n    function beforePrint() {\n        var _a;\n        var chart = this,\n            body = Exporting_doc.body,\n            printMaxWidth = chart.options.exporting.printMaxWidth,\n            printReverseInfo = {\n                childNodes: body.childNodes,\n                origDisplay: [],\n                resetParams: void 0\n            };\n        chart.isPrinting = true;\n        (_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.reset(void 0, 0);\n        Exporting_fireEvent(chart, 'beforePrint');\n        // Handle printMaxWidth\n        var handleMaxWidth = printMaxWidth &&\n                chart.chartWidth > printMaxWidth;\n        if (handleMaxWidth) {\n            printReverseInfo.resetParams = [\n                chart.options.chart.width,\n                void 0,\n                false\n            ];\n            chart.setSize(printMaxWidth, void 0, false);\n        }\n        // Hide all body content\n        [].forEach.call(printReverseInfo.childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                printReverseInfo.origDisplay[i] = node.style.display;\n                node.style.display = 'none';\n            }\n        });\n        // Pull out the chart\n        chart.moveContainers(body);\n        // Storage details for undo action after printing\n        chart.printReverseInfo = printReverseInfo;\n    }\n    /**\n     * @private\n     */\n    function chartCallback(chart) {\n        var composition = chart;\n        composition.renderExporting();\n        Exporting_addEvent(chart, 'redraw', composition.renderExporting);\n        // Destroy the export elements at chart destroy\n        Exporting_addEvent(chart, 'destroy', composition.destroyExport);\n        // Uncomment this to see a button directly below the chart, for quick\n        // testing of export\n        /*\n        let button, viewImage, viewSource;\n        if (!chart.renderer.forExport) {\n            viewImage = function () {\n                let div = doc.createElement('div');\n                div.innerHTML = chart.getSVGForExport();\n                chart.renderTo.parentNode.appendChild(div);\n            };\n\n            viewSource = function () {\n                let pre = doc.createElement('pre');\n                pre.innerHTML = chart.getSVGForExport()\n                    .replace(/</g, '\\n&lt;')\n                    .replace(/>/g, '&gt;');\n                chart.renderTo.parentNode.appendChild(pre);\n            };\n\n            viewImage();\n\n            // View SVG Image\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Image';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewImage;\n\n            // View SVG Source\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Source';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewSource;\n        }\n        //*/\n    }\n    /**\n     * @private\n     */\n    function compose(ChartClass, SVGRendererClass) {\n        Exporting_ExportingSymbols.compose(SVGRendererClass);\n        Exporting_Fullscreen.compose(ChartClass);\n        var chartProto = ChartClass.prototype;\n        if (!chartProto.exportChart) {\n            chartProto.afterPrint = afterPrint;\n            chartProto.exportChart = exportChart;\n            chartProto.inlineStyles = inlineStyles;\n            chartProto.print = print;\n            chartProto.sanitizeSVG = sanitizeSVG;\n            chartProto.getChartHTML = getChartHTML;\n            chartProto.getSVG = getSVG;\n            chartProto.getSVGForExport = getSVGForExport;\n            chartProto.getFilename = getFilename;\n            chartProto.moveContainers = moveContainers;\n            chartProto.beforePrint = beforePrint;\n            chartProto.contextMenu = contextMenu;\n            chartProto.addButton = addButton;\n            chartProto.destroyExport = destroyExport;\n            chartProto.renderExporting = renderExporting;\n            chartProto.resolveCSSVariables = resolveCSSVariables;\n            chartProto.callbacks.push(chartCallback);\n            Exporting_addEvent(ChartClass, 'init', onChartInit);\n            Exporting_addEvent(ChartClass, 'layOutTitle', onChartLayOutTitle);\n            if ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n                Exporting_win.matchMedia('print').addListener(function (mqlEvent) {\n                    if (!printingChart) {\n                        return void 0;\n                    }\n                    if (mqlEvent.matches) {\n                        printingChart.beforePrint();\n                    }\n                    else {\n                        printingChart.afterPrint();\n                    }\n                });\n            }\n            defaultOptions.exporting = merge(Exporting_ExportingDefaults.exporting, defaultOptions.exporting);\n            defaultOptions.lang = merge(Exporting_ExportingDefaults.lang, defaultOptions.lang);\n            // Buttons and menus are collected in a separate config option set\n            // called 'navigation'. This can be extended later to add control\n            // buttons like zoom and pan right click menus.\n            defaultOptions.navigation = merge(Exporting_ExportingDefaults.navigation, defaultOptions.navigation);\n        }\n    }\n    Exporting.compose = compose;\n    /**\n     * Display a popup menu for choosing the export type.\n     *\n     * @private\n     * @function Highcharts.Chart#contextMenu\n     * @param {string} className\n     *        An identifier for the menu.\n     * @param {Array<string|Highcharts.ExportingMenuObject>} items\n     *        A collection with text and onclicks for the items.\n     * @param {number} x\n     *        The x position of the opener button\n     * @param {number} y\n     *        The y position of the opener button\n     * @param {number} width\n     *        The width of the opener button\n     * @param {number} height\n     *        The height of the opener button\n     * @requires modules/exporting\n     */\n    function contextMenu(className, items, x, y, width, height, button) {\n        var _a,\n            _b;\n        var chart = this,\n            navOptions = chart.options.navigation,\n            chartWidth = chart.chartWidth,\n            chartHeight = chart.chartHeight,\n            cacheName = 'cache-' + className, \n            // For mouse leave detection\n            menuPadding = Math.max(width,\n            height);\n        var innerMenu,\n            menu = chart[cacheName];\n        // Create the menu only the first time\n        if (!menu) {\n            // Create a HTML element above the SVG\n            chart.exportContextMenu = chart[cacheName] = menu =\n                createElement('div', {\n                    className: className\n                }, __assign({ position: 'absolute', zIndex: 1000, padding: menuPadding + 'px', pointerEvents: 'auto' }, chart.renderer.style), ((_a = chart.scrollablePlotArea) === null || _a === void 0 ? void 0 : _a.fixedDiv) || chart.container);\n            innerMenu = createElement('ul', { className: 'highcharts-menu' }, chart.styledMode ? {} : {\n                listStyle: 'none',\n                margin: 0,\n                padding: 0\n            }, menu);\n            // Presentational CSS\n            if (!chart.styledMode) {\n                css(innerMenu, extend({\n                    MozBoxShadow: '3px 3px 10px #888',\n                    WebkitBoxShadow: '3px 3px 10px #888',\n                    boxShadow: '3px 3px 10px #888'\n                }, navOptions.menuStyle));\n            }\n            // Hide on mouse out\n            menu.hideMenu = function () {\n                css(menu, { display: 'none' });\n                if (button) {\n                    button.setState(0);\n                }\n                chart.openMenu = false;\n                // #10361, #9998\n                css(chart.renderTo, { overflow: 'hidden' });\n                css(chart.container, { overflow: 'hidden' });\n                highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(menu.hideTimer);\n                Exporting_fireEvent(chart, 'exportMenuHidden');\n            };\n            // Hide the menu some time after mouse leave (#1357)\n            chart.exportEvents.push(Exporting_addEvent(menu, 'mouseleave', function () {\n                menu.hideTimer = Exporting_win.setTimeout(menu.hideMenu, 500);\n            }), Exporting_addEvent(menu, 'mouseenter', function () {\n                highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(menu.hideTimer);\n            }), \n            // Hide it on clicking or touching outside the menu (#2258,\n            // #2335, #2407)\n            Exporting_addEvent(Exporting_doc, 'mouseup', function (e) {\n                var _a;\n                if (!((_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.inClass(e.target, className))) {\n                    menu.hideMenu();\n                }\n            }), Exporting_addEvent(menu, 'click', function () {\n                if (chart.openMenu) {\n                    menu.hideMenu();\n                }\n            }));\n            // Create the items\n            items.forEach(function (item) {\n                if (typeof item === 'string') {\n                    item = chart.options.exporting\n                        .menuItemDefinitions[item];\n                }\n                if (isObject(item, true)) {\n                    var element = void 0;\n                    if (item.separator) {\n                        element = createElement('hr', void 0, void 0, innerMenu);\n                    }\n                    else {\n                        // When chart initialized with the table, wrong button\n                        // text displayed, #14352.\n                        if (item.textKey === 'viewData' &&\n                            chart.isDataTableVisible) {\n                            item.textKey = 'hideData';\n                        }\n                        element = createElement('li', {\n                            className: 'highcharts-menu-item',\n                            onclick: function (e) {\n                                if (e) { // IE7\n                                    e.stopPropagation();\n                                }\n                                menu.hideMenu();\n                                if (typeof item !== 'string' && item.onclick) {\n                                    item.onclick.apply(chart, arguments);\n                                }\n                            }\n                        }, void 0, innerMenu);\n                        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(element, item.text ||\n                            chart.options.lang[item.textKey]);\n                        if (!chart.styledMode) {\n                            element.onmouseover = function () {\n                                css(this, navOptions.menuItemHoverStyle);\n                            };\n                            element.onmouseout = function () {\n                                css(this, navOptions.menuItemStyle);\n                            };\n                            css(element, extend({\n                                cursor: 'pointer'\n                            }, navOptions.menuItemStyle || {}));\n                        }\n                    }\n                    // Keep references to menu divs to be able to destroy them\n                    chart.exportDivElements.push(element);\n                }\n            });\n            // Keep references to menu and innerMenu div to be able to destroy\n            // them\n            chart.exportDivElements.push(innerMenu, menu);\n            chart.exportMenuWidth = menu.offsetWidth;\n            chart.exportMenuHeight = menu.offsetHeight;\n        }\n        var menuStyle = { display: 'block' };\n        // If outside right, right align it\n        if (x + (chart.exportMenuWidth || 0) > chartWidth) {\n            menuStyle.right = (chartWidth - x - width - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.left = (x - menuPadding) + 'px';\n        }\n        // If outside bottom, bottom align it\n        if (y + height + (chart.exportMenuHeight || 0) > chartHeight &&\n            ((_b = button.alignOptions) === null || _b === void 0 ? void 0 : _b.verticalAlign) !== 'top') {\n            menuStyle.bottom = (chartHeight - y - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.top = (y + height - menuPadding) + 'px';\n        }\n        css(menu, menuStyle);\n        // #10361, #9998\n        css(chart.renderTo, { overflow: '' });\n        css(chart.container, { overflow: '' });\n        chart.openMenu = true;\n        Exporting_fireEvent(chart, 'exportMenuShown');\n    }\n    /**\n     * Destroy the export buttons.\n     * @private\n     * @function Highcharts.Chart#destroyExport\n     * @param {global.Event} [e]\n     * @requires modules/exporting\n     */\n    function destroyExport(e) {\n        var chart = e ? e.target : this,\n            exportSVGElements = chart.exportSVGElements,\n            exportDivElements = chart.exportDivElements,\n            exportEvents = chart.exportEvents;\n        var cacheName;\n        // Destroy the extra buttons added\n        if (exportSVGElements) {\n            exportSVGElements.forEach(function (elem, i) {\n                // Destroy and null the svg elements\n                if (elem) { // #1822\n                    elem.onclick = elem.ontouchstart = null;\n                    cacheName = 'cache-' + elem.menuClassName;\n                    if (chart[cacheName]) {\n                        delete chart[cacheName];\n                    }\n                    exportSVGElements[i] = elem.destroy();\n                }\n            });\n            exportSVGElements.length = 0;\n        }\n        // Destroy the exporting group\n        if (chart.exportingGroup) {\n            chart.exportingGroup.destroy();\n            delete chart.exportingGroup;\n        }\n        // Destroy the divs for the menu\n        if (exportDivElements) {\n            exportDivElements.forEach(function (elem, i) {\n                if (elem) {\n                    // Remove the event handler\n                    highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(elem.hideTimer); // #5427\n                    removeEvent(elem, 'mouseleave');\n                    // Remove inline events\n                    // (chart.exportDivElements as any)[i] =\n                    exportDivElements[i] =\n                        elem.onmouseout =\n                            elem.onmouseover =\n                                elem.ontouchstart =\n                                    elem.onclick = null;\n                    // Destroy the div by moving to garbage bin\n                    discardElement(elem);\n                }\n            });\n            exportDivElements.length = 0;\n        }\n        if (exportEvents) {\n            exportEvents.forEach(function (unbind) {\n                unbind();\n            });\n            exportEvents.length = 0;\n        }\n    }\n    /**\n     * Exporting module required. Submit an SVG version of the chart to a server\n     * along with some parameters for conversion.\n     *\n     * @sample highcharts/members/chart-exportchart/\n     *         Export with no options\n     * @sample highcharts/members/chart-exportchart-filename/\n     *         PDF type and custom filename\n     * @sample highcharts/members/chart-exportchart-custom-background/\n     *         Different chart background in export\n     * @sample stock/members/chart-exportchart/\n     *         Export with Highcharts Stock\n     *\n     * @function Highcharts.Chart#exportChart\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     *        Exporting options in addition to those defined in\n     *        [exporting](https://api.highcharts.com/highcharts/exporting).\n     *\n     * @param {Highcharts.Options} chartOptions\n     *        Additional chart options for the exported chart. For example a\n     *        different background color can be added here, or `dataLabels` for\n     *        export only.\n     *\n     * @requires modules/exporting\n     */\n    function exportChart(exportingOptions, chartOptions) {\n        var svg = this.getSVGForExport(exportingOptions,\n            chartOptions);\n        // Merge the options\n        exportingOptions = merge(this.options.exporting, exportingOptions);\n        // Do the post\n        highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_default().post(exportingOptions.url, {\n            filename: exportingOptions.filename ?\n                exportingOptions.filename.replace(/\\//g, '-') :\n                this.getFilename(),\n            type: exportingOptions.type,\n            width: exportingOptions.width,\n            scale: exportingOptions.scale,\n            svg: svg\n        }, exportingOptions.fetchOptions);\n    }\n    /**\n     * Return the unfiltered innerHTML of the chart container. Used as hook for\n     * plugins. In styled mode, it also takes care of inlining CSS style rules.\n     *\n     * @see Chart#getSVG\n     *\n     * @function Highcharts.Chart#getChartHTML\n     *\n     * @return {string}\n     * The unfiltered SVG of the chart.\n     *\n     * @requires modules/exporting\n     */\n    function getChartHTML(applyStyleSheets) {\n        if (applyStyleSheets) {\n            this.inlineStyles();\n        }\n        this.resolveCSSVariables();\n        return this.container.innerHTML;\n    }\n    /**\n     * Get the default file name used for exported charts. By default it creates\n     * a file name based on the chart title.\n     *\n     * @function Highcharts.Chart#getFilename\n     *\n     * @return {string} A file name without extension.\n     *\n     * @requires modules/exporting\n     */\n    function getFilename() {\n        var s = this.userOptions.title && this.userOptions.title.text;\n        var filename = this.options.exporting.filename;\n        if (filename) {\n            return filename.replace(/\\//g, '-');\n        }\n        if (typeof s === 'string') {\n            filename = s\n                .toLowerCase()\n                .replace(/<\\/?[^>]+(>|$)/g, '') // Strip HTML tags\n                .replace(/[\\s_]+/g, '-')\n                .replace(/[^a-z\\d\\-]/g, '') // Preserve only latin\n                .replace(/^[\\-]+/g, '') // Dashes in the start\n                .replace(/[\\-]+/g, '-') // Dashes in a row\n                .substr(0, 24)\n                .replace(/[\\-]+$/g, ''); // Dashes in the end;\n        }\n        if (!filename || filename.length < 5) {\n            filename = 'chart';\n        }\n        return filename;\n    }\n    /**\n     * Return an SVG representation of the chart.\n     *\n     * @sample highcharts/members/chart-getsvg/\n     *         View the SVG from a button\n     *\n     * @function Highcharts.Chart#getSVG\n     *\n     * @param {Highcharts.Options} [chartOptions]\n     *        Additional chart options for the generated SVG representation. For\n     *        collections like `xAxis`, `yAxis` or `series`, the additional\n     *        options is either merged in to the original item of the same\n     *        `id`, or to the first item if a common id is not found.\n     *\n     * @return {string}\n     *         The SVG representation of the rendered chart.\n     *\n     * @emits Highcharts.Chart#event:getSVG\n     *\n     * @requires modules/exporting\n     */\n    function getSVG(chartOptions) {\n        var _a;\n        var chart = this;\n        var svg,\n            seriesOptions, \n            // Copy the options and add extra options\n            options = merge(chart.options,\n            chartOptions);\n        // Use userOptions to make the options chain in series right (#3881)\n        options.plotOptions = merge(chart.userOptions.plotOptions, chartOptions && chartOptions.plotOptions);\n        // ... and likewise with time, avoid that undefined time properties are\n        // merged over legacy global time options\n        options.time = merge(chart.userOptions.time, chartOptions && chartOptions.time);\n        // Create a sandbox where a new chart will be generated\n        var sandbox = createElement('div',\n            null, {\n                position: 'absolute',\n                top: '-9999em',\n                width: chart.chartWidth + 'px',\n                height: chart.chartHeight + 'px'\n            },\n            Exporting_doc.body);\n        // Get the source size\n        var cssWidth = chart.renderTo.style.width, cssHeight = chart.renderTo.style.height, sourceWidth = options.exporting.sourceWidth ||\n                options.chart.width ||\n                (/px$/.test(cssWidth) && parseInt(cssWidth, 10)) ||\n                (options.isGantt ? 800 : 600), sourceHeight = options.exporting.sourceHeight ||\n                options.chart.height ||\n                (/px$/.test(cssHeight) && parseInt(cssHeight, 10)) ||\n                400;\n        // Override some options\n        extend(options.chart, {\n            animation: false,\n            renderTo: sandbox,\n            forExport: true,\n            renderer: 'SVGRenderer',\n            width: sourceWidth,\n            height: sourceHeight\n        });\n        options.exporting.enabled = false; // Hide buttons in print\n        delete options.data; // #3004\n        // prepare for replicating the chart\n        options.series = [];\n        chart.series.forEach(function (serie) {\n            seriesOptions = merge(serie.userOptions, {\n                animation: false, // Turn off animation\n                enableMouseTracking: false,\n                showCheckbox: false,\n                visible: serie.visible\n            });\n            // Used for the navigator series that has its own option set\n            if (!seriesOptions.isInternal) {\n                options.series.push(seriesOptions);\n            }\n        });\n        var colls = {};\n        chart.axes.forEach(function (axis) {\n            // Assign an internal key to ensure a one-to-one mapping (#5924)\n            if (!axis.userOptions.internalKey) { // #6444\n                axis.userOptions.internalKey = uniqueKey();\n            }\n            if (!axis.options.isInternal) {\n                if (!colls[axis.coll]) {\n                    colls[axis.coll] = true;\n                    options[axis.coll] = [];\n                }\n                options[axis.coll].push(merge(axis.userOptions, {\n                    visible: axis.visible,\n                    // Force some options that could have be set directly on\n                    // the axis while missing in the userOptions or options.\n                    type: axis.type,\n                    uniqueNames: axis.uniqueNames\n                }));\n            }\n        });\n        // Make sure the `colorAxis` object of the `defaultOptions` isn't used\n        // in the chart copy's user options, because a color axis should only be\n        // added when the user actually applies it.\n        options.colorAxis = chart.userOptions.colorAxis;\n        // Generate the chart copy\n        var chartCopy = new chart.constructor(options,\n            chart.callback);\n        // Axis options and series options  (#2022, #3900, #5982)\n        if (chartOptions) {\n            ['xAxis', 'yAxis', 'series'].forEach(function (coll) {\n                var collOptions = {};\n                if (chartOptions[coll]) {\n                    collOptions[coll] = chartOptions[coll];\n                    chartCopy.update(collOptions);\n                }\n            });\n        }\n        // Reflect axis extremes in the export (#5924)\n        chart.axes.forEach(function (axis) {\n            var axisCopy = find(chartCopy.axes,\n                function (copy) {\n                    return copy.options.internalKey === axis.userOptions.internalKey;\n            });\n            if (axisCopy) {\n                var extremes = axis.getExtremes(), \n                    // Make sure min and max overrides in the\n                    // `exporting.chartOptions.xAxis` settings are reflected.\n                    // These should override user-set extremes via zooming,\n                    // scrollbar etc (#7873).\n                    exportOverride = splat((chartOptions === null || chartOptions === void 0 ? void 0 : chartOptions[axis.coll]) || {})[0],\n                    userMin = 'min' in exportOverride ?\n                        exportOverride.min :\n                        extremes.userMin,\n                    userMax = 'max' in exportOverride ?\n                        exportOverride.max :\n                        extremes.userMax;\n                if (((typeof userMin !== 'undefined' &&\n                    userMin !== axisCopy.min) || (typeof userMax !== 'undefined' &&\n                    userMax !== axisCopy.max))) {\n                    axisCopy.setExtremes(userMin !== null && userMin !== void 0 ? userMin : void 0, userMax !== null && userMax !== void 0 ? userMax : void 0, true, false);\n                }\n            }\n        });\n        // Get the SVG from the container's innerHTML\n        svg = chartCopy.getChartHTML(chart.styledMode ||\n            ((_a = options.exporting) === null || _a === void 0 ? void 0 : _a.applyStyleSheets));\n        Exporting_fireEvent(this, 'getSVG', { chartCopy: chartCopy });\n        svg = chart.sanitizeSVG(svg, options);\n        // Free up memory\n        options = null;\n        chartCopy.destroy();\n        discardElement(sandbox);\n        return svg;\n    }\n    /**\n     * @private\n     * @function Highcharts.Chart#getSVGForExport\n     */\n    function getSVGForExport(options, chartOptions) {\n        var chartExportingOptions = this.options.exporting;\n        return this.getSVG(merge({ chart: { borderRadius: 0 } }, chartExportingOptions.chartOptions, chartOptions, {\n            exporting: {\n                sourceWidth: ((options && options.sourceWidth) ||\n                    chartExportingOptions.sourceWidth),\n                sourceHeight: ((options && options.sourceHeight) ||\n                    chartExportingOptions.sourceHeight)\n            }\n        }));\n    }\n    /**\n     * Make hyphenated property names out of camelCase\n     * @private\n     * @param {string} prop\n     * Property name in camelCase\n     * @return {string}\n     * Hyphenated property name\n     */\n    function hyphenate(prop) {\n        return prop.replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n        });\n    }\n    /**\n     * Analyze inherited styles from stylesheets and add them inline\n     *\n     * @private\n     * @function Highcharts.Chart#inlineStyles\n     *\n     * @todo What are the border styles for text about? In general, text has a\n     *       lot of properties.\n     *\n     * @todo Make it work with IE9 and IE10.\n     *\n     * @requires modules/exporting\n     */\n    function inlineStyles() {\n        var denylist = inlineDenylist,\n            allowlist = Exporting.inlineAllowlist, // For IE\n            defaultStyles = {};\n        var dummySVG;\n        // Create an iframe where we read default styles without pollution from\n        // this body\n        var iframe = Exporting_doc.createElement('iframe');\n        css(iframe, {\n            width: '1px',\n            height: '1px',\n            visibility: 'hidden'\n        });\n        Exporting_doc.body.appendChild(iframe);\n        var iframeDoc = (iframe.contentWindow && iframe.contentWindow.document);\n        if (iframeDoc) {\n            iframeDoc.body.appendChild(iframeDoc.createElementNS(SVG_NS, 'svg'));\n        }\n        /**\n         * Call this on all elements and recurse to children\n         * @private\n         * @param {Highcharts.HTMLDOMElement} node\n         *        Element child\n             */\n        function recurse(node) {\n            var filteredStyles = {};\n            var styles,\n                parentStyles,\n                dummy,\n                denylisted,\n                allowlisted,\n                i;\n            /**\n             * Check computed styles and whether they are in the allow/denylist\n             * for styles or attributes.\n             * @private\n             * @param {string} val\n             *        Style value\n             * @param {string} prop\n             *        Style property name\n                     */\n            function filterStyles(val, prop) {\n                // Check against allowlist & denylist\n                denylisted = allowlisted = false;\n                if (allowlist.length) {\n                    // Styled mode in IE has a allowlist instead. Exclude all\n                    // props not in this list.\n                    i = allowlist.length;\n                    while (i-- && !allowlisted) {\n                        allowlisted = allowlist[i].test(prop);\n                    }\n                    denylisted = !allowlisted;\n                }\n                // Explicitly remove empty transforms\n                if (prop === 'transform' && val === 'none') {\n                    denylisted = true;\n                }\n                i = denylist.length;\n                while (i-- && !denylisted) {\n                    if (prop.length > 1000 /* RegexLimits.shortLimit */) {\n                        throw new Error('Input too long');\n                    }\n                    denylisted = (denylist[i].test(prop) ||\n                        typeof val === 'function');\n                }\n                if (!denylisted) {\n                    // If parent node has the same style, it gets inherited, no\n                    // need to inline it. Top-level props should be diffed\n                    // against parent (#7687).\n                    if ((parentStyles[prop] !== val ||\n                        node.nodeName === 'svg') &&\n                        defaultStyles[node.nodeName][prop] !== val) {\n                        // Attributes\n                        if (!inlineToAttributes ||\n                            inlineToAttributes.indexOf(prop) !== -1) {\n                            if (val) {\n                                node.setAttribute(hyphenate(prop), val);\n                            }\n                            // Styles\n                        }\n                        else {\n                            filteredStyles[prop] = val;\n                        }\n                    }\n                }\n            }\n            if (iframeDoc &&\n                node.nodeType === 1 &&\n                unstyledElements.indexOf(node.nodeName) === -1) {\n                styles = Exporting_win.getComputedStyle(node, null);\n                parentStyles = node.nodeName === 'svg' ?\n                    {} :\n                    Exporting_win.getComputedStyle(node.parentNode, null);\n                // Get default styles from the browser so that we don't have to\n                // add these\n                if (!defaultStyles[node.nodeName]) {\n                    /*\n                    If (!dummySVG) {\n                        dummySVG = doc.createElementNS(H.SVG_NS, 'svg');\n                        dummySVG.setAttribute('version', '1.1');\n                        doc.body.appendChild(dummySVG);\n                    }\n                    */\n                    dummySVG = iframeDoc.getElementsByTagName('svg')[0];\n                    dummy = iframeDoc.createElementNS(node.namespaceURI, node.nodeName);\n                    dummySVG.appendChild(dummy);\n                    // Get the defaults into a standard object (simple merge\n                    // won't do)\n                    var s = Exporting_win.getComputedStyle(dummy,\n                        null),\n                        defaults = {};\n                    for (var key in s) {\n                        if (key.length < 1000 /* RegexLimits.shortLimit */ &&\n                            typeof s[key] === 'string' &&\n                            !/^\\d+$/.test(key)) {\n                            defaults[key] = s[key];\n                        }\n                    }\n                    defaultStyles[node.nodeName] = defaults;\n                    // Remove default fill, otherwise text disappears when\n                    // exported\n                    if (node.nodeName === 'text') {\n                        delete defaultStyles.text.fill;\n                    }\n                    dummySVG.removeChild(dummy);\n                }\n                // Loop through all styles and add them inline if they are ok\n                for (var p in styles) {\n                    if (\n                    // Some browsers put lots of styles on the prototype...\n                    (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFirefox ||\n                        (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS ||\n                        (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari || // #16902\n                        // ... Chrome puts them on the instance\n                        Object.hasOwnProperty.call(styles, p)) {\n                        filterStyles(styles[p], p);\n                    }\n                }\n                // Apply styles\n                css(node, filteredStyles);\n                // Set default stroke width (needed at least for IE)\n                if (node.nodeName === 'svg') {\n                    node.setAttribute('stroke-width', '1px');\n                }\n                if (node.nodeName === 'text') {\n                    return;\n                }\n                // Recurse\n                [].forEach.call(node.children || node.childNodes, recurse);\n            }\n        }\n        /**\n         * Remove the dummy objects used to get defaults\n         * @private\n         */\n        function tearDown() {\n            dummySVG.parentNode.removeChild(dummySVG);\n            // Remove trash from DOM that stayed after each exporting\n            iframe.parentNode.removeChild(iframe);\n        }\n        recurse(this.container.querySelector('svg'));\n        tearDown();\n    }\n    /**\n     * Resolve CSS variables into hex colors\n     */\n    function resolveCSSVariables() {\n        var svgElements = this.container.querySelectorAll('*'), colorAttributes = ['color', 'fill', 'stop-color', 'stroke'];\n        Array.from(svgElements).forEach(function (element) {\n            colorAttributes.forEach(function (attr) {\n                var attrValue = element.getAttribute(attr);\n                if (attrValue === null || attrValue === void 0 ? void 0 : attrValue.includes('var(')) {\n                    element.setAttribute(attr, getComputedStyle(element).getPropertyValue(attr));\n                }\n            });\n        });\n    }\n    /**\n     * Move the chart container(s) to another div.\n     *\n     * @function Highcharts#moveContainers\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} moveTo\n     *        Move target\n     */\n    function moveContainers(moveTo) {\n        var scrollablePlotArea = this.scrollablePlotArea;\n        (\n        // When scrollablePlotArea is active (#9533)\n        scrollablePlotArea ?\n            [\n                scrollablePlotArea.fixedDiv,\n                scrollablePlotArea.scrollingContainer\n            ] :\n            [this.container]).forEach(function (div) {\n            moveTo.appendChild(div);\n        });\n    }\n    /**\n     * Add update methods to handle chart.update and chart.exporting.update and\n     * chart.navigation.update. These must be added to the chart instance rather\n     * than the Chart prototype in order to use the chart instance inside the\n     * update function.\n     * @private\n     */\n    function onChartInit() {\n        var chart = this, \n            /**\n             * @private\n             * @param {\"exporting\"|\"navigation\"} prop\n             *        Property name in option root\n             * @param {Highcharts.ExportingOptions|Highcharts.NavigationOptions} options\n             *        Options to update\n             * @param {boolean} [redraw=true]\n             *        Whether to redraw\n                     */\n            update = function (prop,\n            options,\n            redraw) {\n                chart.isDirtyExporting = true;\n            merge(true, chart.options[prop], options);\n            if (pick(redraw, true)) {\n                chart.redraw();\n            }\n        };\n        chart.exporting = {\n            update: function (options, redraw) {\n                update('exporting', options, redraw);\n            }\n        };\n        // Register update() method for navigation. Cannot be set the same way\n        // as for exporting, because navigation options are shared with bindings\n        // which has separate update() logic.\n        Chart_ChartNavigationComposition\n            .compose(chart).navigation\n            .addUpdate(function (options, redraw) {\n            update('navigation', options, redraw);\n        });\n    }\n    /**\n     * On layout of titles (title, subtitle and caption), adjust the `alignTo``\n     * box to avoid the context menu button.\n     * @private\n     */\n    function onChartLayOutTitle(_a) {\n        var _b,\n            _c,\n            _d,\n            _e;\n        var alignTo = _a.alignTo,\n            key = _a.key,\n            textPxLength = _a.textPxLength;\n        var exportingOptions = this.options.exporting,\n            _f = merge((_b = this.options.navigation) === null || _b === void 0 ? void 0 : _b.buttonOptions, (_c = exportingOptions === null || exportingOptions === void 0 ? void 0 : exportingOptions.buttons) === null || _c === void 0 ? void 0 : _c.contextButton),\n            align = _f.align,\n            _g = _f.buttonSpacing,\n            buttonSpacing = _g === void 0 ? 0 : _g,\n            verticalAlign = _f.verticalAlign,\n            _h = _f.width,\n            width = _h === void 0 ? 0 : _h,\n            space = alignTo.width - textPxLength,\n            widthAdjust = width + buttonSpacing;\n        if (((_d = exportingOptions === null || exportingOptions === void 0 ? void 0 : exportingOptions.enabled) !== null && _d !== void 0 ? _d : true) &&\n            key === 'title' &&\n            align === 'right' &&\n            verticalAlign === 'top') {\n            if (space < 2 * widthAdjust) {\n                if (space < widthAdjust) {\n                    alignTo.width -= widthAdjust;\n                }\n                else if (((_e = this.title) === null || _e === void 0 ? void 0 : _e.alignValue) !== 'left') {\n                    alignTo.x -= widthAdjust - space / 2;\n                }\n            }\n        }\n    }\n    /**\n     * Exporting module required. Clears away other elements in the page and\n     * prints the chart as it is displayed. By default, when the exporting\n     * module is enabled, a context button with a drop down menu in the upper\n     * right corner accesses this function.\n     *\n     * @sample highcharts/members/chart-print/\n     *         Print from a HTML button\n     *\n     * @function Highcharts.Chart#print\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     * @emits Highcharts.Chart#event:afterPrint\n     *\n     * @requires modules/exporting\n     */\n    function print() {\n        var chart = this;\n        if (chart.isPrinting) { // Block the button while in printing mode\n            return;\n        }\n        printingChart = chart;\n        if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n            chart.beforePrint();\n        }\n        // Give the browser time to draw WebGL content, an issue that randomly\n        // appears (at least) in Chrome ~67 on the Mac (#8708).\n        setTimeout(function () {\n            Exporting_win.focus(); // #1510\n            Exporting_win.print();\n            // Allow the browser to prepare before reverting\n            if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n                setTimeout(function () {\n                    chart.afterPrint();\n                }, 1000);\n            }\n        }, 1);\n    }\n    /**\n     * Add the buttons on chart load\n     * @private\n     * @function Highcharts.Chart#renderExporting\n     * @requires modules/exporting\n     */\n    function renderExporting() {\n        var chart = this,\n            exportingOptions = chart.options.exporting,\n            buttons = exportingOptions.buttons,\n            isDirty = chart.isDirtyExporting || !chart.exportSVGElements;\n        chart.buttonOffset = 0;\n        if (chart.isDirtyExporting) {\n            chart.destroyExport();\n        }\n        if (isDirty && exportingOptions.enabled !== false) {\n            chart.exportEvents = [];\n            chart.exportingGroup = chart.exportingGroup ||\n                chart.renderer.g('exporting-group').attr({\n                    zIndex: 3 // #4955, // #8392\n                }).add();\n            objectEach(buttons, function (button) {\n                chart.addButton(button);\n            });\n            chart.isDirtyExporting = false;\n        }\n    }\n    /**\n     * Exporting module only. A collection of fixes on the produced SVG to\n     * account for expand properties, browser bugs.\n     * Returns a cleaned SVG.\n     *\n     * @private\n     * @function Highcharts.Chart#sanitizeSVG\n     * @param {string} svg\n     *        SVG code to sanitize\n     * @param {Highcharts.Options} options\n     *        Chart options to apply\n     * @return {string}\n     *         Sanitized SVG code\n     * @requires modules/exporting\n     */\n    function sanitizeSVG(svg, options) {\n        var _a;\n        var split = svg.indexOf('</svg>') + 6, useForeignObject = svg.indexOf('<foreignObject') > -1;\n        var html = svg.substr(split);\n        // Remove any HTML added to the container after the SVG (#894, #9087)\n        svg = svg.substr(0, split);\n        if (useForeignObject) {\n            // Some tags needs to be closed in xhtml (#13726)\n            svg = svg.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />');\n            // Move HTML into a foreignObject\n        }\n        else if (html && ((_a = options === null || options === void 0 ? void 0 : options.exporting) === null || _a === void 0 ? void 0 : _a.allowHTML)) {\n            html = '<foreignObject x=\"0\" y=\"0\" ' +\n                'width=\"' + options.chart.width + '\" ' +\n                'height=\"' + options.chart.height + '\">' +\n                '<body xmlns=\"http://www.w3.org/1999/xhtml\">' +\n                // Some tags needs to be closed in xhtml (#13726)\n                html.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />') +\n                '</body>' +\n                '</foreignObject>';\n            svg = svg.replace('</svg>', html + '</svg>');\n        }\n        svg = svg\n            .replace(/zIndex=\"[^\"]+\"/g, '')\n            .replace(/symbolName=\"[^\"]+\"/g, '')\n            .replace(/jQuery\\d+=\"[^\"]+\"/g, '')\n            .replace(/url\\((\"|&quot;)(.*?)(\"|&quot;)\\;?\\)/g, 'url($2)')\n            .replace(/url\\([^#]+#/g, 'url(#')\n            .replace(/<svg /, '<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" ')\n            .replace(/ (NS\\d+\\:)?href=/g, ' xlink:href=') // #3567\n            .replace(/\\n+/g, ' ')\n            // Replace HTML entities, issue #347\n            .replace(/&nbsp;/g, '\\u00A0') // No-break space\n            .replace(/&shy;/g, '\\u00AD'); // Soft hyphen\n        return svg;\n    }\n})(Exporting || (Exporting = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Exporting_Exporting = (Exporting);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired after a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingAfterPrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired before a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingBeforePrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Function to call if the offline-exporting module fails to export a chart on\n * the client side.\n *\n * @callback Highcharts.ExportingErrorCallbackFunction\n *\n * @param {Highcharts.ExportingOptions} options\n *        The exporting options.\n *\n * @param {global.Error} err\n *        The error from the module.\n */\n/**\n * Definition for a menu item in the context menu.\n *\n * @interface Highcharts.ExportingMenuObject\n */ /**\n* The text for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#text\n* @type {string|undefined}\n*/ /**\n* If internationalization is required, the key to a language string.\n*\n* @name Highcharts.ExportingMenuObject#textKey\n* @type {string|undefined}\n*/ /**\n* The click handler for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#onclick\n* @type {Highcharts.EventCallbackFunction<Highcharts.Chart>|undefined}\n*/ /**\n* Indicates a separator line instead of an item.\n*\n* @name Highcharts.ExportingMenuObject#separator\n* @type {boolean|undefined}\n*/\n/**\n * Possible MIME types for exporting.\n *\n * @typedef {\"image/png\"|\"image/jpeg\"|\"application/pdf\"|\"image/svg+xml\"} Highcharts.ExportingMimeTypeValue\n */\n(''); // Keeps doclets above in transpiled file\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after a chart is printed through the context menu item or the\n * `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingAfterPrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.afterPrint\n */\n/**\n * Fires before a chart is printed through the context menu item or\n * the `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingBeforePrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.beforePrint\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es5/es-modules/Extensions/OfflineExporting/OfflineExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n * Declarations\n *\n * */\nvar OfflineExportingDefaults = {\n    libURL: 'https://code.highcharts.com/12.2.0/lib/',\n    // When offline-exporting is loaded, redefine the menu item definitions\n    // related to download.\n    menuItemDefinitions: {\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: function () {\n                this.exportChartLocal();\n            }\n        },\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: function () {\n                this.exportChartLocal({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: function () {\n                this.exportChartLocal({\n                    type: 'image/svg+xml'\n                });\n            }\n        },\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: function () {\n                this.exportChartLocal({\n                    type: 'application/pdf'\n                });\n            }\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var OfflineExporting_OfflineExportingDefaults = (OfflineExportingDefaults);\n\n;// ./code/es5/es-modules/Extensions/OfflineExporting/OfflineExporting.js\n/* *\n *\n *  Client side exporting module\n *\n *  (c) 2015 Torstein Honsi / Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nvar OfflineExporting_defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions;\n\nvar OfflineExporting_downloadURL = Extensions_DownloadURL.downloadURL;\n\n\nvar OfflineExporting_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, OfflineExporting_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\nvar ajax = (highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_default()).ajax;\n\n\nvar OfflineExporting_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, OfflineExporting_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, OfflineExporting_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, OfflineExporting_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\nhighcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().allowedAttributes.push('data-z-index', 'fill-opacity', 'filter', 'rx', 'ry', 'stroke-dasharray', 'stroke-linejoin', 'stroke-opacity', 'text-anchor', 'transform', 'version', 'viewBox', 'visibility', 'xmlns', 'xmlns:xlink');\nhighcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().allowedTags.push('desc', 'clippath', 'g');\n/* *\n *\n *  Composition\n *\n * */\nvar OfflineExporting;\n(function (OfflineExporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // Dummy object so we can reuse our canvas-tools.js without errors\n    OfflineExporting.CanVGRenderer = {}, OfflineExporting.domurl = OfflineExporting_win.URL || OfflineExporting_win.webkitURL || OfflineExporting_win, \n    // Milliseconds to defer image load event handlers to offset IE bug\n    OfflineExporting.loadEventDeferDelay = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS ? 150 : 0;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Extends OfflineExporting with Chart.\n     * @private\n     */\n    function compose(ChartClass) {\n        var chartProto = ChartClass.prototype;\n        if (!chartProto.exportChartLocal) {\n            chartProto.getSVGForLocalExport = getSVGForLocalExport;\n            chartProto.exportChartLocal = exportChartLocal;\n            // Extend the default options to use the local exporter logic\n            OfflineExporting_merge(true, OfflineExporting_defaultOptions.exporting, OfflineExporting_OfflineExportingDefaults);\n        }\n        return ChartClass;\n    }\n    OfflineExporting.compose = compose;\n    /**\n     * Get data URL to an image of an SVG and call download on it options\n     * object:\n     * - **filename:** Name of resulting downloaded file without extension.\n     * Default is `chart`.\n     *\n     * - **type:** File type of resulting download. Default is `image/png`.\n     *\n     * - **scale:** Scaling factor of downloaded image compared to source.\n     * Default is `1`.\n     * - **libURL:** URL pointing to location of dependency scripts to download\n     * on demand. Default is the exporting.libURL option of the global\n     * Highcharts options pointing to our server.\n     *\n     * @function Highcharts.downloadSVGLocal\n     *\n     * @param {string} svg\n     * The generated SVG\n     *\n     * @param {Highcharts.ExportingOptions} options\n     * The exporting options\n     *\n     * @param {Function} failCallback\n     * The callback function in case of errors\n     *\n     * @param {Function} [successCallback]\n     * The callback function in case of success\n     *\n     */\n    function downloadSVGLocal(svg, options, failCallback, successCallback) {\n        var dummySVGContainer = OfflineExporting_doc.createElement('div'), imageType = options.type || 'image/png', filename = ((options.filename || 'chart') +\n                '.' +\n                (imageType === 'image/svg+xml' ?\n                    'svg' : imageType.split('/')[1])), scale = options.scale || 1;\n        var svgurl,\n            blob,\n            finallyHandler,\n            libURL = (options.libURL || OfflineExporting_defaultOptions.exporting.libURL),\n            objectURLRevoke = true,\n            pdfFont = options.pdfFont;\n        // Allow libURL to end with or without fordward slash\n        libURL = libURL.slice(-1) !== '/' ? libURL + '/' : libURL;\n        /*\n         * Detect if we need to load TTF fonts for the PDF, then load them and\n         * proceed.\n         *\n         * @private\n         */\n        var loadPdfFonts = function (svgElement, callback) {\n                var hasNonASCII = function (s) { return (\n                // eslint-disable-next-line no-control-regex\n                /[^\\u0000-\\u007F\\u200B]+/.test(s)); };\n            // Register an event in order to add the font once jsPDF is\n            // initialized\n            var addFont = function (variant,\n                base64) {\n                    OfflineExporting_win.jspdf.jsPDF.API.events.push([\n                        'initialized',\n                        function () {\n                            this.addFileToVFS(variant,\n                base64);\n                        this.addFont(variant, 'HighchartsFont', variant);\n                        if (!this.getFontList().HighchartsFont) {\n                            this.setFont('HighchartsFont');\n                        }\n                    }\n                ]);\n            };\n            // If there are no non-ASCII characters in the SVG, do not use\n            // bother downloading the font files\n            if (pdfFont && !hasNonASCII(svgElement.textContent || '')) {\n                pdfFont = void 0;\n            }\n            // Add new font if the URL is declared, #6417.\n            var variants = ['normal', 'italic', 'bold', 'bolditalic'];\n            // Shift the first element off the variants and add as a font.\n            // Then asynchronously trigger the next variant until calling the\n            // callback when the variants are empty.\n            var normalBase64;\n            var shiftAndLoadVariant = function () {\n                    var variant = variants.shift();\n                // All variants shifted and possibly loaded, proceed\n                if (!variant) {\n                    return callback();\n                }\n                var url = pdfFont && pdfFont[variant];\n                if (url) {\n                    ajax({\n                        url: url,\n                        responseType: 'blob',\n                        success: function (data, xhr) {\n                            var reader = new FileReader();\n                            reader.onloadend = function () {\n                                if (typeof this.result === 'string') {\n                                    var base64 = this.result.split(',')[1];\n                                    addFont(variant, base64);\n                                    if (variant === 'normal') {\n                                        normalBase64 = base64;\n                                    }\n                                }\n                                shiftAndLoadVariant();\n                            };\n                            reader.readAsDataURL(xhr.response);\n                        },\n                        error: shiftAndLoadVariant\n                    });\n                }\n                else {\n                    // For other variants, fall back to normal text weight/style\n                    if (normalBase64) {\n                        addFont(variant, normalBase64);\n                    }\n                    shiftAndLoadVariant();\n                }\n            };\n            shiftAndLoadVariant();\n        };\n        /*\n         * @private\n         */\n        var downloadPDF = function () {\n                highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(dummySVGContainer,\n            svg);\n            var textElements = dummySVGContainer.getElementsByTagName('text'), \n                // Copy style property to element from parents if it's not\n                // there. Searches up hierarchy until it finds prop, or hits the\n                // chart container.\n                setStylePropertyFromParents = function (el,\n                propName) {\n                    var curParent = el;\n                while (curParent && curParent !== dummySVGContainer) {\n                    if (curParent.style[propName]) {\n                        var value = curParent.style[propName];\n                        if (propName === 'fontSize' && /em$/.test(value)) {\n                            value = Math.round(parseFloat(value) * 16) + 'px';\n                        }\n                        el.style[propName] = value;\n                        break;\n                    }\n                    curParent = curParent.parentNode;\n                }\n            };\n            var titleElements,\n                outlineElements;\n            // Workaround for the text styling. Making sure it does pick up\n            // settings for parent elements.\n            [].forEach.call(textElements, function (el) {\n                // Workaround for the text styling. making sure it does pick up\n                // the root element\n                ['fontFamily', 'fontSize']\n                    .forEach(function (property) {\n                    setStylePropertyFromParents(el, property);\n                });\n                el.style.fontFamily = pdfFont && pdfFont.normal ?\n                    // Custom PDF font\n                    'HighchartsFont' :\n                    // Generic font (serif, sans-serif etc)\n                    String(el.style.fontFamily &&\n                        el.style.fontFamily.split(' ').splice(-1));\n                // Workaround for plotband with width, removing title from text\n                // nodes\n                titleElements = el.getElementsByTagName('title');\n                [].forEach.call(titleElements, function (titleElement) {\n                    el.removeChild(titleElement);\n                });\n                // Remove all .highcharts-text-outline elements, #17170\n                outlineElements =\n                    el.getElementsByClassName('highcharts-text-outline');\n                while (outlineElements.length > 0) {\n                    var outline = outlineElements[0];\n                    if (outline.parentNode) {\n                        outline.parentNode.removeChild(outline);\n                    }\n                }\n            });\n            var svgNode = dummySVGContainer.querySelector('svg');\n            if (svgNode) {\n                loadPdfFonts(svgNode, function () {\n                    svgToPdf(svgNode, 0, scale, function (pdfData) {\n                        try {\n                            OfflineExporting_downloadURL(pdfData, filename);\n                            if (successCallback) {\n                                successCallback();\n                            }\n                        }\n                        catch (e) {\n                            failCallback(e);\n                        }\n                    });\n                });\n            }\n        };\n        // Initiate download depending on file type\n        if (imageType === 'image/svg+xml') {\n            // SVG download. In this case, we want to use Microsoft specific\n            // Blob if available\n            try {\n                if (typeof OfflineExporting_win.MSBlobBuilder !== 'undefined') {\n                    blob = new OfflineExporting_win.MSBlobBuilder();\n                    blob.append(svg);\n                    svgurl = blob.getBlob('image/svg+xml');\n                }\n                else {\n                    svgurl = svgToDataUrl(svg);\n                }\n                OfflineExporting_downloadURL(svgurl, filename);\n                if (successCallback) {\n                    successCallback();\n                }\n            }\n            catch (e) {\n                failCallback(e);\n            }\n        }\n        else if (imageType === 'application/pdf') {\n            if (OfflineExporting_win.jspdf && OfflineExporting_win.jspdf.jsPDF) {\n                downloadPDF();\n            }\n            else {\n                // Must load pdf libraries first. // Don't destroy the object\n                // URL yet since we are doing things asynchronously. A cleaner\n                // solution would be nice, but this will do for now.\n                objectURLRevoke = true;\n                getScript(libURL + 'jspdf.js', function () {\n                    getScript(libURL + 'svg2pdf.js', downloadPDF);\n                });\n            }\n        }\n        else {\n            // PNG/JPEG download - create bitmap from SVG\n            svgurl = svgToDataUrl(svg);\n            finallyHandler = function () {\n                try {\n                    OfflineExporting.domurl.revokeObjectURL(svgurl);\n                }\n                catch (e) {\n                    // Ignore\n                }\n            };\n            // First, try to get PNG by rendering on canvas\n            imageToDataUrl(svgurl, imageType, {}, scale, function (imageURL) {\n                // Success\n                try {\n                    OfflineExporting_downloadURL(imageURL, filename);\n                    if (successCallback) {\n                        successCallback();\n                    }\n                }\n                catch (e) {\n                    failCallback(e);\n                }\n            }, function () {\n                if (svg.length > 100000000 /* RegexLimits.svgLimit */) {\n                    throw new Error('Input too long');\n                }\n                // Failed due to tainted canvas\n                // Create new and untainted canvas\n                var canvas = OfflineExporting_doc.createElement('canvas'), ctx = canvas.getContext('2d'), matchedImageWidth = svg.match(\n                    // eslint-disable-next-line max-len\n                    /^<svg[^>]*\\s{,1000}width\\s{,1000}=\\s{,1000}\\\"?(\\d+)\\\"?[^>]*>/), matchedImageHeight = svg.match(\n                    // eslint-disable-next-line max-len\n                    /^<svg[^>]*\\s{0,1000}height\\s{,1000}=\\s{,1000}\\\"?(\\d+)\\\"?[^>]*>/);\n                if (ctx && matchedImageWidth && matchedImageHeight) {\n                    var imageWidth = +matchedImageWidth[1] * scale,\n                        imageHeight = +matchedImageHeight[1] * scale,\n                        downloadWithCanVG = function () {\n                            var v = OfflineExporting_win.canvg.Canvg.fromString(ctx,\n                        svg);\n                        v.start();\n                        try {\n                            OfflineExporting_downloadURL(OfflineExporting_win.navigator.msSaveOrOpenBlob ?\n                                canvas.msToBlob() :\n                                canvas.toDataURL(imageType), filename);\n                            if (successCallback) {\n                                successCallback();\n                            }\n                        }\n                        catch (e) {\n                            failCallback(e);\n                        }\n                        finally {\n                            finallyHandler();\n                        }\n                    };\n                    canvas.width = imageWidth;\n                    canvas.height = imageHeight;\n                    if (OfflineExporting_win.canvg) {\n                        // Use preloaded canvg\n                        downloadWithCanVG();\n                    }\n                    else {\n                        // Must load canVG first.\n                        // Don't destroy the object URL yet since we are\n                        // doing things asynchronously. A cleaner solution\n                        // would be nice, but this will do for now.\n                        objectURLRevoke = true;\n                        getScript(libURL + 'canvg.js', downloadWithCanVG);\n                    }\n                }\n            }, \n            // No canvas support\n            failCallback, \n            // Failed to load image\n            failCallback, \n            // Finally\n            function () {\n                if (objectURLRevoke) {\n                    finallyHandler();\n                }\n            });\n        }\n    }\n    OfflineExporting.downloadSVGLocal = downloadSVGLocal;\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Exporting and offline-exporting modules required. Export a chart to\n     * an image locally in the user's browser.\n     *\n     * @function Highcharts.Chart#exportChartLocal\n     *\n     * @param  {Highcharts.ExportingOptions} [exportingOptions]\n     *         Exporting options, the same as in\n     *         {@link Highcharts.Chart#exportChart}.\n     *\n     * @param  {Highcharts.Options} [chartOptions]\n     *         Additional chart options for the exported chart. For example\n     *         a different background color can be added here, or\n     *         `dataLabels` for export only.\n     *\n     *\n     * @requires modules/exporting\n     * @requires modules/offline-exporting\n     */\n    function exportChartLocal(exportingOptions, chartOptions) {\n        var chart = this,\n            options = OfflineExporting_merge(chart.options.exporting,\n            exportingOptions),\n            fallbackToExportServer = function (err) {\n                if (options.fallbackToExportServer === false) {\n                    if (options.error) {\n                        options.error(options,\n            err);\n                }\n                else {\n                    error(28, true); // Fallback disabled\n                }\n            }\n            else {\n                chart.exportChart(options);\n            }\n        }, svgSuccess = function (svg) {\n            // If SVG contains foreignObjects PDF fails in all browsers\n            // and all exports except SVG will fail in IE, as both CanVG\n            // and svg2pdf choke on this. Gracefully fall back.\n            if (svg.indexOf('<foreignObject') > -1 &&\n                options.type !== 'image/svg+xml' &&\n                ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS || options.type === 'application/pdf')) {\n                fallbackToExportServer(new Error('Image type not supported for charts with embedded HTML'));\n            }\n            else {\n                OfflineExporting.downloadSVGLocal(svg, OfflineExporting_extend({ filename: chart.getFilename() }, options), fallbackToExportServer, function () { return OfflineExporting_fireEvent(chart, 'exportChartLocalSuccess'); });\n            }\n        }, \n        // Return true if the SVG contains images with external data. With\n        // the boost module there are `image` elements with encoded PNGs,\n        // these are supported by svg2pdf and should pass (#10243).\n        hasExternalImages = function () {\n            return [].some.call(chart.container.getElementsByTagName('image'), function (image) {\n                var href = image.getAttribute('href');\n                return (href !== '' &&\n                    typeof href === 'string' &&\n                    href.indexOf('data:') !== 0);\n            });\n        };\n        // If we are on IE and in styled mode, add an allowlist to the renderer\n        // for inline styles that we want to pass through. There are so many\n        // styles by default in IE that we don't want to denylist them all.\n        if ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS && chart.styledMode && !Exporting_Exporting.inlineAllowlist.length) {\n            Exporting_Exporting.inlineAllowlist.push(/^blockSize/, /^border/, /^caretColor/, /^color/, /^columnRule/, /^columnRuleColor/, /^cssFloat/, /^cursor/, /^fill$/, /^fillOpacity/, /^font/, /^inlineSize/, /^length/, /^lineHeight/, /^opacity/, /^outline/, /^parentRule/, /^rx$/, /^ry$/, /^stroke/, /^textAlign/, /^textAnchor/, /^textDecoration/, /^transform/, /^vectorEffect/, /^visibility/, /^x$/, /^y$/);\n        }\n        // Always fall back on:\n        // - MS browsers: Embedded images JPEG/PNG, or any PDF\n        // - Embedded images and PDF\n        if (((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS &&\n            (options.type === 'application/pdf' ||\n                chart.container.getElementsByTagName('image').length &&\n                    options.type !== 'image/svg+xml')) || (options.type === 'application/pdf' &&\n            hasExternalImages())) {\n            fallbackToExportServer(new Error('Image type not supported for this chart/browser.'));\n            return;\n        }\n        chart.getSVGForLocalExport(options, chartOptions || {}, fallbackToExportServer, svgSuccess);\n    }\n    /**\n     * Downloads a script and executes a callback when done.\n     *\n     * @private\n     * @function getScript\n     * @param {string} scriptLocation\n     * @param {Function} callback\n     */\n    function getScript(scriptLocation, callback) {\n        var head = OfflineExporting_doc.getElementsByTagName('head')[0], script = OfflineExporting_doc.createElement('script');\n        script.type = 'text/javascript';\n        script.src = scriptLocation;\n        script.onload = callback;\n        script.onerror = function () {\n            error('Error loading script ' + scriptLocation);\n        };\n        head.appendChild(script);\n    }\n    OfflineExporting.getScript = getScript;\n    /**\n     * Get SVG of chart prepared for client side export. This converts\n     * embedded images in the SVG to data URIs. It requires the regular\n     * exporting module. The options and chartOptions arguments are passed\n     * to the getSVGForExport function.\n     *\n     * @private\n     * @function Highcharts.Chart#getSVGForLocalExport\n     * @param {Highcharts.ExportingOptions} options\n     * @param {Highcharts.Options} chartOptions\n     * @param {Function} failCallback\n     * @param {Function} successCallback\n     */\n    function getSVGForLocalExport(options, chartOptions, failCallback, successCallback) {\n        var chart = this, \n            // After grabbing the SVG of the chart's copy container we need\n            // to do sanitation on the SVG\n            sanitize = function (svg) { return chart.sanitizeSVG(svg,\n            chartCopyOptions); }, \n            // When done with last image we have our SVG\n            checkDone = function () {\n                if (images && imagesEmbedded === imagesLength) {\n                    successCallback(sanitize(chartCopyContainer.innerHTML));\n            }\n        }, \n        // Success handler, we converted image to base64!\n        embeddedSuccess = function (imageURL, imageType, callbackArgs) {\n            ++imagesEmbedded;\n            // Change image href in chart copy\n            callbackArgs.imageElement.setAttributeNS('http://www.w3.org/1999/xlink', 'href', imageURL);\n            checkDone();\n        };\n        var el,\n            chartCopyContainer,\n            chartCopyOptions,\n            href = null,\n            images,\n            imagesLength = 0,\n            imagesEmbedded = 0;\n        // Hook into getSVG to get a copy of the chart copy's\n        // container (#8273)\n        chart.unbindGetSVG = OfflineExporting_addEvent(chart, 'getSVG', function (e) {\n            chartCopyOptions = e.chartCopy.options;\n            chartCopyContainer = e.chartCopy.container.cloneNode(true);\n            images = chartCopyContainer && chartCopyContainer\n                .getElementsByTagName('image') || [];\n            imagesLength = images.length;\n        });\n        // Trigger hook to get chart copy\n        chart.getSVGForExport(options, chartOptions);\n        try {\n            // If there are no images to embed, the SVG is okay now.\n            if (!images || !images.length) {\n                // Use SVG of chart copy\n                successCallback(sanitize(chartCopyContainer.innerHTML));\n                return;\n            }\n            // Go through the images we want to embed\n            for (var i = 0; i < images.length; i++) {\n                el = images[i];\n                href = el.getAttributeNS('http://www.w3.org/1999/xlink', 'href');\n                if (href) {\n                    OfflineExporting.imageToDataUrl(href, 'image/png', { imageElement: el }, options.scale, embeddedSuccess, \n                    // Tainted canvas\n                    failCallback, \n                    // No canvas support\n                    failCallback, \n                    // Failed to load source\n                    failCallback);\n                    // Hidden, boosted series have blank href (#10243)\n                }\n                else {\n                    imagesEmbedded++;\n                    el.parentNode.removeChild(el);\n                    i--;\n                    checkDone();\n                }\n            }\n        }\n        catch (e) {\n            failCallback(e);\n        }\n        // Clean up\n        chart.unbindGetSVG();\n    }\n    /**\n     * Get data:URL from image URL. Pass in callbacks to handle results.\n     *\n     * @private\n     * @function Highcharts.imageToDataUrl\n     *\n     * @param {string} imageURL\n     *\n     * @param {string} imageType\n     *\n     * @param {*} callbackArgs\n     *        callbackArgs is used only by callbacks.\n     *\n     * @param {number} scale\n     *\n     * @param {Function} successCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} taintedCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} noCanvasSupportCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} failedLoadCallback\n     *        Receives four arguments: imageURL, imageType, callbackArgs,\n     *        and scale.\n     *\n     * @param {Function} [finallyCallback]\n     *        finallyCallback is always called at the end of the process. All\n     *        callbacks receive four arguments: imageURL, imageType,\n     *        callbackArgs, and scale.\n     */\n    function imageToDataUrl(imageURL, imageType, callbackArgs, scale, successCallback, taintedCallback, noCanvasSupportCallback, failedLoadCallback, finallyCallback) {\n        var img = new OfflineExporting_win.Image(),\n            taintedHandler;\n        var loadHandler = function () {\n                setTimeout(function () {\n                    var canvas = OfflineExporting_doc.createElement('canvas'), ctx = canvas.getContext && canvas.getContext('2d');\n                var dataURL;\n                try {\n                    if (!ctx) {\n                        noCanvasSupportCallback(imageURL, imageType, callbackArgs, scale);\n                    }\n                    else {\n                        canvas.height = img.height * scale;\n                        canvas.width = img.width * scale;\n                        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);\n                        // Now we try to get the contents of the canvas.\n                        try {\n                            dataURL = canvas.toDataURL(imageType);\n                            successCallback(dataURL, imageType, callbackArgs, scale);\n                        }\n                        catch (e) {\n                            taintedHandler(imageURL, imageType, callbackArgs, scale);\n                        }\n                    }\n                }\n                finally {\n                    if (finallyCallback) {\n                        finallyCallback(imageURL, imageType, callbackArgs, scale);\n                    }\n                }\n                // IE bug where image is not always ready despite calling load\n                // event.\n            }, OfflineExporting.loadEventDeferDelay);\n        }, \n        // Image load failed (e.g. invalid URL)\n        errorHandler = function () {\n            failedLoadCallback(imageURL, imageType, callbackArgs, scale);\n            if (finallyCallback) {\n                finallyCallback(imageURL, imageType, callbackArgs, scale);\n            }\n        };\n        // This is called on load if the image drawing to canvas failed with a\n        // security error. We retry the drawing with crossOrigin set to\n        // Anonymous.\n        taintedHandler = function () {\n            img = new OfflineExporting_win.Image();\n            taintedHandler = taintedCallback;\n            // Must be set prior to loading image source\n            img.crossOrigin = 'Anonymous';\n            img.onload = loadHandler;\n            img.onerror = errorHandler;\n            img.src = imageURL;\n        };\n        img.onload = loadHandler;\n        img.onerror = errorHandler;\n        img.src = imageURL;\n    }\n    OfflineExporting.imageToDataUrl = imageToDataUrl;\n    /**\n     * Get blob URL from SVG code. Falls back to normal data URI.\n     *\n     * @private\n     * @function Highcharts.svgToDataURL\n     */\n    function svgToDataUrl(svg) {\n        // Webkit and not chrome\n        var userAgent = OfflineExporting_win.navigator.userAgent;\n        var webKit = (userAgent.indexOf('WebKit') > -1 &&\n                userAgent.indexOf('Chrome') < 0);\n        try {\n            // Safari requires data URI since it doesn't allow navigation to\n            // blob URLs. ForeignObjects also don't work well in Blobs in Chrome\n            // (#14780).\n            if (!webKit && svg.indexOf('<foreignObject') === -1) {\n                return OfflineExporting.domurl.createObjectURL(new OfflineExporting_win.Blob([svg], {\n                    type: 'image/svg+xml;charset-utf-16'\n                }));\n            }\n        }\n        catch (e) {\n            // Ignore\n        }\n        return 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svg);\n    }\n    OfflineExporting.svgToDataUrl = svgToDataUrl;\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function svgToPdf(svgElement, margin, scale, callback) {\n        var width = (Number(svgElement.getAttribute('width')) + 2 * margin) *\n                scale, height = (Number(svgElement.getAttribute('height')) + 2 * margin) *\n                scale, pdfDoc = new OfflineExporting_win.jspdf.jsPDF(// eslint-disable-line new-cap\n            // setting orientation to portrait if height exceeds width\n            height > width ? 'p' : 'l', 'pt', [width, height]);\n        // Workaround for #7090, hidden elements were drawn anyway. It comes\n        // down to https://github.com/yWorks/svg2pdf.js/issues/28. Check this\n        // later.\n        [].forEach.call(svgElement.querySelectorAll('*[visibility=\"hidden\"]'), function (node) {\n            node.parentNode.removeChild(node);\n        });\n        // Workaround for #13948, multiple stops in linear gradient set to 0\n        // causing error in Acrobat\n        var gradients = svgElement.querySelectorAll('linearGradient');\n        for (var index = 0; index < gradients.length; index++) {\n            var gradient = gradients[index];\n            var stops = gradient.querySelectorAll('stop');\n            var i = 0;\n            while (i < stops.length &&\n                stops[i].getAttribute('offset') === '0' &&\n                stops[i + 1].getAttribute('offset') === '0') {\n                stops[i].remove();\n                i++;\n            }\n        }\n        // Workaround for #15135, zero width spaces, which Highcharts uses\n        // to break lines, are not correctly rendered in PDF. Replace it\n        // with a regular space and offset by some pixels to compensate.\n        [].forEach.call(svgElement.querySelectorAll('tspan'), function (tspan) {\n            if (tspan.textContent === '\\u200B') {\n                tspan.textContent = ' ';\n                tspan.setAttribute('dx', -5);\n            }\n        });\n        pdfDoc.svg(svgElement, {\n            x: 0,\n            y: 0,\n            width: width,\n            height: height,\n            removeInvalid: true\n        }).then(function () { return callback(pdfDoc.output('datauristring')); });\n    }\n    OfflineExporting.svgToPdf = svgToPdf;\n})(OfflineExporting || (OfflineExporting = {}));\n/* *\n *\n * Default Export\n *\n * */\n/* harmony default export */ var OfflineExporting_OfflineExporting = (OfflineExporting);\n\n;// ./code/es5/es-modules/masters/modules/offline-exporting.js\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Compatibility\nG.dataURLtoBlob = G.dataURLtoBlob || Extensions_DownloadURL.dataURLtoBlob;\nG.downloadSVGLocal = OfflineExporting_OfflineExporting.downloadSVGLocal;\nG.downloadURL = G.downloadURL || Extensions_DownloadURL.downloadURL;\n// Compose\nOfflineExporting_OfflineExporting.compose(G.Chart);\n/* harmony default export */ var offline_exporting_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__WEBPACK_EXTERNAL_MODULE__960__", "__WEBPACK_EXTERNAL_MODULE__156__", "ChartNavigationComposition", "Additions", "ExportingSymbols", "Exporting", "OfflineExporting", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "offline_exporting_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "<PERSON><PERSON><PERSON><PERSON>", "win", "doc", "document", "domurl", "URL", "webkitURL", "dataURLtoBlob", "dataURL", "parts", "replace", "match", "length", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "Blob", "createObjectURL", "binStr", "buf", "binary", "i", "charCodeAt", "Extensions_DownloadURL", "downloadURL", "filename", "nav", "navigator", "createElement", "String", "msSaveOrOpenBlob", "userAgent", "Error", "isOldEdgeBrowser", "test", "safariBlob", "indexOf", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "open", "_a", "location", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "compose", "chart", "navigation", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "_this", "for<PERSON>ach", "Chart_ChartNavigationComposition", "isTouchDevice", "Exporting_ExportingDefaults", "exporting", "allowTableSorting", "type", "url", "concat", "version", "pdfFont", "normal", "bold", "bolditalic", "italic", "printMaxWidth", "scale", "buttons", "contextButton", "className", "menuClassName", "symbol", "<PERSON><PERSON><PERSON>", "menuItems", "menuItemDefinitions", "viewFullscreen", "<PERSON><PERSON><PERSON>", "onclick", "fullscreen", "toggle", "printChart", "print", "separator", "downloadPNG", "exportChart", "downloadJPEG", "downloadPDF", "downloadSVG", "lang", "exitFullscreen", "contextButtonTitle", "buttonOptions", "symbolSize", "symbolX", "symbolY", "align", "buttonSpacing", "height", "y", "verticalAlign", "width", "symbolFill", "symbolStroke", "symbolStrokeWidth", "theme", "fill", "padding", "stroke", "menuStyle", "border", "borderRadius", "background", "menuItemStyle", "color", "fontSize", "transition", "menuItemHoverStyle", "modifiedClasses", "menu", "x", "menuball", "h", "path", "circle", "SVGRendererClass", "symbols", "bind", "Exporting_ExportingSymbols", "composed", "addEvent", "fireEvent", "pushUnique", "onChartBeforeRender", "Fullscreen", "isOpen", "container", "renderTo", "browserProps", "requestFullscreen", "fullscreenChange", "mozRequestFullScreen", "webkitRequestFullScreen", "msRequestFullscreen", "ChartClass", "close", "optionsChart", "ownerDocument", "Document", "unbindFullscreenEvent", "setSize", "origWidth", "origHeight", "origWidthOption", "origHeightOption", "setButtonText", "chartWidth", "chartHeight", "unbindChange_1", "unbindDestroy_1", "promise", "alert", "exportDivElements", "exportingOptions", "exportDivElement", "setElementHTML", "text", "highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_", "highcharts_HttpUtilities_commonjs_highcharts_HttpUtilities_commonjs2_highcharts_HttpUtilities_root_Highcharts_HttpUtilities_default", "__assign", "assign", "t", "s", "arguments", "p", "apply", "defaultOptions", "Exporting_doc", "SVG_NS", "Exporting_win", "Exporting_addEvent", "css", "discardElement", "extend", "find", "Exporting_fireEvent", "isObject", "merge", "objectEach", "pick", "removeEvent", "splat", "<PERSON><PERSON><PERSON>", "printingChart", "inlineDenylist", "inlineToAttributes", "inlineAllowlist", "unstyledElements", "addButton", "callback", "renderer", "btnOptions", "btnCount", "exportSVGElements", "enabled", "styledMode", "e", "stopPropagation", "contextMenu", "button", "translateX", "translateY", "setState", "paddingLeft", "useHTML", "addClass", "attr", "title", "_title<PERSON>ey", "Math", "round", "zIndex", "add", "exportingGroup", "buttonOffset", "after<PERSON><PERSON>t", "printReverseInfo", "childNodes", "origDisplay", "resetParams", "moveContainers", "node", "nodeType", "style", "display", "isPrinting", "beforePrint", "pointer", "reset", "chartCallback", "composition", "renderExporting", "destroyExport", "items", "_b", "innerMenu", "navOptions", "cacheName", "menuPadding", "max", "exportContextMenu", "position", "pointerEvents", "scrollablePlotArea", "fixedDiv", "listStyle", "margin", "MozBoxShadow", "WebkitBoxShadow", "boxShadow", "hideMenu", "openMenu", "overflow", "clearTimeout", "hide<PERSON><PERSON>r", "exportEvents", "setTimeout", "inClass", "target", "item", "element", "isDataTableVisible", "on<PERSON><PERSON>ver", "onmouseout", "cursor", "exportMenuWidth", "offsetWidth", "exportMenuHeight", "offsetHeight", "right", "left", "alignOptions", "bottom", "top", "elem", "ontouchstart", "destroy", "unbind", "chartOptions", "svg", "getSVGForExport", "post", "getFilename", "fetchOptions", "getChartHTML", "applyStyleSheets", "inlineStyles", "resolveCSSVariables", "innerHTML", "userOptions", "toLowerCase", "substr", "getSVG", "seriesOptions", "plotOptions", "time", "sandbox", "cssWidth", "cssHeight", "sourceWidth", "parseInt", "isGantt", "sourceHeight", "animation", "forExport", "data", "series", "serie", "enableMouseTracking", "showCheckbox", "visible", "isInternal", "colls", "axes", "axis", "internalKey", "coll", "uniqueNames", "colorAxis", "chartCopy", "constructor", "collOptions", "axisCopy", "copy", "extremes", "getExtremes", "exportOverride", "userMin", "min", "userMax", "setExtremes", "sanitizeSVG", "chartExportingOptions", "dummySVG", "allowlist", "defaultStyles", "iframe", "visibility", "iframeDoc", "contentWindow", "createElementNS", "recurse", "styles", "parentStyles", "dummy", "denylisted", "allowlisted", "filteredStyles", "nodeName", "getComputedStyle", "parentNode", "getElementsByTagName", "namespaceURI", "defaults", "isFirefox", "isMS", "filterStyles", "val", "denylist", "setAttribute", "children", "querySelector", "svgElements", "querySelectorAll", "colorAttributes", "Array", "from", "attrValue", "getAttribute", "includes", "getPropertyValue", "moveTo", "scrollingContainer", "div", "onChartInit", "isDirtyExporting", "onChartLayOutTitle", "_c", "_d", "_e", "alignTo", "textPxLength", "_f", "_g", "_h", "space", "widthAdjust", "alignValue", "focus", "isDirty", "g", "split", "useForeignObject", "html", "allowHTML", "Exporting_Fullscreen", "chartProto", "callbacks", "matchMedia", "addListener", "mqlEvent", "matches", "Exporting_Exporting", "OfflineExporting_OfflineExportingDefaults", "libURL", "exportChartLocal", "OfflineExporting_defaultOptions", "OfflineExporting_downloadURL", "OfflineExporting_doc", "OfflineExporting_win", "ajax", "OfflineExporting_addEvent", "error", "OfflineExporting_extend", "OfflineExporting_fireEvent", "OfflineExporting_merge", "allowedAttributes", "allowedTags", "fallbackToExportServer", "err", "some", "image", "getSVGForLocalExport", "downloadSVGLocal", "getScript", "scriptLocation", "head", "script", "src", "onload", "onerror", "fail<PERSON><PERSON>back", "success<PERSON>allback", "el", "chartCopyContainer", "chartCopyOptions", "images", "sanitize", "checkDone", "imagesEmbedded", "images<PERSON><PERSON>th", "embeddedSuccess", "imageURL", "imageType", "callback<PERSON><PERSON><PERSON>", "imageElement", "setAttributeNS", "unbindGetSVG", "cloneNode", "getAttributeNS", "imageToDataUrl", "taintedCallback", "noCanvasSupportCallback", "failedLoadCallback", "finally<PERSON><PERSON><PERSON>", "taintedHandler", "img", "Image", "loadHandler", "canvas", "ctx", "getContext", "drawImage", "toDataURL", "loadEventDeferDelay", "<PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "svgToDataUrl", "webKit", "encodeURIComponent", "svgToPdf", "svgElement", "Number", "pdfDoc", "jspdf", "jsPDF", "gradients", "index", "stops", "gradient", "remove", "tspan", "textContent", "removeInvalid", "then", "output", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "svgurl", "blob", "<PERSON><PERSON><PERSON><PERSON>", "dummy<PERSON><PERSON><PERSON><PERSON>", "objectURLRevoke", "slice", "loadPdfFonts", "normalBase64", "addFont", "variant", "base64", "API", "events", "addFileToVFS", "getFontList", "HighchartsFont", "setFont", "variants", "shiftAndLoadVariant", "shift", "responseType", "success", "xhr", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "response", "titleElements", "outlineElements", "textElements", "setStylePropertyFromParents", "propName", "curParent", "value", "parseFloat", "property", "fontFamily", "splice", "titleElement", "getElementsByClassName", "outline", "svgNode", "pdfData", "MSBlobBuilder", "append", "getBlob", "revokeObjectURL", "matchedImageWidth", "matchedImageHeight", "imageWidth", "imageHeight", "downloadWithCanVG", "v", "canvg", "Canvg", "fromString", "start", "msToBlob", "OfflineExporting_OfflineExporting", "G", "Chart"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,GAAM,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,aAAgB,EAC7I,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,uCAAwC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,MAAM,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,gBAAgB,CAAC,CAAEJ,GACpL,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,uCAAuC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,GAAM,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,aAAgB,EAErLJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,aAAgB,CAC9I,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACxJ,OAAgB,AAAC,WACP,aACA,IA4PCC,EAgCHC,EAjCJD,EAk9BAE,EAmdAC,EAi6CAC,EAjkGUC,EAAuB,CAE/B,IACC,SAASb,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAalB,OAAO,CAG5B,IAAIC,EAASc,CAAwB,CAACE,EAAS,CAAG,CAGjDjB,QAAS,CAAC,CACX,EAMA,OAHAc,CAAmB,CAACG,EAAS,CAAChB,EAAQA,EAAOD,OAAO,CAAEgB,GAG/Cf,EAAOD,OAAO,AACtB,CAMCgB,EAAoBI,CAAC,CAAG,SAASnB,CAAM,EACtC,IAAIoB,EAASpB,GAAUA,EAAOqB,UAAU,CACvC,WAAa,OAAOrB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAe,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASvB,CAAO,CAAEyB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC3B,EAAS0B,IAC5EE,OAAOC,cAAc,CAAC7B,EAAS0B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAuB,CACtE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAoBjHE,EAAW,AAACD,IAA+EC,QAAQ,CAAEC,EAAM,AAACF,IAA+EE,GAAG,CAAEC,EAAM,AAACH,IAA+EE,GAAG,CAACE,QAAQ,CAMlSC,EAASH,EAAII,GAAG,EAAIJ,EAAIK,SAAS,EAAIL,EAezC,SAASM,EAAcC,CAAO,EAC1B,IAAIC,EAAQD,EACHE,OAAO,CAAC,eAAgB,IACxBC,KAAK,CAAC,yCACf,GAAIF,GACAA,EAAMG,MAAM,CAAG,GACdX,EAAIY,IAAI,EACTZ,EAAIa,WAAW,EACfb,EAAIc,UAAU,EACdd,EAAIe,IAAI,EACPZ,EAAOa,eAAe,CAAG,CAK1B,IAAK,IAHDC,EAASjB,EAAIY,IAAI,CAACJ,CAAK,CAAC,EAAE,EAC1BU,EAAM,IAAIlB,EAAIa,WAAW,CAACI,EAAON,MAAM,EACvCQ,EAAS,IAAInB,EAAIc,UAAU,CAACI,GACvBE,EAAI,EAAGA,EAAID,EAAOR,MAAM,CAAE,EAAES,EACjCD,CAAM,CAACC,EAAE,CAAGH,EAAOI,UAAU,CAACD,GAElC,OAAOjB,EACFa,eAAe,CAAC,IAAIhB,EAAIe,IAAI,CAAC,CAACI,EAAO,CAAE,CAAE,KAAQX,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,CAsE6B,IAAIc,EAJf,CACdhB,cAAeA,EACfiB,YAxDJ,SAAqBhB,CAAO,CAAEiB,CAAQ,EAClC,IAAIC,EAAMzB,EAAI0B,SAAS,CACnB5C,EAAImB,EAAI0B,aAAa,CAAC,KAG1B,GAAI,AAAmB,UAAnB,OAAOpB,GACP,CAAEA,CAAAA,aAAmBqB,MAAK,GAC1BH,EAAII,gBAAgB,CAAE,CACtBJ,EAAII,gBAAgB,CAACtB,EAASiB,GAC9B,MACJ,CAEA,GADAjB,EAAU,GAAKA,EACXkB,EAAIK,SAAS,CAACnB,MAAM,CAAG,IACvB,MAAM,AAAIoB,MAAM,kBAEpB,IAEIC,EAAmB,YAAYC,IAAI,CAACR,EAAIK,SAAS,EAKrD,GAAII,CAAAA,AAHcnC,GACV,AAAmB,UAAnB,OAAOQ,GACPA,AAA4C,IAA5CA,EAAQ4B,OAAO,CAAC,yBACNH,GAAoBzB,EAAQI,MAAM,CAAG,GAAM,GAErD,CADJJ,CAAAA,EAAUD,EAAcC,IAAY,EAAC,EAEjC,MAAM,AAAIwB,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAfjD,EAAEsD,QAAQ,CACjBtD,EAAEuD,IAAI,CAAG9B,EACTzB,EAAEsD,QAAQ,CAAGZ,EACbvB,EAAIqC,IAAI,CAACC,WAAW,CAACzD,GACrBA,EAAE0D,KAAK,GACPvC,EAAIqC,IAAI,CAACG,WAAW,CAAC3D,QAIrB,GAAI,CACA,GAAI,CAACkB,EAAI0C,IAAI,CAACnC,EAAS,SACnB,MAAM,AAAIwB,MAAM,wBAExB,CACA,MAAOY,EAAI,CAEP3C,EAAI4C,QAAQ,CAACP,IAAI,CAAG9B,CACxB,CAER,CASA,EAIIsC,EAAuFvE,EAAoB,KAC3GwE,EAA2GxE,EAAoBI,CAAC,CAACmE,GAElCvE,EAAoB,KAuCnHP,CArBOA,EAqERA,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAhDjCgF,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHKA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAIjF,EAAUgF,EAAK,EAEnCA,CACX,EAgDAjF,EAA2BC,SAAS,CApChCA,EAA2B,WAMvB,SAASA,EAAUgF,CAAK,EACpB,IAAI,CAACE,OAAO,CAAG,EAAE,CACrB,IAAI,CAACF,KAAK,CAAGA,CACjB,CAyBA,OAZAhF,EAAUwB,SAAS,CAAC2D,SAAS,CAAG,SAAUC,CAAQ,EAC9C,IAAI,CAACJ,KAAK,CAACC,UAAU,CAACC,OAAO,CAACG,IAAI,CAACD,EACvC,EAIApF,EAAUwB,SAAS,CAAC8D,MAAM,CAAG,SAAUC,CAAO,CAAEC,CAAM,EAClD,IAAIC,EAAQ,IAAI,CAChB,IAAI,CAACP,OAAO,CAACQ,OAAO,CAAC,SAAUN,CAAQ,EACnCA,EAAS1D,IAAI,CAAC+D,EAAMT,KAAK,CAAEO,EAASC,EACxC,EACJ,EACOxF,CACX,IAQyB,IAAI2F,EAAoC5F,EAcjE6F,EAAgB,AAAC9D,IAA+E8D,aAAa,CAo2BhFC,EALT,CACpBC,UAj1BY,CAwBZC,kBAAmB,CAAA,EAmLnBC,KAAM,YAONC,IAAK,uCAAuCC,MAAM,CAAC,AAACpE,IAA+EqE,OAAO,EAqB1IC,QAAS,CAQLC,OAAQ,KAAK,EAMbC,KAAM,KAAK,EAMXC,WAAY,KAAK,EAMjBC,OAAQ,KAAK,CACjB,EAUAC,cAAe,IAmBfC,MAAO,EAUPC,QAAS,CAWLC,cAAe,CAiCXC,UAAW,2BAIXC,cAAe,yBAgBfC,OAAQ,OASRC,SAAU,qBAoBVC,UAAW,CACP,iBACA,aACA,YACA,cACA,eACA,cACH,AACL,CACJ,EA6BAC,oBAAqB,CAIjBC,eAAgB,CACZC,QAAS,iBACTC,QAAS,WACD,IAAI,CAACC,UAAU,EACf,IAAI,CAACA,UAAU,CAACC,MAAM,EAE9B,CACJ,EAIAC,WAAY,CACRJ,QAAS,aACTC,QAAS,WACL,IAAI,CAACI,KAAK,EACd,CACJ,EAIAC,UAAW,CACPA,UAAW,CAAA,CACf,EAIAC,YAAa,CACTP,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,EACpB,CACJ,EAIAC,aAAc,CACVT,QAAS,eACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb5B,KAAM,YACV,EACJ,CACJ,EAIA8B,YAAa,CACTV,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb5B,KAAM,iBACV,EACJ,CACJ,EAIA+B,YAAa,CACTX,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb5B,KAAM,eACV,EACJ,CACJ,CACJ,CACJ,EAgWIgC,KA3VO,CAOPb,eAAgB,sBAOhBc,eAAgB,wBAOhBT,WAAY,cAOZG,YAAa,qBAObE,aAAc,sBAOdC,YAAa,wBAObC,YAAa,4BAQbG,mBAAoB,oBACxB,EAkSIjD,WA1Ra,CAUbkD,cAAe,CAoBXC,WAAY,GASZC,QAAS,KASTC,QAAS,KAUTC,MAAO,QASPC,cAAe,EASfC,OAAQ,GAsCRC,EAAG,GAWHC,cAAe,MASfC,MAAO,GAUPC,WAAY,UAUZC,aAAc,UASdC,kBAAmB,EAcnBC,MAAO,CAMHC,KAAM,UAINC,QAAS,EAMTC,OAAQ,OAIR,iBAAkB,OACtB,CACJ,EAeAC,UAAW,CAEPC,OAAQ,OAERC,aAAc,MAEdC,WAAY,UAEZL,QAAS,OACb,EAiBAM,cAAe,CAEXD,WAAY,OAEZD,aAAc,MAEdG,MAAO,UAEPP,QAAS,QAETQ,SAAU9D,EAAgB,QAAU,QAEpC+D,WAAY,+BAChB,EAgBAC,mBAAoB,CAEhBL,WAAY,SAChB,CACJ,CAUA,GAsBA,AAAC,SAAUtJ,CAAgB,EAMvB,IAAI4J,EAAkB,EAAE,CAsBxB,SAASC,EAAKC,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EAqB7B,MApBU,CACF,CAAC,IACLsB,EACArB,EAAI,IAAI,CACJ,CAAC,IACLqB,EAAInB,EACJF,EAAI,IAAI,CACJ,CAAC,IACLqB,EACArB,EAAID,EAAS,EAAI,GAAI,CACjB,CAAC,IACLsB,EAAInB,EACJF,EAAID,EAAS,EAAI,GAAI,CACjB,CAAC,IACLsB,EACArB,EAAID,EAAS,IAAI,CACb,CAAC,IACLsB,EAAInB,EACJF,EAAID,EAAS,IAAI,CAChB,AAET,CAIA,SAASuB,EAASD,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EACjC,IAAIwB,EAAI,AAACxB,EAAS,EAAK,EACnByB,EAAO,EAAE,CAEb,OADOA,EAAKhE,MAAM,CAAC,IAAI,CAACiE,MAAM,CAACvB,EAAQqB,EAAGvB,EAAGuB,EAAGA,GAAI,IAAI,CAACE,MAAM,CAACvB,EAAQqB,EAAGvB,EAAIuB,EAAI,EAAGA,EAAGA,GAAI,IAAI,CAACE,MAAM,CAACvB,EAAQqB,EAAGvB,EAAI,EAAKuB,CAAAA,EAAI,CAAA,EAAIA,EAAGA,GAE5I,CAnCAhK,EAAiB8E,OAAO,CARxB,SAAiBqF,CAAgB,EAC7B,GAAIP,AAA8C,KAA9CA,EAAgB1F,OAAO,CAACiG,GAA0B,CAClDP,EAAgBxE,IAAI,CAAC+E,GACrB,IAAIC,EAAUD,EAAiB5I,SAAS,CAAC6I,OAAO,AAChDA,CAAAA,EAAQP,IAAI,CAAGA,EACfO,EAAQL,QAAQ,CAAGA,EAASM,IAAI,CAACD,EACrC,CACJ,CAqCJ,EAAGpK,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAIsK,EAA8BtK,EAsB3DuK,EAAW,AAAC1I,IAA+E0I,QAAQ,CAEnGC,EAAW,AAAC3I,IAA+E2I,QAAQ,CAAEC,EAAY,AAAC5I,IAA+E4I,SAAS,CAAEC,EAAa,AAAC7I,IAA+E6I,UAAU,CASvT,SAASC,IAML,IAAI,CAACtD,UAAU,CAAG,IAAIuD,EAAW,IAAI,CACzC,CAgBA,IAAIA,EAA4B,WAM5B,SAASA,EAAW7F,CAAK,EAMrB,IAAI,CAACA,KAAK,CAAGA,EASb,IAAI,CAAC8F,MAAM,CAAG,CAAA,EACd,IAAIC,EAAY/F,EAAMgG,QAAQ,AAE1B,EAAC,IAAI,CAACC,YAAY,GACd,AAAuC,YAAvC,OAAOF,EAAUG,iBAAiB,CAClC,IAAI,CAACD,YAAY,CAAG,CAChBE,iBAAkB,mBAClBD,kBAAmB,oBACnBjD,eAAgB,gBACpB,EAEK8C,EAAUK,oBAAoB,CACnC,IAAI,CAACH,YAAY,CAAG,CAChBE,iBAAkB,sBAClBD,kBAAmB,uBACnBjD,eAAgB,qBACpB,EAEK8C,EAAUM,uBAAuB,CACtC,IAAI,CAACJ,YAAY,CAAG,CAChBE,iBAAkB,yBAClBD,kBAAmB,0BACnBjD,eAAgB,sBACpB,EAEK8C,EAAUO,mBAAmB,EAClC,CAAA,IAAI,CAACL,YAAY,CAAG,CAChBE,iBAAkB,qBAClBD,kBAAmB,sBACnBjD,eAAgB,kBACpB,CAAA,EAGZ,CA+KA,OAnKA4C,EAAW9F,OAAO,CAAG,SAAUwG,CAAU,EACjCZ,EAAWH,EAAU,eAErBC,EAASc,EAAY,eAAgBX,EAE7C,EAgBAC,EAAWrJ,SAAS,CAACgK,KAAK,CAAG,WACzB,IAAIlE,EAAa,IAAI,CACjBtC,EAAQsC,EAAWtC,KAAK,CACxByG,EAAezG,EAAMO,OAAO,CAACP,KAAK,CACtC0F,EAAU1F,EAAO,kBAAmB,KAAM,WAGlCsC,EAAWwD,MAAM,EACjBxD,EAAW2D,YAAY,EACvBjG,EAAM+F,SAAS,CAACW,aAAa,YAAYC,UACzC3G,EAAM+F,SAAS,CAACW,aAAa,CAACpE,EAAW2D,YAAY,CAAChD,cAAc,CAAC,GAIrEX,EAAWsE,qBAAqB,EAChCtE,CAAAA,EAAWsE,qBAAqB,CAAGtE,EAC9BsE,qBAAqB,EAAC,EAE/B5G,EAAM6G,OAAO,CAACvE,EAAWwE,SAAS,CAAExE,EAAWyE,UAAU,CAAE,CAAA,GAC3DzE,EAAWwE,SAAS,CAAG,KAAK,EAC5BxE,EAAWyE,UAAU,CAAG,KAAK,EAC7BN,EAAa7C,KAAK,CAAGtB,EAAW0E,eAAe,CAC/CP,EAAahD,MAAM,CAAGnB,EAAW2E,gBAAgB,CACjD3E,EAAW0E,eAAe,CAAG,KAAK,EAClC1E,EAAW2E,gBAAgB,CAAG,KAAK,EACnC3E,EAAWwD,MAAM,CAAG,CAAA,EACpBxD,EAAW4E,aAAa,EAC5B,EACJ,EAaArB,EAAWrJ,SAAS,CAACkD,IAAI,CAAG,WACxB,IAAI4C,EAAa,IAAI,CACjBtC,EAAQsC,EAAWtC,KAAK,CACxByG,EAAezG,EAAMO,OAAO,CAACP,KAAK,CACtC0F,EAAU1F,EAAO,iBAAkB,KAAM,WAQrC,GAPIyG,IACAnE,EAAW0E,eAAe,CAAGP,EAAa7C,KAAK,CAC/CtB,EAAW2E,gBAAgB,CAAGR,EAAahD,MAAM,EAErDnB,EAAWwE,SAAS,CAAG9G,EAAMmH,UAAU,CACvC7E,EAAWyE,UAAU,CAAG/G,EAAMoH,WAAW,CAErC9E,EAAW2D,YAAY,CAAE,CACzB,IAAIoB,EAAiB5B,EAASzF,EAAM+F,SAAS,CAACW,aAAa,CACvDpE,EAAW2D,YAAY,CAACE,gBAAgB,CACxC,WAGQ7D,EAAWwD,MAAM,EACjBxD,EAAWwD,MAAM,CAAG,CAAA,EACxBxD,EAAWkE,KAAK,KAGhBxG,EAAM6G,OAAO,CAAC,KAAM,KAAM,CAAA,GAC1BvE,EAAWwD,MAAM,CAAG,CAAA,EACpBxD,EAAW4E,aAAa,GAEhC,GACII,EAAkB7B,EAASzF,EAAO,UAClCqH,EACJ/E,CAAAA,EAAWsE,qBAAqB,CAAG,WAC/BS,IACAC,GACJ,EACA,IAAIC,EAAUvH,EAAMgG,QAAQ,CAAC1D,EAAW2D,YAAY,CAACC,iBAAiB,CAAC,GACnEqB,GACAA,EAAQ,KAAQ,CAAC,WACbC,MACA,+CACJ,EAER,CACJ,EACJ,EAWA3B,EAAWrJ,SAAS,CAAC0K,aAAa,CAAG,WACjC,IAAIlH,EAAQ,IAAI,CAACA,KAAK,CAClByH,EAAoBzH,EAAMyH,iBAAiB,CAC3CC,EAAmB1H,EAAMO,OAAO,CAACO,SAAS,CAC1CmB,EAAayF,GACTA,EAAiB/F,OAAO,EACxB+F,EAAiB/F,OAAO,CAACC,aAAa,CAACK,SAAS,CACpDe,EAAOhD,EAAMO,OAAO,CAACyC,IAAI,CAC7B,GAAI0E,GACAA,EAAiBxF,mBAAmB,EACpCc,GACAA,EAAKC,cAAc,EACnBD,EAAKb,cAAc,EACnBF,GACAwF,EAAmB,CACnB,IAAIE,EAAmBF,CAAiB,CAACxF,EAAU9C,OAAO,CAAC,kBAAkB,CACzEwI,GACA7H,IAA8F8H,cAAc,CAACD,EAAkB,AAAC,IAAI,CAAC7B,MAAM,CAG5G9C,EAAKC,cAAc,CAF7CyE,EAAiBxF,mBAAmB,CAACC,cAAc,CAC/C0F,IAAI,EACL7E,EAAKb,cAAc,CAEnC,CACJ,EAeA0D,EAAWrJ,SAAS,CAAC+F,MAAM,CAAG,WAErBD,AADY,IAAI,CACLwD,MAAM,CAIlBxD,AALa,IAAI,CAKNkE,KAAK,GAHhBlE,AAFa,IAAI,CAEN5C,IAAI,EAKvB,EACOmG,CACX,IAsEIiC,EAA+HxM,EAAoB,KACnJyM,EAAmJzM,EAAoBI,CAAC,CAACoM,GAczKE,EAAgD,WAShD,MAAOA,AARPA,CAAAA,EAAW9L,OAAO+L,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAG/J,EAAI,EAAG1C,EAAI0M,UAAUzK,MAAM,CAAES,EAAI1C,EAAG0C,IAE5C,IAAK,IAAIiK,KADTF,EAAIC,SAAS,CAAChK,EAAE,CACKlC,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACyL,EAAGE,IACzDH,CAAAA,CAAC,CAACG,EAAE,CAAGF,CAAC,CAACE,EAAE,AAAD,EAElB,OAAOH,CACX,CAAA,EACgBI,KAAK,CAAC,IAAI,CAAEF,UAChC,EAKIG,EAAiB,AAACzL,IAA+EyL,cAAc,CAK/GC,EAAgB,AAAC1L,IAA+EG,GAAG,CAAEwL,EAAS,AAAC3L,IAA+E2L,MAAM,CAAEC,EAAgB,AAAC5L,IAA+EE,GAAG,CAGzS2L,EAAqB,AAAC7L,IAA+E2I,QAAQ,CAAEmD,EAAM,AAAC9L,IAA+E8L,GAAG,CAAEjK,EAAgB,AAAC7B,IAA+E6B,aAAa,CAAEkK,EAAiB,AAAC/L,IAA+E+L,cAAc,CAAEC,EAAS,AAAChM,IAA+EgM,MAAM,CAAEC,EAAO,AAACjM,IAA+EiM,IAAI,CAAEC,EAAsB,AAAClM,IAA+E4I,SAAS,CAAEuD,EAAW,AAACnM,IAA+EmM,QAAQ,CAAEC,EAAQ,AAACpM,IAA+EoM,KAAK,CAAEC,EAAa,AAACrM,IAA+EqM,UAAU,CAAEC,EAAO,AAACtM,IAA+EsM,IAAI,CAAEC,EAAc,AAACvM,IAA+EuM,WAAW,CAAEC,EAAQ,AAACxM,IAA+EwM,KAAK,CAAEC,GAAY,AAACzM,IAA+EyM,SAAS,EAOt5C,AAAC,SAAUrO,CAAS,EAYhB,IAmCIsO,EAnCAC,EAAiB,CACb,IACA,sCACA,SACA,4BACA,eACA,0BACA,cACA,oBACA,cACA,WACA,QACH,CAEDC,EAAqB,CACjB,OACA,SACA,gBACA,iBACA,cACA,aACA,IACA,IACH,AACLxO,CAAAA,EAAUyO,eAAe,CAAG,EAAE,CAC9B,IAAIC,EAAmB,CACf,WACA,OACA,OACH,CAoBL,SAASC,EAAUtJ,CAAO,EACtB,IAOIwB,EAaA+H,EApBA9J,EAAQ,IAAI,CACZ+J,EAAW/J,EAAM+J,QAAQ,CACzBC,EAAad,EAAMlJ,EAAMO,OAAO,CAACN,UAAU,CAACkD,aAAa,CACzD5C,GACA8B,EAAU2H,EAAW3H,OAAO,CAC5BJ,EAAY+H,EAAW/H,SAAS,CAChCmB,EAAa4G,EAAW5G,UAAU,EAAI,GAU1C,GARKpD,EAAMiK,QAAQ,EACfjK,CAAAA,EAAMiK,QAAQ,CAAG,CAAA,EAGhBjK,EAAMyH,iBAAiB,GACxBzH,EAAMyH,iBAAiB,CAAG,EAAE,CAC5BzH,EAAMkK,iBAAiB,CAAG,EAAE,EAE5BF,AAAuB,CAAA,IAAvBA,EAAWG,OAAO,EAAeH,EAAWhG,KAAK,EAGrD,IAAIA,EAAQhE,EAAMoK,UAAU,CAAG,CAAC,EAAIJ,EAAWhG,KAAK,CAEhD3B,EACAyH,EAAW,SAAUO,CAAC,EACdA,GACAA,EAAEC,eAAe,GAErBjI,EAAQ3F,IAAI,CAACsD,EAAOqK,EACxB,EAEKpI,GACL6H,CAAAA,EAAW,SAAUO,CAAC,EAEdA,GACAA,EAAEC,eAAe,GAErBtK,EAAMuK,WAAW,CAACC,EAAO1I,aAAa,CAAEG,EAAWuI,EAAOC,UAAU,EAAI,EAAGD,EAAOE,UAAU,EAAI,EAAGF,EAAO5G,KAAK,EAAI,EAAG4G,EAAO/G,MAAM,EAAI,EAAG+G,GAC1IA,EAAOG,QAAQ,CAAC,EACpB,CAAA,EAEAX,EAAWnC,IAAI,EAAImC,EAAWjI,MAAM,CACpCiC,EAAM4G,WAAW,CAAGxB,EAAKpF,EAAM4G,WAAW,CAAE,IAEtCZ,EAAWnC,IAAI,EACrBiB,EAAO9E,EAAO,CACVJ,MAAOoG,EAAWpG,KAAK,CACvBH,OAAQuG,EAAWvG,MAAM,CACzBS,QAAS,CACb,GAEJ,IAAIsG,EAAST,EACJS,MAAM,CAACR,EAAWnC,IAAI,CAAE,EAAG,EAChCiC,EACA9F,EACA,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACLgG,EAAWa,OAAO,EACbC,QAAQ,CAACvK,EAAQsB,SAAS,EAC1BkJ,IAAI,CAAC,CACNC,MAAO5B,EAAKpJ,EAAMO,OAAO,CAACyC,IAAI,CAACgH,EAAWiB,SAAS,EAAIjB,EAAWhI,QAAQ,CAAC,CAAE,GACjF,EACJwI,CAAAA,EAAO1I,aAAa,CAAIvB,EAAQuB,aAAa,EACzC,mBAAqB9B,EAAMiK,QAAQ,GACnCD,EAAWjI,MAAM,GACjBA,EAASgI,EACJhI,MAAM,CAACiI,EAAWjI,MAAM,CAAEmJ,KAAKC,KAAK,CAAC,AAACnB,CAAAA,EAAW3G,OAAO,EAAI,CAAA,EAAMD,EAAa,GAAK8H,KAAKC,KAAK,CAAC,AAACnB,CAAAA,EAAW1G,OAAO,EAAI,CAAA,EAAMF,EAAa,GAAKA,EAAYA,EAE7J,CACEQ,MAAOR,EACPK,OAAQL,CACZ,GACK0H,QAAQ,CAAC,4BACTC,IAAI,CAAC,CACNK,OAAQ,CACZ,GACKC,GAAG,CAACb,GACJxK,EAAMoK,UAAU,EACjBrI,EAAOgJ,IAAI,CAAC,CACR5G,OAAQ6F,EAAWlG,YAAY,CAC/BG,KAAM+F,EAAWnG,UAAU,CAC3B,eAAgBmG,EAAWjG,iBAAiB,EAAI,CACpD,IAGRyG,EACKa,GAAG,CAACrL,EAAMsL,cAAc,EACxB/H,KAAK,CAACuF,EAAOkB,EAAY,CAC1BpG,MAAO4G,EAAO5G,KAAK,CACnBmB,EAAGqE,EAAKY,EAAWjF,CAAC,CAAE/E,EAAMuL,YAAY,CAC5C,GAAI,CAAA,EAAM,cACVvL,EAAMuL,YAAY,EAAK,AAAC,CAAA,AAACf,CAAAA,EAAO5G,KAAK,EAAI,CAAA,EAAKoG,EAAWxG,aAAa,AAAD,EAChEwG,CAAAA,AAAqB,UAArBA,EAAWzG,KAAK,CAAe,GAAK,CAAA,EACzCvD,EAAMkK,iBAAiB,CAAC7J,IAAI,CAACmK,EAAQzI,GACzC,CAaA,SAASyJ,IAEL,GAAKxL,AADO,IAAI,CACLyL,gBAAgB,EAG3B,IAAI9L,EAAKK,AAJG,IAAI,CAIDyL,gBAAgB,CAC3BC,EAAa/L,EAAG+L,UAAU,CAC1BC,EAAchM,EAAGgM,WAAW,CAC5BC,EAAcjM,EAAGiM,WAAW,CAEhC5L,AATY,IAAI,CASV6L,cAAc,CAAC7L,AATT,IAAI,CASWgG,QAAQ,EAEnC,EAAE,CAACtF,OAAO,CAAChE,IAAI,CAACgP,EAAY,SAAUI,CAAI,CAAE1N,CAAC,EACnB,IAAlB0N,EAAKC,QAAQ,EACbD,CAAAA,EAAKE,KAAK,CAACC,OAAO,CAAIN,CAAW,CAACvN,EAAE,EAAI,EAAE,CAElD,GACA4B,AAhBY,IAAI,CAgBVkM,UAAU,CAAG,CAAA,EAEfN,GACA5L,AAnBQ,IAAI,CAmBN6G,OAAO,CAACyB,KAAK,CAnBX,IAAI,CAmBesD,GAE/B,OAAO5L,AArBK,IAAI,CAqBHyL,gBAAgB,CAC7BjC,EAAgB,KAAK,EACrBR,EAvBY,IAAI,CAuBW,cAC/B,CAWA,SAASmD,IAEL,IADIxM,EAEAL,EAAOkJ,EAAclJ,IAAI,CACzBmC,EAAgBzB,AAFR,IAAI,CAEUO,OAAO,CAACO,SAAS,CAACW,aAAa,CACrDgK,EAAmB,CACfC,WAAYpM,EAAKoM,UAAU,CAC3BC,YAAa,EAAE,CACfC,YAAa,KAAK,CACtB,CACJ5L,CARY,IAAI,CAQVkM,UAAU,CAAG,CAAA,EACnB,AAAyB,OAAxBvM,CAAAA,EAAKK,AATM,IAAI,CASJoM,OAAO,AAAD,GAAezM,AAAO,KAAK,IAAZA,GAAyBA,EAAG0M,KAAK,CAAC,KAAK,EAAG,GAC3ErD,EAVY,IAAI,CAUW,eAENvH,GACbzB,AAbI,IAAI,CAaFmH,UAAU,CAAG1F,IAEvBgK,EAAiBG,WAAW,CAAG,CAC3B5L,AAhBI,IAAI,CAgBFO,OAAO,CAACP,KAAK,CAAC4D,KAAK,CACzB,KAAK,EACL,CAAA,EACH,CACD5D,AApBQ,IAAI,CAoBN6G,OAAO,CAACpF,EAAe,KAAK,EAAG,CAAA,IAGzC,EAAE,CAACf,OAAO,CAAChE,IAAI,CAAC+O,EAAiBC,UAAU,CAAE,SAAUI,CAAI,CAAE1N,CAAC,EACpC,IAAlB0N,EAAKC,QAAQ,GACbN,EAAiBE,WAAW,CAACvN,EAAE,CAAG0N,EAAKE,KAAK,CAACC,OAAO,CACpDH,EAAKE,KAAK,CAACC,OAAO,CAAG,OAE7B,GAEAjM,AA9BY,IAAI,CA8BV6L,cAAc,CAACvM,GAErBU,AAhCY,IAAI,CAgCVyL,gBAAgB,CAAGA,CAC7B,CAIA,SAASa,EAActM,CAAK,EAExBuM,AADkBvM,EACNwM,eAAe,GAC3B7D,EAAmB3I,EAAO,SAAUuM,AAFlBvM,EAE8BwM,eAAe,EAE/D7D,EAAmB3I,EAAO,UAAWuM,AAJnBvM,EAI+ByM,aAAa,CAmClE,CAqEA,SAASlC,EAAY1I,CAAS,CAAE6K,CAAK,CAAE3H,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,CAAE+G,CAAM,EAG9D,IAFI7K,EACAgN,EASAC,EARA5M,EAAQ,IAAI,CACZ6M,EAAa7M,EAAMO,OAAO,CAACN,UAAU,CACrCkH,EAAanH,EAAMmH,UAAU,CAC7BC,EAAcpH,EAAMoH,WAAW,CAC/B0F,EAAY,SAAWjL,EAEvBkL,EAAc7B,KAAK8B,GAAG,CAACpJ,EACvBH,GAEAqB,EAAO9E,CAAK,CAAC8M,EAAU,CAEtBhI,IAED9E,EAAMiN,iBAAiB,CAAGjN,CAAK,CAAC8M,EAAU,CAAGhI,EACzCnG,EAAc,MAAO,CACjBkD,UAAWA,CACf,EAAGmG,EAAS,CAAEkF,SAAU,WAAY9B,OAAQ,IAAMlH,QAAS6I,EAAc,KAAMI,cAAe,MAAO,EAAGnN,EAAM+J,QAAQ,CAACiC,KAAK,EAAG,AAAC,CAAA,AAAoC,OAAnCrM,CAAAA,EAAKK,EAAMoN,kBAAkB,AAAD,GAAezN,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG0N,QAAQ,AAAD,GAAMrN,EAAM+F,SAAS,EACxO6G,EAAYjO,EAAc,KAAM,CAAEkD,UAAW,iBAAkB,EAAG7B,EAAMoK,UAAU,CAAG,CAAC,EAAI,CACtFkD,UAAW,OACXC,OAAQ,EACRrJ,QAAS,CACb,EAAGY,GAEE9E,EAAMoK,UAAU,EACjBxB,EAAIgE,EAAW9D,EAAO,CAClB0E,aAAc,oBACdC,gBAAiB,oBACjBC,UAAW,mBACf,EAAGb,EAAWzI,SAAS,GAG3BU,EAAK6I,QAAQ,CAAG,WACZ/E,EAAI9D,EAAM,CAAEmH,QAAS,MAAO,GACxBzB,GACAA,EAAOG,QAAQ,CAAC,GAEpB3K,EAAM4N,QAAQ,CAAG,CAAA,EAEjBhF,EAAI5I,EAAMgG,QAAQ,CAAE,CAAE6H,SAAU,QAAS,GACzCjF,EAAI5I,EAAM+F,SAAS,CAAE,CAAE8H,SAAU,QAAS,GAC1C/Q,IAA8EgR,YAAY,CAAChJ,EAAKiJ,SAAS,EACzG/E,EAAoBhJ,EAAO,mBAC/B,EAEAA,EAAMgO,YAAY,CAAC3N,IAAI,CAACsI,EAAmB7D,EAAM,aAAc,WAC3DA,EAAKiJ,SAAS,CAAGrF,EAAcuF,UAAU,CAACnJ,EAAK6I,QAAQ,CAAE,IAC7D,GAAIhF,EAAmB7D,EAAM,aAAc,WACvChI,IAA8EgR,YAAY,CAAChJ,EAAKiJ,SAAS,CAC7G,GAGApF,EAAmBH,EAAe,UAAW,SAAU6B,CAAC,EACpD,IAAI1K,EACE,CAAA,AAAyB,OAAxBA,CAAAA,EAAKK,EAAMoM,OAAO,AAAD,GAAezM,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuO,OAAO,CAAC7D,EAAE8D,MAAM,CAAEtM,EAAS,GAC1FiD,EAAK6I,QAAQ,EAErB,GAAIhF,EAAmB7D,EAAM,QAAS,WAC9B9E,EAAM4N,QAAQ,EACd9I,EAAK6I,QAAQ,EAErB,IAEAjB,EAAMhM,OAAO,CAAC,SAAU0N,CAAI,EAKxB,GAJoB,UAAhB,OAAOA,GACPA,CAAAA,EAAOpO,EAAMO,OAAO,CAACO,SAAS,CACzBoB,mBAAmB,CAACkM,EAAK,AAAD,EAE7BnF,EAASmF,EAAM,CAAA,GAAO,CACtB,IAAIC,EAAU,KAAK,CACfD,CAAAA,EAAK1L,SAAS,CACd2L,EAAU1P,EAAc,KAAM,KAAK,EAAG,KAAK,EAAGiO,IAKzB,aAAjBwB,EAAKhM,OAAO,EACZpC,EAAMsO,kBAAkB,EACxBF,CAAAA,EAAKhM,OAAO,CAAG,UAAS,EAE5BiM,EAAU1P,EAAc,KAAM,CAC1BkD,UAAW,uBACXQ,QAAS,SAAUgI,CAAC,EACZA,GACAA,EAAEC,eAAe,GAErBxF,EAAK6I,QAAQ,GACO,UAAhB,OAAOS,GAAqBA,EAAK/L,OAAO,EACxC+L,EAAK/L,OAAO,CAACiG,KAAK,CAACtI,EAAOoI,UAElC,CACJ,EAAG,KAAK,EAAGwE,GACX9M,IAA8F8H,cAAc,CAACyG,EAASD,EAAKvG,IAAI,EAC3H7H,EAAMO,OAAO,CAACyC,IAAI,CAACoL,EAAKhM,OAAO,CAAC,EAC/BpC,EAAMoK,UAAU,GACjBiE,EAAQE,WAAW,CAAG,WAClB3F,EAAI,IAAI,CAAEiE,EAAWjI,kBAAkB,CAC3C,EACAyJ,EAAQG,UAAU,CAAG,WACjB5F,EAAI,IAAI,CAAEiE,EAAWrI,aAAa,CACtC,EACAoE,EAAIyF,EAASvF,EAAO,CAChB2F,OAAQ,SACZ,EAAG5B,EAAWrI,aAAa,EAAI,CAAC,MAIxCxE,EAAMyH,iBAAiB,CAACpH,IAAI,CAACgO,EACjC,CACJ,GAGArO,EAAMyH,iBAAiB,CAACpH,IAAI,CAACuM,EAAW9H,GACxC9E,EAAM0O,eAAe,CAAG5J,EAAK6J,WAAW,CACxC3O,EAAM4O,gBAAgB,CAAG9J,EAAK+J,YAAY,EAE9C,IAAIzK,EAAY,CAAE6H,QAAS,OAAQ,CAE/BlH,CAAAA,EAAK/E,CAAAA,EAAM0O,eAAe,EAAI,CAAA,EAAKvH,EACnC/C,EAAU0K,KAAK,CAAG,AAAC3H,EAAapC,EAAInB,EAAQmJ,EAAe,KAG3D3I,EAAU2K,IAAI,CAAG,AAAChK,EAAIgI,EAAe,KAGrCrJ,EAAID,EAAUzD,CAAAA,EAAM4O,gBAAgB,EAAI,CAAA,EAAKxH,GAC7C,AAAC,CAAA,AAA+B,OAA9BuF,CAAAA,EAAKnC,EAAOwE,YAAY,AAAD,GAAerC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGhJ,aAAa,AAAD,IAAO,MACvFS,EAAU6K,MAAM,CAAG,AAAC7H,EAAc1D,EAAIqJ,EAAe,KAGrD3I,EAAU8K,GAAG,CAAG,AAACxL,EAAID,EAASsJ,EAAe,KAEjDnE,EAAI9D,EAAMV,GAEVwE,EAAI5I,EAAMgG,QAAQ,CAAE,CAAE6H,SAAU,EAAG,GACnCjF,EAAI5I,EAAM+F,SAAS,CAAE,CAAE8H,SAAU,EAAG,GACpC7N,EAAM4N,QAAQ,CAAG,CAAA,EACjB5E,EAAoBhJ,EAAO,kBAC/B,CAQA,SAASyM,EAAcpC,CAAC,EACpB,IAIIyC,EAJA9M,EAAQqK,EAAIA,EAAE8D,MAAM,CAAG,IAAI,CAC3BjE,EAAoBlK,EAAMkK,iBAAiB,CAC3CzC,EAAoBzH,EAAMyH,iBAAiB,CAC3CuG,EAAehO,EAAMgO,YAAY,CAGjC9D,IACAA,EAAkBxJ,OAAO,CAAC,SAAUyO,CAAI,CAAE/Q,CAAC,EAEnC+Q,IACAA,EAAK9M,OAAO,CAAG8M,EAAKC,YAAY,CAAG,KAE/BpP,CAAK,CADT8M,EAAY,SAAWqC,EAAKrN,aAAa,CACrB,EAChB,OAAO9B,CAAK,CAAC8M,EAAU,CAE3B5C,CAAiB,CAAC9L,EAAE,CAAG+Q,EAAKE,OAAO,GAE3C,GACAnF,EAAkBvM,MAAM,CAAG,GAG3BqC,EAAMsL,cAAc,GACpBtL,EAAMsL,cAAc,CAAC+D,OAAO,GAC5B,OAAOrP,EAAMsL,cAAc,EAG3B7D,IACAA,EAAkB/G,OAAO,CAAC,SAAUyO,CAAI,CAAE/Q,CAAC,EACnC+Q,IAEArS,IAA8EgR,YAAY,CAACqB,EAAKpB,SAAS,EACzG1E,EAAY8F,EAAM,cAGlB1H,CAAiB,CAACrJ,EAAE,CAChB+Q,EAAKX,UAAU,CACXW,EAAKZ,WAAW,CACZY,EAAKC,YAAY,CACbD,EAAK9M,OAAO,CAAG,KAE/BwG,EAAesG,GAEvB,GACA1H,EAAkB9J,MAAM,CAAG,GAE3BqQ,IACAA,EAAatN,OAAO,CAAC,SAAU4O,CAAM,EACjCA,GACJ,GACAtB,EAAarQ,MAAM,CAAG,EAE9B,CA2BA,SAASiF,EAAY8E,CAAgB,CAAE6H,CAAY,EAC/C,IAAIC,EAAM,IAAI,CAACC,eAAe,CAAC/H,EAC3B6H,GAEJ7H,EAAmBwB,EAAM,IAAI,CAAC3I,OAAO,CAACO,SAAS,CAAE4G,GAEjDK,IAAsI2H,IAAI,CAAChI,EAAiBzG,GAAG,CAAE,CAC7JzC,SAAUkJ,EAAiBlJ,QAAQ,CAC/BkJ,EAAiBlJ,QAAQ,CAACf,OAAO,CAAC,MAAO,KACzC,IAAI,CAACkS,WAAW,GACpB3O,KAAM0G,EAAiB1G,IAAI,CAC3B4C,MAAO8D,EAAiB9D,KAAK,CAC7BlC,MAAOgG,EAAiBhG,KAAK,CAC7B8N,IAAKA,CACT,EAAG9H,EAAiBkI,YAAY,CACpC,CAcA,SAASC,EAAaC,CAAgB,EAKlC,OAJIA,GACA,IAAI,CAACC,YAAY,GAErB,IAAI,CAACC,mBAAmB,GACjB,IAAI,CAACjK,SAAS,CAACkK,SAAS,AACnC,CAWA,SAASN,IACL,IAAIxH,EAAI,IAAI,CAAC+H,WAAW,CAAClF,KAAK,EAAI,IAAI,CAACkF,WAAW,CAAClF,KAAK,CAACnD,IAAI,CACzDrJ,EAAW,IAAI,CAAC+B,OAAO,CAACO,SAAS,CAACtC,QAAQ,QAC9C,AAAIA,EACOA,EAASf,OAAO,CAAC,MAAO,MAElB,UAAb,OAAO0K,GACP3J,CAAAA,EAAW2J,EACNgI,WAAW,GACX1S,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,UAAW,KACnBA,OAAO,CAAC,cAAe,IACvBA,OAAO,CAAC,UAAW,IACnBA,OAAO,CAAC,SAAU,KAClB2S,MAAM,CAAC,EAAG,IACV3S,OAAO,CAAC,UAAW,GAAE,EAE1B,CAAA,CAACe,GAAYA,EAASb,MAAM,CAAG,CAAA,GAC/Ba,CAAAA,EAAW,OAAM,EAEdA,EACX,CAsBA,SAAS6R,EAAOd,CAAY,EAGxB,IAFI5P,EAEA6P,EACAc,EAEA/P,EAAU2I,EAAMlJ,AAJR,IAAI,CAIUO,OAAO,CAC7BgP,EAEJhP,CAAAA,EAAQgQ,WAAW,CAAGrH,EAAMlJ,AAPhB,IAAI,CAOkBkQ,WAAW,CAACK,WAAW,CAAEhB,GAAgBA,EAAagB,WAAW,EAGnGhQ,EAAQiQ,IAAI,CAAGtH,EAAMlJ,AAVT,IAAI,CAUWkQ,WAAW,CAACM,IAAI,CAAEjB,GAAgBA,EAAaiB,IAAI,EAE9E,IAAIC,EAAU9R,EAAc,MACxB,KAAM,CACFuO,SAAU,WACVgC,IAAK,UACLtL,MAAO5D,AAhBH,IAAI,CAgBKmH,UAAU,CAAG,KAC1B1D,OAAQzD,AAjBJ,IAAI,CAiBMoH,WAAW,CAAG,IAChC,EACAoB,EAAclJ,IAAI,EAElBoR,EAAW1Q,AArBH,IAAI,CAqBKgG,QAAQ,CAACgG,KAAK,CAACpI,KAAK,CAAE+M,EAAY3Q,AArB3C,IAAI,CAqB6CgG,QAAQ,CAACgG,KAAK,CAACvI,MAAM,CAAEmN,EAAcrQ,EAAQO,SAAS,CAAC8P,WAAW,EACvHrQ,EAAQP,KAAK,CAAC4D,KAAK,EAClB,MAAM3E,IAAI,CAACyR,IAAaG,SAASH,EAAU,KAC3CnQ,CAAAA,EAAQuQ,OAAO,CAAG,IAAM,GAAE,EAAIC,EAAexQ,EAAQO,SAAS,CAACiQ,YAAY,EAC5ExQ,EAAQP,KAAK,CAACyD,MAAM,EACnB,MAAMxE,IAAI,CAAC0R,IAAcE,SAASF,EAAW,KAC9C,IAER7H,EAAOvI,EAAQP,KAAK,CAAE,CAClBgR,UAAW,CAAA,EACXhL,SAAUyK,EACVQ,UAAW,CAAA,EACXlH,SAAU,cACVnG,MAAOgN,EACPnN,OAAQsN,CACZ,GACAxQ,EAAQO,SAAS,CAACqJ,OAAO,CAAG,CAAA,EAC5B,OAAO5J,EAAQ2Q,IAAI,CAEnB3Q,EAAQ4Q,MAAM,CAAG,EAAE,CACnBnR,AAzCY,IAAI,CAyCVmR,MAAM,CAACzQ,OAAO,CAAC,SAAU0Q,CAAK,EAQ3Bd,AAPLA,CAAAA,EAAgBpH,EAAMkI,EAAMlB,WAAW,CAAE,CACrCc,UAAW,CAAA,EACXK,oBAAqB,CAAA,EACrBC,aAAc,CAAA,EACdC,QAASH,EAAMG,OAAO,AAC1B,EAAC,EAEkBC,UAAU,EACzBjR,EAAQ4Q,MAAM,CAAC9Q,IAAI,CAACiQ,EAE5B,GACA,IAAImB,EAAQ,CAAC,EACbzR,AAtDY,IAAI,CAsDV0R,IAAI,CAAChR,OAAO,CAAC,SAAUiR,CAAI,EAExBA,EAAKzB,WAAW,CAAC0B,WAAW,EAC7BD,CAAAA,EAAKzB,WAAW,CAAC0B,WAAW,CAAGrI,IAAU,EAExCoI,EAAKpR,OAAO,CAACiR,UAAU,GACnBC,CAAK,CAACE,EAAKE,IAAI,CAAC,GACjBJ,CAAK,CAACE,EAAKE,IAAI,CAAC,CAAG,CAAA,EACnBtR,CAAO,CAACoR,EAAKE,IAAI,CAAC,CAAG,EAAE,EAE3BtR,CAAO,CAACoR,EAAKE,IAAI,CAAC,CAACxR,IAAI,CAAC6I,EAAMyI,EAAKzB,WAAW,CAAE,CAC5CqB,QAASI,EAAKJ,OAAO,CAGrBvQ,KAAM2Q,EAAK3Q,IAAI,CACf8Q,YAAaH,EAAKG,WAAW,AACjC,IAER,GAIAvR,EAAQwR,SAAS,CAAG/R,AA5ER,IAAI,CA4EUkQ,WAAW,CAAC6B,SAAS,CAE/C,IAAIC,EAAY,IAAIhS,AA9ER,IAAI,CA8EUiS,WAAW,CAAC1R,EAClCP,AA/EQ,IAAI,CA+EN8J,QAAQ,EA8ClB,OA5CIyF,GACA,CAAC,QAAS,QAAS,SAAS,CAAC7O,OAAO,CAAC,SAAUmR,CAAI,EAC/C,IAAIK,EAAc,CAAC,CACf3C,CAAAA,CAAY,CAACsC,EAAK,GAClBK,CAAW,CAACL,EAAK,CAAGtC,CAAY,CAACsC,EAAK,CACtCG,EAAU1R,MAAM,CAAC4R,GAEzB,GAGJlS,AA3FY,IAAI,CA2FV0R,IAAI,CAAChR,OAAO,CAAC,SAAUiR,CAAI,EAC7B,IAAIQ,EAAWpJ,EAAKiJ,EAAUN,IAAI,CAC9B,SAAUU,CAAI,EACV,OAAOA,EAAK7R,OAAO,CAACqR,WAAW,GAAKD,EAAKzB,WAAW,CAAC0B,WAAW,AACxE,GACA,GAAIO,EAAU,CACV,IAAIE,EAAWV,EAAKW,WAAW,GAK3BC,EAAiBjJ,EAAM,AAACiG,CAAAA,MAAAA,EAAmD,KAAK,EAAIA,CAAY,CAACoC,EAAKE,IAAI,CAAC,AAAD,GAAM,CAAC,EAAE,CAAC,EAAE,CACtHW,EAAU,QAASD,EACfA,EAAeE,GAAG,CAClBJ,EAASG,OAAO,CACpBE,EAAU,QAASH,EACfA,EAAevF,GAAG,CAClBqF,EAASK,OAAO,CACnB,CAAA,AAAoB,KAAA,IAAZF,GACTA,IAAYL,EAASM,GAAG,EAAM,AAAmB,KAAA,IAAZC,GACrCA,IAAYP,EAASnF,GAAG,GACxBmF,EAASQ,WAAW,CAACH,MAAAA,EAAyCA,EAAU,KAAK,EAAGE,MAAAA,EAAyCA,EAAU,KAAK,EAAG,CAAA,EAAM,CAAA,EAEzJ,CACJ,GAEAlD,EAAMwC,EAAUnC,YAAY,CAAC7P,AArHjB,IAAI,CAqHmBoK,UAAU,EACxC,CAAA,AAA6B,OAA5BzK,CAAAA,EAAKY,EAAQO,SAAS,AAAD,GAAenB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmQ,gBAAgB,AAAD,GACrF9G,EAAoB,IAAI,CAAE,SAAU,CAAEgJ,UAAWA,CAAU,GAC3DxC,EAAMxP,AAxHM,IAAI,CAwHJ4S,WAAW,CAACpD,EAAKjP,GAE7BA,EAAU,KACVyR,EAAU3C,OAAO,GACjBxG,EAAe4H,GACRjB,CACX,CAKA,SAASC,EAAgBlP,CAAO,CAAEgP,CAAY,EAC1C,IAAIsD,EAAwB,IAAI,CAACtS,OAAO,CAACO,SAAS,CAClD,OAAO,IAAI,CAACuP,MAAM,CAACnH,EAAM,CAAElJ,MAAO,CAAEsE,aAAc,CAAE,CAAE,EAAGuO,EAAsBtD,YAAY,CAAEA,EAAc,CACvGzO,UAAW,CACP8P,YAAc,AAACrQ,GAAWA,EAAQqQ,WAAW,EACzCiC,EAAsBjC,WAAW,CACrCG,aAAe,AAACxQ,GAAWA,EAAQwQ,YAAY,EAC3C8B,EAAsB9B,YAAY,AAC1C,CACJ,GACJ,CA2BA,SAAShB,IACL,IAGI+C,EAFAC,EAAY7X,EAAUyO,eAAe,CACrCqJ,EAAgB,CAAC,EAIjBC,EAASzK,EAAc7J,aAAa,CAAC,UACzCiK,EAAIqK,EAAQ,CACRrP,MAAO,MACPH,OAAQ,MACRyP,WAAY,QAChB,GACA1K,EAAclJ,IAAI,CAACC,WAAW,CAAC0T,GAC/B,IAAIE,EAAaF,EAAOG,aAAa,EAAIH,EAAOG,aAAa,CAAClW,QAAQ,CAClEiW,GACAA,EAAU7T,IAAI,CAACC,WAAW,CAAC4T,EAAUE,eAAe,CAAC5K,EAAQ,SAgJjE6K,AAxIA,SAASA,EAAQxH,CAAI,EACjB,IACIyH,EACAC,EACAC,EACAC,EACAC,EACAvV,EANAwV,EAAiB,CAAC,EA6DtB,GAAIT,GACArH,AAAkB,IAAlBA,EAAKC,QAAQ,EACbnC,AAA4C,KAA5CA,EAAiBzK,OAAO,CAAC2M,EAAK+H,QAAQ,EAAU,CAOhD,GANAN,EAAS7K,EAAcoL,gBAAgB,CAAChI,EAAM,MAC9C0H,EAAe1H,AAAkB,QAAlBA,EAAK+H,QAAQ,CACxB,CAAC,EACDnL,EAAcoL,gBAAgB,CAAChI,EAAKiI,UAAU,CAAE,MAGhD,CAACf,CAAa,CAAClH,EAAK+H,QAAQ,CAAC,CAAE,CAQ/Bf,EAAWK,EAAUa,oBAAoB,CAAC,MAAM,CAAC,EAAE,CACnDP,EAAQN,EAAUE,eAAe,CAACvH,EAAKmI,YAAY,CAAEnI,EAAK+H,QAAQ,EAClEf,EAASvT,WAAW,CAACkU,GAGrB,IAAItL,EAAIO,EAAcoL,gBAAgB,CAACL,EACnC,MACAS,EAAW,CAAC,EAChB,IAAK,IAAIlY,KAAOmM,EACRnM,EAAI2B,MAAM,CAAG,KACb,AAAkB,UAAlB,OAAOwK,CAAC,CAACnM,EAAI,EACb,CAAC,QAAQiD,IAAI,CAACjD,IACdkY,CAAAA,CAAQ,CAAClY,EAAI,CAAGmM,CAAC,CAACnM,EAAI,AAAD,CAG7BgX,CAAAA,CAAa,CAAClH,EAAK+H,QAAQ,CAAC,CAAGK,EAGT,SAAlBpI,EAAK+H,QAAQ,EACb,OAAOb,EAAcnL,IAAI,CAAC5D,IAAI,CAElC6O,EAASrT,WAAW,CAACgU,EACzB,CAEA,IAAK,IAAIpL,KAAKkL,EAGV,CAAA,AAACzW,IAA+EqX,SAAS,EACrF,AAACrX,IAA+EsX,IAAI,EACpF,AAACtX,IAA+EC,QAAQ,EAExFb,OAAOO,cAAc,CAACC,IAAI,CAAC6W,EAAQlL,EAAC,GACpCgM,AA9FZ,SAAsBC,CAAG,CAAE/X,CAAI,EAG3B,GADAmX,EAAaC,EAAc,CAAA,EACvBZ,EAAUpV,MAAM,CAAE,CAIlB,IADAS,EAAI2U,EAAUpV,MAAM,CACbS,KAAO,CAACuV,GACXA,EAAcZ,CAAS,CAAC3U,EAAE,CAACa,IAAI,CAAC1C,GAEpCmX,EAAa,CAACC,CAClB,CAMA,IAJa,cAATpX,GAAwB+X,AAAQ,SAARA,GACxBZ,CAAAA,EAAa,CAAA,CAAG,EAEpBtV,EAAImW,AAxDG9K,EAwDM9L,MAAM,CACZS,KAAO,CAACsV,GAAY,CACvB,GAAInX,EAAKoB,MAAM,CAAG,IACd,MAAM,AAAIoB,MAAM,kBAEpB2U,EAAca,AA7DX9K,CA6DmB,CAACrL,EAAE,CAACa,IAAI,CAAC1C,IAC3B,AAAe,YAAf,OAAO+X,CACf,CACI,CAACZ,GAIIF,CAAAA,CAAY,CAACjX,EAAK,GAAK+X,GACxBxI,AAAkB,QAAlBA,EAAK+H,QAAQ,AAAS,GACtBb,CAAa,CAAClH,EAAK+H,QAAQ,CAAC,CAACtX,EAAK,GAAK+X,IAEnC,AAAC5K,GACDA,AAAqC,KAArCA,EAAmBvK,OAAO,CAAC5C,GAO3BqX,CAAc,CAACrX,EAAK,CAAG+X,EANnBA,GACAxI,EAAK0I,YAAY,CA7FlCjY,AA6F6CA,EA7FxCkB,OAAO,CAAC,SAAU,SAAUC,CAAK,EACzC,MAAO,IAAMA,EAAMyS,WAAW,EAClC,GA2F2DmE,GASvD,EAkDyBf,CAAM,CAAClL,EAAE,CAAEA,GAShC,GALAO,EAAIkD,EAAM8H,GAEY,QAAlB9H,EAAK+H,QAAQ,EACb/H,EAAK0I,YAAY,CAAC,eAAgB,OAElC1I,AAAkB,SAAlBA,EAAK+H,QAAQ,CACb,OAGJ,EAAE,CAACnT,OAAO,CAAChE,IAAI,CAACoP,EAAK2I,QAAQ,EAAI3I,EAAKJ,UAAU,CAAE4H,EACtD,CACJ,EAUQ,IAAI,CAACvN,SAAS,CAAC2O,aAAa,CAAC,QAJjC5B,EAASiB,UAAU,CAACtU,WAAW,CAACqT,GAEhCG,EAAOc,UAAU,CAACtU,WAAW,CAACwT,EAItC,CAIA,SAASjD,IACL,IAAI2E,EAAc,IAAI,CAAC5O,SAAS,CAAC6O,gBAAgB,CAAC,KAAMC,EAAkB,CAAC,QAAS,OAAQ,aAAc,SAAS,CACnHC,MAAMC,IAAI,CAACJ,GAAajU,OAAO,CAAC,SAAU2N,CAAO,EAC7CwG,EAAgBnU,OAAO,CAAC,SAAUqK,CAAI,EAClC,IAAIiK,EAAY3G,EAAQ4G,YAAY,CAAClK,GACjCiK,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUE,QAAQ,CAAC,OAAM,GAC/E7G,EAAQmG,YAAY,CAACzJ,EAAM+I,iBAAiBzF,GAAS8G,gBAAgB,CAACpK,GAE9E,EACJ,EACJ,CAWA,SAASc,EAAeuJ,CAAM,EAC1B,IAAIhI,EAAqB,IAAI,CAACA,kBAAkB,CAChD,AAEAA,CAAAA,EACI,CACIA,EAAmBC,QAAQ,CAC3BD,EAAmBiI,kBAAkB,CACxC,CACD,CAAC,IAAI,CAACtP,SAAS,CAAC,AAAD,EAAGrF,OAAO,CAAC,SAAU4U,CAAG,EACvCF,EAAO7V,WAAW,CAAC+V,EACvB,EACJ,CAQA,SAASC,IACL,IAAIvV,EAAQ,IAAI,CAUZM,EAAS,SAAU/D,CAAI,CACvBgE,CAAO,CACPC,CAAM,EACFR,EAAMwV,gBAAgB,CAAG,CAAA,EAC7BtM,EAAM,CAAA,EAAMlJ,EAAMO,OAAO,CAAChE,EAAK,CAAEgE,GAC7B6I,EAAK5I,EAAQ,CAAA,IACbR,EAAMQ,MAAM,EAEpB,CACAR,CAAAA,EAAMc,SAAS,CAAG,CACdR,OAAQ,SAAUC,CAAO,CAAEC,CAAM,EAC7BF,EAAO,YAAaC,EAASC,EACjC,CACJ,EAIAG,EACKZ,OAAO,CAACC,GAAOC,UAAU,CACzBE,SAAS,CAAC,SAAUI,CAAO,CAAEC,CAAM,EACpCF,EAAO,aAAcC,EAASC,EAClC,EACJ,CAMA,SAASiV,EAAmB9V,CAAE,EAK1B,IAJIgN,EACA+I,EACAC,EACAC,EACAC,EAAUlW,EAAGkW,OAAO,CACpB7Z,EAAM2D,EAAG3D,GAAG,CACZ8Z,EAAenW,EAAGmW,YAAY,CAC9BpO,EAAmB,IAAI,CAACnH,OAAO,CAACO,SAAS,CACzCiV,EAAK7M,EAAM,AAAmC,OAAlCyD,CAAAA,EAAK,IAAI,CAACpM,OAAO,CAACN,UAAU,AAAD,GAAe0M,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGxJ,aAAa,CAAE,AAAwG,OAAvGuS,CAAAA,EAAKhO,MAAAA,EAA2D,KAAK,EAAIA,EAAiB/F,OAAO,AAAD,GAAe+T,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG9T,aAAa,EAC1P2B,EAAQwS,EAAGxS,KAAK,CAChByS,EAAKD,EAAGvS,aAAa,CAErBG,EAAgBoS,EAAGpS,aAAa,CAChCsS,EAAKF,EAAGnS,KAAK,CAEbsS,EAAQL,EAAQjS,KAAK,CAAGkS,EACxBK,EAAcvS,AAFNqS,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAHbD,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAMpC,CAAA,AAAwG,OAAvGL,CAAAA,EAAKjO,MAAAA,EAA2D,KAAK,EAAIA,EAAiByC,OAAO,AAAD,GAAewL,AAAO,KAAK,IAAZA,GAAgBA,CAAQ,GACzI3Z,AAAQ,UAARA,GACAuH,AAAU,UAAVA,GACAI,AAAkB,QAAlBA,GACIuS,EAAQ,EAAIC,IACRD,EAAQC,EACRN,EAAQjS,KAAK,EAAIuS,EAEX,CAAA,AAAsB,OAArBP,CAAAA,EAAK,IAAI,CAAC5K,KAAK,AAAD,GAAe4K,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGQ,UAAU,AAAD,IAAO,QAChFP,CAAAA,EAAQ9Q,CAAC,EAAIoR,EAAcD,EAAQ,CAAA,EAInD,CAkBA,SAASzT,IACL,IAAIzC,EAAQ,IAAI,EACZA,EAAMkM,UAAU,GAGpB1C,EAAgBxJ,EACX,AAAClD,IAA+EC,QAAQ,EACzFiD,EAAMmM,WAAW,GAIrB8B,WAAW,WACPvF,EAAc2N,KAAK,GACnB3N,EAAcjG,KAAK,GAEd,AAAC3F,IAA+EC,QAAQ,EACzFkR,WAAW,WACPjO,EAAMwL,UAAU,EACpB,EAAG,IAEX,EAAG,GACP,CAOA,SAASgB,IACL,IAAIxM,EAAQ,IAAI,CACZ0H,EAAmB1H,EAAMO,OAAO,CAACO,SAAS,CAC1Ca,EAAU+F,EAAiB/F,OAAO,CAClC2U,EAAUtW,EAAMwV,gBAAgB,EAAI,CAACxV,EAAMkK,iBAAiB,AAChElK,CAAAA,EAAMuL,YAAY,CAAG,EACjBvL,EAAMwV,gBAAgB,EACtBxV,EAAMyM,aAAa,GAEnB6J,GAAW5O,AAA6B,CAAA,IAA7BA,EAAiByC,OAAO,GACnCnK,EAAMgO,YAAY,CAAG,EAAE,CACvBhO,EAAMsL,cAAc,CAAGtL,EAAMsL,cAAc,EACvCtL,EAAM+J,QAAQ,CAACwM,CAAC,CAAC,mBAAmBxL,IAAI,CAAC,CACrCK,OAAQ,CACZ,GAAGC,GAAG,GACVlC,EAAWxH,EAAS,SAAU6I,CAAM,EAChCxK,EAAM6J,SAAS,CAACW,EACpB,GACAxK,EAAMwV,gBAAgB,CAAG,CAAA,EAEjC,CAgBA,SAAS5C,EAAYpD,CAAG,CAAEjP,CAAO,EAE7B,IADIZ,EACA6W,EAAQhH,EAAIrQ,OAAO,CAAC,UAAY,EAAGsX,EAAmBjH,EAAIrQ,OAAO,CAAC,kBAAoB,GACtFuX,EAAOlH,EAAIY,MAAM,CAACoG,GA+BtB,OA7BAhH,EAAMA,EAAIY,MAAM,CAAC,EAAGoG,GAChBC,EAEAjH,EAAMA,EAAI/R,OAAO,CAAC,2BAA4B,SAGzCiZ,GAAS,CAAA,AAA+E,OAA9E/W,CAAAA,EAAKY,MAAAA,EAAyC,KAAK,EAAIA,EAAQO,SAAS,AAAD,GAAenB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGgX,SAAS,AAAD,IACzID,EAAO,qCACSnW,EAAQP,KAAK,CAAC4D,KAAK,CAD5B,aAEUrD,EAAQP,KAAK,CAACyD,MAAM,CAF9B,gDAKHiT,EAAKjZ,OAAO,CAAC,2BAA4B,SALtC,0BAQP+R,EAAMA,EAAI/R,OAAO,CAAC,SAAUiZ,EAAO,WAEvClH,EAAMA,EACD/R,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,sBAAuB,IAC/BA,OAAO,CAAC,qBAAsB,IAC9BA,OAAO,CAAC,uCAAwC,WAChDA,OAAO,CAAC,eAAgB,SACxBA,OAAO,CAAC,QAAS,oDACjBA,OAAO,CAAC,oBAAqB,gBAC7BA,OAAO,CAAC,OAAQ,KAEhBA,OAAO,CAAC,UAAW,QACnBA,OAAO,CAAC,SAAU,OAE3B,CAp4BAvC,EAAU6E,OAAO,CA7CjB,SAAiBwG,CAAU,CAAEnB,CAAgB,EACzCG,EAA2BxF,OAAO,CAACqF,GACnCwR,AA/YiD/Q,EA+Y5B9F,OAAO,CAACwG,GAC7B,IAAIsQ,EAAatQ,EAAW/J,SAAS,AAChCqa,CAAAA,EAAWjU,WAAW,GACvBiU,EAAWrL,UAAU,CAAGA,EACxBqL,EAAWjU,WAAW,CAAGA,EACzBiU,EAAW9G,YAAY,CAAGA,EAC1B8G,EAAWpU,KAAK,CAAGA,EACnBoU,EAAWjE,WAAW,CAAGA,EACzBiE,EAAWhH,YAAY,CAAGA,EAC1BgH,EAAWxG,MAAM,CAAGA,EACpBwG,EAAWpH,eAAe,CAAGA,EAC7BoH,EAAWlH,WAAW,CAAGA,EACzBkH,EAAWhL,cAAc,CAAGA,EAC5BgL,EAAW1K,WAAW,CAAGA,EACzB0K,EAAWtM,WAAW,CAAGA,EACzBsM,EAAWhN,SAAS,CAAGA,EACvBgN,EAAWpK,aAAa,CAAGA,EAC3BoK,EAAWrK,eAAe,CAAGA,EAC7BqK,EAAW7G,mBAAmB,CAAGA,EACjC6G,EAAWC,SAAS,CAACzW,IAAI,CAACiM,GAC1B3D,EAAmBpC,EAAY,OAAQgP,GACvC5M,EAAmBpC,EAAY,cAAekP,GAC1C,AAAC3Y,IAA+EC,QAAQ,EACxF2L,EAAcqO,UAAU,CAAC,SAASC,WAAW,CAAC,SAAUC,CAAQ,EACvDzN,IAGDyN,EAASC,OAAO,CAChB1N,EAAc2C,WAAW,GAGzB3C,EAAcgC,UAAU,GAEhC,GAEJjD,EAAezH,SAAS,CAAGoI,EAAMrI,EAA4BC,SAAS,CAAEyH,EAAezH,SAAS,EAChGyH,EAAevF,IAAI,CAAGkG,EAAMrI,EAA4BmC,IAAI,CAAEuF,EAAevF,IAAI,EAIjFuF,EAAetI,UAAU,CAAGiJ,EAAMrI,EAA4BZ,UAAU,CAAEsI,EAAetI,UAAU,EAE3G,CAs4BJ,EAAG/E,GAAcA,CAAAA,EAAY,CAAC,CAAA,GAMD,IAAIic,GAAuBjc,EAoKvBkc,GA1CF,CAC3BC,OAAQ,0CAGRnV,oBAAqB,CACjBS,YAAa,CACTP,QAAS,cACTC,QAAS,WACL,IAAI,CAACiV,gBAAgB,EACzB,CACJ,EACAzU,aAAc,CACVT,QAAS,eACTC,QAAS,WACL,IAAI,CAACiV,gBAAgB,CAAC,CAClBtW,KAAM,YACV,EACJ,CACJ,EACA+B,YAAa,CACTX,QAAS,cACTC,QAAS,WACL,IAAI,CAACiV,gBAAgB,CAAC,CAClBtW,KAAM,eACV,EACJ,CACJ,EACA8B,YAAa,CACTV,QAAS,cACTC,QAAS,WACL,IAAI,CAACiV,gBAAgB,CAAC,CAClBtW,KAAM,iBACV,EACJ,CACJ,CACJ,CACJ,EAwBIuW,GAAkC,AAACza,IAA+EyL,cAAc,CAEhIiP,GAA+BlZ,EAAuBC,WAAW,CAGjEkZ,GAAuB,AAAC3a,IAA+EG,GAAG,CAAEya,GAAuB,AAAC5a,IAA+EE,GAAG,CAEtN2a,GAAO,AAAC5P,IAAuI4P,IAAI,CAGnJC,GAA4B,AAAC9a,IAA+E2I,QAAQ,CAAEoS,GAAQ,AAAC/a,IAA+E+a,KAAK,CAAEC,GAA0B,AAAChb,IAA+EgM,MAAM,CAAEiP,GAA6B,AAACjb,IAA+E4I,SAAS,CAAEsS,GAAyB,AAAClb,IAA+EoM,KAAK,CACjjBpJ,IAA8FmY,iBAAiB,CAAC5X,IAAI,CAAC,eAAgB,eAAgB,SAAU,KAAM,KAAM,mBAAoB,kBAAmB,iBAAkB,cAAe,YAAa,UAAW,UAAW,aAAc,QAAS,eAC7SP,IAA8FoY,WAAW,CAAC7X,IAAI,CAAC,OAAQ,WAAY,KAOnI,AAAC,SAAUlF,CAAgB,EAiXvB,SAASmc,EAAiB5P,CAAgB,CAAE6H,CAAY,EACpD,IAAIvP,EAAQ,IAAI,CACZO,EAAUyX,GAAuBhY,EAAMO,OAAO,CAACO,SAAS,CACxD4G,GACAyQ,EAAyB,SAAUC,CAAG,EAC9B7X,AAAmC,CAAA,IAAnCA,EAAQ4X,sBAAsB,CAC1B5X,EAAQsX,KAAK,CACbtX,EAAQsX,KAAK,CAACtX,EAC1B6X,GAGQP,GAAM,GAAI,CAAA,GAId7X,EAAM4C,WAAW,CAACrC,EAE1B,EAiCA,GANI,AAACzD,IAA+EsX,IAAI,EAAIpU,EAAMoK,UAAU,EAAI,CAAC+M,GAAoBxN,eAAe,CAAChM,MAAM,EACvJwZ,GAAoBxN,eAAe,CAACtJ,IAAI,CAAC,aAAc,UAAW,cAAe,SAAU,cAAe,mBAAoB,YAAa,UAAW,SAAU,eAAgB,QAAS,cAAe,UAAW,cAAe,WAAY,WAAY,cAAe,OAAQ,OAAQ,UAAW,aAAc,cAAe,kBAAmB,aAAc,gBAAiB,cAAe,MAAO,OAKzY,AAAC,AAACvD,IAA+EsX,IAAI,EACpF7T,CAAAA,AAAiB,oBAAjBA,EAAQS,IAAI,EACThB,EAAM+F,SAAS,CAACiO,oBAAoB,CAAC,SAASrW,MAAM,EAChD4C,AAAiB,kBAAjBA,EAAQS,IAAI,AAAmB,GAAQT,AAAiB,oBAAjBA,EAAQS,IAAI,EAnBpD,EAAE,CAACqX,IAAI,CAAC3b,IAAI,CAACsD,EAAM+F,SAAS,CAACiO,oBAAoB,CAAC,SAAU,SAAUsE,CAAK,EAC9E,IAAIjZ,EAAOiZ,EAAMrD,YAAY,CAAC,QAC9B,MAAQ5V,AAAS,KAATA,GACJ,AAAgB,UAAhB,OAAOA,GACPA,AAA0B,IAA1BA,EAAKF,OAAO,CAAC,QACrB,GAesB,CACtBgZ,EAAuB,AAAIpZ,MAAM,qDACjC,MACJ,CACAiB,EAAMuY,oBAAoB,CAAChY,EAASgP,GAAgB,CAAC,EAAG4I,EAzCxC,SAAU3I,CAAG,EAIrBA,EAAIrQ,OAAO,CAAC,kBAAoB,IAChCoB,AAAiB,kBAAjBA,EAAQS,IAAI,EACX,CAAA,AAAClE,IAA+EsX,IAAI,EAAI7T,AAAiB,oBAAjBA,EAAQS,IAAI,AAAqB,EAC1HmX,EAAuB,AAAIpZ,MAAM,2DAGjC5D,EAAiBqd,gBAAgB,CAAChJ,EAAKsI,GAAwB,CAAEtZ,SAAUwB,EAAM2P,WAAW,EAAG,EAAGpP,GAAU4X,EAAwB,WAAc,OAAOJ,GAA2B/X,EAAO,0BAA4B,EAE/N,EA8BJ,CASA,SAASyY,EAAUC,CAAc,CAAE5O,CAAQ,EACvC,IAAI6O,EAAOlB,GAAqBzD,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAE4E,EAASnB,GAAqB9Y,aAAa,CAAC,SAC7Gia,CAAAA,EAAO5X,IAAI,CAAG,kBACd4X,EAAOC,GAAG,CAAGH,EACbE,EAAOE,MAAM,CAAGhP,EAChB8O,EAAOG,OAAO,CAAG,WACblB,GAAM,wBAA0Ba,EACpC,EACAC,EAAKpZ,WAAW,CAACqZ,EACrB,CAeA,SAASL,EAAqBhY,CAAO,CAAEgP,CAAY,CAAEyJ,CAAY,CAAEC,CAAe,EAC9E,IAkBIC,EACAC,EACAC,EAEAC,EAtBArZ,EAAQ,IAAI,CAGZsZ,EAAW,SAAU9J,CAAG,EAAI,OAAOxP,EAAM4S,WAAW,CAACpD,EACrD4J,EAAmB,EAEnBG,EAAY,WACJF,GAAUG,IAAmBC,GAC7BR,EAAgBK,EAASH,EAAmBlJ,SAAS,EAEjE,EAEAyJ,EAAkB,SAAUC,CAAQ,CAAEC,CAAS,CAAEC,CAAY,EACzD,EAAEL,EAEFK,EAAaC,YAAY,CAACC,cAAc,CAAC,+BAAgC,OAAQJ,GACjFJ,GACJ,EAIIla,EAAO,KAEPoa,EAAe,EACfD,EAAiB,CAGrBxZ,CAAAA,EAAMga,YAAY,CAAGpC,GAA0B5X,EAAO,SAAU,SAAUqK,CAAC,EACvE+O,EAAmB/O,EAAE2H,SAAS,CAACzR,OAAO,CAItCkZ,EAAeJ,AAFfA,CAAAA,EAASF,AADTA,CAAAA,EAAqB9O,EAAE2H,SAAS,CAACjM,SAAS,CAACkU,SAAS,CAAC,CAAA,EAAI,GAC1Bd,EAC1BnF,oBAAoB,CAAC,UAAY,EAAE,AAAD,EACjBrW,MAAM,AAChC,GAEAqC,EAAMyP,eAAe,CAAClP,EAASgP,GAC/B,GAAI,CAEA,GAAI,CAAC8J,GAAU,CAACA,EAAO1b,MAAM,CAAE,CAE3Bsb,EAAgBK,EAASH,EAAmBlJ,SAAS,GACrD,MACJ,CAEA,IAAK,IAAI7R,EAAI,EAAGA,EAAIib,EAAO1b,MAAM,CAAES,IAE/BiB,CAAAA,EAAO6Z,AADPA,CAAAA,EAAKG,CAAM,CAACjb,EAAE,AAAD,EACH8b,cAAc,CAAC,+BAAgC,OAAM,EAE3D/e,EAAiBgf,cAAc,CAAC9a,EAAM,YAAa,CAAEya,aAAcZ,CAAG,EAAG3Y,EAAQmB,KAAK,CAAEgY,EAExFV,EAEAA,EAEAA,IAIAQ,IACAN,EAAGnF,UAAU,CAACtU,WAAW,CAACyZ,GAC1B9a,IACAmb,IAGZ,CACA,MAAOlP,EAAG,CACN2O,EAAa3O,EACjB,CAEArK,EAAMga,YAAY,EACtB,CAqCA,SAASG,EAAeR,CAAQ,CAAEC,CAAS,CAAEC,CAAY,CAAEnY,CAAK,CAAEuX,CAAe,CAAEmB,CAAe,CAAEC,CAAuB,CAAEC,CAAkB,CAAEC,CAAe,EAC5J,IACIC,EADAC,EAAM,IAAI/C,GAAqBgD,KAAK,CAEpCC,EAAc,WACV1M,WAAW,WACP,IACA1Q,EADIqd,EAASnD,GAAqB9Y,aAAa,CAAC,UAAWkc,EAAMD,EAAOE,UAAU,EAAIF,EAAOE,UAAU,CAAC,MAE5G,GAAI,CACA,GAAKD,EAGA,CACDD,EAAOnX,MAAM,CAAGgX,EAAIhX,MAAM,CAAG/B,EAC7BkZ,EAAOhX,KAAK,CAAG6W,EAAI7W,KAAK,CAAGlC,EAC3BmZ,EAAIE,SAAS,CAACN,EAAK,EAAG,EAAGG,EAAOhX,KAAK,CAAEgX,EAAOnX,MAAM,EAEpD,GAAI,CACAlG,EAAUqd,EAAOI,SAAS,CAACpB,GAC3BX,EAAgB1b,EAASqc,EAAWC,EAAcnY,EACtD,CACA,MAAO2I,EAAG,CACNmQ,EAAeb,EAAUC,EAAWC,EAAcnY,EACtD,CACJ,MAdI2Y,EAAwBV,EAAUC,EAAWC,EAAcnY,EAenE,QACQ,CACA6Y,GACAA,EAAgBZ,EAAUC,EAAWC,EAAcnY,EAE3D,CAGJ,EAAGvG,EAAiB8f,mBAAmB,CAC3C,EAEAC,EAAe,WACXZ,EAAmBX,EAAUC,EAAWC,EAAcnY,GAClD6Y,GACAA,EAAgBZ,EAAUC,EAAWC,EAAcnY,EAE3D,EAIA8Y,EAAiB,WACbC,EAAM,IAAI/C,GAAqBgD,KAAK,CACpCF,EAAiBJ,EAEjBK,EAAIU,WAAW,CAAG,YAClBV,EAAI3B,MAAM,CAAG6B,EACbF,EAAI1B,OAAO,CAAGmC,EACdT,EAAI5B,GAAG,CAAGc,CACd,EACAc,EAAI3B,MAAM,CAAG6B,EACbF,EAAI1B,OAAO,CAAGmC,EACdT,EAAI5B,GAAG,CAAGc,CACd,CAQA,SAASyB,EAAa5L,CAAG,EAErB,IAAI1Q,EAAY4Y,GAAqBhZ,SAAS,CAACI,SAAS,CACpDuc,EAAUvc,EAAUK,OAAO,CAAC,UAAY,IACpCL,AAA8B,EAA9BA,EAAUK,OAAO,CAAC,UAC1B,GAAI,CAIA,GAAI,CAACkc,GAAU7L,AAAkC,KAAlCA,EAAIrQ,OAAO,CAAC,kBACvB,OAAOhE,EAAiBgC,MAAM,CAACa,eAAe,CAAC,IAAI0Z,GAAqB3Z,IAAI,CAAC,CAACyR,EAAI,CAAE,CAChFxO,KAAM,8BACV,GAER,CACA,MAAOqJ,EAAG,CAEV,CACA,MAAO,oCAAsCiR,mBAAmB9L,EACpE,CAMA,SAAS+L,EAASC,CAAU,CAAEjO,CAAM,CAAE7L,CAAK,CAAEoI,CAAQ,EACjD,IAAIlG,EAAQ,AAAC6X,CAAAA,OAAOD,EAAWvG,YAAY,CAAC,UAAY,EAAI1H,CAAK,EACzD7L,EAAO+B,EAAS,AAACgY,CAAAA,OAAOD,EAAWvG,YAAY,CAAC,WAAa,EAAI1H,CAAK,EACtE7L,EAAOga,EAAS,IAAIhE,GAAqBiE,KAAK,CAACC,KAAK,CAExDnY,EAASG,EAAQ,IAAM,IAAK,KAAM,CAACA,EAAOH,EAAO,EAIrD,EAAE,CAAC/C,OAAO,CAAChE,IAAI,CAAC8e,EAAW5G,gBAAgB,CAAC,0BAA2B,SAAU9I,CAAI,EACjFA,EAAKiI,UAAU,CAACtU,WAAW,CAACqM,EAChC,GAIA,IAAK,IADD+P,EAAYL,EAAW5G,gBAAgB,CAAC,kBACnCkH,EAAQ,EAAGA,EAAQD,EAAUle,MAAM,CAAEme,IAI1C,IAFA,IAAIC,EAAQC,AADGH,CAAS,CAACC,EAAM,CACVlH,gBAAgB,CAAC,QAClCxW,EAAI,EACDA,EAAI2d,EAAMpe,MAAM,EACnBoe,AAAoC,MAApCA,CAAK,CAAC3d,EAAE,CAAC6W,YAAY,CAAC,WACtB8G,AAAwC,MAAxCA,CAAK,CAAC3d,EAAI,EAAE,CAAC6W,YAAY,CAAC,WAC1B8G,CAAK,CAAC3d,EAAE,CAAC6d,MAAM,GACf7d,IAMR,EAAE,CAACsC,OAAO,CAAChE,IAAI,CAAC8e,EAAW5G,gBAAgB,CAAC,SAAU,SAAUsH,CAAK,EACvC,MAAtBA,EAAMC,WAAW,GACjBD,EAAMC,WAAW,CAAG,IACpBD,EAAM1H,YAAY,CAAC,KAAM,IAEjC,GACAkH,EAAOlM,GAAG,CAACgM,EAAY,CACnBzW,EAAG,EACHrB,EAAG,EACHE,MAAOA,EACPH,OAAQA,EACR2Y,cAAe,CAAA,CACnB,GAAGC,IAAI,CAAC,WAAc,OAAOvS,EAAS4R,EAAOY,MAAM,CAAC,iBAAmB,EAC3E,CAhrBAnhB,EAAiBohB,aAAa,CAAG,CAAC,EAAGphB,EAAiBgC,MAAM,CAAGua,GAAqBta,GAAG,EAAIsa,GAAqBra,SAAS,EAAIqa,GAE7Hvc,EAAiB8f,mBAAmB,CAAG,AAAuF,MAAvF,AAACne,IAA+EsX,IAAI,CAqB3HjZ,EAAiB4E,OAAO,CAVxB,SAAiBwG,CAAU,EACvB,IAAIsQ,EAAatQ,EAAW/J,SAAS,CAOrC,OANKqa,EAAWS,gBAAgB,GAC5BT,EAAW0B,oBAAoB,CAAGA,EAClC1B,EAAWS,gBAAgB,CAAGA,EAE9BU,GAAuB,CAAA,EAAMT,GAAgCzW,SAAS,CAAEsW,KAErE7Q,CACX,EA0TApL,EAAiBqd,gBAAgB,CA3RjC,SAA0BhJ,CAAG,CAAEjP,CAAO,CAAEyY,CAAY,CAAEC,CAAe,EACjE,IAIIuD,EACAC,EACAC,EANAC,EAAoBlF,GAAqB9Y,aAAa,CAAC,OAAQib,EAAYrZ,EAAQS,IAAI,EAAI,YAAaxC,EAAY,AAAC+B,CAAAA,EAAQ/B,QAAQ,EAAI,OAAM,EAC3I,IACCob,CAAAA,AAAc,kBAAdA,EACG,MAAQA,EAAUpD,KAAK,CAAC,IAAI,CAAC,EAAE,AAAD,EAAK9U,EAAQnB,EAAQmB,KAAK,EAAI,EAIpE2V,EAAU9W,EAAQ8W,MAAM,EAAIE,GAAgCzW,SAAS,CAACuW,MAAM,CAC5EuF,EAAkB,CAAA,EAClBxb,EAAUb,EAAQa,OAAO,CAE7BiW,EAASA,AAAqB,MAArBA,EAAOwF,KAAK,CAAC,IAAcxF,EAAS,IAAMA,EAOnD,IAAIyF,EAAe,SAAUtB,CAAU,CAAE1R,CAAQ,EAM7C,IAwBIiT,EA7B4B5U,EAK5B6U,EAAU,SAAUC,CAAO,CAC3BC,CAAM,EACFxF,GAAqBiE,KAAK,CAACC,KAAK,CAACuB,GAAG,CAACC,MAAM,CAAC/c,IAAI,CAAC,CAC7C,cACA,WACI,IAAI,CAACgd,YAAY,CAACJ,EAC9BC,GACQ,IAAI,CAACF,OAAO,CAACC,EAAS,iBAAkBA,GACnC,IAAI,CAACK,WAAW,GAAGC,cAAc,EAClC,IAAI,CAACC,OAAO,CAAC,iBAErB,EACH,CACL,EAGIpc,IArB4B+G,EAqBJqT,EAAWW,WAAW,EAAI,IAnBlD,0BAA0Bld,IAAI,CAACkJ,KAoB/B/G,CAAAA,EAAU,KAAK,CAAA,EAGnB,IAAIqc,EAAW,CAAC,SAAU,SAAU,OAAQ,aAAa,CAKrDC,EAAsB,WAClB,IAAIT,EAAUQ,EAASE,KAAK,GAEhC,GAAI,CAACV,EACD,OAAOnT,IAEX,IAAI7I,EAAMG,GAAWA,CAAO,CAAC6b,EAAQ,CACjChc,EACA0W,GAAK,CACD1W,IAAKA,EACL2c,aAAc,OACdC,QAAS,SAAU3M,CAAI,CAAE4M,CAAG,EACxB,IAAIC,EAAS,IAAIC,UACjBD,CAAAA,EAAOE,SAAS,CAAG,WACf,GAAI,AAAuB,UAAvB,OAAO,IAAI,CAACC,MAAM,CAAe,CACjC,IAAIhB,EAAS,IAAI,CAACgB,MAAM,CAAC1H,KAAK,CAAC,IAAI,CAAC,EAAE,CACtCwG,EAAQC,EAASC,GACD,WAAZD,GACAF,CAAAA,EAAeG,CAAK,CAE5B,CACAQ,GACJ,EACAK,EAAOI,aAAa,CAACL,EAAIM,QAAQ,CACrC,EACAvG,MAAO6F,CACX,IAIIX,GACAC,EAAQC,EAASF,GAErBW,IAER,EACAA,GACJ,EAII5a,EAAc,WACVhD,IAA8F8H,cAAc,CAAC+U,EACjHnN,GACA,IAmBI6O,EACAC,EApBAC,EAAe5B,EAAkB3I,oBAAoB,CAAC,QAItDwK,EAA8B,SAAUtF,CAAE,CAC1CuF,CAAQ,EAER,IADI,IAAIC,EAAYxF,EACbwF,GAAaA,IAAc/B,GAAmB,CACjD,GAAI+B,EAAU1S,KAAK,CAACyS,EAAS,CAAE,CAC3B,IAAIE,EAAQD,EAAU1S,KAAK,CAACyS,EAAS,AACpB,CAAA,aAAbA,GAA2B,MAAMxf,IAAI,CAAC0f,IACtCA,CAAAA,EAAQzT,KAAKC,KAAK,CAACyT,AAAoB,GAApBA,WAAWD,IAAe,IAAG,EAEpDzF,EAAGlN,KAAK,CAACyS,EAAS,CAAGE,EACrB,KACJ,CACAD,EAAYA,EAAU3K,UAAU,AACpC,CACJ,EAKA,EAAE,CAACrT,OAAO,CAAChE,IAAI,CAAC6hB,EAAc,SAAUrF,CAAE,EAsBtC,IAnBA,CAAC,aAAc,WAAW,CACrBxY,OAAO,CAAC,SAAUme,CAAQ,EAC3BL,EAA4BtF,EAAI2F,EACpC,GACA3F,EAAGlN,KAAK,CAAC8S,UAAU,CAAG1d,GAAWA,EAAQC,MAAM,CAE3C,iBAEAzC,OAAOsa,EAAGlN,KAAK,CAAC8S,UAAU,EACtB5F,EAAGlN,KAAK,CAAC8S,UAAU,CAACtI,KAAK,CAAC,KAAKuI,MAAM,CAAC,KAG9CV,EAAgBnF,EAAGlF,oBAAoB,CAAC,SACxC,EAAE,CAACtT,OAAO,CAAChE,IAAI,CAAC2hB,EAAe,SAAUW,CAAY,EACjD9F,EAAGzZ,WAAW,CAACuf,EACnB,GAEAV,EACIpF,EAAG+F,sBAAsB,CAAC,2BACvBX,EAAgB3gB,MAAM,CAAG,GAAG,CAC/B,IAAIuhB,EAAUZ,CAAe,CAAC,EAAE,AAC5BY,CAAAA,EAAQnL,UAAU,EAClBmL,EAAQnL,UAAU,CAACtU,WAAW,CAACyf,EAEvC,CACJ,GACA,IAAIC,EAAUxC,EAAkBjI,aAAa,CAAC,OAC1CyK,GACArC,EAAaqC,EAAS,WAClB5D,EAAS4D,EAAS,EAAGzd,EAAO,SAAU0d,CAAO,EACzC,GAAI,CACA5H,GAA6B4H,EAAS5gB,GAClCya,GACAA,GAER,CACA,MAAO5O,EAAG,CACN2O,EAAa3O,EACjB,CACJ,EACJ,EAER,EAEA,GAAIuP,AAAc,kBAAdA,EAGA,GAAI,CACI,AAA8C,KAAA,IAAvClC,GAAqB2H,aAAa,EAEzC5C,AADAA,CAAAA,EAAO,IAAI/E,GAAqB2H,aAAa,AAAC,EACzCC,MAAM,CAAC9P,GACZgN,EAASC,EAAK8C,OAAO,CAAC,kBAGtB/C,EAASpB,EAAa5L,GAE1BgI,GAA6BgF,EAAQhe,GACjCya,GACAA,GAER,CACA,MAAO5O,EAAG,CACN2O,EAAa3O,EACjB,KAEKuP,AAAc,oBAAdA,EACDlC,GAAqBiE,KAAK,EAAIjE,GAAqBiE,KAAK,CAACC,KAAK,CAC9D9Y,KAMA8Z,EAAkB,CAAA,EAClBnE,EAAUpB,EAAS,WAAY,WAC3BoB,EAAUpB,EAAS,aAAcvU,EACrC,KAKJ0Z,EAASpB,EAAa5L,GACtBkN,EAAiB,WACb,GAAI,CACAvhB,EAAiBgC,MAAM,CAACqiB,eAAe,CAAChD,EAC5C,CACA,MAAOnS,EAAG,CAEV,CACJ,EAEA8P,EAAeqC,EAAQ5C,EAAW,CAAC,EAAGlY,EAAO,SAAUiY,CAAQ,EAE3D,GAAI,CACAnC,GAA6BmC,EAAUnb,GACnCya,GACAA,GAER,CACA,MAAO5O,EAAG,CACN2O,EAAa3O,EACjB,CACJ,EAAG,WACC,GAAImF,EAAI7R,MAAM,CAAG,IACb,MAAM,AAAIoB,MAAM,kBAIpB,IAAI6b,EAASnD,GAAqB9Y,aAAa,CAAC,UAAWkc,EAAMD,EAAOE,UAAU,CAAC,MAAO2E,EAAoBjQ,EAAI9R,KAAK,CAEnH,gEAAiEgiB,EAAqBlQ,EAAI9R,KAAK,CAE/F,kEACJ,GAAImd,GAAO4E,GAAqBC,EAAoB,CAChD,IAAIC,EAAa,CAACF,CAAiB,CAAC,EAAE,CAAG/d,EACrCke,EAAc,CAACF,CAAkB,CAAC,EAAE,CAAGhe,EACvCme,EAAoB,WAGpBC,AAFYpI,GAAqBqI,KAAK,CAACC,KAAK,CAACC,UAAU,CAACpF,EACxDrL,GACE0Q,KAAK,GACP,GAAI,CACA1I,GAA6BE,GAAqBhZ,SAAS,CAACG,gBAAgB,CACxE+b,EAAOuF,QAAQ,GACfvF,EAAOI,SAAS,CAACpB,GAAYpb,GAC7Bya,GACAA,GAER,CACA,MAAO5O,EAAG,CACN2O,EAAa3O,EACjB,QACQ,CACJqS,GACJ,CACJ,CACA9B,CAAAA,EAAOhX,KAAK,CAAG+b,EACf/E,EAAOnX,MAAM,CAAGmc,EACZlI,GAAqBqI,KAAK,CAE1BF,KAOAjD,EAAkB,CAAA,EAClBnE,EAAUpB,EAAS,WAAYwI,GAEvC,CACJ,EAEA7G,EAEAA,EAEA,WACQ4D,GACAF,GAER,GAER,EAoGAvhB,EAAiBsd,SAAS,CAAGA,EAmL7Btd,EAAiBgf,cAAc,CAAGA,EA2BlChf,EAAiBigB,YAAY,CAAGA,EAgDhCjgB,EAAiBogB,QAAQ,CAAGA,CAChC,EAAGpgB,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAIilB,GAAqCjlB,EAQlEklB,GAAKvjB,GAETujB,CAAAA,GAAE/iB,aAAa,CAAG+iB,GAAE/iB,aAAa,EAAIgB,EAAuBhB,aAAa,CACzE+iB,GAAE7H,gBAAgB,CAAG4H,GAAkC5H,gBAAgB,CACvE6H,GAAE9hB,WAAW,CAAG8hB,GAAE9hB,WAAW,EAAID,EAAuBC,WAAW,CAEnE6hB,GAAkCrgB,OAAO,CAACsgB,GAAEC,KAAK,EACpB,IAAI1jB,GAA0BE,IAGjD,OADYH,EAAoB,OAAU,AAE3C,GAET"}