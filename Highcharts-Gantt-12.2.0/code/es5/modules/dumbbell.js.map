{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/dumbbell\n * @requires highcharts\n *\n * (c) 2009-2025 <PERSON>, <PERSON><PERSON><PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/dumbbell\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"SVGRenderer\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/dumbbell\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__540__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ dumbbell_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/AreaRange/AreaRangePoint.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar _a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.area.prototype, AreaPoint = _a.pointClass, areaProto = _a.pointClass.prototype;\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber;\n/* *\n *\n *  Class\n *\n * */\nvar AreaRangePoint = /** @class */ (function (_super) {\n    __extends(AreaRangePoint, _super);\n    function AreaRangePoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /**\n     * Range series only. The high or maximum value for each data point.\n     * @name Highcharts.Point#high\n     * @type {number|undefined}\n     */\n    /**\n     * Range series only. The low or minimum value for each data point.\n     * @name Highcharts.Point#low\n     * @type {number|undefined}\n     */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    AreaRangePoint.prototype.setState = function () {\n        var prevState = this.state,\n            series = this.series,\n            isPolar = series.chart.polar;\n        if (!defined(this.plotHigh)) {\n            // Boost doesn't calculate plotHigh\n            this.plotHigh = series.yAxis.toPixels(this.high, true);\n        }\n        if (!defined(this.plotLow)) {\n            // Boost doesn't calculate plotLow\n            this.plotLow = this.plotY = series.yAxis.toPixels(this.low, true);\n        }\n        series.lowerStateMarkerGraphic = series.stateMarkerGraphic;\n        series.stateMarkerGraphic = series.upperStateMarkerGraphic;\n        // Change state also for the top marker\n        this.graphic = this.graphics && this.graphics[1];\n        this.plotY = this.plotHigh;\n        if (isPolar && isNumber(this.plotHighX)) {\n            this.plotX = this.plotHighX;\n        }\n        // Top state:\n        areaProto.setState.apply(this, arguments);\n        this.state = prevState;\n        // Now restore defaults\n        this.plotY = this.plotLow;\n        this.graphic = this.graphics && this.graphics[0];\n        if (isPolar && isNumber(this.plotLowX)) {\n            this.plotX = this.plotLowX;\n        }\n        series.upperStateMarkerGraphic = series.stateMarkerGraphic;\n        series.stateMarkerGraphic = series.lowerStateMarkerGraphic;\n        // Lower marker is stored at stateMarkerGraphic\n        // to avoid reference duplication (#7021)\n        series.lowerStateMarkerGraphic = void 0;\n        var originalSettings = series.modifyMarkerSettings();\n        // Bottom state\n        areaProto.setState.apply(this, arguments);\n        // Restore previous state\n        series.restoreMarkerSettings(originalSettings);\n    };\n    AreaRangePoint.prototype.haloPath = function () {\n        var isPolar = this.series.chart.polar;\n        var path = [];\n        // Bottom halo\n        this.plotY = this.plotLow;\n        if (isPolar && isNumber(this.plotLowX)) {\n            this.plotX = this.plotLowX;\n        }\n        if (this.isInside) {\n            path = areaProto.haloPath.apply(this, arguments);\n        }\n        // Top halo\n        this.plotY = this.plotHigh;\n        if (isPolar && isNumber(this.plotHighX)) {\n            this.plotX = this.plotHighX;\n        }\n        if (this.isTopInside) {\n            path = path.concat(areaProto.haloPath.apply(this, arguments));\n        }\n        return path;\n    };\n    AreaRangePoint.prototype.isValid = function () {\n        return isNumber(this.low) && isNumber(this.high);\n    };\n    return AreaRangePoint;\n}(AreaPoint));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var AreaRange_AreaRangePoint = (AreaRangePoint);\n\n;// ./code/es5/es-modules/Series/Dumbbell/DumbbellPoint.js\n/* *\n *\n *  (c) 2010-2025 Sebastian Bochan, Rafal Sebestjanski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar DumbbellPoint_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\nvar DumbbellPoint = /** @class */ (function (_super) {\n    DumbbellPoint_extends(DumbbellPoint, _super);\n    function DumbbellPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Set the point's state extended by have influence on the connector\n     * (between low and high value).\n     *\n     * @private\n     */\n    DumbbellPoint.prototype.setState = function () {\n        var _a;\n        var point = this,\n            series = point.series,\n            chart = series.chart,\n            seriesLowColor = series.options.lowColor,\n            seriesMarker = series.options.marker,\n            seriesLowMarker = series.options.lowMarker,\n            pointOptions = point.options,\n            pointLowColor = pointOptions.lowColor,\n            zoneColor = point.zone && point.zone.color,\n            lowerGraphicColor = pick(pointLowColor,\n            seriesLowMarker === null || seriesLowMarker === void 0 ? void 0 : seriesLowMarker.fillColor,\n            seriesLowColor,\n            pointOptions.color,\n            zoneColor,\n            point.color,\n            series.color);\n        var verb = 'attr',\n            upperGraphicColor,\n            origProps;\n        this.pointSetState.apply(point, arguments);\n        if (!point.state) {\n            verb = 'animate';\n            var _b = point.graphics || [],\n                lowerGraphic = _b[0],\n                upperGraphic = _b[1];\n            if (lowerGraphic && !chart.styledMode) {\n                lowerGraphic.attr({\n                    fill: lowerGraphicColor\n                });\n                if (upperGraphic) {\n                    origProps = {\n                        y: point.y,\n                        zone: point.zone\n                    };\n                    point.y = point.high;\n                    point.zone = point.zone ? point.getZone() : void 0;\n                    upperGraphicColor = pick(point.marker ? point.marker.fillColor : void 0, seriesMarker ? seriesMarker.fillColor : void 0, pointOptions.color, point.zone ? point.zone.color : void 0, point.color);\n                    upperGraphic.attr({\n                        fill: upperGraphicColor\n                    });\n                    extend(point, origProps);\n                }\n            }\n        }\n        (_a = point.connector) === null || _a === void 0 ? void 0 : _a[verb](series.getConnectorAttribs(point));\n    };\n    DumbbellPoint.prototype.destroy = function () {\n        var point = this;\n        // #15560\n        if (!point.graphic) {\n            point.graphic = point.connector;\n            point.connector = void 0;\n        }\n        return _super.prototype.destroy.call(this);\n    };\n    return DumbbellPoint;\n}(AreaRange_AreaRangePoint));\nextend(DumbbellPoint.prototype, {\n    pointSetState: AreaRange_AreaRangePoint.prototype.setState\n});\n/* *\n *\n *  Default export\n *\n * */\n/* harmony default export */ var Dumbbell_DumbbellPoint = (DumbbellPoint);\n\n;// ./code/es5/es-modules/Series/Dumbbell/DumbbellSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Sebastian Bochan, Rafal Sebestjanski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The dumbbell series is a cartesian series with higher and lower values\n * for each point along an X axis, connected with a line between the\n * values.\n *\n * Requires `highcharts-more.js` and `modules/dumbbell.js`.\n *\n * @sample {highcharts} highcharts/demo/dumbbell/\n *         Dumbbell chart\n * @sample {highcharts} highcharts/series-dumbbell/styled-mode-dumbbell/\n *         Styled mode\n *\n * @extends      plotOptions.arearange\n * @product      highcharts highstock\n * @excluding    boostThreshold, boostBlendingfillColor, fillOpacity,\n *               legendSymbolColor, lineWidth, stack, stacking, stickyTracking,\n *               trackByArea\n * @since 8.0.0\n * @optionparent plotOptions.dumbbell\n */\nvar DumbbellSeriesDefaults = {\n    /** @ignore-option */\n    trackByArea: false,\n    /** @ignore-option */\n    fillColor: 'none',\n    /** @ignore-option */\n    lineWidth: 0,\n    pointRange: 1,\n    /**\n     * Pixel width of the line that connects the dumbbell point's\n     * values.\n     *\n     * @since 8.0.0\n     * @product   highcharts highstock\n     */\n    connectorWidth: 1,\n    /** @ignore-option */\n    stickyTracking: false,\n    groupPadding: 0.2,\n    crisp: false,\n    pointPadding: 0.1,\n    legendSymbol: 'rectangle',\n    /**\n     * Color of the start markers in a dumbbell graph. This option takes\n     * priority over the series color. To avoid this, set `lowColor` to\n     * `undefined`.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since 8.0.0\n     * @product   highcharts highstock\n     */\n    lowColor: \"#333333\" /* Palette.neutralColor80 */,\n    /**\n     * Color of the line that connects the dumbbell point's values.\n     * By default it is the series' color.\n     *\n     * @type      {string}\n     * @product   highcharts highstock\n     * @since 8.0.0\n     * @apioption plotOptions.dumbbell.connectorColor\n     */\n    /**\n     *\n     * @apioption plotOptions.series.lowMarker\n     */\n    states: {\n        hover: {\n            /** @ignore-option */\n            lineWidthPlus: 0,\n            /**\n             * The additional connector line width for a hovered point.\n             *\n             * @since 8.0.0\n             * @product   highcharts highstock\n             */\n            connectorWidthPlus: 1,\n            /** @ignore-option */\n            halo: false\n        }\n    }\n};\n/**\n * The `dumbbell` series. If the [type](#series.dumbbell.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dumbbell\n * @excluding boostThreshold, boostBlending\n * @product   highcharts highstock\n * @requires  highcharts-more\n * @requires  modules/dumbbell\n * @apioption series.dumbbell\n */\n/**\n * An array of data points for the series. For the `dumbbell` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,low,high`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2\\. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 4, 2],\n *        [1, 2, 1],\n *        [2, 9, 10]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.dumbbell.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        low: 0,\n *        high: 4,\n *        name: \"Point2\",\n *        color: \"#00FF00\",\n *        lowColor: \"#00FFFF\",\n *        connectorWidth: 3,\n *        connectorColor: \"#FF00FF\"\n *    }, {\n *        x: 1,\n *        low: 5,\n *        high: 3,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.arearange.data\n * @product   highcharts highstock\n * @apioption series.dumbbell.data\n */\n/**\n * Color of the start markers in a dumbbell graph. This option takes\n * priority over the series color. To avoid this, set `lowColor` to\n * `undefined`.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @since     8.0.0\n * @product   highcharts highstock\n * @apioption  series.dumbbell.lowColor\n */\n/**\n * Options for the lower markers of the dumbbell-like series. When `lowMarker`\n * is not defined, options inherit form the marker.\n *\n * @see [marker](#series.arearange.marker)\n *\n * @declare   Highcharts.PointMarkerOptionsObject\n * @extends   plotOptions.series.marker\n * @default   undefined\n * @product   highcharts highstock\n * @apioption plotOptions.dumbbell.lowMarker\n */\n/**\n *\n * @sample {highcharts} highcharts/demo/dumbbell-markers\n *         Dumbbell chart with lowMarker option\n *\n * @declare   Highcharts.PointMarkerOptionsObject\n * @extends   plotOptions.series.marker.symbol\n * @product   highcharts highstock\n * @apioption plotOptions.dumbbell.lowMarker.symbol\n */\n/**\n * Color of the line that connects the dumbbell point's values.\n * By default it is the series' color.\n *\n * @type        {string}\n * @since       8.0.0\n * @product     highcharts highstock\n * @apioption   series.dumbbell.data.connectorColor\n */\n/**\n * Pixel width of the line that connects the dumbbell point's values.\n *\n * @type        {number}\n * @since       8.0.0\n * @default     1\n * @product     highcharts highstock\n * @apioption   series.dumbbell.data.connectorWidth\n */\n/**\n * Color of the start markers in a dumbbell graph. This option takes\n * priority over the series color. To avoid this, set `lowColor` to\n * `undefined`.\n *\n * @type        {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @since       8.0.0\n * @default     ${palette.neutralColor80}\n * @product     highcharts highstock\n * @apioption   series.dumbbell.data.lowColor\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Dumbbell_DumbbellSeriesDefaults = (DumbbellSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es5/es-modules/Series/Dumbbell/DumbbellSeries.js\n/* *\n *\n *  (c) 2010-2025 Sebastian Bochan, Rafal Sebestjanski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar DumbbellSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar DumbbellSeries_a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes, AreaRangeSeries = DumbbellSeries_a.arearange, ColumnSeries = DumbbellSeries_a.column, ColumnRangeSeries = DumbbellSeries_a.columnrange;\n\n\nvar DumbbellSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, DumbbellSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The dumbbell series type\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.dumbbell\n *\n * @augments Highcharts.Series\n */\nvar DumbbellSeries = /** @class */ (function (_super) {\n    DumbbellSeries_extends(DumbbellSeries, _super);\n    function DumbbellSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get connector line path and styles that connects dumbbell point's low and\n     * high values.\n     * @private\n     *\n     * @param {Highcharts.Point} point The point to inspect.\n     *\n     * @return {Highcharts.SVGAttributes} attribs The path and styles.\n     */\n    DumbbellSeries.prototype.getConnectorAttribs = function (point) {\n        var series = this,\n            chart = series.chart,\n            pointOptions = point.options,\n            seriesOptions = series.options,\n            xAxis = series.xAxis,\n            yAxis = series.yAxis,\n            connectorWidthPlus = DumbbellSeries_pick(seriesOptions.states &&\n                seriesOptions.states.hover &&\n                seriesOptions.states.hover.connectorWidthPlus, 1),\n            dashStyle = DumbbellSeries_pick(pointOptions.dashStyle,\n            seriesOptions.dashStyle),\n            pxThreshold = yAxis.toPixels(seriesOptions.threshold || 0,\n            true),\n            pointHeight = chart.inverted ?\n                yAxis.len - pxThreshold : pxThreshold;\n        var connectorWidth = DumbbellSeries_pick(pointOptions.connectorWidth,\n            seriesOptions.connectorWidth),\n            connectorColor = DumbbellSeries_pick(pointOptions.connectorColor,\n            seriesOptions.connectorColor,\n            pointOptions.color,\n            point.zone ? point.zone.color : void 0,\n            point.color),\n            pointTop = DumbbellSeries_pick(point.plotLow,\n            point.plotY),\n            pointBottom = DumbbellSeries_pick(point.plotHigh,\n            pointHeight),\n            origProps;\n        if (typeof pointTop !== 'number') {\n            return {};\n        }\n        if (point.state) {\n            connectorWidth = connectorWidth + connectorWidthPlus;\n        }\n        if (pointTop < 0) {\n            pointTop = 0;\n        }\n        else if (pointTop >= yAxis.len) {\n            pointTop = yAxis.len;\n        }\n        if (pointBottom < 0) {\n            pointBottom = 0;\n        }\n        else if (pointBottom >= yAxis.len) {\n            pointBottom = yAxis.len;\n        }\n        if (point.plotX < 0 || point.plotX > xAxis.len) {\n            connectorWidth = 0;\n        }\n        // Connector should reflect upper marker's zone color\n        if (point.graphics && point.graphics[1]) {\n            origProps = {\n                y: point.y,\n                zone: point.zone\n            };\n            point.y = point.high;\n            point.zone = point.zone ? point.getZone() : void 0;\n            connectorColor = DumbbellSeries_pick(pointOptions.connectorColor, seriesOptions.connectorColor, pointOptions.color, point.zone ? point.zone.color : void 0, point.color);\n            DumbbellSeries_extend(point, origProps);\n        }\n        var attribs = {\n                d: highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default().prototype.crispLine([[\n                        'M',\n                        point.plotX,\n                        pointTop\n                    ],\n            [\n                        'L',\n                        point.plotX,\n                        pointBottom\n                    ]],\n            connectorWidth)\n            };\n        if (!chart.styledMode) {\n            attribs.stroke = connectorColor;\n            attribs['stroke-width'] = connectorWidth;\n            if (dashStyle) {\n                attribs.dashstyle = dashStyle;\n            }\n        }\n        return attribs;\n    };\n    /**\n     * Draw connector line that connects dumbbell point's low and high values.\n     * @private\n     * @param {Highcharts.Point} point\n     *        The point to inspect.\n     */\n    DumbbellSeries.prototype.drawConnector = function (point) {\n        var series = this,\n            animationLimit = DumbbellSeries_pick(series.options.animationLimit, 250),\n            verb = point.connector && series.chart.pointCount < animationLimit ?\n                'animate' : 'attr';\n        if (!point.connector) {\n            point.connector = series.chart.renderer.path()\n                .addClass('highcharts-lollipop-stem')\n                .attr({\n                zIndex: -1\n            })\n                .add(series.group);\n        }\n        point.connector[verb](this.getConnectorAttribs(point));\n    };\n    /**\n     * Return the width and x offset of the dumbbell adjusted for grouping,\n     * groupPadding, pointPadding, pointWidth etc.\n     * @private\n     */\n    DumbbellSeries.prototype.getColumnMetrics = function () {\n        var metrics = ColumnSeries.prototype\n                .getColumnMetrics.apply(this,\n            arguments);\n        metrics.offset += metrics.width / 2;\n        return metrics;\n    };\n    /**\n     * Translate each point to the plot area coordinate system and find\n     * shape positions\n     * @private\n     */\n    DumbbellSeries.prototype.translate = function () {\n        var series = this,\n            inverted = series.chart.inverted;\n        // Calculate shapeargs\n        this.setShapeArgs.apply(series);\n        // Calculate point low / high values\n        this.translatePoint.apply(series, arguments);\n        // Correct x position\n        for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            var pointWidth = point.pointWidth,\n                _b = point.shapeArgs,\n                shapeArgs = _b === void 0 ? {} : _b,\n                tooltipPos = point.tooltipPos;\n            point.plotX = shapeArgs.x || 0;\n            shapeArgs.x = point.plotX - pointWidth / 2;\n            if (tooltipPos) {\n                if (inverted) {\n                    tooltipPos[1] = series.xAxis.len - point.plotX;\n                }\n                else {\n                    tooltipPos[0] = point.plotX;\n                }\n            }\n        }\n        series.columnMetrics.offset -= series.columnMetrics.width / 2;\n    };\n    /**\n     * Extend the arearange series' drawPoints method by applying a connector\n     * and coloring markers.\n     * @private\n     */\n    DumbbellSeries.prototype.drawPoints = function () {\n        var series = this,\n            chart = series.chart,\n            pointLength = series.points.length,\n            seriesLowColor = series.lowColor = series.options.lowColor,\n            seriesLowMarker = series.options.lowMarker;\n        var i = 0,\n            lowerGraphicColor,\n            point,\n            zoneColor;\n        this.seriesDrawPoints.apply(series, arguments);\n        // Draw connectors and color upper markers\n        while (i < pointLength) {\n            point = series.points[i];\n            var _a = point.graphics || [],\n                lowerGraphic = _a[0],\n                upperGraphic = _a[1];\n            series.drawConnector(point);\n            if (upperGraphic) {\n                upperGraphic.element.point = point;\n                upperGraphic.addClass('highcharts-lollipop-high');\n            }\n            if (point.connector) {\n                point.connector.element.point = point;\n            }\n            if (lowerGraphic) {\n                zoneColor = point.zone && point.zone.color;\n                lowerGraphicColor = DumbbellSeries_pick(point.options.lowColor, seriesLowMarker === null || seriesLowMarker === void 0 ? void 0 : seriesLowMarker.fillColor, seriesLowColor, point.options.color, zoneColor, point.color, series.color);\n                if (!chart.styledMode) {\n                    lowerGraphic.attr({\n                        fill: lowerGraphicColor\n                    });\n                }\n                lowerGraphic.addClass('highcharts-lollipop-low');\n            }\n            i++;\n        }\n    };\n    /**\n     * Get presentational attributes.\n     *\n     * @private\n     * @function Highcharts.seriesTypes.column#pointAttribs\n     *\n     * @param {Highcharts.Point} point\n     *        The point to inspect.\n     *\n     * @param {string} state\n     *        Current state of point (normal, hover, select).\n     *\n     * @return {Highcharts.SVGAttributes}\n     *         Presentational attributes.\n     */\n    DumbbellSeries.prototype.pointAttribs = function (point, state) {\n        var pointAttribs = _super.prototype.pointAttribs.apply(this,\n            arguments);\n        if (state === 'hover') {\n            delete pointAttribs.fill;\n        }\n        return pointAttribs;\n    };\n    /**\n     * Set the shape arguments for dummbells.\n     * @private\n     */\n    DumbbellSeries.prototype.setShapeArgs = function () {\n        ColumnSeries.prototype.translate.apply(this);\n        ColumnRangeSeries.prototype.afterColumnTranslate.apply(this);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    DumbbellSeries.defaultOptions = merge(AreaRangeSeries.defaultOptions, Dumbbell_DumbbellSeriesDefaults);\n    return DumbbellSeries;\n}(AreaRangeSeries));\nDumbbellSeries_extend(DumbbellSeries.prototype, {\n    crispCol: ColumnSeries.prototype.crispCol,\n    drawGraph: noop,\n    drawTracker: ColumnSeries.prototype.drawTracker,\n    pointClass: Dumbbell_DumbbellPoint,\n    seriesDrawPoints: AreaRangeSeries.prototype.drawPoints,\n    trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n    translatePoint: AreaRangeSeries.prototype.translate\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('dumbbell', DumbbellSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Dumbbell_DumbbellSeries = ((/* unused pure expression or super */ null && (DumbbellSeries)));\n\n;// ./code/es5/es-modules/masters/modules/dumbbell.js\n\n\n\n\n/* harmony default export */ var dumbbell_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__540__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dumbbell_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "_a", "seriesTypes", "area", "AreaPoint", "pointClass", "areaProto", "defined", "isNumber", "AreaRangePoint", "_super", "apply", "arguments", "setState", "prevState", "state", "series", "isPolar", "chart", "polar", "plotHigh", "yAxis", "toPixels", "high", "plotLow", "plotY", "low", "lowerStateMarkerGraphic", "stateMarkerGraphic", "upperStateMarkerGraphic", "graphic", "graphics", "plotHighX", "plotX", "plotLowX", "originalSettings", "modifyMarkerSettings", "restoreMarkerSettings", "haloPath", "path", "isInside", "isTopInside", "concat", "<PERSON><PERSON><PERSON><PERSON>", "DumbbellPoint_extends", "TypeError", "String", "extend", "pick", "DumbbellPoint", "upperGraphicColor", "origProps", "point", "seriesLowColor", "options", "lowColor", "seriesMarker", "marker", "seriesLowMarker", "lowMark<PERSON>", "pointOptions", "pointLowColor", "zoneColor", "zone", "color", "lowerGraphicColor", "fillColor", "verb", "pointSetState", "_b", "lowerGraphic", "upperGraphic", "styledMode", "attr", "fill", "y", "getZone", "connector", "getConnectorAttribs", "destroy", "AreaRange_AreaRangePoint", "Dumbbell_DumbbellSeriesDefaults", "trackByArea", "lineWidth", "pointRange", "connectorWidth", "stickyTracking", "groupPadding", "crisp", "pointPadding", "legendSymbol", "states", "hover", "lineWidthPlus", "connectorWidthPlus", "halo", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "DumbbellSeries_extends", "noop", "DumbbellSeries_a", "AreaRangeSeries", "arearange", "ColumnSeries", "column", "ColumnRangeSeries", "columnrange", "DumbbellSeries_extend", "merge", "DumbbellSeries_pick", "DumbbellSeries", "seriesOptions", "xAxis", "dashStyle", "px<PERSON><PERSON><PERSON><PERSON>", "threshold", "pointHeight", "inverted", "len", "connectorColor", "pointTop", "pointBottom", "attribs", "crispLine", "stroke", "dashstyle", "drawConnector", "animationLimit", "pointCount", "renderer", "addClass", "zIndex", "add", "group", "getColumnMetrics", "metrics", "offset", "width", "translate", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translatePoint", "_i", "points", "length", "pointWidth", "shapeArgs", "tooltipPos", "x", "columnMetrics", "drawPoints", "point<PERSON><PERSON><PERSON>", "i", "seriesDrawPoints", "element", "pointAttribs", "afterColumnTranslate", "defaultOptions", "crispCol", "drawGraph", "drawTracker", "trackerGroups", "registerSeriesType"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,WAAc,EACtH,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,cAAc,CAAC,CAAEJ,GACjJ,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,WAAc,EAErJJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,WAAc,CAC1H,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACtH,OAAgB,AAAC,WACP,aACA,IAyGFC,EAoIAA,EAwWAA,EArlBMC,EAAuB,CAE/B,IACC,SAASR,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,SAASd,CAAM,EACtC,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,WAAa,OAAOhB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASlB,CAAO,CAAEoB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAc,CAC7D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAY7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGAG,EAAK,AAACV,IAA2IW,WAAW,CAACC,IAAI,CAACpB,SAAS,CAAEqB,EAAYH,EAAGI,UAAU,CAAEC,EAAYL,EAAGI,UAAU,CAACtB,SAAS,CAE3OwB,EAAU,AAAClB,IAA+EkB,OAAO,CAAEC,EAAW,AAACnB,IAA+EmB,QAAQ,CAMtMC,EAAgC,SAAUC,CAAM,EAEhD,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAmFA,OAtFApB,EAAUiB,EAAgBC,GAsB1BD,EAAe1B,SAAS,CAAC8B,QAAQ,CAAG,WAChC,IAAIC,EAAY,IAAI,CAACC,KAAK,CACtBC,EAAS,IAAI,CAACA,MAAM,CACpBC,EAAUD,EAAOE,KAAK,CAACC,KAAK,CAC3BZ,EAAQ,IAAI,CAACa,QAAQ,GAEtB,CAAA,IAAI,CAACA,QAAQ,CAAGJ,EAAOK,KAAK,CAACC,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAE,CAAA,EAAI,EAEpDhB,EAAQ,IAAI,CAACiB,OAAO,GAErB,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACC,KAAK,CAAGT,EAAOK,KAAK,CAACC,QAAQ,CAAC,IAAI,CAACI,GAAG,CAAE,CAAA,EAAI,EAEpEV,EAAOW,uBAAuB,CAAGX,EAAOY,kBAAkB,CAC1DZ,EAAOY,kBAAkB,CAAGZ,EAAOa,uBAAuB,CAE1D,IAAI,CAACC,OAAO,CAAG,IAAI,CAACC,QAAQ,EAAI,IAAI,CAACA,QAAQ,CAAC,EAAE,CAChD,IAAI,CAACN,KAAK,CAAG,IAAI,CAACL,QAAQ,CACtBH,GAAWT,EAAS,IAAI,CAACwB,SAAS,GAClC,CAAA,IAAI,CAACC,KAAK,CAAG,IAAI,CAACD,SAAS,AAAD,EAG9B1B,EAAUO,QAAQ,CAACF,KAAK,CAAC,IAAI,CAAEC,WAC/B,IAAI,CAACG,KAAK,CAAGD,EAEb,IAAI,CAACW,KAAK,CAAG,IAAI,CAACD,OAAO,CACzB,IAAI,CAACM,OAAO,CAAG,IAAI,CAACC,QAAQ,EAAI,IAAI,CAACA,QAAQ,CAAC,EAAE,CAC5Cd,GAAWT,EAAS,IAAI,CAAC0B,QAAQ,GACjC,CAAA,IAAI,CAACD,KAAK,CAAG,IAAI,CAACC,QAAQ,AAAD,EAE7BlB,EAAOa,uBAAuB,CAAGb,EAAOY,kBAAkB,CAC1DZ,EAAOY,kBAAkB,CAAGZ,EAAOW,uBAAuB,CAG1DX,EAAOW,uBAAuB,CAAG,KAAK,EACtC,IAAIQ,EAAmBnB,EAAOoB,oBAAoB,GAElD9B,EAAUO,QAAQ,CAACF,KAAK,CAAC,IAAI,CAAEC,WAE/BI,EAAOqB,qBAAqB,CAACF,EACjC,EACA1B,EAAe1B,SAAS,CAACuD,QAAQ,CAAG,WAChC,IAAIrB,EAAU,IAAI,CAACD,MAAM,CAACE,KAAK,CAACC,KAAK,CACjCoB,EAAO,EAAE,CAiBb,OAfA,IAAI,CAACd,KAAK,CAAG,IAAI,CAACD,OAAO,CACrBP,GAAWT,EAAS,IAAI,CAAC0B,QAAQ,GACjC,CAAA,IAAI,CAACD,KAAK,CAAG,IAAI,CAACC,QAAQ,AAAD,EAEzB,IAAI,CAACM,QAAQ,EACbD,CAAAA,EAAOjC,EAAUgC,QAAQ,CAAC3B,KAAK,CAAC,IAAI,CAAEC,UAAS,EAGnD,IAAI,CAACa,KAAK,CAAG,IAAI,CAACL,QAAQ,CACtBH,GAAWT,EAAS,IAAI,CAACwB,SAAS,GAClC,CAAA,IAAI,CAACC,KAAK,CAAG,IAAI,CAACD,SAAS,AAAD,EAE1B,IAAI,CAACS,WAAW,EAChBF,CAAAA,EAAOA,EAAKG,MAAM,CAACpC,EAAUgC,QAAQ,CAAC3B,KAAK,CAAC,IAAI,CAAEC,WAAU,EAEzD2B,CACX,EACA9B,EAAe1B,SAAS,CAAC4D,OAAO,CAAG,WAC/B,OAAOnC,EAAS,IAAI,CAACkB,GAAG,GAAKlB,EAAS,IAAI,CAACe,IAAI,CACnD,EACOd,CACX,EAAEL,GAmBEwC,GACIlF,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAOD,MAAO/B,AANHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOhB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACQ,EAC/DI,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACIzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIoD,UAAU,uBAAyBC,OAAOrD,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAIAiD,EAAS,AAAC1D,IAA+E0D,MAAM,CAAEC,EAAO,AAAC3D,IAA+E2D,IAAI,CAM5LC,EAA+B,SAAUvC,CAAM,EAE/C,SAASuC,IACL,OAAOvC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAqEA,OAxEAgC,EAAsBK,EAAevC,GAerCuC,EAAclE,SAAS,CAAC8B,QAAQ,CAAG,WAE/B,IADIZ,EAkBAiD,EACAC,EAjBAnC,EAASoC,AADD,IAAI,CACGpC,MAAM,CACrBE,EAAQF,EAAOE,KAAK,CACpBmC,EAAiBrC,EAAOsC,OAAO,CAACC,QAAQ,CACxCC,EAAexC,EAAOsC,OAAO,CAACG,MAAM,CACpCC,EAAkB1C,EAAOsC,OAAO,CAACK,SAAS,CAC1CC,EAAeR,AANP,IAAI,CAMSE,OAAO,CAC5BO,EAAgBD,EAAaL,QAAQ,CACrCO,EAAYV,AARJ,IAAI,CAQMW,IAAI,EAAIX,AARlB,IAAI,CAQoBW,IAAI,CAACC,KAAK,CAC1CC,EAAoBjB,EAAKa,EACzBH,MAAAA,EAAyD,KAAK,EAAIA,EAAgBQ,SAAS,CAC3Fb,EACAO,EAAaI,KAAK,CAClBF,EACAV,AAdQ,IAAI,CAcNY,KAAK,CACXhD,EAAOgD,KAAK,EACZG,EAAO,OAIX,GADA,IAAI,CAACC,aAAa,CAACzD,KAAK,CAnBZ,IAAI,CAmBgBC,WAC5B,CAACwC,AApBO,IAAI,CAoBLrC,KAAK,CAAE,CACdoD,EAAO,UACP,IAAIE,EAAKjB,AAtBD,IAAI,CAsBGrB,QAAQ,EAAI,EAAE,CACzBuC,EAAeD,CAAE,CAAC,EAAE,CACpBE,EAAeF,CAAE,CAAC,EAAE,CACpBC,GAAgB,CAACpD,EAAMsD,UAAU,GACjCF,EAAaG,IAAI,CAAC,CACdC,KAAMT,CACV,GACIM,IACApB,EAAY,CACRwB,EAAGvB,AA/BP,IAAI,CA+BSuB,CAAC,CACVZ,KAAMX,AAhCV,IAAI,CAgCYW,IAAI,AACpB,EACAX,AAlCA,IAAI,CAkCEuB,CAAC,CAAGvB,AAlCV,IAAI,CAkCY7B,IAAI,CACpB6B,AAnCA,IAAI,CAmCEW,IAAI,CAAGX,AAnCb,IAAI,CAmCeW,IAAI,CAAGX,AAnC1B,IAAI,CAmC4BwB,OAAO,GAAK,KAAK,EACjD1B,EAAoBF,EAAKI,AApCzB,IAAI,CAoC2BK,MAAM,CAAGL,AApCxC,IAAI,CAoC0CK,MAAM,CAACS,SAAS,CAAG,KAAK,EAAGV,EAAeA,EAAaU,SAAS,CAAG,KAAK,EAAGN,EAAaI,KAAK,CAAEZ,AApC7I,IAAI,CAoC+IW,IAAI,CAAGX,AApC1J,IAAI,CAoC4JW,IAAI,CAACC,KAAK,CAAG,KAAK,EAAGZ,AApCrL,IAAI,CAoCuLY,KAAK,EAChMO,EAAaE,IAAI,CAAC,CACdC,KAAMxB,CACV,GACAH,EAxCA,IAAI,CAwCUI,IAG1B,CACA,AAA2B,OAA1BlD,CAAAA,EAAKmD,AA5CM,IAAI,CA4CJyB,SAAS,AAAD,GAAe5E,AAAO,KAAK,IAAZA,GAAyBA,CAAE,CAACkE,EAAK,CAACnD,EAAO8D,mBAAmB,CA5CnF,IAAI,EA6CpB,EACA7B,EAAclE,SAAS,CAACgG,OAAO,CAAG,WAO9B,OAJK3B,AAFO,IAAI,CAELtB,OAAO,GACdsB,AAHQ,IAAI,CAGNtB,OAAO,CAAGsB,AAHR,IAAI,CAGUyB,SAAS,CAC/BzB,AAJQ,IAAI,CAINyB,SAAS,CAAG,KAAK,GAEpBnE,EAAO3B,SAAS,CAACgG,OAAO,CAAC9F,IAAI,CAAC,IAAI,CAC7C,EACOgE,CACX,EAlH6DxC,GAmH7DsC,EAAOE,EAAclE,SAAS,CAAE,CAC5BqF,cAAeY,AApH0CvE,EAoHjB1B,SAAS,CAAC8B,QAAQ,AAC9D,GAgP6B,IAAIoE,EApMJ,CAEzBC,YAAa,CAAA,EAEbhB,UAAW,OAEXiB,UAAW,EACXC,WAAY,EAQZC,eAAgB,EAEhBC,eAAgB,CAAA,EAChBC,aAAc,GACdC,MAAO,CAAA,EACPC,aAAc,GACdC,aAAc,YAUdnC,SAAU,UAcVoC,OAAQ,CACJC,MAAO,CAEHC,cAAe,EAOfC,mBAAoB,EAEpBC,KAAM,CAAA,CACV,CACJ,CACJ,EA2IIC,EAAuHnI,EAAoB,KAC3IoI,EAA2IpI,EAAoBI,CAAC,CAAC+H,GAYjKE,GACIxI,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAOD,MAAO/B,AANHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOhB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACQ,EAC/DI,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACIzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIoD,UAAU,uBAAyBC,OAAOrD,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAKAqG,EAAO,AAAC9G,IAA+E8G,IAAI,CAE3FC,EAAmB,AAAC7G,IAA2IW,WAAW,CAAEmG,EAAkBD,EAAiBE,SAAS,CAAEC,EAAeH,EAAiBI,MAAM,CAAEC,EAAoBL,EAAiBM,WAAW,CAGlTC,EAAwB,AAACtH,IAA+E0D,MAAM,CAAE6D,EAAQ,AAACvH,IAA+EuH,KAAK,CAAEC,EAAsB,AAACxH,IAA+E2D,IAAI,CAezT8D,EAAgC,SAAUpG,CAAM,EAEhD,SAASoG,IACL,OAAOpG,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAkPA,OArPAsF,EAAuBY,EAAgBpG,GAkBvCoG,EAAe/H,SAAS,CAAC+F,mBAAmB,CAAG,SAAU1B,CAAK,EAC1D,IA0BID,EAzBAjC,EAAQF,AADC,IAAI,CACEE,KAAK,CACpB0C,EAAeR,EAAME,OAAO,CAC5ByD,EAAgB/F,AAHP,IAAI,CAGUsC,OAAO,CAC9B0D,EAAQhG,AAJC,IAAI,CAIEgG,KAAK,CACpB3F,EAAQL,AALC,IAAI,CAKEK,KAAK,CACpByE,EAAqBe,EAAoBE,EAAcpB,MAAM,EACzDoB,EAAcpB,MAAM,CAACC,KAAK,EAC1BmB,EAAcpB,MAAM,CAACC,KAAK,CAACE,kBAAkB,CAAE,GACnDmB,EAAYJ,EAAoBjD,EAAaqD,SAAS,CACtDF,EAAcE,SAAS,EACvBC,EAAc7F,EAAMC,QAAQ,CAACyF,EAAcI,SAAS,EAAI,EACxD,CAAA,GACAC,EAAclG,EAAMmG,QAAQ,CACxBhG,EAAMiG,GAAG,CAAGJ,EAAcA,EAC9B7B,EAAiBwB,EAAoBjD,EAAayB,cAAc,CAChE0B,EAAc1B,cAAc,EAC5BkC,EAAiBV,EAAoBjD,EAAa2D,cAAc,CAChER,EAAcQ,cAAc,CAC5B3D,EAAaI,KAAK,CAClBZ,EAAMW,IAAI,CAAGX,EAAMW,IAAI,CAACC,KAAK,CAAG,KAAK,EACrCZ,EAAMY,KAAK,EACXwD,EAAWX,EAAoBzD,EAAM5B,OAAO,CAC5C4B,EAAM3B,KAAK,EACXgG,EAAcZ,EAAoBzD,EAAMhC,QAAQ,CAChDgG,GAEJ,GAAI,AAAoB,UAApB,OAAOI,EACP,MAAO,CAAC,CAERpE,CAAAA,EAAMrC,KAAK,EACXsE,CAAAA,GAAkCS,CAAiB,EAEnD0B,EAAW,EACXA,EAAW,EAENA,GAAYnG,EAAMiG,GAAG,EAC1BE,CAAAA,EAAWnG,EAAMiG,GAAG,AAAD,EAEnBG,EAAc,EACdA,EAAc,EAETA,GAAepG,EAAMiG,GAAG,EAC7BG,CAAAA,EAAcpG,EAAMiG,GAAG,AAAD,EAEtBlE,CAAAA,EAAMnB,KAAK,CAAG,GAAKmB,EAAMnB,KAAK,CAAG+E,EAAMM,GAAG,AAAD,GACzCjC,CAAAA,EAAiB,CAAA,EAGjBjC,EAAMrB,QAAQ,EAAIqB,EAAMrB,QAAQ,CAAC,EAAE,GACnCoB,EAAY,CACRwB,EAAGvB,EAAMuB,CAAC,CACVZ,KAAMX,EAAMW,IAAI,AACpB,EACAX,EAAMuB,CAAC,CAAGvB,EAAM7B,IAAI,CACpB6B,EAAMW,IAAI,CAAGX,EAAMW,IAAI,CAAGX,EAAMwB,OAAO,GAAK,KAAK,EACjD2C,EAAiBV,EAAoBjD,EAAa2D,cAAc,CAAER,EAAcQ,cAAc,CAAE3D,EAAaI,KAAK,CAAEZ,EAAMW,IAAI,CAAGX,EAAMW,IAAI,CAACC,KAAK,CAAG,KAAK,EAAGZ,EAAMY,KAAK,EACvK2C,EAAsBvD,EAAOD,IAEjC,IAAIuE,EAAU,CACNtJ,EAAG6H,IAA8HlH,SAAS,CAAC4I,SAAS,CAAC,CAAC,CAC9I,IACAvE,EAAMnB,KAAK,CACXuF,EACH,CACT,CACY,IACApE,EAAMnB,KAAK,CACXwF,EACH,CAAC,CACVpC,EACA,EAQJ,MAPI,CAACnE,EAAMsD,UAAU,GACjBkD,EAAQE,MAAM,CAAGL,EACjBG,CAAO,CAAC,eAAe,CAAGrC,EACtB4B,GACAS,CAAAA,EAAQG,SAAS,CAAGZ,CAAQ,GAG7BS,CACX,EAOAZ,EAAe/H,SAAS,CAAC+I,aAAa,CAAG,SAAU1E,CAAK,EACpD,IACI2E,EAAiBlB,EAAoB7F,AAD5B,IAAI,CAC+BsC,OAAO,CAACyE,cAAc,CAAE,KACpE5D,EAAOf,EAAMyB,SAAS,EAAI7D,AAFjB,IAAI,CAEoBE,KAAK,CAAC8G,UAAU,CAAGD,EAChD,UAAY,MACf3E,CAAAA,EAAMyB,SAAS,EAChBzB,CAAAA,EAAMyB,SAAS,CAAG7D,AALT,IAAI,CAKYE,KAAK,CAAC+G,QAAQ,CAAC1F,IAAI,GACvC2F,QAAQ,CAAC,4BACTzD,IAAI,CAAC,CACN0D,OAAQ,EACZ,GACKC,GAAG,CAACpH,AAVA,IAAI,CAUGqH,KAAK,CAAA,EAEzBjF,EAAMyB,SAAS,CAACV,EAAK,CAAC,IAAI,CAACW,mBAAmB,CAAC1B,GACnD,EAMA0D,EAAe/H,SAAS,CAACuJ,gBAAgB,CAAG,WACxC,IAAIC,EAAUhC,EAAaxH,SAAS,CAC3BuJ,gBAAgB,CAAC3H,KAAK,CAAC,IAAI,CAChCC,WAEJ,OADA2H,EAAQC,MAAM,EAAID,EAAQE,KAAK,CAAG,EAC3BF,CACX,EAMAzB,EAAe/H,SAAS,CAAC2J,SAAS,CAAG,WACjC,IACIrB,EAAWrG,AADF,IAAI,CACKE,KAAK,CAACmG,QAAQ,CAEpC,IAAI,CAACsB,YAAY,CAAChI,KAAK,CAHV,IAAI,EAKjB,IAAI,CAACiI,cAAc,CAACjI,KAAK,CALZ,IAAI,CAKiBC,WAElC,IAAK,IAAIiI,EAAK,EAAG5I,EAAKe,AAPT,IAAI,CAOY8H,MAAM,CAAED,EAAK5I,EAAG8I,MAAM,CAAEF,IAAM,CACvD,IAAIzF,EAAQnD,CAAE,CAAC4I,EAAG,CACdG,EAAa5F,EAAM4F,UAAU,CAC7B3E,EAAKjB,EAAM6F,SAAS,CACpBA,EAAY5E,AAAO,KAAK,IAAZA,EAAgB,CAAC,EAAIA,EACjC6E,EAAa9F,EAAM8F,UAAU,AACjC9F,CAAAA,EAAMnB,KAAK,CAAGgH,EAAUE,CAAC,EAAI,EAC7BF,EAAUE,CAAC,CAAG/F,EAAMnB,KAAK,CAAG+G,EAAa,EACrCE,IACI7B,EACA6B,CAAU,CAAC,EAAE,CAAGlI,AAjBf,IAAI,CAiBkBgG,KAAK,CAACM,GAAG,CAAGlE,EAAMnB,KAAK,CAG9CiH,CAAU,CAAC,EAAE,CAAG9F,EAAMnB,KAAK,CAGvC,CACAjB,AAxBa,IAAI,CAwBVoI,aAAa,CAACZ,MAAM,EAAIxH,AAxBlB,IAAI,CAwBqBoI,aAAa,CAACX,KAAK,CAAG,CAChE,EAMA3B,EAAe/H,SAAS,CAACsK,UAAU,CAAG,WAClC,IAMIpF,EACAb,EACAU,EAPA5C,EAAQF,AADC,IAAI,CACEE,KAAK,CACpBoI,EAActI,AAFL,IAAI,CAEQ8H,MAAM,CAACC,MAAM,CAClC1F,EAAiBrC,AAHR,IAAI,CAGWuC,QAAQ,CAAGvC,AAH1B,IAAI,CAG6BsC,OAAO,CAACC,QAAQ,CAC1DG,EAAkB1C,AAJT,IAAI,CAIYsC,OAAO,CAACK,SAAS,CAC1C4F,EAAI,EAMR,IAFA,IAAI,CAACC,gBAAgB,CAAC7I,KAAK,CATd,IAAI,CASmBC,WAE7B2I,EAAID,GAAa,CAEpB,IAAIrJ,EAAKmD,AADTA,CAAAA,EAAQpC,AAZC,IAAI,CAYE8H,MAAM,CAACS,EAAE,AAAD,EACRxH,QAAQ,EAAI,EAAE,CACzBuC,EAAerE,CAAE,CAAC,EAAE,CACpBsE,EAAetE,CAAE,CAAC,EAAE,CACxBe,AAhBS,IAAI,CAgBN8G,aAAa,CAAC1E,GACjBmB,IACAA,EAAakF,OAAO,CAACrG,KAAK,CAAGA,EAC7BmB,EAAa2D,QAAQ,CAAC,6BAEtB9E,EAAMyB,SAAS,EACfzB,CAAAA,EAAMyB,SAAS,CAAC4E,OAAO,CAACrG,KAAK,CAAGA,CAAI,EAEpCkB,IACAR,EAAYV,EAAMW,IAAI,EAAIX,EAAMW,IAAI,CAACC,KAAK,CAC1CC,EAAoB4C,EAAoBzD,EAAME,OAAO,CAACC,QAAQ,CAAEG,MAAAA,EAAyD,KAAK,EAAIA,EAAgBQ,SAAS,CAAEb,EAAgBD,EAAME,OAAO,CAACU,KAAK,CAAEF,EAAWV,EAAMY,KAAK,CAAEhD,AA1BrN,IAAI,CA0BwNgD,KAAK,EACjO9C,EAAMsD,UAAU,EACjBF,EAAaG,IAAI,CAAC,CACdC,KAAMT,CACV,GAEJK,EAAa4D,QAAQ,CAAC,4BAE1BqB,GACJ,CACJ,EAgBAzC,EAAe/H,SAAS,CAAC2K,YAAY,CAAG,SAAUtG,CAAK,CAAErC,CAAK,EAC1D,IAAI2I,EAAehJ,EAAO3B,SAAS,CAAC2K,YAAY,CAAC/I,KAAK,CAAC,IAAI,CACvDC,WAIJ,MAHc,UAAVG,GACA,OAAO2I,EAAahF,IAAI,CAErBgF,CACX,EAKA5C,EAAe/H,SAAS,CAAC4J,YAAY,CAAG,WACpCpC,EAAaxH,SAAS,CAAC2J,SAAS,CAAC/H,KAAK,CAAC,IAAI,EAC3C8F,EAAkB1H,SAAS,CAAC4K,oBAAoB,CAAChJ,KAAK,CAAC,IAAI,CAC/D,EAMAmG,EAAe8C,cAAc,CAAGhD,EAAMP,EAAgBuD,cAAc,CAAE3E,GAC/D6B,CACX,EAAET,GACFM,EAAsBG,EAAe/H,SAAS,CAAE,CAC5C8K,SAAUtD,EAAaxH,SAAS,CAAC8K,QAAQ,CACzCC,UAAW3D,EACX4D,YAAaxD,EAAaxH,SAAS,CAACgL,WAAW,CAC/C1J,WAhiBuD4C,EAiiBvDuG,iBAAkBnD,EAAgBtH,SAAS,CAACsK,UAAU,CACtDW,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DpB,eAAgBvC,EAAgBtH,SAAS,CAAC2J,SAAS,AACvD,GACAnJ,IAA0I0K,kBAAkB,CAAC,WAAYnD,GAa5I,IAAI3H,EAAiBE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,GAET"}