!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").Templating):"function"==typeof define&&define.amd?define("highcharts/modules/parallel-coordinates",[["highcharts/highcharts"],["highcharts/highcharts","Templating"]],e):"object"==typeof exports?exports["highcharts/modules/parallel-coordinates"]=e(require("highcharts"),require("highcharts").Templating):t.Highcharts=e(t.Highcharts,t.Highcharts.Templating)}(this,function(t,e){return function(){"use strict";var i,r,a,o={944:function(e){e.exports=t},984:function(t){t.exports=e}},s={};function n(t){var e=s[t];if(void 0!==e)return e.exports;var i=s[t]={exports:{}};return o[t](i,i.exports,n),i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var l={};n.d(l,{default:function(){return F}});var h=n(944),p=n.n(h),c={chart:{parallelCoordinates:!1,parallelAxes:{lineWidth:1,title:{text:"",reserveSpace:!1},labels:{x:0,y:4,align:"center",reserveSpace:!1},offset:0}},xAxis:{lineWidth:0,tickLength:0,opposite:!0,type:"category"}},u=function(t,e,i){if(i||2==arguments.length)for(var r,a=0,o=e.length;a<o;a++)!r&&a in e||(r||(r=Array.prototype.slice.call(e,0,a)),r[a]=e[a]);return t.concat(r||Array.prototype.slice.call(e))},d=p().addEvent,f=p().arrayMax,v=p().arrayMin,x=p().isNumber,g=p().merge,y=p().pick,m=function(){function t(t){this.axis=t}return t.prototype.setPosition=function(t,e){var i=this.axis,r=i.chart,a=((this.position||0)+.5)/(r.parallelInfo.counter+1);r.polar?e.angle=360*a:(e[t[0]]=100*a+"%",i[t[1]]=e[t[1]]=0,i[t[2]]=e[t[2]]=null,i[t[3]]=e[t[3]]=null)},t}();!function(t){function e(t){var e=this.chart,i=this.parallelCoordinates,r=["left","width","height","top"];if(e.hasParallelCoordinates){if(e.inverted&&(r=r.reverse()),this.isXAxis)this.options=g(this.options,c.xAxis,t.userOptions);else{var a=e.yAxis.indexOf(this);this.options=g(this.options,this.chart.options.chart.parallelAxes,t.userOptions),i.position=y(i.position,a>=0?a:e.yAxis.length),i.setPosition(r,this.options)}}}function i(t){var e=this.chart,i=this.parallelCoordinates;if(i&&e&&e.hasParallelCoordinates&&!this.isXAxis){var r=i.position,a=[];this.series.forEach(function(t){t.visible&&x(r)&&(a=(t.pointArrayMap||["y"]).reduce(function(e,i){var a,o;return u(u([],e,!0),[null!==(o=null===(a=t.getColumn(i))||void 0===a?void 0:a[r])&&void 0!==o?o:null],!1)},a))}),a=a.filter(x),this.dataMin=v(a),this.dataMax=f(a),t.preventDefault()}}function r(){this.parallelCoordinates||(this.parallelCoordinates=new m(this))}t.compose=function(t){t.keepProps.includes("parallel")||(t.keepProps.push("parallel"),d(t,"init",r),d(t,"afterSetOptions",e),d(t,"getSeriesExtremes",i))}}(i||(i={}));var A=i,P=n(984),b=n.n(P),C=p().composed,I=b().format,X=p().addEvent,M=p().defined,T=p().erase,O=p().extend,E=p().insertItem,L=p().isArray,N=p().isNumber,S=p().pushUnique;!function(t){function e(){var t,e,i=this.chart,r=this.points,a=r&&r.length,o=Number.MAX_VALUE;if(this.chart.hasParallelCoordinates){for(var s=0;s<a;s++)M((e=r[s]).y)?(i.polar?e.plotX=i.yAxis[s].angleRad||0:i.inverted?e.plotX=i.plotHeight-i.yAxis[s].top+i.plotTop:e.plotX=i.yAxis[s].left-i.plotLeft,e.clientX=e.plotX,e.plotY=i.yAxis[s].translate(e.y,!1,!0,void 0,!0),N(e.high)&&(e.plotHigh=i.yAxis[s].translate(e.high,!1,!0,void 0,!0)),void 0!==t&&(o=Math.min(o,Math.abs(e.plotX-t))),t=e.plotX,e.isInside=i.isInsidePlot(e.plotX,e.plotY,{inverted:i.inverted})):e.isNull=!0;this.closestPointRangePx=o}}function i(t){var e=this.chart;if(e.hasParallelCoordinates){for(var i=0,r=e.axes;i<r.length;i++){var a=r[i];E(this,a.series),a.isDirty=!0}this.xAxis=e.xAxis[0],this.yAxis=e.yAxis[0],t.preventDefault()}}function r(){var t=this.chart;if(t.hasParallelCoordinates)for(var e=0,i=t.axes||[];e<i.length;e++){var r=i[e];r&&r.series&&(T(r.series,this),r.isDirty=r.forceRedraw=!0)}}function a(){var t,e,i,r,a,o,s=this.chart;if(null==s?void 0:s.hasParallelCoordinates)for(var n=0,l=this.points;n<l.length;n++){var h=l[n],p=s.yAxis[h.x||0],c=p.options,u=null!==(t=c.tooltipValueFormat)&&void 0!==t?t:c.labels.format,d=void 0;d=u?I(u,O(h,{value:h.y}),s):p.dateTime?s.time.dateFormat(s.time.resolveDTLFormat((null===(e=c.dateTimeLabelFormats)||void 0===e?void 0:e[(null===(i=p.tickPositions.info)||void 0===i?void 0:i.unitName)||"year"])||"").main,null!==(r=h.y)&&void 0!==r?r:void 0):L(c.categories)?c.categories[null!==(a=h.y)&&void 0!==a?a:-1]:String(null!==(o=h.y)&&void 0!==o?o:""),h.formattedValue=d}}t.compose=function(t){S(C,"ParallelSeries")&&(X(t,"afterTranslate",e,{order:1}),X(t,"bindAxes",i),X(t,"destroy",r),X(t,"afterGeneratePoints",a))}}(r||(r={}));var j=r,k=p().addEvent,q=p().defined,D=p().merge,H=p().splat,V=function(){function t(t){this.chart=t}return t.prototype.setParallelInfo=function(t){var e=this.chart||this,i=t.series;e.parallelInfo={counter:0};for(var r=0;r<i.length;r++){var a=i[r];a.data&&(e.parallelInfo.counter=Math.max(e.parallelInfo.counter,a.data.length-1))}},t}();!function(t){function e(t){var e=t.args[0],i=H(e.yAxis||{}),r=[],a=i.length;if(this.hasParallelCoordinates=e.chart&&e.chart.parallelCoordinates,this.hasParallelCoordinates){for(this.setParallelInfo(e);a<=this.parallelInfo.counter;a++)r.push({});e.legend||(e.legend={}),e.legend&&void 0===e.legend.enabled&&(e.legend.enabled=!1),D(!0,e,{boost:{seriesThreshold:Number.MAX_VALUE},plotOptions:{series:{boostThreshold:Number.MAX_VALUE}}}),e.yAxis=i.concat(r),e.xAxis=D(c.xAxis,H(e.xAxis||{})[0])}}function i(t){var e=t.options;if(e.chart&&(q(e.chart.parallelCoordinates)&&(this.hasParallelCoordinates=e.chart.parallelCoordinates),this.options.chart.parallelAxes=D(this.options.chart.parallelAxes,e.chart.parallelAxes)),this.hasParallelCoordinates){e.series&&this.setParallelInfo(e);for(var i=0,r=this.yAxis;i<r.length;i++)r[i].update({},!1)}}t.compose=function(t,r,a,o){A.compose(t),j.compose(o);var s=V.prototype,n=r.prototype;n.setParallelInfo||(n.setParallelInfo=s.setParallelInfo,k(r,"init",e),k(r,"update",i),D(!0,a.chart,c.chart))}}(a||(a={}));var _=a,w=p();_.compose(w.Axis,w.Chart,w.defaultOptions,w.Series);var F=p();return l.default}()});