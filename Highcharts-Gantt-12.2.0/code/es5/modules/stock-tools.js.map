{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/stock-tools\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Advanced Highcharts Stock tools\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Templating\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"AST\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/stock-tools\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Templating\"], [\"highcharts/highcharts\",\"Series\"], [\"highcharts/highcharts\",\"AST\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/stock-tools\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Templating\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"AST\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"AST\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__660__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 820:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ stock_tools_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructor\n             *\n             * */\n            function Additions(chart) {\n                this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        Additions.prototype.addUpdate = function (updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        };\n        /**\n         * @private\n         */\n        Additions.prototype.update = function (options, redraw) {\n            var _this = this;\n            this.updates.forEach(function (updateFn) {\n                updateFn.call(_this.chart, options, redraw);\n            });\n        };\n        return Additions;\n    }());\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es5/es-modules/Extensions/Annotations/NavigationBindingsUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define types for editable fields per annotation. There is no need to define\n * numbers, because they won't change their type to string.\n * @private\n */\nvar annotationsFieldsTypes = {\n    backgroundColor: 'string',\n    borderColor: 'string',\n    borderRadius: 'string',\n    color: 'string',\n    fill: 'string',\n    fontSize: 'string',\n    labels: 'string',\n    name: 'string',\n    stroke: 'string',\n    title: 'string'\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns the first xAxis or yAxis that was clicked with its value.\n *\n * @private\n *\n * @param {Array<Highcharts.PointerAxisCoordinateObject>} coords\n *        All the chart's x or y axes with a current pointer's axis value.\n *\n * @return {Highcharts.PointerAxisCoordinateObject}\n *         Object with a first found axis and its value that pointer\n *         is currently pointing.\n */\nfunction getAssignedAxis(coords) {\n    return coords.filter(function (coord) {\n        var extremes = coord.axis.getExtremes(),\n            axisMin = extremes.min,\n            axisMax = extremes.max, \n            // Correct axis edges when axis has series\n            // with pointRange (like column)\n            minPointOffset = pick(coord.axis.minPointOffset, 0);\n        return isNumber(axisMin) && isNumber(axisMax) &&\n            coord.value >= (axisMin - minPointOffset) &&\n            coord.value <= (axisMax + minPointOffset) &&\n            // Don't count navigator axis\n            !coord.axis.options.isInternal;\n    })[0]; // If the axes overlap, return the first axis that was found.\n}\n/**\n * Get field type according to value\n *\n * @private\n *\n * @param {'boolean'|'number'|'string'} value\n * Atomic type (one of: string, number, boolean)\n *\n * @return {'checkbox'|'number'|'text'}\n * Field type (one of: text, number, checkbox)\n */\nfunction getFieldType(key, value) {\n    var predefinedType = annotationsFieldsTypes[key];\n    var fieldType = typeof value;\n    if (defined(predefinedType)) {\n        fieldType = predefinedType;\n    }\n    return {\n        'string': 'text',\n        'number': 'number',\n        'boolean': 'checkbox'\n    }[fieldType];\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar NavigationBindingUtilities = {\n    annotationsFieldsTypes: annotationsFieldsTypes,\n    getAssignedAxis: getAssignedAxis,\n    getFieldType: getFieldType\n};\n/* harmony default export */ var NavigationBindingsUtilities = (NavigationBindingUtilities);\n\n;// ./code/es5/es-modules/Extensions/Annotations/NavigationBindingsDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar NavigationBindingsDefaults_getAssignedAxis = NavigationBindingsUtilities.getAssignedAxis;\n\nvar NavigationBindingsDefaults_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nvar lang = {\n    /**\n     * Configure the Popup strings in the chart. Requires the\n     * `annotations.js` or `annotations-advanced.js` module to be\n     * loaded.\n     * @since   7.0.0\n     * @product highcharts highstock\n     */\n    navigation: {\n        /**\n         * Translations for all field names used in popup.\n         *\n         * @product highcharts highstock\n         */\n        popup: {\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            shapeOptions: 'Shape options',\n            typeOptions: 'Details',\n            fill: 'Fill',\n            format: 'Text',\n            strokeWidth: 'Line width',\n            stroke: 'Line color',\n            title: 'Title',\n            name: 'Name',\n            labelOptions: 'Label options',\n            labels: 'Labels',\n            backgroundColor: 'Background color',\n            backgroundColors: 'Background colors',\n            borderColor: 'Border color',\n            borderRadius: 'Border radius',\n            borderWidth: 'Border width',\n            style: 'Style',\n            padding: 'Padding',\n            fontSize: 'Font size',\n            color: 'Color',\n            height: 'Height',\n            shapes: 'Shape options'\n        }\n    }\n};\n/**\n * @optionparent navigation\n * @product      highcharts highstock\n */\nvar navigation = {\n    /**\n     * A CSS class name where all bindings will be attached to. Multiple\n     * charts on the same page should have separate class names to prevent\n     * duplicating events.\n     *\n     * Default value of versions < 7.0.4 `highcharts-bindings-wrapper`\n     *\n     * @since     7.0.0\n     * @type      {string}\n     */\n    bindingsClassName: 'highcharts-bindings-container',\n    /**\n     * Bindings definitions for custom HTML buttons. Each binding implements\n     * simple event-driven interface:\n     *\n     * - `className`: classname used to bind event to\n     *\n     * - `init`: initial event, fired on button click\n     *\n     * - `start`: fired on first click on a chart\n     *\n     * - `steps`: array of sequential events fired one after another on each\n     *   of users clicks\n     *\n     * - `end`: last event to be called after last step event\n     *\n     * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>|*}\n     *\n     * @sample {highstock} stock/stocktools/stocktools-thresholds\n     *               Custom bindings\n     * @sample {highcharts} highcharts/annotations/bindings/\n     *               Simple binding\n     * @sample {highcharts} highcharts/annotations/bindings-custom-annotation/\n     *               Custom annotation binding\n     *\n     * @since        7.0.0\n     * @requires     modules/annotations\n     * @product      highcharts highstock\n     */\n    bindings: {\n        /**\n         * A circle annotation bindings. Includes `start` and one event in\n         * `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-circle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        circleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-circle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis),\n                    navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(merge({\n                    langKey: 'circle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'circle',\n                            point: {\n                                x: coordsX.value,\n                                y: coordsY.value,\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index\n                            },\n                            r: 5\n                        }]\n                }, navigation.annotationsOptions, navigation.bindings.circleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    var shapes = annotation.options.shapes,\n                        mockPointOpts = ((shapes && shapes[0] && shapes[0].point) ||\n                            {});\n                    var distance;\n                    if (NavigationBindingsDefaults_isNumber(mockPointOpts.xAxis) &&\n                        NavigationBindingsDefaults_isNumber(mockPointOpts.yAxis)) {\n                        var inverted = this.chart.inverted,\n                            x = this.chart.xAxis[mockPointOpts.xAxis]\n                                .toPixels(mockPointOpts.x),\n                            y = this.chart.yAxis[mockPointOpts.yAxis]\n                                .toPixels(mockPointOpts.y);\n                        distance = Math.max(Math.sqrt(Math.pow(inverted ? y - e.chartX : x - e.chartX, 2) +\n                            Math.pow(inverted ? x - e.chartY : y - e.chartY, 2)), 5);\n                    }\n                    annotation.update({\n                        shapes: [{\n                                r: distance\n                            }]\n                    });\n                }\n            ]\n        },\n        /**\n         * A ellipse annotation bindings. Includes `start` and two events in\n         * `steps` array. First updates the second point, responsible for a\n         * rx width, and second updates the ry width.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-ellipse-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        ellipseAnnotation: {\n            className: 'highcharts-ellipse-annotation',\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis),\n                    navigation = this.chart.options.navigation;\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(merge({\n                    langKey: 'ellipse',\n                    type: 'basicAnnotation',\n                    shapes: [\n                        {\n                            type: 'ellipse',\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }, {\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }],\n                            ry: 1\n                        }\n                    ]\n                }, navigation.annotationsOptions, navigation.bindings.ellipseAnnotation\n                    .annotationsOptions));\n            },\n            steps: [\n                function (e, annotation) {\n                    var target = annotation.shapes[0],\n                        position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                },\n                function (e, annotation) {\n                    var target = annotation.shapes[0],\n                        position = target.getAbsolutePosition(target.points[0]),\n                        position2 = target.getAbsolutePosition(target.points[1]),\n                        newR = target.getDistanceFromLine(position,\n                        position2,\n                        e.chartX,\n                        e.chartY),\n                        yAxis = target.getYAxis(),\n                        newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            ]\n        },\n        /**\n         * A rectangle annotation bindings. Includes `start` and one event\n         * in `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-rectangle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        rectangleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-rectangle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                var x = coordsX.value,\n                    y = coordsY.value,\n                    xAxis = coordsX.axis.index,\n                    yAxis = coordsY.axis.index,\n                    navigation = this.chart.options.navigation;\n                return this.chart.addAnnotation(merge({\n                    langKey: 'rectangle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'path',\n                            points: [\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { command: 'Z' }\n                            ]\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .rectangleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    var _a;\n                    var shapes = annotation.options.shapes,\n                        points = ((shapes && shapes[0] && shapes[0].points) ||\n                            []),\n                        coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                        coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                        coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                    if (coordsX && coordsY) {\n                        var x = coordsX.value,\n                            y = coordsY.value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        annotation.update({\n                            shapes: [{\n                                    points: points\n                                }]\n                        });\n                    }\n                }\n            ]\n        },\n        /**\n         * A label annotation bindings. Includes `start` event only.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-label-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        labelAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-label-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis),\n                    navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(merge({\n                    langKey: 'label',\n                    type: 'basicAnnotation',\n                    labelOptions: {\n                        format: '{y:.2f}',\n                        overflow: 'none',\n                        crop: true\n                    },\n                    labels: [{\n                            point: {\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index,\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .labelAnnotation\n                    .annotationsOptions));\n            }\n        }\n    },\n    /**\n     * Path where Highcharts will look for icons. Change this to use icons\n     * from a different server.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/12.2.0/gfx/stock-icons/\n     * @since     7.1.3\n     * @apioption navigation.iconsURL\n     */\n    /**\n     * A `showPopup` event. Fired when selecting for example an annotation.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.showPopup\n     */\n    /**\n     * A `closePopup` event. Fired when Popup should be hidden, for example\n     * when clicking on an annotation again.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.closePopup\n     */\n    /**\n     * Event fired on a button click.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.selectButton\n     */\n    /**\n     * Event fired when button state should change, for example after\n     * adding an annotation.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.deselectButton\n     */\n    /**\n     * Events to communicate between Stock Tools and custom GUI.\n     *\n     * @since        7.0.0\n     * @product      highcharts highstock\n     * @optionparent navigation.events\n     */\n    events: {},\n    /**\n     * Additional options to be merged into all annotations.\n     *\n     * @sample stock/stocktools/navigation-annotation-options\n     *         Set red color of all line annotations\n     *\n     * @type      {Highcharts.AnnotationsOptions}\n     * @extends   annotations\n     * @exclude   crookedLine, elliottWave, fibonacci, infinityLine,\n     *            measure, pitchfork, tunnel, verticalLine, basicAnnotation\n     * @requires     modules/annotations\n     * @apioption navigation.annotationsOptions\n     */\n    annotationsOptions: {\n        animation: {\n            defer: 0\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar NavigationBindingDefaults = {\n    lang: lang,\n    navigation: navigation\n};\n/* harmony default export */ var NavigationBindingsDefaults = (NavigationBindingDefaults);\n\n;// ./code/es5/es-modules/Extensions/Annotations/NavigationBindings.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n\nvar format = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default()).format;\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\nvar NavigationBindings_getAssignedAxis = NavigationBindingsUtilities.getAssignedAxis, NavigationBindings_getFieldType = NavigationBindingsUtilities.getFieldType;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, attr = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).attr, NavigationBindings_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, isFunction = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFunction, NavigationBindings_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, NavigationBindings_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, NavigationBindings_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * IE 9-11 polyfill for Element.closest():\n * @private\n */\nfunction closestPolyfill(el, s) {\n    var ElementProto = win.Element.prototype,\n        elementMatches = ElementProto.matches ||\n            ElementProto.msMatchesSelector ||\n            ElementProto.webkitMatchesSelector;\n    var ret = null;\n    if (ElementProto.closest) {\n        ret = ElementProto.closest.call(el, s);\n    }\n    else {\n        do {\n            if (elementMatches.call(el, s)) {\n                return el;\n            }\n            el = el.parentElement || el.parentNode;\n        } while (el !== null && el.nodeType === 1);\n    }\n    return ret;\n}\n/**\n * @private\n */\nfunction onAnnotationRemove() {\n    if (this.chart.navigationBindings) {\n        this.chart.navigationBindings.deselectAnnotation();\n    }\n}\n/**\n * @private\n */\nfunction onChartDestroy() {\n    if (this.navigationBindings) {\n        this.navigationBindings.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartLoad() {\n    var options = this.options;\n    if (options && options.navigation && options.navigation.bindings) {\n        this.navigationBindings = new NavigationBindings(this, options.navigation);\n        this.navigationBindings.initEvents();\n        this.navigationBindings.initUpdate();\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    var navigationBindings = this.navigationBindings,\n        disabledClassName = 'highcharts-disabled-btn';\n    if (this && navigationBindings) {\n        // Check if the buttons should be enabled/disabled based on\n        // visible series.\n        var buttonsEnabled_1 = false;\n        this.series.forEach(function (series) {\n            if (!series.options.isInternal && series.visible) {\n                buttonsEnabled_1 = true;\n            }\n        });\n        if (this.navigationBindings &&\n            this.navigationBindings.container &&\n            this.navigationBindings.container[0]) {\n            var container_1 = this.navigationBindings.container[0];\n            objectEach(navigationBindings.boundClassNames, function (value, key) {\n                // Get the HTML element corresponding to the className taken\n                // from StockToolsBindings.\n                var buttonNode = container_1.querySelectorAll('.' + key);\n                if (buttonNode) {\n                    for (var i = 0; i < buttonNode.length; i++) {\n                        var button = buttonNode[i],\n                            cls = button.className;\n                        if (value.noDataState === 'normal') {\n                            // If button has noDataState: 'normal', and has\n                            // disabledClassName, remove this className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                        else if (!buttonsEnabled_1) {\n                            if (cls.indexOf(disabledClassName) === -1) {\n                                button.className += ' ' + disabledClassName;\n                            }\n                        }\n                        else {\n                            // Enable all buttons by deleting the className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                    }\n                }\n            });\n        }\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsClosePopup() {\n    this.deselectAnnotation();\n}\n/**\n * @private\n */\nfunction onNavigationBindingsDeselectButton() {\n    this.selectedButtonElement = null;\n}\n/**\n * Show edit-annotation form:\n * @private\n */\nfunction selectableAnnotation(annotationType) {\n    var originalClick = annotationType.prototype.defaultOptions.events &&\n            annotationType.prototype.defaultOptions.events.click;\n    /**\n     * Select and show popup\n     * @private\n     */\n    function selectAndShowPopup(eventArguments) {\n        var annotation = this,\n            navigation = annotation.chart.navigationBindings,\n            prevAnnotation = navigation.activeAnnotation;\n        if (originalClick) {\n            originalClick.call(annotation, eventArguments);\n        }\n        if (prevAnnotation !== annotation) {\n            // Select current:\n            navigation.deselectAnnotation();\n            navigation.activeAnnotation = annotation;\n            annotation.setControlPointsVisibility(true);\n            fireEvent(navigation, 'showPopup', {\n                annotation: annotation,\n                formType: 'annotation-toolbar',\n                options: navigation.annotationToFields(annotation),\n                onSubmit: function (data) {\n                    if (data.actionType === 'remove') {\n                        navigation.activeAnnotation = false;\n                        navigation.chart.removeAnnotation(annotation);\n                    }\n                    else {\n                        var config = {};\n                        navigation.fieldsToOptions(data.fields, config);\n                        navigation.deselectAnnotation();\n                        var typeOptions = config.typeOptions;\n                        if (annotation.options.type === 'measure') {\n                            // Manually disable crooshars according to\n                            // stroke width of the shape:\n                            typeOptions.crosshairY.enabled = (typeOptions.crosshairY\n                                .strokeWidth !== 0);\n                            typeOptions.crosshairX.enabled = (typeOptions.crosshairX\n                                .strokeWidth !== 0);\n                        }\n                        annotation.update(config);\n                    }\n                }\n            });\n        }\n        else {\n            // Deselect current:\n            fireEvent(navigation, 'closePopup');\n        }\n        // Let bubble event to chart.click:\n        eventArguments.activeAnnotation = true;\n    }\n    // #18276, show popup on touchend, but not on touchmove\n    var touchStartX,\n        touchStartY;\n    /**\n     *\n     */\n    function saveCoords(e) {\n        touchStartX = e.touches[0].clientX;\n        touchStartY = e.touches[0].clientY;\n    }\n    /**\n     *\n     */\n    function checkForTouchmove(e) {\n        var hasMoved = touchStartX ? Math.sqrt(Math.pow(touchStartX - e.changedTouches[0].clientX, 2) +\n                Math.pow(touchStartY - e.changedTouches[0].clientY, 2)) >= 4 : false;\n        if (!hasMoved) {\n            selectAndShowPopup.call(this, e);\n        }\n    }\n    NavigationBindings_merge(true, annotationType.prototype.defaultOptions.events, {\n        click: selectAndShowPopup,\n        touchstart: saveCoords,\n        touchend: checkForTouchmove\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nvar NavigationBindings = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function NavigationBindings(chart, options) {\n        this.boundClassNames = void 0;\n        this.chart = chart;\n        this.options = options;\n        this.eventsToUnbind = [];\n        this.container =\n            this.chart.container.getElementsByClassName(this.options.bindingsClassName || '');\n        if (!this.container.length) {\n            this.container = doc.getElementsByClassName(this.options.bindingsClassName || '');\n        }\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    NavigationBindings.compose = function (AnnotationClass, ChartClass) {\n        if (pushUnique(composed, 'NavigationBindings')) {\n            addEvent(AnnotationClass, 'remove', onAnnotationRemove);\n            // Basic shapes:\n            selectableAnnotation(AnnotationClass);\n            // Advanced annotations:\n            objectEach(AnnotationClass.types, function (annotationType) {\n                selectableAnnotation(annotationType);\n            });\n            addEvent(ChartClass, 'destroy', onChartDestroy);\n            addEvent(ChartClass, 'load', onChartLoad);\n            addEvent(ChartClass, 'render', onChartRender);\n            addEvent(NavigationBindings, 'closePopup', onNavigationBindingsClosePopup);\n            addEvent(NavigationBindings, 'deselectButton', onNavigationBindingsDeselectButton);\n            setOptions(NavigationBindingsDefaults);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    NavigationBindings.prototype.getCoords = function (e) {\n        var _a;\n        var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e);\n        return [\n            coords && NavigationBindings_getAssignedAxis(coords.xAxis),\n            coords && NavigationBindings_getAssignedAxis(coords.yAxis)\n        ];\n    };\n    /**\n     * Init all events connected to NavigationBindings.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initEvents\n     */\n    NavigationBindings.prototype.initEvents = function () {\n        var navigation = this,\n            chart = navigation.chart,\n            bindingsContainer = navigation.container,\n            options = navigation.options;\n        // Shorthand object for getting events for buttons:\n        navigation.boundClassNames = {};\n        objectEach((options.bindings || {}), function (value) {\n            navigation.boundClassNames[value.className] = value;\n        });\n        // Handle multiple containers with the same class names:\n        [].forEach.call(bindingsContainer, function (subContainer) {\n            navigation.eventsToUnbind.push(addEvent(subContainer, 'click', function (event) {\n                var bindings = navigation.getButtonEvents(subContainer,\n                    event);\n                if (bindings &&\n                    (!bindings.button.classList\n                        .contains('highcharts-disabled-btn'))) {\n                    navigation.bindingsButtonClick(bindings.button, bindings.events, event);\n                }\n            }));\n        });\n        objectEach((options.events || {}), function (callback, eventName) {\n            if (isFunction(callback)) {\n                navigation.eventsToUnbind.push(addEvent(navigation, eventName, callback, { passive: false }));\n            }\n        });\n        navigation.eventsToUnbind.push(addEvent(chart.container, 'click', function (e) {\n            if (!chart.cancelClick &&\n                chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                navigation.bindingsChartClick(this, e);\n            }\n        }));\n        navigation.eventsToUnbind.push(addEvent(chart.container, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? 'touchmove' : 'mousemove', function (e) {\n            navigation.bindingsContainerMouseMove(this, e);\n        }, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? { passive: false } : void 0));\n    };\n    /**\n     * Common chart.update() delegation, shared between bindings and exporting.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initUpdate\n     */\n    NavigationBindings.prototype.initUpdate = function () {\n        var navigation = this;\n        Chart_ChartNavigationComposition\n            .compose(this.chart).navigation\n            .addUpdate(function (options) {\n            navigation.update(options);\n        });\n    };\n    /**\n     * Hook for click on a button, method selects/unselects buttons,\n     * then calls `bindings.init` callback.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsButtonClick\n     *\n     * @param {Highcharts.HTMLDOMElement} [button]\n     *        Clicked button\n     *\n     * @param {Object} events\n     *        Events passed down from bindings (`init`, `start`, `step`, `end`)\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event\n     */\n    NavigationBindings.prototype.bindingsButtonClick = function (button, events, clickEvent) {\n        var navigation = this,\n            chart = navigation.chart,\n            svgContainer = chart.renderer.boxWrapper;\n        var shouldEventBeFired = true;\n        if (navigation.selectedButtonElement) {\n            if (navigation.selectedButtonElement.classList === button.classList) {\n                shouldEventBeFired = false;\n            }\n            fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n            if (navigation.nextEvent) {\n                // Remove in-progress annotations adders:\n                if (navigation.currentUserDetails &&\n                    navigation.currentUserDetails.coll === 'annotations') {\n                    chart.removeAnnotation(navigation.currentUserDetails);\n                }\n                navigation.mouseMoveEvent = navigation.nextEvent = false;\n            }\n        }\n        if (shouldEventBeFired) {\n            navigation.selectedButton = events;\n            navigation.selectedButtonElement = button;\n            fireEvent(navigation, 'selectButton', { button: button });\n            // Call \"init\" event, for example to open modal window\n            if (events.init) {\n                events.init.call(navigation, button, clickEvent);\n            }\n            if (events.start || events.steps) {\n                chart.renderer.boxWrapper.addClass('highcharts-draw-mode');\n            }\n        }\n        else {\n            chart.stockTools && button.classList.remove('highcharts-active');\n            svgContainer.removeClass('highcharts-draw-mode');\n            navigation.nextEvent = false;\n            navigation.mouseMoveEvent = false;\n            navigation.selectedButton = null;\n        }\n    };\n    /**\n     * Hook for click on a chart, first click on a chart calls `start` event,\n     * then on all subsequent clicks iterate over `steps` array.\n     * When finished, calls `end` event.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsChartClick\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that click was performed on.\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event.\n     */\n    NavigationBindings.prototype.bindingsChartClick = function (chart, clickEvent) {\n        chart = this.chart;\n        var navigation = this,\n            activeAnnotation = navigation.activeAnnotation,\n            selectedButton = navigation.selectedButton,\n            svgContainer = chart.renderer.boxWrapper;\n        if (activeAnnotation) {\n            // Click outside popups, should close them and deselect the\n            // annotation\n            if (!activeAnnotation.cancelClick && // #15729\n                !clickEvent.activeAnnotation &&\n                // Element could be removed in the child action, e.g. button\n                clickEvent.target.parentNode &&\n                // TO DO: Polyfill for IE11?\n                !closestPolyfill(clickEvent.target, '.highcharts-popup')) {\n                fireEvent(navigation, 'closePopup');\n            }\n            else if (activeAnnotation.cancelClick) {\n                // Reset cancelClick after the other event handlers have run\n                setTimeout(function () {\n                    activeAnnotation.cancelClick = false;\n                }, 0);\n            }\n        }\n        if (!selectedButton || !selectedButton.start) {\n            return;\n        }\n        if (!navigation.nextEvent) {\n            // Call init method:\n            navigation.currentUserDetails = selectedButton.start.call(navigation, clickEvent);\n            // If steps exists (e.g. Annotations), bind them:\n            if (navigation.currentUserDetails && selectedButton.steps) {\n                navigation.stepIndex = 0;\n                navigation.steps = true;\n                navigation.mouseMoveEvent = navigation.nextEvent =\n                    selectedButton.steps[navigation.stepIndex];\n            }\n            else {\n                fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                svgContainer.removeClass('highcharts-draw-mode');\n                navigation.steps = false;\n                navigation.selectedButton = null;\n                // First click is also the last one:\n                if (selectedButton.end) {\n                    selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                }\n            }\n        }\n        else {\n            navigation.nextEvent(clickEvent, navigation.currentUserDetails);\n            if (navigation.steps) {\n                navigation.stepIndex++;\n                if (selectedButton.steps[navigation.stepIndex]) {\n                    // If we have more steps, bind them one by one:\n                    navigation.mouseMoveEvent = navigation.nextEvent = selectedButton.steps[navigation.stepIndex];\n                }\n                else {\n                    fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                    svgContainer.removeClass('highcharts-draw-mode');\n                    // That was the last step, call end():\n                    if (selectedButton.end) {\n                        selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                    }\n                    navigation.nextEvent = false;\n                    navigation.mouseMoveEvent = false;\n                    navigation.selectedButton = null;\n                }\n            }\n        }\n    };\n    /**\n     * Hook for mouse move on a chart's container. It calls current step.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsContainerMouseMove\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Chart's container.\n     *\n     * @param {global.Event} moveEvent\n     *        Browser's move event.\n     */\n    NavigationBindings.prototype.bindingsContainerMouseMove = function (_container, moveEvent) {\n        if (this.mouseMoveEvent) {\n            this.mouseMoveEvent(moveEvent, this.currentUserDetails);\n        }\n    };\n    /**\n     * Translate fields (e.g. `params.period` or `marker.styles.color`) to\n     * Highcharts options object (e.g. `{ params: { period } }`).\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#fieldsToOptions<T>\n     *\n     * @param {Highcharts.Dictionary<string>} fields\n     *        Fields from popup form.\n     *\n     * @param {T} config\n     *        Default config to be modified.\n     *\n     * @return {T}\n     *         Modified config\n     */\n    NavigationBindings.prototype.fieldsToOptions = function (fields, config) {\n        objectEach(fields, function (value, field) {\n            var parsedValue = parseFloat(value),\n                path = field.split('.'),\n                pathLength = path.length - 1;\n            // If it's a number (not \"format\" options), parse it:\n            if (NavigationBindings_isNumber(parsedValue) &&\n                !value.match(/px|em/g) &&\n                !field.match(/format/g)) {\n                value = parsedValue;\n            }\n            // Remove values like 0\n            if (value !== 'undefined') {\n                var parent_1 = config;\n                path.forEach(function (name, index) {\n                    if (name !== '__proto__' && name !== 'constructor') {\n                        var nextName = NavigationBindings_pick(path[index + 1], '');\n                        if (pathLength === index) {\n                            // Last index, put value:\n                            parent_1[name] = value;\n                        }\n                        else if (!parent_1[name]) {\n                            // Create middle property:\n                            parent_1[name] = nextName.match(/\\d/g) ?\n                                [] :\n                                {};\n                            parent_1 = parent_1[name];\n                        }\n                        else {\n                            // Jump into next property\n                            parent_1 = parent_1[name];\n                        }\n                    }\n                });\n            }\n        });\n        return config;\n    };\n    /**\n     * Shorthand method to deselect an annotation.\n     *\n     * @function Highcharts.NavigationBindings#deselectAnnotation\n     */\n    NavigationBindings.prototype.deselectAnnotation = function () {\n        if (this.activeAnnotation) {\n            this.activeAnnotation.setControlPointsVisibility(false);\n            this.activeAnnotation = false;\n        }\n    };\n    /**\n     * Generates API config for popup in the same format as options for\n     * Annotation object.\n     *\n     * @function Highcharts.NavigationBindings#annotationToFields\n     *\n     * @param {Highcharts.Annotation} annotation\n     *        Annotations object\n     *\n     * @return {Highcharts.Dictionary<string>}\n     *         Annotation options to be displayed in popup box\n     */\n    NavigationBindings.prototype.annotationToFields = function (annotation) {\n        var options = annotation.options,\n            editables = NavigationBindings.annotationsEditable,\n            nestedEditables = editables.nestedOptions,\n            type = NavigationBindings_pick(options.type,\n            options.shapes && options.shapes[0] &&\n                options.shapes[0].type,\n            options.labels && options.labels[0] &&\n                options.labels[0].type, 'label'),\n            nonEditables = NavigationBindings.annotationsNonEditable[options.langKey] || [],\n            visualOptions = {\n                langKey: options.langKey,\n                type: type\n            };\n        /**\n         * Nested options traversing. Method goes down to the options and copies\n         * allowed options (with values) to new object, which is last parameter:\n         * \"parent\".\n         *\n         * @private\n         *\n         * @param {*} option\n         *        Atomic type or object/array\n         *\n         * @param {string} key\n         *        Option name, for example \"visible\" or \"x\", \"y\"\n         *\n         * @param {Object} parentEditables\n         *        Editables from NavigationBindings.annotationsEditable\n         *\n         * @param {Object} parent\n         *        Where new options will be assigned\n         */\n        function traverse(option, key, parentEditables, parent, parentKey) {\n            var nextParent;\n            if (parentEditables &&\n                NavigationBindings_defined(option) &&\n                nonEditables.indexOf(key) === -1 &&\n                ((parentEditables.indexOf &&\n                    parentEditables.indexOf(key)) >= 0 ||\n                    parentEditables[key] || // Nested array\n                    parentEditables === true // Simple array\n                )) {\n                // Roots:\n                if (isArray(option)) {\n                    parent[key] = [];\n                    option.forEach(function (arrayOption, i) {\n                        if (!isObject(arrayOption)) {\n                            // Simple arrays, e.g. [String, Number, Boolean]\n                            traverse(arrayOption, 0, nestedEditables[key], parent[key], key);\n                        }\n                        else {\n                            // Advanced arrays, e.g. [Object, Object]\n                            parent[key][i] = {};\n                            objectEach(arrayOption, function (nestedOption, nestedKey) {\n                                traverse(nestedOption, nestedKey, nestedEditables[key], parent[key][i], key);\n                            });\n                        }\n                    });\n                }\n                else if (isObject(option)) {\n                    nextParent = {};\n                    if (isArray(parent)) {\n                        parent.push(nextParent);\n                        nextParent[key] = {};\n                        nextParent = nextParent[key];\n                    }\n                    else {\n                        parent[key] = nextParent;\n                    }\n                    objectEach(option, function (nestedOption, nestedKey) {\n                        traverse(nestedOption, nestedKey, key === 0 ?\n                            parentEditables :\n                            nestedEditables[key], nextParent, key);\n                    });\n                }\n                else {\n                    // Leaf:\n                    if (key === 'format') {\n                        parent[key] = [\n                            format(option, annotation.labels[0].points[0]).toString(),\n                            'text'\n                        ];\n                    }\n                    else if (isArray(parent)) {\n                        parent.push([option, NavigationBindings_getFieldType(parentKey, option)]);\n                    }\n                    else {\n                        parent[key] = [option, NavigationBindings_getFieldType(key, option)];\n                    }\n                }\n            }\n        }\n        objectEach(options, function (option, key) {\n            if (key === 'typeOptions') {\n                visualOptions[key] = {};\n                objectEach(options[key], function (typeOption, typeKey) {\n                    traverse(typeOption, typeKey, nestedEditables, visualOptions[key], typeKey);\n                });\n            }\n            else {\n                traverse(option, key, editables[type], visualOptions, key);\n            }\n        });\n        return visualOptions;\n    };\n    /**\n     * Get all class names for all parents in the element. Iterates until finds\n     * main container.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getClickedClassNames\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     * Container that event is bound to.\n     *\n     * @param {global.Event} event\n     * Browser's event.\n     *\n     * @return {Array<Array<string, Highcharts.HTMLDOMElement>>}\n     * Array of class names with corresponding elements\n     */\n    NavigationBindings.prototype.getClickedClassNames = function (container, event) {\n        var element = event.target,\n            classNames = [],\n            elemClassName;\n        while (element && element.tagName) {\n            elemClassName = attr(element, 'class');\n            if (elemClassName) {\n                classNames = classNames.concat(elemClassName\n                    .split(' ')\n                    // eslint-disable-next-line no-loop-func\n                    .map(function (name) { return ([name, element]); }));\n            }\n            element = element.parentNode;\n            if (element === container) {\n                return classNames;\n            }\n        }\n        return classNames;\n    };\n    /**\n     * Get events bound to a button. It's a custom event delegation to find all\n     * events connected to the element.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getButtonEvents\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Container that event is bound to.\n     *\n     * @param {global.Event} event\n     *        Browser's event.\n     *\n     * @return {Object}\n     *         Object with events (init, start, steps, and end)\n     */\n    NavigationBindings.prototype.getButtonEvents = function (container, event) {\n        var navigation = this,\n            classNames = this.getClickedClassNames(container,\n            event);\n        var bindings;\n        classNames.forEach(function (className) {\n            if (navigation.boundClassNames[className[0]] && !bindings) {\n                bindings = {\n                    events: navigation.boundClassNames[className[0]],\n                    button: className[1]\n                };\n            }\n        });\n        return bindings;\n    };\n    /**\n     * Bindings are just events, so the whole update process is simply\n     * removing old events and adding new ones.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#update\n     */\n    NavigationBindings.prototype.update = function (options) {\n        this.options = NavigationBindings_merge(true, this.options, options);\n        this.removeEvents();\n        this.initEvents();\n    };\n    /**\n     * Remove all events created in the navigation.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#removeEvents\n     */\n    NavigationBindings.prototype.removeEvents = function () {\n        this.eventsToUnbind.forEach(function (unbinder) { return unbinder(); });\n    };\n    /**\n     * @private\n     * @function Highcharts.NavigationBindings#destroy\n     */\n    NavigationBindings.prototype.destroy = function () {\n        this.removeEvents();\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    // Define which options from annotations should show up in edit box:\n    NavigationBindings.annotationsEditable = {\n        // `typeOptions` are always available\n        // Nested and shared options:\n        nestedOptions: {\n            labelOptions: ['style', 'format', 'backgroundColor'],\n            labels: ['style'],\n            label: ['style'],\n            style: ['fontSize', 'color'],\n            background: ['fill', 'strokeWidth', 'stroke'],\n            innerBackground: ['fill', 'strokeWidth', 'stroke'],\n            outerBackground: ['fill', 'strokeWidth', 'stroke'],\n            shapeOptions: ['fill', 'strokeWidth', 'stroke'],\n            shapes: ['fill', 'strokeWidth', 'stroke'],\n            line: ['strokeWidth', 'stroke'],\n            backgroundColors: [true],\n            connector: ['fill', 'strokeWidth', 'stroke'],\n            crosshairX: ['strokeWidth', 'stroke'],\n            crosshairY: ['strokeWidth', 'stroke']\n        },\n        // Simple shapes:\n        circle: ['shapes'],\n        ellipse: ['shapes'],\n        verticalLine: [],\n        label: ['labelOptions'],\n        // Measure\n        measure: ['background', 'crosshairY', 'crosshairX'],\n        // Others:\n        fibonacci: [],\n        tunnel: ['background', 'line', 'height'],\n        pitchfork: ['innerBackground', 'outerBackground'],\n        rect: ['shapes'],\n        // Crooked lines, elliots, arrows etc:\n        crookedLine: [],\n        basicAnnotation: ['shapes', 'labelOptions']\n    };\n    // Define non editable fields per annotation, for example Rectangle inherits\n    // options from Measure, but crosshairs are not available\n    NavigationBindings.annotationsNonEditable = {\n        rectangle: ['crosshairX', 'crosshairY', 'labelOptions'],\n        ellipse: ['labelOptions'],\n        circle: ['labelOptions']\n    };\n    return NavigationBindings;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_NavigationBindings = (NavigationBindings);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A config object for navigation bindings in annotations.\n *\n * @interface Highcharts.NavigationBindingsOptionsObject\n */ /**\n* ClassName of the element for a binding.\n* @name Highcharts.NavigationBindingsOptionsObject#className\n* @type {string|undefined}\n*/ /**\n* Last event to be fired after last step event.\n* @name Highcharts.NavigationBindingsOptionsObject#end\n* @type {Function|undefined}\n*/ /**\n* Initial event, fired on a button click.\n* @name Highcharts.NavigationBindingsOptionsObject#init\n* @type {Function|undefined}\n*/ /**\n* Event fired on first click on a chart.\n* @name Highcharts.NavigationBindingsOptionsObject#start\n* @type {Function|undefined}\n*/ /**\n* Last event to be fired after last step event. Array of step events to be\n* called sequentially after each user click.\n* @name Highcharts.NavigationBindingsOptionsObject#steps\n* @type {Array<Function>|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default = /*#__PURE__*/__webpack_require__.n(highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_);\n;// ./code/es5/es-modules/Stock/StockTools/StockToolsUtilities.js\n/**\n *\n *  Events generator for Stock tools\n *\n *  (c) 2009-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar getOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getOptions;\n\nvar StockToolsUtilities_getAssignedAxis = NavigationBindingsUtilities.getAssignedAxis, StockToolsUtilities_getFieldType = NavigationBindingsUtilities.getFieldType;\n\n\nvar StockToolsUtilities_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, StockToolsUtilities_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, StockToolsUtilities_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @private\n */\nvar indicatorsWithAxes = [\n    'apo',\n    'ad',\n    'aroon',\n    'aroonoscillator',\n    'atr',\n    'ao',\n    'cci',\n    'chaikin',\n    'cmf',\n    'cmo',\n    'disparityindex',\n    'dmi',\n    'dpo',\n    'linearRegressionAngle',\n    'linearRegressionIntercept',\n    'linearRegressionSlope',\n    'klinger',\n    'macd',\n    'mfi',\n    'momentum',\n    'natr',\n    'obv',\n    'ppo',\n    'roc',\n    'rsi',\n    'slowstochastic',\n    'stochastic',\n    'trix',\n    'williamsr'\n];\n/**\n * @private\n */\nvar indicatorsWithVolume = [\n    'ad',\n    'cmf',\n    'klinger',\n    'mfi',\n    'obv',\n    'vbp',\n    'vwap'\n];\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Generates function which will add a flag series using modal in GUI.\n * Method fires an event \"showPopup\" with config:\n * `{type, options, callback}`.\n *\n * Example: NavigationBindings.utils.addFlagFromForm('url(...)') - will\n * generate function that shows modal in GUI.\n *\n * @private\n * @function bindingsUtils.addFlagFromForm\n *\n * @param {Highcharts.FlagsShapeValue} type\n *        Type of flag series, e.g. \"squarepin\"\n *\n * @return {Function}\n *         Callback to be used in `start` callback\n */\nfunction addFlagFromForm(type) {\n    return function (e) {\n        var navigation = this,\n            chart = navigation.chart,\n            toolbar = chart.stockTools,\n            point = attractToPoint(e,\n            chart);\n        if (!point) {\n            return;\n        }\n        var pointConfig = {\n                x: point.x,\n                y: point.y\n            };\n        var seriesOptions = {\n                type: 'flags',\n                onSeries: point.series.id,\n                shape: type,\n                data: [pointConfig],\n                xAxis: point.xAxis,\n                yAxis: point.yAxis,\n                point: {\n                    events: {\n                        click: function () {\n                            var point = this,\n            options = point.options;\n                        StockToolsUtilities_fireEvent(navigation, 'showPopup', {\n                            point: point,\n                            formType: 'annotation-toolbar',\n                            options: {\n                                langKey: 'flags',\n                                type: 'flags',\n                                title: [\n                                    options.title,\n                                    StockToolsUtilities_getFieldType('title', options.title)\n                                ],\n                                name: [\n                                    options.name,\n                                    StockToolsUtilities_getFieldType('name', options.name)\n                                ]\n                            },\n                            onSubmit: function (updated) {\n                                if (updated.actionType === 'remove') {\n                                    point.remove();\n                                }\n                                else {\n                                    point.update(navigation.fieldsToOptions(updated.fields, {}));\n                                }\n                            }\n                        });\n                    }\n                }\n            }\n        };\n        if (!toolbar || !toolbar.guiEnabled) {\n            chart.addSeries(seriesOptions);\n        }\n        StockToolsUtilities_fireEvent(navigation, 'showPopup', {\n            formType: 'flag',\n            // Enabled options:\n            options: {\n                langKey: 'flags',\n                type: 'flags',\n                title: ['A', StockToolsUtilities_getFieldType('label', 'A')],\n                name: ['Flag A', StockToolsUtilities_getFieldType('label', 'Flag A')]\n            },\n            // Callback on submit:\n            onSubmit: function (data) {\n                navigation.fieldsToOptions(data.fields, seriesOptions.data[0]);\n                chart.addSeries(seriesOptions);\n            }\n        });\n    };\n}\n/**\n * @private\n * @todo\n * Consider using getHoverData(), but always kdTree (columns?)\n */\nfunction attractToPoint(e, chart) {\n    var _a;\n    var coords = (_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e);\n    var coordsX,\n        coordsY,\n        distX = Number.MAX_VALUE,\n        closestPoint;\n    if (chart.navigationBindings && coords) {\n        coordsX = StockToolsUtilities_getAssignedAxis(coords.xAxis);\n        coordsY = StockToolsUtilities_getAssignedAxis(coords.yAxis);\n    }\n    // Exit if clicked out of axes area.\n    if (!coordsX || !coordsY) {\n        return;\n    }\n    var x = coordsX.value;\n    var y = coordsY.value;\n    // Search by 'x' but only in yAxis' series.\n    coordsY.axis.series.forEach(function (series) {\n        if (series.points) {\n            var point = series.searchPoint(e,\n                true);\n            if (point && distX > Math.abs(point.x - x)) {\n                distX = Math.abs(point.x - x);\n                closestPoint = point;\n            }\n        }\n    });\n    if (closestPoint && closestPoint.x && closestPoint.y) {\n        return {\n            x: closestPoint.x,\n            y: closestPoint.y,\n            below: y < closestPoint.y,\n            series: closestPoint.series,\n            xAxis: closestPoint.series.xAxis.index || 0,\n            yAxis: closestPoint.series.yAxis.index || 0\n        };\n    }\n}\n/**\n * Shorthand to check if given yAxis comes from navigator.\n *\n * @private\n * @function bindingsUtils.isNotNavigatorYAxis\n *\n * @param {Highcharts.Axis} axis\n * Axis to check.\n *\n * @return {boolean}\n * True, if axis comes from navigator.\n */\nfunction isNotNavigatorYAxis(axis) {\n    return axis.userOptions.className !== 'highcharts-navigator-yaxis';\n}\n/**\n * Check if any of the price indicators are enabled.\n * @private\n * @function bindingsUtils.isLastPriceEnabled\n *\n * @param {Array} series\n *        Array of series.\n *\n * @return {boolean}\n *         Tells which indicator is enabled.\n */\nfunction isPriceIndicatorEnabled(series) {\n    return series.some(function (s) { return s.lastVisiblePrice || s.lastPrice; });\n}\n/**\n * @private\n */\nfunction manageIndicators(data) {\n    var chart = this.chart,\n        seriesConfig = {\n            linkedTo: data.linkedTo,\n            type: data.type\n        };\n    var yAxis,\n        parentSeries,\n        defaultOptions,\n        series;\n    if (data.actionType === 'edit') {\n        this.fieldsToOptions(data.fields, seriesConfig);\n        series = chart.get(data.seriesId);\n        if (series) {\n            series.update(seriesConfig, false);\n        }\n    }\n    else if (data.actionType === 'remove') {\n        series = chart.get(data.seriesId);\n        if (series) {\n            yAxis = series.yAxis;\n            if (series.linkedSeries) {\n                series.linkedSeries.forEach(function (linkedSeries) {\n                    linkedSeries.remove(false);\n                });\n            }\n            series.remove(false);\n            if (indicatorsWithAxes.indexOf(series.type) >= 0) {\n                var removedYAxisProps = {\n                        height: yAxis.options.height,\n                        top: yAxis.options.top\n                    };\n                yAxis.remove(false);\n                this.resizeYAxes(removedYAxisProps);\n            }\n        }\n    }\n    else {\n        seriesConfig.id = uniqueKey();\n        this.fieldsToOptions(data.fields, seriesConfig);\n        parentSeries = chart.get(seriesConfig.linkedTo);\n        defaultOptions = getOptions().plotOptions;\n        // Make sure that indicator uses the SUM approx if SUM approx is used\n        // by parent series (#13950).\n        if (typeof parentSeries !== 'undefined' &&\n            parentSeries instanceof (highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default()) &&\n            parentSeries.getDGApproximation() === 'sum' &&\n            // If indicator has defined approx type, use it (e.g. \"ranges\")\n            !StockToolsUtilities_defined(defaultOptions && defaultOptions[seriesConfig.type] &&\n                defaultOptions.dataGrouping &&\n                defaultOptions.dataGrouping.approximation)) {\n            seriesConfig.dataGrouping = {\n                approximation: 'sum'\n            };\n        }\n        if (indicatorsWithAxes.indexOf(data.type) >= 0) {\n            yAxis = chart.addAxis({\n                id: uniqueKey(),\n                offset: 0,\n                opposite: true,\n                title: {\n                    text: ''\n                },\n                tickPixelInterval: 40,\n                showLastLabel: false,\n                labels: {\n                    align: 'left',\n                    y: -2\n                }\n            }, false, false);\n            seriesConfig.yAxis = yAxis.options.id;\n            this.resizeYAxes();\n        }\n        else {\n            seriesConfig.yAxis = chart.get(data.linkedTo).options.yAxis;\n        }\n        if (indicatorsWithVolume.indexOf(data.type) >= 0) {\n            seriesConfig.params.volumeSeriesID = chart.series.filter(function (series) {\n                return series.options.type === 'column';\n            })[0].options.id;\n        }\n        chart.addSeries(seriesConfig, false);\n    }\n    StockToolsUtilities_fireEvent(this, 'deselectButton', {\n        button: this.selectedButtonElement\n    });\n    chart.redraw();\n}\n/**\n * Update height for an annotation. Height is calculated as a difference\n * between last point in `typeOptions` and current position. It's a value,\n * not pixels height.\n *\n * @private\n * @function bindingsUtils.updateHeight\n *\n * @param {Highcharts.PointerEventObject} e\n *        normalized browser event\n *\n * @param {Highcharts.Annotation} annotation\n *        Annotation to be updated\n */\nfunction updateHeight(e, annotation) {\n    var options = annotation.options.typeOptions,\n        yAxis = StockToolsUtilities_isNumber(options.yAxis) && this.chart.yAxis[options.yAxis];\n    if (yAxis && options.points) {\n        annotation.update({\n            typeOptions: {\n                height: yAxis.toValue(e[yAxis.horiz ? 'chartX' : 'chartY']) -\n                    (options.points[1].y || 0)\n            }\n        });\n    }\n}\n/**\n * Update each point after specified index, most of the annotations use\n * this. For example crooked line: logic behind updating each point is the\n * same, only index changes when adding an annotation.\n *\n * Example: NavigationBindings.utils.updateNthPoint(1) - will generate\n * function that updates all consecutive points except point with index=0.\n *\n * @private\n * @function bindingsUtils.updateNthPoint\n *\n * @param {number} startIndex\n *        Index from which point should update\n *\n * @return {Function}\n *         Callback to be used in steps array\n */\nfunction updateNthPoint(startIndex) {\n    return function (e, annotation) {\n        var options = annotation.options.typeOptions,\n            xAxis = StockToolsUtilities_isNumber(options.xAxis) && this.chart.xAxis[options.xAxis],\n            yAxis = StockToolsUtilities_isNumber(options.yAxis) && this.chart.yAxis[options.yAxis];\n        if (xAxis && yAxis) {\n            options.points.forEach(function (point, index) {\n                if (index >= startIndex) {\n                    point.x = xAxis.toValue(e[xAxis.horiz ? 'chartX' : 'chartY']);\n                    point.y = yAxis.toValue(e[yAxis.horiz ? 'chartX' : 'chartY']);\n                }\n            });\n            annotation.update({\n                typeOptions: {\n                    points: options.points\n                }\n            });\n        }\n    };\n}\n/**\n * Update size of background (rect) in some annotations: Measure, Simple\n * Rect.\n *\n * @private\n * @function Highcharts.NavigationBindingsUtilsObject.updateRectSize\n *\n * @param {Highcharts.PointerEventObject} event\n * Normalized browser event\n *\n * @param {Highcharts.Annotation} annotation\n * Annotation to be updated\n */\nfunction updateRectSize(event, annotation) {\n    var chart = annotation.chart,\n        options = annotation.options.typeOptions,\n        xAxis = StockToolsUtilities_isNumber(options.xAxis) && chart.xAxis[options.xAxis],\n        yAxis = StockToolsUtilities_isNumber(options.yAxis) && chart.yAxis[options.yAxis];\n    if (xAxis && yAxis) {\n        var x = xAxis.toValue(event[xAxis.horiz ? 'chartX' : 'chartY']), y = yAxis.toValue(event[yAxis.horiz ? 'chartX' : 'chartY']), width = x - options.point.x, height = options.point.y - y;\n        annotation.update({\n            typeOptions: {\n                background: {\n                    width: chart.inverted ? height : width,\n                    height: chart.inverted ? width : height\n                }\n            }\n        });\n    }\n}\n/**\n * Compares two arrays of strings, checking their length and if corresponding\n * elements are equal.\n *\n * @param {string[]} a\n *        The first array to compare.\n * @param {string[]} b\n *        The second array to compare.\n * @return {boolean}\n *          Return `true` if the arrays are equal, otherwise `false`.\n */\nfunction shallowArraysEqual(a, b) {\n    if (!StockToolsUtilities_defined(a) || !StockToolsUtilities_defined(b)) {\n        return false;\n    }\n    if (a.length !== b.length) {\n        return false;\n    }\n    for (var i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar StockToolsUtilities = {\n    indicatorsWithAxes: indicatorsWithAxes,\n    indicatorsWithVolume: indicatorsWithVolume,\n    addFlagFromForm: addFlagFromForm,\n    attractToPoint: attractToPoint,\n    getAssignedAxis: StockToolsUtilities_getAssignedAxis,\n    isNotNavigatorYAxis: isNotNavigatorYAxis,\n    isPriceIndicatorEnabled: isPriceIndicatorEnabled,\n    manageIndicators: manageIndicators,\n    shallowArraysEqual: shallowArraysEqual,\n    updateHeight: updateHeight,\n    updateNthPoint: updateNthPoint,\n    updateRectSize: updateRectSize\n};\n/* harmony default export */ var StockTools_StockToolsUtilities = (StockToolsUtilities);\n\n;// ./code/es5/es-modules/Stock/StockTools/StockToolsBindings.js\n/**\n *\n *  Events generator for Stock tools\n *\n *  (c) 2009-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar StockToolsBindings_addFlagFromForm = StockTools_StockToolsUtilities.addFlagFromForm, StockToolsBindings_attractToPoint = StockTools_StockToolsUtilities.attractToPoint, StockToolsBindings_isNotNavigatorYAxis = StockTools_StockToolsUtilities.isNotNavigatorYAxis, StockToolsBindings_isPriceIndicatorEnabled = StockTools_StockToolsUtilities.isPriceIndicatorEnabled, StockToolsBindings_manageIndicators = StockTools_StockToolsUtilities.manageIndicators, StockToolsBindings_updateHeight = StockTools_StockToolsUtilities.updateHeight, StockToolsBindings_updateNthPoint = StockTools_StockToolsUtilities.updateNthPoint, StockToolsBindings_updateRectSize = StockTools_StockToolsUtilities.updateRectSize;\n\nvar StockToolsBindings_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, StockToolsBindings_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @sample {highstock} stock/stocktools/custom-stock-tools-bindings\n *         Custom stock tools bindings\n *\n * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>}\n * @since        7.0.0\n * @optionparent navigation.bindings\n */\nvar StockToolsBindings = {\n    // Line type annotations:\n    /**\n     * A segment annotation bindings. Includes `start` and one event in `steps`\n     * array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-segment\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    segment: {\n        /** @ignore-option */\n        className: 'highcharts-segment',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'segment',\n                    type: 'crookedLine',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }, {\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.segment.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A segment with an arrow annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-arrow-segment\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    arrowSegment: {\n        /** @ignore-option */\n        className: 'highcharts-arrow-segment',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'arrowSegment',\n                    type: 'crookedLine',\n                    typeOptions: {\n                        line: {\n                            markerEnd: 'arrow'\n                        },\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }, {\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.arrowSegment.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A ray annotation bindings. Includes `start` and one event in `steps`\n     * array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-ray\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    ray: {\n        /** @ignore-option */\n        className: 'highcharts-ray',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'ray',\n                    type: 'infinityLine',\n                    typeOptions: {\n                        type: 'ray',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }, {\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.ray.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A ray with an arrow annotation bindings. Includes `start` and one event\n     * in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-arrow-ray\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    arrowRay: {\n        /** @ignore-option */\n        className: 'highcharts-arrow-ray',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'arrowRay',\n                    type: 'infinityLine',\n                    typeOptions: {\n                        type: 'ray',\n                        line: {\n                            markerEnd: 'arrow'\n                        },\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }, {\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.arrowRay.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A line annotation. Includes `start` and one event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-infinity-line\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    infinityLine: {\n        /** @ignore-option */\n        className: 'highcharts-infinity-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'infinityLine',\n                    type: 'infinityLine',\n                    typeOptions: {\n                        type: 'line',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }, {\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.infinityLine.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A line with arrow annotation. Includes `start` and one event in `steps`\n     * array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-arrow-infinity-line\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    arrowInfinityLine: {\n        /** @ignore-option */\n        className: 'highcharts-arrow-infinity-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'arrowInfinityLine',\n                    type: 'infinityLine',\n                    typeOptions: {\n                        type: 'line',\n                        line: {\n                            markerEnd: 'arrow'\n                        },\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }, {\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.arrowInfinityLine\n                    .annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    /**\n     * A horizontal line annotation. Includes `start` event.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-horizontal-line\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    horizontalLine: {\n        /** @ignore-option */\n        className: 'highcharts-horizontal-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'horizontalLine',\n                    type: 'infinityLine',\n                    draggable: 'y',\n                    typeOptions: {\n                        type: 'horizontalLine',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings\n                    .horizontalLine.annotationsOptions);\n            this.chart.addAnnotation(options);\n        }\n    },\n    /**\n     * A vertical line annotation. Includes `start` event.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-vertical-line\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    verticalLine: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'verticalLine',\n                    type: 'infinityLine',\n                    draggable: 'x',\n                    typeOptions: {\n                        type: 'verticalLine',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.verticalLine.annotationsOptions);\n            this.chart.addAnnotation(options);\n        }\n    },\n    /**\n     * Crooked line (three points) annotation bindings. Includes `start` and two\n     * events in `steps` (for second and third points in crooked line) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-crooked3\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    // Crooked Line type annotations:\n    crooked3: {\n        /** @ignore-option */\n        className: 'highcharts-crooked3',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value,\n                y = coordsY.value,\n                navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'crooked3',\n                    type: 'crookedLine',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y }\n                        ]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.crooked3.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2)\n        ]\n    },\n    /**\n     * Crooked line (five points) annotation bindings. Includes `start` and four\n     * events in `steps` (for all consequent points in crooked line) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-crooked5\", \"start\": function() {}, \"steps\": [function() {}, function() {}, function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    crooked5: {\n        /** @ignore-option */\n        className: 'highcharts-crooked5',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value,\n                y = coordsY.value,\n                navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'crooked5',\n                    type: 'crookedLine',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y }\n                        ]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.crooked5.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2),\n            StockToolsBindings_updateNthPoint(3),\n            StockToolsBindings_updateNthPoint(4)\n        ]\n    },\n    /**\n     * Elliott wave (three points) annotation bindings. Includes `start` and two\n     * events in `steps` (for second and third points) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-elliott3\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    elliott3: {\n        /** @ignore-option */\n        className: 'highcharts-elliott3',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'elliott3',\n                    type: 'elliottWave',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [\n                            { x: x, y: y },\n                            { x: x, y: y },\n                            { x: x, y: y },\n                            { x: x, y: y }\n                        ]\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.elliott3.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2),\n            StockToolsBindings_updateNthPoint(3)\n        ]\n    },\n    /**\n     * Elliott wave (five points) annotation bindings. Includes `start` and four\n     * event in `steps` (for all consequent points in Elliott wave) array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-elliott3\", \"start\": function() {}, \"steps\": [function() {}, function() {}, function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    elliott5: {\n        /** @ignore-option */\n        className: 'highcharts-elliott5',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'elliott5',\n                    type: 'elliottWave',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [\n                            { x: x, y: y },\n                            { x: x, y: y },\n                            { x: x, y: y },\n                            { x: x, y: y },\n                            { x: x, y: y },\n                            { x: x, y: y }\n                        ]\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.elliott5.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2),\n            StockToolsBindings_updateNthPoint(3),\n            StockToolsBindings_updateNthPoint(4),\n            StockToolsBindings_updateNthPoint(5)\n        ]\n    },\n    /**\n     * A measure (x-dimension) annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-measure-x\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    measureX: {\n        /** @ignore-option */\n        className: 'highcharts-measure-x',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'measure',\n                    type: 'measure',\n                    typeOptions: {\n                        selectType: 'x',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        point: { x: x, y: y },\n                        crosshairX: {\n                            strokeWidth: 1,\n                            stroke: \"#000000\" /* Palette.neutralColor100 */\n                        },\n                        crosshairY: {\n                            enabled: false,\n                            strokeWidth: 0,\n                            stroke: \"#000000\" /* Palette.neutralColor100 */\n                        },\n                        background: {\n                            width: 0,\n                            height: 0,\n                            strokeWidth: 0,\n                            stroke: \"#ffffff\" /* Palette.backgroundColor */\n                        }\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.measureX.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateRectSize\n        ]\n    },\n    /**\n     * A measure (y-dimension) annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-measure-y\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    measureY: {\n        /** @ignore-option */\n        className: 'highcharts-measure-y',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'measure',\n                    type: 'measure',\n                    typeOptions: {\n                        selectType: 'y',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        point: { x: x, y: y },\n                        crosshairX: {\n                            enabled: false,\n                            strokeWidth: 0,\n                            stroke: \"#000000\" /* Palette.neutralColor100 */\n                        },\n                        crosshairY: {\n                            strokeWidth: 1,\n                            stroke: \"#000000\" /* Palette.neutralColor100 */\n                        },\n                        background: {\n                            width: 0,\n                            height: 0,\n                            strokeWidth: 0,\n                            stroke: \"#ffffff\" /* Palette.backgroundColor */\n                        }\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.measureY.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateRectSize\n        ]\n    },\n    /**\n     * A measure (xy-dimension) annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-measure-xy\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    measureXY: {\n        /** @ignore-option */\n        className: 'highcharts-measure-xy',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'measure',\n                    type: 'measure',\n                    typeOptions: {\n                        selectType: 'xy',\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        point: { x: x, y: y },\n                        background: {\n                            width: 0,\n                            height: 0,\n                            strokeWidth: 0,\n                            stroke: \"#ffffff\" /* Palette.backgroundColor */\n                        },\n                        crosshairX: {\n                            strokeWidth: 1,\n                            stroke: \"#000000\" /* Palette.neutralColor100 */\n                        },\n                        crosshairY: {\n                            strokeWidth: 1,\n                            stroke: \"#000000\" /* Palette.neutralColor100 */\n                        }\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.measureXY.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateRectSize\n        ]\n    },\n    // Advanced type annotations:\n    /**\n     * A fibonacci annotation bindings. Includes `start` and two events in\n     * `steps` array (updates second point, then height).\n     *\n     *   @sample {highstock} stock/stocktools/custom-stock-tools-bindings\n     *     Custom stock tools bindings\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-fibonacci\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": { \"typeOptions\": { \"reversed\": false }}}\n     */\n    fibonacci: {\n        className: 'highcharts-fibonacci',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'fibonacci',\n                    type: 'fibonacci',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [\n                            { x: x, y: y },\n                            { x: x, y: y }\n                        ]\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.fibonacci.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateHeight\n        ]\n    },\n    /**\n     * A parallel channel (tunnel) annotation bindings. Includes `start` and\n     * two events in `steps` array (updates second point, then height).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-parallel-channel\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    parallelChannel: {\n        /** @ignore-option */\n        className: 'highcharts-parallel-channel',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value,\n                y = coordsY.value,\n                navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    langKey: 'parallelChannel',\n                    type: 'tunnel',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [\n                            { x: x,\n                y: y },\n                            { x: x,\n                y: y }\n                        ]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.parallelChannel\n                    .annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateHeight\n        ]\n    },\n    /**\n     * An Andrew's pitchfork annotation bindings. Includes `start` and two\n     * events in `steps` array (sets second and third control points).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-pitchfork\", \"start\": function() {}, \"steps\": [function() {}, function() {}], \"annotationsOptions\": {}}\n     */\n    pitchfork: {\n        /** @ignore-option */\n        className: 'highcharts-pitchfork',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var x = coordsX.value, y = coordsY.value, navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'pitchfork',\n                    type: 'pitchfork',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value,\n                                y: coordsY.value,\n                                controlPoint: {\n                                    style: {\n                                        fill: \"#f21313\" /* Palette.negativeColor */\n                                    }\n                                }\n                            },\n                            { x: x, y: y },\n                            { x: x, y: y }\n                        ],\n                        innerBackground: {\n                            fill: 'rgba(100, 170, 255, 0.8)'\n                        }\n                    },\n                    shapeOptions: {\n                        strokeWidth: 2\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.pitchfork.annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        steps: [\n            StockToolsBindings_updateNthPoint(1),\n            StockToolsBindings_updateNthPoint(2)\n        ]\n    },\n    // Labels with arrow and auto increments\n    /**\n     * A vertical counter annotation bindings. Includes `start` event. On click,\n     * finds the closest point and marks it with a numeric annotation -\n     * incrementing counter on each add.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-vertical-counter\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    verticalCounter: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-counter',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var closestPoint = StockToolsBindings_attractToPoint(e,\n                this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            this.verticalCounter = this.verticalCounter || 0;\n            var navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'verticalCounter',\n                    type: 'verticalLine',\n                    typeOptions: {\n                        point: {\n                            x: closestPoint.x,\n                            y: closestPoint.y,\n                            xAxis: closestPoint.xAxis,\n                            yAxis: closestPoint.yAxis\n                        },\n                        label: {\n                            offset: closestPoint.below ? 40 : -40,\n                            text: this.verticalCounter.toString()\n                        }\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */,\n                            fontSize: '0.7em'\n                        }\n                    },\n                    shapeOptions: {\n                        stroke: 'rgba(0, 0, 0, 0.75)',\n                        strokeWidth: 1\n                    }\n                }, navigation.annotationsOptions, navigation.bindings\n                    .verticalCounter.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            this.verticalCounter++;\n            annotation.options.events.click.call(annotation, {});\n        }\n    },\n    /**\n     * A time cycles annotation bindings. Includes `start` event and 1 `step`\n     * event. first click marks the beginning of the circle, and the second one\n     * sets its diameter.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-time-cycles\", \"start\": function() {}, \"steps\": [function (){}] \"annotationsOptions\": {}}\n     */\n    timeCycles: {\n        className: 'highcharts-time-cycles',\n        start: function (e) {\n            var closestPoint = StockToolsBindings_attractToPoint(e,\n                this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            var navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'timeCycles',\n                    type: 'timeCycles',\n                    typeOptions: {\n                        xAxis: closestPoint.xAxis,\n                        yAxis: closestPoint.yAxis,\n                        points: [{\n                                x: closestPoint.x\n                            }, {\n                                x: closestPoint.x\n                            }],\n                        line: {\n                            stroke: 'rgba(0, 0, 0, 0.75)',\n                            fill: 'transparent',\n                            strokeWidth: 2\n                        }\n                    }\n                }, navigation.annotationsOptions, navigation.bindings.timeCycles.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            annotation.options.events.click.call(annotation, {});\n            return annotation;\n        },\n        steps: [\n            StockToolsBindings_updateNthPoint(1)\n        ]\n    },\n    verticalLabel: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-label',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var closestPoint = StockToolsBindings_attractToPoint(e,\n                this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            var navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'verticalLabel',\n                    type: 'verticalLine',\n                    typeOptions: {\n                        point: {\n                            x: closestPoint.x,\n                            y: closestPoint.y,\n                            xAxis: closestPoint.xAxis,\n                            yAxis: closestPoint.yAxis\n                        },\n                        label: {\n                            offset: closestPoint.below ? 40 : -40\n                        }\n                    },\n                    labelOptions: {\n                        style: {\n                            color: \"#666666\" /* Palette.neutralColor60 */,\n                            fontSize: '0.7em'\n                        }\n                    },\n                    shapeOptions: {\n                        stroke: 'rgba(0, 0, 0, 0.75)',\n                        strokeWidth: 1\n                    }\n                }, navigation.annotationsOptions, navigation.bindings\n                    .verticalLabel.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            annotation.options.events.click.call(annotation, {});\n        }\n    },\n    /**\n     * A vertical arrow annotation bindings. Includes `start` event. On click,\n     * finds the closest point and marks it with an arrow.\n     * `${palette.positiveColor}` is the color of the arrow when\n     * pointing from above and `${palette.negativeColor}`\n     * when pointing from below the point.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-vertical-arrow\", \"start\": function() {}, \"annotationsOptions\": {}}\n     */\n    verticalArrow: {\n        /** @ignore-option */\n        className: 'highcharts-vertical-arrow',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var closestPoint = StockToolsBindings_attractToPoint(e,\n                this.chart);\n            // Exit if clicked out of axes area\n            if (!closestPoint) {\n                return;\n            }\n            var navigation = this.chart.options.navigation, options = StockToolsBindings_merge({\n                    langKey: 'verticalArrow',\n                    type: 'verticalLine',\n                    typeOptions: {\n                        point: {\n                            x: closestPoint.x,\n                            y: closestPoint.y,\n                            xAxis: closestPoint.xAxis,\n                            yAxis: closestPoint.yAxis\n                        },\n                        label: {\n                            offset: closestPoint.below ? 40 : -40,\n                            format: ' '\n                        },\n                        connector: {\n                            fill: 'none',\n                            stroke: closestPoint.below ?\n                                \"#f21313\" /* Palette.negativeColor */ :\n                                \"#06b535\" /* Palette.positiveColor */\n                        }\n                    },\n                    shapeOptions: {\n                        stroke: 'rgba(0, 0, 0, 0.75)',\n                        strokeWidth: 1\n                    }\n                }, navigation.annotationsOptions, navigation.bindings\n                    .verticalArrow.annotationsOptions), annotation = this.chart.addAnnotation(options);\n            annotation.options.events.click.call(annotation, {});\n        }\n    },\n    /**\n     * The Fibonacci Time Zones annotation bindings. Includes `start` and one\n     * event in `steps` array.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-fibonacci-time-zones\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n     */\n    fibonacciTimeZones: {\n        /** @ignore-option */\n        className: 'highcharts-fibonacci-time-zones',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        start: function (e) {\n            var _a = this.getCoords(e),\n                coordsX = _a[0],\n                coordsY = _a[1];\n            // Exit if clicked out of axes area\n            if (!coordsX || !coordsY) {\n                return;\n            }\n            var navigation = this.chart.options.navigation,\n                options = StockToolsBindings_merge({\n                    type: 'fibonacciTimeZones',\n                    langKey: 'fibonacciTimeZones',\n                    typeOptions: {\n                        xAxis: coordsX.axis.index,\n                        yAxis: coordsY.axis.index,\n                        points: [{\n                                x: coordsX.value\n                            }]\n                    }\n                },\n                navigation.annotationsOptions,\n                navigation.bindings.fibonacciTimeZones\n                    .annotationsOptions);\n            return this.chart.addAnnotation(options);\n        },\n        /** @ignore-option */\n        // eslint-disable-next-line valid-jsdoc\n        steps: [\n            function (e, annotation) {\n                var mockPointOpts = annotation.options.typeOptions.points,\n                    x = mockPointOpts && mockPointOpts[0].x,\n                    _a = this.getCoords(e),\n                    coordsX = _a[0],\n                    coordsY = _a[1];\n                if (coordsX && coordsY) {\n                    annotation.update({\n                        typeOptions: {\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: x\n                                }, {\n                                    x: coordsX.value\n                                }]\n                        }\n                    });\n                }\n            }\n        ]\n    },\n    // Flag types:\n    /**\n     * A flag series bindings. Includes `start` event. On click, finds the\n     * closest point and marks it with a flag with `'circlepin'` shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-circlepin\", \"start\": function() {}}\n     */\n    flagCirclepin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-circlepin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('circlepin')\n    },\n    /**\n     * A flag series bindings. Includes `start` event. On click, finds the\n     * closest point and marks it with a flag with `'diamondpin'` shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-diamondpin\", \"start\": function() {}}\n     */\n    flagDiamondpin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-diamondpin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('flag')\n    },\n    /**\n     * A flag series bindings. Includes `start` event.\n     * On click, finds the closest point and marks it with a flag with\n     * `'squarepin'` shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-squarepin\", \"start\": function() {}}\n     */\n    flagSquarepin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-squarepin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('squarepin')\n    },\n    /**\n     * A flag series bindings. Includes `start` event.\n     * On click, finds the closest point and marks it with a flag without pin\n     * shape.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-flag-simplepin\", \"start\": function() {}}\n     */\n    flagSimplepin: {\n        /** @ignore-option */\n        className: 'highcharts-flag-simplepin',\n        /** @ignore-option */\n        start: StockToolsBindings_addFlagFromForm('nopin')\n    },\n    // Other tools:\n    /**\n     * Enables zooming in xAxis on a chart. Includes `start` event which\n     * changes [chart.zoomType](#chart.zoomType).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-zoom-x\", \"init\": function() {}}\n     */\n    zoomX: {\n        /** @ignore-option */\n        className: 'highcharts-zoom-x',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.update({\n                chart: {\n                    zooming: {\n                        type: 'x'\n                    }\n                }\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Enables zooming in yAxis on a chart. Includes `start` event which\n     * changes [chart.zoomType](#chart.zoomType).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-zoom-y\", \"init\": function() {}}\n     */\n    zoomY: {\n        /** @ignore-option */\n        className: 'highcharts-zoom-y',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.update({\n                chart: {\n                    zooming: {\n                        type: 'y'\n                    }\n                }\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Enables zooming in xAxis and yAxis on a chart. Includes `start` event\n     * which changes [chart.zoomType](#chart.zoomType).\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-zoom-xy\", \"init\": function() {}}\n     */\n    zoomXY: {\n        /** @ignore-option */\n        className: 'highcharts-zoom-xy',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.update({\n                chart: {\n                    zooming: {\n                        type: 'xy'\n                    }\n                }\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'line'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-line\", \"init\": function() {}}\n     */\n    seriesTypeLine: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-line',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'line',\n                useOhlcData: true\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'ohlc'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-ohlc\", \"init\": function() {}}\n     */\n    seriesTypeOhlc: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-ohlc',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'ohlc'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'candlestick'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-candlestick\", \"init\": function() {}}\n     */\n    seriesTypeCandlestick: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-candlestick',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'candlestick'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'heikinashi'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-heikinashi\", \"init\": function() {}}\n     */\n    seriesTypeHeikinAshi: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-heikinashi',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'heikinashi'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Change main series to `'hlc'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-hlc\", \"init\": function () {}}\n     */\n    seriesTypeHLC: {\n        className: 'highcharts-series-type-hlc',\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'hlc',\n                useOhlcData: true\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Changes main series to `'hollowcandlestick'` type.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-series-type-hollowcandlestick\", \"init\": function() {}}\n     */\n    seriesTypeHollowCandlestick: {\n        /** @ignore-option */\n        className: 'highcharts-series-type-hollowcandlestick',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            this.chart.series[0].update({\n                type: 'hollowcandlestick'\n            });\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Displays chart in fullscreen.\n     *\n     * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"noDataState\": \"normal\", \"highcharts-full-screen\", \"init\": function() {}}\n     */\n    fullScreen: {\n        /** @ignore-option */\n        className: 'highcharts-full-screen',\n        noDataState: 'normal',\n        /** @ignore-option */\n        init: function (button) {\n            if (this.chart.fullscreen) {\n                this.chart.fullscreen.toggle();\n            }\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Hides/shows two price indicators:\n     * - last price in the dataset\n     * - last price in the selected range\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-current-price-indicator\", \"init\": function() {}}\n     */\n    currentPriceIndicator: {\n        /** @ignore-option */\n        className: 'highcharts-current-price-indicator',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            var chart = this.chart,\n                series = chart.series,\n                gui = chart.stockTools,\n                priceIndicatorEnabled = StockToolsBindings_isPriceIndicatorEnabled(chart.series);\n            if (gui && gui.guiEnabled) {\n                series.forEach(function (series) {\n                    series.update({\n                        lastPrice: { enabled: !priceIndicatorEnabled },\n                        lastVisiblePrice: {\n                            enabled: !priceIndicatorEnabled,\n                            label: { enabled: true }\n                        }\n                    }, false);\n                });\n                chart.redraw();\n            }\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Indicators bindings. Includes `init` event to show a popup.\n     *\n     * Note: In order to show base series from the chart in the popup's\n     * dropdown each series requires\n     * [series.id](https://api.highcharts.com/highstock/series.line.id) to be\n     * defined.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-indicators\", \"init\": function() {}}\n     */\n    indicators: {\n        /** @ignore-option */\n        className: 'highcharts-indicators',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function () {\n            var navigation = this;\n            StockToolsBindings_fireEvent(navigation, 'showPopup', {\n                formType: 'indicators',\n                options: {},\n                // Callback on submit:\n                onSubmit: function (data) {\n                    StockToolsBindings_manageIndicators.call(navigation, data);\n                }\n            });\n        }\n    },\n    /**\n     * Hides/shows all annotations on a chart.\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-toggle-annotations\", \"init\": function() {}}\n     */\n    toggleAnnotations: {\n        /** @ignore-option */\n        className: 'highcharts-toggle-annotations',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            var chart = this.chart,\n                gui = chart.stockTools,\n                iconsURL = gui.getIconsURL();\n            this.toggledAnnotations = !this.toggledAnnotations;\n            (chart.annotations || []).forEach(function (annotation) {\n                annotation.setVisibility(!this.toggledAnnotations);\n            }, this);\n            if (gui && gui.guiEnabled) {\n                if (this.toggledAnnotations) {\n                    button.firstChild.style['background-image'] =\n                        'url(\"' + iconsURL +\n                            'annotations-hidden.svg\")';\n                }\n                else {\n                    button.firstChild.style['background-image'] =\n                        'url(\"' + iconsURL +\n                            'annotations-visible.svg\")';\n                }\n            }\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    },\n    /**\n     * Save a chart in localStorage under `highcharts-chart` key.\n     * Stored items:\n     * - annotations\n     * - indicators (with yAxes)\n     * - flags\n     *\n     * @type    {Highcharts.NavigationBindingsOptionsObject}\n     * @product highstock\n     * @default {\"className\": \"highcharts-save-chart\", \"noDataState\": \"normal\", \"init\": function() {}}\n     */\n    saveChart: {\n        /** @ignore-option */\n        className: 'highcharts-save-chart',\n        noDataState: 'normal',\n        // eslint-disable-next-line valid-jsdoc\n        /** @ignore-option */\n        init: function (button) {\n            var navigation = this,\n                chart = navigation.chart,\n                annotations = [],\n                indicators = [],\n                flags = [],\n                yAxes = [];\n            chart.annotations.forEach(function (annotation, index) {\n                annotations[index] = annotation.userOptions;\n            });\n            chart.series.forEach(function (series) {\n                if (series.is('sma')) {\n                    indicators.push(series.userOptions);\n                }\n                else if (series.type === 'flags') {\n                    flags.push(series.userOptions);\n                }\n            });\n            chart.yAxis.forEach(function (yAxis) {\n                if (StockToolsBindings_isNotNavigatorYAxis(yAxis)) {\n                    yAxes.push(yAxis.options);\n                }\n            });\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().win.localStorage.setItem('highcharts-chart', JSON.stringify({\n                annotations: annotations,\n                indicators: indicators,\n                flags: flags,\n                yAxes: yAxes\n            }));\n            StockToolsBindings_fireEvent(this, 'deselectButton', { button: button });\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var StockTools_StockToolsBindings = (StockToolsBindings);\n\n;// ./code/es5/es-modules/Stock/StockTools/StockToolsDefaults.js\n/* *\n *\n *  GUI generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nvar StockToolsDefaults_lang = {\n    /**\n     * Configure the stockTools GUI titles(hints) in the chart. Requires\n     * the `stock-tools.js` module to be loaded.\n     *\n     * @product highstock\n     * @since   7.0.0\n     */\n    stockTools: {\n        gui: {\n            // Main buttons:\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            crookedLines: 'Crooked lines',\n            measure: 'Measure',\n            advanced: 'Advanced',\n            toggleAnnotations: 'Toggle annotations',\n            verticalLabels: 'Vertical labels',\n            flags: 'Flags',\n            zoomChange: 'Zoom change',\n            typeChange: 'Type change',\n            saveChart: 'Save chart',\n            indicators: 'Indicators',\n            currentPriceIndicator: 'Current Price Indicators',\n            // Other features:\n            zoomX: 'Zoom X',\n            zoomY: 'Zoom Y',\n            zoomXY: 'Zooom XY',\n            fullScreen: 'Fullscreen',\n            typeOHLC: 'OHLC',\n            typeLine: 'Line',\n            typeCandlestick: 'Candlestick',\n            typeHLC: 'HLC',\n            typeHollowCandlestick: 'Hollow Candlestick',\n            typeHeikinAshi: 'Heikin Ashi',\n            // Basic shapes:\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            label: 'Label',\n            rectangle: 'Rectangle',\n            // Flags:\n            flagCirclepin: 'Flag circle',\n            flagDiamondpin: 'Flag diamond',\n            flagSquarepin: 'Flag square',\n            flagSimplepin: 'Flag simple',\n            // Measures:\n            measureXY: 'Measure XY',\n            measureX: 'Measure X',\n            measureY: 'Measure Y',\n            // Segment, ray and line:\n            segment: 'Segment',\n            arrowSegment: 'Arrow segment',\n            ray: 'Ray',\n            arrowRay: 'Arrow ray',\n            line: 'Line',\n            arrowInfinityLine: 'Arrow line',\n            horizontalLine: 'Horizontal line',\n            verticalLine: 'Vertical line',\n            infinityLine: 'Infinity line',\n            // Crooked lines:\n            crooked3: 'Crooked 3 line',\n            crooked5: 'Crooked 5 line',\n            elliott3: 'Elliott 3 line',\n            elliott5: 'Elliott 5 line',\n            // Counters:\n            verticalCounter: 'Vertical counter',\n            verticalLabel: 'Vertical label',\n            verticalArrow: 'Vertical arrow',\n            // Advanced:\n            fibonacci: 'Fibonacci',\n            fibonacciTimeZones: 'Fibonacci Time Zones',\n            pitchfork: 'Pitchfork',\n            parallelChannel: 'Parallel channel',\n            timeCycles: 'Time Cycles'\n        }\n    },\n    navigation: {\n        popup: {\n            // Annotations:\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            segment: 'Segment',\n            arrowSegment: 'Arrow segment',\n            ray: 'Ray',\n            arrowRay: 'Arrow ray',\n            line: 'Line',\n            arrowInfinityLine: 'Arrow line',\n            horizontalLine: 'Horizontal line',\n            verticalLine: 'Vertical line',\n            crooked3: 'Crooked 3 line',\n            crooked5: 'Crooked 5 line',\n            elliott3: 'Elliott 3 line',\n            elliott5: 'Elliott 5 line',\n            verticalCounter: 'Vertical counter',\n            verticalLabel: 'Vertical label',\n            verticalArrow: 'Vertical arrow',\n            fibonacci: 'Fibonacci',\n            fibonacciTimeZones: 'Fibonacci Time Zones',\n            pitchfork: 'Pitchfork',\n            parallelChannel: 'Parallel channel',\n            infinityLine: 'Infinity line',\n            measure: 'Measure',\n            measureXY: 'Measure XY',\n            measureX: 'Measure X',\n            measureY: 'Measure Y',\n            timeCycles: 'Time Cycles',\n            // Flags:\n            flags: 'Flags',\n            // GUI elements:\n            addButton: 'Add',\n            saveButton: 'Save',\n            editButton: 'Edit',\n            removeButton: 'Remove',\n            series: 'Series',\n            volume: 'Volume',\n            connector: 'Connector',\n            // Field names:\n            innerBackground: 'Inner background',\n            outerBackground: 'Outer background',\n            crosshairX: 'Crosshair X',\n            crosshairY: 'Crosshair Y',\n            tunnel: 'Tunnel',\n            background: 'Background',\n            // Indicators' searchbox (#16019):\n            noFilterMatch: 'No match',\n            // Indicators' params (#15170):\n            searchIndicators: 'Search Indicators',\n            clearFilter: '\\u2715 clear filter',\n            index: 'Index',\n            period: 'Period',\n            periods: 'Periods',\n            standardDeviation: 'Standard deviation',\n            periodTenkan: 'Tenkan period',\n            periodSenkouSpanB: 'Senkou Span B period',\n            periodATR: 'ATR period',\n            multiplierATR: 'ATR multiplier',\n            shortPeriod: 'Short period',\n            longPeriod: 'Long period',\n            signalPeriod: 'Signal period',\n            decimals: 'Decimals',\n            algorithm: 'Algorithm',\n            topBand: 'Top band',\n            bottomBand: 'Bottom band',\n            initialAccelerationFactor: 'Initial acceleration factor',\n            maxAccelerationFactor: 'Max acceleration factor',\n            increment: 'Increment',\n            multiplier: 'Multiplier',\n            ranges: 'Ranges',\n            highIndex: 'High index',\n            lowIndex: 'Low index',\n            deviation: 'Deviation',\n            xAxisUnit: 'x-axis unit',\n            factor: 'Factor',\n            fastAvgPeriod: 'Fast average period',\n            slowAvgPeriod: 'Slow average period',\n            average: 'Average',\n            /**\n             * Configure the aliases for indicator names.\n             *\n             * @product highstock\n             * @since 9.3.0\n             */\n            indicatorAliases: {\n                // Overlays\n                /**\n                 * Acceleration Bands alias.\n                 *\n                 * @default ['Acceleration Bands']\n                 * @type    {Array<string>}\n                 */\n                abands: ['Acceleration Bands'],\n                /**\n                 * Bollinger Bands alias.\n                 *\n                 * @default ['Bollinger Bands']\n                 * @type    {Array<string>}\n                 */\n                bb: ['Bollinger Bands'],\n                /**\n                 * Double Exponential Moving Average alias.\n                 *\n                 * @default ['Double Exponential Moving Average']\n                 * @type    {Array<string>}\n                 */\n                dema: ['Double Exponential Moving Average'],\n                /**\n                 *  Exponential Moving Average alias.\n                 *\n                 * @default ['Exponential Moving Average']\n                 * @type    {Array<string>}\n                 */\n                ema: ['Exponential Moving Average'],\n                /**\n                 *  Ichimoku Kinko Hyo alias.\n                 *\n                 * @default ['Ichimoku Kinko Hyo']\n                 * @type    {Array<string>}\n                 */\n                ikh: ['Ichimoku Kinko Hyo'],\n                /**\n                 *  Keltner Channels alias.\n                 *\n                 * @default ['Keltner Channels']\n                 * @type    {Array<string>}\n                 */\n                keltnerchannels: ['Keltner Channels'],\n                /**\n                 *  Linear Regression alias.\n                 *\n                 * @default ['Linear Regression']\n                 * @type    {Array<string>}\n                 */\n                linearRegression: ['Linear Regression'],\n                /**\n                 *  Pivot Points alias.\n                 *\n                 * @default ['Pivot Points']\n                 * @type    {Array<string>}\n                 */\n                pivotpoints: ['Pivot Points'],\n                /**\n                 *  Price Channel alias.\n                 *\n                 * @default ['Price Channel']\n                 * @type    {Array<string>}\n                 */\n                pc: ['Price Channel'],\n                /**\n                 *  Price Envelopes alias.\n                 *\n                 * @default ['Price Envelopes']\n                 * @type    {Array<string>}\n                 */\n                priceenvelopes: ['Price Envelopes'],\n                /**\n                 *  Parabolic SAR alias.\n                 *\n                 * @default ['Parabolic SAR']\n                 * @type    {Array<string>}\n                 */\n                psar: ['Parabolic SAR'],\n                /**\n                 *  Simple Moving Average alias.\n                 *\n                 * @default ['Simple Moving Average']\n                 * @type    {Array<string>}\n                 */\n                sma: ['Simple Moving Average'],\n                /**\n                 *  Super Trend alias.\n                 *\n                 * @default ['Super Trend']\n                 * @type    {Array<string>}\n                 */\n                supertrend: ['Super Trend'],\n                /**\n                 *  Triple Exponential Moving Average alias.\n                 *\n                 * @default ['Triple Exponential Moving Average']\n                 * @type    {Array<string>}\n                 */\n                tema: ['Triple Exponential Moving Average'],\n                /**\n                 *  Volume by Price alias.\n                 *\n                 * @default ['Volume by Price']\n                 * @type    {Array<string>}\n                 */\n                vbp: ['Volume by Price'],\n                /**\n                 *  Volume Weighted Moving Average alias.\n                 *\n                 * @default ['Volume Weighted Moving Average']\n                 * @type    {Array<string>}\n                 */\n                vwap: ['Volume Weighted Moving Average'],\n                /**\n                 *  Weighted Moving Average alias.\n                 *\n                 * @default ['Weighted Moving Average']\n                 * @type    {Array<string>}\n                 */\n                wma: ['Weighted Moving Average'],\n                /**\n                 *  Zig Zagalias.\n                 *\n                 * @default ['Zig Zag']\n                 * @type    {Array<string>}\n                 */\n                zigzag: ['Zig Zag'],\n                // Oscilators\n                /**\n                 *  Absolute price indicator alias.\n                 *\n                 * @default ['Absolute price indicator']\n                 * @type    {Array<string>}\n                 */\n                apo: ['Absolute price indicator'],\n                /**\n                 * Accumulation/Distribution alias.\n                 *\n                 * @default ['Accumulation/Distribution’]\n                 * @type    {Array<string>}\n                 */\n                ad: ['Accumulation/Distribution'],\n                /**\n                 *  Aroon alias.\n                 *\n                 * @default ['Aroon']\n                 * @type    {Array<string>}\n                 */\n                aroon: ['Aroon'],\n                /**\n                 *  Aroon oscillator alias.\n                 *\n                 * @default ['Aroon oscillator']\n                 * @type    {Array<string>}\n                 */\n                aroonoscillator: ['Aroon oscillator'],\n                /**\n                 *  Average True Range alias.\n                 *\n                 * @default ['Average True Range’]\n                 * @type    {Array<string>}\n                 */\n                atr: ['Average True Range'],\n                /**\n                 *  Awesome oscillator alias.\n                 *\n                 * @default ['Awesome oscillator’]\n                 * @type    {Array<string>}\n                 */\n                ao: ['Awesome oscillator'],\n                /**\n                 *  Commodity Channel Index alias.\n                 *\n                 * @default ['Commodity Channel Index’]\n                 * @type    {Array<string>}\n                 */\n                cci: ['Commodity Channel Index'],\n                /**\n                 *  Chaikin alias.\n                 *\n                 * @default ['Chaikin’]\n                 * @type    {Array<string>}\n                 */\n                chaikin: ['Chaikin'],\n                /**\n                 *  Chaikin Money Flow alias.\n                 *\n                 * @default ['Chaikin Money Flow’]\n                 * @type    {Array<string>}\n                 */\n                cmf: ['Chaikin Money Flow'],\n                /**\n                 *  Chande Momentum Oscillator alias.\n                 *\n                 * @default ['Chande Momentum Oscillator’]\n                 * @type    {Array<string>}\n                 */\n                cmo: ['Chande Momentum Oscillator'],\n                /**\n                 *  Disparity Index alias.\n                 *\n                 * @default ['Disparity Index’]\n                 * @type    {Array<string>}\n                 */\n                disparityindex: ['Disparity Index'],\n                /**\n                 *  Directional Movement Index alias.\n                 *\n                 * @default ['Directional Movement Index’]\n                 * @type    {Array<string>}\n                 */\n                dmi: ['Directional Movement Index'],\n                /**\n                 *  Detrended price oscillator alias.\n                 *\n                 * @default ['Detrended price oscillator’]\n                 * @type    {Array<string>}\n                 */\n                dpo: ['Detrended price oscillator'],\n                /**\n                 *  Klinger Oscillator alias.\n                 *\n                 * @default [‘Klinger Oscillator’]\n                 * @type    {Array<string>}\n                 */\n                klinger: ['Klinger Oscillator'],\n                /**\n                 *  Linear Regression Angle alias.\n                 *\n                 * @default [‘Linear Regression Angle’]\n                 * @type    {Array<string>}\n                 */\n                linearRegressionAngle: ['Linear Regression Angle'],\n                /**\n                 *  Linear Regression Intercept alias.\n                 *\n                 * @default [‘Linear Regression Intercept’]\n                 * @type    {Array<string>}\n                 */\n                linearRegressionIntercept: ['Linear Regression Intercept'],\n                /**\n                 *  Linear Regression Slope alias.\n                 *\n                 * @default [‘Linear Regression Slope’]\n                 * @type    {Array<string>}\n                 */\n                linearRegressionSlope: ['Linear Regression Slope'],\n                /**\n                 *  Moving Average Convergence Divergence alias.\n                 *\n                 * @default ['Moving Average Convergence Divergence’]\n                 * @type    {Array<string>}\n                 */\n                macd: ['Moving Average Convergence Divergence'],\n                /**\n                 *  Money Flow Index alias.\n                 *\n                 * @default ['Money Flow Index’]\n                 * @type    {Array<string>}\n                 */\n                mfi: ['Money Flow Index'],\n                /**\n                 *  Momentum alias.\n                 *\n                 * @default [‘Momentum’]\n                 * @type    {Array<string>}\n                 */\n                momentum: ['Momentum'],\n                /**\n                 *  Normalized Average True Range alias.\n                 *\n                 * @default ['Normalized Average True Range’]\n                 * @type    {Array<string>}\n                 */\n                natr: ['Normalized Average True Range'],\n                /**\n                 *  On-Balance Volume alias.\n                 *\n                 * @default ['On-Balance Volume’]\n                 * @type    {Array<string>}\n                 */\n                obv: ['On-Balance Volume'],\n                /**\n                 * Percentage Price oscillator alias.\n                 *\n                 * @default ['Percentage Price oscillator’]\n                 * @type    {Array<string>}\n                 */\n                ppo: ['Percentage Price oscillator'],\n                /**\n                 *  Rate of Change alias.\n                 *\n                 * @default ['Rate of Change’]\n                 * @type    {Array<string>}\n                 */\n                roc: ['Rate of Change'],\n                /**\n                 *  Relative Strength Index alias.\n                 *\n                 * @default ['Relative Strength Index’]\n                 * @type    {Array<string>}\n                 */\n                rsi: ['Relative Strength Index'],\n                /**\n                 *  Slow Stochastic alias.\n                 *\n                 * @default [‘Slow Stochastic’]\n                 * @type    {Array<string>}\n                 */\n                slowstochastic: ['Slow Stochastic'],\n                /**\n                 *  Stochastic alias.\n                 *\n                 * @default [‘Stochastic’]\n                 * @type    {Array<string>}\n                 */\n                stochastic: ['Stochastic'],\n                /**\n                 *  TRIX alias.\n                 *\n                 * @default [‘TRIX’]\n                 * @type    {Array<string>}\n                 */\n                trix: ['TRIX'],\n                /**\n                 *  Williams %R alias.\n                 *\n                 * @default [‘Williams %R’]\n                 * @type    {Array<string>}\n                 */\n                williamsr: ['Williams %R']\n            }\n        }\n    }\n};\n/**\n * Configure the stockTools gui strings in the chart. Requires the\n * [stockTools module]() to be loaded. For a description of the module\n * and information on its features, see [Highcharts StockTools]().\n *\n * @product highstock\n *\n * @sample stock/demo/stock-tools-gui Stock Tools GUI\n *\n * @sample stock/demo/stock-tools-custom-gui Stock Tools customized GUI\n *\n * @since        7.0.0\n * @optionparent stockTools\n */\nvar stockTools = {\n    /**\n     * Definitions of buttons in Stock Tools GUI.\n     */\n    gui: {\n        /**\n         * Path where Highcharts will look for icons. Change this to use\n         * icons from a different server.\n         *\n         * Since 7.1.3 use [iconsURL](#navigation.iconsURL) for popup and\n         * stock tools.\n         *\n         * @deprecated\n         * @apioption stockTools.gui.iconsURL\n         *\n         */\n        /**\n         * Enable or disable the stockTools gui.\n         */\n        enabled: true,\n        /**\n         * A CSS class name to apply to the stocktools' div,\n         * allowing unique CSS styling for each chart.\n         */\n        className: 'highcharts-bindings-wrapper',\n        /**\n         * A CSS class name to apply to the container of buttons,\n         * allowing unique CSS styling for each chart.\n         */\n        toolbarClassName: 'stocktools-toolbar',\n        /**\n         * A collection of strings pointing to config options for the\n         * toolbar items. Each name refers to a unique key from the\n         * definitions object.\n         *\n         * @type    {Array<string>}\n         * @default [\n         *   'indicators',\n         *   'separator',\n         *   'simpleShapes',\n         *   'lines',\n         *   'crookedLines',\n         *   'measure',\n         *   'advanced',\n         *   'toggleAnnotations',\n         *   'separator',\n         *   'verticalLabels',\n         *   'flags',\n         *   'separator',\n         *   'zoomChange',\n         *   'fullScreen',\n         *   'typeChange',\n         *   'separator',\n         *   'currentPriceIndicator',\n         *   'saveChart'\n         * ]\n         */\n        buttons: [\n            'indicators',\n            'separator',\n            'simpleShapes',\n            'lines',\n            'crookedLines',\n            'measure',\n            'advanced',\n            'toggleAnnotations',\n            'separator',\n            'verticalLabels',\n            'flags',\n            'separator',\n            'zoomChange',\n            'fullScreen',\n            'typeChange',\n            'separator',\n            'currentPriceIndicator',\n            'saveChart'\n        ],\n        /**\n         * An options object of the buttons definitions. Each name refers to\n         * unique key from buttons array.\n         */\n        definitions: {\n            separator: {\n                elementType: 'span',\n                /**\n                 * A predefined background symbol for the button.\n                 */\n                symbol: 'separator.svg'\n            },\n            simpleShapes: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'label',\n                 *   'circle',\n                 *   'ellipse',\n                 *   'rectangle'\n                 * ]\n                 *\n                 */\n                items: [\n                    'label',\n                    'circle',\n                    'ellipse',\n                    'rectangle'\n                ],\n                circle: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'circle.svg'\n                },\n                ellipse: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'ellipse.svg'\n                },\n                rectangle: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'rectangle.svg'\n                },\n                label: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'label.svg'\n                }\n            },\n            flags: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'flagCirclepin',\n                 *   'flagDiamondpin',\n                 *   'flagSquarepin',\n                 *   'flagSimplepin'\n                 * ]\n                 *\n                 */\n                items: [\n                    'flagCirclepin',\n                    'flagDiamondpin',\n                    'flagSquarepin',\n                    'flagSimplepin'\n                ],\n                flagSimplepin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'flag-basic.svg'\n                },\n                flagDiamondpin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     *\n                     */\n                    symbol: 'flag-diamond.svg'\n                },\n                flagSquarepin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'flag-trapeze.svg'\n                },\n                flagCirclepin: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'flag-elipse.svg'\n                }\n            },\n            lines: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'segment',\n                 *   'arrowSegment',\n                 *   'ray',\n                 *   'arrowRay',\n                 *   'line',\n                 *   'arrowInfinityLine',\n                 *   'horizontalLine',\n                 *   'verticalLine'\n                 * ]\n                 */\n                items: [\n                    'segment',\n                    'arrowSegment',\n                    'ray',\n                    'arrowRay',\n                    'line',\n                    'arrowInfinityLine',\n                    'horizontalLine',\n                    'verticalLine'\n                ],\n                segment: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'segment.svg'\n                },\n                arrowSegment: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'arrow-segment.svg'\n                },\n                ray: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'ray.svg'\n                },\n                arrowRay: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'arrow-ray.svg'\n                },\n                line: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'line.svg'\n                },\n                arrowInfinityLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'arrow-line.svg'\n                },\n                verticalLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-line.svg'\n                },\n                horizontalLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'horizontal-line.svg'\n                }\n            },\n            crookedLines: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'elliott3',\n                 *   'elliott5',\n                 *   'crooked3',\n                 *   'crooked5'\n                 * ]\n                 *\n                 */\n                items: [\n                    'elliott3',\n                    'elliott5',\n                    'crooked3',\n                    'crooked5'\n                ],\n                crooked3: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'crooked-3.svg'\n                },\n                crooked5: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'crooked-5.svg'\n                },\n                elliott3: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'elliott-3.svg'\n                },\n                elliott5: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'elliott-5.svg'\n                }\n            },\n            verticalLabels: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'verticalCounter',\n                 *   'verticalLabel',\n                 *   'verticalArrow'\n                 * ]\n                 */\n                items: [\n                    'verticalCounter',\n                    'verticalLabel',\n                    'verticalArrow'\n                ],\n                verticalCounter: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-counter.svg'\n                },\n                verticalLabel: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-label.svg'\n                },\n                verticalArrow: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'vertical-arrow.svg'\n                }\n            },\n            advanced: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'fibonacci',\n                 *   'fibonacciTimeZones',\n                 *   'pitchfork',\n                 *   'parallelChannel',\n                 *   'timeCycles'\n                 * ]\n                 */\n                items: [\n                    'fibonacci',\n                    'fibonacciTimeZones',\n                    'pitchfork',\n                    'parallelChannel',\n                    'timeCycles'\n                ],\n                pitchfork: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'pitchfork.svg'\n                },\n                fibonacci: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'fibonacci.svg'\n                },\n                fibonacciTimeZones: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'fibonacci-timezone.svg'\n                },\n                parallelChannel: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'parallel-channel.svg'\n                },\n                timeCycles: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type {string}\n                     */\n                    symbol: 'time-cycles.svg'\n                }\n            },\n            measure: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'measureXY',\n                 *   'measureX',\n                 *   'measureY'\n                 * ]\n                 */\n                items: [\n                    'measureXY',\n                    'measureX',\n                    'measureY'\n                ],\n                measureX: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'measure-x.svg'\n                },\n                measureY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'measure-y.svg'\n                },\n                measureXY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'measure-xy.svg'\n                }\n            },\n            toggleAnnotations: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'annotations-visible.svg'\n            },\n            currentPriceIndicator: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'current-price-show.svg'\n            },\n            indicators: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'indicators.svg'\n            },\n            zoomChange: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'zoomX',\n                 *   'zoomY',\n                 *   'zoomXY'\n                 * ]\n                 */\n                items: [\n                    'zoomX',\n                    'zoomY',\n                    'zoomXY'\n                ],\n                zoomX: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'zoom-x.svg'\n                },\n                zoomY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'zoom-y.svg'\n                },\n                zoomXY: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'zoom-xy.svg'\n                }\n            },\n            typeChange: {\n                /**\n                 * A collection of strings pointing to config options for\n                 * the items.\n                 *\n                 * @type {Array}\n                 * @default [\n                 *   'typeOHLC',\n                 *   'typeLine',\n                 *   'typeCandlestick'\n                 *   'typeHollowCandlestick'\n                 * ]\n                 */\n                items: [\n                    'typeOHLC',\n                    'typeLine',\n                    'typeCandlestick',\n                    'typeHollowCandlestick',\n                    'typeHLC',\n                    'typeHeikinAshi'\n                ],\n                typeOHLC: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-ohlc.svg'\n                },\n                typeLine: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-line.svg'\n                },\n                typeCandlestick: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-candlestick.svg'\n                },\n                typeHLC: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-hlc.svg'\n                },\n                typeHeikinAshi: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-heikin-ashi.svg'\n                },\n                typeHollowCandlestick: {\n                    /**\n                     * A predefined background symbol for the button.\n                     *\n                     * @type   {string}\n                     */\n                    symbol: 'series-hollow-candlestick.svg'\n                }\n            },\n            fullScreen: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'fullscreen.svg'\n            },\n            saveChart: {\n                /**\n                 * A predefined background symbol for the button.\n                 *\n                 * @type   {string}\n                 */\n                symbol: 'save-chart.svg'\n            }\n        },\n        /**\n         * Whether the stock tools toolbar is visible.\n         *\n         * @since 11.4.4\n         */\n        visible: true\n    }\n};\n/* *\n *\n *  Default Exports\n *\n * */\nvar StockToolsDefaults = {\n    lang: StockToolsDefaults_lang,\n    stockTools: stockTools\n};\n/* harmony default export */ var StockTools_StockToolsDefaults = (StockToolsDefaults);\n\n;// ./code/es5/es-modules/Stock/StockTools/StockTools.js\n/**\n *\n *  Events generator for Stock tools\n *\n *  (c) 2009-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar StockTools_setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n\nvar StockTools_getAssignedAxis = NavigationBindingsUtilities.getAssignedAxis;\n\n\n\nvar StockTools_isNotNavigatorYAxis = StockTools_StockToolsUtilities.isNotNavigatorYAxis, StockTools_isPriceIndicatorEnabled = StockTools_StockToolsUtilities.isPriceIndicatorEnabled;\n\nvar correctFloat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).correctFloat, StockTools_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, StockTools_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, StockTools_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(NavigationBindingsClass) {\n    var _a;\n    var navigationProto = NavigationBindingsClass.prototype;\n    if (!((_a = navigationProto.utils) === null || _a === void 0 ? void 0 : _a.manageIndicators)) {\n        // Extends NavigationBindings to support indicators and resizers:\n        navigationProto.getYAxisPositions = navigationGetYAxisPositions;\n        navigationProto.getYAxisResizers = navigationGetYAxisResizers;\n        navigationProto.recalculateYAxisPositions =\n            navigationRecalculateYAxisPositions;\n        navigationProto.resizeYAxes = navigationResizeYAxes;\n        navigationProto.utils = navigationProto.utils || {};\n        navigationProto.utils.indicatorsWithAxes = StockTools_StockToolsUtilities.indicatorsWithAxes;\n        navigationProto.utils.indicatorsWithVolume = StockTools_StockToolsUtilities.indicatorsWithVolume;\n        navigationProto.utils.getAssignedAxis = StockTools_getAssignedAxis;\n        navigationProto.utils.isPriceIndicatorEnabled = StockTools_isPriceIndicatorEnabled;\n        navigationProto.utils.manageIndicators = StockTools_StockToolsUtilities.manageIndicators;\n        StockTools_setOptions(StockTools_StockToolsDefaults);\n        StockTools_setOptions({\n            navigation: {\n                bindings: StockTools_StockToolsBindings\n            }\n        });\n    }\n}\n/**\n * Get current positions for all yAxes. If new axis does not have position,\n * returned is default height and last available top place.\n *\n * @private\n * @function Highcharts.NavigationBindings#getYAxisPositions\n *\n * @param {Array<Highcharts.Axis>} yAxes\n *        Array of yAxes available in the chart.\n *\n * @param {number} plotHeight\n *        Available height in the chart.\n *\n * @param {number} defaultHeight\n *        Default height in percents.\n *\n * @param {Highcharts.AxisPositions} removedYAxisProps\n *        Height and top value of the removed yAxis in percents.\n *\n * @return {Highcharts.YAxisPositions}\n *         An object containing an array of calculated positions\n *         in percentages. Format: `{top: Number, height: Number}`\n *         and maximum value of top + height of axes.\n */\nfunction navigationGetYAxisPositions(yAxes, plotHeight, defaultHeight, removedYAxisProps) {\n    var allAxesHeight = 0,\n        previousAxisHeight,\n        removedHeight,\n        removedTop;\n    /** @private */\n    function isPercentage(prop) {\n        return StockTools_defined(prop) && !StockTools_isNumber(prop) && prop.match('%');\n    }\n    if (removedYAxisProps) {\n        removedTop = correctFloat((parseFloat(removedYAxisProps.top) / 100));\n        removedHeight = correctFloat((parseFloat(removedYAxisProps.height) / 100));\n    }\n    var positions = yAxes.map(function (yAxis,\n        index) {\n            var height = correctFloat(isPercentage(yAxis.options.height) ?\n                parseFloat(yAxis.options.height) / 100 :\n                yAxis.height / plotHeight),\n        top = correctFloat(isPercentage(yAxis.options.top) ?\n                parseFloat(yAxis.options.top) / 100 :\n                (yAxis.top - yAxis.chart.plotTop) / plotHeight);\n        if (!removedHeight) {\n            // New axis' height is NaN so we can check if\n            // the axis is newly created this way\n            if (!StockTools_isNumber(height)) {\n                // Check if the previous axis is the\n                // indicator axis (every indicator inherits from sma)\n                height = yAxes[index - 1].series\n                    .every(function (s) { return s.is('sma'); }) ?\n                    previousAxisHeight : defaultHeight / 100;\n            }\n            if (!StockTools_isNumber(top)) {\n                top = allAxesHeight;\n            }\n            previousAxisHeight = height;\n            allAxesHeight = correctFloat(Math.max(allAxesHeight, (top || 0) + (height || 0)));\n        }\n        else {\n            // Move all axes which were below the removed axis up.\n            if (top > removedTop) {\n                top -= removedHeight;\n            }\n            allAxesHeight = Math.max(allAxesHeight, (top || 0) + (height || 0));\n        }\n        return {\n            height: height * 100,\n            top: top * 100\n        };\n    });\n    return { positions: positions, allAxesHeight: allAxesHeight };\n}\n/**\n * Get current resize options for each yAxis. Note that each resize is\n * linked to the next axis, except the last one which shouldn't affect\n * axes in the navigator. Because indicator can be removed with it's yAxis\n * in the middle of yAxis array, we need to bind closest yAxes back.\n *\n * @private\n * @function Highcharts.NavigationBindings#getYAxisResizers\n *\n * @param {Array<Highcharts.Axis>} yAxes\n *        Array of yAxes available in the chart\n *\n * @return {Array<object>}\n *         An array of resizer options.\n *         Format: `{enabled: Boolean, controlledAxis: { next: [String]}}`\n */\nfunction navigationGetYAxisResizers(yAxes) {\n    var resizers = [];\n    yAxes.forEach(function (_yAxis, index) {\n        var nextYAxis = yAxes[index + 1];\n        // We have next axis, bind them:\n        if (nextYAxis) {\n            resizers[index] = {\n                enabled: true,\n                controlledAxis: {\n                    next: [\n                        StockTools_pick(nextYAxis.options.id, nextYAxis.index)\n                    ]\n                }\n            };\n        }\n        else {\n            // Remove binding:\n            resizers[index] = {\n                enabled: false\n            };\n        }\n    });\n    return resizers;\n}\n/**\n * Utility to modify calculated positions according to the remaining/needed\n * space. Later, these positions are used in `yAxis.update({ top, height })`\n *\n * @private\n * @function Highcharts.NavigationBindings#recalculateYAxisPositions\n * @param {Array<Highcharts.Dictionary<number>>} positions\n * Default positions of all yAxes.\n * @param {number} changedSpace\n * How much space should be added or removed.\n * @param {boolean} modifyHeight\n * Update only `top` or both `top` and `height`.\n * @param {number} adder\n * `-1` or `1`, to determine whether we should add or remove space.\n *\n * @return {Array<object>}\n *         Modified positions,\n */\nfunction navigationRecalculateYAxisPositions(positions, changedSpace, modifyHeight, adder) {\n    positions.forEach(function (position, index) {\n        var prevPosition = positions[index - 1];\n        position.top = !prevPosition ? 0 :\n            correctFloat(prevPosition.height + prevPosition.top);\n        if (modifyHeight) {\n            position.height = correctFloat(position.height + adder * changedSpace);\n        }\n    });\n    return positions;\n}\n/**\n * Resize all yAxes (except navigator) to fit the plotting height. Method\n * checks if new axis is added, if the new axis will fit under previous\n * axes it is placed there. If not, current plot area is scaled\n * to make room for new axis.\n *\n * If axis is removed, the current plot area stretches to fit into 100%\n * of the plot area.\n *\n * @private\n */\nfunction navigationResizeYAxes(removedYAxisProps) {\n    // The height of the new axis before rescalling. In %, but as a number.\n    var defaultHeight = 20;\n    var chart = this.chart, \n        // Only non-navigator axes\n        yAxes = chart.yAxis.filter(StockTools_isNotNavigatorYAxis),\n        plotHeight = chart.plotHeight, \n        // Gather current heights (in %)\n        _a = this.getYAxisPositions(yAxes,\n        plotHeight,\n        defaultHeight,\n        removedYAxisProps),\n        positions = _a.positions,\n        allAxesHeight = _a.allAxesHeight,\n        resizers = this.getYAxisResizers(yAxes);\n    // Check if the axis is being either added or removed and\n    // if the new indicator axis will fit under existing axes.\n    // if so, there is no need to scale them.\n    if (!removedYAxisProps &&\n        allAxesHeight <= correctFloat(0.8 + defaultHeight / 100)) {\n        positions[positions.length - 1] = {\n            height: defaultHeight,\n            top: correctFloat(allAxesHeight * 100 - defaultHeight)\n        };\n    }\n    else {\n        positions.forEach(function (position) {\n            position.height = (position.height / (allAxesHeight * 100)) * 100;\n            position.top = (position.top / (allAxesHeight * 100)) * 100;\n        });\n    }\n    positions.forEach(function (position, index) {\n        yAxes[index].update({\n            height: position.height + '%',\n            top: position.top + '%',\n            resize: resizers[index],\n            offset: 0\n        }, false);\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar StockTools = {\n    compose: compose\n};\n/* harmony default export */ var StockTools_StockTools = (StockTools);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n;// ./code/es5/es-modules/Stock/StockTools/StockToolbar.js\n/* *\n *\n *  GUI generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nvar StockToolbar_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement, css = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).css, StockToolbar_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, StockToolbar_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, getStyle = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getStyle, StockToolbar_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, StockToolbar_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, StockToolbar_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\nvar StockToolbar_shallowArraysEqual = StockTools_StockToolsUtilities.shallowArraysEqual;\n/* *\n *\n *  Classes\n *\n * */\n/**\n * Toolbar Class\n *\n * @private\n * @class\n *\n * @param {object} options\n *        Options of toolbar\n *\n * @param {Highcharts.Dictionary<string>|undefined} langOptions\n *        Language options\n *\n * @param {Highcharts.Chart} chart\n *        Reference to chart\n */\nvar Toolbar = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function Toolbar(options, langOptions, chart) {\n        this.width = 0;\n        this.isDirty = false;\n        this.chart = chart;\n        this.options = options;\n        this.lang = langOptions;\n        // Set url for icons.\n        this.iconsURL = this.getIconsURL();\n        this.guiEnabled = options.enabled;\n        this.visible = StockToolbar_pick(options.visible, true);\n        this.guiClassName = options.className;\n        this.toolbarClassName = options.toolbarClassName;\n        // General events collection which should be removed upon\n        // destroy/update:\n        this.eventsToUnbind = [];\n        if (this.guiEnabled) {\n            this.createContainer();\n            this.createButtons();\n            this.showHideNavigation();\n        }\n        StockToolbar_fireEvent(this, 'afterInit');\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create and set up stockTools buttons with their events and submenus.\n     * @private\n     */\n    Toolbar.prototype.createButtons = function () {\n        var _this = this;\n        var lang = this.lang,\n            guiOptions = this.options,\n            toolbar = this.toolbar,\n            buttons = guiOptions.buttons,\n            defs = guiOptions.definitions,\n            allButtons = toolbar.childNodes;\n        this.buttonList = buttons;\n        // Create buttons\n        buttons.forEach(function (btnName) {\n            var button = _this.addButton(toolbar,\n                defs,\n                btnName,\n                lang);\n            _this.eventsToUnbind.push(StockToolbar_addEvent(button.buttonWrapper, 'click', function () { return _this.eraseActiveButtons(allButtons, button.buttonWrapper); }));\n            if (StockToolbar_isArray(defs[btnName].items)) {\n                // Create submenu buttons\n                _this.addSubmenu(button, defs[btnName]);\n            }\n        });\n    };\n    /**\n     * Create submenu (list of buttons) for the option. In example main button\n     * is Line, in submenu will be buttons with types of lines.\n     *\n     * @private\n     *\n     * @param {Highcharts.Dictionary<Highcharts.HTMLDOMElement>} parentBtn\n     *        Button which has submenu\n     *\n     * @param {Highcharts.StockToolsGuiDefinitionsButtonsOptions} button\n     *        List of all buttons\n     */\n    Toolbar.prototype.addSubmenu = function (parentBtn, button) {\n        var _this = this;\n        var submenuArrow = parentBtn.submenuArrow,\n            buttonWrapper = parentBtn.buttonWrapper,\n            buttonWidth = getStyle(buttonWrapper, 'width'),\n            wrapper = this.wrapper,\n            menuWrapper = this.listWrapper,\n            allButtons = this.toolbar.childNodes, \n            // Create submenu container\n            submenuWrapper = this.submenu = createElement('ul', {\n                className: 'highcharts-submenu-wrapper'\n            },\n            void 0,\n            buttonWrapper);\n        // Create submenu buttons and select the first one\n        this.addSubmenuItems(buttonWrapper, button);\n        // Show / hide submenu\n        this.eventsToUnbind.push(StockToolbar_addEvent(submenuArrow, 'click', function (e) {\n            e.stopPropagation();\n            // Erase active class on all other buttons\n            _this.eraseActiveButtons(allButtons, buttonWrapper);\n            // Hide menu\n            if (buttonWrapper.className\n                .indexOf('highcharts-current') >= 0) {\n                menuWrapper.style.width =\n                    menuWrapper.startWidth + 'px';\n                buttonWrapper.classList.remove('highcharts-current');\n                submenuWrapper.style.display = 'none';\n            }\n            else {\n                // Show menu\n                // to calculate height of element\n                submenuWrapper.style.display = 'block';\n                var topMargin = submenuWrapper.offsetHeight -\n                        buttonWrapper.offsetHeight - 3;\n                // Calculate position of submenu in the box\n                // if submenu is inside, reset top margin\n                if (\n                // Cut on the bottom\n                !(submenuWrapper.offsetHeight +\n                    buttonWrapper.offsetTop >\n                    wrapper.offsetHeight &&\n                    // Cut on the top\n                    buttonWrapper.offsetTop > topMargin)) {\n                    topMargin = 0;\n                }\n                // Apply calculated styles\n                css(submenuWrapper, {\n                    top: -topMargin + 'px',\n                    left: buttonWidth + 3 + 'px'\n                });\n                buttonWrapper.className += ' highcharts-current';\n                menuWrapper.startWidth = wrapper.offsetWidth;\n                menuWrapper.style.width = menuWrapper.startWidth +\n                    getStyle(menuWrapper, 'padding-left') +\n                    submenuWrapper.offsetWidth + 3 + 'px';\n            }\n        }));\n    };\n    /**\n     * Create buttons in submenu\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} buttonWrapper\n     *        Button where submenu is placed\n     *\n     * @param {Highcharts.StockToolsGuiDefinitionsButtonsOptions} button\n     *        List of all buttons options\n     */\n    Toolbar.prototype.addSubmenuItems = function (buttonWrapper, button) {\n        var _this = this;\n        var _self = this,\n            submenuWrapper = this.submenu,\n            lang = this.lang,\n            menuWrapper = this.listWrapper,\n            items = button.items;\n        var submenuBtn;\n        // Add items to submenu\n        items.forEach(function (btnName) {\n            // Add buttons to submenu\n            submenuBtn = _this.addButton(submenuWrapper, button, btnName, lang);\n            _this.eventsToUnbind.push(StockToolbar_addEvent(submenuBtn.mainButton, 'click', function () {\n                _self.switchSymbol(this, buttonWrapper, true);\n                menuWrapper.style.width =\n                    menuWrapper.startWidth + 'px';\n                submenuWrapper.style.display = 'none';\n            }));\n        });\n        // Select first submenu item\n        var firstSubmenuItem = submenuWrapper.querySelectorAll('li > .highcharts-menu-item-btn')[0];\n        // Replace current symbol, in main button, with submenu's button style\n        this.switchSymbol(firstSubmenuItem, false);\n    };\n    /**\n     * Erase active class on all other buttons.\n     * @private\n     */\n    Toolbar.prototype.eraseActiveButtons = function (buttons, currentButton, submenuItems) {\n        [].forEach.call(buttons, function (btn) {\n            if (btn !== currentButton) {\n                btn.classList.remove('highcharts-current');\n                btn.classList.remove('highcharts-active');\n                submenuItems =\n                    btn.querySelectorAll('.highcharts-submenu-wrapper');\n                // Hide submenu\n                if (submenuItems.length > 0) {\n                    submenuItems[0].style.display = 'none';\n                }\n            }\n        });\n    };\n    /**\n     * Create single button. Consist of HTML elements `li`, `button`, and (if\n     * exists) submenu container.\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} target\n     *        HTML reference, where button should be added\n     *\n     * @param {object} options\n     *        All options, by btnName refer to particular button\n     *\n     * @param {string} btnName\n     *        Button name of functionality mapped for specific class\n     *\n     * @param {Highcharts.Dictionary<string>} lang\n     *        All titles, by btnName refer to particular button\n     *\n     * @return {object}\n     *         References to all created HTML elements\n     */\n    Toolbar.prototype.addButton = function (target, options, btnName, lang) {\n        if (lang === void 0) { lang = {}; }\n        var btnOptions = options[btnName],\n            items = btnOptions.items,\n            classMapping = Toolbar.prototype.classMapping,\n            userClassName = btnOptions.className || '';\n        // Main button wrapper\n        var buttonWrapper = createElement('li', {\n                className: StockToolbar_pick(classMapping[btnName], '') + ' ' + userClassName,\n                title: lang[btnName] || btnName\n            },\n            void 0,\n            target);\n        // Single button\n        var elementType = (btnOptions.elementType || 'button');\n        var mainButton = createElement(elementType, {\n                className: 'highcharts-menu-item-btn'\n            },\n            void 0,\n            buttonWrapper);\n        // Submenu\n        if (items && items.length) {\n            // Arrow is a hook to show / hide submenu\n            var submenuArrow = createElement('button', {\n                    className: 'highcharts-submenu-item-arrow ' +\n                        'highcharts-arrow-right'\n                },\n                void 0,\n                buttonWrapper);\n            submenuArrow.style.backgroundImage = 'url(' +\n                this.iconsURL + 'arrow-bottom.svg)';\n            return {\n                buttonWrapper: buttonWrapper,\n                mainButton: mainButton,\n                submenuArrow: submenuArrow\n            };\n        }\n        mainButton.style.backgroundImage = 'url(' +\n            this.iconsURL + btnOptions.symbol + ')';\n        return {\n            buttonWrapper: buttonWrapper,\n            mainButton: mainButton\n        };\n    };\n    /**\n     * Create navigation's HTML elements: container and arrows.\n     * @private\n     */\n    Toolbar.prototype.addNavigation = function () {\n        var wrapper = this.wrapper;\n        // Arrow wrapper\n        this.arrowWrapper = createElement('div', {\n            className: 'highcharts-arrow-wrapper'\n        });\n        this.arrowUp = createElement('div', {\n            className: 'highcharts-arrow-up'\n        }, void 0, this.arrowWrapper);\n        this.arrowUp.style.backgroundImage =\n            'url(' + this.iconsURL + 'arrow-right.svg)';\n        this.arrowDown = createElement('div', {\n            className: 'highcharts-arrow-down'\n        }, void 0, this.arrowWrapper);\n        this.arrowDown.style.backgroundImage =\n            'url(' + this.iconsURL + 'arrow-right.svg)';\n        wrapper.insertBefore(this.arrowWrapper, wrapper.childNodes[0]);\n        // Attach scroll events\n        this.scrollButtons();\n    };\n    /**\n     * Add events to navigation (two arrows) which allows user to scroll\n     * top/down GUI buttons, if container's height is not enough.\n     * @private\n     */\n    Toolbar.prototype.scrollButtons = function () {\n        var wrapper = this.wrapper,\n            toolbar = this.toolbar,\n            step = 0.1 * wrapper.offsetHeight; // 0.1 = 10%\n            var targetY = 0;\n        this.eventsToUnbind.push(StockToolbar_addEvent(this.arrowUp, 'click', function () {\n            if (targetY > 0) {\n                targetY -= step;\n                toolbar.style.marginTop = -targetY + 'px';\n            }\n        }));\n        this.eventsToUnbind.push(StockToolbar_addEvent(this.arrowDown, 'click', function () {\n            if (wrapper.offsetHeight + targetY <=\n                toolbar.offsetHeight + step) {\n                targetY += step;\n                toolbar.style.marginTop = -targetY + 'px';\n            }\n        }));\n    };\n    /*\n     * Create the stockTools container and sets up event bindings.\n     *\n     */\n    Toolbar.prototype.createContainer = function () {\n        var _this = this;\n        var chart = this.chart,\n            guiOptions = this.options,\n            container = chart.container,\n            navigation = chart.options.navigation,\n            bindingsClassName = navigation === null || navigation === void 0 ? void 0 : navigation.bindingsClassName,\n            self = this;\n        var listWrapper,\n            toolbar;\n        // Create main container\n        var wrapper = this.wrapper = createElement('div', {\n                className: 'highcharts-stocktools-wrapper ' +\n                    guiOptions.className + ' ' + bindingsClassName\n            });\n        container.appendChild(wrapper);\n        this.showHideBtn = createElement('div', {\n            className: 'highcharts-toggle-toolbar highcharts-arrow-left'\n        }, void 0, wrapper);\n        // Toggle menu\n        this.eventsToUnbind.push(StockToolbar_addEvent(this.showHideBtn, 'click', function () {\n            _this.update({\n                gui: {\n                    visible: !self.visible\n                }\n            });\n        }));\n        // Mimic event behaviour of being outside chart.container\n        [\n            'mousedown',\n            'mousemove',\n            'click',\n            'touchstart'\n        ].forEach(function (eventType) {\n            StockToolbar_addEvent(wrapper, eventType, function (e) {\n                return e.stopPropagation();\n            });\n        });\n        StockToolbar_addEvent(wrapper, 'mouseover', function (e) { var _a; return (_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.onContainerMouseLeave(e); });\n        // Toolbar\n        this.toolbar = toolbar = createElement('ul', {\n            className: 'highcharts-stocktools-toolbar ' +\n                guiOptions.toolbarClassName\n        });\n        // Add container for list of buttons\n        this.listWrapper = listWrapper = createElement('div', {\n            className: 'highcharts-menu-wrapper'\n        });\n        wrapper.insertBefore(listWrapper, wrapper.childNodes[0]);\n        listWrapper.insertBefore(toolbar, listWrapper.childNodes[0]);\n        this.showHideToolbar();\n        // Add navigation which allows user to scroll down / top GUI buttons\n        this.addNavigation();\n    };\n    /**\n     * Function called in redraw verifies if the navigation should be visible.\n     * @private\n     */\n    Toolbar.prototype.showHideNavigation = function () {\n        // Arrows\n        // 50px space for arrows\n        if (this.visible &&\n            this.toolbar.offsetHeight > (this.wrapper.offsetHeight - 50)) {\n            this.arrowWrapper.style.display = 'block';\n        }\n        else {\n            // Reset margin if whole toolbar is visible\n            this.toolbar.style.marginTop = '0px';\n            // Hide arrows\n            this.arrowWrapper.style.display = 'none';\n        }\n    };\n    /**\n     * Create button which shows or hides GUI toolbar.\n     * @private\n     */\n    Toolbar.prototype.showHideToolbar = function () {\n        var wrapper = this.wrapper,\n            toolbar = this.listWrapper,\n            submenu = this.submenu, \n            // Show hide toolbar\n            showHideBtn = this.showHideBtn;\n        var visible = this.visible;\n        showHideBtn.style.backgroundImage =\n            'url(' + this.iconsURL + 'arrow-right.svg)';\n        if (!visible) {\n            // Hide\n            if (submenu) {\n                submenu.style.display = 'none';\n            }\n            showHideBtn.style.left = '0px';\n            visible = this.visible = false;\n            toolbar.classList.add('highcharts-hide');\n            showHideBtn.classList.add('highcharts-arrow-right');\n            wrapper.style.height = showHideBtn.offsetHeight + 'px';\n        }\n        else {\n            wrapper.style.height = '100%';\n            toolbar.classList.remove('highcharts-hide');\n            showHideBtn.classList.remove('highcharts-arrow-right');\n            showHideBtn.style.top = getStyle(toolbar, 'padding-top') + 'px';\n            showHideBtn.style.left = (wrapper.offsetWidth +\n                getStyle(toolbar, 'padding-left')) + 'px';\n        }\n    };\n    /*\n     * In main GUI button, replace icon and class with submenu button's\n     * class / symbol.\n     *\n     * @param {HTMLDOMElement} - submenu button\n     * @param {Boolean} - true or false\n     *\n     */\n    Toolbar.prototype.switchSymbol = function (button, redraw) {\n        var buttonWrapper = button.parentNode,\n            buttonWrapperClass = buttonWrapper.className, \n            // Main button in first level og GUI\n            mainNavButton = buttonWrapper.parentNode.parentNode;\n        // If the button is disabled, don't do anything\n        if (buttonWrapperClass.indexOf('highcharts-disabled-btn') > -1) {\n            return;\n        }\n        // Set class\n        mainNavButton.className = '';\n        if (buttonWrapperClass) {\n            mainNavButton.classList.add(buttonWrapperClass.trim());\n        }\n        // Set icon\n        mainNavButton\n            .querySelectorAll('.highcharts-menu-item-btn')[0]\n            .style.backgroundImage =\n            button.style.backgroundImage;\n        // Set active class\n        if (redraw) {\n            this.toggleButtonActiveClass(mainNavButton);\n        }\n    };\n    /**\n     * Set select state (active class) on button.\n     * @private\n     */\n    Toolbar.prototype.toggleButtonActiveClass = function (button) {\n        var classList = button.classList;\n        if (classList.contains('highcharts-active')) {\n            classList.remove('highcharts-active');\n        }\n        else {\n            classList.add('highcharts-active');\n        }\n    };\n    /**\n     * Remove active class from all buttons except defined.\n     * @private\n     */\n    Toolbar.prototype.unselectAllButtons = function (button) {\n        var activeBtns = button.parentNode\n                .querySelectorAll('.highcharts-active');\n        [].forEach.call(activeBtns, function (activeBtn) {\n            if (activeBtn !== button) {\n                activeBtn.classList.remove('highcharts-active');\n            }\n        });\n    };\n    /**\n     * Update GUI with given options.\n     * @private\n     */\n    Toolbar.prototype.update = function (options, redraw) {\n        this.isDirty = !!options.gui.definitions;\n        StockToolbar_merge(true, this.chart.options.stockTools, options);\n        StockToolbar_merge(true, this.options, options.gui);\n        this.visible = StockToolbar_pick(this.options.visible && this.options.enabled, true);\n        // If Stock Tools are updated, then bindings should be updated too:\n        if (this.chart.navigationBindings) {\n            this.chart.navigationBindings.update();\n        }\n        this.chart.isDirtyBox = true;\n        if (StockToolbar_pick(redraw, true)) {\n            this.chart.redraw();\n        }\n    };\n    /**\n     * Destroy all HTML GUI elements.\n     * @private\n     */\n    Toolbar.prototype.destroy = function () {\n        var stockToolsDiv = this.wrapper,\n            parent = stockToolsDiv && stockToolsDiv.parentNode;\n        this.eventsToUnbind.forEach(function (unbinder) { return unbinder(); });\n        // Remove the empty element\n        if (parent) {\n            parent.removeChild(stockToolsDiv);\n        }\n    };\n    /**\n     * Redraws the toolbar based on the current state of the options.\n     * @private\n     */\n    Toolbar.prototype.redraw = function () {\n        if (this.options.enabled !== this.guiEnabled) {\n            this.handleGuiEnabledChange();\n        }\n        else {\n            if (!this.guiEnabled) {\n                return;\n            }\n            this.updateClassNames();\n            this.updateButtons();\n            this.updateVisibility();\n            this.showHideNavigation();\n            this.showHideToolbar();\n        }\n    };\n    /**\n     * Hadles the change of the `enabled` option.\n     * @private\n     */\n    Toolbar.prototype.handleGuiEnabledChange = function () {\n        if (this.options.enabled === false) {\n            this.destroy();\n            this.visible = false;\n        }\n        if (this.options.enabled === true) {\n            this.createContainer();\n            this.createButtons();\n        }\n        this.guiEnabled = this.options.enabled;\n    };\n    /**\n     * Updates the class names of the GUI and toolbar elements.\n     * @private\n     */\n    Toolbar.prototype.updateClassNames = function () {\n        if (this.options.className !== this.guiClassName) {\n            if (this.guiClassName) {\n                this.wrapper.classList.remove(this.guiClassName);\n            }\n            if (this.options.className) {\n                this.wrapper.classList.add(this.options.className);\n            }\n            this.guiClassName = this.options.className;\n        }\n        if (this.options.toolbarClassName !== this.toolbarClassName) {\n            if (this.toolbarClassName) {\n                this.toolbar.classList.remove(this.toolbarClassName);\n            }\n            if (this.options.toolbarClassName) {\n                this.toolbar.classList.add(this.options.toolbarClassName);\n            }\n            this.toolbarClassName = this.options.toolbarClassName;\n        }\n    };\n    /**\n     * Updates the buttons in the toolbar if the button options have changed.\n     * @private\n     */\n    Toolbar.prototype.updateButtons = function () {\n        if (!StockToolbar_shallowArraysEqual(this.options.buttons, this.buttonList) ||\n            this.isDirty) {\n            this.toolbar.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n            this.createButtons();\n        }\n    };\n    /**\n     * Updates visibility based on current options.\n     * @private\n     */\n    Toolbar.prototype.updateVisibility = function () {\n        if (StockToolbar_defined(this.options.visible)) {\n            this.visible = this.options.visible;\n        }\n    };\n    /**\n     * @private\n     */\n    Toolbar.prototype.getIconsURL = function () {\n        return this.chart.options.navigation.iconsURL ||\n            this.options.iconsURL ||\n            'https://code.highcharts.com/12.2.0/gfx/stock-icons/';\n    };\n    return Toolbar;\n}());\nToolbar.prototype.classMapping = {\n    circle: 'highcharts-circle-annotation',\n    ellipse: 'highcharts-ellipse-annotation',\n    rectangle: 'highcharts-rectangle-annotation',\n    label: 'highcharts-label-annotation',\n    segment: 'highcharts-segment',\n    arrowSegment: 'highcharts-arrow-segment',\n    ray: 'highcharts-ray',\n    arrowRay: 'highcharts-arrow-ray',\n    line: 'highcharts-infinity-line',\n    arrowInfinityLine: 'highcharts-arrow-infinity-line',\n    verticalLine: 'highcharts-vertical-line',\n    horizontalLine: 'highcharts-horizontal-line',\n    crooked3: 'highcharts-crooked3',\n    crooked5: 'highcharts-crooked5',\n    elliott3: 'highcharts-elliott3',\n    elliott5: 'highcharts-elliott5',\n    pitchfork: 'highcharts-pitchfork',\n    fibonacci: 'highcharts-fibonacci',\n    fibonacciTimeZones: 'highcharts-fibonacci-time-zones',\n    parallelChannel: 'highcharts-parallel-channel',\n    measureX: 'highcharts-measure-x',\n    measureY: 'highcharts-measure-y',\n    measureXY: 'highcharts-measure-xy',\n    timeCycles: 'highcharts-time-cycles',\n    verticalCounter: 'highcharts-vertical-counter',\n    verticalLabel: 'highcharts-vertical-label',\n    verticalArrow: 'highcharts-vertical-arrow',\n    currentPriceIndicator: 'highcharts-current-price-indicator',\n    indicators: 'highcharts-indicators',\n    flagCirclepin: 'highcharts-flag-circlepin',\n    flagDiamondpin: 'highcharts-flag-diamondpin',\n    flagSquarepin: 'highcharts-flag-squarepin',\n    flagSimplepin: 'highcharts-flag-simplepin',\n    zoomX: 'highcharts-zoom-x',\n    zoomY: 'highcharts-zoom-y',\n    zoomXY: 'highcharts-zoom-xy',\n    typeLine: 'highcharts-series-type-line',\n    typeOHLC: 'highcharts-series-type-ohlc',\n    typeHLC: 'highcharts-series-type-hlc',\n    typeCandlestick: 'highcharts-series-type-candlestick',\n    typeHollowCandlestick: 'highcharts-series-type-hollowcandlestick',\n    typeHeikinAshi: 'highcharts-series-type-heikinashi',\n    fullScreen: 'highcharts-full-screen',\n    toggleAnnotations: 'highcharts-toggle-annotations',\n    saveChart: 'highcharts-save-chart',\n    separator: 'highcharts-separator'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var StockToolbar = (Toolbar);\n\n;// ./code/es5/es-modules/Stock/StockTools/StockToolsGui.js\n/* *\n *\n *  GUI generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar StockToolsGui_setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n\n\n\nvar StockToolsGui_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, StockToolsGui_getStyle = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getStyle, StockToolsGui_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, StockToolsGui_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Verify if Toolbar should be added.\n * @private\n */\nfunction chartSetStockTools(options) {\n    var chartOptions = this.options,\n        lang = chartOptions.lang,\n        guiOptions = StockToolsGui_merge(chartOptions.stockTools && chartOptions.stockTools.gui,\n        options && options.gui),\n        langOptions = lang && lang.stockTools && lang.stockTools.gui;\n    this.stockTools = new StockToolbar(guiOptions, langOptions, this);\n    if (this.stockTools.guiEnabled) {\n        this.isDirtyBox = true;\n    }\n}\n/**\n * @private\n */\nfunction StockToolsGui_compose(ChartClass, NavigationBindingsClass) {\n    var chartProto = ChartClass.prototype;\n    if (!chartProto.setStockTools) {\n        StockToolsGui_addEvent(ChartClass, 'afterGetContainer', onChartAfterGetContainer);\n        StockToolsGui_addEvent(ChartClass, 'beforeRedraw', onChartBeforeRedraw);\n        StockToolsGui_addEvent(ChartClass, 'beforeRender', onChartBeforeRedraw);\n        StockToolsGui_addEvent(ChartClass, 'destroy', StockToolsGui_onChartDestroy);\n        StockToolsGui_addEvent(ChartClass, 'getMargins', onChartGetMargins, { order: 0 });\n        StockToolsGui_addEvent(ChartClass, 'render', StockToolsGui_onChartRender);\n        chartProto.setStockTools = chartSetStockTools;\n        StockToolsGui_addEvent(NavigationBindingsClass, 'deselectButton', StockToolsGui_onNavigationBindingsDeselectButton);\n        StockToolsGui_addEvent(NavigationBindingsClass, 'selectButton', onNavigationBindingsSelectButton);\n        StockToolsGui_setOptions(StockTools_StockToolsDefaults);\n    }\n}\n/**\n * Run HTML generator\n * @private\n */\nfunction onChartAfterGetContainer() {\n    this.setStockTools();\n}\n/**\n * Handle beforeRedraw and beforeRender\n * @private\n */\nfunction onChartBeforeRedraw() {\n    if (this.stockTools) {\n        this.stockTools.redraw();\n        setOffset(this);\n    }\n}\n/**\n * Function to calculate and set the offset width for stock tools.\n * @private\n */\nfunction setOffset(chart) {\n    var _a;\n    if ((_a = chart.stockTools) === null || _a === void 0 ? void 0 : _a.guiEnabled) {\n        var optionsChart = chart.options.chart;\n        var listWrapper = chart.stockTools.listWrapper;\n        var offsetWidth = listWrapper && ((listWrapper.startWidth +\n                StockToolsGui_getStyle(listWrapper, 'padding-left') +\n                StockToolsGui_getStyle(listWrapper, 'padding-right')) || listWrapper.offsetWidth);\n        chart.stockTools.width = offsetWidth;\n        var dirty = false;\n        if (offsetWidth < chart.plotWidth) {\n            var nextX = StockToolsGui_pick(optionsChart.spacingLeft,\n                optionsChart.spacing && optionsChart.spacing[3], 0) + offsetWidth;\n            var diff = nextX - chart.spacingBox.x;\n            chart.spacingBox.x = nextX;\n            chart.spacingBox.width -= diff;\n            dirty = true;\n        }\n        else if (offsetWidth === 0) {\n            dirty = true;\n        }\n        if (offsetWidth !== chart.stockTools.prevOffsetWidth) {\n            chart.stockTools.prevOffsetWidth = offsetWidth;\n            if (dirty) {\n                chart.isDirtyLegend = true;\n            }\n        }\n    }\n}\n/**\n * @private\n */\nfunction StockToolsGui_onChartDestroy() {\n    if (this.stockTools) {\n        this.stockTools.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartGetMargins() {\n    var _a;\n    var offsetWidth = ((_a = this.stockTools) === null || _a === void 0 ? void 0 : _a.visible) && this.stockTools.guiEnabled ?\n            this.stockTools.width : 0;\n    if (offsetWidth && offsetWidth < this.plotWidth) {\n        this.plotLeft += offsetWidth;\n        this.spacing[3] += offsetWidth;\n    }\n}\n/**\n * Check if the correct price indicator button is displayed, #15029.\n * @private\n */\nfunction StockToolsGui_onChartRender() {\n    var _a,\n        _b;\n    var stockTools = this.stockTools,\n        button = stockTools &&\n            stockTools.toolbar &&\n            stockTools.toolbar.querySelector('.highcharts-current-price-indicator');\n    // Change the initial button background.\n    if (stockTools &&\n        this.navigationBindings &&\n        this.options.series &&\n        button) {\n        if ((_b = (_a = this.navigationBindings.utils) === null || _a === void 0 ? void 0 : _a.isPriceIndicatorEnabled) === null || _b === void 0 ? void 0 : _b.call(_a, this.series)) {\n            button.firstChild.style['background-image'] =\n                'url(\"' + stockTools.getIconsURL() + 'current-price-hide.svg\")';\n        }\n        else {\n            button.firstChild.style['background-image'] =\n                'url(\"' + stockTools.getIconsURL() + 'current-price-show.svg\")';\n        }\n    }\n}\n/**\n * @private\n */\nfunction StockToolsGui_onNavigationBindingsDeselectButton(event) {\n    var className = 'highcharts-submenu-wrapper',\n        gui = this.chart.stockTools;\n    if (gui && gui.guiEnabled) {\n        var button = event.button;\n        // If deselecting a button from a submenu, select state for it's parent\n        if (button.parentNode.className.indexOf(className) >= 0) {\n            button = button.parentNode.parentNode;\n        }\n        button.classList.remove('highcharts-active');\n    }\n}\n/**\n * Communication with bindings\n * @private\n */\nfunction onNavigationBindingsSelectButton(event) {\n    var className = 'highcharts-submenu-wrapper',\n        gui = this.chart.stockTools;\n    if (gui && gui.guiEnabled) {\n        var button = event.button;\n        // Unselect other active buttons\n        gui.unselectAllButtons(event.button);\n        // If clicked on a submenu, select state for it's parent\n        if (button.parentNode.className.indexOf(className) >= 0) {\n            button = button.parentNode.parentNode;\n        }\n        // Set active class on the current button\n        gui.toggleButtonActiveClass(button);\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar StockToolsGui = {\n    compose: StockToolsGui_compose\n};\n/* harmony default export */ var StockTools_StockToolsGui = (StockToolsGui);\n\n;// ./code/es5/es-modules/masters/modules/stock-tools.js\n\n\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.NavigationBindings = G.NavigationBindings || Annotations_NavigationBindings;\nG.Toolbar = StockToolbar;\nStockTools_StockTools.compose(G.NavigationBindings);\nStockTools_StockToolsGui.compose(G.Chart, G.NavigationBindings);\n/* harmony default export */ var stock_tools_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__660__", "ChartNavigationComposition", "Additions", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "stock_tools_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "compose", "chart", "navigation", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "_this", "for<PERSON>ach", "Chart_ChartNavigationComposition", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "defined", "isNumber", "pick", "annotationsFieldsTypes", "backgroundColor", "borderColor", "borderRadius", "color", "fill", "fontSize", "labels", "name", "stroke", "title", "coords", "filter", "coord", "extremes", "axis", "getExtremes", "axisMin", "min", "axisMax", "max", "minPointOffset", "value", "isInternal", "predefinedType", "fieldType", "NavigationBindingsDefaults_isNumber", "merge", "NavigationBindingsDefaults", "lang", "popup", "simpleShapes", "lines", "circle", "ellipse", "rectangle", "label", "shapeOptions", "typeOptions", "format", "strokeWidth", "labelOptions", "backgroundColors", "borderWidth", "style", "padding", "height", "shapes", "bindingsClassName", "bindings", "circleAnnotation", "className", "start", "e", "_a", "pointer", "getCoordinates", "coordsX", "NavigationBindingsDefaults_getAssignedAxis", "xAxis", "coordsY", "yAxis", "addAnnotation", "lang<PERSON><PERSON>", "type", "point", "x", "y", "index", "r", "annotationsOptions", "steps", "annotation", "distance", "mockPointOpts", "inverted", "toPixels", "Math", "sqrt", "pow", "chartX", "chartY", "ellipseAnnotation", "points", "ry", "target", "position", "getAbsolutePosition", "translatePoint", "position2", "newR", "getDistanceFromLine", "getYAxis", "newRY", "abs", "toValue", "setYRadius", "rectangleAnnotation", "command", "labelAnnotation", "overflow", "crop", "events", "animation", "defer", "setOptions", "composed", "doc", "win", "addEvent", "attr", "NavigationBindings_defined", "fireEvent", "isArray", "isFunction", "NavigationBindings_isNumber", "isObject", "NavigationBindings_merge", "objectEach", "NavigationBindings_pick", "pushUnique", "onAnnotationRemove", "navigationBindings", "deselectAnnotation", "onChartDestroy", "destroy", "onChartLoad", "NavigationBindings", "initEvents", "initUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledClassName", "buttonsEnabled_1", "series", "visible", "container", "container_1", "boundClassNames", "buttonNode", "querySelectorAll", "i", "length", "button", "cls", "noDataState", "indexOf", "classList", "remove", "onNavigationBindingsClosePopup", "onNavigationBindingsDeselectButton", "selectedButtonElement", "selectableAnnotation", "annotationType", "touchStartX", "touchStartY", "originalClick", "defaultOptions", "click", "selectAndShowPopup", "eventArguments", "prevAnnotation", "activeAnnotation", "setControlPointsVisibility", "formType", "annotationToFields", "onSubmit", "data", "actionType", "removeAnnotation", "config", "fieldsToOptions", "fields", "crosshairY", "enabled", "crosshairX", "touchstart", "touches", "clientX", "clientY", "touchend", "changedTouches", "eventsToUnbind", "getElementsByClassName", "AnnotationClass", "ChartClass", "types", "getCoords", "NavigationBindings_getAssignedAxis", "bindingsContainer", "subContainer", "event", "getButtonEvents", "contains", "bindingsButtonClick", "callback", "eventName", "passive", "cancelClick", "isInsidePlot", "plotLeft", "plotTop", "visiblePlotOnly", "bindingsChartClick", "isTouchDevice", "bindingsContainerMouseMove", "clickEvent", "svgContainer", "renderer", "boxWrapper", "shouldEventBeFired", "nextEvent", "currentUserDetails", "coll", "mouseMoveEvent", "<PERSON><PERSON><PERSON><PERSON>", "init", "addClass", "stockTools", "removeClass", "parentNode", "closestPolyfill", "el", "s", "ElementProto", "Element", "elementMatches", "matches", "msMatchesSelector", "webkitMatchesSelector", "ret", "closest", "parentElement", "nodeType", "setTimeout", "stepIndex", "end", "_container", "moveEvent", "field", "parsedValue", "parseFloat", "path", "split", "<PERSON><PERSON><PERSON><PERSON>", "match", "parent_1", "nextName", "editables", "annotationsEditable", "nestedEditables", "nestedOptions", "nonEditables", "annotationsNonEditable", "visualOptions", "traverse", "option", "parentEditables", "parent", "parent<PERSON><PERSON>", "nextParent", "arrayOption", "nestedOption", "nested<PERSON><PERSON>", "toString", "NavigationBindings_getFieldType", "typeOption", "typeKey", "getClickedClassNames", "elemClassName", "element", "classNames", "tagName", "concat", "map", "removeEvents", "unbinder", "background", "innerBackground", "outerBackground", "line", "connector", "verticalLine", "measure", "<PERSON><PERSON><PERSON><PERSON>", "tunnel", "pitchfork", "rect", "crookedLine", "basicAnnotation", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_", "highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_default", "getOptions", "StockToolsUtilities_defined", "StockToolsUtilities_fireEvent", "StockToolsUtilities_isNumber", "<PERSON><PERSON><PERSON>", "indicatorsWithAxes", "indicatorsWithVolume", "attractToPoint", "closestPoint", "distX", "Number", "MAX_VALUE", "StockToolsUtilities_getAssignedAxis", "searchPoint", "below", "StockTools_StockToolsUtilities", "addFlagFromForm", "toolbar", "pointConfig", "seriesOptions", "onSeries", "id", "shape", "StockToolsUtilities_getFieldType", "updated", "gui<PERSON><PERSON><PERSON>", "addSeries", "getAssignedAxis", "isNotNavigatorYAxis", "userOptions", "isPriceIndicatorEnabled", "some", "lastVisiblePrice", "lastPrice", "manageIndicators", "parentSeries", "seriesConfig", "linkedTo", "seriesId", "linkedSeries", "removedYAxisProps", "top", "resizeYAxes", "plotOptions", "getDGApproximation", "dataGrouping", "approximation", "addAxis", "offset", "opposite", "text", "tickPixelInterval", "showLastLabel", "align", "params", "volumeSeriesID", "shallowArraysEqual", "b", "updateHeight", "horiz", "updateNthPoint", "startIndex", "updateRectSize", "width", "StockToolsBindings_addFlagFromForm", "StockToolsBindings_attractToPoint", "StockToolsBindings_isNotNavigatorYAxis", "StockToolsBindings_isPriceIndicatorEnabled", "StockToolsBindings_manageIndicators", "StockToolsBindings_updateHeight", "StockToolsBindings_updateNthPoint", "StockToolsBindings_updateRectSize", "StockToolsBindings_fireEvent", "StockToolsBindings_merge", "StockToolsBindings", "segment", "arrowSegment", "markerEnd", "ray", "arrowRay", "infinityLine", "arrowInfinityLine", "horizontalLine", "draggable", "crooked3", "crooked5", "elliott3", "elliott5", "measureX", "selectType", "measureY", "measureXY", "parallelChannel", "controlPoint", "verticalCounter", "timeCycles", "verticalLabel", "verticalArrow", "fibonacciTimeZones", "flagCir<PERSON><PERSON>", "flag<PERSON><PERSON><PERSON><PERSON>", "flagSquarepin", "flagSimplepin", "zoomX", "zooming", "zoomY", "zoomXY", "seriesTypeLine", "useOhlcData", "seriesTypeOhlc", "seriesTypeCandlestick", "seriesTypeHeikinAshi", "seriesTypeHLC", "seriesTypeHollowCandlestick", "fullScreen", "fullscreen", "toggle", "currentPriceIndicator", "gui", "priceIndicatorEnabled", "indicators", "toggleAnnotations", "iconsURL", "getIconsURL", "toggledAnnotations", "annotations", "setVisibility", "<PERSON><PERSON><PERSON><PERSON>", "saveChart", "flags", "yAxes", "is", "localStorage", "setItem", "JSON", "stringify", "StockToolsDefaults", "crookedLines", "advanced", "verticalLabels", "zoomChange", "typeChange", "typeOHLC", "typeLine", "typeCandlestick", "typeHLC", "typeHollowCandlestick", "typeHeikinAshi", "addButton", "saveButton", "edit<PERSON><PERSON><PERSON>", "removeButton", "volume", "noFilterMatch", "searchIndicators", "clearFilter", "period", "periods", "standardDeviation", "periodTenkan", "periodSenkouSpanB", "periodATR", "multiplierATR", "shortPeriod", "<PERSON>P<PERSON><PERSON>", "signalPeriod", "decimals", "algorithm", "topBand", "bottomBand", "initialAccelerationFactor", "maxAccelerationFactor", "increment", "multiplier", "ranges", "highIndex", "lowIndex", "deviation", "xAxisUnit", "factor", "fastAvgPeriod", "slowAvgPeriod", "average", "indicatorAliases", "abands", "bb", "dema", "ema", "ikh", "keltnerchannels", "linearRegression", "pivotpoints", "pc", "priceenvelopes", "psar", "sma", "supertrend", "tema", "vbp", "vwap", "wma", "zigzag", "apo", "ad", "aroon", "aroonoscillator", "atr", "ao", "cci", "chaikin", "cmf", "cmo", "disparityindex", "dmi", "dpo", "klinger", "linearRegressionAngle", "linearRegressionIntercept", "linearRegressionSlope", "macd", "mfi", "momentum", "natr", "obv", "ppo", "roc", "rsi", "slowstochastic", "stochastic", "trix", "<PERSON><PERSON><PERSON><PERSON>", "toolbarClassName", "buttons", "definitions", "separator", "elementType", "symbol", "items", "StockTools_setOptions", "StockTools_isNotNavigatorYAxis", "StockTools_isPriceIndicatorEnabled", "correctFloat", "StockTools_defined", "StockTools_isNumber", "StockTools_pick", "navigationGetYAxisPositions", "plotHeight", "defaultHeight", "previousAxisHeight", "removedHeight", "removedTop", "allAxesHeight", "isPercentage", "positions", "every", "navigationGetYAxisResizers", "resizers", "_yAxis", "nextYAxis", "controlledAxis", "next", "navigationRecalculateYAxisPositions", "changedSpace", "modifyHeight", "adder", "prevPosition", "navigationResizeYAxes", "getYAxisPositions", "getYAxisResizers", "resize", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "StockToolbar_addEvent", "createElement", "css", "StockToolbar_defined", "StockToolbar_fireEvent", "getStyle", "StockToolbar_isArray", "StockToolbar_merge", "StockToolbar_pick", "StockToolbar_shallowArraysEqual", "<PERSON><PERSON><PERSON>", "langOptions", "isDirty", "guiClassName", "createContainer", "createButtons", "showHideNavigation", "guiOptions", "defs", "allButtons", "childNodes", "buttonList", "btnName", "buttonWrapper", "eraseActiveButtons", "addSubmenu", "parentBtn", "submenuArrow", "buttonWidth", "wrapper", "menuWrapper", "listWrapper", "submenuWrapper", "submenu", "addSubmenuItems", "stopPropagation", "startWidth", "display", "<PERSON><PERSON><PERSON><PERSON>", "offsetHeight", "offsetTop", "left", "offsetWidth", "submenuBtn", "_self", "mainButton", "switchSymbol", "firstSubmenuItem", "currentButton", "submenuItems", "btn", "btnOptions", "classMapping", "userClassName", "backgroundImage", "addNavigation", "arrowWrapper", "arrowUp", "arrowDown", "insertBefore", "scrollButtons", "step", "targetY", "marginTop", "self", "append<PERSON><PERSON><PERSON>", "showHideBtn", "eventType", "onContainerMouseLeave", "showHideToolbar", "add", "buttonWrapperClass", "mainNavButton", "trim", "toggleButtonActiveClass", "unselectAllButtons", "activeBtns", "activeBtn", "isDirtyBox", "stockToolsDiv", "<PERSON><PERSON><PERSON><PERSON>", "handleGuiEnabledChange", "updateClassNames", "updateButtons", "updateVisibility", "innerHTML", "emptyHTML", "StockToolsGui_setOptions", "StockToolsGui_addEvent", "StockToolsGui_getStyle", "StockToolsGui_merge", "StockToolsGui_pick", "chartSetStockTools", "chartOptions", "onChartAfterGetContainer", "setStockTools", "onChartBeforeRedraw", "setOffset", "optionsChart", "dirty", "plot<PERSON>id<PERSON>", "nextX", "spacingLeft", "spacing", "diff", "spacingBox", "prevOffsetWidth", "isDirtyLegend", "StockToolsGui_onChartDestroy", "onChart<PERSON><PERSON><PERSON><PERSON><PERSON>", "StockToolsGui_onChartRender", "_b", "querySelector", "utils", "StockToolsGui_onNavigationBindingsDeselectButton", "onNavigationBindingsSelectButton", "G", "StockTools_StockTools", "NavigationBindingsClass", "navigationProto", "recalculateYAxisPositions", "StockTools_StockToolsGui", "chartProto", "order", "Chart"], "mappings": "CAaA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,GAAM,EAC3I,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,aAAa,CAAE,CAAC,wBAAwB,SAAS,CAAE,CAAC,wBAAwB,MAAM,CAAC,CAAEJ,GAC5K,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,GAAM,EAE7KJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,GAAM,CAC5I,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACxJ,OAAgB,AAAC,WACP,aACA,IAkHCC,EAgCHC,EAjCJD,EAjHUE,EAAuB,CAE/B,IACC,SAASV,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,SAAShB,CAAM,EACtC,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,WAAa,OAAOlB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASpB,CAAO,CAAEsB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAiB,CAChE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,EAuCjH3B,EArBOA,EAqERA,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAhDjC6B,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHKA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAI9B,EAAU6B,EAAK,EAEnCA,CACX,EAgDA9B,EAA2BC,SAAS,CApChCA,EAA2B,WAMvB,SAASA,EAAU6B,CAAK,EACpB,IAAI,CAACE,OAAO,CAAG,EAAE,CACrB,IAAI,CAACF,KAAK,CAAGA,CACjB,CAyBA,OAZA7B,EAAUqB,SAAS,CAACW,SAAS,CAAG,SAAUC,CAAQ,EAC9C,IAAI,CAACJ,KAAK,CAACC,UAAU,CAACC,OAAO,CAACG,IAAI,CAACD,EACvC,EAIAjC,EAAUqB,SAAS,CAACc,MAAM,CAAG,SAAUC,CAAO,CAAEC,CAAM,EAClD,IAAIC,EAAQ,IAAI,CAChB,IAAI,CAACP,OAAO,CAACQ,OAAO,CAAC,SAAUN,CAAQ,EACnCA,EAASV,IAAI,CAACe,EAAMT,KAAK,CAAEO,EAASC,EACxC,EACJ,EACOrC,CACX,IAQyB,IAAIwC,EAAoCzC,EAGjE0C,EAAmHtC,EAAoB,KACvIuC,EAAuIvC,EAAoBI,CAAC,CAACkC,GAa7JE,EAAU,AAAChB,IAA+EgB,OAAO,CAAEC,EAAW,AAACjB,IAA+EiB,QAAQ,CAAEC,EAAO,AAAClB,IAA+EkB,IAAI,CAWnSC,EAAyB,CACzBC,gBAAiB,SACjBC,YAAa,SACbC,aAAc,SACdC,MAAO,SACPC,KAAM,SACNC,SAAU,SACVC,OAAQ,SACRC,KAAM,SACNC,OAAQ,SACRC,MAAO,QACX,IAkBA,SAAyBC,CAAM,EAC3B,OAAOA,EAAOC,MAAM,CAAC,SAAUC,CAAK,EAChC,IAAIC,EAAWD,EAAME,IAAI,CAACC,WAAW,GACjCC,EAAUH,EAASI,GAAG,CACtBC,EAAUL,EAASM,GAAG,CAGtBC,EAAiBtB,EAAKc,EAAME,IAAI,CAACM,cAAc,CAAE,GACrD,OAAOvB,EAASmB,IAAYnB,EAASqB,IACjCN,EAAMS,KAAK,EAAKL,EAAUI,GAC1BR,EAAMS,KAAK,EAAKH,EAAUE,GAE1B,CAACR,EAAME,IAAI,CAACzB,OAAO,CAACiC,UAAU,AACtC,EAAE,CAAC,EAAE,AACT,IAYA,SAAsBxD,CAAG,CAAEuD,CAAK,EAC5B,IAAIE,EAAiBxB,CAAsB,CAACjC,EAAI,CAC5C0D,EAAY,OAAOH,EAIvB,OAHIzB,EAAQ2B,IACRC,CAAAA,EAAYD,CAAa,EAEtB,CAAA,CACH,OAAU,OACV,OAAU,SACV,QAAW,UACf,CAAA,CAAC,CAACC,EAAU,AAChB,EA2BIC,EAAsC,AAAC7C,IAA+EiB,QAAQ,CAAE6B,EAAQ,AAAC9C,IAA+E8C,KAAK,CAmahMC,EAJD,CAC5BC,KAvZO,CAQP7C,WAAY,CAMR8C,MAAO,CACHC,aAAc,gBACdC,MAAO,QACPC,OAAQ,SACRC,QAAS,UACTC,UAAW,YACXC,MAAO,QACPC,aAAc,gBACdC,YAAa,UACbjC,KAAM,OACNkC,OAAQ,OACRC,YAAa,aACb/B,OAAQ,aACRC,MAAO,QACPF,KAAM,OACNiC,aAAc,gBACdlC,OAAQ,SACRN,gBAAiB,mBACjByC,iBAAkB,oBAClBxC,YAAa,eACbC,aAAc,gBACdwC,YAAa,eACbC,MAAO,QACPC,QAAS,UACTvC,SAAU,YACVF,MAAO,QACP0C,OAAQ,SACRC,OAAQ,eACZ,CACJ,CACJ,EA4WI/D,WAvWa,CAWbgE,kBAAmB,gCA6BnBC,SAAU,CAQNC,iBAAkB,CAEdC,UAAW,+BAEXC,MAAO,SAAUC,CAAC,EAEd,IADIC,EACA3C,EAAS,AAA8B,OAA7B2C,CAAAA,EAAK,IAAI,CAACvE,KAAK,CAACwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAC1FI,EAAU9C,GAAU+C,EAA2C/C,EAAOgD,KAAK,EAC3EC,EAAUjD,GAAU+C,EAA2C/C,EAAOkD,KAAK,EAC3E7E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAE9C,GAAI,AAACyE,GAAYG,EAGjB,OAAO,IAAI,CAAC7E,KAAK,CAAC+E,aAAa,CAACnC,EAAM,CAClCoC,QAAS,SACTC,KAAM,kBACNjB,OAAQ,CAAC,CACDiB,KAAM,SACNC,MAAO,CACHC,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,CAChBqC,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,AAC7B,EACAC,EAAG,CACP,EAAE,AACV,EAAGrF,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAACC,gBAAgB,CACjEoB,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAUlB,CAAC,CAAEmB,CAAU,EACnB,IAGIC,EAHA1B,EAASyB,EAAWlF,OAAO,CAACyD,MAAM,CAClC2B,EAAiB,AAAC3B,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACkB,KAAK,EACpD,CAAC,EAET,GAAIvC,EAAoCgD,EAAcf,KAAK,GACvDjC,EAAoCgD,EAAcb,KAAK,EAAG,CAC1D,IAAIc,EAAW,IAAI,CAAC5F,KAAK,CAAC4F,QAAQ,CAC9BT,EAAI,IAAI,CAACnF,KAAK,CAAC4E,KAAK,CAACe,EAAcf,KAAK,CAAC,CACpCiB,QAAQ,CAACF,EAAcR,CAAC,EAC7BC,EAAI,IAAI,CAACpF,KAAK,CAAC8E,KAAK,CAACa,EAAcb,KAAK,CAAC,CACpCe,QAAQ,CAACF,EAAcP,CAAC,EACjCM,EAAWI,KAAKzD,GAAG,CAACyD,KAAKC,IAAI,CAACD,KAAKE,GAAG,CAACJ,EAAWR,EAAId,EAAE2B,MAAM,CAAGd,EAAIb,EAAE2B,MAAM,CAAE,GAC3EH,KAAKE,GAAG,CAACJ,EAAWT,EAAIb,EAAE4B,MAAM,CAAGd,EAAId,EAAE4B,MAAM,CAAE,IAAK,EAC9D,CACAT,EAAWnF,MAAM,CAAC,CACd0D,OAAQ,CAAC,CACDsB,EAAGI,CACP,EAAE,AACV,EACJ,EACH,AACL,EASAS,kBAAmB,CACf/B,UAAW,gCACXC,MAAO,SAAUC,CAAC,EAEd,IADIC,EACA3C,EAAS,AAA8B,OAA7B2C,CAAAA,EAAK,IAAI,CAACvE,KAAK,CAACwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAC1FI,EAAU9C,GAAU+C,EAA2C/C,EAAOgD,KAAK,EAC3EC,EAAUjD,GAAU+C,EAA2C/C,EAAOkD,KAAK,EAC3E7E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC9C,GAAI,AAACyE,GAAYG,EAGjB,OAAO,IAAI,CAAC7E,KAAK,CAAC+E,aAAa,CAACnC,EAAM,CAClCoC,QAAS,UACTC,KAAM,kBACNjB,OAAQ,CACJ,CACIiB,KAAM,UACNL,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,CACN8D,GAAI,CACR,EACH,AACL,EAAGpG,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAACiC,iBAAiB,CAClEZ,kBAAkB,EAC3B,EACAC,MAAO,CACH,SAAUlB,CAAC,CAAEmB,CAAU,EACnB,IAAIa,EAASb,EAAWzB,MAAM,CAAC,EAAE,CAC7BuC,EAAWD,EAAOE,mBAAmB,CAACF,EAAOF,MAAM,CAAC,EAAE,EAC1DE,EAAOG,cAAc,CAACnC,EAAE2B,MAAM,CAAGM,EAASpB,CAAC,CAAEb,EAAE4B,MAAM,CAAGK,EAASnB,CAAC,CAAE,GACpEkB,EAAO9F,MAAM,CAAC,CAAA,EAClB,EACA,SAAU8D,CAAC,CAAEmB,CAAU,EACnB,IAAIa,EAASb,EAAWzB,MAAM,CAAC,EAAE,CAC7BuC,EAAWD,EAAOE,mBAAmB,CAACF,EAAOF,MAAM,CAAC,EAAE,EACtDM,EAAYJ,EAAOE,mBAAmB,CAACF,EAAOF,MAAM,CAAC,EAAE,EACvDO,EAAOL,EAAOM,mBAAmB,CAACL,EAClCG,EACApC,EAAE2B,MAAM,CACR3B,EAAE4B,MAAM,EACRpB,EAAQwB,EAAOO,QAAQ,GACvBC,EAAQhB,KAAKiB,GAAG,CAACjC,EAAMkC,OAAO,CAAC,GAAKlC,EAAMkC,OAAO,CAACL,IACtDL,EAAOW,UAAU,CAACH,GAClBR,EAAO9F,MAAM,CAAC,CAAA,EAClB,EACH,AACL,EAQA0G,oBAAqB,CAEjB9C,UAAW,kCAEXC,MAAO,SAAUC,CAAC,EAEd,IADIC,EACA3C,EAAS,AAA8B,OAA7B2C,CAAAA,EAAK,IAAI,CAACvE,KAAK,CAACwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAC1FI,EAAU9C,GAAU+C,EAA2C/C,EAAOgD,KAAK,EAC3EC,EAAUjD,GAAU+C,EAA2C/C,EAAOkD,KAAK,EAE/E,GAAI,AAACJ,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,CACjBqC,EAAQF,EAAQ1C,IAAI,CAACqD,KAAK,CAC1BP,EAAQD,EAAQ7C,IAAI,CAACqD,KAAK,CAC1BpF,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC9C,OAAO,IAAI,CAACD,KAAK,CAAC+E,aAAa,CAACnC,EAAM,CAClCoC,QAAS,YACTC,KAAM,kBACNjB,OAAQ,CAAC,CACDiB,KAAM,OACNmB,OAAQ,CACJ,CAAExB,MAAOA,EAAOE,MAAOA,EAAOK,EAAGA,EAAGC,EAAGA,CAAE,EACzC,CAAER,MAAOA,EAAOE,MAAOA,EAAOK,EAAGA,EAAGC,EAAGA,CAAE,EACzC,CAAER,MAAOA,EAAOE,MAAOA,EAAOK,EAAGA,EAAGC,EAAGA,CAAE,EACzC,CAAER,MAAOA,EAAOE,MAAOA,EAAOK,EAAGA,EAAGC,EAAGA,CAAE,EACzC,CAAE+B,QAAS,GAAI,EAClB,AACL,EAAE,AACV,EAAGlH,EACEsF,kBAAkB,CAAEtF,EACpBiE,QAAQ,CACRgD,mBAAmB,CACnB3B,kBAAkB,GAC3B,EAEAC,MAAO,CACH,SAAUlB,CAAC,CAAEmB,CAAU,EAEnB,IADIlB,EACAP,EAASyB,EAAWlF,OAAO,CAACyD,MAAM,CAClCoC,EAAU,AAACpC,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACoC,MAAM,EAC9C,EAAE,CACNxE,EAAS,AAA8B,OAA7B2C,CAAAA,EAAK,IAAI,CAACvE,KAAK,CAACwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAC1FI,EAAU9C,GAAU+C,EAA2C/C,EAAOgD,KAAK,EAC3EC,EAAUjD,GAAU+C,EAA2C/C,EAAOkD,KAAK,EAC/E,GAAIJ,GAAWG,EAAS,CACpB,IAAIM,EAAIT,EAAQnC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,AAErB6D,CAAAA,CAAM,CAAC,EAAE,CAACjB,CAAC,CAAGA,EAEdiB,CAAM,CAAC,EAAE,CAACjB,CAAC,CAAGA,EACdiB,CAAM,CAAC,EAAE,CAAChB,CAAC,CAAGA,EAEdgB,CAAM,CAAC,EAAE,CAAChB,CAAC,CAAGA,EACdK,EAAWnF,MAAM,CAAC,CACd0D,OAAQ,CAAC,CACDoC,OAAQA,CACZ,EAAE,AACV,EACJ,CACJ,EACH,AACL,EAOAgB,gBAAiB,CAEbhD,UAAW,8BAEXC,MAAO,SAAUC,CAAC,EAEd,IADIC,EACA3C,EAAS,AAA8B,OAA7B2C,CAAAA,EAAK,IAAI,CAACvE,KAAK,CAACwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAC1FI,EAAU9C,GAAU+C,EAA2C/C,EAAOgD,KAAK,EAC3EC,EAAUjD,GAAU+C,EAA2C/C,EAAOkD,KAAK,EAC3E7E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAE9C,GAAI,AAACyE,GAAYG,EAGjB,OAAO,IAAI,CAAC7E,KAAK,CAAC+E,aAAa,CAACnC,EAAM,CAClCoC,QAAS,QACTC,KAAM,kBACNvB,aAAc,CACVF,OAAQ,UACR6D,SAAU,OACVC,KAAM,CAAA,CACV,EACA9F,OAAQ,CAAC,CACD0D,MAAO,CACHN,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBF,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,CACJ,EAAE,AACV,EAAGtC,EACEsF,kBAAkB,CAAEtF,EACpBiE,QAAQ,CACRkD,eAAe,CACf7B,kBAAkB,EAC3B,CACJ,CACJ,EAmDAgC,OAAQ,CAAC,EAcThC,mBAAoB,CAChBiC,UAAW,CACPC,MAAO,CACX,CACJ,CACJ,CASA,EAgBIC,EAAa,AAAC5H,IAA+E4H,UAAU,CAEvGlE,EAAS,AAAC3C,IAA2H2C,MAAM,CAE3ImE,EAAW,AAAC7H,IAA+E6H,QAAQ,CAAEC,EAAM,AAAC9H,IAA+E8H,GAAG,CAAEC,EAAM,AAAC/H,IAA+E+H,GAAG,CAKzRC,EAAW,AAAChI,IAA+EgI,QAAQ,CAAEC,EAAO,AAACjI,IAA+EiI,IAAI,CAAEC,EAA6B,AAAClI,IAA+EgB,OAAO,CAAEmH,EAAY,AAACnI,IAA+EmI,SAAS,CAAEC,EAAU,AAACpI,IAA+EoI,OAAO,CAAEC,EAAa,AAACrI,IAA+EqI,UAAU,CAAEC,EAA8B,AAACtI,IAA+EiB,QAAQ,CAAEsH,EAAW,AAACvI,IAA+EuI,QAAQ,CAAEC,EAA2B,AAACxI,IAA+E8C,KAAK,CAAE2F,EAAa,AAACzI,IAA+EyI,UAAU,CAAEC,EAA0B,AAAC1I,IAA+EkB,IAAI,CAAEyH,EAAa,AAAC3I,IAA+E2I,UAAU,CAgC9vC,SAASC,IACD,IAAI,CAAC1I,KAAK,CAAC2I,kBAAkB,EAC7B,IAAI,CAAC3I,KAAK,CAAC2I,kBAAkB,CAACC,kBAAkB,EAExD,CAIA,SAASC,IACD,IAAI,CAACF,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACG,OAAO,EAEvC,CAIA,SAASC,IACL,IAAIxI,EAAU,IAAI,CAACA,OAAO,CACtBA,GAAWA,EAAQN,UAAU,EAAIM,EAAQN,UAAU,CAACiE,QAAQ,GAC5D,IAAI,CAACyE,kBAAkB,CAAG,IAAIK,EAAmB,IAAI,CAAEzI,EAAQN,UAAU,EACzE,IAAI,CAAC0I,kBAAkB,CAACM,UAAU,GAClC,IAAI,CAACN,kBAAkB,CAACO,UAAU,GAE1C,CAIA,SAASC,IACL,IAAIR,EAAqB,IAAI,CAACA,kBAAkB,CAC5CS,EAAoB,0BACxB,GAAI,IAAI,EAAIT,EAAoB,CAG5B,IAAIU,EAAmB,CAAA,EAMvB,GALA,IAAI,CAACC,MAAM,CAAC5I,OAAO,CAAC,SAAU4I,CAAM,EAC5B,CAACA,EAAO/I,OAAO,CAACiC,UAAU,EAAI8G,EAAOC,OAAO,EAC5CF,CAAAA,EAAmB,CAAA,CAAG,CAE9B,GACI,IAAI,CAACV,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACa,SAAS,EACjC,IAAI,CAACb,kBAAkB,CAACa,SAAS,CAAC,EAAE,CAAE,CACtC,IAAIC,EAAc,IAAI,CAACd,kBAAkB,CAACa,SAAS,CAAC,EAAE,CACtDjB,EAAWI,EAAmBe,eAAe,CAAE,SAAUnH,CAAK,CAAEvD,CAAG,EAG/D,IAAI2K,EAAaF,EAAYG,gBAAgB,CAAC,IAAM5K,GACpD,GAAI2K,EACA,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAWG,MAAM,CAAED,IAAK,CACxC,IAAIE,EAASJ,CAAU,CAACE,EAAE,CACtBG,EAAMD,EAAO3F,SAAS,AACtB7B,AAAsB,CAAA,WAAtBA,EAAM0H,WAAW,CAGsB,KAAnCD,EAAIE,OAAO,CAACd,IACZW,EAAOI,SAAS,CAACC,MAAM,CAAChB,GAGtBC,EAOiC,KAAnCW,EAAIE,OAAO,CAACd,IACZW,EAAOI,SAAS,CAACC,MAAM,CAAChB,GAPW,KAAnCY,EAAIE,OAAO,CAACd,IACZW,CAAAA,EAAO3F,SAAS,EAAI,IAAMgF,CAAgB,CAStD,CAER,EACJ,CACJ,CACJ,CAIA,SAASiB,IACL,IAAI,CAACzB,kBAAkB,EAC3B,CAIA,SAAS0B,IACL,IAAI,CAACC,qBAAqB,CAAG,IACjC,CAKA,SAASC,EAAqBC,CAAc,EACxC,IAqDIC,EACAC,EAtDAC,EAAgBH,EAAejL,SAAS,CAACqL,cAAc,CAACtD,MAAM,EAC1DkD,EAAejL,SAAS,CAACqL,cAAc,CAACtD,MAAM,CAACuD,KAAK,CAK5D,SAASC,EAAmBC,CAAc,EACtC,IAAIvF,EAAa,IAAI,CACjBxF,EAAawF,EAAWzF,KAAK,CAAC2I,kBAAkB,CAChDsC,EAAiBhL,EAAWiL,gBAAgB,CAC5CN,GACAA,EAAclL,IAAI,CAAC+F,EAAYuF,GAE/BC,IAAmBxF,GAEnBxF,EAAW2I,kBAAkB,GAC7B3I,EAAWiL,gBAAgB,CAAGzF,EAC9BA,EAAW0F,0BAA0B,CAAC,CAAA,GACtClD,EAAUhI,EAAY,YAAa,CAC/BwF,WAAYA,EACZ2F,SAAU,qBACV7K,QAASN,EAAWoL,kBAAkB,CAAC5F,GACvC6F,SAAU,SAAUC,CAAI,EACpB,GAAIA,AAAoB,WAApBA,EAAKC,UAAU,CACfvL,EAAWiL,gBAAgB,CAAG,CAAA,EAC9BjL,EAAWD,KAAK,CAACyL,gBAAgB,CAAChG,OAEjC,CACD,IAAIiG,EAAS,CAAC,EACdzL,EAAW0L,eAAe,CAACJ,EAAKK,MAAM,CAAEF,GACxCzL,EAAW2I,kBAAkB,GAC7B,IAAIrF,EAAcmI,EAAOnI,WAAW,AACJ,CAAA,YAA5BkC,EAAWlF,OAAO,CAAC0E,IAAI,GAGvB1B,EAAYsI,UAAU,CAACC,OAAO,CAAIvI,AACb,IADaA,EAAYsI,UAAU,CACnDpI,WAAW,CAChBF,EAAYwI,UAAU,CAACD,OAAO,CAAIvI,AACb,IADaA,EAAYwI,UAAU,CACnDtI,WAAW,EAEpBgC,EAAWnF,MAAM,CAACoL,EACtB,CACJ,CACJ,IAIAzD,EAAUhI,EAAY,cAG1B+K,EAAeE,gBAAgB,CAAG,CAAA,CACtC,CAqBA5C,EAAyB,CAAA,EAAMmC,EAAejL,SAAS,CAACqL,cAAc,CAACtD,MAAM,CAAE,CAC3EuD,MAAOC,EACPiB,WAhBJ,SAAoB1H,CAAC,EACjBoG,EAAcpG,EAAE2H,OAAO,CAAC,EAAE,CAACC,OAAO,CAClCvB,EAAcrG,EAAE2H,OAAO,CAAC,EAAE,CAACE,OAAO,AACtC,EAcIC,SAVJ,SAA2B9H,CAAC,EACToG,GAAc5E,KAAKC,IAAI,CAACD,KAAKE,GAAG,CAAC0E,EAAcpG,EAAE+H,cAAc,CAAC,EAAE,CAACH,OAAO,CAAE,GACnFpG,KAAKE,GAAG,CAAC2E,EAAcrG,EAAE+H,cAAc,CAAC,EAAE,CAACF,OAAO,CAAE,KAAO,GAE/DpB,EAAmBrL,IAAI,CAAC,IAAI,CAAE4E,EAEtC,CAKA,EACJ,CASA,IAAI0E,EAAoC,WAMpC,SAASA,EAAmBhJ,CAAK,CAAEO,CAAO,EACtC,IAAI,CAACmJ,eAAe,CAAG,KAAK,EAC5B,IAAI,CAAC1J,KAAK,CAAGA,EACb,IAAI,CAACO,OAAO,CAAGA,EACf,IAAI,CAAC+L,cAAc,CAAG,EAAE,CACxB,IAAI,CAAC9C,SAAS,CACV,IAAI,CAACxJ,KAAK,CAACwJ,SAAS,CAAC+C,sBAAsB,CAAC,IAAI,CAAChM,OAAO,CAAC0D,iBAAiB,EAAI,IAC7E,IAAI,CAACuF,SAAS,CAACM,MAAM,EACtB,CAAA,IAAI,CAACN,SAAS,CAAG5B,EAAI2E,sBAAsB,CAAC,IAAI,CAAChM,OAAO,CAAC0D,iBAAiB,EAAI,GAAE,CAExF,CAgkBA,OA1jBA+E,EAAmBjJ,OAAO,CAAG,SAAUyM,CAAe,CAAEC,CAAU,EAC1DhE,EAAWd,EAAU,wBACrBG,EAAS0E,EAAiB,SAAU9D,GAEpC8B,EAAqBgC,GAErBjE,EAAWiE,EAAgBE,KAAK,CAAE,SAAUjC,CAAc,EACtDD,EAAqBC,EACzB,GACA3C,EAAS2E,EAAY,UAAW5D,GAChCf,EAAS2E,EAAY,OAAQ1D,GAC7BjB,EAAS2E,EAAY,SAAUtD,GAC/BrB,EAASkB,EAAoB,aAAcqB,GAC3CvC,EAASkB,EAAoB,iBAAkBsB,GAC/C5C,EAAW7E,GAEnB,EAMAmG,EAAmBxJ,SAAS,CAACmN,SAAS,CAAG,SAAUrI,CAAC,EAEhD,IADIC,EACA3C,EAAS,AAA8B,OAA7B2C,CAAAA,EAAK,IAAI,CAACvE,KAAK,CAACwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAC9F,MAAO,CACH1C,GAAUgL,EAAmChL,EAAOgD,KAAK,EACzDhD,GAAUgL,EAAmChL,EAAOkD,KAAK,EAC5D,AACL,EAOAkE,EAAmBxJ,SAAS,CAACyJ,UAAU,CAAG,WACtC,IAAIhJ,EAAa,IAAI,CACjBD,EAAQC,EAAWD,KAAK,CACxB6M,EAAoB5M,EAAWuJ,SAAS,CACxCjJ,EAAUN,EAAWM,OAAO,AAEhCN,CAAAA,EAAWyJ,eAAe,CAAG,CAAC,EAC9BnB,EAAYhI,EAAQ2D,QAAQ,EAAI,CAAC,EAAI,SAAU3B,CAAK,EAChDtC,EAAWyJ,eAAe,CAACnH,EAAM6B,SAAS,CAAC,CAAG7B,CAClD,GAEA,EAAE,CAAC7B,OAAO,CAAChB,IAAI,CAACmN,EAAmB,SAAUC,CAAY,EACrD7M,EAAWqM,cAAc,CAACjM,IAAI,CAACyH,EAASgF,EAAc,QAAS,SAAUC,CAAK,EAC1E,IAAI7I,EAAWjE,EAAW+M,eAAe,CAACF,EACtCC,GACA7I,GACC,CAACA,EAAS6F,MAAM,CAACI,SAAS,CACtB8C,QAAQ,CAAC,4BACdhN,EAAWiN,mBAAmB,CAAChJ,EAAS6F,MAAM,CAAE7F,EAASqD,MAAM,CAAEwF,EAEzE,GACJ,GACAxE,EAAYhI,EAAQgH,MAAM,EAAI,CAAC,EAAI,SAAU4F,CAAQ,CAAEC,CAAS,EACxDjF,EAAWgF,IACXlN,EAAWqM,cAAc,CAACjM,IAAI,CAACyH,EAAS7H,EAAYmN,EAAWD,EAAU,CAAEE,QAAS,CAAA,CAAM,GAElG,GACApN,EAAWqM,cAAc,CAACjM,IAAI,CAACyH,EAAS9H,EAAMwJ,SAAS,CAAE,QAAS,SAAUlF,CAAC,EACrE,CAACtE,EAAMsN,WAAW,EAClBtN,EAAMuN,YAAY,CAACjJ,EAAE2B,MAAM,CAAGjG,EAAMwN,QAAQ,CAAElJ,EAAE4B,MAAM,CAAGlG,EAAMyN,OAAO,CAAE,CACpEC,gBAAiB,CAAA,CACrB,IACAzN,EAAW0N,kBAAkB,CAAC,IAAI,CAAErJ,EAE5C,IACArE,EAAWqM,cAAc,CAACjM,IAAI,CAACyH,EAAS9H,EAAMwJ,SAAS,CAAE,AAAC1J,IAA+E8N,aAAa,CAAG,YAAc,YAAa,SAAUtJ,CAAC,EAC3LrE,EAAW4N,0BAA0B,CAAC,IAAI,CAAEvJ,EAChD,EAAG,AAACxE,IAA+E8N,aAAa,CAAG,CAAEP,QAAS,CAAA,CAAM,EAAI,KAAK,GACjI,EAOArE,EAAmBxJ,SAAS,CAAC0J,UAAU,CAAG,WACtC,IAAIjJ,EAAa,IAAI,CACrBU,EACKZ,OAAO,CAAC,IAAI,CAACC,KAAK,EAAEC,UAAU,CAC9BE,SAAS,CAAC,SAAUI,CAAO,EAC5BN,EAAWK,MAAM,CAACC,EACtB,EACJ,EAiBAyI,EAAmBxJ,SAAS,CAAC0N,mBAAmB,CAAG,SAAUnD,CAAM,CAAExC,CAAM,CAAEuG,CAAU,EACnF,IACI9N,EAAQC,AADK,IAAI,CACED,KAAK,CACxB+N,EAAe/N,EAAMgO,QAAQ,CAACC,UAAU,CACxCC,EAAqB,CAAA,CACrBjO,CAJa,IAAI,CAINsK,qBAAqB,GAC5BtK,AALS,IAAI,CAKFsK,qBAAqB,CAACJ,SAAS,GAAKJ,EAAOI,SAAS,EAC/D+D,CAAAA,EAAqB,CAAA,CAAI,EAE7BjG,EARa,IAAI,CAQK,iBAAkB,CAAE8B,OAAQ9J,AARrC,IAAI,CAQ4CsK,qBAAqB,AAAC,GAC/EtK,AATS,IAAI,CASFkO,SAAS,GAEhBlO,AAXK,IAAI,CAWEmO,kBAAkB,EAC7BnO,AAAuC,gBAAvCA,AAZK,IAAI,CAYEmO,kBAAkB,CAACC,IAAI,EAClCrO,EAAMyL,gBAAgB,CAACxL,AAblB,IAAI,CAayBmO,kBAAkB,EAExDnO,AAfS,IAAI,CAeFqO,cAAc,CAAGrO,AAfnB,IAAI,CAe0BkO,SAAS,CAAG,CAAA,IAGvDD,GACAjO,AAnBa,IAAI,CAmBNsO,cAAc,CAAGhH,EAC5BtH,AApBa,IAAI,CAoBNsK,qBAAqB,CAAGR,EACnC9B,EArBa,IAAI,CAqBK,eAAgB,CAAE8B,OAAQA,CAAO,GAEnDxC,EAAOiH,IAAI,EACXjH,EAAOiH,IAAI,CAAC9O,IAAI,CAxBP,IAAI,CAwBgBqK,EAAQ+D,GAErCvG,CAAAA,EAAOlD,KAAK,EAAIkD,EAAO/B,KAAK,AAAD,GAC3BxF,EAAMgO,QAAQ,CAACC,UAAU,CAACQ,QAAQ,CAAC,0BAIvCzO,EAAM0O,UAAU,EAAI3E,EAAOI,SAAS,CAACC,MAAM,CAAC,qBAC5C2D,EAAaY,WAAW,CAAC,wBACzB1O,AAjCa,IAAI,CAiCNkO,SAAS,CAAG,CAAA,EACvBlO,AAlCa,IAAI,CAkCNqO,cAAc,CAAG,CAAA,EAC5BrO,AAnCa,IAAI,CAmCNsO,cAAc,CAAG,KAEpC,EAeAvF,EAAmBxJ,SAAS,CAACmO,kBAAkB,CAAG,SAAU3N,CAAK,CAAE8N,CAAU,EACzE9N,EAAQ,IAAI,CAACA,KAAK,CAClB,IACIkL,EAAmBjL,AADN,IAAI,CACaiL,gBAAgB,CAC9CqD,EAAiBtO,AAFJ,IAAI,CAEWsO,cAAc,CAC1CR,EAAe/N,EAAMgO,QAAQ,CAACC,UAAU,CACxC/C,IAGI,AAACA,EAAiBoC,WAAW,EAC5BQ,EAAW5C,gBAAgB,GAE5B4C,EAAWxH,MAAM,CAACsI,UAAU,EAE3BC,AA1YjB,SAAyBC,CAAE,CAAEC,CAAC,EAC1B,IAAIC,EAAenH,EAAIoH,OAAO,CAACzP,SAAS,CACpC0P,EAAiBF,EAAaG,OAAO,EACjCH,EAAaI,iBAAiB,EAC9BJ,EAAaK,qBAAqB,CACtCC,EAAM,KACV,GAAIN,EAAaO,OAAO,CACpBD,EAAMN,EAAaO,OAAO,CAAC7P,IAAI,CAACoP,EAAIC,QAGpC,EAAG,CACC,GAAIG,EAAexP,IAAI,CAACoP,EAAIC,GACxB,OAAOD,EAEXA,EAAKA,EAAGU,aAAa,EAAIV,EAAGF,UAAU,AAC1C,OAASE,AAAO,OAAPA,GAAeA,AAAgB,IAAhBA,EAAGW,QAAQ,CAAQ,CAE/C,OAAOH,CACX,EAwXiCxB,EAAWxH,MAAM,CAAE,qBAG/B4E,EAAiBoC,WAAW,EAEjCoC,WAAW,WACPxE,EAAiBoC,WAAW,CAAG,CAAA,CACnC,EAAG,GANHrF,EAbS,IAAI,CAaS,eASzBsG,GAAmBA,EAAelK,KAAK,GAGvCpE,AAzBY,IAAI,CAyBLkO,SAAS,EAsBrBlO,AA/Ca,IAAI,CA+CNkO,SAAS,CAACL,EAAY7N,AA/CpB,IAAI,CA+C2BmO,kBAAkB,EAC1DnO,AAhDS,IAAI,CAgDFuF,KAAK,GAChBvF,AAjDS,IAAI,CAiDF0P,SAAS,GAChBpB,EAAe/I,KAAK,CAACvF,AAlDhB,IAAI,CAkDuB0P,SAAS,CAAC,CAE1C1P,AApDK,IAAI,CAoDEqO,cAAc,CAAGrO,AApDvB,IAAI,CAoD8BkO,SAAS,CAAGI,EAAe/I,KAAK,CAACvF,AApDnE,IAAI,CAoD0E0P,SAAS,CAAC,EAG7F1H,EAvDK,IAAI,CAuDa,iBAAkB,CAAE8B,OAAQ9J,AAvD7C,IAAI,CAuDoDsK,qBAAqB,AAAC,GACnFwD,EAAaY,WAAW,CAAC,wBAErBJ,EAAeqB,GAAG,EAClBrB,EAAeqB,GAAG,CAAClQ,IAAI,CA3DtB,IAAI,CA2D+BoO,EAAY7N,AA3D/C,IAAI,CA2DsDmO,kBAAkB,EAEjFnO,AA7DK,IAAI,CA6DEkO,SAAS,CAAG,CAAA,EACvBlO,AA9DK,IAAI,CA8DEqO,cAAc,CAAG,CAAA,EAC5BrO,AA/DK,IAAI,CA+DEsO,cAAc,CAAG,SApCpCtO,AA3Ba,IAAI,CA2BNmO,kBAAkB,CAAGG,EAAelK,KAAK,CAAC3E,IAAI,CA3B5C,IAAI,CA2BqDoO,GAElE7N,AA7BS,IAAI,CA6BFmO,kBAAkB,EAAIG,EAAe/I,KAAK,EACrDvF,AA9BS,IAAI,CA8BF0P,SAAS,CAAG,EACvB1P,AA/BS,IAAI,CA+BFuF,KAAK,CAAG,CAAA,EACnBvF,AAhCS,IAAI,CAgCFqO,cAAc,CAAGrO,AAhCnB,IAAI,CAgC0BkO,SAAS,CAC5CI,EAAe/I,KAAK,CAACvF,AAjChB,IAAI,CAiCuB0P,SAAS,CAAC,GAG9C1H,EApCS,IAAI,CAoCS,iBAAkB,CAAE8B,OAAQ9J,AApCzC,IAAI,CAoCgDsK,qBAAqB,AAAC,GACnFwD,EAAaY,WAAW,CAAC,wBACzB1O,AAtCS,IAAI,CAsCFuF,KAAK,CAAG,CAAA,EACnBvF,AAvCS,IAAI,CAuCFsO,cAAc,CAAG,KAExBA,EAAeqB,GAAG,EAClBrB,EAAeqB,GAAG,CAAClQ,IAAI,CA1ClB,IAAI,CA0C2BoO,EAAY7N,AA1C3C,IAAI,CA0CkDmO,kBAAkB,IAyB7F,EAaApF,EAAmBxJ,SAAS,CAACqO,0BAA0B,CAAG,SAAUgC,CAAU,CAAEC,CAAS,EACjF,IAAI,CAACxB,cAAc,EACnB,IAAI,CAACA,cAAc,CAACwB,EAAW,IAAI,CAAC1B,kBAAkB,CAE9D,EAiBApF,EAAmBxJ,SAAS,CAACmM,eAAe,CAAG,SAAUC,CAAM,CAAEF,CAAM,EAoCnE,OAnCAnD,EAAWqD,EAAQ,SAAUrJ,CAAK,CAAEwN,CAAK,EACrC,IAAIC,EAAcC,WAAW1N,GACzB2N,EAAOH,EAAMI,KAAK,CAAC,KACnBC,EAAaF,EAAKpG,MAAM,CAAG,EAQ/B,IANI1B,EAA4B4H,IAC3BzN,EAAM8N,KAAK,CAAC,WACZN,EAAMM,KAAK,CAAC,YACb9N,CAAAA,EAAQyN,CAAU,EAGlBzN,AAAU,cAAVA,EAAuB,CACvB,IAAI+N,EAAW5E,EACfwE,EAAKxP,OAAO,CAAC,SAAUe,CAAI,CAAE4D,CAAK,EAC9B,GAAI5D,AAAS,cAATA,GAAwBA,AAAS,gBAATA,EAAwB,CAChD,IAAI8O,EAAW/H,EAAwB0H,CAAI,CAAC7K,EAAQ,EAAE,CAAE,GACpD+K,CAAAA,IAAe/K,EAEfiL,CAAQ,CAAC7O,EAAK,CAAGc,GAEX+N,CAAQ,CAAC7O,EAAK,EAEpB6O,CAAAA,CAAQ,CAAC7O,EAAK,CAAG8O,EAASF,KAAK,CAAC,OAC5B,EAAE,CACF,CAAC,CAAA,EAKLC,EAAWA,CAAQ,CAAC7O,EAAK,CAEjC,CACJ,EACJ,CACJ,GACOiK,CACX,EAMA1C,EAAmBxJ,SAAS,CAACoJ,kBAAkB,CAAG,WAC1C,IAAI,CAACsC,gBAAgB,GACrB,IAAI,CAACA,gBAAgB,CAACC,0BAA0B,CAAC,CAAA,GACjD,IAAI,CAACD,gBAAgB,CAAG,CAAA,EAEhC,EAaAlC,EAAmBxJ,SAAS,CAAC6L,kBAAkB,CAAG,SAAU5F,CAAU,EAClE,IAAIlF,EAAUkF,EAAWlF,OAAO,CAC5BiQ,EAAYxH,EAAmByH,mBAAmB,CAClDC,EAAkBF,EAAUG,aAAa,CACzC1L,EAAOuD,EAAwBjI,EAAQ0E,IAAI,CAC3C1E,EAAQyD,MAAM,EAAIzD,EAAQyD,MAAM,CAAC,EAAE,EAC/BzD,EAAQyD,MAAM,CAAC,EAAE,CAACiB,IAAI,CAC1B1E,EAAQiB,MAAM,EAAIjB,EAAQiB,MAAM,CAAC,EAAE,EAC/BjB,EAAQiB,MAAM,CAAC,EAAE,CAACyD,IAAI,CAAE,SAC5B2L,EAAe5H,EAAmB6H,sBAAsB,CAACtQ,EAAQyE,OAAO,CAAC,EAAI,EAAE,CAC/E8L,EAAgB,CACZ9L,QAASzE,EAAQyE,OAAO,CACxBC,KAAMA,CACV,EAoBJ,SAAS8L,EAASC,CAAM,CAAEhS,CAAG,CAAEiS,CAAe,CAAEC,CAAM,CAAEC,CAAS,EAC7D,IAAIC,EACAH,GACAjJ,EAA2BgJ,IAC3BJ,AAA8B,KAA9BA,EAAa1G,OAAO,CAAClL,IACpB,CAAA,AAACiS,CAAAA,EAAgB/G,OAAO,EACrB+G,EAAgB/G,OAAO,CAAClL,EAAG,GAAM,GACjCiS,CAAe,CAACjS,EAAI,EACpBiS,AAAoB,CAAA,IAApBA,CAAuB,IAGvB/I,EAAQ8I,IACRE,CAAM,CAAClS,EAAI,CAAG,EAAE,CAChBgS,EAAOtQ,OAAO,CAAC,SAAU2Q,CAAW,CAAExH,CAAC,EAC9BxB,EAASgJ,IAMVH,CAAM,CAAClS,EAAI,CAAC6K,EAAE,CAAG,CAAC,EAClBtB,EAAW8I,EAAa,SAAUC,CAAY,CAAEC,CAAS,EACrDR,EAASO,EAAcC,EAAWb,CAAe,CAAC1R,EAAI,CAAEkS,CAAM,CAAClS,EAAI,CAAC6K,EAAE,CAAE7K,EAC5E,IAPA+R,EAASM,EAAa,EAAGX,CAAe,CAAC1R,EAAI,CAAEkS,CAAM,CAAClS,EAAI,CAAEA,EASpE,IAEKqJ,EAAS2I,IACdI,EAAa,CAAC,EACVlJ,EAAQgJ,IACRA,EAAO7Q,IAAI,CAAC+Q,GACZA,CAAU,CAACpS,EAAI,CAAG,CAAC,EACnBoS,EAAaA,CAAU,CAACpS,EAAI,EAG5BkS,CAAM,CAAClS,EAAI,CAAGoS,EAElB7I,EAAWyI,EAAQ,SAAUM,CAAY,CAAEC,CAAS,EAChDR,EAASO,EAAcC,EAAWvS,AAAQ,IAARA,EAC9BiS,EACAP,CAAe,CAAC1R,EAAI,CAAEoS,EAAYpS,EAC1C,IAIIA,AAAQ,WAARA,EACAkS,CAAM,CAAClS,EAAI,CAAG,CACVwE,EAAOwN,EAAQvL,EAAWjE,MAAM,CAAC,EAAE,CAAC4E,MAAM,CAAC,EAAE,EAAEoL,QAAQ,GACvD,OACH,CAEItJ,EAAQgJ,GACbA,EAAO7Q,IAAI,CAAC,CAAC2Q,EAAQS,EAAgCN,EAAWH,GAAQ,EAGxEE,CAAM,CAAClS,EAAI,CAAG,CAACgS,EAAQS,EAAgCzS,EAAKgS,GAAQ,CAIpF,CAYA,OAXAzI,EAAWhI,EAAS,SAAUyQ,CAAM,CAAEhS,CAAG,EACjCA,AAAQ,gBAARA,GACA8R,CAAa,CAAC9R,EAAI,CAAG,CAAC,EACtBuJ,EAAWhI,CAAO,CAACvB,EAAI,CAAE,SAAU0S,CAAU,CAAEC,CAAO,EAClDZ,EAASW,EAAYC,EAASjB,EAAiBI,CAAa,CAAC9R,EAAI,CAAE2S,EACvE,IAGAZ,EAASC,EAAQhS,EAAKwR,CAAS,CAACvL,EAAK,CAAE6L,EAAe9R,EAE9D,GACO8R,CACX,EAiBA9H,EAAmBxJ,SAAS,CAACoS,oBAAoB,CAAG,SAAUpI,CAAS,CAAEuD,CAAK,EAI1E,IAHA,IAEI8E,EAFAC,EAAU/E,EAAMzG,MAAM,CACtByL,EAAa,EAAE,CAEZD,GAAWA,EAAQE,OAAO,GAC7BH,CAAAA,EAAgB9J,EAAK+J,EAAS,QAAO,GAEjCC,CAAAA,EAAaA,EAAWE,MAAM,CAACJ,EAC1B1B,KAAK,CAAC,KAEN+B,GAAG,CAAC,SAAUzQ,CAAI,EAAI,MAAQ,CAACA,EAAMqQ,EAAQ,AAAG,GAAE,EAGvDA,AADJA,CAAAA,EAAUA,EAAQlD,UAAU,AAAD,IACXpF,KAIpB,OAAOuI,CACX,EAiBA/I,EAAmBxJ,SAAS,CAACwN,eAAe,CAAG,SAAUxD,CAAS,CAAEuD,CAAK,EACrE,IAGI7I,EAHAjE,EAAa,IAAI,CAYrB,OARA8R,AAHiB,IAAI,CAACH,oBAAoB,CAACpI,EACvCuD,GAEOrM,OAAO,CAAC,SAAU0D,CAAS,EAC9BnE,EAAWyJ,eAAe,CAACtF,CAAS,CAAC,EAAE,CAAC,EAAI,CAACF,GAC7CA,CAAAA,EAAW,CACPqD,OAAQtH,EAAWyJ,eAAe,CAACtF,CAAS,CAAC,EAAE,CAAC,CAChD2F,OAAQ3F,CAAS,CAAC,EAAE,AACxB,CAAA,CAER,GACOF,CACX,EAQA8E,EAAmBxJ,SAAS,CAACc,MAAM,CAAG,SAAUC,CAAO,EACnD,IAAI,CAACA,OAAO,CAAG+H,EAAyB,CAAA,EAAM,IAAI,CAAC/H,OAAO,CAAEA,GAC5D,IAAI,CAAC4R,YAAY,GACjB,IAAI,CAAClJ,UAAU,EACnB,EAOAD,EAAmBxJ,SAAS,CAAC2S,YAAY,CAAG,WACxC,IAAI,CAAC7F,cAAc,CAAC5L,OAAO,CAAC,SAAU0R,CAAQ,EAAI,OAAOA,GAAY,EACzE,EAKApJ,EAAmBxJ,SAAS,CAACsJ,OAAO,CAAG,WACnC,IAAI,CAACqJ,YAAY,EACrB,EAOAnJ,EAAmByH,mBAAmB,CAAG,CAGrCE,cAAe,CACXjN,aAAc,CAAC,QAAS,SAAU,kBAAkB,CACpDlC,OAAQ,CAAC,QAAQ,CACjB6B,MAAO,CAAC,QAAQ,CAChBQ,MAAO,CAAC,WAAY,QAAQ,CAC5BwO,WAAY,CAAC,OAAQ,cAAe,SAAS,CAC7CC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDjP,aAAc,CAAC,OAAQ,cAAe,SAAS,CAC/CU,OAAQ,CAAC,OAAQ,cAAe,SAAS,CACzCwO,KAAM,CAAC,cAAe,SAAS,CAC/B7O,iBAAkB,CAAC,CAAA,EAAK,CACxB8O,UAAW,CAAC,OAAQ,cAAe,SAAS,CAC5C1G,WAAY,CAAC,cAAe,SAAS,CACrCF,WAAY,CAAC,cAAe,SAAS,AACzC,EAEA3I,OAAQ,CAAC,SAAS,CAClBC,QAAS,CAAC,SAAS,CACnBuP,aAAc,EAAE,CAChBrP,MAAO,CAAC,eAAe,CAEvBsP,QAAS,CAAC,aAAc,aAAc,aAAa,CAEnDC,UAAW,EAAE,CACbC,OAAQ,CAAC,aAAc,OAAQ,SAAS,CACxCC,UAAW,CAAC,kBAAmB,kBAAkB,CACjDC,KAAM,CAAC,SAAS,CAEhBC,YAAa,EAAE,CACfC,gBAAiB,CAAC,SAAU,eAAe,AAC/C,EAGAjK,EAAmB6H,sBAAsB,CAAG,CACxCzN,UAAW,CAAC,aAAc,aAAc,eAAe,CACvDD,QAAS,CAAC,eAAe,CACzBD,OAAQ,CAAC,eAAe,AAC5B,EACO8F,CACX,IAyCIkK,EAAmG5U,EAAoB,KACvH6U,EAAuH7U,EAAoBI,CAAC,CAACwU,GAe7IE,EAAa,AAACtT,IAA+EsT,UAAU,CAKvGC,EAA8B,AAACvT,IAA+EgB,OAAO,CAAEwS,GAAgC,AAACxT,IAA+EmI,SAAS,CAAEsL,GAA+B,AAACzT,IAA+EiB,QAAQ,CAAEyS,GAAY,AAAC1T,IAA+E0T,SAAS,CAShdC,GAAqB,CACrB,MACA,KACA,QACA,kBACA,MACA,KACA,MACA,UACA,MACA,MACA,iBACA,MACA,MACA,wBACA,4BACA,wBACA,UACA,OACA,MACA,WACA,OACA,MACA,MACA,MACA,MACA,iBACA,aACA,OACA,YACH,CAIGC,GAAuB,CACvB,KACA,MACA,UACA,MACA,MACA,MACA,OACH,CAsGD,SAASC,GAAerP,CAAC,CAAEtE,CAAK,EAE5B,IADIuE,EAEAG,EACAG,EAEA+O,EAJAhS,EAAS,AAAyB,OAAxB2C,CAAAA,EAAKvE,EAAMwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,cAAc,CAACH,GAGrFuP,EAAQC,OAAOC,SAAS,CAO5B,GALI/T,EAAM2I,kBAAkB,EAAI/G,IAC5B8C,EAAUsP,EAAoCpS,EAAOgD,KAAK,EAC1DC,EAAUmP,EAAoCpS,EAAOkD,KAAK,GAG1D,AAACJ,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,CAYrB,GAVAsC,EAAQ7C,IAAI,CAACsH,MAAM,CAAC5I,OAAO,CAAC,SAAU4I,CAAM,EACxC,GAAIA,EAAOlD,MAAM,CAAE,CACf,IAAIlB,EAAQoE,EAAO2K,WAAW,CAAC3P,EAC3B,CAAA,GACAY,GAAS2O,EAAQ/N,KAAKiB,GAAG,CAAC7B,EAAMC,CAAC,CAAGA,KACpC0O,EAAQ/N,KAAKiB,GAAG,CAAC7B,EAAMC,CAAC,CAAGA,GAC3ByO,EAAe1O,EAEvB,CACJ,GACI0O,GAAgBA,EAAazO,CAAC,EAAIyO,EAAaxO,CAAC,CAChD,MAAO,CACHD,EAAGyO,EAAazO,CAAC,CACjBC,EAAGwO,EAAaxO,CAAC,CACjB8O,MAAO9O,EAAIwO,EAAaxO,CAAC,CACzBkE,OAAQsK,EAAatK,MAAM,CAC3B1E,MAAOgP,EAAatK,MAAM,CAAC1E,KAAK,CAACS,KAAK,EAAI,EAC1CP,MAAO8O,EAAatK,MAAM,CAACxE,KAAK,CAACO,KAAK,EAAI,CAC9C,EAER,CAkQ6B,IAAI8O,GAdP,CACtBV,mBAAoBA,GACpBC,qBAAsBA,GACtBU,gBA5WJ,SAAyBnP,CAAI,EACzB,OAAO,SAAUX,CAAC,EACd,IAAIrE,EAAa,IAAI,CACjBD,EAAQC,EAAWD,KAAK,CACxBqU,EAAUrU,EAAM0O,UAAU,CAC1BxJ,EAAQyO,GAAerP,EACvBtE,GACJ,GAAKkF,GAGL,IAAIoP,EAAc,CACVnP,EAAGD,EAAMC,CAAC,CACVC,EAAGF,EAAME,CAAC,AACd,EACAmP,EAAgB,CACZtP,KAAM,QACNuP,SAAUtP,EAAMoE,MAAM,CAACmL,EAAE,CACzBC,MAAOzP,EACPsG,KAAM,CAAC+I,EAAY,CACnB1P,MAAOM,EAAMN,KAAK,CAClBE,MAAOI,EAAMJ,KAAK,CAClBI,MAAO,CACHqC,OAAQ,CACJuD,MAAO,WACH,IAAI5F,EAAQ,IAAI,CAChC3E,EAAU2E,EAAM3E,OAAO,CACX+S,GAA8BrT,EAAY,YAAa,CACnDiF,MAAOA,EACPkG,SAAU,qBACV7K,QAAS,CACLyE,QAAS,QACTC,KAAM,QACNtD,MAAO,CACHpB,EAAQoB,KAAK,CACbgT,EAAiC,QAASpU,EAAQoB,KAAK,EAC1D,CACDF,KAAM,CACFlB,EAAQkB,IAAI,CACZkT,EAAiC,OAAQpU,EAAQkB,IAAI,EACxD,AACL,EACA6J,SAAU,SAAUsJ,CAAO,EACnBA,AAAuB,WAAvBA,EAAQpJ,UAAU,CAClBtG,EAAMkF,MAAM,GAGZlF,EAAM5E,MAAM,CAACL,EAAW0L,eAAe,CAACiJ,EAAQhJ,MAAM,CAAE,CAAC,GAEjE,CACJ,EACJ,CACJ,CACJ,CACJ,EACKyI,GAAYA,EAAQQ,UAAU,EAC/B7U,EAAM8U,SAAS,CAACP,GAEpBjB,GAA8BrT,EAAY,YAAa,CACnDmL,SAAU,OAEV7K,QAAS,CACLyE,QAAS,QACTC,KAAM,QACNtD,MAAO,CAAC,IAAKgT,EAAiC,QAAS,KAAK,CAC5DlT,KAAM,CAAC,SAAUkT,EAAiC,QAAS,UAAU,AACzE,EAEArJ,SAAU,SAAUC,CAAI,EACpBtL,EAAW0L,eAAe,CAACJ,EAAKK,MAAM,CAAE2I,EAAchJ,IAAI,CAAC,EAAE,EAC7DvL,EAAM8U,SAAS,CAACP,EACpB,CACJ,GACJ,CACJ,EAoSIZ,eAAgBA,GAChBoB,eAAe,GACfC,oBA7OJ,SAA6BhT,CAAI,EAC7B,MAAOA,AAA+B,+BAA/BA,EAAKiT,WAAW,CAAC7Q,SAAS,AACrC,EA4OI8Q,wBAhOJ,SAAiC5L,CAAM,EACnC,OAAOA,EAAO6L,IAAI,CAAC,SAAUpG,CAAC,EAAI,OAAOA,EAAEqG,gBAAgB,EAAIrG,EAAEsG,SAAS,AAAE,EAChF,EA+NIC,iBA3NJ,SAA0B/J,CAAI,EAC1B,IAKIzG,EACAyQ,EACA1K,EACAvB,EARAtJ,EAAQ,IAAI,CAACA,KAAK,CAClBwV,EAAe,CACXC,SAAUlK,EAAKkK,QAAQ,CACvBxQ,KAAMsG,EAAKtG,IAAI,AACnB,EAKJ,GAAIsG,AAAoB,SAApBA,EAAKC,UAAU,CACf,IAAI,CAACG,eAAe,CAACJ,EAAKK,MAAM,CAAE4J,GAClClM,CAAAA,EAAStJ,EAAMX,GAAG,CAACkM,EAAKmK,QAAQ,CAAA,GAE5BpM,EAAOhJ,MAAM,CAACkV,EAAc,CAAA,QAG/B,GAAIjK,AAAoB,WAApBA,EAAKC,UAAU,CAEpB,CAAA,GAAIlC,AADJA,CAAAA,EAAStJ,EAAMX,GAAG,CAACkM,EAAKmK,QAAQ,CAAA,IAE5B5Q,EAAQwE,EAAOxE,KAAK,CAChBwE,EAAOqM,YAAY,EACnBrM,EAAOqM,YAAY,CAACjV,OAAO,CAAC,SAAUiV,CAAY,EAC9CA,EAAavL,MAAM,CAAC,CAAA,EACxB,GAEJd,EAAOc,MAAM,CAAC,CAAA,GACVqJ,GAAmBvJ,OAAO,CAACZ,EAAOrE,IAAI,GAAK,GAAG,CAC9C,IAAI2Q,EAAoB,CAChB7R,OAAQe,EAAMvE,OAAO,CAACwD,MAAM,CAC5B8R,IAAK/Q,EAAMvE,OAAO,CAACsV,GAAG,AAC1B,EACJ/Q,EAAMsF,MAAM,CAAC,CAAA,GACb,IAAI,CAAC0L,WAAW,CAACF,EACrB,CACJ,MAGAJ,EAAaf,EAAE,CAAGjB,KAClB,IAAI,CAAC7H,eAAe,CAACJ,EAAKK,MAAM,CAAE4J,GAClCD,EAAevV,EAAMX,GAAG,CAACmW,EAAaC,QAAQ,EAC9C5K,EAAiBuI,IAAa2C,WAAW,CAGb,KAAA,IAAjBR,GACPA,aAAyBpC,KACzBoC,AAAsC,QAAtCA,EAAaS,kBAAkB,IAE/B,CAAC3C,EAA4BxI,GAAkBA,CAAc,CAAC2K,EAAavQ,IAAI,CAAC,EAC5E4F,EAAeoL,YAAY,EAC3BpL,EAAeoL,YAAY,CAACC,aAAa,GAC7CV,CAAAA,EAAaS,YAAY,CAAG,CACxBC,cAAe,KACnB,CAAA,EAEAzC,GAAmBvJ,OAAO,CAACqB,EAAKtG,IAAI,GAAK,GAezCuQ,EAAa1Q,KAAK,CAAGA,AAdrBA,CAAAA,EAAQ9E,EAAMmW,OAAO,CAAC,CAClB1B,GAAIjB,KACJ4C,OAAQ,EACRC,SAAU,CAAA,EACV1U,MAAO,CACH2U,KAAM,EACV,EACAC,kBAAmB,GACnBC,cAAe,CAAA,EACfhV,OAAQ,CACJiV,MAAO,OACPrR,EAAG,EACP,CACJ,EAAG,CAAA,EAAO,CAAA,EAAK,EACY7E,OAAO,CAACkU,EAAE,CACrC,IAAI,CAACqB,WAAW,IAGhBN,EAAa1Q,KAAK,CAAG9E,EAAMX,GAAG,CAACkM,EAAKkK,QAAQ,EAAElV,OAAO,CAACuE,KAAK,CAE3D4O,GAAqBxJ,OAAO,CAACqB,EAAKtG,IAAI,GAAK,GAC3CuQ,CAAAA,EAAakB,MAAM,CAACC,cAAc,CAAG3W,EAAMsJ,MAAM,CAACzH,MAAM,CAAC,SAAUyH,CAAM,EACrE,MAAOA,AAAwB,WAAxBA,EAAO/I,OAAO,CAAC0E,IAAI,AAC9B,EAAE,CAAC,EAAE,CAAC1E,OAAO,CAACkU,EAAE,AAAD,EAEnBzU,EAAM8U,SAAS,CAACU,EAAc,CAAA,GAElClC,GAA8B,IAAI,CAAE,iBAAkB,CAClDvJ,OAAQ,IAAI,CAACQ,qBAAqB,AACtC,GACAvK,EAAMQ,MAAM,EAChB,EAqIIoW,mBA5BJ,SAA4B9X,CAAC,CAAE+X,CAAC,EAC5B,GAAI,CAACxD,EAA4BvU,IAAM,CAACuU,EAA4BwD,IAGhE/X,EAAEgL,MAAM,GAAK+M,EAAE/M,MAAM,CAFrB,MAAO,CAAA,EAKX,IAAK,IAAID,EAAI,EAAGA,EAAI/K,EAAEgL,MAAM,CAAED,IAC1B,GAAI/K,CAAC,CAAC+K,EAAE,GAAKgN,CAAC,CAAChN,EAAE,CACb,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,EAgBIiN,aAvHJ,SAAsBxS,CAAC,CAAEmB,CAAU,EAC/B,IAAIlF,EAAUkF,EAAWlF,OAAO,CAACgD,WAAW,CACxCuB,EAAQyO,GAA6BhT,EAAQuE,KAAK,GAAK,IAAI,CAAC9E,KAAK,CAAC8E,KAAK,CAACvE,EAAQuE,KAAK,CAAC,CACtFA,GAASvE,EAAQ6F,MAAM,EACvBX,EAAWnF,MAAM,CAAC,CACdiD,YAAa,CACTQ,OAAQe,EAAMkC,OAAO,CAAC1C,CAAC,CAACQ,EAAMiS,KAAK,CAAG,SAAW,SAAS,EACrDxW,CAAAA,EAAQ6F,MAAM,CAAC,EAAE,CAAChB,CAAC,EAAI,CAAA,CAChC,CACJ,EAER,EA6GI4R,eA3FJ,SAAwBC,CAAU,EAC9B,OAAO,SAAU3S,CAAC,CAAEmB,CAAU,EAC1B,IAAIlF,EAAUkF,EAAWlF,OAAO,CAACgD,WAAW,CACxCqB,EAAQ2O,GAA6BhT,EAAQqE,KAAK,GAAK,IAAI,CAAC5E,KAAK,CAAC4E,KAAK,CAACrE,EAAQqE,KAAK,CAAC,CACtFE,EAAQyO,GAA6BhT,EAAQuE,KAAK,GAAK,IAAI,CAAC9E,KAAK,CAAC8E,KAAK,CAACvE,EAAQuE,KAAK,CAAC,CACtFF,GAASE,IACTvE,EAAQ6F,MAAM,CAAC1F,OAAO,CAAC,SAAUwE,CAAK,CAAEG,CAAK,EACrCA,GAAS4R,IACT/R,EAAMC,CAAC,CAAGP,EAAMoC,OAAO,CAAC1C,CAAC,CAACM,EAAMmS,KAAK,CAAG,SAAW,SAAS,EAC5D7R,EAAME,CAAC,CAAGN,EAAMkC,OAAO,CAAC1C,CAAC,CAACQ,EAAMiS,KAAK,CAAG,SAAW,SAAS,EAEpE,GACAtR,EAAWnF,MAAM,CAAC,CACdiD,YAAa,CACT6C,OAAQ7F,EAAQ6F,MAAM,AAC1B,CACJ,GAER,CACJ,EAyEI8Q,eA3DJ,SAAwBnK,CAAK,CAAEtH,CAAU,EACrC,IAAIzF,EAAQyF,EAAWzF,KAAK,CACxBO,EAAUkF,EAAWlF,OAAO,CAACgD,WAAW,CACxCqB,EAAQ2O,GAA6BhT,EAAQqE,KAAK,GAAK5E,EAAM4E,KAAK,CAACrE,EAAQqE,KAAK,CAAC,CACjFE,EAAQyO,GAA6BhT,EAAQuE,KAAK,GAAK9E,EAAM8E,KAAK,CAACvE,EAAQuE,KAAK,CAAC,CACrF,GAAIF,GAASE,EAAO,CAChB,IAAIK,EAAIP,EAAMoC,OAAO,CAAC+F,CAAK,CAACnI,EAAMmS,KAAK,CAAG,SAAW,SAAS,EAAG3R,EAAIN,EAAMkC,OAAO,CAAC+F,CAAK,CAACjI,EAAMiS,KAAK,CAAG,SAAW,SAAS,EAAGI,EAAQhS,EAAI5E,EAAQ2E,KAAK,CAACC,CAAC,CAAEpB,EAASxD,EAAQ2E,KAAK,CAACE,CAAC,CAAGA,EACtLK,EAAWnF,MAAM,CAAC,CACdiD,YAAa,CACT8O,WAAY,CACR8E,MAAOnX,EAAM4F,QAAQ,CAAG7B,EAASoT,EACjCpT,OAAQ/D,EAAM4F,QAAQ,CAAGuR,EAAQpT,CACrC,CACJ,CACJ,EACJ,CACJ,CA4CA,EAkBIqT,GAAqCjD,GAA+BC,eAAe,CAAEiD,GAAoClD,GAA+BR,cAAc,CAAE2D,GAAyCnD,GAA+Ba,mBAAmB,CAAEuC,GAA6CpD,GAA+Be,uBAAuB,CAAEsC,GAAsCrD,GAA+BmB,gBAAgB,CAAEmC,GAAkCtD,GAA+B2C,YAAY,CAAEY,GAAoCvD,GAA+B6C,cAAc,CAAEW,GAAoCxD,GAA+B+C,cAAc,CAEprBU,GAA+B,AAAC9X,IAA+EmI,SAAS,CAAE4P,GAA2B,AAAC/X,IAA+E8C,KAAK,CAc1OkV,GAAqB,CAUrBC,QAAS,CAEL3T,UAAW,qBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,UACTC,KAAM,cACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAAC6T,OAAO,CAACxS,kBAAkB,EAClD,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GACrC,AACL,EASAM,aAAc,CAEV5T,UAAW,2BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,eACTC,KAAM,cACN1B,YAAa,CACTiP,KAAM,CACFyF,UAAW,OACf,EACArT,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAAC8T,YAAY,CAACzS,kBAAkB,EACvD,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GACrC,AACL,EASAQ,IAAK,CAED9T,UAAW,iBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,MACTC,KAAM,eACN1B,YAAa,CACT0B,KAAM,MACNL,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACgU,GAAG,CAAC3S,kBAAkB,EAC9C,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GACrC,AACL,EASAS,SAAU,CAEN/T,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,WACTC,KAAM,eACN1B,YAAa,CACT0B,KAAM,MACNuN,KAAM,CACFyF,UAAW,OACf,EACArT,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACiU,QAAQ,CAAC5S,kBAAkB,EACnD,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GACrC,AACL,EAQAU,aAAc,CAEVhU,UAAW,2BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,eACTC,KAAM,eACN1B,YAAa,CACT0B,KAAM,OACNL,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACkU,YAAY,CAAC7S,kBAAkB,EACvD,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GACrC,AACL,EASAW,kBAAmB,CAEfjU,UAAW,iCAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,oBACTC,KAAM,eACN1B,YAAa,CACT0B,KAAM,OACNuN,KAAM,CACFyF,UAAW,OACf,EACArT,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAG,CACC4C,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACmU,iBAAiB,CAChC9S,kBAAkB,EAC3B,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GACrC,AACL,EAQAY,eAAgB,CAEZlU,UAAW,6BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,iBACTC,KAAM,eACNsT,UAAW,IACXhV,YAAa,CACT0B,KAAM,iBACNL,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CACdoU,cAAc,CAAC/S,kBAAkB,EAC1C,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GAC7B,CACJ,EAQAmS,aAAc,CAEVtO,UAAW,2BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,eACTC,KAAM,eACNsT,UAAW,IACXhV,YAAa,CACT0B,KAAM,eACNL,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACwO,YAAY,CAACnN,kBAAkB,EACvD,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GAC7B,CACJ,EAUAiY,SAAU,CAENpU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,CACjBtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,WACTC,KAAM,cACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACI,AACL,CACJ,EACAnF,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACsU,QAAQ,CAACjT,kBAAkB,EACnD,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAe,SAAU,CAENrU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,CACjBtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,WACTC,KAAM,cACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACI,AACL,CACJ,EACAnF,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACuU,QAAQ,CAAClT,kBAAkB,EACnD,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAgB,SAAU,CAENtU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,WACTC,KAAM,cACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EAChB,AACL,EACA1B,aAAc,CACVG,MAAO,CACHxC,MAAO,SACX,CACJ,CACJ,EAAGpB,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAACwU,QAAQ,CAACnT,kBAAkB,EACrF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAiB,SAAU,CAENvU,UAAW,sBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,WACTC,KAAM,cACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EAChB,AACL,EACA1B,aAAc,CACVG,MAAO,CACHxC,MAAO,SACX,CACJ,CACJ,EAAGpB,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAACyU,QAAQ,CAACpT,kBAAkB,EACrF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GAClCA,GAAkC,GACrC,AACL,EASAkB,SAAU,CAENxU,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,UACTC,KAAM,UACN1B,YAAa,CACTsV,WAAY,IACZjU,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBH,MAAO,CAAEC,EAAGA,EAAGC,EAAGA,CAAE,EACpB2G,WAAY,CACRtI,YAAa,EACb/B,OAAQ,SACZ,EACAmK,WAAY,CACRC,QAAS,CAAA,EACTrI,YAAa,EACb/B,OAAQ,SACZ,EACA2Q,WAAY,CACR8E,MAAO,EACPpT,OAAQ,EACRN,YAAa,EACb/B,OAAQ,SACZ,CACJ,EACAgC,aAAc,CACVG,MAAO,CACHxC,MAAO,SACX,CACJ,CACJ,EAAGpB,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAAC0U,QAAQ,CAACrT,kBAAkB,EACrF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHmS,GACH,AACL,EASAmB,SAAU,CAEN1U,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,UACTC,KAAM,UACN1B,YAAa,CACTsV,WAAY,IACZjU,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBH,MAAO,CAAEC,EAAGA,EAAGC,EAAGA,CAAE,EACpB2G,WAAY,CACRD,QAAS,CAAA,EACTrI,YAAa,EACb/B,OAAQ,SACZ,EACAmK,WAAY,CACRpI,YAAa,EACb/B,OAAQ,SACZ,EACA2Q,WAAY,CACR8E,MAAO,EACPpT,OAAQ,EACRN,YAAa,EACb/B,OAAQ,SACZ,CACJ,EACAgC,aAAc,CACVG,MAAO,CACHxC,MAAO,SACX,CACJ,CACJ,EAAGpB,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAAC4U,QAAQ,CAACvT,kBAAkB,EACrF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHmS,GACH,AACL,EASAoB,UAAW,CAEP3U,UAAW,wBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,UACTC,KAAM,UACN1B,YAAa,CACTsV,WAAY,KACZjU,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBH,MAAO,CAAEC,EAAGA,EAAGC,EAAGA,CAAE,EACpBiN,WAAY,CACR8E,MAAO,EACPpT,OAAQ,EACRN,YAAa,EACb/B,OAAQ,SACZ,EACAqK,WAAY,CACRtI,YAAa,EACb/B,OAAQ,SACZ,EACAmK,WAAY,CACRpI,YAAa,EACb/B,OAAQ,SACZ,CACJ,EACAgC,aAAc,CACVG,MAAO,CACHxC,MAAO,SACX,CACJ,CACJ,EAAGpB,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAAC6U,SAAS,CAACxT,kBAAkB,EACtF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHmS,GACH,AACL,EAaA/E,UAAW,CACPxO,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,YACTC,KAAM,YACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EAChB,AACL,EACA1B,aAAc,CACVG,MAAO,CACHxC,MAAO,SACX,CACJ,CACJ,EAAGpB,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAAC0O,SAAS,CAACrN,kBAAkB,EACtF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCD,GACH,AACL,EASAuB,gBAAiB,CAEb5U,UAAW,8BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CACjB6C,EAAIP,EAAQtC,KAAK,CACjBtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B7S,QAAS,kBACTC,KAAM,SACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CACJ,CAAEjB,EAAGA,EACjBC,EAAGA,CAAE,EACO,CAAED,EAAGA,EACjBC,EAAGA,CAAE,EACI,AACL,CACJ,EACAnF,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAAC8U,eAAe,CAC9BzT,kBAAkB,EAC3B,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCD,GACH,AACL,EASA3E,UAAW,CAEP1O,UAAW,uBAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAIM,EAAIT,EAAQnC,KAAK,CAAE6C,EAAIP,EAAQtC,KAAK,CAAEtC,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CACjH7S,QAAS,YACTC,KAAM,YACN1B,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,CAChB6C,EAAGP,EAAQtC,KAAK,CAChB0W,aAAc,CACVpV,MAAO,CACHvC,KAAM,SACV,CACJ,CACJ,EACA,CAAE6D,EAAGA,EAAGC,EAAGA,CAAE,EACb,CAAED,EAAGA,EAAGC,EAAGA,CAAE,EAChB,CACDkN,gBAAiB,CACbhR,KAAM,0BACV,CACJ,EACAgC,aAAc,CACVG,YAAa,CACjB,CACJ,EAAGxD,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAAC4O,SAAS,CAACvN,kBAAkB,EACtF,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAEAiF,MAAO,CACHkS,GAAkC,GAClCA,GAAkC,GACrC,AACL,EAWAwB,gBAAiB,CAEb9U,UAAW,8BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIsP,EAAeyD,GAAkC/S,EACjD,IAAI,CAACtE,KAAK,EAEd,GAAK4T,GAGL,IAAI,CAACsF,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,EAC/C,IAAIjZ,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CAC3E7S,QAAS,kBACTC,KAAM,eACN1B,YAAa,CACT2B,MAAO,CACHC,EAAGyO,EAAazO,CAAC,CACjBC,EAAGwO,EAAaxO,CAAC,CACjBR,MAAOgP,EAAahP,KAAK,CACzBE,MAAO8O,EAAa9O,KAAK,AAC7B,EACAzB,MAAO,CACH+S,OAAQxC,EAAaM,KAAK,CAAG,GAAK,IAClCoC,KAAM,IAAI,CAAC4C,eAAe,CAAC1H,QAAQ,EACvC,CACJ,EACA9N,aAAc,CACVG,MAAO,CACHxC,MAAO,UACPE,SAAU,OACd,CACJ,EACA+B,aAAc,CACV5B,OAAQ,sBACR+B,YAAa,CACjB,CACJ,EAAGxD,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAChDgV,eAAe,CAAC3T,kBAAkB,EAAGE,EAAa,IAAI,CAACzF,KAAK,CAAC+E,aAAa,CAACxE,EACpF,CAAA,IAAI,CAAC2Y,eAAe,GACpBzT,EAAWlF,OAAO,CAACgH,MAAM,CAACuD,KAAK,CAACpL,IAAI,CAAC+F,EAAY,CAAC,GACtD,CACJ,EAUA0T,WAAY,CACR/U,UAAW,yBACXC,MAAO,SAAUC,CAAC,EACd,IAAIsP,EAAeyD,GAAkC/S,EACjD,IAAI,CAACtE,KAAK,EAEd,GAAK4T,GAGL,IAAI3T,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CAC3E7S,QAAS,aACTC,KAAM,aACN1B,YAAa,CACTqB,MAAOgP,EAAahP,KAAK,CACzBE,MAAO8O,EAAa9O,KAAK,CACzBsB,OAAQ,CAAC,CACDjB,EAAGyO,EAAazO,CAAC,AACrB,EAAG,CACCA,EAAGyO,EAAazO,CAAC,AACrB,EAAE,CACNqN,KAAM,CACF9Q,OAAQ,sBACRJ,KAAM,cACNmC,YAAa,CACjB,CACJ,CACJ,EAAGxD,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAACiV,UAAU,CAAC5T,kBAAkB,EAAGE,EAAa,IAAI,CAACzF,KAAK,CAAC+E,aAAa,CAACxE,GAEhI,OADAkF,EAAWlF,OAAO,CAACgH,MAAM,CAACuD,KAAK,CAACpL,IAAI,CAAC+F,EAAY,CAAC,GAC3CA,EACX,EACAD,MAAO,CACHkS,GAAkC,GACrC,AACL,EACA0B,cAAe,CAEXhV,UAAW,4BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIsP,EAAeyD,GAAkC/S,EACjD,IAAI,CAACtE,KAAK,EAEd,GAAK4T,GAGL,IAAI3T,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CAC3E7S,QAAS,gBACTC,KAAM,eACN1B,YAAa,CACT2B,MAAO,CACHC,EAAGyO,EAAazO,CAAC,CACjBC,EAAGwO,EAAaxO,CAAC,CACjBR,MAAOgP,EAAahP,KAAK,CACzBE,MAAO8O,EAAa9O,KAAK,AAC7B,EACAzB,MAAO,CACH+S,OAAQxC,EAAaM,KAAK,CAAG,GAAK,GACtC,CACJ,EACAxQ,aAAc,CACVG,MAAO,CACHxC,MAAO,UACPE,SAAU,OACd,CACJ,EACA+B,aAAc,CACV5B,OAAQ,sBACR+B,YAAa,CACjB,CACJ,EAAGxD,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAChDkV,aAAa,CAAC7T,kBAAkB,EAAGE,EAAa,IAAI,CAACzF,KAAK,CAAC+E,aAAa,CAACxE,GAClFkF,EAAWlF,OAAO,CAACgH,MAAM,CAACuD,KAAK,CAACpL,IAAI,CAAC+F,EAAY,CAAC,GACtD,CACJ,EAYA4T,cAAe,CAEXjV,UAAW,4BAGXC,MAAO,SAAUC,CAAC,EACd,IAAIsP,EAAeyD,GAAkC/S,EACjD,IAAI,CAACtE,KAAK,EAEd,GAAK4T,GAGL,IAAI3T,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAAEM,EAAUsX,GAAyB,CAC3E7S,QAAS,gBACTC,KAAM,eACN1B,YAAa,CACT2B,MAAO,CACHC,EAAGyO,EAAazO,CAAC,CACjBC,EAAGwO,EAAaxO,CAAC,CACjBR,MAAOgP,EAAahP,KAAK,CACzBE,MAAO8O,EAAa9O,KAAK,AAC7B,EACAzB,MAAO,CACH+S,OAAQxC,EAAaM,KAAK,CAAG,GAAK,IAClC1Q,OAAQ,GACZ,EACAiP,UAAW,CACPnR,KAAM,OACNI,OAAQkS,EAAaM,KAAK,CACtB,UACA,SACR,CACJ,EACA5Q,aAAc,CACV5B,OAAQ,sBACR+B,YAAa,CACjB,CACJ,EAAGxD,EAAWsF,kBAAkB,CAAEtF,EAAWiE,QAAQ,CAChDmV,aAAa,CAAC9T,kBAAkB,EAAGE,EAAa,IAAI,CAACzF,KAAK,CAAC+E,aAAa,CAACxE,GAClFkF,EAAWlF,OAAO,CAACgH,MAAM,CAACuD,KAAK,CAACpL,IAAI,CAAC+F,EAAY,CAAC,GACtD,CACJ,EASA6T,mBAAoB,CAEhBlV,UAAW,kCAGXC,MAAO,SAAUC,CAAC,EACd,IAAIC,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CAEnB,GAAI,AAACG,GAAYG,GAGjB,IAAI5E,EAAa,IAAI,CAACD,KAAK,CAACO,OAAO,CAACN,UAAU,CAC1CM,EAAUsX,GAAyB,CAC/B5S,KAAM,qBACND,QAAS,qBACTzB,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGT,EAAQnC,KAAK,AACpB,EAAE,AACV,CACJ,EACAtC,EAAWsF,kBAAkB,CAC7BtF,EAAWiE,QAAQ,CAACoV,kBAAkB,CACjC/T,kBAAkB,EAC3B,OAAO,IAAI,CAACvF,KAAK,CAAC+E,aAAa,CAACxE,GACpC,EAGAiF,MAAO,CACH,SAAUlB,CAAC,CAAEmB,CAAU,EACnB,IAAIE,EAAgBF,EAAWlF,OAAO,CAACgD,WAAW,CAAC6C,MAAM,CACrDjB,EAAIQ,GAAiBA,CAAa,CAAC,EAAE,CAACR,CAAC,CACvCZ,EAAK,IAAI,CAACoI,SAAS,CAACrI,GACpBI,EAAUH,CAAE,CAAC,EAAE,CACfM,EAAUN,CAAE,CAAC,EAAE,CACfG,GAAWG,GACXY,EAAWnF,MAAM,CAAC,CACdiD,YAAa,CACTqB,MAAOF,EAAQ1C,IAAI,CAACqD,KAAK,CACzBP,MAAOD,EAAQ7C,IAAI,CAACqD,KAAK,CACzBe,OAAQ,CAAC,CACDjB,EAAGA,CACP,EAAG,CACCA,EAAGT,EAAQnC,KAAK,AACpB,EAAE,AACV,CACJ,EAER,EACH,AACL,EAUAgX,cAAe,CAEXnV,UAAW,4BAEXC,MAAO+S,GAAmC,YAC9C,EASAoC,eAAgB,CAEZpV,UAAW,6BAEXC,MAAO+S,GAAmC,OAC9C,EAUAqC,cAAe,CAEXrV,UAAW,4BAEXC,MAAO+S,GAAmC,YAC9C,EAUAsC,cAAe,CAEXtV,UAAW,4BAEXC,MAAO+S,GAAmC,QAC9C,EAUAuC,MAAO,CAEHvV,UAAW,oBAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACM,MAAM,CAAC,CACdN,MAAO,CACH4Z,QAAS,CACL3U,KAAM,GACV,CACJ,CACJ,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EASA8P,MAAO,CAEHzV,UAAW,oBAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACM,MAAM,CAAC,CACdN,MAAO,CACH4Z,QAAS,CACL3U,KAAM,GACV,CACJ,CACJ,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EASA+P,OAAQ,CAEJ1V,UAAW,qBAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACM,MAAM,CAAC,CACdN,MAAO,CACH4Z,QAAS,CACL3U,KAAM,IACV,CACJ,CACJ,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAQAgQ,eAAgB,CAEZ3V,UAAW,8BAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACsJ,MAAM,CAAC,EAAE,CAAChJ,MAAM,CAAC,CACxB2E,KAAM,OACN+U,YAAa,CAAA,CACjB,GACApC,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAQAkQ,eAAgB,CAEZ7V,UAAW,8BAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACsJ,MAAM,CAAC,EAAE,CAAChJ,MAAM,CAAC,CACxB2E,KAAM,MACV,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAQAmQ,sBAAuB,CAEnB9V,UAAW,qCAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACsJ,MAAM,CAAC,EAAE,CAAChJ,MAAM,CAAC,CACxB2E,KAAM,aACV,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAQAoQ,qBAAsB,CAElB/V,UAAW,oCAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACsJ,MAAM,CAAC,EAAE,CAAChJ,MAAM,CAAC,CACxB2E,KAAM,YACV,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAQAqQ,cAAe,CACXhW,UAAW,6BACXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACsJ,MAAM,CAAC,EAAE,CAAChJ,MAAM,CAAC,CACxB2E,KAAM,MACN+U,YAAa,CAAA,CACjB,GACApC,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAQAsQ,4BAA6B,CAEzBjW,UAAW,2CAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI,CAAC/J,KAAK,CAACsJ,MAAM,CAAC,EAAE,CAAChJ,MAAM,CAAC,CACxB2E,KAAM,mBACV,GACA2S,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAUAuQ,WAAY,CAERlW,UAAW,yBACX6F,YAAa,SAEbuE,KAAM,SAAUzE,CAAM,EACd,IAAI,CAAC/J,KAAK,CAACua,UAAU,EACrB,IAAI,CAACva,KAAK,CAACua,UAAU,CAACC,MAAM,GAEhC5C,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAUA0Q,sBAAuB,CAEnBrW,UAAW,qCAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI/J,EAAQ,IAAI,CAACA,KAAK,CAClBsJ,EAAStJ,EAAMsJ,MAAM,CACrBoR,EAAM1a,EAAM0O,UAAU,CACtBiM,EAAwBpD,GAA2CvX,EAAMsJ,MAAM,EAC/EoR,GAAOA,EAAI7F,UAAU,GACrBvL,EAAO5I,OAAO,CAAC,SAAU4I,CAAM,EAC3BA,EAAOhJ,MAAM,CAAC,CACV+U,UAAW,CAAEvJ,QAAS,CAAC6O,CAAsB,EAC7CvF,iBAAkB,CACdtJ,QAAS,CAAC6O,EACVtX,MAAO,CAAEyI,QAAS,CAAA,CAAK,CAC3B,CACJ,EAAG,CAAA,EACP,GACA9L,EAAMQ,MAAM,IAEhBoX,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAaA6Q,WAAY,CAERxW,UAAW,wBAGXoK,KAAM,WACF,IAAIvO,EAAa,IAAI,CACrB2X,GAA6B3X,EAAY,YAAa,CAClDmL,SAAU,aACV7K,QAAS,CAAC,EAEV+K,SAAU,SAAUC,CAAI,EACpBiM,GAAoC9X,IAAI,CAACO,EAAYsL,EACzD,CACJ,EACJ,CACJ,EAQAsP,kBAAmB,CAEfzW,UAAW,gCAGXoK,KAAM,SAAUzE,CAAM,EAClB,IAAI/J,EAAQ,IAAI,CAACA,KAAK,CAClB0a,EAAM1a,EAAM0O,UAAU,CACtBoM,EAAWJ,EAAIK,WAAW,EAC9B,CAAA,IAAI,CAACC,kBAAkB,CAAG,CAAC,IAAI,CAACA,kBAAkB,CAClD,AAAChb,CAAAA,EAAMib,WAAW,EAAI,EAAE,AAAD,EAAGva,OAAO,CAAC,SAAU+E,CAAU,EAClDA,EAAWyV,aAAa,CAAC,CAAC,IAAI,CAACF,kBAAkB,CACrD,EAAG,IAAI,EACHN,GAAOA,EAAI7F,UAAU,GACjB,IAAI,CAACmG,kBAAkB,CACvBjR,EAAOoR,UAAU,CAACtX,KAAK,CAAC,mBAAmB,CACvC,QAAUiX,EACN,2BAGR/Q,EAAOoR,UAAU,CAACtX,KAAK,CAAC,mBAAmB,CACvC,QAAUiX,EACN,6BAGhBlD,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,EAYAqR,UAAW,CAEPhX,UAAW,wBACX6F,YAAa,SAGbuE,KAAM,SAAUzE,CAAM,EAClB,IACI/J,EAAQC,AADK,IAAI,CACED,KAAK,CACxBib,EAAc,EAAE,CAChBL,EAAa,EAAE,CACfS,EAAQ,EAAE,CACVC,EAAQ,EAAE,CACdtb,EAAMib,WAAW,CAACva,OAAO,CAAC,SAAU+E,CAAU,CAAEJ,CAAK,EACjD4V,CAAW,CAAC5V,EAAM,CAAGI,EAAWwP,WAAW,AAC/C,GACAjV,EAAMsJ,MAAM,CAAC5I,OAAO,CAAC,SAAU4I,CAAM,EAC7BA,EAAOiS,EAAE,CAAC,OACVX,EAAWva,IAAI,CAACiJ,EAAO2L,WAAW,EAEb,UAAhB3L,EAAOrE,IAAI,EAChBoW,EAAMhb,IAAI,CAACiJ,EAAO2L,WAAW,CAErC,GACAjV,EAAM8E,KAAK,CAACpE,OAAO,CAAC,SAAUoE,CAAK,EAC3BwS,GAAuCxS,IACvCwW,EAAMjb,IAAI,CAACyE,EAAMvE,OAAO,CAEhC,GACAT,IAA8E+H,GAAG,CAAC2T,YAAY,CAACC,OAAO,CAAC,mBAAoBC,KAAKC,SAAS,CAAC,CACtIV,YAAaA,EACbL,WAAYA,EACZS,MAAOA,EACPC,MAAOA,CACX,IACA1D,GAA6B,IAAI,CAAE,iBAAkB,CAAE7N,OAAQA,CAAO,EAC1E,CACJ,CACJ,EAirCI6R,GAAqB,CACrB9Y,KArpC0B,CAQ1B4L,WAAY,CACRgM,IAAK,CAED1X,aAAc,gBACdC,MAAO,QACP4Y,aAAc,gBACdlJ,QAAS,UACTmJ,SAAU,WACVjB,kBAAmB,qBACnBkB,eAAgB,kBAChBV,MAAO,QACPW,WAAY,cACZC,WAAY,cACZb,UAAW,aACXR,WAAY,aACZH,sBAAuB,2BAEvBd,MAAO,SACPE,MAAO,SACPC,OAAQ,WACRQ,WAAY,aACZ4B,SAAU,OACVC,SAAU,OACVC,gBAAiB,cACjBC,QAAS,MACTC,sBAAuB,qBACvBC,eAAgB,cAEhBrZ,OAAQ,SACRC,QAAS,UACTE,MAAO,QACPD,UAAW,YAEXmW,cAAe,cACfC,eAAgB,eAChBC,cAAe,cACfC,cAAe,cAEfX,UAAW,aACXH,SAAU,YACVE,SAAU,YAEVf,QAAS,UACTC,aAAc,gBACdE,IAAK,MACLC,SAAU,YACV3F,KAAM,OACN6F,kBAAmB,aACnBC,eAAgB,kBAChB5F,aAAc,gBACd0F,aAAc,gBAEdI,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBAEVO,gBAAiB,mBACjBE,cAAe,iBACfC,cAAe,iBAEfzG,UAAW,YACX0G,mBAAoB,uBACpBxG,UAAW,YACXkG,gBAAiB,mBACjBG,WAAY,aAChB,CACJ,EACAlZ,WAAY,CACR8C,MAAO,CAEHG,OAAQ,SACRC,QAAS,UACTC,UAAW,YACXC,MAAO,QACP0U,QAAS,UACTC,aAAc,gBACdE,IAAK,MACLC,SAAU,YACV3F,KAAM,OACN6F,kBAAmB,aACnBC,eAAgB,kBAChB5F,aAAc,gBACd8F,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBACVC,SAAU,iBACVO,gBAAiB,mBACjBE,cAAe,iBACfC,cAAe,iBACfzG,UAAW,YACX0G,mBAAoB,uBACpBxG,UAAW,YACXkG,gBAAiB,mBACjBZ,aAAc,gBACdzF,QAAS,UACToG,UAAW,aACXH,SAAU,YACVE,SAAU,YACVK,WAAY,cAEZkC,MAAO,QAEPmB,UAAW,MACXC,WAAY,OACZC,WAAY,OACZC,aAAc,SACdrT,OAAQ,SACRsT,OAAQ,SACRnK,UAAW,YAEXH,gBAAiB,mBACjBC,gBAAiB,mBACjBxG,WAAY,cACZF,WAAY,cACZgH,OAAQ,SACRR,WAAY,aAEZwK,cAAe,WAEfC,iBAAkB,oBAClBC,YAAa,iBACb1X,MAAO,QACP2X,OAAQ,SACRC,QAAS,UACTC,kBAAmB,qBACnBC,aAAc,gBACdC,kBAAmB,uBACnBC,UAAW,aACXC,cAAe,iBACfC,YAAa,eACbC,WAAY,cACZC,aAAc,gBACdC,SAAU,WACVC,UAAW,YACXC,QAAS,WACTC,WAAY,cACZC,0BAA2B,8BAC3BC,sBAAuB,0BACvBC,UAAW,YACXC,WAAY,aACZC,OAAQ,SACRC,UAAW,aACXC,SAAU,YACVC,UAAW,YACXC,UAAW,cACXC,OAAQ,SACRC,cAAe,sBACfC,cAAe,sBACfC,QAAS,UAOTC,iBAAkB,CAQdC,OAAQ,CAAC,qBAAqB,CAO9BC,GAAI,CAAC,kBAAkB,CAOvBC,KAAM,CAAC,oCAAoC,CAO3CC,IAAK,CAAC,6BAA6B,CAOnCC,IAAK,CAAC,qBAAqB,CAO3BC,gBAAiB,CAAC,mBAAmB,CAOrCC,iBAAkB,CAAC,oBAAoB,CAOvCC,YAAa,CAAC,eAAe,CAO7BC,GAAI,CAAC,gBAAgB,CAOrBC,eAAgB,CAAC,kBAAkB,CAOnCC,KAAM,CAAC,gBAAgB,CAOvBC,IAAK,CAAC,wBAAwB,CAO9BC,WAAY,CAAC,cAAc,CAO3BC,KAAM,CAAC,oCAAoC,CAO3CC,IAAK,CAAC,kBAAkB,CAOxBC,KAAM,CAAC,iCAAiC,CAOxCC,IAAK,CAAC,0BAA0B,CAOhCC,OAAQ,CAAC,UAAU,CAQnBC,IAAK,CAAC,2BAA2B,CAOjCC,GAAI,CAAC,4BAA4B,CAOjCC,MAAO,CAAC,QAAQ,CAOhBC,gBAAiB,CAAC,mBAAmB,CAOrCC,IAAK,CAAC,qBAAqB,CAO3BC,GAAI,CAAC,qBAAqB,CAO1BC,IAAK,CAAC,0BAA0B,CAOhCC,QAAS,CAAC,UAAU,CAOpBC,IAAK,CAAC,qBAAqB,CAO3BC,IAAK,CAAC,6BAA6B,CAOnCC,eAAgB,CAAC,kBAAkB,CAOnCC,IAAK,CAAC,6BAA6B,CAOnCC,IAAK,CAAC,6BAA6B,CAOnCC,QAAS,CAAC,qBAAqB,CAO/BC,sBAAuB,CAAC,0BAA0B,CAOlDC,0BAA2B,CAAC,8BAA8B,CAO1DC,sBAAuB,CAAC,0BAA0B,CAOlDC,KAAM,CAAC,wCAAwC,CAO/CC,IAAK,CAAC,mBAAmB,CAOzBC,SAAU,CAAC,WAAW,CAOtBC,KAAM,CAAC,gCAAgC,CAOvCC,IAAK,CAAC,oBAAoB,CAO1BC,IAAK,CAAC,8BAA8B,CAOpCC,IAAK,CAAC,iBAAiB,CAOvBC,IAAK,CAAC,0BAA0B,CAOhCC,eAAgB,CAAC,kBAAkB,CAOnCC,WAAY,CAAC,aAAa,CAO1BC,KAAM,CAAC,OAAO,CAOdC,UAAW,CAAC,cAAc,AAC9B,CACJ,CACJ,CACJ,EAmqBIhT,WAppBa,CAIbgM,IAAK,CAeD5O,QAAS,CAAA,EAKT1H,UAAW,8BAKXud,iBAAkB,qBA4BlBC,QAAS,CACL,aACA,YACA,eACA,QACA,eACA,UACA,WACA,oBACA,YACA,iBACA,QACA,YACA,aACA,aACA,aACA,YACA,wBACA,YACH,CAKDC,YAAa,CACTC,UAAW,CACPC,YAAa,OAIbC,OAAQ,eACZ,EACAhf,aAAc,CAcVif,MAAO,CACH,QACA,SACA,UACA,YACH,CACD/e,OAAQ,CAOJ8e,OAAQ,YACZ,EACA7e,QAAS,CAOL6e,OAAQ,aACZ,EACA5e,UAAW,CAOP4e,OAAQ,eACZ,EACA3e,MAAO,CAOH2e,OAAQ,WACZ,CACJ,EACA3G,MAAO,CAcH4G,MAAO,CACH,gBACA,iBACA,gBACA,gBACH,CACDvI,cAAe,CAOXsI,OAAQ,gBACZ,EACAxI,eAAgB,CAOZwI,OAAQ,kBACZ,EACAvI,cAAe,CAMXuI,OAAQ,kBACZ,EACAzI,cAAe,CAMXyI,OAAQ,iBACZ,CACJ,EACA/e,MAAO,CAiBHgf,MAAO,CACH,UACA,eACA,MACA,WACA,OACA,oBACA,iBACA,eACH,CACDlK,QAAS,CAMLiK,OAAQ,aACZ,EACAhK,aAAc,CAMVgK,OAAQ,mBACZ,EACA9J,IAAK,CAMD8J,OAAQ,SACZ,EACA7J,SAAU,CAMN6J,OAAQ,eACZ,EACAxP,KAAM,CAMFwP,OAAQ,UACZ,EACA3J,kBAAmB,CAMf2J,OAAQ,gBACZ,EACAtP,aAAc,CAMVsP,OAAQ,mBACZ,EACA1J,eAAgB,CAMZ0J,OAAQ,qBACZ,CACJ,EACAnG,aAAc,CAcVoG,MAAO,CACH,WACA,WACA,WACA,WACH,CACDzJ,SAAU,CAMNwJ,OAAQ,eACZ,EACAvJ,SAAU,CAMNuJ,OAAQ,eACZ,EACAtJ,SAAU,CAMNsJ,OAAQ,eACZ,EACArJ,SAAU,CAMNqJ,OAAQ,eACZ,CACJ,EACAjG,eAAgB,CAYZkG,MAAO,CACH,kBACA,gBACA,gBACH,CACD/I,gBAAiB,CAMb8I,OAAQ,sBACZ,EACA5I,cAAe,CAMX4I,OAAQ,oBACZ,EACA3I,cAAe,CAMX2I,OAAQ,oBACZ,CACJ,EACAlG,SAAU,CAcNmG,MAAO,CACH,YACA,qBACA,YACA,kBACA,aACH,CACDnP,UAAW,CAMPkP,OAAQ,eACZ,EACApP,UAAW,CAMPoP,OAAQ,eACZ,EACA1I,mBAAoB,CAMhB0I,OAAQ,wBACZ,EACAhJ,gBAAiB,CAMbgJ,OAAQ,sBACZ,EACA7I,WAAY,CAMR6I,OAAQ,iBACZ,CACJ,EACArP,QAAS,CAYLsP,MAAO,CACH,YACA,WACA,WACH,CACDrJ,SAAU,CAMNoJ,OAAQ,eACZ,EACAlJ,SAAU,CAMNkJ,OAAQ,eACZ,EACAjJ,UAAW,CAMPiJ,OAAQ,gBACZ,CACJ,EACAnH,kBAAmB,CAMfmH,OAAQ,yBACZ,EACAvH,sBAAuB,CAMnBuH,OAAQ,wBACZ,EACApH,WAAY,CAMRoH,OAAQ,gBACZ,EACAhG,WAAY,CAYRiG,MAAO,CACH,QACA,QACA,SACH,CACDtI,MAAO,CAMHqI,OAAQ,YACZ,EACAnI,MAAO,CAMHmI,OAAQ,YACZ,EACAlI,OAAQ,CAMJkI,OAAQ,aACZ,CACJ,EACA/F,WAAY,CAaRgG,MAAO,CACH,WACA,WACA,kBACA,wBACA,UACA,iBACH,CACD/F,SAAU,CAMN8F,OAAQ,iBACZ,EACA7F,SAAU,CAMN6F,OAAQ,iBACZ,EACA5F,gBAAiB,CAMb4F,OAAQ,wBACZ,EACA3F,QAAS,CAML2F,OAAQ,gBACZ,EACAzF,eAAgB,CAMZyF,OAAQ,wBACZ,EACA1F,sBAAuB,CAMnB0F,OAAQ,+BACZ,CACJ,EACA1H,WAAY,CAMR0H,OAAQ,gBACZ,EACA5G,UAAW,CAMP4G,OAAQ,gBACZ,CACJ,EAMAzY,QAAS,CAAA,CACb,CACJ,CASA,EAiBI2Y,GAAwB,AAACpiB,IAA+E4H,UAAU,CAMlHya,GAAiChO,GAA+Ba,mBAAmB,CAAEoN,GAAqCjO,GAA+Be,uBAAuB,CAEhLmN,GAAe,AAACviB,IAA+EuiB,YAAY,CAAEC,GAAqB,AAACxiB,IAA+EgB,OAAO,CAAEyhB,GAAsB,AAACziB,IAA+EiB,QAAQ,CAAEyhB,GAAkB,AAAC1iB,IAA+EkB,IAAI,CAyDrb,SAASyhB,GAA4BnH,CAAK,CAAEoH,CAAU,CAAEC,CAAa,CAAE/M,CAAiB,EACpF,IACIgN,EACAC,EACAC,EAHAC,EAAgB,EAKpB,SAASC,EAAazjB,CAAI,EACtB,OAAO+iB,GAAmB/iB,IAAS,CAACgjB,GAAoBhjB,IAASA,EAAK8Q,KAAK,CAAC,IAChF,CAyCA,OAxCIuF,IACAkN,EAAaT,GAAcpS,WAAW2F,EAAkBC,GAAG,EAAI,KAC/DgN,EAAgBR,GAAcpS,WAAW2F,EAAkB7R,MAAM,EAAI,MAsClE,CAAEkf,UApCO3H,EAAMpJ,GAAG,CAAC,SAAUpN,CAAK,CACrCO,CAAK,EACD,IAAItB,EAASse,GAAaW,EAAale,EAAMvE,OAAO,CAACwD,MAAM,EACvDkM,WAAWnL,EAAMvE,OAAO,CAACwD,MAAM,EAAI,IACnCe,EAAMf,MAAM,CAAG2e,GACvB7M,EAAMwM,GAAaW,EAAale,EAAMvE,OAAO,CAACsV,GAAG,EACzC5F,WAAWnL,EAAMvE,OAAO,CAACsV,GAAG,EAAI,IAChC,AAAC/Q,CAAAA,EAAM+Q,GAAG,CAAG/Q,EAAM9E,KAAK,CAACyN,OAAO,AAAD,EAAKiV,GAwB5C,OAvBKG,GAkBGhN,EAAMiN,GACNjN,CAAAA,GAAOgN,CAAY,EAEvBE,EAAgBjd,KAAKzD,GAAG,CAAC0gB,EAAe,AAAClN,CAAAA,GAAO,CAAA,EAAM9R,CAAAA,GAAU,CAAA,KAlB3Dwe,GAAoBxe,IAGrBA,CAAAA,EAASuX,CAAK,CAACjW,EAAQ,EAAE,CAACiE,MAAM,CAC3B4Z,KAAK,CAAC,SAAUnU,CAAC,EAAI,OAAOA,EAAEwM,EAAE,CAAC,MAAQ,GAC1CqH,EAAqBD,EAAgB,GAAE,EAE1CJ,GAAoB1M,IACrBA,CAAAA,EAAMkN,CAAY,EAEtBH,EAAqB7e,EACrBgf,EAAgBV,GAAavc,KAAKzD,GAAG,CAAC0gB,EAAe,AAAClN,CAAAA,GAAO,CAAA,EAAM9R,CAAAA,GAAU,CAAA,KAS1E,CACHA,OAAQA,AAAS,IAATA,EACR8R,IAAKA,AAAM,IAANA,CACT,CACJ,GAC+BkN,cAAeA,CAAc,CAChE,CAiBA,SAASI,GAA2B7H,CAAK,EACrC,IAAI8H,EAAW,EAAE,CAqBjB,OApBA9H,EAAM5a,OAAO,CAAC,SAAU2iB,CAAM,CAAEhe,CAAK,EACjC,IAAIie,EAAYhI,CAAK,CAACjW,EAAQ,EAAE,CAE5Bie,EACAF,CAAQ,CAAC/d,EAAM,CAAG,CACdyG,QAAS,CAAA,EACTyX,eAAgB,CACZC,KAAM,CACFhB,GAAgBc,EAAU/iB,OAAO,CAACkU,EAAE,CAAE6O,EAAUje,KAAK,EACxD,AACL,CACJ,EAIA+d,CAAQ,CAAC/d,EAAM,CAAG,CACdyG,QAAS,CAAA,CACb,CAER,GACOsX,CACX,CAmBA,SAASK,GAAoCR,CAAS,CAAES,CAAY,CAAEC,CAAY,CAAEC,CAAK,EASrF,OARAX,EAAUviB,OAAO,CAAC,SAAU6F,CAAQ,CAAElB,CAAK,EACvC,IAAIwe,EAAeZ,CAAS,CAAC5d,EAAQ,EAAE,AACvCkB,CAAAA,EAASsP,GAAG,CAAG,AAACgO,EACZxB,GAAawB,EAAa9f,MAAM,CAAG8f,EAAahO,GAAG,EADxB,EAE3B8N,GACApd,CAAAA,EAASxC,MAAM,CAAGse,GAAa9b,EAASxC,MAAM,CAAG6f,EAAQF,EAAY,CAE7E,GACOT,CACX,CAYA,SAASa,GAAsBlO,CAAiB,EAG5C,IAAI5V,EAAQ,IAAI,CAACA,KAAK,CAElBsb,EAAQtb,EAAM8E,KAAK,CAACjD,MAAM,CAACsgB,IAC3BO,EAAa1iB,EAAM0iB,UAAU,CAE7Bne,EAAK,IAAI,CAACwf,iBAAiB,CAACzI,EAC5BoH,EAPgB,GAShB9M,GACAqN,EAAY1e,EAAG0e,SAAS,CACxBF,EAAgBxe,EAAGwe,aAAa,CAChCK,EAAW,IAAI,CAACY,gBAAgB,CAAC1I,EAIjC,EAAC1F,GACDmN,GAAiBV,GAAa,GAC9BY,CAAS,CAACA,EAAUnZ,MAAM,CAAG,EAAE,CAAG,CAC9B/F,OAnBY,GAoBZ8R,IAAKwM,GAAaU,AAAgB,IAAhBA,EApBN,GAqBhB,EAGAE,EAAUviB,OAAO,CAAC,SAAU6F,CAAQ,EAChCA,EAASxC,MAAM,CAAG,AAACwC,EAASxC,MAAM,CAAIgf,CAAAA,AAAgB,IAAhBA,CAAkB,EAAM,IAC9Dxc,EAASsP,GAAG,CAAG,AAACtP,EAASsP,GAAG,CAAIkN,CAAAA,AAAgB,IAAhBA,CAAkB,EAAM,GAC5D,GAEJE,EAAUviB,OAAO,CAAC,SAAU6F,CAAQ,CAAElB,CAAK,EACvCiW,CAAK,CAACjW,EAAM,CAAC/E,MAAM,CAAC,CAChByD,OAAQwC,EAASxC,MAAM,CAAG,IAC1B8R,IAAKtP,EAASsP,GAAG,CAAG,IACpBoO,OAAQb,CAAQ,CAAC/d,EAAM,CACvB+Q,OAAQ,CACZ,EAAG,CAAA,EACP,EACJ,CAYA,IAAI8N,GAAuF5lB,EAAoB,KAC3G6lB,GAA2G7lB,EAAoBI,CAAC,CAACwlB,IAiBjIE,GAAwB,AAACtkB,IAA+EgI,QAAQ,CAAEuc,GAAgB,AAACvkB,IAA+EukB,aAAa,CAAEC,GAAM,AAACxkB,IAA+EwkB,GAAG,CAAEC,GAAuB,AAACzkB,IAA+EgB,OAAO,CAAE0jB,GAAyB,AAAC1kB,IAA+EmI,SAAS,CAAEwc,GAAW,AAAC3kB,IAA+E2kB,QAAQ,CAAEC,GAAuB,AAAC5kB,IAA+EoI,OAAO,CAAEyc,GAAqB,AAAC7kB,IAA+E8C,KAAK,CAAEgiB,GAAoB,AAAC9kB,IAA+EkB,IAAI,CACz8B6jB,GAAkC1Q,GAA+ByC,kBAAkB,CAqBnFkO,GAAyB,WAMzB,SAASA,EAAQvkB,CAAO,CAAEwkB,CAAW,CAAE/kB,CAAK,EACxC,IAAI,CAACmX,KAAK,CAAG,EACb,IAAI,CAAC6N,OAAO,CAAG,CAAA,EACf,IAAI,CAAChlB,KAAK,CAAGA,EACb,IAAI,CAACO,OAAO,CAAGA,EACf,IAAI,CAACuC,IAAI,CAAGiiB,EAEZ,IAAI,CAACjK,QAAQ,CAAG,IAAI,CAACC,WAAW,GAChC,IAAI,CAAClG,UAAU,CAAGtU,EAAQuL,OAAO,CACjC,IAAI,CAACvC,OAAO,CAAGqb,GAAkBrkB,EAAQgJ,OAAO,CAAE,CAAA,GAClD,IAAI,CAAC0b,YAAY,CAAG1kB,EAAQ6D,SAAS,CACrC,IAAI,CAACud,gBAAgB,CAAGphB,EAAQohB,gBAAgB,CAGhD,IAAI,CAACrV,cAAc,CAAG,EAAE,CACpB,IAAI,CAACuI,UAAU,GACf,IAAI,CAACqQ,eAAe,GACpB,IAAI,CAACC,aAAa,GAClB,IAAI,CAACC,kBAAkB,IAE3BZ,GAAuB,IAAI,CAAE,YACjC,CAyiBA,OA/hBAM,EAAQtlB,SAAS,CAAC2lB,aAAa,CAAG,WAC9B,IAAI1kB,EAAQ,IAAI,CACZqC,EAAO,IAAI,CAACA,IAAI,CAChBuiB,EAAa,IAAI,CAAC9kB,OAAO,CACzB8T,EAAU,IAAI,CAACA,OAAO,CACtBuN,EAAUyD,EAAWzD,OAAO,CAC5B0D,EAAOD,EAAWxD,WAAW,CAC7B0D,EAAalR,EAAQmR,UAAU,AACnC,CAAA,IAAI,CAACC,UAAU,CAAG7D,EAElBA,EAAQlhB,OAAO,CAAC,SAAUglB,CAAO,EAC7B,IAAI3b,EAAStJ,EAAM+b,SAAS,CAACnI,EACzBiR,EACAI,EACA5iB,GACJrC,EAAM6L,cAAc,CAACjM,IAAI,CAAC+jB,GAAsBra,EAAO4b,aAAa,CAAE,QAAS,WAAc,OAAOllB,EAAMmlB,kBAAkB,CAACL,EAAYxb,EAAO4b,aAAa,CAAG,IAC5JjB,GAAqBY,CAAI,CAACI,EAAQ,CAACzD,KAAK,GAExCxhB,EAAMolB,UAAU,CAAC9b,EAAQub,CAAI,CAACI,EAAQ,CAE9C,EACJ,EAaAZ,EAAQtlB,SAAS,CAACqmB,UAAU,CAAG,SAAUC,CAAS,CAAE/b,CAAM,EACtD,IAAItJ,EAAQ,IAAI,CACZslB,EAAeD,EAAUC,YAAY,CACrCJ,EAAgBG,EAAUH,aAAa,CACvCK,EAAcvB,GAASkB,EAAe,SACtCM,EAAU,IAAI,CAACA,OAAO,CACtBC,EAAc,IAAI,CAACC,WAAW,CAC9BZ,EAAa,IAAI,CAAClR,OAAO,CAACmR,UAAU,CAEpCY,EAAiB,IAAI,CAACC,OAAO,CAAGhC,GAAc,KAAM,CAChDjgB,UAAW,4BACf,EACA,KAAK,EACLuhB,GAEJ,IAAI,CAACW,eAAe,CAACX,EAAe5b,GAEpC,IAAI,CAACuC,cAAc,CAACjM,IAAI,CAAC+jB,GAAsB2B,EAAc,QAAS,SAAUzhB,CAAC,EAK7E,GAJAA,EAAEiiB,eAAe,GAEjB9lB,EAAMmlB,kBAAkB,CAACL,EAAYI,GAEjCA,EAAcvhB,SAAS,CACtB8F,OAAO,CAAC,uBAAyB,EAClCgc,EAAYriB,KAAK,CAACsT,KAAK,CACnB+O,EAAYM,UAAU,CAAG,KAC7Bb,EAAcxb,SAAS,CAACC,MAAM,CAAC,sBAC/Bgc,EAAeviB,KAAK,CAAC4iB,OAAO,CAAG,WAE9B,CAGDL,EAAeviB,KAAK,CAAC4iB,OAAO,CAAG,QAC/B,IAAIC,EAAYN,EAAeO,YAAY,CACnChB,EAAcgB,YAAY,CAAG,CAKnCP,CAAAA,EAAeO,YAAY,CACzBhB,EAAciB,SAAS,CACvBX,EAAQU,YAAY,EAEpBhB,EAAciB,SAAS,CAAGF,GAC1BA,CAAAA,EAAY,CAAA,EAGhBpC,GAAI8B,EAAgB,CAChBvQ,IAAK,CAAC6Q,EAAY,KAClBG,KAAMb,EAAc,EAAI,IAC5B,GACAL,EAAcvhB,SAAS,EAAI,sBAC3B8hB,EAAYM,UAAU,CAAGP,EAAQa,WAAW,CAC5CZ,EAAYriB,KAAK,CAACsT,KAAK,CAAG+O,EAAYM,UAAU,CAC5C/B,GAASyB,EAAa,gBACtBE,EAAeU,WAAW,CAAG,EAAI,IACzC,CACJ,GACJ,EAYAhC,EAAQtlB,SAAS,CAAC8mB,eAAe,CAAG,SAAUX,CAAa,CAAE5b,CAAM,EAC/D,IAMIgd,EANAtmB,EAAQ,IAAI,CACZumB,EAAQ,IAAI,CACZZ,EAAiB,IAAI,CAACC,OAAO,CAC7BvjB,EAAO,IAAI,CAACA,IAAI,CAChBojB,EAAc,IAAI,CAACC,WAAW,CAIlClE,AAHYlY,EAAOkY,KAAK,CAGlBvhB,OAAO,CAAC,SAAUglB,CAAO,EAE3BqB,EAAatmB,EAAM+b,SAAS,CAAC4J,EAAgBrc,EAAQ2b,EAAS5iB,GAC9DrC,EAAM6L,cAAc,CAACjM,IAAI,CAAC+jB,GAAsB2C,EAAWE,UAAU,CAAE,QAAS,WAC5ED,EAAME,YAAY,CAAC,IAAI,CAAEvB,EAAe,CAAA,GACxCO,EAAYriB,KAAK,CAACsT,KAAK,CACnB+O,EAAYM,UAAU,CAAG,KAC7BJ,EAAeviB,KAAK,CAAC4iB,OAAO,CAAG,MACnC,GACJ,GAEA,IAAIU,EAAmBf,EAAexc,gBAAgB,CAAC,iCAAiC,CAAC,EAAE,CAE3F,IAAI,CAACsd,YAAY,CAACC,EAAkB,CAAA,EACxC,EAKArC,EAAQtlB,SAAS,CAAComB,kBAAkB,CAAG,SAAUhE,CAAO,CAAEwF,CAAa,CAAEC,CAAY,EACjF,EAAE,CAAC3mB,OAAO,CAAChB,IAAI,CAACkiB,EAAS,SAAU0F,CAAG,EAC9BA,IAAQF,IACRE,EAAInd,SAAS,CAACC,MAAM,CAAC,sBACrBkd,EAAInd,SAAS,CAACC,MAAM,CAAC,qBAIjBid,AAHJA,CAAAA,EACIC,EAAI1d,gBAAgB,CAAC,8BAA6B,EAErCE,MAAM,CAAG,GACtBud,CAAAA,CAAY,CAAC,EAAE,CAACxjB,KAAK,CAAC4iB,OAAO,CAAG,MAAK,EAGjD,EACJ,EAsBA3B,EAAQtlB,SAAS,CAACgd,SAAS,CAAG,SAAUlW,CAAM,CAAE/F,CAAO,CAAEmlB,CAAO,CAAE5iB,CAAI,EACrD,KAAK,IAAdA,GAAmBA,CAAAA,EAAO,CAAC,CAAA,EAC/B,IAAIykB,EAAahnB,CAAO,CAACmlB,EAAQ,CAC7BzD,EAAQsF,EAAWtF,KAAK,CACxBuF,EAAe1C,EAAQtlB,SAAS,CAACgoB,YAAY,CAC7CC,EAAgBF,EAAWnjB,SAAS,EAAI,GAExCuhB,EAAgBtB,GAAc,KAAM,CAChCjgB,UAAWwgB,GAAkB4C,CAAY,CAAC9B,EAAQ,CAAE,IAAM,IAAM+B,EAChE9lB,MAAOmB,CAAI,CAAC4iB,EAAQ,EAAIA,CAC5B,EACA,KAAK,EACLpf,GAGA2gB,EAAa5C,GADEkD,EAAWxF,WAAW,EAAI,SACD,CACpC3d,UAAW,0BACf,EACA,KAAK,EACLuhB,GAEJ,GAAI1D,GAASA,EAAMnY,MAAM,CAAE,CAEvB,IAAIic,EAAe1B,GAAc,SAAU,CACnCjgB,UAAW,sDAEf,EACA,KAAK,EACLuhB,GAGJ,OAFAI,EAAaliB,KAAK,CAAC6jB,eAAe,CAAG,OACjC,IAAI,CAAC5M,QAAQ,CAAG,oBACb,CACH6K,cAAeA,EACfsB,WAAYA,EACZlB,aAAcA,CAClB,CACJ,CAGA,OAFAkB,EAAWpjB,KAAK,CAAC6jB,eAAe,CAAG,OAC/B,IAAI,CAAC5M,QAAQ,CAAGyM,EAAWvF,MAAM,CAAG,IACjC,CACH2D,cAAeA,EACfsB,WAAYA,CAChB,CACJ,EAKAnC,EAAQtlB,SAAS,CAACmoB,aAAa,CAAG,WAC9B,IAAI1B,EAAU,IAAI,CAACA,OAAO,AAE1B,CAAA,IAAI,CAAC2B,YAAY,CAAGvD,GAAc,MAAO,CACrCjgB,UAAW,0BACf,GACA,IAAI,CAACyjB,OAAO,CAAGxD,GAAc,MAAO,CAChCjgB,UAAW,qBACf,EAAG,KAAK,EAAG,IAAI,CAACwjB,YAAY,EAC5B,IAAI,CAACC,OAAO,CAAChkB,KAAK,CAAC6jB,eAAe,CAC9B,OAAS,IAAI,CAAC5M,QAAQ,CAAG,mBAC7B,IAAI,CAACgN,SAAS,CAAGzD,GAAc,MAAO,CAClCjgB,UAAW,uBACf,EAAG,KAAK,EAAG,IAAI,CAACwjB,YAAY,EAC5B,IAAI,CAACE,SAAS,CAACjkB,KAAK,CAAC6jB,eAAe,CAChC,OAAS,IAAI,CAAC5M,QAAQ,CAAG,mBAC7BmL,EAAQ8B,YAAY,CAAC,IAAI,CAACH,YAAY,CAAE3B,EAAQT,UAAU,CAAC,EAAE,EAE7D,IAAI,CAACwC,aAAa,EACtB,EAMAlD,EAAQtlB,SAAS,CAACwoB,aAAa,CAAG,WAC9B,IAAI/B,EAAU,IAAI,CAACA,OAAO,CACtB5R,EAAU,IAAI,CAACA,OAAO,CACtB4T,EAAO,GAAMhC,EAAQU,YAAY,CAC7BuB,EAAU,EAClB,IAAI,CAAC5b,cAAc,CAACjM,IAAI,CAAC+jB,GAAsB,IAAI,CAACyD,OAAO,CAAE,QAAS,WAC9DK,EAAU,IACVA,GAAWD,EACX5T,EAAQxQ,KAAK,CAACskB,SAAS,CAAG,CAACD,EAAU,KAE7C,IACA,IAAI,CAAC5b,cAAc,CAACjM,IAAI,CAAC+jB,GAAsB,IAAI,CAAC0D,SAAS,CAAE,QAAS,WAChE7B,EAAQU,YAAY,CAAGuB,GACvB7T,EAAQsS,YAAY,CAAGsB,IACvBC,GAAWD,EACX5T,EAAQxQ,KAAK,CAACskB,SAAS,CAAG,CAACD,EAAU,KAE7C,GACJ,EAKApD,EAAQtlB,SAAS,CAAC0lB,eAAe,CAAG,WAChC,IAOIiB,EACA9R,EARA5T,EAAQ,IAAI,CACZT,EAAQ,IAAI,CAACA,KAAK,CAClBqlB,EAAa,IAAI,CAAC9kB,OAAO,CACzBiJ,EAAYxJ,EAAMwJ,SAAS,CAC3BvJ,EAAaD,EAAMO,OAAO,CAACN,UAAU,CACrCgE,EAAoBhE,MAAAA,EAA+C,KAAK,EAAIA,EAAWgE,iBAAiB,CACxGmkB,EAAO,IAAI,CAIXnC,EAAU,IAAI,CAACA,OAAO,CAAG5B,GAAc,MAAO,CAC1CjgB,UAAW,iCACPihB,EAAWjhB,SAAS,CAAG,IAAMH,CACrC,GACJuF,EAAU6e,WAAW,CAACpC,GACtB,IAAI,CAACqC,WAAW,CAAGjE,GAAc,MAAO,CACpCjgB,UAAW,iDACf,EAAG,KAAK,EAAG6hB,GAEX,IAAI,CAAC3Z,cAAc,CAACjM,IAAI,CAAC+jB,GAAsB,IAAI,CAACkE,WAAW,CAAE,QAAS,WACtE7nB,EAAMH,MAAM,CAAC,CACToa,IAAK,CACDnR,QAAS,CAAC6e,EAAK7e,OAAO,AAC1B,CACJ,EACJ,IAEA,CACI,YACA,YACA,QACA,aACH,CAAC7I,OAAO,CAAC,SAAU6nB,CAAS,EACzBnE,GAAsB6B,EAASsC,EAAW,SAAUjkB,CAAC,EACjD,OAAOA,EAAEiiB,eAAe,EAC5B,EACJ,GACAnC,GAAsB6B,EAAS,YAAa,SAAU3hB,CAAC,EAAI,IAAIC,EAAI,OAAO,AAAyB,OAAxBA,CAAAA,EAAKvE,EAAMwE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGikB,qBAAqB,CAAClkB,EAAI,GAEjK,IAAI,CAAC+P,OAAO,CAAGA,EAAUgQ,GAAc,KAAM,CACzCjgB,UAAW,iCACPihB,EAAW1D,gBAAgB,AACnC,GAEA,IAAI,CAACwE,WAAW,CAAGA,EAAc9B,GAAc,MAAO,CAClDjgB,UAAW,yBACf,GACA6hB,EAAQ8B,YAAY,CAAC5B,EAAaF,EAAQT,UAAU,CAAC,EAAE,EACvDW,EAAY4B,YAAY,CAAC1T,EAAS8R,EAAYX,UAAU,CAAC,EAAE,EAC3D,IAAI,CAACiD,eAAe,GAEpB,IAAI,CAACd,aAAa,EACtB,EAKA7C,EAAQtlB,SAAS,CAAC4lB,kBAAkB,CAAG,WAG/B,IAAI,CAAC7b,OAAO,EACZ,IAAI,CAAC8K,OAAO,CAACsS,YAAY,CAAI,IAAI,CAACV,OAAO,CAACU,YAAY,CAAG,GACzD,IAAI,CAACiB,YAAY,CAAC/jB,KAAK,CAAC4iB,OAAO,CAAG,SAIlC,IAAI,CAACpS,OAAO,CAACxQ,KAAK,CAACskB,SAAS,CAAG,MAE/B,IAAI,CAACP,YAAY,CAAC/jB,KAAK,CAAC4iB,OAAO,CAAG,OAE1C,EAKA3B,EAAQtlB,SAAS,CAACipB,eAAe,CAAG,WAChC,IAAIxC,EAAU,IAAI,CAACA,OAAO,CACtB5R,EAAU,IAAI,CAAC8R,WAAW,CAC1BE,EAAU,IAAI,CAACA,OAAO,CAEtBiC,EAAc,IAAI,CAACA,WAAW,CAC9B/e,EAAU,IAAI,CAACA,OAAO,AAC1B+e,CAAAA,EAAYzkB,KAAK,CAAC6jB,eAAe,CAC7B,OAAS,IAAI,CAAC5M,QAAQ,CAAG,mBACxBvR,GAYD0c,EAAQpiB,KAAK,CAACE,MAAM,CAAG,OACvBsQ,EAAQlK,SAAS,CAACC,MAAM,CAAC,mBACzBke,EAAYne,SAAS,CAACC,MAAM,CAAC,0BAC7Bke,EAAYzkB,KAAK,CAACgS,GAAG,CAAG4O,GAASpQ,EAAS,eAAiB,KAC3DiU,EAAYzkB,KAAK,CAACgjB,IAAI,CAAG,AAACZ,EAAQa,WAAW,CACzCrC,GAASpQ,EAAS,gBAAmB,OAfrCgS,GACAA,CAAAA,EAAQxiB,KAAK,CAAC4iB,OAAO,CAAG,MAAK,EAEjC6B,EAAYzkB,KAAK,CAACgjB,IAAI,CAAG,MACzBtd,EAAU,IAAI,CAACA,OAAO,CAAG,CAAA,EACzB8K,EAAQlK,SAAS,CAACue,GAAG,CAAC,mBACtBJ,EAAYne,SAAS,CAACue,GAAG,CAAC,0BAC1BzC,EAAQpiB,KAAK,CAACE,MAAM,CAAGukB,EAAY3B,YAAY,CAAG,KAU1D,EASA7B,EAAQtlB,SAAS,CAAC0nB,YAAY,CAAG,SAAUnd,CAAM,CAAEvJ,CAAM,EACrD,IAAImlB,EAAgB5b,EAAO6E,UAAU,CACjC+Z,EAAqBhD,EAAcvhB,SAAS,CAE5CwkB,EAAgBjD,EAAc/W,UAAU,CAACA,UAAU,EAEnD+Z,CAAAA,EAAmBze,OAAO,CAAC,2BAA6B,EAAC,IAI7D0e,EAAcxkB,SAAS,CAAG,GACtBukB,GACAC,EAAcze,SAAS,CAACue,GAAG,CAACC,EAAmBE,IAAI,IAGvDD,EACKhf,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAChD/F,KAAK,CAAC6jB,eAAe,CACtB3d,EAAOlG,KAAK,CAAC6jB,eAAe,CAE5BlnB,GACA,IAAI,CAACsoB,uBAAuB,CAACF,GAErC,EAKA9D,EAAQtlB,SAAS,CAACspB,uBAAuB,CAAG,SAAU/e,CAAM,EACxD,IAAII,EAAYJ,EAAOI,SAAS,CAC5BA,EAAU8C,QAAQ,CAAC,qBACnB9C,EAAUC,MAAM,CAAC,qBAGjBD,EAAUue,GAAG,CAAC,oBAEtB,EAKA5D,EAAQtlB,SAAS,CAACupB,kBAAkB,CAAG,SAAUhf,CAAM,EACnD,IAAIif,EAAajf,EAAO6E,UAAU,CACzBhF,gBAAgB,CAAC,sBAC1B,EAAE,CAAClJ,OAAO,CAAChB,IAAI,CAACspB,EAAY,SAAUC,CAAS,EACvCA,IAAclf,GACdkf,EAAU9e,SAAS,CAACC,MAAM,CAAC,oBAEnC,EACJ,EAKA0a,EAAQtlB,SAAS,CAACc,MAAM,CAAG,SAAUC,CAAO,CAAEC,CAAM,EAChD,IAAI,CAACwkB,OAAO,CAAG,CAAC,CAACzkB,EAAQma,GAAG,CAACmH,WAAW,CACxC8C,GAAmB,CAAA,EAAM,IAAI,CAAC3kB,KAAK,CAACO,OAAO,CAACmO,UAAU,CAAEnO,GACxDokB,GAAmB,CAAA,EAAM,IAAI,CAACpkB,OAAO,CAAEA,EAAQma,GAAG,EAClD,IAAI,CAACnR,OAAO,CAAGqb,GAAkB,IAAI,CAACrkB,OAAO,CAACgJ,OAAO,EAAI,IAAI,CAAChJ,OAAO,CAACuL,OAAO,CAAE,CAAA,GAE3E,IAAI,CAAC9L,KAAK,CAAC2I,kBAAkB,EAC7B,IAAI,CAAC3I,KAAK,CAAC2I,kBAAkB,CAACrI,MAAM,GAExC,IAAI,CAACN,KAAK,CAACkpB,UAAU,CAAG,CAAA,EACpBtE,GAAkBpkB,EAAQ,CAAA,IAC1B,IAAI,CAACR,KAAK,CAACQ,MAAM,EAEzB,EAKAskB,EAAQtlB,SAAS,CAACsJ,OAAO,CAAG,WACxB,IAAIqgB,EAAgB,IAAI,CAAClD,OAAO,CAC5B/U,EAASiY,GAAiBA,EAAcva,UAAU,CACtD,IAAI,CAACtC,cAAc,CAAC5L,OAAO,CAAC,SAAU0R,CAAQ,EAAI,OAAOA,GAAY,GAEjElB,GACAA,EAAOkY,WAAW,CAACD,EAE3B,EAKArE,EAAQtlB,SAAS,CAACgB,MAAM,CAAG,WACvB,GAAI,IAAI,CAACD,OAAO,CAACuL,OAAO,GAAK,IAAI,CAAC+I,UAAU,CACxC,IAAI,CAACwU,sBAAsB,OAE1B,CACD,GAAI,CAAC,IAAI,CAACxU,UAAU,CAChB,OAEJ,IAAI,CAACyU,gBAAgB,GACrB,IAAI,CAACC,aAAa,GAClB,IAAI,CAACC,gBAAgB,GACrB,IAAI,CAACpE,kBAAkB,GACvB,IAAI,CAACqD,eAAe,EACxB,CACJ,EAKA3D,EAAQtlB,SAAS,CAAC6pB,sBAAsB,CAAG,WACV,CAAA,IAAzB,IAAI,CAAC9oB,OAAO,CAACuL,OAAO,GACpB,IAAI,CAAChD,OAAO,GACZ,IAAI,CAACS,OAAO,CAAG,CAAA,GAEU,CAAA,IAAzB,IAAI,CAAChJ,OAAO,CAACuL,OAAO,GACpB,IAAI,CAACoZ,eAAe,GACpB,IAAI,CAACC,aAAa,IAEtB,IAAI,CAACtQ,UAAU,CAAG,IAAI,CAACtU,OAAO,CAACuL,OAAO,AAC1C,EAKAgZ,EAAQtlB,SAAS,CAAC8pB,gBAAgB,CAAG,WAC7B,IAAI,CAAC/oB,OAAO,CAAC6D,SAAS,GAAK,IAAI,CAAC6gB,YAAY,GACxC,IAAI,CAACA,YAAY,EACjB,IAAI,CAACgB,OAAO,CAAC9b,SAAS,CAACC,MAAM,CAAC,IAAI,CAAC6a,YAAY,EAE/C,IAAI,CAAC1kB,OAAO,CAAC6D,SAAS,EACtB,IAAI,CAAC6hB,OAAO,CAAC9b,SAAS,CAACue,GAAG,CAAC,IAAI,CAACnoB,OAAO,CAAC6D,SAAS,EAErD,IAAI,CAAC6gB,YAAY,CAAG,IAAI,CAAC1kB,OAAO,CAAC6D,SAAS,EAE1C,IAAI,CAAC7D,OAAO,CAACohB,gBAAgB,GAAK,IAAI,CAACA,gBAAgB,GACnD,IAAI,CAACA,gBAAgB,EACrB,IAAI,CAACtN,OAAO,CAAClK,SAAS,CAACC,MAAM,CAAC,IAAI,CAACuX,gBAAgB,EAEnD,IAAI,CAACphB,OAAO,CAACohB,gBAAgB,EAC7B,IAAI,CAACtN,OAAO,CAAClK,SAAS,CAACue,GAAG,CAAC,IAAI,CAACnoB,OAAO,CAACohB,gBAAgB,EAE5D,IAAI,CAACA,gBAAgB,CAAG,IAAI,CAACphB,OAAO,CAACohB,gBAAgB,CAE7D,EAKAmD,EAAQtlB,SAAS,CAAC+pB,aAAa,CAAG,WAC1B,CAAA,CAAC1E,GAAgC,IAAI,CAACtkB,OAAO,CAACqhB,OAAO,CAAE,IAAI,CAAC6D,UAAU,GACtE,IAAI,CAACT,OAAO,AAAD,IACX,IAAI,CAAC3Q,OAAO,CAACoV,SAAS,CAAG,AAACtF,KAA+FuF,SAAS,CAClI,IAAI,CAACvE,aAAa,GAE1B,EAKAL,EAAQtlB,SAAS,CAACgqB,gBAAgB,CAAG,WAC7BjF,GAAqB,IAAI,CAAChkB,OAAO,CAACgJ,OAAO,GACzC,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAAChJ,OAAO,CAACgJ,OAAO,AAAD,CAE1C,EAIAub,EAAQtlB,SAAS,CAACub,WAAW,CAAG,WAC5B,OAAO,IAAI,CAAC/a,KAAK,CAACO,OAAO,CAACN,UAAU,CAAC6a,QAAQ,EACzC,IAAI,CAACva,OAAO,CAACua,QAAQ,EACrB,qDACR,EACOgK,CACX,GACAA,CAAAA,GAAQtlB,SAAS,CAACgoB,YAAY,CAAG,CAC7BtkB,OAAQ,+BACRC,QAAS,gCACTC,UAAW,kCACXC,MAAO,8BACP0U,QAAS,qBACTC,aAAc,2BACdE,IAAK,iBACLC,SAAU,uBACV3F,KAAM,2BACN6F,kBAAmB,iCACnB3F,aAAc,2BACd4F,eAAgB,6BAChBE,SAAU,sBACVC,SAAU,sBACVC,SAAU,sBACVC,SAAU,sBACV7F,UAAW,uBACXF,UAAW,uBACX0G,mBAAoB,kCACpBN,gBAAiB,8BACjBJ,SAAU,uBACVE,SAAU,uBACVC,UAAW,wBACXI,WAAY,yBACZD,gBAAiB,8BACjBE,cAAe,4BACfC,cAAe,4BACfoB,sBAAuB,qCACvBG,WAAY,wBACZrB,cAAe,4BACfC,eAAgB,6BAChBC,cAAe,4BACfC,cAAe,4BACfC,MAAO,oBACPE,MAAO,oBACPC,OAAQ,qBACRqC,SAAU,8BACVD,SAAU,8BACVG,QAAS,6BACTD,gBAAiB,qCACjBE,sBAAuB,2CACvBC,eAAgB,oCAChBjC,WAAY,yBACZO,kBAAmB,gCACnBO,UAAW,wBACX0G,UAAW,sBACf,EAsBA,IAAI6H,GAA2B,AAAC7pB,IAA+E4H,UAAU,CAIrHkiB,GAAyB,AAAC9pB,IAA+EgI,QAAQ,CAAE+hB,GAAyB,AAAC/pB,IAA+E2kB,QAAQ,CAAEqF,GAAsB,AAAChqB,IAA+E8C,KAAK,CAAEmnB,GAAqB,AAACjqB,IAA+EkB,IAAI,CAUhc,SAASgpB,GAAmBzpB,CAAO,EAC/B,IAAI0pB,EAAe,IAAI,CAAC1pB,OAAO,CAC3BuC,EAAOmnB,EAAannB,IAAI,CACxBuiB,EAAayE,GAAoBG,EAAavb,UAAU,EAAIub,EAAavb,UAAU,CAACgM,GAAG,CACvFna,GAAWA,EAAQma,GAAG,EACtBqK,EAAcjiB,GAAQA,EAAK4L,UAAU,EAAI5L,EAAK4L,UAAU,CAACgM,GAAG,AAChE,CAAA,IAAI,CAAChM,UAAU,CAAG,IApC2BoW,GAoCVO,EAAYN,EAAa,IAAI,EAC5D,IAAI,CAACrW,UAAU,CAACmG,UAAU,EAC1B,CAAA,IAAI,CAACqU,UAAU,CAAG,CAAA,CAAG,CAE7B,CAuBA,SAASgB,KACL,IAAI,CAACC,aAAa,EACtB,CAKA,SAASC,KACD,IAAI,CAAC1b,UAAU,GACf,IAAI,CAACA,UAAU,CAAClO,MAAM,GACtB6pB,AAOR,SAAmBrqB,CAAK,EACpB,IAAIuE,EACJ,GAAI,AAA4B,OAA3BA,CAAAA,EAAKvE,EAAM0O,UAAU,AAAD,GAAenK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsQ,UAAU,CAAE,CAC5E,IAAIyV,EAAetqB,EAAMO,OAAO,CAACP,KAAK,CAClCmmB,EAAcnmB,EAAM0O,UAAU,CAACyX,WAAW,CAC1CW,EAAcX,GAAgB,CAAA,AAACA,EAAYK,UAAU,CACjDqD,GAAuB1D,EAAa,gBACpC0D,GAAuB1D,EAAa,kBAAqBA,EAAYW,WAAW,AAAD,CACvF9mB,CAAAA,EAAM0O,UAAU,CAACyI,KAAK,CAAG2P,EACzB,IAAIyD,EAAQ,CAAA,EACZ,GAAIzD,EAAc9mB,EAAMwqB,SAAS,CAAE,CAC/B,IAAIC,EAAQV,GAAmBO,EAAaI,WAAW,CACnDJ,EAAaK,OAAO,EAAIL,EAAaK,OAAO,CAAC,EAAE,CAAE,GAAK7D,EACtD8D,EAAOH,EAAQzqB,EAAM6qB,UAAU,CAAC1lB,CAAC,AACrCnF,CAAAA,EAAM6qB,UAAU,CAAC1lB,CAAC,CAAGslB,EACrBzqB,EAAM6qB,UAAU,CAAC1T,KAAK,EAAIyT,EAC1BL,EAAQ,CAAA,CACZ,MACyB,IAAhBzD,GACLyD,CAAAA,EAAQ,CAAA,CAAG,EAEXzD,IAAgB9mB,EAAM0O,UAAU,CAACoc,eAAe,GAChD9qB,EAAM0O,UAAU,CAACoc,eAAe,CAAGhE,EAC/ByD,GACAvqB,CAAAA,EAAM+qB,aAAa,CAAG,CAAA,CAAG,EAGrC,CACJ,EAnCkB,IAAI,EAEtB,CAqCA,SAASC,KACD,IAAI,CAACtc,UAAU,EACf,IAAI,CAACA,UAAU,CAAC5F,OAAO,EAE/B,CAIA,SAASmiB,KAEL,IADI1mB,EACAuiB,EAAc,AAAC,CAAA,AAA2B,OAA1BviB,CAAAA,EAAK,IAAI,CAACmK,UAAU,AAAD,GAAenK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGgF,OAAO,AAAD,GAAM,IAAI,CAACmF,UAAU,CAACmG,UAAU,CAChH,IAAI,CAACnG,UAAU,CAACyI,KAAK,CAAG,EAC5B2P,GAAeA,EAAc,IAAI,CAAC0D,SAAS,GAC3C,IAAI,CAAChd,QAAQ,EAAIsZ,EACjB,IAAI,CAAC6D,OAAO,CAAC,EAAE,EAAI7D,EAE3B,CAKA,SAASoE,KAGL,IAFI3mB,EACA4mB,EACAzc,EAAa,IAAI,CAACA,UAAU,CAC5B3E,EAAS2E,GACLA,EAAW2F,OAAO,EAClB3F,EAAW2F,OAAO,CAAC+W,aAAa,CAAC,uCAErC1c,GACA,IAAI,CAAC/F,kBAAkB,EACvB,IAAI,CAACpI,OAAO,CAAC+I,MAAM,EACnBS,IACI,CAAA,AAAgH,OAA/GohB,CAAAA,EAAK,AAAyC,OAAxC5mB,CAAAA,EAAK,IAAI,CAACoE,kBAAkB,CAAC0iB,KAAK,AAAD,GAAe9mB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG2Q,uBAAuB,AAAD,GAAeiW,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGzrB,IAAI,CAAC6E,EAAI,IAAI,CAAC+E,MAAM,CAAA,EACxKS,EAAOoR,UAAU,CAACtX,KAAK,CAAC,mBAAmB,CACvC,QAAU6K,EAAWqM,WAAW,GAAK,2BAGzChR,EAAOoR,UAAU,CAACtX,KAAK,CAAC,mBAAmB,CACvC,QAAU6K,EAAWqM,WAAW,GAAK,2BAGrD,CAIA,SAASuQ,GAAiDve,CAAK,EAC3D,IACI2N,EAAM,IAAI,CAAC1a,KAAK,CAAC0O,UAAU,CAC/B,GAAIgM,GAAOA,EAAI7F,UAAU,CAAE,CACvB,IAAI9K,EAASgD,EAAMhD,MAAM,CAErBA,EAAO6E,UAAU,CAACxK,SAAS,CAAC8F,OAAO,CAL3B,+BAK0C,GAClDH,CAAAA,EAASA,EAAO6E,UAAU,CAACA,UAAU,AAAD,EAExC7E,EAAOI,SAAS,CAACC,MAAM,CAAC,oBAC5B,CACJ,CAKA,SAASmhB,GAAiCxe,CAAK,EAC3C,IACI2N,EAAM,IAAI,CAAC1a,KAAK,CAAC0O,UAAU,CAC/B,GAAIgM,GAAOA,EAAI7F,UAAU,CAAE,CACvB,IAAI9K,EAASgD,EAAMhD,MAAM,CAEzB2Q,EAAIqO,kBAAkB,CAAChc,EAAMhD,MAAM,EAE/BA,EAAO6E,UAAU,CAACxK,SAAS,CAAC8F,OAAO,CAP3B,+BAO0C,GAClDH,CAAAA,EAASA,EAAO6E,UAAU,CAACA,UAAU,AAAD,EAGxC8L,EAAIoO,uBAAuB,CAAC/e,EAChC,CACJ,CAmBA,IAAIyhB,GAAK1rB,GACT0rB,CAAAA,GAAExiB,kBAAkB,CAAGwiB,GAAExiB,kBAAkB,EA/2IwBA,EAg3InEwiB,GAAE1G,OAAO,CAjNwCA,GAkNjD2G,AA33BiB,CAAA,CACb1rB,QAjOJ,SAAiB2rB,CAAuB,EAEpC,IADInnB,EACAonB,EAAkBD,EAAwBlsB,SAAS,CACjD,CAAA,AAAiC,OAAhC+E,CAAAA,EAAKonB,EAAgBN,KAAK,AAAD,GAAe9mB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG+Q,gBAAgB,AAAD,IAEtFqW,EAAgB5H,iBAAiB,CAAGtB,GACpCkJ,EAAgB3H,gBAAgB,CAAGb,GACnCwI,EAAgBC,yBAAyB,CACrCnI,GACJkI,EAAgB7V,WAAW,CAAGgO,GAC9B6H,EAAgBN,KAAK,CAAGM,EAAgBN,KAAK,EAAI,CAAC,EAClDM,EAAgBN,KAAK,CAAC5X,kBAAkB,CAAGU,GAA+BV,kBAAkB,CAC5FkY,EAAgBN,KAAK,CAAC3X,oBAAoB,CAAGS,GAA+BT,oBAAoB,CAChGiY,EAAgBN,KAAK,CAACtW,eAAe,GACrC4W,EAAgBN,KAAK,CAACnW,uBAAuB,CAAGkN,GAChDuJ,EAAgBN,KAAK,CAAC/V,gBAAgB,CAAGnB,GAA+BmB,gBAAgB,CACxF4M,GAjD0DtG,IAkD1DsG,GAAsB,CAClBjiB,WAAY,CACRiE,SAnuCkD4T,EAouCtD,CACJ,GAER,CA2MA,CAAA,EAy3BsB/X,OAAO,CAACyrB,GAAExiB,kBAAkB,EAClD6iB,AAjBoB,CAAA,CAChB9rB,QAvJJ,SAA+B0M,CAAU,CAAEif,CAAuB,EAC9D,IAAII,EAAarf,EAAWjN,SAAS,AAChCssB,CAAAA,EAAW3B,aAAa,GACzBP,GAAuBnd,EAAY,oBAAqByd,IACxDN,GAAuBnd,EAAY,eAAgB2d,IACnDR,GAAuBnd,EAAY,eAAgB2d,IACnDR,GAAuBnd,EAAY,UAAWue,IAC9CpB,GAAuBnd,EAAY,aAAcwe,GAAmB,CAAEc,MAAO,CAAE,GAC/EnC,GAAuBnd,EAAY,SAAUye,IAC7CY,EAAW3B,aAAa,CAAGH,GAC3BJ,GAAuB8B,EAAyB,iBAAkBJ,IAClE1B,GAAuB8B,EAAyB,eAAgBH,IAChE5B,GAl+B0D/N,IAo+BlE,CA0IA,CAAA,EAeyB7b,OAAO,CAACyrB,GAAEQ,KAAK,CAAER,GAAExiB,kBAAkB,EACjC,IAAIpJ,GAAoBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,GAET"}