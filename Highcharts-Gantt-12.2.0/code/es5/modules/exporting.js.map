{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/exporting\n * @requires highcharts\n *\n * Exporting module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"AST\"], require(\"highcharts\")[\"Chart\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/exporting\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"AST\"], [\"highcharts/highcharts\",\"Chart\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/exporting\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"AST\"], require(\"highcharts\")[\"Chart\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"AST\"], root[\"Highcharts\"][\"Chart\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__660__, __WEBPACK_EXTERNAL_MODULE__960__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 660:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ exporting_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\n;// ./code/es5/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructor\n             *\n             * */\n            function Additions(chart) {\n                this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        Additions.prototype.addUpdate = function (updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        };\n        /**\n         * @private\n         */\n        Additions.prototype.update = function (options, redraw) {\n            var _this = this;\n            this.updates.forEach(function (updateFn) {\n                updateFn.call(_this.chart, options, redraw);\n            });\n        };\n        return Additions;\n    }());\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es5/es-modules/Extensions/Exporting/ExportingDefaults.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar isTouchDevice = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice;\n/* *\n *\n *  API Options\n *\n * */\n// Add the export related options\n/**\n * Options for the exporting module. For an overview on the matter, see\n * [the docs](https://www.highcharts.com/docs/export-module/export-module-overview) and\n * read our [Fair Usage Policy](https://www.highcharts.com/docs/export-module/privacy-disclaimer-export).\n *\n * @requires     modules/exporting\n * @optionparent exporting\n */\nvar exporting = {\n    /**\n     * Experimental setting to allow HTML inside the chart (added through\n     * the `useHTML` options), directly in the exported image. This allows\n     * you to preserve complicated HTML structures like tables or bi-directional\n     * text in exported charts.\n     *\n     * Disclaimer: The HTML is rendered in a `foreignObject` tag in the\n     * generated SVG. The official export server is based on PhantomJS,\n     * which supports this, but other SVG clients, like Batik, does not\n     * support it. This also applies to downloaded SVG that you want to\n     * open in a desktop client.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.8\n     * @apioption exporting.allowHTML\n     */\n    /**\n     * Allows the end user to sort the data table by clicking on column headers.\n     *\n     * @since 10.3.3\n     * @apioption exporting.allowTableSorting\n     */\n    allowTableSorting: true,\n    /**\n     * Allow exporting a chart retaining any user-applied CSS.\n     *\n     * Note that this is is default behavior in [styledMode](#chart.styledMode).\n     *\n     * @see [styledMode](#chart.styledMode)\n     *\n     * @sample {highcharts} highcharts/exporting/apply-stylesheets/\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since 12.0.0\n     * @apioption exporting.applyStyleSheets\n     */\n    /**\n     * Additional chart options to be merged into the chart before exporting to\n     * an image format. This does not apply to printing the chart via the export\n     * menu.\n     *\n     * For example, a common use case is to add data labels to improve\n     * readability of the exported chart, or to add a printer-friendly color\n     * scheme to exported PDFs.\n     *\n     * @sample {highcharts} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     * @sample {highstock} highcharts/exporting/chartoptions-data-labels/\n     *         Added data labels\n     *\n     * @type      {Highcharts.Options}\n     * @apioption exporting.chartOptions\n     */\n    /**\n     * Whether to enable the exporting module. Disabling the module will\n     * hide the context button, but API methods will still be available.\n     *\n     * @sample {highcharts} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     * @sample {highstock} highcharts/exporting/enabled-false/\n     *         Exporting module is loaded but disabled\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     2.0\n     * @apioption exporting.enabled\n     */\n    /**\n     * Function to call if the offline-exporting module fails to export\n     * a chart on the client side, and [fallbackToExportServer](\n     * #exporting.fallbackToExportServer) is disabled. If left undefined, an\n     * exception is thrown instead. Receives two parameters, the exporting\n     * options, and the error from the module.\n     *\n     * @see [fallbackToExportServer](#exporting.fallbackToExportServer)\n     *\n     * @type      {Highcharts.ExportingErrorCallbackFunction}\n     * @since     5.0.0\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.error\n     */\n    /**\n     * Whether or not to fall back to the export server if the offline-exporting\n     * module is unable to export the chart on the client side. This happens for\n     * certain browsers, and certain features (e.g.\n     * [allowHTML](#exporting.allowHTML)), depending on the image type exporting\n     * to. For very complex charts, it is possible that export can fail in\n     * browsers that don't support Blob objects, due to data URL length limits.\n     * It is recommended to define the [exporting.error](#exporting.error)\n     * handler if disabling fallback, in order to notify users in case export\n     * fails.\n     *\n     * @type      {boolean}\n     * @default   true\n     * @since     4.1.8\n     * @requires  modules/exporting\n     * @requires  modules/offline-exporting\n     * @apioption exporting.fallbackToExportServer\n     */\n    /**\n     * The filename, without extension, to use for the exported chart.\n     *\n     * @sample {highcharts} highcharts/exporting/filename/\n     *         Custom file name\n     * @sample {highstock} highcharts/exporting/filename/\n     *         Custom file name\n     *\n     * @type      {string}\n     * @default   chart\n     * @since     2.0\n     * @apioption exporting.filename\n     */\n    /**\n     * Highcharts v11.2.0 and older. An object containing additional key value\n     * data for the POST form that sends the SVG to the export server. For\n     * example, a `target` can be set to make sure the generated image is\n     * received in another frame, or a custom `enctype` or `encoding` can be\n     * set.\n     *\n     * With Highcharts v11.3.0, the `fetch` API replaced the old HTML form. To\n     * modify the request, now use [fetchOptions](#exporting.fetchOptions)\n     * instead.\n     *\n     * @deprecated\n     * @type      {Highcharts.HTMLAttributes}\n     * @since     3.0.8\n     * @apioption exporting.formAttributes\n     */\n    /**\n     * Options for the fetch request used when sending the SVG to the export\n     * server.\n     *\n     * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/fetch)\n     * for more information\n     *\n     * @type {Object}\n     * @since 11.3.0\n     * @apioption exporting.fetchOptions\n     */\n    /**\n     * Path where Highcharts will look for export module dependencies to\n     * load on demand if they don't already exist on `window`. Should currently\n     * point to location of [CanVG](https://github.com/canvg/canvg) library,\n     * [jsPDF](https://github.com/parallax/jsPDF) and\n     * [svg2pdf.js](https://github.com/yWorks/svg2pdf.js), required for client\n     * side export in certain browsers.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/{version}/lib\n     * @since     5.0.0\n     * @apioption exporting.libURL\n     */\n    /**\n     * Analogous to [sourceWidth](#exporting.sourceWidth).\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceHeight\n     */\n    /**\n     * The width of the original chart when exported, unless an explicit\n     * [chart.width](#chart.width) is set, or a pixel width is set on the\n     * container. The width exported raster image is then multiplied by\n     * [scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highstock} highcharts/exporting/sourcewidth/\n     *         Source size demo\n     * @sample {highmaps} maps/exporting/sourcewidth/\n     *         Source size demo\n     *\n     * @type      {number}\n     * @since     3.0\n     * @apioption exporting.sourceWidth\n     */\n    /**\n     * The pixel width of charts exported to PNG or JPG. As of Highcharts\n     * 3.0, the default pixel width is a function of the [chart.width](\n     * #chart.width) or [exporting.sourceWidth](#exporting.sourceWidth) and the\n     * [exporting.scale](#exporting.scale).\n     *\n     * @sample {highcharts} highcharts/exporting/width/\n     *         Export to 200px wide images\n     * @sample {highstock} highcharts/exporting/width/\n     *         Export to 200px wide images\n     *\n     * @type      {number}\n     * @since     2.0\n     * @apioption exporting.width\n     */\n    /**\n     * Default MIME type for exporting if `chart.exportChart()` is called\n     * without specifying a `type` option. Possible values are `image/png`,\n     *  `image/jpeg`, `application/pdf` and `image/svg+xml`.\n     *\n     * @type  {Highcharts.ExportingMimeTypeValue}\n     * @since 2.0\n     */\n    type: 'image/png',\n    /**\n     * The URL for the server module converting the SVG string to an image\n     * format. By default this points to Highchart's free web service.\n     *\n     * @since 2.0\n     */\n    url: \"https://export-svg.highcharts.com?v=\".concat((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).version),\n    /**\n     * Settings for a custom font for the exported PDF, when using the\n     * `offline-exporting` module. This is used for languages containing\n     * non-ASCII characters, like Chinese, Russian, Japanese etc.\n     *\n     * As described in the [jsPDF\n     * docs](https://github.com/parallax/jsPDF#use-of-unicode-characters--utf-8),\n     * the 14 standard fonts in PDF are limited to the ASCII-codepage.\n     * Therefore, in order to support other text in the exported PDF, one or\n     * more TTF font files have to be passed on to the exporting module.\n     *\n     * See more in [the\n     * docs](https://www.highcharts.com/docs/export-module/client-side-export).\n     *\n     * @sample {highcharts} highcharts/exporting/offline-download-pdffont/\n     *         Download PDF in a language containing non-Latin characters.\n     *\n     * @since 10.0.0\n     * @requires modules/offline-exporting\n     */\n    pdfFont: {\n        /**\n         * The TTF font file for normal `font-style`. If font variations like\n         * `bold` or `italic` are not defined, the `normal` font will be used\n         * for those too.\n         *\n         * @type string|undefined\n         */\n        normal: void 0,\n        /**\n         * The TTF font file for bold text.\n         *\n         * @type string|undefined\n         */\n        bold: void 0,\n        /**\n         * The TTF font file for bold and italic text.\n         *\n         * @type string|undefined\n         */\n        bolditalic: void 0,\n        /**\n         * The TTF font file for italic text.\n         *\n         * @type string|undefined\n         */\n        italic: void 0\n    },\n    /**\n     * When printing the chart from the menu item in the burger menu, if\n     * the on-screen chart exceeds this width, it is resized. After printing\n     * or cancelled, it is restored. The default width makes the chart\n     * fit into typical paper format. Note that this does not affect the\n     * chart when printing the web page as a whole.\n     *\n     * @since 4.2.5\n     */\n    printMaxWidth: 780,\n    /**\n     * Defines the scale or zoom factor for the exported image compared\n     * to the on-screen display. While for instance a 600px wide chart\n     * may look good on a website, it will look bad in print. The default\n     * scale of 2 makes this chart export to a 1200px PNG or JPG.\n     *\n     * @see [chart.width](#chart.width)\n     * @see [exporting.sourceWidth](#exporting.sourceWidth)\n     *\n     * @sample {highcharts} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highstock} highcharts/exporting/scale/\n     *         Scale demonstrated\n     * @sample {highmaps} maps/exporting/scale/\n     *         Scale demonstrated\n     *\n     * @since 3.0\n     */\n    scale: 2,\n    /**\n     * Options for the export related buttons, print and export. In addition\n     * to the default buttons listed here, custom buttons can be added.\n     * See [navigation.buttonOptions](#navigation.buttonOptions) for general\n     * options.\n     *\n     * @type     {Highcharts.Dictionary<*>}\n     * @requires modules/exporting\n     */\n    buttons: {\n        /**\n         * Options for the export button.\n         *\n         * In styled mode, export button styles can be applied with the\n         * `.highcharts-contextbutton` class.\n         *\n         * @declare  Highcharts.ExportingButtonsOptionsObject\n         * @extends  navigation.buttonOptions\n         * @requires modules/exporting\n         */\n        contextButton: {\n            /**\n             * A click handler callback to use on the button directly instead of\n             * the popup menu.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-onclick/\n             *         Skip the menu and export the chart directly\n             *\n             * @type      {Function}\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.onclick\n             */\n            /**\n             * See [navigation.buttonOptions.symbolFill](\n             * #navigation.buttonOptions.symbolFill).\n             *\n             * @type      {Highcharts.ColorString}\n             * @default   #666666\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.symbolFill\n             */\n            /**\n             * The horizontal position of the button relative to the `align`\n             * option.\n             *\n             * @type      {number}\n             * @default   -10\n             * @since     2.0\n             * @apioption exporting.buttons.contextButton.x\n             */\n            /**\n             * The class name of the context button.\n             */\n            className: 'highcharts-contextbutton',\n            /**\n             * The class name of the menu appearing from the button.\n             */\n            menuClassName: 'highcharts-contextmenu',\n            /**\n             * The symbol for the button. Points to a definition function in\n             * the `Highcharts.Renderer.symbols` collection. The default\n             * `menu` function is part of the exporting module. Possible\n             * values are \"circle\", \"square\", \"diamond\", \"triangle\",\n             * \"triangle-down\", \"menu\", \"menuball\" or custom shape.\n             *\n             * @sample highcharts/exporting/buttons-contextbutton-symbol/\n             *         Use a circle for symbol\n             * @sample highcharts/exporting/buttons-contextbutton-symbol-custom/\n             *         Custom shape as symbol\n             *\n             * @type  {Highcharts.SymbolKeyValue|\"menu\"|\"menuball\"|string}\n             * @since 2.0\n             */\n            symbol: 'menu',\n            /**\n             * The key to a [lang](#lang) option setting that is used for the\n             * button's title tooltip. When the key is `contextButtonTitle`, it\n             * refers to [lang.contextButtonTitle](#lang.contextButtonTitle)\n             * that defaults to \"Chart context menu\".\n             *\n             * @since 6.1.4\n             */\n            titleKey: 'contextButtonTitle',\n            /**\n             * A collection of strings pointing to config options for the menu\n             * items. The config options are defined in the\n             * `menuItemDefinitions` option.\n             *\n             * By default, there is the \"View in full screen\" and \"Print\" menu\n             * items, plus one menu item for each of the available export types.\n             *\n             * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n             *         Menu item definitions\n             *\n             * @type    {Array<string>}\n             * @default [\"viewFullscreen\", \"printChart\", \"separator\", \"downloadPNG\", \"downloadJPEG\", \"downloadSVG\"]\n             * @since   2.0\n             */\n            menuItems: [\n                'viewFullscreen',\n                'printChart',\n                'separator',\n                'downloadPNG',\n                'downloadJPEG',\n                'downloadSVG'\n            ]\n        }\n    },\n    /**\n     * An object consisting of definitions for the menu items in the context\n     * menu. Each key value pair has a `key` that is referenced in the\n     * [menuItems](#exporting.buttons.contextButton.menuItems) setting,\n     * and a `value`, which is an object with the following properties:\n     *\n     * - **onclick:** The click handler for the menu item\n     *\n     * - **text:** The text for the menu item\n     *\n     * - **textKey:** If internationalization is required, the key to a language\n     *   string\n     *\n     * Custom text for the \"exitFullScreen\" can be set only in lang options\n     * (it is not a separate button).\n     *\n     * @sample {highcharts} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highstock} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     * @sample {highmaps} highcharts/exporting/menuitemdefinitions/\n     *         Menu item definitions\n     *\n     *\n     * @type    {Highcharts.Dictionary<Highcharts.ExportingMenuObject>}\n     * @default {\"viewFullscreen\": {}, \"printChart\": {}, \"separator\": {}, \"downloadPNG\": {}, \"downloadJPEG\": {}, \"downloadPDF\": {}, \"downloadSVG\": {}}\n     * @since   5.0.13\n     */\n    menuItemDefinitions: {\n        /**\n         * @ignore\n         */\n        viewFullscreen: {\n            textKey: 'viewFullscreen',\n            onclick: function () {\n                if (this.fullscreen) {\n                    this.fullscreen.toggle();\n                }\n            }\n        },\n        /**\n         * @ignore\n         */\n        printChart: {\n            textKey: 'printChart',\n            onclick: function () {\n                this.print();\n            }\n        },\n        /**\n         * @ignore\n         */\n        separator: {\n            separator: true\n        },\n        /**\n         * @ignore\n         */\n        downloadPNG: {\n            textKey: 'downloadPNG',\n            onclick: function () {\n                this.exportChart();\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadJPEG: {\n            textKey: 'downloadJPEG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/jpeg'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadPDF: {\n            textKey: 'downloadPDF',\n            onclick: function () {\n                this.exportChart({\n                    type: 'application/pdf'\n                });\n            }\n        },\n        /**\n         * @ignore\n         */\n        downloadSVG: {\n            textKey: 'downloadSVG',\n            onclick: function () {\n                this.exportChart({\n                    type: 'image/svg+xml'\n                });\n            }\n        }\n    }\n};\n// Add language\n/**\n * @optionparent lang\n */\nvar lang = {\n    /**\n     * Exporting module only. The text for the menu item to view the chart\n     * in full screen.\n     *\n     * @since 8.0.1\n     */\n    viewFullscreen: 'View in full screen',\n    /**\n     * Exporting module only. The text for the menu item to exit the chart\n     * from full screen.\n     *\n     * @since 8.0.1\n     */\n    exitFullscreen: 'Exit from full screen',\n    /**\n     * Exporting module only. The text for the menu item to print the chart.\n     *\n     * @since    3.0.1\n     * @requires modules/exporting\n     */\n    printChart: 'Print chart',\n    /**\n     * Exporting module only. The text for the PNG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPNG: 'Download PNG image',\n    /**\n     * Exporting module only. The text for the JPEG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadJPEG: 'Download JPEG image',\n    /**\n     * Exporting module only. The text for the PDF download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadPDF: 'Download PDF document',\n    /**\n     * Exporting module only. The text for the SVG download menu item.\n     *\n     * @since    2.0\n     * @requires modules/exporting\n     */\n    downloadSVG: 'Download SVG vector image',\n    /**\n     * Exporting module menu. The tooltip title for the context menu holding\n     * print and export menu items.\n     *\n     * @since    3.0\n     * @requires modules/exporting\n     */\n    contextButtonTitle: 'Chart context menu'\n};\n/**\n * A collection of options for buttons and menus appearing in the exporting\n * module or in Stock Tools.\n *\n * @requires     modules/exporting\n * @optionparent navigation\n */\nvar navigation = {\n    /**\n     * A collection of options for buttons appearing in the exporting\n     * module.\n     *\n     * In styled mode, the buttons are styled with the\n     * `.highcharts-contextbutton` and `.highcharts-button-symbol` classes.\n     *\n     * @requires modules/exporting\n     */\n    buttonOptions: {\n        /**\n         * Whether to enable buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-enabled/\n         *         Exporting module loaded but buttons disabled\n         *\n         * @type      {boolean}\n         * @default   true\n         * @since     2.0\n         * @apioption navigation.buttonOptions.enabled\n         */\n        /**\n         * The pixel size of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolSize: 14,\n        /**\n         * The x position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolX: 14.5,\n        /**\n         * The y position of the center of the symbol inside the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolY: 13.5,\n        /**\n         * Alignment for the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-align/\n         *         Center aligned\n         *\n         * @type  {Highcharts.AlignValue}\n         * @since 2.0\n         */\n        align: 'right',\n        /**\n         * The pixel spacing between buttons, and between the context button and\n         * the title.\n         *\n         * @sample highcharts/title/widthadjust\n         *         Adjust the spacing when using text button\n         * @since 2.0\n         */\n        buttonSpacing: 5,\n        /**\n         * Pixel height of the buttons.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        height: 28,\n        /**\n         * A text string to add to the individual button.\n         *\n         * @sample highcharts/exporting/buttons-text/\n         *         Full text button\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         * @sample highcharts/exporting/buttons-text-symbol/\n         *         Combined symbol and text\n         *\n         * @type      {string}\n         * @default   null\n         * @since     3.0\n         * @apioption navigation.buttonOptions.text\n         */\n        /**\n         * Whether to use HTML for rendering the button. HTML allows for things\n         * like inline CSS or image-based icons.\n         *\n         * @sample highcharts/exporting/buttons-text-usehtml/\n         *         Icon using CSS font in text\n         *\n         * @type      boolean\n         * @default   false\n         * @since 10.3.0\n         * @apioption navigation.buttonOptions.useHTML\n         */\n        /**\n         * The vertical offset of the button's position relative to its\n         * `verticalAlign`. By default adjusted for the chart title alignment.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @since     2.0\n         * @apioption navigation.buttonOptions.y\n         */\n        y: -5,\n        /**\n         * The vertical alignment of the buttons. Can be one of `\"top\"`,\n         * `\"middle\"` or `\"bottom\"`.\n         *\n         * @sample highcharts/navigation/buttonoptions-verticalalign/\n         *         Buttons at lower right\n         *\n         * @type  {Highcharts.VerticalAlignValue}\n         * @since 2.0\n         */\n        verticalAlign: 'top',\n        /**\n         * The pixel width of the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        width: 28,\n        /**\n         * Fill color for the symbol within the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolfill/\n         *         Blue symbol stroke for one of the buttons\n         *\n         * @type  {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @since 2.0\n         */\n        symbolFill: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The color of the symbol's stroke or line.\n         *\n         * @sample highcharts/navigation/buttonoptions-symbolstroke/\n         *         Blue symbol stroke\n         *\n         * @type  {Highcharts.ColorString}\n         * @since 2.0\n         */\n        symbolStroke: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The pixel stroke width of the symbol on the button.\n         *\n         * @sample highcharts/navigation/buttonoptions-height/\n         *         Bigger buttons\n         *\n         * @since 2.0\n         */\n        symbolStrokeWidth: 3,\n        /**\n         * A configuration object for the button theme. The object accepts\n         * SVG properties like `stroke-width`, `stroke` and `fill`.\n         * Tri-state button styles are supported by the `states.hover` and\n         * `states.select` objects.\n         *\n         * @sample highcharts/navigation/buttonoptions-theme/\n         *         Theming the buttons\n         *\n         * @requires modules/exporting\n         *\n         * @since 3.0\n         */\n        theme: {\n            /**\n             * The default fill exists only to capture hover events.\n             *\n             * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n             */\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            /**\n             * Padding for the button.\n             */\n            padding: 5,\n            /**\n             * Default stroke for the buttons.\n             *\n             * @type      {Highcharts.ColorString}\n             */\n            stroke: 'none',\n            /**\n             * Default stroke linecap for the buttons.\n             */\n            'stroke-linecap': 'round'\n        }\n    },\n    /**\n     * CSS styles for the popup menu appearing by default when the export\n     * icon is clicked. This menu is rendered in HTML.\n     *\n     * @see In styled mode, the menu is styled with the `.highcharts-menu`\n     *      class.\n     *\n     * @sample highcharts/navigation/menustyle/\n     *         Light gray menu background\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#ffffff\", \"borderRadius\": \"3px\", \"padding\": \"0.5em\"}\n     * @since   2.0\n     */\n    menuStyle: {\n        /** @ignore-option */\n        border: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        background: \"#ffffff\" /* Palette.backgroundColor */,\n        /** @ignore-option */\n        padding: '0.5em'\n    },\n    /**\n     * CSS styles for the individual items within the popup menu appearing\n     * by default when the export icon is clicked. The menu items are\n     * rendered in HTML. Font size defaults to `11px` on desktop and `14px`\n     * on touch devices.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample {highcharts} highcharts/navigation/menuitemstyle/\n     *         Add a grey stripe to the left\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"padding\": \"0.5em\", \"color\": \"#333333\", \"background\": \"none\", \"borderRadius\": \"3px\", \"fontSize\": \"0.8em\", \"transition\": \"background 250ms, color 250ms\"}\n     * @since   2.0\n     */\n    menuItemStyle: {\n        /** @ignore-option */\n        background: 'none',\n        /** @ignore-option */\n        borderRadius: '3px',\n        /** @ignore-option */\n        color: \"#333333\" /* Palette.neutralColor80 */,\n        /** @ignore-option */\n        padding: '0.5em',\n        /** @ignore-option */\n        fontSize: isTouchDevice ? '0.9em' : '0.8em',\n        /** @ignore-option */\n        transition: 'background 250ms, color 250ms'\n    },\n    /**\n     * CSS styles for the hover state of the individual items within the\n     * popup menu appearing by default when the export icon is clicked. The\n     * menu items are rendered in HTML.\n     *\n     * @see In styled mode, the menu items are styled with the\n     *      `.highcharts-menu-item` class.\n     *\n     * @sample highcharts/navigation/menuitemhoverstyle/\n     *         Bold text on hover\n     *\n     * @type    {Highcharts.CSSObject}\n     * @default {\"background\": \"#f2f2f2\" }\n     * @since   2.0\n     */\n    menuItemHoverStyle: {\n        /** @ignore-option */\n        background: \"#f2f2f2\" /* Palette.neutralColor5 */\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar ExportingDefaults = {\n    exporting: exporting,\n    lang: lang,\n    navigation: navigation\n};\n/* harmony default export */ var Exporting_ExportingDefaults = (ExportingDefaults);\n\n;// ./code/es5/es-modules/Extensions/Exporting/ExportingSymbols.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ExportingSymbols;\n(function (ExportingSymbols) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    var modifiedClasses = [];\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(SVGRendererClass) {\n        if (modifiedClasses.indexOf(SVGRendererClass) === -1) {\n            modifiedClasses.push(SVGRendererClass);\n            var symbols = SVGRendererClass.prototype.symbols;\n            symbols.menu = menu;\n            symbols.menuball = menuball.bind(symbols);\n        }\n    }\n    ExportingSymbols.compose = compose;\n    /**\n     * @private\n     */\n    function menu(x, y, width, height) {\n        var arr = [\n                ['M',\n            x,\n            y + 2.5],\n                ['L',\n            x + width,\n            y + 2.5],\n                ['M',\n            x,\n            y + height / 2 + 0.5],\n                ['L',\n            x + width,\n            y + height / 2 + 0.5],\n                ['M',\n            x,\n            y + height - 1.5],\n                ['L',\n            x + width,\n            y + height - 1.5]\n            ];\n        return arr;\n    }\n    /**\n     * @private\n     */\n    function menuball(x, y, width, height) {\n        var h = (height / 3) - 2;\n        var path = [];\n        path = path.concat(this.circle(width - h, y, h, h), this.circle(width - h, y + h + 4, h, h), this.circle(width - h, y + 2 * (h + 4), h, h));\n        return path;\n    }\n})(ExportingSymbols || (ExportingSymbols = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Exporting_ExportingSymbols = (ExportingSymbols);\n\n;// ./code/es5/es-modules/Extensions/Exporting/Fullscreen.js\n/* *\n *\n *  (c) 2009-2025 Rafal Sebestjanski\n *\n *  Full screen for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/**\n * The module allows user to enable display chart in full screen mode.\n * Used in StockTools too.\n * Based on default solutions in browsers.\n */\n\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onChartBeforeRender() {\n    /**\n     * @name Highcharts.Chart#fullscreen\n     * @type {Highcharts.Fullscreen}\n     * @requires modules/full-screen\n     */\n    this.fullscreen = new Fullscreen(this);\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Handles displaying chart's container in the fullscreen mode.\n *\n * **Note**: Fullscreen is not supported on iPhone due to iOS limitations.\n *\n * @class\n * @name Highcharts.Fullscreen\n *\n * @requires modules/exporting\n */\nvar Fullscreen = /** @class */ (function () {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function Fullscreen(chart) {\n        /**\n         * Chart managed by the fullscreen controller.\n         * @name Highcharts.Fullscreen#chart\n         * @type {Highcharts.Chart}\n         */\n        this.chart = chart;\n        /**\n         * The flag is set to `true` when the chart is displayed in\n         * the fullscreen mode.\n         *\n         * @name Highcharts.Fullscreen#isOpen\n         * @type {boolean|undefined}\n         * @since 8.0.1\n         */\n        this.isOpen = false;\n        var container = chart.renderTo;\n        // Hold event and methods available only for a current browser.\n        if (!this.browserProps) {\n            if (typeof container.requestFullscreen === 'function') {\n                this.browserProps = {\n                    fullscreenChange: 'fullscreenchange',\n                    requestFullscreen: 'requestFullscreen',\n                    exitFullscreen: 'exitFullscreen'\n                };\n            }\n            else if (container.mozRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'mozfullscreenchange',\n                    requestFullscreen: 'mozRequestFullScreen',\n                    exitFullscreen: 'mozCancelFullScreen'\n                };\n            }\n            else if (container.webkitRequestFullScreen) {\n                this.browserProps = {\n                    fullscreenChange: 'webkitfullscreenchange',\n                    requestFullscreen: 'webkitRequestFullScreen',\n                    exitFullscreen: 'webkitExitFullscreen'\n                };\n            }\n            else if (container.msRequestFullscreen) {\n                this.browserProps = {\n                    fullscreenChange: 'MSFullscreenChange',\n                    requestFullscreen: 'msRequestFullscreen',\n                    exitFullscreen: 'msExitFullscreen'\n                };\n            }\n        }\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Prepares the chart class to support fullscreen.\n     *\n     * @param {typeof_Highcharts.Chart} ChartClass\n     * The chart class to decorate with fullscreen support.\n     */\n    Fullscreen.compose = function (ChartClass) {\n        if (pushUnique(composed, 'Fullscreen')) {\n            // Initialize fullscreen\n            addEvent(ChartClass, 'beforeRender', onChartBeforeRender);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Stops displaying the chart in fullscreen mode.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function    Highcharts.Fullscreen#close\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    Fullscreen.prototype.close = function () {\n        var fullscreen = this,\n            chart = fullscreen.chart,\n            optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenClose', null, function () {\n            // Don't fire exitFullscreen() when user exited\n            // using 'Escape' button.\n            if (fullscreen.isOpen &&\n                fullscreen.browserProps &&\n                chart.container.ownerDocument instanceof Document) {\n                chart.container.ownerDocument[fullscreen.browserProps.exitFullscreen]();\n            }\n            // Unbind event as it's necessary only before exiting\n            // from fullscreen.\n            if (fullscreen.unbindFullscreenEvent) {\n                fullscreen.unbindFullscreenEvent = fullscreen\n                    .unbindFullscreenEvent();\n            }\n            chart.setSize(fullscreen.origWidth, fullscreen.origHeight, false);\n            fullscreen.origWidth = void 0;\n            fullscreen.origHeight = void 0;\n            optionsChart.width = fullscreen.origWidthOption;\n            optionsChart.height = fullscreen.origHeightOption;\n            fullscreen.origWidthOption = void 0;\n            fullscreen.origHeightOption = void 0;\n            fullscreen.isOpen = false;\n            fullscreen.setButtonText();\n        });\n    };\n    /**\n     * Displays the chart in fullscreen mode.\n     * When fired customly by user before exporting context button is created,\n     * button's text will not be replaced - it's on the user side.\n     * Exporting module required.\n     *\n     * @since       8.0.1\n     *\n     * @function Highcharts.Fullscreen#open\n     * @return      {void}\n     * @requires    modules/full-screen\n     */\n    Fullscreen.prototype.open = function () {\n        var fullscreen = this,\n            chart = fullscreen.chart,\n            optionsChart = chart.options.chart;\n        fireEvent(chart, 'fullscreenOpen', null, function () {\n            if (optionsChart) {\n                fullscreen.origWidthOption = optionsChart.width;\n                fullscreen.origHeightOption = optionsChart.height;\n            }\n            fullscreen.origWidth = chart.chartWidth;\n            fullscreen.origHeight = chart.chartHeight;\n            // Handle exitFullscreen() method when user clicks 'Escape' button.\n            if (fullscreen.browserProps) {\n                var unbindChange_1 = addEvent(chart.container.ownerDocument, // Chart's document\n                    fullscreen.browserProps.fullscreenChange,\n                    function () {\n                        // Handle lack of async of browser's\n                        // fullScreenChange event.\n                        if (fullscreen.isOpen) {\n                            fullscreen.isOpen = false;\n                        fullscreen.close();\n                    }\n                    else {\n                        chart.setSize(null, null, false);\n                        fullscreen.isOpen = true;\n                        fullscreen.setButtonText();\n                    }\n                });\n                var unbindDestroy_1 = addEvent(chart, 'destroy',\n                    unbindChange_1);\n                fullscreen.unbindFullscreenEvent = function () {\n                    unbindChange_1();\n                    unbindDestroy_1();\n                };\n                var promise = chart.renderTo[fullscreen.browserProps.requestFullscreen]();\n                if (promise) {\n                    promise['catch'](function () {\n                        alert(// eslint-disable-line no-alert\n                        'Full screen is not supported inside a frame.');\n                    });\n                }\n            }\n        });\n    };\n    /**\n     * Replaces the exporting context button's text when toogling the\n     * fullscreen mode.\n     *\n     * @private\n     *\n     * @since 8.0.1\n     *\n     * @requires modules/full-screen\n     */\n    Fullscreen.prototype.setButtonText = function () {\n        var chart = this.chart,\n            exportDivElements = chart.exportDivElements,\n            exportingOptions = chart.options.exporting,\n            menuItems = (exportingOptions &&\n                exportingOptions.buttons &&\n                exportingOptions.buttons.contextButton.menuItems),\n            lang = chart.options.lang;\n        if (exportingOptions &&\n            exportingOptions.menuItemDefinitions &&\n            lang &&\n            lang.exitFullscreen &&\n            lang.viewFullscreen &&\n            menuItems &&\n            exportDivElements) {\n            var exportDivElement = exportDivElements[menuItems.indexOf('viewFullscreen')];\n            if (exportDivElement) {\n                highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(exportDivElement, !this.isOpen ?\n                    (exportingOptions.menuItemDefinitions.viewFullscreen\n                        .text ||\n                        lang.viewFullscreen) : lang.exitFullscreen);\n            }\n        }\n    };\n    /**\n     * Toggles displaying the chart in fullscreen mode.\n     * By default, when the exporting module is enabled, a context button with\n     * a drop down menu in the upper right corner accesses this function.\n     * Exporting module required.\n     *\n     * @since 8.0.1\n     *\n     * @sample      highcharts/members/chart-togglefullscreen/\n     *              Toggle fullscreen mode from a HTML button\n     *\n     * @function Highcharts.Fullscreen#toggle\n     * @requires    modules/full-screen\n     */\n    Fullscreen.prototype.toggle = function () {\n        var fullscreen = this;\n        if (!fullscreen.isOpen) {\n            fullscreen.open();\n        }\n        else {\n            fullscreen.close();\n        }\n    };\n    return Fullscreen;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Exporting_Fullscreen = (Fullscreen);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired when closing the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenCloseCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired when opening the fullscreen\n *\n * @callback Highcharts.FullScreenfullscreenOpenCallbackFunction\n *\n * @param {Highcharts.Chart} chart\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n(''); // Keeps doclets above separated from following code\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires when a fullscreen is closed through the context menu item,\n * or a fullscreen is closed on the `Escape` button click,\n * or the `Chart.fullscreen.close` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenCloseCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenClose\n */\n/**\n * Fires when a fullscreen is opened through the context menu item,\n * or the `Chart.fullscreen.open` method.\n *\n * @sample highcharts/chart/events-fullscreen\n *         Title size change on fullscreen open\n *\n * @type      {Highcharts.FullScreenfullscreenOpenCallbackFunction}\n * @since     10.1.0\n * @context   Highcharts.Chart\n * @requires  modules/full-screen\n * @apioption chart.events.fullscreenOpen\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es5/es-modules/Core/HttpUtilities.js\n/* *\n *\n *  (c) 2010-2025 Christer Vasseng, Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\nvar win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\nvar discardElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).discardElement, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Perform an Ajax call.\n *\n * @function Highcharts.ajax\n *\n * @param {Highcharts.AjaxSettingsObject} settings\n *        The Ajax settings to use.\n *\n * @return {false|undefined}\n *         Returns false, if error occurred.\n */\nfunction ajax(settings) {\n    var _a;\n    var headers = {\n            json: 'application/json',\n            xml: 'application/xml',\n            text: 'text/plain',\n            octet: 'application/octet-stream'\n        },\n        r = new XMLHttpRequest();\n    /**\n     * Private error handler.\n     * @private\n     * @param {XMLHttpRequest} xhr\n     * Internal request object.\n     * @param {string|Error} err\n     * Occurred error.\n     */\n    function handleError(xhr, err) {\n        if (settings.error) {\n            settings.error(xhr, err);\n        }\n        else {\n            // @todo Maybe emit a highcharts error event here\n        }\n    }\n    if (!settings.url) {\n        return false;\n    }\n    r.open((settings.type || 'get').toUpperCase(), settings.url, true);\n    if (!((_a = settings.headers) === null || _a === void 0 ? void 0 : _a['Content-Type'])) {\n        r.setRequestHeader('Content-Type', headers[settings.dataType || 'json'] || headers.text);\n    }\n    objectEach(settings.headers, function (val, key) {\n        r.setRequestHeader(key, val);\n    });\n    if (settings.responseType) {\n        r.responseType = settings.responseType;\n    }\n    // @todo lacking timeout handling\n    r.onreadystatechange = function () {\n        var _a;\n        var res;\n        if (r.readyState === 4) {\n            if (r.status === 200) {\n                if (settings.responseType !== 'blob') {\n                    res = r.responseText;\n                    if (settings.dataType === 'json') {\n                        try {\n                            res = JSON.parse(res);\n                        }\n                        catch (e) {\n                            if (e instanceof Error) {\n                                return handleError(r, e);\n                            }\n                        }\n                    }\n                }\n                return (_a = settings.success) === null || _a === void 0 ? void 0 : _a.call(settings, res, r);\n            }\n            handleError(r, r.responseText);\n        }\n    };\n    if (settings.data && typeof settings.data !== 'string') {\n        settings.data = JSON.stringify(settings.data);\n    }\n    r.send(settings.data);\n}\n/**\n * Get a JSON resource over XHR, also supporting CORS without preflight.\n *\n * @function Highcharts.getJSON\n * @param {string} url\n *        The URL to load.\n * @param {Function} success\n *        The success callback. For error handling, use the `Highcharts.ajax`\n *        function instead.\n */\nfunction getJSON(url, success) {\n    HttpUtilities.ajax({\n        url: url,\n        success: success,\n        dataType: 'json',\n        headers: {\n            // Override the Content-Type to avoid preflight problems with CORS\n            // in the Highcharts demos\n            'Content-Type': 'text/plain'\n        }\n    });\n}\n/**\n * The post utility\n *\n * @private\n * @function Highcharts.post\n *\n * @param {string} url\n * Post URL\n *\n * @param {Object} data\n * Post data\n *\n * @param {RequestInit} [fetchOptions]\n * Additional attributes for the post request\n */\n/**\n *\n */\nfunction post(url, data, fetchOptions) {\n    var formData = new win.FormData();\n    // Add the data\n    objectEach(data, function (val, name) {\n        formData.append(name, val);\n    });\n    formData.append('b64', 'true');\n    var filename = data.filename,\n        type = data.type;\n    return win.fetch(url, __assign({ method: 'POST', body: formData }, fetchOptions)).then(function (res) {\n        if (res.ok) {\n            res.text().then(function (text) {\n                var link = document.createElement('a');\n                link.href = \"data:\".concat(type, \";base64,\").concat(text);\n                link.download = filename;\n                link.click();\n                discardElement(link);\n            });\n        }\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar HttpUtilities = {\n    ajax: ajax,\n    getJSON: getJSON,\n    post: post\n};\n/* harmony default export */ var Core_HttpUtilities = (HttpUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @interface Highcharts.AjaxSettingsObject\n */ /**\n* The payload to send.\n*\n* @name Highcharts.AjaxSettingsObject#data\n* @type {string|Highcharts.Dictionary<any>|undefined}\n*/ /**\n* The data type expected.\n* @name Highcharts.AjaxSettingsObject#dataType\n* @type {\"json\"|\"xml\"|\"text\"|\"octet\"|undefined}\n*/ /**\n* Function to call on error.\n* @name Highcharts.AjaxSettingsObject#error\n* @type {Function|undefined}\n*/ /**\n* The headers; keyed on header name.\n* @name Highcharts.AjaxSettingsObject#headers\n* @type {Highcharts.Dictionary<string>|undefined}\n*/ /**\n* Function to call on success.\n* @name Highcharts.AjaxSettingsObject#success\n* @type {Function|undefined}\n*/ /**\n* The HTTP method to use. For example GET or POST.\n* @name Highcharts.AjaxSettingsObject#type\n* @type {string|undefined}\n*/ /**\n* The URL to call.\n* @name Highcharts.AjaxSettingsObject#url\n* @type {string}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Extensions/Exporting/Exporting.js\n/* *\n *\n *  Exporting module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Exporting_assign = (undefined && undefined.__assign) || function () {\n    Exporting_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return Exporting_assign.apply(this, arguments);\n};\n\n\n\n\nvar defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions;\n\n\n\n\nvar doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, SVG_NS = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).SVG_NS, Exporting_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\nvar Exporting_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, css = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).css, createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement, Exporting_discardElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).discardElement, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, Exporting_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, Exporting_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, removeEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).removeEvent, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Composition\n *\n * */\nvar Exporting;\n(function (Exporting) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // These CSS properties are not inlined. Remember camelCase.\n    var inlineDenylist = [\n            /-/, // In Firefox, both hyphened and camelCased names are listed\n            /^(clipPath|cssText|d|height|width)$/, // Full words\n            /^font$/, // More specific props are set\n            /[lL]ogical(Width|Height)$/,\n            /^parentRule$/,\n            /^(cssRules|ownerRules)$/, // #19516 read-only properties\n            /perspective/,\n            /TapHighlightColor/,\n            /^transition/,\n            /^length$/, // #7700\n            /^\\d+$/ // #17538\n        ];\n    // These ones are translated to attributes rather than styles\n    var inlineToAttributes = [\n            'fill',\n            'stroke',\n            'strokeLinecap',\n            'strokeLinejoin',\n            'strokeWidth',\n            'textAnchor',\n            'x',\n            'y'\n        ];\n    Exporting.inlineAllowlist = [];\n    var unstyledElements = [\n            'clipPath',\n            'defs',\n            'desc'\n        ];\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    var printingChart;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add the export button to the chart, with options.\n     *\n     * @private\n     * @function Highcharts.Chart#addButton\n     * @param {Highcharts.NavigationButtonOptions} options\n     * @requires modules/exporting\n     */\n    function addButton(options) {\n        var chart = this,\n            renderer = chart.renderer,\n            btnOptions = merge(chart.options.navigation.buttonOptions,\n            options),\n            onclick = btnOptions.onclick,\n            menuItems = btnOptions.menuItems,\n            symbolSize = btnOptions.symbolSize || 12;\n        var symbol;\n        if (!chart.btnCount) {\n            chart.btnCount = 0;\n        }\n        // Keeps references to the button elements\n        if (!chart.exportDivElements) {\n            chart.exportDivElements = [];\n            chart.exportSVGElements = [];\n        }\n        if (btnOptions.enabled === false || !btnOptions.theme) {\n            return;\n        }\n        var theme = chart.styledMode ? {} : btnOptions.theme;\n        var callback;\n        if (onclick) {\n            callback = function (e) {\n                if (e) {\n                    e.stopPropagation();\n                }\n                onclick.call(chart, e);\n            };\n        }\n        else if (menuItems) {\n            callback = function (e) {\n                // Consistent with onclick call (#3495)\n                if (e) {\n                    e.stopPropagation();\n                }\n                chart.contextMenu(button.menuClassName, menuItems, button.translateX || 0, button.translateY || 0, button.width || 0, button.height || 0, button);\n                button.setState(2);\n            };\n        }\n        if (btnOptions.text && btnOptions.symbol) {\n            theme.paddingLeft = pick(theme.paddingLeft, 30);\n        }\n        else if (!btnOptions.text) {\n            extend(theme, {\n                width: btnOptions.width,\n                height: btnOptions.height,\n                padding: 0\n            });\n        }\n        var button = renderer\n                .button(btnOptions.text, 0, 0,\n            callback,\n            theme,\n            void 0,\n            void 0,\n            void 0,\n            void 0,\n            btnOptions.useHTML)\n                .addClass(options.className)\n                .attr({\n                title: pick(chart.options.lang[btnOptions._titleKey || btnOptions.titleKey], '')\n            });\n        button.menuClassName = (options.menuClassName ||\n            'highcharts-menu-' + chart.btnCount++);\n        if (btnOptions.symbol) {\n            symbol = renderer\n                .symbol(btnOptions.symbol, Math.round((btnOptions.symbolX || 0) - (symbolSize / 2)), Math.round((btnOptions.symbolY || 0) - (symbolSize / 2)), symbolSize, symbolSize\n            // If symbol is an image, scale it (#7957)\n            , {\n                width: symbolSize,\n                height: symbolSize\n            })\n                .addClass('highcharts-button-symbol')\n                .attr({\n                zIndex: 1\n            })\n                .add(button);\n            if (!chart.styledMode) {\n                symbol.attr({\n                    stroke: btnOptions.symbolStroke,\n                    fill: btnOptions.symbolFill,\n                    'stroke-width': btnOptions.symbolStrokeWidth || 1\n                });\n            }\n        }\n        button\n            .add(chart.exportingGroup)\n            .align(extend(btnOptions, {\n            width: button.width,\n            x: pick(btnOptions.x, chart.buttonOffset) // #1654\n        }), true, 'spacingBox');\n        chart.buttonOffset += (((button.width || 0) + btnOptions.buttonSpacing) *\n            (btnOptions.align === 'right' ? -1 : 1));\n        chart.exportSVGElements.push(button, symbol);\n    }\n    /**\n     * Clean up after printing a chart.\n     *\n     * @function Highcharts#afterPrint\n     *\n     * @private\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that was (or suppose to be) printed\n     *\n     * @emits Highcharts.Chart#event:afterPrint\n     */\n    function afterPrint() {\n        var chart = this;\n        if (!chart.printReverseInfo) {\n            return void 0;\n        }\n        var _a = chart.printReverseInfo,\n            childNodes = _a.childNodes,\n            origDisplay = _a.origDisplay,\n            resetParams = _a.resetParams;\n        // Put the chart back in\n        chart.moveContainers(chart.renderTo);\n        // Restore all body content\n        [].forEach.call(childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                node.style.display = (origDisplay[i] || '');\n            }\n        });\n        chart.isPrinting = false;\n        // Reset printMaxWidth\n        if (resetParams) {\n            chart.setSize.apply(chart, resetParams);\n        }\n        delete chart.printReverseInfo;\n        printingChart = void 0;\n        Exporting_fireEvent(chart, 'afterPrint');\n    }\n    /**\n     * Prepare chart and document before printing a chart.\n     *\n     * @function Highcharts#beforePrint\n     *\n     * @private\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     */\n    function beforePrint() {\n        var _a;\n        var chart = this,\n            body = doc.body,\n            printMaxWidth = chart.options.exporting.printMaxWidth,\n            printReverseInfo = {\n                childNodes: body.childNodes,\n                origDisplay: [],\n                resetParams: void 0\n            };\n        chart.isPrinting = true;\n        (_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.reset(void 0, 0);\n        Exporting_fireEvent(chart, 'beforePrint');\n        // Handle printMaxWidth\n        var handleMaxWidth = printMaxWidth &&\n                chart.chartWidth > printMaxWidth;\n        if (handleMaxWidth) {\n            printReverseInfo.resetParams = [\n                chart.options.chart.width,\n                void 0,\n                false\n            ];\n            chart.setSize(printMaxWidth, void 0, false);\n        }\n        // Hide all body content\n        [].forEach.call(printReverseInfo.childNodes, function (node, i) {\n            if (node.nodeType === 1) {\n                printReverseInfo.origDisplay[i] = node.style.display;\n                node.style.display = 'none';\n            }\n        });\n        // Pull out the chart\n        chart.moveContainers(body);\n        // Storage details for undo action after printing\n        chart.printReverseInfo = printReverseInfo;\n    }\n    /**\n     * @private\n     */\n    function chartCallback(chart) {\n        var composition = chart;\n        composition.renderExporting();\n        Exporting_addEvent(chart, 'redraw', composition.renderExporting);\n        // Destroy the export elements at chart destroy\n        Exporting_addEvent(chart, 'destroy', composition.destroyExport);\n        // Uncomment this to see a button directly below the chart, for quick\n        // testing of export\n        /*\n        let button, viewImage, viewSource;\n        if (!chart.renderer.forExport) {\n            viewImage = function () {\n                let div = doc.createElement('div');\n                div.innerHTML = chart.getSVGForExport();\n                chart.renderTo.parentNode.appendChild(div);\n            };\n\n            viewSource = function () {\n                let pre = doc.createElement('pre');\n                pre.innerHTML = chart.getSVGForExport()\n                    .replace(/</g, '\\n&lt;')\n                    .replace(/>/g, '&gt;');\n                chart.renderTo.parentNode.appendChild(pre);\n            };\n\n            viewImage();\n\n            // View SVG Image\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Image';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewImage;\n\n            // View SVG Source\n            button = doc.createElement('button');\n            button.innerHTML = 'View SVG Source';\n            chart.renderTo.parentNode.appendChild(button);\n            button.onclick = viewSource;\n        }\n        //*/\n    }\n    /**\n     * @private\n     */\n    function compose(ChartClass, SVGRendererClass) {\n        Exporting_ExportingSymbols.compose(SVGRendererClass);\n        Exporting_Fullscreen.compose(ChartClass);\n        var chartProto = ChartClass.prototype;\n        if (!chartProto.exportChart) {\n            chartProto.afterPrint = afterPrint;\n            chartProto.exportChart = exportChart;\n            chartProto.inlineStyles = inlineStyles;\n            chartProto.print = print;\n            chartProto.sanitizeSVG = sanitizeSVG;\n            chartProto.getChartHTML = getChartHTML;\n            chartProto.getSVG = getSVG;\n            chartProto.getSVGForExport = getSVGForExport;\n            chartProto.getFilename = getFilename;\n            chartProto.moveContainers = moveContainers;\n            chartProto.beforePrint = beforePrint;\n            chartProto.contextMenu = contextMenu;\n            chartProto.addButton = addButton;\n            chartProto.destroyExport = destroyExport;\n            chartProto.renderExporting = renderExporting;\n            chartProto.resolveCSSVariables = resolveCSSVariables;\n            chartProto.callbacks.push(chartCallback);\n            Exporting_addEvent(ChartClass, 'init', onChartInit);\n            Exporting_addEvent(ChartClass, 'layOutTitle', onChartLayOutTitle);\n            if ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n                Exporting_win.matchMedia('print').addListener(function (mqlEvent) {\n                    if (!printingChart) {\n                        return void 0;\n                    }\n                    if (mqlEvent.matches) {\n                        printingChart.beforePrint();\n                    }\n                    else {\n                        printingChart.afterPrint();\n                    }\n                });\n            }\n            defaultOptions.exporting = merge(Exporting_ExportingDefaults.exporting, defaultOptions.exporting);\n            defaultOptions.lang = merge(Exporting_ExportingDefaults.lang, defaultOptions.lang);\n            // Buttons and menus are collected in a separate config option set\n            // called 'navigation'. This can be extended later to add control\n            // buttons like zoom and pan right click menus.\n            defaultOptions.navigation = merge(Exporting_ExportingDefaults.navigation, defaultOptions.navigation);\n        }\n    }\n    Exporting.compose = compose;\n    /**\n     * Display a popup menu for choosing the export type.\n     *\n     * @private\n     * @function Highcharts.Chart#contextMenu\n     * @param {string} className\n     *        An identifier for the menu.\n     * @param {Array<string|Highcharts.ExportingMenuObject>} items\n     *        A collection with text and onclicks for the items.\n     * @param {number} x\n     *        The x position of the opener button\n     * @param {number} y\n     *        The y position of the opener button\n     * @param {number} width\n     *        The width of the opener button\n     * @param {number} height\n     *        The height of the opener button\n     * @requires modules/exporting\n     */\n    function contextMenu(className, items, x, y, width, height, button) {\n        var _a,\n            _b;\n        var chart = this,\n            navOptions = chart.options.navigation,\n            chartWidth = chart.chartWidth,\n            chartHeight = chart.chartHeight,\n            cacheName = 'cache-' + className, \n            // For mouse leave detection\n            menuPadding = Math.max(width,\n            height);\n        var innerMenu,\n            menu = chart[cacheName];\n        // Create the menu only the first time\n        if (!menu) {\n            // Create a HTML element above the SVG\n            chart.exportContextMenu = chart[cacheName] = menu =\n                createElement('div', {\n                    className: className\n                }, Exporting_assign({ position: 'absolute', zIndex: 1000, padding: menuPadding + 'px', pointerEvents: 'auto' }, chart.renderer.style), ((_a = chart.scrollablePlotArea) === null || _a === void 0 ? void 0 : _a.fixedDiv) || chart.container);\n            innerMenu = createElement('ul', { className: 'highcharts-menu' }, chart.styledMode ? {} : {\n                listStyle: 'none',\n                margin: 0,\n                padding: 0\n            }, menu);\n            // Presentational CSS\n            if (!chart.styledMode) {\n                css(innerMenu, extend({\n                    MozBoxShadow: '3px 3px 10px #888',\n                    WebkitBoxShadow: '3px 3px 10px #888',\n                    boxShadow: '3px 3px 10px #888'\n                }, navOptions.menuStyle));\n            }\n            // Hide on mouse out\n            menu.hideMenu = function () {\n                css(menu, { display: 'none' });\n                if (button) {\n                    button.setState(0);\n                }\n                chart.openMenu = false;\n                // #10361, #9998\n                css(chart.renderTo, { overflow: 'hidden' });\n                css(chart.container, { overflow: 'hidden' });\n                highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(menu.hideTimer);\n                Exporting_fireEvent(chart, 'exportMenuHidden');\n            };\n            // Hide the menu some time after mouse leave (#1357)\n            chart.exportEvents.push(Exporting_addEvent(menu, 'mouseleave', function () {\n                menu.hideTimer = Exporting_win.setTimeout(menu.hideMenu, 500);\n            }), Exporting_addEvent(menu, 'mouseenter', function () {\n                highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(menu.hideTimer);\n            }), \n            // Hide it on clicking or touching outside the menu (#2258,\n            // #2335, #2407)\n            Exporting_addEvent(doc, 'mouseup', function (e) {\n                var _a;\n                if (!((_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.inClass(e.target, className))) {\n                    menu.hideMenu();\n                }\n            }), Exporting_addEvent(menu, 'click', function () {\n                if (chart.openMenu) {\n                    menu.hideMenu();\n                }\n            }));\n            // Create the items\n            items.forEach(function (item) {\n                if (typeof item === 'string') {\n                    item = chart.options.exporting\n                        .menuItemDefinitions[item];\n                }\n                if (isObject(item, true)) {\n                    var element = void 0;\n                    if (item.separator) {\n                        element = createElement('hr', void 0, void 0, innerMenu);\n                    }\n                    else {\n                        // When chart initialized with the table, wrong button\n                        // text displayed, #14352.\n                        if (item.textKey === 'viewData' &&\n                            chart.isDataTableVisible) {\n                            item.textKey = 'hideData';\n                        }\n                        element = createElement('li', {\n                            className: 'highcharts-menu-item',\n                            onclick: function (e) {\n                                if (e) { // IE7\n                                    e.stopPropagation();\n                                }\n                                menu.hideMenu();\n                                if (typeof item !== 'string' && item.onclick) {\n                                    item.onclick.apply(chart, arguments);\n                                }\n                            }\n                        }, void 0, innerMenu);\n                        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(element, item.text ||\n                            chart.options.lang[item.textKey]);\n                        if (!chart.styledMode) {\n                            element.onmouseover = function () {\n                                css(this, navOptions.menuItemHoverStyle);\n                            };\n                            element.onmouseout = function () {\n                                css(this, navOptions.menuItemStyle);\n                            };\n                            css(element, extend({\n                                cursor: 'pointer'\n                            }, navOptions.menuItemStyle || {}));\n                        }\n                    }\n                    // Keep references to menu divs to be able to destroy them\n                    chart.exportDivElements.push(element);\n                }\n            });\n            // Keep references to menu and innerMenu div to be able to destroy\n            // them\n            chart.exportDivElements.push(innerMenu, menu);\n            chart.exportMenuWidth = menu.offsetWidth;\n            chart.exportMenuHeight = menu.offsetHeight;\n        }\n        var menuStyle = { display: 'block' };\n        // If outside right, right align it\n        if (x + (chart.exportMenuWidth || 0) > chartWidth) {\n            menuStyle.right = (chartWidth - x - width - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.left = (x - menuPadding) + 'px';\n        }\n        // If outside bottom, bottom align it\n        if (y + height + (chart.exportMenuHeight || 0) > chartHeight &&\n            ((_b = button.alignOptions) === null || _b === void 0 ? void 0 : _b.verticalAlign) !== 'top') {\n            menuStyle.bottom = (chartHeight - y - menuPadding) + 'px';\n        }\n        else {\n            menuStyle.top = (y + height - menuPadding) + 'px';\n        }\n        css(menu, menuStyle);\n        // #10361, #9998\n        css(chart.renderTo, { overflow: '' });\n        css(chart.container, { overflow: '' });\n        chart.openMenu = true;\n        Exporting_fireEvent(chart, 'exportMenuShown');\n    }\n    /**\n     * Destroy the export buttons.\n     * @private\n     * @function Highcharts.Chart#destroyExport\n     * @param {global.Event} [e]\n     * @requires modules/exporting\n     */\n    function destroyExport(e) {\n        var chart = e ? e.target : this,\n            exportSVGElements = chart.exportSVGElements,\n            exportDivElements = chart.exportDivElements,\n            exportEvents = chart.exportEvents;\n        var cacheName;\n        // Destroy the extra buttons added\n        if (exportSVGElements) {\n            exportSVGElements.forEach(function (elem, i) {\n                // Destroy and null the svg elements\n                if (elem) { // #1822\n                    elem.onclick = elem.ontouchstart = null;\n                    cacheName = 'cache-' + elem.menuClassName;\n                    if (chart[cacheName]) {\n                        delete chart[cacheName];\n                    }\n                    exportSVGElements[i] = elem.destroy();\n                }\n            });\n            exportSVGElements.length = 0;\n        }\n        // Destroy the exporting group\n        if (chart.exportingGroup) {\n            chart.exportingGroup.destroy();\n            delete chart.exportingGroup;\n        }\n        // Destroy the divs for the menu\n        if (exportDivElements) {\n            exportDivElements.forEach(function (elem, i) {\n                if (elem) {\n                    // Remove the event handler\n                    highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(elem.hideTimer); // #5427\n                    removeEvent(elem, 'mouseleave');\n                    // Remove inline events\n                    // (chart.exportDivElements as any)[i] =\n                    exportDivElements[i] =\n                        elem.onmouseout =\n                            elem.onmouseover =\n                                elem.ontouchstart =\n                                    elem.onclick = null;\n                    // Destroy the div by moving to garbage bin\n                    Exporting_discardElement(elem);\n                }\n            });\n            exportDivElements.length = 0;\n        }\n        if (exportEvents) {\n            exportEvents.forEach(function (unbind) {\n                unbind();\n            });\n            exportEvents.length = 0;\n        }\n    }\n    /**\n     * Exporting module required. Submit an SVG version of the chart to a server\n     * along with some parameters for conversion.\n     *\n     * @sample highcharts/members/chart-exportchart/\n     *         Export with no options\n     * @sample highcharts/members/chart-exportchart-filename/\n     *         PDF type and custom filename\n     * @sample highcharts/members/chart-exportchart-custom-background/\n     *         Different chart background in export\n     * @sample stock/members/chart-exportchart/\n     *         Export with Highcharts Stock\n     *\n     * @function Highcharts.Chart#exportChart\n     *\n     * @param {Highcharts.ExportingOptions} exportingOptions\n     *        Exporting options in addition to those defined in\n     *        [exporting](https://api.highcharts.com/highcharts/exporting).\n     *\n     * @param {Highcharts.Options} chartOptions\n     *        Additional chart options for the exported chart. For example a\n     *        different background color can be added here, or `dataLabels` for\n     *        export only.\n     *\n     * @requires modules/exporting\n     */\n    function exportChart(exportingOptions, chartOptions) {\n        var svg = this.getSVGForExport(exportingOptions,\n            chartOptions);\n        // Merge the options\n        exportingOptions = merge(this.options.exporting, exportingOptions);\n        // Do the post\n        Core_HttpUtilities.post(exportingOptions.url, {\n            filename: exportingOptions.filename ?\n                exportingOptions.filename.replace(/\\//g, '-') :\n                this.getFilename(),\n            type: exportingOptions.type,\n            width: exportingOptions.width,\n            scale: exportingOptions.scale,\n            svg: svg\n        }, exportingOptions.fetchOptions);\n    }\n    /**\n     * Return the unfiltered innerHTML of the chart container. Used as hook for\n     * plugins. In styled mode, it also takes care of inlining CSS style rules.\n     *\n     * @see Chart#getSVG\n     *\n     * @function Highcharts.Chart#getChartHTML\n     *\n     * @return {string}\n     * The unfiltered SVG of the chart.\n     *\n     * @requires modules/exporting\n     */\n    function getChartHTML(applyStyleSheets) {\n        if (applyStyleSheets) {\n            this.inlineStyles();\n        }\n        this.resolveCSSVariables();\n        return this.container.innerHTML;\n    }\n    /**\n     * Get the default file name used for exported charts. By default it creates\n     * a file name based on the chart title.\n     *\n     * @function Highcharts.Chart#getFilename\n     *\n     * @return {string} A file name without extension.\n     *\n     * @requires modules/exporting\n     */\n    function getFilename() {\n        var s = this.userOptions.title && this.userOptions.title.text;\n        var filename = this.options.exporting.filename;\n        if (filename) {\n            return filename.replace(/\\//g, '-');\n        }\n        if (typeof s === 'string') {\n            filename = s\n                .toLowerCase()\n                .replace(/<\\/?[^>]+(>|$)/g, '') // Strip HTML tags\n                .replace(/[\\s_]+/g, '-')\n                .replace(/[^a-z\\d\\-]/g, '') // Preserve only latin\n                .replace(/^[\\-]+/g, '') // Dashes in the start\n                .replace(/[\\-]+/g, '-') // Dashes in a row\n                .substr(0, 24)\n                .replace(/[\\-]+$/g, ''); // Dashes in the end;\n        }\n        if (!filename || filename.length < 5) {\n            filename = 'chart';\n        }\n        return filename;\n    }\n    /**\n     * Return an SVG representation of the chart.\n     *\n     * @sample highcharts/members/chart-getsvg/\n     *         View the SVG from a button\n     *\n     * @function Highcharts.Chart#getSVG\n     *\n     * @param {Highcharts.Options} [chartOptions]\n     *        Additional chart options for the generated SVG representation. For\n     *        collections like `xAxis`, `yAxis` or `series`, the additional\n     *        options is either merged in to the original item of the same\n     *        `id`, or to the first item if a common id is not found.\n     *\n     * @return {string}\n     *         The SVG representation of the rendered chart.\n     *\n     * @emits Highcharts.Chart#event:getSVG\n     *\n     * @requires modules/exporting\n     */\n    function getSVG(chartOptions) {\n        var _a;\n        var chart = this;\n        var svg,\n            seriesOptions, \n            // Copy the options and add extra options\n            options = merge(chart.options,\n            chartOptions);\n        // Use userOptions to make the options chain in series right (#3881)\n        options.plotOptions = merge(chart.userOptions.plotOptions, chartOptions && chartOptions.plotOptions);\n        // ... and likewise with time, avoid that undefined time properties are\n        // merged over legacy global time options\n        options.time = merge(chart.userOptions.time, chartOptions && chartOptions.time);\n        // Create a sandbox where a new chart will be generated\n        var sandbox = createElement('div',\n            null, {\n                position: 'absolute',\n                top: '-9999em',\n                width: chart.chartWidth + 'px',\n                height: chart.chartHeight + 'px'\n            },\n            doc.body);\n        // Get the source size\n        var cssWidth = chart.renderTo.style.width, cssHeight = chart.renderTo.style.height, sourceWidth = options.exporting.sourceWidth ||\n                options.chart.width ||\n                (/px$/.test(cssWidth) && parseInt(cssWidth, 10)) ||\n                (options.isGantt ? 800 : 600), sourceHeight = options.exporting.sourceHeight ||\n                options.chart.height ||\n                (/px$/.test(cssHeight) && parseInt(cssHeight, 10)) ||\n                400;\n        // Override some options\n        extend(options.chart, {\n            animation: false,\n            renderTo: sandbox,\n            forExport: true,\n            renderer: 'SVGRenderer',\n            width: sourceWidth,\n            height: sourceHeight\n        });\n        options.exporting.enabled = false; // Hide buttons in print\n        delete options.data; // #3004\n        // prepare for replicating the chart\n        options.series = [];\n        chart.series.forEach(function (serie) {\n            seriesOptions = merge(serie.userOptions, {\n                animation: false, // Turn off animation\n                enableMouseTracking: false,\n                showCheckbox: false,\n                visible: serie.visible\n            });\n            // Used for the navigator series that has its own option set\n            if (!seriesOptions.isInternal) {\n                options.series.push(seriesOptions);\n            }\n        });\n        var colls = {};\n        chart.axes.forEach(function (axis) {\n            // Assign an internal key to ensure a one-to-one mapping (#5924)\n            if (!axis.userOptions.internalKey) { // #6444\n                axis.userOptions.internalKey = uniqueKey();\n            }\n            if (!axis.options.isInternal) {\n                if (!colls[axis.coll]) {\n                    colls[axis.coll] = true;\n                    options[axis.coll] = [];\n                }\n                options[axis.coll].push(merge(axis.userOptions, {\n                    visible: axis.visible,\n                    // Force some options that could have be set directly on\n                    // the axis while missing in the userOptions or options.\n                    type: axis.type,\n                    uniqueNames: axis.uniqueNames\n                }));\n            }\n        });\n        // Make sure the `colorAxis` object of the `defaultOptions` isn't used\n        // in the chart copy's user options, because a color axis should only be\n        // added when the user actually applies it.\n        options.colorAxis = chart.userOptions.colorAxis;\n        // Generate the chart copy\n        var chartCopy = new chart.constructor(options,\n            chart.callback);\n        // Axis options and series options  (#2022, #3900, #5982)\n        if (chartOptions) {\n            ['xAxis', 'yAxis', 'series'].forEach(function (coll) {\n                var collOptions = {};\n                if (chartOptions[coll]) {\n                    collOptions[coll] = chartOptions[coll];\n                    chartCopy.update(collOptions);\n                }\n            });\n        }\n        // Reflect axis extremes in the export (#5924)\n        chart.axes.forEach(function (axis) {\n            var axisCopy = find(chartCopy.axes,\n                function (copy) {\n                    return copy.options.internalKey === axis.userOptions.internalKey;\n            });\n            if (axisCopy) {\n                var extremes = axis.getExtremes(), \n                    // Make sure min and max overrides in the\n                    // `exporting.chartOptions.xAxis` settings are reflected.\n                    // These should override user-set extremes via zooming,\n                    // scrollbar etc (#7873).\n                    exportOverride = splat((chartOptions === null || chartOptions === void 0 ? void 0 : chartOptions[axis.coll]) || {})[0],\n                    userMin = 'min' in exportOverride ?\n                        exportOverride.min :\n                        extremes.userMin,\n                    userMax = 'max' in exportOverride ?\n                        exportOverride.max :\n                        extremes.userMax;\n                if (((typeof userMin !== 'undefined' &&\n                    userMin !== axisCopy.min) || (typeof userMax !== 'undefined' &&\n                    userMax !== axisCopy.max))) {\n                    axisCopy.setExtremes(userMin !== null && userMin !== void 0 ? userMin : void 0, userMax !== null && userMax !== void 0 ? userMax : void 0, true, false);\n                }\n            }\n        });\n        // Get the SVG from the container's innerHTML\n        svg = chartCopy.getChartHTML(chart.styledMode ||\n            ((_a = options.exporting) === null || _a === void 0 ? void 0 : _a.applyStyleSheets));\n        Exporting_fireEvent(this, 'getSVG', { chartCopy: chartCopy });\n        svg = chart.sanitizeSVG(svg, options);\n        // Free up memory\n        options = null;\n        chartCopy.destroy();\n        Exporting_discardElement(sandbox);\n        return svg;\n    }\n    /**\n     * @private\n     * @function Highcharts.Chart#getSVGForExport\n     */\n    function getSVGForExport(options, chartOptions) {\n        var chartExportingOptions = this.options.exporting;\n        return this.getSVG(merge({ chart: { borderRadius: 0 } }, chartExportingOptions.chartOptions, chartOptions, {\n            exporting: {\n                sourceWidth: ((options && options.sourceWidth) ||\n                    chartExportingOptions.sourceWidth),\n                sourceHeight: ((options && options.sourceHeight) ||\n                    chartExportingOptions.sourceHeight)\n            }\n        }));\n    }\n    /**\n     * Make hyphenated property names out of camelCase\n     * @private\n     * @param {string} prop\n     * Property name in camelCase\n     * @return {string}\n     * Hyphenated property name\n     */\n    function hyphenate(prop) {\n        return prop.replace(/[A-Z]/g, function (match) {\n            return '-' + match.toLowerCase();\n        });\n    }\n    /**\n     * Analyze inherited styles from stylesheets and add them inline\n     *\n     * @private\n     * @function Highcharts.Chart#inlineStyles\n     *\n     * @todo What are the border styles for text about? In general, text has a\n     *       lot of properties.\n     *\n     * @todo Make it work with IE9 and IE10.\n     *\n     * @requires modules/exporting\n     */\n    function inlineStyles() {\n        var denylist = inlineDenylist,\n            allowlist = Exporting.inlineAllowlist, // For IE\n            defaultStyles = {};\n        var dummySVG;\n        // Create an iframe where we read default styles without pollution from\n        // this body\n        var iframe = doc.createElement('iframe');\n        css(iframe, {\n            width: '1px',\n            height: '1px',\n            visibility: 'hidden'\n        });\n        doc.body.appendChild(iframe);\n        var iframeDoc = (iframe.contentWindow && iframe.contentWindow.document);\n        if (iframeDoc) {\n            iframeDoc.body.appendChild(iframeDoc.createElementNS(SVG_NS, 'svg'));\n        }\n        /**\n         * Call this on all elements and recurse to children\n         * @private\n         * @param {Highcharts.HTMLDOMElement} node\n         *        Element child\n             */\n        function recurse(node) {\n            var filteredStyles = {};\n            var styles,\n                parentStyles,\n                dummy,\n                denylisted,\n                allowlisted,\n                i;\n            /**\n             * Check computed styles and whether they are in the allow/denylist\n             * for styles or attributes.\n             * @private\n             * @param {string} val\n             *        Style value\n             * @param {string} prop\n             *        Style property name\n                     */\n            function filterStyles(val, prop) {\n                // Check against allowlist & denylist\n                denylisted = allowlisted = false;\n                if (allowlist.length) {\n                    // Styled mode in IE has a allowlist instead. Exclude all\n                    // props not in this list.\n                    i = allowlist.length;\n                    while (i-- && !allowlisted) {\n                        allowlisted = allowlist[i].test(prop);\n                    }\n                    denylisted = !allowlisted;\n                }\n                // Explicitly remove empty transforms\n                if (prop === 'transform' && val === 'none') {\n                    denylisted = true;\n                }\n                i = denylist.length;\n                while (i-- && !denylisted) {\n                    if (prop.length > 1000 /* RegexLimits.shortLimit */) {\n                        throw new Error('Input too long');\n                    }\n                    denylisted = (denylist[i].test(prop) ||\n                        typeof val === 'function');\n                }\n                if (!denylisted) {\n                    // If parent node has the same style, it gets inherited, no\n                    // need to inline it. Top-level props should be diffed\n                    // against parent (#7687).\n                    if ((parentStyles[prop] !== val ||\n                        node.nodeName === 'svg') &&\n                        defaultStyles[node.nodeName][prop] !== val) {\n                        // Attributes\n                        if (!inlineToAttributes ||\n                            inlineToAttributes.indexOf(prop) !== -1) {\n                            if (val) {\n                                node.setAttribute(hyphenate(prop), val);\n                            }\n                            // Styles\n                        }\n                        else {\n                            filteredStyles[prop] = val;\n                        }\n                    }\n                }\n            }\n            if (iframeDoc &&\n                node.nodeType === 1 &&\n                unstyledElements.indexOf(node.nodeName) === -1) {\n                styles = Exporting_win.getComputedStyle(node, null);\n                parentStyles = node.nodeName === 'svg' ?\n                    {} :\n                    Exporting_win.getComputedStyle(node.parentNode, null);\n                // Get default styles from the browser so that we don't have to\n                // add these\n                if (!defaultStyles[node.nodeName]) {\n                    /*\n                    If (!dummySVG) {\n                        dummySVG = doc.createElementNS(H.SVG_NS, 'svg');\n                        dummySVG.setAttribute('version', '1.1');\n                        doc.body.appendChild(dummySVG);\n                    }\n                    */\n                    dummySVG = iframeDoc.getElementsByTagName('svg')[0];\n                    dummy = iframeDoc.createElementNS(node.namespaceURI, node.nodeName);\n                    dummySVG.appendChild(dummy);\n                    // Get the defaults into a standard object (simple merge\n                    // won't do)\n                    var s = Exporting_win.getComputedStyle(dummy,\n                        null),\n                        defaults = {};\n                    for (var key in s) {\n                        if (key.length < 1000 /* RegexLimits.shortLimit */ &&\n                            typeof s[key] === 'string' &&\n                            !/^\\d+$/.test(key)) {\n                            defaults[key] = s[key];\n                        }\n                    }\n                    defaultStyles[node.nodeName] = defaults;\n                    // Remove default fill, otherwise text disappears when\n                    // exported\n                    if (node.nodeName === 'text') {\n                        delete defaultStyles.text.fill;\n                    }\n                    dummySVG.removeChild(dummy);\n                }\n                // Loop through all styles and add them inline if they are ok\n                for (var p in styles) {\n                    if (\n                    // Some browsers put lots of styles on the prototype...\n                    (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFirefox ||\n                        (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS ||\n                        (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari || // #16902\n                        // ... Chrome puts them on the instance\n                        Object.hasOwnProperty.call(styles, p)) {\n                        filterStyles(styles[p], p);\n                    }\n                }\n                // Apply styles\n                css(node, filteredStyles);\n                // Set default stroke width (needed at least for IE)\n                if (node.nodeName === 'svg') {\n                    node.setAttribute('stroke-width', '1px');\n                }\n                if (node.nodeName === 'text') {\n                    return;\n                }\n                // Recurse\n                [].forEach.call(node.children || node.childNodes, recurse);\n            }\n        }\n        /**\n         * Remove the dummy objects used to get defaults\n         * @private\n         */\n        function tearDown() {\n            dummySVG.parentNode.removeChild(dummySVG);\n            // Remove trash from DOM that stayed after each exporting\n            iframe.parentNode.removeChild(iframe);\n        }\n        recurse(this.container.querySelector('svg'));\n        tearDown();\n    }\n    /**\n     * Resolve CSS variables into hex colors\n     */\n    function resolveCSSVariables() {\n        var svgElements = this.container.querySelectorAll('*'), colorAttributes = ['color', 'fill', 'stop-color', 'stroke'];\n        Array.from(svgElements).forEach(function (element) {\n            colorAttributes.forEach(function (attr) {\n                var attrValue = element.getAttribute(attr);\n                if (attrValue === null || attrValue === void 0 ? void 0 : attrValue.includes('var(')) {\n                    element.setAttribute(attr, getComputedStyle(element).getPropertyValue(attr));\n                }\n            });\n        });\n    }\n    /**\n     * Move the chart container(s) to another div.\n     *\n     * @function Highcharts#moveContainers\n     *\n     * @private\n     *\n     * @param {Highcharts.HTMLDOMElement} moveTo\n     *        Move target\n     */\n    function moveContainers(moveTo) {\n        var scrollablePlotArea = this.scrollablePlotArea;\n        (\n        // When scrollablePlotArea is active (#9533)\n        scrollablePlotArea ?\n            [\n                scrollablePlotArea.fixedDiv,\n                scrollablePlotArea.scrollingContainer\n            ] :\n            [this.container]).forEach(function (div) {\n            moveTo.appendChild(div);\n        });\n    }\n    /**\n     * Add update methods to handle chart.update and chart.exporting.update and\n     * chart.navigation.update. These must be added to the chart instance rather\n     * than the Chart prototype in order to use the chart instance inside the\n     * update function.\n     * @private\n     */\n    function onChartInit() {\n        var chart = this, \n            /**\n             * @private\n             * @param {\"exporting\"|\"navigation\"} prop\n             *        Property name in option root\n             * @param {Highcharts.ExportingOptions|Highcharts.NavigationOptions} options\n             *        Options to update\n             * @param {boolean} [redraw=true]\n             *        Whether to redraw\n                     */\n            update = function (prop,\n            options,\n            redraw) {\n                chart.isDirtyExporting = true;\n            merge(true, chart.options[prop], options);\n            if (pick(redraw, true)) {\n                chart.redraw();\n            }\n        };\n        chart.exporting = {\n            update: function (options, redraw) {\n                update('exporting', options, redraw);\n            }\n        };\n        // Register update() method for navigation. Cannot be set the same way\n        // as for exporting, because navigation options are shared with bindings\n        // which has separate update() logic.\n        Chart_ChartNavigationComposition\n            .compose(chart).navigation\n            .addUpdate(function (options, redraw) {\n            update('navigation', options, redraw);\n        });\n    }\n    /**\n     * On layout of titles (title, subtitle and caption), adjust the `alignTo``\n     * box to avoid the context menu button.\n     * @private\n     */\n    function onChartLayOutTitle(_a) {\n        var _b,\n            _c,\n            _d,\n            _e;\n        var alignTo = _a.alignTo,\n            key = _a.key,\n            textPxLength = _a.textPxLength;\n        var exportingOptions = this.options.exporting,\n            _f = merge((_b = this.options.navigation) === null || _b === void 0 ? void 0 : _b.buttonOptions, (_c = exportingOptions === null || exportingOptions === void 0 ? void 0 : exportingOptions.buttons) === null || _c === void 0 ? void 0 : _c.contextButton),\n            align = _f.align,\n            _g = _f.buttonSpacing,\n            buttonSpacing = _g === void 0 ? 0 : _g,\n            verticalAlign = _f.verticalAlign,\n            _h = _f.width,\n            width = _h === void 0 ? 0 : _h,\n            space = alignTo.width - textPxLength,\n            widthAdjust = width + buttonSpacing;\n        if (((_d = exportingOptions === null || exportingOptions === void 0 ? void 0 : exportingOptions.enabled) !== null && _d !== void 0 ? _d : true) &&\n            key === 'title' &&\n            align === 'right' &&\n            verticalAlign === 'top') {\n            if (space < 2 * widthAdjust) {\n                if (space < widthAdjust) {\n                    alignTo.width -= widthAdjust;\n                }\n                else if (((_e = this.title) === null || _e === void 0 ? void 0 : _e.alignValue) !== 'left') {\n                    alignTo.x -= widthAdjust - space / 2;\n                }\n            }\n        }\n    }\n    /**\n     * Exporting module required. Clears away other elements in the page and\n     * prints the chart as it is displayed. By default, when the exporting\n     * module is enabled, a context button with a drop down menu in the upper\n     * right corner accesses this function.\n     *\n     * @sample highcharts/members/chart-print/\n     *         Print from a HTML button\n     *\n     * @function Highcharts.Chart#print\n     *\n     *\n     * @emits Highcharts.Chart#event:beforePrint\n     * @emits Highcharts.Chart#event:afterPrint\n     *\n     * @requires modules/exporting\n     */\n    function print() {\n        var chart = this;\n        if (chart.isPrinting) { // Block the button while in printing mode\n            return;\n        }\n        printingChart = chart;\n        if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n            chart.beforePrint();\n        }\n        // Give the browser time to draw WebGL content, an issue that randomly\n        // appears (at least) in Chrome ~67 on the Mac (#8708).\n        setTimeout(function () {\n            Exporting_win.focus(); // #1510\n            Exporting_win.print();\n            // Allow the browser to prepare before reverting\n            if (!(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari) {\n                setTimeout(function () {\n                    chart.afterPrint();\n                }, 1000);\n            }\n        }, 1);\n    }\n    /**\n     * Add the buttons on chart load\n     * @private\n     * @function Highcharts.Chart#renderExporting\n     * @requires modules/exporting\n     */\n    function renderExporting() {\n        var chart = this,\n            exportingOptions = chart.options.exporting,\n            buttons = exportingOptions.buttons,\n            isDirty = chart.isDirtyExporting || !chart.exportSVGElements;\n        chart.buttonOffset = 0;\n        if (chart.isDirtyExporting) {\n            chart.destroyExport();\n        }\n        if (isDirty && exportingOptions.enabled !== false) {\n            chart.exportEvents = [];\n            chart.exportingGroup = chart.exportingGroup ||\n                chart.renderer.g('exporting-group').attr({\n                    zIndex: 3 // #4955, // #8392\n                }).add();\n            Exporting_objectEach(buttons, function (button) {\n                chart.addButton(button);\n            });\n            chart.isDirtyExporting = false;\n        }\n    }\n    /**\n     * Exporting module only. A collection of fixes on the produced SVG to\n     * account for expand properties, browser bugs.\n     * Returns a cleaned SVG.\n     *\n     * @private\n     * @function Highcharts.Chart#sanitizeSVG\n     * @param {string} svg\n     *        SVG code to sanitize\n     * @param {Highcharts.Options} options\n     *        Chart options to apply\n     * @return {string}\n     *         Sanitized SVG code\n     * @requires modules/exporting\n     */\n    function sanitizeSVG(svg, options) {\n        var _a;\n        var split = svg.indexOf('</svg>') + 6, useForeignObject = svg.indexOf('<foreignObject') > -1;\n        var html = svg.substr(split);\n        // Remove any HTML added to the container after the SVG (#894, #9087)\n        svg = svg.substr(0, split);\n        if (useForeignObject) {\n            // Some tags needs to be closed in xhtml (#13726)\n            svg = svg.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />');\n            // Move HTML into a foreignObject\n        }\n        else if (html && ((_a = options === null || options === void 0 ? void 0 : options.exporting) === null || _a === void 0 ? void 0 : _a.allowHTML)) {\n            html = '<foreignObject x=\"0\" y=\"0\" ' +\n                'width=\"' + options.chart.width + '\" ' +\n                'height=\"' + options.chart.height + '\">' +\n                '<body xmlns=\"http://www.w3.org/1999/xhtml\">' +\n                // Some tags needs to be closed in xhtml (#13726)\n                html.replace(/(<(?:img|br).*?(?=\\>))>/g, '$1 />') +\n                '</body>' +\n                '</foreignObject>';\n            svg = svg.replace('</svg>', html + '</svg>');\n        }\n        svg = svg\n            .replace(/zIndex=\"[^\"]+\"/g, '')\n            .replace(/symbolName=\"[^\"]+\"/g, '')\n            .replace(/jQuery\\d+=\"[^\"]+\"/g, '')\n            .replace(/url\\((\"|&quot;)(.*?)(\"|&quot;)\\;?\\)/g, 'url($2)')\n            .replace(/url\\([^#]+#/g, 'url(#')\n            .replace(/<svg /, '<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" ')\n            .replace(/ (NS\\d+\\:)?href=/g, ' xlink:href=') // #3567\n            .replace(/\\n+/g, ' ')\n            // Replace HTML entities, issue #347\n            .replace(/&nbsp;/g, '\\u00A0') // No-break space\n            .replace(/&shy;/g, '\\u00AD'); // Soft hyphen\n        return svg;\n    }\n})(Exporting || (Exporting = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Exporting_Exporting = (Exporting);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Gets fired after a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingAfterPrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Gets fired before a chart is printed through the context menu item or the\n * Chart.print method.\n *\n * @callback Highcharts.ExportingBeforePrintCallbackFunction\n *\n * @param {Highcharts.Chart} this\n *        The chart on which the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n/**\n * Function to call if the offline-exporting module fails to export a chart on\n * the client side.\n *\n * @callback Highcharts.ExportingErrorCallbackFunction\n *\n * @param {Highcharts.ExportingOptions} options\n *        The exporting options.\n *\n * @param {global.Error} err\n *        The error from the module.\n */\n/**\n * Definition for a menu item in the context menu.\n *\n * @interface Highcharts.ExportingMenuObject\n */ /**\n* The text for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#text\n* @type {string|undefined}\n*/ /**\n* If internationalization is required, the key to a language string.\n*\n* @name Highcharts.ExportingMenuObject#textKey\n* @type {string|undefined}\n*/ /**\n* The click handler for the menu item.\n*\n* @name Highcharts.ExportingMenuObject#onclick\n* @type {Highcharts.EventCallbackFunction<Highcharts.Chart>|undefined}\n*/ /**\n* Indicates a separator line instead of an item.\n*\n* @name Highcharts.ExportingMenuObject#separator\n* @type {boolean|undefined}\n*/\n/**\n * Possible MIME types for exporting.\n *\n * @typedef {\"image/png\"|\"image/jpeg\"|\"application/pdf\"|\"image/svg+xml\"} Highcharts.ExportingMimeTypeValue\n */\n(''); // Keeps doclets above in transpiled file\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after a chart is printed through the context menu item or the\n * `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingAfterPrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.afterPrint\n */\n/**\n * Fires before a chart is printed through the context menu item or\n * the `Chart.print` method.\n *\n * @sample highcharts/chart/events-beforeprint-afterprint/\n *         Rescale the chart to print\n *\n * @type      {Highcharts.ExportingBeforePrintCallbackFunction}\n * @since     4.1.0\n * @context   Highcharts.Chart\n * @requires  modules/exporting\n * @apioption chart.events.beforePrint\n */\n(''); // Keeps doclets above in transpiled file\n\n;// ./code/es5/es-modules/masters/modules/exporting.js\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.HttpUtilities = G.HttpUtilities || Core_HttpUtilities;\nG.ajax = G.HttpUtilities.ajax;\nG.getJSON = G.HttpUtilities.getJSON;\nG.post = G.HttpUtilities.post;\nExporting_Exporting.compose(G.Chart, G.Renderer);\n/* harmony default export */ var exporting_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__660__", "__WEBPACK_EXTERNAL_MODULE__960__", "ChartNavigationComposition", "Additions", "ExportingSymbols", "Exporting", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "exporting_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "compose", "chart", "navigation", "updates", "addUpdate", "updateFn", "push", "update", "options", "redraw", "_this", "for<PERSON>ach", "Chart_ChartNavigationComposition", "isTouchDevice", "Exporting_ExportingDefaults", "exporting", "allowTableSorting", "type", "url", "concat", "version", "pdfFont", "normal", "bold", "bolditalic", "italic", "printMaxWidth", "scale", "buttons", "contextButton", "className", "menuClassName", "symbol", "<PERSON><PERSON><PERSON>", "menuItems", "menuItemDefinitions", "viewFullscreen", "<PERSON><PERSON><PERSON>", "onclick", "fullscreen", "toggle", "printChart", "print", "separator", "downloadPNG", "exportChart", "downloadJPEG", "downloadPDF", "downloadSVG", "lang", "exitFullscreen", "contextButtonTitle", "buttonOptions", "symbolSize", "symbolX", "symbolY", "align", "buttonSpacing", "height", "y", "verticalAlign", "width", "symbolFill", "symbolStroke", "symbolStrokeWidth", "theme", "fill", "padding", "stroke", "menuStyle", "border", "borderRadius", "background", "menuItemStyle", "color", "fontSize", "transition", "menuItemHoverStyle", "modifiedClasses", "menu", "x", "menuball", "h", "path", "circle", "SVGRendererClass", "indexOf", "symbols", "bind", "Exporting_ExportingSymbols", "composed", "addEvent", "fireEvent", "pushUnique", "onChartBeforeRender", "Fullscreen", "isOpen", "container", "renderTo", "browserProps", "requestFullscreen", "fullscreenChange", "mozRequestFullScreen", "webkitRequestFullScreen", "msRequestFullscreen", "ChartClass", "close", "optionsChart", "ownerDocument", "Document", "unbindFullscreenEvent", "setSize", "origWidth", "origHeight", "origWidthOption", "origHeightOption", "setButtonText", "open", "chartWidth", "chartHeight", "unbindChange_1", "unbindDestroy_1", "promise", "alert", "exportDivElements", "exportingOptions", "exportDivElement", "setElementHTML", "text", "__assign", "assign", "t", "s", "i", "arguments", "length", "p", "apply", "win", "discardElement", "objectEach", "HttpUtilities", "ajax", "settings", "_a", "headers", "json", "xml", "octet", "r", "XMLHttpRequest", "handleError", "xhr", "err", "error", "toUpperCase", "setRequestHeader", "dataType", "val", "responseType", "onreadystatechange", "res", "readyState", "status", "responseText", "JSON", "parse", "e", "Error", "success", "data", "stringify", "send", "getJSON", "post", "fetchOptions", "formData", "FormData", "name", "append", "filename", "fetch", "method", "body", "then", "ok", "link", "document", "createElement", "href", "download", "click", "Exporting_assign", "defaultOptions", "doc", "SVG_NS", "Exporting_win", "Exporting_addEvent", "css", "Exporting_discardElement", "extend", "find", "Exporting_fireEvent", "isObject", "merge", "Exporting_objectEach", "pick", "removeEvent", "splat", "<PERSON><PERSON><PERSON>", "printingChart", "inlineDenylist", "inlineToAttributes", "inlineAllowlist", "unstyledElements", "addButton", "callback", "renderer", "btnOptions", "btnCount", "exportSVGElements", "enabled", "styledMode", "stopPropagation", "contextMenu", "button", "translateX", "translateY", "setState", "paddingLeft", "useHTML", "addClass", "attr", "title", "_title<PERSON>ey", "Math", "round", "zIndex", "add", "exportingGroup", "buttonOffset", "after<PERSON><PERSON>t", "printReverseInfo", "childNodes", "origDisplay", "resetParams", "moveContainers", "node", "nodeType", "style", "display", "isPrinting", "beforePrint", "pointer", "reset", "chartCallback", "composition", "renderExporting", "destroyExport", "items", "_b", "innerMenu", "navOptions", "cacheName", "menuPadding", "max", "exportContextMenu", "position", "pointerEvents", "scrollablePlotArea", "fixedDiv", "listStyle", "margin", "MozBoxShadow", "WebkitBoxShadow", "boxShadow", "hideMenu", "openMenu", "overflow", "clearTimeout", "hide<PERSON><PERSON>r", "exportEvents", "setTimeout", "inClass", "target", "item", "element", "isDataTableVisible", "on<PERSON><PERSON>ver", "onmouseout", "cursor", "exportMenuWidth", "offsetWidth", "exportMenuHeight", "offsetHeight", "right", "left", "alignOptions", "bottom", "top", "elem", "ontouchstart", "destroy", "unbind", "chartOptions", "svg", "getSVGForExport", "Core_HttpUtilities", "replace", "getFilename", "getChartHTML", "applyStyleSheets", "inlineStyles", "resolveCSSVariables", "innerHTML", "userOptions", "toLowerCase", "substr", "getSVG", "seriesOptions", "plotOptions", "time", "sandbox", "cssWidth", "cssHeight", "sourceWidth", "test", "parseInt", "isGantt", "sourceHeight", "animation", "forExport", "series", "serie", "enableMouseTracking", "showCheckbox", "visible", "isInternal", "colls", "axes", "axis", "internalKey", "coll", "uniqueNames", "colorAxis", "chartCopy", "constructor", "collOptions", "axisCopy", "copy", "extremes", "getExtremes", "exportOverride", "userMin", "min", "userMax", "setExtremes", "sanitizeSVG", "chartExportingOptions", "dummySVG", "allowlist", "defaultStyles", "iframe", "visibility", "append<PERSON><PERSON><PERSON>", "iframeDoc", "contentWindow", "createElementNS", "recurse", "styles", "parentStyles", "dummy", "denylisted", "allowlisted", "filteredStyles", "nodeName", "getComputedStyle", "parentNode", "getElementsByTagName", "namespaceURI", "defaults", "<PERSON><PERSON><PERSON><PERSON>", "isFirefox", "isMS", "<PERSON><PERSON><PERSON><PERSON>", "filterStyles", "denylist", "setAttribute", "match", "children", "querySelector", "svgElements", "querySelectorAll", "colorAttributes", "Array", "from", "attrValue", "getAttribute", "includes", "getPropertyValue", "moveTo", "scrollingContainer", "div", "onChartInit", "isDirtyExporting", "onChartLayOutTitle", "_c", "_d", "_e", "alignTo", "textPxLength", "_f", "_g", "_h", "space", "widthAdjust", "alignValue", "focus", "isDirty", "g", "split", "useForeignObject", "html", "allowHTML", "Exporting_Fullscreen", "chartProto", "callbacks", "matchMedia", "addListener", "mqlEvent", "matches", "Exporting_Exporting", "G", "Chart", "<PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,GAAM,CAAEA,QAAQ,cAAc,KAAQ,EACrG,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,MAAM,CAAE,CAAC,wBAAwB,QAAQ,CAAC,CAAEJ,GACjI,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,GAAM,CAAEA,QAAQ,cAAc,KAAQ,EAErIJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACzG,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACtH,OAAgB,AAAC,WACP,aACA,IAgHCC,EAgCHC,EAjCJD,EAk9BAE,EA4qBAC,EA7uDUC,EAAuB,CAE/B,IACC,SAASX,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,SAASjB,CAAM,EACtC,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,WAAa,OAAOnB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASrB,CAAO,CAAEuB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAe,CAC9D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAuFzB,EAAoB,KAC3G0B,EAA2G1B,EAAoBI,CAAC,CAACqB,GAElCzB,EAAoB,KAuCnHN,CArBOA,EAqERA,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAhDjCiC,OAAO,CANlC,SAAiBC,CAAK,EAIlB,OAHKA,EAAMC,UAAU,EACjBD,CAAAA,EAAMC,UAAU,CAAG,IAAIlC,EAAUiC,EAAK,EAEnCA,CACX,EAgDAlC,EAA2BC,SAAS,CApChCA,EAA2B,WAMvB,SAASA,EAAUiC,CAAK,EACpB,IAAI,CAACE,OAAO,CAAG,EAAE,CACrB,IAAI,CAACF,KAAK,CAAGA,CACjB,CAyBA,OAZAjC,EAAUuB,SAAS,CAACa,SAAS,CAAG,SAAUC,CAAQ,EAC9C,IAAI,CAACJ,KAAK,CAACC,UAAU,CAACC,OAAO,CAACG,IAAI,CAACD,EACvC,EAIArC,EAAUuB,SAAS,CAACgB,MAAM,CAAG,SAAUC,CAAO,CAAEC,CAAM,EAClD,IAAIC,EAAQ,IAAI,CAChB,IAAI,CAACP,OAAO,CAACQ,OAAO,CAAC,SAAUN,CAAQ,EACnCA,EAASZ,IAAI,CAACiB,EAAMT,KAAK,CAAEO,EAASC,EACxC,EACJ,EACOzC,CACX,IAQyB,IAAI4C,EAAoC7C,EAcjE8C,EAAgB,AAAChB,IAA+EgB,aAAa,CAo2BhFC,EALT,CACpBC,UAj1BY,CAwBZC,kBAAmB,CAAA,EAmLnBC,KAAM,YAONC,IAAK,uCAAuCC,MAAM,CAAC,AAACtB,IAA+EuB,OAAO,EAqB1IC,QAAS,CAQLC,OAAQ,KAAK,EAMbC,KAAM,KAAK,EAMXC,WAAY,KAAK,EAMjBC,OAAQ,KAAK,CACjB,EAUAC,cAAe,IAmBfC,MAAO,EAUPC,QAAS,CAWLC,cAAe,CAiCXC,UAAW,2BAIXC,cAAe,yBAgBfC,OAAQ,OASRC,SAAU,qBAoBVC,UAAW,CACP,iBACA,aACA,YACA,cACA,eACA,cACH,AACL,CACJ,EA6BAC,oBAAqB,CAIjBC,eAAgB,CACZC,QAAS,iBACTC,QAAS,WACD,IAAI,CAACC,UAAU,EACf,IAAI,CAACA,UAAU,CAACC,MAAM,EAE9B,CACJ,EAIAC,WAAY,CACRJ,QAAS,aACTC,QAAS,WACL,IAAI,CAACI,KAAK,EACd,CACJ,EAIAC,UAAW,CACPA,UAAW,CAAA,CACf,EAIAC,YAAa,CACTP,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,EACpB,CACJ,EAIAC,aAAc,CACVT,QAAS,eACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb5B,KAAM,YACV,EACJ,CACJ,EAIA8B,YAAa,CACTV,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb5B,KAAM,iBACV,EACJ,CACJ,EAIA+B,YAAa,CACTX,QAAS,cACTC,QAAS,WACL,IAAI,CAACO,WAAW,CAAC,CACb5B,KAAM,eACV,EACJ,CACJ,CACJ,CACJ,EAgWIgC,KA3VO,CAOPb,eAAgB,sBAOhBc,eAAgB,wBAOhBT,WAAY,cAOZG,YAAa,qBAObE,aAAc,sBAOdC,YAAa,wBAObC,YAAa,4BAQbG,mBAAoB,oBACxB,EAkSIjD,WA1Ra,CAUbkD,cAAe,CAoBXC,WAAY,GASZC,QAAS,KASTC,QAAS,KAUTC,MAAO,QASPC,cAAe,EASfC,OAAQ,GAsCRC,EAAG,GAWHC,cAAe,MASfC,MAAO,GAUPC,WAAY,UAUZC,aAAc,UASdC,kBAAmB,EAcnBC,MAAO,CAMHC,KAAM,UAINC,QAAS,EAMTC,OAAQ,OAIR,iBAAkB,OACtB,CACJ,EAeAC,UAAW,CAEPC,OAAQ,OAERC,aAAc,MAEdC,WAAY,UAEZL,QAAS,OACb,EAiBAM,cAAe,CAEXD,WAAY,OAEZD,aAAc,MAEdG,MAAO,UAEPP,QAAS,QAETQ,SAAU9D,EAAgB,QAAU,QAEpC+D,WAAY,+BAChB,EAgBAC,mBAAoB,CAEhBL,WAAY,SAChB,CACJ,CAUA,GAsBA,AAAC,SAAUvG,CAAgB,EAMvB,IAAI6G,EAAkB,EAAE,CAsBxB,SAASC,EAAKC,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EAqB7B,MApBU,CACF,CAAC,IACLsB,EACArB,EAAI,IAAI,CACJ,CAAC,IACLqB,EAAInB,EACJF,EAAI,IAAI,CACJ,CAAC,IACLqB,EACArB,EAAID,EAAS,EAAI,GAAI,CACjB,CAAC,IACLsB,EAAInB,EACJF,EAAID,EAAS,EAAI,GAAI,CACjB,CAAC,IACLsB,EACArB,EAAID,EAAS,IAAI,CACb,CAAC,IACLsB,EAAInB,EACJF,EAAID,EAAS,IAAI,CAChB,AAET,CAIA,SAASuB,EAASD,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,EACjC,IAAIwB,EAAI,AAACxB,EAAS,EAAK,EACnByB,EAAO,EAAE,CAEb,OADOA,EAAKhE,MAAM,CAAC,IAAI,CAACiE,MAAM,CAACvB,EAAQqB,EAAGvB,EAAGuB,EAAGA,GAAI,IAAI,CAACE,MAAM,CAACvB,EAAQqB,EAAGvB,EAAIuB,EAAI,EAAGA,EAAGA,GAAI,IAAI,CAACE,MAAM,CAACvB,EAAQqB,EAAGvB,EAAI,EAAKuB,CAAAA,EAAI,CAAA,EAAIA,EAAGA,GAE5I,CAnCAjH,EAAiB+B,OAAO,CARxB,SAAiBqF,CAAgB,EAC7B,GAAIP,AAA8C,KAA9CA,EAAgBQ,OAAO,CAACD,GAA0B,CAClDP,EAAgBxE,IAAI,CAAC+E,GACrB,IAAIE,EAAUF,EAAiB9F,SAAS,CAACgG,OAAO,AAChDA,CAAAA,EAAQR,IAAI,CAAGA,EACfQ,EAAQN,QAAQ,CAAGA,EAASO,IAAI,CAACD,EACrC,CACJ,CAqCJ,EAAGtH,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAIwH,EAA8BxH,EAsB3DyH,EAAW,AAAC7F,IAA+E6F,QAAQ,CAEnGC,EAAW,AAAC9F,IAA+E8F,QAAQ,CAAEC,EAAY,AAAC/F,IAA+E+F,SAAS,CAAEC,EAAa,AAAChG,IAA+EgG,UAAU,CASvT,SAASC,IAML,IAAI,CAACvD,UAAU,CAAG,IAAIwD,EAAW,IAAI,CACzC,CAgBA,IAAIA,EAA4B,WAM5B,SAASA,EAAW9F,CAAK,EAMrB,IAAI,CAACA,KAAK,CAAGA,EASb,IAAI,CAAC+F,MAAM,CAAG,CAAA,EACd,IAAIC,EAAYhG,EAAMiG,QAAQ,AAE1B,EAAC,IAAI,CAACC,YAAY,GACd,AAAuC,YAAvC,OAAOF,EAAUG,iBAAiB,CAClC,IAAI,CAACD,YAAY,CAAG,CAChBE,iBAAkB,mBAClBD,kBAAmB,oBACnBlD,eAAgB,gBACpB,EAEK+C,EAAUK,oBAAoB,CACnC,IAAI,CAACH,YAAY,CAAG,CAChBE,iBAAkB,sBAClBD,kBAAmB,uBACnBlD,eAAgB,qBACpB,EAEK+C,EAAUM,uBAAuB,CACtC,IAAI,CAACJ,YAAY,CAAG,CAChBE,iBAAkB,yBAClBD,kBAAmB,0BACnBlD,eAAgB,sBACpB,EAEK+C,EAAUO,mBAAmB,EAClC,CAAA,IAAI,CAACL,YAAY,CAAG,CAChBE,iBAAkB,qBAClBD,kBAAmB,sBACnBlD,eAAgB,kBACpB,CAAA,EAGZ,CA+KA,OAnKA6C,EAAW/F,OAAO,CAAG,SAAUyG,CAAU,EACjCZ,EAAWH,EAAU,eAErBC,EAASc,EAAY,eAAgBX,EAE7C,EAgBAC,EAAWxG,SAAS,CAACmH,KAAK,CAAG,WACzB,IAAInE,EAAa,IAAI,CACjBtC,EAAQsC,EAAWtC,KAAK,CACxB0G,EAAe1G,EAAMO,OAAO,CAACP,KAAK,CACtC2F,EAAU3F,EAAO,kBAAmB,KAAM,WAGlCsC,EAAWyD,MAAM,EACjBzD,EAAW4D,YAAY,EACvBlG,EAAMgG,SAAS,CAACW,aAAa,YAAYC,UACzC5G,EAAMgG,SAAS,CAACW,aAAa,CAACrE,EAAW4D,YAAY,CAACjD,cAAc,CAAC,GAIrEX,EAAWuE,qBAAqB,EAChCvE,CAAAA,EAAWuE,qBAAqB,CAAGvE,EAC9BuE,qBAAqB,EAAC,EAE/B7G,EAAM8G,OAAO,CAACxE,EAAWyE,SAAS,CAAEzE,EAAW0E,UAAU,CAAE,CAAA,GAC3D1E,EAAWyE,SAAS,CAAG,KAAK,EAC5BzE,EAAW0E,UAAU,CAAG,KAAK,EAC7BN,EAAa9C,KAAK,CAAGtB,EAAW2E,eAAe,CAC/CP,EAAajD,MAAM,CAAGnB,EAAW4E,gBAAgB,CACjD5E,EAAW2E,eAAe,CAAG,KAAK,EAClC3E,EAAW4E,gBAAgB,CAAG,KAAK,EACnC5E,EAAWyD,MAAM,CAAG,CAAA,EACpBzD,EAAW6E,aAAa,EAC5B,EACJ,EAaArB,EAAWxG,SAAS,CAAC8H,IAAI,CAAG,WACxB,IAAI9E,EAAa,IAAI,CACjBtC,EAAQsC,EAAWtC,KAAK,CACxB0G,EAAe1G,EAAMO,OAAO,CAACP,KAAK,CACtC2F,EAAU3F,EAAO,iBAAkB,KAAM,WAQrC,GAPI0G,IACApE,EAAW2E,eAAe,CAAGP,EAAa9C,KAAK,CAC/CtB,EAAW4E,gBAAgB,CAAGR,EAAajD,MAAM,EAErDnB,EAAWyE,SAAS,CAAG/G,EAAMqH,UAAU,CACvC/E,EAAW0E,UAAU,CAAGhH,EAAMsH,WAAW,CAErChF,EAAW4D,YAAY,CAAE,CACzB,IAAIqB,EAAiB7B,EAAS1F,EAAMgG,SAAS,CAACW,aAAa,CACvDrE,EAAW4D,YAAY,CAACE,gBAAgB,CACxC,WAGQ9D,EAAWyD,MAAM,EACjBzD,EAAWyD,MAAM,CAAG,CAAA,EACxBzD,EAAWmE,KAAK,KAGhBzG,EAAM8G,OAAO,CAAC,KAAM,KAAM,CAAA,GAC1BxE,EAAWyD,MAAM,CAAG,CAAA,EACpBzD,EAAW6E,aAAa,GAEhC,GACIK,EAAkB9B,EAAS1F,EAAO,UAClCuH,EACJjF,CAAAA,EAAWuE,qBAAqB,CAAG,WAC/BU,IACAC,GACJ,EACA,IAAIC,EAAUzH,EAAMiG,QAAQ,CAAC3D,EAAW4D,YAAY,CAACC,iBAAiB,CAAC,GACnEsB,GACAA,EAAQ,KAAQ,CAAC,WACbC,MACA,+CACJ,EAER,CACJ,EACJ,EAWA5B,EAAWxG,SAAS,CAAC6H,aAAa,CAAG,WACjC,IAAInH,EAAQ,IAAI,CAACA,KAAK,CAClB2H,EAAoB3H,EAAM2H,iBAAiB,CAC3CC,EAAmB5H,EAAMO,OAAO,CAACO,SAAS,CAC1CmB,EAAa2F,GACTA,EAAiBjG,OAAO,EACxBiG,EAAiBjG,OAAO,CAACC,aAAa,CAACK,SAAS,CACpDe,EAAOhD,EAAMO,OAAO,CAACyC,IAAI,CAC7B,GAAI4E,GACAA,EAAiB1F,mBAAmB,EACpCc,GACAA,EAAKC,cAAc,EACnBD,EAAKb,cAAc,EACnBF,GACA0F,EAAmB,CACnB,IAAIE,EAAmBF,CAAiB,CAAC1F,EAAUoD,OAAO,CAAC,kBAAkB,CACzEwC,GACA/H,IAA8FgI,cAAc,CAACD,EAAkB,AAAC,IAAI,CAAC9B,MAAM,CAG5G/C,EAAKC,cAAc,CAF7C2E,EAAiB1F,mBAAmB,CAACC,cAAc,CAC/C4F,IAAI,EACL/E,EAAKb,cAAc,CAEnC,CACJ,EAeA2D,EAAWxG,SAAS,CAACiD,MAAM,CAAG,WAErBD,AADY,IAAI,CACLyD,MAAM,CAIlBzD,AALa,IAAI,CAKNmE,KAAK,GAHhBnE,AAFa,IAAI,CAEN8E,IAAI,EAKvB,EACOtB,CACX,IAgFIkC,EAAgD,WAShD,MAAOA,AARPA,CAAAA,EAAWhJ,OAAOiJ,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAGC,EAAI,EAAG5J,EAAI6J,UAAUC,MAAM,CAAEF,EAAI5J,EAAG4J,IAE5C,IAAK,IAAIG,KADTJ,EAAIE,SAAS,CAACD,EAAE,CACKpJ,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2I,EAAGI,IACzDL,CAAAA,CAAC,CAACK,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,EAElB,OAAOL,CACX,CAAA,EACgBM,KAAK,CAAC,IAAI,CAAEH,UAChC,EAEII,EAAM,AAAC7I,IAA+E6I,GAAG,CAEzFC,EAAiB,AAAC9I,IAA+E8I,cAAc,CAAEC,EAAa,AAAC/I,IAA+E+I,UAAU,CAsJxNC,EAAgB,CAChBC,KAtIJ,SAAcC,CAAQ,EAElB,IADIC,EACAC,EAAU,CACNC,KAAM,mBACNC,IAAK,kBACLnB,KAAM,aACNoB,MAAO,0BACX,EACAC,EAAI,IAAIC,eASZ,SAASC,EAAYC,CAAG,CAAEC,CAAG,EACrBV,EAASW,KAAK,EACdX,EAASW,KAAK,CAACF,EAAKC,EAK5B,CACA,GAAI,CAACV,EAAS7H,GAAG,CACb,MAAO,CAAA,EAEXmI,EAAEhC,IAAI,CAAC,AAAC0B,CAAAA,EAAS9H,IAAI,EAAI,KAAI,EAAG0I,WAAW,GAAIZ,EAAS7H,GAAG,CAAE,CAAA,GACvD,CAAA,AAA4B,OAA3B8H,CAAAA,EAAKD,EAASE,OAAO,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC,eAAe,AAAD,GAChFK,EAAEO,gBAAgB,CAAC,eAAgBX,CAAO,CAACF,EAASc,QAAQ,EAAI,OAAO,EAAIZ,EAAQjB,IAAI,EAE3FY,EAAWG,EAASE,OAAO,CAAE,SAAUa,CAAG,CAAE/K,CAAG,EAC3CsK,EAAEO,gBAAgB,CAAC7K,EAAK+K,EAC5B,GACIf,EAASgB,YAAY,EACrBV,CAAAA,EAAEU,YAAY,CAAGhB,EAASgB,YAAY,AAAD,EAGzCV,EAAEW,kBAAkB,CAAG,eACfhB,EACAiB,EACJ,GAAIZ,AAAiB,IAAjBA,EAAEa,UAAU,CAAQ,CACpB,GAAIb,AAAa,MAAbA,EAAEc,MAAM,CAAU,CAClB,GAAIpB,AAA0B,SAA1BA,EAASgB,YAAY,GACrBE,EAAMZ,EAAEe,YAAY,CAChBrB,AAAsB,SAAtBA,EAASc,QAAQ,EACjB,GAAI,CACAI,EAAMI,KAAKC,KAAK,CAACL,EACrB,CACA,MAAOM,EAAG,CACN,GAAIA,aAAaC,MACb,OAAOjB,EAAYF,EAAGkB,EAE9B,CAGR,OAAO,AAA4B,OAA3BvB,CAAAA,EAAKD,EAAS0B,OAAO,AAAD,GAAezB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGvJ,IAAI,CAACsJ,EAAUkB,EAAKZ,EAC/F,CACAE,EAAYF,EAAGA,EAAEe,YAAY,CACjC,CACJ,EACIrB,EAAS2B,IAAI,EAAI,AAAyB,UAAzB,OAAO3B,EAAS2B,IAAI,EACrC3B,CAAAA,EAAS2B,IAAI,CAAGL,KAAKM,SAAS,CAAC5B,EAAS2B,IAAI,CAAA,EAEhDrB,EAAEuB,IAAI,CAAC7B,EAAS2B,IAAI,CACxB,EAqEIG,QA1DJ,SAAiB3J,CAAG,CAAEuJ,CAAO,EACzB5B,EAAcC,IAAI,CAAC,CACf5H,IAAKA,EACLuJ,QAASA,EACTZ,SAAU,OACVZ,QAAS,CAGL,eAAgB,YACpB,CACJ,EACJ,EAgDI6B,KA7BJ,SAAc5J,CAAG,CAAEwJ,CAAI,CAAEK,CAAY,EACjC,IAAIC,EAAW,IAAItC,EAAIuC,QAAQ,CAE/BrC,EAAW8B,EAAM,SAAUZ,CAAG,CAAEoB,CAAI,EAChCF,EAASG,MAAM,CAACD,EAAMpB,EAC1B,GACAkB,EAASG,MAAM,CAAC,MAAO,QACvB,IAAIC,EAAWV,EAAKU,QAAQ,CACxBnK,EAAOyJ,EAAKzJ,IAAI,CACpB,OAAOyH,EAAI2C,KAAK,CAACnK,EAAK+G,EAAS,CAAEqD,OAAQ,OAAQC,KAAMP,CAAS,EAAGD,IAAeS,IAAI,CAAC,SAAUvB,CAAG,EAC5FA,EAAIwB,EAAE,EACNxB,EAAIjC,IAAI,GAAGwD,IAAI,CAAC,SAAUxD,CAAI,EAC1B,IAAI0D,EAAOC,SAASC,aAAa,CAAC,IAClCF,CAAAA,EAAKG,IAAI,CAAG,QAAQ1K,MAAM,CAACF,EAAM,YAAYE,MAAM,CAAC6G,GACpD0D,EAAKI,QAAQ,CAAGV,EAChBM,EAAKK,KAAK,GACVpD,EAAe+C,EACnB,EAER,EACJ,CAUA,EAsDIM,EAAwD,WASxD,MAAOA,AARPA,CAAAA,EAAmB/M,OAAOiJ,MAAM,EAAI,SAASC,CAAC,EAC1C,IAAK,IAAIC,EAAGC,EAAI,EAAG5J,EAAI6J,UAAUC,MAAM,CAAEF,EAAI5J,EAAG4J,IAE5C,IAAK,IAAIG,KADTJ,EAAIE,SAAS,CAACD,EAAE,CACKpJ,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2I,EAAGI,IACzDL,CAAAA,CAAC,CAACK,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,EAElB,OAAOL,CACX,CAAA,EACwBM,KAAK,CAAC,IAAI,CAAEH,UACxC,EAKI2D,EAAiB,AAACpM,IAA+EoM,cAAc,CAK/GC,EAAM,AAACrM,IAA+EqM,GAAG,CAAEC,EAAS,AAACtM,IAA+EsM,MAAM,CAAEC,EAAgB,AAACvM,IAA+E6I,GAAG,CAG/R2D,EAAqB,AAACxM,IAA+E8F,QAAQ,CAAE2G,EAAM,AAACzM,IAA+EyM,GAAG,CAAEV,EAAgB,AAAC/L,IAA+E+L,aAAa,CAAEW,EAA2B,AAAC1M,IAA+E8I,cAAc,CAAE6D,EAAS,AAAC3M,IAA+E2M,MAAM,CAAEC,EAAO,AAAC5M,IAA+E4M,IAAI,CAAEC,EAAsB,AAAC7M,IAA+E+F,SAAS,CAAE+G,EAAW,AAAC9M,IAA+E8M,QAAQ,CAAEC,EAAQ,AAAC/M,IAA+E+M,KAAK,CAAEC,EAAuB,AAAChN,IAA+E+I,UAAU,CAAEkE,EAAO,AAACjN,IAA+EiN,IAAI,CAAEC,EAAc,AAAClN,IAA+EkN,WAAW,CAAEC,EAAQ,AAACnN,IAA+EmN,KAAK,CAAEC,EAAY,AAACpN,IAA+EoN,SAAS,EAO16C,AAAC,SAAU/O,CAAS,EAYhB,IAmCIgP,EAnCAC,EAAiB,CACb,IACA,sCACA,SACA,4BACA,eACA,0BACA,cACA,oBACA,cACA,WACA,QACH,CAEDC,EAAqB,CACjB,OACA,SACA,gBACA,iBACA,cACA,aACA,IACA,IACH,AACLlP,CAAAA,EAAUmP,eAAe,CAAG,EAAE,CAC9B,IAAIC,EAAmB,CACf,WACA,OACA,OACH,CAoBL,SAASC,EAAU/M,CAAO,EACtB,IAOIwB,EAaAwL,EApBAvN,EAAQ,IAAI,CACZwN,EAAWxN,EAAMwN,QAAQ,CACzBC,EAAad,EAAM3M,EAAMO,OAAO,CAACN,UAAU,CAACkD,aAAa,CACzD5C,GACA8B,EAAUoL,EAAWpL,OAAO,CAC5BJ,EAAYwL,EAAWxL,SAAS,CAChCmB,EAAaqK,EAAWrK,UAAU,EAAI,GAU1C,GARKpD,EAAM0N,QAAQ,EACf1N,CAAAA,EAAM0N,QAAQ,CAAG,CAAA,EAGhB1N,EAAM2H,iBAAiB,GACxB3H,EAAM2H,iBAAiB,CAAG,EAAE,CAC5B3H,EAAM2N,iBAAiB,CAAG,EAAE,EAE5BF,AAAuB,CAAA,IAAvBA,EAAWG,OAAO,EAAeH,EAAWzJ,KAAK,EAGrD,IAAIA,EAAQhE,EAAM6N,UAAU,CAAG,CAAC,EAAIJ,EAAWzJ,KAAK,CAEhD3B,EACAkL,EAAW,SAAUjD,CAAC,EACdA,GACAA,EAAEwD,eAAe,GAErBzL,EAAQ7C,IAAI,CAACQ,EAAOsK,EACxB,EAEKrI,GACLsL,CAAAA,EAAW,SAAUjD,CAAC,EAEdA,GACAA,EAAEwD,eAAe,GAErB9N,EAAM+N,WAAW,CAACC,EAAOlM,aAAa,CAAEG,EAAW+L,EAAOC,UAAU,EAAI,EAAGD,EAAOE,UAAU,EAAI,EAAGF,EAAOpK,KAAK,EAAI,EAAGoK,EAAOvK,MAAM,EAAI,EAAGuK,GAC1IA,EAAOG,QAAQ,CAAC,EACpB,CAAA,EAEAV,EAAW1F,IAAI,EAAI0F,EAAW1L,MAAM,CACpCiC,EAAMoK,WAAW,CAAGvB,EAAK7I,EAAMoK,WAAW,CAAE,IAEtCX,EAAW1F,IAAI,EACrBwE,EAAOvI,EAAO,CACVJ,MAAO6J,EAAW7J,KAAK,CACvBH,OAAQgK,EAAWhK,MAAM,CACzBS,QAAS,CACb,GAEJ,IAAI8J,EAASR,EACJQ,MAAM,CAACP,EAAW1F,IAAI,CAAE,EAAG,EAChCwF,EACAvJ,EACA,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACLyJ,EAAWY,OAAO,EACbC,QAAQ,CAAC/N,EAAQsB,SAAS,EAC1B0M,IAAI,CAAC,CACNC,MAAO3B,EAAK7M,EAAMO,OAAO,CAACyC,IAAI,CAACyK,EAAWgB,SAAS,EAAIhB,EAAWzL,QAAQ,CAAC,CAAE,GACjF,EACJgM,CAAAA,EAAOlM,aAAa,CAAIvB,EAAQuB,aAAa,EACzC,mBAAqB9B,EAAM0N,QAAQ,GACnCD,EAAW1L,MAAM,GACjBA,EAASyL,EACJzL,MAAM,CAAC0L,EAAW1L,MAAM,CAAE2M,KAAKC,KAAK,CAAC,AAAClB,CAAAA,EAAWpK,OAAO,EAAI,CAAA,EAAMD,EAAa,GAAKsL,KAAKC,KAAK,CAAC,AAAClB,CAAAA,EAAWnK,OAAO,EAAI,CAAA,EAAMF,EAAa,GAAKA,EAAYA,EAE7J,CACEQ,MAAOR,EACPK,OAAQL,CACZ,GACKkL,QAAQ,CAAC,4BACTC,IAAI,CAAC,CACNK,OAAQ,CACZ,GACKC,GAAG,CAACb,GACJhO,EAAM6N,UAAU,EACjB9L,EAAOwM,IAAI,CAAC,CACRpK,OAAQsJ,EAAW3J,YAAY,CAC/BG,KAAMwJ,EAAW5J,UAAU,CAC3B,eAAgB4J,EAAW1J,iBAAiB,EAAI,CACpD,IAGRiK,EACKa,GAAG,CAAC7O,EAAM8O,cAAc,EACxBvL,KAAK,CAACgJ,EAAOkB,EAAY,CAC1B7J,MAAOoK,EAAOpK,KAAK,CACnBmB,EAAG8H,EAAKY,EAAW1I,CAAC,CAAE/E,EAAM+O,YAAY,CAC5C,GAAI,CAAA,EAAM,cACV/O,EAAM+O,YAAY,EAAK,AAAC,CAAA,AAACf,CAAAA,EAAOpK,KAAK,EAAI,CAAA,EAAK6J,EAAWjK,aAAa,AAAD,EAChEiK,CAAAA,AAAqB,UAArBA,EAAWlK,KAAK,CAAe,GAAK,CAAA,EACzCvD,EAAM2N,iBAAiB,CAACtN,IAAI,CAAC2N,EAAQjM,GACzC,CAaA,SAASiN,IAEL,GAAKhP,AADO,IAAI,CACLiP,gBAAgB,EAG3B,IAAIlG,EAAK/I,AAJG,IAAI,CAIDiP,gBAAgB,CAC3BC,EAAanG,EAAGmG,UAAU,CAC1BC,EAAcpG,EAAGoG,WAAW,CAC5BC,EAAcrG,EAAGqG,WAAW,CAEhCpP,AATY,IAAI,CASVqP,cAAc,CAACrP,AATT,IAAI,CASWiG,QAAQ,EAEnC,EAAE,CAACvF,OAAO,CAAClB,IAAI,CAAC0P,EAAY,SAAUI,CAAI,CAAElH,CAAC,EACnB,IAAlBkH,EAAKC,QAAQ,EACbD,CAAAA,EAAKE,KAAK,CAACC,OAAO,CAAIN,CAAW,CAAC/G,EAAE,EAAI,EAAE,CAElD,GACApI,AAhBY,IAAI,CAgBV0P,UAAU,CAAG,CAAA,EAEfN,GACApP,AAnBQ,IAAI,CAmBN8G,OAAO,CAAC0B,KAAK,CAnBX,IAAI,CAmBe4G,GAE/B,OAAOpP,AArBK,IAAI,CAqBHiP,gBAAgB,CAC7BhC,EAAgB,KAAK,EACrBR,EAvBY,IAAI,CAuBW,cAC/B,CAWA,SAASkD,IAEL,IADI5G,EAEAuC,EAAOW,EAAIX,IAAI,CACf7J,EAAgBzB,AAFR,IAAI,CAEUO,OAAO,CAACO,SAAS,CAACW,aAAa,CACrDwN,EAAmB,CACfC,WAAY5D,EAAK4D,UAAU,CAC3BC,YAAa,EAAE,CACfC,YAAa,KAAK,CACtB,CACJpP,CARY,IAAI,CAQV0P,UAAU,CAAG,CAAA,EACnB,AAAyB,OAAxB3G,CAAAA,EAAK/I,AATM,IAAI,CASJ4P,OAAO,AAAD,GAAe7G,AAAO,KAAK,IAAZA,GAAyBA,EAAG8G,KAAK,CAAC,KAAK,EAAG,GAC3EpD,EAVY,IAAI,CAUW,eAENhL,GACbzB,AAbI,IAAI,CAaFqH,UAAU,CAAG5F,IAEvBwN,EAAiBG,WAAW,CAAG,CAC3BpP,AAhBI,IAAI,CAgBFO,OAAO,CAACP,KAAK,CAAC4D,KAAK,CACzB,KAAK,EACL,CAAA,EACH,CACD5D,AApBQ,IAAI,CAoBN8G,OAAO,CAACrF,EAAe,KAAK,EAAG,CAAA,IAGzC,EAAE,CAACf,OAAO,CAAClB,IAAI,CAACyP,EAAiBC,UAAU,CAAE,SAAUI,CAAI,CAAElH,CAAC,EACpC,IAAlBkH,EAAKC,QAAQ,GACbN,EAAiBE,WAAW,CAAC/G,EAAE,CAAGkH,EAAKE,KAAK,CAACC,OAAO,CACpDH,EAAKE,KAAK,CAACC,OAAO,CAAG,OAE7B,GAEAzP,AA9BY,IAAI,CA8BVqP,cAAc,CAAC/D,GAErBtL,AAhCY,IAAI,CAgCViP,gBAAgB,CAAGA,CAC7B,CAIA,SAASa,EAAc9P,CAAK,EAExB+P,AADkB/P,EACNgQ,eAAe,GAC3B5D,EAAmBpM,EAAO,SAAU+P,AAFlB/P,EAE8BgQ,eAAe,EAE/D5D,EAAmBpM,EAAO,UAAW+P,AAJnB/P,EAI+BiQ,aAAa,CAmClE,CAqEA,SAASlC,EAAYlM,CAAS,CAAEqO,CAAK,CAAEnL,CAAC,CAAErB,CAAC,CAAEE,CAAK,CAAEH,CAAM,CAAEuK,CAAM,EAG9D,IAFIjF,EACAoH,EASAC,EARApQ,EAAQ,IAAI,CACZqQ,EAAarQ,EAAMO,OAAO,CAACN,UAAU,CACrCoH,EAAarH,EAAMqH,UAAU,CAC7BC,EAActH,EAAMsH,WAAW,CAC/BgJ,EAAY,SAAWzO,EAEvB0O,EAAc7B,KAAK8B,GAAG,CAAC5M,EACvBH,GAEAqB,EAAO9E,CAAK,CAACsQ,EAAU,CAEtBxL,IAED9E,EAAMyQ,iBAAiB,CAAGzQ,CAAK,CAACsQ,EAAU,CAAGxL,EACzC6G,EAAc,MAAO,CACjB9J,UAAWA,CACf,EAAGkK,EAAiB,CAAE2E,SAAU,WAAY9B,OAAQ,IAAM1K,QAASqM,EAAc,KAAMI,cAAe,MAAO,EAAG3Q,EAAMwN,QAAQ,CAACgC,KAAK,EAAG,AAAC,CAAA,AAAoC,OAAnCzG,CAAAA,EAAK/I,EAAM4Q,kBAAkB,AAAD,GAAe7H,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8H,QAAQ,AAAD,GAAM7Q,EAAMgG,SAAS,EAChPoK,EAAYzE,EAAc,KAAM,CAAE9J,UAAW,iBAAkB,EAAG7B,EAAM6N,UAAU,CAAG,CAAC,EAAI,CACtFiD,UAAW,OACXC,OAAQ,EACR7M,QAAS,CACb,EAAGY,GAEE9E,EAAM6N,UAAU,EACjBxB,EAAI+D,EAAW7D,EAAO,CAClByE,aAAc,oBACdC,gBAAiB,oBACjBC,UAAW,mBACf,EAAGb,EAAWjM,SAAS,GAG3BU,EAAKqM,QAAQ,CAAG,WACZ9E,EAAIvH,EAAM,CAAE2K,QAAS,MAAO,GACxBzB,GACAA,EAAOG,QAAQ,CAAC,GAEpBnO,EAAMoR,QAAQ,CAAG,CAAA,EAEjB/E,EAAIrM,EAAMiG,QAAQ,CAAE,CAAEoL,SAAU,QAAS,GACzChF,EAAIrM,EAAMgG,SAAS,CAAE,CAAEqL,SAAU,QAAS,GAC1CzR,IAA8E0R,YAAY,CAACxM,EAAKyM,SAAS,EACzG9E,EAAoBzM,EAAO,mBAC/B,EAEAA,EAAMwR,YAAY,CAACnR,IAAI,CAAC+L,EAAmBtH,EAAM,aAAc,WAC3DA,EAAKyM,SAAS,CAAGpF,EAAcsF,UAAU,CAAC3M,EAAKqM,QAAQ,CAAE,IAC7D,GAAI/E,EAAmBtH,EAAM,aAAc,WACvClF,IAA8E0R,YAAY,CAACxM,EAAKyM,SAAS,CAC7G,GAGAnF,EAAmBH,EAAK,UAAW,SAAU3B,CAAC,EAC1C,IAAIvB,EACE,CAAA,AAAyB,OAAxBA,CAAAA,EAAK/I,EAAM4P,OAAO,AAAD,GAAe7G,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG2I,OAAO,CAACpH,EAAEqH,MAAM,CAAE9P,EAAS,GAC1FiD,EAAKqM,QAAQ,EAErB,GAAI/E,EAAmBtH,EAAM,QAAS,WAC9B9E,EAAMoR,QAAQ,EACdtM,EAAKqM,QAAQ,EAErB,IAEAjB,EAAMxP,OAAO,CAAC,SAAUkR,CAAI,EAKxB,GAJoB,UAAhB,OAAOA,GACPA,CAAAA,EAAO5R,EAAMO,OAAO,CAACO,SAAS,CACzBoB,mBAAmB,CAAC0P,EAAK,AAAD,EAE7BlF,EAASkF,EAAM,CAAA,GAAO,CACtB,IAAIC,EAAU,KAAK,CACfD,CAAAA,EAAKlP,SAAS,CACdmP,EAAUlG,EAAc,KAAM,KAAK,EAAG,KAAK,EAAGyE,IAKzB,aAAjBwB,EAAKxP,OAAO,EACZpC,EAAM8R,kBAAkB,EACxBF,CAAAA,EAAKxP,OAAO,CAAG,UAAS,EAE5ByP,EAAUlG,EAAc,KAAM,CAC1B9J,UAAW,uBACXQ,QAAS,SAAUiI,CAAC,EACZA,GACAA,EAAEwD,eAAe,GAErBhJ,EAAKqM,QAAQ,GACO,UAAhB,OAAOS,GAAqBA,EAAKvP,OAAO,EACxCuP,EAAKvP,OAAO,CAACmG,KAAK,CAACxI,EAAOqI,UAElC,CACJ,EAAG,KAAK,EAAG+H,GACXtQ,IAA8FgI,cAAc,CAAC+J,EAASD,EAAK7J,IAAI,EAC3H/H,EAAMO,OAAO,CAACyC,IAAI,CAAC4O,EAAKxP,OAAO,CAAC,EAC/BpC,EAAM6N,UAAU,GACjBgE,EAAQE,WAAW,CAAG,WAClB1F,EAAI,IAAI,CAAEgE,EAAWzL,kBAAkB,CAC3C,EACAiN,EAAQG,UAAU,CAAG,WACjB3F,EAAI,IAAI,CAAEgE,EAAW7L,aAAa,CACtC,EACA6H,EAAIwF,EAAStF,EAAO,CAChB0F,OAAQ,SACZ,EAAG5B,EAAW7L,aAAa,EAAI,CAAC,MAIxCxE,EAAM2H,iBAAiB,CAACtH,IAAI,CAACwR,EACjC,CACJ,GAGA7R,EAAM2H,iBAAiB,CAACtH,IAAI,CAAC+P,EAAWtL,GACxC9E,EAAMkS,eAAe,CAAGpN,EAAKqN,WAAW,CACxCnS,EAAMoS,gBAAgB,CAAGtN,EAAKuN,YAAY,EAE9C,IAAIjO,EAAY,CAAEqL,QAAS,OAAQ,CAE/B1K,CAAAA,EAAK/E,CAAAA,EAAMkS,eAAe,EAAI,CAAA,EAAK7K,EACnCjD,EAAUkO,KAAK,CAAG,AAACjL,EAAatC,EAAInB,EAAQ2M,EAAe,KAG3DnM,EAAUmO,IAAI,CAAG,AAACxN,EAAIwL,EAAe,KAGrC7M,EAAID,EAAUzD,CAAAA,EAAMoS,gBAAgB,EAAI,CAAA,EAAK9K,GAC7C,AAAC,CAAA,AAA+B,OAA9B6I,CAAAA,EAAKnC,EAAOwE,YAAY,AAAD,GAAerC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGxM,aAAa,AAAD,IAAO,MACvFS,EAAUqO,MAAM,CAAG,AAACnL,EAAc5D,EAAI6M,EAAe,KAGrDnM,EAAUsO,GAAG,CAAG,AAAChP,EAAID,EAAS8M,EAAe,KAEjDlE,EAAIvH,EAAMV,GAEViI,EAAIrM,EAAMiG,QAAQ,CAAE,CAAEoL,SAAU,EAAG,GACnChF,EAAIrM,EAAMgG,SAAS,CAAE,CAAEqL,SAAU,EAAG,GACpCrR,EAAMoR,QAAQ,CAAG,CAAA,EACjB3E,EAAoBzM,EAAO,kBAC/B,CAQA,SAASiQ,EAAc3F,CAAC,EACpB,IAIIgG,EAJAtQ,EAAQsK,EAAIA,EAAEqH,MAAM,CAAG,IAAI,CAC3BhE,EAAoB3N,EAAM2N,iBAAiB,CAC3ChG,EAAoB3H,EAAM2H,iBAAiB,CAC3C6J,EAAexR,EAAMwR,YAAY,CAGjC7D,IACAA,EAAkBjN,OAAO,CAAC,SAAUiS,CAAI,CAAEvK,CAAC,EAEnCuK,IACAA,EAAKtQ,OAAO,CAAGsQ,EAAKC,YAAY,CAAG,KAE/B5S,CAAK,CADTsQ,EAAY,SAAWqC,EAAK7Q,aAAa,CACrB,EAChB,OAAO9B,CAAK,CAACsQ,EAAU,CAE3B3C,CAAiB,CAACvF,EAAE,CAAGuK,EAAKE,OAAO,GAE3C,GACAlF,EAAkBrF,MAAM,CAAG,GAG3BtI,EAAM8O,cAAc,GACpB9O,EAAM8O,cAAc,CAAC+D,OAAO,GAC5B,OAAO7S,EAAM8O,cAAc,EAG3BnH,IACAA,EAAkBjH,OAAO,CAAC,SAAUiS,CAAI,CAAEvK,CAAC,EACnCuK,IAEA/S,IAA8E0R,YAAY,CAACqB,EAAKpB,SAAS,EACzGzE,EAAY6F,EAAM,cAGlBhL,CAAiB,CAACS,EAAE,CAChBuK,EAAKX,UAAU,CACXW,EAAKZ,WAAW,CACZY,EAAKC,YAAY,CACbD,EAAKtQ,OAAO,CAAG,KAE/BiK,EAAyBqG,GAEjC,GACAhL,EAAkBW,MAAM,CAAG,GAE3BkJ,IACAA,EAAa9Q,OAAO,CAAC,SAAUoS,CAAM,EACjCA,GACJ,GACAtB,EAAalJ,MAAM,CAAG,EAE9B,CA2BA,SAAS1F,EAAYgF,CAAgB,CAAEmL,CAAY,EAC/C,IAAIC,EAAM,IAAI,CAACC,eAAe,CAACrL,EAC3BmL,GAEJnL,EAAmB+E,EAAM,IAAI,CAACpM,OAAO,CAACO,SAAS,CAAE8G,GAEjDsL,AA7pB+CtK,EA6pB5BiC,IAAI,CAACjD,EAAiB3G,GAAG,CAAE,CAC1CkK,SAAUvD,EAAiBuD,QAAQ,CAC/BvD,EAAiBuD,QAAQ,CAACgI,OAAO,CAAC,MAAO,KACzC,IAAI,CAACC,WAAW,GACpBpS,KAAM4G,EAAiB5G,IAAI,CAC3B4C,MAAOgE,EAAiBhE,KAAK,CAC7BlC,MAAOkG,EAAiBlG,KAAK,CAC7BsR,IAAKA,CACT,EAAGpL,EAAiBkD,YAAY,CACpC,CAcA,SAASuI,EAAaC,CAAgB,EAKlC,OAJIA,GACA,IAAI,CAACC,YAAY,GAErB,IAAI,CAACC,mBAAmB,GACjB,IAAI,CAACxN,SAAS,CAACyN,SAAS,AACnC,CAWA,SAASL,IACL,IAAIjL,EAAI,IAAI,CAACuL,WAAW,CAAClF,KAAK,EAAI,IAAI,CAACkF,WAAW,CAAClF,KAAK,CAACzG,IAAI,CACzDoD,EAAW,IAAI,CAAC5K,OAAO,CAACO,SAAS,CAACqK,QAAQ,QAC9C,AAAIA,EACOA,EAASgI,OAAO,CAAC,MAAO,MAElB,UAAb,OAAOhL,GACPgD,CAAAA,EAAWhD,EACNwL,WAAW,GACXR,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,UAAW,KACnBA,OAAO,CAAC,cAAe,IACvBA,OAAO,CAAC,UAAW,IACnBA,OAAO,CAAC,SAAU,KAClBS,MAAM,CAAC,EAAG,IACVT,OAAO,CAAC,UAAW,GAAE,EAE1B,CAAA,CAAChI,GAAYA,EAAS7C,MAAM,CAAG,CAAA,GAC/B6C,CAAAA,EAAW,OAAM,EAEdA,EACX,CAsBA,SAAS0I,EAAOd,CAAY,EAGxB,IAFIhK,EAEAiK,EACAc,EAEAvT,EAAUoM,EAAM3M,AAJR,IAAI,CAIUO,OAAO,CAC7BwS,EAEJxS,CAAAA,EAAQwT,WAAW,CAAGpH,EAAM3M,AAPhB,IAAI,CAOkB0T,WAAW,CAACK,WAAW,CAAEhB,GAAgBA,EAAagB,WAAW,EAGnGxT,EAAQyT,IAAI,CAAGrH,EAAM3M,AAVT,IAAI,CAUW0T,WAAW,CAACM,IAAI,CAAEjB,GAAgBA,EAAaiB,IAAI,EAE9E,IAAIC,EAAUtI,EAAc,MACxB,KAAM,CACF+E,SAAU,WACVgC,IAAK,UACL9O,MAAO5D,AAhBH,IAAI,CAgBKqH,UAAU,CAAG,KAC1B5D,OAAQzD,AAjBJ,IAAI,CAiBMsH,WAAW,CAAG,IAChC,EACA2E,EAAIX,IAAI,EAER4I,EAAWlU,AArBH,IAAI,CAqBKiG,QAAQ,CAACuJ,KAAK,CAAC5L,KAAK,CAAEuQ,EAAYnU,AArB3C,IAAI,CAqB6CiG,QAAQ,CAACuJ,KAAK,CAAC/L,MAAM,CAAE2Q,EAAc7T,EAAQO,SAAS,CAACsT,WAAW,EACvH7T,EAAQP,KAAK,CAAC4D,KAAK,EAClB,MAAMyQ,IAAI,CAACH,IAAaI,SAASJ,EAAU,KAC3C3T,CAAAA,EAAQgU,OAAO,CAAG,IAAM,GAAE,EAAIC,EAAejU,EAAQO,SAAS,CAAC0T,YAAY,EAC5EjU,EAAQP,KAAK,CAACyD,MAAM,EACnB,MAAM4Q,IAAI,CAACF,IAAcG,SAASH,EAAW,KAC9C,IAER5H,EAAOhM,EAAQP,KAAK,CAAE,CAClByU,UAAW,CAAA,EACXxO,SAAUgO,EACVS,UAAW,CAAA,EACXlH,SAAU,cACV5J,MAAOwQ,EACP3Q,OAAQ+Q,CACZ,GACAjU,EAAQO,SAAS,CAAC8M,OAAO,CAAG,CAAA,EAC5B,OAAOrN,EAAQkK,IAAI,CAEnBlK,EAAQoU,MAAM,CAAG,EAAE,CACnB3U,AAzCY,IAAI,CAyCV2U,MAAM,CAACjU,OAAO,CAAC,SAAUkU,CAAK,EAQ3Bd,AAPLA,CAAAA,EAAgBnH,EAAMiI,EAAMlB,WAAW,CAAE,CACrCe,UAAW,CAAA,EACXI,oBAAqB,CAAA,EACrBC,aAAc,CAAA,EACdC,QAASH,EAAMG,OAAO,AAC1B,EAAC,EAEkBC,UAAU,EACzBzU,EAAQoU,MAAM,CAACtU,IAAI,CAACyT,EAE5B,GACA,IAAImB,EAAQ,CAAC,EACbjV,AAtDY,IAAI,CAsDVkV,IAAI,CAACxU,OAAO,CAAC,SAAUyU,CAAI,EAExBA,EAAKzB,WAAW,CAAC0B,WAAW,EAC7BD,CAAAA,EAAKzB,WAAW,CAAC0B,WAAW,CAAGpI,GAAU,EAExCmI,EAAK5U,OAAO,CAACyU,UAAU,GACnBC,CAAK,CAACE,EAAKE,IAAI,CAAC,GACjBJ,CAAK,CAACE,EAAKE,IAAI,CAAC,CAAG,CAAA,EACnB9U,CAAO,CAAC4U,EAAKE,IAAI,CAAC,CAAG,EAAE,EAE3B9U,CAAO,CAAC4U,EAAKE,IAAI,CAAC,CAAChV,IAAI,CAACsM,EAAMwI,EAAKzB,WAAW,CAAE,CAC5CqB,QAASI,EAAKJ,OAAO,CAGrB/T,KAAMmU,EAAKnU,IAAI,CACfsU,YAAaH,EAAKG,WAAW,AACjC,IAER,GAIA/U,EAAQgV,SAAS,CAAGvV,AA5ER,IAAI,CA4EU0T,WAAW,CAAC6B,SAAS,CAE/C,IAAIC,EAAY,IAAIxV,AA9ER,IAAI,CA8EUyV,WAAW,CAAClV,EAClCP,AA/EQ,IAAI,CA+ENuN,QAAQ,EA8ClB,OA5CIwF,GACA,CAAC,QAAS,QAAS,SAAS,CAACrS,OAAO,CAAC,SAAU2U,CAAI,EAC/C,IAAIK,EAAc,CAAC,CACf3C,CAAAA,CAAY,CAACsC,EAAK,GAClBK,CAAW,CAACL,EAAK,CAAGtC,CAAY,CAACsC,EAAK,CACtCG,EAAUlV,MAAM,CAACoV,GAEzB,GAGJ1V,AA3FY,IAAI,CA2FVkV,IAAI,CAACxU,OAAO,CAAC,SAAUyU,CAAI,EAC7B,IAAIQ,EAAWnJ,EAAKgJ,EAAUN,IAAI,CAC9B,SAAUU,CAAI,EACV,OAAOA,EAAKrV,OAAO,CAAC6U,WAAW,GAAKD,EAAKzB,WAAW,CAAC0B,WAAW,AACxE,GACA,GAAIO,EAAU,CACV,IAAIE,EAAWV,EAAKW,WAAW,GAK3BC,EAAiBhJ,EAAM,AAACgG,CAAAA,MAAAA,EAAmD,KAAK,EAAIA,CAAY,CAACoC,EAAKE,IAAI,CAAC,AAAD,GAAM,CAAC,EAAE,CAAC,EAAE,CACtHW,EAAU,QAASD,EACfA,EAAeE,GAAG,CAClBJ,EAASG,OAAO,CACpBE,EAAU,QAASH,EACfA,EAAevF,GAAG,CAClBqF,EAASK,OAAO,CACnB,CAAA,AAAoB,KAAA,IAAZF,GACTA,IAAYL,EAASM,GAAG,EAAM,AAAmB,KAAA,IAAZC,GACrCA,IAAYP,EAASnF,GAAG,GACxBmF,EAASQ,WAAW,CAACH,MAAAA,EAAyCA,EAAU,KAAK,EAAGE,MAAAA,EAAyCA,EAAU,KAAK,EAAG,CAAA,EAAM,CAAA,EAEzJ,CACJ,GAEAlD,EAAMwC,EAAUnC,YAAY,CAACrT,AArHjB,IAAI,CAqHmB6N,UAAU,EACxC,CAAA,AAA6B,OAA5B9E,CAAAA,EAAKxI,EAAQO,SAAS,AAAD,GAAeiI,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuK,gBAAgB,AAAD,GACrF7G,EAAoB,IAAI,CAAE,SAAU,CAAE+I,UAAWA,CAAU,GAC3DxC,EAAMhT,AAxHM,IAAI,CAwHJoW,WAAW,CAACpD,EAAKzS,GAE7BA,EAAU,KACViV,EAAU3C,OAAO,GACjBvG,EAAyB2H,GAClBjB,CACX,CAKA,SAASC,EAAgB1S,CAAO,CAAEwS,CAAY,EAC1C,IAAIsD,EAAwB,IAAI,CAAC9V,OAAO,CAACO,SAAS,CAClD,OAAO,IAAI,CAAC+S,MAAM,CAAClH,EAAM,CAAE3M,MAAO,CAAEsE,aAAc,CAAE,CAAE,EAAG+R,EAAsBtD,YAAY,CAAEA,EAAc,CACvGjS,UAAW,CACPsT,YAAc,AAAC7T,GAAWA,EAAQ6T,WAAW,EACzCiC,EAAsBjC,WAAW,CACrCI,aAAe,AAACjU,GAAWA,EAAQiU,YAAY,EAC3C6B,EAAsB7B,YAAY,AAC1C,CACJ,GACJ,CA2BA,SAASjB,IACL,IAGI+C,EAFAC,EAAYtY,EAAUmP,eAAe,CACrCoJ,EAAgB,CAAC,EAIjBC,EAASxK,EAAIN,aAAa,CAAC,UAC/BU,EAAIoK,EAAQ,CACR7S,MAAO,MACPH,OAAQ,MACRiT,WAAY,QAChB,GACAzK,EAAIX,IAAI,CAACqL,WAAW,CAACF,GACrB,IAAIG,EAAaH,EAAOI,aAAa,EAAIJ,EAAOI,aAAa,CAACnL,QAAQ,CAClEkL,GACAA,EAAUtL,IAAI,CAACqL,WAAW,CAACC,EAAUE,eAAe,CAAC5K,EAAQ,SAgJjE6K,AAxIA,SAASA,EAAQzH,CAAI,EACjB,IACI0H,EACAC,EACAC,EACAC,EACAC,EACAhP,EANAiP,EAAiB,CAAC,EA6DtB,GAAIT,GACAtH,AAAkB,IAAlBA,EAAKC,QAAQ,EACblC,AAA4C,KAA5CA,EAAiBhI,OAAO,CAACiK,EAAKgI,QAAQ,EAAU,CAOhD,GANAN,EAAS7K,EAAcoL,gBAAgB,CAACjI,EAAM,MAC9C2H,EAAe3H,AAAkB,QAAlBA,EAAKgI,QAAQ,CACxB,CAAC,EACDnL,EAAcoL,gBAAgB,CAACjI,EAAKkI,UAAU,CAAE,MAGhD,CAAChB,CAAa,CAAClH,EAAKgI,QAAQ,CAAC,CAAE,CAQ/BhB,EAAWM,EAAUa,oBAAoB,CAAC,MAAM,CAAC,EAAE,CACnDP,EAAQN,EAAUE,eAAe,CAACxH,EAAKoI,YAAY,CAAEpI,EAAKgI,QAAQ,EAClEhB,EAASK,WAAW,CAACO,GAGrB,IAAI/O,EAAIgE,EAAcoL,gBAAgB,CAACL,EACnC,MACAS,EAAW,CAAC,EAChB,IAAK,IAAI7Y,KAAOqJ,EACRrJ,EAAIwJ,MAAM,CAAG,KACb,AAAkB,UAAlB,OAAOH,CAAC,CAACrJ,EAAI,EACb,CAAC,QAAQuV,IAAI,CAACvV,IACd6Y,CAAAA,CAAQ,CAAC7Y,EAAI,CAAGqJ,CAAC,CAACrJ,EAAI,AAAD,CAG7B0X,CAAAA,CAAa,CAAClH,EAAKgI,QAAQ,CAAC,CAAGK,EAGT,SAAlBrI,EAAKgI,QAAQ,EACb,OAAOd,EAAczO,IAAI,CAAC9D,IAAI,CAElCqS,EAASsB,WAAW,CAACV,EACzB,CAEA,IAAK,IAAI3O,KAAKyO,EAGV,CAAA,AAACpX,IAA+EiY,SAAS,EACrF,AAACjY,IAA+EkY,IAAI,EACpF,AAAClY,IAA+EmY,QAAQ,EAExF/Y,OAAOO,cAAc,CAACC,IAAI,CAACwX,EAAQzO,EAAC,GACpCyP,AA9FZ,SAAsBnO,CAAG,CAAExK,CAAI,EAG3B,GADA8X,EAAaC,EAAc,CAAA,EACvBb,EAAUjO,MAAM,CAAE,CAIlB,IADAF,EAAImO,EAAUjO,MAAM,CACbF,KAAO,CAACgP,GACXA,EAAcb,CAAS,CAACnO,EAAE,CAACiM,IAAI,CAAChV,GAEpC8X,EAAa,CAACC,CAClB,CAMA,IAJa,cAAT/X,GAAwBwK,AAAQ,SAARA,GACxBsN,CAAAA,EAAa,CAAA,CAAG,EAEpB/O,EAAI6P,AAxDG/K,EAwDM5E,MAAM,CACZF,KAAO,CAAC+O,GAAY,CACvB,GAAI9X,EAAKiJ,MAAM,CAAG,IACd,MAAM,AAAIiC,MAAM,kBAEpB4M,EAAcc,AA7DX/K,CA6DmB,CAAC9E,EAAE,CAACiM,IAAI,CAAChV,IAC3B,AAAe,YAAf,OAAOwK,CACf,CACI,CAACsN,GAIIF,CAAAA,CAAY,CAAC5X,EAAK,GAAKwK,GACxByF,AAAkB,QAAlBA,EAAKgI,QAAQ,AAAS,GACtBd,CAAa,CAAClH,EAAKgI,QAAQ,CAAC,CAACjY,EAAK,GAAKwK,IAEnC,AAACsD,GACDA,AAAqC,KAArCA,EAAmB9H,OAAO,CAAChG,GAO3BgY,CAAc,CAAChY,EAAK,CAAGwK,EANnBA,GACAyF,EAAK4I,YAAY,CA7FlC7Y,AA6F6CA,EA7FxC8T,OAAO,CAAC,SAAU,SAAUgF,CAAK,EACzC,MAAO,IAAMA,EAAMxE,WAAW,EAClC,GA2F2D9J,GASvD,EAkDyBmN,CAAM,CAACzO,EAAE,CAAEA,GAShC,GALA8D,EAAIiD,EAAM+H,GAEY,QAAlB/H,EAAKgI,QAAQ,EACbhI,EAAK4I,YAAY,CAAC,eAAgB,OAElC5I,AAAkB,SAAlBA,EAAKgI,QAAQ,CACb,OAGJ,EAAE,CAAC5W,OAAO,CAAClB,IAAI,CAAC8P,EAAK8I,QAAQ,EAAI9I,EAAKJ,UAAU,CAAE6H,EACtD,CACJ,EAUQ,IAAI,CAAC/Q,SAAS,CAACqS,aAAa,CAAC,QAJjC/B,EAASkB,UAAU,CAACI,WAAW,CAACtB,GAEhCG,EAAOe,UAAU,CAACI,WAAW,CAACnB,EAItC,CAIA,SAASjD,IACL,IAAI8E,EAAc,IAAI,CAACtS,SAAS,CAACuS,gBAAgB,CAAC,KAAMC,EAAkB,CAAC,QAAS,OAAQ,aAAc,SAAS,CACnHC,MAAMC,IAAI,CAACJ,GAAa5X,OAAO,CAAC,SAAUmR,CAAO,EAC7C2G,EAAgB9X,OAAO,CAAC,SAAU6N,CAAI,EAClC,IAAIoK,EAAY9G,EAAQ+G,YAAY,CAACrK,GACjCoK,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUE,QAAQ,CAAC,OAAM,GAC/EhH,EAAQqG,YAAY,CAAC3J,EAAMgJ,iBAAiB1F,GAASiH,gBAAgB,CAACvK,GAE9E,EACJ,EACJ,CAWA,SAASc,EAAe0J,CAAM,EAC1B,IAAInI,EAAqB,IAAI,CAACA,kBAAkB,CAChD,AAEAA,CAAAA,EACI,CACIA,EAAmBC,QAAQ,CAC3BD,EAAmBoI,kBAAkB,CACxC,CACD,CAAC,IAAI,CAAChT,SAAS,CAAC,AAAD,EAAGtF,OAAO,CAAC,SAAUuY,CAAG,EACvCF,EAAOpC,WAAW,CAACsC,EACvB,EACJ,CAQA,SAASC,IACL,IAAIlZ,EAAQ,IAAI,CAUZM,EAAS,SAAUjB,CAAI,CACvBkB,CAAO,CACPC,CAAM,EACFR,EAAMmZ,gBAAgB,CAAG,CAAA,EAC7BxM,EAAM,CAAA,EAAM3M,EAAMO,OAAO,CAAClB,EAAK,CAAEkB,GAC7BsM,EAAKrM,EAAQ,CAAA,IACbR,EAAMQ,MAAM,EAEpB,CACAR,CAAAA,EAAMc,SAAS,CAAG,CACdR,OAAQ,SAAUC,CAAO,CAAEC,CAAM,EAC7BF,EAAO,YAAaC,EAASC,EACjC,CACJ,EAIAG,EACKZ,OAAO,CAACC,GAAOC,UAAU,CACzBE,SAAS,CAAC,SAAUI,CAAO,CAAEC,CAAM,EACpCF,EAAO,aAAcC,EAASC,EAClC,EACJ,CAMA,SAAS4Y,EAAmBrQ,CAAE,EAK1B,IAJIoH,EACAkJ,EACAC,EACAC,EACAC,EAAUzQ,EAAGyQ,OAAO,CACpB1a,EAAMiK,EAAGjK,GAAG,CACZ2a,EAAe1Q,EAAG0Q,YAAY,CAC9B7R,EAAmB,IAAI,CAACrH,OAAO,CAACO,SAAS,CACzC4Y,EAAK/M,EAAM,AAAmC,OAAlCwD,CAAAA,EAAK,IAAI,CAAC5P,OAAO,CAACN,UAAU,AAAD,GAAekQ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGhN,aAAa,CAAE,AAAwG,OAAvGkW,CAAAA,EAAKzR,MAAAA,EAA2D,KAAK,EAAIA,EAAiBjG,OAAO,AAAD,GAAe0X,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGzX,aAAa,EAC1P2B,EAAQmW,EAAGnW,KAAK,CAChBoW,EAAKD,EAAGlW,aAAa,CAErBG,EAAgB+V,EAAG/V,aAAa,CAChCiW,EAAKF,EAAG9V,KAAK,CAEbiW,EAAQL,EAAQ5V,KAAK,CAAG6V,EACxBK,EAAclW,AAFNgW,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAHbD,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAMpC,CAAA,AAAwG,OAAvGL,CAAAA,EAAK1R,MAAAA,EAA2D,KAAK,EAAIA,EAAiBgG,OAAO,AAAD,GAAe0L,AAAO,KAAK,IAAZA,GAAgBA,CAAQ,GACzIxa,AAAQ,UAARA,GACAyE,AAAU,UAAVA,GACAI,AAAkB,QAAlBA,GACIkW,EAAQ,EAAIC,IACRD,EAAQC,EACRN,EAAQ5V,KAAK,EAAIkW,EAEX,CAAA,AAAsB,OAArBP,CAAAA,EAAK,IAAI,CAAC/K,KAAK,AAAD,GAAe+K,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGQ,UAAU,AAAD,IAAO,QAChFP,CAAAA,EAAQzU,CAAC,EAAI+U,EAAcD,EAAQ,CAAA,EAInD,CAkBA,SAASpX,IACL,IAAIzC,EAAQ,IAAI,EACZA,EAAM0P,UAAU,GAGpBzC,EAAgBjN,EACX,AAACJ,IAA+EmY,QAAQ,EACzF/X,EAAM2P,WAAW,GAIrB8B,WAAW,WACPtF,EAAc6N,KAAK,GACnB7N,EAAc1J,KAAK,GAEd,AAAC7C,IAA+EmY,QAAQ,EACzFtG,WAAW,WACPzR,EAAMgP,UAAU,EACpB,EAAG,IAEX,EAAG,GACP,CAOA,SAASgB,IACL,IAAIhQ,EAAQ,IAAI,CACZ4H,EAAmB5H,EAAMO,OAAO,CAACO,SAAS,CAC1Ca,EAAUiG,EAAiBjG,OAAO,CAClCsY,EAAUja,EAAMmZ,gBAAgB,EAAI,CAACnZ,EAAM2N,iBAAiB,AAChE3N,CAAAA,EAAM+O,YAAY,CAAG,EACjB/O,EAAMmZ,gBAAgB,EACtBnZ,EAAMiQ,aAAa,GAEnBgK,GAAWrS,AAA6B,CAAA,IAA7BA,EAAiBgG,OAAO,GACnC5N,EAAMwR,YAAY,CAAG,EAAE,CACvBxR,EAAM8O,cAAc,CAAG9O,EAAM8O,cAAc,EACvC9O,EAAMwN,QAAQ,CAAC0M,CAAC,CAAC,mBAAmB3L,IAAI,CAAC,CACrCK,OAAQ,CACZ,GAAGC,GAAG,GACVjC,EAAqBjL,EAAS,SAAUqM,CAAM,EAC1ChO,EAAMsN,SAAS,CAACU,EACpB,GACAhO,EAAMmZ,gBAAgB,CAAG,CAAA,EAEjC,CAgBA,SAAS/C,EAAYpD,CAAG,CAAEzS,CAAO,EAE7B,IADIwI,EACAoR,EAAQnH,EAAI3N,OAAO,CAAC,UAAY,EAAG+U,EAAmBpH,EAAI3N,OAAO,CAAC,kBAAoB,GACtFgV,EAAOrH,EAAIY,MAAM,CAACuG,GA+BtB,OA7BAnH,EAAMA,EAAIY,MAAM,CAAC,EAAGuG,GAChBC,EAEApH,EAAMA,EAAIG,OAAO,CAAC,2BAA4B,SAGzCkH,GAAS,CAAA,AAA+E,OAA9EtR,CAAAA,EAAKxI,MAAAA,EAAyC,KAAK,EAAIA,EAAQO,SAAS,AAAD,GAAeiI,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuR,SAAS,AAAD,IACzID,EAAO,qCACS9Z,EAAQP,KAAK,CAAC4D,KAAK,CAD5B,aAEUrD,EAAQP,KAAK,CAACyD,MAAM,CAF9B,gDAKH4W,EAAKlH,OAAO,CAAC,2BAA4B,SALtC,0BAQPH,EAAMA,EAAIG,OAAO,CAAC,SAAUkH,EAAO,WAEvCrH,EAAMA,EACDG,OAAO,CAAC,kBAAmB,IAC3BA,OAAO,CAAC,sBAAuB,IAC/BA,OAAO,CAAC,qBAAsB,IAC9BA,OAAO,CAAC,uCAAwC,WAChDA,OAAO,CAAC,eAAgB,SACxBA,OAAO,CAAC,QAAS,oDACjBA,OAAO,CAAC,oBAAqB,gBAC7BA,OAAO,CAAC,OAAQ,KAEhBA,OAAO,CAAC,UAAW,QACnBA,OAAO,CAAC,SAAU,OAE3B,CAp4BAlV,EAAU8B,OAAO,CA7CjB,SAAiByG,CAAU,CAAEpB,CAAgB,EACzCI,EAA2BzF,OAAO,CAACqF,GACnCmV,AAxmBiDzU,EAwmB5B/F,OAAO,CAACyG,GAC7B,IAAIgU,EAAahU,EAAWlH,SAAS,AAChCkb,CAAAA,EAAW5X,WAAW,GACvB4X,EAAWxL,UAAU,CAAGA,EACxBwL,EAAW5X,WAAW,CAAGA,EACzB4X,EAAWjH,YAAY,CAAGA,EAC1BiH,EAAW/X,KAAK,CAAGA,EACnB+X,EAAWpE,WAAW,CAAGA,EACzBoE,EAAWnH,YAAY,CAAGA,EAC1BmH,EAAW3G,MAAM,CAAGA,EACpB2G,EAAWvH,eAAe,CAAGA,EAC7BuH,EAAWpH,WAAW,CAAGA,EACzBoH,EAAWnL,cAAc,CAAGA,EAC5BmL,EAAW7K,WAAW,CAAGA,EACzB6K,EAAWzM,WAAW,CAAGA,EACzByM,EAAWlN,SAAS,CAAGA,EACvBkN,EAAWvK,aAAa,CAAGA,EAC3BuK,EAAWxK,eAAe,CAAGA,EAC7BwK,EAAWhH,mBAAmB,CAAGA,EACjCgH,EAAWC,SAAS,CAACpa,IAAI,CAACyP,GAC1B1D,EAAmB5F,EAAY,OAAQ0S,GACvC9M,EAAmB5F,EAAY,cAAe4S,GAC1C,AAACxZ,IAA+EmY,QAAQ,EACxF5L,EAAcuO,UAAU,CAAC,SAASC,WAAW,CAAC,SAAUC,CAAQ,EACvD3N,IAGD2N,EAASC,OAAO,CAChB5N,EAAc0C,WAAW,GAGzB1C,EAAc+B,UAAU,GAEhC,GAEJhD,EAAelL,SAAS,CAAG6L,EAAM9L,EAA4BC,SAAS,CAAEkL,EAAelL,SAAS,EAChGkL,EAAehJ,IAAI,CAAG2J,EAAM9L,EAA4BmC,IAAI,CAAEgJ,EAAehJ,IAAI,EAIjFgJ,EAAe/L,UAAU,CAAG0M,EAAM9L,EAA4BZ,UAAU,CAAE+L,EAAe/L,UAAU,EAE3G,CAs4BJ,EAAGhC,GAAcA,CAAAA,EAAY,CAAC,CAAA,GAMD,IAAI6c,EAAuB7c,EAgHpD8c,EAAKnb,GACTmb,CAAAA,EAAEnS,aAAa,CAAGmS,EAAEnS,aAAa,EA55CsBA,EA65CvDmS,EAAElS,IAAI,CAAGkS,EAAEnS,aAAa,CAACC,IAAI,CAC7BkS,EAAEnQ,OAAO,CAAGmQ,EAAEnS,aAAa,CAACgC,OAAO,CACnCmQ,EAAElQ,IAAI,CAAGkQ,EAAEnS,aAAa,CAACiC,IAAI,CAC7BiQ,EAAoB/a,OAAO,CAACgb,EAAEC,KAAK,CAAED,EAAEE,QAAQ,EAClB,IAAIvb,EAAkBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,GAET"}