{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/xrange\n * @requires highcharts\n *\n * X-range series\n *\n * (c) 2010-2025 <PERSON><PERSON>, Lars <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/xrange\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Color\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/xrange\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ xrange_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/XRange/XRangeSeriesDefaults.js\n/* *\n *\n *  X-range series module\n *\n *  (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar correctFloat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).correctFloat, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * The X-range series displays ranges on the X axis, typically time\n * intervals with a start and end date.\n *\n * @sample {highcharts} highcharts/demo/x-range/\n *         X-range\n * @sample {highcharts} highcharts/css/x-range/\n *         Styled mode X-range\n * @sample {highcharts} highcharts/chart/inverted-xrange/\n *         Inverted X-range\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts highstock gantt\n * @excluding    boostThreshold, crisp, cropThreshold, depth, edgeColor,\n *               edgeWidth, findNearestPointBy, getExtremesFromAll,\n *               negativeColor, pointInterval, pointIntervalUnit,\n *               pointPlacement, pointRange, pointStart, softThreshold,\n *               stacking, threshold, data, dataSorting, boostBlending\n * @requires     modules/xrange\n * @optionparent plotOptions.xrange\n */\nvar XRangeSeriesDefaults = {\n    /**\n     * A partial fill for each point, typically used to visualize how much\n     * of a task is performed. The partial fill object can be set either on\n     * series or point level.\n     *\n     * @sample {highcharts} highcharts/demo/x-range\n     *         X-range with partial fill\n     *\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.xrange.partialFill\n     */\n    /**\n     * The fill color to be used for partial fills. Defaults to a darker\n     * shade of the point color.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @product   highcharts highstock gantt\n     * @apioption plotOptions.xrange.partialFill.fill\n     */\n    /**\n     * A partial fill for each point, typically used to visualize how much\n     * of a task is performed. See [completed](series.gantt.data.completed).\n     *\n     * @sample gantt/demo/progress-indicator\n     *         Gantt with progress indicator\n     *\n     * @product   gantt\n     * @apioption plotOptions.gantt.partialFill\n     */\n    /**\n     * In an X-range series, this option makes all points of the same Y-axis\n     * category the same color.\n     */\n    colorByPoint: true,\n    dataLabels: {\n        formatter: function () {\n            var amount = this.partialFill;\n            if (isObject(amount)) {\n                amount = amount.amount;\n            }\n            if (isNumber(amount) && amount > 0) {\n                return correctFloat(amount * 100) + '%';\n            }\n        },\n        inside: true,\n        verticalAlign: 'middle',\n        style: {\n            whiteSpace: 'nowrap'\n        }\n    },\n    tooltip: {\n        headerFormat: '<span style=\"font-size: 0.8em\">{ucfirst point.x} - {point.x2}</span><br/>',\n        pointFormat: '<span style=\"color:{point.color}\">\\u25CF</span> {series.name}: <b>{point.yCategory}</b><br/>'\n    },\n    borderRadius: 3,\n    pointRange: 0\n};\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ var XRange_XRangeSeriesDefaults = (XRangeSeriesDefaults);\n/* *\n *\n * API Options\n *\n * */\n/**\n * An `xrange` series. If the [type](#series.xrange.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.xrange\n * @excluding boostThreshold, crisp, cropThreshold, depth, edgeColor, edgeWidth,\n *            findNearestPointBy, getExtremesFromAll, negativeColor,\n *            pointInterval, pointIntervalUnit, pointPlacement, pointRange,\n *            pointStart, softThreshold, stacking, threshold, dataSorting,\n *            boostBlending\n * @product   highcharts highstock gantt\n * @requires  modules/xrange\n * @apioption series.xrange\n */\n/**\n * An array of data points for the series. For the `xrange` series type,\n * points can be given in the following ways:\n *\n * 1. An array of objects with named values. The objects are point configuration\n *    objects as seen below.\n *    ```js\n *    data: [{\n *        x: Date.UTC(2017, 0, 1),\n *        x2: Date.UTC(2017, 0, 3),\n *        name: \"Test\",\n *        y: 0,\n *        color: \"#00FF00\"\n *    }, {\n *        x: Date.UTC(2017, 0, 4),\n *        x2: Date.UTC(2017, 0, 5),\n *        name: \"Deploy\",\n *        y: 1,\n *        color: \"#FF0000\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @declare   Highcharts.XrangePointOptionsObject\n * @type      {Array<*>}\n * @extends   series.line.data\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data\n */\n/**\n * The starting X value of the range point.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.x\n */\n/**\n * The ending X value of the range point.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.x2\n */\n/**\n * The Y value of the range point.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.y\n */\n/**\n * A partial fill for each point, typically used to visualize how much of\n * a task is performed. The partial fill object can be set either on series\n * or point level.\n *\n * @sample {highcharts} highcharts/demo/x-range\n *         X-range with partial fill\n *\n * @declare   Highcharts.XrangePointPartialFillOptionsObject\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.partialFill\n */\n/**\n * The amount of the X-range point to be filled. Values can be 0-1 and are\n * converted to percentages in the default data label formatter.\n *\n * @type      {number}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.partialFill.amount\n */\n/**\n * The fill color to be used for partial fills. Defaults to a darker shade\n * of the point color.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts highstock gantt\n * @apioption series.xrange.data.partialFill.fill\n */\n(''); // Adds doclets above to transpiled file\n\n;// ./code/es5/es-modules/Series/XRange/XRangePoint.js\n/* *\n *\n *  X-range series module\n *\n *  (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/* *\n *\n *  Class\n *\n * */\nvar XRangePoint = /** @class */ (function (_super) {\n    __extends(XRangePoint, _super);\n    /**\n     * Extend init to have y default to 0.\n     *\n     * @private\n     */\n    function XRangePoint(series, options) {\n        var _this = _super.call(this,\n            series,\n            options) || this;\n        if (!_this.y) {\n            _this.y = 0;\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Return color of a point based on its category.\n     *\n     * @private\n     * @function getColorByCategory\n     *\n     * @param {object} series\n     *        The series which the point belongs to.\n     *\n     * @param {object} point\n     *        The point to calculate its color for.\n     *\n     * @return {object}\n     *         Returns an object containing the properties color and colorIndex.\n     */\n    XRangePoint.getColorByCategory = function (series, point) {\n        var colors = series.options.colors || series.chart.options.colors,\n            colorCount = colors ?\n                colors.length :\n                series.chart.options.chart.colorCount,\n            colorIndex = point.y % colorCount,\n            color = colors === null || colors === void 0 ? void 0 : colors[colorIndex];\n        return {\n            colorIndex: colorIndex,\n            color: color\n        };\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    XRangePoint.prototype.resolveColor = function () {\n        var series = this.series;\n        if (series.options.colorByPoint && !this.options.color) {\n            var colorByPoint = XRangePoint.getColorByCategory(series,\n                this);\n            if (!series.chart.styledMode) {\n                this.color = colorByPoint.color;\n            }\n            if (!this.options.colorIndex) {\n                this.colorIndex = colorByPoint.colorIndex;\n            }\n        }\n        else {\n            this.color = this.options.color || series.color;\n        }\n    };\n    /**\n     * Extend applyOptions to handle time strings for x2\n     *\n     * @private\n     */\n    XRangePoint.prototype.applyOptions = function (options, x) {\n        var _a;\n        _super.prototype.applyOptions.call(this, options, x);\n        this.x2 = this.series.chart.time.parse(this.x2);\n        this.isNull = !((_a = this.isValid) === null || _a === void 0 ? void 0 : _a.call(this));\n        return this;\n    };\n    /**\n     * @private\n     */\n    XRangePoint.prototype.setState = function () {\n        _super.prototype.setState.apply(this, arguments);\n        this.series.drawPoint(this, this.series.getAnimationVerb());\n    };\n    /**\n     * @private\n     */\n    XRangePoint.prototype.isValid = function () {\n        return typeof this.x === 'number' &&\n            typeof this.x2 === 'number';\n    };\n    return XRangePoint;\n}(ColumnPoint));\nextend(XRangePoint.prototype, {\n    ttBelow: false,\n    tooltipDateKeys: ['x', 'x2']\n});\n/* *\n *\n *  Class Namespace\n *\n * */\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var XRange_XRangePoint = (XRangePoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * The ending X value of the range point.\n * @name Highcharts.Point#x2\n * @type {number|undefined}\n * @requires modules/xrange\n */\n/**\n * @interface Highcharts.PointOptionsObject in parts/Point.ts\n */ /**\n* The ending X value of the range point.\n* @name Highcharts.PointOptionsObject#x2\n* @type {number|undefined}\n* @requires modules/xrange\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Series/XRange/XRangeSeries.js\n/* *\n *\n *  X-range series module\n *\n *  (c) 2010-2025 Torstein Honsi, Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar XRangeSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, crisp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).crisp, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, XRangeSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, XRangeSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, XRangeSeries_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, relativeLength = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).relativeLength;\n\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Max x2 should be considered in xAxis extremes\n * @private\n */\nfunction onAxisAfterGetSeriesExtremes() {\n    var dataMax,\n        modMax;\n    if (this.isXAxis) {\n        dataMax = pick(this.dataMax, -Number.MAX_VALUE);\n        for (var _i = 0, _a = this.series; _i < _a.length; _i++) {\n            var series = _a[_i];\n            var column = (series.dataTable.getColumn('x2',\n                true) ||\n                    series.dataTable.getColumn('end',\n                true));\n            if (column) {\n                for (var _b = 0, _c = column; _b < _c.length; _b++) {\n                    var val = _c[_b];\n                    if (XRangeSeries_isNumber(val) && val > dataMax) {\n                        dataMax = val;\n                        modMax = true;\n                    }\n                }\n            }\n        }\n        if (modMax) {\n            this.dataMax = dataMax;\n        }\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.xrange\n *\n * @augments Highcharts.Series\n */\nvar XRangeSeries = /** @class */ (function (_super) {\n    XRangeSeries_extends(XRangeSeries, _super);\n    function XRangeSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    XRangeSeries.compose = function (AxisClass) {\n        if (pushUnique(composed, 'Series.XRange')) {\n            addEvent(AxisClass, 'afterGetSeriesExtremes', onAxisAfterGetSeriesExtremes);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    XRangeSeries.prototype.init = function () {\n        _super.prototype.init.apply(this, arguments);\n        this.options.stacking = void 0; // #13161\n    };\n    /**\n     * Borrow the column series metrics, but with swapped axes. This gives\n     * free access to features like groupPadding, grouping, pointWidth etc.\n     * @private\n     */\n    XRangeSeries.prototype.getColumnMetrics = function () {\n        var _this = this;\n        var swapAxes = function () {\n                for (var _i = 0,\n            _a = _this.chart.series; _i < _a.length; _i++) {\n                    var series = _a[_i];\n                var xAxis = series.xAxis;\n                series.xAxis = series.yAxis;\n                series.yAxis = xAxis;\n            }\n        };\n        swapAxes();\n        var metrics = _super.prototype.getColumnMetrics.call(this);\n        swapAxes();\n        return metrics;\n    };\n    /**\n     * Override cropData to show a point where x or x2 is outside visible range,\n     * but one of them is inside.\n     * @private\n     */\n    XRangeSeries.prototype.cropData = function (table, min, max) {\n        // Replace xData with x2Data to find the appropriate cropStart\n        var xData = table.getColumn('x') || [], x2Data = table.getColumn('x2');\n        table.setColumn('x', x2Data, void 0, { silent: true });\n        var croppedData = _super.prototype.cropData.call(this,\n            table,\n            min,\n            max);\n        // Re-insert the cropped xData\n        table.setColumn('x', xData.slice(croppedData.start, croppedData.end), void 0, { silent: true });\n        return croppedData;\n    };\n    /**\n     * Finds the index of an existing point that matches the given point\n     * options.\n     *\n     * @private\n     *\n     * @param {Highcharts.XRangePointOptions} options\n     *        The options of the point.\n     *\n     * @return {number|undefined}\n     *         Returns index of a matching point, or undefined if no match is\n     *         found.\n     */\n    XRangeSeries.prototype.findPointIndex = function (options) {\n        var _a = this,\n            cropStart = _a.cropStart,\n            points = _a.points;\n        var id = options.id;\n        var pointIndex;\n        if (id) {\n            var point = find(points,\n                function (point) { return point.id === id; });\n            pointIndex = point ? point.index : void 0;\n        }\n        if (typeof pointIndex === 'undefined') {\n            var point = find(points,\n                function (point) { return (point.x === options.x &&\n                    point.x2 === options.x2 &&\n                    !point.touched); });\n            pointIndex = point ? point.index : void 0;\n        }\n        // Reduce pointIndex if data is cropped\n        if (this.cropped &&\n            XRangeSeries_isNumber(pointIndex) &&\n            XRangeSeries_isNumber(cropStart) &&\n            pointIndex >= cropStart) {\n            pointIndex -= cropStart;\n        }\n        return pointIndex;\n    };\n    XRangeSeries.prototype.alignDataLabel = function (point) {\n        var _a,\n            _b;\n        var oldPlotX = point.plotX;\n        point.plotX = pick((_a = point.dlBox) === null || _a === void 0 ? void 0 : _a.centerX, point.plotX);\n        if (point.dataLabel && ((_b = point.shapeArgs) === null || _b === void 0 ? void 0 : _b.width)) {\n            point.dataLabel.css({\n                width: \"\" + point.shapeArgs.width + \"px\"\n            });\n        }\n        _super.prototype.alignDataLabel.apply(this, arguments);\n        point.plotX = oldPlotX;\n    };\n    /**\n     * @private\n     */\n    XRangeSeries.prototype.translatePoint = function (point) {\n        var _a,\n            _b,\n            _c;\n        var xAxis = this.xAxis,\n            yAxis = this.yAxis,\n            metrics = this.columnMetrics,\n            options = this.options,\n            minPointLength = options.minPointLength || 0,\n            oldColWidth = (((_a = point.shapeArgs) === null || _a === void 0 ? void 0 : _a.width) || 0) / 2,\n            seriesXOffset = this.pointXOffset = metrics.offset,\n            posX = pick(point.x2,\n            point.x + (point.len || 0)),\n            borderRadius = options.borderRadius,\n            plotTop = this.chart.plotTop,\n            plotLeft = this.chart.plotLeft;\n        var plotX = point.plotX,\n            plotX2 = xAxis.translate(posX, 0, 0, 0, 1);\n        var length = Math.abs(plotX2 - plotX),\n            inverted = this.chart.inverted,\n            borderWidth = pick(options.borderWidth, 1);\n        var widthDifference,\n            partialFill,\n            yOffset = metrics.offset,\n            pointHeight = Math.round(metrics.width),\n            dlLeft,\n            dlRight,\n            dlWidth,\n            clipRectWidth;\n        if (minPointLength) {\n            widthDifference = minPointLength - length;\n            if (widthDifference < 0) {\n                widthDifference = 0;\n            }\n            plotX -= widthDifference / 2;\n            plotX2 += widthDifference / 2;\n        }\n        plotX = Math.max(plotX, -10);\n        plotX2 = clamp(plotX2, -10, xAxis.len + 10);\n        // Handle individual pointWidth\n        if (defined(point.options.pointWidth)) {\n            yOffset -= ((Math.ceil(point.options.pointWidth) - pointHeight) / 2);\n            pointHeight = Math.ceil(point.options.pointWidth);\n        }\n        // Apply pointPlacement to the Y axis\n        if (options.pointPlacement &&\n            XRangeSeries_isNumber(point.plotY) &&\n            yAxis.categories) {\n            point.plotY = yAxis.translate(point.y, 0, 1, 0, 1, options.pointPlacement);\n        }\n        var x = crisp(Math.min(plotX,\n            plotX2),\n            borderWidth),\n            x2 = crisp(Math.max(plotX,\n            plotX2),\n            borderWidth),\n            width = x2 - x;\n        var r = Math.min(relativeLength((typeof borderRadius === 'object' ?\n                borderRadius.radius :\n                borderRadius || 0),\n            pointHeight),\n            Math.min(width,\n            pointHeight) / 2);\n        var shapeArgs = {\n                x: x,\n                y: crisp((point.plotY || 0) + yOffset,\n            borderWidth),\n                width: width,\n                height: pointHeight,\n                r: r\n            };\n        point.shapeArgs = shapeArgs;\n        // Move tooltip to default position\n        if (!inverted) {\n            point.tooltipPos[0] -= oldColWidth +\n                seriesXOffset -\n                shapeArgs.width / 2;\n        }\n        else {\n            point.tooltipPos[1] += seriesXOffset +\n                oldColWidth;\n        }\n        // Align data labels inside the shape and inside the plot area\n        dlLeft = shapeArgs.x;\n        dlRight = dlLeft + shapeArgs.width;\n        if (dlLeft < 0 || dlRight > xAxis.len) {\n            dlLeft = clamp(dlLeft, 0, xAxis.len);\n            dlRight = clamp(dlRight, 0, xAxis.len);\n            dlWidth = dlRight - dlLeft;\n            point.dlBox = merge(shapeArgs, {\n                x: dlLeft,\n                width: dlRight - dlLeft,\n                centerX: dlWidth ? dlWidth / 2 : null\n            });\n        }\n        else {\n            point.dlBox = null;\n        }\n        // Tooltip position\n        var tooltipPos = point.tooltipPos;\n        var xIndex = !inverted ? 0 : 1;\n        var yIndex = !inverted ? 1 : 0;\n        var tooltipYOffset = (this.columnMetrics ?\n                this.columnMetrics.offset :\n                -metrics.width / 2);\n        // Centering tooltip position (#14147)\n        if (inverted) {\n            tooltipPos[xIndex] += shapeArgs.width / 2;\n        }\n        else {\n            tooltipPos[xIndex] = clamp(tooltipPos[xIndex] +\n                (xAxis.reversed ? -1 : 0) * shapeArgs.width, xAxis.left - plotLeft, xAxis.left + xAxis.len - plotLeft - 1);\n        }\n        tooltipPos[yIndex] = clamp(tooltipPos[yIndex] + ((inverted ? -1 : 1) * tooltipYOffset), yAxis.top - plotTop, yAxis.top + yAxis.len - plotTop - 1);\n        // Add a partShapeArgs to the point, based on the shapeArgs property\n        partialFill = point.partialFill;\n        if (partialFill) {\n            // Get the partial fill amount\n            if (XRangeSeries_isObject(partialFill)) {\n                partialFill = partialFill.amount;\n            }\n            // If it was not a number, assume 0\n            if (!XRangeSeries_isNumber(partialFill)) {\n                partialFill = 0;\n            }\n            point.partShapeArgs = merge(shapeArgs);\n            clipRectWidth = Math.max(Math.round(length * partialFill + point.plotX -\n                plotX), 0);\n            point.clipRectArgs = {\n                x: xAxis.reversed ? // #10717\n                    shapeArgs.x + length - clipRectWidth :\n                    shapeArgs.x,\n                y: shapeArgs.y,\n                width: clipRectWidth,\n                height: shapeArgs.height\n            };\n        }\n        // Add formatting keys for tooltip and data labels. Use 'category' as\n        // 'key' to ensure tooltip datetime formatting. Use 'name' only when\n        // 'category' is undefined.\n        point.key = point.category || point.name;\n        point.yCategory = (_b = yAxis.categories) === null || _b === void 0 ? void 0 : _b[(_c = point.y) !== null && _c !== void 0 ? _c : -1];\n    };\n    /**\n     * @private\n     */\n    XRangeSeries.prototype.translate = function () {\n        _super.prototype.translate.apply(this, arguments);\n        for (var _i = 0, _a = this.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            this.translatePoint(point);\n        }\n    };\n    /**\n     * Draws a single point in the series. Needed for partial fill.\n     *\n     * This override turns point.graphic into a group containing the\n     * original graphic and an overlay displaying the partial fill.\n     *\n     * @private\n     *\n     * @param {Highcharts.Point} point\n     *        An instance of Point in the series.\n     *\n     * @param {\"animate\"|\"attr\"} verb\n     *        'animate' (animates changes) or 'attr' (sets options)\n     */\n    XRangeSeries.prototype.drawPoint = function (point, verb) {\n        var seriesOpts = this.options,\n            renderer = this.chart.renderer,\n            type = point.shapeType,\n            shapeArgs = point.shapeArgs,\n            partShapeArgs = point.partShapeArgs,\n            clipRectArgs = point.clipRectArgs,\n            pointState = point.state,\n            stateOpts = (seriesOpts.states[pointState || 'normal'] ||\n                {}),\n            pointStateVerb = typeof pointState === 'undefined' ?\n                'attr' : verb,\n            pointAttr = this.pointAttribs(point,\n            pointState),\n            animation = pick(this.chart.options.chart.animation,\n            stateOpts.animation);\n        var graphic = point.graphic,\n            pfOptions = point.partialFill;\n        if (!point.isNull && point.visible !== false) {\n            // Original graphic\n            if (graphic) { // Update\n                graphic.rect[verb](shapeArgs);\n            }\n            else {\n                point.graphic = graphic = renderer.g('point')\n                    .addClass(point.getClassName())\n                    .add(point.group || this.group);\n                graphic.rect = renderer[type](merge(shapeArgs))\n                    .addClass(point.getClassName())\n                    .addClass('highcharts-partfill-original')\n                    .add(graphic);\n            }\n            // Partial fill graphic\n            if (partShapeArgs) {\n                if (graphic.partRect) {\n                    graphic.partRect[verb](merge(partShapeArgs));\n                    graphic.partialClipRect[verb](merge(clipRectArgs));\n                }\n                else {\n                    graphic.partialClipRect = renderer.clipRect(clipRectArgs.x, clipRectArgs.y, clipRectArgs.width, clipRectArgs.height);\n                    graphic.partRect =\n                        renderer[type](partShapeArgs)\n                            .addClass('highcharts-partfill-overlay')\n                            .add(graphic)\n                            .clip(graphic.partialClipRect);\n                }\n            }\n            // Presentational\n            if (!this.chart.styledMode) {\n                graphic\n                    .rect[verb](pointAttr, animation)\n                    .shadow(seriesOpts.shadow);\n                if (partShapeArgs) {\n                    // Ensure pfOptions is an object\n                    if (!XRangeSeries_isObject(pfOptions)) {\n                        pfOptions = {};\n                    }\n                    if (XRangeSeries_isObject(seriesOpts.partialFill)) {\n                        pfOptions = merge(seriesOpts.partialFill, pfOptions);\n                    }\n                    var fill = (pfOptions.fill ||\n                            color(pointAttr.fill).brighten(-0.3).get() ||\n                            color(point.color || this.color)\n                                .brighten(-0.3).get());\n                    pointAttr.fill = fill;\n                    graphic\n                        .partRect[pointStateVerb](pointAttr, animation)\n                        .shadow(seriesOpts.shadow);\n                }\n            }\n        }\n        else if (graphic) {\n            point.graphic = graphic.destroy(); // #1269\n        }\n    };\n    /**\n     * @private\n     */\n    XRangeSeries.prototype.drawPoints = function () {\n        var verb = this.getAnimationVerb();\n        // Draw the columns\n        for (var _i = 0, _a = this.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            this.drawPoint(point, verb);\n        }\n    };\n    /**\n     * Returns \"animate\", or \"attr\" if the number of points is above the\n     * animation limit.\n     *\n     * @private\n     */\n    XRangeSeries.prototype.getAnimationVerb = function () {\n        return (this.chart.pointCount < (this.options.animationLimit || 250) ?\n            'animate' :\n            'attr');\n    };\n    /**\n     * @private\n     */\n    XRangeSeries.prototype.isPointInside = function (point) {\n        var shapeArgs = point.shapeArgs,\n            plotX = point.plotX,\n            plotY = point.plotY;\n        if (!shapeArgs) {\n            return _super.prototype.isPointInside.apply(this, arguments);\n        }\n        var isInside = typeof plotX !== 'undefined' &&\n                typeof plotY !== 'undefined' &&\n                plotY >= 0 &&\n                plotY <= this.yAxis.len &&\n                (shapeArgs.x || 0) + (shapeArgs.width || 0) >= 0 &&\n                plotX <= this.xAxis.len;\n        return isInside;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    XRangeSeries.defaultOptions = merge(ColumnSeries.defaultOptions, XRange_XRangeSeriesDefaults);\n    return XRangeSeries;\n}(ColumnSeries));\nXRangeSeries_extend(XRangeSeries.prototype, {\n    pointClass: XRange_XRangePoint,\n    pointArrayMap: ['x2', 'y'],\n    getExtremesFromAll: true,\n    keysAffectYAxis: ['y'],\n    parallelArrays: ['x', 'x2', 'y'],\n    requireSorting: false,\n    type: 'xrange',\n    animate: (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series.prototype.animate,\n    autoIncrement: noop,\n    buildKDTree: noop\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('xrange', XRangeSeries);\n/* *\n *\n * Default Export\n *\n * */\n/* harmony default export */ var XRange_XRangeSeries = (XRangeSeries);\n\n;// ./code/es5/es-modules/masters/modules/xrange.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nXRange_XRangeSeries.compose(G.Axis);\n/* harmony default export */ var xrange_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "xrange_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "correctFloat", "isNumber", "isObject", "XRange_XRangeSeriesDefaults", "colorByPoint", "dataLabels", "formatter", "amount", "partialFill", "inside", "verticalAlign", "style", "whiteSpace", "tooltip", "headerFormat", "pointFormat", "borderRadius", "pointRange", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "ColumnPoint", "seriesTypes", "column", "pointClass", "extend", "XRangePoint", "_super", "series", "options", "_this", "y", "getColorByCategory", "point", "colors", "chart", "colorCount", "length", "colorIndex", "color", "resolveColor", "styledMode", "applyOptions", "x", "_a", "x2", "time", "parse", "isNull", "<PERSON><PERSON><PERSON><PERSON>", "setState", "apply", "arguments", "drawPoint", "getAnimationVerb", "ttBelow", "tooltipDateKeys", "XRangeSeries_extends", "TypeError", "String", "composed", "noop", "ColumnSeries", "addEvent", "clamp", "crisp", "defined", "XRangeSeries_extend", "find", "XRangeSeries_isNumber", "XRangeSeries_isObject", "merge", "pick", "pushUnique", "<PERSON><PERSON><PERSON><PERSON>", "onAxisAfterGetSeriesExtremes", "dataMax", "modMax", "isXAxis", "Number", "MAX_VALUE", "_i", "dataTable", "getColumn", "_b", "_c", "val", "XRangeSeries", "compose", "AxisClass", "init", "stacking", "getColumnMetrics", "swapAxes", "xAxis", "yAxis", "metrics", "cropData", "table", "min", "max", "xData", "x2Data", "setColumn", "silent", "croppedData", "slice", "start", "end", "findPointIndex", "pointIndex", "cropStart", "points", "id", "index", "touched", "cropped", "alignDataLabel", "oldPlotX", "plotX", "dlBox", "centerX", "dataLabel", "shapeArgs", "width", "css", "translatePoint", "widthDifference", "dlLeft", "dlRight", "dl<PERSON><PERSON><PERSON>", "clipRectWidth", "columnMetrics", "minP<PERSON><PERSON><PERSON>th", "oldColWidth", "seriesXOffset", "pointXOffset", "offset", "posX", "len", "plotTop", "plotLeft", "plotX2", "translate", "Math", "abs", "inverted", "borderWidth", "yOffset", "pointHeight", "round", "pointWidth", "ceil", "pointPlacement", "plotY", "categories", "r", "radius", "height", "tooltipPos", "xIndex", "yIndex", "tooltipYOffset", "reversed", "left", "top", "partShapeArgs", "clipRectArgs", "category", "name", "yCategory", "verb", "seriesOpts", "renderer", "type", "shapeType", "pointState", "state", "stateOpts", "states", "pointStateVerb", "pointAttr", "pointAttribs", "animation", "graphic", "pfOptions", "visible", "destroy", "rect", "g", "addClass", "getClassName", "add", "group", "partRect", "partialClipRect", "clipRect", "clip", "shadow", "fill", "brighten", "drawPoints", "pointCount", "animationLimit", "isPointInside", "defaultOptions", "pointArrayMap", "getExtremesFromAll", "keysAffectYAxis", "parallelArrays", "requireSorting", "animate", "autoIncrement", "buildKDTree", "registerSeriesType", "G", "XRange_XRangeSeries", "Axis"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,EAChH,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACzI,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,EAE7IJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACpH,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACtH,OAAgB,AAAC,WACP,aACA,IAsUFC,EA8KAA,EApfMC,EAAuB,CAE/B,IACC,SAASR,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAab,OAAO,CAG5B,IAAIC,EAASS,CAAwB,CAACE,EAAS,CAAG,CAGjDZ,QAAS,CAAC,CACX,EAMA,OAHAS,CAAmB,CAACG,EAAS,CAACX,EAAQA,EAAOD,OAAO,CAAEW,GAG/CV,EAAOD,OAAO,AACtB,CAMCW,EAAoBI,CAAC,CAAG,SAASd,CAAM,EACtC,IAAIe,EAASf,GAAUA,EAAOgB,UAAU,CACvC,WAAa,OAAOhB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAU,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASlB,CAAO,CAAEoB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACtB,EAASqB,IAC5EE,OAAOC,cAAc,CAACxB,EAASqB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAY,CAC3D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,GAEzIE,EAAmI3B,EAAoB,KACvJ4B,EAAuJ5B,EAAoBI,CAAC,CAACuB,GAe7KE,EAAe,AAACL,IAA+EK,YAAY,CAAEC,EAAW,AAACN,IAA+EM,QAAQ,CAAEC,EAAW,AAACP,IAA+EO,QAAQ,CA2FxRC,EA/DN,CAkCvBC,aAAc,CAAA,EACdC,WAAY,CACRC,UAAW,WACP,IAAIC,EAAS,IAAI,CAACC,WAAW,CAI7B,GAHIN,EAASK,IACTA,CAAAA,EAASA,EAAOA,MAAM,AAAD,EAErBN,EAASM,IAAWA,EAAS,EAC7B,OAAOP,EAAaO,AAAS,IAATA,GAAgB,GAE5C,EACAE,OAAQ,CAAA,EACRC,cAAe,SACfC,MAAO,CACHC,WAAY,QAChB,CACJ,EACAC,QAAS,CACLC,aAAc,4EACdC,YAAa,yFACjB,EACAC,aAAc,EACdC,WAAY,CAChB,EAkIIC,GACIlD,EAAgB,SAAUU,CAAC,CAC3ByC,CAAC,EAMD,MAAOnD,AALHA,CAAAA,EAAgBe,OAAOqC,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU5C,CAAC,CAC1DyC,CAAC,EAAIzC,EAAE2C,SAAS,CAAGF,CAAG,GACd,SAAUzC,CAAC,CACnByC,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAE7B,cAAc,CAACiC,IAAI7C,CAAAA,CAAC,CAAC6C,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvC7C,EAAGyC,EAC5B,EACO,SAAUzC,CAAC,CAAEyC,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG/C,CAAG,CADtCV,EAAcU,EAAGyC,GAEjBzC,EAAEW,SAAS,CAAG8B,AAAM,OAANA,EAAapC,OAAO2C,MAAM,CAACP,GAAMK,CAAAA,EAAGnC,SAAS,CAAG8B,EAAE9B,SAAS,CAAE,IAAImC,CAAG,CACtF,GAGAG,EAAc,AAAC5B,IAA2I6B,WAAW,CAACC,MAAM,CAACxC,SAAS,CAACyC,UAAU,CAEjMC,EAAS,AAACpC,IAA+EoC,MAAM,CAM/FC,EAA6B,SAAUC,CAAM,EAO7C,SAASD,EAAYE,CAAM,CAAEC,CAAO,EAChC,IAAIC,EAAQH,EAAO1C,IAAI,CAAC,IAAI,CACxB2C,EACAC,IAAY,IAAI,CAIpB,OAHKC,EAAMC,CAAC,EACRD,CAAAA,EAAMC,CAAC,CAAG,CAAA,EAEPD,CACX,CAmFA,OAjGAlB,EAAUc,EAAaC,GAmCvBD,EAAYM,kBAAkB,CAAG,SAAUJ,CAAM,CAAEK,CAAK,EACpD,IAAIC,EAASN,EAAOC,OAAO,CAACK,MAAM,EAAIN,EAAOO,KAAK,CAACN,OAAO,CAACK,MAAM,CAC7DE,EAAaF,EACTA,EAAOG,MAAM,CACbT,EAAOO,KAAK,CAACN,OAAO,CAACM,KAAK,CAACC,UAAU,CACzCE,EAAaL,EAAMF,CAAC,CAAGK,EACvBG,EAAQL,MAAAA,EAAuC,KAAK,EAAIA,CAAM,CAACI,EAAW,CAC9E,MAAO,CACHA,WAAYA,EACZC,MAAOA,CACX,CACJ,EASAb,EAAY3C,SAAS,CAACyD,YAAY,CAAG,WACjC,IAAIZ,EAAS,IAAI,CAACA,MAAM,CACxB,GAAIA,EAAOC,OAAO,CAAC/B,YAAY,EAAI,CAAC,IAAI,CAAC+B,OAAO,CAACU,KAAK,CAAE,CACpD,IAAIzC,EAAe4B,EAAYM,kBAAkB,CAACJ,EAC9C,IAAI,CACHA,CAAAA,EAAOO,KAAK,CAACM,UAAU,EACxB,CAAA,IAAI,CAACF,KAAK,CAAGzC,EAAayC,KAAK,AAAD,EAE7B,IAAI,CAACV,OAAO,CAACS,UAAU,EACxB,CAAA,IAAI,CAACA,UAAU,CAAGxC,EAAawC,UAAU,AAAD,CAEhD,MAEI,IAAI,CAACC,KAAK,CAAG,IAAI,CAACV,OAAO,CAACU,KAAK,EAAIX,EAAOW,KAAK,AAEvD,EAMAb,EAAY3C,SAAS,CAAC2D,YAAY,CAAG,SAAUb,CAAO,CAAEc,CAAC,EACrD,IAAIC,EAIJ,OAHAjB,EAAO5C,SAAS,CAAC2D,YAAY,CAACzD,IAAI,CAAC,IAAI,CAAE4C,EAASc,GAClD,IAAI,CAACE,EAAE,CAAG,IAAI,CAACjB,MAAM,CAACO,KAAK,CAACW,IAAI,CAACC,KAAK,CAAC,IAAI,CAACF,EAAE,EAC9C,IAAI,CAACG,MAAM,CAAG,CAAE,CAAA,AAAwB,OAAvBJ,CAAAA,EAAK,IAAI,CAACK,OAAO,AAAD,GAAeL,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG3D,IAAI,CAAC,IAAI,CAAA,EAC9E,IAAI,AACf,EAIAyC,EAAY3C,SAAS,CAACmE,QAAQ,CAAG,WAC7BvB,EAAO5C,SAAS,CAACmE,QAAQ,CAACC,KAAK,CAAC,IAAI,CAAEC,WACtC,IAAI,CAACxB,MAAM,CAACyB,SAAS,CAAC,IAAI,CAAE,IAAI,CAACzB,MAAM,CAAC0B,gBAAgB,GAC5D,EAIA5B,EAAY3C,SAAS,CAACkE,OAAO,CAAG,WAC5B,MAAO,AAAkB,UAAlB,OAAO,IAAI,CAACN,CAAC,EAChB,AAAmB,UAAnB,OAAO,IAAI,CAACE,EAAE,AACtB,EACOnB,CACX,EAAEL,GACFI,EAAOC,EAAY3C,SAAS,CAAE,CAC1BwE,QAAS,CAAA,EACTC,gBAAiB,CAAC,IAAK,KAAK,AAChC,GA8CA,IAAIC,GACI/F,EAAgB,SAAUU,CAAC,CAC3ByC,CAAC,EAOD,MAAOnD,AANHA,CAAAA,EAAgBe,OAAOqC,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU5C,CAAC,CAC1DyC,CAAC,EAAIzC,EAAE2C,SAAS,CAAGF,CAAG,GACd,SAAUzC,CAAC,CACnByC,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOpC,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC4B,EAC/DI,IAAI7C,CAAAA,CAAC,CAAC6C,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACI7C,EAAGyC,EAC5B,EACO,SAAUzC,CAAC,CAAEyC,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAI6C,UAAU,uBAAyBC,OAAO9C,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG/C,CAAG,CADtCV,EAAcU,EAAGyC,GAEjBzC,EAAEW,SAAS,CAAG8B,AAAM,OAANA,EAAapC,OAAO2C,MAAM,CAACP,GAAMK,CAAAA,EAAGnC,SAAS,CAAG8B,EAAE9B,SAAS,CAAE,IAAImC,CAAG,CACtF,GAGA0C,EAAW,AAACvE,IAA+EuE,QAAQ,CAAEC,EAAO,AAACxE,IAA+EwE,IAAI,CAEhMtB,EAAQ,AAAChD,IAAuGwD,KAAK,CAErHe,EAAe,AAACrE,IAA2I6B,WAAW,CAACC,MAAM,CAE7KwC,EAAW,AAAC1E,IAA+E0E,QAAQ,CAAEC,EAAQ,AAAC3E,IAA+E2E,KAAK,CAAEC,EAAQ,AAAC5E,IAA+E4E,KAAK,CAAEC,EAAU,AAAC7E,IAA+E6E,OAAO,CAAEC,EAAsB,AAAC9E,IAA+EoC,MAAM,CAAE2C,EAAO,AAAC/E,IAA+E+E,IAAI,CAAEC,EAAwB,AAAChF,IAA+EM,QAAQ,CAAE2E,EAAwB,AAACjF,IAA+EO,QAAQ,CAAE2E,EAAQ,AAAClF,IAA+EkF,KAAK,CAAEC,EAAO,AAACnF,IAA+EmF,IAAI,CAAEC,EAAa,AAACpF,IAA+EoF,UAAU,CAAEC,EAAiB,AAACrF,IAA+EqF,cAAc,CAY7sC,SAASC,IACL,IAAIC,EACAC,EACJ,GAAI,IAAI,CAACC,OAAO,CAAE,CACdF,EAAUJ,EAAK,IAAI,CAACI,OAAO,CAAE,CAACG,OAAOC,SAAS,EAC9C,IAAK,IAAIC,EAAK,EAAGrC,EAAK,IAAI,CAAChB,MAAM,CAAEqD,EAAKrC,EAAGP,MAAM,CAAE4C,IAAM,CACrD,IAAIrD,EAASgB,CAAE,CAACqC,EAAG,CACf1D,EAAUK,EAAOsD,SAAS,CAACC,SAAS,CAAC,KACrC,CAAA,IACIvD,EAAOsD,SAAS,CAACC,SAAS,CAAC,MAC/B,CAAA,GACJ,GAAI5D,EACA,IAAK,IAAI6D,EAAK,EAAgBA,EAAKC,AAAb9D,EAAgBc,MAAM,CAAE+C,IAAM,CAChD,IAAIE,EAAMD,AADQ9D,CACN,CAAC6D,EAAG,CACZf,EAAsBiB,IAAQA,EAAMV,IACpCA,EAAUU,EACVT,EAAS,CAAA,EAEjB,CAER,CACIA,GACA,CAAA,IAAI,CAACD,OAAO,CAAGA,CAAM,CAE7B,CACJ,CAaA,IAAIW,EAA8B,SAAU5D,CAAM,EAE9C,SAAS4D,IACL,OAAO5D,AAAW,OAAXA,GAAmBA,EAAOwB,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAqZA,OAxZAK,EAAqB8B,EAAc5D,GASnC4D,EAAaC,OAAO,CAAG,SAAUC,CAAS,EAClChB,EAAWb,EAAU,kBACrBG,EAAS0B,EAAW,yBAA0Bd,EAEtD,EASAY,EAAaxG,SAAS,CAAC2G,IAAI,CAAG,WAC1B/D,EAAO5C,SAAS,CAAC2G,IAAI,CAACvC,KAAK,CAAC,IAAI,CAAEC,WAClC,IAAI,CAACvB,OAAO,CAAC8D,QAAQ,CAAG,KAAK,CACjC,EAMAJ,EAAaxG,SAAS,CAAC6G,gBAAgB,CAAG,WACtC,IAAI9D,EAAQ,IAAI,CACZ+D,EAAW,WACP,IAAK,IAAIZ,EAAK,EAClBrC,EAAKd,EAAMK,KAAK,CAACP,MAAM,CAAEqD,EAAKrC,EAAGP,MAAM,CAAE4C,IAAM,CACvC,IAAIrD,EAASgB,CAAE,CAACqC,EAAG,CACnBa,EAAQlE,EAAOkE,KAAK,AACxBlE,CAAAA,EAAOkE,KAAK,CAAGlE,EAAOmE,KAAK,CAC3BnE,EAAOmE,KAAK,CAAGD,CACnB,CACJ,EACAD,IACA,IAAIG,EAAUrE,EAAO5C,SAAS,CAAC6G,gBAAgB,CAAC3G,IAAI,CAAC,IAAI,EAEzD,OADA4G,IACOG,CACX,EAMAT,EAAaxG,SAAS,CAACkH,QAAQ,CAAG,SAAUC,CAAK,CAAEC,CAAG,CAAEC,CAAG,EAEvD,IAAIC,EAAQH,EAAMf,SAAS,CAAC,MAAQ,EAAE,CAAEmB,EAASJ,EAAMf,SAAS,CAAC,MACjEe,EAAMK,SAAS,CAAC,IAAKD,EAAQ,KAAK,EAAG,CAAEE,OAAQ,CAAA,CAAK,GACpD,IAAIC,EAAc9E,EAAO5C,SAAS,CAACkH,QAAQ,CAAChH,IAAI,CAAC,IAAI,CACjDiH,EACAC,EACAC,GAGJ,OADAF,EAAMK,SAAS,CAAC,IAAKF,EAAMK,KAAK,CAACD,EAAYE,KAAK,CAAEF,EAAYG,GAAG,EAAG,KAAK,EAAG,CAAEJ,OAAQ,CAAA,CAAK,GACtFC,CACX,EAcAlB,EAAaxG,SAAS,CAAC8H,cAAc,CAAG,SAAUhF,CAAO,EACrD,IAIIiF,EAHAC,EAAYnE,AADP,IAAI,CACMmE,SAAS,CACxBC,EAASpE,AAFJ,IAAI,CAEGoE,MAAM,CAClBC,EAAKpF,EAAQoF,EAAE,CAEnB,GAAIA,EAAI,CACJ,IAAIhF,EAAQmC,EAAK4C,EACb,SAAU/E,CAAK,EAAI,OAAOA,EAAMgF,EAAE,GAAKA,CAAI,GAC/CH,EAAa7E,EAAQA,EAAMiF,KAAK,CAAG,KAAK,CAC5C,CACA,GAAI,AAAsB,KAAA,IAAfJ,EAA4B,CACnC,IAAI7E,EAAQmC,EAAK4C,EACb,SAAU/E,CAAK,EAAI,OAAQA,EAAMU,CAAC,GAAKd,EAAQc,CAAC,EAC5CV,EAAMY,EAAE,GAAKhB,EAAQgB,EAAE,EACvB,CAACZ,EAAMkF,OAAO,AAAG,GACzBL,EAAa7E,EAAQA,EAAMiF,KAAK,CAAG,KAAK,CAC5C,CAQA,OANI,IAAI,CAACE,OAAO,EACZ/C,EAAsByC,IACtBzC,EAAsB0C,IACtBD,GAAcC,GACdD,CAAAA,GAAcC,CAAQ,EAEnBD,CACX,EACAvB,EAAaxG,SAAS,CAACsI,cAAc,CAAG,SAAUpF,CAAK,EAGnD,IAFIW,EACAwC,EACAkC,EAAWrF,EAAMsF,KAAK,AAC1BtF,CAAAA,EAAMsF,KAAK,CAAG/C,EAAK,AAAuB,OAAtB5B,CAAAA,EAAKX,EAAMuF,KAAK,AAAD,GAAe5E,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6E,OAAO,CAAExF,EAAMsF,KAAK,EAC9FtF,EAAMyF,SAAS,EAAK,CAAA,AAA2B,OAA1BtC,CAAAA,EAAKnD,EAAM0F,SAAS,AAAD,GAAevC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGwC,KAAK,AAAD,GACvF3F,EAAMyF,SAAS,CAACG,GAAG,CAAC,CAChBD,MAAO,GAAK3F,EAAM0F,SAAS,CAACC,KAAK,CAAG,IACxC,GAEJjG,EAAO5C,SAAS,CAACsI,cAAc,CAAClE,KAAK,CAAC,IAAI,CAAEC,WAC5CnB,EAAMsF,KAAK,CAAGD,CAClB,EAIA/B,EAAaxG,SAAS,CAAC+I,cAAc,CAAG,SAAU7F,CAAK,EAInD,IAHIW,EACAwC,EACAC,EAkBA0C,EACA7H,EAGA8H,EACAC,EACAC,EACAC,EAxBArC,EAAQ,IAAI,CAACA,KAAK,CAClBC,EAAQ,IAAI,CAACA,KAAK,CAClBC,EAAU,IAAI,CAACoC,aAAa,CAC5BvG,EAAU,IAAI,CAACA,OAAO,CACtBwG,EAAiBxG,EAAQwG,cAAc,EAAI,EAC3CC,EAAc,AAAC,CAAA,AAAC,CAAA,AAA2B,OAA1B1F,CAAAA,EAAKX,EAAM0F,SAAS,AAAD,GAAe/E,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGgF,KAAK,AAAD,GAAM,CAAA,EAAK,EAC9FW,EAAgB,IAAI,CAACC,YAAY,CAAGxC,EAAQyC,MAAM,CAClDC,EAAOlE,EAAKvC,EAAMY,EAAE,CACpBZ,EAAMU,CAAC,CAAIV,CAAAA,EAAM0G,GAAG,EAAI,CAAA,GACxBjI,EAAemB,EAAQnB,YAAY,CACnCkI,EAAU,IAAI,CAACzG,KAAK,CAACyG,OAAO,CAC5BC,EAAW,IAAI,CAAC1G,KAAK,CAAC0G,QAAQ,CAC9BtB,EAAQtF,EAAMsF,KAAK,CACnBuB,EAAShD,EAAMiD,SAAS,CAACL,EAAM,EAAG,EAAG,EAAG,GACxCrG,EAAS2G,KAAKC,GAAG,CAACH,EAASvB,GAC3B2B,EAAW,IAAI,CAAC/G,KAAK,CAAC+G,QAAQ,CAC9BC,EAAc3E,EAAK3C,EAAQsH,WAAW,CAAE,GAGxCC,EAAUpD,EAAQyC,MAAM,CACxBY,EAAcL,KAAKM,KAAK,CAACtD,EAAQ4B,KAAK,EAKtCS,IACAN,CAAAA,EAAkBM,EAAiBhG,CAAK,EAClB,GAClB0F,CAAAA,EAAkB,CAAA,EAEtBR,GAASQ,EAAkB,EAC3Be,GAAUf,EAAkB,GAEhCR,EAAQyB,KAAK5C,GAAG,CAACmB,EAAO,KACxBuB,EAAS9E,EAAM8E,EAAQ,IAAKhD,EAAM6C,GAAG,CAAG,IAEpCzE,EAAQjC,EAAMJ,OAAO,CAAC0H,UAAU,IAChCH,GAAY,AAACJ,CAAAA,KAAKQ,IAAI,CAACvH,EAAMJ,OAAO,CAAC0H,UAAU,EAAIF,CAAU,EAAK,EAClEA,EAAcL,KAAKQ,IAAI,CAACvH,EAAMJ,OAAO,CAAC0H,UAAU,GAGhD1H,EAAQ4H,cAAc,EACtBpF,EAAsBpC,EAAMyH,KAAK,GACjC3D,EAAM4D,UAAU,EAChB1H,CAAAA,EAAMyH,KAAK,CAAG3D,EAAMgD,SAAS,CAAC9G,EAAMF,CAAC,CAAE,EAAG,EAAG,EAAG,EAAGF,EAAQ4H,cAAc,CAAA,EAE7E,IAAI9G,EAAIsB,EAAM+E,KAAK7C,GAAG,CAACoB,EACnBuB,GACAK,GAIAvB,EAAQ/E,AAHHoB,EAAM+E,KAAK5C,GAAG,CAACmB,EACpBuB,GACAK,GACaxG,EACbiH,EAAIZ,KAAK7C,GAAG,CAACzB,EAAgB,AAAwB,UAAxB,OAAOhE,EAChCA,EAAamJ,MAAM,CACnBnJ,GAAgB,EACpB2I,GACAL,KAAK7C,GAAG,CAACyB,EACTyB,GAAe,GACf1B,EAAY,CACRhF,EAAGA,EACHZ,EAAGkC,EAAM,AAAChC,CAAAA,EAAMyH,KAAK,EAAI,CAAA,EAAKN,EAClCD,GACIvB,MAAOA,EACPkC,OAAQT,EACRO,EAAGA,CACP,CACJ3H,CAAAA,EAAM0F,SAAS,CAAGA,EAEbuB,EAMDjH,EAAM8H,UAAU,CAAC,EAAE,EAAIxB,EACnBD,EANJrG,EAAM8H,UAAU,CAAC,EAAE,EAAIzB,EACnBC,EACAZ,EAAUC,KAAK,CAAG,EAQ1BK,EAAUD,AADVA,CAAAA,EAASL,EAAUhF,CAAC,AAADA,EACAgF,EAAUC,KAAK,CAC9BI,EAAS,GAAKC,EAAUnC,EAAM6C,GAAG,EACjCX,EAAShE,EAAMgE,EAAQ,EAAGlC,EAAM6C,GAAG,EAEnCT,EAAUD,AADVA,CAAAA,EAAUjE,EAAMiE,EAAS,EAAGnC,EAAM6C,GAAG,CAAA,EACjBX,EACpB/F,EAAMuF,KAAK,CAAGjD,EAAMoD,EAAW,CAC3BhF,EAAGqF,EACHJ,MAAOK,EAAUD,EACjBP,QAASS,EAAUA,EAAU,EAAI,IACrC,IAGAjG,EAAMuF,KAAK,CAAG,KAGlB,IAAIuC,EAAa9H,EAAM8H,UAAU,CAC7BC,EAAS,GAACd,EACVe,EAAS,EAACf,EACVgB,EAAkB,IAAI,CAAC9B,aAAa,CAChC,IAAI,CAACA,aAAa,CAACK,MAAM,CACzB,CAACzC,EAAQ4B,KAAK,CAAG,EAErBsB,EACAa,CAAU,CAACC,EAAO,EAAIrC,EAAUC,KAAK,CAAG,EAGxCmC,CAAU,CAACC,EAAO,CAAGhG,EAAM+F,CAAU,CAACC,EAAO,CACzC,AAAClE,CAAAA,EAAMqE,QAAQ,CAAG,GAAK,CAAA,EAAKxC,EAAUC,KAAK,CAAE9B,EAAMsE,IAAI,CAAGvB,EAAU/C,EAAMsE,IAAI,CAAGtE,EAAM6C,GAAG,CAAGE,EAAW,GAEhHkB,CAAU,CAACE,EAAO,CAAGjG,EAAM+F,CAAU,CAACE,EAAO,CAAI,AAACf,CAAAA,EAAW,GAAK,CAAA,EAAKgB,EAAiBnE,EAAMsE,GAAG,CAAGzB,EAAS7C,EAAMsE,GAAG,CAAGtE,EAAM4C,GAAG,CAAGC,EAAU,GAE/I1I,CAAAA,EAAc+B,EAAM/B,WAAW,AAAD,IAGtBoE,EAAsBpE,IACtBA,CAAAA,EAAcA,EAAYD,MAAM,AAAD,EAG9BoE,EAAsBnE,IACvBA,CAAAA,EAAc,CAAA,EAElB+B,EAAMqI,aAAa,CAAG/F,EAAMoD,GAC5BQ,EAAgBa,KAAK5C,GAAG,CAAC4C,KAAKM,KAAK,CAACjH,EAASnC,EAAc+B,EAAMsF,KAAK,CAClEA,GAAQ,GACZtF,EAAMsI,YAAY,CAAG,CACjB5H,EAAGmD,EAAMqE,QAAQ,CACbxC,EAAUhF,CAAC,CAAGN,EAAS8F,EACvBR,EAAUhF,CAAC,CACfZ,EAAG4F,EAAU5F,CAAC,CACd6F,MAAOO,EACP2B,OAAQnC,EAAUmC,MAAM,AAC5B,GAKJ7H,EAAM1D,GAAG,CAAG0D,EAAMuI,QAAQ,EAAIvI,EAAMwI,IAAI,CACxCxI,EAAMyI,SAAS,CAAG,AAA4B,OAA3BtF,CAAAA,EAAKW,EAAM4D,UAAU,AAAD,GAAevE,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC,AAAmB,OAAlBC,CAAAA,EAAKpD,EAAMF,CAAC,AAADA,GAAesD,AAAO,KAAK,IAAZA,EAAgBA,EAAK,GAAG,AACzI,EAIAE,EAAaxG,SAAS,CAACgK,SAAS,CAAG,WAC/BpH,EAAO5C,SAAS,CAACgK,SAAS,CAAC5F,KAAK,CAAC,IAAI,CAAEC,WACvC,IAAK,IAAI6B,EAAK,EAAGrC,EAAK,IAAI,CAACoE,MAAM,CAAE/B,EAAKrC,EAAGP,MAAM,CAAE4C,IAAM,CACrD,IAAIhD,EAAQW,CAAE,CAACqC,EAAG,CAClB,IAAI,CAAC6C,cAAc,CAAC7F,EACxB,CACJ,EAeAsD,EAAaxG,SAAS,CAACsE,SAAS,CAAG,SAAUpB,CAAK,CAAE0I,CAAI,EACpD,IAAIC,EAAa,IAAI,CAAC/I,OAAO,CACzBgJ,EAAW,IAAI,CAAC1I,KAAK,CAAC0I,QAAQ,CAC9BC,EAAO7I,EAAM8I,SAAS,CACtBpD,EAAY1F,EAAM0F,SAAS,CAC3B2C,EAAgBrI,EAAMqI,aAAa,CACnCC,EAAetI,EAAMsI,YAAY,CACjCS,EAAa/I,EAAMgJ,KAAK,CACxBC,EAAaN,EAAWO,MAAM,CAACH,GAAc,SAAS,EAClD,CAAC,EACLI,EAAiB,AAAsB,KAAA,IAAfJ,EACpB,OAASL,EACbU,EAAY,IAAI,CAACC,YAAY,CAACrJ,EAC9B+I,GACAO,EAAY/G,EAAK,IAAI,CAACrC,KAAK,CAACN,OAAO,CAACM,KAAK,CAACoJ,SAAS,CACnDL,EAAUK,SAAS,EACnBC,EAAUvJ,EAAMuJ,OAAO,CACvBC,EAAYxJ,EAAM/B,WAAW,CACjC,GAAI,AAAC+B,EAAMe,MAAM,EAAIf,AAAkB,CAAA,IAAlBA,EAAMyJ,OAAO,CAqDzBF,GACLvJ,CAAAA,EAAMuJ,OAAO,CAAGA,EAAQG,OAAO,EAAC,OAxBhC,GA5BIH,EACAA,EAAQI,IAAI,CAACjB,EAAK,CAAChD,IAGnB1F,EAAMuJ,OAAO,CAAGA,EAAUX,EAASgB,CAAC,CAAC,SAChCC,QAAQ,CAAC7J,EAAM8J,YAAY,IAC3BC,GAAG,CAAC/J,EAAMgK,KAAK,EAAI,IAAI,CAACA,KAAK,EAClCT,EAAQI,IAAI,CAAGf,CAAQ,CAACC,EAAK,CAACvG,EAAMoD,IAC/BmE,QAAQ,CAAC7J,EAAM8J,YAAY,IAC3BD,QAAQ,CAAC,gCACTE,GAAG,CAACR,IAGTlB,IACIkB,EAAQU,QAAQ,EAChBV,EAAQU,QAAQ,CAACvB,EAAK,CAACpG,EAAM+F,IAC7BkB,EAAQW,eAAe,CAACxB,EAAK,CAACpG,EAAMgG,MAGpCiB,EAAQW,eAAe,CAAGtB,EAASuB,QAAQ,CAAC7B,EAAa5H,CAAC,CAAE4H,EAAaxI,CAAC,CAAEwI,EAAa3C,KAAK,CAAE2C,EAAaT,MAAM,EACnH0B,EAAQU,QAAQ,CACZrB,CAAQ,CAACC,EAAK,CAACR,GACVwB,QAAQ,CAAC,+BACTE,GAAG,CAACR,GACJa,IAAI,CAACb,EAAQW,eAAe,IAIzC,CAAC,IAAI,CAAChK,KAAK,CAACM,UAAU,GACtB+I,EACKI,IAAI,CAACjB,EAAK,CAACU,EAAWE,GACtBe,MAAM,CAAC1B,EAAW0B,MAAM,EACzBhC,GAAe,CAEVhG,EAAsBmH,IACvBA,CAAAA,EAAY,CAAC,CAAA,EAEbnH,EAAsBsG,EAAW1K,WAAW,GAC5CuL,CAAAA,EAAYlH,EAAMqG,EAAW1K,WAAW,CAAEuL,EAAS,EAEvD,IAAIc,EAAQd,EAAUc,IAAI,EAClBhK,EAAM8I,EAAUkB,IAAI,EAAEC,QAAQ,CAAC,KAAM5N,GAAG,IACxC2D,EAAMN,EAAMM,KAAK,EAAI,IAAI,CAACA,KAAK,EAC1BiK,QAAQ,CAAC,KAAM5N,GAAG,EAC/ByM,CAAAA,EAAUkB,IAAI,CAAGA,EACjBf,EACKU,QAAQ,CAACd,EAAe,CAACC,EAAWE,GACpCe,MAAM,CAAC1B,EAAW0B,MAAM,CACjC,CAMZ,EAIA/G,EAAaxG,SAAS,CAAC0N,UAAU,CAAG,WAGhC,IAAK,IAFD9B,EAAO,IAAI,CAACrH,gBAAgB,GAEvB2B,EAAK,EAAGrC,EAAK,IAAI,CAACoE,MAAM,CAAE/B,EAAKrC,EAAGP,MAAM,CAAE4C,IAAM,CACrD,IAAIhD,EAAQW,CAAE,CAACqC,EAAG,CAClB,IAAI,CAAC5B,SAAS,CAACpB,EAAO0I,EAC1B,CACJ,EAOApF,EAAaxG,SAAS,CAACuE,gBAAgB,CAAG,WACtC,OAAQ,IAAI,CAACnB,KAAK,CAACuK,UAAU,CAAI,CAAA,IAAI,CAAC7K,OAAO,CAAC8K,cAAc,EAAI,GAAE,EAC9D,UACA,MACR,EAIApH,EAAaxG,SAAS,CAAC6N,aAAa,CAAG,SAAU3K,CAAK,EAClD,IAAI0F,EAAY1F,EAAM0F,SAAS,CAC3BJ,EAAQtF,EAAMsF,KAAK,CACnBmC,EAAQzH,EAAMyH,KAAK,QACvB,AAAK/B,EAGU,AAAiB,KAAA,IAAVJ,GACd,AAAiB,KAAA,IAAVmC,GACPA,GAAS,GACTA,GAAS,IAAI,CAAC3D,KAAK,CAAC4C,GAAG,EACvB,AAAChB,CAAAA,EAAUhF,CAAC,EAAI,CAAA,EAAMgF,CAAAA,EAAUC,KAAK,EAAI,CAAA,GAAM,GAC/CL,GAAS,IAAI,CAACzB,KAAK,CAAC6C,GAAG,CAPpBhH,EAAO5C,SAAS,CAAC6N,aAAa,CAACzJ,KAAK,CAAC,IAAI,CAAEC,UAS1D,EAMAmC,EAAasH,cAAc,CAAGtI,EAAMT,EAAa+I,cAAc,CAAEhN,GAC1D0F,CACX,EAAEzB,GACFK,EAAoBoB,EAAaxG,SAAS,CAAE,CACxCyC,WA3gBmDE,EA4gBnDoL,cAAe,CAAC,KAAM,IAAI,CAC1BC,mBAAoB,CAAA,EACpBC,gBAAiB,CAAC,IAAI,CACtBC,eAAgB,CAAC,IAAK,KAAM,IAAI,CAChCC,eAAgB,CAAA,EAChBpC,KAAM,SACNqC,QAAS,AAAC1N,IAA2ImC,MAAM,CAAC7C,SAAS,CAACoO,OAAO,CAC7KC,cAAevJ,EACfwJ,YAAaxJ,CACjB,GACApE,IAA0I6N,kBAAkB,CAAC,SAAU/H,GAavK,IAAIgI,EAAKlO,IACTmO,AARwDjI,EAQpCC,OAAO,CAAC+H,EAAEE,IAAI,EACL,IAAItO,EAAeE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,GAET"}