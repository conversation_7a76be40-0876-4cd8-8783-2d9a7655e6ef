{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/vector\n * @requires highcharts\n *\n * Vector plot series module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/vector\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/vector\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ vector_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Vector/VectorSeriesDefaults.js\n/* *\n *\n *  Vector plot series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A vector plot is a type of cartesian chart where each point has an X and\n * Y position, a length and a direction. Vectors are drawn as arrows.\n *\n * @sample {highcharts|highstock} highcharts/demo/vector-plot/\n *         Vector pot\n *\n * @since        6.0.0\n * @extends      plotOptions.scatter\n * @excluding    boostThreshold, marker, connectEnds, connectNulls,\n *               cropThreshold, dashStyle, dragDrop, gapSize, gapUnit,\n *               dataGrouping, linecap, shadow, stacking, step, jitter,\n *               boostBlending\n * @product      highcharts highstock\n * @requires     modules/vector\n * @optionparent plotOptions.vector\n */\nvar VectorSeriesDefaults = {\n    /**\n     * The line width for each vector arrow.\n     */\n    lineWidth: 2,\n    marker: void 0,\n    /**\n     * What part of the vector it should be rotated around. Can be one of\n     * `start`, `center` and `end`. When `start`, the vectors will start\n     * from the given [x, y] position, and when `end` the vectors will end\n     * in the [x, y] position.\n     *\n     * @sample highcharts/plotoptions/vector-rotationorigin-start/\n     *         Rotate from start\n     *\n     * @validvalue [\"start\", \"center\", \"end\"]\n     */\n    rotationOrigin: 'center',\n    states: {\n        hover: {\n            /**\n             * Additonal line width for the vector errors when they are\n             * hovered.\n             */\n            lineWidthPlus: 1\n        }\n    },\n    tooltip: {\n        /**\n         * @default [{point.x}, {point.y}] Length: {point.length} Direction: {point.direction}°\n         */\n        pointFormat: '<b>[{point.x}, {point.y}]</b><br/>Length: <b>{point.length}</b><br/>Direction: <b>{point.direction}\\u00B0</b><br/>'\n    },\n    /**\n     * Maximum length of the arrows in the vector plot. The individual arrow\n     * length is computed between 0 and this value.\n     */\n    vectorLength: 20\n};\n/**\n * A `vector` series. If the [type](#series.vector.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.vector\n * @excluding dataParser, dataURL, boostThreshold, boostBlending\n * @product   highcharts highstock\n * @requires  modules/vector\n * @apioption series.vector\n */\n/**\n * An array of data points for the series. For the `vector` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 4 values. In this case, the values correspond to\n *    to `x,y,length,direction`. If the first value is a string, it is applied\n *    as the name of the point, and the `x` value is inferred.\n *    ```js\n *    data: [\n *        [0, 0, 10, 90],\n *        [0, 1, 5, 180],\n *        [1, 1, 2, 270]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.area.turboThreshold), this option is not\n *    available.\n *    ```js\n *    data: [{\n *        x: 0,\n *        y: 0,\n *        name: \"Point2\",\n *        length: 10,\n *        direction: 90\n *    }, {\n *        x: 1,\n *        y: 1,\n *        name: \"Point1\",\n *        direction: 270\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number,number,number>|*>}\n * @extends   series.line.data\n * @product   highcharts highstock\n * @apioption series.vector.data\n */\n/**\n * The length of the vector. The rendered length will relate to the\n * `vectorLength` setting.\n *\n * @type      {number}\n * @product   highcharts highstock\n * @apioption series.vector.data.length\n */\n/**\n * The vector direction in degrees, where 0 is north (pointing towards south).\n *\n * @type      {number}\n * @product   highcharts highstock\n * @apioption series.vector.data.direction\n */\n''; // Adds doclets above to the transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Vector_VectorSeriesDefaults = (VectorSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Vector/VectorSeries.js\n/* *\n *\n *  Vector plot series module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\n\n\nvar Series = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series, ScatterSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.scatter;\n\nvar arrayMax = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).arrayMax, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The vector series class.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.vector\n *\n * @augments Highcharts.seriesTypes.scatter\n */\nvar VectorSeries = /** @class */ (function (_super) {\n    __extends(VectorSeries, _super);\n    function VectorSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Fade in the arrows on initializing series.\n     * @private\n     */\n    VectorSeries.prototype.animate = function (init) {\n        if (init) {\n            this.markerGroup.attr({\n                opacity: 0.01\n            });\n        }\n        else {\n            this.markerGroup.animate({\n                opacity: 1\n            }, animObject(this.options.animation));\n        }\n    };\n    /**\n     * Create a single arrow. It is later rotated around the zero\n     * centerpoint.\n     * @private\n     */\n    VectorSeries.prototype.arrow = function (point) {\n        var fraction = point.length / this.lengthMax, u = fraction * this.options.vectorLength / 20, o = {\n                start: 10 * u,\n                center: 0,\n                end: -10 * u\n            }[this.options.rotationOrigin] || 0, \n            // The stem and the arrow head. Draw the arrow first with rotation\n            // 0, which is the arrow pointing down (vector from north to south).\n            path = [\n                ['M', 0, 7 * u + o], // Base of arrow\n                ['L', -1.5 * u, 7 * u + o],\n                ['L', 0, 10 * u + o],\n                ['L', 1.5 * u, 7 * u + o],\n                ['L', 0, 7 * u + o],\n                ['L', 0, -10 * u + o] // Top\n            ];\n        return path;\n    };\n    /*\n    DrawLegendSymbol: function (legend, item) {\n        let options = legend.options,\n            symbolHeight = legend.symbolHeight,\n            square = options.squareSymbol,\n            symbolWidth = square ? symbolHeight : legend.symbolWidth,\n            path = this.arrow.call({\n                lengthMax: 1,\n                options: {\n                    vectorLength: symbolWidth\n                }\n            }, {\n                length: 1\n            });\n        legendItem.line = this.chart.renderer.path(path)\n        .addClass('highcharts-point')\n        .attr({\n            zIndex: 3,\n            translateY: symbolWidth / 2,\n            rotation: 270,\n            'stroke-width': 1,\n            'stroke': 'black'\n        }).add(item.legendItem.group);\n    },\n    */\n    /**\n     * @private\n     */\n    VectorSeries.prototype.drawPoints = function () {\n        var chart = this.chart;\n        for (var _i = 0, _a = this.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            var plotX = point.plotX,\n                plotY = point.plotY;\n            if (this.options.clip === false ||\n                chart.isInsidePlot(plotX, plotY, { inverted: chart.inverted })) {\n                if (!point.graphic) {\n                    point.graphic = this.chart.renderer\n                        .path()\n                        .add(this.markerGroup)\n                        .addClass('highcharts-point ' +\n                        'highcharts-color-' +\n                        pick(point.colorIndex, point.series.colorIndex));\n                }\n                point.graphic\n                    .attr({\n                    d: this.arrow(point),\n                    translateX: plotX,\n                    translateY: plotY,\n                    rotation: point.direction\n                });\n                if (!this.chart.styledMode) {\n                    point.graphic\n                        .attr(this.pointAttribs(point));\n                }\n            }\n            else if (point.graphic) {\n                point.graphic = point.graphic.destroy();\n            }\n        }\n    };\n    /**\n     * Get presentational attributes.\n     * @private\n     */\n    VectorSeries.prototype.pointAttribs = function (point, state) {\n        var options = this.options;\n        var stroke = (point === null || point === void 0 ? void 0 : point.color) || this.color,\n            strokeWidth = this.options.lineWidth;\n        if (state) {\n            stroke = options.states[state].color || stroke;\n            strokeWidth =\n                (options.states[state].lineWidth || strokeWidth) +\n                    (options.states[state].lineWidthPlus || 0);\n        }\n        return {\n            'stroke': stroke,\n            'stroke-width': strokeWidth\n        };\n    };\n    /**\n     * @private\n     */\n    VectorSeries.prototype.translate = function () {\n        Series.prototype.translate.call(this);\n        this.lengthMax = arrayMax(this.getColumn('length'));\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    VectorSeries.defaultOptions = merge(ScatterSeries.defaultOptions, Vector_VectorSeriesDefaults);\n    return VectorSeries;\n}(ScatterSeries));\nextend(VectorSeries.prototype, {\n    /**\n     * @ignore\n     * @deprecated\n     */\n    drawGraph: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop,\n    /**\n     * @ignore\n     * @deprecated\n     */\n    getSymbol: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop,\n    /**\n     * @ignore\n     * @deprecated\n     */\n    markerAttribs: (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop,\n    parallelArrays: ['x', 'y', 'length', 'direction'],\n    pointArrayMap: ['y', 'length', 'direction']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('vector', VectorSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Vector_VectorSeries = ((/* unused pure expression or super */ null && (VectorSeries)));\n\n;// ./code/es5/es-modules/masters/modules/vector.js\n\n\n\n\n/* harmony default export */ var vector_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "vector_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "Vector_VectorSeriesDefaults", "lineWidth", "marker", "<PERSON><PERSON><PERSON><PERSON>", "states", "hover", "lineWidthPlus", "tooltip", "pointFormat", "vectorLength", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "animObject", "Series", "series", "ScatterSeries", "seriesTypes", "scatter", "arrayMax", "extend", "merge", "pick", "VectorSeries", "_super", "apply", "arguments", "animate", "init", "markerGroup", "attr", "opacity", "options", "animation", "arrow", "point", "u", "fraction", "length", "lengthMax", "start", "center", "end", "drawPoints", "chart", "_i", "_a", "points", "plotX", "plotY", "clip", "isInsidePlot", "inverted", "graphic", "renderer", "path", "add", "addClass", "colorIndex", "translateX", "translateY", "rotation", "direction", "styledMode", "pointAttribs", "destroy", "state", "stroke", "color", "strokeWidth", "translate", "getColumn", "defaultOptions", "drawGraph", "noop", "getSymbol", "markerAttribs", "parallelArrays", "pointArrayMap", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACtG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE7GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAgQFC,EAhQMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAY,CAC3D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GA2JhJE,EAvHN,CAIvBC,UAAW,EACXC,OAAQ,KAAK,EAYbC,eAAgB,SAChBC,OAAQ,CACJC,MAAO,CAKHC,cAAe,CACnB,CACJ,EACAC,QAAS,CAILC,YAAa,kHACjB,EAKAC,aAAc,EAClB,EAgGIC,GACIxC,EAAgB,SAAUU,CAAC,CAC3B+B,CAAC,EAMD,MAAOzC,AALHA,CAAAA,EAAgBe,OAAO2B,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUlC,CAAC,CAC1D+B,CAAC,EAAI/B,EAAEiC,SAAS,CAAGF,CAAG,GACd,SAAU/B,CAAC,CACnB+B,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAEnB,cAAc,CAACuB,IAAInC,CAAAA,CAAC,CAACmC,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCnC,EAAG+B,EAC5B,EACO,SAAU/B,CAAC,CAAE+B,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAGrC,CAAG,CADtCV,EAAcU,EAAG+B,GAEjB/B,EAAEW,SAAS,CAAGoB,AAAM,OAANA,EAAa1B,OAAOiC,MAAM,CAACP,GAAMK,CAAAA,EAAGzB,SAAS,CAAGoB,EAAEpB,SAAS,CAAE,IAAIyB,CAAG,CACtF,GAGAG,EAAa,AAACtB,IAA+EsB,UAAU,CAGvGC,EAAS,AAACrB,IAA2IsB,MAAM,CAAEC,EAAgB,AAACvB,IAA2IwB,WAAW,CAACC,OAAO,CAE5UC,EAAW,AAAC5B,IAA+E4B,QAAQ,CAAEC,EAAS,AAAC7B,IAA+E6B,MAAM,CAAEC,EAAQ,AAAC9B,IAA+E8B,KAAK,CAAEC,EAAO,AAAC/B,IAA+E+B,IAAI,CAgBhYC,EAA8B,SAAUC,CAAM,EAE9C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CA0IA,OA7IAtB,EAAUmB,EAAcC,GAaxBD,EAAatC,SAAS,CAAC0C,OAAO,CAAG,SAAUC,CAAI,EACvCA,EACA,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,CAClBC,QAAS,GACb,GAGA,IAAI,CAACF,WAAW,CAACF,OAAO,CAAC,CACrBI,QAAS,CACb,EAAGlB,EAAW,IAAI,CAACmB,OAAO,CAACC,SAAS,EAE5C,EAMAV,EAAatC,SAAS,CAACiD,KAAK,CAAG,SAAUC,CAAK,EAC1C,IAA8CC,EAAIC,AAAnCF,EAAMG,MAAM,CAAG,IAAI,CAACC,SAAS,CAAiB,IAAI,CAACP,OAAO,CAAC7B,YAAY,CAAG,GAAIzB,EAAI,CACzF8D,MAAO,GAAKJ,EACZK,OAAQ,EACRC,IAAK,IAAMN,CACf,CAAC,CAAC,IAAI,CAACJ,OAAO,CAACnC,cAAc,CAAC,EAAI,EAWtC,MARW,CACH,CAAC,IAAK,EAAG,EAAIuC,EAAI1D,EAAE,CACnB,CAAC,IAAK,KAAO0D,EAAG,EAAIA,EAAI1D,EAAE,CAC1B,CAAC,IAAK,EAAG,GAAK0D,EAAI1D,EAAE,CACpB,CAAC,IAAK,IAAM0D,EAAG,EAAIA,EAAI1D,EAAE,CACzB,CAAC,IAAK,EAAG,EAAI0D,EAAI1D,EAAE,CACnB,CAAC,IAAK,EAAG,IAAM0D,EAAI1D,EAAE,CACxB,AAET,EA6BA6C,EAAatC,SAAS,CAAC0D,UAAU,CAAG,WAEhC,IAAK,IADDC,EAAQ,IAAI,CAACA,KAAK,CACbC,EAAK,EAAGC,EAAK,IAAI,CAACC,MAAM,CAAEF,EAAKC,EAAGR,MAAM,CAAEO,IAAM,CACrD,IAAIV,EAAQW,CAAE,CAACD,EAAG,CACdG,EAAQb,EAAMa,KAAK,CACnBC,EAAQd,EAAMc,KAAK,AACnB,AAAsB,EAAA,IAAtB,IAAI,CAACjB,OAAO,CAACkB,IAAI,EACjBN,EAAMO,YAAY,CAACH,EAAOC,EAAO,CAAEG,SAAUR,EAAMQ,QAAQ,AAAC,IACvDjB,EAAMkB,OAAO,EACdlB,CAAAA,EAAMkB,OAAO,CAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAC9BC,IAAI,GACJC,GAAG,CAAC,IAAI,CAAC3B,WAAW,EACpB4B,QAAQ,CAAC,qCAEVnC,EAAKa,EAAMuB,UAAU,CAAEvB,EAAMpB,MAAM,CAAC2C,UAAU,EAAC,EAEvDvB,EAAMkB,OAAO,CACRvB,IAAI,CAAC,CACNxD,EAAG,IAAI,CAAC4D,KAAK,CAACC,GACdwB,WAAYX,EACZY,WAAYX,EACZY,SAAU1B,EAAM2B,SAAS,AAC7B,GACK,IAAI,CAAClB,KAAK,CAACmB,UAAU,EACtB5B,EAAMkB,OAAO,CACRvB,IAAI,CAAC,IAAI,CAACkC,YAAY,CAAC7B,KAG3BA,EAAMkB,OAAO,EAClBlB,CAAAA,EAAMkB,OAAO,CAAGlB,EAAMkB,OAAO,CAACY,OAAO,EAAC,CAE9C,CACJ,EAKA1C,EAAatC,SAAS,CAAC+E,YAAY,CAAG,SAAU7B,CAAK,CAAE+B,CAAK,EACxD,IAAIlC,EAAU,IAAI,CAACA,OAAO,CACtBmC,EAAS,AAAChC,CAAAA,MAAAA,EAAqC,KAAK,EAAIA,EAAMiC,KAAK,AAAD,GAAM,IAAI,CAACA,KAAK,CAClFC,EAAc,IAAI,CAACrC,OAAO,CAACrC,SAAS,CAOxC,OANIuE,IACAC,EAASnC,EAAQlC,MAAM,CAACoE,EAAM,CAACE,KAAK,EAAID,EACxCE,EACI,AAACrC,CAAAA,EAAQlC,MAAM,CAACoE,EAAM,CAACvE,SAAS,EAAI0E,CAAU,EACzCrC,CAAAA,EAAQlC,MAAM,CAACoE,EAAM,CAAClE,aAAa,EAAI,CAAA,GAE7C,CACH,OAAUmE,EACV,eAAgBE,CACpB,CACJ,EAIA9C,EAAatC,SAAS,CAACqF,SAAS,CAAG,WAC/BxD,EAAO7B,SAAS,CAACqF,SAAS,CAACnF,IAAI,CAAC,IAAI,EACpC,IAAI,CAACoD,SAAS,CAAGpB,EAAS,IAAI,CAACoD,SAAS,CAAC,UAC7C,EAMAhD,EAAaiD,cAAc,CAAGnD,EAAML,EAAcwD,cAAc,CAAE9E,GAC3D6B,CACX,EAAEP,GACFI,EAAOG,EAAatC,SAAS,CAAE,CAK3BwF,UAAW,AAAClF,IAA+EmF,IAAI,CAK/FC,UAAW,AAACpF,IAA+EmF,IAAI,CAK/FE,cAAe,AAACrF,IAA+EmF,IAAI,CACnGG,eAAgB,CAAC,IAAK,IAAK,SAAU,YAAY,CACjDC,cAAe,CAAC,IAAK,SAAU,YAAY,AAC/C,GACArF,IAA0IsF,kBAAkB,CAAC,SAAUxD,GAa1I,IAAIlC,EAAeE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,GAET"}