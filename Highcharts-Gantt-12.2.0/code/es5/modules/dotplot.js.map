{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/dotplot\n * @requires highcharts\n *\n * Dot plot series type for Highcharts\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/dotplot\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/dotplot\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ dotplot_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Series/DotPlot/DotPlotSeriesDefaults.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  Dot plot series type for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\nvar DotPlotSeriesDefaults = {\n    itemPadding: 0.1,\n    marker: {\n        symbol: 'circle',\n        states: {\n            hover: {},\n            select: {}\n        }\n    },\n    slotsPerBar: void 0\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var DotPlot_DotPlotSeriesDefaults = (DotPlotSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/DotPlot/DotPlotSeries.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  Dot plot series type for Highcharts\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/**\n * @private\n * @todo\n * - Check update, remove etc.\n * - Custom icons like persons, carts etc. Either as images, font icons or\n *   Highcharts symbols.\n */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.dotplot\n *\n * @augments Highcharts.Series\n */\nvar DotPlotSeries = /** @class */ (function (_super) {\n    __extends(DotPlotSeries, _super);\n    function DotPlotSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    DotPlotSeries.prototype.drawPoints = function () {\n        var _a,\n            _b,\n            _c;\n        var series = this,\n            options = series.options,\n            renderer = series.chart.renderer,\n            seriesMarkerOptions = options.marker,\n            total = this.points.reduce(function (acc,\n            point) { return acc + Math.abs(point.y || 0); }, 0),\n            totalHeight = this.points.reduce(function (acc,\n            point) { var _a; return acc + (((_a = point.shapeArgs) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0),\n            itemPadding = options.itemPadding || 0,\n            columnWidth = ((_b = (_a = this.points[0]) === null || _a === void 0 ? void 0 : _a.shapeArgs) === null || _b === void 0 ? void 0 : _b.width) || 0;\n        var slotsPerBar = options.slotsPerBar,\n            slotWidth = columnWidth;\n        // Find the suitable number of slots per column\n        if (!isNumber(slotsPerBar)) {\n            slotsPerBar = 1;\n            while (slotsPerBar < total) {\n                if (total / slotsPerBar <\n                    (totalHeight / slotWidth) * 1.2) {\n                    break;\n                }\n                slotsPerBar++;\n                slotWidth = columnWidth / slotsPerBar;\n            }\n        }\n        var height = (totalHeight * slotsPerBar) / total;\n        for (var _i = 0, _d = series.points; _i < _d.length; _i++) {\n            var point = _d[_i];\n            var pointMarkerOptions = point.marker || {},\n                symbol = (pointMarkerOptions.symbol ||\n                    seriesMarkerOptions.symbol),\n                radius = pick(pointMarkerOptions.radius,\n                seriesMarkerOptions.radius),\n                isSquare = symbol !== 'rect',\n                width = isSquare ? height : slotWidth,\n                shapeArgs = point.shapeArgs || {},\n                startX = (shapeArgs.x || 0) + ((shapeArgs.width || 0) -\n                    slotsPerBar * width) / 2,\n                positiveYValue = Math.abs((_c = point.y) !== null && _c !== void 0 ? _c : 0),\n                shapeY = (shapeArgs.y || 0),\n                shapeHeight = (shapeArgs.height || 0);\n            var graphics = void 0,\n                x = startX,\n                y = point.negative ? shapeY : shapeY + shapeHeight - height,\n                slotColumn = 0;\n            point.graphics = graphics = point.graphics || [];\n            var pointAttr = point.pointAttr ?\n                    (point.pointAttr[point.selected ? 'selected' : ''] ||\n                        series.pointAttr['']) :\n                    series.pointAttribs(point,\n                point.selected && 'select');\n            delete pointAttr.r;\n            if (series.chart.styledMode) {\n                delete pointAttr.stroke;\n                delete pointAttr['stroke-width'];\n            }\n            if (typeof point.y === 'number') {\n                if (!point.graphic) {\n                    point.graphic = renderer.g('point').add(series.group);\n                }\n                for (var val = 0; val < positiveYValue; val++) {\n                    var attr = {\n                            x: x + width * itemPadding,\n                            y: y + height * itemPadding,\n                            width: width * (1 - 2 * itemPadding),\n                            height: height * (1 - 2 * itemPadding),\n                            r: radius\n                        };\n                    var graphic = graphics[val];\n                    if (graphic) {\n                        graphic.animate(attr);\n                    }\n                    else {\n                        graphic = renderer\n                            .symbol(symbol)\n                            .attr(extend(attr, pointAttr))\n                            .add(point.graphic);\n                    }\n                    graphic.isActive = true;\n                    graphics[val] = graphic;\n                    x += width;\n                    slotColumn++;\n                    if (slotColumn >= slotsPerBar) {\n                        slotColumn = 0;\n                        x = startX;\n                        y = point.negative ? y + height : y - height;\n                    }\n                }\n            }\n            var i = -1;\n            for (var _e = 0, graphics_1 = graphics; _e < graphics_1.length; _e++) {\n                var graphic = graphics_1[_e];\n                ++i;\n                if (graphic) {\n                    if (!graphic.isActive) {\n                        graphic.destroy();\n                        graphics.splice(i, 1);\n                    }\n                    else {\n                        graphic.isActive = false;\n                    }\n                }\n            }\n        }\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    DotPlotSeries.defaultOptions = merge(ColumnSeries.defaultOptions, DotPlot_DotPlotSeriesDefaults);\n    return DotPlotSeries;\n}(ColumnSeries));\nextend(DotPlotSeries.prototype, {\n    markerAttribs: void 0\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('dotplot', DotPlotSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var DotPlot_DotPlotSeries = ((/* unused pure expression or super */ null && (DotPlotSeries)));\n\n;// ./code/es5/es-modules/masters/modules/dotplot.js\n\n\n\n\n/* harmony default export */ var dotplot_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dotplot_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "DotPlot_DotPlotSeriesDefaults", "itemPadding", "marker", "symbol", "states", "hover", "select", "slotsPerBar", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "ColumnSeries", "seriesTypes", "column", "extend", "isNumber", "merge", "pick", "DotPlotSeries", "_super", "apply", "arguments", "drawPoints", "_a", "_b", "_c", "options", "series", "renderer", "chart", "seriesMarkerOptions", "total", "points", "reduce", "acc", "point", "Math", "abs", "y", "totalHeight", "shapeArgs", "height", "columnWidth", "width", "slotWidth", "_i", "_d", "length", "pointMarkerOptions", "radius", "isSquare", "startX", "x", "positiveYValue", "shapeY", "shapeHeight", "graphics", "negative", "slotColumn", "pointAttr", "selected", "pointAttribs", "r", "styledMode", "stroke", "graphic", "g", "add", "group", "val", "attr", "animate", "isActive", "i", "_e", "graphics_1", "destroy", "splice", "defaultOptions", "markerAttribs", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACvG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE9GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IA+IFC,EA/IMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAa,CAC5D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAmCpFE,EAhBL,CACxBC,YAAa,GACbC,OAAQ,CACJC,OAAQ,SACRC,OAAQ,CACJC,MAAO,CAAC,EACRC,OAAQ,CAAC,CACb,CACJ,EACAC,YAAa,KAAK,CACtB,EASIC,EAAmIjC,EAAoB,KACvJkC,EAAuJlC,EAAoBI,CAAC,CAAC6B,GAqB7KE,GACItC,EAAgB,SAAUU,CAAC,CAC3B6B,CAAC,EAMD,MAAOvC,AALHA,CAAAA,EAAgBe,OAAOyB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUhC,CAAC,CAC1D6B,CAAC,EAAI7B,EAAE+B,SAAS,CAAGF,CAAG,GACd,SAAU7B,CAAC,CACnB6B,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAEjB,cAAc,CAACqB,IAAIjC,CAAAA,CAAC,CAACiC,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCjC,EAAG6B,EAC5B,EACO,SAAU7B,CAAC,CAAE6B,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAGnC,CAAG,CADtCV,EAAcU,EAAG6B,GAEjB7B,EAAEW,SAAS,CAAGkB,AAAM,OAANA,EAAaxB,OAAO+B,MAAM,CAACP,GAAMK,CAAAA,EAAGvB,SAAS,CAAGkB,EAAElB,SAAS,CAAE,IAAIuB,CAAG,CACtF,GAIAG,EAAe,AAACV,IAA2IW,WAAW,CAACC,MAAM,CAE7KC,EAAS,AAACvB,IAA+EuB,MAAM,CAAEC,EAAW,AAACxB,IAA+EwB,QAAQ,CAAEC,EAAQ,AAACzB,IAA+EyB,KAAK,CAAEC,EAAO,AAAC1B,IAA+E0B,IAAI,CAahYC,EAA+B,SAAUC,CAAM,EAE/C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAwHA,OA3HAnB,EAAUgB,EAAeC,GASzBD,EAAcjC,SAAS,CAACqC,UAAU,CAAG,WAIjC,IAHIC,EACAC,EACAC,EAEAC,EAAUC,AADD,IAAI,CACID,OAAO,CACxBE,EAAWD,AAFF,IAAI,CAEKE,KAAK,CAACD,QAAQ,CAChCE,EAAsBJ,EAAQhC,MAAM,CACpCqC,EAAQ,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,SAAUC,CAAG,CACxCC,CAAK,EAAI,OAAOD,EAAME,KAAKC,GAAG,CAACF,EAAMG,CAAC,EAAI,EAAI,EAAG,GACjDC,EAAc,IAAI,CAACP,MAAM,CAACC,MAAM,CAAC,SAAUC,CAAG,CAC9CC,CAAK,EAAI,IAAIZ,EAAI,OAAOW,EAAO,CAAA,AAAC,CAAA,AAA2B,OAA1BX,CAAAA,EAAKY,EAAMK,SAAS,AAAD,GAAejB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGkB,MAAM,AAAD,GAAM,CAAA,CAAI,EAAG,GACjHhD,EAAciC,EAAQjC,WAAW,EAAI,EACrCiD,EAAc,AAAC,CAAA,AAAmF,OAAlFlB,CAAAA,EAAK,AAA0B,OAAzBD,CAAAA,EAAK,IAAI,CAACS,MAAM,CAAC,EAAE,AAAD,GAAeT,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGiB,SAAS,AAAD,GAAehB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmB,KAAK,AAAD,GAAM,EAChJ5C,EAAc2B,EAAQ3B,WAAW,CACjC6C,EAAYF,EAEhB,GAAI,CAAC3B,EAAShB,GAEV,IADAA,EAAc,EAEV,AADGA,EAAcgC,IACbA,CAAAA,EAAQhC,EACR,AAACwC,EAAcK,EAAa,GAAE,GAIlCA,EAAYF,IAAc3C,EAIlC,IAAK,IADD0C,EAAS,AAACF,EAAcxC,EAAegC,EAClCc,EAAK,EAAGC,EAAKnB,AAzBT,IAAI,CAyBYK,MAAM,CAAEa,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACvD,IAAIV,EAAQW,CAAE,CAACD,EAAG,CACdG,EAAqBb,EAAMzC,MAAM,EAAI,CAAC,EACtCC,EAAUqD,EAAmBrD,MAAM,EAC/BmC,EAAoBnC,MAAM,CAC9BsD,EAAShC,EAAK+B,EAAmBC,MAAM,CACvCnB,EAAoBmB,MAAM,EAE1BN,EAAQO,AADGvD,AAAW,SAAXA,EACQ8C,EAASG,EAC5BJ,EAAYL,EAAMK,SAAS,EAAI,CAAC,EAChCW,EAAS,AAACX,CAAAA,EAAUY,CAAC,EAAI,CAAA,EAAK,AAAC,CAAA,AAACZ,CAAAA,EAAUG,KAAK,EAAI,CAAA,EAC/C5C,EAAc4C,CAAI,EAAK,EAC3BU,EAAiBjB,KAAKC,GAAG,CAAC,AAAmB,OAAlBZ,CAAAA,EAAKU,EAAMG,CAAC,AAADA,GAAeb,AAAO,KAAK,IAAZA,EAAgBA,EAAK,GAC1E6B,EAAUd,EAAUF,CAAC,EAAI,EACzBiB,EAAef,EAAUC,MAAM,EAAI,EACnCe,EAAW,KAAK,EAChBJ,EAAID,EACJb,EAAIH,EAAMsB,QAAQ,CAAGH,EAASA,EAASC,EAAcd,EACrDiB,EAAa,CACjBvB,CAAAA,EAAMqB,QAAQ,CAAGA,EAAWrB,EAAMqB,QAAQ,EAAI,EAAE,CAChD,IAAIG,EAAYxB,EAAMwB,SAAS,CACtBxB,EAAMwB,SAAS,CAACxB,EAAMyB,QAAQ,CAAG,WAAa,GAAG,EAC9CjC,AA/CH,IAAI,CA+CMgC,SAAS,CAAC,GAAG,CACxBhC,AAhDC,IAAI,CAgDEkC,YAAY,CAAC1B,EACxBA,EAAMyB,QAAQ,EAAI,UAMtB,GALA,OAAOD,EAAUG,CAAC,CACdnC,AAnDK,IAAI,CAmDFE,KAAK,CAACkC,UAAU,GACvB,OAAOJ,EAAUK,MAAM,CACvB,OAAOL,CAAS,CAAC,eAAe,EAEhC,AAAmB,UAAnB,OAAOxB,EAAMG,CAAC,CAAe,CACxBH,EAAM8B,OAAO,EACd9B,CAAAA,EAAM8B,OAAO,CAAGrC,EAASsC,CAAC,CAAC,SAASC,GAAG,CAACxC,AAzDvC,IAAI,CAyD0CyC,KAAK,CAAA,EAExD,IAAK,IAAIC,EAAM,EAAGA,EAAMhB,EAAgBgB,IAAO,CAC3C,IAAIC,EAAO,CACHlB,EAAGA,EAAIT,EAAQlD,EACf6C,EAAGA,EAAIG,EAAShD,EAChBkD,MAAOA,EAAS,CAAA,EAAI,EAAIlD,CAAU,EAClCgD,OAAQA,EAAU,CAAA,EAAI,EAAIhD,CAAU,EACpCqE,EAAGb,CACP,EACAgB,EAAUT,CAAQ,CAACa,EAAI,CACvBJ,EACAA,EAAQM,OAAO,CAACD,GAGhBL,EAAUrC,EACLjC,MAAM,CAACA,GACP2E,IAAI,CAACxD,EAAOwD,EAAMX,IAClBQ,GAAG,CAAChC,EAAM8B,OAAO,EAE1BA,EAAQO,QAAQ,CAAG,CAAA,EACnBhB,CAAQ,CAACa,EAAI,CAAGJ,EAChBb,GAAKT,IAEDe,GAAc3D,IACd2D,EAAa,EACbN,EAAID,EACJb,EAAIH,EAAMsB,QAAQ,CAAGnB,EAAIG,EAASH,EAAIG,EAE9C,CACJ,CAEA,IAAK,IADDgC,EAAI,GACCC,EAAK,EAAGC,EAAanB,EAAUkB,EAAKC,EAAW5B,MAAM,CAAE2B,IAAM,CAClE,IAAIT,EAAUU,CAAU,CAACD,EAAG,AAC5B,GAAED,EACER,IACKA,EAAQO,QAAQ,CAKjBP,EAAQO,QAAQ,CAAG,CAAA,GAJnBP,EAAQW,OAAO,GACfpB,EAASqB,MAAM,CAACJ,EAAG,IAM/B,CACJ,CACJ,EAMAvD,EAAc4D,cAAc,CAAG9D,EAAML,EAAamE,cAAc,CAAEtF,GAC3D0B,CACX,EAAEP,GACFG,EAAOI,EAAcjC,SAAS,CAAE,CAC5B8F,cAAe,KAAK,CACxB,GACA9E,IAA0I+E,kBAAkB,CAAC,UAAW9D,GAa3I,IAAI7B,EAAgBE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,GAET"}