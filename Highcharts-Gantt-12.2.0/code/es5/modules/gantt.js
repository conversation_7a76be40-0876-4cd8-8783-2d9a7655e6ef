!function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(require("highcharts"),require("highcharts").Chart,require("highcharts").Axis,require("highcharts").Color,require("highcharts").SeriesRegistry,require("highcharts").RendererRegistry,require("highcharts").SVGRenderer,require("highcharts").SVGElement,require("highcharts").Templating,require("highcharts").Point,require("highcharts").StackItem):"function"==typeof define&&define.amd?define("highcharts/modules/gantt",[["highcharts/highcharts"],["highcharts/highcharts","Chart"],["highcharts/highcharts","Axis"],["highcharts/highcharts","Color"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","RendererRegistry"],["highcharts/highcharts","SVGRenderer"],["highcharts/highcharts","SVGElement"],["highcharts/highcharts","Templating"],["highcharts/highcharts","Point"],["highcharts/highcharts","StackItem"]],i):"object"==typeof exports?exports["highcharts/modules/gantt"]=i(require("highcharts"),require("highcharts").Chart,require("highcharts").Axis,require("highcharts").Color,require("highcharts").SeriesRegistry,require("highcharts").RendererRegistry,require("highcharts").SVGRenderer,require("highcharts").SVGElement,require("highcharts").Templating,require("highcharts").Point,require("highcharts").StackItem):t.Highcharts=i(t.Highcharts,t.Highcharts.Chart,t.Highcharts.Axis,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.RendererRegistry,t.Highcharts.SVGRenderer,t.Highcharts.SVGElement,t.Highcharts.Templating,t.Highcharts.Point,t.Highcharts.StackItem)}(this,function(t,i,e,o,r,n,s,a,h,l,d){return function(){"use strict";var c,p,u,f,g,v,x,m,y,b,M,k,A,w,O,P,S,E={28:function(t){t.exports=a},184:function(t){t.exports=d},260:function(t){t.exports=l},512:function(t){t.exports=r},532:function(t){t.exports=e},540:function(t){t.exports=s},608:function(t){t.exports=n},620:function(t){t.exports=o},944:function(i){i.exports=t},960:function(t){t.exports=i},984:function(t){t.exports=h}},B={};function T(t){var i=B[t];if(void 0!==i)return i.exports;var e=B[t]={exports:{}};return E[t](e,e.exports,T),e.exports}T.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return T.d(i,{a:i}),i},T.d=function(t,i){for(var e in i)T.o(i,e)&&!T.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},T.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)};var C={};T.d(C,{default:function(){return nt}});var I=T(944),R=T.n(I);function D(t,i,e,o){return[["M",t,i+o/2],["L",t+e,i],["L",t,i+o/2],["L",t+e,i+o]]}function G(t,i,e,o){return D(t,i,e/2,o)}function L(t,i,e,o){return[["M",t+e,i],["L",t,i+o/2],["L",t+e,i+o],["Z"]]}function z(t,i,e,o){return L(t,i,e/2,o)}var N=function(t){var i=t.prototype.symbols;i.arrow=D,i["arrow-filled"]=L,i["arrow-filled-half"]=z,i["arrow-half"]=G,i["triangle-left"]=L,i["triangle-left-half"]=z},W=R().defined,H=R().error,F=R().merge,_=R().objectEach,U=R().deg2rad,X=Math.max,Y=Math.min,j=function(){function t(t,i,e){this.init(t,i,e)}return t.prototype.init=function(t,i,e){this.fromPoint=t,this.toPoint=i,this.options=e,this.chart=t.series.chart,this.pathfinder=this.chart.pathfinder},t.prototype.renderPath=function(t,i){var e=this.chart,o=e.styledMode,r=this.pathfinder,n={},s=this.graphics&&this.graphics.path;r.group||(r.group=e.renderer.g().addClass("highcharts-pathfinder-group").attr({zIndex:-1}).add(e.seriesGroup)),r.group.translate(e.plotLeft,e.plotTop),s&&s.renderer||(s=e.renderer.path().add(r.group),o||s.attr({opacity:0})),s.attr(i),n.d=t,o||(n.opacity=1),s.animate(n),this.graphics=this.graphics||{},this.graphics.path=s},t.prototype.addMarker=function(t,i,e){var o,r,n,s,a,h,l,d=this.fromPoint.series.chart,c=d.pathfinder,p=d.renderer,u="start"===t?this.fromPoint:this.toPoint,f=u.getPathfinderAnchorPoint(i);i.enabled&&((l="start"===t?e[1]:e[e.length-2])&&"M"===l[0]||"L"===l[0])&&(h={x:l[1],y:l[2]},r=u.getRadiansToVector(h,f),o=u.getMarkerVector(r,i.radius,f),i.width&&i.height?(s=i.width,a=i.height):s=a=2*i.radius,this.graphics=this.graphics||{},n={x:o.x-s/2,y:o.y-a/2,width:s,height:a,rotation:-r/U,rotationOriginX:o.x,rotationOriginY:o.y},this.graphics[t]?this.graphics[t].animate(n):(this.graphics[t]=p.symbol(i.symbol).addClass("highcharts-point-connecting-path-"+t+"-marker highcharts-color-"+this.fromPoint.colorIndex).attr(n).add(c.group),p.styledMode||this.graphics[t].attr({fill:i.color||this.fromPoint.color,stroke:i.lineColor,"stroke-width":i.lineWidth,opacity:0}).animate({opacity:1},u.series.options.animation)))},t.prototype.getPath=function(t){var i=this.pathfinder,e=this.chart,o=i.algorithms[t.type],r=i.chartObstacles;return"function"!=typeof o?(H('"'+t.type+'" is not a Pathfinder algorithm.'),{path:[],obstacles:[]}):(o.requiresObstacles&&!r&&(r=i.chartObstacles=i.getChartObstacles(t),e.options.connectors.algorithmMargin=t.algorithmMargin,i.chartObstacleMetrics=i.getObstacleMetrics(r)),o(this.fromPoint.getPathfinderAnchorPoint(t.startMarker),this.toPoint.getPathfinderAnchorPoint(t.endMarker),F({chartObstacles:r,lineObstacles:i.lineObstacles||[],obstacleMetrics:i.chartObstacleMetrics,hardBounds:{xMin:0,xMax:e.plotWidth,yMin:0,yMax:e.plotHeight},obstacleOptions:{margin:t.algorithmMargin},startDirectionX:i.getAlgorithmStartDirection(t.startMarker)},t)))},t.prototype.render=function(){var t=this.fromPoint,i=t.series,e=i.chart,o=e.pathfinder,r={},n=F(e.options.connectors,i.options.connectors,t.options.connectors,this.options);!e.styledMode&&(r.stroke=n.lineColor||t.color,r["stroke-width"]=n.lineWidth,n.dashStyle&&(r.dashstyle=n.dashStyle)),r.class="highcharts-point-connecting-path highcharts-color-"+t.colorIndex,W((n=F(r,n)).marker.radius)||(n.marker.radius=Y(X(Math.ceil((n.algorithmMargin||8)/2)-1,1),5));var s=this.getPath(n),a=s.path;s.obstacles&&(o.lineObstacles=o.lineObstacles||[],o.lineObstacles=o.lineObstacles.concat(s.obstacles)),this.renderPath(a,r),this.addMarker("start",F(n.marker,n.startMarker),a),this.addMarker("end",F(n.marker,n.endMarker),a)},t.prototype.destroy=function(){this.graphics&&(_(this.graphics,function(t){t.destroy()}),delete this.graphics)},t}(),V=R().composed,q=R().addEvent,Z=R().merge,K=R().pushUnique,$=R().wrap,J={color:"#ccd3ff",width:2,label:{format:"%[abdYHM]",formatter:function(t,i){return this.axis.chart.time.dateFormat(i||"",t,!0)},rotation:0,style:{fontSize:"0.7em"}}};function Q(){var t=this.options,i=t.currentDateIndicator;if(i){var e="object"==typeof i?Z(J,i):Z(J);e.value=Date.now(),e.className="highcharts-current-date-indicator",t.plotLines||(t.plotLines=[]),t.plotLines.push(e)}}function tt(){this.label&&this.label.attr({text:this.getLabelText(this.options.label)})}function ti(t,i){var e=this.options;return e&&e.className&&-1!==e.className.indexOf("highcharts-current-date-indicator")&&e.label&&"function"==typeof e.label.formatter?(e.value=Date.now(),e.label.formatter.call(this,e.value,e.label.format)):t.call(this,i)}var te=T(960),to=T.n(te),tr=(c=function(t,i){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)i.hasOwnProperty(e)&&(t[e]=i[e])})(t,i)},function(t,i){function e(){this.constructor=t}c(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),tn=R().defaultOptions,ts=R().isArray,ta=R().merge,th=R().splat,tl=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return tr(i,t),i.prototype.init=function(i,e){var o,r=i.xAxis,n=i.yAxis;i.xAxis=i.yAxis=void 0;var s=ta(!0,{chart:{type:"gantt"},title:{text:""},legend:{enabled:!1},navigator:{series:{type:"gantt"},yAxis:{type:"category"}}},i,{isGantt:!0});i.xAxis=r,i.yAxis=n,s.xAxis=(ts(i.xAxis)?i.xAxis:[i.xAxis||{},{}]).map(function(t,i){var e,r,n;return 1===i&&(o=0),ta({grid:{borderColor:"#cccccc",enabled:!0},opposite:null===(n=null!==(r=null===(e=tn.xAxis)||void 0===e?void 0:e.opposite)&&void 0!==r?r:t.opposite)||void 0===n||n,linkedTo:o},t,{type:"datetime"})}),s.yAxis=th(i.yAxis||{}).map(function(t){return ta({grid:{borderColor:"#cccccc",enabled:!0},staticScale:50,reversed:!0,type:t.categories?t.type:"treegrid"},t)}),t.prototype.init.call(this,s,e)},i}(to());(p=tl||(tl={})).ganttChart=function(t,i,e){return new p(t,i,e)};var td=tl,tc=T(532),tp=T.n(tc),tu=R().isTouchDevice,tf=R().addEvent,tg=R().merge,tv=R().pick,tx=[];function tm(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function ty(){var t,i,e,o,r=this.legend,n=this.navigator;if(n){i=r&&r.options,e=n.xAxis,o=n.yAxis;var s=n.scrollbarHeight,a=n.scrollButtonSize;this.inverted?(n.left=n.opposite?this.chartWidth-s-n.height:this.spacing[3]+s,n.top=this.plotTop+a):(n.left=tv(e.left,this.plotLeft+a),n.top=n.navigatorOptions.top||this.chartHeight-n.height-s-((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(i&&"bottom"===i.verticalAlign&&"proximate"!==i.layout&&i.enabled&&!i.floating?r.legendHeight+tv(i.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),e&&o&&(this.inverted?e.options.left=o.options.left=n.left:e.options.top=o.options.top=n.top,e.setAxisSize(),o.setAxisSize())}}function tb(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new y(this),tv(t.redraw,!0)&&this.redraw(t.animation))}function tM(){var t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new y(this))}function tk(){var t=this.options,i=t.navigator,e=t.rangeSelector;if((i&&i.enabled||e&&e.enabled)&&(!tu&&"x"===this.zooming.type||tu&&"x"===this.zooming.pinchType))return!1}function tA(t){var i=t.navigator;if(i&&t.xAxis[0]){var e=t.xAxis[0].getExtremes();i.render(e.min,e.max)}}function tw(t){var i=t.options.navigator||{},e=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(i.enabled||e.enabled)&&(tg(!0,this.options.navigator,i),tg(!0,this.options.scrollbar,e),delete t.options.navigator,delete t.options.scrollbar)}var tO=function(t,i){if(R().pushUnique(tx,t)){var e=t.prototype;y=i,e.callbacks.push(tA),tf(t,"afterAddSeries",tm),tf(t,"afterSetChartSize",ty),tf(t,"afterUpdate",tb),tf(t,"beforeRender",tM),tf(t,"beforeShowResetZoom",tk),tf(t,"update",tw)}},tP=R().isTouchDevice,tS=R().addEvent,tE=R().correctFloat,tB=R().defined,tT=R().isNumber,tC=R().pick;function tI(){this.navigatorAxis||(this.navigatorAxis=new tD(this))}function tR(t){var i,e=this.chart,o=e.options,r=o.navigator,n=this.navigatorAxis,s=e.zooming.pinchType,a=o.rangeSelector,h=e.zooming.type;if(this.isXAxis&&((null==r?void 0:r.enabled)||(null==a?void 0:a.enabled))){if("y"===h&&"zoom"===t.trigger)i=!1;else if(("zoom"===t.trigger&&"xy"===h||tP&&"xy"===s)&&this.options.range){var l=n.previousZoom;tB(t.min)?n.previousZoom=[this.min,this.max]:l&&(t.min=l[0],t.max=l[1],n.previousZoom=void 0)}}void 0!==i&&t.preventDefault()}var tD=function(){function t(t){this.axis=t}return t.compose=function(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),tS(t,"init",tI),tS(t,"setExtremes",tR))},t.prototype.destroy=function(){this.axis=void 0},t.prototype.toFixedRange=function(t,i,e,o){var r=this.axis,n=(r.pointRange||0)/2,s=tC(e,r.translate(t,!0,!r.horiz)),a=tC(o,r.translate(i,!0,!r.horiz));return tB(e)||(s=tE(s+n)),tB(o)||(a=tE(a-n)),tT(s)&&tT(a)||(s=a=void 0),{min:s,max:a}},t}(),tG=T(620),tL=T.n(tG),tz=T(512),tN=T.n(tz),tW=tL().parse,tH=tN().seriesTypes,tF={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:tW("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===tH.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},t_=R().defined,tU=(R().isNumber,R().pick),tX=function(t,i,e){if(e||2==arguments.length)for(var o,r=0,n=i.length;r<n;r++)!o&&r in i||(o||(o=Array.prototype.slice.call(i,0,r)),o[r]=i[r]);return t.concat(o||Array.prototype.slice.call(i))},tY=R().relativeLength,tj={"navigator-handle":function(t,i,e,o,r){void 0===r&&(r={});var n,s,a,h,l,d=r.width?r.width/2:e,c=tY(r.borderRadius||0,Math.min(2*d,o));return tX([["M",-1.5,(o=r.height||o)/2-3.5],["L",-1.5,o/2+4.5],["M",.5,o/2-3.5],["L",.5,o/2+4.5]],(n=-d-1,s=.5,a=2*d+1,h=o,(l={r:c},l.r)?function(t,i,e,o,r){var n=(null==r?void 0:r.r)||0;return[["M",t+n,i],["L",t+e-n,i],["A",n,n,0,0,1,t+e,i+n],["L",t+e,i+o-n],["A",n,n,0,0,1,t+e-n,i+o],["L",t+n,i+o],["A",n,n,0,0,1,t,i+o-n],["L",t,i+n],["A",n,n,0,0,1,t+n,i],["Z"]]}(n,.5,a,h,l):[["M",n,s],["L",n+a,s],["L",n+a,s+h],["L",n,s+h],["Z"]]),!0)}},tV=T(608),tq=T.n(tV),tZ=R().defined,tK=R().setOptions,t$=R().composed,tJ=tq().getRendererType,tQ={setFixedRange:function(t){var i=this.xAxis[0];tZ(i.dataMax)&&tZ(i.dataMin)&&t?this.fixedRange=Math.min(t,i.dataMax-i.dataMin):this.fixedRange=t}}.setFixedRange,t0=R().addEvent,t1=R().extend,t2=R().pushUnique;function t3(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}var t5=function(t,i,e){tD.compose(i),t2(t$,"Navigator")&&(t.prototype.setFixedRange=tQ,t1(tJ().prototype.symbols,tj),t0(e,"afterUpdate",t3),tK({navigator:tF}))},t6=R().composed,t4=R().addEvent,t8=R().defined,t9=R().pick,t7=R().pushUnique;!function(t){var i;function e(t){var i,e,o=t9(null===(i=t.options)||void 0===i?void 0:i.min,t.min),r=t9(null===(e=t.options)||void 0===e?void 0:e.max,t.max);return{axisMin:o,axisMax:r,scrollMin:t8(t.dataMin)?Math.min(o,t.min,t.dataMin,t9(t.threshold,1/0)):o,scrollMax:t8(t.dataMax)?Math.max(r,t.max,t.dataMax,t9(t.threshold,-1/0)):r}}function o(){var t=this.scrollbar,i=t&&!t.options.opposite,e=this.horiz?2:i?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[e]+=t.size+(t.options.margin||0))}function r(){var t,o,r=this;(null===(o=null===(t=r.options)||void 0===t?void 0:t.scrollbar)||void 0===o?void 0:o.enabled)&&(r.options.scrollbar.vertical=!r.horiz,r.options.startOnTick=r.options.endOnTick=!1,r.scrollbar=new i(r.chart.renderer,r.options.scrollbar,r.chart),t4(r.scrollbar,"changed",function(t){var i,o,n=e(r),s=n.axisMin,a=n.axisMax,h=n.scrollMin,l=n.scrollMax-h;if(t8(s)&&t8(a)){if(r.horiz&&!r.reversed||!r.horiz&&r.reversed?(i=h+l*this.to,o=h+l*this.from):(i=h+l*(1-this.from),o=h+l*(1-this.to)),this.shouldUpdateExtremes(t.DOMType)){var d="mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&void 0;r.setExtremes(o,i,!0,d,t)}else this.setRange(this.from,this.to)}}))}function n(){var t,i,o,r=e(this),n=r.scrollMin,s=r.scrollMax,a=this.scrollbar,h=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,d=this.options.margin||0;if(a&&l){if(this.horiz)this.opposite||(l[1]+=h),a.position(this.left,this.top+this.height+2+l[1]-(this.opposite?d:0),this.width,this.height),this.opposite||(l[1]+=d),t=1;else{this.opposite&&(l[0]+=h);var c=void 0;c=a.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:d):this.opposite?0:d,a.position(c,this.top,this.width,this.height),this.opposite&&(l[0]+=d),t=0}if(l[t]+=a.size+(a.options.margin||0),isNaN(n)||isNaN(s)||!t8(this.min)||!t8(this.max)||this.dataMin===this.dataMax)a.setRange(0,1);else if(this.min===this.max){var p=this.pointRange/(this.dataMax+1);i=p*this.min,o=p*(this.max+1),a.setRange(i,o)}else i=(this.min-n)/(s-n),o=(this.max-n)/(s-n),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(i,o):a.setRange(1-o,1-i)}}t.compose=function(t,e){t7(t6,"Axis.Scrollbar")&&(i=e,t4(t,"afterGetOffset",o),t4(t,"afterInit",r),t4(t,"afterRender",n))}}(b||(b={}));var it=b,ii={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},ie=R().defaultOptions,io=R().addEvent,ir=R().correctFloat,is=R().crisp,ia=R().defined,ih=R().destroyObjectProperties,il=R().fireEvent,id=R().merge,ic=R().pick,ip=R().removeEvent,iu=function(){function t(t,i,e){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,i,e)}return t.compose=function(i){it.compose(i,t)},t.swapXY=function(t,i){return i&&t.forEach(function(t){for(var i,e=t.length,o=0;o<e;o+=2)"number"==typeof(i=t[o+1])&&(t[o+1]=t[o+2],t[o+2]=i)}),t},t.prototype.addEvents=function(){var t=this.options.inverted?[1,0]:[0,1],i=this.scrollbarButtons,e=this.scrollbarGroup.element,o=this.track.element,r=this.mouseDownHandler.bind(this),n=this.mouseMoveHandler.bind(this),s=this.mouseUpHandler.bind(this),a=[[i[t[0]].element,"click",this.buttonToMinClick.bind(this)],[i[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[o,"click",this.trackClick.bind(this)],[e,"mousedown",r],[e.ownerDocument,"mousemove",n],[e.ownerDocument,"mouseup",s],[e,"touchstart",r],[e.ownerDocument,"touchmove",n],[e.ownerDocument,"touchend",s]];a.forEach(function(t){io.apply(null,t)}),this._events=a},t.prototype.buttonToMaxClick=function(t){var i=(this.to-this.from)*ic(this.options.step,.2);this.updatePosition(this.from+i,this.to+i),il(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.buttonToMinClick=function(t){var i=ir(this.to-this.from)*ic(this.options.step,.2);this.updatePosition(ir(this.from-i),ir(this.to-i)),il(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.cursorToScrollbarPosition=function(t){var i=this.options,e=i.minWidth>this.calculatedWidth?i.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-e),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-e)}},t.prototype.destroy=function(){var t=this,i=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(i){t[i]&&t[i].destroy&&(t[i]=t[i].destroy())}),i&&t===i.scrollbar&&(i.scrollbar=null,ih(i.scrollbarButtons))},t.prototype.drawScrollbarButton=function(i){var e=this.renderer,o=this.scrollbarButtons,r=this.options,n=this.size,s=e.g().add(this.group);if(o.push(s),r.buttonsEnabled){var a=e.rect().addClass("highcharts-scrollbar-button").add(s);this.chart.styledMode||a.attr({stroke:r.buttonBorderColor,"stroke-width":r.buttonBorderWidth,fill:r.buttonBackgroundColor}),a.attr(a.crisp({x:-.5,y:-.5,width:n,height:n,r:r.buttonBorderRadius},a.strokeWidth()));var h=e.path(t.swapXY([["M",n/2+(i?-1:1),n/2-3],["L",n/2+(i?-1:1),n/2+3],["L",n/2+(i?2:-2),n/2]],r.vertical)).addClass("highcharts-scrollbar-arrow").add(o[i]);this.chart.styledMode||h.attr({fill:r.buttonArrowColor})}},t.prototype.init=function(t,i,e){this.scrollbarButtons=[],this.renderer=t,this.userOptions=i,this.options=id(ii,ie.scrollbar,i),this.options.margin=ic(this.options.margin,10),this.chart=e,this.size=ic(this.options.size,this.options.height),i.enabled&&(this.render(),this.addEvents())},t.prototype.mouseDownHandler=function(t){var i,e=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t,o=this.cursorToScrollbarPosition(e);this.chartX=o.chartX,this.chartY=o.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0},t.prototype.mouseMoveHandler=function(t){var i,e,o=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t,r=this.options.vertical?"chartY":"chartX",n=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][r])&&(e=this.cursorToScrollbarPosition(o)[r]-this[r],this.hasDragged=!0,this.updatePosition(n[0]+e,n[1]+e),this.hasDragged&&il(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))},t.prototype.mouseUpHandler=function(t){this.hasDragged&&il(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null},t.prototype.position=function(t,i,e,o){var r=this.options,n=r.buttonsEnabled,s=r.margin,a=void 0===s?0:s,h=r.vertical,l=this.rendered?"animate":"attr",d=o,c=0;this.group.show(),this.x=t,this.y=i+this.trackBorderWidth,this.width=e,this.height=o,this.xOffset=d,this.yOffset=c,h?(this.width=this.yOffset=e=c=this.size,this.xOffset=d=0,this.yOffset=c=n?this.size:0,this.barWidth=o-(n?2*e:0),this.x=t+=a):(this.height=o=this.size,this.xOffset=d=n?this.size:0,this.barWidth=e-(n?2*o:0),this.y=this.y+a),this.group[l]({translateX:t,translateY:this.y}),this.track[l]({width:e,height:o}),this.scrollbarButtons[1][l]({translateX:h?0:e-d,translateY:h?o-c:0})},t.prototype.removeEvents=function(){this._events.forEach(function(t){ip.apply(null,t)}),this._events.length=0},t.prototype.render=function(){var i=this.renderer,e=this.options,o=this.size,r=this.chart.styledMode,n=i.g("scrollbar").attr({zIndex:e.zIndex}).hide().add();this.group=n,this.track=i.rect().addClass("highcharts-scrollbar-track").attr({r:e.trackBorderRadius||0,height:o,width:o}).add(n),r||this.track.attr({fill:e.trackBackgroundColor,stroke:e.trackBorderColor,"stroke-width":e.trackBorderWidth});var s=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-is(0,s),y:-is(0,s)}),this.scrollbarGroup=i.g().add(n),this.scrollbar=i.rect().addClass("highcharts-scrollbar-thumb").attr({height:o-s,width:o-s,r:e.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=i.path(t.swapXY([["M",-3,o/4],["L",-3,2*o/3],["M",0,o/4],["L",0,2*o/3],["M",3,o/4],["L",3,2*o/3]],e.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),r||(this.scrollbar.attr({fill:e.barBackgroundColor,stroke:e.barBorderColor,"stroke-width":e.barBorderWidth}),this.scrollbarRifles.attr({stroke:e.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-is(0,this.scrollbarStrokeWidth),-is(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)},t.prototype.setRange=function(t,i){var e,o,r=this.options,n=r.vertical,s=r.minWidth,a=this.barWidth,h=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(ia(a)){var l=a*Math.min(i,1);e=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=o=ir(l-e),o<s&&(e=(a-s+o)*t,o=s);var d=Math.floor(e+this.xOffset+this.yOffset),c=o/2-.5;this.from=t,this.to=i,n?(this.scrollbarGroup[h]({translateY:d}),this.scrollbar[h]({height:o}),this.scrollbarRifles[h]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:d}),this.scrollbar[h]({width:o}),this.scrollbarRifles[h]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),o<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===r.showFull&&(t<=0&&i>=1?this.group.hide():this.group.show()),this.rendered=!0}},t.prototype.shouldUpdateExtremes=function(t){return ic(this.options.liveRedraw,R().svg&&!R().isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!ia(t)},t.prototype.trackClick=function(t){var i,e=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t,o=this.to-this.from,r=this.y+this.scrollbarTop,n=this.x+this.scrollbarLeft;this.options.vertical&&e.chartY>r||!this.options.vertical&&e.chartX>n?this.updatePosition(this.from+o,this.to+o):this.updatePosition(this.from-o,this.to-o),il(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.update=function(t){this.destroy(),this.init(this.chart.renderer,id(!0,this.options,t),this.chart)},t.prototype.updatePosition=function(t,i){i>1&&(t=ir(1-ir(i-t)),i=1),t<0&&(i=ir(i-t),t=0),this.from=t,this.to=i},t.defaultOptions=ii,t}();ie.scrollbar=id(!0,iu.defaultOptions,ie.scrollbar);var ig=T(540),iv=T.n(ig),ix=function(){return(ix=Object.assign||function(t){for(var i,e=1,o=arguments.length;e<o;e++)for(var r in i=arguments[e])Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r]);return t}).apply(this,arguments)},im=R().defaultOptions,iy=R().isTouchDevice,ib=iv().prototype.symbols,iM=R().addEvent,ik=R().clamp,iA=R().correctFloat,iw=R().defined,iO=R().destroyObjectProperties,iP=R().erase,iS=R().extend,iE=R().find,iB=R().fireEvent,iT=R().isArray,iC=R().isNumber,iI=R().merge,iR=R().pick,iD=R().removeEvent,iG=R().splat;function iL(t){for(var i=[],e=1;e<arguments.length;e++)i[e-1]=arguments[e];var o=[].filter.call(i,iC);if(o.length)return Math[t].apply(0,o)}var iz=function(){function t(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}return t.compose=function(i,e,o){tO(i,t),t5(i,e,o)},t.prototype.drawHandle=function(t,i,e,o){var r=this.navigatorOptions.handles.height;this.handles[i][o](e?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-r)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-r/2-1)})},t.prototype.drawOutline=function(t,i,e,o){var r,n,s=this.navigatorOptions.maskInside,a=this.outline.strokeWidth(),h=a/2,l=a%2/2,d=this.scrollButtonSize,c=this.size,p=this.top,u=this.height,f=p-h,g=p+u,v=this.left;e?(r=p+i+l,i=p+t+l,n=[["M",v+u,p-d-l],["L",v+u,r],["L",v,r],["M",v,i],["L",v+u,i],["L",v+u,p+c+d]],s&&n.push(["M",v+u,r-h],["L",v+u,i+h])):(v-=d,t+=v+d-l,i+=v+d-l,n=[["M",v,f],["L",t,f],["L",t,g],["M",i,g],["L",i,f],["L",v+c+2*d,f]],s&&n.push(["M",t-h,f],["L",i+h,f])),this.outline[o]({d:n})},t.prototype.drawMasks=function(t,i,e,o){var r,n,s,a,h=this.left,l=this.top,d=this.height;e?(s=[h,h,h],a=[l,l+t,l+i],n=[d,d,d],r=[t,i-t,this.size-i]):(s=[h,h+t,h+i],a=[l,l,l],n=[t,i-t,this.size-i],r=[d,d,d]),this.shades.forEach(function(t,i){t[o]({x:s[i],y:a[i],width:n[i],height:r[i]})})},t.prototype.renderElements=function(){var t,i,e=this,o=e.navigatorOptions,r=o.maskInside,n=e.chart,s=n.inverted,a=n.renderer,h={cursor:s?"ns-resize":"ew-resize"},l=null!==(t=e.navigatorGroup)&&void 0!==t?t:e.navigatorGroup=a.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();if([!r,r,!r].forEach(function(t,i){var r,s=null!==(r=e.shades[i])&&void 0!==r?r:e.shades[i]=a.rect().addClass("highcharts-navigator-mask"+(1===i?"-inside":"-outside")).add(l);n.styledMode||(s.attr({fill:t?o.maskFill:"rgba(0,0,0,0)"}),1===i&&s.css(h))}),e.outline||(e.outline=a.path().addClass("highcharts-navigator-outline").add(l)),n.styledMode||e.outline.attr({"stroke-width":o.outlineWidth,stroke:o.outlineColor}),null===(i=o.handles)||void 0===i?void 0:i.enabled){var d=o.handles,c=d.height,p=d.width;[0,1].forEach(function(t){var i,o=d.symbols[t];if(e.handles[t]&&e.handles[t].symbolUrl===o){if(!e.handles[t].isImg&&e.handles[t].symbolName!==o){var r=ib[o].call(ib,-p/2-1,0,p,c);e.handles[t].attr({d:r}),e.handles[t].symbolName=o}}else null===(i=e.handles[t])||void 0===i||i.destroy(),e.handles[t]=a.symbol(o,-p/2-1,0,p,c,d),e.handles[t].attr({zIndex:7-t}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][t]).add(l),e.addMouseEvents();n.inverted&&e.handles[t].attr({rotation:90,rotationOriginX:Math.floor(-p/2),rotationOriginY:(c+p)/2}),n.styledMode||e.handles[t].attr({fill:d.backgroundColor,stroke:d.borderColor,"stroke-width":d.lineWidth,width:d.width,height:d.height,x:-p/2-1,y:0}).css(h)})}},t.prototype.update=function(t,i){var e,o,r,n,s=this;void 0===i&&(i=!1);var a=this.chart,h=a.options.chart.inverted!==(null===(r=a.scrollbar)||void 0===r?void 0:r.options.vertical);if(iI(!0,a.options.navigator,t),this.navigatorOptions=a.options.navigator||{},this.setOpposite(),iw(t.enabled)||h)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(a);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){iD(t,"updatedData",s.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){t.eventsToUnbind.push(iM(t,"updatedData",s.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=null!==(n=t.height)&&void 0!==n?n:this.height;var l=this.getXAxisOffsets();this.xAxis.update(ix(ix({},t.xAxis),((e={offsets:l})[a.inverted?"width":"height"]=this.height,e[a.inverted?"height":"width"]=void 0,e)),!1),this.yAxis.update(ix(ix({},t.yAxis),((o={})[a.inverted?"width":"height"]=this.height,o)),!1)}i&&a.redraw()},t.prototype.render=function(t,i,e,o){var r,n,s,a,h,l=this.chart,d=this.xAxis,c=d.pointRange||0,p=d.navigatorAxis.fake?l.xAxis[0]:d,u=this.navigatorEnabled,f=this.rendered,g=l.inverted,v=l.xAxis[0].minRange,x=l.xAxis[0].options.maxRange,m=this.scrollButtonSize,y=this.scrollbarHeight;if(!this.hasDragged||iw(e)){if(this.isDirty&&this.renderElements(),t=iA(t-c/2),i=iA(i+c/2),!iC(t)||!iC(i)){if(!f)return;e=0,o=iR(d.width,p.width)}this.left=iR(d.left,l.plotLeft+m+(g?l.plotWidth:0));var b=this.size=a=iR(d.len,(g?l.plotHeight:l.plotWidth)-2*m);r=g?y:a+2*m,e=iR(e,d.toPixels(t,!0)),o=iR(o,d.toPixels(i,!0)),iC(e)&&Math.abs(e)!==1/0||(e=0,o=r);var M=d.toValue(e,!0),k=d.toValue(o,!0),A=Math.abs(iA(k-M));A<v?this.grabbedLeft?e=d.toPixels(k-v-c,!0):this.grabbedRight&&(o=d.toPixels(M+v+c,!0)):iw(x)&&iA(A-c)>x&&(this.grabbedLeft?e=d.toPixels(k-x-c,!0):this.grabbedRight&&(o=d.toPixels(M+x+c,!0))),this.zoomedMax=ik(Math.max(e,o),0,b),this.zoomedMin=ik(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(e,o),0,b),this.range=this.zoomedMax-this.zoomedMin,b=Math.round(this.zoomedMax);var w=Math.round(this.zoomedMin);u&&(this.navigatorGroup.attr({visibility:"inherit"}),h=f&&!this.hasDragged?"animate":"attr",this.drawMasks(w,b,g,h),this.drawOutline(w,b,g,h),this.navigatorOptions.handles.enabled&&(this.drawHandle(w,0,g,h),this.drawHandle(b,1,g,h))),this.scrollbar&&(g?(s=this.top-m,n=this.left-y+(u||!p.opposite?0:(p.titleOffset||0)+p.axisTitleMargin),y=a+2*m):(s=this.top+(u?this.height:-y),n=this.left-m),this.scrollbar.position(n,s,r,y),this.scrollbar.setRange(this.zoomedMin/(a||1),this.zoomedMax/(a||1))),this.rendered=!0,this.isDirty=!1,iB(this,"afterRender")}},t.prototype.addMouseEvents=function(){var t,i,e=this,o=e.chart,r=o.container,n=[];e.mouseMoveHandler=t=function(t){e.onMouseMove(t)},e.mouseUpHandler=i=function(t){e.onMouseUp(t)},(n=e.getPartsEvents("mousedown")).push(iM(o.renderTo,"mousemove",t),iM(r.ownerDocument,"mouseup",i),iM(o.renderTo,"touchmove",t),iM(r.ownerDocument,"touchend",i)),n.concat(e.getPartsEvents("touchstart")),e.eventsToUnbind=n,e.series&&e.series[0]&&n.push(iM(e.series[0].xAxis,"foundExtremes",function(){o.navigator.modifyNavigatorAxisExtremes()}))},t.prototype.getPartsEvents=function(t){var i=this,e=[];return["shades","handles"].forEach(function(o){i[o].forEach(function(r,n){e.push(iM(r.element,t,function(t){i[o+"Mousedown"](t,n)}))})}),e},t.prototype.shadesMousedown=function(t,i){t=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t;var e,o,r,n,s,a=this.chart,h=this.xAxis,l=this.zoomedMin,d=this.size,c=this.range,p=this.left,u=t.chartX;a.inverted&&(u=t.chartY,p=this.top),1===i?(this.grabbedCenter=u,this.fixedWidth=c,this.dragOffset=u-l):(s=u-p-c/2,0===i?s=Math.max(0,s):2===i&&s+c>=d&&(s=d-c,this.reversedExtremes?(s-=c,r=this.getUnionExtremes().dataMin):o=this.getUnionExtremes().dataMax),s!==l&&(this.fixedWidth=c,iw((n=h.navigatorAxis.toFixedRange(s,s+c,r,o)).min)&&iB(this,"setRange",{min:Math.min(n.min,n.max),max:Math.max(n.min,n.max),redraw:!0,eventArguments:{trigger:"navigator"}})))},t.prototype.handlesMousedown=function(t,i){t=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t;var e,o=this.chart,r=o.xAxis[0],n=this.reversedExtremes;0===i?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=n?r.min:r.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=n?r.max:r.min),o.setFixedRange(void 0)},t.prototype.onMouseMove=function(t){var i,e,o=this,r=o.chart,n=o.navigatorSize,s=o.range,a=o.dragOffset,h=r.inverted,l=o.left;(!t.touches||0!==t.touches[0].pageX)&&(e=(t=(null===(i=r.pointer)||void 0===i?void 0:i.normalize(t))||t).chartX,h&&(l=o.top,e=t.chartY),o.grabbedLeft?(o.hasDragged=!0,o.render(0,0,e-l,o.otherHandlePos)):o.grabbedRight?(o.hasDragged=!0,o.render(0,0,o.otherHandlePos,e-l)):o.grabbedCenter&&(o.hasDragged=!0,e<a?e=a:e>n+a-s&&(e=n+a-s),o.render(0,0,e-a,e-a+s)),o.hasDragged&&o.scrollbar&&iR(o.scrollbar.options.liveRedraw,!iy&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){o.onMouseUp(t)},0)))},t.prototype.onMouseUp=function(t){var i,e,o,r,n,s,a=this.chart,h=this.xAxis,l=this.scrollbar,d=t.DOMEvent||t,c=a.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!l||!l.hasDragged)||"scrollbar"===t.trigger)&&(o=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?r=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(n=this.fixedExtreme),this.zoomedMax===this.size&&(n=this.reversedExtremes?o.dataMin:o.dataMax),0===this.zoomedMin&&(r=this.reversedExtremes?o.dataMax:o.dataMin),iw((s=h.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,r,n)).min)&&iB(this,"setRange",{min:Math.min(s.min,s.max),max:Math.max(s.min,s.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&iC(this.zoomedMin)&&iC(this.zoomedMax)&&(e=Math.round(this.zoomedMin),i=Math.round(this.zoomedMax),this.shades&&this.drawMasks(e,i,c,p),this.outline&&this.drawOutline(e,i,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(e,0,c,p),this.drawHandle(i,1,c,p)))},t.prototype.removeEvents=function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()},t.prototype.removeBaseSeriesEvents=function(){var t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){iD(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&iD(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},t.prototype.getXAxisOffsets=function(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]},t.prototype.init=function(t){var i,e,o=t.options,r=o.navigator||{},n=r.enabled,s=o.scrollbar||{},a=s.enabled,h=n&&r.height||0,l=a&&s.height||0,d=s.buttonsEnabled&&l||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=h,this.scrollbarHeight=l,this.scrollButtonSize=d,this.scrollbarEnabled=a,this.navigatorEnabled=n,this.navigatorOptions=r,this.scrollbarOptions=s,this.setOpposite();var c=this,p=c.baseSeries,u=t.xAxis.length,f=t.yAxis.length,g=p&&p[0]&&p[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,c.navigatorEnabled){var v=this.getXAxisOffsets();c.xAxis=new(tp())(t,iI({breaks:g.options.breaks,ordinal:g.options.ordinal,overscroll:g.options.overscroll},r.xAxis,{type:"datetime",yAxis:null===(i=r.yAxis)||void 0===i?void 0:i.id,index:u,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:g.options.ordinal?0:g.options.minPadding,maxPadding:g.options.ordinal?0:g.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:v,width:h}:{offsets:v,height:h}),"xAxis"),c.yAxis=new(tp())(t,iI(r.yAxis,{alignTicks:!1,offset:0,index:f,isInternal:!0,reversed:iR(r.yAxis&&r.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:h}:{height:h}),"yAxis"),p||r.series.data?c.updateNavigatorSeries(!1):0===t.series.length&&(c.unbindRedraw=iM(t,"beforeRedraw",function(){t.series.length>0&&!c.series&&(c.setBaseSeries(),c.unbindRedraw())})),c.reversedExtremes=t.inverted&&!c.xAxis.reversed||!t.inverted&&c.xAxis.reversed,c.renderElements(),c.addMouseEvents()}else c.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(i,e){var o=t.xAxis[0],r=o.getExtremes(),n=o.len-2*d,s=iL("min",o.options.min,r.dataMin),a=iL("max",o.options.max,r.dataMax)-s;return e?i*a/n+s:n*(i-s)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},c.xAxis.navigatorAxis.axis=c.xAxis,c.xAxis.navigatorAxis.toFixedRange=tD.prototype.toFixedRange.bind(c.xAxis.navigatorAxis);if(null===(e=t.options.scrollbar)||void 0===e?void 0:e.enabled){var x=iI(t.options.scrollbar,{vertical:t.inverted});iC(x.margin)||(x.margin=t.inverted?-3:3),t.scrollbar=c.scrollbar=new iu(t.renderer,x,t),iM(c.scrollbar,"changed",function(t){var i=c.size,e=i*this.to,o=i*this.from;c.hasDragged=c.scrollbar.hasDragged,c.render(0,0,o,e),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){c.onMouseUp(t)})})}c.addBaseSeriesEvents(),c.addChartEvents()},t.prototype.setOpposite=function(){var t=this.navigatorOptions,i=this.navigatorEnabled,e=this.chart;this.opposite=iR(t.opposite,!!(!i&&e.inverted))},t.prototype.getUnionExtremes=function(t){var i,e=this.chart.xAxis[0],o=this.chart.time,r=this.xAxis,n=r.options,s=e.options;return t&&null===e.dataMin||(i={dataMin:iR(o.parse(null==n?void 0:n.min),iL("min",o.parse(s.min),e.dataMin,r.dataMin,r.min)),dataMax:iR(o.parse(null==n?void 0:n.max),iL("max",o.parse(s.max),e.dataMax,r.dataMax,r.max))}),i},t.prototype.setBaseSeries=function(t,i){var e=this.chart,o=this.baseSeries=[];t=t||e.options&&e.options.navigator.baseSeries||(e.series.length?iE(e.series,function(t){return!t.options.isInternal}).index:0),(e.series||[]).forEach(function(i,e){!i.options.isInternal&&(i.options.showInNavigator||(e===t||i.options.id===t)&&!1!==i.options.showInNavigator)&&o.push(i)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,i)},t.prototype.updateNavigatorSeries=function(t,i){var e,o,r,n,s,a=this,h=a.chart,l=a.baseSeries,d={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:null===(e=this.navigatorOptions.xAxis)||void 0===e?void 0:e.id,yAxis:null===(o=this.navigatorOptions.yAxis)||void 0===o?void 0:o.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},c=a.series=(a.series||[]).filter(function(t){var i=t.baseSeries;return!(0>l.indexOf(i))||(i&&(iD(i,"updatedData",a.updatedDataHandler),delete i.navigatorSeries),t.chart&&t.destroy(),!1)}),p=a.navigatorOptions.series;l&&l.length&&l.forEach(function(t){var e,o=t.navigatorSeries,u=iS({color:t.color,visible:t.visible},iT(p)?im.navigator.series:p);if(!o||!1!==a.navigatorOptions.adaptToUpdatedData){d.name="Navigator "+l.length,s=(r=t.options||{}).navigatorOptions||{},u.dataLabels=iG(u.dataLabels),(n=iI(r,d,u,s)).pointRange=iR(u.pointRange,s.pointRange,im.plotOptions[n.type||"line"].pointRange);var f=s.data||u.data;a.hasNavigatorData=a.hasNavigatorData||!!f,n.data=f||(null===(e=r.data)||void 0===e?void 0:e.slice(0)),o&&o.options?o.update(n,i):(t.navigatorSeries=h.initSeries(n),h.setSortedData(),t.navigatorSeries.baseSeries=t,c.push(t.navigatorSeries))}}),(p.data&&!(l&&l.length)||iT(p))&&(a.hasNavigatorData=!1,(p=iG(p)).forEach(function(t,i){d.name="Navigator "+(c.length+1),(n=iI(im.navigator.series,{color:h.series[i]&&!h.series[i].options.isInternal&&h.series[i].color||h.options.colors[i]||h.options.colors[0]},d,t)).data=t.data,n.data&&(a.hasNavigatorData=!0,c.push(h.initSeries(n)))})),t&&this.addBaseSeriesEvents()},t.prototype.addBaseSeriesEvents=function(){var t=this,i=this,e=i.baseSeries||[];e[0]&&e[0].xAxis&&e[0].eventsToUnbind.push(iM(e[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),e.forEach(function(o){o.eventsToUnbind.push(iM(o,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),o.eventsToUnbind.push(iM(o,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==t.navigatorOptions.adaptToUpdatedData&&o.xAxis&&o.eventsToUnbind.push(iM(o,"updatedData",t.updatedDataHandler)),o.eventsToUnbind.push(iM(o,"remove",function(){e&&iP(e,o),this.navigatorSeries&&i.series&&(iP(i.series,this.navigatorSeries),iw(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})},t.prototype.getBaseSeriesMin=function(t){return this.baseSeries.reduce(function(t,i){var e;return Math.min(t,null!==(e=i.getColumn("x")[0])&&void 0!==e?e:t)},t)},t.prototype.modifyNavigatorAxisExtremes=function(){var t=this.xAxis;if(void 0!==t.getExtremes){var i=this.getUnionExtremes(!0);i&&(i.dataMin!==t.min||i.dataMax!==t.max)&&(t.min=i.dataMin,t.max=i.dataMax)}},t.prototype.modifyBaseAxisExtremes=function(){var t,i,e,o=this.chart.navigator,r=this.getExtremes(),n=r.min,s=r.max,a=r.dataMin,h=r.dataMax,l=s-n,d=o.stickToMin,c=o.stickToMax,p=iR(null===(t=this.ordinal)||void 0===t?void 0:t.convertOverscroll(this.options.overscroll),0),u=o.series&&o.series[0],f=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(d&&(i=(e=a)+l),c&&(i=h+p,d||(e=Math.max(a,i-l,o.getBaseSeriesMin(u&&u.xData?u.xData[0]:-Number.MAX_VALUE)))),f&&(d||c)&&iC(e)&&(this.min=this.userMin=e,this.max=this.userMax=i)),o.stickToMin=o.stickToMax=null},t.prototype.updatedDataHandler=function(){var t=this.chart.navigator,i=this.navigatorSeries,e=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=iR(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,e),t.stickToMin=t.shouldStickToMin(this,t),i&&!t.hasNavigatorData&&(i.options.pointStart=this.getColumn("x")[0],i.setData(this.options.data,!1,null,!1))},t.prototype.shouldStickToMin=function(t,i){var e=i.getBaseSeriesMin(t.getColumn("x")[0]),o=t.xAxis,r=o.max,n=o.min,s=o.options.range,a=!0;return!!(iC(r)&&iC(n))&&(s&&r-e>0?r-e<s:n<=e)},t.prototype.addChartEvents=function(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(iM(this.chart,"redraw",function(){var t=this.navigator,i=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);i&&t.render(i.min,i.max)}),iM(this.chart,"getMargins",function(){var t,i=this.navigator,e=i.opposite?"plotTop":"marginBottom";this.inverted&&(e=i.opposite?"marginRight":"plotLeft"),this[e]=(this[e]||0)+(i.navigatorEnabled||!this.inverted?i.height+((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)+i.scrollbarHeight:0)+(i.navigatorOptions.margin||0)}),iM(t,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))},t.prototype.destroy=function(){var t=this;this.removeEvents(),this.xAxis&&(iP(this.chart.xAxis,this.xAxis),iP(this.chart.axes,this.xAxis)),this.yAxis&&(iP(this.chart.yAxis,this.yAxis),iP(this.chart.axes,this.yAxis)),(this.series||[]).forEach(function(t){t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(function(i){t[i]&&t[i].destroy&&t[i].destroy(),t[i]=null}),[this.handles].forEach(function(t){iO(t)}),this.baseSeries.forEach(function(t){t.navigatorSeries=void 0}),this.navigatorEnabled=!1},t}(),iN={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},iW=R().defaultOptions,iH=R().composed,iF=R().addEvent,i_=R().defined,iU=R().extend,iX=R().isNumber,iY=R().merge,ij=R().pick,iV=R().pushUnique,iq=[];function iZ(){var t,i,e=this.range,o=e.type,r=this.max,n=this.chart.time,s=function(t,i){var e=n.toParts(t),r=e.slice();"year"===o?r[0]+=i:r[1]+=i;var s=n.makeTime.apply(n,r),a=n.toParts(s);return"month"===o&&e[1]===a[1]&&1===Math.abs(i)&&(r[0]=e[0],r[1]=e[1],r[2]=0),(s=n.makeTime.apply(n,r))-t};iX(e)?(t=r-e,i=e):e&&(t=r+s(r,-(e.count||1)),this.chart&&this.chart.setFixedRange(r-t));var a=ij(this.dataMin,Number.MIN_VALUE);return iX(t)||(t=a),t<=a&&(t=a,void 0===i&&(i=s(t,e.count)),this.newMax=Math.min(t+i,ij(this.dataMax,Number.MAX_VALUE))),iX(r)?!iX(e)&&e&&e._offsetMin&&(t+=e._offsetMin):t=void 0,t}function iK(){var t;null===(t=this.rangeSelector)||void 0===t||t.redrawElements()}function i$(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new M(this))}function iJ(){var t=this.rangeSelector;if(t){iX(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);var i=t.options.verticalAlign;t.options.floating||("bottom"===i?this.extraBottomMargin=!0:"top"!==i||(this.extraTopMargin=!0))}}function iQ(){var t,i=this.rangeSelector;if(i){var e=this.xAxis[0].getExtremes(),o=this.legend,r=i&&i.options.verticalAlign;iX(e.min)&&i.render(e.min,e.max),o.display&&"top"===r&&r===o.options.verticalAlign&&(t=iY(this.spacingBox),"vertical"===o.options.layout?t.y=this.plotTop:t.y+=i.getHeight(),o.group.placed=!1,o.align(t))}}function i0(){for(var t=0,i=iq.length;t<i;++t){var e=iq[t];if(e[0]===this){e[1].forEach(function(t){return t()}),iq.splice(t,1);return}}}function i1(){var t,i=this.rangeSelector;if(null===(t=null==i?void 0:i.options)||void 0===t?void 0:t.enabled){var e=i.getHeight(),o=i.options.verticalAlign;i.options.floating||("bottom"===o?this.marginBottom+=e:"middle"===o||(this.plotTop+=e))}}function i2(t){var i=t.options.rangeSelector,e=this.extraBottomMargin,o=this.extraTopMargin,r=this.rangeSelector;if(i&&i.enabled&&!i_(r)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=r=new M(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,r){var n=i&&i.verticalAlign||r.options&&r.options.verticalAlign;r.options.floating||("bottom"===n?this.extraBottomMargin=!0:"middle"===n||(this.extraTopMargin=!0)),(this.extraBottomMargin!==e||this.extraTopMargin!==o)&&(this.isDirtyBox=!0)}}var i3=function(t,i,e){if(M=e,iV(iH,"RangeSelector")){var o=i.prototype;t.prototype.minFromRange=iZ,iF(i,"afterGetContainer",i$),iF(i,"beforeRender",iJ),iF(i,"destroy",i0),iF(i,"getMargins",i1),iF(i,"redraw",iQ),iF(i,"update",i2),iF(i,"beforeRedraw",iK),o.callbacks.push(iQ),iU(iW,{rangeSelector:iN.rangeSelector}),iU(iW.lang,iN.lang)}},i5=T(28),i6=T.n(i5),i4=T(984),i8=T.n(i4),i9=function(t,i,e){if(e||2==arguments.length)for(var o,r=0,n=i.length;r<n;r++)!o&&r in i||(o||(o=Array.prototype.slice.call(i,0,r)),o[r]=i[r]);return t.concat(o||Array.prototype.slice.call(i))};(u=k||(k={})).setLength=function(t,i,e){return Array.isArray(t)?(t.length=i,t):t[e?"subarray":"slice"](0,i)},u.splice=function(t,i,e,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,i9([i,e],r,!1)),array:t};var n=Object.getPrototypeOf(t).constructor,s=t[o?"subarray":"slice"](i,i+e),a=new n(t.length-e+r.length);return a.set(t.subarray(0,i),0),a.set(r,i),a.set(t.subarray(i+e),i+r.length),{removed:s,array:a}};var i7=k,et=i7.setLength,ei=i7.splice,ee=R().fireEvent,eo=R().objectEach,er=R().uniqueKey,en=function(){function t(t){void 0===t&&(t={});var i=this;this.autoId=!t.id,this.columns={},this.id=t.id||er(),this.modified=this,this.rowCount=0,this.versionTag=er();var e=0;eo(t.columns||{},function(t,o){i.columns[o]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}return t.prototype.applyRowCount=function(t){var i=this;this.rowCount=t,eo(this.columns,function(e,o){e.length!==t&&(i.columns[o]=et(e,t))})},t.prototype.deleteRows=function(t,i){var e=this;if(void 0===i&&(i=1),i>0&&t<this.rowCount){var o=0;eo(this.columns,function(r,n){e.columns[n]=ei(r,t,i).array,o=r.length}),this.rowCount=o}ee(this,"afterDeleteRows",{rowIndex:t,rowCount:i}),this.versionTag=er()},t.prototype.getColumn=function(t,i){return this.columns[t]},t.prototype.getColumns=function(t,i){var e=this;return(t||Object.keys(this.columns)).reduce(function(t,i){return t[i]=e.columns[i],t},{})},t.prototype.getRow=function(t,i){var e=this;return(i||Object.keys(this.columns)).map(function(i){var o;return null===(o=e.columns[i])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,i,e,o){var r;void 0===i&&(i=[]),void 0===e&&(e=0),this.setColumns(((r={})[t]=i,r),e,o)},t.prototype.setColumns=function(t,i,e){var o=this,r=this.rowCount;eo(t,function(t,i){o.columns[i]=t.slice(),r=t.length}),this.applyRowCount(r),(null==e?void 0:e.silent)||(ee(this,"afterSetColumns"),this.versionTag=er())},t.prototype.setRow=function(t,i,e,o){void 0===i&&(i=this.rowCount);var r=this.columns,n=e?this.rowCount+1:i+1;eo(t,function(t,s){var a=r[s]||(null==o?void 0:o.addColumns)!==!1&&Array(n);a&&(e?a=ei(a,i,0,!0,[t]).array:a[i]=t,r[s]=a)}),n>this.rowCount&&this.applyRowCount(n),(null==o?void 0:o.silent)||(ee(this,"afterSetRows"),this.versionTag=er())},t}(),es=function(){return(es=Object.assign||function(t){for(var i,e=1,o=arguments.length;e<o;e++)for(var r in i=arguments[e])Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r]);return t}).apply(this,arguments)},ea=R().addEvent,eh=R().correctFloat,el=R().css,ed=R().defined,ec=R().error,ep=R().isNumber,eu=R().pick,ef=R().timeUnits,eg=R().isString;!function(t){function i(t,i,e,o,r,n,s){void 0===r&&(r=[]),void 0===n&&(n=0);var a,h,l,d,c,p={},u=this.options.tickPixelInterval,f=this.chart.time,g=[],v=0,x=[],m=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!r||r.length<3||void 0===i)return f.getTimeTicks.apply(f,arguments);var y=r.length;for(a=0;a<y;a++){if(c=a&&r[a-1]>e,r[a]<i&&(v=a),a===y-1||r[a+1]-r[a]>5*n||c){if(r[a]>m){for(h=f.getTimeTicks(t,r[v],r[a],o);h.length&&h[0]<=m;)h.shift();h.length&&(m=h[h.length-1]),g.push(x.length),x=x.concat(h)}v=a+1}if(c)break}if(h){if(d=h.info,s&&d.unitRange<=ef.hour){for(v=1,a=x.length-1;v<a;v++)f.dateFormat("%d",x[v])!==f.dateFormat("%d",x[v-1])&&(p[x[v]]="day",l=!0);l&&(p[x[0]]="day"),d.higherRanks=p}d.segmentStarts=g,x.info=d}else ec(12,!1,this.chart);if(s&&ed(u)){for(var b=x.length,M=[],k=[],A=void 0,w=void 0,O=void 0,P=void 0,S=void 0,E=b;E--;)w=this.translate(x[E]),O&&(k[E]=O-w),M[E]=O=w;for(k.sort(function(t,i){return t-i}),(P=k[Math.floor(k.length/2)])<.6*u&&(P=null),E=x[b-1]>e?b-1:b,O=void 0;E--;)S=Math.abs(O-(w=M[E])),O&&S<.8*u&&(null===P||S<.8*P)?(p[x[E]]&&!p[x[E+1]]?(A=E+1,O=w):A=E,x.splice(A,1)):O=w}return x}function e(t){var i=this.ordinal.positions;if(!i)return t;var e,o=i.length-1;return(t<0?t=i[0]:t>o?t=i[o]:(o=Math.floor(t),e=t-o),void 0!==e&&void 0!==i[o])?i[o]+(e?e*(i[o+1]-i[o]):0):t}function o(t){var i=this.ordinal,e=this.old?this.old.min:this.min,o=this.old?this.old.transA:this.transA,r=i.getExtendedPositions();if(null==r?void 0:r.length){var n=eh((t-e)*o+this.minPixelPadding),s=eh(i.getIndexOfPoint(n,r)),a=eh(s%1);if(s>=0&&s<=r.length-1){var h=r[Math.floor(s)],l=r[Math.ceil(s)];return r[Math.floor(s)]+a*(l-h)}}return t}function r(i,e){var o=t.Additions.findIndexOf(i,e,!0);if(i[o]===e)return o;var r=(e-i[o])/(i[o+1]-i[o]);return o+r}function n(){this.ordinal||(this.ordinal=new t.Additions(this))}function s(){var t=this.eventArgs,i=this.options;if(this.isXAxis&&ed(i.overscroll)&&0!==i.overscroll&&ep(this.max)&&ep(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&((null==t?void 0:t.trigger)!=="pan"||this.isInternal)&&(null==t?void 0:t.trigger)!=="navigator")){var e=this.ordinal.convertOverscroll(i.overscroll);this.max+=e,!this.isInternal&&ed(this.userMin)&&(null==t?void 0:t.trigger)!=="mousewheel"&&(this.min+=e)}}function a(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function h(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function l(t){var i,e=this.xAxis[0],o=e.ordinal.convertOverscroll(e.options.overscroll),r=t.originalEvent.chartX,n=this.options.chart.panning,s=!1;if(n&&"y"!==n.type&&e.options.ordinal&&e.series.length&&(!t.touches||t.touches.length<=1)){var a=this.mouseDownX,h=e.getExtremes(),l=h.dataMin,d=h.dataMax,c=h.min,p=h.max,u=this.hoverPoints,f=e.closestPointRange||(null===(i=e.ordinal)||void 0===i?void 0:i.overscrollPointsRange),g=Math.round((a-r)/(e.translationSlope*(e.ordinal.slope||f))),v=e.ordinal.getExtendedPositions(),x={ordinal:{positions:v,extendedOrdinalPositions:v}},m=e.index2val,y=e.val2lin,b=void 0,M=void 0;if(c<=l&&g<0||p+o>=d&&g>0)return;x.ordinal.positions?Math.abs(g)>1&&(u&&u.forEach(function(t){t.setState()}),d>(M=x.ordinal.positions)[M.length-1]&&M.push(d),this.setFixedRange(p-c),(b=e.navigatorAxis.toFixedRange(void 0,void 0,m.apply(x,[y.apply(x,[c,!0])+g]),m.apply(x,[y.apply(x,[p,!0])+g]))).min>=Math.min(M[0],c)&&b.max<=Math.max(M[M.length-1],p)+o&&e.setExtremes(b.min,b.max,!0,!1,{trigger:"pan"}),this.mouseDownX=r,el(this.container,{cursor:"move"})):s=!0}else s=!0;s||n&&/y/.test(n.type)?o&&(e.max=e.dataMax+o):t.preventDefault()}function d(){var t=this.xAxis;(null==t?void 0:t.options.ordinal)&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function c(t,i){var e,o,n,s=this.ordinal,a=s.positions,h=s.slope;if(!a)return t;var l=a.length;if(a[0]<=t&&a[l-1]>=t)e=r(a,t);else{if(!(null==(n=null===(o=s.getExtendedPositions)||void 0===o?void 0:o.call(s))?void 0:n.length))return t;var d=n.length;h||(h=(n[d-1]-n[0])/d);var c=r(n,a[0]);if(t>=n[0]&&t<=n[d-1])e=r(n,t)-c;else{if(!i)return t;if(t<n[0]){var p=n[0]-t,u=p/h;e=-c-u}else{var p=t-n[d-1],u=p/h;e=u+d-c}}}return i?e:h*(e||0)+s.offset}t.compose=function(t,r,p){var u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=i,u.index2val=e,u.lin2val=o,u.val2lin=c,u.ordinal2lin=u.val2lin,ea(t,"afterInit",n),ea(t,"foundExtremes",s),ea(t,"afterSetScale",a),ea(t,"initialAxisTranslation",h),ea(p,"pan",l),ea(p,"touchpan",l),ea(r,"updatedData",d)),t},t.Additions=function(){function t(t){this.index={},this.axis=t}return t.prototype.beforeSetTickPositions=function(){var t,i,e,o,r,n,s,a,h=this.axis,l=h.ordinal,d=h.getExtremes(),c=d.min,p=d.max,u=null===(t=h.brokenAxis)||void 0===t?void 0:t.hasBreaks,f=h.options.ordinal,g=[],v=Number.MAX_VALUE,x=!1,m=!1,y=!1;if(f||u){var b=0;if(h.series.forEach(function(t,o){var r=t.getColumn("x",!0);if(e=[],o>0&&"highcharts-navigator-series"!==t.options.id&&r.length>1&&(m=b!==r[1]-r[0]),b=r[1]-r[0],t.boosted&&(y=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||u)&&(i=(g=g.concat(r)).length,g.sort(function(t,i){return t-i}),v=Math.min(v,eu(t.closestPointRange,v)),i)){for(o=0;o<i-1;)g[o]!==g[o+1]&&e.push(g[o+1]),o++;e[0]!==g[0]&&e.unshift(g[0]),g=e}}),h.ordinal.originalOrdinalRange||(h.ordinal.originalOrdinalRange=(g.length-1)*v),m&&y&&(g.pop(),g.shift()),(i=g.length)>2){for(o=g[1]-g[0],a=i-1;a--&&!x;)g[a+1]-g[a]!==o&&(x=!0);!h.options.keepOrdinalPadding&&(g[0]-c>o||p-g[g.length-1]>o)&&(x=!0)}else h.options.overscroll&&(2===i?v=g[1]-g[0]:1===i?(v=h.ordinal.convertOverscroll(h.options.overscroll),g=[g[0],g[0]+v]):v=l.overscrollPointsRange);x||h.forceOrdinal?(h.options.overscroll&&(l.overscrollPointsRange=v,g=g.concat(l.getOverscrollPositions())),l.positions=g,r=h.ordinal2lin(Math.max(c,g[0]),!0),n=Math.max(h.ordinal2lin(Math.min(p,g[g.length-1]),!0),1),l.slope=s=(p-c)/(n-r),l.offset=c-r*s):(l.overscrollPointsRange=eu(h.closestPointRange,l.overscrollPointsRange),l.positions=h.ordinal.slope=l.offset=void 0)}h.isOrdinal=f&&x,l.groupIntervalFactor=null},t.findIndexOf=function(t,i,e){for(var o,r=0,n=t.length-1;r<n;)t[o=Math.ceil((r+n)/2)]<=i?r=o:n=o-1;return t[r]===i?r:e?r:-1},t.prototype.getExtendedPositions=function(t){void 0===t&&(t=!0);var i,e=this,o=e.axis,r=o.constructor.prototype,n=o.chart,s=o.series.reduce(function(t,i){var e=i.currentDataGrouping;return t+(e?e.count+e.unitName:"raw")},""),a=t?o.ordinal.convertOverscroll(o.options.overscroll):0,h=o.getExtremes(),l=void 0,d=e.index;return d||(d=e.index={}),!d[s]&&((i={series:[],chart:n,forceOrdinal:!1,getExtremes:function(){return{min:h.dataMin,max:h.dataMax+a}},applyGrouping:r.applyGrouping,getGroupPixelWidth:r.getGroupPixelWidth,getTimeTicks:r.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:r.ordinal2lin,getIndexOfPoint:r.getIndexOfPoint,val2lin:r.val2lin}).ordinal.axis=i,o.series.forEach(function(o){l={xAxis:i,chart:n,groupPixelWidth:o.groupPixelWidth,destroyGroupedData:R().noop,getColumn:o.getColumn,applyGrouping:o.applyGrouping,getProcessedData:o.getProcessedData,reserveSpace:o.reserveSpace,visible:o.visible};var r,s,a,h=o.getColumn("x").concat(t?e.getOverscrollPositions():[]);l.dataTable=new en({columns:{x:h}}),l.options=es(es({},o.options),{dataGrouping:o.currentDataGrouping?{firstAnchor:null===(r=o.options.dataGrouping)||void 0===r?void 0:r.firstAnchor,anchor:null===(s=o.options.dataGrouping)||void 0===s?void 0:s.anchor,lastAnchor:null===(a=o.options.dataGrouping)||void 0===a?void 0:a.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[o.currentDataGrouping.unitName,[o.currentDataGrouping.count]]]}:{enabled:!1}}),i.series.push(l),o.processData.apply(l)}),i.applyGrouping({hasExtremesChanged:!0}),(null==l?void 0:l.closestPointRange)!==(null==l?void 0:l.basePointRange)&&l.currentDataGrouping&&(i.forceOrdinal=!0),o.ordinal.beforeSetTickPositions.apply({axis:i}),!o.ordinal.originalOrdinalRange&&i.ordinal.originalOrdinalRange&&(o.ordinal.originalOrdinalRange=i.ordinal.originalOrdinalRange),i.ordinal.positions&&(d[s]=i.ordinal.positions)),d[s]},t.prototype.getGroupIntervalFactor=function(t,i,e){var o,r,n=e.getColumn("x",!0),s=n.length,a=[],h=this.groupIntervalFactor;if(!h){for(r=0;r<s-1;r++)a[r]=n[r+1]-n[r];a.sort(function(t,i){return t-i}),o=a[Math.floor(s/2)],t=Math.max(t,n[0]),i=Math.min(i,n[s-1]),this.groupIntervalFactor=h=s*o/(i-t)}return h},t.prototype.getIndexOfPoint=function(t,i){var e=this.axis,o=e.min,n=e.minPixelPadding;return r(i,o)+eh((t-n)/(e.translationSlope*(this.slope||e.closestPointRange||this.overscrollPointsRange)))},t.prototype.getOverscrollPositions=function(){var t=this.axis,i=this.convertOverscroll(t.options.overscroll),e=this.overscrollPointsRange,o=[],r=t.dataMax;if(ed(e))for(;r<t.dataMax+i;)o.push(r+=e);return o},t.prototype.postProcessTickInterval=function(t){var i,e=this.axis,o=this.slope,r=e.closestPointRange;return o&&r?e.options.breaks?r||t:t/(o/r):t},t.prototype.convertOverscroll=function(t){void 0===t&&(t=0);var i=this,e=i.axis,o=function(t){return eu(i.originalOrdinalRange,ed(e.dataMax)&&ed(e.dataMin)?e.dataMax-e.dataMin:0)*t};if(eg(t)){var r=parseInt(t,10),n=void 0;if(ed(e.min)&&ed(e.max)&&ed(e.dataMin)&&ed(e.dataMax)&&!(n=e.max-e.min==e.dataMax-e.dataMin)&&(this.originalOrdinalRange=e.max-e.min),/%$/.test(t))return o(r/100);if(/px/.test(t)){var s=Math.min(r,.9*e.len)/e.len;return o(s/(n?1-s:1))}return 0}return t},t}()}(A||(A={}));var ev=A,ex=R().defaultOptions,em=i8().format,ey=R().addEvent,eb=R().createElement,eM=R().css,ek=R().defined,eA=R().destroyObjectProperties,ew=R().diffObjects,eO=R().discardElement,eP=R().extend,eS=R().fireEvent,eE=R().isNumber,eB=R().isString,eT=R().merge,eC=R().objectEach,eI=R().pick,eR=R().splat;function eD(t){var i=function(i){return new RegExp("%[[a-zA-Z]*".concat(i)).test(t)};if(eB(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";var e=eB(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(i):t.dateStyle||t.day||t.month||t.year,o=eB(t)?["H","k","I","l","M","S"].some(i):t.timeStyle||t.hour||t.minute||t.second;return e&&o?"datetime-local":e?"date":o?"time":"text"}var eG=function(){function t(t){var i=this;this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=function(){var t=0;return i.buttons.forEach(function(i){var e=i.getBBox();e.width>t&&(t=e.width)}),t},this.init(t)}return t.compose=function(i,e){i3(i,e,t)},t.prototype.clickButton=function(t,i){var e,o,r,n,s,a=this.chart,h=this.buttonOptions[t],l=a.xAxis[0],d=a.scroller&&a.scroller.getUnionExtremes()||l||{},c=h.type,p=h.dataGrouping,u=d.dataMin,f=d.dataMax,g=eE(null==l?void 0:l.max)?Math.round(Math.min(l.max,null!=f?f:l.max)):void 0,v=h._range,x=!0;if(null!==u&&null!==f){if(this.setSelected(t),p&&(this.forcedDataGrouping=!0,tp().prototype.setDataGrouping.call(l||{chart:this.chart},p,!1),this.frozenStates=h.preserveDataGrouping),"month"===c||"year"===c)l?(n={range:h,max:g,chart:a,dataMin:u,dataMax:f},e=l.minFromRange.call(n),eE(n.newMax)&&(g=n.newMax),x=!1):v=h;else if(v)eE(g)&&(g=Math.min((e=Math.max(g-v,u))+v,f),x=!1);else if("ytd"===c){if(l)!l.hasData()||eE(f)&&eE(u)||(u=Number.MAX_VALUE,f=-Number.MAX_VALUE,a.series.forEach(function(t){var i=t.getColumn("x");i.length&&(u=Math.min(i[0],u),f=Math.max(i[i.length-1],f))}),i=!1),eE(f)&&eE(u)&&(e=r=(s=this.getYTDExtremes(f,u)).min,g=s.max);else{this.deferredYTDClick=t;return}}else"all"===c&&l&&(a.navigator&&a.navigator.baseSeries[0]&&(a.navigator.baseSeries[0].xAxis.options.range=void 0),e=u,g=f);if(x&&h._offsetMin&&ek(e)&&(e+=h._offsetMin),h._offsetMax&&ek(g)&&(g+=h._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),l)eE(e)&&eE(g)&&(l.setExtremes(e,g,eI(i,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:h}),a.setFixedRange(h._range));else{o=eR(a.options.xAxis||{})[0];var m=ey(a,"afterCreateAxes",function(){var t=a.xAxis[0];t.range=t.options.range=v,t.min=t.options.min=r});ey(a,"load",function(){var t=a.xAxis[0];a.setFixedRange(h._range),t.options.range=o.range,t.options.min=o.min,m()})}eS(this,"afterBtnClick")}},t.prototype.setSelected=function(t){this.selected=this.options.selected=t},t.prototype.init=function(t){var i=this,e=t.options.rangeSelector,o=t.options.lang,r=e.buttons,n=e.selected,s=function(){var t=i.minInput,e=i.maxInput;t&&t.blur&&eS(t,"blur"),e&&e.blur&&eS(e,"blur")};i.chart=t,i.options=e,i.buttons=[],i.buttonOptions=r.map(function(t){var i,e;return t.type&&o.rangeSelector&&(null!==(i=t.text)&&void 0!==i||(t.text=o.rangeSelector[""+t.type+"Text"]),null!==(e=t.title)&&void 0!==e||(t.title=o.rangeSelector[""+t.type+"Title"])),t.text=em(t.text,{count:t.count||1}),t.title=em(t.title,{count:t.count||1}),t}),this.eventsToUnbind=[],this.eventsToUnbind.push(ey(t.container,"mousedown",s)),this.eventsToUnbind.push(ey(t,"resize",s)),r.forEach(i.computeButtonRange),void 0!==n&&r[n]&&this.clickButton(n,!1),this.eventsToUnbind.push(ey(t,"load",function(){t.xAxis&&t.xAxis[0]&&ey(t.xAxis[0],"setExtremes",function(e){eE(this.max)&&eE(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==e.trigger&&"updatedData"!==e.trigger&&i.forcedDataGrouping&&!i.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()},t.prototype.updateButtonStates=function(){var t=this,i=this.chart,e=this.dropdown,o=this.dropdownLabel,r=i.xAxis[0],n=Math.round(r.max-r.min),s=!r.hasVisibleSeries,a=24*36e5,h=i.scroller&&i.scroller.getUnionExtremes()||r,l=h.dataMin,d=h.dataMax,c=t.getYTDExtremes(d,l),p=c.min,u=c.max,f=t.selected,g=t.options.allButtonsEnabled,v=Array(t.buttonOptions.length).fill(0),x=eE(f),m=t.buttons,y=!1,b=null;t.buttonOptions.forEach(function(i,e){var o,h=i._range,c=i.type,m=i.count||1,M=i._offsetMax-i._offsetMin,k=e===f,A=h>d-l,w=h<r.minRange,O=!1,P=h===n;if(k&&A&&(y=!0),r.isOrdinal&&(null===(o=r.ordinal)||void 0===o?void 0:o.positions)&&h&&n<h){var S=r.ordinal.positions,E=ev.Additions.findIndexOf(S,r.min,!0),B=Math.min(ev.Additions.findIndexOf(S,r.max,!0)+1,S.length-1);S[B]-S[E]>h&&(P=!0)}else("month"===c||"year"===c)&&n+36e5>=({month:28,year:365})[c]*a*m-M&&n-36e5<=({month:31,year:366})[c]*a*m+M?P=!0:"ytd"===c?(P=u-p+M===n,O=!k):"all"===c&&(P=r.max-r.min>=d-l);var T=!g&&!(y&&"all"===c)&&(A||w||s),C=y&&"all"===c||!O&&P||k&&t.frozenStates;T?v[e]=3:C&&(!x||e===f)&&(b=e)}),null!==b?(v[b]=2,t.setSelected(b),this.dropdown&&(this.dropdown.selectedIndex=b+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),o&&(o.setState(0),o.attr({text:(ex.lang.rangeSelectorZoom||"")+" ▾"})));for(var M=0;M<v.length;M++){var k=v[M],A=m[M];if(A.state!==k&&(A.setState(k),e)){e.options[M+1].disabled=3===k,2===k&&(o&&(o.setState(2),o.attr({text:t.buttonOptions[M].text+" ▾"})),e.selectedIndex=M+1);var w=o.getBBox();eM(e,{width:""+w.width+"px",height:""+w.height+"px"})}}},t.prototype.computeButtonRange=function(t){var i=t.type,e=t.count||1,o={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};o[i]?t._range=o[i]*e:("month"===i||"year"===i)&&(t._range=24*({month:30,year:365})[i]*36e5*e),t._offsetMin=eI(t.offsetMin,0),t._offsetMax=eI(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin},t.prototype.getInputValue=function(t){var i="min"===t?this.minInput:this.maxInput,e=this.chart.options.rangeSelector,o=this.chart.time;return i?("text"===i.type&&e.inputDateParser||this.defaultInputDateParser)(i.value,"UTC"===o.timezone,o):0},t.prototype.setInputValue=function(t,i){var e=this.options,o=this.chart.time,r="min"===t?this.minInput:this.maxInput,n="min"===t?this.minDateBox:this.maxDateBox;if(r){r.setAttribute("type",eD(e.inputDateFormat||"%e %b %Y"));var s=r.getAttribute("data-hc-time"),a=ek(s)?Number(s):void 0;if(ek(i)){var h=a;ek(h)&&r.setAttribute("data-hc-time-previous",h),r.setAttribute("data-hc-time",i),a=i}r.value=o.dateFormat(this.inputTypeFormats[r.type]||e.inputEditDateFormat,a),n&&n.attr({text:o.dateFormat(e.inputDateFormat,a)})}},t.prototype.setInputExtremes=function(t,i,e){var o="min"===t?this.minInput:this.maxInput;if(o){var r=this.inputTypeFormats[o.type],n=this.chart.time;if(r){var s=n.dateFormat(r,i);o.min!==s&&(o.min=s);var a=n.dateFormat(r,e);o.max!==a&&(o.max=a)}}},t.prototype.showInput=function(t){var i="min"===t?this.minDateBox:this.maxDateBox,e="min"===t?this.minInput:this.maxInput;if(e&&i&&this.inputGroup){var o="text"===e.type,r=this.inputGroup,n=r.translateX,s=void 0===n?0:n,a=r.translateY,h=void 0===a?0:a,l=i.x,d=void 0===l?0:l,c=i.width,p=void 0===c?0:c,u=i.height,f=void 0===u?0:u,g=this.options.inputBoxWidth;eM(e,{width:o?p+(g?-2:20)+"px":"auto",height:f-2+"px",border:"2px solid silver"}),o&&g?eM(e,{left:s+d+"px",top:h+"px"}):eM(e,{left:Math.min(Math.round(d+s-(e.offsetWidth-p)/2),this.chart.chartWidth-e.offsetWidth)+"px",top:h-(e.offsetHeight-f)/2+"px"})}},t.prototype.hideInput=function(t){var i="min"===t?this.minInput:this.maxInput;i&&eM(i,{top:"-9999em",border:0,width:"1px",height:"1px"})},t.prototype.defaultInputDateParser=function(t,i,e){return(null==e?void 0:e.parse(t))||0},t.prototype.drawInput=function(t){var i=this.chart,e=this.div,o=this.inputGroup,r=this,n=i.renderer.style||{},s=i.renderer,a=i.options.rangeSelector,h=ex.lang,l="min"===t;function d(t){var e,o=r.maxInput,n=r.minInput,s=i.xAxis[0],a=(null===(e=i.scroller)||void 0===e?void 0:e.getUnionExtremes())||s,h=a.dataMin,d=a.dataMax,c=i.xAxis[0].getExtremes()[t],p=r.getInputValue(t);eE(p)&&p!==c&&(l&&o&&eE(h)?p>Number(o.getAttribute("data-hc-time"))?p=void 0:p<h&&(p=h):n&&eE(d)&&(p<Number(n.getAttribute("data-hc-time"))?p=void 0:p>d&&(p=d)),void 0!==p&&s.setExtremes(l?p:s.min,l?s.max:p,void 0,void 0,{trigger:"rangeSelectorInput"}))}var c=h[l?"rangeSelectorFrom":"rangeSelectorTo"]||"",p=s.label(c,0).addClass("highcharts-range-label").attr({padding:2*!!c,height:c?a.inputBoxHeight:0}).add(o),u=s.label("",0).addClass("highcharts-range-input").attr({padding:2,width:a.inputBoxWidth,height:a.inputBoxHeight,"text-align":"center"}).on("click",function(){r.showInput(t),r[t+"Input"].focus()});i.styledMode||u.attr({stroke:a.inputBoxBorderColor,"stroke-width":1}),u.add(o);var f=eb("input",{name:t,className:"highcharts-range-selector"},void 0,e);f.setAttribute("type",eD(a.inputDateFormat||"%e %b %Y")),i.styledMode||(p.css(eT(n,a.labelStyle)),u.css(eT({color:"#333333"},n,a.inputStyle)),eM(f,eP({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:n.fontSize,fontFamily:n.fontFamily,top:"-9999em"},a.inputStyle))),f.onfocus=function(){r.showInput(t)},f.onblur=function(){f===R().doc.activeElement&&d(t),r.hideInput(t),r.setInputValue(t),f.blur()};var g=!1;return f.onchange=function(){g||(d(t),r.hideInput(t),f.blur())},f.onkeypress=function(i){13===i.keyCode&&d(t)},f.onkeydown=function(i){g=!0,("ArrowUp"===i.key||"ArrowDown"===i.key||"Tab"===i.key)&&d(t)},f.onkeyup=function(){g=!1},{dateBox:u,input:f,label:p}},t.prototype.getPosition=function(){var t=this.chart,i=t.options.rangeSelector,e="top"===i.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:e+i.buttonPosition.y,inputTop:e+i.inputPosition.y-10}},t.prototype.getYTDExtremes=function(t,i){var e=this.chart.time,o=e.toParts(t)[0];return{max:t,min:Math.max(i,e.makeTime(o,0))}},t.prototype.createElements=function(){var t,i=this.chart,e=i.renderer,o=i.container,r=i.options,n=r.rangeSelector,s=n.inputEnabled,a=eI(null===(t=r.chart.style)||void 0===t?void 0:t.zIndex,0)+1;!1!==n.enabled&&(this.group=e.g("range-selector-group").attr({zIndex:7}).add(),this.div=eb("div",void 0,{position:"relative",height:0,zIndex:a}),this.buttonOptions.length&&this.renderButtons(),o.parentNode&&o.parentNode.insertBefore(this.div,o),s&&this.createInputs())},t.prototype.createInputs=function(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);var t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;var i=this.drawInput("max");this.maxDateBox=i.dateBox,this.maxLabel=i.label,this.maxInput=i.input},t.prototype.render=function(t,i){if(!1!==this.options.enabled){var e,o,r=this.chart,n=r.options.rangeSelector;if(n.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",i),this.chart.styledMode||(null===(e=this.maxLabel)||void 0===e||e.css(n.labelStyle),null===(o=this.minLabel)||void 0===o||o.css(n.labelStyle));var s=r.scroller&&r.scroller.getUnionExtremes()||r.xAxis[0]||{};if(ek(s.dataMin)&&ek(s.dataMax)){var a=r.xAxis[0].minRange||0;this.setInputExtremes("min",s.dataMin,Math.min(s.dataMax,this.getInputValue("max"))-a),this.setInputExtremes("max",Math.max(s.dataMin,this.getInputValue("min"))+a,s.dataMax)}if(this.inputGroup){var h=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(function(t){if(t){var i=t.getBBox().width;i&&(t.attr({x:h}),h+=i+n.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(n.labelStyle),this.alignElements(),this.updateButtonStates()}},t.prototype.renderButtons=function(){var t,i,e,o=this,r=this.chart,n=this.options,s=ex.lang,a=r.renderer,h=eT(n.buttonTheme),l=h&&h.states;delete h.width,delete h.states,this.buttonGroup=a.g("range-selector-buttons").add(this.group);var d=this.dropdown=eb("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),c=null===(t=r.userOptions.rangeSelector)||void 0===t?void 0:t.buttonTheme;this.dropdownLabel=a.button("",0,0,function(){},eT(h,{"stroke-width":eI(h["stroke-width"],0),width:"auto",paddingLeft:eI(n.buttonTheme.paddingLeft,null==c?void 0:c.padding,8),paddingRight:eI(n.buttonTheme.paddingRight,null==c?void 0:c.padding,8)}),l&&l.hover,l&&l.select,l&&l.disabled).hide().add(this.group),ey(d,"touchstart",function(){d.style.fontSize="16px"});var p=R().isMS?"mouseover":"mouseenter",u=R().isMS?"mouseout":"mouseleave";ey(d,p,function(){eS(o.dropdownLabel.element,p)}),ey(d,u,function(){eS(o.dropdownLabel.element,u)}),ey(d,"change",function(){eS(o.buttons[d.selectedIndex-1].element,"click")}),this.zoomText=a.label(s.rangeSelectorZoom||"",0).attr({padding:n.buttonTheme.padding,height:n.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(n.labelStyle),(null===(i=(e=n.buttonTheme)["stroke-width"])||void 0===i)&&(e["stroke-width"]=0)),eb("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,d),this.createButtons()},t.prototype.createButtons=function(){var t=this,i=eT(this.options.buttonTheme),e=i&&i.states,o=i.width||28;delete i.width,delete i.states,this.buttonOptions.forEach(function(i,r){t.createButton(i,r,o,e)})},t.prototype.createButton=function(t,i,e,o){var r,n=this,s=this.dropdown,a=this.buttons,h=this.chart,l=this.options,d=h.renderer,c=eT(l.buttonTheme);null==s||s.add(eb("option",{textContent:t.title||t.text}),i+2),a[i]=d.button(null!==(r=t.text)&&void 0!==r?r:"",0,0,function(e){var o,r=t.events&&t.events.click;r&&(o=r.call(t,e)),!1!==o&&n.clickButton(i),n.isActive=!0},c,o&&o.hover,o&&o.select,o&&o.disabled).attr({"text-align":"center",width:e}).add(this.buttonGroup),t.title&&a[i].attr("title",t.title)},t.prototype.alignElements=function(){var t,i=this,e=this.buttonGroup,o=this.buttons,r=this.chart,n=this.group,s=this.inputGroup,a=this.options,h=this.zoomText,l=r.options,d=l.exporting&&!1!==l.exporting.enabled&&l.navigation&&l.navigation.buttonOptions,c=a.buttonPosition,p=a.inputPosition,u=a.verticalAlign,f=function(t,e,o){return d&&i.titleCollision(r)&&"top"===u&&o&&e.y-t.getBBox().height-12<(d.y||0)+(d.height||0)+r.spacing[0]?-40:0},g=r.plotLeft;if(n&&c&&p){var v=c.x-r.spacing[3];if(e){if(this.positionButtons(),!this.initialButtonGroupWidth){var x=0;h&&(x+=h.getBBox().width+5),o.forEach(function(t,i){x+=t.width||0,i!==o.length-1&&(x+=a.buttonSpacing)}),this.initialButtonGroupWidth=x}g-=r.spacing[3];var m=f(e,c,"right"===c.align||"right"===p.align);this.alignButtonGroup(m),(null===(t=this.buttonGroup)||void 0===t?void 0:t.translateY)&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),n.placed=e.placed=r.hasLoaded}var y=0;a.inputEnabled&&s&&(y=f(s,p,"right"===c.align||"right"===p.align),"left"===p.align?v=g:"right"===p.align&&(v=-Math.max(r.axisOffset[1],-y)),s.align({y:p.y,width:s.getBBox().width,align:p.align,x:p.x+v-2},!0,r.spacingBox),s.placed=r.hasLoaded),this.handleCollision(y),n.align({verticalAlign:u},!0,r.spacingBox);var b=n.alignAttr.translateY,M=n.getBBox().height+20,k=0;if("bottom"===u){var A=r.legend&&r.legend.options;k=b-(M=M+(A&&"bottom"===A.verticalAlign&&A.enabled&&!A.floating?r.legend.legendHeight+eI(A.margin,10):0)-20)-(a.floating?0:a.y)-(r.titleOffset?r.titleOffset[2]:0)-10}"top"===u?(a.floating&&(k=0),r.titleOffset&&r.titleOffset[0]&&(k=r.titleOffset[0]),k+=r.margin[0]-r.spacing[0]||0):"middle"===u&&(p.y===c.y?k=b:(p.y||c.y)&&(p.y<0||c.y<0?k-=Math.min(p.y,c.y):k=b-M)),n.translate(a.x,a.y+Math.floor(k));var w=this.minInput,O=this.maxInput,P=this.dropdown;a.inputEnabled&&w&&O&&(w.style.marginTop=n.translateY+"px",O.style.marginTop=n.translateY+"px"),P&&(P.style.marginTop=n.translateY+"px")}},t.prototype.redrawElements=function(){var t,i,e,o,r,n,s,a=this.chart,h=this.options,l=h.inputBoxHeight,d=h.inputBoxBorderColor;if(null===(t=this.maxDateBox)||void 0===t||t.attr({height:l}),null===(i=this.minDateBox)||void 0===i||i.attr({height:l}),a.styledMode||(null===(e=this.maxDateBox)||void 0===e||e.attr({stroke:d}),null===(o=this.minDateBox)||void 0===o||o.attr({stroke:d})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;var c=null!==(r=this.options.buttons)&&void 0!==r?r:[],p=Math.min(c.length,this.buttonOptions.length),u=this.dropdown,f=eT(this.options.buttonTheme),g=f&&f.states,v=f.width||28;if(c.length<this.buttonOptions.length)for(var x=this.buttonOptions.length-1;x>=c.length;x--){var m=this.buttons.pop();null==m||m.destroy(),null===(n=this.dropdown)||void 0===n||n.options.remove(x+1)}for(var x=p-1;x>=0;x--)if(0!==Object.keys(ew(c[x],this.buttonOptions[x])).length){var y=c[x];this.buttons[x].destroy(),null==u||u.options.remove(x+1),this.createButton(y,x,v,g),this.computeButtonRange(y)}if(c.length>this.buttonOptions.length)for(var x=this.buttonOptions.length;x<c.length;x++)this.createButton(c[x],x,v,g),this.computeButtonRange(c[x]);this.buttonOptions=null!==(s=this.options.buttons)&&void 0!==s?s:[],ek(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}},t.prototype.alignButtonGroup=function(t,i){var e=this.chart,o=this.options,r=this.buttonGroup,n=this.dropdown,s=this.dropdownLabel,a=o.buttonPosition,h=e.plotLeft-e.spacing[3],l=a.x-e.spacing[3],d=e.plotLeft;"right"===a.align?(l+=t-h,this.hasVisibleDropdown&&(d=e.chartWidth+t-this.maxButtonWidth()-20)):"center"===a.align&&(l-=h/2,this.hasVisibleDropdown&&(d=e.chartWidth/2-this.maxButtonWidth())),n&&eM(n,{left:d+"px",top:(null==r?void 0:r.translateY)+"px"}),null==s||s.attr({x:d}),r&&r.align({y:a.y,width:eI(i,this.initialButtonGroupWidth),align:a.align,x:l},!0,e.spacingBox)},t.prototype.positionButtons=function(){var t=this.buttons,i=this.chart,e=this.options,o=this.zoomText,r=i.hasLoaded?"animate":"attr",n=e.buttonPosition,s=i.plotLeft,a=s;o&&"hidden"!==o.visibility&&(o[r]({x:eI(s+n.x,s)}),a+=n.x+o.getBBox().width+5);for(var h=0,l=this.buttonOptions.length;h<l;++h)"hidden"!==t[h].visibility?(t[h][r]({x:a}),a+=(t[h].width||0)+e.buttonSpacing):t[h][r]({x:s})},t.prototype.handleCollision=function(t){var i=this.chart,e=this.buttonGroup,o=this.inputGroup,r=this.initialButtonGroupWidth,n=this.options,s=n.buttonPosition,a=n.dropdown,h=n.inputPosition,l=function(){o&&e&&o.attr({translateX:o.alignAttr.translateX+(i.axisOffset[1]>=-t?0:-t),translateY:o.alignAttr.translateY+e.getBBox().height+10})};o&&e?h.align===s.align?(l(),r>i.plotWidth+t-20?this.collapseButtons():this.expandButtons()):r-t+o.getBBox().width>i.plotWidth?"responsive"===a?this.collapseButtons():l():this.expandButtons():e&&"responsive"===a&&(r>i.plotWidth?this.collapseButtons():this.expandButtons()),e&&("always"===a&&this.collapseButtons(),"never"===a&&this.expandButtons()),this.alignButtonGroup(t)},t.prototype.collapseButtons=function(){var t=this.buttons,i=this.zoomText;!0!==this.isCollapsed&&(this.isCollapsed=!0,i.hide(),t.forEach(function(t){t.hide()}),this.showDropdown())},t.prototype.expandButtons=function(){var t=this.buttons,i=this.zoomText;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),i.show(),t.forEach(function(t){t.show()}),this.positionButtons())},t.prototype.showDropdown=function(){var t=this.buttonGroup,i=this.dropdownLabel,e=this.dropdown;t&&e&&(i.show(),eM(e,{visibility:"inherit"}),this.hasVisibleDropdown=!0)},t.prototype.hideDropdown=function(){var t=this.dropdown;t&&(this.dropdownLabel.hide(),eM(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)},t.prototype.getHeight=function(){var t=this.options,i=this.group,e=t.inputPosition,o=t.buttonPosition,r=t.y,n=o.y,s=e.y,a=0;if(t.height)return t.height;this.alignElements(),a=i?i.getBBox(!0).height+13+r:0;var h=Math.min(s,n);return(s<0&&n<0||s>0&&n>0)&&(a+=Math.abs(h)),a},t.prototype.titleCollision=function(t){return!(t.options.title.text||t.options.subtitle.text)},t.prototype.update=function(t,i){void 0===i&&(i=!0);var e=this.chart;if(eT(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,e.options.rangeSelector.selected=void 0),ek(t.enabled))return this.destroy(),this.init(e);this.isDirty=!!t.buttons,i&&this.render()},t.prototype.destroy=function(){var i=this,e=i.minInput,o=i.maxInput;i.eventsToUnbind&&(i.eventsToUnbind.forEach(function(t){return t()}),i.eventsToUnbind=void 0),eA(i.buttons),e&&(e.onfocus=e.onblur=e.onchange=null),o&&(o.onfocus=o.onblur=o.onchange=null),eC(i,function(e,o){e&&"chart"!==o&&(e instanceof i6()?e.destroy():e instanceof window.HTMLElement&&eO(e),delete i[o]),e!==t.prototype[o]&&(i[o]=null)},this),this.buttons=[]},t}();eP(eG.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}});var eL=function(t,i){for(var e=[],o=0;o<t.length;o++){var r=t[o][1],n=t[o][2];if("number"==typeof r&&"number"==typeof n){if(0===o)e.push(["M",r,n]);else if(o===t.length-1)e.push(["L",r,n]);else if(i){var s=t[o-1],a=t[o+1];if(s&&a){var h=s[1],l=s[2],d=a[1],c=a[2];if("number"==typeof h&&"number"==typeof d&&"number"==typeof l&&"number"==typeof c&&h!==d&&l!==c){var p=h<d?1:-1,u=l<c?1:-1;e.push(["L",r-p*Math.min(Math.abs(r-h),i),n-u*Math.min(Math.abs(n-l),i)],["C",r,n,r,n,r+p*Math.min(Math.abs(r-d),i),n+u*Math.min(Math.abs(n-c),i)])}}}else e.push(["L",r,n])}}return e},ez=R().pick,eN=Math.min,eW=Math.max,eH=Math.abs;function eF(t,i,e){for(var o,r,n=i-1e-7,s=e||0,a=t.length-1;s<=a;)if((r=n-t[o=a+s>>1].xMin)>0)s=o+1;else{if(!(r<0))return o;a=o-1}return s>0?s-1:0}function e_(t,i){for(var e,o=eF(t,i.x+1)+1;o--;)if(t[o].xMax>=i.x&&(e=t[o],i.x<=e.xMax&&i.x>=e.xMin&&i.y<=e.yMax&&i.y>=e.yMin))return o;return -1}function eU(t){var i=[];if(t.length){i.push(["M",t[0].start.x,t[0].start.y]);for(var e=0;e<t.length;++e)i.push(["L",t[e].end.x,t[e].end.y])}return i}function eX(t,i){t.yMin=eW(t.yMin,i.yMin),t.yMax=eN(t.yMax,i.yMax),t.xMin=eW(t.xMin,i.xMin),t.xMax=eN(t.xMax,i.xMax)}var eY=function(t,i,e){var o,r,n,s,a,h=[],l=e.chartObstacles,d=e_(l,t),c=e_(l,i),p=ez(e.startDirectionX,eH(i.x-t.x)>eH(i.y-t.y))?"x":"y";function u(t,i,e,o,r){var n={x:t.x,y:t.y};return n[i]=e[o||i]+(r||0),n}function f(t,i,e){var o=eH(i[e]-t[e+"Min"])>eH(i[e]-t[e+"Max"]);return u(i,e,t,e+(o?"Max":"Min"),o?1:-1)}c>-1?(o={start:n=f(l[c],i,p),end:i},a=n):a=i,d>-1&&(n=f(r=l[d],t,p),h.push({start:t,end:n}),n[p]>=t[p]==n[p]>=a[p]&&(s=t[p="y"===p?"x":"y"]<i[p],h.push({start:n,end:u(n,p,r,p+(s?"Max":"Min"),s?1:-1)}),p="y"===p?"x":"y"));var g=h.length?h[h.length-1].end:t;n=u(g,p,a),h.push({start:g,end:n});var v=u(n,p="y"===p?"x":"y",a);return h.push({start:n,end:v}),h.push(o),{path:eL(eU(h),e.radius),obstacles:h}};function ej(t,i,e){var o,r,n,s,a,h,l,d=ez(e.startDirectionX,eH(i.x-t.x)>eH(i.y-t.y)),c=d?"x":"y",p=[],u=e.obstacleMetrics,f=eN(t.x,i.x)-u.maxWidth-10,g=eW(t.x,i.x)+u.maxWidth+10,v=eN(t.y,i.y)-u.maxHeight-10,x=eW(t.y,i.y)+u.maxHeight+10,m=!1,y=e.chartObstacles,b=eF(y,g),M=eF(y,f);function k(t,i,e){var o,r,n,s,a=t.x<i.x?1:-1;t.x<i.x?(o=t,r=i):(o=i,r=t),t.y<i.y?(s=t,n=i):(s=i,n=t);for(var h=a<0?eN(eF(y,r.x),y.length-1):0;y[h]&&(a>0&&y[h].xMin<=r.x||a<0&&y[h].xMax>=o.x);){if(y[h].xMin<=r.x&&y[h].xMax>=o.x&&y[h].yMin<=n.y&&y[h].yMax>=s.y){if(e)return{y:t.y,x:t.x<i.x?y[h].xMin-1:y[h].xMax+1,obstacle:y[h]};return{x:t.x,y:t.y<i.y?y[h].yMin-1:y[h].yMax+1,obstacle:y[h]}}h+=a}return i}function A(t,i,e,o,r){var n=r.soft,s=r.hard,a=o?"x":"y",h={x:i.x,y:i.y},l={x:i.x,y:i.y},d=t[a+"Max"]>=n[a+"Max"],c=t[a+"Min"]<=n[a+"Min"],p=t[a+"Max"]>=s[a+"Max"],u=t[a+"Min"]<=s[a+"Min"],f=eH(t[a+"Min"]-i[a]),g=eH(t[a+"Max"]-i[a]),v=10>eH(f-g)?i[a]<e[a]:g<f;l[a]=t[a+"Min"],h[a]=t[a+"Max"];var x=k(i,l,o)[a]!==l[a],m=k(i,h,o)[a]!==h[a];return v=x?!m||v:!m&&v,v=c?!d||v:!d&&v,v=u?!p||v:!p&&v}for((b=e_(y=y.slice(M,b+1),i))>-1&&(o=y[b],r=i,n=eN(o.xMax-r.x,r.x-o.xMin)<eN(o.yMax-r.y,r.y-o.yMin),s=A(o,r,t,n,{soft:e.hardBounds,hard:e.hardBounds}),l=n?{y:r.y,x:o[s?"xMax":"xMin"]+(s?1:-1)}:{x:r.x,y:o[s?"yMax":"yMin"]+(s?1:-1)},p.push({end:i,start:l}),i=l);(b=e_(y,i))>-1;)h=i[c]-t[c]<0,(l={x:i.x,y:i.y})[c]=y[b][h?c+"Max":c+"Min"]+(h?1:-1),p.push({end:i,start:l}),i=l;return{path:eU(a=(a=function t(i,o,r){if(i.x===o.x&&i.y===o.y)return[];var n,s,a,h,l,d,c,p=r?"x":"y",u=e.obstacleOptions.margin,b={soft:{xMin:f,xMax:g,yMin:v,yMax:x},hard:e.hardBounds};return(l=e_(y,i))>-1?(h=A(l=y[l],i,o,r,b),eX(l,e.hardBounds),c=r?{y:i.y,x:l[h?"xMax":"xMin"]+(h?1:-1)}:{x:i.x,y:l[h?"yMax":"yMin"]+(h?1:-1)},(d=e_(y,c))>-1&&(eX(d=y[d],e.hardBounds),c[p]=h?eW(l[p+"Max"]-u+1,(d[p+"Min"]+l[p+"Max"])/2):eN(l[p+"Min"]+u-1,(d[p+"Max"]+l[p+"Min"])/2),i.x===c.x&&i.y===c.y?(m&&(c[p]=h?eW(l[p+"Max"],d[p+"Max"])+1:eN(l[p+"Min"],d[p+"Min"])-1),m=!m):m=!1),s=[{start:i,end:c}]):(n=k(i,{x:r?o.x:i.x,y:r?i.y:o.y},r),s=[{start:i,end:{x:n.x,y:n.y}}],n[r?"x":"y"]!==o[r?"x":"y"]&&(h=A(n.obstacle,n,o,!r,b),eX(n.obstacle,e.hardBounds),a={x:r?n.x:n.obstacle[h?"xMax":"xMin"]+(h?1:-1),y:r?n.obstacle[h?"yMax":"yMin"]+(h?1:-1):n.y},r=!r,s=s.concat(t({x:n.x,y:n.y},a,r)))),s=s.concat(t(s[s.length-1].end,o,!r))}(t,i,d)).concat(p.reverse())),obstacles:a}}eY.requiresObstacles=!0,ej.requiresObstacles=!0;var eV={connectors:{type:"straight",radius:0,lineWidth:1,marker:{enabled:!1,align:"center",verticalAlign:"middle",inside:!1,lineWidth:1},startMarker:{symbol:"diamond"},endMarker:{symbol:"arrow-filled"}}},eq=R().setOptions,eZ=R().defined,eK=R().error,e$=R().merge;function eJ(t){var i=t.shapeArgs;if(i)return{xMin:i.x||0,xMax:(i.x||0)+(i.width||0),yMin:i.y||0,yMax:(i.y||0)+(i.height||0)};var e=t.graphic&&t.graphic.getBBox();return e?{xMin:t.plotX-e.width/2,xMax:t.plotX+e.width/2,yMin:t.plotY-e.height/2,yMax:t.plotY+e.height/2}:null}!function(t){function i(t){var i,e,o=eJ(this);switch(t.align){case"right":i="xMax";break;case"left":i="xMin"}switch(t.verticalAlign){case"top":e="yMin";break;case"bottom":e="yMax"}return{x:i?o[i]:(o.xMin+o.xMax)/2,y:e?o[e]:(o.yMin+o.yMax)/2}}function e(t,i){var e;return!eZ(i)&&(e=eJ(this))&&(i={x:(e.xMin+e.xMax)/2,y:(e.yMin+e.yMax)/2}),Math.atan2(i.y-t.y,t.x-i.x)}function o(t,i,e){for(var o=2*Math.PI,r=eJ(this),n=r.xMax-r.xMin,s=r.yMax-r.yMin,a=Math.atan2(s,n),h=n/2,l=s/2,d=r.xMin+h,c=r.yMin+l,p={x:d,y:c},u=t,f=1,g=!1,v=1,x=1;u<-Math.PI;)u+=o;for(;u>Math.PI;)u-=o;return f=Math.tan(u),u>-a&&u<=a?(x=-1,g=!0):u>a&&u<=Math.PI-a?x=-1:u>Math.PI-a||u<=-(Math.PI-a)?(v=-1,g=!0):v=-1,g?(p.x+=v*h,p.y+=x*h*f):(p.x+=s/(2*f)*v,p.y+=x*l),e.x!==d&&(p.x=e.x),e.y!==c&&(p.y=e.y),{x:p.x+i*Math.cos(u),y:p.y-i*Math.sin(u)}}t.compose=function(t,r,n){var s=n.prototype;s.getPathfinderAnchorPoint||(t.prototype.callbacks.push(function(t){if(!1!==t.options.connectors.enabled)(t.options.pathfinder||t.series.reduce(function(t,i){return i.options&&e$(!0,i.options.connectors=i.options.connectors||{},i.options.pathfinder),t||i.options&&i.options.pathfinder},!1))&&(e$(!0,t.options.connectors=t.options.connectors||{},t.options.pathfinder),eK('WARNING: Pathfinder options have been renamed. Use "chart.connectors" or "series.connectors" instead.')),this.pathfinder=new r(this),this.pathfinder.update(!0)}),s.getMarkerVector=o,s.getPathfinderAnchorPoint=i,s.getRadiansToVector=e,eq(eV))}}(w||(w={}));var eQ=w,e0=T(260),e1=T.n(e0),e2=R().addEvent,e3=R().defined,e5=R().pick,e6=R().splat,e4=Math.max,e8=Math.min,e9=function(){function t(t){this.init(t)}return t.compose=function(i,e){eQ.compose(i,t,e)},t.prototype.init=function(t){this.chart=t,this.connections=[],e2(t,"redraw",function(){this.pathfinder.update()})},t.prototype.update=function(t){var i=this.chart,e=this,o=e.connections;e.connections=[],i.series.forEach(function(t){t.visible&&!t.options.isInternal&&t.points.forEach(function(t){var o,r,n=t.options;n&&n.dependency&&(n.connect=n.dependency);var s=(null===(o=t.options)||void 0===o?void 0:o.connect)?e6(t.options.connect):[];t.visible&&!1!==t.isInside&&s.forEach(function(o){var n="string"==typeof o?o:o.to;n&&(r=i.get(n)),r instanceof e1()&&r.series.visible&&r.visible&&!1!==r.isInside&&e.connections.push(new j(t,r,"string"==typeof o?{}:o))})})});for(var r=0,n=void 0,s=void 0,a=o.length,h=e.connections.length;r<a;++r){s=!1;var l=o[r];for(n=0;n<h;++n){var d=e.connections[n];if((l.options&&l.options.type)===(d.options&&d.options.type)&&l.fromPoint===d.fromPoint&&l.toPoint===d.toPoint){d.graphics=l.graphics,s=!0;break}}s||l.destroy()}delete this.chartObstacles,delete this.lineObstacles,e.renderConnections(t)},t.prototype.renderConnections=function(t){t?this.chart.series.forEach(function(t){var i=function(){var i=t.chart.pathfinder;(i&&i.connections||[]).forEach(function(i){i.fromPoint&&i.fromPoint.series===t&&i.render()}),t.pathfinderRemoveRenderEvent&&(t.pathfinderRemoveRenderEvent(),delete t.pathfinderRemoveRenderEvent)};!1===t.options.animation?i():t.pathfinderRemoveRenderEvent=e2(t,"afterAnimate",i)}):this.connections.forEach(function(t){t.render()})},t.prototype.getChartObstacles=function(t){for(var i,e=this.chart.series,o=e5(t.algorithmMargin,0),r=[],n=0,s=e.length;n<s;++n)if(e[n].visible&&!e[n].options.isInternal)for(var a=0,h=e[n].points.length,l=void 0,d=void 0;a<h;++a)(d=e[n].points[a]).visible&&(l=function(t){var i=t.shapeArgs;if(i)return{xMin:i.x||0,xMax:(i.x||0)+(i.width||0),yMin:i.y||0,yMax:(i.y||0)+(i.height||0)};var e=t.graphic&&t.graphic.getBBox();return e?{xMin:t.plotX-e.width/2,xMax:t.plotX+e.width/2,yMin:t.plotY-e.height/2,yMax:t.plotY+e.height/2}:null}(d))&&r.push({xMin:l.xMin-o,xMax:l.xMax+o,yMin:l.yMin-o,yMax:l.yMax+o});return r=r.sort(function(t,i){return t.xMin-i.xMin}),e3(t.algorithmMargin)||(i=t.algorithmMargin=function(t){for(var i,e=t.length,o=[],r=0;r<e;++r)for(var n=r+1;n<e;++n)(i=function t(i,e,o){var r=e5(o,10),n=i.yMax+r>e.yMin-r&&i.yMin-r<e.yMax+r,s=i.xMax+r>e.xMin-r&&i.xMin-r<e.xMax+r,a=n?i.xMin>e.xMax?i.xMin-e.xMax:e.xMin-i.xMax:1/0,h=s?i.yMin>e.yMax?i.yMin-e.yMax:e.yMin-i.yMax:1/0;return s&&n?r?t(i,e,Math.floor(r/2)):1/0:e8(a,h)}(t[r],t[n]))<80&&o.push(i);return o.push(80),e4(Math.floor(o.sort(function(t,i){return t-i})[Math.floor(o.length/10)]/2-1),1)}(r),r.forEach(function(t){t.xMin-=i,t.xMax+=i,t.yMin-=i,t.yMax+=i})),r},t.prototype.getObstacleMetrics=function(t){for(var i,e,o=0,r=0,n=t.length;n--;)i=t[n].xMax-t[n].xMin,e=t[n].yMax-t[n].yMin,o<i&&(o=i),r<e&&(r=e);return{maxHeight:r,maxWidth:o}},t.prototype.getAlgorithmStartDirection=function(t){var i="left"!==t.align&&"right"!==t.align,e="top"!==t.verticalAlign&&"bottom"!==t.verticalAlign;return i?!!e&&void 0:!!e||void 0},t}();e9.prototype.algorithms={fastAvoid:ej,straight:function(t,i){return{path:[["M",t.x,t.y],["L",i.x,i.y]],obstacles:[{start:t,end:i}]}},simpleConnect:eY};var e7=R();e7.Pathfinder=e7.Pathfinder||e9,N(e7.SVGRenderer),e7.Pathfinder.compose(e7.Chart,e7.Point);var ot=R().addEvent,oi=R().defined,oe=R().isNumber,oo=R().pick;function or(){var t=this.chart.options.chart;!this.horiz&&oe(this.options.staticScale)&&(!t.height||t.scrollablePlotArea&&t.scrollablePlotArea.minHeight)&&(this.staticScale=this.options.staticScale)}function on(){if("adjustHeight"!==this.redrawTrigger){for(var t=0,i=this.axes||[];t<i.length;t++)!function(t){var i=t.chart,e=!!i.initiatedScale&&i.options.animation,o=t.options.staticScale;if(t.staticScale&&oi(t.min)){var r=oo(t.brokenAxis&&t.brokenAxis.unitLength,t.max+t.tickInterval-t.min)*o,n=(r=Math.max(r,o))-i.plotHeight;!i.scrollablePixelsY&&Math.abs(n)>=1&&(i.plotHeight=r,i.redrawTrigger="adjustHeight",i.setSize(void 0,i.chartHeight+n,e)),t.series.forEach(function(t){var e=t.sharedClipKey&&i.sharedClips[t.sharedClipKey];e&&e.attr(i.inverted?{width:i.plotHeight}:{height:i.plotHeight})})}}(i[t]);this.initiatedScale=!0}this.redrawTrigger=null}var os=function(t,i){var e=i.prototype;e.adjustHeight||(ot(t,"afterSetOptions",or),e.adjustHeight=on,ot(i,"render",e.adjustHeight))},oa=R();os(oa.Axis,oa.Chart);var oh=R().correctFloat,ol=R().isNumber,od=R().isObject,oc={colorByPoint:!0,dataLabels:{formatter:function(){var t=this.partialFill;if(od(t)&&(t=t.amount),ol(t)&&t>0)return oh(100*t)+"%"},inside:!0,verticalAlign:"middle",style:{whiteSpace:"nowrap"}},tooltip:{headerFormat:'<span style="font-size: 0.8em">{ucfirst point.x} - {point.x2}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.yCategory}</b><br/>'},borderRadius:3,pointRange:0},op=(f=function(t,i){return(f=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw TypeError("Class extends value "+String(i)+" is not a constructor or null");function e(){this.constructor=t}f(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),ou=tN().seriesTypes.column.prototype.pointClass,of=R().extend,og=function(t){function i(i,e){var o=t.call(this,i,e)||this;return o.y||(o.y=0),o}return op(i,t),i.getColorByCategory=function(t,i){var e=t.options.colors||t.chart.options.colors,o=e?e.length:t.chart.options.chart.colorCount,r=i.y%o,n=null==e?void 0:e[r];return{colorIndex:r,color:n}},i.prototype.resolveColor=function(){var t=this.series;if(t.options.colorByPoint&&!this.options.color){var e=i.getColorByCategory(t,this);t.chart.styledMode||(this.color=e.color),this.options.colorIndex||(this.colorIndex=e.colorIndex)}else this.color=this.options.color||t.color},i.prototype.applyOptions=function(i,e){var o;return t.prototype.applyOptions.call(this,i,e),this.x2=this.series.chart.time.parse(this.x2),this.isNull=!(null===(o=this.isValid)||void 0===o?void 0:o.call(this)),this},i.prototype.setState=function(){t.prototype.setState.apply(this,arguments),this.series.drawPoint(this,this.series.getAnimationVerb())},i.prototype.isValid=function(){return"number"==typeof this.x&&"number"==typeof this.x2},i}(ou);of(og.prototype,{ttBelow:!1,tooltipDateKeys:["x","x2"]});var ov=(g=function(t,i){return(g=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw TypeError("Class extends value "+String(i)+" is not a constructor or null");function e(){this.constructor=t}g(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),ox=R().composed,om=R().noop,oy=tL().parse,ob=tN().seriesTypes.column,oM=R().addEvent,ok=R().clamp,oA=R().crisp,ow=R().defined,oO=R().extend,oP=R().find,oS=R().isNumber,oE=R().isObject,oB=R().merge,oT=R().pick,oC=R().pushUnique,oI=R().relativeLength;function oR(){var t,i;if(this.isXAxis){t=oT(this.dataMax,-Number.MAX_VALUE);for(var e=0,o=this.series;e<o.length;e++){var r=o[e],n=r.dataTable.getColumn("x2",!0)||r.dataTable.getColumn("end",!0);if(n)for(var s=0;s<n.length;s++){var a=n[s];oS(a)&&a>t&&(t=a,i=!0)}}i&&(this.dataMax=t)}}var oD=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return ov(i,t),i.compose=function(t){oC(ox,"Series.XRange")&&oM(t,"afterGetSeriesExtremes",oR)},i.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options.stacking=void 0},i.prototype.getColumnMetrics=function(){var i=this,e=function(){for(var t=0,e=i.chart.series;t<e.length;t++){var o=e[t],r=o.xAxis;o.xAxis=o.yAxis,o.yAxis=r}};e();var o=t.prototype.getColumnMetrics.call(this);return e(),o},i.prototype.cropData=function(i,e,o){var r=i.getColumn("x")||[],n=i.getColumn("x2");i.setColumn("x",n,void 0,{silent:!0});var s=t.prototype.cropData.call(this,i,e,o);return i.setColumn("x",r.slice(s.start,s.end),void 0,{silent:!0}),s},i.prototype.findPointIndex=function(t){var i,e=this.cropStart,o=this.points,r=t.id;if(r){var n=oP(o,function(t){return t.id===r});i=n?n.index:void 0}if(void 0===i){var n=oP(o,function(i){return i.x===t.x&&i.x2===t.x2&&!i.touched});i=n?n.index:void 0}return this.cropped&&oS(i)&&oS(e)&&i>=e&&(i-=e),i},i.prototype.alignDataLabel=function(i){var e,o,r=i.plotX;i.plotX=oT(null===(e=i.dlBox)||void 0===e?void 0:e.centerX,i.plotX),i.dataLabel&&(null===(o=i.shapeArgs)||void 0===o?void 0:o.width)&&i.dataLabel.css({width:""+i.shapeArgs.width+"px"}),t.prototype.alignDataLabel.apply(this,arguments),i.plotX=r},i.prototype.translatePoint=function(t){var i,e,o,r,n,s,a,h,l,d=this.xAxis,c=this.yAxis,p=this.columnMetrics,u=this.options,f=u.minPointLength||0,g=((null===(i=t.shapeArgs)||void 0===i?void 0:i.width)||0)/2,v=this.pointXOffset=p.offset,x=oT(t.x2,t.x+(t.len||0)),m=u.borderRadius,y=this.chart.plotTop,b=this.chart.plotLeft,M=t.plotX,k=d.translate(x,0,0,0,1),A=Math.abs(k-M),w=this.chart.inverted,O=oT(u.borderWidth,1),P=p.offset,S=Math.round(p.width);f&&((r=f-A)<0&&(r=0),M-=r/2,k+=r/2),M=Math.max(M,-10),k=ok(k,-10,d.len+10),ow(t.options.pointWidth)&&(P-=(Math.ceil(t.options.pointWidth)-S)/2,S=Math.ceil(t.options.pointWidth)),u.pointPlacement&&oS(t.plotY)&&c.categories&&(t.plotY=c.translate(t.y,0,1,0,1,u.pointPlacement));var E=oA(Math.min(M,k),O),B=oA(Math.max(M,k),O)-E,T=Math.min(oI("object"==typeof m?m.radius:m||0,S),Math.min(B,S)/2),C={x:E,y:oA((t.plotY||0)+P,O),width:B,height:S,r:T};t.shapeArgs=C,w?t.tooltipPos[1]+=v+g:t.tooltipPos[0]-=g+v-C.width/2,a=(s=C.x)+C.width,s<0||a>d.len?(s=ok(s,0,d.len),h=(a=ok(a,0,d.len))-s,t.dlBox=oB(C,{x:s,width:a-s,centerX:h?h/2:null})):t.dlBox=null;var I=t.tooltipPos,R=+!!w,D=+!w,G=this.columnMetrics?this.columnMetrics.offset:-p.width/2;w?I[R]+=C.width/2:I[R]=ok(I[R]+(d.reversed?-1:0)*C.width,d.left-b,d.left+d.len-b-1),I[D]=ok(I[D]+(w?-1:1)*G,c.top-y,c.top+c.len-y-1),(n=t.partialFill)&&(oE(n)&&(n=n.amount),oS(n)||(n=0),t.partShapeArgs=oB(C),l=Math.max(Math.round(A*n+t.plotX-M),0),t.clipRectArgs={x:d.reversed?C.x+A-l:C.x,y:C.y,width:l,height:C.height}),t.key=t.category||t.name,t.yCategory=null===(e=c.categories)||void 0===e?void 0:e[null!==(o=t.y)&&void 0!==o?o:-1]},i.prototype.translate=function(){t.prototype.translate.apply(this,arguments);for(var i=0,e=this.points;i<e.length;i++){var o=e[i];this.translatePoint(o)}},i.prototype.drawPoint=function(t,i){var e=this.options,o=this.chart.renderer,r=t.shapeType,n=t.shapeArgs,s=t.partShapeArgs,a=t.clipRectArgs,h=t.state,l=e.states[h||"normal"]||{},d=void 0===h?"attr":i,c=this.pointAttribs(t,h),p=oT(this.chart.options.chart.animation,l.animation),u=t.graphic,f=t.partialFill;if(t.isNull||!1===t.visible)u&&(t.graphic=u.destroy());else if(u?u.rect[i](n):(t.graphic=u=o.g("point").addClass(t.getClassName()).add(t.group||this.group),u.rect=o[r](oB(n)).addClass(t.getClassName()).addClass("highcharts-partfill-original").add(u)),s&&(u.partRect?(u.partRect[i](oB(s)),u.partialClipRect[i](oB(a))):(u.partialClipRect=o.clipRect(a.x,a.y,a.width,a.height),u.partRect=o[r](s).addClass("highcharts-partfill-overlay").add(u).clip(u.partialClipRect))),!this.chart.styledMode&&(u.rect[i](c,p).shadow(e.shadow),s)){oE(f)||(f={}),oE(e.partialFill)&&(f=oB(e.partialFill,f));var g=f.fill||oy(c.fill).brighten(-.3).get()||oy(t.color||this.color).brighten(-.3).get();c.fill=g,u.partRect[d](c,p).shadow(e.shadow)}},i.prototype.drawPoints=function(){for(var t=this.getAnimationVerb(),i=0,e=this.points;i<e.length;i++){var o=e[i];this.drawPoint(o,t)}},i.prototype.getAnimationVerb=function(){return this.chart.pointCount<(this.options.animationLimit||250)?"animate":"attr"},i.prototype.isPointInside=function(i){var e=i.shapeArgs,o=i.plotX,r=i.plotY;return e?void 0!==o&&void 0!==r&&r>=0&&r<=this.yAxis.len&&(e.x||0)+(e.width||0)>=0&&o<=this.xAxis.len:t.prototype.isPointInside.apply(this,arguments)},i.defaultOptions=oB(ob.defaultOptions,oc),i}(ob);oO(oD.prototype,{pointClass:og,pointArrayMap:["x2","y"],getExtremesFromAll:!0,keysAffectYAxis:["y"],parallelArrays:["x","x2","y"],requireSorting:!1,type:"xrange",animate:tN().series.prototype.animate,autoIncrement:om,buildKDTree:om}),tN().registerSeriesType("xrange",oD);var oG=R();oD.compose(oG.Axis);var oL=(v=function(t,i){return(v=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw TypeError("Class extends value "+String(i)+" is not a constructor or null");function e(){this.constructor=t}v(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),oz=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return oL(i,t),i.setGanttPointAliases=function(t,i){var e,o,r;t.x=t.start=i.time.parse(null!==(e=t.start)&&void 0!==e?e:t.x),t.x2=t.end=i.time.parse(null!==(o=t.end)&&void 0!==o?o:t.x2),t.partialFill=t.completed=null!==(r=t.completed)&&void 0!==r?r:t.partialFill},i.prototype.applyOptions=function(e,o){var r,n=t.prototype.applyOptions.call(this,e,o);return i.setGanttPointAliases(n,n.series.chart),this.isNull=!(null===(r=this.isValid)||void 0===r?void 0:r.call(this)),n},i.prototype.isValid=function(){return("number"==typeof this.start||"number"==typeof this.x)&&("number"==typeof this.end||"number"==typeof this.x2||this.milestone)},i}(tN().seriesTypes.xrange.prototype.pointClass),oN=R().isNumber,oW={grouping:!1,dataLabels:{enabled:!0},tooltip:{headerFormat:'<span style="font-size: 0.8em">{series.name}</span><br/>',pointFormat:null,pointFormatter:function(){var t=this.series,i=t.xAxis,e=t.tooltipOptions.dateTimeLabelFormats,o=i.options.startOfWeek,r=t.tooltipOptions,n=this.options.milestone,s=r.xDateFormat,a="<b>"+(this.name||this.yCategory)+"</b>";if(r.pointFormat)return this.tooltipFormatter(r.pointFormat);!s&&oN(this.start)&&(s=t.chart.time.getDateFormat(i.closestPointRange,this.start,o,e||{}));var h=t.chart.time.dateFormat(s,this.start),l=t.chart.time.dateFormat(s,this.end);return a+="<br/>",n?a+=h+"<br/>":a+="Start: "+h+"<br/>"+("End: "+l)+"<br/>",a}},connectors:{type:"simpleConnect",animation:{reversed:!0},radius:0,startMarker:{enabled:!0,symbol:"arrow-filled",radius:4,fill:"#fa0",align:"left"},endMarker:{enabled:!1,align:"right"}}},oH=T(184),oF=T.n(oH),o_=R().addEvent,oU=R().find,oX=R().fireEvent,oY=R().isArray,oj=R().isNumber,oV=R().pick;!function(t){function i(){void 0!==this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)}function e(){var t;(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(this.options.ordinal=!1)}function o(){var t=this.brokenAxis;if(null==t?void 0:t.hasBreaks){for(var i=this.tickPositions,e=this.tickPositions.info,o=[],r=0;r<i.length;r++)t.isInAnyBreak(i[r])||o.push(i[r]);this.tickPositions=o,this.tickPositions.info=e}}function r(){this.brokenAxis||(this.brokenAxis=new l(this))}function n(){var t,i,e=this.isDirty,o=this.options.connectNulls,r=this.points,n=this.xAxis,s=this.yAxis;if(e)for(var a=r.length;a--;){var h=r[a],l=(null!==h.y||!1!==o)&&((null===(t=null==n?void 0:n.brokenAxis)||void 0===t?void 0:t.isInAnyBreak(h.x,!0))||(null===(i=null==s?void 0:s.brokenAxis)||void 0===i?void 0:i.isInAnyBreak(h.y,!0)));h.visible=!l&&!1!==h.options.visible}}function s(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,oV(this.pointArrayMap,["y"]))}function a(t,i){var e,o,r,n,s=this,a=s.points;if(null===(e=null==t?void 0:t.brokenAxis)||void 0===e?void 0:e.hasBreaks){var h=t.brokenAxis;i.forEach(function(i){o=(null==h?void 0:h.breakArray)||[],r=t.isXAxis?t.min:oV(s.options.threshold,t.min);var e,l,d=null===(l=null===(e=null==t?void 0:t.options)||void 0===e?void 0:e.breaks)||void 0===l?void 0:l.filter(function(t){for(var i=!0,e=0;e<o.length;e++){var r=o[e];if(r.from===t.from&&r.to===t.to){i=!1;break}}return i});a.forEach(function(e){n=oV(e["stack"+i.toUpperCase()],e[i]),o.forEach(function(i){if(oj(r)&&oj(n)){var o="";r<i.from&&n>i.to||r>i.from&&n<i.from?o="pointBreak":(r<i.from&&n>i.from&&n<i.to||r>i.from&&n>i.to&&n<i.from)&&(o="pointInBreak"),o&&oX(t,o,{point:e,brk:i})}}),null==d||d.forEach(function(i){oX(t,"pointOutsideOfBreak",{point:e,brk:i})})})})}}function h(){var t=this.currentDataGrouping,i=null==t?void 0:t.gapSize,e=this.points.slice(),o=this.yAxis,r=this.options.gapSize,n=e.length-1;if(r&&n>0){"value"!==this.options.gapUnit&&(r*=this.basePointRange),i&&i>r&&i>=this.basePointRange&&(r=i);for(var s=void 0,a=void 0;n--;)if(a&&!1!==a.visible||(a=e[n+1]),s=e[n],!1!==a.visible&&!1!==s.visible){if(a.x-s.x>r){var h=(s.x+a.x)/2;e.splice(n+1,0,{isNull:!0,x:h}),o.stacking&&this.options.stacking&&((o.stacking.stacks[this.stackKey][h]=new(oF())(o,o.options.stackLabels,!1,h,this.stack)).total=0)}a=s}}return this.getGraphPath(e)}t.compose=function(t,l){if(!t.keepProps.includes("brokenAxis")){t.keepProps.push("brokenAxis"),o_(t,"init",r),o_(t,"afterInit",i),o_(t,"afterSetTickPositions",o),o_(t,"afterSetOptions",e);var d=l.prototype;d.drawBreaks=a,d.gappedPath=h,o_(l,"afterGeneratePoints",n),o_(l,"afterRender",s)}return t};var l=function(){function t(t){this.hasBreaks=!1,this.axis=t}return t.isInBreak=function(t,i){var e,o=t.repeat||1/0,r=t.from,n=t.to-t.from,s=i>=r?(i-r)%o:o-(r-i)%o;return t.inclusive?s<=n:s<n&&0!==s},t.lin2Val=function(i){var e=this.brokenAxis,o=null==e?void 0:e.breakArray;if(!o||!oj(i))return i;var r,n,s=i;for(n=0;n<o.length&&!((r=o[n]).from>=s);n++)r.to<s?s+=r.len:t.isInBreak(r,s)&&(s+=r.len);return s},t.val2Lin=function(i){var e=this.brokenAxis,o=null==e?void 0:e.breakArray;if(!o||!oj(i))return i;var r,n,s=i;for(n=0;n<o.length;n++)if((r=o[n]).to<=i)s-=r.len;else if(r.from>=i)break;else if(t.isInBreak(r,i)){s-=i-r.from;break}return s},t.prototype.findBreakAt=function(t,i){return oU(i,function(i){return i.from<t&&t<i.to})},t.prototype.isInAnyBreak=function(i,e){var o,r,n,s=this.axis,a=s.options.breaks||[],h=a.length;if(h&&oj(i)){for(;h--;)t.isInBreak(a[h],i)&&(o=!0,r||(r=oV(a[h].showPoints,!s.isXAxis)));n=o&&e?o&&!r:o}return n},t.prototype.setBreaks=function(i,e){var o=this,r=o.axis,n=r.chart.time,s=oY(i)&&!!i.length&&!!Object.keys(i[0]).length;r.isDirty=o.hasBreaks!==s,o.hasBreaks=s,null==i||i.forEach(function(t){t.from=n.parse(t.from)||0,t.to=n.parse(t.to)||0}),i!==r.options.breaks&&(r.options.breaks=r.userOptions.breaks=i),r.forceRedraw=!0,r.series.forEach(function(t){t.isDirty=!0}),s||r.val2lin!==t.val2Lin||(delete r.val2lin,delete r.lin2val),s&&(r.userOptions.ordinal=!1,r.lin2val=t.lin2Val,r.val2lin=t.val2Lin,r.setExtremes=function(t,i,e,n,s){if(o.hasBreaks){for(var a=this.options.breaks||[],h=void 0;h=o.findBreakAt(t,a);)t=h.to;for(;h=o.findBreakAt(i,a);)i=h.from;i<t&&(i=t)}r.constructor.prototype.setExtremes.call(this,t,i,e,n,s)},r.setAxisTranslation=function(){if(r.constructor.prototype.setAxisTranslation.call(this),o.unitLength=void 0,o.hasBreaks){var i,e,n,s,a=r.options.breaks||[],h=[],l=[],d=oV(r.pointRangePadding,0),c=0,p=r.userMin||r.min,u=r.userMax||r.max;a.forEach(function(i){e=i.repeat||1/0,oj(p)&&oj(u)&&(t.isInBreak(i,p)&&(p+=i.to%e-p%e),t.isInBreak(i,u)&&(u-=u%e-i.from%e))}),a.forEach(function(t){if(n=t.from,e=t.repeat||1/0,oj(p)&&oj(u)){for(;n-e>p;)n-=e;for(;n<p;)n+=e;for(s=n;s<u;s+=e)h.push({value:s,move:"in"}),h.push({value:s+t.to-t.from,move:"out",size:t.breakSize})}}),h.sort(function(t,i){return t.value===i.value?+("in"!==t.move)-+("in"!==i.move):t.value-i.value}),i=0,n=p,h.forEach(function(t){1===(i+="in"===t.move?1:-1)&&"in"===t.move&&(n=t.value),0===i&&oj(n)&&(l.push({from:n,to:t.value,len:t.value-n-(t.size||0)}),c+=t.value-n-(t.size||0))}),o.breakArray=l,oj(p)&&oj(u)&&oj(r.min)&&(o.unitLength=u-p-c+d,oX(r,"afterBreaks"),r.staticScale?r.transA=r.staticScale:o.unitLength&&(r.transA*=(u-r.min+d)/o.unitLength),d&&(r.minPixelPadding=r.transA*(r.minPointOffset||0)),r.min=p,r.max=u)}}),oV(e,!0)&&r.chart.redraw()},t}();t.Additions=l}(O||(O={}));var oq=O,oZ=R().dateFormats,oK=R().addEvent,o$=R().defined,oJ=R().erase,oQ=R().find,o0=R().isArray,o1=R().isNumber,o2=R().merge,o3=R().pick,o5=R().timeUnits,o6=R().wrap;function o4(t){return R().isObject(t,!0)}function o8(t,i){var e={width:0,height:0};if(i.forEach(function(i){var o,r=t[i],n=0,s=0;o4(r)&&(n=(o=o4(r.label)?r.label:{}).getBBox?o.getBBox().height:0,o.textStr&&!o1(o.textPxLength)&&(o.textPxLength=o.getBBox().width),s=o1(o.textPxLength)?Math.round(o.textPxLength):0,o.textStr&&(s=Math.round(o.getBBox().width)),e.height=Math.max(n,e.height),e.width=Math.max(s,e.width))}),"treegrid"===this.type&&this.treeGrid&&this.treeGrid.mapOfPosToGridNode){var o=this.treeGrid.mapOfPosToGridNode[-1].height||0;e.width+=this.options.labels.indentation*(o-1)}return e}function o9(t){var i=this.grid,e=3===this.side;if(e||t.apply(this),!(null==i?void 0:i.isColumn)){var o=(null==i?void 0:i.columns)||[];e&&(o=o.slice().reverse()),o.forEach(function(t){t.getOffset()})}e&&t.apply(this)}function o7(t){if(!0===(this.options.grid||{}).enabled){var i=this.axisTitle,e=this.height,o=this.horiz,r=this.left,n=this.offset,s=this.opposite,a=this.options,h=this.top,l=this.width,d=this.tickSize(),c=null==i?void 0:i.getBBox().width,p=a.title.x,u=a.title.y,f=o3(a.title.margin,o?5:10),g=i?this.chart.renderer.fontMetrics(i).f:0,v=(o?h+e:r)+(o?1:-1)*(s?-1:1)*(d?d[0]/2:0)+(this.side===P.bottom?g:0);t.titlePosition.x=o?r-(c||0)/2-f+p:v+(s?l:0)+n+p,t.titlePosition.y=o?v-(s?e:0)+(s?g:-g)/2+n+u:h-f+u}}function rt(){var t,i=this.chart,e=this.options.grid,o=void 0===e?{}:e,r=this.userOptions;if(o.enabled&&((t=this.options).labels.align=o3(t.labels.align,"center"),this.categories||(t.showLastLabel=!1),this.labelRotation=0,t.labels.rotation=0,t.minTickInterval=1),o.columns)for(var n=this.grid.columns=[],s=this.grid.columnIndex=0;++s<o.columns.length;){var a=o2(r,o.columns[s],{isInternal:!0,linkedTo:0,scrollbar:{enabled:!1}},{grid:{columns:void 0}}),h=new(tp())(this.chart,a,"yAxis");h.grid.isColumn=!0,h.grid.columnIndex=s,oJ(i.axes,h),oJ(i[this.coll]||[],h),n.push(h)}}function ri(){var t,i,e=this.axisTitle,o=this.grid,r=this.options;if(!0===(r.grid||{}).enabled){var n=this.min||0,s=this.max||0,a=this.ticks[this.tickPositions[0]];if(e&&!this.chart.styledMode&&(null==a?void 0:a.slotWidth)&&!this.options.title.style.width&&e.css({width:""+a.slotWidth+"px"}),this.maxLabelDimensions=this.getMaxLabelDimensions(this.ticks,this.tickPositions),this.rightWall&&this.rightWall.destroy(),(null===(t=this.grid)||void 0===t?void 0:t.isOuterAxis())&&this.axisLine){var h=r.lineWidth;if(h){var l=this.getLinePath(h),d=l[0],c=l[1],p=(this.tickSize("tick")||[1])[0]*(this.side===P.top||this.side===P.left?-1:1);if("M"===d[0]&&"L"===c[0]&&(this.horiz?(d[2]+=p,c[2]+=p):(d[1]+=p,c[1]+=p)),!this.horiz&&this.chart.marginRight){var u=["L",this.left,d[2]||0],f=[d,u],g=["L",this.chart.chartWidth-this.chart.marginRight,this.toPixels(s+this.tickmarkOffset)],v=[["M",c[1]||0,this.toPixels(s+this.tickmarkOffset)],g];this.grid.upperBorder||n%1==0||(this.grid.upperBorder=this.grid.renderBorder(f)),this.grid.upperBorder&&(this.grid.upperBorder.attr({stroke:r.lineColor,"stroke-width":r.lineWidth}),this.grid.upperBorder.animate({d:f})),this.grid.lowerBorder||s%1==0||(this.grid.lowerBorder=this.grid.renderBorder(v)),this.grid.lowerBorder&&(this.grid.lowerBorder.attr({stroke:r.lineColor,"stroke-width":r.lineWidth}),this.grid.lowerBorder.animate({d:v}))}this.grid.axisLineExtra?(this.grid.axisLineExtra.attr({stroke:r.lineColor,"stroke-width":r.lineWidth}),this.grid.axisLineExtra.animate({d:l})):this.grid.axisLineExtra=this.grid.renderBorder(l),this.axisLine[this.showAxis?"show":"hide"]()}}if(((null==o?void 0:o.columns)||[]).forEach(function(t){return t.render()}),!this.horiz&&this.chart.hasRendered&&(this.scrollbar||(null===(i=this.linkedParent)||void 0===i?void 0:i.scrollbar))&&this.tickPositions.length){for(var x=this.tickmarkOffset,m=this.tickPositions[this.tickPositions.length-1],y=this.tickPositions[0],b=void 0,M=void 0;(b=this.hiddenLabels.pop())&&b.element;)b.show();for(;(M=this.hiddenMarks.pop())&&M.element;)M.show();(b=this.ticks[y].label)&&(n-y>x?this.hiddenLabels.push(b.hide()):b.show()),(b=this.ticks[m].label)&&(m-s>x?this.hiddenLabels.push(b.hide()):b.show());var k=this.ticks[m].mark;k&&m-s<x&&m-s>0&&this.ticks[m].isLast&&this.hiddenMarks.push(k.hide())}}}function re(){var t,i=null===(t=this.tickPositions)||void 0===t?void 0:t.info,e=this.options,o=e.grid||{},r=this.userOptions.labels||{};o.enabled&&(this.horiz?(this.series.forEach(function(t){t.options.pointRange=0}),i&&e.dateTimeLabelFormats&&e.labels&&!o$(r.align)&&(!1===e.dateTimeLabelFormats[i.unitName].range||i.count>1)&&(e.labels.align="left",o$(r.x)||(e.labels.x=3))):"treegrid"!==this.type&&this.grid&&this.grid.columns&&(this.minPointOffset=this.tickInterval))}function ro(t){var i,e=this.options,o=t.userOptions,r=e&&o4(e.grid)?e.grid:{};!0===r.enabled&&(i=o2(!0,{className:"highcharts-grid-axis "+(o.className||""),dateTimeLabelFormats:{hour:{list:["%[HM]","%[H]"]},day:{list:["%[AeB]","%[aeb]","%[E]"]},week:{list:["Week %W","W%W"]},month:{list:["%[B]","%[b]","%o"]}},grid:{borderWidth:1},labels:{padding:2,style:{fontSize:"0.9em"}},margin:0,title:{text:null,reserveSpace:!1,rotation:0,style:{textOverflow:"ellipsis"}},units:[["millisecond",[1,10,100]],["second",[1,10]],["minute",[1,5,15]],["hour",[1,6]],["day",[1]],["week",[1]],["month",[1]],["year",null]]},o),"xAxis"!==this.coll||(o$(o.linkedTo)&&!o$(o.tickPixelInterval)&&(i.tickPixelInterval=350),!(!o$(o.tickPixelInterval)&&o$(o.linkedTo))||o$(o.tickPositioner)||o$(o.tickInterval)||o$(o.units)||(i.tickPositioner=function(t,e){var o,r,n=null===(r=null===(o=this.linkedParent)||void 0===o?void 0:o.tickPositions)||void 0===r?void 0:r.info;if(n){for(var s=i.units||[],a=void 0,h=1,l="year",d=0;d<s.length;d++){var c=s[d];if(c&&c[0]===n.unitName){a=d;break}}var p=o1(a)&&s[a+1];if(p){l=p[0]||"year";var u=p[1];h=(null==u?void 0:u[0])||1}else"year"===n.unitName&&(h=10*n.count);var f=o5[l];return this.tickInterval=f*h,this.chart.time.getTimeTicks({unitRange:f,count:h,unitName:l},t,e,this.options.startOfWeek)}})),o2(!0,this.options,i),this.horiz&&(e.minPadding=o3(o.minPadding,0),e.maxPadding=o3(o.maxPadding,0)),o1(e.grid.borderWidth)&&(e.tickWidth=e.lineWidth=r.borderWidth))}function rr(t){var i=t.userOptions,e=(null==i?void 0:i.grid)||{},o=e.columns;e.enabled&&o&&o2(!0,this.options,o[0])}function rn(){(this.grid.columns||[]).forEach(function(t){return t.setScale()})}function rs(t){var i=this.horiz,e=this.maxLabelDimensions,o=this.options.grid,r=void 0===o?{}:o;if(r.enabled&&e){var n=2*this.options.labels.distance,s=i?r.cellHeight||n+e.height:n+e.width;o0(t.tickSize)?t.tickSize[0]=s:t.tickSize=[s,0]}}function ra(){this.axes.forEach(function(t){var i;((null===(i=t.grid)||void 0===i?void 0:i.columns)||[]).forEach(function(t){t.setAxisSize(),t.setAxisTranslation()})})}function rh(t){var i=this.grid;(i.columns||[]).forEach(function(i){return i.destroy(t.keepEvents)}),i.columns=void 0}function rl(t){var i=t.userOptions||{},e=i.grid||{};e.enabled&&o$(e.borderColor)&&(i.tickColor=i.lineColor=e.borderColor),this.grid||(this.grid=new rf(this)),this.hiddenLabels=[],this.hiddenMarks=[]}function rd(t){var i=this.label,e=this.axis,o=e.reversed,r=e.chart,n=e.options.grid||{},s=e.options.labels,a=s.align,h=P[e.side],l=t.tickmarkOffset,d=e.tickPositions,c=this.pos-l,p=o1(d[t.index+1])?d[t.index+1]-l:(e.max||0)+l,u=e.tickSize("tick"),f=u?u[0]:0,g=u?u[1]/2:0;if(!0===n.enabled){var v,x=void 0,m=void 0,y=void 0;if("top"===h?v=(x=e.top+e.offset)-f:"bottom"===h?x=(v=r.chartHeight-e.bottom+e.offset)+f:(x=e.top+e.len-(e.translate(o?p:c)||0),v=e.top+e.len-(e.translate(o?c:p)||0)),"right"===h?y=(m=r.chartWidth-e.right+e.offset)+f:"left"===h?m=(y=e.left+e.offset)-f:(m=Math.round(e.left+(e.translate(o?p:c)||0))-g,y=Math.min(Math.round(e.left+(e.translate(o?c:p)||0))-g,e.left+e.len)),this.slotWidth=y-m,t.pos.x="left"===a?m:"right"===a?y:m+(y-m)/2,t.pos.y=v+(x-v)/2,i){var b=r.renderer.fontMetrics(i),M=i.getBBox().height;if(s.useHTML)t.pos.y+=b.b+-(M/2);else{var k=Math.round(M/b.h);t.pos.y+=(b.b-(b.h-b.f))/2+-((k-1)*b.h/2)}}t.pos.x+=e.horiz&&s.x||0}}function rc(t){var i,e=t.axis,o=t.value;if(null===(i=e.options.grid)||void 0===i?void 0:i.enabled){var r=e.tickPositions,n=(e.linkedParent||e).series[0],s=o===r[0],a=o===r[r.length-1],h=n&&oQ(n.options.data,function(t){return t[e.isXAxis?"x":"y"]===o}),l=void 0;h&&n.is("gantt")&&(l=o2(h),R().seriesTypes.gantt.prototype.pointClass.setGanttPointAliases(l,e.chart)),t.isFirst=s,t.isLast=a,t.point=l}}function rp(){var t,i,e=this.options,o=e.grid||{},r=this.categories,n=this.tickPositions,s=n[0],a=n[1],h=n[n.length-1],l=n[n.length-2],d=null===(t=this.linkedParent)||void 0===t?void 0:t.min,c=null===(i=this.linkedParent)||void 0===i?void 0:i.max,p=d||this.min,u=c||this.max,f=this.tickInterval,g=o1(p)&&p>=s+f&&p<a,v=o1(p)&&s<p&&s+f>p,x=o1(u)&&h>u&&h-f<u,m=o1(u)&&u<=h-f&&u>l;!0===o.enabled&&!r&&(this.isXAxis||this.isLinked)&&((v||g)&&!e.startOnTick&&(n[0]=p),(x||m)&&!e.endOnTick&&(n[n.length-1]=u))}function ru(t){var i,e=this.options.grid;return!0===(void 0===e?{}:e).enabled&&this.categories?this.tickInterval:t.apply(this,(i=arguments,Array.prototype.slice.call(i,1)))}(x=P||(P={}))[x.top=0]="top",x[x.right=1]="right",x[x.bottom=2]="bottom",x[x.left=3]="left";var rf=function(){function t(t){this.axis=t}return t.prototype.isOuterAxis=function(){var t,i=this.axis,e=i.chart,o=i.grid.columnIndex,r=(null===(t=i.linkedParent)||void 0===t?void 0:t.grid.columns)||i.grid.columns||[],n=o?i.linkedParent:i,s=-1,a=0;return 3===i.side&&!e.inverted&&r.length?!i.linkedParent:((e[i.coll]||[]).forEach(function(t,e){t.side!==i.side||t.options.isInternal||(a=e,t!==n||(s=e))}),a===s&&(!o1(o)||r.length===o))},t.prototype.renderBorder=function(t){var i=this.axis,e=i.chart.renderer,o=i.options,r=e.path(t).addClass("highcharts-axis-line").add(i.axisGroup);return e.styledMode||r.attr({stroke:o.lineColor,"stroke-width":o.lineWidth,zIndex:7}),r},t}();oZ.E=function(t){return this.dateFormat("%a",t,!0).charAt(0)},oZ.W=function(t){var i=this.toParts(t),e=(i[7]+6)%7,o=i.slice(0);o[2]=i[2]-e+3;var r=this.toParts(this.makeTime(o[0],0,1));return 4!==r[7]&&(i[1]=0,i[2]=1+(11-r[7])%7),(1+Math.floor((this.makeTime(o[0],o[1],o[2])-this.makeTime(r[0],r[1],r[2]))/6048e5)).toString()};var rg=function(t,i){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>i.indexOf(o)&&(e[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)0>i.indexOf(o[r])&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(e[o[r]]=t[o[r]]);return e},rv=R().extend,rx=R().isNumber,rm=R().pick;function ry(t,i,e,o,r,n){var s,a,h=n&&n.after,l=n&&n.before,d={data:o,depth:e-1,id:t,level:e,parent:i||""},c=0,p=0;"function"==typeof l&&l(d,n);var u=(r[t]||[]).map(function(i){var o=ry(i.id,t,e+1,i,r,n),h=i.start||NaN,l=!0===i.milestone?h:i.end||NaN;return s=!rx(s)||h<s?h:s,a=!rx(a)||l>a?l:a,c=c+1+o.descendants,p=Math.max(o.height+1,p),o});return o&&(o.start=rm(o.start,s),o.end=rm(o.end,a)),rv(d,{children:u,descendants:c,height:p}),"function"==typeof h&&h(d,n),d}var rb={getNode:ry,getTree:function(t,i){var e,o;return ry("",null,1,null,(e=[],Object.keys(o=t.reduce(function(t,i){var o=i.parent,r=void 0===o?"":o,n=i.id;return void 0===t[r]&&(t[r]=[]),t[r].push(i),n&&e.push(n),t},{})).forEach(function(t){var i;if(""!==t&&-1===e.indexOf(t)){var r=o[t].map(function(t){return rg(t,[])});(i=o[""]).push.apply(i,r),delete o[t]}}),o),i)}},rM=R().addEvent,rk=R().removeEvent,rA=R().isObject,rw=R().isNumber,rO=R().pick,rP=R().wrap;function rS(){this.treeGrid||(this.treeGrid=new rT(this))}function rE(t,i,e,o,r,n,s,a,h){var l,d,c,p,u=rO(null===(l=this.options)||void 0===l?void 0:l.labels,n),f=this.pos,g=this.axis,v="treegrid"===g.type,x=t.apply(this,[i,e,o,r,u,s,a,h]);if(v){var m=u&&rA(u.symbol,!0)?u.symbol:{},y=m.width,b=m.padding,M=void 0===b?5*!g.linkedParent:b,k=u&&rw(u.indentation)?u.indentation:0;p=(null==(c=null==(d=g.treeGrid.mapOfPosToGridNode)?void 0:d[f])?void 0:c.depth)||1,x.x+=(void 0===y?0:y)+2*M+(p-1)*k}return x}function rB(t){var i,e,o,r,n,s,a,h,l,d,c,p,u,f,g,v=this.pos,x=this.axis,m=this.label,y=this.treeGrid,b=this.options,M=null==y?void 0:y.labelIcon,k=null==m?void 0:m.element,A=x.treeGrid,w=x.options,O=x.chart,P=x.tickPositions,S=A.mapOfPosToGridNode,E=rO(null==b?void 0:b.labels,null==w?void 0:w.labels),B=E&&rA(E.symbol,!0)?E.symbol:{},T=null==S?void 0:S[v],C=T||{},I=C.descendants,R=C.depth,D=T&&I&&I>0,G="treegrid"===x.type&&k,L=P.indexOf(v)>-1,z="highcharts-treegrid-node-",N=z+"level-",W=O.styledMode;(G&&T&&m.removeClass(RegExp(N+".*")).addClass(N+R),t.apply(this,Array.prototype.slice.call(arguments,1)),G&&D)?(g=A.isCollapsed(T),i={color:!W&&m.styles.color||"",collapsed:g,group:m.parentGroup,options:B,renderer:m.renderer,show:L,xy:m.xy},r=!(o=this.treeGrid).labelIcon,n=i.renderer,s=i.xy,h=(a=i.options).width||0,l=a.height||0,d=5*(null!==(e=a.padding)&&void 0!==e?!e:!this.axis.linkedParent),c={x:s.x-h/2-d,y:s.y-l/2},p=i.collapsed?90:180,u=i.show&&rw(c.y),(f=o.labelIcon)||(o.labelIcon=f=n.path(n.symbols[a.type](a.x||0,a.y||0,h,l)).addClass("highcharts-label-icon").add(i.group)),f[u?"show":"hide"](),n.styledMode||f.attr({cursor:"pointer",fill:rO(i.color,"#666666"),"stroke-width":1,stroke:a.lineColor,strokeWidth:a.lineWidth||0}),f[r?"attr":"animate"]({translateX:c.x,translateY:c.y,rotation:p}),m.addClass(z+(g?"collapsed":"expanded")).removeClass(z+(g?"expanded":"collapsed")),W||m.css({cursor:"pointer"}),[m,M].forEach(function(t){t&&!t.attachedTreeGridEvents&&(rM(t.element,"mouseover",function(){m.addClass("highcharts-treegrid-node-active"),m.renderer.styledMode||m.css({textDecoration:"underline"})}),rM(t.element,"mouseout",function(){var t;t=rA(E.style)?E.style:{},m.removeClass("highcharts-treegrid-node-active"),m.renderer.styledMode||m.css({textDecoration:t.textDecoration||"none"})}),rM(t.element,"click",function(){y.toggleCollapse()}),t.attachedTreeGridEvents=!0)})):M&&(rk(k),null==m||m.css({cursor:"default"}),M.destroy())}var rT=function(){function t(t){this.tick=t}return t.compose=function(t){var i=t.prototype;i.toggleCollapse||(rM(t,"init",rS),rP(i,"getLabelPosition",rE),rP(i,"renderLabel",rB),i.collapse=function(t){this.treeGrid.collapse(t)},i.expand=function(t){this.treeGrid.expand(t)},i.toggleCollapse=function(t){this.treeGrid.toggleCollapse(t)})},t.prototype.collapse=function(t){var i=this.tick,e=i.axis,o=e.brokenAxis;if(o&&e.treeGrid.mapOfPosToGridNode){var r=i.pos,n=e.treeGrid.mapOfPosToGridNode[r],s=e.treeGrid.collapse(n);o.setBreaks(s,rO(t,!0))}},t.prototype.destroy=function(){this.labelIcon&&this.labelIcon.destroy()},t.prototype.expand=function(t){var i=this.tick,e=i.pos,o=i.axis,r=o.treeGrid,n=o.brokenAxis,s=r.mapOfPosToGridNode;if(n&&s){var a=s[e],h=r.expand(a);n.setBreaks(h,rO(t,!0))}},t.prototype.toggleCollapse=function(t){var i=this.tick,e=i.axis,o=e.brokenAxis;if(o&&e.treeGrid.mapOfPosToGridNode){var r=i.pos,n=e.treeGrid.mapOfPosToGridNode[r],s=e.treeGrid.toggleCollapse(n);o.setBreaks(s,rO(t,!0))}},t}(),rC=(R().extend,R().isArray),rI=R().isNumber,rR=R().isObject,rD=R().merge,rG=R().pick,rL=(R().relativeLength,function(t){var i,e,o,r,n,s,a={};if(rR(t))for(r=rI(t.from)?t.from:1,s=t.levels,e={},i=rR(t.defaults)?t.defaults:{},rC(s)&&(e=s.reduce(function(t,e){var o,n,s;return rR(e)&&rI(e.level)&&(n=rG((s=rD({},e)).levelIsConstant,i.levelIsConstant),delete s.levelIsConstant,delete s.level,rR(t[o=e.level+(n?0:r-1)])?rD(!0,t[o],s):t[o]=s),t},{})),n=rI(t.to)?t.to:1,o=0;o<=n;o++)a[o]=rD({},i,rR(e[o])?e[o]:{});return a}),rz=R().addEvent,rN=R().isArray,rW=R().splat,rH=R().find,rF=R().fireEvent,r_=R().isObject,rU=R().isString,rX=R().merge,rY=R().pick,rj=R().removeEvent,rV=R().wrap;function rq(t,i){var e=t.collapseEnd||0,o=t.collapseStart||0;return e>=i&&(o-=.5),{from:o,to:e,showPoints:!1}}function rZ(t,i,e){var o,r,n=[],s=[],a={},h=i||!1,l={},d=-1,c=rb.getTree(t,{after:function(t){var i=l[t.pos],e=0,o=0;i.children.forEach(function(t){o+=(t.descendants||0)+1,e=Math.max((t.height||0)+1,e)}),i.descendants=o,i.height=e,i.collapsed&&s.push(i)},before:function(t){var i,e,o=r_(t.data,!0)?t.data:{},r=rU(o.name)?o.name:"",s=a[t.parent],c=r_(s,!0)?l[s.pos]:null;h&&r_(c,!0)&&(i=rH(c.children,function(t){return t.name===r}))?(e=i.pos,i.nodes.push(t)):e=d++,!l[e]&&(l[e]=i={depth:c?c.depth+1:0,name:r,id:o.id,nodes:[t],children:[],pos:e},-1!==e&&n.push(r),r_(c,!0)&&c.children.push(i)),rU(t.id)&&(a[t.id]=t),i&&!0===o.collapsed&&(i.collapsed=!0),t.pos=e}});return o=l,{categories:n,mapOfIdToNode:a,mapOfPosToGridNode:l=(r=function(t,i,o){var n=t.nodes,s=i+(-1===i?0:e-1),a=(s-i)/2,h=i+a;return n.forEach(function(t){var e=t.data;r_(e,!0)&&(e.y=i+(e.seriesIndex||0),delete e.seriesIndex),t.pos=h}),o[h]=t,t.pos=h,t.tickmarkOffset=a+.5,t.collapseStart=s+.5,t.children.forEach(function(t){r(t,s+1,o),s=(t.collapseEnd||0)-.5}),t.collapseEnd=s+.5,o})(o["-1"],-1,{}),collapsedNodes:s,tree:c}}function rK(t){var i=t.target;i.axes.filter(function(t){return"treegrid"===t.type}).forEach(function(e){var o,r,n,s=e.options||{},a=s.labels,h=e.uniqueNames,l=i.time.parse(s.max),d=!e.treeGrid.mapOfPosToGridNode||e.series.some(function(t){return!t.hasRendered||t.isDirtyData||t.isDirty}),c=0;if(d){var p=[];if(r=e.series.reduce(function(t,e){var o=e.options.data||[],r=o[0],n=Array.isArray(r)&&!r.find(function(t){return"object"==typeof t});return p.push(n),e.visible&&(o.forEach(function(o){var r;(n||(null===(r=e.options.keys)||void 0===r?void 0:r.length))&&(o=e.pointClass.prototype.optionsToObject.call({series:e},o),e.pointClass.setGanttPointAliases(o,i)),r_(o,!0)&&(o.seriesIndex=c,t.push(o))}),!0===h&&c++),t},[]),l&&r.length<l)for(var u=r.length;u<=l;u++)r.push({name:u+"​"});e.categories=(n=rZ(r,h||!1,!0===h?c:1)).categories,e.treeGrid.mapOfPosToGridNode=n.mapOfPosToGridNode,e.hasNames=!0,e.treeGrid.tree=n.tree,e.series.forEach(function(t,i){var e=(t.options.data||[]).map(function(e){return(p[i]||rN(e)&&t.options.keys&&t.options.keys.length)&&r.forEach(function(t){var i=rW(e);i.indexOf(t.x||0)>=0&&i.indexOf(t.x2||0)>=0&&(e=t)}),r_(e,!0)?rX(e):e});t.visible&&t.setData(e,!1)}),e.treeGrid.mapOptionsToLevel=rL({defaults:a,from:1,levels:null==a?void 0:a.levels,to:null===(o=e.treeGrid.tree)||void 0===o?void 0:o.height}),"beforeRender"===t.type&&(e.treeGrid.collapsedNodes=n.collapsedNodes)}})}function r$(t,i){var e,o,r,n=this.treeGrid.mapOptionsToLevel||{},s="treegrid"===this.type,a=this.ticks,h=a[i];s&&this.treeGrid.mapOfPosToGridNode?((e=n[(r=this.treeGrid.mapOfPosToGridNode[i]).depth])&&(o={labels:e}),!h&&S?a[i]=h=new S(this,i,void 0,void 0,{category:r.name,tickmarkOffset:r.tickmarkOffset,options:o}):(h.parameters.category=r.name,h.options=o,h.addLabel())):t.apply(this,Array.prototype.slice.call(arguments,1))}function rJ(t,i,e,o){var r=this,n="treegrid"===e.type;r.treeGrid||(r.treeGrid=new r1(r)),n&&(rz(i,"beforeRender",rK),rz(i,"beforeRedraw",rK),rz(i,"addSeries",function(t){if(t.options.data){var i=rZ(t.options.data,e.uniqueNames||!1,1);r.treeGrid.collapsedNodes=(r.treeGrid.collapsedNodes||[]).concat(i.collapsedNodes)}}),rz(r,"foundExtremes",function(){r.treeGrid.collapsedNodes&&r.treeGrid.collapsedNodes.forEach(function(t){var i=r.treeGrid.collapse(t);r.brokenAxis&&(r.brokenAxis.setBreaks(i,!1),r.treeGrid.collapsedNodes&&(r.treeGrid.collapsedNodes=r.treeGrid.collapsedNodes.filter(function(i){return t.collapseStart!==i.collapseStart||t.collapseEnd!==i.collapseEnd})))})}),rz(r,"afterBreaks",function(){"yAxis"===r.coll&&!r.staticScale&&r.chart.options.chart.height&&(r.isDirty=!0)}),e=rX({grid:{enabled:!0},labels:{align:"left",levels:[{level:void 0},{level:1,style:{fontWeight:"bold"}}],symbol:{type:"triangle",x:-5,y:-5,height:10,width:10}},uniqueNames:!1},e,{reversed:!0})),t.apply(r,[i,e,o]),n&&(r.hasNames=!0,r.options.showLastLabel=!0)}function rQ(t){var i,e,o,r,n,s=this.options,a=this.chart.time,h="number"==typeof s.linkedTo?null===(i=this.chart[this.coll])||void 0===i?void 0:i[s.linkedTo]:void 0;if("treegrid"===this.type){if(this.min=null!==(o=null!==(e=this.userMin)&&void 0!==e?e:a.parse(s.min))&&void 0!==o?o:this.dataMin,this.max=null!==(n=null!==(r=this.userMax)&&void 0!==r?r:a.parse(s.max))&&void 0!==n?n:this.dataMax,rF(this,"foundExtremes"),this.setAxisTranslation(),this.tickInterval=1,this.tickmarkOffset=.5,this.tickPositions=this.treeGrid.mapOfPosToGridNode?this.treeGrid.getTickPositions():[],h){var l=h.getExtremes();this.min=rY(l.min,l.dataMin),this.max=rY(l.max,l.dataMax),this.tickPositions=h.tickPositions}this.linkedParent=h}else t.apply(this,Array.prototype.slice.call(arguments,1))}function r0(t){var i=this;"treegrid"===this.type&&i.visible&&i.tickPositions.forEach(function(t){var e,o=i.ticks[t];(null===(e=o.label)||void 0===e?void 0:e.attachedTreeGridEvents)&&(rj(o.label.element),o.label.attachedTreeGridEvents=!1)}),t.apply(i,Array.prototype.slice.call(arguments,1))}var r1=function(){function t(t){this.axis=t}return t.compose=function(t,i,e,o){if(!t.keepProps.includes("treeGrid")){var r,n,s,a=t.prototype;t.keepProps.push("treeGrid"),rV(a,"generateTick",r$),rV(a,"init",rJ),rV(a,"setTickInterval",rQ),rV(a,"redraw",r0),a.utils={getNode:rb.getNode},S||(S=o)}return r=t,n=i,s=o,r.keepProps.includes("grid")||(r.keepProps.push("grid"),r.prototype.getMaxLabelDimensions=o8,o6(r.prototype,"unsquish",ru),o6(r.prototype,"getOffset",o9),oK(r,"init",rl),oK(r,"afterGetTitlePosition",o7),oK(r,"afterInit",rt),oK(r,"afterRender",ri),oK(r,"afterSetAxisTranslation",re),oK(r,"afterSetOptions",ro),oK(r,"afterSetOptions",rr),oK(r,"afterSetScale",rn),oK(r,"afterTickSize",rs),oK(r,"trimTicks",rp),oK(r,"destroy",rh),oK(n,"afterSetChartSize",ra),oK(s,"afterGetLabelPosition",rd),oK(s,"labelFormat",rc)),oq.compose(t,e),rT.compose(o),t},t.prototype.setCollapsedStatus=function(t){var i=this.axis,e=i.chart;i.series.forEach(function(i){var o=i.options.data;if(t.id&&o){var r=e.get(t.id),n=o[i.data.indexOf(r)];r&&n&&(r.collapsed=t.collapsed,n.collapsed=t.collapsed)}})},t.prototype.collapse=function(t){var i=this.axis,e=i.options.breaks||[],o=rq(t,i.max);return e.push(o),t.collapsed=!0,i.treeGrid.setCollapsedStatus(t),e},t.prototype.expand=function(t){var i=this.axis,e=i.options.breaks||[],o=rq(t,i.max);return t.collapsed=!1,i.treeGrid.setCollapsedStatus(t),e.reduce(function(t,i){return(i.to!==o.to||i.from!==o.from)&&t.push(i),t},[])},t.prototype.getTickPositions=function(){var t=this.axis,i=Math.floor(t.min/t.tickInterval)*t.tickInterval,e=Math.ceil(t.max/t.tickInterval)*t.tickInterval;return Object.keys(t.treeGrid.mapOfPosToGridNode||{}).reduce(function(o,r){var n,s=+r;return s>=i&&s<=e&&!(null===(n=t.brokenAxis)||void 0===n?void 0:n.isInAnyBreak(s))&&o.push(s),o},[])},t.prototype.isCollapsed=function(t){var i=this.axis,e=i.options.breaks||[],o=rq(t,i.max);return e.some(function(t){return t.from===o.from&&t.to===o.to})},t.prototype.toggleCollapse=function(t){return this.isCollapsed(t)?this.expand(t):this.collapse(t)},t}(),r2=(m=function(t,i){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw TypeError("Class extends value "+String(i)+" is not a constructor or null");function e(){this.constructor=t}m(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}),r3=tN().series,r5=tN().seriesTypes.xrange,r6=R().extend,r4=R().isNumber,r8=R().merge,r9=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return r2(i,t),i.compose=function(t,i,e,o){if(r5.compose(t),i&&(os(t,i),e))e9.compose(i,e.prototype.pointClass),o&&r1.compose(t,i,e,o)},i.prototype.drawPoint=function(i,e){var o,r=this.options,n=this.chart.renderer,s=i.shapeArgs,a=i.plotY,h=i.selected&&"select",l=r.stacking&&!r.borderRadius,d=i.graphic;i.options.milestone?r4(a)&&null!==i.y&&!1!==i.visible?(o=n.symbols.diamond(s.x||0,s.y||0,s.width||0,s.height||0),d?d[e]({d:o}):i.graphic=d=n.path(o).addClass(i.getClassName(),!0).add(i.group||this.group),this.chart.styledMode||i.graphic.attr(this.pointAttribs(i,h)).shadow(r.shadow,null,l)):d&&(i.graphic=d.destroy()):t.prototype.drawPoint.call(this,i,e)},i.prototype.translatePoint=function(i){var e,o;t.prototype.translatePoint.call(this,i),i.options.milestone&&(o=(e=i.shapeArgs).height||0,i.shapeArgs={x:(e.x||0)-o/2,y:e.y,width:o,height:o})},i.defaultOptions=r8(r5.defaultOptions,oW),i}(r5);r6(r9.prototype,{pointArrayMap:["start","end","y"],pointClass:oz,setData:r3.prototype.setData}),tN().registerSeriesType("gantt",r9);var r7=R();r7.Connection=r7.Connection||j,r7.GanttChart=r7.GanttChart||td,r7.Navigator=r7.Navigator||iz,r7.RangeSelector=r7.RangeSelector||eG,r7.Scrollbar=r7.Scrollbar||iu,r7.ganttChart=r7.GanttChart.ganttChart,N(r7.SVGRenderer),({compose:function(t,i){K(V,"CurrentDateIndication")&&(q(t,"afterSetOptions",Q),q(i,"render",tt),$(i.prototype,"getLabelText",ti))}}).compose(r7.Axis,r7.PlotLineOrBand),r9.compose(r7.Axis,r7.Chart,r7.Series,r7.Tick),r7.Navigator.compose(r7.Chart,r7.Axis,r7.Series),r7.RangeSelector.compose(r7.Axis,r7.Chart),r7.Scrollbar.compose(r7.Axis);var nt=R();return C.default}()});