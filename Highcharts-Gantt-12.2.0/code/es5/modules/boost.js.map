{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/boost\n * @requires highcharts\n *\n * Boost module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n *\n * */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/boost\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Color\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/boost\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ boost_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/Boost/Boostables.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nvar Boostables = [\n    'area',\n    'areaspline',\n    'arearange',\n    'column',\n    'columnrange',\n    'bar',\n    'line',\n    'scatter',\n    'heatmap',\n    'bubble',\n    'treemap'\n];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_Boostables = (Boostables);\n\n;// ./code/es5/es-modules/Extensions/Boost/BoostableMap.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nvar BoostableMap = {};\nBoost_Boostables.forEach(function (item) {\n    BoostableMap[item] = true;\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_BoostableMap = (BoostableMap);\n\n;// ./code/es5/es-modules/Extensions/Boost/BoostChart.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass, wglMode) {\n    if (wglMode && pushUnique(composed, 'Boost.Chart')) {\n        ChartClass.prototype.callbacks.push(onChartCallback);\n    }\n    return ChartClass;\n}\n/**\n * Get the clip rectangle for a target, either a series or the chart.\n * For the chart, we need to consider the maximum extent of its Y axes,\n * in case of Highcharts Stock panes and navigator.\n *\n * @private\n * @function Highcharts.Chart#getBoostClipRect\n */\nfunction getBoostClipRect(chart, target) {\n    var navigator = chart.navigator;\n    var clipBox = {\n            x: chart.plotLeft,\n            y: chart.plotTop,\n            width: chart.plotWidth,\n            height: chart.plotHeight\n        };\n    if (navigator && chart.inverted) { // #17820, #20936\n        clipBox.width += navigator.top + navigator.height;\n        if (!navigator.opposite) {\n            clipBox.x = navigator.left;\n        }\n    }\n    else if (navigator && !chart.inverted) {\n        clipBox.height = navigator.top + navigator.height - chart.plotTop;\n    }\n    // Clipping of individual series (#11906, #19039).\n    if (target.is) {\n        var _a = target,\n            xAxis = _a.xAxis,\n            yAxis = _a.yAxis;\n        clipBox = chart.getClipBox(target);\n        if (chart.inverted) {\n            var lateral = clipBox.width;\n            clipBox.width = clipBox.height;\n            clipBox.height = lateral;\n            clipBox.x = yAxis.pos;\n            clipBox.y = xAxis.pos;\n        }\n        else {\n            clipBox.x = xAxis.pos;\n            clipBox.y = yAxis.pos;\n        }\n    }\n    if (target === chart) {\n        var verticalAxes = chart.inverted ? chart.xAxis : chart.yAxis; // #14444\n            if (verticalAxes.length <= 1) {\n                clipBox.y = Math.min(verticalAxes[0].pos,\n            clipBox.y);\n            clipBox.height = (verticalAxes[0].pos -\n                chart.plotTop +\n                verticalAxes[0].len);\n        }\n    }\n    return clipBox;\n}\n/**\n * Returns true if the chart is in series boost mode.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart to check.\n * @return {boolean}\n * `true` if the chart is in series boost mode.\n */\nfunction isChartSeriesBoosting(chart) {\n    var allSeries = chart.series,\n        boost = chart.boost = chart.boost || {},\n        boostOptions = chart.options.boost || {},\n        threshold = pick(boostOptions.seriesThreshold, 50);\n    if (allSeries.length >= threshold) {\n        return true;\n    }\n    if (allSeries.length === 1) {\n        return false;\n    }\n    var allowBoostForce = boostOptions.allowForce;\n    if (typeof allowBoostForce === 'undefined') {\n        allowBoostForce = true;\n        for (var _i = 0, _a = chart.xAxis; _i < _a.length; _i++) {\n            var axis = _a[_i];\n            if (pick(axis.min, -Infinity) > pick(axis.dataMin, -Infinity) ||\n                pick(axis.max, Infinity) < pick(axis.dataMax, Infinity)) {\n                allowBoostForce = false;\n                break;\n            }\n        }\n    }\n    if (typeof boost.forceChartBoost !== 'undefined') {\n        if (allowBoostForce) {\n            return boost.forceChartBoost;\n        }\n        boost.forceChartBoost = void 0;\n    }\n    // If there are more than five series currently boosting,\n    // we should boost the whole chart to avoid running out of webgl contexts.\n    var canBoostCount = 0,\n        needBoostCount = 0,\n        seriesOptions;\n    for (var _b = 0, allSeries_1 = allSeries; _b < allSeries_1.length; _b++) {\n        var series = allSeries_1[_b];\n        seriesOptions = series.options;\n        // Don't count series with boostThreshold set to 0\n        // See #8950\n        // Also don't count if the series is hidden.\n        // See #9046\n        if (seriesOptions.boostThreshold === 0 ||\n            series.visible === false) {\n            continue;\n        }\n        // Don't count heatmap series as they are handled differently.\n        // In the future we should make the heatmap/treemap path compatible\n        // with forcing. See #9636.\n        if (series.type === 'heatmap') {\n            continue;\n        }\n        if (Boost_BoostableMap[series.type]) {\n            ++canBoostCount;\n        }\n        if (patientMax(series.getColumn('x', true), seriesOptions.data, \n        /// series.xData,\n        series.points) >= (seriesOptions.boostThreshold || Number.MAX_VALUE)) {\n            ++needBoostCount;\n        }\n    }\n    boost.forceChartBoost = allowBoostForce && ((\n    // Even when the series that need a boost are less than or equal\n    // to 5, force a chart boost when all series are to be boosted.\n    // See #18815\n    canBoostCount === allSeries.length &&\n        needBoostCount === canBoostCount) ||\n        needBoostCount > 5);\n    return boost.forceChartBoost;\n}\n/**\n * Take care of the canvas blitting\n * @private\n */\nfunction onChartCallback(chart) {\n    /**\n     * Convert chart-level canvas to image.\n     * @private\n     */\n    function canvasToSVG() {\n        if (chart.boost &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            chart.boost.wgl.render(chart);\n        }\n    }\n    /**\n     * Clear chart-level canvas.\n     * @private\n     */\n    function preRender() {\n        var _a,\n            _b;\n        // Reset force state\n        chart.boost = chart.boost || {};\n        chart.boost.forceChartBoost = void 0;\n        chart.boosted = false;\n        // Clear the canvas\n        if (!chart.axes.some(function (axis) { return axis.isPanning; })) {\n            (_b = (_a = chart.boost).clear) === null || _b === void 0 ? void 0 : _b.call(_a);\n        }\n        if (chart.boost.canvas &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            // Allocate\n            chart.boost.wgl.allocateBuffer(chart);\n        }\n        // See #6518 + #6739\n        if (chart.boost.markerGroup &&\n            chart.xAxis &&\n            chart.xAxis.length > 0 &&\n            chart.yAxis &&\n            chart.yAxis.length > 0) {\n            chart.boost.markerGroup.translate(chart.xAxis[0].pos, chart.yAxis[0].pos);\n        }\n    }\n    addEvent(chart, 'predraw', preRender);\n    // Use the load event rather than redraw, otherwise user load events will\n    // fire too early (#18755)\n    addEvent(chart, 'load', canvasToSVG, { order: -1 });\n    addEvent(chart, 'redraw', canvasToSVG);\n    var prevX = -1;\n    var prevY = -1;\n    addEvent(chart.pointer, 'afterGetHoverData', function (e) {\n        var _a;\n        var series = (_a = e.hoverPoint) === null || _a === void 0 ? void 0 : _a.series;\n        chart.boost = chart.boost || {};\n        if (chart.boost.markerGroup && series) {\n            var xAxis = chart.inverted ? series.yAxis : series.xAxis;\n            var yAxis = chart.inverted ? series.xAxis : series.yAxis;\n            if ((xAxis && xAxis.pos !== prevX) ||\n                (yAxis && yAxis.pos !== prevY)) {\n                // #21176: If the axis is changed, hide teh halo without\n                // animation  to prevent flickering of halos sharing the\n                // same marker group\n                chart.series.forEach(function (s) {\n                    var _a;\n                    (_a = s.halo) === null || _a === void 0 ? void 0 : _a.hide();\n                });\n                // #10464: Keep the marker group position in sync with the\n                // position of the hovered series axes since there is only\n                // one shared marker group when boosting.\n                chart.boost.markerGroup.translate(xAxis.pos, yAxis.pos);\n                prevX = xAxis.pos;\n                prevY = yAxis.pos;\n            }\n        }\n    });\n}\n/**\n * Tolerant max() function.\n *\n * @private\n * @param {...Array<Array<unknown>>} args\n * Max arguments\n * @return {number}\n * Max value\n */\nfunction patientMax() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var r = -Number.MAX_VALUE;\n    args.forEach(function (t) {\n        if (typeof t !== 'undefined' &&\n            t !== null &&\n            typeof t.length !== 'undefined') {\n            if (t.length > 0) {\n                r = t.length;\n                return true;\n            }\n        }\n    });\n    return r;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar BoostChart = {\n    compose: compose,\n    getBoostClipRect: getBoostClipRect,\n    isChartSeriesBoosting: isChartSeriesBoosting\n};\n/* harmony default export */ var Boost_BoostChart = (BoostChart);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Extensions/Boost/WGLDrawMode.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nvar WGLDrawMode = {\n    'area': 'LINES',\n    'arearange': 'LINES',\n    'areaspline': 'LINES',\n    'column': 'LINES',\n    'columnrange': 'LINES',\n    'bar': 'LINES',\n    'line': 'LINE_STRIP',\n    'scatter': 'POINTS',\n    'heatmap': 'TRIANGLES',\n    'treemap': 'TRIANGLES',\n    'bubble': 'POINTS'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLDrawMode = (WGLDrawMode);\n\n;// ./code/es5/es-modules/Extensions/Boost/WGLShader.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, WGLShader_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\nvar fragmentShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    'precision highp float;',\n    'uniform vec4 fillColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform sampler2D uSampler;',\n    'uniform bool isCircle;',\n    'uniform bool hasColor;',\n    // 'vec4 toColor(float value, vec2 point) {',\n    //     'return vec4(0.0, 0.0, 0.0, 0.0);',\n    // '}',\n    'void main(void) {',\n    'vec4 col = fillColor;',\n    'vec4 tcol = texture2D(uSampler, gl_PointCoord.st);',\n    'if (hasColor) {',\n    'col = vColor;',\n    '}',\n    'if (isCircle) {',\n    'col *= tcol;',\n    'if (tcol.r < 0.0) {',\n    'discard;',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\nvar vertexShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    '#version 100',\n    '#define LN10 2.302585092994046',\n    'precision highp float;',\n    'attribute vec4 aVertexPosition;',\n    'attribute vec4 aColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform mat4 uPMatrix;',\n    'uniform float pSize;',\n    'uniform float translatedThreshold;',\n    'uniform bool hasThreshold;',\n    'uniform bool skipTranslation;',\n    'uniform float xAxisTrans;',\n    'uniform float xAxisMin;',\n    'uniform float xAxisMinPad;',\n    'uniform float xAxisPointRange;',\n    'uniform float xAxisLen;',\n    'uniform bool  xAxisPostTranslate;',\n    'uniform float xAxisOrdinalSlope;',\n    'uniform float xAxisOrdinalOffset;',\n    'uniform float xAxisPos;',\n    'uniform bool  xAxisCVSCoord;',\n    'uniform bool  xAxisIsLog;',\n    'uniform bool  xAxisReversed;',\n    'uniform float yAxisTrans;',\n    'uniform float yAxisMin;',\n    'uniform float yAxisMinPad;',\n    'uniform float yAxisPointRange;',\n    'uniform float yAxisLen;',\n    'uniform bool  yAxisPostTranslate;',\n    'uniform float yAxisOrdinalSlope;',\n    'uniform float yAxisOrdinalOffset;',\n    'uniform float yAxisPos;',\n    'uniform bool  yAxisCVSCoord;',\n    'uniform bool  yAxisIsLog;',\n    'uniform bool  yAxisReversed;',\n    'uniform bool  isBubble;',\n    'uniform bool  bubbleSizeByArea;',\n    'uniform float bubbleZMin;',\n    'uniform float bubbleZMax;',\n    'uniform float bubbleZThreshold;',\n    'uniform float bubbleMinSize;',\n    'uniform float bubbleMaxSize;',\n    'uniform bool  bubbleSizeAbs;',\n    'uniform bool  isInverted;',\n    'float bubbleRadius(){',\n    'float value = aVertexPosition.w;',\n    'float zMax = bubbleZMax;',\n    'float zMin = bubbleZMin;',\n    'float radius = 0.0;',\n    'float pos = 0.0;',\n    'float zRange = zMax - zMin;',\n    'if (bubbleSizeAbs){',\n    'value = value - bubbleZThreshold;',\n    'zMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);',\n    'zMin = 0.0;',\n    '}',\n    'if (value < zMin){',\n    'radius = bubbleZMin / 2.0 - 1.0;',\n    '} else {',\n    'pos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;',\n    'if (bubbleSizeByArea && pos > 0.0){',\n    'pos = sqrt(pos);',\n    '}',\n    'radius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;',\n    '}',\n    'return radius * 2.0;',\n    '}',\n    'float translate(float val,',\n    'float pointPlacement,',\n    'float localA,',\n    'float localMin,',\n    'float minPixelPadding,',\n    'float pointRange,',\n    'float len,',\n    'bool  cvsCoord,',\n    'bool  isLog,',\n    'bool  reversed',\n    '){',\n    'float sign = 1.0;',\n    'float cvsOffset = 0.0;',\n    'if (cvsCoord) {',\n    'sign *= -1.0;',\n    'cvsOffset = len;',\n    '}',\n    'if (isLog) {',\n    'val = log(val) / LN10;',\n    '}',\n    'if (reversed) {',\n    'sign *= -1.0;',\n    'cvsOffset -= sign * len;',\n    '}',\n    'return sign * (val - localMin) * localA + cvsOffset + ',\n    '(sign * minPixelPadding);', // ' + localA * pointPlacement * pointRange;',\n    '}',\n    'float xToPixels(float value) {',\n    'if (skipTranslation){',\n    'return value;// + xAxisPos;',\n    '}',\n    'return translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;',\n    '}',\n    'float yToPixels(float value, float checkTreshold) {',\n    'float v;',\n    'if (skipTranslation){',\n    'v = value;// + yAxisPos;',\n    '} else {',\n    'v = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;',\n    'if (v > yAxisLen) {',\n    'v = yAxisLen;',\n    '}',\n    '}',\n    'if (checkTreshold > 0.0 && hasThreshold) {',\n    'v = min(v, translatedThreshold);',\n    '}',\n    'return v;',\n    '}',\n    'void main(void) {',\n    'if (isBubble){',\n    'gl_PointSize = bubbleRadius();',\n    '} else {',\n    'gl_PointSize = pSize;',\n    '}',\n    // 'gl_PointSize = 10.0;',\n    'vColor = aColor;',\n    'if (skipTranslation && isInverted) {',\n    // If we get translated values from JS, just swap them (x, y)\n    'gl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);',\n    '} else if (isInverted) {',\n    // But when calculating pixel positions directly,\n    // swap axes and values (x, y)\n    'gl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);',\n    '} else {',\n    'gl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);',\n    '}',\n    // 'gl_Position = uPMatrix * vec4(aVertexPosition.x, aVertexPosition.y, 0.0, 1.0);',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * A static shader mimicing axis translation functions found in Core/Axis\n *\n * @private\n *\n * @param {WebGLContext} gl\n * the context in which the shader is active\n */\nvar WGLShader = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function WGLShader(gl) {\n        // Error stack\n        this.errors = [];\n        this.uLocations = {};\n        this.gl = gl;\n        if (gl && !this.createShader()) {\n            return void 0;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Bind the shader.\n     * This makes the shader the active one until another one is bound,\n     * or until 0 is bound.\n     * @private\n     */\n    WGLShader.prototype.bind = function () {\n        if (this.gl && this.shaderProgram) {\n            this.gl.useProgram(this.shaderProgram);\n        }\n    };\n    /**\n     * Create the shader.\n     * Loads the shader program statically defined above\n     * @private\n     */\n    WGLShader.prototype.createShader = function () {\n        var _this = this;\n        var v = this.stringToProgram(vertexShader, 'vertex'), f = this.stringToProgram(fragmentShader, 'fragment'), uloc = function (n) { return (_this.gl.getUniformLocation(_this.shaderProgram, n)); };\n        if (!v || !f) {\n            this.shaderProgram = false;\n            this.handleErrors();\n            return false;\n        }\n        this.shaderProgram = this.gl.createProgram();\n        this.gl.attachShader(this.shaderProgram, v);\n        this.gl.attachShader(this.shaderProgram, f);\n        this.gl.linkProgram(this.shaderProgram);\n        if (!this.gl.getProgramParameter(this.shaderProgram, this.gl.LINK_STATUS)) {\n            this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram));\n            this.handleErrors();\n            this.shaderProgram = false;\n            return false;\n        }\n        this.gl.useProgram(this.shaderProgram);\n        this.gl.bindAttribLocation(this.shaderProgram, 0, 'aVertexPosition');\n        this.pUniform = uloc('uPMatrix');\n        this.psUniform = uloc('pSize');\n        this.fcUniform = uloc('fillColor');\n        this.isBubbleUniform = uloc('isBubble');\n        this.bubbleSizeAbsUniform = uloc('bubbleSizeAbs');\n        this.bubbleSizeAreaUniform = uloc('bubbleSizeByArea');\n        this.uSamplerUniform = uloc('uSampler');\n        this.skipTranslationUniform = uloc('skipTranslation');\n        this.isCircleUniform = uloc('isCircle');\n        this.isInverted = uloc('isInverted');\n        return true;\n    };\n    /**\n     * Handle errors accumulated in errors stack\n     * @private\n     */\n    WGLShader.prototype.handleErrors = function () {\n        if (this.errors.length) {\n            error('[highcharts boost] shader error - ' +\n                this.errors.join('\\n'));\n        }\n    };\n    /**\n     * String to shader program\n     * @private\n     * @param {string} str\n     * Program source\n     * @param {string} type\n     * Program type: either `vertex` or `fragment`\n     */\n    WGLShader.prototype.stringToProgram = function (str, type) {\n        var shader = this.gl.createShader(type === 'vertex' ? this.gl.VERTEX_SHADER : this.gl.FRAGMENT_SHADER);\n        this.gl.shaderSource(shader, str);\n        this.gl.compileShader(shader);\n        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {\n            this.errors.push('when compiling ' +\n                type +\n                ' shader:\\n' +\n                this.gl.getShaderInfoLog(shader));\n            return false;\n        }\n        return shader;\n    };\n    /**\n     * Destroy the shader\n     * @private\n     */\n    WGLShader.prototype.destroy = function () {\n        if (this.gl && this.shaderProgram) {\n            this.gl.deleteProgram(this.shaderProgram);\n            this.shaderProgram = false;\n        }\n    };\n    WGLShader.prototype.fillColorUniform = function () {\n        return this.fcUniform;\n    };\n    /**\n     * Get the shader program handle\n     * @private\n     * @return {WebGLProgram}\n     * The handle for the program\n     */\n    WGLShader.prototype.getProgram = function () {\n        return this.shaderProgram;\n    };\n    WGLShader.prototype.pointSizeUniform = function () {\n        return this.psUniform;\n    };\n    WGLShader.prototype.perspectiveUniform = function () {\n        return this.pUniform;\n    };\n    /**\n     * Flush\n     * @private\n     */\n    WGLShader.prototype.reset = function () {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isBubbleUniform, 0);\n            this.gl.uniform1i(this.isCircleUniform, 0);\n        }\n    };\n    /**\n     * Set bubble uniforms\n     * @private\n     * @param {Highcharts.Series} series\n     * Series to use\n     */\n    WGLShader.prototype.setBubbleUniforms = function (series, zCalcMin, zCalcMax, pixelRatio) {\n        if (pixelRatio === void 0) { pixelRatio = 1; }\n        var seriesOptions = series.options;\n        var zMin = Number.MAX_VALUE,\n            zMax = -Number.MAX_VALUE;\n        if (this.gl && this.shaderProgram && series.is('bubble')) {\n            var pxSizes = series.getPxExtremes();\n            zMin = WGLShader_pick(seriesOptions.zMin, clamp(zCalcMin, seriesOptions.displayNegative === false ?\n                seriesOptions.zThreshold : -Number.MAX_VALUE, zMin));\n            zMax = WGLShader_pick(seriesOptions.zMax, Math.max(zMax, zCalcMax));\n            this.gl.uniform1i(this.isBubbleUniform, 1);\n            this.gl.uniform1i(this.isCircleUniform, 1);\n            this.gl.uniform1i(this.bubbleSizeAreaUniform, (series.options.sizeBy !== 'width'));\n            this.gl.uniform1i(this.bubbleSizeAbsUniform, series.options\n                .sizeByAbsoluteValue);\n            this.setUniform('bubbleMinSize', pxSizes.minPxSize * pixelRatio);\n            this.setUniform('bubbleMaxSize', pxSizes.maxPxSize * pixelRatio);\n            this.setUniform('bubbleZMin', zMin);\n            this.setUniform('bubbleZMax', zMax);\n            this.setUniform('bubbleZThreshold', series.options.zThreshold);\n        }\n    };\n    /**\n     * Set the Color uniform.\n     * @private\n     * @param {Array<number>} color\n     * Array with RGBA values.\n     */\n    WGLShader.prototype.setColor = function (color) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform4f(this.fcUniform, color[0] / 255.0, color[1] / 255.0, color[2] / 255.0, color[3]);\n        }\n    };\n    /**\n     * Enable/disable circle drawing\n     * @private\n     */\n    WGLShader.prototype.setDrawAsCircle = function (flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isCircleUniform, flag ? 1 : 0);\n        }\n    };\n    /**\n     * Set if inversion state\n     * @private\n     * @param {number} flag\n     * Inversion flag\n     */\n    WGLShader.prototype.setInverted = function (flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isInverted, flag);\n        }\n    };\n    /**\n     * Set the perspective matrix\n     * @private\n     * @param {Float32List} m\n     * Matrix 4 x 4\n     */\n    WGLShader.prototype.setPMatrix = function (m) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniformMatrix4fv(this.pUniform, false, m);\n        }\n    };\n    /**\n     * Set the point size.\n     * @private\n     * @param {number} p\n     * Point size\n     */\n    WGLShader.prototype.setPointSize = function (p) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1f(this.psUniform, p);\n        }\n    };\n    /**\n     * Set skip translation\n     * @private\n     */\n    WGLShader.prototype.setSkipTranslation = function (flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.skipTranslationUniform, flag === true ? 1 : 0);\n        }\n    };\n    /**\n     * Set the active texture\n     * @private\n     * @param {number} texture\n     * Texture to activate\n     */\n    WGLShader.prototype.setTexture = function (texture) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.uSamplerUniform, texture);\n        }\n    };\n    /**\n     * Set a uniform value.\n     * This uses a hash map to cache uniform locations.\n     * @private\n     * @param {string} name\n     * Name of the uniform to set.\n     * @param {number} val\n     * Value to set\n     */\n    WGLShader.prototype.setUniform = function (name, val) {\n        if (this.gl && this.shaderProgram) {\n            var u = this.uLocations[name] = (this.uLocations[name] ||\n                    this.gl.getUniformLocation(this.shaderProgram,\n                name));\n            this.gl.uniform1f(u, val);\n        }\n    };\n    return WGLShader;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLShader = (WGLShader);\n\n;// ./code/es5/es-modules/Extensions/Boost/WGLVertexBuffer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Vertex Buffer abstraction.\n * A vertex buffer is a set of vertices which are passed to the GPU\n * in a single call.\n *\n * @private\n * @class\n * @name WGLVertexBuffer\n *\n * @param {WebGLContext} gl\n * Context in which to create the buffer.\n * @param {WGLShader} shader\n * Shader to use.\n */\nvar WGLVertexBuffer = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function WGLVertexBuffer(gl, shader, dataComponents\n    /* , type */\n    ) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.buffer = false;\n        this.iterator = 0;\n        this.preAllocated = false;\n        this.vertAttribute = false;\n        this.components = dataComponents || 2;\n        this.dataComponents = dataComponents;\n        this.gl = gl;\n        this.shader = shader;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Note about pre-allocated buffers:\n     *     - This is slower for charts with many series\n     * @private\n     */\n    WGLVertexBuffer.prototype.allocate = function (size) {\n        this.iterator = -1;\n        this.preAllocated = new Float32Array(size * 4);\n    };\n    /**\n     * Bind the buffer\n     * @private\n     */\n    WGLVertexBuffer.prototype.bind = function () {\n        if (!this.buffer) {\n            return false;\n        }\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        // gl.enableVertexAttribArray(vertAttribute);\n        // gl.bindBuffer(gl.ARRAY_BUFFER, buffer);\n        this.gl.vertexAttribPointer(this.vertAttribute, this.components, this.gl.FLOAT, false, 0, 0);\n        /// gl.enableVertexAttribArray(vertAttribute);\n    };\n    /**\n     * Build the buffer\n     * @private\n     * @param {Array<number>} dataIn\n     * Zero padded array of indices\n     * @param {string} attrib\n     * Name of the Attribute to bind the buffer to\n     * @param {number} dataComponents\n     * Number of components per. indice\n     */\n    WGLVertexBuffer.prototype.build = function (dataIn, attrib, dataComponents) {\n        var farray;\n        this.data = dataIn || [];\n        if ((!this.data || this.data.length === 0) && !this.preAllocated) {\n            /// console.error('trying to render empty vbuffer');\n            this.destroy();\n            return false;\n        }\n        this.components = dataComponents || this.components;\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n        }\n        if (!this.preAllocated) {\n            farray = new Float32Array(this.data);\n        }\n        this.buffer = this.gl.createBuffer();\n        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffer);\n        this.gl.bufferData(this.gl.ARRAY_BUFFER, this.preAllocated || farray, this.gl.STATIC_DRAW);\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        this.vertAttribute = this.gl\n            .getAttribLocation(this.shader.getProgram(), attrib);\n        this.gl.enableVertexAttribArray(this.vertAttribute);\n        // Trigger cleanup\n        farray = false;\n        return true;\n    };\n    /**\n     * @private\n     */\n    WGLVertexBuffer.prototype.destroy = function () {\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n            this.buffer = false;\n            this.vertAttribute = false;\n        }\n        this.iterator = 0;\n        this.components = this.dataComponents || 2;\n        this.data = [];\n    };\n    /**\n     * Adds data to the pre-allocated buffer.\n     * @private\n     * @param {number} x\n     * X data\n     * @param {number} y\n     * Y data\n     * @param {number} a\n     * A data\n     * @param {number} b\n     * B data\n     */\n    WGLVertexBuffer.prototype.push = function (x, y, a, b) {\n        if (this.preAllocated) { // && iterator <= preAllocated.length - 4) {\n            this.preAllocated[++this.iterator] = x;\n            this.preAllocated[++this.iterator] = y;\n            this.preAllocated[++this.iterator] = a;\n            this.preAllocated[++this.iterator] = b;\n        }\n    };\n    /**\n     * Render the buffer\n     *\n     * @private\n     * @param {number} from\n     * Start indice.\n     * @param {number} to\n     * End indice.\n     * @param {WGLDrawModeValue} drawMode\n     * Draw mode.\n     */\n    WGLVertexBuffer.prototype.render = function (from, to, drawMode) {\n        var length = this.preAllocated ?\n                this.preAllocated.length : this.data.length;\n        if (!this.buffer) {\n            return false;\n        }\n        if (!length) {\n            return false;\n        }\n        if (!from || from > length || from < 0) {\n            from = 0;\n        }\n        if (!to || to > length) {\n            to = length;\n        }\n        if (from >= to) {\n            return false;\n        }\n        drawMode = drawMode || 'POINTS';\n        this.gl.drawArrays(this.gl[drawMode], from / this.components, (to - from) / this.components);\n        return true;\n    };\n    return WGLVertexBuffer;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLVertexBuffer = (WGLVertexBuffer);\n\n;// ./code/es5/es-modules/Extensions/Boost/WGLRenderer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, WGLRenderer_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\n\n\n/* *\n *\n *  Constants\n *\n * */\n// Things to draw as \"rectangles\" (i.e lines)\nvar asBar = {\n    'column': true,\n    'columnrange': true,\n    'bar': true,\n    'area': true,\n    'areaspline': true,\n    'arearange': true\n};\nvar asCircle = {\n    'scatter': true,\n    'bubble': true\n};\nvar contexts = [\n    'webgl',\n    'experimental-webgl',\n    'moz-webgl',\n    'webkit-3d'\n];\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * Main renderer. Used to render series.\n *\n * Notes to self:\n * - May be able to build a point map by rendering to a separate canvas and\n *   encoding values in the color data.\n * - Need to figure out a way to transform the data quicker\n *\n * @private\n *\n * @param {Function} postRenderCallback\n */\nvar WGLRenderer = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function WGLRenderer(postRenderCallback) {\n        /**\n         * The data to render - array of coordinates.\n         * Repeating sequence of [x, y, checkThreshold, pointSize].\n         */\n        this.data = [];\n        // Height of our viewport in pixels\n        this.height = 0;\n        // Is it inited?\n        this.isInited = false;\n        // The marker data\n        this.markerData = [];\n        // The series stack\n        this.series = [];\n        // Texture handles\n        this.textureHandles = {};\n        // Width of our viewport in pixels\n        this.width = 0;\n        this.postRenderCallback = postRenderCallback;\n        this.settings = {\n            pointSize: 1,\n            lineWidth: 1,\n            fillColor: '#AA00AA',\n            useAlpha: true,\n            usePreallocated: false,\n            useGPUTranslations: false,\n            debug: {\n                timeRendering: false,\n                timeSeriesProcessing: false,\n                timeSetup: false,\n                timeBufferCopy: false,\n                timeKDTree: false,\n                showSkipSummary: false\n            }\n        };\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns an orthographic perspective matrix\n     * @private\n     * @param {number} width\n     * the width of the viewport in pixels\n     * @param {number} height\n     * the height of the viewport in pixels\n     */\n    WGLRenderer.orthoMatrix = function (width, height) {\n        var near = 0,\n            far = 1;\n        return [\n            2 / width, 0, 0, 0,\n            0, -(2 / height), 0, 0,\n            0, 0, -2 / (far - near), 0,\n            -1, 1, -(far + near) / (far - near), 1\n        ];\n    };\n    /**\n     * @private\n     */\n    WGLRenderer.seriesPointCount = function (series) {\n        var isStacked,\n            xData,\n            s;\n        if (series.boosted) {\n            isStacked = !!series.options.stacking;\n            xData = ((series.getColumn('x').length ?\n                series.getColumn('x') :\n                void 0) ||\n                series.options.xData ||\n                series.getColumn('x', true));\n            s = (isStacked ? series.data : (xData || series.options.data))\n                .length;\n            if (series.type === 'treemap') {\n                s *= 12;\n            }\n            else if (series.type === 'heatmap') {\n                s *= 6;\n            }\n            else if (asBar[series.type]) {\n                s *= 2;\n            }\n            return s;\n        }\n        return 0;\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    WGLRenderer.prototype.getPixelRatio = function () {\n        return this.settings.pixelRatio || win.devicePixelRatio || 1;\n    };\n    /**\n     * @private\n     */\n    WGLRenderer.prototype.setOptions = function (options) {\n        // The pixelRatio defaults to 1. This is an antipattern, we should\n        // refactor the Boost options to include an object of default options as\n        // base for the merge, like other components.\n        if (!('pixelRatio' in options)) {\n            options.pixelRatio = 1;\n        }\n        merge(true, this.settings, options);\n    };\n    /**\n     * Allocate a float buffer to fit all series\n     * @private\n     */\n    WGLRenderer.prototype.allocateBuffer = function (chart) {\n        var vbuffer = this.vbuffer;\n        var s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        chart.series.forEach(function (series) {\n            if (series.boosted) {\n                s += WGLRenderer.seriesPointCount(series);\n            }\n        });\n        vbuffer && vbuffer.allocate(s);\n    };\n    /**\n     * @private\n     */\n    WGLRenderer.prototype.allocateBufferForSingleSeries = function (series) {\n        var vbuffer = this.vbuffer;\n        var s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        if (series.boosted) {\n            s = WGLRenderer.seriesPointCount(series);\n        }\n        vbuffer && vbuffer.allocate(s);\n    };\n    /**\n     * Clear the depth and color buffer\n     * @private\n     */\n    WGLRenderer.prototype.clear = function () {\n        var gl = this.gl;\n        gl && gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);\n    };\n    /**\n     * Push data for a single series\n     * This calculates additional vertices and transforms the data to be\n     * aligned correctly in memory\n     * @private\n     */\n    WGLRenderer.prototype.pushSeriesData = function (series, inst) {\n        var _this = this;\n        var _a,\n            _b;\n        var data = this.data, settings = this.settings, vbuffer = this.vbuffer, isRange = (series.pointArrayMap &&\n                series.pointArrayMap.join(',') === 'low,high'), chart = series.chart, options = series.options, sorted = series.sorted, xAxis = series.xAxis, yAxis = series.yAxis, isStacked = !!options.stacking, rawData = options.data, xExtremes = series.xAxis.getExtremes(), \n            // Taking into account the offset of the min point #19497\n            xMin = xExtremes.min - (series.xAxis.minPointOffset || 0), xMax = xExtremes.max + (series.xAxis.minPointOffset || 0), yExtremes = series.yAxis.getExtremes(), yMin = yExtremes.min - (series.yAxis.minPointOffset || 0), yMax = yExtremes.max + (series.yAxis.minPointOffset || 0), xData = (series.getColumn('x').length ? series.getColumn('x') : void 0) || options.xData || series.getColumn('x', true), yData = (series.getColumn('y').length ? series.getColumn('y') : void 0) || options.yData || series.getColumn('y', true), zData = (series.getColumn('z').length ? series.getColumn('z') : void 0) || options.zData || series.getColumn('z', true), useRaw = !xData || xData.length === 0, \n            /// threshold = options.threshold,\n            // yBottom = chart.yAxis[0].getThreshold(threshold),\n            // hasThreshold = isNumber(threshold),\n            // colorByPoint = series.options.colorByPoint,\n            // This is required for color by point, so make sure this is\n            // uncommented if enabling that\n            // colorIndex = 0,\n            // Required for color axis support\n            // caxis,\n            connectNulls = options.connectNulls, \n            // For some reason eslint/TypeScript don't pick up that this is\n            // actually used: --- bre1470: it is never read, just set\n            // maxVal: (number|undefined), // eslint-disable-line no-unused-vars\n            points = series.points || false, sdata = isStacked ? series.data : (xData || rawData), closestLeft = { x: Number.MAX_VALUE, y: 0 }, closestRight = { x: -Number.MAX_VALUE, y: 0 }, cullXThreshold = 1, cullYThreshold = 1, chartDestroyed = typeof chart.index === 'undefined', drawAsBar = asBar[series.type], zoneAxis = options.zoneAxis || 'y', zones = options.zones || false, threshold = options.threshold, pixelRatio = this.getPixelRatio();\n        var plotWidth = series.chart.plotWidth,\n            lastX = false,\n            lastY = false,\n            minVal,\n            scolor, \n            //\n            skipped = 0,\n            hadPoints = false, \n            // The following are used in the builder while loop\n            x,\n            y,\n            d,\n            z,\n            i = -1,\n            px = false,\n            nx = false,\n            low,\n            nextInside = false,\n            prevInside = false,\n            pcolor = false,\n            isXInside = false,\n            isYInside = true,\n            firstPoint = true,\n            zoneColors,\n            zoneDefColor = false,\n            gapSize = false,\n            vlen = 0;\n        if (options.boostData && options.boostData.length > 0) {\n            return;\n        }\n        if (options.gapSize) {\n            gapSize = options.gapUnit !== 'value' ?\n                options.gapSize * series.closestPointRange :\n                options.gapSize;\n        }\n        if (zones) {\n            zoneColors = [];\n            zones.forEach(function (zone, i) {\n                if (zone.color) {\n                    var zoneColor = color(zone.color).rgba;\n                    zoneColor[0] /= 255.0;\n                    zoneColor[1] /= 255.0;\n                    zoneColor[2] /= 255.0;\n                    zoneColors[i] = zoneColor;\n                    if (!zoneDefColor && typeof zone.value === 'undefined') {\n                        zoneDefColor = zoneColor;\n                    }\n                }\n            });\n            if (!zoneDefColor) {\n                var seriesColor = ((series.pointAttribs && series.pointAttribs().fill) ||\n                        series.color);\n                zoneDefColor = color(seriesColor).rgba;\n                zoneDefColor[0] /= 255.0;\n                zoneDefColor[1] /= 255.0;\n                zoneDefColor[2] /= 255.0;\n            }\n        }\n        if (chart.inverted) {\n            plotWidth = series.chart.plotHeight;\n        }\n        series.closestPointRangePx = Number.MAX_VALUE;\n        /**\n         * Push color to color buffer - need to do this per vertex.\n         * @private\n         */\n        var pushColor = function (color) {\n                if (color) {\n                    inst.colorData.push(color[0]);\n                inst.colorData.push(color[1]);\n                inst.colorData.push(color[2]);\n                inst.colorData.push(color[3]);\n            }\n        };\n        /**\n         * Push a vertice to the data buffer.\n         * @private\n         */\n        var vertice = function (x,\n            y,\n            checkTreshold,\n            pointSize,\n            color) {\n                if (pointSize === void 0) { pointSize = 1; }\n                pushColor(color);\n            // Correct for pixel ratio\n            if (pixelRatio !== 1 && (!settings.useGPUTranslations ||\n                inst.skipTranslation)) {\n                x *= pixelRatio;\n                y *= pixelRatio;\n                pointSize *= pixelRatio;\n            }\n            if (settings.usePreallocated && vbuffer) {\n                vbuffer.push(x, y, checkTreshold ? 1 : 0, pointSize);\n                vlen += 4;\n            }\n            else {\n                data.push(x);\n                data.push(y);\n                data.push(checkTreshold ? pixelRatio : 0);\n                data.push(pointSize);\n            }\n        };\n        /**\n         * @private\n         */\n        var closeSegment = function () {\n                if (inst.segments.length) {\n                    inst.segments[inst.segments.length - 1].to = data.length || vlen;\n            }\n        };\n        /**\n         * Create a new segment for the current set.\n         * @private\n         */\n        var beginSegment = function () {\n                // Insert a segment on the series.\n                // A segment is just a start indice.\n                // When adding a segment, if one exists from before, it should\n                // set the previous segment's end\n                if (inst.segments.length &&\n                    inst.segments[inst.segments.length - 1].from === (data.length || vlen)) {\n                    return;\n            }\n            closeSegment();\n            inst.segments.push({\n                from: data.length || vlen\n            });\n        };\n        /**\n         * Push a rectangle to the data buffer.\n         * @private\n         */\n        var pushRect = function (x,\n            y,\n            w,\n            h,\n            color) {\n                pushColor(color);\n            vertice(x + w, y);\n            pushColor(color);\n            vertice(x, y);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x + w, y + h);\n            pushColor(color);\n            vertice(x + w, y);\n        };\n        // Create the first segment\n        beginSegment();\n        // Special case for point shapes\n        if (points && points.length > 0) {\n            // If we're doing points, we assume that the points are already\n            // translated, so we skip the shader translation.\n            inst.skipTranslation = true;\n            // Force triangle draw mode\n            inst.drawMode = 'TRIANGLES';\n            // We don't have a z component in the shader, so we need to sort.\n            if (points[0].node && points[0].node.levelDynamic) {\n                points.sort(function (a, b) {\n                    if (a.node) {\n                        if (a.node.levelDynamic >\n                            b.node.levelDynamic) {\n                            return 1;\n                        }\n                        if (a.node.levelDynamic <\n                            b.node.levelDynamic) {\n                            return -1;\n                        }\n                    }\n                    return 0;\n                });\n            }\n            points.forEach(function (point) {\n                var plotY = point.plotY;\n                var swidth,\n                    pointAttr;\n                if (typeof plotY !== 'undefined' &&\n                    !isNaN(plotY) &&\n                    point.y !== null &&\n                    point.shapeArgs) {\n                    var _a = point.shapeArgs,\n                        _b = _a.x,\n                        x_1 = _b === void 0 ? 0 : _b,\n                        _c = _a.y,\n                        y_1 = _c === void 0 ? 0 : _c,\n                        _d = _a.width,\n                        width = _d === void 0 ? 0 : _d,\n                        _e = _a.height,\n                        height = _e === void 0 ? 0 : _e;\n                    pointAttr = chart.styledMode ?\n                        point.series\n                            .colorAttribs(point) :\n                        pointAttr = point.series.pointAttribs(point);\n                    swidth = pointAttr['stroke-width'] || 0;\n                    // Handle point colors\n                    pcolor = color(pointAttr.fill).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                    // So there are two ways of doing this. Either we can\n                    // create a rectangle of two triangles, or we can do a\n                    // point and use point size. Latter is faster, but\n                    // only supports squares. So we're doing triangles.\n                    // We could also use one color per. vertice to get\n                    // better color interpolation.\n                    // If there's stroking, we do an additional rect\n                    if (series.is('treemap')) {\n                        swidth = swidth || 1;\n                        scolor = color(pointAttr.stroke).rgba;\n                        scolor[0] /= 255.0;\n                        scolor[1] /= 255.0;\n                        scolor[2] /= 255.0;\n                        pushRect(x_1, y_1, width, height, scolor);\n                        swidth /= 2;\n                    }\n                    // } else {\n                    //     swidth = 0;\n                    // }\n                    // Fixes issues with inverted heatmaps (see #6981). The root\n                    // cause is that the coordinate system is flipped. In other\n                    // words, instead of [0,0] being top-left, it's\n                    // bottom-right. This causes a vertical and horizontal flip\n                    // in the resulting image, making it rotated 180 degrees.\n                    if (series.is('heatmap') && chart.inverted) {\n                        x_1 = xAxis.len - x_1;\n                        y_1 = yAxis.len - y_1;\n                        width = -width;\n                        height = -height;\n                    }\n                    pushRect(x_1 + swidth, y_1 + swidth, width - (swidth * 2), height - (swidth * 2), pcolor);\n                }\n            });\n            closeSegment();\n            return;\n        }\n        var _loop_1 = function () {\n                d = sdata[++i];\n            if (typeof d === 'undefined') {\n                return \"continue\";\n            }\n            /// px = x = y = z = nx = low = false;\n            // chartDestroyed = typeof chart.index === 'undefined';\n            // nextInside = prevInside = pcolor = isXInside = isYInside = false;\n            // drawAsBar = asBar[series.type];\n            if (chartDestroyed) {\n                return \"break\";\n            }\n            // Uncomment this to enable color by point.\n            // This currently left disabled as the charts look really ugly\n            // when enabled and there's a lot of points.\n            // Leaving in for the future (tm).\n            // if (colorByPoint) {\n            //     colorIndex = ++colorIndex %\n            //         series.chart.options.colors.length;\n            //     pcolor = toRGBAFast(series.chart.options.colors[colorIndex]);\n            //     pcolor[0] /= 255.0;\n            //     pcolor[1] /= 255.0;\n            //     pcolor[2] /= 255.0;\n            // }\n            // Handle the point.color option (#5999)\n            var pointOptions = rawData && rawData[i];\n            if (!useRaw && isObject(pointOptions, true)) {\n                if (pointOptions.color) {\n                    pcolor = color(pointOptions.color).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                }\n            }\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1][0];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1][0];\n                }\n                if (d.length >= 3) {\n                    z = d[2];\n                    if (d[2] > inst.zMax) {\n                        inst.zMax = d[2];\n                    }\n                    if (d[2] < inst.zMin) {\n                        inst.zMin = d[2];\n                    }\n                }\n            }\n            else {\n                x = d;\n                y = yData === null || yData === void 0 ? void 0 : yData[i];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1];\n                }\n                if (zData && zData.length) {\n                    z = zData[i];\n                    if (zData[i] > inst.zMax) {\n                        inst.zMax = zData[i];\n                    }\n                    if (zData[i] < inst.zMin) {\n                        inst.zMin = zData[i];\n                    }\n                }\n            }\n            if (!connectNulls && (x === null || y === null)) {\n                beginSegment();\n                return \"continue\";\n            }\n            if (nx && nx >= xMin && nx <= xMax) {\n                nextInside = true;\n            }\n            if (px && px >= xMin && px <= xMax) {\n                prevInside = true;\n            }\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = (_a = series.getColumn('low', true)) === null || _a === void 0 ? void 0 : _a[i];\n                y = ((_b = series.getColumn('high', true)) === null || _b === void 0 ? void 0 : _b[i]) || 0;\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n            }\n            if (yMin !== null &&\n                typeof yMin !== 'undefined' &&\n                yMax !== null &&\n                typeof yMax !== 'undefined') {\n                isYInside = y >= yMin && y <= yMax;\n            }\n            // Do not render points outside the zoomed range (#19701)\n            if (!sorted && !isYInside) {\n                return \"continue\";\n            }\n            if (x > xMax && closestRight.x < xMax) {\n                closestRight.x = x;\n                closestRight.y = y;\n            }\n            if (x < xMin && closestLeft.x > xMin) {\n                closestLeft.x = x;\n                closestLeft.y = y;\n            }\n            if (y === null && connectNulls) {\n                return \"continue\";\n            }\n            // Cull points outside the extremes\n            // Continue if `sdata` has only one point as `nextInside` asserts\n            // whether the next point exists and will thus be false. (#22194)\n            if (y === null || (!isYInside && sdata.length > 1 &&\n                !nextInside && !prevInside)) {\n                beginSegment();\n                return \"continue\";\n            }\n            // The first point before and first after extremes should be\n            // rendered (#9962, 19701)\n            // Make sure series with a single point are rendered (#21897)\n            if (sorted && ((nx >= xMin || x >= xMin) &&\n                (px <= xMax || x <= xMax)) ||\n                !sorted && ((x >= xMin) && (x <= xMax))) {\n                isXInside = true;\n            }\n            if (!isXInside && !nextInside && !prevInside) {\n                return \"continue\";\n            }\n            if (gapSize && x - px > gapSize) {\n                beginSegment();\n            }\n            // Note: Boost requires that zones are sorted!\n            if (zones) {\n                var zoneColor_1;\n                zones.some(function (// eslint-disable-line no-loop-func\n                zone, i) {\n                    var last = zones[i - 1];\n                    if (zoneAxis === 'x') {\n                        if (typeof zone.value !== 'undefined' &&\n                            x <= zone.value) {\n                            if (zoneColors[i] &&\n                                (!last || x >= last.value)) {\n                                zoneColor_1 = zoneColors[i];\n                            }\n                            return true;\n                        }\n                        return false;\n                    }\n                    if (typeof zone.value !== 'undefined' && y <= zone.value) {\n                        if (zoneColors[i] &&\n                            (!last || y >= last.value)) {\n                            zoneColor_1 = zoneColors[i];\n                        }\n                        return true;\n                    }\n                    return false;\n                });\n                pcolor = zoneColor_1 || zoneDefColor || pcolor;\n            }\n            // Skip translations - temporary floating point fix\n            if (!settings.useGPUTranslations) {\n                inst.skipTranslation = true;\n                x = xAxis.toPixels(x, true);\n                y = yAxis.toPixels(y, true);\n                // Make sure we're not drawing outside of the chart area.\n                // See #6594. Update: this is no longer required as far as I\n                // can tell. Leaving in for git blame in case there are edge\n                // cases I've not found. Having this in breaks #10246.\n                // if (y > plotHeight) {\n                // y = plotHeight;\n                // }\n                if (x > plotWidth) {\n                    // If this is rendered as a point, just skip drawing it\n                    // entirely, as we're not dependant on lineTo'ing to it.\n                    // See #8197\n                    if (inst.drawMode === 'POINTS') {\n                        return \"continue\";\n                    }\n                    // Having this here will clamp markers and make the angle\n                    // of the last line wrong. See 9166.\n                    // x = plotWidth;\n                }\n            }\n            // No markers on out of bounds things.\n            // Out of bound things are shown if and only if the next\n            // or previous point is inside the rect.\n            if (inst.hasMarkers && isXInside) {\n                /// x = Highcharts.correctFloat(\n                //     Math.min(Math.max(-1e5, xAxis.translate(\n                //         x,\n                //         0,\n                //         0,\n                //         0,\n                //         1,\n                //         0.5,\n                //         false\n                //     )), 1e5)\n                // );\n                if (lastX !== false) {\n                    series.closestPointRangePx = Math.min(series.closestPointRangePx, Math.abs(x - lastX));\n                }\n            }\n            // If the last _drawn_ point is closer to this point than the\n            // threshold, skip it. Shaves off 20-100ms in processing.\n            if (!settings.useGPUTranslations &&\n                !settings.usePreallocated &&\n                (lastX && Math.abs(x - lastX) < cullXThreshold) &&\n                (lastY && Math.abs(y - lastY) < cullYThreshold)) {\n                if (settings.debug.showSkipSummary) {\n                    ++skipped;\n                }\n                return \"continue\";\n            }\n            if (drawAsBar) {\n                minVal = low || 0;\n                if (low === false || typeof low === 'undefined') {\n                    if (y < 0) {\n                        minVal = y;\n                    }\n                    else {\n                        minVal = 0;\n                    }\n                }\n                if ((!isRange && !isStacked) ||\n                    yAxis.logarithmic // #16850\n                ) {\n                    minVal = Math.max(threshold === null ? yMin : threshold, // #5268\n                    yMin); // #8731\n                }\n                if (!settings.useGPUTranslations) {\n                    minVal = yAxis.toPixels(minVal, true);\n                }\n                // Need to add an extra point here\n                vertice(x, minVal, 0, 0, pcolor);\n            }\n            // Do step line if enabled.\n            // Draws an additional point at the old Y at the new X.\n            // See #6976.\n            if (options.step && !firstPoint) {\n                vertice(x, lastY, 0, 2, pcolor);\n            }\n            vertice(x, y, 0, series.type === 'bubble' ? (z || 1) : 2, pcolor);\n            // Uncomment this to support color axis.\n            // if (caxis) {\n            //     pcolor = color(caxis.toColor(y)).rgba;\n            //     inst.colorData.push(color[0] / 255.0);\n            //     inst.colorData.push(color[1] / 255.0);\n            //     inst.colorData.push(color[2] / 255.0);\n            //     inst.colorData.push(color[3]);\n            // }\n            lastX = x;\n            lastY = y;\n            hadPoints = true;\n            firstPoint = false;\n        };\n        // Extract color axis\n        // (chart.axes || []).forEach((a): void => {\n        //     if (H.ColorAxis && a instanceof H.ColorAxis) {\n        //         caxis = a;\n        //     }\n        // });\n        while (i < sdata.length - 1) {\n            var state_1 = _loop_1();\n            if (state_1 === \"break\")\n                break;\n        }\n        if (settings.debug.showSkipSummary) {\n            console.log('skipped points:', skipped); // eslint-disable-line no-console\n        }\n        var pushSupplementPoint = function (point,\n            atStart) {\n                if (!settings.useGPUTranslations) {\n                    inst.skipTranslation = true;\n                point.x = xAxis.toPixels(point.x, true);\n                point.y = yAxis.toPixels(point.y, true);\n            }\n            // We should only do this for lines, and we should ignore markers\n            // since there's no point here that would have a marker.\n            if (atStart) {\n                _this.data = [point.x, point.y, 0, 2].concat(_this.data);\n                return;\n            }\n            vertice(point.x, point.y, 0, 2);\n        };\n        if (!hadPoints &&\n            connectNulls !== false &&\n            series.drawMode === 'line_strip') {\n            if (closestLeft.x < Number.MAX_VALUE) {\n                // We actually need to push this *before* the complete buffer.\n                pushSupplementPoint(closestLeft, true);\n            }\n            if (closestRight.x > -Number.MAX_VALUE) {\n                pushSupplementPoint(closestRight);\n            }\n        }\n        closeSegment();\n    };\n    /**\n     * Push a series to the renderer\n     * If we render the series immediately, we don't have to loop later\n     * @private\n     * @param {Highchart.Series} s\n     * The series to push.\n     */\n    WGLRenderer.prototype.pushSeries = function (s) {\n        var markerData = this.markerData,\n            series = this.series,\n            settings = this.settings;\n        if (series.length > 0) {\n            if (series[series.length - 1].hasMarkers) {\n                series[series.length - 1].markerTo = markerData.length;\n            }\n        }\n        if (settings.debug.timeSeriesProcessing) {\n            console.time('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n        var obj = {\n                segments: [],\n                markerFrom: markerData.length,\n                // Push RGBA values to this array to use per. point coloring.\n                // It should be 0-padded, so each component should be pushed in\n                // succession.\n                colorData: [],\n                series: s,\n                zMin: Number.MAX_VALUE,\n                zMax: -Number.MAX_VALUE,\n                hasMarkers: s.options.marker ?\n                    s.options.marker.enabled !== false :\n                    false,\n                showMarkers: true,\n                drawMode: Boost_WGLDrawMode[s.type] || 'LINE_STRIP'\n            };\n        if (s.index >= series.length) {\n            series.push(obj);\n        }\n        else {\n            series[s.index] = obj;\n        }\n        // Add the series data to our buffer(s)\n        this.pushSeriesData(s, obj);\n        if (settings.debug.timeSeriesProcessing) {\n            console.timeEnd('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n    };\n    /**\n     * Flush the renderer.\n     * This removes pushed series and vertices.\n     * Should be called after clearing and before rendering\n     * @private\n     */\n    WGLRenderer.prototype.flush = function () {\n        var vbuffer = this.vbuffer;\n        this.data = [];\n        this.markerData = [];\n        this.series = [];\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n    };\n    /**\n     * Pass x-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The x-axis.\n     */\n    WGLRenderer.prototype.setXAxis = function (axis) {\n        var shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        var pixelRatio = this.getPixelRatio();\n        shader.setUniform('xAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('xAxisMin', axis.min);\n        shader.setUniform('xAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('xAxisPointRange', axis.pointRange);\n        shader.setUniform('xAxisLen', axis.len * pixelRatio);\n        shader.setUniform('xAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('xAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('xAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('xAxisReversed', (!!axis.reversed));\n    };\n    /**\n     * Pass y-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The y-axis.\n     */\n    WGLRenderer.prototype.setYAxis = function (axis) {\n        var shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        var pixelRatio = this.getPixelRatio();\n        shader.setUniform('yAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('yAxisMin', axis.min);\n        shader.setUniform('yAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('yAxisPointRange', axis.pointRange);\n        shader.setUniform('yAxisLen', axis.len * pixelRatio);\n        shader.setUniform('yAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('yAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('yAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('yAxisReversed', (!!axis.reversed));\n    };\n    /**\n     * Set the translation threshold\n     * @private\n     * @param {boolean} has\n     * Has threshold flag.\n     * @param {numbe} translation\n     * The threshold.\n     */\n    WGLRenderer.prototype.setThreshold = function (has, translation) {\n        var shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        shader.setUniform('hasThreshold', has);\n        shader.setUniform('translatedThreshold', translation);\n    };\n    /**\n     * Render the data\n     * This renders all pushed series.\n     * @private\n     */\n    WGLRenderer.prototype.renderChart = function (chart) {\n        var _this = this;\n        var gl = this.gl,\n            settings = this.settings,\n            shader = this.shader,\n            vbuffer = this.vbuffer;\n        var pixelRatio = this.getPixelRatio();\n        if (chart) {\n            this.width = chart.chartWidth * pixelRatio;\n            this.height = chart.chartHeight * pixelRatio;\n        }\n        else {\n            return false;\n        }\n        var height = this.height,\n            width = this.width;\n        if (!gl || !shader || !width || !height) {\n            return false;\n        }\n        if (settings.debug.timeRendering) {\n            console.time('gl rendering'); // eslint-disable-line no-console\n        }\n        gl.canvas.width = width;\n        gl.canvas.height = height;\n        shader.bind();\n        gl.viewport(0, 0, width, height);\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n        if (settings.lineWidth > 1 && !(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS) {\n            gl.lineWidth(settings.lineWidth);\n        }\n        if (vbuffer) {\n            vbuffer.build(this.data, 'aVertexPosition', 4);\n            vbuffer.bind();\n        }\n        shader.setInverted(chart.inverted);\n        // Render the series\n        this.series.forEach(function (s, si) {\n            var _a,\n                _b,\n                _c;\n            var options = s.series.options,\n                shapeOptions = options.marker,\n                lineWidth = (typeof options.lineWidth !== 'undefined' ?\n                    options.lineWidth :\n                    1),\n                threshold = options.threshold,\n                hasThreshold = isNumber(threshold),\n                yBottom = s.series.yAxis.getThreshold(threshold),\n                translatedThreshold = yBottom,\n                showMarkers = WGLRenderer_pick(options.marker ? options.marker.enabled : null,\n                s.series.xAxis.isRadial ? true : null,\n                s.series.closestPointRangePx >\n                    2 * ((options.marker ?\n                        options.marker.radius :\n                        10) || 10)),\n                shapeTexture = _this.textureHandles[(shapeOptions && shapeOptions.symbol) ||\n                    s.series.symbol] || _this.textureHandles.circle;\n            var sindex,\n                cbuffer,\n                fillColor,\n                scolor = [];\n            if (s.segments.length === 0 ||\n                s.segments[0].from === s.segments[0].to) {\n                return;\n            }\n            if (shapeTexture.isReady) {\n                gl.bindTexture(gl.TEXTURE_2D, shapeTexture.handle);\n                shader.setTexture(shapeTexture.handle);\n            }\n            if (chart.styledMode) {\n                if (s.series.markerGroup === ((_a = s.series.chart.boost) === null || _a === void 0 ? void 0 : _a.markerGroup)) {\n                    // Create a temporary markerGroup to get the fill color\n                    delete s.series.markerGroup;\n                    s.series.markerGroup = s.series.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n                    fillColor = s.series.markerGroup.getStyle('fill');\n                    s.series.markerGroup.destroy();\n                    s.series.markerGroup = (_b = s.series.chart.boost) === null || _b === void 0 ? void 0 : _b.markerGroup;\n                }\n                else {\n                    fillColor = (_c = s.series.markerGroup) === null || _c === void 0 ? void 0 : _c.getStyle('fill');\n                }\n            }\n            else {\n                fillColor =\n                    (s.drawMode === 'POINTS' && // #14260\n                        s.series.pointAttribs &&\n                        s.series.pointAttribs().fill) ||\n                        s.series.color;\n                if (options.colorByPoint) {\n                    fillColor = s.series.chart.options.colors[si];\n                }\n            }\n            if (s.series.fillOpacity && options.fillOpacity) {\n                fillColor = new (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default())(fillColor).setOpacity(WGLRenderer_pick(options.fillOpacity, 1.0)).get();\n            }\n            scolor = color(fillColor).rgba;\n            if (!settings.useAlpha) {\n                scolor[3] = 1.0;\n            }\n            // Blending\n            if (options.boostBlending === 'add') {\n                gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n                gl.blendEquation(gl.FUNC_ADD);\n            }\n            else if (options.boostBlending === 'mult' ||\n                options.boostBlending === 'multiply') {\n                gl.blendFunc(gl.DST_COLOR, gl.ZERO);\n            }\n            else if (options.boostBlending === 'darken') {\n                gl.blendFunc(gl.ONE, gl.ONE);\n                gl.blendEquation(gl.FUNC_MIN);\n            }\n            else {\n                /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n                // gl.blendEquation(gl.FUNC_ADD);\n                gl.blendFuncSeparate(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA);\n            }\n            shader.reset();\n            // If there are entries in the colorData buffer, build and bind it.\n            if (s.colorData.length > 0) {\n                shader.setUniform('hasColor', 1);\n                cbuffer = new Boost_WGLVertexBuffer(gl, shader);\n                cbuffer.build(\n                // The color array attribute for vertex is assigned from 0,\n                // so it needs to be shifted to be applied to further\n                // segments. #18858\n                Array(s.segments[0].from).concat(s.colorData), 'aColor', 4);\n                cbuffer.bind();\n            }\n            else {\n                // Set the hasColor uniform to false (0) when the series\n                // contains no colorData buffer points. #18858\n                shader.setUniform('hasColor', 0);\n                // #15869, a buffer with fewer points might already be bound by\n                // a different series/chart causing out of range errors\n                gl.disableVertexAttribArray(gl.getAttribLocation(shader.getProgram(), 'aColor'));\n            }\n            // Set series specific uniforms\n            shader.setColor(scolor);\n            _this.setXAxis(s.series.xAxis);\n            _this.setYAxis(s.series.yAxis);\n            _this.setThreshold(hasThreshold, translatedThreshold);\n            if (s.drawMode === 'POINTS') {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 0.5) * 2 * pixelRatio);\n            }\n            // If set to true, the toPixels translations in the shader\n            // is skipped, i.e it's assumed that the value is a pixel coord.\n            shader.setSkipTranslation(s.skipTranslation);\n            if (s.series.type === 'bubble') {\n                shader.setBubbleUniforms(s.series, s.zMin, s.zMax, pixelRatio);\n            }\n            shader.setDrawAsCircle(asCircle[s.series.type] || false);\n            if (!vbuffer) {\n                return;\n            }\n            // Do the actual rendering\n            // If the line width is < 0, skip rendering of the lines. See #7833.\n            if (lineWidth > 0 || s.drawMode !== 'LINE_STRIP') {\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, s.drawMode);\n                }\n            }\n            if (s.hasMarkers && showMarkers) {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 5) * 2 * pixelRatio);\n                shader.setDrawAsCircle(true);\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, 'POINTS');\n                }\n            }\n        });\n        if (settings.debug.timeRendering) {\n            console.timeEnd('gl rendering'); // eslint-disable-line no-console\n        }\n        if (this.postRenderCallback) {\n            this.postRenderCallback(this);\n        }\n        this.flush();\n    };\n    /**\n     * Render the data when ready\n     * @private\n     */\n    WGLRenderer.prototype.render = function (chart) {\n        var _this = this;\n        this.clear();\n        if (chart.renderer.forExport) {\n            return this.renderChart(chart);\n        }\n        if (this.isInited) {\n            this.renderChart(chart);\n        }\n        else {\n            setTimeout(function () {\n                _this.render(chart);\n            }, 1);\n        }\n    };\n    /**\n     * Set the viewport size in pixels\n     * Creates an orthographic perspective matrix and applies it.\n     * @private\n     */\n    WGLRenderer.prototype.setSize = function (width, height) {\n        var shader = this.shader;\n        // Skip if there's no change, or if we have no valid shader\n        if (!shader || (this.width === width && this.height === height)) {\n            return;\n        }\n        this.width = width;\n        this.height = height;\n        shader.bind();\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n    };\n    /**\n     * Init OpenGL\n     * @private\n     */\n    WGLRenderer.prototype.init = function (canvas, noFlush) {\n        var _this = this;\n        var settings = this.settings;\n        this.isInited = false;\n        if (!canvas) {\n            return false;\n        }\n        if (settings.debug.timeSetup) {\n            console.time('gl setup'); // eslint-disable-line no-console\n        }\n        for (var i = 0; i < contexts.length; ++i) {\n            this.gl = canvas.getContext(contexts[i], {\n            //    /premultipliedAlpha: false\n            });\n            if (this.gl) {\n                break;\n            }\n        }\n        var gl = this.gl;\n        if (gl) {\n            if (!noFlush) {\n                this.flush();\n            }\n        }\n        else {\n            return false;\n        }\n        gl.enable(gl.BLEND);\n        /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n        gl.disable(gl.DEPTH_TEST);\n        /// gl.depthMask(gl.FALSE);\n        gl.depthFunc(gl.LESS);\n        var shader = this.shader = new Boost_WGLShader(gl);\n        if (!shader) {\n            // We need to abort, there's no shader context\n            return false;\n        }\n        this.vbuffer = new Boost_WGLVertexBuffer(gl, shader);\n        var createTexture = function (name,\n            fn) {\n                var props = {\n                    isReady: false,\n                    texture: doc.createElement('canvas'),\n                    handle: gl.createTexture()\n                },\n            ctx = props.texture.getContext('2d');\n            _this.textureHandles[name] = props;\n            props.texture.width = 512;\n            props.texture.height = 512;\n            ctx.mozImageSmoothingEnabled = false;\n            ctx.webkitImageSmoothingEnabled = false;\n            ctx.msImageSmoothingEnabled = false;\n            ctx.imageSmoothingEnabled = false;\n            ctx.strokeStyle = 'rgba(255, 255, 255, 0)';\n            ctx.fillStyle = '#FFF';\n            fn(ctx);\n            try {\n                gl.activeTexture(gl.TEXTURE0);\n                gl.bindTexture(gl.TEXTURE_2D, props.handle);\n                /// gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, true);\n                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, props.texture);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);\n                /// gl.generateMipmap(gl.TEXTURE_2D);\n                gl.bindTexture(gl.TEXTURE_2D, null);\n                props.isReady = true;\n            }\n            catch (e) {\n                // Silent error\n            }\n        };\n        // Circle shape\n        createTexture('circle', function (ctx) {\n            ctx.beginPath();\n            ctx.arc(256, 256, 256, 0, 2 * Math.PI);\n            ctx.stroke();\n            ctx.fill();\n        });\n        // Square shape\n        createTexture('square', function (ctx) {\n            ctx.fillRect(0, 0, 512, 512);\n        });\n        // Diamond shape\n        createTexture('diamond', function (ctx) {\n            ctx.beginPath();\n            ctx.moveTo(256, 0);\n            ctx.lineTo(512, 256);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(0, 256);\n            ctx.lineTo(256, 0);\n            ctx.fill();\n        });\n        // Triangle shape\n        createTexture('triangle', function (ctx) {\n            ctx.beginPath();\n            ctx.moveTo(0, 512);\n            ctx.lineTo(256, 0);\n            ctx.lineTo(512, 512);\n            ctx.lineTo(0, 512);\n            ctx.fill();\n        });\n        // Triangle shape (rotated)\n        createTexture('triangle-down', function (ctx) {\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(512, 0);\n            ctx.lineTo(0, 0);\n            ctx.fill();\n        });\n        this.isInited = true;\n        if (settings.debug.timeSetup) {\n            console.timeEnd('gl setup'); // eslint-disable-line no-console\n        }\n        return true;\n    };\n    /**\n     * @private\n     * @todo use it\n     */\n    WGLRenderer.prototype.destroy = function () {\n        var gl = this.gl,\n            shader = this.shader,\n            vbuffer = this.vbuffer;\n        this.flush();\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n        if (shader) {\n            shader.destroy();\n        }\n        if (gl) {\n            objectEach(this.textureHandles, function (texture) {\n                if (texture.handle) {\n                    gl.deleteTexture(texture.handle);\n                }\n            });\n            gl.canvas.width = 1;\n            gl.canvas.height = 1;\n        }\n    };\n    return WGLRenderer;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLRenderer = (WGLRenderer);\n\n;// ./code/es5/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items) {\n        if (items === void 0) { items = []; }\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice.apply(column, __spreadArray([start, deleteCount], items, false)),\n                array: column\n            };\n        }\n        var Constructor = Object.getPrototypeOf(column)\n                .constructor;\n        var removed = column[removedAsSubarray ? 'subarray' : 'slice'](start,\n            start + deleteCount);\n        var newLength = column.length - deleteCount + items.length;\n        var result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es5/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nvar setLength = Data_ColumnUtils.setLength, splice = Data_ColumnUtils.splice;\n\nvar fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, DataTableCore_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nvar DataTableCore = /** @class */ (function () {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    function DataTableCore(options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        var rowCount = 0;\n        DataTableCore_objectEach(options.columns || {}, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    DataTableCore.prototype.applyRowCount = function (rowCount) {\n        var _this = this;\n        this.rowCount = rowCount;\n        DataTableCore_objectEach(this.columns, function (column, columnName) {\n            if (column.length !== rowCount) {\n                _this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    };\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    DataTableCore.prototype.deleteRows = function (rowIndex, rowCount) {\n        var _this = this;\n        if (rowCount === void 0) { rowCount = 1; }\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            var length_1 = 0;\n            DataTableCore_objectEach(this.columns, function (column, columnName) {\n                _this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length_1 = column.length;\n            });\n            this.rowCount = length_1;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex: rowIndex, rowCount: rowCount });\n        this.versionTag = uniqueKey();\n    };\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getColumn = function (columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    };\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    DataTableCore.prototype.getColumns = function (columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).reduce(function (columns, columnName) {\n            columns[columnName] = _this.columns[columnName];\n            return columns;\n        }, {});\n    };\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getRow = function (rowIndex, columnNames) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).map(function (key) { var _a; return (_a = _this.columns[key]) === null || _a === void 0 ? void 0 : _a[rowIndex]; });\n    };\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumn = function (columnName, column, rowIndex, eventDetail) {\n        var _a;\n        if (column === void 0) { column = []; }\n        if (rowIndex === void 0) { rowIndex = 0; }\n        this.setColumns((_a = {}, _a[columnName] = column, _a), rowIndex, eventDetail);\n    };\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumns = function (columns, rowIndex, eventDetail) {\n        var _this = this;\n        var rowCount = this.rowCount;\n        DataTableCore_objectEach(columns, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    };\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    DataTableCore.prototype.setRow = function (row, rowIndex, insert, eventDetail) {\n        if (rowIndex === void 0) { rowIndex = this.rowCount; }\n        var columns = this.columns,\n            indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        DataTableCore_objectEach(row, function (cellValue, columnName) {\n            var column = columns[columnName] ||\n                    (eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.addColumns) !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    };\n    return DataTableCore;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Extensions/Boost/BoostSeries.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nvar BoostSeries_getBoostClipRect = Boost_BoostChart.getBoostClipRect, BoostSeries_isChartSeriesBoosting = Boost_BoostChart.isChartSeriesBoosting;\n\nvar getOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getOptions;\n\nvar BoostSeries_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, BoostSeries_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop, BoostSeries_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\nvar BoostSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, destroyObjectProperties = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).destroyObjectProperties, BoostSeries_error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, BoostSeries_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, BoostSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, BoostSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, BoostSeries_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined;\n\n\n/* *\n *\n *  Constants\n *\n * */\nvar CHUNK_SIZE = 3000;\n/* *\n *\n *  Variables\n *\n * */\nvar index, mainCanvas;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction allocateIfNotSeriesBoosting(renderer, series) {\n    var boost = series.boost;\n    if (renderer &&\n        boost &&\n        boost.target &&\n        boost.canvas &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        renderer.allocateBufferForSingleSeries(series);\n    }\n}\n/**\n * Return true if ths boost.enabled option is true\n *\n * @private\n * @param {Highcharts.Chart} chart\n * The chart\n * @return {boolean}\n * True, if boost is enabled.\n */\nfunction boostEnabled(chart) {\n    return BoostSeries_pick((chart &&\n        chart.options &&\n        chart.options.boost &&\n        chart.options.boost.enabled), true);\n}\n/**\n * @private\n */\nfunction BoostSeries_compose(SeriesClass, seriesTypes, PointClass, wglMode) {\n    if (BoostSeries_pushUnique(BoostSeries_composed, 'Boost.Series')) {\n        var plotOptions_1 = getOptions().plotOptions,\n            seriesProto_1 = SeriesClass.prototype;\n        BoostSeries_addEvent(SeriesClass, 'destroy', onSeriesDestroy);\n        BoostSeries_addEvent(SeriesClass, 'hide', onSeriesHide);\n        if (wglMode) {\n            seriesProto_1.renderCanvas = seriesRenderCanvas;\n        }\n        wrap(seriesProto_1, 'getExtremes', wrapSeriesGetExtremes);\n        wrap(seriesProto_1, 'processData', wrapSeriesProcessData);\n        wrap(seriesProto_1, 'searchPoint', wrapSeriesSearchPoint);\n        [\n            'translate',\n            'generatePoints',\n            'drawTracker',\n            'drawPoints',\n            'render'\n        ].forEach(function (method) {\n            return wrapSeriesFunctions(seriesProto_1, seriesTypes, method);\n        });\n        wrap(PointClass.prototype, 'firePointEvent', function (proceed, type, e) {\n            var _a,\n                _b;\n            if (type === 'click' && this.series.boosted) {\n                var point = e.point;\n                if ((point.dist || point.distX) >= ((_b = (_a = point.series.options.marker) === null || _a === void 0 ? void 0 : _a.radius) !== null && _b !== void 0 ? _b : 10)) {\n                    return;\n                }\n            }\n            return proceed.apply(this, [].slice.call(arguments, 1));\n        });\n        // Set default options\n        Boost_Boostables.forEach(function (type) {\n            var typePlotOptions = plotOptions_1[type];\n            if (typePlotOptions) {\n                typePlotOptions.boostThreshold = 5000;\n                typePlotOptions.boostData = [];\n                seriesTypes[type].prototype.fillOpacity = true;\n            }\n        });\n        if (wglMode) {\n            var AreaSeries = seriesTypes.area,\n                AreaSplineSeries = seriesTypes.areaspline,\n                BubbleSeries = seriesTypes.bubble,\n                ColumnSeries = seriesTypes.column,\n                HeatmapSeries = seriesTypes.heatmap,\n                ScatterSeries = seriesTypes.scatter,\n                TreemapSeries = seriesTypes.treemap;\n            if (AreaSeries) {\n                extend(AreaSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (AreaSplineSeries) {\n                extend(AreaSplineSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (BubbleSeries) {\n                var bubbleProto_1 = BubbleSeries.prototype;\n                // By default, the bubble series does not use the KD-tree, so\n                // force it to.\n                delete bubbleProto_1.buildKDTree;\n                // SeriesTypes.bubble.prototype.directTouch = false;\n                // Needed for markers to work correctly\n                wrap(bubbleProto_1, 'markerAttribs', function (proceed) {\n                    if (this.boosted) {\n                        return false;\n                    }\n                    return proceed.apply(this, [].slice.call(arguments, 1));\n                });\n            }\n            if (ColumnSeries) {\n                extend(ColumnSeries.prototype, {\n                    fill: true,\n                    sampling: true\n                });\n            }\n            if (ScatterSeries) {\n                ScatterSeries.prototype.fill = true;\n            }\n            // We need to handle heatmaps separately, since we can't perform the\n            // size/color calculations in the shader easily.\n            // @todo This likely needs future optimization.\n            [HeatmapSeries, TreemapSeries].forEach(function (SC) {\n                if (SC) {\n                    wrap(SC.prototype, 'drawPoints', wrapSeriesDrawPoints);\n                }\n            });\n        }\n    }\n    return SeriesClass;\n}\n/**\n * Create a canvas + context and attach it to the target\n *\n * @private\n * @function createAndAttachRenderer\n *\n * @param {Highcharts.Chart} chart\n * the chart\n *\n * @param {Highcharts.Series} series\n * the series\n *\n * @return {Highcharts.BoostGLRenderer}\n * the canvas renderer\n */\nfunction createAndAttachRenderer(chart, series) {\n    var _a,\n        _b,\n        _c;\n    var ChartClass = chart.constructor,\n        targetGroup = chart.seriesGroup || series.group,\n        alpha = 1;\n    var width = chart.chartWidth,\n        height = chart.chartHeight,\n        target = chart,\n        foSupported = typeof SVGForeignObjectElement !== 'undefined',\n        hasClickHandler = false;\n    if (BoostSeries_isChartSeriesBoosting(chart)) {\n        target = chart;\n    }\n    else {\n        target = series;\n        hasClickHandler = Boolean(((_a = series.options.events) === null || _a === void 0 ? void 0 : _a.click) ||\n            ((_c = (_b = series.options.point) === null || _b === void 0 ? void 0 : _b.events) === null || _c === void 0 ? void 0 : _c.click));\n    }\n    var boost = target.boost =\n            target.boost ||\n                {};\n    // Support for foreignObject is flimsy as best.\n    // IE does not support it, and Chrome has a bug which messes up\n    // the canvas draw order.\n    // As such, we force the Image fallback for now, but leaving the\n    // actual Canvas path in-place in case this changes in the future.\n    foSupported = false;\n    if (!mainCanvas) {\n        mainCanvas = BoostSeries_doc.createElement('canvas');\n    }\n    if (!boost.target) {\n        boost.canvas = mainCanvas;\n        // Fall back to image tag if foreignObject isn't supported,\n        // or if we're exporting.\n        if (chart.renderer.forExport || !foSupported) {\n            target.renderTarget = boost.target = chart.renderer.image('', 0, 0, width, height)\n                .addClass('highcharts-boost-canvas')\n                .add(targetGroup);\n            boost.clear = function () {\n                boost.target.attr({\n                    // Insert a blank pixel (#17182)\n                    /* eslint-disable-next-line max-len*/\n                    href: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='\n                });\n            };\n            boost.copy = function () {\n                boost.resize();\n                boost.target.attr({\n                    href: boost.canvas.toDataURL('image/png')\n                });\n            };\n        }\n        else {\n            boost.targetFo = chart.renderer\n                .createElement('foreignObject')\n                .add(targetGroup);\n            target.renderTarget = boost.target =\n                BoostSeries_doc.createElement('canvas');\n            boost.targetCtx = boost.target.getContext('2d');\n            boost.targetFo.element.appendChild(boost.target);\n            boost.clear = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n            };\n            boost.copy = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n                boost.targetCtx.drawImage(boost.canvas, 0, 0);\n            };\n        }\n        boost.resize = function () {\n            var _a,\n                _b;\n            width = chart.chartWidth;\n            height = chart.chartHeight;\n            (boost.targetFo || boost.target)\n                .attr({\n                x: 0,\n                y: 0,\n                width: width,\n                height: height\n            })\n                .css({\n                pointerEvents: hasClickHandler ? void 0 : 'none',\n                mixedBlendMode: 'normal',\n                opacity: alpha\n            })\n                .addClass(hasClickHandler ? 'highcharts-tracker' : '');\n            if (target instanceof ChartClass) {\n                (_b = (_a = target.boost) === null || _a === void 0 ? void 0 : _a.markerGroup) === null || _b === void 0 ? void 0 : _b.translate(chart.plotLeft, chart.plotTop);\n            }\n        };\n        boost.clipRect = chart.renderer.clipRect();\n        (boost.targetFo || boost.target)\n            .attr({\n            // Set the z index of the boost target to that of the last\n            // series using it. This logic is not perfect, as it will not\n            // handle interleaved series with boost enabled or disabled. But\n            // it will cover the most common use case of one or more\n            // successive boosted or non-boosted series (#9819).\n            zIndex: series.options.zIndex\n        });\n        if (target instanceof ChartClass) {\n            target.boost.markerGroup = target.renderer\n                .g()\n                .add(targetGroup)\n                .translate(series.xAxis.pos, series.yAxis.pos);\n        }\n    }\n    boost.canvas.width = width;\n    boost.canvas.height = height;\n    if (boost.clipRect) {\n        var box = BoostSeries_getBoostClipRect(chart,\n            target), \n            // When using panes, the image itself must be clipped. When not\n            // using panes, it is better to clip the target group, because then\n            // we preserve clipping on touch- and mousewheel zoom preview.\n            clippedElement = (box.width === chart.clipBox.width &&\n                box.height === chart.clipBox.height) ? targetGroup :\n                (boost.targetFo || boost.target);\n        boost.clipRect.attr(box);\n        clippedElement === null || clippedElement === void 0 ? void 0 : clippedElement.clip(boost.clipRect);\n    }\n    boost.resize();\n    boost.clear();\n    if (!boost.wgl) {\n        boost.wgl = new Boost_WGLRenderer(function (wgl) {\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.time('buffer copy'); // eslint-disable-line no-console\n            }\n            boost.copy();\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.timeEnd('buffer copy'); // eslint-disable-line no-console\n            }\n        });\n        if (!boost.wgl.init(boost.canvas)) {\n            // The OGL renderer couldn't be inited. This likely means a shader\n            // error as we wouldn't get to this point if there was no WebGL\n            // support.\n            BoostSeries_error('[highcharts boost] - unable to init WebGL renderer');\n        }\n        boost.wgl.setOptions(chart.options.boost || {});\n        if (target instanceof ChartClass) {\n            boost.wgl.allocateBuffer(chart);\n        }\n    }\n    boost.wgl.setSize(width, height);\n    return boost.wgl;\n}\n/**\n * If implemented in the core, parts of this can probably be\n * shared with other similar methods in Highcharts.\n * @private\n * @function Highcharts.Series#destroyGraphics\n */\nfunction destroyGraphics(series) {\n    var points = series.points;\n    if (points) {\n        var point = void 0,\n            i = void 0;\n        for (i = 0; i < points.length; i = i + 1) {\n            point = points[i];\n            if (point && point.destroyElements) {\n                point.destroyElements(); // #7557\n            }\n        }\n    }\n    ['graph', 'area', 'tracker'].forEach(function (prop) {\n        var seriesProp = series[prop];\n        if (seriesProp) {\n            series[prop] = seriesProp.destroy();\n        }\n    });\n    for (var _i = 0, _a = series.zones; _i < _a.length; _i++) {\n        var zone = _a[_i];\n        destroyObjectProperties(zone, void 0, true);\n    }\n}\n/**\n * An \"async\" foreach loop. Uses a setTimeout to keep the loop from blocking the\n * UI thread.\n *\n * @private\n * @param {Array<unknown>} arr\n * The array to loop through.\n * @param {Function} fn\n * The callback to call for each item.\n * @param {Function} finalFunc\n * The callback to call when done.\n * @param {number} [chunkSize]\n * The number of iterations per timeout.\n * @param {number} [i]\n * The current index.\n * @param {boolean} [noTimeout]\n * Set to true to skip timeouts.\n */\nfunction eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout) {\n    i = i || 0;\n    chunkSize = chunkSize || CHUNK_SIZE;\n    var threshold = i + chunkSize;\n    var proceed = true;\n    while (proceed && i < threshold && i < arr.length) {\n        proceed = fn(arr[i], i);\n        ++i;\n    }\n    if (proceed) {\n        if (i < arr.length) {\n            if (noTimeout) {\n                eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout);\n            }\n            else if (BoostSeries_win.requestAnimationFrame) {\n                // If available, do requestAnimationFrame - shaves off a few ms\n                BoostSeries_win.requestAnimationFrame(function () {\n                    eachAsync(arr, fn, finalFunc, chunkSize, i);\n                });\n            }\n            else {\n                setTimeout(eachAsync, 0, arr, fn, finalFunc, chunkSize, i);\n            }\n        }\n        else if (finalFunc) {\n            finalFunc();\n        }\n    }\n}\n/**\n * Enter boost mode and apply boost-specific properties.\n * @private\n * @function Highcharts.Series#enterBoost\n */\nfunction enterBoost(series) {\n    var _a;\n    series.boost = series.boost || {\n        // Faster than a series bind:\n        getPoint: (function (bp) { return getPoint(series, bp); })\n    };\n    var alteredByBoost = series.boost.altered = [];\n    // Save the original values, including whether it was an own\n    // property or inherited from the prototype.\n    ['allowDG', 'directTouch', 'stickyTracking'].forEach(function (prop) {\n        alteredByBoost.push({\n            prop: prop,\n            val: series[prop],\n            own: Object.hasOwnProperty.call(series, prop)\n        });\n    });\n    series.allowDG = false;\n    series.directTouch = false;\n    series.stickyTracking = true;\n    // Prevent animation when zooming in on boosted series(#13421).\n    series.finishedAnimating = true;\n    // Hide series label if any\n    if (series.labelBySeries) {\n        series.labelBySeries = series.labelBySeries.destroy();\n    }\n    // Destroy existing points after zoom out\n    if (series.is('scatter') &&\n        !series.is('treemap') &&\n        series.data.length) {\n        for (var _i = 0, _b = series.data; _i < _b.length; _i++) {\n            var point = _b[_i];\n            (_a = point === null || point === void 0 ? void 0 : point.destroy) === null || _a === void 0 ? void 0 : _a.call(point);\n        }\n        series.data.length = 0;\n        series.points.length = 0;\n        delete series.processedData;\n    }\n}\n/**\n * Exit from boost mode and restore non-boost properties.\n * @private\n * @function Highcharts.Series#exitBoost\n */\nfunction exitBoost(series) {\n    var _a;\n    var boost = series.boost,\n        chart = series.chart,\n        chartBoost = chart.boost;\n    if (chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup) {\n        chartBoost.markerGroup.destroy();\n        chartBoost.markerGroup = void 0;\n        for (var _i = 0, _b = chart.series; _i < _b.length; _i++) {\n            var s = _b[_i];\n            s.markerGroup = void 0;\n            s.markerGroup = s.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n        }\n    }\n    // Reset instance properties and/or delete instance properties and go back\n    // to prototype\n    if (boost) {\n        (boost.altered || []).forEach(function (setting) {\n            if (setting.own) {\n                series[setting.prop] = setting.val;\n            }\n            else {\n                // Revert to prototype\n                delete series[setting.prop];\n            }\n        });\n        // Clear previous run\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n    // #21106, clean up boost clipping on the series groups.\n    (_a = (chart.seriesGroup || series.group)) === null || _a === void 0 ? void 0 : _a.clip();\n}\n/**\n * @private\n * @function Highcharts.Series#hasExtremes\n */\nfunction hasExtremes(series, checkX) {\n    var options = series.options,\n        dataLength = series.dataTable.modified.rowCount,\n        xAxis = series.xAxis && series.xAxis.options,\n        yAxis = series.yAxis && series.yAxis.options,\n        colorAxis = series.colorAxis && series.colorAxis.options;\n    return dataLength > (options.boostThreshold || Number.MAX_VALUE) &&\n        // Defined yAxis extremes\n        BoostSeries_isNumber(yAxis.min) &&\n        BoostSeries_isNumber(yAxis.max) &&\n        // Defined (and required) xAxis extremes\n        (!checkX ||\n            (BoostSeries_isNumber(xAxis.min) && BoostSeries_isNumber(xAxis.max))) &&\n        // Defined (e.g. heatmap) colorAxis extremes\n        (!colorAxis ||\n            (BoostSeries_isNumber(colorAxis.min) && BoostSeries_isNumber(colorAxis.max)));\n}\n/**\n * Used multiple times. In processData first on this.options.data, the second\n * time it runs the check again after processedXData is built.\n * If the data is going to be grouped, the series shouldn't be boosted.\n * @private\n */\nvar getSeriesBoosting = function (series, data) {\n    // Check if will be grouped.\n    if (series.forceCrop) {\n        return false;\n    }\n    return (BoostSeries_isChartSeriesBoosting(series.chart) ||\n        ((data ? data.length : 0) >=\n            (series.options.boostThreshold || Number.MAX_VALUE)));\n};\n/**\n * Extend series.destroy to also remove the fake k-d-tree points (#5137).\n * Normally this is handled by Series.destroy that calls Point.destroy,\n * but the fake search points are not registered like that.\n * @private\n */\nfunction onSeriesDestroy() {\n    var series = this,\n        chart = series.chart;\n    if (chart.boost &&\n        chart.boost.markerGroup === series.markerGroup) {\n        series.markerGroup = null;\n    }\n    if (chart.hoverPoints) {\n        chart.hoverPoints = chart.hoverPoints.filter(function (point) {\n            return point.series === series;\n        });\n    }\n    if (chart.hoverPoint && chart.hoverPoint.series === series) {\n        chart.hoverPoint = null;\n    }\n}\n/**\n * @private\n */\nfunction onSeriesHide() {\n    var boost = this.boost;\n    if (boost && boost.canvas && boost.target) {\n        if (boost.wgl) {\n            boost.wgl.clear();\n        }\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n}\n/**\n * Performs the actual render if the renderer is\n * attached to the series.\n * @private\n */\nfunction renderIfNotSeriesBoosting(series) {\n    var boost = series.boost;\n    if (boost &&\n        boost.canvas &&\n        boost.target &&\n        boost.wgl &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        boost.wgl.render(series.chart);\n    }\n}\n/**\n * Return a full Point object based on the index.\n * The boost module uses stripped point objects for performance reasons.\n * @private\n * @param {object|Highcharts.Point} boostPoint\n *        A stripped-down point object\n * @return {Highcharts.Point}\n *         A Point object as per https://api.highcharts.com/highcharts#Point\n */\nfunction getPoint(series, boostPoint) {\n    var _a;\n    var seriesOptions = series.options,\n        xAxis = series.xAxis,\n        PointClass = series.pointClass;\n    if (boostPoint instanceof PointClass) {\n        return boostPoint;\n    }\n    var isScatter = series.is('scatter'), xData = ((isScatter && series.getColumn('x', true).length ?\n            series.getColumn('x', true) :\n            void 0) ||\n            (series.getColumn('x').length ? series.getColumn('x') : void 0) ||\n            seriesOptions.xData ||\n            series.getColumn('x', true) ||\n            false), yData = (series.getColumn('y', true) ||\n            seriesOptions.yData ||\n            false), point = new PointClass(series, (isScatter && xData && yData) ?\n            [xData[boostPoint.i], yData[boostPoint.i]] :\n            (isArray(series.options.data) ? series.options.data : [])[boostPoint.i], xData ? xData[boostPoint.i] : void 0);\n    point.category = BoostSeries_pick(xAxis.categories ?\n        xAxis.categories[point.x] :\n        point.x, // @todo simplify\n    point.x);\n    point.key = (_a = point.name) !== null && _a !== void 0 ? _a : point.category;\n    point.dist = boostPoint.dist;\n    point.distX = boostPoint.distX;\n    point.plotX = boostPoint.plotX;\n    point.plotY = boostPoint.plotY;\n    point.index = boostPoint.i;\n    point.percentage = boostPoint.percentage;\n    point.isInside = series.isPointInside(point);\n    return point;\n}\n/**\n * @private\n */\nfunction scatterProcessData(force) {\n    var _a,\n        _b,\n        _c,\n        _d,\n        _e,\n        _f,\n        _g,\n        _h;\n    var series = this,\n        options = series.options,\n        xAxis = series.xAxis,\n        yAxis = series.yAxis;\n    // Process only on changes\n    if (!series.isDirty &&\n        !xAxis.isDirty &&\n        !yAxis.isDirty &&\n        !force) {\n        return false;\n    }\n    // Required to get tick-based zoom ranges that take options into account\n    // like `minPadding`, `maxPadding`, `startOnTick`, `endOnTick`.\n    series.yAxis.setTickInterval();\n    var boostThreshold = options.boostThreshold || 0, cropThreshold = options.cropThreshold, xData = series.getColumn('x'), xExtremes = xAxis.getExtremes(), xMax = (_a = xExtremes.max) !== null && _a !== void 0 ? _a : Number.MAX_VALUE, xMin = (_b = xExtremes.min) !== null && _b !== void 0 ? _b : -Number.MAX_VALUE, yData = series.getColumn('y'), yExtremes = yAxis.getExtremes(), yMax = (_c = yExtremes.max) !== null && _c !== void 0 ? _c : Number.MAX_VALUE, yMin = (_d = yExtremes.min) !== null && _d !== void 0 ? _d : -Number.MAX_VALUE;\n    // Skip processing in non-boost zoom\n    if (!series.boosted &&\n        xAxis.old &&\n        yAxis.old &&\n        xMin >= ((_e = xAxis.old.min) !== null && _e !== void 0 ? _e : -Number.MAX_VALUE) &&\n        xMax <= ((_f = xAxis.old.max) !== null && _f !== void 0 ? _f : Number.MAX_VALUE) &&\n        yMin >= ((_g = yAxis.old.min) !== null && _g !== void 0 ? _g : -Number.MAX_VALUE) &&\n        yMax <= ((_h = yAxis.old.max) !== null && _h !== void 0 ? _h : Number.MAX_VALUE)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Without thresholds just assign data\n    var dataLength = series.dataTable.rowCount;\n    if (!boostThreshold ||\n        dataLength < boostThreshold ||\n        (cropThreshold &&\n            !series.forceCrop &&\n            !series.getExtremesFromAll &&\n            !options.getExtremesFromAll &&\n            dataLength < cropThreshold)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Filter unsorted scatter data for ranges\n    var processedData = [],\n        processedXData = [],\n        processedYData = [],\n        xRangeNeeded = !(BoostSeries_isNumber(xExtremes.max) || BoostSeries_isNumber(xExtremes.min)),\n        yRangeNeeded = !(BoostSeries_isNumber(yExtremes.max) || BoostSeries_isNumber(yExtremes.min));\n    var cropped = false,\n        x,\n        xDataMax = xData[0],\n        xDataMin = xData[0],\n        y,\n        yDataMax = yData === null || yData === void 0 ? void 0 : yData[0],\n        yDataMin = yData === null || yData === void 0 ? void 0 : yData[0];\n    for (var i = 0, iEnd = xData.length; i < iEnd; ++i) {\n        x = xData[i];\n        y = yData === null || yData === void 0 ? void 0 : yData[i];\n        if (x >= xMin && x <= xMax &&\n            y >= yMin && y <= yMax) {\n            processedData.push({ x: x, y: y });\n            processedXData.push(x);\n            processedYData.push(y);\n            if (xRangeNeeded) {\n                xDataMax = Math.max(xDataMax, x);\n                xDataMin = Math.min(xDataMin, x);\n            }\n            if (yRangeNeeded) {\n                yDataMax = Math.max(yDataMax, y);\n                yDataMin = Math.min(yDataMin, y);\n            }\n        }\n        else {\n            cropped = true;\n        }\n    }\n    if (xRangeNeeded) {\n        xAxis.dataMax = Math.max(xDataMax, xAxis.dataMax || 0);\n        xAxis.dataMin = Math.min(xDataMin, xAxis.dataMin || 0);\n    }\n    if (yRangeNeeded) {\n        yAxis.dataMax = Math.max(yDataMax, yAxis.dataMax || 0);\n        yAxis.dataMin = Math.min(yDataMin, yAxis.dataMin || 0);\n    }\n    // Set properties as base processData\n    series.cropped = cropped;\n    series.cropStart = 0;\n    // For boosted points rendering\n    if (cropped && series.dataTable.modified === series.dataTable) {\n        // Calling setColumns with cropped data must be done on a new instance\n        // to avoid modification of the original (complete) data\n        series.dataTable.modified = new Data_DataTableCore();\n    }\n    series.dataTable.modified.setColumns({\n        x: processedXData,\n        y: processedYData\n    });\n    if (!getSeriesBoosting(series, processedXData)) {\n        series.processedData = processedData; // For un-boosted points rendering\n    }\n    return true;\n}\n/**\n * @private\n * @function Highcharts.Series#renderCanvas\n */\nfunction seriesRenderCanvas() {\n    var _this = this;\n    var options = this.options || {}, chart = this.chart, chartBoost = chart.boost, seriesBoost = this.boost, xAxis = this.xAxis, yAxis = this.yAxis, xData = options.xData || this.getColumn('x', true), yData = options.yData || this.getColumn('y', true), lowData = this.getColumn('low', true), highData = this.getColumn('high', true), rawData = this.processedData || options.data, xExtremes = xAxis.getExtremes(), \n        // Taking into account the offset of the min point #19497\n        xMin = xExtremes.min - (xAxis.minPointOffset || 0), xMax = xExtremes.max + (xAxis.minPointOffset || 0), yExtremes = yAxis.getExtremes(), yMin = yExtremes.min - (yAxis.minPointOffset || 0), yMax = yExtremes.max + (yAxis.minPointOffset || 0), pointTaken = {}, sampling = !!this.sampling, enableMouseTracking = options.enableMouseTracking, threshold = options.threshold, isRange = this.pointArrayMap &&\n            this.pointArrayMap.join(',') === 'low,high', isStacked = !!options.stacking, cropStart = this.cropStart || 0, requireSorting = this.requireSorting, useRaw = !xData, compareX = options.findNearestPointBy === 'x', xDataFull = ((this.getColumn('x').length ?\n            this.getColumn('x') :\n            void 0) ||\n            this.options.xData ||\n            this.getColumn('x', true)), lineWidth = BoostSeries_pick(options.lineWidth, 1), nullYSubstitute = options.nullInteraction && yMin;\n    var renderer = false,\n        lastClientX,\n        yBottom = yAxis.getThreshold(threshold),\n        minVal,\n        maxVal,\n        minI,\n        maxI;\n    // When touch-zooming or mouse-panning, re-rendering the canvas would not\n    // perform fast enough. Instead, let the axes redraw, but not the series.\n    // The series is scale-translated in an event handler for an approximate\n    // preview.\n    if (xAxis.isPanning || yAxis.isPanning) {\n        return;\n    }\n    // Get or create the renderer\n    renderer = createAndAttachRenderer(chart, this);\n    chart.boosted = true;\n    if (!this.visible) {\n        return;\n    }\n    // If we are zooming out from SVG mode, destroy the graphics\n    if (this.points || this.graph) {\n        destroyGraphics(this);\n    }\n    // If we're rendering per. series we should create the marker groups\n    // as usual.\n    if (!BoostSeries_isChartSeriesBoosting(chart)) {\n        // If all series were boosting, but are not anymore\n        // restore private markerGroup\n        if (this.markerGroup === (chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup)) {\n            this.markerGroup = void 0;\n        }\n        this.markerGroup = this.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n    }\n    else {\n        // If series has a private markerGroup, remove that\n        // and use common markerGroup\n        if (this.markerGroup &&\n            this.markerGroup !== (chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup)) {\n            this.markerGroup.destroy();\n        }\n        // Use a single group for the markers\n        this.markerGroup = chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup;\n        // When switching from chart boosting mode, destroy redundant\n        // series boosting targets\n        if (seriesBoost && seriesBoost.target) {\n            this.renderTarget =\n                seriesBoost.target =\n                    seriesBoost.target.destroy();\n        }\n    }\n    var points = this.points = [],\n        addKDPoint = function (clientX,\n        plotY,\n        i,\n        percentage) {\n            var x = xDataFull ? xDataFull[cropStart + i] : false,\n        pushPoint = function (plotX) {\n                if (chart.inverted) {\n                    plotX = xAxis.len - plotX;\n                plotY = yAxis.len - plotY;\n            }\n            points.push({\n                destroy: noop,\n                x: x,\n                clientX: plotX,\n                plotX: plotX,\n                plotY: plotY,\n                i: cropStart + i,\n                percentage: percentage\n            });\n        };\n        // We need to do ceil on the clientX to make things\n        // snap to pixel values. The renderer will frequently\n        // draw stuff on \"sub-pixels\".\n        clientX = Math.ceil(clientX);\n        // Shaves off about 60ms compared to repeated concatenation\n        index = compareX ? clientX : clientX + ',' + plotY;\n        // The k-d tree requires series points.\n        // Reduce the amount of points, since the time to build the\n        // tree increases exponentially.\n        if (enableMouseTracking) {\n            if (!pointTaken[index]) {\n                pointTaken[index] = true;\n                pushPoint(clientX);\n            }\n            else if (x === xDataFull[xDataFull.length - 1]) {\n                // If the last point is on the same pixel as the last\n                // tracked point, swap them. (#18856)\n                points.length--;\n                pushPoint(clientX);\n            }\n        }\n    };\n    // Do not start building while drawing\n    this.buildKDTree = noop;\n    BoostSeries_fireEvent(this, 'renderCanvas');\n    if (this.is('line') &&\n        lineWidth > 1 &&\n        (seriesBoost === null || seriesBoost === void 0 ? void 0 : seriesBoost.target) &&\n        chartBoost &&\n        !chartBoost.lineWidthFilter) {\n        chartBoost.lineWidthFilter = chart.renderer.definition({\n            tagName: 'filter',\n            children: [\n                {\n                    tagName: 'feMorphology',\n                    attributes: {\n                        operator: 'dilate',\n                        radius: 0.25 * lineWidth\n                    }\n                }\n            ],\n            attributes: { id: 'linewidth' }\n        });\n        seriesBoost.target.attr({\n            filter: 'url(#linewidth)'\n        });\n    }\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n        // Perform the actual renderer if we're on series level\n        renderIfNotSeriesBoosting(this);\n    }\n    /**\n     * This builds the KD-tree\n     * @private\n     */\n    function processPoint(d, i) {\n        var _a,\n            _b;\n        var chartDestroyed = typeof chart.index === 'undefined';\n        var x,\n            y,\n            clientX,\n            plotY,\n            percentage,\n            low = false,\n            isYInside = true;\n        if (!defined(d)) {\n            return true;\n        }\n        if (!chartDestroyed) {\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n            }\n            else {\n                x = d;\n                y = (_b = (_a = yData[i]) !== null && _a !== void 0 ? _a : nullYSubstitute) !== null && _b !== void 0 ? _b : null;\n            }\n            // Resolve low and high for range series\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = lowData[i];\n                y = highData[i];\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n                percentage = d.percentage;\n            }\n            // Optimize for scatter zooming\n            if (!requireSorting) {\n                isYInside = (y || 0) >= yMin && y <= yMax;\n            }\n            if (y !== null && x >= xMin && x <= xMax && isYInside) {\n                clientX = xAxis.toPixels(x, true);\n                if (sampling) {\n                    if (typeof minI === 'undefined' ||\n                        clientX === lastClientX) {\n                        if (!isRange) {\n                            low = y;\n                        }\n                        if (typeof maxI === 'undefined' ||\n                            y > maxVal) {\n                            maxVal = y;\n                            maxI = i;\n                        }\n                        if (typeof minI === 'undefined' ||\n                            low < minVal) {\n                            minVal = low;\n                            minI = i;\n                        }\n                    }\n                    // Add points and reset\n                    if (!compareX || clientX !== lastClientX) {\n                        // `maxI` is number too:\n                        if (typeof minI !== 'undefined') {\n                            plotY =\n                                yAxis.toPixels(maxVal, true);\n                            yBottom =\n                                yAxis.toPixels(minVal, true);\n                            addKDPoint(clientX, plotY, maxI, percentage);\n                            if (yBottom !== plotY) {\n                                addKDPoint(clientX, yBottom, minI, percentage);\n                            }\n                        }\n                        minI = maxI = void 0;\n                        lastClientX = clientX;\n                    }\n                }\n                else {\n                    plotY = Math.ceil(yAxis.toPixels(y, true));\n                    addKDPoint(clientX, plotY, i, percentage);\n                }\n            }\n        }\n        return !chartDestroyed;\n    }\n    /**\n     * @private\n     */\n    var boostOptions = renderer.settings,\n        doneProcessing = function () {\n            BoostSeries_fireEvent(_this, 'renderedCanvas');\n        // Go back to prototype, ready to build\n        delete _this.buildKDTree;\n        // Check that options exist, as async processing\n        // could mean the series is removed at this point (#19895)\n        if (_this.options) {\n            _this.buildKDTree();\n        }\n        if (boostOptions.debug.timeKDTree) {\n            console.timeEnd('kd tree building'); // eslint-disable-line no-console\n        }\n    };\n    // Loop over the points to build the k-d tree - skip this if\n    // exporting\n    if (!chart.renderer.forExport) {\n        if (boostOptions.debug.timeKDTree) {\n            console.time('kd tree building'); // eslint-disable-line no-console\n        }\n        eachAsync(isStacked ?\n            this.data.slice(cropStart) :\n            (xData || rawData), processPoint, doneProcessing);\n    }\n}\n/**\n * Used for treemap|heatmap.drawPoints\n * @private\n */\nfunction wrapSeriesDrawPoints(proceed) {\n    var enabled = true;\n    if (this.chart.options && this.chart.options.boost) {\n        enabled = typeof this.chart.options.boost.enabled === 'undefined' ?\n            true :\n            this.chart.options.boost.enabled;\n    }\n    if (!enabled || !this.boosted) {\n        return proceed.call(this);\n    }\n    this.chart.boosted = true;\n    // Make sure we have a valid OGL context\n    var renderer = createAndAttachRenderer(this.chart,\n        this);\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n    }\n    renderIfNotSeriesBoosting(this);\n}\n/**\n * Override a bunch of methods the same way. If the number of points is\n * below the threshold, run the original method. If not, check for a\n * canvas version or do nothing.\n *\n * Note that we're not overriding any of these for heatmaps.\n */\nfunction wrapSeriesFunctions(seriesProto, seriesTypes, method) {\n    /**\n     * @private\n     */\n    function branch(proceed) {\n        var letItPass = this.options.stacking &&\n                (method === 'translate' || method === 'generatePoints');\n        if (!this.boosted ||\n            letItPass ||\n            !boostEnabled(this.chart) ||\n            this.type === 'heatmap' ||\n            this.type === 'treemap' ||\n            !Boost_BoostableMap[this.type] ||\n            this.options.boostThreshold === 0) {\n            proceed.call(this);\n            // Run canvas version of method, like renderCanvas(), if it exists\n        }\n        else if (method === 'render' && this.renderCanvas) {\n            this.renderCanvas();\n        }\n    }\n    wrap(seriesProto, method, branch);\n    // Special case for some types, when translate method is already wrapped\n    if (method === 'translate') {\n        for (var _i = 0, _a = [\n            'column',\n            'arearange',\n            'columnrange',\n            'heatmap',\n            'treemap'\n        ]; _i < _a.length; _i++) {\n            var type = _a[_i];\n            if (seriesTypes[type]) {\n                wrap(seriesTypes[type].prototype, method, branch);\n            }\n        }\n    }\n}\n/**\n * Do not compute extremes when min and max are set. If we use this in the\n * core, we can add the hook to hasExtremes to the methods directly.\n * @private\n */\nfunction wrapSeriesGetExtremes(proceed) {\n    if (this.boosted) {\n        if (hasExtremes(this)) {\n            return {};\n        }\n        if (this.xAxis.isPanning || this.yAxis.isPanning) {\n            // Do not re-compute the extremes during panning, because looping\n            // the data is expensive. The `this` contains the `dataMin` and\n            // `dataMax` to use.\n            return this;\n        }\n    }\n    return proceed.apply(this, [].slice.call(arguments, 1));\n}\n/**\n * If the series is a heatmap or treemap, or if the series is not boosting\n * do the default behaviour. Otherwise, process if the series has no\n * extremes.\n * @private\n */\nfunction wrapSeriesProcessData(proceed) {\n    var _a,\n        _b,\n        _c;\n    var dataToMeasure = this.options.data;\n    if (boostEnabled(this.chart) && Boost_BoostableMap[this.type]) {\n        var series = this, \n            // Flag for code that should run for ScatterSeries and its\n            // subclasses, apart from the enlisted exceptions.\n            isScatter = series.is('scatter') &&\n                !series.is('bubble') &&\n                !series.is('treemap') &&\n                !series.is('heatmap');\n        // If there are no extremes given in the options, we also need to\n        // process the data to read the data extremes. If this is a heatmap,\n        // do default behaviour.\n        if (\n        // First pass with options.data:\n        !getSeriesBoosting(series, dataToMeasure) ||\n            isScatter ||\n            series.is('treemap') ||\n            // Use processedYData for the stack (#7481):\n            series.options.stacking ||\n            !hasExtremes(series, true)) {\n            // Do nothing until the panning stops\n            if (series.boosted && (((_a = series.xAxis) === null || _a === void 0 ? void 0 : _a.isPanning) || ((_b = series.yAxis) === null || _b === void 0 ? void 0 : _b.isPanning))) {\n                return;\n            }\n            // Extra check for zoomed scatter data\n            if (isScatter && series.yAxis.type !== 'treegrid') {\n                scatterProcessData.call(series, arguments[1]);\n            }\n            else {\n                proceed.apply(series, [].slice.call(arguments, 1));\n            }\n            dataToMeasure = series.getColumn('x', true);\n        }\n        // Set the isBoosting flag, second pass with processedXData to\n        // see if we have zoomed.\n        series.boosted = getSeriesBoosting(series, dataToMeasure);\n        // Enter or exit boost mode\n        if (series.boosted) {\n            // Force turbo-mode:\n            var firstPoint = void 0;\n            if ((_c = series.options.data) === null || _c === void 0 ? void 0 : _c.length) {\n                firstPoint = series.getFirstValidPoint(series.options.data);\n                if (!BoostSeries_isNumber(firstPoint) &&\n                    !isArray(firstPoint) &&\n                    !series.is('treemap')) {\n                    BoostSeries_error(12, false, series.chart);\n                }\n            }\n            enterBoost(series);\n        }\n        else {\n            exitBoost(series);\n        }\n        // The series type is not boostable\n    }\n    else {\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Return a point instance from the k-d-tree\n * @private\n */\nfunction wrapSeriesSearchPoint(proceed) {\n    var result = proceed.apply(this,\n        [].slice.call(arguments, 1));\n    if (this.boost && result) {\n        return this.boost.getPoint(result);\n    }\n    return result;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar BoostSeries = {\n    compose: BoostSeries_compose,\n    destroyGraphics: destroyGraphics,\n    eachAsync: eachAsync,\n    getPoint: getPoint\n};\n/* harmony default export */ var Boost_BoostSeries = (BoostSeries);\n\n;// ./code/es5/es-modules/Extensions/Boost/NamedColors.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// Register color names since GL can't render those directly.\n// TODO: When supporting modern syntax, make this a named export\nvar defaultHTMLColorMap = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dodgerblue: '#1e90ff',\n    feldspar: '#d19275',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    gold: '#ffd700',\n    goldenrod: '#daa520',\n    gray: '#808080',\n    grey: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavender: '#e6e6fa',\n    lavenderblush: '#fff0f5',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgrey: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslateblue: '#8470ff',\n    lightslategray: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370d8',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#d87093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    violetred: '#d02090',\n    wheat: '#f5deb3',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32'\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar namedColors = {\n    defaultHTMLColorMap: defaultHTMLColorMap\n};\n/* harmony default export */ var NamedColors = (namedColors);\n\n;// ./code/es5/es-modules/Extensions/Boost/Boost.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar Boost_spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n\n\nvar Boost_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, Boost_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\nvar Boost_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Boost_error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error;\n/* *\n *\n *  Constants\n *\n * */\nvar Boost_contexts = [\n    'webgl',\n    'experimental-webgl',\n    'moz-webgl',\n    'webkit-3d'\n];\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction Boost_compose(ChartClass, AxisClass, SeriesClass, seriesTypes, PointClass, ColorClass) {\n    var wglMode = hasWebGLSupport();\n    if (!wglMode) {\n        if (typeof (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).initCanvasBoost !== 'undefined') {\n            // Fallback to canvas boost\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().initCanvasBoost();\n        }\n        else {\n            Boost_error(26);\n        }\n    }\n    if (ColorClass && !ColorClass.names.lightgoldenrodyellow) {\n        ColorClass.names = __assign(__assign({}, ColorClass.names), NamedColors.defaultHTMLColorMap);\n    }\n    // WebGL support is alright, and we're good to go.\n    Boost_BoostChart.compose(ChartClass, wglMode);\n    Boost_BoostSeries.compose(SeriesClass, seriesTypes, PointClass, wglMode);\n    // Handle zooming by touch/pinch or mouse wheel. Assume that boosted charts\n    // are too slow for a live preview while dragging. Instead, just scale the\n    // div while `isPanning`.\n    Boost_addEvent(AxisClass, 'setExtremes', function (e) {\n        var _a;\n        var _b,\n            _c;\n        // Render targets can be either chart-wide or series-specific\n        var renderTargets = Boost_spreadArray([this.chart],\n            this.series,\n            true).map(function (item) { return item.renderTarget; })\n                .filter(Boolean);\n        for (var _i = 0, renderTargets_1 = renderTargets; _i < renderTargets_1.length; _i++) {\n            var renderTarget = renderTargets_1[_i];\n            var _d = this, horiz = _d.horiz, pos = _d.pos, scaleKey = horiz ? 'scaleX' : 'scaleY', translateKey = horiz ? 'translateX' : 'translateY', lastScale = (_b = renderTarget === null || renderTarget === void 0 ? void 0 : renderTarget[scaleKey]) !== null && _b !== void 0 ? _b : 1;\n            var scale = 1,\n                translate = 0,\n                opacity = 1,\n                filter = 'none';\n            if (this.isPanning) {\n                scale = ((_c = e.scale) !== null && _c !== void 0 ? _c : 1) * lastScale;\n                translate = ((renderTarget === null || renderTarget === void 0 ? void 0 : renderTarget[translateKey]) || 0) -\n                    scale * (e.move || 0) +\n                    lastScale * pos -\n                    scale * pos;\n                opacity = 0.7;\n                filter = 'blur(3px)';\n            }\n            renderTarget === null || renderTarget === void 0 ? void 0 : renderTarget.attr((_a = {},\n                _a[scaleKey] = scale,\n                _a[translateKey] = translate,\n                _a)).css({\n                transition: '250ms filter, 250ms opacity',\n                filter: filter,\n                opacity: opacity\n            });\n        }\n    });\n}\n/**\n * Returns true if the current browser supports WebGL.\n *\n * @requires modules/boost\n *\n * @function Highcharts.hasWebGLSupport\n *\n * @return {boolean}\n * `true` if the browser supports WebGL.\n */\nfunction hasWebGLSupport() {\n    var canvas,\n        gl = false;\n    if (typeof Boost_win.WebGLRenderingContext !== 'undefined') {\n        canvas = Boost_doc.createElement('canvas');\n        for (var i = 0; i < Boost_contexts.length; ++i) {\n            try {\n                gl = canvas.getContext(Boost_contexts[i]);\n                if (typeof gl !== 'undefined' && gl !== null) {\n                    return true;\n                }\n            }\n            catch (e) {\n                // Silent error\n            }\n        }\n    }\n    return false;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Boost = {\n    compose: Boost_compose,\n    hasWebGLSupport: hasWebGLSupport\n};\n/* harmony default export */ var Boost_Boost = (Boost);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Options for the Boost module. The Boost module allows certain series types\n * to be rendered by WebGL instead of the default SVG. This allows hundreds of\n * thousands of data points to be rendered in milliseconds. In addition to the\n * WebGL rendering it saves time by skipping processing and inspection of the\n * data wherever possible. This introduces some limitations to what features are\n * available in boost mode. See [the docs](\n * https://www.highcharts.com/docs/advanced-chart-features/boost-module) for\n * details.\n *\n * In addition to the global `boost` option, each series has a\n * [boostThreshold](#plotOptions.series.boostThreshold) that defines when the\n * boost should kick in.\n *\n * Requires the `modules/boost.js` module.\n *\n * @sample {highstock} highcharts/boost/line-series-heavy-stock\n *         Stock chart\n * @sample {highstock} highcharts/boost/line-series-heavy-dynamic\n *         Dynamic stock chart\n * @sample highcharts/boost/line\n *         Line chart\n * @sample highcharts/boost/line-series-heavy\n *         Line chart with hundreds of series\n * @sample highcharts/boost/scatter\n *         Scatter chart\n * @sample highcharts/boost/area\n *         Area chart\n * @sample highcharts/boost/arearange\n *         Area range chart\n * @sample highcharts/boost/column\n *         Column chart\n * @sample highcharts/boost/columnrange\n *         Column range chart\n * @sample highcharts/boost/bubble\n *         Bubble chart\n * @sample highcharts/boost/heatmap\n *         Heat map\n * @sample highcharts/boost/treemap\n *         Tree map\n *\n * @product   highcharts highstock\n * @requires  modules/boost\n * @apioption boost\n */\n/**\n * The chart will be boosted, if one of the series crosses its threshold and all\n * the series in the chart can be boosted.\n *\n * @type      {boolean}\n * @default   true\n * @apioption boost.allowForce\n */\n/**\n * Enable or disable boost on a chart.\n *\n * @type      {boolean}\n * @default   true\n * @apioption boost.enabled\n */\n/**\n * Debugging options for boost.\n * Useful for benchmarking, and general timing.\n *\n * @apioption boost.debug\n */\n/**\n * Time the series rendering.\n *\n * This outputs the time spent on actual rendering in the console when\n * set to true.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeRendering\n */\n/**\n * Time the series processing.\n *\n * This outputs the time spent on transforming the series data to\n * vertex buffers when set to true.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeSeriesProcessing\n */\n/**\n * Time the WebGL setup.\n *\n * This outputs the time spent on setting up the WebGL context,\n * creating shaders, and textures.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeSetup\n */\n/**\n * Time the building of the k-d tree.\n *\n * This outputs the time spent building the k-d tree used for\n * markers etc.\n *\n * Note that the k-d tree is built async, and runs post-rendering.\n * Following, it does not affect the performance of the rendering itself.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeKDTree\n */\n/**\n * Show the number of points skipped through culling.\n *\n * When set to true, the number of points skipped in series processing\n * is outputted. Points are skipped if they are closer than 1 pixel from\n * each other.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.showSkipSummary\n */\n/**\n * Time the WebGL to SVG buffer copy\n *\n * After rendering, the result is copied to an image which is injected\n * into the SVG.\n *\n * If this property is set to true, the time it takes for the buffer copy\n * to complete is outputted.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.debug.timeBufferCopy\n */\n/**\n * The pixel ratio for the WebGL content. If 0, the `window.devicePixelRatio` is\n * used. This ensures sharp graphics on high DPI displays like Apple's Retina,\n * as well as when a page is zoomed.\n *\n * The default is left at 1 for now, as this is a new feature that has the\n * potential to break existing setups. Over time, when it has been battle\n * tested, the intention is to set it to 0 by default.\n *\n * Another use case for this option is to set it to 2 in order to make exported\n * and upscaled charts render sharp.\n *\n * One limitation when using the `pixelRatio` is that the line width of graphs\n * is scaled down. Since the Boost module currently can only render 1px line\n * widths, it is scaled down to a thin 0.5 pixels on a Retina display.\n *\n * @sample    highcharts/boost/line-devicepixelratio\n *            Enable the `devicePixelRatio`\n * @sample    highcharts/boost/line-export-pixelratio\n *            Sharper graphics in export\n *\n * @type      {number}\n * @since 10.0.0\n * @default   1\n * @apioption boost.pixelRatio\n */\n/**\n * Set the series threshold for when the boost should kick in globally.\n *\n * Setting to e.g. 20 will cause the whole chart to enter boost mode\n * if there are 20 or more series active. When the chart is in boost mode,\n * every series in it will be rendered to a common canvas. This offers\n * a significant speed improvement in charts with a very high\n * amount of series.\n *\n * @type      {number}\n * @default   50\n * @apioption boost.seriesThreshold\n */\n/**\n * Enable or disable GPU translations. GPU translations are faster than doing\n * the translation in JavaScript.\n *\n * This option may cause rendering issues with certain datasets.\n * Namely, if your dataset has large numbers with small increments (such as\n * timestamps), it won't work correctly. This is due to floating point\n * precision.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.useGPUTranslations\n */\n/**\n * Enable or disable pre-allocation of vertex buffers.\n *\n * Enabling this will make it so that the binary data arrays required for\n * storing the series data will be allocated prior to transforming the data\n * to a WebGL-compatible format.\n *\n * This saves a copy operation on the order of O(n) and so is significantly more\n * performant. However, this is currently an experimental option, and may cause\n * visual artifacts with some datasets.\n *\n * As such, care should be taken when using this setting to make sure that\n * it doesn't cause any rendering glitches with the given use-case.\n *\n * @type      {boolean}\n * @default   false\n * @apioption boost.usePreallocated\n */\n/**\n * Set the point threshold for when a series should enter boost mode.\n *\n * Setting it to e.g. 2000 will cause the series to enter boost mode when there\n * are 2000 or more points in the series.\n *\n * To disable boosting on the series, set the `boostThreshold` to 0. Setting it\n * to 1 will force boosting.\n *\n * Note that the [cropThreshold](plotOptions.series.cropThreshold) also affects\n * this setting. When zooming in on a series that has fewer points than the\n * `cropThreshold`, all points are rendered although outside the visible plot\n * area, and the `boostThreshold` won't take effect.\n *\n * @type      {number}\n * @default   5000\n * @requires  modules/boost\n * @apioption plotOptions.series.boostThreshold\n */\n/**\n * Sets the color blending in the boost module.\n *\n * @type       {string}\n * @default    undefined\n * @validvalue [\"add\", \"multiply\", \"darken\"]\n * @requires   modules/boost\n * @apioption  plotOptions.series.boostBlending\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es5/es-modules/masters/modules/boost.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.hasWebGLSupport = Boost_Boost.hasWebGLSupport;\nBoost_Boost.compose(G.Chart, G.Axis, G.Series, G.seriesTypes, G.Point, G.Color);\n/* harmony default export */ var boost_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "ChartClass", "AxisClass", "SeriesClass", "seriesTypes", "PointClass", "ColorClass", "wglMode", "Column<PERSON><PERSON><PERSON>", "index", "mainCanvas", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "boost_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Boost_Boostables", "BoostableMap", "for<PERSON>ach", "item", "composed", "addEvent", "pick", "pushUnique", "isChartSeriesBoosting", "chart", "allSeries", "series", "boost", "boostOptions", "options", "threshold", "seriesThreshold", "length", "allowBoostForce", "allowForce", "_i", "_a", "xAxis", "axis", "min", "Infinity", "dataMin", "max", "dataMax", "forceChartBoost", "seriesOptions", "canBoostCount", "needBoostCount", "_b", "allSeries_1", "boostThreshold", "visible", "type", "Boost_BoostableMap", "patientMax", "args", "arguments", "r", "Number", "MAX_VALUE", "t", "getColumn", "data", "points", "onChartCallback", "canvasToSVG", "wgl", "render", "boosted", "axes", "some", "isPanning", "clear", "canvas", "allocate<PERSON><PERSON><PERSON>", "markerGroup", "yAxis", "translate", "pos", "order", "prevX", "prevY", "pointer", "e", "hoverPoint", "inverted", "s", "halo", "hide", "target", "navigator", "clipBox", "x", "plotLeft", "y", "plotTop", "width", "plot<PERSON>id<PERSON>", "height", "plotHeight", "top", "opposite", "left", "is", "getClipBox", "lateral", "verticalAxes", "Math", "len", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "Boost_WGLDrawMode", "clamp", "error", "WG<PERSON><PERSON><PERSON>_pick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gl", "errors", "uLocations", "createShader", "bind", "shaderProgram", "useProgram", "_this", "v", "stringToProgram", "f", "uloc", "getUniformLocation", "createProgram", "<PERSON><PERSON><PERSON><PERSON>", "linkProgram", "getProgramParameter", "LINK_STATUS", "bindAttribLocation", "pUniform", "psUniform", "fcUniform", "isBubbleUniform", "bubbleSizeAbsUniform", "bubbleSizeAreaUniform", "uSamplerUniform", "skipTranslationUniform", "isCircleUniform", "isInverted", "push", "getProgramInfoLog", "handleErrors", "join", "str", "shader", "VERTEX_SHADER", "FRAGMENT_SHADER", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS", "getShaderInfoLog", "destroy", "deleteProgram", "fillColorUniform", "getProgram", "pointSizeUniform", "perspectiveUniform", "reset", "uniform1i", "setBubbleUniforms", "zCalcMin", "zCalcMax", "pixelRatio", "zMin", "zMax", "pxSizes", "getPxExtremes", "displayNegative", "zThreshold", "sizeBy", "sizeByAbsoluteValue", "setUniform", "minPxSize", "maxPxSize", "setColor", "color", "uniform4f", "setDrawAsCircle", "flag", "setInverted", "setPMatrix", "m", "uniformMatrix4fv", "setPointSize", "p", "uniform1f", "setSkipTranslation", "setTexture", "texture", "name", "val", "u", "WGLVertexBuffer", "dataComponents", "buffer", "iterator", "preAllocated", "vertAttribute", "components", "allocate", "size", "Float32Array", "vertexAttribPointer", "FLOAT", "build", "dataIn", "attrib", "farray", "deleteBuffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "bufferData", "STATIC_DRAW", "getAttribLocation", "enableVertexAttribArray", "b", "from", "to", "drawMode", "drawArrays", "parse", "doc", "win", "isNumber", "isObject", "merge", "objectEach", "WG<PERSON>enderer_pick", "asBar", "asCircle", "contexts", "WG<PERSON><PERSON><PERSON>", "postRender<PERSON>allback", "isInited", "markerData", "textureHandles", "settings", "pointSize", "lineWidth", "fillColor", "useAlpha", "usePreallocated", "useGPUTranslations", "debug", "timeRendering", "timeSeriesProcessing", "timeSetup", "timeBufferCopy", "timeKDTree", "showSkipSummary", "orthoMatrix", "seriesPointCount", "isStacked", "xData", "stacking", "getPixelRatio", "devicePixelRatio", "setOptions", "vbuffer", "allocateBufferForSingleSeries", "COLOR_BUFFER_BIT", "DEPTH_BUFFER_BIT", "pushSeriesData", "inst", "minVal", "scolor", "z", "low", "zoneColors", "isRange", "pointArrayMap", "sorted", "rawData", "xExtremes", "getExtremes", "xMin", "minPointOffset", "xMax", "yExtremes", "yMin", "yMax", "yData", "zData", "useRaw", "connectNulls", "sdata", "closestLeft", "closestRight", "chartDestroyed", "drawAsBar", "zoneAxis", "zones", "lastX", "lastY", "skipped", "hadPoints", "i", "px", "nx", "nextInside", "prevInside", "pcolor", "isXInside", "isYInside", "firstPoint", "zoneDefColor", "gapSize", "vlen", "boostData", "gapUnit", "closestPointRange", "zone", "zoneColor", "rgba", "value", "pointAttribs", "fill", "closestPointRangePx", "pushColor", "colorData", "vertice", "checkTreshold", "skipTranslation", "closeSegment", "segments", "beginSegment", "pushRect", "w", "h", "node", "levelDynamic", "sort", "point", "swidth", "pointAttr", "plotY", "isNaN", "shapeArgs", "x_1", "_c", "y_1", "_d", "_e", "styledMode", "colorAttribs", "stroke", "state_1", "_loop_1", "zoneColor_1", "pointOptions", "slice", "stackY", "last", "toPixels", "hasMark<PERSON>", "abs", "logarithmic", "step", "console", "log", "pushSupplementPoint", "atStart", "concat", "pushSeries", "markerTo", "time", "markerFrom", "marker", "enabled", "showMarkers", "timeEnd", "flush", "setXAxis", "transA", "minPixelPadding", "pointRange", "horiz", "reversed", "setYAxis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "translation", "<PERSON><PERSON><PERSON>", "chartWidth", "chartHeight", "viewport", "isMS", "si", "sindex", "cbuffer", "shapeOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yBottom", "get<PERSON><PERSON><PERSON>old", "isRadial", "radius", "shapeTexture", "symbol", "circle", "isReady", "bindTexture", "TEXTURE_2D", "handle", "plotGroup", "seriesGroup", "addClass", "getStyle", "colorByPoint", "colors", "fillOpacity", "setOpacity", "boostBlending", "blendFunc", "SRC_ALPHA", "ONE", "blendEquation", "FUNC_ADD", "DST_COLOR", "ZERO", "FUNC_MIN", "blendFuncSeparate", "ONE_MINUS_SRC_ALPHA", "Array", "disableVertexAttribArray", "renderer", "forExport", "setTimeout", "setSize", "init", "noFlush", "getContext", "enable", "BLEND", "disable", "DEPTH_TEST", "depthFunc", "LESS", "createTexture", "fn", "props", "createElement", "ctx", "mozImageSmoothingEnabled", "webkitImageSmoothingEnabled", "msImageSmoothingEnabled", "imageSmoothingEnabled", "strokeStyle", "fillStyle", "activeTexture", "TEXTURE0", "texImage2D", "RGBA", "UNSIGNED_BYTE", "texParameteri", "TEXTURE_WRAP_S", "CLAMP_TO_EDGE", "TEXTURE_WRAP_T", "TEXTURE_MAG_FILTER", "LINEAR", "TEXTURE_MIN_FILTER", "beginPath", "arc", "PI", "fillRect", "moveTo", "lineTo", "deleteTexture", "__spread<PERSON><PERSON>y", "pack", "ar", "l", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "removed", "apply", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "Data_ColumnUtils", "fireEvent", "DataTableCore_objectEach", "<PERSON><PERSON><PERSON>", "DataTableCore", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "applyRowCount", "deleteRows", "rowIndex", "length_1", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "getOptions", "BoostSeries_composed", "BoostSeries_doc", "noop", "BoostSeries_win", "BoostSeries_addEvent", "destroyObjectProperties", "BoostSeries_error", "extend", "BoostSeries_fireEvent", "BoostSeries_isNumber", "BoostSeries_pick", "BoostSeries_pushUnique", "wrap", "defined", "allocateIfNotSeriesBoosting", "BoostSeries_isChartSeriesBoosting", "boostEnabled", "createAndAttachRenderer", "targetGroup", "group", "foSupported", "SVGForeignObjectElement", "hasClickHandler", "Boolean", "events", "click", "renderTarget", "image", "add", "attr", "href", "copy", "resize", "toDataURL", "targetFo", "targetCtx", "element", "append<PERSON><PERSON><PERSON>", "drawImage", "css", "pointerEvents", "mixedBlendMode", "opacity", "clipRect", "zIndex", "g", "box", "BoostSeries_getBoostClipRect", "clippedElement", "clip", "destroyGraphics", "destroyElements", "seriesProp", "eachAsync", "arr", "finalFunc", "chunkSize", "noTimeout", "proceed", "requestAnimationFrame", "hasExtremes", "checkX", "dataLength", "dataTable", "colorAxis", "getSeriesBoosting", "forceCrop", "onSeriesDestroy", "hoverPoints", "filter", "onSeriesHide", "renderIfNotSeriesBoosting", "getPoint", "boostPoint", "pointClass", "isScatter", "category", "categories", "dist", "distX", "plotX", "percentage", "isInside", "isPointInside", "scatterProcessData", "force", "isDirty", "setTickInterval", "cropThreshold", "old", "_f", "_g", "_h", "getExtremesFromAll", "processedData", "processedXData", "processedYData", "xRangeNeeded", "y<PERSON>ange<PERSON><PERSON>ed", "cropped", "xDataMax", "xDataMin", "yDataMax", "yDataMin", "iEnd", "cropStart", "seriesRenderCanvas", "lastClientX", "maxVal", "minI", "maxI", "chartBoost", "seriesBoost", "lowData", "highData", "pointTaken", "sampling", "enableMouseTracking", "requireSorting", "compareX", "findNearestPointBy", "xDataFull", "nullYSubstitute", "nullInteraction", "graph", "addKDPoint", "clientX", "pushPoint", "ceil", "buildKDTree", "lineWidthFilter", "tagName", "children", "attributes", "operator", "wrapSeriesDrawPoints", "wrapSeriesGetExtremes", "wrapSeriesProcessData", "dataToMeasure", "getFirstValidPoint", "enterBoost", "bp", "alteredByBoost", "altered", "own", "allowDG", "directTouch", "stickyTracking", "finishedAnimating", "labelBySeries", "exitBoost", "setting", "wrapSeriesSearchPoint", "plotOptions_1", "plotOptions", "seriesProto_1", "renderCanvas", "method", "wrapSeriesFunctions", "seriesProto", "branch", "letItPass", "typePlotOptions", "AreaSeries", "area", "AreaSplineSeries", "areaspline", "BubbleSeries", "bubble", "ColumnSeries", "HeatmapSeries", "heatmap", "ScatterSeries", "scatter", "TreemapSeries", "treemap", "bubbleProto_1", "SC", "NamedColors", "defaultHTMLColorMap", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "dodgerblue", "feldspar", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "grey", "green", "greenyellow", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "<PERSON><PERSON>rey", "lightgreen", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslateblue", "lightslategray", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "violetred", "wheat", "whitesmoke", "yellow", "yellowgreen", "__assign", "assign", "Boost_spreadArray", "Boost_doc", "Boost_win", "Boost_addEvent", "Boost_error", "Boost_contexts", "hasWebGLSupport", "WebGLRenderingContext", "G", "Chart", "Axis", "Series", "Point", "Color", "initCanvasBoost", "names", "callbacks", "Boost_BoostSeries", "renderTargets", "renderTargets_1", "scaleKey", "<PERSON><PERSON><PERSON>", "lastScale", "scale", "move", "transition"], "mappings": "CAaA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,EACvE,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAC,CAAEJ,GAC5F,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,EAEnGJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAC9E,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAmsIaC,EAAYC,EAAWC,EAAaC,EAAaC,EAAYC,EAC5EC,EA5gISN,EAkuENO,EADPA,EA2cAC,EAAOC,EAp2FGC,EAAuB,CAE/B,IACC,SAAShB,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIa,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAarB,OAAO,CAG5B,IAAIC,EAASiB,CAAwB,CAACE,EAAS,CAAG,CAGjDpB,QAAS,CAAC,CACX,EAMA,OAHAiB,CAAmB,CAACG,EAAS,CAACnB,EAAQA,EAAOD,OAAO,CAAEmB,GAG/ClB,EAAOD,OAAO,AACtB,CAMCmB,EAAoBI,CAAC,CAAG,SAAStB,CAAM,EACtC,IAAIuB,EAASvB,GAAUA,EAAOwB,UAAU,CACvC,WAAa,OAAOxB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAkB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAAS1B,CAAO,CAAE4B,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC9B,EAAS6B,IAC5EE,OAAOC,cAAc,CAAChC,EAAS6B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAW,CAC1D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAsCpFE,EAlBhB,CACb,OACA,aACA,YACA,SACA,cACA,MACA,OACA,UACA,UACA,SACA,UACH,CAiCGC,EAAe,CAAC,EACpBD,EAAiBE,OAAO,CAAC,SAAUC,CAAI,EACnCF,CAAY,CAACE,EAAK,CAAG,CAAA,CACzB,GAuBA,IAAIC,EAAW,AAACL,IAA+EK,QAAQ,CAEnGC,EAAW,AAACN,IAA+EM,QAAQ,CAAEC,EAAO,AAACP,IAA+EO,IAAI,CAAEC,EAAa,AAACR,IAA+EQ,UAAU,CA8E7S,SAASC,EAAsBC,CAAK,EAChC,IAAIC,EAAYD,EAAME,MAAM,CACxBC,EAAQH,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EACtCC,EAAeJ,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,EACvCG,EAAYT,EAAKO,EAAaG,eAAe,CAAE,IACnD,GAAIN,EAAUO,MAAM,EAAIF,EACpB,MAAO,CAAA,EAEX,GAAIL,AAAqB,IAArBA,EAAUO,MAAM,CAChB,MAAO,CAAA,EAEX,IAAIC,EAAkBL,EAAaM,UAAU,CAC7C,GAAI,AAA2B,KAAA,IAApBD,EAAiC,CACxCA,EAAkB,CAAA,EAClB,IAAK,IAAIE,EAAK,EAAGC,EAAKZ,EAAMa,KAAK,CAAEF,EAAKC,EAAGJ,MAAM,CAAEG,IAAM,CACrD,IAAIG,EAAOF,CAAE,CAACD,EAAG,CACjB,GAAId,EAAKiB,EAAKC,GAAG,CAAE,CAACC,KAAYnB,EAAKiB,EAAKG,OAAO,CAAE,CAACD,MAChDnB,EAAKiB,EAAKI,GAAG,CAAEF,KAAYnB,EAAKiB,EAAKK,OAAO,CAAEH,KAAW,CACzDP,EAAkB,CAAA,EAClB,KACJ,CACJ,CACJ,CACA,GAAI,AAAiC,KAAA,IAA1BN,EAAMiB,eAAe,CAAkB,CAC9C,GAAIX,EACA,OAAON,EAAMiB,eAAe,AAEhCjB,CAAAA,EAAMiB,eAAe,CAAG,KAAK,CACjC,CAMA,IAAK,IADDC,EAFAC,EAAgB,EAChBC,EAAiB,EAEZC,EAAK,EAA4BA,EAAKC,AAAhBxB,EAA4BO,MAAM,CAAEgB,IAAM,CACrE,IAAItB,EAASuB,AADcxB,CACH,CAACuB,EAAG,AAMS,CAAA,IAAjCH,AALJA,CAAAA,EAAgBnB,EAAOG,OAAO,AAAD,EAKXqB,cAAc,EAC5BxB,AAAmB,CAAA,IAAnBA,EAAOyB,OAAO,EAMdzB,AAAgB,YAAhBA,EAAO0B,IAAI,GAGXC,AApJ2CrC,CAoJzB,CAACU,EAAO0B,IAAI,CAAC,EAC/B,EAAEN,EAEFQ,AAuGZ,WAEI,IAAK,IADDC,EAAO,EAAE,CACJpB,EAAK,EAAGA,EAAKqB,UAAUxB,MAAM,CAAEG,IACpCoB,CAAI,CAACpB,EAAG,CAAGqB,SAAS,CAACrB,EAAG,CAF5B,IAIIsB,EAAI,CAACC,OAAOC,SAAS,CAWzB,OAVAJ,EAAKtC,OAAO,CAAC,SAAU2C,CAAC,EACpB,GAAI,MAAOA,GAEP,AAAoB,KAAA,IAAbA,EAAE5B,MAAM,EACX4B,EAAE5B,MAAM,CAAG,EAEX,OADAyB,EAAIG,EAAE5B,MAAM,CACL,CAAA,CAGnB,GACOyB,CACX,EAxHuB/B,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOhB,EAAciB,IAAI,CAE9DpC,EAAOqC,MAAM,GAAMlB,CAAAA,EAAcK,cAAc,EAAIQ,OAAOC,SAAS,AAAD,GAC9D,EAAEZ,EAEV,CAQA,OAPApB,EAAMiB,eAAe,CAAGX,GAAoB,CAAA,AAI5Ca,IAAkBrB,EAAUO,MAAM,EAC9Be,IAAmBD,GACnBC,EAAiB,CAAA,EACdpB,EAAMiB,eAAe,AAChC,CAKA,SAASoB,EAAgBxC,CAAK,EAK1B,SAASyC,IACDzC,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAACuC,GAAG,EACf3C,EAAsBC,IACtBA,EAAMG,KAAK,CAACuC,GAAG,CAACC,MAAM,CAAC3C,EAE/B,CA+BAJ,EAASI,EAAO,UA1BhB,WACI,IAAIY,EACAY,CAEJxB,CAAAA,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAC9BH,EAAMG,KAAK,CAACiB,eAAe,CAAG,KAAK,EACnCpB,EAAM4C,OAAO,CAAG,CAAA,EAEX5C,EAAM6C,IAAI,CAACC,IAAI,CAAC,SAAUhC,CAAI,EAAI,OAAOA,EAAKiC,SAAS,AAAE,IAC1D,AAAoC,OAAnCvB,CAAAA,EAAK,AAACZ,CAAAA,EAAKZ,EAAMG,KAAK,AAAD,EAAG6C,KAAK,AAAD,GAAexB,AAAO,KAAK,IAAZA,GAAyBA,EAAGtC,IAAI,CAAC0B,GAE7EZ,EAAMG,KAAK,CAAC8C,MAAM,EAClBjD,EAAMG,KAAK,CAACuC,GAAG,EACf3C,EAAsBC,IAEtBA,EAAMG,KAAK,CAACuC,GAAG,CAACQ,cAAc,CAAClD,GAG/BA,EAAMG,KAAK,CAACgD,WAAW,EACvBnD,EAAMa,KAAK,EACXb,EAAMa,KAAK,CAACL,MAAM,CAAG,GACrBR,EAAMoD,KAAK,EACXpD,EAAMoD,KAAK,CAAC5C,MAAM,CAAG,GACrBR,EAAMG,KAAK,CAACgD,WAAW,CAACE,SAAS,CAACrD,EAAMa,KAAK,CAAC,EAAE,CAACyC,GAAG,CAAEtD,EAAMoD,KAAK,CAAC,EAAE,CAACE,GAAG,CAEhF,GAIA1D,EAASI,EAAO,OAAQyC,EAAa,CAAEc,MAAO,EAAG,GACjD3D,EAASI,EAAO,SAAUyC,GAC1B,IAAIe,EAAQ,GACRC,EAAQ,GACZ7D,EAASI,EAAM0D,OAAO,CAAE,oBAAqB,SAAUC,CAAC,EAEpD,IADI/C,EACAV,EAAS,AAAwB,OAAvBU,CAAAA,EAAK+C,EAAEC,UAAU,AAAD,GAAehD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGV,MAAM,CAE/E,GADAF,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EAC1BH,EAAMG,KAAK,CAACgD,WAAW,EAAIjD,EAAQ,CACnC,IAAIW,EAAQb,EAAM6D,QAAQ,CAAG3D,EAAOkD,KAAK,CAAGlD,EAAOW,KAAK,CACpDuC,EAAQpD,EAAM6D,QAAQ,CAAG3D,EAAOW,KAAK,CAAGX,EAAOkD,KAAK,CACpD,CAAA,AAACvC,GAASA,EAAMyC,GAAG,GAAKE,GACvBJ,GAASA,EAAME,GAAG,GAAKG,CAAK,IAI7BzD,EAAME,MAAM,CAACT,OAAO,CAAC,SAAUqE,CAAC,EAC5B,IAAIlD,CACJ,AAAkB,QAAjBA,CAAAA,EAAKkD,EAAEC,IAAI,AAAD,GAAenD,AAAO,KAAK,IAAZA,GAAyBA,EAAGoD,IAAI,EAC9D,GAIAhE,EAAMG,KAAK,CAACgD,WAAW,CAACE,SAAS,CAACxC,EAAMyC,GAAG,CAAEF,EAAME,GAAG,EACtDE,EAAQ3C,EAAMyC,GAAG,CACjBG,EAAQL,EAAME,GAAG,CAEzB,CACJ,EACJ,CAsC6B,MAhP7B,SAA0BtD,CAAK,CAAEiE,CAAM,EACnC,IAAIC,EAAYlE,EAAMkE,SAAS,CAC3BC,EAAU,CACNC,EAAGpE,EAAMqE,QAAQ,CACjBC,EAAGtE,EAAMuE,OAAO,CAChBC,MAAOxE,EAAMyE,SAAS,CACtBC,OAAQ1E,EAAM2E,UAAU,AAC5B,EAWJ,GAVIT,GAAalE,EAAM6D,QAAQ,EAC3BM,EAAQK,KAAK,EAAIN,EAAUU,GAAG,CAAGV,EAAUQ,MAAM,CAC5CR,EAAUW,QAAQ,EACnBV,CAAAA,EAAQC,CAAC,CAAGF,EAAUY,IAAI,AAAD,GAGxBZ,GAAa,CAAClE,EAAM6D,QAAQ,EACjCM,CAAAA,EAAQO,MAAM,CAAGR,EAAUU,GAAG,CAAGV,EAAUQ,MAAM,CAAG1E,EAAMuE,OAAO,AAAD,EAGhEN,EAAOc,EAAE,CAAE,CACX,IACIlE,EAAQD,AADHqD,EACMpD,KAAK,CAChBuC,EAAQxC,AAFHqD,EAEMb,KAAK,CAEpB,GADAe,EAAUnE,EAAMgF,UAAU,CAACf,GACvBjE,EAAM6D,QAAQ,CAAE,CAChB,IAAIoB,EAAUd,EAAQK,KAAK,AAC3BL,CAAAA,EAAQK,KAAK,CAAGL,EAAQO,MAAM,CAC9BP,EAAQO,MAAM,CAAGO,EACjBd,EAAQC,CAAC,CAAGhB,EAAME,GAAG,CACrBa,EAAQG,CAAC,CAAGzD,EAAMyC,GAAG,AACzB,MAEIa,EAAQC,CAAC,CAAGvD,EAAMyC,GAAG,CACrBa,EAAQG,CAAC,CAAGlB,EAAME,GAAG,AAE7B,CACA,GAAIW,IAAWjE,EAAO,CAClB,IAAIkF,EAAelF,EAAM6D,QAAQ,CAAG7D,EAAMa,KAAK,CAAGb,EAAMoD,KAAK,AACrD8B,CAAAA,EAAa1E,MAAM,EAAI,IACvB2D,EAAQG,CAAC,CAAGa,KAAKpE,GAAG,CAACmE,CAAY,CAAC,EAAE,CAAC5B,GAAG,CAC5Ca,EAAQG,CAAC,EACTH,EAAQO,MAAM,CAAIQ,CAAY,CAAC,EAAE,CAAC5B,GAAG,CACjCtD,EAAMuE,OAAO,CACbW,CAAY,CAAC,EAAE,CAACE,GAAG,CAE/B,CACA,OAAOjB,CACX,EAqMIkB,EAA+FvH,EAAoB,KACnHwH,EAAmHxH,EAAoBI,CAAC,CAACmH,GAqC5GE,EAlBf,CACd,KAAQ,QACR,UAAa,QACb,WAAc,QACd,OAAU,QACV,YAAe,QACf,IAAO,QACP,KAAQ,aACR,QAAW,SACX,QAAW,YACX,QAAW,YACX,OAAU,QACd,EAsBIC,EAAQ,AAAClG,IAA+EkG,KAAK,CAAEC,EAAQ,AAACnG,IAA+EmG,KAAK,CAAEC,EAAiB,AAACpG,IAA+EO,IAAI,CA+LnS8F,EAA2B,WAM3B,SAASA,EAAUC,CAAE,EAKjB,GAHA,IAAI,CAACC,MAAM,CAAG,EAAE,CAChB,IAAI,CAACC,UAAU,CAAG,CAAC,EACnB,IAAI,CAACF,EAAE,CAAGA,EACNA,GAAM,CAAC,IAAI,CAACG,YAAY,GACxB,MAER,CAiPA,OArOAJ,EAAU3G,SAAS,CAACgH,IAAI,CAAG,WACnB,IAAI,CAACJ,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,CAE7C,EAMAN,EAAU3G,SAAS,CAAC+G,YAAY,CAAG,WAC/B,IAAII,EAAQ,IAAI,CACZC,EAAI,IAAI,CAACC,eAAe,CAhMjB,ygHAgMgC,UAAWC,EAAI,IAAI,CAACD,eAAe,CA/NjE,+bA+NkF,YAAaE,EAAO,SAAUrI,CAAC,EAAI,OAAQiI,EAAMP,EAAE,CAACY,kBAAkB,CAACL,EAAMF,aAAa,CAAE/H,EAAK,SAChM,AAAI,AAACkI,GAAME,GAKX,IAAI,CAACL,aAAa,CAAG,IAAI,CAACL,EAAE,CAACa,aAAa,GAC1C,IAAI,CAACb,EAAE,CAACc,YAAY,CAAC,IAAI,CAACT,aAAa,CAAEG,GACzC,IAAI,CAACR,EAAE,CAACc,YAAY,CAAC,IAAI,CAACT,aAAa,CAAEK,GACzC,IAAI,CAACV,EAAE,CAACe,WAAW,CAAC,IAAI,CAACV,aAAa,EACjC,IAAI,CAACL,EAAE,CAACgB,mBAAmB,CAAC,IAAI,CAACX,aAAa,CAAE,IAAI,CAACL,EAAE,CAACiB,WAAW,IAMxE,IAAI,CAACjB,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,EACrC,IAAI,CAACL,EAAE,CAACkB,kBAAkB,CAAC,IAAI,CAACb,aAAa,CAAE,EAAG,mBAClD,IAAI,CAACc,QAAQ,CAAGR,EAAK,YACrB,IAAI,CAACS,SAAS,CAAGT,EAAK,SACtB,IAAI,CAACU,SAAS,CAAGV,EAAK,aACtB,IAAI,CAACW,eAAe,CAAGX,EAAK,YAC5B,IAAI,CAACY,oBAAoB,CAAGZ,EAAK,iBACjC,IAAI,CAACa,qBAAqB,CAAGb,EAAK,oBAClC,IAAI,CAACc,eAAe,CAAGd,EAAK,YAC5B,IAAI,CAACe,sBAAsB,CAAGf,EAAK,mBACnC,IAAI,CAACgB,eAAe,CAAGhB,EAAK,YAC5B,IAAI,CAACiB,UAAU,CAAGjB,EAAK,cAChB,CAAA,IAjBH,IAAI,CAACV,MAAM,CAAC4B,IAAI,CAAC,IAAI,CAAC7B,EAAE,CAAC8B,iBAAiB,CAAC,IAAI,CAACzB,aAAa,GAC7D,IAAI,CAAC0B,YAAY,GACjB,IAAI,CAAC1B,aAAa,CAAG,CAAA,EACd,CAAA,IAZP,IAAI,CAACA,aAAa,CAAG,CAAA,EACrB,IAAI,CAAC0B,YAAY,GACV,CAAA,EAyBf,EAKAhC,EAAU3G,SAAS,CAAC2I,YAAY,CAAG,WAC3B,IAAI,CAAC9B,MAAM,CAACrF,MAAM,EAClBiF,EAAM,qCACF,IAAI,CAACI,MAAM,CAAC+B,IAAI,CAAC,MAE7B,EASAjC,EAAU3G,SAAS,CAACqH,eAAe,CAAG,SAAUwB,CAAG,CAAEjG,CAAI,EACrD,IAAIkG,EAAS,IAAI,CAAClC,EAAE,CAACG,YAAY,CAACnE,AAAS,WAATA,EAAoB,IAAI,CAACgE,EAAE,CAACmC,aAAa,CAAG,IAAI,CAACnC,EAAE,CAACoC,eAAe,QAGrG,CAFA,IAAI,CAACpC,EAAE,CAACqC,YAAY,CAACH,EAAQD,GAC7B,IAAI,CAACjC,EAAE,CAACsC,aAAa,CAACJ,GACjB,IAAI,CAAClC,EAAE,CAACuC,kBAAkB,CAACL,EAAQ,IAAI,CAAClC,EAAE,CAACwC,cAAc,GAOvDN,GANH,IAAI,CAACjC,MAAM,CAAC4B,IAAI,CAAC,kBACb7F,EACA,aACA,IAAI,CAACgE,EAAE,CAACyC,gBAAgB,CAACP,IACtB,CAAA,EAGf,EAKAnC,EAAU3G,SAAS,CAACsJ,OAAO,CAAG,WACtB,IAAI,CAAC1C,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAAC2C,aAAa,CAAC,IAAI,CAACtC,aAAa,EACxC,IAAI,CAACA,aAAa,CAAG,CAAA,EAE7B,EACAN,EAAU3G,SAAS,CAACwJ,gBAAgB,CAAG,WACnC,OAAO,IAAI,CAACvB,SAAS,AACzB,EAOAtB,EAAU3G,SAAS,CAACyJ,UAAU,CAAG,WAC7B,OAAO,IAAI,CAACxC,aAAa,AAC7B,EACAN,EAAU3G,SAAS,CAAC0J,gBAAgB,CAAG,WACnC,OAAO,IAAI,CAAC1B,SAAS,AACzB,EACArB,EAAU3G,SAAS,CAAC2J,kBAAkB,CAAG,WACrC,OAAO,IAAI,CAAC5B,QAAQ,AACxB,EAKApB,EAAU3G,SAAS,CAAC4J,KAAK,CAAG,WACpB,IAAI,CAAChD,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAAC3B,eAAe,CAAE,GACxC,IAAI,CAACtB,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACtB,eAAe,CAAE,GAEhD,EAOA5B,EAAU3G,SAAS,CAAC8J,iBAAiB,CAAG,SAAU5I,CAAM,CAAE6I,CAAQ,CAAEC,CAAQ,CAAEC,CAAU,EACjE,KAAK,IAApBA,GAAyBA,CAAAA,EAAa,CAAA,EAC1C,IAAI5H,EAAgBnB,EAAOG,OAAO,CAC9B6I,EAAOhH,OAAOC,SAAS,CACvBgH,EAAO,CAACjH,OAAOC,SAAS,CAC5B,GAAI,IAAI,CAACyD,EAAE,EAAI,IAAI,CAACK,aAAa,EAAI/F,EAAO6E,EAAE,CAAC,UAAW,CACtD,IAAIqE,EAAUlJ,EAAOmJ,aAAa,GAClCH,EAAOxD,EAAerE,EAAc6H,IAAI,CAAE1D,EAAMuD,EAAU1H,AAAkC,CAAA,IAAlCA,EAAciI,eAAe,CACnFjI,EAAckI,UAAU,CAAG,CAACrH,OAAOC,SAAS,CAAE+G,IAClDC,EAAOzD,EAAerE,EAAc8H,IAAI,CAAEhE,KAAKjE,GAAG,CAACiI,EAAMH,IACzD,IAAI,CAACpD,EAAE,CAACiD,SAAS,CAAC,IAAI,CAAC3B,eAAe,CAAE,GACxC,IAAI,CAACtB,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACtB,eAAe,CAAE,GACxC,IAAI,CAAC3B,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACzB,qBAAqB,CAAGlH,AAA0B,UAA1BA,EAAOG,OAAO,CAACmJ,MAAM,EACpE,IAAI,CAAC5D,EAAE,CAACiD,SAAS,CAAC,IAAI,CAAC1B,oBAAoB,CAAEjH,EAAOG,OAAO,CACtDoJ,mBAAmB,EACxB,IAAI,CAACC,UAAU,CAAC,gBAAiBN,EAAQO,SAAS,CAAGV,GACrD,IAAI,CAACS,UAAU,CAAC,gBAAiBN,EAAQQ,SAAS,CAAGX,GACrD,IAAI,CAACS,UAAU,CAAC,aAAcR,GAC9B,IAAI,CAACQ,UAAU,CAAC,aAAcP,GAC9B,IAAI,CAACO,UAAU,CAAC,mBAAoBxJ,EAAOG,OAAO,CAACkJ,UAAU,CACjE,CACJ,EAOA5D,EAAU3G,SAAS,CAAC6K,QAAQ,CAAG,SAAUC,CAAK,EACtC,IAAI,CAAClE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACmE,SAAS,CAAC,IAAI,CAAC9C,SAAS,CAAE6C,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAExG,EAKAnE,EAAU3G,SAAS,CAACgL,eAAe,CAAG,SAAUC,CAAI,EAC5C,IAAI,CAACrE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACtB,eAAe,CAAE0C,GAAAA,EAEhD,EAOAtE,EAAU3G,SAAS,CAACkL,WAAW,CAAG,SAAUD,CAAI,EACxC,IAAI,CAACrE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACrB,UAAU,CAAEyC,EAE3C,EAOAtE,EAAU3G,SAAS,CAACmL,UAAU,CAAG,SAAUC,CAAC,EACpC,IAAI,CAACxE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACyE,gBAAgB,CAAC,IAAI,CAACtD,QAAQ,CAAE,CAAA,EAAOqD,EAEvD,EAOAzE,EAAU3G,SAAS,CAACsL,YAAY,CAAG,SAAUC,CAAC,EACtC,IAAI,CAAC3E,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC4E,SAAS,CAAC,IAAI,CAACxD,SAAS,CAAEuD,EAE1C,EAKA5E,EAAU3G,SAAS,CAACyL,kBAAkB,CAAG,SAAUR,CAAI,EAC/C,IAAI,CAACrE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACvB,sBAAsB,CAAE2C,CAAAA,CAAAA,AAAS,CAAA,IAATA,CAAY,EAEnE,EAOAtE,EAAU3G,SAAS,CAAC0L,UAAU,CAAG,SAAUC,CAAO,EAC1C,IAAI,CAAC/E,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACxB,eAAe,CAAEsD,EAEhD,EAUAhF,EAAU3G,SAAS,CAAC0K,UAAU,CAAG,SAAUkB,CAAI,CAAEC,CAAG,EAChD,GAAI,IAAI,CAACjF,EAAE,EAAI,IAAI,CAACK,aAAa,CAAE,CAC/B,IAAI6E,EAAI,IAAI,CAAChF,UAAU,CAAC8E,EAAK,CAAI,IAAI,CAAC9E,UAAU,CAAC8E,EAAK,EAC9C,IAAI,CAAChF,EAAE,CAACY,kBAAkB,CAAC,IAAI,CAACP,aAAa,CACjD2E,GACJ,IAAI,CAAChF,EAAE,CAAC4E,SAAS,CAACM,EAAGD,EACzB,CACJ,EACOlF,CACX,IAwCIoF,EAAiC,WAMjC,SAASA,EAAgBnF,CAAE,CAAEkC,CAAM,CAAEkD,CAAc,EAQ/C,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,YAAY,CAAG,CAAA,EACpB,IAAI,CAACC,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,UAAU,CAAGL,GAAkB,EACpC,IAAI,CAACA,cAAc,CAAGA,EACtB,IAAI,CAACpF,EAAE,CAAGA,EACV,IAAI,CAACkC,MAAM,CAAGA,CAClB,CAmIA,OAxHAiD,EAAgB/L,SAAS,CAACsM,QAAQ,CAAG,SAAUC,CAAI,EAC/C,IAAI,CAACL,QAAQ,CAAG,GAChB,IAAI,CAACC,YAAY,CAAG,IAAIK,aAAaD,AAAO,EAAPA,EACzC,EAKAR,EAAgB/L,SAAS,CAACgH,IAAI,CAAG,WAC7B,GAAI,CAAC,IAAI,CAACiF,MAAM,CACZ,MAAO,CAAA,EAKX,IAAI,CAACrF,EAAE,CAAC6F,mBAAmB,CAAC,IAAI,CAACL,aAAa,CAAE,IAAI,CAACC,UAAU,CAAE,IAAI,CAACzF,EAAE,CAAC8F,KAAK,CAAE,CAAA,EAAO,EAAG,EAE9F,EAWAX,EAAgB/L,SAAS,CAAC2M,KAAK,CAAG,SAAUC,CAAM,CAAEC,CAAM,CAAEb,CAAc,EACtE,IAAIc,QAEJ,CADA,IAAI,CAACxJ,IAAI,CAAGsJ,GAAU,EAAE,CACpB,AAAE,IAAI,CAACtJ,IAAI,EAAI,AAAqB,IAArB,IAAI,CAACA,IAAI,CAAC9B,MAAM,EAAY,IAAI,CAAC2K,YAAY,GAKhE,IAAI,CAACE,UAAU,CAAGL,GAAkB,IAAI,CAACK,UAAU,CAC/C,IAAI,CAACJ,MAAM,EACX,IAAI,CAACrF,EAAE,CAACmG,YAAY,CAAC,IAAI,CAACd,MAAM,EAE/B,IAAI,CAACE,YAAY,EAClBW,CAAAA,EAAS,IAAIN,aAAa,IAAI,CAAClJ,IAAI,CAAA,EAEvC,IAAI,CAAC2I,MAAM,CAAG,IAAI,CAACrF,EAAE,CAACoG,YAAY,GAClC,IAAI,CAACpG,EAAE,CAACqG,UAAU,CAAC,IAAI,CAACrG,EAAE,CAACsG,YAAY,CAAE,IAAI,CAACjB,MAAM,EACpD,IAAI,CAACrF,EAAE,CAACuG,UAAU,CAAC,IAAI,CAACvG,EAAE,CAACsG,YAAY,CAAE,IAAI,CAACf,YAAY,EAAIW,EAAQ,IAAI,CAAClG,EAAE,CAACwG,WAAW,EAEzF,IAAI,CAAChB,aAAa,CAAG,IAAI,CAACxF,EAAE,CACvByG,iBAAiB,CAAC,IAAI,CAACvE,MAAM,CAACW,UAAU,GAAIoD,GACjD,IAAI,CAACjG,EAAE,CAAC0G,uBAAuB,CAAC,IAAI,CAAClB,aAAa,EAElDU,EAAS,CAAA,EACF,CAAA,IAnBH,IAAI,CAACxD,OAAO,GACL,CAAA,EAmBf,EAIAyC,EAAgB/L,SAAS,CAACsJ,OAAO,CAAG,WAC5B,IAAI,CAAC2C,MAAM,GACX,IAAI,CAACrF,EAAE,CAACmG,YAAY,CAAC,IAAI,CAACd,MAAM,EAChC,IAAI,CAACA,MAAM,CAAG,CAAA,EACd,IAAI,CAACG,aAAa,CAAG,CAAA,GAEzB,IAAI,CAACF,QAAQ,CAAG,EAChB,IAAI,CAACG,UAAU,CAAG,IAAI,CAACL,cAAc,EAAI,EACzC,IAAI,CAAC1I,IAAI,CAAG,EAAE,AAClB,EAaAyI,EAAgB/L,SAAS,CAACyI,IAAI,CAAG,SAAUrD,CAAC,CAAEE,CAAC,CAAEhG,CAAC,CAAEiO,CAAC,EAC7C,IAAI,CAACpB,YAAY,GACjB,IAAI,CAACA,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG9G,EACrC,IAAI,CAAC+G,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG5G,EACrC,IAAI,CAAC6G,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG5M,EACrC,IAAI,CAAC6M,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAGqB,EAE7C,EAYAxB,EAAgB/L,SAAS,CAAC2D,MAAM,CAAG,SAAU6J,CAAI,CAAEC,CAAE,CAAEC,CAAQ,EAC3D,IAAIlM,EAAS,IAAI,CAAC2K,YAAY,CACtB,IAAI,CAACA,YAAY,CAAC3K,MAAM,CAAG,IAAI,CAAC8B,IAAI,CAAC9B,MAAM,OACnD,EAAK,IAAI,CAACyK,MAAM,IAGXzK,IAGD,CAAA,CAACgM,GAAQA,EAAOhM,GAAUgM,EAAO,CAAA,GACjCA,CAAAA,EAAO,CAAA,EAEP,CAAA,CAACC,GAAMA,EAAKjM,CAAK,GACjBiM,CAAAA,EAAKjM,CAAK,GAEVgM,CAAAA,GAAQC,CAAC,IAGbC,EAAWA,GAAY,SACvB,IAAI,CAAC9G,EAAE,CAAC+G,UAAU,CAAC,IAAI,CAAC/G,EAAE,CAAC8G,EAAS,CAAEF,EAAO,IAAI,CAACnB,UAAU,CAAE,AAACoB,CAAAA,EAAKD,CAAG,EAAK,IAAI,CAACnB,UAAU,EACpF,CAAA,GACX,EACON,CACX,IAsBIjB,EAAQ,AAACxE,IAAuGsH,KAAK,CAErHC,EAAM,AAACvN,IAA+EuN,GAAG,CAAEC,EAAM,AAACxN,IAA+EwN,GAAG,CAEpLC,EAAW,AAACzN,IAA+EyN,QAAQ,CAAEC,EAAW,AAAC1N,IAA+E0N,QAAQ,CAAEC,EAAQ,AAAC3N,IAA+E2N,KAAK,CAAEC,EAAa,AAAC5N,IAA+E4N,UAAU,CAAEC,EAAmB,AAAC7N,IAA+EO,IAAI,CAUzfuN,EAAQ,CACR,OAAU,CAAA,EACV,YAAe,CAAA,EACf,IAAO,CAAA,EACP,KAAQ,CAAA,EACR,WAAc,CAAA,EACd,UAAa,CAAA,CACjB,EACIC,EAAW,CACX,QAAW,CAAA,EACX,OAAU,CAAA,CACd,EACIC,EAAW,CACX,QACA,qBACA,YACA,YACH,CAmBGC,EAA6B,WAM7B,SAASA,EAAYC,CAAkB,EAKnC,IAAI,CAAClL,IAAI,CAAG,EAAE,CAEd,IAAI,CAACoC,MAAM,CAAG,EAEd,IAAI,CAAC+I,QAAQ,CAAG,CAAA,EAEhB,IAAI,CAACC,UAAU,CAAG,EAAE,CAEpB,IAAI,CAACxN,MAAM,CAAG,EAAE,CAEhB,IAAI,CAACyN,cAAc,CAAG,CAAC,EAEvB,IAAI,CAACnJ,KAAK,CAAG,EACb,IAAI,CAACgJ,kBAAkB,CAAGA,EAC1B,IAAI,CAACI,QAAQ,CAAG,CACZC,UAAW,EACXC,UAAW,EACXC,UAAW,UACXC,SAAU,CAAA,EACVC,gBAAiB,CAAA,EACjBC,mBAAoB,CAAA,EACpBC,MAAO,CACHC,cAAe,CAAA,EACfC,qBAAsB,CAAA,EACtBC,UAAW,CAAA,EACXC,eAAgB,CAAA,EAChBC,WAAY,CAAA,EACZC,gBAAiB,CAAA,CACrB,CACJ,CACJ,CAqpCA,OAvoCAlB,EAAYmB,WAAW,CAAG,SAAUlK,CAAK,CAAEE,CAAM,EAG7C,MAAO,CACH,EAAIF,EAAO,EAAG,EAAG,EACjB,EAAG,CAAE,CAAA,EAAIE,CAAK,EAAI,EAAG,EACrB,EAAG,EAAG,GAAmB,EACzB,GAAI,EAAG,GAA8B,EACxC,AACL,EAIA6I,EAAYoB,gBAAgB,CAAG,SAAUzO,CAAM,EAC3C,IAAI0O,EACAC,EACA/K,SACJ,AAAI5D,EAAO0C,OAAO,EACdgM,EAAY,CAAC,CAAC1O,EAAOG,OAAO,CAACyO,QAAQ,CACrCD,EAAS,AAAC3O,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAClCN,EAAOmC,SAAS,CAAC,KACjB,KAAK,CAAA,GACLnC,EAAOG,OAAO,CAACwO,KAAK,EACpB3O,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAC1ByB,EAAI,AAAC8K,CAAAA,EAAY1O,EAAOoC,IAAI,CAAIuM,GAAS3O,EAAOG,OAAO,CAACiC,IAAI,EACvD9B,MAAM,CACPN,AAAgB,YAAhBA,EAAO0B,IAAI,CACXkC,GAAK,GAEA5D,AAAgB,YAAhBA,EAAO0B,IAAI,CAChBkC,GAAK,EAEAsJ,CAAK,CAAClN,EAAO0B,IAAI,CAAC,EACvBkC,CAAAA,GAAK,CAAA,EAEFA,GAEJ,CACX,EASAyJ,EAAYvO,SAAS,CAAC+P,aAAa,CAAG,WAClC,OAAO,IAAI,CAACnB,QAAQ,CAAC3E,UAAU,EAAI6D,EAAIkC,gBAAgB,EAAI,CAC/D,EAIAzB,EAAYvO,SAAS,CAACiQ,UAAU,CAAG,SAAU5O,CAAO,EAI1C,eAAgBA,GAClBA,CAAAA,EAAQ4I,UAAU,CAAG,CAAA,EAEzBgE,EAAM,CAAA,EAAM,IAAI,CAACW,QAAQ,CAAEvN,EAC/B,EAKAkN,EAAYvO,SAAS,CAACkE,cAAc,CAAG,SAAUlD,CAAK,EAClD,IAAIkP,EAAU,IAAI,CAACA,OAAO,CACtBpL,EAAI,CACH,CAAA,IAAI,CAAC8J,QAAQ,CAACK,eAAe,GAGlCjO,EAAME,MAAM,CAACT,OAAO,CAAC,SAAUS,CAAM,EAC7BA,EAAO0C,OAAO,EACdkB,CAAAA,GAAKyJ,EAAYoB,gBAAgB,CAACzO,EAAM,CAEhD,GACAgP,GAAWA,EAAQ5D,QAAQ,CAACxH,GAChC,EAIAyJ,EAAYvO,SAAS,CAACmQ,6BAA6B,CAAG,SAAUjP,CAAM,EAClE,IAAIgP,EAAU,IAAI,CAACA,OAAO,CACtBpL,EAAI,CACH,CAAA,IAAI,CAAC8J,QAAQ,CAACK,eAAe,GAG9B/N,EAAO0C,OAAO,EACdkB,CAAAA,EAAIyJ,EAAYoB,gBAAgB,CAACzO,EAAM,EAE3CgP,GAAWA,EAAQ5D,QAAQ,CAACxH,GAChC,EAKAyJ,EAAYvO,SAAS,CAACgE,KAAK,CAAG,WAC1B,IAAI4C,EAAK,IAAI,CAACA,EAAE,AAChBA,CAAAA,GAAMA,EAAG5C,KAAK,CAAC4C,EAAGwJ,gBAAgB,CAAGxJ,EAAGyJ,gBAAgB,CAC5D,EAOA9B,EAAYvO,SAAS,CAACsQ,cAAc,CAAG,SAAUpP,CAAM,CAAEqP,CAAI,EACzD,IACI3O,EACAY,EAsBAgO,EACAC,EAKArL,EACAE,EACAjG,EACAqR,EAIAC,EAOAC,EA5CAzJ,EAAQ,IAAI,CAGZ7D,EAAO,IAAI,CAACA,IAAI,CAAEsL,EAAW,IAAI,CAACA,QAAQ,CAAEsB,EAAU,IAAI,CAACA,OAAO,CAAEW,EAAW3P,EAAO4P,aAAa,EAC/F5P,AAAmC,aAAnCA,EAAO4P,aAAa,CAAClI,IAAI,CAAC,KAAsB5H,EAAQE,EAAOF,KAAK,CAAEK,EAAUH,EAAOG,OAAO,CAAE0P,EAAS7P,EAAO6P,MAAM,CAAElP,EAAQX,EAAOW,KAAK,CAAEuC,EAAQlD,EAAOkD,KAAK,CAAEwL,EAAY,CAAC,CAACvO,EAAQyO,QAAQ,CAAEkB,EAAU3P,EAAQiC,IAAI,CAAE2N,EAAY/P,EAAOW,KAAK,CAACqP,WAAW,GAEpQC,EAAOF,EAAUlP,GAAG,CAAIb,CAAAA,EAAOW,KAAK,CAACuP,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU/O,GAAG,CAAIhB,CAAAA,EAAOW,KAAK,CAACuP,cAAc,EAAI,CAAA,EAAIE,EAAYpQ,EAAOkD,KAAK,CAAC8M,WAAW,GAAIK,EAAOD,EAAUvP,GAAG,CAAIb,CAAAA,EAAOkD,KAAK,CAACgN,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUpP,GAAG,CAAIhB,CAAAA,EAAOkD,KAAK,CAACgN,cAAc,EAAI,CAAA,EAAIvB,EAAQ,AAAC3O,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAAMhC,EAAQwO,KAAK,EAAI3O,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOoO,EAAQ,AAACvQ,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAAMhC,EAAQoQ,KAAK,EAAIvQ,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOqO,EAAQ,AAACxQ,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAAMhC,EAAQqQ,KAAK,EAAIxQ,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOsO,EAAS,CAAC9B,GAASA,AAAiB,IAAjBA,EAAMrO,MAAM,CAU9pBoQ,EAAevQ,EAAQuQ,YAAY,CAInCrO,EAASrC,EAAOqC,MAAM,EAAI,CAAA,EAAOsO,EAAQjC,EAAY1O,EAAOoC,IAAI,CAAIuM,GAASmB,EAAUc,EAAc,CAAE1M,EAAGlC,OAAOC,SAAS,CAAEmC,EAAG,CAAE,EAAGyM,EAAe,CAAE3M,EAAG,CAAClC,OAAOC,SAAS,CAAEmC,EAAG,CAAE,EAA2C0M,EAAiB,AAAuB,KAAA,IAAhBhR,EAAMtC,KAAK,CAAkBuT,EAAY7D,CAAK,CAAClN,EAAO0B,IAAI,CAAC,CAAEsP,EAAW7Q,EAAQ6Q,QAAQ,EAAI,IAAKC,EAAQ9Q,EAAQ8Q,KAAK,EAAI,CAAA,EAAO7Q,EAAYD,EAAQC,SAAS,CAAE2I,EAAa,IAAI,CAAC8F,aAAa,GAClbtK,EAAYvE,EAAOF,KAAK,CAACyE,SAAS,CAClC2M,EAAQ,CAAA,EACRC,EAAQ,CAAA,EAIRC,EAAU,EACVC,EAAY,CAAA,EAMZC,EAAI,GACJC,GAAK,CAAA,EACLC,GAAK,CAAA,EAELC,GAAa,CAAA,EACbC,GAAa,CAAA,EACbC,GAAS,CAAA,EACTC,GAAY,CAAA,EACZC,GAAY,CAAA,EACZC,GAAa,CAAA,EAEbC,GAAe,CAAA,EACfC,GAAU,CAAA,EACVC,GAAO,EACX,GAAI9R,CAAAA,EAAQ+R,SAAS,GAAI/R,CAAAA,EAAQ+R,SAAS,CAAC5R,MAAM,CAAG,CAAA,GAGhDH,EAAQ6R,OAAO,EACfA,CAAAA,GAAU7R,AAAoB,UAApBA,EAAQgS,OAAO,CACrBhS,EAAQ6R,OAAO,CAAGhS,EAAOoS,iBAAiB,CAC1CjS,EAAQ6R,OAAO,AAAD,EAElBf,IACAvB,EAAa,EAAE,CACfuB,EAAM1R,OAAO,CAAC,SAAU8S,CAAI,CAAEf,CAAC,EAC3B,GAAIe,EAAKzI,KAAK,CAAE,CACZ,IAAI0I,EAAY1I,EAAMyI,EAAKzI,KAAK,EAAE2I,IAAI,AACtCD,CAAAA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChB5C,CAAU,CAAC4B,EAAE,CAAGgB,EACXP,IAAgB,AAAsB,KAAA,IAAfM,EAAKG,KAAK,EAClCT,CAAAA,GAAeO,CAAQ,CAE/B,CACJ,GACKP,KAGDA,GAAenI,EAFI,AAAC5J,EAAOyS,YAAY,EAAIzS,EAAOyS,YAAY,GAAGC,IAAI,EAC7D1S,EAAO4J,KAAK,EACc2I,IAAI,CACtCR,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,MAGvBjS,EAAM6D,QAAQ,EACdY,CAAAA,EAAYvE,EAAOF,KAAK,CAAC2E,UAAU,AAAD,EAEtCzE,EAAO2S,mBAAmB,CAAG3Q,OAAOC,SAAS,CAK7C,IAAI2Q,GAAY,SAAUhJ,CAAK,EACnBA,IACAyF,EAAKwD,SAAS,CAACtL,IAAI,CAACqC,CAAK,CAAC,EAAE,EAChCyF,EAAKwD,SAAS,CAACtL,IAAI,CAACqC,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAACtL,IAAI,CAACqC,CAAK,CAAC,EAAE,EAC5ByF,EAAKwD,SAAS,CAACtL,IAAI,CAACqC,CAAK,CAAC,EAAE,EAEpC,EAKIkJ,GAAU,SAAU5O,CAAC,CACrBE,CAAC,CACD2O,CAAa,CACbpF,CAAS,CACT/D,CAAK,EACiB,KAAK,IAAnB+D,GAAwBA,CAAAA,EAAY,CAAA,EACxCiF,GAAUhJ,GAEK,IAAfb,GAAqB,CAAA,CAAC2E,EAASM,kBAAkB,EACjDqB,EAAK2D,eAAe,AAAD,IACnB9O,GAAK6E,EACL3E,GAAK2E,EACL4E,GAAa5E,GAEb2E,EAASK,eAAe,EAAIiB,GAC5BA,EAAQzH,IAAI,CAACrD,EAAGE,EAAG2O,GAAAA,EAAuBpF,GAC1CsE,IAAQ,IAGR7P,EAAKmF,IAAI,CAACrD,GACV9B,EAAKmF,IAAI,CAACnD,GACVhC,EAAKmF,IAAI,CAACwL,EAAgBhK,EAAa,GACvC3G,EAAKmF,IAAI,CAACoG,GAElB,EAIIsF,GAAe,WACP5D,EAAK6D,QAAQ,CAAC5S,MAAM,EACpB+O,CAAAA,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC5S,MAAM,CAAG,EAAE,CAACiM,EAAE,CAAGnK,EAAK9B,MAAM,EAAI2R,EAAG,CAE3E,EAKIkB,GAAe,WAKP9D,CAAAA,CAAAA,EAAK6D,QAAQ,CAAC5S,MAAM,EACpB+O,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC5S,MAAM,CAAG,EAAE,CAACgM,IAAI,GAAMlK,CAAAA,EAAK9B,MAAM,EAAI2R,EAAG,CAAC,IAG7EgB,KACA5D,EAAK6D,QAAQ,CAAC3L,IAAI,CAAC,CACf+E,KAAMlK,EAAK9B,MAAM,EAAI2R,EACzB,GACJ,EAKImB,GAAW,SAAUlP,CAAC,CACtBE,CAAC,CACDiP,CAAC,CACDC,CAAC,CACD1J,CAAK,EACDgJ,GAAUhJ,GACdkJ,GAAQ5O,EAAImP,EAAGjP,GACfwO,GAAUhJ,GACVkJ,GAAQ5O,EAAGE,GACXwO,GAAUhJ,GACVkJ,GAAQ5O,EAAGE,EAAIkP,GACfV,GAAUhJ,GACVkJ,GAAQ5O,EAAGE,EAAIkP,GACfV,GAAUhJ,GACVkJ,GAAQ5O,EAAImP,EAAGjP,EAAIkP,GACnBV,GAAUhJ,GACVkJ,GAAQ5O,EAAImP,EAAGjP,EACnB,EAIA,GAFA+O,KAEI9Q,GAAUA,EAAO/B,MAAM,CAAG,EAAG,CAG7B+O,EAAK2D,eAAe,CAAG,CAAA,EAEvB3D,EAAK7C,QAAQ,CAAG,YAEZnK,CAAM,CAAC,EAAE,CAACkR,IAAI,EAAIlR,CAAM,CAAC,EAAE,CAACkR,IAAI,CAACC,YAAY,EAC7CnR,EAAOoR,IAAI,CAAC,SAAUrV,CAAC,CAAEiO,CAAC,EACtB,GAAIjO,EAAEmV,IAAI,CAAE,CACR,GAAInV,EAAEmV,IAAI,CAACC,YAAY,CACnBnH,EAAEkH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEX,GAAIpV,EAAEmV,IAAI,CAACC,YAAY,CACnBnH,EAAEkH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEf,CACA,OAAO,CACX,GAEJnR,EAAO9C,OAAO,CAAC,SAAUmU,CAAK,EAC1B,IACIC,EACAC,EAFAC,EAAQH,EAAMG,KAAK,CAGvB,GAAI,AAAiB,KAAA,IAAVA,GACP,CAACC,MAAMD,IACPH,AAAY,OAAZA,EAAMtP,CAAC,EACPsP,EAAMK,SAAS,CAAE,CACjB,IAAIrT,EAAKgT,EAAMK,SAAS,CACpBzS,EAAKZ,EAAGwD,CAAC,CACT8P,EAAM1S,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC1B2S,EAAKvT,EAAG0D,CAAC,CACT8P,EAAMD,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC1BE,EAAKzT,EAAG4D,KAAK,CACbA,EAAQ6P,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BC,EAAK1T,EAAG8D,MAAM,CACdA,EAAS4P,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAKjCT,EAASC,AAJTA,CAAAA,EAAY9T,EAAMuU,UAAU,CACxBX,EAAM1T,MAAM,CACPsU,YAAY,CAACZ,GAClBE,EAAYF,EAAM1T,MAAM,CAACyS,YAAY,CAACiB,EAAK,CAC7B,CAAC,eAAe,EAAI,EAEtC/B,GAAS/H,EAAMgK,EAAUlB,IAAI,EAAEH,IAAI,CACnCZ,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IAQT3R,EAAO6E,EAAE,CAAC,aACV8O,EAASA,GAAU,EACnBpE,EAAS3F,EAAMgK,EAAUW,MAAM,EAAEhC,IAAI,CACrChD,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACb6D,GAASY,EAAKE,EAAK5P,EAAOE,EAAQ+K,GAClCoE,GAAU,GAUV3T,EAAO6E,EAAE,CAAC,YAAc/E,EAAM6D,QAAQ,GACtCqQ,EAAMrT,EAAMuE,GAAG,CAAG8O,EAClBE,EAAMhR,EAAMgC,GAAG,CAAGgP,EAClB5P,EAAQ,CAACA,EACTE,EAAS,CAACA,GAEd4O,GAASY,EAAML,EAAQO,EAAMP,EAAQrP,EAASqP,AAAS,EAATA,EAAanP,EAAUmP,AAAS,EAATA,EAAahC,GACtF,CACJ,GACAsB,KACA,MACJ,CA4QA,KAAO3B,EAAIX,EAAMrQ,MAAM,CAAG,GAElBkU,AAAY,UADFC,AA5QJ,WAEV,GAAI,AAAa,KAAA,IADbtW,CAAAA,EAAIwS,CAAK,CAAC,EAAEW,EAAE,AAAD,EAEb,MAAO,WAMX,GAAIR,EACA,MAAO,QAeX,IAkHQ4D,EAlHJC,EAAe7E,GAAWA,CAAO,CAACwB,EAAE,OA+CxC,CA9CI,CAACb,GAAU3D,EAAS6H,EAAc,CAAA,IAC9BA,EAAa/K,KAAK,GAClB+H,GAAS/H,EAAM+K,EAAa/K,KAAK,EAAE2I,IAAI,CACvCZ,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,KAGjBlB,GACAvM,EAAI/F,CAAC,CAAC,EAAE,CACRiG,EAAIjG,CAAC,CAAC,EAAE,CACJwS,CAAK,CAACW,EAAI,EAAE,EACZE,CAAAA,GAAKb,CAAK,CAACW,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBX,CAAK,CAACW,EAAI,EAAE,EACZC,CAAAA,GAAKZ,CAAK,CAACW,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBnT,EAAEmC,MAAM,EAAI,IACZkP,EAAIrR,CAAC,CAAC,EAAE,CACJA,CAAC,CAAC,EAAE,CAAGkR,EAAKpG,IAAI,EAChBoG,CAAAA,EAAKpG,IAAI,CAAG9K,CAAC,CAAC,EAAE,AAAD,EAEfA,CAAC,CAAC,EAAE,CAAGkR,EAAKrG,IAAI,EAChBqG,CAAAA,EAAKrG,IAAI,CAAG7K,CAAC,CAAC,EAAE,AAAD,KAKvB+F,EAAI/F,EACJiG,EAAImM,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAACe,EAAE,CACtDX,CAAK,CAACW,EAAI,EAAE,EACZE,CAAAA,GAAKb,CAAK,CAACW,EAAI,EAAE,AAAD,EAEhBX,CAAK,CAACW,EAAI,EAAE,EACZC,CAAAA,GAAKZ,CAAK,CAACW,EAAI,EAAE,AAAD,EAEhBd,GAASA,EAAMlQ,MAAM,GACrBkP,EAAIgB,CAAK,CAACc,EAAE,CACRd,CAAK,CAACc,EAAE,CAAGjC,EAAKpG,IAAI,EACpBoG,CAAAA,EAAKpG,IAAI,CAAGuH,CAAK,CAACc,EAAE,AAAD,EAEnBd,CAAK,CAACc,EAAE,CAAGjC,EAAKrG,IAAI,EACpBqG,CAAAA,EAAKrG,IAAI,CAAGwH,CAAK,CAACc,EAAE,AAAD,IAI3B,AAACZ,GAAiBxM,AAAM,OAANA,GAAcE,AAAM,OAANA,IAIhCoN,IAAMA,IAAMvB,GAAQuB,IAAMrB,GAC1BsB,CAAAA,GAAa,CAAA,CAAG,EAEhBF,IAAMA,IAAMtB,GAAQsB,IAAMpB,GAC1BuB,CAAAA,GAAa,CAAA,CAAG,EAEhB/B,GACIc,GACArM,CAAAA,EAAIjG,EAAEyW,KAAK,CAAC,EAAG,EAAC,EAEpBnF,EAAM,AAAyC,OAAxC/O,CAAAA,EAAKV,EAAOmC,SAAS,CAAC,MAAO,CAAA,EAAI,GAAezB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC4Q,EAAE,CACrFlN,EAAI,AAAC,CAAA,AAA0C,OAAzC9C,CAAAA,EAAKtB,EAAOmC,SAAS,CAAC,OAAQ,CAAA,EAAI,GAAeb,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAACgQ,EAAE,AAAD,GAAM,GAErF5C,IACLxK,EAAI/F,EAAE+F,CAAC,CAEPuL,EAAMrL,AADNA,CAAAA,EAAIjG,EAAE0W,MAAM,AAAD,EACD1W,EAAEiG,CAAC,QAEbiM,GAGA,MADAC,GAEAuB,CAAAA,GAAYzN,GAAKiM,GAAQjM,GAAKkM,CAAG,EAGjC,AAACT,GAAWgC,KAGZ3N,EAAIiM,GAAQU,EAAa3M,CAAC,CAAGiM,IAC7BU,EAAa3M,CAAC,CAAGA,EACjB2M,EAAazM,CAAC,CAAGA,GAEjBF,EAAI+L,GAAQW,EAAY1M,CAAC,CAAG+L,IAC5BW,EAAY1M,CAAC,CAAGA,EAChB0M,EAAYxM,CAAC,CAAGA,GAEhBA,AAAM,OAANA,GAAcsM,GACP,WAKPtM,AAAM,OAANA,GAAe,CAAA,AAACyN,KAAalB,CAAAA,EAAMrQ,MAAM,CAAG,CAAA,GAC3CmR,IAAeC,EAAS,GAOzB7B,CAAAA,GAAY2B,CAAAA,IAAMvB,GAAQ/L,GAAK+L,CAAG,GACjCsB,CAAAA,IAAMpB,GAAQjM,GAAKiM,CAAG,GACvB,CAACN,GAAY3L,GAAK+L,GAAU/L,GAAKiM,CAAK,GACtCyB,CAAAA,GAAY,CAAA,CAAG,EAEf,AAACA,IAAcH,IAAeC,KAG9BM,IAAW9N,EAAIqN,GAAKS,IACpBmB,KAGAlC,IAEAA,EAAMrO,IAAI,CAAC,SACXyP,CAAI,CAAEf,CAAC,EACH,IAAIwD,EAAO7D,CAAK,CAACK,EAAI,EAAE,OACvB,AAAIN,AAAa,MAAbA,EACA,AAA0B,KAAA,IAAfqB,EAAKG,KAAK,EACjBtO,GAAKmO,EAAKG,KAAK,GACX9C,CAAU,CAAC4B,EAAE,EACZ,CAAA,CAACwD,GAAQ5Q,GAAK4Q,EAAKtC,KAAK,AAAD,GACxBkC,CAAAA,EAAchF,CAAU,CAAC4B,EAAE,AAAD,EAEvB,CAAA,GAIf,AAA0B,KAAA,IAAfe,EAAKG,KAAK,EAAoBpO,GAAKiO,EAAKG,KAAK,GAChD9C,CAAU,CAAC4B,EAAE,EACZ,CAAA,CAACwD,GAAQ1Q,GAAK0Q,EAAKtC,KAAK,AAAD,GACxBkC,CAAAA,EAAchF,CAAU,CAAC4B,EAAE,AAAD,EAEvB,CAAA,EAGf,GACAK,GAAS+C,GAAe3C,IAAgBJ,IAGxC,CAACjE,EAASM,kBAAkB,GAC5BqB,EAAK2D,eAAe,CAAG,CAAA,EACvB9O,EAAIvD,EAAMoU,QAAQ,CAAC7Q,EAAG,CAAA,GACtBE,EAAIlB,EAAM6R,QAAQ,CAAC3Q,EAAG,CAAA,GAQlBF,EAAIK,GAIA8K,AAAkB,WAAlBA,EAAK7C,QAAQ,GACN,YAUf6C,EAAK2F,UAAU,EAAIpD,IAYfV,AAAU,CAAA,IAAVA,GACAlR,CAAAA,EAAO2S,mBAAmB,CAAG1N,KAAKpE,GAAG,CAACb,EAAO2S,mBAAmB,CAAE1N,KAAKgQ,GAAG,CAAC/Q,EAAIgN,GAAM,EAKzF,CAACxD,EAASM,kBAAkB,EAC5B,CAACN,EAASK,eAAe,EACxBmD,GAASjM,AApcsL,EAoctLA,KAAKgQ,GAAG,CAAC/Q,EAAIgN,IACtBC,GAASlM,AArc0M,EAqc1MA,KAAKgQ,GAAG,CAAC7Q,EAAI+M,KACnBzD,EAASO,KAAK,CAACM,eAAe,EAC9B,EAAE6C,EAEC,iBAEPL,IACAzB,EAASG,GAAO,EACZA,CAAAA,AAAQ,CAAA,IAARA,GAAiB,AAAe,KAAA,IAARA,CAAkB,IAEtCH,EADAlL,EAAI,EACKA,EAGA,GAGZ,CAAA,AAACuL,GAAYjB,CAAQ,IACtBxL,EAAMgS,WAAW,EAEjB5F,CAAAA,EAASrK,KAAKjE,GAAG,CAACZ,AAAc,OAAdA,EAAqBiQ,EAAOjQ,EAC9CiQ,EAAI,EAEH3C,EAASM,kBAAkB,EAC5BsB,CAAAA,EAASpM,EAAM6R,QAAQ,CAACzF,EAAQ,CAAA,EAAI,EAGxCwD,GAAQ5O,EAAGoL,EAAQ,EAAG,EAAGqC,KAKzBxR,EAAQgV,IAAI,EAAI,CAACrD,IACjBgB,GAAQ5O,EAAGiN,EAAO,EAAG,EAAGQ,IAE5BmB,GAAQ5O,EAAGE,EAAG,EAAGpE,AAAgB,WAAhBA,EAAO0B,IAAI,CAAiB8N,GAAK,EAAK,EAAGmC,IAS1DT,EAAQhN,EACRiN,EAAQ/M,EACRiN,EAAY,CAAA,EACZS,GAAa,CAAA,GA/HF,YAZPqB,KACO,YAnBA,YA7BPA,KACO,WA0Lf,MAYIzF,EAASO,KAAK,CAACM,eAAe,EAC9B6G,QAAQC,GAAG,CAAC,kBAAmBjE,GAEnC,IAAIkE,GAAsB,SAAU5B,CAAK,CACrC6B,CAAO,EAQP,GAPS7H,EAASM,kBAAkB,GAC5BqB,EAAK2D,eAAe,CAAG,CAAA,EAC3BU,EAAMxP,CAAC,CAAGvD,EAAMoU,QAAQ,CAACrB,EAAMxP,CAAC,CAAE,CAAA,GAClCwP,EAAMtP,CAAC,CAAGlB,EAAM6R,QAAQ,CAACrB,EAAMtP,CAAC,CAAE,CAAA,IAIlCmR,EAAS,CACTtP,EAAM7D,IAAI,CAAG,CAACsR,EAAMxP,CAAC,CAAEwP,EAAMtP,CAAC,CAAE,EAAG,EAAE,CAACoR,MAAM,CAACvP,EAAM7D,IAAI,EACvD,MACJ,CACA0Q,GAAQY,EAAMxP,CAAC,CAAEwP,EAAMtP,CAAC,CAAE,EAAG,EACjC,CACI,EAACiN,GACDX,AAAiB,CAAA,IAAjBA,GACA1Q,AAAoB,eAApBA,EAAOwM,QAAQ,GACXoE,EAAY1M,CAAC,CAAGlC,OAAOC,SAAS,EAEhCqT,GAAoB1E,EAAa,CAAA,GAEjCC,EAAa3M,CAAC,CAAG,CAAClC,OAAOC,SAAS,EAClCqT,GAAoBzE,IAG5BoC,KACJ,EAQA5F,EAAYvO,SAAS,CAAC2W,UAAU,CAAG,SAAU7R,CAAC,EAC1C,IAAI4J,EAAa,IAAI,CAACA,UAAU,CAC5BxN,EAAS,IAAI,CAACA,MAAM,CACpB0N,EAAW,IAAI,CAACA,QAAQ,AACxB1N,CAAAA,EAAOM,MAAM,CAAG,GACZN,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAAC0U,UAAU,EACpChV,CAAAA,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAACoV,QAAQ,CAAGlI,EAAWlN,MAAM,AAAD,EAGzDoN,EAASO,KAAK,CAACE,oBAAoB,EACnCiH,QAAQO,IAAI,CAAC,YAAc/R,EAAElC,IAAI,CAAG,WAExC,IAAI9C,EAAM,CACFsU,SAAU,EAAE,CACZ0C,WAAYpI,EAAWlN,MAAM,CAI7BuS,UAAW,EAAE,CACb7S,OAAQ4D,EACRoF,KAAMhH,OAAOC,SAAS,CACtBgH,KAAM,CAACjH,OAAOC,SAAS,CACvB+S,WAAYpR,EAAAA,EAAEzD,OAAO,CAAC0V,MAAM,EACxBjS,AAA6B,CAAA,IAA7BA,EAAEzD,OAAO,CAAC0V,MAAM,CAACC,OAAO,CAE5BC,YAAa,CAAA,EACbvJ,SAAUnH,CAAiB,CAACzB,EAAElC,IAAI,CAAC,EAAI,YAC3C,CACAkC,CAAAA,EAAEpG,KAAK,EAAIwC,EAAOM,MAAM,CACxBN,EAAOuH,IAAI,CAAC3I,GAGZoB,CAAM,CAAC4D,EAAEpG,KAAK,CAAC,CAAGoB,EAGtB,IAAI,CAACwQ,cAAc,CAACxL,EAAGhF,GACnB8O,EAASO,KAAK,CAACE,oBAAoB,EACnCiH,QAAQY,OAAO,CAAC,YAAcpS,EAAElC,IAAI,CAAG,UAE/C,EAOA2L,EAAYvO,SAAS,CAACmX,KAAK,CAAG,WAC1B,IAAIjH,EAAU,IAAI,CAACA,OAAO,AAC1B,CAAA,IAAI,CAAC5M,IAAI,CAAG,EAAE,CACd,IAAI,CAACoL,UAAU,CAAG,EAAE,CACpB,IAAI,CAACxN,MAAM,CAAG,EAAE,CACZgP,GACAA,EAAQ5G,OAAO,EAEvB,EAOAiF,EAAYvO,SAAS,CAACoX,QAAQ,CAAG,SAAUtV,CAAI,EAC3C,IAAIgH,EAAS,IAAI,CAACA,MAAM,CACxB,GAAKA,GAGL,IAAImB,EAAa,IAAI,CAAC8F,aAAa,GACnCjH,EAAO4B,UAAU,CAAC,aAAc5I,EAAKuV,MAAM,CAAGpN,GAC9CnB,EAAO4B,UAAU,CAAC,WAAY5I,EAAKC,GAAG,EACtC+G,EAAO4B,UAAU,CAAC,cAAe5I,EAAKwV,eAAe,CAAGrN,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmB5I,EAAKyV,UAAU,EACpDzO,EAAO4B,UAAU,CAAC,WAAY5I,EAAKsE,GAAG,CAAG6D,GACzCnB,EAAO4B,UAAU,CAAC,WAAY5I,EAAKwC,GAAG,CAAG2F,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAAC5I,EAAK0V,KAAK,EAC/C1O,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAAC5I,EAAKsU,WAAW,EACnDtN,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAAC5I,EAAK2V,QAAQ,EACvD,EAOAlJ,EAAYvO,SAAS,CAAC0X,QAAQ,CAAG,SAAU5V,CAAI,EAC3C,IAAIgH,EAAS,IAAI,CAACA,MAAM,CACxB,GAAKA,GAGL,IAAImB,EAAa,IAAI,CAAC8F,aAAa,GACnCjH,EAAO4B,UAAU,CAAC,aAAc5I,EAAKuV,MAAM,CAAGpN,GAC9CnB,EAAO4B,UAAU,CAAC,WAAY5I,EAAKC,GAAG,EACtC+G,EAAO4B,UAAU,CAAC,cAAe5I,EAAKwV,eAAe,CAAGrN,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmB5I,EAAKyV,UAAU,EACpDzO,EAAO4B,UAAU,CAAC,WAAY5I,EAAKsE,GAAG,CAAG6D,GACzCnB,EAAO4B,UAAU,CAAC,WAAY5I,EAAKwC,GAAG,CAAG2F,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAAC5I,EAAK0V,KAAK,EAC/C1O,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAAC5I,EAAKsU,WAAW,EACnDtN,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAAC5I,EAAK2V,QAAQ,EACvD,EASAlJ,EAAYvO,SAAS,CAAC2X,YAAY,CAAG,SAAUC,CAAG,CAAEC,CAAW,EAC3D,IAAI/O,EAAS,IAAI,CAACA,MAAM,CACnBA,IAGLA,EAAO4B,UAAU,CAAC,eAAgBkN,GAClC9O,EAAO4B,UAAU,CAAC,sBAAuBmN,GAC7C,EAMAtJ,EAAYvO,SAAS,CAAC8X,WAAW,CAAG,SAAU9W,CAAK,EAC/C,IAAImG,EAAQ,IAAI,CACZP,EAAK,IAAI,CAACA,EAAE,CACZgI,EAAW,IAAI,CAACA,QAAQ,CACxB9F,EAAS,IAAI,CAACA,MAAM,CACpBoH,EAAU,IAAI,CAACA,OAAO,CACtBjG,EAAa,IAAI,CAAC8F,aAAa,GACnC,IAAI/O,EAKA,MAAO,CAAA,CAJP,CAAA,IAAI,CAACwE,KAAK,CAAGxE,EAAM+W,UAAU,CAAG9N,EAChC,IAAI,CAACvE,MAAM,CAAG1E,EAAMgX,WAAW,CAAG/N,EAKtC,IAAIvE,EAAS,IAAI,CAACA,MAAM,CACpBF,EAAQ,IAAI,CAACA,KAAK,CACtB,GAAI,CAACoB,GAAM,CAACkC,GAAU,CAACtD,GAAS,CAACE,EAC7B,MAAO,CAAA,CAEPkJ,CAAAA,EAASO,KAAK,CAACC,aAAa,EAC5BkH,QAAQO,IAAI,CAAC,gBAEjBjQ,EAAG3C,MAAM,CAACuB,KAAK,CAAGA,EAClBoB,EAAG3C,MAAM,CAACyB,MAAM,CAAGA,EACnBoD,EAAO9B,IAAI,GACXJ,EAAGqR,QAAQ,CAAC,EAAG,EAAGzS,EAAOE,GACzBoD,EAAOqC,UAAU,CAACoD,EAAYmB,WAAW,CAAClK,EAAOE,IAC7CkJ,EAASE,SAAS,CAAG,GAAK,CAAC,AAACxO,IAA+E4X,IAAI,EAC/GtR,EAAGkI,SAAS,CAACF,EAASE,SAAS,EAE/BoB,IACAA,EAAQvD,KAAK,CAAC,IAAI,CAACrJ,IAAI,CAAE,kBAAmB,GAC5C4M,EAAQlJ,IAAI,IAEhB8B,EAAOoC,WAAW,CAAClK,EAAM6D,QAAQ,EAEjC,IAAI,CAAC3D,MAAM,CAACT,OAAO,CAAC,SAAUqE,CAAC,CAAEqT,CAAE,EAI/B,IAHIvW,EACAY,EACA2S,EAkBAiD,EACAC,EACAtJ,EAnBA1N,EAAUyD,EAAE5D,MAAM,CAACG,OAAO,CAC1BiX,EAAejX,EAAQ0V,MAAM,CAC7BjI,EAAa,AAA6B,KAAA,IAAtBzN,EAAQyN,SAAS,CACjCzN,EAAQyN,SAAS,CACjB,EACJxN,EAAYD,EAAQC,SAAS,CAC7BiX,EAAexK,EAASzM,GACxBkX,EAAU1T,EAAE5D,MAAM,CAACkD,KAAK,CAACqU,YAAY,CAACnX,GAEtC2V,EAAc9I,EAAiB9M,EAAQ0V,MAAM,CAAG1V,EAAQ0V,MAAM,CAACC,OAAO,CAAG,KACzElS,EAAAA,EAAE5D,MAAM,CAACW,KAAK,CAAC6W,QAAQ,EAAU,KACjC5T,EAAE5D,MAAM,CAAC2S,mBAAmB,CACxB,EAAK,CAAA,AAACxS,CAAAA,EAAQ0V,MAAM,CAChB1V,EAAQ0V,MAAM,CAAC4B,MAAM,CACrB,EAAC,GAAM,EAAC,GAChBC,EAAezR,EAAMwH,cAAc,CAAC,AAAC2J,GAAgBA,EAAaO,MAAM,EACpE/T,EAAE5D,MAAM,CAAC2X,MAAM,CAAC,EAAI1R,EAAMwH,cAAc,CAACmK,MAAM,CAInDrI,EAAS,EAAE,CACf,GAAI3L,AAAsB,IAAtBA,EAAEsP,QAAQ,CAAC5S,MAAM,EACjBsD,EAAEsP,QAAQ,CAAC,EAAE,CAAC5G,IAAI,GAAK1I,EAAEsP,QAAQ,CAAC,EAAE,CAAC3G,EAAE,CA0F3C,CAAA,GAvFImL,EAAaG,OAAO,GACpBnS,EAAGoS,WAAW,CAACpS,EAAGqS,UAAU,CAAEL,EAAaM,MAAM,EACjDpQ,EAAO4C,UAAU,CAACkN,EAAaM,MAAM,GAErClY,EAAMuU,UAAU,CACZzQ,EAAE5D,MAAM,CAACiD,WAAW,GAAM,CAAA,AAAgC,OAA/BvC,CAAAA,EAAKkD,EAAE5D,MAAM,CAACF,KAAK,CAACG,KAAK,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuC,WAAW,AAAD,GAExG,OAAOW,EAAE5D,MAAM,CAACiD,WAAW,CAC3BW,EAAE5D,MAAM,CAACiD,WAAW,CAAGW,EAAE5D,MAAM,CAACiY,SAAS,CAAC,cAAe,UAAW,UAAW,EAAGnY,EAAMoY,WAAW,EAAEC,QAAQ,CAAC,sBAC9GtK,EAAYjK,EAAE5D,MAAM,CAACiD,WAAW,CAACmV,QAAQ,CAAC,QAC1CxU,EAAE5D,MAAM,CAACiD,WAAW,CAACmF,OAAO,GAC5BxE,EAAE5D,MAAM,CAACiD,WAAW,CAAG,AAAgC,OAA/B3B,CAAAA,EAAKsC,EAAE5D,MAAM,CAACF,KAAK,CAACG,KAAK,AAAD,GAAeqB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG2B,WAAW,EAGtG4K,EAAY,AAAgC,OAA/BoG,CAAAA,EAAKrQ,EAAE5D,MAAM,CAACiD,WAAW,AAAD,GAAegR,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmE,QAAQ,CAAC,SAI7FvK,EACI,AAAgB,WAAfjK,EAAE4I,QAAQ,EACP5I,EAAE5D,MAAM,CAACyS,YAAY,EACrB7O,EAAE5D,MAAM,CAACyS,YAAY,GAAGC,IAAI,EAC5B9O,EAAE5D,MAAM,CAAC4J,KAAK,CAClBzJ,EAAQkY,YAAY,EACpBxK,CAAAA,EAAYjK,EAAE5D,MAAM,CAACF,KAAK,CAACK,OAAO,CAACmY,MAAM,CAACrB,EAAG,AAAD,GAGhDrT,EAAE5D,MAAM,CAACuY,WAAW,EAAIpY,EAAQoY,WAAW,EAC3C1K,CAAAA,EAAY,GAAKzI,CAAAA,GAAoG,EAAGyI,GAAW2K,UAAU,CAACvL,EAAiB9M,EAAQoY,WAAW,CAAE,IAAM5Z,GAAG,EAAC,EAElM4Q,EAAS3F,EAAMiE,GAAW0E,IAAI,CACzB7E,EAASI,QAAQ,EAClByB,CAAAA,CAAM,CAAC,EAAE,CAAG,CAAE,EAGdpP,AAA0B,QAA1BA,EAAQsY,aAAa,EACrB/S,EAAGgT,SAAS,CAAChT,EAAGiT,SAAS,CAAEjT,EAAGkT,GAAG,EACjClT,EAAGmT,aAAa,CAACnT,EAAGoT,QAAQ,GAEvB3Y,AAA0B,SAA1BA,EAAQsY,aAAa,EAC1BtY,AAA0B,aAA1BA,EAAQsY,aAAa,CACrB/S,EAAGgT,SAAS,CAAChT,EAAGqT,SAAS,CAAErT,EAAGsT,IAAI,EAE7B7Y,AAA0B,WAA1BA,EAAQsY,aAAa,EAC1B/S,EAAGgT,SAAS,CAAChT,EAAGkT,GAAG,CAAElT,EAAGkT,GAAG,EAC3BlT,EAAGmT,aAAa,CAACnT,EAAGuT,QAAQ,GAK5BvT,EAAGwT,iBAAiB,CAACxT,EAAGiT,SAAS,CAAEjT,EAAGyT,mBAAmB,CAAEzT,EAAGkT,GAAG,CAAElT,EAAGyT,mBAAmB,EAE7FvR,EAAOc,KAAK,GAER9E,EAAEiP,SAAS,CAACvS,MAAM,CAAG,GACrBsH,EAAO4B,UAAU,CAAC,WAAY,GAE9B2N,AADAA,CAAAA,EAAU,IAhhCgCtM,EAghCNnF,EAAIkC,EAAM,EACtC6D,KAAK,CAIb2N,MAAMxV,EAAEsP,QAAQ,CAAC,EAAE,CAAC5G,IAAI,EAAEkJ,MAAM,CAAC5R,EAAEiP,SAAS,EAAG,SAAU,GACzDsE,EAAQrR,IAAI,KAKZ8B,EAAO4B,UAAU,CAAC,WAAY,GAG9B9D,EAAG2T,wBAAwB,CAAC3T,EAAGyG,iBAAiB,CAACvE,EAAOW,UAAU,GAAI,YAG1EX,EAAO+B,QAAQ,CAAC4F,GAChBtJ,EAAMiQ,QAAQ,CAACtS,EAAE5D,MAAM,CAACW,KAAK,EAC7BsF,EAAMuQ,QAAQ,CAAC5S,EAAE5D,MAAM,CAACkD,KAAK,EAC7B+C,EAAMwQ,YAAY,CAACY,EA7FOC,GA8FP,WAAf1T,EAAE4I,QAAQ,EACV5E,EAAOwC,YAAY,CAAC6C,AAAiE,EAAjEA,EAAiB9M,EAAQ0V,MAAM,EAAI1V,EAAQ0V,MAAM,CAAC4B,MAAM,CAAE,IAAW1O,GAI7FnB,EAAO2C,kBAAkB,CAAC3G,EAAEoP,eAAe,EACrB,WAAlBpP,EAAE5D,MAAM,CAAC0B,IAAI,EACbkG,EAAOgB,iBAAiB,CAAChF,EAAE5D,MAAM,CAAE4D,EAAEoF,IAAI,CAAEpF,EAAEqF,IAAI,CAAEF,GAEvDnB,EAAOkC,eAAe,CAACqD,CAAQ,CAACvJ,EAAE5D,MAAM,CAAC0B,IAAI,CAAC,EAAI,CAAA,GAC7CsN,GAKL,GAAIpB,EAAY,GAAKhK,AAAe,eAAfA,EAAE4I,QAAQ,CAC3B,IAAK0K,EAAS,EAAGA,EAAStT,EAAEsP,QAAQ,CAAC5S,MAAM,CAAE4W,IACzClI,EAAQvM,MAAM,CAACmB,EAAEsP,QAAQ,CAACgE,EAAO,CAAC5K,IAAI,CAAE1I,EAAEsP,QAAQ,CAACgE,EAAO,CAAC3K,EAAE,CAAE3I,EAAE4I,QAAQ,EAGjF,GAAI5I,EAAEoR,UAAU,EAAIe,EAGhB,IAFAnO,EAAOwC,YAAY,CAAC6C,AAA+D,EAA/DA,EAAiB9M,EAAQ0V,MAAM,EAAI1V,EAAQ0V,MAAM,CAAC4B,MAAM,CAAE,GAAS1O,GACvFnB,EAAOkC,eAAe,CAAC,CAAA,GAClBoN,EAAS,EAAGA,EAAStT,EAAEsP,QAAQ,CAAC5S,MAAM,CAAE4W,IACzClI,EAAQvM,MAAM,CAACmB,EAAEsP,QAAQ,CAACgE,EAAO,CAAC5K,IAAI,CAAE1I,EAAEsP,QAAQ,CAACgE,EAAO,CAAC3K,EAAE,CAAE,UAZvE,CAeJ,GACImB,EAASO,KAAK,CAACC,aAAa,EAC5BkH,QAAQY,OAAO,CAAC,gBAEhB,IAAI,CAAC1I,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAC,IAAI,EAEhC,IAAI,CAAC2I,KAAK,EACd,EAKA5I,EAAYvO,SAAS,CAAC2D,MAAM,CAAG,SAAU3C,CAAK,EAC1C,IAAImG,EAAQ,IAAI,CAEhB,GADA,IAAI,CAACnD,KAAK,GACNhD,EAAMwZ,QAAQ,CAACC,SAAS,CACxB,OAAO,IAAI,CAAC3C,WAAW,CAAC9W,EAExB,CAAA,IAAI,CAACyN,QAAQ,CACb,IAAI,CAACqJ,WAAW,CAAC9W,GAGjB0Z,WAAW,WACPvT,EAAMxD,MAAM,CAAC3C,EACjB,EAAG,EAEX,EAMAuN,EAAYvO,SAAS,CAAC2a,OAAO,CAAG,SAAUnV,CAAK,CAAEE,CAAM,EACnD,IAAIoD,EAAS,IAAI,CAACA,MAAM,CAEnBA,GAAW,CAAA,IAAI,CAACtD,KAAK,GAAKA,GAAS,IAAI,CAACE,MAAM,GAAKA,CAAK,IAG7D,IAAI,CAACF,KAAK,CAAGA,EACb,IAAI,CAACE,MAAM,CAAGA,EACdoD,EAAO9B,IAAI,GACX8B,EAAOqC,UAAU,CAACoD,EAAYmB,WAAW,CAAClK,EAAOE,IACrD,EAKA6I,EAAYvO,SAAS,CAAC4a,IAAI,CAAG,SAAU3W,CAAM,CAAE4W,CAAO,EAClD,IAAI1T,EAAQ,IAAI,CACZyH,EAAW,IAAI,CAACA,QAAQ,CAE5B,GADA,IAAI,CAACH,QAAQ,CAAG,CAAA,EACZ,CAACxK,EACD,MAAO,CAAA,CAEP2K,CAAAA,EAASO,KAAK,CAACG,SAAS,EACxBgH,QAAQO,IAAI,CAAC,YAEjB,IAAK,IAAIrE,EAAI,EAAGA,EAAIlE,EAAS9M,MAAM,GAC/B,IAAI,CAACoF,EAAE,CAAG3C,EAAO6W,UAAU,CAACxM,CAAQ,CAACkE,EAAE,CAAE,CAEzC,IACI,IAAI,CAAC5L,EAAE,EAJsB,EAAE4L,GAQvC,IAAI5L,EAAK,IAAI,CAACA,EAAE,CAChB,IAAIA,EAMA,MAAO,CAAA,EALFiU,GACD,IAAI,CAAC1D,KAAK,GAMlBvQ,EAAGmU,MAAM,CAACnU,EAAGoU,KAAK,EAElBpU,EAAGgT,SAAS,CAAChT,EAAGiT,SAAS,CAAEjT,EAAGyT,mBAAmB,EACjDzT,EAAGqU,OAAO,CAACrU,EAAGsU,UAAU,EAExBtU,EAAGuU,SAAS,CAACvU,EAAGwU,IAAI,EACpB,IAAItS,EAAS,IAAI,CAACA,MAAM,CAAG,IAn1CiBnC,EAm1CGC,GAC/C,GAAI,CAACkC,EAED,MAAO,CAAA,CAEX,CAAA,IAAI,CAACoH,OAAO,CAAG,IAtpCmCnE,EAspCTnF,EAAIkC,GAC7C,IAAIuS,EAAgB,SAAUzP,CAAI,CAC9B0P,CAAE,EACE,IAAIC,EAAQ,CACRxC,QAAS,CAAA,EACTpN,QAASkC,EAAI2N,aAAa,CAAC,UAC3BtC,OAAQtS,EAAGyU,aAAa,EAC5B,EACJI,EAAMF,EAAM5P,OAAO,CAACmP,UAAU,CAAC,KAC/B3T,CAAAA,EAAMwH,cAAc,CAAC/C,EAAK,CAAG2P,EAC7BA,EAAM5P,OAAO,CAACnG,KAAK,CAAG,IACtB+V,EAAM5P,OAAO,CAACjG,MAAM,CAAG,IACvB+V,EAAIC,wBAAwB,CAAG,CAAA,EAC/BD,EAAIE,2BAA2B,CAAG,CAAA,EAClCF,EAAIG,uBAAuB,CAAG,CAAA,EAC9BH,EAAII,qBAAqB,CAAG,CAAA,EAC5BJ,EAAIK,WAAW,CAAG,yBAClBL,EAAIM,SAAS,CAAG,OAChBT,EAAGG,GACH,GAAI,CACA7U,EAAGoV,aAAa,CAACpV,EAAGqV,QAAQ,EAC5BrV,EAAGoS,WAAW,CAACpS,EAAGqS,UAAU,CAAEsC,EAAMrC,MAAM,EAE1CtS,EAAGsV,UAAU,CAACtV,EAAGqS,UAAU,CAAE,EAAGrS,EAAGuV,IAAI,CAAEvV,EAAGuV,IAAI,CAAEvV,EAAGwV,aAAa,CAAEb,EAAM5P,OAAO,EACjF/E,EAAGyV,aAAa,CAACzV,EAAGqS,UAAU,CAAErS,EAAG0V,cAAc,CAAE1V,EAAG2V,aAAa,EACnE3V,EAAGyV,aAAa,CAACzV,EAAGqS,UAAU,CAAErS,EAAG4V,cAAc,CAAE5V,EAAG2V,aAAa,EACnE3V,EAAGyV,aAAa,CAACzV,EAAGqS,UAAU,CAAErS,EAAG6V,kBAAkB,CAAE7V,EAAG8V,MAAM,EAChE9V,EAAGyV,aAAa,CAACzV,EAAGqS,UAAU,CAAErS,EAAG+V,kBAAkB,CAAE/V,EAAG8V,MAAM,EAEhE9V,EAAGoS,WAAW,CAACpS,EAAGqS,UAAU,CAAE,MAC9BsC,EAAMxC,OAAO,CAAG,CAAA,CACpB,CACA,MAAOpU,EAAG,CAEV,CACJ,EA4CA,OA1CA0W,EAAc,SAAU,SAAUI,CAAG,EACjCA,EAAImB,SAAS,GACbnB,EAAIoB,GAAG,CAAC,IAAK,IAAK,IAAK,EAAG,EAAI1W,KAAK2W,EAAE,EACrCrB,EAAIhG,MAAM,GACVgG,EAAI7H,IAAI,EACZ,GAEAyH,EAAc,SAAU,SAAUI,CAAG,EACjCA,EAAIsB,QAAQ,CAAC,EAAG,EAAG,IAAK,IAC5B,GAEA1B,EAAc,UAAW,SAAUI,CAAG,EAClCA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,IAAK,GAChBvB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAI7H,IAAI,EACZ,GAEAyH,EAAc,WAAY,SAAUI,CAAG,EACnCA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,EAAG,KACdvB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAI7H,IAAI,EACZ,GAEAyH,EAAc,gBAAiB,SAAUI,CAAG,EACxCA,EAAImB,SAAS,GACbnB,EAAIuB,MAAM,CAAC,EAAG,GACdvB,EAAIwB,MAAM,CAAC,IAAK,KAChBxB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIwB,MAAM,CAAC,EAAG,GACdxB,EAAI7H,IAAI,EACZ,GACA,IAAI,CAACnF,QAAQ,CAAG,CAAA,EACZG,EAASO,KAAK,CAACG,SAAS,EACxBgH,QAAQY,OAAO,CAAC,YAEb,CAAA,CACX,EAKA3I,EAAYvO,SAAS,CAACsJ,OAAO,CAAG,WAC5B,IAAI1C,EAAK,IAAI,CAACA,EAAE,CACZkC,EAAS,IAAI,CAACA,MAAM,CACpBoH,EAAU,IAAI,CAACA,OAAO,CAC1B,IAAI,CAACiH,KAAK,GACNjH,GACAA,EAAQ5G,OAAO,GAEfR,GACAA,EAAOQ,OAAO,GAEd1C,IACAsH,EAAW,IAAI,CAACS,cAAc,CAAE,SAAUhD,CAAO,EACzCA,EAAQuN,MAAM,EACdtS,EAAGsW,aAAa,CAACvR,EAAQuN,MAAM,CAEvC,GACAtS,EAAG3C,MAAM,CAACuB,KAAK,CAAG,EAClBoB,EAAG3C,MAAM,CAACyB,MAAM,CAAG,EAE3B,EACO6I,CACX,IAqBI4O,EAA0D,SAAU1P,CAAE,CAAED,CAAI,CAAE4P,CAAI,EAClF,GAAIA,GAAQpa,AAAqB,GAArBA,UAAUxB,MAAM,CAAQ,IAAK,IAA4B6b,EAAxB7K,EAAI,EAAG8K,EAAI9P,EAAKhM,MAAM,CAAMgR,EAAI8K,EAAG9K,KACxE6K,GAAQ7K,KAAKhF,IACR6P,GAAIA,CAAAA,EAAK/C,MAAMta,SAAS,CAAC8V,KAAK,CAAC5V,IAAI,CAACsN,EAAM,EAAGgF,EAAC,EACnD6K,CAAE,CAAC7K,EAAE,CAAGhF,CAAI,CAACgF,EAAE,EAGvB,OAAO/E,EAAGiJ,MAAM,CAAC2G,GAAM/C,MAAMta,SAAS,CAAC8V,KAAK,CAAC5V,IAAI,CAACsN,GACtD,CA2CI/O,EArCOA,EA2FRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAtDlB8e,SAAS,CAPrB,SAAmBC,CAAM,CAAEhc,CAAM,CAAEic,CAAU,SACzC,AAAInD,MAAMoD,OAAO,CAACF,IACdA,EAAOhc,MAAM,CAAGA,EACTgc,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAGjc,EACxD,EAsDA/C,EAAYkf,MAAM,CAzBlB,SAAgBH,CAAM,CAAEI,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,CAAK,EAEhE,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQ,EAAE,AAAD,EAC7BzD,MAAMoD,OAAO,CAACF,GAId,OAHKlD,MAAMoD,OAAO,CAACK,IACfA,CAAAA,EAAQzD,MAAM9M,IAAI,CAACuQ,EAAK,EAErB,CACHC,QAASR,EAAOG,MAAM,CAACM,KAAK,CAACT,EAAQL,EAAc,CAACS,EAAOC,EAAY,CAAEE,EAAO,CAAA,IAChFG,MAAOV,CACX,EAEJ,IAAIW,EAAcze,OAAO0e,cAAc,CAACZ,GAC/Ba,WAAW,CAChBL,EAAUR,CAAM,CAACM,EAAoB,WAAa,QAAQ,CAACF,EAC3DA,EAAQC,GAERS,EAAS,IAAIH,EADDX,EAAOhc,MAAM,CAAGqc,EAAcE,EAAMvc,MAAM,EAK1D,OAHA8c,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMvc,MAAM,EAC9D,CACHwc,QAASA,EACTE,MAAOI,CACX,CACJ,EAQyB,IAAIG,EAAoBhgB,EAmBjD8e,EAAYkB,EAAiBlB,SAAS,CAAEI,EAASc,EAAiBd,MAAM,CAExEe,EAAY,AAACpe,IAA+Eoe,SAAS,CAAEC,GAA2B,AAACre,IAA+E4N,UAAU,CAAE0Q,GAAY,AAACte,IAA+Ese,SAAS,CAiBnUC,GAA+B,WAiB/B,SAASA,EAAcxd,CAAO,EACV,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAI8F,EAAQ,IAAI,AAOhB,CAAA,IAAI,CAAC2X,MAAM,CAAG,CAACzd,EAAQ0d,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAI1d,EAAQ0d,EAAE,EAAIH,KACzB,IAAI,CAACK,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGP,KAClB,IAAIM,EAAW,EACfP,GAAyBtd,EAAQ2d,OAAO,EAAI,CAAC,EAAG,SAAUxB,CAAM,CAAE4B,CAAU,EACxEjY,EAAM6X,OAAO,CAACI,EAAW,CAAG5B,EAAO1H,KAAK,GACxCoJ,EAAW/Y,KAAKjE,GAAG,CAACgd,EAAU1B,EAAOhc,MAAM,CAC/C,GACA,IAAI,CAAC6d,aAAa,CAACH,EACvB,CAyMA,OA5LAL,EAAc7e,SAAS,CAACqf,aAAa,CAAG,SAAUH,CAAQ,EACtD,IAAI/X,EAAQ,IAAI,AAChB,CAAA,IAAI,CAAC+X,QAAQ,CAAGA,EAChBP,GAAyB,IAAI,CAACK,OAAO,CAAE,SAAUxB,CAAM,CAAE4B,CAAU,EAC3D5B,EAAOhc,MAAM,GAAK0d,GAClB/X,CAAAA,EAAM6X,OAAO,CAACI,EAAW,CAAG7B,EAAUC,EAAQ0B,EAAQ,CAE9D,EACJ,EAeAL,EAAc7e,SAAS,CAACsf,UAAU,CAAG,SAAUC,CAAQ,CAAEL,CAAQ,EAC7D,IAAI/X,EAAQ,IAAI,CAEhB,GADiB,KAAK,IAAlB+X,GAAuBA,CAAAA,EAAW,CAAA,EAClCA,EAAW,GAAKK,EAAW,IAAI,CAACL,QAAQ,CAAE,CAC1C,IAAIM,EAAW,EACfb,GAAyB,IAAI,CAACK,OAAO,CAAE,SAAUxB,CAAM,CAAE4B,CAAU,EAC/DjY,EAAM6X,OAAO,CAACI,EAAW,CACrBzB,EAAOH,EAAQ+B,EAAUL,GAAUhB,KAAK,CAC5CsB,EAAWhC,EAAOhc,MAAM,AAC5B,GACA,IAAI,CAAC0d,QAAQ,CAAGM,CACpB,CACAd,EAAU,IAAI,CAAE,kBAAmB,CAAEa,SAAUA,EAAUL,SAAUA,CAAS,GAC5E,IAAI,CAACC,UAAU,CAAGP,IACtB,EAWAC,EAAc7e,SAAS,CAACqD,SAAS,CAAG,SAAU+b,CAAU,CAExDK,CAAW,EACP,OAAO,IAAI,CAACT,OAAO,CAACI,EAAW,AACnC,EAYAP,EAAc7e,SAAS,CAAC0f,UAAU,CAAG,SAAUC,CAAW,CAE1DF,CAAW,EACP,IAAItY,EAAQ,IAAI,CAChB,MAAO,AAACwY,CAAAA,GAAejgB,OAAOkgB,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAA,EAAGa,MAAM,CAAC,SAAUb,CAAO,CAAEI,CAAU,EAElF,OADAJ,CAAO,CAACI,EAAW,CAAGjY,EAAM6X,OAAO,CAACI,EAAW,CACxCJ,CACX,EAAG,CAAC,EACR,EAaAH,EAAc7e,SAAS,CAAC8f,MAAM,CAAG,SAAUP,CAAQ,CAAEI,CAAW,EAC5D,IAAIxY,EAAQ,IAAI,CAChB,MAAO,AAACwY,CAAAA,GAAejgB,OAAOkgB,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAA,EAAGe,GAAG,CAAC,SAAUvgB,CAAG,EAAI,IAAIoC,EAAI,OAAO,AAA8B,OAA7BA,CAAAA,EAAKuF,EAAM6X,OAAO,CAACxf,EAAI,AAAD,GAAeoC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC2d,EAAS,AAAE,EACvK,EAmBAV,EAAc7e,SAAS,CAACggB,SAAS,CAAG,SAAUZ,CAAU,CAAE5B,CAAM,CAAE+B,CAAQ,CAAEU,CAAW,EACnF,IAAIre,CACW,MAAK,IAAhB4b,GAAqBA,CAAAA,EAAS,EAAE,AAAD,EAClB,KAAK,IAAlB+B,GAAuBA,CAAAA,EAAW,CAAA,EACtC,IAAI,CAACW,UAAU,CAAEte,CAAAA,AAASA,CAATA,EAAK,CAAC,CAAA,CAAK,CAACwd,EAAW,CAAG5B,EAAQ5b,CAAC,EAAI2d,EAAUU,EACtE,EAmBApB,EAAc7e,SAAS,CAACkgB,UAAU,CAAG,SAAUlB,CAAO,CAAEO,CAAQ,CAAEU,CAAW,EACzE,IAAI9Y,EAAQ,IAAI,CACZ+X,EAAW,IAAI,CAACA,QAAQ,CAC5BP,GAAyBK,EAAS,SAAUxB,CAAM,CAAE4B,CAAU,EAC1DjY,EAAM6X,OAAO,CAACI,EAAW,CAAG5B,EAAO1H,KAAK,GACxCoJ,EAAW1B,EAAOhc,MAAM,AAC5B,GACA,IAAI,CAAC6d,aAAa,CAACH,GACbe,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,MAAM,AAAD,IAC7EzB,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACS,UAAU,CAAGP,KAE1B,EAoBAC,EAAc7e,SAAS,CAACogB,MAAM,CAAG,SAAUC,CAAG,CAAEd,CAAQ,CAAEe,CAAM,CAAEL,CAAW,EACxD,KAAK,IAAlBV,GAAuBA,CAAAA,EAAW,IAAI,CAACL,QAAQ,AAAD,EAClD,IAAIF,EAAU,IAAI,CAACA,OAAO,CACtBuB,EAAgBD,EAAS,IAAI,CAACpB,QAAQ,CAAG,EAAIK,EAAW,EAC5DZ,GAAyB0B,EAAK,SAAUG,CAAS,CAAEpB,CAAU,EACzD,IAAI5B,EAASwB,CAAO,CAACI,EAAW,EACxB,AAACa,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYQ,UAAU,AAAD,IAAO,CAAA,GAAS,AAAInG,MAAMiG,GAC9G/C,IACI8C,EACA9C,EAASG,EAAOH,EAAQ+B,EAAU,EAAG,CAAA,EAAM,CAACiB,EAAU,EAAEtC,KAAK,CAG7DV,CAAM,CAAC+B,EAAS,CAAGiB,EAEvBxB,CAAO,CAACI,EAAW,CAAG5B,EAE9B,GACI+C,EAAgB,IAAI,CAACrB,QAAQ,EAC7B,IAAI,CAACG,aAAa,CAACkB,GAEjBN,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,MAAM,AAAD,IAC7EzB,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACS,UAAU,CAAGP,KAE1B,EACOC,CACX,IA2DI6B,GAAa,AAACpgB,IAA+EogB,UAAU,CAEvGC,GAAuB,AAACrgB,IAA+EK,QAAQ,CAAEigB,GAAkB,AAACtgB,IAA+EuN,GAAG,CAAEgT,GAAO,AAACvgB,IAA+EugB,IAAI,CAAEC,GAAkB,AAACxgB,IAA+EwN,GAAG,CAE1ZiT,GAAuB,AAACzgB,IAA+EM,QAAQ,CAAEogB,GAA0B,AAAC1gB,IAA+E0gB,uBAAuB,CAAEC,GAAoB,AAAC3gB,IAA+EmG,KAAK,CAAEya,GAAS,AAAC5gB,IAA+E4gB,MAAM,CAAEC,GAAwB,AAAC7gB,IAA+Eoe,SAAS,CAAEhB,GAAU,AAACpd,IAA+Eod,OAAO,CAAE0D,GAAuB,AAAC9gB,IAA+EyN,QAAQ,CAAEsT,GAAmB,AAAC/gB,IAA+EO,IAAI,CAAEygB,GAAyB,AAAChhB,IAA+EQ,UAAU,CAAEygB,GAAO,AAACjhB,IAA+EihB,IAAI,CAAEC,GAAU,AAAClhB,IAA+EkhB,OAAO,CAuBvqC,SAASC,GAA4BjH,CAAQ,CAAEtZ,CAAM,EACjD,IAAIC,EAAQD,EAAOC,KAAK,CACpBqZ,GACArZ,GACAA,EAAM8D,MAAM,EACZ9D,EAAM8C,MAAM,EACZ,CAACyd,AA/7EkB3gB,EA+7EgBG,EAAOF,KAAK,GAC/CwZ,EAASrK,6BAA6B,CAACjP,EAE/C,CAUA,SAASygB,GAAa3gB,CAAK,EACvB,OAAOqgB,GAAkBrgB,GACrBA,EAAMK,OAAO,EACbL,EAAMK,OAAO,CAACF,KAAK,EACnBH,EAAMK,OAAO,CAACF,KAAK,CAAC6V,OAAO,CAAG,CAAA,EACtC,CAqHA,SAAS4K,GAAwB5gB,CAAK,CAAEE,CAAM,EAI1C,IAHIU,EACAY,EACA2S,EACAjX,EAAa8C,EAAMqd,WAAW,CAC9BwD,EAAc7gB,EAAMoY,WAAW,EAAIlY,EAAO4gB,KAAK,CAE/Ctc,EAAQxE,EAAM+W,UAAU,CACxBrS,EAAS1E,EAAMgX,WAAW,CAC1B/S,EAASjE,EACT+gB,EAAc,AAAmC,aAAnC,OAAOC,wBACrBC,EAAkB,CAAA,EAClBP,AAllFmB3gB,EAklFeC,GAClCiE,EAASjE,GAGTiE,EAAS/D,EACT+gB,EAAkBC,CAAAA,CAAQ,CAAA,AAAC,CAAA,AAAiC,OAAhCtgB,CAAAA,EAAKV,EAAOG,OAAO,CAAC8gB,MAAM,AAAD,GAAevgB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGwgB,KAAK,AAAD,GAC/F,CAAA,AAAsF,OAArFjN,CAAAA,EAAK,AAAgC,OAA/B3S,CAAAA,EAAKtB,EAAOG,OAAO,CAACuT,KAAK,AAAD,GAAepS,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG2f,MAAM,AAAD,GAAehN,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGiN,KAAK,AAAD,CAAC,GAExI,IAAIjhB,EAAQ8D,EAAO9D,KAAK,CAChB8D,EAAO9D,KAAK,EACR,CAAC,EA2Fb,GArFA4gB,EAAc,CAAA,EACTpjB,GACDA,CAAAA,EAAaiiB,GAAgBpF,aAAa,CAAC,SAAQ,EAEnD,CAACra,EAAM8D,MAAM,GACb9D,EAAM8C,MAAM,CAAGtF,EAGXqC,EAAMwZ,QAAQ,CAACC,SAAS,EAAI,CAACsH,GAC7B9c,EAAOod,YAAY,CAAGlhB,EAAM8D,MAAM,CAAGjE,EAAMwZ,QAAQ,CAAC8H,KAAK,CAAC,GAAI,EAAG,EAAG9c,EAAOE,GACtE2T,QAAQ,CAAC,2BACTkJ,GAAG,CAACV,GACT1gB,EAAM6C,KAAK,CAAG,WACV7C,EAAM8D,MAAM,CAACud,IAAI,CAAC,CAGdC,KAAM,oHACV,EACJ,EACAthB,EAAMuhB,IAAI,CAAG,WACTvhB,EAAMwhB,MAAM,GACZxhB,EAAM8D,MAAM,CAACud,IAAI,CAAC,CACdC,KAAMthB,EAAM8C,MAAM,CAAC2e,SAAS,CAAC,YACjC,EACJ,IAGAzhB,EAAM0hB,QAAQ,CAAG7hB,EAAMwZ,QAAQ,CAC1BgB,aAAa,CAAC,iBACd+G,GAAG,CAACV,GACT5c,EAAOod,YAAY,CAAGlhB,EAAM8D,MAAM,CAC9B2b,GAAgBpF,aAAa,CAAC,UAClCra,EAAM2hB,SAAS,CAAG3hB,EAAM8D,MAAM,CAAC6V,UAAU,CAAC,MAC1C3Z,EAAM0hB,QAAQ,CAACE,OAAO,CAACC,WAAW,CAAC7hB,EAAM8D,MAAM,EAC/C9D,EAAM6C,KAAK,CAAG,WACV7C,EAAM8D,MAAM,CAACO,KAAK,CAAGrE,EAAM8C,MAAM,CAACuB,KAAK,CACvCrE,EAAM8D,MAAM,CAACS,MAAM,CAAGvE,EAAM8C,MAAM,CAACyB,MAAM,AAC7C,EACAvE,EAAMuhB,IAAI,CAAG,WACTvhB,EAAM8D,MAAM,CAACO,KAAK,CAAGrE,EAAM8C,MAAM,CAACuB,KAAK,CACvCrE,EAAM8D,MAAM,CAACS,MAAM,CAAGvE,EAAM8C,MAAM,CAACyB,MAAM,CACzCvE,EAAM2hB,SAAS,CAACG,SAAS,CAAC9hB,EAAM8C,MAAM,CAAE,EAAG,EAC/C,GAEJ9C,EAAMwhB,MAAM,CAAG,WACX,IAAI/gB,EACAY,EACJgD,EAAQxE,EAAM+W,UAAU,CACxBrS,EAAS1E,EAAMgX,WAAW,CAC1B,AAAC7W,CAAAA,EAAM0hB,QAAQ,EAAI1hB,EAAM8D,MAAM,AAAD,EACzBud,IAAI,CAAC,CACNpd,EAAG,EACHE,EAAG,EACHE,MAAOA,EACPE,OAAQA,CACZ,GACKwd,GAAG,CAAC,CACLC,cAAelB,EAAkB,KAAK,EAAI,OAC1CmB,eAAgB,SAChBC,QAjFA,CAkFJ,GACKhK,QAAQ,CAAC4I,EAAkB,qBAAuB,IACnDhd,aAAkB/G,GAClB,CAAA,AAAmF,OAAlFsE,CAAAA,EAAK,AAAwB,OAAvBZ,CAAAA,EAAKqD,EAAO9D,KAAK,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuC,WAAW,AAAD,GAAe3B,AAAO,KAAK,IAAZA,GAAyBA,EAAG6B,SAAS,CAACrD,EAAMqE,QAAQ,CAAErE,EAAMuE,OAAO,CAAA,CAEtK,EACApE,EAAMmiB,QAAQ,CAAGtiB,EAAMwZ,QAAQ,CAAC8I,QAAQ,GACxC,AAACniB,CAAAA,EAAM0hB,QAAQ,EAAI1hB,EAAM8D,MAAM,AAAD,EACzBud,IAAI,CAAC,CAMNe,OAAQriB,EAAOG,OAAO,CAACkiB,MAAM,AACjC,GACIte,aAAkB/G,GAClB+G,CAAAA,EAAO9D,KAAK,CAACgD,WAAW,CAAGc,EAAOuV,QAAQ,CACrCgJ,CAAC,GACDjB,GAAG,CAACV,GACJxd,SAAS,CAACnD,EAAOW,KAAK,CAACyC,GAAG,CAAEpD,EAAOkD,KAAK,CAACE,GAAG,CAAA,GAGzDnD,EAAM8C,MAAM,CAACuB,KAAK,CAAGA,EACrBrE,EAAM8C,MAAM,CAACyB,MAAM,CAAGA,EAClBvE,EAAMmiB,QAAQ,CAAE,CAChB,IAAIG,EAAMC,EAA6B1iB,EACnCiE,GAIA0e,EAAiB,AAACF,EAAIje,KAAK,GAAKxE,EAAMmE,OAAO,CAACK,KAAK,EAC/Cie,EAAI/d,MAAM,GAAK1E,EAAMmE,OAAO,CAACO,MAAM,CAAImc,EACtC1gB,EAAM0hB,QAAQ,EAAI1hB,EAAM8D,MAAM,CACvC9D,EAAMmiB,QAAQ,CAACd,IAAI,CAACiB,GACpBE,MAAAA,GAAgEA,EAAeC,IAAI,CAACziB,EAAMmiB,QAAQ,CACtG,CAyBA,OAxBAniB,EAAMwhB,MAAM,GACZxhB,EAAM6C,KAAK,GACP,CAAC7C,EAAMuC,GAAG,GACVvC,EAAMuC,GAAG,CAAG,IA7vBkC6K,EA6vBZ,SAAU7K,CAAG,EACvCA,EAAIkL,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjC+G,QAAQO,IAAI,CAAC,eAEjB1V,EAAMuhB,IAAI,GACNhf,EAAIkL,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjC+G,QAAQY,OAAO,CAAC,cAExB,GACK/V,EAAMuC,GAAG,CAACkX,IAAI,CAACzZ,EAAM8C,MAAM,GAI5Bgd,GAAkB,sDAEtB9f,EAAMuC,GAAG,CAACuM,UAAU,CAACjP,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,GACzC8D,aAAkB/G,GAClBiD,EAAMuC,GAAG,CAACQ,cAAc,CAAClD,IAGjCG,EAAMuC,GAAG,CAACiX,OAAO,CAACnV,EAAOE,GAClBvE,EAAMuC,GAAG,AACpB,CAOA,SAASmgB,GAAgB3iB,CAAM,EAC3B,IAAIqC,EAASrC,EAAOqC,MAAM,CAC1B,GAAIA,EAAQ,CACR,IAAIqR,EAAQ,KAAK,EACbpC,EAAI,KAAK,EACb,IAAKA,EAAI,EAAGA,EAAIjP,EAAO/B,MAAM,CAAEgR,GAAQ,EACnCoC,CAAAA,EAAQrR,CAAM,CAACiP,EAAE,AAAD,GACHoC,EAAMkP,eAAe,EAC9BlP,EAAMkP,eAAe,EAGjC,CACA,CAAC,QAAS,OAAQ,UAAU,CAACrjB,OAAO,CAAC,SAAUV,CAAI,EAC/C,IAAIgkB,EAAa7iB,CAAM,CAACnB,EAAK,CACzBgkB,GACA7iB,CAAAA,CAAM,CAACnB,EAAK,CAAGgkB,EAAWza,OAAO,EAAC,CAE1C,GACA,IAAK,IAAI3H,EAAK,EAAGC,EAAKV,EAAOiR,KAAK,CAAExQ,EAAKC,EAAGJ,MAAM,CAAEG,IAEhDqf,GADWpf,CAAE,CAACD,EAAG,CACa,KAAK,EAAG,CAAA,EAE9C,CAmBA,SAASqiB,GAAUC,CAAG,CAAE3I,CAAE,CAAE4I,CAAS,CAAEC,CAAS,CAAE3R,CAAC,CAAE4R,CAAS,EAK1D,IAFA,IAAI9iB,EAAYkR,AAFhBA,CAAAA,EAAIA,GAAK,CAAA,EACT2R,CAAAA,EAAYA,GApWC,GAoWqB,EAE9BE,EAAU,CAAA,EACPA,GAAW7R,EAAIlR,GAAakR,EAAIyR,EAAIziB,MAAM,EAC7C6iB,EAAU/I,EAAG2I,CAAG,CAACzR,EAAE,CAAEA,GACrB,EAAEA,EAEF6R,IACI7R,EAAIyR,EAAIziB,MAAM,CACV4iB,EACAJ,GAAUC,EAAK3I,EAAI4I,EAAWC,EAAW3R,EAAG4R,GAEvCtD,GAAgBwD,qBAAqB,CAE1CxD,GAAgBwD,qBAAqB,CAAC,WAClCN,GAAUC,EAAK3I,EAAI4I,EAAWC,EAAW3R,EAC7C,GAGAkI,WAAWsJ,GAAW,EAAGC,EAAK3I,EAAI4I,EAAWC,EAAW3R,GAGvD0R,GACLA,IAGZ,CAuFA,SAASK,GAAYrjB,CAAM,CAAEsjB,CAAM,EAC/B,IAAInjB,EAAUH,EAAOG,OAAO,CACxBojB,EAAavjB,EAAOwjB,SAAS,CAACzF,QAAQ,CAACC,QAAQ,CAC/Crd,EAAQX,EAAOW,KAAK,EAAIX,EAAOW,KAAK,CAACR,OAAO,CAC5C+C,EAAQlD,EAAOkD,KAAK,EAAIlD,EAAOkD,KAAK,CAAC/C,OAAO,CAC5CsjB,EAAYzjB,EAAOyjB,SAAS,EAAIzjB,EAAOyjB,SAAS,CAACtjB,OAAO,CAC5D,OAAOojB,EAAcpjB,CAAAA,EAAQqB,cAAc,EAAIQ,OAAOC,SAAS,AAAD,GAE1Die,GAAqBhd,EAAMrC,GAAG,GAC9Bqf,GAAqBhd,EAAMlC,GAAG,GAE7B,CAAA,CAACsiB,GACGpD,GAAqBvf,EAAME,GAAG,GAAKqf,GAAqBvf,EAAMK,GAAG,CAAC,GAEtE,CAAA,CAACyiB,GACGvD,GAAqBuD,EAAU5iB,GAAG,GAAKqf,GAAqBuD,EAAUziB,GAAG,CAAC,CACvF,CAOA,IAAI0iB,GAAoB,SAAU1jB,CAAM,CAAEoC,CAAI,QAE1C,CAAIpC,EAAO2jB,SAAS,EAGZnD,CAAAA,AA35Fe3gB,EA25FmBG,EAAOF,KAAK,GACjD,AAACsC,CAAAA,EAAOA,EAAK9B,MAAM,CAAG,CAAA,GAClBN,CAAAA,EAAOG,OAAO,CAACqB,cAAc,EAAIQ,OAAOC,SAAS,AAAD,CAAE,CAC/D,EAOA,SAAS2hB,KACL,IAAI5jB,EAAS,IAAI,CACbF,EAAQE,EAAOF,KAAK,AACpBA,CAAAA,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAACgD,WAAW,GAAKjD,EAAOiD,WAAW,EAC9CjD,CAAAA,EAAOiD,WAAW,CAAG,IAAG,EAExBnD,EAAM+jB,WAAW,EACjB/jB,CAAAA,EAAM+jB,WAAW,CAAG/jB,EAAM+jB,WAAW,CAACC,MAAM,CAAC,SAAUpQ,CAAK,EACxD,OAAOA,EAAM1T,MAAM,GAAKA,CAC5B,EAAC,EAEDF,EAAM4D,UAAU,EAAI5D,EAAM4D,UAAU,CAAC1D,MAAM,GAAKA,GAChDF,CAAAA,EAAM4D,UAAU,CAAG,IAAG,CAE9B,CAIA,SAASqgB,KACL,IAAI9jB,EAAQ,IAAI,CAACA,KAAK,CAClBA,GAASA,EAAM8C,MAAM,EAAI9C,EAAM8D,MAAM,GACjC9D,EAAMuC,GAAG,EACTvC,EAAMuC,GAAG,CAACM,KAAK,GAEf7C,EAAM6C,KAAK,EACX7C,EAAM6C,KAAK,GAGvB,CAMA,SAASkhB,GAA0BhkB,CAAM,EACrC,IAAIC,EAAQD,EAAOC,KAAK,CACpBA,GACAA,EAAM8C,MAAM,EACZ9C,EAAM8D,MAAM,EACZ9D,EAAMuC,GAAG,EACT,CAACge,AA98FkB3gB,EA88FgBG,EAAOF,KAAK,GAC/CG,EAAMuC,GAAG,CAACC,MAAM,CAACzC,EAAOF,KAAK,CAErC,CAUA,SAASmkB,GAASjkB,CAAM,CAAEkkB,CAAU,EAEhC,IADIxjB,EACAS,EAAgBnB,EAAOG,OAAO,CAC9BQ,EAAQX,EAAOW,KAAK,CACpBvD,EAAa4C,EAAOmkB,UAAU,CAClC,GAAID,aAAsB9mB,EACtB,OAAO8mB,EAEX,IAAIE,EAAYpkB,EAAO6E,EAAE,CAAC,WAAY8J,EAAS,AAACyV,CAAAA,GAAapkB,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAM7B,MAAM,CACvFN,EAAOmC,SAAS,CAAC,IAAK,CAAA,GACtB,KAAK,CAAA,GACJnC,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAC7DhB,EAAcwN,KAAK,EACnB3O,EAAOmC,SAAS,CAAC,IAAK,CAAA,IACtB,CAAA,EAAQoO,EAASvQ,EAAOmC,SAAS,CAAC,IAAK,CAAA,IACvChB,EAAcoP,KAAK,EACnB,CAAA,EAAQmD,EAAQ,IAAItW,EAAW4C,EAAQ,AAACokB,GAAazV,GAAS4B,EAC9D,CAAC5B,CAAK,CAACuV,EAAW5S,CAAC,CAAC,CAAEf,CAAK,CAAC2T,EAAW5S,CAAC,CAAC,CAAC,CAC1C,AAACkL,CAAAA,GAAQxc,EAAOG,OAAO,CAACiC,IAAI,EAAIpC,EAAOG,OAAO,CAACiC,IAAI,CAAG,EAAE,AAAD,CAAE,CAAC8hB,EAAW5S,CAAC,CAAC,CAAE3C,EAAQA,CAAK,CAACuV,EAAW5S,CAAC,CAAC,CAAG,KAAK,GAapH,OAZAoC,EAAM2Q,QAAQ,CAAGlE,GAAiBxf,EAAM2jB,UAAU,CAC9C3jB,EAAM2jB,UAAU,CAAC5Q,EAAMxP,CAAC,CAAC,CACzBwP,EAAMxP,CAAC,CACXwP,EAAMxP,CAAC,EACPwP,EAAMpV,GAAG,CAAG,AAAsB,OAArBoC,CAAAA,EAAKgT,EAAMhJ,IAAI,AAAD,GAAehK,AAAO,KAAK,IAAZA,EAAgBA,EAAKgT,EAAM2Q,QAAQ,CAC7E3Q,EAAM6Q,IAAI,CAAGL,EAAWK,IAAI,CAC5B7Q,EAAM8Q,KAAK,CAAGN,EAAWM,KAAK,CAC9B9Q,EAAM+Q,KAAK,CAAGP,EAAWO,KAAK,CAC9B/Q,EAAMG,KAAK,CAAGqQ,EAAWrQ,KAAK,CAC9BH,EAAMlW,KAAK,CAAG0mB,EAAW5S,CAAC,CAC1BoC,EAAMgR,UAAU,CAAGR,EAAWQ,UAAU,CACxChR,EAAMiR,QAAQ,CAAG3kB,EAAO4kB,aAAa,CAAClR,GAC/BA,CACX,CAIA,SAASmR,GAAmBC,CAAK,EAS7B,IACI3kB,EAAUH,AADD,IAAI,CACIG,OAAO,CACxBQ,EAAQX,AAFC,IAAI,CAEEW,KAAK,CACpBuC,EAAQlD,AAHC,IAAI,CAGEkD,KAAK,CAExB,GAAI,CAAClD,AALQ,IAAI,CAKL+kB,OAAO,EACf,CAACpkB,EAAMokB,OAAO,EACd,CAAC7hB,EAAM6hB,OAAO,EACd,CAACD,EACD,MAAO,CAAA,EAIX9kB,AAba,IAAI,CAaVkD,KAAK,CAAC8hB,eAAe,GAC5B,IAAIxjB,EAAiBrB,EAAQqB,cAAc,EAAI,EAAGyjB,EAAgB9kB,EAAQ8kB,aAAa,CAAEtW,EAAQ3O,AAdpF,IAAI,CAcuFmC,SAAS,CAAC,KAAM4N,EAAYpP,EAAMqP,WAAW,GAAIG,EAAO,AAAyB,OAAxBzP,CAAAA,EAAKqP,EAAU/O,GAAG,AAAD,GAAeN,AAAO,KAAK,IAAZA,EAAgBA,EAAKsB,OAAOC,SAAS,CAAEgO,EAAO,AAAyB,OAAxB3O,CAAAA,EAAKyO,EAAUlP,GAAG,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACU,OAAOC,SAAS,CAAEsO,EAAQvQ,AAdnT,IAAI,CAcsTmC,SAAS,CAAC,KAAMiO,EAAYlN,EAAM8M,WAAW,GAAIM,EAAO,AAAyB,OAAxB2D,CAAAA,EAAK7D,EAAUpP,GAAG,AAAD,GAAeiT,AAAO,KAAK,IAAZA,EAAgBA,EAAKjS,OAAOC,SAAS,CAAEoO,EAAO,AAAyB,OAAxB8D,CAAAA,EAAK/D,EAAUvP,GAAG,AAAD,GAAesT,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACnS,OAAOC,SAAS,CAErhB,GAAI,CAACjC,AAhBQ,IAAI,CAgBL0C,OAAO,EACf/B,EAAMukB,GAAG,EACThiB,EAAMgiB,GAAG,EACTjV,GAAS,CAAA,AAAyB,OAAxBmE,CAAAA,EAAKzT,EAAMukB,GAAG,CAACrkB,GAAG,AAAD,GAAeuT,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACpS,OAAOC,SAAS,AAAD,GAC/EkO,GAAS,CAAA,AAAyB,OAAxBgV,CAAAA,EAAKxkB,EAAMukB,GAAG,CAAClkB,GAAG,AAAD,GAAemkB,AAAO,KAAK,IAAZA,EAAgBA,EAAKnjB,OAAOC,SAAS,AAAD,GAC9EoO,GAAS,CAAA,AAAyB,OAAxB+U,CAAAA,EAAKliB,EAAMgiB,GAAG,CAACrkB,GAAG,AAAD,GAAeukB,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACpjB,OAAOC,SAAS,AAAD,GAC/EqO,GAAS,CAAA,AAAyB,OAAxB+U,CAAAA,EAAKniB,EAAMgiB,GAAG,CAAClkB,GAAG,AAAD,GAAeqkB,AAAO,KAAK,IAAZA,EAAgBA,EAAKrjB,OAAOC,SAAS,AAAD,EAK9E,OAJAjC,AAvBS,IAAI,CAuBNwjB,SAAS,CAACzF,QAAQ,CAACiB,UAAU,CAAC,CACjC9a,EAAGyK,EACHvK,EAAGmM,CACP,GACO,CAAA,EAGX,IAAIgT,EAAavjB,AA9BJ,IAAI,CA8BOwjB,SAAS,CAACxF,QAAQ,CAC1C,GAAI,CAACxc,GACD+hB,EAAa/hB,GACZyjB,GACG,CAACjlB,AAlCI,IAAI,CAkCD2jB,SAAS,EACjB,CAAC3jB,AAnCI,IAAI,CAmCDslB,kBAAkB,EAC1B,CAACnlB,EAAQmlB,kBAAkB,EAC3B/B,EAAa0B,EAKjB,OAJAjlB,AAtCS,IAAI,CAsCNwjB,SAAS,CAACzF,QAAQ,CAACiB,UAAU,CAAC,CACjC9a,EAAGyK,EACHvK,EAAGmM,CACP,GACO,CAAA,EAeX,IAAK,IAjED7P,EACAY,EACA2S,EACAE,EACAC,EACA+Q,EACAC,EACAC,EAoDAnhB,EAGAE,EATAmhB,EAAgB,EAAE,CAClBC,EAAiB,EAAE,CACnBC,EAAiB,EAAE,CACnBC,EAAe,CAAExF,CAAAA,GAAqBnQ,EAAU/O,GAAG,GAAKkf,GAAqBnQ,EAAUlP,GAAG,CAAA,EAC1F8kB,EAAe,CAAEzF,CAAAA,GAAqB9P,EAAUpP,GAAG,GAAKkf,GAAqB9P,EAAUvP,GAAG,CAAA,EAC1F+kB,EAAU,CAAA,EAEVC,EAAWlX,CAAK,CAAC,EAAE,CACnBmX,EAAWnX,CAAK,CAAC,EAAE,CAEnBoX,EAAWxV,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAAC,EAAE,CACjEyV,EAAWzV,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAAC,EAAE,CAC5De,EAAI,EAAG2U,EAAOtX,EAAMrO,MAAM,CAAEgR,EAAI2U,EAAM,EAAE3U,EAC7CpN,EAAIyK,CAAK,CAAC2C,EAAE,CACZlN,EAAImM,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAACe,EAAE,CACtDpN,GAAK+L,GAAQ/L,GAAKiM,GAClB/L,GAAKiM,GAAQjM,GAAKkM,GAClBiV,EAAche,IAAI,CAAC,CAAErD,EAAGA,EAAGE,EAAGA,CAAE,GAChCohB,EAAeje,IAAI,CAACrD,GACpBuhB,EAAele,IAAI,CAACnD,GAChBshB,IACAG,EAAW5gB,KAAKjE,GAAG,CAAC6kB,EAAU3hB,GAC9B4hB,EAAW7gB,KAAKpE,GAAG,CAACilB,EAAU5hB,IAE9ByhB,IACAI,EAAW9gB,KAAKjE,GAAG,CAAC+kB,EAAU3hB,GAC9B4hB,EAAW/gB,KAAKpE,GAAG,CAACmlB,EAAU5hB,KAIlCwhB,EAAU,CAAA,EA2BlB,OAxBIF,IACA/kB,EAAMM,OAAO,CAAGgE,KAAKjE,GAAG,CAAC6kB,EAAUllB,EAAMM,OAAO,EAAI,GACpDN,EAAMI,OAAO,CAAGkE,KAAKpE,GAAG,CAACilB,EAAUnlB,EAAMI,OAAO,EAAI,IAEpD4kB,IACAziB,EAAMjC,OAAO,CAAGgE,KAAKjE,GAAG,CAAC+kB,EAAU7iB,EAAMjC,OAAO,EAAI,GACpDiC,EAAMnC,OAAO,CAAGkE,KAAKpE,GAAG,CAACmlB,EAAU9iB,EAAMnC,OAAO,EAAI,IAGxDf,AAvFa,IAAI,CAuFV4lB,OAAO,CAAGA,EACjB5lB,AAxFa,IAAI,CAwFVkmB,SAAS,CAAG,EAEfN,GAAW5lB,AA1FF,IAAI,CA0FKwjB,SAAS,CAACzF,QAAQ,GAAK/d,AA1FhC,IAAI,CA0FmCwjB,SAAS,EAGzDxjB,CAAAA,AA7FS,IAAI,CA6FNwjB,SAAS,CAACzF,QAAQ,CAAG,IA5vBmBJ,EA4vBI,EAEvD3d,AA/Fa,IAAI,CA+FVwjB,SAAS,CAACzF,QAAQ,CAACiB,UAAU,CAAC,CACjC9a,EAAGshB,EACHphB,EAAGqhB,CACP,GACK/B,GAnGQ,IAAI,CAmGc8B,IAC3BxlB,CAAAA,AApGS,IAAI,CAoGNulB,aAAa,CAAGA,CAAY,EAEhC,CAAA,CACX,CAKA,SAASY,KACL,IAUIC,EAEA9W,EACA+W,EACAC,EACAC,EAfAtgB,EAAQ,IAAI,CACZ9F,EAAU,IAAI,CAACA,OAAO,EAAI,CAAC,EAAGL,EAAQ,IAAI,CAACA,KAAK,CAAE0mB,EAAa1mB,EAAMG,KAAK,CAAEwmB,EAAc,IAAI,CAACxmB,KAAK,CAAEU,EAAQ,IAAI,CAACA,KAAK,CAAEuC,EAAQ,IAAI,CAACA,KAAK,CAAEyL,EAAQxO,EAAQwO,KAAK,EAAI,IAAI,CAACxM,SAAS,CAAC,IAAK,CAAA,GAAOoO,EAAQpQ,EAAQoQ,KAAK,EAAI,IAAI,CAACpO,SAAS,CAAC,IAAK,CAAA,GAAOukB,EAAU,IAAI,CAACvkB,SAAS,CAAC,MAAO,CAAA,GAAOwkB,EAAW,IAAI,CAACxkB,SAAS,CAAC,OAAQ,CAAA,GAAO2N,EAAU,IAAI,CAACyV,aAAa,EAAIplB,EAAQiC,IAAI,CAAE2N,EAAYpP,EAAMqP,WAAW,GAEjZC,EAAOF,EAAUlP,GAAG,CAAIF,CAAAA,EAAMuP,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU/O,GAAG,CAAIL,CAAAA,EAAMuP,cAAc,EAAI,CAAA,EAAIE,EAAYlN,EAAM8M,WAAW,GAAIK,EAAOD,EAAUvP,GAAG,CAAIqC,CAAAA,EAAMgN,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUpP,GAAG,CAAIkC,CAAAA,EAAMgN,cAAc,EAAI,CAAA,EAAI0W,EAAa,CAAC,EAAGC,EAAW,CAAC,CAAC,IAAI,CAACA,QAAQ,CAAEC,EAAsB3mB,EAAQ2mB,mBAAmB,CAAE1mB,EAAYD,EAAQC,SAAS,CAAEuP,EAAU,IAAI,CAACC,aAAa,EACxY,AAAiC,aAAjC,IAAI,CAACA,aAAa,CAAClI,IAAI,CAAC,KAAqBgH,EAAY,CAAC,CAACvO,EAAQyO,QAAQ,CAAEsX,EAAY,IAAI,CAACA,SAAS,EAAI,EAAGa,EAAiB,IAAI,CAACA,cAAc,CAAEtW,EAAS,CAAC9B,EAAOqY,EAAW7mB,AAA+B,MAA/BA,EAAQ8mB,kBAAkB,CAAUC,EAAa,AAAC,CAAA,IAAI,CAAC/kB,SAAS,CAAC,KAAK7B,MAAM,CAC5P,IAAI,CAAC6B,SAAS,CAAC,KACf,KAAK,CAAA,GACL,IAAI,CAAChC,OAAO,CAACwO,KAAK,EAClB,IAAI,CAACxM,SAAS,CAAC,IAAK,CAAA,GAAQyL,EAAYuS,GAAiBhgB,EAAQyN,SAAS,CAAE,GAAIuZ,EAAkBhnB,EAAQinB,eAAe,EAAI/W,EACjIiJ,EAAW,CAAA,EAEXhC,EAAUpU,EAAMqU,YAAY,CAACnX,GASjC,GAAIO,CAAAA,EAAMkC,SAAS,GAAIK,EAAML,SAAS,CAMtC,CAAA,GAFAyW,EAAWoH,GAAwB5gB,EAAO,IAAI,EAC9CA,EAAM4C,OAAO,CAAG,CAAA,EACX,IAAI,CAACjB,OAAO,EAIb,CAAA,IAAI,CAACY,MAAM,EAAI,IAAI,CAACglB,KAAK,AAAD,GACxB1E,GAAgB,IAAI,EAInBnC,AAxpGkB3gB,EAwpGgBC,IAW/B,IAAI,CAACmD,WAAW,EAChB,IAAI,CAACA,WAAW,GAAMujB,CAAAA,MAAAA,EAA+C,KAAK,EAAIA,EAAWvjB,WAAW,AAAD,GACnG,IAAI,CAACA,WAAW,CAACmF,OAAO,GAG5B,IAAI,CAACnF,WAAW,CAAGujB,MAAAA,EAA+C,KAAK,EAAIA,EAAWvjB,WAAW,CAG7FwjB,GAAeA,EAAY1iB,MAAM,EACjC,CAAA,IAAI,CAACod,YAAY,CACbsF,EAAY1iB,MAAM,CACd0iB,EAAY1iB,MAAM,CAACqE,OAAO,EAAC,IAnBnC,IAAI,CAACnF,WAAW,GAAMujB,CAAAA,MAAAA,EAA+C,KAAK,EAAIA,EAAWvjB,WAAW,AAAD,GACnG,CAAA,IAAI,CAACA,WAAW,CAAG,KAAK,CAAA,EAE5B,IAAI,CAACA,WAAW,CAAG,IAAI,CAACgV,SAAS,CAAC,cAAe,UAAW,UAAW,EAAGnY,EAAMoY,WAAW,EAAEC,QAAQ,CAAC,uBAmB1G,IAAI9V,EAAS,IAAI,CAACA,MAAM,CAAG,EAAE,CACzBilB,EAAa,SAAUC,CAAO,CAC9B1T,CAAK,CACLvC,CAAC,CACDoT,CAAU,EACN,IAAIxgB,EAAIgjB,EAAAA,GAAYA,CAAS,CAAChB,EAAY5U,EAAE,CAChDkW,EAAY,SAAU/C,CAAK,EACf3kB,EAAM6D,QAAQ,GACd8gB,EAAQ9jB,EAAMuE,GAAG,CAAGuf,EACxB5Q,EAAQ3Q,EAAMgC,GAAG,CAAG2O,GAExBxR,EAAOkF,IAAI,CAAC,CACRa,QAASuX,GACTzb,EAAGA,EACHqjB,QAAS9C,EACTA,MAAOA,EACP5Q,MAAOA,EACPvC,EAAG4U,EAAY5U,EACfoT,WAAYA,CAChB,EACJ,EAIA6C,EAAUtiB,KAAKwiB,IAAI,CAACF,GAEpB/pB,EAAQwpB,EAAWO,EAAUA,EAAU,IAAM1T,EAIzCiT,IACKF,CAAU,CAACppB,EAAM,CAIb0G,IAAMgjB,CAAS,CAACA,EAAU5mB,MAAM,CAAG,EAAE,GAG1C+B,EAAO/B,MAAM,GACbknB,EAAUD,KAPVX,CAAU,CAACppB,EAAM,CAAG,CAAA,EACpBgqB,EAAUD,IAStB,CAEA,CAAA,IAAI,CAACG,WAAW,CAAG/H,GACnBM,GAAsB,IAAI,CAAE,gBACxB,IAAI,CAACpb,EAAE,CAAC,SACR+I,EAAY,GACX6Y,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAY1iB,MAAM,AAAD,GAC5EyiB,GACA,CAACA,EAAWmB,eAAe,GAC3BnB,EAAWmB,eAAe,CAAG7nB,EAAMwZ,QAAQ,CAACjb,UAAU,CAAC,CACnDupB,QAAS,SACTC,SAAU,CACN,CACID,QAAS,eACTE,WAAY,CACRC,SAAU,SACVtQ,OAAQ,IAAO7J,CACnB,CACJ,EACH,CACDka,WAAY,CAAEjK,GAAI,WAAY,CAClC,GACA4I,EAAY1iB,MAAM,CAACud,IAAI,CAAC,CACpBwC,OAAQ,iBACZ,IAEAxK,IACAiH,GAA4BjH,EAAU,IAAI,EAC1CA,EAAS7D,UAAU,CAAC,IAAI,EAExBuO,GAA0B,IAAI,GA8FlC,IAAI9jB,EAAeoZ,EAAS5L,QAAQ,AAgB/B5N,CAAAA,EAAMwZ,QAAQ,CAACC,SAAS,GACrBrZ,EAAa+N,KAAK,CAACK,UAAU,EAC7B8G,QAAQO,IAAI,CAAC,oBAEjBmN,GAAUpU,EACN,IAAI,CAACtM,IAAI,CAACwS,KAAK,CAACsR,GACfvX,GAASmB,EA9GlB,SAAsB3R,CAAC,CAAEmT,CAAC,EAGtB,IAFI5Q,EACAY,EAEA4C,EACAE,EACAmjB,EACA1T,EACA6Q,EALA5T,EAAiB,AAAuB,KAAA,IAAhBhR,EAAMtC,KAAK,CAMnCiS,EAAM,CAAA,EACNoC,EAAY,CAAA,QAChB,CAAKyO,GAAQniB,KAGT,CAAC2S,IACGL,GACAvM,EAAI/F,CAAC,CAAC,EAAE,CACRiG,EAAIjG,CAAC,CAAC,EAAE,GAGR+F,EAAI/F,EACJiG,EAAI,AAA4E,OAA3E9C,CAAAA,EAAK,AAAoB,OAAnBZ,CAAAA,EAAK6P,CAAK,CAACe,EAAE,AAAD,GAAe5Q,AAAO,KAAK,IAAZA,EAAgBA,EAAKymB,CAAc,GAAe7lB,AAAO,KAAK,IAAZA,EAAgBA,EAAK,MAG7GqO,GACIc,GACArM,CAAAA,EAAIjG,EAAEyW,KAAK,CAAC,EAAG,EAAC,EAEpBnF,EAAMiX,CAAO,CAACpV,EAAE,CAChBlN,EAAIuiB,CAAQ,CAACrV,EAAE,EAEV5C,IACLxK,EAAI/F,EAAE+F,CAAC,CAEPuL,EAAMrL,AADNA,CAAAA,EAAIjG,EAAE0W,MAAM,AAAD,EACD1W,EAAEiG,CAAC,CACbsgB,EAAavmB,EAAEumB,UAAU,EAGxBqC,GACDlV,CAAAA,EAAY,AAACzN,CAAAA,GAAK,CAAA,GAAMiM,GAAQjM,GAAKkM,CAAG,EAElC,OAANlM,GAAcF,GAAK+L,GAAQ/L,GAAKiM,GAAQ0B,IACxC0V,EAAU5mB,EAAMoU,QAAQ,CAAC7Q,EAAG,CAAA,GACxB2iB,GACI,CAAA,AAAgB,KAAA,IAATP,GACPiB,IAAYnB,CAAU,IACjBzW,GACDF,CAAAA,EAAMrL,CAAAA,EAEN,CAAA,AAAgB,KAAA,IAATmiB,GACPniB,EAAIiiB,CAAK,IACTA,EAASjiB,EACTmiB,EAAOjV,GAEP,CAAA,AAAgB,KAAA,IAATgV,GACP7W,EAAMH,CAAK,IACXA,EAASG,EACT6W,EAAOhV,IAIV0V,GAAYO,IAAYnB,IAEL,KAAA,IAATE,IACPzS,EACI3Q,EAAM6R,QAAQ,CAACsR,EAAQ,CAAA,GAC3B/O,EACIpU,EAAM6R,QAAQ,CAACzF,EAAQ,CAAA,GAC3BgY,EAAWC,EAAS1T,EAAO0S,EAAM7B,GAC7BpN,IAAYzD,GACZyT,EAAWC,EAASjQ,EAASgP,EAAM5B,IAG3C4B,EAAOC,EAAO,KAAK,EACnBH,EAAcmB,IAKlBD,EAAWC,EADX1T,EAAQ5O,KAAKwiB,IAAI,CAACvkB,EAAM6R,QAAQ,CAAC3Q,EAAG,CAAA,IACTkN,EAAGoT,KAInC,CAAC5T,EACZ,EAKqB,WACbmP,GAAsBha,EAAO,kBAEjC,OAAOA,EAAMyhB,WAAW,CAGpBzhB,EAAM9F,OAAO,EACb8F,EAAMyhB,WAAW,GAEjBxnB,EAAa+N,KAAK,CAACK,UAAU,EAC7B8G,QAAQY,OAAO,CAAC,mBAExB,IAnNA,CA8NJ,CAKA,SAASgS,GAAqB7E,CAAO,EACjC,IAAIrN,EAAU,CAAA,EAMd,GALI,IAAI,CAAChW,KAAK,CAACK,OAAO,EAAI,IAAI,CAACL,KAAK,CAACK,OAAO,CAACF,KAAK,EAC9C6V,CAAAA,EAAU,AAA4C,KAAA,IAArC,IAAI,CAAChW,KAAK,CAACK,OAAO,CAACF,KAAK,CAAC6V,OAAO,EAE7C,IAAI,CAAChW,KAAK,CAACK,OAAO,CAACF,KAAK,CAAC6V,OAAO,AAAD,EAEnC,CAACA,GAAW,CAAC,IAAI,CAACpT,OAAO,CACzB,OAAOygB,EAAQnkB,IAAI,CAAC,IAAI,CAE5B,CAAA,IAAI,CAACc,KAAK,CAAC4C,OAAO,CAAG,CAAA,EAErB,IAAI4W,EAAWoH,GAAwB,IAAI,CAAC5gB,KAAK,CAC7C,IAAI,EACJwZ,IACAiH,GAA4BjH,EAAU,IAAI,EAC1CA,EAAS7D,UAAU,CAAC,IAAI,GAE5BuO,GAA0B,IAAI,CAClC,CAmDA,SAASiE,GAAsB9E,CAAO,EAClC,GAAI,IAAI,CAACzgB,OAAO,CAAE,CACd,GAAI2gB,GAAY,IAAI,EAChB,MAAO,CAAC,EAEZ,GAAI,IAAI,CAAC1iB,KAAK,CAACkC,SAAS,EAAI,IAAI,CAACK,KAAK,CAACL,SAAS,CAI5C,OAAO,IAAI,AAEnB,CACA,OAAOsgB,EAAQpG,KAAK,CAAC,IAAI,CAAE,EAAE,CAACnI,KAAK,CAAC5V,IAAI,CAAC8C,UAAW,GACxD,CAOA,SAASomB,GAAsB/E,CAAO,EAIlC,IAHIziB,EACAY,EACA2S,EACAkU,EAAgB,IAAI,CAAChoB,OAAO,CAACiC,IAAI,CACrC,GAAIqe,GAAa,IAAI,CAAC3gB,KAAK,GAAK6B,AA3uHmBrC,CA2uHD,CAAC,IAAI,CAACoC,IAAI,CAAC,CAAE,CAC3D,IAGI0iB,EAAYpkB,AAHH,IAAI,CAGM6E,EAAE,CAAC,YAClB,CAAC7E,AAJI,IAAI,CAID6E,EAAE,CAAC,WACX,CAAC7E,AALI,IAAI,CAKD6E,EAAE,CAAC,YACX,CAAC7E,AANI,IAAI,CAMD6E,EAAE,CAAC,WAInB,GAEA,CAAC6e,GAZY,IAAI,CAYUyE,IACvB/D,GACApkB,AAdS,IAAI,CAcN6E,EAAE,CAAC,YAEV7E,AAhBS,IAAI,CAgBNG,OAAO,CAACyO,QAAQ,EACvB,CAACyU,GAjBQ,IAAI,CAiBQ,CAAA,GAAO,CAE5B,GAAIrjB,AAnBK,IAAI,CAmBF0C,OAAO,EAAK,CAAA,AAAC,CAAA,AAAwB,OAAvBhC,CAAAA,EAAKV,AAnBrB,IAAI,CAmBwBW,KAAK,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmC,SAAS,AAAD,GAAO,CAAA,AAAwB,OAAvBvB,CAAAA,EAAKtB,AAnBhG,IAAI,CAmBmGkD,KAAK,AAAD,GAAe5B,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuB,SAAS,AAAD,CAAC,EACpK,MAGAuhB,CAAAA,GAAapkB,AAAsB,aAAtBA,AAvBR,IAAI,CAuBWkD,KAAK,CAACxB,IAAI,CAC9BmjB,GAAmB7lB,IAAI,CAxBlB,IAAI,CAwBuB8C,SAAS,CAAC,EAAE,EAG5CqhB,EAAQpG,KAAK,CA3BR,IAAI,CA2Ba,EAAE,CAACnI,KAAK,CAAC5V,IAAI,CAAC8C,UAAW,IAEnDqmB,EAAgBnoB,AA7BP,IAAI,CA6BUmC,SAAS,CAAC,IAAK,CAAA,EAC1C,CAKA,GAFAnC,AAjCa,IAAI,CAiCV0C,OAAO,CAAGghB,GAjCJ,IAAI,CAiC0ByE,GAEvCnoB,AAnCS,IAAI,CAmCN0C,OAAO,CAAE,CAEhB,IAAIoP,EAAa,KAAK,CACa,QAA9BmC,CAAAA,EAAKjU,AAtCD,IAAI,CAsCIG,OAAO,CAACiC,IAAI,AAAD,GAAe6R,AAAO,KAAK,IAAZA,IAAyBA,EAAG3T,MAAM,EAErE,AAAC4f,GADLpO,EAAa9R,AAvCR,IAAI,CAuCWooB,kBAAkB,CAACpoB,AAvClC,IAAI,CAuCqCG,OAAO,CAACiC,IAAI,IAErDoa,GAAQ1K,IACR9R,AA1CA,IAAI,CA0CG6E,EAAE,CAAC,YACXkb,GAAkB,GAAI,CAAA,EAAO/f,AA3C5B,IAAI,CA2C+BF,KAAK,EAGjDuoB,AAptBZ,SAAoBroB,CAAM,EAEtBA,EAAOC,KAAK,CAAGD,EAAOC,KAAK,EAAI,CAE3BgkB,SAAW,SAAUqE,CAAE,EAAI,OAAOrE,GAASjkB,EAAQsoB,EAAK,CAC5D,EACA,IALI5nB,EAKA6nB,EAAiBvoB,EAAOC,KAAK,CAACuoB,OAAO,CAAG,EAAE,CAoB9C,GAjBA,CAAC,UAAW,cAAe,iBAAiB,CAACjpB,OAAO,CAAC,SAAUV,CAAI,EAC/D0pB,EAAehhB,IAAI,CAAC,CAChB1I,KAAMA,EACN8L,IAAK3K,CAAM,CAACnB,EAAK,CACjB4pB,IAAKjqB,OAAOO,cAAc,CAACC,IAAI,CAACgB,EAAQnB,EAC5C,EACJ,GACAmB,EAAO0oB,OAAO,CAAG,CAAA,EACjB1oB,EAAO2oB,WAAW,CAAG,CAAA,EACrB3oB,EAAO4oB,cAAc,CAAG,CAAA,EAExB5oB,EAAO6oB,iBAAiB,CAAG,CAAA,EAEvB7oB,EAAO8oB,aAAa,EACpB9oB,CAAAA,EAAO8oB,aAAa,CAAG9oB,EAAO8oB,aAAa,CAAC1gB,OAAO,EAAC,EAGpDpI,EAAO6E,EAAE,CAAC,YACV,CAAC7E,EAAO6E,EAAE,CAAC,YACX7E,EAAOoC,IAAI,CAAC9B,MAAM,CAAE,CACpB,IAAK,IAAIG,EAAK,EAAGa,EAAKtB,EAAOoC,IAAI,CAAE3B,EAAKa,EAAGhB,MAAM,CAAEG,IAAM,CACrD,IAAIiT,EAAQpS,CAAE,CAACb,EAAG,AAClB,AAAuE,QAAtEC,CAAAA,EAAKgT,MAAAA,EAAqC,KAAK,EAAIA,EAAMtL,OAAO,AAAD,GAAe1H,AAAO,KAAK,IAAZA,GAAyBA,EAAG1B,IAAI,CAAC0U,EACpH,CACA1T,EAAOoC,IAAI,CAAC9B,MAAM,CAAG,EACrBN,EAAOqC,MAAM,CAAC/B,MAAM,CAAG,EACvB,OAAON,EAAOulB,aAAa,AAC/B,CACJ,EAioBqB,IAAI,CA+CjB,MAEIwD,AA5qBZ,SAAmB/oB,CAAM,EAErB,IADIU,EACAT,EAAQD,EAAOC,KAAK,CACpBH,EAAQE,EAAOF,KAAK,CACpB0mB,EAAa1mB,EAAMG,KAAK,CAC5B,GAAIumB,MAAAA,EAA+C,KAAK,EAAIA,EAAWvjB,WAAW,CAAE,CAChFujB,EAAWvjB,WAAW,CAACmF,OAAO,GAC9Boe,EAAWvjB,WAAW,CAAG,KAAK,EAC9B,IAAK,IAAIxC,EAAK,EAAGa,EAAKxB,EAAME,MAAM,CAAES,EAAKa,EAAGhB,MAAM,CAAEG,IAAM,CACtD,IAAImD,EAAItC,CAAE,CAACb,EAAG,AACdmD,CAAAA,EAAEX,WAAW,CAAG,KAAK,EACrBW,EAAEX,WAAW,CAAGW,EAAEqU,SAAS,CAAC,cAAe,UAAW,UAAW,EAAGnY,EAAMoY,WAAW,EAAEC,QAAQ,CAAC,qBACpG,CACJ,CAGIlY,IACA,AAACA,CAAAA,EAAMuoB,OAAO,EAAI,EAAE,AAAD,EAAGjpB,OAAO,CAAC,SAAUypB,CAAO,EACvCA,EAAQP,GAAG,CACXzoB,CAAM,CAACgpB,EAAQnqB,IAAI,CAAC,CAAGmqB,EAAQre,GAAG,CAIlC,OAAO3K,CAAM,CAACgpB,EAAQnqB,IAAI,CAAC,AAEnC,GAEIoB,EAAM6C,KAAK,EACX7C,EAAM6C,KAAK,IAInB,AAA+C,OAA9CpC,CAAAA,EAAMZ,EAAMoY,WAAW,EAAIlY,EAAO4gB,KAAK,GAAelgB,AAAO,KAAK,IAAZA,GAAyBA,EAAGgiB,IAAI,EAC3F,EA0lBqB,IAAI,CAoDrB,MAEIS,EAAQpG,KAAK,CAAC,IAAI,CAAE,EAAE,CAACnI,KAAK,CAAC5V,IAAI,CAAC8C,UAAW,GAErD,CAKA,SAASmnB,GAAsB9F,CAAO,EAClC,IAAI/F,EAAS+F,EAAQpG,KAAK,CAAC,IAAI,CAC3B,EAAE,CAACnI,KAAK,CAAC5V,IAAI,CAAC8C,UAAW,WAC7B,AAAI,IAAI,CAAC7B,KAAK,EAAImd,EACP,IAAI,CAACnd,KAAK,CAACgkB,QAAQ,CAAC7G,GAExBA,CACX,CAY6B,OA/kC7B,SAA6BlgB,CAAW,CAAEC,CAAW,CAAEC,CAAU,CAAEE,CAAO,EACtE,GAAI8iB,GAAuBX,GAAsB,gBAAiB,CAC9D,IAAIyJ,EAAgB1J,KAAa2J,WAAW,CACxCC,EAAgBlsB,EAAY4B,SAAS,CAsCzC,GArCA+gB,GAAqB3iB,EAAa,UAAW0mB,IAC7C/D,GAAqB3iB,EAAa,OAAQ6mB,IACtCzmB,GACA8rB,CAAAA,EAAcC,YAAY,CAAGlD,EAAiB,EAElD9F,GAAK+I,EAAe,cAAenB,IACnC5H,GAAK+I,EAAe,cAAelB,IACnC7H,GAAK+I,EAAe,cAAeH,IACnC,CACI,YACA,iBACA,cACA,aACA,SACH,CAAC1pB,OAAO,CAAC,SAAU+pB,CAAM,EACtB,OAAOC,AAu6BnB,SAA6BC,CAAW,CAAErsB,CAAW,CAAEmsB,CAAM,EAIzD,SAASG,EAAOtG,CAAO,EACnB,IAAIuG,EAAY,IAAI,CAACvpB,OAAO,CAACyO,QAAQ,EAC5B0a,CAAAA,AAAW,cAAXA,GAA0BA,AAAW,mBAAXA,CAA0B,CACzD,AAAC,CAAA,IAAI,CAAC5mB,OAAO,GACbgnB,GACCjJ,GAAa,IAAI,CAAC3gB,KAAK,GACxB,AAAc,YAAd,IAAI,CAAC4B,IAAI,EACT,AAAc,YAAd,IAAI,CAACA,IAAI,EACRC,AAnrH0CrC,CAmrHxB,CAAC,IAAI,CAACoC,IAAI,CAAC,EAC9B,AAAgC,IAAhC,IAAI,CAACvB,OAAO,CAACqB,cAAc,CAIX,WAAX8nB,GAAuB,IAAI,CAACD,YAAY,EAC7C,IAAI,CAACA,YAAY,GAJjBlG,EAAQnkB,IAAI,CAAC,IAAI,CAMzB,CAGA,GAFAqhB,GAAKmJ,EAAaF,EAAQG,GAEtBH,AAAW,cAAXA,EACA,IAAK,IAAI7oB,EAAK,EAAGC,EAAK,CAClB,SACA,YACA,cACA,UACA,UACH,CAAED,EAAKC,EAAGJ,MAAM,CAAEG,IAAM,CACrB,IAAIiB,EAAOhB,CAAE,CAACD,EAAG,AACbtD,CAAAA,CAAW,CAACuE,EAAK,EACjB2e,GAAKljB,CAAW,CAACuE,EAAK,CAAC5C,SAAS,CAAEwqB,EAAQG,EAElD,CAER,EA58BuCL,EAAejsB,EAAamsB,EAC3D,GACAjJ,GAAKjjB,EAAW0B,SAAS,CAAE,iBAAkB,SAAUqkB,CAAO,CAAEzhB,CAAI,CAAE+B,CAAC,EACnE,IAAI/C,EACAY,EACJ,GAAII,AAAS,UAATA,GAAoB,IAAI,CAAC1B,MAAM,CAAC0C,OAAO,CAAE,CACzC,IAAIgR,EAAQjQ,EAAEiQ,KAAK,CACnB,GAAI,AAACA,CAAAA,EAAM6Q,IAAI,EAAI7Q,EAAM8Q,KAAK,AAAD,GAAO,CAAA,AAA6F,OAA5FljB,CAAAA,EAAK,AAAuC,OAAtCZ,CAAAA,EAAKgT,EAAM1T,MAAM,CAACG,OAAO,CAAC0V,MAAM,AAAD,GAAenV,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG+W,MAAM,AAAD,GAAenW,AAAO,KAAK,IAAZA,EAAgBA,EAAK,EAAC,EAC3J,MAER,CACA,OAAO6hB,EAAQpG,KAAK,CAAC,IAAI,CAAE,EAAE,CAACnI,KAAK,CAAC5V,IAAI,CAAC8C,UAAW,GACxD,GAEAzC,EAAiBE,OAAO,CAAC,SAAUmC,CAAI,EACnC,IAAIioB,EAAkBT,CAAa,CAACxnB,EAAK,CACrCioB,IACAA,EAAgBnoB,cAAc,CAAG,IACjCmoB,EAAgBzX,SAAS,CAAG,EAAE,CAC9B/U,CAAW,CAACuE,EAAK,CAAC5C,SAAS,CAACyZ,WAAW,CAAG,CAAA,EAElD,GACIjb,EAAS,CACT,IAAIssB,EAAazsB,EAAY0sB,IAAI,CAC7BC,EAAmB3sB,EAAY4sB,UAAU,CACzCC,EAAe7sB,EAAY8sB,MAAM,CACjCC,EAAe/sB,EAAYmf,MAAM,CACjC6N,EAAgBhtB,EAAYitB,OAAO,CACnCC,EAAgBltB,EAAYmtB,OAAO,CACnCC,EAAgBptB,EAAYqtB,OAAO,CAevC,GAdIZ,GACA5J,GAAO4J,EAAW9qB,SAAS,CAAE,CACzB4T,KAAM,CAAA,EACN6F,YAAa,CAAA,EACbsO,SAAU,CAAA,CACd,GAEAiD,GACA9J,GAAO8J,EAAiBhrB,SAAS,CAAE,CAC/B4T,KAAM,CAAA,EACN6F,YAAa,CAAA,EACbsO,SAAU,CAAA,CACd,GAEAmD,EAAc,CACd,IAAIS,EAAgBT,EAAalrB,SAAS,AAG1C,QAAO2rB,EAAc/C,WAAW,CAGhCrH,GAAKoK,EAAe,gBAAiB,SAAUtH,CAAO,QAClD,CAAI,IAAI,CAACzgB,OAAO,EAGTygB,EAAQpG,KAAK,CAAC,IAAI,CAAE,EAAE,CAACnI,KAAK,CAAC5V,IAAI,CAAC8C,UAAW,GACxD,EACJ,CACIooB,GACAlK,GAAOkK,EAAaprB,SAAS,CAAE,CAC3B4T,KAAM,CAAA,EACNmU,SAAU,CAAA,CACd,GAEAwD,GACAA,CAAAA,EAAcvrB,SAAS,CAAC4T,IAAI,CAAG,CAAA,CAAG,EAKtC,CAACyX,EAAeI,EAAc,CAAChrB,OAAO,CAAC,SAAUmrB,CAAE,EAC3CA,GACArK,GAAKqK,EAAG5rB,SAAS,CAAE,aAAckpB,GAEzC,EACJ,CACJ,CACA,OAAO9qB,CACX,EA4pCiCytB,GAHf,CACdC,oBAtJsB,CACtBC,UAAW,UACXC,aAAc,UACdC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,MAAO,UACPC,OAAQ,UACRC,eAAgB,UAChBC,KAAM,UACNC,WAAY,UACZC,MAAO,UACPC,UAAW,UACXC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,MAAO,UACPC,eAAgB,UAChBC,SAAU,UACVC,QAAS,UACTC,KAAM,UACNC,SAAU,UACVC,SAAU,UACVC,cAAe,UACfC,SAAU,UACVC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,eAAgB,UAChBC,WAAY,UACZC,WAAY,UACZC,QAAS,UACTC,WAAY,UACZC,aAAc,UACdC,cAAe,UACfC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,SAAU,UACVC,YAAa,UACbC,QAAS,UACTC,WAAY,UACZC,SAAU,UACVC,UAAW,UACXC,YAAa,UACbC,YAAa,UACbC,QAAS,UACTC,UAAW,UACXC,WAAY,UACZC,KAAM,UACNC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,MAAO,UACPC,YAAa,UACbC,SAAU,UACVC,QAAS,UACTC,UAAW,UACXC,OAAQ,UACRC,MAAO,UACPC,MAAO,UACPC,SAAU,UACVC,cAAe,UACfC,UAAW,UACXC,aAAc,UACdC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,qBAAsB,UACtBC,UAAW,UACXC,WAAY,UACZC,UAAW,UACXC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,eAAgB,UAChBC,eAAgB,UAChBC,eAAgB,UAChBC,YAAa,UACbC,KAAM,UACNC,UAAW,UACXC,MAAO,UACPC,QAAS,UACTC,OAAQ,UACRC,iBAAkB,UAClBC,WAAY,UACZC,aAAc,UACdC,aAAc,UACdC,eAAgB,UAChBC,gBAAiB,UACjBC,kBAAmB,UACnBC,gBAAiB,UACjBC,gBAAiB,UACjBC,aAAc,UACdC,UAAW,UACXC,UAAW,UACXC,SAAU,UACVC,YAAa,UACbC,KAAM,UACNC,QAAS,UACTC,MAAO,UACPC,UAAW,UACXC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,cAAe,UACfC,UAAW,UACXC,cAAe,UACfC,cAAe,UACfC,WAAY,UACZC,UAAW,UACXC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,WAAY,UACZC,OAAQ,UACRC,IAAK,UACLC,UAAW,UACXC,UAAW,UACXC,YAAa,UACbC,OAAQ,UACRC,WAAY,UACZC,SAAU,UACVC,SAAU,UACVC,OAAQ,UACRC,OAAQ,UACRC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,KAAM,UACNC,YAAa,UACbC,UAAW,UACXC,IAAK,UACLC,KAAM,UACNC,QAAS,UACTC,OAAQ,UACRC,UAAW,UACXC,OAAQ,UACRC,UAAW,UACXC,MAAO,UACPC,WAAY,UACZC,OAAQ,UACRC,YAAa,SACjB,CAQA,EAgBIC,GAAgD,WAShD,MAAOA,AARPA,CAAAA,GAAWn1B,OAAOo1B,MAAM,EAAI,SAAS1xB,CAAC,EAClC,IAAK,IAAI0B,EAAG0N,EAAI,EAAGtT,EAAI8D,UAAUxB,MAAM,CAAEgR,EAAItT,EAAGsT,IAE5C,IAAK,IAAIjH,KADTzG,EAAI9B,SAAS,CAACwP,EAAE,CACK9S,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC4E,EAAGyG,IACzDnI,CAAAA,CAAC,CAACmI,EAAE,CAAGzG,CAAC,CAACyG,EAAE,AAAD,EAElB,OAAOnI,CACX,CAAA,EACgB6a,KAAK,CAAC,IAAI,CAAEjb,UAChC,EACI+xB,GAA8D,SAAUtnB,CAAE,CAAED,CAAI,CAAE4P,CAAI,EACtF,GAAIA,GAAQpa,AAAqB,GAArBA,UAAUxB,MAAM,CAAQ,IAAK,IAA4B6b,EAAxB7K,EAAI,EAAG8K,EAAI9P,EAAKhM,MAAM,CAAMgR,EAAI8K,EAAG9K,KACxE6K,GAAQ7K,KAAKhF,IACR6P,GAAIA,CAAAA,EAAK/C,MAAMta,SAAS,CAAC8V,KAAK,CAAC5V,IAAI,CAACsN,EAAM,EAAGgF,EAAC,EACnD6K,CAAE,CAAC7K,EAAE,CAAGhF,CAAI,CAACgF,EAAE,EAGvB,OAAO/E,EAAGiJ,MAAM,CAAC2G,GAAM/C,MAAMta,SAAS,CAAC8V,KAAK,CAAC5V,IAAI,CAACsN,GACtD,EAIIwnB,GAAY,AAAC10B,IAA+EuN,GAAG,CAAEonB,GAAY,AAAC30B,IAA+EwN,GAAG,CAGhMonB,GAAiB,AAAC50B,IAA+EM,QAAQ,CAAEu0B,GAAc,AAAC70B,IAA+EmG,KAAK,CAM9M2uB,GAAiB,CACjB,QACA,qBACA,YACA,YACH,CA2ED,SAASC,KACL,IAAIpxB,EACA2C,EAAK,CAAA,EACT,GAAI,AAA2C,KAAA,IAApCquB,GAAUK,qBAAqB,CAAkB,CACxDrxB,EAAS+wB,GAAUxZ,aAAa,CAAC,UACjC,IAAK,IAAIhJ,EAAI,EAAGA,EAAI4iB,GAAe5zB,MAAM,CAAE,EAAEgR,EACzC,GAAI,CAEA,GAAI,MADCvO,EAAO6W,UAAU,CAACsa,EAAc,CAAC5iB,EAAE,EAEpC,MAAO,CAAA,CAEf,CACA,MAAO7N,EAAG,CAEV,CAER,CACA,MAAO,CAAA,CACX,CAU6B,IAoPzB4wB,GAAKj1B,GACTi1B,CAAAA,GAAEF,eAAe,CAvPIA,GA5FEn3B,EAoVHq3B,GAAEC,KAAK,CApVQr3B,EAoVNo3B,GAAEE,IAAI,CApVWr3B,EAoVTm3B,GAAEG,MAAM,CApVcr3B,EAoVZk3B,GAAEl3B,WAAW,CApVYC,EAoVVi3B,GAAEI,KAAK,CApVep3B,EAoVbg3B,GAAEK,KAAK,EAnVtEp3B,EAAU62B,QAEN,AAA2G,KAAA,IAApG,AAAC/0B,IAA+Eu1B,eAAe,CAEtGv1B,IAA8Eu1B,eAAe,GAG7FV,GAAY,KAGhB52B,GAAc,CAACA,EAAWu3B,KAAK,CAAC5F,oBAAoB,EACpD3xB,CAAAA,EAAWu3B,KAAK,CAAGjB,GAASA,GAAS,CAAC,EAAGt2B,EAAWu3B,KAAK,EAAGjK,GAAYC,mBAAmB,CAAA,EAvhIlF5tB,EA0hIYA,EAAYM,GAzhItBsC,EAAWH,EAAU,gBAChCzC,EAAW8B,SAAS,CAAC+1B,SAAS,CAACttB,IAAI,CAACjF,GAyhIxCwyB,GAA0B53B,EAAaC,EAAaC,EAAYE,GAIhE02B,GAAe/2B,EAAW,cAAe,SAAUwG,CAAC,EAShD,IAAK,IARD/C,EACAY,EACA2S,EAEA8gB,EAAgBlB,GAAkB,CAAC,IAAI,CAAC/zB,KAAK,CAAC,CAC9C,IAAI,CAACE,MAAM,CACX,CAAA,GAAM6e,GAAG,CAAC,SAAUrf,CAAI,EAAI,OAAOA,EAAK2hB,YAAY,AAAE,GACjD2C,MAAM,CAAC9C,SACPvgB,EAAK,EAAoCA,EAAKu0B,AAApBD,EAAoCz0B,MAAM,CAAEG,IAAM,CACjF,IAAI0gB,EAAe6T,AADYD,CACG,CAACt0B,EAAG,CACvB6V,EAAQnC,AAAd,IAAI,CAAamC,KAAK,CAAElT,EAAM+Q,AAA9B,IAAI,CAA6B/Q,GAAG,CAAE6xB,EAAW3e,EAAQ,SAAW,SAAU4e,EAAe5e,EAAQ,aAAe,aAAc6e,EAAY,AAA8F,OAA7F7zB,CAAAA,EAAK6f,MAAAA,EAAmD,KAAK,EAAIA,CAAY,CAAC8T,EAAS,AAAD,GAAe3zB,AAAO,KAAK,IAAZA,EAAgBA,EAAK,EAC9Q8zB,EAAQ,EACRjyB,EAAY,EACZgf,EAAU,EACV2B,EAAS,MACT,CAAA,IAAI,CAACjhB,SAAS,GACduyB,EAAQ,AAAC,CAAA,AAAmB,OAAlBnhB,CAAAA,EAAKxQ,EAAE2xB,KAAK,AAAD,GAAenhB,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAAA,EAAKkhB,EAC9DhyB,EAAY,AAAC,CAAA,AAACge,CAAAA,MAAAA,EAAmD,KAAK,EAAIA,CAAY,CAAC+T,EAAa,AAAD,GAAM,CAAA,EACrGE,EAAS3xB,CAAAA,EAAE4xB,IAAI,EAAI,CAAA,EACnBF,EAAY/xB,EACZgyB,EAAQhyB,EACZ+e,EAAU,GACV2B,EAAS,aAEb3C,MAAAA,GAA4DA,EAAaG,IAAI,CAAE5gB,CAAAA,AAC3EA,CAD2EA,EAAK,CAAC,CAAA,CAC/E,CAACu0B,EAAS,CAAGG,EACf10B,CAAE,CAACw0B,EAAa,CAAG/xB,EACnBzC,CAAC,GAAIshB,GAAG,CAAC,CACTsT,WAAY,8BACZxR,OAAQA,EACR3B,QAASA,CACb,EACJ,CACJ,GA+RyB,IAAIjjB,GAAcE,IAGrC,OADYH,EAAoB,OAAU,AAE3C,GAET"}