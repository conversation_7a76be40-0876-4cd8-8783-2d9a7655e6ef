{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/networkgraph\n * @requires highcharts\n *\n * Force directed graph module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SVGElement\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/networkgraph\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SVGElement\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/networkgraph\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SVGElement\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SVGElement\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__28__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ networkgraph_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es5/es-modules/Series/DragNodesComposition.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass) {\n    if (pushUnique(composed, 'DragNodes')) {\n        addEvent(ChartClass, 'load', onChartLoad);\n    }\n}\n/**\n * Draggable mode:\n * @private\n */\nfunction onChartLoad() {\n    var chart = this;\n    var mousedownUnbinder,\n        mousemoveUnbinder,\n        mouseupUnbinder,\n        point;\n    if (chart.container) {\n        mousedownUnbinder = addEvent(chart.container, 'mousedown', function (event) {\n            if (mousemoveUnbinder) {\n                mousemoveUnbinder();\n            }\n            if (mouseupUnbinder) {\n                mouseupUnbinder();\n            }\n            point = chart.hoverPoint;\n            if (point &&\n                point.series &&\n                point.series.hasDraggableNodes &&\n                point.series.options.draggable) {\n                point.series.onMouseDown(point, event);\n                mousemoveUnbinder = addEvent(chart.container, 'mousemove', function (e) { return (point &&\n                    point.series &&\n                    point.series.onMouseMove(point, e)); });\n                mouseupUnbinder = addEvent(chart.container.ownerDocument, 'mouseup', function (e) {\n                    mousemoveUnbinder();\n                    mouseupUnbinder();\n                    return point &&\n                        point.series &&\n                        point.series.onMouseUp(point, e);\n                });\n            }\n        });\n    }\n    addEvent(chart, 'destroy', function () {\n        mousedownUnbinder();\n    });\n}\n/**\n * Mouse down action, initializing drag&drop mode.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n * @param {Highcharts.PointerEventObject} event\n *        Browser event, before normalization.\n */\nfunction onMouseDown(point, event) {\n    var _a;\n    var normalizedEvent = ((_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.normalize(event)) || event;\n    point.fixedPosition = {\n        chartX: normalizedEvent.chartX,\n        chartY: normalizedEvent.chartY,\n        plotX: point.plotX,\n        plotY: point.plotY\n    };\n    point.inDragMode = true;\n}\n/**\n * Mouse move action during drag&drop.\n *\n * @private\n *\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n * @param {global.Event} event\n *        Browser event, before normalization.\n */\nfunction onMouseMove(point, event) {\n    var _a;\n    if (point.fixedPosition && point.inDragMode) {\n        var series = this,\n            chart = series.chart,\n            normalizedEvent = ((_a = chart.pointer) === null || _a === void 0 ? void 0 : _a.normalize(event)) || event,\n            diffX = point.fixedPosition.chartX - normalizedEvent.chartX,\n            diffY = point.fixedPosition.chartY - normalizedEvent.chartY,\n            graphLayoutsLookup = chart.graphLayoutsLookup;\n        var newPlotX = void 0,\n            newPlotY = void 0;\n        // At least 5px to apply change (avoids simple click):\n        if (Math.abs(diffX) > 5 || Math.abs(diffY) > 5) {\n            newPlotX = point.fixedPosition.plotX - diffX;\n            newPlotY = point.fixedPosition.plotY - diffY;\n            if (chart.isInsidePlot(newPlotX, newPlotY)) {\n                point.plotX = newPlotX;\n                point.plotY = newPlotY;\n                point.hasDragged = true;\n                this.redrawHalo(point);\n                graphLayoutsLookup.forEach(function (layout) {\n                    layout.restartSimulation();\n                });\n            }\n        }\n    }\n}\n/**\n * Mouse up action, finalizing drag&drop.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that event occurred.\n */\nfunction onMouseUp(point) {\n    if (point.fixedPosition) {\n        if (point.hasDragged) {\n            if (this.layout.enableSimulation) {\n                this.layout.start();\n            }\n            else {\n                this.chart.redraw();\n            }\n        }\n        point.inDragMode = point.hasDragged = false;\n        if (!this.options.fixedDraggable) {\n            delete point.fixedPosition;\n        }\n    }\n}\n/**\n * Redraw halo on mousemove during the drag&drop action.\n *\n * @private\n * @param {Highcharts.Point} point\n *        The point that should show halo.\n */\nfunction redrawHalo(point) {\n    if (point && this.halo) {\n        this.halo.attr({\n            d: point.haloPath(this.options.states.hover.halo.size)\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DragNodesComposition = {\n    compose: compose,\n    onMouseDown: onMouseDown,\n    onMouseMove: onMouseMove,\n    onMouseUp: onMouseUp,\n    redrawHalo: redrawHalo\n};\n/* harmony default export */ var Series_DragNodesComposition = (DragNodesComposition);\n\n;// ./code/es5/es-modules/Series/GraphLayoutComposition.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar setAnimation = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setAnimation;\n\nvar GraphLayoutComposition_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar GraphLayoutComposition_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, GraphLayoutComposition_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Constants\n *\n * */\nvar integrations = {};\nvar layouts = {};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction GraphLayoutComposition_compose(ChartClass) {\n    if (GraphLayoutComposition_pushUnique(GraphLayoutComposition_composed, 'GraphLayout')) {\n        GraphLayoutComposition_addEvent(ChartClass, 'afterPrint', onChartAfterPrint);\n        GraphLayoutComposition_addEvent(ChartClass, 'beforePrint', onChartBeforePrint);\n        GraphLayoutComposition_addEvent(ChartClass, 'predraw', onChartPredraw);\n        GraphLayoutComposition_addEvent(ChartClass, 'render', onChartRender);\n    }\n}\n/**\n * Re-enable simulation after print.\n * @private\n */\nfunction onChartAfterPrint() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach(function (layout) {\n            // Return to default simulation\n            layout.updateSimulation();\n        });\n        this.redraw();\n    }\n}\n/**\n * Disable simulation before print if enabled.\n * @private\n */\nfunction onChartBeforePrint() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach(function (layout) {\n            layout.updateSimulation(false);\n        });\n        this.redraw();\n    }\n}\n/**\n * Clear previous layouts.\n * @private\n */\nfunction onChartPredraw() {\n    if (this.graphLayoutsLookup) {\n        this.graphLayoutsLookup.forEach(function (layout) {\n            layout.stop();\n        });\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    var systemsStable,\n        afterRender = false;\n    var layoutStep = function (layout) {\n            if (layout.maxIterations-- &&\n                isFinite(layout.temperature) &&\n                !layout.isStable() &&\n                !layout.enableSimulation) {\n                // Hook similar to build-in addEvent, but instead of\n                // creating whole events logic, use just a function.\n                // It's faster which is important for rAF code.\n                // Used e.g. in packed-bubble series for bubble radius\n                // calculations\n                if (layout.beforeStep) {\n                    layout.beforeStep();\n            }\n            layout.step();\n            systemsStable = false;\n            afterRender = true;\n        }\n    };\n    if (this.graphLayoutsLookup) {\n        setAnimation(false, this);\n        // Start simulation\n        this.graphLayoutsLookup.forEach(function (layout) { return layout.start(); });\n        // Just one sync step, to run different layouts similar to\n        // async mode.\n        while (!systemsStable) {\n            systemsStable = true;\n            this.graphLayoutsLookup.forEach(layoutStep);\n        }\n        if (afterRender) {\n            this.series.forEach(function (series) {\n                if (series && series.layout) {\n                    series.render();\n                }\n            });\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar GraphLayoutComposition = {\n    compose: GraphLayoutComposition_compose,\n    integrations: integrations,\n    layouts: layouts\n};\n/* harmony default export */ var Series_GraphLayoutComposition = (GraphLayoutComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/NodesComposition.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar _a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series, seriesProto = _a.prototype, pointProto = _a.prototype.pointClass.prototype;\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Composition\n *\n * */\nvar NodesComposition;\n(function (NodesComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(PointClass, SeriesClass) {\n        var pointProto = PointClass.prototype,\n            seriesProto = SeriesClass.prototype;\n        pointProto.setNodeState = setNodeState;\n        pointProto.setState = setNodeState;\n        pointProto.update = updateNode;\n        seriesProto.destroy = destroy;\n        seriesProto.setData = setData;\n        return SeriesClass;\n    }\n    NodesComposition.compose = compose;\n    /**\n     * Create a single node that holds information on incoming and outgoing\n     * links.\n     * @private\n     */\n    function createNode(id) {\n        var PointClass = this.pointClass,\n            findById = function (nodes,\n            id) { return find(nodes,\n            function (node) { return node.id === id; }); };\n        var node = findById(this.nodes,\n            id),\n            options;\n        if (!node) {\n            options = this.options.nodes && findById(this.options.nodes, id);\n            var newNode_1 = new PointClass(this,\n                extend({\n                    className: 'highcharts-node',\n                    isNode: true,\n                    id: id,\n                    y: 1 // Pass isNull test\n                },\n                options));\n            newNode_1.linksTo = [];\n            newNode_1.linksFrom = [];\n            /**\n             * Return the largest sum of either the incoming or outgoing links.\n             * @private\n             */\n            newNode_1.getSum = function () {\n                var sumTo = 0,\n                    sumFrom = 0;\n                newNode_1.linksTo.forEach(function (link) {\n                    sumTo += link.weight || 0;\n                });\n                newNode_1.linksFrom.forEach(function (link) {\n                    sumFrom += link.weight || 0;\n                });\n                return Math.max(sumTo, sumFrom);\n            };\n            /**\n             * Get the offset in weight values of a point/link.\n             * @private\n             */\n            newNode_1.offset = function (point, coll) {\n                var offset = 0;\n                for (var i = 0; i < newNode_1[coll].length; i++) {\n                    if (newNode_1[coll][i] === point) {\n                        return offset;\n                    }\n                    offset += newNode_1[coll][i].weight;\n                }\n            };\n            // Return true if the node has a shape, otherwise all links are\n            // outgoing.\n            newNode_1.hasShape = function () {\n                var outgoing = 0;\n                newNode_1.linksTo.forEach(function (link) {\n                    if (link.outgoing) {\n                        outgoing++;\n                    }\n                });\n                return (!newNode_1.linksTo.length ||\n                    outgoing !== newNode_1.linksTo.length);\n            };\n            newNode_1.index = this.nodes.push(newNode_1) - 1;\n            node = newNode_1;\n        }\n        node.formatPrefix = 'node';\n        // For use in formats\n        node.name = node.name || node.options.id || '';\n        // Mass is used in networkgraph:\n        node.mass = pick(\n        // Node:\n        node.options.mass, node.options.marker && node.options.marker.radius, \n        // Series:\n        this.options.marker && this.options.marker.radius, \n        // Default:\n        4);\n        return node;\n    }\n    NodesComposition.createNode = createNode;\n    /**\n     * Destroy all nodes and links.\n     * @private\n     */\n    function destroy() {\n        // Nodes must also be destroyed (#8682, #9300)\n        this.data = []\n            .concat(this.points || [], this.nodes);\n        return seriesProto.destroy.apply(this, arguments);\n    }\n    NodesComposition.destroy = destroy;\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects but\n     * pushed to the this.nodes array.\n     * @private\n     */\n    function generatePoints() {\n        var _this = this;\n        var chart = this.chart,\n            nodeLookup = {};\n        seriesProto.generatePoints.call(this);\n        if (!this.nodes) {\n            this.nodes = []; // List of Point-like node items\n        }\n        this.colorCounter = 0;\n        // Reset links from previous run\n        this.nodes.forEach(function (node) {\n            node.linksFrom.length = 0;\n            node.linksTo.length = 0;\n            node.level = node.options.level;\n        });\n        // Create the node list and set up links\n        this.points.forEach(function (point) {\n            if (defined(point.from)) {\n                if (!nodeLookup[point.from]) {\n                    nodeLookup[point.from] = _this.createNode(point.from);\n                }\n                nodeLookup[point.from].linksFrom.push(point);\n                point.fromNode = nodeLookup[point.from];\n                // Point color defaults to the fromNode's color\n                if (chart.styledMode) {\n                    point.colorIndex = pick(point.options.colorIndex, nodeLookup[point.from].colorIndex);\n                }\n                else {\n                    point.color =\n                        point.options.color || nodeLookup[point.from].color;\n                }\n            }\n            if (defined(point.to)) {\n                if (!nodeLookup[point.to]) {\n                    nodeLookup[point.to] = _this.createNode(point.to);\n                }\n                nodeLookup[point.to].linksTo.push(point);\n                point.toNode = nodeLookup[point.to];\n            }\n            point.name = point.name || point.id; // For use in formats\n        }, this);\n        // Store lookup table for later use\n        this.nodeLookup = nodeLookup;\n    }\n    NodesComposition.generatePoints = generatePoints;\n    /**\n     * Destroy all nodes on setting new data\n     * @private\n     */\n    function setData() {\n        if (this.nodes) {\n            this.nodes.forEach(function (node) {\n                node.destroy();\n            });\n            this.nodes.length = 0;\n        }\n        seriesProto.setData.apply(this, arguments);\n    }\n    /**\n     * When hovering node, highlight all connected links. When hovering a link,\n     * highlight all connected nodes.\n     * @private\n     */\n    function setNodeState(state) {\n        var args = arguments,\n            others = this.isNode ? this.linksTo.concat(this.linksFrom) :\n                [this.fromNode,\n            this.toNode];\n        if (state !== 'select') {\n            others.forEach(function (linkOrNode) {\n                if (linkOrNode && linkOrNode.series) {\n                    pointProto.setState.apply(linkOrNode, args);\n                    if (!linkOrNode.isNode) {\n                        if (linkOrNode.fromNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.fromNode, args);\n                        }\n                        if (linkOrNode.toNode && linkOrNode.toNode.graphic) {\n                            pointProto.setState.apply(linkOrNode.toNode, args);\n                        }\n                    }\n                }\n            });\n        }\n        pointProto.setState.apply(this, args);\n    }\n    NodesComposition.setNodeState = setNodeState;\n    /**\n     * When updating a node, don't update `series.options.data`, but\n     * `series.options.nodes`\n     * @private\n     */\n    function updateNode(options, redraw, animation, runEvent) {\n        var _this = this;\n        var nodes = this.series.options.nodes,\n            data = this.series.options.data,\n            dataLength = (data === null || data === void 0 ? void 0 : data.length) || 0,\n            linkConfig = data === null || data === void 0 ? void 0 : data[this.index];\n        pointProto.update.call(this, options, this.isNode ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        if (this.isNode) {\n            // `this.index` refers to `series.nodes`, not `options.nodes` array\n            var nodeIndex = (nodes || [])\n                    .reduce(// Array.findIndex needs a polyfill\n                function (prevIndex,\n                n,\n                index) {\n                    return (_this.id === n.id ? index : prevIndex);\n            }, -1), \n            // Merge old config with new config. New config is stored in\n            // options.data, because of default logic in point.update()\n            nodeConfig = merge(nodes && nodes[nodeIndex] || {}, (data === null || data === void 0 ? void 0 : data[this.index]) || {});\n            // Restore link config\n            if (data) {\n                if (linkConfig) {\n                    data[this.index] = linkConfig;\n                }\n                else {\n                    // Remove node from config if there's more nodes than links\n                    data.length = dataLength;\n                }\n            }\n            // Set node config\n            if (nodes) {\n                if (nodeIndex >= 0) {\n                    nodes[nodeIndex] = nodeConfig;\n                }\n                else {\n                    nodes.push(nodeConfig);\n                }\n            }\n            else {\n                this.series.options.nodes = [nodeConfig];\n            }\n            if (pick(redraw, true)) {\n                this.series.chart.redraw(animation);\n            }\n        }\n    }\n    NodesComposition.updateNode = updateNode;\n})(NodesComposition || (NodesComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Series_NodesComposition = (NodesComposition);\n\n;// ./code/es5/es-modules/Series/Networkgraph/NetworkgraphPoint.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar NetworkgraphPoint_a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series, NetworkgraphPoint_seriesProto = NetworkgraphPoint_a.prototype, Point = NetworkgraphPoint_a.prototype.pointClass;\n\nvar NetworkgraphPoint_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, css = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).css, NetworkgraphPoint_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, NetworkgraphPoint_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, NetworkgraphPoint_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\nvar NetworkgraphPoint = /** @class */ (function (_super) {\n    __extends(NetworkgraphPoint, _super);\n    /**\n     * Basic `point.init()` and additional styles applied when\n     * `series.draggable` is enabled.\n     * @private\n     */\n    function NetworkgraphPoint(series, options, x) {\n        var _this = _super.call(this,\n            series,\n            options,\n            x) || this;\n        if (_this.series.options.draggable &&\n            !_this.series.chart.styledMode) {\n            NetworkgraphPoint_addEvent(_this, 'mouseOver', function () {\n                css(this.series.chart.container, { cursor: 'move' });\n            });\n            NetworkgraphPoint_addEvent(_this, 'mouseOut', function () {\n                css(this.series.chart.container, { cursor: 'default' });\n            });\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy point. If it's a node, remove all links coming out of this\n     * node. Then remove point from the layout.\n     * @private\n     */\n    NetworkgraphPoint.prototype.destroy = function () {\n        if (this.isNode) {\n            this.linksFrom.concat(this.linksTo).forEach(function (link) {\n                // Removing multiple nodes at the same time\n                // will try to remove link between nodes twice\n                if (link.destroyElements) {\n                    link.destroyElements();\n                }\n            });\n        }\n        this.series.layout.removeElementFromCollection(this, this.series.layout[this.isNode ? 'nodes' : 'links']);\n        return Point.prototype.destroy.apply(this, arguments);\n    };\n    /**\n     * Return degree of a node. If node has no connections, it still has\n     * deg=1.\n     * @private\n     */\n    NetworkgraphPoint.prototype.getDegree = function () {\n        var deg = this.isNode ?\n                this.linksFrom.length + this.linksTo.length :\n                0;\n        return deg === 0 ? 1 : deg;\n    };\n    /**\n     * Get presentational attributes of link connecting two nodes.\n     * @private\n     */\n    NetworkgraphPoint.prototype.getLinkAttributes = function () {\n        var linkOptions = this.series.options.link,\n            pointOptions = this.options;\n        return {\n            'stroke-width': NetworkgraphPoint_pick(pointOptions.width, linkOptions.width),\n            stroke: (pointOptions.color || linkOptions.color),\n            dashstyle: (pointOptions.dashStyle || linkOptions.dashStyle),\n            opacity: NetworkgraphPoint_pick(pointOptions.opacity, linkOptions.opacity, 1)\n        };\n    };\n    /**\n     * Get link path connecting two nodes.\n     * @private\n     * @return {Array<Highcharts.SVGPathArray>}\n     *         Path: `['M', x, y, 'L', x, y]`\n     */\n    NetworkgraphPoint.prototype.getLinkPath = function () {\n        var left = this.fromNode,\n            right = this.toNode;\n        // Start always from left to the right node, to prevent rendering\n        // labels upside down\n        if (left.plotX > right.plotX) {\n            left = this.toNode;\n            right = this.fromNode;\n        }\n        return [\n            ['M', left.plotX || 0, left.plotY || 0],\n            ['L', right.plotX || 0, right.plotY || 0]\n        ];\n        /*\n        IDEA: different link shapes?\n        return [\n            'M',\n            from.plotX,\n            from.plotY,\n            'Q',\n            (to.plotX + from.plotX) / 2,\n            (to.plotY + from.plotY) / 2 + 15,\n            to.plotX,\n            to.plotY\n        ];*/\n    };\n    /**\n     * Get mass fraction applied on two nodes connected to each other. By\n     * default, when mass is equal to `1`, mass fraction for both nodes\n     * equal to 0.5.\n     * @private\n     * @return {Highcharts.Dictionary<number>}\n     *         For example `{ fromNode: 0.5, toNode: 0.5 }`\n     */\n    NetworkgraphPoint.prototype.getMass = function () {\n        var m1 = this.fromNode.mass,\n            m2 = this.toNode.mass,\n            sum = m1 + m2;\n        return {\n            fromNode: 1 - m1 / sum,\n            toNode: 1 - m2 / sum\n        };\n    };\n    /**\n     * @private\n     */\n    NetworkgraphPoint.prototype.isValid = function () {\n        return !this.isNode || NetworkgraphPoint_defined(this.id);\n    };\n    /**\n     * Redraw link's path.\n     * @private\n     */\n    NetworkgraphPoint.prototype.redrawLink = function () {\n        var path = this.getLinkPath();\n        var attribs;\n        if (this.graphic) {\n            this.shapeArgs = {\n                d: path\n            };\n            if (!this.series.chart.styledMode) {\n                attribs = this.series.pointAttribs(this);\n                this.graphic.attr(attribs);\n                (this.dataLabels || []).forEach(function (label) {\n                    if (label) {\n                        label.attr({\n                            opacity: attribs.opacity\n                        });\n                    }\n                });\n            }\n            this.graphic.animate(this.shapeArgs);\n            // Required for dataLabels\n            var start = path[0];\n            var end = path[1];\n            if (start[0] === 'M' && end[0] === 'L') {\n                this.plotX = (start[1] + end[1]) / 2;\n                this.plotY = (start[2] + end[2]) / 2;\n            }\n        }\n    };\n    /**\n     * Common method for removing points and nodes in networkgraph. To\n     * remove `link`, use `series.data[index].remove()`. To remove `node`\n     * with all connections, use `series.nodes[index].remove()`.\n     * @private\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart or wait for an explicit call. When\n     *        doing more operations on the chart, for example running\n     *        `point.remove()` in a loop, it is best practice to set\n     *        `redraw` to false and call `chart.redraw()` after.\n     * @param {boolean|Partial<Highcharts.AnimationOptionsObject>} [animation=false]\n     *        Whether to apply animation, and optionally animation\n     *        configuration.\n     */\n    NetworkgraphPoint.prototype.remove = function (redraw, animation) {\n        var point = this,\n            series = point.series,\n            nodesOptions = series.options.nodes || [];\n        var index,\n            i = nodesOptions.length;\n        // For nodes, remove all connected links:\n        if (point.isNode) {\n            // Temporary disable series.points array, because\n            // Series.removePoint() modifies it\n            series.points = [];\n            // Remove link from all nodes collections:\n            []\n                .concat(point.linksFrom)\n                .concat(point.linksTo)\n                .forEach(function (linkFromTo) {\n                // Incoming links\n                index = linkFromTo.fromNode.linksFrom.indexOf(linkFromTo);\n                if (index > -1) {\n                    linkFromTo.fromNode.linksFrom.splice(index, 1);\n                }\n                // Outcoming links\n                index = linkFromTo.toNode.linksTo.indexOf(linkFromTo);\n                if (index > -1) {\n                    linkFromTo.toNode.linksTo.splice(index, 1);\n                }\n                // Remove link from data/points collections\n                NetworkgraphPoint_seriesProto.removePoint.call(series, series.data.indexOf(linkFromTo), false, false);\n            });\n            // Restore points array, after links are removed\n            series.points = series.data.slice();\n            // Proceed with removing node. It's similar to\n            // Series.removePoint() method, but doesn't modify other arrays\n            series.nodes.splice(series.nodes.indexOf(point), 1);\n            // Remove node options from config\n            while (i--) {\n                if (nodesOptions[i].id === point.options.id) {\n                    series.options.nodes.splice(i, 1);\n                    break;\n                }\n            }\n            if (point) {\n                point.destroy();\n            }\n            // Run redraw if requested\n            series.isDirty = true;\n            series.isDirtyData = true;\n            if (redraw) {\n                series.chart.redraw(redraw);\n            }\n        }\n        else {\n            series.removePoint(series.data.indexOf(point), redraw, animation);\n        }\n    };\n    /**\n     * Render link and add it to the DOM.\n     * @private\n     */\n    NetworkgraphPoint.prototype.renderLink = function () {\n        var attribs;\n        if (!this.graphic) {\n            this.graphic = this.series.chart.renderer\n                .path(this.getLinkPath())\n                .addClass(this.getClassName(), true)\n                .add(this.series.group);\n            if (!this.series.chart.styledMode) {\n                attribs = this.series.pointAttribs(this);\n                this.graphic.attr(attribs);\n                (this.dataLabels || []).forEach(function (label) {\n                    if (label) {\n                        label.attr({\n                            opacity: attribs.opacity\n                        });\n                    }\n                });\n            }\n        }\n    };\n    return NetworkgraphPoint;\n}(Point));\nNetworkgraphPoint_extend(NetworkgraphPoint.prototype, {\n    setState: Series_NodesComposition.setNodeState\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Networkgraph_NetworkgraphPoint = (NetworkgraphPoint);\n\n;// ./code/es5/es-modules/Series/Networkgraph/NetworkgraphSeriesDefaults.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * A networkgraph is a type of relationship chart, where connnections\n * (links) attracts nodes (points) and other nodes repulse each other.\n *\n * @extends      plotOptions.line\n * @product      highcharts\n * @sample       highcharts/demo/network-graph/\n *               Networkgraph\n * @since        7.0.0\n * @excluding    boostThreshold, animation, animationLimit, connectEnds,\n *               colorAxis, colorKey, connectNulls, cropThreshold, dragDrop,\n *               getExtremesFromAll, label, linecap, negativeColor,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointStart, softThreshold, stack, stacking, step,\n *               threshold, xAxis, yAxis, zoneAxis, dataSorting,\n *               boostBlending\n * @requires     modules/networkgraph\n * @optionparent plotOptions.networkgraph\n *\n * @private\n */\nvar NetworkgraphSeriesDefaults = {\n    stickyTracking: false,\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    marker: {\n        enabled: true,\n        states: {\n            /**\n             * The opposite state of a hover for a single point node.\n             * Applied to all not connected nodes to the hovered one.\n             *\n             * @declare Highcharts.PointStatesInactiveOptionsObject\n             */\n            inactive: {\n                /**\n                 * Opacity of inactive markers.\n                 */\n                opacity: 0.3,\n                /**\n                 * Animation when not hovering over the node.\n                 *\n                 * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n                 */\n                animation: {\n                    /** @internal */\n                    duration: 50\n                }\n            }\n        }\n    },\n    states: {\n        /**\n         * The opposite state of a hover for a single point link. Applied\n         * to all links that are not coming from the hovered node.\n         *\n         * @declare Highcharts.SeriesStatesInactiveOptionsObject\n         */\n        inactive: {\n            /**\n             * Opacity of inactive links.\n             */\n            linkOpacity: 0.3,\n            /**\n             * Animation when not hovering over the node.\n             *\n             * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n             */\n            animation: {\n                /** @internal */\n                duration: 50\n            }\n        }\n    },\n    /**\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Networkgraph with labels on links\n     * @sample highcharts/series-networkgraph/textpath-datalabels\n     *         Networkgraph with labels around nodes\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Data labels moved into the nodes\n     * @sample highcharts/series-networkgraph/link-datalabels\n     *         Data labels moved under the links\n     *\n     * @declare Highcharts.SeriesNetworkgraphDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _node_ in the networkgraph. In v7.0\n         * defaults to `{key}`, since v7.1 defaults to `undefined` and\n         * `formatter` is used instead.\n         *\n         * @type      {string}\n         * @since     7.0.0\n         * @apioption plotOptions.networkgraph.dataLabels.format\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback JavaScript function to format the data label for a node.\n         * Note that if a `format` is defined, the format takes precedence\n         * and the formatter is ignored.\n         *\n         * @since 7.0.0\n         */\n        formatter: function () {\n            var _a;\n            return String((_a = this.key) !== null && _a !== void 0 ? _a : '');\n        },\n        /**\n         * The\n         * [format string](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * specifying what to show for _links_ in the networkgraph.\n         * (Default: `undefined`)\n         *\n         * @type      {string}\n         * @since     7.1.0\n         * @apioption plotOptions.networkgraph.dataLabels.linkFormat\n         */\n        // eslint-disable-next-line valid-jsdoc\n        /**\n         * Callback to format data labels for _links_ in the sankey diagram.\n         * The `linkFormat` option takes precedence over the\n         * `linkFormatter`.\n         *\n         * @since 7.1.0\n         */\n        linkFormatter: function () {\n            return (this.fromNode.name +\n                '<br>' +\n                this.toNode.name);\n        },\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label that\n         * follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option. Setting\n         * `useHTML` to true will disable this option.\n         *\n         * @extends plotOptions.networkgraph.dataLabels.textPath\n         * @since   7.1.0\n         */\n        linkTextPath: {\n            enabled: true\n        },\n        textPath: {\n            enabled: false\n        },\n        style: {\n            transition: 'opacity 2000ms'\n        },\n        defer: true,\n        animation: {\n            defer: 1000\n        }\n    },\n    /**\n     * Link style options\n     * @private\n     */\n    link: {\n        /**\n         * A name for the dash style to use for links.\n         *\n         * @type      {string}\n         * @apioption plotOptions.networkgraph.link.dashStyle\n         */\n        /**\n         * Opacity of the link between two nodes.\n         *\n         * @type      {number}\n         * @default   1\n         * @apioption plotOptions.networkgraph.link.opacity\n         */\n        /**\n         * Color of the link between two nodes.\n         */\n        color: 'rgba(100, 100, 100, 0.5)',\n        /**\n         * Width (px) of the link between two nodes.\n         */\n        width: 1\n    },\n    /**\n     * Flag to determine if nodes are draggable or not.\n     * @private\n     */\n    draggable: true,\n    layoutAlgorithm: {\n        /**\n         * Repulsive force applied on a node. Passed are two arguments:\n         * - `d` - which is current distance between two nodes\n         * - `k` - which is desired distance between two nodes\n         *\n         * In `verlet` integration, defaults to:\n         * `function (d, k) { return (k - d) / d * (k > d ? 1 : 0) }`\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         * @sample highcharts/series-networkgraph/cuboids/\n         *         Custom forces with Verlet integration\n         *\n         * @type      {Function}\n         * @default   function (d, k) { return k * k / d; }\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.repulsiveForce\n         */\n        /**\n         * Attraction force applied on a node which is conected to another\n         * node by a link. Passed are two arguments:\n         * - `d` - which is current distance between two nodes\n         * - `k` - which is desired distance between two nodes\n         *\n         * In `verlet` integration, defaults to:\n         * `function (d, k) { return (k - d) / d; }`\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         * @sample highcharts/series-networkgraph/cuboids/\n         *         Custom forces with Verlet integration\n         *\n         * @type      {Function}\n         * @default   function (d, k) { return k * k / d; }\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.attractiveForce\n         */\n        /**\n         * Ideal length (px) of the link between two nodes. When not\n         * defined, length is calculated as:\n         * `Math.pow(availableWidth * availableHeight / nodesLength, 0.4);`\n         *\n         * Note: Because of the algorithm specification, length of each link\n         * might be not exactly as specified.\n         *\n         * @sample highcharts/series-networkgraph/styled-links/\n         *         Numerical values\n         *\n         * @type      {number}\n         * @apioption plotOptions.networkgraph.layoutAlgorithm.linkLength\n         */\n        /**\n         * Initial layout algorithm for positioning nodes. Can be one of\n         * built-in options (\"circle\", \"random\") or a function where\n         * positions should be set on each node (`this.nodes`) as\n         * `node.plotX` and `node.plotY`\n         *\n         * @sample highcharts/series-networkgraph/initial-positions/\n         *         Initial positions with callback\n         *\n         * @type {\"circle\"|\"random\"|Function}\n         */\n        initialPositions: 'circle',\n        /**\n         * When `initialPositions` are set to 'circle',\n         * `initialPositionRadius` is a distance from the center of circle,\n         * in which nodes are created.\n         *\n         * @type    {number}\n         * @default 1\n         * @since   7.1.0\n         */\n        initialPositionRadius: 1,\n        /**\n         * Experimental. Enables live simulation of the algorithm\n         * implementation. All nodes are animated as the forces applies on\n         * them.\n         *\n         * @sample highcharts/demo/network-graph/\n         *         Live simulation enabled\n         */\n        enableSimulation: false,\n        /**\n         * Barnes-Hut approximation only.\n         * Deteremines when distance between cell and node is small enough\n         * to calculate forces. Value of `theta` is compared directly with\n         * quotient `s / d`, where `s` is the size of the cell, and `d` is\n         * distance between center of cell's mass and currently compared\n         * node.\n         *\n         * @see [layoutAlgorithm.approximation](#series.networkgraph.layoutAlgorithm.approximation)\n         *\n         * @since 7.1.0\n         */\n        theta: 0.5,\n        /**\n         * Verlet integration only.\n         * Max speed that node can get in one iteration. In terms of\n         * simulation, it's a maximum translation (in pixels) that node can\n         * move (in both, x and y, dimensions). While `friction` is applied\n         * on all nodes, max speed is applied only for nodes that move very\n         * fast, for example small or disconnected ones.\n         *\n         * @see [layoutAlgorithm.integration](#series.networkgraph.layoutAlgorithm.integration)\n         * @see [layoutAlgorithm.friction](#series.networkgraph.layoutAlgorithm.friction)\n         *\n         * @since 7.1.0\n         */\n        maxSpeed: 10,\n        /**\n         * Approximation used to calculate repulsive forces affecting nodes.\n         * By default, when calculating net force, nodes are compared\n         * against each other, which gives O(N^2) complexity. Using\n         * Barnes-Hut approximation, we decrease this to O(N log N), but the\n         * resulting graph will have different layout. Barnes-Hut\n         * approximation divides space into rectangles via quad tree, where\n         * forces exerted on nodes are calculated directly for nearby cells,\n         * and for all others, cells are treated as a separate node with\n         * center of mass.\n         *\n         * @see [layoutAlgorithm.theta](#series.networkgraph.layoutAlgorithm.theta)\n         *\n         * @sample highcharts/series-networkgraph/barnes-hut-approximation/\n         *         A graph with Barnes-Hut approximation\n         *\n         * @type       {string}\n         * @validvalue [\"barnes-hut\", \"none\"]\n         * @since      7.1.0\n         */\n        approximation: 'none',\n        /**\n         * Type of the algorithm used when positioning nodes.\n         *\n         * @type       {string}\n         * @validvalue [\"reingold-fruchterman\"]\n         */\n        type: 'reingold-fruchterman',\n        /**\n         * Integration type. Available options are `'euler'` and `'verlet'`.\n         * Integration determines how forces are applied on particles. In\n         * Euler integration, force is applied direct as\n         * `newPosition += velocity;`.\n         * In Verlet integration, new position is based on a previous\n         * position without velocity:\n         * `newPosition += previousPosition - newPosition`.\n         *\n         * Note that different integrations give different results as forces\n         * are different.\n         *\n         * In Highcharts v7.0.x only `'euler'` integration was supported.\n         *\n         * @sample highcharts/series-networkgraph/integration-comparison/\n         *         Comparison of Verlet and Euler integrations\n         *\n         * @type       {string}\n         * @validvalue [\"euler\", \"verlet\"]\n         * @since      7.1.0\n         */\n        integration: 'euler',\n        /**\n         * Max number of iterations before algorithm will stop. In general,\n         * algorithm should find positions sooner, but when rendering huge\n         * number of nodes, it is recommended to increase this value as\n         * finding perfect graph positions can require more time.\n         */\n        maxIterations: 1000,\n        /**\n         * Gravitational const used in the barycenter force of the\n         * algorithm.\n         *\n         * @sample highcharts/series-networkgraph/forces/\n         *         Custom forces with Euler integration\n         */\n        gravitationalConstant: 0.0625,\n        /**\n         * Friction applied on forces to prevent nodes rushing to fast to\n         * the desired positions.\n         */\n        friction: -0.981\n    },\n    showInLegend: false\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Networkgraph_NetworkgraphSeriesDefaults = (NetworkgraphSeriesDefaults);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Fires after the simulation is ended and the layout is stable.\n *\n * @type      {Highcharts.NetworkgraphAfterSimulationCallbackFunction}\n * @product   highcharts\n * @apioption series.networkgraph.events.afterSimulation\n */\n/**\n * A `networkgraph` series. If the [type](#series.networkgraph.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.networkgraph\n * @excluding boostThreshold, animation, animationLimit, connectEnds,\n *            connectNulls, cropThreshold, dragDrop, getExtremesFromAll, label,\n *            linecap, negativeColor, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointStart, softThreshold, stack, stacking,\n *            step, threshold, xAxis, yAxis, zoneAxis, dataSorting,\n *            boostBlending\n * @product   highcharts\n * @requires  modules/networkgraph\n * @apioption series.networkgraph\n */\n/**\n * An array of data points for the series. For the `networkgraph` series type,\n * points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of\n * data points exceeds the series'\n * [turboThreshold](#series.area.turboThreshold), this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2'\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3'\n *     }]\n *  ```\n *\n * @type      {Array<Object|Array|number>}\n * @extends   series.line.data\n * @excluding drilldown,marker,x,y,dragDrop\n * @sample    {highcharts} highcharts/chart/reflow-true/\n *            Numerical values\n * @sample    {highcharts} highcharts/series/data-array-of-arrays/\n *            Arrays of numeric x and y\n * @sample    {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *            Arrays of datetime x and y\n * @sample    {highcharts} highcharts/series/data-array-of-name-value/\n *            Arrays of point.name and y\n * @sample    {highcharts} highcharts/series/data-array-of-objects/\n *            Config objects\n * @product   highcharts\n * @apioption series.networkgraph.data\n */\n/**\n * @type      {Highcharts.SeriesNetworkgraphDataLabelsOptionsObject|Array<Highcharts.SeriesNetworkgraphDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.networkgraph.data.dataLabels\n */\n/**\n * The node that the link runs from.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.data.from\n */\n/**\n * The node that the link runs to.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.data.to\n */\n/**\n * A collection of options for the individual nodes. The nodes in a\n * networkgraph diagram are auto-generated instances of `Highcharts.Point`,\n * but options can be applied here and linked by the `id`.\n *\n * @sample highcharts/series-networkgraph/data-options/\n *         Networkgraph diagram with node options\n *\n * @type      {Array<*>}\n * @product   highcharts\n * @apioption series.networkgraph.nodes\n */\n/**\n * The id of the auto-generated node, referring to the `from` or `to` setting of\n * the link.\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.id\n */\n/**\n * The color of the auto generated node.\n *\n * @type      {Highcharts.ColorString}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.color\n */\n/**\n * The color index of the auto generated node, especially for use in styled\n * mode.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.colorIndex\n */\n/**\n * The name to display for the node in data labels and tooltips. Use this when\n * the name is different from the `id`. Where the id must be unique for each\n * node, this is not necessary for the name.\n *\n * @sample highcharts/series-networkgraph/data-options/\n *         Networkgraph diagram with node options\n *\n * @type      {string}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.name\n */\n/**\n * Mass of the node. By default, each node has mass equal to it's marker radius\n * . Mass is used to determine how two connected nodes should affect\n * each other:\n *\n * Attractive force is multiplied by the ratio of two connected\n * nodes; if a big node has weights twice as the small one, then the small one\n * will move towards the big one twice faster than the big one to the small one\n * .\n *\n * @sample highcharts/series-networkgraph/ragdoll/\n *         Mass determined by marker.radius\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.networkgraph.nodes.mass\n */\n/**\n * Options for the node markers.\n *\n * @extends   plotOptions.networkgraph.marker\n * @apioption series.networkgraph.nodes.marker\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.networkgraph.dataLabels](#series.networkgraph.dataLabels).\n *\n * @type      {Highcharts.SeriesNetworkgraphDataLabelsOptionsObject|Array<Highcharts.SeriesNetworkgraphDataLabelsOptionsObject>}\n *\n * @apioption series.networkgraph.nodes.dataLabels\n */\n''; // Adds doclets above to transpiled file\n\n;// ./code/es5/es-modules/Series/Networkgraph/EulerIntegration.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Attractive force.\n *\n * In Euler integration, force is stored in a node, not changing it's\n * position. Later, in `integrate()` forces are applied on nodes.\n *\n * @private\n * @param {Highcharts.Point} link\n *        Link that connects two nodes\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n * @param {number} distanceR\n     */\nfunction attractive(link, force, distanceXY, distanceR) {\n    var massFactor = link.getMass(), translatedX = (distanceXY.x / distanceR) * force, translatedY = (distanceXY.y / distanceR) * force;\n    if (!link.fromNode.fixedPosition) {\n        link.fromNode.dispX -=\n            translatedX * massFactor.fromNode / link.fromNode.degree;\n        link.fromNode.dispY -=\n            translatedY * massFactor.fromNode / link.fromNode.degree;\n    }\n    if (!link.toNode.fixedPosition) {\n        link.toNode.dispX +=\n            translatedX * massFactor.toNode / link.toNode.degree;\n        link.toNode.dispY +=\n            translatedY * massFactor.toNode / link.toNode.degree;\n    }\n}\n/**\n * Attractive force function. Can be replaced by API's\n * `layoutAlgorithm.attractiveForce`\n *\n * Other forces that can be used:\n *\n * basic, not recommended:\n *    `function (d, k) { return d / k }`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction attractiveForceFunction(d, k) {\n    return d * d / k;\n}\n/**\n * Barycenter force. Calculate and applys barycenter forces on the\n * nodes. Making them closer to the center of their barycenter point.\n *\n * In Euler integration, force is stored in a node, not changing it's\n * position. Later, in `integrate()` forces are applied on nodes.\n *\n * @private\n */\nfunction barycenter() {\n    var gravitationalConstant = this.options.gravitationalConstant,\n        xFactor = this.barycenter.xFactor,\n        yFactor = this.barycenter.yFactor;\n    this.nodes.forEach(function (node) {\n        if (!node.fixedPosition) {\n            var degree = node.getDegree(),\n                phi = degree * (1 + degree / 2);\n            node.dispX += ((xFactor - node.plotX) *\n                gravitationalConstant *\n                phi / node.degree);\n            node.dispY += ((yFactor - node.plotY) *\n                gravitationalConstant *\n                phi / node.degree);\n        }\n    });\n}\n/**\n * Estimate the best possible distance between two nodes, making graph\n * readable.\n * @private\n */\nfunction getK(layout) {\n    return Math.pow(layout.box.width * layout.box.height / layout.nodes.length, 0.3);\n}\n/**\n * Integration method.\n *\n * In Euler integration, force were stored in a node, not changing it's\n * position. Now, in the integrator method, we apply changes.\n *\n * Euler:\n *\n * Basic form: `x(n+1) = x(n) + v(n)`\n *\n * With Rengoild-Fruchterman we get:\n * `x(n+1) = x(n) + v(n) / length(v(n)) * min(v(n), temperature(n))`\n * where:\n * - `x(n+1)`: next position\n * - `x(n)`: current position\n * - `v(n)`: velocity (comes from net force)\n * - `temperature(n)`: current temperature\n *\n * Known issues:\n * Oscillations when force vector has the same magnitude but opposite\n * direction in the next step. Potentially solved by decreasing force by\n * `v * (1 / node.degree)`\n *\n * Note:\n * Actually `min(v(n), temperature(n))` replaces simulated annealing.\n *\n * @private\n * @param {Highcharts.NetworkgraphLayout} layout\n *        Layout object\n * @param {Highcharts.Point} node\n *        Node that should be translated\n */\nfunction integrate(layout, node) {\n    node.dispX +=\n        node.dispX * layout.options.friction;\n    node.dispY +=\n        node.dispY * layout.options.friction;\n    var distanceR = node.temperature = layout.vectorLength({\n            x: node.dispX,\n            y: node.dispY\n        });\n    if (distanceR !== 0) {\n        node.plotX += (node.dispX / distanceR *\n            Math.min(Math.abs(node.dispX), layout.temperature));\n        node.plotY += (node.dispY / distanceR *\n            Math.min(Math.abs(node.dispY), layout.temperature));\n    }\n}\n/**\n * Repulsive force.\n *\n * @private\n * @param {Highcharts.Point} node\n *        Node that should be translated by force.\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction repulsive(node, force, distanceXY, distanceR) {\n    node.dispX +=\n        (distanceXY.x / distanceR) * force / node.degree;\n    node.dispY +=\n        (distanceXY.y / distanceR) * force / node.degree;\n}\n/**\n * Repulsive force function. Can be replaced by API's\n * `layoutAlgorithm.repulsiveForce`.\n *\n * Other forces that can be used:\n *\n * basic, not recommended:\n *    `function (d, k) { return k / d }`\n *\n * standard:\n *    `function (d, k) { return k * k / d }`\n *\n * grid-variant:\n *    `function (d, k) { return k * k / d * (2 * k - d > 0 ? 1 : 0) }`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction repulsiveForceFunction(d, k) {\n    return k * k / d;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar EulerIntegration = {\n    attractive: attractive,\n    attractiveForceFunction: attractiveForceFunction,\n    barycenter: barycenter,\n    getK: getK,\n    integrate: integrate,\n    repulsive: repulsive,\n    repulsiveForceFunction: repulsiveForceFunction\n};\n/* harmony default export */ var Networkgraph_EulerIntegration = (EulerIntegration);\n\n;// ./code/es5/es-modules/Series/Networkgraph/QuadTreeNode.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The QuadTree node class. Used in Networkgraph chart as a base for Barnes-Hut\n * approximation.\n *\n * @private\n * @class\n * @name Highcharts.QuadTreeNode\n *\n * @param {Highcharts.Dictionary<number>} box\n *        Available space for the node\n */\nvar QuadTreeNode = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function QuadTreeNode(box) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        /**\n         * Read only. If QuadTreeNode is an external node, Point is stored in\n         * `this.body`.\n         *\n         * @name Highcharts.QuadTreeNode#body\n         * @type {boolean|Highcharts.Point}\n         */\n        this.body = false;\n        /**\n         * Read only. Internal nodes when created are empty to reserve the\n         * space. If Point is added to this QuadTreeNode, QuadTreeNode is no\n         * longer empty.\n         *\n         * @name Highcharts.QuadTreeNode#isEmpty\n         * @type {boolean}\n         */\n        this.isEmpty = false;\n        /**\n         * Read only. Flag to determine if QuadTreeNode is internal (and has\n         * subnodes with mass and central position) or external (bound to\n         * Point).\n         *\n         * @name Highcharts.QuadTreeNode#isInternal\n         * @type {boolean}\n         */\n        this.isInternal = false;\n        /**\n         * Read only. Array of subnodes. Empty if QuadTreeNode has just one\n         * Point. When added another Point to this QuadTreeNode, array is\n         * filled with four subnodes.\n         *\n         * @name Highcharts.QuadTreeNode#nodes\n         * @type {Array<Highcharts.QuadTreeNode>}\n         */\n        this.nodes = [];\n        /**\n         * Read only. The available space for node.\n         *\n         * @name Highcharts.QuadTreeNode#box\n         * @type {Highcharts.Dictionary<number>}\n         */\n        this.box = box;\n        /**\n         * Read only. The minium of width and height values.\n         *\n         * @name Highcharts.QuadTreeNode#boxSize\n         * @type {number}\n         */\n        this.boxSize = Math.min(box.width, box.height);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * When inserting another node into the box, that already hove one node,\n     * divide the available space into another four quadrants.\n     *\n     * Indexes of quadrants are:\n     * ```\n     * -------------               -------------\n     * |           |               |     |     |\n     * |           |               |  0  |  1  |\n     * |           |   divide()    |     |     |\n     * |     1     | ----------->  -------------\n     * |           |               |     |     |\n     * |           |               |  3  |  2  |\n     * |           |               |     |     |\n     * -------------               -------------\n     * ```\n     */\n    QuadTreeNode.prototype.divideBox = function () {\n        var halfWidth = this.box.width / 2, halfHeight = this.box.height / 2;\n        // Top left\n        this.nodes[0] = new QuadTreeNode({\n            left: this.box.left,\n            top: this.box.top,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Top right\n        this.nodes[1] = new QuadTreeNode({\n            left: this.box.left + halfWidth,\n            top: this.box.top,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Bottom right\n        this.nodes[2] = new QuadTreeNode({\n            left: this.box.left + halfWidth,\n            top: this.box.top + halfHeight,\n            width: halfWidth,\n            height: halfHeight\n        });\n        // Bottom left\n        this.nodes[3] = new QuadTreeNode({\n            left: this.box.left,\n            top: this.box.top + halfHeight,\n            width: halfWidth,\n            height: halfHeight\n        });\n    };\n    /**\n     * Determine which of the quadrants should be used when placing node in\n     * the QuadTree. Returned index is always in range `< 0 , 3 >`.\n     * @private\n     */\n    QuadTreeNode.prototype.getBoxPosition = function (point) {\n        var left = point.plotX < this.box.left + this.box.width / 2, top = point.plotY < this.box.top + this.box.height / 2;\n        var index;\n        if (left) {\n            if (top) {\n                // Top left\n                index = 0;\n            }\n            else {\n                // Bottom left\n                index = 3;\n            }\n        }\n        else {\n            if (top) {\n                // Top right\n                index = 1;\n            }\n            else {\n                // Bottom right\n                index = 2;\n            }\n        }\n        return index;\n    };\n    /**\n     * Insert recursively point(node) into the QuadTree. If the given\n     * quadrant is already occupied, divide it into smaller quadrants.\n     *\n     * @param {Highcharts.Point} point\n     *        Point/node to be inserted\n     * @param {number} depth\n     *        Max depth of the QuadTree\n     */\n    QuadTreeNode.prototype.insert = function (point, depth) {\n        var newQuadTreeNode;\n        if (this.isInternal) {\n            // Internal node:\n            this.nodes[this.getBoxPosition(point)].insert(point, depth - 1);\n        }\n        else {\n            this.isEmpty = false;\n            if (!this.body) {\n                // First body in a quadrant:\n                this.isInternal = false;\n                this.body = point;\n            }\n            else {\n                if (depth) {\n                    // Every other body in a quadrant:\n                    this.isInternal = true;\n                    this.divideBox();\n                    // Reinsert main body only once:\n                    if (this.body !== true) {\n                        this.nodes[this.getBoxPosition(this.body)]\n                            .insert(this.body, depth - 1);\n                        this.body = true;\n                    }\n                    // Add second body:\n                    this.nodes[this.getBoxPosition(point)]\n                        .insert(point, depth - 1);\n                }\n                else {\n                    // We are below max allowed depth. That means either:\n                    // - really huge number of points\n                    // - falling two points into exactly the same position\n                    // In this case, create another node in the QuadTree.\n                    //\n                    // Alternatively we could add some noise to the\n                    // position, but that could result in different\n                    // rendered chart in exporting.\n                    newQuadTreeNode = new QuadTreeNode({\n                        top: point.plotX || NaN,\n                        left: point.plotY || NaN,\n                        // Width/height below 1px\n                        width: 0.1,\n                        height: 0.1\n                    });\n                    newQuadTreeNode.body = point;\n                    newQuadTreeNode.isInternal = false;\n                    this.nodes.push(newQuadTreeNode);\n                }\n            }\n        }\n    };\n    /**\n     * Each quad node requires it's mass and center position. That mass and\n     * position is used to imitate real node in the layout by approximation.\n     */\n    QuadTreeNode.prototype.updateMassAndCenter = function () {\n        var mass = 0,\n            plotX = 0,\n            plotY = 0;\n        if (this.isInternal) {\n            // Calculate weightened mass of the quad node:\n            for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {\n                var pointMass = _a[_i];\n                if (!pointMass.isEmpty) {\n                    mass += pointMass.mass;\n                    plotX += pointMass.plotX * pointMass.mass;\n                    plotY += pointMass.plotY * pointMass.mass;\n                }\n            }\n            plotX /= mass;\n            plotY /= mass;\n        }\n        else if (this.body) {\n            // Just one node, use coordinates directly:\n            mass = this.body.mass;\n            plotX = this.body.plotX;\n            plotY = this.body.plotY;\n        }\n        // Store details:\n        this.mass = mass;\n        this.plotX = plotX;\n        this.plotY = plotY;\n    };\n    return QuadTreeNode;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Networkgraph_QuadTreeNode = (QuadTreeNode);\n\n;// ./code/es5/es-modules/Series/Networkgraph/QuadTree.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * The QuadTree class. Used in Networkgraph chart as a base for Barnes-Hut\n * approximation.\n *\n * @private\n * @class\n * @name Highcharts.QuadTree\n *\n * @param {number} x\n *        Left position of the plotting area\n * @param {number} y\n *        Top position of the plotting area\n * @param {number} width\n *        Width of the plotting area\n * @param {number} height\n *        Height of the plotting area\n */\nvar QuadTree = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function QuadTree(x, y, width, height) {\n        // Boundary rectangle:\n        this.box = {\n            left: x,\n            top: y,\n            width: width,\n            height: height\n        };\n        this.maxDepth = 25;\n        this.root = new Networkgraph_QuadTreeNode(this.box);\n        this.root.isInternal = true;\n        this.root.isRoot = true;\n        this.root.divideBox();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculate mass of the each QuadNode in the tree.\n     */\n    QuadTree.prototype.calculateMassAndCenter = function () {\n        this.visitNodeRecursive(null, null, function (node) {\n            node.updateMassAndCenter();\n        });\n    };\n    /**\n     * Insert nodes into the QuadTree\n     *\n     * @param {Array<Highcharts.Point>} points\n     *        Points as nodes\n     */\n    QuadTree.prototype.insertNodes = function (points) {\n        for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n            var point = points_1[_i];\n            this.root.insert(point, this.maxDepth);\n        }\n    };\n    /**\n     * Depth first treversal (DFS). Using `before` and `after` callbacks,\n     * we can get two results: preorder and postorder traversals, reminder:\n     *\n     * ```\n     *     (a)\n     *     / \\\n     *   (b) (c)\n     *   / \\\n     * (d) (e)\n     * ```\n     *\n     * DFS (preorder): `a -> b -> d -> e -> c`\n     *\n     * DFS (postorder): `d -> e -> b -> c -> a`\n     *\n     * @param {Highcharts.QuadTreeNode|null} node\n     *        QuadTree node\n     * @param {Function} [beforeCallback]\n     *        Function to be called before visiting children nodes.\n     * @param {Function} [afterCallback]\n     *        Function to be called after visiting children nodes.\n     */\n    QuadTree.prototype.visitNodeRecursive = function (node, beforeCallback, afterCallback) {\n        var goFurther;\n        if (!node) {\n            node = this.root;\n        }\n        if (node === this.root && beforeCallback) {\n            goFurther = beforeCallback(node);\n        }\n        if (goFurther === false) {\n            return;\n        }\n        for (var _i = 0, _a = node.nodes; _i < _a.length; _i++) {\n            var qtNode = _a[_i];\n            if (qtNode.isInternal) {\n                if (beforeCallback) {\n                    goFurther = beforeCallback(qtNode);\n                }\n                if (goFurther === false) {\n                    continue;\n                }\n                this.visitNodeRecursive(qtNode, beforeCallback, afterCallback);\n            }\n            else if (qtNode.body) {\n                if (beforeCallback) {\n                    beforeCallback(qtNode.body);\n                }\n            }\n            if (afterCallback) {\n                afterCallback(qtNode);\n            }\n        }\n        if (node === this.root && afterCallback) {\n            afterCallback(node);\n        }\n    };\n    return QuadTree;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Networkgraph_QuadTree = (QuadTree);\n\n;// ./code/es5/es-modules/Series/Networkgraph/VerletIntegration.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Attractive force.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n * @param {Highcharts.Point} link\n *        Link that connects two nodes\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction VerletIntegration_attractive(link, force, distanceXY) {\n    var massFactor = link.getMass(),\n        translatedX = -distanceXY.x * force * this.diffTemperature,\n        translatedY = -distanceXY.y * force * this.diffTemperature;\n    if (!link.fromNode.fixedPosition) {\n        link.fromNode.plotX -=\n            translatedX * massFactor.fromNode / link.fromNode.degree;\n        link.fromNode.plotY -=\n            translatedY * massFactor.fromNode / link.fromNode.degree;\n    }\n    if (!link.toNode.fixedPosition) {\n        link.toNode.plotX +=\n            translatedX * massFactor.toNode / link.toNode.degree;\n        link.toNode.plotY +=\n            translatedY * massFactor.toNode / link.toNode.degree;\n    }\n}\n/**\n * Attractive force function. Can be replaced by API's\n * `layoutAlgorithm.attractiveForce`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction VerletIntegration_attractiveForceFunction(d, k) {\n    // Used in API:\n    return (k - d) / d;\n}\n/**\n * Barycenter force. Calculate and applys barycenter forces on the\n * nodes. Making them closer to the center of their barycenter point.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n */\nfunction VerletIntegration_barycenter() {\n    var gravitationalConstant = this.options.gravitationalConstant || 0,\n        xFactor = (this.barycenter.xFactor -\n            (this.box.left + this.box.width) / 2) * gravitationalConstant,\n        yFactor = (this.barycenter.yFactor -\n            (this.box.top + this.box.height) / 2) * gravitationalConstant;\n    this.nodes.forEach(function (node) {\n        if (!node.fixedPosition) {\n            node.plotX -=\n                xFactor / node.mass / node.degree;\n            node.plotY -=\n                yFactor / node.mass / node.degree;\n        }\n    });\n}\n/**\n * Estiamte the best possible distance between two nodes, making graph\n * readable.\n * @private\n */\nfunction VerletIntegration_getK(layout) {\n    return Math.pow(layout.box.width * layout.box.height / layout.nodes.length, 0.5);\n}\n/**\n * Integration method.\n *\n * In Verlet integration, forces are applied on node immediately to it's\n * `plotX` and `plotY` position.\n *\n * Verlet without velocity:\n *\n *    x(n+1) = 2 * x(n) - x(n-1) + A(T) * deltaT ^ 2\n *\n * where:\n *     - x(n+1) - new position\n *     - x(n) - current position\n *     - x(n-1) - previous position\n *\n * Assuming A(t) = 0 (no acceleration) and (deltaT = 1) we get:\n *\n *     x(n+1) = x(n) + (x(n) - x(n-1))\n *\n * where:\n *     - (x(n) - x(n-1)) - position change\n *\n * TO DO:\n * Consider Verlet with velocity to support additional\n * forces. Or even Time-Corrected Verlet by Jonathan\n * \"lonesock\" Dummer\n *\n * @private\n * @param {Highcharts.NetworkgraphLayout} layout layout object\n * @param {Highcharts.Point} node node that should be translated\n */\nfunction VerletIntegration_integrate(layout, node) {\n    var friction = -layout.options.friction, maxSpeed = layout.options.maxSpeed, prevX = node.prevX, prevY = node.prevY, \n        // Apply friction:\n        frictionX = ((node.plotX + node.dispX -\n            prevX) * friction), frictionY = ((node.plotY + node.dispY -\n            prevY) * friction), abs = Math.abs, signX = abs(frictionX) / (frictionX || 1), // Need to deal with 0\n        signY = abs(frictionY) / (frictionY || 1), \n        // Apply max speed:\n        diffX = signX * Math.min(maxSpeed, Math.abs(frictionX)), diffY = signY * Math.min(maxSpeed, Math.abs(frictionY));\n    // Store for the next iteration:\n    node.prevX = node.plotX + node.dispX;\n    node.prevY = node.plotY + node.dispY;\n    // Update positions:\n    node.plotX += diffX;\n    node.plotY += diffY;\n    node.temperature = layout.vectorLength({\n        x: diffX,\n        y: diffY\n    });\n}\n/**\n * Repulsive force.\n *\n * In Verlet integration, force is applied on a node immediately to it's\n * `plotX` and `plotY` position.\n *\n * @private\n * @param {Highcharts.Point} node\n *        Node that should be translated by force.\n * @param {number} force\n *        Force calculated in `repulsiveForceFunction`\n * @param {Highcharts.PositionObject} distanceXY\n *        Distance between two nodes e.g. `{x, y}`\n */\nfunction VerletIntegration_repulsive(node, force, distanceXY) {\n    var factor = force * this.diffTemperature / node.mass / node.degree;\n    if (!node.fixedPosition) {\n        node.plotX += distanceXY.x * factor;\n        node.plotY += distanceXY.y * factor;\n    }\n}\n/**\n * Repulsive force function. Can be replaced by API's\n * `layoutAlgorithm.repulsiveForce`\n *\n * @private\n * @param {number} d current distance between two nodes\n * @param {number} k expected distance between two nodes\n * @return {number} force\n */\nfunction VerletIntegration_repulsiveForceFunction(d, k) {\n    // Used in API:\n    return (k - d) / d * (k > d ? 1 : 0); // Force only for close nodes\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar VerletIntegration = {\n    attractive: VerletIntegration_attractive,\n    attractiveForceFunction: VerletIntegration_attractiveForceFunction,\n    barycenter: VerletIntegration_barycenter,\n    getK: VerletIntegration_getK,\n    integrate: VerletIntegration_integrate,\n    repulsive: VerletIntegration_repulsive,\n    repulsiveForceFunction: VerletIntegration_repulsiveForceFunction\n};\n/* harmony default export */ var Networkgraph_VerletIntegration = (VerletIntegration);\n\n;// ./code/es5/es-modules/Series/Networkgraph/ReingoldFruchtermanLayout.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\n\nvar clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, ReingoldFruchtermanLayout_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, isFunction = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFunction, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, ReingoldFruchtermanLayout_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Reingold-Fruchterman algorithm from\n * \"Graph Drawing by Force-directed Placement\" paper.\n * @private\n */\nvar ReingoldFruchtermanLayout = /** @class */ (function () {\n    function ReingoldFruchtermanLayout() {\n        /* *\n         *\n         *  Static Functions\n         *\n         * */\n        this.box = {};\n        this.currentStep = 0;\n        this.initialRendering = true;\n        this.links = [];\n        this.nodes = [];\n        this.series = [];\n        this.simulation = false;\n    }\n    ReingoldFruchtermanLayout.compose = function (ChartClass) {\n        Series_GraphLayoutComposition.compose(ChartClass);\n        Series_GraphLayoutComposition.integrations.euler = Networkgraph_EulerIntegration;\n        Series_GraphLayoutComposition.integrations.verlet = Networkgraph_VerletIntegration;\n        Series_GraphLayoutComposition.layouts['reingold-fruchterman'] =\n            ReingoldFruchtermanLayout;\n    };\n    ReingoldFruchtermanLayout.prototype.init = function (options) {\n        this.options = options;\n        this.nodes = [];\n        this.links = [];\n        this.series = [];\n        this.box = {\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0\n        };\n        this.setInitialRendering(true);\n        this.integration =\n            Series_GraphLayoutComposition.integrations[options.integration];\n        this.enableSimulation = options.enableSimulation;\n        this.attractiveForce = ReingoldFruchtermanLayout_pick(options.attractiveForce, this.integration.attractiveForceFunction);\n        this.repulsiveForce = ReingoldFruchtermanLayout_pick(options.repulsiveForce, this.integration.repulsiveForceFunction);\n        this.approximation = options.approximation;\n    };\n    ReingoldFruchtermanLayout.prototype.updateSimulation = function (enable) {\n        this.enableSimulation = ReingoldFruchtermanLayout_pick(enable, this.options.enableSimulation);\n    };\n    ReingoldFruchtermanLayout.prototype.start = function () {\n        var layout = this,\n            series = this.series,\n            options = this.options;\n        layout.currentStep = 0;\n        layout.forces = series[0] && series[0].forces || [];\n        layout.chart = series[0] && series[0].chart;\n        if (layout.initialRendering) {\n            layout.initPositions();\n            // Render elements in initial positions:\n            series.forEach(function (s) {\n                s.finishedAnimating = true; // #13169\n                s.render();\n            });\n        }\n        layout.setK();\n        layout.resetSimulation(options);\n        if (layout.enableSimulation) {\n            layout.step();\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.step = function () {\n        var _this = this;\n        var anyLayout = this,\n            allSeries = this.series;\n        // Algorithm:\n        this.currentStep++;\n        if (this.approximation === 'barnes-hut') {\n            this.createQuadTree();\n            this.quadTree.calculateMassAndCenter();\n        }\n        for (var _i = 0, _a = this.forces || []; _i < _a.length; _i++) {\n            var forceName = _a[_i];\n            anyLayout[forceName + 'Forces'](this.temperature);\n        }\n        // Limit to the plotting area and cool down:\n        this.applyLimits();\n        // Cool down the system:\n        this.temperature = this.coolDown(this.startTemperature, this.diffTemperature, this.currentStep);\n        this.prevSystemTemperature = this.systemTemperature;\n        this.systemTemperature = this.getSystemTemperature();\n        if (this.enableSimulation) {\n            for (var _b = 0, allSeries_1 = allSeries; _b < allSeries_1.length; _b++) {\n                var series = allSeries_1[_b];\n                // Chart could be destroyed during the simulation\n                if (series.chart) {\n                    series.render();\n                }\n            }\n            if (this.maxIterations-- &&\n                isFinite(this.temperature) &&\n                !this.isStable()) {\n                if (this.simulation) {\n                    win.cancelAnimationFrame(this.simulation);\n                }\n                this.simulation = win.requestAnimationFrame(function () { return _this.step(); });\n            }\n            else {\n                this.simulation = false;\n                this.series.forEach(function (s) {\n                    fireEvent(s, 'afterSimulation');\n                });\n            }\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.stop = function () {\n        if (this.simulation) {\n            win.cancelAnimationFrame(this.simulation);\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.setArea = function (x, y, w, h) {\n        this.box = {\n            left: x,\n            top: y,\n            width: w,\n            height: h\n        };\n    };\n    ReingoldFruchtermanLayout.prototype.setK = function () {\n        // Optimal distance between nodes,\n        // available space around the node:\n        this.k = this.options.linkLength || this.integration.getK(this);\n    };\n    ReingoldFruchtermanLayout.prototype.addElementsToCollection = function (elements, collection) {\n        for (var _i = 0, elements_1 = elements; _i < elements_1.length; _i++) {\n            var element = elements_1[_i];\n            if (collection.indexOf(element) === -1) {\n                collection.push(element);\n            }\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.removeElementFromCollection = function (element, collection) {\n        var index = collection.indexOf(element);\n        if (index !== -1) {\n            collection.splice(index, 1);\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.clear = function () {\n        this.nodes.length = 0;\n        this.links.length = 0;\n        this.series.length = 0;\n        this.resetSimulation();\n    };\n    ReingoldFruchtermanLayout.prototype.resetSimulation = function () {\n        this.forcedStop = false;\n        this.systemTemperature = 0;\n        this.setMaxIterations();\n        this.setTemperature();\n        this.setDiffTemperature();\n    };\n    ReingoldFruchtermanLayout.prototype.restartSimulation = function () {\n        if (!this.simulation) {\n            // When dragging nodes, we don't need to calculate\n            // initial positions and rendering nodes:\n            this.setInitialRendering(false);\n            // Start new simulation:\n            if (!this.enableSimulation) {\n                // Run only one iteration to speed things up:\n                this.setMaxIterations(1);\n            }\n            else {\n                this.start();\n            }\n            if (this.chart) {\n                this.chart.redraw();\n            }\n            // Restore defaults:\n            this.setInitialRendering(true);\n        }\n        else {\n            // Extend current simulation:\n            this.resetSimulation();\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.setMaxIterations = function (maxIterations) {\n        this.maxIterations = ReingoldFruchtermanLayout_pick(maxIterations, this.options.maxIterations);\n    };\n    ReingoldFruchtermanLayout.prototype.setTemperature = function () {\n        this.temperature = this.startTemperature =\n            Math.sqrt(this.nodes.length);\n    };\n    ReingoldFruchtermanLayout.prototype.setDiffTemperature = function () {\n        this.diffTemperature = this.startTemperature /\n            (this.options.maxIterations + 1);\n    };\n    ReingoldFruchtermanLayout.prototype.setInitialRendering = function (enable) {\n        this.initialRendering = enable;\n    };\n    ReingoldFruchtermanLayout.prototype.createQuadTree = function () {\n        this.quadTree = new Networkgraph_QuadTree(this.box.left, this.box.top, this.box.width, this.box.height);\n        this.quadTree.insertNodes(this.nodes);\n    };\n    ReingoldFruchtermanLayout.prototype.initPositions = function () {\n        var initialPositions = this.options.initialPositions;\n        if (isFunction(initialPositions)) {\n            initialPositions.call(this);\n            for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {\n                var node = _a[_i];\n                if (!ReingoldFruchtermanLayout_defined(node.prevX)) {\n                    node.prevX = node.plotX;\n                }\n                if (!ReingoldFruchtermanLayout_defined(node.prevY)) {\n                    node.prevY = node.plotY;\n                }\n                node.dispX = 0;\n                node.dispY = 0;\n            }\n        }\n        else if (initialPositions === 'circle') {\n            this.setCircularPositions();\n        }\n        else {\n            this.setRandomPositions();\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.setCircularPositions = function () {\n        var box = this.box,\n            nodes = this.nodes,\n            nodesLength = nodes.length + 1,\n            angle = 2 * Math.PI / nodesLength,\n            rootNodes = nodes.filter(function (node) {\n                return node.linksTo.length === 0;\n        }), visitedNodes = {}, radius = this.options.initialPositionRadius, addToNodes = function (node) {\n            for (var _i = 0, _a = node.linksFrom || []; _i < _a.length; _i++) {\n                var link = _a[_i];\n                if (!visitedNodes[link.toNode.id]) {\n                    visitedNodes[link.toNode.id] = true;\n                    sortedNodes.push(link.toNode);\n                    addToNodes(link.toNode);\n                }\n            }\n        };\n        var sortedNodes = [];\n        // Start with identified root nodes an sort the nodes by their\n        // hierarchy. In trees, this ensures that branches don't cross\n        // eachother.\n        for (var _i = 0, rootNodes_1 = rootNodes; _i < rootNodes_1.length; _i++) {\n            var rootNode = rootNodes_1[_i];\n            sortedNodes.push(rootNode);\n            addToNodes(rootNode);\n        }\n        // Cyclic tree, no root node found\n        if (!sortedNodes.length) {\n            sortedNodes = nodes;\n            // Dangling, cyclic trees\n        }\n        else {\n            for (var _a = 0, nodes_1 = nodes; _a < nodes_1.length; _a++) {\n                var node_1 = nodes_1[_a];\n                if (sortedNodes.indexOf(node_1) === -1) {\n                    sortedNodes.push(node_1);\n                }\n            }\n        }\n        var node;\n        // Initial positions are laid out along a small circle, appearing\n        // as a cluster in the middle\n        for (var i = 0, iEnd = sortedNodes.length; i < iEnd; ++i) {\n            node = sortedNodes[i];\n            node.plotX = node.prevX = ReingoldFruchtermanLayout_pick(node.plotX, box.width / 2 + radius * Math.cos(i * angle));\n            node.plotY = node.prevY = ReingoldFruchtermanLayout_pick(node.plotY, box.height / 2 + radius * Math.sin(i * angle));\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.setRandomPositions = function () {\n        var box = this.box,\n            nodes = this.nodes,\n            nodesLength = nodes.length + 1, \n            /**\n             * Return a repeatable,\n            quasi-random number based on an integer\n             * input. For the initial positions\n             * @private\n             */\n            unrandom = function (n) {\n                var rand = n * n / Math.PI;\n            rand = rand - Math.floor(rand);\n            return rand;\n        };\n        var node;\n        // Initial positions:\n        for (var i = 0, iEnd = nodes.length; i < iEnd; ++i) {\n            node = nodes[i];\n            node.plotX = node.prevX = ReingoldFruchtermanLayout_pick(node.plotX, box.width * unrandom(i));\n            node.plotY = node.prevY = ReingoldFruchtermanLayout_pick(node.plotY, box.height * unrandom(nodesLength + i));\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.force = function (name) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        this.integration[name].apply(this, args);\n    };\n    ReingoldFruchtermanLayout.prototype.barycenterForces = function () {\n        this.getBarycenter();\n        this.force('barycenter');\n    };\n    ReingoldFruchtermanLayout.prototype.getBarycenter = function () {\n        var systemMass = 0,\n            cx = 0,\n            cy = 0;\n        for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {\n            var node = _a[_i];\n            cx += node.plotX * node.mass;\n            cy += node.plotY * node.mass;\n            systemMass += node.mass;\n        }\n        this.barycenter = {\n            x: cx,\n            y: cy,\n            xFactor: cx / systemMass,\n            yFactor: cy / systemMass\n        };\n        return this.barycenter;\n    };\n    ReingoldFruchtermanLayout.prototype.barnesHutApproximation = function (node, quadNode) {\n        var distanceXY = this.getDistXY(node,\n            quadNode),\n            distanceR = this.vectorLength(distanceXY);\n        var goDeeper,\n            force;\n        if (node !== quadNode && distanceR !== 0) {\n            if (quadNode.isInternal) {\n                // Internal node:\n                if (quadNode.boxSize / distanceR <\n                    this.options.theta &&\n                    distanceR !== 0) {\n                    // Treat as an external node:\n                    force = this.repulsiveForce(distanceR, this.k);\n                    this.force('repulsive', node, force * quadNode.mass, distanceXY, distanceR);\n                    goDeeper = false;\n                }\n                else {\n                    // Go deeper:\n                    goDeeper = true;\n                }\n            }\n            else {\n                // External node, direct force:\n                force = this.repulsiveForce(distanceR, this.k);\n                this.force('repulsive', node, force * quadNode.mass, distanceXY, distanceR);\n            }\n        }\n        return goDeeper;\n    };\n    ReingoldFruchtermanLayout.prototype.repulsiveForces = function () {\n        var _this = this;\n        if (this.approximation === 'barnes-hut') {\n            var _loop_1 = function (node) {\n                    this_1.quadTree.visitNodeRecursive(null,\n                function (quadNode) { return (_this.barnesHutApproximation(node,\n                quadNode)); });\n            };\n            var this_1 = this;\n            for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {\n                var node = _a[_i];\n                _loop_1(node);\n            }\n        }\n        else {\n            var force = void 0,\n                distanceR = void 0,\n                distanceXY = void 0;\n            for (var _b = 0, _c = this.nodes; _b < _c.length; _b++) {\n                var node = _c[_b];\n                for (var _d = 0, _e = this.nodes; _d < _e.length; _d++) {\n                    var repNode = _e[_d];\n                    if (\n                    // Node cannot repulse itself:\n                    node !== repNode &&\n                        // Only close nodes affect each other:\n                        // layout.getDistR(node, repNode) < 2 * k &&\n                        // Not dragged:\n                        !node.fixedPosition) {\n                        distanceXY = this.getDistXY(node, repNode);\n                        distanceR = this.vectorLength(distanceXY);\n                        if (distanceR !== 0) {\n                            force = this.repulsiveForce(distanceR, this.k);\n                            this.force('repulsive', node, force * repNode.mass, distanceXY, distanceR);\n                        }\n                    }\n                }\n            }\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.attractiveForces = function () {\n        var distanceXY,\n            distanceR,\n            force;\n        for (var _i = 0, _a = this.links; _i < _a.length; _i++) {\n            var link = _a[_i];\n            if (link.fromNode && link.toNode) {\n                distanceXY = this.getDistXY(link.fromNode, link.toNode);\n                distanceR = this.vectorLength(distanceXY);\n                if (distanceR !== 0) {\n                    force = this.attractiveForce(distanceR, this.k);\n                    this.force('attractive', link, force, distanceXY, distanceR);\n                }\n            }\n        }\n    };\n    ReingoldFruchtermanLayout.prototype.applyLimits = function () {\n        var nodes = this.nodes;\n        for (var _i = 0, nodes_2 = nodes; _i < nodes_2.length; _i++) {\n            var node = nodes_2[_i];\n            if (node.fixedPosition) {\n                continue;\n            }\n            this.integration.integrate(this, node);\n            this.applyLimitBox(node, this.box);\n            // Reset displacement:\n            node.dispX = 0;\n            node.dispY = 0;\n        }\n    };\n    /**\n     * External box that nodes should fall. When hitting an edge, node\n     * should stop or bounce.\n     * @private\n     */\n    ReingoldFruchtermanLayout.prototype.applyLimitBox = function (node, box) {\n        var radius = node.radius;\n        /*\n        TO DO: Consider elastic collision instead of stopping.\n        o' means end position when hitting plotting area edge:\n\n        - \"inelastic\":\n        o\n            \\\n        ______\n        |  o'\n        |   \\\n        |    \\\n\n        - \"elastic\"/\"bounced\":\n        o\n            \\\n        ______\n        |  ^\n        | / \\\n        |o'  \\\n\n        Euler sample:\n        if (plotX < 0) {\n            plotX = 0;\n            dispX *= -1;\n        }\n\n        if (plotX > box.width) {\n            plotX = box.width;\n            dispX *= -1;\n        }\n\n        */\n        // Limit X-coordinates:\n        node.plotX = clamp(node.plotX, box.left + radius, box.width - radius);\n        // Limit Y-coordinates:\n        node.plotY = clamp(node.plotY, box.top + radius, box.height - radius);\n    };\n    /**\n     * From \"A comparison of simulated annealing cooling strategies\" by\n     * Nourani and Andresen work.\n     * @private\n     */\n    ReingoldFruchtermanLayout.prototype.coolDown = function (temperature, temperatureStep, currentStep) {\n        // Logarithmic:\n        /*\n        return Math.sqrt(this.nodes.length) -\n            Math.log(\n                currentStep * layout.diffTemperature\n            );\n        */\n        // Exponential:\n        /*\n        let alpha = 0.1;\n        layout.temperature = Math.sqrt(layout.nodes.length) *\n            Math.pow(alpha, layout.diffTemperature);\n        */\n        // Linear:\n        return temperature - temperatureStep * currentStep;\n    };\n    ReingoldFruchtermanLayout.prototype.isStable = function () {\n        return Math.abs(this.systemTemperature -\n            this.prevSystemTemperature) < 0.00001 || this.temperature <= 0;\n    };\n    ReingoldFruchtermanLayout.prototype.getSystemTemperature = function () {\n        var value = 0;\n        for (var _i = 0, _a = this.nodes; _i < _a.length; _i++) {\n            var node = _a[_i];\n            value += node.temperature;\n        }\n        return value;\n    };\n    ReingoldFruchtermanLayout.prototype.vectorLength = function (vector) {\n        return Math.sqrt(vector.x * vector.x + vector.y * vector.y);\n    };\n    ReingoldFruchtermanLayout.prototype.getDistR = function (nodeA, nodeB) {\n        var distance = this.getDistXY(nodeA,\n            nodeB);\n        return this.vectorLength(distance);\n    };\n    ReingoldFruchtermanLayout.prototype.getDistXY = function (nodeA, nodeB) {\n        var xDist = nodeA.plotX - nodeB.plotX,\n            yDist = nodeA.plotY - nodeB.plotY;\n        return {\n            x: xDist,\n            y: yDist,\n            absX: Math.abs(xDist),\n            absY: Math.abs(yDist)\n        };\n    };\n    return ReingoldFruchtermanLayout;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Networkgraph_ReingoldFruchtermanLayout = (ReingoldFruchtermanLayout);\n\n;// ./code/es5/es-modules/Series/SimulationSeriesUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar SimulationSeriesUtilities_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, syncTimeout = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).syncTimeout;\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\n/**\n * Create a setTimeout for the first drawDataLabels()\n * based on the dataLabels.animation.defer value\n * for series which have enabled simulation.\n * @private\n */\nfunction initDataLabelsDefer() {\n    var _this = this;\n    var _a;\n    var dlOptions = this.options.dataLabels;\n    // Method drawDataLabels() fires for the first time after\n    // dataLabels.animation.defer time unless\n    // the dataLabels.animation = false or dataLabels.defer = false\n    // or if the simulation is disabled\n    if (!(dlOptions === null || dlOptions === void 0 ? void 0 : dlOptions.defer) ||\n        !((_a = this.options.layoutAlgorithm) === null || _a === void 0 ? void 0 : _a.enableSimulation)) {\n        this.deferDataLabels = false;\n    }\n    else {\n        syncTimeout(function () {\n            _this.deferDataLabels = false;\n        }, dlOptions ? animObject(dlOptions.animation).defer : 0);\n    }\n}\n/**\n * Initialize the SVG group for the DataLabels with correct opacities\n * and correct styles so that the animation for the series that have\n * simulation enabled works fine.\n * @private\n */\nfunction initDataLabels() {\n    var series = this,\n        dlOptions = series.options.dataLabels;\n    if (!series.dataLabelsGroup) {\n        var dataLabelsGroup = this.initDataLabelsGroup();\n        // Apply the dataLabels.style not only to the\n        // individual dataLabels but also to the entire group\n        if (!series.chart.styledMode && (dlOptions === null || dlOptions === void 0 ? void 0 : dlOptions.style)) {\n            dataLabelsGroup.css(dlOptions.style);\n        }\n        // Initialize the opacity of the group to 0 (start of animation)\n        dataLabelsGroup.attr({ opacity: 0 });\n        if (series.visible) { // #2597, #3023, #3024\n            dataLabelsGroup.show();\n        }\n        return dataLabelsGroup;\n    }\n    // Place it on first and subsequent (redraw) calls\n    series.dataLabelsGroup.attr(SimulationSeriesUtilities_merge({ opacity: 1 }, this.getPlotBox('data-labels')));\n    return series.dataLabelsGroup;\n}\nvar DataLabelsDeferUtils = {\n    initDataLabels: initDataLabels,\n    initDataLabelsDefer: initDataLabelsDefer\n};\n/* harmony default export */ var SimulationSeriesUtilities = (DataLabelsDeferUtils);\n\n;// ./code/es5/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\nvar TextPath_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, TextPath_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey, TextPath_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, TextPath_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    var _this = this;\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    var url = this.renderer.url,\n        textWrapper = this.text || this,\n        textPath = textWrapper.textPath,\n        attributes = textPathOptions.attributes,\n        enabled = textPathOptions.enabled;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        var undo = TextPath_addEvent(textWrapper, 'afterModifyTree',\n            function (e) {\n                if (path && enabled) {\n                    // Set ID for the path\n                    var textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                var textAttribs = {\n                        // `dx`/`dy` options must by set on <text> (parent), the\n                        // rest should be set on <textPath>\n                        x: 0,\n                        y: 0\n                    };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                _this.attr({ transform: '' });\n                if (_this.box) {\n                    _this.box = _this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                var children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: \"\" + url + \"#\".concat(textPathId)\n                    }),\n                    children: children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path: path, undo: undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    var _a;\n    var bBox = event.bBox,\n        tp = (_a = this.element) === null || _a === void 0 ? void 0 : _a.querySelector('textPath');\n    if (tp) {\n        var polygon = [], _b = this.renderer.fontMetrics(this.element), b_1 = _b.b, h = _b.h, descender_1 = h - b_1, lineCleanerRegex = new RegExp('(<tspan>|' +\n                '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n                '<\\\\/tspan>)', 'g'), lines = tp\n                .innerHTML\n                .replace(lineCleanerRegex, '')\n                .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        var appendTopAndBottom = function (charIndex,\n            positionOfChar) {\n                var x = positionOfChar.x,\n            y = positionOfChar.y,\n            rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad,\n            cosRot = Math.cos(rotation),\n            sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender_1 * cosRot,\n                    y - descender_1 * sinRot\n                ],\n                [\n                    x + b_1 * cosRot,\n                    y + b_1 * sinRot\n                ]\n            ];\n        };\n        for (var i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            var line = lines[lineIndex],\n                lineLen = line.length;\n            for (var lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    var srcCharIndex = (i +\n                            lineCharIndex +\n                            lineIndex),\n                        _c = appendTopAndBottom(srcCharIndex,\n                        tp.getStartPositionOfChar(srcCharIndex)),\n                        lower = _c[0],\n                        upper = _c[1];\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                var srcCharIndex = i + lineIndex,\n                    charPos = tp.getEndPositionOfChar(srcCharIndex),\n                    _d = appendTopAndBottom(srcCharIndex,\n                    charPos),\n                    lower = _d[0],\n                    upper = _d[1];\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    var _a;\n    var labelOptions = event.labelOptions,\n        point = event.point,\n        textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n            labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(((_a = point.getDataLabelPath) === null || _a === void 0 ? void 0 : _a.call(point, this)) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction TextPath_compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    var svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nvar TextPath = {\n    compose: TextPath_compose\n};\n/* harmony default export */ var Extensions_TextPath = (TextPath);\n\n;// ./code/es5/es-modules/Series/Networkgraph/NetworkgraphSeries.js\n/* *\n *\n *  Networkgraph series\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar NetworkgraphSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\n\nvar noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\n\n\n\n\nvar Series = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series, NetworkgraphSeries_a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes, columnProto = NetworkgraphSeries_a.column.prototype, lineProto = NetworkgraphSeries_a.line.prototype;\n\nvar NetworkgraphSeries_initDataLabels = SimulationSeriesUtilities.initDataLabels, NetworkgraphSeries_initDataLabelsDefer = SimulationSeriesUtilities.initDataLabelsDefer;\n\nvar NetworkgraphSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, NetworkgraphSeries_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, NetworkgraphSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, NetworkgraphSeries_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, NetworkgraphSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.networkgraph\n *\n * @extends Highcharts.Series\n */\nvar NetworkgraphSeries = /** @class */ (function (_super) {\n    NetworkgraphSeries_extends(NetworkgraphSeries, _super);\n    function NetworkgraphSeries() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.deferDataLabels = true;\n        return _this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    NetworkgraphSeries.compose = function (ChartClass) {\n        Series_DragNodesComposition.compose(ChartClass);\n        Networkgraph_ReingoldFruchtermanLayout.compose(ChartClass);\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Defer the layout.\n     * Each series first registers all nodes and links, then layout\n     * calculates all nodes positions and calls `series.render()` in every\n     * simulation step.\n     *\n     * Note:\n     * Animation is done through `requestAnimationFrame` directly, without\n     * `Highcharts.animate()` use.\n     * @private\n     */\n    NetworkgraphSeries.prototype.deferLayout = function () {\n        var layoutOptions = this.options.layoutAlgorithm,\n            chartOptions = this.chart.options.chart;\n        var layout,\n            graphLayoutsStorage = this.chart.graphLayoutsStorage,\n            graphLayoutsLookup = this.chart.graphLayoutsLookup;\n        if (!this.visible) {\n            return;\n        }\n        if (!graphLayoutsStorage) {\n            this.chart.graphLayoutsStorage = graphLayoutsStorage = {};\n            this.chart.graphLayoutsLookup = graphLayoutsLookup = [];\n        }\n        layout = graphLayoutsStorage[layoutOptions.type];\n        if (!layout) {\n            layoutOptions.enableSimulation =\n                !NetworkgraphSeries_defined(chartOptions.forExport) ?\n                    layoutOptions.enableSimulation :\n                    !chartOptions.forExport;\n            graphLayoutsStorage[layoutOptions.type] = layout =\n                new Series_GraphLayoutComposition.layouts[layoutOptions.type]();\n            layout.init(layoutOptions);\n            graphLayoutsLookup.splice(layout.index, 0, layout);\n        }\n        this.layout = layout;\n        layout.setArea(0, 0, this.chart.plotWidth, this.chart.plotHeight);\n        layout.addElementsToCollection([this], layout.series);\n        layout.addElementsToCollection(this.nodes, layout.nodes);\n        layout.addElementsToCollection(this.points, layout.links);\n    };\n    /**\n     * @private\n     */\n    NetworkgraphSeries.prototype.destroy = function () {\n        if (this.layout) {\n            this.layout.removeElementFromCollection(this, this.layout.series);\n        }\n        Series_NodesComposition.destroy.call(this);\n    };\n    /**\n     * Networkgraph has two separate collections of nodes and lines, render\n     * dataLabels for both sets:\n     * @private\n     */\n    NetworkgraphSeries.prototype.drawDataLabels = function () {\n        // We defer drawing the dataLabels\n        // until dataLabels.animation.defer time passes\n        if (this.deferDataLabels) {\n            return;\n        }\n        var dlOptions = this.options.dataLabels;\n        var textPath;\n        if (dlOptions === null || dlOptions === void 0 ? void 0 : dlOptions.textPath) {\n            textPath = dlOptions.textPath;\n        }\n        // Render node labels:\n        Series.prototype.drawDataLabels.call(this, this.nodes);\n        // Render link labels:\n        if (dlOptions === null || dlOptions === void 0 ? void 0 : dlOptions.linkTextPath) {\n            // If linkTextPath is set, render link labels with linkTextPath\n            dlOptions.textPath = dlOptions.linkTextPath;\n        }\n        Series.prototype.drawDataLabels.call(this, this.data);\n        // Go back to textPath for nodes\n        if (dlOptions === null || dlOptions === void 0 ? void 0 : dlOptions.textPath) {\n            dlOptions.textPath = textPath;\n        }\n    };\n    /**\n     * Extend generatePoints by adding the nodes, which are Point objects\n     * but pushed to the this.nodes array.\n     * @private\n     */\n    NetworkgraphSeries.prototype.generatePoints = function () {\n        var node,\n            i;\n        Series_NodesComposition.generatePoints.apply(this, arguments);\n        // In networkgraph, it's fine to define standalone nodes, create\n        // them:\n        if (this.options.nodes) {\n            this.options.nodes.forEach(function (nodeOptions) {\n                if (!this.nodeLookup[nodeOptions.id]) {\n                    this.nodeLookup[nodeOptions.id] =\n                        this.createNode(nodeOptions.id);\n                }\n            }, this);\n        }\n        for (i = this.nodes.length - 1; i >= 0; i--) {\n            node = this.nodes[i];\n            node.degree = node.getDegree();\n            node.radius = NetworkgraphSeries_pick(node.marker && node.marker.radius, this.options.marker && this.options.marker.radius, 0);\n            node.key = node.name;\n            // If node exists, but it's not available in nodeLookup,\n            // then it's leftover from previous runs (e.g. setData)\n            if (!this.nodeLookup[node.id]) {\n                node.remove();\n            }\n        }\n        this.data.forEach(function (link) {\n            link.formatPrefix = 'link';\n        });\n        this.indexateNodes();\n    };\n    /**\n     * In networkgraph, series.points refers to links,\n     * but series.nodes refers to actual points.\n     * @private\n     */\n    NetworkgraphSeries.prototype.getPointsCollection = function () {\n        return this.nodes || [];\n    };\n    /**\n     * Set index for each node. Required for proper `node.update()`.\n     * Note that links are indexated out of the box in `generatePoints()`.\n     *\n     * @private\n     */\n    NetworkgraphSeries.prototype.indexateNodes = function () {\n        this.nodes.forEach(function (node, index) {\n            node.index = index;\n        });\n    };\n    /**\n     * Extend init with base event, which should stop simulation during\n     * update. After data is updated, `chart.render` resumes the simulation.\n     * @private\n     */\n    NetworkgraphSeries.prototype.init = function (chart, options) {\n        var _this = this;\n        _super.prototype.init.call(this, chart, options);\n        NetworkgraphSeries_initDataLabelsDefer.call(this);\n        NetworkgraphSeries_addEvent(this, 'updatedData', function () {\n            if (_this.layout) {\n                _this.layout.stop();\n            }\n        });\n        NetworkgraphSeries_addEvent(this, 'afterUpdate', function () {\n            _this.nodes.forEach(function (node) {\n                if (node && node.series) {\n                    node.resolveColor();\n                }\n            });\n        });\n        // If the dataLabels.animation.defer time is longer than\n        // the time it takes for the layout to become stable then\n        // drawDataLabels would never be called (that's why we force it here)\n        NetworkgraphSeries_addEvent(this, 'afterSimulation', function () {\n            this.deferDataLabels = false;\n            this.drawDataLabels();\n        });\n        return this;\n    };\n    /**\n     * Extend the default marker attribs by using a non-rounded X position,\n     * otherwise the nodes will jump from pixel to pixel which looks a bit\n     * jaggy when approaching equilibrium.\n     * @private\n     */\n    NetworkgraphSeries.prototype.markerAttribs = function (point, state) {\n        var attribs = Series.prototype.markerAttribs.call(this,\n            point,\n            state);\n        // Series.render() is called before initial positions are set:\n        if (!NetworkgraphSeries_defined(point.plotY)) {\n            attribs.y = 0;\n        }\n        attribs.x = (point.plotX || 0) - (attribs.width || 0) / 2;\n        return attribs;\n    };\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    NetworkgraphSeries.prototype.pointAttribs = function (point, state) {\n        // By default, only `selected` state is passed on\n        var pointState = state || point && point.state || 'normal',\n            stateOptions = this.options.states[pointState];\n        var attribs = Series.prototype.pointAttribs.call(this,\n            point,\n            pointState);\n        if (point && !point.isNode) {\n            attribs = point.getLinkAttributes();\n            // For link, get prefixed names:\n            if (stateOptions) {\n                attribs = {\n                    // TO DO: API?\n                    stroke: stateOptions.linkColor || attribs.stroke,\n                    dashstyle: (stateOptions.linkDashStyle || attribs.dashstyle),\n                    opacity: NetworkgraphSeries_pick(stateOptions.linkOpacity, attribs.opacity),\n                    'stroke-width': stateOptions.linkColor ||\n                        attribs['stroke-width']\n                };\n            }\n        }\n        return attribs;\n    };\n    /**\n     * Extend the render function to also render this.nodes together with\n     * the points.\n     * @private\n     */\n    NetworkgraphSeries.prototype.render = function () {\n        var series = this,\n            points = series.points,\n            hoverPoint = series.chart.hoverPoint,\n            dataLabels = [];\n        // Render markers:\n        series.points = series.nodes;\n        lineProto.render.call(this);\n        series.points = points;\n        points.forEach(function (point) {\n            if (point.fromNode && point.toNode) {\n                point.renderLink();\n                point.redrawLink();\n            }\n        });\n        if (hoverPoint && hoverPoint.series === series) {\n            series.redrawHalo(hoverPoint);\n        }\n        if (series.chart.hasRendered &&\n            !series.options.dataLabels.allowOverlap) {\n            series.nodes.concat(series.points).forEach(function (node) {\n                if (node.dataLabel) {\n                    dataLabels.push(node.dataLabel);\n                }\n            });\n            series.chart.hideOverlappingLabels(dataLabels);\n        }\n    };\n    /**\n     * When state should be passed down to all points, concat nodes and\n     * links and apply this state to all of them.\n     * @private\n     */\n    NetworkgraphSeries.prototype.setState = function (state, inherit) {\n        if (inherit) {\n            this.points = this.nodes.concat(this.data);\n            Series.prototype.setState.apply(this, arguments);\n            this.points = this.data;\n        }\n        else {\n            Series.prototype.setState.apply(this, arguments);\n        }\n        // If simulation is done, re-render points with new states:\n        if (!this.layout.simulation && !state) {\n            this.render();\n        }\n    };\n    /**\n     * Run pre-translation and register nodes&links to the deffered layout.\n     * @private\n     */\n    NetworkgraphSeries.prototype.translate = function () {\n        this.generatePoints();\n        this.deferLayout();\n        this.nodes.forEach(function (node) {\n            // Draw the links from this node\n            node.isInside = true;\n            node.linksFrom.forEach(function (point) {\n                point.shapeType = 'path';\n                // Pass test in drawPoints\n                point.y = 1;\n            });\n        });\n    };\n    NetworkgraphSeries.defaultOptions = NetworkgraphSeries_merge(Series.defaultOptions, Networkgraph_NetworkgraphSeriesDefaults);\n    return NetworkgraphSeries;\n}(Series));\nNetworkgraphSeries_extend(NetworkgraphSeries.prototype, {\n    pointClass: Networkgraph_NetworkgraphPoint,\n    animate: void 0, // Animation is run in `series.simulation`\n    directTouch: true,\n    drawGraph: void 0,\n    forces: ['barycenter', 'repulsive', 'attractive'],\n    hasDraggableNodes: true,\n    isCartesian: false,\n    noSharedTooltip: true,\n    pointArrayMap: ['from', 'to'],\n    requireSorting: false,\n    trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n    initDataLabels: NetworkgraphSeries_initDataLabels,\n    buildKDTree: noop,\n    createNode: Series_NodesComposition.createNode,\n    drawTracker: columnProto.drawTracker,\n    onMouseDown: Series_DragNodesComposition.onMouseDown,\n    onMouseMove: Series_DragNodesComposition.onMouseMove,\n    onMouseUp: Series_DragNodesComposition.onMouseUp,\n    redrawHalo: Series_DragNodesComposition.redrawHalo\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('networkgraph', NetworkgraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Networkgraph_NetworkgraphSeries = (NetworkgraphSeries);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback that fires after the end of Networkgraph series simulation\n * when the layout is stable.\n *\n * @callback Highcharts.NetworkgraphAfterSimulationCallbackFunction\n *\n * @param {Highcharts.Series} this\n *        The series where the event occurred.\n *\n * @param {global.Event} event\n *        The event that occurred.\n */\n''; // Detach doclets above\n\n;// ./code/es5/es-modules/masters/modules/networkgraph.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nNetworkgraph_NetworkgraphSeries.compose(G.Chart);\n/* harmony default export */ var networkgraph_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__28__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "NodesComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "networkgraph_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "composed", "addEvent", "pushUnique", "onChartLoad", "mousedownUnbinder", "mousemoveUnbinder", "mouseupUnbinder", "point", "chart", "container", "event", "hoverPoint", "series", "hasDraggableNodes", "options", "draggable", "onMouseDown", "e", "onMouseMove", "ownerDocument", "onMouseUp", "Series_DragNodesComposition", "compose", "ChartClass", "_a", "normalizedEvent", "pointer", "normalize", "fixedPosition", "chartX", "chartY", "plotX", "plotY", "inDragMode", "diffX", "diffY", "graphLayoutsLookup", "newPlotX", "newPlotY", "Math", "abs", "isInsidePlot", "hasDragged", "redrawHalo", "for<PERSON>ach", "layout", "restartSimulation", "enableSimulation", "start", "redraw", "fixedDraggable", "halo", "attr", "haloPath", "states", "hover", "size", "setAnimation", "GraphLayoutComposition_composed", "GraphLayoutComposition_addEvent", "GraphLayoutComposition_pushUnique", "onChartAfterPrint", "updateSimulation", "onChartBeforePrint", "onChartPredraw", "stop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "systemsStable", "afterRender", "layoutStep", "maxIterations", "isFinite", "temperature", "isStable", "beforeStep", "step", "render", "Series_GraphLayoutComposition", "integrations", "layouts", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesProto", "pointProto", "pointClass", "defined", "extend", "find", "merge", "pick", "destroy", "data", "concat", "points", "nodes", "apply", "arguments", "setData", "node", "length", "setNodeState", "state", "args", "others", "isNode", "linksTo", "linksFrom", "fromNode", "toNode", "linkOrNode", "setState", "graphic", "updateNode", "animation", "runEvent", "_this", "dataLength", "linkConfig", "index", "update", "nodeIndex", "reduce", "prevIndex", "id", "nodeConfig", "push", "PointClass", "SeriesClass", "createNode", "findById", "newNode_1", "className", "y", "getSum", "sumTo", "sumFrom", "link", "weight", "max", "offset", "coll", "i", "<PERSON><PERSON><PERSON><PERSON>", "outgoing", "formatPrefix", "name", "mass", "marker", "radius", "generatePoints", "nodeLookup", "colorCounter", "level", "from", "styledMode", "colorIndex", "color", "to", "Series_NodesComposition", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "NetworkgraphPoint_a", "NetworkgraphPoint_seriesProto", "Point", "NetworkgraphPoint_addEvent", "css", "NetworkgraphPoint_defined", "NetworkgraphPoint_extend", "NetworkgraphPoint_pick", "NetworkgraphPoint", "_super", "x", "cursor", "destroyElements", "removeElementFromCollection", "getDegree", "deg", "getLinkAttributes", "linkOptions", "pointOptions", "width", "stroke", "dashstyle", "dashStyle", "opacity", "getLinkPath", "left", "right", "getMass", "m1", "m2", "sum", "<PERSON><PERSON><PERSON><PERSON>", "redrawLink", "attribs", "path", "shapeArgs", "pointAttribs", "dataLabels", "label", "animate", "end", "remove", "nodesOptions", "linkFromTo", "indexOf", "splice", "removePoint", "slice", "isDirty", "isDirtyData", "renderLink", "renderer", "addClass", "getClassName", "add", "group", "Networkgraph_NetworkgraphSeriesDefaults", "stickyTracking", "inactiveOtherPoints", "enabled", "inactive", "duration", "linkOpacity", "formatter", "String", "linkFormatter", "linkTextPath", "textPath", "style", "transition", "defer", "layoutAlgorithm", "initialPositions", "initialPositionRadius", "theta", "maxSpeed", "approximation", "type", "integration", "gravitationalConstant", "friction", "showInLegend", "Networkgraph_EulerIntegration", "attractive", "force", "distanceXY", "distanceR", "massFactor", "translatedX", "translatedY", "dispX", "degree", "dispY", "attractiveForceFunction", "k", "barycenter", "xFactor", "yFactor", "phi", "getK", "pow", "box", "height", "integrate", "vectorLength", "min", "repulsive", "repulsiveForceFunction", "QuadTreeNode", "body", "isEmpty", "isInternal", "boxSize", "divideBox", "halfWidth", "halfHeight", "top", "getBoxPosition", "insert", "depth", "newQuadTreeNode", "NaN", "updateMassAndCenter", "_i", "pointMass", "QuadTree", "max<PERSON><PERSON><PERSON>", "isRoot", "calculateMassAndCenter", "visitNodeRecursive", "insertNodes", "points_1", "beforeCallback", "afterCallback", "<PERSON><PERSON><PERSON><PERSON>", "qtNode", "Networkgraph_VerletIntegration", "diffTemperature", "prevX", "prevY", "frictionX", "frictionY", "signX", "signY", "factor", "win", "clamp", "ReingoldFruchtermanLayout_defined", "isFunction", "fireEvent", "ReingoldFruchtermanLayout_pick", "ReingoldFruchtermanLayout", "currentStep", "initialRendering", "links", "simulation", "euler", "verlet", "init", "setInitialRendering", "attractive<PERSON><PERSON><PERSON>", "repulsiveForce", "enable", "forces", "initPositions", "s", "finishedAnimating", "setK", "resetSimulation", "allSeries", "createQuadTree", "quadTree", "anyLayout", "forceName", "applyLimits", "coolDown", "startTemperature", "prevSystemTemperature", "systemTemperature", "getSystemTemperature", "_b", "allSeries_1", "cancelAnimationFrame", "requestAnimationFrame", "<PERSON><PERSON><PERSON>", "w", "h", "linkLength", "addElementsToCollection", "elements", "collection", "elements_1", "element", "clear", "forcedStop", "setMaxIterations", "setTemperature", "setDiffTemperature", "sqrt", "setCircularPositions", "setRandomPositions", "angle", "PI", "rootNodes", "filter", "visitedNodes", "addToNodes", "sortedNodes", "rootNodes_1", "rootNode", "nodes_1", "node_1", "iEnd", "cos", "sin", "<PERSON><PERSON><PERSON><PERSON>", "unrandom", "rand", "floor", "barycenterForces", "getBarycenter", "systemMass", "cx", "cy", "barnesHutApproximation", "quadNode", "goDeeper", "getDistXY", "repulsiveForces", "_loop_1", "this_1", "_c", "_d", "_e", "repNode", "attractiveForces", "nodes_2", "applyLimitBox", "temperatureStep", "value", "vector", "getDistR", "nodeA", "nodeB", "distance", "xDist", "yDist", "absX", "absY", "SimulationSeriesUtilities_merge", "syncTimeout", "animObject", "SimulationSeriesUtilities", "initDataLabels", "dlOptions", "dataLabelsGroup", "initDataLabelsGroup", "visible", "show", "getPlotBox", "initDataLabelsDefer", "deferDataLabels", "deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "textPathOptions", "attributes", "dy", "startOffset", "textAnchor", "url", "textWrapper", "text", "undo", "textPathId", "textAttribs", "dx", "transform", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "bBox", "tp", "querySelector", "polygon", "fontMetrics", "b_1", "descender_1", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "sinRot", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "getStartPositionOfChar", "lower", "upper", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "NetworkgraphSeries_extends", "TypeError", "noop", "Series", "NetworkgraphSeries_a", "seriesTypes", "columnProto", "column", "lineProto", "NetworkgraphSeries_initDataLabels", "NetworkgraphSeries_initDataLabelsDefer", "NetworkgraphSeries_addEvent", "NetworkgraphSeries_defined", "NetworkgraphSeries_extend", "NetworkgraphSeries_merge", "NetworkgraphSeries_pick", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "NetworkgraphSeries", "Networkgraph_ReingoldFruchtermanLayout", "deferLayout", "layoutOptions", "chartOptions", "graphLayoutsStorage", "forExport", "plot<PERSON>id<PERSON>", "plotHeight", "drawDataLabels", "nodeOptions", "indexateNodes", "getPointsCollection", "resolveColor", "markerAttribs", "pointState", "stateOptions", "linkColor", "linkDashStyle", "hasRendered", "allowOverlap", "dataLabel", "hideOverlappingLabels", "inherit", "translate", "isInside", "shapeType", "defaultOptions", "directTouch", "drawGraph", "isCartesian", "noSharedTooltip", "pointArrayMap", "requireSorting", "trackerGroups", "buildKDTree", "drawTracker", "registerSeriesType", "G", "Networkgraph_NetworkgraphSeries", "Chart"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,CAAEA,QAAQ,cAAc,cAAiB,EACrH,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,aAAa,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACpJ,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,CAAEA,QAAQ,cAAc,cAAiB,EAExJJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACzH,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAA+B,CAAEC,CAAgC,EACrH,OAAgB,AAAC,WACP,aACA,IAqsBFC,EA2gFAA,EAxyFJC,EAxaUC,EAAuB,CAE/B,GACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,SAASf,CAAM,EACtC,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,WAAa,OAAOjB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASnB,CAAO,CAAEqB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAkB,CACjE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmHzB,EAAoB,IACvI0B,EAAuI1B,EAAoBI,CAAC,CAACqB,GAe7JE,EAAW,AAACH,IAA+EG,QAAQ,CAEnGC,EAAW,AAACJ,IAA+EI,QAAQ,CAAEC,EAAa,AAACL,IAA+EK,UAAU,CAkBhN,SAASC,IACL,IACIC,EACAC,EACAC,EACAC,EAJAC,EAAQ,IAAI,AAKZA,CAAAA,EAAMC,SAAS,EACfL,CAAAA,EAAoBH,EAASO,EAAMC,SAAS,CAAE,YAAa,SAAUC,CAAK,EAClEL,GACAA,IAEAC,GACAA,IAEJC,CAAAA,EAAQC,EAAMG,UAAU,AAAD,GAEnBJ,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACC,iBAAiB,EAC9BN,EAAMK,MAAM,CAACE,OAAO,CAACC,SAAS,GAC9BR,EAAMK,MAAM,CAACI,WAAW,CAACT,EAAOG,GAChCL,EAAoBJ,EAASO,EAAMC,SAAS,CAAE,YAAa,SAAUQ,CAAC,EAAI,OAAQV,GAC9EA,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACM,WAAW,CAACX,EAAOU,EAAK,GACzCX,EAAkBL,EAASO,EAAMC,SAAS,CAACU,aAAa,CAAE,UAAW,SAAUF,CAAC,EAG5E,OAFAZ,IACAC,IACOC,GACHA,EAAMK,MAAM,EACZL,EAAMK,MAAM,CAACQ,SAAS,CAACb,EAAOU,EACtC,GAER,EAAC,EAELhB,EAASO,EAAO,UAAW,WACvBJ,GACJ,EACJ,CA2G6B,IAAIiB,EAPN,CACvBC,QAlJJ,SAAiBC,CAAU,EACnBrB,EAAWF,EAAU,cACrBC,EAASsB,EAAY,OAAQpB,EAErC,EA+IIa,YA5FJ,SAAqBT,CAAK,CAAEG,CAAK,EAE7B,IADIc,EACAC,EAAkB,AAAC,CAAA,AAA8B,OAA7BD,CAAAA,EAAK,IAAI,CAAChB,KAAK,CAACkB,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGG,SAAS,CAACjB,EAAK,GAAMA,CAC9GH,CAAAA,EAAMqB,aAAa,CAAG,CAClBC,OAAQJ,EAAgBI,MAAM,CAC9BC,OAAQL,EAAgBK,MAAM,CAC9BC,MAAOxB,EAAMwB,KAAK,CAClBC,MAAOzB,EAAMyB,KAAK,AACtB,EACAzB,EAAM0B,UAAU,CAAG,CAAA,CACvB,EAmFIf,YAxEJ,SAAqBX,CAAK,CAAEG,CAAK,EAC7B,IAAIc,EACJ,GAAIjB,EAAMqB,aAAa,EAAIrB,EAAM0B,UAAU,CAAE,CACzC,IACIzB,EAAQI,AADC,IAAI,CACEJ,KAAK,CACpBiB,EAAkB,AAAC,CAAA,AAAyB,OAAxBD,CAAAA,EAAKhB,EAAMkB,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGG,SAAS,CAACjB,EAAK,GAAMA,EACrGwB,EAAQ3B,EAAMqB,aAAa,CAACC,MAAM,CAAGJ,EAAgBI,MAAM,CAC3DM,EAAQ5B,EAAMqB,aAAa,CAACE,MAAM,CAAGL,EAAgBK,MAAM,CAC3DM,EAAqB5B,EAAM4B,kBAAkB,CAC7CC,EAAW,KAAK,EAChBC,EAAW,KAAK,EAEhBC,CAAAA,KAAKC,GAAG,CAACN,GAAS,GAAKK,KAAKC,GAAG,CAACL,GAAS,CAAA,IACzCE,EAAW9B,EAAMqB,aAAa,CAACG,KAAK,CAAGG,EACvCI,EAAW/B,EAAMqB,aAAa,CAACI,KAAK,CAAGG,EACnC3B,EAAMiC,YAAY,CAACJ,EAAUC,KAC7B/B,EAAMwB,KAAK,CAAGM,EACd9B,EAAMyB,KAAK,CAAGM,EACd/B,EAAMmC,UAAU,CAAG,CAAA,EACnB,IAAI,CAACC,UAAU,CAACpC,GAChB6B,EAAmBQ,OAAO,CAAC,SAAUC,CAAM,EACvCA,EAAOC,iBAAiB,EAC5B,IAGZ,CACJ,EA+CI1B,UAvCJ,SAAmBb,CAAK,EAChBA,EAAMqB,aAAa,GACfrB,EAAMmC,UAAU,GACZ,IAAI,CAACG,MAAM,CAACE,gBAAgB,CAC5B,IAAI,CAACF,MAAM,CAACG,KAAK,GAGjB,IAAI,CAACxC,KAAK,CAACyC,MAAM,IAGzB1C,EAAM0B,UAAU,CAAG1B,EAAMmC,UAAU,CAAG,CAAA,EACjC,IAAI,CAAC5B,OAAO,CAACoC,cAAc,EAC5B,OAAO3C,EAAMqB,aAAa,CAGtC,EAyBIe,WAjBJ,SAAoBpC,CAAK,EACjBA,GAAS,IAAI,CAAC4C,IAAI,EAClB,IAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CACXxE,EAAG2B,EAAM8C,QAAQ,CAAC,IAAI,CAACvC,OAAO,CAACwC,MAAM,CAACC,KAAK,CAACJ,IAAI,CAACK,IAAI,CACzD,EAER,CAYA,EAiBIC,EAAe,AAAC5D,IAA+E4D,YAAY,CAE3GC,EAAkC,AAAC7D,IAA+EG,QAAQ,CAE1H2D,EAAkC,AAAC9D,IAA+EI,QAAQ,CAAE2D,EAAoC,AAAC/D,IAA+EK,UAAU,CA4B9P,SAAS2D,IACD,IAAI,CAACzB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACQ,OAAO,CAAC,SAAUC,CAAM,EAE5CA,EAAOiB,gBAAgB,EAC3B,GACA,IAAI,CAACb,MAAM,GAEnB,CAKA,SAASc,IACD,IAAI,CAAC3B,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACQ,OAAO,CAAC,SAAUC,CAAM,EAC5CA,EAAOiB,gBAAgB,CAAC,CAAA,EAC5B,GACA,IAAI,CAACb,MAAM,GAEnB,CAKA,SAASe,IACD,IAAI,CAAC5B,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACQ,OAAO,CAAC,SAAUC,CAAM,EAC5CA,EAAOoB,IAAI,EACf,EAER,CAIA,SAASC,IACL,IAAIC,EACAC,EAAc,CAAA,EACdC,EAAa,SAAUxB,CAAM,EACrBA,EAAOyB,aAAa,IACpBC,SAAS1B,EAAO2B,WAAW,GAC3B,CAAC3B,EAAO4B,QAAQ,IAChB,CAAC5B,EAAOE,gBAAgB,GAMpBF,EAAO6B,UAAU,EACjB7B,EAAO6B,UAAU,GAEzB7B,EAAO8B,IAAI,GACXR,EAAgB,CAAA,EAChBC,EAAc,CAAA,EAEtB,EACA,GAAI,IAAI,CAAChC,kBAAkB,CAAE,CAMzB,IALAqB,EAAa,CAAA,EAAO,IAAI,EAExB,IAAI,CAACrB,kBAAkB,CAACQ,OAAO,CAAC,SAAUC,CAAM,EAAI,OAAOA,EAAOG,KAAK,EAAI,GAGpE,CAACmB,GACJA,EAAgB,CAAA,EAChB,IAAI,CAAC/B,kBAAkB,CAACQ,OAAO,CAACyB,GAEhCD,GACA,IAAI,CAACxD,MAAM,CAACgC,OAAO,CAAC,SAAUhC,CAAM,EAC5BA,GAAUA,EAAOiC,MAAM,EACvBjC,EAAOgE,MAAM,EAErB,EAER,CACJ,CAW6B,IAAIC,EALJ,CACzBvD,QA7FJ,SAAwCC,CAAU,EAC1CqC,EAAkCF,EAAiC,iBACnEC,EAAgCpC,EAAY,aAAcsC,GAC1DF,EAAgCpC,EAAY,cAAewC,GAC3DJ,EAAgCpC,EAAY,UAAWyC,GACvDL,EAAgCpC,EAAY,SAAU2C,GAE9D,EAuFIY,aAxGe,CAAC,EAyGhBC,QAxGU,CAAC,CAyGf,EAIIC,EAAmI3G,EAAoB,KACvJ4G,EAAuJ5G,EAAoBI,CAAC,CAACuG,GAS7KxD,EAAK,AAACyD,IAA2IrE,MAAM,CAAEsE,EAAc1D,EAAGjC,SAAS,CAAE4F,EAAa3D,EAAGjC,SAAS,CAAC6F,UAAU,CAAC7F,SAAS,CAEnO8F,EAAU,AAACxF,IAA+EwF,OAAO,CAAEC,EAAS,AAACzF,IAA+EyF,MAAM,CAAEC,EAAO,AAAC1F,IAA+E0F,IAAI,CAAEC,EAAQ,AAAC3F,IAA+E2F,KAAK,CAAEC,EAAO,AAAC5F,IAA+E4F,IAAI,EAO/d,AAAC,SAAUvH,CAAgB,EA+GvB,SAASwH,IAIL,OAFA,IAAI,CAACC,IAAI,CAAG,EAAE,CACTC,MAAM,CAAC,IAAI,CAACC,MAAM,EAAI,EAAE,CAAE,IAAI,CAACC,KAAK,EAClCZ,EAAYQ,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC3C,CAwDA,SAASC,IACD,IAAI,CAACH,KAAK,GACV,IAAI,CAACA,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAC7BA,EAAKR,OAAO,EAChB,GACA,IAAI,CAACI,KAAK,CAACK,MAAM,CAAG,GAExBjB,EAAYe,OAAO,CAACF,KAAK,CAAC,IAAI,CAAEC,UACpC,CAMA,SAASI,EAAaC,CAAK,EACvB,IAAIC,EAAON,UACPO,EAAS,IAAI,CAACC,MAAM,CAAG,IAAI,CAACC,OAAO,CAACb,MAAM,CAAC,IAAI,CAACc,SAAS,EACrD,CAAC,IAAI,CAACC,QAAQ,CAClB,IAAI,CAACC,MAAM,CAAC,AACF,CAAA,WAAVP,GACAE,EAAO3D,OAAO,CAAC,SAAUiE,CAAU,EAC3BA,GAAcA,EAAWjG,MAAM,GAC/BuE,EAAW2B,QAAQ,CAACf,KAAK,CAACc,EAAYP,GAClC,CAACO,EAAWL,MAAM,GACdK,EAAWF,QAAQ,CAACI,OAAO,EAC3B5B,EAAW2B,QAAQ,CAACf,KAAK,CAACc,EAAWF,QAAQ,CAAEL,GAE/CO,EAAWD,MAAM,EAAIC,EAAWD,MAAM,CAACG,OAAO,EAC9C5B,EAAW2B,QAAQ,CAACf,KAAK,CAACc,EAAWD,MAAM,CAAEN,IAI7D,GAEJnB,EAAW2B,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEO,EACpC,CAOA,SAASU,EAAWlG,CAAO,CAAEmC,CAAM,CAAEgE,CAAS,CAAEC,CAAQ,EACpD,IAAIC,EAAQ,IAAI,CACZrB,EAAQ,IAAI,CAAClF,MAAM,CAACE,OAAO,CAACgF,KAAK,CACjCH,EAAO,IAAI,CAAC/E,MAAM,CAACE,OAAO,CAAC6E,IAAI,CAC/ByB,EAAa,AAACzB,CAAAA,MAAAA,EAAmC,KAAK,EAAIA,EAAKQ,MAAM,AAAD,GAAM,EAC1EkB,EAAa1B,MAAAA,EAAmC,KAAK,EAAIA,CAAI,CAAC,IAAI,CAAC2B,KAAK,CAAC,CAG7E,GAFAnC,EAAWoC,MAAM,CAAC9H,IAAI,CAAC,IAAI,CAAEqB,EAAS,CAAA,IAAI,CAAC0F,MAAM,EAAWvD,EAC5DgE,EAAWC,GACP,IAAI,CAACV,MAAM,CAAE,CAEb,IAAIgB,EAAY,AAAC1B,CAAAA,GAAS,EAAE,AAAD,EAClB2B,MAAM,CACX,SAAUC,CAAS,CACnBjJ,CAAC,CACD6I,CAAK,EACD,OAAQH,EAAMQ,EAAE,GAAKlJ,EAAEkJ,EAAE,CAAGL,EAAQI,CAC5C,EAAG,IAGHE,EAAapC,EAAMM,GAASA,CAAK,CAAC0B,EAAU,EAAI,CAAC,EAAG,AAAC7B,CAAAA,MAAAA,EAAmC,KAAK,EAAIA,CAAI,CAAC,IAAI,CAAC2B,KAAK,CAAC,AAAD,GAAM,CAAC,GAEnH3B,IACI0B,EACA1B,CAAI,CAAC,IAAI,CAAC2B,KAAK,CAAC,CAAGD,EAInB1B,EAAKQ,MAAM,CAAGiB,GAIlBtB,EACI0B,GAAa,EACb1B,CAAK,CAAC0B,EAAU,CAAGI,EAGnB9B,EAAM+B,IAAI,CAACD,GAIf,IAAI,CAAChH,MAAM,CAACE,OAAO,CAACgF,KAAK,CAAG,CAAC8B,EAAW,CAExCnC,EAAKxC,EAAQ,CAAA,IACb,IAAI,CAACrC,MAAM,CAACJ,KAAK,CAACyC,MAAM,CAACgE,EAEjC,CACJ,CA5OA/I,EAAiBoD,OAAO,CAVxB,SAAiBwG,CAAU,CAAEC,CAAW,EACpC,IAAI5C,EAAa2C,EAAWvI,SAAS,CACjC2F,EAAc6C,EAAYxI,SAAS,CAMvC,OALA4F,EAAWiB,YAAY,CAAGA,EAC1BjB,EAAW2B,QAAQ,CAAGV,EACtBjB,EAAWoC,MAAM,CAAGP,EACpB9B,EAAYQ,OAAO,CAAGA,EACtBR,EAAYe,OAAO,CAAGA,EACf8B,CACX,EAmFA7J,EAAiB8J,UAAU,CA5E3B,SAAoBL,CAAE,EAClB,IAMI7G,EANAgH,EAAa,IAAI,CAAC1C,UAAU,CAC5B6C,EAAW,SAAUnC,CAAK,CAC1B6B,CAAE,EAAI,OAAOpC,EAAKO,EAClB,SAAUI,CAAI,EAAI,OAAOA,EAAKyB,EAAE,GAAKA,CAAI,EAAI,EAC7CzB,EAAO+B,EAAS,IAAI,CAACnC,KAAK,CAC1B6B,GAEJ,GAAI,CAACzB,EAAM,CACPpF,EAAU,IAAI,CAACA,OAAO,CAACgF,KAAK,EAAImC,EAAS,IAAI,CAACnH,OAAO,CAACgF,KAAK,CAAE6B,GAC7D,IAAIO,EAAY,IAAIJ,EAAW,IAAI,CAC/BxC,EAAO,CACH6C,UAAW,kBACX3B,OAAQ,CAAA,EACRmB,GAAIA,EACJS,EAAG,CACP,EACAtH,GACJoH,CAAAA,EAAUzB,OAAO,CAAG,EAAE,CACtByB,EAAUxB,SAAS,CAAG,EAAE,CAKxBwB,EAAUG,MAAM,CAAG,WACf,IAAIC,EAAQ,EACRC,EAAU,EAOd,OANAL,EAAUzB,OAAO,CAAC7D,OAAO,CAAC,SAAU4F,CAAI,EACpCF,GAASE,EAAKC,MAAM,EAAI,CAC5B,GACAP,EAAUxB,SAAS,CAAC9D,OAAO,CAAC,SAAU4F,CAAI,EACtCD,GAAWC,EAAKC,MAAM,EAAI,CAC9B,GACOlG,KAAKmG,GAAG,CAACJ,EAAOC,EAC3B,EAKAL,EAAUS,MAAM,CAAG,SAAUpI,CAAK,CAAEqI,CAAI,EAEpC,IAAK,IADDD,EAAS,EACJE,EAAI,EAAGA,EAAIX,CAAS,CAACU,EAAK,CAACzC,MAAM,CAAE0C,IAAK,CAC7C,GAAIX,CAAS,CAACU,EAAK,CAACC,EAAE,GAAKtI,EACvB,OAAOoI,EAEXA,GAAUT,CAAS,CAACU,EAAK,CAACC,EAAE,CAACJ,MAAM,AACvC,CACJ,EAGAP,EAAUY,QAAQ,CAAG,WACjB,IAAIC,EAAW,EAMf,OALAb,EAAUzB,OAAO,CAAC7D,OAAO,CAAC,SAAU4F,CAAI,EAChCA,EAAKO,QAAQ,EACbA,GAER,GACQ,CAACb,EAAUzB,OAAO,CAACN,MAAM,EAC7B4C,IAAab,EAAUzB,OAAO,CAACN,MAAM,AAC7C,EACA+B,EAAUZ,KAAK,CAAG,IAAI,CAACxB,KAAK,CAAC+B,IAAI,CAACK,GAAa,EAC/ChC,EAAOgC,CACX,CAYA,OAXAhC,EAAK8C,YAAY,CAAG,OAEpB9C,EAAK+C,IAAI,CAAG/C,EAAK+C,IAAI,EAAI/C,EAAKpF,OAAO,CAAC6G,EAAE,EAAI,GAE5CzB,EAAKgD,IAAI,CAAGzD,EAEZS,EAAKpF,OAAO,CAACoI,IAAI,CAAEhD,EAAKpF,OAAO,CAACqI,MAAM,EAAIjD,EAAKpF,OAAO,CAACqI,MAAM,CAACC,MAAM,CAEpE,IAAI,CAACtI,OAAO,CAACqI,MAAM,EAAI,IAAI,CAACrI,OAAO,CAACqI,MAAM,CAACC,MAAM,CAEjD,GACOlD,CACX,EAYAhI,EAAiBwH,OAAO,CAAGA,EAkD3BxH,EAAiBmL,cAAc,CA5C/B,WACI,IAAIlC,EAAQ,IAAI,CACZ3G,EAAQ,IAAI,CAACA,KAAK,CAClB8I,EAAa,CAAC,EAClBpE,EAAYmE,cAAc,CAAC5J,IAAI,CAAC,IAAI,EAC/B,IAAI,CAACqG,KAAK,EACX,CAAA,IAAI,CAACA,KAAK,CAAG,EAAE,AAAD,EAElB,IAAI,CAACyD,YAAY,CAAG,EAEpB,IAAI,CAACzD,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAC7BA,EAAKQ,SAAS,CAACP,MAAM,CAAG,EACxBD,EAAKO,OAAO,CAACN,MAAM,CAAG,EACtBD,EAAKsD,KAAK,CAAGtD,EAAKpF,OAAO,CAAC0I,KAAK,AACnC,GAEA,IAAI,CAAC3D,MAAM,CAACjD,OAAO,CAAC,SAAUrC,CAAK,EAC3B8E,EAAQ9E,EAAMkJ,IAAI,IACbH,CAAU,CAAC/I,EAAMkJ,IAAI,CAAC,EACvBH,CAAAA,CAAU,CAAC/I,EAAMkJ,IAAI,CAAC,CAAGtC,EAAMa,UAAU,CAACzH,EAAMkJ,IAAI,CAAA,EAExDH,CAAU,CAAC/I,EAAMkJ,IAAI,CAAC,CAAC/C,SAAS,CAACmB,IAAI,CAACtH,GACtCA,EAAMoG,QAAQ,CAAG2C,CAAU,CAAC/I,EAAMkJ,IAAI,CAAC,CAEnCjJ,EAAMkJ,UAAU,CAChBnJ,EAAMoJ,UAAU,CAAGlE,EAAKlF,EAAMO,OAAO,CAAC6I,UAAU,CAAEL,CAAU,CAAC/I,EAAMkJ,IAAI,CAAC,CAACE,UAAU,EAGnFpJ,EAAMqJ,KAAK,CACPrJ,EAAMO,OAAO,CAAC8I,KAAK,EAAIN,CAAU,CAAC/I,EAAMkJ,IAAI,CAAC,CAACG,KAAK,EAG3DvE,EAAQ9E,EAAMsJ,EAAE,IACXP,CAAU,CAAC/I,EAAMsJ,EAAE,CAAC,EACrBP,CAAAA,CAAU,CAAC/I,EAAMsJ,EAAE,CAAC,CAAG1C,EAAMa,UAAU,CAACzH,EAAMsJ,EAAE,CAAA,EAEpDP,CAAU,CAAC/I,EAAMsJ,EAAE,CAAC,CAACpD,OAAO,CAACoB,IAAI,CAACtH,GAClCA,EAAMqG,MAAM,CAAG0C,CAAU,CAAC/I,EAAMsJ,EAAE,CAAC,EAEvCtJ,EAAM0I,IAAI,CAAG1I,EAAM0I,IAAI,EAAI1I,EAAMoH,EAAE,AACvC,EAAG,IAAI,EAEP,IAAI,CAAC2B,UAAU,CAAGA,CACtB,EA0CApL,EAAiBkI,YAAY,CAAGA,EAqDhClI,EAAiB8I,UAAU,CAAGA,CAClC,EAAG9I,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,GAMf,IAAI4L,EAA2B5L,EAexD6L,GACI9L,EAAgB,SAAUW,CAAC,CAC3BoL,CAAC,EAMD,MAAO/L,AALHA,CAAAA,EAAgBgB,OAAOgL,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUvL,CAAC,CAC1DoL,CAAC,EAAIpL,EAAEsL,SAAS,CAAGF,CAAG,GACd,SAAUpL,CAAC,CACnBoL,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAExK,cAAc,CAAC4K,IAAIxL,CAAAA,CAAC,CAACwL,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCxL,EAAGoL,EAC5B,EACO,SAAUpL,CAAC,CAAEoL,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG1L,CAAG,CADtCX,EAAcW,EAAGoL,GAEjBpL,EAAEW,SAAS,CAAGyK,AAAM,OAANA,EAAa/K,OAAOsL,MAAM,CAACP,GAAMK,CAAAA,EAAG9K,SAAS,CAAGyK,EAAEzK,SAAS,CAAE,IAAI8K,CAAG,CACtF,GAIAG,EAAsB,AAACvF,IAA2IrE,MAAM,CAAE6J,EAAgCD,EAAoBjL,SAAS,CAAEmL,EAAQF,EAAoBjL,SAAS,CAAC6F,UAAU,CAEzRuF,EAA6B,AAAC9K,IAA+EI,QAAQ,CAAE2K,EAAM,AAAC/K,IAA+E+K,GAAG,CAAEC,EAA4B,AAAChL,IAA+EwF,OAAO,CAAEyF,EAA2B,AAACjL,IAA+EyF,MAAM,CAAEyF,EAAyB,AAAClL,IAA+E4F,IAAI,CAMviBuF,EAAmC,SAAUC,CAAM,EAOnD,SAASD,EAAkBpK,CAAM,CAAEE,CAAO,CAAEoK,CAAC,EACzC,IAAI/D,EAAQ8D,EAAOxL,IAAI,CAAC,IAAI,CACxBmB,EACAE,EACAoK,IAAM,IAAI,CAUd,OATI/D,EAAMvG,MAAM,CAACE,OAAO,CAACC,SAAS,EAC9B,CAACoG,EAAMvG,MAAM,CAACJ,KAAK,CAACkJ,UAAU,GAC9BiB,EAA2BxD,EAAO,YAAa,WAC3CyD,EAAI,IAAI,CAAChK,MAAM,CAACJ,KAAK,CAACC,SAAS,CAAE,CAAE0K,OAAQ,MAAO,EACtD,GACAR,EAA2BxD,EAAO,WAAY,WAC1CyD,EAAI,IAAI,CAAChK,MAAM,CAACJ,KAAK,CAACC,SAAS,CAAE,CAAE0K,OAAQ,SAAU,EACzD,IAEGhE,CACX,CAqOA,OA1PA4C,EAAUiB,EAAmBC,GAgC7BD,EAAkBzL,SAAS,CAACmG,OAAO,CAAG,WAWlC,OAVI,IAAI,CAACc,MAAM,EACX,IAAI,CAACE,SAAS,CAACd,MAAM,CAAC,IAAI,CAACa,OAAO,EAAE7D,OAAO,CAAC,SAAU4F,CAAI,EAGlDA,EAAK4C,eAAe,EACpB5C,EAAK4C,eAAe,EAE5B,GAEJ,IAAI,CAACxK,MAAM,CAACiC,MAAM,CAACwI,2BAA2B,CAAC,IAAI,CAAE,IAAI,CAACzK,MAAM,CAACiC,MAAM,CAAC,IAAI,CAAC2D,MAAM,CAAG,QAAU,QAAQ,EACjGkE,EAAMnL,SAAS,CAACmG,OAAO,CAACK,KAAK,CAAC,IAAI,CAAEC,UAC/C,EAMAgF,EAAkBzL,SAAS,CAAC+L,SAAS,CAAG,WACpC,IAAIC,EAAM,IAAI,CAAC/E,MAAM,CACb,IAAI,CAACE,SAAS,CAACP,MAAM,CAAG,IAAI,CAACM,OAAO,CAACN,MAAM,CAC3C,EACR,OAAOoF,AAAQ,IAARA,EAAY,EAAIA,CAC3B,EAKAP,EAAkBzL,SAAS,CAACiM,iBAAiB,CAAG,WAC5C,IAAIC,EAAc,IAAI,CAAC7K,MAAM,CAACE,OAAO,CAAC0H,IAAI,CACtCkD,EAAe,IAAI,CAAC5K,OAAO,CAC/B,MAAO,CACH,eAAgBiK,EAAuBW,EAAaC,KAAK,CAAEF,EAAYE,KAAK,EAC5EC,OAASF,EAAa9B,KAAK,EAAI6B,EAAY7B,KAAK,CAChDiC,UAAYH,EAAaI,SAAS,EAAIL,EAAYK,SAAS,CAC3DC,QAAShB,EAAuBW,EAAaK,OAAO,CAAEN,EAAYM,OAAO,CAAE,EAC/E,CACJ,EAOAf,EAAkBzL,SAAS,CAACyM,WAAW,CAAG,WACtC,IAAIC,EAAO,IAAI,CAACtF,QAAQ,CACpBuF,EAAQ,IAAI,CAACtF,MAAM,CAOvB,OAJIqF,EAAKlK,KAAK,CAAGmK,EAAMnK,KAAK,GACxBkK,EAAO,IAAI,CAACrF,MAAM,CAClBsF,EAAQ,IAAI,CAACvF,QAAQ,EAElB,CACH,CAAC,IAAKsF,EAAKlK,KAAK,EAAI,EAAGkK,EAAKjK,KAAK,EAAI,EAAE,CACvC,CAAC,IAAKkK,EAAMnK,KAAK,EAAI,EAAGmK,EAAMlK,KAAK,EAAI,EAAE,CAC5C,AAaL,EASAgJ,EAAkBzL,SAAS,CAAC4M,OAAO,CAAG,WAClC,IAAIC,EAAK,IAAI,CAACzF,QAAQ,CAACuC,IAAI,CACvBmD,EAAK,IAAI,CAACzF,MAAM,CAACsC,IAAI,CACrBoD,EAAMF,EAAKC,EACf,MAAO,CACH1F,SAAU,EAAIyF,EAAKE,EACnB1F,OAAQ,EAAIyF,EAAKC,CACrB,CACJ,EAIAtB,EAAkBzL,SAAS,CAACgN,OAAO,CAAG,WAClC,MAAO,CAAC,IAAI,CAAC/F,MAAM,EAAIqE,EAA0B,IAAI,CAAClD,EAAE,CAC5D,EAKAqD,EAAkBzL,SAAS,CAACiN,UAAU,CAAG,WACrC,IACIC,EADAC,EAAO,IAAI,CAACV,WAAW,GAE3B,GAAI,IAAI,CAACjF,OAAO,CAAE,CACd,IAAI,CAAC4F,SAAS,CAAG,CACb/N,EAAG8N,CACP,EACK,IAAI,CAAC9L,MAAM,CAACJ,KAAK,CAACkJ,UAAU,GAC7B+C,EAAU,IAAI,CAAC7L,MAAM,CAACgM,YAAY,CAAC,IAAI,EACvC,IAAI,CAAC7F,OAAO,CAAC3D,IAAI,CAACqJ,GAClB,AAAC,CAAA,IAAI,CAACI,UAAU,EAAI,EAAE,AAAD,EAAGjK,OAAO,CAAC,SAAUkK,CAAK,EACvCA,GACAA,EAAM1J,IAAI,CAAC,CACP2I,QAASU,EAAQV,OAAO,AAC5B,EAER,IAEJ,IAAI,CAAChF,OAAO,CAACgG,OAAO,CAAC,IAAI,CAACJ,SAAS,EAEnC,IAAI3J,EAAQ0J,CAAI,CAAC,EAAE,CACfM,EAAMN,CAAI,CAAC,EAAE,AACA,CAAA,MAAb1J,CAAK,CAAC,EAAE,EAAYgK,AAAW,MAAXA,CAAG,CAAC,EAAE,GAC1B,IAAI,CAACjL,KAAK,CAAG,AAACiB,CAAAA,CAAK,CAAC,EAAE,CAAGgK,CAAG,CAAC,EAAE,AAAD,EAAK,EACnC,IAAI,CAAChL,KAAK,CAAG,AAACgB,CAAAA,CAAK,CAAC,EAAE,CAAGgK,CAAG,CAAC,EAAE,AAAD,EAAK,EAE3C,CACJ,EAeAhC,EAAkBzL,SAAS,CAAC0N,MAAM,CAAG,SAAUhK,CAAM,CAAEgE,CAAS,EAC5D,IAGIK,EAFA1G,EAASL,AADD,IAAI,CACGK,MAAM,CACrBsM,EAAetM,EAAOE,OAAO,CAACgF,KAAK,EAAI,EAAE,CAEzC+C,EAAIqE,EAAa/G,MAAM,CAE3B,GAAI5F,AANQ,IAAI,CAMNiG,MAAM,CAAE,CA4Bd,IAzBA5F,EAAOiF,MAAM,CAAG,EAAE,CAElB,EAAE,CACGD,MAAM,CAACrF,AAZJ,IAAI,CAYMmG,SAAS,EACtBd,MAAM,CAACrF,AAbJ,IAAI,CAaMkG,OAAO,EACpB7D,OAAO,CAAC,SAAUuK,CAAU,EAE7B7F,CAAAA,EAAQ6F,EAAWxG,QAAQ,CAACD,SAAS,CAAC0G,OAAO,CAACD,EAAU,EAC5C,IACRA,EAAWxG,QAAQ,CAACD,SAAS,CAAC2G,MAAM,CAAC/F,EAAO,GAGhDA,CAAAA,EAAQ6F,EAAWvG,MAAM,CAACH,OAAO,CAAC2G,OAAO,CAACD,EAAU,EACxC,IACRA,EAAWvG,MAAM,CAACH,OAAO,CAAC4G,MAAM,CAAC/F,EAAO,GAG5CmD,EAA8B6C,WAAW,CAAC7N,IAAI,CAACmB,EAAQA,EAAO+E,IAAI,CAACyH,OAAO,CAACD,GAAa,CAAA,EAAO,CAAA,EACnG,GAEAvM,EAAOiF,MAAM,CAAGjF,EAAO+E,IAAI,CAAC4H,KAAK,GAGjC3M,EAAOkF,KAAK,CAACuH,MAAM,CAACzM,EAAOkF,KAAK,CAACsH,OAAO,CAhChC,IAAI,EAgCqC,GAE1CvE,KACH,GAAIqE,CAAY,CAACrE,EAAE,CAAClB,EAAE,GAAKpH,AAnCvB,IAAI,CAmCyBO,OAAO,CAAC6G,EAAE,CAAE,CACzC/G,EAAOE,OAAO,CAACgF,KAAK,CAACuH,MAAM,CAACxE,EAAG,GAC/B,KACJ,CAtCI,IAAI,EAyCRtI,AAzCI,IAAI,CAyCFmF,OAAO,GAGjB9E,EAAO4M,OAAO,CAAG,CAAA,EACjB5M,EAAO6M,WAAW,CAAG,CAAA,EACjBxK,GACArC,EAAOJ,KAAK,CAACyC,MAAM,CAACA,EAE5B,MAEIrC,EAAO0M,WAAW,CAAC1M,EAAO+E,IAAI,CAACyH,OAAO,CAnD9B,IAAI,EAmDmCnK,EAAQgE,EAE/D,EAKA+D,EAAkBzL,SAAS,CAACmO,UAAU,CAAG,WACrC,IAAIjB,CACC,CAAA,IAAI,CAAC1F,OAAO,GACb,IAAI,CAACA,OAAO,CAAG,IAAI,CAACnG,MAAM,CAACJ,KAAK,CAACmN,QAAQ,CACpCjB,IAAI,CAAC,IAAI,CAACV,WAAW,IACrB4B,QAAQ,CAAC,IAAI,CAACC,YAAY,GAAI,CAAA,GAC9BC,GAAG,CAAC,IAAI,CAAClN,MAAM,CAACmN,KAAK,EACrB,IAAI,CAACnN,MAAM,CAACJ,KAAK,CAACkJ,UAAU,GAC7B+C,EAAU,IAAI,CAAC7L,MAAM,CAACgM,YAAY,CAAC,IAAI,EACvC,IAAI,CAAC7F,OAAO,CAAC3D,IAAI,CAACqJ,GAClB,AAAC,CAAA,IAAI,CAACI,UAAU,EAAI,EAAE,AAAD,EAAGjK,OAAO,CAAC,SAAUkK,CAAK,EACvCA,GACAA,EAAM1J,IAAI,CAAC,CACP2I,QAASU,EAAQV,OAAO,AAC5B,EAER,IAGZ,EACOf,CACX,EAAEN,GACFI,EAAyBE,EAAkBzL,SAAS,CAAE,CAClDuH,SAAUgD,EAAwB1D,YAAY,AAClD,GA4Z6B,IAAI4H,EA7WA,CAC7BC,eAAgB,CAAA,EAMhBC,oBAAqB,CAAA,EACrB/E,OAAQ,CACJgF,QAAS,CAAA,EACT7K,OAAQ,CAOJ8K,SAAU,CAINrC,QAAS,GAMT9E,UAAW,CAEPoH,SAAU,EACd,CACJ,CACJ,CACJ,EACA/K,OAAQ,CAOJ8K,SAAU,CAINE,YAAa,GAMbrH,UAAW,CAEPoH,SAAU,EACd,CACJ,CACJ,EAeAxB,WAAY,CAoBR0B,UAAW,WACP,IAAI/M,EACJ,OAAOgN,OAAO,AAAoB,OAAnBhN,CAAAA,EAAK,IAAI,CAACzC,GAAG,AAAD,GAAeyC,AAAO,KAAK,IAAZA,EAAgBA,EAAK,GACnE,EAmBAiN,cAAe,WACX,OAAQ,IAAI,CAAC9H,QAAQ,CAACsC,IAAI,CACtB,OACA,IAAI,CAACrC,MAAM,CAACqC,IAAI,AACxB,EAYAyF,aAAc,CACVP,QAAS,CAAA,CACb,EACAQ,SAAU,CACNR,QAAS,CAAA,CACb,EACAS,MAAO,CACHC,WAAY,gBAChB,EACAC,MAAO,CAAA,EACP7H,UAAW,CACP6H,MAAO,GACX,CACJ,EAKAtG,KAAM,CAiBFoB,MAAO,2BAIP+B,MAAO,CACX,EAKA5K,UAAW,CAAA,EACXgO,gBAAiB,CAiEbC,iBAAkB,SAUlBC,sBAAuB,EASvBlM,iBAAkB,CAAA,EAalBmM,MAAO,GAcPC,SAAU,GAqBVC,cAAe,OAOfC,KAAM,uBAsBNC,YAAa,QAObhL,cAAe,IAQfiL,sBAAuB,MAKvBC,SAAU,KACd,EACAC,aAAc,CAAA,CAClB,EAiXiCC,EATV,CACnBC,WAhKJ,SAAoBnH,CAAI,CAAEoH,CAAK,CAAEC,CAAU,CAAEC,CAAS,EAClD,IAAIC,EAAavH,EAAK2D,OAAO,GAAI6D,EAAc,AAACH,EAAW3E,CAAC,CAAG4E,EAAaF,EAAOK,EAAc,AAACJ,EAAWzH,CAAC,CAAG0H,EAAaF,CACzHpH,CAAAA,EAAK7B,QAAQ,CAAC/E,aAAa,GAC5B4G,EAAK7B,QAAQ,CAACuJ,KAAK,EACfF,EAAcD,EAAWpJ,QAAQ,CAAG6B,EAAK7B,QAAQ,CAACwJ,MAAM,CAC5D3H,EAAK7B,QAAQ,CAACyJ,KAAK,EACfH,EAAcF,EAAWpJ,QAAQ,CAAG6B,EAAK7B,QAAQ,CAACwJ,MAAM,EAE3D3H,EAAK5B,MAAM,CAAChF,aAAa,GAC1B4G,EAAK5B,MAAM,CAACsJ,KAAK,EACbF,EAAcD,EAAWnJ,MAAM,CAAG4B,EAAK5B,MAAM,CAACuJ,MAAM,CACxD3H,EAAK5B,MAAM,CAACwJ,KAAK,EACbH,EAAcF,EAAWnJ,MAAM,CAAG4B,EAAK5B,MAAM,CAACuJ,MAAM,CAEhE,EAmJIE,wBApIJ,SAAiCzR,CAAC,CAAE0R,CAAC,EACjC,OAAO1R,EAAIA,EAAI0R,CACnB,EAmIIC,WAzHJ,WACI,IAAIhB,EAAwB,IAAI,CAACzO,OAAO,CAACyO,qBAAqB,CAC1DiB,EAAU,IAAI,CAACD,UAAU,CAACC,OAAO,CACjCC,EAAU,IAAI,CAACF,UAAU,CAACE,OAAO,CACrC,IAAI,CAAC3K,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAC7B,GAAI,CAACA,EAAKtE,aAAa,CAAE,CACrB,IAAIuO,EAASjK,EAAKoF,SAAS,GACvBoF,EAAMP,EAAU,CAAA,EAAIA,EAAS,CAAA,CACjCjK,CAAAA,EAAKgK,KAAK,EAAK,AAACM,CAAAA,EAAUtK,EAAKnE,KAAK,AAAD,EAC/BwN,EACAmB,EAAMxK,EAAKiK,MAAM,CACrBjK,EAAKkK,KAAK,EAAK,AAACK,CAAAA,EAAUvK,EAAKlE,KAAK,AAAD,EAC/BuN,EACAmB,EAAMxK,EAAKiK,MAAM,AACzB,CACJ,EACJ,EA0GIQ,KApGJ,SAAc9N,CAAM,EAChB,OAAON,KAAKqO,GAAG,CAAC/N,EAAOgO,GAAG,CAAClF,KAAK,CAAG9I,EAAOgO,GAAG,CAACC,MAAM,CAAGjO,EAAOiD,KAAK,CAACK,MAAM,CAAE,GAChF,EAmGI4K,UAlEJ,SAAmBlO,CAAM,CAAEqD,CAAI,EAC3BA,EAAKgK,KAAK,EACNhK,EAAKgK,KAAK,CAAGrN,EAAO/B,OAAO,CAAC0O,QAAQ,CACxCtJ,EAAKkK,KAAK,EACNlK,EAAKkK,KAAK,CAAGvN,EAAO/B,OAAO,CAAC0O,QAAQ,CACxC,IAAIM,EAAY5J,EAAK1B,WAAW,CAAG3B,EAAOmO,YAAY,CAAC,CAC/C9F,EAAGhF,EAAKgK,KAAK,CACb9H,EAAGlC,EAAKkK,KAAK,AACjB,EACc,CAAA,IAAdN,IACA5J,EAAKnE,KAAK,EAAKmE,EAAKgK,KAAK,CAAGJ,EACxBvN,KAAK0O,GAAG,CAAC1O,KAAKC,GAAG,CAAC0D,EAAKgK,KAAK,EAAGrN,EAAO2B,WAAW,EACrD0B,EAAKlE,KAAK,EAAKkE,EAAKkK,KAAK,CAAGN,EACxBvN,KAAK0O,GAAG,CAAC1O,KAAKC,GAAG,CAAC0D,EAAKkK,KAAK,EAAGvN,EAAO2B,WAAW,EAE7D,EAoDI0M,UAxCJ,SAAmBhL,CAAI,CAAE0J,CAAK,CAAEC,CAAU,CAAEC,CAAS,EACjD5J,EAAKgK,KAAK,EACN,AAACL,EAAW3E,CAAC,CAAG4E,EAAaF,EAAQ1J,EAAKiK,MAAM,CACpDjK,EAAKkK,KAAK,EACN,AAACP,EAAWzH,CAAC,CAAG0H,EAAaF,EAAQ1J,EAAKiK,MAAM,AACxD,EAoCIgB,uBAfJ,SAAgCvS,CAAC,CAAE0R,CAAC,EAChC,OAAOA,EAAIA,EAAI1R,CACnB,CAcA,EAgCIwS,EAA8B,WAM9B,SAASA,EAAaP,CAAG,EAarB,IAAI,CAACQ,IAAI,CAAG,CAAA,EASZ,IAAI,CAACC,OAAO,CAAG,CAAA,EASf,IAAI,CAACC,UAAU,CAAG,CAAA,EASlB,IAAI,CAACzL,KAAK,CAAG,EAAE,CAOf,IAAI,CAAC+K,GAAG,CAAGA,EAOX,IAAI,CAACW,OAAO,CAAGjP,KAAK0O,GAAG,CAACJ,EAAIlF,KAAK,CAAEkF,EAAIC,MAAM,CACjD,CAgLA,OAzJAM,EAAa7R,SAAS,CAACkS,SAAS,CAAG,WAC/B,IAAIC,EAAY,IAAI,CAACb,GAAG,CAAClF,KAAK,CAAG,EAAGgG,EAAa,IAAI,CAACd,GAAG,CAACC,MAAM,CAAG,CAEnE,CAAA,IAAI,CAAChL,KAAK,CAAC,EAAE,CAAG,IAAIsL,EAAa,CAC7BnF,KAAM,IAAI,CAAC4E,GAAG,CAAC5E,IAAI,CACnB2F,IAAK,IAAI,CAACf,GAAG,CAACe,GAAG,CACjBjG,MAAO+F,EACPZ,OAAQa,CACZ,GAEA,IAAI,CAAC7L,KAAK,CAAC,EAAE,CAAG,IAAIsL,EAAa,CAC7BnF,KAAM,IAAI,CAAC4E,GAAG,CAAC5E,IAAI,CAAGyF,EACtBE,IAAK,IAAI,CAACf,GAAG,CAACe,GAAG,CACjBjG,MAAO+F,EACPZ,OAAQa,CACZ,GAEA,IAAI,CAAC7L,KAAK,CAAC,EAAE,CAAG,IAAIsL,EAAa,CAC7BnF,KAAM,IAAI,CAAC4E,GAAG,CAAC5E,IAAI,CAAGyF,EACtBE,IAAK,IAAI,CAACf,GAAG,CAACe,GAAG,CAAGD,EACpBhG,MAAO+F,EACPZ,OAAQa,CACZ,GAEA,IAAI,CAAC7L,KAAK,CAAC,EAAE,CAAG,IAAIsL,EAAa,CAC7BnF,KAAM,IAAI,CAAC4E,GAAG,CAAC5E,IAAI,CACnB2F,IAAK,IAAI,CAACf,GAAG,CAACe,GAAG,CAAGD,EACpBhG,MAAO+F,EACPZ,OAAQa,CACZ,EACJ,EAMAP,EAAa7R,SAAS,CAACsS,cAAc,CAAG,SAAUtR,CAAK,EACnD,IAAI0L,EAAO1L,EAAMwB,KAAK,CAAG,IAAI,CAAC8O,GAAG,CAAC5E,IAAI,CAAG,IAAI,CAAC4E,GAAG,CAAClF,KAAK,CAAG,EAAGiG,EAAMrR,EAAMyB,KAAK,CAAG,IAAI,CAAC6O,GAAG,CAACe,GAAG,CAAG,IAAI,CAACf,GAAG,CAACC,MAAM,CAAG,EAsBlH,OApBI7E,EAOY,GANR2F,EAUAA,EAEQ,EAIA,CAIpB,EAUAR,EAAa7R,SAAS,CAACuS,MAAM,CAAG,SAAUvR,CAAK,CAAEwR,CAAK,EAClD,IAAIC,CACA,CAAA,IAAI,CAACT,UAAU,CAEf,IAAI,CAACzL,KAAK,CAAC,IAAI,CAAC+L,cAAc,CAACtR,GAAO,CAACuR,MAAM,CAACvR,EAAOwR,EAAQ,IAG7D,IAAI,CAACT,OAAO,CAAG,CAAA,EACV,IAAI,CAACD,IAAI,CAMNU,GAEA,IAAI,CAACR,UAAU,CAAG,CAAA,EAClB,IAAI,CAACE,SAAS,GAEI,CAAA,IAAd,IAAI,CAACJ,IAAI,GACT,IAAI,CAACvL,KAAK,CAAC,IAAI,CAAC+L,cAAc,CAAC,IAAI,CAACR,IAAI,EAAE,CACrCS,MAAM,CAAC,IAAI,CAACT,IAAI,CAAEU,EAAQ,GAC/B,IAAI,CAACV,IAAI,CAAG,CAAA,GAGhB,IAAI,CAACvL,KAAK,CAAC,IAAI,CAAC+L,cAAc,CAACtR,GAAO,CACjCuR,MAAM,CAACvR,EAAOwR,EAAQ,KAkB3BC,AAPAA,CAAAA,EAAkB,IAAIZ,EAAa,CAC/BQ,IAAKrR,EAAMwB,KAAK,EAAIkQ,IACpBhG,KAAM1L,EAAMyB,KAAK,EAAIiQ,IAErBtG,MAAO,GACPmF,OAAQ,EACZ,EAAC,EACeO,IAAI,CAAG9Q,EACvByR,EAAgBT,UAAU,CAAG,CAAA,EAC7B,IAAI,CAACzL,KAAK,CAAC+B,IAAI,CAACmK,KApCpB,IAAI,CAACT,UAAU,CAAG,CAAA,EAClB,IAAI,CAACF,IAAI,CAAG9Q,GAuCxB,EAKA6Q,EAAa7R,SAAS,CAAC2S,mBAAmB,CAAG,WACzC,IAAIhJ,EAAO,EACPnH,EAAQ,EACRC,EAAQ,EACZ,GAAI,IAAI,CAACuP,UAAU,CAAE,CAEjB,IAAK,IAAIY,EAAK,EAAG3Q,EAAK,IAAI,CAACsE,KAAK,CAAEqM,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CACpD,IAAIC,EAAY5Q,CAAE,CAAC2Q,EAAG,AACjBC,CAAAA,EAAUd,OAAO,GAClBpI,GAAQkJ,EAAUlJ,IAAI,CACtBnH,GAASqQ,EAAUrQ,KAAK,CAAGqQ,EAAUlJ,IAAI,CACzClH,GAASoQ,EAAUpQ,KAAK,CAAGoQ,EAAUlJ,IAAI,CAEjD,CACAnH,GAASmH,EACTlH,GAASkH,CACb,MACS,IAAI,CAACmI,IAAI,GAEdnI,EAAO,IAAI,CAACmI,IAAI,CAACnI,IAAI,CACrBnH,EAAQ,IAAI,CAACsP,IAAI,CAACtP,KAAK,CACvBC,EAAQ,IAAI,CAACqP,IAAI,CAACrP,KAAK,CAG3B,CAAA,IAAI,CAACkH,IAAI,CAAGA,EACZ,IAAI,CAACnH,KAAK,CAAGA,EACb,IAAI,CAACC,KAAK,CAAGA,CACjB,EACOoP,CACX,IA4CIiB,EAA0B,WAM1B,SAASA,EAASnH,CAAC,CAAE9C,CAAC,CAAEuD,CAAK,CAAEmF,CAAM,EAEjC,IAAI,CAACD,GAAG,CAAG,CACP5E,KAAMf,EACN0G,IAAKxJ,EACLuD,MAAOA,EACPmF,OAAQA,CACZ,EACA,IAAI,CAACwB,QAAQ,CAAG,GAChB,IAAI,CAAC/U,IAAI,CAAG,IArD0C6T,EAqDZ,IAAI,CAACP,GAAG,EAClD,IAAI,CAACtT,IAAI,CAACgU,UAAU,CAAG,CAAA,EACvB,IAAI,CAAChU,IAAI,CAACgV,MAAM,CAAG,CAAA,EACnB,IAAI,CAAChV,IAAI,CAACkU,SAAS,EACvB,CAoFA,OA3EAY,EAAS9S,SAAS,CAACiT,sBAAsB,CAAG,WACxC,IAAI,CAACC,kBAAkB,CAAC,KAAM,KAAM,SAAUvM,CAAI,EAC9CA,EAAKgM,mBAAmB,EAC5B,EACJ,EAOAG,EAAS9S,SAAS,CAACmT,WAAW,CAAG,SAAU7M,CAAM,EAC7C,IAAK,IAAIsM,EAAK,EAAsBA,EAAKQ,AAAb9M,EAAsBM,MAAM,CAAEgM,IAAM,CAC5D,IAAI5R,EAAQoS,AADY9M,CACJ,CAACsM,EAAG,CACxB,IAAI,CAAC5U,IAAI,CAACuU,MAAM,CAACvR,EAAO,IAAI,CAAC+R,QAAQ,CACzC,CACJ,EAwBAD,EAAS9S,SAAS,CAACkT,kBAAkB,CAAG,SAAUvM,CAAI,CAAE0M,CAAc,CAAEC,CAAa,EACjF,IAAIC,EAOJ,GANK5M,GACDA,CAAAA,EAAO,IAAI,CAAC3I,IAAI,AAAD,EAEf2I,IAAS,IAAI,CAAC3I,IAAI,EAAIqV,GACtBE,CAAAA,EAAYF,EAAe1M,EAAI,EAE/B4M,AAAc,CAAA,IAAdA,GAGJ,IAAK,IAAIX,EAAK,EAAG3Q,EAAK0E,EAAKJ,KAAK,CAAEqM,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CACpD,IAAIY,EAASvR,CAAE,CAAC2Q,EAAG,CACnB,GAAIY,EAAOxB,UAAU,CAAE,CAInB,GAHIqB,GACAE,CAAAA,EAAYF,EAAeG,EAAM,EAEjCD,AAAc,CAAA,IAAdA,EACA,SAEJ,IAAI,CAACL,kBAAkB,CAACM,EAAQH,EAAgBC,EACpD,MACSE,EAAO1B,IAAI,EACZuB,GACAA,EAAeG,EAAO1B,IAAI,EAG9BwB,GACAA,EAAcE,EAEtB,CACI7M,IAAS,IAAI,CAAC3I,IAAI,EAAIsV,GACtBA,EAAc3M,GAEtB,EACOmM,CACX,IAyMiCW,EATT,CACpBrD,WAzJJ,SAAsCnH,CAAI,CAAEoH,CAAK,CAAEC,CAAU,EACzD,IAAIE,EAAavH,EAAK2D,OAAO,GACzB6D,EAAc,CAACH,EAAW3E,CAAC,CAAG0E,EAAQ,IAAI,CAACqD,eAAe,CAC1DhD,EAAc,CAACJ,EAAWzH,CAAC,CAAGwH,EAAQ,IAAI,CAACqD,eAAe,AACzDzK,CAAAA,EAAK7B,QAAQ,CAAC/E,aAAa,GAC5B4G,EAAK7B,QAAQ,CAAC5E,KAAK,EACfiO,EAAcD,EAAWpJ,QAAQ,CAAG6B,EAAK7B,QAAQ,CAACwJ,MAAM,CAC5D3H,EAAK7B,QAAQ,CAAC3E,KAAK,EACfiO,EAAcF,EAAWpJ,QAAQ,CAAG6B,EAAK7B,QAAQ,CAACwJ,MAAM,EAE3D3H,EAAK5B,MAAM,CAAChF,aAAa,GAC1B4G,EAAK5B,MAAM,CAAC7E,KAAK,EACbiO,EAAcD,EAAWnJ,MAAM,CAAG4B,EAAK5B,MAAM,CAACuJ,MAAM,CACxD3H,EAAK5B,MAAM,CAAC5E,KAAK,EACbiO,EAAcF,EAAWnJ,MAAM,CAAG4B,EAAK5B,MAAM,CAACuJ,MAAM,CAEhE,EA0IIE,wBAhIJ,SAAmDzR,CAAC,CAAE0R,CAAC,EAEnD,MAAO,AAACA,CAAAA,EAAI1R,CAAAA,EAAKA,CACrB,EA8HI2R,WApHJ,WACI,IAAIhB,EAAwB,IAAI,CAACzO,OAAO,CAACyO,qBAAqB,EAAI,EAC9DiB,EAAU,AAAC,CAAA,IAAI,CAACD,UAAU,CAACC,OAAO,CAC9B,AAAC,CAAA,IAAI,CAACK,GAAG,CAAC5E,IAAI,CAAG,IAAI,CAAC4E,GAAG,CAAClF,KAAK,AAAD,EAAK,CAAA,EAAK4D,EAC5CkB,EAAU,AAAC,CAAA,IAAI,CAACF,UAAU,CAACE,OAAO,CAC9B,AAAC,CAAA,IAAI,CAACI,GAAG,CAACe,GAAG,CAAG,IAAI,CAACf,GAAG,CAACC,MAAM,AAAD,EAAK,CAAA,EAAKvB,EAChD,IAAI,CAACzJ,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EACxBA,EAAKtE,aAAa,GACnBsE,EAAKnE,KAAK,EACNyO,EAAUtK,EAAKgD,IAAI,CAAGhD,EAAKiK,MAAM,CACrCjK,EAAKlE,KAAK,EACNyO,EAAUvK,EAAKgD,IAAI,CAAGhD,EAAKiK,MAAM,CAE7C,EACJ,EAuGIQ,KAjGJ,SAAgC9N,CAAM,EAClC,OAAON,KAAKqO,GAAG,CAAC/N,EAAOgO,GAAG,CAAClF,KAAK,CAAG9I,EAAOgO,GAAG,CAACC,MAAM,CAAGjO,EAAOiD,KAAK,CAACK,MAAM,CAAE,GAChF,EAgGI4K,UAhEJ,SAAqClO,CAAM,CAAEqD,CAAI,EAC7C,IAAIsJ,EAAW,CAAC3M,EAAO/B,OAAO,CAAC0O,QAAQ,CAAEL,EAAWtM,EAAO/B,OAAO,CAACqO,QAAQ,CAAE+D,EAAQhN,EAAKgN,KAAK,CAAEC,EAAQjN,EAAKiN,KAAK,CAE/GC,EAAa,AAAClN,CAAAA,EAAKnE,KAAK,CAAGmE,EAAKgK,KAAK,CACjCgD,CAAI,EAAK1D,EAAW6D,EAAa,AAACnN,CAAAA,EAAKlE,KAAK,CAAGkE,EAAKkK,KAAK,CACzD+C,CAAI,EAAK3D,EAAWhN,EAAMD,KAAKC,GAAG,CAAE8Q,EAAQ9Q,EAAI4Q,GAAcA,CAAAA,GAAa,CAAA,EAC/EG,EAAQ/Q,EAAI6Q,GAAcA,CAAAA,GAAa,CAAA,EAEvCnR,EAAQoR,EAAQ/Q,KAAK0O,GAAG,CAAC9B,EAAU5M,KAAKC,GAAG,CAAC4Q,IAAajR,EAAQoR,EAAQhR,KAAK0O,GAAG,CAAC9B,EAAU5M,KAAKC,GAAG,CAAC6Q,GAEzGnN,CAAAA,EAAKgN,KAAK,CAAGhN,EAAKnE,KAAK,CAAGmE,EAAKgK,KAAK,CACpChK,EAAKiN,KAAK,CAAGjN,EAAKlE,KAAK,CAAGkE,EAAKkK,KAAK,CAEpClK,EAAKnE,KAAK,EAAIG,EACdgE,EAAKlE,KAAK,EAAIG,EACd+D,EAAK1B,WAAW,CAAG3B,EAAOmO,YAAY,CAAC,CACnC9F,EAAGhJ,EACHkG,EAAGjG,CACP,EACJ,EA8CI+O,UA/BJ,SAAqChL,CAAI,CAAE0J,CAAK,CAAEC,CAAU,EACxD,IAAI2D,EAAS5D,EAAQ,IAAI,CAACqD,eAAe,CAAG/M,EAAKgD,IAAI,CAAGhD,EAAKiK,MAAM,AAC9DjK,CAAAA,EAAKtE,aAAa,GACnBsE,EAAKnE,KAAK,EAAI8N,EAAW3E,CAAC,CAAGsI,EAC7BtN,EAAKlE,KAAK,EAAI6N,EAAWzH,CAAC,CAAGoL,EAErC,EA0BIrC,uBAhBJ,SAAkDvS,CAAC,CAAE0R,CAAC,EAElD,MAAO,AAACA,CAAAA,EAAI1R,CAAAA,EAAKA,EAAK0R,CAAAA,CAAAA,EAAI1R,CAAAA,CAC9B,CAcA,EAkBI6U,GAAM,AAAC5T,IAA+E4T,GAAG,CAIzFC,GAAQ,AAAC7T,IAA+E6T,KAAK,CAAEC,GAAoC,AAAC9T,IAA+EwF,OAAO,CAAEuO,GAAa,AAAC/T,IAA+E+T,UAAU,CAAEC,GAAY,AAAChU,IAA+EgU,SAAS,CAAEC,GAAiC,AAACjU,IAA+E4F,IAAI,CAYjiBsO,GAA2C,WAC3C,SAASA,IAML,IAAI,CAAClD,GAAG,CAAG,CAAC,EACZ,IAAI,CAACmD,WAAW,CAAG,EACnB,IAAI,CAACC,gBAAgB,CAAG,CAAA,EACxB,IAAI,CAACC,KAAK,CAAG,EAAE,CACf,IAAI,CAACpO,KAAK,CAAG,EAAE,CACf,IAAI,CAAClF,MAAM,CAAG,EAAE,CAChB,IAAI,CAACuT,UAAU,CAAG,CAAA,CACtB,CAyfA,OAxfAJ,EAA0BzS,OAAO,CAAG,SAAUC,CAAU,EACpDsD,EAA8BvD,OAAO,CAACC,GACtCsD,EAA8BC,YAAY,CAACsP,KAAK,CAAG1E,EACnD7K,EAA8BC,YAAY,CAACuP,MAAM,CAAGrB,EACpDnO,EAA8BE,OAAO,CAAC,uBAAuB,CACzDgP,CACR,EACAA,EAA0BxU,SAAS,CAAC+U,IAAI,CAAG,SAAUxT,CAAO,EACxD,IAAI,CAACA,OAAO,CAAGA,EACf,IAAI,CAACgF,KAAK,CAAG,EAAE,CACf,IAAI,CAACoO,KAAK,CAAG,EAAE,CACf,IAAI,CAACtT,MAAM,CAAG,EAAE,CAChB,IAAI,CAACiQ,GAAG,CAAG,CACP3F,EAAG,EACH9C,EAAG,EACHuD,MAAO,EACPmF,OAAQ,CACZ,EACA,IAAI,CAACyD,mBAAmB,CAAC,CAAA,GACzB,IAAI,CAACjF,WAAW,CACZzK,EAA8BC,YAAY,CAAChE,EAAQwO,WAAW,CAAC,CACnE,IAAI,CAACvM,gBAAgB,CAAGjC,EAAQiC,gBAAgB,CAChD,IAAI,CAACyR,eAAe,CAAGV,GAA+BhT,EAAQ0T,eAAe,CAAE,IAAI,CAAClF,WAAW,CAACe,uBAAuB,EACvH,IAAI,CAACoE,cAAc,CAAGX,GAA+BhT,EAAQ2T,cAAc,CAAE,IAAI,CAACnF,WAAW,CAAC6B,sBAAsB,EACpH,IAAI,CAAC/B,aAAa,CAAGtO,EAAQsO,aAAa,AAC9C,EACA2E,EAA0BxU,SAAS,CAACuE,gBAAgB,CAAG,SAAU4Q,CAAM,EACnE,IAAI,CAAC3R,gBAAgB,CAAG+Q,GAA+BY,EAAQ,IAAI,CAAC5T,OAAO,CAACiC,gBAAgB,CAChG,EACAgR,EAA0BxU,SAAS,CAACyD,KAAK,CAAG,WACxC,IACIpC,EAAS,IAAI,CAACA,MAAM,CACpBE,EAAU,IAAI,CAACA,OAAO,AAC1B+B,CAHa,IAAI,CAGVmR,WAAW,CAAG,EACrBnR,AAJa,IAAI,CAIV8R,MAAM,CAAG/T,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC+T,MAAM,EAAI,EAAE,CACnD9R,AALa,IAAI,CAKVrC,KAAK,CAAGI,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACJ,KAAK,CACvCqC,AANS,IAAI,CAMNoR,gBAAgB,GACvBpR,AAPS,IAAI,CAON+R,aAAa,GAEpBhU,EAAOgC,OAAO,CAAC,SAAUiS,CAAC,EACtBA,EAAEC,iBAAiB,CAAG,CAAA,EACtBD,EAAEjQ,MAAM,EACZ,IAEJ/B,AAda,IAAI,CAcVkS,IAAI,GACXlS,AAfa,IAAI,CAeVmS,eAAe,CAAClU,GACnB+B,AAhBS,IAAI,CAgBNE,gBAAgB,EACvBF,AAjBS,IAAI,CAiBN8B,IAAI,EAEnB,EACAoP,EAA0BxU,SAAS,CAACoF,IAAI,CAAG,WACvC,IAAIwC,EAAQ,IAAI,CAEZ8N,EAAY,IAAI,CAACrU,MAAM,AAE3B,CAAA,IAAI,CAACoT,WAAW,GACW,eAAvB,IAAI,CAAC5E,aAAa,GAClB,IAAI,CAAC8F,cAAc,GACnB,IAAI,CAACC,QAAQ,CAAC3C,sBAAsB,IAExC,IAAK,IAAIL,EAAK,EAAG3Q,EAAK,IAAI,CAACmT,MAAM,EAAI,EAAE,CAAExC,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAErDiD,AAVY,IAAI,AAUP,CAACC,AADM7T,CAAE,CAAC2Q,EAAG,CACA,SAAS,CAAC,IAAI,CAAC3N,WAAW,EAQpD,GALA,IAAI,CAAC8Q,WAAW,GAEhB,IAAI,CAAC9Q,WAAW,CAAG,IAAI,CAAC+Q,QAAQ,CAAC,IAAI,CAACC,gBAAgB,CAAE,IAAI,CAACvC,eAAe,CAAE,IAAI,CAACe,WAAW,EAC9F,IAAI,CAACyB,qBAAqB,CAAG,IAAI,CAACC,iBAAiB,CACnD,IAAI,CAACA,iBAAiB,CAAG,IAAI,CAACC,oBAAoB,GAC9C,IAAI,CAAC5S,gBAAgB,CAAE,CACvB,IAAK,IAAI6S,EAAK,EAA4BA,EAAKC,AAAhBZ,EAA4B9O,MAAM,CAAEyP,IAAM,CACrE,IAAIhV,EAASiV,AADcZ,CACH,CAACW,EAAG,AAExBhV,CAAAA,EAAOJ,KAAK,EACZI,EAAOgE,MAAM,EAErB,CACI,IAAI,CAACN,aAAa,IAClBC,SAAS,IAAI,CAACC,WAAW,GACzB,CAAC,IAAI,CAACC,QAAQ,IACV,IAAI,CAAC0P,UAAU,EACfV,GAAIqC,oBAAoB,CAAC,IAAI,CAAC3B,UAAU,EAE5C,IAAI,CAACA,UAAU,CAAGV,GAAIsC,qBAAqB,CAAC,WAAc,OAAO5O,EAAMxC,IAAI,EAAI,KAG/E,IAAI,CAACwP,UAAU,CAAG,CAAA,EAClB,IAAI,CAACvT,MAAM,CAACgC,OAAO,CAAC,SAAUiS,CAAC,EAC3BhB,GAAUgB,EAAG,kBACjB,GAER,CACJ,EACAd,EAA0BxU,SAAS,CAAC0E,IAAI,CAAG,WACnC,IAAI,CAACkQ,UAAU,EACfV,GAAIqC,oBAAoB,CAAC,IAAI,CAAC3B,UAAU,CAEhD,EACAJ,EAA0BxU,SAAS,CAACyW,OAAO,CAAG,SAAU9K,CAAC,CAAE9C,CAAC,CAAE6N,CAAC,CAAEC,CAAC,EAC9D,IAAI,CAACrF,GAAG,CAAG,CACP5E,KAAMf,EACN0G,IAAKxJ,EACLuD,MAAOsK,EACPnF,OAAQoF,CACZ,CACJ,EACAnC,EAA0BxU,SAAS,CAACwV,IAAI,CAAG,WAGvC,IAAI,CAACzE,CAAC,CAAG,IAAI,CAACxP,OAAO,CAACqV,UAAU,EAAI,IAAI,CAAC7G,WAAW,CAACqB,IAAI,CAAC,IAAI,CAClE,EACAoD,EAA0BxU,SAAS,CAAC6W,uBAAuB,CAAG,SAAUC,CAAQ,CAAEC,CAAU,EACxF,IAAK,IAAInE,EAAK,EAA0BA,EAAKoE,AAAfF,EAA0BlQ,MAAM,CAAEgM,IAAM,CAClE,IAAIqE,EAAUD,AADYF,CACF,CAAClE,EAAG,AACQ,CAAA,KAAhCmE,EAAWlJ,OAAO,CAACoJ,IACnBF,EAAWzO,IAAI,CAAC2O,EAExB,CACJ,EACAzC,EAA0BxU,SAAS,CAAC8L,2BAA2B,CAAG,SAAUmL,CAAO,CAAEF,CAAU,EAC3F,IAAIhP,EAAQgP,EAAWlJ,OAAO,CAACoJ,EACjB,CAAA,KAAVlP,GACAgP,EAAWjJ,MAAM,CAAC/F,EAAO,EAEjC,EACAyM,EAA0BxU,SAAS,CAACkX,KAAK,CAAG,WACxC,IAAI,CAAC3Q,KAAK,CAACK,MAAM,CAAG,EACpB,IAAI,CAAC+N,KAAK,CAAC/N,MAAM,CAAG,EACpB,IAAI,CAACvF,MAAM,CAACuF,MAAM,CAAG,EACrB,IAAI,CAAC6O,eAAe,EACxB,EACAjB,EAA0BxU,SAAS,CAACyV,eAAe,CAAG,WAClD,IAAI,CAAC0B,UAAU,CAAG,CAAA,EAClB,IAAI,CAAChB,iBAAiB,CAAG,EACzB,IAAI,CAACiB,gBAAgB,GACrB,IAAI,CAACC,cAAc,GACnB,IAAI,CAACC,kBAAkB,EAC3B,EACA9C,EAA0BxU,SAAS,CAACuD,iBAAiB,CAAG,WAC/C,IAAI,CAACqR,UAAU,CAoBhB,IAAI,CAACa,eAAe,IAjBpB,IAAI,CAACT,mBAAmB,CAAC,CAAA,GAEpB,IAAI,CAACxR,gBAAgB,CAKtB,IAAI,CAACC,KAAK,GAHV,IAAI,CAAC2T,gBAAgB,CAAC,GAKtB,IAAI,CAACnW,KAAK,EACV,IAAI,CAACA,KAAK,CAACyC,MAAM,GAGrB,IAAI,CAACsR,mBAAmB,CAAC,CAAA,GAMjC,EACAR,EAA0BxU,SAAS,CAACoX,gBAAgB,CAAG,SAAUrS,CAAa,EAC1E,IAAI,CAACA,aAAa,CAAGwP,GAA+BxP,EAAe,IAAI,CAACxD,OAAO,CAACwD,aAAa,CACjG,EACAyP,EAA0BxU,SAAS,CAACqX,cAAc,CAAG,WACjD,IAAI,CAACpS,WAAW,CAAG,IAAI,CAACgR,gBAAgB,CACpCjT,KAAKuU,IAAI,CAAC,IAAI,CAAChR,KAAK,CAACK,MAAM,CACnC,EACA4N,EAA0BxU,SAAS,CAACsX,kBAAkB,CAAG,WACrD,IAAI,CAAC5D,eAAe,CAAG,IAAI,CAACuC,gBAAgB,CACvC,CAAA,IAAI,CAAC1U,OAAO,CAACwD,aAAa,CAAG,CAAA,CACtC,EACAyP,EAA0BxU,SAAS,CAACgV,mBAAmB,CAAG,SAAUG,CAAM,EACtE,IAAI,CAACT,gBAAgB,CAAGS,CAC5B,EACAX,EAA0BxU,SAAS,CAAC2V,cAAc,CAAG,WACjD,IAAI,CAACC,QAAQ,CAAG,IArakC9C,EAqaR,IAAI,CAACxB,GAAG,CAAC5E,IAAI,CAAE,IAAI,CAAC4E,GAAG,CAACe,GAAG,CAAE,IAAI,CAACf,GAAG,CAAClF,KAAK,CAAE,IAAI,CAACkF,GAAG,CAACC,MAAM,EACtG,IAAI,CAACqE,QAAQ,CAACzC,WAAW,CAAC,IAAI,CAAC5M,KAAK,CACxC,EACAiO,EAA0BxU,SAAS,CAACqV,aAAa,CAAG,WAChD,IAAI5F,EAAmB,IAAI,CAAClO,OAAO,CAACkO,gBAAgB,CACpD,GAAI4E,GAAW5E,GAAmB,CAC9BA,EAAiBvP,IAAI,CAAC,IAAI,EAC1B,IAAK,IAAI0S,EAAK,EAAG3Q,EAAK,IAAI,CAACsE,KAAK,CAAEqM,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CACpD,IAAIjM,EAAO1E,CAAE,CAAC2Q,EAAG,CACZwB,GAAkCzN,EAAKgN,KAAK,GAC7ChN,CAAAA,EAAKgN,KAAK,CAAGhN,EAAKnE,KAAK,AAAD,EAErB4R,GAAkCzN,EAAKiN,KAAK,GAC7CjN,CAAAA,EAAKiN,KAAK,CAAGjN,EAAKlE,KAAK,AAAD,EAE1BkE,EAAKgK,KAAK,CAAG,EACbhK,EAAKkK,KAAK,CAAG,CACjB,CACJ,KACSpB,AAAqB,WAArBA,EACL,IAAI,CAAC+H,oBAAoB,GAGzB,IAAI,CAACC,kBAAkB,EAE/B,EACAjD,EAA0BxU,SAAS,CAACwX,oBAAoB,CAAG,WAqBvD,IAAK,IAkBD7Q,EAtCA2K,EAAM,IAAI,CAACA,GAAG,CACd/K,EAAQ,IAAI,CAACA,KAAK,CAElBmR,EAAQ,EAAI1U,KAAK2U,EAAE,CADLpR,CAAAA,EAAMK,MAAM,CAAG,CAAA,EAE7BgR,EAAYrR,EAAMsR,MAAM,CAAC,SAAUlR,CAAI,EACnC,OAAOA,AAAwB,IAAxBA,EAAKO,OAAO,CAACN,MAAM,AAClC,GAAIkR,EAAe,CAAC,EAAGjO,EAAS,IAAI,CAACtI,OAAO,CAACmO,qBAAqB,CAAEqI,EAAa,SAAUpR,CAAI,EAC3F,IAAK,IAAIiM,EAAK,EAAG3Q,EAAK0E,EAAKQ,SAAS,EAAI,EAAE,CAAEyL,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CAC9D,IAAI3J,EAAOhH,CAAE,CAAC2Q,EAAG,AACZkF,CAAAA,CAAY,CAAC7O,EAAK5B,MAAM,CAACe,EAAE,CAAC,GAC7B0P,CAAY,CAAC7O,EAAK5B,MAAM,CAACe,EAAE,CAAC,CAAG,CAAA,EAC/B4P,EAAY1P,IAAI,CAACW,EAAK5B,MAAM,EAC5B0Q,EAAW9O,EAAK5B,MAAM,EAE9B,CACJ,EACI2Q,EAAc,EAAE,CAIXpF,EAAK,EAA4BA,EAAKqF,AAAhBL,EAA4BhR,MAAM,CAAEgM,IAAM,CACrE,IAAIsF,EAAWD,AADYL,CACD,CAAChF,EAAG,CAC9BoF,EAAY1P,IAAI,CAAC4P,GACjBH,EAAWG,EACf,CAEA,GAAKF,EAAYpR,MAAM,CAKnB,IAAK,IAAI3E,EAAK,EAAoBA,EAAKkW,AAAZ5R,EAAoBK,MAAM,CAAE3E,IAAM,CACzD,IAAImW,EAASD,AADU5R,CACH,CAACtE,EAAG,AACY,CAAA,KAAhC+V,EAAYnK,OAAO,CAACuK,IACpBJ,EAAY1P,IAAI,CAAC8P,EAEzB,MATAJ,EAAczR,EAclB,IAAK,IAAI+C,EAAI,EAAG+O,EAAOL,EAAYpR,MAAM,CAAE0C,EAAI+O,EAAM,EAAE/O,EAEnD3C,AADAA,CAAAA,EAAOqR,CAAW,CAAC1O,EAAE,AAAD,EACf9G,KAAK,CAAGmE,EAAKgN,KAAK,CAAGY,GAA+B5N,EAAKnE,KAAK,CAAE8O,EAAIlF,KAAK,CAAG,EAAIvC,EAAS7G,KAAKsV,GAAG,CAAChP,EAAIoO,IAC3G/Q,EAAKlE,KAAK,CAAGkE,EAAKiN,KAAK,CAAGW,GAA+B5N,EAAKlE,KAAK,CAAE6O,EAAIC,MAAM,CAAG,EAAI1H,EAAS7G,KAAKuV,GAAG,CAACjP,EAAIoO,IAC5G/Q,EAAKgK,KAAK,CAAG,EACbhK,EAAKkK,KAAK,CAAG,CAErB,EACA2D,EAA0BxU,SAAS,CAACyX,kBAAkB,CAAG,WAiBrD,IAAK,IAFD9Q,EAdA2K,EAAM,IAAI,CAACA,GAAG,CACd/K,EAAQ,IAAI,CAACA,KAAK,CAClBiS,EAAcjS,EAAMK,MAAM,CAAG,EAO7B6R,EAAW,SAAUvZ,CAAC,EAClB,IAAIwZ,EAAOxZ,EAAIA,EAAI8D,KAAK2U,EAAE,CAE9B,OADAe,EAAc1V,KAAK2V,KAAK,CAACD,EAE7B,EAGSpP,EAAI,EAAG+O,EAAO9R,EAAMK,MAAM,CAAE0C,EAAI+O,EAAM,EAAE/O,EAE7C3C,AADAA,CAAAA,EAAOJ,CAAK,CAAC+C,EAAE,AAAD,EACT9G,KAAK,CAAGmE,EAAKgN,KAAK,CAAGY,GAA+B5N,EAAKnE,KAAK,CAAE8O,EAAIlF,KAAK,CAAGqM,EAASnP,IAC1F3C,EAAKlE,KAAK,CAAGkE,EAAKiN,KAAK,CAAGW,GAA+B5N,EAAKlE,KAAK,CAAE6O,EAAIC,MAAM,CAAGkH,EAASD,EAAclP,IACzG3C,EAAKgK,KAAK,CAAG,EACbhK,EAAKkK,KAAK,CAAG,CAErB,EACA2D,EAA0BxU,SAAS,CAACqQ,KAAK,CAAG,SAAU3G,CAAI,EAEtD,IAAK,IADD3C,EAAO,EAAE,CACJ6L,EAAK,EAAGA,EAAKnM,UAAUG,MAAM,CAAEgM,IACpC7L,CAAI,CAAC6L,EAAK,EAAE,CAAGnM,SAAS,CAACmM,EAAG,CAEhC,IAAI,CAAC7C,WAAW,CAACrG,EAAK,CAAClD,KAAK,CAAC,IAAI,CAAEO,EACvC,EACAyN,EAA0BxU,SAAS,CAAC4Y,gBAAgB,CAAG,WACnD,IAAI,CAACC,aAAa,GAClB,IAAI,CAACxI,KAAK,CAAC,aACf,EACAmE,EAA0BxU,SAAS,CAAC6Y,aAAa,CAAG,WAIhD,IAAK,IAHDC,EAAa,EACbC,EAAK,EACLC,EAAK,EACApG,EAAK,EAAG3Q,EAAK,IAAI,CAACsE,KAAK,CAAEqM,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CACpD,IAAIjM,EAAO1E,CAAE,CAAC2Q,EAAG,CACjBmG,GAAMpS,EAAKnE,KAAK,CAAGmE,EAAKgD,IAAI,CAC5BqP,GAAMrS,EAAKlE,KAAK,CAAGkE,EAAKgD,IAAI,CAC5BmP,GAAcnS,EAAKgD,IAAI,AAC3B,CAOA,OANA,IAAI,CAACqH,UAAU,CAAG,CACdrF,EAAGoN,EACHlQ,EAAGmQ,EACH/H,QAAS8H,EAAKD,EACd5H,QAAS8H,EAAKF,CAClB,EACO,IAAI,CAAC9H,UAAU,AAC1B,EACAwD,EAA0BxU,SAAS,CAACiZ,sBAAsB,CAAG,SAAUtS,CAAI,CAAEuS,CAAQ,EACjF,IAGIC,EACA9I,EAJAC,EAAa,IAAI,CAAC8I,SAAS,CAACzS,EAC5BuS,GACA3I,EAAY,IAAI,CAACkB,YAAY,CAACnB,GAyBlC,OAtBI3J,IAASuS,GAAY3I,AAAc,IAAdA,IACjB2I,EAASlH,UAAU,CAEfkH,EAASjH,OAAO,CAAG1B,EACnB,IAAI,CAAChP,OAAO,CAACoO,KAAK,EAClBY,AAAc,IAAdA,GAEAF,EAAQ,IAAI,CAAC6E,cAAc,CAAC3E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAa1J,EAAM0J,EAAQ6I,EAASvP,IAAI,CAAE2G,EAAYC,GACjE4I,EAAW,CAAA,GAIXA,EAAW,CAAA,GAKf9I,EAAQ,IAAI,CAAC6E,cAAc,CAAC3E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAa1J,EAAM0J,EAAQ6I,EAASvP,IAAI,CAAE2G,EAAYC,KAGlE4I,CACX,EACA3E,EAA0BxU,SAAS,CAACqZ,eAAe,CAAG,WAClD,IAAIzR,EAAQ,IAAI,CAChB,GAAI,AAAuB,eAAvB,IAAI,CAACiI,aAAa,CAOlB,IAAK,IANDyJ,EAAU,SAAU3S,CAAI,EACpB4S,EAAO3D,QAAQ,CAAC1C,kBAAkB,CAAC,KACvC,SAAUgG,CAAQ,EAAI,OAAQtR,EAAMqR,sBAAsB,CAACtS,EAC3DuS,EAAY,EAChB,EACIK,EAAS,IAAI,CACR3G,EAAK,EAAG3Q,EAAK,IAAI,CAACsE,KAAK,CAAEqM,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CACpD,IAAIjM,EAAO1E,CAAE,CAAC2Q,EAAG,CACjB0G,EAAQ3S,EACZ,MAMA,IAAK,IAHD0J,EAAQ,KAAK,EACbE,EAAY,KAAK,EACjBD,EAAa,KAAK,EACb+F,EAAK,EAAGmD,EAAK,IAAI,CAACjT,KAAK,CAAE8P,EAAKmD,EAAG5S,MAAM,CAAEyP,IAE9C,IAAK,IADD1P,EAAO6S,CAAE,CAACnD,EAAG,CACRoD,EAAK,EAAGC,EAAK,IAAI,CAACnT,KAAK,CAAEkT,EAAKC,EAAG9S,MAAM,CAAE6S,IAAM,CACpD,IAAIE,EAAUD,CAAE,CAACD,EAAG,CAGpB9S,IAASgT,GAIJhT,EAAKtE,aAAa,GACnBiO,EAAa,IAAI,CAAC8I,SAAS,CAACzS,EAAMgT,GAEhB,IADlBpJ,CAAAA,EAAY,IAAI,CAACkB,YAAY,CAACnB,EAAU,IAEpCD,EAAQ,IAAI,CAAC6E,cAAc,CAAC3E,EAAW,IAAI,CAACQ,CAAC,EAC7C,IAAI,CAACV,KAAK,CAAC,YAAa1J,EAAM0J,EAAQsJ,EAAQhQ,IAAI,CAAE2G,EAAYC,IAG5E,CAGZ,EACAiE,EAA0BxU,SAAS,CAAC4Z,gBAAgB,CAAG,WAInD,IAAK,IAHDtJ,EACAC,EACAF,EACKuC,EAAK,EAAG3Q,EAAK,IAAI,CAAC0S,KAAK,CAAE/B,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAAM,CACpD,IAAI3J,EAAOhH,CAAE,CAAC2Q,EAAG,AACb3J,CAAAA,EAAK7B,QAAQ,EAAI6B,EAAK5B,MAAM,GAC5BiJ,EAAa,IAAI,CAAC8I,SAAS,CAACnQ,EAAK7B,QAAQ,CAAE6B,EAAK5B,MAAM,EAEpC,IADlBkJ,CAAAA,EAAY,IAAI,CAACkB,YAAY,CAACnB,EAAU,IAEpCD,EAAQ,IAAI,CAAC4E,eAAe,CAAC1E,EAAW,IAAI,CAACQ,CAAC,EAC9C,IAAI,CAACV,KAAK,CAAC,aAAcpH,EAAMoH,EAAOC,EAAYC,IAG9D,CACJ,EACAiE,EAA0BxU,SAAS,CAAC+V,WAAW,CAAG,WAE9C,IAAK,IADDxP,EAAQ,IAAI,CAACA,KAAK,CACbqM,EAAK,EAAoBA,EAAKiH,AAAZtT,EAAoBK,MAAM,CAAEgM,IAAM,CACzD,IAAIjM,EAAOkT,AADYtT,CACL,CAACqM,EAAG,EAClBjM,EAAKtE,aAAa,GAGtB,IAAI,CAAC0N,WAAW,CAACyB,SAAS,CAAC,IAAI,CAAE7K,GACjC,IAAI,CAACmT,aAAa,CAACnT,EAAM,IAAI,CAAC2K,GAAG,EAEjC3K,EAAKgK,KAAK,CAAG,EACbhK,EAAKkK,KAAK,CAAG,EACjB,CACJ,EAMA2D,EAA0BxU,SAAS,CAAC8Z,aAAa,CAAG,SAAUnT,CAAI,CAAE2K,CAAG,EACnE,IAAIzH,EAASlD,EAAKkD,MAAM,AAkCxBlD,CAAAA,EAAKnE,KAAK,CAAG2R,GAAMxN,EAAKnE,KAAK,CAAE8O,EAAI5E,IAAI,CAAG7C,EAAQyH,EAAIlF,KAAK,CAAGvC,GAE9DlD,EAAKlE,KAAK,CAAG0R,GAAMxN,EAAKlE,KAAK,CAAE6O,EAAIe,GAAG,CAAGxI,EAAQyH,EAAIC,MAAM,CAAG1H,EAClE,EAMA2K,EAA0BxU,SAAS,CAACgW,QAAQ,CAAG,SAAU/Q,CAAW,CAAE8U,CAAe,CAAEtF,CAAW,EAe9F,OAAOxP,EAAc8U,EAAkBtF,CAC3C,EACAD,EAA0BxU,SAAS,CAACkF,QAAQ,CAAG,WAC3C,OAAOlC,AAC2B,KAD3BA,KAAKC,GAAG,CAAC,IAAI,CAACkT,iBAAiB,CAClC,IAAI,CAACD,qBAAqB,GAAe,IAAI,CAACjR,WAAW,EAAI,CACrE,EACAuP,EAA0BxU,SAAS,CAACoW,oBAAoB,CAAG,WAEvD,IAAK,IADD4D,EAAQ,EACHpH,EAAK,EAAG3Q,EAAK,IAAI,CAACsE,KAAK,CAAEqM,EAAK3Q,EAAG2E,MAAM,CAAEgM,IAE9CoH,GAASrT,AADE1E,CAAE,CAAC2Q,EAAG,CACH3N,WAAW,CAE7B,OAAO+U,CACX,EACAxF,EAA0BxU,SAAS,CAACyR,YAAY,CAAG,SAAUwI,CAAM,EAC/D,OAAOjX,KAAKuU,IAAI,CAAC0C,EAAOtO,CAAC,CAAGsO,EAAOtO,CAAC,CAAGsO,EAAOpR,CAAC,CAAGoR,EAAOpR,CAAC,CAC9D,EACA2L,EAA0BxU,SAAS,CAACka,QAAQ,CAAG,SAAUC,CAAK,CAAEC,CAAK,EACjE,IAAIC,EAAW,IAAI,CAACjB,SAAS,CAACe,EAC1BC,GACJ,OAAO,IAAI,CAAC3I,YAAY,CAAC4I,EAC7B,EACA7F,EAA0BxU,SAAS,CAACoZ,SAAS,CAAG,SAAUe,CAAK,CAAEC,CAAK,EAClE,IAAIE,EAAQH,EAAM3X,KAAK,CAAG4X,EAAM5X,KAAK,CACjC+X,EAAQJ,EAAM1X,KAAK,CAAG2X,EAAM3X,KAAK,CACrC,MAAO,CACHkJ,EAAG2O,EACHzR,EAAG0R,EACHC,KAAMxX,KAAKC,GAAG,CAACqX,GACfG,KAAMzX,KAAKC,GAAG,CAACsX,EACnB,CACJ,EACO/F,CACX,IAgBIkG,GAAkC,AAACpa,IAA+E2F,KAAK,CAAE0U,GAAc,AAACra,IAA+Eqa,WAAW,CAElOC,GAAa,AAACta,IAA+Esa,UAAU,CAwD1EC,GAJN,CACvBC,eAtBJ,WACI,IACIC,EAAY1Z,AADH,IAAI,CACME,OAAO,CAAC+L,UAAU,CACzC,GAAI,CAACjM,AAFQ,IAAI,CAEL2Z,eAAe,CAAE,CACzB,IAAIA,EAAkB,IAAI,CAACC,mBAAmB,GAW9C,MARI,CAAC5Z,AANI,IAAI,CAMDJ,KAAK,CAACkJ,UAAU,EAAK4Q,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAU1L,KAAK,AAAD,GACjG2L,EAAgB3P,GAAG,CAAC0P,EAAU1L,KAAK,EAGvC2L,EAAgBnX,IAAI,CAAC,CAAE2I,QAAS,CAAE,GAC9BnL,AAXK,IAAI,CAWF6Z,OAAO,EACdF,EAAgBG,IAAI,GAEjBH,CACX,CAGA,OADA3Z,AAjBa,IAAI,CAiBV2Z,eAAe,CAACnX,IAAI,CAAC6W,GAAgC,CAAElO,QAAS,CAAE,EAAG,IAAI,CAAC4O,UAAU,CAAC,iBACrF/Z,AAlBM,IAAI,CAkBH2Z,eAAe,AACjC,EAGIK,oBA/CJ,WACI,IACIpZ,EADA2F,EAAQ,IAAI,CAEZmT,EAAY,IAAI,CAACxZ,OAAO,CAAC+L,UAAU,AAKnC,CAAEyN,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUxL,KAAK,AAAD,GACpE,CAAA,AAAwC,OAAvCtN,CAAAA,EAAK,IAAI,CAACV,OAAO,CAACiO,eAAe,AAAD,GAAevN,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuB,gBAAgB,AAAD,EAI7FmX,GAAY,WACR/S,EAAM0T,eAAe,CAAG,CAAA,CAC5B,EAAGP,EAAYH,GAAWG,EAAUrT,SAAS,EAAE6H,KAAK,CAAG,GALvD,IAAI,CAAC+L,eAAe,CAAG,CAAA,CAO/B,CA+BA,EAkBIC,GAAU,AAACjb,IAA+Eib,OAAO,CACjGC,GAAoB,AAAClb,IAA+EI,QAAQ,CAAE+a,GAAiB,AAACnb,IAA+E2F,KAAK,CAAEyV,GAAY,AAACpb,IAA+Eob,SAAS,CAAEC,GAAmB,AAACrb,IAA+EwF,OAAO,CAAE8V,GAAkB,AAACtb,IAA+EyF,MAAM,CAyBrhB,SAAS8V,GAAY1O,CAAI,CAAE2O,CAAe,EACtC,IAAIlU,EAAQ,IAAI,CAEhBkU,EAAkBL,GAAe,CAAA,EAAM,CACnC7M,QAAS,CAAA,EACTmN,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGJ,GACH,IAAIK,EAAM,IAAI,CAAC/N,QAAQ,CAAC+N,GAAG,CACvBC,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAC/BjN,EAAWgN,EAAYhN,QAAQ,CAC/B2M,EAAaD,EAAgBC,UAAU,CACvCnN,EAAUkN,EAAgBlN,OAAO,CAMrC,GALAzB,EAAOA,GAASiC,GAAYA,EAASjC,IAAI,CAErCiC,GACAA,EAASkN,IAAI,GAEbnP,GAAQyB,EAAS,CACjB,IAAI0N,EAAOd,GAAkBY,EAAa,kBACtC,SAAU1a,CAAC,EACP,GAAIyL,GAAQyB,EAAS,CAEjB,IAAI2N,EAAapP,EAAKtJ,IAAI,CAAC,MAC1B0Y,GACDpP,EAAKtJ,IAAI,CAAC,KAAM0Y,EAAab,MAGjC,IAAIc,EAAc,CAGV7Q,EAAG,EACH9C,EAAG,CACP,EACA8S,GAAiBI,EAAWU,EAAE,IAC9BD,EAAYC,EAAE,CAAGV,EAAWU,EAAE,CAC9B,OAAOV,EAAWU,EAAE,EAEpBd,GAAiBI,EAAWC,EAAE,IAC9BQ,EAAYR,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBI,EAAYvY,IAAI,CAAC2Y,GAEjB5U,EAAM/D,IAAI,CAAC,CAAE6Y,UAAW,EAAG,GACvB9U,EAAM0J,GAAG,EACT1J,CAAAA,EAAM0J,GAAG,CAAG1J,EAAM0J,GAAG,CAACnL,OAAO,EAAC,EAGlC,IAAIwW,EAAWjb,EAAE6E,KAAK,CAACyH,KAAK,CAAC,EAC7BtM,CAAAA,EAAE6E,KAAK,CAACK,MAAM,CAAG,EACjBlF,EAAE6E,KAAK,CAAC,EAAE,CAAG,CACTqW,QAAS,WACTb,WAAYH,GAAgBG,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCW,KAAM,GAAKV,EAAM,IAAI9V,MAAM,CAACkW,EAChC,GACAI,SAAUA,CACd,CACJ,CACJ,EAEAP,CAAAA,EAAYhN,QAAQ,CAAG,CAAEjC,KAAMA,EAAMmP,KAAMA,CAAK,CACpD,MAEIF,EAAYvY,IAAI,CAAC,CAAE4Y,GAAI,EAAGT,GAAI,CAAE,GAChC,OAAOI,EAAYhN,QAAQ,CAO/B,OALI,IAAI,CAAC0N,KAAK,GAEVV,EAAYW,SAAS,CAAG,GACxB,IAAI,CAAC3O,QAAQ,CAAC4O,SAAS,CAACZ,IAErB,IAAI,AACf,CAWA,SAASa,GAAW9b,CAAK,EAErB,IADIc,EACAib,EAAO/b,EAAM+b,IAAI,CACjBC,EAAK,AAAwB,OAAvBlb,CAAAA,EAAK,IAAI,CAACgV,OAAO,AAAD,GAAehV,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmb,aAAa,CAAC,YACnF,GAAID,EAAI,CA4BJ,IAAK,IA3BDE,EAAU,EAAE,CAAEhH,EAAK,IAAI,CAACjI,QAAQ,CAACkP,WAAW,CAAC,IAAI,CAACrG,OAAO,EAAGsG,EAAMlH,EAAG5L,CAAC,CAAY+S,EAAc7G,AAApBN,EAAGM,CAAC,CAAoB4G,EAAKE,EAAmB,AAAIC,OAAO,gEAEpH,KAAMC,EAAQR,EAC5BS,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAM/W,MAAM,CAI3EoX,EAAqB,SAAUC,CAAS,CACxCC,CAAc,EACV,IAAIvS,EAAIuS,EAAevS,CAAC,CAC5B9C,EAAIqV,EAAerV,CAAC,CACpBsV,EAAW,AAAChB,CAAAA,EAAGiB,iBAAiB,CAACH,GAAa,EAAC,EAAK1C,GACpD8C,EAASrb,KAAKsV,GAAG,CAAC6F,GAClBG,EAAStb,KAAKuV,GAAG,CAAC4F,GAClB,MAAO,CACH,CACIxS,EAAI6R,EAAca,EAClBxV,EAAI2U,EAAcc,EACrB,CACD,CACI3S,EAAI4R,EAAMc,EACVxV,EAAI0U,EAAMe,EACb,CACJ,AACL,EACShV,EAAI,EAAGiV,EAAY,EAAGA,EAAYR,EAAYQ,IAAa,CAGhE,IAAK,IADDC,EAAUC,AADHd,CAAK,CAACY,EAAU,CACR3X,MAAM,CAChB8X,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAIC,EAAgBrV,EACZoV,EACAH,EACJ/E,EAAKwE,EAAmBW,EACxBxB,EAAGyB,sBAAsB,CAACD,IAC1BE,EAAQrF,CAAE,CAAC,EAAE,CACbsF,EAAQtF,CAAE,CAAC,EAAE,AACbkF,AAAkB,CAAA,IAAlBA,GACArB,EAAQ/U,IAAI,CAACwW,GACbzB,EAAQ/U,IAAI,CAACuW,KAGK,IAAdN,GACAlB,EAAQ0B,OAAO,CAACD,GAEhBP,IAAcR,EAAa,GAC3BV,EAAQ/U,IAAI,CAACuW,GAGzB,CACA,MAAOnd,EAAG,CAGN,KACJ,CAEJ4H,GAAKkV,EAAU,EACf,GAAI,CACA,IAAIG,EAAerV,EAAIiV,EACnBS,EAAU7B,EAAG8B,oBAAoB,CAACN,GAClClF,EAAKuE,EAAmBW,EACxBK,GACAH,EAAQpF,CAAE,CAAC,EAAE,CACbqF,EAAQrF,CAAE,CAAC,EAAE,CACjB4D,EAAQ0B,OAAO,CAACD,GAChBzB,EAAQ0B,OAAO,CAACF,EACpB,CACA,MAAOnd,EAAG,CAGN,KACJ,CACJ,CAEI2b,EAAQzW,MAAM,EACdyW,EAAQ/U,IAAI,CAAC+U,CAAO,CAAC,EAAE,CAACrP,KAAK,IAEjCkP,EAAKG,OAAO,CAAGA,CACnB,CACA,OAAOH,CACX,CAWA,SAASgC,GAAa/d,CAAK,EAEvB,IADIc,EACAkd,EAAehe,EAAMge,YAAY,CACjCne,EAAQG,EAAMH,KAAK,CACnB8a,EAAmBqD,CAAY,CAACne,EAAMyI,YAAY,CAAG,WAAW,EAC5D0V,EAAa/P,QAAQ,CACzB0M,GAAmB,CAACqD,EAAaC,OAAO,GACxC,IAAI,CAACvD,WAAW,CAAC,AAAC,CAAA,AAAkC,OAAjC5Z,CAAAA,EAAKjB,EAAMqe,gBAAgB,AAAD,GAAepd,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG/B,IAAI,CAACc,EAAO,IAAI,CAAA,GAAMA,EAAMwG,OAAO,CAAEsU,GACzH9a,EAAMse,aAAa,EACnB,CAACxD,EAAgBlN,OAAO,EAExB5N,CAAAA,EAAMse,aAAa,CAAIte,EAAMse,aAAa,CAACnZ,OAAO,EAAE,EAGhE,CA2BA,IAAIoZ,IACI7gB,EAAgB,SAAUW,CAAC,CAC3BoL,CAAC,EAOD,MAAO/L,AANHA,CAAAA,EAAgBgB,OAAOgL,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUvL,CAAC,CAC1DoL,CAAC,EAAIpL,EAAEsL,SAAS,CAAGF,CAAG,GACd,SAAUpL,CAAC,CACnBoL,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAO/K,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuK,EAC/DI,IAAIxL,CAAAA,CAAC,CAACwL,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACIxL,EAAGoL,EAC5B,EACO,SAAUpL,CAAC,CAAEoL,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAI+U,UAAU,uBAAyBvQ,OAAOxE,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG1L,CAAG,CADtCX,EAAcW,EAAGoL,GAEjBpL,EAAEW,SAAS,CAAGyK,AAAM,OAANA,EAAa/K,OAAOsL,MAAM,CAACP,GAAMK,CAAAA,EAAG9K,SAAS,CAAGyK,EAAEzK,SAAS,CAAE,IAAI8K,CAAG,CACtF,GAMA2U,GAAO,AAACnf,IAA+Emf,IAAI,CAM3FC,GAAS,AAACha,IAA2IrE,MAAM,CAAEse,GAAuB,AAACja,IAA2Ika,WAAW,CAAEC,GAAcF,GAAqBG,MAAM,CAAC9f,SAAS,CAAE+f,GAAYJ,GAAqBlB,IAAI,CAACze,SAAS,CAEjbggB,GAAoCnF,GAA0BC,cAAc,CAAEmF,GAAyCpF,GAA0BQ,mBAAmB,CAEpK6E,GAA8B,AAAC5f,IAA+EI,QAAQ,CAAEyf,GAA6B,AAAC7f,IAA+EwF,OAAO,CAAEsa,GAA4B,AAAC9f,IAA+EyF,MAAM,CAAEsa,GAA2B,AAAC/f,IAA+E2F,KAAK,CAAEqa,GAA0B,AAAChgB,IAA+E4F,IAAI,CAEtkBqa,AArDe,CAAA,CACXxe,QATJ,SAA0Bye,CAAe,EACrChF,GAAkBgF,EAAiB,eAAgBvD,IACnDzB,GAAkBgF,EAAiB,wBAAyBtB,IAC5D,IAAIuB,EAAkBD,EAAgBxgB,SAAS,AAC1CygB,CAAAA,EAAgB5E,WAAW,EAC5B4E,CAAAA,EAAgB5E,WAAW,CAAGA,EAAU,CAEhD,CAGA,CAAA,EAmDoB9Z,OAAO,CAAEvB,KAa7B,IAAIkgB,GAAoC,SAAUhV,CAAM,EAEpD,SAASgV,IAML,IAAI9Y,EAAQ8D,AAAW,OAAXA,GAAmBA,EAAOlF,KAAK,CAAC,IAAI,CAC5CC,YAAc,IAAI,CAEtB,OADAmB,EAAM0T,eAAe,CAAG,CAAA,EACjB1T,CACX,CAoSA,OA/SA2X,GAA2BmB,EAAoBhV,GAiB/CgV,EAAmB3e,OAAO,CAAG,SAAUC,CAAU,EAC7CF,EAA4BC,OAAO,CAACC,GACpC2e,AAtZmEnM,GAsZ5BzS,OAAO,CAACC,EACnD,EAiBA0e,EAAmB1gB,SAAS,CAAC4gB,WAAW,CAAG,WACvC,IAEItd,EAFAud,EAAgB,IAAI,CAACtf,OAAO,CAACiO,eAAe,CAC5CsR,EAAe,IAAI,CAAC7f,KAAK,CAACM,OAAO,CAACN,KAAK,CAEvC8f,EAAsB,IAAI,CAAC9f,KAAK,CAAC8f,mBAAmB,CACpDle,EAAqB,IAAI,CAAC5B,KAAK,CAAC4B,kBAAkB,AACjD,CAAA,IAAI,CAACqY,OAAO,GAGZ6F,IACD,IAAI,CAAC9f,KAAK,CAAC8f,mBAAmB,CAAGA,EAAsB,CAAC,EACxD,IAAI,CAAC9f,KAAK,CAAC4B,kBAAkB,CAAGA,EAAqB,EAAE,EAE3DS,CAAAA,EAASyd,CAAmB,CAACF,EAAc/Q,IAAI,CAAC,AAAD,IAE3C+Q,EAAcrd,gBAAgB,CAC1B,AAAC2c,GAA2BW,EAAaE,SAAS,EAE9C,CAACF,EAAaE,SAAS,CADvBH,EAAcrd,gBAAgB,CAEtCud,CAAmB,CAACF,EAAc/Q,IAAI,CAAC,CAAGxM,EACtC,IAAIgC,EAA8BE,OAAO,CAACqb,EAAc/Q,IAAI,CAAC,CACjExM,EAAOyR,IAAI,CAAC8L,GACZhe,EAAmBiL,MAAM,CAACxK,EAAOyE,KAAK,CAAE,EAAGzE,IAE/C,IAAI,CAACA,MAAM,CAAGA,EACdA,EAAOmT,OAAO,CAAC,EAAG,EAAG,IAAI,CAACxV,KAAK,CAACggB,SAAS,CAAE,IAAI,CAAChgB,KAAK,CAACigB,UAAU,EAChE5d,EAAOuT,uBAAuB,CAAC,CAAC,IAAI,CAAC,CAAEvT,EAAOjC,MAAM,EACpDiC,EAAOuT,uBAAuB,CAAC,IAAI,CAACtQ,KAAK,CAAEjD,EAAOiD,KAAK,EACvDjD,EAAOuT,uBAAuB,CAAC,IAAI,CAACvQ,MAAM,CAAEhD,EAAOqR,KAAK,EAC5D,EAIA+L,EAAmB1gB,SAAS,CAACmG,OAAO,CAAG,WAC/B,IAAI,CAAC7C,MAAM,EACX,IAAI,CAACA,MAAM,CAACwI,2BAA2B,CAAC,IAAI,CAAE,IAAI,CAACxI,MAAM,CAACjC,MAAM,EAEpEkJ,EAAwBpE,OAAO,CAACjG,IAAI,CAAC,IAAI,CAC7C,EAMAwgB,EAAmB1gB,SAAS,CAACmhB,cAAc,CAAG,WAG1C,IAAI,IAAI,CAAC7F,eAAe,EAGxB,IACIlM,EADA2L,EAAY,IAAI,CAACxZ,OAAO,CAAC+L,UAAU,CAEnCyN,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAU3L,QAAQ,AAAD,GACvEA,CAAAA,EAAW2L,EAAU3L,QAAQ,AAAD,EAGhCsQ,GAAO1f,SAAS,CAACmhB,cAAc,CAACjhB,IAAI,CAAC,IAAI,CAAE,IAAI,CAACqG,KAAK,EAEjDwU,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAU5L,YAAY,AAAD,GAE3E4L,CAAAA,EAAU3L,QAAQ,CAAG2L,EAAU5L,YAAY,AAAD,EAE9CuQ,GAAO1f,SAAS,CAACmhB,cAAc,CAACjhB,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkG,IAAI,EAEhD2U,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAU3L,QAAQ,AAAD,GACvE2L,CAAAA,EAAU3L,QAAQ,CAAGA,CAAO,EAEpC,EAMAsR,EAAmB1gB,SAAS,CAAC8J,cAAc,CAAG,WAC1C,IAAInD,EACA2C,EAYJ,IAXAiB,EAAwBT,cAAc,CAACtD,KAAK,CAAC,IAAI,CAAEC,WAG/C,IAAI,CAAClF,OAAO,CAACgF,KAAK,EAClB,IAAI,CAAChF,OAAO,CAACgF,KAAK,CAAClD,OAAO,CAAC,SAAU+d,CAAW,EACvC,IAAI,CAACrX,UAAU,CAACqX,EAAYhZ,EAAE,CAAC,EAChC,CAAA,IAAI,CAAC2B,UAAU,CAACqX,EAAYhZ,EAAE,CAAC,CAC3B,IAAI,CAACK,UAAU,CAAC2Y,EAAYhZ,EAAE,CAAA,CAE1C,EAAG,IAAI,EAENkB,EAAI,IAAI,CAAC/C,KAAK,CAACK,MAAM,CAAG,EAAG0C,GAAK,EAAGA,IAEpC3C,AADAA,CAAAA,EAAO,IAAI,CAACJ,KAAK,CAAC+C,EAAE,AAAD,EACdsH,MAAM,CAAGjK,EAAKoF,SAAS,GAC5BpF,EAAKkD,MAAM,CAAGyW,GAAwB3Z,EAAKiD,MAAM,EAAIjD,EAAKiD,MAAM,CAACC,MAAM,CAAE,IAAI,CAACtI,OAAO,CAACqI,MAAM,EAAI,IAAI,CAACrI,OAAO,CAACqI,MAAM,CAACC,MAAM,CAAE,GAC5HlD,EAAKnH,GAAG,CAAGmH,EAAK+C,IAAI,CAGf,IAAI,CAACK,UAAU,CAACpD,EAAKyB,EAAE,CAAC,EACzBzB,EAAK+G,MAAM,GAGnB,IAAI,CAACtH,IAAI,CAAC/C,OAAO,CAAC,SAAU4F,CAAI,EAC5BA,EAAKQ,YAAY,CAAG,MACxB,GACA,IAAI,CAAC4X,aAAa,EACtB,EAMAX,EAAmB1gB,SAAS,CAACshB,mBAAmB,CAAG,WAC/C,OAAO,IAAI,CAAC/a,KAAK,EAAI,EAAE,AAC3B,EAOAma,EAAmB1gB,SAAS,CAACqhB,aAAa,CAAG,WACzC,IAAI,CAAC9a,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,CAAEoB,CAAK,EACpCpB,EAAKoB,KAAK,CAAGA,CACjB,EACJ,EAMA2Y,EAAmB1gB,SAAS,CAAC+U,IAAI,CAAG,SAAU9T,CAAK,CAAEM,CAAO,EACxD,IAAIqG,EAAQ,IAAI,CAsBhB,OArBA8D,EAAO1L,SAAS,CAAC+U,IAAI,CAAC7U,IAAI,CAAC,IAAI,CAAEe,EAAOM,GACxC0e,GAAuC/f,IAAI,CAAC,IAAI,EAChDggB,GAA4B,IAAI,CAAE,cAAe,WACzCtY,EAAMtE,MAAM,EACZsE,EAAMtE,MAAM,CAACoB,IAAI,EAEzB,GACAwb,GAA4B,IAAI,CAAE,cAAe,WAC7CtY,EAAMrB,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAC1BA,GAAQA,EAAKtF,MAAM,EACnBsF,EAAK4a,YAAY,EAEzB,EACJ,GAIArB,GAA4B,IAAI,CAAE,kBAAmB,WACjD,IAAI,CAAC5E,eAAe,CAAG,CAAA,EACvB,IAAI,CAAC6F,cAAc,EACvB,GACO,IAAI,AACf,EAOAT,EAAmB1gB,SAAS,CAACwhB,aAAa,CAAG,SAAUxgB,CAAK,CAAE8F,CAAK,EAC/D,IAAIoG,EAAUwS,GAAO1f,SAAS,CAACwhB,aAAa,CAACthB,IAAI,CAAC,IAAI,CAClDc,EACA8F,GAMJ,OAJKqZ,GAA2Bnf,EAAMyB,KAAK,GACvCyK,CAAAA,EAAQrE,CAAC,CAAG,CAAA,EAEhBqE,EAAQvB,CAAC,CAAG,AAAC3K,CAAAA,EAAMwB,KAAK,EAAI,CAAA,EAAK,AAAC0K,CAAAA,EAAQd,KAAK,EAAI,CAAA,EAAK,EACjDc,CACX,EAKAwT,EAAmB1gB,SAAS,CAACqN,YAAY,CAAG,SAAUrM,CAAK,CAAE8F,CAAK,EAE9D,IAAI2a,EAAa3a,GAAS9F,GAASA,EAAM8F,KAAK,EAAI,SAC9C4a,EAAe,IAAI,CAACngB,OAAO,CAACwC,MAAM,CAAC0d,EAAW,CAC9CvU,EAAUwS,GAAO1f,SAAS,CAACqN,YAAY,CAACnN,IAAI,CAAC,IAAI,CACjDc,EACAygB,GAeJ,OAdIzgB,GAAS,CAACA,EAAMiG,MAAM,GACtBiG,EAAUlM,EAAMiL,iBAAiB,GAE7ByV,GACAxU,CAAAA,EAAU,CAENb,OAAQqV,EAAaC,SAAS,EAAIzU,EAAQb,MAAM,CAChDC,UAAYoV,EAAaE,aAAa,EAAI1U,EAAQZ,SAAS,CAC3DE,QAAS8T,GAAwBoB,EAAa3S,WAAW,CAAE7B,EAAQV,OAAO,EAC1E,eAAgBkV,EAAaC,SAAS,EAClCzU,CAAO,CAAC,eAAe,AAC/B,CAAA,GAGDA,CACX,EAMAwT,EAAmB1gB,SAAS,CAACqF,MAAM,CAAG,WAClC,IACIiB,EAASjF,AADA,IAAI,CACGiF,MAAM,CACtBlF,EAAaC,AAFJ,IAAI,CAEOJ,KAAK,CAACG,UAAU,CACpCkM,EAAa,EAAE,AAEnBjM,CALa,IAAI,CAKViF,MAAM,CAAGjF,AALH,IAAI,CAKMkF,KAAK,CAC5BwZ,GAAU1a,MAAM,CAACnF,IAAI,CAAC,IAAI,EAC1BmB,AAPa,IAAI,CAOViF,MAAM,CAAGA,EAChBA,EAAOjD,OAAO,CAAC,SAAUrC,CAAK,EACtBA,EAAMoG,QAAQ,EAAIpG,EAAMqG,MAAM,GAC9BrG,EAAMmN,UAAU,GAChBnN,EAAMiM,UAAU,GAExB,GACI7L,GAAcA,EAAWC,MAAM,GAdtB,IAAI,EAebA,AAfS,IAAI,CAeN+B,UAAU,CAAChC,GAElBC,AAjBS,IAAI,CAiBNJ,KAAK,CAAC4gB,WAAW,EACxB,CAACxgB,AAlBQ,IAAI,CAkBLE,OAAO,CAAC+L,UAAU,CAACwU,YAAY,GACvCzgB,AAnBS,IAAI,CAmBNkF,KAAK,CAACF,MAAM,CAAChF,AAnBX,IAAI,CAmBciF,MAAM,EAAEjD,OAAO,CAAC,SAAUsD,CAAI,EACjDA,EAAKob,SAAS,EACdzU,EAAWhF,IAAI,CAAC3B,EAAKob,SAAS,CAEtC,GACA1gB,AAxBS,IAAI,CAwBNJ,KAAK,CAAC+gB,qBAAqB,CAAC1U,GAE3C,EAMAoT,EAAmB1gB,SAAS,CAACuH,QAAQ,CAAG,SAAUT,CAAK,CAAEmb,CAAO,EACxDA,GACA,IAAI,CAAC3b,MAAM,CAAG,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC,IAAI,CAACD,IAAI,EACzCsZ,GAAO1f,SAAS,CAACuH,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEC,WACtC,IAAI,CAACH,MAAM,CAAG,IAAI,CAACF,IAAI,EAGvBsZ,GAAO1f,SAAS,CAACuH,QAAQ,CAACf,KAAK,CAAC,IAAI,CAAEC,WAGrC,IAAI,CAACnD,MAAM,CAACsR,UAAU,EAAK9N,GAC5B,IAAI,CAACzB,MAAM,EAEnB,EAKAqb,EAAmB1gB,SAAS,CAACkiB,SAAS,CAAG,WACrC,IAAI,CAACpY,cAAc,GACnB,IAAI,CAAC8W,WAAW,GAChB,IAAI,CAACra,KAAK,CAAClD,OAAO,CAAC,SAAUsD,CAAI,EAE7BA,EAAKwb,QAAQ,CAAG,CAAA,EAChBxb,EAAKQ,SAAS,CAAC9D,OAAO,CAAC,SAAUrC,CAAK,EAClCA,EAAMohB,SAAS,CAAG,OAElBphB,EAAM6H,CAAC,CAAG,CACd,EACJ,EACJ,EACA6X,EAAmB2B,cAAc,CAAGhC,GAAyBX,GAAO2C,cAAc,CAAE5T,GAC7EiS,CACX,EAAEhB,IACFU,GAA0BM,GAAmB1gB,SAAS,CAAE,CACpD6F,WA/kF+D4F,EAglF/D+B,QAAS,KAAK,EACd8U,YAAa,CAAA,EACbC,UAAW,KAAK,EAChBnN,OAAQ,CAAC,aAAc,YAAa,aAAa,CACjD9T,kBAAmB,CAAA,EACnBkhB,YAAa,CAAA,EACbC,gBAAiB,CAAA,EACjBC,cAAe,CAAC,OAAQ,KAAK,CAC7BC,eAAgB,CAAA,EAChBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1D9H,eAAgBkF,GAChB6C,YAAapD,GACbhX,WAAY8B,EAAwB9B,UAAU,CAC9Cqa,YAAajD,GAAYiD,WAAW,CACpCrhB,YAAaK,EAA4BL,WAAW,CACpDE,YAAaG,EAA4BH,WAAW,CACpDE,UAAWC,EAA4BD,SAAS,CAChDuB,WAAYtB,EAA4BsB,UAAU,AACtD,GACAsC,IAA0Iqd,kBAAkB,CAAC,eAAgBrC,IA+B7K,IAAIsC,GAAK1iB,IACT2iB,AA1BoEvC,GA0BpC3e,OAAO,CAACihB,GAAEE,KAAK,EAClB,IAAI9iB,GAAqBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,GAET"}