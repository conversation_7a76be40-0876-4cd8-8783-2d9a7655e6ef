!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("highcharts/highcharts",[],e):"object"==typeof exports?exports.highcharts=e():(t.Highcharts&&t.Highcharts.error(16,!0),t.Highcharts=e())}(this,function(){return function(){"use strict";var t,e,i,o,n,r,s,a,h,l,d,c,p,u,f,g,v,m,y,x,b,k,M,w,S,A,T,C,O,P,E,L,B,D,I,z,R,N,W,G,X,H,F,Y,_,j,U,V,Z,q,K,$,J,Q,tt,te,ti,to,tn,tr,ts,ta,th,tl,td,tc,tp,tu,tf={};tf.d=function(t,e){for(var i in e)tf.o(e,i)&&!tf.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},tf.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var tg={};tf.d(tg,{default:function(){return fW}}),(t=W||(W={})).SVG_NS="http://www.w3.org/2000/svg",t.product="Highcharts",t.version="12.2.0",t.win="undefined"!=typeof window?window:{},t.doc=t.win.document,t.svg=!!(null===(i=null===(e=null===t.doc||void 0===t.doc?void 0:t.doc.createElementNS)||void 0===e?void 0:e.call(t.doc,t.SVG_NS,"svg"))||void 0===i?void 0:i.createSVGRect),t.pageLang=null===(n=null===(o=null===t.doc||void 0===t.doc?void 0:t.doc.documentElement)||void 0===o?void 0:o.closest("[lang]"))||void 0===n?void 0:n.lang,t.userAgent=(null===(r=t.win.navigator)||void 0===r?void 0:r.userAgent)||"",t.isChrome=t.win.chrome,t.isFirefox=-1!==t.userAgent.indexOf("Firefox"),t.isMS=/(edge|msie|trident)/i.test(t.userAgent)&&!t.win.opera,t.isSafari=!t.isChrome&&-1!==t.userAgent.indexOf("Safari"),t.isTouchDevice=/(Mobile|Android|Windows Phone)/.test(t.userAgent),t.isWebKit=-1!==t.userAgent.indexOf("AppleWebKit"),t.deg2rad=2*Math.PI/360,t.marginNames=["plotTop","marginRight","marginBottom","plotLeft"],t.noop=function(){},t.supportsPassiveEvents=function(){var e=!1;if(!t.isMS){var i=Object.defineProperty({},"passive",{get:function(){e=!0}});t.win.addEventListener&&t.win.removeEventListener&&(t.win.addEventListener("testPassive",t.noop,i),t.win.removeEventListener("testPassive",t.noop,i))}return e}(),t.charts=[],t.composed=[],t.dateFormats={},t.seriesTypes={},t.symbolSizes={},t.chartCount=0;var tv=W,tm=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},ty=tv.charts,tx=tv.doc,tb=tv.win;function tk(t,e,i,o){var n=e?"Highcharts error":"Highcharts warning";32===t&&(t=""+n+": Deprecated member");var r=tO(t),s=r?""+n+" #"+t+": www.highcharts.com/errors/"+t+"/":t.toString();if(void 0!==o){var a="";r&&(s+="?"),tW(o,function(t,e){a+="\n - ".concat(e,": ").concat(t),r&&(s+=encodeURI(e)+"="+encodeURI(t))}),s+=a}tX(tv,"displayError",{chart:i,code:t,message:s,params:o},function(){if(e)throw Error(s);tb.console&&-1===tk.messages.indexOf(s)&&console.warn(s)}),tk.messages.push(s)}function tM(t,e){return parseInt(t,e||10)}function tw(t){return"string"==typeof t}function tS(t){var e=Object.prototype.toString.call(t);return"[object Array]"===e||"[object Array Iterator]"===e}function tA(t,e){return!!t&&"object"==typeof t&&(!e||!tS(t))}function tT(t){return tA(t)&&"number"==typeof t.nodeType}function tC(t){var e=null==t?void 0:t.constructor;return!!(tA(t,!0)&&!tT(t)&&(null==e?void 0:e.name)&&"Object"!==e.name)}function tO(t){return"number"==typeof t&&!isNaN(t)&&t<1/0&&t>-1/0}function tP(t){return null!=t}function tE(t,e,i){var o,n=tw(e)&&!tP(i),r=function(e,i){tP(e)?t.setAttribute(i,e):n?(o=t.getAttribute(i))||"class"!==i||(o=t.getAttribute(i+"Name")):t.removeAttribute(i)};return tw(e)?r(i,e):tW(e,r),o}function tL(t){return tS(t)?t:[t]}function tB(t,e){var i;for(i in t||(t={}),e)t[i]=e[i];return t}function tD(){for(var t=arguments,e=t.length,i=0;i<e;i++){var o=t[i];if(null!=o)return o}}function tI(t,e){tB(t.style,e)}function tz(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))}function tR(t,e){return t>1e14?t:parseFloat(t.toPrecision(e||14))}(tk||(tk={})).messages=[],Math.easeInOutSine=function(t){return -.5*(Math.cos(Math.PI*t)-1)};var tN=Array.prototype.find?function(t,e){return t.find(e)}:function(t,e){var i,o=t.length;for(i=0;i<o;i++)if(e(t[i],i))return t[i]};function tW(t,e,i){for(var o in t)Object.hasOwnProperty.call(t,o)&&e.call(i||t[o],t[o],o,t)}function tG(t,e,i){function o(e,i){var o=t.removeEventListener;o&&o.call(t,e,i,!1)}function n(i){var n,r;t.nodeName&&(e?(n={})[e]=!0:n=i,tW(n,function(t,e){if(i[e])for(r=i[e].length;r--;)o(e,i[e][r].fn)}))}var r="function"==typeof t&&t.prototype||t;if(Object.hasOwnProperty.call(r,"hcEvents")){var s=r.hcEvents;if(e){var a=s[e]||[];i?(s[e]=a.filter(function(t){return i!==t.fn}),o(e,i)):(n(s),s[e]=[])}else n(s),delete r.hcEvents}}function tX(t,e,i,o){if(i=i||{},(null==tx?void 0:tx.createEvent)&&(t.dispatchEvent||t.fireEvent&&t!==tv)){var n=tx.createEvent("Events");n.initEvent(e,!0,!0),i=tB(n,i),t.dispatchEvent?t.dispatchEvent(i):t.fireEvent(e,i)}else if(t.hcEvents){i.target||tB(i,{preventDefault:function(){i.defaultPrevented=!0},target:t,type:e});for(var r=[],s=t,a=!1;s.hcEvents;)Object.hasOwnProperty.call(s,"hcEvents")&&s.hcEvents[e]&&(r.length&&(a=!0),r.unshift.apply(r,s.hcEvents[e])),s=Object.getPrototypeOf(s);a&&r.sort(function(t,e){return t.order-e.order}),r.forEach(function(e){!1===e.fn.call(t,i)&&i.preventDefault()})}o&&!i.defaultPrevented&&o.call(t,i)}var tH=(s=Math.random().toString(36).substring(2,9)+"-",a=0,function(){return"highcharts-"+(G?"":s)+a++});tb.jQuery&&(tb.jQuery.fn.highcharts=function(){var t=[].slice.call(arguments);if(this[0])return t[0]?(new tv[tw(t[0])?t.shift():"Chart"](this[0],t[0],t[1]),this):ty[tE(this[0],"data-highcharts-chart")]});var tF={addEvent:function(t,e,i,o){void 0===o&&(o={});var n="function"==typeof t&&t.prototype||t;Object.hasOwnProperty.call(n,"hcEvents")||(n.hcEvents={});var r=n.hcEvents;tv.Point&&t instanceof tv.Point&&t.series&&t.series.chart&&(t.series.chart.runTrackerClick=!0);var s=t.addEventListener;s&&s.call(t,e,i,!!tv.supportsPassiveEvents&&{passive:void 0===o.passive?-1!==e.indexOf("touch"):o.passive,capture:!1}),r[e]||(r[e]=[]);var a={fn:i,order:"number"==typeof o.order?o.order:1/0};return r[e].push(a),r[e].sort(function(t,e){return t.order-e.order}),function(){tG(t,e,i)}},arrayMax:function(t){for(var e=t.length,i=t[0];e--;)t[e]>i&&(i=t[e]);return i},arrayMin:function(t){for(var e=t.length,i=t[0];e--;)t[e]<i&&(i=t[e]);return i},attr:tE,clamp:function(t,e,i){return t>e?t<i?t:i:e},clearTimeout:function(t){tP(t)&&clearTimeout(t)},correctFloat:tR,createElement:function(t,e,i,o,n){var r=tx.createElement(t);return e&&tB(r,e),n&&tI(r,{padding:"0",border:"none",margin:"0"}),i&&tI(r,i),o&&o.appendChild(r),r},crisp:function(t,e,i){void 0===e&&(e=0);var o=e%2/2,n=i?-1:1;return(Math.round(t*n-o)+o)*n},css:tI,defined:tP,destroyObjectProperties:function(t,e,i){tW(t,function(o,n){o!==e&&(null==o?void 0:o.destroy)&&o.destroy(),((null==o?void 0:o.destroy)||!i)&&delete t[n]})},diffObjects:function(t,e,i,o){var n={};return!function t(e,n,r,s){var a=i?n:e;tW(e,function(i,h){if(!s&&o&&o.indexOf(h)>-1&&n[h]){i=tL(i),r[h]=[];for(var l=0;l<Math.max(i.length,n[h].length);l++)n[h][l]&&(void 0===i[l]?r[h][l]=n[h][l]:(r[h][l]={},t(i[l],n[h][l],r[h][l],s+1)))}else tA(i,!0)&&!i.nodeType?(r[h]=tS(i)?[]:{},t(i,n[h]||{},r[h],s+1),0===Object.keys(r[h]).length&&("colorAxis"!==h||0!==s)&&delete r[h]):(e[h]!==n[h]||h in e&&!(h in n))&&"__proto__"!==h&&"constructor"!==h&&(r[h]=a[h])})}(t,e,n,0),n},discardElement:function(t){var e;null===(e=null==t?void 0:t.parentElement)||void 0===e||e.removeChild(t)},erase:function(t,e){for(var i=t.length;i--;)if(t[i]===e){t.splice(i,1);break}},error:tk,extend:tB,extendClass:function(t,e){var i=function(){};return i.prototype=new t,tB(i.prototype,e),i},find:tN,fireEvent:tX,getAlignFactor:function(t){return void 0===t&&(t=""),({center:.5,right:1,middle:.5,bottom:1})[t]||0},getClosestDistance:function(t,e){var i,o,n,r,s=!e;return t.forEach(function(t){if(t.length>1)for(r=o=t.length-1;r>0;r--)(n=t[r]-t[r-1])<0&&!s?(null==e||e(),e=void 0):n&&(void 0===i||n<i)&&(i=n)}),i},getMagnitude:tz,getNestedProperty:function(t,e){for(var i=t.split(".");i.length&&tP(e);){var o=i.shift();if(void 0===o||"__proto__"===o)return;if("this"===o){var n=void 0;return tA(e)&&(n=e["@this"]),null!=n?n:e}var r=e[o.replace(/[\\'"]/g,"")];if(!tP(r)||"function"==typeof r||"number"==typeof r.nodeType||r===tb)return;e=r}return e},getStyle:function t(e,i,o){if("width"===i){var n,r,s=Math.min(e.offsetWidth,e.scrollWidth),a=null===(n=e.getBoundingClientRect)||void 0===n?void 0:n.call(e).width;return a<s&&a>=s-1&&(s=Math.floor(a)),Math.max(0,s-(t(e,"padding-left",!0)||0)-(t(e,"padding-right",!0)||0))}if("height"===i)return Math.max(0,Math.min(e.offsetHeight,e.scrollHeight)-(t(e,"padding-top",!0)||0)-(t(e,"padding-bottom",!0)||0));var h=tb.getComputedStyle(e,void 0);return h&&(r=h.getPropertyValue(i),tD(o,"opacity"!==i)&&(r=tM(r))),r},insertItem:function(t,e){var i,o=t.options.index,n=e.length;for(i=t.options.isInternal?n:0;i<n+1;i++)if(!e[i]||tO(o)&&o<tD(e[i].options.index,e[i]._i)||e[i].options.isInternal){e.splice(i,0,t);break}return i},isArray:tS,isClass:tC,isDOMElement:tT,isFunction:function(t){return"function"==typeof t},isNumber:tO,isObject:tA,isString:tw,merge:function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o,n=tm([t],e,!0),r={},s=function(t,e){return"object"!=typeof t&&(t={}),tW(e,function(i,o){"__proto__"!==o&&"constructor"!==o&&(!tA(i,!0)||tC(i)||tT(i)?t[o]=e[o]:t[o]=s(t[o]||{},i))}),t};!0===t&&(r=n[1],n=Array.prototype.slice.call(n,2));var a=n.length;for(o=0;o<a;o++)r=s(r,n[o]);return r},normalizeTickInterval:function(t,e,i,o,n){var r,s=t;i=tD(i,tz(t));var a=t/i;for(!e&&(e=n?[1,1.2,1.5,2,2.5,3,4,5,6,8,10]:[1,2,2.5,5,10],!1===o&&(1===i?e=e.filter(function(t){return t%1==0}):i<=.1&&(e=[1/i]))),r=0;r<e.length&&(s=e[r],(!n||!(s*i>=t))&&(n||!(a<=(e[r]+(e[r+1]||e[r]))/2)));r++);return tR(s*i,-Math.round(Math.log(.001)/Math.LN10))},objectEach:tW,offset:function(t){var e=tx.documentElement,i=t.parentElement||t.parentNode?t.getBoundingClientRect():{top:0,left:0,width:0,height:0};return{top:i.top+(tb.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(tb.pageXOffset||e.scrollLeft)-(e.clientLeft||0),width:i.width,height:i.height}},pad:function(t,e,i){return Array((e||2)+1-String(t).replace("-","").length).join(i||"0")+t},pick:tD,pInt:tM,pushUnique:function(t,e){return 0>t.indexOf(e)&&!!t.push(e)},relativeLength:function(t,e,i){return/%$/.test(t)?e*parseFloat(t)/100+(i||0):parseFloat(t)},removeEvent:tG,replaceNested:function(t){for(var e,i,o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];do{e=t;for(var r=0;r<o.length;r++)i=o[r],t=t.replace(i[0],i[1])}while(t!==e);return t},splat:tL,stableSort:function(t,e){var i,o,n=t.length;for(o=0;o<n;o++)t[o].safeI=o;for(t.sort(function(t,o){return 0===(i=e(t,o))?t.safeI-o.safeI:i}),o=0;o<n;o++)delete t[o].safeI},syncTimeout:function(t,e,i){return e>0?setTimeout(t,e,i):(t.call(0,i),-1)},timeUnits:{millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5,month:24192e5,year:314496e5},ucfirst:function(t){return tw(t)?t.substring(0,1).toUpperCase()+t.substring(1):String(t)},uniqueKey:tH,useSerialIds:function(t){return G=tD(t,G)},wrap:function(t,e,i){var o=t[e];t[e]=function(){var t=arguments,e=this;return i.apply(this,[function(){return o.apply(e,arguments.length?arguments:t)}].concat([].slice.call(arguments)))}}},tY=tv.pageLang,t_=tv.win,tj=tF.defined,tU=tF.error,tV=tF.extend,tZ=tF.isNumber,tq=tF.isObject,tK=tF.isString,t$=tF.merge,tJ=tF.objectEach,tQ=tF.pad,t0=tF.splat,t1=tF.timeUnits,t2=tF.ucfirst,t3=tv.isSafari&&t_.Intl&&!t_.Intl.DateTimeFormat.prototype.formatRange,t5=function(){function t(t,e){this.options={timezone:"UTC"},this.variableTimezone=!1,this.Date=t_.Date,this.update(t),this.lang=e}return t.prototype.update=function(t){var e=this;void 0===t&&(t={}),this.dTLCache={},this.options=t=t$(!0,this.options,t);var i=t.timezoneOffset,o=t.useUTC;this.Date=t.Date||t_.Date||Date;var n=t.timezone;tj(o)&&(n=o?"UTC":void 0),i&&i%60==0&&(n="Etc/GMT"+(i>0?"+":"")+i/60),this.variableTimezone="UTC"!==n&&(null==n?void 0:n.indexOf("Etc/GMT"))!==0,this.timezone=n,["months","shortMonths","weekdays","shortWeekdays"].forEach(function(t){var i=/months/i.test(t),o=/short/.test(t),n={timeZone:"UTC"};n[i?"month":"weekday"]=o?"short":"long",e[t]=(i?[0,1,2,3,4,5,6,7,8,9,10,11]:[3,4,5,6,7,8,9]).map(function(t){return e.dateFormat(n,(i?31:1)*24*36e5*t)})})},t.prototype.toParts=function(t){var e=this.dateTimeFormat({weekday:"narrow",day:"numeric",month:"numeric",year:"numeric",hour:"numeric",minute:"numeric",second:"numeric"},t,"es").split(/(?:, | |\/|:)/g),i=e[0],o=e[1],n=e[2];return[e[3],+n-1,o,e[4],e[5],e[6],Math.floor(Number(t)||0)%1e3,"DLMXJVS".indexOf(i)].map(Number)},t.prototype.dateTimeFormat=function(t,e,i){void 0===i&&(i=this.options.locale||tY);var o,n=JSON.stringify(t)+i;tK(t)&&(t=this.str2dtf(t));var r=this.dTLCache[n];if(!r){null!==(o=t.timeZone)&&void 0!==o||(t.timeZone=this.timezone);try{r=new Intl.DateTimeFormat(i,t)}catch(e){/Invalid time zone/i.test(e.message)?(tU(34),t.timeZone="UTC",r=new Intl.DateTimeFormat(i,t)):tU(e.message,!1)}}return this.dTLCache[n]=r,(null==r?void 0:r.format(e))||""},t.prototype.str2dtf=function(t,e){void 0===e&&(e={});var i={L:{fractionalSecondDigits:3},S:{second:"2-digit"},M:{minute:"numeric"},H:{hour:"2-digit"},k:{hour:"numeric"},E:{weekday:"narrow"},a:{weekday:"short"},A:{weekday:"long"},d:{day:"2-digit"},e:{day:"numeric"},b:{month:"short"},B:{month:"long"},m:{month:"2-digit"},o:{month:"numeric"},y:{year:"2-digit"},Y:{year:"numeric"}};return Object.keys(i).forEach(function(o){-1!==t.indexOf(o)&&tV(e,i[o])}),e},t.prototype.makeTime=function(t,e,i,o,n,r,s){void 0===i&&(i=1),void 0===o&&(o=0);var a=this.Date.UTC(t,e,i,o,n||0,r||0,s||0);if("UTC"!==this.timezone){var h=this.getTimezoneOffset(a);if(a+=h,-1!==[2,3,8,9,10,11].indexOf(e)&&(o<5||o>20)){var l=this.getTimezoneOffset(a);h!==l?a+=l-h:h-36e5!==this.getTimezoneOffset(a-36e5)||t3||(a-=36e5)}}return a},t.prototype.parse=function(t){if(!tK(t))return null!=t?t:void 0;var e=(t=t.replace(/\//g,"-").replace(/(GMT|UTC)/,"")).indexOf("Z")>-1||/([+-][0-9]{2}):?[0-9]{2}$/.test(t),i=/^[0-9]{4}-[0-9]{2}(-[0-9]{2}|)$/.test(t);e||i||(t+="Z");var o=Date.parse(t);if(tZ(o))return o+(!e||i?this.getTimezoneOffset(o):0)},t.prototype.getTimezoneOffset=function(t){if("UTC"!==this.timezone){var e=this.dateTimeFormat({timeZoneName:"shortOffset"},t,"en").split(/(GMT|:)/).map(Number),i=(e[0],e[1],e[2]),o=(e[3],e[4]),n=-(36e5*(i+(void 0===o?0:o)/60));if(tZ(n))return n}return 0},t.prototype.dateFormat=function(t,e,i){var o,n=this.lang;if(!tj(e)||isNaN(e))return(null==n?void 0:n.invalidDate)||"";if(tK(t=null!=t?t:"%Y-%m-%d %H:%M:%S"))for(var r=/%\[([a-zA-Z]+)\]/g,s=void 0;s=r.exec(t);)t=t.replace(s[0],this.dateTimeFormat(s[1],e,null==n?void 0:n.locale));if(tK(t)&&-1!==t.indexOf("%")){var a=this,h=this.toParts(e),l=h[0],d=h[1],c=h[2],p=h[3],u=h[4],f=h[5],g=h[6],v=h[7],m=(null==n?void 0:n.weekdays)||this.weekdays,y=(null==n?void 0:n.shortWeekdays)||this.shortWeekdays,x=(null==n?void 0:n.months)||this.months,b=(null==n?void 0:n.shortMonths)||this.shortMonths;tJ(tV({a:y?y[v]:m[v].substr(0,3),A:m[v],d:tQ(c),e:tQ(c,2," "),w:v,v:null!==(o=null==n?void 0:n.weekFrom)&&void 0!==o?o:"",b:b[d],B:x[d],m:tQ(d+1),o:d+1,y:l.toString().substr(2,2),Y:l,H:tQ(p),k:p,I:tQ(p%12||12),l:p%12||12,M:tQ(u),p:p<12?"AM":"PM",P:p<12?"am":"pm",S:tQ(f),L:tQ(g,3)},tv.dateFormats),function(i,o){if(tK(t))for(;-1!==t.indexOf("%"+o);)t=t.replace("%"+o,"function"==typeof i?i.call(a,e):i)})}else if(tq(t)){var k=(this.getTimezoneOffset(e)||0)/36e5,M=this.timezone||"Etc/GMT"+(k>=0?"+":"")+k,w=t.prefix,S=t.suffix;t=(void 0===w?"":w)+this.dateTimeFormat(tV({timeZone:M},t),e)+(void 0===S?"":S)}return i?t2(t):t},t.prototype.resolveDTLFormat=function(t){return tq(t,!0)?tq(t,!0)&&void 0===t.main?{main:t}:t:{main:(t=t0(t))[0],from:t[1],to:t[2]}},t.prototype.getDateFormat=function(t,e,i,o){var n=this.dateFormat("%m-%d %H:%M:%S.%L",e),r="01-01 00:00:00.000",s={millisecond:15,second:12,minute:9,hour:6,day:3},a="millisecond",h=a;for(a in t1){if(t&&t===t1.week&&+this.dateFormat("%w",e)===i&&n.substr(6)===r.substr(6)){a="week";break}if(t&&t1[a]>t){a=h;break}if(s[a]&&n.substr(s[a])!==r.substr(s[a]))break;"week"!==a&&(h=a)}return this.resolveDTLFormat(o[a]).main},t}(),t6=(h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),t9=tF.defined,t4=tF.extend,t8=tF.timeUnits,t7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return t6(e,t),e.prototype.getTimeTicks=function(t,e,i,o){var n,r=this,s=[],a={},h=t.count,l=void 0===h?1:h,d=t.unitRange,c=r.toParts(e),p=c[0],u=c[1],f=c[2],g=c[3],v=c[4],m=c[5],y=(e||0)%1e3;if(null!=o||(o=1),t9(e)){if(y=d>=t8.second?0:l*Math.floor(y/l),d>=t8.second&&(m=d>=t8.minute?0:l*Math.floor(m/l)),d>=t8.minute&&(v=d>=t8.hour?0:l*Math.floor(v/l)),d>=t8.hour&&(g=d>=t8.day?0:l*Math.floor(g/l)),d>=t8.day&&(f=d>=t8.month?1:Math.max(1,l*Math.floor(f/l))),d>=t8.month&&(u=d>=t8.year?0:l*Math.floor(u/l)),d>=t8.year&&(p-=p%l),d===t8.week){l&&(e=r.makeTime(p,u,f,g,v,m,y));var x=this.dateTimeFormat({timeZone:this.timezone,weekday:"narrow"},e,"es"),b="DLMXJVS".indexOf(x);f+=-b+o+(b<o?-7:0)}e=r.makeTime(p,u,f,g,v,m,y),r.variableTimezone&&t9(i)&&(n=i-e>4*t8.month||r.getTimezoneOffset(e)!==r.getTimezoneOffset(i));for(var k=e,M=1;k<i;)s.push(k),d===t8.year?k=r.makeTime(p+M*l,0):d===t8.month?k=r.makeTime(p,u+M*l):n&&(d===t8.day||d===t8.week)?k=r.makeTime(p,u,f+M*l*(d===t8.day?1:7)):n&&d===t8.hour&&l>1?k=r.makeTime(p,u,f,g+M*l):k+=d*l,M++;s.push(k),d<=t8.hour&&s.length<1e4&&s.forEach(function(t){t%18e5==0&&"000000000"===r.dateFormat("%H%M%S%L",t)&&(a[t]="day")})}return s.info=t4(t,{higherRanks:a,totalRange:d*l}),s},e}(t5),et=function(){return(et=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},ee=tv.isTouchDevice,ei=tF.fireEvent,eo=tF.merge,en={colors:["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{weekFrom:"week from",chartTitle:"Chart title",locale:void 0,loading:"Loading...",months:void 0,seriesName:"Series {add index 1}",shortMonths:void 0,weekdays:void 0,numericSymbols:["k","M","G","T","P","E"],pieSliceName:"Slice",resetZoom:"Reset zoom",yAxisTitle:"Values",resetZoomTitle:"Reset zoom level 1:1"},global:{buttonTheme:{fill:"#f7f7f7",padding:8,r:2,stroke:"#cccccc","stroke-width":1,style:{color:"#333333",cursor:"pointer",fontSize:"0.8em",fontWeight:"normal"},states:{hover:{fill:"#e6e6e6"},select:{fill:"#e6e9ff",style:{color:"#000000",fontWeight:"bold"}},disabled:{style:{color:"#cccccc"}}}}},time:{Date:void 0,timezone:"UTC",timezoneOffset:0,useUTC:void 0},chart:{alignThresholds:!1,panning:{enabled:!1,type:"x"},styledMode:!1,borderRadius:0,colorCount:10,allowMutatingData:!0,ignoreHiddenSeries:!0,spacing:[10,10,15,10],resetZoomButton:{theme:{},position:{}},reflow:!0,type:"line",zooming:{singleTouch:!1,resetButton:{theme:{zIndex:6},position:{align:"right",x:-10,y:10}}},width:null,height:null,borderColor:"#334eff",backgroundColor:"#ffffff",plotBorderColor:"#cccccc"},title:{style:{color:"#333333",fontWeight:"bold"},text:"Chart title",margin:15,minScale:.67},subtitle:{style:{color:"#666666",fontSize:"0.8em"},text:""},caption:{margin:15,style:{color:"#666666",fontSize:"0.8em"},text:"",align:"left",verticalAlign:"bottom"},plotOptions:{},legend:{enabled:!0,align:"center",alignColumns:!0,className:"highcharts-no-tooltip",events:{},layout:"horizontal",itemMarginBottom:2,itemMarginTop:2,labelFormatter:function(){return this.name},borderColor:"#999999",borderRadius:0,navigation:{style:{fontSize:"0.8em"},activeColor:"#0022ff",inactiveColor:"#cccccc"},itemStyle:{color:"#333333",cursor:"pointer",fontSize:"0.8em",textDecoration:"none",textOverflow:"ellipsis"},itemHoverStyle:{color:"#000000"},itemHiddenStyle:{color:"#666666",textDecoration:"line-through"},shadow:!1,itemCheckboxStyle:{position:"absolute",width:"13px",height:"13px"},squareSymbol:!0,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontSize:"0.8em",fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:"relative",top:"45%"},style:{position:"absolute",backgroundColor:"#ffffff",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:{duration:300,easing:function(t){return Math.sqrt(1-Math.pow(t-1,2))}},borderRadius:3,dateTimeLabelFormats:{millisecond:"%[AebHMSL]",second:"%[AebHMS]",minute:"%[AebHM]",hour:"%[AebHM]",day:"%[AebY]",week:"%v %[AebY]",month:"%[BY]",year:"%Y"},footerFormat:"",headerShape:"callout",hideDelay:500,padding:8,position:{x:0,y:3},shared:!1,snap:ee?25:10,headerFormat:'<span style="font-size: 0.8em">{ucfirst point.key}</span><br/>',pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.y}</b><br/>',backgroundColor:"#ffffff",borderWidth:void 0,stickOnContact:!1,style:{color:"#333333",cursor:"default",fontSize:"0.8em"},useHTML:!1},credits:{enabled:!0,href:"https://www.highcharts.com?credits",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#999999",fontSize:"0.6em"},text:"Highcharts.com"}},er=new t7(en.time,en.lang),es=function(){return en},ea=function(t){var e;return ei(tv,"setOptions",{options:t}),eo(!0,en,t),t.time&&er.update(en.time),t.lang&&"locale"in t.lang&&er.update({locale:t.lang.locale}),(null===(e=t.lang)||void 0===e?void 0:e.chartTitle)&&(en.title=et(et({},en.title),{text:t.lang.chartTitle})),en},eh=tv.win,el=tF.isNumber,ed=tF.isString,ec=tF.merge,ep=tF.pInt,eu=tF.defined,ef=function(t,e,i){return"color-mix(in srgb,".concat(t,",").concat(e," ").concat(100*i,"%)")},eg=function(t){return ed(t)&&!!t&&"none"!==t},ev=function(){var t;function e(t){this.rgba=[NaN,NaN,NaN,NaN],this.input=t;var i,o,n,r,s=tv.Color;if(s&&s!==e)return new s(t);if("object"==typeof t&&void 0!==t.stops)this.stops=t.stops.map(function(t){return new e(t[1])});else if("string"==typeof t)for(this.input=t=e.names[t.toLowerCase()]||t,n=e.parsers.length;n--&&!o;)(i=(r=e.parsers[n]).regex.exec(t))&&(o=r.parse(i));o&&(this.rgba=o)}return e.parse=function(t){return t?new e(t):e.None},e.prototype.get=function(t){var e=this.input,i=this.rgba;if(this.output)return this.output;if("object"==typeof e&&void 0!==this.stops){var o=ec(e);return o.stops=[].slice.call(o.stops),this.stops.forEach(function(e,i){o.stops[i]=[o.stops[i][0],e.get(t)]}),o}return i&&el(i[0])?"rgb"!==t&&(t||1!==i[3])?"a"===t?"".concat(i[3]):"rgba("+i.join(",")+")":"rgb("+i[0]+","+i[1]+","+i[2]+")":e},e.prototype.brighten=function(t){var i=this.rgba;if(this.stops)this.stops.forEach(function(e){e.brighten(t)});else if(el(t)&&0!==t){if(el(i[0]))for(var o=0;o<3;o++)i[o]+=ep(255*t),i[o]<0&&(i[o]=0),i[o]>255&&(i[o]=255);else e.useColorMix&&eg(this.input)&&(this.output=ef(this.input,t>0?"white":"black",Math.abs(t)))}return this},e.prototype.setOpacity=function(t){return this.rgba[3]=t,this},e.prototype.tweenTo=function(t,i){var o=this.rgba,n=t.rgba;if(!el(o[0])||!el(n[0]))return e.useColorMix&&eg(this.input)&&eg(t.input)&&i<.99?ef(this.input,t.input,i):t.input||"none";var r=1!==n[3]||1!==o[3],s=function(t,e){return t+(o[e]-t)*(1-i)},a=n.slice(0,3).map(s).map(Math.round);return r&&a.push(s(n[3],3)),(r?"rgba(":"rgb(")+a.join(",")+")"},e.names={white:"#ffffff",black:"#000000"},e.parsers=[{regex:/rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?(?:\.\d+)?)\s*\)/,parse:function(t){return[ep(t[1]),ep(t[2]),ep(t[3]),parseFloat(t[4],10)]}},{regex:/rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)/,parse:function(t){return[ep(t[1]),ep(t[2]),ep(t[3]),1]}},{regex:/^#([a-f0-9])([a-f0-9])([a-f0-9])([a-f0-9])?$/i,parse:function(t){return[ep(t[1]+t[1],16),ep(t[2]+t[2],16),ep(t[3]+t[3],16),eu(t[4])?ep(t[4]+t[4],16)/255:1]}},{regex:/^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})?$/i,parse:function(t){return[ep(t[1],16),ep(t[2],16),ep(t[3],16),eu(t[4])?ep(t[4],16)/255:1]}}],e.useColorMix=null===(t=eh.CSS)||void 0===t?void 0:t.supports("color","color-mix(in srgb,red,blue 9%)"),e.None=new e(""),e}(),em=ev.parse,ey=tv.win,ex=tF.isNumber,eb=tF.objectEach,ek=function(){function t(t,e,i){this.pos=NaN,this.options=e,this.elem=t,this.prop=i}return t.prototype.dSetter=function(){var t=this.paths,e=null==t?void 0:t[0],i=null==t?void 0:t[1],o=this.now||0,n=[];if(1!==o&&e&&i){if(e.length===i.length&&o<1)for(var r=0;r<i.length;r++){for(var s=e[r],a=i[r],h=[],l=0;l<a.length;l++){var d=s[l],c=a[l];ex(d)&&ex(c)&&("A"!==a[0]||4!==l&&5!==l)?h[l]=d+o*(c-d):h[l]=c}n.push(h)}else n=i}else n=this.toD||[];this.elem.attr("d",n,void 0,!0)},t.prototype.update=function(){var t=this.elem,e=this.prop,i=this.now,o=this.options.step;this[e+"Setter"]?this[e+"Setter"]():t.attr?t.element&&t.attr(e,i,null,!0):t.style[e]=i+this.unit,o&&o.call(t,i,this)},t.prototype.run=function(e,i,o){var n=this,r=n.options,s=function(t){return!s.stopped&&n.step(t)},a=ey.requestAnimationFrame||function(t){setTimeout(t,13)},h=function(){for(var e=0;e<t.timers.length;e++)t.timers[e]()||t.timers.splice(e--,1);t.timers.length&&a(h)};e!==i||this.elem["forceAnimate:"+this.prop]?(this.startTime=+new Date,this.start=e,this.end=i,this.unit=o,this.now=this.start,this.pos=0,s.elem=this.elem,s.prop=this.prop,s()&&1===t.timers.push(s)&&a(h)):(delete r.curAnim[this.prop],r.complete&&0===Object.keys(r.curAnim).length&&r.complete.call(this.elem))},t.prototype.step=function(t){var e,i,o=+new Date,n=this.options,r=this.elem,s=n.complete,a=n.duration,h=n.curAnim;return r.attr&&!r.element?e=!1:t||o>=a+this.startTime?(this.now=this.end,this.pos=1,this.update(),h[this.prop]=!0,i=!0,eb(h,function(t){!0!==t&&(i=!1)}),i&&s&&s.call(r),e=!1):(this.pos=n.easing((o-this.startTime)/a),this.now=this.start+(this.end-this.start)*this.pos,this.update(),e=!0),e},t.prototype.initPath=function(t,e,i){var o,n,r,s,a=t.startX,h=t.endX,l=i.slice(),d=t.isArea,c=d?2:1,p=e&&i.length>e.length&&i.hasStackedCliffs,u=null==e?void 0:e.slice();if(!u||p)return[l,l];function f(t,e){for(;t.length<n;){var i=t[0],o=e[n-t.length];if(o&&"M"===i[0]&&("C"===o[0]?t[0]=["C",i[1],i[2],i[1],i[2],i[1],i[2]]:t[0]=["L",i[1],i[2]]),t.unshift(i),d){var r=t.pop();t.push(t[t.length-1],r)}}}function g(t){for(;t.length<n;){var e=t[Math.floor(t.length/c)-1].slice();if("C"===e[0]&&(e[1]=e[5],e[2]=e[6]),d){var i=t[Math.floor(t.length/c)].slice();t.splice(t.length/2,0,e,i)}else t.push(e)}}if(a&&h&&h.length){for(r=0;r<a.length;r++){if(a[r]===h[0]){o=r;break}if(a[0]===h[h.length-a.length+r]){o=r,s=!0;break}if(a[a.length-1]===h[h.length-a.length+r]){o=a.length-r;break}}void 0===o&&(u=[])}return u.length&&ex(o)&&(n=l.length+o*c,s?(f(u,l),g(l)):(f(l,u),g(u))),[u,l]},t.prototype.fillSetter=function(){t.prototype.strokeSetter.apply(this,arguments)},t.prototype.strokeSetter=function(){this.elem.attr(this.prop,em(this.start).tweenTo(em(this.end),this.pos),void 0,!0)},t.timers=[],t}(),eM=tF.defined,ew=tF.getStyle,eS=tF.isArray,eA=tF.isNumber,eT=tF.isObject,eC=tF.merge,eO=tF.objectEach,eP=tF.pick;function eE(t){return eT(t)?eC({duration:500,defer:0},t):{duration:500*!!t,defer:0}}function eL(t,e){for(var i=ek.timers.length;i--;)ek.timers[i].elem!==t||e&&e!==ek.timers[i].prop||(ek.timers[i].stopped=!0)}var eB=function(t,e,i){var o,n,r,s,a="";eT(i)||(s=arguments,i={duration:s[2],easing:s[3],complete:s[4]}),eA(i.duration)||(i.duration=400),i.easing="function"==typeof i.easing?i.easing:Math[i.easing]||Math.easeInOutSine,i.curAnim=eC(e),eO(e,function(s,h){eL(t,h),r=new ek(t,i,h),n=void 0,"d"===h&&eS(e.d)?(r.paths=r.initPath(t,t.pathArray,e.d),r.toD=e.d,o=0,n=1):t.attr?o=t.attr(h):(o=parseFloat(ew(t,h))||0,"opacity"!==h&&(a="px")),n||(n=s),"string"==typeof n&&n.match("px")&&(n=n.replace(/px/g,"")),r.run(o,n,a)})},eD=function(t,e,i){var o=eE(e),n=i?[i]:t.series,r=0,s=0;return n.forEach(function(t){var i=eE(t.options.animation);r=eT(e)&&eM(e.defer)?o.defer:Math.max(r,i.duration+i.defer),s=Math.min(o.duration,i.duration)}),t.renderer.forExport&&(r=0),{defer:Math.max(0,r-s),duration:Math.min(r,s)}},eI=function(t,e){e.renderer.globalAnimation=eP(t,e.options.chart.animation,!0)},ez=tv.SVG_NS,eR=tv.win,eN=tF.attr,eW=tF.createElement,eG=tF.css,eX=tF.error,eH=tF.isFunction,eF=tF.isString,eY=tF.objectEach,e_=tF.splat,ej=eR.trustedTypes,eU=ej&&eH(ej.createPolicy)&&ej.createPolicy("highcharts",{createHTML:function(t){return t}}),eV=eU?eU.createHTML(""):"",eZ=function(){function t(t){this.nodes="string"==typeof t?this.parseMarkup(t):t}return t.filterUserAttributes=function(e){return eY(e,function(i,o){var n=!0;-1===t.allowedAttributes.indexOf(o)&&(n=!1),-1!==["background","dynsrc","href","lowsrc","src"].indexOf(o)&&(n=eF(i)&&t.allowedReferences.some(function(t){return 0===i.indexOf(t)})),n||(eX(33,!1,void 0,{"Invalid attribute in config":"".concat(o)}),delete e[o]),eF(i)&&e[o]&&(e[o]=i.replace(/</g,"&lt;"))}),e},t.parseStyle=function(t){return t.split(";").reduce(function(t,e){var i=e.split(":").map(function(t){return t.trim()}),o=i.shift();return o&&i.length&&(t[o.replace(/-([a-z])/g,function(t){return t[1].toUpperCase()})]=i.join(":")),t},{})},t.setElementHTML=function(e,i){e.innerHTML=t.emptyHTML,i&&new t(i).addToDOM(e)},t.prototype.addToDOM=function(e){return function e(i,o){var n;return e_(i).forEach(function(i){var r,s=i.tagName,a=i.textContent?tv.doc.createTextNode(i.textContent):void 0,h=t.bypassHTMLFiltering;if(s){if("#text"===s)r=a;else if(-1!==t.allowedTags.indexOf(s)||h){var l="svg"===s?ez:o.namespaceURI||ez,d=tv.doc.createElementNS(l,s),c=i.attributes||{};eY(i,function(t,e){"tagName"!==e&&"attributes"!==e&&"children"!==e&&"style"!==e&&"textContent"!==e&&(c[e]=t)}),eN(d,h?c:t.filterUserAttributes(c)),i.style&&eG(d,i.style),a&&d.appendChild(a),e(i.children||[],d),r=d}else eX(33,!1,void 0,{"Invalid tagName in config":s})}r&&o.appendChild(r),n=r}),n}(this.nodes,e)},t.prototype.parseMarkup=function(e){var i,o=[];e=e.trim().replace(/ style=(["'])/g," data-style=$1");try{i=new DOMParser().parseFromString(eU?eU.createHTML(e):e,"text/html")}catch(t){}if(!i){var n=eW("div");n.innerHTML=e,i={body:n}}var r=function(e,i){var o=e.nodeName.toLowerCase(),n={tagName:o};"#text"===o&&(n.textContent=e.textContent||"");var s=e.attributes;if(s){var a={};[].forEach.call(s,function(e){"data-style"===e.name?n.style=t.parseStyle(e.value):a[e.name]=e.value}),n.attributes=a}if(e.childNodes.length){var h=[];[].forEach.call(e.childNodes,function(t){r(t,h)}),h.length&&(n.children=h)}i.push(n)};return[].forEach.call(i.body.childNodes,function(t){return r(t,o)}),o},t.allowedAttributes=["alt","aria-controls","aria-describedby","aria-expanded","aria-haspopup","aria-hidden","aria-label","aria-labelledby","aria-live","aria-pressed","aria-readonly","aria-roledescription","aria-selected","class","clip-path","color","colspan","cx","cy","d","dx","dy","disabled","fill","filterUnits","flood-color","flood-opacity","height","href","id","in","in2","markerHeight","markerWidth","offset","opacity","operator","orient","padding","paddingLeft","paddingRight","patternUnits","r","radius","refX","refY","role","scope","slope","src","startOffset","stdDeviation","stroke","stroke-linecap","stroke-width","style","tableValues","result","rowspan","summary","target","tabindex","text-align","text-anchor","textAnchor","textLength","title","type","valign","width","x","x1","x2","xlink:href","y","y1","y2","zIndex"],t.allowedReferences=["https://","http://","mailto:","/","../","./","#"],t.allowedTags=["a","abbr","b","br","button","caption","circle","clipPath","code","dd","defs","div","dl","dt","em","feComponentTransfer","feComposite","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feMorphology","feOffset","feMerge","feMergeNode","filter","h1","h2","h3","h4","h5","h6","hr","i","img","li","linearGradient","marker","ol","p","path","pattern","pre","rect","small","span","stop","strong","style","sub","sup","svg","table","text","textPath","thead","title","tbody","tspan","td","th","tr","u","ul","#text"],t.emptyHTML=eV,t.bypassHTMLFiltering=!1,t}(),eq=tv.pageLang,eK=tF.extend,e$=tF.getNestedProperty,eJ=tF.isArray,eQ=tF.isNumber,e0=tF.isObject,e1=tF.isString,e2=tF.pick,e3={add:function(t,e){return t+e},divide:function(t,e){return 0!==e?t/e:""},eq:function(t,e){return t==e},each:function(t){var e=arguments[arguments.length-1];return!!eJ(t)&&t.map(function(i,o){return e9(e.body,eK(e0(i)?i:{"@this":i},{"@index":o,"@first":0===o,"@last":o===t.length-1}))}).join("")},ge:function(t,e){return t>=e},gt:function(t,e){return t>e},if:function(t){return!!t},le:function(t,e){return t<=e},lt:function(t,e){return t<e},multiply:function(t,e){return t*e},ne:function(t,e){return t!=e},subtract:function(t,e){return t-e},ucfirst:tF.ucfirst,unless:function(t){return!t}},e5={},e6=function(t){return/^["'].+["']$/.test(t)};function e9(t,e,i){void 0===t&&(t="");for(var o,n,r,s,a=/\{([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'’= #\(\)]+)\}/g,h=/\(([a-zA-Z\u00C0-\u017F\d:\.,;\-\/<>\[\]%_@+"'= ]+)\)/g,l=[],d=/f$/,c=/\.(\d)/,p=(null===(o=null==i?void 0:i.options)||void 0===o?void 0:o.lang)||en.lang,u=(null==i?void 0:i.time)||er,f=(null==i?void 0:i.numberFormatter)||e4,g=function(t){var i;return void 0===t&&(t=""),"true"===t||"false"!==t&&((i=Number(t)).toString()===t?i:e6(t)?t.slice(1,-1):e$(t,e))},v=0;null!==(n=a.exec(t));){var m=n,y=h.exec(n[1]);y&&(n=y,s=!0),(null==r?void 0:r.isBlock)||(r={ctx:e,expression:n[1],find:n[0],isBlock:"#"===n[1].charAt(0),start:n.index,startInner:n.index+n[0].length,length:n[0].length});var x=(r.isBlock?m:n)[1].split(" ")[0].replace("#","");e3[x]&&(r.isBlock&&x===r.fn&&v++,r.fn||(r.fn=x));var b="else"===n[1];if(r.isBlock&&r.fn&&(n[1]==="/".concat(r.fn)||b)){if(v)!b&&v--;else{var k=r.startInner,M=t.substr(k,n.index-k);void 0===r.body?(r.body=M,r.startInner=n.index+n[0].length):r.elseBody=M,r.find+=M+n[0],b||(l.push(r),r=void 0)}}else r.isBlock||l.push(r);if(y&&!(null==r?void 0:r.isBlock))break}return l.forEach(function(o){var n,r,s=o.body,a=o.elseBody,l=o.expression,v=o.fn;if(v){var m=[o],y=[],x=l.length,b=0,k=void 0;for(r=0;r<=x;r++){var M=l.charAt(r);k||'"'!==M&&"'"!==M?k===M&&(k=""):k=M,k||" "!==M&&r!==x||(y.push(l.substr(b,r-b)),b=r+1)}for(r=e3[v].length;r--;)m.unshift(g(y[r+1]));n=e3[v].apply(e,m),o.isBlock&&"boolean"==typeof n&&(n=e9(n?s:a,e,i))}else{var w=e6(l)?[l]:l.split(":");if(n=g(w.shift()||""),w.length&&"number"==typeof n){var S=w.join(":");if(d.test(S)){var A=parseInt((S.match(c)||["","-1"])[1],10);null!==n&&(n=f(n,A,p.decimalPoint,S.indexOf(",")>-1?p.thousandsSep:""))}else n=u.dateFormat(S,n)}h.lastIndex=0,h.test(o.find)&&e1(n)&&(n='"'.concat(n,'"'))}t=t.replace(o.find,e2(n,""))}),s?e9(t,e,i):t}function e4(t,e,i,o){e*=1;var n,r,s,a,h=(t=+t||0).toString().split("e").map(Number),l=h[0],d=h[1],c=(null===(n=this===null||void 0===this?void 0:this.options)||void 0===n?void 0:n.lang)||en.lang,p=(t.toString().split(".")[1]||"").split("e")[0].length,u=e,f={};null!=i||(i=c.decimalPoint),null!=o||(o=c.thousandsSep),-1===e?e=Math.min(p,20):eQ(e)?e&&d<0&&((a=e+d)>=0?(l=+l.toExponential(a).split("e")[0],e=a):(l=Math.floor(l),t=e<20?+(l*Math.pow(10,d)).toFixed(e):0,d=0)):e=2,d&&(null!=e||(e=2),t=l),eQ(e)&&e>=0&&(f.minimumFractionDigits=e,f.maximumFractionDigits=e),""===o&&(f.useGrouping=!1);var g=o||i,v=g?"en":(this===null||void 0===this?void 0:this.locale)||c.locale||eq,m=JSON.stringify(f)+v;return s=(null!==(r=e5[m])&&void 0!==r?r:e5[m]=new Intl.NumberFormat(v,f)).format(t),g&&(s=s.replace(/([,\.])/g,"_$1").replace(/_\,/g,null!=o?o:",").replace("_.",null!=i?i:".")),(e||0!=+s)&&(!(d<0)||u)||(s="0"),d&&0!=+s&&(s+="e"+(d<0?"":"+")+d),s}var e8={dateFormat:function(t,e,i){return er.dateFormat(t,e,i)},format:e9,helpers:e3,numberFormat:e4};(l=X||(X={})).rendererTypes={},l.getRendererType=function(t){return void 0===t&&(t=d),l.rendererTypes[t]||l.rendererTypes[d]},l.registerRendererType=function(t,e,i){l.rendererTypes[t]=e,(!d||i)&&(d=t,tv.Renderer=e)};var e7=X,it=tF.clamp,ie=tF.pick,ii=tF.pushUnique,io=tF.stableSort;(H||(H={})).distribute=function t(e,i,o){var n,r,s,a,h,l,d=e,c=d.reducedLen||i,p=function(t,e){return t.target-e.target},u=[],f=e.length,g=[],v=u.push,m=!0,y=0;for(n=f;n--;)y+=e[n].size;if(y>c){for(io(e,function(t,e){return(e.rank||0)-(t.rank||0)}),s=(l=e[0].rank===e[e.length-1].rank)?f/2:-1,r=l?s:f-1;s&&y>c;)a=e[n=Math.floor(r)],ii(g,n)&&(y-=a.size),r+=s,l&&r>=e.length&&(s/=2,r=s);g.sort(function(t,e){return e-t}).forEach(function(t){return v.apply(u,e.splice(t,1))})}for(io(e,p),e=e.map(function(t){return{size:t.size,targets:[t.target],align:ie(t.align,.5)}});m;){for(n=e.length;n--;)a=e[n],h=(Math.min.apply(0,a.targets)+Math.max.apply(0,a.targets))/2,a.pos=it(h-a.size*a.align,0,i-a.size);for(n=e.length,m=!1;n--;)n>0&&e[n-1].pos+e[n-1].size>e[n].pos&&(e[n-1].size+=e[n].size,e[n-1].targets=e[n-1].targets.concat(e[n].targets),e[n-1].align=.5,e[n-1].pos+e[n-1].size>i&&(e[n-1].pos=i-e[n-1].size),e.splice(n,1),m=!0)}return v.apply(d,u),n=0,e.some(function(e){var r=0;return(e.targets||[]).some(function(){return(d[n].pos=e.pos+r,void 0!==o&&Math.abs(d[n].pos-d[n].target)>o)?(d.slice(0,n+1).forEach(function(t){return delete t.pos}),d.reducedLen=(d.reducedLen||i)-.1*i,d.reducedLen>.1*i&&t(d,i,o),!0):(r+=d[n].size,n++,!1)})}),io(d,p),d};var ir=H,is=tv.deg2rad,ia=tv.doc,ih=tv.svg,il=tv.SVG_NS,id=tv.win,ic=tv.isFirefox,ip=tF.addEvent,iu=tF.attr,ig=tF.createElement,iv=tF.crisp,im=tF.css,iy=tF.defined,ix=tF.erase,ib=tF.extend,ik=tF.fireEvent,iM=tF.getAlignFactor,iw=tF.isArray,iS=tF.isFunction,iA=tF.isNumber,iT=tF.isObject,iC=tF.isString,iO=tF.merge,iP=tF.objectEach,iE=tF.pick,iL=tF.pInt,iB=tF.pushUnique,iD=tF.replaceNested,iI=tF.syncTimeout,iz=tF.uniqueKey,iR=function(){function t(t,e){this.onEvents={},this.opacity=1,this.SVG_NS=il,this.element="span"===e||"body"===e?ig(e):ia.createElementNS(this.SVG_NS,e),this.renderer=t,this.styles={},ik(this,"afterInit")}return t.prototype._defaultGetter=function(t){var e=iE(this[t+"Value"],this[t],this.element?this.element.getAttribute(t):null,0);return/^-?[\d\.]+$/.test(e)&&(e=parseFloat(e)),e},t.prototype._defaultSetter=function(t,e,i){i.setAttribute(e,t)},t.prototype.add=function(t){var e,i=this.renderer,o=this.element;return t&&(this.parentGroup=t),void 0!==this.textStr&&"text"===this.element.nodeName&&i.buildText(this),this.added=!0,(!t||t.handleZ||this.zIndex)&&(e=this.zIndexSetter()),e||(t?t.element:i.box).appendChild(o),this.onAdd&&this.onAdd(),this},t.prototype.addClass=function(t,e){var i=e?"":this.attr("class")||"";return(t=(t||"").split(/ /g).reduce(function(t,e){return -1===i.indexOf(e)&&t.push(e),t},i?[i]:[]).join(" "))!==i&&this.attr("class",t),this},t.prototype.afterSetters=function(){this.doTransform&&(this.updateTransform(),this.doTransform=!1)},t.prototype.align=function(t,e,i,o){void 0===o&&(o=!0);var n=this.renderer,r=n.alignedObjects,s=!!t;t?(this.alignOptions=t,this.alignByTranslate=e,this.alignTo=i):(t=this.alignOptions||{},e=this.alignByTranslate,i=this.alignTo);var a=!i||iC(i)?i||"renderer":void 0;a&&(s&&iB(r,this),i=void 0);var h=iE(i,n[a],n),l=(h.x||0)+(t.x||0)+((h.width||0)-(t.width||0))*iM(t.align),d=(h.y||0)+(t.y||0)+((h.height||0)-(t.height||0))*iM(t.verticalAlign),c={"text-align":null==t?void 0:t.align};return c[e?"translateX":"x"]=Math.round(l),c[e?"translateY":"y"]=Math.round(d),o&&(this[this.placed?"animate":"attr"](c),this.placed=!0),this.alignAttr=c,this},t.prototype.alignSetter=function(t){var e={left:"start",center:"middle",right:"end"};e[t]&&(this.alignValue=t,this.element.setAttribute("text-anchor",e[t]))},t.prototype.animate=function(t,e,i){var o=this,n=eE(iE(e,this.renderer.globalAnimation,!0)),r=n.defer;return ia.hidden&&(n.duration=0),0!==n.duration?(i&&(n.complete=i),iI(function(){o.element&&eB(o,t,n)},r)):(this.attr(t,void 0,i||n.complete),iP(t,function(t,e){n.step&&n.step.call(this,t,{prop:e,pos:1,elem:this})},this)),this},t.prototype.applyTextOutline=function(t){var e=this.element;-1!==t.indexOf("contrast")&&(t=t.replace(/contrast/g,this.renderer.getContrast(e.style.fill)));var i=t.indexOf(" "),o=t.substring(i+1),n=t.substring(0,i);if(n&&"none"!==n&&tv.svg){this.fakeTS=!0,n=n.replace(/(^[\d\.]+)(.*?)$/g,function(t,e,i){return 2*Number(e)+i}),this.removeTextOutline();var r=ia.createElementNS(il,"tspan");iu(r,{class:"highcharts-text-outline",fill:o,stroke:o,"stroke-width":n,"stroke-linejoin":"round"});var s=e.querySelector("textPath")||e;[].forEach.call(s.childNodes,function(t){var e=t.cloneNode(!0);e.removeAttribute&&["fill","stroke","stroke-width","stroke"].forEach(function(t){return e.removeAttribute(t)}),r.appendChild(e)});var a=0;[].forEach.call(s.querySelectorAll("text tspan"),function(t){a+=Number(t.getAttribute("dy"))});var h=ia.createElementNS(il,"tspan");h.textContent="​",iu(h,{x:Number(e.getAttribute("x")),dy:-a}),r.appendChild(h),s.insertBefore(r,s.firstChild)}},t.prototype.attr=function(e,i,o,n){var r,s,a,h=this.element,l=t.symbolCustomAttribs,d=this;return"string"==typeof e&&void 0!==i&&(r=e,(e={})[r]=i),"string"==typeof e?d=(this[e+"Getter"]||this._defaultGetter).call(this,e,h):(iP(e,function(t,i){a=!1,n||eL(this,i),this.symbolName&&-1!==l.indexOf(i)&&(s||(this.symbolAttr(e),s=!0),a=!0),this.rotation&&("x"===i||"y"===i)&&(this.doTransform=!0),a||(this[i+"Setter"]||this._defaultSetter).call(this,t,i,h)},this),this.afterSetters()),o&&o.call(this),d},t.prototype.clip=function(t){if(t&&!t.clipPath){var e=iz()+"-",i=this.renderer.createElement("clipPath").attr({id:e}).add(this.renderer.defs);ib(t,{clipPath:i,id:e,count:0}),t.add(i)}return this.attr("clip-path",t?"url(".concat(this.renderer.url,"#").concat(t.id,")"):"none")},t.prototype.crisp=function(t,e){e=Math.round(e||t.strokeWidth||0);var i=t.x||this.x||0,o=t.y||this.y||0,n=(t.width||this.width||0)+i,r=(t.height||this.height||0)+o,s=iv(i,e),a=iv(o,e);return ib(t,{x:s,y:a,width:iv(n,e)-s,height:iv(r,e)-a}),iy(t.strokeWidth)&&(t.strokeWidth=e),t},t.prototype.complexColor=function(t,e,i){var o,n,r,s,a,h,l,d,c,p,u,f=this.renderer,g=[];ik(this.renderer,"complexColor",{args:arguments},function(){if(t.radialGradient?n="radialGradient":t.linearGradient&&(n="linearGradient"),n){if(r=t[n],a=f.gradients,h=t.stops,c=i.radialReference,iw(r)&&(t[n]=r={x1:r[0],y1:r[1],x2:r[2],y2:r[3],gradientUnits:"userSpaceOnUse"}),"radialGradient"===n&&c&&!iy(r.gradientUnits)&&(s=r,r=iO(r,f.getRadialAttr(c,s),{gradientUnits:"userSpaceOnUse"})),iP(r,function(t,e){"id"!==e&&g.push(e,t)}),iP(h,function(t){g.push(t)}),a[g=g.join(",")])p=a[g].attr("id");else{r.id=p=iz();var v=a[g]=f.createElement(n).attr(r).add(f.defs);v.radAttr=s,v.stops=[],h.forEach(function(t){0===t[1].indexOf("rgba")?(l=(o=ev.parse(t[1])).get("rgb"),d=o.get("a")):(l=t[1],d=1);var e=f.createElement("stop").attr({offset:t[0],"stop-color":l,"stop-opacity":d}).add(v);v.stops.push(e)})}u="url("+f.url+"#"+p+")",i.setAttribute(e,u),i.gradient=g,t.toString=function(){return u}}})},t.prototype.css=function(t){var e,i=this.styles,o={},n=this.element,r=!i;if(i&&iP(t,function(t,e){i&&i[e]!==t&&(o[e]=t,r=!0)}),r){i&&(t=ib(i,o)),null===t.width||"auto"===t.width?delete this.textWidth:"text"===n.nodeName.toLowerCase()&&t.width&&(e=this.textWidth=iL(t.width)),ib(this.styles,t),e&&!ih&&this.renderer.forExport&&delete t.width;var s=ic&&t.fontSize||null;s&&(iA(s)||/^\d+$/.test(s))&&(t.fontSize+="px");var a=iO(t);n.namespaceURI===this.SVG_NS&&(["textOutline","textOverflow","whiteSpace","width"].forEach(function(t){return a&&delete a[t]}),a.color&&(a.fill=a.color,delete a.color)),im(n,a)}return this.added&&("text"===this.element.nodeName&&this.renderer.buildText(this),t.textOutline&&this.applyTextOutline(t.textOutline)),this},t.prototype.dashstyleSetter=function(t){var e,i=this["stroke-width"];if("inherit"===i&&(i=1),t){var o=(t=t.toLowerCase()).replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(",");for(e=o.length;e--;)o[e]=""+iL(o[e])*iE(i,NaN);t=o.join(",").replace(/NaN/g,"none"),this.element.setAttribute("stroke-dasharray",t)}},t.prototype.destroy=function(){var t,e,i=this,o=i.element||{},n=i.renderer,r=o.ownerSVGElement,s="SPAN"===o.nodeName&&i.parentGroup||void 0;if(o.onclick=o.onmouseout=o.onmouseover=o.onmousemove=o.point=null,eL(i),i.clipPath&&r){var a=i.clipPath;[].forEach.call(r.querySelectorAll("[clip-path],[CLIP-PATH]"),function(t){t.getAttribute("clip-path").indexOf(a.element.id)>-1&&t.removeAttribute("clip-path")}),i.clipPath=a.destroy()}if(i.stops){for(e=0;e<i.stops.length;e++)i.stops[e].destroy();i.stops.length=0,i.stops=void 0}for(i.safeRemoveChild(o);(null==s?void 0:s.div)&&0===s.div.childNodes.length;)t=s.parentGroup,i.safeRemoveChild(s.div),delete s.div,s=t;i.alignOptions&&ix(n.alignedObjects,i),iP(i,function(t,e){var o,n,r;((null===(o=i[e])||void 0===o?void 0:o.parentGroup)===i||-1!==["connector","foreignObject"].indexOf(e))&&(null===(r=null===(n=i[e])||void 0===n?void 0:n.destroy)||void 0===r||r.call(n)),delete i[e]})},t.prototype.dSetter=function(t,e,i){iw(t)&&("string"==typeof t[0]&&(t=this.renderer.pathToSegments(t)),this.pathArray=t,t=t.reduce(function(t,e,i){return(null==e?void 0:e.join)?(i?t+" ":"")+e.join(" "):(e||"").toString()},"")),/(NaN| {2}|^$)/.test(t)&&(t="M 0 0"),this[e]!==t&&(i.setAttribute(e,t),this[e]=t)},t.prototype.fillSetter=function(t,e,i){"string"==typeof t?i.setAttribute(e,t):t&&this.complexColor(t,e,i)},t.prototype.hrefSetter=function(t,e,i){i.setAttributeNS("http://www.w3.org/1999/xlink",e,t)},t.prototype.getBBox=function(e,i){var o,n,r,s,a=this.alignValue,h=this.element,l=this.renderer,d=this.styles,c=this.textStr,p=l.cache,u=l.cacheKeys,f=h.namespaceURI===this.SVG_NS,g=iE(i,this.rotation,0),v=l.styledMode?h&&t.prototype.getStyle.call(h,"font-size"):d.fontSize;if(iy(c)&&(-1===(s=c.toString()).indexOf("<")&&(s=s.replace(/\d/g,"0")),s+=["",l.rootFontSize,v,g,this.textWidth,a,d.lineClamp,d.textOverflow,d.fontWeight].join(",")),s&&!e&&(o=p[s]),!o||o.polygon){if(f||l.forExport){try{r=this.fakeTS&&function(t){var e=h.querySelector(".highcharts-text-outline");e&&im(e,{display:t})},iS(r)&&r("none"),o=h.getBBox?ib({},h.getBBox()):{width:h.offsetWidth,height:h.offsetHeight,x:0,y:0},iS(r)&&r("")}catch(t){}(!o||o.width<0)&&(o={x:0,y:0,width:0,height:0})}else o=this.htmlGetBBox();n=o.height,f&&(o.height=n=({"11px,17":14,"13px,20":16})[""+(v||"")+",".concat(Math.round(n))]||n),g&&(o=this.getRotatedBox(o,g));var m={bBox:o};ik(this,"afterGetBBox",m),o=m.bBox}if(s&&(""===c||o.height>0)){for(;u.length>250;)delete p[u.shift()];p[s]||u.push(s),p[s]=o}return o},t.prototype.getRotatedBox=function(t,e){var i=t.x,o=t.y,n=t.width,r=t.height,s=this.alignValue,a=this.translateY,h=this.rotationOriginX,l=this.rotationOriginY,d=iM(s),c=Number(this.element.getAttribute("y")||0)-(a?0:o),p=e*is,u=(e-90)*is,f=Math.cos(p),g=Math.sin(p),v=n*f,m=n*g,y=Math.cos(u),x=Math.sin(u),b=[void 0===h?0:h,void 0===l?0:l].map(function(t){return[t-t*f,t*g]}),k=b[0],M=k[0],w=k[1],S=b[1],A=S[0],T=i+d*(n-v)+M+S[1]+c*y,C=T+v,O=C-r*y,P=O-v,E=o+c-d*m-w+A+c*x,L=E+m,B=L-r*x,D=B-m,I=Math.min(T,C,O,P),z=Math.min(E,L,B,D),R=Math.max(T,C,O,P)-I,N=Math.max(E,L,B,D)-z;return{x:I,y:z,width:R,height:N,polygon:[[T,E],[C,L],[O,B],[P,D]]}},t.prototype.getStyle=function(t){return id.getComputedStyle(this.element||this,"").getPropertyValue(t)},t.prototype.hasClass=function(t){return -1!==(""+this.attr("class")).split(" ").indexOf(t)},t.prototype.hide=function(){return this.attr({visibility:"hidden"})},t.prototype.htmlGetBBox=function(){return{height:0,width:0,x:0,y:0}},t.prototype.on=function(t,e){var i=this.onEvents;return i[t]&&i[t](),i[t]=ip(this.element,t,e),this},t.prototype.opacitySetter=function(t,e,i){var o=Number(Number(t).toFixed(3));this.opacity=o,i.setAttribute(e,o)},t.prototype.reAlign=function(){var t;(null===(t=this.alignOptions)||void 0===t?void 0:t.width)&&"left"!==this.alignOptions.align&&(this.alignOptions.width=this.getBBox().width,this.placed=!1,this.align())},t.prototype.removeClass=function(t){return this.attr("class",(""+this.attr("class")).replace(iC(t)?new RegExp("(^| )".concat(t,"( |$)")):t," ").replace(/ +/g," ").trim())},t.prototype.removeTextOutline=function(){var t=this.element.querySelector("tspan.highcharts-text-outline");t&&this.safeRemoveChild(t)},t.prototype.safeRemoveChild=function(t){var e=t.parentNode;e&&e.removeChild(t)},t.prototype.setRadialReference=function(t){var e=this.element.gradient&&this.renderer.gradients[this.element.gradient]||void 0;return this.element.radialReference=t,(null==e?void 0:e.radAttr)&&e.animate(this.renderer.getRadialAttr(t,e.radAttr)),this},t.prototype.shadow=function(t){var e,i=this.renderer,o=iO((null===(e=this.parentGroup)||void 0===e?void 0:e.rotation)===90?{offsetX:-1,offsetY:-1}:{},iT(t)?t:{}),n=i.shadowDefinition(o);return this.attr({filter:t?"url(".concat(i.url,"#").concat(n,")"):"none"})},t.prototype.show=function(t){return void 0===t&&(t=!0),this.attr({visibility:t?"inherit":"visible"})},t.prototype["stroke-widthSetter"]=function(t,e,i){this[e]=t,i.setAttribute(e,t)},t.prototype.strokeWidth=function(){if(!this.renderer.styledMode)return this["stroke-width"]||0;var t,e=this.getStyle("stroke-width"),i=0;return/px$/.test(e)?i=iL(e):""!==e&&(iu(t=ia.createElementNS(il,"rect"),{width:e,"stroke-width":0}),this.element.parentNode.appendChild(t),i=t.getBBox().width,t.parentNode.removeChild(t)),i},t.prototype.symbolAttr=function(e){var i=this;t.symbolCustomAttribs.forEach(function(t){i[t]=iE(e[t],i[t])}),i.attr({d:i.renderer.symbols[i.symbolName](i.x,i.y,i.width,i.height,i)})},t.prototype.textSetter=function(t){t!==this.textStr&&(delete this.textPxLength,this.textStr=t,this.added&&this.renderer.buildText(this),this.reAlign())},t.prototype.titleSetter=function(t){var e=this.element,i=e.getElementsByTagName("title")[0]||ia.createElementNS(this.SVG_NS,"title");e.insertBefore?e.insertBefore(i,e.firstChild):e.appendChild(i),i.textContent=iD(iE(t,""),[/<[^>]*>/g,""]).replace(/&lt;/g,"<").replace(/&gt;/g,">")},t.prototype.toFront=function(){var t=this.element;return t.parentNode.appendChild(t),this},t.prototype.translate=function(t,e){return this.attr({translateX:t,translateY:e})},t.prototype.updateTransform=function(t){void 0===t&&(t="transform");var e,i,o,n,r=this.element,s=this.foreignObject,a=this.matrix,h=this.padding,l=this.rotation,d=void 0===l?0:l,c=this.rotationOriginX,p=this.rotationOriginY,u=this.scaleX,f=this.scaleY,g=this.text,v=this.translateX,m=this.translateY,y=["translate("+(void 0===v?0:v)+","+(void 0===m?0:m)+")"];iy(a)&&y.push("matrix("+a.join(",")+")"),!d||(y.push("rotate("+d+" "+(null!==(i=null!==(e=null!=c?c:r.getAttribute("x"))&&void 0!==e?e:this.x)&&void 0!==i?i:0)+" "+(null!==(n=null!==(o=null!=p?p:r.getAttribute("y"))&&void 0!==o?o:this.y)&&void 0!==n?n:0)+")"),(null==g?void 0:g.element.tagName)!=="SPAN"||(null==g?void 0:g.foreignObject)||g.attr({rotation:d,rotationOriginX:(c||0)-h,rotationOriginY:(p||0)-h})),(iy(u)||iy(f))&&y.push("scale("+iE(u,1)+" "+iE(f,1)+")"),y.length&&!(g||this).textPath&&((null==s?void 0:s.element)||r).setAttribute(t,y.join(" "))},t.prototype.visibilitySetter=function(t,e,i){"inherit"===t?i.removeAttribute(e):this[e]!==t&&i.setAttribute(e,t),this[e]=t},t.prototype.xGetter=function(t){return"circle"===this.element.nodeName&&("x"===t?t="cx":"y"===t&&(t="cy")),this._defaultGetter(t)},t.prototype.zIndexSetter=function(t,e){var i,o,n,r,s,a=this.renderer,h=this.parentGroup,l=(h||a).element||a.box,d=this.element,c=l===a.box,p=!1,u=this.added;if(iy(t)?(d.setAttribute("data-z-index",t),t*=1,this[e]===t&&(u=!1)):iy(this[e])&&d.removeAttribute("data-z-index"),this[e]=t,u){for((t=this.zIndex)&&h&&(h.handleZ=!0),s=(i=l.childNodes).length-1;s>=0&&!p;s--)r=!iy(n=(o=i[s]).getAttribute("data-z-index")),o!==d&&(t<0&&r&&!c&&!s?(l.insertBefore(d,i[s]),p=!0):(iL(n)<=t||r&&(!iy(t)||t>=0))&&(l.insertBefore(d,i[s+1]),p=!0));p||(l.insertBefore(d,i[3*!!c]),p=!0)}return p},t.symbolCustomAttribs=["anchorX","anchorY","clockwise","end","height","innerR","r","start","width","x","y"],t}();iR.prototype.strokeSetter=iR.prototype.fillSetter,iR.prototype.yGetter=iR.prototype.xGetter,iR.prototype.matrixSetter=iR.prototype.rotationOriginXSetter=iR.prototype.rotationOriginYSetter=iR.prototype.rotationSetter=iR.prototype.scaleXSetter=iR.prototype.scaleYSetter=iR.prototype.translateXSetter=iR.prototype.translateYSetter=iR.prototype.verticalAlignSetter=function(t,e){this[e]=t,this.doTransform=!0};var iN=(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),iW=tF.defined,iG=tF.extend,iX=tF.getAlignFactor,iH=tF.isNumber,iF=tF.merge,iY=tF.pick,i_=tF.removeEvent,ij=function(t){function e(i,o,n,r,s,a,h,l,d,c){var p,u=t.call(this,i,"g")||this;return u.paddingLeftSetter=u.paddingSetter,u.paddingRightSetter=u.paddingSetter,u.doUpdate=!1,u.textStr=o,u.x=n,u.y=r,u.anchorX=a,u.anchorY=h,u.baseline=d,u.className=c,u.addClass("button"===c?"highcharts-no-tooltip":"highcharts-label"),c&&u.addClass("highcharts-"+c),u.text=i.text(void 0,0,0,l).attr({zIndex:1}),"string"==typeof s&&((p=/^url\((.*?)\)$/.test(s))||u.renderer.symbols[s])&&(u.symbolKey=s),u.bBox=e.emptyBBox,u.padding=3,u.baselineOffset=0,u.needsBox=i.styledMode||p,u.deferredAttr={},u.alignFactor=0,u}return iN(e,t),e.prototype.alignSetter=function(t){var e=iX(t);this.textAlign=t,e!==this.alignFactor&&(this.alignFactor=e,this.bBox&&iH(this.xSetting)&&this.attr({x:this.xSetting}))},e.prototype.anchorXSetter=function(t,e){this.anchorX=t,this.boxAttr(e,Math.round(t)-this.getCrispAdjust()-this.xSetting)},e.prototype.anchorYSetter=function(t,e){this.anchorY=t,this.boxAttr(e,t-this.ySetting)},e.prototype.boxAttr=function(t,e){this.box?this.box.attr(t,e):this.deferredAttr[t]=e},e.prototype.css=function(t){if(t){var i={};t=iF(t),e.textProps.forEach(function(e){void 0!==t[e]&&(i[e]=t[e],delete t[e])}),this.text.css(i),"fontSize"in i||"fontWeight"in i?this.updateTextPadding():("width"in i||"textOverflow"in i)&&this.updateBoxSize()}return iR.prototype.css.call(this,t)},e.prototype.destroy=function(){i_(this.element,"mouseenter"),i_(this.element,"mouseleave"),this.text&&this.text.destroy(),this.box&&(this.box=this.box.destroy()),iR.prototype.destroy.call(this)},e.prototype.fillSetter=function(t,e){t&&(this.needsBox=!0),this.fill=t,this.boxAttr(e,t)},e.prototype.getBBox=function(t,e){this.textStr&&0===this.bBox.width&&0===this.bBox.height&&this.updateBoxSize();var i=this.padding,o=this.height,n=this.translateX,r=this.translateY,s=this.width,a=iY(this.paddingLeft,i),h=null!=e?e:this.rotation||0,l={width:void 0===s?0:s,height:void 0===o?0:o,x:(void 0===n?0:n)+this.bBox.x-a,y:(void 0===r?0:r)+this.bBox.y-i+this.baselineOffset};return h&&(l=this.getRotatedBox(l,h)),l},e.prototype.getCrispAdjust=function(){return(this.renderer.styledMode&&this.box?this.box.strokeWidth():this["stroke-width"]?parseInt(this["stroke-width"],10):0)%2/2},e.prototype.heightSetter=function(t){this.heightSetting=t,this.doUpdate=!0},e.prototype.afterSetters=function(){t.prototype.afterSetters.call(this),this.doUpdate&&(this.updateBoxSize(),this.doUpdate=!1)},e.prototype.onAdd=function(){this.text.add(this),this.attr({text:iY(this.textStr,""),x:this.x||0,y:this.y||0}),this.box&&iW(this.anchorX)&&this.attr({anchorX:this.anchorX,anchorY:this.anchorY})},e.prototype.paddingSetter=function(t,e){iH(t)?t!==this[e]&&(this[e]=t,this.updateTextPadding()):this[e]=void 0},e.prototype.rSetter=function(t,e){this.boxAttr(e,t)},e.prototype.strokeSetter=function(t,e){this.stroke=t,this.boxAttr(e,t)},e.prototype["stroke-widthSetter"]=function(t,e){t&&(this.needsBox=!0),this["stroke-width"]=t,this.boxAttr(e,t)},e.prototype["text-alignSetter"]=function(t){this.textAlign=this["text-align"]=t,this.updateTextPadding()},e.prototype.textSetter=function(t){void 0!==t&&this.text.attr({text:t}),this.updateTextPadding(),this.reAlign()},e.prototype.updateBoxSize=function(){var t,i=this.text,o={},n=this.padding,r=this.bBox=(!iH(this.widthSetting)||!iH(this.heightSetting)||this.textAlign)&&iW(i.textStr)?i.getBBox(void 0,0):e.emptyBBox;this.width=this.getPaddedWidth(),this.height=(this.heightSetting||r.height||0)+2*n;var s=this.renderer.fontMetrics(i);if(this.baselineOffset=n+Math.min((this.text.firstLineMetrics||s).b,r.height||1/0),this.heightSetting&&(this.baselineOffset+=(this.heightSetting-s.h)/2),this.needsBox&&!i.textPath){if(!this.box){var a=this.box=this.symbolKey?this.renderer.symbol(this.symbolKey):this.renderer.rect();a.addClass(("button"===this.className?"":"highcharts-label-box")+(this.className?" highcharts-"+this.className+"-box":"")),a.add(this)}o.x=t=this.getCrispAdjust(),o.y=(this.baseline?-this.baselineOffset:0)+t,o.width=Math.round(this.width),o.height=Math.round(this.height),this.box.attr(iG(o,this.deferredAttr)),this.deferredAttr={}}},e.prototype.updateTextPadding=function(){var t,e,i=this.text,o=i.styles.textAlign||this.textAlign;if(!i.textPath){this.updateBoxSize();var n=this.baseline?0:this.baselineOffset,r=(null!==(t=this.paddingLeft)&&void 0!==t?t:this.padding)+iX(o)*(null!==(e=this.widthSetting)&&void 0!==e?e:this.bBox.width);(r!==i.x||n!==i.y)&&(i.attr({align:o,x:r}),void 0!==n&&i.attr("y",n)),i.x=r,i.y=n}},e.prototype.widthSetter=function(t){this.widthSetting=iH(t)?t:void 0,this.doUpdate=!0},e.prototype.getPaddedWidth=function(){var t=this.padding,e=iY(this.paddingLeft,t),i=iY(this.paddingRight,t);return(this.widthSetting||this.bBox.width||0)+e+i},e.prototype.xSetter=function(t){this.x=t,this.alignFactor&&(t-=this.alignFactor*this.getPaddedWidth(),this["forceAnimate:x"]=!0),this.xSetting=Math.round(t),this.attr("translateX",this.xSetting)},e.prototype.ySetter=function(t){this.ySetting=this.y=Math.round(t),this.attr("translateY",this.ySetting)},e.emptyBBox={width:0,height:0,x:0,y:0},e.textProps=["color","direction","fontFamily","fontSize","fontStyle","fontWeight","lineClamp","lineHeight","textAlign","textDecoration","textOutline","textOverflow","whiteSpace","width"],e}(iR),iU=tF.defined,iV=tF.isNumber,iZ=tF.pick;function iq(t,e,i,o,n){var r=[];if(n){var s=n.start||0,a=n.end||0,h=iZ(n.r,i),l=iZ(n.r,o||i),d=2e-4/(n.borderRadius?1:Math.max(h,1)),c=Math.abs(a-s-2*Math.PI)<d;c&&(s=Math.PI/2,a=2.5*Math.PI-d);var p=n.innerR,u=iZ(n.open,c),f=Math.cos(s),g=Math.sin(s),v=Math.cos(a),m=Math.sin(a),y=iZ(n.longArc,a-s-Math.PI<d?0:1),x=["A",h,l,0,y,iZ(n.clockwise,1),t+h*v,e+l*m];x.params={start:s,end:a,cx:t,cy:e},r.push(["M",t+h*f,e+l*g],x),iU(p)&&((x=["A",p,p,0,y,iU(n.clockwise)?1-n.clockwise:0,t+p*f,e+p*g]).params={start:a,end:s,cx:t,cy:e},r.push(u?["M",t+p*v,e+p*m]:["L",t+p*v,e+p*m],x)),u||r.push(["Z"])}return r}function iK(t,e,i,o,n){return(null==n?void 0:n.r)?i$(t,e,i,o,n):[["M",t,e],["L",t+i,e],["L",t+i,e+o],["L",t,e+o],["Z"]]}function i$(t,e,i,o,n){var r=(null==n?void 0:n.r)||0;return[["M",t+r,e],["L",t+i-r,e],["A",r,r,0,0,1,t+i,e+r],["L",t+i,e+o-r],["A",r,r,0,0,1,t+i-r,e+o],["L",t+r,e+o],["A",r,r,0,0,1,t,e+o-r],["L",t,e+r],["A",r,r,0,0,1,t+r,e],["Z"]]}var iJ={arc:iq,callout:function(t,e,i,o,n){var r=Math.min((null==n?void 0:n.r)||0,i,o),s=r+6,a=null==n?void 0:n.anchorX,h=(null==n?void 0:n.anchorY)||0,l=i$(t,e,i,o,{r:r});if(!iV(a)||a<i&&a>0&&h<o&&h>0)return l;if(t+a>i-s){if(h>e+s&&h<e+o-s)l.splice(3,1,["L",t+i,h-6],["L",t+i+6,h],["L",t+i,h+6],["L",t+i,e+o-r]);else if(a<i){var d=h<e+s,c=d?e:e+o,p=d?2:5;l.splice(p,0,["L",a,h],["L",t+i-r,c])}else l.splice(3,1,["L",t+i,o/2],["L",a,h],["L",t+i,o/2],["L",t+i,e+o-r])}else if(t+a<s){if(h>e+s&&h<e+o-s)l.splice(7,1,["L",t,h+6],["L",t-6,h],["L",t,h-6],["L",t,e+r]);else if(a>0){var d=h<e+s,c=d?e:e+o,p=d?1:6;l.splice(p,0,["L",a,h],["L",t+r,c])}else l.splice(7,1,["L",t,o/2],["L",a,h],["L",t,o/2],["L",t,e+r])}else h>o&&a<i-s?l.splice(5,1,["L",a+6,e+o],["L",a,e+o+6],["L",a-6,e+o],["L",t+r,e+o]):h<0&&a>s&&l.splice(1,1,["L",a-6,e],["L",a,e-6],["L",a+6,e],["L",i-r,e]);return l},circle:function(t,e,i,o){return iq(t+i/2,e+o/2,i/2,o/2,{start:.5*Math.PI,end:2.5*Math.PI,open:!1})},diamond:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o/2],["L",t+i/2,e+o],["L",t,e+o/2],["Z"]]},rect:iK,roundedRect:i$,square:iK,triangle:function(t,e,i,o){return[["M",t+i/2,e],["L",t+i,e+o],["L",t,e+o],["Z"]]},"triangle-down":function(t,e,i,o){return[["M",t,e],["L",t+i,e],["L",t+i/2,e+o],["Z"]]}},iQ=tv.doc,i0=tv.SVG_NS,i1=tv.win,i2=tF.attr,i3=tF.extend,i5=tF.fireEvent,i6=tF.isString,i9=tF.objectEach,i4=tF.pick,i8=function(t,e){return t.substring(0,e)+"…"},i7=function(){function t(t){var e=t.styles;this.renderer=t.renderer,this.svgElement=t,this.width=t.textWidth,this.textLineHeight=null==e?void 0:e.lineHeight,this.textOutline=null==e?void 0:e.textOutline,this.ellipsis=(null==e?void 0:e.textOverflow)==="ellipsis",this.lineClamp=null==e?void 0:e.lineClamp,this.noWrap=(null==e?void 0:e.whiteSpace)==="nowrap"}return t.prototype.buildSVG=function(){var t=this.svgElement,e=t.element,i=t.renderer,o=i4(t.textStr,"").toString(),n=-1!==o.indexOf("<"),r=e.childNodes,s=!t.added&&i.box,a=[o,this.ellipsis,this.noWrap,this.textLineHeight,this.textOutline,t.getStyle("font-size"),t.styles.lineClamp,this.width].join(",");if(a!==t.textCache){t.textCache=a,delete t.actualWidth;for(var h=r.length;h--;)e.removeChild(r[h]);if(n||this.ellipsis||this.width||t.textPath||-1!==o.indexOf(" ")&&(!this.noWrap||/<br.*?>/g.test(o))){if(""!==o){s&&s.appendChild(e);var l=new eZ(o);this.modifyTree(l.nodes),l.addToDOM(e),this.modifyDOM(),this.ellipsis&&-1!==(e.textContent||"").indexOf("…")&&t.attr("title",this.unescapeEntities(t.textStr||"",["&lt;","&gt;"])),s&&s.removeChild(e)}}else e.appendChild(iQ.createTextNode(this.unescapeEntities(o)));i6(this.textOutline)&&t.applyTextOutline&&t.applyTextOutline(this.textOutline)}},t.prototype.modifyDOM=function(){var t,e=this,i=this.svgElement,o=i2(i.element,"x");for(i.firstLineMetrics=void 0;t=i.element.firstChild;)if(/^[\s\u200B]*$/.test(t.textContent||" "))i.element.removeChild(t);else break;[].forEach.call(i.element.querySelectorAll("tspan.highcharts-br"),function(t,n){t.nextSibling&&t.previousSibling&&(0===n&&1===t.previousSibling.nodeType&&(i.firstLineMetrics=i.renderer.fontMetrics(t.previousSibling)),i2(t,{dy:e.getLineHeight(t.nextSibling),x:o}))});var n=this.width||0;if(n){var r=function(t,r){var s,a=t.textContent||"",h=a.replace(/([^\^])-/g,"$1- ").split(" "),l=!e.noWrap&&(h.length>1||i.element.childNodes.length>1),d=e.getLineHeight(r),c=Math.max(0,n-.8*d),p=0,u=i.actualWidth;if(l){for(var f=[],g=[];r.firstChild&&r.firstChild!==t;)g.push(r.firstChild),r.removeChild(r.firstChild);for(;h.length;)if(h.length&&!e.noWrap&&p>0&&(f.push(t.textContent||""),t.textContent=h.join(" ").replace(/- /g,"-")),e.truncate(t,void 0,h,0===p&&u||0,n,c,function(t,e){return h.slice(0,e).join(" ").replace(/- /g,"-")}),u=i.actualWidth,p++,e.lineClamp&&p>=e.lineClamp){h.length&&(e.truncate(t,t.textContent||"",void 0,0,n,c,i8),t.textContent=(null===(s=t.textContent)||void 0===s?void 0:s.replace("…",""))+"…");break}g.forEach(function(e){r.insertBefore(e,t)}),f.forEach(function(e){r.insertBefore(iQ.createTextNode(e),t);var i=iQ.createElementNS(i0,"tspan");i.textContent="​",i2(i,{dy:d,x:o}),r.insertBefore(i,t)})}else e.ellipsis&&a&&e.truncate(t,a,void 0,0,n,c,i8)},s=function(t){[].slice.call(t.childNodes).forEach(function(e){e.nodeType===i1.Node.TEXT_NODE?r(e,t):(-1!==e.className.baseVal.indexOf("highcharts-br")&&(i.actualWidth=0),s(e))})};s(i.element)}},t.prototype.getLineHeight=function(t){var e=t.nodeType===i1.Node.TEXT_NODE?t.parentElement:t;return this.textLineHeight?parseInt(this.textLineHeight.toString(),10):this.renderer.fontMetrics(e||this.svgElement.element).h},t.prototype.modifyTree=function(t){var e=this,i=function(o,n){var r=o.attributes,s=void 0===r?{}:r,a=o.children,h=o.style,l=void 0===h?{}:h,d=o.tagName,c=e.renderer.styledMode;if("b"===d||"strong"===d?c?s.class="highcharts-strong":l.fontWeight="bold":("i"===d||"em"===d)&&(c?s.class="highcharts-emphasized":l.fontStyle="italic"),(null==l?void 0:l.color)&&(l.fill=l.color),"br"===d){s.class="highcharts-br",o.textContent="​";var p=t[n+1];(null==p?void 0:p.textContent)&&(p.textContent=p.textContent.replace(/^ +/gm,""))}else"a"===d&&a&&a.some(function(t){return"#text"===t.tagName})&&(o.children=[{children:a,tagName:"tspan"}]);"#text"!==d&&"a"!==d&&(o.tagName="tspan"),i3(o,{attributes:s,style:l}),a&&a.filter(function(t){return"#text"!==t.tagName}).forEach(i)};t.forEach(i),i5(this.svgElement,"afterModifyTree",{nodes:t})},t.prototype.truncate=function(t,e,i,o,n,r,s){var a,h,l=this.svgElement,d=l.rotation,c=[],p=i&&!o?1:0,u=(e||i||"").length,f=u;i||(n=r);var g=function(e,n){var r=n||e,s=t.parentNode;if(s&&void 0===c[r]&&s.getSubStringLength)try{c[r]=o+s.getSubStringLength(0,i?r+1:r)}catch(t){}return c[r]};if(l.rotation=0,o+(h=g(t.textContent.length))>n){for(;p<=u;)f=Math.ceil((p+u)/2),i&&(a=s(i,f)),h=g(f,a&&a.length-1),p===u?p=u+1:h>n?u=f-1:p=f;0===u?t.textContent="":e&&u===e.length-1||(t.textContent=a||s(e||i,f)),this.ellipsis&&h>n&&this.truncate(t,t.textContent||"",void 0,0,n,r,i8)}i&&i.splice(0,f),l.actualWidth=h,l.rotation=d},t.prototype.unescapeEntities=function(t,e){return i9(this.renderer.escapes,function(i,o){e&&-1!==e.indexOf(i)||(t=t.toString().replace(RegExp(i,"g"),o))}),t},t}(),ot=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},oe=tv.charts,oi=tv.deg2rad,oo=tv.doc,on=tv.isFirefox,or=tv.isMS,os=tv.isWebKit,oa=tv.noop,oh=tv.SVG_NS,ol=tv.symbolSizes,od=tv.win,oc=tF.addEvent,op=tF.attr,ou=tF.createElement,of=tF.crisp,og=tF.css,ov=tF.defined,om=tF.destroyObjectProperties,oy=tF.extend,ox=tF.isArray,ob=tF.isNumber,ok=tF.isObject,oM=tF.isString,ow=tF.merge,oS=tF.pick,oA=tF.pInt,oT=tF.replaceNested,oC=tF.uniqueKey,oO=function(){function t(t,e,i,o,n,r,s){this.x=0,this.y=0;var a,h,l=this.createElement("svg").attr({version:"1.1",class:"highcharts-root"}),d=l.element;s||l.css(this.getStyle(o||{})),t.appendChild(d),op(t,"dir","ltr"),-1===t.innerHTML.indexOf("xmlns")&&op(d,"xmlns",this.SVG_NS),this.box=d,this.boxWrapper=l,this.alignedObjects=[],this.url=this.getReferenceURL(),this.createElement("desc").add().element.appendChild(oo.createTextNode("Created with Highcharts 12.2.0")),this.defs=this.createElement("defs").add(),this.allowHTML=r,this.forExport=n,this.styledMode=s,this.gradients={},this.cache={},this.cacheKeys=[],this.imgCount=0,this.rootFontSize=l.getStyle("font-size"),this.setSize(e,i,!1),on&&t.getBoundingClientRect&&((a=function(){og(t,{left:0,top:0}),h=t.getBoundingClientRect(),og(t,{left:Math.ceil(h.left)-h.left+"px",top:Math.ceil(h.top)-h.top+"px"})})(),this.unSubPixelFix=oc(od,"resize",a))}return t.prototype.definition=function(t){return new eZ([t]).addToDOM(this.defs.element)},t.prototype.getReferenceURL=function(){if((on||os)&&oo.getElementsByTagName("base").length){if(!ov(F)){var t=oC(),e=new eZ([{tagName:"svg",attributes:{width:8,height:8},children:[{tagName:"defs",children:[{tagName:"clipPath",attributes:{id:t},children:[{tagName:"rect",attributes:{width:4,height:4}}]}]},{tagName:"rect",attributes:{id:"hitme",width:8,height:8,"clip-path":"url(#".concat(t,")"),fill:"rgba(0,0,0,0.001)"}}]}]).addToDOM(oo.body);og(e,{position:"fixed",top:0,left:0,zIndex:9e5});var i=oo.elementFromPoint(6,6);F=(null==i?void 0:i.id)==="hitme",oo.body.removeChild(e)}if(F)return oT(od.location.href.split("#")[0],[/<[^>]*>/g,""],[/([\('\)])/g,"\\$1"],[/ /g,"%20"])}return""},t.prototype.getStyle=function(t){return this.style=oy({fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif',fontSize:"1rem"},t),this.style},t.prototype.setStyle=function(t){this.boxWrapper.css(this.getStyle(t))},t.prototype.isHidden=function(){return!this.boxWrapper.getBBox().width},t.prototype.destroy=function(){var t=this.defs;return this.box=null,this.boxWrapper=this.boxWrapper.destroy(),om(this.gradients||{}),this.gradients=null,this.defs=t.destroy(),this.unSubPixelFix&&this.unSubPixelFix(),this.alignedObjects=null,null},t.prototype.createElement=function(t){return new this.Element(this,t)},t.prototype.getRadialAttr=function(t,e){return{cx:t[0]-t[2]/2+(e.cx||0)*t[2],cy:t[1]-t[2]/2+(e.cy||0)*t[2],r:(e.r||0)*t[2]}},t.prototype.shadowDefinition=function(t){var e=ot(["highcharts-drop-shadow-".concat(this.chartIndex)],Object.keys(t).map(function(e){return""+e+"-".concat(t[e])}),!0).join("-").toLowerCase().replace(/[^a-z\d\-]/g,""),i=ow({color:"#000000",offsetX:1,offsetY:1,opacity:.15,width:5},t);return this.defs.element.querySelector("#".concat(e))||this.definition({tagName:"filter",attributes:{id:e,filterUnits:i.filterUnits},children:this.getShadowFilterContent(i)}),e},t.prototype.getShadowFilterContent=function(t){return[{tagName:"feDropShadow",attributes:{dx:t.offsetX,dy:t.offsetY,"flood-color":t.color,"flood-opacity":Math.min(5*t.opacity,1),stdDeviation:t.width/2}}]},t.prototype.buildText=function(t){new i7(t).buildSVG()},t.prototype.getContrast=function(t){var e=ev.parse(t).rgba,i=" clamp(0,calc(9e9*(0.5 - (0.2126*r + 0.7152*g + 0.0722*b))),1)";if(ob(e[0])||!ev.useColorMix){var o=e.map(function(t){var e=t/255;return e<=.04?e/12.92:Math.pow((e+.055)/1.055,2.4)}),n=.2126*o[0]+.7152*o[1]+.0722*o[2];return 1.05/(n+.05)>(n+.05)/.05?"#FFFFFF":"#000000"}return"color(from "+t+" srgb"+i+i+i+")"},t.prototype.button=function(t,e,i,o,n,r,s,a,h,l){void 0===n&&(n={});var d=this.label(t,e,i,h,void 0,void 0,l,void 0,"button"),c=this.styledMode,p=arguments,u=0;n=ow(en.global.buttonTheme,n),c&&(delete n.fill,delete n.stroke,delete n["stroke-width"]);var f=n.states||{},g=n.style||{};delete n.states,delete n.style;var v=[eZ.filterUserAttributes(n)],m=[g];return c||["hover","select","disabled"].forEach(function(t,e){v.push(ow(v[0],eZ.filterUserAttributes(p[e+5]||f[t]||{}))),m.push(v[e+1].style),delete v[e+1].style}),oc(d.element,or?"mouseover":"mouseenter",function(){3!==u&&d.setState(1)}),oc(d.element,or?"mouseout":"mouseleave",function(){3!==u&&d.setState(u)}),d.setState=function(t){if(void 0===t&&(t=0),1!==t&&(d.state=u=t),d.removeClass(/highcharts-button-(normal|hover|pressed|disabled)/).addClass("highcharts-button-"+["normal","hover","pressed","disabled"][t]),!c){d.attr(v[t]);var e=m[t];ok(e)&&d.css(e)}},d.attr(v[0]),!c&&(d.css(oy({cursor:"default"},g)),l&&d.text.css({pointerEvents:"none"})),d.on("touchstart",function(t){return t.stopPropagation()}).on("click",function(t){3!==u&&(null==o||o.call(d,t))})},t.prototype.crispLine=function(t,e){var i=t[0],o=t[1];return ov(i[1])&&i[1]===o[1]&&(i[1]=o[1]=of(i[1],e)),ov(i[2])&&i[2]===o[2]&&(i[2]=o[2]=of(i[2],e)),t},t.prototype.path=function(t){var e=this.styledMode?{}:{fill:"none"};return ox(t)?e.d=t:ok(t)&&oy(e,t),this.createElement("path").attr(e)},t.prototype.circle=function(t,e,i){var o=ok(t)?t:void 0===t?{}:{x:t,y:e,r:i},n=this.createElement("circle");return n.xSetter=n.ySetter=function(t,e,i){i.setAttribute("c"+e,t)},n.attr(o)},t.prototype.arc=function(t,e,i,o,n,r){ok(t)?(e=(s=t).y,i=s.r,o=s.innerR,n=s.start,r=s.end,t=s.x):s={innerR:o,start:n,end:r};var s,a=this.symbol("arc",t,e,i,i,s);return a.r=i,a},t.prototype.rect=function(t,e,i,o,n,r){var s=ok(t)?t:void 0===t?{}:{x:t,y:e,r:n,width:Math.max(i||0,0),height:Math.max(o||0,0)},a=this.createElement("rect");return this.styledMode||(void 0!==r&&(s["stroke-width"]=r,oy(s,a.crisp(s))),s.fill="none"),a.rSetter=function(t,e,i){a.r=t,op(i,{rx:t,ry:t})},a.rGetter=function(){return a.r||0},a.attr(s)},t.prototype.roundedRect=function(t){return this.symbol("roundedRect").attr(t)},t.prototype.setSize=function(t,e,i){this.width=t,this.height=e,this.boxWrapper.animate({width:t,height:e},{step:function(){this.attr({viewBox:"0 0 "+this.attr("width")+" "+this.attr("height")})},duration:oS(i,!0)?void 0:0}),this.alignElements()},t.prototype.g=function(t){var e=this.createElement("g");return t?e.attr({class:"highcharts-"+t}):e},t.prototype.image=function(t,e,i,o,n,r){var s={preserveAspectRatio:"none"};ob(e)&&(s.x=e),ob(i)&&(s.y=i),ob(o)&&(s.width=o),ob(n)&&(s.height=n);var a=this.createElement("image").attr(s),h=function(e){a.attr({href:t}),r.call(a,e)};if(r){a.attr({href:"data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="});var l=new od.Image;oc(l,"load",h),l.src=t,l.complete&&h({})}else a.attr({href:t});return a},t.prototype.symbol=function(t,e,i,o,n,r){var s,a,h,l,d,c,p=this,u=/^url\((.*?)\)$/,f=u.test(t),g=!f&&(this.symbols[t]?t:"circle"),v=g&&this.symbols[g];if(v)"number"==typeof e&&(l=v.call(this.symbols,e||0,i||0,o||0,n||0,r)),h=this.path(l),p.styledMode||h.attr("fill","none"),oy(h,{symbolName:g||void 0,x:e,y:i,width:o,height:n}),r&&oy(h,r);else if(f){d=t.match(u)[1];var m=h=this.image(d);m.imgwidth=oS(null==r?void 0:r.width,null===(s=ol[d])||void 0===s?void 0:s.width),m.imgheight=oS(null==r?void 0:r.height,null===(a=ol[d])||void 0===a?void 0:a.height),c=function(t){return t.attr({width:t.width,height:t.height})},["width","height"].forEach(function(t){m[""+t+"Setter"]=function(t,e){this[e]=t;var i=this.alignByTranslate,o=this.element,n=this.width,s=this.height,a=this.imgwidth,h=this.imgheight,l="width"===e?a:h,d=1;r&&"within"===r.backgroundSize&&n&&s&&a&&h?(d=Math.min(n/a,s/h),op(o,{width:Math.round(a*d),height:Math.round(h*d)})):o&&l&&o.setAttribute(e,l),!i&&a&&h&&this.translate(((n||0)-a*d)/2,((s||0)-h*d)/2)}}),ov(e)&&m.attr({x:e,y:i}),m.isImg=!0,m.symbolUrl=t,ov(m.imgwidth)&&ov(m.imgheight)?c(m):(m.attr({width:0,height:0}),ou("img",{onload:function(){var t=oe[p.chartIndex];0===this.width&&(og(this,{position:"absolute",top:"-999em"}),oo.body.appendChild(this)),ol[d]={width:this.width,height:this.height},m.imgwidth=this.width,m.imgheight=this.height,m.element&&c(m),this.parentNode&&this.parentNode.removeChild(this),p.imgCount--,p.imgCount||!t||t.hasLoaded||t.onload()},src:d}),this.imgCount++)}return h},t.prototype.clipRect=function(t,e,i,o){return this.rect(t,e,i,o,0)},t.prototype.text=function(t,e,i,o){var n={};if(o&&(this.allowHTML||!this.forExport))return this.html(t,e,i);n.x=Math.round(e||0),i&&(n.y=Math.round(i)),ov(t)&&(n.text=t);var r=this.createElement("text").attr(n);return o&&(!this.forExport||this.allowHTML)||(r.xSetter=function(t,e,i){for(var o=i.getElementsByTagName("tspan"),n=i.getAttribute(e),r=0,s=void 0;r<o.length;r++)(s=o[r]).getAttribute(e)===n&&s.setAttribute(e,t);i.setAttribute(e,t)}),r},t.prototype.fontMetrics=function(t){var e=oA(iR.prototype.getStyle.call(t,"font-size")||0),i=e<24?e+3:Math.round(1.2*e),o=Math.round(.8*i);return{h:i,b:o,f:e}},t.prototype.rotCorr=function(t,e,i){var o=t;return e&&i&&(o=Math.max(o*Math.cos(e*oi),4)),{x:-t/3*Math.sin(e*oi),y:o}},t.prototype.pathToSegments=function(t){for(var e=[],i=[],o={A:8,C:7,H:2,L:3,M:3,Q:5,S:5,T:3,V:2},n=0;n<t.length;n++)oM(i[0])&&ob(t[n])&&i.length===o[i[0].toUpperCase()]&&t.splice(n,0,i[0].replace("M","L").replace("m","l")),"string"==typeof t[n]&&(i.length&&e.push(i.slice(0)),i.length=0),i.push(t[n]);return e.push(i.slice(0)),e},t.prototype.label=function(t,e,i,o,n,r,s,a,h){return new ij(this,t,e,i,o,n,r,s,a,h)},t.prototype.alignElements=function(){this.alignedObjects.forEach(function(t){return t.align()})},t}();oy(oO.prototype,{Element:iR,SVG_NS:oh,escapes:{"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},symbols:iJ,draw:oa}),e7.registerRendererType("svg",oO,!0);var oP=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),oE=function(){return(oE=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},oL=tv.composed,oB=tv.isFirefox,oD=tF.attr,oI=tF.css,oz=tF.createElement,oR=tF.defined,oN=tF.extend,oW=tF.getAlignFactor,oG=tF.isNumber,oX=tF.pInt,oH=tF.pushUnique;function oF(t,e,i){var o,n=(null===(o=this.div)||void 0===o?void 0:o.style)||i.style;iR.prototype[""+e+"Setter"].call(this,t,e,i),n&&(n[e]=t)}var oY=function(t,e){var i;if(!t.div){var o=oD(t.element,"class"),n=t.css,r=oz("div",o?{className:o}:void 0,oE(oE({position:"absolute",left:""+(t.translateX||0)+"px",top:""+(t.translateY||0)+"px"},t.styles),{display:t.display,opacity:t.opacity,visibility:t.visibility}),(null===(i=t.parentGroup)||void 0===i?void 0:i.div)||e);t.classSetter=function(t,e,i){i.setAttribute("class",t),r.className=t},t.translateXSetter=t.translateYSetter=function(e,i){t[i]=e,r.style["translateX"===i?"left":"top"]=""+e+"px",t.doTransform=!0},t.opacitySetter=t.visibilitySetter=oF,t.css=function(e){return n.call(t,e),e.cursor&&(r.style.cursor=e.cursor),e.pointerEvents&&(r.style.pointerEvents=e.pointerEvents),t},t.on=function(){return iR.prototype.on.apply({element:r,onEvents:t.onEvents},arguments),t},t.div=r}return t.div},o_=function(t){function e(i,o){var n=t.call(this,i,o)||this;return e.useForeignObject?n.foreignObject=i.createElement("foreignObject").attr({zIndex:2}):n.css(oE({position:"absolute"},i.styledMode?{}:{fontFamily:i.style.fontFamily,fontSize:i.style.fontSize})),n.element.style.whiteSpace="nowrap",n}return oP(e,t),e.compose=function(t){oH(oL,this.compose)&&(t.prototype.html=function(t,i,o){return new e(this,"span").attr({text:t,x:Math.round(i),y:Math.round(o)})})},e.prototype.getSpanCorrection=function(t,e,i){this.xCorr=-t*i,this.yCorr=-e},e.prototype.css=function(t){var e,i=this.element,o="SPAN"===i.tagName&&t&&"width"in t,n=o&&t.width;return o&&(delete t.width,this.textWidth=oX(n)||void 0,e=!0),(null==t?void 0:t.textOverflow)==="ellipsis"&&(t.overflow="hidden",t.whiteSpace="nowrap"),(null==t?void 0:t.lineClamp)&&(t.display="-webkit-box",t.WebkitLineClamp=t.lineClamp,t.WebkitBoxOrient="vertical",t.overflow="hidden"),oG(Number(null==t?void 0:t.fontSize))&&(t.fontSize+="px"),oN(this.styles,t),oI(i,t),e&&this.updateTransform(),this},e.prototype.htmlGetBBox=function(){var t=this.element;return{x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}},e.prototype.updateTransform=function(){if(!this.added){this.alignOnAdd=!0;return}var e,i=this.element,o=this.foreignObject,n=this.oldTextWidth,r=this.renderer,s=this.rotation,a=this.rotationOriginX,h=this.rotationOriginY,l=this.scaleX,d=this.scaleY,c=this.styles,p=c.display,u=void 0===p?"inline-block":p,f=c.whiteSpace,g=this.textAlign,v=void 0===g?"left":g,m=this.textWidth,y=this.translateX,x=this.translateY,b=this.x,k=void 0===b?0:b,M=this.y,w=void 0===M?0:M;if(o||oI(i,{marginLeft:""+(void 0===y?0:y)+"px",marginTop:""+(void 0===x?0:x)+"px"}),"SPAN"===i.tagName){var S=[s,v,i.innerHTML,m,this.textAlign].join(","),A=-((null===(e=this.parentGroup)||void 0===e?void 0:e.padding)*1)||0,T=void 0;if(m!==n){var C=this.textPxLength?this.textPxLength:(oI(i,{width:"",whiteSpace:f||"nowrap"}),i.offsetWidth),O=m||0,P=""===i.style.textOverflow&&i.style.webkitLineClamp;(O>n||C>O||P)&&(/[\-\s\u00AD]/.test(i.textContent||i.innerText)||"ellipsis"===i.style.textOverflow)&&(oI(i,{width:(s||l||C>O||P)&&oG(m)?m+"px":"auto",display:u,whiteSpace:f||"normal"}),this.oldTextWidth=m)}o&&(oI(i,{display:"inline-block",verticalAlign:"top"}),o.attr({width:r.width,height:r.height})),S!==this.cTT&&(T=r.fontMetrics(i).b,oR(s)&&!o&&(s!==(this.oldRotation||0)||v!==this.oldAlign)&&oI(i,{transform:"rotate(".concat(s,"deg)"),transformOrigin:""+A+"% "+A+"px"}),this.getSpanCorrection(!oR(s)&&!this.textWidth&&this.textPxLength||i.offsetWidth,T,oW(v)));var E=this.xCorr,L=void 0===E?0:E,B=this.yCorr,D=void 0===B?0:B,I=(null!=a?a:k)-L-k-A,z=(null!=h?h:w)-D-w-A,R={left:""+(k+L)+"px",top:""+(w+D)+"px",textAlign:v,transformOrigin:""+I+"px "+z+"px"};(l||d)&&(R.transform="scale(".concat(null!=l?l:1,",").concat(null!=d?d:1,")")),o?(t.prototype.updateTransform.call(this),oG(k)&&oG(w)?(o.attr({x:k+L,y:w+D,width:i.offsetWidth+3,height:i.offsetHeight,"transform-origin":i.getAttribute("transform-origin")||"0 0"}),oI(i,{display:u,textAlign:v})):oB&&o.attr({width:0,height:0})):oI(i,R),this.cTT=S,this.oldRotation=s,this.oldAlign=v}},e.prototype.add=function(e){var i=this.foreignObject,o=this.renderer,n=o.box.parentNode,r=[];if(i)i.add(e),t.prototype.add.call(this,o.createElement("body").attr({xmlns:"http://www.w3.org/1999/xhtml"}).css({background:"transparent",margin:"0 3px 0 0"}).add(i));else{var s=void 0;if(this.parentGroup=e,e&&!(s=e.div)){for(var a=e;a;)r.push(a),a=a.parentGroup;for(var h=0,l=r.reverse();h<l.length;h++)s=oY(l[h],n)}(s||n).appendChild(this.element)}return this.added=!0,this.alignOnAdd&&this.updateTransform(),this},e.prototype.textSetter=function(t){t!==this.textStr&&(delete this.bBox,delete this.oldTextWidth,eZ.setElementHTML(this.element,null!=t?t:""),this.textStr=t,this.doTransform=!0)},e.prototype.alignSetter=function(t){this.alignValue=this.textAlign=t,this.doTransform=!0},e.prototype.xSetter=function(t,e){this[e]=t,this.doTransform=!0},e}(iR),oj=o_.prototype;oj.visibilitySetter=oj.opacitySetter=oF,oj.ySetter=oj.rotationSetter=oj.rotationOriginXSetter=oj.rotationOriginYSetter=oj.xSetter,(u=Y||(Y={})).xAxis={alignTicks:!0,allowDecimals:void 0,panningEnabled:!0,zIndex:2,zoomEnabled:!0,dateTimeLabelFormats:{millisecond:{main:"%[HMSL]",range:!1},second:{main:"%[HMS]",range:!1},minute:{main:"%[HM]",range:!1},hour:{main:"%[HM]",range:!1},day:{main:"%[eb]"},week:{main:"%[eb]"},month:{main:"%[bY]"},year:{main:"%Y"}},endOnTick:!1,gridLineDashStyle:"Solid",gridZIndex:1,labels:{autoRotationLimit:80,distance:15,enabled:!0,indentation:10,overflow:"justify",reserveSpace:void 0,rotation:void 0,staggerLines:0,step:0,useHTML:!1,zIndex:7,style:{color:"#333333",cursor:"default",fontSize:"0.8em",textOverflow:"ellipsis"}},maxPadding:.01,minorGridLineDashStyle:"Solid",minorTickLength:2,minorTickPosition:"outside",minorTicksPerMajor:5,minPadding:.01,offset:void 0,reversed:void 0,reversedStacks:!1,showEmpty:!0,showFirstLabel:!0,showLastLabel:!0,startOfWeek:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickmarkPlacement:"between",tickPosition:"outside",title:{align:"middle",useHTML:!1,x:0,y:0,style:{color:"#666666",fontSize:"0.8em"}},visible:!0,minorGridLineColor:"#f2f2f2",minorGridLineWidth:1,minorTickColor:"#999999",lineColor:"#333333",lineWidth:1,gridLineColor:"#e6e6e6",gridLineWidth:void 0,tickColor:"#333333"},u.yAxis={reversedStacks:!0,endOnTick:!0,maxPadding:.05,minPadding:.05,tickPixelInterval:72,showLastLabel:!0,labels:{x:void 0},startOnTick:!0,title:{},stackLabels:{animation:{},allowOverlap:!1,enabled:!1,crop:!0,overflow:"justify",formatter:function(){return(0,this.axis.chart.numberFormatter)(this.total||0,-1)},style:{color:"#000000",fontSize:"0.7em",fontWeight:"bold",textOutline:"1px contrast"}},gridLineWidth:1,lineWidth:0};var oU=Y,oV=tF.addEvent,oZ=tF.isFunction,oq=tF.objectEach,oK=tF.removeEvent;(_||(_={})).registerEventOptions=function(t,e){t.eventOptions=t.eventOptions||{},oq(e.events,function(e,i){t.eventOptions[i]!==e&&(t.eventOptions[i]&&(oK(t,i,t.eventOptions[i]),delete t.eventOptions[i]),oZ(e)&&(t.eventOptions[i]=e,oV(t,i,e,{order:0})))})};var o$=_,oJ=tv.deg2rad,oQ=tF.clamp,o0=tF.correctFloat,o1=tF.defined,o2=tF.destroyObjectProperties,o3=tF.extend,o5=tF.fireEvent,o6=tF.getAlignFactor,o9=tF.isNumber,o4=tF.merge,o8=tF.objectEach,o7=tF.pick,nt=function(){function t(t,e,i,o,n){this.isNew=!0,this.isNewLabel=!0,this.axis=t,this.pos=e,this.type=i||"",this.parameters=n||{},this.tickmarkOffset=this.parameters.tickmarkOffset,this.options=this.parameters.options,o5(this,"init"),i||o||this.addLabel()}return t.prototype.addLabel=function(){var t,e,i,o,n=this,r=n.axis,s=r.options,a=r.chart,h=r.categories,l=r.logarithmic,d=r.names,c=n.pos,p=o7(null===(t=n.options)||void 0===t?void 0:t.labels,s.labels),u=r.tickPositions,f=c===u[0],g=c===u[u.length-1],v=(!p.step||1===p.step)&&1===r.tickInterval,m=u.info,y=n.label,x=this.parameters.category||(h?o7(h[c],d[c],c):c);l&&o9(x)&&(x=o0(l.lin2log(x))),r.dateTime&&(m?e=(i=a.time.resolveDTLFormat(s.dateTimeLabelFormats[!s.grid&&m.higherRanks[c]||m.unitName])).main:o9(x)&&(e=r.dateTime.getXDateFormat(x,s.dateTimeLabelFormats||{}))),n.isFirst=f,n.isLast=g;var b={axis:r,chart:a,dateTimeLabelFormat:e,isFirst:f,isLast:g,pos:c,tick:n,tickPositionInfo:m,value:x};o5(this,"labelFormat",b);var k=function(t){return p.formatter?p.formatter.call(t,t):p.format?(t.text=r.defaultLabelFormatter.call(t),e8.format(p.format,t,a)):r.defaultLabelFormatter.call(t)},M=k.call(b,b),w=null==i?void 0:i.list;w?n.shortenLabel=function(){for(o=0;o<w.length;o++)if(o3(b,{dateTimeLabelFormat:w[o]}),y.attr({text:k.call(b,b)}),y.getBBox().width<r.getSlotWidth(n)-2*(p.padding||0))return;y.attr({text:""})}:n.shortenLabel=void 0,v&&r._addedPlotLB&&n.moveLabel(M,p),o1(y)||n.movedLabel?y&&y.textStr!==M&&!v&&(!y.textWidth||p.style.width||y.styles.width||y.css({width:null}),y.attr({text:M}),y.textPxLength=y.getBBox().width):(n.label=y=n.createLabel(M,p),n.rotation=0)},t.prototype.createLabel=function(t,e,i){var o=this.axis,n=o.chart,r=n.renderer,s=n.styledMode,a=e.style.whiteSpace,h=o1(t)&&e.enabled?r.text(t,null==i?void 0:i.x,null==i?void 0:i.y,e.useHTML).add(o.labelGroup):void 0;return h&&(s||h.css(o4(e.style)),h.textPxLength=h.getBBox().width,!s&&a&&h.css({whiteSpace:a})),h},t.prototype.destroy=function(){o2(this,this.axis)},t.prototype.getPosition=function(t,e,i,o){var n=this.axis,r=n.chart,s=o&&r.oldChartHeight||r.chartHeight,a={x:t?o0(n.translate(e+i,void 0,void 0,o)+n.transB):n.left+n.offset+(n.opposite?(o&&r.oldChartWidth||r.chartWidth)-n.right-n.left:0),y:t?s-n.bottom+n.offset-(n.opposite?n.height:0):o0(s-n.translate(e+i,void 0,void 0,o)-n.transB)};return a.y=oQ(a.y,-1e9,1e9),o5(this,"afterGetPosition",{pos:a}),a},t.prototype.getLabelPosition=function(t,e,i,o,n,r,s,a){var h,l,d=this.axis,c=d.transA,p=d.isLinked&&d.linkedParent?d.linkedParent.reversed:d.reversed,u=d.staggerLines,f=d.tickRotCorr||{x:0,y:0},g=o||d.reserveSpaceDefault?0:-d.labelOffset*("center"===d.labelAlign?.5:1),v=n.distance,m={};return h=0===d.side?i.rotation?-v:-i.getBBox().height:2===d.side?f.y+v:Math.cos(i.rotation*oJ)*(f.y-i.getBBox(!1,0).height/2),o1(n.y)&&(h=0===d.side&&d.horiz?n.y+h:n.y),t=t+o7(n.x,[0,1,0,-1][d.side]*v)+g+f.x-(r&&o?r*c*(p?-1:1):0),e=e+h-(r&&!o?r*c*(p?1:-1):0),u&&(l=s/(a||1)%u,d.opposite&&(l=u-l-1),e+=l*(d.labelOffset/u)),m.x=t,m.y=Math.round(e),o5(this,"afterGetLabelPosition",{pos:m,tickmarkOffset:r,index:s}),m},t.prototype.getLabelSize=function(){return this.label?this.label.getBBox()[this.axis.horiz?"height":"width"]:0},t.prototype.getMarkPath=function(t,e,i,o,n,r){return void 0===n&&(n=!1),r.crispLine([["M",t,e],["L",t+(n?0:-i),e+(n?i:0)]],o)},t.prototype.handleOverflow=function(t){var e,i,o=this.axis,n=o.options.labels,r=t.x,s=o.chart.chartWidth,a=o.chart.spacing,h=o7(o.labelLeft,Math.min(o.pos,a[3])),l=o7(o.labelRight,Math.max(o.isRadial?0:o.pos+o.len,s-a[1])),d=this.label,c=this.rotation,p=o6(o.labelAlign||d.attr("align")),u=d.getBBox().width,f=o.getSlotWidth(this),g=f,v=1;c||"justify"!==n.overflow?c<0&&r-p*u<h?i=Math.round(r/Math.cos(c*oJ)-h):c>0&&r+p*u>l&&(i=Math.round((s-r)/Math.cos(c*oJ))):(r-p*u<h?g=t.x+g*(1-p)-h:r+(1-p)*u>l&&(g=l-t.x+g*p,v=-1),(g=Math.min(f,g))<f&&"center"===o.labelAlign&&(t.x+=v*(f-g-p*(f-Math.min(u,g)))),(u>g||o.autoRotation&&(null===(e=null==d?void 0:d.styles)||void 0===e?void 0:e.width))&&(i=g)),i&&d&&(this.shortenLabel?this.shortenLabel():d.css(o3({},{width:Math.floor(i)+"px",lineClamp:+!o.isRadial})))},t.prototype.moveLabel=function(t,e){var i,o=this,n=o.label,r=o.axis,s=!1;n&&n.textStr===t?(o.movedLabel=n,s=!0,delete o.label):o8(r.ticks,function(e){s||e.isNew||e===o||!e.label||e.label.textStr!==t||(o.movedLabel=e.label,s=!0,e.labelPos=o.movedLabel.xy,delete e.label)}),!s&&(o.labelPos||n)&&(i=o.labelPos||n.xy,o.movedLabel=o.createLabel(t,e,i),o.movedLabel&&o.movedLabel.attr({opacity:0}))},t.prototype.render=function(t,e,i){var o,n=this.axis,r=n.horiz,s=this.pos,a=o7(this.tickmarkOffset,n.tickmarkOffset),h=this.getPosition(r,s,a,e),l=h.x,d=h.y,c=n.pos,p=c+n.len,u=r?l:d,f=o7(i,null===(o=this.label)||void 0===o?void 0:o.newOpacity,1);!n.chart.polar&&(o0(u)<c||u>p)&&(i=0),null!=i||(i=1),this.isActive=!0,this.renderGridLine(e,i),this.renderMark(h,i),this.renderLabel(h,e,f,t),this.isNew=!1,o5(this,"afterRender")},t.prototype.renderGridLine=function(t,e){var i,o=this.axis,n=o.options,r={},s=this.pos,a=this.type,h=o7(this.tickmarkOffset,o.tickmarkOffset),l=o.chart.renderer,d=this.gridLine,c=n.gridLineWidth,p=n.gridLineColor,u=n.gridLineDashStyle;"minor"===this.type&&(c=n.minorGridLineWidth,p=n.minorGridLineColor,u=n.minorGridLineDashStyle),d||(o.chart.styledMode||(r.stroke=p,r["stroke-width"]=c||0,r.dashstyle=u),a||(r.zIndex=1),t&&(e=0),this.gridLine=d=l.path().attr(r).addClass("highcharts-"+(a?a+"-":"")+"grid-line").add(o.gridGroup)),d&&(i=o.getPlotLinePath({value:s+h,lineWidth:d.strokeWidth(),force:"pass",old:t,acrossPanes:!1}))&&d[t||this.isNew?"attr":"animate"]({d:i,opacity:e})},t.prototype.renderMark=function(t,e){var i=this.axis,o=i.options,n=i.chart.renderer,r=this.type,s=i.tickSize(r?r+"Tick":"tick"),a=t.x,h=t.y,l=o7(o["minor"!==r?"tickWidth":"minorTickWidth"],!r&&i.isXAxis?1:0),d=o["minor"!==r?"tickColor":"minorTickColor"],c=this.mark,p=!c;s&&(i.opposite&&(s[0]=-s[0]),c||(this.mark=c=n.path().addClass("highcharts-"+(r?r+"-":"")+"tick").add(i.axisGroup),i.chart.styledMode||c.attr({stroke:d,"stroke-width":l})),c[p?"attr":"animate"]({d:this.getMarkPath(a,h,s[0],c.strokeWidth(),i.horiz,n),opacity:e}))},t.prototype.renderLabel=function(t,e,i,o){var n=this.axis,r=n.horiz,s=n.options,a=this.label,h=s.labels,l=h.step,d=o7(this.tickmarkOffset,n.tickmarkOffset),c=t.x,p=t.y,u=!0;a&&o9(c)&&(a.xy=t=this.getLabelPosition(c,p,a,r,h,d,o,l),(!this.isFirst||this.isLast||s.showFirstLabel)&&(!this.isLast||this.isFirst||s.showLastLabel)?!r||h.step||h.rotation||e||0===i||this.handleOverflow(t):u=!1,l&&o%l&&(u=!1),u&&o9(t.y)?(t.opacity=i,a[this.isNewLabel?"attr":"animate"](t).show(!0),this.isNewLabel=!1):(a.hide(),this.isNewLabel=!0))},t.prototype.replaceMovedLabel=function(){var t=this.label,e=this.axis;t&&!this.isNew&&(t.animate({opacity:0},void 0,t.destroy),delete this.label),e.isDirty=!0,this.label=this.movedLabel,delete this.movedLabel},t}(),ne=oU.xAxis,ni=oU.yAxis,no=o$.registerEventOptions,nn=tv.deg2rad,nr=tF.arrayMax,ns=tF.arrayMin,na=tF.clamp,nh=tF.correctFloat,nl=tF.defined,nd=tF.destroyObjectProperties,nc=tF.erase,np=tF.error,nu=tF.extend,nf=tF.fireEvent,ng=tF.getClosestDistance,nv=tF.insertItem,nm=tF.isArray,ny=tF.isNumber,nx=tF.isString,nb=tF.merge,nk=tF.normalizeTickInterval,nM=tF.objectEach,nw=tF.pick,nS=tF.relativeLength,nA=tF.removeEvent,nT=tF.splat,nC=tF.syncTimeout,nO=function(t,e){return nk(e,void 0,void 0,nw(t.options.allowDecimals,e<.5||void 0!==t.tickAmount),!!t.tickAmount)};nu(en,{xAxis:ne,yAxis:nb(ne,ni)});var nP=function(){function t(t,e,i){this.init(t,e,i)}return t.prototype.init=function(t,e,i){void 0===i&&(i=this.coll);var o,n,r,s,a="xAxis"===i,h=this.isZAxis||(t.inverted?!a:a);this.chart=t,this.horiz=h,this.isXAxis=a,this.coll=i,nf(this,"init",{userOptions:e}),this.opposite=nw(e.opposite,this.opposite),this.side=nw(e.side,this.side,h?2*!this.opposite:this.opposite?1:3),this.setOptions(e);var l=this.options,d=l.labels;null!==(o=this.type)&&void 0!==o||(this.type=l.type||"linear"),null!==(n=this.uniqueNames)&&void 0!==n||(this.uniqueNames=null===(r=l.uniqueNames)||void 0===r||r),nf(this,"afterSetType"),this.userOptions=e,this.minPixelPadding=0,this.reversed=nw(l.reversed,this.reversed),this.visible=l.visible,this.zoomEnabled=l.zoomEnabled,this.hasNames="category"===this.type||!0===l.categories,this.categories=nm(l.categories)&&l.categories||(this.hasNames?[]:void 0),this.names||(this.names=[],this.names.keys={}),this.plotLinesAndBandsGroups={},this.positiveValuesOnly=!!this.logarithmic,this.isLinked=nl(l.linkedTo),this.ticks={},this.labelEdge=[],this.minorTicks={},this.plotLinesAndBands=[],this.alternateBands={},null!==(s=this.len)&&void 0!==s||(this.len=0),this.minRange=this.userMinRange=l.minRange||l.maxZoom,this.range=l.range,this.offset=l.offset||0,this.max=void 0,this.min=void 0;var c=nw(l.crosshair,nT(t.options.tooltip.crosshairs)[+!a]);this.crosshair=!0===c?{}:c,-1===t.axes.indexOf(this)&&(a?t.axes.splice(t.xAxis.length,0,this):t.axes.push(this),nv(this,t[this.coll])),t.orderItems(this.coll),this.series=this.series||[],t.inverted&&!this.isZAxis&&a&&!nl(this.reversed)&&(this.reversed=!0),this.labelRotation=ny(d.rotation)?d.rotation:void 0,no(this,l),nf(this,"afterInit")},t.prototype.setOptions=function(t){var e=this.horiz?{labels:{autoRotation:[-45],padding:3},margin:15}:{labels:{padding:1},title:{rotation:90*this.side}};this.options=nb(e,"yAxis"===this.coll?{title:{text:this.chart.options.lang.yAxisTitle}}:{},en[this.coll],t),nf(this,"afterSetOptions",{userOptions:t})},t.prototype.defaultLabelFormatter=function(){var t,e,i=this.axis,o=this.chart.numberFormatter,n=ny(this.value)?this.value:NaN,r=i.chart.time,s=i.categories,a=this.dateTimeLabelFormat,h=en.lang,l=h.numericSymbols,d=h.numericSymbolMagnitude||1e3,c=i.logarithmic?Math.abs(n):i.tickInterval,p=null==l?void 0:l.length;if(s)e="".concat(this.value);else if(a)e=r.dateFormat(a,n,!0);else if(p&&l&&c>=1e3)for(;p--&&void 0===e;)c>=(t=Math.pow(d,p+1))&&10*n%t==0&&null!==l[p]&&0!==n&&(e=o(n/t,-1)+l[p]);return void 0===e&&(e=Math.abs(n)>=1e4?o(n,-1):o(n,-1,void 0,"")),e},t.prototype.getSeriesExtremes=function(){var t,e=this;nf(this,"getSeriesExtremes",null,function(){e.hasVisibleSeries=!1,e.dataMin=e.dataMax=e.threshold=void 0,e.softThreshold=!e.isXAxis,e.series.forEach(function(i){if(i.reserveSpace()){var o=i.options,n=void 0,r=o.threshold,s=void 0,a=void 0;if(e.hasVisibleSeries=!0,e.positiveValuesOnly&&0>=(r||0)&&(r=void 0),e.isXAxis)(n=i.getColumn("x")).length&&(n=e.logarithmic?n.filter(function(t){return t>0}):n,s=(t=i.getXExtremes(n)).min,a=t.max,ny(s)||s instanceof Date||(n=n.filter(ny),s=(t=i.getXExtremes(n)).min,a=t.max),n.length&&(e.dataMin=Math.min(nw(e.dataMin,s),s),e.dataMax=Math.max(nw(e.dataMax,a),a)));else{var h=i.applyExtremes();ny(h.dataMin)&&(s=h.dataMin,e.dataMin=Math.min(nw(e.dataMin,s),s)),ny(h.dataMax)&&(a=h.dataMax,e.dataMax=Math.max(nw(e.dataMax,a),a)),nl(r)&&(e.threshold=r),(!o.softThreshold||e.positiveValuesOnly)&&(e.softThreshold=!1)}}})}),nf(this,"afterGetSeriesExtremes")},t.prototype.translate=function(t,e,i,o,n,r){var s,a=this.linkedParent||this,h=o&&a.old?a.old.min:a.min;if(!ny(h))return NaN;var l=a.minPixelPadding,d=(a.isOrdinal||(null===(s=a.brokenAxis)||void 0===s?void 0:s.hasBreaks)||a.logarithmic&&n)&&a.lin2val,c=1,p=0,u=o&&a.old?a.old.transA:a.transA,f=0;return u||(u=a.transA),i&&(c*=-1,p=a.len),a.reversed&&(c*=-1,p-=c*(a.sector||a.len)),e?(f=(t=t*c+p-l)/u+h,d&&(f=a.lin2val(f))):(d&&(t=a.val2lin(t)),f=c*(t-h)*u+p+c*l+(ny(r)?u*r:0),a.isRadial||(f=nh(f))),f},t.prototype.toPixels=function(t,e){var i,o;return this.translate(null!==(o=null===(i=this.chart)||void 0===i?void 0:i.time.parse(t))&&void 0!==o?o:NaN,!1,!this.horiz,void 0,!0)+(e?0:this.pos)},t.prototype.toValue=function(t,e){return this.translate(t-(e?0:this.pos),!0,!this.horiz,void 0,!0)},t.prototype.getPlotLinePath=function(t){var e,i,o,n,r,s=this,a=s.chart,h=s.left,l=s.top,d=t.old,c=t.value,p=t.lineWidth,u=d&&a.oldChartHeight||a.chartHeight,f=d&&a.oldChartWidth||a.chartWidth,g=s.transB,v=t.translatedValue,m=t.force;function y(t,e,i){return"pass"!==m&&(t<e||t>i)&&(m?t=na(t,e,i):r=!0),t}var x={value:c,lineWidth:p,old:d,force:m,acrossPanes:t.acrossPanes,translatedValue:v};return nf(this,"getPlotLinePath",x,function(t){e=o=(v=na(v=nw(v,s.translate(c,void 0,void 0,d)),-1e9,1e9))+g,i=n=u-v-g,ny(v)?s.horiz?(i=l,n=u-s.bottom+(s.options.isInternal?0:a.scrollablePixelsY||0),e=o=y(e,h,h+s.width)):(e=h,o=f-s.right+(a.scrollablePixelsX||0),i=n=y(i,l,l+s.height)):(r=!0,m=!1),t.path=r&&!m?void 0:a.renderer.crispLine([["M",e,i],["L",o,n]],p||1)}),x.path},t.prototype.getLinearTickPositions=function(t,e,i){var o,n,r,s=nh(Math.floor(e/t)*t),a=nh(Math.ceil(i/t)*t),h=[];if(nh(s+t)===s&&(r=20),this.single)return[e];for(o=s;o<=a&&(h.push(o),(o=nh(o+t,r))!==n);)n=o;return h},t.prototype.getMinorTickInterval=function(){var t=this.options,e=t.minorTicks,i=t.minorTickInterval;return!0===e?nw(i,"auto"):!1!==e?i:void 0},t.prototype.getMinorTickPositions=function(){var t,e,i=this.options,o=this.tickPositions,n=this.minorTickInterval,r=this.pointRangePadding||0,s=(this.min||0)-r,a=(this.max||0)+r,h=(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)?this.brokenAxis.unitLength:a-s,l=[];if(h&&h/n<this.len/3){var d=this.logarithmic;if(d)this.paddedTicks.forEach(function(t,e,i){e&&l.push.apply(l,d.getLogTickPositions(n,i[e-1],i[e],!0))});else if(this.dateTime&&"auto"===this.getMinorTickInterval())l=l.concat(this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(n),s,a,i.startOfWeek));else for(e=s+(o[0]-s)%n;e<=a&&e!==l[0];e+=n)l.push(e)}return 0!==l.length&&this.trimTicks(l),l},t.prototype.adjustForMinRange=function(){var t,e,i,o,n,r,s,a=this.options,h=this.logarithmic,l=this.chart.time,d=this.max,c=this.min,p=this.minRange;this.isXAxis&&void 0===p&&!h&&(p=nl(a.min)||nl(a.max)||nl(a.floor)||nl(a.ceiling)?null:Math.min(5*(ng(this.series.map(function(t){var e=t.getColumn("x");return t.xIncrement?e.slice(0,2):e}))||0),this.dataMax-this.dataMin)),ny(d)&&ny(c)&&ny(p)&&d-c<p&&(n=this.dataMax-this.dataMin>=p,o=(p-d+c)/2,r=[c-o,null!==(t=l.parse(a.min))&&void 0!==t?t:c-o],n&&(r[2]=h?h.log2lin(this.dataMin):this.dataMin),s=[(c=nr(r))+p,null!==(e=l.parse(a.max))&&void 0!==e?e:c+p],n&&(s[2]=h?h.log2lin(this.dataMax):this.dataMax),(d=ns(s))-c<p&&(r[0]=d-p,r[1]=null!==(i=l.parse(a.min))&&void 0!==i?i:d-p,c=nr(r))),this.minRange=p,this.min=c,this.max=d},t.prototype.getClosest=function(){var t,e;if(this.categories)e=1;else{var i=[];this.series.forEach(function(t){var o=t.closestPointRange,n=t.getColumn("x");1===n.length?i.push(n[0]):t.sorted&&nl(o)&&t.reserveSpace()&&(e=nl(e)?Math.min(e,o):o)}),i.length&&(i.sort(function(t,e){return t-e}),t=ng([i]))}return t&&e?Math.min(t,e):t||e},t.prototype.nameToX=function(t){var e,i=nm(this.options.categories),o=i?this.categories:this.names,n=t.options.x;return t.series.requireSorting=!1,nl(n)||(n=this.uniqueNames&&o?i?o.indexOf(t.name):nw(o.keys[t.name],-1):t.series.autoIncrement()),-1===n?!i&&o&&(e=o.length):ny(n)&&(e=n),void 0!==e?(this.names[e]=t.name,this.names.keys[t.name]=e):t.x&&(e=t.x),e},t.prototype.updateNames=function(){var t=this,e=this.names;e.length>0&&(Object.keys(e.keys).forEach(function(t){delete e.keys[t]}),e.length=0,this.minRange=this.userMinRange,(this.series||[]).forEach(function(e){e.xIncrement=null,(!e.points||e.isDirtyData)&&(t.max=Math.max(t.max||0,e.dataTable.rowCount-1),e.processData(),e.generatePoints());var i=e.getColumn("x").slice();e.data.forEach(function(e,o){var n=i[o];(null==e?void 0:e.options)&&void 0!==e.name&&void 0!==(n=t.nameToX(e))&&n!==e.x&&(i[o]=e.x=n)}),e.dataTable.setColumn("x",i)}))},t.prototype.setAxisTranslation=function(){var t,e,i,o=this,n=o.max-o.min,r=o.linkedParent,s=!!o.categories,a=o.isXAxis,h=o.axisPointRange||0,l=0,d=0,c=o.transA;(a||s||h)&&(e=o.getClosest(),r?(l=r.minPointOffset,d=r.pointRangePadding):o.series.forEach(function(t){var i=s?1:a?nw(t.options.pointRange,e,0):o.axisPointRange||0,n=t.options.pointPlacement;if(h=Math.max(h,i),!o.single||s){var r=t.is("xrange")?!a:a;l=Math.max(l,r&&nx(n)?0:i/2),d=Math.max(d,r&&"on"===n?0:i)}}),i=(null===(t=o.ordinal)||void 0===t?void 0:t.slope)&&e?o.ordinal.slope/e:1,o.minPointOffset=l*=i,o.pointRangePadding=d*=i,o.pointRange=Math.min(h,o.single&&s?1:n),a&&(o.closestPointRange=e)),o.translationSlope=o.transA=c=o.staticScale||o.len/(n+d||1),o.transB=o.horiz?o.left:o.bottom,o.minPixelPadding=c*l,nf(this,"afterSetAxisTranslation")},t.prototype.minFromRange=function(){var t=this.max,e=this.min;return ny(t)&&ny(e)&&t-e||void 0},t.prototype.setTickInterval=function(t){var e,i,o,n,r,s,a,h,l,d=this.categories,c=this.chart,p=this.dataMax,u=this.dataMin,f=this.dateTime,g=this.isXAxis,v=this.logarithmic,m=this.options,y=this.softThreshold,x=c.time,b=ny(this.threshold)?this.threshold:void 0,k=this.minRange||0,M=m.ceiling,w=m.floor,S=m.linkedTo,A=m.softMax,T=m.softMin,C=ny(S)&&(null===(e=c[this.coll])||void 0===e?void 0:e[S]),O=m.tickPixelInterval,P=m.maxPadding,E=m.minPadding,L=0,B=ny(m.tickInterval)&&m.tickInterval>=0?m.tickInterval:void 0;if(f||d||C||this.getTickAmount(),h=nw(this.userMin,x.parse(m.min)),l=nw(this.userMax,x.parse(m.max)),C?(this.linkedParent=C,r=C.getExtremes(),this.min=nw(r.min,r.dataMin),this.max=nw(r.max,r.dataMax),this.type!==C.type&&np(11,!0,c)):(y&&nl(b)&&ny(p)&&ny(u)&&(u>=b?(s=b,E=0):p<=b&&(a=b,P=0)),this.min=nw(h,s,u),this.max=nw(l,a,p)),ny(this.max)&&ny(this.min)&&(v&&(this.positiveValuesOnly&&!t&&0>=Math.min(this.min,nw(u,this.min))&&np(10,!0,c),this.min=nh(v.log2lin(this.min),16),this.max=nh(v.log2lin(this.max),16)),this.range&&ny(u)&&(this.userMin=this.min=h=Math.max(u,this.minFromRange()||0),this.userMax=l=this.max,this.range=void 0)),nf(this,"foundExtremes"),this.adjustForMinRange(),ny(this.min)&&ny(this.max)){if(!ny(this.userMin)&&ny(T)&&T<this.min&&(this.min=h=T),!ny(this.userMax)&&ny(A)&&A>this.max&&(this.max=l=A),d||this.axisPointRange||(null===(i=this.stacking)||void 0===i?void 0:i.usePercentage)||C||!(L=this.max-this.min)||(!nl(h)&&E&&(this.min-=L*E),nl(l)||!P||(this.max+=L*P)),!ny(this.userMin)&&ny(w)&&(this.min=Math.max(this.min,w)),!ny(this.userMax)&&ny(M)&&(this.max=Math.min(this.max,M)),y&&ny(u)&&ny(p)){var D=b||0;!nl(h)&&this.min<D&&u>=D?this.min=m.minRange?Math.min(D,this.max-k):D:!nl(l)&&this.max>D&&p<=D&&(this.max=m.minRange?Math.max(D,this.min+k):D)}!c.polar&&this.min>this.max&&(nl(m.min)?this.max=this.min:nl(m.max)&&(this.min=this.max)),L=this.max-this.min}if(this.min!==this.max&&ny(this.min)&&ny(this.max)?C&&!B&&O===C.options.tickPixelInterval?this.tickInterval=B=C.tickInterval:this.tickInterval=nw(B,this.tickAmount?L/Math.max(this.tickAmount-1,1):void 0,d?1:L*O/Math.max(this.len,O)):this.tickInterval=1,g&&!t){var I=this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(n=this.old)||void 0===n?void 0:n.max);this.series.forEach(function(t){var e;t.forceCrop=null===(e=t.forceCropping)||void 0===e?void 0:e.call(t),t.processData(I)}),nf(this,"postProcessData",{hasExtremesChanged:I})}this.setAxisTranslation(),nf(this,"initialAxisTranslation"),this.pointRange&&!B&&(this.tickInterval=Math.max(this.pointRange,this.tickInterval));var z=nw(m.minTickInterval,f&&!this.series.some(function(t){return!t.sorted})?this.closestPointRange:0);!B&&z&&this.tickInterval<z&&(this.tickInterval=z),f||v||B||(this.tickInterval=nO(this,this.tickInterval)),this.tickAmount||(this.tickInterval=this.unsquish()),this.setTickPositions()},t.prototype.setTickPositions=function(){var t,e,i,o=this.options,n=o.tickPositions,r=o.tickPositioner,s=this.getMinorTickInterval(),a=!this.isPanning,h=a&&o.startOnTick,l=a&&o.endOnTick,d=[];if(this.tickmarkOffset=this.categories&&"between"===o.tickmarkPlacement&&1===this.tickInterval?.5:0,this.single=this.min===this.max&&nl(this.min)&&!this.tickAmount&&(this.min%1==0||!1!==o.allowDecimals),n)d=n.slice();else if(ny(this.min)&&ny(this.max)){if(!(null===(t=this.ordinal)||void 0===t?void 0:t.positions)&&(this.max-this.min)/this.tickInterval>Math.max(2*this.len,200))d=[this.min,this.max],np(19,!1,this.chart);else if(this.dateTime)d=this.getTimeTicks(this.dateTime.normalizeTimeTickInterval(this.tickInterval,o.units),this.min,this.max,o.startOfWeek,null===(e=this.ordinal)||void 0===e?void 0:e.positions,this.closestPointRange,!0);else if(this.logarithmic)d=this.logarithmic.getLogTickPositions(this.tickInterval,this.min,this.max);else for(var c=this.tickInterval,p=c;p<=2*c;)if(d=this.getLinearTickPositions(this.tickInterval,this.min,this.max),this.tickAmount&&d.length>this.tickAmount)this.tickInterval=nO(this,p*=1.1);else break;d.length>this.len&&(d=[d[0],d[d.length-1]])[0]===d[1]&&(d.length=1),r&&(this.tickPositions=d,(i=r.apply(this,[this.min,this.max]))&&(d=i))}this.tickPositions=d,this.minorTickInterval="auto"===s&&this.tickInterval?this.tickInterval/o.minorTicksPerMajor:s,this.paddedTicks=d.slice(0),this.trimTicks(d,h,l),!this.isLinked&&ny(this.min)&&ny(this.max)&&(this.single&&d.length<2&&!this.categories&&!this.series.some(function(t){return t.is("heatmap")&&"between"===t.options.pointPlacement})&&(this.min-=.5,this.max+=.5),n||i||this.adjustTickAmount()),nf(this,"afterSetTickPositions")},t.prototype.trimTicks=function(t,e,i){var o=t[0],n=t[t.length-1],r=!this.isOrdinal&&this.minPointOffset||0;if(nf(this,"trimTicks"),!this.isLinked||!this.grid){if(e&&o!==-1/0)this.min=o;else for(;this.min-r>t[0];)t.shift();if(i)this.max=n;else for(;this.max+r<t[t.length-1];)t.pop();0===t.length&&nl(o)&&!this.options.tickPositions&&t.push((n+o)/2)}},t.prototype.alignToOthers=function(){var t,e=this,i=e.chart,o=[this],n=e.options,r=i.options.chart,s="yAxis"===this.coll&&r.alignThresholds,a=[];if(e.thresholdAlignment=void 0,(!1!==r.alignTicks&&n.alignTicks||s)&&!1!==n.startOnTick&&!1!==n.endOnTick&&!e.logarithmic){var h=function(t){var e=t.horiz,i=t.options;return[e?i.left:i.top,i.width,i.height,i.pane].join(",")},l=h(this);i[this.coll].forEach(function(i){var n=i.series;n.length&&n.some(function(t){return t.visible})&&i!==e&&h(i)===l&&(t=!0,o.push(i))})}if(t&&s){o.forEach(function(t){var i=t.getThresholdAlignment(e);ny(i)&&a.push(i)});var d=a.length>1?a.reduce(function(t,e){return t+e},0)/a.length:void 0;o.forEach(function(t){t.thresholdAlignment=d})}return t},t.prototype.getThresholdAlignment=function(t){if((!ny(this.dataMin)||this!==t&&this.series.some(function(t){return t.isDirty||t.isDirtyData}))&&this.getSeriesExtremes(),ny(this.threshold)){var e=na((this.threshold-(this.dataMin||0))/((this.dataMax||0)-(this.dataMin||0)),0,1);return this.options.reversed&&(e=1-e),e}},t.prototype.getTickAmount=function(){var t=this.options,e=t.tickPixelInterval,i=t.tickAmount;nl(t.tickInterval)||i||!(this.len<e)||this.isRadial||this.logarithmic||!t.startOnTick||!t.endOnTick||(i=2),!i&&this.alignToOthers()&&(i=Math.ceil(this.len/e)+1),i<4&&(this.finalTickAmt=i,i=5),this.tickAmount=i},t.prototype.adjustTickAmount=function(){var t,e,i,o=this,n=o.finalTickAmt,r=o.max,s=o.min,a=o.options,h=o.tickPositions,l=o.tickAmount,d=o.thresholdAlignment,c=null==h?void 0:h.length,p=nw(o.threshold,o.softThreshold?0:null),u=o.tickInterval,f=function(){return h.push(nh(h[h.length-1]+u))},g=function(){return h.unshift(nh(h[0]-u))};if(ny(d)&&(i=d<.5?Math.ceil(d*(l-1)):Math.floor(d*(l-1)),a.reversed&&(i=l-1-i)),o.hasData()&&ny(s)&&ny(r)){var v=function(){o.transA*=(c-1)/(l-1),o.min=a.startOnTick?h[0]:Math.min(s,h[0]),o.max=a.endOnTick?h[h.length-1]:Math.max(r,h[h.length-1])};if(ny(i)&&ny(o.threshold)){for(;h[i]!==p||h.length!==l||h[0]>s||h[h.length-1]<r;){for(h.length=0,h.push(o.threshold);h.length<l;)void 0===h[i]||h[i]>o.threshold?g():f();if(u>8*o.tickInterval)break;u*=2}v()}else if(c<l){for(;h.length<l;)h.length%2||s===p?f():g();v()}if(nl(n)){for(e=t=h.length;e--;)(3===n&&e%2==1||n<=2&&e>0&&e<t-1)&&h.splice(e,1);o.finalTickAmt=void 0}}},t.prototype.setScale=function(){var t,e,i,o,n,r=this.coll,s=this.stacking,a=!1,h=!1;this.series.forEach(function(t){var e;a=a||t.isDirtyData||t.isDirty,h=h||(null===(e=t.xAxis)||void 0===e?void 0:e.isDirty)||!1}),this.setAxisSize();var l=this.len!==(null===(t=this.old)||void 0===t?void 0:t.len);l||a||h||this.isLinked||this.forceRedraw||this.userMin!==(null===(e=this.old)||void 0===e?void 0:e.userMin)||this.userMax!==(null===(i=this.old)||void 0===i?void 0:i.userMax)||this.alignToOthers()?(s&&"yAxis"===r&&s.buildStacks(),this.forceRedraw=!1,this.userMinRange||(this.minRange=void 0),this.getSeriesExtremes(),this.setTickInterval(),s&&"xAxis"===r&&s.buildStacks(),this.isDirty||(this.isDirty=l||this.min!==(null===(o=this.old)||void 0===o?void 0:o.min)||this.max!==(null===(n=this.old)||void 0===n?void 0:n.max))):s&&s.cleanStacks(),a&&delete this.allExtremes,nf(this,"afterSetScale")},t.prototype.setExtremes=function(t,e,i,o,n){var r=this;void 0===i&&(i=!0);var s=this.chart;this.series.forEach(function(t){delete t.kdTree}),nf(this,"setExtremes",n=nu(n,{min:t=s.time.parse(t),max:e=s.time.parse(e)}),function(t){r.userMin=t.min,r.userMax=t.max,r.eventArgs=t,i&&s.redraw(o)})},t.prototype.setAxisSize=function(){var t=this.chart,e=this.options,i=e.offsets||[0,0,0,0],o=this.horiz,n=this.width=Math.round(nS(nw(e.width,t.plotWidth-i[3]+i[1]),t.plotWidth)),r=this.height=Math.round(nS(nw(e.height,t.plotHeight-i[0]+i[2]),t.plotHeight)),s=this.top=Math.round(nS(nw(e.top,t.plotTop+i[0]),t.plotHeight,t.plotTop)),a=this.left=Math.round(nS(nw(e.left,t.plotLeft+i[3]),t.plotWidth,t.plotLeft));this.bottom=t.chartHeight-r-s,this.right=t.chartWidth-n-a,this.len=Math.max(o?n:r,0),this.pos=o?a:s},t.prototype.getExtremes=function(){var t=this.logarithmic;return{min:t?nh(t.lin2log(this.min)):this.min,max:t?nh(t.lin2log(this.max)):this.max,dataMin:this.dataMin,dataMax:this.dataMax,userMin:this.userMin,userMax:this.userMax}},t.prototype.getThreshold=function(t){var e=this.logarithmic,i=e?e.lin2log(this.min):this.min,o=e?e.lin2log(this.max):this.max;return null===t||t===-1/0?t=i:t===1/0?t=o:i>t?t=i:o<t&&(t=o),this.translate(t,0,1,0,1)},t.prototype.autoLabelAlign=function(t){var e=(nw(t,0)-90*this.side+720)%360,i={align:"center"};return nf(this,"autoLabelAlign",i,function(t){e>15&&e<165?t.align="right":e>195&&e<345&&(t.align="left")}),i.align},t.prototype.tickSize=function(t){var e,i=this.options,o=nw(i["tick"===t?"tickWidth":"minorTickWidth"],"tick"===t&&this.isXAxis&&!this.categories?1:0),n=i["tick"===t?"tickLength":"minorTickLength"];o&&n&&("inside"===i[t+"Position"]&&(n=-n),e=[n,o]);var r={tickSize:e};return nf(this,"afterTickSize",r),r.tickSize},t.prototype.labelMetrics=function(){var t=this.chart.renderer,e=this.ticks,i=e[Object.keys(e)[0]]||{};return this.chart.renderer.fontMetrics(i.label||i.movedLabel||t.box)},t.prototype.unsquish=function(){var t,e,i=this.options.labels,o=i.padding||0,n=this.horiz,r=this.tickInterval,s=this.len/((+!!this.categories+this.max-this.min)/r),a=i.rotation,h=nh(.8*this.labelMetrics().h),l=Math.max(this.max-this.min,0),d=function(t){var e=(t+2*o)/(s||1);return(e=e>1?Math.ceil(e):1)*r>l&&t!==1/0&&s!==1/0&&l&&(e=Math.ceil(l/r)),nh(e*r)},c=r,p=Number.MAX_VALUE;if(n){if(!i.staggerLines&&(ny(a)?e=[a]:s<i.autoRotationLimit&&(e=i.autoRotation)),e)for(var u=void 0,f=void 0,g=0,v=e;g<v.length;g++){var m=v[g];(m===a||m&&m>=-90&&m<=90)&&(f=(u=d(Math.abs(h/Math.sin(nn*m))))+Math.abs(m/360))<p&&(p=f,t=m,c=u)}}else c=d(.75*h);return this.autoRotation=e,this.labelRotation=nw(t,ny(a)?a:0),i.step?r:c},t.prototype.getSlotWidth=function(t){var e=this.chart,i=this.horiz,o=this.options.labels,n=Math.max(this.tickPositions.length-+!this.categories,1),r=e.margin[3];if(t&&ny(t.slotWidth))return t.slotWidth;if(i&&o.step<2&&!this.isRadial)return o.rotation?0:(this.staggerLines||1)*this.len/n;if(!i){var s=o.style.width;if(void 0!==s)return parseInt(String(s),10);if(r)return r-e.spacing[3]}return .33*e.chartWidth},t.prototype.renderUnsquish=function(){var t,e=this.chart,i=e.renderer,o=this.tickPositions,n=this.ticks,r=this.options.labels,s=r.style,a=this.horiz,h=this.getSlotWidth(),l=Math.max(1,Math.round(h-(a?2*(r.padding||0):r.distance||0))),d={},c=this.labelMetrics(),p=s.lineClamp,u=null!=p?p:Math.floor(this.len/(o.length*c.h))||1,f=0;nx(r.rotation)||(d.rotation=r.rotation||0),o.forEach(function(t){var e,i=n[t];i.movedLabel&&i.replaceMovedLabel();var o=(null===(e=i.label)||void 0===e?void 0:e.textPxLength)||0;o>f&&(f=o)}),this.maxLabelLength=f,this.autoRotation?f>l&&f>c.h?d.rotation=this.labelRotation:this.labelRotation=0:h&&(t=l),d.rotation&&(t=f>.5*e.chartHeight?.33*e.chartHeight:f,p||(u=1)),this.labelAlign=r.align||this.autoLabelAlign(this.labelRotation),this.labelAlign&&(d.align=this.labelAlign),o.forEach(function(e){var i=n[e],o=null==i?void 0:i.label,r=s.width,a={};o&&(o.attr(d),i.shortenLabel?i.shortenLabel():t&&!r&&"nowrap"!==s.whiteSpace&&(t<(o.textPxLength||0)||"SPAN"===o.element.tagName)?o.css(nu(a,{width:""+t+"px",lineClamp:u})):!o.styles.width||a.width||r||o.css({width:"auto"}),i.rotation=d.rotation)},this),this.tickRotCorr=i.rotCorr(c.b,this.labelRotation||0,0!==this.side)},t.prototype.hasData=function(){return this.series.some(function(t){return t.hasData()})||this.options.showEmpty&&nl(this.min)&&nl(this.max)},t.prototype.addTitle=function(t){var e,i=this.chart.renderer,o=this.horiz,n=this.opposite,r=this.options.title,s=this.chart.styledMode;this.axisTitle||((e=r.textAlign)||(e=(o?{low:"left",middle:"center",high:"right"}:{low:n?"right":"left",middle:"center",high:n?"left":"right"})[r.align]),this.axisTitle=i.text(r.text||"",0,0,r.useHTML).attr({zIndex:7,rotation:r.rotation||0,align:e}).addClass("highcharts-axis-title"),s||this.axisTitle.css(nb(r.style)),this.axisTitle.add(this.axisGroup),this.axisTitle.isNew=!0),s||r.style.width||this.isRadial||this.axisTitle.css({width:this.len+"px"}),this.axisTitle[t?"show":"hide"](t)},t.prototype.generateTick=function(t){var e=this.ticks;e[t]?e[t].addLabel():e[t]=new nt(this,t)},t.prototype.createGroups=function(){var t=this,e=this.axisParent,i=this.chart,o=this.coll,n=this.options,r=i.renderer,s=function(i,s,a){return r.g(i).attr({zIndex:a}).addClass("highcharts-".concat(o.toLowerCase()).concat(s," ")+(t.isRadial?"highcharts-radial-axis".concat(s," "):"")+(n.className||"")).add(e)};this.axisGroup||(this.gridGroup=s("grid","-grid",n.gridZIndex),this.axisGroup=s("axis","",n.zIndex),this.labelGroup=s("axis-labels","-labels",n.labels.zIndex))},t.prototype.getOffset=function(){var t,e,i,o,n=this,r=n.chart,s=n.horiz,a=n.options,h=n.side,l=n.ticks,d=n.tickPositions,c=n.coll,p=r.inverted&&!n.isZAxis?[1,0,3,2][h]:h,u=n.hasData(),f=a.title,g=a.labels,v=ny(a.crossing),m=r.axisOffset,y=r.clipOffset,x=[-1,1,1,-1][h],b=0,k=0,M=0;if(n.showAxis=t=u||a.showEmpty,n.staggerLines=n.horiz&&g.staggerLines||void 0,n.createGroups(),u||n.isLinked?(d.forEach(function(t){n.generateTick(t)}),n.renderUnsquish(),n.reserveSpaceDefault=0===h||2===h||({1:"left",3:"right"})[h]===n.labelAlign,nw(g.reserveSpace,!v&&null,"center"===n.labelAlign||null,n.reserveSpaceDefault)&&d.forEach(function(t){M=Math.max(l[t].getLabelSize(),M)}),n.staggerLines&&(M*=n.staggerLines),n.labelOffset=M*(n.opposite?-1:1)):nM(l,function(t,e){t.destroy(),delete l[e]}),(null==f?void 0:f.text)&&!1!==f.enabled&&(n.addTitle(t),t&&!v&&!1!==f.reserveSpace&&(n.titleOffset=b=n.axisTitle.getBBox()[s?"height":"width"],k=nl(e=f.offset)?0:nw(f.margin,s?5:10))),n.renderLine(),n.offset=x*nw(a.offset,m[h]?m[h]+(a.margin||0):0),n.tickRotCorr=n.tickRotCorr||{x:0,y:0},o=0===h?-n.labelMetrics().h:2===h?n.tickRotCorr.y:0,i=Math.abs(M)+k,M&&(i-=o,i+=x*(s?nw(g.y,n.tickRotCorr.y+x*g.distance):nw(g.x,x*g.distance))),n.axisTitleMargin=nw(e,i),n.getMaxLabelDimensions&&(n.maxLabelDimensions=n.getMaxLabelDimensions(l,d)),"colorAxis"!==c&&y){var w=this.tickSize("tick");m[h]=Math.max(m[h],(n.axisTitleMargin||0)+b+x*n.offset,i,(null==d?void 0:d.length)&&w?w[0]+x*n.offset:0);var S=!n.axisLine||a.offset?0:n.axisLine.strokeWidth()/2;y[p]=Math.max(y[p],S)}nf(this,"afterGetOffset")},t.prototype.getLinePath=function(t){var e=this.chart,i=this.opposite,o=this.offset,n=this.horiz,r=this.left+(i?this.width:0)+o,s=e.chartHeight-this.bottom-(i?this.height:0)+o;return i&&(t*=-1),e.renderer.crispLine([["M",n?this.left:r,n?s:this.top],["L",n?e.chartWidth-this.right:r,n?s:e.chartHeight-this.bottom]],t)},t.prototype.renderLine=function(){this.axisLine||(this.axisLine=this.chart.renderer.path().addClass("highcharts-axis-line").add(this.axisGroup),this.chart.styledMode||this.axisLine.attr({stroke:this.options.lineColor,"stroke-width":this.options.lineWidth,zIndex:7}))},t.prototype.getTitlePosition=function(t){var e=this.horiz,i=this.left,o=this.top,n=this.len,r=this.options.title,s=e?i:o,a=this.opposite,h=this.offset,l=r.x,d=r.y,c=this.chart.renderer.fontMetrics(t),p=t?Math.max(t.getBBox(!1,0).height-c.h-1,0):0,u={low:s+(e?0:n),middle:s+n/2,high:s+(e?n:0)}[r.align],f=(e?o+this.height:i)+(e?1:-1)*(a?-1:1)*(this.axisTitleMargin||0)+[-p,p,c.f,-p][this.side],g={x:e?u+l:f+(a?this.width:0)+h+l,y:e?f+d-(a?this.height:0)+h:u+d};return nf(this,"afterGetTitlePosition",{titlePosition:g}),g},t.prototype.renderMinorTick=function(t,e){var i=this.minorTicks;i[t]||(i[t]=new nt(this,t,"minor")),e&&i[t].isNew&&i[t].render(null,!0),i[t].render(null,!1,1)},t.prototype.renderTick=function(t,e,i){var o,n=this.isLinked,r=this.ticks;(!n||t>=this.min&&t<=this.max||(null===(o=this.grid)||void 0===o?void 0:o.isColumn))&&(r[t]||(r[t]=new nt(this,t)),i&&r[t].isNew&&r[t].render(e,!0,-1),r[t].render(e))},t.prototype.render=function(){var t,e,i=this,o=i.chart,n=i.logarithmic,r=o.renderer,s=i.options,a=i.isLinked,h=i.tickPositions,l=i.axisTitle,d=i.ticks,c=i.minorTicks,p=i.alternateBands,u=s.stackLabels,f=s.alternateGridColor,g=s.crossing,v=i.tickmarkOffset,m=i.axisLine,y=i.showAxis,x=eE(r.globalAnimation);if(i.labelEdge.length=0,i.overlap=!1,[d,c,p].forEach(function(t){nM(t,function(t){t.isActive=!1})}),ny(g)){var b=this.isXAxis?o.yAxis[0]:o.xAxis[0],k=[1,-1,-1,1][this.side];if(b){var M=b.toPixels(g,!0);i.horiz&&(M=b.len-M),i.offset=k*M}}if(i.hasData()||a){var w=i.chart.hasRendered&&i.old&&ny(i.old.min);i.minorTickInterval&&!i.categories&&i.getMinorTickPositions().forEach(function(t){i.renderMinorTick(t,w)}),h.length&&(h.forEach(function(t,e){i.renderTick(t,e,w)}),v&&(0===i.min||i.single)&&(d[-1]||(d[-1]=new nt(i,-1,null,!0)),d[-1].render(-1))),f&&h.forEach(function(r,s){e=void 0!==h[s+1]?h[s+1]+v:i.max-v,s%2==0&&r<i.max&&e<=i.max+(o.polar?-v:v)&&(p[r]||(p[r]=new tv.PlotLineOrBand(i,{})),t=r+v,p[r].options={from:n?n.lin2log(t):t,to:n?n.lin2log(e):e,color:f,className:"highcharts-alternate-grid"},p[r].render(),p[r].isActive=!0)}),i._addedPlotLB||(i._addedPlotLB=!0,(s.plotLines||[]).concat(s.plotBands||[]).forEach(function(t){i.addPlotBandOrLine(t)}))}[d,c,p].forEach(function(t){var e=[],i=x.duration;nM(t,function(t,i){t.isActive||(t.render(i,!1,0),t.isActive=!1,e.push(i))}),nC(function(){for(var i=e.length;i--;)t[e[i]]&&!t[e[i]].isActive&&(t[e[i]].destroy(),delete t[e[i]])},t!==p&&o.hasRendered&&i?i:0)}),m&&(m[m.isPlaced?"animate":"attr"]({d:this.getLinePath(m.strokeWidth())}),m.isPlaced=!0,m[y?"show":"hide"](y)),l&&y&&(l[l.isNew?"attr":"animate"](i.getTitlePosition(l)),l.isNew=!1),(null==u?void 0:u.enabled)&&i.stacking&&i.stacking.renderStackTotals(),i.old={len:i.len,max:i.max,min:i.min,transA:i.transA,userMax:i.userMax,userMin:i.userMin},i.isDirty=!1,nf(this,"afterRender")},t.prototype.redraw=function(){this.visible&&(this.render(),this.plotLinesAndBands.forEach(function(t){t.render()})),this.series.forEach(function(t){t.isDirty=!0})},t.prototype.getKeepProps=function(){return this.keepProps||t.keepProps},t.prototype.destroy=function(t){var e=this,i=e.plotLinesAndBands,o=this.eventOptions;if(nf(this,"destroy",{keepEvents:t}),t||nA(e),[e.ticks,e.minorTicks,e.alternateBands].forEach(function(t){nd(t)}),i)for(var n=i.length;n--;)i[n].destroy();for(var r in["axisLine","axisTitle","axisGroup","gridGroup","labelGroup","cross","scrollbar"].forEach(function(t){e[t]&&(e[t]=e[t].destroy())}),e.plotLinesAndBandsGroups)e.plotLinesAndBandsGroups[r]=e.plotLinesAndBandsGroups[r].destroy();nM(e,function(t,i){-1===e.getKeepProps().indexOf(i)&&delete e[i]}),this.eventOptions=o},t.prototype.drawCrosshair=function(t,e){var i,o,n,r,s,a,h=this.crosshair,l=null===(i=null==h?void 0:h.snap)||void 0===i||i,d=this.chart,c=this.cross;if(nf(this,"drawCrosshair",{e:t,point:e}),t||(t=null===(o=this.cross)||void 0===o?void 0:o.e),h&&!1!==(nl(e)||!l)){if(l?nl(e)&&(r=nw("colorAxis"!==this.coll?e.crosshairPos:null,this.isXAxis?e.plotX:this.len-e.plotY)):r=t&&(this.horiz?t.chartX-this.pos:this.len-t.chartY+this.pos),nl(r)&&(a={value:e&&(this.isXAxis?e.x:nw(e.stackY,e.y)),translatedValue:r},d.polar&&nu(a,{isCrosshair:!0,chartX:null==t?void 0:t.chartX,chartY:null==t?void 0:t.chartY,point:e}),n=this.getPlotLinePath(a)||null),!nl(n)){this.hideCrosshair();return}s=this.categories&&!this.isRadial,c||(this.cross=c=d.renderer.path().addClass("highcharts-crosshair highcharts-crosshair-"+(s?"category ":"thin ")+(h.className||"")).attr({zIndex:nw(h.zIndex,2)}).add(),!d.styledMode&&(c.attr({stroke:h.color||(s?ev.parse("#ccd3ff").setOpacity(.25).get():"#cccccc"),"stroke-width":nw(h.width,1)}).css({"pointer-events":"none"}),h.dashStyle&&c.attr({dashstyle:h.dashStyle}))),c.show().attr({d:n}),s&&!h.width&&c.attr({"stroke-width":this.transA}),this.cross.e=t}else this.hideCrosshair();nf(this,"afterDrawCrosshair",{e:t,point:e})},t.prototype.hideCrosshair=function(){this.cross&&this.cross.hide(),nf(this,"afterHideCrosshair")},t.prototype.update=function(t,e){var i=this.chart;t=nb(this.userOptions,t),this.destroy(!0),this.init(i,t),i.isDirtyBox=!0,nw(e,!0)&&i.redraw()},t.prototype.remove=function(t){for(var e=this.chart,i=this.coll,o=this.series,n=o.length;n--;)o[n]&&o[n].remove(!1);nc(e.axes,this),nc(e[i]||[],this),e.orderItems(i),this.destroy(),e.isDirtyBox=!0,nw(t,!0)&&e.redraw()},t.prototype.setTitle=function(t,e){this.update({title:t},e)},t.prototype.setCategories=function(t,e){this.update({categories:t},e)},t.keepProps=["coll","extKey","hcEvents","len","names","series","userMax","userMin"],t}(),nE=tF.addEvent,nL=tF.getMagnitude,nB=tF.normalizeTickInterval,nD=tF.timeUnits;!function(t){function e(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function i(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=e,nE(t,"afterSetType",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.normalizeTimeTickInterval=function(t,e){var i,o=e||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],n=o[o.length-1],r=nD[n[0]],s=n[1];for(i=0;i<o.length&&(r=nD[(n=o[i])[0]],s=n[1],!o[i+1]||!(t<=(r*s[s.length-1]+nD[o[i+1][0]])/2));i++);r===nD.year&&t<5*r&&(s=[1,2,5]);var a=nB(t/r,s,"year"===n[0]?Math.max(nL(t/r),1):1);return{unitRange:r,count:a,unitName:n[0]}},t.prototype.getXDateFormat=function(t,e){var i=this.axis,o=i.chart.time;return i.closestPointRange?o.getDateFormat(i.closestPointRange,t,i.options.startOfWeek,e)||o.resolveDTLFormat(e.year).main:o.resolveDTLFormat(e.day).main},t}();t.Additions=o}(j||(j={}));var nI=j,nz=tF.addEvent,nR=tF.normalizeTickInterval,nN=tF.pick;!function(t){function e(){var t;"logarithmic"!==this.type?this.logarithmic=void 0:null!==(t=this.logarithmic)&&void 0!==t||(this.logarithmic=new o(this))}function i(){var t=this.logarithmic;t&&(this.lin2val=function(e){return t.lin2log(e)},this.val2lin=function(e){return t.log2lin(e)})}t.compose=function(t){return t.keepProps.includes("logarithmic")||(t.keepProps.push("logarithmic"),nz(t,"afterSetType",e),nz(t,"afterInit",i)),t};var o=function(){function t(t){this.axis=t}return t.prototype.getLogTickPositions=function(t,e,i,o){var n=this.axis,r=n.len,s=n.options,a=[];if(o||(this.minorAutoInterval=void 0),t>=.5)t=Math.round(t),a=n.getLinearTickPositions(t,e,i);else if(t>=.08){var h=Math.floor(e),l=void 0,d=void 0,c=void 0,p=void 0,u=void 0,f=void 0,g=void 0;for(l=t>.3?[1,2,4]:t>.15?[1,2,4,6,8]:[1,2,3,4,5,6,7,8,9],d=h;d<i+1&&!g;d++)for(c=0,p=l.length;c<p&&!g;c++)(u=this.log2lin(this.lin2log(d)*l[c]))>e&&(!o||f<=i)&&void 0!==f&&a.push(f),f>i&&(g=!0),f=u}else{var v=this.lin2log(e),m=this.lin2log(i),y=o?n.getMinorTickInterval():s.tickInterval,x=s.tickPixelInterval/(o?5:1),b=o?r/n.tickPositions.length:r;t=nR(t=nN("auto"===y?null:y,this.minorAutoInterval,(m-v)*x/(b||1))),a=n.getLinearTickPositions(t,v,m).map(this.log2lin),o||(this.minorAutoInterval=t/5)}return o||(n.tickInterval=t),a},t.prototype.lin2log=function(t){return Math.pow(10,t)},t.prototype.log2lin=function(t){return Math.log(t)/Math.LN10},t}();t.Additions=o}(U||(U={}));var nW=U,nG=tF.erase,nX=tF.extend,nH=tF.isNumber;!function(t){var e;function i(t){return this.addPlotBandOrLine(t,"plotBands")}function o(t,i){var o=this,n=this.userOptions,r=new e(this,t);if(this.visible&&(r=r.render()),r){if(this._addedPlotLB||(this._addedPlotLB=!0,(n.plotLines||[]).concat(n.plotBands||[]).forEach(function(t){o.addPlotBandOrLine(t)})),i){var s=n[i]||[];s.push(t),n[i]=s}this.plotLinesAndBands.push(r)}return r}function n(t){return this.addPlotBandOrLine(t,"plotLines")}function r(t,e,i){i=i||this.options;var o,n,r=this.getPlotLinePath({value:e,force:!0,acrossPanes:i.acrossPanes}),s=[],a=this.horiz,h=!nH(this.min)||!nH(this.max)||t<this.min&&e<this.min||t>this.max&&e>this.max,l=this.getPlotLinePath({value:t,force:!0,acrossPanes:i.acrossPanes}),d=1;if(l&&r)for(h&&(n=l.toString()===r.toString(),d=0),o=0;o<l.length;o+=2){var c=l[o],p=l[o+1],u=r[o],f=r[o+1];("M"===c[0]||"L"===c[0])&&("M"===p[0]||"L"===p[0])&&("M"===u[0]||"L"===u[0])&&("M"===f[0]||"L"===f[0])&&(a&&u[1]===c[1]?(u[1]+=d,f[1]+=d):a||u[2]!==c[2]||(u[2]+=d,f[2]+=d),s.push(["M",c[1],c[2]],["L",p[1],p[2]],["L",f[1],f[2]],["L",u[1],u[2]],["Z"])),s.isFlat=n}return s}function s(t){this.removePlotBandOrLine(t)}function a(t){var e=this.plotLinesAndBands,i=this.options,o=this.userOptions;if(e){for(var n=e.length;n--;)e[n].id===t&&e[n].destroy();[i.plotLines||[],o.plotLines||[],i.plotBands||[],o.plotBands||[]].forEach(function(e){var i;for(n=e.length;n--;)(null===(i=e[n])||void 0===i?void 0:i.id)===t&&nG(e,e[n])})}}function h(t){this.removePlotBandOrLine(t)}t.compose=function(t,l){var d=l.prototype;return d.addPlotBand||(e=t,nX(d,{addPlotBand:i,addPlotLine:n,addPlotBandOrLine:o,getPlotBandPath:r,removePlotBand:s,removePlotLine:h,removePlotBandOrLine:a})),l}}(V||(V={}));var nF=V,nY=function(){return(nY=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},n_=tF.addEvent,nj=tF.arrayMax,nU=tF.arrayMin,nV=tF.defined,nZ=tF.destroyObjectProperties,nq=tF.erase,nK=tF.fireEvent,n$=tF.merge,nJ=tF.objectEach,nQ=tF.pick,n0=function(){function t(t,e){this.axis=t,this.options=e,this.id=e.id}return t.compose=function(e,i){return n_(e,"afterInit",function(){var t=this;this.labelCollectors.push(function(){for(var e,i=[],o=0,n=t.axes;o<n.length;o++)for(var r=n[o],s=0,a=r.plotLinesAndBands;s<a.length;s++){var h=a[s],l=h.label,d=h.options;!l||(null===(e=null==d?void 0:d.label)||void 0===e?void 0:e.allowOverlap)||i.push(l)}return i})}),nF.compose(t,i)},t.prototype.render=function(){var t=this;nK(this,"render");var e,i,o,n,r=this.axis,s=this.options,a=r.horiz,h=r.logarithmic,l=s.color,d=s.events,c=s.zIndex,p=void 0===c?0:c,u=r.chart,f=u.renderer,g=u.time,v={},m=g.parse(s.to),y=g.parse(s.from),x=g.parse(s.value),b=s.borderWidth,k=s.label,M=this.label,w=this.svgElem,S=[],A=nV(y)&&nV(m),T=nV(x),C=!w,O={class:"highcharts-plot-"+(A?"band ":"line ")+(s.className||"")},P=A?"bands":"lines";if(!r.chart.styledMode&&(T?(O.stroke=l||"#999999",O["stroke-width"]=nQ(s.width,1),s.dashStyle&&(O.dashstyle=s.dashStyle)):A&&(O.fill=l||"#e6e9ff",b&&(O.stroke=s.borderColor,O["stroke-width"]=b))),v.zIndex=p,P+="-"+p,(n=r.plotLinesAndBandsGroups[P])||(r.plotLinesAndBandsGroups[P]=n=f.g("plot-"+P).attr(v).add()),w||(this.svgElem=w=f.path().attr(O).add(n)),nV(x))S=r.getPlotLinePath({value:null!==(e=null==h?void 0:h.log2lin(x))&&void 0!==e?e:x,lineWidth:w.strokeWidth(),acrossPanes:s.acrossPanes});else{if(!(nV(y)&&nV(m)))return;S=r.getPlotBandPath(null!==(i=null==h?void 0:h.log2lin(y))&&void 0!==i?i:y,null!==(o=null==h?void 0:h.log2lin(m))&&void 0!==o?o:m,s)}return!this.eventsAdded&&d&&(nJ(d,function(e,i){null==w||w.on(i,function(e){d[i].apply(t,[e])})}),this.eventsAdded=!0),(C||!w.d)&&(null==S?void 0:S.length)?w.attr({d:S}):w&&(S?(w.show(),w.animate({d:S})):w.d&&(w.hide(),M&&(this.label=M=M.destroy()))),k&&(nV(k.text)||nV(k.formatter))&&(null==S?void 0:S.length)&&r.width>0&&r.height>0&&!S.isFlat?(k=n$(nY({align:a&&A?"center":void 0,x:a?!A&&4:10,verticalAlign:!a&&A?"middle":void 0,y:a?A?16:10:A?6:-4,rotation:a&&!A?90:0},A?{inside:!0}:{}),k),this.renderLabel(k,S,A,p)):M&&M.hide(),this},t.prototype.renderLabel=function(t,e,i,o){var n,r=this.axis,s=r.chart.renderer,a=t.inside,h=this.label;h||(this.label=h=s.text(this.getLabelText(t),0,0,t.useHTML).attr({align:t.textAlign||t.align,rotation:t.rotation,class:"highcharts-plot-"+(i?"band":"line")+"-label "+(t.className||""),zIndex:o}),r.chart.styledMode||h.css(n$({fontSize:"0.8em",textOverflow:i&&!a?"":"ellipsis"},t.style)),h.add());var l=e.xBounds||[e[0][1],e[1][1],i?e[2][1]:e[0][1]],d=e.yBounds||[e[0][2],e[1][2],i?e[2][2]:e[0][2]],c=nU(l),p=nU(d),u=nj(l)-c;h.align(t,!1,{x:c,y:p,width:u,height:nj(d)-p}),h.alignAttr.y-=s.fontMetrics(h).b,(!h.alignValue||"left"===h.alignValue||nV(a))&&h.css({width:((null===(n=t.style)||void 0===n?void 0:n.width)||(i&&a?u:90===h.rotation?r.height-(h.alignAttr.y-r.top):(t.clip?r.width:r.chart.chartWidth)-(h.alignAttr.x-r.left)))+"px"}),h.show(!0)},t.prototype.getLabelText=function(t){return nV(t.formatter)?t.formatter.call(this):t.text},t.prototype.destroy=function(){nq(this.axis.plotLinesAndBands,this),delete this.axis,nZ(this)},t}(),n1=e8.format,n2=tv.composed,n3=tv.dateFormats,n5=tv.doc,n6=tv.isSafari,n9=ir.distribute,n4=tF.addEvent,n8=tF.clamp,n7=tF.css,rt=tF.discardElement,re=tF.extend,ri=tF.fireEvent,ro=tF.getAlignFactor,rn=tF.isArray,rr=tF.isNumber,rs=tF.isObject,ra=tF.isString,rh=tF.merge,rl=tF.pick,rd=tF.pushUnique,rc=tF.splat,rp=tF.syncTimeout,ru=function(){function t(t,e,i){this.allowShared=!0,this.crosshairs=[],this.distance=0,this.isHidden=!0,this.isSticky=!1,this.options={},this.outside=!1,this.chart=t,this.init(t,e),this.pointer=i}return t.prototype.bodyFormatter=function(t){return t.map(function(t){var e=t.series.tooltipOptions,i=t.formatPrefix||"point";return(e[i+"Formatter"]||t.tooltipFormatter).call(t,e[i+"Format"]||"")})},t.prototype.cleanSplit=function(t){this.chart.series.forEach(function(e){var i=null==e?void 0:e.tt;i&&(!i.isActive||t?e.tt=i.destroy():i.isActive=!1)})},t.prototype.defaultFormatter=function(t){var e,i=this.points||rc(this);return(e=(e=[t.headerFooterFormatter(i[0])]).concat(t.bodyFormatter(i))).push(t.headerFooterFormatter(i[0],!0)),e},t.prototype.destroy=function(){this.label&&(this.label=this.label.destroy()),this.split&&(this.cleanSplit(!0),this.tt&&(this.tt=this.tt.destroy())),this.renderer&&(this.renderer=this.renderer.destroy(),rt(this.container)),tF.clearTimeout(this.hideTimer)},t.prototype.getAnchor=function(t,e){var i,o,n=this.chart,r=this.pointer,s=n.inverted,a=n.plotTop,h=n.plotLeft;if((null===(i=(t=rc(t))[0].series)||void 0===i?void 0:i.yAxis)&&!t[0].series.yAxis.options.reversedStacks&&(t=t.slice().reverse()),this.followPointer&&e)void 0===e.chartX&&(e=r.normalize(e)),o=[e.chartX-h,e.chartY-a];else if(t[0].tooltipPos)o=t[0].tooltipPos;else{var l=0,d=0;t.forEach(function(t){var e=t.pos(!0);e&&(l+=e[0],d+=e[1])}),l/=t.length,d/=t.length,this.shared&&t.length>1&&e&&(s?l=e.chartX:d=e.chartY),o=[l-h,d-a]}return o.map(Math.round)},t.prototype.getClassName=function(t,e,i){var o=this.options,n=t.series,r=n.options;return[o.className,"highcharts-label",i&&"highcharts-tooltip-header",e?"highcharts-tooltip-box":"highcharts-tooltip",!i&&"highcharts-color-"+rl(t.colorIndex,n.colorIndex),null==r?void 0:r.className].filter(ra).join(" ")},t.prototype.getLabel=function(t){var e,i=void 0===t?{anchorX:0,anchorY:0}:t,o=i.anchorX,n=i.anchorY,r=this,s=this.chart.styledMode,a=this.options,h=this.split&&this.allowShared,l=this.container,d=this.chart.renderer;if(this.label){var c=!this.label.hasClass("highcharts-label");(!h&&c||h&&!c)&&this.destroy()}if(!this.label){if(this.outside){var p=this.chart,u=p.options.chart.style,f=e7.getRendererType();this.container=l=tv.doc.createElement("div"),l.className="highcharts-tooltip-container "+(p.renderTo.className.match(/(highcharts[a-zA-Z0-9-]+)\s?/gm)||""),n7(l,{position:"absolute",top:"1px",pointerEvents:"none",zIndex:Math.max(this.options.style.zIndex||0,((null==u?void 0:u.zIndex)||0)+3)}),this.renderer=d=new f(l,0,0,u,void 0,void 0,d.styledMode)}if(h?this.label=d.g("tooltip"):(this.label=d.label("",o,n,a.shape||"callout",void 0,void 0,a.useHTML,void 0,"tooltip").attr({padding:a.padding,r:a.borderRadius}),s||this.label.attr({fill:a.backgroundColor,"stroke-width":a.borderWidth||0}).css(a.style).css({pointerEvents:a.style.pointerEvents||(this.shouldStickOnContact()?"auto":"none")})),r.outside){var g=this.label;[g.xSetter,g.ySetter].forEach(function(t,e){g[e?"ySetter":"xSetter"]=function(i){t.call(g,r.distance),g[e?"y":"x"]=i,l&&(l.style[e?"top":"left"]=""+i+"px")}})}this.label.attr({zIndex:8}).shadow(null!==(e=a.shadow)&&void 0!==e?e:!a.fixed).add()}return l&&!l.parentElement&&tv.doc.body.appendChild(l),this.label},t.prototype.getPlayingField=function(){var t=n5.body,e=n5.documentElement,i=this.chart,o=this.distance,n=this.outside;return{width:n?Math.max(t.scrollWidth,e.scrollWidth,t.offsetWidth,e.offsetWidth,e.clientWidth)-2*o-2:i.chartWidth,height:n?Math.max(t.scrollHeight,e.scrollHeight,t.offsetHeight,e.offsetHeight,e.clientHeight):i.chartHeight}},t.prototype.getPosition=function(t,e,i){var o,n,r,s=this.distance,a=this.chart,h=this.outside,l=this.pointer,d=a.inverted,c=a.plotLeft,p=a.plotTop,u=a.polar,f=i.plotX,g=void 0===f?0:f,v=i.plotY,m=void 0===v?0:v,y={},x=d&&i.h||0,b=this.getPlayingField(),k=b.height,M=b.width,w=l.getChartPosition(),S=function(t){return t*w.scaleX},A=function(t){return t*w.scaleY},T=function(i){var o="x"===i;return[i,o?M:k,o?t:e].concat(h?[o?S(t):A(e),o?w.left-s+S(g+c):w.top-s+A(m+p),0,o?M:k]:[o?t:e,o?g+c:m+p,o?c:p,o?c+a.plotWidth:p+a.plotHeight])},C=T("y"),O=T("x"),P=!!i.negative;!u&&(null===(n=null===(o=a.hoverSeries)||void 0===o?void 0:o.yAxis)||void 0===n?void 0:n.reversed)&&(P=!P);var E=!this.followPointer&&rl(i.ttBelow,!u&&!d===P),L=function(t,e,i,o,n,r,a){var l=h?"y"===t?A(s):S(s):s,d=(i-o)/2,c=o<n-s,p=n+s+o<e,u=n-l-i+d,f=n+l-d;if(E&&p)y[t]=f;else if(!E&&c)y[t]=u;else if(c)y[t]=Math.min(a-o,u-x<0?u:u-x);else{if(!p)return y[t]=0,!1;y[t]=Math.max(r,f+x+i>e?f:f+x)}},B=function(t,e,i,o,n){if(n<s||n>e-s)return!1;n<i/2?y[t]=1:n>e-o/2?y[t]=e-o-2:y[t]=n-i/2},D=function(t){var e;C=(e=[O,C])[0],O=e[1],r=t},I=function(){!1!==L.apply(0,C)?!1!==B.apply(0,O)||r||(D(!0),I()):r?y.x=y.y=0:(D(!0),I())};return(d&&!u||this.len>1)&&D(),I(),y},t.prototype.getFixedPosition=function(t,e,i){var o,n=i.series,r=this.chart,s=this.options,a=this.split,h=s.position,l=h.relativeTo,d=s.shared||(null===(o=null==n?void 0:n.yAxis)||void 0===o?void 0:o.isRadial)&&("pane"===l||!l)?"plotBox":l,c="chart"===d?r.renderer:r[d]||r.getClipBox(n,!0);return{x:c.x+(c.width-t)*ro(h.align)+h.x,y:c.y+(c.height-e)*ro(h.verticalAlign)+(!a&&h.y||0)}},t.prototype.hide=function(t){var e=this;tF.clearTimeout(this.hideTimer),t=rl(t,this.options.hideDelay),this.isHidden||(this.hideTimer=rp(function(){var i=e.getLabel();e.getLabel().animate({opacity:0},{duration:t?150:t,complete:function(){i.hide(),e.container&&e.container.remove()}}),e.isHidden=!0},t))},t.prototype.init=function(t,e){this.chart=t,this.options=e,this.crosshairs=[],this.isHidden=!0,this.split=e.split&&!t.inverted&&!t.polar,this.shared=e.shared||this.split,this.outside=rl(e.outside,!!(t.scrollablePixelsX||t.scrollablePixelsY))},t.prototype.shouldStickOnContact=function(t){return!!(!this.followPointer&&this.options.stickOnContact&&(!t||this.pointer.inClass(t.target,"highcharts-tooltip")))},t.prototype.move=function(t,e,i,o){var n=this,r=this.followPointer,s=this.options,a=eE(!r&&!this.isHidden&&!s.fixed&&s.animation),h=r||(this.len||0)>1,l={x:t,y:e};h?l.anchorX=l.anchorY=NaN:(l.anchorX=i,l.anchorY=o),a.step=function(){return n.drawTracker()},this.getLabel().animate(l,a)},t.prototype.refresh=function(t,e){var i=this.chart,o=this.options,n=this.pointer,r=this.shared,s=rc(t),a=s[0],h=o.format,l=o.formatter||this.defaultFormatter,d=i.styledMode,c=this.allowShared;if(o.enabled&&a.series){tF.clearTimeout(this.hideTimer),this.allowShared=!(!rn(t)&&t.series&&t.series.noSharedTooltip),c=c&&!this.allowShared,this.followPointer=!this.split&&a.series.tooltipOptions.followPointer;var p=this.getAnchor(t,e),u=p[0],f=p[1];r&&this.allowShared&&(n.applyInactiveState(s),s.forEach(function(t){return t.setState("hover")}),a.points=s),this.len=s.length;var g=ra(h)?n1(h,a,i):l.call(a,this);a.points=void 0;var v=a.series;if(this.distance=rl(v.tooltipOptions.distance,16),!1===g)this.hide();else{if(this.split&&this.allowShared)this.renderSplit(g,s);else{var m=u,y=f;if(e&&n.isDirectTouch&&(m=e.chartX-i.plotLeft,y=e.chartY-i.plotTop),i.polar||!1===v.options.clip||s.some(function(t){return n.isDirectTouch||t.series.shouldShowTooltip(m,y)})){var x=this.getLabel(c&&this.tt||{});(!o.style.width||d)&&x.css({width:(this.outside?this.getPlayingField():i.spacingBox).width+"px"}),x.attr({class:this.getClassName(a),text:g&&g.join?g.join(""):g}),this.outside&&x.attr({x:n8(x.x||0,0,this.getPlayingField().width-(x.width||0)-1)}),d||x.attr({stroke:o.borderColor||a.color||v.color||"#666666"}),this.updatePosition({plotX:u,plotY:f,negative:a.negative,ttBelow:a.ttBelow,series:v,h:p[2]||0})}else{this.hide();return}}this.isHidden&&this.label&&this.label.attr({opacity:1}).show(),this.isHidden=!1}ri(this,"refresh")}},t.prototype.renderSplit=function(t,e){var i,o,n=this,r=this,s=r.chart,a=r.chart,h=a.chartWidth,l=a.chartHeight,d=a.plotHeight,c=a.plotLeft,p=a.plotTop,u=a.scrollablePixelsY,f=a.scrollablePixelsX,g=a.styledMode,v=r.distance,m=r.options,y=r.options,x=y.fixed,b=y.position,k=y.positioner,M=r.pointer,w=(null===(i=s.scrollablePlotArea)||void 0===i?void 0:i.scrollingContainer)||{},S=w.scrollLeft,A=void 0===S?0:S,T=w.scrollTop,C=void 0===T?0:T,O=r.outside&&"number"!=typeof f?n5.documentElement.getBoundingClientRect():{left:A,right:A+h,top:C,bottom:C+l},P=r.getLabel(),E=this.renderer||s.renderer,L=!!(null===(o=s.xAxis[0])||void 0===o?void 0:o.opposite),B=M.getChartPosition(),D=B.left,I=B.top,z=k||x,R=p+C,N=0,W=d-(void 0===u?0:u),G=function(t,e,i,o,n){if(void 0===o&&(o=[0,0]),void 0===n&&(n=!0),i.isHeader)a=L?0:W,s=n8(o[0]-t/2,O.left,O.right-t-(r.outside?D:0));else if(x&&i){var s,a,h=r.getFixedPosition(t,e,i);s=h.x,a=h.y-R}else a=o[1]-R,s=n8(s=n?o[0]-t-v:o[0]+v,n?s:O.left,O.right);return{x:s,y:a}};ra(t)&&(t=[!1,t]);var X=t.slice(0,e.length+1).reduce(function(t,i,o){if(!1!==i&&""!==i){var n=e[o-1]||{isHeader:!0,plotX:e[0].plotX,plotY:d,series:{}},s=n.isHeader,a=s?r:n.series,h=a.tt=function(t,e,i){var o,n=t,s=e.isHeader,a=e.series,h=a.tooltipOptions||m;if(!n){var l={padding:h.padding,r:h.borderRadius};g||(l.fill=h.backgroundColor,l["stroke-width"]=null!==(o=h.borderWidth)&&void 0!==o?o:x&&!s?0:1),n=E.label("",0,0,h[s?"headerShape":"shape"]||(x&&!s?"rect":"callout"),void 0,void 0,h.useHTML).addClass(r.getClassName(e,!0,s)).attr(l).add(P)}return n.isActive=!0,n.attr({text:i}),g||n.css(h.style).attr({stroke:h.borderColor||e.color||a.color||"#333333"}),n}(a.tt,n,i.toString()),l=h.getBBox(),u=l.width+h.strokeWidth();s&&(N=l.height,W+=N,L&&(R-=N));var f=function(t){var e,i,o=t.isHeader,n=t.plotX,r=void 0===n?0:n,s=t.plotY,a=void 0===s?0:s,h=t.series;if(o)e=Math.max(c+r,c),i=p+d/2;else{var l=h.xAxis,u=h.yAxis;e=l.pos+n8(r,-v,l.len+v),h.shouldShowTooltip(0,u.pos-p+a,{ignoreX:!0})&&(i=u.pos+a)}return{anchorX:e=n8(e,O.left-v,O.right+v),anchorY:i}}(n),y=f.anchorX,b=f.anchorY;if("number"==typeof b){var M=l.height+1,w=(k||G).call(r,u,M,n,[y,b]);t.push({align:z?0:void 0,anchorX:y,anchorY:b,boxWidth:u,point:n,rank:rl(w.rank,+!!s),size:M,target:w.y,tt:h,x:w.x})}else h.isActive=!1}return t},[]);!z&&X.some(function(t){var e=(r.outside?D:0)+t.anchorX;return e<O.left&&e+t.boxWidth<O.right||e<D-O.left+t.boxWidth&&O.right-e>e})&&(X=X.map(function(t){var e=G.call(n,t.boxWidth,t.size,t.point,[t.anchorX,t.anchorY],!1),i=e.x;return re(t,{target:e.y,x:i})})),r.cleanSplit(),n9(X,W);var H={left:D,right:D};X.forEach(function(t){var e=t.x,i=t.boxWidth,o=t.isHeader;!o&&(r.outside&&D+e<H.left&&(H.left=D+e),!o&&r.outside&&H.left+i>H.right&&(H.right=D+e))}),X.forEach(function(t){var e=t.x,i=t.anchorX,o=t.anchorY,n=t.pos,s=t.point.isHeader,a={visibility:void 0===n?"hidden":"inherit",x:e,y:(n||0)+R+(x&&b.y||0),anchorX:i,anchorY:o};if(r.outside&&e<i){var h=D-H.left;h>0&&(s||(a.x=e+h,a.anchorX=i+h),s&&(a.x=(H.right-H.left)/2,a.anchorX=i+h))}t.tt.attr(a)});var F=r.container,Y=r.outside,_=r.renderer;if(Y&&F&&_){var j=P.getBBox(),U=j.width,V=j.height,Z=j.x,q=j.y;_.setSize(U+Z,V+q,!1),F.style.left=H.left+"px",F.style.top=I+"px"}n6&&P.attr({opacity:1===P.opacity?.999:1})},t.prototype.drawTracker=function(){if(!this.shouldStickOnContact()){this.tracker&&(this.tracker=this.tracker.destroy());return}var t=this.chart,e=this.label,i=this.shared?t.hoverPoints:t.hoverPoint;if(e&&i){var o={x:0,y:0,width:0,height:0},n=this.getAnchor(i),r=e.getBBox();n[0]+=t.plotLeft-(e.translateX||0),n[1]+=t.plotTop-(e.translateY||0),o.x=Math.min(0,n[0]),o.y=Math.min(0,n[1]),o.width=n[0]<0?Math.max(Math.abs(n[0]),r.width-n[0]):Math.max(Math.abs(n[0]),r.width),o.height=n[1]<0?Math.max(Math.abs(n[1]),r.height-Math.abs(n[1])):Math.max(Math.abs(n[1]),r.height),this.tracker?this.tracker.attr(o):(this.tracker=e.renderer.rect(o).addClass("highcharts-tracker").add(e),t.styledMode||this.tracker.attr({fill:"rgba(0,0,0,0)"}))}},t.prototype.styledModeFormat=function(t){return t.replace('style="font-size: 0.8em"','class="highcharts-header"').replace(/style="color:{(point|series)\.color}"/g,'class="highcharts-color-{$1.colorIndex} {series.options.className} {point.options.className}"')},t.prototype.headerFooterFormatter=function(t,e){var i=t.series,o=i.tooltipOptions,n=i.xAxis,r=null==n?void 0:n.dateTime,s={isFooter:e,point:t},a=o.xDateFormat||"",h=o[e?"footerFormat":"headerFormat"];return ri(this,"headerFormatter",s,function(e){if(r&&!a&&rr(t.key)&&(a=r.getXDateFormat(t.key,o.dateTimeLabelFormats)),r&&a){if(rs(a)){var n=a;n3[0]=function(t){return i.chart.time.dateFormat(n,t)},a="%0"}(t.tooltipDateKeys||["key"]).forEach(function(t){h=h.replace(RegExp("point\\."+t+"([ \\)}])"),"(point.".concat(t,":").concat(a,")$1"))})}i.chart.styledMode&&(h=this.styledModeFormat(h)),e.text=n1(h,t,this.chart)}),s.text||""},t.prototype.update=function(t){this.destroy(),this.init(this.chart,rh(!0,this.options,t))},t.prototype.updatePosition=function(t){var e,i,o=this.chart,n=this.container,r=this.distance,s=this.options,a=this.pointer,h=this.renderer,l=this.getLabel(),d=l.height,c=void 0===d?0:d,p=l.width,u=void 0===p?0:p,f=s.fixed,g=s.positioner,v=a.getChartPosition(),m=v.left,y=v.top,x=v.scaleX,b=v.scaleY,k=(g||f&&this.getFixedPosition||this.getPosition).call(this,u,c,t),M=tv.doc,w=(t.plotX||0)+o.plotLeft,S=(t.plotY||0)+o.plotTop;if(h&&n){if(g||f){var A=(null===(e=o.scrollablePlotArea)||void 0===e?void 0:e.scrollingContainer)||{},T=A.scrollLeft,C=A.scrollTop;k.x+=(void 0===T?0:T)+m-r,k.y+=(void 0===C?0:C)+y-r}i=(s.borderWidth||0)+2*r+2,h.setSize(n8(u+i,0,M.documentElement.clientWidth)-1,c+i,!1),(1!==x||1!==b)&&(n7(n,{transform:"scale(".concat(x,", ").concat(b,")")}),w*=x,S*=b),w+=m-k.x,S+=y-k.y}this.move(Math.round(k.x),Math.round(k.y||0),w,S)},t}();(f=ru||(ru={})).compose=function(t){rd(n2,"Core.Tooltip")&&n4(t,"afterInit",function(){var t=this.chart;t.options.tooltip&&(t.tooltip=new f(t,t.options.tooltip,this))})};var rf=ru,rg=e8.format,rv=tF.addEvent,rm=tF.crisp,ry=tF.erase,rx=tF.extend,rb=tF.fireEvent,rk=tF.getNestedProperty,rM=tF.isArray,rw=tF.isFunction,rS=tF.isNumber,rA=tF.isObject,rT=tF.merge,rC=tF.pick,rO=tF.syncTimeout,rP=tF.removeEvent,rE=tF.uniqueKey,rL=function(){function t(t,e,i){var o,n;this.formatPrefix="point",this.visible=!0,this.point=this,this.series=t,this.applyOptions(e,i),null!==(o=this.id)&&void 0!==o||(this.id=rE()),this.resolveColor(),null!==(n=this.dataLabelOnNull)&&void 0!==n||(this.dataLabelOnNull=t.options.nullInteraction),t.chart.pointCount++,rb(this,"afterInit")}return t.prototype.animateBeforeDestroy=function(){var t=this,e={x:t.startXPos,opacity:0},i=t.getGraphicalProps();i.singular.forEach(function(i){t[i]=t[i].animate("dataLabel"===i?{x:t[i].startXPos,y:t[i].startYPos,opacity:0}:e)}),i.plural.forEach(function(e){t[e].forEach(function(e){e.element&&e.animate(rx({x:t.startXPos},e.startYPos?{x:e.startXPos,y:e.startYPos}:{}))})})},t.prototype.applyOptions=function(e,i){var o=this.series,n=o.options.pointValKey||o.pointValKey;return rx(this,e=t.prototype.optionsToObject.call(this,e)),this.options=this.options?rx(this.options,e):e,e.group&&delete this.group,e.dataLabels&&delete this.dataLabels,n&&(this.y=t.prototype.getNestedProperty.call(this,n)),this.selected&&(this.state="select"),"name"in this&&void 0===i&&o.xAxis&&o.xAxis.hasNames&&(this.x=o.xAxis.nameToX(this)),void 0===this.x&&o?this.x=null!=i?i:o.autoIncrement():rS(e.x)&&o.options.relativeXValue?this.x=o.autoIncrement(e.x):"string"==typeof this.x&&(null!=i||(i=o.chart.time.parse(this.x)),rS(i)&&(this.x=i)),this.isNull=this.isValid&&!this.isValid(),this.formatPrefix=this.isNull?"null":"point",this},t.prototype.destroy=function(){if(!this.destroyed){var t=this,e=t.series,i=e.chart,o=e.options.dataSorting,n=i.hoverPoints,r=eE(t.series.chart.renderer.globalAnimation),s=function(){for(var e in(t.graphic||t.graphics||t.dataLabel||t.dataLabels)&&(rP(t),t.destroyElements()),t)delete t[e]};t.legendItem&&i.legend.destroyItem(t),n&&(t.setState(),ry(n,t),n.length||(i.hoverPoints=null)),t===i.hoverPoint&&t.onMouseOut(),(null==o?void 0:o.enabled)?(this.animateBeforeDestroy(),rO(s,r.duration)):s(),i.pointCount--}this.destroyed=!0},t.prototype.destroyElements=function(t){var e=this,i=e.getGraphicalProps(t);i.singular.forEach(function(t){e[t]=e[t].destroy()}),i.plural.forEach(function(t){e[t].forEach(function(t){(null==t?void 0:t.element)&&t.destroy()}),delete e[t]})},t.prototype.firePointEvent=function(t,e,i){var o=this,n=this.series.options;o.manageEvent(t),"click"===t&&n.allowPointSelect&&(i=function(t){!o.destroyed&&o.select&&o.select(null,t.ctrlKey||t.metaKey||t.shiftKey)}),rb(o,t,e,i)},t.prototype.getClassName=function(){var t;return"highcharts-point"+(this.selected?" highcharts-point-select":"")+(this.negative?" highcharts-negative":"")+(this.isNull?" highcharts-null-point":"")+(void 0!==this.colorIndex?" highcharts-color-"+this.colorIndex:"")+(this.options.className?" "+this.options.className:"")+((null===(t=this.zone)||void 0===t?void 0:t.className)?" "+this.zone.className.replace("highcharts-negative",""):"")},t.prototype.getGraphicalProps=function(t){var e,i,o=this,n=[],r={singular:[],plural:[]};for((t=t||{graphic:1,dataLabel:1}).graphic&&n.push("graphic","connector"),t.dataLabel&&n.push("dataLabel","dataLabelPath","dataLabelUpper"),i=n.length;i--;)o[e=n[i]]&&r.singular.push(e);return["graphic","dataLabel"].forEach(function(e){var i=e+"s";t[e]&&o[i]&&r.plural.push(i)}),r},t.prototype.getNestedProperty=function(t){return t?0===t.indexOf("custom.")?rk(t,this.options):this[t]:void 0},t.prototype.getZone=function(){var t,e=this.series,i=e.zones,o=e.zoneAxis||"y",n=0;for(t=i[0];this[o]>=t.value;)t=i[++n];return this.nonZonedColor||(this.nonZonedColor=this.color),(null==t?void 0:t.color)&&!this.options.color?this.color=t.color:this.color=this.nonZonedColor,t},t.prototype.hasNewShapeType=function(){return(this.graphic&&(this.graphic.symbolName||this.graphic.element.nodeName))!==this.shapeType},t.prototype.isValid=function(){return(rS(this.x)||this.x instanceof Date)&&rS(this.y)},t.prototype.optionsToObject=function(e){var i,o,n=this.series,r=n.options.keys,s=r||n.pointArrayMap||["y"],a=s.length,h={},l=0,d=0;if(rS(e)||null===e)h[s[0]]=e;else if(rM(e))for(!r&&e.length>a&&("string"==(o=typeof e[0])?(null===(i=n.xAxis)||void 0===i?void 0:i.dateTime)?h.x=n.chart.time.parse(e[0]):h.name=e[0]:"number"===o&&(h.x=e[0]),l++);d<a;)r&&void 0===e[l]||(s[d].indexOf(".")>0?t.prototype.setNestedProperty(h,e[l],s[d]):h[s[d]]=e[l]),l++,d++;else"object"==typeof e&&(h=e,e.dataLabels&&(n.hasDataLabels=function(){return!0}),e.marker&&(n._hasPointMarkers=!0));return h},t.prototype.pos=function(t,e){if(void 0===e&&(e=this.plotY),!this.destroyed){var i=this.plotX,o=this.series,n=o.chart,r=o.xAxis,s=o.yAxis,a=0,h=0;if(rS(i)&&rS(e))return t&&(a=r?r.pos:n.plotLeft,h=s?s.pos:n.plotTop),n.inverted&&r&&s?[s.len-e+h,r.len-i+a]:[i+a,e+h]}},t.prototype.resolveColor=function(){var t,e,i,o=this.series,n=o.chart.options.chart,r=o.chart.styledMode,s=n.colorCount;delete this.nonZonedColor,o.options.colorByPoint?(r||(t=(e=o.options.colors||o.chart.options.colors)[o.colorCounter],s=e.length),i=o.colorCounter,o.colorCounter++,o.colorCounter===s&&(o.colorCounter=0)):(r||(t=o.color),i=o.colorIndex),this.colorIndex=rC(this.options.colorIndex,i),this.color=rC(this.options.color,t)},t.prototype.setNestedProperty=function(t,e,i){return i.split(".").reduce(function(t,i,o,n){var r=n.length-1===o;return t[i]=r?e:rA(t[i],!0)?t[i]:{},t[i]},t),t},t.prototype.shouldDraw=function(){return!this.isNull},t.prototype.tooltipFormatter=function(t){var e,i=this.series,o=i.chart,n=i.pointArrayMap,r=i.tooltipOptions,s=r.valueDecimals,a=void 0===s?"":s,h=r.valuePrefix,l=void 0===h?"":h,d=r.valueSuffix,c=void 0===d?"":d;return o.styledMode&&(t=(null===(e=o.tooltip)||void 0===e?void 0:e.styledModeFormat(t))||t),(void 0===n?["y"]:n).forEach(function(e){e="{point."+e,(l||c)&&(t=t.replace(RegExp(e+"}","g"),l+e+"}"+c)),t=t.replace(RegExp(e+"}","g"),e+":,."+a+"f}")}),rg(t,this,o)},t.prototype.update=function(t,e,i,o){var n,r=this,s=r.series,a=r.graphic,h=s.chart,l=s.options;function d(){r.applyOptions(t);var o=a&&r.hasMockGraphic,d=null===r.y?!o:o;a&&d&&(r.graphic=a.destroy(),delete r.hasMockGraphic),rA(t,!0)&&((null==a?void 0:a.element)&&t&&t.marker&&void 0!==t.marker.symbol&&(r.graphic=a.destroy()),(null==t?void 0:t.dataLabels)&&r.dataLabel&&(r.dataLabel=r.dataLabel.destroy())),n=r.index;for(var c={},p=0,u=s.dataColumnKeys();p<u.length;p++){var f=u[p];c[f]=r[f]}s.dataTable.setRow(c,n),l.data[n]=rA(l.data[n],!0)||rA(t,!0)?r.options:rC(t,l.data[n]),s.isDirty=s.isDirtyData=!0,!s.fixedBox&&s.hasCartesianSeries&&(h.isDirtyBox=!0),"point"===l.legendType&&(h.isDirtyLegend=!0),e&&h.redraw(i)}e=rC(e,!0),!1===o?d():r.firePointEvent("update",{options:t},d)},t.prototype.remove=function(t,e){this.series.removePoint(this.series.data.indexOf(this),t,e)},t.prototype.select=function(t,e){var i=this,o=i.series,n=o.chart;t=rC(t,!i.selected),this.selectedStaging=t,i.firePointEvent(t?"select":"unselect",{accumulate:e},function(){i.selected=i.options.selected=t,o.options.data[o.data.indexOf(i)]=i.options,i.setState(t&&"select"),e||n.getSelectedPoints().forEach(function(t){var e=t.series;t.selected&&t!==i&&(t.selected=t.options.selected=!1,e.options.data[e.data.indexOf(t)]=t.options,t.setState(n.hoverPoints&&e.options.inactiveOtherPoints?"inactive":""),t.firePointEvent("unselect"))})}),delete this.selectedStaging},t.prototype.onMouseOver=function(t){var e=this.series.chart,i=e.inverted,o=e.pointer;o&&(t=t?o.normalize(t):o.getChartCoordinatesFromPoint(this,i),o.runPointActions(t,this))},t.prototype.onMouseOut=function(){var t=this.series.chart;this.firePointEvent("mouseOut"),this.series.options.inactiveOtherPoints||(t.hoverPoints||[]).forEach(function(t){t.setState()}),t.hoverPoints=t.hoverPoint=null},t.prototype.manageEvent=function(t){var e,i,o,n,r,s,a,h=null===(e=rT(this.series.options.point,this.options).events)||void 0===e?void 0:e[t];!rw(h)||(null===(i=this.hcEvents)||void 0===i?void 0:i[t])&&(null===(n=null===(o=this.hcEvents)||void 0===o?void 0:o[t])||void 0===n?void 0:n.map(function(t){return t.fn}).indexOf(h))!==-1?this.importedUserEvent&&!h&&(null===(s=this.hcEvents)||void 0===s?void 0:s[t])&&(null===(a=this.hcEvents)||void 0===a?void 0:a[t].userEvent)&&(rP(this,t),delete this.hcEvents[t],Object.keys(this.hcEvents)||delete this.importedUserEvent):(null===(r=this.importedUserEvent)||void 0===r||r.call(this),this.importedUserEvent=rv(this,t,h),this.hcEvents&&(this.hcEvents[t].userEvent=!0))},t.prototype.setState=function(t,e){var i,o,n,r,s,a,h=this.series,l=this.state,d=h.options.states[t||"normal"]||{},c=en.plotOptions[h.type].marker&&h.options.marker,p=c&&!1===c.enabled,u=(null===(i=null==c?void 0:c.states)||void 0===i?void 0:i[t||"normal"])||{},f=!1===u.enabled,g=this.marker||{},v=h.chart,m=c&&h.markerAttribs,y=h.halo,x=h.stateMarkerGraphic;if(((t=t||"")!==this.state||e)&&(!this.selected||"select"===t)&&!1!==d.enabled&&(!t||!f&&(!p||!1!==u.enabled))&&(!t||!g.states||!g.states[t]||!1!==g.states[t].enabled)){if(this.state=t,m&&(n=h.markerAttribs(this,t)),this.graphic&&!this.hasMockGraphic){if(l&&this.graphic.removeClass("highcharts-point-"+l),t&&this.graphic.addClass("highcharts-point-"+t),!v.styledMode){r=h.pointAttribs(this,t),s=rC(v.options.chart.animation,d.animation);var b=r.opacity;h.options.inactiveOtherPoints&&rS(b)&&(this.dataLabels||[]).forEach(function(t){t&&!t.hasClass("highcharts-data-label-hidden")&&(t.animate({opacity:b},s),t.connector&&t.connector.animate({opacity:b},s))}),this.graphic.animate(r,s)}n&&this.graphic.animate(n,rC(v.options.chart.animation,u.animation,c.animation)),x&&x.hide()}else t&&u&&(a=g.symbol||h.symbol,x&&x.currentSymbol!==a&&(x=x.destroy()),n&&(x?x[e?"animate":"attr"]({x:n.x,y:n.y}):a&&(h.stateMarkerGraphic=x=v.renderer.symbol(a,n.x,n.y,n.width,n.height,rT(c,u)).add(h.markerGroup),x.currentSymbol=a)),!v.styledMode&&x&&"inactive"!==this.state&&x.attr(h.pointAttribs(this,t))),x&&(x[t&&this.isInside?"show":"hide"](),x.element.point=this,x.addClass(this.getClassName(),!0));var k=d.halo,M=this.graphic||x,w=(null==M?void 0:M.visibility)||"inherit";(null==k?void 0:k.size)&&M&&"hidden"!==w&&!this.isCluster?(y||(h.halo=y=v.renderer.path().add(M.parentGroup)),y.show()[e?"animate":"attr"]({d:this.haloPath(k.size)}),y.attr({class:"highcharts-halo highcharts-color-"+rC(this.colorIndex,h.colorIndex)+(this.className?" "+this.className:""),visibility:w,zIndex:-1}),y.point=this,v.styledMode||y.attr(rx({fill:this.color||h.color,"fill-opacity":k.opacity},eZ.filterUserAttributes(k.attributes||{})))):(null===(o=null==y?void 0:y.point)||void 0===o?void 0:o.haloPath)&&!y.point.destroyed&&y.animate({d:y.point.haloPath(0)},null,y.hide),rb(this,"afterSetState",{state:t})}},t.prototype.haloPath=function(t){var e=this.pos();return e?this.series.chart.renderer.symbols.circle(rm(e[0],1)-t,e[1]-t,2*t,2*t):[]},t}(),rB=function(){return(rB=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},rD=ev.parse,rI=tv.charts,rz=tv.composed,rR=tv.isTouchDevice,rN=tF.addEvent,rW=tF.attr,rG=tF.css,rX=tF.extend,rH=tF.find,rF=tF.fireEvent,rY=tF.isNumber,r_=tF.isObject,rj=tF.objectEach,rU=tF.offset,rV=tF.pick,rZ=tF.pushUnique,rq=tF.splat,rK=function(){function t(t,e){var i;this.hasDragged=0,this.pointerCaptureEventsToUnbind=[],this.eventsToUnbind=[],this.options=e,this.chart=t,this.runChartClick=!!(null===(i=e.chart.events)||void 0===i?void 0:i.click),this.pinchDown=[],this.setDOMEvents(),rF(this,"afterInit")}return t.prototype.applyInactiveState=function(t){var e=this;void 0===t&&(t=[]);var i=[];t.forEach(function(t){var o=t.series;i.push(o),o.linkedParent&&i.push(o.linkedParent),o.linkedSeries&&i.push.apply(i,o.linkedSeries),o.navigatorSeries&&i.push(o.navigatorSeries),o.boosted&&o.markerGroup&&i.push.apply(i,e.chart.series.filter(function(t){return t.markerGroup===o.markerGroup}))}),this.chart.series.forEach(function(t){-1===i.indexOf(t)?t.setState("inactive",!0):t.options.inactiveOtherPoints&&t.setAllPointsToState("inactive")})},t.prototype.destroy=function(){var e=this;this.eventsToUnbind.forEach(function(t){return t()}),this.eventsToUnbind=[],!tv.chartCount&&(t.unbindDocumentMouseUp.forEach(function(t){return t.unbind()}),t.unbindDocumentMouseUp.length=0,t.unbindDocumentTouchEnd&&(t.unbindDocumentTouchEnd=t.unbindDocumentTouchEnd())),clearInterval(e.tooltipTimeout),rj(e,function(t,i){e[i]=void 0})},t.prototype.getSelectionMarkerAttrs=function(t,e){var i=this,o={args:{chartX:t,chartY:e},attrs:{},shapeType:"rect"};return rF(this,"getSelectionMarkerAttrs",o,function(o){var n,r=i.chart,s=i.zoomHor,a=i.zoomVert,h=r.mouseDownX,l=void 0===h?0:h,d=r.mouseDownY,c=void 0===d?0:d,p=o.attrs;p.x=r.plotLeft,p.y=r.plotTop,p.width=s?1:r.plotWidth,p.height=a?1:r.plotHeight,s&&(p.width=Math.max(1,Math.abs(n=t-l)),p.x=(n>0?0:n)+l),a&&(p.height=Math.max(1,Math.abs(n=e-c)),p.y=(n>0?0:n)+c)}),o},t.prototype.drag=function(t){var e,i=this.chart,o=i.mouseDownX,n=void 0===o?0:o,r=i.mouseDownY,s=void 0===r?0:r,a=i.options.chart,h=a.panning,l=a.panKey,d=a.selectionMarkerFill,c=i.plotLeft,p=i.plotTop,u=i.plotWidth,f=i.plotHeight,g=r_(h)?h.enabled:h,v=l&&t[""+l+"Key"],m=t.chartX,y=t.chartY,x=this.selectionMarker;if((!x||!x.touch)&&(m<c?m=c:m>c+u&&(m=c+u),y<p?y=p:y>p+f&&(y=p+f),this.hasDragged=Math.sqrt(Math.pow(n-m,2)+Math.pow(s-y,2)),this.hasDragged>10)){e=i.isInsidePlot(n-c,s-p,{visiblePlotOnly:!0});var b=this.getSelectionMarkerAttrs(m,y),k=b.shapeType,M=b.attrs;(i.hasCartesianSeries||i.mapView)&&this.hasZoom&&e&&!v&&!x&&(this.selectionMarker=x=i.renderer[k](),x.attr({class:"highcharts-selection-marker",zIndex:7}).add(),i.styledMode||x.attr({fill:d||rD("#334eff").setOpacity(.25).get()})),x&&x.attr(M),e&&!x&&g&&i.pan(t,h)}},t.prototype.dragStart=function(t){var e=this.chart;e.mouseIsDown=t.type,e.cancelClick=!1,e.mouseDownX=t.chartX,e.mouseDownY=t.chartY},t.prototype.getSelectionBox=function(t){var e={args:{marker:t},result:t.getBBox()};return rF(this,"getSelectionBox",e),e.result},t.prototype.drop=function(t){for(var e,i=this,o=this.chart,n=this.selectionMarker,r=0,s=o.axes;r<s.length;r++){var a=s[r];a.isPanning&&(a.isPanning=!1,(a.options.startOnTick||a.options.endOnTick||a.series.some(function(t){return t.boosted}))&&(a.forceRedraw=!0,a.setExtremes(a.userMin,a.userMax,!1),e=!0))}if(e&&o.redraw(),n&&t){if(this.hasDragged){var h=this.getSelectionBox(n);o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&("xAxis"===t.coll&&i.zoomX||"yAxis"===t.coll&&i.zoomY)}),selection:rB({originalEvent:t,xAxis:[],yAxis:[]},h),from:h})}rY(o.index)&&(this.selectionMarker=n.destroy())}o&&rY(o.index)&&(rG(o.container,{cursor:o._cursor}),o.cancelClick=this.hasDragged>10,o.mouseIsDown=!1,this.hasDragged=0,this.pinchDown=[])},t.prototype.findNearestKDPoint=function(t,e,i){var o;return t.forEach(function(t){var n,r,s,a,h,l,d,c=!(t.noSharedTooltip&&e)&&0>t.options.findNearestPointBy.indexOf("y"),p=t.searchPoint(i,c);r_(p,!0)&&p.series&&(!r_(o,!0)||(h=(n=o).distX-p.distX,l=n.dist-p.dist,d=(null===(r=p.series.group)||void 0===r?void 0:r.zIndex)-(null===(s=n.series.group)||void 0===s?void 0:s.zIndex),(0!==h&&e?h:0!==l?l:0!==d?d:n.series.index>p.series.index?-1:1)>0))&&(o=p)}),o},t.prototype.getChartCoordinatesFromPoint=function(t,e){var i,o,n=t.series,r=n.xAxis,s=n.yAxis,a=t.shapeArgs;if(r&&s){var h=null!==(o=null!==(i=t.clientX)&&void 0!==i?i:t.plotX)&&void 0!==o?o:0,l=t.plotY||0;return t.isNode&&a&&rY(a.x)&&rY(a.y)&&(h=a.x,l=a.y),e?{chartX:s.len+s.pos-l,chartY:r.len+r.pos-h}:{chartX:h+r.pos,chartY:l+s.pos}}if((null==a?void 0:a.x)&&a.y)return{chartX:a.x,chartY:a.y}},t.prototype.getChartPosition=function(){if(this.chartPosition)return this.chartPosition;var t=this.chart.container,e=rU(t);this.chartPosition={left:e.left,top:e.top,scaleX:1,scaleY:1};var i=t.offsetHeight,o=t.offsetWidth;return o>2&&i>2&&(this.chartPosition.scaleX=e.width/o,this.chartPosition.scaleY=e.height/i),this.chartPosition},t.prototype.getCoordinates=function(t){for(var e={xAxis:[],yAxis:[]},i=0,o=this.chart.axes;i<o.length;i++){var n=o[i];e[n.isXAxis?"xAxis":"yAxis"].push({axis:n,value:n.toValue(t[n.horiz?"chartX":"chartY"])})}return e},t.prototype.getHoverData=function(t,e,i,o,n,r){var s,a=[],h=function(t){return t.visible&&!(!n&&t.directTouch)&&rV(t.options.enableMouseTracking,!0)},l=e,d={chartX:r?r.chartX:void 0,chartY:r?r.chartY:void 0,shared:n};rF(this,"beforeGetHoverData",d),s=l&&!l.stickyTracking?[l]:i.filter(function(t){return t.stickyTracking&&(d.filter||h)(t)});var c=o&&t||!r?t:this.findNearestKDPoint(s,n,r);return l=null==c?void 0:c.series,c&&(n&&!l.noSharedTooltip?(s=i.filter(function(t){return d.filter?d.filter(t):h(t)&&!t.noSharedTooltip})).forEach(function(t){var e,i=null===(e=t.options)||void 0===e?void 0:e.nullInteraction,o=rH(t.points,function(t){return t.x===c.x&&(!t.isNull||!!i)});r_(o)&&(t.boosted&&t.boost&&(o=t.boost.getPoint(o)),a.push(o))}):a.push(c)),rF(this,"afterGetHoverData",d={hoverPoint:c}),{hoverPoint:d.hoverPoint,hoverSeries:l,hoverPoints:a}},t.prototype.getPointFromEvent=function(t){for(var e,i=t.target;i&&!e;)e=i.point,i=i.parentNode;return e},t.prototype.onTrackerMouseOut=function(t){var e=this.chart,i=t.relatedTarget,o=e.hoverSeries;this.isDirectTouch=!1,!o||!i||o.stickyTracking||this.inClass(i,"highcharts-tooltip")||this.inClass(i,"highcharts-series-"+o.index)&&this.inClass(i,"highcharts-tracker")||o.onMouseOut()},t.prototype.inClass=function(t,e){for(var i,o=t;o;){if(i=rW(o,"class")){if(-1!==i.indexOf(e))return!0;if(-1!==i.indexOf("highcharts-container"))return!1}o=o.parentElement}},t.prototype.normalize=function(t,e){var i=t.touches,o=i?i.length?i.item(0):rV(i.changedTouches,t.changedTouches)[0]:t;e||(e=this.getChartPosition());var n=o.pageX-e.left,r=o.pageY-e.top;return rX(t,{chartX:Math.round(n/=e.scaleX),chartY:Math.round(r/=e.scaleY)})},t.prototype.onContainerClick=function(t){var e=this.chart,i=e.hoverPoint,o=this.normalize(t),n=e.plotLeft,r=e.plotTop;!e.cancelClick&&(i&&this.inClass(o.target,"highcharts-tracker")?(rF(i.series,"click",rX(o,{point:i})),e.hoverPoint&&i.firePointEvent("click",o)):(rX(o,this.getCoordinates(o)),e.isInsidePlot(o.chartX-n,o.chartY-r,{visiblePlotOnly:!0})&&rF(e,"click",o)))},t.prototype.onContainerMouseDown=function(t){var e,i=(1&(t.buttons||t.button))==1;t=this.normalize(t),tv.isFirefox&&0!==t.button&&this.onContainerMouseMove(t),(void 0===t.button||i)&&(this.zoomOption(t),i&&(null===(e=t.preventDefault)||void 0===e||e.call(t)),this.dragStart(t))},t.prototype.onContainerMouseLeave=function(e){var i=(rI[rV(t.hoverChartIndex,-1)]||{}).pointer;e=this.normalize(e),this.onContainerMouseMove(e),i&&!this.inClass(e.relatedTarget,"highcharts-tooltip")&&(i.reset(),i.chartPosition=void 0)},t.prototype.onContainerMouseEnter=function(){delete this.chartPosition},t.prototype.onContainerMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.normalize(t);this.setHoverChartIndex(t),("mousedown"===e.mouseIsDown||this.touchSelect(o))&&this.drag(o),!e.openMenu&&(this.inClass(o.target,"highcharts-tracker")||e.isInsidePlot(o.chartX-e.plotLeft,o.chartY-e.plotTop,{visiblePlotOnly:!0}))&&!(null==i?void 0:i.shouldStickOnContact(o))&&(this.inClass(o.target,"highcharts-no-tooltip")?this.reset(!1,0):this.runPointActions(o))},t.prototype.onDocumentTouchEnd=function(t){this.onDocumentMouseUp(t)},t.prototype.onContainerTouchMove=function(t){this.touchSelect(t)?this.onContainerMouseMove(t):this.touch(t)},t.prototype.onContainerTouchStart=function(t){this.touchSelect(t)?this.onContainerMouseDown(t):(this.zoomOption(t),this.touch(t,!0))},t.prototype.onDocumentMouseMove=function(t){var e=this.chart,i=e.tooltip,o=this.chartPosition,n=this.normalize(t,o);!o||e.isInsidePlot(n.chartX-e.plotLeft,n.chartY-e.plotTop,{visiblePlotOnly:!0})||(null==i?void 0:i.shouldStickOnContact(n))||n.target!==e.container.ownerDocument&&this.inClass(n.target,"highcharts-tracker")||this.reset()},t.prototype.onDocumentMouseUp=function(e){var i,o;null===(o=null===(i=rI[rV(t.hoverChartIndex,-1)])||void 0===i?void 0:i.pointer)||void 0===o||o.drop(e)},t.prototype.pinch=function(t){var e=this,i=this,o=i.chart,n=i.hasZoom,r=i.lastTouches,s=[].map.call(t.touches||[],function(t){return i.normalize(t)}),a=s.length,h=1===a&&(i.inClass(t.target,"highcharts-tracker")&&o.runTrackerClick||i.runChartClick),l=o.tooltip,d=1===a&&rV(null==l?void 0:l.options.followTouchMove,!0);a>1?i.initiated=!0:d&&(i.initiated=!1),n&&i.initiated&&!h&&!1!==t.cancelable&&t.preventDefault(),"touchstart"===t.type?(i.pinchDown=s,i.res=!0,o.mouseDownX=t.chartX):d?this.runPointActions(i.normalize(t)):r&&(rF(o,"touchpan",{originalEvent:t,touches:s},function(){var i=function(t){var e=t[0],i=t[1]||e;return{x:e.chartX,y:e.chartY,width:i.chartX-e.chartX,height:i.chartY-e.chartY}};o.transform({axes:o.axes.filter(function(t){return t.zoomEnabled&&(e.zoomHor&&t.horiz||e.zoomVert&&!t.horiz)}),to:i(s),from:i(r),trigger:t.type})}),i.res&&(i.res=!1,this.reset(!1,0))),i.lastTouches=s},t.prototype.reset=function(t,e){var i=this.chart,o=i.hoverSeries,n=i.hoverPoint,r=i.hoverPoints,s=i.tooltip,a=(null==s?void 0:s.shared)?r:n;t&&a&&rq(a).forEach(function(e){e.series.isCartesian&&void 0===e.plotX&&(t=!1)}),t?s&&a&&rq(a).length&&(s.refresh(a),s.shared&&r?r.forEach(function(t){t.setState(t.state,!0),t.series.isCartesian&&(t.series.xAxis.crosshair&&t.series.xAxis.drawCrosshair(null,t),t.series.yAxis.crosshair&&t.series.yAxis.drawCrosshair(null,t))}):n&&(n.setState(n.state,!0),i.axes.forEach(function(t){t.crosshair&&n.series[t.coll]===t&&t.drawCrosshair(null,n)}))):(n&&n.onMouseOut(),r&&r.forEach(function(t){t.setState()}),o&&o.onMouseOut(),s&&s.hide(e),this.unDocMouseMove&&(this.unDocMouseMove=this.unDocMouseMove()),i.axes.forEach(function(t){t.hideCrosshair()}),i.hoverPoints=i.hoverPoint=void 0)},t.prototype.runPointActions=function(e,i,o){var n,r=this.chart,s=r.series,a=(null===(n=r.tooltip)||void 0===n?void 0:n.options.enabled)?r.tooltip:void 0,h=!!a&&a.shared,l=i||r.hoverPoint,d=(null==l?void 0:l.series)||r.hoverSeries,c=(!e||"touchmove"!==e.type)&&(!!i||(null==d?void 0:d.directTouch)&&this.isDirectTouch),p=this.getHoverData(l,d,s,c,h,e);l=p.hoverPoint,d=p.hoverSeries;var u=p.hoverPoints,f=(null==d?void 0:d.tooltipOptions.followPointer)&&!d.tooltipOptions.split,g=h&&d&&!d.noSharedTooltip;if(l&&(o||l!==r.hoverPoint||(null==a?void 0:a.isHidden))){if((r.hoverPoints||[]).forEach(function(t){-1===u.indexOf(t)&&t.setState()}),r.hoverSeries!==d&&d.onMouseOver(),this.applyInactiveState(u),(u||[]).forEach(function(t){t.setState("hover")}),r.hoverPoint&&r.hoverPoint.firePointEvent("mouseOut"),!l.series)return;r.hoverPoints=u,r.hoverPoint=l,l.firePointEvent("mouseOver",void 0,function(){a&&l&&a.refresh(g?u:l,e)})}else if(f&&a&&!a.isHidden){var v=a.getAnchor([{}],e);r.isInsidePlot(v[0],v[1],{visiblePlotOnly:!0})&&a.updatePosition({plotX:v[0],plotY:v[1]})}this.unDocMouseMove||(this.unDocMouseMove=rN(r.container.ownerDocument,"mousemove",function(e){var i,o,n;return null===(n=null===(o=rI[null!==(i=t.hoverChartIndex)&&void 0!==i?i:-1])||void 0===o?void 0:o.pointer)||void 0===n?void 0:n.onDocumentMouseMove(e)}),this.eventsToUnbind.push(this.unDocMouseMove)),r.axes.forEach(function(t){var i,o,n,s=null===(o=null===(i=t.crosshair)||void 0===i?void 0:i.snap)||void 0===o||o;!s||(n=r.hoverPoint)&&n.series[t.coll]===t||(n=rH(u,function(e){var i;return(null===(i=e.series)||void 0===i?void 0:i[t.coll])===t})),n||!s?t.drawCrosshair(e,n):t.hideCrosshair()})},t.prototype.setDOMEvents=function(){var e=this,i=this.chart.container,o=i.ownerDocument;i.onmousedown=this.onContainerMouseDown.bind(this),i.onmousemove=this.onContainerMouseMove.bind(this),i.onclick=this.onContainerClick.bind(this),this.eventsToUnbind.push(rN(i,"mouseenter",this.onContainerMouseEnter.bind(this)),rN(i,"mouseleave",this.onContainerMouseLeave.bind(this))),t.unbindDocumentMouseUp.some(function(t){return t.doc===o})||t.unbindDocumentMouseUp.push({doc:o,unbind:rN(o,"mouseup",this.onDocumentMouseUp.bind(this))});for(var n=this.chart.renderTo.parentElement;n&&"BODY"!==n.tagName;)this.eventsToUnbind.push(rN(n,"scroll",function(){delete e.chartPosition})),n=n.parentElement;this.eventsToUnbind.push(rN(i,"touchstart",this.onContainerTouchStart.bind(this),{passive:!1}),rN(i,"touchmove",this.onContainerTouchMove.bind(this),{passive:!1})),t.unbindDocumentTouchEnd||(t.unbindDocumentTouchEnd=rN(o,"touchend",this.onDocumentTouchEnd.bind(this),{passive:!1})),this.setPointerCapture(),rN(this.chart,"redraw",this.setPointerCapture.bind(this))},t.prototype.setPointerCapture=function(){if(rR){var t,e,i=this.pointerCaptureEventsToUnbind,o=this.chart,n=o.container,r=rV(null===(t=o.options.tooltip)||void 0===t?void 0:t.followTouchMove,!0)&&o.series.some(function(t){return t.options.findNearestPointBy.indexOf("y")>-1});!this.hasPointerCapture&&r?(i.push(rN(n,"pointerdown",function(t){var e,i;(null===(e=t.target)||void 0===e?void 0:e.hasPointerCapture(t.pointerId))&&(null===(i=t.target)||void 0===i||i.releasePointerCapture(t.pointerId))}),rN(n,"pointermove",function(t){var e,i;null===(i=null===(e=o.pointer)||void 0===e?void 0:e.getPointFromEvent(t))||void 0===i||i.onMouseOver(t)})),o.styledMode||rG(n,{"touch-action":"none"}),n.className+=" highcharts-no-touch-action",this.hasPointerCapture=!0):this.hasPointerCapture&&!r&&(i.forEach(function(t){return t()}),i.length=0,o.styledMode||rG(n,{"touch-action":rV(null===(e=o.options.chart.style)||void 0===e?void 0:e["touch-action"],"manipulation")}),n.className=n.className.replace(" highcharts-no-touch-action",""),this.hasPointerCapture=!1)}},t.prototype.setHoverChartIndex=function(e){var i,o=this.chart,n=tv.charts[rV(t.hoverChartIndex,-1)];if(n&&n!==o){var r={relatedTarget:o.container};!e||(null==e?void 0:e.relatedTarget)||Object.assign({},e,r),null===(i=n.pointer)||void 0===i||i.onContainerMouseLeave(e||r)}(null==n?void 0:n.mouseIsDown)||(t.hoverChartIndex=o.index)},t.prototype.touch=function(t,e){var i,o=this.chart,n=this.pinchDown,r=void 0===n?[]:n;this.setHoverChartIndex(),1===(t=this.normalize(t)).touches.length?o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})&&!o.openMenu?(e&&this.runPointActions(t),"touchmove"===t.type&&(i=!!r[0]&&Math.pow(r[0].chartX-t.chartX,2)+Math.pow(r[0].chartY-t.chartY,2)>=16),rV(i,!0)&&this.pinch(t)):e&&this.reset():2===t.touches.length&&this.pinch(t)},t.prototype.touchSelect=function(t){return!!(this.chart.zooming.singleTouch&&t.touches&&1===t.touches.length)},t.prototype.zoomOption=function(t){var e,i,o=this.chart,n=o.inverted,r=o.zooming.type||"";/touch/.test(t.type)&&(r=rV(o.zooming.pinchType,r)),this.zoomX=e=/x/.test(r),this.zoomY=i=/y/.test(r),this.zoomHor=e&&!n||i&&n,this.zoomVert=i&&!n||e&&n,this.hasZoom=e||i},t.unbindDocumentMouseUp=[],t}();(g=rK||(rK={})).compose=function(t){rZ(rz,"Core.Pointer")&&rN(t,"beforeRender",function(){this.pointer=new g(this,this.options)})};var r$=rK,rJ=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))};(v=Z||(Z={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},v.splice=function(t,e,i,o,n){if(void 0===n&&(n=[]),Array.isArray(t))return Array.isArray(n)||(n=Array.from(n)),{removed:t.splice.apply(t,rJ([e,i],n,!1)),array:t};var r=Object.getPrototypeOf(t).constructor,s=t[o?"subarray":"slice"](e,e+i),a=new r(t.length-i+n.length);return a.set(t.subarray(0,e),0),a.set(n,e),a.set(t.subarray(e+i),e+n.length),{removed:s,array:a}};var rQ=Z,r0=rQ.setLength,r1=rQ.splice,r2=tF.fireEvent,r3=tF.objectEach,r5=tF.uniqueKey,r6=function(){function t(t){void 0===t&&(t={});var e=this;this.autoId=!t.id,this.columns={},this.id=t.id||r5(),this.modified=this,this.rowCount=0,this.versionTag=r5();var i=0;r3(t.columns||{},function(t,o){e.columns[o]=t.slice(),i=Math.max(i,t.length)}),this.applyRowCount(i)}return t.prototype.applyRowCount=function(t){var e=this;this.rowCount=t,r3(this.columns,function(i,o){i.length!==t&&(e.columns[o]=r0(i,t))})},t.prototype.deleteRows=function(t,e){var i=this;if(void 0===e&&(e=1),e>0&&t<this.rowCount){var o=0;r3(this.columns,function(n,r){i.columns[r]=r1(n,t,e).array,o=n.length}),this.rowCount=o}r2(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=r5()},t.prototype.getColumn=function(t,e){return this.columns[t]},t.prototype.getColumns=function(t,e){var i=this;return(t||Object.keys(this.columns)).reduce(function(t,e){return t[e]=i.columns[e],t},{})},t.prototype.getRow=function(t,e){var i=this;return(e||Object.keys(this.columns)).map(function(e){var o;return null===(o=i.columns[e])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,e,i,o){var n;void 0===e&&(e=[]),void 0===i&&(i=0),this.setColumns(((n={})[t]=e,n),i,o)},t.prototype.setColumns=function(t,e,i){var o=this,n=this.rowCount;r3(t,function(t,e){o.columns[e]=t.slice(),n=t.length}),this.applyRowCount(n),(null==i?void 0:i.silent)||(r2(this,"afterSetColumns"),this.versionTag=r5())},t.prototype.setRow=function(t,e,i,o){void 0===e&&(e=this.rowCount);var n=this.columns,r=i?this.rowCount+1:e+1;r3(t,function(t,s){var a=n[s]||(null==o?void 0:o.addColumns)!==!1&&Array(r);a&&(i?a=r1(a,e,0,!0,[t]).array:a[e]=t,n[s]=a)}),r>this.rowCount&&this.applyRowCount(r),(null==o?void 0:o.silent)||(r2(this,"afterSetRows"),this.versionTag=r5())},t}(),r9=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},r4=tF.extend,r8=tF.merge,r7=tF.pick;!function(t){function e(t,e,i){var o,n,r,s=this.legendItem=this.legendItem||{},a=this.chart,h=this.options,l=t.baseline,d=void 0===l?0:l,c=t.symbolWidth,p=t.symbolHeight,u=this.symbol||"circle",f=p/2,g=a.renderer,v=s.group,m=d-Math.round(((null===(o=t.fontMetrics)||void 0===o?void 0:o.b)||p)*(i?.4:.3)),y={},x=h.marker,b=0;if(a.styledMode||(y["stroke-width"]=Math.min(h.lineWidth||0,24),h.dashStyle?y.dashstyle=h.dashStyle:"square"===h.linecap||(y["stroke-linecap"]="round")),s.line=g.path().addClass("highcharts-graph").attr(y).add(v),i&&(s.area=g.path().addClass("highcharts-area").add(v)),y["stroke-linecap"]&&(b=Math.min(s.line.strokeWidth(),c)/2),c){var k=[["M",b,m],["L",c-b,m]];s.line.attr({d:k}),null===(n=s.area)||void 0===n||n.attr({d:r9(r9([],k,!0),[["L",c-b,d],["L",b,d]],!1)})}if(x&&!1!==x.enabled&&c){var M=Math.min(r7(x.radius,f),f);0===u.indexOf("url")&&(x=r8(x,{width:p,height:p}),M=0),s.symbol=r=g.symbol(u,c/2-M,m-M,2*M,2*M,r4({context:"legend"},x)).addClass("highcharts-point").add(v),r.isMarker=!0}}t.areaMarker=function(t,i){e.call(this,t,i,!0)},t.lineMarker=e,t.rectangle=function(t,e){var i=e.legendItem||{},o=t.options,n=t.symbolHeight,r=o.squareSymbol,s=r?n:t.symbolWidth;i.symbol=this.chart.renderer.rect(r?(t.symbolWidth-n)/2:0,t.baseline-n+1,s,n,r7(t.options.symbolRadius,n/2)).addClass("highcharts-point").attr({zIndex:3}).add(i.group)}}(q||(q={}));var st=q,se={lineWidth:2,allowPointSelect:!1,crisp:!0,showCheckbox:!1,animation:{duration:1e3},enableMouseTracking:!0,events:{},marker:{enabledThreshold:2,lineColor:"#ffffff",lineWidth:0,radius:4,states:{normal:{animation:!0},hover:{animation:{duration:150},enabled:!0,radiusPlus:2,lineWidthPlus:1},select:{fillColor:"#cccccc",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:{animation:{},align:"center",borderWidth:0,defer:!0,formatter:function(){var t=this.series.chart.numberFormatter;return"number"!=typeof this.y?"":t(this.y,-1)},padding:5,style:{fontSize:"0.7em",fontWeight:"bold",color:"contrast",textOutline:"1px contrast"},verticalAlign:"bottom",x:0,y:0},cropThreshold:300,opacity:1,pointRange:0,softThreshold:!0,states:{normal:{animation:!0},hover:{animation:{duration:150},lineWidthPlus:1,marker:{},halo:{size:10,opacity:.25}},select:{animation:{duration:0}},inactive:{animation:{duration:150},opacity:.2}},stickyTracking:!0,turboThreshold:1e3,findNearestPointBy:"x"},si=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),so=tF.extend,sn=tF.extendClass,sr=tF.merge;!function(t){function e(e,i){var o=en.plotOptions||{},n=i.defaultOptions,r=i.prototype;return r.type=e,r.pointClass||(r.pointClass=rL),!t.seriesTypes[e]&&(n&&(o[e]=n),t.seriesTypes[e]=i,!0)}t.seriesTypes=tv.seriesTypes,t.registerSeriesType=e,t.seriesType=function(i,o,n,r,s){var a=en.plotOptions||{};if(o=o||"",a[i]=sr(a[o],n),delete t.seriesTypes[i],e(i,sn(t.seriesTypes[o]||function(){},r)),t.seriesTypes[i].prototype.type=i,s){var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return si(e,t),e}(rL);so(h.prototype,s),t.seriesTypes[i].prototype.pointClass=h}return t.seriesTypes[i]}}(K||(K={}));var ss=K,sa=function(){return(sa=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},sh=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},sl=o$.registerEventOptions,sd=tv.svg,sc=tv.win,sp=ss.seriesTypes,su=e8.format,sf=tF.arrayMax,sg=tF.arrayMin,sv=tF.clamp,sm=tF.correctFloat,sy=tF.crisp,sx=tF.defined,sb=tF.destroyObjectProperties,sk=tF.diffObjects,sM=tF.erase,sw=tF.error,sS=tF.extend,sA=tF.find,sT=tF.fireEvent,sC=tF.getClosestDistance,sO=tF.getNestedProperty,sP=tF.insertItem,sE=tF.isArray,sL=tF.isNumber,sB=tF.isString,sD=tF.merge,sI=tF.objectEach,sz=tF.pick,sR=tF.removeEvent,sN=tF.syncTimeout,sW=function(){function t(){this.zoneAxis="y"}return t.prototype.init=function(t,e){sT(this,"init",{options:e}),null!==(i=this.dataTable)&&void 0!==i||(this.dataTable=new r6);var i,o,n,r,s,a=t.series;this.eventsToUnbind=[],this.chart=t,this.options=this.setOptions(e);var h=this.options,l=!1!==h.visible;this.linkedSeries=[],this.bindAxes(),sS(this,{name:h.name,state:"",visible:l,selected:!0===h.selected}),sl(this,h);var d=h.events;((null==d?void 0:d.click)||(null===(n=null===(o=h.point)||void 0===o?void 0:o.events)||void 0===n?void 0:n.click)||h.allowPointSelect)&&(t.runTrackerClick=!0),this.getColor(),this.getSymbol(),this.isCartesian&&(t.hasCartesianSeries=!0),a.length&&(s=a[a.length-1]),this._i=sz(null==s?void 0:s._i,-1)+1,this.opacity=this.options.opacity,t.orderItems("series",sP(this,a)),(null===(r=h.dataSorting)||void 0===r?void 0:r.enabled)?this.setDataSortingOptions():this.points||this.data||this.setData(h.data,!1),sT(this,"afterInit")},t.prototype.is=function(t){return sp[t]&&this instanceof sp[t]},t.prototype.bindAxes=function(){var t,e=this,i=e.options,o=e.chart;sT(this,"bindAxes",null,function(){(e.axisTypes||[]).forEach(function(n){(o[n]||[]).forEach(function(o){t=o.options,(sz(i[n],0)===o.index||void 0!==i[n]&&i[n]===t.id)&&(sP(e,o.series),e[n]=o,o.isDirty=!0)}),e[n]||e.optionalAxis===n||sw(18,!0,o)})}),sT(this,"afterBindAxes")},t.prototype.hasData=function(){return this.visible&&void 0!==this.dataMax&&void 0!==this.dataMin||this.visible&&this.dataTable.rowCount>0},t.prototype.hasMarkerChanged=function(t,e){var i=t.marker,o=e.marker||{};return i&&(o.enabled&&!i.enabled||o.symbol!==i.symbol||o.height!==i.height||o.width!==i.width)},t.prototype.autoIncrement=function(t){var e,i,o,n=this.options,r=this.options,s=r.pointIntervalUnit,a=r.relativeXValue,h=this.chart.time,l=null!==(i=null!==(e=this.xIncrement)&&void 0!==e?e:h.parse(n.pointStart))&&void 0!==i?i:0;if(this.pointInterval=o=sz(this.pointInterval,n.pointInterval,1),a&&sL(t)&&(o*=t),s){var d=h.toParts(l);"day"===s?d[2]+=o:"month"===s?d[1]+=o:"year"===s&&(d[0]+=o),o=h.makeTime.apply(h,d)-l}return a&&sL(t)?l+o:(this.xIncrement=l+o,l)},t.prototype.setDataSortingOptions=function(){var t=this.options;sS(this,{requireSorting:!1,sorted:!1,enabledDataSorting:!0,allowDG:!1}),sx(t.pointRange)||(t.pointRange=1)},t.prototype.setOptions=function(t){var e,i,o,n=this.chart,r=n.options.plotOptions,s=n.userOptions||{},a=sD(t),h=n.styledMode,l={plotOptions:r,userOptions:a};sT(this,"setOptions",l);var d=l.plotOptions[this.type],c=s.plotOptions||{},p=c.series||{},u=en.plotOptions[this.type]||{},f=c[this.type]||{};d.dataLabels=this.mergeArrays(u.dataLabels,d.dataLabels),this.userOptions=l.userOptions;var g=sD(d,r.series,f,a);this.tooltipOptions=sD(en.tooltip,null===(e=en.plotOptions.series)||void 0===e?void 0:e.tooltip,null==u?void 0:u.tooltip,n.userOptions.tooltip,null===(i=c.series)||void 0===i?void 0:i.tooltip,f.tooltip,a.tooltip),this.stickyTracking=sz(a.stickyTracking,f.stickyTracking,p.stickyTracking,!!this.tooltipOptions.shared&&!this.noSharedTooltip||g.stickyTracking),null===d.marker&&delete g.marker,this.zoneAxis=g.zoneAxis||"y";var v=this.zones=(g.zones||[]).map(function(t){return sa({},t)});return(g.negativeColor||g.negativeFillColor)&&!g.zones&&(o={value:g[this.zoneAxis+"Threshold"]||g.threshold||0,className:"highcharts-negative"},h||(o.color=g.negativeColor,o.fillColor=g.negativeFillColor),v.push(o)),v.length&&sx(v[v.length-1].value)&&v.push(h?{}:{color:this.color,fillColor:this.fillColor}),sT(this,"afterSetOptions",{options:g}),g},t.prototype.getName=function(){var t;return null!==(t=this.options.name)&&void 0!==t?t:su(this.chart.options.lang.seriesName,this,this.chart)},t.prototype.getCyclic=function(t,e,i){var o,n,r=this.chart,s=""+t+"Index",a=""+t+"Counter",h=(null==i?void 0:i.length)||r.options.chart.colorCount;!e&&(sx(n=sz("color"===t?this.options.colorIndex:void 0,this[s]))?o=n:(r.series.length||(r[a]=0),o=r[a]%h,r[a]+=1),i&&(e=i[o])),void 0!==o&&(this[s]=o),this[t]=e},t.prototype.getColor=function(){this.chart.styledMode?this.getCyclic("color"):this.options.colorByPoint?this.color="#cccccc":this.getCyclic("color",this.options.color||en.plotOptions[this.type].color,this.chart.options.colors)},t.prototype.getPointsCollection=function(){return(this.hasGroupedData?this.points:this.data)||[]},t.prototype.getSymbol=function(){var t=this.options.marker;this.getCyclic("symbol",t.symbol,this.chart.options.symbols)},t.prototype.getColumn=function(t,e){return(e?this.dataTable.modified:this.dataTable).getColumn(t,!0)||[]},t.prototype.findPointIndex=function(t,e){var i,o,n,r,s=t.id,a=t.x,h=this.points,l=this.options.dataSorting,d=this.cropStart||0;if(s){var c=this.chart.get(s);c instanceof rL&&(o=c)}else if(this.linkedParent||this.enabledDataSorting||this.options.relativeXValue){var p=function(e){return!e.touched&&e.index===t.index};if((null==l?void 0:l.matchByName)?p=function(e){return!e.touched&&e.name===t.name}:this.options.relativeXValue&&(p=function(e){return!e.touched&&e.options.x===t.x}),!(o=sA(h,p)))return}return o&&void 0!==(r=null==o?void 0:o.index)&&(n=!0),void 0===r&&sL(a)&&(r=this.getColumn("x").indexOf(a,e)),-1!==r&&void 0!==r&&this.cropped&&(r=r>=d?r-d:r),!n&&sL(r)&&(null===(i=h[r])||void 0===i?void 0:i.touched)&&(r=void 0),r},t.prototype.updateData=function(t,e){var i,o,n,r,s,a=this,h=this.options,l=this.requireSorting,d=h.dataSorting,c=this.points,p=[],u=t.length===c.length,f=!0;if(this.xIncrement=null,t.forEach(function(t,e){var i,n,r=sx(t)&&a.pointClass.prototype.optionsToObject.call({series:a},t)||{},f=r.id,g=r.x;f||sL(g)?(-1===(n=a.findPointIndex(r,s))||void 0===n?p.push(t):c[n]&&t!==(null===(i=h.data)||void 0===i?void 0:i[n])?(c[n].update(t,!1,void 0,!1),c[n].touched=!0,l&&(s=n+1)):c[n]&&(c[n].touched=!0),(!u||e!==n||(null==d?void 0:d.enabled)||a.hasDerivedData)&&(o=!0)):p.push(t)},this),o)for(n=c.length;n--;)(r=c[n])&&!r.touched&&(null===(i=r.remove)||void 0===i||i.call(r,!1,e));else!u||(null==d?void 0:d.enabled)?f=!1:(t.forEach(function(t,e){t===c[e].y||c[e].destroyed||c[e].update(t,!1,void 0,!1)}),p.length=0);if(c.forEach(function(t){t&&(t.touched=!1)}),!f)return!1;p.forEach(function(t){a.addPoint(t,!1,void 0,void 0,!1)},this);var g=this.getColumn("x");return null===this.xIncrement&&g.length&&(this.xIncrement=sf(g),this.autoIncrement()),!0},t.prototype.dataColumnKeys=function(){return sh(["x"],this.pointArrayMap||["y"],!0)},t.prototype.setData=function(t,e,i,o){void 0===e&&(e=!0);var n,r,s,a,h,l,d,c=this.points,p=(null==c?void 0:c.length)||0,u=this.options,f=this.chart,g=u.dataSorting,v=this.xAxis,m=u.turboThreshold,y=this.dataTable,x=this.dataColumnKeys(),b=this.pointValKey||"y",k=(this.pointArrayMap||[]).length,M=u.keys,w=0,S=1;f.options.chart.allowMutatingData||(u.data&&delete this.options.data,this.userOptions.data&&delete this.userOptions.data,d=sD(!0,t));var A=(t=d||t||[]).length;if((null==g?void 0:g.enabled)&&(t=this.sortData(t)),f.options.chart.allowMutatingData&&!1!==o&&A&&p&&!this.cropped&&!this.hasGroupedData&&this.visible&&!this.boosted&&(l=this.updateData(t,i)),!l){this.xIncrement=null,this.colorCounter=0;var T=m&&A>m;if(T){var C=this.getFirstValidPoint(t),O=this.getFirstValidPoint(t,A-1,-1),P=function(t){return!!(sE(t)&&(M||sL(t[0])))};if(sL(C)&&sL(O)){for(var E=[],L=[],B=0,D=t;B<D.length;B++){var I=D[B];E.push(this.autoIncrement()),L.push(I)}y.setColumns(((n={x:E})[b]=L,n))}else if(P(C)&&P(O)){if(k){for(var z=+(C.length===k),R=Array(x.length).fill(0).map(function(){return[]}),N=0,W=t;N<W.length;N++){var G=W[N];z&&R[0].push(this.autoIncrement());for(var X=z;X<=k;X++)null===(s=R[X])||void 0===s||s.push(G[X-z])}y.setColumns(x.reduce(function(t,e,i){return t[e]=R[i],t},{}))}else{M&&(w=M.indexOf("x"),S=M.indexOf("y"),w=w>=0?w:0,S=S>=0?S:1),1===C.length&&(S=0);var H=[],L=[];if(w===S)for(var F=0,Y=t;F<Y.length;F++){var G=Y[F];H.push(this.autoIncrement()),L.push(G[S])}else for(var _=0,j=t;_<j.length;_++){var G=j[_];H.push(G[w]),L.push(G[S])}y.setColumns(((r={x:H})[b]=L,r))}}else T=!1}if(!T){var U=x.reduce(function(t,e){return t[e]=[],t},{});for(h=0;h<A;h++)for(var G=this.pointClass.prototype.applyOptions.apply({series:this},[t[h]]),V=0;V<x.length;V++){var Z=x[V];U[Z][h]=G[Z]}y.setColumns(U)}for(sB(this.getColumn("y")[0])&&sw(14,!0,f),this.data=[],this.options.data=this.userOptions.data=t,h=p;h--;)null===(a=c[h])||void 0===a||a.destroy();v&&(v.minRange=v.userMinRange),this.isDirty=f.isDirtyBox=!0,this.isDirtyData=!!c,i=!1}"point"===u.legendType&&(this.processData(),this.generatePoints()),e&&f.redraw(i)},t.prototype.sortData=function(t){var e=this,i=e.options.dataSorting.sortKey||"y",o=function(t,e){return sx(e)&&t.pointClass.prototype.optionsToObject.call({series:t},e)||{}};return t.forEach(function(i,n){t[n]=o(e,i),t[n].index=n},this),t.concat().sort(function(t,e){var o=sO(i,t),n=sO(i,e);return n<o?-1:+(n>o)}).forEach(function(t,e){t.x=e},this),e.linkedSeries&&e.linkedSeries.forEach(function(e){var i,n=e.options,r=n.data;(null===(i=n.dataSorting)||void 0===i?void 0:i.enabled)||!r||(r.forEach(function(i,n){r[n]=o(e,i),t[n]&&(r[n].x=t[n].x,r[n].index=n)}),e.setData(r,!1))}),t},t.prototype.getProcessedData=function(t){var e,i,o,n,r,s=this,a=s.dataTable,h=s.isCartesian,l=s.options,d=s.xAxis,c=l.cropThreshold,p=t||s.getExtremesFromAll,u=null==d?void 0:d.logarithmic,f=a.rowCount,g=0,v=s.getColumn("x"),m=a,y=!1;return d&&(n=(o=d.getExtremes()).min,r=o.max,y=!!(d.categories&&!d.names.length),h&&s.sorted&&!p&&(!c||f>c||s.forceCrop)&&(v[f-1]<n||v[0]>r?m=new r6:s.getColumn(s.pointValKey||"y").length&&(v[0]<n||v[f-1]>r)&&(m=(e=this.cropData(a,n,r)).modified,g=e.start,i=!0))),v=m.getColumn("x")||[],{modified:m,cropped:i,cropStart:g,closestPointRange:sC([u?v.map(u.log2lin):v],function(){return s.requireSorting&&!y&&sw(15,!1,s.chart)})}},t.prototype.processData=function(t){var e=this.xAxis,i=this.dataTable;if(this.isCartesian&&!this.isDirty&&!e.isDirty&&!this.yAxis.isDirty&&!t)return!1;var o=this.getProcessedData();i.modified=o.modified,this.cropped=o.cropped,this.cropStart=o.cropStart,this.closestPointRange=this.basePointRange=o.closestPointRange,sT(this,"afterProcessData")},t.prototype.cropData=function(t,e,i){var o,n,r=t.getColumn("x",!0)||[],s=r.length,a={},h=0,l=s;for(o=0;o<s;o++)if(r[o]>=e){h=Math.max(0,o-1);break}for(n=o;n<s;n++)if(r[n]>i){l=n+1;break}for(var d=0,c=this.dataColumnKeys();d<c.length;d++){var p=c[d],u=t.getColumn(p,!0);u&&(a[p]=u.slice(h,l))}return{modified:new r6({columns:a}),start:h,end:l}},t.prototype.generatePoints=function(){var t,e,i,o,n,r,s,a,h,l,d=this.options,c=this.processedData||d.data,p=this.dataTable.modified,u=this.getColumn("x",!0),f=this.pointClass,g=p.rowCount,v=this.cropStart||0,m=this.hasGroupedData,y=d.keys,x=[],b=(null===(t=d.dataGrouping)||void 0===t?void 0:t.groupAll)?v:0,k=null===(e=this.xAxis)||void 0===e?void 0:e.categories,M=this.pointArrayMap||["y"],w=this.dataColumnKeys(),S=this.data;if(!S&&!m){var A=[];A.length=(null==c?void 0:c.length)||0,S=this.data=A}for(y&&m&&(this.options.keys=!1),h=0;h<g;h++)s=v+h,m?((a=new f(this,p.getRow(h,w)||[])).dataGroup=this.groupMap[b+h],(null===(i=a.dataGroup)||void 0===i?void 0:i.options)&&(a.options=a.dataGroup.options,sS(a,a.dataGroup.options),delete a.dataLabels)):(a=S[s],l=c?c[s]:p.getRow(h,M),a||void 0===l||(S[s]=a=new f(this,l,u[h]))),a&&(a.index=m?b+h:s,x[h]=a,a.category=null!==(o=null==k?void 0:k[a.x])&&void 0!==o?o:a.x,a.key=null!==(n=a.name)&&void 0!==n?n:a.category);if(this.options.keys=y,S&&(g!==(r=S.length)||m))for(h=0;h<r;h++)h!==v||m||(h+=g),S[h]&&(S[h].destroyElements(),S[h].plotX=void 0);this.data=S,this.points=x,sT(this,"afterGeneratePoints")},t.prototype.getXExtremes=function(t){return{min:sg(t),max:sf(t)}},t.prototype.getExtremes=function(t,e){var i,o,n,r,s=this.xAxis,a=this.yAxis,h=e||this.getExtremesFromAll||this.options.getExtremesFromAll,l=h&&this.cropped?this.dataTable:this.dataTable.modified,d=l.rowCount,c=t||this.stackedYData,p=c?[c]:(null===(i=this.keysAffectYAxis||this.pointArrayMap||["y"])||void 0===i?void 0:i.map(function(t){return l.getColumn(t,!0)||[]}))||[],u=this.getColumn("x",!0),f=[],g=this.requireSorting&&!this.is("column")?1:0,v=!!a&&a.positiveValuesOnly,m=h||this.cropped||!s,y=0,x=0;for(s&&(y=(o=s.getExtremes()).min,x=o.max),r=0;r<d;r++)if(n=u[r],m||(u[r+g]||n)>=y&&(u[r-g]||n)<=x)for(var b=0;b<p.length;b++){var k=p[b][r];sL(k)&&(k>0||!v)&&f.push(k)}var M={activeYData:f,dataMin:sg(f),dataMax:sf(f)};return sT(this,"afterGetExtremes",{dataExtremes:M}),M},t.prototype.applyExtremes=function(){var t=this.getExtremes();return this.dataMin=t.dataMin,this.dataMax=t.dataMax,t},t.prototype.getFirstValidPoint=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=1);for(var o=t.length,n=e;n>=0&&n<o;){if(sx(t[n]))return t[n];n+=i}},t.prototype.translate=function(){this.generatePoints();var t,e,i,o,n,r=this.options,s=r.stacking,a=this.xAxis,h=this.enabledDataSorting,l=this.yAxis,d=this.points,c=d.length,p=this.pointPlacementToXValue(),u=!!p,f=r.threshold,g=r.startFromThreshold?f:0,v=(null==r?void 0:r.nullInteraction)&&l.len,m=Number.MAX_VALUE;function y(t){return sv(t,-1e9,1e9)}for(e=0;e<c;e++){var x=d[e],b=x.x,k=void 0,M=void 0,w=x.y,S=x.low,A=s&&(null===(t=l.stacking)||void 0===t?void 0:t.stacks[(this.negStacks&&w<(g?0:f)?"-":"")+this.stackKey]);x.plotX=sL(i=a.translate(b,!1,!1,!1,!0,p))?sm(y(i)):void 0,s&&this.visible&&A&&A[b]&&(n=this.getStackIndicator(n,b,this.index),!x.isNull&&n.key&&(M=(k=A[b]).points[n.key]),k&&sE(M)&&(S=M[0],w=M[1],S===g&&n.key===A[b].base&&(S=sz(sL(f)?f:l.min)),l.positiveValuesOnly&&sx(S)&&S<=0&&(S=void 0),x.total=x.stackTotal=sz(k.total),x.percentage=sx(x.y)&&k.total?x.y/k.total*100:void 0,x.stackY=w,this.irregularWidths||k.setOffset(this.pointXOffset||0,this.barW||0,void 0,void 0,void 0,this.xAxis))),x.yBottom=sx(S)?y(l.translate(S,!1,!0,!1,!0)):void 0,this.dataModify&&(w=this.dataModify.modifyValue(w,e));var T=void 0;sL(w)&&void 0!==x.plotX?T=sL(T=l.translate(w,!1,!0,!1,!0))?y(T):void 0:!sL(w)&&v&&(T=v),x.plotY=T,x.isInside=this.isPointInside(x),x.clientX=u?sm(a.translate(b,!1,!1,!1,!0,p)):i,x.negative=(x.y||0)<(f||0),x.isNull||!1===x.visible||(void 0!==o&&(m=Math.min(m,Math.abs(i-o))),o=i),x.zone=this.zones.length?x.getZone():void 0,!x.graphic&&this.group&&h&&(x.isNew=!0)}this.closestPointRangePx=m,sT(this,"afterTranslate")},t.prototype.getValidPoints=function(t,e,i){var o=this.chart;return(t||this.points||[]).filter(function(t){var n=t.plotX,r=t.plotY;return!!((i||!t.isNull&&sL(r))&&(!e||o.isInsidePlot(n,r,{inverted:o.inverted})))&&!1!==t.visible})},t.prototype.getSharedClipKey=function(){return this.sharedClipKey=(this.options.xAxis||0)+","+(this.options.yAxis||0),this.sharedClipKey},t.prototype.setClip=function(){var t=this.chart,e=this.group,i=this.markerGroup,o=t.sharedClips,n=t.renderer,r=t.getClipBox(this),s=this.getSharedClipKey(),a=o[s];a?a.animate(r):o[s]=a=n.clipRect(r),e&&e.clip(!1===this.options.clip?void 0:a),i&&i.clip()},t.prototype.animate=function(t){var e=this.chart,i=this.group,o=this.markerGroup,n=e.inverted,r=eE(this.options.animation),s=[this.getSharedClipKey(),r.duration,r.easing,r.defer].join(","),a=e.sharedClips[s],h=e.sharedClips[s+"m"];if(t&&i){var l=e.getClipBox(this);if(a)a.attr("height",l.height);else{l.width=0,n&&(l.x=e.plotHeight),a=e.renderer.clipRect(l),e.sharedClips[s]=a;var d={x:-99,y:-99,width:n?e.plotWidth+199:99,height:n?99:e.plotHeight+199};h=e.renderer.clipRect(d),e.sharedClips[s+"m"]=h}i.clip(a),null==o||o.clip(h)}else if(a&&!a.hasClass("highcharts-animating")){var c=e.getClipBox(this),p=r.step;((null==o?void 0:o.element.childNodes.length)||e.series.length>1)&&(r.step=function(t,e){p&&p.apply(e,arguments),"width"===e.prop&&(null==h?void 0:h.element)&&h.attr(n?"height":"width",t+99)}),a.addClass("highcharts-animating").animate(c,r)}},t.prototype.afterAnimate=function(){var t=this;this.setClip(),sI(this.chart.sharedClips,function(e,i,o){e&&!t.chart.container.querySelector('[clip-path="url(#'.concat(e.id,')"]'))&&(e.destroy(),delete o[i])}),this.finishedAnimating=!0,sT(this,"afterAnimate")},t.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i,o,n,r,s,a,h=this.chart,l=h.styledMode,d=this.colorAxis,c=this.options,p=c.marker,u=c.nullInteraction,f=this[this.specialGroup||"markerGroup"],g=this.xAxis,v=sz(p.enabled,!g||!!g.isRadial||null,this.closestPointRangePx>=p.enabledThreshold*p.radius);if(!1!==p.enabled||this._hasPointMarkers)for(e=0;e<t.length;e++){n=(o=(i=t[e]).graphic)?"animate":"attr",r=i.marker||{},s=!!i.marker;var m=i.isNull;if((v&&!sx(r.enabled)||r.enabled)&&(!m||u)&&!1!==i.visible){var y=sz(r.symbol,this.symbol,"rect");a=this.markerAttribs(i,i.selected&&"select"),this.enabledDataSorting&&(i.startXPos=g.reversed?-(a.width||0):g.width);var x=!1!==i.isInside;if(!o&&x&&((a.width||0)>0||i.hasImage)&&(i.graphic=o=h.renderer.symbol(y,a.x,a.y,a.width,a.height,s?r:p).add(f),this.enabledDataSorting&&h.hasRendered&&(o.attr({x:i.startXPos}),n="animate")),o&&"animate"===n&&o[x?"show":"hide"](x).animate(a),o){var b=this.pointAttribs(i,l||!i.selected?void 0:"select");l?d&&o.css({fill:b.fill}):o[n](b)}o&&o.addClass(i.getClassName(),!0)}else o&&(i.graphic=o.destroy())}},t.prototype.markerAttribs=function(t,e){var i,o,n=this.options,r=n.marker,s=t.marker||{},a=s.symbol||r.symbol,h={},l=sz(s.radius,null==r?void 0:r.radius);e&&(i=r.states[e],l=sz(null==(o=s.states&&s.states[e])?void 0:o.radius,null==i?void 0:i.radius,l&&l+((null==i?void 0:i.radiusPlus)||0))),t.hasImage=a&&0===a.indexOf("url"),t.hasImage&&(l=0);var d=t.pos();return sL(l)&&d&&(n.crisp&&(d[0]=sy(d[0],t.hasImage?0:"rect"===a?(null==r?void 0:r.lineWidth)||0:1)),h.x=d[0]-l,h.y=d[1]-l),l&&(h.width=h.height=2*l),h},t.prototype.pointAttribs=function(t,e){var i,o,n,r,s,a=this.options,h=a.marker,l=null==t?void 0:t.options,d=(null==l?void 0:l.marker)||{},c=null==l?void 0:l.color,p=null==t?void 0:t.color,u=null===(i=null==t?void 0:t.zone)||void 0===i?void 0:i.color,f=this.color,g=sz(d.lineWidth,h.lineWidth),v=(null==t?void 0:t.isNull)&&a.nullInteraction?0:1;return f=c||u||p||f,r=d.fillColor||h.fillColor||f,s=d.lineColor||h.lineColor||f,e=e||"normal",o=h.states[e]||{},g=sz((n=d.states&&d.states[e]||{}).lineWidth,o.lineWidth,g+sz(n.lineWidthPlus,o.lineWidthPlus,0)),r=n.fillColor||o.fillColor||r,s=n.lineColor||o.lineColor||s,{stroke:s,"stroke-width":g,fill:r,opacity:v=sz(n.opacity,o.opacity,v)}},t.prototype.destroy=function(t){var e,i,o,n,r=this,s=r.chart,a=/AppleWebKit\/533/.test(sc.navigator.userAgent),h=r.data||[];for(sT(r,"destroy",{keepEventsForUpdate:t}),this.removeEvents(t),(r.axisTypes||[]).forEach(function(t){(null==(n=r[t])?void 0:n.series)&&(sM(n.series,r),n.isDirty=n.forceRedraw=!0)}),r.legendItem&&r.chart.legend.destroyItem(r),o=h.length;o--;)null===(i=null===(e=h[o])||void 0===e?void 0:e.destroy)||void 0===i||i.call(e);for(var l=0,d=r.zones;l<d.length;l++)sb(d[l],void 0,!0);tF.clearTimeout(r.animationTimeout),sI(r,function(t,e){t instanceof iR&&!t.survive&&t[a&&"group"===e?"hide":"destroy"]()}),s.hoverSeries===r&&(s.hoverSeries=void 0),sM(s.series,r),s.orderItems("series"),sI(r,function(e,i){t&&"hcEvents"===i||delete r[i]})},t.prototype.applyZones=function(){var t=this.area,e=this.chart,i=this.graph,o=this.zones,n=this.points,r=this.xAxis,s=this.yAxis,a=this.zoneAxis,h=e.inverted,l=e.renderer,d=this[""+a+"Axis"],c=d||{},p=c.isXAxis,u=c.len,f=void 0===u?0:u,g=c.minPointOffset,v=void 0===g?0:g,m=((null==i?void 0:i.strokeWidth())||0)/2+1,y=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0),h&&(i=f-i);var o=t.translated,n=void 0===o?0:o,r=t.lineClip,s=i-n;null==r||r.push(["L",e,Math.abs(s)<m?i-m*(s<=0?-1:1):n])};if(o.length&&(i||t)&&d&&sL(d.min)){var x=d.getExtremes().max+v,b=function(t){t.forEach(function(e,i){("M"===e[0]||"L"===e[0])&&(t[i]=[e[0],p?f-e[1]:e[1],p?e[2]:f-e[2]])})};if(o.forEach(function(t){t.lineClip=[],t.translated=sv(d.toPixels(sz(t.value,x),!0)||0,0,f)}),i&&!this.showLine&&i.hide(),t&&t.hide(),"y"===a&&n.length<r.len)for(var k=0;k<n.length;k++){var M=n[k],w=M.plotX,S=M.plotY,A=M.zone,T=A&&o[o.indexOf(A)-1];A&&y(A,w,S),T&&y(T,w,S)}var C=[],O=d.toPixels(d.getExtremes().min-v,!0);o.forEach(function(e){var o,n,a=e.lineClip||[],d=Math.round(e.translated||0);r.reversed&&a.reverse();var c=e.clip,u=e.simpleClip,f=0,g=0,v=r.len,m=s.len;p?(f=d,v=O):(g=d,m=O);var y=[["M",f,g],["L",v,g],["L",v,m],["L",f,m],["Z"]],x=sh(sh(sh(sh([y[0]],a,!0),[y[1],y[2]],!1),C,!0),[y[3],y[4]],!1);C=a.reverse(),O=d,h&&(b(x),t&&b(y)),c?(c.animate({d:x}),null==u||u.animate({d:y})):(c=e.clip=l.path(x),t&&(u=e.simpleClip=l.path(y))),i&&(null===(o=e.graph)||void 0===o||o.clip(c)),t&&(null===(n=e.area)||void 0===n||n.clip(u))})}else this.visible&&(i&&i.show(),t&&t.show())},t.prototype.plotGroup=function(t,e,i,o,n){var r=this[t],s=!r,a={visibility:i,zIndex:o||.1};return sx(this.opacity)&&!this.chart.styledMode&&"inactive"!==this.state&&(a.opacity=this.opacity),r||(this[t]=r=this.chart.renderer.g().add(n)),r.addClass("highcharts-"+e+" highcharts-series-"+this.index+" highcharts-"+this.type+"-series "+(sx(this.colorIndex)?"highcharts-color-"+this.colorIndex+" ":"")+(this.options.className||"")+(r.hasClass("highcharts-tracker")?" highcharts-tracker":""),!0),r.attr(a)[s?"attr":"animate"](this.getPlotBox(e)),r},t.prototype.getPlotBox=function(t){var e=this.xAxis,i=this.yAxis,o=this.chart,n=o.inverted&&!o.polar&&e&&this.invertible&&"series"===t;return o.inverted&&(e=i,i=this.xAxis),{translateX:e?e.left:o.plotLeft,translateY:i?i.top:o.plotTop,rotation:90*!!n,rotationOriginX:n?(e.len-i.len)/2:0,rotationOriginY:n?(e.len+i.len)/2:0,scaleX:n?-1:1,scaleY:1}},t.prototype.removeEvents=function(t){var e=this.eventsToUnbind;t||sR(this),e.length&&(e.forEach(function(t){t()}),e.length=0)},t.prototype.render=function(){var t,e,i,o,n,r=this,s=r.chart,a=r.options,h=r.hasRendered,l=eE(a.animation),d=r.visible?"inherit":"hidden",c=a.zIndex,p=s.seriesGroup,u=r.finishedAnimating?0:l.duration;sT(this,"render"),r.plotGroup("group","series",d,c,p),r.markerGroup=r.plotGroup("markerGroup","markers",d,c,p),!1!==a.clip&&r.setClip(),u&&(null===(t=r.animate)||void 0===t||t.call(r,!0)),r.drawGraph&&(r.drawGraph(),r.applyZones()),r.visible&&r.drawPoints(),null===(e=r.drawDataLabels)||void 0===e||e.call(r),null===(i=r.redrawPoints)||void 0===i||i.call(r),a.enableMouseTracking&&(null===(o=r.drawTracker)||void 0===o||o.call(r)),u&&(null===(n=r.animate)||void 0===n||n.call(r)),h||(u&&l.defer&&(u+=l.defer),r.animationTimeout=sN(function(){r.afterAnimate()},u||0)),r.isDirty=!1,r.hasRendered=!0,sT(r,"afterRender")},t.prototype.redraw=function(){var t=this.isDirty||this.isDirtyData;this.translate(),this.render(),t&&delete this.kdTree},t.prototype.reserveSpace=function(){return this.visible||!this.chart.options.chart.ignoreHiddenSeries},t.prototype.searchPoint=function(t,e){var i=this.xAxis,o=this.yAxis,n=this.chart.inverted;return this.searchKDTree({clientX:n?i.len-t.chartY+i.pos:t.chartX-i.pos,plotY:n?o.len-t.chartX+o.pos:t.chartY-o.pos},e,t)},t.prototype.buildKDTree=function(t){this.buildingKdTree=!0;var e=this,i=e.options,o=i.findNearestPointBy.indexOf("y")>-1?2:1;delete e.kdTree,sN(function(){e.kdTree=function t(i,o,n){var r,s,a=null==i?void 0:i.length;if(a)return r=e.kdAxisArray[o%n],i.sort(function(t,e){return(t[r]||0)-(e[r]||0)}),{point:i[s=Math.floor(a/2)],left:t(i.slice(0,s),o+1,n),right:t(i.slice(s+1),o+1,n)}}(e.getValidPoints(void 0,!e.directTouch,null==i?void 0:i.nullInteraction),o,o),e.buildingKdTree=!1},i.kdNow||(null==t?void 0:t.type)==="touchstart"?0:1)},t.prototype.searchKDTree=function(t,e,i,o,n){var r=this,s=this.kdAxisArray,a=s[0],h=s[1],l=e?"distX":"dist",d=(r.options.findNearestPointBy||"").indexOf("y")>-1?2:1,c=!!r.isBubble,p=o||function(t,e,i){var o=t[i]||0,n=e[i]||0;return[o===n&&t.index>e.index||o<n?t:e,!1]},u=n||function(t,e){return t<e};if(this.kdTree||this.buildingKdTree||this.buildKDTree(i),this.kdTree)return function t(e,i,o,n){var s,d,f,g,v,m,y,x,b,k,M=i.point,w=r.kdAxisArray[o%n],S=M,A=!1;d=e[a],f=M[a],g=sx(d)&&sx(f)?d-f:null,v=e[h],m=M[h],y=sx(v)&&sx(m)?v-m:0,x=c&&(null===(s=M.marker)||void 0===s?void 0:s.radius)||0,M.dist=Math.sqrt((g&&g*g||0)+y*y)-x,M.distX=sx(g)?Math.abs(g)-x:Number.MAX_VALUE;var T=(e[w]||0)-(M[w]||0)+(c&&(null===(k=M.marker)||void 0===k?void 0:k.radius)||0),C=T<0?"left":"right",O=T<0?"right":"left";return i[C]&&(S=(b=p(M,t(e,i[C],o+1,n),l))[0],A=b[1]),i[O]&&u(Math.sqrt(T*T),S[l],A)&&(S=p(S,t(e,i[O],o+1,n),l)[0]),S}(t,this.kdTree,d,d)},t.prototype.pointPlacementToXValue=function(){var t=this.options,e=this.xAxis,i=t.pointPlacement;return"between"===i&&(i=e.reversed?-.5:.5),sL(i)?i*(t.pointRange||e.pointRange):0},t.prototype.isPointInside=function(t){var e=this.chart,i=this.xAxis,o=this.yAxis,n=t.plotX,r=void 0===n?-1:n,s=t.plotY,a=void 0===s?-1:s;return a>=0&&a<=(o?o.len:e.plotHeight)&&r>=0&&r<=(i?i.len:e.plotWidth)},t.prototype.drawTracker=function(){var t,e=this,i=e.options,o=i.trackByArea,n=[].concat((o?e.areaPath:e.graphPath)||[]),r=e.chart,s=r.pointer,a=r.renderer,h=(null===(t=r.options.tooltip)||void 0===t?void 0:t.snap)||0,l=function(){i.enableMouseTracking&&r.hoverSeries!==e&&e.onMouseOver()},d="rgba(192,192,192,"+(sd?1e-4:.002)+")",c=e.tracker;c?c.attr({d:n}):e.graph&&(e.tracker=c=a.path(n).attr({visibility:e.visible?"inherit":"hidden",zIndex:2}).addClass(o?"highcharts-tracker-area":"highcharts-tracker-line").add(e.group),r.styledMode||c.attr({"stroke-linecap":"round","stroke-linejoin":"round",stroke:d,fill:o?d:"none","stroke-width":e.graph.strokeWidth()+(o?0:2*h)}),[e.tracker,e.markerGroup,e.dataLabelsGroup].forEach(function(t){t&&(t.addClass("highcharts-tracker").on("mouseover",l).on("mouseout",function(t){null==s||s.onTrackerMouseOut(t)}),i.cursor&&!r.styledMode&&t.css({cursor:i.cursor}),t.on("touchstart",l))})),sT(this,"afterDrawTracker")},t.prototype.addPoint=function(t,e,i,o,n){var r,s,a=this.options,h=this.chart,l=this.data,d=this.dataTable,c=this.xAxis,p=(null==c?void 0:c.hasNames)&&c.names,u=a.data,f=this.getColumn("x");e=sz(e,!0);var g={series:this};this.pointClass.prototype.applyOptions.apply(g,[t]);var v=g.x;if(s=f.length,this.requireSorting&&v<f[s-1])for(r=!0;s&&f[s-1]>v;)s--;d.setRow(g,s,!0,{addColumns:!1}),p&&g.name&&(p[v]=g.name),null==u||u.splice(s,0,t),(r||this.processedData)&&(this.data.splice(s,0,null),this.processData()),"point"===a.legendType&&this.generatePoints(),i&&(l[0]&&l[0].remove?l[0].remove(!1):([l,u].filter(sx).forEach(function(t){t.shift()}),d.deleteRows(0))),!1!==n&&sT(this,"addPoint",{point:g}),this.isDirty=!0,this.isDirtyData=!0,e&&h.redraw(o)},t.prototype.removePoint=function(t,e,i){var o=this,n=o.chart,r=o.data,s=o.points,a=o.dataTable,h=r[t],l=function(){[(null==s?void 0:s.length)===r.length?s:void 0,r,o.options.data].filter(sx).forEach(function(e){e.splice(t,1)}),a.deleteRows(t),null==h||h.destroy(),o.isDirty=!0,o.isDirtyData=!0,e&&n.redraw()};eI(i,n),e=sz(e,!0),h?h.firePointEvent("remove",null,l):l()},t.prototype.remove=function(t,e,i,o){var n=this,r=n.chart;function s(){n.destroy(o),r.isDirtyLegend=r.isDirtyBox=!0,r.linkSeries(o),sz(t,!0)&&r.redraw(e)}!1!==i?sT(n,"remove",null,s):s()},t.prototype.update=function(e,i){sT(this,"update",{options:e=sk(e,this.userOptions)});var o,n,r,s,a,h,l=this,d=l.chart,c=l.userOptions,p=l.initialType||l.type,u=d.options.plotOptions,f=sp[p].prototype,g=l.finishedAnimating&&{animation:!1},v={},m=t.keepProps.slice(),y=e.type||c.type||d.options.chart.type,x=!(this.hasDerivedData||y&&y!==this.type||void 0!==e.keys||void 0!==e.pointStart||void 0!==e.pointInterval||void 0!==e.relativeXValue||e.joinBy||e.mapData||["dataGrouping","pointStart","pointInterval","pointIntervalUnit","keys"].some(function(t){return l.hasOptionChanged(t)}));y=y||p,x?(m.push.apply(m,t.keepPropsForPoints),!1!==e.visible&&m.push("area","graph"),l.parallelArrays.forEach(function(t){m.push(t+"Data")}),e.data&&(e.dataSorting&&sS(l.options.dataSorting,e.dataSorting),this.setData(e.data,!1))):this.dataTable.modified=this.dataTable,e=sD(c,{index:void 0===c.index?l.index:c.index,pointStart:null!==(r=null!==(n=null===(o=null==u?void 0:u.series)||void 0===o?void 0:o.pointStart)&&void 0!==n?n:c.pointStart)&&void 0!==r?r:l.getColumn("x")[0]},!x&&{data:l.options.data},e,g),x&&e.data&&(e.data=l.options.data),(m=["group","markerGroup","dataLabelsGroup","transformGroup"].concat(m)).forEach(function(t){m[t]=l[t],delete l[t]});var b=!1;if(sp[y]){if(b=y!==l.type,l.remove(!1,!1,!1,!0),b){if(d.propFromSeries(),Object.setPrototypeOf)Object.setPrototypeOf(l,sp[y].prototype);else{var k=Object.hasOwnProperty.call(l,"hcEvents")&&l.hcEvents;for(h in f)l[h]=void 0;sS(l,sp[y].prototype),k?l.hcEvents=k:delete l.hcEvents}}}else sw(17,!0,d,{missingModuleFor:y});if(m.forEach(function(t){l[t]=m[t]}),l.init(d,e),x&&this.points){!1===(a=l.options).visible?(v.graphic=1,v.dataLabel=1):(this.hasMarkerChanged(a,c)&&(v.graphic=1),(null===(s=l.hasDataLabels)||void 0===s?void 0:s.call(l))||(v.dataLabel=1));for(var M=0,w=this.points;M<w.length;M++){var S=w[M];(null==S?void 0:S.series)&&(S.resolveColor(),Object.keys(v).length&&S.destroyElements(v),!1===a.showInLegend&&S.legendItem&&d.legend.destroyItem(S))}}l.initialType=p,d.linkSeries(),d.setSortedData(),b&&l.linkedSeries.length&&(l.isDirtyData=!0),sT(this,"afterUpdate"),sz(i,!0)&&d.redraw(!!x&&void 0)},t.prototype.setName=function(t){this.name=this.options.name=this.userOptions.name=t,this.chart.isDirtyLegend=!0},t.prototype.hasOptionChanged=function(t){var e,i,o=this.chart,n=this.options[t],r=o.options.plotOptions,s=this.userOptions[t],a=sz(null===(e=null==r?void 0:r[this.type])||void 0===e?void 0:e[t],null===(i=null==r?void 0:r.series)||void 0===i?void 0:i[t]);return s&&!sx(a)?n!==s:n!==sz(a,n)},t.prototype.onMouseOver=function(){var t=this.chart,e=t.hoverSeries,i=t.pointer;null==i||i.setHoverChartIndex(),e&&e!==this&&e.onMouseOut(),this.options.events.mouseOver&&sT(this,"mouseOver"),this.setState("hover"),t.hoverSeries=this},t.prototype.onMouseOut=function(){var t=this.options,e=this.chart,i=e.tooltip,o=e.hoverPoint;e.hoverSeries=null,o&&o.onMouseOut(),this&&t.events.mouseOut&&sT(this,"mouseOut"),i&&!this.stickyTracking&&(!i.shared||this.noSharedTooltip)&&i.hide(),e.series.forEach(function(t){t.setState("",!0)})},t.prototype.setState=function(t,e){var i=this,o=i.options,n=i.graph,r=o.inactiveOtherPoints,s=o.states,a=sz(s[t||"normal"]&&s[t||"normal"].animation,i.chart.options.chart.animation),h=o.lineWidth,l=o.opacity;if(t=t||"",i.state!==t&&([i.group,i.markerGroup,i.dataLabelsGroup].forEach(function(e){e&&(i.state&&e.removeClass("highcharts-series-"+i.state),t&&e.addClass("highcharts-series-"+t))}),i.state=t,!i.chart.styledMode)){if(s[t]&&!1===s[t].enabled)return;if(t&&(h=s[t].lineWidth||h+(s[t].lineWidthPlus||0),l=sz(s[t].opacity,l)),n&&!n.dashstyle&&sL(h))for(var d=0,c=sh([n],this.zones.map(function(t){return t.graph}),!0);d<c.length;d++){var p=c[d];null==p||p.animate({"stroke-width":h},a)}r||[i.group,i.markerGroup,i.dataLabelsGroup,i.labelBySeries].forEach(function(t){t&&t.animate({opacity:l},a)})}e&&r&&i.points&&i.setAllPointsToState(t||void 0)},t.prototype.setAllPointsToState=function(t){this.points.forEach(function(e){e.setState&&e.setState(t)})},t.prototype.setVisible=function(t,e){var i,o=this,n=o.chart,r=n.options.chart.ignoreHiddenSeries,s=o.visible;o.visible=t=o.options.visible=o.userOptions.visible=void 0===t?!s:t;var a=t?"show":"hide";["group","dataLabelsGroup","markerGroup","tracker","tt"].forEach(function(t){var e;null===(e=o[t])||void 0===e||e[a]()}),(n.hoverSeries===o||(null===(i=n.hoverPoint)||void 0===i?void 0:i.series)===o)&&o.onMouseOut(),o.legendItem&&n.legend.colorizeItem(o,t),o.isDirty=!0,o.options.stacking&&n.series.forEach(function(t){t.options.stacking&&t.visible&&(t.isDirty=!0)}),o.linkedSeries.forEach(function(e){e.setVisible(t,!1)}),r&&(n.isDirtyBox=!0),sT(o,a),!1!==e&&n.redraw()},t.prototype.show=function(){this.setVisible(!0)},t.prototype.hide=function(){this.setVisible(!1)},t.prototype.select=function(t){this.selected=t=this.options.selected=void 0===t?!this.selected:t,this.checkbox&&(this.checkbox.checked=t),sT(this,t?"select":"unselect")},t.prototype.shouldShowTooltip=function(t,e,i){return void 0===i&&(i={}),i.series=this,i.visiblePlotOnly=!0,this.chart.isInsidePlot(t,e,i)},t.prototype.drawLegendSymbol=function(t,e){var i;null===(i=st[this.options.legendSymbol||"rectangle"])||void 0===i||i.call(this,t,e)},t.defaultOptions=se,t.types=ss.seriesTypes,t.registerType=ss.registerSeriesType,t.keepProps=["colorIndex","eventOptions","navigatorSeries","symbolIndex","baseSeries"],t.keepPropsForPoints=["data","isDirtyData","isDirtyCanvas","points","dataTable","processedData","xIncrement","cropped","_hasPointMarkers","hasDataLabels","nodes","layout","level","mapMap","mapData","minY","maxY","minX","maxX","transformGroups"],t}();sS(sW.prototype,{axisTypes:["xAxis","yAxis"],coll:"series",colorCounter:0,directTouch:!1,invertible:!0,isCartesian:!0,kdAxisArray:["clientX","plotY"],parallelArrays:["x","y"],pointClass:rL,requireSorting:!0,sorted:!0}),ss.series=sW;var sG=o$.registerEventOptions,sX=tv.composed,sH=tv.marginNames,sF=ir.distribute,sY=e8.format,s_=tF.addEvent,sj=tF.createElement,sU=tF.css,sV=tF.defined,sZ=tF.discardElement,sq=tF.find,sK=tF.fireEvent,s$=tF.isNumber,sJ=tF.merge,sQ=tF.pick,s0=tF.pushUnique,s1=tF.relativeLength,s2=tF.stableSort,s3=tF.syncTimeout,s5=function(){function t(t,e){var i=this;this.allItems=[],this.initialItemY=0,this.itemHeight=0,this.itemMarginBottom=0,this.itemMarginTop=0,this.itemX=0,this.itemY=0,this.lastItemY=0,this.lastLineHeight=0,this.legendHeight=0,this.legendWidth=0,this.maxItemWidth=0,this.maxLegendWidth=0,this.offsetWidth=0,this.padding=0,this.pages=[],this.symbolHeight=0,this.symbolWidth=0,this.titleHeight=0,this.totalItemWidth=0,this.widthOption=0,this.chart=t,this.setOptions(e),e.enabled&&(this.render(),sG(this,e),s_(this.chart,"endResize",function(){this.legend.positionCheckboxes()})),s_(this.chart,"render",function(){i.options.enabled&&i.proximate&&(i.proximatePositions(),i.positionItems())})}return t.prototype.setOptions=function(t){var e=sQ(t.padding,8);this.options=t,this.chart.styledMode||(this.itemStyle=t.itemStyle,this.itemHiddenStyle=sJ(this.itemStyle,t.itemHiddenStyle)),this.itemMarginTop=t.itemMarginTop,this.itemMarginBottom=t.itemMarginBottom,this.padding=e,this.initialItemY=e-5,this.symbolWidth=sQ(t.symbolWidth,16),this.pages=[],this.proximate="proximate"===t.layout&&!this.chart.inverted,this.baseline=void 0},t.prototype.update=function(t,e){var i=this.chart;this.setOptions(sJ(!0,this.options,t)),"events"in this.options&&sG(this,this.options),this.destroy(),i.isDirtyLegend=i.isDirtyBox=!0,sQ(e,!0)&&i.redraw(),sK(this,"afterUpdate",{redraw:e})},t.prototype.colorizeItem=function(t,e){var i,o=t.color,n=t.legendItem||{},r=n.area,s=n.group,a=n.label,h=n.line,l=n.symbol;if((t instanceof sW||t instanceof rL)&&(t.color=(null===(i=t.options)||void 0===i?void 0:i.legendSymbolColor)||o),null==s||s[e?"removeClass":"addClass"]("highcharts-legend-item-hidden"),!this.chart.styledMode){var d=this.itemHiddenStyle,c=void 0===d?{}:d,p=c.color,u=t.options,f=u.fillColor,g=u.fillOpacity,v=u.lineColor,m=u.marker,y=function(t){return!e&&(t.fill&&(t.fill=p),t.stroke&&(t.stroke=p)),t};null==a||a.css(sJ(e?this.itemStyle:c)),null==h||h.attr(y({stroke:v||t.color})),l&&l.attr(y(m&&l.isMarker?t.pointAttribs():{fill:t.color})),null==r||r.attr(y({fill:f||t.color,"fill-opacity":f?1:null!=g?g:.75}))}t.color=o,sK(this,"afterColorizeItem",{item:t,visible:e})},t.prototype.positionItems=function(){this.allItems.forEach(this.positionItem,this),this.chart.isResizing||this.positionCheckboxes()},t.prototype.positionItem=function(t){var e=this,i=t.legendItem||{},o=i.group,n=i.x,r=void 0===n?0:n,s=i.y,a=void 0===s?0:s,h=this.options,l=h.symbolPadding,d=!h.rtl,c=t.checkbox;if(null==o?void 0:o.element){var p={translateX:d?r:this.legendWidth-r-2*l-4,translateY:a};o[sV(o.translateY)?"animate":"attr"](p,void 0,function(){sK(e,"afterPositionItem",{item:t})})}c&&(c.x=r,c.y=a)},t.prototype.destroyItem=function(t){for(var e=t.checkbox,i=t.legendItem||{},o=0,n=["group","label","line","symbol"];o<n.length;o++){var r=n[o];i[r]&&(i[r]=i[r].destroy())}e&&sZ(e),t.legendItem=void 0},t.prototype.destroy=function(){for(var t=0,e=this.getAllItems();t<e.length;t++){var i=e[t];this.destroyItem(i)}for(var o=0,n=["clipRect","up","down","pager","nav","box","title","group"];o<n.length;o++){var r=n[o];this[r]&&(this[r]=this[r].destroy())}this.display=null},t.prototype.positionCheckboxes=function(){var t,e,i=null===(t=this.group)||void 0===t?void 0:t.alignAttr,o=this.clipHeight||this.legendHeight,n=this.titleHeight;i&&(e=i.translateY,this.allItems.forEach(function(t){var r,s=t.checkbox;s&&(r=e+n+s.y+(this.scrollOffset||0)+3,sU(s,{left:i.translateX+t.checkboxOffset+s.x-20+"px",top:r+"px",display:this.proximate||r>e-6&&r<e+o-6?"":"none"}))},this))},t.prototype.renderTitle=function(){var t,e=this.options,i=this.padding,o=e.title,n=0;o.text&&(this.title||(this.title=this.chart.renderer.label(o.text,i-3,i-4,void 0,void 0,void 0,e.useHTML,void 0,"legend-title").attr({zIndex:1}),this.chart.styledMode||this.title.css(o.style),this.title.add(this.group)),o.width||this.title.css({width:this.maxLegendWidth+"px"}),n=(t=this.title.getBBox()).height,this.offsetWidth=t.width,this.contentGroup.attr({translateY:n})),this.titleHeight=n},t.prototype.setText=function(t){var e=this.options;t.legendItem.label.attr({text:e.labelFormat?sY(e.labelFormat,t,this.chart):e.labelFormatter.call(t)})},t.prototype.renderItem=function(t){var e,i=t.legendItem=t.legendItem||{},o=this.chart,n=o.renderer,r=this.options,s="horizontal"===r.layout,a=this.symbolWidth,h=r.symbolPadding||0,l=this.itemStyle,d=this.itemHiddenStyle,c=s?sQ(r.itemDistance,20):0,p=!r.rtl,u=!t.series,f=!u&&t.series.drawLegendSymbol?t.series:t,g=f.options,v=!!this.createCheckboxForItem&&g&&g.showCheckbox,m=r.useHTML,y=t.options.className,x=i.label,b=a+h+c+20*!!v;!x&&(i.group=n.g("legend-item").addClass("highcharts-"+f.type+"-series highcharts-color-"+t.colorIndex+(y?" "+y:"")+(u?" highcharts-series-"+t.index:"")).attr({zIndex:1}).add(this.scrollGroup),i.label=x=n.text("",p?a+h:-h,this.baseline||0,m),o.styledMode||x.css(sJ(t.visible?l:d)),x.attr({align:p?"left":"right",zIndex:2}).add(i.group),!this.baseline&&(this.fontMetrics=n.fontMetrics(x),this.baseline=this.fontMetrics.f+3+this.itemMarginTop,x.attr("y",this.baseline),this.symbolHeight=sQ(r.symbolHeight,this.fontMetrics.f),r.squareSymbol&&(this.symbolWidth=sQ(r.symbolWidth,Math.max(this.symbolHeight,16)),b=this.symbolWidth+h+c+20*!!v,p&&x.attr("x",this.symbolWidth+h))),f.drawLegendSymbol(this,t),this.setItemEvents&&this.setItemEvents(t,x,m)),v&&!t.checkbox&&this.createCheckboxForItem&&this.createCheckboxForItem(t),this.colorizeItem(t,t.visible),(o.styledMode||!l.width)&&x.css({width:(r.itemWidth||this.widthOption||o.spacingBox.width)-b+"px"}),this.setText(t);var k=x.getBBox(),M=(null===(e=this.fontMetrics)||void 0===e?void 0:e.h)||0;t.itemWidth=t.checkboxOffset=r.itemWidth||i.labelWidth||k.width+b,this.maxItemWidth=Math.max(this.maxItemWidth,t.itemWidth),this.totalItemWidth+=t.itemWidth,this.itemHeight=t.itemHeight=Math.round(i.labelHeight||(k.height>1.5*M?k.height:M))},t.prototype.layoutItem=function(t){var e=this.options,i=this.padding,o="horizontal"===e.layout,n=t.itemHeight,r=this.itemMarginBottom,s=this.itemMarginTop,a=o?sQ(e.itemDistance,20):0,h=this.maxLegendWidth,l=e.alignColumns&&this.totalItemWidth>h?this.maxItemWidth:t.itemWidth,d=t.legendItem||{};o&&this.itemX-i+l>h&&(this.itemX=i,this.lastLineHeight&&(this.itemY+=s+this.lastLineHeight+r),this.lastLineHeight=0),this.lastItemY=s+this.itemY+r,this.lastLineHeight=Math.max(n,this.lastLineHeight),d.x=this.itemX,d.y=this.itemY,o?this.itemX+=l:(this.itemY+=s+n+r,this.lastLineHeight=n),this.offsetWidth=this.widthOption||Math.max((o?this.itemX-i-(t.checkbox?0:a):l)+i,this.offsetWidth)},t.prototype.getAllItems=function(){var t=[];return this.chart.series.forEach(function(e){var i,o=null==e?void 0:e.options;e&&sQ(o.showInLegend,!sV(o.linkedTo)&&void 0,!0)&&(t=t.concat((null===(i=e.legendItem)||void 0===i?void 0:i.labels)||("point"===o.legendType?e.data:e)))}),sK(this,"afterGetAllItems",{allItems:t}),t},t.prototype.getAlignment=function(){var t=this.options;return this.proximate?t.align.charAt(0)+"tv":t.floating?"":t.align.charAt(0)+t.verticalAlign.charAt(0)+t.layout.charAt(0)},t.prototype.adjustMargins=function(t,e){var i=this.chart,o=this.options,n=this.getAlignment();n&&[/(lth|ct|rth)/,/(rtv|rm|rbv)/,/(rbh|cb|lbh)/,/(lbv|lm|ltv)/].forEach(function(r,s){r.test(n)&&!sV(t[s])&&(i[sH[s]]=Math.max(i[sH[s]],i.legend[(s+1)%2?"legendHeight":"legendWidth"]+[1,-1,-1,1][s]*o[s%2?"x":"y"]+sQ(o.margin,12)+e[s]+(i.titleOffset[s]||0)))})},t.prototype.proximatePositions=function(){var t,e=this.chart,i=[],o="left"===this.options.align;this.allItems.forEach(function(t){var n,r,s,a,h=o;t.yAxis&&(t.xAxis.options.reversed&&(h=!h),t.points&&(n=sq(h?t.points:t.points.slice(0).reverse(),function(t){return s$(t.plotY)})),r=this.itemMarginTop+t.legendItem.label.getBBox().height+this.itemMarginBottom,a=t.yAxis.top-e.plotTop,s=t.visible?(n?n.plotY:t.yAxis.height)+(a-.3*r):a+t.yAxis.height,i.push({target:s,size:r,item:t}))},this);for(var n=0,r=sF(i,e.plotHeight);n<r.length;n++){var s=r[n];t=s.item.legendItem||{},s$(s.pos)&&(t.y=e.plotTop-e.spacing[0]+s.pos)}},t.prototype.render=function(){var t,e,i,o,n=this.chart,r=n.renderer,s=this.options,a=this.padding,h=this.getAllItems(),l=this.group,d=this.box;this.itemX=a,this.itemY=this.initialItemY,this.offsetWidth=0,this.lastItemY=0,this.widthOption=s1(s.width,n.spacingBox.width-a),o=n.spacingBox.width-2*a-s.x,["rm","lm"].indexOf(this.getAlignment().substring(0,2))>-1&&(o/=2),this.maxLegendWidth=this.widthOption||o,l||(this.group=l=r.g("legend").addClass(s.className||"").attr({zIndex:7}).add(),this.contentGroup=r.g().attr({zIndex:1}).add(l),this.scrollGroup=r.g().add(this.contentGroup)),this.renderTitle(),s2(h,function(t,e){var i,o;return((null===(i=t.options)||void 0===i?void 0:i.legendIndex)||0)-((null===(o=e.options)||void 0===o?void 0:o.legendIndex)||0)}),s.reversed&&h.reverse(),this.allItems=h,this.display=t=!!h.length,this.lastLineHeight=0,this.maxItemWidth=0,this.totalItemWidth=0,this.itemHeight=0,h.forEach(this.renderItem,this),h.forEach(this.layoutItem,this),e=(this.widthOption||this.offsetWidth)+a,i=this.lastItemY+this.lastLineHeight+this.titleHeight,i=this.handleOverflow(i)+a,d||(this.box=d=r.rect().addClass("highcharts-legend-box").attr({r:s.borderRadius}).add(l)),n.styledMode||d.attr({stroke:s.borderColor,"stroke-width":s.borderWidth||0,fill:s.backgroundColor||"none"}).shadow(s.shadow),e>0&&i>0&&d[d.placed?"animate":"attr"](d.crisp.call({},{x:0,y:0,width:e,height:i},d.strokeWidth())),l[t?"show":"hide"](),n.styledMode&&"none"===l.getStyle("display")&&(e=i=0),this.legendWidth=e,this.legendHeight=i,t&&this.align(),this.proximate||this.positionItems(),sK(this,"afterRender")},t.prototype.align=function(t){void 0===t&&(t=this.chart.spacingBox);var e=this.chart,i=this.options,o=t.y;/(lth|ct|rth)/.test(this.getAlignment())&&e.titleOffset[0]>0?o+=e.titleOffset[0]:/(lbh|cb|rbh)/.test(this.getAlignment())&&e.titleOffset[2]>0&&(o-=e.titleOffset[2]),o!==t.y&&(t=sJ(t,{y:o})),e.hasRendered||(this.group.placed=!1),this.group.align(sJ(i,{width:this.legendWidth,height:this.legendHeight,verticalAlign:this.proximate?"top":i.verticalAlign}),!0,t)},t.prototype.handleOverflow=function(t){var e,i,o,n,r=this,s=this.chart,a=s.renderer,h=this.options,l=h.y,d="top"===h.verticalAlign,c=this.padding,p=h.maxHeight,u=h.navigation,f=sQ(u.animation,!0),g=u.arrowSize||12,v=this.pages,m=this.allItems,y=function(t){"number"==typeof t?M.attr({height:t}):M&&(r.clipRect=M.destroy(),r.contentGroup.clip()),r.contentGroup.div&&(r.contentGroup.div.style.clip=t?"rect("+c+"px,9999px,"+(c+t)+"px,0)":"auto")},x=function(t){return r[t]=a.circle(0,0,1.3*g).translate(g/2,g/2).add(k),s.styledMode||r[t].attr("fill","rgba(0,0,0,0.0001)"),r[t]},b=s.spacingBox.height+(d?-l:l)-c,k=this.nav,M=this.clipRect;return"horizontal"!==h.layout||"middle"===h.verticalAlign||h.floating||(b/=2),p&&(b=Math.min(b,p)),v.length=0,t&&b>0&&t>b&&!1!==u.enabled?(this.clipHeight=e=Math.max(b-20-this.titleHeight-c,0),this.currentPage=sQ(this.currentPage,1),this.fullHeight=t,m.forEach(function(t,r){var s=(o=t.legendItem||{}).y||0,a=Math.round(o.label.getBBox().height),h=v.length;(!h||s-v[h-1]>e&&(i||s)!==v[h-1])&&(v.push(i||s),h++),o.pageIx=h-1,i&&n&&(n.pageIx=h-1),r===m.length-1&&s+a-v[h-1]>e&&s>v[h-1]&&(v.push(s),o.pageIx=h),s!==i&&(i=s),n=o}),M||(M=r.clipRect=a.clipRect(0,c-2,9999,0),r.contentGroup.clip(M)),y(e),k||(this.nav=k=a.g().attr({zIndex:1}).add(this.group),this.up=a.symbol("triangle",0,0,g,g).add(k),x("upTracker").on("click",function(){r.scroll(-1,f)}),this.pager=a.text("",15,10).addClass("highcharts-legend-navigation"),!s.styledMode&&u.style&&this.pager.css(u.style),this.pager.add(k),this.down=a.symbol("triangle-down",0,0,g,g).add(k),x("downTracker").on("click",function(){r.scroll(1,f)})),r.scroll(0),t=b):k&&(y(),this.nav=k.destroy(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),t},t.prototype.scroll=function(t,e){var i=this,o=this.chart,n=this.pages,r=n.length,s=this.clipHeight,a=this.options.navigation,h=this.pager,l=this.padding,d=this.currentPage+t;d>r&&(d=r),d>0&&(void 0!==e&&eI(e,o),this.nav.attr({translateX:l,translateY:s+this.padding+7+this.titleHeight,visibility:"inherit"}),[this.up,this.upTracker].forEach(function(t){t.attr({class:1===d?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})}),h.attr({text:d+"/"+r}),[this.down,this.downTracker].forEach(function(t){t.attr({x:18+this.pager.getBBox().width,class:d===r?"highcharts-legend-nav-inactive":"highcharts-legend-nav-active"})},this),o.styledMode||(this.up.attr({fill:1===d?a.inactiveColor:a.activeColor}),this.upTracker.css({cursor:1===d?"default":"pointer"}),this.down.attr({fill:d===r?a.inactiveColor:a.activeColor}),this.downTracker.css({cursor:d===r?"default":"pointer"})),this.scrollOffset=-n[d-1]+this.initialItemY,this.scrollGroup.animate({translateY:this.scrollOffset}),this.currentPage=d,this.positionCheckboxes(),s3(function(){sK(i,"afterScroll",{currentPage:d})},eE(sQ(e,o.renderer.globalAnimation,!0)).duration))},t.prototype.setItemEvents=function(t,e,i){for(var o=this,n=t.legendItem||{},r=o.chart.renderer.boxWrapper,s=t instanceof rL,a=t instanceof sW,h="highcharts-legend-"+(s?"point":"series")+"-active",l=o.chart.styledMode,d=i?[e,n.symbol]:[n.group],c=function(e){o.allItems.forEach(function(i){t!==i&&[i].concat(i.linkedSeries||[]).forEach(function(t){t.setState(e,!s)})})},p=0;p<d.length;p++){var u=d[p];u&&u.on("mouseover",function(){t.visible&&c("inactive"),t.setState("hover"),t.visible&&r.addClass(h),l||e.css(o.options.itemHoverStyle)}).on("mouseout",function(){o.chart.styledMode||e.css(sJ(t.visible?o.itemStyle:o.itemHiddenStyle)),c(""),r.removeClass(h),t.setState()}).on("click",function(e){var i=function(){t.setVisible&&t.setVisible(),c(t.visible?"inactive":"")};r.removeClass(h),sK(o,"itemClick",{browserEvent:e,legendItem:t},i),s?t.firePointEvent("legendItemClick",{browserEvent:e}):a&&sK(t,"legendItemClick",{browserEvent:e})})}},t.prototype.createCheckboxForItem=function(t){t.checkbox=sj("input",{type:"checkbox",className:"highcharts-legend-checkbox",checked:t.selected,defaultChecked:t.selected},this.options.itemCheckboxStyle,this.chart.container),s_(t.checkbox,"click",function(e){var i=e.target;sK(t.series||t,"checkboxClick",{checked:i.checked,item:t},function(){t.select()})})},t}();(y=s5||(s5={})).compose=function(t){s0(sX,"Core.Legend")&&s_(t,"beforeMargins",function(){this.legend=new y(this,this.options.legend)})};var s6=s5,s9=function(){return(s9=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},s4=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},s8=e8.numberFormat,s7=o$.registerEventOptions,at=tv.charts,ae=tv.doc,ai=tv.marginNames,ao=tv.svg,an=tv.win,ar=ss.seriesTypes,as=tF.addEvent,aa=tF.attr,ah=tF.createElement,al=tF.css,ad=tF.defined,ac=tF.diffObjects,ap=tF.discardElement,au=tF.erase,af=tF.error,ag=tF.extend,av=tF.find,am=tF.fireEvent,ay=tF.getAlignFactor,ax=tF.getStyle,ab=tF.isArray,ak=tF.isNumber,aM=tF.isObject,aw=tF.isString,aS=tF.merge,aA=tF.objectEach,aT=tF.pick,aC=tF.pInt,aO=tF.relativeLength,aP=tF.removeEvent,aE=tF.splat,aL=tF.syncTimeout,aB=tF.uniqueKey,aD=function(){function t(t,e,i){this.sharedClips={};var o=s4([],arguments,!0);(aw(t)||t.nodeName)&&(this.renderTo=o.shift()),this.init(o[0],o[1])}return t.chart=function(e,i,o){return new t(e,i,o)},t.prototype.setZoomOptions=function(){var t=this.options.chart,e=t.zooming;this.zooming=s9(s9({},e),{type:aT(t.zoomType,e.type),key:aT(t.zoomKey,e.key),pinchType:aT(t.pinchType,e.pinchType),singleTouch:aT(t.zoomBySingleTouch,e.singleTouch,!1),resetButton:aS(e.resetButton,t.resetZoomButton)})},t.prototype.init=function(t,e){am(this,"init",{args:arguments},function(){var i,o,n=aS(en,t),r=n.chart,s=this.renderTo||r.renderTo;this.userOptions=ag({},t),(this.renderTo=aw(s)?ae.getElementById(s):s)||af(13,!0,this),this.margin=[],this.spacing=[],this.labelCollectors=[],this.callback=e,this.isResizing=0,this.options=n,this.axes=[],this.series=[],this.locale=null!==(i=n.lang.locale)&&void 0!==i?i:null===(o=this.renderTo.closest("[lang]"))||void 0===o?void 0:o.lang,this.time=new t7(ag(n.time||{},{locale:this.locale}),n.lang),n.time=this.time.options,this.numberFormatter=(r.numberFormatter||s8).bind(this),this.styledMode=r.styledMode,this.hasCartesianSeries=r.showAxes,this.index=at.length,at.push(this),tv.chartCount++,s7(this,r),this.xAxis=[],this.yAxis=[],this.pointCount=this.colorCounter=this.symbolCounter=0,this.setZoomOptions(),am(this,"afterInit"),this.firstRender()})},t.prototype.initSeries=function(t){var e=this.options.chart,i=t.type||e.type,o=ar[i];o||af(17,!0,this,{missingModuleFor:i});var n=new o;return"function"==typeof n.init&&n.init(this,t),n},t.prototype.setSortedData=function(){this.getSeriesOrderByLinks().forEach(function(t){t.points||t.data||!t.enabledDataSorting||t.setData(t.options.data,!1)})},t.prototype.getSeriesOrderByLinks=function(){return this.series.concat().sort(function(t,e){return t.linkedSeries.length||e.linkedSeries.length?e.linkedSeries.length-t.linkedSeries.length:0})},t.prototype.orderItems=function(t,e){void 0===e&&(e=0);var i=this[t],o=this.options[t]=aE(this.options[t]).slice(),n=this.userOptions[t]=this.userOptions[t]?aE(this.userOptions[t]).slice():[];if(this.hasRendered&&(o.splice(e),n.splice(e)),i)for(var r=e,s=i.length;r<s;++r){var a=i[r];a&&(a.index=r,a instanceof sW&&(a.name=a.getName()),a.options.isInternal||(o[r]=a.options,n[r]=a.userOptions))}},t.prototype.getClipBox=function(t,e){var i,o,n,r,s,a=this.inverted,h=t||{},l=h.xAxis,d=h.yAxis,c=aS(this.clipBox),p=c.x,u=c.y,f=c.width,g=c.height;return t&&(l&&l.len!==this.plotSizeX&&(f=l.len),d&&d.len!==this.plotSizeY&&(g=d.len),a&&!t.invertible&&(f=(i=[g,f])[0],g=i[1])),e&&(p+=null!==(n=null===(o=a?d:l)||void 0===o?void 0:o.pos)&&void 0!==n?n:this.plotLeft,u+=null!==(s=null===(r=a?l:d)||void 0===r?void 0:r.pos)&&void 0!==s?s:this.plotTop),{x:p,y:u,width:f,height:g}},t.prototype.isInsidePlot=function(t,e,i){void 0===i&&(i={});var o,n=this.inverted,r=this.plotBox,s=this.plotLeft,a=this.plotTop,h=this.scrollablePlotBox,l=i.visiblePlotOnly&&(null===(o=this.scrollablePlotArea)||void 0===o?void 0:o.scrollingContainer)||{},d=l.scrollLeft,c=void 0===d?0:d,p=l.scrollTop,u=void 0===p?0:p,f=i.series,g=i.visiblePlotOnly&&h||r,v=i.inverted?e:t,m=i.inverted?t:e,y={x:v,y:m,isInsidePlot:!0,options:i};if(!i.ignoreX){var x=f&&(n&&!this.polar?f.yAxis:f.xAxis)||{pos:s,len:1/0},b=i.paneCoordinates?x.pos+v:s+v;b>=Math.max(c+s,x.pos)&&b<=Math.min(c+s+g.width,x.pos+x.len)||(y.isInsidePlot=!1)}if(!i.ignoreY&&y.isInsidePlot){var k=!n&&i.axis&&!i.axis.isXAxis&&i.axis||f&&(n?f.xAxis:f.yAxis)||{pos:a,len:1/0},M=i.paneCoordinates?k.pos+m:a+m;M>=Math.max(u+a,k.pos)&&M<=Math.min(u+a+g.height,k.pos+k.len)||(y.isInsidePlot=!1)}return am(this,"afterIsInsidePlot",y),y.isInsidePlot},t.prototype.redraw=function(t){am(this,"beforeRedraw");var e,i,o,n,r=this.hasCartesianSeries?this.axes:this.colorAxis||[],s=this.series,a=this.pointer,h=this.legend,l=this.userOptions.legend,d=this.renderer,c=d.isHidden(),p=[],u=this.isDirtyBox,f=this.isDirtyLegend;for(d.rootFontSize=d.boxWrapper.getStyle("font-size"),this.setResponsive&&this.setResponsive(!1),eI(!!this.hasRendered&&t,this),c&&this.temporaryDisplay(),this.layOutTitles(!1),o=s.length;o--;)if(((n=s[o]).options.stacking||n.options.centerInCategory)&&(i=!0,n.isDirty)){e=!0;break}if(e)for(o=s.length;o--;)(n=s[o]).options.stacking&&(n.isDirty=!0);s.forEach(function(t){t.isDirty&&("point"===t.options.legendType?("function"==typeof t.updateTotals&&t.updateTotals(),f=!0):l&&(l.labelFormatter||l.labelFormat)&&(f=!0)),t.isDirtyData&&am(t,"updatedData")}),f&&h&&h.options.enabled&&(h.render(),this.isDirtyLegend=!1),i&&this.getStacks(),r.forEach(function(t){t.updateNames(),t.setScale()}),this.getMargins(),r.forEach(function(t){t.isDirty&&(u=!0)}),r.forEach(function(t){var e=t.min+","+t.max;t.extKey!==e&&(t.extKey=e,p.push(function(){am(t,"afterSetExtremes",ag(t.eventArgs,t.getExtremes())),delete t.eventArgs})),(u||i)&&t.redraw()}),u&&this.drawChartBox(),am(this,"predraw"),s.forEach(function(t){(u||t.isDirty)&&t.visible&&t.redraw(),t.isDirtyData=!1}),a&&a.reset(!0),d.draw(),am(this,"redraw"),am(this,"render"),c&&this.temporaryDisplay(!0),p.forEach(function(t){t.call()})},t.prototype.get=function(t){var e=this.series;function i(e){return e.id===t||e.options&&e.options.id===t}for(var o=av(this.axes,i)||av(this.series,i),n=0;!o&&n<e.length;n++)o=av(e[n].points||[],i);return o},t.prototype.createAxes=function(){var t=this.userOptions;am(this,"createAxes");for(var e=0,i=["xAxis","yAxis"];e<i.length;e++)for(var o=i[e],n=t[o]=aE(t[o]||{}),r=0;r<n.length;r++)new nP(this,n[r],o);am(this,"afterCreateAxes")},t.prototype.getSelectedPoints=function(){return this.series.reduce(function(t,e){return e.getPointsCollection().forEach(function(e){aT(e.selectedStaging,e.selected)&&t.push(e)}),t},[])},t.prototype.getSelectedSeries=function(){return this.series.filter(function(t){return t.selected})},t.prototype.setTitle=function(t,e,i){this.applyDescription("title",t),this.applyDescription("subtitle",e),this.applyDescription("caption",void 0),this.layOutTitles(i)},t.prototype.applyDescription=function(t,e){var i,o=this,n=this.options[t]=aS(this.options[t],e),r=this[t];r&&e&&(this[t]=r=r.destroy()),n&&!r&&((r=this.renderer.text(n.text,0,0,n.useHTML).attr({align:n.align,class:"highcharts-"+t,zIndex:n.zIndex||4}).css({textOverflow:"ellipsis",whiteSpace:"nowrap"}).add()).update=function(e,i){o.applyDescription(t,e),o.layOutTitles(i)},this.styledMode||r.css(ag("title"===t?{fontSize:this.options.isStock?"1em":"1.2em"}:{},n.style)),r.textPxLength=r.getBBox().width,r.css({whiteSpace:null===(i=n.style)||void 0===i?void 0:i.whiteSpace}),this[t]=r)},t.prototype.layOutTitles=function(t){var e,i,o,n,r=this;void 0===t&&(t=!0);var s=[0,0,0],a=this.options,h=this.renderer,l=this.spacingBox;["title","subtitle","caption"].forEach(function(t){var e,i=r[t],o=r.options[t],n=aS(l),a=(null==i?void 0:i.textPxLength)||0;if(i&&o){am(r,"layOutTitle",{alignTo:n,key:t,textPxLength:a});var d=h.fontMetrics(i),c=d.b,p=d.h,u=o.verticalAlign||"top",f="top"===u,g=f&&o.minScale||1,v="title"===t?f?-3:0:f?s[0]+2:0,m=Math.min(n.width/a,1),y=Math.max(g,m),x=aS({y:"bottom"===u?c:v+c},{align:"title"===t?m<g?"left":"center":null===(e=r.title)||void 0===e?void 0:e.alignValue},o),b=(o.width||(m>g?r.chartWidth:n.width)/y)+"px";i.alignValue!==x.align&&(i.placed=!1);var k=Math.round(i.css({width:b}).getBBox(o.useHTML).height);if(x.height=k,i.align(x,!1,n).attr({align:x.align,scaleX:y,scaleY:y,"transform-origin":""+(n.x+a*y*ay(x.align))+" ".concat(p)}),!o.floating){var M=k*(k<1.2*p?1:y);"top"===u?s[0]=Math.ceil(s[0]+M):"bottom"===u&&(s[2]=Math.ceil(s[2]+M))}}},this),s[0]&&"top"===((null===(e=a.title)||void 0===e?void 0:e.verticalAlign)||"top")&&(s[0]+=(null===(i=a.title)||void 0===i?void 0:i.margin)||0),s[2]&&(null===(o=a.caption)||void 0===o?void 0:o.verticalAlign)==="bottom"&&(s[2]+=(null===(n=a.caption)||void 0===n?void 0:n.margin)||0);var d=!this.titleOffset||this.titleOffset.join(",")!==s.join(",");this.titleOffset=s,am(this,"afterLayOutTitles"),!this.isDirtyBox&&d&&(this.isDirtyBox=this.isDirtyLegend=d,this.hasRendered&&t&&this.isDirtyBox&&this.redraw())},t.prototype.getContainerBox=function(){var t=this,e=[].map.call(this.renderTo.children,function(e){if(e!==t.container){var i=e.style.display;return e.style.display="none",[e,i]}}),i={width:ax(this.renderTo,"width",!0)||0,height:ax(this.renderTo,"height",!0)||0};return e.filter(Boolean).forEach(function(t){var e=t[0],i=t[1];e.style.display=i}),i},t.prototype.getChartSize=function(){var t,e=this.options.chart,i=e.width,o=e.height,n=this.getContainerBox(),r=n.height<=1||!(null===(t=this.renderTo.parentElement)||void 0===t?void 0:t.style.height)&&"100%"===this.renderTo.style.height;this.chartWidth=Math.max(0,i||n.width||600),this.chartHeight=Math.max(0,aO(o,this.chartWidth)||(r?400:n.height)),this.containerBox=n},t.prototype.temporaryDisplay=function(t){var e,i=this.renderTo;if(t)for(;null==i?void 0:i.style;)i.hcOrigStyle&&(al(i,i.hcOrigStyle),delete i.hcOrigStyle),i.hcOrigDetached&&(ae.body.removeChild(i),i.hcOrigDetached=!1),i=i.parentNode;else for(;(null==i?void 0:i.style)&&(ae.body.contains(i)||i.parentNode||(i.hcOrigDetached=!0,ae.body.appendChild(i)),("none"===ax(i,"display",!1)||i.hcOricDetached)&&(i.hcOrigStyle={display:i.style.display,height:i.style.height,overflow:i.style.overflow},e={display:"block",overflow:"hidden"},i!==this.renderTo&&(e.height=0),al(i,e),i.offsetWidth||i.style.setProperty("display","block","important")),(i=i.parentNode)!==ae.body););},t.prototype.setClassName=function(t){this.container.className="highcharts-container "+(t||"")},t.prototype.getContainer=function(){var t,e,i,o=this.options,n=o.chart,r="data-highcharts-chart",s=aB(),a=this.renderTo,h=aC(aa(a,r));ak(h)&&at[h]&&at[h].hasRendered&&at[h].destroy(),aa(a,r,this.index),a.innerHTML=eZ.emptyHTML,n.skipClone||a.offsetWidth||this.temporaryDisplay(),this.getChartSize();var l=this.chartHeight,d=this.chartWidth;al(a,{overflow:"hidden"}),this.styledMode||(i=ag({position:"relative",overflow:"hidden",width:d+"px",height:l+"px",textAlign:"left",lineHeight:"normal",zIndex:0,"-webkit-tap-highlight-color":"rgba(0,0,0,0)",userSelect:"none","touch-action":"manipulation",outline:"none",padding:"0px"},n.style||{}));var c=ah("div",{id:s},i,a);this.container=c,this.getChartSize(),d===this.chartWidth||(d=this.chartWidth,this.styledMode||al(c,{width:aT(null===(t=n.style)||void 0===t?void 0:t.width,d+"px")})),this.containerBox=this.getContainerBox(),this._cursor=c.style.cursor;var p=n.renderer||!ao?e7.getRendererType(n.renderer):oO;if(this.renderer=new p(c,d,l,void 0,n.forExport,null===(e=o.exporting)||void 0===e?void 0:e.allowHTML,this.styledMode),eI(void 0,this),this.setClassName(n.className),this.styledMode)for(var u in o.defs)this.renderer.definition(o.defs[u]);else this.renderer.setStyle(n.style);this.renderer.chartIndex=this.index,am(this,"afterGetContainer")},t.prototype.getMargins=function(t){var e,i=this.spacing,o=this.margin,n=this.titleOffset;this.resetMargins(),n[0]&&!ad(o[0])&&(this.plotTop=Math.max(this.plotTop,n[0]+i[0])),n[2]&&!ad(o[2])&&(this.marginBottom=Math.max(this.marginBottom,n[2]+i[2])),(null===(e=this.legend)||void 0===e?void 0:e.display)&&this.legend.adjustMargins(o,i),am(this,"getMargins"),t||this.getAxisMargins()},t.prototype.getAxisMargins=function(){var t=this,e=t.axisOffset=[0,0,0,0],i=t.colorAxis,o=t.margin,n=function(t){t.forEach(function(t){t.visible&&t.getOffset()})};t.hasCartesianSeries?n(t.axes):(null==i?void 0:i.length)&&n(i),ai.forEach(function(i,n){ad(o[n])||(t[i]+=e[n])}),t.setChartSize()},t.prototype.getOptions=function(){return ac(this.userOptions,en)},t.prototype.reflow=function(t){var e,i=this,o=i.containerBox,n=i.getContainerBox();null===(e=i.pointer)||void 0===e||delete e.chartPosition,!i.isPrinting&&!i.isResizing&&o&&n.width&&((n.width!==o.width||n.height!==o.height)&&(tF.clearTimeout(i.reflowTimeout),i.reflowTimeout=aL(function(){i.container&&i.setSize(void 0,void 0,!1)},100*!!t)),i.containerBox=n)},t.prototype.setReflow=function(){var t=this,e=function(e){var i;(null===(i=t.options)||void 0===i?void 0:i.chart.reflow)&&t.hasLoaded&&t.reflow(e)};if("function"==typeof ResizeObserver)new ResizeObserver(e).observe(t.renderTo);else{var i=as(an,"resize",e);as(this,"destroy",i)}},t.prototype.setSize=function(t,e,i){var o=this,n=o.renderer;o.isResizing+=1,eI(i,o);var r=n.globalAnimation;o.oldChartHeight=o.chartHeight,o.oldChartWidth=o.chartWidth,void 0!==t&&(o.options.chart.width=t),void 0!==e&&(o.options.chart.height=e),o.getChartSize();var s=o.chartWidth,a=o.chartHeight,h=o.scrollablePixelsX,l=o.scrollablePixelsY;(o.isDirtyBox||s!==o.oldChartWidth||a!==o.oldChartHeight)&&(o.styledMode||(r?eB:al)(o.container,{width:""+(s+(void 0===h?0:h))+"px",height:""+(a+(void 0===l?0:l))+"px"},r),o.setChartSize(!0),n.setSize(s,a,r),o.axes.forEach(function(t){t.isDirty=!0,t.setScale()}),o.isDirtyLegend=!0,o.isDirtyBox=!0,o.layOutTitles(),o.getMargins(),o.redraw(r),o.oldChartHeight=void 0,am(o,"resize"),setTimeout(function(){o&&am(o,"endResize")},eE(r).duration)),o.isResizing-=1},t.prototype.setChartSize=function(t){var e,i,o,n,r,s,a=this.chartHeight,h=this.chartWidth,l=this.inverted,d=this.spacing,c=this.renderer,p=this.clipOffset,u=Math[l?"floor":"round"];this.plotLeft=o=Math.round(this.plotLeft),this.plotTop=n=Math.round(this.plotTop),this.plotWidth=r=Math.max(0,Math.round(h-o-(null!==(e=this.marginRight)&&void 0!==e?e:0))),this.plotHeight=s=Math.max(0,Math.round(a-n-(null!==(i=this.marginBottom)&&void 0!==i?i:0))),this.plotSizeX=l?s:r,this.plotSizeY=l?r:s,this.spacingBox=c.spacingBox={x:d[3],y:d[0],width:h-d[3]-d[1],height:a-d[0]-d[2]},this.plotBox=c.plotBox={x:o,y:n,width:r,height:s},p&&(this.clipBox={x:u(p[3]),y:u(p[0]),width:u(this.plotSizeX-p[1]-p[3]),height:u(this.plotSizeY-p[0]-p[2])}),t||(this.axes.forEach(function(t){t.setAxisSize(),t.setAxisTranslation()}),c.alignElements()),am(this,"afterSetChartSize",{skipAxes:t})},t.prototype.resetMargins=function(){am(this,"resetMargins");var t=this,e=t.options.chart,i=e.plotBorderWidth||0,o=Math.round(i)/2;["margin","spacing"].forEach(function(i){var o=e[i],n=aM(o)?o:[o,o,o,o];["Top","Right","Bottom","Left"].forEach(function(o,r){t[i][r]=aT(e[i+o],n[r])})}),ai.forEach(function(e,i){t[e]=aT(t.margin[i],t.spacing[i])}),t.axisOffset=[0,0,0,0],t.clipOffset=[o,o,o,o],t.plotBorderWidth=i},t.prototype.drawChartBox=function(){var t,e,i,o=this.options.chart,n=this.renderer,r=this.chartWidth,s=this.chartHeight,a=this.styledMode,h=this.plotBGImage,l=o.backgroundColor,d=o.plotBackgroundColor,c=o.plotBackgroundImage,p=this.plotLeft,u=this.plotTop,f=this.plotWidth,g=this.plotHeight,v=this.plotBox,m=this.clipRect,y=this.clipBox,x=this.chartBackground,b=this.plotBackground,k=this.plotBorder,M="animate";x||(this.chartBackground=x=n.rect().addClass("highcharts-background").add(),M="attr"),a?t=e=x.strokeWidth():(e=(t=o.borderWidth||0)+8*!!o.shadow,i={fill:l||"none"},(t||x["stroke-width"])&&(i.stroke=o.borderColor,i["stroke-width"]=t),x.attr(i).shadow(o.shadow)),x[M]({x:e/2,y:e/2,width:r-e-t%2,height:s-e-t%2,r:o.borderRadius}),M="animate",b||(M="attr",this.plotBackground=b=n.rect().addClass("highcharts-plot-background").add()),b[M](v),!a&&(b.attr({fill:d||"none"}).shadow(o.plotShadow),c&&(h?(c!==h.attr("href")&&h.attr("href",c),h.animate(v)):this.plotBGImage=n.image(c,p,u,f,g).add())),m?m.animate({width:y.width,height:y.height}):this.clipRect=n.clipRect(y),M="animate",k||(M="attr",this.plotBorder=k=n.rect().addClass("highcharts-plot-border").attr({zIndex:1}).add()),a||k.attr({stroke:o.plotBorderColor,"stroke-width":o.plotBorderWidth||0,fill:"none"}),k[M](k.crisp(v,-k.strokeWidth())),this.isDirtyBox=!1,am(this,"afterDrawChartBox")},t.prototype.propFromSeries=function(){var t,e,i,o=this,n=o.options.chart,r=o.options.series;["inverted","angular","polar"].forEach(function(s){for(e=ar[n.type],i=n[s]||e&&e.prototype[s],t=null==r?void 0:r.length;!i&&t--;)(e=ar[r[t].type])&&e.prototype[s]&&(i=!0);o[s]=i})},t.prototype.linkSeries=function(t){var e=this,i=e.series;i.forEach(function(t){t.linkedSeries.length=0}),i.forEach(function(t){var i=t.options.linkedTo;if(aw(i)){var o=void 0;(o=":previous"===i?e.series[t.index-1]:e.get(i))&&o.linkedParent!==t&&(o.linkedSeries.push(t),t.linkedParent=o,o.enabledDataSorting&&t.setDataSortingOptions(),t.visible=aT(t.options.visible,o.options.visible,t.visible))}}),am(this,"afterLinkSeries",{isUpdating:t})},t.prototype.renderSeries=function(){this.series.forEach(function(t){t.translate(),t.render()})},t.prototype.render=function(){var t,e,i=this.axes,o=this.colorAxis,n=this.renderer,r=this.options.chart.axisLayoutRuns||2,s=function(t){t.forEach(function(t){t.visible&&t.render()})},a=0,h=!0,l=0;this.setTitle(),am(this,"beforeMargins"),null===(t=this.getStacks)||void 0===t||t.call(this),this.getMargins(!0),this.setChartSize();for(var d=0;d<i.length;d++){var c=i[d],p=c.options,u=p.labels;if(this.hasCartesianSeries&&c.horiz&&c.visible&&u.enabled&&c.series.length&&"colorAxis"!==c.coll&&!this.polar){a=p.tickLength,c.createGroups();var f=new nt(c,0,"",!0),g=f.createLabel("x",u);if(f.destroy(),g&&aT(u.reserveSpace,!ak(p.crossing))&&(a=g.getBBox().height+u.distance+Math.max(p.offset||0,0)),a){null==g||g.destroy();break}}}for(this.plotHeight=Math.max(this.plotHeight-a,0);(h||e||r>1)&&l<r;){for(var v=this.plotWidth,m=this.plotHeight,y=0;y<i.length;y++){var c=i[y];0===l?c.setScale():(c.horiz&&h||!c.horiz&&e)&&c.setTickInterval(!0)}0===l?this.getAxisMargins():this.getMargins(),h=v/this.plotWidth>(l?1:1.1),e=m/this.plotHeight>(l?1:1.05),l++}this.drawChartBox(),this.hasCartesianSeries?s(i):(null==o?void 0:o.length)&&s(o),this.seriesGroup||(this.seriesGroup=n.g("series-group").attr({zIndex:3}).shadow(this.options.chart.seriesGroupShadow).add()),this.renderSeries(),this.addCredits(),this.setResponsive&&this.setResponsive(),this.hasRendered=!0},t.prototype.addCredits=function(t){var e=this,i=aS(!0,this.options.credits,t);i.enabled&&!this.credits&&(this.credits=this.renderer.text(i.text+(this.mapCredits||""),0,0).addClass("highcharts-credits").on("click",function(){i.href&&(an.location.href=i.href)}).attr({align:i.position.align,zIndex:8}),e.styledMode||this.credits.css(i.style),this.credits.add().align(i.position),this.credits.update=function(t){e.credits=e.credits.destroy(),e.addCredits(t)})},t.prototype.destroy=function(){var t,e,i,o=this,n=o.axes,r=o.series,s=o.container,a=null==s?void 0:s.parentNode;for(am(o,"destroy"),o.renderer.forExport?au(at,o):at[o.index]=void 0,tv.chartCount--,o.renderTo.removeAttribute("data-highcharts-chart"),aP(o),i=n.length;i--;)n[i]=n[i].destroy();for(null===(e=null===(t=this.scroller)||void 0===t?void 0:t.destroy)||void 0===e||e.call(t),i=r.length;i--;)r[i]=r[i].destroy();["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","rangeSelector","legend","resetZoomButton","tooltip","renderer"].forEach(function(t){var e,i;o[t]=null===(i=null===(e=o[t])||void 0===e?void 0:e.destroy)||void 0===i?void 0:i.call(e)}),s&&(s.innerHTML=eZ.emptyHTML,aP(s),a&&ap(s)),aA(o,function(t,e){delete o[e]})},t.prototype.firstRender=function(){var t,e=this,i=e.options;e.getContainer(),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.createAxes();var o=ab(i.series)?i.series:[];i.series=[],o.forEach(function(t){e.initSeries(t)}),e.linkSeries(),e.setSortedData(),am(e,"beforeRender"),e.render(),null===(t=e.pointer)||void 0===t||t.getChartPosition(),e.renderer.imgCount||e.hasLoaded||e.onload(),e.temporaryDisplay(!0)},t.prototype.onload=function(){this.callbacks.concat([this.callback]).forEach(function(t){t&&void 0!==this.index&&t.apply(this,[this])},this),am(this,"load"),am(this,"render"),ad(this.index)&&this.setReflow(),this.warnIfA11yModuleNotLoaded(),this.hasLoaded=!0},t.prototype.warnIfA11yModuleNotLoaded=function(){var t=this.options,e=this.title;!t||this.accessibility||(this.renderer.boxWrapper.attr({role:"img","aria-label":((null==e?void 0:e.element.textContent)||"").replace(/</g,"&lt;")}),t.accessibility&&!1===t.accessibility.enabled||af('Highcharts warning: Consider including the "accessibility.js" module to make your chart more usable for people with disabilities. Set the "accessibility.enabled" option to false to remove this warning. See https://www.highcharts.com/docs/accessibility/accessibility-module.',!1,this))},t.prototype.addSeries=function(t,e,i){var o,n=this;return t&&(e=aT(e,!0),am(n,"addSeries",{options:t},function(){o=n.initSeries(t),n.isDirtyLegend=!0,n.linkSeries(),o.enabledDataSorting&&o.setData(t.data,!1),am(n,"afterAddSeries",{series:o}),e&&n.redraw(i)})),o},t.prototype.addAxis=function(t,e,i,o){return this.createAxis(e?"xAxis":"yAxis",{axis:t,redraw:i,animation:o})},t.prototype.addColorAxis=function(t,e,i){return this.createAxis("colorAxis",{axis:t,redraw:e,animation:i})},t.prototype.createAxis=function(t,e){var i=new nP(this,e.axis,t);return aT(e.redraw,!0)&&this.redraw(e.animation),i},t.prototype.showLoading=function(t){var e=this,i=e.options,o=i.loading,n=function(){r&&al(r,{left:e.plotLeft+"px",top:e.plotTop+"px",width:e.plotWidth+"px",height:e.plotHeight+"px"})},r=e.loadingDiv,s=e.loadingSpan;r||(e.loadingDiv=r=ah("div",{className:"highcharts-loading highcharts-loading-hidden"},null,e.container)),s||(e.loadingSpan=s=ah("span",{className:"highcharts-loading-inner"},null,r),as(e,"redraw",n)),r.className="highcharts-loading",eZ.setElementHTML(s,aT(t,i.lang.loading,"")),e.styledMode||(al(r,ag(o.style,{zIndex:10})),al(s,o.labelStyle),e.loadingShown||(al(r,{opacity:0,display:""}),eB(r,{opacity:o.style.opacity||.5},{duration:o.showDuration||0}))),e.loadingShown=!0,n()},t.prototype.hideLoading=function(){var t=this.options,e=this.loadingDiv;e&&(e.className="highcharts-loading highcharts-loading-hidden",this.styledMode||eB(e,{opacity:0},{duration:t.loading.hideDuration||100,complete:function(){al(e,{display:"none"})}})),this.loadingShown=!1},t.prototype.update=function(t,e,i,o){var n,r,s,a=this,h={credits:"addCredits",title:"setTitle",subtitle:"setSubtitle",caption:"setCaption"},l=t.isResponsiveOptions,d=[];am(a,"update",{options:t}),l||a.setResponsive(!1,!0),t=ac(t,a.options),a.userOptions=aS(a.userOptions,t);var c=t.chart;c&&(aS(!0,a.options.chart,c),this.setZoomOptions(),"className"in c&&a.setClassName(c.className),("inverted"in c||"polar"in c||"type"in c)&&(a.propFromSeries(),n=!0),"alignTicks"in c&&(n=!0),"events"in c&&s7(this,c),aA(c,function(t,e){-1!==a.propsRequireUpdateSeries.indexOf("chart."+e)&&(r=!0),-1!==a.propsRequireDirtyBox.indexOf(e)&&(a.isDirtyBox=!0),-1===a.propsRequireReflow.indexOf(e)||(a.isDirtyBox=!0,l||(s=!0))}),!a.styledMode&&c.style&&a.renderer.setStyle(a.options.chart.style||{})),!a.styledMode&&t.colors&&(this.options.colors=t.colors),aA(t,function(e,i){a[i]&&"function"==typeof a[i].update?a[i].update(e,!1):"function"==typeof a[h[i]]?a[h[i]](e):"colors"!==i&&-1===a.collectionsWithUpdate.indexOf(i)&&aS(!0,a.options[i],t[i]),"chart"!==i&&-1!==a.propsRequireUpdateSeries.indexOf(i)&&(r=!0)}),this.collectionsWithUpdate.forEach(function(e){t[e]&&(aE(t[e]).forEach(function(t,o){var n,r=ad(t.id);r&&(n=a.get(t.id)),!n&&a[e]&&(n=a[e][aT(t.index,o)])&&(r&&ad(n.options.id)||n.options.isInternal)&&(n=void 0),n&&n.coll===e&&(n.update(t,!1),i&&(n.touched=!0)),!n&&i&&a.collectionsWithInit[e]&&(a.collectionsWithInit[e][0].apply(a,[t].concat(a.collectionsWithInit[e][1]||[]).concat([!1])).touched=!0)}),i&&a[e].forEach(function(t){t.touched||t.options.isInternal?delete t.touched:d.push(t)}))}),d.forEach(function(t){t.chart&&t.remove&&t.remove(!1)}),n&&a.axes.forEach(function(t){t.update({},!1)}),r&&a.getSeriesOrderByLinks().forEach(function(t){t.chart&&t.update({},!1)},this);var p=null==c?void 0:c.width,u=c&&(aw(c.height)?aO(c.height,p||a.chartWidth):c.height);s||ak(p)&&p!==a.chartWidth||ak(u)&&u!==a.chartHeight?a.setSize(p,u,o):aT(e,!0)&&a.redraw(o),am(a,"afterUpdate",{options:t,redraw:e,animation:o})},t.prototype.setSubtitle=function(t,e){this.applyDescription("subtitle",t),this.layOutTitles(e)},t.prototype.setCaption=function(t,e){this.applyDescription("caption",t),this.layOutTitles(e)},t.prototype.showResetZoom=function(){var t=this,e=en.lang,i=t.zooming.resetButton,o=i.theme,n="chart"===i.relativeTo||"spacingBox"===i.relativeTo?null:"plotBox";function r(){t.zoomOut()}am(this,"beforeShowResetZoom",null,function(){t.resetZoomButton=t.renderer.button(e.resetZoom,null,null,r,o).attr({align:i.position.align,title:e.resetZoomTitle}).addClass("highcharts-reset-zoom").add().align(i.position,!1,n)}),am(this,"afterShowResetZoom")},t.prototype.zoomOut=function(){var t=this;am(this,"selection",{resetSelection:!0},function(){return t.transform({reset:!0,trigger:"zoom"})})},t.prototype.pan=function(t,e){var i=this,o="object"==typeof e?e:{enabled:e,type:"x"},n=o.type,r=n&&i[({x:"xAxis",xy:"axes",y:"yAxis"})[n]].filter(function(t){return t.options.panningEnabled&&!t.options.isInternal}),s=i.options.chart;(null==s?void 0:s.panning)&&(s.panning=o),am(this,"pan",{originalEvent:t},function(){i.transform({axes:r,event:t,to:{x:t.chartX-(i.mouseDownX||0),y:t.chartY-(i.mouseDownY||0)},trigger:"pan"}),al(i.container,{cursor:"move"})})},t.prototype.transform=function(t){var e,i,o,n,r,s,a=this,h=t.axes,l=void 0===h?this.axes:h,d=t.event,c=t.from,p=void 0===c?{}:c,u=t.reset,f=t.selection,g=t.to,v=void 0===g?{}:g,m=t.trigger,y=this.inverted,x=this.time,b=!1;null===(i=this.hoverPoints)||void 0===i||i.forEach(function(t){return t.setState()});for(var k=0;k<l.length;k++){var M=l[k],w=M.horiz,S=M.len,A=M.minPointOffset,T=void 0===A?0:A,C=M.options,O=M.reversed,P=w?"width":"height",E=w?"x":"y",L=aT(v[P],M.len),B=aT(p[P],M.len),D=10>Math.abs(L)?1:L/B,I=(p[E]||0)+B/2-M.pos,z=I-((null!==(o=v[E])&&void 0!==o?o:M.pos)+L/2-M.pos)/D,R=O&&!y||!O&&y?-1:1;if(u||!(I<0)&&!(I>M.len)){var N=M.toValue(z,!0)+(f||M.isOrdinal?0:T*R),W=M.toValue(z+S/D,!0)-(f||M.isOrdinal?0:T*R||0),G=M.allExtremes;if(N>W&&(N=(e=[W,N])[0],W=e[1]),1===D&&!u&&"yAxis"===M.coll&&!G){for(var X=0,H=M.series;X<H.length;X++){var F=H[X],Y=F.getExtremes(F.getProcessedData(!0).modified.getColumn("y")||[],!0);null!=G||(G={dataMin:Number.MAX_VALUE,dataMax:-Number.MAX_VALUE}),ak(Y.dataMin)&&ak(Y.dataMax)&&(G.dataMin=Math.min(Y.dataMin,G.dataMin),G.dataMax=Math.max(Y.dataMax,G.dataMax))}M.allExtremes=G}var _=ag(M.getExtremes(),G||{}),j=_.dataMin,U=_.dataMax,V=_.min,Z=_.max,q=x.parse(C.min),K=x.parse(C.max),$=null!=j?j:q,J=null!=U?U:K,Q=W-N,tt=M.categories?0:Math.min(Q,J-$),te=$-tt*(ad(q)?0:C.minPadding),ti=J+tt*(ad(K)?0:C.maxPadding),to=M.allowZoomOutside||1===D||"zoom"!==m&&D>1,tn=Math.min(null!=q?q:te,te,to?V:te),tr=Math.max(null!=K?K:ti,ti,to?Z:ti);(!M.isOrdinal||M.options.overscroll||1!==D||u)&&(N<tn&&(N=tn,D>=1&&(W=N+Q)),W>tr&&(W=tr,D>=1&&(N=W-Q)),(u||M.series.length&&(N!==V||W!==Z)&&N>=tn&&W<=tr)&&(f?f[M.coll].push({axis:M,min:N,max:W}):(M.isPanning="zoom"!==m,M.isPanning&&(s=!0),M.setExtremes(u?void 0:N,u?void 0:W,!1,!1,{move:z,trigger:m,scale:D}),!u&&(N>tn||W<tr)&&"mousewheel"!==m&&(r=!0)),b=!0),d&&(this[w?"mouseDownX":"mouseDownY"]=d[w?"chartX":"chartY"]))}}return b&&(f?am(this,"selection",f,function(){delete t.selection,t.trigger="zoom",a.transform(t)}):(!r||s||this.resetZoomButton?!r&&this.resetZoomButton&&(this.resetZoomButton=this.resetZoomButton.destroy()):this.showResetZoom(),this.redraw("zoom"===m&&(null!==(n=this.options.chart.animation)&&void 0!==n?n:this.pointCount<100)))),b},t}();ag(aD.prototype,{callbacks:[],collectionsWithInit:{xAxis:[aD.prototype.addAxis,[!0]],yAxis:[aD.prototype.addAxis,[!1]],series:[aD.prototype.addSeries]},collectionsWithUpdate:["xAxis","yAxis","series"],propsRequireDirtyBox:["backgroundColor","borderColor","borderWidth","borderRadius","plotBackgroundColor","plotBackgroundImage","plotBorderColor","plotBorderWidth","plotShadow","shadow"],propsRequireReflow:["margin","marginTop","marginRight","marginBottom","marginLeft","spacing","spacingTop","spacingRight","spacingBottom","spacingLeft"],propsRequireUpdateSeries:["chart.inverted","chart.polar","chart.ignoreHiddenSeries","chart.type","colors","plotOptions","time","tooltip"]});var aI=tv.composed,az=tF.addEvent,aR=tF.createElement,aN=tF.css,aW=tF.defined,aG=tF.erase,aX=tF.merge,aH=tF.pushUnique;function aF(){var t=this.scrollablePlotArea;(this.scrollablePixelsX||this.scrollablePixelsY)&&!t&&(this.scrollablePlotArea=t=new a_(this)),null==t||t.applyFixed()}function aY(){this.chart.scrollablePlotArea&&(this.chart.scrollablePlotArea.isDirty=!0)}var a_=function(){function t(t){var e,i,o,n=t.options.chart,r=e7.getRendererType(),s=n.scrollablePlotArea||{},a=this.moveFixedElements.bind(this),h={WebkitOverflowScrolling:"touch",overflowX:"hidden",overflowY:"hidden"};t.scrollablePixelsX&&(h.overflowX="auto"),t.scrollablePixelsY&&(h.overflowY="auto"),this.chart=t;var l=this.parentDiv=aR("div",{className:"highcharts-scrolling-parent"},{position:"relative"},t.renderTo),d=this.scrollingContainer=aR("div",{className:"highcharts-scrolling"},h,l),c=this.innerContainer=aR("div",{className:"highcharts-inner-container"},void 0,d),p=this.fixedDiv=aR("div",{className:"highcharts-fixed"},{position:"absolute",overflow:"hidden",pointerEvents:"none",zIndex:((null===(e=n.style)||void 0===e?void 0:e.zIndex)||0)+2,top:0},void 0,!0),u=this.fixedRenderer=new r(p,t.chartWidth,t.chartHeight,n.style);this.mask=u.path().attr({fill:n.backgroundColor||"#fff","fill-opacity":null!==(i=s.opacity)&&void 0!==i?i:.85,zIndex:-1}).addClass("highcharts-scrollable-mask").add(),d.parentNode.insertBefore(p,d),aN(t.renderTo,{overflow:"visible"}),az(t,"afterShowResetZoom",a),az(t,"afterApplyDrilldown",a),az(t,"afterLayOutTitles",a),az(d,"scroll",function(){var e=t.pointer,i=t.hoverPoint;e&&(delete e.chartPosition,i&&(o=i),e.runPointActions(void 0,o,!0))}),c.appendChild(t.container)}return t.compose=function(t,e,i){var o=this;aH(aI,this.compose)&&(az(t,"afterInit",aY),az(e,"afterSetChartSize",function(t){return o.afterSetSize(t.target,t)}),az(e,"render",aF),az(i,"show",aY))},t.afterSetSize=function(t,e){var i,o,n,r=t.options.chart.scrollablePlotArea||{},s=r.minWidth,a=r.minHeight,h=t.clipBox,l=t.plotBox,d=t.inverted;if(!t.renderer.forExport&&(s?(t.scrollablePixelsX=i=Math.max(0,s-t.chartWidth),i&&(t.scrollablePlotBox=aX(t.plotBox),l.width=t.plotWidth+=i,h[d?"height":"width"]+=i,n=!0)):a&&(t.scrollablePixelsY=o=Math.max(0,a-t.chartHeight),aW(o)&&(t.scrollablePlotBox=aX(t.plotBox),l.height=t.plotHeight+=o,h[d?"width":"height"]+=o,n=!1)),aW(n)&&!e.skipAxes))for(var c=0,p=t.axes;c<p.length;c++){var u=p[c];(u.horiz===n||t.hasParallelCoordinates&&"yAxis"===u.coll)&&(u.setAxisSize(),u.setAxisTranslation())}},t.prototype.applyFixed=function(){var t,e=this.chart,i=this.fixedRenderer,o=this.isDirty,n=this.scrollingContainer,r=e.axisOffset,s=e.chartWidth,a=e.chartHeight,h=e.container,l=e.plotHeight,d=e.plotLeft,c=e.plotTop,p=e.plotWidth,u=e.scrollablePixelsX,f=void 0===u?0:u,g=e.scrollablePixelsY,v=void 0===g?0:g,m=e.options.chart.scrollablePlotArea||{},y=m.scrollPositionX,x=m.scrollPositionY,b=s+f,k=a+v;i.setSize(s,a),(null==o||o)&&(this.isDirty=!1,this.moveFixedElements()),eL(e.container),aN(h,{width:""+b+"px",height:""+k+"px"}),e.renderer.boxWrapper.attr({width:b,height:k,viewBox:[0,0,b,k].join(" ")}),null===(t=e.chartBackground)||void 0===t||t.attr({width:b,height:k}),aN(n,{width:""+s+"px",height:""+a+"px"}),aW(o)||(n.scrollLeft=f*(void 0===y?0:y),n.scrollTop=v*(void 0===x?0:x));var M=c-r[0]-1,w=d-r[3]-1,S=c+l+r[2]+1,A=d+p+r[1]+1,T=d+p-f,C=c+l-v,O=[["M",0,0]];f?O=[["M",0,M],["L",d-1,M],["L",d-1,S],["L",0,S],["Z"],["M",T,M],["L",s,M],["L",s,S],["L",T,S],["Z"]]:v&&(O=[["M",w,0],["L",w,c-1],["L",A,c-1],["L",A,0],["Z"],["M",w,C],["L",w,a],["L",A,a],["L",A,C],["Z"]]),"adjustHeight"!==e.redrawTrigger&&this.mask.attr({d:O})},t.prototype.moveFixedElements=function(){var e,i=this.chart,o=i.container,n=i.inverted,r=i.scrollablePixelsX,s=i.scrollablePixelsY,a=this.fixedRenderer,h=t.fixedSelectors;if(r&&!n?e=".highcharts-yaxis":r&&n?e=".highcharts-xaxis":s&&!n?e=".highcharts-xaxis":s&&n&&(e=".highcharts-yaxis"),e&&!(this.chart.hasParallelCoordinates&&".highcharts-yaxis"===e))for(var l=0,d=[""+e+":not(.highcharts-radial-axis)",""+e+"-labels:not(.highcharts-radial-axis-labels)"];l<d.length;l++){var c=d[l];aH(h,c)}else for(var p=0,u=[".highcharts-xaxis",".highcharts-yaxis"];p<u.length;p++)for(var f=u[p],g=0,v=[""+f+":not(.highcharts-radial-axis)",""+f+"-labels:not(.highcharts-radial-axis-labels)"];g<v.length;g++){var c=v[g];aG(h,c)}for(var m=0;m<h.length;m++){var c=h[m];[].forEach.call(o.querySelectorAll(c),function(t){(t.namespaceURI===a.SVG_NS?a.box:a.box.parentNode).appendChild(t),t.style.pointerEvents="auto"})}},t.fixedSelectors=[".highcharts-breadcrumbs-group",".highcharts-contextbutton",".highcharts-caption",".highcharts-credits",".highcharts-drillup-button",".highcharts-legend",".highcharts-legend-checkbox",".highcharts-navigator-series",".highcharts-navigator-xaxis",".highcharts-navigator-yaxis",".highcharts-navigator",".highcharts-range-selector-group",".highcharts-reset-zoom",".highcharts-scrollbar",".highcharts-subtitle",".highcharts-title"],t}(),aj=e8.format,aU=ss.series,aV=tF.destroyObjectProperties,aZ=tF.fireEvent,aq=tF.getAlignFactor,aK=tF.isNumber,a$=tF.pick,aJ=function(){function t(t,e,i,o,n){var r=t.chart.inverted,s=t.reversed;this.axis=t;var a=this.isNegative=!!i!=!!s;this.options=e=e||{},this.x=o,this.total=null,this.cumulative=null,this.points={},this.hasValidPoints=!1,this.stack=n,this.leftCliff=0,this.rightCliff=0,this.alignOptions={align:e.align||(r?a?"left":"right":"center"),verticalAlign:e.verticalAlign||(r?"middle":a?"bottom":"top"),y:e.y,x:e.x},this.textAlign=e.textAlign||(r?a?"right":"left":"center")}return t.prototype.destroy=function(){aV(this,this.axis)},t.prototype.render=function(t){var e=this.axis.chart,i=this.options,o=i.format,n=o?aj(o,this,e):i.formatter.call(this);if(this.label)this.label.attr({text:n,visibility:"hidden"});else{this.label=e.renderer.label(n,null,void 0,i.shape,void 0,void 0,i.useHTML,!1,"stack-labels");var r={r:i.borderRadius||0,text:n,padding:a$(i.padding,5),visibility:"hidden"};e.styledMode||(r.fill=i.backgroundColor,r.stroke=i.borderColor,r["stroke-width"]=i.borderWidth,this.label.css(i.style||{})),this.label.attr(r),this.label.added||this.label.add(t)}this.label.labelrank=e.plotSizeY,aZ(this,"afterRender")},t.prototype.setOffset=function(t,e,i,o,n,r){var s=this.alignOptions,a=this.axis,h=this.label,l=this.options,d=this.textAlign,c=a.chart,p=this.getStackBox({xOffset:t,width:e,boxBottom:i,boxTop:o,defaultX:n,xAxis:r}),u=s.verticalAlign;if(h&&p){var f=h.getBBox(void 0,0),g=h.padding,v="justify"===a$(l.overflow,"justify"),m=void 0;s.x=l.x||0,s.y=l.y||0;var y=this.adjustStackPosition({labelBox:f,verticalAlign:u,textAlign:d}),x=y.x,b=y.y;p.x-=x,p.y-=b,h.align(s,!1,p),(m=c.isInsidePlot(h.alignAttr.x+s.x+x,h.alignAttr.y+s.y+b))||(v=!1),v&&aU.prototype.justifyDataLabel.call(a,h,s,h.alignAttr,f,p),h.attr({x:h.alignAttr.x,y:h.alignAttr.y,rotation:l.rotation,rotationOriginX:f.width*aq(l.textAlign||"center"),rotationOriginY:f.height/2}),a$(!v&&l.crop,!0)&&(m=aK(h.x)&&aK(h.y)&&c.isInsidePlot(h.x-g+(h.width||0),h.y)&&c.isInsidePlot(h.x+g,h.y)),h[m?"show":"hide"]()}aZ(this,"afterSetOffset",{xOffset:t,width:e})},t.prototype.adjustStackPosition=function(t){var e=t.labelBox,i=t.verticalAlign,o=t.textAlign;return{x:e.width/2+e.width/2*(2*aq(o)-1),y:e.height/2*2*(1-aq(i))}},t.prototype.getStackBox=function(t){var e=this.axis,i=e.chart,o=t.boxTop,n=t.defaultX,r=t.xOffset,s=t.width,a=t.boxBottom,h=e.stacking.usePercentage?100:a$(o,this.total,0),l=e.toPixels(h),d=t.xAxis||i.xAxis[0],c=a$(n,d.translate(this.x))+r,p=Math.abs(l-e.toPixels(a||aK(e.min)&&e.logarithmic&&e.logarithmic.lin2log(e.min)||0)),u=i.inverted,f=this.isNegative;return u?{x:(f?l:l-p)-i.plotLeft,y:d.height-c-s+d.top-i.plotTop,width:p,height:s}:{x:c+d.transB-i.plotLeft,y:(f?l-p:l)-i.plotTop,width:s,height:p}},t}(),aQ=ss.series.prototype,a0=tF.addEvent,a1=tF.correctFloat,a2=tF.defined,a3=tF.destroyObjectProperties,a5=tF.fireEvent,a6=tF.isNumber,a9=tF.objectEach,a4=tF.pick;function a8(){var t=this.inverted;this.axes.forEach(function(t){var e;(null===(e=t.stacking)||void 0===e?void 0:e.stacks)&&t.hasVisibleSeries&&(t.stacking.oldStacks=t.stacking.stacks)}),this.series.forEach(function(e){var i,o=(null===(i=e.xAxis)||void 0===i?void 0:i.options)||{};e.options.stacking&&e.reserveSpace()&&(e.stackKey=[e.type,a4(e.options.stack,""),t?o.top:o.left,t?o.height:o.width].join(","))})}function a7(){var t,e=this.stacking;if(e){var i=e.stacks;a9(i,function(t,e){a3(t),delete i[e]}),null===(t=e.stackTotalGroup)||void 0===t||t.destroy()}}function ht(){this.stacking||(this.stacking=new hs(this))}function he(t,e,i,o){return!a2(t)||t.x!==e||o&&t.stackKey!==o?t={x:e,index:0,key:o,stackKey:o}:t.index++,t.key=[i,e,t.index].join(","),t}function hi(){var t,e=this,i=e.yAxis,o=e.stackKey||"",n=i.stacking.stacks,r=e.getColumn("x",!0),s=e.options.stacking,a=e[s+"Stacker"];a&&[o,"-"+o].forEach(function(i){for(var o,s,h,l,d=r.length;d--;)s=r[d],t=e.getStackIndicator(t,s,e.index,i),(l=null==(h=null===(o=n[i])||void 0===o?void 0:o[s])?void 0:h.points[t.key||""])&&a.call(e,l,h,d)})}function ho(t,e,i){var o=e.total?100/e.total:0;t[0]=a1(t[0]*o),t[1]=a1(t[1]*o),this.stackedYData[i]=t[1]}function hn(t){(this.is("column")||this.is("columnrange"))&&(this.options.centerInCategory&&this.chart.series.length>1?aQ.setStackedPoints.call(this,t,"group"):t.stacking.resetStacks())}function hr(t,e){var i,o,n,r,s,a,h,l,d,c=e||this.options.stacking;if(c&&this.reserveSpace()&&(({group:"xAxis"})[c]||"yAxis")===t.coll){var p=this.getColumn("x",!0),u=this.getColumn(this.pointValKey||"y",!0),f=[],g=u.length,v=this.options,m=v.threshold||0,y=v.startFromThreshold?m:0,x=v.stack,b=e?""+this.type+",".concat(c):this.stackKey||"",k="-"+b,M=this.negStacks,w=t.stacking,S=w.stacks,A=w.oldStacks;for(w.stacksTouched+=1,d=0;d<g;d++){var T=p[d]||0,C=u[d],O=a6(C)&&C||0;l=(n=this.getStackIndicator(n,T,this.index)).key||"",S[h=(r=M&&O<(y?0:m))?k:b]||(S[h]={}),S[h][T]||((null===(i=A[h])||void 0===i?void 0:i[T])?(S[h][T]=A[h][T],S[h][T].total=null):S[h][T]=new aJ(t,t.options.stackLabels,!!r,T,x)),s=S[h][T],null!==C?(s.points[l]=s.points[this.index]=[a4(s.cumulative,y)],a2(s.cumulative)||(s.base=l),s.touched=w.stacksTouched,n.index>0&&!1===this.singleStacks&&(s.points[l][0]=s.points[this.index+","+T+",0"][0])):(delete s.points[l],delete s.points[this.index]);var P=s.total||0;"percent"===c?(a=r?b:k,P=M&&(null===(o=S[a])||void 0===o?void 0:o[T])?(a=S[a][T]).total=Math.max(a.total||0,P)+Math.abs(O):a1(P+Math.abs(O))):"group"===c?a6(C)&&P++:P=a1(P+O),"group"===c?s.cumulative=(P||1)-1:s.cumulative=a1(a4(s.cumulative,y)+O),s.total=P,null!==C&&(s.points[l].push(s.cumulative),f[d]=s.cumulative,s.hasValidPoints=!0)}"percent"===c&&(w.usePercentage=!0),"group"!==c&&(this.stackedYData=f),w.oldStacks={}}}var hs=function(){function t(t){this.oldStacks={},this.stacks={},this.stacksTouched=0,this.axis=t}return t.prototype.buildStacks=function(){var t,e,i=this.axis,o=i.series,n="xAxis"===i.coll,r=i.options.reversedStacks,s=o.length;for(this.resetStacks(),this.usePercentage=!1,e=s;e--;)t=o[r?e:s-e-1],n&&t.setGroupedPoints(i),t.setStackedPoints(i);if(!n)for(e=0;e<s;e++)o[e].modifyStacks();a5(i,"afterBuildStacks")},t.prototype.cleanStacks=function(){this.oldStacks&&(this.stacks=this.oldStacks,a9(this.stacks,function(t){a9(t,function(t){t.cumulative=t.total})}))},t.prototype.resetStacks=function(){var t=this;a9(this.stacks,function(e){a9(e,function(i,o){a6(i.touched)&&i.touched<t.stacksTouched?(i.destroy(),delete e[o]):(i.total=null,i.cumulative=null)})})},t.prototype.renderStackTotals=function(){var t,e=this.axis,i=e.chart,o=i.renderer,n=this.stacks,r=eD(i,(null===(t=e.options.stackLabels)||void 0===t?void 0:t.animation)||!1),s=this.stackTotalGroup=this.stackTotalGroup||o.g("stack-labels").attr({zIndex:6,opacity:0}).add();s.translate(i.plotLeft,i.plotTop),a9(n,function(t){a9(t,function(t){t.render(s)})}),s.animate({opacity:1},r)},t}();($||($={})).compose=function(t,e,i){var o=e.prototype,n=i.prototype;o.getStacks||(a0(t,"init",ht),a0(t,"destroy",a7),o.getStacks=a8,n.getStackIndicator=he,n.modifyStacks=hi,n.percentStacker=ho,n.setGroupedPoints=hn,n.setStackedPoints=hr)};var ha=$,hh=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hl=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},hd=tF.defined,hc=tF.merge,hp=tF.isObject,hu=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hh(e,t),e.prototype.drawGraph=function(){var t=this,e=this.options,i=(this.gappedPath||this.getGraphPath).call(this),o=this.chart.styledMode;hl([this],this.zones,!0).forEach(function(n,r){var s,a=n.graph,h=a?"animate":"attr",l=n.dashStyle||e.dashStyle;a?(a.endX=t.preventGraphAnimation?null:i.xMap,a.animate({d:i})):i.length&&(n.graph=a=t.chart.renderer.path(i).addClass("highcharts-graph"+(r?" highcharts-zone-graph-".concat(r-1," "):" ")+(r&&n.className||"")).attr({zIndex:1}).add(t.group)),a&&!o&&(s={stroke:!r&&e.lineColor||n.color||t.color||"#cccccc","stroke-width":e.lineWidth||0,fill:t.fillGraph&&t.color||"none"},l?s.dashstyle=l:"square"!==e.linecap&&(s["stroke-linecap"]=s["stroke-linejoin"]="round"),a[h](s).shadow(e.shadow&&hc({filterUnits:"userSpaceOnUse"},hp(e.shadow)?e.shadow:{}))),a&&(a.startX=i.xMap,a.isArea=i.isArea)})},e.prototype.getGraphPath=function(t,e,i){var o,n=this,r=n.options,s=[],a=[],h=r.step,l=(t=t||n.points).reversed;return l&&t.reverse(),(h=({right:1,center:2})[h]||h&&3)&&l&&(h=4-h),(t=this.getValidPoints(t,!1,r.nullInteraction||!(r.connectNulls&&!e&&!i))).forEach(function(l,d){var c,p=l.plotX,u=l.plotY,f=t[d-1],g=l.isNull||"number"!=typeof u;(l.leftCliff||(null==f?void 0:f.rightCliff))&&!i&&(o=!0),g&&!hd(e)&&d>0?o=!r.connectNulls:g&&!e?o=!0:(0===d||o?c=[["M",l.plotX,l.plotY]]:n.getPointSpline?c=[n.getPointSpline(t,l,d)]:h?(c=1===h?[["L",f.plotX,u]]:2===h?[["L",(f.plotX+p)/2,f.plotY],["L",(f.plotX+p)/2,u]]:[["L",p,f.plotY]]).push(["L",p,u]):c=[["L",p,u]],a.push(l.x),h&&(a.push(l.x),2===h&&a.push(l.x)),s.push.apply(s,c),o=!1)}),s.xMap=a,n.graphPath=s,s},e.defaultOptions=hc(sW.defaultOptions,{legendSymbol:"lineMarker"}),e}(sW);ss.registerSeriesType("line",hu);var hf={threshold:0,legendSymbol:"areaMarker"},hg=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hv=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},hm=ss.seriesTypes.line,hy=tF.extend,hx=tF.merge,hb=tF.objectEach,hk=tF.pick,hM=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hg(e,t),e.prototype.drawGraph=function(){var e=this;this.areaPath=[],t.prototype.drawGraph.apply(this);var i=this.areaPath,o=this.options;hv([this],this.zones,!0).forEach(function(t,n){var r,s={},a=t.fillColor||o.fillColor,h=t.area,l=h?"animate":"attr";h?(h.endX=e.preventGraphAnimation?null:i.xMap,h.animate({d:i})):(s.zIndex=0,(h=t.area=e.chart.renderer.path(i).addClass("highcharts-area"+(n?" highcharts-zone-area-".concat(n-1," "):" ")+(n&&t.className||"")).add(e.group)).isArea=!0),e.chart.styledMode||(s.fill=a||t.color||e.color,s["fill-opacity"]=a?1:null!==(r=o.fillOpacity)&&void 0!==r?r:.75,h.css({pointerEvents:e.stickyTracking?"none":"auto"})),h[l](s),h.startX=i.xMap,h.shiftUnit=o.step?2:1})},e.prototype.getGraphPath=function(t){var e,i,o,n=hm.prototype.getGraphPath,r=this.options,s=r.stacking,a=this.yAxis,h=[],l=[],d=this.index,c=a.stacking.stacks[this.stackKey],p=r.threshold,u=Math.round(a.getThreshold(r.threshold)),f=hk(r.connectNulls,"percent"===s),g=function(i,o,n){var r,f,g=t[i],v=s&&c[g.x].points[d],m=g[n+"Null"]||0,y=g[n+"Cliff"]||0,x=!0;y||m?(r=(m?v[0]:v[1])+y,f=v[0]+y,x=!!m):!s&&t[o]&&t[o].isNull&&(r=f=p),void 0!==r&&(l.push({plotX:e,plotY:null===r?u:a.getThreshold(r),isNull:x,isCliff:!0}),h.push({plotX:e,plotY:null===f?u:a.getThreshold(f),doCurve:!1}))};t=t||this.points,s&&(t=this.getStackPoints(t));for(var v=0,m=t.length;v<m;++v)s||(t[v].leftCliff=t[v].rightCliff=t[v].leftNull=t[v].rightNull=void 0),i=t[v].isNull,e=hk(t[v].rectPlotX,t[v].plotX),o=s?hk(t[v].yBottom,u):u,i&&!f||(f||g(v,v-1,"left"),i&&!s&&f||(l.push(t[v]),h.push({x:v,plotX:e,plotY:o})),f||g(v,v+1,"right"));var y=n.call(this,l,!0,!0);h.reversed=!0;var x=n.call(this,h,!0,!0),b=x[0];b&&"M"===b[0]&&(x[0]=["L",b[1],b[2]]);var k=y.concat(x);k.length&&k.push(["Z"]);var M=n.call(this,l,!1,f);return this.chart.series.length>1&&s&&l.some(function(t){return t.isCliff})&&(k.hasStackedCliffs=M.hasStackedCliffs=!0),k.xMap=y.xMap,this.areaPath=k,M},e.prototype.getStackPoints=function(t){var e=this,i=[],o=[],n=this.xAxis,r=this.yAxis,s=r.stacking.stacks[this.stackKey],a={},h=r.series,l=h.length,d=r.options.reversedStacks?1:-1,c=h.indexOf(e);if(t=t||this.points,this.options.stacking){for(var p=0;p<t.length;p++)t[p].leftNull=t[p].rightNull=void 0,a[t[p].x]=t[p];hb(s,function(t,e){null!==t.total&&o.push(e)}),o.sort(function(t,e){return t-e});var u=h.map(function(t){return t.visible});o.forEach(function(t,p){var f,g,v=0;if(a[t]&&!a[t].isNull)i.push(a[t]),[-1,1].forEach(function(i){var n=1===i?"rightNull":"leftNull",r=s[o[p+i]],v=0;if(r)for(var m=c;m>=0&&m<l;){var y=h[m].index;!(f=r.points[y])&&(y===e.index?a[t][n]=!0:u[m]&&(g=s[t].points[y])&&(v-=g[1]-g[0])),m+=d}a[t][1===i?"rightCliff":"leftCliff"]=v});else{for(var m=c;m>=0&&m<l;){var y=h[m].index;if(f=s[t].points[y]){v=f[1];break}m+=d}v=hk(v,0),v=r.translate(v,0,1,0,1),i.push({isNull:!0,plotX:n.translate(t,0,0,0,1),x:t,plotY:v,yBottom:v})}})}return i},e.defaultOptions=hx(hm.defaultOptions,hf),e}(hm);hy(hM.prototype,{singleStacks:!1}),ss.registerSeriesType("area",hM);var hw=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hS=ss.seriesTypes.line,hA=tF.merge,hT=tF.pick,hC=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hw(e,t),e.prototype.getPointSpline=function(t,e,i){var o,n,r,s,a=e.plotX||0,h=e.plotY||0,l=t[i-1],d=t[i+1];function c(t){return t&&!t.isNull&&!1!==t.doCurve&&!e.isCliff}if(c(l)&&c(d)){var p=l.plotX||0,u=l.plotY||0,f=d.plotX||0,g=d.plotY||0,v=0;o=(1.5*a+p)/2.5,n=(1.5*h+u)/2.5,r=(1.5*a+f)/2.5,s=(1.5*h+g)/2.5,r!==o&&(v=(s-n)*(r-a)/(r-o)+h-s),n+=v,s+=v,n>u&&n>h?(n=Math.max(u,h),s=2*h-n):n<u&&n<h&&(n=Math.min(u,h),s=2*h-n),s>g&&s>h?(s=Math.max(g,h),n=2*h-s):s<g&&s<h&&(s=Math.min(g,h),n=2*h-s),e.rightContX=r,e.rightContY=s,e.controlPoints={low:[o,n],high:[r,s]}}var m=["C",hT(l.rightContX,l.plotX,0),hT(l.rightContY,l.plotY,0),hT(o,a,0),hT(n,h,0),a,h];return l.rightContX=l.rightContY=void 0,m},e.defaultOptions=hA(hS.defaultOptions),e}(hS);ss.registerSeriesType("spline",hC);var hO=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hP=ss.seriesTypes,hE=hP.area,hL=hP.area.prototype,hB=tF.extend,hD=tF.merge,hI=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hO(e,t),e.defaultOptions=hD(hC.defaultOptions,hE.defaultOptions),e}(hC);hB(hI.prototype,{getGraphPath:hL.getGraphPath,getStackPoints:hL.getStackPoints,drawGraph:hL.drawGraph}),ss.registerSeriesType("areaspline",hI);var hz={borderRadius:3,centerInCategory:!1,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{halo:!1,brightness:.1},select:{color:"#cccccc",borderColor:"#000000"}},dataLabels:{align:void 0,verticalAlign:void 0,y:void 0},startFromThreshold:!0,stickyTracking:!1,tooltip:{distance:6},threshold:0,borderColor:"#ffffff"},hR=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),hN=ev.parse,hW=tv.noop,hG=tF.clamp,hX=tF.crisp,hH=tF.defined,hF=tF.extend,hY=tF.fireEvent,h_=tF.isArray,hj=tF.isNumber,hU=tF.merge,hV=tF.pick,hZ=tF.objectEach,hq=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return hR(e,t),e.prototype.animate=function(t){var e,i,o=this,n=this.yAxis,r=n.pos,s=n.reversed,a=o.options,h=this.chart,l=h.clipOffset,d=h.inverted,c={},p=d?"translateX":"translateY";t&&l?(c.scaleY=.001,i=hG(n.toPixels(a.threshold||0),r,r+n.len),d?c.translateX=(i+=s?-Math.floor(l[0]):Math.ceil(l[2]))-n.len:c.translateY=i+=s?Math.ceil(l[0]):-Math.floor(l[2]),o.clipBox&&o.setClip(),o.group.attr(c)):(e=Number(o.group.attr(p)),o.group.animate({scaleY:1},hF(eE(o.options.animation),{step:function(t,i){o.group&&(c[p]=e+i.pos*(r-e),o.group.attr(c))}})))},e.prototype.init=function(e,i){t.prototype.init.apply(this,arguments);var o=this;(e=o.chart).hasRendered&&e.series.forEach(function(t){t.type===o.type&&(t.isDirty=!0)})},e.prototype.getColumnMetrics=function(){var t,e,i,o=this,n=o.options,r=o.xAxis,s=o.yAxis,a=r.options.reversedStacks,h=r.reversed&&!a||!r.reversed&&a,l={},d=0;!1===n.grouping?d=1:o.chart.series.forEach(function(t){var e,n=t.yAxis,r=t.options;t.type===o.type&&t.reserveSpace()&&s.len===n.len&&s.pos===n.pos&&(r.stacking&&"group"!==r.stacking?(void 0===l[i=t.stackKey]&&(l[i]=d++),e=l[i]):!1!==r.grouping&&(e=d++),t.columnIndex=e)});var c=Math.min(Math.abs(r.transA)*(!(null===(t=r.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(null===(e=r.ordinal)||void 0===e?void 0:e.slope)||n.pointRange||r.closestPointRange||r.tickInterval||1),r.len),p=c*n.groupPadding,u=(c-2*p)/(d||1),f=Math.min(n.maxPointWidth||r.len,hV(n.pointWidth,u*(1-2*n.pointPadding))),g=(o.columnIndex||0)+ +!!h;return o.columnMetrics={width:f,offset:(u-f)/2+(p+g*u-c/2)*(h?-1:1),paddedWidth:u,columnCount:d},o.columnMetrics},e.prototype.crispCol=function(t,e,i,o){var n=this.borderWidth,r=this.chart.inverted;return o=hX(e+o,n,r)-(e=hX(e,n,r)),this.options.crisp&&(i=hX(t+i,n)-(t=hX(t,n))),{x:t,y:e,width:i,height:o}},e.prototype.adjustForMissingColumns=function(t,e,i,o){var n,r=this;if(!i.isNull&&o.columnCount>1){var s=this.xAxis.series.filter(function(t){return t.visible}).map(function(t){return t.index}),a=0,h=0;hZ(null===(n=this.xAxis.stacking)||void 0===n?void 0:n.stacks,function(t){var e,o="number"==typeof i.x?null===(e=t[i.x.toString()])||void 0===e?void 0:e.points:void 0,n=null==o?void 0:o[r.index],l={};if(o&&h_(n)){var d=r.index,c=Object.keys(o).filter(function(t){return!t.match(",")&&o[t]&&o[t].length>1}).map(parseFloat).filter(function(t){return -1!==s.indexOf(t)}).filter(function(t){var e=r.chart.series[t].options,i=e.stacking&&e.stack;if(hH(i)){if(hj(l[i]))return d===t&&(d=l[i]),!1;l[i]=t}return!0}).sort(function(t,e){return e-t});a=c.indexOf(d),h=c.length}}),a=this.xAxis.reversed?h-1-a:a;var l=(h-1)*o.paddedWidth+e;t=(i.plotX||0)+l/2-e-a*o.paddedWidth}return t},e.prototype.translate=function(){var t=this,e=t.chart,i=t.options,o=t.dense=t.closestPointRange*t.xAxis.transA<2,n=t.borderWidth=hV(i.borderWidth,+!o),r=t.xAxis,s=t.yAxis,a=i.threshold,h=hV(i.minPointLength,5),l=t.getColumnMetrics(),d=l.width,c=t.pointXOffset=l.offset,p=t.dataMin,u=t.dataMax,f=t.translatedThreshold=s.getThreshold(a),g=t.barW=Math.max(d,1+2*n);i.pointPadding&&i.crisp&&(g=Math.ceil(g)),sW.prototype.translate.apply(t),t.points.forEach(function(o){var n,v=hV(o.yBottom,f),m=999+Math.abs(v),y=o.plotX||0,x=hG(o.plotY,-m,s.len+m),b=Math.min(x,v),k=Math.max(x,v)-b,M=d,w=y+c,S=g;h&&Math.abs(k)<h&&(k=h,n=!s.reversed&&!o.negative||s.reversed&&o.negative,hj(a)&&hj(u)&&o.y===a&&u<=a&&(s.min||0)<a&&(p!==u||(s.max||0)<=a)&&(n=!n,o.negative=!o.negative),b=Math.abs(b-f)>h?v-h:f-(n?h:0)),hH(o.options.pointWidth)&&(w-=Math.round(((M=S=Math.ceil(o.options.pointWidth))-d)/2)),i.centerInCategory&&(w=t.adjustForMissingColumns(w,M,o,l)),o.barX=w,o.pointWidth=M,o.tooltipPos=e.inverted?[hG(s.len+s.pos-e.plotLeft-x,s.pos-e.plotLeft,s.len+s.pos-e.plotLeft),r.len+r.pos-e.plotTop-w-S/2,k]:[r.left-e.plotLeft+w+S/2,hG(x+s.pos-e.plotTop,s.pos-e.plotTop,s.len+s.pos-e.plotTop),k],o.shapeType=t.pointClass.prototype.shapeType||"roundedRect",o.shapeArgs=t.crispCol(w,b,S,o.isNull?0:k)}),hY(this,"afterColumnTranslate")},e.prototype.drawGraph=function(){this.group[this.dense?"addClass":"removeClass"]("highcharts-dense-data")},e.prototype.pointAttribs=function(t,e){var i,o,n,r,s,a=this.options,h=this.pointAttrToOptions||{},l=h.stroke||"borderColor",d=h["stroke-width"]||"borderWidth",c=t&&t.color||this.color,p=t&&t[l]||a[l]||c,u=t&&t.options.dashStyle||a.dashStyle,f=t&&t[d]||a[d]||this[d]||0,g=(null==t?void 0:t.isNull)&&a.nullInteraction?0:null!==(o=null!==(i=null==t?void 0:t.opacity)&&void 0!==i?i:a.opacity)&&void 0!==o?o:1;t&&this.zones.length&&(r=t.getZone(),c=t.options.color||r&&(r.color||t.nonZonedColor)||this.color,r&&(p=r.borderColor||p,u=r.dashStyle||u,f=r.borderWidth||f)),e&&t&&(s=(n=hU(a.states[e],t.options.states&&t.options.states[e]||{})).brightness,c=n.color||void 0!==s&&hN(c).brighten(n.brightness).get()||c,p=n[l]||p,f=n[d]||f,u=n.dashStyle||u,g=hV(n.opacity,g));var v={fill:c,stroke:p,"stroke-width":f,opacity:g};return u&&(v.dashstyle=u),v},e.prototype.drawPoints=function(t){void 0===t&&(t=this.points);var e,i=this,o=this.chart,n=i.options,r=n.nullInteraction,s=o.renderer,a=n.animationLimit||250;t.forEach(function(t){var h=t.plotY,l=t.graphic,d=!!l,c=l&&o.pointCount<a?"animate":"attr";hj(h)&&(null!==t.y||r)?(e=t.shapeArgs,l&&t.hasNewShapeType()&&(l=l.destroy()),i.enabledDataSorting&&(t.startXPos=i.xAxis.reversed?-(e&&e.width||0):i.xAxis.width),!l&&(t.graphic=l=s[t.shapeType](e).add(t.group||i.group),l&&i.enabledDataSorting&&o.hasRendered&&o.pointCount<a&&(l.attr({x:t.startXPos}),d=!0,c="animate")),l&&d&&l[c](hU(e)),o.styledMode||l[c](i.pointAttribs(t,t.selected&&"select")).shadow(!1!==t.allowShadow&&n.shadow),l&&(l.addClass(t.getClassName(),!0),l.attr({visibility:t.visible?"inherit":"hidden"}))):l&&(t.graphic=l.destroy())})},e.prototype.drawTracker=function(t){void 0===t&&(t=this.points);var e,i=this,o=i.chart,n=o.pointer,r=function(t){null==n||n.normalize(t);var e=null==n?void 0:n.getPointFromEvent(t);n&&e&&i.options.enableMouseTracking&&(o.isInsidePlot(t.chartX-o.plotLeft,t.chartY-o.plotTop,{visiblePlotOnly:!0})||(null==n?void 0:n.inClass(t.target,"highcharts-data-label")))&&(n.isDirectTouch=!0,e.onMouseOver(t))};t.forEach(function(t){e=h_(t.dataLabels)?t.dataLabels:t.dataLabel?[t.dataLabel]:[],t.graphic&&(t.graphic.element.point=t),e.forEach(function(e){(e.div||e.element).point=t})}),i._hasTracking||(i.trackerGroups.forEach(function(t){i[t]&&(i[t].addClass("highcharts-tracker").on("mouseover",r).on("mouseout",function(t){null==n||n.onTrackerMouseOut(t)}).on("touchstart",r),!o.styledMode&&i.options.cursor&&i[t].css({cursor:i.options.cursor}))}),i._hasTracking=!0),hY(this,"afterDrawTracker")},e.prototype.remove=function(){var t=this,e=t.chart;e.hasRendered&&e.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)}),sW.prototype.remove.apply(t,arguments)},e.defaultOptions=hU(sW.defaultOptions,hz),e}(sW);hF(hq.prototype,{directTouch:!0,getSymbol:hW,negStacks:!0,trackerGroups:["group","dataLabelsGroup"]}),ss.registerSeriesType("column",hq);var hK=e8.format,h$=tF.defined,hJ=tF.extend,hQ=tF.fireEvent,h0=tF.getAlignFactor,h1=tF.isArray,h2=tF.isString,h3=tF.merge,h5=tF.objectEach,h6=tF.pick,h9=tF.pInt,h4=tF.splat;!function(t){function e(){return h(this).some(function(t){return null==t?void 0:t.enabled})}function i(t,e,i,o,n){var r,s=this.chart,a=this.enabledDataSorting,h=this.isCartesian&&s.inverted,l=t.plotX,d=t.plotY,c=i.rotation||0,p=h$(l)&&h$(d)&&s.isInsidePlot(l,Math.round(d),{inverted:h,paneCoordinates:!0,series:this}),u=0===c&&"justify"===h6(i.overflow,a?"none":"justify"),f=this.visible&&!1!==t.visible&&h$(l)&&(t.series.forceDL||a&&!u||p||h6(i.inside,!!this.options.stacking)&&o&&s.isInsidePlot(l,h?o.x+1:o.y+o.height-1,{inverted:h,paneCoordinates:!0,series:this})),g=t.pos();if(f&&g){var v,m=e.getBBox(),y=e.getBBox(void 0,0);if(o=hJ({x:g[0],y:Math.round(g[1]),width:0,height:0},o||{}),"plotEdges"===i.alignTo&&this.isCartesian&&(o[h?"x":"y"]=0,o[h?"width":"height"]=(null===(r=this.yAxis)||void 0===r?void 0:r.len)||0),hJ(i,{width:m.width,height:m.height}),v=o,a&&this.xAxis&&!u&&this.setDataLabelStartPos(t,e,n,p,v),e.align(h3(i,{width:y.width,height:y.height}),!1,o,!1),e.alignAttr.x+=h0(i.align)*(y.width-m.width),e.alignAttr.y+=h0(i.verticalAlign)*(y.height-m.height),e[e.placed?"animate":"attr"]({"text-align":e.alignAttr["text-align"]||"center",x:e.alignAttr.x+(m.width-y.width)/2,y:e.alignAttr.y+(m.height-y.height)/2,rotationOriginX:(e.width||0)/2,rotationOriginY:(e.height||0)/2}),u&&o.height>=0)this.justifyDataLabel(e,i,e.alignAttr,m,o,n);else if(h6(i.crop,!0)){var x=e.alignAttr,b=x.x,k=x.y;f=s.isInsidePlot(b,k,{paneCoordinates:!0,series:this})&&s.isInsidePlot(b+m.width-1,k+m.height-1,{paneCoordinates:!0,series:this})}i.shape&&!c&&e[n?"attr":"animate"]({anchorX:g[0],anchorY:g[1]})}n&&a&&(e.placed=!1),f||a&&!u?(e.show(),e.placed=!0):(e.hide(),e.placed=!1)}function o(){return this.plotGroup("dataLabelsGroup","data-labels",this.hasRendered?"inherit":"hidden",this.options.dataLabels.zIndex||6)}function n(t){var e=this.hasRendered||0,i=this.initDataLabelsGroup().attr({opacity:+e});return!e&&i&&(this.visible&&i.show(),this.options.animation?i.animate({opacity:1},t):i.attr({opacity:1})),i}function r(t){t=t||this.points;var e,i,o=this,n=o.chart,r=o.options,s=n.renderer,l=n.options.chart,d=l.backgroundColor,c=l.plotBackgroundColor,p=s.getContrast(h2(c)&&c||h2(d)&&d||"#000000"),u=h(o),f=u[0],g=f.animation,v=f.defer?eD(n,g,o):{defer:0,duration:0};hQ(this,"drawDataLabels"),(null===(e=o.hasDataLabels)||void 0===e?void 0:e.call(o))&&(i=this.initDataLabels(v),t.forEach(function(t){var e,h,l,d=t.dataLabels||[],c=t.color||o.color;h4(a(u,t.dlOptions||(null===(e=t.options)||void 0===e?void 0:e.dataLabels))).forEach(function(e,a){var h,l,u,f,g,v=e.enabled&&(t.visible||t.dataLabelOnHidden)&&(!t.isNull||t.dataLabelOnNull)&&function(t,e){var i=e.filter;if(i){var o=i.operator,n=t[i.property],r=i.value;return">"===o&&n>r||"<"===o&&n<r||">="===o&&n>=r||"<="===o&&n<=r||"=="===o&&n==r||"==="===o&&n===r||"!="===o&&n!=r||"!=="===o&&n!==r||!1}return!0}(t,e),m=e.backgroundColor,y=e.borderColor,x=e.distance,b=e.style,k=void 0===b?{}:b,M={},w=d[a],S=!w;v&&(u=h$(l=h6(e[t.formatPrefix+"Format"],e.format))?hK(l,t,n):(e[t.formatPrefix+"Formatter"]||e.formatter).call(t,e),f=e.rotation,!n.styledMode&&(k.color=h6(e.color,k.color,h2(o.color)?o.color:void 0,"#000000"),"contrast"===k.color?("none"!==m&&(g=m),t.contrastColor=s.getContrast("auto"!==g&&h2(g)&&g||(h2(c)?c:"")),k.color=g||!h$(x)&&e.inside||0>h9(x||0)||r.stacking?t.contrastColor:p):delete t.contrastColor,r.cursor&&(k.cursor=r.cursor)),M={r:e.borderRadius||0,rotation:f,padding:e.padding,zIndex:1},n.styledMode||(M.fill="auto"===m?t.color:m,M.stroke="auto"===y?t.color:y,M["stroke-width"]=e.borderWidth),h5(M,function(t,e){void 0===t&&delete M[e]})),!w||v&&h$(u)&&!!(w.div||(null===(h=w.text)||void 0===h?void 0:h.foreignObject))==!!e.useHTML&&(w.rotation&&e.rotation||w.rotation===e.rotation)||(w=void 0,S=!0),v&&h$(u)&&(w?M.text=u:(w=s.label(u,0,0,e.shape,void 0,void 0,e.useHTML,void 0,"data-label")).addClass(" highcharts-data-label-color-"+t.colorIndex+" "+(e.className||"")+(e.useHTML?" highcharts-tracker":"")),w&&(w.options=e,w.attr(M),n.styledMode?k.width&&w.css({width:k.width,textOverflow:k.textOverflow,whiteSpace:k.whiteSpace}):w.css(k).shadow(e.shadow),hQ(w,"beforeAddingDataLabel",{labelOptions:e,point:t}),w.added||w.add(i),o.alignDataLabel(t,w,e,void 0,S),w.isActive=!0,d[a]&&d[a]!==w&&d[a].destroy(),d[a]=w))});for(var f=d.length;f--;)(null===(h=d[f])||void 0===h?void 0:h.isActive)?d[f].isActive=!1:(null===(l=d[f])||void 0===l||l.destroy(),d.splice(f,1));t.dataLabel=d[0],t.dataLabels=d})),hQ(this,"afterDrawDataLabels")}function s(t,e,i,o,n,r){var s,a,h=this.chart,l=e.align,d=e.verticalAlign,c=t.box?0:t.padding||0,p=h.inverted?this.yAxis:this.xAxis,u=p?p.left-h.plotLeft:0,f=h.inverted?this.xAxis:this.yAxis,g=f?f.top-h.plotTop:0,v=e.x,m=void 0===v?0:v,y=e.y,x=void 0===y?0:y;return(s=(i.x||0)+c+u)<0&&("right"===l&&m>=0?(e.align="left",e.inside=!0):m-=s,a=!0),(s=(i.x||0)+o.width-c+u)>h.plotWidth&&("left"===l&&m<=0?(e.align="right",e.inside=!0):m+=h.plotWidth-s,a=!0),(s=i.y+c+g)<0&&("bottom"===d&&x>=0?(e.verticalAlign="top",e.inside=!0):x-=s,a=!0),(s=(i.y||0)+o.height-c+g)>h.plotHeight&&("top"===d&&x<=0?(e.verticalAlign="bottom",e.inside=!0):x+=h.plotHeight-s,a=!0),a&&(e.x=m,e.y=x,t.placed=!r,t.align(e,void 0,n)),a}function a(t,e){var i,o=[];if(h1(t)&&!h1(e))o=t.map(function(t){return h3(t,e)});else if(h1(e)&&!h1(t))o=e.map(function(e){return h3(t,e)});else if(h1(t)||h1(e)){if(h1(t)&&h1(e))for(i=Math.max(t.length,e.length);i--;)o[i]=h3(t[i],e[i])}else o=h3(t,e);return o}function h(t){var e,i,o=t.chart.options.plotOptions;return h4(a(a(null===(e=null==o?void 0:o.series)||void 0===e?void 0:e.dataLabels,null===(i=null==o?void 0:o[t.type])||void 0===i?void 0:i.dataLabels),t.options.dataLabels))}function l(t,e,i,o,n){var r=this.chart,s=r.inverted,a=this.xAxis,h=a.reversed,l=((s?e.height:e.width)||0)/2,d=t.pointWidth,c=d?d/2:0;e.startXPos=s?n.x:h?-l-c:a.width-l+c,e.startYPos=s?h?this.yAxis.height-l+c:-l-c:n.y,o?"hidden"===e.visibility&&(e.show(),e.attr({opacity:0}).animate({opacity:1})):e.attr({opacity:1}).animate({opacity:0},void 0,e.hide),r.hasRendered&&(i&&e.attr({x:e.startXPos,y:e.startYPos}),e.placed=!0)}t.compose=function(t){var h=t.prototype;h.initDataLabels||(h.initDataLabels=n,h.initDataLabelsGroup=o,h.alignDataLabel=i,h.drawDataLabels=r,h.justifyDataLabel=s,h.mergeArrays=a,h.setDataLabelStartPos=l,h.hasDataLabels=e)}}(J||(J={}));var h8=J,h7=tv.composed,lt=ss.series,le=tF.merge,li=tF.pushUnique;!function(t){function e(t,e,i,o,n){var r,s,a,h,l,d,c,p=this.chart,u=this.options,f=p.inverted,g=(null===(r=this.xAxis)||void 0===r?void 0:r.len)||p.plotSizeX||0,v=(null===(s=this.yAxis)||void 0===s?void 0:s.len)||p.plotSizeY||0,m=t.dlBox||t.shapeArgs,y=null!==(a=t.below)&&void 0!==a?a:(t.plotY||0)>(null!==(h=this.translatedThreshold)&&void 0!==h?h:v),x=null!==(l=i.inside)&&void 0!==l?l:!!u.stacking;if(m){if(o=le(m),"allow"!==i.overflow||!1!==i.crop||!1!==u.clip){o.y<0&&(o.height+=o.y,o.y=0);var b=o.y+o.height-v;b>0&&b<o.height-1&&(o.height-=b)}f&&(o={x:v-o.y-o.height,y:g-o.x-o.width,width:o.height,height:o.width}),x||(f?(o.x+=y?0:o.width,o.width=0):(o.y+=y?o.height:0,o.height=0))}null!==(d=i.align)&&void 0!==d||(i.align=!f||x?"center":y?"right":"left"),null!==(c=i.verticalAlign)&&void 0!==c||(i.verticalAlign=f||x?"middle":y?"top":"bottom"),lt.prototype.alignDataLabel.call(this,t,e,i,o,n),i.inside&&t.contrastColor&&e.css({color:t.contrastColor})}t.compose=function(t){h8.compose(lt),li(h7,"ColumnDataLabel")&&(t.prototype.alignDataLabel=e)}}(Q||(Q={}));var lo=Q,ln=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lr=tF.extend,ls=tF.merge,la=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ln(e,t),e.defaultOptions=ls(hq.defaultOptions,{}),e}(hq);lr(la.prototype,{inverted:!0}),ss.registerSeriesType("bar",la);var lh={lineWidth:0,findNearestPointBy:"xy",jitter:{x:0,y:0},marker:{enabled:!0},tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>"}},ll=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ld=ss.seriesTypes,lc=ld.column,lp=ld.line,lu=tF.addEvent,lf=tF.extend,lg=tF.merge,lv=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ll(e,t),e.prototype.applyJitter=function(){var t=this,e=this.options.jitter,i=this.points.length;e&&this.points.forEach(function(o,n){["x","y"].forEach(function(r,s){if(e[r]&&!o.isNull){var a="plot".concat(r.toUpperCase()),h=t[""+r+"Axis"],l=e[r]*h.transA;if(h&&!h.logarithmic){var d,c=Math.max(0,(o[a]||0)-l),p=Math.min(h.len,(o[a]||0)+l);o[a]=c+(p-c)*((d=1e4*Math.sin(n+s*i))-Math.floor(d)),"x"===r&&(o.clientX=o.plotX)}}})})},e.prototype.drawGraph=function(){this.options.lineWidth?t.prototype.drawGraph.call(this):this.graph&&(this.graph=this.graph.destroy())},e.defaultOptions=lg(lp.defaultOptions,lh),e}(lp);lf(lv.prototype,{drawTracker:lc.prototype.drawTracker,sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","markerGroup","dataLabelsGroup"]}),lu(lv,"afterTranslate",function(){this.applyJitter()}),ss.registerSeriesType("scatter",lv);var lm=tv.deg2rad,ly=tF.fireEvent,lx=tF.isNumber,lb=tF.pick,lk=tF.relativeLength;(T=tt||(tt={})).getCenter=function(){var t,e,i,o=this.options,n=this.chart,r=2*(o.slicedOffset||0),s=n.plotWidth-2*r,a=n.plotHeight-2*r,h=o.center,l=Math.min(s,a),d=o.thickness,c=o.size,p=o.innerSize||0;"string"==typeof c&&(c=parseFloat(c)),"string"==typeof p&&(p=parseFloat(p));var u=[lb(null==h?void 0:h[0],"50%"),lb(null==h?void 0:h[1],"50%"),lb(c&&c<0?void 0:o.size,"100%"),lb(p&&p<0?void 0:o.innerSize||0,"0%")];for(!n.angular||this instanceof sW||(u[3]=0),e=0;e<4;++e)i=u[e],t=e<2||2===e&&/%$/.test(i),u[e]=lk(i,[s,a,l,u[2]][e])+(t?r:0);return u[3]>u[2]&&(u[3]=u[2]),lx(d)&&2*d<u[2]&&d>0&&(u[3]=u[2]-2*d),ly(this,"afterGetCenter",{positions:u}),u},T.getStartAndEndRadians=function(t,e){var i=lx(t)?t:0,o=lx(e)&&e>i&&e-i<360?e:i+360;return{start:lm*(i+-90),end:lm*(o+-90)}};var lM=tt,lw=(C=function(t,e){return(C=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}C(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lS=function(){return(lS=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},lA=tF.addEvent,lT=tF.defined,lC=tF.extend,lO=tF.isNumber,lP=tF.pick,lE=tF.relativeLength,lL=function(t){function e(e,i,o){var n,r=t.call(this,e,i,o)||this;r.half=0,null!==(n=r.name)&&void 0!==n||(r.name=e.chart.options.lang.pieSliceName);var s=function(t){r.slice("select"===t.type)};return lA(r,"select",s),lA(r,"unselect",s),r}return lw(e,t),e.prototype.getConnectorPath=function(t){var e=t.dataLabelPosition,i=t.options||{},o=i.connectorShape,n=this.connectorShapes[o]||o;return e&&n.call(this,lS(lS({},e.computed),{alignment:e.alignment}),e.connectorPosition,i)||[]},e.prototype.getTranslate=function(){return this.sliced&&this.slicedTranslation||{translateX:0,translateY:0}},e.prototype.haloPath=function(t){var e=this.shapeArgs;return this.sliced||!this.visible?[]:this.series.chart.renderer.symbols.arc(e.x,e.y,e.r+t,e.r+t,{innerR:e.r-1,start:e.start,end:e.end,borderRadius:e.borderRadius})},e.prototype.isValid=function(){return lO(this.y)&&this.y>=0},e.prototype.setVisible=function(t,e){void 0===e&&(e=!0),t!==this.visible&&this.update({visible:null!=t?t:!this.visible},e,void 0,!1)},e.prototype.slice=function(t,e,i){var o=this.series;eI(i,o.chart),e=lP(e,!0),this.sliced=this.options.sliced=t=lT(t)?t:!this.sliced,o.options.data[o.data.indexOf(this)]=this.options,this.graphic&&this.graphic.animate(this.getTranslate())},e}(rL);lC(lL.prototype,{connectorShapes:{fixedOffset:function(t,e,i){var o=e.breakAt,n=e.touchingSliceAt,r=i.softConnector?["C",t.x+("left"===t.alignment?-5:5),t.y,2*o.x-n.x,2*o.y-n.y,o.x,o.y]:["L",o.x,o.y];return[["M",t.x,t.y],r,["L",n.x,n.y]]},straight:function(t,e){var i=e.touchingSliceAt;return[["M",t.x,t.y],["L",i.x,i.y]]},crookedLine:function(t,e,i){var o=e.angle,n=void 0===o?this.angle||0:o,r=e.breakAt,s=e.touchingSliceAt,a=this.series,h=a.center,l=h[0],d=h[1],c=h[2]/2,p=a.chart,u=p.plotLeft,f=p.plotWidth,g="left"===t.alignment,v=t.x,m=t.y,y=r.x;if(i.crookDistance){var x=lE(i.crookDistance,1);y=g?l+c+(f+u-l-c)*(1-x):u+(l-c)*x}else y=l+(d-m)*Math.tan(n-Math.PI/2);var b=[["M",v,m]];return(g?y<=v&&y>=r.x:y>=v&&y<=r.x)&&b.push(["L",y,m]),b.push(["L",r.x,r.y],["L",s.x,s.y]),b}}});var lB={borderRadius:3,center:[null,null],clip:!1,colorByPoint:!0,dataLabels:{connectorPadding:5,connectorShape:"crookedLine",crookDistance:void 0,distance:30,enabled:!0,formatter:function(){return this.isNull?void 0:this.name},softConnector:!0,x:0},fillColor:void 0,ignoreHiddenPoint:!0,inactiveOtherPoints:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,stickyTracking:!1,tooltip:{followPointer:!0},borderColor:"#ffffff",borderWidth:1,lineWidth:void 0,states:{hover:{brightness:.1}}},lD=(O=function(t,e){return(O=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}O(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),lI=lM.getStartAndEndRadians,lz=tv.noop,lR=tF.clamp,lN=tF.extend,lW=tF.fireEvent,lG=tF.merge,lX=tF.pick,lH=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return lD(e,t),e.prototype.animate=function(t){var e=this,i=e.points,o=e.startAngleRad;t||i.forEach(function(t){var i=t.graphic,n=t.shapeArgs;i&&n&&(i.attr({r:lX(t.startR,e.center&&e.center[3]/2),start:o,end:o}),i.animate({r:n.r,start:n.start,end:n.end},e.options.animation))})},e.prototype.drawEmpty=function(){var t,e,i=this.startAngleRad,o=this.endAngleRad,n=this.options;0===this.total&&this.center?(t=this.center[0],e=this.center[1],this.graph||(this.graph=this.chart.renderer.arc(t,e,this.center[1]/2,0,i,o).addClass("highcharts-empty-series").add(this.group)),this.graph.attr({d:iJ.arc(t,e,this.center[2]/2,0,{start:i,end:o,innerR:this.center[3]/2})}),this.chart.styledMode||this.graph.attr({"stroke-width":n.borderWidth,fill:n.fillColor||"none",stroke:n.color||"#cccccc"})):this.graph&&(this.graph=this.graph.destroy())},e.prototype.drawPoints=function(){var t=this.chart.renderer;this.points.forEach(function(e){e.graphic&&e.hasNewShapeType()&&(e.graphic=e.graphic.destroy()),e.graphic||(e.graphic=t[e.shapeType](e.shapeArgs).add(e.series.group),e.delayedRendering=!0)})},e.prototype.generatePoints=function(){t.prototype.generatePoints.call(this),this.updateTotals()},e.prototype.getX=function(t,e,i,o){var n=this.center,r=this.radii?this.radii[i.index]||0:n[2]/2,s=o.dataLabelPosition,a=(null==s?void 0:s.distance)||0,h=Math.asin(lR((t-n[1])/(r+a),-1,1));return n[0]+Math.cos(h)*(r+a)*(e?-1:1)+(a>0?(e?-1:1)*(o.padding||0):0)},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.redrawPoints=function(){var t,e,i,o,n=this,r=n.chart;this.drawEmpty(),n.group&&!r.styledMode&&n.group.shadow(n.options.shadow),n.points.forEach(function(s){var a={};e=s.graphic,!s.isNull&&e?(o=s.shapeArgs,t=s.getTranslate(),r.styledMode||(i=n.pointAttribs(s,s.selected&&"select")),s.delayedRendering?(e.setRadialReference(n.center).attr(o).attr(t),r.styledMode||e.attr(i).attr({"stroke-linejoin":"round"}),s.delayedRendering=!1):(e.setRadialReference(n.center),r.styledMode||lG(!0,a,i),lG(!0,a,o,t),e.animate(a)),e.attr({visibility:s.visible?"inherit":"hidden"}),e.addClass(s.getClassName(),!0)):e&&(s.graphic=e.destroy())})},e.prototype.sortByAngle=function(t,e){t.sort(function(t,i){return void 0!==t.angle&&(i.angle-t.angle)*e})},e.prototype.translate=function(t){lW(this,"translate"),this.generatePoints();var e,i,o,n,r,s,a,h=this.options,l=h.slicedOffset,d=lI(h.startAngle,h.endAngle),c=this.startAngleRad=d.start,p=(this.endAngleRad=d.end)-c,u=this.points,f=h.ignoreHiddenPoint,g=u.length,v=0;for(t||(this.center=t=this.getCenter()),s=0;s<g;s++){a=u[s],e=c+v*p,a.isValid()&&(!f||a.visible)&&(v+=a.percentage/100),i=c+v*p;var m={x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2,start:Math.round(1e3*e)/1e3,end:Math.round(1e3*i)/1e3};a.shapeType="arc",a.shapeArgs=m,(o=(i+e)/2)>1.5*Math.PI?o-=2*Math.PI:o<-Math.PI/2&&(o+=2*Math.PI),a.slicedTranslation={translateX:Math.round(Math.cos(o)*l),translateY:Math.round(Math.sin(o)*l)},n=Math.cos(o)*t[2]/2,r=Math.sin(o)*t[2]/2,a.tooltipPos=[t[0]+.7*n,t[1]+.7*r],a.half=+(o<-Math.PI/2||o>Math.PI/2),a.angle=o}lW(this,"afterTranslate")},e.prototype.updateTotals=function(){var t,e,i=this.points,o=i.length,n=this.options.ignoreHiddenPoint,r=0;for(t=0;t<o;t++)(e=i[t]).isValid()&&(!n||e.visible)&&(r+=e.y);for(t=0,this.total=r;t<o;t++)(e=i[t]).percentage=r>0&&(e.visible||!n)?e.y/r*100:0,e.total=r},e.defaultOptions=lG(sW.defaultOptions,lB),e}(sW);lN(lH.prototype,{axisTypes:[],directTouch:!0,drawGraph:void 0,drawTracker:hq.prototype.drawTracker,getCenter:lM.getCenter,getSymbol:lz,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointAttribs:hq.prototype.pointAttribs,pointClass:lL,requireSorting:!1,searchPoint:lz,trackerGroups:["group","dataLabelsGroup"]}),ss.registerSeriesType("pie",lH);var lF=tv.composed,lY=tv.noop,l_=ir.distribute,lj=ss.series,lU=tF.arrayMax,lV=tF.clamp,lZ=tF.defined,lq=tF.pick,lK=tF.pushUnique,l$=tF.relativeLength;!function(t){var e={radialDistributionY:function(t,e){var i;return((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.top)||0)+t.distributeBox.pos},radialDistributionX:function(t,e,i,o,n){var r=n.dataLabelPosition;return t.getX(i<((null==r?void 0:r.top)||0)+2||i>((null==r?void 0:r.bottom)||0)-2?o:i,e.half,e,n)},justify:function(t,e,i,o){var n;return o[0]+(t.half?-1:1)*(i+((null===(n=e.dataLabelPosition)||void 0===n?void 0:n.distance)||0))},alignToPlotEdges:function(t,e,i,o){var n=t.getBBox().width;return e?n+o:i-n-o},alignToConnectors:function(t,e,i,o){var n,r=0;return t.forEach(function(t){(n=t.dataLabel.getBBox().width)>r&&(r=n)}),e?r+o:i-r-o}};function i(t,e){var i=Math.PI/2,o=t.shapeArgs||{},n=o.start,r=void 0===n?0:n,s=o.end,a=void 0===s?0:s,h=t.angle||0;e>0&&r<i&&a>i&&h>i/2&&h<1.5*i&&(h=h<=i?Math.max(i/2,(r+i)/2):Math.min(1.5*i,(i+a)/2));var l=this.center,d=this.options,c=l[2]/2,p=Math.cos(h),u=Math.sin(h),f=l[0]+p*c,g=l[1]+u*c,v=Math.min((d.slicedOffset||0)+(d.borderWidth||0),e/5);return{natural:{x:f+p*e,y:g+u*e},computed:{},alignment:e<0?"center":t.half?"right":"left",connectorPosition:{angle:h,breakAt:{x:f+p*v,y:g+u*v},touchingSliceAt:{x:f,y:g}},distance:e}}function o(){var t,e,i,o,n=this,r=this,s=r.points,a=r.chart,h=a.plotWidth,l=a.plotHeight,d=a.plotLeft,c=Math.round(a.chartWidth/3),p=r.center,u=p[2]/2,f=p[1],g=[[],[]],v=[0,0,0,0],m=r.dataLabelPositioners,y=0;r.visible&&(null===(t=r.hasDataLabels)||void 0===t?void 0:t.call(r))&&(s.forEach(function(t){(t.dataLabels||[]).forEach(function(t){t.shortened&&(t.attr({width:"auto"}).css({width:"auto",textOverflow:"clip"}),t.shortened=!1)})}),lj.prototype.drawDataLabels.apply(r),s.forEach(function(t){(t.dataLabels||[]).forEach(function(e,i){var o,r=p[2]/2,s=e.options,a=l$((null==s?void 0:s.distance)||0,r);0===i&&g[t.half].push(t),!lZ(null===(o=null==s?void 0:s.style)||void 0===o?void 0:o.width)&&e.getBBox().width>c&&(e.css({width:Math.round(.7*c)+"px"}),e.shortened=!0),e.dataLabelPosition=n.getDataLabelPosition(t,a),y=Math.max(y,a)})}),g.forEach(function(t,e){var n,s,c,g=t.length,x=[],b=0;g&&(r.sortByAngle(t,e-.5),y>0&&(n=Math.max(0,f-u-y),s=Math.min(f+u+y,a.plotHeight),t.forEach(function(t){(t.dataLabels||[]).forEach(function(e){var i,o=e.dataLabelPosition;o&&o.distance>0&&(o.top=Math.max(0,f-u-o.distance),o.bottom=Math.min(f+u+o.distance,a.plotHeight),b=e.getBBox().height||21,e.lineHeight=a.renderer.fontMetrics(e.text||e).h+2*e.padding,t.distributeBox={target:((null===(i=e.dataLabelPosition)||void 0===i?void 0:i.natural.y)||0)-o.top+e.lineHeight/2,size:b,rank:t.y},x.push(t.distributeBox))})}),l_(x,c=s+b-n,c/5)),t.forEach(function(n){(n.dataLabels||[]).forEach(function(s){var a=s.options||{},c=n.distributeBox,f=s.dataLabelPosition,g=(null==f?void 0:f.natural.y)||0,y=a.connectorPadding||0,b=s.lineHeight||21,k=(b-s.getBBox().height)/2,M=0,w=g,S="inherit";if(f){if(x&&lZ(c)&&f.distance>0&&(void 0===c.pos?S="hidden":(o=c.size,w=m.radialDistributionY(n,s))),a.justify)M=m.justify(n,s,u,p);else switch(a.alignTo){case"connectors":M=m.alignToConnectors(t,e,h,d);break;case"plotEdges":M=m.alignToPlotEdges(s,e,h,d);break;default:M=m.radialDistributionX(r,n,w-k,g,s)}if(f.attribs={visibility:S,align:f.alignment},f.posAttribs={x:M+(a.x||0)+(({left:y,right:-y})[f.alignment]||0),y:w+(a.y||0)-b/2},f.computed.x=M,f.computed.y=w-k,lq(a.crop,!0)){i=s.getBBox().width;var A=void 0;M-i<y&&1===e?(A=Math.round(i-M+y),v[3]=Math.max(A,v[3])):M+i>h-y&&0===e&&(A=Math.round(M+i-h+y),v[1]=Math.max(A,v[1])),w-o/2<0?v[0]=Math.max(Math.round(-w+o/2),v[0]):w+o/2>l&&(v[2]=Math.max(Math.round(w+o/2-l),v[2])),f.sideOverflow=A}}})}))}),(0===lU(v)||this.verifyDataLabelOverflow(v))&&(this.placeDataLabels(),this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(i){var o,n=i.options||{},s=n.connectorColor,h=n.connectorWidth,l=void 0===h?1:h,d=i.dataLabelPosition;if(l){var c=void 0;e=i.connector,d&&d.distance>0?(c=!e,e||(i.connector=e=a.renderer.path().addClass("highcharts-data-label-connector  highcharts-color-"+t.colorIndex+(t.className?" "+t.className:"")).add(r.dataLabelsGroup)),a.styledMode||e.attr({"stroke-width":l,stroke:s||t.color||"#666666"}),e[c?"attr":"animate"]({d:t.getConnectorPath(i)}),e.attr({visibility:null===(o=d.attribs)||void 0===o?void 0:o.visibility})):e&&(i.connector=e.destroy())}})})))}function n(){this.points.forEach(function(t){(t.dataLabels||[]).forEach(function(t){var e,i,o=t.dataLabelPosition;o?(o.sideOverflow&&(t.css({width:Math.max(t.getBBox().width-o.sideOverflow,0)+"px",textOverflow:(null===(i=null===(e=t.options)||void 0===e?void 0:e.style)||void 0===i?void 0:i.textOverflow)||"ellipsis"}),t.shortened=!0),t.attr(o.attribs),t[t.moved?"animate":"attr"](o.posAttribs),t.moved=!0):t&&t.attr({y:-9999})}),delete t.distributeBox},this)}function r(t){var e=this.center,i=this.options,o=i.center,n=i.minSize||80,r=n,s=null!==i.size;return!s&&(null!==o[0]?r=Math.max(e[2]-Math.max(t[1],t[3]),n):(r=Math.max(e[2]-t[1]-t[3],n),e[0]+=(t[3]-t[1])/2),null!==o[1]?r=lV(r,n,e[2]-Math.max(t[0],t[2])):(r=lV(r,n,e[2]-t[0]-t[2]),e[1]+=(t[0]-t[2])/2),r<e[2]?(e[2]=r,e[3]=Math.min(i.thickness?Math.max(0,r-2*i.thickness):Math.max(0,l$(i.innerSize||0,r)),r),this.translate(e),this.drawDataLabels&&this.drawDataLabels()):s=!0),s}t.compose=function(t){if(h8.compose(lj),lK(lF,"PieDataLabel")){var s=t.prototype;s.dataLabelPositioners=e,s.alignDataLabel=lY,s.drawDataLabels=o,s.getDataLabelPosition=i,s.placeDataLabels=n,s.verifyDataLabelOverflow=r}}}(te||(te={}));var lJ=te;(P=ti||(ti={})).getCenterOfPoints=function(t){var e=t.reduce(function(t,e){return t.x+=e.x,t.y+=e.y,t},{x:0,y:0});return{x:e.x/t.length,y:e.y/t.length}},P.getDistanceBetweenPoints=function(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},P.getAngleBetweenPoints=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)},P.pointInPolygon=function(t,e){var i,o,n=t.x,r=t.y,s=e.length,a=!1;for(i=0,o=s-1;i<s;o=i++){var h=e[i],l=h[0],d=h[1],c=e[o],p=c[0],u=c[1];d>r!=u>r&&n<(p-l)*(r-d)/(u-d)+l&&(a=!a)}return a};var lQ=ti.pointInPolygon,l0=tF.addEvent,l1=tF.getAlignFactor,l2=tF.fireEvent,l3=tF.objectEach,l5=tF.pick;function l6(t){for(var e,i,o,n,r,s=t.length,a=!1,h=0;h<s;h++)(e=t[h])&&(e.oldOpacity=e.opacity,e.newOpacity=1,e.absoluteBox=function(t){var e,i;if(t&&(!t.alignAttr||t.placed)){var o=t.box?0:t.padding||0,n=t.alignAttr||{x:t.attr("x"),y:t.attr("y")},r=t.getBBox(),s=r.height,a=r.polygon,h=r.width,l=l1(t.alignValue)*h;return t.width=h,t.height=s,{x:n.x+((null===(e=t.parentGroup)||void 0===e?void 0:e.translateX)||0)+o-l,y:n.y+((null===(i=t.parentGroup)||void 0===i?void 0:i.translateY)||0)+o,width:h-2*o,height:s-2*o,polygon:a}}}(e));t.sort(function(t,e){return(e.labelrank||0)-(t.labelrank||0)});for(var h=0;h<s;++h)for(var l=null==(n=(i=t[h])&&i.absoluteBox)?void 0:n.polygon,d=h+1;d<s;++d){r=(o=t[d])&&o.absoluteBox;var c=!1;if(n&&r&&i!==o&&0!==i.newOpacity&&0!==o.newOpacity&&"hidden"!==i.visibility&&"hidden"!==o.visibility){var p=r.polygon;if(l&&p&&l!==p?function(t,e){for(var i=0;i<t.length;i++){var o=t[i];if(lQ({x:o[0],y:o[1]},e))return!0}return!1}(l,p)&&(c=!0):!(r.x>=n.x+n.width||r.x+r.width<=n.x||r.y>=n.y+n.height||r.y+r.height<=n.y)&&(c=!0),c){var u=i.labelrank<o.labelrank?i:o,f=u.text;u.newOpacity=0,(null==f?void 0:f.element.querySelector("textPath"))&&f.hide()}}}for(var g=0;g<t.length;g++)l9(t[g],this)&&(a=!0);a&&l2(this,"afterHideAllOverlappingLabels")}function l9(t,e){var i,o=!1;return t&&(i=t.newOpacity,t.oldOpacity!==i&&(t.hasClass("highcharts-data-label")?(t[i?"removeClass":"addClass"]("highcharts-data-label-hidden"),o=!0,t[t.isOld?"animate":"attr"]({opacity:i},void 0,function(){e.styledMode||t.css({pointerEvents:i?"auto":"none"})}),l2(e,"afterHideOverlappingLabel")):t.attr({opacity:i})),t.isOld=!0),o}function l4(){for(var t,e=this,i=[],o=0,n=e.labelCollectors||[];o<n.length;o++){var r=n[o];i=i.concat(r())}for(var s=0,a=e.yAxis||[];s<a.length;s++){var h=a[s];h.stacking&&h.options.stackLabels&&!h.options.stackLabels.allowOverlap&&l3(h.stacking.stacks,function(t){l3(t,function(t){t.label&&i.push(t.label)})})}for(var l=0,d=e.series||[];l<d.length;l++){var c=d[l];if(c.visible&&(null===(t=c.hasDataLabels)||void 0===t?void 0:t.call(c))){var p=function(t){for(var o=function(t){t.visible&&(t.dataLabels||[]).forEach(function(o){var n,r,s=o.options||{};o.labelrank=l5(s.labelrank,t.labelrank,null===(n=t.shapeArgs)||void 0===n?void 0:n.height),(null!==(r=s.allowOverlap)&&void 0!==r?r:Number(s.distance)>0)?(o.oldOpacity=o.opacity,o.newOpacity=1,l9(o,e)):i.push(o)})},n=0;n<t.length;n++)o(t[n])};p(c.nodes||[]),p(c.points)}}this.hideOverlappingLabels(i)}var l8={compose:function(t){var e=t.prototype;e.hideOverlappingLabels||(e.hideOverlappingLabels=l6,l0(t,"render",l4))}},l7=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},dt=tv.noop,de=tF.addEvent,di=tF.extend,dn=tF.isObject,dr=tF.merge,ds=tF.relativeLength,da={radius:0,scope:"stack",where:void 0},dh=dt,dl=dt;function dd(t,e,i,o,n){void 0===n&&(n={});var r=dh(t,e,i,o,n),s=n.innerR,a=void 0===s?0:s,h=n.r,l=void 0===h?i:h,d=n.start,c=n.end;if(n.open||!n.borderRadius)return r;for(var p=(void 0===c?0:c)-(void 0===d?0:d),u=Math.sin(p/2),f=Math.max(Math.min(ds(n.borderRadius||0,l-a),(l-a)/2,l*u/(1+u)),0),g=Math.min(f,p/Math.PI*2*a),v=r.length-1;v--;)!function(t,e,i){var o,n,r,s=t[e],a=t[e+1];if("Z"===a[0]&&(a=t[0]),("M"===s[0]||"L"===s[0])&&"A"===a[0]?(o=s,n=a,r=!0):"A"===s[0]&&("M"===a[0]||"L"===a[0])&&(o=a,n=s),o&&n&&n.params){var h=n[1],l=n[5],d=n.params,c=d.start,p=d.end,u=d.cx,f=d.cy,g=l?h-i:h+i,v=g?Math.asin(i/g):0,m=l?v:-v,y=Math.cos(v)*g;r?(d.start=c+m,o[1]=u+y*Math.cos(c),o[2]=f+y*Math.sin(c),t.splice(e+1,0,["A",i,i,0,0,1,u+h*Math.cos(d.start),f+h*Math.sin(d.start)])):(d.end=p-m,n[6]=u+h*Math.cos(d.end),n[7]=f+h*Math.sin(d.end),t.splice(e+1,0,["A",i,i,0,0,1,u+y*Math.cos(p),f+y*Math.sin(p)])),n[4]=Math.abs(d.end-d.start)<Math.PI?0:1}}(r,v,v>1?g:f);return r}function dc(){var t,e;if(this.options.borderRadius&&!(this.chart.is3d&&this.chart.is3d()))for(var i=this.options,o=this.yAxis,n="percent"===i.stacking,r=null===(e=null===(t=en.plotOptions)||void 0===t?void 0:t[this.type])||void 0===e?void 0:e.borderRadius,s=dp(i.borderRadius,dn(r)?r:{}),a=o.options.reversed,h=0,l=this.points;h<l.length;h++){var d=l[h],c=d.shapeArgs;if("roundedRect"===d.shapeType&&c){var p=c.width,u=void 0===p?0:p,f=c.height,g=void 0===f?0:f,v=c.y,m=void 0===v?0:v,y=g;if("stack"===s.scope&&d.stackTotal){var x=o.translate(n?100:d.stackTotal,!1,!0,!1,!0),b=o.translate(i.threshold||0,!1,!0,!1,!0),k=this.crispCol(0,Math.min(x,b),0,Math.abs(x-b));m=k.y,y=k.height}var M=(d.negative?-1:1)*(a?-1:1)==-1,w=s.where;!w&&this.is("waterfall")&&Math.abs((d.yBottom||0)-(this.translatedThreshold||0))>this.borderWidth&&(w="all"),w||(w="end");var S=Math.min(ds(s.radius,u),u/2,"all"===w?g/2:1/0)||0;"end"===w&&(M&&(m-=S),y+=S),di(c,{brBoxHeight:y,brBoxY:m,r:S})}}}function dp(t,e){return dn(t)||(t={radius:t||0}),dr(da,e,t)}function du(){for(var t=dp(this.options.borderRadius),e=0,i=this.points;e<i.length;e++){var o=i[e].shapeArgs;o&&(o.borderRadius=ds(t.radius,(o.r||0)-(o.innerR||0)))}}function df(t,e,i,o,n){void 0===n&&(n={});var r=dl(t,e,i,o,n),s=n.r,a=void 0===s?0:s,h=n.brBoxHeight,l=void 0===h?o:h,d=n.brBoxY,c=void 0===d?e:d,p=e-c,u=c+l-(e+o),f=p-a>-.1?0:a,g=u-a>-.1?0:a,v=Math.max(f&&p,0),m=Math.max(g&&u,0),y=[t+f,e],x=[t+i-f,e],b=[t+i,e+f],k=[t+i,e+o-g],M=[t+i-g,e+o],w=[t+g,e+o],S=[t,e+o-g],A=[t,e+f],T=function(t,e){return Math.sqrt(Math.pow(t,2)-Math.pow(e,2))};if(v){var C=T(f,f-v);y[0]-=C,x[0]+=C,b[1]=A[1]=e+f-v}if(o<f-v){var C=T(f,f-v-o);b[0]=k[0]=t+i-f+C,M[0]=Math.min(b[0],M[0]),w[0]=Math.max(k[0],w[0]),S[0]=A[0]=t+f-C,b[1]=A[1]=e+o}if(m){var C=T(g,g-m);M[0]+=C,w[0]-=C,k[1]=S[1]=e+o-g+m}if(o<g-m){var C=T(g,g-m-o);b[0]=k[0]=t+i-g+C,x[0]=Math.min(b[0],x[0]),y[0]=Math.max(k[0],y[0]),S[0]=A[0]=t+g-C,k[1]=S[1]=e}return r.length=0,r.push(l7(["M"],y,!0),l7(["L"],x,!0),l7(["A",f,f,0,0,1],b,!0),l7(["L"],k,!0),l7(["A",g,g,0,0,1],M,!0),l7(["L"],w,!0),l7(["A",g,g,0,0,1],S,!0),l7(["L"],A,!0),l7(["A",f,f,0,0,1],y,!0),["Z"]),r}var dg=tF.diffObjects,dv=tF.extend,dm=tF.find,dy=tF.merge,dx=tF.pick,db=tF.uniqueKey;!function(t){function e(t,e){var i=t.condition;(i.callback||function(){return this.chartWidth<=dx(i.maxWidth,Number.MAX_VALUE)&&this.chartHeight<=dx(i.maxHeight,Number.MAX_VALUE)&&this.chartWidth>=dx(i.minWidth,0)&&this.chartHeight>=dx(i.minHeight,0)}).call(this)&&e.push(t._id)}function i(t,e){var i,o=this,n=this.options.responsive,r=this.currentResponsive,s=[];!e&&n&&n.rules&&n.rules.forEach(function(t){void 0===t._id&&(t._id=db()),o.matchResponsiveRule(t,s)},this);var a=dy.apply(void 0,s.map(function(t){return dm((null==n?void 0:n.rules)||[],function(e){return e._id===t})}).map(function(t){return null==t?void 0:t.chartOptions}));a.isResponsiveOptions=!0,s=s.toString()||void 0;var h=null==r?void 0:r.ruleIds;s===h||(r&&(this.currentResponsive=void 0,this.updatingResponsive=!0,this.update(r.undoOptions,t,!0),this.updatingResponsive=!1),s?((i=dg(a,this.options,!0,this.collectionsWithUpdate)).isResponsiveOptions=!0,this.currentResponsive={ruleIds:s,mergedOptions:a,undoOptions:i},this.updatingResponsive||this.update(a,t,!0)):this.currentResponsive=void 0)}t.compose=function(t){var o=t.prototype;return o.matchResponsiveRule||dv(o,{matchResponsiveRule:e,setResponsive:i}),t}}(to||(to={}));var dk=to;tv.AST=eZ,tv.Axis=nP,tv.Chart=aD,tv.Color=ev,tv.DataLabel=h8,tv.DataTableCore=r6,tv.Fx=ek,tv.HTMLElement=o_,tv.Legend=s6,tv.LegendSymbol=st,tv.OverlappingDataLabels=tv.OverlappingDataLabels||l8,tv.PlotLineOrBand=n0,tv.Point=rL,tv.Pointer=r$,tv.RendererRegistry=e7,tv.Series=sW,tv.SeriesRegistry=ss,tv.StackItem=aJ,tv.SVGElement=iR,tv.SVGRenderer=oO,tv.Templating=e8,tv.Tick=nt,tv.Time=t7,tv.Tooltip=rf,tv.animate=eB,tv.animObject=eE,tv.chart=aD.chart,tv.color=ev.parse,tv.dateFormat=e8.dateFormat,tv.defaultOptions=en,tv.distribute=ir.distribute,tv.format=e8.format,tv.getDeferredAnimation=eD,tv.getOptions=es,tv.numberFormat=e8.numberFormat,tv.seriesType=ss.seriesType,tv.setAnimation=eI,tv.setOptions=ea,tv.stop=eL,tv.time=er,tv.timers=ek.timers,({compose:function(t,e,i){var o=t.types.pie;if(!e.symbolCustomAttribs.includes("borderRadius")){var n=i.prototype.symbols;de(t,"afterColumnTranslate",dc,{order:9}),de(o,"afterTranslate",du),e.symbolCustomAttribs.push("borderRadius","brBoxHeight","brBoxY"),dh=n.arc,dl=n.roundedRect,n.arc=dd,n.roundedRect=df}},optionsToObject:dp}).compose(tv.Series,tv.SVGElement,tv.SVGRenderer),lo.compose(tv.Series.types.column),h8.compose(tv.Series),nI.compose(tv.Axis),o_.compose(tv.SVGRenderer),s6.compose(tv.Chart),nW.compose(tv.Axis),l8.compose(tv.Chart),lJ.compose(tv.Series.types.pie),n0.compose(tv.Chart,tv.Axis),r$.compose(tv.Chart),dk.compose(tv.Chart),a_.compose(tv.Axis,tv.Chart,tv.Series),ha.compose(tv.Axis,tv.Chart,tv.Series),rf.compose(tv.Pointer),tF.extend(tv,tF);var dM=rL.prototype.tooltipFormatter,dw=tF.addEvent,dS=tF.arrayMax,dA=tF.arrayMin,dT=tF.correctFloat,dC=tF.defined,dO=tF.isArray,dP=tF.isNumber,dE=tF.isString,dL=tF.pick;!function(t){function e(t,e,i){!this.isXAxis&&(this.series.forEach(function(i){"compare"===t&&"boolean"!=typeof e?i.setCompare(e,!1):"cumulative"!==t||dE(e)||i.setCumulative(e,!1)}),dL(i,!0)&&this.chart.redraw())}function i(t){var e=this,i=e.series.chart.numberFormatter,o=function(o){t=t.replace("{point."+o+"}",(e[o]>0&&"change"===o?"+":"")+i(e[o],dL(e.series.tooltipOptions.changeDecimals,2)))};return dC(e.change)&&o("change"),dC(e.cumulativeSum)&&o("cumulativeSum"),dM.apply(this,[t])}function o(){var t,e=this.options.compare;("percent"===e||"value"===e||this.options.cumulative)&&(t=new d(this),"percent"===e||"value"===e?t.initCompare(e):t.initCumulative()),this.dataModify=t}function n(t){var e=t.dataExtremes,i=e.activeYData;if(this.dataModify&&e){var o=void 0;this.options.compare?o=[this.dataModify.modifyValue(e.dataMin),this.dataModify.modifyValue(e.dataMax)]:this.options.cumulative&&dO(i)&&i.length>=2&&(o=d.getCumulativeExtremes(i)),o&&(e.dataMin=dA(o),e.dataMax=dS(o))}}function r(t,e){this.options.compare=this.userOptions.compare=t,this.update({},dL(e,!0)),this.dataModify&&("value"===t||"percent"===t)?this.dataModify.initCompare(t):this.points.forEach(function(t){delete t.change})}function s(){var t=this.getColumn(this.pointArrayMap&&(this.options.pointValKey||this.pointValKey)||"y",!0);if(this.xAxis&&t.length&&this.dataModify)for(var e=this.getColumn("x",!0),i=this.dataTable.rowCount,o=+(!0!==this.options.compareStart),n=0;n<i-o;n++){var r=t[n];if(dP(r)&&0!==r&&e[n+o]>=(this.xAxis.min||0)){this.dataModify.compareValue=r;break}}}function a(t,e){this.setModifier("compare",t,e)}function h(t,e){t=dL(t,!1),this.options.cumulative=this.userOptions.cumulative=t,this.update({},dL(e,!0)),this.dataModify?this.dataModify.initCumulative():this.points.forEach(function(t){delete t.cumulativeSum})}function l(t,e){this.setModifier("cumulative",t,e)}t.compose=function(t,d,c){var p=d.prototype,u=c.prototype,f=t.prototype;return f.setCompare||(f.setCompare=r,f.setCumulative=h,dw(t,"afterInit",o),dw(t,"afterGetExtremes",n),dw(t,"afterProcessData",s)),p.setCompare||(p.setCompare=a,p.setModifier=e,p.setCumulative=l,u.tooltipFormatter=i),t};var d=function(){function t(t){this.series=t}return t.prototype.modifyValue=function(){return 0},t.getCumulativeExtremes=function(t){var e=1/0,i=-1/0;return t.reduce(function(t,o){var n=t+o;return e=Math.min(e,n,t),i=Math.max(i,n,t),n}),[e,i]},t.prototype.initCompare=function(t){this.modifyValue=function(e,i){null===e&&(e=0);var o=this.compareValue;if(void 0!==e&&void 0!==o){if("value"===t?e-=o:e=e/o*100-100*(100!==this.series.options.compareBase),void 0!==i){var n=this.series.points[i];n&&(n.change=e)}return e}return 0}},t.prototype.initCumulative=function(){this.modifyValue=function(t,e){if(null===t&&(t=0),void 0!==t&&void 0!==e){var i=e>0?this.series.points[e-1]:null;i&&i.cumulativeSum&&(t=dT(i.cumulativeSum+t));var o=this.series.points[e],n=o.series.options.cumulativeStart,r=o.x<=this.series.xAxis.max&&o.x>=this.series.xAxis.min;return o&&(!n||r?o.cumulativeSum=t:o.cumulativeSum=void 0),t}return 0}},t}();t.Additions=d}(tn||(tn={}));var dB=tn,dD=tv.isTouchDevice,dI=tF.addEvent,dz=tF.merge,dR=tF.pick,dN=[];function dW(){this.navigator&&this.navigator.setBaseSeries(null,!1)}function dG(){var t,e,i,o,n=this.legend,r=this.navigator;if(r){e=n&&n.options,i=r.xAxis,o=r.yAxis;var s=r.scrollbarHeight,a=r.scrollButtonSize;this.inverted?(r.left=r.opposite?this.chartWidth-s-r.height:this.spacing[3]+s,r.top=this.plotTop+a):(r.left=dR(i.left,this.plotLeft+a),r.top=r.navigatorOptions.top||this.chartHeight-r.height-s-((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)-this.spacing[2]-(this.rangeSelector&&this.extraBottomMargin?this.rangeSelector.getHeight():0)-(e&&"bottom"===e.verticalAlign&&"proximate"!==e.layout&&e.enabled&&!e.floating?n.legendHeight+dR(e.margin,10):0)-(this.titleOffset?this.titleOffset[2]:0)),i&&o&&(this.inverted?i.options.left=o.options.left=r.left:i.options.top=o.options.top=r.top,i.setAxisSize(),o.setAxisSize())}}function dX(t){!this.navigator&&!this.scroller&&(this.options.navigator.enabled||this.options.scrollbar.enabled)&&(this.scroller=this.navigator=new tr(this),dR(t.redraw,!0)&&this.redraw(t.animation))}function dH(){var t=this.options;(t.navigator.enabled||t.scrollbar.enabled)&&(this.scroller=this.navigator=new tr(this))}function dF(){var t=this.options,e=t.navigator,i=t.rangeSelector;if((e&&e.enabled||i&&i.enabled)&&(!dD&&"x"===this.zooming.type||dD&&"x"===this.zooming.pinchType))return!1}function dY(t){var e=t.navigator;if(e&&t.xAxis[0]){var i=t.xAxis[0].getExtremes();e.render(i.min,i.max)}}function d_(t){var e=t.options.navigator||{},i=t.options.scrollbar||{};!this.navigator&&!this.scroller&&(e.enabled||i.enabled)&&(dz(!0,this.options.navigator,e),dz(!0,this.options.scrollbar,i),delete t.options.navigator,delete t.options.scrollbar)}var dj=function(t,e){if(tF.pushUnique(dN,t)){var i=t.prototype;tr=e,i.callbacks.push(dY),dI(t,"afterAddSeries",dW),dI(t,"afterSetChartSize",dG),dI(t,"afterUpdate",dX),dI(t,"beforeRender",dH),dI(t,"beforeShowResetZoom",dF),dI(t,"update",d_)}},dU=tv.isTouchDevice,dV=tF.addEvent,dZ=tF.correctFloat,dq=tF.defined,dK=tF.isNumber,d$=tF.pick;function dJ(){this.navigatorAxis||(this.navigatorAxis=new d0(this))}function dQ(t){var e,i=this.chart,o=i.options,n=o.navigator,r=this.navigatorAxis,s=i.zooming.pinchType,a=o.rangeSelector,h=i.zooming.type;if(this.isXAxis&&((null==n?void 0:n.enabled)||(null==a?void 0:a.enabled))){if("y"===h&&"zoom"===t.trigger)e=!1;else if(("zoom"===t.trigger&&"xy"===h||dU&&"xy"===s)&&this.options.range){var l=r.previousZoom;dq(t.min)?r.previousZoom=[this.min,this.max]:l&&(t.min=l[0],t.max=l[1],r.previousZoom=void 0)}}void 0!==e&&t.preventDefault()}var d0=function(){function t(t){this.axis=t}return t.compose=function(t){t.keepProps.includes("navigatorAxis")||(t.keepProps.push("navigatorAxis"),dV(t,"init",dJ),dV(t,"setExtremes",dQ))},t.prototype.destroy=function(){this.axis=void 0},t.prototype.toFixedRange=function(t,e,i,o){var n=this.axis,r=(n.pointRange||0)/2,s=d$(i,n.translate(t,!0,!n.horiz)),a=d$(o,n.translate(e,!0,!n.horiz));return dq(i)||(s=dZ(s+r)),dq(o)||(a=dZ(a-r)),dK(s)&&dK(a)||(s=a=void 0),{min:s,max:a}},t}(),d1=ev.parse,d2=ss.seriesTypes,d3={height:40,margin:22,maskInside:!0,handles:{width:7,borderRadius:0,height:15,symbols:["navigator-handle","navigator-handle"],enabled:!0,lineWidth:1,backgroundColor:"#f2f2f2",borderColor:"#999999"},maskFill:d1("#667aff").setOpacity(.3).get(),outlineColor:"#999999",outlineWidth:1,series:{type:void 0===d2.areaspline?"line":"areaspline",fillOpacity:.05,lineWidth:1,compare:null,sonification:{enabled:!1},dataGrouping:{approximation:"average",enabled:!0,groupPixelWidth:2,firstAnchor:"firstPoint",anchor:"middle",lastAnchor:"lastPoint",units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2,3,4]],["week",[1,2,3]],["month",[1,3,6]],["year",null]]},dataLabels:{enabled:!1,zIndex:2},id:"highcharts-navigator-series",className:"highcharts-navigator-series",lineColor:null,marker:{enabled:!1},threshold:null},xAxis:{className:"highcharts-navigator-xaxis",tickLength:0,lineWidth:0,gridLineColor:"#e6e6e6",id:"navigator-x-axis",gridLineWidth:1,tickPixelInterval:200,labels:{align:"left",style:{color:"#000000",fontSize:"0.7em",opacity:.6,textOutline:"2px contrast"},x:3,y:-4},crosshair:!1},yAxis:{className:"highcharts-navigator-yaxis",gridLineWidth:0,startOnTick:!1,endOnTick:!1,minPadding:.1,id:"navigator-y-axis",maxPadding:.1,labels:{enabled:!1},crosshair:!1,title:{text:void 0},tickLength:0,tickWidth:0}},d5=function(t,e,i){if(i||2==arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))},d6=tF.relativeLength,d9={"navigator-handle":function(t,e,i,o,n){void 0===n&&(n={});var r=n.width?n.width/2:i,s=d6(n.borderRadius||0,Math.min(2*r,o));return d5([["M",-1.5,(o=n.height||o)/2-3.5],["L",-1.5,o/2+4.5],["M",.5,o/2-3.5],["L",.5,o/2+4.5]],iJ.rect(-r-1,.5,2*r+1,o,{r:s}),!0)}},d4=tF.defined,d8={setFixedRange:function(t){var e=this.xAxis[0];d4(e.dataMax)&&d4(e.dataMin)&&t?this.fixedRange=Math.min(t,e.dataMax-e.dataMin):this.fixedRange=t}},d7=tv.composed,ct=e7.getRendererType,ce=d8.setFixedRange,ci=tF.addEvent,co=tF.extend,cn=tF.pushUnique;function cr(){this.chart.navigator&&!this.options.isInternal&&this.chart.navigator.setBaseSeries(null,!1)}var cs=function(t,e,i){d0.compose(e),cn(d7,"Navigator")&&(t.prototype.setFixedRange=ce,co(ct().prototype.symbols,d9),ci(i,"afterUpdate",cr),ea({navigator:d3}))},ca=tv.composed,ch=tF.addEvent,cl=tF.defined,cd=tF.pick,cc=tF.pushUnique;!function(t){var e;function i(t){var e,i,o=cd(null===(e=t.options)||void 0===e?void 0:e.min,t.min),n=cd(null===(i=t.options)||void 0===i?void 0:i.max,t.max);return{axisMin:o,axisMax:n,scrollMin:cl(t.dataMin)?Math.min(o,t.min,t.dataMin,cd(t.threshold,1/0)):o,scrollMax:cl(t.dataMax)?Math.max(n,t.max,t.dataMax,cd(t.threshold,-1/0)):n}}function o(){var t=this.scrollbar,e=t&&!t.options.opposite,i=this.horiz?2:e?3:1;t&&(this.chart.scrollbarsOffsets=[0,0],this.chart.axisOffset[i]+=t.size+(t.options.margin||0))}function n(){var t,o,n=this;(null===(o=null===(t=n.options)||void 0===t?void 0:t.scrollbar)||void 0===o?void 0:o.enabled)&&(n.options.scrollbar.vertical=!n.horiz,n.options.startOnTick=n.options.endOnTick=!1,n.scrollbar=new e(n.chart.renderer,n.options.scrollbar,n.chart),ch(n.scrollbar,"changed",function(t){var e,o,r=i(n),s=r.axisMin,a=r.axisMax,h=r.scrollMin,l=r.scrollMax-h;if(cl(s)&&cl(a)){if(n.horiz&&!n.reversed||!n.horiz&&n.reversed?(e=h+l*this.to,o=h+l*this.from):(e=h+l*(1-this.from),o=h+l*(1-this.to)),this.shouldUpdateExtremes(t.DOMType)){var d="mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&void 0;n.setExtremes(o,e,!0,d,t)}else this.setRange(this.from,this.to)}}))}function r(){var t,e,o,n=i(this),r=n.scrollMin,s=n.scrollMax,a=this.scrollbar,h=this.axisTitleMargin+(this.titleOffset||0),l=this.chart.scrollbarsOffsets,d=this.options.margin||0;if(a&&l){if(this.horiz)this.opposite||(l[1]+=h),a.position(this.left,this.top+this.height+2+l[1]-(this.opposite?d:0),this.width,this.height),this.opposite||(l[1]+=d),t=1;else{this.opposite&&(l[0]+=h);var c=void 0;c=a.options.opposite?this.left+this.width+2+l[0]-(this.opposite?0:d):this.opposite?0:d,a.position(c,this.top,this.width,this.height),this.opposite&&(l[0]+=d),t=0}if(l[t]+=a.size+(a.options.margin||0),isNaN(r)||isNaN(s)||!cl(this.min)||!cl(this.max)||this.dataMin===this.dataMax)a.setRange(0,1);else if(this.min===this.max){var p=this.pointRange/(this.dataMax+1);e=p*this.min,o=p*(this.max+1),a.setRange(e,o)}else e=(this.min-r)/(s-r),o=(this.max-r)/(s-r),this.horiz&&!this.reversed||!this.horiz&&this.reversed?a.setRange(e,o):a.setRange(1-o,1-e)}}t.compose=function(t,i){cc(ca,"Axis.Scrollbar")&&(e=i,ch(t,"afterGetOffset",o),ch(t,"afterInit",n),ch(t,"afterRender",r))}}(ts||(ts={}));var cp=ts,cu={height:10,barBorderRadius:5,buttonBorderRadius:0,buttonsEnabled:!1,liveRedraw:void 0,margin:void 0,minWidth:6,opposite:!0,step:.2,zIndex:3,barBackgroundColor:"#cccccc",barBorderWidth:0,barBorderColor:"#cccccc",buttonArrowColor:"#333333",buttonBackgroundColor:"#e6e6e6",buttonBorderColor:"#cccccc",buttonBorderWidth:1,rifleColor:"none",trackBackgroundColor:"rgba(255, 255, 255, 0.001)",trackBorderColor:"#cccccc",trackBorderRadius:5,trackBorderWidth:1},cf=tF.addEvent,cg=tF.correctFloat,cv=tF.crisp,cm=tF.defined,cy=tF.destroyObjectProperties,cx=tF.fireEvent,cb=tF.merge,ck=tF.pick,cM=tF.removeEvent,cw=function(){function t(t,e,i){this._events=[],this.chartX=0,this.chartY=0,this.from=0,this.scrollbarButtons=[],this.scrollbarLeft=0,this.scrollbarStrokeWidth=1,this.scrollbarTop=0,this.size=0,this.to=0,this.trackBorderWidth=1,this.x=0,this.y=0,this.init(t,e,i)}return t.compose=function(e){cp.compose(e,t)},t.swapXY=function(t,e){return e&&t.forEach(function(t){for(var e,i=t.length,o=0;o<i;o+=2)"number"==typeof(e=t[o+1])&&(t[o+1]=t[o+2],t[o+2]=e)}),t},t.prototype.addEvents=function(){var t=this.options.inverted?[1,0]:[0,1],e=this.scrollbarButtons,i=this.scrollbarGroup.element,o=this.track.element,n=this.mouseDownHandler.bind(this),r=this.mouseMoveHandler.bind(this),s=this.mouseUpHandler.bind(this),a=[[e[t[0]].element,"click",this.buttonToMinClick.bind(this)],[e[t[1]].element,"click",this.buttonToMaxClick.bind(this)],[o,"click",this.trackClick.bind(this)],[i,"mousedown",n],[i.ownerDocument,"mousemove",r],[i.ownerDocument,"mouseup",s],[i,"touchstart",n],[i.ownerDocument,"touchmove",r],[i.ownerDocument,"touchend",s]];a.forEach(function(t){cf.apply(null,t)}),this._events=a},t.prototype.buttonToMaxClick=function(t){var e=(this.to-this.from)*ck(this.options.step,.2);this.updatePosition(this.from+e,this.to+e),cx(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.buttonToMinClick=function(t){var e=cg(this.to-this.from)*ck(this.options.step,.2);this.updatePosition(cg(this.from-e),cg(this.to-e)),cx(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.cursorToScrollbarPosition=function(t){var e=this.options,i=e.minWidth>this.calculatedWidth?e.minWidth:0;return{chartX:(t.chartX-this.x-this.xOffset)/(this.barWidth-i),chartY:(t.chartY-this.y-this.yOffset)/(this.barWidth-i)}},t.prototype.destroy=function(){var t=this,e=t.chart.scroller;t.removeEvents(),["track","scrollbarRifles","scrollbar","scrollbarGroup","group"].forEach(function(e){t[e]&&t[e].destroy&&(t[e]=t[e].destroy())}),e&&t===e.scrollbar&&(e.scrollbar=null,cy(e.scrollbarButtons))},t.prototype.drawScrollbarButton=function(e){var i=this.renderer,o=this.scrollbarButtons,n=this.options,r=this.size,s=i.g().add(this.group);if(o.push(s),n.buttonsEnabled){var a=i.rect().addClass("highcharts-scrollbar-button").add(s);this.chart.styledMode||a.attr({stroke:n.buttonBorderColor,"stroke-width":n.buttonBorderWidth,fill:n.buttonBackgroundColor}),a.attr(a.crisp({x:-.5,y:-.5,width:r,height:r,r:n.buttonBorderRadius},a.strokeWidth()));var h=i.path(t.swapXY([["M",r/2+(e?-1:1),r/2-3],["L",r/2+(e?-1:1),r/2+3],["L",r/2+(e?2:-2),r/2]],n.vertical)).addClass("highcharts-scrollbar-arrow").add(o[e]);this.chart.styledMode||h.attr({fill:n.buttonArrowColor})}},t.prototype.init=function(t,e,i){this.scrollbarButtons=[],this.renderer=t,this.userOptions=e,this.options=cb(cu,en.scrollbar,e),this.options.margin=ck(this.options.margin,10),this.chart=i,this.size=ck(this.options.size,this.options.height),e.enabled&&(this.render(),this.addEvents())},t.prototype.mouseDownHandler=function(t){var e,i=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,o=this.cursorToScrollbarPosition(i);this.chartX=o.chartX,this.chartY=o.chartY,this.initPositions=[this.from,this.to],this.grabbedCenter=!0},t.prototype.mouseMoveHandler=function(t){var e,i,o=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,n=this.options.vertical?"chartY":"chartX",r=this.initPositions||[];this.grabbedCenter&&(!t.touches||0!==t.touches[0][n])&&(i=this.cursorToScrollbarPosition(o)[n]-this[n],this.hasDragged=!0,this.updatePosition(r[0]+i,r[1]+i),this.hasDragged&&cx(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}))},t.prototype.mouseUpHandler=function(t){this.hasDragged&&cx(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMType:t.type,DOMEvent:t}),this.grabbedCenter=this.hasDragged=this.chartX=this.chartY=null},t.prototype.position=function(t,e,i,o){var n=this.options,r=n.buttonsEnabled,s=n.margin,a=void 0===s?0:s,h=n.vertical,l=this.rendered?"animate":"attr",d=o,c=0;this.group.show(),this.x=t,this.y=e+this.trackBorderWidth,this.width=i,this.height=o,this.xOffset=d,this.yOffset=c,h?(this.width=this.yOffset=i=c=this.size,this.xOffset=d=0,this.yOffset=c=r?this.size:0,this.barWidth=o-(r?2*i:0),this.x=t+=a):(this.height=o=this.size,this.xOffset=d=r?this.size:0,this.barWidth=i-(r?2*o:0),this.y=this.y+a),this.group[l]({translateX:t,translateY:this.y}),this.track[l]({width:i,height:o}),this.scrollbarButtons[1][l]({translateX:h?0:i-d,translateY:h?o-c:0})},t.prototype.removeEvents=function(){this._events.forEach(function(t){cM.apply(null,t)}),this._events.length=0},t.prototype.render=function(){var e=this.renderer,i=this.options,o=this.size,n=this.chart.styledMode,r=e.g("scrollbar").attr({zIndex:i.zIndex}).hide().add();this.group=r,this.track=e.rect().addClass("highcharts-scrollbar-track").attr({r:i.trackBorderRadius||0,height:o,width:o}).add(r),n||this.track.attr({fill:i.trackBackgroundColor,stroke:i.trackBorderColor,"stroke-width":i.trackBorderWidth});var s=this.trackBorderWidth=this.track.strokeWidth();this.track.attr({x:-cv(0,s),y:-cv(0,s)}),this.scrollbarGroup=e.g().add(r),this.scrollbar=e.rect().addClass("highcharts-scrollbar-thumb").attr({height:o-s,width:o-s,r:i.barBorderRadius||0}).add(this.scrollbarGroup),this.scrollbarRifles=e.path(t.swapXY([["M",-3,o/4],["L",-3,2*o/3],["M",0,o/4],["L",0,2*o/3],["M",3,o/4],["L",3,2*o/3]],i.vertical)).addClass("highcharts-scrollbar-rifles").add(this.scrollbarGroup),n||(this.scrollbar.attr({fill:i.barBackgroundColor,stroke:i.barBorderColor,"stroke-width":i.barBorderWidth}),this.scrollbarRifles.attr({stroke:i.rifleColor,"stroke-width":1})),this.scrollbarStrokeWidth=this.scrollbar.strokeWidth(),this.scrollbarGroup.translate(-cv(0,this.scrollbarStrokeWidth),-cv(0,this.scrollbarStrokeWidth)),this.drawScrollbarButton(0),this.drawScrollbarButton(1)},t.prototype.setRange=function(t,e){var i,o,n=this.options,r=n.vertical,s=n.minWidth,a=this.barWidth,h=!this.rendered||this.hasDragged||this.chart.navigator&&this.chart.navigator.hasDragged?"attr":"animate";if(cm(a)){var l=a*Math.min(e,1);i=Math.ceil(a*(t=Math.max(t,0))),this.calculatedWidth=o=cg(l-i),o<s&&(i=(a-s+o)*t,o=s);var d=Math.floor(i+this.xOffset+this.yOffset),c=o/2-.5;this.from=t,this.to=e,r?(this.scrollbarGroup[h]({translateY:d}),this.scrollbar[h]({height:o}),this.scrollbarRifles[h]({translateY:c}),this.scrollbarTop=d,this.scrollbarLeft=0):(this.scrollbarGroup[h]({translateX:d}),this.scrollbar[h]({width:o}),this.scrollbarRifles[h]({translateX:c}),this.scrollbarLeft=d,this.scrollbarTop=0),o<=12?this.scrollbarRifles.hide():this.scrollbarRifles.show(),!1===n.showFull&&(t<=0&&e>=1?this.group.hide():this.group.show()),this.rendered=!0}},t.prototype.shouldUpdateExtremes=function(t){return ck(this.options.liveRedraw,tv.svg&&!tv.isTouchDevice&&!this.chart.boosted)||"mouseup"===t||"touchend"===t||!cm(t)},t.prototype.trackClick=function(t){var e,i=(null===(e=this.chart.pointer)||void 0===e?void 0:e.normalize(t))||t,o=this.to-this.from,n=this.y+this.scrollbarTop,r=this.x+this.scrollbarLeft;this.options.vertical&&i.chartY>n||!this.options.vertical&&i.chartX>r?this.updatePosition(this.from+o,this.to+o):this.updatePosition(this.from-o,this.to-o),cx(this,"changed",{from:this.from,to:this.to,trigger:"scrollbar",DOMEvent:t})},t.prototype.update=function(t){this.destroy(),this.init(this.chart.renderer,cb(!0,this.options,t),this.chart)},t.prototype.updatePosition=function(t,e){e>1&&(t=cg(1-cg(e-t)),e=1),t<0&&(e=cg(e-t),t=0),this.from=t,this.to=e},t.defaultOptions=cu,t}();en.scrollbar=cb(!0,cw.defaultOptions,en.scrollbar);var cS=function(){return(cS=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},cA=tv.isTouchDevice,cT=oO.prototype.symbols,cC=tF.addEvent,cO=tF.clamp,cP=tF.correctFloat,cE=tF.defined,cL=tF.destroyObjectProperties,cB=tF.erase,cD=tF.extend,cI=tF.find,cz=tF.fireEvent,cR=tF.isArray,cN=tF.isNumber,cW=tF.merge,cG=tF.pick,cX=tF.removeEvent,cH=tF.splat;function cF(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var o=[].filter.call(e,cN);if(o.length)return Math[t].apply(0,o)}var cY=function(){function t(t){this.isDirty=!1,this.scrollbarHeight=0,this.init(t)}return t.compose=function(e,i,o){dj(e,t),cs(e,i,o)},t.prototype.drawHandle=function(t,e,i,o){var n=this.navigatorOptions.handles.height;this.handles[e][o](i?{translateX:Math.round(this.left+this.height/2),translateY:Math.round(this.top+parseInt(t,10)+.5-n)}:{translateX:Math.round(this.left+parseInt(t,10)),translateY:Math.round(this.top+this.height/2-n/2-1)})},t.prototype.drawOutline=function(t,e,i,o){var n,r,s=this.navigatorOptions.maskInside,a=this.outline.strokeWidth(),h=a/2,l=a%2/2,d=this.scrollButtonSize,c=this.size,p=this.top,u=this.height,f=p-h,g=p+u,v=this.left;i?(n=p+e+l,e=p+t+l,r=[["M",v+u,p-d-l],["L",v+u,n],["L",v,n],["M",v,e],["L",v+u,e],["L",v+u,p+c+d]],s&&r.push(["M",v+u,n-h],["L",v+u,e+h])):(v-=d,t+=v+d-l,e+=v+d-l,r=[["M",v,f],["L",t,f],["L",t,g],["M",e,g],["L",e,f],["L",v+c+2*d,f]],s&&r.push(["M",t-h,f],["L",e+h,f])),this.outline[o]({d:r})},t.prototype.drawMasks=function(t,e,i,o){var n,r,s,a,h=this.left,l=this.top,d=this.height;i?(s=[h,h,h],a=[l,l+t,l+e],r=[d,d,d],n=[t,e-t,this.size-e]):(s=[h,h+t,h+e],a=[l,l,l],r=[t,e-t,this.size-e],n=[d,d,d]),this.shades.forEach(function(t,e){t[o]({x:s[e],y:a[e],width:r[e],height:n[e]})})},t.prototype.renderElements=function(){var t,e,i=this,o=i.navigatorOptions,n=o.maskInside,r=i.chart,s=r.inverted,a=r.renderer,h={cursor:s?"ns-resize":"ew-resize"},l=null!==(t=i.navigatorGroup)&&void 0!==t?t:i.navigatorGroup=a.g("navigator").attr({zIndex:8,visibility:"hidden"}).add();if([!n,n,!n].forEach(function(t,e){var n,s=null!==(n=i.shades[e])&&void 0!==n?n:i.shades[e]=a.rect().addClass("highcharts-navigator-mask"+(1===e?"-inside":"-outside")).add(l);r.styledMode||(s.attr({fill:t?o.maskFill:"rgba(0,0,0,0)"}),1===e&&s.css(h))}),i.outline||(i.outline=a.path().addClass("highcharts-navigator-outline").add(l)),r.styledMode||i.outline.attr({"stroke-width":o.outlineWidth,stroke:o.outlineColor}),null===(e=o.handles)||void 0===e?void 0:e.enabled){var d=o.handles,c=d.height,p=d.width;[0,1].forEach(function(t){var e,o=d.symbols[t];if(i.handles[t]&&i.handles[t].symbolUrl===o){if(!i.handles[t].isImg&&i.handles[t].symbolName!==o){var n=cT[o].call(cT,-p/2-1,0,p,c);i.handles[t].attr({d:n}),i.handles[t].symbolName=o}}else null===(e=i.handles[t])||void 0===e||e.destroy(),i.handles[t]=a.symbol(o,-p/2-1,0,p,c,d),i.handles[t].attr({zIndex:7-t}).addClass("highcharts-navigator-handle highcharts-navigator-handle-"+["left","right"][t]).add(l),i.addMouseEvents();r.inverted&&i.handles[t].attr({rotation:90,rotationOriginX:Math.floor(-p/2),rotationOriginY:(c+p)/2}),r.styledMode||i.handles[t].attr({fill:d.backgroundColor,stroke:d.borderColor,"stroke-width":d.lineWidth,width:d.width,height:d.height,x:-p/2-1,y:0}).css(h)})}},t.prototype.update=function(t,e){var i,o,n,r,s=this;void 0===e&&(e=!1);var a=this.chart,h=a.options.chart.inverted!==(null===(n=a.scrollbar)||void 0===n?void 0:n.options.vertical);if(cW(!0,a.options.navigator,t),this.navigatorOptions=a.options.navigator||{},this.setOpposite(),cE(t.enabled)||h)return this.destroy(),this.navigatorEnabled=t.enabled||this.navigatorEnabled,this.init(a);if(this.navigatorEnabled&&(this.isDirty=!0,!1===t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){cX(t,"updatedData",s.updatedDataHandler)},this),t.adaptToUpdatedData&&this.baseSeries.forEach(function(t){t.eventsToUnbind.push(cC(t,"updatedData",s.updatedDataHandler))},this),(t.series||t.baseSeries)&&this.setBaseSeries(void 0,!1),t.height||t.xAxis||t.yAxis)){this.height=null!==(r=t.height)&&void 0!==r?r:this.height;var l=this.getXAxisOffsets();this.xAxis.update(cS(cS({},t.xAxis),((i={offsets:l})[a.inverted?"width":"height"]=this.height,i[a.inverted?"height":"width"]=void 0,i)),!1),this.yAxis.update(cS(cS({},t.yAxis),((o={})[a.inverted?"width":"height"]=this.height,o)),!1)}e&&a.redraw()},t.prototype.render=function(t,e,i,o){var n,r,s,a,h,l=this.chart,d=this.xAxis,c=d.pointRange||0,p=d.navigatorAxis.fake?l.xAxis[0]:d,u=this.navigatorEnabled,f=this.rendered,g=l.inverted,v=l.xAxis[0].minRange,m=l.xAxis[0].options.maxRange,y=this.scrollButtonSize,x=this.scrollbarHeight;if(!this.hasDragged||cE(i)){if(this.isDirty&&this.renderElements(),t=cP(t-c/2),e=cP(e+c/2),!cN(t)||!cN(e)){if(!f)return;i=0,o=cG(d.width,p.width)}this.left=cG(d.left,l.plotLeft+y+(g?l.plotWidth:0));var b=this.size=a=cG(d.len,(g?l.plotHeight:l.plotWidth)-2*y);n=g?x:a+2*y,i=cG(i,d.toPixels(t,!0)),o=cG(o,d.toPixels(e,!0)),cN(i)&&Math.abs(i)!==1/0||(i=0,o=n);var k=d.toValue(i,!0),M=d.toValue(o,!0),w=Math.abs(cP(M-k));w<v?this.grabbedLeft?i=d.toPixels(M-v-c,!0):this.grabbedRight&&(o=d.toPixels(k+v+c,!0)):cE(m)&&cP(w-c)>m&&(this.grabbedLeft?i=d.toPixels(M-m-c,!0):this.grabbedRight&&(o=d.toPixels(k+m+c,!0))),this.zoomedMax=cO(Math.max(i,o),0,b),this.zoomedMin=cO(this.fixedWidth?this.zoomedMax-this.fixedWidth:Math.min(i,o),0,b),this.range=this.zoomedMax-this.zoomedMin,b=Math.round(this.zoomedMax);var S=Math.round(this.zoomedMin);u&&(this.navigatorGroup.attr({visibility:"inherit"}),h=f&&!this.hasDragged?"animate":"attr",this.drawMasks(S,b,g,h),this.drawOutline(S,b,g,h),this.navigatorOptions.handles.enabled&&(this.drawHandle(S,0,g,h),this.drawHandle(b,1,g,h))),this.scrollbar&&(g?(s=this.top-y,r=this.left-x+(u||!p.opposite?0:(p.titleOffset||0)+p.axisTitleMargin),x=a+2*y):(s=this.top+(u?this.height:-x),r=this.left-y),this.scrollbar.position(r,s,n,x),this.scrollbar.setRange(this.zoomedMin/(a||1),this.zoomedMax/(a||1))),this.rendered=!0,this.isDirty=!1,cz(this,"afterRender")}},t.prototype.addMouseEvents=function(){var t,e,i=this,o=i.chart,n=o.container,r=[];i.mouseMoveHandler=t=function(t){i.onMouseMove(t)},i.mouseUpHandler=e=function(t){i.onMouseUp(t)},(r=i.getPartsEvents("mousedown")).push(cC(o.renderTo,"mousemove",t),cC(n.ownerDocument,"mouseup",e),cC(o.renderTo,"touchmove",t),cC(n.ownerDocument,"touchend",e)),r.concat(i.getPartsEvents("touchstart")),i.eventsToUnbind=r,i.series&&i.series[0]&&r.push(cC(i.series[0].xAxis,"foundExtremes",function(){o.navigator.modifyNavigatorAxisExtremes()}))},t.prototype.getPartsEvents=function(t){var e=this,i=[];return["shades","handles"].forEach(function(o){e[o].forEach(function(n,r){i.push(cC(n.element,t,function(t){e[o+"Mousedown"](t,r)}))})}),i},t.prototype.shadesMousedown=function(t,e){t=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t;var i,o,n,r,s,a=this.chart,h=this.xAxis,l=this.zoomedMin,d=this.size,c=this.range,p=this.left,u=t.chartX;a.inverted&&(u=t.chartY,p=this.top),1===e?(this.grabbedCenter=u,this.fixedWidth=c,this.dragOffset=u-l):(s=u-p-c/2,0===e?s=Math.max(0,s):2===e&&s+c>=d&&(s=d-c,this.reversedExtremes?(s-=c,n=this.getUnionExtremes().dataMin):o=this.getUnionExtremes().dataMax),s!==l&&(this.fixedWidth=c,cE((r=h.navigatorAxis.toFixedRange(s,s+c,n,o)).min)&&cz(this,"setRange",{min:Math.min(r.min,r.max),max:Math.max(r.min,r.max),redraw:!0,eventArguments:{trigger:"navigator"}})))},t.prototype.handlesMousedown=function(t,e){t=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(t))||t;var i,o=this.chart,n=o.xAxis[0],r=this.reversedExtremes;0===e?(this.grabbedLeft=!0,this.otherHandlePos=this.zoomedMax,this.fixedExtreme=r?n.min:n.max):(this.grabbedRight=!0,this.otherHandlePos=this.zoomedMin,this.fixedExtreme=r?n.max:n.min),o.setFixedRange(void 0)},t.prototype.onMouseMove=function(t){var e,i,o=this,n=o.chart,r=o.navigatorSize,s=o.range,a=o.dragOffset,h=n.inverted,l=o.left;(!t.touches||0!==t.touches[0].pageX)&&(i=(t=(null===(e=n.pointer)||void 0===e?void 0:e.normalize(t))||t).chartX,h&&(l=o.top,i=t.chartY),o.grabbedLeft?(o.hasDragged=!0,o.render(0,0,i-l,o.otherHandlePos)):o.grabbedRight?(o.hasDragged=!0,o.render(0,0,o.otherHandlePos,i-l)):o.grabbedCenter&&(o.hasDragged=!0,i<a?i=a:i>r+a-s&&(i=r+a-s),o.render(0,0,i-a,i-a+s)),o.hasDragged&&o.scrollbar&&cG(o.scrollbar.options.liveRedraw,!cA&&!this.chart.boosted)&&(t.DOMType=t.type,setTimeout(function(){o.onMouseUp(t)},0)))},t.prototype.onMouseUp=function(t){var e,i,o,n,r,s,a=this.chart,h=this.xAxis,l=this.scrollbar,d=t.DOMEvent||t,c=a.inverted,p=this.rendered&&!this.hasDragged?"animate":"attr";(this.hasDragged&&(!l||!l.hasDragged)||"scrollbar"===t.trigger)&&(o=this.getUnionExtremes(),this.zoomedMin===this.otherHandlePos?n=this.fixedExtreme:this.zoomedMax===this.otherHandlePos&&(r=this.fixedExtreme),this.zoomedMax===this.size&&(r=this.reversedExtremes?o.dataMin:o.dataMax),0===this.zoomedMin&&(n=this.reversedExtremes?o.dataMax:o.dataMin),cE((s=h.navigatorAxis.toFixedRange(this.zoomedMin,this.zoomedMax,n,r)).min)&&cz(this,"setRange",{min:Math.min(s.min,s.max),max:Math.max(s.min,s.max),redraw:!0,animation:!this.hasDragged&&null,eventArguments:{trigger:"navigator",triggerOp:"navigator-drag",DOMEvent:d}})),"mousemove"!==t.DOMType&&"touchmove"!==t.DOMType&&(this.grabbedLeft=this.grabbedRight=this.grabbedCenter=this.fixedWidth=this.fixedExtreme=this.otherHandlePos=this.hasDragged=this.dragOffset=null),this.navigatorEnabled&&cN(this.zoomedMin)&&cN(this.zoomedMax)&&(i=Math.round(this.zoomedMin),e=Math.round(this.zoomedMax),this.shades&&this.drawMasks(i,e,c,p),this.outline&&this.drawOutline(i,e,c,p),this.navigatorOptions.handles.enabled&&Object.keys(this.handles).length===this.handles.length&&(this.drawHandle(i,0,c,p),this.drawHandle(e,1,c,p)))},t.prototype.removeEvents=function(){this.eventsToUnbind&&(this.eventsToUnbind.forEach(function(t){t()}),this.eventsToUnbind=void 0),this.removeBaseSeriesEvents()},t.prototype.removeBaseSeriesEvents=function(){var t=this.baseSeries||[];this.navigatorEnabled&&t[0]&&(!1!==this.navigatorOptions.adaptToUpdatedData&&t.forEach(function(t){cX(t,"updatedData",this.updatedDataHandler)},this),t[0].xAxis&&cX(t[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes))},t.prototype.getXAxisOffsets=function(){return this.chart.inverted?[this.scrollButtonSize,0,-this.scrollButtonSize,0]:[0,-this.scrollButtonSize,0,this.scrollButtonSize]},t.prototype.init=function(t){var e,i,o=t.options,n=o.navigator||{},r=n.enabled,s=o.scrollbar||{},a=s.enabled,h=r&&n.height||0,l=a&&s.height||0,d=s.buttonsEnabled&&l||0;this.handles=[],this.shades=[],this.chart=t,this.setBaseSeries(),this.height=h,this.scrollbarHeight=l,this.scrollButtonSize=d,this.scrollbarEnabled=a,this.navigatorEnabled=r,this.navigatorOptions=n,this.scrollbarOptions=s,this.setOpposite();var c=this,p=c.baseSeries,u=t.xAxis.length,f=t.yAxis.length,g=p&&p[0]&&p[0].xAxis||t.xAxis[0]||{options:{}};if(t.isDirtyBox=!0,c.navigatorEnabled){var v=this.getXAxisOffsets();c.xAxis=new nP(t,cW({breaks:g.options.breaks,ordinal:g.options.ordinal,overscroll:g.options.overscroll},n.xAxis,{type:"datetime",yAxis:null===(e=n.yAxis)||void 0===e?void 0:e.id,index:u,isInternal:!0,offset:0,keepOrdinalPadding:!0,startOnTick:!1,endOnTick:!1,minPadding:g.options.ordinal?0:g.options.minPadding,maxPadding:g.options.ordinal?0:g.options.maxPadding,zoomEnabled:!1},t.inverted?{offsets:v,width:h}:{offsets:v,height:h}),"xAxis"),c.yAxis=new nP(t,cW(n.yAxis,{alignTicks:!1,offset:0,index:f,isInternal:!0,reversed:cG(n.yAxis&&n.yAxis.reversed,t.yAxis[0]&&t.yAxis[0].reversed,!1),zoomEnabled:!1},t.inverted?{width:h}:{height:h}),"yAxis"),p||n.series.data?c.updateNavigatorSeries(!1):0===t.series.length&&(c.unbindRedraw=cC(t,"beforeRedraw",function(){t.series.length>0&&!c.series&&(c.setBaseSeries(),c.unbindRedraw())})),c.reversedExtremes=t.inverted&&!c.xAxis.reversed||!t.inverted&&c.xAxis.reversed,c.renderElements(),c.addMouseEvents()}else c.xAxis={chart:t,navigatorAxis:{fake:!0},translate:function(e,i){var o=t.xAxis[0],n=o.getExtremes(),r=o.len-2*d,s=cF("min",o.options.min,n.dataMin),a=cF("max",o.options.max,n.dataMax)-s;return i?e*a/r+s:r*(e-s)/a},toPixels:function(t){return this.translate(t)},toValue:function(t){return this.translate(t,!0)}},c.xAxis.navigatorAxis.axis=c.xAxis,c.xAxis.navigatorAxis.toFixedRange=d0.prototype.toFixedRange.bind(c.xAxis.navigatorAxis);if(null===(i=t.options.scrollbar)||void 0===i?void 0:i.enabled){var m=cW(t.options.scrollbar,{vertical:t.inverted});cN(m.margin)||(m.margin=t.inverted?-3:3),t.scrollbar=c.scrollbar=new cw(t.renderer,m,t),cC(c.scrollbar,"changed",function(t){var e=c.size,i=e*this.to,o=e*this.from;c.hasDragged=c.scrollbar.hasDragged,c.render(0,0,o,i),this.shouldUpdateExtremes(t.DOMType)&&setTimeout(function(){c.onMouseUp(t)})})}c.addBaseSeriesEvents(),c.addChartEvents()},t.prototype.setOpposite=function(){var t=this.navigatorOptions,e=this.navigatorEnabled,i=this.chart;this.opposite=cG(t.opposite,!!(!e&&i.inverted))},t.prototype.getUnionExtremes=function(t){var e,i=this.chart.xAxis[0],o=this.chart.time,n=this.xAxis,r=n.options,s=i.options;return t&&null===i.dataMin||(e={dataMin:cG(o.parse(null==r?void 0:r.min),cF("min",o.parse(s.min),i.dataMin,n.dataMin,n.min)),dataMax:cG(o.parse(null==r?void 0:r.max),cF("max",o.parse(s.max),i.dataMax,n.dataMax,n.max))}),e},t.prototype.setBaseSeries=function(t,e){var i=this.chart,o=this.baseSeries=[];t=t||i.options&&i.options.navigator.baseSeries||(i.series.length?cI(i.series,function(t){return!t.options.isInternal}).index:0),(i.series||[]).forEach(function(e,i){!e.options.isInternal&&(e.options.showInNavigator||(i===t||e.options.id===t)&&!1!==e.options.showInNavigator)&&o.push(e)}),this.xAxis&&!this.xAxis.navigatorAxis.fake&&this.updateNavigatorSeries(!0,e)},t.prototype.updateNavigatorSeries=function(t,e){var i,o,n,r,s,a=this,h=a.chart,l=a.baseSeries,d={enableMouseTracking:!1,index:null,linkedTo:null,group:"nav",padXAxis:!1,xAxis:null===(i=this.navigatorOptions.xAxis)||void 0===i?void 0:i.id,yAxis:null===(o=this.navigatorOptions.yAxis)||void 0===o?void 0:o.id,showInLegend:!1,stacking:void 0,isInternal:!0,states:{inactive:{opacity:1}}},c=a.series=(a.series||[]).filter(function(t){var e=t.baseSeries;return!(0>l.indexOf(e))||(e&&(cX(e,"updatedData",a.updatedDataHandler),delete e.navigatorSeries),t.chart&&t.destroy(),!1)}),p=a.navigatorOptions.series;l&&l.length&&l.forEach(function(t){var i,o=t.navigatorSeries,u=cD({color:t.color,visible:t.visible},cR(p)?en.navigator.series:p);if(!o||!1!==a.navigatorOptions.adaptToUpdatedData){d.name="Navigator "+l.length,s=(n=t.options||{}).navigatorOptions||{},u.dataLabels=cH(u.dataLabels),(r=cW(n,d,u,s)).pointRange=cG(u.pointRange,s.pointRange,en.plotOptions[r.type||"line"].pointRange);var f=s.data||u.data;a.hasNavigatorData=a.hasNavigatorData||!!f,r.data=f||(null===(i=n.data)||void 0===i?void 0:i.slice(0)),o&&o.options?o.update(r,e):(t.navigatorSeries=h.initSeries(r),h.setSortedData(),t.navigatorSeries.baseSeries=t,c.push(t.navigatorSeries))}}),(p.data&&!(l&&l.length)||cR(p))&&(a.hasNavigatorData=!1,(p=cH(p)).forEach(function(t,e){d.name="Navigator "+(c.length+1),(r=cW(en.navigator.series,{color:h.series[e]&&!h.series[e].options.isInternal&&h.series[e].color||h.options.colors[e]||h.options.colors[0]},d,t)).data=t.data,r.data&&(a.hasNavigatorData=!0,c.push(h.initSeries(r)))})),t&&this.addBaseSeriesEvents()},t.prototype.addBaseSeriesEvents=function(){var t=this,e=this,i=e.baseSeries||[];i[0]&&i[0].xAxis&&i[0].eventsToUnbind.push(cC(i[0].xAxis,"foundExtremes",this.modifyBaseAxisExtremes)),i.forEach(function(o){o.eventsToUnbind.push(cC(o,"show",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!0,!1)})),o.eventsToUnbind.push(cC(o,"hide",function(){this.navigatorSeries&&this.navigatorSeries.setVisible(!1,!1)})),!1!==t.navigatorOptions.adaptToUpdatedData&&o.xAxis&&o.eventsToUnbind.push(cC(o,"updatedData",t.updatedDataHandler)),o.eventsToUnbind.push(cC(o,"remove",function(){i&&cB(i,o),this.navigatorSeries&&e.series&&(cB(e.series,this.navigatorSeries),cE(this.navigatorSeries.options)&&this.navigatorSeries.remove(!1),delete this.navigatorSeries)}))})},t.prototype.getBaseSeriesMin=function(t){return this.baseSeries.reduce(function(t,e){var i;return Math.min(t,null!==(i=e.getColumn("x")[0])&&void 0!==i?i:t)},t)},t.prototype.modifyNavigatorAxisExtremes=function(){var t=this.xAxis;if(void 0!==t.getExtremes){var e=this.getUnionExtremes(!0);e&&(e.dataMin!==t.min||e.dataMax!==t.max)&&(t.min=e.dataMin,t.max=e.dataMax)}},t.prototype.modifyBaseAxisExtremes=function(){var t,e,i,o=this.chart.navigator,n=this.getExtremes(),r=n.min,s=n.max,a=n.dataMin,h=n.dataMax,l=s-r,d=o.stickToMin,c=o.stickToMax,p=cG(null===(t=this.ordinal)||void 0===t?void 0:t.convertOverscroll(this.options.overscroll),0),u=o.series&&o.series[0],f=!!this.setExtremes;!(this.eventArgs&&"rangeSelectorButton"===this.eventArgs.trigger)&&(d&&(e=(i=a)+l),c&&(e=h+p,d||(i=Math.max(a,e-l,o.getBaseSeriesMin(u&&u.xData?u.xData[0]:-Number.MAX_VALUE)))),f&&(d||c)&&cN(i)&&(this.min=this.userMin=i,this.max=this.userMax=e)),o.stickToMin=o.stickToMax=null},t.prototype.updatedDataHandler=function(){var t=this.chart.navigator,e=this.navigatorSeries,i=t.reversedExtremes?0===Math.round(t.zoomedMin):Math.round(t.zoomedMax)>=Math.round(t.size);t.stickToMax=cG(this.chart.options.navigator&&this.chart.options.navigator.stickToMax,i),t.stickToMin=t.shouldStickToMin(this,t),e&&!t.hasNavigatorData&&(e.options.pointStart=this.getColumn("x")[0],e.setData(this.options.data,!1,null,!1))},t.prototype.shouldStickToMin=function(t,e){var i=e.getBaseSeriesMin(t.getColumn("x")[0]),o=t.xAxis,n=o.max,r=o.min,s=o.options.range,a=!0;return!!(cN(n)&&cN(r))&&(s&&n-i>0?n-i<s:r<=i)},t.prototype.addChartEvents=function(){this.eventsToUnbind||(this.eventsToUnbind=[]),this.eventsToUnbind.push(cC(this.chart,"redraw",function(){var t=this.navigator,e=t&&(t.baseSeries&&t.baseSeries[0]&&t.baseSeries[0].xAxis||this.xAxis[0]);e&&t.render(e.min,e.max)}),cC(this.chart,"getMargins",function(){var t,e=this.navigator,i=e.opposite?"plotTop":"marginBottom";this.inverted&&(i=e.opposite?"marginRight":"plotLeft"),this[i]=(this[i]||0)+(e.navigatorEnabled||!this.inverted?e.height+((null===(t=this.scrollbar)||void 0===t?void 0:t.options.margin)||0)+e.scrollbarHeight:0)+(e.navigatorOptions.margin||0)}),cC(t,"setRange",function(t){this.chart.xAxis[0].setExtremes(t.min,t.max,t.redraw,t.animation,t.eventArguments)}))},t.prototype.destroy=function(){var t=this;this.removeEvents(),this.xAxis&&(cB(this.chart.xAxis,this.xAxis),cB(this.chart.axes,this.xAxis)),this.yAxis&&(cB(this.chart.yAxis,this.yAxis),cB(this.chart.axes,this.yAxis)),(this.series||[]).forEach(function(t){t.destroy&&t.destroy()}),["series","xAxis","yAxis","shades","outline","scrollbarTrack","scrollbarRifles","scrollbarGroup","scrollbar","navigatorGroup","rendered"].forEach(function(e){t[e]&&t[e].destroy&&t[e].destroy(),t[e]=null}),[this.handles].forEach(function(t){cL(t)}),this.baseSeries.forEach(function(t){t.navigatorSeries=void 0}),this.navigatorEnabled=!1},t}(),c_=function(){return(c_=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},cj=tF.addEvent,cU=tF.correctFloat,cV=tF.css,cZ=tF.defined,cq=tF.error,cK=tF.isNumber,c$=tF.pick,cJ=tF.timeUnits,cQ=tF.isString;!function(t){function e(t,e,i,o,n,r,s){void 0===n&&(n=[]),void 0===r&&(r=0);var a,h,l,d,c,p={},u=this.options.tickPixelInterval,f=this.chart.time,g=[],v=0,m=[],y=-Number.MAX_VALUE;if(!this.options.ordinal&&!this.options.breaks||!n||n.length<3||void 0===e)return f.getTimeTicks.apply(f,arguments);var x=n.length;for(a=0;a<x;a++){if(c=a&&n[a-1]>i,n[a]<e&&(v=a),a===x-1||n[a+1]-n[a]>5*r||c){if(n[a]>y){for(h=f.getTimeTicks(t,n[v],n[a],o);h.length&&h[0]<=y;)h.shift();h.length&&(y=h[h.length-1]),g.push(m.length),m=m.concat(h)}v=a+1}if(c)break}if(h){if(d=h.info,s&&d.unitRange<=cJ.hour){for(v=1,a=m.length-1;v<a;v++)f.dateFormat("%d",m[v])!==f.dateFormat("%d",m[v-1])&&(p[m[v]]="day",l=!0);l&&(p[m[0]]="day"),d.higherRanks=p}d.segmentStarts=g,m.info=d}else cq(12,!1,this.chart);if(s&&cZ(u)){for(var b=m.length,k=[],M=[],w=void 0,S=void 0,A=void 0,T=void 0,C=void 0,O=b;O--;)S=this.translate(m[O]),A&&(M[O]=A-S),k[O]=A=S;for(M.sort(function(t,e){return t-e}),(T=M[Math.floor(M.length/2)])<.6*u&&(T=null),O=m[b-1]>i?b-1:b,A=void 0;O--;)C=Math.abs(A-(S=k[O])),A&&C<.8*u&&(null===T||C<.8*T)?(p[m[O]]&&!p[m[O+1]]?(w=O+1,A=S):w=O,m.splice(w,1)):A=S}return m}function i(t){var e=this.ordinal.positions;if(!e)return t;var i,o=e.length-1;return(t<0?t=e[0]:t>o?t=e[o]:(o=Math.floor(t),i=t-o),void 0!==i&&void 0!==e[o])?e[o]+(i?i*(e[o+1]-e[o]):0):t}function o(t){var e=this.ordinal,i=this.old?this.old.min:this.min,o=this.old?this.old.transA:this.transA,n=e.getExtendedPositions();if(null==n?void 0:n.length){var r=cU((t-i)*o+this.minPixelPadding),s=cU(e.getIndexOfPoint(r,n)),a=cU(s%1);if(s>=0&&s<=n.length-1){var h=n[Math.floor(s)],l=n[Math.ceil(s)];return n[Math.floor(s)]+a*(l-h)}}return t}function n(e,i){var o=t.Additions.findIndexOf(e,i,!0);if(e[o]===i)return o;var n=(i-e[o])/(e[o+1]-e[o]);return o+n}function r(){this.ordinal||(this.ordinal=new t.Additions(this))}function s(){var t=this.eventArgs,e=this.options;if(this.isXAxis&&cZ(e.overscroll)&&0!==e.overscroll&&cK(this.max)&&cK(this.min)&&(this.options.ordinal&&!this.ordinal.originalOrdinalRange&&this.ordinal.getExtendedPositions(!1),this.max===this.dataMax&&((null==t?void 0:t.trigger)!=="pan"||this.isInternal)&&(null==t?void 0:t.trigger)!=="navigator")){var i=this.ordinal.convertOverscroll(e.overscroll);this.max+=i,!this.isInternal&&cZ(this.userMin)&&(null==t?void 0:t.trigger)!=="mousewheel"&&(this.min+=i)}}function a(){this.horiz&&!this.isDirty&&(this.isDirty=this.isOrdinal&&this.chart.navigator&&!this.chart.navigator.adaptToUpdatedData)}function h(){this.ordinal&&(this.ordinal.beforeSetTickPositions(),this.tickInterval=this.ordinal.postProcessTickInterval(this.tickInterval))}function l(t){var e,i=this.xAxis[0],o=i.ordinal.convertOverscroll(i.options.overscroll),n=t.originalEvent.chartX,r=this.options.chart.panning,s=!1;if(r&&"y"!==r.type&&i.options.ordinal&&i.series.length&&(!t.touches||t.touches.length<=1)){var a=this.mouseDownX,h=i.getExtremes(),l=h.dataMin,d=h.dataMax,c=h.min,p=h.max,u=this.hoverPoints,f=i.closestPointRange||(null===(e=i.ordinal)||void 0===e?void 0:e.overscrollPointsRange),g=Math.round((a-n)/(i.translationSlope*(i.ordinal.slope||f))),v=i.ordinal.getExtendedPositions(),m={ordinal:{positions:v,extendedOrdinalPositions:v}},y=i.index2val,x=i.val2lin,b=void 0,k=void 0;if(c<=l&&g<0||p+o>=d&&g>0)return;m.ordinal.positions?Math.abs(g)>1&&(u&&u.forEach(function(t){t.setState()}),d>(k=m.ordinal.positions)[k.length-1]&&k.push(d),this.setFixedRange(p-c),(b=i.navigatorAxis.toFixedRange(void 0,void 0,y.apply(m,[x.apply(m,[c,!0])+g]),y.apply(m,[x.apply(m,[p,!0])+g]))).min>=Math.min(k[0],c)&&b.max<=Math.max(k[k.length-1],p)+o&&i.setExtremes(b.min,b.max,!0,!1,{trigger:"pan"}),this.mouseDownX=n,cV(this.container,{cursor:"move"})):s=!0}else s=!0;s||r&&/y/.test(r.type)?o&&(i.max=i.dataMax+o):t.preventDefault()}function d(){var t=this.xAxis;(null==t?void 0:t.options.ordinal)&&(delete t.ordinal.index,delete t.ordinal.originalOrdinalRange)}function c(t,e){var i,o,r,s=this.ordinal,a=s.positions,h=s.slope;if(!a)return t;var l=a.length;if(a[0]<=t&&a[l-1]>=t)i=n(a,t);else{if(!(null==(r=null===(o=s.getExtendedPositions)||void 0===o?void 0:o.call(s))?void 0:r.length))return t;var d=r.length;h||(h=(r[d-1]-r[0])/d);var c=n(r,a[0]);if(t>=r[0]&&t<=r[d-1])i=n(r,t)-c;else{if(!e)return t;if(t<r[0]){var p=r[0]-t,u=p/h;i=-c-u}else{var p=t-r[d-1],u=p/h;i=u+d-c}}}return e?i:h*(i||0)+s.offset}t.compose=function(t,n,p){var u=t.prototype;return u.ordinal2lin||(u.getTimeTicks=e,u.index2val=i,u.lin2val=o,u.val2lin=c,u.ordinal2lin=u.val2lin,cj(t,"afterInit",r),cj(t,"foundExtremes",s),cj(t,"afterSetScale",a),cj(t,"initialAxisTranslation",h),cj(p,"pan",l),cj(p,"touchpan",l),cj(n,"updatedData",d)),t},t.Additions=function(){function t(t){this.index={},this.axis=t}return t.prototype.beforeSetTickPositions=function(){var t,e,i,o,n,r,s,a,h=this.axis,l=h.ordinal,d=h.getExtremes(),c=d.min,p=d.max,u=null===(t=h.brokenAxis)||void 0===t?void 0:t.hasBreaks,f=h.options.ordinal,g=[],v=Number.MAX_VALUE,m=!1,y=!1,x=!1;if(f||u){var b=0;if(h.series.forEach(function(t,o){var n=t.getColumn("x",!0);if(i=[],o>0&&"highcharts-navigator-series"!==t.options.id&&n.length>1&&(y=b!==n[1]-n[0]),b=n[1]-n[0],t.boosted&&(x=t.boosted),t.reserveSpace()&&(!1!==t.takeOrdinalPosition||u)&&(e=(g=g.concat(n)).length,g.sort(function(t,e){return t-e}),v=Math.min(v,c$(t.closestPointRange,v)),e)){for(o=0;o<e-1;)g[o]!==g[o+1]&&i.push(g[o+1]),o++;i[0]!==g[0]&&i.unshift(g[0]),g=i}}),h.ordinal.originalOrdinalRange||(h.ordinal.originalOrdinalRange=(g.length-1)*v),y&&x&&(g.pop(),g.shift()),(e=g.length)>2){for(o=g[1]-g[0],a=e-1;a--&&!m;)g[a+1]-g[a]!==o&&(m=!0);!h.options.keepOrdinalPadding&&(g[0]-c>o||p-g[g.length-1]>o)&&(m=!0)}else h.options.overscroll&&(2===e?v=g[1]-g[0]:1===e?(v=h.ordinal.convertOverscroll(h.options.overscroll),g=[g[0],g[0]+v]):v=l.overscrollPointsRange);m||h.forceOrdinal?(h.options.overscroll&&(l.overscrollPointsRange=v,g=g.concat(l.getOverscrollPositions())),l.positions=g,n=h.ordinal2lin(Math.max(c,g[0]),!0),r=Math.max(h.ordinal2lin(Math.min(p,g[g.length-1]),!0),1),l.slope=s=(p-c)/(r-n),l.offset=c-n*s):(l.overscrollPointsRange=c$(h.closestPointRange,l.overscrollPointsRange),l.positions=h.ordinal.slope=l.offset=void 0)}h.isOrdinal=f&&m,l.groupIntervalFactor=null},t.findIndexOf=function(t,e,i){for(var o,n=0,r=t.length-1;n<r;)t[o=Math.ceil((n+r)/2)]<=e?n=o:r=o-1;return t[n]===e?n:i?n:-1},t.prototype.getExtendedPositions=function(t){void 0===t&&(t=!0);var e,i=this,o=i.axis,n=o.constructor.prototype,r=o.chart,s=o.series.reduce(function(t,e){var i=e.currentDataGrouping;return t+(i?i.count+i.unitName:"raw")},""),a=t?o.ordinal.convertOverscroll(o.options.overscroll):0,h=o.getExtremes(),l=void 0,d=i.index;return d||(d=i.index={}),!d[s]&&((e={series:[],chart:r,forceOrdinal:!1,getExtremes:function(){return{min:h.dataMin,max:h.dataMax+a}},applyGrouping:n.applyGrouping,getGroupPixelWidth:n.getGroupPixelWidth,getTimeTicks:n.getTimeTicks,options:{ordinal:!0},ordinal:{getGroupIntervalFactor:this.getGroupIntervalFactor},ordinal2lin:n.ordinal2lin,getIndexOfPoint:n.getIndexOfPoint,val2lin:n.val2lin}).ordinal.axis=e,o.series.forEach(function(o){l={xAxis:e,chart:r,groupPixelWidth:o.groupPixelWidth,destroyGroupedData:tv.noop,getColumn:o.getColumn,applyGrouping:o.applyGrouping,getProcessedData:o.getProcessedData,reserveSpace:o.reserveSpace,visible:o.visible};var n,s,a,h=o.getColumn("x").concat(t?i.getOverscrollPositions():[]);l.dataTable=new r6({columns:{x:h}}),l.options=c_(c_({},o.options),{dataGrouping:o.currentDataGrouping?{firstAnchor:null===(n=o.options.dataGrouping)||void 0===n?void 0:n.firstAnchor,anchor:null===(s=o.options.dataGrouping)||void 0===s?void 0:s.anchor,lastAnchor:null===(a=o.options.dataGrouping)||void 0===a?void 0:a.firstAnchor,enabled:!0,forced:!0,approximation:"open",units:[[o.currentDataGrouping.unitName,[o.currentDataGrouping.count]]]}:{enabled:!1}}),e.series.push(l),o.processData.apply(l)}),e.applyGrouping({hasExtremesChanged:!0}),(null==l?void 0:l.closestPointRange)!==(null==l?void 0:l.basePointRange)&&l.currentDataGrouping&&(e.forceOrdinal=!0),o.ordinal.beforeSetTickPositions.apply({axis:e}),!o.ordinal.originalOrdinalRange&&e.ordinal.originalOrdinalRange&&(o.ordinal.originalOrdinalRange=e.ordinal.originalOrdinalRange),e.ordinal.positions&&(d[s]=e.ordinal.positions)),d[s]},t.prototype.getGroupIntervalFactor=function(t,e,i){var o,n,r=i.getColumn("x",!0),s=r.length,a=[],h=this.groupIntervalFactor;if(!h){for(n=0;n<s-1;n++)a[n]=r[n+1]-r[n];a.sort(function(t,e){return t-e}),o=a[Math.floor(s/2)],t=Math.max(t,r[0]),e=Math.min(e,r[s-1]),this.groupIntervalFactor=h=s*o/(e-t)}return h},t.prototype.getIndexOfPoint=function(t,e){var i=this.axis,o=i.min,r=i.minPixelPadding;return n(e,o)+cU((t-r)/(i.translationSlope*(this.slope||i.closestPointRange||this.overscrollPointsRange)))},t.prototype.getOverscrollPositions=function(){var t=this.axis,e=this.convertOverscroll(t.options.overscroll),i=this.overscrollPointsRange,o=[],n=t.dataMax;if(cZ(i))for(;n<t.dataMax+e;)o.push(n+=i);return o},t.prototype.postProcessTickInterval=function(t){var e,i=this.axis,o=this.slope,n=i.closestPointRange;return o&&n?i.options.breaks?n||t:t/(o/n):t},t.prototype.convertOverscroll=function(t){void 0===t&&(t=0);var e=this,i=e.axis,o=function(t){return c$(e.originalOrdinalRange,cZ(i.dataMax)&&cZ(i.dataMin)?i.dataMax-i.dataMin:0)*t};if(cQ(t)){var n=parseInt(t,10),r=void 0;if(cZ(i.min)&&cZ(i.max)&&cZ(i.dataMin)&&cZ(i.dataMax)&&!(r=i.max-i.min==i.dataMax-i.dataMin)&&(this.originalOrdinalRange=i.max-i.min),/%$/.test(t))return o(n/100);if(/px/.test(t)){var s=Math.min(n,.9*i.len)/i.len;return o(s/(r?1-s:1))}return 0}return t},t}()}(ta||(ta={}));var c0=ta,c1={lang:{rangeSelectorZoom:"Zoom",rangeSelectorFrom:"",rangeSelectorTo:"→",rangeSelector:{allText:"All",allTitle:"View all",monthText:"{count}m",monthTitle:"View {count} {#eq count 1}month{else}months{/eq}",yearText:"{count}y",yearTitle:"View {count} {#eq count 1}year{else}years{/eq}",ytdText:"YTD",ytdTitle:"View year to date"}},rangeSelector:{allButtonsEnabled:!1,buttons:[{type:"month",count:1},{type:"month",count:3},{type:"month",count:6},{type:"ytd"},{type:"year",count:1},{type:"all"}],buttonSpacing:5,dropdown:"responsive",enabled:void 0,verticalAlign:"top",buttonTheme:{width:28,height:18,padding:2,zIndex:7},floating:!1,x:0,y:0,height:void 0,inputBoxBorderColor:"none",inputBoxHeight:17,inputBoxWidth:void 0,inputDateFormat:"%[ebY]",inputDateParser:void 0,inputEditDateFormat:"%Y-%m-%d",inputEnabled:!0,inputPosition:{align:"right",x:0,y:0},inputSpacing:5,selected:void 0,buttonPosition:{align:"left",x:0,y:0},inputStyle:{color:"#334eff",cursor:"pointer",fontSize:"0.8em"},labelStyle:{color:"#666666",fontSize:"0.8em"}}},c2=tv.composed,c3=tF.addEvent,c5=tF.defined,c6=tF.extend,c9=tF.isNumber,c4=tF.merge,c8=tF.pick,c7=tF.pushUnique,pt=[];function pe(){var t,e,i=this.range,o=i.type,n=this.max,r=this.chart.time,s=function(t,e){var i=r.toParts(t),n=i.slice();"year"===o?n[0]+=e:n[1]+=e;var s=r.makeTime.apply(r,n),a=r.toParts(s);return"month"===o&&i[1]===a[1]&&1===Math.abs(e)&&(n[0]=i[0],n[1]=i[1],n[2]=0),(s=r.makeTime.apply(r,n))-t};c9(i)?(t=n-i,e=i):i&&(t=n+s(n,-(i.count||1)),this.chart&&this.chart.setFixedRange(n-t));var a=c8(this.dataMin,Number.MIN_VALUE);return c9(t)||(t=a),t<=a&&(t=a,void 0===e&&(e=s(t,i.count)),this.newMax=Math.min(t+e,c8(this.dataMax,Number.MAX_VALUE))),c9(n)?!c9(i)&&i&&i._offsetMin&&(t+=i._offsetMin):t=void 0,t}function pi(){var t;null===(t=this.rangeSelector)||void 0===t||t.redrawElements()}function po(){this.options.rangeSelector&&this.options.rangeSelector.enabled&&(this.rangeSelector=new th(this))}function pn(){var t=this.rangeSelector;if(t){c9(t.deferredYTDClick)&&(t.clickButton(t.deferredYTDClick),delete t.deferredYTDClick);var e=t.options.verticalAlign;t.options.floating||("bottom"===e?this.extraBottomMargin=!0:"top"!==e||(this.extraTopMargin=!0))}}function pr(){var t,e=this.rangeSelector;if(e){var i=this.xAxis[0].getExtremes(),o=this.legend,n=e&&e.options.verticalAlign;c9(i.min)&&e.render(i.min,i.max),o.display&&"top"===n&&n===o.options.verticalAlign&&(t=c4(this.spacingBox),"vertical"===o.options.layout?t.y=this.plotTop:t.y+=e.getHeight(),o.group.placed=!1,o.align(t))}}function ps(){for(var t=0,e=pt.length;t<e;++t){var i=pt[t];if(i[0]===this){i[1].forEach(function(t){return t()}),pt.splice(t,1);return}}}function pa(){var t,e=this.rangeSelector;if(null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.enabled){var i=e.getHeight(),o=e.options.verticalAlign;e.options.floating||("bottom"===o?this.marginBottom+=i:"middle"===o||(this.plotTop+=i))}}function ph(t){var e=t.options.rangeSelector,i=this.extraBottomMargin,o=this.extraTopMargin,n=this.rangeSelector;if(e&&e.enabled&&!c5(n)&&this.options.rangeSelector&&(this.options.rangeSelector.enabled=!0,this.rangeSelector=n=new th(this)),this.extraBottomMargin=!1,this.extraTopMargin=!1,n){var r=e&&e.verticalAlign||n.options&&n.options.verticalAlign;n.options.floating||("bottom"===r?this.extraBottomMargin=!0:"middle"===r||(this.extraTopMargin=!0)),(this.extraBottomMargin!==i||this.extraTopMargin!==o)&&(this.isDirtyBox=!0)}}var pl=function(t,e,i){if(th=i,c7(c2,"RangeSelector")){var o=e.prototype;t.prototype.minFromRange=pe,c3(e,"afterGetContainer",po),c3(e,"beforeRender",pn),c3(e,"destroy",ps),c3(e,"getMargins",pa),c3(e,"redraw",pr),c3(e,"update",ph),c3(e,"beforeRedraw",pi),o.callbacks.push(pr),c6(en,{rangeSelector:c1.rangeSelector}),c6(en.lang,c1.lang)}},pd=e8.format,pc=tF.addEvent,pp=tF.createElement,pu=tF.css,pf=tF.defined,pg=tF.destroyObjectProperties,pv=tF.diffObjects,pm=tF.discardElement,py=tF.extend,px=tF.fireEvent,pb=tF.isNumber,pk=tF.isString,pM=tF.merge,pw=tF.objectEach,pS=tF.pick,pA=tF.splat;function pT(t){var e=function(e){return new RegExp("%[[a-zA-Z]*".concat(e)).test(t)};if(pk(t)?-1!==t.indexOf("%L"):t.fractionalSecondDigits)return"text";var i=pk(t)?["a","A","d","e","w","b","B","m","o","y","Y"].some(e):t.dateStyle||t.day||t.month||t.year,o=pk(t)?["H","k","I","l","M","S"].some(e):t.timeStyle||t.hour||t.minute||t.second;return i&&o?"datetime-local":i?"date":o?"time":"text"}var pC=function(){function t(t){var e=this;this.isDirty=!1,this.buttonOptions=[],this.initialButtonGroupWidth=0,this.maxButtonWidth=function(){var t=0;return e.buttons.forEach(function(e){var i=e.getBBox();i.width>t&&(t=i.width)}),t},this.init(t)}return t.compose=function(e,i){pl(e,i,t)},t.prototype.clickButton=function(t,e){var i,o,n,r,s,a=this.chart,h=this.buttonOptions[t],l=a.xAxis[0],d=a.scroller&&a.scroller.getUnionExtremes()||l||{},c=h.type,p=h.dataGrouping,u=d.dataMin,f=d.dataMax,g=pb(null==l?void 0:l.max)?Math.round(Math.min(l.max,null!=f?f:l.max)):void 0,v=h._range,m=!0;if(null!==u&&null!==f){if(this.setSelected(t),p&&(this.forcedDataGrouping=!0,nP.prototype.setDataGrouping.call(l||{chart:this.chart},p,!1),this.frozenStates=h.preserveDataGrouping),"month"===c||"year"===c)l?(r={range:h,max:g,chart:a,dataMin:u,dataMax:f},i=l.minFromRange.call(r),pb(r.newMax)&&(g=r.newMax),m=!1):v=h;else if(v)pb(g)&&(g=Math.min((i=Math.max(g-v,u))+v,f),m=!1);else if("ytd"===c){if(l)!l.hasData()||pb(f)&&pb(u)||(u=Number.MAX_VALUE,f=-Number.MAX_VALUE,a.series.forEach(function(t){var e=t.getColumn("x");e.length&&(u=Math.min(e[0],u),f=Math.max(e[e.length-1],f))}),e=!1),pb(f)&&pb(u)&&(i=n=(s=this.getYTDExtremes(f,u)).min,g=s.max);else{this.deferredYTDClick=t;return}}else"all"===c&&l&&(a.navigator&&a.navigator.baseSeries[0]&&(a.navigator.baseSeries[0].xAxis.options.range=void 0),i=u,g=f);if(m&&h._offsetMin&&pf(i)&&(i+=h._offsetMin),h._offsetMax&&pf(g)&&(g+=h._offsetMax),this.dropdown&&(this.dropdown.selectedIndex=t+1),l)pb(i)&&pb(g)&&(l.setExtremes(i,g,pS(e,!0),void 0,{trigger:"rangeSelectorButton",rangeSelectorButton:h}),a.setFixedRange(h._range));else{o=pA(a.options.xAxis||{})[0];var y=pc(a,"afterCreateAxes",function(){var t=a.xAxis[0];t.range=t.options.range=v,t.min=t.options.min=n});pc(a,"load",function(){var t=a.xAxis[0];a.setFixedRange(h._range),t.options.range=o.range,t.options.min=o.min,y()})}px(this,"afterBtnClick")}},t.prototype.setSelected=function(t){this.selected=this.options.selected=t},t.prototype.init=function(t){var e=this,i=t.options.rangeSelector,o=t.options.lang,n=i.buttons,r=i.selected,s=function(){var t=e.minInput,i=e.maxInput;t&&t.blur&&px(t,"blur"),i&&i.blur&&px(i,"blur")};e.chart=t,e.options=i,e.buttons=[],e.buttonOptions=n.map(function(t){var e,i;return t.type&&o.rangeSelector&&(null!==(e=t.text)&&void 0!==e||(t.text=o.rangeSelector[""+t.type+"Text"]),null!==(i=t.title)&&void 0!==i||(t.title=o.rangeSelector[""+t.type+"Title"])),t.text=pd(t.text,{count:t.count||1}),t.title=pd(t.title,{count:t.count||1}),t}),this.eventsToUnbind=[],this.eventsToUnbind.push(pc(t.container,"mousedown",s)),this.eventsToUnbind.push(pc(t,"resize",s)),n.forEach(e.computeButtonRange),void 0!==r&&n[r]&&this.clickButton(r,!1),this.eventsToUnbind.push(pc(t,"load",function(){t.xAxis&&t.xAxis[0]&&pc(t.xAxis[0],"setExtremes",function(i){pb(this.max)&&pb(this.min)&&this.max-this.min!==t.fixedRange&&"rangeSelectorButton"!==i.trigger&&"updatedData"!==i.trigger&&e.forcedDataGrouping&&!e.frozenStates&&this.setDataGrouping(!1,!1)})})),this.createElements()},t.prototype.updateButtonStates=function(){var t=this,e=this.chart,i=this.dropdown,o=this.dropdownLabel,n=e.xAxis[0],r=Math.round(n.max-n.min),s=!n.hasVisibleSeries,a=24*36e5,h=e.scroller&&e.scroller.getUnionExtremes()||n,l=h.dataMin,d=h.dataMax,c=t.getYTDExtremes(d,l),p=c.min,u=c.max,f=t.selected,g=t.options.allButtonsEnabled,v=Array(t.buttonOptions.length).fill(0),m=pb(f),y=t.buttons,x=!1,b=null;t.buttonOptions.forEach(function(e,i){var o,h=e._range,c=e.type,y=e.count||1,k=e._offsetMax-e._offsetMin,M=i===f,w=h>d-l,S=h<n.minRange,A=!1,T=h===r;if(M&&w&&(x=!0),n.isOrdinal&&(null===(o=n.ordinal)||void 0===o?void 0:o.positions)&&h&&r<h){var C=n.ordinal.positions,O=c0.Additions.findIndexOf(C,n.min,!0),P=Math.min(c0.Additions.findIndexOf(C,n.max,!0)+1,C.length-1);C[P]-C[O]>h&&(T=!0)}else("month"===c||"year"===c)&&r+36e5>=({month:28,year:365})[c]*a*y-k&&r-36e5<=({month:31,year:366})[c]*a*y+k?T=!0:"ytd"===c?(T=u-p+k===r,A=!M):"all"===c&&(T=n.max-n.min>=d-l);var E=!g&&!(x&&"all"===c)&&(w||S||s),L=x&&"all"===c||!A&&T||M&&t.frozenStates;E?v[i]=3:L&&(!m||i===f)&&(b=i)}),null!==b?(v[b]=2,t.setSelected(b),this.dropdown&&(this.dropdown.selectedIndex=b+1)):(t.setSelected(),this.dropdown&&(this.dropdown.selectedIndex=-1),o&&(o.setState(0),o.attr({text:(en.lang.rangeSelectorZoom||"")+" ▾"})));for(var k=0;k<v.length;k++){var M=v[k],w=y[k];if(w.state!==M&&(w.setState(M),i)){i.options[k+1].disabled=3===M,2===M&&(o&&(o.setState(2),o.attr({text:t.buttonOptions[k].text+" ▾"})),i.selectedIndex=k+1);var S=o.getBBox();pu(i,{width:""+S.width+"px",height:""+S.height+"px"})}}},t.prototype.computeButtonRange=function(t){var e=t.type,i=t.count||1,o={millisecond:1,second:1e3,minute:6e4,hour:36e5,day:864e5,week:6048e5};o[e]?t._range=o[e]*i:("month"===e||"year"===e)&&(t._range=24*({month:30,year:365})[e]*36e5*i),t._offsetMin=pS(t.offsetMin,0),t._offsetMax=pS(t.offsetMax,0),t._range+=t._offsetMax-t._offsetMin},t.prototype.getInputValue=function(t){var e="min"===t?this.minInput:this.maxInput,i=this.chart.options.rangeSelector,o=this.chart.time;return e?("text"===e.type&&i.inputDateParser||this.defaultInputDateParser)(e.value,"UTC"===o.timezone,o):0},t.prototype.setInputValue=function(t,e){var i=this.options,o=this.chart.time,n="min"===t?this.minInput:this.maxInput,r="min"===t?this.minDateBox:this.maxDateBox;if(n){n.setAttribute("type",pT(i.inputDateFormat||"%e %b %Y"));var s=n.getAttribute("data-hc-time"),a=pf(s)?Number(s):void 0;if(pf(e)){var h=a;pf(h)&&n.setAttribute("data-hc-time-previous",h),n.setAttribute("data-hc-time",e),a=e}n.value=o.dateFormat(this.inputTypeFormats[n.type]||i.inputEditDateFormat,a),r&&r.attr({text:o.dateFormat(i.inputDateFormat,a)})}},t.prototype.setInputExtremes=function(t,e,i){var o="min"===t?this.minInput:this.maxInput;if(o){var n=this.inputTypeFormats[o.type],r=this.chart.time;if(n){var s=r.dateFormat(n,e);o.min!==s&&(o.min=s);var a=r.dateFormat(n,i);o.max!==a&&(o.max=a)}}},t.prototype.showInput=function(t){var e="min"===t?this.minDateBox:this.maxDateBox,i="min"===t?this.minInput:this.maxInput;if(i&&e&&this.inputGroup){var o="text"===i.type,n=this.inputGroup,r=n.translateX,s=void 0===r?0:r,a=n.translateY,h=void 0===a?0:a,l=e.x,d=void 0===l?0:l,c=e.width,p=void 0===c?0:c,u=e.height,f=void 0===u?0:u,g=this.options.inputBoxWidth;pu(i,{width:o?p+(g?-2:20)+"px":"auto",height:f-2+"px",border:"2px solid silver"}),o&&g?pu(i,{left:s+d+"px",top:h+"px"}):pu(i,{left:Math.min(Math.round(d+s-(i.offsetWidth-p)/2),this.chart.chartWidth-i.offsetWidth)+"px",top:h-(i.offsetHeight-f)/2+"px"})}},t.prototype.hideInput=function(t){var e="min"===t?this.minInput:this.maxInput;e&&pu(e,{top:"-9999em",border:0,width:"1px",height:"1px"})},t.prototype.defaultInputDateParser=function(t,e,i){return(null==i?void 0:i.parse(t))||0},t.prototype.drawInput=function(t){var e=this.chart,i=this.div,o=this.inputGroup,n=this,r=e.renderer.style||{},s=e.renderer,a=e.options.rangeSelector,h=en.lang,l="min"===t;function d(t){var i,o=n.maxInput,r=n.minInput,s=e.xAxis[0],a=(null===(i=e.scroller)||void 0===i?void 0:i.getUnionExtremes())||s,h=a.dataMin,d=a.dataMax,c=e.xAxis[0].getExtremes()[t],p=n.getInputValue(t);pb(p)&&p!==c&&(l&&o&&pb(h)?p>Number(o.getAttribute("data-hc-time"))?p=void 0:p<h&&(p=h):r&&pb(d)&&(p<Number(r.getAttribute("data-hc-time"))?p=void 0:p>d&&(p=d)),void 0!==p&&s.setExtremes(l?p:s.min,l?s.max:p,void 0,void 0,{trigger:"rangeSelectorInput"}))}var c=h[l?"rangeSelectorFrom":"rangeSelectorTo"]||"",p=s.label(c,0).addClass("highcharts-range-label").attr({padding:2*!!c,height:c?a.inputBoxHeight:0}).add(o),u=s.label("",0).addClass("highcharts-range-input").attr({padding:2,width:a.inputBoxWidth,height:a.inputBoxHeight,"text-align":"center"}).on("click",function(){n.showInput(t),n[t+"Input"].focus()});e.styledMode||u.attr({stroke:a.inputBoxBorderColor,"stroke-width":1}),u.add(o);var f=pp("input",{name:t,className:"highcharts-range-selector"},void 0,i);f.setAttribute("type",pT(a.inputDateFormat||"%e %b %Y")),e.styledMode||(p.css(pM(r,a.labelStyle)),u.css(pM({color:"#333333"},r,a.inputStyle)),pu(f,py({position:"absolute",border:0,boxShadow:"0 0 15px rgba(0,0,0,0.3)",width:"1px",height:"1px",padding:0,textAlign:"center",fontSize:r.fontSize,fontFamily:r.fontFamily,top:"-9999em"},a.inputStyle))),f.onfocus=function(){n.showInput(t)},f.onblur=function(){f===tv.doc.activeElement&&d(t),n.hideInput(t),n.setInputValue(t),f.blur()};var g=!1;return f.onchange=function(){g||(d(t),n.hideInput(t),f.blur())},f.onkeypress=function(e){13===e.keyCode&&d(t)},f.onkeydown=function(e){g=!0,("ArrowUp"===e.key||"ArrowDown"===e.key||"Tab"===e.key)&&d(t)},f.onkeyup=function(){g=!1},{dateBox:u,input:f,label:p}},t.prototype.getPosition=function(){var t=this.chart,e=t.options.rangeSelector,i="top"===e.verticalAlign?t.plotTop-t.axisOffset[0]:0;return{buttonTop:i+e.buttonPosition.y,inputTop:i+e.inputPosition.y-10}},t.prototype.getYTDExtremes=function(t,e){var i=this.chart.time,o=i.toParts(t)[0];return{max:t,min:Math.max(e,i.makeTime(o,0))}},t.prototype.createElements=function(){var t,e=this.chart,i=e.renderer,o=e.container,n=e.options,r=n.rangeSelector,s=r.inputEnabled,a=pS(null===(t=n.chart.style)||void 0===t?void 0:t.zIndex,0)+1;!1!==r.enabled&&(this.group=i.g("range-selector-group").attr({zIndex:7}).add(),this.div=pp("div",void 0,{position:"relative",height:0,zIndex:a}),this.buttonOptions.length&&this.renderButtons(),o.parentNode&&o.parentNode.insertBefore(this.div,o),s&&this.createInputs())},t.prototype.createInputs=function(){this.inputGroup=this.chart.renderer.g("input-group").add(this.group);var t=this.drawInput("min");this.minDateBox=t.dateBox,this.minLabel=t.label,this.minInput=t.input;var e=this.drawInput("max");this.maxDateBox=e.dateBox,this.maxLabel=e.label,this.maxInput=e.input},t.prototype.render=function(t,e){if(!1!==this.options.enabled){var i,o,n=this.chart,r=n.options.rangeSelector;if(r.inputEnabled){this.inputGroup||this.createInputs(),this.setInputValue("min",t),this.setInputValue("max",e),this.chart.styledMode||(null===(i=this.maxLabel)||void 0===i||i.css(r.labelStyle),null===(o=this.minLabel)||void 0===o||o.css(r.labelStyle));var s=n.scroller&&n.scroller.getUnionExtremes()||n.xAxis[0]||{};if(pf(s.dataMin)&&pf(s.dataMax)){var a=n.xAxis[0].minRange||0;this.setInputExtremes("min",s.dataMin,Math.min(s.dataMax,this.getInputValue("max"))-a),this.setInputExtremes("max",Math.max(s.dataMin,this.getInputValue("min"))+a,s.dataMax)}if(this.inputGroup){var h=0;[this.minLabel,this.minDateBox,this.maxLabel,this.maxDateBox].forEach(function(t){if(t){var e=t.getBBox().width;e&&(t.attr({x:h}),h+=e+r.inputSpacing)}})}}else this.inputGroup&&(this.inputGroup.destroy(),delete this.inputGroup);!this.chart.styledMode&&this.zoomText&&this.zoomText.css(r.labelStyle),this.alignElements(),this.updateButtonStates()}},t.prototype.renderButtons=function(){var t,e,i,o=this,n=this.chart,r=this.options,s=en.lang,a=n.renderer,h=pM(r.buttonTheme),l=h&&h.states;delete h.width,delete h.states,this.buttonGroup=a.g("range-selector-buttons").add(this.group);var d=this.dropdown=pp("select",void 0,{position:"absolute",padding:0,border:0,cursor:"pointer",opacity:1e-4},this.div),c=null===(t=n.userOptions.rangeSelector)||void 0===t?void 0:t.buttonTheme;this.dropdownLabel=a.button("",0,0,function(){},pM(h,{"stroke-width":pS(h["stroke-width"],0),width:"auto",paddingLeft:pS(r.buttonTheme.paddingLeft,null==c?void 0:c.padding,8),paddingRight:pS(r.buttonTheme.paddingRight,null==c?void 0:c.padding,8)}),l&&l.hover,l&&l.select,l&&l.disabled).hide().add(this.group),pc(d,"touchstart",function(){d.style.fontSize="16px"});var p=tv.isMS?"mouseover":"mouseenter",u=tv.isMS?"mouseout":"mouseleave";pc(d,p,function(){px(o.dropdownLabel.element,p)}),pc(d,u,function(){px(o.dropdownLabel.element,u)}),pc(d,"change",function(){px(o.buttons[d.selectedIndex-1].element,"click")}),this.zoomText=a.label(s.rangeSelectorZoom||"",0).attr({padding:r.buttonTheme.padding,height:r.buttonTheme.height,paddingLeft:0,paddingRight:0}).add(this.buttonGroup),this.chart.styledMode||(this.zoomText.css(r.labelStyle),(null===(e=(i=r.buttonTheme)["stroke-width"])||void 0===e)&&(i["stroke-width"]=0)),pp("option",{textContent:this.zoomText.textStr,disabled:!0},void 0,d),this.createButtons()},t.prototype.createButtons=function(){var t=this,e=pM(this.options.buttonTheme),i=e&&e.states,o=e.width||28;delete e.width,delete e.states,this.buttonOptions.forEach(function(e,n){t.createButton(e,n,o,i)})},t.prototype.createButton=function(t,e,i,o){var n,r=this,s=this.dropdown,a=this.buttons,h=this.chart,l=this.options,d=h.renderer,c=pM(l.buttonTheme);null==s||s.add(pp("option",{textContent:t.title||t.text}),e+2),a[e]=d.button(null!==(n=t.text)&&void 0!==n?n:"",0,0,function(i){var o,n=t.events&&t.events.click;n&&(o=n.call(t,i)),!1!==o&&r.clickButton(e),r.isActive=!0},c,o&&o.hover,o&&o.select,o&&o.disabled).attr({"text-align":"center",width:i}).add(this.buttonGroup),t.title&&a[e].attr("title",t.title)},t.prototype.alignElements=function(){var t,e=this,i=this.buttonGroup,o=this.buttons,n=this.chart,r=this.group,s=this.inputGroup,a=this.options,h=this.zoomText,l=n.options,d=l.exporting&&!1!==l.exporting.enabled&&l.navigation&&l.navigation.buttonOptions,c=a.buttonPosition,p=a.inputPosition,u=a.verticalAlign,f=function(t,i,o){return d&&e.titleCollision(n)&&"top"===u&&o&&i.y-t.getBBox().height-12<(d.y||0)+(d.height||0)+n.spacing[0]?-40:0},g=n.plotLeft;if(r&&c&&p){var v=c.x-n.spacing[3];if(i){if(this.positionButtons(),!this.initialButtonGroupWidth){var m=0;h&&(m+=h.getBBox().width+5),o.forEach(function(t,e){m+=t.width||0,e!==o.length-1&&(m+=a.buttonSpacing)}),this.initialButtonGroupWidth=m}g-=n.spacing[3];var y=f(i,c,"right"===c.align||"right"===p.align);this.alignButtonGroup(y),(null===(t=this.buttonGroup)||void 0===t?void 0:t.translateY)&&this.dropdownLabel.attr({y:this.buttonGroup.translateY}),r.placed=i.placed=n.hasLoaded}var x=0;a.inputEnabled&&s&&(x=f(s,p,"right"===c.align||"right"===p.align),"left"===p.align?v=g:"right"===p.align&&(v=-Math.max(n.axisOffset[1],-x)),s.align({y:p.y,width:s.getBBox().width,align:p.align,x:p.x+v-2},!0,n.spacingBox),s.placed=n.hasLoaded),this.handleCollision(x),r.align({verticalAlign:u},!0,n.spacingBox);var b=r.alignAttr.translateY,k=r.getBBox().height+20,M=0;if("bottom"===u){var w=n.legend&&n.legend.options;M=b-(k=k+(w&&"bottom"===w.verticalAlign&&w.enabled&&!w.floating?n.legend.legendHeight+pS(w.margin,10):0)-20)-(a.floating?0:a.y)-(n.titleOffset?n.titleOffset[2]:0)-10}"top"===u?(a.floating&&(M=0),n.titleOffset&&n.titleOffset[0]&&(M=n.titleOffset[0]),M+=n.margin[0]-n.spacing[0]||0):"middle"===u&&(p.y===c.y?M=b:(p.y||c.y)&&(p.y<0||c.y<0?M-=Math.min(p.y,c.y):M=b-k)),r.translate(a.x,a.y+Math.floor(M));var S=this.minInput,A=this.maxInput,T=this.dropdown;a.inputEnabled&&S&&A&&(S.style.marginTop=r.translateY+"px",A.style.marginTop=r.translateY+"px"),T&&(T.style.marginTop=r.translateY+"px")}},t.prototype.redrawElements=function(){var t,e,i,o,n,r,s,a=this.chart,h=this.options,l=h.inputBoxHeight,d=h.inputBoxBorderColor;if(null===(t=this.maxDateBox)||void 0===t||t.attr({height:l}),null===(e=this.minDateBox)||void 0===e||e.attr({height:l}),a.styledMode||(null===(i=this.maxDateBox)||void 0===i||i.attr({stroke:d}),null===(o=this.minDateBox)||void 0===o||o.attr({stroke:d})),this.isDirty){this.isDirty=!1,this.isCollapsed=void 0;var c=null!==(n=this.options.buttons)&&void 0!==n?n:[],p=Math.min(c.length,this.buttonOptions.length),u=this.dropdown,f=pM(this.options.buttonTheme),g=f&&f.states,v=f.width||28;if(c.length<this.buttonOptions.length)for(var m=this.buttonOptions.length-1;m>=c.length;m--){var y=this.buttons.pop();null==y||y.destroy(),null===(r=this.dropdown)||void 0===r||r.options.remove(m+1)}for(var m=p-1;m>=0;m--)if(0!==Object.keys(pv(c[m],this.buttonOptions[m])).length){var x=c[m];this.buttons[m].destroy(),null==u||u.options.remove(m+1),this.createButton(x,m,v,g),this.computeButtonRange(x)}if(c.length>this.buttonOptions.length)for(var m=this.buttonOptions.length;m<c.length;m++)this.createButton(c[m],m,v,g),this.computeButtonRange(c[m]);this.buttonOptions=null!==(s=this.options.buttons)&&void 0!==s?s:[],pf(this.options.selected)&&this.buttons.length&&this.clickButton(this.options.selected,!1)}},t.prototype.alignButtonGroup=function(t,e){var i=this.chart,o=this.options,n=this.buttonGroup,r=this.dropdown,s=this.dropdownLabel,a=o.buttonPosition,h=i.plotLeft-i.spacing[3],l=a.x-i.spacing[3],d=i.plotLeft;"right"===a.align?(l+=t-h,this.hasVisibleDropdown&&(d=i.chartWidth+t-this.maxButtonWidth()-20)):"center"===a.align&&(l-=h/2,this.hasVisibleDropdown&&(d=i.chartWidth/2-this.maxButtonWidth())),r&&pu(r,{left:d+"px",top:(null==n?void 0:n.translateY)+"px"}),null==s||s.attr({x:d}),n&&n.align({y:a.y,width:pS(e,this.initialButtonGroupWidth),align:a.align,x:l},!0,i.spacingBox)},t.prototype.positionButtons=function(){var t=this.buttons,e=this.chart,i=this.options,o=this.zoomText,n=e.hasLoaded?"animate":"attr",r=i.buttonPosition,s=e.plotLeft,a=s;o&&"hidden"!==o.visibility&&(o[n]({x:pS(s+r.x,s)}),a+=r.x+o.getBBox().width+5);for(var h=0,l=this.buttonOptions.length;h<l;++h)"hidden"!==t[h].visibility?(t[h][n]({x:a}),a+=(t[h].width||0)+i.buttonSpacing):t[h][n]({x:s})},t.prototype.handleCollision=function(t){var e=this.chart,i=this.buttonGroup,o=this.inputGroup,n=this.initialButtonGroupWidth,r=this.options,s=r.buttonPosition,a=r.dropdown,h=r.inputPosition,l=function(){o&&i&&o.attr({translateX:o.alignAttr.translateX+(e.axisOffset[1]>=-t?0:-t),translateY:o.alignAttr.translateY+i.getBBox().height+10})};o&&i?h.align===s.align?(l(),n>e.plotWidth+t-20?this.collapseButtons():this.expandButtons()):n-t+o.getBBox().width>e.plotWidth?"responsive"===a?this.collapseButtons():l():this.expandButtons():i&&"responsive"===a&&(n>e.plotWidth?this.collapseButtons():this.expandButtons()),i&&("always"===a&&this.collapseButtons(),"never"===a&&this.expandButtons()),this.alignButtonGroup(t)},t.prototype.collapseButtons=function(){var t=this.buttons,e=this.zoomText;!0!==this.isCollapsed&&(this.isCollapsed=!0,e.hide(),t.forEach(function(t){t.hide()}),this.showDropdown())},t.prototype.expandButtons=function(){var t=this.buttons,e=this.zoomText;!1!==this.isCollapsed&&(this.isCollapsed=!1,this.hideDropdown(),e.show(),t.forEach(function(t){t.show()}),this.positionButtons())},t.prototype.showDropdown=function(){var t=this.buttonGroup,e=this.dropdownLabel,i=this.dropdown;t&&i&&(e.show(),pu(i,{visibility:"inherit"}),this.hasVisibleDropdown=!0)},t.prototype.hideDropdown=function(){var t=this.dropdown;t&&(this.dropdownLabel.hide(),pu(t,{visibility:"hidden",width:"1px",height:"1px"}),this.hasVisibleDropdown=!1)},t.prototype.getHeight=function(){var t=this.options,e=this.group,i=t.inputPosition,o=t.buttonPosition,n=t.y,r=o.y,s=i.y,a=0;if(t.height)return t.height;this.alignElements(),a=e?e.getBBox(!0).height+13+n:0;var h=Math.min(s,r);return(s<0&&r<0||s>0&&r>0)&&(a+=Math.abs(h)),a},t.prototype.titleCollision=function(t){return!(t.options.title.text||t.options.subtitle.text)},t.prototype.update=function(t,e){void 0===e&&(e=!0);var i=this.chart;if(pM(!0,this.options,t),this.options.selected&&this.options.selected>=this.options.buttons.length&&(this.options.selected=void 0,i.options.rangeSelector.selected=void 0),pf(t.enabled))return this.destroy(),this.init(i);this.isDirty=!!t.buttons,e&&this.render()},t.prototype.destroy=function(){var e=this,i=e.minInput,o=e.maxInput;e.eventsToUnbind&&(e.eventsToUnbind.forEach(function(t){return t()}),e.eventsToUnbind=void 0),pg(e.buttons),i&&(i.onfocus=i.onblur=i.onchange=null),o&&(o.onfocus=o.onblur=o.onchange=null),pw(e,function(i,o){i&&"chart"!==o&&(i instanceof iR?i.destroy():i instanceof window.HTMLElement&&pm(i),delete e[o]),i!==t.prototype[o]&&(e[o]=null)},this),this.buttons=[]},t}();py(pC.prototype,{inputTypeFormats:{"datetime-local":"%Y-%m-%dT%H:%M:%S",date:"%Y-%m-%d",time:"%H:%M:%S"}});var pO=(E=function(t,e){return(E=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}E(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),pP=e8.format,pE=d8.setFixedRange,pL=tF.addEvent,pB=tF.clamp,pD=tF.crisp,pI=tF.defined,pz=tF.extend,pR=tF.find,pN=tF.isNumber,pW=tF.isString,pG=tF.merge,pX=tF.pick,pH=tF.splat;function pF(t,e,i){var o,n;return"xAxis"===t?{minPadding:0,maxPadding:0,overscroll:0,ordinal:!0}:"yAxis"===t?{labels:{y:-2},opposite:null===(n=null!==(o=i.opposite)&&void 0!==o?o:e.opposite)||void 0===n||n,showLastLabel:!!(e.categories||"category"===e.type),title:{text:void 0}}:{}}function pY(t,e){var i;if("xAxis"===t){var o=pX(null===(i=e.navigator)||void 0===i?void 0:i.enabled,d3.enabled,!0),n={type:"datetime",categories:void 0};return o&&(n.startOnTick=!1,n.endOnTick=!1),n}return{}}var p_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return pO(e,t),e.prototype.init=function(e,i){var o,n,r=es(),s=e.xAxis,a=e.yAxis,h=pX(null===(o=e.navigator)||void 0===o?void 0:o.enabled,d3.enabled,!0);e.xAxis=e.yAxis=void 0;var l=pG({chart:{panning:{enabled:!0,type:"x"},zooming:{pinchType:"x",mouseWheel:{type:"x"}}},navigator:{enabled:h},scrollbar:{enabled:pX(cu.enabled,!0)},rangeSelector:{enabled:pX(c1.rangeSelector.enabled,!0)},title:{text:null},tooltip:{split:pX(null===(n=r.tooltip)||void 0===n?void 0:n.split,!0),crosshairs:!0},legend:{enabled:!1}},e,{isStock:!0});e.xAxis=s,e.yAxis=a,l.xAxis=pH(e.xAxis||{}).map(function(t){return pG(pF("xAxis",t,r.xAxis),t,pY("xAxis",e))}),l.yAxis=pH(e.yAxis||{}).map(function(t){return pG(pF("yAxis",t,r.yAxis),t)}),t.prototype.init.call(this,l,i)},e.prototype.createAxis=function(e,i){return i.axis=pG(pF(e,i.axis,es()[e]),i.axis,pY(e,this.userOptions)),t.prototype.createAxis.call(this,e,i)},e}(aD);pL(aD,"update",function(t){var e=t.options;"scrollbar"in e&&this.navigator&&(pG(!0,this.options.scrollbar,e.scrollbar),this.navigator.update({enabled:!!this.navigator.navigatorEnabled}),delete e.scrollbar)}),function(t){function e(t){if((null===(i=null===(e=this.crosshair)||void 0===e?void 0:e.label)||void 0===i?void 0:i.enabled)&&this.cross&&pN(this.min)&&pN(this.max)){var e,i,o,n,r,s,a,h=this.chart,l=this.logarithmic,d=this.crosshair.label,c=this.horiz,p=this.opposite,u=this.left,f=this.top,g=this.width,v="inside"===this.options.tickPosition,m=!1!==this.crosshair.snap,y=t.e||(null===(o=this.cross)||void 0===o?void 0:o.e),x=t.point,b=this.crossLabel,k=d.format,M="",w=0,S=this.min,A=this.max;l&&(S=l.lin2log(this.min),A=l.lin2log(this.max));var T=c?"center":p?"right"===this.labelAlign?"right":"left":"left"===this.labelAlign?"left":"center";b||(b=this.crossLabel=h.renderer.label("",0,void 0,d.shape||"callout").addClass("highcharts-crosshair-label highcharts-color-"+((null==x?void 0:x.series)?x.series.colorIndex:this.series[0]&&this.series[0].colorIndex)).attr({align:d.align||T,padding:pX(d.padding,8),r:pX(d.borderRadius,3),zIndex:2}).add(this.labelGroup),h.styledMode||b.attr({fill:d.backgroundColor||(null===(n=null==x?void 0:x.series)||void 0===n?void 0:n.color)||"#666666",stroke:d.borderColor||"","stroke-width":d.borderWidth||0}).css(pz({color:"#ffffff",fontWeight:"normal",fontSize:"0.7em",textAlign:"center"},d.style||{}))),c?(r=m?(x.plotX||0)+u:y.chartX,s=f+(p?0:this.height)):(r=u+this.offset+(p?g:0),s=m?(x.plotY||0)+f:y.chartY),k||d.formatter||(this.dateTime&&(M="%b %d, %Y"),k="{value"+(M?":"+M:"")+"}");var C=m?this.isXAxis?x.x:x.y:this.toValue(c?y.chartX:y.chartY),O=(null==x?void 0:x.series)?x.series.isPointInside(x):pN(C)&&C>S&&C<A,P="";k?P=pP(k,{value:C},h):d.formatter&&pN(C)&&(P=d.formatter.call(this,C)),b.attr({text:P,x:r,y:s,visibility:O?"inherit":"hidden"});var E=b.getBBox();!pN(b.x)||c||p||(r=b.x-E.width/2),pN(b.y)&&(c?(v&&!p||!v&&p)&&(s=b.y-E.height):s=b.y-E.height/2),a=c?{left:u,right:u+this.width}:{left:"left"===this.labelAlign?u:0,right:"right"===this.labelAlign?u+this.width:h.chartWidth};var L=b.translateX||0;L<a.left&&(w=a.left-L),L+E.width>=a.right&&(w=-(L+E.width-a.right)),b.attr({x:Math.max(0,r+w),y:Math.max(0,s),anchorX:c?r:this.opposite?0:h.chartWidth,anchorY:c?this.opposite?h.chartHeight:0:s+E.height/2})}}function i(){this.crossLabel&&(this.crossLabel=this.crossLabel.hide())}function o(t){var e=this.chart,i=this.options,o=e._labelPanes=e._labelPanes||{},n=i.labels;if(e.options.isStock&&"yAxis"===this.coll){var r=i.top+","+i.height;!o[r]&&n.enabled&&(15===n.distance&&1===this.side&&(n.distance=0),void 0===n.align&&(n.align="right"),o[r]=this,t.align="right",t.preventDefault())}}function n(){var t=this.chart,e=this.options&&this.options.top+","+this.options.height;e&&t._labelPanes&&t._labelPanes[e]===this&&delete t._labelPanes[e]}function r(t){var e,i,o,n,r,s,a,h=this.isLinked&&!this.series&&this.linkedParent?this.linkedParent.series:this.series,l=this.chart,d=l.renderer,c=this.left,p=this.top,u=[],f=t.translatedValue,g=t.value,v=t.force,m=[];if(l.options.isStock&&!1!==t.acrossPanes&&"xAxis"===this.coll||"yAxis"===this.coll){t.preventDefault(),y="xAxis"===this.coll?"yAxis":"xAxis",m=pN(x=this.options[y])?[l[y][x]]:pW(x)?[l.get(x)]:h.map(function(t){return t[y]}),r=this.isXAxis?l.yAxis:l.xAxis;for(var y,x,b=0;b<r.length;b++){var k=r[b];if(!k.options.isInternal){var M=k.isXAxis?"yAxis":"xAxis";this===(pI(k.options[M])?l[M][k.options[M]]:l[M][0])&&m.push(k)}}s=m.length?[]:[this.isXAxis?l.yAxis[0]:l.xAxis[0]];for(var w=function(t){-1!==s.indexOf(t)||pR(s,function(e){return e.pos===t.pos&&e.len===t.len})||s.push(t)},S=0,A=m;S<A.length;S++){var T=A[S];w(T)}if(pN(a=pX(f,this.translate(g||0,void 0,void 0,t.old)))){if(this.horiz)for(var C=0;C<s.length;C++){var T=s[C],O=void 0;n=(i=T.pos)+T.len,e=o=Math.round(a+this.transB),"pass"!==v&&(e<c||e>c+this.width)&&(v?e=o=pB(e,c,c+this.width):O=!0),O||u.push(["M",e,i],["L",o,n])}else for(var P=0;P<s.length;P++){var T=s[P],O=void 0;o=(e=T.pos)+T.len,i=n=Math.round(p+this.height-a),"pass"!==v&&(i<p||i>p+this.height)&&(v?i=n=pB(i,p,p+this.height):O=!0),O||u.push(["M",e,i],["L",o,n])}}t.path=u.length>0?d.crispPolyLine(u,t.lineWidth||1):void 0}}function s(t){if(this.chart.options.isStock){var e=void 0;this.is("column")||this.is("columnrange")?e={borderWidth:0,shadow:!1}:this.is("scatter")||this.is("sma")||(e={marker:{enabled:!1,radius:2}}),e&&(t.plotOptions[this.type]=pG(t.plotOptions[this.type],e))}}function a(){var t=this.chart,e=this.options.dataGrouping;return!1!==this.allowDG&&e&&pX(e.enabled,t.options.isStock)}function h(t,e){for(var i=0;i<t.length;i+=2){var o=t[i],n=t[i+1];pI(o[1])&&o[1]===n[1]&&(o[1]=n[1]=pD(o[1],e)),pI(o[2])&&o[2]===n[2]&&(o[2]=n[2]=pD(o[2],e))}return t}t.compose=function(t,l,d,c){var p=d.prototype;p.forceCropping||(pL(l,"afterDrawCrosshair",e),pL(l,"afterHideCrosshair",i),pL(l,"autoLabelAlign",o),pL(l,"destroy",n),pL(l,"getPlotLinePath",r),t.prototype.setFixedRange=pE,p.forceCropping=a,pL(d,"setOptions",s),c.prototype.crispPolyLine=h)},t.stockChart=function(e,i,o){return new t(e,i,o)}}(p_||(p_={}));var pj=p_,pU=(L=function(t,e){return(L=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}L(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),pV=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return pU(e,t),e}(ss.seriesTypes.column.prototype.pointClass),pZ={lineWidth:1,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>{series.chart.options.lang.stockHigh}: {point.high}<br/>{series.chart.options.lang.stockLow}: {point.low}<br/>{series.chart.options.lang.stockClose}: {point.close}<br/>'},threshold:null,states:{hover:{lineWidth:3}},stickyTracking:!0},pq=(B=function(t,e){return(B=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}B(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),pK=ss.seriesTypes.column,p$=tF.crisp,pJ=tF.extend,pQ=tF.merge,p0=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return pq(e,t),e.prototype.extendStem=function(t,e,i){var o=t[0],n=t[1];"number"==typeof o[2]&&(o[2]=Math.max(i+e,o[2])),"number"==typeof n[2]&&(n[2]=Math.min(i-e,n[2]))},e.prototype.getPointPath=function(t,e){var i=e.strokeWidth(),o=t.series,n=p$(t.plotX||0,i),r=Math.round(t.shapeArgs.width/2),s=[["M",n,Math.round(t.yBottom)],["L",n,Math.round(t.plotHigh)]];if(null!==t.close){var a=p$(t.plotClose,i);s.push(["M",n,a],["L",n+r,a]),o.extendStem(s,i/2,a)}return s},e.prototype.drawSinglePoint=function(t){var e,i=t.series,o=i.chart,n=t.graphic;void 0!==t.plotY&&(n||(t.graphic=n=o.renderer.path().add(i.group)),o.styledMode||n.attr(i.pointAttribs(t,t.selected&&"select")),e=i.getPointPath(t,n),n[n?"animate":"attr"]({d:e}).addClass(t.getClassName(),!0))},e.prototype.drawPoints=function(){this.points.forEach(this.drawSinglePoint)},e.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options.stacking=void 0},e.prototype.pointAttribs=function(e,i){var o=t.prototype.pointAttribs.call(this,e,i);return delete o.fill,o},e.prototype.toYData=function(t){return[t.high,t.low,t.close]},e.prototype.translate=function(){var e=this,i=e.yAxis,o=this.pointArrayMap&&this.pointArrayMap.slice()||[],n=o.map(function(t){return"plot".concat(t.charAt(0).toUpperCase()+t.slice(1))});n.push("yBottom"),o.push("low"),t.prototype.translate.apply(e),e.points.forEach(function(t){o.forEach(function(o,r){var s=t[o];null!==s&&(e.dataModify&&(s=e.dataModify.modifyValue(s)),t[n[r]]=i.toPixels(s,!0))}),t.tooltipPos[1]=t.plotHigh+i.pos-e.chart.plotTop})},e.defaultOptions=pQ(pK.defaultOptions,pZ),e}(pK);pJ(p0.prototype,{pointClass:pV,animate:null,directTouch:!1,keysAffectYAxis:["low","high"],pointArrayMap:["high","low","close"],pointAttrToOptions:{stroke:"color","stroke-width":"lineWidth"},pointValKey:"close"}),pJ(en.lang,{stockOpen:"Open",stockHigh:"High",stockLow:"Low",stockClose:"Close"}),ss.registerSeriesType("hlc",p0);var p1=(D=function(t,e){return(D=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}D(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),p2=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return p1(e,t),e.prototype.getClassName=function(){return t.prototype.getClassName.call(this)+(this.open<this.close?" highcharts-point-up":" highcharts-point-down")},e.prototype.resolveUpColor=function(){this.open<this.close&&!this.options.color&&this.series.options.upColor&&(this.color=this.series.options.upColor)},e.prototype.resolveColor=function(){t.prototype.resolveColor.call(this),this.series.is("heikinashi")||this.resolveUpColor()},e.prototype.getZone=function(){var e=t.prototype.getZone.call(this);return this.resolveUpColor(),e},e.prototype.applyOptions=function(){return t.prototype.applyOptions.apply(this,arguments),this.resolveColor&&this.resolveColor(),this},e}(ss.seriesTypes.hlc.prototype.pointClass),p3={tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>{series.chart.options.lang.stockOpen}: {point.open}<br/>{series.chart.options.lang.stockHigh}: {point.high}<br/>{series.chart.options.lang.stockLow}: {point.low}<br/>{series.chart.options.lang.stockClose}: {point.close}<br/>'}},p5=(I=function(t,e){return(I=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}I(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),p6=tv.composed,p9=ss.seriesTypes.hlc,p4=tF.addEvent,p8=tF.crisp,p7=tF.extend,ut=tF.merge,ue=tF.pushUnique;function ui(t){var e=t.options,i=e.dataGrouping;i&&e.useOhlcData&&"highcharts-navigator-series"!==e.id&&(i.approximation="ohlc")}function uo(t){var e=t.options;e.useOhlcData&&"highcharts-navigator-series"!==e.id&&p7(this,{pointValKey:un.prototype.pointValKey,pointArrayMap:un.prototype.pointArrayMap,toYData:un.prototype.toYData})}var un=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return p5(e,t),e.compose=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];ue(p6,"OHLCSeries")&&(p4(t,"afterSetOptions",ui),p4(t,"init",uo))},e.prototype.getPointPath=function(e,i){var o=t.prototype.getPointPath.call(this,e,i),n=i.strokeWidth(),r=p8(e.plotX||0,n),s=Math.round(e.shapeArgs.width/2);if(null!==e.open){var a=p8(e.plotOpen,n);o.push(["M",r,a],["L",r-s,a]),t.prototype.extendStem.call(this,o,n/2,a)}return o},e.prototype.pointAttribs=function(e,i){var o=t.prototype.pointAttribs.call(this,e,i),n=this.options;return delete o.fill,!e.options.color&&n.upColor&&e.open<e.close&&(o.stroke=n.upColor),o},e.prototype.toYData=function(t){return[t.open,t.high,t.low,t.close]},e.defaultOptions=ut(p9.defaultOptions,p3),e}(p9);p7(un.prototype,{pointClass:p2,pointArrayMap:["open","high","low","close"]}),ss.registerSeriesType("ohlc",un);var ur={states:{hover:{lineWidth:2}},threshold:null,lineColor:"#000000",lineWidth:1,upColor:"#ffffff",stickyTracking:!0},us=(z=function(t,e){return(z=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}z(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ua=ss.seriesTypes,uh=ua.column,ul=ua.ohlc,ud=tF.crisp,uc=tF.merge,up=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return us(e,t),e.prototype.pointAttribs=function(t,e){var i=uh.prototype.pointAttribs.call(this,t,e),o=this.options,n=t.open<t.close,r=o.lineColor||this.color,s=t.color||this.color;if(i["stroke-width"]=o.lineWidth,i.fill=t.options.color||n&&o.upColor||s,i.stroke=t.options.lineColor||n&&o.upLineColor||r,e){var a=o.states[e];i.fill=a.color||i.fill,i.stroke=a.lineColor||i.stroke,i["stroke-width"]=a.lineWidth||i["stroke-width"]}return i},e.prototype.drawPoints=function(){for(var t=this.points,e=this.chart,i=this.yAxis.reversed,o=0;o<t.length;o++){var n=t[o],r=n.graphic,s=void 0,a=void 0,h=void 0,l=void 0,d=void 0,c=void 0,p=void 0,u=void 0,f=void 0,g=!r;if(void 0!==n.plotY){r||(n.graphic=r=e.renderer.path().add(this.group)),this.chart.styledMode||r.attr(this.pointAttribs(n,n.selected&&"select")).shadow(this.options.shadow);var v=r.strokeWidth();p=ud(n.plotX||0,v),h=Math.min(s=n.plotOpen,a=n.plotClose),l=Math.max(s,a),f=Math.round(n.shapeArgs.width/2),d=i?l!==n.yBottom:Math.round(h)!==Math.round(n.plotHigh||0),c=i?Math.round(h)!==Math.round(n.plotHigh||0):l!==n.yBottom,h=ud(h,v),l=ud(l,v),(u=[]).push(["M",p-f,l],["L",p-f,h],["L",p+f,h],["L",p+f,l],["Z"],["M",p,h],["L",p,d?Math.round(i?n.yBottom:n.plotHigh):h],["M",p,l],["L",p,c?Math.round(i?n.plotHigh:n.yBottom):l]),r[g?"attr":"animate"]({d:u}).addClass(n.getClassName(),!0)}}},e.defaultOptions=uc(ul.defaultOptions,{tooltip:ul.defaultOptions.tooltip},ur),e}(ul);ss.registerSeriesType("candlestick",up);var uu=(R=function(t,e){return(R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}R(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uf=ss.seriesTypes.column.prototype.pointClass,ug=tF.isNumber,uv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ttBelow=!1,e}return uu(e,t),e.prototype.isValid=function(){return ug(this.y)||void 0===this.y},e.prototype.hasNewShapeType=function(){var t=this.options.shape||this.series.options.shape;return this.graphic&&t&&t!==this.graphic.symbolKey},e}(uf),um={borderRadius:0,pointRange:0,allowOverlapX:!1,shape:"flag",stackDistance:12,textAlign:"center",tooltip:{pointFormat:"{point.text}"},threshold:null,y:-30,fillColor:"#ffffff",lineWidth:1,states:{hover:{lineColor:"#000000",fillColor:"#ccd3ff"}},style:{fontSize:"0.7em",fontWeight:"bold"}};!function(t){var e=[];function i(t,e,i,o,n){var r=n&&n.anchorX||t,s=n&&n.anchorY||e,a=this.circle(r-1,s-1,2,2);return a.push(["M",r,s],["L",t,e+o],["L",t,e],["L",t+i,e],["L",t+i,e+o],["L",t,e+o],["Z"]),a}function o(t,e){t[e+"pin"]=function(i,o,n,r,s){var a,h=s&&s.anchorX,l=s&&s.anchorY;if("circle"===e&&r>n&&(i-=Math.round((r-n)/2),n=r),a=t[e](i,o,n,r,s),h&&l){var d=h;if("circle"===e)d=i+n/2;else{var c=a[0],p=a[1];"M"===c[0]&&"L"===p[0]&&(d=(c[1]+p[1])/2)}var u=o>l?o:o+r;a.push(["M",d,u],["L",h,l]),a=a.concat(t.circle(h-1,l-1,2,2))}return a}}t.compose=function(t){if(-1===e.indexOf(t)){e.push(t);var n=t.prototype.symbols;n.flag=i,o(n,"circle"),o(n,"square")}var r=e7.getRendererType();e.indexOf(r)&&e.push(r)}}(tl||(tl={}));var uy=tl,ux=tv.composed,ub=hq.prototype,uk=sW.prototype,uM=tF.defined,uw=tF.pushUnique,uS=tF.stableSort;!function(t){function e(t){return uk.getPlotBox.call(this.options.onSeries&&this.chart.get(this.options.onSeries)||this,t)}function i(){ub.translate.apply(this);var t,e,i,o,n,r,s,a,h,l=this,d=l.options,c=l.chart,p=l.points,u=d.onSeries,f=u&&c.get(u),g=f&&f.options.step,v=f&&f.points,m=c.inverted,y=l.xAxis,x=l.yAxis,b=p.length-1,k=d.onKey||"y",M=v&&v.length,w=0;if(f&&f.visible&&M)for(w=(f.pointXOffset||0)+(f.barW||0)/2,a=f.currentDataGrouping,r=v[M-1].x+(a?a.totalRange:0),uS(p,function(t,e){return t.x-e.x}),k="plot"+k[0].toUpperCase()+k.substr(1);M--&&p[b]&&"break"!==function(){if(n=v[M],(i=p[b]).y=n.y,n.x<=i.x&&void 0!==n[k]){if(i.x<=r&&(i.plotY=n[k],n.x<i.x&&!g&&(s=v[M+1])&&void 0!==s[k])){if(uM(i.plotX)&&f.is("spline")){for(var o=[n.plotX||0,n.plotY||0],a=[s.plotX||0,s.plotY||0],l=(null===(t=n.controlPoints)||void 0===t?void 0:t.high)||o,d=(null===(e=s.controlPoints)||void 0===e?void 0:e.low)||a,c=function(t,e){return Math.pow(1-t,3)*o[e]+3*(1-t)*(1-t)*t*l[e]+3*(1-t)*t*t*d[e]+t*t*t*a[e]},u=0,m=1,y=void 0,w=0;w<100;w++){var S=(u+m)/2,A=c(S,0);if(null===A)break;if(.25>Math.abs(A-i.plotX)){y=S;break}A<i.plotX?u=S:m=S}uM(y)&&(i.plotY=c(y,1),i.y=x.toValue(i.plotY,!0))}else h=(i.x-n.x)/(s.x-n.x),i.plotY+=h*(s[k]-n[k]),i.y+=h*(s.y-n.y)}if(b--,M++,b<0)return"break"}}(););p.forEach(function(t,e){var i;t.plotX+=w,(void 0===t.plotY||m)&&(t.plotX>=0&&t.plotX<=y.len?m?(t.plotY=y.translate(t.x,0,1,0,1),t.plotX=uM(t.y)?x.translate(t.y,0,0,0,1):0):t.plotY=(y.opposite?0:l.yAxis.len)+y.offset:t.shapeArgs={}),(o=p[e-1])&&o.plotX===t.plotX&&(void 0===o.stackIndex&&(o.stackIndex=0),i=o.stackIndex+1),t.stackIndex=i}),this.onSeries=f}t.compose=function(t){if(uw(ux,"OnSeries")){var o=t.prototype;o.getPlotBox=e,o.translate=i}return t},t.getPlotBox=e,t.translate=i}(td||(td={}));var uA=td,uT=(N=function(t,e){return(N=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}N(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),uC=tv.noop,uO=ir.distribute,uP=ss.series,uE=ss.seriesTypes.column,uL=tF.addEvent,uB=tF.defined,uD=tF.extend,uI=tF.isNumber,uz=tF.merge,uR=tF.objectEach,uN=tF.wrap,uW=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return uT(e,t),e.prototype.animate=function(t){t&&this.setClip()},e.prototype.drawPoints=function(){var t,e,i,o,n,r,s,a,h,l,d,c,p,u=this.points,f=this.chart,g=f.renderer,v=f.inverted,m=this.options,y=m.y,x=this.yAxis,b={},k=[],M=uI(m.borderRadius)?m.borderRadius:0;for(r=u.length;r--;)s=u[r],c=(v?s.plotY:s.plotX)>this.xAxis.len,i=s.plotX,h=s.stackIndex,n=s.options.shape||m.shape,void 0!==(o=s.plotY)&&(o=s.plotY+y-(void 0!==h&&h*m.stackDistance)),s.anchorX=h?void 0:s.plotX,l=h?void 0:s.plotY,p="flag"!==n,a=s.graphic,void 0!==o&&i>=0&&!c?(a&&s.hasNewShapeType()&&(a=a.destroy()),a||(a=s.graphic=g.label("",0,void 0,n,void 0,void 0,m.useHTML).addClass("highcharts-point").add(this.markerGroup),s.graphic.div&&(s.graphic.div.point=s),a.isNew=!0),a.attr({align:p?"center":"left",width:m.width,height:m.height,"text-align":m.textAlign,r:M}),f.styledMode||a.attr(this.pointAttribs(s)).css(uz(m.style,s.style)).shadow(m.shadow),i>0&&(i-=a.strokeWidth()%2),d={y:o,anchorY:l},m.allowOverlapX&&(d.x=i,d.anchorX=s.anchorX),a.attr({text:null!==(e=null!==(t=s.options.title)&&void 0!==t?t:m.title)&&void 0!==e?e:"A"})[a.isNew?"attr":"animate"](d),m.allowOverlapX||(b[s.plotX]?b[s.plotX].size=Math.max(b[s.plotX].size,a.width||0):b[s.plotX]={align:.5*!!p,size:a.width||0,target:i,anchorX:i}),s.tooltipPos=[i,o+x.pos-f.plotTop]):a&&(s.graphic=a.destroy());if(!m.allowOverlapX){var w=100;uR(b,function(t){t.plotX=t.anchorX,k.push(t),w=Math.max(t.size,w)}),uO(k,v?x.len:this.xAxis.len,w);for(var S=0;S<u.length;S++){var A=u[S],T=A.plotX,C=A.graphic,O=C&&b[T];O&&C&&(uB(O.pos)?C[C.isNew?"attr":"animate"]({x:O.pos+(O.align||0)*O.size,anchorX:A.anchorX}).show().isNew=!1:C.hide().isNew=!0)}}m.useHTML&&this.markerGroup&&uN(this.markerGroup,"on",function(t){return iR.prototype.on.apply(t.apply(this,[].slice.call(arguments,1)),[].slice.call(arguments,1))})},e.prototype.drawTracker=function(){var e=this.points;t.prototype.drawTracker.call(this);for(var i=function(t){var i=t.graphic;i&&(t.unbindMouseOver&&t.unbindMouseOver(),t.unbindMouseOver=uL(i.element,"mouseover",function(){t.stackIndex>0&&!t.raised&&(t._y=i.y,i.attr({y:t._y-8}),t.raised=!0);for(var o=0;o<e.length;o++){var n=e[o];n!==t&&n.raised&&n.graphic&&(n.graphic.attr({y:n._y}),n.raised=!1)}}))},o=0;o<e.length;o++)i(e[o])},e.prototype.pointAttribs=function(t,e){var i=this.options,o=t&&t.color||this.color,n=i.lineColor,r=t&&t.lineWidth,s=t&&t.fillColor||i.fillColor;return e&&(s=i.states[e].fillColor,n=i.states[e].lineColor,r=i.states[e].lineWidth),{fill:s||o,stroke:n||o,"stroke-width":r||i.lineWidth||0}},e.prototype.setClip=function(){uP.prototype.setClip.apply(this,arguments),!1!==this.options.clip&&this.sharedClipKey&&this.markerGroup&&this.markerGroup.clip(this.chart.sharedClips[this.sharedClipKey])},e.compose=uy.compose,e.defaultOptions=uz(uE.defaultOptions,um),e}(uE);uA.compose(uW),uD(uW.prototype,{allowDG:!1,forceCrop:!0,invertible:!1,noSharedTooltip:!0,pointClass:uv,sorted:!1,takeOrdinalPosition:!1,trackerGroups:["markerGroup"],buildKDTree:uC,init:uP.prototype.init}),ss.registerSeriesType("flags",uW);var uG=tF.addEvent,uX=tF.find,uH=tF.fireEvent,uF=tF.isArray,uY=tF.isNumber,u_=tF.pick;!function(t){function e(){void 0!==this.brokenAxis&&this.brokenAxis.setBreaks(this.options.breaks,!1)}function i(){var t;(null===(t=this.brokenAxis)||void 0===t?void 0:t.hasBreaks)&&(this.options.ordinal=!1)}function o(){var t=this.brokenAxis;if(null==t?void 0:t.hasBreaks){for(var e=this.tickPositions,i=this.tickPositions.info,o=[],n=0;n<e.length;n++)t.isInAnyBreak(e[n])||o.push(e[n]);this.tickPositions=o,this.tickPositions.info=i}}function n(){this.brokenAxis||(this.brokenAxis=new l(this))}function r(){var t,e,i=this.isDirty,o=this.options.connectNulls,n=this.points,r=this.xAxis,s=this.yAxis;if(i)for(var a=n.length;a--;){var h=n[a],l=(null!==h.y||!1!==o)&&((null===(t=null==r?void 0:r.brokenAxis)||void 0===t?void 0:t.isInAnyBreak(h.x,!0))||(null===(e=null==s?void 0:s.brokenAxis)||void 0===e?void 0:e.isInAnyBreak(h.y,!0)));h.visible=!l&&!1!==h.options.visible}}function s(){this.drawBreaks(this.xAxis,["x"]),this.drawBreaks(this.yAxis,u_(this.pointArrayMap,["y"]))}function a(t,e){var i,o,n,r,s=this,a=s.points;if(null===(i=null==t?void 0:t.brokenAxis)||void 0===i?void 0:i.hasBreaks){var h=t.brokenAxis;e.forEach(function(e){o=(null==h?void 0:h.breakArray)||[],n=t.isXAxis?t.min:u_(s.options.threshold,t.min);var i,l,d=null===(l=null===(i=null==t?void 0:t.options)||void 0===i?void 0:i.breaks)||void 0===l?void 0:l.filter(function(t){for(var e=!0,i=0;i<o.length;i++){var n=o[i];if(n.from===t.from&&n.to===t.to){e=!1;break}}return e});a.forEach(function(i){r=u_(i["stack"+e.toUpperCase()],i[e]),o.forEach(function(e){if(uY(n)&&uY(r)){var o="";n<e.from&&r>e.to||n>e.from&&r<e.from?o="pointBreak":(n<e.from&&r>e.from&&r<e.to||n>e.from&&r>e.to&&r<e.from)&&(o="pointInBreak"),o&&uH(t,o,{point:i,brk:e})}}),null==d||d.forEach(function(e){uH(t,"pointOutsideOfBreak",{point:i,brk:e})})})})}}function h(){var t=this.currentDataGrouping,e=null==t?void 0:t.gapSize,i=this.points.slice(),o=this.yAxis,n=this.options.gapSize,r=i.length-1;if(n&&r>0){"value"!==this.options.gapUnit&&(n*=this.basePointRange),e&&e>n&&e>=this.basePointRange&&(n=e);for(var s=void 0,a=void 0;r--;)if(a&&!1!==a.visible||(a=i[r+1]),s=i[r],!1!==a.visible&&!1!==s.visible){if(a.x-s.x>n){var h=(s.x+a.x)/2;i.splice(r+1,0,{isNull:!0,x:h}),o.stacking&&this.options.stacking&&((o.stacking.stacks[this.stackKey][h]=new aJ(o,o.options.stackLabels,!1,h,this.stack)).total=0)}a=s}}return this.getGraphPath(i)}t.compose=function(t,l){if(!t.keepProps.includes("brokenAxis")){t.keepProps.push("brokenAxis"),uG(t,"init",n),uG(t,"afterInit",e),uG(t,"afterSetTickPositions",o),uG(t,"afterSetOptions",i);var d=l.prototype;d.drawBreaks=a,d.gappedPath=h,uG(l,"afterGeneratePoints",r),uG(l,"afterRender",s)}return t};var l=function(){function t(t){this.hasBreaks=!1,this.axis=t}return t.isInBreak=function(t,e){var i,o=t.repeat||1/0,n=t.from,r=t.to-t.from,s=e>=n?(e-n)%o:o-(n-e)%o;return t.inclusive?s<=r:s<r&&0!==s},t.lin2Val=function(e){var i=this.brokenAxis,o=null==i?void 0:i.breakArray;if(!o||!uY(e))return e;var n,r,s=e;for(r=0;r<o.length&&!((n=o[r]).from>=s);r++)n.to<s?s+=n.len:t.isInBreak(n,s)&&(s+=n.len);return s},t.val2Lin=function(e){var i=this.brokenAxis,o=null==i?void 0:i.breakArray;if(!o||!uY(e))return e;var n,r,s=e;for(r=0;r<o.length;r++)if((n=o[r]).to<=e)s-=n.len;else if(n.from>=e)break;else if(t.isInBreak(n,e)){s-=e-n.from;break}return s},t.prototype.findBreakAt=function(t,e){return uX(e,function(e){return e.from<t&&t<e.to})},t.prototype.isInAnyBreak=function(e,i){var o,n,r,s=this.axis,a=s.options.breaks||[],h=a.length;if(h&&uY(e)){for(;h--;)t.isInBreak(a[h],e)&&(o=!0,n||(n=u_(a[h].showPoints,!s.isXAxis)));r=o&&i?o&&!n:o}return r},t.prototype.setBreaks=function(e,i){var o=this,n=o.axis,r=n.chart.time,s=uF(e)&&!!e.length&&!!Object.keys(e[0]).length;n.isDirty=o.hasBreaks!==s,o.hasBreaks=s,null==e||e.forEach(function(t){t.from=r.parse(t.from)||0,t.to=r.parse(t.to)||0}),e!==n.options.breaks&&(n.options.breaks=n.userOptions.breaks=e),n.forceRedraw=!0,n.series.forEach(function(t){t.isDirty=!0}),s||n.val2lin!==t.val2Lin||(delete n.val2lin,delete n.lin2val),s&&(n.userOptions.ordinal=!1,n.lin2val=t.lin2Val,n.val2lin=t.val2Lin,n.setExtremes=function(t,e,i,r,s){if(o.hasBreaks){for(var a=this.options.breaks||[],h=void 0;h=o.findBreakAt(t,a);)t=h.to;for(;h=o.findBreakAt(e,a);)e=h.from;e<t&&(e=t)}n.constructor.prototype.setExtremes.call(this,t,e,i,r,s)},n.setAxisTranslation=function(){if(n.constructor.prototype.setAxisTranslation.call(this),o.unitLength=void 0,o.hasBreaks){var e,i,r,s,a=n.options.breaks||[],h=[],l=[],d=u_(n.pointRangePadding,0),c=0,p=n.userMin||n.min,u=n.userMax||n.max;a.forEach(function(e){i=e.repeat||1/0,uY(p)&&uY(u)&&(t.isInBreak(e,p)&&(p+=e.to%i-p%i),t.isInBreak(e,u)&&(u-=u%i-e.from%i))}),a.forEach(function(t){if(r=t.from,i=t.repeat||1/0,uY(p)&&uY(u)){for(;r-i>p;)r-=i;for(;r<p;)r+=i;for(s=r;s<u;s+=i)h.push({value:s,move:"in"}),h.push({value:s+t.to-t.from,move:"out",size:t.breakSize})}}),h.sort(function(t,e){return t.value===e.value?+("in"!==t.move)-+("in"!==e.move):t.value-e.value}),e=0,r=p,h.forEach(function(t){1===(e+="in"===t.move?1:-1)&&"in"===t.move&&(r=t.value),0===e&&uY(r)&&(l.push({from:r,to:t.value,len:t.value-r-(t.size||0)}),c+=t.value-r-(t.size||0))}),o.breakArray=l,uY(p)&&uY(u)&&uY(n.min)&&(o.unitLength=u-p-c+d,uH(n,"afterBreaks"),n.staticScale?n.transA=n.staticScale:o.unitLength&&(n.transA*=(u-n.min+d)/o.unitLength),d&&(n.minPixelPadding=n.transA*(n.minPointOffset||0)),n.min=p,n.max=u)}}),u_(i,!0)&&n.chart.redraw()},t}();t.Additions=l}(tc||(tc={}));var uj=tc;tv.BrokenAxis=tv.BrokenAxis||uj,tv.BrokenAxis.compose(tv.Axis,tv.Series);var uU={},uV=tF.arrayMax,uZ=tF.arrayMin,uq=tF.correctFloat,uK=tF.extend,u$=tF.isNumber;function uJ(t){var e=t.length,i=uQ(t);return u$(i)&&e&&(i=uq(i/e)),i}function uQ(t){var e,i=t.length;if(!i&&t.hasNulls)e=null;else if(i)for(e=0;i--;)e+=t[i];return e}var u0={average:uJ,averages:function(){var t=[];return[].forEach.call(arguments,function(e){t.push(uJ(e))}),void 0===t[0]?void 0:t},close:function(t){return t.length?t[t.length-1]:t.hasNulls?null:void 0},high:function(t){return t.length?uV(t):t.hasNulls?null:void 0},hlc:function(t,e,i){if(t=uU.high(t),e=uU.low(e),i=uU.close(i),u$(t)||u$(e)||u$(i))return[t,e,i]},low:function(t){return t.length?uZ(t):t.hasNulls?null:void 0},ohlc:function(t,e,i,o){if(t=uU.open(t),e=uU.high(e),i=uU.low(i),o=uU.close(o),u$(t)||u$(e)||u$(i)||u$(o))return[t,e,i,o]},open:function(t){return t.length?t[0]:t.hasNulls?null:void 0},range:function(t,e){return(t=uU.low(t),e=uU.high(e),u$(t)||u$(e))?[t,e]:null===t&&null===e?null:void 0},sum:uQ};uK(uU,u0);var u1={common:{groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%[AebHMSL]","%[AebHMSL]","-%[HMSL]"],second:["%[AebHMS]","%[AebHMS]","-%[HMS]"],minute:["%[AebHM]","%[AebHM]","-%[HM]"],hour:["%[AebHM]","%[AebHM]","-%[HM]"],day:["%[AebY]","%[Aeb]","-%[AebY]"],week:["%v %[AebY]","%[Aeb]","-%[AebY]"],month:["%[BY]","%[B]","-%[BY]"],year:["%Y","%Y","-%Y"]}},seriesSpecific:{line:{},spline:{},area:{},areaspline:{},arearange:{},column:{groupPixelWidth:10},columnrange:{groupPixelWidth:10},candlestick:{groupPixelWidth:10},ohlc:{groupPixelWidth:5},hlc:{groupPixelWidth:5},heikinashi:{groupPixelWidth:10}},units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]]},u2=tF.addEvent,u3=tF.extend,u5=tF.merge,u6=tF.pick;function u9(t){var e=this,i=e.series;i.forEach(function(t){t.groupPixelWidth=void 0}),i.forEach(function(i){i.groupPixelWidth=e.getGroupPixelWidth&&e.getGroupPixelWidth(),i.groupPixelWidth&&(i.hasProcessed=!0),i.applyGrouping(!!t.hasExtremesChanged)})}function u4(){for(var t,e,i=this.series,o=i.length,n=0,r=!1;o--;)(e=i[o].options.dataGrouping)&&(n=Math.max(n,u6(e.groupPixelWidth,u1.common.groupPixelWidth)),t=(i[o].dataTable.modified||i[o].dataTable).rowCount,(i[o].groupPixelWidth||t>this.chart.plotSizeX/n||t&&e.forced)&&(r=!0));return r?n:0}function u8(){this.series.forEach(function(t){t.hasProcessed=!1})}function u7(t,e){var i;if(e=u6(e,!0),t||(t={forced:!1,units:null}),this instanceof tp)for(i=this.series.length;i--;)this.series[i].update({dataGrouping:t},!1);else this.chart.options.series.forEach(function(e){e.dataGrouping="boolean"==typeof t?t:u5(t,e.dataGrouping)});this.ordinal&&(this.ordinal.slope=void 0),e&&this.chart.redraw()}var ft=function(t){tp=t;var e=t.prototype;e.applyGrouping||(u2(t,"afterSetScale",u8),u2(t,"postProcessData",u9),u3(e,{applyGrouping:u9,getGroupPixelWidth:u4,setDataGrouping:u7}))},fe=ss.series.prototype,fi=tF.addEvent,fo=tF.defined,fn=tF.error,fr=tF.extend,fs=tF.isNumber,fa=tF.merge,fh=tF.pick,fl=tF.splat,fd=fe.generatePoints;function fc(t){var e,i,o,n,r=this.chart,s=this.options.dataGrouping,a=!1!==this.allowDG&&s&&fh(s.enabled,r.options.isStock),h=this.reserveSpace(),l=this.currentDataGrouping,d=!1;a&&!this.requireSorting&&(this.requireSorting=d=!0);var c=!1==(x=this,b=t,!(x.isCartesian&&!x.isDirty&&!x.xAxis.isDirty&&!x.yAxis.isDirty&&!b))||!a;if(d&&(this.requireSorting=!1),!c){this.destroyGroupedData();var p=s.groupAll?this.dataTable:this.dataTable.modified||this.dataTable,u=this.getColumn("x",!s.groupAll),f=r.plotSizeX,g=this.xAxis,v=g.getExtremes(),m=g.options.ordinal,y=this.groupPixelWidth;if(y&&u&&p.rowCount&&f&&fs(v.min)){i=!0,this.isDirty=!0,this.points=null;var x,b,k,M=v.min,w=v.max,S=m&&g.ordinal&&g.ordinal.getGroupIntervalFactor(M,w,this)||1,A=y*(w-M)/f*S,T=g.getTimeTicks(nI.Additions.prototype.normalizeTimeTickInterval(A,s.units||u1.units),Math.min(M,u[0]),Math.max(w,u[u.length-1]),g.options.startOfWeek,u,this.closestPointRange),C=fe.groupData.apply(this,[p,T,s.approximation]),O=C.modified,P=O.getColumn("x",!0),E=0;for((null==s?void 0:s.smoothed)&&O.rowCount&&(s.firstAnchor="firstPoint",s.anchor="middle",s.lastAnchor="lastPoint",fn(32,!1,r,{"dataGrouping.smoothed":"use dataGrouping.anchor"})),e=1;e<T.length;e++)T.info.segmentStarts&&-1!==T.info.segmentStarts.indexOf(e)||(E=Math.max(T[e]-T[e-1],E));(o=T.info).gapSize=E,this.closestPointRange=T.info.totalRange,this.groupMap=C.groupMap,this.currentDataGrouping=o,!function(t,e,i){var o=t.options.dataGrouping,n=t.currentDataGrouping&&t.currentDataGrouping.gapSize,r=t.getColumn("x");if(o&&r.length&&n&&t.groupMap){var s=e.length-1,a=o.anchor,h=o.firstAnchor,l=o.lastAnchor,d=e.length-1,c=0;if(h&&r[0]>=e[0]){c++;var p=t.groupMap[0].start,u=t.groupMap[0].length,f=void 0;fs(p)&&fs(u)&&(f=p+(u-1)),e[0]=({start:e[0],middle:e[0]+.5*n,end:e[0]+n,firstPoint:r[0],lastPoint:f&&r[f]})[h]}if(s>0&&l&&n&&e[s]>=i-n){d--;var g=t.groupMap[t.groupMap.length-1].start;e[s]=({start:e[s],middle:e[s]+.5*n,end:e[s]+n,firstPoint:g&&r[g],lastPoint:r[r.length-1]})[l]}if(a&&"start"!==a)for(var v=n*({middle:.5,end:1})[a];d>=c;)e[d]+=v,d--}}(this,P||[],w),h&&P&&(fo((k=P)[0])&&fs(g.min)&&fs(g.dataMin)&&k[0]<g.min&&((!fo(g.options.min)&&g.min<=g.dataMin||g.min===g.dataMin)&&(g.min=Math.min(k[0],g.min)),g.dataMin=Math.min(k[0],g.dataMin)),fo(k[k.length-1])&&fs(g.max)&&fs(g.dataMax)&&k[k.length-1]>g.max&&((!fo(g.options.max)&&fs(g.dataMax)&&g.max>=g.dataMax||g.max===g.dataMax)&&(g.max=Math.max(k[k.length-1],g.max)),g.dataMax=Math.max(k[k.length-1],g.dataMax))),s.groupAll&&(this.allGroupedTable=O,P=(O=(n=this.cropData(O,g.min||0,g.max||0)).modified).getColumn("x"),this.cropStart=n.start),this.dataTable.modified=O}else this.groupMap=void 0,this.currentDataGrouping=void 0;this.hasGroupedData=i,this.preventGraphAnimation=(l&&l.totalRange)!==(o&&o.totalRange)}}function fp(){this.groupedData&&(this.groupedData.forEach(function(t,e){t&&(this.groupedData[e]=t.destroy?t.destroy():null)},this),this.groupedData.length=0,delete this.allGroupedTable)}function fu(){fd.apply(this),this.destroyGroupedData(),this.groupedData=this.hasGroupedData?this.points:null}function ff(){return this.is("arearange")?"range":this.is("ohlc")?"ohlc":this.is("hlc")?"hlc":this.is("column")||this.options.cumulative?"sum":"average"}function fg(t,e,i){var o,n,r,s=t.getColumn("x",!0)||[],a=t.getColumn("y",!0),h=this,l=h.data,d=h.options&&h.options.data,c=[],p=new r6,u=[],f=t.rowCount,g=!!a,v=[],m=h.pointArrayMap,y=m&&m.length,x=["x"].concat(m||["y"]),b=(m||["y"]).map(function(){return[]}),k=this.options.dataGrouping&&this.options.dataGrouping.groupAll,M=0,w=0,S="function"==typeof i?i:i&&uU[i]?uU[i]:uU[h.getDGApproximation&&h.getDGApproximation()||"average"];if(y)for(var A=m.length;A--;)v.push([]);else v.push([]);for(var T=y||1,C=0;C<=f;C++)if(!(s[C]<e[0])){for(;void 0!==e[M+1]&&s[C]>=e[M+1]||C===f;){if(o=e[M],h.dataGroupInfo={start:k?w:h.cropStart+w,length:v[0].length,groupStart:o},r=S.apply(h,v),h.pointClass&&!fo(h.dataGroupInfo.options)&&(h.dataGroupInfo.options=fa(h.pointClass.prototype.optionsToObject.call({series:h},h.options.data[h.cropStart+w])),x.forEach(function(t){delete h.dataGroupInfo.options[t]})),void 0!==r){c.push(o);for(var O=fl(r),P=0;P<O.length;P++)b[P].push(O[P]);u.push(h.dataGroupInfo)}w=C;for(var P=0;P<T;P++)v[P].length=0,v[P].hasNulls=!1;if(M+=1,C===f)break}if(C===f)break;if(m)for(var E=k?C:h.cropStart+C,L=l&&l[E]||h.pointClass.prototype.applyOptions.apply({series:h},[d[E]]),B=void 0,P=0;P<y;P++)fs(B=L[m[P]])?v[P].push(B):null===B&&(v[P].hasNulls=!0);else fs(n=g?a[C]:null)?v[0].push(n):null===n&&(v[0].hasNulls=!0)}var D={x:c};return(m||["y"]).forEach(function(t,e){D[t]=b[e]}),p.setColumns(D),{groupMap:u,modified:p}}function fv(t){var e=t.options,i=this.type,o=this.chart.options.plotOptions,n=this.useCommonDataGrouping&&u1.common,r=u1.seriesSpecific,s=en.plotOptions[i].dataGrouping;if(o&&(r[i]||n)){var a=this.chart.rangeSelector;s||(s=fa(u1.common,r[i])),e.dataGrouping=fa(n,s,o.series&&o.series.dataGrouping,o[i].dataGrouping,this.userOptions.dataGrouping,!e.isInternal&&a&&fs(a.selected)&&a.buttonOptions[a.selected].dataGrouping)}}var fm=function(t){var e=t.prototype;e.applyGrouping||(fi(t.prototype.pointClass,"update",function(){if(this.dataGroup)return fn(24,!1,this.series.chart),!1}),fi(t,"afterSetOptions",fv),fi(t,"destroy",fp),fr(e,{applyGrouping:fc,destroyGroupedData:fp,generatePoints:fu,getDGApproximation:ff,groupData:fg}))},fy=e8.format,fx=tv.composed,fb=tF.addEvent,fk=tF.extend,fM=tF.isNumber,fw=tF.pick,fS=tF.pushUnique;function fA(t){var e,i,o,n,r,s,a=this.chart,h=a.time,l=t.point,d=l.series,c=d.options,p=d.tooltipOptions,u=c.dataGrouping,f=d.xAxis,g=p.xDateFormat||"",v=p[t.isFooter?"footerFormat":"headerFormat"];if(f&&"datetime"===f.options.type&&u&&fM(l.key)){o=d.currentDataGrouping,n=u.dateTimeLabelFormats||u1.common.dateTimeLabelFormats,o?(r=n[o.unitName],1===o.count?g=r[0]:(g=r[1],i=r[2])):!g&&n&&f.dateTime&&(g=f.dateTime.getXDateFormat(l.x,p.dateTimeLabelFormats));var m=fw(null===(e=d.groupMap)||void 0===e?void 0:e[l.index].groupStart,l.key),y=m+((null==o?void 0:o.totalRange)||0)-1;s=h.dateFormat(g,m),i&&(s+=h.dateFormat(i,y)),d.chart.styledMode&&(v=this.styledModeFormat(v)),t.text=fy(v,{point:fk(l,{key:s}),series:d},a),t.preventDefault()}}tv.dataGrouping=tv.dataGrouping||{},tv.dataGrouping.approximationDefaults=tv.dataGrouping.approximationDefaults||u0,tv.dataGrouping.approximations=tv.dataGrouping.approximations||uU,({compose:function(t,e,i){ft(t),fm(e),i&&fS(fx,"DataGrouping")&&fb(i,"headerFormatter",fA)},groupData:fg}).compose(tv.Axis,tv.Series,tv.Tooltip),tF.defined;var fT=tF.isNumber,fC=tF.pick,fO=tF.addEvent,fP=tF.isObject,fE=tF.pick,fL=tF.defined,fB=tF.merge,fD=function(t){return t.filter(function(t){var e=t.axis.getExtremes(),i=e.min,o=e.max,n=fC(t.axis.minPointOffset,0);return fT(i)&&fT(o)&&t.value>=i-n&&t.value<=o+n&&!t.axis.options.isInternal})[0]},fI=[],fz={enabled:!0,sensitivity:1.1},fR=function(t,e,i,o,n,r,s){var a=fE(s.type,t.zooming.type,""),h=[];"x"===a?h=i:"y"===a?h=o:"xy"===a&&(h=t.axes);var l=t.transform({axes:h,to:{x:n-5,y:r-5,width:10,height:10},from:{x:n-5*e,y:r-5*e,width:10*e,height:10*e},trigger:"mousewheel"});return l&&(fL(tu)&&clearTimeout(tu),tu=setTimeout(function(){var e;null===(e=t.pointer)||void 0===e||e.drop()},400)),l};function fN(){var t,e=this,i=(fP(t=this.zooming.mouseWheel)||(t={enabled:null==t||t}),fB(fz,t));i.enabled&&fO(this.container,"wheel",function(t){t=(null===(o=e.pointer)||void 0===o?void 0:o.normalize(t))||t;var o,n,r=e.pointer,s=r&&!r.inClass(t.target,"highcharts-no-mousewheel");if(e.isInsidePlot(t.chartX-e.plotLeft,t.chartY-e.plotTop)&&s){var a=i.sensitivity||1.1,h=t.detail||(t.deltaY||0)/120,l=fD(r.getCoordinates(t).xAxis),d=fD(r.getCoordinates(t).yAxis);fR(e,Math.pow(a,h),l?[l.axis]:e.xAxis,d?[d.axis]:e.yAxis,t.chartX,t.chartY,i)&&(null===(n=t.preventDefault)||void 0===n||n.call(t))}})}tv.MouseWheelZoom=tv.MouseWheelZoom||{compose:function(t){-1===fI.indexOf(t)&&(fI.push(t),fO(t,"afterGetContainer",fN))}},tv.MouseWheelZoom.compose(tv.Chart),tv.Navigator=tv.Navigator||cY,tv.OrdinalAxis=tv.OrdinalAxis||c0,tv.RangeSelector=tv.RangeSelector||pC,tv.Scrollbar=tv.Scrollbar||cw,tv.stockChart=tv.stockChart||pj.stockChart,tv.StockChart=tv.StockChart||tv.stockChart,tv.extend(tv.StockChart,pj),dB.compose(tv.Series,tv.Axis,tv.Point),uW.compose(tv.Renderer),un.compose(tv.Series),tv.Navigator.compose(tv.Chart,tv.Axis,tv.Series),tv.OrdinalAxis.compose(tv.Axis,tv.Series,tv.Chart),tv.RangeSelector.compose(tv.Axis,tv.Chart),tv.Scrollbar.compose(tv.Axis),tv.StockChart.compose(tv.Chart,tv.Axis,tv.Series,tv.SVGRenderer),tv.product="Highstock";var fW=tv;return tg.default}()});