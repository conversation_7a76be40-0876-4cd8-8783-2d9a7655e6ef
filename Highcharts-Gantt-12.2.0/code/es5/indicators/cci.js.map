{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/cci\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Sebastian <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/cci\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/cci\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ cci_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Stock/Indicators/CCI/CCIIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar SMAIndicator = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.sma;\n\nvar isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction sumArray(array) {\n    return array.reduce(function (prev, cur) {\n        return prev + cur;\n    }, 0);\n}\n/**\n * @private\n */\nfunction meanDeviation(arr, sma) {\n    var len = arr.length;\n    var sum = 0,\n        i;\n    for (i = 0; i < len; i++) {\n        sum += Math.abs(sma - (arr[i]));\n    }\n    return sum;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CCI series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cci\n *\n * @augments Highcharts.Series\n */\nvar CCIIndicator = /** @class */ (function (_super) {\n    __extends(CCIIndicator, _super);\n    function CCIIndicator() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    CCIIndicator.prototype.getValues = function (series, params) {\n        var period = params.period,\n            xVal = series.xData,\n            yVal = series.yData,\n            yValLen = yVal ? yVal.length : 0,\n            TP = [],\n            CCI = [],\n            xData = [],\n            yData = [];\n        var CCIPoint,\n            p,\n            periodTP = [],\n            len,\n            range = 1,\n            smaTP,\n            TPtemp,\n            meanDev,\n            i;\n        // CCI requires close value\n        if (xVal.length <= period ||\n            !isArray(yVal[0]) ||\n            yVal[0].length !== 4) {\n            return;\n        }\n        // Accumulate first N-points\n        while (range < period) {\n            p = yVal[range - 1];\n            TP.push((p[1] + p[2] + p[3]) / 3);\n            range++;\n        }\n        for (i = period; i <= yValLen; i++) {\n            p = yVal[i - 1];\n            TPtemp = (p[1] + p[2] + p[3]) / 3;\n            len = TP.push(TPtemp);\n            periodTP = TP.slice(len - period);\n            smaTP = sumArray(periodTP) / period;\n            meanDev = meanDeviation(periodTP, smaTP) / period;\n            CCIPoint = ((TPtemp - smaTP) / (0.015 * meanDev));\n            CCI.push([xVal[i - 1], CCIPoint]);\n            xData.push(xVal[i - 1]);\n            yData.push(CCIPoint);\n        }\n        return {\n            values: CCI,\n            xData: xData,\n            yData: yData\n        };\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Commodity Channel Index (CCI). This series requires `linkedTo` option to\n     * be set.\n     *\n     * @sample stock/indicators/cci\n     *         CCI indicator\n     *\n     * @extends      plotOptions.sma\n     * @since        6.0.0\n     * @product      highstock\n     * @requires     stock/indicators/indicators\n     * @requires     stock/indicators/cci\n     * @optionparent plotOptions.cci\n     */\n    CCIIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n        /**\n         * @excluding index\n         */\n        params: {\n            index: void 0 // Unused index, do not inherit (#15362)\n        }\n    });\n    return CCIIndicator;\n}(SMAIndicator));\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cci', CCIIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var CCI_CCIIndicator = ((/* unused pure expression or super */ null && (CCIIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CCI` series. If the [type](#series.cci.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cci\n * @since     6.0.0\n * @excluding dataParser, dataURL\n * @product   highstock\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cci\n * @apioption series.cci\n */\n''; // To include the above in the js output\n\n;// ./code/es5/es-modules/masters/indicators/cci.js\n\n\n\n\n/* harmony default export */ var cci_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cci_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "SMAIndicator", "seriesTypes", "sma", "isArray", "merge", "CCIIndicator", "_super", "apply", "arguments", "getV<PERSON>ues", "series", "params", "CCIPoint", "len", "smaTP", "TPtemp", "meanDev", "i", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "TP", "CCI", "periodTP", "range", "push", "sumArray", "array", "slice", "reduce", "prev", "cur", "meanDeviation", "arr", "sum", "Math", "abs", "values", "defaultOptions", "index", "registerSeriesType"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACtG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE7GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IA+FFC,EA/FMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAS,CACxD,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAS7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGAG,EAAe,AAACV,IAA2IW,WAAW,CAACC,GAAG,CAE1KC,EAAU,AAACf,IAA+Ee,OAAO,CAAEC,EAAQ,AAAChB,IAA+EgB,KAAK,CAyChMC,EAA8B,SAAUC,CAAM,EAE9C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAiFA,OApFAjB,EAAUc,EAAcC,GASxBD,EAAavB,SAAS,CAAC2B,SAAS,CAAG,SAAUC,CAAM,CAAEC,CAAM,EACvD,IAQIC,EACAhB,EAEAiB,EAEAC,EACAC,EACAC,EACAC,EAhBAC,EAASP,EAAOO,MAAM,CACtBC,EAAOT,EAAOU,KAAK,CACnBC,EAAOX,EAAOY,KAAK,CACnBC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAC/BC,EAAK,EAAE,CACPC,EAAM,EAAE,CACRN,EAAQ,EAAE,CACVE,EAAQ,EAAE,CAGVK,EAAW,EAAE,CAEbC,EAAQ,EAMZ,GAAIT,CAAAA,CAAAA,EAAKK,MAAM,EAAIN,CAAK,GACnBf,EAAQkB,CAAI,CAAC,EAAE,GAChBA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACG,MAAM,EAIlB,KAAOI,EAAQV,GACXtB,EAAIyB,CAAI,CAACO,EAAQ,EAAE,CACnBH,EAAGI,IAAI,CAAC,AAACjC,CAAAA,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,AAAD,EAAK,GAC/BgC,IAEJ,IAAKX,EAAIC,EAAQD,GAAKM,EAASN,IAE3BF,EAAS,AAACnB,CAAAA,AADVA,CAAAA,EAAIyB,CAAI,CAACJ,EAAI,EAAE,AAAD,CACH,CAAC,EAAE,CAAGrB,CAAC,CAAC,EAAE,CAAGA,CAAC,CAAC,EAAE,AAAD,EAAK,EAChCiB,EAAMY,EAAGI,IAAI,CAACd,GAEdD,EAAQgB,AA3ETC,AA0ECJ,CAAAA,EAAWF,EAAGO,KAAK,CAACnB,EAAMK,EAAM,EA1E3Be,MAAM,CAAC,SAAUC,CAAI,CAAEC,CAAG,EACnC,OAAOD,EAAOC,CAClB,EAAG,GAyEkCjB,EAC7BF,EAAUoB,AArEtB,SAAuBC,CAAG,CAAEnC,CAAG,EAC3B,IAEIe,EAFAJ,EAAMwB,EAAIb,MAAM,CAChBc,EAAM,EAEV,IAAKrB,EAAI,EAAGA,EAAIJ,EAAKI,IACjBqB,GAAOC,KAAKC,GAAG,CAACtC,EAAOmC,CAAG,CAACpB,EAAE,EAEjC,OAAOqB,CACX,EA6DoCX,EAAUb,GAASI,EAC3CN,EAAY,AAACG,CAAAA,EAASD,CAAI,EAAM,CAAA,KAAQE,CAAM,EAC9CU,EAAIG,IAAI,CAAC,CAACV,CAAI,CAACF,EAAI,EAAE,CAAEL,EAAS,EAChCQ,EAAMS,IAAI,CAACV,CAAI,CAACF,EAAI,EAAE,EACtBK,EAAMO,IAAI,CAACjB,GAEf,MAAO,CACH6B,OAAQf,EACRN,MAAOA,EACPE,MAAOA,CACX,EACJ,EAoBAjB,EAAaqC,cAAc,CAAGtC,EAAMJ,EAAa0C,cAAc,CAAE,CAI7D/B,OAAQ,CACJgC,MAAO,KAAK,CAChB,CACJ,GACOtC,CACX,EAAEL,GACFV,IAA0IsD,kBAAkB,CAAC,MAAOvC,GA+BvI,IAAInB,EAAYE,IAGnC,OADYH,EAAoB,OAAU,AAE3C,GAET"}