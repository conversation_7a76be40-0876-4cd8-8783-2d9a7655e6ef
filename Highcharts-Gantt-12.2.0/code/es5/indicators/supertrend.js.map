{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/supertrend\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/supertrend\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/supertrend\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ supertrend_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Stock/Indicators/Supertrend/SupertrendIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar _a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes, ATRIndicator = _a.atr, SMAIndicator = _a.sma;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, correctFloat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).correctFloat, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach;\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n * @private\n */\nfunction createPointObj(mainSeries, index) {\n    return {\n        index: index,\n        close: mainSeries.getColumn('close')[index],\n        x: mainSeries.getColumn('x')[index]\n    };\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Supertrend series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.supertrend\n *\n * @augments Highcharts.Series\n */\nvar SupertrendIndicator = /** @class */ (function (_super) {\n    __extends(SupertrendIndicator, _super);\n    function SupertrendIndicator() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    SupertrendIndicator.prototype.init = function () {\n        var indicator = this;\n        _super.prototype.init.apply(indicator, arguments);\n        // Only after series are linked add some additional logic/properties.\n        var unbinder = addEvent(this.chart.constructor, 'afterLinkSeries',\n            function () {\n                // Protection for a case where the indicator is being updated,\n                // for a brief moment the indicator is deleted.\n                if (indicator.options) {\n                    var options = indicator.options,\n            parentOptions = indicator.linkedParent.options;\n                // Indicator cropThreshold has to be equal linked series one\n                // reduced by period due to points comparison in drawGraph\n                // (#9787)\n                options.cropThreshold = (parentOptions.cropThreshold -\n                    (options.params.period - 1));\n            }\n            unbinder();\n        }, {\n            order: 1\n        });\n    };\n    SupertrendIndicator.prototype.drawGraph = function () {\n        var indicator = this,\n            indicOptions = indicator.options, \n            // Series that indicator is linked to\n            mainSeries = indicator.linkedParent,\n            mainXData = mainSeries.getColumn('x'),\n            mainLinePoints = (mainSeries ? mainSeries.points : []),\n            indicPoints = indicator.points,\n            indicPath = indicator.graph, \n            // Points offset between lines\n            tempOffset = mainLinePoints.length - indicPoints.length,\n            offset = tempOffset > 0 ? tempOffset : 0, \n            // @todo: fix when ichi-moku indicator is merged to master.\n            gappedExtend = {\n                options: {\n                    gapSize: indicOptions.gapSize\n                }\n            }, \n            // Sorted supertrend points array\n            groupedPoints = {\n                top: [], // Rising trend line points\n                bottom: [], // Falling trend line points\n                intersect: [] // Change trend line points\n            }, \n            // Options for trend lines\n            supertrendLineOptions = {\n                top: {\n                    styles: {\n                        lineWidth: indicOptions.lineWidth,\n                        lineColor: (indicOptions.fallingTrendColor ||\n                            indicOptions.color),\n                        dashStyle: indicOptions.dashStyle\n                    }\n                },\n                bottom: {\n                    styles: {\n                        lineWidth: indicOptions.lineWidth,\n                        lineColor: (indicOptions.risingTrendColor ||\n                            indicOptions.color),\n                        dashStyle: indicOptions.dashStyle\n                    }\n                },\n                intersect: indicOptions.changeTrendLine\n            };\n        var // Supertrend line point\n            point, \n            // Supertrend line next point (has smaller x pos than point)\n            nextPoint, \n            // Main series points\n            mainPoint,\n            nextMainPoint, \n            // Used when supertrend and main points are shifted\n            // relative to each other\n            prevMainPoint,\n            prevPrevMainPoint, \n            // Used when particular point color is set\n            pointColor, \n            // Temporary points that fill groupedPoints array\n            newPoint,\n            newNextPoint,\n            indicPointsLen = indicPoints.length;\n        // Loop which sort supertrend points\n        while (indicPointsLen--) {\n            point = indicPoints[indicPointsLen];\n            nextPoint = indicPoints[indicPointsLen - 1];\n            mainPoint = mainLinePoints[indicPointsLen - 1 + offset];\n            nextMainPoint = mainLinePoints[indicPointsLen - 2 + offset];\n            prevMainPoint = mainLinePoints[indicPointsLen + offset];\n            prevPrevMainPoint = mainLinePoints[indicPointsLen + offset + 1];\n            pointColor = point.options.color;\n            newPoint = {\n                x: point.x,\n                plotX: point.plotX,\n                plotY: point.plotY,\n                isNull: false\n            };\n            // When mainPoint is the last one (left plot area edge)\n            // but supertrend has additional one\n            if (!nextMainPoint &&\n                mainPoint &&\n                isNumber(mainXData[mainPoint.index - 1])) {\n                nextMainPoint = createPointObj(mainSeries, mainPoint.index - 1);\n            }\n            // When prevMainPoint is the last one (right plot area edge)\n            // but supertrend has additional one (and points are shifted)\n            if (!prevPrevMainPoint &&\n                prevMainPoint &&\n                isNumber(mainXData[prevMainPoint.index + 1])) {\n                prevPrevMainPoint = createPointObj(mainSeries, prevMainPoint.index + 1);\n            }\n            // When points are shifted (right or left plot area edge)\n            if (!mainPoint &&\n                nextMainPoint &&\n                isNumber(mainXData[nextMainPoint.index + 1])) {\n                mainPoint = createPointObj(mainSeries, nextMainPoint.index + 1);\n            }\n            else if (!mainPoint &&\n                prevMainPoint &&\n                isNumber(mainXData[prevMainPoint.index - 1])) {\n                mainPoint = createPointObj(mainSeries, prevMainPoint.index - 1);\n            }\n            // Check if points are shifted relative to each other\n            if (point &&\n                mainPoint &&\n                prevMainPoint &&\n                nextMainPoint &&\n                point.x !== mainPoint.x) {\n                if (point.x === prevMainPoint.x) {\n                    nextMainPoint = mainPoint;\n                    mainPoint = prevMainPoint;\n                }\n                else if (point.x === nextMainPoint.x) {\n                    mainPoint = nextMainPoint;\n                    nextMainPoint = {\n                        close: mainSeries.getColumn('close')[mainPoint.index - 1],\n                        x: mainXData[mainPoint.index - 1]\n                    };\n                }\n                else if (prevPrevMainPoint && point.x === prevPrevMainPoint.x) {\n                    mainPoint = prevPrevMainPoint;\n                    nextMainPoint = prevMainPoint;\n                }\n            }\n            if (nextPoint && nextMainPoint && mainPoint) {\n                newNextPoint = {\n                    x: nextPoint.x,\n                    plotX: nextPoint.plotX,\n                    plotY: nextPoint.plotY,\n                    isNull: false\n                };\n                if (point.y >= mainPoint.close &&\n                    nextPoint.y >= nextMainPoint.close) {\n                    point.color = (pointColor || indicOptions.fallingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.top.push(newPoint);\n                }\n                else if (point.y < mainPoint.close &&\n                    nextPoint.y < nextMainPoint.close) {\n                    point.color = (pointColor || indicOptions.risingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.bottom.push(newPoint);\n                }\n                else {\n                    groupedPoints.intersect.push(newPoint);\n                    groupedPoints.intersect.push(newNextPoint);\n                    // Additional null point to make a gap in line\n                    groupedPoints.intersect.push(merge(newNextPoint, {\n                        isNull: true\n                    }));\n                    if (point.y >= mainPoint.close &&\n                        nextPoint.y < nextMainPoint.close) {\n                        point.color = (pointColor || indicOptions.fallingTrendColor ||\n                            indicOptions.color);\n                        nextPoint.color = (pointColor || indicOptions.risingTrendColor ||\n                            indicOptions.color);\n                        groupedPoints.top.push(newPoint);\n                        groupedPoints.top.push(merge(newNextPoint, {\n                            isNull: true\n                        }));\n                    }\n                    else if (point.y < mainPoint.close &&\n                        nextPoint.y >= nextMainPoint.close) {\n                        point.color = (pointColor || indicOptions.risingTrendColor ||\n                            indicOptions.color);\n                        nextPoint.color = (pointColor || indicOptions.fallingTrendColor ||\n                            indicOptions.color);\n                        groupedPoints.bottom.push(newPoint);\n                        groupedPoints.bottom.push(merge(newNextPoint, {\n                            isNull: true\n                        }));\n                    }\n                }\n            }\n            else if (mainPoint) {\n                if (point.y >= mainPoint.close) {\n                    point.color = (pointColor || indicOptions.fallingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.top.push(newPoint);\n                }\n                else {\n                    point.color = (pointColor || indicOptions.risingTrendColor ||\n                        indicOptions.color);\n                    groupedPoints.bottom.push(newPoint);\n                }\n            }\n        }\n        // Generate lines:\n        objectEach(groupedPoints, function (values, lineName) {\n            indicator.points = values;\n            indicator.options = merge(supertrendLineOptions[lineName].styles, gappedExtend);\n            indicator.graph = indicator['graph' + lineName + 'Line'];\n            SMAIndicator.prototype.drawGraph.call(indicator);\n            // Now save line\n            indicator['graph' + lineName + 'Line'] = indicator.graph;\n        });\n        // Restore options:\n        indicator.points = indicPoints;\n        indicator.options = indicOptions;\n        indicator.graph = indicPath;\n    };\n    // Supertrend (Multiplier, Period) Formula:\n    // BASIC UPPERBAND = (HIGH + LOW) / 2 + Multiplier * ATR(Period)\n    // BASIC LOWERBAND = (HIGH + LOW) / 2 - Multiplier * ATR(Period)\n    // FINAL UPPERBAND =\n    //     IF(\n    //      Current BASICUPPERBAND  < Previous FINAL UPPERBAND AND\n    //      Previous Close > Previous FINAL UPPERBAND\n    //     ) THEN (Current BASIC UPPERBAND)\n    //     ELSE (Previous FINALUPPERBAND)\n    // FINAL LOWERBAND =\n    //     IF(\n    //      Current BASIC LOWERBAND  > Previous FINAL LOWERBAND AND\n    //      Previous Close < Previous FINAL LOWERBAND\n    //     ) THEN (Current BASIC LOWERBAND)\n    //     ELSE (Previous FINAL LOWERBAND)\n    // SUPERTREND =\n    //     IF(\n    //      Previous Supertrend == Previous FINAL UPPERBAND AND\n    //      Current Close < Current FINAL UPPERBAND\n    //     ) THAN Current FINAL UPPERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL LOWERBAND AND\n    //      Current Close < Current FINAL LOWERBAND\n    //     ) THAN Current FINAL UPPERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL UPPERBAND AND\n    //      Current Close > Current FINAL UPPERBAND\n    //     ) THAN Current FINAL LOWERBAND\n    //     ELSE IF(\n    //      Previous Supertrend == Previous FINAL LOWERBAND AND\n    //      Current Close > Current FINAL LOWERBAND\n    //     ) THAN Current FINAL LOWERBAND\n    SupertrendIndicator.prototype.getValues = function (series, params) {\n        var period = params.period,\n            multiplier = params.multiplier,\n            xVal = series.xData,\n            yVal = series.yData, \n            // 0- date, 1- Supertrend indicator\n            st = [],\n            xData = [],\n            yData = [],\n            close = 3,\n            low = 2,\n            high = 1,\n            periodsOffset = (period === 0) ? 0 : period - 1,\n            finalUp = [],\n            finalDown = [];\n        var atrData = [],\n            basicUp,\n            basicDown,\n            supertrend,\n            prevFinalUp,\n            prevFinalDown,\n            prevST, // Previous Supertrend\n            prevY,\n            y,\n            i;\n        if ((xVal.length <= period) || !isArray(yVal[0]) ||\n            yVal[0].length !== 4 || period < 0) {\n            return;\n        }\n        atrData = ATRIndicator.prototype.getValues.call(this, series, {\n            period: period\n        }).yData;\n        for (i = 0; i < atrData.length; i++) {\n            y = yVal[periodsOffset + i];\n            prevY = yVal[periodsOffset + i - 1] || [];\n            prevFinalUp = finalUp[i - 1];\n            prevFinalDown = finalDown[i - 1];\n            prevST = yData[i - 1];\n            if (i === 0) {\n                prevFinalUp = prevFinalDown = prevST = 0;\n            }\n            basicUp = correctFloat((y[high] + y[low]) / 2 + multiplier * atrData[i]);\n            basicDown = correctFloat((y[high] + y[low]) / 2 - multiplier * atrData[i]);\n            if ((basicUp < prevFinalUp) ||\n                (prevY[close] > prevFinalUp)) {\n                finalUp[i] = basicUp;\n            }\n            else {\n                finalUp[i] = prevFinalUp;\n            }\n            if ((basicDown > prevFinalDown) ||\n                (prevY[close] < prevFinalDown)) {\n                finalDown[i] = basicDown;\n            }\n            else {\n                finalDown[i] = prevFinalDown;\n            }\n            if (prevST === prevFinalUp && y[close] < finalUp[i] ||\n                prevST === prevFinalDown && y[close] < finalDown[i]) {\n                supertrend = finalUp[i];\n            }\n            else if (prevST === prevFinalUp && y[close] > finalUp[i] ||\n                prevST === prevFinalDown && y[close] > finalDown[i]) {\n                supertrend = finalDown[i];\n            }\n            st.push([xVal[periodsOffset + i], supertrend]);\n            xData.push(xVal[periodsOffset + i]);\n            yData.push(supertrend);\n        }\n        return {\n            values: st,\n            xData: xData,\n            yData: yData\n        };\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Supertrend indicator. This series requires the `linkedTo` option to be\n     * set and should be loaded after the `stock/indicators/indicators.js` and\n     * `stock/indicators/sma.js`.\n     *\n     * @sample {highstock} stock/indicators/supertrend\n     *         Supertrend indicator\n     *\n     * @extends      plotOptions.sma\n     * @since        7.0.0\n     * @product      highstock\n     * @excluding    allAreas, cropThreshold, negativeColor, colorAxis, joinBy,\n     *               keys, navigatorOptions, pointInterval, pointIntervalUnit,\n     *               pointPlacement, pointRange, pointStart, showInNavigator,\n     *               stacking, threshold\n     * @requires     stock/indicators/indicators\n     * @requires     stock/indicators/supertrend\n     * @optionparent plotOptions.supertrend\n     */\n    SupertrendIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n        /**\n         * Parameters used in calculation of Supertrend indicator series points.\n         *\n         * @excluding index\n         */\n        params: {\n            index: void 0, // Unchangeable index, do not inherit (#15362)\n            /**\n             * Multiplier for Supertrend Indicator.\n             */\n            multiplier: 3,\n            /**\n             * The base period for indicator Supertrend Indicator calculations.\n             * This is the number of data points which are taken into account\n             * for the indicator calculations.\n             */\n            period: 10\n        },\n        /**\n         * Color of the Supertrend series line that is beneath the main series.\n         *\n         * @sample {highstock} stock/indicators/supertrend/\n         *         Example with risingTrendColor\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        risingTrendColor: \"#06b535\" /* Palette.positiveColor */,\n        /**\n         * Color of the Supertrend series line that is above the main series.\n         *\n         * @sample {highstock} stock/indicators/supertrend/\n         *         Example with fallingTrendColor\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        fallingTrendColor: \"#f21313\" /* Palette.negativeColor */,\n        /**\n         * The styles for the Supertrend line that intersect main series.\n         *\n         * @sample {highstock} stock/indicators/supertrend/\n         *         Example with changeTrendLine\n         */\n        changeTrendLine: {\n            styles: {\n                /**\n                 * Pixel width of the line.\n                 */\n                lineWidth: 1,\n                /**\n                 * Color of the line.\n                 *\n                 * @type {Highcharts.ColorString}\n                 */\n                lineColor: \"#333333\" /* Palette.neutralColor80 */,\n                /**\n                 * The dash or dot style of the grid lines. For possible\n                 * values, see\n                 * [this demonstration](https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/plotoptions/series-dashstyle-all/).\n                 *\n                 * @sample {highcharts} highcharts/yaxis/gridlinedashstyle/\n                 *         Long dashes\n                 * @sample {highstock} stock/xaxis/gridlinedashstyle/\n                 *         Long dashes\n                 *\n                 * @type  {Highcharts.DashStyleValue}\n                 * @since 7.0.0\n                 */\n                dashStyle: 'LongDash'\n            }\n        }\n    });\n    return SupertrendIndicator;\n}(SMAIndicator));\nextend(SupertrendIndicator.prototype, {\n    nameBase: 'Supertrend',\n    nameComponents: ['multiplier', 'period']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('supertrend', SupertrendIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Supertrend_SupertrendIndicator = ((/* unused pure expression or super */ null && (SupertrendIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `Supertrend indicator` series. If the [type](#series.supertrend.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.supertrend\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, cropThreshold, data, dataParser, dataURL,\n *            joinBy, keys, navigatorOptions, negativeColor, pointInterval,\n *            pointIntervalUnit, pointPlacement, pointRange, pointStart,\n *            showInNavigator, stacking, threshold\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/supertrend\n * @apioption series.supertrend\n */\n''; // To include the above in the js output\n\n;// ./code/es5/es-modules/masters/indicators/supertrend.js\n\n\n\n\n/* harmony default export */ var supertrend_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "supertrend_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "_a", "seriesTypes", "ATRIndicator", "atr", "SMAIndicator", "sma", "addEvent", "correctFloat", "isArray", "isNumber", "extend", "merge", "objectEach", "createPointObj", "mainSeries", "index", "close", "getColumn", "x", "SupertrendIndicator", "_super", "apply", "arguments", "init", "indicator", "unbinder", "chart", "options", "cropThreshold", "parentOptions", "linkedParent", "params", "period", "order", "drawGraph", "point", "nextPoint", "mainPoint", "nextMainPoint", "prevMainPoint", "prevPrevMainPoint", "pointColor", "newPoint", "newNextPoint", "indicOptions", "mainXData", "mainLinePoints", "points", "indicPoints", "indicPath", "graph", "tempOffset", "length", "offset", "gappedExtend", "gapSize", "groupedPoints", "top", "bottom", "intersect", "supertrendLineOptions", "styles", "lineWidth", "lineColor", "fallingTrendColor", "color", "dashStyle", "risingTrendColor", "changeTrendLine", "indicPointsLen", "plotX", "plotY", "isNull", "y", "push", "values", "lineName", "getV<PERSON>ues", "series", "basicUp", "basicDown", "supertrend", "prevFinalUp", "prevFinalDown", "prevST", "prevY", "i", "multiplier", "xVal", "xData", "yVal", "yData", "st", "periodsOffset", "finalUp", "finalDown", "atrData", "defaultOptions", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,mCAAoC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GAC7G,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,mCAAmC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAEpHJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAgGFC,EAhGMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAgB,CAC/D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAU7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGAG,EAAK,AAACV,IAA2IW,WAAW,CAAEC,EAAeF,EAAGG,GAAG,CAAEC,EAAeJ,EAAGK,GAAG,CAE1MC,EAAW,AAAClB,IAA+EkB,QAAQ,CAAEC,EAAe,AAACnB,IAA+EmB,YAAY,CAAEC,EAAU,AAACpB,IAA+EoB,OAAO,CAAEC,EAAW,AAACrB,IAA+EqB,QAAQ,CAAEC,EAAS,AAACtB,IAA+EsB,MAAM,CAAEC,EAAQ,AAACvB,IAA+EuB,KAAK,CAAEC,EAAa,AAACxB,IAA+EwB,UAAU,CAUrsB,SAASC,EAAeC,CAAU,CAAEC,CAAK,EACrC,MAAO,CACHA,MAAOA,EACPC,MAAOF,EAAWG,SAAS,CAAC,QAAQ,CAACF,EAAM,CAC3CG,EAAGJ,EAAWG,SAAS,CAAC,IAAI,CAACF,EAAM,AACvC,CACJ,CAeA,IAAII,EAAqC,SAAUC,CAAM,EAErD,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CA+aA,OAlbA/B,EAAU4B,EAAqBC,GAS/BD,EAAoBrC,SAAS,CAACyC,IAAI,CAAG,WACjC,IAAIC,EAAY,IAAI,CACpBJ,EAAOtC,SAAS,CAACyC,IAAI,CAACF,KAAK,CAACG,EAAWF,WAEvC,IAAIG,EAAWnB,EAAS,IAAI,CAACoB,KAAK,CAAC5B,WAAW,CAAE,kBAC5C,WAGI,GAAI0B,EAAUG,OAAO,CAAE,CACnB,IAAIA,EAAUH,EAAUG,OAAO,AAKnCA,CAAAA,EAAQC,aAAa,CAAIC,AAJbL,EAAUM,YAAY,CAACH,OAAO,CAIHC,aAAa,CAC/CD,CAAAA,EAAQI,MAAM,CAACC,MAAM,CAAG,CAAA,CACjC,CACAP,GACJ,EAAG,CACCQ,MAAO,CACX,EACJ,EACAd,EAAoBrC,SAAS,CAACoD,SAAS,CAAG,WA8DtC,IA7DA,IA4CIC,EAEAC,EAEAC,EACAC,EAGAC,EACAC,EAEAC,EAEAC,EACAC,EA1DAnB,EAAY,IAAI,CAChBoB,EAAepB,EAAUG,OAAO,CAEhCb,EAAaU,EAAUM,YAAY,CACnCe,EAAY/B,EAAWG,SAAS,CAAC,KACjC6B,EAAkBhC,EAAaA,EAAWiC,MAAM,CAAG,EAAE,CACrDC,EAAcxB,EAAUuB,MAAM,CAC9BE,EAAYzB,EAAU0B,KAAK,CAE3BC,EAAaL,EAAeM,MAAM,CAAGJ,EAAYI,MAAM,CACvDC,EAASF,EAAa,EAAIA,EAAa,EAEvCG,EAAe,CACX3B,QAAS,CACL4B,QAASX,EAAaW,OAAO,AACjC,CACJ,EAEAC,EAAgB,CACZC,IAAK,EAAE,CACPC,OAAQ,EAAE,CACVC,UAAW,EAAE,AACjB,EAEAC,EAAwB,CACpBH,IAAK,CACDI,OAAQ,CACJC,UAAWlB,EAAakB,SAAS,CACjCC,UAAYnB,EAAaoB,iBAAiB,EACtCpB,EAAaqB,KAAK,CACtBC,UAAWtB,EAAasB,SAAS,AACrC,CACJ,EACAR,OAAQ,CACJG,OAAQ,CACJC,UAAWlB,EAAakB,SAAS,CACjCC,UAAYnB,EAAauB,gBAAgB,EACrCvB,EAAaqB,KAAK,CACtBC,UAAWtB,EAAasB,SAAS,AACrC,CACJ,EACAP,UAAWf,EAAawB,eAAe,AAC3C,EAiBAC,EAAiBrB,EAAYI,MAAM,CAEhCiB,KACHlC,EAAQa,CAAW,CAACqB,EAAe,CACnCjC,EAAYY,CAAW,CAACqB,EAAiB,EAAE,CAC3ChC,EAAYS,CAAc,CAACuB,EAAiB,EAAIhB,EAAO,CACvDf,EAAgBQ,CAAc,CAACuB,EAAiB,EAAIhB,EAAO,CAC3Dd,EAAgBO,CAAc,CAACuB,EAAiBhB,EAAO,CACvDb,EAAoBM,CAAc,CAACuB,EAAiBhB,EAAS,EAAE,CAC/DZ,EAAaN,EAAMR,OAAO,CAACsC,KAAK,CAChCvB,EAAW,CACPxB,EAAGiB,EAAMjB,CAAC,CACVoD,MAAOnC,EAAMmC,KAAK,CAClBC,MAAOpC,EAAMoC,KAAK,CAClBC,OAAQ,CAAA,CACZ,EAGI,CAAClC,GACDD,GACA5B,EAASoC,CAAS,CAACR,EAAUtB,KAAK,CAAG,EAAE,GACvCuB,CAAAA,EAAgBzB,EAAeC,EAAYuB,EAAUtB,KAAK,CAAG,EAAC,EAI9D,CAACyB,GACDD,GACA9B,EAASoC,CAAS,CAACN,EAAcxB,KAAK,CAAG,EAAE,GAC3CyB,CAAAA,EAAoB3B,EAAeC,EAAYyB,EAAcxB,KAAK,CAAG,EAAC,EAGtE,CAACsB,GACDC,GACA7B,EAASoC,CAAS,CAACP,EAAcvB,KAAK,CAAG,EAAE,EAC3CsB,EAAYxB,EAAeC,EAAYwB,EAAcvB,KAAK,CAAG,GAExD,CAACsB,GACNE,GACA9B,EAASoC,CAAS,CAACN,EAAcxB,KAAK,CAAG,EAAE,GAC3CsB,CAAAA,EAAYxB,EAAeC,EAAYyB,EAAcxB,KAAK,CAAG,EAAC,EAG9DoB,GACAE,GACAE,GACAD,GACAH,EAAMjB,CAAC,GAAKmB,EAAUnB,CAAC,GACnBiB,EAAMjB,CAAC,GAAKqB,EAAcrB,CAAC,EAC3BoB,EAAgBD,EAChBA,EAAYE,GAEPJ,EAAMjB,CAAC,GAAKoB,EAAcpB,CAAC,EAChCmB,EAAYC,EACZA,EAAgB,CACZtB,MAAOF,EAAWG,SAAS,CAAC,QAAQ,CAACoB,EAAUtB,KAAK,CAAG,EAAE,CACzDG,EAAG2B,CAAS,CAACR,EAAUtB,KAAK,CAAG,EAAE,AACrC,GAEKyB,GAAqBL,EAAMjB,CAAC,GAAKsB,EAAkBtB,CAAC,GACzDmB,EAAYG,EACZF,EAAgBC,IAGpBH,GAAaE,GAAiBD,GAC9BM,EAAe,CACXzB,EAAGkB,EAAUlB,CAAC,CACdoD,MAAOlC,EAAUkC,KAAK,CACtBC,MAAOnC,EAAUmC,KAAK,CACtBC,OAAQ,CAAA,CACZ,EACIrC,EAAMsC,CAAC,EAAIpC,EAAUrB,KAAK,EAC1BoB,EAAUqC,CAAC,EAAInC,EAActB,KAAK,EAClCmB,EAAM8B,KAAK,CAAIxB,GAAcG,EAAaoB,iBAAiB,EACvDpB,EAAaqB,KAAK,CACtBT,EAAcC,GAAG,CAACiB,IAAI,CAAChC,IAElBP,EAAMsC,CAAC,CAAGpC,EAAUrB,KAAK,EAC9BoB,EAAUqC,CAAC,CAAGnC,EAActB,KAAK,EACjCmB,EAAM8B,KAAK,CAAIxB,GAAcG,EAAauB,gBAAgB,EACtDvB,EAAaqB,KAAK,CACtBT,EAAcE,MAAM,CAACgB,IAAI,CAAChC,KAG1Bc,EAAcG,SAAS,CAACe,IAAI,CAAChC,GAC7Bc,EAAcG,SAAS,CAACe,IAAI,CAAC/B,GAE7Ba,EAAcG,SAAS,CAACe,IAAI,CAAC/D,EAAMgC,EAAc,CAC7C6B,OAAQ,CAAA,CACZ,IACIrC,EAAMsC,CAAC,EAAIpC,EAAUrB,KAAK,EAC1BoB,EAAUqC,CAAC,CAAGnC,EAActB,KAAK,EACjCmB,EAAM8B,KAAK,CAAIxB,GAAcG,EAAaoB,iBAAiB,EACvDpB,EAAaqB,KAAK,CACtB7B,EAAU6B,KAAK,CAAIxB,GAAcG,EAAauB,gBAAgB,EAC1DvB,EAAaqB,KAAK,CACtBT,EAAcC,GAAG,CAACiB,IAAI,CAAChC,GACvBc,EAAcC,GAAG,CAACiB,IAAI,CAAC/D,EAAMgC,EAAc,CACvC6B,OAAQ,CAAA,CACZ,KAEKrC,EAAMsC,CAAC,CAAGpC,EAAUrB,KAAK,EAC9BoB,EAAUqC,CAAC,EAAInC,EAActB,KAAK,GAClCmB,EAAM8B,KAAK,CAAIxB,GAAcG,EAAauB,gBAAgB,EACtDvB,EAAaqB,KAAK,CACtB7B,EAAU6B,KAAK,CAAIxB,GAAcG,EAAaoB,iBAAiB,EAC3DpB,EAAaqB,KAAK,CACtBT,EAAcE,MAAM,CAACgB,IAAI,CAAChC,GAC1Bc,EAAcE,MAAM,CAACgB,IAAI,CAAC/D,EAAMgC,EAAc,CAC1C6B,OAAQ,CAAA,CACZ,OAIHnC,IACDF,EAAMsC,CAAC,EAAIpC,EAAUrB,KAAK,EAC1BmB,EAAM8B,KAAK,CAAIxB,GAAcG,EAAaoB,iBAAiB,EACvDpB,EAAaqB,KAAK,CACtBT,EAAcC,GAAG,CAACiB,IAAI,CAAChC,KAGvBP,EAAM8B,KAAK,CAAIxB,GAAcG,EAAauB,gBAAgB,EACtDvB,EAAaqB,KAAK,CACtBT,EAAcE,MAAM,CAACgB,IAAI,CAAChC,KAKtC9B,EAAW4C,EAAe,SAAUmB,CAAM,CAAEC,CAAQ,EAChDpD,EAAUuB,MAAM,CAAG4B,EACnBnD,EAAUG,OAAO,CAAGhB,EAAMiD,CAAqB,CAACgB,EAAS,CAACf,MAAM,CAAEP,GAClE9B,EAAU0B,KAAK,CAAG1B,CAAS,CAAC,QAAUoD,EAAW,OAAO,CACxDxE,EAAatB,SAAS,CAACoD,SAAS,CAAClD,IAAI,CAACwC,GAEtCA,CAAS,CAAC,QAAUoD,EAAW,OAAO,CAAGpD,EAAU0B,KAAK,AAC5D,GAEA1B,EAAUuB,MAAM,CAAGC,EACnBxB,EAAUG,OAAO,CAAGiB,EACpBpB,EAAU0B,KAAK,CAAGD,CACtB,EAiCA9B,EAAoBrC,SAAS,CAAC+F,SAAS,CAAG,SAAUC,CAAM,CAAE/C,CAAM,EAC9D,IAeIgD,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAZ,EACAa,EAvBAtD,EAASD,EAAOC,MAAM,CACtBuD,EAAaxD,EAAOwD,UAAU,CAC9BC,EAAOV,EAAOW,KAAK,CACnBC,EAAOZ,EAAOa,KAAK,CAEnBC,EAAK,EAAE,CACPH,EAAQ,EAAE,CACVE,EAAQ,EAAE,CAIVE,EAAgB,AAAC7D,AAAW,IAAXA,EAAgB,EAAIA,EAAS,EAC9C8D,EAAU,EAAE,CACZC,EAAY,EAAE,CACdC,EAAU,EAAE,CAUhB,GAAI,CAACR,CAAAA,EAAKpC,MAAM,EAAIpB,CAAK,GAAOxB,EAAQkF,CAAI,CAAC,EAAE,GAC3CA,AAAmB,IAAnBA,CAAI,CAAC,EAAE,CAACtC,MAAM,GAAUpB,CAAAA,EAAS,CAAA,GAMrC,IAAKsD,EAAI,EAHTU,EAAU9F,EAAapB,SAAS,CAAC+F,SAAS,CAAC7F,IAAI,CAAC,IAAI,CAAE8F,EAAQ,CAC1D9C,OAAQA,CACZ,GAAG2D,KAAK,CACIL,EAAIU,EAAQ5C,MAAM,CAAEkC,IAC5Bb,EAAIiB,CAAI,CAACG,EAAgBP,EAAE,CAC3BD,EAAQK,CAAI,CAACG,EAAgBP,EAAI,EAAE,EAAI,EAAE,CACzCJ,EAAcY,CAAO,CAACR,EAAI,EAAE,CAC5BH,EAAgBY,CAAS,CAACT,EAAI,EAAE,CAChCF,EAASO,CAAK,CAACL,EAAI,EAAE,CACX,IAANA,GACAJ,CAAAA,EAAcC,EAAgBC,EAAS,CAAA,EAE3CL,EAAUxE,EAAa,AAACkE,CAAAA,CAAC,CA9BlB,EA8BwB,CAAGA,CAAC,CA/B7B,EA+BkC,AAAD,EAAK,EAAIc,EAAaS,CAAO,CAACV,EAAE,EACvEN,EAAYzE,EAAa,AAACkE,CAAAA,CAAC,CA/BpB,EA+B0B,CAAGA,CAAC,CAhC/B,EAgCoC,AAAD,EAAK,EAAIc,EAAaS,CAAO,CAACV,EAAE,EACrE,AAACP,EAAUG,GACVG,CAAK,CAnCF,EAmCS,CAAGH,EAChBY,CAAO,CAACR,EAAE,CAAGP,EAGbe,CAAO,CAACR,EAAE,CAAGJ,EAEb,AAACF,EAAYG,GACZE,CAAK,CA1CF,EA0CS,CAAGF,EAChBY,CAAS,CAACT,EAAE,CAAGN,EAGfe,CAAS,CAACT,EAAE,CAAGH,EAEfC,IAAWF,GAAeT,CAAC,CAhDvB,EAgD8B,CAAGqB,CAAO,CAACR,EAAE,EAC/CF,IAAWD,GAAiBV,CAAC,CAjDzB,EAiDgC,CAAGsB,CAAS,CAACT,EAAE,CACnDL,EAAaa,CAAO,CAACR,EAAE,CAElBF,CAAAA,IAAWF,GAAeT,CAAC,CApD5B,EAoDmC,CAAGqB,CAAO,CAACR,EAAE,EACpDF,IAAWD,GAAiBV,CAAC,CArDzB,EAqDgC,CAAGsB,CAAS,CAACT,EAAE,AAAD,GAClDL,CAAAA,EAAac,CAAS,CAACT,EAAE,AAAD,EAE5BM,EAAGlB,IAAI,CAAC,CAACc,CAAI,CAACK,EAAgBP,EAAE,CAAEL,EAAW,EAC7CQ,EAAMf,IAAI,CAACc,CAAI,CAACK,EAAgBP,EAAE,EAClCK,EAAMjB,IAAI,CAACO,GAEf,MAAO,CACHN,OAAQiB,EACRH,MAAOA,EACPE,MAAOA,CACX,EACJ,EAyBAxE,EAAoB8E,cAAc,CAAGtF,EAAMP,EAAa6F,cAAc,CAAE,CAMpElE,OAAQ,CACJhB,MAAO,KAAK,EAIZwE,WAAY,EAMZvD,OAAQ,EACZ,EASAmC,iBAAkB,UASlBH,kBAAmB,UAOnBI,gBAAiB,CACbP,OAAQ,CAIJC,UAAW,EAMXC,UAAW,UAcXG,UAAW,UACf,CACJ,CACJ,GACO/C,CACX,EAAEf,GACFM,EAAOS,EAAoBrC,SAAS,CAAE,CAClCoH,SAAU,aACVC,eAAgB,CAAC,aAAc,SAAS,AAC5C,GACA7G,IAA0I8G,kBAAkB,CAAC,aAAcjF,GAkC9I,IAAIjC,EAAmBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,GAET"}