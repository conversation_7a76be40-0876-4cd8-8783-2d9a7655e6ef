!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").Chart,require("highcharts").SeriesRegistry,require("highcharts").dataGrouping.approximations,require("highcharts").Color):"function"==typeof define&&define.amd?define("highcharts/indicators/indicators-all",[["highcharts/highcharts"],["highcharts/highcharts","Chart"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","dataGrouping","approximations"],["highcharts/highcharts","Color"]],e):"object"==typeof exports?exports["highcharts/indicators/indicators-all"]=e(require("highcharts"),require("highcharts").Chart,require("highcharts").SeriesRegistry,require("highcharts").dataGrouping.approximations,require("highcharts").Color):t.Highcharts=e(t.Highcharts,t.Highcharts.Chart,t.Highcharts.SeriesRegistry,t.Highcharts.dataGrouping.approximations,t.Highcharts.Color)}(this,function(t,e,o,n,r){return function(){"use strict";var i,a,s,p,l,u,c,h,f,y,d,g,m,v,x,_,O,D,b,P,A,S,C,T,w,j,M,V,E,L,k,I,N,B,G,W,z,Y,F,X,R,K,U,Z,q,H,J,Q,$,tt,te,to={512:function(t){t.exports=o},620:function(t){t.exports=r},944:function(e){e.exports=t},956:function(t){t.exports=n},960:function(t){t.exports=e}},tn={};function tr(t){var e=tn[t];if(void 0!==e)return e.exports;var o=tn[t]={exports:{}};return to[t](o,o.exports,tr),o.exports}tr.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return tr.d(e,{a:e}),e},tr.d=function(t,e){for(var o in e)tr.o(e,o)&&!tr.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},tr.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var ti={};tr.d(ti,{default:function(){return iX}});var ta=tr(944),ts=tr.n(ta),tp=tr(960),tl=tr.n(tp),tu=tr(512),tc=tr.n(tu),th=(i=function(t,e){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),tf=function(){return(tf=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var r in e=arguments[o])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},ty=function(t,e,o){if(o||2==arguments.length)for(var n,r=0,i=e.length;r<i;r++)!n&&r in e||(n||(n=Array.prototype.slice.call(e,0,r)),n[r]=e[r]);return t.concat(n||Array.prototype.slice.call(e))},td=tc().seriesTypes.line,tg=ts().addEvent,tm=ts().fireEvent,tv=ts().error,tx=ts().extend,t_=ts().isArray,tO=ts().merge,tD=ts().pick,tb=function(t,e){var o=[],n=t.pointArrayMap,r=e&&t.dataTable.modified||t.dataTable;if(!n)return t.getColumn("y",e);for(var i=n.map(function(o){return t.getColumn(o,e)}),a=function(t){var e=n.map(function(e,o){var n;return(null===(n=i[o])||void 0===n?void 0:n[t])||0});o.push(e)},s=0;s<r.rowCount;s++)a(s);return o},tP=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return th(e,t),e.prototype.destroy=function(){this.dataEventsToUnbind.forEach(function(t){t()}),t.prototype.destroy.apply(this,arguments)},e.prototype.getName=function(){var t=[],e=this.name;return e||((this.nameComponents||[]).forEach(function(e,o){t.push(this.options.params[e]+tD(this.nameSuffixes[o],""))},this),e=(this.nameBase||this.type.toUpperCase())+(this.nameComponents?" ("+t.join(", ")+")":"")),e},e.prototype.getValues=function(t,e){var o,n,r=e.period,i=t.xData||[],a=t.yData,s=a.length,p=[],l=[],u=[],c=-1,h=0,f=0;if(!(i.length<r)){for(t_(a[0])&&(c=e.index?e.index:0);h<r-1;)f+=c<0?a[h]:a[h][c],h++;for(o=h;o<s;o++)f+=c<0?a[o]:a[o][c],n=[i[o],f/r],p.push(n),l.push(n[0]),u.push(n[1]),f-=c<0?a[o-h]:a[o-h][c];return{values:p,xData:l,yData:u}}},e.prototype.init=function(e,o){var n=this;t.prototype.init.call(n,e,o);var r=tg(tl(),"afterLinkSeries",function(t){if(!t.isUpdating){var o=!!n.dataEventsToUnbind.length;if(!n.linkedParent)return tv("Series "+n.options.linkedTo+" not found! Check `linkedTo`.",!1,e);if(!o&&(n.dataEventsToUnbind.push(tg(n.linkedParent,"updatedData",function(){n.recalculateValues()})),n.calculateOn.xAxis&&n.dataEventsToUnbind.push(tg(n.linkedParent.xAxis,n.calculateOn.xAxis,function(){n.recalculateValues()}))),"init"===n.calculateOn.chart)n.closestPointRange||n.recalculateValues();else if(!o)var r=tg(n.chart,n.calculateOn.chart,function(){n.recalculateValues(),r()})}},{order:0});n.dataEventsToUnbind=[],n.eventsToUnbind.push(r)},e.prototype.recalculateValues=function(){var t,e,o,n,r,i=this,a=[],s=this.dataTable,p=this.points||[],l=this.dataTable.rowCount,u=!0,c=this.linkedParent.yData,h=this.linkedParent.processedYData;this.linkedParent.xData=this.linkedParent.getColumn("x"),this.linkedParent.yData=tb(this.linkedParent),this.linkedParent.processedYData=tb(this.linkedParent,!0);var f=this.linkedParent.options&&this.linkedParent.dataTable.rowCount&&this.getValues(this.linkedParent,this.options.params)||{values:[],xData:[],yData:[]};delete this.linkedParent.xData,this.linkedParent.yData=c,this.linkedParent.processedYData=h;var y=this.pointArrayMap||["y"],d={};if(f.yData.forEach(function(t){y.forEach(function(e,o){var n=d[e]||[];n.push(t_(t)?t[o]:t),d[e]||(d[e]=n)})}),l&&!this.hasGroupedData&&this.visible&&this.points){if(this.cropped){this.xAxis&&(n=this.xAxis.min,r=this.xAxis.max);for(var g=this.cropData(s,n,r),m=ty(["x"],this.pointArrayMap||["y"],!0),v=function(t){var e=m.map(function(e){return i.getColumn(e)[t]||0});a.push(e)},x=0;x<((null===(t=g.modified)||void 0===t?void 0:t.rowCount)||0);x++)v(x);var _=this.getColumn("x");e=f.xData.indexOf(_[0]),o=f.xData.indexOf(_[_.length-1]),-1===e&&o===f.xData.length-2&&a[0][0]===p[0].x&&a.shift(),this.updateData(a)}else(this.updateAllPoints||f.xData.length!==l-1&&f.xData.length!==l+1)&&(u=!1,this.updateData(f.values))}u&&(s.setColumns(tf(tf({},d),{x:f.xData})),this.options.data=f.values),this.calculateOn.xAxis&&this.getColumn("x",!0).length&&(this.isDirty=!0,this.redraw()),this.isDirtyData=!!this.linkedSeries.length,tm(this,"updatedData")},e.prototype.processData=function(){var e=this.options.compareToMain,o=this.linkedParent;t.prototype.processData.apply(this,arguments),this.dataModify&&o&&o.dataModify&&o.dataModify.compareValue&&e&&(this.dataModify.compareValue=o.dataModify.compareValue)},e.defaultOptions=tO(td.defaultOptions,{name:void 0,tooltip:{valueDecimals:4},linkedTo:void 0,compareToMain:!1,params:{index:3,period:14}}),e}(td);tx(tP.prototype,{calculateOn:{chart:"init"},hasDerivedData:!0,nameComponents:["period"],nameSuffixes:[],useCommonDataGrouping:!0}),tc().registerSeriesType("sma",tP);var tA=(a=function(t,e){return(a=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}a(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),tS=tc().seriesTypes.sma,tC=ts().correctFloat,tT=ts().isArray,tw=ts().merge,tj=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tA(e,t),e.prototype.accumulatePeriodPoints=function(t,e,o){for(var n=0,r=0,i=0;r<t;)n+=e<0?o[r]:o[r][e],r++;return n},e.prototype.calculateEma=function(t,e,o,n,r,i,a){var s=t[o-1],p=i<0?e[o-1]:e[o-1][i];return[s,void 0===r?a:tC(p*n+r*(1-n))]},e.prototype.getValues=function(t,e){var o,n,r,i=e.period,a=t.xData,s=t.yData,p=s?s.length:0,l=2/(i+1),u=[],c=[],h=[],f=-1,y=0,d=0;if(!(p<i)){for(tT(s[0])&&(f=e.index?e.index:0),d=this.accumulatePeriodPoints(i,f,s)/i,r=i;r<p+1;r++)n=this.calculateEma(a,s,r,l,o,f,d),u.push(n),c.push(n[0]),h.push(n[1]),o=n[1];return{values:u,xData:c,yData:h}}},e.defaultOptions=tw(tS.defaultOptions,{params:{index:3,period:9}}),e}(tS);tc().registerSeriesType("ema",tj);var tM=(s=function(t,e){return(s=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),tV=tc().seriesTypes.sma,tE=ts().error,tL=ts().extend,tk=ts().merge,tI=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tM(e,t),e.populateAverage=function(t,e,o,n,r){var i=e[n][1],a=e[n][2],s=e[n][3],p=o[n],l=s===i&&s===a||i===a?0:(2*s-a-i)/(i-a)*p;return[t[n],l]},e.prototype.getValues=function(t,o){var n,r,i,a=o.period,s=t.xData,p=t.yData,l=o.volumeSeriesID,u=t.chart.get(l),c=null==u?void 0:u.getColumn("y"),h=p?p.length:0,f=[],y=[],d=[];if(!(s.length<=a)||!h||4===p[0].length){if(!u){tE("Series "+l+" not found! Check `volumeSeriesID`.",!0,t.chart);return}for(r=a;r<h;r++)n=f.length,i=e.populateAverage(s,p,c,r,a),n>0&&(i[1]+=f[n-1][1]),f.push(i),y.push(i[0]),d.push(i[1]);return{values:f,xData:y,yData:d}}},e.defaultOptions=tk(tV.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),e}(tV);tL(tI.prototype,{nameComponents:!1,nameBase:"Accumulation/Distribution"}),tc().registerSeriesType("ad",tI);var tN=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),tB=ts().noop,tG=tc().seriesTypes,tW=tG.column.prototype,tz=tG.sma,tY=ts().extend,tF=ts().merge,tX=ts().correctFloat,tR=ts().isArray,tK=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tN(e,t),e.prototype.drawGraph=function(){var t,e=this.options,o=this.points,n=this.userOptions.color,r=e.greaterBarColor,i=e.lowerBarColor,a=o[0];if(!n&&a)for(t=1,a.color=r;t<o.length;t++)o[t].y>o[t-1].y?o[t].color=r:o[t].y<o[t-1].y?o[t].color=i:o[t].color=o[t-1].color},e.prototype.getValues=function(t){var e,o,n,r,i,a,s=t.xData||[],p=t.yData||[],l=p.length,u=[],c=[],h=[],f=0,y=0;if(!(s.length<=34)&&tR(p[0])&&4===p[0].length){for(i=0;i<33;i++)r=(p[i][1]+p[i][2])/2,i>=29&&(y=tX(y+r)),f=tX(f+r);for(a=33;a<l;a++)y=tX(y+(r=(p[a][1]+p[a][2])/2)),f=tX(f+r),e=tX(y/5-f/34),u.push([s[a],e]),c.push(s[a]),h.push(e),o=a+1-5,n=a+1-34,y=tX(y-(p[o][1]+p[o][2])/2),f=tX(f-(p[n][1]+p[n][2])/2);return{values:u,xData:c,yData:h}}},e.defaultOptions=tF(tz.defaultOptions,{params:{index:void 0,period:void 0},greaterBarColor:"#06b535",lowerBarColor:"#f21313",threshold:0,groupPadding:.2,pointPadding:.2,crisp:!1,states:{hover:{halo:{size:0}}}}),e}(tz);tY(tK.prototype,{nameBase:"AO",nameComponents:void 0,markerAttribs:tB,getColumnMetrics:tW.getColumnMetrics,crispCol:tW.crispCol,translate:tW.translate,drawPoints:tW.drawPoints}),tc().registerSeriesType("ao",tK);var tU=tc().seriesTypes.sma.prototype,tZ=ts().defined,tq=ts().error,tH=ts().merge;!function(t){var e=["bottomLine"],o=["top","bottom"],n=["top"];function r(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function i(t,e){var o=[];return(t.pointArrayMap||[]).forEach(function(t){t!==e&&o.push(r(t))}),o}function a(){var t,e=this,o=e.pointValKey,n=e.linesApiNames,a=e.areaLinesNames,s=e.points,p=e.options,l=e.graph,u={options:{gapSize:p.gapSize}},c=[],h=i(e,o),f=s.length;if(h.forEach(function(e,o){for(c[o]=[];f--;)t=s[f],c[o].push({x:t.x,plotX:t.plotX,plotY:t[e],isNull:!tZ(t[e])});f=s.length}),e.userOptions.fillColor&&a.length){var y=c[h.indexOf(r(a[0]))],d=1===a.length?s:c[h.indexOf(r(a[1]))],g=e.color;e.points=d,e.nextPoints=y,e.color=e.userOptions.fillColor,e.options=tH(s,u),e.graph=e.area,e.fillGraph=!0,tU.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=g}n.forEach(function(t,o){c[o]?(e.points=c[o],p[t]?e.options=tH(p[t].styles,u):tq('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],tU.drawGraph.call(e),e["graph"+t]=e.graph):tq('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=s,e.options=p,e.graph=l,tU.drawGraph.call(e)}function s(t){var e,o=[],n=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=tU.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",o=tU.getGraphPath.call(this,t),n=e.slice(0,o.length);for(var r=n.length-1;r>=0;r--)o.push(n[r])}}else o=tU.getGraphPath.apply(this,arguments);return o}function p(t){var e=[];return(this.pointArrayMap||[]).forEach(function(o){e.push(t[o])}),e}function l(){var t,e=this,o=this.pointArrayMap,n=[];n=i(this),tU.translate.apply(this,arguments),this.points.forEach(function(r){o.forEach(function(o,i){t=r[o],e.dataModify&&(t=e.dataModify.modifyValue(t)),null!==t&&(r[n[i]]=e.yAxis.toPixels(t,!0))})})}t.compose=function(t){var r=t.prototype;return r.linesApiNames=r.linesApiNames||e.slice(),r.pointArrayMap=r.pointArrayMap||o.slice(),r.pointValKey=r.pointValKey||"top",r.areaLinesNames=r.areaLinesNames||n.slice(),r.drawGraph=a,r.getGraphPath=s,r.toYData=p,r.translate=l,t}}(te||(te={}));var tJ=te,tQ=(l=function(t,e){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),t$=tc().seriesTypes.sma,t0=ts().extend,t1=ts().merge,t2=ts().pick;function t3(t,e){var o,n=t[0],r=0;for(o=1;o<t.length;o++)("max"===e&&t[o]>=n||"min"===e&&t[o]<=n)&&(n=t[o],r=o);return r}var t4=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tQ(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s=e.period,p=t.xData,l=t.yData,u=l?l.length:0,c=[],h=[],f=[];for(i=s-1;i<u;i++)r=t3((a=l.slice(i-s+1,i+2)).map(function(t){return t2(t[2],t)}),"min"),o=t3(a.map(function(t){return t2(t[1],t)}),"max")/s*100,n=r/s*100,p[i+1]&&(c.push([p[i+1],o,n]),h.push(p[i+1]),f.push([o,n]));return{values:c,xData:h,yData:f}},e.defaultOptions=t1(t$.defaultOptions,{params:{index:void 0,period:25},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Aroon Up: {point.y}<br/>Aroon Down: {point.aroonDown}<br/>'},aroonDown:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),e}(t$);t0(t4.prototype,{areaLinesNames:[],linesApiNames:["aroonDown"],nameBase:"Aroon",pointArrayMap:["y","aroonDown"],pointValKey:"y"}),tJ.compose(t4),tc().registerSeriesType("aroon",t4);var t5=(u=function(t,e){return(u=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),t6=tc().seriesTypes.aroon,t9=ts().extend,t8=ts().merge,t7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return t5(e,t),e.prototype.getValues=function(e,o){var n,r,i=[],a=[],s=[],p=t.prototype.getValues.call(this,e,o);for(r=0;r<p.yData.length;r++)n=p.yData[r][0]-p.yData[r][1],i.push([p.xData[r],n]),a.push(p.xData[r]),s.push(n);return{values:i,xData:a,yData:s}},e.defaultOptions=t8(t6.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b>: {point.y}'}}),e}(t6);t9(t7.prototype,{nameBase:"Aroon Oscillator",linesApiNames:[],pointArrayMap:["y"],pointValKey:"y"}),tJ.compose(t6),tc().registerSeriesType("aroonoscillator",t7);var et=(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ee=tc().seriesTypes.sma,eo=ts().isArray,en=ts().merge;function er(t,e){return Math.max(t[1]-t[2],void 0===e?0:Math.abs(t[1]-e[3]),void 0===e?0:Math.abs(t[2]-e[3]))}var ei=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return et(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a=e.period,s=t.xData,p=t.yData,l=p?p.length:0,u=[[s[0],p[0]]],c=[],h=[],f=[],y=0,d=1,g=0;if(!(s.length<=a)&&eo(p[0])&&4===p[0].length){for(i=1;i<=l;i++)((function(t,e,o,n){var r=e[n],i=o[n];t.push([r,i])})(u,s,p,i),a<d)?(o=i,n=y,y=(r=[s[o-1],(n*(a-1)+er(p[o-1],p[o-2]))/a])[1],c.push(r),h.push(r[0]),f.push(r[1])):(a===d?(y=g/(i-1),c.push([s[i-1],y]),h.push(s[i-1]),f.push(y)):g+=er(p[i-1],p[i-2]),d++);return{values:c,xData:h,yData:f}}},e.defaultOptions=en(ee.defaultOptions,{params:{index:void 0}}),e}(ee);tc().registerSeriesType("atr",ei);var ea=(h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),es=tc().seriesTypes.sma,ep=ts().extend,el=ts().isArray,eu=ts().merge,ec=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ea(e,t),e.prototype.init=function(){tc().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=eu({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)},e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l,u,c=e.period,h=e.standardDeviation,f=[],y=[],d=t.xData,g=t.yData,m=g?g.length:0,v=[];if(!(d.length<c)){var x=el(g[0]);for(u=c;u<=m;u++)a=d.slice(u-c,u),s=g.slice(u-c,u),i=(l=tc().seriesTypes.sma.prototype.getValues.call(this,{xData:a,yData:s},e)).xData[0],o=l.yData[0],p=function(t,e,o,n){for(var r,i=t.length,a=0,s=0,p=0;a<i;a++)p+=(r=(o?t[a][e]:t[a])-n)*r;return Math.sqrt(p/=i-1)}(s,e.index,x,o),n=o+h*p,r=o-h*p,v.push([i,n,o,r]),f.push(i),y.push([n,o,r]);return{values:v,xData:f,yData:y}}},e.defaultOptions=eu(es.defaultOptions,{params:{period:20,standardDeviation:2,index:3},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),e}(es);ep(ec.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","standardDeviation"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),tJ.compose(ec),tc().registerSeriesType("bb",ec);var eh=(f=function(t,e){return(f=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}f(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ef=tc().seriesTypes.sma,ey=ts().isArray,ed=ts().merge,eg=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eh(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l=e.period,u=t.xData,c=t.yData,h=c?c.length:0,f=[],y=[],d=[],g=[],m=[],v=1;if(!(u.length<=l)&&ey(c[0])&&4===c[0].length){for(;v<l;)n=c[v-1],f.push((n[1]+n[2]+n[3])/3),v++;for(p=l;p<=h;p++)a=((n=c[p-1])[1]+n[2]+n[3])/3,r=f.push(a),i=(m=f.slice(r-l)).reduce(function(t,e){return t+e},0)/l,s=function(t,e){var o,n=t.length,r=0;for(o=0;o<n;o++)r+=Math.abs(e-t[o]);return r}(m,i)/l,o=(a-i)/(.015*s),y.push([u[p-1],o]),d.push(u[p-1]),g.push(o);return{values:y,xData:d,yData:g}}},e.defaultOptions=ed(ef.defaultOptions,{params:{index:void 0}}),e}(ef);tc().registerSeriesType("cci",eg);var em=(y=function(t,e){return(y=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}y(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ev=tc().seriesTypes.sma,ex=ts().merge,e_=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.nameBase="Chaikin Money Flow",e}return em(e,t),e.prototype.isValid=function(){var t,e=this.chart,o=this.options,n=this.linkedParent,r=this.volumeSeries||(this.volumeSeries=e.get(o.params.volumeSeriesID)),i=(null===(t=null==n?void 0:n.pointArrayMap)||void 0===t?void 0:t.length)===4;function a(t){return t.dataTable.rowCount>=o.params.period}return!!(n&&r&&a(n)&&a(r)&&i)},e.prototype.getValues=function(t,e){if(this.isValid())return this.getMoneyFlow(t.xData,t.yData,this.volumeSeries.getColumn("y"),e.period)},e.prototype.getMoneyFlow=function(t,e,o,n){var r,i,a=e.length,s=[],p=[],l=[],u=[],c=-1,h=0,f=0;function y(t,e){var o=t[1],n=t[2],i=t[3];return null!==e&&null!==o&&null!==n&&null!==i&&o!==n?(i-n-(o-i))/(o-n)*e:(c=r,null)}if(n>0&&n<=a){for(r=0;r<n;r++)s[r]=y(e[r],o[r]),h+=o[r],f+=s[r];for(p.push(t[r-1]),l.push(r-c>=n&&0!==h?f/h:null),u.push([p[0],l[0]]);r<a;r++)s[r]=y(e[r],o[r]),h-=o[r-n],h+=o[r],f-=s[r-n],f+=s[r],i=[t[r],r-c>=n?f/h:null],p.push(i[0]),l.push(i[1]),u.push([i[0],i[1]])}return{values:u,xData:p,yData:l}},e.defaultOptions=ex(ev.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),e}(ev);tc().registerSeriesType("cmf",e_);var eO=(d=function(t,e){return(d=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}d(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),eD=tc().seriesTypes.sma,eb=ts().correctFloat,eP=ts().extend,eA=ts().isArray,eS=ts().merge,eC=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eO(e,t),e.prototype.calculateDM=function(t,e,o){var n,r=t[e][1],i=t[e][2],a=t[e-1][1],s=t[e-1][2];return eb(r-a>s-i?o?Math.max(r-a,0):0:o?0:Math.max(s-i,0))},e.prototype.calculateDI=function(t,e){return t/e*100},e.prototype.calculateDX=function(t,e){return eb(Math.abs(t-e)/Math.abs(t+e)*100)},e.prototype.smoothValues=function(t,e,o){return eb(t-t/o+e)},e.prototype.getTR=function(t,e){return eb(Math.max(t[1]-t[2],e?Math.abs(t[1]-e[3]):0,e?Math.abs(t[2]-e[3]):0))},e.prototype.getValues=function(t,e){var o=e.period,n=t.xData,r=t.yData,i=r?r.length:0,a=[],s=[],p=[];if(!(n.length<=o)&&eA(r[0])&&4===r[0].length){var l,u=0,c=0,h=0;for(l=1;l<i;l++){var f=void 0,y=void 0,d=void 0,g=void 0,m=void 0,v=void 0,x=void 0,_=void 0,O=void 0;l<=o?(g=this.calculateDM(r,l,!0),m=this.calculateDM(r,l),v=this.getTR(r[l],r[l-1]),u+=g,c+=m,h+=v,l===o&&(x=this.calculateDI(u,h),_=this.calculateDI(c,h),O=this.calculateDX(u,c),a.push([n[l],O,x,_]),s.push(n[l]),p.push([O,x,_]))):(g=this.calculateDM(r,l,!0),m=this.calculateDM(r,l),v=this.getTR(r[l],r[l-1]),f=this.smoothValues(u,g,o),y=this.smoothValues(c,m,o),d=this.smoothValues(h,v,o),u=f,c=y,h=d,x=this.calculateDI(u,h),_=this.calculateDI(c,h),O=this.calculateDX(u,c),a.push([n[l],O,x,_]),s.push(n[l]),p.push([O,x,_]))}return{values:a,xData:s,yData:p}}},e.defaultOptions=eS(eD.defaultOptions,{params:{index:void 0},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">DX</span>: {point.y}<br/><span style="color: {point.series.options.plusDILine.styles.lineColor}">+DI</span>: {point.plusDI}<br/><span style="color: {point.series.options.minusDILine.styles.lineColor}">-DI</span>: {point.minusDI}<br/>'},plusDILine:{styles:{lineWidth:1,lineColor:"#06b535"}},minusDILine:{styles:{lineWidth:1,lineColor:"#f21313"}},dataGrouping:{approximation:"averages"}}),e}(eD);eP(eC.prototype,{areaLinesNames:[],nameBase:"DMI",linesApiNames:["plusDILine","minusDILine"],pointArrayMap:["y","plusDI","minusDI"],parallelArrays:["x","y","plusDI","minusDI"],pointValKey:"y"}),tJ.compose(eC),tc().registerSeriesType("dmi",eC);var eT=(g=function(t,e){return(g=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}g(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ew=tc().seriesTypes.sma,ej=ts().extend,eM=ts().merge,eV=ts().correctFloat,eE=ts().pick;function eL(t,e,o,n,r){var i=eE(e[o][n],e[o]);return r?eV(t-i):eV(t+i)}var ek=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eT(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s=e.period,p=e.index,l=Math.floor(s/2+1),u=s+l,c=t.xData||[],h=t.yData||[],f=h.length,y=[],d=[],g=[],m=0;if(!(c.length<=u)){for(i=0;i<s-1;i++)m=eL(m,h,i,p);for(a=0;a<=f-u;a++)n=a+s-1,r=a+u-1,m=eL(m,h,n,p),o=eE(h[r][p],h[r])-m/s,m=eL(m,h,a,p,!0),y.push([c[r],o]),d.push(c[r]),g.push(o);return{values:y,xData:d,yData:g}}},e.defaultOptions=eM(ew.defaultOptions,{params:{index:0,period:21}}),e}(ew);ej(ek.prototype,{nameBase:"DPO"}),tc().registerSeriesType("dpo",ek);var eI=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),eN=tc().seriesTypes.ema,eB=ts().correctFloat,eG=ts().extend,eW=ts().merge,ez=ts().error,eY=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eI(e,t),e.prototype.getValues=function(e,o){var n,r,i=o.periods,a=o.period,s=[],p=[],l=[];if(2!==i.length||i[1]<=i[0]){ez('Error: "Chaikin requires two periods. Notice, first period should be lower than the second one."');return}var u=tI.prototype.getValues.call(this,e,{volumeSeriesID:o.volumeSeriesID,period:a});if(u){var c=t.prototype.getValues.call(this,u,{period:i[0]}),h=t.prototype.getValues.call(this,u,{period:i[1]});if(c&&h){var f=i[1]-i[0];for(r=0;r<h.yData.length;r++)n=eB(c.yData[r+f]-h.yData[r]),s.push([h.xData[r],n]),p.push(h.xData[r]),l.push(n);return{values:s,xData:p,yData:l}}}},e.defaultOptions=eW(eN.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",period:9,periods:[3,10]}}),e}(eN);eG(eY.prototype,{nameBase:"Chaikin Osc",nameComponents:["periods"]}),tc().registerSeriesType("chaikin",eY);var eF=(v=function(t,e){return(v=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}v(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),eX=tc().seriesTypes.sma,eR=ts().isNumber,eK=ts().merge,eU=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eF(e,t),e.prototype.getValues=function(t,e){var o,n,r=e.period,i=t.xData,a=t.yData,s=a?a.length:0,p=[],l=[],u=[],c=e.index;if(!(i.length<r)){eR(a[0])?n=a:(c=Math.min(c,a[0].length-1),n=a.map(function(t){return t[c]}));for(var h,f=0,y=0,d=0,g=r;g>0;g--)n[g]>n[g-1]?y+=n[g]-n[g-1]:n[g]<n[g-1]&&(d+=n[g-1]-n[g]);for(h=y+d>0?100*(y-d)/(y+d):0,l.push(i[r]),u.push(h),p.push([i[r],h]),o=r+1;o<s;o++)f=Math.abs(n[o-r-1]-n[o-r]),n[o]>n[o-1]?y+=n[o]-n[o-1]:n[o]<n[o-1]&&(d+=n[o-1]-n[o]),n[o-r]>n[o-r-1]?y-=f:d-=f,h=y+d>0?100*(y-d)/(y+d):0,l.push(i[o]),u.push(h),p.push([i[o],h]);return{values:p,xData:l,yData:u}}},e.defaultOptions=eK(eX.defaultOptions,{params:{period:20,index:3}}),e}(eX);tc().registerSeriesType("cmo",eU);var eZ=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),eq=tc().seriesTypes.ema,eH=ts().correctFloat,eJ=ts().isArray,eQ=ts().merge,e$=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eZ(e,t),e.prototype.getEMA=function(e,o,n,r,i,a){return t.prototype.calculateEma.call(this,a||[],e,void 0===i?1:i,this.EMApercent,o,void 0===r?-1:r,n)},e.prototype.getValues=function(e,o){var n,r,i,a,s,p=o.period,l=[],u=2*p,c=e.xData,h=e.yData,f=h?h.length:0,y=[],d=[],g=[],m=0,v=0,x=-1,_=0;if(this.EMApercent=2/(p+1),!(f<2*p-1)){for(eJ(h[0])&&(x=o.index?o.index:0),_=(m=t.prototype.accumulatePeriodPoints.call(this,p,x,h))/p,m=0,a=p;a<f+2;a++)a<f+1&&(v=this.getEMA(h,r,_,x,a)[1],l.push(v)),r=v,a<u?m+=v:(a===u&&(_=m/p),v=l[a-p-1],n=this.getEMA([v],i,_)[1],s=[c[a-2],eH(2*v-n)],y.push(s),d.push(s[0]),g.push(s[1]),i=n);return{values:y,xData:d,yData:g}}},e.defaultOptions=eQ(eq.defaultOptions),e}(eq);tc().registerSeriesType("dema",e$);var e0=(_=function(t,e){return(_=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}_(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),e1=tc().seriesTypes.ema,e2=ts().correctFloat,e3=ts().isArray,e4=ts().merge,e5=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return e0(e,t),e.prototype.getEMA=function(e,o,n,r,i,a){return t.prototype.calculateEma.call(this,a||[],e,void 0===i?1:i,this.EMApercent,o,void 0===r?-1:r,n)},e.prototype.getTemaPoint=function(t,e,o,n){return[t[n-3],e2(3*o.level1-3*o.level2+o.level3)]},e.prototype.getValues=function(e,o){var n,r,i,a,s=o.period,p=2*s,l=3*s,u=e.xData,c=e.yData,h=c?c.length:0,f=[],y=[],d=[],g=[],m=[],v={},x=-1,_=0,O=0;if(this.EMApercent=2/(s+1),!(h<3*s-2)){for(e3(c[0])&&(x=o.index?o.index:0),O=(_=t.prototype.accumulatePeriodPoints.call(this,s,x,c))/s,_=0,i=s;i<h+3;i++)i<h+1&&(v.level1=this.getEMA(c,n,O,x,i)[1],g.push(v.level1)),n=v.level1,i<p?_+=v.level1:(i===p&&(O=_/s,_=0),v.level1=g[i-s-1],v.level2=this.getEMA([v.level1],r,O)[1],m.push(v.level2),r=v.level2,i<l?_+=v.level2:(i===l&&(O=_/s),i===h+1&&(v.level1=g[i-s-1],v.level2=this.getEMA([v.level1],r,O)[1],m.push(v.level2)),v.level1=g[i-s-2],v.level2=m[i-2*s-1],v.level3=this.getEMA([v.level2],v.prevLevel3,O)[1],(a=this.getTemaPoint(u,l,v,i))&&(f.push(a),y.push(a[0]),d.push(a[1])),v.prevLevel3=v.level3));return{values:f,xData:y,yData:d}}},e.defaultOptions=e4(e1.defaultOptions),e}(e1);tc().registerSeriesType("tema",e5);var e6=(O=function(t,e){return(O=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}O(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),e9=tc().seriesTypes.tema,e8=ts().correctFloat,e7=ts().merge,ot=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return e6(e,t),e.prototype.getTemaPoint=function(t,e,o,n){if(n>e)return[t[n-3],0!==o.prevLevel3?e8(o.level3-o.prevLevel3)/o.prevLevel3*100:null]},e.defaultOptions=e7(e9.defaultOptions),e}(e9);tc().registerSeriesType("trix",ot);var oe=(D=function(t,e){return(D=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}D(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),oo=tc().seriesTypes.ema,on=ts().extend,or=ts().merge,oi=ts().error,oa=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oe(e,t),e.prototype.getValues=function(e,o){var n,r,i=o.periods,a=o.index,s=[],p=[],l=[];if(2!==i.length||i[1]<=i[0]){oi('Error: "APO requires two periods. Notice, first period should be lower than the second one."');return}var u=t.prototype.getValues.call(this,e,{index:a,period:i[0]}),c=t.prototype.getValues.call(this,e,{index:a,period:i[1]});if(u&&c){var h=i[1]-i[0];for(r=0;r<c.yData.length;r++)n=u.yData[r+h]-c.yData[r],s.push([c.xData[r],n]),p.push(c.xData[r]),l.push(n);return{values:s,xData:p,yData:l}}},e.defaultOptions=or(oo.defaultOptions,{params:{period:void 0,periods:[10,20]}}),e}(oo);on(oa.prototype,{nameBase:"APO",nameComponents:["periods"]}),tc().registerSeriesType("apo",oa);var os=tr(956),op=tr.n(os),ol=tr(620),ou=tr.n(ol),oc=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),oh=ou().parse,of=tc().seriesTypes.sma,oy=ts().defined,od=ts().extend,og=ts().isArray,om=ts().isNumber,ov=ts().getClosestDistance,ox=ts().merge,o_=ts().objectEach;function oO(t){return{high:t.reduce(function(t,e){return Math.max(t,e[1])},-1/0),low:t.reduce(function(t,e){return Math.min(t,e[2])},1/0)}}function oD(t){var e=t.indicator;e.points=t.points,e.nextPoints=t.nextPoints,e.color=t.color,e.options=ox(t.options.senkouSpan.styles,t.gap),e.graph=t.graph,e.fillGraph=!0,tc().seriesTypes.sma.prototype.drawGraph.call(e)}var ob=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.data=[],e.options={},e.points=[],e.graphCollection=[],e}return oc(e,t),e.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options=ox({tenkanLine:{styles:{lineColor:this.color}},kijunLine:{styles:{lineColor:this.color}},chikouLine:{styles:{lineColor:this.color}},senkouSpanA:{styles:{lineColor:this.color,fill:oh(this.color).setOpacity(.5).get()}},senkouSpanB:{styles:{lineColor:this.color,fill:oh(this.color).setOpacity(.5).get()}},senkouSpan:{styles:{fill:oh(this.color).setOpacity(.2).get()}}},this.options)},e.prototype.toYData=function(t){return[t.tenkanSen,t.kijunSen,t.chikouSpan,t.senkouSpanA,t.senkouSpanB]},e.prototype.translate=function(){tc().seriesTypes.sma.prototype.translate.apply(this);for(var t=0,e=this.points;t<e.length;t++)for(var o=e[t],n=0,r=this.pointArrayMap;n<r.length;n++){var i=r[n],a=o[i];om(a)&&(o["plot"+i]=this.yAxis.toPixels(a,!0),o.plotY=o["plot"+i],o.tooltipPos=[o.plotX,o["plot"+i]],o.isNull=!1)}},e.prototype.drawGraph=function(){var t,e,o,n,r,i,a,s,p,l,u,c,h,f=this,y=f.points,d=f.options,g=f.graph,m=f.color,v={options:{gapSize:d.gapSize}},x=f.pointArrayMap.length,_=[[],[],[],[],[],[]],O={tenkanLine:_[0],kijunLine:_[1],chikouLine:_[2],senkouSpanA:_[3],senkouSpanB:_[4],senkouSpan:_[5]},D=[],b=f.options.senkouSpan,P=b.color||b.styles.fill,A=b.negativeColor,S=[[],[]],C=[[],[]],T=y.length,w=0;for(f.ikhMap=O;T--;){for(o=0,e=y[T];o<x;o++)oy(e[t=f.pointArrayMap[o]])&&_[o].push({plotX:e.plotX,plotY:e["plot"+t],isNull:!1});if(A&&T!==y.length-1){var j=O.senkouSpanB.length-1,M=function(t,e,o,n){if(t&&e&&o&&n){var r=e.plotX-t.plotX,i=e.plotY-t.plotY,a=n.plotX-o.plotX,s=n.plotY-o.plotY,p=t.plotX-o.plotX,l=t.plotY-o.plotY,u=(-i*p+r*l)/(-a*i+r*s),c=(a*l-s*p)/(-a*i+r*s);if(u>=0&&u<=1&&c>=0&&c<=1)return{plotX:t.plotX+c*r,plotY:t.plotY+c*i}}}(O.senkouSpanA[j-1],O.senkouSpanA[j],O.senkouSpanB[j-1],O.senkouSpanB[j]);if(M){var V={plotX:M.plotX,plotY:M.plotY,isNull:!1,intersectPoint:!0};O.senkouSpanA.splice(j,0,V),O.senkouSpanB.splice(j,0,V),D.push(j)}}}if(o_(O,function(t,e){d[e]&&"senkouSpan"!==e&&(f.points=_[w],f.options=ox(d[e].styles,v),f.graph=f["graph"+e],f.fillGraph=!1,f.color=m,tc().seriesTypes.sma.prototype.drawGraph.call(f),f["graph"+e]=f.graph),w++}),f.graphCollection)for(var E=0,L=f.graphCollection;E<L.length;E++){var k=L[E];f[k].destroy(),delete f[k]}if(f.graphCollection=[],A&&O.senkouSpanA[0]&&O.senkouSpanB[0]){for(D.unshift(0),D.push(O.senkouSpanA.length-1),c=0;c<D.length-1;c++)if(n=D[c],r=D[c+1],i=O.senkouSpanB.slice(n,r+1),a=O.senkouSpanA.slice(n,r+1),Math.floor(i.length/2)>=1){var I=Math.floor(i.length/2);if(i[I].plotY===a[I].plotY){for(h=0,s=0,p=0;h<i.length;h++)s+=i[h].plotY,p+=a[h].plotY;S[u=s>p?0:1]=S[u].concat(i),C[u]=C[u].concat(a)}else S[u=i[I].plotY>a[I].plotY?0:1]=S[u].concat(i),C[u]=C[u].concat(a)}else S[u=i[0].plotY>a[0].plotY?0:1]=S[u].concat(i),C[u]=C[u].concat(a);["graphsenkouSpanColor","graphsenkouSpanNegativeColor"].forEach(function(t,e){S[e].length&&C[e].length&&(l=0===e?P:A,oD({indicator:f,points:S[e],nextPoints:C[e],color:l,options:d,gap:v,graph:f[t]}),f[t]=f.graph,f.graphCollection.push(t))})}else oD({indicator:f,points:O.senkouSpanB,nextPoints:O.senkouSpanA,color:P,options:d,gap:v,graph:f.graphsenkouSpan}),f.graphsenkouSpan=f.graph;delete f.nextPoints,delete f.fillGraph,f.points=y,f.options=d,f.graph=g,f.color=m},e.prototype.getGraphPath=function(t){var e,o=[],n=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=tc().seriesTypes.sma.prototype.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",o=tc().seriesTypes.sma.prototype.getGraphPath.call(this,t),n=e.slice(0,o.length);for(var r=n.length-1;r>=0;r--)o.push(n[r])}}else o=tc().seriesTypes.sma.prototype.getGraphPath.apply(this,arguments);return o},e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l,u,c,h=e.period,f=e.periodTenkan,y=e.periodSenkouSpanB,d=t.xData,g=t.yData,m=t.xAxis,v=g&&g.length||0,x=ov(m.series.map(function(t){return t.getColumn("x")})),_=[],O=[];if(!(d.length<=h)&&og(g[0])&&4===g[0].length){var D=d[0]-h*x;for(a=0;a<h;a++)O.push(D+a*x);for(a=0;a<v;a++)a>=f&&(s=((n=oO(g.slice(a-f,a))).high+n.low)/2),a>=h&&(u=(s+(p=((r=oO(g.slice(a-h,a))).high+r.low)/2))/2),a>=y&&(c=((i=oO(g.slice(a-y,a))).high+i.low)/2),l=g[a][3],o=d[a],void 0===_[a]&&(_[a]=[]),void 0===_[a+h-1]&&(_[a+h-1]=[]),_[a+h-1][0]=s,_[a+h-1][1]=p,_[a+h-1][2]=void 0,void 0===_[a+1]&&(_[a+1]=[]),_[a+1][2]=l,a<=h&&(_[a+h-1][3]=void 0,_[a+h-1][4]=void 0),void 0===_[a+2*h-2]&&(_[a+2*h-2]=[]),_[a+2*h-2][3]=u,_[a+2*h-2][4]=c,O.push(o);for(a=1;a<=h;a++)O.push(o+a*x);return{values:_,xData:O,yData:_}}},e.defaultOptions=ox(of.defaultOptions,{params:{index:void 0,period:26,periodTenkan:9,periodSenkouSpanB:52},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>TENKAN SEN: {point.tenkanSen:.3f}<br/>KIJUN SEN: {point.kijunSen:.3f}<br/>CHIKOU SPAN: {point.chikouSpan:.3f}<br/>SENKOU SPAN A: {point.senkouSpanA:.3f}<br/>SENKOU SPAN B: {point.senkouSpanB:.3f}<br/>'},tenkanLine:{styles:{lineWidth:1,lineColor:void 0}},kijunLine:{styles:{lineWidth:1,lineColor:void 0}},chikouLine:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanA:{styles:{lineWidth:1,lineColor:void 0}},senkouSpanB:{styles:{lineWidth:1,lineColor:void 0}},senkouSpan:{styles:{fill:"rgba(255, 0, 0, 0.5)"}},dataGrouping:{approximation:"ichimoku-averages"}}),e}(of);od(ob.prototype,{pointArrayMap:["tenkanSen","kijunSen","chikouSpan","senkouSpanA","senkouSpanB"],pointValKey:"tenkanSen",nameComponents:["periodSenkouSpanB","period","periodTenkan"]}),op()["ichimoku-averages"]=function(){var t,e=[];return[].forEach.call(arguments,function(o,n){e.push(op().average(o)),t=!t&&void 0===e[n]}),t?void 0:e},tc().registerSeriesType("ikh",ob);var oP=(P=function(t,e){return(P=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}P(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),oA=tc().seriesTypes.sma,oS=ts().correctFloat,oC=ts().extend,oT=ts().merge,ow=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oP(e,t),e.prototype.init=function(){tc().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=oT({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)},e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l=e.period,u=e.periodATR,c=e.multiplierATR,h=e.index,f=t.yData,y=f?f.length:0,d=[],g=tc().seriesTypes.ema.prototype.getValues(t,{period:l,index:h}),m=tc().seriesTypes.atr.prototype.getValues(t,{period:u}),v=[],x=[];if(!(y<l)){for(p=l;p<=y;p++)a=g.values[p-l],s=m.values[p-u],i=a[0],n=oS(a[1]+c*s[1]),r=oS(a[1]-c*s[1]),o=a[1],d.push([i,n,o,r]),v.push(i),x.push([n,o,r]);return{values:d,xData:v,yData:x}}},e.defaultOptions=oT(oA.defaultOptions,{params:{index:0,period:20,periodATR:10,multiplierATR:2},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Upper Channel: {point.top}<br/>EMA({series.options.params.period}): {point.middle}<br/>Lower Channel: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"},lineWidth:1}),e}(oA);oC(ow.prototype,{nameBase:"Keltner Channels",areaLinesNames:["top","bottom"],nameComponents:["period","periodATR","multiplierATR"],linesApiNames:["topLine","bottomLine"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),tJ.compose(ow),tc().registerSeriesType("keltnerchannels",ow);var oj=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),oM=tc().seriesTypes,oV=oM.ema,oE=oM.sma,oL=ts().correctFloat,ok=ts().error,oI=ts().extend,oN=ts().isArray,oB=ts().merge,oG=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oj(e,t),e.prototype.calculateTrend=function(t,e){return t[e][1]+t[e][2]+t[e][3]>t[e-1][1]+t[e-1][2]+t[e-1][3]?1:-1},e.prototype.isValidData=function(t){var e=this.chart,o=this.options,n=this.linkedParent,r=oN(t)&&4===t.length,i=this.volumeSeries||(this.volumeSeries=e.get(o.params.volumeSeriesID));return i||ok("Series "+o.params.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,n.chart),!!([n,i].every(function(t){return t&&t.dataTable.rowCount>=o.params.slowAvgPeriod})&&r)},e.prototype.getCM=function(t,e,o,n,r){return oL(e+(o===n?t:r))},e.prototype.getDM=function(t,e){return oL(t-e)},e.prototype.getVolumeForce=function(t){for(var e,o,n=[],r=0,i=1,a=0,s=t[0][1]-t[0][2],p=0;i<t.length;i++)o=this.calculateTrend(t,i),e=this.getDM(t[i][1],t[i][2]),r=this.getCM(a,e,o,p,s),n.push([this.volumeSeries.getColumn("y")[i]*o*Math.abs(2*(e/r-1))*100]),p=o,a=r,s=e;return n},e.prototype.getEMA=function(t,e,o,n,r,i,a){return oV.prototype.calculateEma(a||[],t,void 0===i?1:i,n,e,void 0===r?-1:r,o)},e.prototype.getSMA=function(t,e,o){return oV.prototype.accumulatePeriodPoints(t,e,o)/t},e.prototype.getValues=function(t,e){var o,n,r=[],i=t.xData,a=t.yData,s=[],p=[],l=[],u=0,c=0,h=void 0,f=void 0,y=null;if(this.isValidData(a[0])){for(var d=this.getVolumeForce(a),g=this.getSMA(e.fastAvgPeriod,0,d),m=this.getSMA(e.slowAvgPeriod,0,d),v=2/(e.fastAvgPeriod+1),x=2/(e.slowAvgPeriod+1);u<a.length;u++)u>=e.fastAvgPeriod&&(h=c=this.getEMA(d,h,g,v,0,u,i)[1]),u>=e.slowAvgPeriod&&(f=n=this.getEMA(d,f,m,x,0,u,i)[1],o=oL(c-n),l.push(o),l.length>=e.signalPeriod&&(y=l.slice(-e.signalPeriod).reduce(function(t,e){return t+e})/e.signalPeriod),r.push([i[u],o,y]),s.push(i[u]),p.push([o,y]));return{values:r,xData:s,yData:p}}},e.defaultOptions=oB(oE.defaultOptions,{params:{fastAvgPeriod:34,slowAvgPeriod:55,signalPeriod:13,volumeSeriesID:"volume"},signalLine:{styles:{lineWidth:1,lineColor:"#ff0000"}},dataGrouping:{approximation:"averages"},tooltip:{pointFormat:'<span style="color: {point.color}">●</span><b> {series.name}</b><br/><span style="color: {point.color}">Klinger</span>: {point.y}<br/><span style="color: {point.series.options.signalLine.styles.lineColor}">Signal</span>: {point.signal}<br/>'}}),e}(oE);oI(oG.prototype,{areaLinesNames:[],linesApiNames:["signalLine"],nameBase:"Klinger",nameComponents:["fastAvgPeriod","slowAvgPeriod"],pointArrayMap:["y","signal"],parallelArrays:["x","y","signal"],pointValKey:"y"}),tJ.compose(oG),tc().registerSeriesType("klinger",oG);var oW=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),oz=ts().noop,oY=tc().seriesTypes,oF=(oY.column,oY.sma),oX=ts().extend,oR=ts().correctFloat,oK=ts().defined,oU=ts().merge,oZ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oW(e,t),e.prototype.init=function(){tc().seriesTypes.sma.prototype.init.apply(this,arguments);var t=this.color;this.options&&(oK(this.colorIndex)&&(this.options.signalLine&&this.options.signalLine.styles&&!this.options.signalLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.signalLine.styles.lineColor=this.color),this.options.macdLine&&this.options.macdLine.styles&&!this.options.macdLine.styles.lineColor&&(this.options.colorIndex=this.colorIndex+1,this.getCyclic("color",void 0,this.chart.options.colors),this.options.macdLine.styles.lineColor=this.color)),this.macdZones={zones:this.options.macdLine.zones,startIndex:0},this.signalZones={zones:this.macdZones.zones.concat(this.options.signalLine.zones),startIndex:this.macdZones.zones.length}),this.color=t},e.prototype.toYData=function(t){return[t.y,t.signal,t.MACD]},e.prototype.translate=function(){var t=this,e=["plotSignal","plotMACD"];ts().seriesTypes.column.prototype.translate.apply(t),t.points.forEach(function(o){[o.signal,o.MACD].forEach(function(n,r){null!==n&&(o[e[r]]=t.yAxis.toPixels(n,!0))})})},e.prototype.destroy=function(){this.graph=null,this.graphmacd=this.graphmacd&&this.graphmacd.destroy(),this.graphsignal=this.graphsignal&&this.graphsignal.destroy(),tc().seriesTypes.sma.prototype.destroy.apply(this,arguments)},e.prototype.drawGraph=function(){for(var t,e=this,o=e.points,n=e.options,r=e.zones,i={options:{gapSize:n.gapSize}},a=[[],[]],s=o.length;s--;)oK((t=o[s]).plotMACD)&&a[0].push({plotX:t.plotX,plotY:t.plotMACD,isNull:!oK(t.plotMACD)}),oK(t.plotSignal)&&a[1].push({plotX:t.plotX,plotY:t.plotSignal,isNull:!oK(t.plotMACD)});["macd","signal"].forEach(function(t,o){var r;e.points=a[o],e.options=oU((null===(r=n[""+t+"Line"])||void 0===r?void 0:r.styles)||{},i),e.graph=e["graph".concat(t)],e.zones=(e[""+t+"Zones"].zones||[]).slice(e[""+t+"Zones"].startIndex||0),tc().seriesTypes.sma.prototype.drawGraph.call(e),e["graph".concat(t)]=e.graph}),e.points=o,e.options=n,e.zones=r},e.prototype.applyZones=function(){var t=this.zones;this.zones=this.signalZones.zones,tc().seriesTypes.sma.prototype.applyZones.call(this),this.graphmacd&&this.options.macdLine.zones.length&&this.graphmacd.hide(),this.zones=t},e.prototype.getValues=function(t,e){var o,n,r,i=e.longPeriod-e.shortPeriod,a=[],s=[],p=[],l=0,u=[];if(!(t.xData.length<e.longPeriod+e.signalPeriod)){for(r=0,o=tc().seriesTypes.ema.prototype.getValues(t,{period:e.shortPeriod,index:e.index}),n=tc().seriesTypes.ema.prototype.getValues(t,{period:e.longPeriod,index:e.index}),o=o.values,n=n.values;r<=o.length;r++)oK(n[r])&&oK(n[r][1])&&oK(o[r+i])&&oK(o[r+i][0])&&a.push([o[r+i][0],0,null,o[r+i][1]-n[r][1]]);for(r=0;r<a.length;r++)s.push(a[r][0]),p.push([0,null,a[r][3]]);for(r=0,u=(u=tc().seriesTypes.ema.prototype.getValues({xData:s,yData:p},{period:e.signalPeriod,index:2})).values;r<a.length;r++)a[r][0]>=u[0][0]&&(a[r][2]=u[l][1],p[r]=[0,u[l][1],a[r][3]],null===a[r][3]?(a[r][1]=0,p[r][0]=0):(a[r][1]=oR(a[r][3]-u[l][1]),p[r][0]=oR(a[r][3]-u[l][1])),l++);return{values:a,xData:s,yData:p}}},e.defaultOptions=oU(oF.defaultOptions,{params:{shortPeriod:12,longPeriod:26,signalPeriod:9,period:26},signalLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},macdLine:{zones:[],styles:{lineWidth:1,lineColor:void 0}},threshold:0,groupPadding:.1,pointPadding:.1,crisp:!1,states:{hover:{halo:{size:0}}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b> {series.name}</b><br/>Value: {point.MACD}<br/>Signal: {point.signal}<br/>Histogram: {point.y}<br/>'},dataGrouping:{approximation:"averages"},minPointLength:0}),e}(oF);oX(oZ.prototype,{nameComponents:["longPeriod","shortPeriod","signalPeriod"],pointArrayMap:["y","signal","MACD"],parallelArrays:["x","y","signal","MACD"],pointValKey:"y",markerAttribs:oz,getColumnMetrics:ts().seriesTypes.column.prototype.getColumnMetrics,crispCol:ts().seriesTypes.column.prototype.crispCol,drawPoints:ts().seriesTypes.column.prototype.drawPoints}),tc().registerSeriesType("macd",oZ);var oq=(C=function(t,e){return(C=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}C(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),oH=tc().seriesTypes.sma,oJ=ts().extend,oQ=ts().merge,o$=ts().error,o0=ts().isArray;function o1(t){return t.reduce(function(t,e){return t+e})}function o2(t){return(t[1]+t[2]+t[3])/3}var o3=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oq(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p=e.period,l=t.xData,u=t.yData,c=u?u.length:0,h=e.decimals,f=t.chart.get(e.volumeSeriesID),y=(null==f?void 0:f.getColumn("y"))||[],d=[],g=[],m=[],v=[],x=[],_=!1,O=1;if(!f){o$("Series "+e.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,t.chart);return}if(!(l.length<=p)&&o0(u[0])&&4===u[0].length&&y){for(o=o2(u[O]);O<p+1;)n=o,_=(o=o2(u[O]))>=n,r=o*y[O],v.push(_?r:0),x.push(_?0:r),O++;for(s=O-1;s<c;s++)s>O-1&&(v.shift(),x.shift(),n=o,_=(o=o2(u[s]))>n,r=o*y[s],v.push(_?r:0),x.push(_?0:r)),i=o1(x),a=parseFloat((100-100/(1+o1(v)/i)).toFixed(h)),d.push([l[s],a]),g.push(l[s]),m.push(a);return{values:d,xData:g,yData:m}}},e.defaultOptions=oQ(oH.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",decimals:4}}),e}(oH);oJ(o3.prototype,{nameBase:"Money Flow Index"}),tc().registerSeriesType("mfi",o3);var o4=(T=function(t,e){return(T=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}T(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),o5=tc().seriesTypes.sma,o6=ts().extend,o9=ts().isArray,o8=ts().merge;function o7(t,e,o,n,r){var i=e[o-1][r]-e[o-n-1][r];return[t[o-1],i]}var nt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o4(e,t),e.prototype.getValues=function(t,e){var o,n,r=e.period,i=e.index,a=t.xData,s=t.yData,p=s?s.length:0,l=[],u=[],c=[];if(!(a.length<=r)&&o9(s[0])){for(o=r+1;o<p;o++)n=o7(a,s,o,r,i),l.push(n),u.push(n[0]),c.push(n[1]);return n=o7(a,s,o,r,i),l.push(n),u.push(n[0]),c.push(n[1]),{values:l,xData:u,yData:c}}},e.defaultOptions=o8(o5.defaultOptions,{params:{index:3}}),e}(o5);o6(nt.prototype,{nameBase:"Momentum"}),tc().registerSeriesType("momentum",nt);var ne=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),no=tc().seriesTypes.atr,nn=ts().merge,nr=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ne(e,t),e.prototype.getValues=function(e,o){var n=t.prototype.getValues.apply(this,arguments),r=n.values.length,i=e.yData,a=0,s=o.period-1;if(n){for(;a<r;a++)n.yData[a]=n.values[a][1]/i[s][3]*100,n.values[a][1]=n.yData[a],s++;return n}},e.defaultOptions=nn(no.defaultOptions,{tooltip:{valueSuffix:"%"}}),e}(no);tc().registerSeriesType("natr",nr);var ni=(j=function(t,e){return(j=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}j(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),na=tc().seriesTypes.sma,ns=ts().isNumber,np=ts().error,nl=ts().extend,nu=ts().merge,nc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ni(e,t),e.prototype.getValues=function(t,e){var o,n=t.chart.get(e.volumeSeriesID),r=t.xData,i=t.yData,a=[],s=[],p=[],l=!ns(i[0]),u=[],c=1,h=0,f=0,y=0,d=0;if(n)for(o=n.getColumn("y"),u=[r[0],h],y=l?i[0][3]:i[0],a.push(u),s.push(r[0]),p.push(u[1]);c<i.length;c++)f=(d=l?i[c][3]:i[c])>y?h+o[c]:d===y?h:h-o[c],u=[r[c],f],h=f,y=d,a.push(u),s.push(r[c]),p.push(u[1]);else{np("Series "+e.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,t.chart);return}return{values:a,xData:s,yData:p}},e.defaultOptions=nu(na.defaultOptions,{marker:{enabled:!1},params:{index:void 0,period:void 0,volumeSeriesID:"volume"},tooltip:{valueDecimals:0}}),e}(na);nl(nc.prototype,{nameComponents:void 0}),tc().registerSeriesType("obv",nc);var nh=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});function nf(t,e){var o,n=t.series.pointArrayMap,r=n.length;for(tc().seriesTypes.sma.prototype.pointClass.prototype[e].call(t);r--;)t[o="dataLabel"+n[r]]&&t[o].element&&t[o].destroy(),t[o]=null}var ny=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nh(e,t),e.prototype.destroyElements=function(){nf(this,"destroyElements")},e.prototype.destroy=function(){nf(this,"destroyElements")},e}(tc().seriesTypes.sma.prototype.pointClass),nd=(V=function(t,e){return(V=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}V(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ng=tc().seriesTypes.sma,nm=ts().merge,nv=ts().extend,nx=ts().defined,n_=ts().isArray,nO=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nd(e,t),e.prototype.toYData=function(t){return[t.P]},e.prototype.translate=function(){var e=this;t.prototype.translate.apply(e),e.points.forEach(function(t){e.pointArrayMap.forEach(function(o){nx(t[o])&&(t["plot"+o]=e.yAxis.toPixels(t[o],!0))})}),e.plotEndPoint=e.xAxis.toPixels(e.endPoint,!0)},e.prototype.getGraphPath=function(e){for(var o,n,r,i=this,a=[[],[],[],[],[],[],[],[],[]],s=i.pointArrayMap.length,p=i.plotEndPoint,l=[],u=e.length;u--;){for(r=0,n=e[u];r<s;r++)nx(n[o=i.pointArrayMap[r]])&&a[r].push({plotX:n.plotX,plotY:n["plot"+o],isNull:!1},{plotX:p,plotY:n["plot"+o],isNull:!1},{plotX:p,plotY:null,isNull:!0});p=n.plotX}return a.forEach(function(e){l=l.concat(t.prototype.getGraphPath.call(i,e))}),l},e.prototype.drawDataLabels=function(){var e,o,n,r,i=this,a=i.pointArrayMap;i.options.dataLabels.enabled&&(o=i.points.length,a.concat([!1]).forEach(function(s,p){for(r=o;r--;)n=i.points[r],s?(n.y=n[s],n.pivotLine=s,n.plotY=n["plot"+s],e=n["dataLabel"+s],p&&(n["dataLabel"+a[p-1]]=n.dataLabel),n.dataLabels||(n.dataLabels=[]),n.dataLabels[0]=n.dataLabel=e=e&&e.element?e:null):n["dataLabel"+a[p-1]]=n.dataLabel;t.prototype.drawDataLabels.call(i)}))},e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l=e.period,u=t.xData,c=t.yData,h=c?c.length:0,f=this[e.algorithm+"Placement"],y=[],d=[],g=[];if(!(u.length<l)&&n_(c[0])&&4===c[0].length){for(p=l+1;p<=h+l;p+=l)r=u.slice(p-l-1,p),i=c.slice(p-l-1,p),n=r.length,o=r[n-1],s=f(this.getPivotAndHLC(i)),a=y.push([o].concat(s)),d.push(o),g.push(y[a-1].slice(1));return this.endPoint=r[0]+(o-r[0])/n*l,{values:y,xData:d,yData:g}}},e.prototype.getPivotAndHLC=function(t){var e=t[t.length-1][3],o=-1/0,n=1/0;return t.forEach(function(t){o=Math.max(o,t[1]),n=Math.min(n,t[2])}),[(o+n+e)/3,o,n,e]},e.prototype.standardPlacement=function(t){var e=t[1]-t[2];return[null,null,t[0]+e,2*t[0]-t[2],t[0],2*t[0]-t[1],t[0]-e,null,null]},e.prototype.camarillaPlacement=function(t){var e=t[1]-t[2];return[t[3]+1.5*e,t[3]+1.25*e,t[3]+1.1666*e,t[3]+1.0833*e,t[0],t[3]-1.0833*e,t[3]-1.1666*e,t[3]-1.25*e,t[3]-1.5*e]},e.prototype.fibonacciPlacement=function(t){var e=t[1]-t[2];return[null,t[0]+e,t[0]+.618*e,t[0]+.382*e,t[0],t[0]-.382*e,t[0]-.618*e,t[0]-e,null]},e.defaultOptions=nm(ng.defaultOptions,{params:{index:void 0,period:28,algorithm:"standard"},marker:{enabled:!1},enableMouseTracking:!1,dataLabels:{enabled:!0,format:"{point.pivotLine}"},dataGrouping:{approximation:"averages"}}),e}(ng);nv(nO.prototype,{nameBase:"Pivot Points",pointArrayMap:["R4","R3","R2","R1","P","S1","S2","S3","S4"],pointValKey:"P",pointClass:ny}),tc().registerSeriesType("pivotpoints",nO);var nD=(E=function(t,e){return(E=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}E(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),nb=tc().seriesTypes.ema,nP=ts().correctFloat,nA=ts().extend,nS=ts().merge,nC=ts().error,nT=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nD(e,t),e.prototype.getValues=function(e,o){var n,r,i=o.periods,a=o.index,s=[],p=[],l=[];if(2!==i.length||i[1]<=i[0]){nC('Error: "PPO requires two periods. Notice, first period should be lower than the second one."');return}var u=t.prototype.getValues.call(this,e,{index:a,period:i[0]}),c=t.prototype.getValues.call(this,e,{index:a,period:i[1]});if(u&&c){var h=i[1]-i[0];for(r=0;r<c.yData.length;r++)n=nP((u.yData[r+h]-c.yData[r])/c.yData[r]*100),s.push([c.xData[r],n]),p.push(c.xData[r]),l.push(n);return{values:s,xData:p,yData:l}}},e.defaultOptions=nS(nb.defaultOptions,{params:{period:void 0,periods:[12,26]}}),e}(nb);nA(nT.prototype,{nameBase:"PPO",nameComponents:["periods"]}),tc().registerSeriesType("ppo",nT);var nw={getArrayExtremes:function(t,e,o){return t.reduce(function(t,n){return[Math.min(t[0],n[e]),Math.max(t[1],n[o])]},[Number.MAX_VALUE,-Number.MAX_VALUE])}},nj=["#2caffe","#544fc5","#00e272","#fe6a35","#6b8abc","#d568fb","#2ee0ca","#fa4b42","#feb56a","#91e8e1"],nM=(L=function(t,e){return(L=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}L(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),nV=tc().seriesTypes.sma,nE=ts().merge,nL=ts().extend,nk=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nM(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l=e.period,u=t.xData,c=t.yData,h=c?c.length:0,f=[],y=[],d=[];if(!(h<l)){for(p=l;p<=h;p++)i=u[p-1],a=c.slice(p-l,p),o=((n=(s=nw.getArrayExtremes(a,2,1))[1])+(r=s[0]))/2,f.push([i,n,o,r]),y.push(i),d.push([n,o,r]);return{values:f,xData:y,yData:d}}},e.defaultOptions=nE(nV.defaultOptions,{params:{index:void 0,period:20},lineWidth:1,topLine:{styles:{lineColor:"#00e272",lineWidth:1}},bottomLine:{styles:{lineColor:"#feb56a",lineWidth:1}},dataGrouping:{approximation:"averages"}}),e}(nV);nL(nk.prototype,{areaLinesNames:["top","bottom"],nameBase:"Price Channel",nameComponents:["period"],linesApiNames:["topLine","bottomLine"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),tJ.compose(nk),tc().registerSeriesType("pc",nk);var nI=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),nN=tc().seriesTypes.sma,nB=ts().extend,nG=ts().isArray,nW=ts().merge,nz=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nI(e,t),e.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options=nW({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)},e.prototype.getValues=function(e,o){var n,r,i,a,s,p,l,u,c=o.period,h=o.topBand,f=o.bottomBand,y=e.xData,d=e.yData,g=d?d.length:0,m=[],v=[],x=[];if(!(y.length<c)&&nG(d[0])&&4===d[0].length){for(u=c;u<=g;u++)s=y.slice(u-c,u),p=d.slice(u-c,u),a=(l=t.prototype.getValues.call(this,{xData:s,yData:p},o)).xData[0],r=(n=l.yData[0])*(1+h),i=n*(1-f),m.push([a,r,n,i]),v.push(a),x.push([r,n,i]);return{values:m,xData:v,yData:x}}},e.defaultOptions=nW(nN.defaultOptions,{marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},params:{period:20,topBand:.1,bottomBand:.1},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),e}(nN);nB(nz.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","topBand","bottomBand"],nameBase:"Price envelopes",pointArrayMap:["top","middle","bottom"],parallelArrays:["x","y","top","bottom"],pointValKey:"middle"}),tJ.compose(nz),tc().registerSeriesType("priceenvelopes",nz);var nY=(I=function(t,e){return(I=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}I(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),nF=tc().seriesTypes.sma,nX=ts().merge;function nR(t,e){return parseFloat(t.toFixed(e))}var nK=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.nameComponents=void 0,e}return nY(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l,u,c,h,f,y,d,g,m,v,x,_,O,D,b,P,A,S,C,T,w,j,M,V,E,L,k=t.xData,I=t.yData,N=e.maxAccelerationFactor,B=e.increment,G=e.initialAccelerationFactor,W=e.decimals,z=e.index,Y=[],F=[],X=[],R=e.initialAccelerationFactor,K=I[0][1],U=1,Z=I[0][2];if(!(z>=I.length)){for(L=0;L<z;L++)K=Math.max(I[L][1],K),Z=Math.min(I[L][2],nR(Z,W));for(b=I[L][1]>Z?1:-1,P=K-Z,A=(R=e.initialAccelerationFactor)*P,Y.push([k[z],Z]),F.push(k[z]),X.push(nR(Z,W)),L=z+1;L<I.length;L++)if(C=I[L-1][2],T=I[L-2][2],w=I[L-1][1],j=I[L-2][1],V=I[L][1],E=I[L][2],null!==T&&null!==j&&null!==C&&null!==w&&null!==V&&null!==E){o=b,n=U,r=Z,i=A,a=T,s=C,p=w,l=j,u=K,Z=o===n?1===o?r+i<Math.min(a,s)?r+i:Math.min(a,s):r+i>Math.max(l,p)?r+i:Math.max(l,p):u,x=b,_=K,M=1===x?V>_?V:_:E<_?E:_,O=U,D=Z,c=S=1===O&&E>D||-1===O&&V>D?1:-1,h=b,f=M,y=K,d=R,g=B,m=N,v=G,A=(R=c===h?1===c&&f>y||-1===c&&f<y?d===m?m:nR(d+g,2):d:v)*(P=M-Z),Y.push([k[L],nR(Z,W)]),F.push(k[L]),X.push(nR(Z,W)),U=b,b=S,K=M}return{values:Y,xData:F,yData:X}}},e.defaultOptions=nX(nF.defaultOptions,{lineWidth:0,marker:{enabled:!0},states:{hover:{lineWidthPlus:0}},params:{period:void 0,initialAccelerationFactor:.02,maxAccelerationFactor:.2,increment:.02,index:2,decimals:4}}),e}(nF);tc().registerSeriesType("psar",nK);var nU=(N=function(t,e){return(N=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}N(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),nZ=tc().seriesTypes.sma,nq=ts().isArray,nH=ts().merge,nJ=ts().extend,nQ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return nU(e,t),e.prototype.getValues=function(t,e){var o,n,r=e.period,i=t.xData,a=t.yData,s=a?a.length:0,p=[],l=[],u=[],c=-1;if(!(i.length<=r)){for(nq(a[0])&&(c=e.index),o=r;o<s;o++)n=function(t,e,o,n,r){var i,a;return a=r<0?(i=e[o-n])?(e[o]-i)/i*100:null:(i=e[o-n][r])?(e[o][r]-i)/i*100:null,[t[o],a]}(i,a,o,r,c),p.push(n),l.push(n[0]),u.push(n[1]);return{values:p,xData:l,yData:u}}},e.defaultOptions=nH(nZ.defaultOptions,{params:{index:3,period:9}}),e}(nZ);nJ(nQ.prototype,{nameBase:"Rate of Change"}),tc().registerSeriesType("roc",nQ);var n$=(B=function(t,e){return(B=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}B(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),n0=tc().seriesTypes.sma,n1=ts().isNumber,n2=ts().merge;function n3(t,e){return parseFloat(t.toFixed(e))}var n4=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n$(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p=e.period,l=t.xData,u=t.yData,c=u?u.length:0,h=e.decimals,f=[],y=[],d=[],g=0,m=0,v=e.index,x=1;if(!(l.length<p)){for(n1(u[0])?s=u:(v=Math.min(v,u[0].length-1),s=u.map(function(t){return t[v]}));x<p;)(n=n3(s[x]-s[x-1],h))>0?g+=n:m+=Math.abs(n),x++;for(r=n3(g/(p-1),h),i=n3(m/(p-1),h),a=x;a<c;a++)(n=n3(s[a]-s[a-1],h))>0?(g=n,m=0):(g=0,m=Math.abs(n)),r=n3((r*(p-1)+g)/p,h),o=0===(i=n3((i*(p-1)+m)/p,h))?100:0===r?0:n3(100-100/(1+r/i),h),f.push([l[a],o]),y.push(l[a]),d.push(o);return{values:f,xData:y,yData:d}}},e.defaultOptions=n2(n0.defaultOptions,{params:{decimals:4,index:3}}),e}(n0);tc().registerSeriesType("rsi",n4);var n5=(G=function(t,e){return(G=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}G(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),n6=tc().seriesTypes.sma,n9=ts().extend,n8=ts().isArray,n7=ts().merge,rt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n5(e,t),e.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options=n7({smoothedLine:{styles:{lineColor:this.color}}},this.options)},e.prototype.getValues=function(e,o){var n,r,i,a,s,p=o.periods[0],l=o.periods[1],u=e.xData,c=e.yData,h=c?c.length:0,f=[],y=[],d=[],g=null;if(!(h<p)&&n8(c[0])&&4===c[0].length){var m=!0,v=0;for(s=p-1;s<h;s++){if(n=c.slice(s-p+1,s+1),r=(a=nw.getArrayExtremes(n,2,1))[0],isNaN(i=(c[s][3]-r)/(a[1]-r)*100)&&m){v++;continue}m&&!isNaN(i)&&(m=!1);var x=y.push(u[s]);isNaN(i)?d.push([d[x-2]&&"number"==typeof d[x-2][0]?d[x-2][0]:null,null]):d.push([i,null]),s>=v+(p-1)+(l-1)&&(g=t.prototype.getValues.call(this,{xData:y.slice(-l),yData:d.slice(-l)},{period:l}).yData[0]),f.push([u[s],i,g]),d[x-1][1]=g}return{values:f,xData:y,yData:d}}},e.defaultOptions=n7(n6.defaultOptions,{params:{index:void 0,period:void 0,periods:[14,3]},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>%K: {point.y}<br/>%D: {point.smoothed}<br/>'},smoothedLine:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),e}(n6);n9(rt.prototype,{areaLinesNames:[],nameComponents:["periods"],nameBase:"Stochastic",pointArrayMap:["y","smoothed"],parallelArrays:["x","y","smoothed"],pointValKey:"y",linesApiNames:["smoothedLine"]}),tJ.compose(rt),tc().registerSeriesType("stochastic",rt);var re=(W=function(t,e){return(W=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}W(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ro=tc().seriesTypes,rn=ro.sma,rr=ro.stochastic,ri=ts().extend,ra=ts().merge,rs=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return re(e,t),e.prototype.getValues=function(e,o){var n=o.periods,r=t.prototype.getValues.call(this,e,o),i={values:[],xData:[],yData:[]};if(r){i.xData=r.xData.slice(n[1]-1);var a=r.yData.slice(n[1]-1),s=rn.prototype.getValues.call(this,{xData:i.xData,yData:a},{index:1,period:n[2]});if(s){for(var p=0,l=i.xData.length;p<l;p++)i.yData[p]=[a[p][1],s.yData[p-n[2]+1]||null],i.values[p]=[i.xData[p],a[p][1],s.yData[p-n[2]+1]||null];return i}}},e.defaultOptions=ra(rr.defaultOptions,{params:{periods:[14,3,3]}}),e}(rr);ri(rs.prototype,{nameBase:"Slow Stochastic"}),tc().registerSeriesType("slowstochastic",rs);var rp=(z=function(t,e){return(z=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}z(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),rl=tc().seriesTypes,ru=rl.atr,rc=rl.sma,rh=ts().addEvent,rf=ts().correctFloat,ry=ts().isArray,rd=ts().isNumber,rg=ts().extend,rm=ts().merge,rv=ts().objectEach;function rx(t,e){return{index:e,close:t.getColumn("close")[e],x:t.getColumn("x")[e]}}var r_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rp(e,t),e.prototype.init=function(){var e=this;t.prototype.init.apply(e,arguments);var o=rh(this.chart.constructor,"afterLinkSeries",function(){if(e.options){var t=e.options;t.cropThreshold=e.linkedParent.options.cropThreshold-(t.params.period-1)}o()},{order:1})},e.prototype.drawGraph=function(){for(var t,e,o,n,r,i,a,s,p,l=this,u=l.options,c=l.linkedParent,h=c.getColumn("x"),f=c?c.points:[],y=l.points,d=l.graph,g=f.length-y.length,m=g>0?g:0,v={options:{gapSize:u.gapSize}},x={top:[],bottom:[],intersect:[]},_={top:{styles:{lineWidth:u.lineWidth,lineColor:u.fallingTrendColor||u.color,dashStyle:u.dashStyle}},bottom:{styles:{lineWidth:u.lineWidth,lineColor:u.risingTrendColor||u.color,dashStyle:u.dashStyle}},intersect:u.changeTrendLine},O=y.length;O--;)t=y[O],e=y[O-1],o=f[O-1+m],n=f[O-2+m],r=f[O+m],i=f[O+m+1],a=t.options.color,s={x:t.x,plotX:t.plotX,plotY:t.plotY,isNull:!1},!n&&o&&rd(h[o.index-1])&&(n=rx(c,o.index-1)),!i&&r&&rd(h[r.index+1])&&(i=rx(c,r.index+1)),!o&&n&&rd(h[n.index+1])?o=rx(c,n.index+1):!o&&r&&rd(h[r.index-1])&&(o=rx(c,r.index-1)),t&&o&&r&&n&&t.x!==o.x&&(t.x===r.x?(n=o,o=r):t.x===n.x?(o=n,n={close:c.getColumn("close")[o.index-1],x:h[o.index-1]}):i&&t.x===i.x&&(o=i,n=r)),e&&n&&o?(p={x:e.x,plotX:e.plotX,plotY:e.plotY,isNull:!1},t.y>=o.close&&e.y>=n.close?(t.color=a||u.fallingTrendColor||u.color,x.top.push(s)):t.y<o.close&&e.y<n.close?(t.color=a||u.risingTrendColor||u.color,x.bottom.push(s)):(x.intersect.push(s),x.intersect.push(p),x.intersect.push(rm(p,{isNull:!0})),t.y>=o.close&&e.y<n.close?(t.color=a||u.fallingTrendColor||u.color,e.color=a||u.risingTrendColor||u.color,x.top.push(s),x.top.push(rm(p,{isNull:!0}))):t.y<o.close&&e.y>=n.close&&(t.color=a||u.risingTrendColor||u.color,e.color=a||u.fallingTrendColor||u.color,x.bottom.push(s),x.bottom.push(rm(p,{isNull:!0}))))):o&&(t.y>=o.close?(t.color=a||u.fallingTrendColor||u.color,x.top.push(s)):(t.color=a||u.risingTrendColor||u.color,x.bottom.push(s)));rv(x,function(t,e){l.points=t,l.options=rm(_[e].styles,v),l.graph=l["graph"+e+"Line"],rc.prototype.drawGraph.call(l),l["graph"+e+"Line"]=l.graph}),l.points=y,l.options=u,l.graph=d},e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l,u,c=e.period,h=e.multiplier,f=t.xData,y=t.yData,d=[],g=[],m=[],v=0===c?0:c-1,x=[],_=[],O=[];if(!(f.length<=c)&&ry(y[0])&&4===y[0].length&&!(c<0)){for(u=0,O=ru.prototype.getValues.call(this,t,{period:c}).yData;u<O.length;u++)l=y[v+u],p=y[v+u-1]||[],i=x[u-1],a=_[u-1],s=m[u-1],0===u&&(i=a=s=0),o=rf((l[1]+l[2])/2+h*O[u]),n=rf((l[1]+l[2])/2-h*O[u]),o<i||p[3]>i?x[u]=o:x[u]=i,n>a||p[3]<a?_[u]=n:_[u]=a,s===i&&l[3]<x[u]||s===a&&l[3]<_[u]?r=x[u]:(s===i&&l[3]>x[u]||s===a&&l[3]>_[u])&&(r=_[u]),d.push([f[v+u],r]),g.push(f[v+u]),m.push(r);return{values:d,xData:g,yData:m}}},e.defaultOptions=rm(rc.defaultOptions,{params:{index:void 0,multiplier:3,period:10},risingTrendColor:"#06b535",fallingTrendColor:"#f21313",changeTrendLine:{styles:{lineWidth:1,lineColor:"#333333",dashStyle:"LongDash"}}}),e}(rc);rg(r_.prototype,{nameBase:"Supertrend",nameComponents:["multiplier","period"]}),tc().registerSeriesType("supertrend",r_);var rO=(Y=function(t,e){return(Y=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}Y(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),rD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rO(e,t),e.prototype.destroy=function(){this.negativeGraphic&&(this.negativeGraphic=this.negativeGraphic.destroy()),t.prototype.destroy.apply(this,arguments)},e}(tc().seriesTypes.sma.prototype.pointClass),rb=(F=function(t,e){return(F=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}F(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),rP=ts().animObject,rA=ts().noop,rS=tc().seriesTypes,rC=rS.column.prototype,rT=rS.sma,rw=ts().addEvent,rj=ts().arrayMax,rM=ts().arrayMin,rV=ts().correctFloat,rE=ts().defined,rL=ts().error,rk=ts().extend,rI=ts().isArray,rN=ts().merge,rB=Math.abs,rG=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rb(e,t),e.prototype.init=function(e,o){var n=this;delete o.data,t.prototype.init.apply(n,arguments);var r=rw(this.chart.constructor,"afterLinkSeries",function(){if(n.options){var t=n.options.params,o=n.linkedParent,i=e.get(t.volumeSeriesID);n.addCustomEvents(o,i)}r()},{order:1});return n},e.prototype.addCustomEvents=function(t,e){var o=this,n=function(){o.chart.redraw(),o.setData([]),o.zoneStarts=[],o.zoneLinesSVG&&(o.zoneLinesSVG=o.zoneLinesSVG.destroy())};return o.dataEventsToUnbind.push(rw(t,"remove",function(){n()})),e&&o.dataEventsToUnbind.push(rw(e,"remove",function(){n()})),o},e.prototype.animate=function(t){var e=this,o=e.chart.inverted,n=e.group,r={};if(!t&&n){var i=o?e.yAxis.top:e.xAxis.left;o?(n["forceAnimate:translateY"]=!0,r.translateY=i):(n["forceAnimate:translateX"]=!0,r.translateX=i),n.animate(r,rk(rP(e.options.animation),{step:function(t,o){e.group.attr({scaleX:Math.max(.001,o.pos)})}}))}},e.prototype.drawPoints=function(){this.options.volumeDivision.enabled&&(this.posNegVolume(!0,!0),rC.drawPoints.apply(this,arguments),this.posNegVolume(!1,!1)),rC.drawPoints.apply(this,arguments)},e.prototype.posNegVolume=function(t,e){var o,n,r,i,a=e?["positive","negative"]:["negative","positive"],s=this.options.volumeDivision,p=this.points.length,l=[],u=[],c=0;for(t?(this.posWidths=l,this.negWidths=u):(l=this.posWidths,u=this.negWidths);c<p;c++)(i=this.points[c])[a[0]+"Graphic"]=i.graphic,i.graphic=i[a[1]+"Graphic"],t&&(o=i.shapeArgs.width,(r=(n=this.priceZones[c]).wholeVolumeData)?(l.push(o/r*n.positiveVolumeData),u.push(o/r*n.negativeVolumeData)):(l.push(0),u.push(0))),i.color=e?s.styles.positiveColor:s.styles.negativeColor,i.shapeArgs.width=e?this.posWidths[c]:this.negWidths[c],i.shapeArgs.x=e?i.shapeArgs.x:this.posWidths[c]},e.prototype.translate=function(){var t,e,o,n,r,i,a,s,p,l,u=this,c=u.options,h=u.chart,f=u.yAxis,y=f.min,d=u.options.zoneLines,g=u.priceZones,m=0;rC.translate.apply(u);var v=u.points;v.length&&(a=c.pointPadding<.5?c.pointPadding:.1,t=rj(u.volumeDataArray),e=h.plotWidth/2,s=h.plotTop,o=rB(f.toPixels(y)-f.toPixels(y+u.rangeStep)),r=rB(f.toPixels(y)-f.toPixels(y+u.rangeStep)),a&&(n=rB(o*(1-2*a)),m=rB((o-n)/2),o=rB(n)),v.forEach(function(n,a){p=n.barX=n.plotX=0,l=n.plotY=f.toPixels(g[a].start)-s-(f.reversed?o-r:o)-m,n.pointWidth=i=rV(e*g[a].wholeVolumeData/t),n.shapeArgs=u.crispCol.apply(u,[p,l,i,o]),n.volumeNeg=g[a].negativeVolumeData,n.volumePos=g[a].positiveVolumeData,n.volumeAll=g[a].wholeVolumeData}),d.enabled&&u.drawZones(h,f,u.zoneStarts,d.styles))},e.prototype.getExtremes=function(){var e,o=this.options.compare,n=this.options.cumulative;return this.options.compare?(this.options.compare=void 0,e=t.prototype.getExtremes.call(this),this.options.compare=o):this.options.cumulative?(this.options.cumulative=!1,e=t.prototype.getExtremes.call(this),this.options.cumulative=n):e=t.prototype.getExtremes.call(this),e},e.prototype.getValues=function(t,e){var o=t.getColumn("x",!0),n=t.processedYData,r=this.chart,i=e.ranges,a=[],s=[],p=[],l=r.get(e.volumeSeriesID);if(!t.chart){rL("Base series not found! In case it has been removed, add a new one.",!0,r);return}if(!l||!l.getColumn("x",!0).length){var u=l&&!l.getColumn("x",!0).length?" does not contain any data.":" not found! Check `volumeSeriesID`.";rL("Series "+e.volumeSeriesID+u,!0,r);return}var c=rI(n[0]);if(c&&4!==n[0].length){rL("Type of "+t.name+" series is different than line, OHLC or candlestick.",!0,r);return}return(this.priceZones=this.specifyZones(c,o,n,i,l)).forEach(function(t,e){a.push([t.x,t.end]),s.push(a[e][0]),p.push(a[e][1])}),{values:a,xData:s,yData:p}},e.prototype.specifyZones=function(t,e,o,n,r){var i=!!t&&function(t){for(var e,o=t.length,n=t[0][3],r=n,i=1;i<o;i++)(e=t[i][3])<n&&(n=e),e>r&&(r=e);return{min:n,max:r}}(o),a=this.zoneStarts=[],s=[],p=i?i.min:rM(o),l=i?i.max:rj(o),u=0,c=1,h=this.linkedParent;if(!this.options.compareToMain&&h.dataModify&&(p=h.dataModify.modifyValue(p),l=h.dataModify.modifyValue(l)),!rE(p)||!rE(l))return this.points.length&&(this.setData([]),this.zoneStarts=[],this.zoneLinesSVG&&(this.zoneLinesSVG=this.zoneLinesSVG.destroy())),[];var f=this.rangeStep=rV(l-p)/n;for(a.push(p);u<n-1;u++)a.push(rV(a[u]+f));a.push(l);for(var y=a.length;c<y;c++)s.push({index:c-1,x:e[0],start:a[c-1],end:a[c]});return this.volumePerZone(t,s,r,e,o)},e.prototype.volumePerZone=function(t,e,o,n,r){var i,a,s,p,l,u=this,c=o.getColumn("x",!0),h=o.getColumn("y",!0),f=e.length-1,y=r.length,d=h.length;return rB(y-d)&&(n[0]!==c[0]&&h.unshift(0),n[y-1]!==c[d-1]&&h.push(0)),u.volumeDataArray=[],e.forEach(function(e){for(l=0,e.wholeVolumeData=0,e.positiveVolumeData=0,e.negativeVolumeData=0;l<y;l++){a=!1,s=!1,p=t?r[l][3]:r[l],i=l?t?r[l-1][3]:r[l-1]:p;var o=u.linkedParent;!u.options.compareToMain&&o.dataModify&&(p=o.dataModify.modifyValue(p),i=o.dataModify.modifyValue(i)),p<=e.start&&0===e.index&&(a=!0),p>=e.end&&e.index===f&&(s=!0),(p>e.start||a)&&(p<e.end||s)&&(e.wholeVolumeData+=h[l],i>p?e.negativeVolumeData+=h[l]:e.positiveVolumeData+=h[l])}u.volumeDataArray.push(e.wholeVolumeData)}),e},e.prototype.drawZones=function(t,e,o,n){var r,i=t.renderer,a=t.plotWidth,s=t.plotTop,p=this.zoneLinesSVG,l=[];o.forEach(function(o){r=e.toPixels(o)-s,l=l.concat(t.renderer.crispLine([["M",0,r],["L",a,r]],n.lineWidth))}),p?p.animate({d:l}):p=this.zoneLinesSVG=i.path(l).attr({"stroke-width":n.lineWidth,stroke:n.color,dashstyle:n.dashStyle,zIndex:this.group.zIndex+.1}).add(this.group)},e.defaultOptions=rN(rT.defaultOptions,{params:{index:void 0,period:void 0,ranges:12,volumeSeriesID:"volume"},zoneLines:{enabled:!0,styles:{color:"#0A9AC9",dashStyle:"LongDash",lineWidth:1}},volumeDivision:{enabled:!0,styles:{positiveColor:"rgba(144, 237, 125, 0.8)",negativeColor:"rgba(244, 91, 91, 0.8)"}},animationLimit:1e3,enableMouseTracking:!1,pointPadding:0,zIndex:-1,crisp:!0,dataGrouping:{enabled:!1},dataLabels:{align:"left",allowOverlap:!0,enabled:!0,format:"P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}",padding:0,style:{fontSize:"0.5em"},verticalAlign:"top"}}),e}(rT);rk(rG.prototype,{nameBase:"Volume by Price",nameComponents:["ranges"],calculateOn:{chart:"render",xAxis:"afterSetExtremes"},pointClass:rD,markerAttribs:rA,drawGraph:rA,getColumnMetrics:rC.getColumnMetrics,crispCol:rC.crispCol}),tc().registerSeriesType("vbp",rG);var rW=(X=function(t,e){return(X=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}X(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),rz=tc().seriesTypes.sma,rY=ts().error,rF=ts().isArray,rX=ts().merge,rR=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rW(e,t),e.prototype.getValues=function(t,e){var o,n=t.chart,r=t.xData,i=t.yData,a=e.period,s=!0;if(!(o=n.get(e.volumeSeriesID))){rY("Series "+e.volumeSeriesID+" not found! Check `volumeSeriesID`.",!0,n);return}return rF(i[0])||(s=!1),this.calculateVWAPValues(s,r,i,o,a)},e.prototype.calculateVWAPValues=function(t,e,o,n,r){var i,a,s,p,l,u,c=n.getColumn("y"),h=c.length,f=e.length,y=[],d=[],g=[],m=[],v=[];for(l=0,i=f<=h?f:h,u=0;l<i;l++)a=(t?(o[l][1]+o[l][2]+o[l][3])/3:o[l])*c[l],s=u?y[l-1]+a:a,p=u?d[l-1]+c[l]:c[l],y.push(s),d.push(p),v.push([e[l],s/p]),g.push(v[l][0]),m.push(v[l][1]),++u===r&&(u=0);return{values:v,xData:g,yData:m}},e.defaultOptions=rX(rz.defaultOptions,{params:{index:void 0,period:30,volumeSeriesID:"volume"}}),e}(rz);tc().registerSeriesType("vwap",rR);var rK=(R=function(t,e){return(R=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}R(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),rU=tc().seriesTypes.sma,rZ=ts().extend,rq=ts().isArray,rH=ts().merge,rJ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rK(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p=e.period,l=t.xData,u=t.yData,c=u?u.length:0,h=[],f=[],y=[];if(!(l.length<p)&&rq(u[0])&&4===u[0].length){for(s=p-1;s<c;s++)o=u.slice(s-p+1,s+1),a=(n=nw.getArrayExtremes(o,2,1))[0],r=-(((i=n[1])-u[s][3])/(i-a)*100),l[s]&&(h.push([l[s],r]),f.push(l[s]),y.push(r));return{values:h,xData:f,yData:y}}},e.defaultOptions=rH(rU.defaultOptions,{params:{index:void 0,period:14}}),e}(rU);rZ(rJ.prototype,{nameBase:"Williams %R"}),tc().registerSeriesType("williamsr",rJ);var rQ=(K=function(t,e){return(K=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}K(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),r$=tc().seriesTypes.sma,r0=ts().isArray,r1=ts().merge;function r2(t,e,o,n,r){var i=e[n],a=r<0?o[n]:o[n][r];t.push([i,a])}function r3(t,e,o,n){var r=t.length,i=t.reduce(function(t,e,o){return[null,t[1]+e[1]*(o+1)]})[1]/((r+1)/2*r),a=e[n-1];return t.shift(),[a,i]}var r4=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rQ(e,t),e.prototype.getValues=function(t,e){var o,n,r=e.period,i=t.xData,a=t.yData,s=a?a.length:0,p=i[0],l=[],u=[],c=[],h=1,f=-1,y=a[0];if(!(i.length<r)){r0(a[0])&&(f=e.index,y=a[0][f]);for(var d=[[p,y]];h!==r;)r2(d,i,a,h,f),h++;for(o=h;o<s;o++)n=r3(d,i,a,o),l.push(n),u.push(n[0]),c.push(n[1]),r2(d,i,a,o,f);return n=r3(d,i,a,o),l.push(n),u.push(n[0]),c.push(n[1]),{values:l,xData:u,yData:c}}},e.defaultOptions=r1(r$.defaultOptions,{params:{index:3,period:9}}),e}(r$);tc().registerSeriesType("wma",r4);var r5=(U=function(t,e){return(U=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}U(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),r6=tc().seriesTypes.sma,r9=ts().merge,r8=ts().extend,r7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r5(e,t),e.prototype.getValues=function(t,e){var o,n,r,i,a=e.lowIndex,s=e.highIndex,p=e.deviation/100,l={low:1+p,high:1-p},u=t.xData,c=t.yData,h=c?c.length:0,f=[],y=[],d=[],g=!1,m=!1;if(u&&!(u.length<=1)&&(!h||void 0!==c[0][a]&&void 0!==c[0][s])){var v=c[0][a],x=c[0][s];for(o=1;o<h;o++)c[o][a]<=x*l.high?(f.push([u[0],x]),r=[u[o],c[o][a]],i=!0,g=!0):c[o][s]>=v*l.low&&(f.push([u[0],v]),r=[u[o],c[o][s]],i=!1,g=!0),g&&(y.push(f[0][0]),d.push(f[0][1]),n=o++,o=h);for(o=n;o<h;o++)i?(c[o][a]<=r[1]&&(r=[u[o],c[o][a]]),c[o][s]>=r[1]*l.low&&(m=s)):(c[o][s]>=r[1]&&(r=[u[o],c[o][s]]),c[o][a]<=r[1]*l.high&&(m=a)),!1!==m&&(f.push(r),y.push(r[0]),d.push(r[1]),r=[u[o],c[o][m]],i=!i,m=!1);var _=f.length;return 0!==_&&f[_-1][0]<u[h-1]&&(f.push(r),y.push(r[0]),d.push(r[1])),{values:f,xData:y,yData:d}}},e.defaultOptions=r9(r6.defaultOptions,{params:{index:void 0,period:void 0,lowIndex:2,highIndex:1,deviation:1}}),e}(r6);r8(r7.prototype,{nameComponents:["deviation"],nameSuffixes:["%"],nameBase:"Zig Zag"}),tc().registerSeriesType("zigzag",r7);var it=(Z=function(t,e){return(Z=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}Z(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ie=tc().seriesTypes.sma,io=ts().isArray,ir=ts().extend,ii=ts().merge,ia=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return it(e,t),e.prototype.getRegressionLineParameters=function(t,e){var o,n,r=this.options.params.index,i=function(t,e){return io(t)?t[e]:t},a=t.reduce(function(t,e){return e+t},0),s=e.reduce(function(t,e){return i(e,r)+t},0),p=a/t.length,l=s/e.length,u=0,c=0;for(n=0;n<t.length;n++)u+=(o=t[n]-p)*(i(e[n],r)-l),c+=Math.pow(o,2);var h=c?u/c:0;return{slope:h,intercept:l-h*p}},e.prototype.getEndPointY=function(t,e){return t.slope*e+t.intercept},e.prototype.transformXData=function(t,e){var o=t[0];return t.map(function(t){return(t-o)/e})},e.prototype.findClosestDistance=function(t){var e,o,n;for(n=1;n<t.length-1;n++)(e=t[n]-t[n-1])>0&&(void 0===o||e<o)&&(o=e);return o},e.prototype.getValues=function(t,e){var o,n,r,i,a,s,p,l,u,c=t.xData,h=t.yData,f=e.period,y={xData:[],yData:[],values:[]},d=this.options.params.xAxisUnit||this.findClosestDistance(c);for(n=f-1;n<=c.length-1;n++)r=n-f+1,i=n+1,a=c[n],p=c.slice(r,i),l=h.slice(r,i),u=this.transformXData(p,d),o=this.getRegressionLineParameters(u,l),s=this.getEndPointY(o,u[u.length-1]),y.values.push({regressionLineParameters:o,x:a,y:s}),io(y.xData)&&y.xData.push(a),io(y.yData)&&y.yData.push(s);return y},e.defaultOptions=ii(ie.defaultOptions,{params:{xAxisUnit:null},tooltip:{valueDecimals:4}}),e}(ie);ir(ia.prototype,{nameBase:"Linear Regression Indicator"}),tc().registerSeriesType("linearRegression",ia);var is=(q=function(t,e){return(q=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}q(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ip=tc().seriesTypes.linearRegression,il=ts().extend,iu=ts().merge,ic=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return is(e,t),e.prototype.getEndPointY=function(t){return t.slope},e.defaultOptions=iu(ip.defaultOptions),e}(ip);il(ic.prototype,{nameBase:"Linear Regression Slope Indicator"}),tc().registerSeriesType("linearRegressionSlope",ic);var ih=(H=function(t,e){return(H=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}H(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),iy=tc().seriesTypes.linearRegression,id=ts().extend,ig=ts().merge,im=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ih(e,t),e.prototype.getEndPointY=function(t){return t.intercept},e.defaultOptions=ig(iy.defaultOptions),e}(iy);id(im.prototype,{nameBase:"Linear Regression Intercept Indicator"}),tc().registerSeriesType("linearRegressionIntercept",im);var iv=(J=function(t,e){return(J=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}J(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ix=tc().seriesTypes.linearRegression,i_=ts().extend,iO=ts().merge,iD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return iv(e,t),e.prototype.slopeToAngle=function(t){return 180/Math.PI*Math.atan(t)},e.prototype.getEndPointY=function(t){return this.slopeToAngle(t.slope)},e.defaultOptions=iO(ix.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span>{series.name}: <b>{point.y}\xb0</b><br/>'}}),e}(ix);i_(iD.prototype,{nameBase:"Linear Regression Angle Indicator"}),tc().registerSeriesType("linearRegressionAngle",iD);var ib=(Q=function(t,e){return(Q=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}Q(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),iP=tc().seriesTypes.sma,iA=ts().correctFloat,iS=ts().extend,iC=ts().merge,iT=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ib(e,t),e.prototype.getValues=function(e,o){var n,r,i,a,s,p,l,u,c,h,f,y,d,g=o.period,m=o.factor,v=o.index,x=e.xData,_=e.yData,O=_?_.length:0,D=[],b=[],P=[],A=[],S=[];if(!(O<g)){for(d=0;d<=O;d++)d<O&&(n=_[d][2],l=iA((r=_[d][1])-n)/(iA(r+n)/2)*1e3*m,D.push(_[d][1]*iA(1+2*l)),b.push(_[d][2]*iA(1-2*l))),d>=g&&(f=x.slice(d-g,d),y=_.slice(d-g,d),c=t.prototype.getValues.call(this,{xData:f,yData:D.slice(d-g,d)},{period:g}),h=t.prototype.getValues.call(this,{xData:f,yData:b.slice(d-g,d)},{period:g}),p=(u=t.prototype.getValues.call(this,{xData:f,yData:y},{period:g,index:v})).xData[0],a=c.yData[0],s=h.yData[0],i=u.yData[0],P.push([p,a,i,s]),A.push(p),S.push([a,i,s]));return{values:P,xData:A,yData:S}}},e.defaultOptions=iC(iP.defaultOptions,{params:{period:20,factor:.001,index:3},lineWidth:1,topLine:{styles:{lineWidth:1}},bottomLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),e}(iP);iS(iT.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameBase:"Acceleration Bands",nameComponents:["period","factor"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),tJ.compose(iT),tc().registerSeriesType("abands",iT);var iw=($=function(t,e){return($=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}$(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),ij=tc().seriesTypes.sma,iM=ts().extend,iV=ts().merge,iE=ts().isArray,iL=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.updateAllPoints=!0,e}return iw(e,t),e.prototype.getValues=function(t,e){for(var o=t.xData,n=t.yData,r=[],i=[],a=[],s=[],p=e.index,l=0,u=0,c=0,h=0,f=0,y=0;y<o.length;y++)(0===y||o[y]!==o[y-1])&&f++,r.push(f);for(var y=0;y<r.length;y++)c+=r[y],h+=iE(n[y])?n[y][p]:n[y];for(var d=c/r.length,g=h/n.length,y=0;y<r.length;y++){var m=iE(n[y])?n[y][p]:n[y];l+=(r[y]-d)*(m-g),u+=Math.pow(r[y]-d,2)}for(var y=0;y<r.length;y++)if(o[y]!==a[a.length-1]){var v=o[y],m=g+l/u*(r[y]-d);i.push([v,m]),a.push(v),s.push(m)}return{xData:a,yData:s,values:i}},e.defaultOptions=iV(ij.defaultOptions,{params:{period:void 0,index:3}}),e}(ij);iM(iL.prototype,{nameBase:"Trendline",nameComponents:void 0}),tc().registerSeriesType("trendline",iL);var ik=(tt=function(t,e){return(tt=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}tt(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),iI=tc().seriesTypes.sma,iN=ts().correctFloat,iB=ts().defined,iG=ts().extend,iW=ts().isArray,iz=ts().merge,iY=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ik(e,t),e.prototype.init=function(){var t=arguments,e=t[1].params,o=e&&e.average?e.average:void 0;this.averageIndicator=tc().seriesTypes[o]||iI,this.averageIndicator.prototype.init.apply(this,t)},e.prototype.calculateDisparityIndex=function(t,e){return iN(t-e)/e*100},e.prototype.getValues=function(t,e){var o=e.index,n=t.xData,r=t.yData,i=r?r.length:0,a=[],s=[],p=[],l=this.averageIndicator,u=iW(r[0]),c=l.prototype.getValues(t,e),h=c.yData,f=n.indexOf(c.xData[0]);if(h&&0!==h.length&&iB(o)&&!(r.length<=f)){for(var y=f;y<i;y++){var d=this.calculateDisparityIndex(u?r[y][o]:r[y],h[y-f]);a.push([n[y],d]),s.push(n[y]),p.push(d)}return{values:a,xData:s,yData:p}}},e.defaultOptions=iz(iI.defaultOptions,{params:{average:"sma",index:3},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),e}(iI);iG(iY.prototype,{nameBase:"Disparity Index",nameComponents:["period","average"]}),tc().registerSeriesType("disparityindex",iY);var iF=ts();iF.MultipleLinesComposition=iF.MultipleLinesComposition||tJ;var iX=ts();return ti.default}()});