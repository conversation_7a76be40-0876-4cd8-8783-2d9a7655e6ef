!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").Color,require("highcharts").SeriesRegistry,require("highcharts").RendererRegistry,require("highcharts").Series,require("highcharts").StackItem,require("highcharts").Axis,require("highcharts").Series.types.scatter):"function"==typeof define&&define.amd?define("highcharts/highcharts-3d",[["highcharts/highcharts"],["highcharts/highcharts","Color"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","RendererRegistry"],["highcharts/highcharts","Series"],["highcharts/highcharts","StackItem"],["highcharts/highcharts","Axis"],["highcharts/highcharts","Series","types","scatter"]],e):"object"==typeof exports?exports["highcharts/highcharts-3d"]=e(require("highcharts"),require("highcharts").Color,require("highcharts").SeriesRegistry,require("highcharts").RendererRegistry,require("highcharts").Series,require("highcharts").StackItem,require("highcharts").Axis,require("highcharts").Series.types.scatter):t.Highcharts=e(t.Highcharts,t.Highcharts.Color,t.Highcharts.SeriesRegistry,t.Highcharts.RendererRegistry,t.Highcharts.Series,t.Highcharts.StackItem,t.Highcharts.Axis,t.Highcharts.Series.types.scatter)}(this,function(t,e,i,r,s,o,a,n){return function(){"use strict";var h,l,p,c,d,x,y,f,u,v={184:function(t){t.exports=o},512:function(t){t.exports=i},532:function(t){t.exports=a},608:function(t){t.exports=r},620:function(t){t.exports=e},632:function(t){t.exports=n},820:function(t){t.exports=s},944:function(e){e.exports=t}},g={};function z(t){var e=g[t];if(void 0!==e)return e.exports;var i=g[t]={exports:{}};return v[t](i,i.exports,z),i.exports}z.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return z.d(e,{a:e}),e},z.d=function(t,e){for(var i in e)z.o(e,i)&&!z.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},z.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var b={};z.d(b,{default:function(){return eC}});var m=z(944),M=z.n(m),P=z(620),A=z.n(P),S=M().deg2rad,k=M().pick;function w(t,e,i,r){var s=e.options.chart.options3d,o=k(r,!!i&&e.inverted),a={x:e.plotWidth/2,y:e.plotHeight/2,z:s.depth/2,vd:k(s.depth,1)*k(s.viewDistance,0)},n=e.scale3d||1,h=S*s.beta*(o?-1:1),l=S*s.alpha*(o?-1:1),p={cosA:Math.cos(l),cosB:Math.cos(-h),sinA:Math.sin(l),sinB:Math.sin(-h)};return i||(a.x+=e.plotLeft,a.y+=e.plotTop),t.map(function(t){var e,i,r,s=(e=(o?t.y:t.x)-a.x,i=(o?t.x:t.y)-a.y,r=(t.z||0)-a.z,{x:p.cosB*e-p.sinB*r,y:-p.sinA*p.sinB*e+p.cosA*i-p.cosB*p.sinA*r,z:p.cosA*p.sinB*e+p.sinA*i+p.cosA*p.cosB*r}),h=O(s,a,a.vd);return h.x=h.x*n+a.x,h.y=h.y*n+a.y,h.z=s.z*n+a.z,{x:o?h.y:h.x,y:o?h.x:h.y,z:h.z}})}function O(t,e,i){var r=i>0&&i<Number.POSITIVE_INFINITY?i/(t.z+e.z+i):1;return{x:t.x*r,y:t.y*r}}function _(t){var e,i,r=0;for(e=0;e<t.length;e++)i=(e+1)%t.length,r+=t[e].x*t[i].y-t[i].x*t[e].y;return r/2}var I=function(t,e){var i=e.options.chart.options3d,r={x:e.plotWidth/2,y:e.plotHeight/2,z:k(i.depth,1)*k(i.viewDistance,0)+i.depth};return Math.sqrt(Math.pow(r.x-k(t.plotX,t.x),2)+Math.pow(r.y-k(t.plotY,t.y),2)+Math.pow(r.z-k(t.plotZ,t.z),2))},L=A().parse,D=M().defaultOptions,T=M().addEvent,X=M().isArray,j=M().merge,Y=M().pick,E=M().wrap;!function(t){function e(t){this.is3d()&&"scatter"===t.options.type&&(t.options.type="scatter3d")}function i(){if(this.chart3d&&this.is3d()){var t=this.renderer,e=this.options.chart.options3d,i=this.chart3d.get3dFrame(),r=this.plotLeft,s=this.plotLeft+this.plotWidth,o=this.plotTop,a=this.plotTop+this.plotHeight,n=e.depth,h=r-(i.left.visible?i.left.size:0),l=s+(i.right.visible?i.right.size:0),p=o-(i.top.visible?i.top.size:0),c=a+(i.bottom.visible?i.bottom.size:0),d=0-(i.front.visible?i.front.size:0),x=n+(i.back.visible?i.back.size:0),y=this.hasRendered?"animate":"attr";this.chart3d.frame3d=i,this.frameShapes||(this.frameShapes={bottom:t.polyhedron().add(),top:t.polyhedron().add(),left:t.polyhedron().add(),right:t.polyhedron().add(),back:t.polyhedron().add(),front:t.polyhedron().add()}),this.frameShapes.bottom[y]({class:"highcharts-3d-frame highcharts-3d-frame-bottom",zIndex:i.bottom.frontFacing?-1e3:1e3,faces:[{fill:L(i.bottom.color).brighten(.1).get(),vertexes:[{x:h,y:c,z:d},{x:l,y:c,z:d},{x:l,y:c,z:x},{x:h,y:c,z:x}],enabled:i.bottom.visible},{fill:L(i.bottom.color).brighten(.1).get(),vertexes:[{x:r,y:a,z:n},{x:s,y:a,z:n},{x:s,y:a,z:0},{x:r,y:a,z:0}],enabled:i.bottom.visible},{fill:L(i.bottom.color).brighten(-.1).get(),vertexes:[{x:h,y:c,z:d},{x:h,y:c,z:x},{x:r,y:a,z:n},{x:r,y:a,z:0}],enabled:i.bottom.visible&&!i.left.visible},{fill:L(i.bottom.color).brighten(-.1).get(),vertexes:[{x:l,y:c,z:x},{x:l,y:c,z:d},{x:s,y:a,z:0},{x:s,y:a,z:n}],enabled:i.bottom.visible&&!i.right.visible},{fill:L(i.bottom.color).get(),vertexes:[{x:l,y:c,z:d},{x:h,y:c,z:d},{x:r,y:a,z:0},{x:s,y:a,z:0}],enabled:i.bottom.visible&&!i.front.visible},{fill:L(i.bottom.color).get(),vertexes:[{x:h,y:c,z:x},{x:l,y:c,z:x},{x:s,y:a,z:n},{x:r,y:a,z:n}],enabled:i.bottom.visible&&!i.back.visible}]}),this.frameShapes.top[y]({class:"highcharts-3d-frame highcharts-3d-frame-top",zIndex:i.top.frontFacing?-1e3:1e3,faces:[{fill:L(i.top.color).brighten(.1).get(),vertexes:[{x:h,y:p,z:x},{x:l,y:p,z:x},{x:l,y:p,z:d},{x:h,y:p,z:d}],enabled:i.top.visible},{fill:L(i.top.color).brighten(.1).get(),vertexes:[{x:r,y:o,z:0},{x:s,y:o,z:0},{x:s,y:o,z:n},{x:r,y:o,z:n}],enabled:i.top.visible},{fill:L(i.top.color).brighten(-.1).get(),vertexes:[{x:h,y:p,z:x},{x:h,y:p,z:d},{x:r,y:o,z:0},{x:r,y:o,z:n}],enabled:i.top.visible&&!i.left.visible},{fill:L(i.top.color).brighten(-.1).get(),vertexes:[{x:l,y:p,z:d},{x:l,y:p,z:x},{x:s,y:o,z:n},{x:s,y:o,z:0}],enabled:i.top.visible&&!i.right.visible},{fill:L(i.top.color).get(),vertexes:[{x:h,y:p,z:d},{x:l,y:p,z:d},{x:s,y:o,z:0},{x:r,y:o,z:0}],enabled:i.top.visible&&!i.front.visible},{fill:L(i.top.color).get(),vertexes:[{x:l,y:p,z:x},{x:h,y:p,z:x},{x:r,y:o,z:n},{x:s,y:o,z:n}],enabled:i.top.visible&&!i.back.visible}]}),this.frameShapes.left[y]({class:"highcharts-3d-frame highcharts-3d-frame-left",zIndex:i.left.frontFacing?-1e3:1e3,faces:[{fill:L(i.left.color).brighten(.1).get(),vertexes:[{x:h,y:c,z:d},{x:r,y:a,z:0},{x:r,y:a,z:n},{x:h,y:c,z:x}],enabled:i.left.visible&&!i.bottom.visible},{fill:L(i.left.color).brighten(.1).get(),vertexes:[{x:h,y:p,z:x},{x:r,y:o,z:n},{x:r,y:o,z:0},{x:h,y:p,z:d}],enabled:i.left.visible&&!i.top.visible},{fill:L(i.left.color).brighten(-.1).get(),vertexes:[{x:h,y:c,z:x},{x:h,y:p,z:x},{x:h,y:p,z:d},{x:h,y:c,z:d}],enabled:i.left.visible},{fill:L(i.left.color).brighten(-.1).get(),vertexes:[{x:r,y:o,z:n},{x:r,y:a,z:n},{x:r,y:a,z:0},{x:r,y:o,z:0}],enabled:i.left.visible},{fill:L(i.left.color).get(),vertexes:[{x:h,y:c,z:d},{x:h,y:p,z:d},{x:r,y:o,z:0},{x:r,y:a,z:0}],enabled:i.left.visible&&!i.front.visible},{fill:L(i.left.color).get(),vertexes:[{x:h,y:p,z:x},{x:h,y:c,z:x},{x:r,y:a,z:n},{x:r,y:o,z:n}],enabled:i.left.visible&&!i.back.visible}]}),this.frameShapes.right[y]({class:"highcharts-3d-frame highcharts-3d-frame-right",zIndex:i.right.frontFacing?-1e3:1e3,faces:[{fill:L(i.right.color).brighten(.1).get(),vertexes:[{x:l,y:c,z:x},{x:s,y:a,z:n},{x:s,y:a,z:0},{x:l,y:c,z:d}],enabled:i.right.visible&&!i.bottom.visible},{fill:L(i.right.color).brighten(.1).get(),vertexes:[{x:l,y:p,z:d},{x:s,y:o,z:0},{x:s,y:o,z:n},{x:l,y:p,z:x}],enabled:i.right.visible&&!i.top.visible},{fill:L(i.right.color).brighten(-.1).get(),vertexes:[{x:s,y:o,z:0},{x:s,y:a,z:0},{x:s,y:a,z:n},{x:s,y:o,z:n}],enabled:i.right.visible},{fill:L(i.right.color).brighten(-.1).get(),vertexes:[{x:l,y:c,z:d},{x:l,y:p,z:d},{x:l,y:p,z:x},{x:l,y:c,z:x}],enabled:i.right.visible},{fill:L(i.right.color).get(),vertexes:[{x:l,y:p,z:d},{x:l,y:c,z:d},{x:s,y:a,z:0},{x:s,y:o,z:0}],enabled:i.right.visible&&!i.front.visible},{fill:L(i.right.color).get(),vertexes:[{x:l,y:c,z:x},{x:l,y:p,z:x},{x:s,y:o,z:n},{x:s,y:a,z:n}],enabled:i.right.visible&&!i.back.visible}]}),this.frameShapes.back[y]({class:"highcharts-3d-frame highcharts-3d-frame-back",zIndex:i.back.frontFacing?-1e3:1e3,faces:[{fill:L(i.back.color).brighten(.1).get(),vertexes:[{x:l,y:c,z:x},{x:h,y:c,z:x},{x:r,y:a,z:n},{x:s,y:a,z:n}],enabled:i.back.visible&&!i.bottom.visible},{fill:L(i.back.color).brighten(.1).get(),vertexes:[{x:h,y:p,z:x},{x:l,y:p,z:x},{x:s,y:o,z:n},{x:r,y:o,z:n}],enabled:i.back.visible&&!i.top.visible},{fill:L(i.back.color).brighten(-.1).get(),vertexes:[{x:h,y:c,z:x},{x:h,y:p,z:x},{x:r,y:o,z:n},{x:r,y:a,z:n}],enabled:i.back.visible&&!i.left.visible},{fill:L(i.back.color).brighten(-.1).get(),vertexes:[{x:l,y:p,z:x},{x:l,y:c,z:x},{x:s,y:a,z:n},{x:s,y:o,z:n}],enabled:i.back.visible&&!i.right.visible},{fill:L(i.back.color).get(),vertexes:[{x:r,y:o,z:n},{x:s,y:o,z:n},{x:s,y:a,z:n},{x:r,y:a,z:n}],enabled:i.back.visible},{fill:L(i.back.color).get(),vertexes:[{x:h,y:c,z:x},{x:l,y:c,z:x},{x:l,y:p,z:x},{x:h,y:p,z:x}],enabled:i.back.visible}]}),this.frameShapes.front[y]({class:"highcharts-3d-frame highcharts-3d-frame-front",zIndex:i.front.frontFacing?-1e3:1e3,faces:[{fill:L(i.front.color).brighten(.1).get(),vertexes:[{x:h,y:c,z:d},{x:l,y:c,z:d},{x:s,y:a,z:0},{x:r,y:a,z:0}],enabled:i.front.visible&&!i.bottom.visible},{fill:L(i.front.color).brighten(.1).get(),vertexes:[{x:l,y:p,z:d},{x:h,y:p,z:d},{x:r,y:o,z:0},{x:s,y:o,z:0}],enabled:i.front.visible&&!i.top.visible},{fill:L(i.front.color).brighten(-.1).get(),vertexes:[{x:h,y:p,z:d},{x:h,y:c,z:d},{x:r,y:a,z:0},{x:r,y:o,z:0}],enabled:i.front.visible&&!i.left.visible},{fill:L(i.front.color).brighten(-.1).get(),vertexes:[{x:l,y:c,z:d},{x:l,y:p,z:d},{x:s,y:o,z:0},{x:s,y:a,z:0}],enabled:i.front.visible&&!i.right.visible},{fill:L(i.front.color).get(),vertexes:[{x:s,y:o,z:0},{x:r,y:o,z:0},{x:r,y:a,z:0},{x:s,y:a,z:0}],enabled:i.front.visible},{fill:L(i.front.color).get(),vertexes:[{x:l,y:c,z:d},{x:h,y:c,z:d},{x:h,y:p,z:d},{x:l,y:p,z:d}],enabled:i.front.visible}]})}}function r(){this.styledMode&&[{name:"darker",slope:.6},{name:"brighter",slope:1.4}].forEach(function(t){this.renderer.definition({tagName:"filter",attributes:{id:"highcharts-"+t.name},children:[{tagName:"feComponentTransfer",children:[{tagName:"feFuncR",attributes:{type:"linear",slope:t.slope}},{tagName:"feFuncG",attributes:{type:"linear",slope:t.slope}},{tagName:"feFuncB",attributes:{type:"linear",slope:t.slope}}]}]})},this)}function s(){var t=this.options;this.is3d()&&(t.series||[]).forEach(function(e){"scatter"===(e.type||t.chart.type||t.chart.defaultSeriesType)&&(e.type="scatter3d")})}function o(){var t=this.options.chart.options3d;if(this.chart3d&&this.is3d()){t&&(t.alpha=t.alpha%360+(t.alpha>=0?0:360),t.beta=t.beta%360+(t.beta>=0?0:360));var e=this.inverted,i=this.clipBox,r=this.margin;i[e?"y":"x"]=-(r[3]||0),i[e?"x":"y"]=-(r[0]||0),i[e?"height":"width"]=this.chartWidth+(r[3]||0)+(r[1]||0),i[e?"width":"height"]=this.chartHeight+(r[0]||0)+(r[2]||0),this.scale3d=1,!0===t.fitToPlot&&(this.scale3d=this.chart3d.getScale(t.depth)),this.chart3d.frame3d=this.chart3d.get3dFrame()}}function a(){this.is3d()&&(this.isDirtyBox=!0)}function n(){this.chart3d&&this.is3d()&&(this.chart3d.frame3d=this.chart3d.get3dFrame())}function h(){this.chart3d||(this.chart3d=new d(this))}function l(t){return this.is3d()||t.apply(this,[].slice.call(arguments,1))}function p(t){var e,i=this.series.length;if(this.is3d())for(;i--;)(e=this.series[i]).translate(),e.render();else t.call(this)}function c(t){t.apply(this,[].slice.call(arguments,1)),this.is3d()&&(this.container.className+=" highcharts-3d-chart")}t.defaultOptions={chart:{options3d:{enabled:!1,alpha:0,beta:0,depth:100,fitToPlot:!0,viewDistance:25,axisLabelPosition:null,frame:{visible:"default",size:1,bottom:{},top:{},left:{},right:{},back:{},front:{}}}}},t.compose=function(d,x){var y=d.prototype,f=x.prototype;y.is3d=function(){var t;return!!(null===(t=this.options.chart.options3d)||void 0===t?void 0:t.enabled)},y.propsRequireDirtyBox.push("chart.options3d"),y.propsRequireUpdateSeries.push("chart.options3d"),f.matrixSetter=function(){var t;if(this.pos<1&&(X(this.start)||X(this.end))){var e=this.start||[1,0,0,1,0,0],i=this.end||[1,0,0,1,0,0];t=[];for(var r=0;r<6;r++)t.push(this.pos*i[r]+(1-this.pos)*e[r])}else t=this.end;this.elem.attr(this.prop,t,null,!0)},j(!0,D,t.defaultOptions),T(d,"init",h),T(d,"addSeries",e),T(d,"afterDrawChartBox",i),T(d,"afterGetContainer",r),T(d,"afterInit",s),T(d,"afterSetChartSize",o),T(d,"beforeRedraw",a),T(d,"beforeRender",n),E(y,"isInsidePlot",l),E(y,"renderSeries",p),E(y,"setClassName",c)};var d=function(){function t(t){this.chart=t}return t.prototype.get3dFrame=function(){var t=this.chart,e=t.options.chart.options3d,i=e.frame,r=t.plotLeft,s=t.plotLeft+t.plotWidth,o=t.plotTop,a=t.plotTop+t.plotHeight,n=e.depth,h=function(e){var i,r=_(w(e,t,void 0));return r>.5?1:r<-.5?-1:0},l=h([{x:r,y:a,z:n},{x:s,y:a,z:n},{x:s,y:a,z:0},{x:r,y:a,z:0}]),p=h([{x:r,y:o,z:0},{x:s,y:o,z:0},{x:s,y:o,z:n},{x:r,y:o,z:n}]),c=h([{x:r,y:o,z:0},{x:r,y:o,z:n},{x:r,y:a,z:n},{x:r,y:a,z:0}]),d=h([{x:s,y:o,z:n},{x:s,y:o,z:0},{x:s,y:a,z:0},{x:s,y:a,z:n}]),x=h([{x:r,y:a,z:0},{x:s,y:a,z:0},{x:s,y:o,z:0},{x:r,y:o,z:0}]),y=h([{x:r,y:o,z:n},{x:s,y:o,z:n},{x:s,y:a,z:n},{x:r,y:a,z:n}]),f=!1,u=!1,v=!1,g=!1;[].concat(t.xAxis,t.yAxis,t.zAxis).forEach(function(t){t&&(t.horiz?t.opposite?u=!0:f=!0:t.opposite?g=!0:v=!0)});var z=function(t,e,i){for(var r=["size","color","visible"],s={},o=0;o<r.length;o++)for(var a=r[o],n=0;n<t.length;n++)if("object"==typeof t[n]){var h=t[n][a];if(null!=h){s[a]=h;break}}var l=i;return!0===s.visible||!1===s.visible?l=s.visible:"auto"===s.visible&&(l=e>0),{size:Y(s.size,1),color:Y(s.color,"none"),frontFacing:e>0,visible:l}},b={axes:{},bottom:z([i.bottom,i.top,i],l,f),top:z([i.top,i.bottom,i],p,u),left:z([i.left,i.right,i.side,i],c,v),right:z([i.right,i.left,i.side,i],d,g),back:z([i.back,i.front,i],y,!0),front:z([i.front,i.back,i],x,!1)};if("auto"===e.axisLabelPosition){var m=function(t,e){return t.visible!==e.visible||t.visible&&e.visible&&t.frontFacing!==e.frontFacing},M=[];m(b.left,b.front)&&M.push({y:(o+a)/2,x:r,z:0,xDir:{x:1,y:0,z:0}}),m(b.left,b.back)&&M.push({y:(o+a)/2,x:r,z:n,xDir:{x:0,y:0,z:-1}}),m(b.right,b.front)&&M.push({y:(o+a)/2,x:s,z:0,xDir:{x:0,y:0,z:1}}),m(b.right,b.back)&&M.push({y:(o+a)/2,x:s,z:n,xDir:{x:-1,y:0,z:0}});var P=[];m(b.bottom,b.front)&&P.push({x:(r+s)/2,y:a,z:0,xDir:{x:1,y:0,z:0}}),m(b.bottom,b.back)&&P.push({x:(r+s)/2,y:a,z:n,xDir:{x:-1,y:0,z:0}});var A=[];m(b.top,b.front)&&A.push({x:(r+s)/2,y:o,z:0,xDir:{x:1,y:0,z:0}}),m(b.top,b.back)&&A.push({x:(r+s)/2,y:o,z:n,xDir:{x:-1,y:0,z:0}});var S=[];m(b.bottom,b.left)&&S.push({z:(0+n)/2,y:a,x:r,xDir:{x:0,y:0,z:-1}}),m(b.bottom,b.right)&&S.push({z:(0+n)/2,y:a,x:s,xDir:{x:0,y:0,z:1}});var k=[];m(b.top,b.left)&&k.push({z:(0+n)/2,y:o,x:r,xDir:{x:0,y:0,z:-1}}),m(b.top,b.right)&&k.push({z:(0+n)/2,y:o,x:s,xDir:{x:0,y:0,z:1}});var O=function(e,i,r){if(0===e.length)return null;if(1===e.length)return e[0];for(var s=w(e,t,!1),o=0,a=1;a<s.length;a++)r*s[a][i]>r*s[o][i]?o=a:r*s[a][i]==r*s[o][i]&&s[a].z<s[o].z&&(o=a);return e[o]};b.axes={y:{left:O(M,"x",-1),right:O(M,"x",1)},x:{top:O(A,"y",-1),bottom:O(P,"y",1)},z:{top:O(k,"y",-1),bottom:O(S,"y",1)}}}else b.axes={y:{left:{x:r,z:0,xDir:{x:1,y:0,z:0}},right:{x:s,z:0,xDir:{x:0,y:0,z:1}}},x:{top:{y:o,z:0,xDir:{x:1,y:0,z:0}},bottom:{y:a,z:0,xDir:{x:1,y:0,z:0}}},z:{top:{x:v?s:r,y:o,xDir:v?{x:0,y:0,z:1}:{x:0,y:0,z:-1}},bottom:{x:v?s:r,y:a,xDir:v?{x:0,y:0,z:1}:{x:0,y:0,z:-1}}}};return b},t.prototype.getScale=function(t){var e,i=this.chart,r=i.plotLeft,s=i.plotWidth+r,o=i.plotTop,a=i.plotHeight+o,n=r+i.plotWidth/2,h=o+i.plotHeight/2,l={minX:Number.MAX_VALUE,maxX:-Number.MAX_VALUE,minY:Number.MAX_VALUE,maxY:-Number.MAX_VALUE},p=1;return e=[{x:r,y:o,z:0},{x:r,y:o,z:t}],[0,1].forEach(function(t){e.push({x:s,y:e[t].y,z:e[t].z})}),[0,1,2,3].forEach(function(t){e.push({x:e[t].x,y:a,z:e[t].z})}),(e=w(e,i,!1)).forEach(function(t){l.minX=Math.min(l.minX,t.x),l.maxX=Math.max(l.maxX,t.x),l.minY=Math.min(l.minY,t.y),l.maxY=Math.max(l.maxY,t.y)}),r>l.minX&&(p=Math.min(p,1-Math.abs((r+n)/(l.minX+n))%1)),s<l.maxX&&(p=Math.min(p,(s-n)/(l.maxX-n))),o>l.minY&&(p=l.minY<0?Math.min(p,(o+h)/(-l.minY+o+h)):Math.min(p,1-(o+h)/(l.minY+h)%1)),a<l.maxY&&(p=Math.min(p,Math.abs((a-h)/(l.maxY-h)))),p},t}();t.Additions=d}(f||(f={}));var Z=f,C=z(512),F=z.n(C),R=M().composed,q=F().seriesTypes.line.prototype,W=M().pushUnique,B=M().wrap;function G(t){var e=t.apply(this,[].slice.call(arguments,1));if(!this.chart.is3d())return e;var i=q.getGraphPath,r=this.options,s=Math.round(this.yAxis.getThreshold(r.threshold)),o=[];if(this.rawPointsX)for(var a=0;a<this.points.length;a++)o.push({x:this.rawPointsX[a],y:r.stacking?this.points[a].yBottom:s,z:this.zPadding});var n=this.chart.options.chart.options3d;o=w(o,this.chart,!0).map(function(t){return{plotX:t.x,plotY:t.y,plotZ:t.z}}),this.group&&n&&n.depth&&n.beta&&(this.markerGroup&&(this.markerGroup.add(this.group),this.markerGroup.attr({translateX:0,translateY:0})),this.group.attr({zIndex:Math.max(1,n.beta>270||n.beta<90?n.depth-Math.round(this.zPadding||0):Math.round(this.zPadding||0))})),o.reversed=!0;var h=i.call(this,o,!0,!0);if(h[0]&&"M"===h[0][0]&&(h[0]=["L",h[0][1],h[0][2]]),this.areaPath){var l=this.areaPath.splice(0,this.areaPath.length/2).concat(h);l.xMap=this.areaPath.xMap,this.areaPath=l}return this.graphPath=e,e}var H={labels:{position3d:"offset",skew3d:!1},title:{position3d:null,skew3d:null}},N=M().composed,U=M().addEvent,V=M().extend,J=M().pushUnique,K=M().wrap;function Q(t){var e=this.axis.axis3D;e&&V(t.pos,e.fix3dPosition(t.pos))}function $(t){var e=this.axis.axis3D,i=t.apply(this,[].slice.call(arguments,1));if(e){var r=i[0],s=i[1];if("M"===r[0]&&"L"===s[0]){var o=[e.fix3dPosition({x:r[1],y:r[2],z:0}),e.fix3dPosition({x:s[1],y:s[2],z:0})];return this.axis.chart.renderer.toLineSegments(o)}}return i}var tt=function(t){J(N,"Axis.Tick3D")&&(U(t,"afterGetLabelPosition",Q),K(t.prototype,"getMarkPath",$))},te=M().defaultOptions,ti=M().deg2rad,tr=O,ts=M().addEvent,to=M().merge,ta=M().pick,tn=M().wrap;function th(){var t,e=this.chart,i=this.options;(null===(t=e.is3d)||void 0===t?void 0:t.call(e))&&"colorAxis"!==this.coll&&(i.tickWidth=ta(i.tickWidth,0),i.gridLineWidth=ta(i.gridLineWidth,1))}function tl(t){this.chart.is3d()&&"colorAxis"!==this.coll&&t.point&&(t.point.crosshairPos=this.isXAxis?t.point.axisXpos:this.len-t.point.axisYpos)}function tp(){this.axis3D||(this.axis3D=new tu(this))}function tc(t){return this.chart.is3d()&&"colorAxis"!==this.coll?[]:t.apply(this,[].slice.call(arguments,1))}function td(t){if(!this.chart.is3d()||"colorAxis"===this.coll)return t.apply(this,[].slice.call(arguments,1));var e=arguments,i=e[1],r=e[2],s=[],o=this.getPlotLinePath({value:i}),a=this.getPlotLinePath({value:r});if(o&&a)for(var n=0;n<o.length;n+=2){var h=o[n],l=o[n+1],p=a[n],c=a[n+1];"M"===h[0]&&"L"===l[0]&&"M"===p[0]&&"L"===c[0]&&s.push(h,l,c,["L",p[1],p[2]],["Z"])}return s}function tx(t){var e=this.axis3D,i=this.chart,r=t.apply(this,[].slice.call(arguments,1));if("colorAxis"===this.coll||!i.chart3d||!i.is3d()||null===r)return r;var s,o=i.options.chart.options3d,a=this.isZAxis?i.plotWidth:o.depth,n=i.chart3d.frame3d,h=r[0],l=r[1],p=[];return"M"===h[0]&&"L"===l[0]&&(s=[e.swapZ({x:h[1],y:h[2],z:0}),e.swapZ({x:h[1],y:h[2],z:a}),e.swapZ({x:l[1],y:l[2],z:0}),e.swapZ({x:l[1],y:l[2],z:a})],this.horiz?(this.isZAxis?(n.left.visible&&p.push(s[0],s[2]),n.right.visible&&p.push(s[1],s[3])):(n.front.visible&&p.push(s[0],s[2]),n.back.visible&&p.push(s[1],s[3])),n.top.visible&&p.push(s[0],s[1]),n.bottom.visible&&p.push(s[2],s[3])):(n.front.visible&&p.push(s[0],s[2]),n.back.visible&&p.push(s[1],s[3]),n.left.visible&&p.push(s[0],s[1]),n.right.visible&&p.push(s[2],s[3])),p=w(p,this.chart,!1)),i.renderer.toLineSegments(p)}function ty(t,e){var i,r,s=this.chart,o=this.gridGroup,a=this.tickPositions,n=this.ticks;if(this.categories&&s.frameShapes&&s.is3d()&&o&&e&&e.label){var h=o.element.childNodes[0].getBBox(),l=s.frameShapes.left.getBBox(),p=s.options.chart.options3d,c={x:s.plotWidth/2,y:s.plotHeight/2,z:p.depth/2,vd:ta(p.depth,1)*ta(p.viewDistance,0)},d=a.indexOf(e.pos),x=n[a[d-1]],y=n[a[d+1]],f=void 0,u=void 0,v=void 0;return(null===(i=null==x?void 0:x.label)||void 0===i?void 0:i.xy)&&(u=tr({x:x.label.xy.x,y:x.label.xy.y,z:null},c,c.vd)),(null===(r=null==y?void 0:y.label)||void 0===r?void 0:r.xy)&&(v=tr({x:y.label.xy.x,y:y.label.xy.y,z:null},c,c.vd)),f=tr(f={x:e.label.xy.x,y:e.label.xy.y,z:null},c,c.vd),Math.abs(u?f.x-u.x:v?v.x-f.x:h.x-l.x)}return t.apply(this,[].slice.call(arguments,1))}function tf(t){var e=t.apply(this,[].slice.call(arguments,1));return this.axis3D?this.axis3D.fix3dPosition(e,!0):e}var tu=function(){function t(t){this.axis=t}return t.compose=function(t,e){if(tt(e),!t.keepProps.includes("axis3D")){to(!0,te.xAxis,H),t.keepProps.push("axis3D"),ts(t,"init",tp),ts(t,"afterSetOptions",th),ts(t,"drawCrosshair",tl);var i=t.prototype;tn(i,"getLinePath",tc),tn(i,"getPlotBandPath",td),tn(i,"getPlotLinePath",tx),tn(i,"getSlotWidth",ty),tn(i,"getTitlePosition",tf)}},t.prototype.fix3dPosition=function(t,e){var i=this.axis,r=i.chart;if("colorAxis"===i.coll||!r.chart3d||!r.is3d())return t;var s,o=ti*r.options.chart.options3d.alpha,a=ti*r.options.chart.options3d.beta,n=ta(e&&i.options.title.position3d,i.options.labels.position3d),h=ta(e&&i.options.title.skew3d,i.options.labels.skew3d),l=r.chart3d.frame3d,p=r.plotLeft,c=r.plotWidth+p,d=r.plotTop,x=r.plotHeight+d,y=0,f=0,u={x:0,y:1,z:0},v=!1;if(t=i.axis3D.swapZ({x:t.x,y:t.y,z:0}),i.isZAxis){if(i.opposite){if(null===l.axes.z.top)return{};f=t.y-d,t.x=l.axes.z.top.x,t.y=l.axes.z.top.y,s=l.axes.z.top.xDir,v=!l.top.frontFacing}else{if(null===l.axes.z.bottom)return{};f=t.y-x,t.x=l.axes.z.bottom.x,t.y=l.axes.z.bottom.y,s=l.axes.z.bottom.xDir,v=!l.bottom.frontFacing}}else if(i.horiz){if(i.opposite){if(null===l.axes.x.top)return{};f=t.y-d,t.y=l.axes.x.top.y,t.z=l.axes.x.top.z,s=l.axes.x.top.xDir,v=!l.top.frontFacing}else{if(null===l.axes.x.bottom)return{};f=t.y-x,t.y=l.axes.x.bottom.y,t.z=l.axes.x.bottom.z,s=l.axes.x.bottom.xDir,v=!l.bottom.frontFacing}}else if(i.opposite){if(null===l.axes.y.right)return{};y=t.x-c,t.x=l.axes.y.right.x,t.z=l.axes.y.right.z,s={x:(s=l.axes.y.right.xDir).z,y:s.y,z:-s.x}}else{if(null===l.axes.y.left)return{};y=t.x-p,t.x=l.axes.y.left.x,t.z=l.axes.y.left.z,s=l.axes.y.left.xDir}if("chart"===n);else if("flap"===n){if(i.horiz){var g=Math.sin(o),z=Math.cos(o);i.opposite&&(g=-g),v&&(g=-g),u={x:s.z*g,y:z,z:-s.x*g}}else s={x:Math.cos(a),y:0,z:Math.sin(a)}}else if("ortho"===n){if(i.horiz){var b=Math.sin(o),m=Math.cos(o),M={x:Math.sin(a)*m,y:-b,z:-m*Math.cos(a)},P=1/Math.sqrt((u={x:s.y*M.z-s.z*M.y,y:s.z*M.x-s.x*M.z,z:s.x*M.y-s.y*M.x}).x*u.x+u.y*u.y+u.z*u.z);v&&(P=-P),u={x:P*u.x,y:P*u.y,z:P*u.z}}else s={x:Math.cos(a),y:0,z:Math.sin(a)}}else i.horiz?u={x:Math.sin(a)*Math.sin(o),y:Math.cos(o),z:-Math.cos(a)*Math.sin(o)}:s={x:Math.cos(a),y:0,z:Math.sin(a)};t.x+=y*s.x+f*u.x,t.y+=y*s.y+f*u.y,t.z+=y*s.z+f*u.z;var A=w([t],i.chart)[0];if(h){0>_(w([t,{x:t.x+s.x,y:t.y+s.y,z:t.z+s.z},{x:t.x+u.x,y:t.y+u.y,z:t.z+u.z}],i.chart))&&(s={x:-s.x,y:-s.y,z:-s.z});var S=w([{x:t.x,y:t.y,z:t.z},{x:t.x+s.x,y:t.y+s.y,z:t.z+s.z},{x:t.x+u.x,y:t.y+u.y,z:t.z+u.z}],i.chart);A.matrix=[S[1].x-S[0].x,S[1].y-S[0].y,S[2].x-S[0].x,S[2].y-S[0].y,A.x,A.y],A.matrix[4]-=A.x*A.matrix[0]+A.y*A.matrix[2],A.matrix[5]-=A.x*A.matrix[1]+A.y*A.matrix[3]}return A},t.prototype.swapZ=function(t,e){var i=this.axis;if(i.isZAxis){var r=e?0:i.chart.plotLeft;return{x:r+t.z,y:t.y,z:t.x-r}}return t},t}(),tv=z(608),tg=z.n(tv),tz=z(820),tb=z.n(tz),tm=(h=function(t,e){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}h(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tM=M().composed,tP=M().addEvent,tA=M().extend,tS=M().isNumber,tk=M().merge,tw=M().pick,tO=M().pushUnique,t_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tm(e,t),e.compose=function(t){tO(tM,"Core.Series3D")&&(tP(t,"afterTranslate",function(){this.chart.is3d()&&this.translate3dPoints()}),tA(t.prototype,{translate3dPoints:e.prototype.translate3dPoints}))},e.prototype.translate3dPoints=function(){var t,e,i=this,r=i.options,s=i.chart,o=tw(i.zAxis,s.options.zAxis[0]),a=[],n=[],h=r.stacking?tS(r.stack)?r.stack:0:i.index||0;i.zPadding=h*(r.depth||0+(r.groupZPadding||1)),i.data.forEach(function(t){(null==o?void 0:o.translate)?(e=o.logarithmic&&o.val2lin?o.val2lin(t.z):t.z,t.plotZ=o.translate(e),t.isInside=!!t.isInside&&e>=o.min&&e<=o.max):t.plotZ=i.zPadding,t.axisXpos=t.plotX,t.axisYpos=t.plotY,t.axisZpos=t.plotZ,a.push({x:t.plotX,y:t.plotY,z:t.plotZ}),n.push(t.plotX||0)}),i.rawPointsX=n;var l=w(a,s,!0);i.data.forEach(function(e,i){e.plotX=(t=l[i]).x,e.plotY=t.y,e.plotZ=t.z})},e.defaultOptions=tk(tb().defaultOptions),e}(tb()),tI=z(184),tL=z.n(tI),tD=(l=function(t,e){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tT=A().parse,tX=tg().getRendererType().prototype.Element,tj=M().defined,tY=M().pick,tE=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.parts=["front","top","side"],e.pathType="cuboid",e}return tD(e,t),e.prototype.initArgs=function(t){for(var e=this.renderer,i=e[this.pathType+"Path"](t),r=i.zIndexes,s=0,o=this.parts;s<o.length;s++){var a=o[s],n={class:"highcharts-3d-"+a,zIndex:r[a]||0};e.styledMode&&("top"===a?n.filter="url(#highcharts-brighter)":"side"===a&&(n.filter="url(#highcharts-darker)")),this[a]=e.path(i[a]).attr(n).add(this)}this.attr({"stroke-linejoin":"round",zIndex:r.group}),this.forcedSides=i.forcedSides},e.prototype.singleSetterForParts=function(t,e,i,r,s,o){var a={},n=[null,null,r||"attr",s,o],h=null==i?void 0:i.zIndexes;if(i){(null==h?void 0:h.group)&&this.attr({zIndex:h.group});for(var l=0,p=Object.keys(i);l<p.length;l++){var c=p[l];a[c]={},a[c][t]=i[c],h&&(a[c].zIndex=i.zIndexes[c]||0)}n[1]=a}else a[t]=e,n[0]=a;return this.processParts.apply(this,n)},e.prototype.processParts=function(t,e,i,r,s){for(var o=0,a=this.parts;o<a.length;o++){var n=a[o];e&&(t=tY(e[n],!1)),!1!==t&&this[n][i](t,r,s)}return this},e.prototype.destroy=function(){return this.processParts(null,null,"destroy"),t.prototype.destroy.call(this)},e.prototype.attr=function(e,i,r,s){if("string"==typeof e&&void 0!==i){var o=e;(e={})[o]=i}return e.shapeArgs||tj(e.x)?this.singleSetterForParts("d",null,this.renderer[this.pathType+"Path"](e.shapeArgs||e)):t.prototype.attr.call(this,e,void 0,r,s)},e.prototype.animate=function(e,i,r){if(tj(e.x)&&tj(e.y)){var s=this.renderer[this.pathType+"Path"](e),o=s.forcedSides;this.singleSetterForParts("d",null,s,"animate",i,r),this.attr({zIndex:s.zIndexes.group}),o===this.forcedSides||(this.forcedSides=o,this.renderer.styledMode||this.fillSetter(this.fill))}else t.prototype.animate.call(this,e,i,r);return this},e.prototype.fillSetter=function(t){return this.forcedSides=this.forcedSides||[],this.singleSetterForParts("fill",null,{front:t,top:tT(t).brighten(this.forcedSides.indexOf("top")>=0?0:.1).get(),side:tT(t).brighten(this.forcedSides.indexOf("side")>=0?0:-.1).get()}),this.color=this.fill=t,this},e.types={base:e,cuboid:e},e}(tX),tZ=M().animObject,tC=A().parse,tF=M().charts,tR=M().deg2rad,tq=M().defined,tW=M().extend,tB=M().merge,tG=M().pick,tH=Math.cos,tN=Math.sin,tU=Math.PI,tV=4*(Math.sqrt(2)-1)/3/(tU/2);function tJ(t,e,i,r,s,o,a,n){var h=o-s,l=[];return o>s&&o-s>Math.PI/2+1e-4?l=(l=l.concat(tJ(t,e,i,r,s,s+Math.PI/2,a,n))).concat(tJ(t,e,i,r,s+Math.PI/2,o,a,n)):o<s&&s-o>Math.PI/2+1e-4?l=(l=l.concat(tJ(t,e,i,r,s,s-Math.PI/2,a,n))).concat(tJ(t,e,i,r,s-Math.PI/2,o,a,n)):[["C",t+i*Math.cos(s)-i*tV*h*Math.sin(s)+a,e+r*Math.sin(s)+r*tV*h*Math.cos(s)+n,t+i*Math.cos(o)+i*tV*h*Math.sin(o)+a,e+r*Math.sin(o)-r*tV*h*Math.cos(o)+n,t+i*Math.cos(o)+a,e+r*Math.sin(o)+n]]}!function(t){function e(t,e){for(var i=[],r=0;r<t.length;r++){var s=t[r];i.push(["L",s.x,s.y])}return t.length&&(i[0][0]="M",e&&i.push(["Z"])),i}function i(t){for(var e=[],i=!0,r=0;r<t.length;r++){var s=t[r];e.push(i?["M",s.x,s.y]:["L",s.x,s.y]),i=!i}return e}function r(t){var e=this,i=e.Element.prototype,r=e.createElement("path");return r.vertexes=[],r.insidePlotArea=!1,r.enabled=!0,r.attr=function(t){if("object"==typeof t&&(tq(t.enabled)||tq(t.vertexes)||tq(t.insidePlotArea))){this.enabled=tG(t.enabled,this.enabled),this.vertexes=tG(t.vertexes,this.vertexes),this.insidePlotArea=tG(t.insidePlotArea,this.insidePlotArea),delete t.enabled,delete t.vertexes,delete t.insidePlotArea;var r=tF[e.chartIndex],s=w(this.vertexes,r,this.insidePlotArea),o=e.toLinePath(s,!0),a=_(s);t.d=o,t.visibility=this.enabled&&a>0?"inherit":"hidden"}return i.attr.apply(this,arguments)},r.animate=function(t){if("object"==typeof t&&(tq(t.enabled)||tq(t.vertexes)||tq(t.insidePlotArea))){this.enabled=tG(t.enabled,this.enabled),this.vertexes=tG(t.vertexes,this.vertexes),this.insidePlotArea=tG(t.insidePlotArea,this.insidePlotArea),delete t.enabled,delete t.vertexes,delete t.insidePlotArea;var r=tF[e.chartIndex],s=w(this.vertexes,r,this.insidePlotArea),o=e.toLinePath(s,!0),a=_(s),n=this.enabled&&a>0?"visible":"hidden";t.d=o,this.attr("visibility",n)}return i.animate.apply(this,arguments)},r.attr(t)}function s(t){var e=this,i=e.Element.prototype,r=e.g(),s=r.destroy;return this.styledMode||r.attr({"stroke-linejoin":"round"}),r.faces=[],r.destroy=function(){for(var t=0;t<r.faces.length;t++)r.faces[t].destroy();return s.call(this)},r.attr=function(t,s,o,a){if("object"==typeof t&&tq(t.faces)){for(;r.faces.length>t.faces.length;)r.faces.pop().destroy();for(;r.faces.length<t.faces.length;)r.faces.push(e.face3d().add(r));for(var n=0;n<t.faces.length;n++)e.styledMode&&delete t.faces[n].fill,r.faces[n].attr(t.faces[n],null,o,a);delete t.faces}return i.attr.apply(this,arguments)},r.animate=function(t,s,o){if(null==t?void 0:t.faces){for(;r.faces.length>t.faces.length;)r.faces.pop().destroy();for(;r.faces.length<t.faces.length;)r.faces.push(e.face3d().add(r));for(var a=0;a<t.faces.length;a++)r.faces[a].animate(t.faces[a],s,o);delete t.faces}return i.animate.apply(this,arguments)},r.attr(t)}function o(t,e){var i=new tE.types[t](this,"g");return i.initArgs(e),i}function a(t){return this.element3d("cuboid",t)}function n(t){var e,i=t.x||0,r=t.y||0,s=t.z||0,o=t.height||0,a=t.width||0,n=t.depth||0,h=tF[this.chartIndex],l=h.options.chart.options3d.alpha,p=[],c=0,d=[{x:i,y:r,z:s},{x:i+a,y:r,z:s},{x:i+a,y:r+o,z:s},{x:i,y:r+o,z:s},{x:i,y:r+o,z:s+n},{x:i+a,y:r+o,z:s+n},{x:i+a,y:r,z:s+n},{x:i,y:r,z:s+n}];d=w(d,h,t.insidePlotArea);var x=function(t){return 0===o&&t>1&&t<6?{x:d[t].x,y:d[t].y+10,z:d[t].z}:d[0].x===d[7].x&&t>=4?{x:d[t].x+10,y:d[t].y,z:d[t].z}:0===n&&t<2||t>5?{x:d[t].x,y:d[t].y,z:d[t].z+10}:d[t]},y=function(t){return d[t]},f=function(t,e,i){var r=t.map(y),s=e.map(y),o=t.map(x),a=e.map(x),n=[[],-1];return 0>_(r)?n=[r,0]:0>_(s)?n=[s,1]:i&&(p.push(i),n=0>_(o)?[r,0]:0>_(a)?[s,1]:[r,0]),n},u=(e=f([3,2,1,0],[7,6,5,4],"front"))[0],v=e[1],g=(e=f([1,6,7,0],[4,5,2,3],"top"))[0],z=e[1],b=(e=f([1,2,5,6],[0,7,4,3],"side"))[0],m=e[1];return 1===m?c+=1e6*(h.plotWidth-i):m||(c+=1e6*i),c+=10*(!z||l>=0&&l<=180||l<360&&l>357.5?h.plotHeight-r:10+r),1===v?c+=100*s:v||(c+=100*(1e3-s)),{front:this.toLinePath(u,!0),top:this.toLinePath(g,!0),side:this.toLinePath(b,!0),zIndexes:{group:Math.round(c)},forcedSides:p,isFront:v,isTop:z}}function h(t){var e=this.g(),i=this.Element.prototype,r=["alpha","beta","x","y","r","innerR","start","end","depth"];function s(t){var e,i={};for(e in t=tB(t))-1!==r.indexOf(e)&&(i[e]=t[e],delete t[e]);return!!Object.keys(i).length&&[i,t]}(t=tB(t)).alpha=(t.alpha||0)*tR,t.beta=(t.beta||0)*tR,e.top=this.path(),e.side1=this.path(),e.side2=this.path(),e.inn=this.path(),e.out=this.path(),e.onAdd=function(){var t=e.parentGroup,i=e.attr("class");e.top.add(e);for(var r=0,s=["out","inn","side1","side2"];r<s.length;r++)e[s[r]].attr({class:i+" highcharts-3d-side"}).add(t)};for(var o=function(t){e[t]=function(){for(var i=arguments,r=0,s=["top","out","inn","side1","side2"];r<s.length;r++){var o=s[r];e[o][t].apply(e[o],i)}}},a=0,n=["addClass","removeClass"];a<n.length;a++)o(n[a]);e.setPaths=function(t){var i=e.renderer.arc3dPath(t),r=100*i.zTop;e.attribs=t,e.top.attr({d:i.top,zIndex:i.zTop}),e.inn.attr({d:i.inn,zIndex:i.zInn}),e.out.attr({d:i.out,zIndex:i.zOut}),e.side1.attr({d:i.side1,zIndex:i.zSide1}),e.side2.attr({d:i.side2,zIndex:i.zSide2}),e.zIndex=r,e.attr({zIndex:r}),t.center&&(e.top.setRadialReference(t.center),delete t.center)},e.setPaths(t),e.fillSetter=function(t){var e=tC(t).brighten(-.1).get();return this.fill=t,this.side1.attr({fill:e}),this.side2.attr({fill:e}),this.inn.attr({fill:e}),this.out.attr({fill:e}),this.top.attr({fill:t}),this};for(var h=0,l=["opacity","translateX","translateY","visibility"];h<l.length;h++)e[l[h]+"Setter"]=function(t,i){e[i]=t;for(var r=0,s=["out","inn","side1","side2","top"];r<s.length;r++)e[s[r]].attr(i,t)};return e.attr=function(t){if("object"==typeof t){var r=s(t);if(r){var o=r[0];arguments[0]=r[1],void 0!==o.alpha&&(o.alpha*=tR),void 0!==o.beta&&(o.beta*=tR),tW(e.attribs,o),e.attribs&&e.setPaths(e.attribs)}}return i.attr.apply(e,arguments)},e.animate=function(t,r,o){var a=this.attribs,n="data-"+Math.random().toString(26).substring(2,9);delete t.center,delete t.z;var h=tZ(tG(r,this.renderer.globalAnimation));if(h.duration){var l=s(t);if(e[n]=0,t[n]=1,e[n+"Setter"]=M().noop,l){var p=l[0],c=function(t,e){return a[t]+(tG(p[t],a[t])-a[t])*e};h.step=function(t,e){e.prop===n&&e.elem.setPaths(tB(a,{x:c("x",e.pos),y:c("y",e.pos),r:c("r",e.pos),innerR:c("innerR",e.pos),start:c("start",e.pos),end:c("end",e.pos),depth:c("depth",e.pos)}))}}r=h}return i.animate.call(this,t,r,o)},e.destroy=function(){return this.top.destroy(),this.out.destroy(),this.inn.destroy(),this.side1.destroy(),this.side2.destroy(),i.destroy.call(this)},e.hide=function(){this.top.hide(),this.out.hide(),this.inn.hide(),this.side1.hide(),this.side2.hide()},e.show=function(t){this.top.show(t),this.out.show(t),this.inn.show(t),this.side1.show(t),this.side2.show(t)},e}function l(t){var e=t.x||0,i=t.y||0,r=t.start||0,s=(t.end||0)-1e-5,o=t.r||0,a=t.innerR||0,n=t.depth||0,h=t.alpha||0,l=t.beta||0,p=Math.cos(r),c=Math.sin(r),d=Math.cos(s),x=Math.sin(s),y=o*Math.cos(l),f=o*Math.cos(h),u=a*Math.cos(l),v=a*Math.cos(h),g=n*Math.sin(l),z=n*Math.sin(h),b=[["M",e+y*p,i+f*c]];(b=b.concat(tJ(e,i,y,f,r,s,0,0))).push(["L",e+u*d,i+v*x]),(b=b.concat(tJ(e,i,u,v,s,r,0,0))).push(["Z"]);var m=l>0?Math.PI/2:0,M=h>0?0:Math.PI/2,P=r>-m?r:s>-m?-m:r,A=s<tU-M?s:r<tU-M?tU-M:s,S=2*tU-M,k=[["M",e+y*tH(P),i+f*tN(P)]];k=k.concat(tJ(e,i,y,f,P,A,0,0)),s>S&&r<S?(k.push(["L",e+y*tH(A)+g,i+f*tN(A)+z]),(k=k.concat(tJ(e,i,y,f,A,S,g,z))).push(["L",e+y*tH(S),i+f*tN(S)]),(k=k.concat(tJ(e,i,y,f,S,s,0,0))).push(["L",e+y*tH(s)+g,i+f*tN(s)+z]),(k=k.concat(tJ(e,i,y,f,s,S,g,z))).push(["L",e+y*tH(S),i+f*tN(S)]),k=k.concat(tJ(e,i,y,f,S,A,0,0))):s>tU-M&&r<tU-M&&(k.push(["L",e+y*Math.cos(A)+g,i+f*Math.sin(A)+z]),(k=k.concat(tJ(e,i,y,f,A,s,g,z))).push(["L",e+y*Math.cos(s),i+f*Math.sin(s)]),k=k.concat(tJ(e,i,y,f,s,A,0,0))),k.push(["L",e+y*Math.cos(A)+g,i+f*Math.sin(A)+z]),(k=k.concat(tJ(e,i,y,f,A,P,g,z))).push(["Z"]);var w=[["M",e+u*p,i+v*c]];(w=w.concat(tJ(e,i,u,v,r,s,0,0))).push(["L",e+u*Math.cos(s)+g,i+v*Math.sin(s)+z]),(w=w.concat(tJ(e,i,u,v,s,r,g,z))).push(["Z"]);var O=[["M",e+y*p,i+f*c],["L",e+y*p+g,i+f*c+z],["L",e+u*p+g,i+v*c+z],["L",e+u*p,i+v*c],["Z"]],_=[["M",e+y*d,i+f*x],["L",e+y*d+g,i+f*x+z],["L",e+u*d+g,i+v*x+z],["L",e+u*d,i+v*x],["Z"]],I=Math.atan2(z,-g),L=Math.abs(s+I),D=Math.abs(r+I),T=Math.abs((r+s)/2+I);function X(t){return(t%=2*Math.PI)>Math.PI&&(t=2*Math.PI-t),t}L=X(L),D=X(D);var j=1e5*(T=X(T)),Y=1e5*D,E=1e5*L;return{top:b,zTop:1e5*Math.PI+1,out:k,zOut:Math.max(j,Y,E),inn:w,zInn:Math.max(j,Y,E),side1:O,zSide1:.99*E,side2:_,zSide2:.99*Y}}t.compose=function(t){var p=t.prototype;p.element3d||tW(p,{Element3D:tE,arc3d:h,arc3dPath:l,cuboid:a,cuboidPath:n,element3d:o,face3d:r,polyhedron:s,toLinePath:e,toLineSegments:i})}}(u||(u={}));var tK=u,tQ=z(532),t$=z.n(tQ),t0=(p=function(t,e){return(p=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),t1=M().defaultOptions,t3=M().addEvent,t2=M().merge,t5=M().pick,t6=M().splat;function t9(t){return new t7(this,t)}function t4(){var t=this,e=this.options.zAxis=t6(this.options.zAxis||{});this.is3d()&&(this.zAxis=[],e.forEach(function(e){t.addZAxis(e).setScale()}))}var t7=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.isZAxis=!0,e}return t0(e,t),e.compose=function(t){var e=t.prototype;e.addZAxis||(t1.zAxis=t2(t1.xAxis,{offset:0,lineWidth:0}),e.addZAxis=t9,e.collectionsWithInit.zAxis=[e.addZAxis],e.collectionsWithUpdate.push("zAxis"),t3(t,"afterCreateAxes",t4))},e.prototype.init=function(e,i){this.isZAxis=!0,t.prototype.init.call(this,e,i,"zAxis")},e.prototype.getSeriesExtremes=function(){var t=this;this.hasVisibleSeries=!1,this.dataMin=this.dataMax=this.ignoreMinPadding=this.ignoreMaxPadding=void 0,this.stacking&&this.stacking.buildStacks(),this.series.forEach(function(e){if(e.reserveSpace()){var i=e.options.threshold;t.hasVisibleSeries=!0,t.positiveValuesOnly&&i<=0&&(i=void 0);var r=e.getColumn("z");r.length&&(t.dataMin=Math.min(t5(t.dataMin,r[0]),Math.min.apply(null,r)),t.dataMax=Math.max(t5(t.dataMax,r[0]),Math.max.apply(null,r)))}})},e.prototype.setAxisSize=function(){var e,i=this.chart;t.prototype.setAxisSize.call(this),this.width=this.len=(null===(e=i.options.chart.options3d)||void 0===e?void 0:e.depth)||0,this.right=i.chartWidth-this.width-this.left},e}(t$()),t8=M().composed,et=M().addEvent,ee=M().extend,ei=M().pick,er=M().pushUnique,es=M().wrap;function eo(){var t,e=this.chart,i=this.options,r=i.depth,s=(i.stacking?i.stack||0:this.index)*(r+(i.groupZPadding||1)),o=this.borderWidth%2?.5:0;e.inverted&&!this.yAxis.reversed&&(o*=-1),!1!==i.grouping&&(s=0),s+=i.groupZPadding||1;for(var a=0,n=this.points;a<n.length;a++){var h=n[a];if(h.outside3dPlot=null,null!==h.y){for(var l=ee({x:0,y:0,width:0,height:0},h.shapeArgs||{}),p=[["x","width"],["y","height"]],c=h.tooltipPos,d=void 0,x=0;x<p.length;x++){var y=p[x];if((d=l[y[0]]-o)<0&&(l[y[1]]+=l[y[0]]+o,l[y[0]]=-o,d=0),d+l[y[1]]>this[y[0]+"Axis"].len&&0!==l[y[1]]&&(l[y[1]]=this[y[0]+"Axis"].len-l[y[0]]),0!==l[y[1]]&&(l[y[0]]>=this[y[0]+"Axis"].len||l[y[0]]+l[y[1]]<=o)){for(var f in l)l[f]="y"===f?-9999:0;h.outside3dPlot=!0}}if("roundedRect"===h.shapeType&&(h.shapeType="cuboid"),h.shapeArgs=ee(l,{z:s,depth:r,insidePlotArea:!0}),t={x:l.x+l.width/2,y:l.y,z:s+r/2},e.inverted&&(t.x=l.height,t.y=h.clientX||0),h.axisXpos=t.x,h.axisYpos=t.y,h.axisZpos=t.z,h.plot3d=w([t],e,!0,!1)[0],c){var u=w([{x:c[0],y:c[1],z:s+r/2}],e,!0,!1)[0];h.tooltipPos=[u.x,u.y]}}}this.z=s}function ea(){if(this.chart.is3d()){var t=this.options,e=t.grouping,i=t.stacking,r=this.yAxis.options.reversedStacks,s=0;if(!(void 0!==e&&!e)){var o,a,n,h,l=(o=this.chart.series,a={totalStacks:0},h=1,o.forEach(function(t){a[n=ei(t.options.stack,i?0:o.length-1-t.index)]?a[n].series.push(t):(a[n]={series:[t],position:h},h++)}),a.totalStacks=h+1,a),p=t.stack||0,c=void 0;for(c=0;c<l[p].series.length&&l[p].series[c]!==this;c++);s=10*(l.totalStacks-l[p].position)+(r?c:-c),this.xAxis.reversed||(s=10*l.totalStacks-s)}t.depth=t.depth||25,this.z=this.z||0,t.zIndex=s}}function en(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];return this.series.chart.is3d()?this.graphic&&"g"!==this.graphic.element.nodeName:t.apply(this,e)}function eh(t){if(this.chart.is3d()){var e=arguments,i=e[1],r=this.yAxis,s=this.yAxis.reversed;if(i)for(var o=0,a=this.points;o<a.length;o++){var n=a[o];null===n.y||(n.height=n.shapeArgs.height,n.shapey=n.shapeArgs.y,n.shapeArgs.height=1,s||(n.stackY?n.shapeArgs.y=n.plotY+r.translate(n.stackY):n.shapeArgs.y=n.plotY+(n.negative?-n.height:n.height)))}else{for(var h=0,l=this.points;h<l.length;h++){var n=l[h];null!==n.y&&(n.shapeArgs.height=n.height,n.shapeArgs.y=n.shapey,n.graphic&&n.graphic[n.outside3dPlot?"attr":"animate"](n.shapeArgs,this.options.animation))}this.drawDataLabels()}}else t.apply(this,[].slice.call(arguments,1))}function el(t,e,i,r,s,o){return"dataLabelsGroup"!==e&&"markerGroup"!==e&&this.chart.is3d()&&(this[e]&&delete this[e],o&&(this.chart.columnGroup||(this.chart.columnGroup=this.chart.renderer.g("columnGroup").add(o)),this[e]=this.chart.columnGroup,this.chart.columnGroup.attr(this.getPlotBox()),this[e].survive=!0,"group"===e&&(arguments[3]="visible"))),t.apply(this,Array.prototype.slice.call(arguments,1))}function ep(t){var e=t.apply(this,[].slice.call(arguments,1));return this.chart.is3d&&this.chart.is3d()&&(e.stroke=this.options.edgeColor||e.fill,e["stroke-width"]=ei(this.options.edgeWidth,1)),e}function ec(t,e,i){var r=this.chart.is3d&&this.chart.is3d();r&&(this.options.inactiveOtherPoints=!0),t.call(this,e,i),r&&(this.options.inactiveOtherPoints=!1)}function ed(t,e){if(this.chart.is3d())for(var i=0,r=this.points;i<r.length;i++){var s=r[i];s.visible=s.options.visible=e=void 0===e?!ei(this.visible,s.visible):e,this.options.data[this.data.indexOf(s)]=s.options,s.graphic&&s.graphic.attr({visibility:e?"visible":"hidden"})}t.apply(this,Array.prototype.slice.call(arguments,1))}function ex(t){t.apply(this,[].slice.call(arguments,1)),this.chart.is3d()&&this.translate3dShapes()}function ey(t,e,i,r,s){var o=this.chart;if(r.outside3dPlot=e.outside3dPlot,o.is3d()&&this.is("column")){var a=this.options,n=ei(r.inside,!!this.options.stacking),h=o.options.chart.options3d,l=e.pointWidth/2||0,p={x:s.x+l,y:s.y,z:this.z+a.depth/2};o.inverted&&(n&&(s.width=0,p.x+=e.shapeArgs.height/2),h.alpha>=90&&h.alpha<=270&&(p.y+=e.shapeArgs.width)),s.x=(p=w([p],o,!0,!1)[0]).x-l,s.y=e.outside3dPlot?-9e9:p.y}t.apply(this,[].slice.call(arguments,1))}function ef(t){return!arguments[2].outside3dPlot&&t.apply(this,[].slice.call(arguments,1))}function eu(t,e){var i=t.apply(this,[].slice.call(arguments,1)),r=this.axis.chart,s=e.width;if(r.is3d()&&this.base){var o=+this.base.split(",")[0],a=r.series[o],n=r.options.chart.options3d;if(a&&"column"===a.type){var h={x:i.x+(r.inverted?i.height:s/2),y:i.y,z:a.options.depth/2};r.inverted&&(i.width=0,n.alpha>=90&&n.alpha<=270&&(h.y+=s)),i.x=(h=w([h],r,!0,!1)[0]).x-s/2,i.y=h.y}}return i}var ev=(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),eg=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ev(e,t),e.prototype.haloPath=function(){var e;return(null===(e=this.series)||void 0===e?void 0:e.chart.is3d())?[]:t.prototype.haloPath.apply(this,arguments)},e}(F().seriesTypes.pie.prototype.pointClass),ez=(d=function(t,e){return(d=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}d(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),eb=M().composed,em=M().deg2rad,eM=F().seriesTypes.pie,eP=M().extend,eA=M().pick,eS=M().pushUnique,ek=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ez(e,t),e.compose=function(t){eS(eb,"Pie3D")&&(t.types.pie=e)},e.prototype.addPoint=function(){t.prototype.addPoint.apply(this,arguments),this.chart.is3d()&&this.update(this.userOptions,!0)},e.prototype.animate=function(e){if(this.chart.is3d()){var i=this.center,r=this.group,s=this.markerGroup,o=this.options.animation,a=void 0;!0===o&&(o={}),e?(r.oldtranslateX=eA(r.oldtranslateX,r.translateX),r.oldtranslateY=eA(r.oldtranslateY,r.translateY),a={translateX:i[0],translateY:i[1],scaleX:.001,scaleY:.001},r.attr(a),s&&(s.attrSetters=r.attrSetters,s.attr(a))):(a={translateX:r.oldtranslateX,translateY:r.oldtranslateY,scaleX:1,scaleY:1},r.animate(a,o),s&&s.animate(a,o))}else t.prototype.animate.apply(this,arguments)},e.prototype.getDataLabelPosition=function(e,i){var r=t.prototype.getDataLabelPosition.call(this,e,i);if(this.chart.is3d())for(var s=this.chart.options.chart.options3d,o=e.shapeArgs,a=o.r,n=(o.alpha||(null==s?void 0:s.alpha))*em,h=(o.beta||(null==s?void 0:s.beta))*em,l=(o.start+o.end)/2,p=r.connectorPosition,c=-a*(1-Math.cos(n))*Math.sin(l),d=a*(Math.cos(h)-1)*Math.cos(l),x=0,y=[null==r?void 0:r.natural,p.breakAt,p.touchingSliceAt];x<y.length;x++){var f=y[x];f.x+=d,f.y+=c}return r},e.prototype.pointAttribs=function(e){var i=t.prototype.pointAttribs.apply(this,arguments),r=this.options;return this.chart.is3d()&&!this.chart.styledMode&&(i.stroke=r.edgeColor||e.color||this.color,i["stroke-width"]=eA(r.edgeWidth,1)),i},e.prototype.translate=function(){if(t.prototype.translate.apply(this,arguments),this.chart.is3d()){var e=this.options,i=e.depth||0,r=this.chart.options.chart.options3d,s=r.alpha,o=r.beta,a=e.stacking?(e.stack||0)*i:this._i*i;a+=i/2,!1!==e.grouping&&(a=0);for(var n=0,h=this.points;n<h.length;n++){var l=h[n],p=l.shapeArgs;l.shapeType="arc3d",p.z=a,p.depth=.75*i,p.alpha=s,p.beta=o,p.center=this.center;var c=(p.end+p.start)/2;l.slicedTranslation={translateX:Math.round(Math.cos(c)*e.slicedOffset*Math.cos(s*em)),translateY:Math.round(Math.sin(c)*e.slicedOffset*Math.cos(s*em))}}}},e.prototype.drawTracker=function(){if(t.prototype.drawTracker.apply(this,arguments),this.chart.is3d())for(var e=0,i=this.points;e<i.length;e++){var r=i[e];if(r.graphic)for(var s=0,o=["out","inn","side1","side2"];s<o.length;s++){var a=o[s];r.graphic&&(r.graphic[a].element.point=r)}}},e}(eM);eP(ek.prototype,{pointClass:eg});var ew=z(632),eO=z.n(ew),e_=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),eI=eO().prototype.pointClass,eL=M().defined,eD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return e_(e,t),e.prototype.applyOptions=function(){return t.prototype.applyOptions.apply(this,arguments),eL(this.z)||(this.z=0),this},e}(eI),eT={tooltip:{pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>z: <b>{point.z}</b><br/>"}},eX=(y=function(t,e){return(y=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}y(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ej=M().extend,eY=M().merge,eE=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eX(e,t),e.prototype.pointAttribs=function(e){var i=t.prototype.pointAttribs.apply(this,arguments);return this.chart.is3d()&&e&&(i.zIndex=I(e,this.chart)),i},e.defaultOptions=eY(eO().defaultOptions,eT),e}(eO());ej(eE.prototype,{axisTypes:["xAxis","yAxis","zAxis"],directTouch:!0,parallelArrays:["x","y","z"],pointArrayMap:["x","y","z"],pointClass:eD}),F().registerSeriesType("scatter3d",eE);var eZ=M();({compose:function(t){W(R,"Area3DSeries")&&B(t.prototype,"getGraphPath",G)}}).compose(eZ.Series.types.area),tu.compose(eZ.Axis,eZ.Tick),Z.compose(eZ.Chart,eZ.Fx),({compose:function(t,e){if(er(t8,"Column3D")){var i=t.prototype,r=e.prototype,s=t.types,o=s.column,a=s.columnRange;if(es(i,"alignDataLabel",ey),es(i,"justifyDataLabel",ef),es(r,"getStackBox",eu),o){var n=o.prototype,h=n.pointClass.prototype;n.translate3dPoints=function(){},n.translate3dShapes=eo,et(n,"afterInit",ea),es(h,"hasNewShapeType",en),es(n,"animate",eh),es(n,"plotGroup",el),es(n,"pointAttribs",ep),es(n,"setState",ec),es(n,"setVisible",ed),es(n,"translate",ex)}if(a){var l=a.prototype;es(l.pointClass.prototype,"hasNewShapeType",en),es(l,"plotGroup",el),es(l,"pointAttribs",ep),es(l,"setState",ec),es(l,"setVisible",ed)}}}}).compose(eZ.Series,tL()),ek.compose(eZ.Series),t_.compose(eZ.Series),tK.compose(tg().getRendererType()),t7.compose(eZ.Chart);var eC=eZ;return b.default}()});