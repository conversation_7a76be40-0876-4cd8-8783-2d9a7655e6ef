!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Series,require("highcharts").Series.types.column,require("highcharts").Templating,require("highcharts").Point,require("highcharts").Color,require("highcharts").Chart,require("highcharts").SVGElement,require("highcharts").StackItem):"function"==typeof define&&define.amd?define("highcharts/highcharts-more",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","Series"],["highcharts/highcharts","Series","types","column"],["highcharts/highcharts","Templating"],["highcharts/highcharts","Point"],["highcharts/highcharts","Color"],["highcharts/highcharts","Chart"],["highcharts/highcharts","SVGElement"],["highcharts/highcharts","StackItem"]],e):"object"==typeof exports?exports["highcharts/highcharts-more"]=e(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Series,require("highcharts").Series.types.column,require("highcharts").Templating,require("highcharts").Point,require("highcharts").Color,require("highcharts").Chart,require("highcharts").SVGElement,require("highcharts").StackItem):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Series,t.Highcharts.Series.types.column,t.Highcharts.Templating,t.Highcharts.Point,t.Highcharts.Color,t.Highcharts.Chart,t.Highcharts.SVGElement,t.Highcharts.StackItem)}(this,function(t,e,i,o,r,a,s,n,h,l){return function(){"use strict";var p,c,d,u,f,g,y,v,b,m,x,P,M,w,L,k,A,S,T,C,N,X,O={28:function(t){t.exports=h},184:function(t){t.exports=l},260:function(t){t.exports=a},448:function(t){t.exports=o},512:function(t){t.exports=e},620:function(t){t.exports=s},820:function(t){t.exports=i},944:function(e){e.exports=t},960:function(t){t.exports=n},984:function(t){t.exports=r}},Y={};function I(t){var e=Y[t];if(void 0!==e)return e.exports;var i=Y[t]={exports:{}};return O[t](i,i.exports,I),i.exports}I.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return I.d(e,{a:e}),e},I.d=function(t,e){for(var i in e)I.o(e,i)&&!I.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},I.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var R={};I.d(R,{default:function(){return rJ}});var E=I(944),_=I.n(E),z=I(512),D=I.n(z),B=I(820),W=I.n(B),H=_().deg2rad,j=_().fireEvent,F=_().isNumber,q=_().pick,G=_().relativeLength;(p=C||(C={})).getCenter=function(){var t,e,i,o=this.options,r=this.chart,a=2*(o.slicedOffset||0),s=r.plotWidth-2*a,n=r.plotHeight-2*a,h=o.center,l=Math.min(s,n),p=o.thickness,c=o.size,d=o.innerSize||0;"string"==typeof c&&(c=parseFloat(c)),"string"==typeof d&&(d=parseFloat(d));var u=[q(null==h?void 0:h[0],"50%"),q(null==h?void 0:h[1],"50%"),q(c&&c<0?void 0:o.size,"100%"),q(d&&d<0?void 0:o.innerSize||0,"0%")];for(!r.angular||this instanceof W()||(u[3]=0),e=0;e<4;++e)i=u[e],t=e<2||2===e&&/%$/.test(i),u[e]=G(i,[s,n,l,u[2]][e])+(t?a:0);return u[3]>u[2]&&(u[3]=u[2]),F(p)&&2*p<u[2]&&p>0&&(u[3]=u[2]-2*p),j(this,"afterGetCenter",{positions:u}),u},p.getStartAndEndRadians=function(t,e){var i=F(t)?t:0,o=F(e)&&e>i&&e-i<360?e:i+360;return{start:H*(i+-90),end:H*(o+-90)}};var V=C,U=_().addEvent,K=_().correctFloat,Z=_().defined,$=_().pick;function Q(t){var e,i=this;return t&&i.pane.forEach(function(o){J(t.chartX-i.plotLeft,t.chartY-i.plotTop,o.center)&&(e=o)}),e}function J(t,e,i,o,r){var a=!0,s=i[0],n=i[1],h=Math.sqrt(Math.pow(t-s,2)+Math.pow(e-n,2));if(Z(o)&&Z(r)){var l=Math.atan2(K(e-n,8),K(t-s,8));r!==o&&(a=o>r?l>=o&&l<=Math.PI||l<=r&&l>=-Math.PI:l>=o&&l<=K(r,8))}return h<=Math.ceil(i[2]/2)&&a}function tt(t){var e;this.polar&&(t.options.inverted&&(e=[t.y,t.x],t.x=e[0],t.y=e[1]),t.isInsidePlot=this.pane.some(function(e){return J(t.x,t.y,e.center,e.axis&&e.axis.normalizedStartAngleRad,e.axis&&e.axis.normalizedEndAngleRad)}))}function te(t){var e=this.chart;t.hoverPoint&&t.hoverPoint.plotX&&t.hoverPoint.plotY&&e.hoverPane&&!J(t.hoverPoint.plotX,t.hoverPoint.plotY,e.hoverPane.center)&&(t.hoverPoint=void 0)}function ti(t){var e=this.chart;e.polar?(e.hoverPane=e.getHoverPane(t),t.filter=function(i){return i.visible&&!(!t.shared&&i.directTouch)&&$(i.options.enableMouseTracking,!0)&&(!e.hoverPane||i.xAxis.pane===e.hoverPane)}):e.hoverPane=void 0}var to=function(t,e){var i=t.prototype;i.getHoverPane||(i.collectionsWithUpdate.push("pane"),i.getHoverPane=Q,U(t,"afterIsInsidePlot",tt),U(e,"afterGetHoverData",te),U(e,"beforeGetHoverData",ti))},tr={pane:{center:["50%","50%"],size:"85%",innerSize:"0%",startAngle:0},background:{shape:"circle",borderRadius:0,borderWidth:1,borderColor:"#cccccc",backgroundColor:{linearGradient:{x1:0,y1:0,x2:0,y2:1},stops:[[0,"#ffffff"],[1,"#e6e6e6"]]},from:-Number.MAX_VALUE,innerRadius:0,to:Number.MAX_VALUE,outerRadius:"105%"}},ta=_().extend,ts=_().merge,tn=_().splat,th=function(){function t(t,e){this.coll="pane",this.init(t,e)}return t.prototype.init=function(t,e){this.chart=e,this.background=[],e.pane.push(this),this.setOptions(t)},t.prototype.setOptions=function(t){this.options=t=ts(tr.pane,this.chart.angular?{background:{}}:void 0,t)},t.prototype.render=function(){var t=this.options,e=this.chart.renderer;this.group||(this.group=e.g("pane-group").attr({zIndex:t.zIndex||0}).add()),this.updateCenter();var i=this.options.background;if(i)for(var o=Math.max((i=tn(i)).length,this.background.length||0),r=0;r<o;r++)i[r]&&this.axis?this.renderBackground(ts(tr.background,i[r]),r):this.background[r]&&(this.background[r]=this.background[r].destroy(),this.background.splice(r,1))},t.prototype.renderBackground=function(t,e){var i={class:"highcharts-pane "+(t.className||"")},o="animate";this.chart.styledMode||ta(i,{fill:t.backgroundColor,stroke:t.borderColor,"stroke-width":t.borderWidth}),this.background[e]||(this.background[e]=this.chart.renderer.path().add(this.group),o="attr"),this.background[e][o]({d:this.axis.getPlotBandPath(t.from,t.to,t)}).attr(i)},t.prototype.updateCenter=function(t){this.center=(t||this.axis||{}).center=V.getCenter.call(this)},t.prototype.update=function(t,e){ts(!0,this.options,t),this.setOptions(this.options),this.render(),this.chart.axes.forEach(function(t){t.pane===this&&(t.pane=null,t.update({},e))},this)},t.compose=to,t}(),tl=(c=function(t,e){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}c(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tp=D().seriesTypes.area.prototype,tc=tp.pointClass,td=tp.pointClass.prototype,tu=_().defined,tf=_().isNumber,tg=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tl(e,t),e.prototype.setState=function(){var t=this.state,e=this.series,i=e.chart.polar;tu(this.plotHigh)||(this.plotHigh=e.yAxis.toPixels(this.high,!0)),tu(this.plotLow)||(this.plotLow=this.plotY=e.yAxis.toPixels(this.low,!0)),e.lowerStateMarkerGraphic=e.stateMarkerGraphic,e.stateMarkerGraphic=e.upperStateMarkerGraphic,this.graphic=this.graphics&&this.graphics[1],this.plotY=this.plotHigh,i&&tf(this.plotHighX)&&(this.plotX=this.plotHighX),td.setState.apply(this,arguments),this.state=t,this.plotY=this.plotLow,this.graphic=this.graphics&&this.graphics[0],i&&tf(this.plotLowX)&&(this.plotX=this.plotLowX),e.upperStateMarkerGraphic=e.stateMarkerGraphic,e.stateMarkerGraphic=e.lowerStateMarkerGraphic,e.lowerStateMarkerGraphic=void 0;var o=e.modifyMarkerSettings();td.setState.apply(this,arguments),e.restoreMarkerSettings(o)},e.prototype.haloPath=function(){var t=this.series.chart.polar,e=[];return this.plotY=this.plotLow,t&&tf(this.plotLowX)&&(this.plotX=this.plotLowX),this.isInside&&(e=td.haloPath.apply(this,arguments)),this.plotY=this.plotHigh,t&&tf(this.plotHighX)&&(this.plotX=this.plotHighX),this.isTopInside&&(e=e.concat(td.haloPath.apply(this,arguments))),e},e.prototype.isValid=function(){return tf(this.low)&&tf(this.high)},e}(tc),ty=(d=function(t,e){return(d=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}d(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tv=_().noop,tb=D().seriesTypes,tm=tb.area,tx=tb.area.prototype,tP=tb.column.prototype,tM=_().addEvent,tw=_().defined,tL=_().extend,tk=_().isArray,tA=_().isNumber,tS=_().pick,tT=_().merge,tC={lineWidth:1,threshold:null,tooltip:{pointFormat:'<span style="color:{series.color}">●</span> {series.name}: <b>{point.low}</b> - <b>{point.high}</b><br/>'},trackByArea:!0,dataLabels:{align:void 0,verticalAlign:void 0,xLow:0,xHigh:0,yLow:0,yHigh:0}},tN=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ty(e,t),e.prototype.toYData=function(t){return[t.low,t.high]},e.prototype.highToXY=function(t){var e=this.chart,i=this.xAxis.postTranslate(t.rectPlotX||0,this.yAxis.len-(t.plotHigh||0));t.plotHighX=i.x-e.plotLeft,t.plotHigh=i.y-e.plotTop,t.plotLowX=t.plotX},e.prototype.getGraphPath=function(t){var e,i,o,r=[],a=[],s=tx.getGraphPath,n=this.options,h=this.chart.polar,l=h&&!1!==n.connectEnds,p=n.connectNulls,c=n.step;for(e=(t=t||this.points).length;e--;){i=t[e];var d=h?{plotX:i.rectPlotX,plotY:i.yBottom,doCurve:!1}:{plotX:i.plotX,plotY:i.plotY,doCurve:!1};i.isNull||l||p||t[e+1]&&!t[e+1].isNull||a.push(d),o={polarPlotY:i.polarPlotY,rectPlotX:i.rectPlotX,yBottom:i.yBottom,plotX:tS(i.plotHighX,i.plotX),plotY:i.plotHigh,isNull:i.isNull},a.push(o),r.push(o),i.isNull||l||p||t[e-1]&&!t[e-1].isNull||a.push(d)}var u=s.call(this,t);c&&(!0===c&&(c="left"),n.step=({left:"right",center:"center",right:"left"})[c]);var f=s.call(this,r),g=s.call(this,a);n.step=c;var y=[].concat(u,f);return!this.chart.polar&&g[0]&&"M"===g[0][0]&&(g[0]=["L",g[0][1],g[0][2]]),this.graphPath=y,this.areaPath=u.concat(g),y.isArea=!0,y.xMap=u.xMap,this.areaPath.xMap=u.xMap,y},e.prototype.drawDataLabels=function(){var t,e,i,o,r,a,s,n=this.points,h=n.length,l=[],p=this.options.dataLabels,c=this.chart.inverted;if(p){if(tk(p)?(a=p[0]||{enabled:!1},s=p[1]||{enabled:!1}):((a=tL({},p)).x=p.xHigh,a.y=p.yHigh,(s=tL({},p)).x=p.xLow,s.y=p.yLow),a.enabled||(null===(t=this.hasDataLabels)||void 0===t?void 0:t.call(this))){for(i=h;i--;)if(o=n[i]){var d=o.plotHigh,u=void 0===d?0:d,f=o.plotLow,g=void 0===f?0:f;r=a.inside?u<g:u>g,o.y=o.high,o._plotY=o.plotY,o.plotY=u,l[i]=o.dataLabel,o.dataLabel=o.dataLabelUpper,o.below=r,c?a.align||(a.align=r?"right":"left"):a.verticalAlign||(a.verticalAlign=r?"top":"bottom")}for(this.options.dataLabels=a,tx.drawDataLabels&&tx.drawDataLabels.apply(this,arguments),i=h;i--;)(o=n[i])&&(o.dataLabelUpper=o.dataLabel,o.dataLabel=l[i],delete o.dataLabels,o.y=o.low,o.plotY=o._plotY)}if(s.enabled||(null===(e=this.hasDataLabels)||void 0===e?void 0:e.call(this))){for(i=h;i--;)if(o=n[i]){var y=o.plotHigh,u=void 0===y?0:y,v=o.plotLow,g=void 0===v?0:v;r=s.inside?u<g:u>g,o.below=!r,c?s.align||(s.align=r?"left":"right"):s.verticalAlign||(s.verticalAlign=r?"bottom":"top")}this.options.dataLabels=s,tx.drawDataLabels&&tx.drawDataLabels.apply(this,arguments)}if(a.enabled)for(i=h;i--;)(o=n[i])&&(o.dataLabels=[o.dataLabelUpper,o.dataLabel].filter(function(t){return!!t}));this.options.dataLabels=p}},e.prototype.alignDataLabel=function(){tP.alignDataLabel.apply(this,arguments)},e.prototype.modifyMarkerSettings=function(){var t={marker:this.options.marker,symbol:this.symbol};if(this.options.lowMarker){var e=this.options,i=e.marker,o=e.lowMarker;this.options.marker=tT(i,o),o.symbol&&(this.symbol=o.symbol)}return t},e.prototype.restoreMarkerSettings=function(t){this.options.marker=t.marker,this.symbol=t.symbol},e.prototype.drawPoints=function(){var t,e,i=this.points.length,o=this.modifyMarkerSettings();for(tx.drawPoints.apply(this,arguments),this.restoreMarkerSettings(o),t=0;t<i;)(e=this.points[t]).graphics=e.graphics||[],e.origProps={plotY:e.plotY,plotX:e.plotX,isInside:e.isInside,negative:e.negative,zone:e.zone,y:e.y},(e.graphic||e.graphics[0])&&(e.graphics[0]=e.graphic),e.graphic=e.graphics[1],e.plotY=e.plotHigh,tw(e.plotHighX)&&(e.plotX=e.plotHighX),e.y=tS(e.high,e.origProps.y),e.negative=e.y<(this.options.threshold||0),this.zones.length&&(e.zone=e.getZone()),this.chart.polar||(e.isInside=e.isTopInside=void 0!==e.plotY&&e.plotY>=0&&e.plotY<=this.yAxis.len&&e.plotX>=0&&e.plotX<=this.xAxis.len),t++;for(tx.drawPoints.apply(this,arguments),t=0;t<i;)(e=this.points[t]).graphics=e.graphics||[],(e.graphic||e.graphics[1])&&(e.graphics[1]=e.graphic),e.graphic=e.graphics[0],e.origProps&&(tL(e,e.origProps),delete e.origProps),t++},e.prototype.hasMarkerChanged=function(e,i){var o=e.lowMarker,r=i.lowMarker||{};return o&&(!1===o.enabled||r.symbol!==o.symbol||r.height!==o.height||r.width!==o.width)||t.prototype.hasMarkerChanged.call(this,e,i)},e.defaultOptions=tT(tm.defaultOptions,tC),e}(tm);tM(tN,"afterTranslate",function(){var t=this;"low,high"===this.pointArrayMap.join(",")&&this.points.forEach(function(e){var i=e.high,o=e.plotY;e.isNull?e.plotY=void 0:(e.plotLow=o,e.plotHigh=tA(i)?t.yAxis.translate(t.dataModify?t.dataModify.modifyValue(i):i,!1,!0,void 0,!0):void 0,t.dataModify&&(e.yBottom=e.plotHigh))})},{order:0}),tM(tN,"afterTranslate",function(){var t=this;this.points.forEach(function(e){if(t.chart.polar)t.highToXY(e),e.plotLow=e.plotY,e.tooltipPos=[((e.plotHighX||0)+(e.plotLowX||0))/2,((e.plotHigh||0)+(e.plotLow||0))/2];else{var i=e.pos(!1,e.plotLow),o=e.pos(!1,e.plotHigh);i&&o&&(i[0]=(i[0]+o[0])/2,i[1]=(i[1]+o[1])/2),e.tooltipPos=i}})},{order:3}),tL(tN.prototype,{deferTranslatePolar:!0,pointArrayMap:["low","high"],pointClass:tg,pointValKey:"low",setStackedPoints:tv}),D().registerSeriesType("arearange",tN);var tX=(u=function(t,e){return(u=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tO=D().seriesTypes.spline.prototype,tY=_().merge,tI=_().extend,tR=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tX(e,t),e.defaultOptions=tY(tN.defaultOptions),e}(tN);tI(tR.prototype,{getPointSpline:tO.getPointSpline}),D().registerSeriesType("areasplinerange",tR);var tE={threshold:null,tooltip:{pointFormat:'<span style="color:{point.color}">●</span> <b>{series.name}</b><br/>Maximum: {point.high}<br/>Upper quartile: {point.q3}<br/>Median: {point.median}<br/>Lower quartile: {point.q1}<br/>Minimum: {point.low}<br/>'},whiskerLength:"50%",fillColor:"#ffffff",lineWidth:1,medianWidth:2,whiskerWidth:2},t_=I(448),tz=I.n(t_),tD=(f=function(t,e){return(f=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}f(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),tB=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,a=e.length;r<a;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},tW=_().noop,tH=_().crisp,tj=_().extend,tF=_().merge,tq=_().pick,tG=_().relativeLength,tV=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tD(e,t),e.prototype.pointAttribs=function(){return{}},e.prototype.getWhiskerPair=function(t,e,i,o,r){var a=r.whiskers.strokeWidth(),s=function(i,o){var r=tG(i,2*t)/2,s=tH(o,a);return[["M",tH(e-r),s],["L",tH(e+r),s]]};return tB(tB([],s(i,r.highPlot),!0),s(o,r.lowPlot),!0)},e.prototype.translate=function(){var e=this.yAxis,i=this.pointArrayMap;t.prototype.translate.apply(this),this.points.forEach(function(t){i.forEach(function(i){null!==t[i]&&(t[i+"Plot"]=e.translate(t[i],0,1,0,1))}),t.plotHigh=t.highPlot})},e.prototype.drawPoints=function(){for(var t,e,i,o,r,a,s,n,h,l,p,c,d,u,f,g=this.points,y=this.options,v=this.chart,b=v.renderer,m=!1!==this.doQuartiles,x=this.options.whiskerLength,P=0;P<g.length;P++){var M=g[P],w=(c=M.graphic)?"animate":"attr",L=M.shapeArgs,k={},A={},S={},T={},C=M.color||this.color,N=M.options.whiskerLength||x;if(void 0!==M.plotY){d=L.width,f=(u=L.x)+d,r=m?M.q1Plot:M.lowPlot,a=m?M.q3Plot:M.lowPlot,s=M.highPlot,n=M.lowPlot,c||(M.graphic=c=b.g("point").add(this.group),M.stem=b.path().addClass("highcharts-boxplot-stem").add(c),x&&(M.whiskers=b.path().addClass("highcharts-boxplot-whisker").add(c)),m&&(M.box=b.path(p).addClass("highcharts-boxplot-box").add(c)),M.medianShape=b.path(l).addClass("highcharts-boxplot-median").add(c)),v.styledMode||(A.stroke=M.stemColor||y.stemColor||C,A["stroke-width"]=tq(M.stemWidth,y.stemWidth,y.lineWidth),A.dashstyle=M.stemDashStyle||y.stemDashStyle||y.dashStyle,M.stem.attr(A),N&&(S.stroke=M.whiskerColor||y.whiskerColor||C,S["stroke-width"]=tq(M.whiskerWidth,y.whiskerWidth,y.lineWidth),S.dashstyle=M.whiskerDashStyle||y.whiskerDashStyle||y.dashStyle,M.whiskers.attr(S)),m&&(k.fill=M.fillColor||y.fillColor||C,k.stroke=y.lineColor||C,k["stroke-width"]=y.lineWidth||0,k.dashstyle=M.boxDashStyle||y.boxDashStyle||y.dashStyle,M.box.attr(k)),T.stroke=M.medianColor||y.medianColor||C,T["stroke-width"]=tq(M.medianWidth,y.medianWidth,y.lineWidth),T.dashstyle=M.medianDashStyle||y.medianDashStyle||y.dashStyle,M.medianShape.attr(T));var X=void 0,O=tH((M.plotX||0)+(this.pointXOffset||0)+(this.barW||0)/2,M.stem.strokeWidth());if(X=[["M",O,a],["L",O,s],["M",O,r],["L",O,n]],M.stem[w]({d:X}),m){var Y=M.box.strokeWidth();r=tH(r,Y),a=tH(a,Y),X=[["M",u=tH(u,Y),a],["L",u,r],["L",f=tH(f,Y),r],["L",f,a],["L",u,a],["Z"]],M.box[w]({d:X})}if(N){var I=d/2,R=this.getWhiskerPair(I,O,null!==(e=null!==(t=M.upperWhiskerLength)&&void 0!==t?t:y.upperWhiskerLength)&&void 0!==e?e:N,null!==(o=null!==(i=M.lowerWhiskerLength)&&void 0!==i?i:y.lowerWhiskerLength)&&void 0!==o?o:N,M);M.whiskers[w]({d:R})}X=[["M",u,h=tH(M.medianPlot,M.medianShape.strokeWidth())],["L",f,h]],M.medianShape[w]({d:X})}}},e.prototype.toYData=function(t){return[t.low,t.q1,t.median,t.q3,t.high]},e.defaultOptions=tF(tz().defaultOptions,tE),e}(tz());tj(tV.prototype,{pointArrayMap:["low","q1","median","q3","high"],pointValKey:"high",drawDataLabels:tW,setStackedPoints:tW}),D().registerSeriesType("boxplot",tV);var tU={borderColor:void 0,borderWidth:2,className:void 0,color:void 0,connectorClassName:void 0,connectorColor:void 0,connectorDistance:60,connectorWidth:1,enabled:!1,labels:{className:void 0,allowOverlap:!1,format:"",formatter:void 0,align:"right",style:{fontSize:"0.9em",color:"#000000"},x:0,y:0},maxSize:60,minSize:10,legendIndex:0,ranges:{value:void 0,borderColor:void 0,color:void 0,connectorColor:void 0},sizeBy:"area",sizeByAbsoluteValue:!1,zIndex:1,zThreshold:0},tK=I(984),tZ=I.n(tK),t$=_().noop,tQ=_().arrayMax,tJ=_().arrayMin,t0=_().isNumber,t1=_().merge,t2=_().pick,t3=_().stableSort,t5=function(){function t(t,e){this.setState=t$,this.init(t,e)}return t.prototype.init=function(t,e){this.options=t,this.visible=!0,this.chart=e.chart,this.legend=e},t.prototype.addToLegend=function(t){t.splice(this.options.legendIndex,0,this)},t.prototype.drawLegendSymbol=function(t){var e,i=t2(t.options.itemDistance,20),o=this.legendItem||{},r=this.options,a=r.ranges,s=r.connectorDistance;if(!a||!a.length||!t0(a[0].value)){t.options.bubbleLegend.autoRanges=!0;return}t3(a,function(t,e){return e.value-t.value}),this.ranges=a,this.setOptions(),this.render();var n=this.getMaxLabelSize(),h=this.ranges[0].radius,l=2*h;e=(e=s-h+n.width)>0?e:0,this.maxLabel=n,this.movementX="left"===r.labels.align?e:0,o.labelWidth=l+e+i,o.labelHeight=l+n.height/2},t.prototype.setOptions=function(){var t=this.ranges,e=this.options,i=this.chart.series[e.seriesIndex],o=this.legend.baseline,r={zIndex:e.zIndex,"stroke-width":e.borderWidth},a={zIndex:e.zIndex,"stroke-width":e.connectorWidth},s={align:this.legend.options.rtl||"left"===e.labels.align?"right":"left",zIndex:e.zIndex},n=i.options.marker.fillOpacity,h=this.chart.styledMode;t.forEach(function(l,p){h||(r.stroke=t2(l.borderColor,e.borderColor,i.color),r.fill=l.color||e.color,r.fill||(r.fill=i.color,r["fill-opacity"]=null!=n?n:1),a.stroke=t2(l.connectorColor,e.connectorColor,i.color)),t[p].radius=this.getRangeRadius(l.value),t[p]=t1(t[p],{center:t[0].radius-t[p].radius+o}),h||t1(!0,t[p],{bubbleAttribs:t1(r),connectorAttribs:t1(a),labelAttribs:s})},this)},t.prototype.getRangeRadius=function(t){var e=this.options,i=this.options.seriesIndex,o=this.chart.series[i],r=e.ranges[0].value,a=e.ranges[e.ranges.length-1].value,s=e.minSize,n=e.maxSize;return o.getRadius.call(this,a,r,s,n,t)},t.prototype.render=function(){var t=this.legendItem||{},e=this.chart.renderer,i=this.options.zThreshold;this.symbols||(this.symbols={connectors:[],bubbleItems:[],labels:[]}),t.symbol=e.g("bubble-legend"),t.label=e.g("bubble-legend-item").css(this.legend.itemStyle||{}),t.symbol.translateX=0,t.symbol.translateY=0,t.symbol.add(t.label),t.label.add(t.group);for(var o=0,r=this.ranges;o<r.length;o++){var a=r[o];a.value>=i&&this.renderRange(a)}this.hideOverlappingLabels()},t.prototype.renderRange=function(t){var e=this.ranges[0],i=this.legend,o=this.options,r=o.labels,a=this.chart,s=a.series[o.seriesIndex],n=a.renderer,h=this.symbols,l=h.labels,p=t.center,c=Math.abs(t.radius),d=o.connectorDistance||0,u=r.align,f=i.options.rtl,g=o.borderWidth,y=o.connectorWidth,v=e.radius||0,b=p-c-g/2+y/2,m=(b%1?1:.5)-(y%2?0:.5),x=n.styledMode,P=f||"left"===u?-d:d;"center"===u&&(P=0,o.connectorDistance=0,t.labelAttribs.align="center"),h.bubbleItems.push(n.circle(v,p+m,c).attr(x?{}:t.bubbleAttribs).addClass((x?"highcharts-color-"+s.colorIndex+" ":"")+"highcharts-bubble-legend-symbol "+(o.className||"")).add(this.legendItem.symbol)),h.connectors.push(n.path(n.crispLine([["M",v,b],["L",v+P,b]],o.connectorWidth)).attr(x?{}:t.connectorAttribs).addClass((x?"highcharts-color-"+this.options.seriesIndex+" ":"")+"highcharts-bubble-legend-connectors "+(o.connectorClassName||"")).add(this.legendItem.symbol));var M=n.text(this.formatLabel(t)).attr(x?{}:t.labelAttribs).css(x?{}:r.style).addClass("highcharts-bubble-legend-labels "+(o.labels.className||"")).add(this.legendItem.symbol),w={x:v+P+o.labels.x,y:b+o.labels.y+.4*M.getBBox().height};M.attr(w),l.push(M),M.placed=!0,M.alignAttr=w},t.prototype.getMaxLabelSize=function(){var t,e;return this.symbols.labels.forEach(function(i){e=i.getBBox(!0),t=t?e.width>t.width?e:t:e}),t||{}},t.prototype.formatLabel=function(t){var e=this.options,i=e.labels.formatter,o=e.labels.format,r=this.chart.numberFormatter;return o?tZ().format(o,t,this.chart):i?i.call(t):r(t.value,1)},t.prototype.hideOverlappingLabels=function(){var t=this.chart,e=this.options.labels.allowOverlap,i=this.symbols;!e&&i&&(t.hideOverlappingLabels(i.labels),i.labels.forEach(function(t,e){t.newOpacity?t.newOpacity!==t.oldOpacity&&i.connectors[e].show():i.connectors[e].hide()}))},t.prototype.getRanges=function(){var t,e,i=this.legend.bubbleLegend,o=i.chart.series,r=i.options.ranges,a=Number.MAX_VALUE,s=-Number.MAX_VALUE;return o.forEach(function(t){t.isBubble&&!t.ignoreSeries&&(e=t.getColumn("z").filter(t0)).length&&(a=t2(t.options.zMin,Math.min(a,Math.max(tJ(e),!1===t.options.displayNegative?t.options.zThreshold:-Number.MAX_VALUE))),s=t2(t.options.zMax,Math.max(s,tQ(e))))}),t=a===s?[{value:s}]:[{value:a},{value:(a+s)/2},{value:s,autoRanges:!0}],r.length&&r[0].radius&&t.reverse(),t.forEach(function(e,i){r&&r[i]&&(t[i]=t1(r[i],e))}),t},t.prototype.predictBubbleSizes=function(){var t,e=this.chart,i=e.legend.options,o=i.floating,r="horizontal"===i.layout,a=r?e.legend.lastLineHeight:0,s=e.plotSizeX,n=e.plotSizeY,h=e.series[this.options.seriesIndex],l=h.getPxExtremes(),p=Math.ceil(l.minPxSize),c=Math.ceil(l.maxPxSize),d=Math.min(n,s),u=h.options.maxSize;return o||!/%$/.test(u)?t=c:(t=(d+a)*(u=parseFloat(u))/100/(u/100+1),(r&&n-t>=s||!r&&s-t>=n)&&(t=c)),[p,Math.ceil(t)]},t.prototype.updateRanges=function(t,e){var i=this.legend.options.bubbleLegend;i.minSize=t,i.maxSize=e,i.ranges=this.getRanges()},t.prototype.correctSizes=function(){var t=this.legend,e=this.chart.series[this.options.seriesIndex].getPxExtremes();Math.abs(Math.ceil(e.maxPxSize)-this.options.maxSize)>1&&(this.updateRanges(this.options.minSize,e.maxPxSize),t.render())},t}(),t8=_().setOptions,t6=_().composed,t4=_().addEvent,t9=_().objectEach,t7=_().pushUnique,et=_().wrap;function ee(t,e,i){var o,r,a,s=this.legend,n=ei(this)>=0;s&&s.options.enabled&&s.bubbleLegend&&s.options.bubbleLegend.autoRanges&&n?(o=s.bubbleLegend.options,r=s.bubbleLegend.predictBubbleSizes(),s.bubbleLegend.updateRanges(r[0],r[1]),o.placed||(s.group.placed=!1,s.allItems.forEach(function(t){(a=t.legendItem||{}).group&&(a.group.translateY=void 0)})),s.render(),o.placed||(this.getMargins(),this.axes.forEach(function(t){t.setScale(),t.updateNames(),t9(t.ticks,function(t){t.isNew=!0,t.isNewLabel=!0})}),this.getMargins()),o.placed=!0,t.call(this,e,i),s.bubbleLegend.correctSizes(),es(s,eo(s))):(t.call(this,e,i),s&&s.options.enabled&&s.bubbleLegend&&(s.render(),es(s,eo(s))))}function ei(t){for(var e=t.series,i=0;i<e.length;){if(e[i]&&e[i].isBubble&&e[i].visible&&e[i].dataTable.rowCount)return i;i++}return -1}function eo(t){var e,i,o,r=t.allItems,a=[],s=r.length,n=0,h=0;for(n=0;n<s;n++)if(i=r[n].legendItem||{},o=(r[n+1]||{}).legendItem||{},i.labelHeight&&(r[n].itemHeight=i.labelHeight),r[n]===r[s-1]||i.y!==o.y){for(a.push({height:0}),e=a[a.length-1];h<=n;h++)r[h].itemHeight>e.height&&(e.height=r[h].itemHeight);e.step=n}return a}function er(t){var e=this.bubbleLegend,i=this.options,o=i.bubbleLegend,r=ei(this.chart);e&&e.ranges&&e.ranges.length&&(o.ranges.length&&(o.autoRanges=!!o.ranges[0].autoRanges),this.destroyItem(e)),r>=0&&i.enabled&&o.enabled&&(o.seriesIndex=r,this.bubbleLegend=new t5(o,this),this.bubbleLegend.addToLegend(t.allItems))}function ea(t){if(t.defaultPrevented)return!1;var e,i=t.legendItem,o=this.chart,r=i.visible;this&&this.bubbleLegend&&(i.visible=!r,i.ignoreSeries=r,e=ei(o)>=0,this.bubbleLegend.visible!==e&&(this.update({bubbleLegend:{enabled:e}}),this.bubbleLegend.visible=e),i.visible=r)}function es(t,e){var i,o,r,a,s=t.allItems,n=t.options.rtl,h=0;s.forEach(function(t,s){(a=t.legendItem||{}).group&&(i=a.group.translateX||0,o=a.y||0,((r=t.movementX)||n&&t.ranges)&&(r=n?i-t.options.maxSize/2:i+r,a.group.attr({translateX:r})),s>e[h].step&&h++,a.group.attr({translateY:Math.round(o+e[h].height/2)}),a.y=o+e[h].height/2)})}var en=function(t,e){t7(t6,"Series.BubbleLegend")&&(t8({legend:{bubbleLegend:tU}}),et(t.prototype,"drawChartBox",ee),t4(e,"afterGetAllItems",er),t4(e,"itemClick",ea))},eh=I(260),el=I.n(eh),ep=(g=function(t,e){return(g=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}g(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ec=D().seriesTypes.scatter.prototype.pointClass,ed=_().extend,eu=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ep(e,t),e.prototype.haloPath=function(t){var e=(t&&this.marker&&this.marker.radius||0)+t;if(this.series.chart.inverted){var i=this.pos()||[0,0],o=this.series,r=o.xAxis,a=o.yAxis;return o.chart.renderer.symbols.circle(r.len-i[1]-e,a.len-i[0]-e,2*e,2*e)}return el().prototype.haloPath.call(this,e)},e}(ec);ed(eu.prototype,{ttBelow:!1});var ef=(y=function(t,e){return(y=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}y(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),eg=_().composed,ey=_().noop,ev=D().series,eb=D().seriesTypes,em=eb.column.prototype,ex=eb.scatter,eP=_().addEvent,eM=_().arrayMax,ew=_().arrayMin,eL=_().clamp,ek=_().extend,eA=_().isNumber,eS=_().merge,eT=_().pick,eC=_().pushUnique;function eN(){var t,e=this,i=this.len,o=this.coll,r=this.isXAxis,a=this.min,s=(this.max||0)-(a||0),n=0,h=i,l=i/s;("xAxis"===o||"yAxis"===o)&&(this.series.forEach(function(i){if(i.bubblePadding&&i.reserveSpace()){e.allowZoomOutside=!0,t=!0;var o=i.getColumn(r?"x":"y");if(r&&((i.onPoint||i).getRadii(0,0,i),i.onPoint&&(i.radii=i.onPoint.radii)),s>0){for(var p=o.length;p--;)if(eA(o[p])&&e.dataMin<=o[p]&&o[p]<=e.max){var c=i.radii&&i.radii[p]||0;n=Math.min((o[p]-a)*l-c,n),h=Math.max((o[p]-a)*l+c,h)}}}}),t&&s>0&&!this.logarithmic&&(h-=i,l*=(i+Math.max(0,n)-Math.min(h,i))/i,[["min","userMin",n],["max","userMax",h]].forEach(function(t){void 0===eT(e.options[t[0]],e[t[1]])&&(e[t[0]]+=t[2]/l)})))}function eX(){var t,e=this.ticks,i=this.tickPositions,o=this.dataMin,r=void 0===o?0:o,a=this.dataMax,s=void 0===a?0:a,n=this.categories,h=this.options.type;if(((null==n?void 0:n.length)||"category"===h)&&this.series.find(function(t){return t.bubblePadding}))for(var l=i.length;l--;){var p=e[i[l]],c=p.pos||0;(c>s||c<r)&&(null===(t=p.label)||void 0===t||t.hide())}}var eO=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ef(e,t),e.compose=function(t,e,i){en(e,i),eC(eg,"Series.Bubble")&&(eP(t,"foundExtremes",eN),eP(t,"afterRender",eX))},e.prototype.animate=function(t){!t&&this.points.length<this.options.animationLimit&&this.points.forEach(function(t){var e=t.graphic,i=t.plotX,o=t.plotY;e&&e.width&&(this.hasRendered||e.attr({x:void 0===i?0:i,y:void 0===o?0:o,width:1,height:1}),e.animate(this.markerAttribs(t),this.options.animation))},this)},e.prototype.getRadii=function(){var t,e,i,o=this.getColumn("z"),r=this.getColumn("y"),a=[],s=this.chart.bubbleZExtremes,n=this.getPxExtremes(),h=n.minPxSize,l=n.maxPxSize;if(!s){var p,c=Number.MAX_VALUE,d=-Number.MAX_VALUE;this.chart.series.forEach(function(t){if(t.bubblePadding&&t.reserveSpace()){var e=(t.onPoint||t).getZExtremes();e&&(c=Math.min(eT(c,e.zMin),e.zMin),d=Math.max(eT(d,e.zMax),e.zMax),p=!0)}}),p?(s={zMin:c,zMax:d},this.chart.bubbleZExtremes=s):s={zMin:0,zMax:0}}for(e=0,t=o.length;e<t;e++)i=o[e],a.push(this.getRadius(s.zMin,s.zMax,h,l,i,r&&r[e]));this.radii=a},e.prototype.getRadius=function(t,e,i,o,r,a){var s=this.options,n="width"!==s.sizeBy,h=s.zThreshold,l=e-t,p=.5;if(null===a||null===r)return null;if(eA(r)){if(s.sizeByAbsoluteValue&&(r=Math.abs(r-h),e=l=Math.max(e-h,Math.abs(t-h)),t=0),r<t)return i/2-1;l>0&&(p=(r-t)/l)}return n&&p>=0&&(p=Math.sqrt(p)),Math.ceil(i+p*(o-i))/2},e.prototype.hasData=function(){return!!this.dataTable.rowCount},e.prototype.markerAttribs=function(e,i){var o=t.prototype.markerAttribs.call(this,e,i),r=o.height,a=o.width;return this.chart.inverted?ek(o,{x:(e.plotX||0)-(void 0===a?0:a)/2,y:(e.plotY||0)-(void 0===r?0:r)/2}):o},e.prototype.pointAttribs=function(t,e){var i=this.options.marker,o=null==i?void 0:i.fillOpacity,r=ev.prototype.pointAttribs.call(this,t,e);return r["fill-opacity"]=null!=o?o:1,r},e.prototype.translate=function(){t.prototype.translate.call(this),this.getRadii(),this.translateBubble()},e.prototype.translateBubble=function(){for(var t=this.data,e=this.options,i=this.radii,o=this.getPxExtremes().minPxSize,r=t.length;r--;){var a=t[r],s=i?i[r]:0;"z"===this.zoneAxis&&(a.negative=(a.z||0)<(e.zThreshold||0)),eA(s)&&s>=o/2?(a.marker=ek(a.marker,{radius:s,width:2*s,height:2*s}),a.dlBox={x:a.plotX-s,y:a.plotY-s,width:2*s,height:2*s}):(a.shapeArgs=a.plotY=a.dlBox=void 0,a.isInside=!1)}},e.prototype.getPxExtremes=function(){var t=Math.min(this.chart.plotWidth,this.chart.plotHeight),e=function(e){var i;return"string"==typeof e&&(i=/%$/.test(e),e=parseInt(e,10)),i?t*e/100:e},i=e(eT(this.options.minSize,8)),o=Math.max(e(eT(this.options.maxSize,"20%")),i);return{minPxSize:i,maxPxSize:o}},e.prototype.getZExtremes=function(){var t=this.options,e=this.getColumn("z").filter(eA);if(e.length){var i=eT(t.zMin,eL(ew(e),!1===t.displayNegative?t.zThreshold||0:-Number.MAX_VALUE,Number.MAX_VALUE)),o=eT(t.zMax,eM(e));if(eA(i)&&eA(o))return{zMin:i,zMax:o}}},e.prototype.searchKDTree=function(e,i,o,r,a){return void 0===r&&(r=ey),void 0===a&&(a=ey),r=function(t,e,i){var o,r,a,s=t[i]||0,n=e[i]||0,h=!1;return s===n?a=t.index>e.index?t:e:s<0&&n<0?(a=s-((null===(o=t.marker)||void 0===o?void 0:o.radius)||0)>=n-((null===(r=e.marker)||void 0===r?void 0:r.radius)||0)?t:e,h=!0):a=s<n?t:e,[a,h]},a=function(t,e,i){return!i&&t>e||t<e},t.prototype.searchKDTree.call(this,e,i,o,r,a)},e.defaultOptions=eS(ex.defaultOptions,{dataLabels:{formatter:function(){var t=this.series.chart.numberFormatter,e=this.point.z;return eA(e)?t(e,-1):""},inside:!0,verticalAlign:"middle"},animationLimit:250,marker:{lineColor:null,lineWidth:1,fillOpacity:.5,radius:null,states:{hover:{radiusPlus:0}},symbol:"circle"},minSize:8,maxSize:"20%",softThreshold:!1,states:{hover:{halo:{size:5}}},tooltip:{pointFormat:"({point.x}, {point.y}), Size: {point.z}"},turboThreshold:0,zThreshold:0,zoneAxis:"z"}),e}(ex);ek(eO.prototype,{alignDataLabel:em.alignDataLabel,applyZones:ey,bubblePadding:!0,isBubble:!0,keysAffectYAxis:["y"],pointArrayMap:["y","z"],pointClass:eu,parallelArrays:["x","y","z"],trackerGroups:["group","dataLabelsGroup"],specialGroup:"group",zoneAxis:"z"}),eP(eO,"updatedData",function(t){delete t.target.chart.bubbleZExtremes}),eP(eO,"remove",function(t){delete t.target.chart.bubbleZExtremes}),D().registerSeriesType("bubble",eO);var eY=(v=function(t,e){return(v=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}v(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),eI=D().seriesTypes,eR=eI.column.prototype.pointClass.prototype,eE=eI.arearange.prototype.pointClass,e_=_().extend,ez=_().isNumber,eD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eY(e,t),e.prototype.isValid=function(){return ez(this.low)},e}(eE);e_(eD.prototype,{setState:eR.setState});var eB=(b=function(t,e){return(b=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}b(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),eW=_().noop,eH=D().seriesTypes,ej=eH.arearange,eF=eH.column,eq=eH.column.prototype,eG=_().addEvent,eV=_().clamp,eU=_().extend,eK=_().isNumber,eZ=_().merge,e$=_().pick,eQ={borderRadius:{where:"all"},pointRange:null,legendSymbol:"rectangle",marker:null,states:{hover:{halo:!1}}},eJ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return eB(e,t),e.prototype.setOptions=function(){return eZ(!0,arguments[0],{stacking:void 0}),ej.prototype.setOptions.apply(this,arguments)},e.prototype.translate=function(){return eq.translate.apply(this)},e.prototype.pointAttribs=function(){return eq.pointAttribs.apply(this,arguments)},e.prototype.translate3dPoints=function(){return eq.translate3dPoints.apply(this,arguments)},e.prototype.translate3dShapes=function(){return eq.translate3dShapes.apply(this,arguments)},e.prototype.afterColumnTranslate=function(){var t,e,i,o,r=this,a=this.yAxis,s=this.xAxis,n=s.startAngleRad,h=this.chart,l=this.xAxis.isRadial,p=Math.max(h.chartWidth,h.chartHeight)+999;this.points.forEach(function(c){var d=c.shapeArgs||{},u=r.options.minPointLength,f=c.plotY,g=a.translate(c.high,0,1,0,1);if(eK(g)&&eK(f)){if(c.plotHigh=eV(g,-p,p),c.plotLow=eV(f,-p,p),o=c.plotHigh,Math.abs(t=e$(c.rectPlotY,c.plotY)-c.plotHigh)<u?(e=u-t,t+=e,o-=e/2):t<0&&(t*=-1,o-=t),l&&r.polar)i=c.barX+n,c.shapeType="arc",c.shapeArgs=r.polar.arc(o+t,o,i,i+c.pointWidth);else{d.height=t,d.y=o;var y=d.x,v=void 0===y?0:y,b=d.width,m=void 0===b?0:b;c.shapeArgs=eZ(c.shapeArgs,r.crispCol(v,o,m,t)),c.tooltipPos=h.inverted?[a.len+a.pos-h.plotLeft-o-t/2,s.len+s.pos-h.plotTop-v-m/2,t]:[s.left-h.plotLeft+v+m/2,a.pos-h.plotTop+o+t/2,t]}}})},e.defaultOptions=eZ(eF.defaultOptions,ej.defaultOptions,eQ),e}(ej);eG(eJ,"afterColumnTranslate",function(){eJ.prototype.afterColumnTranslate.apply(this)},{order:5}),eU(eJ.prototype,{directTouch:!0,pointClass:eD,trackerGroups:["group","dataLabelsGroup"],adjustForMissingColumns:eq.adjustForMissingColumns,animate:eq.animate,crispCol:eq.crispCol,drawGraph:eW,drawPoints:eq.drawPoints,getSymbol:eW,drawTracker:eq.drawTracker,getColumnMetrics:eq.getColumnMetrics}),D().registerSeriesType("columnrange",eJ);var e0={},e1=(m=function(t,e){return(m=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}m(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),e2=D().seriesTypes.column,e3=_().clamp,e5=_().merge,e8=_().pick,e6=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return e1(e,t),e.prototype.translate=function(){var e=this.chart,i=this.options,o=this.dense=this.closestPointRange*this.xAxis.transA<2,r=this.borderWidth=e8(i.borderWidth,+!o),a=this.yAxis,s=i.threshold,n=e8(i.minPointLength,5),h=this.getColumnMetrics(),l=h.width,p=this.pointXOffset=h.offset,c=this.translatedThreshold=a.getThreshold(s),d=this.barW=Math.max(l,1+2*r);e.inverted&&(c-=.5),i.pointPadding&&(d=Math.ceil(d)),t.prototype.translate.call(this);for(var u=0,f=this.points;u<f.length;u++){var g=f[u],y=e8(g.yBottom,c),v=999+Math.abs(y),b=e3(g.plotY,-v,a.len+v),m=d/2,x=Math.min(b,y),P=Math.max(b,y)-x,M=g.plotX+p,w=void 0,L=void 0,k=void 0,A=void 0,S=void 0,T=void 0,C=void 0,N=void 0,X=void 0,O=void 0,Y=void 0;i.centerInCategory&&(M=this.adjustForMissingColumns(M,l,g,h)),g.barX=M,g.pointWidth=l,g.tooltipPos=e.inverted?[a.len+a.pos-e.plotLeft-b,this.xAxis.len-M-m,P]:[M+m,b+a.pos-e.plotTop,P],w=s+(g.total||g.y),"percent"===i.stacking&&(w=s+(g.y<0)?-100:100);var I=a.toPixels(w,!0);k=(L=e.plotHeight-I-(e.plotHeight-c))?m*(x-I)/L:0,A=L?m*(x+P-I)/L:0,T=M-k+m,C=M+k+m,N=M+A+m,X=M-A+m,O=x-n,Y=x+P,g.y<0&&(O=x,Y=x+P+n),e.inverted&&(S=a.width-x,L=I-(a.width-c),k=m*(I-S)/L,A=m*(I-(S-P))/L,C=(T=M+m+k)-2*k,N=M-A+m,X=M+A+m,O=x,Y=x+P-n,g.y<0&&(Y=x+P+n)),g.shapeType="path",g.shapeArgs={x:T,y:O,width:C-T,height:P,d:[["M",T,O],["L",C,O],["L",N,Y],["L",X,Y],["Z"]]}}},e.defaultOptions=e5(e2.defaultOptions,e0),e}(e2);D().registerSeriesType("columnpyramid",e6);var e4={color:"#000000",grouping:!1,linkedTo:":previous",tooltip:{pointFormat:'<span style="color:{point.color}">●</span> {series.name}: <b>{point.low}</b> - <b>{point.high}</b><br/>'},whiskerWidth:null},e9=(x=function(t,e){return(x=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}x(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),e7=D().seriesTypes.arearange,it=_().addEvent,ie=_().merge,ii=_().extend,io=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return e9(e,t),e.prototype.getColumnMetrics=function(){return this.linkedParent&&this.linkedParent.columnMetrics||tz().prototype.getColumnMetrics.call(this)},e.prototype.drawDataLabels=function(){var t=this.pointValKey;if(e7){e7.prototype.drawDataLabels.call(this);for(var e=0,i=this.points;e<i.length;e++){var o=i[e];o.y=o[t]}}},e.prototype.toYData=function(t){return[t.low,t.high]},e.defaultOptions=ie(tV.defaultOptions,e4),e}(tV);it(io,"afterTranslate",function(){for(var t=0,e=this.points;t<e.length;t++){var i=e[t];i.plotLow=i.plotY}},{order:0}),ii(io.prototype,{pointArrayMap:["low","high"],pointValKey:"high",doQuartiles:!1}),D().registerSeriesType("errorbar",io);var ir=(P=function(t,e){return(P=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}P(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ia=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ir(e,t),e.prototype.setState=function(t){this.state=t},e}(D().series.prototype.pointClass),is=(M=function(t,e){return(M=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}M(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),ih=_().noop,il=D().series,ip=D().seriesTypes.column,ic=_().clamp,id=_().isNumber,iu=_().extend,ig=_().merge,iy=_().pick,iv=_().pInt,ib=_().defined,im=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return is(e,t),e.prototype.translate=function(){var t=this.yAxis,e=this.options,i=t.center;this.generatePoints(),this.points.forEach(function(o){var r=ig(e.dial,o.dial),a=iv(r.radius)*i[2]/200,s=iv(r.baseLength)*a/100,n=iv(r.rearLength)*a/100,h=r.baseWidth,l=r.topWidth,p=e.overshoot,c=t.startAngleRad+t.translate(o.y,void 0,void 0,void 0,!0);(id(p)||!1===e.wrap)&&(p=id(p)?p/180*Math.PI:0,c=ic(c,t.startAngleRad-p,t.endAngleRad+p)),c=180*c/Math.PI,o.shapeType="path",o.shapeArgs={d:r.path||[["M",-n,-h/2],["L",s,-h/2],["L",a,-l/2],["L",a,l/2],["L",s,h/2],["L",-n,h/2],["Z"]],translateX:i[0],translateY:i[1],rotation:c},o.plotX=i[0],o.plotY=i[1],ib(o.y)&&t.max-t.min&&(o.percentage=(o.y-t.min)/(t.max-t.min)*100)})},e.prototype.drawPoints=function(){var t=this,e=t.chart,i=t.yAxis.center,o=t.pivot,r=t.options,a=r.pivot,s=e.renderer;t.points.forEach(function(i){var o=i.graphic,a=i.shapeArgs,n=a.d,h=ig(r.dial,i.dial);o?(o.animate(a),a.d=n):i.graphic=s[i.shapeType](a).addClass("highcharts-dial").add(t.group),e.styledMode||i.graphic[o?"animate":"attr"]({stroke:h.borderColor,"stroke-width":h.borderWidth,fill:h.backgroundColor})}),o?o.animate({translateX:i[0],translateY:i[1]}):a&&(t.pivot=s.circle(0,0,a.radius).attr({zIndex:2}).addClass("highcharts-pivot").translate(i[0],i[1]).add(t.group),e.styledMode||t.pivot.attr({fill:a.backgroundColor,stroke:a.borderColor,"stroke-width":a.borderWidth}))},e.prototype.animate=function(t){var e=this;t||e.points.forEach(function(t){var i=t.graphic;i&&(i.attr({rotation:180*e.yAxis.startAngleRad/Math.PI}),i.animate({rotation:t.shapeArgs.rotation},e.options.animation))})},e.prototype.render=function(){this.group=this.plotGroup("group","series",this.visible?"inherit":"hidden",this.options.zIndex,this.chart.seriesGroup),il.prototype.render.call(this),this.group.clip(this.chart.clipRect)},e.prototype.setData=function(t,e){il.prototype.setData.call(this,t,!1),this.processData(),this.generatePoints(),iy(e,!0)&&this.chart.redraw()},e.prototype.hasData=function(){return!!this.points.length},e.defaultOptions=ig(il.defaultOptions,{dataLabels:{borderColor:"#cccccc",borderRadius:3,borderWidth:1,crop:!1,defer:!1,enabled:!0,verticalAlign:"top",y:15,zIndex:2},dial:{backgroundColor:"#000000",baseLength:"70%",baseWidth:3,borderColor:"#cccccc",borderWidth:0,radius:"80%",rearLength:"10%",topWidth:1},pivot:{radius:5,borderWidth:0,borderColor:"#cccccc",backgroundColor:"#000000"},tooltip:{headerFormat:""},showInLegend:!1}),e}(il);iu(im.prototype,{angular:!0,directTouch:!0,drawGraph:ih,drawTracker:ip.prototype.drawTracker,fixedBox:!0,forceDL:!0,noSharedTooltip:!0,pointClass:ia,trackerGroups:["group","dataLabelsGroup"]}),D().registerSeriesType("gauge",im);var ix=I(620),iP=I.n(ix),iM=_().composed,iw=_().addEvent,iL=_().pushUnique;function ik(){var t,e,i,o,r=this;r.container&&(t=iw(r.container,"mousedown",function(t){e&&e(),i&&i(),(o=r.hoverPoint)&&o.series&&o.series.hasDraggableNodes&&o.series.options.draggable&&(o.series.onMouseDown(o,t),e=iw(r.container,"mousemove",function(t){return o&&o.series&&o.series.onMouseMove(o,t)}),i=iw(r.container.ownerDocument,"mouseup",function(t){return e(),i(),o&&o.series&&o.series.onMouseUp(o,t)}))})),iw(r,"destroy",function(){t()})}var iA={compose:function(t){iL(iM,"DragNodes")&&iw(t,"load",ik)},onMouseDown:function(t,e){var i,o=(null===(i=this.chart.pointer)||void 0===i?void 0:i.normalize(e))||e;t.fixedPosition={chartX:o.chartX,chartY:o.chartY,plotX:t.plotX,plotY:t.plotY},t.inDragMode=!0},onMouseMove:function(t,e){var i;if(t.fixedPosition&&t.inDragMode){var o=this.chart,r=(null===(i=o.pointer)||void 0===i?void 0:i.normalize(e))||e,a=t.fixedPosition.chartX-r.chartX,s=t.fixedPosition.chartY-r.chartY,n=o.graphLayoutsLookup,h=void 0,l=void 0;(Math.abs(a)>5||Math.abs(s)>5)&&(h=t.fixedPosition.plotX-a,l=t.fixedPosition.plotY-s,o.isInsidePlot(h,l)&&(t.plotX=h,t.plotY=l,t.hasDragged=!0,this.redrawHalo(t),n.forEach(function(t){t.restartSimulation()})))}},onMouseUp:function(t){t.fixedPosition&&(t.hasDragged&&(this.layout.enableSimulation?this.layout.start():this.chart.redraw()),t.inDragMode=t.hasDragged=!1,this.options.fixedDraggable||delete t.fixedPosition)},redrawHalo:function(t){t&&this.halo&&this.halo.attr({d:t.haloPath(this.options.states.hover.halo.size)})}},iS=_().setAnimation,iT=_().composed,iC=_().addEvent,iN=_().pushUnique;function iX(){this.graphLayoutsLookup&&(this.graphLayoutsLookup.forEach(function(t){t.updateSimulation()}),this.redraw())}function iO(){this.graphLayoutsLookup&&(this.graphLayoutsLookup.forEach(function(t){t.updateSimulation(!1)}),this.redraw())}function iY(){this.graphLayoutsLookup&&this.graphLayoutsLookup.forEach(function(t){t.stop()})}function iI(){var t,e=!1,i=function(i){i.maxIterations--&&isFinite(i.temperature)&&!i.isStable()&&!i.enableSimulation&&(i.beforeStep&&i.beforeStep(),i.step(),t=!1,e=!0)};if(this.graphLayoutsLookup){for(iS(!1,this),this.graphLayoutsLookup.forEach(function(t){return t.start()});!t;)t=!0,this.graphLayoutsLookup.forEach(i);e&&this.series.forEach(function(t){t&&t.layout&&t.render()})}}var iR={compose:function(t){iN(iT,"GraphLayout")&&(iC(t,"afterPrint",iX),iC(t,"beforePrint",iO),iC(t,"predraw",iY),iC(t,"render",iI))},integrations:{},layouts:{}},iE=I(960),i_=I.n(iE),iz=(w=function(t,e){return(w=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}w(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),iD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return iz(e,t),e.prototype.destroy=function(){var t;return(null===(t=this.series)||void 0===t?void 0:t.layout)&&this.series.layout.removeElementFromCollection(this,this.series.layout.nodes),el().prototype.destroy.apply(this,arguments)},e.prototype.firePointEvent=function(){var t=this.series.options;if(this.isParentNode&&t.parentNode){var e=t.allowPointSelect;t.allowPointSelect=t.parentNode.allowPointSelect,el().prototype.firePointEvent.apply(this,arguments),t.allowPointSelect=e}else el().prototype.firePointEvent.apply(this,arguments)},e.prototype.select=function(){var t=this.series.chart;this.isParentNode?(t.getSelectedPoints=t.getSelectedParentNodes,el().prototype.select.apply(this,arguments),t.getSelectedPoints=i_().prototype.getSelectedPoints):el().prototype.select.apply(this,arguments)},e}(D().seriesTypes.bubble.prototype.pointClass),iB=_().isNumber,iW={minSize:"10%",maxSize:"50%",sizeBy:"area",zoneAxis:"y",crisp:!1,tooltip:{pointFormat:"Value: {point.value}"},draggable:!0,useSimulation:!0,parentNode:{allowPointSelect:!1},dataLabels:{formatter:function(){var t=this.series.chart.numberFormatter,e=this.point.value;return iB(e)?t(e,-1):""},parentNodeFormatter:function(){return this.name||""},parentNodeTextPath:{enabled:!0},padding:0,style:{transition:"opacity 2000ms"}},layoutAlgorithm:{initialPositions:"circle",initialPositionRadius:20,bubblePadding:5,parentNodeLimit:!1,seriesInteraction:!0,dragBetweenSeries:!1,parentNodeOptions:{maxIterations:400,gravitationalConstant:.03,maxSpeed:50,initialPositionRadius:100,seriesInteraction:!0,marker:{fillColor:null,fillOpacity:1,lineWidth:null,lineColor:null,symbol:"circle"}},enableSimulation:!0,type:"packedbubble",integration:"packedbubble",maxIterations:1e3,splitSeries:!1,maxSpeed:5,gravitationalConstant:.01,friction:-.981},stickyTracking:!1},iH={attractive:function(t,e,i){var o=t.getMass(),r=-i.x*e*this.diffTemperature,a=-i.y*e*this.diffTemperature;t.fromNode.fixedPosition||(t.fromNode.plotX-=r*o.fromNode/t.fromNode.degree,t.fromNode.plotY-=a*o.fromNode/t.fromNode.degree),t.toNode.fixedPosition||(t.toNode.plotX+=r*o.toNode/t.toNode.degree,t.toNode.plotY+=a*o.toNode/t.toNode.degree)},attractiveForceFunction:function(t,e){return(e-t)/t},barycenter:function(){var t=this.options.gravitationalConstant||0,e=(this.barycenter.xFactor-(this.box.left+this.box.width)/2)*t,i=(this.barycenter.yFactor-(this.box.top+this.box.height)/2)*t;this.nodes.forEach(function(t){t.fixedPosition||(t.plotX-=e/t.mass/t.degree,t.plotY-=i/t.mass/t.degree)})},getK:function(t){return Math.pow(t.box.width*t.box.height/t.nodes.length,.5)},integrate:function(t,e){var i=-t.options.friction,o=t.options.maxSpeed,r=e.prevX,a=e.prevY,s=(e.plotX+e.dispX-r)*i,n=(e.plotY+e.dispY-a)*i,h=Math.abs,l=h(s)/(s||1),p=h(n)/(n||1),c=l*Math.min(o,Math.abs(s)),d=p*Math.min(o,Math.abs(n));e.prevX=e.plotX+e.dispX,e.prevY=e.plotY+e.dispY,e.plotX+=c,e.plotY+=d,e.temperature=t.vectorLength({x:c,y:d})},repulsive:function(t,e,i){var o=e*this.diffTemperature/t.mass/t.degree;t.fixedPosition||(t.plotX+=i.x*o,t.plotY+=i.y*o)},repulsiveForceFunction:function(t,e){return(e-t)/t*+(e>t)}},ij={barycenter:function(){for(var t,e,i=this.options.gravitationalConstant||0,o=this.box,r=this.nodes,a=Math.sqrt(r.length),s=0;s<r.length;s++){var n=r[s];if(!n.fixedPosition){var h=n.mass*a,l=n.plotX||0,p=n.plotY||0,c=n.series,d=c.parentNode;this.resolveSplitSeries(n)&&d&&!n.isParentNode?(t=d.plotX||0,e=d.plotY||0):(t=o.width/2,e=o.height/2),n.plotX=l-(l-t)*i/h,n.plotY=p-(p-e)*i/h,c.chart.hoverPoint===n&&c.redrawHalo&&c.halo&&c.redrawHalo(n)}}},getK:_().noop,integrate:iH.integrate,repulsive:function(t,e,i,o){var r=e*this.diffTemperature/t.mass/t.degree,a=i.x*r,s=i.y*r;t.fixedPosition||(t.plotX+=a,t.plotY+=s),o.fixedPosition||(o.plotX-=a,o.plotY-=s)},repulsiveForceFunction:function(t,e,i,o){return Math.min(t,(i.marker.radius+o.marker.radius)/2)}},iF={attractive:function(t,e,i,o){var r=t.getMass(),a=i.x/o*e,s=i.y/o*e;t.fromNode.fixedPosition||(t.fromNode.dispX-=a*r.fromNode/t.fromNode.degree,t.fromNode.dispY-=s*r.fromNode/t.fromNode.degree),t.toNode.fixedPosition||(t.toNode.dispX+=a*r.toNode/t.toNode.degree,t.toNode.dispY+=s*r.toNode/t.toNode.degree)},attractiveForceFunction:function(t,e){return t*t/e},barycenter:function(){var t=this.options.gravitationalConstant,e=this.barycenter.xFactor,i=this.barycenter.yFactor;this.nodes.forEach(function(o){if(!o.fixedPosition){var r=o.getDegree(),a=r*(1+r/2);o.dispX+=(e-o.plotX)*t*a/o.degree,o.dispY+=(i-o.plotY)*t*a/o.degree}})},getK:function(t){return Math.pow(t.box.width*t.box.height/t.nodes.length,.3)},integrate:function(t,e){e.dispX+=e.dispX*t.options.friction,e.dispY+=e.dispY*t.options.friction;var i=e.temperature=t.vectorLength({x:e.dispX,y:e.dispY});0!==i&&(e.plotX+=e.dispX/i*Math.min(Math.abs(e.dispX),t.temperature),e.plotY+=e.dispY/i*Math.min(Math.abs(e.dispY),t.temperature))},repulsive:function(t,e,i,o){t.dispX+=i.x/o*e/t.degree,t.dispY+=i.y/o*e/t.degree},repulsiveForceFunction:function(t,e){return e*e/t}},iq=function(){function t(t){this.body=!1,this.isEmpty=!1,this.isInternal=!1,this.nodes=[],this.box=t,this.boxSize=Math.min(t.width,t.height)}return t.prototype.divideBox=function(){var e=this.box.width/2,i=this.box.height/2;this.nodes[0]=new t({left:this.box.left,top:this.box.top,width:e,height:i}),this.nodes[1]=new t({left:this.box.left+e,top:this.box.top,width:e,height:i}),this.nodes[2]=new t({left:this.box.left+e,top:this.box.top+i,width:e,height:i}),this.nodes[3]=new t({left:this.box.left,top:this.box.top+i,width:e,height:i})},t.prototype.getBoxPosition=function(t){var e=t.plotX<this.box.left+this.box.width/2,i=t.plotY<this.box.top+this.box.height/2;return e?3*!i:i?1:2},t.prototype.insert=function(e,i){var o;this.isInternal?this.nodes[this.getBoxPosition(e)].insert(e,i-1):(this.isEmpty=!1,this.body?i?(this.isInternal=!0,this.divideBox(),!0!==this.body&&(this.nodes[this.getBoxPosition(this.body)].insert(this.body,i-1),this.body=!0),this.nodes[this.getBoxPosition(e)].insert(e,i-1)):((o=new t({top:e.plotX||NaN,left:e.plotY||NaN,width:.1,height:.1})).body=e,o.isInternal=!1,this.nodes.push(o)):(this.isInternal=!1,this.body=e))},t.prototype.updateMassAndCenter=function(){var t=0,e=0,i=0;if(this.isInternal){for(var o=0,r=this.nodes;o<r.length;o++){var a=r[o];a.isEmpty||(t+=a.mass,e+=a.plotX*a.mass,i+=a.plotY*a.mass)}e/=t,i/=t}else this.body&&(t=this.body.mass,e=this.body.plotX,i=this.body.plotY);this.mass=t,this.plotX=e,this.plotY=i},t}(),iG=function(){function t(t,e,i,o){this.box={left:t,top:e,width:i,height:o},this.maxDepth=25,this.root=new iq(this.box),this.root.isInternal=!0,this.root.isRoot=!0,this.root.divideBox()}return t.prototype.calculateMassAndCenter=function(){this.visitNodeRecursive(null,null,function(t){t.updateMassAndCenter()})},t.prototype.insertNodes=function(t){for(var e=0;e<t.length;e++){var i=t[e];this.root.insert(i,this.maxDepth)}},t.prototype.visitNodeRecursive=function(t,e,i){var o;if(t||(t=this.root),t===this.root&&e&&(o=e(t)),!1!==o){for(var r=0,a=t.nodes;r<a.length;r++){var s=a[r];if(s.isInternal){if(e&&(o=e(s)),!1===o)continue;this.visitNodeRecursive(s,e,i)}else s.body&&e&&e(s.body);i&&i(s)}t===this.root&&i&&i(t)}},t}(),iV=_().win,iU=_().clamp,iK=_().defined,iZ=_().isFunction,i$=_().fireEvent,iQ=_().pick,iJ=function(){function t(){this.box={},this.currentStep=0,this.initialRendering=!0,this.links=[],this.nodes=[],this.series=[],this.simulation=!1}return t.compose=function(e){iR.compose(e),iR.integrations.euler=iF,iR.integrations.verlet=iH,iR.layouts["reingold-fruchterman"]=t},t.prototype.init=function(t){this.options=t,this.nodes=[],this.links=[],this.series=[],this.box={x:0,y:0,width:0,height:0},this.setInitialRendering(!0),this.integration=iR.integrations[t.integration],this.enableSimulation=t.enableSimulation,this.attractiveForce=iQ(t.attractiveForce,this.integration.attractiveForceFunction),this.repulsiveForce=iQ(t.repulsiveForce,this.integration.repulsiveForceFunction),this.approximation=t.approximation},t.prototype.updateSimulation=function(t){this.enableSimulation=iQ(t,this.options.enableSimulation)},t.prototype.start=function(){var t=this.series,e=this.options;this.currentStep=0,this.forces=t[0]&&t[0].forces||[],this.chart=t[0]&&t[0].chart,this.initialRendering&&(this.initPositions(),t.forEach(function(t){t.finishedAnimating=!0,t.render()})),this.setK(),this.resetSimulation(e),this.enableSimulation&&this.step()},t.prototype.step=function(){var t=this,e=this.series;this.currentStep++,"barnes-hut"===this.approximation&&(this.createQuadTree(),this.quadTree.calculateMassAndCenter());for(var i=0,o=this.forces||[];i<o.length;i++)this[o[i]+"Forces"](this.temperature);if(this.applyLimits(),this.temperature=this.coolDown(this.startTemperature,this.diffTemperature,this.currentStep),this.prevSystemTemperature=this.systemTemperature,this.systemTemperature=this.getSystemTemperature(),this.enableSimulation){for(var r=0;r<e.length;r++){var a=e[r];a.chart&&a.render()}this.maxIterations--&&isFinite(this.temperature)&&!this.isStable()?(this.simulation&&iV.cancelAnimationFrame(this.simulation),this.simulation=iV.requestAnimationFrame(function(){return t.step()})):(this.simulation=!1,this.series.forEach(function(t){i$(t,"afterSimulation")}))}},t.prototype.stop=function(){this.simulation&&iV.cancelAnimationFrame(this.simulation)},t.prototype.setArea=function(t,e,i,o){this.box={left:t,top:e,width:i,height:o}},t.prototype.setK=function(){this.k=this.options.linkLength||this.integration.getK(this)},t.prototype.addElementsToCollection=function(t,e){for(var i=0;i<t.length;i++){var o=t[i];-1===e.indexOf(o)&&e.push(o)}},t.prototype.removeElementFromCollection=function(t,e){var i=e.indexOf(t);-1!==i&&e.splice(i,1)},t.prototype.clear=function(){this.nodes.length=0,this.links.length=0,this.series.length=0,this.resetSimulation()},t.prototype.resetSimulation=function(){this.forcedStop=!1,this.systemTemperature=0,this.setMaxIterations(),this.setTemperature(),this.setDiffTemperature()},t.prototype.restartSimulation=function(){this.simulation?this.resetSimulation():(this.setInitialRendering(!1),this.enableSimulation?this.start():this.setMaxIterations(1),this.chart&&this.chart.redraw(),this.setInitialRendering(!0))},t.prototype.setMaxIterations=function(t){this.maxIterations=iQ(t,this.options.maxIterations)},t.prototype.setTemperature=function(){this.temperature=this.startTemperature=Math.sqrt(this.nodes.length)},t.prototype.setDiffTemperature=function(){this.diffTemperature=this.startTemperature/(this.options.maxIterations+1)},t.prototype.setInitialRendering=function(t){this.initialRendering=t},t.prototype.createQuadTree=function(){this.quadTree=new iG(this.box.left,this.box.top,this.box.width,this.box.height),this.quadTree.insertNodes(this.nodes)},t.prototype.initPositions=function(){var t=this.options.initialPositions;if(iZ(t)){t.call(this);for(var e=0,i=this.nodes;e<i.length;e++){var o=i[e];iK(o.prevX)||(o.prevX=o.plotX),iK(o.prevY)||(o.prevY=o.plotY),o.dispX=0,o.dispY=0}}else"circle"===t?this.setCircularPositions():this.setRandomPositions()},t.prototype.setCircularPositions=function(){for(var t,e=this.box,i=this.nodes,o=2*Math.PI/(i.length+1),r=i.filter(function(t){return 0===t.linksTo.length}),a={},s=this.options.initialPositionRadius,n=function(t){for(var e=0,i=t.linksFrom||[];e<i.length;e++){var o=i[e];a[o.toNode.id]||(a[o.toNode.id]=!0,h.push(o.toNode),n(o.toNode))}},h=[],l=0;l<r.length;l++){var p=r[l];h.push(p),n(p)}if(h.length)for(var c=0;c<i.length;c++){var d=i[c];-1===h.indexOf(d)&&h.push(d)}else h=i;for(var u=0,f=h.length;u<f;++u)(t=h[u]).plotX=t.prevX=iQ(t.plotX,e.width/2+s*Math.cos(u*o)),t.plotY=t.prevY=iQ(t.plotY,e.height/2+s*Math.sin(u*o)),t.dispX=0,t.dispY=0},t.prototype.setRandomPositions=function(){for(var t,e=this.box,i=this.nodes,o=i.length+1,r=function(t){var e=t*t/Math.PI;return e-Math.floor(e)},a=0,s=i.length;a<s;++a)(t=i[a]).plotX=t.prevX=iQ(t.plotX,e.width*r(a)),t.plotY=t.prevY=iQ(t.plotY,e.height*r(o+a)),t.dispX=0,t.dispY=0},t.prototype.force=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];this.integration[t].apply(this,e)},t.prototype.barycenterForces=function(){this.getBarycenter(),this.force("barycenter")},t.prototype.getBarycenter=function(){for(var t=0,e=0,i=0,o=0,r=this.nodes;o<r.length;o++){var a=r[o];e+=a.plotX*a.mass,i+=a.plotY*a.mass,t+=a.mass}return this.barycenter={x:e,y:i,xFactor:e/t,yFactor:i/t},this.barycenter},t.prototype.barnesHutApproximation=function(t,e){var i,o,r=this.getDistXY(t,e),a=this.vectorLength(r);return t!==e&&0!==a&&(e.isInternal?e.boxSize/a<this.options.theta&&0!==a?(o=this.repulsiveForce(a,this.k),this.force("repulsive",t,o*e.mass,r,a),i=!1):i=!0:(o=this.repulsiveForce(a,this.k),this.force("repulsive",t,o*e.mass,r,a))),i},t.prototype.repulsiveForces=function(){var t=this;if("barnes-hut"===this.approximation)for(var e=function(e){i.quadTree.visitNodeRecursive(null,function(i){return t.barnesHutApproximation(e,i)})},i=this,o=0,r=this.nodes;o<r.length;o++){var a=r[o];e(a)}else for(var s=void 0,n=void 0,h=void 0,l=0,p=this.nodes;l<p.length;l++)for(var a=p[l],c=0,d=this.nodes;c<d.length;c++){var u=d[c];a===u||a.fixedPosition||(h=this.getDistXY(a,u),0!==(n=this.vectorLength(h))&&(s=this.repulsiveForce(n,this.k),this.force("repulsive",a,s*u.mass,h,n)))}},t.prototype.attractiveForces=function(){for(var t,e,i,o=0,r=this.links;o<r.length;o++){var a=r[o];a.fromNode&&a.toNode&&(t=this.getDistXY(a.fromNode,a.toNode),0!==(e=this.vectorLength(t))&&(i=this.attractiveForce(e,this.k),this.force("attractive",a,i,t,e)))}},t.prototype.applyLimits=function(){for(var t=this.nodes,e=0;e<t.length;e++){var i=t[e];!i.fixedPosition&&(this.integration.integrate(this,i),this.applyLimitBox(i,this.box),i.dispX=0,i.dispY=0)}},t.prototype.applyLimitBox=function(t,e){var i=t.radius;t.plotX=iU(t.plotX,e.left+i,e.width-i),t.plotY=iU(t.plotY,e.top+i,e.height-i)},t.prototype.coolDown=function(t,e,i){return t-e*i},t.prototype.isStable=function(){return 1e-5>Math.abs(this.systemTemperature-this.prevSystemTemperature)||this.temperature<=0},t.prototype.getSystemTemperature=function(){for(var t=0,e=0,i=this.nodes;e<i.length;e++)t+=i[e].temperature;return t},t.prototype.vectorLength=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.prototype.getDistR=function(t,e){var i=this.getDistXY(t,e);return this.vectorLength(i)},t.prototype.getDistXY=function(t,e){var i=t.plotX-e.plotX,o=t.plotY-e.plotY;return{x:i,y:o,absX:Math.abs(i),absY:Math.abs(o)}},t}(),i0=(L=function(t,e){return(L=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}L(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),i1=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,a=e.length;r<a;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},i2=_().addEvent,i3=_().defined,i5=_().pick;function i8(){var t=this.series,e=[];return t.forEach(function(t){t.parentNode&&t.parentNode.selected&&e.push(t.parentNode)}),e}function i6(){this.allDataPoints&&delete this.allDataPoints}var i4=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.index=NaN,e.nodes=[],e.series=[],e}return i0(e,t),e.compose=function(t){iJ.compose(t),iR.integrations.packedbubble=ij,iR.layouts.packedbubble=e;var i=t.prototype;i.getSelectedParentNodes||(i2(t,"beforeRedraw",i6),i.getSelectedParentNodes=i8),i.allParentNodes||(i.allParentNodes=[])},e.prototype.beforeStep=function(){this.options.marker&&this.series.forEach(function(t){t&&t.calculateParentRadius()})},e.prototype.isStable=function(){var t=Math.abs(this.prevSystemTemperature-this.systemTemperature);return 1>Math.abs(10*this.systemTemperature/Math.sqrt(this.nodes.length))&&t<1e-5||this.temperature<=0},e.prototype.setCircularPositions=function(){for(var t,e,i,o=this.box,r=i1(i1([],this.nodes,!0),(null===(t=null===this||void 0===this?void 0:this.chart)||void 0===t?void 0:t.allParentNodes)||[],!0),a=2*Math.PI/(r.length+1),s=this.options.initialPositionRadius,n=0,h=0;h<r.length;h++){var l=r[h];this.resolveSplitSeries(l)&&!l.isParentNode?(e=l.series.parentNode.plotX,i=l.series.parentNode.plotY):(e=o.width/2,i=o.height/2),l.plotX=l.prevX=i5(l.plotX,e+s*Math.cos(l.index||n*a)),l.plotY=l.prevY=i5(l.plotY,i+s*Math.sin(l.index||n*a)),l.dispX=0,l.dispY=0,n++}},e.prototype.repulsiveForces=function(){for(var t,e,i,o=this.options,r=this.k,a=o.bubblePadding,s=void 0===a?0:a,n=o.seriesInteraction,h=i1(i1([],this.nodes,!0),(null===(t=null===this||void 0===this?void 0:this.chart)||void 0===t?void 0:t.allParentNodes)||[],!0),l=0;l<h.length;l++){var p=h[l],c=p.series,d=p.fixedPosition,u=((null===(e=p.marker)||void 0===e?void 0:e.radius)||0)+s;p.degree=p.mass,p.neighbours=0;for(var f=0;f<h.length;f++){var g=h[f],y=g.series;if(p!==g&&!d&&(n||c===y)&&!(c===y&&(g.isParentNode||p.isParentNode))){var v=this.getDistXY(p,g),b=this.vectorLength(v)-(u+((null===(i=g.marker)||void 0===i?void 0:i.radius)||0)),m=void 0;b<0&&(p.degree+=.01,m=this.repulsiveForce(-b/Math.sqrt(++p.neighbours),r,p,g)*g.mass),this.force("repulsive",p,m||0,v,g,b)}}}},e.prototype.resolveSplitSeries=function(t){var e,i,o,r,a,s,n,h,l=null===(o=null===(i=null===(e=t.series)||void 0===e?void 0:e.options)||void 0===i?void 0:i.layoutAlgorithm)||void 0===o?void 0:o.splitSeries;return!i3(l)&&(null===(h=null===(n=null===(s=null===(a=null===(r=t.series.chart)||void 0===r?void 0:r.options)||void 0===a?void 0:a.plotOptions)||void 0===s?void 0:s.packedbubble)||void 0===n?void 0:n.layoutAlgorithm)||void 0===h?void 0:h.splitSeries)||l||!1},e.prototype.applyLimitBox=function(e,i){var o,r;this.resolveSplitSeries(e)&&!e.isParentNode&&this.options.parentNodeLimit&&(o=this.getDistXY(e,e.series.parentNode),(r=e.series.parentNodeRadius-e.marker.radius-this.vectorLength(o))<0&&r>-2*e.marker.radius&&(e.plotX-=.01*o.x,e.plotY-=.01*o.y)),t.prototype.applyLimitBox.call(this,e,i)},e}(iJ);iR.layouts.packedbubble=i4;var i9=_().merge,i7=_().syncTimeout,ot=_().animObject,oe={initDataLabels:function(){var t=this.options.dataLabels;if(!this.dataLabelsGroup){var e=this.initDataLabelsGroup();return!this.chart.styledMode&&(null==t?void 0:t.style)&&e.css(t.style),e.attr({opacity:0}),this.visible&&e.show(),e}return this.dataLabelsGroup.attr(i9({opacity:1},this.getPlotBox("data-labels"))),this.dataLabelsGroup},initDataLabelsDefer:function(){var t,e=this,i=this.options.dataLabels;(null==i?void 0:i.defer)&&(null===(t=this.options.layoutAlgorithm)||void 0===t?void 0:t.enableSimulation)?i7(function(){e.deferDataLabels=!1},i?ot(i.animation).defer:0):this.deferDataLabels=!1}},oi=I(28),oo=I.n(oi),or=_().deg2rad,oa=_().addEvent,os=_().merge,on=_().uniqueKey,oh=_().defined,ol=_().extend;function op(t,e){var i=this;e=os(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);var o=this.renderer.url,r=this.text||this,a=r.textPath,s=e.attributes,n=e.enabled;if(t=t||a&&a.path,a&&a.undo(),t&&n){var h=oa(r,"afterModifyTree",function(e){if(t&&n){var a=t.attr("id");a||t.attr("id",a=on());var h={x:0,y:0};oh(s.dx)&&(h.dx=s.dx,delete s.dx),oh(s.dy)&&(h.dy=s.dy,delete s.dy),r.attr(h),i.attr({transform:""}),i.box&&(i.box=i.box.destroy());var l=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:ol(s,{"text-anchor":s.textAnchor,href:""+o+"#".concat(a)}),children:l}}});r.textPath={path:t,undo:h}}else r.attr({dx:0,dy:0}),delete r.textPath;return this.added&&(r.textCache="",this.renderer.buildText(r)),this}function oc(t){var e,i=t.bBox,o=null===(e=this.element)||void 0===e?void 0:e.querySelector("textPath");if(o){for(var r=[],a=this.renderer.fontMetrics(this.element),s=a.b,n=a.h-s,h=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),l=o.innerHTML.replace(h,"").split(/<tspan class="highcharts-br"[^>]*>/),p=l.length,c=function(t,e){var i=e.x,r=e.y,a=(o.getRotationOfChar(t)-90)*or,h=Math.cos(a),l=Math.sin(a);return[[i-n*h,r-n*l],[i+s*h,r+s*l]]},d=0,u=0;u<p;u++){for(var f=l[u].length,g=0;g<f;g+=5)try{var y=d+g+u,v=c(y,o.getStartPositionOfChar(y)),b=v[0],m=v[1];0===g?(r.push(m),r.push(b)):(0===u&&r.unshift(m),u===p-1&&r.push(b))}catch(t){break}d+=f-1;try{var y=d+u,x=o.getEndPositionOfChar(y),P=c(y,x),b=P[0],m=P[1];r.unshift(m),r.unshift(b)}catch(t){break}}r.length&&r.push(r[0].slice()),i.polygon=r}return i}function od(t){var e,i=t.labelOptions,o=t.point,r=i[o.formatPrefix+"TextPath"]||i.textPath;r&&!i.useHTML&&(this.setTextPath((null===(e=o.getDataLabelPath)||void 0===e?void 0:e.call(o,this))||o.graphic,r),o.dataLabelPath&&!r.enabled&&(o.dataLabelPath=o.dataLabelPath.destroy()))}var ou=(k=function(t,e){return(k=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}k(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),of=iP().parse,og=_().noop,oy=D().series.prototype,ov=D().seriesTypes.bubble,ob=oe.initDataLabels,om=oe.initDataLabelsDefer,ox=_().addEvent,oP=_().clamp,oM=_().defined,ow=_().extend,oL=_().fireEvent,ok=_().isArray,oA=_().isNumber,oS=_().merge,oT=_().pick;({compose:function(t){oa(t,"afterGetBBox",oc),oa(t,"beforeAddingDataLabel",od);var e=t.prototype;e.setTextPath||(e.setTextPath=op)}}).compose(oo());var oC=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.parentNodeMass=0,e.deferDataLabels=!0,e}return ou(e,t),e.compose=function(t,e,i){ov.compose(t,e,i),iA.compose(e),i4.compose(e)},e.prototype.accumulateAllPoints=function(){for(var t=this.chart,e=[],i=0,o=t.series;i<o.length;i++){var r=o[i];if(r.is("packedbubble")&&r.reserveSpace())for(var a=r.getColumn("value"),s=0;s<a.length;s++)e.push([null,null,a[s],r.index,s,{id:s,marker:{radius:0}}])}return e},e.prototype.addLayout=function(){var t,e=this.options.layoutAlgorithm=this.options.layoutAlgorithm||{},i=e.type||"packedbubble",o=this.chart.options.chart,r=this.chart.graphLayoutsStorage,a=this.chart.graphLayoutsLookup;r||(this.chart.graphLayoutsStorage=r={},this.chart.graphLayoutsLookup=a=[]),(t=r[i])||(e.enableSimulation=oM(o.forExport)?!o.forExport:e.enableSimulation,r[i]=t=new iR.layouts[i],t.init(e),a.splice(t.index,0,t)),this.layout=t,this.points.forEach(function(t){t.mass=2,t.degree=1,t.collisionNmb=1}),t.setArea(0,0,this.chart.plotWidth,this.chart.plotHeight),t.addElementsToCollection([this],t.series),t.addElementsToCollection(this.points,t.nodes)},e.prototype.addSeriesLayout=function(){var t=this.options.layoutAlgorithm=this.options.layoutAlgorithm||{},e=t.type||"packedbubble",i=this.chart.graphLayoutsStorage,o=this.chart.graphLayoutsLookup,r=oS(t,t.parentNodeOptions,{enableSimulation:this.layout.options.enableSimulation}),a=i[e+"-series"];a||(i[e+"-series"]=a=new iR.layouts[e],a.init(r),o.splice(a.index,0,a)),this.parentNodeLayout=a,this.createParentNodes()},e.prototype.calculateParentRadius=function(){var t=this.seriesBox();this.parentNodeRadius=oP(Math.sqrt(2*this.parentNodeMass/Math.PI)+20,20,t?Math.max(Math.sqrt(Math.pow(t.width,2)+Math.pow(t.height,2))/2+20,20):Math.sqrt(2*this.parentNodeMass/Math.PI)+20),this.parentNode&&(this.parentNode.marker.radius=this.parentNode.radius=this.parentNodeRadius)},e.prototype.calculateZExtremes=function(){var t=this.chart.series,e=this.options.zMin,i=this.options.zMax,o=1/0,r=-1/0;return e&&i?[e,i]:(t.forEach(function(t){t.getColumn("value").forEach(function(t){oM(t)&&(t>r&&(r=t),t<o&&(o=t))})}),[e=oT(e,o),i=oT(i,r)])},e.prototype.checkOverlap=function(t,e){var i=t[0]-e[0],o=t[1]-e[1];return Math.sqrt(i*i+o*o)-Math.abs(t[2]+e[2])<-.001},e.prototype.createParentNodes=function(){var t,e=this,i=this.pointClass,o=this.chart,r=this.parentNodeLayout,a=this.layout.options,s=this.parentNode,n={radius:this.parentNodeRadius,lineColor:this.color,fillColor:of(this.color).brighten(.4).get()};a.parentNodeOptions&&(n=oS(a.parentNodeOptions.marker||{},n)),this.parentNodeMass=0,this.points.forEach(function(t){e.parentNodeMass+=Math.PI*Math.pow(t.marker.radius,2)}),this.calculateParentRadius(),r.nodes.forEach(function(i){i.seriesIndex===e.index&&(t=!0)}),r.setArea(0,0,o.plotWidth,o.plotHeight),t||(s||(s=new i(this,{mass:this.parentNodeRadius/2,marker:n,dataLabels:{inside:!1},states:{normal:{marker:n},hover:{marker:n}},dataLabelOnNull:!0,degree:this.parentNodeRadius,isParentNode:!0,seriesIndex:this.index}),this.chart.allParentNodes.push(s)),this.parentNode&&(s.plotX=this.parentNode.plotX,s.plotY=this.parentNode.plotY),this.parentNode=s,r.addElementsToCollection([this],r.series),r.addElementsToCollection([s],r.nodes))},e.prototype.deferLayout=function(){var t=this.options.layoutAlgorithm;this.visible&&(this.addLayout(),t.splitSeries&&this.addSeriesLayout())},e.prototype.destroy=function(){var t=this;this.chart.graphLayoutsLookup&&this.chart.graphLayoutsLookup.forEach(function(e){e.removeElementFromCollection(t,e.series)},this),this.parentNode&&this.parentNodeLayout&&(this.parentNodeLayout.removeElementFromCollection(this.parentNode,this.parentNodeLayout.nodes),this.parentNode.dataLabel&&(this.parentNode.dataLabel=this.parentNode.dataLabel.destroy())),oy.destroy.apply(this,arguments)},e.prototype.drawDataLabels=function(){!this.deferDataLabels&&(oy.drawDataLabels.call(this,this.points),this.parentNode&&(this.parentNode.formatPrefix="parentNode",oy.drawDataLabels.call(this,[this.parentNode])))},e.prototype.drawGraph=function(){if(this.layout&&this.layout.options.splitSeries){var t,e=this.chart,i=this.layout.options.parentNodeOptions.marker,o={fill:i.fillColor||of(this.color).brighten(.4).get(),opacity:i.fillOpacity,stroke:i.lineColor||this.color,"stroke-width":oT(i.lineWidth,this.options.lineWidth)},r={};this.parentNodesGroup=this.plotGroup("parentNodesGroup","parentNode",this.visible?"inherit":"hidden",.1,e.seriesGroup),null===(t=this.group)||void 0===t||t.attr({zIndex:2}),this.calculateParentRadius(),this.parentNode&&oM(this.parentNode.plotX)&&oM(this.parentNode.plotY)&&oM(this.parentNodeRadius)&&(r=oS({x:this.parentNode.plotX-this.parentNodeRadius,y:this.parentNode.plotY-this.parentNodeRadius,width:2*this.parentNodeRadius,height:2*this.parentNodeRadius},o),this.parentNode.graphic||(this.graph=this.parentNode.graphic=e.renderer.symbol(o.symbol).add(this.parentNodesGroup)),this.parentNode.graphic.attr(r))}},e.prototype.drawTracker=function(){var e,i=this.parentNode;t.prototype.drawTracker.call(this),i&&(e=ok(i.dataLabels)?i.dataLabels:i.dataLabel?[i.dataLabel]:[],i.graphic&&(i.graphic.element.point=i),e.forEach(function(t){(t.div||t.element).point=i}))},e.prototype.getPointRadius=function(){var t,e,i,o,r=this,a=this.chart,s=a.plotWidth,n=a.plotHeight,h=this.options,l=h.useSimulation,p=Math.min(s,n),c={},d=[],u=a.allDataPoints||[],f=u.length;["minSize","maxSize"].forEach(function(t){var e=parseInt(h[t],10),i=/%$/.test(h[t]);c[t]=i?p*e/100:e*Math.sqrt(f)}),a.minRadius=t=c.minSize/Math.sqrt(f),a.maxRadius=e=c.maxSize/Math.sqrt(f);var g=l?this.calculateZExtremes():[t,e];u.forEach(function(a,s){i=l?oP(a[2],g[0],g[1]):a[2],0===(o=r.getRadius(g[0],g[1],t,e,i))&&(o=null),u[s][2]=o,d.push(o)}),this.radii=d},e.prototype.init=function(){return oy.init.apply(this,arguments),om.call(this),this.eventsToUnbind.push(ox(this,"updatedData",function(){var t=this;this.chart.series.forEach(function(e){e.type===t.type&&(e.isDirty=!0)},this)})),this},e.prototype.onMouseUp=function(t){if(t.fixedPosition&&!t.removed){var e,i=this.layout,o=this.parentNodeLayout;!t.isParentNode&&o&&i.options.dragBetweenSeries&&o.nodes.forEach(function(o){t&&t.marker&&o!==t.series.parentNode&&(e=i.getDistXY(t,o),i.vectorLength(e)-o.marker.radius-t.marker.radius<0&&(o.series.addPoint(oS(t.options,{plotX:t.plotX,plotY:t.plotY}),!1),i.removeElementFromCollection(t,i.nodes),t.remove()))}),iA.onMouseUp.apply(this,arguments)}},e.prototype.placeBubbles=function(t){var e,i,o=this.checkOverlap,r=this.positionBubble,a=[],s=1,n=0,h=0,l=[],p=t.sort(function(t,e){return e[2]-t[2]});if(p.length){if(a.push([[0,0,p[0][2],p[0][3],p[0][4]]]),p.length>1)for(a.push([[0,0-p[1][2]-p[0][2],p[1][2],p[1][3],p[1][4]]]),i=2;i<p.length;i++)p[i][2]=p[i][2]||1,o(e=r(a[s][n],a[s-1][h],p[i]),a[s][0])?(a.push([]),h=0,a[s+1].push(r(a[s][n],a[s][0],p[i])),s++,n=0):s>1&&a[s-1][h+1]&&o(e,a[s-1][h+1])?(h++,a[s].push(r(a[s][n],a[s-1][h],p[i])),n++):(n++,a[s].push(e));this.chart.stages=a,this.chart.rawPositions=[].concat.apply([],a),this.resizeRadius(),l=this.chart.rawPositions}return l},e.prototype.pointAttribs=function(t,e){var i=this.options,o=t&&t.isParentNode,r=i.marker;o&&i.layoutAlgorithm&&i.layoutAlgorithm.parentNodeOptions&&(r=i.layoutAlgorithm.parentNodeOptions.marker);var a=r.fillOpacity,s=oy.pointAttribs.call(this,t,e);return 1!==a&&(s["fill-opacity"]=a),s},e.prototype.positionBubble=function(t,e,i){var o=Math.asin,r=Math.acos,a=Math.pow,s=Math.abs,n=(0,Math.sqrt)(a(t[0]-e[0],2)+a(t[1]-e[1],2)),h=r((a(n,2)+a(i[2]+e[2],2)-a(i[2]+t[2],2))/(2*(i[2]+e[2])*n)),l=o(s(t[0]-e[0])/n),p=(t[1]-e[1]<0?0:Math.PI)+h+l*((t[0]-e[0])*(t[1]-e[1])<0?1:-1),c=Math.cos(p),d=Math.sin(p);return[e[0]+(e[2]+i[2])*d,e[1]-(e[2]+i[2])*c,i[2],i[3],i[4]]},e.prototype.render=function(){var t=[];oy.render.apply(this,arguments),!this.options.dataLabels.allowOverlap&&(this.data.forEach(function(e){ok(e.dataLabels)&&e.dataLabels.forEach(function(e){t.push(e)})}),this.options.useSimulation&&this.chart.hideOverlappingLabels(t))},e.prototype.resizeRadius=function(){var t,e,i,o,r,a=this.chart,s=a.rawPositions,n=Math.min,h=Math.max,l=a.plotLeft,p=a.plotTop,c=a.plotHeight,d=a.plotWidth;t=i=Number.POSITIVE_INFINITY,e=o=Number.NEGATIVE_INFINITY;for(var u=0;u<s.length;u++){var f=s[u];r=f[2],t=n(t,f[0]-r),e=h(e,f[0]+r),i=n(i,f[1]-r),o=h(o,f[1]+r)}var g=[e-t,o-i],y=[(d-l)/g[0],(c-p)/g[1]],v=n.apply([],y);if(Math.abs(v-1)>1e-10){for(var b=0;b<s.length;b++){var f=s[b];f[2]*=v}this.placeBubbles(s)}else a.diffY=c/2+p-i-(o-i)/2,a.diffX=d/2+l-t-(e-t)/2},e.prototype.seriesBox=function(){var t,e=this.chart,i=this.data,o=Math.max,r=Math.min,a=[e.plotLeft,e.plotLeft+e.plotWidth,e.plotTop,e.plotTop+e.plotHeight];return i.forEach(function(e){oM(e.plotX)&&oM(e.plotY)&&e.marker.radius&&(t=e.marker.radius,a[0]=r(a[0],e.plotX-t),a[1]=o(a[1],e.plotX+t),a[2]=r(a[2],e.plotY-t),a[3]=o(a[3],e.plotY+t))}),oA(a.width/a.height)?a:null},e.prototype.setVisible=function(){var t=this;oy.setVisible.apply(t,arguments),t.parentNodeLayout&&t.graph?t.visible?(t.graph.show(),t.parentNode.dataLabel&&t.parentNode.dataLabel.show()):(t.graph.hide(),t.parentNodeLayout.removeElementFromCollection(t.parentNode,t.parentNodeLayout.nodes),t.parentNode.dataLabel&&t.parentNode.dataLabel.hide()):t.layout&&(t.visible?t.layout.addElementsToCollection(t.points,t.layout.nodes):t.points.forEach(function(e){t.layout.removeElementFromCollection(e,t.layout.nodes)}))},e.prototype.translate=function(){var t,e,i,o=this.chart,r=this.data,a=this.index,s=this.options.useSimulation;this.generatePoints(),oM(o.allDataPoints)||(o.allDataPoints=this.accumulateAllPoints(),this.getPointRadius()),s?i=o.allDataPoints:(i=this.placeBubbles(o.allDataPoints),this.options.draggable=!1);for(var n=0,h=i;n<h.length;n++){var l=h[n];l[3]===a&&(t=r[l[4]],e=oT(l[2],void 0),s||(t.plotX=l[0]-o.plotLeft+o.diffX,t.plotY=l[1]-o.plotTop+o.diffY),oA(e)&&(t.marker=ow(t.marker,{radius:e,width:2*e,height:2*e}),t.radius=e))}s&&this.deferLayout(),oL(this,"afterTranslate")},e.defaultOptions=oS(ov.defaultOptions,iW),e}(ov);ow(oC.prototype,{pointClass:iD,axisTypes:[],directTouch:!0,forces:["barycenter","repulsive"],hasDraggableNodes:!0,invertible:!1,isCartesian:!1,noSharedTooltip:!0,pointArrayMap:["value"],pointValKey:"value",requireSorting:!1,trackerGroups:["group","dataLabelsGroup","parentNodesGroup"],initDataLabels:ob,alignDataLabel:oy.alignDataLabel,indexateNodes:og,onMouseDown:iA.onMouseDown,onMouseMove:iA.onMouseMove,redrawHalo:iA.redrawHalo,searchPoint:og}),D().registerSeriesType("packedbubble",oC);var oN={marker:{enabled:!1,states:{hover:{enabled:!1}}},stickyTracking:!1,tooltip:{followPointer:!0,pointFormat:""},trackByArea:!0,legendSymbol:"rectangle"},oX=(A=function(t,e){return(A=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),oO=_().noop,oY=D().seriesTypes,oI=oY.area,oR=oY.line,oE=oY.scatter,o_=_().extend,oz=_().merge,oD=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return oX(e,t),e.prototype.getGraphPath=function(){for(var t=oR.prototype.getGraphPath.call(this),e=t.length+1;e--;)(e===t.length||"M"===t[e][0])&&e>0&&t.splice(e,0,["Z"]);return this.areaPath=t,t},e.prototype.drawGraph=function(){this.options.fillColor=this.color,oI.prototype.drawGraph.call(this)},e.defaultOptions=oz(oE.defaultOptions,oN),e}(oE);o_(oD.prototype,{type:"polygon",drawTracker:oR.prototype.drawTracker,setStackedPoints:oO}),D().registerSeriesType("polygon",oD);var oB={circular:{gridLineWidth:1,labels:{align:void 0,x:0,y:void 0},maxPadding:0,minPadding:0,showLastLabel:!1,tickLength:0},radial:{gridLineInterpolation:"circle",gridLineWidth:1,labels:{align:"right",padding:5,x:-3,y:-2},showLastLabel:!1,title:{x:4,text:null,rotation:90}},radialGauge:{endOnTick:!1,gridLineWidth:0,labels:{align:"center",distance:-25,x:0,y:void 0},lineWidth:1,minorGridLineWidth:0,minorTickInterval:"auto",minorTickLength:10,minorTickPosition:"inside",minorTickWidth:1,startOnTick:!1,tickLength:10,tickPixelInterval:100,tickPosition:"inside",tickWidth:2,title:{rotation:0,text:""},zIndex:2}},oW=_().defaultOptions,oH=_().composed,oj=_().noop,oF=_().addEvent,oq=_().correctFloat,oG=_().defined,oV=_().extend,oU=_().fireEvent,oK=_().isObject,oZ=_().merge,o$=_().pick,oQ=_().pushUnique,oJ=_().relativeLength,o0=_().wrap;!function(t){function e(){this.autoConnect=this.isCircular&&void 0===o$(this.userMax,this.options.max)&&oq(this.endAngleRad-this.startAngleRad)===oq(2*Math.PI),!this.isCircular&&this.chart.inverted&&this.max++,this.autoConnect&&(this.max+=this.categories&&1||this.pointRange||this.closestPointRange||0)}function i(){var t=this;return function(){if(t.isRadial&&t.tickPositions&&t.options.labels&&!0!==t.options.labels.allowOverlap)return t.tickPositions.map(function(e){var i;return null===(i=t.ticks[e])||void 0===i?void 0:i.label}).filter(function(t){return!!t})}}function o(){return oj}function r(t,e,i){var o,r,a,s=this.pane.center,n=t.value;return this.isCircular?(oG(n)?t.point&&(t.point.shapeArgs||{}).start&&(n=this.chart.inverted?this.translate(t.point.rectPlotY,!0):t.point.x):(r=t.chartX||0,a=t.chartY||0,n=this.translate(Math.atan2(a-i,r-e)-this.startAngleRad,!0)),r=(o=this.getPosition(n)).x,a=o.y):(oG(n)||(r=t.chartX,a=t.chartY),oG(r)&&oG(a)&&(i=s[1]+this.chart.plotTop,n=this.translate(Math.min(Math.sqrt(Math.pow(r-e,2)+Math.pow(a-i,2)),s[2]/2)-s[3]/2,!0))),[n,r||0,a||0]}function a(t,e,i){var o,r,a=this.pane.center,s=this.chart,n=this.left||0,h=this.top||0,l=o$(e,a[2]/2-this.offset);return void 0===i&&(i=this.horiz?0:this.center&&-this.center[3]/2),i&&(l+=i),this.isCircular||void 0!==e?((r=this.chart.renderer.symbols.arc(n+a[0],h+a[1],l,l,{start:this.startAngleRad,end:this.endAngleRad,open:!0,innerR:0})).xBounds=[n+a[0]],r.yBounds=[h+a[1]-l]):(o=this.postTranslate(this.angleRad,l),r=[["M",this.center[0]+s.plotLeft,this.center[1]+s.plotTop],["L",o.x,o.y]]),r}function s(){this.constructor.prototype.getOffset.call(this),this.chart.axisOffset[this.side]=0}function n(t,e,i){var o,r,a,s,n,h,l=this.chart,p=function(t){if("string"==typeof t){var e=parseInt(t,10);return v.test(t)&&(e=e*u/100),e}return t},c=this.center,d=this.startAngleRad,u=c[2]/2,f=Math.min(this.offset,0),g=this.left||0,y=this.top||0,v=/%$/,b=this.isCircular,m=o$(p(i.outerRadius),u),x=p(i.innerRadius),P=o$(p(i.thickness),10);if("polygon"===this.options.gridLineInterpolation)h=this.getPlotLinePath({value:t}).concat(this.getPlotLinePath({value:e,reverse:!0}));else{t=Math.max(t,this.min),e=Math.min(e,this.max);var M=this.translate(t),w=this.translate(e);b||(m=M||0,x=w||0),"circle"!==i.shape&&b?(o=d+(M||0),r=d+(w||0)):(o=-Math.PI/2,r=1.5*Math.PI,n=!0),m-=f,P-=f,h=l.renderer.symbols.arc(g+c[0],y+c[1],m,m,{start:Math.min(o,r),end:Math.max(o,r),innerR:o$(x,m-P),open:n,borderRadius:i.borderRadius}),b&&(a=(r+o)/2,s=g+c[0]+c[2]/2*Math.cos(a),h.xBounds=a>-Math.PI/2&&a<Math.PI/2?[s,l.plotWidth]:[0,s],h.yBounds=[y+c[1]+c[2]/2*Math.sin(a)],h.yBounds[0]+=a>-Math.PI&&a<0||a>Math.PI?-10:10)}return h}function h(t){var e,i,o,r,a,s,n,h,l,p=this,c=this.pane.center,d=this.chart,u=d.inverted,f=t.reverse,g=this.pane.options.background?this.pane.options.background[0]||this.pane.options.background:{},y=g.innerRadius||"0%",v=g.outerRadius||"100%",b=c[0]+d.plotLeft,m=c[1]+d.plotTop,x=this.height,P=t.isCrosshair,M=c[3]/2,w=t.value,L=this.getPosition(w),k=L.x,A=L.y;if(P&&(w=(h=this.getCrosshairPosition(t,b,m))[0],k=h[1],A=h[2]),this.isCircular)i=Math.sqrt(Math.pow(k-b,2)+Math.pow(A-m,2)),o="string"==typeof y?oJ(y,1):y/i,r="string"==typeof v?oJ(v,1):v/i,c&&M&&(o<(e=M/i)&&(o=e),r<e&&(r=e)),l=[["M",b+o*(k-b),m-o*(m-A)],["L",k-(1-r)*(k-b),A+(1-r)*(m-A)]];else if((w=this.translate(w))&&(w<0||w>x)&&(w=0),"circle"===this.options.gridLineInterpolation)l=this.getLinePath(0,w,M);else if(l=[],d[u?"yAxis":"xAxis"].forEach(function(t){t.pane===p.pane&&(a=t)}),a){n=a.tickPositions,a.autoConnect&&(n=n.concat([n[0]])),f&&(n=n.slice().reverse()),w&&(w+=M);for(var S=0;S<n.length;S++)s=a.getPosition(n[S],w),l.push(S?["L",s.x,s.y]:["M",s.x,s.y])}return l}function l(t,e){var i=this.translate(t);return this.postTranslate(this.isCircular?i:this.angleRad,o$(this.isCircular?e:i<0?0:i,this.center[2]/2)-this.offset)}function p(){var t=this.center,e=this.chart,i=this.options.title;return{x:e.plotLeft+t[0]+(i.x||0),y:e.plotTop+t[1]-({high:.5,middle:.25,low:0})[i.align]*t[2]+(i.y||0)}}function c(t){t.beforeSetTickPositions=e,t.createLabelCollector=i,t.getCrosshairPosition=r,t.getLinePath=a,t.getOffset=s,t.getPlotBandPath=n,t.getPlotLinePath=h,t.getPosition=l,t.getTitlePosition=p,t.postTranslate=x,t.setAxisSize=M,t.setAxisTranslation=w,t.setOptions=L}function d(){var t=this.chart,e=this.options,i=t.angular&&this.isXAxis,o=this.pane,r=null==o?void 0:o.options;if(!i&&o&&(t.angular||t.polar)){var a=2*Math.PI,s=(o$(r.startAngle,0)-90)*Math.PI/180,n=(o$(r.endAngle,o$(r.startAngle,0)+360)-90)*Math.PI/180;this.angleRad=(e.angle||0)*Math.PI/180,this.startAngleRad=s,this.endAngleRad=n,this.offset=e.offset||0;var h=(s%a+a)%a,l=(n%a+a)%a;h>Math.PI&&(h-=a),l>Math.PI&&(l-=a),this.normalizedStartAngleRad=h,this.normalizedEndAngleRad=l}}function u(t){this.isRadial&&(t.align=void 0,t.preventDefault())}function f(){var t;if(null===(t=this.chart)||void 0===t?void 0:t.labelCollectors){var e=this.labelCollector?this.chart.labelCollectors.indexOf(this.labelCollector):-1;e>=0&&this.chart.labelCollectors.splice(e,1)}}function g(t){var e,i=this.chart,r=i.angular,a=i.polar,s=this.isXAxis,n=this.coll,h=t.userOptions.pane||0,l=this.pane=i.pane&&i.pane[h];if("colorAxis"===n){this.isRadial=!1;return}r?(r&&s?(this.isHidden=!0,this.createLabelCollector=o,this.getOffset=oj,this.redraw=P,this.render=P,this.setScale=oj,this.setCategories=oj,this.setTitle=oj):c(this),e=!s):a&&(c(this),e=this.horiz),r||a?(this.isRadial=!0,this.labelCollector||(this.labelCollector=this.createLabelCollector()),this.labelCollector&&i.labelCollectors.push(this.labelCollector)):this.isRadial=!1,l&&e&&(l.axis=this),this.isCircular=e}function y(){this.isRadial&&this.beforeSetTickPositions()}function v(t){var e=this.label;if(e){var i,o=this.axis,r=e.getBBox(),a=o.options.labels,s=(o.translate(this.pos)+o.startAngleRad+Math.PI/2)/Math.PI*180%360,n=Math.round(s),h=oG(a.y)?0:-(.3*r.height),l=a.y,p=20,c=a.align,d="end",u=n<0?n+360:n,f=u,g=0,y=0;o.isRadial&&(i=o.getPosition(this.pos,o.center[2]/2+oJ(o$(a.distance,-25),o.center[2]/2,-o.center[2]/2)),"auto"===a.rotation?e.attr({rotation:s}):oG(l)||(l=o.chart.renderer.fontMetrics(e).b-r.height/2),oG(c)||(o.isCircular?(r.width>o.len*o.tickInterval/(o.max-o.min)&&(p=0),c=s>p&&s<180-p?"left":s>180+p&&s<360-p?"right":"center"):c="center",e.attr({align:c})),"auto"===c&&2===o.tickPositions.length&&o.isCircular&&(u>90&&u<180?u=180-u:u>270&&u<=360&&(u=540-u),f>180&&f<=360&&(f=360-f),(o.pane.options.startAngle===n||o.pane.options.startAngle===n+360||o.pane.options.startAngle===n-360)&&(d="start"),c=n>=-90&&n<=90||n>=-360&&n<=-270||n>=270&&n<=360?"start"===d?"right":"left":"start"===d?"left":"right",f>70&&f<110&&(c="center"),u<15||u>=180&&u<195?g=.3*r.height:u>=15&&u<=35?g="start"===d?0:.75*r.height:u>=195&&u<=215?g="start"===d?.75*r.height:0:u>35&&u<=90?g="start"===d?-(.25*r.height):r.height:u>215&&u<=270&&(g="start"===d?r.height:-(.25*r.height)),f<15?y="start"===d?-(.15*r.height):.15*r.height:f>165&&f<=180&&(y="start"===d?.15*r.height:-(.15*r.height)),e.attr({align:c}),e.translate(y,g+h)),t.pos.x=i.x+(a.x||0),t.pos.y=i.y+(l||0))}}function b(t){this.axis.getPosition&&oV(t.pos,this.axis.getPosition(this.pos))}function m(e){var i=e.options;i.xAxis&&oZ(!0,t.radialDefaultOptions.circular,i.xAxis),i.yAxis&&oZ(!0,t.radialDefaultOptions.radialGauge,i.yAxis)}function x(t,e){var i=this.chart,o=this.center;return t=this.startAngleRad+t,{x:i.plotLeft+o[0]+Math.cos(t)*e,y:i.plotTop+o[1]+Math.sin(t)*e}}function P(){this.isDirty=!1}function M(){var t,e;this.constructor.prototype.setAxisSize.call(this),this.isRadial&&(this.pane.updateCenter(this),t=this.center=this.pane.center.slice(),this.isCircular?this.sector=this.endAngleRad-this.startAngleRad:(e=this.postTranslate(this.angleRad,t[3]/2),t[0]=e.x-this.chart.plotLeft,t[1]=e.y-this.chart.plotTop),this.len=this.width=this.height=(t[2]-t[3])*o$(this.sector,1)/2)}function w(){this.constructor.prototype.setAxisTranslation.call(this),this.center&&(this.isCircular?this.transA=(this.endAngleRad-this.startAngleRad)/(this.max-this.min||1):this.transA=(this.center[2]-this.center[3])/2/(this.max-this.min||1),this.isXAxis?this.minPixelPadding=this.transA*this.minPointOffset:this.minPixelPadding=0)}function L(e){var i=this.coll,o=this.chart,r=o.angular,a=o.inverted,s=o.polar,n={};r?this.isXAxis||(n=oZ(oW.yAxis,t.radialDefaultOptions.radialGauge)):s&&(n=this.horiz?oZ(oW.xAxis,t.radialDefaultOptions.circular):oZ("xAxis"===i?oW.xAxis:oW.yAxis,t.radialDefaultOptions.radial)),a&&"yAxis"===i&&(n.stackLabels=oK(oW.yAxis,!0)?oW.yAxis.stackLabels:{},n.reversedStacks=!0);var h=this.options=oZ(n,e);h.plotBands||(h.plotBands=[]),oU(this,"afterSetOptions")}function k(t,e,i,o,r,a,s){var n,h,l=this.axis;return l.isRadial?["M",e,i,"L",(n=l.getPosition(this.pos,l.center[2]/2+o)).x,n.y]:t.call(this,e,i,o,r,a,s)}t.radialDefaultOptions=oZ(oB),t.compose=function(t,e){return oQ(oH,"Axis.Radial")&&(oF(t,"afterInit",d),oF(t,"autoLabelAlign",u),oF(t,"destroy",f),oF(t,"init",g),oF(t,"initialAxisTranslation",y),oF(e,"afterGetLabelPosition",v),oF(e,"afterGetPosition",b),oF(_(),"setOptions",m),o0(e.prototype,"getMarkPath",k)),t}}(N||(N={}));var o1=N,o2=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,a=e.length;r<a;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))},o3=_().animObject,o5=_().composed,o8=_().addEvent,o6=_().defined,o4=_().find,o9=_().isNumber,o7=_().merge,rt=_().pick,re=_().pushUnique,ri=_().relativeLength,ro=_().splat,rr=_().uniqueKey,ra=_().wrap;function rs(){(this.pane||[]).forEach(function(t){t.render()})}function rn(t){var e=t.args[0].xAxis,i=t.args[0].yAxis,o=t.args[0].chart;e&&i&&("polygon"===i.gridLineInterpolation?(e.startOnTick=!0,e.endOnTick=!0):"polygon"===e.gridLineInterpolation&&o.inverted&&(i.startOnTick=!0,i.endOnTick=!0))}function rh(){var t=this;this.pane||(this.pane=[]),this.options.pane=ro(this.options.pane||{}),this.options.pane.forEach(function(e){new th(e,t)},this)}function rl(t){var e=t.args.marker,i=this.chart.xAxis[0],o=this.chart.yAxis[0],r=this.chart.inverted,a=r?o:i,s=r?i:o;if(this.chart.polar){t.preventDefault();var n=(e.attr?e.attr("start"):e.start)-a.startAngleRad,h=e.attr?e.attr("r"):e.r,l=(e.attr?e.attr("end"):e.end)-a.startAngleRad,p=e.attr?e.attr("innerR"):e.innerR;t.result.x=n+a.pos,t.result.width=l-n,t.result.y=s.len+s.pos-h,t.result.height=h-p}}function rp(t){var e=this.chart;if(e.polar&&e.hoverPane&&e.hoverPane.axis){t.preventDefault();var i=e.hoverPane.center,o=e.mouseDownX||0,r=e.mouseDownY||0,a=t.args.chartY,s=t.args.chartX,n=2*Math.PI,h=e.hoverPane.axis.startAngleRad,l=e.hoverPane.axis.endAngleRad,p=e.inverted?e.xAxis[0]:e.yAxis[0],c={},d="arc";if(c.x=i[0]+e.plotLeft,c.y=i[1]+e.plotTop,this.zoomHor){var u=h>0?l-h:Math.abs(h)+Math.abs(l),f=Math.atan2(r-e.plotTop-i[1],o-e.plotLeft-i[0])-h,g=Math.atan2(a-e.plotTop-i[1],s-e.plotLeft-i[0])-h;c.r=i[2]/2,c.innerR=i[3]/2,f<=0&&(f+=n),g<=0&&(g+=n),g<f&&(g=[f,f=g][0]),u<n&&h+g>l+(n-u)/2&&(g=f,f=h<=0?h:0);var y=c.start=Math.max(f+h,h),v=c.end=Math.min(g+h,l);if("polygon"===p.options.gridLineInterpolation){var b=e.hoverPane.axis,m=y-b.startAngleRad+b.pos,x=v-y,P=p.getPlotLinePath({value:p.max}),M=b.toValue(m),w=b.toValue(m+x);if(M<b.getExtremes().min){var L=b.getExtremes(),k=L.min;M=L.max-(k-M)}if(w<b.getExtremes().min){var A=b.getExtremes(),S=A.min;w=A.max-(S-w)}w<M&&(w=[M,M=w][0]),(P=rf(P,M,w,b)).push(["L",i[0]+e.plotLeft,e.plotTop+i[1]]),c.d=P,d="path"}}if(this.zoomVert){var T=e.inverted?e.xAxis[0]:e.yAxis[0],C=Math.sqrt(Math.pow(o-e.plotLeft-i[0],2)+Math.pow(r-e.plotTop-i[1],2)),N=Math.sqrt(Math.pow(s-e.plotLeft-i[0],2)+Math.pow(a-e.plotTop-i[1],2));if(N<C&&(C=[N,N=C][0]),N>i[2]/2&&(N=i[2]/2),C<i[3]/2&&(C=i[3]/2),this.zoomHor||(c.start=h,c.end=l),c.r=N,c.innerR=C,"polygon"===T.options.gridLineInterpolation){var v=T.toValue(T.len+T.pos-C),y=T.toValue(T.len+T.pos-N),P=T.getPlotLinePath({value:y}).concat(T.getPlotLinePath({value:v,reverse:!0}));c.d=P,d="path"}}if(this.zoomHor&&this.zoomVert&&"polygon"===p.options.gridLineInterpolation){var b=e.hoverPane.axis,y=c.start||0,v=c.end||0,m=y-b.startAngleRad+b.pos,x=v-y,M=b.toValue(m),w=b.toValue(m+x);if(c.d instanceof Array){var X=c.d.slice(0,c.d.length/2),O=c.d.slice(c.d.length/2,c.d.length);O=o2([],O,!0).reverse();var Y=e.hoverPane.axis;X=rf(X,M,w,Y),(O=rf(O,M,w,Y))&&(O[0][0]="L"),O=o2([],O,!0).reverse(),c.d=X.concat(O),d="path"}}t.attrs=c,t.shapeType=d}}function rc(){var t=this.chart;t.polar&&(this.polar=new rL(this),t.inverted&&(this.isRadialSeries=!0,this.is("column")&&(this.isRadialBar=!0)))}function rd(){if(this.chart.polar&&this.xAxis){var t=this.xAxis,e=this.yAxis,i=this.chart;this.kdByAngle=i.tooltip&&i.tooltip.shared,this.kdByAngle||i.inverted?this.searchPoint=ru:this.options.findNearestPointBy="xy";for(var o=this.points,r=o.length;r--;)this.is("column")||this.is("columnrange")||this.polar.toXY(o[r]),i.hasParallelCoordinates||this.yAxis.reversed||(rt(o[r].y,Number.MIN_VALUE)<e.min||o[r].x<t.min||o[r].x>t.max?(o[r].isNull=!0,o[r].plotY=NaN):o[r].isNull=o[r].isValid&&!o[r].isValid());this.hasClipCircleSetter||(this.hasClipCircleSetter=!!this.eventsToUnbind.push(o8(this,"afterRender",function(){var t,e,o,r,a,s,n,h,l;i.polar&&!1!==this.options.clip&&((t=this.yAxis.pane.center,this.clipCircle)?this.clipCircle.animate({x:t[0],y:t[1],r:t[2]/2,innerR:t[3]/2}):this.clipCircle=(e=i.renderer,o=t[0],r=t[1],a=t[2]/2,s=t[3]/2,n=rr(),h=e.createElement("clipPath").attr({id:n}).add(e.defs),(l=s?e.arc(o,r,a,s,0,2*Math.PI).add(h):e.circle(o,r,a).add(h)).id=n,l.clipPath=h,l),this.group.clip(this.clipCircle),this.setClip=_().noop)})))}}function ru(t){var e=this.chart,i=this.xAxis,o=this.yAxis,r=i.pane&&i.pane.center,a=t.chartX-(r&&r[0]||0)-e.plotLeft,s=t.chartY-(r&&r[1]||0)-e.plotTop,n=e.inverted?{clientX:t.chartX-o.pos,plotY:t.chartY-i.pos}:{clientX:180+-180/Math.PI*Math.atan2(a,s)};return this.searchKDTree(n)}function rf(t,e,i,o){var r=o.tickInterval,a=o.tickPositions,s=o4(a,function(t){return t>=i}),n=o4(o2([],a,!0).reverse(),function(t){return t<=e});return o6(s)||(s=a[a.length-1]),o6(n)||(n=a[0],s+=r,t[0][0]="L",t.unshift(t[t.length-3])),(t=t.slice(a.indexOf(n),a.indexOf(s)+1))[0][0]="M",t}function rg(t,e){return o4(this.pane||[],function(t){return t.options.id===e})||t.call(this,e)}function ry(t,e,i,o,r,a){var s,n,h,l,p=this.chart,c=rt(o.inside,!!this.options.stacking);p.polar?((n=e.rectPlotX/Math.PI*180,p.inverted)?(this.forceDL=p.isInsidePlot(e.plotX,e.plotY),c&&e.shapeArgs?(h=e.shapeArgs,r=o7(r,{x:(l=this.yAxis.postTranslate(((h.start||0)+(h.end||0))/2-this.xAxis.startAngleRad,e.barX+e.pointWidth/2)).x-p.plotLeft,y:l.y-p.plotTop})):e.tooltipPos&&(r=o7(r,{x:e.tooltipPos[0],y:e.tooltipPos[1]})),o.align=rt(o.align,"center"),o.verticalAlign=rt(o.verticalAlign,"middle")):(null===(s=o).align&&(s.align=n>20&&n<160?"left":n>200&&n<340?"right":"center"),null===s.verticalAlign&&(s.verticalAlign=n<45||n>315?"bottom":n>135&&n<225?"top":"middle"),o=s),W().prototype.alignDataLabel.call(this,e,i,o,r,a),this.isRadialBar&&e.shapeArgs&&e.shapeArgs.start===e.shapeArgs.end?i.hide():i.show()):t.call(this,e,i,o,r,a)}function rv(){var t,e,i,o,r,a,s,n,h,l,p,c,d,u=this.options,f=u.stacking,g=this.chart,y=this.xAxis,v=this.yAxis,b=v.reversed,m=v.center,x=y.startAngleRad,P=y.endAngleRad-x,M=u.threshold,w=0,L=0,k=0;if(y.isRadial)for(i=(t=this.points).length,o=v.translate(v.min),r=v.translate(v.max),M=u.threshold||0,g.inverted&&o9(M)&&o6(w=v.translate(M))&&(w<0?w=0:w>P&&(w=P),this.translatedThreshold=w+x);i--;){if(p=(e=t[i]).barX,s=e.x,n=e.y,e.shapeType="arc",g.inverted){e.plotY=v.translate(n),f&&v.stacking?(l=v.stacking.stacks[(n<0?"-":"")+this.stackKey],this.visible&&l&&l[s]&&!e.isNull&&(h=l[s].points[this.getStackIndicator(void 0,s,this.index).key],L=v.translate(h[0]),k=v.translate(h[1]),o6(L)&&(L=_().clamp(L,0,P)))):(L=w,k=e.plotY),L>k&&(k=[L,L=k][0]),b?k>o?k=o:L<r?L=r:(L>o||k<r)&&(L=k=P):L<o?L=o:k>r?k=r:(k<o||L>r)&&(L=k=0),v.min>v.max&&(L=k=b?P:0),L+=x,k+=x,m&&(e.barX=p+=m[3]/2),c=Math.max(p,0),d=Math.max(p+e.pointWidth,0);var A=u.borderRadius,S=ri(("object"==typeof A?A.radius:A)||0,d-c);e.shapeArgs={x:m[0],y:m[1],r:d,innerR:c,start:L,end:k,borderRadius:S},e.opacity=L===k?0:void 0,e.plotY=(o6(this.translatedThreshold)&&(L<this.translatedThreshold?L:k))-x}else L=p+x,e.shapeArgs=this.polar.arc(e.yBottom,e.plotY,L,L+e.pointWidth),e.shapeArgs.borderRadius=0;this.polar.toXY(e),g.inverted?(a=v.postTranslate(e.rectPlotY,p+e.pointWidth/2),e.tooltipPos=[a.x-g.plotLeft,a.y-g.plotTop]):e.tooltipPos=[e.plotX,e.plotY],m&&(e.ttBelow=e.plotY>m[1])}}function rb(t,e){var i,o,r=this;if(this.chart.polar){e=e||this.points;for(var a=0;a<e.length;a++)if(!e[a].isNull){i=a;break}!1!==this.options.connectEnds&&void 0!==i&&(this.connectEnds=!0,e.splice(e.length,0,e[i]),o=!0),e.forEach(function(t){void 0===t.polarPlotY&&r.polar.toXY(t)})}var s=t.apply(this,[].slice.call(arguments,1));return o&&e.pop(),s}function rm(t,e){var i=this.chart,o={xAxis:[],yAxis:[]};return i.polar?i.axes.forEach(function(t){if("colorAxis"!==t.coll){var r=t.isXAxis,a=t.center,s=e.chartX-a[0]-i.plotLeft,n=e.chartY-a[1]-i.plotTop;o[r?"xAxis":"yAxis"].push({axis:t,value:t.translate(r?Math.PI-Math.atan2(s,n):Math.sqrt(Math.pow(s,2)+Math.pow(n,2)),!0)})}}):o=t.call(this,e),o}function rx(t,e){!this.chart.polar&&t.call(this,e)}function rP(t,e){var i,o,r,a,s,n,h=this,l=this.chart,p=this.group,c=this.markerGroup,d=this.xAxis&&this.xAxis.center,u=l.plotLeft,f=l.plotTop,g=this.options.animation;l.polar?h.isRadialBar?e||(h.startAngleRad=rt(h.translatedThreshold,h.xAxis.startAngleRad),_().seriesTypes.pie.prototype.animate.call(h,e)):(g=o3(g),h.is("column")?e||(o=d[3]/2,h.points.forEach(function(t){r=t.graphic,s=(a=t.shapeArgs)&&a.r,n=a&&a.innerR,r&&a&&(r.attr({r:o,innerR:o}),r.animate({r:s,innerR:n},h.options.animation))})):e?(i={translateX:d[0]+u,translateY:d[1]+f,scaleX:.001,scaleY:.001},p.attr(i),c&&c.attr(i)):(i={translateX:u,translateY:f,scaleX:1,scaleY:1},p.animate(i,g),c&&c.animate(i,g))):t.call(this,e)}function rM(t,e,i,o){var r,a;if(this.chart.polar){if(o){var s=(a=function t(e,i,o,r){var a,s,n,h,l,p,c=+!!r,d=(a=i>=0&&i<=e.length-1?i:i<0?e.length-1+i:0)-1<0?e.length-(1+c):a-1,u=a+1>e.length-1?c:a+1,f=e[d],g=e[u],y=f.plotX,v=f.plotY,b=g.plotX,m=g.plotY,x=e[a].plotX,P=e[a].plotY;s=(1.5*x+y)/2.5,n=(1.5*P+v)/2.5,h=(1.5*x+b)/2.5,l=(1.5*P+m)/2.5;var M=Math.sqrt(Math.pow(s-x,2)+Math.pow(n-P,2)),w=Math.sqrt(Math.pow(h-x,2)+Math.pow(l-P,2)),L=Math.atan2(n-P,s-x);p=Math.PI/2+(L+Math.atan2(l-P,h-x))/2,Math.abs(L-p)>Math.PI/2&&(p-=Math.PI),s=x+Math.cos(p)*M,n=P+Math.sin(p)*M;var k={rightContX:h=x+Math.cos(Math.PI+p)*w,rightContY:l=P+Math.sin(Math.PI+p)*w,leftContX:s,leftContY:n,plotX:x,plotY:P};return o&&(k.prevPointCont=t(e,d,!1,r)),k}(e,o,!0,this.connectEnds)).prevPointCont&&a.prevPointCont.rightContX,n=a.prevPointCont&&a.prevPointCont.rightContY;r=["C",o9(s)?s:a.plotX,o9(n)?n:a.plotY,o9(a.leftContX)?a.leftContX:a.plotX,o9(a.leftContY)?a.leftContY:a.plotY,a.plotX,a.plotY]}else r=["M",i.plotX,i.plotY]}else r=t.call(this,e,i,o);return r}function rw(t,e,i){if(void 0===i&&(i=this.plotY),!this.destroyed){var o=this.plotX,r=this.series.chart;return r.polar&&o9(o)&&o9(i)?[o+(e?r.plotLeft:0),i+(e?r.plotTop:0)]:t.call(this,e,i)}}var rL=function(){function t(t){this.series=t}return t.compose=function(t,e,i,o,r,a,s,n,h,l){if(th.compose(e,i),o1.compose(t,r),re(o5,"Polar")){var p=e.prototype,c=a.prototype,d=i.prototype,u=o.prototype;if(o8(e,"afterDrawChartBox",rs),o8(e,"createAxes",rh),o8(e,"init",rn),ra(p,"get",rg),ra(d,"getCoordinates",rm),ra(d,"pinch",rx),o8(i,"getSelectionMarkerAttrs",rp),o8(i,"getSelectionBox",rl),o8(o,"afterInit",rc),o8(o,"afterTranslate",rd,{order:2}),o8(o,"afterColumnTranslate",rv,{order:4}),ra(u,"animate",rP),ra(c,"pos",rw),n){var f=n.prototype;ra(f,"alignDataLabel",ry),ra(f,"animate",rP)}if(h&&ra(h.prototype,"getGraphPath",rb),l){var g=l.prototype;ra(g,"getPointSpline",rM),s&&(s.prototype.getPointSpline=g.getPointSpline)}}},t.prototype.arc=function(t,e,i,o){var r=this.series,a=r.xAxis.center,s=r.yAxis.len,n=a[3]/2,h=s-e+n,l=s-rt(t,s)+n;return r.yAxis.reversed&&(h<0&&(h=n),l<0&&(l=n)),{x:a[0],y:a[1],r:h,innerR:l,start:i,end:o}},t.prototype.toXY=function(t){var e,i=this.series,o=i.chart,r=i.xAxis,a=i.yAxis,s=t.plotX,n=o.inverted,h=t.y,l=t.plotY,p=n?s:a.len-l;if(n&&i&&!i.isRadialBar&&(t.plotY=l=o9(h)?a.translate(h):0),t.rectPlotX=s,t.rectPlotY=l,a.center&&(p+=a.center[3]/2),o9(l)){var c=n?a.postTranslate(l,p):r.postTranslate(s,p);t.plotX=t.polarPlotX=c.x-o.plotLeft,t.plotY=t.polarPlotY=c.y-o.plotTop}i.kdByAngle?((e=(s/Math.PI*180+r.pane.options.startAngle)%360)<0&&(e+=360),t.clientX=e):t.clientX=t.plotX},t}(),rk=I(184),rA=I.n(rk),rS=_().composed,rT=_().addEvent,rC=_().objectEach,rN=_().pushUnique;!function(t){function e(){var t=this.waterfall.stacks;t&&(t.changed=!1,delete t.alreadyChanged)}function i(){var t=this.options.stackLabels;(null==t?void 0:t.enabled)&&this.waterfall.stacks&&this.waterfall.renderStackTotals()}function o(){this.waterfall||(this.waterfall=new a(this))}function r(){for(var t=this.axes,e=this.series,i=0;i<e.length;i++)if(e[i].options.stacking){for(var o=0;o<t.length;o++){var r=t[o];r.isXAxis||(r.waterfall.stacks.changed=!0)}break}}t.compose=function(t,a){rN(rS,"Axis.Waterfall")&&(rT(t,"init",o),rT(t,"afterBuildStacks",e),rT(t,"afterRender",i),rT(a,"beforeRedraw",r))};var a=function(){function t(t){this.axis=t,this.stacks={changed:!1}}return t.prototype.renderStackTotals=function(){var t,e=this.axis,i=e.waterfall.stacks,o=null===(t=e.stacking)||void 0===t?void 0:t.stackTotalGroup,r=new(rA())(e,e.options.stackLabels||{},!1,0,void 0);this.dummyStackItem=r,o&&rC(i,function(t){rC(t,function(t,e){r.total=t.stackTotal,r.x=+e,t.label&&(r.label=t.label),rA().prototype.render.call(r,o),t.label=r.label,delete r.label})}),r.total=null},t}();t.Composition=a}(X||(X={}));var rX=X,rO=(S=function(t,e){return(S=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),rY=_().isNumber,rI=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rO(e,t),e.prototype.getClassName=function(){var t=el().prototype.getClassName.call(this);return this.isSum?t+=" highcharts-sum":this.isIntermediateSum&&(t+=" highcharts-intermediate-sum"),t},e.prototype.isValid=function(){return rY(this.y)||this.isSum||!!this.isIntermediateSum},e}(tz().prototype.pointClass),rR={dataLabels:{inside:!0},lineWidth:1,lineColor:"#333333",dashStyle:"Dot",borderColor:"#333333",states:{hover:{lineWidthPlus:0}}},rE=(T=function(t,e){return(T=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}T(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),r_=D().seriesTypes,rz=r_.column,rD=r_.line,rB=_().addEvent,rW=_().arrayMax,rH=_().arrayMin,rj=_().correctFloat,rF=_().crisp,rq=_().extend,rG=_().isNumber,rV=_().merge,rU=_().objectEach,rK=_().pick;function rZ(t,e){return Object.hasOwnProperty.call(t,e)}var r$=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rE(e,t),e.prototype.generatePoints=function(){rz.prototype.generatePoints.apply(this);for(var t=this.getColumn("y",!0),e=0,i=this.points.length;e<i;e++){var o=this.points[e],r=t[e];rG(r)&&(o.isIntermediateSum||o.isSum)&&(o.y=rj(r))}},e.prototype.processData=function(e){var i,o,r,a,s,n,h=this.options,l=this.getColumn("y"),p=h.data,c=l.length,d=h.threshold||0;r=o=a=s=0;for(var u=0;u<c;u++)n=l[u],i=(null==p?void 0:p[u])||{},"sum"===n||i.isSum?l[u]=rj(r):"intermediateSum"===n||i.isIntermediateSum?(l[u]=rj(o),o=0):(r+=n,o+=n),a=Math.min(r,a),s=Math.max(r,s);t.prototype.processData.call(this,e),h.stacking||(this.dataMin=a+d,this.dataMax=s)},e.prototype.toYData=function(t){return t.isSum?"sum":t.isIntermediateSum?"intermediateSum":t.y},e.prototype.pointAttribs=function(t,e){var i=this.options.upColor;i&&!t.options.color&&rG(t.y)&&(t.color=t.y>0?i:void 0);var o=rz.prototype.pointAttribs.call(this,t,e);return delete o.dashstyle,o},e.prototype.getGraphPath=function(){return[["M",0,0]]},e.prototype.getCrispPath=function(){for(var t,e=this.data.filter(function(t){return rG(t.y)}),i=this.yAxis,o=e.length,r=(null===(t=this.graph)||void 0===t?void 0:t.strokeWidth())||0,a=this.xAxis.reversed,s=this.yAxis.reversed,n=this.options.stacking,h=[],l=1;l<o;l++){if(this.options.connectNulls||rG(this.data[e[l].index-1].y)){var p=e[l].box,c=e[l-1],d=c.y||0,u=e[l-1].box;if(p&&u){var f=i.waterfall.stacks[this.stackKey],g=d>0?-u.height:0;if(f&&u&&p){var y=f[l-1],v=void 0;if(n){var b=y.connectorThreshold;v=rF(i.translate(b,!1,!0,!1,!0)+(s?g:0),r)}else v=rF(u.y+(c.minPointLengthOffset||0),r);h.push(["M",(u.x||0)+(a?0:u.width||0),v],["L",(p.x||0)+(a&&p.width||0),v])}if(u&&h.length&&(!n&&d<0&&!s||d>0&&s)){var m=h[h.length-2];m&&"number"==typeof m[2]&&(m[2]+=u.height||0);var x=h[h.length-1];x&&"number"==typeof x[2]&&(x[2]+=u.height||0)}}}}return h},e.prototype.drawGraph=function(){rD.prototype.drawGraph.call(this),this.graph&&this.graph.attr({d:this.getCrispPath()})},e.prototype.setStackedPoints=function(t){var e,i,o,r,a,s,n,h,l,p,c=this.options,d=null===(e=t.waterfall)||void 0===e?void 0:e.stacks,u=c.threshold||0,f=this.stackKey,g=this.getColumn("x"),y=this.getColumn("y"),v=g.length,b=u,m=b,x=0,P=0,M=0,w=function(t,e,r,a){if(i){if(o)for(;r<o;r++)i.stackState[r]+=a;else i.stackState[0]=t,o=i.stackState.length;i.stackState.push(i.stackState[o-1]+e)}};if(t.stacking&&d&&this.reserveSpace()){p=d.changed,(l=d.alreadyChanged)&&0>l.indexOf(f)&&(p=!0),d[f]||(d[f]={});var L=d[f];if(L)for(var k=0;k<v;k++)(!L[h=g[k]]||p)&&(L[h]={negTotal:0,posTotal:0,stackTotal:0,threshold:0,stateIndex:0,stackState:[],label:p&&L[h]?L[h].label:void 0}),i=L[h],(n=y[k])>=0?i.posTotal+=n:i.negTotal+=n,s=c.data[k],r=i.absolutePos=i.posTotal,a=i.absoluteNeg=i.negTotal,i.stackTotal=r+a,o=i.stackState.length,(null==s?void 0:s.isIntermediateSum)?(w(M,P,0,M),M=P,P=u,b^=m,m^=b,b^=m):(null==s?void 0:s.isSum)?(w(u,x,o,0),b=u):(w(b,n,0,x),s&&(x+=n,P+=n)),i.stateIndex++,i.threshold=b,b+=i.stackTotal;d.changed=!1,d.alreadyChanged||(d.alreadyChanged=[]),d.alreadyChanged.push(f)}},e.prototype.getExtremes=function(){var t,e,i,o=this.options.stacking;return o?(t=this.yAxis.waterfall.stacks,e=this.stackedYNeg=[],i=this.stackedYPos=[],"overlap"===o?rU(t[this.stackKey],function(t){e.push(rH(t.stackState)),i.push(rW(t.stackState))}):rU(t[this.stackKey],function(t){e.push(t.negTotal+t.threshold),i.push(t.posTotal+t.threshold)}),{dataMin:rH(e),dataMax:rW(i)}):{dataMin:this.dataMin,dataMax:this.dataMax}},e.defaultOptions=rV(rz.defaultOptions,rR),e.compose=rX.compose,e}(rz);rq(r$.prototype,{pointValKey:"y",showLine:!0,pointClass:rI}),rB(r$,"afterColumnTranslate",function(){for(var t,e,i,o,r=this.options,a=this.points,s=this.yAxis,n=rK(r.minPointLength,5),h=n/2,l=r.threshold||0,p=r.stacking,c=s.waterfall.stacks[this.stackKey],d=this.getColumn("y",!0),u=l,f=l,g=0;g<a.length;g++){var y=a[g],v=d[g],b=rq({x:0,y:0,width:0,height:0},y.shapeArgs||{});y.box=b;var m=[0,v],x=y.y||0;if(p){if(c){var P=c[g];"overlap"===p?(e=P.stackState[P.stateIndex--],t=x>=0?e:e-x,rZ(P,"absolutePos")&&delete P.absolutePos,rZ(P,"absoluteNeg")&&delete P.absoluteNeg):(x>=0?(e=P.threshold+P.posTotal,P.posTotal-=x,t=e):(e=P.threshold+P.negTotal,P.negTotal-=x,t=e-x),!P.posTotal&&rG(P.absolutePos)&&rZ(P,"absolutePos")&&(P.posTotal=P.absolutePos,delete P.absolutePos),!P.negTotal&&rG(P.absoluteNeg)&&rZ(P,"absoluteNeg")&&(P.negTotal=P.absoluteNeg,delete P.absoluteNeg)),y.isSum||(P.connectorThreshold=P.threshold+P.stackTotal),s.reversed?(i=x>=0?t-x:t+x,o=t):(i=t,o=t-x),y.below=i<=l,b.y=s.translate(i,!1,!0,!1,!0),b.height=Math.abs(b.y-s.translate(o,!1,!0,!1,!0));var M=s.waterfall.dummyStackItem;M&&(M.x=g,M.label=c[g].label,M.setOffset(this.pointXOffset||0,this.barW||0,this.stackedYNeg[g],this.stackedYPos[g],void 0,this.xAxis))}}else t=Math.max(f,f+x)+m[0],b.y=s.translate(t,!1,!0,!1,!0),y.isSum?(b.y=s.translate(m[1],!1,!0,!1,!0),b.height=Math.min(s.translate(m[0],!1,!0,!1,!0),s.len)-b.y,y.below=m[1]<=l):y.isIntermediateSum?(x>=0?(i=m[1]+u,o=u):(i=u,o=m[1]+u),s.reversed&&(i^=o,o^=i,i^=o),b.y=s.translate(i,!1,!0,!1,!0),b.height=Math.abs(b.y-Math.min(s.translate(o,!1,!0,!1,!0),s.len)),u+=m[1],y.below=i<=l):(b.height=v>0?s.translate(f,!1,!0,!1,!0)-b.y:s.translate(f,!1,!0,!1,!0)-s.translate(f-v,!1,!0,!1,!0),y.below=(f+=v)<l),b.height<0&&(b.y+=b.height,b.height*=-1);y.plotY=b.y,y.yBottom=b.y+b.height,b.height<=n&&!y.isNull?(b.height=n,b.y-=h,y.yBottom=b.y+b.height,y.plotY=b.y,x<0?y.minPointLengthOffset=-h:y.minPointLengthOffset=h):(y.isNull&&(b.width=0),y.minPointLengthOffset=0);var w=y.plotY+(y.negative?b.height:0);y.below&&(y.plotY+=b.height),y.tooltipPos&&(this.chart.inverted?y.tooltipPos[0]=s.len-w:y.tooltipPos[1]=w),y.isInside=this.isPointInside(y);var L=rF(y.yBottom,this.borderWidth);b.y=rF(b.y,this.borderWidth),b.height=L-b.y,rV(!0,y.shapeArgs,b)}},{order:2}),D().registerSeriesType("waterfall",r$);var rQ=_();rQ.RadialAxis=o1,eO.compose(rQ.Axis,rQ.Chart,rQ.Legend),oC.compose(rQ.Axis,rQ.Chart,rQ.Legend),th.compose(rQ.Chart,rQ.Pointer),rL.compose(rQ.Axis,rQ.Chart,rQ.Pointer,rQ.Series,rQ.Tick,rQ.Point,D().seriesTypes.areasplinerange,D().seriesTypes.column,D().seriesTypes.line,D().seriesTypes.spline),r$.compose(rQ.Axis,rQ.Chart);var rJ=rQ;return R.default}()});